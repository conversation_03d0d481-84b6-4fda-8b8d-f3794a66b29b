package com.gosun.zhjg.common.util;

import com.gosun.zhjg.common.exception.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Http请求
 *
 * <AUTHOR>
 * @date 2023/9/11 9:18
 */
@Slf4j
public class HttpUtil {

    public static String Get(String url, int timeout) throws IOException {
        log.info("----> Get {}", url);
        URL requestUrl = new URL(url);
        HttpURLConnection connection = (HttpURLConnection) requestUrl.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(timeout);
        connection.setReadTimeout(timeout);
        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                log.info("<---- {}", response);
                return response.toString();
            }
        } else {
            throw new BaseException("HTTP request failed with response code: " + responseCode, responseCode);
        }
    }

    public static String Get(String url,Map<String, String> header, int timeout) throws IOException {
        log.info("----> Get {}", url);
        URL requestUrl = new URL(url);
        HttpURLConnection connection = (HttpURLConnection) requestUrl.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(timeout);
        connection.setReadTimeout(timeout);
        if (header != null) {//添加请求头
            header.forEach((k, v) -> {
                connection.setRequestProperty(k, v);
            });
        }
        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                log.info("<---- {}", response);
                return response.toString();
            }
        } else {
            throw new BaseException("HTTP request failed with response code: " + responseCode, responseCode);
        }
    }

    public static String PostJSON(String url, String jsonData, int timeout) throws IOException {
        log.info("----> PostJSON {}  {}", url, jsonData);
        URL requestUrl = new URL(url);
        HttpURLConnection connection = (HttpURLConnection) requestUrl.openConnection();
        connection.setRequestMethod("POST");
        connection.setConnectTimeout(timeout);
        connection.setReadTimeout(timeout);
        connection.setDoOutput(true);
        connection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
        try (OutputStream outputStream = connection.getOutputStream()) {
            outputStream.write(jsonData.getBytes());
            outputStream.flush();
        }
        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                log.info("<---- {}", response);
                return response.toString();
            }
        } else {
            throw new BaseException("HTTP request failed with response code: " + responseCode, responseCode);
        }
    }

    public static String PostForm(String url, Map<String, Object> form, Map<String, String> header, int timeout) throws IOException {
        String urlParameters = form.entrySet().stream()
                .map(m -> {
                    try {
                        return m.getKey() + "=" + (m.getValue() == null ? "" : URLEncoder.encode((m.getValue() + ""), "UTF-8"));
                    } catch (UnsupportedEncodingException e) {
                        throw new RuntimeException(e);
                    }
                })
                .collect(Collectors.joining("&"));
        return PostForm(url, urlParameters, header, timeout);
    }

    /**
     * 发送表单请求
     *
     * @param url
     * @param urlParameters
     * @param timeout
     * @return
     * @throws IOException
     */
    public static String PostForm(String url, String urlParameters, Map<String, String> header, int timeout) throws IOException {
        log.info("----> PostForm {}", urlParameters == null ? url : (url + "?" + urlParameters));
        URL requestUrl = new URL(url);
        HttpURLConnection connection = (HttpURLConnection) requestUrl.openConnection();
        connection.setRequestMethod("POST");
        connection.setConnectTimeout(timeout);
        connection.setReadTimeout(timeout);
        connection.setDoOutput(true);
        connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
        connection.setRequestProperty("Content-Length", String.valueOf(urlParameters.getBytes().length));
        if (header != null) {
            header.forEach((k, v) -> {
                connection.setRequestProperty(k, v);
            });
        }
        try (OutputStream outputStream = connection.getOutputStream()) {
            if (urlParameters != null && urlParameters.length() > 0) {
                outputStream.write(urlParameters.getBytes());
            }
            outputStream.flush();
        }
        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                log.info("<---- {}", response);
                return response.toString();
            }
        } else {
            throw new BaseException("HTTP request failed with response code: " + responseCode, responseCode);
        }
    }

    /**
     * 文件上传
     *
     * @param url
     * @param parameters
     * @return
     * @throws IOException
     */
    public static String UploadFile(String url, Map<String, Object> parameters) throws IOException {
        HttpURLConnection connection = null;
        DataOutputStream outputStream = null;
        try {
            log.info("----> UploadFile {}", url);
            URL uploadUrl = new URL(url);
            connection = (HttpURLConnection) uploadUrl.openConnection();
            connection.setDoOutput(true);
            connection.setRequestMethod("POST");
            connection.setConnectTimeout(1000 * 60 * 2);
            connection.setReadTimeout(1000 * 60 * 2);
            // 设置请求头
            String boundary = UUID.randomUUID().toString();
            connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);
            // 获取连接的输出流
            outputStream = new DataOutputStream(connection.getOutputStream());
            // 写入其他参数
            if (parameters != null) {
                for (Map.Entry<String, Object> entry : parameters.entrySet()) {
                    Object value = entry.getValue();
                    if (value instanceof MultipartFile) {
                        MultipartFile file = (MultipartFile) value;
                        try (InputStream fileInputStream = file.getInputStream()) {
                            // 写入文件数据
                            outputStream.writeBytes("--" + boundary + "\r\n");
                            outputStream.writeBytes("Content-Disposition: form-data; name=\"" + entry.getKey() + "\"; filename=\"" + file.getOriginalFilename() + "\"\r\n");
                            outputStream.writeBytes("Content-Type: application/octet-stream\r\n\r\n");
                            byte[] buffer = new byte[8192];
                            int bytesRead;
                            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                                outputStream.write(buffer, 0, bytesRead);
                            }
                            outputStream.writeBytes("\r\n");
                        }
                    } else if (value instanceof File) {
                        File file = (File) value;
                        // 写入文件数据
                        outputStream.writeBytes("--" + boundary + "\r\n");
                        outputStream.writeBytes("Content-Disposition: form-data; name=\"" + entry.getKey() + "\"; filename=\"" + file.getName() + "\"\r\n");
                        outputStream.writeBytes("Content-Type: application/octet-stream\r\n\r\n");
                        try (InputStream fileInputStream = new FileInputStream(file)) {
                            byte[] buffer = new byte[8192];
                            int bytesRead;
                            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                                outputStream.write(buffer, 0, bytesRead);
                            }
                            outputStream.writeBytes("\r\n");
                        }
                    } else if (value != null) {
                        outputStream.writeBytes("--" + boundary + "\r\n");
                        outputStream.writeBytes("Content-Disposition: form-data; name=\"" + entry.getKey() + "\"\r\n\r\n");
                        outputStream.writeBytes(String.valueOf(entry.getValue()));
                        outputStream.writeBytes("\r\n");
                    }
                }
            }
            outputStream.writeBytes("--" + boundary + "--\r\n");
            outputStream.flush();
            // 获取响应码
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"))) {
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    log.info("<---- {}", response);
                    return response.toString();
                }
            } else {
                throw new BaseException("HTTP request failed with response code: " + responseCode, responseCode);
            }
        } finally {
            // 关闭连接和输入流
            if (connection != null) {
                connection.disconnect();
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    // 处理关闭输出流时的异常
                }
            }
        }
    }

    public static String UploadFile(String url, Map<String, Object> parameters,String tokenKey,String tokenValue) throws IOException {
        HttpURLConnection connection = null;
        DataOutputStream outputStream = null;
        try {
            log.info("----> UploadFile {}", url);
            URL uploadUrl = new URL(url);
            connection = (HttpURLConnection) uploadUrl.openConnection();
            connection.setDoOutput(true);
            connection.setRequestMethod("POST");
            connection.setConnectTimeout(1000 * 60 * 2);
            connection.setReadTimeout(1000 * 60 * 2);
            // 设置请求头
            String boundary = UUID.randomUUID().toString();
            connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);
            connection.setRequestProperty(tokenKey, tokenValue);
            // 获取连接的输出流
            outputStream = new DataOutputStream(connection.getOutputStream());
            // 写入其他参数
            if (parameters != null) {
                for (Map.Entry<String, Object> entry : parameters.entrySet()) {
                    Object value = entry.getValue();
                    if (value instanceof MultipartFile) {
                        MultipartFile file = (MultipartFile) value;
                        try (InputStream fileInputStream = file.getInputStream()) {
                            // 写入文件数据
                            outputStream.writeBytes("--" + boundary + "\r\n");
                            outputStream.writeBytes("Content-Disposition: form-data; name=\"" + entry.getKey() + "\"; filename=\"" + file.getOriginalFilename() + "\"\r\n");
                            outputStream.writeBytes("Content-Type: application/octet-stream\r\n\r\n");
                            byte[] buffer = new byte[8192];
                            int bytesRead;
                            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                                outputStream.write(buffer, 0, bytesRead);
                            }
                            outputStream.writeBytes("\r\n");
                        }
                    } else if (value instanceof File) {
                        File file = (File) value;
                        // 写入文件数据
                        outputStream.writeBytes("--" + boundary + "\r\n");
                        outputStream.writeBytes("Content-Disposition: form-data; name=\"" + entry.getKey() + "\"; filename=\"" + file.getName() + "\"\r\n");
                        outputStream.writeBytes("Content-Type: application/octet-stream\r\n\r\n");
                        try (InputStream fileInputStream = new FileInputStream(file)) {
                            byte[] buffer = new byte[8192];
                            int bytesRead;
                            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                                outputStream.write(buffer, 0, bytesRead);
                            }
                            outputStream.writeBytes("\r\n");
                        }
                    } else if (value != null) {
                        outputStream.writeBytes("--" + boundary + "\r\n");
                        outputStream.writeBytes("Content-Disposition: form-data; name=\"" + entry.getKey() + "\"\r\n\r\n");
                        outputStream.writeBytes(String.valueOf(entry.getValue()));
                        outputStream.writeBytes("\r\n");
                    }
                }
            }
            outputStream.writeBytes("--" + boundary + "--\r\n");
            outputStream.flush();
            // 获取响应码
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"))) {
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    log.info("<---- {}", response);
                    return response.toString();
                }
            } else {
                throw new BaseException("HTTP request failed with response code: " + responseCode, responseCode);
            }
        } finally {
            // 关闭连接和输入流
            if (connection != null) {
                connection.disconnect();
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    // 处理关闭输出流时的异常
                }
            }
        }
    }
}
