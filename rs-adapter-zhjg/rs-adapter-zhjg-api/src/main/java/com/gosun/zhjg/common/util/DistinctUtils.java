package com.gosun.zhjg.common.util;


import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;

/**去重工具类
*<AUTHOR>
*@email <EMAIL>
*@date 2021/12/17
*/
public class DistinctUtils {
    //去重函数
    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> set = ConcurrentHashMap.newKeySet();
        return t -> set.add(keyExtractor.apply(t));
    }

}
