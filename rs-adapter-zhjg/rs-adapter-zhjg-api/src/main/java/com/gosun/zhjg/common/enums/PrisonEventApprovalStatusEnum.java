package com.gosun.zhjg.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 所情管理所领导审批状态

 */
public enum PrisonEventApprovalStatusEnum {
	APPROVAL_PASS("1", "通过"),
	APPROVAL_REJECT("2", "不通过"),
	;

	private String value;
	private String desc;

	PrisonEventApprovalStatusEnum(String value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public String getValue() {
		return value;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(String value) {
		for (PrisonEventApprovalStatusEnum e : PrisonEventApprovalStatusEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}
}
