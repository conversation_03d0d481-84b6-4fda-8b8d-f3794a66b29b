package com.gosun.zhjg.basic.business.modules.police.feign;

import com.gosun.zhjg.basic.business.modules.police.dto.BasePoliceInfoPageDto;
import com.gosun.zhjg.basic.business.modules.police.vo.BasePoliceInfoVO;
import com.gosun.zhjg.basic.business.modules.police.vo.PrisonPoliceCountVO;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.common.msg.ResultVO;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Author: panyusheng
 * @Date: 2020/7/28
 * @Version 1.0
 */
@Component
@FeignClient(value = "zhjg-basic-business", path = "police/basepoliceinfo", contextId = "police/basepoliceinfo")
public interface BasePoliceInfoApi {

    @RequestMapping(value = "/findById/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "根据id查询民警信息")
    R<BasePoliceInfoVO> findById(@PathVariable("id") String id);

    @RequestMapping(value = "/findByUserId/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "根据id查询民警信息")
    ResultVO<BasePoliceInfoVO> findByUserId(@PathVariable("id") int id);

    @RequestMapping(value = "/findByIds/{ids}", method = RequestMethod.GET)
    @ApiOperation(value = "根据id批量查询民警信息")
    List<BasePoliceInfoVO> findByIds(@PathVariable("ids") String ids);

    /**
     * 根据id查询详细信息
     */
    @RequestMapping(value = "/findDetailsById/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "民警信息表-根据id查询详细信息", responseContainer = "Map", response = BasePoliceInfoVO.class)
    @ApiImplicitParam(paramType = "path", name = "id", value = "编号", required = true, dataType = "String")
    R<List<BasePoliceInfoVO>> findDetailsById(@PathVariable("id") String id);

    @RequestMapping(value = "/findByDutyPolice", method = RequestMethod.POST)
    @ApiOperation(value = "查询今日值班民警", responseContainer = "Map", response = BasePoliceInfoVO.class)
    R<BasePoliceInfoVO> findByDutyPolice(BasePoliceInfoPageDto pageDto);
    
    /**
	 * 获取单位民警数量
	 */
	@RequestMapping(value = "/getPrisonPoliceCount", method = RequestMethod.GET)
	public R<List<PrisonPoliceCountVO>> getPrisonPoliceCount();
	
	/**
	 * 获取单位中拥有指定角色的人
	 */
	@RequestMapping(value = "/getPoliceByRole", method = RequestMethod.GET)
	public R<List<BasePoliceInfoVO>> getPoliceByRole(@RequestParam("prisonId") String prisonId, @RequestParam("roleId") int... roleId);
}
