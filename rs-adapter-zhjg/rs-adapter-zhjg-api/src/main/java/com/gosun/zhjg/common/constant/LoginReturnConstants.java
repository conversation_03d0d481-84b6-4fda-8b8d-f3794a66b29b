package com.gosun.zhjg.common.constant;

/***
 * 登录返回信息静态变量定义
 */
public class LoginReturnConstants {

    public static final Integer PASSWORD_ERROR_CODE = 500;

    public static final Integer LOGIN_SUCCESS_CODE = 200;

    public static final Integer LOGIN_ERROR_CONFIG_CODE = 201;

    public static final Integer LOGIN_NOT_USER_CODE = 700;

    /**
     * 密码长度复杂度校验id编号
     */
    public static final String PASSWORD_CHECK_VALUE = "密码长度至少需要达到8位，且为字母，数字，符号中至少两种，是否立即修改！";

    /**
     * 超过180天未修改密码
     */
    public static final String PASSWORD_DAY_CHECK_VALUE = "超过180天未修改密码,是否立即修改！";

    /**
     * 登入成功
     */
    public static final String LOGIN_SUCCESS = "登录成功";

    /**
     * 用户不存在
     */
    public static final String NOT_USER = "用户不存在";

    /**
     * 账号密码错误
     */
    public static final String PASSWORD_ERROR = "账号密码错误";
}
