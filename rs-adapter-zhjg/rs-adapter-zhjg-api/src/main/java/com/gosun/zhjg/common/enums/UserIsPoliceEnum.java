package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * permission_user表 ispolice字段字典
 * 
 * <AUTHOR>
 *
 */
public enum UserIsPoliceEnum implements IEnum<Integer> {

	Police(1, "警员"), //
	Assistant(0, "辅警"), //
	Other(-1, "其他");//

	private Integer value;
	private String desc;

	UserIsPoliceEnum(Integer value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public Integer getValue() {
		return value;
	}

	public void setValue(Integer value) {
		this.value = value;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(Integer value) {
		for (UserIsPoliceEnum e : UserIsPoliceEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}

	public static String getDesc(String value) {
		for (UserIsPoliceEnum e : UserIsPoliceEnum.values()) {
			if (value != null && value.equals(e.value + "")) {
				return e.desc;
			}
		}
		return null;
	}

	public static Integer getValue(String desc) {
		for (UserIsPoliceEnum e : UserIsPoliceEnum.values()) {
			if (e.getDesc().equals(desc)) {
				return e.value;
			}
		}
		return null;
	}

}