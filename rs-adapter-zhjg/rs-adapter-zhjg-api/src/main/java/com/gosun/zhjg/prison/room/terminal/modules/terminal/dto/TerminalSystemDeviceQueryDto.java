package com.gosun.zhjg.prison.room.terminal.modules.terminal.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 智能终端管理系统-设备列表查询
 *
 * <AUTHOR>
 * @date 2024/2/27 14:07
 */
@ApiModel("智能终端管理系统-设备列表查询")
@Data
public class TerminalSystemDeviceQueryDto extends AbstractPageQueryForm {

	private String deviceName;
	private String areaName;
	private String serialNumber;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新时间-开始", required = false)
	private Date upgradeTimeStart;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新时间-结束", required = false)
	private Date upgradeTimeEnd;

	private String apkVersion;
	private String webVersion;

	@ApiModelProperty(value = "查询 1仓内屏，2仓外屏", required = false)
	private Integer deviceType;

	private List<String> deviceList;
	/**
	 * 是否分頁
	 */
	private boolean pageing = true;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "apk更新时间-开始", required = false)
	private Date apkUpgradeTimeStart;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "apk更新时间-结束", required = false)
	private Date apkUpgradeTimeEnd;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "web更新时间-开始", required = false)
	private Date webUpgradeTimeStart;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "web更新时间-结束", required = false)
	private Date webUpgradeTimeEnd;
}
