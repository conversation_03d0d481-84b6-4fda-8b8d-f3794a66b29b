package com.gosun.zhjg.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 广东行政区域代码-四位
*<AUTHOR>
*@email <EMAIL>
*@date 2022/7/19
*/

@Getter
public enum AreaCityEnum {
    GD("44", "广东"),
    SZ("4403", "深圳市"),
    ZH("4404", "珠海市"),
    ST("4405", "汕头市"),
    FS("4406", "佛山市"),
    SG("4402", "韶关市"),

    ZJ("4408", "湛江市"),
    ZQ("4412", "肇庆市"),
    JM("4407", "江门市"),
    MM("4409", "茂名市"),
    HZ("4413", "惠州市"),
    MZ("4414", "梅州市"),

    SW("4415", "汕尾市"),
    HY("4416", "河源市"),
    <PERSON><PERSON>("4417", "阳江市"),
    QY("4418", "清远市"),
    DG("4419", "东莞市"),
    ZS("4420", "中山市"),

    CZ("4451", "潮州市"),
    JY("4452", "揭阳市"),
    YF("4453", "云浮市");


    private String value;
    private String desc;

    AreaCityEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @JsonValue
    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value) {
        for (AreaCityEnum e : AreaCityEnum.values()) {
            if (e.value.equals(value)) {
                return e.desc;
            }
        }
        return null;
    }

    public static String getValue(String desc) {
        for (AreaCityEnum e : AreaCityEnum.values()) {
            if (e.getDesc().equals(desc)) {
                return e.value;
            }
        }
        return null;
    }

}
