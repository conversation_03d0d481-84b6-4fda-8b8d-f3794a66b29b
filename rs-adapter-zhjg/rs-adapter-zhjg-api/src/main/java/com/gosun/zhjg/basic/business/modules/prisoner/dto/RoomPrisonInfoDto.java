package com.gosun.zhjg.basic.business.modules.prisoner.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/3 17:19
 */
@Data
@ApiModel("监室在押人员列表查询dto")
public class RoomPrisonInfoDto {

    @ApiModelProperty(value = "从属监室")
    private String roomId;

    @ApiModelProperty("去除重病号(传1")
    private Integer sickStatus;

    @ApiModelProperty("去除高风险(传1")
    private Integer riskStatus;

    @ApiModelProperty("去除新入所(传1")
    private Integer newPrisonerStatus;

    @ApiModelProperty("去除械具使用(传1")
    private Integer toolStatus;

    @ApiModelProperty("去除出所就医(传1")
    private Integer outStatus;

    @ApiModelProperty("去除特殊案件(传1")
    private Integer caseStatus;

}
