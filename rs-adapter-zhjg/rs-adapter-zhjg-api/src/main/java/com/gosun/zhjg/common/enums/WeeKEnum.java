package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum WeeKEnum implements IEnum<String> {
    <PERSON>("1", "周一", "一"),
    <PERSON><PERSON>("2", "周二", "二"),
    <PERSON><PERSON>("3", "周三", "三"),
    <PERSON><PERSON>("4","周四","四"),
    <PERSON><PERSON>("5","周五","五"),
    <PERSON><PERSON>("6","周六","六"),
    <PERSON>("0","周日","日");

    private String value;
    private String desc;
    private String shortName;

    WeeKEnum(final String value, final String desc, final String shortName) {
        this.desc = desc;
        this.value = value;
        this.shortName = shortName;
    }

    @Override
    public String getValue() {
        return value;
    }

    public String getShortName() {
        return shortName;
    }

    @JsonValue
    public String getDesc() {
        return desc;
    }

    public static String getValue(String desc) {
        for (WeeKEnum e : WeeKEnum.values()) {
            if (e.desc.equals(desc)) {
                return e.value;
            }
        }
        return null;
    }

    public static String getDesc(String value) {
        for (WeeKEnum e : WeeKEnum.values()) {
            if (e.value.equals(value)) {
                return e.desc;
            }
        }
        return null;
    }

    public static String getShortName(String value) {
        for (WeeKEnum e : WeeKEnum.values()) {
            if (e.value.equals(value)) {
                return e.shortName;
            }
        }
        return null;
    }
}
