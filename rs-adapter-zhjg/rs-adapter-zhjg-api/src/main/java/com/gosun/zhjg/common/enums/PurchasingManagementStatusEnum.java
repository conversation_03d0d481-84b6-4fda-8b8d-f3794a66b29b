package com.gosun.zhjg.common.enums;

/**
 * <AUTHOR>
 * @Description: 枚举
 * @date 2021/7/5
 * @time 17:45
 */
public enum PurchasingManagementStatusEnum {
    //插入 flag 状态
    INSERT_SAVE(1, "保存"),
    INSERT_SEND(0, "发送"),

    //修改 flag 状态
    UPDATE_SAVE(1, "保存"),
    UPDATE_SEND(0, "发送"),
    UPDATE_RETURN(2, "退回"),
    UPDATE_COMPLETE(3, "完成"),

    //记录 status 状态
    RECORD_WAIT_SEND(0, "待发送"),
    RECORD_WAIT_APPROVAL(1, "待审批"),
    RECORD_COMPLETE(2, "已完成"),
    RECORD_RETURN(3, "已退回"),

    //子表 item 状态
    HANDLE_MSG(0, "处理信息"),
    RETURN_MSG(1, "退回信息"),
    SUPPLEMENTARY_MSG(2, "补录信息"),

    //消息通知内容
    MESSAGE_SEND_CONTENT(1, "发起了采购工单"),
    MESSAGE_RETURN_CONTENT(2, "驳回了采购工单"),
    MESSAGE_PASS_CONTENT(3, "通过了采购工单"),
    MESSAGE_SEND_COMPLETE(4,"已将采购工单归档");

    private String key;

    private Integer value;

    PurchasingManagementStatusEnum(Integer value, String key) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }
}
