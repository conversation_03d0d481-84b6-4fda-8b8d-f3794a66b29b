package com.gosun.zhjg.prison.app.modules.yjlf.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
*<AUTHOR>
*@email <EMAIL>
*@date 2020-08-05
*/


@Data
@ApiModel("收发信息")
public class PrisonSendReceiveMessageVO implements Serializable {


    private static final long serialVersionUID = 2444316475370848953L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 押解联防编号
     */
    @ApiModelProperty(value = "押解联防编号" )
    private String defenseId;

    /**
     * 发送消息内容
     */
    @ApiModelProperty(value = "发送消息内容" )
    private String sendContent;

    /**
     * 发送消息时间
     */
    @ApiModelProperty(value = "发送消息时间" )
    private Date sendTime;

    /**
     * 发送消息名字
     */
    @ApiModelProperty(value = "发送消息名字" )
    private String sendName;

    /**
     * 删除标志
     */
    @ApiModelProperty(value = "删除标志" )
    private Integer delFlag;

    @ApiModelProperty("排序标志")
    private Integer orderCode;

    @ApiModelProperty("图片")
    private String photo;

}
