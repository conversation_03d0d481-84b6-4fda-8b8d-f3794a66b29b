package com.gosun.zhjg.basic.business.modules.file.feign;

import com.gosun.zhjg.basic.business.modules.file.vo.BaseFileVo;
import com.gosun.zhjg.common.dto.BaseFileSaveDto;
import com.gosun.zhjg.common.msg.R;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(value = "zhjg-basic-business", path = "basefile",contextId = "basefile")
public interface BaseFileApi {

	@RequestMapping(value = "/list/{linkId}", method = RequestMethod.GET)
	@ApiOperation(value = "列表", responseContainer = "List", response = BaseFileVo.class)
	public R<List<BaseFileVo>> list(@RequestParam("linkId") String... linkId);

	@RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST)
	@ApiOperation(value = "保存或更新", responseContainer = "Map")
	public R<?> saveOrUpdate(@RequestBody List<BaseFileSaveDto> fileList, @RequestParam(value = "linkId", required = false) String linkId);

	@RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST)
	@ApiOperation(value = "保存或更新", responseContainer = "Map")
	public R<?> saveOrUpdate(@RequestBody List<BaseFileSaveDto> fileList);

	@RequestMapping(value = "/delete/linkId", method = RequestMethod.DELETE)
	@ApiOperation(value = "linkId删除", responseContainer = "Map")
	@ApiImplicitParam(paramType = "body", name = "linkIds", value = "编号", required = true, allowMultiple = true, dataType = "String")
	public R<?> delete(@RequestBody String... linkIds);
}
