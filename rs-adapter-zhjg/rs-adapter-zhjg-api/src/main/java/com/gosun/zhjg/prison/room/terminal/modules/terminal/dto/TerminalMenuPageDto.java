package com.gosun.zhjg.prison.room.terminal.modules.terminal.dto;

import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 终端-仓内外屏菜单表
 *
 * <AUTHOR>
 * @date 2024/12/12 10:59
 */
@Data
@ApiModel("终端-仓内外屏菜单表分页Dto")
public class TerminalMenuPageDto extends AbstractPageQueryForm {

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments", required = true)
	private String id;
	/**
	 * 菜单名称
	 */
	@ApiModelProperty(value = "菜单名称", required = true)
	private String menuName;
	/**
	 * 编码
	 */
	@ApiModelProperty(value = "编码", required = true)
	private String code;
	/**
	 * 父级节点ID，-1表根节点
	 */
	@ApiModelProperty(value = "父级节点ID，-1表根节点", required = true)
	private String parentId;
	/**
	 * 节点全路径，前后逗号隔开
	 */
	@ApiModelProperty(value = "节点全路径，前后逗号隔开", required = true)
	private String parentNodes;
	/**
	 * 终端类型 CNP、CWP
	 */
	@ApiModelProperty(value = "终端类型 CNP、CWP", required = true)
	private String terminalType;
	/**
	 * 人员类型，区分民警还是被监管人员菜单 POLICE 、PRISONER
	 */
	@ApiModelProperty(value = "人员类型，区分民警还是被监管人员菜单 POLICE 、PRISONER", required = true)
	private String personnelType;
	/**
	 * 是否分页
	 */
	private Boolean paging;

}
