package com.gosun.zhjg.basic.business.modules.prisoner.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 在押人员信息VO类
 * @date 2020/5/18
 */

@Data
@ApiModel("在押人员信息")
public class BasePrisonerVO {
    @ApiModelProperty("编号")
    private String prisonerId;

    @ApiModelProperty("出所就医编号")
    private String outDoctorId;

    @ApiModelProperty("单位代码")
    private String prisonId;

    @ApiModelProperty("关押单位")
    private String prisonName;

    @ApiModelProperty("姓名")
    private String prisonerName;

    @ApiModelProperty("别名")
    private String bm;

    @ApiModelProperty("衣服号")
    private String identificationServiceNumber;

    @ApiModelProperty("监室号")
    private String roomId;

    @ApiModelProperty("监室名")
    private String roomName;

    @ApiModelProperty("涉嫌罪名")
    private String suspectedCharges;

    @ApiModelProperty("诉讼环节")
    private String litigationLink;

    @ApiModelProperty("办案单位")
    private String caseHandUnit;

    @ApiModelProperty("档案编号")
    private String fileNumber;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("入所时间")
    private Date entryTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("出所时间")
    private Date outTime;

    @ApiModelProperty("风险等级")
    private String riskLevel;

    @ApiModelProperty("风险等级 字典code")
    private String riskLevelCode;

    @ApiModelProperty("健康状况")
    private String healthType;

    @ApiModelProperty("健康状况 C_KSS_JKZK")
    private String healthTypeDisplayName;

    @ApiModelProperty("国籍")
    private String nationality;

    @ApiModelProperty("籍贯")
    private String jg;

    @ApiModelProperty("户籍地")
    private String domicile;

    @ApiModelProperty("婚姻状况")
    private String maritalStatus;

    @ApiModelProperty("在押人员正面照片")
    private String frontPhoto;

    @ApiModelProperty("民族")
    private String nation;

    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("出生日期")
    private Date birth;

    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("关押期限")
    private Date imprisonmentDate;

    @ApiModelProperty("性别")
    private String sex;

    @ApiModelProperty("性别")
    private String sexDisplayName;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("耳目业务编号")
    private String undercoverId;

    @ApiModelProperty("1 :重病号")
    private Integer sickType;

    @ApiModelProperty("1 :高风险")
    private Integer riskType;

    @ApiModelProperty("1: 紧急风险")
    private Integer urgentRiskType;

    @ApiModelProperty("1: 重点关注")
    private Integer importantType;

    @ApiModelProperty("是否处于监室调整环节中 0=否；1=是")
    private String ifChangingRoom;

    @ApiModelProperty("是否耳目 0=否；1=是")
    private String ifUndercover;

    @ApiModelProperty("证件号码")
    private String certificateNumber;

    @ApiModelProperty("出所原因")
    private String outReason;

    @ApiModelProperty("是否处于耳目登记流程中 0=否；1=是")
    private String registeringUndercover;

    @ApiModelProperty("是否处于使用状态中 0=否 1:是")
    private String ingStatus;

    @ApiModelProperty("核实身份")
    private String checkIdentity;

    @ApiModelProperty("身份")
    private String sf;

    @ApiModelProperty("特殊身份")
    private String tssf;

    @ApiModelProperty("文化程度 字典")
    private String degreeEducation;

    @ApiModelProperty("职业 字典")
    private String occupation;

    @ApiModelProperty("政治面貌 字典")
    private String politicalOutlook;

    @ApiModelProperty("同案编号")
    private String commonCaseNo;

    @ApiModelProperty("案件编号")
    private String ajbh;

    @ApiModelProperty("办案单位类型")
    private String caseHandUnitType;

    @ApiModelProperty("办案人")
    private String caseHandler;

    @ApiModelProperty("办案人联系方式")
    private String caseHandlerPhone;

    @ApiModelProperty("羁押日期")
    private String detentionDate;

    @ApiModelProperty("简要案情")
    private String jyaq;

    @ApiModelProperty("现住址")
    private String xzz;

    @ApiModelProperty("现住址详址")
    private String xzzxz;

    @ApiModelProperty("证件类型")
    private String zjlx;

    @ApiModelProperty("户籍地详址")
    private String hjdxz;

    @ApiModelProperty("工作单位")
    private String gzdw;

    @ApiModelProperty("职务")
    private String zw;

    @ApiModelProperty("职务级别")
    private String zwjb;

    @ApiModelProperty("身高")
    private String sg;

    @ApiModelProperty("体重")
    private String tz;

    @ApiModelProperty("足长")
    private String zc;

    @ApiModelProperty("专长")
    private String tc;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("风险评估时间")
    private Date assessmentTime;

    @ApiModelProperty("最终处置日期")
    private Date zzczrq;

    @ApiModelProperty("特殊病")
    private String specialDiseases;

    @ApiModelProperty("送押人")
    private String syr;
    /**
     * 送押单位
     */
    @ApiModelProperty(value = "送押单位")
    private String sydw;

    @ApiModelProperty("备注")
    private String bz;

    @ApiModelProperty("年龄")
    private String age;

    @ApiModelProperty("防疫核查,人员还有未完成的核酸检测：1，没有：0")
    private Integer epidemicState;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("关押期限起")
    private Date gyqxStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("关押期限止")
    private Date gyqxEnd;

    @ApiModelProperty("入所天数")
    private Integer entryDays;

    @ApiModelProperty("1:死刑人员标签")
    private Integer deathTag;
    /**
     * 所处监室等级
     */
    private String roomLevel;

}
