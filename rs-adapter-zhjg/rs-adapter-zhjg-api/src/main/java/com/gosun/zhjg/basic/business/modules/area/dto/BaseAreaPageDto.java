package com.gosun.zhjg.basic.business.modules.area.dto;

import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 区域表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-08-13 08:57:25
 */
@Data
@ApiModel("区域表分页Dto")
public class BaseAreaPageDto extends AbstractPageQueryForm {

    /**
     * 区域ID
     */
    @ApiModelProperty(value = "区域ID")
    private String id;
    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    private String areaName;
    /**
     * 父节点ID
     */
    @ApiModelProperty(value = "父节点ID")
    private String parentId;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer orderId;
    /**
     * 区域类型
     */
    @ApiModelProperty(value = "区域类型")
    private String areaType;


    @ApiModelProperty("查询类型,查区域的所有区域类型：1,2:查询区域下所有的区域包扣子节点的子节点")
    private Integer queryType;
    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码")
    private String areaCode;
    /**
     * 所属监所
     */
    @ApiModelProperty(value = "所属监所")
    private String prisonId;
    private List<String> prisonIdList;

    private String queryText;
}
