package com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.minidev.json.annotate.JsonIgnore;

import java.util.Date;

/**
 * 人脸库管理-被监管人员人脸补录列表
 *
 * <AUTHOR>
 * @date 2023/3/21 10:49
 */
@Data
public class CnpfacePrisonerVO {

    private String personnelId;

    private String personnelType;

    private String name;

    private String prisonerPhoto;

    private String roomId;

    private String roomName;

    private String faceId;

    private String photo;

    @ApiModelProperty("补录时间")
    private Date updateTime;

    @JSONField(serialize = false)
    @JsonIgnore
    private String serialNumber;
}

