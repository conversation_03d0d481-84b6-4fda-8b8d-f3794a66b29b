package com.gosun.zhjg.basic.business.modules.room.feign;

import com.gosun.zhjg.basic.business.modules.room.dto.AreaPrisonRoomPageDto;
import com.gosun.zhjg.basic.business.modules.room.vo.AreaPrisonRoomPageVO;
import com.gosun.zhjg.common.msg.R;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * Create by JieXing on ${Date} 10:14
 */
@FeignClient(value = "zhjg-basic-business", path = "base/room", contextId = "base/room")
public interface RoomApi {

    @ApiOperation(value = "模糊查询监室", response = AreaPrisonRoomPageVO.class)
    @GetMapping("/findByRoom")
    R findByRoom(AreaPrisonRoomPageDto pageDto);

    @ApiOperation(value = "根据监室id获取监室名字", response = String.class)
    @GetMapping("/findRoomNameByRoomId/{roomId}")
    String findRoomNameByRoomId(@PathVariable("roomId") String roomId);

    @ApiOperation(value = "根据监室id获取主协管名字", response = AreaPrisonRoomPageVO.class)
    @GetMapping("/findPoliceNameByRoomId/{roomId}")
    AreaPrisonRoomPageVO findPoliceNameByRoomId(@PathVariable("roomId") String roomId);


    @ApiOperation(value = "数据中心一室一档监室详情", response = AreaPrisonRoomPageVO.class)
    @PostMapping("/findRoomDetailByPost")
    R findRoomDetailByPost(@RequestBody AreaPrisonRoomPageDto pageDto);


    @ApiOperation(value = "通过id获取数据中心一室一档监室详情", response = AreaPrisonRoomPageVO.class)
    @GetMapping("/findRoomDetailNewByAccess")
    R findRoomDetailNewByAccess(@RequestParam("id")String id);
}


