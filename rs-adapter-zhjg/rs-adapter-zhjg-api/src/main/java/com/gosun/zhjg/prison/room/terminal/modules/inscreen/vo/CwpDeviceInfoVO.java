package com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CwpDeviceInfoVO {
	/**
	 * 主键
	 */
	@ApiModelProperty(value = "主键")
	private String id;
	/**
	 * 序列号
	 */
	@ApiModelProperty(value = "序列号")
	private String serialNumber;
	/**
	 * 设备名称
	 */
	@ApiModelProperty("设备名称")
	private String deviceName;
	/**
	 * 监所
	 */
	@ApiModelProperty(value = "监所")
	private String prisonId;

	/**
	 * 监所名称
	 */
	@ApiModelProperty(value = "监所名称")
	private String prisonName;
	/**
	 * 监室类型
	 */
	@ApiModelProperty(value = "监室类型")
	private String roomType;
	/**
	 * 监室号
	 */
	@ApiModelProperty(value = "监室号")
	private String roomId;
	/**
	 * 监室名称
	 */
	@ApiModelProperty(value = "监室名称")
	private String roomName;

	@ApiModelProperty(value = "监室性别")
	private String roomSex;

	@ApiModelProperty(value = "人员数量")
	private Integer prisonerCount;

	@ApiModelProperty(value = "监室等级 字典：ROOM_LEVEL")
	private String roomLevel;
}
