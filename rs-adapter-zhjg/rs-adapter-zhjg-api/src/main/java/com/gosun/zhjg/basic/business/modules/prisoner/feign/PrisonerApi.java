package com.gosun.zhjg.basic.business.modules.prisoner.feign;


import com.gosun.zhjg.basic.business.modules.prisoner.dto.BasePrisonerPageInfoDto;
import com.gosun.zhjg.basic.business.modules.prisoner.vo.BasePrisonerVO;
import com.gosun.zhjg.common.msg.R;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

@Component
@FeignClient(value = "zhjg-basic-business",path = "base/prisoner", contextId = "base/prisoner")
public interface PrisonerApi {
    @PostMapping(value = "/getPrisonerPageList")
    @ApiOperation(value = "在押人员搜索", responseContainer = "List", response = BasePrisonerVO.class)
    R getPrisonerPageList(BasePrisonerPageInfoDto basePrisonerPageInfoDto);

    @GetMapping(value = "/info/{id}")
    @ApiOperation(value = "在押人员信息", responseContainer = "Map", response = BasePrisonerVO.class)
    @ApiImplicitParam(paramType = "path", name = "id", value = "编号", required = true, dataType = "String")
    R<BasePrisonerVO> info(@PathVariable("id") String id);

    @RequestMapping(value = "/findPrisonerNameByRybh/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "获取在押人员姓名", responseContainer = "Map", response = BasePrisonerVO.class)
    @ApiImplicitParam(paramType = "path", name = "id", value = "编号", required = true, dataType = "String")
    R<BasePrisonerVO> findPrisonerNameByRybh(@PathVariable("id") String id);

    @PostMapping(value = "/getPrisonerPageListByPost")
    @ApiOperation(value = "在押人员搜索-跨服务提供第三方", responseContainer = "List", response = BasePrisonerVO.class)
     R getPrisonerPageListByPost(BasePrisonerPageInfoDto basePrisonerPageInfoDto);
}
