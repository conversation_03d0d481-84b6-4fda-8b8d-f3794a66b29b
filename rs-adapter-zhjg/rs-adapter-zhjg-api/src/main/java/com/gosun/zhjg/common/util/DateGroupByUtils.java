package com.gosun.zhjg.common.util;

import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @program: trunk
 * @description: 时间分组Utils
 * @author: TangTao
 * @create: 2019-09-18 16:24
 **/
@SuppressWarnings({ "unchecked", "rawtypes" })
public class DateGroupByUtils<T> {

    public List<T> completionDate(List<T> list, String begin, int daySub, String dateFormat, int format,Class clz) {
        ArrayList<T> dateResult = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        Calendar yesterday = Calendar.getInstance();
        yesterday.add(format, 1);
        Calendar calendar10 = Calendar.getInstance();
        Calendar calendar5 = Calendar.getInstance();
        try {
            Date date = sdf.parse(begin);
            calendar10.setTime(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        for(int curr = 0 ; curr < daySub ; curr++){

            boolean dbDataExist = false;
            int index = 0;
            for(int i  = 0 ; i < list.size() ; i++){
                try {

                    Date date2 = sdf.parse(String.valueOf(getProperty(list.get(i),"getName")));
                    calendar5.setTime(date2);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if(calendar10.compareTo(calendar5) == 0){
                    dbDataExist  = true;
                    index = i;
                    break;
                }
            }
            if(dbDataExist){
                String name=String.valueOf(getProperty(list.get(index),"getName"));
                Integer count=(Integer) getProperty(list.get(index),"getCount");
                setProperty(list.get(index),name,"name");
                setProperty(list.get(index),count,"count");
                dateResult.add(list.get(index));
            }else{
                try {
                    T t= (T) clz.newInstance();
                    String name=sdf.format(calendar10.getTime());
                    setProperty(t,name,"name");
                    setProperty(t,0,"count");
                    dateResult.add(t);
                } catch (InstantiationException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
            calendar10.add(format, 1 );
        }

        return dateResult;
    }

    private Object getProperty(T t,String methodName){
        Method[] m=t.getClass().getMethods();
        for (int i=0;i<m.length;i++){
            if(methodName.toLowerCase().equals(m[i].getName().toLowerCase())){
                try {
                    return m[i].invoke(t);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                }
            }
        }
        return "";
    }

    private void setProperty(T t,Object value,String fieldName)  {
        try {
            PropertyDescriptor pd=new PropertyDescriptor(fieldName,t.getClass());
            Method method=pd.getWriteMethod();
            method.invoke(t,value);
        } catch (IntrospectionException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }
    }

    public static int days(int year, int month) {
        int days = 0;
        if (month != 2) {
            switch (month) {
                case 1:
                case 3:
                case 5:
                case 7:
                case 8:
                case 10:
                case 12:
                    days = 31;
                    break;
                case 4:
                case 6:
                case 9:
                case 11:
                    days = 30;

            }
        } else {
            // 闰年
            if (year % 4 == 0 && year % 100 != 0 || year % 400 == 0)
                days = 29;
            else
                days = 28;

        }
        return days;
    }
}
