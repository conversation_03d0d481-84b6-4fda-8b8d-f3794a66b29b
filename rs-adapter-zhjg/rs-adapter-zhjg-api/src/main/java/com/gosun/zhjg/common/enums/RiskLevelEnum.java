package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 风险研判，风险等级
 *
 * <AUTHOR>
 * @date 2022/9/1 9:30
 */
public enum RiskLevelEnum implements IEnum<Integer> {
    NORMAL(0, "一般风险"), LEVEL1(1, "一级风险"), LEVEL2(2, "二级风险"), LEVEL3(3, "三级风险");

    private Integer value;
    private String desc;

    RiskLevelEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    @JsonValue
    public String getDesc() {
        return desc;
    }

    public static String getDesc(Integer value) {
        for (RiskLevelEnum e : RiskLevelEnum.values()) {
            if (e.value.equals(value)) {
                return e.desc;
            }
        }
        return null;
    }

    public static Integer getValue(String desc) {
        for (RiskLevelEnum e : RiskLevelEnum.values()) {
            if (e.desc.equals(desc)) {
                return e.value;
            }
        }
        return null;
    }
}