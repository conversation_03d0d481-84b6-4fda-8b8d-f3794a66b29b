package com.gosun.zhjg.basic.business.modules.file.dto;

import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 *
 * <AUTHOR>
 *
 */
@Data
@ApiModel("${comments}分页Dto")
public class BaseFilePageDto extends AbstractPageQueryForm {

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments", required = true)
	private String id;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments", required = true)
	private String prisonId;
	/**
	 * 关联业务表表名
	 */
	@ApiModelProperty(value = "关联业务表表名", required = true)
	private String linkTable;
	/**
	 * 关联ID
	 */
	@ApiModelProperty(value = "关联ID", required = true)
	private String linkId;
	/**
	 * 文件路径
	 */
	@ApiModelProperty(value = "文件路径", required = true)
	private String path;
	/**
	 * 文件名
	 */
	@ApiModelProperty(value = "文件名", required = true)
	private String fileName;
	/**
	 * 文件后缀
	 */
	@ApiModelProperty(value = "文件后缀", required = true)
	private String fileType;
	/**
	 * userid
	 */
	@ApiModelProperty(value = "userid", required = true)
	private String createUserId;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments", required = true)
	private String createUsername;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments", required = true)
	private Date createTime;

}
