package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class TerminalVersionUpgradeDto {

    @NotEmpty
    @ApiModelProperty("要升级设备的序列号集合")
    private List<String> serialNumberList;

    @NotBlank
    @ApiModelProperty("更新的包类型    apk、web")
    private String upgradeType;
    /**
     * 要升级包的id
     */
    @NotBlank
    @ApiModelProperty("更新的包id")
    private String packageId;
}
