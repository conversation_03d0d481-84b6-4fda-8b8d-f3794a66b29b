package com.gosun.zhjg.common.enums.commutation;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * prison_commutation表node 减刑的节点
 * 
 * <AUTHOR>
 *
 */
public enum PrisonCommutationNode_JX_Enum implements IEnum<String> {

	JY("JY", "减刑建议阶段"), //
	SWHYJ("SWHYJ", "所务会研究阶段"), //
	GS("GS", "公示阶段"), //
	CBSC("CBSC", "层报审查阶段"), //
	TQJX("TQJX", "提起减刑阶段"), //
	BLCSSX("BLCSSX", "办理出所手续阶段"), //
	DONE("DONE", "已完成"), //
	;

	private String value;
	private String desc;

	PrisonCommutationNode_JX_Enum(String value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(String value) {
		for (PrisonCommutationNode_JX_Enum e : PrisonCommutationNode_JX_Enum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}

	public static String getValue(String desc) {
		for (PrisonCommutationNode_JX_Enum e : PrisonCommutationNode_JX_Enum.values()) {
			if (e.getDesc().equals(desc)) {
				return e.value;
			}
		}
		return null;
	}

	public static Integer getStep(String value) {
		int i = 1;
		for (PrisonCommutationNode_JX_Enum e : PrisonCommutationNode_JX_Enum.values()) {
			if (e.getValue().equals(value)) {
				return i;
			}
			++i;
		}
		return null;
	}
}