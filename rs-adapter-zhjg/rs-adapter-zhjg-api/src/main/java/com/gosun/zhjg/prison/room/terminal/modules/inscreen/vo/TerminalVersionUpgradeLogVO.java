package com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备版本升级日志
 *
 * <AUTHOR>
 * @date 2023/9/7 16:11
 */
@Data
@ApiModel("设备版本升级日志VO")
public class TerminalVersionUpgradeLogVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;
    /**
     * 序列号
     */
    @ApiModelProperty(value = "序列号")
    private String serialNumber;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    private String ip;
    /**
     * 之前apk包版本
     */
    @ApiModelProperty(value = "之前apk包版本")
    private String apkVersion;
    /**
     * 之前web包版本
     */
    @ApiModelProperty(value = "之前web包版本")
    private String webVersion;
    /**
     * 更新的包类型    apk、web
     */
    @ApiModelProperty(value = "更新的包类型    apk、web")
    private String upgradeType;
    /**
     * 要更新版本
     */
    @ApiModelProperty(value = "要更新版本")
    private String upgradeVersion;
    /**
     * 是否更新成功 1、0
     */
    @ApiModelProperty(value = "是否更新成功 1、0")
    private Integer ok;

    public String getOkDisplayName() {
        if (ok == null) {
            return null;
        }
        return ok > 0 ? "成功" : "失败";
    }

    /**
     * 设备的更新时间
     */
    @ApiModelProperty(value = "设备的更新时间")
    private Date upgradeTime;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    private Date createTime;

    private Integer deviceType;

    private String deviceTypeDisplayName;
}
