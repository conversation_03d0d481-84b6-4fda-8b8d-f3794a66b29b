package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto;

import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("监室仓内外屏分页列表")
public class BaseDeviceTerminalTypeDto extends AbstractPageQueryForm {
    @ApiModelProperty("类型")
    private Integer deviceType;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("设备ip")
    private String deviceIp;

    @ApiModelProperty(value = "监所编号",hidden = true)
    private String prisonId;
}
