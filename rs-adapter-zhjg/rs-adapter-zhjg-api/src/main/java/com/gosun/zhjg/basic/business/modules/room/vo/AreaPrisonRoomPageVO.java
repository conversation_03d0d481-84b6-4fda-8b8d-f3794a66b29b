package com.gosun.zhjg.basic.business.modules.room.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gosun.zhjg.common.dto.PhotoDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 监室表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-05-09 16:12:20
 */
@Data
@ApiModel("监室表分页VO")
public class AreaPrisonRoomPageVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 监室ID
     */
    @ApiModelProperty(value = "监室ID")
    private String id;

    @ApiModelProperty("监所id")
    private String prisonId;

    @ApiModelProperty("监所名称")
    private String prisonName;

    @ApiModelProperty(value = "安全检查ID")
    private String safetyCheckId;
    /**
     * 监室名称
     */
    @ApiModelProperty(value = "监室名称")
    private String roomName;

    @ApiModelProperty(value = "主管民警id")
    private String policeId;

    @ApiModelProperty(value = "主管警号")
    private String policeCode;

    @ApiModelProperty("警衔")
    private String policeRank;

    @ApiModelProperty("民警警衔")
    private String policeRank2;

    @ApiModelProperty("民警照片")
    private List<PhotoDto> photo;

    @ApiModelProperty(value = "主管民警照片")
    private String policePhoto;

    @ApiModelProperty("主管民警性别")
    private String policeSex;

    @ApiModelProperty("协管民警性别")
    private String assistPoliceSex;

    @ApiModelProperty(value = "协管民警id")
    private String assistPoliceId;

    @ApiModelProperty(value = "协管民警编号")
    private String assistPoliceCode;

    @ApiModelProperty(value = "协管民警照片")
    private String assistPolicePhoto;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "进仓时间")
    private Date enterTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("签到时间")
    private Date updateTime;

    private String enterPoliceId;

    @ApiModelProperty(value = "进仓民警")
    private String enterPoliceName;

    @ApiModelProperty(value = "协管民警")
    private String assistPoliceName;

    @ApiModelProperty("主协管类型 w:主 a:协管")
    private String userType;

    @ApiModelProperty("主管民警姓名")
    private String policeName;


    @ApiModelProperty("高风险人数")
    private String riskCount;

    @ApiModelProperty("重病号人数")
    private String sickCount;

    @ApiModelProperty("预约人数")
    private Integer currencyCount;
    //    /**
//     * 是否启用:1启用,0停用
//     */
//    @ApiModelProperty(value = "是否启用:1启用,0停用")
//    private String status;
    /**
     * 关押量
     */
    @ApiModelProperty(value = "关押量")
    private Integer imprisonmentAmount;

    /**
     * 设计关押量
     */
    @ApiModelProperty(value = "设计关押量")
    private Integer planImprisonmentAmount;

//    /**
//     * 所属中队
//     */
//    @ApiModelProperty(value = "所属中队")
//    private String areaId;
//    /**
//     * 排序
//     */
//    @ApiModelProperty(value = "排序")
//    private Integer orderId;
//    /**
//     * 监室编号
//     */
//    @ApiModelProperty(value = "监室编号")
//    private String roomCode;
//    /**
//     * 监室类型
//     */
    @ApiModelProperty(value = "监室类型")
    private String roomType;
    //    /**
//     * 人员性别
//     */
    @ApiModelProperty(value = "人员性别")
    private String roomSex;

    @ApiModelProperty(value = "人员数量")
    private Integer count;
//    /**
//     * 所属监所
//     */
//    @ApiModelProperty(value = "所属监所")
//    private String prisonId;

    @ApiModelProperty(value = "所属区域ID")
    private String areaId;

    @ApiModelProperty(value = "所属区域名称")
    private String areaName;

    @ApiModelProperty(value = "所属中队")
    private String squareId;

    @ApiModelProperty(value = "所属中队")
    private String squareName;

    @ApiModelProperty("耳目数量")
    private Integer undercoverCount;

    /**
     * 监室面积
     */
    @ApiModelProperty("监室面积")
    private BigDecimal roomArea;

    /**
     * 人均铺位面积
     */
    @ApiModelProperty("人均铺位面积")
    private BigDecimal avgBedsArea;

    /**
     * 是否一级风险
     */
    @ApiModelProperty("是否一级风险 1：是 ")
    private Integer isLevelRisk;

    @ApiModelProperty("0003-监室")
    private String areaType;

    @ApiModelProperty("协管民警列表")
    private List<AreaPrisonPoliceVo> assistPolice;

    @ApiModelProperty("监室等级")
    private String roomLevel;
}
