package com.gosun.zhjg.common.constant;

/**
 * 基础系统配置
 * acp_sys_basic_rule_config表key
 *
 * <AUTHOR>
 * @date 2024/12/13 9:26
 */
public class BaseSystemRuleConfigConstants {
	/**
	 * 《终端配置》仓外屏是否显示被监管人员照片和姓名，是1否0
	 */
	public static final String TERMINAL_CWP_SHOW_PRISONER_PHOTO_NAME = "TERMINAL_CWP_SHOW_PRISONER_PHOTO_NAME";

	/**
	 * 《终端配置》仓外屏页面操作时限配置，单位秒
	 */
	public static final String TERMINAL_CWP_PAGE_OPERATION_TIME = "TERMINAL_CWP_PAGE_OPERATION_TIME";

	/**
	 * 《终端配置》仓内屏页面操作时限配置，单位秒
	 */
	public static final String TERMINAL_CNP_PAGE_OPERATION_TIME = "TERMINAL_CNP_PAGE_OPERATION_TIME";

	/**
	 * 仓内屏-屏幕保护是否启用，1启用 0禁用
	 */
	public static final String TERMINAL_CNP_SCREEN_PROTECTION_ENABLE = "TERMINAL_CNP_SCREEN_PROTECTION_ENABLE";

	/**
	 * 是否有仓内屏，1有 0无
	 */
	public static final String TERMINAL_HAS_CNP = "TERMINAL_HAS_CNP";

	/**
	 * 是否有仓外屏，1有 0无
	 */
	public static final String TERMINAL_HAS_CWP = "TERMINAL_HAS_CWP";

	/**
	 * 《出入登记》管教认证是否启用，1启用 0禁用
	 */
	public static final String TERMINAL_PRISONER_OUT_IN_POLICE_AUTH = "TERMINAL_PRISONER_OUT_IN_POLICE_AUTH";

	/**
	 * 《出入登记》被监管人员认证是否启用，1启用 0禁用
	 */
	public static final String TERMINAL_PRISONER_OUT_IN_PRISONER_AUTH = "TERMINAL_PRISONER_OUT_IN_PRISONER_AUTH";

	/**
	 * 仓外屏-首页对讲按钮是否启用，1启用 0禁用
	 */
	public static final String TERMINAL_CWP_HOME_PAGE_TALKBACK_ENABLE = "TERMINAL_CWP_HOME_PAGE_TALKBACK_ENABLE";
}
