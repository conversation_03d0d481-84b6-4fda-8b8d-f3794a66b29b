package com.gosun.zhjg.device.server.modules.inscreen.dto;

import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class LbGetAllMasterPageDto extends AbstractPageQueryForm {

	private String svrIp;

	@ApiModelProperty("终端类型：0-主机，1-分机")
	public Integer terminalType;

	@ApiModelProperty("终端编号")
	public Integer displayNum;

	@ApiModelProperty("终端名称")
	public String name;
}
