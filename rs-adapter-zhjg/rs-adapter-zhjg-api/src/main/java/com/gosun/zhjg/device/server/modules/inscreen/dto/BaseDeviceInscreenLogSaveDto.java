package com.gosun.zhjg.device.server.modules.inscreen.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 仓内屏人脸录入失败日志记录表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-05-26 09:01:45
 */
@Data
@ApiModel("仓内屏人脸录入失败日志记录表新增Dto")
public class BaseDeviceInscreenLogSaveDto {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    private String id;
    /**
     * 仓内屏IP
     */
    @ApiModelProperty(value = "仓内屏IP", required = true)
    private String ip;
    /**
     * 监室id
     */
    @ApiModelProperty(value = "监室id", required = true)
    private String roomId;
    /**
     * 人员编号
     */
    @ApiModelProperty(value = "人员编号", required = true)
    private String rybh;
    /**
     * 人员类型
     */
    @ApiModelProperty(value = "人员类型", required = true)
    private String type;
    /**
     * 日志记录时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "日志记录时间", required = true)
    private Date logTime;
    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因", required = true)
    private String reason;
    /**
     * 监室名称
     */
    @ApiModelProperty(value = "监室名称", required = true)
    private String roomName;
    /**
     * 人员姓名
     */
    @ApiModelProperty(value = "人员姓名", required = true)
    private String personName;
    /**
     * 录入失败的照片地址
     */
    @ApiModelProperty(value = "录入失败的照片地址", required = true)
    private String imgUrl;

    /**
     * 录入失败消息
     */
    @ApiModelProperty(value = "录入失败消息", required = true)
    private String msg;

}
