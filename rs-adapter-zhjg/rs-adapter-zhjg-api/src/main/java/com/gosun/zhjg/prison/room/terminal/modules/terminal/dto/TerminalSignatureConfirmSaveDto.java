package com.gosun.zhjg.prison.room.terminal.modules.terminal.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 终端签名确认
 *
 * <AUTHOR>
 * @date 2024/4/12 11:37
 */
@Data
@ApiModel("终端签名确认新增Dto")
public class TerminalSignatureConfirmSaveDto {

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments", required = true)
	private String id;
	/**
	 * 下发签名给人员编号
	 */
	@NotNull(message = "下发签名给人员编号 不能为空!")
	@ApiModelProperty(value = "下发签名给人员编号", required = true)
	private String prisonerId;
//	/**
//	 * 单位代码
//	 */
//	@ApiModelProperty(value = "单位代码", required = true)
//	private String prisonId;
//	/**
//	 * 人员姓名
//	 */
//	@ApiModelProperty(value = "人员姓名", required = true)
//	private String prisonerName;
	/**
	 * 业务类型 healthCheck 入所健康检、
	 * ${@link com.gosun.zhjg.common.enums.TerminalSignatureConfirmBusinessTypeEnum}
	 */
	@NotNull(message = "业务类型 healthCheck 入所健康检、 不能为空!")
	@ApiModelProperty(value = "业务类型 healthCheck 入所健康检、", required = true)
	private String businessType;
	/**
	 * 关联业务主键
	 */
	@NotNull(message = "关联业务主键 不能为空!")
	@ApiModelProperty(value = "关联业务主键", required = true)
	private String linkId;

	@ApiModelProperty("创建人id")
	private String createUserId;
	/**
	 * $column.comments
	 */
	@ApiModelProperty("创建人")
	private String createUserName;

	@ApiModelProperty("是否跳过下发仓内屏代办。默认下发")
	private Boolean skipCnpWorkRemind = false;
	// ============================================ 下面非必填
//	/**
//	 * 是否已签名 1已签名、0未
//	 */
//	@ApiModelProperty(value = "是否已签名 1已签名、0未", required = true)
//	private Integer state;
//	/**
//	 * 确认时间
//	 */
//	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//	@NotNull(message = "确认时间 不能为空!")
//	@ApiModelProperty(value = "确认时间", required = true)
//	private Date confirmTime;
	/**
	 * 签名图片地址
	 */
	@NotNull(message = "签名图片地址 不能为空!")
	@ApiModelProperty(value = "签名图片地址", required = true)
	private String signatureUrl;

	/**
	 * 校验标志，
	 * 1：检查人员是否已下发对应业务的签名任务，存在的抛出异常；
	 * 2：检查人员是否已下发对应业务的签名任务-未完成状态，存在则删除。重新录入；
	 */
	private Integer checkmark;
}
