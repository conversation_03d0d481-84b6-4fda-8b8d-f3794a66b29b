package com.gosun.zhjg.common.msg;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.http.HttpStatus;


/**
 * @Method
 * @params
 * @Return
 * @Exception
 * @Description
 * <AUTHOR>
 * @Date 19-6-5 下午1:44
 */
@SuppressWarnings({"unused","rawtypes","unchecked"})
public class R<T> extends ResponseMessage<T> {

	private static final long serialVersionUID = 1L;

	public static R ResponseOk(){
		R message = new R();
		message.setStatus(HttpStatus.OK.value());
		message.setCode(0);
		message.setReturnCode(0);
		message.setMessage(DEFAULT_SUCCESS_MESSAGE);
		message.setMsg(DEFAULT_SUCCESS_MESSAGE);
		return message;
	}

	public static R ResponseOk(String msg){
		R message = new R();
		message.setStatus(HttpStatus.OK.value());
		message.setMessage(msg);
		message.setCode(0);
		message.setReturnCode(0);
		message.setMsg(msg);

		return message;
	}

	public static R ResponseError(int code,String msg){
		R message = new R();
		message.setStatus(code);
		message.setCode(1);
		message.setReturnCode(1);
		message.setMessage(msg);
		message.setMsg(msg);
		return message;
	}

	public static R ResponseError(String msg){
		R message = new R();
		message.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
		message.setMessage(msg);
		message.setReturnCode(1);
		message.setCode(1);
		return message;
	}

	public static R ResponseError(){
		R message = new R();
		message.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
		message.setMessage(R.DEFAULT_ERROR_MESSAGE);
		message.setReturnCode(1);
		message.setCode(1);
		return message;
	}

	public static R ResponsePage(Page page){
		R message = new R();
		message.setStatus(HttpStatus.OK.value());
		message.setMessage(DEFAULT_SUCCESS_MESSAGE);
		message.setPage(page);
		message.setReturnCode(0);
		message.setCode(0);
		return message;
	}

	public static R ResponseObj(String id,Object obj){
		R message = new R();
		if (null == obj) {
			message.setMessage("没有找到数据 [id = " + id + " ]");
			message.setStatus(HttpStatus.BAD_REQUEST.value());
			return message;
		}
		message.setStatus(HttpStatus.OK.value());
		message.setData(obj);
		message.setMessage(DEFAULT_SUCCESS_MESSAGE);
		message.setReturnCode(0);
		message.setCode(0);
		return message;
	}

	public static R ResponseResult(Object obj) {
		R message = new R();
		message.setCode(0);
		message.setReturnCode(0);
		message.setMsg(DEFAULT_SUCCESS_MESSAGE);
		message.setStatus(HttpStatus.OK.value());
		message.setData(obj);
		message.setMessage(DEFAULT_SUCCESS_MESSAGE);
		return message;
	}


	public R<T> ResultData(T data) {
		R<T> message = new R<T>();
		message.setStatus(HttpStatus.OK.value());
		message.setData(data);
		message.setMessage(DEFAULT_SUCCESS_MESSAGE);
		message.setReturnCode(0);
		message.setCode(0);
		return message;
	}

	public R<T> ResultPage(Page<T> page) {
		R<T> message = new R<T>();
		message.setStatus(HttpStatus.OK.value());
		message.setMessage(DEFAULT_SUCCESS_MESSAGE);
		message.setPage(page);
		message.setReturnCode(0);
		message.setCode(0);
		return message;
	}

	/**
	 * 响应成功结果
	 *
	 * @return 成功结果
	 */
	public static <T> R<T> responseOk() {
		R<T> message = new R<>();
		message.setStatus(HttpStatus.OK.value());
		message.setMessage(DEFAULT_SUCCESS_MESSAGE);
		message.setReturnCode(0);
		message.setCode(0);
		return message;
	}

	/**
	 * 响应成功结果
	 *
	 * @param data 成功数据
	 * @return 成功结果
	 */
	public static <T> R<T> responseOk(T data) {
		R<T> message = new R<>();
		message.setStatus(HttpStatus.OK.value());
		message.setMessage(DEFAULT_SUCCESS_MESSAGE);
		message.setData(data);
		message.setReturnCode(0);
		message.setCode(0);
		return message;
	}

	public static <T> R<T> responseError(String msg){
		return responseError(HttpStatus.INTERNAL_SERVER_ERROR.value(),msg);
	}

	public static <T> R<T> responseError(int code,String msg){
		R message = new R();
		message.setStatus(code);
		message.setMessage(msg);
		message.setReturnCode(1);
		message.setCode(1);
		return message;
	}

	public static <T> R<T> responsePage(Page page){
		R<T> message = new R();
		message.setStatus(HttpStatus.OK.value());
		message.setMessage(DEFAULT_SUCCESS_MESSAGE);
		message.setPage(page);
		message.setReturnCode(0);
		message.setCode(0);
		return message;
	}


}
