package com.gosun.zhjg.prison.room.terminal.modules.terminal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class TerminalSystemUpgradeDto {

	@NotBlank
	@ApiModelProperty(value = "更新包id", required = false)
	private String packageId;

	@NotEmpty
	@ApiModelProperty(value = "设备id集合，列表virtualId", required = false)
	private List<String> deviceList;
}
