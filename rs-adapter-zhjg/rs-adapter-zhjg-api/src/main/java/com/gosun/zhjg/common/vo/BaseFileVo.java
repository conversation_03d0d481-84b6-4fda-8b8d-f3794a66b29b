package com.gosun.zhjg.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 李春明
 * 
 * <AUTHOR>
 *
 */
@Data
@ApiModel("${comments}VO")
public class BaseFileVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String id;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String prisonId;
	/**
	 * 关联业务表表名
	 */
	@ApiModelProperty(value = "关联业务表表名")
	private String linkTable;
	/**
	 * 关联ID
	 */
	@ApiModelProperty(value = "关联ID")
	private String linkId;
	/**
	 * 文件路径
	 */
	@ApiModelProperty(value = "文件路径")
	private String path;
	/**
	 * 兼容之前
	 */
	@Deprecated
	private String filePath;

	/**
	 * 文件路径
	 */
	@ApiModelProperty(value = "视频云基线版本文件路径")
	private String accessUrl;
	/**
	 * 文件名
	 */
	@ApiModelProperty(value = "文件名")
	private String fileName;

	/**
	 * 文件名
	 */
	@ApiModelProperty(value = "视频云基线版本文件名")
	private String originalFilename;
	/**
	 * 文件后缀
	 */
	@ApiModelProperty(value = "文件后缀")
	private String fileType;
	/**
	 * userid
	 */
	@ApiModelProperty(value = "userid")
	private String createUserId;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String createUsername;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Date createTime;
}
