package com.gosun.zhjg.basic.business.modules.prisoner.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gosun.zhjg.basic.business.modules.prisoner.vo.BaseNewPrisonerVO;
import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class BaseNewPrisonerPageInfoDto extends AbstractPageQueryForm<BaseNewPrisonerVO> implements Serializable {
    @ApiModelProperty(value = "姓名（模糊查询）")
    private String prisonerName;

    @ApiModelProperty(value = "编号，多个逗号隔开")
    private String prisonerId;

    @ApiModelProperty(value = "从属监室 多个,分割")
    private String roomId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("入所开始时间")
    private Date entryStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("入所结束时间")
    private Date entryEndTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("关押期限起")
    private Date gyqxStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("关押期限止")
    private Date gyqxEnd;

    @ApiModelProperty("类型,1:耳目，2：风险评估,3:紧急风险，4,械具使用，6:核酸检测")
    private String type;

    @ApiModelProperty("办案单位")
    private String caseHandUnit;

    @ApiModelProperty("查询类型 1=查询是耳目的人员")
    private String queryType;

    @ApiModelProperty("监室名称")
    private String roomName;
}
