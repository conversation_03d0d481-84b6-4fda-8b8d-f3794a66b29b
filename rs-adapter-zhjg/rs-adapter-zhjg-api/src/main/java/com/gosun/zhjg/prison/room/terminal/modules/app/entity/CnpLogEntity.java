package com.gosun.zhjg.prison.room.terminal.modules.app.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 仓内屏日志记录
 *
 * <AUTHOR>
 * @date 2021-11-09 21:13:40
 */
@Data
@TableName("acp_pm_cnp_log")
public class CnpLogEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@TableId
	private String id;
	/**
	 * 仓内屏序列号
	 */
	private String serialNum;
	/**
	 * 日志等级大写 ERROR WARN
	 */
	private String errLevel;
	/**
	 * 日志分类标记
	 */
	private String groupSign;
	/**
	 * 异常类#方法#行
	 */
	private String tag;
	/**
	 * 日志信息1
	 */
	private String msg1;
	/**
	 * $column.comments
	 */
	private String msg2;
	/**
	 * $column.comments
	 */
	private String msg3;
	/**
	 * $column.comments
	 */
	private String msg4;
	/**
	 * 记录时间
	 */
	private Date logTime;

	public void setErrLevel(CnpLogErrLevelEnum logLevel) {
		this.errLevel = logLevel.name();
	}

	public void setErrLevel(String errLevel) {
		this.errLevel = errLevel;
	}

	public static CnpLogEntity newCnpLogEntity(CnpLogErrLevelEnum logLevel, String groupSign, Object... msg) {
		CnpLogEntity entity = new CnpLogEntity();
		entity.setLogTime(new Date());
		entity.setErrLevel(logLevel.name());
		entity.setTag(getTag(3));
		entity.setSerialNum(null);
		entity.setGroupSign(groupSign);
		entity.setMsg1(strMaxLength(getArrayIndex(msg, 0), 255));
		entity.setMsg2(strMaxLength(getArrayIndex(msg, 1), 255));
		entity.setMsg3(strMaxLength(getArrayIndex(msg, 2), 255));
		entity.setMsg4(strMaxLength(getArrayIndex(msg, 3), 255));
		return entity;
	}

	public enum CnpLogErrLevelEnum {
		ERROR, WARN, INFO
	}

	/**
	 * 获取打印日志行信息
	 *
	 * @param depth
	 * @return execute 394#com.ben.aidltest.TestCordova
	 */
	public static String getTag(int depth) {
		// 调用的类名
		String className = Thread.currentThread().getStackTrace()[depth].getClassName();
		// 调用的方法名
		String methodName = Thread.currentThread().getStackTrace()[depth].getMethodName();
		// 调用的行数
		int lineNumber = Thread.currentThread().getStackTrace()[depth].getLineNumber();
		return methodName + " " + lineNumber + "#" + className;
	}

	private static String getArrayIndex(Object[] array, int index) {
		if (array == null || index >= array.length || index < 0) {
			return null;
		}
		return array[index] == null ? null : array[index].toString();
	}

	private static String strMaxLength(String data, int maxLength) {
		if (data == null) {
			return null;
		}
		int length = data.length();
		return (length > maxLength) ? data.substring(0, maxLength) : data;
	}
}
