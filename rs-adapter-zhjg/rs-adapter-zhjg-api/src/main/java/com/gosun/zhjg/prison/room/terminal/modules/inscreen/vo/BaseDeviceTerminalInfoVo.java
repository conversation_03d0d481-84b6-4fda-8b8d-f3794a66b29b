package com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("仓内外屏设备vo")
public class BaseDeviceTerminalInfoVo implements Serializable {

    @ApiModelProperty(value = "序列号")
    private String serialNumber;

    @ApiModelProperty(value = "设备编号")
    private String deviceId;

    @ApiModelProperty(value = "设备ip")
    private String deviceIp;

    @ApiModelProperty("设备状态")
    private String deviceStatus;

    @ApiModelProperty("设备状态")
    private String deviceStatusDisplayName;

    @ApiModelProperty(value = "监室名称")
    private String roomName;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty(value = "本机编号")
    private Integer deviceNum;

    @ApiModelProperty(value = "主机编号")
    private Integer hostNum;

    @ApiModelProperty(value = "地址盒IP")
    private String addressIp;

    @ApiModelProperty("用户名")
    private String devUserName;

    @ApiModelProperty("密码")
    private String devPassword;

    @ApiModelProperty("已绑定的监室数量")
    private Integer bindCount;

    private Integer deviceType;
}
