package com.gosun.zhjg.basic.business.modules.menu.vo;

import com.gosun.zhjg.common.util.TreeLcmUtils.TreeNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseMenuTreeVO extends TreeNode<BaseMenuTreeVO> {

	private String id;

	private String parentId;

	private String nodeName;

	private String menuType;

	private String code;

	private String url;

	private String icon;

	private String openMode;

	private String countUrl;

	private List<BaseMenuTreeVO> child;

	@Override
	public void setChild(List<BaseMenuTreeVO> child) {
		this.child = child;
	}
}
