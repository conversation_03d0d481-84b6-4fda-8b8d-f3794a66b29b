package com.gosun.zhjg.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 所请来源 字典：C_JQLY
 *
 * <AUTHOR>
 */

public enum EventSrcEnum {
    YINGJI("YINGJI", "应急报警"),
    YJBJ("YJBJ", "智能终端报警"),
    QYGKBJ("QYGKBJ", "区域管控报警"),
    DJFJ("DJFJ", "对讲分机报警"),
    ZJBJ("ZJBJ", "周界报警"),
    XLBJ("XLBJ", "手环心率报警"),
    //    SPBJ("SPBJ", "视频异动报警"),
    SPYD("SPYD", "视频异动报警"),
    SQDJ("SQDJ", "所情登记"),
    // 废弃。海康摄像头报警未归类的放到此
    SPBJ("SPBJ", "视频报警"),
    ;

    private String value;
    private String desc;

    EventSrcEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    @JsonValue
    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value) {
        for (EventSrcEnum e : EventSrcEnum.values()) {
            if (e.value.equals(value)) {
                return e.desc;
            }
        }
        return null;
    }
}
