package com.gosun.zhjg.basic.business.modules.device.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BaseDeviceTerminalDto {
    @ApiModelProperty("序列号")
    private String serialNumber;

    @ApiModelProperty("本机编号")
    private Integer deviceNum;

    @ApiModelProperty("主机编号")
    private Integer hostNum;

    @ApiModelProperty("地址盒IP")
    private String addressIp;

    @ApiModelProperty("描述信息")
    private String remark;

    @ApiModelProperty("用户名")
    private String devUserName;

    @ApiModelProperty("密码")
    private String devPassword;

    private String deviceId;

    private String deviceName;

    /**
     * 设备类型
     */
    private String deviceTypeId;

    private String deviceIp;
}
