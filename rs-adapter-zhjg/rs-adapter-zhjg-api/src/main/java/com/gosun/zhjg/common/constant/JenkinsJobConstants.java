package com.gosun.zhjg.common.constant;


/**
 * jenkins 相关
 *<AUTHOR>
 *@email <EMAIL>
 *@date 2022/4/27
 */
public class JenkinsJobConstants {
    /***
     * 任务名-汕尾定制
     */
    public static final String SWAUTH = "Intelligent-upervisiont-shanwei-dev/java/swdz-zhjg-auth";

    public static final String SWBASIC = "Intelligent-upervisiont-shanwei-dev/java/swdz-basic-business";

    public static final String SWBRANCH = "Intelligent-upervisiont-shanwei-dev/java/swdz-branch-work";

    public static final String SWQUALITY = "Intelligent-upervisiont-shanwei-dev/java/swdz-data-quality";

    public static final String SWMANAGER = "Intelligent-upervisiont-shanwei-dev/java/swdz-manager";

    public static final String SWAPP = "Intelligent-upervisiont-shanwei-dev/java/swdz-prison-app";

    public static final String SWTERMINAL = "Intelligent-upervisiont-shanwei-dev/java/swdz-prison-room-terminal";

    public static final String SWWORK = "Intelligent-upervisiont-shanwei-dev/java/swdz-prison-work";

    public static final String SWZUUL = "Intelligent-upervisiont-shanwei-dev/java/swdz-zhjg-zuul";

    public static final String SWALL = "Intelligent-upervisiont-shanwei-dev/java/swjg";

    /***
     * 任务名-基线
     */
    public static final String AUTH = "base-supervision-plat-dev/java/base-zhjg-auth";

    public static final String BASIC = "base-supervision-plat-dev/java/base-basic-business";

    public static final String BRANCH = "base-supervision-plat-dev/java/base-zhjg-branch-work";

    public static final String QUALITY = "base-supervision-plat-dev/java/base-zhjg-data-quality";

    public static final String MANAGER = "base-supervision-plat-dev/java/base-manager";

    public static final String APP = "base-supervision-plat-dev/java/base-zhjg-prison-app";

    public static final String TERMINAL = "base-supervision-plat-dev/java/base-zhjg-prison-room-terminal";

    public static final String WORK = "base-supervision-plat-dev/java/base-zhjg-prison-work";

    public static final String ZUUL = "base-supervision-plat-dev/java/base-zhjg-zuul";


    public static final String SW_GITLAB_ID = "525";

    public static final String GITLAB_ID_FE = "430";

    public static final String SW_GITLAB_ID_FE = "523";

    public static final int CONFIG_SERVER = 110;

    public static final String GITLAB_ID = "433";

    public static final String SW_PROJECT_NAME = "汕尾定制版";

    public static final String PROJECT_NAME = "平台基线版";

    public static final String GITLAB_URL = "http://pagit.gosuncn.com";

    public static final String GITLAB_TOKEN = "********************";

    public static final String ALL = "base-supervision-plat-dev/java/swjg";

    /***
     * 后端服务ip
     */
    public static final String server_ip = "http://**************:8663/";
}
