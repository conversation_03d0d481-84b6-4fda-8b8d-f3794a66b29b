package com.gosun.zhjg.common.context;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据脱敏
 * 
 * <AUTHOR> <br/>
 *         注意role、onlyForbidRole和prison、onlyForbidPrison配置冲突的部分会被去敏感处理
 *
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
//@JacksonAnnotationsInside
//@JsonSerialize(using = SensitiveJsonSerializer.class)
public @interface Sensitive {

	/**
	 * 可以定制需要替换的敏感的部分
	 * 
	 * @see SensitiveJsonSerializer#HIDE_CHARACTER_DEFAULT
	 * 
	 * @return
	 */
	String value() default "";

	/**
	 * 支持正则表达式匹配替换
	 * 
	 * 最终展示等于 "String".replaceAll(regex, value)
	 * 
	 * @return
	 */
	String regex() default "";

	/**
	 * 允许的监所ID，除此之外其他展示* 星
	 * 
	 * @return
	 */
	String[] prison() default {};

	/**
	 * 允许的岗位ID，除此之外其他展示* 星
	 * 
	 * @return
	 */
	int[] role() default {};

	/**
	 * 禁止的监所ID，除此之外全部开放。注意与prison冲突的部分会被去敏感
	 * 
	 * @return
	 */
	String[] onlyForbidPrison() default {};

	/**
	 * 禁止的岗位ID，除此之外全部开放。注意与post冲突的部分会被去敏感
	 * 
	 * @return
	 */
	int[] onlyForbidRole() default {};

	/**
	 * 策略，（定义好默认的匹配规则替换规则，预留）
	 * 
	 * @return
	 */
	@Deprecated
	String strategy() default "";

	/**
	 * 等于1会禁止 去敏感。这里不用管，为了拓展 SensitivePo类
	 * 
	 * @return
	 */
	@Deprecated
	int clean() default 0;
}
