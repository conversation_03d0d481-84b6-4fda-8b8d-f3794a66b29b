package com.gosun.zhjg.common.constant;

public class DictionaryDataCodeConstants {
    //========== 看守所风险的等级 ==========
    /**
     * 一般风险等级
     */
    public static final String FORMAL_RISK = "1";
    /**
     * 一级重大风险
     */
    public static final String HIGH_RISK01 = "2";

    /**
     * 二级重大风险
     */
    public static final String HIGH_RISK02 = "3";

    /**
     * 三级重大风险
     */
    public static final String HIGH_RISK03 = "4";

    /**
     * 一般风险等级
     */
    public static final String FORMAL_RISK_NAME = "一般";
    /**
     * 一级重大风险
     */
    public static final String HIGH_RISK01_NAME = "一级";

    /**
     * 二级重大风险
     */
    public static final String HIGH_RISK02_NAME = "二级";

    /**
     * 三级重大风险
     */
    public static final String HIGH_RISK03_NAME = "三级";

    //========== 戒毒所风险的等级 ==========
    /**
     * 一般安全风险
     */
    public static final String JDS_HIGH_RISK01 = "0";

    /**
     * 较大安全风险
     */
    public static final String JDS_HIGH_RISK02 = "1";

    /**
     * 重大安全风险
     */
    public static final String JDS_HIGH_RISK03 = "2";

    //========== 健康状况代码 ==========
    /**
     * 重病号
     */
    public static final String HIGH_SICK = "8";


    //========== 角色代码 ==========
    /***
     * 管教岗位角色编号
     */
    public static final Integer WARDER_ROLE = RoleConstants.gj_id;

    /***
     * 医务岗角色编号
     */
    public static final Integer DOCTOR_ROLE = RoleConstants.ys_id;

    //========== 监所单位代码 ==========
    /**
     * 第一看守所
     */
    public static final String KSS_Y = "440400111";
    /**
     * 第一看守所
     */
    public static final String KSS_Y_NAME = "珠海市第一看守所";
    /**
     * 珠海市第一戒毒所
     */
    public static final String JDS_Y_NAME = "珠海市第一戒毒所";


    /**
     * 第二看守所
     */
    public static final String KSS_E = "440400112";

    /**
     * 第二看守所
     */
    public static final String KSS_E_NAME = "珠海市第二看守所";

    /**
     * 机构类型：看守所
     */
    public static final Integer JGJG_TYPE_KSS = 1;

    /**
     * *看守所在所表
     */
    public static final String JGJG_TYPE_KSS_TABLE = "vw_acp_pm_prisoner_kss_in";
    /**
     * 机构类型：拘留所
     */
    public static final Integer JGJG_TYPE_JLS = 2;

    /***
     * 拘留所在所表
     */
    public static final String JGJG_TYPE_JLS_TABLE = "vw_acp_pm_prisoner_jls_in";
    /**
     * 机构类型：戒毒所
     */
    public static final Integer JGJG_TYPE_JDS = 3;

    /***personnelType
     * 戒毒所在所表
     */
    public static final String JGJG_TYPE_JDS_TABLE = "vw_acp_pm_prisoner_jds_in";
    /**
     * 机构类型：监管支队
     */
    public static final Integer JGJG_TYPE_JGZD = 4;

    /**
     * 机构类型：医院账号
     */
    public static final Integer JGJG_TYPE_YY = 5;

    /**
     * 珠海市第二戒毒所
     */
    public static final String JDS_E_NAME = "珠海市第二戒毒所";

    /**
     * 第一戒毒所
     */
    public static final String JDS_Y = "440400131";

    /**
     * 第二戒毒所
     */
    public static final String JDS_E = "440400132";

    /**
     * 第一拘留所
     */
    public static final String JLS_Y = "440400121";

    /**
     * 第二拘留所
     */
    public static final String JLS_E = "440400122";

    /**
     * 支队
     */
    public static final String ZD = "440400133";

    public static Integer ESCORT_ORDER_MAX = 0;

    /**
     * 区域类型为监室
     */
    public static final String AREA_TYPE_ROOM = "0003";

    /**
     * 区域类型为审讯室
     */
    public static final String AREA_TYPE_INTERROGATE = "0007";

    /**
     * 区域类型为领事外事会见室内
     */
    public static final String AREA_TYPE_CONSULAR_INTERVIEW_ROOM= "0039";

    /**
     * 区域类型为家属会见室
     */
    public static final String AREA_TYPE_FAMILY = "0015";

    /**
     * 区域类型为律师会见室
     */
    public static final String AREA_TYPE_LAWYER = "0016";

    /**
     * 会见室状态
     */
    public static final String MEET_ROOM_STATUS = "C_SXHJSZT";

    /**
     * 监所类型
     */
    public static final String PRISON_TYPE="C_JGJG";

    /***
     * 监所等级
     */
    public static final String PRISON_LEVEL="C_GAJSDJ";

    /***
     * 监所规模
     */
    public static final String PRISON_GM="C_GM";

    /**
     * 数据来源
     */
    public static final String DATA_SOURCE="C_SJLY";

    /**
     * 监区类型
     */
    public static final String JQLX="0002";

    /**
     * 楼层类型
     */
    public static final String LDLX="0037";

    /**
     * 设备在线编码
     */
    public static final String SBZX="001";

    /***
     * 诉讼阶段-逮捕
     */
    public static final String DP="12";

    /***
     * 诉讼阶段-刑事拘留
     */
    public static final String XSJL="11";

    /***
     * 诉讼阶段-待执行
     */
    public static final String DZX="35";

    /***
     * 诉讼阶段-其它
     */
    public static final String QT="99";

    /***
     * 民警状态-在职
     */
    public static final String MJZZ="在职";

    /**
     * 成功失败状态 0成功
     */
    public static final String C_CGSHZT="C_CGSHZT";

    public static  final String SUCCESS="0";

    public static  final String FAIL="1";

    /**
     * 在押人员在所状态-在所
     */
    public static final String RYZT_IN = "10";

    /**
     * 购物管理-商品属性-普通商品
     */
    public static final Integer TERMINAL_GOODS_ATTR_PUBLIC = 0;
    /**
     * 购物管理-商品属性-特殊商品
     */
    public static final Integer TERMINAL_GOODS_ATTR_SPECIFIC = 1;
    /**
     * 购物管理-审批意见-通过
     */
    public static final Integer TERMINAL_ORDERS_AUDIT_OPINION_PASS = 1;
    /**
     * 购物管理-审批意见-驳回
     */
    public static final Integer TERMINAL_ORDERS_AUDIT_OPINION_REJECT = 2;
    /**
     * 购物管理-购物状态-正常
     */
    public static final Integer TERMINAL_PRISONER_SHOPPING_STATE_NORMAL = 1;
    /**
     * 购物管理-购物状态
     */
    public static final Integer TERMINAL_PRISONER_SHOPPING_STATE_LIMIT = 2;
    /**
     * 购物管理-是否自动签收，1自动签收。2签名签收
     */
    public static final Integer TERMINAL_ORDERS_RECEIPT_AUTO_AUTO = 1;
    /**
     * 购物管理-是否自动签收，1自动签收。2签名签收
     */
    public static final Integer TERMINAL_ORDERS_RECEIPT_AUTO_SIGNATURE = 2;
    /**
     * 购物管理-商品购买对象-全部
     */
    public static final String TERMINAL_GOODS_BUYER_TARGET_ALL = "all";
    /**
     * 在押人员在所状态-出所
     */
    public static final String RYZT_OUT = "11";

    //访客类型-办案民警
    public static  final String  VISITOR_TYPE_BAMJ="0";

    //访客类型-律师
    public static  final String VISITOR_TYPE_LS="1";

    //访客类型-家属
    public static  final String VISITOR_TYPE_JS="2";

    //访客类型-其他
    public static  final String VISITOR_TYPE_QT="3";

    //设备类型-提人交接终端
    public static  final String DEVICE_TYPE_TRJJZD="0023";

    //设备类型-自助会见终端
    public static  final String DEVICE_TYPE_ZJHJZD="0021";

    //设备类型-外来人员终端
    public static  final String DEVICE_TYPE_WLRYZD="0020";


    //设备类型-仓内屏
    public static  final String DEVICE_TYPE_CNP="0008";

    //设备类型-仓外屏
    public static  final String DEVICE_TYPE_CWP="0015";


    public static final String ONLINE_STATE = "001";

    public static final String OFFLINE_STATE = "002";

    //在押人员1
    public static  final Integer FACE_GROUP_ZYRY=1;
    //民警2
    public static  final Integer FACE_GROUP_MJ=1;
    //外来人员3
    public static  final Integer FACE_GROUP_WLRY=3;

    /**
     * 看守所入所原因-刑事拘留
     */
    public static final String C_KSS_RSYY_XSJL="11";

    /**
     *看守所入所原因-逮捕
     */
    public static final String C_KSS_RSYY_DB="12";

    /**
     * 看守所入所原因-临时寄押
     */
    public static final String C_KSS_RSYY_LSJY="13";

    /**
     * 看守所入所原因-异地羁押
     */
    public static final String C_KSS_RSYY_YDJY="16";

    /**
     * 看守所入所原因-本省市转入
     */
    public static final String C_KSS_RSYY_BSSZR="15";


    /**
     * 看守所出所原因-刑满释放
     */
    public static final String C_KSS_CSYY_XMSF="05";

    /**
     * 看守所出所原因-投送监狱
     */
    public static final String C_KSS_CSYY_TSJY="01";

    /**
     * 看守所出所原因-取保候审
     */
    public static final String C_KSS_CSYY_QBHS="09";

    /**
     * 看守所出所原因-假释
     */
    public static final String C_KSS_CSYY_JS="13";

    /**
     * 看守所出所原因-保外就医
     */
    public static final String C_KSS_CSYY_BWJY="12";


    /**
     * 拘留所入所原因-行政拘留
     */
    public static final String C_JLS_RSYY_XZJL="10";

    /**
     * 拘留所入所原因-司法拘留
     */
    public static final String C_JLS_RSYY_SFJL="11";

    /**
     * 拘留所入所原因-拘留审查
     */
    public static final String C_JLS_RSYY_JLSC="12";

    /**
     * 拘留所入所原因-现场拘留
     */
    public static final String C_JLS_RSYY_XCJL="13";

    /**
     * 拘留所入所原因-临时寄拘
     */
    public static final String C_JLS_RSYY_LSJJ="14";


    /**
     * 拘留所出所原因-拘留期满
     */
    public static final String C_JLS_CSYY_JLQM="01";

    /**
     * 拘留所入所原因-提前解除拘留
     */
    public static final String C_JLS_CSYY_TQJCJL="02";

    /**
     * 拘留所出所原因-转刑事拘留
     */
    public static final String C_JLS_RSYY_ZXSJL="21";

    /**
     * 拘留所入所原因-撤销行政拘留
     */
    public static final String C_JLS_CSYY_CXXZJL="40";

    /**
     * 拘留所入所原因-请假
     */
    public static final String C_JLS_CSYY_QJ="98";



    /**
     * 戒毒所入所原因-强制隔离戒毒
     */
    public static final String C_JDS_RSYY_QZGLJD="01";

    /**
     * 戒毒所入所原因-自愿戒毒
     */
    public static final String C_JDS_RSYY_ZYJD="05";

    /**
     * 戒毒所入所原因-行政拘留
     */
    public static final String C_JDS_RSYY_JLSC="02";

    /**
     * 戒毒所入所原因-留所康复
     */
    public static final String C_JDS_RSYY_SLKF="5";

    /**
     * 戒毒所入所原因-所外就医收回
     */
    public static final String C_JDS_RSYY_SWJYSH="07";


    /**
     * 戒毒所出所原因-拘留期满
     */
    public static final String C_JDS_CSYY_JLQM="50";

    /**
     * 戒毒所出所原因-强制所外戒毒
     */
    public static final String C_JDS_CSYY_QZSWJD="40";

    /**
     * 戒毒所入所原因-担保外出
     */
    public static final String C_JDS_CSYY_DBWC="30";

    /**
     * 戒毒所入所原因-按期解除强制戒毒
     */
    public static final String C_JDS_CSYY_AQJCQZJD="10";

    /**
     * 戒毒所入所原因-提前终止强制戒毒
     */
    public static final String C_JDS_CSYY_TQZZJD="11";

}
