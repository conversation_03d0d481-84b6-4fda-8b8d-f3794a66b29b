package com.gosun.zhjg.prison.room.terminal.modules.terminal.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.gosun.zhjg.common.util.TreeLcmUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 终端-仓内外屏菜单表
 *
 * <AUTHOR>
 * @date 2024/12/12 11:00
 */
@Data
@ApiModel("终端-仓内外屏菜单表分页VO")
public class TerminalMenuVO extends TreeLcmUtils.TreeNode<TerminalMenuVO> implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String id;
	/**
	 * 菜单名称
	 */
	@ApiModelProperty(value = "菜单名称")
	private String menuName;
	/**
	 * 编码
	 */
	@ApiModelProperty(value = "编码")
	private String code;
	/**
	 * 父级节点ID，-1表根节点
	 */
	@ApiModelProperty(value = "父级节点ID，-1表根节点")
	private String parentId;
	/**
	 * 终端类型 CNP、CWP
	 */
	@ApiModelProperty(value = "终端类型 CNP、CWP")
	private String terminalType;
	/**
	 * 人员类型，区分民警还是被监管人员菜单 POLICE 、PRISONER
	 */
	@ApiModelProperty(value = "人员类型，区分民警还是被监管人员菜单 POLICE 、PRISONER")
	private String personnelType;
//	/**
//	 * 排序字段，从小到大
//	 */
//	@ApiModelProperty(value = "排序字段，从小到大")
//	private Integer sortOrder;
	/**
	 * 子集菜单
	 */
	private List<TerminalMenuVO> children;

	@JsonIgnore
	private String linkId;
	/**
	 * 是否启用
	 */
	private Boolean enabled;

	@Override
	protected void setChild(List<TerminalMenuVO> child) {
		this.children = child;
	}

	@Override
	protected List<TerminalMenuVO> getChild() {
		return this.children;
	}
}
