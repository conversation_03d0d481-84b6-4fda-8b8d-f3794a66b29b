package com.gosun.zhjg.prison.room.terminal.modules.terminal.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 终端签名确认
 *
 * <AUTHOR>
 * @date 2024/4/12 11:37
 */
@Data
@ApiModel("终端签名确认查询Dto")
public class TerminalSignatureConfirmQueryDto {
	/**
	 * 下发签名给人员编号
	 */
	@NotNull(message = "下发签名给人员编号 不能为空!")
	@ApiModelProperty(value = "下发签名给人员编号", required = true)
	private String prisonerId;

	/**
	 * 业务类型 healthCheck 入所健康检、
	 * ${@link com.gosun.zhjg.common.enums.TerminalSignatureConfirmBusinessTypeEnum}
	 */
	@NotNull(message = "业务类型 healthCheck 入所健康检、 不能为空!")
	@ApiModelProperty(value = "业务类型 healthCheck 入所健康检、", required = true)
	private String businessType;

	private List<String> businessTypeList;

	@JsonIgnore
	@JSONField(serialize = false)
	public List<String> getBusinessTypeList() {
		if (this.businessTypeList != null) {
			return this.businessTypeList;
		}
		return Arrays.asList(StringUtils.delimitedListToStringArray(this.businessType, ","));
	}

	/**
	 * 是否已签名 1已签名、0未
	 */
	private Integer state;
}
