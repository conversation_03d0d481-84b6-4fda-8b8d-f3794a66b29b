package com.gosun.zhjg.basic.business.modules.menu.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 系统菜单分组表
 *
 * <AUTHOR>
 *
 */
@Data
@ApiModel("系统菜单分组表VO")
public class BaseMenuGroupVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String id;
	/**
	 * 分组名
	 */
	@ApiModelProperty(value = "分组名")
	private String groupName;
	/**
	 * 编码
	 */
	@ApiModelProperty(value = "编码")
	private String code;
	/**
	 * 排序大靠前，默认100
	 */
	@ApiModelProperty(value = "排序大靠前，默认100")
	private Integer sort;
	/**
	 * 是否启用 1 / 0
	 */
	@ApiModelProperty(value = "是否启用 1 / 0")
	private Integer enable;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Date createTime;
//	/**
//	 * $column.comments
//	 */
//	@ApiModelProperty(value = "$column.comments")
//	private String createUserId;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String createUsername;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Date updateTime;
//	/**
//	 * $column.comments
//	 */
//	@ApiModelProperty(value = "$column.comments")
//	private String updateUserId;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String updateUserName;
//	/**
//	 * $column.comments
//	 */
//	@ApiModelProperty(value = "$column.comments")
//	private Integer delFlag;

	@ApiModelProperty(value = "分组管理的菜单ID集合")
	List<String> menuIds;
}
