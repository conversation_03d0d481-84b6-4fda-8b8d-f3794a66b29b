package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 出所回所记录-武警大屏
 * 
 * <AUTHOR>
 *
 */
public enum PrisonOutRecordExtEnum implements IEnum<Integer> {

	WAIT_OUT(1, "待出所"), WAIT_RETURN(2, "待回所"), RETURNED(3, "已回所"), OUT(4, "已出所");

	private Integer value;
	private String desc;

	PrisonOutRecordExtEnum(final Integer value, final String desc) {
		this.desc = desc;
		this.value = value;
	}

	@Override
	public Integer getValue() {
		return value;
	}

	public static Integer getValue(String desc) {
		for (PrisonOutRecordExtEnum e : PrisonOutRecordExtEnum.values()) {
			if (e.desc.equals(desc)) {
				return e.value;
			}
		}
		return null;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(Integer value) {
		for (PrisonOutRecordExtEnum e : PrisonOutRecordExtEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}

	public static String getDesc(String value) {
		for (PrisonOutRecordExtEnum e : PrisonOutRecordExtEnum.values()) {
			if (e.value.toString().equals(value)) {
				return e.desc;
			}
		}
		return null;
	}
}
