package com.gosun.zhjg.basic.business.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户对应其他菜单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-09-16 15:37:51
 */
@Data
@ApiModel("用户对应其他菜单表VO")
public class PermissionUserMenuVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 用户主键
     */
    @ApiModelProperty(value = "用户主键")
    private String userid;
    /**
     * 菜单编号
     */
    @ApiModelProperty(value = "菜单编号")
    private String menuCode;

}
