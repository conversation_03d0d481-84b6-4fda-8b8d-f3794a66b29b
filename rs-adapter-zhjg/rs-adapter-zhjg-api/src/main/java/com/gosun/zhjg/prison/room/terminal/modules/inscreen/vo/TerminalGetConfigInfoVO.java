package com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 仓内屏获取设备信息VO
 *
 * <AUTHOR>
 * @date 2023/9/11 18:55
 */
@Data
public class TerminalGetConfigInfoVO {

    /**
     * apk版本
     */
    private String appVersion;
    /**
     * web版本
     */
    private String webVersion;
    /**
     * 设备ip
     */
    private String devIp;
    /**
     * 设备序列号
     */
    private String devSerial;
    /**
     * 兼容版本
     */
    private String matchVer;
    /**
     * 连接服务ip
     */
    private String serverIp;
    private Integer serverPort;
    /**
     * web包更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date webUpgradeTime;
    /**
     * apk更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date apkUpgradeTime;
}
