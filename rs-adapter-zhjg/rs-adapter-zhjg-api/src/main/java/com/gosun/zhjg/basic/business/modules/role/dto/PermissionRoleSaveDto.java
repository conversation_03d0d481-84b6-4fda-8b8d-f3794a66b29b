package com.gosun.zhjg.basic.business.modules.role.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 角色表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-05-11 20:09:15
 */
@Data
@ApiModel("角色表新增Dto")
public class PermissionRoleSaveDto {
//	/**
//	 * 角色ID
//	 */
//	@NotNull(message = "角色ID 不能为空!")
//	@ApiModelProperty(value = "角色ID", required = true)
	private Integer roleid;
	/**
	 * 备注
	 */
//	@NotNull(message = "备注 不能为空!")
	@ApiModelProperty(value = "备注", required = true)
	private String remark;
	/**
	 * 角色名称
	 */
	@NotNull(message = "角色名称 不能为空!")
	@ApiModelProperty(value = "角色名称", required = true)
	private String rolename;
	/**
	 * 岗位编码（新增）
	 */
//	@NotNull(message = "岗位编码 不能为空!")
	@ApiModelProperty(value = "岗位编码（新增）", required = false)
	private String rolepost;

	@ApiModelProperty(value = "默认首页", required = false)
	private String homePageCode;
}
