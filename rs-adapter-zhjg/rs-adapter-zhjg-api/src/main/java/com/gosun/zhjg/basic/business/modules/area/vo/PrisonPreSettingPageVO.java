package com.gosun.zhjg.basic.business.modules.area.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * prison_pre_setting-三维预置位表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-11-25 15:06:11
 */
@Data
@ApiModel("prison_pre_setting-三维预置位表分页VO")
public class PrisonPreSettingPageVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String title;
    /**
     * 用户编号
     */
    @ApiModelProperty(value = "用户编号")
    private String userid;
    /**
     * 监所编号
     */
    @ApiModelProperty(value = "监所编号")
    private String prisonId;
    /**
     * 精度
     */
    @ApiModelProperty(value = "精度")
    private String longitude;
    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private String latitude;
    /**
     * 高度
     */
    @ApiModelProperty(value = "高度")
    private String height;
    /**
     * 方位角
     */
    @ApiModelProperty(value = "方位角")
    private String heading;
    /**
     * 俯仰角
     */
    @ApiModelProperty(value = "俯仰角")
    private String pitch;
    /**
     * 滚动角
     */
    @ApiModelProperty(value = "滚动角")
    private String roll;
    /**
     * 是否可删除
     */
    @ApiModelProperty(value = "是否可删除")
    private Integer flag;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

}
