package com.gosun.zhjg.common.util;

import org.springframework.util.Assert;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Optional;

public class RequestContextUtil {

	private RequestContextUtil() {
	}

	public static HttpServletRequest getRequest() {
		ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		Assert.notNull(servletRequestAttributes, "ServletRequestAttributes is null");
		return servletRequestAttributes.getRequest();
	}

	public static HttpServletResponse getResponse() {
		ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		Assert.notNull(servletRequestAttributes, "ServletRequestAttributes is null");
		return servletRequestAttributes.getResponse();
	}

	/**
	 * 获取App-key
	 * 
	 * @return appKey
	 */
	public static String getAppKey() {
		return Optional.ofNullable(getRequest().getHeader("appKey")).orElse(getRequest().getParameter("appKey"));
	}

	/**
	 * 获取App-Secret
	 * 
	 * @return appSecret
	 */
	public static String getAppSecret() {
		return Optional.ofNullable(getRequest().getHeader("appSecret")).orElse(getRequest().getParameter("appSecret"));
	}
}
