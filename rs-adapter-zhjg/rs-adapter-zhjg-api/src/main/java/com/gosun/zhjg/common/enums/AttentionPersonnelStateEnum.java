package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 重点人员关注状态
 * attention_personnel 表 state
 *
 * <AUTHOR>
 * @date 2025/2/26 17:33
 */
public enum AttentionPersonnelStateEnum implements IEnum<Integer> {
	PENDING(1, "审批中"),
	REJECTED(2, "审批不通过"),
	MONITORING(3, "关注中"),
	COMPLETED(4, "已结束"),
	;

	@Getter
	private final Integer value;
	private final String desc;

	AttentionPersonnelStateEnum(Integer value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(Integer value) {
		for (AttentionPersonnelStateEnum e : AttentionPersonnelStateEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}

	public static Integer getValue(String desc) {
		for (AttentionPersonnelStateEnum e : AttentionPersonnelStateEnum.values()) {
			if (e.desc.equals(desc)) {
				return e.value;
			}
		}
		return null;
	}
}