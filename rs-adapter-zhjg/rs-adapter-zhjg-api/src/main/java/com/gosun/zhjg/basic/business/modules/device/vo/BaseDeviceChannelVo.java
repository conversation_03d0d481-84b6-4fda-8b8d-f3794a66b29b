package com.gosun.zhjg.basic.business.modules.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("设备-摄像机通道dto")
public class BaseDeviceChannelVo {


    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("通道id")
    private String channelId;

    @ApiModelProperty("通道名称")
    private String channelName;

    @ApiModelProperty("摄像机类型（0枪机，1球机）")
    private Integer type;

}
