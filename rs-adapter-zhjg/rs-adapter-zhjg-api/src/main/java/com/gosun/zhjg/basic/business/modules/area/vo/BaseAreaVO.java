package com.gosun.zhjg.basic.business.modules.area.vo;

import com.gosun.zhjg.basic.business.modules.room.dto.AreaPrisonRoomDto;
import com.gosun.zhjg.basic.business.modules.room.dto.MeetRoomDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 区域表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-08-13 08:57:25
 */
@Data
@ApiModel("区域表VO")
public class BaseAreaVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 区域ID
     */
    @ApiModelProperty(value = "区域ID")
    private String id;
    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    private String areaName;
    /**
     * 父节点ID
     */
    @ApiModelProperty(value = "父节点ID")
    private String parentId;

    @ApiModelProperty(value = "父节点名字")
    private String parentName;

    /**
     * 父区域的路径信息,#分隔
     */
    @ApiModelProperty(value = "父区域的路径信息,#分隔")
    private String allParentId;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer orderId;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 区域类型
     */
    @ApiModelProperty(value = "区域类型")
    private String areaType;

    @ApiModelProperty(value = "区域类型字符串")
    private String areaTypeDisplayName;
    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码")
    private String areaCode;

    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码")
    private String areaId;
    /**
     * 所属监所
     */
    @ApiModelProperty(value = "所属监所")
    private String prisonId;
    /**
     * 中队名称
     */
    @ApiModelProperty("中队名称")
    private String squadronName;
    /**
     * 所属监区编码
     */
    @ApiModelProperty(value = "所属监区编码")
    private String mapCode;

    /**
     * 所属监区编码
     */
    @ApiModelProperty(value = "所属监区编码")
    private String mapId;

    @ApiModelProperty(value = "能否删除，1能，0不能")
    private Integer IsDelete;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date creatTime;

    @ApiModelProperty("监室信息，区域类型为监室时传")
    private AreaPrisonRoomDto areaPrisonRoomDto;

    @ApiModelProperty("会见室信息，区域类型为审讯室，家属会见室或者律师会见室时传")
    private MeetRoomDto meetRoomDto;

    @ApiModelProperty(value = "电子地图面编号")
    private String surfaceId;
}
