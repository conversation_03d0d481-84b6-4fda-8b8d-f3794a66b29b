package com.gosun.zhjg.prison.room.terminal.modules.app.feign;

import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.prison.room.terminal.modules.app.entity.CnpLogEntity;
import com.gosun.zhjg.prison.room.terminal.modules.app.entity.ScreenCandidEntity;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(value = "zhjg-prison-room-terminal", path = "app/cnplog",contextId = "app/cnplog")
public interface CnpLogFeign {

	@ApiOperation("记录已下发人脸照片")
	@PostMapping("logScreenPhoto")
	public R<?> screenPhoto(@RequestBody ScreenCandidEntity entity);

	@PostMapping
	public R<?> log(@RequestBody CnpLogEntity entity);
}
