package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 工作人员在职状态 参照字典 C_ZZZT
 * 
 * <AUTHOR>
 *
 */
public enum UserStatusEnum implements IEnum<Integer> {
	ZZ(0, "在职"), LZ(1, "离职");

	private Integer value;
	private String desc;

	UserStatusEnum(Integer value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public Integer getValue() {
		return value;
	}

	public void setValue(Integer value) {
		this.value = value;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(Integer value) {
		for (UserStatusEnum e : UserStatusEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}
	
	public static Integer getValue(String desc) {
		for (UserStatusEnum e : UserStatusEnum.values()) {
			if (e.desc.equals(desc)) {
				return e.value;
			}
		}
		return null;
	}
}