package com.gosun.zhjg.common.util;

public class RedisKeyUtil {

	/**
     * redis的key
     * 形式为：
     * 表名:主键名:主键值:列名
     *
     * @param tableName 表名
     * @param majorKey 主键名
     * @param majorKeyValue 主键值
     * @param column 列名
     * @return
     */
    public static String getKeyByTableWithColumn(String tableName,String majorKey,String majorKeyValue,String column){
        StringBuffer buffer = new StringBuffer();
        buffer.append(tableName).append(":");
        buffer.append(majorKey).append(":");
        buffer.append(majorKeyValue).append(":");
        buffer.append(column);
        return buffer.toString();
    }
    
    /**
     * redis的key
     * 形式为：
     * 表名:主键名:主键值
     *
     * @param tableName 表名
     * @param majorKey 主键名
     * @param majorKeyValue 主键值
     * @return
     */
    public static String getKeyByTable(String tableName,String majorKey,String majorKeyValue){
        StringBuffer buffer = new StringBuffer();
        buffer.append(tableName).append(":");
        buffer.append(majorKey).append(":");
        buffer.append(majorKeyValue).append(":");
        return buffer.toString();
    }
    
    /**
     * redis的key
     * 形式为：
     * 模块:功能:子项:元素
     *
     * @param moduleName 模块
     * @param functionName 功能
     * @param subitem 子项
     * @param element 元素
     * @return
     */
    public static String getKeyByModuleWithElement(String moduleName,String functionName,String subitem,String element){
        StringBuffer buffer = new StringBuffer();
        buffer.append(moduleName).append(":");
        buffer.append(functionName).append(":");
        buffer.append(subitem).append(":");
        buffer.append(element);
        return buffer.toString();
    }
    
    /**
     * redis的key
     * 形式为：
     * 模块:功能:子项
     *
     * @param moduleName 模块
     * @param functionName 功能
     * @param subitem 子项
     * @return
     */
    public static String getKeyByModule(String moduleName,String functionName,String subitem){
        StringBuffer buffer = new StringBuffer();
        buffer.append(moduleName).append(":");
        buffer.append(functionName).append(":");
        buffer.append(subitem);
        return buffer.toString();
    }
}
