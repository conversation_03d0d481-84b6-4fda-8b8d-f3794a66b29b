package com.gosun.zhjg.basic.business.modules.role.feign;

import com.gosun.zhjg.basic.business.modules.role.vo.PermissionRoleVO;
import com.gosun.zhjg.common.msg.R;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@Component
@FeignClient(value = "zhjg-basic-business",path = "role/permissionrole", contextId = "role/permissionrole")
public interface PermissionRoleApi {

    @RequestMapping(value = "/info/{roleId}", method = RequestMethod.GET)
    @ApiOperation(value = "角色表-详情", responseContainer = "Map", response = PermissionRoleVO.class)
    @ApiImplicitParam(paramType = "path", name = "roleId", value = "角色ID", required = true, dataType = "Integer")
    public R<PermissionRoleVO> info(@PathVariable("roleId") Integer roleId);
}
