package com.gosun.zhjg.basic.business.modules.role.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 角色表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-05-11 20:09:15
 */
@Data
@ApiModel("角色表VO")
public class PermissionRoleVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 角色ID
	 */
	@ApiModelProperty(value = "角色ID")
	private Integer roleid;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 角色名称
	 */
	@ApiModelProperty(value = "角色名称")
	private String rolename;
//	/**
//	 * 未知1
//	 */
//	@ApiModelProperty(value = "未知1")
//	private String roleorgcode;
//	/**
//	 * 未知2
//	 */
//	@ApiModelProperty(value = "未知2")
//	private String roleorgname;
//	/**
//	 * 角色类型
//	 */
//	@ApiModelProperty(value = "角色类型")
//	private String roletypecode;
	/**
	 * 岗位编码（新增）
	 */
	@ApiModelProperty(value = "岗位编码（新增）")
	private String rolepost;

	/**
	 * 岗位编码（新增）
	 */
	@ApiModelProperty(value = "岗位编码（新增） 中文")
	private String postName;

	private String createTime;

	@ApiModelProperty(value = "是否只读 0 和1）")
	private Integer readOnly = 0;

//	@ApiModelProperty(value = "拥有权限集合")
//	private List<BaseMenuVO> menus;
	/**
	 * 角色首页，code\ name \ url
	 */
	private String homePageCode;
	private String homePageName;
	private String homePageUrl;
}
