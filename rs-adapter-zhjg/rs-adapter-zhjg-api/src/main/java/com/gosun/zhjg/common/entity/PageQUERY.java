package com.gosun.zhjg.common.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
*<AUTHOR>
*@email <EMAIL>
*@date 2021/12/23
*/

@Data
@ApiModel("分页实体")
public class PageQUERY {
    @ApiModelProperty(value = "当前页",required=true)
    private Integer curPage;

    @ApiModelProperty(value = "分页大小",required=true)
    private Integer pageSize;
}
