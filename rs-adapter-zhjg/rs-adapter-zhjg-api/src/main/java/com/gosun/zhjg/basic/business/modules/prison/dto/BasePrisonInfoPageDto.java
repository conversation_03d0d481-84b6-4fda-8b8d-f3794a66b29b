package com.gosun.zhjg.basic.business.modules.prison.dto;

import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BasePrisonInfoPageDto extends AbstractPageQueryForm{
	/**
	 * 监所名称
	 */
	@ApiModelProperty(value = "监所名称")
	private String prisonName;

	@ApiModelProperty(value = "监所简称")
	private String prisonAbbreviation;

	@ApiModelProperty(value = "领导名称")
	private String leaderName;

	@ApiModelProperty(value = "所属支队")
	private String branchId;
}
