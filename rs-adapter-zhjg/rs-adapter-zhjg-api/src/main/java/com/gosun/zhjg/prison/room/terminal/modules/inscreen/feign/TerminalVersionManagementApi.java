package com.gosun.zhjg.prison.room.terminal.modules.inscreen.feign;

import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.TerminalVersionManagementsDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@Component
@FeignClient(value = "zhjg-prison-room-terminal", path = "inscreen/terminalversionmanagement",contextId = "inscreen/terminalversionmanagement")
public interface TerminalVersionManagementApi {

    /**
     * 保存-根据文件名
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/saveByPackageName", method = RequestMethod.POST)
    R<?> saveByPackageName(@RequestBody TerminalVersionManagementsDto form);

    /**
     * 检查包名是否正确
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/checkPackageName", method = RequestMethod.POST)
    R<?> checkPackageName(@RequestBody TerminalVersionManagementsDto form);
}
