package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 出入登记-人员带出状态
 * cwp_prisoner_out_in_status 表
 *
 * <AUTHOR>
 * @date 2025/3/21 9:46
 */
public enum CwpPrisonerOutInStatusStatusEnum implements IEnum<Integer> {
	WAITING_OUT(0, "待带出"),
	WAITING_IN(1, "待带回"),
	DONE(2, "已完成"),
	;

	@Getter
	private final Integer value;
	private final String desc;

	CwpPrisonerOutInStatusStatusEnum(Integer value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(Integer value) {
		for (CwpPrisonerOutInStatusStatusEnum e : CwpPrisonerOutInStatusStatusEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}

	public static Integer getValue(String desc) {
		for (CwpPrisonerOutInStatusStatusEnum e : CwpPrisonerOutInStatusStatusEnum.values()) {
			if (e.desc.equals(desc)) {
				return e.value;
			}
		}
		return null;
	}
}