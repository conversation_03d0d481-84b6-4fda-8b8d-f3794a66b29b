package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto;

import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备版本信息表
 *
 * <AUTHOR>
 * @date 2023/9/7 16:00
 */
@Data
@ApiModel("设备版本信息表分页Dto")
public class TerminalVersionInfoPageDto extends AbstractPageQueryForm {

    /**
     * 序列号
     */
    @ApiModelProperty(value = "序列号", required = true)
    private String serialNumber;

    @ApiModelProperty("设备类型")
    private Integer deviceType;

    /**
     * ip
     */
    @ApiModelProperty(value = "ip", required = true)
    private String ip;
    /**
     * 当前apk包版本
     */
    @ApiModelProperty(value = "当前apk包版本", required = true)
    private String apkVersion;
    /**
     * 当前web包版本
     */
    @ApiModelProperty(value = "当前web包版本", required = true)
    private String webVersion;

    private String roomId;
}
