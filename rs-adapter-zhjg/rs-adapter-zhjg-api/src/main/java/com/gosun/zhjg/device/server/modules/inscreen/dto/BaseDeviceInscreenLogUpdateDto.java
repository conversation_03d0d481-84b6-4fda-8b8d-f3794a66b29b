package com.gosun.zhjg.device.server.modules.inscreen.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;


/**
 * 仓内屏人脸录入失败日志记录表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-05-26 09:01:45
 */
@Data
@ApiModel("仓内屏人脸录入失败日志记录表更新Dto")
public class BaseDeviceInscreenLogUpdateDto {

    /**
     * 主键
     */
    @NotNull(message = "主键 不能为空!")
    @ApiModelProperty(value = "主键", required = true)
    private String id;
    /**
     * 仓内屏IP
     */
    @NotNull(message = "仓内屏IP 不能为空!")
    @ApiModelProperty(value = "仓内屏IP", required = true)
    private String ip;
    /**
     * 监室id
     */
    @NotNull(message = "监室id 不能为空!")
    @ApiModelProperty(value = "监室id", required = true)
    private String roomId;
    /**
     * 人员编号
     */
    @NotNull(message = "人员编号 不能为空!")
    @ApiModelProperty(value = "人员编号", required = true)
    private String rybh;
    /**
     * 人员类型
     */
    @NotNull(message = "人员类型 不能为空!")
    @ApiModelProperty(value = "人员类型", required = true)
    private String type;
    /**
     * 日志记录时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "日志记录时间 不能为空!")
    @ApiModelProperty(value = "日志记录时间", required = true)
    private Date logTime;
    /**
     * 失败原因
     */
    @NotNull(message = "失败原因 不能为空!")
    @ApiModelProperty(value = "失败原因", required = true)
    private String reason;
    /**
     * 监室名称
     */
    @NotNull(message = "监室名称 不能为空!")
    @ApiModelProperty(value = "监室名称", required = true)
    private String roomName;
    /**
     * 人员姓名
     */
    @NotNull(message = "人员姓名 不能为空!")
    @ApiModelProperty(value = "人员姓名", required = true)
    private String personName;
    /**
     * 录入失败的照片地址
     */
    @NotNull(message = "录入失败的照片地址 不能为空!")
    @ApiModelProperty(value = "录入失败的照片地址", required = true)
    private String imgUrl;

}
