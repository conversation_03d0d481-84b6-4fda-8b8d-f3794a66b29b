package com.gosun.zhjg.basic.business.modules.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 删除表日志记录(DeleteTableLog)dto
 *
 * <AUTHOR>
 * @since 2023-06-19 09:59:36
 */
@Data
@ApiModel("删除表日志记录dto")
public class DeleteTableLogDto implements Serializable {
    private static final long serialVersionUID = 802438026033110800L;

        @ApiModelProperty("主键id")
        private String id;

        @ApiModelProperty("配置id")
        private String configId;

        @ApiModelProperty("开始时间")
        private Date startTime;

        @ApiModelProperty("结束时间")
        private Date endTime;

        @ApiModelProperty("清理行数")
        private Integer num;


}
