package com.gosun.zhjg.basic.business.modules.prisoner.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020-11-12
 */

@Data
public class BasePrisonerKssInVO implements Serializable {

    private static final long serialVersionUID = 7098491254053423283L;

    /**
     * 人员编号
     */
    @ApiModelProperty(value = "人员编号")
    private String rybh;

    /**
     * 案件编号
     */
    @ApiModelProperty(value = "案件编号")
    private String ajbh;

    /**
     * 办案单位
     */
    @ApiModelProperty(value = "办案单位")
    private String badw;

    /**
     * 办案单位类型
     */
    @ApiModelProperty(value = "办案单位类型")
    private String badwlx;

    /**
     * 办案人
     */
    @ApiModelProperty(value = "办案人")
    private String bar;

    /**
     * 办案人联系方式
     */
    @ApiModelProperty(value = "办案人联系方式")
    private String barlxff;

    /**
     * 变更人
     */
    @ApiModelProperty(value = "变更人")
    private String bgr;

    /**
     * 变更时间
     */
    @ApiModelProperty(value = "变更时间")
    private Date bgsj;

    /**
     * 别名
     */
    @ApiModelProperty(value = "别名")
    private String bm;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String bz;

    /**
     * 出所去向
     */
    @ApiModelProperty(value = "出所去向")
    private String csqx;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    private Date csrq;

    /**
     * 出所时间
     */
    @ApiModelProperty(value = "出所时间")
    private Date cssj;

    /**
     * 出所原因
     */
    @ApiModelProperty(value = "出所原因")
    private String csyy;

    /**
     * 床位号
     */
    @ApiModelProperty(value = "床位号")
    private String cwh;

    /**
     * 成员类型
     */
    @ApiModelProperty(value = "成员类型")
    private String cylx;

    /**
     * 档案编号
     */
    @ApiModelProperty(value = "档案编号")
    private String dabh;

    /**
     * 逮捕日期
     */
    @ApiModelProperty(value = "逮捕日期")
    private Date dbrq;

    /**
     * 单位代码
     */
    @ApiModelProperty(value = "单位代码")
    private String dwdm;

    /**
     * 附加处理
     */
    @ApiModelProperty(value = "附加处理")
    private String fjcl;

    /**
     * 附物编号
     */
    @ApiModelProperty(value = "附物编号")
    private String fwbh;

    /**
     * 风险等级
     */
    @ApiModelProperty(value = "风险等级")
    private String fxdj;

    /**
     * 国籍
     */
    @ApiModelProperty(value = "国籍")
    private String gj;

    /**
     * 管理类别
     */
    @ApiModelProperty(value = "管理类别")
    private String gllb;

    /**
     * 关押期限
     */
    @ApiModelProperty(value = "关押期限")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date gyqx;

    /**
     * 工作单位
     */
    @ApiModelProperty(value = "工作单位")
    private String gzdw;

    /**
     * 户籍地
     */
    @ApiModelProperty(value = "户籍地")
    private String hjd;

    /**
     * 户籍地详址
     */
    @ApiModelProperty(value = "户籍地详址")
    private String hjdxz;

    /**
     * 婚姻状况
     */
    @ApiModelProperty(value = "婚姻状况")
    private String hyzk;

    /**
     * 经办人
     */
    @ApiModelProperty(value = "经办人")
    private String jbr;

    /**
     * 经办时间
     */
    @ApiModelProperty(value = "经办时间")
    private Date jbsj;

    /**
     * 籍贯
     */
    @ApiModelProperty(value = "籍贯")
    private String jg;

    /**
     * 健康状况
     */
    @ApiModelProperty(value = "健康状况")
    private String jkzk;

    /**
     * 简历
     */
    @ApiModelProperty(value = "简历")
    private String jl;

    /**
     * 拘留日期
     */
    @ApiModelProperty(value = "拘留日期")
    private Date jlrq;

    /**
     * 监室号
     */
    @ApiModelProperty(value = "监室号")
    private String jsh;

    /**
     * 简要案情
     */
    @ApiModelProperty(value = "简要案情")
    private String jyaq;

    /**
     * 羁押日期
     */
    @ApiModelProperty(value = "羁押日期")
    private Date jyrq;

    /**
     * 留所原因
     */
    @ApiModelProperty(value = "留所原因")
    private String lsyy;

    /**
     * 民族
     */
    @ApiModelProperty(value = "民族")
    private String mz;

    /**
     * 入所时间
     */
    @ApiModelProperty(value = "入所时间")
    private Date rssj;

    /**
     * 入所原因
     */
    @ApiModelProperty(value = "入所原因")
    private String rsyy;

    /**
     * 人员状态
     */
    @ApiModelProperty(value = "人员状态")
    private String ryzt;

    /**
     * 识别服号
     */
    @ApiModelProperty(value = "识别服号")
    private String sbfh;

    /**
     * 审查起诉日期
     */
    @ApiModelProperty(value = "审查起诉日期")
    private Date scqsrq;

    /**
     * 身份
     */
    @ApiModelProperty(value = "身份")
    private String sf;

    /**
     * 身份核实
     */
    @ApiModelProperty(value = "身份核实")
    private String sfhs;

    /**
     * 身高
     */
    @ApiModelProperty(value = "身高")
    private String sg;

    /**
     * 所内编号
     */
    @ApiModelProperty(value = "所内编号")
    private String snbh;

    /**
     * 诉讼环节
     */
    @ApiModelProperty(value = "诉讼环节")
    private String sshj;

    /**
     * 涉嫌罪名
     */
    @ApiModelProperty(value = "涉嫌罪名")
    private String sxzm;

    /**
     * 送押单位
     */
    @ApiModelProperty(value = "送押单位")
    private String sydw;

    /**
     * 收押凭证
     */
    @ApiModelProperty(value = "收押凭证")
    private String sypz;

    /**
     * 收押凭证文书号
     */
    @ApiModelProperty(value = "收押凭证文书号")
    private String sypzwsh;

    /**
     * 送押人
     */
    @ApiModelProperty(value = "送押人")
    private String syr;

    /**
     * 同案编号
     */
    @ApiModelProperty(value = "同案编号")
    private String tabh;

    /**
     * 体表特殊标记
     */
    @ApiModelProperty(value = "体表特殊标记")
    private String tbtsbj;

    /**
     * 特长(专长)
     */
    @ApiModelProperty(value = "特长(专长)")
    private String tc;

    /**
     * 特殊身份
     */
    @ApiModelProperty(value = "特殊身份")
    private String tssf;

    /**
     * 体重
     */
    @ApiModelProperty(value = "体重")
    private String tz;

    /**
     * 违法犯罪经历
     */
    @ApiModelProperty(value = "违法犯罪经历")
    private String wffzjl;

    /**
     * 文化程度
     */
    @ApiModelProperty(value = "文化程度")
    private String whcd;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String xb;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String xm;

    /**
     * 姓名拼音
     */
    @ApiModelProperty(value = "姓名拼音")
    private String xmpy;

    /**
     * 刑期
     */
    @ApiModelProperty(value = "刑期")
    private String xq;

    /**
     * 刑期截止日期
     */
    @ApiModelProperty(value = "刑期截止日期")
    private Date xqjzrq;

    /**
     * 刑期起始日期
     */
    @ApiModelProperty(value = "刑期起始日期")
    private Date xqqsrq;

    /**
     * 现住址
     */
    @ApiModelProperty(value = "现住址")
    private String xzz;

    /**
     * 现住址详址
     */
    @ApiModelProperty(value = "现住址详址")
    private String xzzxz;

    /**
     * 移送法院日期
     */
    @ApiModelProperty(value = "移送法院日期")
    private Date ysfyrq;

    /**
     * 专案
     */
    @ApiModelProperty(value = "专案")
    private String za;

    /**
     * 足长
     */
    @ApiModelProperty(value = "足长")
    private String zc;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    private String zjhm;

    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
    private String zjlx;

    /**
     * 职务
     */
    @ApiModelProperty(value = "职务")
    private String zw;

    /**
     * 职务级别
     */
    @ApiModelProperty(value = "职务级别")
    private String zwjb;

    /**
     * 重刑犯
     */
    @ApiModelProperty(value = "重刑犯")
    private String zxf;

    /**
     * 执行通知书送达日期
     */
    @ApiModelProperty(value = "执行通知书送达日期")
    private Date zxtzssdrq;

    /**
     * 职业
     */
    @ApiModelProperty(value = "职业")
    private String zy;

    /**
     * 最终处置结果
     */
    @ApiModelProperty(value = "最终处置结果")
    private String zzczjg;

    /**
     * 最终处置日期
     */
    @ApiModelProperty(value = "最终处置日期")
    private Date zzczrq;

    /**
     * 政治面貌
     */
    @ApiModelProperty(value = "政治面貌")
    private String zzmm;

    /**
     * 正面照片
     */
    @ApiModelProperty(value = "正面照片")
    private String frontPhoto;

    /**
     * 左侧照片
     */
    @ApiModelProperty(value = "左侧照片")
    private String leftPhoto;

    /**
     * 右侧照片
     */
    @ApiModelProperty(value = "右侧照片")
    private String rightPhoto;

    /**
     * 监室名称
     */
    @ApiModelProperty(value = "监室名称")
    private String roomName;


}
