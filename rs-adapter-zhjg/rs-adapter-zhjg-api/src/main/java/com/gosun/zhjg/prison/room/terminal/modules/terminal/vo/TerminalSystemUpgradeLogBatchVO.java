package com.gosun.zhjg.prison.room.terminal.modules.terminal.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("智能终端管理系统-更新台账查询")
public class TerminalSystemUpgradeLogBatchVO implements TransPojo {

	private String id;
	@ApiModelProperty(value = "终端类型", required = false)
	@Trans(type = TransType.DICTIONARY, key = "ZD_TERMINAL_SYSTEM_TERMINAL_TYPE", ref = "terminalTypeDisplayName")
	private String terminalType;
	private String terminalTypeDisplayName;
	@ApiModelProperty(value = "更新的包类型", required = false)
	@Trans(type = TransType.DICTIONARY, key = "ZD_TERMINAL_SYSTEM_PACKAGE_TYPE", ref = "packageTypeDisplayName")
	private String packageType;
	private String packageTypeDisplayName;
	@ApiModelProperty(value = "更新版本", required = false)
	private String upgradeVersion;
	@ApiModelProperty(value = "更新包名", required = false)
	private String packageName;
	@ApiModelProperty(value = "更新时间", required = false)
	private Date upgradeTime;
	@ApiModelProperty(value = "关联版本id", required = false)
	private String versionManagementId;
	@ApiModelProperty(value = "版本说明", required = false)
	private String releaseNotes;
	@ApiModelProperty(value = "更新情况“50/58”", required = false)
	private String upgradeInfoDisplayName;
}
