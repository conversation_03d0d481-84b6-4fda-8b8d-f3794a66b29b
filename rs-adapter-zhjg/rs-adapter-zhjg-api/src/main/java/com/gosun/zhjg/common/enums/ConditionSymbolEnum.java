package com.gosun.zhjg.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;

public enum ConditionSymbolEnum {
    gt(">", "大于"),
    lt("<", "小于"),
    ge(">=", "大于等于"),
    le("<=", "小于等于"),
    eq("==", "等于"),
    ne("!=", "不等于"),
    ;

    private String value;
    private String desc;

    ConditionSymbolEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    @JsonValue
    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value) {
        for (ConditionSymbolEnum e : ConditionSymbolEnum.values()) {
            if (e.value.equals(value)) {
                return e.desc;
            }
        }
        return null;
    }
}
