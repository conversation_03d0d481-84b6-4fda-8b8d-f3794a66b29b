package com.gosun.zhjg.common.enums.commutation;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * prison_commutation表business_type 业务类型
 * 
 * <AUTHOR>
 *
 */
public enum PrisonCommutationBusinessTypeEnum implements IEnum<String> {

	JX("JX", "减刑"), //
	JS("JS", "假释"), //
	JWZX("JWZX", "监外执行"), //
	;//

	private String value;
	private String desc;

	PrisonCommutationBusinessTypeEnum(String value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(String value) {
		for (PrisonCommutationBusinessTypeEnum e : PrisonCommutationBusinessTypeEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}

	public static String getValue(String desc) {
		for (PrisonCommutationBusinessTypeEnum e : PrisonCommutationBusinessTypeEnum.values()) {
			if (e.getDesc().equals(desc)) {
				return e.value;
			}
		}
		return null;
	}

}