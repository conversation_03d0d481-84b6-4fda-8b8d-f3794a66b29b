package com.gosun.zhjg.common.util;

import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Map;

@Slf4j
public class ParamUtils {
    public static String buildUrl(String baseUrl, Map<String, String> params) {
        StringBuilder url = new StringBuilder(baseUrl);

        if(params != null && !params.isEmpty()) {
            url.append("?");

            for(Map.Entry<String, String> entry : params.entrySet()) {
                try {
                    url.append(entry.getKey())
                            .append("=")
                            .append(URLEncoder.encode(entry.getValue(), "UTF-8"))
                            .append("&");
                } catch (UnsupportedEncodingException e) {
                   log.error("",e);
                }
            }

            url.deleteCharAt(url.length() - 1); // 删除最后一个"&"
        }
        return url.toString();
    }

}
