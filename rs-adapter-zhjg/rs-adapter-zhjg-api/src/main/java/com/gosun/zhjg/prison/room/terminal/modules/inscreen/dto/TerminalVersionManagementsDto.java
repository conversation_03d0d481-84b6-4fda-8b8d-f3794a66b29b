package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 终端版本管理
 *
 * <AUTHOR>
 * @date 2023/8/18 17:22
 */
@Data
@ApiModel("终端版本管理新增Dto")
public class TerminalVersionManagementsDto implements TransPojo {

    /**
     * $column.comments
     */
    @NotNull(message = "$column.comments 不能为空!")
    @ApiModelProperty(value = "$column.comments", required = true)
    private String id;
    /**
     * 包名
     */
    @NotNull(message = "包名 不能为空!")
    @ApiModelProperty(value = "包名", required = true)
    private String packageName;
    /**
     * 软件包种类，区分包。固定前缀
     */
    @NotNull(message = "软件包种类，区分包。固定前缀 不能为空!")
    @ApiModelProperty(value = "软件包种类，区分包。固定前缀", required = true)
    private String fixed;
    /**
     * 版本号
     */
    @NotNull(message = "版本号 不能为空!")
    @ApiModelProperty(value = "版本号", required = true)
    private String versionNumber;
    /**
     * 地市缩写
     */
    @NotNull(message = "地市缩写 不能为空!")
    @ApiModelProperty(value = "地市缩写", required = true)
    private String cityAbbr;
    /**
     * 系统缩写
     */
    @NotNull(message = "系统缩写 不能为空!")
    @ApiModelProperty(value = "系统缩写", required = true)
    private String sysAbbr;
    /**
     * 机器缩写
     */
    @NotNull(message = "机器缩写 不能为空!")
    @ApiModelProperty(value = "机器缩写", required = true)
    private String machineAbbr;
    /**
     * 兼容版本
     */
    @NotNull(message = "兼容版本 不能为空!")
    @ApiModelProperty(value = "兼容版本", required = true)
    private String competingVersions;
    /**
     * 版本说明
     */
    @NotNull(message = "版本说明 不能为空!")
    @ApiModelProperty(value = "版本说明", required = true)
    private String releaseNotes;
    /**
     * 软件包状态：0缺失、1存在
     */
    @NotNull(message = "软件包状态：0缺失、1存在 不能为空!")
    @ApiModelProperty(value = "软件包状态：0缺失、1存在", required = true)
    private Integer state;

    public String getStateDisplayName() {
        if (Integer.valueOf("0").equals(state)) {
            return "缺失";
        }
        if (Integer.valueOf("1").equals(state)) {
            return "存在";
        }
        return null;
    }

    /**
     * 上传时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "上传时间 不能为空!")
    @ApiModelProperty(value = "上传时间", required = true)
    private Date createTime;
    /**
     * $column.comments
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "$column.comments 不能为空!")
    @ApiModelProperty(value = "$column.comments", required = true)
    private Date updateTime;

    @ApiModelProperty(value = "文件路径", required = true)
    private String packageUrl;

    /**
     * 主版本号
     */
    @JsonIgnore
    private Integer majorVersion;
    /**
     * 次版本号
     */
    @JsonIgnore
    private Integer minorVersion;
    /**
     * 修补版本号
     */
    @JsonIgnore
    private Integer patchVersion;
	/**
	 * 终端类型,字典：TERMINAL_SYSTEM_TERMINAL_TYPE
	 */
    @Trans(type = TransType.DICTIONARY, key = "ZD_TERMINAL_SYSTEM_TERMINAL_TYPE", ref = "terminalTypeDisplayName")
	private String terminalType;
	private String terminalTypeDisplayName;
	/**
	 * 更新的包类型（upgrade_type） apk、web 字典：TERMINAL_SYSTEM_PACKAGE_TYPE
	 */
    @Trans(type = TransType.DICTIONARY, key = "ZD_TERMINAL_SYSTEM_PACKAGE_TYPE", ref = "packageTypeDisplayName")
	private String packageType;
	private String packageTypeDisplayName;
}
