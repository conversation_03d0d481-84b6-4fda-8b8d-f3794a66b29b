package com.gosun.zhjg.common.context;

import lombok.Data;
import lombok.experimental.Accessors;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 *
 */
@Target({ ElementType.TYPE, ElementType.FIELD, ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
public @interface PageSortMapping {

	/**
	 * 属性映射的查询字段
	 *
	 * @return
	 */
	String value() default "";

	/**
	 * 查询字段加前缀
	 *
	 * @return
	 */
	String prefix() default "";

	/**
	 * 标注在方法，忽略该方法被自动扫描。目前仅作用于方法 属性无必要
	 *
	 * @return
	 */
	boolean ignore() default false;

	/**
	 *
	 * <AUTHOR>
	 *
	 */
	@Accessors(chain = true)
	@Data
	public static class PageSort {

		private Map<String, String> cache = new HashMap<String, String>();
		private String className;
		private String methodName;
		// 忽略全局排序
		private Boolean ignore;

		public PageSort() {
		}

		public String getMethodId() {
			return className + "." + methodName;
		}

		public PageSort(String className, String methodName) {
			this.className = className;
			this.methodName = methodName;
		}

		public PageSort(Class<?> clazz, String methodName) {
			this.className = clazz.getName();
			this.methodName = methodName;
		}

		public static PageSort INSTANCE(String className, String methodName) {
			return new PageSort(className, methodName);
		}

		public static PageSort INSTANCE(Class<?> clazz, String methodName) {
			return new PageSort(clazz.getName(), methodName);
		}

		public static PageSort INSTANCE(Class<?> clazz) {
			Method[] methods = clazz.getDeclaredMethods();
			for (Method m : methods) {
				// 方法上存在注解生效
//				if (m.getDeclaredAnnotation(PageSortMapping.class) != null) {
//					return new PageSort(clazz.getName(), m.getName());
//				}
				// 省略注解，包含指定类型参数生效
				Class<?>[] parameterTypes = m.getParameterTypes();
				boolean page = false;
				boolean form = false;
				for (Class<?> argClazz : parameterTypes) {
					if (com.gosun.zhjg.common.entity.AbstractPageQueryForm.class.isAssignableFrom(argClazz)) {
						form = true;
					}
					if (com.baomidou.mybatisplus.core.metadata.IPage.class.isAssignableFrom(argClazz)) {
						page = true;
					}
				}
				if (page && form) {
					// 找到第一个符合直接结束
					return new PageSort(clazz.getName(), m.getName());
				}
			}
			throw new IllegalArgumentException(clazz.getName() + "中未找到@" + PageSortMapping.class.getSimpleName() + "注解方法");
		}

		public PageSort put(String property, String field) {
			cache.put(property, field);
			return this;
		}
	}
}
