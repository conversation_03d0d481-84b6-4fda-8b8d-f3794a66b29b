package com.gosun.zhjg.common.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 *
 */
public class PageUtils {

	/**
	 * 分页
	 *
	 * @param <E>
	 *
	 * @param list
	 * @param page
	 * @param size
	 * @return
	 */
	public static <E> List<E> page(List<E> list, long page, long size) {
		if (list == null) {
			return new ArrayList<>();
		}
		long startIndex = size * (page - 1);
		if (list.size() < startIndex) {
			return new ArrayList<>();
		} else {
			return list.subList((int) startIndex, (int) (size * page > list.size() ? list.size() : startIndex + size));
		}
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <E> Page<E> page(List<E> list, Page page) {
		if (list == null) {
			list = new ArrayList<>();
		}
		List<E> records = page(list, (int) page.getCurrent(), page.getSize());
		page.setRecords(records);
		page.setTotal(list.size());
		return page;

	}
}
