package com.gosun.zhjg.basic.business.modules.prisoner.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 人员业务状态
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-04-08 17:51:09
 */
@Data
@ApiModel("人员业务状态标签VO")
public class BasePrisonerBusStatusTagVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "",hidden = true)
    private String prisonerId;

    @ApiModelProperty("模块标识(模块字典)")
    private String moduleId;

    @ApiModelProperty("模块名称")
    private String moduleName;

    @ApiModelProperty("业务id")
    private String businessId;

    @ApiModelProperty("业务名称")
    private String businessName;

    @ApiModelProperty("全称")
    private String fullName;

    @ApiModelProperty("简称")
    private String shortName;

}
