package com.gosun.zhjg.prison.room.terminal.modules.socket.feign;


import com.gosun.zhjg.common.feign.intercept.response.FeignResponseIntercept;
import com.gosun.zhjg.common.feign.intercept.response.ResultResponseHandle;
import com.gosun.zhjg.common.msg.ResultVO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.CustomPushMessageConditionDTO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.PushMessageAckVO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.SocketRelationMapVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * isbs-common-service 对应的socket接口
 */
@FeignClient(name = "${feign.server.isbs.name:zhjg-prison-room-terminal}", url = "${feign.server.isbs.url:}", path = "/socket")
@FeignResponseIntercept(ResultResponseHandle.class)
public interface SocketPushFeign {

    /**
     * 推送消息给序列号
     */
    @PostMapping("/pushMessageToSerialNumber")
    ResultVO<Void> pushMessageToSerialNumber(@RequestBody @Validated PushMessageForm form);

    /**
     * 推送消息给监室
     */
    @PostMapping("/pushMessageToRoom")
    ResultVO<Void> pushMessageToRoom(@RequestBody @Validated PushMessageForm form);

    /**
     * 推送消息给某监所下所有用户 -平台，不等待响应
     */
    @PostMapping("/pushMessageToPrison")
    ResultVO<Void> pushMessageToPrison(@RequestBody @Valid PushMessageForm message);

    /**
     * 获取当前的socketio连接信息
     */
    @GetMapping("/getSocketInfo")
    ResultVO<SocketRelationMapVO> getSocketInfo();

    /**
     * 推送消息给监室-等待响应
     */
    @PostMapping("/pushMessageToRoomWaitReply")
    ResultVO<List<PushMessageAckVO>> pushMessageToRoomWaitReplyMultiple(@RequestBody @Valid PushMessageForm form);

    /**
     * 推送消息给序列号-等待响应
     */
    @PostMapping("/pushMessageToSerialNumberWaitReply")
    ResultVO<List<PushMessageAckVO>> pushMessageToSerialNumberWaitReplyMultiple(@RequestBody @Valid PushMessageForm form);

    /**
     * 推送消息给某监所下所有用户 -平台，等待响应
     */
    @PostMapping("/pushMessageToPrisonWaitReplyMultiple")
    ResultVO<List<PushMessageAckVO>> pushMessageToPrisonWaitReplyMultiple(@RequestBody @Validated PushMessageForm form);

    /**
     * 推送消息给用户 -平台，等待响应
     */
    @PostMapping("/pushMessageToUser")
    ResultVO<String> pushMessageToUser(@RequestBody @Validated PushMessageForm form);

    /**
     * 推送消息给客户端 -平台，等待响应
     */
    @PostMapping("/pushMessageToClientWaitReply")
    ResultVO<List<PushMessageAckVO>> pushMessageToClientWaitReply(@RequestBody @Validated PushMessageForm form);

    /**
     * 发送消息给客户端不期待回应
     */
    @PostMapping("/pushMessageToClient")
    ResultVO<Void> pushMessageToClient(@RequestBody @Validated PushMessageForm form);

    /**
     * 推送消息给拥有该角色或岗位用户 -平台，不等待响应
     */
    @PostMapping("/pushMessageToRole")
    ResultVO<Void> pushMessageToRole(@RequestBody @Validated PushMessageForm form);

    /**
     * 推送消息给拥有该角色或岗位用户 -平台，等待响应
     */
    @PostMapping("/pushMessageToRoleWaitReplyMultiple")
    ResultVO<List<PushMessageAckVO>> pushMessageToRoleWaitReplyMultiple(@RequestBody @Validated PushMessageForm form);

    /**
     * 按指定条件发送指定消息格式到客户端
     */
    @PostMapping("/push/message/withCondition")
    ResultVO<Void> pushMessageWithCondition(@RequestBody PushMessageForm condition);

    /**
     * 按指定条件发送指定消息格式到客户端-等待响应
     */
    @PostMapping("/push/message/withCondition/waitReply")
    ResultVO<List<PushMessageAckVO>> pushMessageWithConditionWaitReply(@RequestBody PushMessageForm condition);

    /**
     * 按指定条件发送自定义消息格式到客户端
     */
    @PostMapping("/custom/push/message/withCondition")
    ResultVO<Void> customPushMessageWithCondition(@RequestBody CustomPushMessageConditionDTO condition);

    /**
     * 按指定条件发送自定义消息格式到客户端-等待响应
     */
    @PostMapping("/custom/push/message/withCondition/waitReply")
    ResultVO<List<PushMessageAckVO>> customPushMessageWithConditionWaitReply(
            @RequestBody CustomPushMessageConditionDTO condition);

}
