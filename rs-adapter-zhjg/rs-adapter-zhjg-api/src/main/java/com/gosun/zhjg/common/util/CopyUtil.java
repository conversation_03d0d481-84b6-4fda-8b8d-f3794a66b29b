package com.gosun.zhjg.common.util;

import com.alibaba.fastjson.JSON;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
*<AUTHOR>
*@email <EMAIL>
*@date 2022/8/12
*/

public class CopyUtil {
    /**
     * 从List<A>到List<B>
     *
     * @param list
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> List<T> copyList1(List<?> list, Class<T> clazz) {
        return JSON.parseArray(JSON.toJSONString(list), clazz);
    }

    /**
     * 从List<A>到List<B>
     *
     * @param list
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> List<T> copyList2(List<?> list, Class<T> clazz) {
        List<T> result = new ArrayList<>(list.size());
        for (Object source : list) {
            T target;
            try {
                target = clazz.getDeclaredConstructor().newInstance();
            } catch (Exception e) {
                throw new RuntimeException();
            }
            BeanUtils.copyProperties(source, target);
            result.add(target);
        }
        return result;
    }

}
