package com.gosun.zhjg.common.util.sm2;


/**
 * @ Description 国密公私钥对工具类
 */
import com.antherd.smcrypto.sm2.Keypair;
import com.antherd.smcrypto.sm2.Sm2;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.security.*;
import java.security.spec.ECGenParameterSpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class KeyUtils {
    public static final String PUBLIC_KEY = "publicKey";

    public static final String PRIVATE_KEY = "privateKey";

    private static final SmKeyPair smKeyPair = new SmKeyPair();

    public static String privateKeys = "";
    private static String publicKeyStr = "";
    private static String privateKeyStr = "92b704fee9ac448ac27abfa537021ab42d0035151f01fdc03e49b0c2f5496148";

    /**
     * 加密，使用公钥
     *
     * @param publicKey
     * @param originalText
     * @return
     */
    public static String encryptText(String publicKey, String originalText) throws Exception {
        if (StringUtil.isNullOrEmpty(publicKey)) {
            throw new Exception("密钥不能为空...");
        }
        if (StringUtil.isNullOrEmpty(originalText)) {
            throw new Exception("明文不能为空...");
        }

        try {
            return Sm2.doEncrypt(originalText, publicKey);//HexUtil.encodeHexStr(cipherText); // 加密结果
        } catch (Exception e) {
            throw new Exception("加密错误：密钥不正确...");
        }
    }

    public static String getPublicKey() {
        return publicKeyStr;
    }

    public static String getPrivateKey() {
        return privateKeyStr;
    }

    public static SmKeyPair smKeyPair() {
        return smKeyPair;
    }

    /**
     * 解密，使用私钥
     *
     * @param privateKey
     * @param cipherText
     * @return
     */
    public static String decryptText(String privateKey, String cipherText) throws Exception {
        if (StringUtil.isNullOrEmpty(privateKey)) {
            throw new Exception("密钥不能为空...");
        }
        if (StringUtil.isNullOrEmpty(cipherText)) {
            throw new Exception("明文不能为空...");
        }
        try {
            String s = Sm2.doDecrypt(cipherText, privateKey);
            System.out.println(s);
            return Sm2.doDecrypt(cipherText, privateKey); // new String(sm2.decrypt(sourceData,prvKey)); // 解密结果
        } catch (Exception e) {
            throw new Exception("解密错误：密钥不正确...");
        }
    }



    /**
     * 生成国密公私钥对
     */
    public static Map<String, String> generateSmKey() throws Exception {
        KeyPairGenerator keyPairGenerator = null;
        SecureRandom secureRandom = new SecureRandom();
        ECGenParameterSpec sm2Spec = new ECGenParameterSpec("sm2p256v1");
        keyPairGenerator = KeyPairGenerator.getInstance("EC", new BouncyCastleProvider());
        keyPairGenerator.initialize(sm2Spec);
        keyPairGenerator.initialize(sm2Spec, secureRandom);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();
        PrivateKey privateKey = keyPair.getPrivate();
        PublicKey publicKey = keyPair.getPublic();
        String publicKeyStr = new String(Base64.getEncoder().encode(publicKey.getEncoded()));
        String privateKeyStr = new String(Base64.getEncoder().encode(privateKey.getEncoded()));
        Map<String, String> map = new HashMap<>();
        map.put(PUBLIC_KEY,publicKeyStr);
        map.put(PRIVATE_KEY,privateKeyStr);
        return map;
    }

    /**
     * 获取sm2密钥对，
     *
     * @return 返回String[]；第0个为公钥，第1个为私钥
     * @throws Exception
     */
    public static String[] generateKeyPair() throws Exception {
        try {
            Keypair keypair = Sm2.generateKeyPairHex();
            String[] result = new String[2];
            if (keypair != null) {
                result[0] = keypair.getPublicKey(); //公钥
                smKeyPair.setPublicKey(keypair.getPublicKey());
                publicKeyStr = keypair.getPublicKey();
                result[1] = keypair.getPrivateKey(); // 私钥
                smKeyPair.setPrivateKey(keypair.getPrivateKey());
                privateKeyStr = keypair.getPrivateKey();
            }
            return result;
        } catch (Exception e) {
            throw new Exception("生成密钥对失败...");
        }
    }



    /**
     * 将Base64转码的公钥串，转化为公钥对象
     */
    public static PublicKey createPublicKey(String publicKey) {
        PublicKey publickey = null;
        try {
            X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(Base64.getDecoder().decode(publicKey));
            KeyFactory keyFactory = KeyFactory.getInstance("EC", new BouncyCastleProvider());
            publickey = keyFactory.generatePublic(publicKeySpec);
        } catch (Exception e) {
            log.error("将Base64转码的公钥串，转化为公钥对象异常：{}", e.getMessage(), e);
        }
        return publickey;
    }

    /**
     * 将Base64转码的私钥串，转化为私钥对象
     */
    public static PrivateKey createPrivateKey(String privateKey) {
        PrivateKey publickey = null;
        try {
            PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKey));
            KeyFactory keyFactory = KeyFactory.getInstance("EC", new BouncyCastleProvider());
            publickey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        } catch (Exception e) {
            log.error("将Base64转码的私钥串，转化为私钥对象异常：{}", e.getMessage(), e);
        }
        return publickey;
    }

    /**
     * RSA密钥对对象
     */
    public static class SmKeyPair {
        private String publicKey;
        private String privateKey;

        public SmKeyPair() {

        }

        public SmKeyPair(String publicKey, String privateKey) {
            this.publicKey = publicKey;
            this.privateKey = privateKey;
        }

        public String getPublicKey() {
            return publicKey;
        }

        public void setPublicKey(String publicKey) {
            this.publicKey = publicKey;
        }

        public String getPrivateKey() {
            return privateKey;
        }

        public void setPrivateKey(String privateKey) {
            this.privateKey = privateKey;
        }
    }


}
