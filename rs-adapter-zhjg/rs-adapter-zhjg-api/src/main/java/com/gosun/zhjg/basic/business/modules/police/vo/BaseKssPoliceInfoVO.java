package com.gosun.zhjg.basic.business.modules.police.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* BaseKssPoliceInfoVO 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2022-12-30
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="BaseKssPoliceInfoVO对象", description="")
public class BaseKssPoliceInfoVO implements Serializable {


    @JsonProperty("prisonId")
    @ApiModelProperty(value = "监所编号")
    private String prisonId;

    @JsonProperty("year")
    @ApiModelProperty(value = "年度")
    private String year;

    @JsonProperty("levelFlag")
    @ApiModelProperty(value = "级别标志")
    private String levelFlag;

    @JsonProperty("name")
    @ApiModelProperty(value = "姓名")
    private String name;

    @JsonProperty("sex")
    @ApiModelProperty(value = "性别")
    private String sex;

    @JsonProperty("sexDisplayName")
    @ApiModelProperty(value = "性别 中文")
    private String sexDisplayName;

    @JsonProperty("birth")
    @ApiModelProperty(value = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    private Date birth;

    @JsonProperty("cultureLevel")
    @ApiModelProperty(value = "文化程度")
    private String cultureLevel;

    @JsonProperty("policeNum")
    @ApiModelProperty(value = "警号")
    private String policeNum;

    @JsonProperty("idNum")
    @ApiModelProperty(value = "身份证号")
    private String idNum;

    @JsonProperty("major")
    @ApiModelProperty(value = "专业")
    private String major;

    @JsonProperty("majorPost")
    @ApiModelProperty(value = "专业职称")
    private String majorPost;

    @JsonProperty("politicsStatus")
    @ApiModelProperty(value = "政治面貌")
    private String politicsStatus;

    @JsonProperty("workDate")
    @ApiModelProperty(value = "参加工作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    private Date workDate;

    @JsonProperty("workPoliceDate")
    @ApiModelProperty(value = "参加公安工作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    private Date workPoliceDate;

    @JsonProperty("workPrisonDate")
    @ApiModelProperty(value = "参加监管工作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    private Date workPrisonDate;

    @JsonProperty("phoneNumber")
    @ApiModelProperty(value = "联系电话")
    private String phoneNumber;

    @JsonProperty("id")
    @ApiModelProperty(value = "主键")
    private String id;

    @JsonProperty("recordPerson")
    @ApiModelProperty(value = "录入人")
    private String recordPerson;

    @JsonProperty("recordDate")
    @ApiModelProperty(value = "录入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    private Date recordDate;

    @JsonProperty("auditPerson")
    @ApiModelProperty(value = "审批人")
    private String auditPerson;

    @JsonProperty("auditDate")
    @ApiModelProperty(value = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    private Date auditDate;

    @JsonProperty("status")
    @ApiModelProperty(value = "状态")
    private Integer status;

    @JsonProperty("statusDisplayName")
    @ApiModelProperty(value = "状态 中文")
    private String statusDisplayName;

    @JsonProperty("post")
    @ApiModelProperty(value = "岗位")
    private String post;

    @JsonProperty("photo")
    @ApiModelProperty(value = "相片")
    private String photo;

    @JsonProperty("sort")
    @ApiModelProperty(value = "排序")
    private String sort;

    @JsonProperty("branchPost")
    @ApiModelProperty(value = "总队支队职务")
    private String branchPost;

    @JsonProperty("branchName")
    @ApiModelProperty(value = "机构名称")
    private String branchName;

    @JsonProperty("prisonType")
    @ApiModelProperty(value = "监所类型")
    private String prisonType;

    @JsonProperty("jg")
    @ApiModelProperty(value = "籍贯")
    private String jg;

    @JsonProperty("mz")
    @ApiModelProperty(value = "民族")
    private String mz;

    @JsonProperty("rtrq")
    @ApiModelProperty(value = "入团日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    private Date rtrq;

    @JsonProperty("rdrq")
    @ApiModelProperty(value = "入党日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    private Date rdrq;

    @JsonProperty("duty")
    @ApiModelProperty(value = "职务")
    private String duty;

    @JsonProperty("jobRank")
    @ApiModelProperty(value = "职务级别")
    private String jobRank;

    @JsonProperty("policeRank")
    @ApiModelProperty(value = "警衔")
    private String policeRank;

    @JsonProperty("outDate")
    @ApiModelProperty(value = "离职时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    private Date outDate;

    @JsonProperty("mobile")
    @ApiModelProperty(value = "手机号")
    private String mobile;

    @JsonProperty("email")
    @ApiModelProperty(value = "EMAIL地址(如果忘记密码，可以返回新密码)")
    private String email;

}
