package com.gosun.zhjg.basic.business.modules.menu.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 *
 */
@Data
@ApiModel("${comments}VO")
public class BaseMenuVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String id;
	/**
	 * 菜单或功能名称
	 */
	@ApiModelProperty(value = "菜单或功能名称")
	private String menuName;
	/**
	 * 编码
	 */
	@ApiModelProperty(value = "编码")
	private String code;
	/**
	 * 父级节点ID
	 */
	@ApiModelProperty(value = "父级节点ID")
	private String parentId;
	private String parentMenuName;
	/**
	 * 节点全路径逗号隔开
	 */
	@ApiModelProperty(value = "节点全路径逗号隔开")
	private String parentNodes;
	/**
	 * 菜单地址
	 */
	@ApiModelProperty(value = "菜单地址")
	private String url;
	/**
	 * 带单类型-菜单menu / 功能func
	 */
	@ApiModelProperty(value = "带单类型-菜单menu /  功能func")
	private String menuType;
	private String menuTypeDisplayName;
	/**
	 * 排序大靠前，默认100
	 */
	@ApiModelProperty(value = "排序大靠前，默认100")
	private Integer sort;
	/**
	 * 图标
	 */
	@ApiModelProperty(value = "图标")
	private String icon;
	/**
	 * 打开方式 inner / external
	 */
	@ApiModelProperty(value = "打开方式	inner / external")
	private String openMode;
	private String openModeDisplayName;
	/**
	 * 是否启用 1 / 0
	 */
	@ApiModelProperty(value = "统计接口地址")
	private String countUrl;
	/**
	 * 统计接口地址
	 */
	@ApiModelProperty(value = "是否启用 1 / 0")
	private Integer enable;
//	/**
//	 * $column.comments
//	 */
//	@ApiModelProperty(value = "$column.comments")
//	private Date createTime;
//	/**
//	 * $column.comments
//	 */
//	@ApiModelProperty(value = "$column.comments")
//	private String createUserId;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String createUserName;
//	/**
//	 * $column.comments
//	 */
//	@ApiModelProperty(value = "$column.comments")
//	private Date updateTime;
//	/**
//	 * $column.comments
//	 */
//	@ApiModelProperty(value = "$column.comments")
//	private String updateUserId;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String updateUserName;


	@ApiModelProperty("备注")
	private String remark;

	@ApiModelProperty("别称")
	private String alias;

	@ApiModelProperty("菜单分类，业务分组.字典：BASE_MENU_CLASSIFICATION")
	private String classification;
	private String classificationDisplayName;
}
