package com.gosun.zhjg.common.context;

import com.gosun.zhjg.auth.common.util.jwt.IJWTInfo;
import com.gosun.zhjg.common.config.UserContext;
import com.gosun.zhjg.common.constant.CommonConstants;
import com.gosun.zhjg.common.constant.RoleConstants;
import com.gosun.zhjg.common.controller.BaseController;
import com.gosun.zhjg.common.util.ReflectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Aspect//表面该类是一个切面
@EnableAspectJAutoProxy//开启切面管理
@Slf4j//日志打印
@Component//将该切面交给IOC管理
public class IsEditAopAspect extends BaseController {
    /**
     * 这里我们使用注解的形式
     * 当然，我们也可以通过切点表达式直接指定需要拦截的package,需要拦截的class 以及 method
     * 切点表达式:   execution(...)确定作用的方法的范围
     */
    //我们使用的是关联到上面的注解类中，例如：com.test.Annotation
    @Pointcut("@annotation(com.gosun.zhjg.common.context.IsEdit)")
    public void annotationPointcut() {
    }

//    @Before("@annotation(isEdit)")
//    public void beforePointcut(JoinPoint joinPoint, IsEdit isEdit) {
//        // 此处进入到方法前  可以实现一些业务逻辑
//        log.info("beforePointcut2");
//    }


    /**
     * 环绕增强
     *
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Around("annotationPointcut()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        IJWTInfo jwtInfo = UserContext.getJwtInfo();
        String rolepost = jwtInfo.getAttributes().getString(CommonConstants.JWT_ATTR_ROLEPOST);
        String name = joinPoint.getSignature().getName();//获取方法名
        Object[] args = joinPoint.getArgs();//获取参数
        log.info("当前系统时间：" + new Date() + "开始执行的方法" + name + ",方法参数:" + Arrays.toString(args));
        long start = System.currentTimeMillis();
        List proceed = (ArrayList) joinPoint.proceed();//执行目标对象的方法
        proceed.forEach(item -> {
            try {
                Class clazz = item.getClass();             // 获取集合中的对象
                Field[] fields = clazz.getDeclaredFields();
                String endField = null;
                String endValue = null;
                String isEditField = null;
                int isEditValue = 0;
                //拿到台账是否需要判断完成
                for (Field field : fields) {
                    field.setAccessible(true);
                    IsEnd isEnd = field.getDeclaredAnnotation(IsEnd.class);
                    IsEdit isEdit = field.getDeclaredAnnotation(IsEdit.class);
                    if(isEnd!=null){
                        endField = field.getName();
                        endValue = isEnd.value();
                    }
                    if(isEdit!=null){
                        isEditField = field.getName();
                        isEditValue = isEdit.type();
                    }
                }
                //判断完成和是否可编辑
                for (Field field : fields) {
                    field.setAccessible(true);         // 设置字段可访问
                    if (isEditField != null) {
                        Object fieldValue = ReflectionUtils.getFieldValue(item, isEditField);
                        if(endField!=null){
                            Object fieldValue2 = ReflectionUtils.getFieldValue(item, endField);
                            if(endValue.equals(fieldValue2)){
                                if ((StringUtils.isNotBlank(fieldValue.toString()) && fieldValue.toString().equals(isEditValue==0?getUser().getUserid()+"":getUser().getName())) || rolepost.equals(RoleConstants.rolepost_xtgly)) {
                                    ReflectionUtils.setFieldValue(item,"isEdit",1);
                                }else {
                                    //非系统管理员或者本人不能编辑
                                    ReflectionUtils.setFieldValue(item,"isEdit",0);
                                }
                            }else {//状态未结束 不能编辑
                                ReflectionUtils.setFieldValue(item,"isEdit",0);
                            }
                        }else {//没有流程状态的台账
                            //非系统管理员或者本人不能编辑
                            if ((StringUtils.isNotBlank(fieldValue.toString()) && fieldValue.toString().equals(isEditValue==0?getUser().getUserid()+"":getUser().getName())) || rolepost.equals(RoleConstants.rolepost_xtgly)) {
                                ReflectionUtils.setFieldValue(item,"isEdit",1);
                            }else {//状态未结束 不能编辑
                                ReflectionUtils.setFieldValue(item,"isEdit",0);
                            }
                        }

                    }

                }
            } catch (Exception e) {
               log.info(e.toString());
            }
        });
        long end = System.currentTimeMillis();
        log.info("结束执行方法：" + name + ",方法执行时间：" + (end - start) + "毫秒");
        return proceed;

    }

}
