package com.gosun.zhjg.prison.room.terminal.modules.terminal.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 智能终端管理系统-设备列表VO
 *
 * <AUTHOR>
 * @date 2024/2/27 14:07
 */
@ApiModel("智能终端管理系统-设备列表VO")
@Data
public class TerminalSystemDeviceVO {

	private String prisonId;

	/**
	 * 拼接的虚拟id
	 */
	private String virtualId;

	public String getVirtualId() {
		if (virtualId != null) {
			return virtualId;
		}
		return terminalType + "_" + linkId;
	}

	@ApiModelProperty(value = "acp_pm_device_inscreen 表id、base_device 表id")
	private String linkId;
	@ApiModelProperty(value = "终端类型 0008 仓内屏、0015 仓外屏、0023 提人交接终端。字典：TERMINAL_SYSTEM_TERMINAL_TYPE")
	private String terminalType;
	private String deviceName;
	private String serialNumber;
	private String areaName;
	private String deviceIp;
	private String apkVersion;
	private String webVersion;
	/**
	 * apk更新时间
	 */
	private Date apkUpgradeTime;
	/**
	 * web更新时间
	 */
	private Date webUpgradeTime;
	/**
	 * 在线状态 1在线，0离线
	 */
	private Integer onlineState;
	private String onlineStateDisplayName;
	/**
	 * 终端配置页面地址
	 */
	private String terminalServicePage;

	public String getOnlineStateDisplayName() {
		if (onlineStateDisplayName != null && !onlineStateDisplayName.isEmpty()) {
			return onlineStateDisplayName;
		}
		if (onlineState == null) {
			return null;
		}
		switch (onlineState) {
			case 1:
				return "在线";
			case 0:
				return "离线";
			default:
				return onlineState + "";
		}
	}

	/**
	 * 最新更新时间
	 */
	private Date upgradeTime;

	/**
	 * apkUpgradeTime 和 webUpgradeTime 谁大取谁
	 *
	 * @return
	 */
	public Date getUpgradeTime() {
		if (upgradeTime != null) {
			return upgradeTime;
		}
		Date t = null;
		if (apkUpgradeTime != null) {
			t = apkUpgradeTime;
		}
		if (webUpgradeTime != null) {
			if (t != null && t.compareTo(webUpgradeTime) > 0) {
				return t;
			} else {
				t = webUpgradeTime;
			}
		}
		return t;
	}

}
