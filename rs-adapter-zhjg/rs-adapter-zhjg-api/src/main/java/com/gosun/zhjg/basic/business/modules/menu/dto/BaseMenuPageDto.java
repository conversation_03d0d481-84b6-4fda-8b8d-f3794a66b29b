package com.gosun.zhjg.basic.business.modules.menu.dto;

import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 *
 */
@Data
@ApiModel("${comments}分页Dto")
public class BaseMenuPageDto extends AbstractPageQueryForm {

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments", required = true)
	private String id;
	/**
	 * 菜单或功能名称
	 */
	@ApiModelProperty(value = "菜单或功能名称", required = true)
	private String menuName;
	/**
	 * 编码
	 */
	@ApiModelProperty(value = "编码", required = true)
	private String code;
	/**
	 * 父级节点ID
	 */
	@ApiModelProperty(value = "父级节点ID", required = true)
	private String parentId;
	/**
	 * 父级节点ID
	 */
	@ApiModelProperty(value = "父级节点ID.关联另外一个表", required = true)
	private String groupId;
	/**
	 * 节点全路径逗号隔开
	 */
	@ApiModelProperty(value = "节点全路径逗号隔开", required = true)
	private String parentNodes;
	/**
	 * 菜单地址
	 */
	@ApiModelProperty(value = "菜单地址", required = true)
	private String url;
	/**
	 * 带单类型-菜单menu / 功能func
	 */
	@ApiModelProperty(value = "带单类型-菜单menu /  功能func", required = true)
	private String menuType;
	/**
	 * 排序大靠前，默认100
	 */
	@ApiModelProperty(value = "排序大靠前，默认100", required = true)
	private Integer sort;
	/**
	 * 图标
	 */
	@ApiModelProperty(value = "图标", required = true)
	private String icon;
	/**
	 * 打开方式 inner / external
	 */
	@ApiModelProperty(value = "打开方式	inner / external", required = true)
	private String openMode;
	/**
	 * 是否启用 1 / 0
	 */
	@ApiModelProperty(value = "统计接口地址", required = true)
	private String countUrl;
	/**
	 * 统计接口地址
	 */
	@ApiModelProperty(value = "是否启用 1 / 0", required = true)
	private Integer enable;
	/**
	 * 角色ID
	 */
	@ApiModelProperty(value = "查询角色拥有菜单", required = false)
	private Integer roleId;
	@ApiModelProperty(value = "查询角色拥有大菜单", required = false)
	private Integer roleId2;

	@ApiModelProperty(value = "是否分页 1 / 0")
	private Boolean page;
}
