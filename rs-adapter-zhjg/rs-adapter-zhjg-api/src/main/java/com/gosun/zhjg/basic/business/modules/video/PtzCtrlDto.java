package com.gosun.zhjg.basic.business.modules.video;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2020/12/18 09:06
 */
@Data
@ApiModel("云台控制dto")
public class PtzCtrlDto {

    @ApiModelProperty(value = "通道ID")
    String channelId;

    @ApiModelProperty(value = "云台控制类型值")
    String operType;

    @ApiModelProperty(value = "操作内容 1:表示动作开始 0:表示动作结束")
    String operContent;

    @ApiModelProperty(value = "如该动作带有相关参数(比如速度,预置点的索引等)，则表示该参数值；否则置 0")
    String extParam;

    @ApiModelProperty(value = "3D缩放操作时，该字段可使用，如，\"277:397:135:201:31:18\",其中，277为屏幕宽度，397为屏幕高度，135为选择矩形区域中点横坐标，201为选择矩形区域中心纵坐标，31为选择矩形区域宽度，18为选择矩形区域高度")
    String name;

}
