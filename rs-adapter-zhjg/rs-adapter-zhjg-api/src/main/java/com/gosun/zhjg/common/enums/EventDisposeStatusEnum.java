package com.gosun.zhjg.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 所情管理岗位处置状态
 * prison_event 表
 * 管教处理状态 warder_dispose_status 、 医生处理状态 doctor_dispose_status
 *
 * <AUTHOR>
 * @date 2024/10/16 14:40
 */
public enum EventDisposeStatusEnum {
	PENDING("0", "未办结"),
	RESOLVED("1", "已办结"),
	AUDIT_REJECT("2", "不通过"),
	AUDIT_PASS("3", "通过"),
	;

	private String value;
	private String desc;

	EventDisposeStatusEnum(String value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public String getValue() {
		return value;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(String value) {
		for (EventDisposeStatusEnum e : EventDisposeStatusEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}
}
