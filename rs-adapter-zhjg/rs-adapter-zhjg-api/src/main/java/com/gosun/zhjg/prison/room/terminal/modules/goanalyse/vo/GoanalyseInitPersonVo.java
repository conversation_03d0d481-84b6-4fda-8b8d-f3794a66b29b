package com.gosun.zhjg.prison.room.terminal.modules.goanalyse.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GoanalyseInitPersonVo {

    @ApiModelProperty("人员编号")
    private String faceCode;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("照片数据")
    private String photo;

    @ApiModelProperty("仓内屏补录照片")
    private String cnpPhoto;
}
