package com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo;

import com.gosun.zhjg.common.context.PageSortMapping;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


@Data
public class CnpFaceImportStateVO {
    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 仓内屏设备名称
     */
    private String deviceName;

    private String name;

    private String personnelType;
    private String personnelTypeDisplayName;

    @ApiModelProperty("警号/工号/人员编号")
    private String code;

    /**
     * 导入状态 1成功，0失败
     */
    private Integer importState;

    public String getImportStateDisplayName() {
        if (importState == null || "".equals(importState)) {
            //  待导入
            return null;
        }
        if (importState == 1) {
            return "成功";
        }
        if (importState == 0) {
            return "失败";
        }
        return importState + "";
    }

    /**
     * 最后操作时间
     */
    @ApiModelProperty("导入时间")
    private Date updateTime;

    /**
     * 失败描述
     */
    private String msg;

    /**
     * 设备类型 1仓内屏，2仓外屏
     */
    private Integer deviceType;

    @ApiModelProperty("在押人员人员编号、民警policeId")
    private String personnelId;

    private String roomId;

    @PageSortMapping("room_id")
    private String roomName;
}
