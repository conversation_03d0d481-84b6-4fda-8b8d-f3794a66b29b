package com.gosun.zhjg.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 所情管理岗位处置是否是历史数据
 * prison_event_handle 表
 *
 */
public enum EventHandleHistoryEnum {
	NO(0, "否"),
	YES(1, "是"),
	;

	private Integer value;
	private String desc;

	EventHandleHistoryEnum(Integer value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public Integer getValue() {
		return value;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(Integer value) {
		for (EventHandleHistoryEnum e : EventHandleHistoryEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}
}
