package com.gosun.zhjg.basic.business.modules.menu.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 菜单图标
 *
 * <AUTHOR>
 *
 */
@Data
public class BaseMenuIconVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String id;
	/**
	 * 地址
	 */
	@ApiModelProperty(value = "地址")
	private String path;

	private String iconTag;
}
