package com.gosun.zhjg.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 代办-业务编码：区分业务来源
 * sacp_assign 表
 *
 * <AUTHOR>
 * @date 2025/2/21 8:51
 */
public enum SacpAssignBusinessCodeEnum {

//	RISK_ASSESSMENT_PRISONER_ENTRY("RiskAssessmentPrisonerEntry", "新入所风险评估"),
	XRSFXPG("XRSFXPG", "新入所风险评估"),
	GDQMFXPG("GDQMFXPG", "过渡期满风险评估"),
	ZDRYGZFXPG("ZDRYGZFXPG", "重点人员关注风险评估"),
	WGDJFXPG("WGDJFXPG", "违规登记风险评估"),
	XJSYFXPG("XJSYFXPG", "械具使用风险评估"),
	ZBHDJFXPG("ZBHDJFXPG", "重病号登记风险评估"),
	MY2CPGRY("MY2CPGRY", "每月2次评估任务"),
	ROOM_CHANGE_TALK("ROOM_CHANGE_TALK", "监室调整谈话"),
	RISK_ASSESSMENT_TALK("RISK_ASSESSMENT_TALK", "风险评估谈话"),
	PUNISHMENT_TOOL_USE_TALK("PUNISHMENT_TOOL_USE_TALK", "械具使用谈话"),
	PRISON_UNDERCOVER_TALK("PRISON_UNDERCOVER_TALK", "耳目布建谈话"),
	FYJSDT_ZXB("FYJSDT_ZXB", "反映监视动态-主协办民警"),
	FYJSDT_SLD("FYJSDT_SLD", "反映监视动态所领导");

	@Getter
	private final String value;
	private final String desc;

	SacpAssignBusinessCodeEnum(String value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(String value) {
		for (SacpAssignBusinessCodeEnum e : SacpAssignBusinessCodeEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}
}
