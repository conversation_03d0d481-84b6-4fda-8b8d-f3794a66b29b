package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;


public enum ServicesEnum implements IEnum<String> {

	AUTH("zhjg-auth", "鉴权服务"),
	BASIC("zhjg-basic-business", "基础服务"),
	ZUUL("zhjg-zuul", "网关服务"),
	TERMINAL("zhjg-prison-room-terminal","监室事务相关服务"),
	BRANCH("zhjg-branch-work","支队服务"),

	WORK("zhjg-prison-work", "民警业务相关服务"),
	APP("zhjg-prison-app", "移动警务APP服务"),
	MANAGER("zhjg-manager","数据资源平台服务"),
	RECEPTION("zhjg-data-reception","数据下发接收服务"),

	DEVICE("zhjg-device-server", "设备对接服务"),
	QUANTITY("zhjg-data-quantity", "数据监测服务"),
	PLATFORM("zhjg-platform-access","广信安相关对接服务"),
	CONFIG("config-server","配置中心服务");

	private String value;
	private String desc;

	ServicesEnum(String value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(String value) {
		for (ServicesEnum e : ServicesEnum.values()) {
			if (value != null && value.equals(e.value + "")) {
				return e.desc;
			}
		}
		return null;
	}

	public static String getValue(String desc) {
		for (ServicesEnum e : ServicesEnum.values()) {
			if (e.getDesc().equals(desc)) {
				return e.value;
			}
		}
		return null;
	}

}