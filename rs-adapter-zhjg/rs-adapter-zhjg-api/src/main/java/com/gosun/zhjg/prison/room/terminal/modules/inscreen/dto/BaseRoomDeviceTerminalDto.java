package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto;

import com.gosun.zhjg.common.enums.DeviceTerminalTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("监室仓内外屏配置")
public class BaseRoomDeviceTerminalDto {
    @ApiModelProperty("监室id")
    private String roomId;


    @ApiModelProperty("设备类型 1仓内屏，2仓外屏")
    private Integer deviceType;


    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("原配置设备id")
    private String oldDeviceId;


}
