package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class CnpFacePageDto extends AbstractPageQueryForm {

    private String prisonId;
    
    @ApiModelProperty("人员类型 民警: police_1, 辅警: police_0, 其他: police_-1, 被监管人员: prisoner")
    private String personnelType;
    @JSONField(serialize = false)
    private Integer ispolice;
    /**
     * 用戶id
     */
    private String userid;

    @ApiModelProperty("姓名-模糊查询")
    private String name;

    @ApiModelProperty("警号/工号/人员编号")
    private String code;

    @ApiModelProperty("人员编号-精确查询")
    private String rybh;

    @ApiModelProperty("监室号id")
    private String roomId;

    @ApiModelProperty("导入状态 1成功，0失败")
    private Integer importState;

    @ApiModelProperty("设备类型 1仓内屏，2仓外屏")
    private Integer deviceType;

    private String serialNumber;

    private List<String> personnelIdList;
}
