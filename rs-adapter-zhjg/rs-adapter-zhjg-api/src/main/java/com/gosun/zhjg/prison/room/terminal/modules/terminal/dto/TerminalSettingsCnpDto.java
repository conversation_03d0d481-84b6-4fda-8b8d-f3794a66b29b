package com.gosun.zhjg.prison.room.terminal.modules.terminal.dto;

import com.gosun.zhjg.prison.room.terminal.modules.home.dto.BaseSystemRuleConfigKeyValueDto;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalMenuVO;
import lombok.Data;

import java.util.List;

/**
 * 终端配置-仓内屏配置
 *
 * <AUTHOR>
 * @date 2024/12/13 10:23
 */
@Data
public class TerminalSettingsCnpDto {
	private String prisonId;
	private List<TerminalMenuVO> prisonerMenuTreeList;
	private List<TerminalMenuVO> policeMenuTreeList;
	private List<BaseSystemRuleConfigKeyValueDto> ruleConfigList;
}
