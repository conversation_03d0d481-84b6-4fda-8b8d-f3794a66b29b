package com.gosun.zhjg.auth.common.constatns;

/**
 * Created by ace on 2017/8/29.
 */
public class CommonConstants {
    public final static String APP_CODE = "combatplatform";
    public final static String RESOURCE_TYPE_MENU = "menu";
    public final static String RESOURCE_TYPE_BTN = "button";
    public static final Integer EX_TOKEN_ERROR_CODE = 40101;
    // 用户token异常
    public static final Integer EX_USER_INVALID_CODE = 40102;
    // 客户端token异常
    public static final Integer EX_CLIENT_INVALID_CODE = 40131;
    public static final Integer EX_CLIENT_FORBIDDEN_CODE = 40331;
    public static final Integer EX_OTHER_CODE = 500;
    //    public static final String CONTEXT_KEY_USER_ID = "currentUserId";
//    public static final String CONTEXT_KEY_USERNAME = "currentUserName";
//    public static final String CONTEXT_KEY_USER_NAME = "currentUser";
//    public static final String CONTEXT_KEY_USER_TOKEN = "currentUserToken";
    public static final String JWT_KEY_USERID = "userid";
    public static final String JWT_KEY_NAME = "name";
    public static final String JWT_KEY_POLICE_CODE = "policeCode";
    public static final String JWT_KEY_POLICE_ID = "policeId";
    public static final String JWT_KEY_POLICE_NAME = "policeName";
    public static final String JWT_KEY_SQUADRON_ID = "squadronId";
    public static final String JWT_KEY_SQUADRON_NAME = "squadronName";
    public static final String JWT_KEY_PRISON_ID = "prisonId";
    public static final String JWT_KEY_PRISON_NAME = "prisonName";
    public static final String JWT_KEY_USERNAME = "username";
    public static final String JWT_KEY_ROLEID = "roleId";
    public static final String JWT_KEY_ROLENAME = "roleName";
    public static final String JWT_KEY_ROLE_LIST = "roleList";
    public static final String JWT_KEY_PRISON_LIST = "prisonList";
    public static final String JWT_KEY_IS_ZD = "isZd";
    public static final String PHOTO = "photo";
    public static final String JWT_KEY_LOGIN_TYPE = "loginType";
    public static final String JWT_KEY_PERSONNEL_TYPE = "personnelType";
    public static final String JWT_KEY_ATTRIBUTES = "attributes";
    public static final String JWT_KEY_ROLEPOST = "rolepost";
    public static final String JWT_KEY_PRISON_TYPE = "prisonType";
    public static final String JWT_KEY_PRISONER_ID = "prisonerId";
    public static final String JWT_KEY_PRISONER_NAME = "prisonerName";
    public static final String JWT_KEY_IDCARD = "idcard";
//    public static final String JWT_KEY_AUTH_CODE_LIST = "auth_code_list";
//    public static final String JWT_KEY_PRISON_ID="prisonId";
//    public static final String JWT_KEY_PRISON_NAME="prisonName";
//    public static final String JWT_KEY_POLICE_ID="policeId";
//    public static final String JWT_KEY_POLICE_NAME="policeName";
//    public static final String JWT_KEY_POLICE_CODE="policeCode";

    public static final String REDIS_KEY_TOKEN_PREFIX = "token:";

    public static final String POLICE="police";

    public static final String PRISONER="prisoner";
}
