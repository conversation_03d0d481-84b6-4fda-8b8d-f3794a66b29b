package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 事由、业务类型。字典：PRISONER_OUT_IN_REASON
 * ！！ 同步维护字典
 *
 * <AUTHOR>
 * @date 2025/3/21 9:46
 */
public enum CwpPrisonerOutInReasonCodeEnum implements IEnum<String> {
	SWJY("SWJY", "所外就医"),
	TX("TX", "提讯"),
	TJ("TJ", "提解"),
	LSHJ("LSHJ", "律师会见"),
	JSHJ("JSHJ", "家属会见"),
	JSTZ("JSTZ", "监室调整"),
	LSWSHJ("LSWSHJ", "领事外事会见"),
	// "其他"放最后一行
	QT("QT", "其他"),
	;

	@Getter
	private final String value;
	private final String desc;

	CwpPrisonerOutInReasonCodeEnum(String value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(String value) {
		for (CwpPrisonerOutInReasonCodeEnum e : CwpPrisonerOutInReasonCodeEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}

	public static String getValue(String desc) {
		for (CwpPrisonerOutInReasonCodeEnum e : CwpPrisonerOutInReasonCodeEnum.values()) {
			if (e.desc.equals(desc)) {
				return e.value;
			}
		}
		return null;
	}

	public static CwpPrisonerOutInReasonCodeEnum getEnum(String value) {
		for (CwpPrisonerOutInReasonCodeEnum e : CwpPrisonerOutInReasonCodeEnum.values()) {
			if (e.value.equals(value)) {
				return e;
			}
		}
		return null;
	}
}