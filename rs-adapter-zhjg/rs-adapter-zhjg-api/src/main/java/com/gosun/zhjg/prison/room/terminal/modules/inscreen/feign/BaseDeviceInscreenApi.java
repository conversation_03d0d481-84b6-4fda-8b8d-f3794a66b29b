package com.gosun.zhjg.prison.room.terminal.modules.inscreen.feign;

import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.BaseDeviceTerminalPageDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.BaseDeviceTerminalVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(value = "zhjg-prison-room-terminal", path = "inscreen/basedeviceinscreen", contextId = "inscreen/basedeviceinscreen")
public interface BaseDeviceInscreenApi {


    @PostMapping("getRoomDeviceListByPost")
    @ApiOperation(value = "获取监室终端设备列表",response = BaseDeviceTerminalVo.class)
    public R getRoomDeviceListByPost(@RequestBody BaseDeviceTerminalPageDto dto);
}
