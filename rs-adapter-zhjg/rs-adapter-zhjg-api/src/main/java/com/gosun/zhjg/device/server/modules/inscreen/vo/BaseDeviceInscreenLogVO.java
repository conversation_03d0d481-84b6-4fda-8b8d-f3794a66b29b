package com.gosun.zhjg.device.server.modules.inscreen.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 仓内屏人脸录入失败日志记录表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-05-26 09:01:45
 */
@Data
@ApiModel("仓内屏人脸录入失败日志记录表VO")
public class BaseDeviceInscreenLogVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 仓内屏IP
     */
    @ApiModelProperty(value = "仓内屏IP")
    private String ip;
    /**
     * 监室id
     */
    @ApiModelProperty(value = "监室id")
    private String roomId;
    /**
     * 人员编号
     */
    @ApiModelProperty(value = "人员编号")
    private String rybh;
    /**
     * 人员类型
     */
    @ApiModelProperty(value = "人员类型")
    private String type;
    /**
     * 日志记录时间
     */
    @ApiModelProperty(value = "日志记录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date logTime;
    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因")
    private String reason;
    /**
     * 监室名称
     */
    @ApiModelProperty(value = "监室名称")
    private String roomName;
    /**
     * 人员姓名
     */
    @ApiModelProperty(value = "人员姓名")
    private String personName;
    /**
     * 录入失败的照片地址
     */
    @ApiModelProperty(value = "录入失败的照片地址")
    private String imgUrl;

}
