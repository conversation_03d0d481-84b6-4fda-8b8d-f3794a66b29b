package com.gosun.zhjg.basic.business.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * 用户对应其他菜单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-09-16 15:37:51
 */
@Data
@ApiModel("用户对应其他菜单表更新Dto")
public class PermissionUserMenuUpdateDto {

    /**
     * 主键
     */
    @NotNull(message = "主键 不能为空!")
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 用户主键
     */
    @NotNull(message = "用户主键 不能为空!")
    @ApiModelProperty(value = "用户主键")
    private String userid;
    /**
     * 菜单编号
     */
    @NotNull(message = "菜单编号 不能为空!")
    @ApiModelProperty(value = "菜单编号")
    private String menuCode;

}
