package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

public enum VisitorTypeEnum implements IEnum<String> {

    BAMJ("0", "办案民警"),
    LS("1", "律师"),
    JS("2", "家属"),
    QT("3", "其他");
    private String value;
    private String desc;

    VisitorTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @JsonValue
    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value) {
        for (VisitorTypeEnum e : VisitorTypeEnum.values()) {
            if (e.value.equals(value)) {
                return e.desc;
            }
        }
        return null;
    }

    public static String getValue(String desc) {
        for (VisitorTypeEnum e : VisitorTypeEnum.values()) {
            if (e.getDesc().equals(desc)) {
                return e.value;
            }
        }
        return null;
    }

}
