package com.gosun.zhjg.common.enums.commutation;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * prison_commutation表node 监外执行的节点
 * 
 * <AUTHOR>
 *
 */
public enum PrisonCommutationNode_JWZX_Enum implements IEnum<String> {

	JY("JY", "暂予监外执行建议阶段"), //
	KSSSY("KSSSY", "看守所审议阶段"), //
	YWJD("YWJD", "医务鉴定阶段"), //
	SCQDBZR("SCQDBZR", "审查确定保证人阶段"), //
	ZSJCSSC("ZSJCSSC", "驻所检察室审查阶段"), //
	GS("GS", "公示阶段"), //
	SBSP("SBSP", "上报审批阶段"), //
	BLCSSX("BLCSSX", "办理出所手续阶段"), //
	JFZX("JFZX", "交付执行阶段"), //
	DONE("DONE", "已完成"), //
	;

	private String value;
	private String desc;

	PrisonCommutationNode_JWZX_Enum(String value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(String value) {
		for (PrisonCommutationNode_JWZX_Enum e : PrisonCommutationNode_JWZX_Enum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}

	public static String getValue(String desc) {
		for (PrisonCommutationNode_JWZX_Enum e : PrisonCommutationNode_JWZX_Enum.values()) {
			if (e.getDesc().equals(desc)) {
				return e.value;
			}
		}
		return null;
	}
	
	public static Integer getStep(String value) {
		int i = 1;
		for (PrisonCommutationNode_JWZX_Enum e : PrisonCommutationNode_JWZX_Enum.values()) {
			if (e.getValue().equals(value)) {
				return i;
			}
			++i;
		}
		return null;
	}

}