package com.gosun.zhjg.basic.business.modules.prison.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 *
 * 监所关联或解除角色保存DTO
 *
 * com.gosun.zhjg.basic.business.modules.prison.service.impl.BasePrisonInfoServiceImpl.addUsers(BasePrisonInfoAddRolesForm)
 *
 * <AUTHOR>
 *
 */
@Data
public class BasePrisonInfoAddRolesForm {

	@NotEmpty
	private String prisonId;

	@NotEmpty
	@ApiModelProperty("需要关联或解除的角色ID集合")
	private List<String> roleIds;
}
