package com.gosun.zhjg.prison.room.terminal.modules.terminal.feign;

import com.gosun.zhjg.common.enums.TerminalSignatureConfirmBusinessTypeEnum;
import com.gosun.zhjg.common.msg.ResultVO;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dto.TerminalSignatureConfirmQueryDto;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dto.TerminalSignatureConfirmSaveDto;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalSignatureConfirmVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@Component
@FeignClient(value = "zhjg-prison-room-terminal", path = "terminal/terminalsignatureconfirm",contextId = "terminal/terminalsignatureconfirm")
public interface TerminalSignatureConfirmApi {

	@RequestMapping(value = "/sendSignatureTask", method = RequestMethod.POST)
	@ApiOperation(value = "终端签名确认-下发签名任务", responseContainer = "Map")
	ResultVO<?> sendSignatureTask(@RequestBody TerminalSignatureConfirmSaveDto form);

	/**
	 * @param businessType {@link TerminalSignatureConfirmBusinessTypeEnum}
	 * @param linkId       业务id
	 */
	@RequestMapping(value = "/findTerminalSignatureTask", method = RequestMethod.GET)
	@ApiOperation(value = "终端签名确认-根据业务id查询签名信息", responseContainer = "Map")
	ResultVO<List<TerminalSignatureConfirmVO>> findTerminalSignatureTask(@RequestParam("businessType") String businessType, @RequestParam("linkId") String linkId);

	@RequestMapping(value = "/checkHasUnfinishedSignatureTask", method = RequestMethod.GET)
	@ApiOperation(value = "终端签名确认-判断人员是否有签名任务")
	ResultVO<Map<String, Object>> checkHasUnfinishedSignatureTask(@SpringQueryMap TerminalSignatureConfirmQueryDto form);

	/**
	 * 终端签名确认-签名确认
	 */
	@RequestMapping(value = "/signatureConfirm", method = RequestMethod.POST)
	@ApiOperation(value = "终端签名确认-签名确认")
	ResultVO<?> signatureConfirm(@RequestBody TerminalSignatureConfirmSaveDto form);
}
