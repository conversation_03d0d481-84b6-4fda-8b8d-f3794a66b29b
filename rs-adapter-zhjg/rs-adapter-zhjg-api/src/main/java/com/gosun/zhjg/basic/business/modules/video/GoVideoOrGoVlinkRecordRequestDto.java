package com.gosun.zhjg.basic.business.modules.video;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2020/12/17 15:30
 */
@Data
@ApiModel("录像相关服务dto")
public class GoVideoOrGoVlinkRecordRequestDto {

    @ApiModelProperty(value = "录像的通道主键ID（多通道逗号隔开）")
    String channelIds;

    @ApiModelProperty(value = "查询开始时间(例如：2018-01-01 12:00:00)")
    String startTime;

    @ApiModelProperty(value = "查询结束时间(例如：2018-01-01 23:00:00)")
    String endTime;

    @ApiModelProperty(value = "当前页数（从1开始计算）")
    Integer pageStart;

    @ApiModelProperty(value = "返回的记录条数(最大值不超过500)")
    Integer pageNum;

    @ApiModelProperty(value = "是否锁定(0:该锁无效,即不支持锁定;1:没有被锁定;2:被锁定;)")
    Integer locked;

    @ApiModelProperty(value = "录像产生类型 可填选项：time/alarm/manual/all")
    String recordType;

    @ApiModelProperty(value = "0：中心录像；1：前端录像；2:前端网络存储设备；3:级联中心")
    String storageType;

    @ApiModelProperty(value = "返回录像数据模板类型。可选值：goVlink/goVideo;非标准值都以goVlink为准")
    String dataTemplate;
}
