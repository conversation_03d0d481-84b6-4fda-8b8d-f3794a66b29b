package com.gosun.zhjg.basic.business.modules.prison.feign;

import com.gosun.zhjg.basic.business.modules.prison.vo.BasePrisonInfoVO;
import com.gosun.zhjg.common.msg.R;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: panyusheng
 * @Date: 2022/6/1
 * @Version 1.0
 */
@Component
@FeignClient(value = "zhjg-basic-business", path = "base/baseprisoninfo", contextId = "base/baseprisoninfo")
public interface PrisonInfoApi {

    @RequestMapping(value = "/info/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "根据监所编码查询单位信息")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "id", name = "id", value = "单位代码", required = true, dataType = "int")
    })
    R<BasePrisonInfoVO> info(@PathVariable("id") String id);


    @RequestMapping(value = "/prisonType", method = RequestMethod.GET)
    @ApiOperation(value = "获取监所类型")
    R<?> prisonType(@RequestParam(required = false, value = "prisonId") String prisonId);
}
