package com.gosun.zhjg.basic.business.modules.system.feign;

import com.gosun.zhjg.basic.business.modules.system.dto.StateSecretsCheckDto;
import com.gosun.zhjg.basic.business.modules.system.dto.StateSecretsDto;
import com.gosun.zhjg.basic.business.modules.system.dto.StateSecretsSummaryCheckDto;
import com.gosun.zhjg.basic.business.modules.system.dto.StateSecretsSummaryDto;
import com.gosun.zhjg.common.msg.ResultVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(value = "zhjg-basic-business", path = "stateSecretsEncrypt", contextId = "stateSecretsEncrypt")
public interface StateSecretsEncryptApi {

    @ApiOperation("保存新增加密数据")
    @PostMapping("saveEncryptData")
    public ResultVO saveEncryptData(@RequestBody StateSecretsDto saveDto);

    @ApiOperation("保存签名")
    @PostMapping("saveSummary")
    public ResultVO saveSummary(@RequestBody StateSecretsSummaryDto saveDto);

    @ApiOperation("检查摘要")
    @PostMapping("checkSummary")
    public ResultVO checkSummary(@RequestBody StateSecretsSummaryCheckDto saveDto);

    @ApiOperation("获取解密数据")
    @PostMapping("getDecryptDataAndCheck")
    public ResultVO getDecryptDataAndCheck(@RequestBody StateSecretsCheckDto dto);

    @ApiOperation("计算数据完整性")
    @PostMapping("dataIntegrity")
    public ResultVO dataIntegrity(@RequestBody StateSecretsSummaryDto dto);


    @ApiOperation("比对数据完整性")
    @PostMapping("checkDataIntegrity")
    public ResultVO checkDataIntegrity(@RequestBody StateSecretsSummaryCheckDto dto);
}
