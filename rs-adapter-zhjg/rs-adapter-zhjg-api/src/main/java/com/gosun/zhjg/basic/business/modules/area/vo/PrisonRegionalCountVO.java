package com.gosun.zhjg.basic.business.modules.area.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * prison_regional_control-区域防控表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-11-27 17:04:46
 */
@Data
@ApiModel("prison_regional_control-区域防控表VO")
public class PrisonRegionalCountVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * key
     */
    @ApiModelProperty(value = "key")
    private Integer key;
    /**
     * name
     */
    @ApiModelProperty(value = "name")
    private String name;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer count;
    /**
     * 预警数量
     */
//    @ApiModelProperty(value = "预警数量")
//    private Integer warningNum;
    /**
     * 民警数量
     */
//    @ApiModelProperty(value = "民警数量")
//    private Integer policeNum;
    /**
     * 被监管人员数量
     */
//    @ApiModelProperty(value = "被监管人员数量")
//    private Integer pisonerNum;
    /**
     * 外来人员数量
     */
//    @ApiModelProperty(value = "外来人员数量")
//    private Integer outsiderNum;

}
