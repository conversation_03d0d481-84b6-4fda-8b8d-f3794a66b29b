package com.gosun.zhjg.basic.business.modules.prisoner.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class BaseNewPrisonerVO {
    @ApiModelProperty("编号")
    private String prisonerId;

    @ApiModelProperty("姓名")
    private String prisonerName;

    @ApiModelProperty("单位代码")
    private String prisonId;

    @ApiModelProperty("监室号")
    private String roomId;

    @ApiModelProperty("监室名")
    private String roomName;

    @ApiModelProperty("在押人员正面照片")
    private String frontPhoto;

    @ApiModelProperty("性别")
    private String sex;

    @ApiModelProperty("性别")
    private String sexDisplayName;

    @ApiModelProperty("关押期限")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date imprisonmentDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @ApiModelProperty("入所时间")
    private Date rssj;

    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("出生日期")
    private Date birth;

    @ApiModelProperty("风险等级")
    private String riskLevel;

    @ApiModelProperty("涉嫌罪名")
    private String suspectedCharges;

    @ApiModelProperty("诉讼环节")
    private String litigationLink;

    @ApiModelProperty("1 :重病号")
    private Integer sickType;

    @ApiModelProperty("1 :高风险")
    private Integer riskType;

    @ApiModelProperty("1: 紧急风险")
    private Integer urgentRiskType;

    @ApiModelProperty("1: 重点关注")
    private Integer importantType;

    @ApiModelProperty("1:死刑标签")
    private Integer deathTag;

    @ApiModelProperty("办案单位")
    private String caseHandUnit;
}
