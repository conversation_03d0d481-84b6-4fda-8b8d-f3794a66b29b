package com.gosun.zhjg.prison.room.terminal.modules.goanalyse.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("初始化人员数据")
public class GoanalyseInitPersonDto {

    @ApiModelProperty("ip")
    private String ip;

    @ApiModelProperty("在押人员1，民警2.外开人员3")
    private List<Integer> groupId;

    @ApiModelProperty("监室id")
    private String roomId;

    @ApiModelProperty("监所编号")
    private String prisonId;
}
