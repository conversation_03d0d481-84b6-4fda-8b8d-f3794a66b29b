package com.gosun.zhjg.common.util;

import com.gosun.zhjg.common.exception.FormValidException;

import java.io.Serializable;

/**
 * @ClassName: CommonServiceUtils
 * @Author: ren
 * @Description: 通过方法工具类
 * @CreateTIme: 2019/6/12 0012 下午 4:16
 **/
@SuppressWarnings({ "rawtypes" })
public class CommonServiceUtils {

	/**
	 * 判断结果是否有效
	 *
	 * @param obj
	 * @param id
	 */
	public static void isEffectiveObj(Object obj, Serializable id) {
		if (null == obj) {
			throw new FormValidException("没有找到数据 [id = " + id + " ]");
		}
	}

}
