package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 基础系统配置
 *
 * <AUTHOR>
 * @date 2024/12/13 9:05
 */
@Data
public class BaseSystemRuleConfigKeyValueDto implements Serializable {

	public BaseSystemRuleConfigKeyValueDto() {
	}

	public BaseSystemRuleConfigKeyValueDto(String key, String value) {
		this.key = key;
		this.value = value;
	}

	/**
	 * 配置项key。与单位代码组合唯一
	 */
	@ApiModelProperty(value = "配置项key。与单位代码组合唯一")
	private String key;
	/**
	 * 配置项值
	 */
	@ApiModelProperty(value = "配置项值")
	private String value;
}
