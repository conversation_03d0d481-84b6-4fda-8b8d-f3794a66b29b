package com.gosun.zhjg.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 所请状态。字典：C_SQZT
 *
 * <AUTHOR>
 */

public enum EventStatusEnum {
    STATUS_3("3", "待核实"),
    STATUS_0("0", "待办结"),
    STATUS_1("1", "已办结"),
    STATUS_4("4", "已忽略"),
    // 已废弃
//    STATUS_2("2", "待处置"),

    /**
     * 预警待核实
     */
    WAIT_VERIFY("3", "待核实"),
    /**
     * 巡控处理完，到管教，医生
     */
    WAIT_DISPOSE("0", "待处置"),
    /**
     * 领导未审核
     */
    WAIT_AUDIT("5", "待办结"),
    /**
     * 领导审核通过
     */
    DONE("1", "已办结"),

    END("4", "结束"),
    ;

    private String value;
    private String desc;

    EventStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    @JsonValue
    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value) {
        for (EventStatusEnum e : EventStatusEnum.values()) {
            if (e.value.equals(value)) {
                return e.desc;
            }
        }
        return null;
    }
}
