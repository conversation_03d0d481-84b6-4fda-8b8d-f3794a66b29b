package com.gosun.zhjg.basic.business.modules.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.gosun.zhjg.common.vo.BaseFileVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* BaseSystemInfoVO 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2022-12-23
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="BaseSystemInfoVO对象", description="")
public class BaseSystemInfoVO implements Serializable {


    @JsonProperty("id")
    @ApiModelProperty(value = "主键")
    private String id;

    @JsonProperty("systemName")
    @ApiModelProperty(value = "系统名字")
    private String systemName;

    @JsonProperty("systemNameShort")
    @ApiModelProperty(value = "系统名字缩写")
    private String systemNameShort;

    @JsonProperty("onlineDate")
    @ApiModelProperty(value = "系统上线日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    private Date onlineDate;

    @JsonProperty("loginPhoto")
    @NotEmpty(message = "登录页logo图片 不能为空")
    @ApiModelProperty(value = "登录页logo图片",required = true)
    private List<BaseFileVo> loginPhoto;

    @JsonProperty("systemPhoto")
    @NotEmpty(message = "系统名称图片 不能为空")
    @ApiModelProperty(value = "系统名称图片",required = true)
    private List<BaseFileVo> systemPhoto;

    @JsonProperty("logoLoginUrl")
    @ApiModelProperty(value = "登录页logo图片")
    private String logoLoginUrl;

    @JsonProperty("logoLoginUrlName")
    @ApiModelProperty(value = "登录页logo图片名字")
    private String logoLoginUrlName;

    @JsonProperty("systemNameUrl")
    @ApiModelProperty(value = "系统名称图片")
    private String systemNameUrl;

    @JsonProperty("systemNameUrlName")
    @ApiModelProperty(value = "系统名称图片名字")
    private String systemNameUrlName;

    @JsonProperty("versionInfo")
    @ApiModelProperty(value = "版权信息")
    private String versionInfo;

    @JsonProperty("systemInfo")
    @ApiModelProperty(value = "系统说明")
    private String systemInfo;

    @JsonProperty("updateUser")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    @JsonProperty("updateTime")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    private Date updateTime;

    @ApiModelProperty(value = "1=pki 0-默认")
    private String loginType;

    @ApiModelProperty("公匙")
    private String publicKey;

    @ApiModelProperty("前端配置项（配置文件中配置）")
    private Map<String, String> frontendProperties;

    /**
     * 背景图片
     */
    @ApiModelProperty("背景图片")
    private String backgroundImage;

    /**
     * 登录页效果 1：警务蓝（默认） 2：科技蓝
     */
    @ApiModelProperty("登录页效果 1：警务蓝（默认） 2：科技蓝")
    private String loginPageEffect;

    /**
     * 是否展示插件列表 0：否 1：是
     */
    @ApiModelProperty("是否展示插件列表 0：否 1：是")
    private String isShowPlugin;

    /**
     * 是否展示浏览器列表 0：否 1：是
     */
    @ApiModelProperty("是否展示浏览器列表 0：否 1：是")
    private String isShowBrowser;

    /**
     * 是否展示超链接网址 0：否 1：是
     */
    @ApiModelProperty("是否展示超链接网址 0：否 1：是")
    private String isShowHyperlink;

    /**
     * 插件列表
     */
    @ApiModelProperty(value = "插件列表")
    private List<BaseSystemInfoUrlVO> plugins;

    /**
     * 浏览器列表
     */
    @ApiModelProperty(value = "浏览器列表")
    private List<BaseSystemInfoUrlVO> browsers;

    /**
     * 超链接网址列表
     */
    @ApiModelProperty(value = "超链接网址列表")
    private List<BaseSystemInfoUrlVO> hyperlinks;
}
