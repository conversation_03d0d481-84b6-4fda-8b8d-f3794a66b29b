package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 出入登记-进出监室类型
 *
 * <AUTHOR>
 * @date 2025/3/21 9:46
 */
public enum CwpPrisonerOutInOperationTypeEnum implements IEnum<Integer> {
	WAITING_OUT(-1, "待出监"),
	OUT(0, "出监"),
	IN(1, "进监"),
	;

	@Getter
	private final Integer value;
	private final String desc;

	CwpPrisonerOutInOperationTypeEnum(Integer value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(Integer value) {
		for (CwpPrisonerOutInOperationTypeEnum e : CwpPrisonerOutInOperationTypeEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}

	public static Integer getValue(String desc) {
		for (CwpPrisonerOutInOperationTypeEnum e : CwpPrisonerOutInOperationTypeEnum.values()) {
			if (e.desc.equals(desc)) {
				return e.value;
			}
		}
		return null;
	}
}