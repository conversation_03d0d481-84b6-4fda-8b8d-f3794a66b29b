package com.gosun.zhjg.basic.business.modules.prison.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel("监所基础信息VO")
public class BasePrisonVO implements Serializable {
    @ApiModelProperty(value = "监所id")
    private String prisonId;
    /**
     * 监所名称
     */
    @ApiModelProperty(value = "监所名称")
    private String prisonName;
}
