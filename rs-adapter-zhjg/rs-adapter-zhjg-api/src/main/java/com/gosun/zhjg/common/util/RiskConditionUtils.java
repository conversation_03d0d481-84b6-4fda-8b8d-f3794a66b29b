package com.gosun.zhjg.common.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mvel2.MVEL;

import javax.validation.constraints.NotEmpty;
import java.util.*;

@Slf4j
public class RiskConditionUtils {

    public final static String MVEL_VARS_KEY = "v";


    /**
     * 条件对象转换储存字符串
     *
     * @param conditionList
     * @return
     */
    public static String condition2String(List<RiskConditionDto> conditionList) {
        StringBuilder conditionValueSB = new StringBuilder();
        for (int i = 0; i < conditionList.size(); i++) {
            RiskConditionDto condition = conditionList.get(i);
            if (i != 0) {
                conditionValueSB.append(condition.getJoinOp().toUpperCase());
            }
            conditionValueSB.append(String.format("(%s %s)", condition.getOp(), condition.getVal()));
        }
        return conditionValueSB.toString();
    }


    /**
     * 数据库储存的条件字符串抓换对象
     *
     * @param conditionValue
     * @return
     */
    public static List<RiskConditionDto> string2Condition(String conditionValue) {
        if (StringUtils.isBlank(conditionValue)) {
            return Collections.emptyList();
        }
        List<RiskConditionDto> conditionList = new ArrayList<>();
        String[] split = conditionValue.split("[\\(\\)]");
        for (int i = 0; i < split.length; i = (i + 2)) {
            RiskConditionDto condition = new RiskConditionDto();
            conditionList.add(condition);
            condition.setJoinOp(StringUtils.isBlank(split[i]) ? null : split[i]);
            condition.setOp(split[i + 1].split(" ")[0]);
            condition.setVal(split[i + 1].split(" ")[1]);
        }
        return conditionList;
    }


    public static boolean compare(String conditionValue, Object value, Integer valueType) {
        List<RiskConditionDto> conditionList = string2Condition(conditionValue);
        return compare(conditionList, value, valueType);
    }


    /**
     * 判断条件是否成立
     *
     * @param conditionList 条件
     * @param value         比对值
     * @param valueType     1 数值，2字符
     * @return
     */
    public static boolean compare(List<RiskConditionDto> conditionList, Object value, Integer valueType) {
        boolean v = compare(conditionList, value, new RiskConditionUtilCallback() {
            @Override
            public String callback(String op, String val) {
                switch (valueType) {
                    case 1:
                        break;
                    case 2:
                        val = strSymbol(val);
                        break;
                    default:
                        throw new IllegalArgumentException("Illegal valueType");
                }
                return String.format("v %s %s", op, val);
            }
        });
        return v;
    }


    public static boolean compare(List<RiskConditionDto> conditionList, Object value, RiskConditionUtilCallback callback) {
        if (conditionList == null || conditionList.isEmpty()) {
            throw new IllegalArgumentException("Illegal conditionList");
        }
        StringBuffer expressionSB = new StringBuffer();
        for (int i = 0; i < conditionList.size(); i++) {
            RiskConditionDto condition = conditionList.get(i);
            String joinOp = null;
            if (i != 0) {
                switch (condition.getJoinOp().trim().toUpperCase()) {
                    case "AND":
                        joinOp = "&&";
                        break;
                    case "OR":
                        joinOp = "||";
                        break;
                    default:
                        throw new IllegalArgumentException("Illegal condition.joinOp");
                }
                expressionSB.append(joinOp);
            }
            String expression = callback.callback(condition.getOp(), condition.getVal());
            expressionSB.append(String.format(" ( %s ) ", expression));
        }
        try {
            Map vars = new HashMap();
            vars.put("v", value);
            boolean result = (boolean) MVEL.eval(expressionSB.toString(), vars);
            log.debug("expression={} value={}, result={}", expressionSB.toString(), value, result);
            return result;
        } catch (Exception e) {
            throw new RuntimeException(("表达式执行异常：expression=" + expressionSB.toString() + " ,v=" + value), e);
        }
    }

    /**
     * 01 变成  '01'
     */
    public static String strSymbol(Object val) {
        return ("'" + val + "'");
    }


    public static boolean compareValueStartsWith(String conditionValue, Object value) {
        List<RiskConditionDto> condition = string2Condition(conditionValue);
        if (value == null) {
            // null.startsWith 空指针这里统一处理下
            return false;
        }
        return compareValueStartsWith(condition, value);
    }


    /**
     * 判断value是否以某些字符串开头<br/>
     * 举例 户籍、涉嫌罪名等。 比如  050101 入户抢劫案 和 050102 拦路抢劫案都归属于 0501 “抢劫案”下。 数据表中会储存到6位 到具体项，字典中只到4位
     *
     * @param condition
     * @param value
     * @return
     */
    public static boolean compareValueStartsWith(List<RiskConditionDto> condition, Object value) {
        if (value == null) {
            // null.startsWith 空指针这里统一处理下
            return false;
        }
        boolean compare = compare(condition, value, new RiskConditionUtilCallback() {
            @Override
            public String callback(String op, String val) {
                String expression = "";
                switch (op) {
                    case "!=":
                        expression = "!";
                    case "==":
                        expression = expression + (String.format("v.startsWith('%s')", val));
                        break;
                    default:
                        // 这个配置上不应存在
//                        expression = String.format("v %s '%s'", op, val);
                        expression = "false";
                        break;
                }
                return expression;
            }
        });
        return compare;
    }

    /**
     * 包含判断
     *
     * @param conditionValue
     * @param value
     * @return
     */
    public static boolean compareValueContains(String conditionValue, Object value) {
        List<RiskConditionDto> condition = string2Condition(conditionValue);
        return compareValueContains(condition, value);
    }

    /**
     * 包含判断
     *
     * @param condition
     * @param value
     * @return
     */
    public static boolean compareValueContains(List<RiskConditionDto> condition, Object value) {
        boolean compare = compare(condition, value, new RiskConditionUtilCallback() {
            @Override
            public String callback(String op, String val) {
                String expression = "";
                switch (op) {
                    case "!=":
                        expression = "!";
                    case "==":
                        expression = expression + (String.format("v.contains('%s')", val));
                        break;
                    default:
                        // 这个配置上不应存在
//                        expression = String.format("v %s '%s'", op, val);
                        expression = "false";
                        break;
                }
                return expression;
            }
        });
        return compare;
    }


    public interface RiskConditionUtilCallback {
        String callback(String op, String val);
    }


    @Data
    public static class RiskConditionDto {
        /**
         * 与上个条件逻辑连接符  取值 AND,OR
         */
        private String joinOp;

        /**
         * 单个条件比较操作符  取值 >,=,<,>=,<=,==
         */
        @NotEmpty(message = "op不能为空")
        private String op;

        /**
         * op条件的比较的值
         */
        @NotEmpty(message = "val不能为空")
        private String val;
    }
}
