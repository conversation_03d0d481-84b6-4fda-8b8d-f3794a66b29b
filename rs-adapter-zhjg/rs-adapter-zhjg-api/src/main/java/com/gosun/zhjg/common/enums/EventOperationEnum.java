package com.gosun.zhjg.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 所请操作类型状态
 *
 */

public enum EventOperationEnum {
    SAVE("save", "保存"),
    SUBMIT("submit", "提交"),
    CLOSE("close", "办结"),
    ;

    private String value;
    private String desc;

    EventOperationEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    @JsonValue
    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value) {
        for (EventOperationEnum e : EventOperationEnum.values()) {
            if (e.value.equals(value)) {
                return e.desc;
            }
        }
        return null;
    }
}
