package com.gosun.zhjg.basic.business.modules.menu.vo;

import com.gosun.zhjg.common.util.TreeLcmUtils.TreeNodeVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * com.gosun.zhjg.basic.business.modules.menu.controller.BaseMenuController.menuTree
 *
 * <AUTHOR>
 *
 */
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseMenuTreeNodeExtVO extends TreeNodeVO<BaseMenuTreeNodeExtVO> {

	/**
	 * 别称
	 */
	private String alias;

	public BaseMenuTreeNodeExtVO(String id, String nodeName, String menuType) {
		super(id, nodeName);
		this.menuType = menuType;
	}

	public BaseMenuTreeNodeExtVO(String id, String nodeName, String menuType,String alias) {
		super(id, nodeName);
		this.menuType = menuType;
		this.alias = alias;
	}

	private String menuType;

}
