package com.gosun.zhjg.common.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gosun.zhjg.auth.common.util.jwt.IJWTInfo;
import com.gosun.zhjg.common.config.UserContext;
import com.gosun.zhjg.common.context.Sensitive;
import com.gosun.zhjg.common.entity.BaseSensitiveEntity;
import com.gosun.zhjg.common.msg.ResponseMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 去敏感工具类
 *
 * <AUTHOR>
 *
 */
@Slf4j
public class SensitiveUtils {

	/**
	 * 默认对敏感字的替换 符号
	 */
	public static final String HIDE_CHARACTER_DEFAULT = "******";

	/**
	 * 去除敏感，根据注解
	 *
	 * @param value
	 * @param annotation
	 * @return
	 */
	public static Object rinse(Object value, Sensitive annotation) {
		// 几个需要关联token的是否都有内容
		if (annotation.prison().length != 0 || annotation.role().length != 0 || annotation.onlyForbidRole().length != 0 || annotation.onlyForbidPrison().length != 0) {
			IJWTInfo jwtInfo = null;
			try {
				jwtInfo = UserContext.getJwtInfo();
				Integer roleId = jwtInfo.getRoleId();
				String prisonId = jwtInfo.getPrisonId();
				boolean pass = false; // 是否放行.注意放行可能是因为未配置
// @formatter:off
				if((annotation.role().length == 0 || ArrayUtils.contains(annotation.role(), roleId))
						&& (annotation.prison().length == 0 || ArrayUtils.contains(annotation.prison(), prisonId))) {
					// role和prison放行
					pass = true;
				}
				// 这里“pass &&” 如果上面不放行，这里就不进一步做验证。目前两个对立的规则都放行这里才放行
				if(pass && (!ArrayUtils.contains(annotation.onlyForbidRole(), roleId))
						&& (!ArrayUtils.contains(annotation.onlyForbidPrison(), prisonId))) {
					// onlyForbidRole和onlyForbidPrison放行
					pass = true;
				}else {
					pass = false;
				}
// @formatter:on
				if (pass) {
					// 两个对立的规则都放行这里才放行，对于冲突的部分还是去敏感化
					return value; // 不处理
				} else {
					// 冲突的部分
					// TODO
				}
			} catch (Exception ex) {
				// token不存在
//				log.info(ex.getMessage());
			}
		}
		// 执行到这里的都是需要转换的 // ┗( ▔, ▔ )┛
		String rSensitiveValue = StringUtils.isBlank(annotation.value()) ? HIDE_CHARACTER_DEFAULT : annotation.value();
		if (value == null || StringUtils.isBlank(annotation.regex())) {
			return rSensitiveValue;
		} else {
			return value.toString().replaceAll(annotation.regex(), rSensitiveValue);
		}
	}

	/**
	 * 去除敏感
	 *
	 * @param obj        对象
	 * @param nodes      节点属性
	 * @param annotation
	 * @return
	 */
	@SuppressWarnings("deprecation")
	public static Object rinse(Object o, String nodes, List<BaseSensitiveEntity> mapPos) {
		if (o == null) {
			return null;
		}
		Class<?> clazz = o.getClass();
		if (Collection.class.isAssignableFrom(clazz)) {
			Collection<?> c = (Collection<?>) o;
			for (Object t : c) {
				rinse(t, nodes, mapPos);
			}
			return o;
		}
		if (clazz.isArray()) {
			if (clazz.getComponentType().isPrimitive()) {
				return o; // 基础类型数组 不能直接转换Object[]。 没必要继续处理
			}
			Object[] c = (Object[]) o;
			for (Object t : c) {
				rinse(t, nodes, mapPos);
			}
			return o;
		}
		if (clazz.getName().startsWith("java")) {
			return o; // 剩下的java基础包装类等类也不需要继续处理
		}
		Field[] fields = clazz.getDeclaredFields();
		for (Field field : fields) {
			String cFieldNodes = StringUtils.isBlank(nodes) ? field.getName() : String.format("%s.%s", nodes, field.getName());
			ImmutablePair<BaseSensitiveEntity, List<BaseSensitiveEntity>> currentNodeMapPos = getCurrentNodeMapPos(cFieldNodes, mapPos);

			boolean isObject = isObject(field.getType());
			if (isObject) {
				org.springframework.util.ReflectionUtils.makeAccessible(field); // field.setAccessible(true);
				try {
					Object val = field.get(o);
					if (val == null) {
						continue;
					}
					rinse(val, cFieldNodes, currentNodeMapPos.getRight());
					continue;
				} catch (Exception e) {
					log.error("", e);
				}
			}
			Sensitive fieldSensitiveInfo = currentNodeMapPos.getLeft();
			if (fieldSensitiveInfo == null) {
				// 需要去敏感化处理,如果配置中要不到再考虑从注解中去拿，这样配置的优先级大于注解
				fieldSensitiveInfo = field.getAnnotation(Sensitive.class);
			}
			if (fieldSensitiveInfo == null) {
				continue;
			}
			if (fieldSensitiveInfo.clean() == 1) {
				continue;
			}
			org.springframework.util.ReflectionUtils.makeAccessible(field); // field.setAccessible(true);
			// 默认值
			String defaultValue = StringUtils.isBlank(fieldSensitiveInfo.value()) ? null : fieldSensitiveInfo.value().trim();
			try {
				if (field.getType().isPrimitive()) {
					// 如果是基础类型，注意去敏感就不能随便赋值了，不能为空
					field.set(o, typeCast(defaultValue, field.getType(), field.getName()));
					continue;
				}
				Object val = field.get(o);
				if (val == null) {
					// 给出默认值，赋值上去
					if (String.class == field.getType() && (StringUtils.isBlank(fieldSensitiveInfo.value()) || StringUtils.isNotBlank(fieldSensitiveInfo.regex()))) {
						val = HIDE_CHARACTER_DEFAULT;
					} else {
						val = fieldSensitiveInfo.value();
					}
					field.set(o, typeCast(val, field.getType(), field.getName()));
					continue;
				} else {
					Object rinseRes = SensitiveUtils.rinse(val, fieldSensitiveInfo);
					if (val.equals(rinseRes)) {
						continue;
					} else if (field.getType() != rinseRes.getClass()) {
						// 类型不匹配，置null或者设置默认值
						field.set(o, typeCast(rinseRes, field.getType(), field.getName()));
						continue;
					}
					field.set(o, rinseRes);
					continue;
				}
			} catch (Exception e) {
				log.error("", e);
			}
		}
		return o;
	}

	/**
	 * 是否对象
	 *
	 * @return
	 */
	public static boolean isObject(Class<?> clazz) {
		if (clazz.isArray() || Collection.class.isAssignableFrom(clazz) || (!clazz.isPrimitive() && !clazz.getName().startsWith("java"))) {
			return true;
		}
		return false;
	}

	/**
	 * 获取nodes节点匹配的配置
	 *
	 * @param nodes
	 * @param mapPos
	 * @return left:当前具体节点。right:当前及其子节点
	 */
	public static ImmutablePair<BaseSensitiveEntity, List<BaseSensitiveEntity>> getCurrentNodeMapPos(String nodes, List<BaseSensitiveEntity> mapPos) {
		List<BaseSensitiveEntity> collect = mapPos.stream().filter(f -> f.getAttr() != null && (f.getAttr().startsWith(nodes) || f.getAttr().startsWith("$.data.".concat(nodes)) || f.getAttr().startsWith("$.page.records.".concat(nodes)))).collect(Collectors.toList());
		Optional<BaseSensitiveEntity> optional = collect.stream().filter(f -> (f.getAttr().startsWith(nodes) || f.getAttr().startsWith("$.data.".concat(nodes)) || f.getAttr().startsWith("$.page.records.".concat(nodes)))).findFirst();
		BaseSensitiveEntity sensitivePo = null;
		if (optional.isPresent()) {
			sensitivePo = optional.get();
//			collect.remove(sensitivePo);
		}
		ImmutablePair<BaseSensitiveEntity, List<BaseSensitiveEntity>> pair = new ImmutablePair<BaseSensitiveEntity, List<BaseSensitiveEntity>>(sensitivePo, collect);
		return pair;
	}

	/**
	 * 类型转换，对于基础类型转化失败给出默认值
	 *
	 * @param defaultValue 需要转换的值
	 * @param clazz        要转换的类型
	 * @param fieldName    转换失败附加信息。这里主要为展示出是哪个属性配置有问题打印出属性名
	 * @return
	 */
	public static Object typeCast(Object defaultValue, Class<?> clazz, String ext) {
		Object val = null;
		try {
			val = com.alibaba.fastjson.util.TypeUtils.cast(defaultValue, clazz, null);
		} catch (Exception e) {
			// [age] java.lang.NumberFormatException: For input string: "******"
			log.warn((ext == null ? "" : String.format("[%s] ", ext)) + String.format("%s: %s", e.getClass().getName(), e.getMessage()));
		} finally {
			if (val == null && clazz.isPrimitive()) {
				val = com.alibaba.fastjson.util.TypeUtils.cast(null, clazz, null);
			}
		}
		return val;
	}

	/**
	 * AOP入口
	 *
	 * @param result
	 * @param store
	 * @param beforeReturn 拓展。AspectBeforeReturning的返回
	 * @return
	 */
	public static Object AspectAfterReturning(ProceedingJoinPoint point, Object result, List<BaseSensitiveEntity> store, Object beforeReturn) {

		ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		if (attributes == null) {
			return result;
		}
		HttpServletRequest request = attributes.getRequest();
		String requestURI = request.getRequestURI();
		String method = request.getMethod();

		AntPathMatcher matcher = new AntPathMatcher();
		List<BaseSensitiveEntity> storeCollect = new ArrayList<BaseSensitiveEntity>();

		// @formatter:off 匹配出最优配置
		Map<String, List<BaseSensitiveEntity>> collect = store.stream()
				.filter(f -> f.getUri() != null
					&& (StringUtils.isBlank(f.getHttpMethod()) || method.equals(f.getHttpMethod().toUpperCase()))
					&& matcher.match(f.getUri(), requestURI))
				.collect(Collectors.groupingBy(BaseSensitiveEntity::getUri));
		Optional<String> opt = collect.keySet().stream().sorted(matcher.getPatternComparator(requestURI)).findFirst();
		// @formatter:on
		if (opt.isPresent()) {
			storeCollect = collect.get(opt.get());
			log.debug("Matcher URI: {}", opt.get());
		}

		if (result instanceof ResponseMessage) {
			ResponseMessage<?> r = ((ResponseMessage<?>) result);
			Object data = r.getData();
			Page<?> page = r.getPage();
			List<?> records = null;
			if (data != null) {
				SensitiveUtils.rinse(data, "", storeCollect); // $.data
			}
			if (page != null && (records = page.getRecords()) != null && records.size() > 0) {
				SensitiveUtils.rinse(records, "", storeCollect); // $.page.records
			}
		}
		return result;
	}

	/**
	 * 拓展
	 *
	 * @param point
	 * @return
	 */
	public static Object AspectBeforeReturning(ProceedingJoinPoint point, String appName) {

		return null;
	}

}
