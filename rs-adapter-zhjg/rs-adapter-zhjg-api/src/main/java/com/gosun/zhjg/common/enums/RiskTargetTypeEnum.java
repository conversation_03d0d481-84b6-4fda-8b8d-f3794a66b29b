package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 风险研判，模型类型
 *
 * <AUTHOR>
 * @date 2022/9/1 9:30
 */
public enum RiskTargetTypeEnum implements IEnum<Integer> {
    PRISONER(1, "人员风险模型"), ROOM(2, "监室模型"), PRISON(3, "监所模型");

    private Integer value;
    private String desc;

    RiskTargetTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    @JsonValue
    public String getDesc() {
        return desc;
    }

    public static String getDesc(Integer value) {
        for (RiskTargetTypeEnum e : RiskTargetTypeEnum.values()) {
            if (e.value.equals(value)) {
                return e.desc;
            }
        }
        return null;
    }

    public static Integer getValue(String desc) {
        for (RiskTargetTypeEnum e : RiskTargetTypeEnum.values()) {
            if (e.desc.equals(desc)) {
                return e.value;
            }
        }
        return null;
    }
}