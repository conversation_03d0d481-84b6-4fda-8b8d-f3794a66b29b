package com.gosun.zhjg.prison.room.terminal.config.socketio.kafka.product;


import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RabbitProduct {
    @Autowired
    private RabbitTemplate rabbitTemplate;
    /**
     *  通用消息
     * @param topic 主题
     * @param message 消息
     */
    public void sendMsg(String topic,String message) {
        log.info("+++++++++++++++++++++ topic={}， message = {}",topic,message);
        rabbitTemplate.convertAndSend(topic,message);
    }
}
