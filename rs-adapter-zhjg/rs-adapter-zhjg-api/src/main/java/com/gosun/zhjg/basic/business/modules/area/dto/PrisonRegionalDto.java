package com.gosun.zhjg.basic.business.modules.area.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 楼层分布Dto
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-08-13 08:57:25
 */
@Data
@ApiModel("获取区域防控统计数据")
public class PrisonRegionalDto {

    /**
     * 区域防控id
     */
    @ApiModelProperty(value = "区域防控id")
    private String regionalId;
    /**
     * 所id
     */
    @ApiModelProperty(value = "所id")
    private String prisonId;
}
