package com.gosun.zhjg.basic.business.modules.file.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.io.FilenameUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * 仓内屏文件
 *
 * <AUTHOR>
 *
 */
@Data
@ApiModel("仓内屏文件VO")
public class BaseDeviceInscreenFileVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String id;
	/**
	 * 主机编号
	 */
	@ApiModelProperty(value = "主机编号")
	private Integer hostNum;
	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "设备编号")
	private Integer deviceNum;
	/**
	 * O是呼出I是呼入
	 */
	@ApiModelProperty(value = "O是呼出I是呼入")
	private String callType;

	public String getCallTypeDisplayName() {
		if ("O".equals(callType)) {
			return "呼出";
		}
		if ("I".equals(callType)) {
			return "呼入";
		}
		return callType;
	}

	/**
	 * 呼叫时间
	 */
	@ApiModelProperty(value = "呼叫时间")
	private Date callTime;
	/**
	 * 主机型号
	 */
	@ApiModelProperty(value = "主机型号")
	private String model;
	/**
	 * 文件路径
	 */
	@ApiModelProperty(value = "文件路径")
	private String filePath;

	@ApiModelProperty(value = "文件名称")
	public String getFilename() {
		if (filePath == null || "".equals(filePath)) {
			return null;
		}
		String strip = FilenameUtils.getName(filePath);
		return strip;
	}

	public String getFiletype() {
		// TODO 预留
		if (filePath == null || "".equals(filePath)) {
			return null;
		}
		// 之前型号会有wav格式。后面都是mp4
		if (filePath.endsWith("mp4")) {
			return "视频";
		}
		return "音频";
	}

	/**
	 * 分区
	 */
	@ApiModelProperty(value = "分区")
	private Integer zoneNum;
//	/**
//	 * 文件标记，这里存放文件名用于检索是否存在
//	 */
//	@ApiModelProperty(value = "文件标记，这里存放文件名用于检索是否存在")
//	private String fileSign;
//	/**
//	 * 创建时间，服务器
//	 */
//	@ApiModelProperty(value = "创建时间，服务器")
//	private Date createTime;

}
