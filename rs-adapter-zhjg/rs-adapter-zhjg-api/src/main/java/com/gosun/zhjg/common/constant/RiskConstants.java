package com.gosun.zhjg.common.constant;

/**
 * 风险相关
 *
 * <AUTHOR>
 * @date 2022/8/18 10:39
 */
public interface RiskConstants {

    /**
     * 基本情况-年龄
     */
    final String RISK_ITEM_CODE_JBQK_AGE = "jbqk_age";
    /**
     * 基本情况-民族
     */
    final String RISK_ITEM_CODE_JBQK_MZ = "jbqk_mz";
    /**
     * 基本情况-国籍
     */
    final String RISK_ITEM_CODE_JBQK_GJ = "jbqk_gj";
    /**
     * 基本情况-籍贯
     */
    final String RISK_ITEM_CODE_JBQK_JG = "jbqk_jg";
    /**
     * 基本情况-婚姻状况
     */
    final String RISK_ITEM_CODE_JBQK_HYZK = "jbqk_hyzk";
    /**
     * 基本情况-特殊身份
     */
    final String RISK_ITEM_CODE_JBQK_TSSF = "jbqk_tssf";
    /**
     * 基本情况-重刑犯
     */
    final String RISK_ITEM_CODE_JBQK_ZXF = "jbqk_zxf";
    /**
     * 基本情况-强制入级
     */
    final String RISK_ITEM_CODE_JBQK_QZRJ = "jbqk_qzrj";
    /**
     * 案件类别-涉嫌罪名
     */
    final String RISK_ITEM_CODE_AJQK_SXZM = "ajqk_sxzm";
    /**
     * 案件类别-环节变动（诉讼环节）
     */
    final String RISK_ITEM_CODE_AJQK_HJBD = "ajqk_hjbd";
    /**
     * 案件类别-处理结果
     */
    final String RISK_ITEM_CODE_AJQK_CLJG = "ajqk_cljg";
    /**
     * 日常动态-家属会见次数（例子：家属会见次数等于0）
     */
    final String RISK_ITEM_CODE_RCDT_JSSJ_COUNT = "rcdt_jssj_count";
    /**
     * 日常动态-所请管理-未完成处置任务数量
     */
    final String RISK_ITEM_CODE_RCDT_SQGL_UNDONE_COUNT = "rcdt_sqgl_undone_count";
    /**
     * 日常动态-支队督导-未完成处置任务数量
     */
    final String RISK_ITEM_CODE_RCDT_DD_UNDONE_COUNT = "rcdt_dd_undone_count";
    /**
     * 日常动态-奖励情况
     */
    final String RISK_ITEM_CODE_RCDT_JLQK = "rcdt_jlqk";
    /**
     * 日常动态-限制措施（械具使用）
     */
    final String RISK_ITEM_CODE_RCDT_XJSY = "rcdt_xjsy";

    /**
     * 打架斗殴-对应的来源为所情登记中的发生事件“违反监规、纪律 / 打架斗殴、欺压他人”
     */
    final String RISK_ITEM_CODE_WGQK_DJDO = "wgqk_djdo";

    /**
     * 自杀自缢-对应的来源为所情登记中的发生事件“违反监规、纪律 / 自杀自残以及装病对抗管理”
     */
    final String RISK_ITEM_CODE_WGQK_ZSZY = "wgqk_zszy";

    /**
     * 所情事件
     */
    final String RISK_ITEM_CODE_PRISON_EVENT = "prison_event";

    /**
     * 风险评估情况
     */
    final String RISK_ITEM_CODE_FXPGDJ_FXPGDJ = "fxpgdj_fxpgdj";

    /**
     * 身体心理情况-身体情况（登记为“重病号”、“特殊病号”）
     */
    final String RISK_ITEM_CODE_STXLQK_ZBH_TSBH = "stxlqk_zbh_tsbh";

    /**
     * 身体心理情况-心理情况（有任意量表“测评结果”数据项为“阳性”，或测评得分不在“量表正常分值”区间内）
     */
    final String RISK_ITEM_CODE_STXLQK_XLCP = "stxlqk_xlcp";

    /**
     * 监室风险指标-人员整体风险-人员风险-该监室各级风险人员风险分之和/总人数
     */
    final String RISK_ITEM_CODE_RYZTFX_RYFX = "ryztfx_ryfx";
    /**
     * 监室风险指标-监室动态-耳目设置-耳目数量
     */
    final String RISK_ITEM_CODE_JSDT_EM_COUNT = "jsdt_em_count";
    /**
     * 监室风险指标-监室动态-所内就医-占监室总人数比例
     */
    final String RISK_ITEM_CODE_JSDT_SNJY = "jsdt_snjy";
    /**
     * 监室风险指标-监室动态-所情登记-未完成处置任务数量（非指定人员）
     */
    final String RISK_ITEM_CODE_JSDT_SQDJ_UNDONE_COUNT = "jsdt_sqdj_undone_count";
    /**
     * 监室风险指标-监室动态-支队督导-未完成处置任务数量（非指定人员）
     */
    final String RISK_ITEM_CODE_JSDT_DD_UNDONE_COUNT = "jsdt_dd_undone_count";

    /**
     * 监室风险指标-管教履职-谈话教育-每人每月【统计自然月】少于2次的数量
     */
    final String RISK_ITEM_CODE_GJLZ_THJY_MONTH_LT_ARG_COUNT = "gjlz_thjy_month_lt_arg_count";

    /**
     * 监室风险指标-管教履职-面对面管理-每天次数
     */
    final String RISK_ITEM_CODE_GJLZ_MDM = "gjlz_mdm";
    /**
     * 监室风险指标-管教履职-安全检查-每月【统计自然月】次数
     */
    final String RISK_ITEM_CODE_GJLZ_AQJC_MONTH_COUNT = "gjlz_aqjc_month_count";
    /**
     * 监室风险指标-硬件设施-维修工单-未完成处置数量
     */
    final String RISK_ITEM_CODE_YJSS_WXGD_UNDONE_COUNT = "yjss_wxgd_undone_count";

    /**
     * 监所风险指标-监室整体风险-各监室风险分之和/总数
     */
    final String RISK_ITEM_CODE_JSZTFX_JSZTFX = "jsztfx_jsztfx";
    /**
     * 监所风险指标-支队督导-未完成处置任务
     */
    final String RISK_ITEM_CODE_ZDDD_DD_UNDONE_COUNT = "zddd_dd_undone_count";
    /**
     * 监所风险指标-所外就医-未归人数
     */
    final String RISK_ITEM_CODE_SWJY_SWJY = "swjy_swjy";
    /**
     * 监所风险指标-押解投牢-同时押解投牢人数
     */
    final String RISK_ITEM_CODE_YJTL_YJTL = "yjtl_yjtl";
    /**
     * 监所风险指标-人员执勤-当天打卡异常次数
     */
    final String RISK_ITEM_CODE_RYZQ_RYZQ = "ryzq_ryzq";

    // =================================================================================================================

    /**
     * 字典中国籍 外国人
     */
    final String RISK_DICT_DATA_CODE_CWGR = "CWGR";

    /**
     * C_GJ 中国字典值
     */
    final String RISK_DICT_C_GJ_ZG = "156";

    /**
     * 对应的来源为所情登记中的发生事件“违反监规、纪律 / 打架斗殴、欺压他人”。事件id
     */
    final String RISK_CONSTANTS_EVENT_TYPE_ID_DJDO = "6";
    /**
     * 对应的来源为所情登记中的发生事件“违反监规、纪律 / 自杀自残以及装病对抗管理”。事件id
     */
    final String RISK_CONSTANTS_EVENT_TYPE_ID_ZSZY = "15";
}
