package com.gosun.zhjg.device.server.modules.inscreen.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 仓内屏分页VO
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-09-15 10:55:29
 */
@Data
@ApiModel("仓内屏分页VO")
public class BaseDeviceInscreenVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 序列号
     */
    @ApiModelProperty(value = "序列号")
    private String serialNumber;
    /**
     * 设备编号
     */
    @ApiModelProperty(value = "设备编号")
    private String deviceId;
    /**
     * 设备ip
     */
    @ApiModelProperty(value = "设备ip")
    private String deviceIp;
    /**
     * 监室号
     */
    @ApiModelProperty(value = "监室号")
    private String roomId;

    /**
     * 监室名称
     */
    @ApiModelProperty(value = "监室名称")
    private String roomName;

    /**
     * 设备编号
     */
    @ApiModelProperty(value = "设备编号")
    private Integer deviceNum;
    /**
     *监所
     */
    @ApiModelProperty(value = "监所")
    private String prisonId;
    /**
     * 主机编号
     */
    @ApiModelProperty(value = "主机编号")
    private Integer hostNum;
    /**
     *地址盒IP
     */
    @ApiModelProperty(value = "地址盒IP")
    private String addressIp;

    private String deviceName;

    private Integer deviceType;
}
