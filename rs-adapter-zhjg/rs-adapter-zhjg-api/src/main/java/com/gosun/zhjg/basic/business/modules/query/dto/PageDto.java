package com.gosun.zhjg.basic.business.modules.query.dto;

import com.baomidou.mybatisplus.core.toolkit.ArrayUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 通用查询分页dto
 *
 * <AUTHOR> on 2020/5/19
 * @email <EMAIL>
 * @date 2020/5/19
 */
@Data
@ApiModel("通用查询分页dto")
public class PageDto extends AbstractPageQueryForm implements Serializable {

    private static final long serialVersionUID = -3274249298428458757L;

    @ApiModelProperty(value = "表名称")
    private String tableName;

    @ApiModelProperty(value = "模块编码")
    private String modelCode;

    private List<String> column;

    private List<String> filter;

    private List<String> values;

    private List<String> joins;

    @JsonIgnore
    private String orderBy;

    /**
     * 表头
     */
    @ApiModelProperty(value = "表头(不用传)")
    private List<Map> title;

    /**
     * 塞入字段
     * @param columns
     */
    public void setColumn(String... columns){
        if (ArrayUtils.isNotEmpty(columns)) {
            column= Arrays.asList(columns);
        }
    }

    /**
     * 塞入筛选条件
     * @param filters
     */
    public void setFilter(String... filters){
        if (ArrayUtils.isNotEmpty(filters)) {
            filter= Arrays.asList(filters);
        }
    }

    /**
     * 塞入值
     * @param value
     */
    public void setValues(String... value){
        if (ArrayUtils.isNotEmpty(value)) {
            values= Arrays.asList(value);
        }
    }

    /**
     * 塞入排序字段
     * @param orderys
     */
    public void setOrderBy(String... orderys){
        if (ArrayUtils.isNotEmpty(orderys)) {
            orderBy= String.join(",",orderys);
        }
    }

    /**
     * 塞入拼接符
     * @param join
     */
    public void setJoins(String... join){
        if (ArrayUtils.isNotEmpty(join)) {
            joins= Arrays.asList(join);
        }
    }


}
