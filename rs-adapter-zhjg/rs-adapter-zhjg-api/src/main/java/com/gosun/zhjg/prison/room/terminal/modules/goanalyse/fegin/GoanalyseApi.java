package com.gosun.zhjg.prison.room.terminal.modules.goanalyse.fegin;

import com.gosun.zhjg.common.msg.ResultVO;
import com.gosun.zhjg.prison.room.terminal.modules.goanalyse.dto.GoanalyseDeletePersonDto;
import com.gosun.zhjg.prison.room.terminal.modules.goanalyse.dto.GoanalyseSavePersonDto;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(value = "zhjg-prison-room-terminal", path = "goanalyse", contextId = "goanalyse")
public interface GoanalyseApi {

    @PostMapping("savePerson")
    @ApiOperation("保存人员信息")
    ResultVO savePerson(@RequestBody GoanalyseSavePersonDto dto);


    @PostMapping("deletePerson")
    @ApiOperation(value = "删除人员信息")
    ResultVO deletePerson(@RequestBody GoanalyseDeletePersonDto dto);
}
