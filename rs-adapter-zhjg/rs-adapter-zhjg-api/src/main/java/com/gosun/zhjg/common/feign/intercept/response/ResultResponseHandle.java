package com.gosun.zhjg.common.feign.intercept.response;

import com.gosun.zhjg.common.exception.BaseException;
import com.gosun.zhjg.common.msg.ResultVO;
import org.springframework.stereotype.Component;

import java.util.Objects;


/**
 * 统一响应接口为 {@link ResultVO} 格式的处理器
 */
@Component
public class ResultResponseHandle extends AbstractFeignResponseHandle<ResultVO<?>> {

    @Override
    protected ResultVO<?> doResponseHandle(ResultVO<?> result) {
        if (Objects.isNull(result)) {
            throw new BaseException("feign 响应为空");
        }

        Integer resultCode = result.getReturnCode();
        if (!Objects.equals(resultCode, ResultVO.SUCCESS)) {
            throw new BaseException("feign 响应失败,code:" + resultCode + " message:" + result.getReturnMsg());
        }
        return result;
    }

}
