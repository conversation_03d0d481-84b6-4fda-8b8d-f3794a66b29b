package com.gosun.zhjg.common.util;


import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 查询参数
 */
public class Query extends LinkedHashMap<String, Object> {
	private static final long serialVersionUID = 1L;
	//当前页码
    private int current = 1;
    //每页条数
    private int pageSize = 10;

    public Query(Map<String, Object> params){
        this.putAll(params);
        //分页参数
        if(params.get("current")!=null && !"0".equals(params.get("current").toString())) {
            this.current = Integer.parseInt(params.get("current").toString());
        }
        if(params.get("size")!=null && !"0".equals(params.get("size").toString())) {
            this.pageSize = Integer.parseInt(params.get("size").toString());
        }
        this.remove("current");
        this.remove("size");
    }

	public int getCurrent() {
		return current;
	}

	public void setCurrent(int current) {
		this.current = current;
	}

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
}
