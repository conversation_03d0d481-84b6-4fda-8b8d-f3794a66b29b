package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 工作提醒类型
 *
 * <AUTHOR>
 */
public enum WorkRemindTypeEnum implements IEnum<Integer> {
    PRISON_NOTICE_TZ(24, "通知"),
    PRISON_NOTICE_JB(25, "交办"),
    PRISON_NOTICE_CG(26, "采购");

    private Integer value;
    private String desc;

    WorkRemindTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    @JsonValue
    public String getDesc() {
        return desc;
    }

    public static String getDesc(Integer value) {
        for (WorkRemindTypeEnum e : WorkRemindTypeEnum.values()) {
            if (e.value.equals(value)) {
                return e.desc;
            }
        }
        return null;
    }

    public static Integer getValue(String desc) {
        for (WorkRemindTypeEnum e : WorkRemindTypeEnum.values()) {
            if (e.desc.equals(desc)) {
                return e.value;
            }
        }
        return null;
    }
}