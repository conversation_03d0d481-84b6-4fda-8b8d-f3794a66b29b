package com.gosun.zhjg.prison.room.terminal.modules.login.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 登录人信息
 *
 * <AUTHOR> on 2020/6/10
 * @email <EMAIL>
 * @date 2020/6/10
 */
@Data
@ApiModel("登录人信息")
public class LoginInfoVO {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "照片")
    private String photo;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "监所编码")
    private String prisonId;

    @ApiModelProperty(value = "用户id")
    private String userid;

    @ApiModelProperty("token")
    private String token;
}
