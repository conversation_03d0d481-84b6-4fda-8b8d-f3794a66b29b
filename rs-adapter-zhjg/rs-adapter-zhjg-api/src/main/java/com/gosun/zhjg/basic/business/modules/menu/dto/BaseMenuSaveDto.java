package com.gosun.zhjg.basic.business.modules.menu.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 *
 * <AUTHOR>
 *
 */
@Data
@ApiModel("${comments}新增Dto")
public class BaseMenuSaveDto {

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments", required = false)
	private String id;
	/**
	 * 菜单或功能名称
	 */
	@NotNull(message = "菜单或功能名称 不能为空!")
	@ApiModelProperty(value = "菜单或功能名称", required = false)
	private String menuName;
	/**
	 * 编码
	 */
//	@NotNull(message = "编码 不能为空!")
	@ApiModelProperty(value = "编码", required = false)
	private String code;
	/**
	 * 父级节点ID
	 */
//	@NotNull(message = "父级节点ID 不能为空!")
	@ApiModelProperty(value = "父级节点ID", required = false)
	private String parentId;
	/**
	 * 节点全路径逗号隔开
	 */
//	@NotNull(message = "节点全路径逗号隔开 不能为空!")
	@ApiModelProperty(value = "节点全路径逗号隔开", required = false)
	private String parentNodes;
	/**
	 * 菜单地址
	 */
//	@NotNull(message = "菜单地址 不能为空!")
	@ApiModelProperty(value = "菜单地址", required = false)
	private String url;
	/**
	 * 带单类型-菜单menu / 功能func
	 */
	@NotNull(message = "菜单类型-菜单menu /  功能func 不能为空!")
	@ApiModelProperty(value = "菜单类型-菜单menu /  功能func", required = false)
	private String menuType;
	/**
	 * 排序大靠前，默认100
	 */
//	@NotNull(message = "排序大靠前，默认100 不能为空!")
	@ApiModelProperty(value = "排序大靠前，默认100", required = false)
	private Integer sort;
	/**
	 * 图标
	 */
//	@NotNull(message = "图标 不能为空!")
	@ApiModelProperty(value = "图标", required = false)
	private String icon;
	/**
	 * 打开方式 inner / external
	 */
//	@NotNull(message = "打开方式	inner / external 不能为空!")
	@ApiModelProperty(value = "打开方式	inner / external", required = false)
	private String openMode;
	/**
	 * 是否启用 1 / 0
	 */
//	@NotNull(message = "是否启用 1 / 0 不能为空!")
	@ApiModelProperty(value = "是否启用 1 / 0", required = false)
	private String countUrl;
	/**
	 * 是否启用
	 */
	@ApiModelProperty(value = "是否启用", required = false)
	private Integer enable;

	@ApiModelProperty("备注")
	private String remark;

	@ApiModelProperty("别称")
	private String alias;

	@ApiModelProperty("菜单分类.字典：BASE_MENU_CLASSIFICATION")
	private String classification;

//	/**
//	 * $column.comments
//	 */
//	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//	@NotNull(message = "$column.comments 不能为空!")
//	@ApiModelProperty(value = "$column.comments", required = false)
//	private Date createTime;
//	/**
//	 * $column.comments
//	 */
//	@NotNull(message = "$column.comments 不能为空!")
//	@ApiModelProperty(value = "$column.comments", required = false)
//	private String createUserid;
//	/**
//	 * $column.comments
//	 */
//	@NotNull(message = "$column.comments 不能为空!")
//	@ApiModelProperty(value = "$column.comments", required = false)
//	private String createUsername;
//	/**
//	 * $column.comments
//	 */
//	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//	@NotNull(message = "$column.comments 不能为空!")
//	@ApiModelProperty(value = "$column.comments", required = false)
//	private Date updateTime;
//	/**
//	 * $column.comments
//	 */
//	@NotNull(message = "$column.comments 不能为空!")
//	@ApiModelProperty(value = "$column.comments", required = false)
//	private String updateUserid;
//	/**
//	 * $column.comments
//	 */
//	@NotNull(message = "$column.comments 不能为空!")
//	@ApiModelProperty(value = "$column.comments", required = false)
//	private String updateUsername;
//	/**
//	 * $column.comments
//	 */
//	@NotNull(message = "$column.comments 不能为空!")
//	@ApiModelProperty(value = "$column.comments", required = false)
//	private Integer delFlag;
}
