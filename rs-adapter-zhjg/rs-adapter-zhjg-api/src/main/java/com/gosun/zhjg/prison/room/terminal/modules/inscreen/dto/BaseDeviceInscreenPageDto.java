package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto;

import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 仓内屏分页Dto
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-09-15 10:55:29
 */
@Data
@ApiModel("仓内屏分页Dto")
public class BaseDeviceInscreenPageDto extends AbstractPageQueryForm {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    private String id;
    /**
     * 序列号
     */
    @ApiModelProperty(value = "序列号", required = true)
    private String serialNumber;
    /**
     * 设备编号
     */
    @ApiModelProperty(value = "设备编号", required = true)
    private String deviceId;
    /**
     * 设备ip
     */
    @ApiModelProperty(value = "设备ip", required = true)
    private String deviceIp;
    /**
     * 监室号
     */
    @ApiModelProperty(value = "监室号", required = true)
    private String roomId;

    /**
     * 设备类型
     */
    @ApiModelProperty(value = "1内屏， 2外屏", required = true)
    private Integer deviceType;
    /**
     * 是否忽略空监室
     */
    private Boolean excludeEmptyRoom;
}
