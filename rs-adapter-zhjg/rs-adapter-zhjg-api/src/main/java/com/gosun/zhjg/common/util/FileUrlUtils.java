package com.gosun.zhjg.common.util;

import org.springframework.util.StringUtils;

import java.util.Arrays;

public class FileUrlUtils {


    /**
     * 拼接文件路径前缀（返回给前台路径时调用）
     */
    public static String concatUrl(String url,String photoPathPrefix) {
        if(StringUtils.isEmpty(url) || StringUtils.isEmpty(photoPathPrefix))
            return "";
        if(url.startsWith("http")) {
        	return url;
        }
        if(!photoPathPrefix.endsWith(("/"))) {
            photoPathPrefix += "/";
        }
        if (url.startsWith("/file")) {
            return photoPathPrefix+url.substring("/file".length());
        }
        if(url.startsWith("/") && url.length() > 1) {
        	url = url.substring(1);
        }
        return photoPathPrefix + url;
    }

    /**
     *  截取文件路径前缀（保存文件路径时调用）
     */
    public static String cutPath(String path){
        if(StringUtils.isEmpty(path)){
            return "";
        }
        int ind = path.indexOf("file/");
        if(ind > 0) {
        	return path.substring(ind + 5);
        }else {
        	return path;
        }
    }

    public static Integer fileType(String url){
        int i = url.lastIndexOf(".");
        if (i < 0) {
            return null;
        }
        String suffix = url.substring(i).toLowerCase();
        if (Arrays.asList(".jpg", ".jpeg", ".png", ".bmp").contains(suffix)){
            return 4;
        }
        if (Arrays.asList(".mp4", ".avi", ".rmvb", ".mkv").contains(suffix)){
            return 1;
        }
        if (Arrays.asList(".wav", ".m4a", ".mp3").contains(suffix)){
            return 2;
        }
        return 3;
    }

}
