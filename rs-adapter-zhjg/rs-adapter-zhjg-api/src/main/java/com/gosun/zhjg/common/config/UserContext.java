package com.gosun.zhjg.common.config;


import com.gosun.zhjg.auth.common.util.jwt.IJWTInfo;

/**
 * @ClassName: UserContext
 * @Author: lucifer
 * @Description:
 * @CreateTIme: 2019/12/16 0008 下午 3:43
 **/
public class UserContext {

    private static final ThreadLocal<String> tokenThreadLocal = new ThreadLocal<>();

    private static final ThreadLocal<IJWTInfo> jwtInfoThreadLocal = new ThreadLocal<>();


    public static void putJwtInfo(String token, IJWTInfo info) {
        tokenThreadLocal.set(token);
        jwtInfoThreadLocal.set(info);
    }

    public static String getToken() {
        return tokenThreadLocal.get();
    }

    public static IJWTInfo getJwtInfo() {
        IJWTInfo info = jwtInfoThreadLocal.get();

        if (null == info) {
            throw new RuntimeException("当前session中不具备用户信息!");
        }

        return info;
    }

    public static void remove() {
        tokenThreadLocal.remove();
        jwtInfoThreadLocal.remove();
    }
}
