package com.gosun.zhjg.basic.business.modules.system.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* base_system_info 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2022-12-23
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="BaseSystemInfoSearchDTO对象", description="")
public class BaseSystemInfoSearchDTO extends AbstractPageQueryForm implements Serializable {


   @JsonProperty("id")
   @ApiModelProperty(value = "主键")
   private String id;

   @JsonProperty("systemName")
   @ApiModelProperty(value = "系统名字")
   private String systemName;

   @JsonProperty("systemNameShort")
   @ApiModelProperty(value = "系统名字缩写")
   private String systemNameShort;

   @JsonProperty("onlineDate")
   @ApiModelProperty(value = "系统上线日期")
   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
   private Date onlineDate;

   @JsonProperty("logoLoginUrl")
   @ApiModelProperty(value = "登录页logo图片")
   private String logoLoginUrl;

   @JsonProperty("systemNameUrl")
   @ApiModelProperty(value = "系统名称图片")
   private String systemNameUrl;

   @JsonProperty("versionInfo")
   @ApiModelProperty(value = "版权信息")
   private String versionInfo;

   @JsonProperty("systemInfo")
   @ApiModelProperty(value = "系统说明")
   private String systemInfo;

   @JsonProperty("updateUser")
   @ApiModelProperty(value = "更新人")
   private String updateUser;

   @JsonProperty("updateTime")
   @ApiModelProperty(value = "更新时间")
   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
   private Date updateTime;

}
