package com.gosun.zhjg.prison.room.terminal.modules.socket.vo;

import com.alibaba.fastjson.JSON;
import lombok.Data;

@Data
public class PushMessageAckVO {

    public PushMessageAckVO() {
    }

    public PushMessageAckVO(String sessionId) {
        this.sessionId = sessionId;
    }

    private String sessionId;

    private Boolean ok;

    public Boolean getOk() {
        return this.ok == null ? false : this.ok;
    }

    private Object response;

    /**
     * 获取指定长度 response
     *
     * @param length
     * @return
     */
    public String getResponse(int length) {
        if (response == null) {
            return null;
        }
        String t;
        if (this.response instanceof String) {
            t = (String) this.response;
        } else {
            t = JSON.toJSONString(this.response);
        }
        if (t != null && t.length() > length) {
            t = t.substring(0, length);
        }
        return t;
    }

}
