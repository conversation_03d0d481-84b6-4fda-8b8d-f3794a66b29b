package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 通知，交办状态
 * 
 * <AUTHOR>
 *
 */
public enum PrisonNoticeStatusEnum implements IEnum<Integer> {
	WAIT_SEND(0, "待发送"), SENT(1, "待处理"), FINISH(2, "已完成");

	private Integer value;
	private String desc;

	PrisonNoticeStatusEnum(Integer value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public Integer getValue() {
		return value;
	}

	public void setValue(Integer value) {
		this.value = value;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(Integer value) {
		for (PrisonNoticeStatusEnum e : PrisonNoticeStatusEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}

	public static Integer getValue(String desc) {
		for (PrisonNoticeStatusEnum e : PrisonNoticeStatusEnum.values()) {
			if (e.desc.equals(desc)) {
				return e.value;
			}
		}
		return null;
	}
}