package com.gosun.zhjg.basic.business.modules.police.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 民警信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-05-09 17:13:49
 */
@Data
@ApiModel("民警信息表分页VO")
public class BasePoliceInfoPageVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private String id;

    @ApiModelProperty(value = "民警编号")
    private String policeCode;
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userid;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;
    /**
     * 角色名称
     */
    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "民警图片")
    private String photo;

    @ApiModelProperty(value = "岗位名称")
    private String postName;
//    /**
//     * 警号
//     */
//    @ApiModelProperty(value = "警号")
//    private String policeCode;
//    /**
//     * 所属中队
//     */
//    @ApiModelProperty(value = "所属中队")
//    private String squadronId;
//    /**
//     * 所属监所
//     */
//    @ApiModelProperty(value = "所属监所")
//    private String prisonId;

}
