package com.gosun.zhjg.common.controller;

import com.gosun.zhjg.auth.common.util.jwt.IJWTInfo;
import com.gosun.zhjg.common.config.UserContext;
import com.gosun.zhjg.common.msg.R;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 基础controller
 *
 * <AUTHOR> on 2020/5/12
 * @email <EMAIL>
 * @date 2020/5/12
 */
public class BaseController {


    public IJWTInfo getUser() {
        return UserContext.getJwtInfo();
    }

    @ApiOperation("返回用户信息")
    @GetMapping("/returnUser")
    public R returnUser(){
        return R.ResponseResult(getUser());
    }
}