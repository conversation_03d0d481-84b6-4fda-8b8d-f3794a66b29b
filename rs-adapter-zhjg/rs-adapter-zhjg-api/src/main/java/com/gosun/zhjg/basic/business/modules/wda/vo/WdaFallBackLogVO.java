package com.gosun.zhjg.basic.business.modules.wda.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * @Author: panyusheng
 * @Date: 2021/5/7
 * @Version 1.0
 */
@Data
@ApiModel("微达安回写记录VO")
public class WdaFallBackLogVO {

    /**
     * 主键
     */
    private String id;
    /**
     * 业务名称
     */
    private String businessName;
    /**
     * 回写内容
     */
    private String content;
    /**
     * 回写状态
     */
    private String status;
    /**
     * 标志（1=需要回写/0=无需回写）
     */
    private String flag;
    /**
     * 信息
     */
    private String message;
    /**
     * 业务生产时间
     */
    private Date businessTime;
    /**
     * 数据回写时间
     */
    private Date fallBackTime;
    /**
     * 本地回写数据库
     */
    private String localTableName;
    /**
     * 本地回写数据库主键
     */
    private String localPrimaryKey;
    /**
     * 回写类型（insert/update/delete）
     */
    private String updateType;
    /**
     * 维达安数据库主键
     */
    private String wdaPrimaryKey;

}
