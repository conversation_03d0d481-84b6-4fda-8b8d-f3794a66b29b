package com.gosun.zhjg.basic.business.modules.prisoner.feign;

import com.gosun.zhjg.basic.business.modules.prisoner.vo.BasePrisonerBusStatusTagVO;
import com.gosun.zhjg.common.msg.ResultVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;


@FeignClient(value = "isbs-common-service", contextId = "basic-business-api")
public interface IsbsCommonServiceFeign {


	@RequestMapping(value = "/busstatus/getBusTag", method = RequestMethod.POST)
	ResultVO<List<BasePrisonerBusStatusTagVO>> getBusTag(@RequestHeader("token") String token, @RequestBody String... prisonerIds);
}
