package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto;

import com.gosun.zhjg.prison.room.terminal.modules.terminal.dto.TerminalSystemCallbackArgsDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 设备版本升级日志
 *
 * <AUTHOR>
 * @date 2023/9/7 18:20
 */
@Data
@ApiModel("设备版本升级日志新增Dto")
public class TerminalVersionUpgradeLogSaveDto {

    /**
     * 序列号
     */
    @ApiModelProperty(value = "序列号", required = true)
    private String serialNumber;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments", required = true)
    private String ip;
    /**
     * 之前apk包版本
     */
    @ApiModelProperty(value = "之前apk包版本", required = true)
    private String apkVersion;
    /**
     * 之前web包版本
     */
    @ApiModelProperty(value = "之前web包版本", required = true)
    private String webVersion;
    /**
     * 更新的包类型    apk、web
     */
    @ApiModelProperty(value = "更新的包类型    apk、web", required = true)
    private String upgradeType;
    /**
     * 要更新版本
     */
    @ApiModelProperty(value = "要更新版本", required = true)
    private String upgradeVersion;
    /**
     * 是否更新成功 1、0
     */
    @ApiModelProperty(value = "是否更新成功 1、0", required = true)
    private Integer ok;
    /**
     * 设备的更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "设备的更新时间", required = true)
    private Date upgradeTime;
    /**
     * 失败信息
     */
    private String errMsg;
    /**
     * ${@link TerminalSystemCallbackArgsDto} 升級回调参数
     */
    private String callbackArgs;
    /**
     * 能兼容的web包版本
     */
    private String compatibleWebVersions;
    /**
     * 终端类型
     */
    private String terminalType;
    /**
     * apk更新时间
     */
    private Date apkUpgradeTime;
    /**
     * web更新时间
     */
    private Date webUpgradeTime;
}
