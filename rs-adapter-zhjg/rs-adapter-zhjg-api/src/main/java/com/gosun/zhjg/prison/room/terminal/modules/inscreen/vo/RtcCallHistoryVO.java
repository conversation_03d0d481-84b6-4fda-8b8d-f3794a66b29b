package com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * rtc对讲记录表
 *
 * <AUTHOR>
 * @date 2023/8/10 18:46
 */
@Data
@ApiModel("rtc对讲记录表VO")
public class RtcCallHistoryVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    private String id;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    private String prisonId;
    /**
     * web对讲：webTalk
     */
    @ApiModelProperty(value = "web对讲：webTalk")
    private String callType;
    /**
     * 会议状态 0：未开始 1：进行中 2：已结束 3：已取消
     */
    private Integer meetingState;
    /**
     * 发起呼叫人员
     */
    @ApiModelProperty(value = "发起呼叫人员")
    private Integer callerUserId;
    /**
     * 呼叫方设备序列号
     */
    @ApiModelProperty(value = "呼叫方设备序列号")
    private String callerSerialNumber;
    /**
     * 呼叫方名称
     */
    @ApiModelProperty(value = "呼叫方名称")
    private String callerName;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    private Integer callerRoleId;
    /**
     * 呼叫方角色
     */
    @ApiModelProperty(value = "呼叫方角色")
    private String callerRoleName;
    /**
     * 接受方监室
     */
    @ApiModelProperty(value = "接受方监室")
    private String receiverRoomId;
    /**
     * 接受方设备序列号
     */
    @ApiModelProperty(value = "接受方设备序列号")
    private String receiverSerialNumber;
    /**
     * 接受方名称
     */
    @ApiModelProperty(value = "接受方名称")
    private String receiverName;
    /**
     * 呼叫时间
     */
    @ApiModelProperty(value = "呼叫时间")
    private Date callTime;
    /**
     * 可视化展示，比如： 刚刚 几分钟前 一小时内
     */
    private String callTimeDisplayName;
    /**
     * 呼叫挂断时间
     */
    @ApiModelProperty(value = "呼叫挂断时间")
    private Date callEndTime;

    /**
     * 对讲时长。单位秒
     */
    private Long callDurationSec;

    /**
     * 对讲时长，单位秒
     *
     * @return
     */
    @ApiModelProperty(value = "对讲时长，单位秒")
    public Long getCallDurationSec() {
        if (this.callDurationSec != null) {
            return this.callDurationSec;
        }
        if (callTime == null || callEndTime == null) {
            return null;
        }
        return (callEndTime.getTime() - callTime.getTime()) / 1000;
    }

    @ApiModelProperty(value = "对讲时长，可视化展示")
    private String callDurationSecDisplayName;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    private Long rtcRoomId;
    /**
     * RTC房间名称
     */
    @ApiModelProperty(value = "RTC房间名称")
    private String rtcRoomName;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    private Long rtcMeetingId;
    /**
     * RTC会议名称
     */
    @ApiModelProperty(value = "RTC会议名称")
    private String rtcMeetingName;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    private Date createTime;

    /**
     * 将 61秒转换为  1时1秒
     *
     * @return
     */
    @SuppressWarnings("unused")
    public String getCallDurationSecDisplayName() {
        if (this.callDurationSecDisplayName != null) {
            return this.callDurationSecDisplayName;
        }
        Long talkTimeSec = this.getCallDurationSec();
        if (this.getCallDurationSec() == null) {
            return null;
        }
        long seconds = talkTimeSec;
        long hours = seconds / 3600;
        long minutes = (seconds % 3600) / 60;
        long remainingSeconds = seconds % 60;
        String displayTime = "";
        if (hours > 0) {
            displayTime += hours + "时";
        }
        if (minutes > 0) {
            displayTime += minutes + "分";
        }
        if (remainingSeconds > 0 || displayTime.isEmpty()) {
            displayTime += remainingSeconds + "秒";
        }
        return displayTime;
    }

//    /**
//     * 呼叫时间格式化
//     * 如果是今天仅展示时分秒，否则展示正常，带上面月日
//     */
//    @SuppressWarnings("unused")
//    public String getCallTimeDisplayName() {
//        if (this.callTime == null) {
//            return null;
//        }
//        Date now = new Date();
//        if (this.callTime.getYear() == now.getYear() && this.callTime.getMonth() == now.getMonth() && this.callTime.getDate() == now.getDate()) {
//            return new SimpleDateFormat("HH:mm:ss").format(this.callTime);
//        }
//        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(this.callTime);
//    }
}
