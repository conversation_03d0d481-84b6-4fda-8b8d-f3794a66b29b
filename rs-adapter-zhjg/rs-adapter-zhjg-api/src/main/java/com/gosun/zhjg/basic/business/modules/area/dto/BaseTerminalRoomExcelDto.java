package com.gosun.zhjg.basic.business.modules.area.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 区域表
 */
@Data
@ApiModel("区域ExcelDto")
public class BaseTerminalRoomExcelDto  implements Serializable {

    private static final long serialVersionUID = 6255738183411898983L;

    private final static String header1="监室信息";


    @ExcelProperty({header1,"监室名称*"})
    private String areaName;

    @ExcelProperty({header1,"监室编码*"})
    private String areaCode;

    @ExcelProperty({header1,"父节点ID*"})
    private String parentId;

    @ExcelProperty({header1,"关押量"})
    private String imprisonmentAmount;

    @ExcelProperty({header1,"状态*"})
    private String status;

    @ExcelProperty({header1,"所属中队"})
    private String squadronId;

    @ExcelProperty({header1,"监室类型"})
    private String roomType;

    @ExcelProperty({header1,"性别类型"})
    private String roomSex;

    @ExcelProperty({header1,"监室面积*"})
    private BigDecimal roomArea;

    @ExcelProperty({header1,"人均铺位面积"})
    private BigDecimal avgBedsArea;

    @ExcelProperty({header1,"设计关押量"})
    private Integer planImprisonmentAmount;

    @ExcelProperty({header1,"是否一级风险"})
    private String isLevelRisk;

    @ExcelProperty({header1,"上游监室ID*"})
    private String selfAreaId;

    @ExcelProperty({header1,"风险等级"})
    private String fxdj;

    @ExcelProperty({header1,"预警时间"})
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date yjsj;

}
