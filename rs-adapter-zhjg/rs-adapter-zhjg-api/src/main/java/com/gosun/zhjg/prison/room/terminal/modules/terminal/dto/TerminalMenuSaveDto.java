package com.gosun.zhjg.prison.room.terminal.modules.terminal.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;


/**
 * 终端-仓内外屏菜单表
 *
 * <AUTHOR>
 * @date 2024/12/12 10:59
 */
@Data
@ApiModel("终端-仓内外屏菜单表新增Dto")
public class TerminalMenuSaveDto {

	/**
	 * $column.comments
	 */
	@NotNull(message = "$column.comments 不能为空!")
	@ApiModelProperty(value = "$column.comments", required = true)
	private String id;
	/**
	 * 菜单名称
	 */
	@NotNull(message = "菜单名称 不能为空!")
	@ApiModelProperty(value = "菜单名称", required = true)
	private String menuName;
	/**
	 * 编码
	 */
	@NotNull(message = "编码 不能为空!")
	@ApiModelProperty(value = "编码", required = true)
	private String code;
	/**
	 * 父级节点ID，-1表根节点
	 */
	@NotNull(message = "父级节点ID，-1表根节点 不能为空!")
	@ApiModelProperty(value = "父级节点ID，-1表根节点", required = true)
	private String parentId;
	/**
	 * 节点全路径，前后逗号隔开
	 */
	@NotNull(message = "节点全路径，前后逗号隔开 不能为空!")
	@ApiModelProperty(value = "节点全路径，前后逗号隔开", required = true)
	private String parentNodes;
	/**
	 * 终端类型 CNP、CWP
	 */
	@NotNull(message = "终端类型 CNP、CWP 不能为空!")
	@ApiModelProperty(value = "终端类型 CNP、CWP", required = true)
	private String terminalType;
	/**
	 * 人员类型，区分民警还是被监管人员菜单 POLICE 、PRISONER
	 */
	@NotNull(message = "人员类型，区分民警还是被监管人员菜单 POLICE 、PRISONER 不能为空!")
	@ApiModelProperty(value = "人员类型，区分民警还是被监管人员菜单 POLICE 、PRISONER", required = true)
	private String personnelType;
	/**
	 * 排序字段，从小到大
	 */
	@NotNull(message = "排序字段，从小到大 不能为空!")
	@ApiModelProperty(value = "排序字段，从小到大", required = true)
	private Integer sortOrder;
	/**
	 * $column.comments
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@NotNull(message = "$column.comments 不能为空!")
	@ApiModelProperty(value = "$column.comments", required = true)
	private Date createTime;
	/**
	 * $column.comments
	 */
	@NotNull(message = "$column.comments 不能为空!")
	@ApiModelProperty(value = "$column.comments", required = true)
	private String updateUserId;
	/**
	 * $column.comments
	 */
	@NotNull(message = "$column.comments 不能为空!")
	@ApiModelProperty(value = "$column.comments", required = true)
	private String updateUserName;
	/**
	 * $column.comments
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@NotNull(message = "$column.comments 不能为空!")
	@ApiModelProperty(value = "$column.comments", required = true)
	private Date updateTime;
	/**
	 * $column.comments
	 */
	@NotNull(message = "$column.comments 不能为空!")
	@ApiModelProperty(value = "$column.comments", required = true)
	private Integer delFlag;

}
