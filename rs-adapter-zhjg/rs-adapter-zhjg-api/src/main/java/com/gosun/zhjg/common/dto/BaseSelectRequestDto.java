package com.gosun.zhjg.common.dto;

import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("人员或监室信息通用查询Dto")
public class BaseSelectRequestDto extends AbstractPageQueryForm {

    @ApiModelProperty("模糊查询name")
    private String queryText;

    @ApiModelProperty("模糊查询name。根据监室名或者监区")
    private String areaName;

    @ApiModelProperty("监室编号,多个参数用逗号隔开")
    private String roomId;

    @ApiModelProperty("区域编号,多个参数用逗号隔开")
    private String areaId;

    @ApiModelProperty("监所id")
    private String prisonId;

    @ApiModelProperty("监所ids,不需要传")
    private String[] prisonIds;

    @ApiModelProperty("中队编号")
    private String squadronId;

    @ApiModelProperty("民警类型")
    private Integer userType;
}
