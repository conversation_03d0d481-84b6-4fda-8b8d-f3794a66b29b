package com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo;

import com.gosun.zhjg.prison.room.terminal.modules.home.dto.BaseSystemRuleConfigKeyValueDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CwpInfoVO {

    /**
     * 监所名称
     */
    @ApiModelProperty(value = "监所名称")
    private String prisonName;
    /**
     * 监所编码
     */
    @ApiModelProperty(value = "监所编码")
    private String prisonCode;

    List<CwpDeviceInfoVO> list;

    @ApiModelProperty(value = "app编码")
    private String appCode;

    @ApiModelProperty(value = "系统配置")
    private List<BaseSystemRuleConfigKeyValueDto> ruleConfigList;
}
