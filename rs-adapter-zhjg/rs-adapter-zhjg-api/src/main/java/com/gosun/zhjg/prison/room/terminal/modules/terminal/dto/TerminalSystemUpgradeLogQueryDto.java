package com.gosun.zhjg.prison.room.terminal.modules.terminal.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel("智能终端管理系统-更新台账查询")
public class TerminalSystemUpgradeLogQueryDto extends AbstractPageQueryForm {

	@ApiModelProperty(value = "终端类型", required = false)
	private String terminalType;

	@ApiModelProperty(value = "更新包类型", required = false)
	private String packageType;

	@ApiModelProperty(value = "更新包名", required = false)
	private String packageName;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新时间-开始", required = false)
	private Date upgradeTimeStart;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新时间-结束", required = false)
	private Date upgradeTimeEnd;

	@ApiModelProperty(value = "acp_pm_terminal_version_upgrade_log_batch表id", required = false)
	private String batchId;

	@ApiModelProperty(value = "序列号", required = false)
	private String serialNumber;

	@ApiModelProperty(value = "设备名称", required = false)
	private String deviceName;

	@ApiModelProperty(value = "设备区域", required = false)
	private String areaName;

	@ApiModelProperty(value = "更新版本号", required = false)
	private String upgradeVersion;
	/**
	 * 是否更新成功 1成功、0失败、2进行中
	 */
	private Integer ok;

	private String ip;
}
