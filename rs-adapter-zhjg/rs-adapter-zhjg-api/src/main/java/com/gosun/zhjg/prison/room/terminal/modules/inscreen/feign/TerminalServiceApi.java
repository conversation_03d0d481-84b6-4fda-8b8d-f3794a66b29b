package com.gosun.zhjg.prison.room.terminal.modules.inscreen.feign;

import com.alibaba.fastjson.JSONObject;
import com.gosun.zhjg.common.msg.R;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

@Component
@FeignClient(value = "zhjg-prison-room-terminal", path = "inscreen/terminalservice")
public interface TerminalServiceApi {

    @RequestMapping(value = "/getConfigInfo", method = RequestMethod.GET)
    @ApiOperation(value = "获取配置信息异常")
    public R<?> getConfigInfo(@RequestParam(value = "ip") String ip);

    @RequestMapping(value = "/reStartApp", method = RequestMethod.GET)
    @ApiOperation(value = "重启")
    public R<?> reStartApp(@RequestParam(value = "ip") String ip);

    @PostMapping("saveConfigInfo")
    @ApiOperation(value = "保存配置", notes = "json中需加ip")
    public R saveConfigInfo(@RequestBody JSONObject object);
}
