package com.gosun.zhjg.basic.business.modules.prisoner.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/3 17:33
 */
@Data
@ApiModel("监室在押人员信息")
public class RoomPrisonerVO {

    @ApiModelProperty("编号")
    private String prisonerId;

    @ApiModelProperty("监室id")
    private String roomId;

    @ApiModelProperty("监室name")
    private String roomName;

    @ApiModelProperty("单位代码")
    private String prison;

//    @ApiModelProperty("关押单位")
//    private String prisonName;

    @ApiModelProperty("姓名")
    private String prisonerName;

    @ApiModelProperty("1 :重病号")
    private Integer sickType;

    @ApiModelProperty("1 :高风险")
    private Integer riskType;

    @ApiModelProperty("1: 紧急风险")
    private Integer urgentRiskType;

    @ApiModelProperty("1: 重点关注")
    private Integer importantType;

    @ApiModelProperty("在押人员正面照片")
    private String frontPhoto;

    @ApiModelProperty("风险等级")
    private String riskLevel;

    @ApiModelProperty("风险等级 字典code")
    private String riskLevelCode;

    @ApiModelProperty("性别")
    private String sex;

    @ApiModelProperty("1:死刑人员标签")
    private Integer deathTag;

    @ApiModelProperty("证件号码")
    private String zjhm;
}
