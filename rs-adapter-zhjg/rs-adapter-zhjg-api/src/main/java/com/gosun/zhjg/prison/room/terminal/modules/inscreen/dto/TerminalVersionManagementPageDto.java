package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 终端版本管理
 *
 * <AUTHOR>
 * @date 2023/8/18 17:22
 */
@Data
@ApiModel("终端版本管理分页Dto")
public class TerminalVersionManagementPageDto extends AbstractPageQueryForm {

    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments", required = true)
    private String id;
    /**
     * 包名
     */
    @ApiModelProperty(value = "包名", required = true)
    private String packageName;
    /**
     * 软件包种类，区分包。固定前缀
     */
    @ApiModelProperty(value = "软件包种类，区分包。固定前缀", required = true)
    private String fixed;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号", required = true)
    private String versionNumber;
    /**
     * 地市缩写
     */
    @ApiModelProperty(value = "地市缩写", required = true)
    private String cityAbbr;
    /**
     * 系统缩写
     */
    @ApiModelProperty(value = "系统缩写", required = true)
    private String sysAbbr;
    /**
     * 机器缩写
     */
    @ApiModelProperty(value = "机器缩写", required = true)
    private String machineAbbr;
    /**
     * 兼容版本
     */
    @ApiModelProperty(value = "兼容版本", required = true)
    private String competingVersions;
    /**
     * 版本说明
     */
    @ApiModelProperty(value = "版本说明", required = true)
    private String releaseNotes;
    /**
     * 软件包状态：0缺失、1存在
     */
    @ApiModelProperty(value = "软件包状态：0缺失、1存在", required = true)
    private Integer state;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 主版本号
     */
    private Integer majorVersion;
    /**
     * 次版本号
     */
    private Integer minorVersion;
    /**
     * 修补版本号
     */
    private Integer patchVersion;
    /**
     * "^" 升级次版本号和修订号
     * "~" 只升级修订号
     * ">" 必须大于某个版本号
     * "<" 必须小于某个版本号
     */
    private String versionMatchRule;

    @ApiModelProperty(value = "是否分页 1 / 0")
    private Boolean page;
    /**
     * 是否根据版本号排序
     */
    @JsonIgnore
    private Boolean orderByVersion;
	/**
	 * 终端类型,字典：TERMINAL_SYSTEM_TERMINAL_TYPE
	 */
	private String terminalType;
	/**
	 * 更新的包类型（upgrade_type） apk、web 字典：TERMINAL_SYSTEM_PACKAGE_TYPE
	 */
	private String packageType;
}
