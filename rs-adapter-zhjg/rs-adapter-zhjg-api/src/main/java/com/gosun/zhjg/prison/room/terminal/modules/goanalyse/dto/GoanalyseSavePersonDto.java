package com.gosun.zhjg.prison.room.terminal.modules.goanalyse.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.List;

@Data
@ApiModel("研究院客户端人脸-新增")
public class GoanalyseSavePersonDto {

    @ApiModelProperty(value = "终端ip集合",required = true)
    private List<String> ips;

    @ApiModelProperty(value = "关联实战平台编号：在押人员编号，民警编号，外来人员唯一编号等",required = true)
    private String faceCode;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty(value = "在押人员1，民警2.外来人员3",required = true)
    private Integer groupId;

    @ApiModelProperty("base64 图片数据")
    private String base64;

    @ApiModelProperty("创建人")
    private String createUserName;

    @ApiModelProperty("数据源")
    private Integer dataSource;

    @ApiModelProperty("有效开始时间")
    private String effectiveStartTime;

    @ApiModelProperty("有效结束时间")
    private String effectiveEndTime;

    @ApiModelProperty("操作类型：1外来人员信息推送")
    private Integer operationType;

}
