package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * rtc对讲记录表
 *
 * <AUTHOR>
 * @date 2023/8/10 18:47
 */
@Data
@ApiModel("rtc对讲记录表分页Dto")
public class RtcCallHistoryPageDto extends AbstractPageQueryForm {
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments", required = false)
    private String prisonId;
    /**
     * web对讲：webTalk
     */
    @ApiModelProperty(value = "web对讲：webTalk", required = false)
    private String callType;
    /**
     * 会议状态 0：未开始 1：进行中 2：已结束 3：已取消
     */
    private Integer meetingState;
    /**
     * 发起呼叫人员
     */
    @ApiModelProperty(value = "发起呼叫人员", required = false)
    private Integer callerUserId;
    /**
     * 呼叫方设备序列号
     */
    @ApiModelProperty(value = "呼叫方设备序列号", required = false)
    private String callerSerialNumber;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments", required = false)
    private Integer callerRoleId;
    /**
     * 接受方监室
     */
    @ApiModelProperty(value = "接受方监室", required = false)
    private String receiverRoomId;
    /**
     * 接受方设备序列号
     */
    @ApiModelProperty(value = "接受方设备序列号", required = false)
    private String receiverSerialNumber;
    /**
     * 呼叫时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "呼叫开始时间", required = false)
    private Date startTime;
    /**
     * 呼叫挂断时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "呼叫结束时间", required = false)
    private Date endTime;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments", required = false)
    private Long rtcRoomId;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments", required = false)
    private Long rtcMeetingId;

}
