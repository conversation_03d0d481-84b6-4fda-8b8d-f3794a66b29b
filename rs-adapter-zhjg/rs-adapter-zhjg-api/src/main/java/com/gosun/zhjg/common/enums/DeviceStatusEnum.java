package com.gosun.zhjg.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 设备在线状态
 *
 * <AUTHOR>
 * @date 2023/11/16 20:47
 */
public enum DeviceStatusEnum {
    ONLINE("001", "在线"),
    OFF_LINE("002", "离线"),
    DAMAGED("003", "故障"),
    ;

    private String value;
    private String desc;

    DeviceStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    @JsonValue
    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value) {
        for (DeviceStatusEnum e : DeviceStatusEnum.values()) {
            if (e.value.equals(value)) {
                return e.desc;
            }
        }
        return null;
    }
}
