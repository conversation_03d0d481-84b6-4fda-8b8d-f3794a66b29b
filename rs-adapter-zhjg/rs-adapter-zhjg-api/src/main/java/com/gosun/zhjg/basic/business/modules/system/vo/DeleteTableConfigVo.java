package com.gosun.zhjg.basic.business.modules.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 定时删除表数据配置(DeleteTableConfig)Vo
 *
 * <AUTHOR>
 * @since 2023-06-19 09:31:17
 */
@Data
@ApiModel("定时删除表数据配置vo")
public class DeleteTableConfigVo  implements Serializable {
    private static final long serialVersionUID = -66238894774903775L;

        @ApiModelProperty("主键")
        private String id;

        @ApiModelProperty("标题")
        private String title;

        @ApiModelProperty("sql")
        private String sql;

        @ApiModelProperty("备注")
        private String remarks;

        @ApiModelProperty("创建时间")
        private String creatTime;

        @ApiModelProperty("删除标记")
        private Integer delFlag;


}
