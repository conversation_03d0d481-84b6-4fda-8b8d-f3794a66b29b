package com.gosun.zhjg.basic.business.modules.role.dto;

import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 角色监所分组表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-05-20 10:00:10
 */
@Data
@ApiModel("角色监所分组表分页Dto")
public class PermissionRolePrisonPageDto extends AbstractPageQueryForm {

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments", required = true)
	private String id;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments", required = true)
	private Integer roleId;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments", required = true)
	private String dwdm;

}
