package com.gosun.zhjg.prison.room.terminal.modules.goanalyse.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("终端人脸查询")
public class GoanalysePersonListDto {

    @ApiModelProperty("终端ip")
    private String ip;

    @ApiModelProperty("人员编号")
    private String faceCode;

    @ApiModelProperty("在押人员1，民警2.外开人员3")
    private Integer groupId;

    @ApiModelProperty("是否分页,1不分页")
    private Integer isPage;

    @ApiModelProperty("页码")
    private Integer curPage;

    @ApiModelProperty("页大小")
    private Integer pageSize;
}
