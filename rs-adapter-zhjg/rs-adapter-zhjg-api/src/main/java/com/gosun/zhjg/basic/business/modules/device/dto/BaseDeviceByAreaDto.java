package com.gosun.zhjg.basic.business.modules.device.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 楼层分布 - 设备dto
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-10-19 11:40:50
 */
@Data
@ApiModel("楼层分布-设备dto")
public class BaseDeviceByAreaDto {

    /**
     * 所属区域
     */
    @ApiModelProperty(value = "所属区域")
    private String areaId;

}
