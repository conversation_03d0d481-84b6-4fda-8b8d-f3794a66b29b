package com.gosun.zhjg.basic.business.modules.menu.dto;

import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 系统菜单分组表
 *
 * <AUTHOR>
 *
 */
@Data
@ApiModel("系统菜单分组表分页Dto")
public class BaseMenuGroupPageDto extends AbstractPageQueryForm {

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments", required = false)
	private String id;
	/**
	 * 分组名
	 */
	@ApiModelProperty(value = "分组名", required = false)
	private String groupName;
	/**
	 * 编码
	 */
	@ApiModelProperty(value = "编码", required = false)
	private String code;
	/**
	 * 排序大靠前，默认100
	 */
	@ApiModelProperty(value = "排序大靠前，默认100", required = false)
	private Integer sort;
	/**
	 * 是否启用 1 / 0
	 */
	@ApiModelProperty(value = "是否启用 1 / 0", required = false)
	private Integer enable;

	@ApiModelProperty(value = "角色ID", required = false)
	private Integer roleId;
}
