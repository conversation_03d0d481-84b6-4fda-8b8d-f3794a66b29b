package com.gosun.zhjg.prison.room.terminal.modules.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 仓内屏图片表
 */
@Data
@TableName("acp_pm_cnp_screen_candid")
public class ScreenCandidEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@TableId
	private String id;
	/**
	 * 人员编号
	 */
	private String rybh;
	/**
	 * 文件名称
	 */
	private String fileName;
	/**
	 * 文件地址
	 */
	private String filePath;
	/**
	 * 是否人脸录入的图片(1:录入的人脸图片,2:抓拍的图片)
	 */
	private Integer faceFlag;
	/**
	 * 删除标识
	 */
	@TableField(value = "IS_DEL", fill = FieldFill.INSERT)
	@TableLogic
	private Integer isDel;

	private Date updateTime;
	/**
	 * 人员类型
	 */
	private String personType;

	/**
	 * 序列号
	 */
	private String serialNum;

	/**
	 * 区分 照片来自 摄像头录入还是 照片录入 origin=camera 摄像头
	 */
	@TableField(exist = false)
	private String origin;
}
