package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 字典 C_XB
 * 
 * <AUTHOR>
 *
 */
public enum GenderEnum implements IEnum<String> {

	UNKNOWN("0", "未知"), //
	MAN("1", "男"), //男性
	WOMAN("2", "女"), //女性
	WOMANCHANGETOMAN("5", "女性改（变）为男性"), //
	MANCHANGETOWOMAN("6", "男性改（变）为女性"), //
	UNEXPLAIN("9", "未说明的性别");//

	private String value;
	private String desc;

	GenderEnum(String value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(String value) {
		for (GenderEnum e : GenderEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}
	
	public static String getValue(String desc) {
		for (GenderEnum e : GenderEnum.values()) {
			if (e.getDesc().equals(desc)) {
				return e.value;
			}
		}
		return null;
	}

}