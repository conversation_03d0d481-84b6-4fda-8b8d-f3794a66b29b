package com.gosun.zhjg.prison.room.terminal.modules.terminal.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 终端签名确认
 *
 * <AUTHOR>
 * @date 2024/4/12 11:37
 */
@Data
@ApiModel("终端签名确认新增VO")
public class TerminalSignatureConfirmVO {
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments", required = true)
	private String id;
	/**
	 * 下发签名给人员编号
	 */
	@NotNull(message = "下发签名给人员编号 不能为空!")
	@ApiModelProperty(value = "下发签名给人员编号", required = true)
	private String prisonerId;
	/**
	 * 业务类型 healthCheck 入所健康检、
	 * ${@link com.gosun.zhjg.common.enums.TerminalSignatureConfirmBusinessTypeEnum}
	 */
	@NotNull(message = "业务类型 healthCheck 入所健康检、 不能为空!")
	@ApiModelProperty(value = "业务类型 healthCheck 入所健康检、", required = true)
	private String businessType;
	/**
	 * 关联业务主键
	 */
	@NotNull(message = "关联业务主键 不能为空!")
	@ApiModelProperty(value = "关联业务主键", required = true)
	private String linkId;
	/**
	 * 是否已签名 1已签名、0未
	 */
	@ApiModelProperty(value = "是否已签名 1已签名、0未", required = true)
	private Integer state;
	/**
	 * 确认时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
	@NotNull(message = "确认时间 不能为空!")
	@ApiModelProperty(value = "确认时间", required = true)
	private Date confirmTime;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
	private Date createTime;
	/**
	 * 签名图片地址
	 */
	@NotNull(message = "签名图片地址 不能为空!")
	@ApiModelProperty(value = "签名图片地址", required = true)
	private String signatureUrl;

	/**
	 * 人员签名后平台第一次查阅或收到通知时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
	private Date signatureReviewedAt;
}
