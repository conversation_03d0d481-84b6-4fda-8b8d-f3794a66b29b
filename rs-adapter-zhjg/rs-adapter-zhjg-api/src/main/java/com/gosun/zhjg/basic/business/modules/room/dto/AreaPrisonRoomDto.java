package com.gosun.zhjg.basic.business.modules.room.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;


@Data
@ApiModel("监室dto")
public class AreaPrisonRoomDto {

    @ApiModelProperty(value = "监室ID", required = false)
    private String id;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("是否启用:1启用,0停用")
    private String status;

    @ApiModelProperty("是否启用:1启用,0停用")
    private String statusDisplayName;

    @ApiModelProperty("关押量")
    private Integer imprisonmentAmount;

    @ApiModelProperty("设计关押量")
    private Integer planImprisonmentAmount;

    @ApiModelProperty("所属中队")
    private String squadronId;

    @ApiModelProperty("所属中队")
    private String squadronIdDisplayName;

    @ApiModelProperty("监区id")
    private String areaId;

    @ApiModelProperty("监区名称")
    private String areaName;

    @ApiModelProperty("排序")
    private Integer orderId;

    @ApiModelProperty("监室编号")
    private String roomCode;

    @ApiModelProperty("监室类型")
    private String roomType;

    @ApiModelProperty("性别")
    private String roomSex;

    @ApiModelProperty("性别")
    private String roomSexDisplayName;

    @ApiModelProperty("所属监所")
    private String prisonId;

    @ApiModelProperty("监室面积")
    private BigDecimal roomArea;

    @ApiModelProperty("人均铺位面积")
    private BigDecimal avgBedsArea;

    @ApiModelProperty("是否一级风险,1:是，0：否")
    private String isLevelRisk;

    @ApiModelProperty("是否一级风险,1:是，0：否")
    private String isLevelRiskDisplayName;

    @ApiModelProperty("本身区域id")
    private String selfAreaId;

    @ApiModelProperty("风险等级")
    private String fxdj;

    @ApiModelProperty("预警时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date yjsj;
}
