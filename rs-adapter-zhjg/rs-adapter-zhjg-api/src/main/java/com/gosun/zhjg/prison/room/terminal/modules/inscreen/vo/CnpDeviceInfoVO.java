package com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo;

import com.gosun.zhjg.prison.room.terminal.modules.home.dto.BaseSystemRuleConfigKeyValueDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CnpDeviceInfoVO {
	/**
	 * 主键
	 */
	@ApiModelProperty(value = "主键")
	private String id;
	/**
	 * 序列号
	 */
	@ApiModelProperty(value = "序列号")
	private String serialNumber;
	/**
	 * 设备名称
	 */
	@ApiModelProperty("设备名称")
	private String deviceName;
	/**
	 * 监所
	 */
	@ApiModelProperty(value = "监所")
	private String prisonId;

	@ApiModelProperty(value = "监所名称")
	private String prisonName;
	/**
	 * 监室号
	 */
	@ApiModelProperty(value = "监室号")
	private String roomId;
	/**
	 * 监室名称
	 */
	@ApiModelProperty(value = "监室名称")
	private String roomName;

	@ApiModelProperty(value = "系统配置")
	private List<BaseSystemRuleConfigKeyValueDto> ruleConfigList;

	@ApiModelProperty(value = "app编码")
	private String appCode;
}
