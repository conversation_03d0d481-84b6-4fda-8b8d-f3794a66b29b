package com.gosun.zhjg.common.constant;

/**
 * 字典常量接口
 */
public interface DictionaryConstants extends WdaDictionaryConstants {

	/**
	 * 学历
	 */
	String BASE_EDUCATION = "BASE_EDUCATION";
	/**
	 * 学位
	 */
	String BASE_DEGREE = "BASE_DEGREE";
	/**
	 * 政治面貌
	 */
	String BASE_POLITICAL_STATUS = "BASE_POLITICAL_STATUS";
	/**
	 * 警衔
	 */
	String BASE_POLICERANK = "BASE_POLICERANK";
	/**
	 * 监室调整-智能推荐忽略列表（划分监所）
	 */
	String ROOM_CHANGE_AUTO_IGNORE = "ROOM-CHANGE-AUTO-IGNORE";
	/**
	 * 所长名称集合字典
	 */
	String PRISON_LEADER_NAME = "PRISON_LEADER_NAME";

	String DICTSYSTEM="C_ZDSSXY";
	/**
	 * 菜单类型
	 */
	String BASE_MENU_TYPE = "BASE_MENU_TYPE";
	/**
	 * 菜单打开方式
	 */
	String BASE_MENU_OPEN_MODE = "BASE_MENU_OPEN_MODE";
	/**
	 * 角色管理-角色首页。permission_role表home_page_dict code映射的首页名称
	 */
	String ROLE_HOME_PAGE_NAME = "ROLE_HOME_PAGE_NAME";
	/**
	 * 角色管理-角色首页。permission_role表home_page_dict code映射的首页地址
	 */
	String ROLE_HOME_PAGE_URL = "ROLE_HOME_PAGE_URL";
	/**
	 * 功能管理-菜单分类
	 */
	String BASE_MENU_CLASSIFICATION = "BASE_MENU_CLASSIFICATION";
	/**
	 * 区域类型编码
	 */
	String AREA_TYPE="C_QYLXDM";

	/**
	 * 是否启用
	 */
	String IS_ENABLE ="C_SFQY";


	String IS_ENABLE_QY="1";

	String IS_ENABLE_TY="0";

	/**
	 * 海康事件
	 */
	String HK_EVENT_TYPES = "HK_EVENT_TYPES";

	/**
	 * 风险研判风险等级
	 */
	String RISK_LEVEL = "RISK_LEVEL";

	/**
	 * 考核状态
	 */
	String APPRAISAL_STATUS="APPRAISAL_STATUS";

	/**
	 * 通知公告发布类型
	 */
	String PUBLISHING_TYPE="PUBLISHING_TYPE";
	/**
	 * 图书管理借阅状态
	 */
	String BOOK_BORROWING_STATUS="BOOK_BORROWING_STATUS";

	/**
	 * 图书管理借阅处理状态
	 */
	String BORROWING_HANDLE_STATUS="BORROWING_HANDLE_STATUS";
	/**
	 * 报警联动设置-联动配置项
	 */
	String PRISON_EVENT_SETTING_ITEM="PRISON_EVENT_SETTING_ITEM";

	/**
	 * 购物-审批状态
	 * 废弃，变更为 TERMINAL_ORDERS_AUDIT_OPINION
	 */
	@Deprecated
	String SHOPPING_AUDIT_STATE = "SHOPPING_AUDIT_STATE";
	/**
	 * 订单审核意见。terminal_orders 表 audit_state 、 agent_audit_state
	 */
	String TERMINAL_ORDERS_AUDIT_OPINION = "TERMINAL_ORDERS_AUDIT_OPINION";
	/**
	 * 消费类型 0购物消费 默认。1预购消费 无财务账号线下购物
	 */
	String TERMINAL_SHOPPING_TYPE = "TERMINAL_SHOPPING_TYPE";
	/**
	 * 购物管理-预购限额类型 1总额、2每周、3每月、4每季度。字典 TERMINAL_CYCLE_LIMIT_TYPE
	 */
	String TERMINAL_CYCLE_LIMIT_TYPE = "TERMINAL_CYCLE_LIMIT_TYPE";
	/**
	 * 购物-物品状态
	 */
	String SHOPPING_GOODS_STATUS = "SHOPPING_GOODS_STATUS";
	/**
	 * 购物-系统模式数据来源
	 */
	String SHOPPING_SYSTEM_MODE = "SHOPPING_SYSTEM_MODE";
	/**
	 * 购物-供应商
	 */
	String SHOPPING_GOODS_PROVIDER = "SHOPPING_GOODS_PROVIDER";
	/**
	 * 监管综合数据总览-数据类型字典
	 */
	String AGGREGATE_DATA_GROUPS = "AGGREGATE_DATA_GROUPS";
	/**
	 * 智能监督预警-预警等级
	 */
	String INTELLIGENT_WARN_MODEL_LEVEL = "INTELLIGENT_WARN_MODEL_LEVEL";
	/**
	 * 智能监督预警-比较符号
	 */
	String INTELLIGENT_WARN_COMPARISON = "INTELLIGENT_WARN_COMPARISON";
	/**
	 * 智能监督预警-时间单位
	 */
	String INTELLIGENT_WARN_TIME_OPTION = "INTELLIGENT_WARN_TIME_OPTION";
	/**
	 * 智能监督预警-预警状态
	 */
	String INTELLIGENT_WARN_RECORDS_STATE = "INTELLIGENT_WARN_RECORDS_STATE";

	/**
	 * IP替换配置
	 */
	String IP_REPLACE_CONF="IP_REPLACE_CONF";
	/**
	 * 智能终端管理系统-终端类型
	 */
	String TERMINAL_SYSTEM_TERMINAL_TYPE = "TERMINAL_SYSTEM_TERMINAL_TYPE";
	/**
	 * 智能终端管理系统-包类型
	 */
	String TERMINAL_SYSTEM_PACKAGE_TYPE = "TERMINAL_SYSTEM_PACKAGE_TYPE";
	/**
	 * 商品属性。terminal_goods 表
	 */
	String TERMINAL_GOODS_ATTR = "TERMINAL_GOODS_ATTR";
	/**
	 * 购物状态。terminal_prisoner_shopping_limit 表
	 */
	String TERMINAL_PRISONER_SHOPPING_STATE = "TERMINAL_PRISONER_SHOPPING_STATE";
	/**
	 * 订单状态。terminal_orders 表
	 */
	String TERMINAL_ORDERS_STATE = "TERMINAL_ORDERS_STATE";
	/**
	 * 监室等级
	 */
	String ROOM_LEVEL = "ROOM_LEVEL";
	/**
	 * 购物反馈类型字典
	 */
	String SHOPPING_FEEDBACK_TYPE="SHOPPING_FEEDBACK_TYPE";



	/**
	 * 武警终端人员类型
	 */
	String C_WJZDRYLX="C_WJZDRYLX";

	/**
	 * 用户管理其他人员类型
	 */
	String OTHER_USER_TYPE="OTHER_USER_TYPE";

	/***
	 * 非涉案物品-物品状态
	 */
	String WPZT = "WPZT";

	/***
	 * 非涉案物品-物品来源
	 */
	String WPLY = "WPLY";

	/**
	 * 武警终端出入台账状态
	 */
	String C_WJCRTZZT="C_WJCRTZZT";

	/***
	 * 视频巡查状态表
	 */
	String C_SPXC_ZT = "C_SPXC_ZT";

	/**
	 * 所情关联业务类型
	 */
	String PRISON_EVENT_BUSINESS_TYPE="PRISON_EVENT_BUSINESS_TYPE";

	/**
	 * 风险评估-评估原因
	 * 2025年2月21日 新增
	 */
	String RISK_ASSESSMENT_CAUSE = "RISK_ASSESSMENT_CAUSE";

	/**
	 * 关注人员信息表-推荐的待关注人员-推荐原因
	 * attention_personnel_suggestion 表
	 */
	String ATTENTION_SUGGESTION_REASON = "ATTENTION_SUGGESTION_REASON";

	/**
	 * 带入带出业务-事由、业务类型。字典：PRISONER_OUT_IN_REASON。枚举：CwpPrisonerOutInOperationTypeEnum
	 * cwp_prisoner_out_in 表
	 */
	String PRISONER_OUT_IN_REASON = "PRISONER_OUT_IN_REASON";

	/***
	 * 窗口-自助登记审核状态
	 */
	String WINDOW_APPROVAL_RESULT = "WINDOW_APPROVAL_RESULT";
}
