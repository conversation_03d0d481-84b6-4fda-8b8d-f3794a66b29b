package com.gosun.zhjg.basic.business.modules.area.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * prison_regional_control-区域防控表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-11-27 17:04:46
 */
@Data
@ApiModel("区域防控表统计VO")
public class PrisonRegionalControlVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    private String areaName;
    /**
     * 区域类型
     */
    @ApiModelProperty(value = "区域类型")
    private String areaType;
    /**
     * 所属监所
     */
    @ApiModelProperty(value = "所属监所")
    private String prisonId;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer orderId;
    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码")
    private String areaCode;

    /**
     * 父节点ID
     */
    @ApiModelProperty(value = "父节点ID")
    private String parentId;
    /**
     * 预警数量
     */
    @ApiModelProperty(value = "预警数量")
    private Integer warningNum;
}
