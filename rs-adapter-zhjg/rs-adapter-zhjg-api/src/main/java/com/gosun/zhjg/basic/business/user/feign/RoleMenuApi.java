package com.gosun.zhjg.basic.business.user.feign;


import com.gosun.zhjg.common.msg.ResultVO;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@Component
@FeignClient(value = "zhjg-basic-business", path = "user/permission/usermenu", contextId = "user/permission/usermenu")
public interface RoleMenuApi {

    @RequestMapping(value = "/getRoleMenuByCode/{roleId}/{code}", method = RequestMethod.GET)
    @ApiOperation(value = "判断该用户角色是否存在相应角色编码")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "roleId", name = "roleId", value = "角色id", required = true, dataType = "int"),
            @ApiImplicitParam(paramType = "code", name = "code", value = "权限编码", required = true, dataType = "String")
    })
    ResultVO<Boolean> getRoleMenuByCode(@PathVariable("roleId") Integer roleId, @PathVariable("code") String code);
}