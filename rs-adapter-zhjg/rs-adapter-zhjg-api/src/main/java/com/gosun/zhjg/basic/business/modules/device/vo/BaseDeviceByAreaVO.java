package com.gosun.zhjg.basic.business.modules.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-10-19 11:40:50
 */
@Data
@ApiModel("设备表分页VO")
public class BaseDeviceByAreaVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String deviceName;
    /**
     * 设备数量
     */
    @ApiModelProperty(value = "设备数量")
    private String deviceNum;
    /**
     * 设备启用数量
     */
    @ApiModelProperty(value = "设备启用数量")
    private String deviceOnNum;
    /**
     * 设备类型
     */
    @ApiModelProperty(value = "设备类型")
    private String deviceTypeId;

}
