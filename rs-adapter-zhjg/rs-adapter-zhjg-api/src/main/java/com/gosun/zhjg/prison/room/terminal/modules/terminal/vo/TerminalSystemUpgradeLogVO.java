package com.gosun.zhjg.prison.room.terminal.modules.terminal.vo;

import com.gosun.zhjg.common.context.PageSortMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("智能终端管理系统-更新台账查询")
public class TerminalSystemUpgradeLogVO extends TerminalSystemUpgradeLogBatchVO {

	private String batchId;

	private String deviceName;

	private String areaName;

	private String serialNumber;
	/**
	 * 是否更新成功 1成功、0失败、2进行中
	 */
	private Integer ok;
	@PageSortMapping(value = "ok")
	private String okDisplayName;

	public String getOkDisplayName() {
		if (okDisplayName != null) {
			return okDisplayName;
		}
		if (ok != null) {
			switch (ok) {
				case 0:
					return "失败";
				case 1:
					return "成功";
				case 2:
					return "进行中";
				default:
					return ok + "";
			}
		}
		return null;
	}

	private String errMsg;

	private String apkVersion;
	private String webVersion;

	@ApiModelProperty(value = "之前的版本", required = false)
	private String previousVersion;

	public String getPreviousVersion() {
		if (previousVersion != null) {
			return previousVersion;
		}
		String tPackageType = getPackageType();
		if (tPackageType == null) {
			return null;
		}
		switch (tPackageType) {
			case "apk":
				return apkVersion;
			case "web":
				return webVersion;
		}
		return null;
	}

	private String ip;
}
