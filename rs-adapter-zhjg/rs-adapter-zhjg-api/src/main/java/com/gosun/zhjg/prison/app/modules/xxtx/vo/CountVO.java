package com.gosun.zhjg.prison.app.modules.xxtx.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("业务代办/已办统计")
public class CountVO {

    @ApiModelProperty("紧急风险待办/已经")
    private Integer num1;

    @ApiModelProperty("预警待办/已经")
    private Integer num2;

    @ApiModelProperty("所情待办/已经")
    private Integer num3;

    @ApiModelProperty("督导单待办/已经")
    private Integer num4;
}
