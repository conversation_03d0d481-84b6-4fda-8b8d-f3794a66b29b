package com.gosun.zhjg.common.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.gosun.zhjg.common.dto.ExcelSheetDto;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * easyExcel导入导出
 *
 * <AUTHOR>
 */
@Slf4j
public class ExcelUtil {


    /**
     * excel导出，返回二进制流
     *
     * @param data
     * @param sheetName
     * @param clazz
     * @throws Exception
     */
    public static ByteArrayOutputStream writeExcel(List<? extends Object> data, String sheetName, Class clazz) throws Exception {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        // 表头样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 设置表头居中对齐
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 设置内容靠左对齐
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        EasyExcel.write(byteArrayOutputStream, clazz).excelType(ExcelTypeEnum.XLSX).sheet(sheetName).registerWriteHandler(horizontalCellStyleStrategy).doWrite(data);
        return byteArrayOutputStream;
    }

    /**
     * excel导出
     *
     * @param data
     * @param sheetName
     * @param head      表头投
     * @return
     */
    public static ByteArrayOutputStream writeExcel(List<? extends Object> data, String sheetName, List<List<String>> head) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        // 表头样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 设置表头居中对齐
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 设置内容靠左对齐
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        EasyExcel.write(byteArrayOutputStream).head(head).excelType(ExcelTypeEnum.XLSX).sheet(sheetName).registerWriteHandler(horizontalCellStyleStrategy).doWrite(data);
        return byteArrayOutputStream;
    }

    /**
     * 输出多个sheet
     *
     * @param dtoList
     * @return
     */
    public static ByteArrayOutputStream writeExcelSheets(List<ExcelSheetDto> dtoList) {

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ExcelWriter excelWriter = EasyExcel.write(byteArrayOutputStream).build();
        // 表头样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 设置表头居中对齐
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 设置内容居中
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        for (int i = 0; i < dtoList.size(); i++) {
            // 创建 ExcelWriter 对象
            WriteSheet sheet = EasyExcel.writerSheet(i, dtoList.get(i).getSheetName()).head(dtoList.get(i).getClazz()).registerWriteHandler(horizontalCellStyleStrategy).build();
            excelWriter.write(dtoList.get(i).getData(), sheet);
        }
        excelWriter.finish();
        return byteArrayOutputStream;
    }

    /**
     * 解析监听器， 每解析一行会回调invoke()方法。 整个excel解析结束会执行doAfterAllAnalysed()方法
     */
    @Getter
    @Setter
    public static class ExcelListener extends AnalysisEventListener {

        private List<Object> datas = new ArrayList<>();

        /**
         * 逐行解析 object : 当前行的数据
         */
        @Override
        public void invoke(Object object, AnalysisContext context) {
            // 当前行
            // context.getCurrentRowNum()
            if (object != null) {
                datas.add(object);
            }
        }

        /**
         * 解析完所有数据后会调用该方法
         */
        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            // 解析结束销毁不用的资源
        }
    }

}
