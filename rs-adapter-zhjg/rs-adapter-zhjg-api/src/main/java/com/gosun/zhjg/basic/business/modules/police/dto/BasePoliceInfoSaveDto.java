package com.gosun.zhjg.basic.business.modules.police.dto;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.sql.Time;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 后勤人员信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-05-10 15:06:19
 */
@Data
@ApiModel("账号用户添加")
public class BasePoliceInfoSaveDto {

	// ======================================================================================================================
	// 基本信息

	@ApiModelProperty(value = "账号", required = true)
	private String userid;

	/**
	 * 所属监所
	 */
	@NotNull(message = "所属监所 不能为空!")
	@ApiModelProperty(value = "所属监所", required = true)
	private String prisonId;

	@NotNull(message = "姓名 不能为空!")
	@ApiModelProperty(value = "姓名", required = true)
	private String name;

	/**
	 * 登录用户名
	 */
//	@NotNull(message = "登录用户名 不能为空!")
	@ApiModelProperty(value = "登录用户名", required = false)
	private String username;
	/**
	 * 密码
	 */
//	@NotNull(message = "密码 不能为空!")
	@ApiModelProperty(value = "密码", required = false)
	private String userpassword;

	/**
	 * 部门编码
	 */
//	@NotNull(message = "部门编码 不能为空!")
	@ApiModelProperty(value = "部门编码", required = false)
	private String departmentcode;
	/**
	 * 部门名称
	 */
//	@NotNull(message = "部门名称 不能为空!")
	@ApiModelProperty(value = "部门名称", required = false)
	private String departmentname;
	/**
	 * 性别
	 */
	@NotNull(message = "性别 不能为空!")
	@ApiModelProperty(value = "性别", required = false)
	private String sex;
	/**
	 * 警号
	 */
	@NotNull(message = "警号 不能为空!")
	@ApiModelProperty(value = "警号", required = true)
	private String policeCode;
	/**
	 * 身份证
	 */
	@NotNull(message = "身份证 不能为空!")
	@ApiModelProperty(value = "身份证", required = true)
	private String idcard;
	/**
	 * 状态
	 */
//	@NotNull(message = "状态 不能为空!")
	@ApiModelProperty(value = "状态", required = false)
	private Integer status;
	/**
	 * 电话号码
	 */
	@NotNull(message = "电话号码 不能为空!")
	@ApiModelProperty(value = "电话号码", required = true)
	private String phoneNumber;
	/**
	 * 工作电话
	 */
//	@NotNull(message = "工作电话 不能为空!")
	@ApiModelProperty(value = "工作电话", required = false)
	private String worktelnum;

	/**
	 * 工作地址
	 */
	@ApiModelProperty(value = "工作地址", required = false)
	private String workaddress;
	/**
	 * 邮箱
	 */
	@NotNull(message = "邮箱 不能为空!")
	@ApiModelProperty(value = "邮箱", required = false)
	private String email;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注", required = false)
	private String remark;

	/**
	 * 联系地址
	 */
	@ApiModelProperty(value = "联系地址", required = false)
	private String address;

	/**
	 * 所属中队
	 */
	@ApiModelProperty(value = "所属中队", required = false)
	private String squadronId;

	/**
	 * 照片
	 */
//	@NotNull(message = "照片 不能为空!")
	@ApiModelProperty(value = "照片", required = false)
	private List<JSONObject> photo;
	private String photo2;

	public String getPhoto2() {
		if (this.photo2 != null)
			return this.photo2;
		if (photo != null && photo.size() > 0 && photo.get(0) != null) {
			return photo.get(0).getString("path");
		}
		return null;
	}

	/**
	 * 账号拥有角色 map中 key监所ID，v= 角色ID集合
	 *
	 */
	private Map<String, List<Integer>> userRoles;
	/**
	 * 同上面一致，格式不同 // ["4404400111_1", "4404400111_2"]
	 */
	private List<String> userRoles2;

	/**
	 * 优先从 userRoles2中解析获取
	 */
	public Map<String, List<Integer>> getUserRoles() {
		if (this.userRoles2 != null) {
			Map<String, List<Integer>> userRoles = new HashMap<String, List<Integer>>();
			boolean exists = false;
			for (String it : this.userRoles2) {
				int inde = it.indexOf("_");
				if (it == null || inde == -1) {
					continue;
				}
				String prisonId = it.substring(0, inde);
				Integer roleId = Integer.parseInt(it.substring(inde + 1));
				if (prisonId.length() == 0) {
					continue;
				}
				if (userRoles.get(prisonId) == null) {
					userRoles.put(prisonId, Lists.newArrayList(roleId));
				} else {
					userRoles.get(prisonId).add(roleId);
				}
				exists = true;
			}
			if (exists) {
				return userRoles;
			} else {
				return null;
			}
		}
		return this.userRoles;
	}

	/**
	 * 是否警员。默认是
	 */
	private Integer ispolice;

	/**
	 * 本单位入职时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "本单位入职时间", required = false)
	private Date entryTime;
	/**
	 * 出生时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "出生时间", required = false)
	private Date birthTime;
	/**
	 * 参加工作时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "参加工作时间", required = false)
	private Date workTime;
	/**
	 * 警衔
	 */
	@ApiModelProperty(value = "警衔", required = false)
	private String policeRank;
	/**
	 * 学历
	 */
	@ApiModelProperty(value = "学历", required = false)
	private String education;
	/**
	 * 学位
	 */
	@ApiModelProperty(value = "学位", required = false)
	private String degree;
	/**
	 * 政治面貌
	 */
	@ApiModelProperty(value = "政治面貌", required = false)
	private String politicalStatus;

	// ======================================================================================================================
	// 安全配置信息
	/**
	 * 用户有效期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@NotNull(message = "用户有效期 不能为空!")
	@ApiModelProperty(value = "用户有效期", required = false)
	private Date userexpdate;
	/**
	 * 密码有效期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@NotNull(message = "密码有效期 不能为空!")
	@ApiModelProperty(value = "密码有效期", required = false)
	private Date passwordexpdate;

	/**
	 * 允许登录IP起始段
	 */
	@ApiModelProperty(value = "允许登录IP起始段", required = false)
	private String ipbegin;
	/**
	 * 允许登录IP结束段
	 */
	@ApiModelProperty(value = "允许登录IP结束段", required = false)
	private String ipend;
	/**
	 * 允许登录IP起始段
	 */
	@ApiModelProperty(value = "允许登录时段结束", required = false)
	private Time loginend;
	/**
	 * 允许登录IP结束段
	 */
	@ApiModelProperty(value = "允许登录时段起始", required = false)
	private Time loginstart;

	@ApiModelProperty("20231122适配前端")
	private String photoStr;

	// ======================================================================================================================
	// 其他

}
