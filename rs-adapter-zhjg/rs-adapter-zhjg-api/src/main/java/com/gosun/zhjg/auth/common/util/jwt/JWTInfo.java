package com.gosun.zhjg.auth.common.util.jwt;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by ace on 2017/9/10.
 */
@SuppressWarnings("serial")
@Data
public class JWTInfo implements Serializable, IJWTInfo {

    /**
     * 用户id
     */
    private String userid;

    /**
     * 身份证号
     */
    private String idcard;

    /**
     * 用户名
     */
    private String name;

    /**
     * 警号
     */
    private String policeCode;

    /**
     * 民警id
     */
    private String policeId;

    /**
     * 民警名称
     */
    private String policeName;

    /**
     * 中队id
     */
    private String squadronId;
    /**
     * 中队名称
     */
    private String squadronName;
    /**
     * 监所id
     */
    private String prisonId;

    /***
     * 登录用户监所名
     */
    private String prisonName;
    private String prisonAbbreviation;

    /**
     * 登录用户名
     */
    private String username;

    /**
     * 登录用户密码
     */
    private String userpassword;

    /**
     * 登录用户角色
     */
    private List<SysRoleVO> roleList;

    /***
     * 登录角色id
     */
    private Integer roleId;

    /***
     * 登录角色姓名
     */
    private String roleName;
    /**
     * 拥有监所列表
     */
    private List<PrisonInfoVo> prisonList;

    /**
     * 在押人员编号
     */
    private String prisonerId;

    /**
     * 在押人员姓名
     */
    private String prisonerName;

    /***
     * 是否是属于支队 0:否  1:是
     */
    private String isZd;

    public JWTInfo(String userid, String name, String policeCode, String policeId, String policeName, String squadronId,
                   String squadronName, String prisonId, String prisonName, String username, List<SysRoleVO> roleList, Integer roleId, String roleName, List<PrisonInfoVo> prisonList, String isZd,String photo,String rolepost,Integer prisonType) {
        this.userid = userid;
        this.name = name;
        this.policeCode = policeCode;
        this.policeId = policeId;
        this.policeName = policeName;
        this.squadronId = squadronId;
        this.squadronName = squadronName;
        this.prisonId = prisonId;
        this.prisonName = prisonName;
        this.username = username;
        this.roleList = roleList;
        this.roleId = roleId;
        this.roleName = roleName;
        this.prisonList = prisonList;
        this.isZd = isZd;
        this.photo = photo;
        this.rolepost=rolepost;
        this.prisonType=prisonType;
    }

    public JWTInfo(String userid, String name, String policeCode, String policeId, String policeName, String squadronId,
                   String squadronName, String prisonId, String prisonName, String username, List<SysRoleVO> roleList,
                   Integer roleId, String roleName, List<PrisonInfoVo> prisonList, String isZd,String photo,String rolepost,
                   Integer prisonType, String prisonerId, String prisonerName) {
        this.userid = userid;
        this.name = name;
        this.policeCode = policeCode;
        this.policeId = policeId;
        this.policeName = policeName;
        this.squadronId = squadronId;
        this.squadronName = squadronName;
        this.prisonId = prisonId;
        this.prisonName = prisonName;
        this.username = username;
        this.roleList = roleList;
        this.roleId = roleId;
        this.roleName = roleName;
        this.prisonList = prisonList;
        this.isZd = isZd;
        this.photo = photo;
        this.rolepost=rolepost;
        this.prisonType=prisonType;
        this.prisonerId=prisonerId;
        this.prisonerName=prisonerName;

    }

    public JWTInfo() {
    }

    /**
     * token
     */
    @ApiModelProperty(value = "token")
    private String token;

    @ApiModelProperty("民警照片")
    private String photo;

	@ApiModelProperty("登录方式")
	private String loginType;

	@ApiModelProperty("人员类型")
	private String personnelType;

    @ApiModelProperty("监所类型")
    private Integer prisonType;

    /**
     * 岗位编码
     */
    private String rolepost;

	private JSONObject attributes = new JSONObject();

	public void putAttribute(String key, Object value) {
		this.attributes.put(key, value);
	}
}
