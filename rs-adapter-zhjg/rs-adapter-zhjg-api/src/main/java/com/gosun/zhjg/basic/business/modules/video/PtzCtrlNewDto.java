package com.gosun.zhjg.basic.business.modules.video;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("云台控制dto-视频联网")
public class PtzCtrlNewDto {
    @ApiModelProperty(value = "通道ID")
    private String chanId;

    @ApiModelProperty(value = "云台控制类型值")
    private String operType;

    @ApiModelProperty(value = "操作内容 1:表示动作开始 0:表示动作结束")
    private String operContent;

    @ApiModelProperty(value = "如该动作带有相关参数(比如速度,预置点的索引等)，则表示该参数值；否则置 0")
    private String extParam;
}
