package com.gosun.zhjg.basic.business.modules.role.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 角色监所分组表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-05-20 10:00:10
 */
@Data
@ApiModel("角色监所分组表新增Dto")
public class PermissionRolePrisonSaveDto {

	/**
	 * $column.comments
	 */
	@NotNull(message = "$column.comments 不能为空!")
	@ApiModelProperty(value = "$column.comments", required = true)
	private String id;
	/**
	 * $column.comments
	 */
	@NotNull(message = "$column.comments 不能为空!")
	@ApiModelProperty(value = "$column.comments", required = true)
	private Integer roleId;
	/**
	 * $column.comments
	 */
	@NotNull(message = "$column.comments 不能为空!")
	@ApiModelProperty(value = "$column.comments", required = true)
	private String dwdm;

}
