package com.gosun.zhjg.basic.business.modules.room.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("会见室dto")
public class MeetRoomDto {

    @ApiModelProperty("id,新增时不需传")
    private String id;

    @ApiModelProperty("别名")
    private String roomName;

    @ApiModelProperty("上游系统编码")
    private String roomId;

    @ApiModelProperty("ip")
    private String ip;

    @ApiModelProperty("状态（0：使用超时；1：使用中；2：空闲；3：维护中）")
    private String status;

    @ApiModelProperty("状态")
    private String statusDisplayName;

}
