package com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 人脸库管理-用户照片补录列表
 *
 * <AUTHOR>
 * @date 2023/3/21 10:49
 */
@Data
public class CnpfacePoliceVO {

    private String userid;

    private String name;

    private String policeCode;

    @ApiModelProperty("1警员，0辅警，-1其他")
    private String personnelType;
    private String personnelTypeDisplayName;

    private String personnelId;

    @ApiModelProperty("民警表照片")
    private String policePhoto;

    private String faceId;

    @ApiModelProperty("补录照片")
    private String photo;

    @ApiModelProperty("补录时间")
    private Date updateTime;
}

