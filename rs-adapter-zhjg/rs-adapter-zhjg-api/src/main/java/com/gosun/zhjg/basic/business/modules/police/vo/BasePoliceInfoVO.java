package com.gosun.zhjg.basic.business.modules.police.vo;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Time;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 民警信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-28 17:28:08
 */
@Data
@ApiModel("民警信息VO")
public class BasePoliceInfoVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 序号
	 */
	@ApiModelProperty(value = "序号")
	private String id;
	/**
	 * 用户ID
	 */
	@ApiModelProperty(value = "用户ID")
	private String userid;

	/**
	 * 增加个小写的get方法。 这个为兼容，有地方使用userid有使用userId
	 *
	 * @return
	 */
	@Deprecated
	public String getUserId() {
		return this.userid;
	}

	public String getUserid() {
		return this.userid;
	}

	/**
	 * 姓名
	 */
	@ApiModelProperty(value = "姓名")
	private String name;
	/**
	 * 警号
	 */
	@ApiModelProperty(value = "警号")
	private String policeCode;
	/**
	 * 所属中队
	 */
	@ApiModelProperty(value = "所属中队")
	private String squadronId;
	/**
	 * 所属监所
	 */
	@ApiModelProperty(value = "所属监所")
	private String prisonId;
	/**
	 * 监所名称
	 */
	@ApiModelProperty(value = "所属名称")
	private String prisonName;
	/**
	 * 民警电话
	 */
	@ApiModelProperty("民警电话")
	private String phoneNumber;
	/**
	 * 照片
	 */
//	@ApiModelProperty("照片")
//	private List<JSONObject> photo;
//
//	public List<JSONObject> getPhoto() {
//		if (this.photo != null)
//			return this.photo;
//		if (this.photo2 == null) {
//			return null;
//		}
//		JSONObject j = new JSONObject();
//		j.put("path", this.photo2);
//		j.put("fileName", "民警照片");
//		return Arrays.asList(j);
//	}

	/**
	 * 2021年6月16日10:22:35 定义Object 原因  之前已经有接口使用 String, 后面又有些接口需要符合前端规范 返回数组
	 */
	@ApiModelProperty("照片")
	private Object photo;

	public Object getPhoto() {
		if (this.photo != null)
			return this.photo;
		if (this.photo2 == null || "".equals(this.photo2)) {
			return null;
		}
		JSONObject j = new JSONObject();
		j.put("path", this.photo2);
		j.put("fileName", "民警照片");
		return Arrays.asList(j);
	}

	@JsonIgnore
	private String photo2;
	/**
	 * 中队名称
	 */
	@ApiModelProperty("中队名称")
	private String squadronName;
	/**
	 * 岗位
	 */
	@ApiModelProperty("岗位")
	private String jobs;

	/***
	 * 性别
	 */
	@ApiModelProperty("性别")
	private String sex;
	private String sex2;

	@ApiModelProperty("值班民警类型")
	private String type;

	@ApiModelProperty("年龄")
	private Integer age;

	@ApiModelProperty("警龄")
	private Integer workAge;

	@ApiModelProperty("参加工作时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date workTime;

	@ApiModelProperty("本单位入职时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date entryTime;

	@ApiModelProperty("出生时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date birthTime;

	@ApiModelProperty("警衔")
	private String policeRank;
	private String policeRank2;

	@ApiModelProperty("部门编号")
	private String departmentcode;

	@ApiModelProperty("部门")
	private String departmentname;

	@ApiModelProperty("证件号")
	private String idcard;

	@ApiModelProperty("在离职状态")
	private Integer status;
	private String status2;

	@ApiModelProperty("更新时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updatetime;

	@ApiModelProperty("民警地址")
	private String address;

	@ApiModelProperty("账号")
	private String username;

	@ApiModelProperty("办公电话")
	private String worktelnum;

	@ApiModelProperty("学历")
	private String education;
	private String education2;

	@ApiModelProperty("学位")
	private String degree;
	private String degree2;

	@ApiModelProperty("警衔")
	private String politicalStatus;
	private String politicalStatus2;

	@ApiModelProperty("办公地址")
	private String workaddress;

	@ApiModelProperty("email")
	private String email;

	@ApiModelProperty("备注")
	private String remark;

	@ApiModelProperty("账号有效期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date userexpdate;

	@ApiModelProperty("密码过期时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date passwordexpdate;

	/**
	 * 允许登录IP起始段
	 */
	@ApiModelProperty(value = "允许登录IP起始段", required = false)
	private String ipbegin;
	/**
	 * 允许登录IP结束段
	 */
	@ApiModelProperty(value = "允许登录IP结束段", required = false)
	private String ipend;
	/**
	 * 允许登录IP起始段
	 */
	@ApiModelProperty(value = "允许登录时段结束", required = false)
	private Time loginend;
	/**
	 * 允许登录IP结束段
	 */
	@ApiModelProperty(value = "允许登录时段起始", required = false)
	private Time loginstart;

	@ApiModelProperty(value = "是否警员", required = false)
	private Integer ispolice;
	private String ispolice2;

	/**
	 * 账号拥有角色 map中 key监所ID，v= 角色ID集合
	 *
	 */
	private Map<String, List<Long>> userRoles;
	/**
	 * 同上面一致，格式不同 // ["4404400111_1", "4404400111_2"]
	 */
	private List<String> userRoles2;

	@ApiModelProperty("角色名称返回字符串，多个逗号隔开")
	private String userRoles2DisplayName;

	@ApiModelProperty("得分")
	private Integer score;

	@ApiModelProperty("1-锁定")
	private Integer lockStatus;

	@ApiModelProperty("锁定状态中文")
	private String lockStatusName;

	@ApiModelProperty("图片字符串")
	private String photoStr;
}
