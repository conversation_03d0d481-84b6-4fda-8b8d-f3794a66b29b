package com.gosun.zhjg.basic.business.modules.area.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 区域树dto
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-08-13 08:57:25
 */
@Data
@ApiModel("区域树Dto")
public class BaseAreaTreeDto {

    /**
     * 监所id
     */
    @ApiModelProperty(value = "监所id")
    private String prisonId;
    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    private String areaName;

    /**
     * 区域名称
     */
    @ApiModelProperty(value = "同步设备级别")
    private int isSys;

}
