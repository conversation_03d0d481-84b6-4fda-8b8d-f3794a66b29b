package com.gosun.zhjg.auth.common.authority;

import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.List;


public class AppMenuTreeNode {
	/**
	 * 显示节点文本
	 */
	private String name;
	/**
	 * ID
	 */
	private int id;
	/**
	 * 父节点ID
	 */
	private int pId;
	/**
	 * 表示是否被选中
	 */
	private boolean checked;
	/**半选中*/
	private boolean halfCheck;
	/**
	 * 样式
	 */
	private String iconSkin;
	/**
	 * 是否叶子节点
	 */
	private boolean leaf;
	/**
	 * 子节点
	 */
	private List<AppMenuTreeNode> children;
	/**
	 * 是否展开
	 */
	private String open;
	/**
	 * 应用编码
	 */
	private String appCode;
	
	/**
     * 访问URL
     */
	private String appUrl;
	
	/**
	 * 
	 */
	private String type;
	private int appIndex;
	/** 数据权限URL */
	private String dataAuthority;
	private String code;
	
	public AppMenuTreeNode(){
		
	}
	
	public AppMenuTreeNode(String appCode,int id, int pId, String name , boolean bCheck , String isOpen , String iconSkin,String code) {
		this.id   		= id;
		this.pId  		= pId;
		this.name 		= name;
		this.leaf 		= false;
		this.open   	= isOpen;
		this.checked    = bCheck;
		this.appCode	= appCode;
		this.iconSkin	= iconSkin;
		this.code 		= code;
		children        = new ArrayList<AppMenuTreeNode>();
	}
	
	public AppMenuTreeNode(String appCode,int id, int pId, String name , String isOpen , String iconSkin,String code) {
		this.id   		= id;
		this.pId  		= pId;
		this.name 		= name;
		this.leaf 		= false;
		this.open   	= isOpen;
		this.appCode	= appCode;
		this.iconSkin	= iconSkin;
		this.code 		= code;
		children        = new ArrayList<AppMenuTreeNode>();
	}
	
	public AppMenuTreeNode(String appCode,int id, String name,int appIndex){
		this.id   		= id;
		this.name 		= name;
		this.leaf 		= false;
		this.open   	= "false";
		this.appCode	= appCode;
		this.iconSkin	= "app";
		this.appIndex	= appIndex;
		children        = new ArrayList<AppMenuTreeNode>();
	}
	
	public AppMenuTreeNode(String appCode,int id, String name,int appIndex, String dataAuthority, String appUrl){
		this.id   		= id;
		this.name 		= name;
		this.leaf 		= false;
		this.open   	= "false";
		this.appCode	= appCode;
		this.appUrl     = appUrl;
		this.dataAuthority	= dataAuthority;
		this.iconSkin	= "app";
		this.appIndex	= appIndex;
		children        = new ArrayList<AppMenuTreeNode>();
	}
	
	@Override
	public String toString() {
		JSONObject obj = new JSONObject();
		obj.put("id", id);
		obj.put("pId", pId);
		obj.put("text", name);
		obj.put("children", children);
		obj.put("leaf", leaf);
		obj.put("open", open);
		obj.put("appCode", appCode);
		obj.put("code", code);
		
		return obj.toString();
	}
	
	
	public boolean getChecked() {
		return checked;
	}
	public void setChecked(boolean checked) {
		this.checked = checked;
	}
	public List<AppMenuTreeNode> getChildren() {
		return children;
	}
	public void setChildren(List<AppMenuTreeNode> children) {
		this.children = children;
	}
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public int getpId() {
		return pId;
	}
	public void setpId(int pId) {
		this.pId = pId;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getIconSkin() {
		return iconSkin;
	}
	public void setIconSkin(String iconSkin) {
		this.iconSkin = iconSkin;
	}
	public boolean isLeaf() {
		return leaf;
	}
	public void setLeaf(boolean leaf) {
		this.leaf = leaf;
	}
	public String getOpen() {
		return open;
	}
	public void setOpen(String open) {
		this.open = open;
	}
	public String getAppCode() {
		return appCode;
	}
	public void setAppCode(String appCode) {
		this.appCode = appCode;
	}
	
	public String getAppUrl() {
        return appUrl;
    }

    public void setAppUrl(String appUrl) {
        this.appUrl = appUrl;
    }

    public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public int getAppIndex() {
		return appIndex;
	}
	public void setAppIndex(int appIndex) {
		this.appIndex = appIndex;
	}
	public boolean getHalfCheck() {
		return halfCheck;
	}
	public void setHalfCheck(boolean halfCheck) {
		this.halfCheck = halfCheck;
	}
	public String getDataAuthority() {
		return dataAuthority;
	}
	public void setDataAuthority(String dataAuthority) {
		this.dataAuthority = dataAuthority;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	

    @Override
    public boolean equals(Object obj){
        if (obj == null)
            return false;
        if (this == obj)
            return true;
        if (obj instanceof AppMenuTreeNode) {
        	AppMenuTreeNode vo = (AppMenuTreeNode) obj;
 
            // 比较每个属性的值 一致时才返回true
            if (vo.getId()==this.id && vo.getName().equals(this.name))
                return true;
        }
        return false;
    }
    /**
     * 重写hashcode 方法，返回的hashCode不一样才再去比较每个属性的值
     */
    @Override
    public int hashCode() {
        return name.hashCode()*id;
    }
}
