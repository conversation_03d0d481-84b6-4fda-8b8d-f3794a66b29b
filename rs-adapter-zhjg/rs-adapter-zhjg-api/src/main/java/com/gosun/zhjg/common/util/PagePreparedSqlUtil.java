package com.gosun.zhjg.common.util;

import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.mapping.ParameterMode;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.type.TypeHandlerRegistry;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * sql中将占位参数填充
 *
 * <AUTHOR>
 * @date 2024/4/25 19:20
 */
public class PagePreparedSqlUtil {

	/**
	 * ${@link com.baomidou.mybatisplus.core.MybatisParameterHandler#setParameters}
	 *
	 * @param configuration
	 * @param boundSql
	 * @return
	 */
	private static Map<Integer, Object> buildParameterValues(Configuration configuration, BoundSql boundSql) {
		Object parameterObject = boundSql.getParameterObject();
		List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
		if (parameterMappings != null) {
			Map<Integer, Object> parameterValues = new HashMap<>();
			TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
			for (int i = 0; i < parameterMappings.size(); i++) {
				ParameterMapping parameterMapping = parameterMappings.get(i);
				if (parameterMapping.getMode() != ParameterMode.OUT) {
					Object value;
					String propertyName = parameterMapping.getProperty();
					// issue #448 ask first for additional params
					if (boundSql.hasAdditionalParameter(propertyName)) {
						value = boundSql.getAdditionalParameter(propertyName);
					} else if (parameterObject == null) {
						value = null;
					} else if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
						value = parameterObject;
					} else {
						MetaObject metaObject = configuration.newMetaObject(parameterObject);
						value = metaObject.getValue(propertyName);
					}
					parameterValues.put(i, new Value(value));
				}
			}
			return parameterValues;
		}
		return Collections.emptyMap();
	}

	public static String getSqlWithValues(String statementQuery, Map<Integer, Object> parameterValues) {
		final StringBuilder sb = new StringBuilder();

		// iterate over the characters in the query replacing the parameter placeholders
		// with the actual values
		int currentParameter = 0;
		for (int pos = 0; pos < statementQuery.length(); pos++) {
			char character = statementQuery.charAt(pos);
			if (statementQuery.charAt(pos) == '?' && currentParameter <= parameterValues.size()) {
				// replace with parameter value
				Object value = parameterValues.get(currentParameter);
				sb.append(value != null ? value.toString() : new Value().toString());
				currentParameter++;
			} else {
				sb.append(character);
			}
		}

		return sb.toString();
	}

	public static String getPreparedSql(Configuration configuration, BoundSql boundSql) {
		return getSqlWithValues(boundSql.getSql(), buildParameterValues(configuration, boundSql));
	}

	public static String getPreparedSql(Configuration configuration, BoundSql boundSql, String sql) {
		return getSqlWithValues(sql, buildParameterValues(configuration, boundSql));
	}

	/**
	 * <AUTHOR>
	 * @date 2024/4/25 19:20
	 */
	public static class Value {
		public static final String NORM_DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

		public static final String DATABASE_DIALECT_DATE_FORMAT = NORM_DATETIME_PATTERN;
		public static final String DATABASE_DIALECT_TIMESTAMP_FORMAT = NORM_DATETIME_PATTERN;

		private Object value;

		public Value(Object valueToSet) {
			this();
			this.value = valueToSet;
		}

		public Value() {
		}

		public Object getValue() {
			return value;
		}

		public void setValue(Object value) {
			this.value = value;
		}

		@Override
		public String toString() {
			return convertToString(this.value);
		}

		public String convertToString(Object value) {
			String result;

			if (value == null) {
				result = "NULL";
			} else {
				if (value instanceof byte[]) {
					result = new String((byte[]) value);
				} else if (value instanceof Timestamp) {
					result = new SimpleDateFormat(DATABASE_DIALECT_TIMESTAMP_FORMAT).format(value);
				} else if (value instanceof Date) {
					result = new SimpleDateFormat(DATABASE_DIALECT_DATE_FORMAT).format(value);
				} else if (value instanceof Boolean) {
					result = Boolean.FALSE.equals(value) ? "0" : "1";
				} else {
					result = value.toString();
				}
				result = quoteIfNeeded(result, value);
			}

			return result;
		}

		private String quoteIfNeeded(String stringValue, Object obj) {
			if (stringValue == null) {
				return null;
			}
			if (Number.class.isAssignableFrom(obj.getClass()) || Boolean.class.isAssignableFrom(obj.getClass())) {
				return stringValue;
			} else {
				return "'" + escape(stringValue) + "'";
			}
		}

		private String escape(String stringValue) {
			return stringValue.replaceAll("'", "''");
		}

	}
}