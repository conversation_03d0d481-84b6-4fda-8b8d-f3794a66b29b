package com.gosun.zhjg.prison.room.terminal.modules.inscreen.feign;

import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.CnpFaceForm;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@Component
@FeignClient(value = "zhjg-prison-room-terminal", path = "inscreen/cnpface", contextId = "inscreen/cnpface")
public interface CnpFaceFeign {

    @RequestMapping(value = "/importFace", method = RequestMethod.POST)
    @ApiOperation(value = "导入照片")
    R<?> importFace(@RequestBody CnpFaceForm form);
}
