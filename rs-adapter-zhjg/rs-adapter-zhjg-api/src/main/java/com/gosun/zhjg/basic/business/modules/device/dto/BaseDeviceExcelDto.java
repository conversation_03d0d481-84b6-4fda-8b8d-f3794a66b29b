package com.gosun.zhjg.basic.business.modules.device.dto;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("设备ExcelDto")
public class BaseDeviceExcelDto  implements Serializable {

    private static final long serialVersionUID = 6255738183411898983L;

    @ExcelProperty("设备编码*")
    private String deviceCode;

    @ExcelProperty("设备名称*")
    private String deviceName;

    @ExcelProperty("设备类型编码*")
    private String deviceTypeId;

    @ExcelProperty("所属区域编码*")
    private String areaId;

    @ExcelProperty("点位名称")
    private String pointName;

    @ExcelProperty("设备状态*")
    private String deviceStatus;

    @ExcelProperty("通道id")
    private String channelId;

    @ExcelProperty("通道名称")
    private String channelName;

}
