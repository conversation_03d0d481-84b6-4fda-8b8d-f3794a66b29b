package com.gosun.zhjg.prison.room.terminal.modules.goanalyse.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("研究院客户端人员-删除")
public class GoanalyseDeletePersonDto {

    @ApiModelProperty(value = "终端ip",required = true)
    private List<String> ips;

    @ApiModelProperty(value = "关联实战平台编号：在押人员编号，民警编号，外来人员唯一编号等",required = true)
    private String faceCode;

    @ApiModelProperty("姓名")
    private String userName;
}
