package com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备版本信息表
 *
 * <AUTHOR>
 * @date 2023/9/7 15:56
 */
@Data
@ApiModel("设备版本信息表VO")
public class TerminalVersionInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 序列号
     */
    @ApiModelProperty(value = "序列号")
    private String serialNumber;

    @ApiModelProperty(value = "设备类型")
    private Integer deviceType;
    private String deviceTypeDisplayName;

    private String roomId;

    @ApiModelProperty(value = "监室号")
    private String roomName;

    /**
     * ip
     */
    @ApiModelProperty(value = "ip")
    private String ip;
    /**
     * 当前apk包版本
     */
    @ApiModelProperty(value = "当前apk包版本")
    private String apkVersion;
    /**
     * 当前web包版本
     */
    @ApiModelProperty(value = "当前web包版本")
    private String webVersion;
    /**
     * 能兼容的web包版本
     */
    @ApiModelProperty(value = "能兼容的web包版本")
    private String compatibleWebVersions;
    /**
     * apk更新时间
     */
    @ApiModelProperty(value = "apk更新时间")
    private Date apkUpgradeTime;
    /**
     * web更新时间
     */
    @ApiModelProperty(value = "web更新时间")
    private Date webUpgradeTime;
    /**
     * 同步时间
     */
    @ApiModelProperty(value = "同步时间")
    private Date updateTime;

    @ApiModelProperty(value = "设备在线状态，1在线、0不在线")
    private Integer onLine;

    public String getOnLineDisplayName() {
        if (onLine == null) {
            return null;
        }
        return onLine > 0 ? "在线" : "离线";
    }
}
