package com.gosun.zhjg.basic.business.modules.menu.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 角色管理授权-菜单授权配置
 *
 * <AUTHOR>
 *
 */
@Data
public class RoleGroupBigMenuSaveForm {

	@NotNull
	private Integer roleId;

	@NotNull
	private RoleBigMenuSaveItemForm[] items;

	@Data
	public static class RoleBigMenuSaveItemForm {
		@NotBlank
		private String groupId;

		@NotEmpty
		private List<String> menuIds;
	}
}
