package com.gosun.zhjg.common.config;

import com.gosun.zhjg.common.constant.KafkaTopicsConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.Field;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.List;


import static com.gosun.zhjg.common.constant.KafkaTopicsConstants.*;

/**
 * rabbitmq配置类
 *
 * <AUTHOR>
 * @date 2025年4月20日
 */
@Configuration
@Slf4j
public class RabbitMqConfig {

    @Autowired
    RabbitAdmin rabbitAdmin;


    @Bean
    public DirectExchange myExchange() {
        return new DirectExchange("cnp.kafka.exchange");
    }


    @Bean
    public Map<String, Queue> queues() throws IllegalAccessException {
        Field[] fields = KafkaTopicsConstants.class.getDeclaredFields();
        Map<String, Queue> queueMap = new LinkedHashMap<>();

        for (Field field : fields) {
            if (field.getType() == String.class &&
                    java.lang.reflect.Modifier.isStatic(field.getModifiers()) &&
                    java.lang.reflect.Modifier.isPublic(field.getModifiers()) &&
                    java.lang.reflect.Modifier.isFinal(field.getModifiers())) {

                try {
                    String topic = (String) field.get(null);
                    queueMap.put(topic, new Queue(topic, true));
                } catch (IllegalAccessException e) {
                    log.warn("无法访问字段: {}", field.getName(), e);
                }
            }
        }

        // 声明交换机和队列
        rabbitAdmin.declareExchange(myExchange());
        queueMap.values().forEach(rabbitAdmin::declareQueue);

        return queueMap;
    }

    /**
     * 自动绑定所有队列到交换机，使用各自的名称作为路由键
     */
    @Bean
    public List<Binding> bindings(Map<String, Queue> queues) {
        return queues.entrySet().stream()
                .map(entry -> BindingBuilder.bind(entry.getValue()).to(myExchange()).with(entry.getKey()))
                .collect(Collectors.toList());
    }

    /************************************* 初始化 ************************************/

    /**
     * 初始化 RabbitAdmin
     *
     * @param connectionFactory ConnectionFactory 连接工厂
     * @return RabbitAdmin
     */
    @Bean
    public RabbitAdmin rabbitAdmin(ConnectionFactory connectionFactory) {
        RabbitAdmin rabbitAdmin = new RabbitAdmin(connectionFactory);
        rabbitAdmin.setAutoStartup(true);    //只有设置为 true，spring 才会加载 RabbitAdmin 这个类
        return rabbitAdmin;
    }

    /**
     * 初始化 RabbitTemplate
     *
     * @param connectionFactory ConnectionFactory 连接工厂
     * @return RabbitTemplate
     */
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);

        //设置开启Mandatory,才能触发回调函数,无论消息推送结果怎么样都强制调用回调函数
        // 需要配置 spring.rabbitmq.publisher-confirms: true
        rabbitTemplate.setMandatory(true);

        // 发送端消息回调
        rabbitTemplate.setConfirmCallback((correlationData, ack, s) -> {
        });

        return rabbitTemplate;
    }
}
