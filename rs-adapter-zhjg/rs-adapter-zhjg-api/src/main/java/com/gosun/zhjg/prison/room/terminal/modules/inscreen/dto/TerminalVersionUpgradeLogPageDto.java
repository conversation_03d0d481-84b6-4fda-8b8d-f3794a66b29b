package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 设备版本升级日志
 *
 * <AUTHOR>
 * @date 2023/9/7 16:10
 */
@Data
@ApiModel("设备版本升级日志分页Dto")
public class TerminalVersionUpgradeLogPageDto extends AbstractPageQueryForm {

    /**
     * 序列号
     */
    @ApiModelProperty(value = "序列号", required = true)
    private String serialNumber;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments", required = true)
    private String ip;
    /**
     * 之前apk包版本
     */
    @ApiModelProperty(value = "之前apk包版本", required = true)
    private String apkVersion;
    /**
     * 之前web包版本
     */
    @ApiModelProperty(value = "之前web包版本", required = true)
    private String webVersion;
    /**
     * 更新的包类型    apk、web
     */
    @ApiModelProperty(value = "更新的包类型    apk、web", required = true)
    private String upgradeType;
    /**
     * 要更新版本
     */
    @ApiModelProperty(value = "要更新版本", required = true)
    private String upgradeVersion;
    /**
     * 是否更新成功 1、0
     */
    @ApiModelProperty(value = "是否更新成功 1、0", required = true)
    private Integer ok;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty("设备类型")
    private Integer deviceType;
}
