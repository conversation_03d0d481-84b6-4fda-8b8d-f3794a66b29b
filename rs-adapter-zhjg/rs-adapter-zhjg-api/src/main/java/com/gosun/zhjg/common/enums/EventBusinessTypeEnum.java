package com.gosun.zhjg.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 所情管理岗位处置状态
 * prison_event 表
 * 管教处理状态 warder_dispose_status 、 医生处理状态 doctor_dispose_status
 *
 * <AUTHOR>
 * @date 2024/10/16 14:40
 */
public enum EventBusinessTypeEnum {
	THJY("1", "谈话教育"),
	JSTZ("2", "监室调整"),
	XJSY("3", "械具使用"),
	SQBG("4", "伤情报告登记"),
	SNJY("5", "所内就医"),
	CSJY("6", "出所就医"),
	XWBL("9", "询问笔录"),
	FJSC("11", "附件上传"),
	SCFJ("12", "删除附件"),
	FSSRYXWBL("15", "询问笔录（非涉事人员）"),
	;

	private String value;
	private String desc;

	EventBusinessTypeEnum(String value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public String getValue() {
		return value;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(String value) {
		for (EventBusinessTypeEnum e : EventBusinessTypeEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}
}
