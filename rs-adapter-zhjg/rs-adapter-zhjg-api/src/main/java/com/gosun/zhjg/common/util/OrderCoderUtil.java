package com.gosun.zhjg.common.util;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

/***
 * 编码生成工具
 * yyyymmddhhmmss+四位随机数
 */
public class OrderCoderUtil {

    /**
     * 生成编码
     *nian
     */
    public static String getOrderCode(){
        DateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String format = sdf.format(new Date());
        return format+getRandom(4);
    }

    public static String getAutotPresentCode(){
        DateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String format = sdf.format(new Date());
        return format;
    }

    public static String getStartPresentCode(){
        DateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
        String format = sdf.format(new Date());
        return format;
    }

    /**
     * 生成固定长度随机码
     * @param n    长度
     */
    private static long getRandom(long n) {
        long min = 1,max = 9;
        for (int i = 1; i < n; i++) {
            min *= 10;
            max *= 10;
        }
        long rangeLong = (((long) (new Random().nextDouble() * (max - min)))) + min ;
        return rangeLong;
    }

}
