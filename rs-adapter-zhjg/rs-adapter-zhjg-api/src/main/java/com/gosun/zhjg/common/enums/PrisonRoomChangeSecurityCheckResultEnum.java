package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 监室调整-安全检查-检查结果
 * security_check_result
 *
 * <AUTHOR>
 * @date 2025/3/12 17:24
 */
public enum PrisonRoomChangeSecurityCheckResultEnum implements IEnum<Integer> {
	UNCHECKED(0, "未检查"),
	NORMAL(1, "正常"),
	ABNORMAL(2, "异常"),
	;

	@Getter
	private final Integer value;
	private final String desc;

	PrisonRoomChangeSecurityCheckResultEnum(Integer value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(Integer value) {
		for (PrisonRoomChangeSecurityCheckResultEnum e : PrisonRoomChangeSecurityCheckResultEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}

	public static Integer getValue(String desc) {
		for (PrisonRoomChangeSecurityCheckResultEnum e : PrisonRoomChangeSecurityCheckResultEnum.values()) {
			if (e.desc.equals(desc)) {
				return e.value;
			}
		}
		return null;
	}
}