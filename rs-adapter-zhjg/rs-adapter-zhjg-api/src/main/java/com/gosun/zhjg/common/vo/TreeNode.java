package com.gosun.zhjg.common.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by Ace on 2017/6/12.
 */
@Data
public class TreeNode implements Serializable {
    /**
     * ID
     */
    private String id;
    /**
     * 节点显示内容
     */
    private String title;
    /**
     * 父节点ID
     */
    private String parentId;
    /**
     * 类型
     */
    private String type;

    /**
     * 节点类型:0：区域，1：设备
     */
    private Integer nodeType;
    /**
     * 子节点集合
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<TreeNode> children;
    /**
     * 其他附带数据
     */
    private String data;

    /***
     *   展开
     */
    private Boolean expand;

    private String label;

    private String value;

    /**
     * 摄像机类型（0枪机，1球机），用于摄像机设备树
     */
    private Integer cameraType;
    /**
     * 用于摄像机设备树，若设备属于监室，则返回监室名称
     */
    private String roomName;

    private String questionType;

    /***
     * 监所编号
     */
    private String prisonId;

    /**
     * 扩展属性 可用于控制是否拥有此节点，编码等。 如果有一个拓展属性可放到此
     */
    private Object mark;

    @ApiModelProperty("电子地图-是否有点位")
    private Integer isPoint;

    @ApiModelProperty("电子地图-地图id")
    private String mapId;

    @ApiModelProperty("电子地图-区域名字")
    private String areaName;

    @ApiModelProperty("地图所在节点级别")
    private Integer treeLevel;

    @ApiModelProperty("电子地图-设备类型")
    private String deviceType;

    @ApiModelProperty("电子地图-配置类型")
    private String configCode;

    @ApiModelProperty("1-可进行配置")
    private Integer isConfig;

    @ApiModelProperty("每个监室的人员数量")
    private int personCunt;
}
