package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;

/**
 * 数据变动类型
 *
 * <AUTHOR>
 * @date 2023/7/24 9:48
 */
public enum TableDataChangeUpdateTypeEnum implements IEnum<String> {

    INSERT("INSERT"),
    DELETE("DELETE"),
    UPDATE("UPDATE");

    private String value;

    TableDataChangeUpdateTypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}