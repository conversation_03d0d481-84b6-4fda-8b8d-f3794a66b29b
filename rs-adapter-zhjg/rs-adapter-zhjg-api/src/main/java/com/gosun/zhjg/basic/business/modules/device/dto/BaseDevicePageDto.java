package com.gosun.zhjg.basic.business.modules.device.dto;

import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-10-19 11:40:50
 */
@Data
@ApiModel("设备表分页Dto")
public class BaseDevicePageDto extends AbstractPageQueryForm {

    /**
     * 设备编码
     */
    @ApiModelProperty(value = "设备编码")
    private String deviceCode;
    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String deviceName;
    /**
     * 设备类型
     */
    @ApiModelProperty(value = "设备类型")
    private String deviceTypeId;
    /**
     * 所属区域
     */
    @ApiModelProperty(value = "所属区域")
    private String areaId;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String status;
    /**
     * 点位名称
     */
    @ApiModelProperty(value = "点位名称")
    private String pointName;
    /**
     * 监室号
     */
    @ApiModelProperty(value = "监室号")
    private String roomId;
    /**
     * 设备状态
     */
    @ApiModelProperty(value = "设备状态")
    private String deviceStatus;
    /**
     * 所属监所
     */
    @ApiModelProperty(value = "所属监所")
    private String prisonId;
    /**
     * 搜索类型
     */
    @ApiModelProperty(value = "搜索类型(1:只查看会见室、审讯室设备,2,当前区域下所有节点的设备)")
    private Integer searchType;

}
