package com.gosun.zhjg.common.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.lang.annotation.Annotation;
import java.util.Arrays;

/**
 * 敏感属性配置实体
 *
 * <AUTHOR>
 *
 */
@Data
@TableName("base_sensitive")
public class BaseSensitiveEntity implements com.gosun.zhjg.common.context.Sensitive, Serializable {
	/**
	 *
	 */
	private static final long serialVersionUID = -2650420198913315158L;
	/**
	 * $column.comments
	 */
	@TableId
	private String id;
	/**
	 * 归属模块
	 */
	private String moduleName;
	/**
	 * 属性。多层级可用 .点分隔
	 */
	private String attr;
	/**
	 * URI
	 */
	private String uri;
	/**
	 * 地址请求方法，可不填。如果存在相似URL需要填写
	 */
	private String httpMethod;
	/**
	 * 去敏感需要替换的值
	 */
	private String value;
	/**
	 * 是否清楚去敏感化操作默认否。可用于禁用除某属性的去敏感操作
	 */
	private Integer clean;
	/**
	 * 支持正则表达式匹配替换
	 */
	private String regex;
	/**
	 * 允许可见监所，多个逗号分隔
	 */
	private String prison;
	/**
	 * 允许可见角色，多个逗号分隔
	 */
	private String role;
	/**
	 * 除*监所外其他允许，多个逗号分隔
	 */
	private String onlyForbidPrison;
	/**
	 * 除*角色外其他允许，多个逗号分隔
	 */
	private String onlyForbidRole;
	/**
	 * 预留
	 */
	private String strategy;
	/**
	 * 无实际意义，为此行记录做说明方便阅读
	 */
	private String remark;
	/**
	 * $column.comments
	 */
	@TableLogic
	private Integer delFlag;

	@Override
	public String value() {
		return this.value;
	}

	@Override
	public String regex() {
		return this.regex;
	}

	@Override
	public String[] prison() {
		String[] array = org.springframework.util.StringUtils.delimitedListToStringArray(prison, ",");
		return array;
	}

	@Override
	public int[] role() {
		int[] array = Arrays.stream(org.springframework.util.StringUtils.delimitedListToStringArray(role, ",")).mapToInt(Integer::parseInt).toArray();
		return array;
	}

	@Override
	public String[] onlyForbidPrison() {
		String[] array = org.springframework.util.StringUtils.delimitedListToStringArray(onlyForbidPrison, ",");
		return array;
	}

	@Override
	public int[] onlyForbidRole() {
		int[] array = Arrays.stream(org.springframework.util.StringUtils.delimitedListToStringArray(onlyForbidRole, ",")).mapToInt(Integer::parseInt).toArray();
		return array;
	}

	@Override
	public String strategy() {
		return this.strategy;
	}

	@Override
	public int clean() {
		return clean == null ? 0 : clean;
	}

	@Override
	public Class<? extends Annotation> annotationType() {
		return null;
	}
}
