package com.gosun.zhjg.basic.business.modules.file.dto;

import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 仓内屏文件
 *
 * <AUTHOR>
 */
@Data
@ApiModel("仓内屏文件分页Dto")
public class BaseDeviceInscreenFilePageDto extends AbstractPageQueryForm {

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments", required = true)
	private String id;
	/**
	 * 主机编号
	 */
	@ApiModelProperty(value = "主机编号", required = true)
	private Integer hostNum;
	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "设备编号", required = true)
	private Integer deviceNum;
	/**
	 * O是呼出I是呼入
	 */
	@ApiModelProperty(value = "O是呼出I是呼入", required = true)
	private String callType;
	/**
	 * 呼叫时间
	 */
	@ApiModelProperty(value = "呼叫时间", required = true)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date callStartTime;
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date callEndTime;
	/**
	 * 主机型号
	 */
	@ApiModelProperty(value = "主机型号", required = true)
	private String model;
	/**
	 * 分区
	 */
	@ApiModelProperty(value = "分区", required = true)
	private Integer zoneNum;
	/**
	 * 文件标记，这里存放文件名用于检索是否存在
	 */
	@ApiModelProperty(value = "文件标记，这里存放文件名用于检索是否存在", required = true)
	private String fileSign;
}
