package com.gosun.zhjg.common.constant;

/**
 * kafka消息主题
 *
 * <AUTHOR> on 2020/6/2
 * @email <EMAIL>
 * @date 2020/6/2
 */
public class KafkaTopicsConstants {

    /**
     * 业务异常kafka消息推送
     */
    public static final String bussinessCron = "bussiness-cron";
    /**
     * 数据监测kafka消息推送
     */
    public static final String databussinessCron = "data-bussiness-cron";
    /**
     * 监室终端使用-监室调整kafka转发主题
     */
    public static final String Kafkatopic_RoomChange = "terminal-notice-room-change";
    public static final String Kafkatopic_RoomChange2 = "terminal-notice-room-change2";
    /**
     * 监室终端使用-人员信息变动
     */
    public static final String Kafkatopic_PrisonerChange = "terminal-notice-prisoner-change";
    /**
     * 监室终端使用-人员信息变动
     */
    public static final String Kafkatopic_ZPChange = "terminal-notice-zp-change";
    /**
     * 监室终端-推送消息给序列号
     */
    public static final String Kafkatopic_PushMessageToSerialNumber = "terminal-push-message-to-serialnumber";
    /**
     * 监室终端-推送消息给监室
     */
    public static final String Kafkatopic_PushMessageToRoom = "terminal-push-message-to-room";
    /**
     * 平台-推送消息给监所下所有用户
     */
    public static final String Kafkatopic_PushMessageToPrison = "platform-push-message-to-prison";
    /**
     * 平台-推送消息给对外服务终端
     */
    public static final String Kafkatopic_PushMessageToExternal = "platform-push-message-to-external";


    /**
     * 平台-推送消息给用户
     */
    public static final String Kafkatopic_PushMessageToUser = "platform-push-message-to-user";

    /**
     * 设备预警
     */
    public static final String Kafkatopic_DevicePrisonWarning = "device-prison-warning";
    /**
     * 业务异常kafka消息推送
     */
    public static final String Patrol_Check_Cron = "patrol_check_cron";

    /***
     * 解除床位夹控
     */
    public static final String REMOVE_ROOM_BED_TOGETHER = "remove_room_bed_together";

    /***
     * 解除床位夹控
     */
    public static final String REMOVE_ROOM_BED = "remove_room_bed";

    /***
     * 解除床位夹控
     */
    public static final String FUGITIVE_PERSON = "fugtitive_person";


    /**
     * 访客终端人脸推送海康访客管理
     */
    public static final String Kafkatopic_External_Face_HK = "external_face_hk";

    /**
     * 会见业务推送到仓内屏带入带出
     */
    public static final String prisoner_out_in_message = "prisoner_out_in_message";

    /**
     * 点名异常推送到巡控岗待办事项
     */
    public static final String prisoner_room_present = "prisoner_room_present";

    /***
     * 心率告警
     */
    public static final String SENSE_EVENT = "SENSE_EVENT";
    public static final String represent_start = "represent_start";
    public static final String represent_start_auto = "represent_start_auto";
    public static final String del_represent_config = "del_represent_config";
    public static final String escort_event_handle_notice = "escort_event_handle_notice";
    public static final String business_notice = "business_notice";
    public static final String business_notice_del = "business_notice_del";
    public static final String prison_event_change = "prison_event_change";
    public static final String prison_warning_change = "prison_warning_change";
    public static final String bigScreen_notice = "bigScreen_notice";


}
