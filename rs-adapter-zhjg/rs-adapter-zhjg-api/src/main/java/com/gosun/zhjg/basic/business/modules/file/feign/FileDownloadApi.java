package com.gosun.zhjg.basic.business.modules.file.feign;

import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;

import java.net.URI;


/**
 * <AUTHOR>
 * @date 2023/2/24 15:36
 */
@Component
@FeignClient(name = "fileDownloadApi", url = "fileDownloadApi")
public interface FileDownloadApi {

//    @PostMapping("/ftp/downloadFile")
//    public R downloadFile(@RequestBody String path) throws URISyntaxException, IOException {
//        URI uri = new URI(path);
//        Response response = fileDownloadApi.downloadFile(uri);
//        if (response.status() == 200) {
//            File file = new File("H:\\lichunming\\Desktop", "aaa.jpg");
//            IOUtils.copy(response.body().asInputStream(), new FileOutputStream(file));
//        }
//        return R.ResponseOk();
//    }

    /**
     * 下载文件使用示例 见上
     *
     * @param uri
     * @return
     */
    @RequestMapping
    Response downloadFile(URI uri);
}
