package com.gosun.zhjg.basic.business.modules.prisoner.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gosun.zhjg.basic.business.modules.prisoner.vo.BasePrisonerVO;
import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * @description 在押人员信息分页dto
 * @date 2020/5/18
 */
@Data
@ApiModel("在押人员信息")
public class BasePrisonerPageInfoDto extends AbstractPageQueryForm<BasePrisonerVO> implements Serializable {

    private static final long serialVersionUID = -3274249298428458757L;

    @ApiModelProperty(value = "姓名（模糊查询）")
    private String prisonerName;

    @ApiModelProperty(value = "编号")
    private String prisonerId;

    @ApiModelProperty(value = "从属监室")
    private String roomId;

    @ApiModelProperty(value = "监室的,多个逗号隔开")
    private String roomIds;

    @ApiModelProperty("监所id")
    private String prisonId;

    @ApiModelProperty("风险等级")
    private String riskLevel;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("入所开始时间")
    private Date entryStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("入所结束时间")
    private Date entryEndTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("出所开始时间")
    private Date outStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("出所结束时间")
    private Date outEndTime;

    @ApiModelProperty("1 :高风险")
    private Integer riskType;

    @ApiModelProperty("1 :重病号")
    private Integer sickType;

    @ApiModelProperty("1: 紧急风险")
    private Integer urgentRiskType;

    @ApiModelProperty("1: 重点关注")
    private Integer importantType;

    @ApiModelProperty(hidden = true)
    private String riskCode1;

    @ApiModelProperty(hidden = true)
    private String riskCode2;

    @ApiModelProperty(hidden = true)
    private String sickCode;

    @ApiModelProperty("1: 耳目 2:风险评估 3:紧急风险 4:械具使用 5:下拉选择在押人员全部，6：防疫核查轨迹登记，7：监室调整")
    private Integer type;

    @ApiModelProperty("出所原因")
    private String outReason;

    @ApiModelProperty("证件号码")
    private String cardNum;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("关押期限起")
    private Date gyqxStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("关押期限止")
    private Date gyqxEnd;

    private String userid;

    @ApiModelProperty(value = "从属区域")
    private String areaId;
    
    @ApiModelProperty(value = "当前用户民警Id", hidden = true)
    private String currentUserPoliceId;

    @ApiModelProperty("涉嫌罪名")
    private String suspectedCharges;

    @ApiModelProperty("诉讼环节")
    private String litigationLink;

    @ApiModelProperty("档案编号")
    private String fileNumber;
}
