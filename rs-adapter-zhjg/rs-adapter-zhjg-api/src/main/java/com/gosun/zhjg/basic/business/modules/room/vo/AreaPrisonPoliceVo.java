package com.gosun.zhjg.basic.business.modules.room.vo;

import com.gosun.zhjg.common.dto.PhotoDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AreaPrisonPoliceVo implements Serializable {

    @ApiModelProperty("民警id")
    private String policeId;

    @ApiModelProperty("民警编号")
    private String policeCode;

    @ApiModelProperty("民警照片")
    private List<PhotoDto> photo;

    @ApiModelProperty("民警姓名")
    private String name;

    @ApiModelProperty("民警性别")
    private String sex;

    @ApiModelProperty("民警警衔")
    private String policeRank;

    @ApiModelProperty("民警警衔")
    private String policeRank2;

}
