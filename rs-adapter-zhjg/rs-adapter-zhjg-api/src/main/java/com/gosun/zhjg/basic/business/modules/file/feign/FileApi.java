package com.gosun.zhjg.basic.business.modules.file.feign;

import com.gosun.zhjg.basic.business.modules.file.dto.GetFileListDto;
import com.gosun.zhjg.basic.business.modules.file.vo.BaseFileVo;
import com.gosun.zhjg.common.msg.R;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> Lan
 * @Date 2023/1/31 16:36
 * @Description
 */
@Component
@FeignClient(value = "zhjg-basic-business", path = "file", contextId = "file")
public interface FileApi {

    @RequestMapping(value = "/ftp/getFileInputStream", method = RequestMethod.POST)
    @ApiOperation(value = "获取ftp目录下的所有文件流", responseContainer = "Map")
    public R<?> getFileInputStream(@RequestParam(value = "path") String path);


    /**
     * 上传文件
     *
     * @param multipartFile
     * @param directory     文件存放目录。可为空，默认空则日期
     * @return
     */
    @PostMapping(value = "/ftp/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    R<BaseFileVo> uploadFile(@RequestPart("file") MultipartFile multipartFile, @RequestParam(value = "directory", required = false) String directory
            , @RequestParam(value = "fileName", required = false) String fileName);

    /**
     * 查找指定ftp目录下文件列表
     *
     * @return
     */
    @PostMapping("/ftp/getFileList")
    R<List<String>> getFileList(GetFileListDto from);
}
