package com.gosun.zhjg.common.feign.intercept.response;



import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Objects;

@Aspect
@Component
public class FeignResponseAspect implements ApplicationContextAware {

    private static ApplicationContext context;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }

    @Around(value = "@annotation(feignResponseIntercept) || @within(feignResponseIntercept)")
    public Object around (ProceedingJoinPoint point, FeignResponseIntercept feignResponseIntercept) throws Throwable {
        Signature signature = point.getSignature();
        if (signature instanceof MethodSignature) {
            MethodSignature methodSignature = (MethodSignature) signature;
            Method method = methodSignature.getMethod();
            FeignResponseIntercept annotation = method.getAnnotation(FeignResponseIntercept.class);
            // 方法上的注解优先
            if (Objects.nonNull(annotation)) {
                feignResponseIntercept = annotation;
            }
        }
        Object proceed = point.proceed();
        Class<? extends FeignResponseHandle> feignResponseHandleClass = feignResponseIntercept.value();
        FeignResponseHandle feignResponseHandle = context.getBean(feignResponseHandleClass);
        return feignResponseHandle.responseHandle(proceed);
    }



}
