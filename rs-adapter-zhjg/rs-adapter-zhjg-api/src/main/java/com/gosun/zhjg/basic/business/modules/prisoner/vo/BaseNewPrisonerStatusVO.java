package com.gosun.zhjg.basic.business.modules.prisoner.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BaseNewPrisonerStatusVO extends BaseNewPrisonerVO{

    @ApiModelProperty("监室调整状态")
    private Integer ifChangingRoom;

    @ApiModelProperty("耳目状态,是否是耳目")
    private Integer  ifUndercover;

    @ApiModelProperty("是否处于耳目登记流程中 0=否；1=是")
    private Integer registeringUndercover;

    @ApiModelProperty("核酸检测状态")
    private Integer epidemicState;

    //目前前端要求这三个状态按原统一一个字段返回，后续若有组合状态判断再修改重新自定义状态变量
    @ApiModelProperty("风险评估，紧急风险状态，械具使用状态")
    private Integer ingStatus;

    @ApiModelProperty("是否械具使用 1是")
    private Integer punishmentToolUse;

    @ApiModelProperty("是否风险评估 1是")
    private Integer riskAssessment;

    @ApiModelProperty("黑名单状态")
    private Integer blacklistStatus;

    @ApiModelProperty("是否卧床登记 1是0否")
    private Integer stayBedStatus;

}
