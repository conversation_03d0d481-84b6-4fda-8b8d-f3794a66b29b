package com.gosun.zhjg.common.constant;

/**
 * Created by ace on 2017/8/29.
 */
public class CommonConstants {
    public final static String RESOURCE_TYPE_MENU = "menu";
    public final static String RESOURCE_TYPE_BTN = "button";
    // 用户token异常
    public static final Integer EX_USER_INVALID_CODE = 40301;
    public static final Integer EX_USER_PASS_INVALID_CODE = 500;
    // 客户端token异常
    public static final Integer EX_CLIENT_INVALID_CODE = 502;
    public static final Integer EX_CLIENT_FORBIDDEN_CODE = 40332;
    public static final Integer EX_OTHER_CODE = 500;
    public static final String CONTEXT_KEY_USER_ID = "currentUserId";
    public static final String CONTEXT_KEY_USERNAME = "currentUserName";
    public static final String CONTEXT_KEY_USER_NAME = "currentUser";
    public static final String CONTEXT_KEY_USER_TOKEN = "currentUserToken";
//    public static final String JWT_KEY_USER_ID = "userId";
//    public static final String JWT_KEY_NAME = "name";

    public static final String JWT_KEY_USERID = "userid";
    public static final String JWT_KEY_NAME = "name";
    public static final String JWT_KEY_POLICE_CODE = "policeCode";
    public static final String JWT_KEY_POLICE_ID = "policeId";
    public static final String JWT_KEY_POLICE_NAME = "policeName";
    public static final String JWT_KEY_SQUADRON_ID = "squadronId";
    public static final String JWT_KEY_SQUADRON_NAME = "squadronName";
    public static final String JWT_KEY_PRISON_ID = "prisonId";
    public static final String JWT_KEY_USERNAME = "username";
    public static final String JWT_KEY_ROLE_LIST = "roleList";
    
    /**
	 * token中attributes中岗位编码
	 */
    public static final String JWT_ATTR_ROLEPOST = "rolepost";

    /***
     * Token的实际有效时间
     */
    public static final long TOKEN_EXPIRE_TIME = 3600l;
}
