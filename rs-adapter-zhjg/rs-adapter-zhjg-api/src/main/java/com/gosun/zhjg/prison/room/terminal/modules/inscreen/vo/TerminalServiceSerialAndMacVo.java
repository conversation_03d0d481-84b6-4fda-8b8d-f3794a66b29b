package com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TerminalServiceSerialAndMacVo {

    @ApiModelProperty("mac地址")
    private String mac;

    @ApiModelProperty("序列号")
    private String devSerial;

    @ApiModelProperty("后台服务ip")
    private String serverIp;

    @ApiModelProperty("后台服务端口")
    private String serverPort;

    //会见终端-外来人员管理终端

    @ApiModelProperty("人脸服务器ip")
    private String serverFaceIp;

    @ApiModelProperty("人脸服务器端口")
    private String serverFacePort;
}
