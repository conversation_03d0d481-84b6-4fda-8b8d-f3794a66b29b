package com.gosun.zhjg.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 终端签名确认。业务类型
 *
 * <AUTHOR>
 */

public enum TerminalSignatureConfirmBusinessTypeEnum {
	HEALTH_CHECK("healthCheck", "入所健康检", "2"),
	GOODS_CONSIGNMENT("goodsConsignment", "物品顾送-签名确认", "5"),
	;

	@Getter
	private final String value;
	private final String desc;
	/**
	 * 关联仓内屏代办提醒类型
	 * {@link com.gosun.zhjg.prison.room.terminal.common.CnpWorkRemindTypeEnum}
	 */
	@Getter
	private final String cnpWorkRemindBusinessType;

	TerminalSignatureConfirmBusinessTypeEnum(String value, String desc) {
		this.value = value;
		this.desc = desc;
		this.cnpWorkRemindBusinessType = null;
	}


	TerminalSignatureConfirmBusinessTypeEnum(String value, String desc, String cnpWorkRemindBusinessType) {
		this.value = value;
		this.desc = desc;
		this.cnpWorkRemindBusinessType = cnpWorkRemindBusinessType;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(String value) {
		for (TerminalSignatureConfirmBusinessTypeEnum e : TerminalSignatureConfirmBusinessTypeEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}

	public static TerminalSignatureConfirmBusinessTypeEnum get(String value) {
		for (TerminalSignatureConfirmBusinessTypeEnum e : TerminalSignatureConfirmBusinessTypeEnum.values()) {
			if (e.value.equals(value)) {
				return e;
			}
		}
		return null;
	}
}
