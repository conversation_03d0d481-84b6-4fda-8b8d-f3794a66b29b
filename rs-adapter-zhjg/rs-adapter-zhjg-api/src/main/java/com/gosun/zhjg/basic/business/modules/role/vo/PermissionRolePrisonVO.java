package com.gosun.zhjg.basic.business.modules.role.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 角色监所分组表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-05-20 10:00:10
 */
@Data
@ApiModel("角色监所分组表VO")
public class PermissionRolePrisonVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String id;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Integer roleId;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String dwdm;

}
