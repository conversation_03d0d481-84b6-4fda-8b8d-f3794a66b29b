package com.gosun.zhjg.common.enums.commutation;

/**
 * prison_commutation表node 节点
 * 
 * <AUTHOR>
 *
 */
public enum PrisonCommutationNodeEnum {
	;
	public static String getDesc(String businessType, String value) {
		if (PrisonCommutationBusinessTypeEnum.JX.getValue().equals(businessType)) {
			return PrisonCommutationNode_JX_Enum.getDesc(value);
		} else if (PrisonCommutationBusinessTypeEnum.JS.getValue().equals(businessType)) {
			return PrisonCommutationNode_JS_Enum.getDesc(value);
		} else if (PrisonCommutationBusinessTypeEnum.JWZX.getValue().equals(businessType)) {
			return PrisonCommutationNode_JWZX_Enum.getDesc(value);
		}
		return null;
	}

	public static String getValue(String businessType, String desc) {
		if (PrisonCommutationBusinessTypeEnum.JX.getValue().equals(businessType)) {
			return PrisonCommutationNode_JX_Enum.getValue(desc);
		} else if (PrisonCommutationBusinessTypeEnum.JS.getValue().equals(businessType)) {
			return PrisonCommutationNode_JS_Enum.getValue(desc);
		} else if (PrisonCommutationBusinessTypeEnum.JWZX.getValue().equals(businessType)) {
			return PrisonCommutationNode_JWZX_Enum.getValue(desc);
		}
		return null;
	}

	public static Integer getStep(String businessType, String value) {
		if (PrisonCommutationBusinessTypeEnum.JX.getValue().equals(businessType)) {
			return PrisonCommutationNode_JX_Enum.getStep(value);
		} else if (PrisonCommutationBusinessTypeEnum.JS.getValue().equals(businessType)) {
			return PrisonCommutationNode_JS_Enum.getStep(value);
		} else if (PrisonCommutationBusinessTypeEnum.JWZX.getValue().equals(businessType)) {
			return PrisonCommutationNode_JWZX_Enum.getStep(value);
		}
		return null;
	}

	public static String getFirstNode(String businessType) {
		if (PrisonCommutationBusinessTypeEnum.JX.getValue().equals(businessType)) {
			return PrisonCommutationNode_JX_Enum.values()[0].getValue();
		} else if (PrisonCommutationBusinessTypeEnum.JS.getValue().equals(businessType)) {
			return PrisonCommutationNode_JS_Enum.values()[0].getValue();
		} else if (PrisonCommutationBusinessTypeEnum.JWZX.getValue().equals(businessType)) {
			return PrisonCommutationNode_JWZX_Enum.values()[0].getValue();
		}
		return null;
	}

	/**
	 * 下一个流程节点
	 * 
	 * @param businessType
	 * @param value
	 * @return
	 */
	public static String getNextNode(String businessType, String value) {
		if (PrisonCommutationBusinessTypeEnum.JX.getValue().equals(businessType)) {
			PrisonCommutationNode_JX_Enum[] values = PrisonCommutationNode_JX_Enum.values();
			for (int i = 0; i < values.length; i++) {
				if (values[i].getValue().equals(value) && i + 1 < values.length) {
					return values[i + 1].getValue();
				}
			}
			return null;
		} else if (PrisonCommutationBusinessTypeEnum.JS.getValue().equals(businessType)) {
			PrisonCommutationNode_JS_Enum[] values = PrisonCommutationNode_JS_Enum.values();
			for (int i = 0; i < values.length; i++) {
				if (values[i].getValue().equals(value) && i + 1 < values.length) {
					return values[i + 1].getValue();
				}
			}
			return null;
		} else if (PrisonCommutationBusinessTypeEnum.JWZX.getValue().equals(businessType)) {
			PrisonCommutationNode_JWZX_Enum[] values = PrisonCommutationNode_JWZX_Enum.values();
			for (int i = 0; i < values.length; i++) {
				if (values[i].getValue().equals(value) && i + 1 < values.length) {
					return values[i + 1].getValue();
				}
			}
			return null;
		}
		return null;
	}

	public static String getDoneNode(String businessType) {
		if (PrisonCommutationBusinessTypeEnum.JX.getValue().equals(businessType)) {
			return PrisonCommutationNode_JX_Enum.DONE.getValue();
		} else if (PrisonCommutationBusinessTypeEnum.JS.getValue().equals(businessType)) {
			return PrisonCommutationNode_JS_Enum.DONE.getValue();
		} else if (PrisonCommutationBusinessTypeEnum.JWZX.getValue().equals(businessType)) {
			return PrisonCommutationNode_JWZX_Enum.DONE.getValue();
		}
		return null;
	}

}