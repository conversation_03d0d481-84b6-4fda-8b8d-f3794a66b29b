package com.gosun.zhjg.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum HkPersonTypeEnum {
    WORKMAN("workman", "民警"),
    OUTMAN("outman", "外来人员"),
    PRISONER("prisoner", "被监管人员"),
    UNKNOW("unknown", "未登记人员");

    private String value;
    private String desc;

    HkPersonTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @JsonValue
    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value) {
        for (HkPersonTypeEnum e : HkPersonTypeEnum.values()) {
            if (e.value.equals(value)) {
                return e.desc;
            }
        }
        return null;
    }

    public static String getValue(String desc) {
        for (HkPersonTypeEnum e : HkPersonTypeEnum.values()) {
            if (e.getDesc().equals(desc)) {
                return e.value;
            }
        }
        return null;
    }
}
