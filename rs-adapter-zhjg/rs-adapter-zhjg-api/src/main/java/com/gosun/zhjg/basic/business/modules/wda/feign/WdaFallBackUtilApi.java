package com.gosun.zhjg.basic.business.modules.wda.feign;

import com.gosun.zhjg.basic.business.modules.wda.vo.WdaFallBackLogVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: panyusheng
 * @Date: 2021/5/7
 * @Version 1.0
 */
@FeignClient(value = "zhjg-basic-business", path = "base/wdaFallBackLogUtil", contextId = "base/wdaFallBackLogUtil")
public interface WdaFallBackUtilApi {

    @ApiOperation(value = "获取回写微达安成功的记录（插入）", response = WdaFallBackLogVO.class)
    @GetMapping("/getCallBackInsertRecord")
    WdaFallBackLogVO getCallBackInsertRecord(@RequestParam("tableName") String tableName, @RequestParam("pk") String pk);

}
