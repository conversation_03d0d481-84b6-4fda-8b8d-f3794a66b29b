package com.gosun.zhjg.common.util;

import com.gosun.zhjg.common.vo.TreeNode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Ace on 2017/6/12.
 */
public class TreeUtil {

    public static final String UUID_PARENT = "-1";

    /**
     * 两层循环实现建树
     *
     * @param treeNodes 传入的树节点列表
     * @return
     */
    public static <T extends TreeNode> List<T> bulid(List<T> treeNodes, Object root) {

        List<T> trees = new ArrayList<T>();

        for (T treeNode : treeNodes) {

            if (root.equals(treeNode.getParentId())) {
                trees.add(treeNode);
            }

            for (T it : treeNodes) {
                if (it.getParentId() == treeNode.getId()) {
                    ((List<T>) treeNode).add(it);
                }
            }
        }
        return trees;
    }

    /**
     * 使用递归方法建树
     *
     * @param treeNodes
     * @return
     */
    public static <T extends TreeNode> List<T> buildByRecursive(List<T> treeNodes, Object root) {
        List<T> trees = new ArrayList<T>();
        for (T treeNode : treeNodes) {
            treeNode.setExpand(true);
            treeNode.setParentId(StringUtils.isBlank(treeNode.getParentId()) ? "" : treeNode.getParentId());
            if (root.equals(treeNode.getParentId())) {
                Map<String, Object> map = new HashMap<>();
                treeNode.setTreeLevel(1);
                trees.add(findChildren(treeNode, treeNodes));
            }
        }
        return trees;
    }


    /**
     * 递归查找子节点
     *
     * @param treeNodes
     * @return
     */
    public static <T extends TreeNode> T findChildren(T treeNode, List<T> treeNodes) {
        for (T it : treeNodes) {
            if (treeNode.getId().equals(it.getParentId())) {
                treeNode.setExpand(true);
                it.setTreeLevel(treeNode.getTreeLevel() + 1);
                if (treeNode.getChildren() == null) {
                    treeNode.setChildren(new ArrayList<>());
                }
                treeNode.getChildren().add(findChildren(it, treeNodes));
            }
        }
        return treeNode;
    }

//  public static void main(String[] args) {
//		List<TreeNode> list=new ArrayList<TreeNode>();
//		list.add(new TreeNode("1","1",""));
//		list.add(new TreeNode("2","2",""));
//		list.add(new TreeNode("11","11","1"));
//		list.add(new TreeNode("12","12","1"));
//		list.add(new TreeNode("13","13","1"));
//		list.add(new TreeNode("21","21","2"));
//		list.add(new TreeNode("211","211","21"));
//		list.add(new TreeNode("2111","2111","211"));
//		
//		List<TreeNode> result=buildByRecursive(list,"");
//		
//		System.out.println(JSON.toJSON(result));
//
//	}

}
