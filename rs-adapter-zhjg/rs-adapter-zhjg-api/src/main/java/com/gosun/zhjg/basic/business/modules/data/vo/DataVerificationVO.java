package com.gosun.zhjg.basic.business.modules.data.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 数据对账
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-05-25 10:06:40
 */
@Data
@ApiModel("数据对账VO")
public class DataVerificationVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String id;
	/**
	 * 表中文名
	 */
	@ApiModelProperty(value = "表中文名")
	private String tableName;
	/**
	 * 批次号
	 */
	@ApiModelProperty(value = "批次号")
	private String batchNo;
	/**
	 * 维达安表
	 */
	@ApiModelProperty(value = "维达安表")
	private String wdaTable;
	/**
	 * 维达安数据量
	 */
	@ApiModelProperty(value = "维达安数据量")
	private Long wdaRecordCount;
	/**
	 * 本地表
	 */
	@ApiModelProperty(value = "本地表")
	private String localTable;
	/**
	 * 本地表数据量
	 */
	@ApiModelProperty(value = "本地表数据量")
	private Long localRecordCount;
	/**
	 * 对账时间
	 */
	@ApiModelProperty(value = "对账时间")
	private Date createTime;


	@ApiModelProperty("状态：字典C_CGSHZT")
	private String status;

	@ApiModelProperty("状态")
	private String statusDisplayName;

	@ApiModelProperty("备注")
	private String remarks;

	@ApiModelProperty("中间库表名")
	private String middleTable;

	@ApiModelProperty("中间表数量")
	private Long middleRecordCount;

	@ApiModelProperty("对账结果，0一致，1：不一致")
	private String result;

	@ApiModelProperty("对账结果，0一致，1：不一致")
	private String resultDisplayName;

}
