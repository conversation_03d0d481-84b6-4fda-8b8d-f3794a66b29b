package com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("监室仓内外屏列表")
public class BaseDeviceTerminalVo {

    @ApiModelProperty("监室id")
    private String roomId;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("仓内屏名称")
    private String internalName;

    @ApiModelProperty("仓内屏设备id")
    private String internalId;

    @ApiModelProperty("仓外屏名称")
    private String warehouseName;

    @ApiModelProperty("仓外屏设备id")
    private String warehouseId;

    @ApiModelProperty("更新人")
    private String updateUserName;

    @ApiModelProperty("更新时间")
    private Date updateTime;

}
