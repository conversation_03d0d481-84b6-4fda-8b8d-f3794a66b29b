package com.gosun.zhjg.common.dto;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
@ApiModel("基础文件表新增Dto")
public class BaseFileSaveDto {

	public BaseFileSaveDto() {

	}

	public BaseFileSaveDto(String path, String fileName) {
		this.path = path;
		this.fileName = fileName;
	}

	public BaseFileSaveDto(JSONObject json) {
		this.path = json.getString("path");
		this.fileName = json.getString("fileName");
	}

	/**
	 * $column.comments
	 */
	@NotNull(message = "$column.comments 不能为空!")
	@ApiModelProperty(value = "$column.comments", required = true)
	private String id;
	/**
	 * $column.comments
	 */
	@NotNull(message = "$column.comments 不能为空!")
	@ApiModelProperty(value = "$column.comments", required = true)
	private String prisonId;
	/**
	 * 关联业务表表名
	 */
	@NotNull(message = "关联业务表表名 不能为空!")
	@ApiModelProperty(value = "关联业务表表名", required = true)
	private String linkTable;
	/**
	 * 关联ID
	 */
	@NotNull(message = "关联ID 不能为空!")
	@ApiModelProperty(value = "关联ID", required = true)
	private String linkId;
	/**
	 * 文件路径
	 */
	@NotNull(message = "文件路径 不能为空!")
	@ApiModelProperty(value = "文件路径", required = true)
	private String path;
	/**
	 * 文件名
	 */
	@NotNull(message = "文件名 不能为空!")
	@ApiModelProperty(value = "文件名", required = true)
	private String fileName;
	/**
	 * 文件后缀
	 */
	@NotNull(message = "文件后缀 不能为空!")
	@ApiModelProperty(value = "文件后缀", required = true)
	private String fileType;
	/**
	 * userid
	 */
	@ApiModelProperty(value = "userid", required = true)
	private String createUserId;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments", required = true)
	private String createUsername;
	/**
	 * $column.comments
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "$column.comments", required = true)
	private Date createTime;
}
