package com.gosun.zhjg.basic.business.modules.police.dto;

import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 民警信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-05-09 17:13:49
 */
@Data
@ApiModel("民警信息表分页Dto")
public class BasePoliceInfoPageDto extends AbstractPageQueryForm {

//    /**
//     * 序号
//     */
//    @ApiModelProperty(value = "序号", required = true)
//    private String id;
	/**
	 * 用户ID
	 */
	@ApiModelProperty(value = "用户ID", required = true)
	private String userid;

	private List<Integer> userIdList;

	private Integer roleId;

	/**
	 * 姓名
	 */
	@ApiModelProperty(value = "姓名", required = true)
	private String name;
//    /**
//     * 警号
//     */
//    @ApiModelProperty(value = "警号", required = true)
//    private String policeCode;
//    /**
//     * 所属中队
//     */
//    @ApiModelProperty(value = "所属中队", required = true)
//    private String squadronId;
//    /**
//     * 所属监所
//     */
//    @ApiModelProperty(value = "所属监所", required = true)
//    private String prisonId;

	@ApiModelProperty(value = "角色类型", required = true)
	private int roleType;

	@ApiModelProperty("监所id")
	private String prisonId;

	@ApiModelProperty("值班日历选择时间")
	private Date DoDate;

	@ApiModelProperty("部门编号")
	private String departmentcode;

	@ApiModelProperty("部门")
	private String departmentname;

	@ApiModelProperty("账号")
	private String username;

	@ApiModelProperty("警号")
	private String policeCode;

	@ApiModelProperty("状态")
	private Integer status;

	@ApiModelProperty("查询类型,一警一档：1")
	private Integer searchType;

	@ApiModelProperty("民警类型")
	private String ispolice;
}
