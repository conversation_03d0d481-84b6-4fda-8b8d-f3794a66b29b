package com.gosun.zhjg.prison.room.terminal.modules.socket.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 语音播报
 *
 * <AUTHOR>
 * @date 2023/5/21 13:43
 */
@Data
public class PushVoiceBroadcastForm {
    /**
     * 业务类型。下面有类型枚举
     */
    @NotEmpty
    private String bizType;

    public String getBizTypeDisplayName() {
        return VoiceBroadcastBizTypeEnum.getDesc(this.bizType);
    }

    /**
     * 1语音提示、2弹窗、3语音+弹窗。下面有枚举
     * com.gosun.zhjg.prison.room.terminal.modules.socket.dto.VoiceBroadcastForm.VoiceBroadcastNoticeModeEnum
     */
    @NotNull
    private Integer noticeMode;
    /**
     * 消息内容
     */
    @NotEmpty
    private String content;
    /**
     * 监室id
     */
    @NotEmpty
    private List<String> roomIds;


    /**
     * noticeMode属性。展现形式（枚举：语音提示、弹窗、语音+弹窗）；
     *
     * <AUTHOR>
     * @date 2023/5/21 14:09
     */
    public enum VoiceBroadcastNoticeModeEnum {
        VOICE(1),
        POPUP(2),
        VOICE_POPUP(3);

        private Integer value;

        VoiceBroadcastNoticeModeEnum(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return this.value;
        }
    }

    /**
     * 业务类型。后续新增自行拓展
     *
     * <AUTHOR>
     * @date 2023/5/21 14:17
     */
    public enum VoiceBroadcastBizTypeEnum {

        TXHJ("TXHJ", "提讯会见"),

        LSHJ("LSHJ", "律师会见"),

        JSHJ("JSHJ", "家属会见");

        private String value;
        private String desc;

        VoiceBroadcastBizTypeEnum(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public String getValue() {
            return this.value;
        }

        public String getDesc() {
            return this.desc;
        }

        public static String getDesc(String value) {
            for (VoiceBroadcastBizTypeEnum e : VoiceBroadcastBizTypeEnum.values()) {
                if (e.value.equals(value)) {
                    return e.desc;
                }
            }
            return null;
        }
    }
}
