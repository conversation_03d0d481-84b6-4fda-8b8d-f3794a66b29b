package com.gosun.zhjg.common.util;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ArrayUtils;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ExceptionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.gosun.zhjg.common.context.PageSortMapping;
import com.gosun.zhjg.common.context.PageSortMapping.PageSort;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.OrderByElement;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Stream;

import static java.util.stream.Collectors.joining;

/**
 * <AUTHOR>
 */
@Slf4j
public class PageSortUtil {
    /**
     * 匹配order by
     */
    private static final Pattern ORDER_BY_PATTERN = Pattern.compile("(?i)order\\s+by");
    public static final String CLEANED_SORT = "CleanedSort";
    public static final String SORTING_PARAMS_COUNT = "SortingParamsCount";
    /**
     * 可配置属性。是否默认排序 null 排在最后
     */
    public static boolean CONFIG_DEFAULT_ORDER_BY_NULLS_LAST = true;

    /**
     * 替换掉sql中一些不能正常被(Select) CCJSqlParserUtil.parse(sql)解析的地方
     *
     * @param originalSql
     * @param dbType
     * @return
     */
    public static String SqlPostConstruct(String originalSql, DbType dbType) {
        if (DbType.POSTGRE_SQL.equals(dbType)) {
            originalSql = StringUtils.replace(originalSql, "isnull", "is null");
            originalSql = StringUtils.replace(originalSql, "ISNULL", "is null");
            originalSql = StringUtils.replace(originalSql, "notnull", "is not null");
            originalSql = StringUtils.replace(originalSql, "NOTNULL", "is not null");
        }
        return originalSql;
    }

    public static String DefaultConvert(String field) {
        return com.baomidou.mybatisplus.core.toolkit.StringUtils.camelToUnderline(field);
    }

    /**
     * 解析Dto中属性的注解
     *
     * @param argClazz
     * @return
     */
    public static PageSort reflectMapping(Class<?> argClazz) {
        PageSort obj = new PageSort();
        Field[] fields = argClazz.getDeclaredFields();
        for (Field field : fields) {
            PageSortMapping fileAnnotion = field.getAnnotation(PageSortMapping.class);
            if (fileAnnotion != null) {
                String prefix = fileAnnotion.prefix();
                String value = fileAnnotion.value();
                if (StringUtils.isBlank(prefix) && StringUtils.isBlank(value)) {
                    continue;
                }
                obj.put(field.getName(), prefix + (StringUtils.isBlank(value) ? PageSortUtil.DefaultConvert(field.getName()) : value));
            }
        }
        return obj;
    }

    /**
     * SQL结尾拼接Order BY
     *
     * @param originalSql
     * @param ascs
     * @param descs
     * @param orderByFieldMapping
     * @return
     */
    public static String concatOrderBy(String originalSql, String[] ascs, String[] descs, Map<String, String> orderByFieldMapping) {
        StringBuilder buildSql = new StringBuilder(originalSql);
        String ascStr = concatOrderBuilder(convertOrderField(ascs, orderByFieldMapping), " ASC");
        String descStr = concatOrderBuilder(convertOrderField(descs, orderByFieldMapping), " DESC");
        if (StringUtils.isNotEmpty(ascStr) && StringUtils.isNotEmpty(descStr)) {
            ascStr += ", ";
        }
        if (StringUtils.isNotEmpty(ascStr) || StringUtils.isNotEmpty(descStr)) {
            buildSql.append(" ORDER BY ");
            String orderByFragment = ascStr + descStr;
            buildSql.append(orderByFragment);
            if (CONFIG_DEFAULT_ORDER_BY_NULLS_LAST && !orderByFragment.toUpperCase().contains("NULLS LAST")) {
                buildSql.append(" NULLS LAST");
            }
        }
        return buildSql.toString();
    }

    /**
     * 拼接多个排序方法
     *
     * @param columns
     * @param orderWord
     */
    private static String concatOrderBuilder(String[] columns, String orderWord) {
        if (ArrayUtils.isNotEmpty(columns)) {
            return Arrays.stream(columns).filter(StringUtils::isNotEmpty).map(i -> i + orderWord).collect(joining(StringPool.COMMA));
        }
        return StringUtils.EMPTY;
    }

    /**
     * 转换排序字段 优先在映射里拿，映射里拿不到再转换下划线
     *
     * @return
     */
    private static String[] convertOrderField(String[] field, Map<String, String> mapping) {
        if (field == null) {
            return null;
        }
        Stream<String> stream = Arrays.stream(field).filter(StringUtils::isNotEmpty);
        if (mapping != null) {
            // 先在映射里拿，映射里拿不到再转换下划线
            stream = stream.map(m -> mapping.getOrDefault(m, PageSortUtil.DefaultConvert(m)));
        } else {
            stream = stream.map(PageSortUtil::DefaultConvert);
        }
        return stream.toArray(String[]::new);
    }

    /**
     * 剔除最后一个order by
     *
     * @param originalSql
     * @param dbType
     * @return
     */
    public static String clearOrderBy(String originalSql, DbType dbType) {
        Matcher matcher = ORDER_BY_PATTERN.matcher(originalSql);
        int lastIndex = -1;
        while (matcher.find()) {
            lastIndex = matcher.start();
        }
        // 剔除最后一个order by
        if (lastIndex >= 0) {
            originalSql = originalSql.substring(0, lastIndex);
        }
        return originalSql;
    }

    /**
     * 获取最后一个order by后面的sql
     *
     * @param originalSql
     * @return
     */
    public static String getOrderBySql(String originalSql) {
        Matcher matcher = ORDER_BY_PATTERN.matcher(originalSql);
        int lastIndex = -1;
        while (matcher.find()) {
            lastIndex = matcher.start();
        }
        // 剔除最后一个order by
        if (lastIndex >= 0) {
            return originalSql.substring(lastIndex);
        }
        return null;
    }

    /**
     * 打印SQL日志
     *
     * @param timing
     * @param total
     * @param mapperId
     * @param sql
     * @param debugWriteLog
     */
    public static void logSQL(long timing, long total, String mapperId, String sql, boolean debugWriteLog) {
        String sqlStr = "";
        try {
            Statement stmt = CCJSqlParserUtil.parse(sql);
            sqlStr = stmt.toString();
        } catch (Exception e) {

        }
        // @formatter:off
		StringBuilder formatSql = new StringBuilder()
	            .append(" 执行耗时：").append(timing)
	            .append(" ms Count: ")
	            .append(total)
	            .append(" - ID：").append(mapperId)
	            .append(StringPool.NEWLINE).append("======= SQL：")
	            .append(sqlStr).append(StringPool.NEWLINE);
		// @formatter:on
        if (debugWriteLog) {
            log.debug(formatSql.toString());
        } else {
            log.info(formatSql.toString());
        }
    }

    /**
     * 查询总记录条数
     *
     * @param sql
     * @param mappedStatement
     * @param boundSql
     * @param page
     */
    public static void queryTotal(boolean overflowCurrent, String sql, MappedStatement mappedStatement, BoundSql boundSql, IPage page, Connection connection) {
        try (PreparedStatement statement = connection.prepareStatement(sql)) {
            org.apache.ibatis.scripting.defaults.DefaultParameterHandler parameterHandler =
                    new org.apache.ibatis.scripting.defaults.DefaultParameterHandler(mappedStatement, boundSql.getParameterObject(), boundSql);
            parameterHandler.setParameters(statement);
            long total = 0;
            try (ResultSet resultSet = statement.executeQuery()) {
                if (resultSet.next()) {
                    total = resultSet.getLong(1);
                }
            }
            page.setTotal(total);
            /*
             * 溢出总页数，设置第一页
             */
            long pages = page.getPages();
            if (overflowCurrent && page.getCurrent() > pages) {
                // 设置为第一条
                page.setCurrent(1);
            }
        } catch (Exception e) {
            log.error("", e);
            throw ExceptionUtils.mpe("Error: Method queryTotal execution error.", e);
        }
    }

    /**
     * 获取方法返回值的泛型参数的类型
     */
    public static Class<?> getMethodGenricReturnType(final Method m) {
        if (List.class == m.getReturnType()) {
            Type type = m.getGenericReturnType();
            if (type instanceof ParameterizedType) {
                Type[] t = ((ParameterizedType) type).getActualTypeArguments();
                if (t.length > 0 && t[0] instanceof Class) {
                    return (Class<?>) t[0];
                }
            }
        }
        return Object.class;
    }

    /**
     * 获取排序sql中占位符数量
     *
     * @param plainSelect (PlainSelect) selectStatement.getSelectBody()
     * @return -1 无排序；0有排序 无占位符参数；其他：占位符参数数量
     */
    public static int getSortingParamsCount(PlainSelect plainSelect) {
        int sortingParamsCount = -1;
        List<OrderByElement> orderByElements = plainSelect.getOrderByElements();
        if (orderByElements != null) {
            sortingParamsCount = 0;
            for (OrderByElement item : orderByElements) {
                String str = item.toString();
                for (int i = 0; i < str.length(); i++) {
                    if ('?' == str.charAt(i)) {
                        sortingParamsCount++;
                    }
                }
            }
        }
        return sortingParamsCount;
    }

    /**
     * 获取sql中占位符“？”数量
     *
     * @param originalSql
     * @return
     */
    public static int getSqlParamsCount(String originalSql) {
        int count = 0;
        if (originalSql != null) {
            for (int i = 0; i < originalSql.length(); i++) {
                if ('?' == originalSql.charAt(i)) {
                    count++;
                }
            }
        }
        return count;
    }

    /**
     * 排序处理
     * 1.移除原sql末尾排序语句；2.拼接页面传递排序；3.将移除的排序语句包含的占位符参数一并移除
     *
     * @param page
     * @param originalSql
     * @param parameterMappings
     * @param pageSort
     * @param dbType
     * @return
     */
    public static String sortHandler(IPage page, String originalSql, List<ParameterMapping> parameterMappings, PageSort pageSort, DbType dbType) {
        JSONObject ret = new JSONObject();
        originalSql = PageSortUtil.concatOrderBy(originalSql, null, null, -1, dbType, pageSort, ret);
        // 去除可能出现的占位符参数量不匹配问题。order by 语句中移除几个占位符 这里就把对应数量的参数移除
        int sortingParamsCount = ret.getIntValue(PageSortUtil.SORTING_PARAMS_COUNT);
        if (ret.getBooleanValue(PageSortUtil.CLEANED_SORT) && sortingParamsCount > 0) {
            int t = sortingParamsCount;
            for (int i = parameterMappings.size() - 1; t > 0; t--) {
                parameterMappings.remove(i);
            }
        }
        return originalSql;
    }

    /**
     * sql语句中添加排序。如果原本sql中已存在则移除后添加
     *
     * @param originalSql
     * @param ascs
     * @param descs
     * @param sorted      1. 存在默认排序、0不存在默排序、-1不确认、-2解析失败
     * @param dbType
     * @param pageSort    属性与字段映射  PageSortContext.MAPPING_CACHE.getPageSort(mapperId)
     * @param ret         对象值传递，返回参数。CLEANED_SORT：是否清理过排序、SortingParamsCount 排序sql中占位符数量
     * @return
     */
    public static String concatOrderBy(String originalSql, String[] ascs, String[] descs, int sorted, DbType dbType, PageSort pageSort, JSONObject ret) {
        if (ret == null) {
            ret = new JSONObject();
        }
        ret.put(CLEANED_SORT, false);
        if (ArrayUtils.isEmpty(ascs) && ArrayUtils.isEmpty(descs)) {
            return originalSql;
        }
        // 可以粗略检查是否包含 order by
        String orderBySql = PageSortUtil.getOrderBySql(originalSql);
        // 查询出占位符号数量
        int sortingParamsCount = PageSortUtil.getSqlParamsCount(orderBySql);
        ret.put(SORTING_PARAMS_COUNT, sortingParamsCount);
        if (orderBySql == null) {
            // 不包含 order by 文字，一定不带排序
            sorted = 0;
        }
        if (pageSort != null && pageSort.getIgnore() != null && pageSort.getIgnore()) {
            return originalSql;
        }
        Map<String, String> orderByFieldMapping = pageSort == null ? null : pageSort.getCache();
        if (sorted == -1) {
            // 不确认是否存在排序
            try {
                Select selectStatement = (Select) CCJSqlParserUtil.parse(originalSql);
                PlainSelect plainSelect = (PlainSelect) selectStatement.getSelectBody();
                List<OrderByElement> orderBy = plainSelect.getOrderByElements();
                if (CollectionUtils.isNotEmpty(orderBy)) {
                    originalSql = PageSortUtil.clearOrderBy(originalSql, dbType);
                    ret.put(CLEANED_SORT, true);
                    ret.put(SORTING_PARAMS_COUNT, PageSortUtil.getSortingParamsCount(plainSelect));
                }
            } catch (Throwable e) {
                // 到这里报错影响范围：页面指定了orderBy排序，同时 Mapper Xml中存在默认排序 并且排序使用占位符。这样导致的页面排序覆盖xml中的排序 但是"?"占位符参数列表 可能会不匹配导致的sql执行出错
                // 继续处理
                sorted = -2;
            }
        }
        if (sorted == 1 || sorted == -2) {
            // 1：存在排序；或 -2：Sql解析异常后 默认按照存在排序处理
            originalSql = PageSortUtil.clearOrderBy(originalSql, dbType);
            ret.put(CLEANED_SORT, true);
        }
        return PageSortUtil.concatOrderBy(originalSql, ascs, descs, orderByFieldMapping);
    }

}
