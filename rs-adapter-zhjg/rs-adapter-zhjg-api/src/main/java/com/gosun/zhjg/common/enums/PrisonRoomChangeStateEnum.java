package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 监室调整-状态
 * prison_room_change
 *
 * <AUTHOR>
 * @date 2025/3/12 17:24
 */
public enum PrisonRoomChangeStateEnum implements IEnum<Integer> {
	AWAITING_SQUADRON_APPROVAL(1, "待科/警组审批"),
	AWAITING_LEADER_APPROVAL(2, "待所领导审批"),
	AWAITING_EXIT_CONFIRM(3, "待带出确认"),
	AWAITING_SECURITY_CHECK(4, "待带入确认"),
	COMPLETED(5, "已完成"),
	;

	@Getter
	private final Integer value;
	private final String desc;

	PrisonRoomChangeStateEnum(Integer value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(Integer value) {
		for (PrisonRoomChangeStateEnum e : PrisonRoomChangeStateEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}

	public static Integer getValue(String desc) {
		for (PrisonRoomChangeStateEnum e : PrisonRoomChangeStateEnum.values()) {
			if (e.desc.equals(desc)) {
				return e.value;
			}
		}
		return null;
	}
}