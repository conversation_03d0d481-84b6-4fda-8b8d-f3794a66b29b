package com.gosun.zhjg.common.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-08
 */

@Data
@ApiModel("通用流程阶段")
public class SimpleStepsVO {
    @ApiModelProperty("流程标题")
    private String title;

    @ApiModelProperty("流程内容")
    private String content;

    private String tooltip;

    /***
     *登记人/
     */
    @ApiModelProperty("登记人/审批人")
    private String operatePolice;

    /***
     * 登记日期
     */
    @ApiModelProperty("登记日期/审批日期")
    private String operateDate;

    /***
     * 状态集合
     */
    @ApiModelProperty("状态集合")
    private List<String> statusList;
}
