/**
* @Title:：Base64Utils.java
* @Package ：com.gosun.security.dataSharing.corpsCollect.commons.utils
* @Description： TODO
* @author： LQB
* @date： 2019年9月25日 下午3:42:44
* @version ： 1.0
*/
package com.gosun.zhjg.common.util;

import org.apache.commons.codec.binary.Base64;

import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

/**
* @ClassName:：Base64Utils
* @Description： TODO
* <AUTHOR>
* @date ：2019年9月25日 下午3:42:44
*
*/
public class Base64Util {
	//base64字符串转byte[]
    public static byte[] base64String2ByteFun(String base64Str){
        return Base64.decodeBase64(base64Str);
    }
    //byte[]转base64
    public static String byte2Base64StringFun(byte[] b){
        return Base64.encodeBase64String(b);
    }
    /**
     * 本地图片转换Base64的方法
     *
     * @param imgPath     
     */
    public static String ImageToBase64(String imgPath) {
        byte[] data = null;
        // 读取图片字节数组
        try {
            InputStream in = new FileInputStream(imgPath);
            data = new byte[in.available()];
            in.read(data);
            in.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        // 返回Base64编码过的字节数组字符串
        return Base64.encodeBase64String(data);
    }

    /**
     * 照片url路径转换base64
     * @param imageUrl
     * @return
     * @throws IOException
     */
    public static String imageUrlToBase64(String imageUrl) throws IOException {
        URL url = new URL(imageUrl);
        InputStream is = url.openStream();
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        int nRead;
        byte[] data = new byte[1024];
        while ((nRead = is.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, nRead);
        }
        buffer.flush();
        byte[] bytes = buffer.toByteArray();
        is.close();
        return java.util.Base64.getEncoder().encodeToString(bytes);
    }

    public static String base64Head(String fileSuffix){
        if (fileSuffix.endsWith(".bmp") || fileSuffix.endsWith(".BMP")){
            return "data:image/bmp;base64,";
        }else if (fileSuffix.endsWith(".png") || fileSuffix.endsWith(".PNG")){
            return "data:image/png;base64,";
        }else if (fileSuffix.endsWith(".webp") || fileSuffix.endsWith(".WEBP")){
            return "data:image/webp;base64,";
        }else {
            return "data:image/jpeg;base64,";
        }
    }
}
