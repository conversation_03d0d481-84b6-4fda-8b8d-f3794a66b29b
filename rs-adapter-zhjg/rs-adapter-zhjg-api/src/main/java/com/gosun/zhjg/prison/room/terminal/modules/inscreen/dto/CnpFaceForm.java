package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


@Data
public class CnpFaceForm {

    @ApiModelProperty(value = "序列号集合")
    private List<String> serialNumbers;

    @ApiModelProperty("人员类型 民警: police_1, 辅警: police_0, 其他: police_-1, 被监管人员: prisoner")
    private String personnelType;

    @ApiModelProperty("在押人员人员编号、民警policeId")
    private String personnelId;

    private List<String> personnelIdList = null;

    public List<String> getPersonnelIdList() {
        if (personnelIdList != null) {
            return personnelIdList;
        }
        if (StringUtils.isNotBlank(personnelId)) {
            return Collections.singletonList(personnelId);
        }
        return new ArrayList<String>();
    }

    @ApiModelProperty("在押人员人员编号、民警policeId")
    private String photo;

    @Deprecated
    private String origin;

    @ApiModelProperty(value = "忽略对指定序列号的设备的人脸录入")
    private List<String> skipImportSerialNumbers;
}
