package com.gosun.zhjg.basic.business.modules.device.dto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("设备dto")
public class BaseDeviceDto{

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("设备编码")
    private String deviceCode;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("设备类型")
    private String deviceTypeId;

    @ApiModelProperty("所属区域")
    private String areaId;

    @ApiModelProperty("厂家")
    private String factoryId;

    @ApiModelProperty("型号")
    private String modelId;

    @ApiModelProperty("协议")
    private String protocolId;

    @ApiModelProperty("是否启用")
    private String status;

    @ApiModelProperty("删除标记")
    private Integer delFlag;

    @ApiModelProperty("ip地址")
    private String ipAddress;

    @ApiModelProperty("点位名称")
    private String pointName;

    @ApiModelProperty("监室号")
    private String roomId;

    @ApiModelProperty("设备状态")
    private String deviceStatus;

    @ApiModelProperty("所属监所")
    private String prisonId;

    @ApiModelProperty("通道id")
    private String channelId;

    @ApiModelProperty("通道名称")
    private String channelName;

    @ApiModelProperty("mac地址")
    private String macAddress;

    @ApiModelProperty("监室终端（仓内外屏）信息")
    private BaseDeviceTerminalDto terminalDto;


}
