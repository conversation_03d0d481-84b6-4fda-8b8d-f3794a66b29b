package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 出所原因
 * 
 * <AUTHOR>
 *
 */
public enum PrisonOutRecordExtOutCauseEnum implements IEnum<String> {

	TJ("TJ", "提解", true), LSCS("LSCS", "临时出所", true), CSJY("CSJY", "出所就医", true);

	private String value;
	private String desc;
	// 是否需要回所
	private boolean needReturn;

	PrisonOutRecordExtOutCauseEnum(final String value, final String desc, final boolean needReturn) {
		this.desc = desc;
		this.value = value;
		this.needReturn = needReturn;
	}

	@Override
	public String getValue() {
		return value;
	}

	public boolean isNeedReturn() {
		return needReturn;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getValue(String desc) {
		for (PrisonOutRecordExtOutCauseEnum e : PrisonOutRecordExtOutCauseEnum.values()) {
			if (e.desc.equals(desc)) {
				return e.value;
			}
		}
		return null;
	}

	public static String getDesc(String value) {
		for (PrisonOutRecordExtOutCauseEnum e : PrisonOutRecordExtOutCauseEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}

	public static boolean isNeedReturn(String value) {
		for (PrisonOutRecordExtOutCauseEnum e : PrisonOutRecordExtOutCauseEnum.values()) {
			if (e.value.equals(value)) {
				return e.needReturn;
			}
		}
		return false;
	}
}
