package com.gosun.zhjg.common.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;

/**
 * 日期处理
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年12月21日 下午12:53:33
 */
public class DateUtils {
    /**
     * 时间格式(yyyy-MM-dd)
     */
    public final static String DATE_PATTERN = "yyyy-MM-dd";
    /**
     * 时间格式(yyyy-MM-dd HH:mm:ss)
     */
    public final static String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public final static SimpleDateFormat sdf_iso8061 = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
    public final static SimpleDateFormat sdf_yyyyMMdd = new SimpleDateFormat("yyyyMMdd");

    public final static String DATE_ISO8061 = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX";

    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     *
     * @param date 日期
     * @return 返回yyyy-MM-dd格式日期
     */
    public static String format(Date date) {
        return format(date, DATE_PATTERN);
    }

    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     *
     * @param date    日期
     * @param pattern 格式，如：DateUtils.DATE_TIME_PATTERN
     * @return 返回yyyy-MM-dd格式日期
     */
    public static String format(Date date, String pattern) {
        if (date != null) {
            SimpleDateFormat df = new SimpleDateFormat(pattern);
            return df.format(date);
        }
        return null;
    }

    /**
     * 字符串转换成日期
     *
     * @param strDate 日期字符串
     * @param pattern 日期的格式，如：DateUtils.DATE_TIME_PATTERN
     */
    public static Date stringToDate(String strDate, String pattern) {
        if (StringUtils.isBlank(strDate)) {
            return null;
        }

        DateTimeFormatter fmt = DateTimeFormat.forPattern(pattern);
        return fmt.parseLocalDateTime(strDate).toDate();
    }

    /**
     * 根据周数，获取开始日期、结束日期
     *
     * @param week 周期 0本周，-1上周，-2上上周，1下周，2下下周
     * @return 返回date[0]开始日期、date[1]结束日期
     */
    public static Date[] getWeekStartAndEnd(int week) {
        DateTime dateTime = new DateTime();
        LocalDate date = new LocalDate(dateTime.plusWeeks(week));

        date = date.dayOfWeek().withMinimumValue();
        Date beginDate = date.toDate();
        Date endDate = date.plusDays(6).toDate();
        return new Date[]{beginDate, endDate};
    }

    /**
     * 对日期的【秒】进行加/减
     *
     * @param date    日期
     * @param seconds 秒数，负数为减
     * @return 加/减几秒后的日期
     */
    public static Date addDateSeconds(Date date, int seconds) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusSeconds(seconds).toDate();
    }

    /**
     * 对日期的【分钟】进行加/减
     *
     * @param date    日期
     * @param minutes 分钟数，负数为减
     * @return 加/减几分钟后的日期
     */
    public static Date addDateMinutes(Date date, int minutes) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusMinutes(minutes).toDate();
    }

    /**
     * 对日期的【小时】进行加/减
     *
     * @param date  日期
     * @param hours 小时数，负数为减
     * @return 加/减几小时后的日期
     */
    public static Date addDateHours(Date date, int hours) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusHours(hours).toDate();
    }

    /**
     * 对日期的【天】进行加/减
     *
     * @param date 日期
     * @param days 天数，负数为减
     * @return 加/减几天后的日期
     */
    public static Date addDateDays(Date date, int days) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusDays(days).toDate();
    }

    /**
     * 对日期的【周】进行加/减
     *
     * @param date  日期
     * @param weeks 周数，负数为减
     * @return 加/减几周后的日期
     */
    public static Date addDateWeeks(Date date, int weeks) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusWeeks(weeks).toDate();
    }

    /**
     * 对日期的【月】进行加/减
     *
     * @param date   日期
     * @param months 月数，负数为减
     * @return 加/减几月后的日期
     */
    public static Date addDateMonths(Date date, int months) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusMonths(months).toDate();
    }

    /**
     * 对日期的【年】进行加/减
     *
     * @param date  日期
     * @param years 年数，负数为减
     * @return 加/减几年后的日期
     */
    public static Date addDateYears(Date date, int years) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusYears(years).toDate();
    }

    /**
     * @param date
     * @return
     */
    public static Date getStartTimeOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        Date beginDate = null;
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        beginDate = stringToDate(format(calendar.getTime(), "yyyy-MM-dd 00:00:00"), "yyyy-MM-dd 00:00:00");
        return beginDate;
    }

    /**
     * @param date
     * @return
     */
    public static Date getEndTimeOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        Date beginDate = null;
        calendar.add(Calendar.MONTH, -0);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        beginDate = stringToDate(format(calendar.getTime(), "yyyy-MM-dd 23:59:59"), "yyyy-MM-dd HH:mm:ss");
        return beginDate;
    }

    /**
     * @param year
     * @param month
     * @return
     */
    public static Date getStartTimeOfMonth(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        // 设置年份
        calendar.set(Calendar.YEAR, year);
        // 设置月份
        calendar.set(Calendar.MONTH, month - 1);
        // 设置日期
        calendar.set(Calendar.DAY_OF_MONTH, 1);

        Date beginDate = null;
        beginDate = stringToDate(format(calendar.getTime(), "yyyy-MM-dd 00:00:00"), "yyyy-MM-dd 00:00:00");
        return beginDate;
    }

    /**
     * @param year
     * @param month
     * @return
     */
    public static Date getEndTimeOfMonth(int year, int month) throws ParseException {
        Calendar calendar = Calendar.getInstance();
        // 设置年份
        calendar.set(Calendar.YEAR, year);
        // 设置月份
        calendar.set(Calendar.MONTH, month - 1);
        // 获取某月最大天数
        int lastDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        // 设置日历中月份的最大天数
        calendar.set(Calendar.DAY_OF_MONTH, lastDay);

        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd H:m:s");
        String endDateStr = format(calendar.getTime(), "yyyy-MM-dd 23:59:59");
//        Date endDate =  stringToDate(format(calendar.getTime(),"yyyy-MM-dd 23:59:59"),"yyyy-MM-dd HH:mm:dd");
        Date endDate = df.parse(endDateStr);
        return endDate;
    }

    public static Date getISO8061Date(JSONObject obj, String k) {
        if (obj.containsKey(k)) {
            try {
                return sdf_iso8061.parse(obj.getString(k));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public static Date getISO8061Date(String dateStr) {
        try {
            return sdf_iso8061.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Integer getInt(JSONObject obj, String k) {
        if (obj.containsKey(k)) {
            return obj.getInteger(k);
        }
        return null;
    }

    public static Date formatDate2yyyyMMdd(String dateStr) {
        try {
            return sdf_yyyyMMdd.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * @param date1     开始时间
     * @param date2     结束时间
     * @param separator 年和月中间分隔字符串
     * @return ["2017-12","2018-01","2018-02"]
     * @Description: 获取两个时间间隔中所有月
     * @date: 2019年10月26日 上午9:59:12
     */
    @SuppressWarnings("unused")
    public static List<String> getIntervalAllMonth(Date date1, Date date2, String separator) {

        List<String> r = new ArrayList<>();

        DateTime dt = new DateTime(date1);
        DateTime dt2 = new DateTime(date2);
        String s, s2 = null;

        while ((s = dt.getYear() + separator + String.format("%02d", dt.getMonthOfYear()))
                .compareTo(s2 = dt2.getYear() + separator + String.format("%02d", dt2.getMonthOfYear())) <= 0) {
            r.add(s);
            dt = dt.plusMonths(1);
        }
        return r;
    }

    /**
     * @param date1     开始时间
     * @param date2     结束时间
     * @param separator separator 年月日中间分隔字符串
     * @return ["2019-01-30","2019-01-31","2019-02-01"]
     * @Description: 获取两个时间间隔中所有日
     * @date: 2019年10月26日 上午10:30:26
     */
    @SuppressWarnings("unused")
    public static List<String> getIntervalAllDay(Date date1, Date date2, String separator) {

        List<String> r = new ArrayList<>();

        DateTime dt = new DateTime(date1);
        DateTime dt2 = new DateTime(date2);
        String s, s2 = null;

        while ((s = dt.getYear() + separator + String.format("%02d", dt.getMonthOfYear()) + separator
                + String.format("%02d", dt.getDayOfMonth()))
                .compareTo(s2 = dt2.getYear() + separator + String.format("%02d", dt2.getMonthOfYear())
                        + separator + String.format("%02d", dt2.getDayOfMonth())) <= 0) {
            r.add(s);
            dt = dt.plusDays(1);
        }
        return r;
    }

    public static int getAgeByBirth(Date birthDay) {
        int age = 0;
        try {
            Calendar cal = Calendar.getInstance();
            if (cal.before(birthDay)) { // 出生日期晚于当前时间，无法计算
                throw new IllegalArgumentException("The birthDay is before Now.It's unbelievable!");
            }
            int yearNow = cal.get(Calendar.YEAR); // 当前年份
            int monthNow = cal.get(Calendar.MONTH); // 当前月份
            int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH); // 当前日期
            cal.setTime(birthDay);
            int yearBirth = cal.get(Calendar.YEAR);
            int monthBirth = cal.get(Calendar.MONTH);
            int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);
            age = yearNow - yearBirth; // 计算整岁数
            if (monthNow <= monthBirth) {
                if (monthNow == monthBirth) {
                    if (dayOfMonthNow < dayOfMonthBirth)
                        age--;// 当前日期在生日之前，年龄减一
                } else {
                    age--;// 当前月份在生日之前，年龄减一
                }
            }
        } catch (Exception ex) {
            System.out.println("年龄转换异常：" + ex.getMessage());
        }
        return age;
    }

    /**
     * 获取多少天前的零点
     *
     * @param day
     * @return
     */
    public static Date getDateZeroBefore(int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH) - day,
                0, 0, 0);
        return calendar.getTime();
    }

    /**
     * 判断是否超过多少小时 如：24
     *
     * @param tableTime 业务时间
     * @param hour      多少小时
     * @return boolean
     * @throws Exception
     */
    public static boolean judgmentDate(String tableTime, Integer hour) throws Exception {
        String currentTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());//当前时间

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-M-d HH:mm:ss");

        Date start = sdf.parse(tableTime);//业务时间

        Date end = sdf.parse(currentTime);//当前时间

        long cha = end.getTime() - start.getTime();

        if (cha < 0) {

            return false;
        }

        double result = cha * 1.0 / (1000 * 60 * 60);

        if (result <= 24) {

            return true;//是小于等于 hour 小时

        } else {

            return false;

        }

    }

    /**
     * 获取现在时间
     *
     * @return返回字符串格式 yyyy-MM-dd HH:mm:ss
     */
    public static String getStringDate() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * 获取现在时间
     *
     * @return 返回短时间字符串格式yyyy-MM-dd
     */
    public static String getStringDateShort() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * 获得任意时间
     */
    public static Date getWantTime(int hour) throws ParseException {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DAY_OF_YEAR, 0);
        c.set(Calendar.HOUR_OF_DAY, hour);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        String format = df.format(c.getTime());
        Date parse = df.parse(format);
        return parse;
    }

    /**
     * 获得任意时间
     */
    public static Date getWantTime(int hour, int day) throws ParseException {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DAY_OF_YEAR, day);
        c.set(Calendar.HOUR_OF_DAY, hour);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        String format = df.format(c.getTime());
        Date parse = df.parse(format);
        return parse;
    }

    /**
     * 计算两个日期之间相差的天数
     *
     * @param smdate 较小的时间
     * @param bdate  较大的时间
     * @return 相差天数
     * @throws ParseException
     */

    public static int daysBetween(Date smdate, Date bdate) throws ParseException {

        Calendar cal = Calendar.getInstance();

        cal.setTime(smdate);

        long time1 = cal.getTimeInMillis();

        cal.setTime(bdate);

        long time2 = cal.getTimeInMillis();

        long between_days = (time2 - time1) / (1000 * 3600 * 24);

        return Integer.parseInt(String.valueOf(between_days));

    }



    /**
     * 计算两个日期之间相差的分钟
     *
     * @param smdate 较小的时间
     * @param bdate  较大的时间
     * @return 相差分钟
     * @throws ParseException
     */

    public static Integer minuteBetween(Date smdate, Date bdate) throws ParseException {

        Calendar cal = Calendar.getInstance();

        cal.setTime(smdate);

        long time1 = cal.getTimeInMillis();

        cal.setTime(bdate);

        long time2 = cal.getTimeInMillis();

        long between_minute = (time2 - time1) / (1000 * 60);

        return Integer.parseInt(String.valueOf(between_minute));
    }

    /**
     * 获取当前时间所在周的周一和周日的日期时间
     *
     * @return
     */
    public static Map<String, String> getWeekDate() {
        Map<String, String> map = new HashMap();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        Calendar cal = Calendar.getInstance();
        // 设置一个星期的第一天，按中国的习惯一个星期的第一天是星期一
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        // 获得当前日期是一个星期的第几天
        int dayWeek = cal.get(Calendar.DAY_OF_WEEK);
        if (dayWeek == 1) {
            dayWeek = 8;
        }

        cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - dayWeek);// 根据日历的规则，给当前日期减去星期几与一个星期第一天的差值
        Date mondayDate = cal.getTime();
        String weekBegin = sdf.format(mondayDate);

        cal.add(Calendar.DATE, 4 + cal.getFirstDayOfWeek());
        Date sundayDate = cal.getTime();
        String weekEnd = sdf.format(sundayDate);

        map.put("mondayDate", weekBegin);
        map.put("sundayDate", weekEnd);
        return map;
    }

    /***
     * 获取本月的第一天和最后一天
     * @return
     */
    public static Map<String, String> getMonthDate() {
        Map<String, String> map = new HashMap();

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

        Calendar c = Calendar.getInstance();

        c.add(Calendar.MONTH, 0);

        c.set(Calendar.DAY_OF_MONTH, 1);//1:本月第一天

        String day1 = format.format(c.getTime());


//获取当前月最后一天

        Calendar ca = Calendar.getInstance();

        ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH));

        String day2 = format.format(ca.getTime());

        map.put("firstDay", day1);
        map.put("lastDay", day2);

        return map;

    }

    public static Date getEndTime() {
        Calendar calendar1 = Calendar.getInstance();
        Calendar calendar2 = Calendar.getInstance();
        calendar1.set(calendar2.get(Calendar.YEAR), calendar2.get(Calendar.MONTH), calendar2.get(Calendar.DAY_OF_MONTH),
                23, 59, 59);
        Date endOfDate = calendar1.getTime();
        return endOfDate;
    }

    /***
     * 获取当天的开始时间 如:yyy-mm-dd 00:00:00
     * @return
     */
    public static Date startOfDay() {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(new Date().getTime()), ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /***
     * 获取当天的结束时间 如:yyy-mm-dd 23:59:59
     * @return
     */
    public static Date endOfDay() {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(new Date().getTime()), ZoneId.systemDefault());
        LocalDateTime endOfDay = localDateTime.with(LocalTime.MAX);
        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /***
     *判断当前日期是星期几
     * @param pTime
     * @return
     * @throws Throwable
     */
    public static String dayForWeek(String pTime) throws Throwable {

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

        Date tmpDate = format.parse(pTime);

        Calendar cal = Calendar.getInstance();

        String[] weekDays = {"7", "1", "2", "3", "4", "5", "6"};

        try {

            cal.setTime(tmpDate);

        } catch (Exception e) {

            e.printStackTrace();

        }

        int w = cal.get(Calendar.DAY_OF_WEEK) - 1; // 指示一个星期中的某天。

        if (w < 0)

            w = 0;

        return weekDays[w];

    }

    public static Date fmtIso8061(String strDate) {
        if (StringUtils.isBlank(strDate)) {
            return null;
        }
        try {
            return new SimpleDateFormat(DATE_ISO8061).parse(strDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static List<String> getDayListByMonth(Integer year, Integer month) {
        List<String> strings = new ArrayList<>();
        int count = 0;
        if (month == 2) {
            //判断年是不是闰年
            if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
                count = 29;
            } else {
                count = 28;
            }
        } else if (month == 4 || month == 6 || month == 9 || month == 11) {
            count = 30;
        } else if (month == 1 || month == 3 || month == 5 || month == 7 || month == 8 || month == 10 || month == 12) {
            count = 31;
        }
        for(int i =1;i<=count;i++){
            strings.add("1");
        }
        return strings;
    }

    public static String getRandomNoBuDate(){
        String mark = "";

        for (int i = 0; i < 3; i++) {

            int random = (int) (Math.random() * 10);

            mark = mark + random;

        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");

        String timeStamp = simpleDateFormat.format(new Date());

        String newmark="F"+timeStamp + mark;

        return newmark;
    }

    //年月日组合时分秒
    public static Date makeDate(Date Time1, Date Time3) throws ParseException {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdf =new SimpleDateFormat("yyyy-MM-dd ");
        SimpleDateFormat sdf1 =new SimpleDateFormat("HH:mm:ss");
        Date Time2=null;
        if (Time1 != null) {
            String format1 = sdf.format(Time1);
            String format2 = sdf1.format(Time3);
            Date parse1 = df.parse(format1 + format2);
            Time2 = new Date(parse1.getTime());
        }
        return Time2;
    }

    /**
     * 判断某个日期是否在当前时间范围内
     * @param date
     * @param startDate
     * @param endDate
     * @return
     */
    public static boolean isDateInRange(Date date, Date startDate, Date endDate) {
        if (date == null || startDate == null || endDate == null || endDate.before(startDate)) {
            return false;
        }
        return !date.before(startDate) && !date.after(endDate);
    }

    public static boolean isSameDate(Date date1, Date date2){
        if (date1 == null || date2 == null) {
            return false;
        }
        String d1 = sdf_yyyyMMdd.format(date1);
        String d2 = sdf_yyyyMMdd.format(date2);
        return d1.equals(d2);
    }

    /**
     * 获取某个日期中午12点的时间
     * @param dateStr
     * @return
     */
    public static Date getDateNoon(Date dateStr){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dateStr);
        calendar.set(Calendar.HOUR_OF_DAY, 12);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);

        return calendar.getTime();
    }

    /**
     * 获取两个日期展示结果
     * @param
     * @return
     */
    public static String getTwoDatesDisplay(Date dateStart, Date dateEnd) {
        if (dateStart == null || dateEnd == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String start = sdf.format(dateStart).replaceAll("-",".");
        String end = sdf.format(dateEnd).replaceAll("-",".");
        return start + "-" + end;
    }
    /**
     * 获取日期的时分秒
     * @param
     * @return
     */
    public static String getHourTime(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        return sdf.format(date);
    }
    
}
