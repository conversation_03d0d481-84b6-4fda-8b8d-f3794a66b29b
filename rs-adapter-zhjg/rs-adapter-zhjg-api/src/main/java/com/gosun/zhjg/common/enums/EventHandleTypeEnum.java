package com.gosun.zhjg.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 所情管理岗位处置类型
 * prison_event_handle 表
 *
 */
public enum EventHandleTypeEnum {
	DISPOSE(1, "处置"),
	AUDIT(1, "审批"),
	;

	private Integer value;
	private String desc;

	EventHandleTypeEnum(Integer value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public Integer getValue() {
		return value;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(Integer value) {
		for (EventHandleTypeEnum e : EventHandleTypeEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}
}
