package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 关注人员信息表-推荐的待关注人员
 * 推荐原因code。字典：ATTENTION_SUGGESTION_REASON，枚举：AttentionSuggestionReasonEnum
 *
 * <AUTHOR>
 * @date 2025/2/26 17:33
 */
public enum AttentionSuggestionReasonEnum implements IEnum<String> {
	YGRY("YGRY", "严管人员"),
	GFXRY("GFXRY", "高风险人员"),
	XRSRY("XRSRY", "新入所人员"),
	JDXJRY("JDXJRY", "加戴械具人员"),
	ZBHRY("ZBHRY", "重病号人员"),
	DCFZRY("DCFZRY", "多次犯罪人员"),
	WGRY("WGRY", "违规人员"),
	WCNR("WCNR", "未成年人"),
	;

	@Getter
	private final String value;
	private final String desc;

	AttentionSuggestionReasonEnum(String value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(String value) {
		for (AttentionSuggestionReasonEnum e : AttentionSuggestionReasonEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}

	public static String getValue(String desc) {
		for (AttentionSuggestionReasonEnum e : AttentionSuggestionReasonEnum.values()) {
			if (e.desc.equals(desc)) {
				return e.value;
			}
		}
		return null;
	}
}