package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto;

import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("监室仓内外屏分页列表")
public class BaseDeviceTerminalPageDto extends AbstractPageQueryForm {
    @ApiModelProperty("监室id")
    private String roomId;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty(value = "监所编号",hidden = true)
    private String prisonId;

    @ApiModelProperty("仓内屏名称")
    private String internalName;

    @ApiModelProperty("仓外屏名称")
    private String warehouseName;


}
