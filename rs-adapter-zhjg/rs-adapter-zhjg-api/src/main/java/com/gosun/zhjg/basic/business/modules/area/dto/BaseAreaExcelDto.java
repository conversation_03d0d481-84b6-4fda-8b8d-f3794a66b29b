package com.gosun.zhjg.basic.business.modules.area.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 区域表
 */
@Data
@ApiModel("区域ExcelDto")
public class BaseAreaExcelDto  implements Serializable {

    private static final long serialVersionUID = 6255738183411898983L;

    private final static String header1="区域信息";

    private final static String header2="当区域类型为监室（0003）时，这部分内容才需要填写";

    private final static String header3="提讯室、会见室需要填写";


    @ExcelProperty({header1,"区域名称*"})
    private String areaName;

    @ExcelProperty({header1,"区域编码*"})
    private String areaCode;

    @ExcelProperty({header1,"父节点ID*"})
    private String parentId;

    @ExcelProperty({header1,"父节点名字"})
    private String parentName;

    @ExcelProperty({header1,"父区域的路径信息"})
    private String allParentId;

    @ExcelProperty({header1,"区域类型ID*"})
    private String areaType;

    @ExcelProperty({header2,"关押量"})
    private String imprisonmentAmount;

    @ExcelProperty({header2,"状态*"})
    private String status;

    @ExcelProperty({header2,"所属中队"})
    private String squadronId;

    @ExcelProperty({header2,"监室类型"})
    private String roomType;

    @ExcelProperty({header2,"性别类型"})
    private String roomSex;

    @ExcelProperty({header2,"监室面积*"})
    private BigDecimal roomArea;

    @ExcelProperty({header2,"人均铺位面积"})
    private BigDecimal avgBedsArea;

    @ExcelProperty({header2,"设计关押量"})
    private Integer planImprisonmentAmount;

    @ExcelProperty({header2,"是否一级风险"})
    private String isLevelRisk;

    @ExcelProperty({header2,"上游区域ID*"})
    private String selfAreaId;

    @ExcelProperty({header2,"风险等级"})
    private String fxdj;

    @ExcelProperty({header2,"预警时间"})
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date yjsj;

    @ExcelProperty({header3,"上游系统编码*"})
    private String roomId;

    @ExcelProperty({header3,"会见室状态*"})
    private String meetRoomStatus;

}
