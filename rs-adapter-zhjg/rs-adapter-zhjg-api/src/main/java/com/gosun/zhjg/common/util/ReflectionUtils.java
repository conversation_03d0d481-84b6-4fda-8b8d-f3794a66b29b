package com.gosun.zhjg.common.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import java.lang.reflect.*;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * 反射工具类.
 * 提供调用getter/setter方法, 访问私有变量, 调用私有方法, 获取泛型类型Class, 被AOP过的真实类等工具函数.
 *
 * <AUTHOR>
 * @version 2013-01-15
 */
@SuppressWarnings({"rawtypes","unchecked"})
public class ReflectionUtils {

    private static final String SETTER_PREFIX = "set";

    private static final String GETTER_PREFIX = "get";

    private static final String CGLIB_CLASS_SEPARATOR = "$$";

    private static Logger logger = LoggerFactory.getLogger(ReflectionUtils.class);

    /**
     * 调用Getter方法.
     * 支持多级，如：对象名.对象名.方法
     */
    public static Object invokeGetter(Object obj, String propertyName) {
        Object object = obj;
        for (String name : StringUtils.split(propertyName, ".")) {
            String getterMethodName = GETTER_PREFIX + StringUtils.capitalize(name);
            object = invokeMethod(object, getterMethodName, new Class[]{}, new Object[]{});
        }
        return object;
    }

    /**
     * 调用Setter方法, 仅匹配方法名。
     * 支持多级，如：对象名.对象名.方法
     */
    public static void invokeSetter(Object obj, String propertyName, Object value) {
        Object object = obj;
        String[] names = StringUtils.split(propertyName, ".");
        for (int i = 0; i < names.length; i++) {
            if (i < names.length - 1) {
                String getterMethodName = GETTER_PREFIX + StringUtils.capitalize(names[i]);
                object = invokeMethod(object, getterMethodName, new Class[]{}, new Object[]{});
            } else {
                String setterMethodName = SETTER_PREFIX + StringUtils.capitalize(names[i]);
                invokeMethodByName(object, setterMethodName, new Object[]{value});
            }
        }
    }

    /**
     * 直接读取对象属性值, 无视private/protected修饰符, 不经过getter函数.
     */
    public static Object getFieldValue(final Object obj, final String fieldName) {
        Field field = getAccessibleField(obj, fieldName);

        if (field == null) {
            throw new IllegalArgumentException("Could not find field [" + fieldName + "] on target [" + obj + "]");
        }

        Object result = null;
        try {
            result = field.get(obj);
        } catch (IllegalAccessException e) {
            logger.error("不可能抛出的异常{}", e.getMessage());
        }
        return result;
    }

    /**
     * 直接设置对象属性值, 无视private/protected修饰符, 不经过setter函数.
     */
    public static void setFieldValue(final Object obj, final String fieldName, final Object value) {
        Field field = getAccessibleField(obj, fieldName);

        if (field == null) {
            logger.error("Could not find field [" + fieldName + "] on target [" + obj + "]");
            return;
            //throw new IllegalArgumentException("Could not find field [" + fieldName + "] on target [" + obj + "]");
        }
        try {
            field.set(obj, convert(value, field.getType()));
        } catch (IllegalAccessException e) {
            logger.error("不可能抛出的异常:{}", e.getMessage());
        }
    }

    public static Object convert(Object object, Class<?> type) {
        if (object instanceof Number) {
            Number number = (Number) object;
            if (type.equals(byte.class) || type.equals(Byte.class)) {
                return number.byteValue();
            }
            if (type.equals(short.class) || type.equals(Short.class)) {
                return number.shortValue();
            }
            if (type.equals(int.class) || type.equals(Integer.class)) {
                return number.intValue();
            }
            if (type.equals(long.class) || type.equals(Long.class)) {
                return number.longValue();
            }
            if (type.equals(float.class) || type.equals(Float.class)) {
                return number.floatValue();
            }
            if (type.equals(double.class) || type.equals(Double.class)) {
                return number.doubleValue();
            }
        }
        if (type.equals(String.class)) {
            return object == null ? "" : object.toString();
        }
        return object;
    }

    /**
     * 直接调用对象方法, 无视private/protected修饰符.
     * 用于一次性调用的情况，否则应使用getAccessibleMethod()函数获得Method后反复调用.
     * 同时匹配方法名+参数类型，
     */
    public static Object invokeMethod(final Object obj, final String methodName, final Class<?>[] parameterTypes,
                                      final Object[] args) {
        Method method = getAccessibleMethod(obj, methodName, parameterTypes);
        if (method == null) {
            throw new IllegalArgumentException("Could not find method [" + methodName + "] on target [" + obj + "]");
        }

        try {
            return method.invoke(obj, args);
        } catch (Exception e) {
            throw convertReflectionExceptionToUnchecked(e);
        }
    }

    /**
     * 直接调用对象方法, 无视private/protected修饰符，
     * 用于一次性调用的情况，否则应使用getAccessibleMethodByName()函数获得Method后反复调用.
     * 只匹配函数名，如果有多个同名函数调用第一个。
     */
    public static Object invokeMethodByName(final Object obj, final String methodName, final Object[] args) {
        Method method = getAccessibleMethodByName(obj, methodName);
        if (method == null) {
            throw new IllegalArgumentException("Could not find method [" + methodName + "] on target [" + obj + "]");
        }

        try {
            return method.invoke(obj, args);
        } catch (Exception e) {
            throw convertReflectionExceptionToUnchecked(e);
        }
    }

    /**
     * 循环向上转型, 获取对象的DeclaredField, 并强制设置为可访问.
     * <p>
     * 如向上转型到Object仍无法找到, 返回null.
     */
    public static Field getAccessibleField(final Object obj, final String fieldName) {
        Validate.notNull(obj, "object can't be null");
        Validate.notBlank(fieldName, "fieldName can't be blank");
        for (Class<?> superClass = obj.getClass(); superClass != Object.class; superClass = superClass.getSuperclass()) {
            try {
                Field field = superClass.getDeclaredField(fieldName);
                makeAccessible(field);
                return field;
            } catch (NoSuchFieldException e) {//NOSONAR
                // Field不在当前类定义,继续向上转型
                continue;// new add
            }
        }
        return null;
    }

    /**
     * 循环向上转型, 获取对象的DeclaredMethod,并强制设置为可访问.
     * 如向上转型到Object仍无法找到, 返回null.
     * 匹配函数名+参数类型。
     * <p>
     * 用于方法需要被多次调用的情况. 先使用本函数先取得Method,然后调用Method.invoke(Object obj, Object... args)
     */
    public static Method getAccessibleMethod(final Object obj, final String methodName,
                                             final Class<?>... parameterTypes) {
        Validate.notNull(obj, "object can't be null");
        Validate.notBlank(methodName, "methodName can't be blank");

        for (Class<?> searchType = obj.getClass(); searchType != Object.class; searchType = searchType.getSuperclass()) {
            try {
                Method method = searchType.getDeclaredMethod(methodName, parameterTypes);
                makeAccessible(method);
                return method;
            } catch (NoSuchMethodException e) {
                // Method不在当前类定义,继续向上转型
                continue;// new add
            }
        }
        return null;
    }

    /**
     * 循环向上转型, 获取对象的DeclaredMethod,并强制设置为可访问.
     * 如向上转型到Object仍无法找到, 返回null.
     * 只匹配函数名。
     * <p>
     * 用于方法需要被多次调用的情况. 先使用本函数先取得Method,然后调用Method.invoke(Object obj, Object... args)
     */
    public static Method getAccessibleMethodByName(final Object obj, final String methodName) {
        Validate.notNull(obj, "object can't be null");
        Validate.notBlank(methodName, "methodName can't be blank");

        for (Class<?> searchType = obj.getClass(); searchType != Object.class; searchType = searchType.getSuperclass()) {
            Method[] methods = searchType.getDeclaredMethods();
            for (Method method : methods) {
                if (method.getName().equals(methodName)) {
                    makeAccessible(method);
                    return method;
                }
            }
        }
        return null;
    }

    /**
     * 改变private/protected的方法为public，尽量不调用实际改动的语句，避免JDK的SecurityManager抱怨。
     */
    public static void makeAccessible(Method method) {
        if ((!Modifier.isPublic(method.getModifiers()) || !Modifier.isPublic(method.getDeclaringClass().getModifiers()))
                && !method.isAccessible()) {
            method.setAccessible(true);
        }
    }

    /**
     * 改变private/protected的成员变量为public，尽量不调用实际改动的语句，避免JDK的SecurityManager抱怨。
     */
    public static void makeAccessible(Field field) {
        if ((!Modifier.isPublic(field.getModifiers()) || !Modifier.isPublic(field.getDeclaringClass().getModifiers()) || Modifier
                .isFinal(field.getModifiers())) && !field.isAccessible()) {
            field.setAccessible(true);
        }
    }

    /**
     * 通过反射, 获得Class定义中声明的泛型参数的类型, 注意泛型必须定义在父类处
     * 如无法找到, 返回Object.class.
     * eg.
     * public UserDao extends HibernateDao<User>
     *
     * @param clazz The class to introspect
     * @return the first generic declaration, or Object.class if cannot be determined
     */
    public static <T> Class<T> getClassGenricType(final Class clazz) {
        return getClassGenricType(clazz, 0);
    }

    /**
     * 通过反射, 获得Class定义中声明的父类的泛型参数的类型.
     * 如无法找到, 返回Object.class.
     * <p>
     * 如public UserDao extends HibernateDao<User,Long>
     *
     * @param clazz clazz The class to introspect
     * @param index the Index of the generic ddeclaration,start from 0.
     * @return the index generic declaration, or Object.class if cannot be determined
     */
    public static Class getClassGenricType(final Class clazz, final int index) {

        Type genType = clazz.getGenericSuperclass();

        if (!(genType instanceof ParameterizedType)) {
            logger.warn(clazz.getSimpleName() + "'s superclass not ParameterizedType");
            return Object.class;
        }

        Type[] params = ((ParameterizedType) genType).getActualTypeArguments();

        if (index >= params.length || index < 0) {
            logger.warn("Index: " + index + ", Size of " + clazz.getSimpleName() + "'s Parameterized Type: "
                    + params.length);
            return Object.class;
        }
        if (!(params[index] instanceof Class)) {
            logger.warn(clazz.getSimpleName() + " not set the actual class on superclass generic parameter");
            return Object.class;
        }

        return (Class) params[index];
    }

    public static Class<?> getUserClass(Object instance) {
        Assert.notNull(instance, "Instance must not be null");
        Class clazz = instance.getClass();
        if (clazz != null && clazz.getName().contains(CGLIB_CLASS_SEPARATOR)) {
            Class<?> superClass = clazz.getSuperclass();
            if (superClass != null && !Object.class.equals(superClass)) {
                return superClass;
            }
        }
        return clazz;

    }

    /**
     * 将反射时的checked exception转换为unchecked exception.
     */
    public static RuntimeException convertReflectionExceptionToUnchecked(Exception e) {
        if (e instanceof IllegalAccessException || e instanceof IllegalArgumentException
                || e instanceof NoSuchMethodException) {
            return new IllegalArgumentException(e);
        } else if (e instanceof InvocationTargetException) {
            return new RuntimeException(((InvocationTargetException) e).getTargetException());
        } else if (e instanceof RuntimeException) {
            return (RuntimeException) e;
        }
        return new RuntimeException("Unexpected Checked Exception.", e);
    }

    /**
     * 判断某个对象是否拥有某个属性
     *
     * @param obj       对象
     * @param fieldName 属性名
     * @return 有属性返回true
     * 无属性返回false
     */
    public static boolean hasField(final Object obj, final String fieldName) {
        Field field = getAccessibleField(obj, fieldName);
        if (field == null) {
            return false;
        }
        return true;

    }

    /**
     * 获取Method
     *
     * @param clazz
     * @param methodName
     * @param args
     * @return
     */
    public static Method getMethod(Class clazz, String methodName, Class... args) {
        Method method = null;
        try {
            method = clazz.getMethod(methodName, args);
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        }
        return method;
    }

    /**
     * 通过实体表单转换成为实体
     *
     * @param source
     * @param clazz
     * @param <T>
     * @return
     * @throws IllegalAccessException
     * @throws InstantiationException
     * @throws InvocationTargetException
     */
    public static <T> T updateEntityFormToEntity(Object source, Class<T> clazz) throws IllegalAccessException, InstantiationException, InvocationTargetException {

        Map<String, Method> entityFormMethods = new HashMap<>(16);
        getMethodBySource(source.getClass(), entityFormMethods);

        Map<String, Object> sourceValueMap = new HashMap<>(16);
        getValueBySource(source, source.getClass(), entityFormMethods, sourceValueMap);

        Object obj = clazz.newInstance();
        setEntityMethodsByGetMethodSet(sourceValueMap, clazz, obj);

        return (T) obj;
    }

    /**
     * 将表单值注入到Map
     *
     * @param source
     * @param clazz
     * @param entityFormMethods
     * @param sourceValueMap
     * @throws IllegalAccessException
     * @throws InvocationTargetException
     */
    private static void getValueBySource(Object source, Class clazz, Map<String, Method> entityFormMethods, Map<String, Object> sourceValueMap) throws IllegalAccessException, InvocationTargetException {
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            String getMethodName = tranFieldToGetterMethodName(field.getName());
            Method method = entityFormMethods.get(getMethodName);
            if (null == method) {
                continue;
            }

            Object value = method.invoke(source);
            sourceValueMap.put(field.getName(), value);
        }

        if (!clazz.getSuperclass().equals(Object.class)) {
            getValueBySource(source, clazz.getSuperclass(), entityFormMethods, sourceValueMap);
        }
    }

    /**
     * 获取包含父级的所有的 MethodMap
     *
     * @param sourceClazz
     * @param entityFormMethods
     */
    private static void getMethodBySource(Class sourceClazz, Map<String, Method> entityFormMethods) {
        Method[] methods = sourceClazz.getMethods();
        for (Method method : methods) {
            entityFormMethods.put(method.getName(), method);
        }

        if (!sourceClazz.getSuperclass().equals(Object.class)) {
            getMethodBySource(sourceClazz.getSuperclass(), entityFormMethods);
        }
    }

    /**
     * @param sourceValueMap
     * @param obj
     */
    private static void setEntityMethodsByGetMethodSet(Map<String, Object> sourceValueMap, Class clazz, Object obj) throws InvocationTargetException, IllegalAccessException {

        Method[] methods = clazz.getMethods();
        Map<String, Method> setMethods = new HashMap<>(methods.length);
        for (Method method : methods) {
            setMethods.put(method.getName(), method);
        }

        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {

            Object value = sourceValueMap.get(field.getName());
            if (null == value) {
                continue;
            }

            Method method = setMethods.get(tranFieldToSetterMethodName(field.getName()));
            if (null != method) {
                method.invoke(obj, value);
            }
        }

        if (!clazz.getSuperclass().equals(Object.class)) {
            setEntityMethodsByGetMethodSet(sourceValueMap, clazz.getSuperclass(), obj);
        }

    }

    /**
     * 默认不更新集合信息
     *
     * @param source
     * @param target
     */
    public static void updateFieldByClass(Object source, Object target) {
        updateFieldByClass(source.getClass(), source, target, false);
    }

    /**
     * 级联更新对象属性值，Object.class 不在更新范围
     *
     * @param clazz             类类型
     * @param source            源对象
     * @param target            更新对象
     * @param updateCollections 是否级联更新集合
     */
    public static void updateFieldByClass(Class clazz, Object source, Object target, boolean updateCollections) {

        Field[] fields = clazz.getDeclaredFields();
        Method[] methods = clazz.getDeclaredMethods();

        Map<String, Method> methodMap = new HashMap<>(methods.length);
        for (Method method : methods) {
            methodMap.put(method.getName(), method);
        }

        updateFieldMap(source, target, updateCollections, fields, methodMap);

        Class superClazz = clazz.getSuperclass();
        if (!superClazz.equals(Object.class)) {
        	logger.warn("映射类型:" + superClazz.getSimpleName());
            updateFieldByClass(superClazz, source, target, updateCollections);
        }

    }

    /**
     * 更新Filed
     *
     * @param source
     * @param target
     * @param updateCollections
     * @param fields
     * @param methodMap
     */
    private static void updateFieldMap(Object source, Object target, boolean updateCollections, Field[] fields, Map<String, Method> methodMap) {
        try {
            for (Field field : fields) {
                Method setMethod = methodMap.get(tranFieldToSetterMethodName(field.getName()));
                Method getMethod = methodMap.get(tranFieldToGetterMethodName(field.getName()));

                Object objValue = getMethod.invoke(source);
                if (objValue != null) {
                    if (objValue instanceof Collection && !updateCollections) {
                        continue;
                    }
                    if (setMethod != null) {
                        setMethod.invoke(target, objValue);
                    } else {
                    	logger.warn(tranFieldToSetterMethodName(field.getName()) + " 不存在！");
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String tranFieldToGetterMethodName(String field) {
        field = "get" + field.substring(0, 1).toUpperCase() + field.substring(1, field.length());
        return field;
    }

    public static String tranFieldToSetterMethodName(String field) {
        field = "set" + field.substring(0, 1).toUpperCase() + field.substring(1, field.length());
        return field;
    }

    /**
     *  将空字符串 设置为null
     * @param obj
     */
    public static void setEmptyStringToNull(Object obj) {

        Map<String, Method> setterMethods = new HashMap<>();
        Map<String, Method> getterMethods = new HashMap<>();
        putStringFiledToList(obj.getClass(), setterMethods, getterMethods);

        setterMethods.forEach((key, method) -> {
            try {

                Method getterMethod = getterMethods.get(key);
                Object objValue = getterMethod.invoke(obj);

                if (null != objValue && objValue instanceof String) {
                    if (StringUtils.isBlank((String) objValue)) {

                    	logger.debug("method:" + method.getName() + " 将值设置为 null");

                        String arg = null;
                        method.invoke(obj, arg);
                    }
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        });

    }

    private static void putStringFiledToList(Class clazz, Map<String, Method> setterMethods, Map<String, Method> getterMethods) {

        Method[] methods = clazz.getDeclaredMethods();
        Field[] fields = clazz.getDeclaredFields();

        Map<String, Method> tempMap = new HashMap<>();

        for (Method method : methods) {
            tempMap.put(method.getName(), method);
        }

        for (Field field : fields) {
            if (field.getType().equals(String.class)) {
                String setterMethod = tranFieldToSetterMethodName(field.getName());
                Method method = tempMap.get(setterMethod);
                if (null != method) {
                    setterMethods.put(field.getName(), method);
                }

                String getterMethod = tranFieldToGetterMethodName(field.getName());
                method = tempMap.get(getterMethod);
                if (null != method) {
                    getterMethods.put(field.getName(), method);
                }
            }
        }

        if (!clazz.getSuperclass().equals(Object.class)) {
            putStringFiledToList(clazz.getSuperclass(), setterMethods, getterMethods);
        }
    }
}
