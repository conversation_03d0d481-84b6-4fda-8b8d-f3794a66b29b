package com.gosun.zhjg.basic.business.user.dto;

import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户对应其他菜单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-09-16 15:37:51
 */
@Data
@ApiModel("用户对应其他菜单表分页Dto")
public class PermissionUserMenuPageDto extends AbstractPageQueryForm {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 用户主键
     */
    @ApiModelProperty(value = "用户主键")
    private String userid;
    /**
     * 菜单编号
     */
    @ApiModelProperty(value = "菜单编号")
    private String menuCode;

}
