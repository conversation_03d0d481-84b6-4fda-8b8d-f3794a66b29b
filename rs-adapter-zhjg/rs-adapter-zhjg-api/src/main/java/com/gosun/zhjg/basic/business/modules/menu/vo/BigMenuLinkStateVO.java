package com.gosun.zhjg.basic.business.modules.menu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * 分组管理-授权-大菜单列表。获取菜单与分组的关联状态。会列出所有菜单标记出是否关联该分组
 * com.gosun.zhjg.basic.business.modules.menu.controller.BaseMenuGroupController.allBigMenuLinkState(String)
 *
 * <AUTHOR>
 *
 */
@Data
public class BigMenuLinkStateVO {

	private String menuId;

//	private String menuCode;

	private String menuName;

	/**
	 * true 已关联， false 未关联
	 */
	private Boolean mark;

	//别称
	private String alias;

	/**
	 * 菜单分类.字典：BASE_MENU_CLASSIFICATION
	 */
	@JsonIgnore
	@JSONField(serialize = false)
	private String classification;
}
