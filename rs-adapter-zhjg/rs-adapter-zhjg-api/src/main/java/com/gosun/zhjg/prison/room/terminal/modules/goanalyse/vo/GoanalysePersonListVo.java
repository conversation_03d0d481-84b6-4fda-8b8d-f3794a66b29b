package com.gosun.zhjg.prison.room.terminal.modules.goanalyse.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class GoanalysePersonListVo {

    @ApiModelProperty("id")
    private String id;


    @ApiModelProperty("人员编号")
    private String faceCode;

    @ApiModelProperty("人员姓名")
    private String userName;

    @ApiModelProperty("图片base64数据")
    private String imagePath;

    @ApiModelProperty("创建时间")
    private Date registerTime;

    @ApiModelProperty("创建人")
    private String registerName;

    @ApiModelProperty("在押人员1，民警2.外开人员3")
    private Integer groupId;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("异常信息")
    private String errMsg;

    @ApiModelProperty("错误码")
    private Integer errCode;

    @ApiModelProperty("数据原")
    private Integer dataSource;

    @ApiModelProperty("人脸code 不成功的人脸为null")
    private Integer faceLibCode;
}
