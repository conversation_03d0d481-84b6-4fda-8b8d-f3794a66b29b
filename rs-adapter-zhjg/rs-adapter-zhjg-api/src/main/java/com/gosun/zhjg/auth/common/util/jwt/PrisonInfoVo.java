package com.gosun.zhjg.auth.common.util.jwt;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
*<AUTHOR>
*@email <EMAIL>
*@date 2020-07-13
*/


@Data
public class PrisonInfoVo implements Serializable {


    private static final long serialVersionUID = -7565398533090752955L;

    public PrisonInfoVo() {
    }

    public PrisonInfoVo(String prisonId, String prisonName) {
        this.prisonId = prisonId;
        this.prisonName = prisonName;
    }

    @ApiModelProperty("监所id")
    private String prisonId;

    @ApiModelProperty("监所名字")
    private String prisonName;

    @ApiModelProperty("监所类型")
    private Integer prisonType;

    @ApiModelProperty("监所简称")
    private String prisonAbbreviation;
}
