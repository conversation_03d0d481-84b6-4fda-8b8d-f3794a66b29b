package com.gosun.zhjg.common.feign.intercept.response;



import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Objects;

/**
 * feign响应拦截抽象处理类，子类设置响应的类型，然后拦截响应进行判断处理
 * @param <T>
 */
public abstract class AbstractFeignResponseHandle<T> implements FeignResponseHandle {


    private Class<?> responseClass;

    public AbstractFeignResponseHandle() {
        Type superClass = this.getClass().getGenericSuperclass();
        if (superClass instanceof Class) {
            throw new IllegalArgumentException("Internal error: TypeReference constructed without actual type information");
        } else {
            Type actualTypeArgument = ((ParameterizedType) superClass).getActualTypeArguments()[0];
            if (actualTypeArgument instanceof Class) {
                responseClass = (Class<?>) actualTypeArgument;
                return;
            }

            if (actualTypeArgument instanceof ParameterizedType) {
                Type rawType = ((ParameterizedType) actualTypeArgument).getRawType();
                if (rawType instanceof Class) {
                    responseClass = (Class<?>) rawType;
                    return;
                }
            }

            if (responseClass == null) {
                throw new IllegalArgumentException("泛型参数设置异常");
            }
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public Object responseHandle(Object response) {
        if (Objects.isNull(response)) {
            return null;
        }
        Class<?> paramResponseClass = response.getClass();
        if (!Objects.equals(responseClass, paramResponseClass)) {
            throw new IllegalArgumentException("feign 响应拦截的返回类型和对应的处理类不一致，feign 返回："
                    + paramResponseClass.getName() + "，处理类处理类型：" + responseClass.getName());
        }
        T t = (T) response;
        return doResponseHandle(t);
    }


    protected abstract T doResponseHandle(T response);


}
