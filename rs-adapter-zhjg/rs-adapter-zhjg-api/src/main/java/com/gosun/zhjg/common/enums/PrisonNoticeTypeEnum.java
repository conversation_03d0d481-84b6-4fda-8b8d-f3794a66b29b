package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 通知，交办类型
 * 
 * <AUTHOR>
 *
 */
public enum PrisonNoticeTypeEnum implements IEnum<Integer> {
	TZ(1, "通知"), JB(2, "交办");

	private Integer value;
	private String desc;

	PrisonNoticeTypeEnum(Integer value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public Integer getValue() {
		return value;
	}

	public void setValue(Integer value) {
		this.value = value;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(Integer value) {
		for (PrisonNoticeTypeEnum e : PrisonNoticeTypeEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}

	public static Integer getValue(String desc) {
		for (PrisonNoticeTypeEnum e : PrisonNoticeTypeEnum.values()) {
			if (e.desc.equals(desc)) {
				return e.value;
			}
		}
		return null;
	}
}