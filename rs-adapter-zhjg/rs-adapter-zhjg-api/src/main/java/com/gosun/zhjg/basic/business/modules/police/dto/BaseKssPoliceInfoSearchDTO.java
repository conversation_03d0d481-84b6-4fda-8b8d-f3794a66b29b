package com.gosun.zhjg.basic.business.modules.police.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
* base_kss_police_info 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2022-12-30
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="BaseKssPoliceInfoSearchDTO对象", description="")
public class BaseKssPoliceInfoSearchDTO extends AbstractPageQueryForm implements Serializable {


   @JsonProperty("prisonId")
   @ApiModelProperty(value = "监所编号")
   private String prisonId;

   @JsonProperty("name")
   @ApiModelProperty(value = "姓名")
   private String name;

   @JsonProperty("policeNum")
   @ApiModelProperty(value = "警号")
   private String policeNum;

   @JsonProperty("status")
   @ApiModelProperty(value = "状态")
   private Integer status;



}
