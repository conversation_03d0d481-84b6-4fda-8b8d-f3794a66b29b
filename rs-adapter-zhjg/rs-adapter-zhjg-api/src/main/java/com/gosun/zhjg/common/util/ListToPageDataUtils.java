package com.gosun.zhjg.common.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gosun.zhjg.common.msg.PageVO;

import java.util.ArrayList;
import java.util.List;

/**List分页工具类
*<AUTHOR>
*@email <EMAIL>
*@date 2021/12/17
*/
public class ListToPageDataUtils {
    /**
     * 数据集合的分页方法，根据传入总共的数据跟页码，返回页码所需要显示多少条的数据
     * <BR/>采用泛型的方法，即为，list中为什么类型的数据就返回什么类型的数据
     * @param List 带有需要进行分页的数据集合
     * @param page 第几页
     * @param rows 显示多少条数据
     * @return 进过分页之后返回的数据
     */
    public static<T> PageVO getListToPageData(Integer page, Integer rows, List<T> List) {
        PageVO data = new PageVO();
        List<T> datepaging = datepaging(List, page, rows);
        //总页数
        int size = List.size();
        int totalPage = size / rows;
        //总条数
        data.setTotal(size);
        //每页的内容
        data.setRows(datepaging);
        return data;
    }

    /**
     * 新接口规范分页查询Total以及list返回
     * @param page 分页
     * @param List 查询的结果集
     * @param <T>
     * @return
     */
    public static<T> PageVO returnToPageVo(Page page, List<T> List) {
        page.setRecords(List);
        PageVO<T> pageVO = new PageVO<>();
        pageVO.setTotal((int) page.getTotal());
        pageVO.setRows(List);
        return pageVO;
    }

    /**
     * 数据集合的分页方法，根据传入总共的数据跟页码，返回页码所需要显示多少条的数据
     * <BR/>采用泛型的方法，即为，list中为什么类型的数据就返回什么类型的数据
     * @param list 带有需要进行分页的数据集合
     * @param page 第几页
     * @param rows 显示多少条数据
     * @return 进过分页之后返回的数据
     */
    private static <T> List<T> datepaging(List<T> list, Integer page, Integer rows) {
        /*
         * 经过测试发现当page为0或者小于时，也就是第0页时，程序会报错，所以需要处理一下page的值
         * 先进行空值的判断，避免程序出现null异常
         * 当page的值小于等于0时，我们让它的值为1
         */
        //参数的校验 当传入过来的list集合为null时，先进行实例化
        if (list == null) {
            list = new ArrayList<T>();
        }
        //当传入过来的page为null时，先进行赋值操作
        if ((Object) page == null) {
            page = 1;
        }
        //当传入过来的rows为null时，先进行赋值操作
        if ((Object) rows == null) {
            rows = 1;
        }
        if (page <= 0) {
            page = 1;
        }
        //记录一下数据一共有多少条
        int totalitems = list.size();
        //实例化一个接受分页处理之后的数据
        List<T> afterList = new ArrayList<T>();
        /*
         * 进行分页处理,采用for循环的方式来进行处理
         * 首先for循环中，i应该从哪里开始:i应该从 (当前是第几页 -1 乘以 条数) 开始
         * 然后for循环应该到哪里结束，也就是i应该小于:判断(开始的索引+显示条数)是不是大于总条数，如果大于就是总条数，如果小于就是(开始的索引+显示条数)
         * 然后让i++
         */
        for (int i = (page - 1) * rows; i < (((page - 1) * rows) + rows > totalitems ? totalitems : ((page - 1) * rows) + rows); i++) {
            //然后将数据存入afterList中
            afterList.add(list.get(i));
        }
        //然后将处理后的数据集合进行返回
        return afterList;
    }

}
