package com.gosun.zhjg.device.server.modules.inscreen.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class TerminalInfoVO {

    @ApiModelProperty("终端类型：0-主机，1-分机")
    public int terminalType;

    @ApiModelProperty("终端类型-字典描述")
    public String getTerminalTypeDisplayName() {
        if (terminalType == 8) {
            return "门口机";
        } else if (terminalType == 7) {
            return "交互终端";
        } else if (terminalType == 0) {
            // 主机没有类型默认0
            if (StringUtils.isNotBlank(name) && name.contains("主机")) {
                return "主机";
            }
            return "普通分机";
        }
        return null;
    }

    @ApiModelProperty("终端编号")
    public int displayNum;

    @ApiModelProperty("终端IP地址")
    public String netAddr;

    @ApiModelProperty("终端名称")
    public String name;

    @ApiModelProperty("终端型号")
    public String model;

    @ApiModelProperty("描述信息")
    public String desc;

    public String getDesc() {
        if (this.desc != null && this.desc.endsWith("$")) {
            // 描述信息和备注通过$ 隔开
            return this.desc.substring(0, this.desc.length() - 1);
        }
        return this.desc;
    }

    /**
     * 后拓展属性
     */
    @ApiModelProperty("在线状态 0离线 1在线")
    private Integer state;
    public String stateDisplayName;
}
