package com.gosun.zhjg.basic.business.modules.area.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 楼层所含区域VO
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-08-13 08:57:25
 */
@Data
@ApiModel("楼层所含区域VO")
public class BaseAreaFloorVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    private String areaName;
    /**
     * 区域类型
     */
    @ApiModelProperty(value = "区域类型")
    private String areaType;

}
