package com.gosun.zhjg.basic.business.modules.area.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("电子地图区域树Dto")
public class ElectronicMapAreaTreeDto {

    /**
     * 监所id
     */
    @ApiModelProperty(value = "监所id")
    private String prisonId;
    /**
     * 地图编号
     */
    @ApiModelProperty(value = "地图编号")
    private String mapId;
    /***
     * 区域编号
     */
    @ApiModelProperty(value = "区域编号")
    private String areaId;

}
