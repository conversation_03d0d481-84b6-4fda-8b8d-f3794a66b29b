package com.gosun.zhjg.prison.room.terminal.modules.terminal.dto;

import com.gosun.zhjg.prison.room.terminal.modules.home.dto.BaseSystemRuleConfigKeyValueDto;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalMenuVO;
import lombok.Data;

import java.util.List;

/**
 * 终端配置-仓外屏配置
 *
 * <AUTHOR>
 * @date 2024/12/13 10:23
 */
@Data
public class TerminalSettingsCwpDto {
	private String prisonId;
	private List<TerminalMenuVO> menuTreeList;
	private List<BaseSystemRuleConfigKeyValueDto> ruleConfigList;
}
