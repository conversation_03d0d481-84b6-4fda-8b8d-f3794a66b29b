package com.gosun.zhjg.basic.business.modules.prison.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.Date;

/**
 * 监所基础信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-08-12 19:44:28
 */
@Data
@ApiModel("监所基础信息VO")
public class BasePrisonInfoVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 监所id
	 */
	@ApiModelProperty(value = "监所id")
	private String id;
	/**
	 * 监所名称
	 */
	@ApiModelProperty(value = "监所名称")
	private String prisonName;
	/**
	 * 监所简称
	 */
	@ApiModelProperty(value = "监所简称")
	private String prisonAbbreviation;
	/**
	 * 组织机构id
	 */
	@ApiModelProperty(value = "组织机构id")
	private String orgId;
	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private String status;
//    /**
//     * 删除标记
//     */
//    @ApiModelProperty(value = "删除标记")
//    private Integer delFlag;
	/**
	 * 所领导用户id
	 */
	@ApiModelProperty(value = "所领导用户id")
	private Integer leaderId;
	/**
	 * 所领导名称
	 */
	@ApiModelProperty(value = "所领导名称")
	private String leaderName;
	/**
	 * 监所类型
	 */
	@ApiModelProperty(value = "监所类型")
	private String prisonType;

	@ApiModelProperty(value = "监所类型")
	private String prisonTypeDisplayName;

	@ApiModelProperty(value = "是否可删除。 该机构下绑定有角色不允许直接删除")
	private Boolean deleteAble;

	private String createUserName;
	private Date createTime;
	private String updateUserName;
	private Date updateTime;

	@ApiModelProperty(value = "所属支队")
	private String branchId;

	@ApiModelProperty(value = "所属支队名字")
	private String branchIdName;

	@ApiModelProperty(value = "0-非支队 1-支队")
	private Integer isBranch;

	/**
	 * 地址
	 */
	@ApiModelProperty(value = "地址",required = true)
	private String dz;

	/**
	 * 地址
	 */
	@ApiModelProperty(value = "地址中文",required = true)
	private String dzName;
	/**
	 * 详址
	 */
	@ApiModelProperty(value = "详址")
	private String xz;
	/**
	 * 电话
	 */
	@ApiModelProperty(value = "电话")
	private String dh;
	/**
	 * 传真
	 */
	@ApiModelProperty(value = "传真")
	private String cz;
	/**
	 * 邮政编码
	 */
	@ApiModelProperty(value = "邮政编码")
	private String yzbm;
	/**
	 * 电子信箱
	 */
	@ApiModelProperty(value = "电子信箱")
	private String dzxx;
	/**
	 * 看守所等级
	 */
	@ApiModelProperty(value = "监所等级")
	private String jsdj;

	@ApiModelProperty(value = "监所等级 中文")
	private String jsdjDisplayName;
	/**
	 * 看守所规模
	 */
	@ApiModelProperty(value = "监所规模")
	private String jsgm;

	@ApiModelProperty(value = "监所规模 中文")
	private String jsgmDisplayName;
	/**
	 * 民警总数
	 */
	@ApiModelProperty(value = "民警总数")
	private Integer mjzs;
	/**
	 * 编制人数
	 */
	@ApiModelProperty(value = "编制人数")
	private Integer bzrs;
	/**
	 * 监室数
	 */
	@ApiModelProperty(value = "监室数")
	private Integer jsh;
	/**
	 * 设计容量
	 */
	@ApiModelProperty(value = "设计容量")
	private Integer sjrl;
	/**
	 * 协警
	 */
	@ApiModelProperty(value = "协警")
	private Integer xj;
	/**
	 * 职工
	 */
	@ApiModelProperty(value = "职工")
	private Integer zg;
	/**
	 * 文职
	 */
	@ApiModelProperty(value = "文职")
	private Integer wz;
	/**
	 * 建设时间
	 */
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JSONField(format="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	@ApiModelProperty(value = "建设时间")
	private Date jssj;
	/**
	 * 启用时间
	 */
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JSONField(format="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	@ApiModelProperty(value = "启用时间")
	private Date qysj;
	/**
	 * 单人监室
	 */
	@ApiModelProperty(value = "单人监室")
	private Integer drjs;
	/**
	 * 总建筑面积
	 */
	@ApiModelProperty(value = "总建筑面积")
	private BigDecimal zjzmj;
	/**
	 * 监区面积
	 */
	@ApiModelProperty(value = "监区面积")
	private BigDecimal jqmj;
	/**
	 * 驻所武警数
	 */
	@ApiModelProperty(value = "驻所武警数")
	private Integer zswjs;
	/**
	 * 询问室
	 */
	@ApiModelProperty(value = "询问室")
	private Integer xws;
	/**
	 * 律师会见室
	 */
	@ApiModelProperty(value = "律师会见室")
	private Integer lxhjs;
	/**
	 * 家属会见室
	 */
	@ApiModelProperty(value = "家属会见室")
	private Integer jshjs;
	/**
	 * 医生数量
	 */
	@ApiModelProperty(value = "医生数量")
	private Integer yssl;
	/**
	 * 经度
	 */
	@ApiModelProperty(value = "经度")
	private String jd;
	/**
	 * 纬度
	 */
	@ApiModelProperty(value = "纬度")
	private String wd;

	/**
	 * 地图坐标
	 */
	@ApiModelProperty(value = "地图坐标")
	private String mapPostion;
	/**
	 * 地图id
	 */
	@ApiModelProperty(value = "地图id")
	private String mapId;
	/**
	 * 标签位置
	 */
	@ApiModelProperty(value = "标签位置")
	private String mapTag;
	/**
	 * 提示框位置
	 */
	@ApiModelProperty(value = "提示框位置")
	private String tskwz;

	@ApiModelProperty(value = "护士数量")
	private Integer hssl;

	@ApiModelProperty(value = "监所图片")
	private String photoUrl;

	private String zbkssj;
	private String zbjssj;
}
