package com.gosun.zhjg.basic.business.modules.area.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel("监所监区VO")
public class BasePrisonAreaVO implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "监区ID")
    private String areaId;

    @ApiModelProperty(value = "监区名称")
    private String areaName;
}
