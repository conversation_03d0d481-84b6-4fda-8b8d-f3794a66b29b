package com.gosun.zhjg.device.server.modules.inscreen.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * 仓内屏新增Dto
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-09-15 10:55:29
 */
@Data
@ApiModel("仓内屏新增Dto")
public class BaseDeviceInscreenSaveDto {

    /**
     * 主键
     */
    @NotNull(message = "主键 不能为空!")
    @ApiModelProperty(value = "主键", required = true)
    private String id;
    /**
     * 序列号
     */
    @NotNull(message = "序列号 不能为空!")
    @ApiModelProperty(value = "序列号", required = true)
    private String serialNumber;
    /**
     * 设备编号
     */
    @NotNull(message = "设备编号 不能为空!")
    @ApiModelProperty(value = "设备编号", required = true)
    private String deviceId;
    /**
     * 设备ip
     */
    @NotNull(message = "设备ip 不能为空!")
    @ApiModelProperty(value = "设备ip", required = true)
    private String deviceIp;
    /**
     * 监室号
     */
    @NotNull(message = "监室号 不能为空!")
    @ApiModelProperty(value = "监室号", required = true)
    private String roomId;

}
