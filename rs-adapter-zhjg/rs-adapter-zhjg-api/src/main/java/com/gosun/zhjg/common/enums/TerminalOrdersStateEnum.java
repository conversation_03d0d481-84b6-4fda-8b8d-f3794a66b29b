package com.gosun.zhjg.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 订单状态：TERMINAL_ORDERS_STATE
 *
 * <AUTHOR>
 * @date 2024/9/9 19:12
 */
public enum TerminalOrdersStateEnum implements IEnum<Integer> {
	GJDSH(0, "管教待审核"), //
	GJSHTG(1, "管教审核通过"), //
	GJSHBTG(2, "管教审核不通过"), //
	DGSHTG(3, "代购审核通过"), //
	DGSHBTG(4, "代购审核不通过"), //
	YPH(5, "已派货"), //
	YQS(6, "已签收"), //
	RYCH(7, "人员撤回"), //
	;

	private Integer value;
	private String desc;

	TerminalOrdersStateEnum(Integer value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public Integer getValue() {
		return value;
	}

	public void setValue(Integer value) {
		this.value = value;
	}

	@JsonValue
	public String getDesc() {
		return desc;
	}

	public static String getDesc(Integer value) {
		for (TerminalOrdersStateEnum e : TerminalOrdersStateEnum.values()) {
			if (e.value.equals(value)) {
				return e.desc;
			}
		}
		return null;
	}

	public static Integer getValue(String desc) {
		for (TerminalOrdersStateEnum e : TerminalOrdersStateEnum.values()) {
			if (e.desc.equals(desc)) {
				return e.value;
			}
		}
		return null;
	}
}