package com.gosun.zhjg.common.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.util.Date;

/**
 * 基础类
 *
 * <AUTHOR> on 2020/2/21
 * @email <EMAIL>
 * @date 2020/2/21
 */
@Data
public class BaseEntity {
    /**
     * 最后更新人ID
     */
    private String updateUserId;
    /**
     * 最后更新人
     */
    private String updateUserName;
    /**
     * 最后更新时间
     */
    private Date updateTime;
    /**
     * 删除标记
     */
    @TableLogic
    private Integer delFlag;
    
}
