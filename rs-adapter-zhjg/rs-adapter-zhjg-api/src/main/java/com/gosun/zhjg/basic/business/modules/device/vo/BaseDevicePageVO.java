package com.gosun.zhjg.basic.business.modules.device.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-10-19 11:40:50
 */
@Data
@ApiModel("设备表分页VO")
public class BaseDevicePageVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 设备编码
     */
    @ApiModelProperty(value = "设备编码")
    private String deviceCode;
    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String deviceName;
    /**
     * 设备类型
     */
    @ApiModelProperty(value = "设备类型")
    private String deviceTypeId;

    @ApiModelProperty(value = "设备类型")
    private String deviceTypeIdDisplayName;
    /**
     * 所属区域
     */
    @ApiModelProperty(value = "所属区域")
    private String areaId;

    @ApiModelProperty(value = "所属区域")
    private String areaIdDisplayName;
    /**
     * 厂家
     */
    @ApiModelProperty(value = "厂家")
    private String factoryId;
    /**
     * 型号
     */
    @ApiModelProperty(value = "型号")
    private String modelId;
    /**
     * 协议
     */
    @ApiModelProperty(value = "协议")
    private String protocolId;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String status;

    @ApiModelProperty(value = "是否启用")
    private String statusDisplayName;
    /**
     * 最后更新人ID
     */
    @ApiModelProperty(value = "最后更新人ID")
    private String updateUserId;
    /**
     * 最后更新人
     */
    @ApiModelProperty(value = "最后更新人")
    private String updateUserName;
    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后更新时间")
    private Date updateTime;
    /**
     * 删除标记
     */
    @ApiModelProperty(value = "删除标记")
    private Integer delFlag;
    /**
     * ip地址
     */
    @ApiModelProperty(value = "ip地址")
    private String ipAddress;
    /**
     * 点位名称
     */
    @ApiModelProperty(value = "点位名称")
    private String pointName;
    /**
     * 监室号
     */
    @ApiModelProperty(value = "监室号")
    private String roomId;
    /**
     * 设备状态
     */
    @ApiModelProperty(value = "设备状态")
    private String deviceStatus;
    /**
     * 所属监所
     */
    @ApiModelProperty(value = "所属监所")
    private String prisonId;

    @ApiModelProperty("通道编号")
    private String channelId;

    @ApiModelProperty("通道名称")
    private String channelName;

    @ApiModelProperty("录像级联类型")
    private Integer storageType;

}
