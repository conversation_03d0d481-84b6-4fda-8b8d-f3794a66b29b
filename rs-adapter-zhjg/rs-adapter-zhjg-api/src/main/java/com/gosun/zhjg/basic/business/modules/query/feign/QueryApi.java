package com.gosun.zhjg.basic.business.modules.query.feign;

import com.gosun.zhjg.basic.business.modules.query.dto.PageDto;
import com.gosun.zhjg.common.msg.R;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(value = "zhjg-basic-business",path = "base/query", contextId = "base/query")
public interface QueryApi {

    @ApiOperation(value = "通用查询列表")
    @PostMapping("/findByPage")
    R findByPage(@RequestBody PageDto pageDto);

    @ApiOperation(value = "通用表头")
    @PostMapping("/findByTitle")
    R findByTitle(@RequestBody PageDto pageDto);
}
