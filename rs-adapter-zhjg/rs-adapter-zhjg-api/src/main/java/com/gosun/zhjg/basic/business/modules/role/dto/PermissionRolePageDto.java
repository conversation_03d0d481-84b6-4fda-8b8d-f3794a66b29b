package com.gosun.zhjg.basic.business.modules.role.dto;

import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 角色表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-05-11 20:09:15
 */
@Data
@ApiModel("角色表分页Dto")
public class PermissionRolePageDto extends AbstractPageQueryForm {

	@ApiModelProperty(value = "是否分页，默认是", required = true)
	private Boolean page;

	/**
	 * 角色ID
	 */
	@ApiModelProperty(value = "角色ID", required = true)
	private Integer roleId;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注", required = true)
	private String remark;
	/**
	 * 角色名称
	 */
	@ApiModelProperty(value = "角色名称", required = true)
	private String rolename;
	/**
	 * 未知1
	 */
	@ApiModelProperty(value = "未知1", required = true)
	private String roleorgcode;
	/**
	 * 未知2
	 */
	@ApiModelProperty(value = "未知2", required = true)
	private String roleorgname;
	/**
	 * 角色类型
	 */
	@ApiModelProperty(value = "角色类型", required = true)
	private String roletypecode;
	/**
	 * 岗位编码（新增）
	 */
	@ApiModelProperty(value = "岗位编码（新增）", required = true)
	private String rolepost;

	/**
	 * 根据角色ID和监所ID查询用户拥有的角色列表
	 */
	private String userid;
}
