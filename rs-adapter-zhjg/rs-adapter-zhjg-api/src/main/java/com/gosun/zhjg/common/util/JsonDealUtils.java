package com.gosun.zhjg.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.Iterator;
import java.util.Set;


/**
 *
  * <AUTHOR>
 *  @date 2023-07-03 13:48:08
 */
public class JsonDealUtils {


    public static JSONObject getNoNullValue(String json) {
        JSONObject objTem= JSON.parseObject(json);
        JSONObject objRel=JSON.parseObject(json);
        return deal(objTem,objRel);
    }

    private static JSONObject deal(JSONObject objTem,JSONObject objRel) {
        Set<String> keySet = objTem.keySet();
        Iterator<String> iterator = keySet.iterator();
        while(iterator.hasNext()) {
            String temp =  iterator.next();
            Object objR = objTem.get(temp);
            if(temp==null||"".equals(temp)||"null".equals(temp)) {
                objRel.remove(temp);
                continue;
            }
            if(objR==null||"".equals(objR.toString())||"null".equals(objR.toString())||"[]".equals(objR.toString())||"{}".equals(objR.toString())) {
                objRel.remove(temp);
                continue;
            }
            if(objR instanceof JSONObject) {
                JSONObject j=(JSONObject)objR;
                JSONObject object2 = (JSONObject)objRel.get(temp);
                deal(j,object2);
                continue;
            }
            if(objR instanceof JSONArray) {
                JSONArray jsonArray = objTem.getJSONArray(temp);
                JSONArray jsonArray2 = objRel.getJSONArray(temp);
                for(int i=0;i<jsonArray.size();i++) {
                    deal(jsonArray.getJSONObject(i),jsonArray2.getJSONObject(i));
                }
            }
        }
        return objRel;
    }
}

