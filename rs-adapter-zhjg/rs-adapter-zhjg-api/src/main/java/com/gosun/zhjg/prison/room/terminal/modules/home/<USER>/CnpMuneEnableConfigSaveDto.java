package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 *
 * <AUTHOR>
 *
 */
@Data
@ApiModel("仓内屏菜单启用配置新增Dto")
public class CnpMuneEnableConfigSaveDto {
	/**
	 * $column.comments
	 */
	@NotNull(message = "菜单编码 不能为空!")
	@ApiModelProperty(value = "$column.comments", required = true)
	private String menuCode;
	/**
	 * $column.comments
	 */
	@NotNull(message = "监室号 不能为空!")
	@ApiModelProperty(value = "$column.comments", required = true)
	private String roomId;
}
