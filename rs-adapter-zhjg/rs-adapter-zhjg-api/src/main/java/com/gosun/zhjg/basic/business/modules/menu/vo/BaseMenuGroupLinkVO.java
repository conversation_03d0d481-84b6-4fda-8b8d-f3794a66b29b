package com.gosun.zhjg.basic.business.modules.menu.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 菜单与分组联系表
 *
 * <AUTHOR>
 *
 */
@Data
@ApiModel("菜单与分组联系表VO")
public class BaseMenuGroupLinkVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String id;
	/**
	 * base_menu表ID
	 */
	@ApiModelProperty(value = "base_menu表ID")
	private String menuId;
	/**
	 * base_menu_group表ID
	 */
	@ApiModelProperty(value = "base_menu_group表ID")
	private String groupId;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Date updateTime;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String updateUsername;

}
