package com.gosun.zhjg.basic.business.modules.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UpdateBaseSystemInfoUrlDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 链接名称
     */
    @ApiModelProperty(value = "链接名称")
    private String urlName;

    /**
     * 链接地址
     */
    @ApiModelProperty(value = "链接地址")
    private String url;

    /**
     * 是否展示 0：否 1：是
     */
    @ApiModelProperty(value = "是否展示 0：否 1：是")
    private String isShow;
}
