package com.gosun.zhjg.basic.business.modules.room.dto;

import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 监室表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-05-09 16:12:20
 */
@Data
@ApiModel("监室表分页Dto")
public class AreaPrisonRoomPageDto extends AbstractPageQueryForm {

//    /**
//     * 监室ID
//     */
//    @ApiModelProperty(value = "监室ID", required = true)
//    private String id;
    /**
     * 监室名称
     */
    @ApiModelProperty("业务id")
    private String id;

    @ApiModelProperty(value = "监室名称", required = false)
    private String roomName;

    @ApiModelProperty(value = "用户id", required = false)
    private Integer accountId;

    @ApiModelProperty("0: 不带监室人数1：查询监室对应人数以及主协管信息")
    private String type;

    @ApiModelProperty("w:主管 a:协管 s:关注")
    private String roomType;

    @ApiModelProperty("一室一档监室类型查询条件")
    private String roomType2;

    //    /**
//     * 是否启用:1启用,0停用
//     */
//    @ApiModelProperty(value = "是否启用:1启用,0停用", required = true)
//    private String status;
//    /**
//     * 关押量
//     */
//    @ApiModelProperty(value = "关押量", required = true)
//    private Integer imprisonmentAmount;
//    /**
//     * 所属中队
//     */
//    @ApiModelProperty(value = "所属中队", required = false)
//    private String areaId;

    @ApiModelProperty(value = "所属中队", required = false)
    private String squadronId;

    @ApiModelProperty(value = "所属q区域", required = false)
    private String partId;
    //    /**
//     * 排序
//     */
//    @ApiModelProperty(value = "排序", required = true)
//    private Integer orderId;
//    /**
//     * 监室编号
//     */
//    @ApiModelProperty(value = "监室编号", required = true)
//    private String roomCode;
//    /**
//     * 监室类型
//     */
//    @ApiModelProperty(value = "监室类型", required = true)
//    private String roomType;
//    /**
//     * 人员性别
//     */
//    @ApiModelProperty(value = "人员性别", required = true)
//    private String roomSex;
//    /**
//     * 所属监所
//     */
    @ApiModelProperty(value = "所属监所")
    private String prisonId;
    @ApiModelProperty(value = "在押人员编号")
    private String prisonerId;

    @ApiModelProperty(value = "一室一档管教条件")
    private String warderPoliceName;

    @ApiModelProperty("监室选择查询通用接口判断")
    private Integer status;

    @ApiModelProperty("是否分页 0不分页 1分页")
    private Boolean paging;

    @ApiModelProperty("中队名字")
    private String squadronName;


}
