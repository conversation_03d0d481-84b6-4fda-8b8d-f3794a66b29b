package com.gosun.zhjg.basic.business.modules.menu.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 系统菜单分组表
 *
 * <AUTHOR>
 *
 */
@Data
@ApiModel("系统菜单分组表新增Dto")
public class BaseMenuGroupSaveDto {

	/**
	 * $column.comments
	 */
//	@NotNull(message = "$column.comments 不能为空!")
	@ApiModelProperty(value = "$column.comments", required = true)
	private String id;
	/**
	 * 分组名
	 */
	@NotNull(message = "分组名 不能为空!")
	@ApiModelProperty(value = "分组名", required = true)
	private String groupName;
	/**
	 * 编码
	 */
//	@NotNull(message = "编码 不能为空!")
	@ApiModelProperty(value = "编码", required = true)
	private String code;
	/**
	 * 排序大靠前，默认100
	 */
//	@NotNull(message = "排序大靠前，默认100 不能为空!")
	@ApiModelProperty(value = "排序大靠前，默认100", required = true)
	private Integer sort;
	/**
	 * 是否启用 1 / 0
	 */
//	@NotNull(message = "是否启用 1 / 0 不能为空!")
	@ApiModelProperty(value = "是否启用 1 / 0", required = true)
	private Integer enable;

//	@NotNull
	private List<String> menuIds;

//	/**
//	 * $column.comments
//	 */
//	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//	@NotNull(message = "$column.comments 不能为空!")
//	@ApiModelProperty(value = "$column.comments", required = true)
//	private Date createTime;
//	/**
//	 * $column.comments
//	 */
//	@NotNull(message = "$column.comments 不能为空!")
//	@ApiModelProperty(value = "$column.comments", required = true)
//	private String createUserid;
//	/**
//	 * $column.comments
//	 */
//	@NotNull(message = "$column.comments 不能为空!")
//	@ApiModelProperty(value = "$column.comments", required = true)
//	private String createUsername;
//	/**
//	 * $column.comments
//	 */
//	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//	@NotNull(message = "$column.comments 不能为空!")
//	@ApiModelProperty(value = "$column.comments", required = true)
//	private Date updateTime;
//	/**
//	 * $column.comments
//	 */
//	@NotNull(message = "$column.comments 不能为空!")
//	@ApiModelProperty(value = "$column.comments", required = true)
//	private String updateUserid;
//	/**
//	 * $column.comments
//	 */
//	@NotNull(message = "$column.comments 不能为空!")
//	@ApiModelProperty(value = "$column.comments", required = true)
//	private String updateUsername;
//	/**
//	 * $column.comments
//	 */
//	@NotNull(message = "$column.comments 不能为空!")
//	@ApiModelProperty(value = "$column.comments", required = true)
//	private Integer delFlag;

}
