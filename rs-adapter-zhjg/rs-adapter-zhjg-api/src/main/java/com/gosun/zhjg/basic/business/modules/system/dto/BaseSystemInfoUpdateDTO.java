package com.gosun.zhjg.basic.business.modules.system.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.gosun.zhjg.common.vo.BaseFileVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
* base_system_info 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2022-12-23
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="BaseSystemInfoUpdateDTO对象", description="编辑")
public class BaseSystemInfoUpdateDTO implements Serializable {


    @JsonProperty("id")
    @ApiModelProperty(value = "主键",required = true)
    @NotBlank(message = "id不能为空")
    private String id;

    @JsonProperty("systemName")
    @ApiModelProperty(value = "系统名字",required = true)
    @NotBlank(message = "系统名字 不能为空")
    private String systemName;

    @JsonProperty("systemNameShort")
    @ApiModelProperty(value = "系统名字缩写",required = true)
    @NotBlank(message = "系统名字缩写 不能为空")
    private String systemNameShort;

    @JsonProperty("onlineDate")
    @ApiModelProperty(value = "系统上线日期",required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @NotNull(message = "系统上线日期 不能为空")
    private Date onlineDate;

    @JsonProperty("loginPhoto")
    @NotEmpty(message = "登录页logo图片 不能为空")
    @ApiModelProperty(value = "登录页logo图片",required = true)
    private List<BaseFileVo> loginPhoto;

    @JsonProperty("systemPhoto")
    @NotEmpty(message = "系统名称图片 不能为空")
    @ApiModelProperty(value = "系统名称图片",required = true)
    private List<BaseFileVo> systemPhoto;

    @JsonProperty("versionInfo")
    @ApiModelProperty(value = "版权信息",required = true)
    private String versionInfo;

    @JsonProperty("systemInfo")
    @ApiModelProperty(value = "系统说明",required = true)
    private String systemInfo;

    @JsonProperty("updateUser")
    @ApiModelProperty(value = "更新人",hidden = true)
    @NotBlank(message = "更新人 不能为空")
    private String updateUser;

    @JsonProperty("updateTime")
    @ApiModelProperty(value = "更新时间",hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @NotNull(message = "更新时间 不能为空")
    private Date updateTime;

    /**
     * 背景图片
     */
    @ApiModelProperty(value = "背景图片")
    private String backgroundImage;

    /**
     * 登录页效果 1：警务蓝（默认） 2：科技蓝
     */
    @NotBlank(message = "登录页效果 不能为空")
    @ApiModelProperty(value = "登录页效果 1：警务蓝（默认） 2：科技蓝")
    private String loginPageEffect;

    /**
     * 是否展示插件列表 0：否 1：是
     */
    @NotBlank(message = "是否展示插件列表 不能为空")
    @ApiModelProperty(value = "是否展示插件列表 0：否 1：是")
    private String isShowPlugin;

    /**
     * 是否展示浏览器列表 0：否 1：是
     */
    @NotBlank(message = "是否展示浏览器列表 不能为空")
    @ApiModelProperty(value = "是否展示浏览器列表 0：否 1：是")
    private String isShowBrowser;

    /**
     * 是否展示超链接网址 0：否 1：是
     */
    @NotBlank(message = "是否展示超链接网址 不能为空")
    @ApiModelProperty(value = "是否展示超链接网址 0：否 1：是")
    private String isShowHyperlink;

    /**
     * 插件列表
     */
    @ApiModelProperty(value = "插件列表")
    private List<UpdateBaseSystemInfoUrlDTO> plugins;

    /**
     * 浏览器列表
     */
    @ApiModelProperty(value = "浏览器列表")
    private List<UpdateBaseSystemInfoUrlDTO> browsers;

    /**
     * 超链接网址列表
     */
    @ApiModelProperty(value = "超链接网址列表")
    private List<UpdateBaseSystemInfoUrlDTO> hyperlinks;
}

