<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.rs</groupId>
		<artifactId>rs-adapter-zhjg</artifactId>
		<version>${rs.version}</version>
	</parent>

	<artifactId>rs-adapter-zhjg-api</artifactId>
	<packaging>jar</packaging>
	<name>${project.artifactId}</name>
	<description>旧平台适配器-api模块</description>

	<dependencies>
		<dependency>
			<!-- 第三方拼音依赖包，配合 hutool-all 包中的 PinyinUtil 拼音工具使用 start -->
			<groupId>com.belerweb</groupId>
			<artifactId>pinyin4j</artifactId>
		</dependency>
		<!-- web 相关 begin -->
		<dependency>
            <groupId>org.springdoc</groupId>  <!-- 接口文档 -->
            <artifactId>springdoc-openapi-ui</artifactId>
        </dependency>
		<!-- web 相关 end -->

		<!-- 技术组件 begin -->
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-starter-rpc</artifactId>
		</dependency>
		<!-- 技术组件 end -->
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
		</dependency>
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
		</dependency>
		<dependency>
			<groupId>org.mvel</groupId>
			<artifactId>mvel2</artifactId>
		</dependency>
		<dependency>
			<groupId>com.antherd</groupId>
			<artifactId>sm-crypto</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
		</dependency>
	</dependencies>
</project>
