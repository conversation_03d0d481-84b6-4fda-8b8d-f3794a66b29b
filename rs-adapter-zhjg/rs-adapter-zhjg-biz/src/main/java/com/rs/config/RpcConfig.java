package com.rs.config;

import com.gosun.zhjg.basic.business.modules.file.feign.FileApi;
import com.gosun.zhjg.prison.room.terminal.modules.app.feign.CnpLogFeign;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.feign.TerminalVersionManagementApi;
import com.gosun.zhjg.prison.room.terminal.modules.socket.feign.CnpSocketFeign;
import com.gosun.zhjg.prison.room.terminal.modules.socket.feign.SocketPushFeign;
import com.rs.adapter.bsp.api.BpmApi;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.BspSdk;
import com.rs.adapter.bsp.api.UserApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * RPC远程访问配置类
 * <AUTHOR>
 * @date 2025年4月13日
 */
@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {UserApi.class, BspApi.class, BspSdk.class, BpmApi.class, SocketPushFeign.class, TerminalVersionManagementApi.class, CnpLogFeign.class, FileApi.class, CnpSocketFeign.class})
public class RpcConfig {

}
