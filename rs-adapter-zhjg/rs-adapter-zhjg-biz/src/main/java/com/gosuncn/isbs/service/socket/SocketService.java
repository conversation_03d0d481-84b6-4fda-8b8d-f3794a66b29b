package com.gosuncn.isbs.service.socket;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.corundumstudio.socketio.SocketIOClient;
import com.gosuncn.isbs.common.constant.TopicConstants;
import com.gosuncn.isbs.common.domain.dto.Result;
import com.gosuncn.isbs.common.socketio.SocketClientContext;
import com.gosuncn.isbs.common.util.GlobalObjectMapper;
import com.gosuncn.isbs.domain.dto.socket.CustomPushMessageConditionDTO;
import com.gosuncn.isbs.domain.dto.socket.LcmAckCallback;
import com.gosuncn.isbs.domain.dto.socket.PushMessageForm;
import com.gosuncn.isbs.domain.vo.auth.User;
import com.gosuncn.isbs.domain.vo.socket.PushMessageAckVO;
import com.gosuncn.isbs.domain.vo.socket.SocketRelationMapVO;
import com.rs.framework.common.exception.BizException;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * 与仓内屏交互Socket长连接
 *
 * <AUTHOR>
 */
@Slf4j
@Service("isbsSocketService")
@SuppressWarnings({"LombokGetterMayBeUsed", "LombokSetterMayBeUsed"})
public class SocketService {

    /**
     * 维持长连接socket主题
     */
    private static final String TOPIC_KEEP_ALIVE = TopicConstants.TOPIC_KEEP_ALIVE;

    @Getter
    @Setter
    private boolean logPrintln;


    /**
     * 推送消息给序列号所在客户端等待响应
     */
    public List<PushMessageAckVO> pushMessageToSerialNumberWaitReplyMultiple(PushMessageForm form) {
        form.setTerminal(null);
        form.setSessionIds(null);
        form.setRoomIds(null);
        return this.pushMessageWithConditionWaitReply(form);
    }

    /**
     * 推送消息给序列号，不等待响应
     */
    public void pushMessageToSerialNumber(PushMessageForm form) {
        form.setTerminal(null);
        form.setSessionIds(null);
        form.setRoomIds(null);
        this.pushMessageWithCondition(form);
    }

    /**
     * 推送消息给某监所下所有用户 -平台，不等待响应
     * 支持黑名单
     */
    public void pushMessageToPrison(PushMessageForm form) {
        this.pushMessageWithCondition(form);
    }

    /**
     * 推送消息给某监所下所有用户 -平台，等待响应
     * 支持黑名单
     */
    public List<PushMessageAckVO> pushMessageToPrisonWaitReplyMultiple(PushMessageForm form) {
        return this.pushMessageWithConditionWaitReply(form);
    }

    /**
     * 推送消息给用户 -平台，不等待响应
     */
    public void pushMessageToUser(PushMessageForm form) {
        this.pushMessageWithCondition(form);
    }


    /**
     * 推送消息给用户 -平台，等待响应
     */
    public List<PushMessageAckVO> pushMessageToUserWaitReplyMultiple(PushMessageForm form) {
        return this.pushMessageWithConditionWaitReply(form);
    }

    /**
     * 推送消息给某角色下所有用户 -平台，不等待响应
     * <p>
     * roleIds： 角色id
     * rolepostList 岗位集合
     * userIdBlacklist 黑名单
     */
    public int pushMessageToRole(PushMessageForm form) {
        this.pushMessageWithCondition(form);
        return this.getSessionIdsWithCondition(form).size();
    }

    /**
     * 推送消息给某角色下所有用户 -平台，等待响应
     * <p>
     * roleIds： 角色id
     * rolepostList 岗位集合
     * userIdBlacklist 黑名单
     */
    public List<PushMessageAckVO> pushMessageToRoleWaitReplyMultiple(PushMessageForm form) {
        return this.pushMessageWithConditionWaitReply(form);
    }

    /**
     * 推送消息给监室不等待响应
     */
    public void pushMessageToRoom(PushMessageForm form) {
        form.setSerialNumbers(null);
        form.setSessionIds(null);
        this.pushMessageWithCondition(form);
    }

    /**
     * 推送消息给监室等待响应
     */
    public List<PushMessageAckVO> pushMessageToRoomWaitReplyMultiple(PushMessageForm form) {
        form.setSerialNumbers(null);
        form.setSessionIds(null);
        return this.pushMessageWithConditionWaitReply(form);
    }


    /**
     * 发送消息给客户端不期待回应
     */
    public int pushMessageToClient(PushMessageForm form) {
        return this.pushMessageWithCondition(form);
    }

    /**
     * 解析回调消息
     */
    private PushMessageAckVO parseCallback(Object result) {
        Result<?> finalResult = parseCallback2(result);
        PushMessageAckVO ackVO = new PushMessageAckVO();
        if (finalResult.getReturnCode() == Result.SUCCESS_CODE) {
            ackVO.setOk(true);
            ackVO.setResponse(finalResult.getData());
        } else {
            ackVO.setOk(false);
            ackVO.setResponse(finalResult.getReturnMsg());
        }
        return ackVO;
    }

    private Result<?> parseCallback2(Object result) {
        if (result == null) {
            return Result.success();
        }
        try {
            String body;
            if (result instanceof String) {
                body = (String) result;
            } else {
                body = GlobalObjectMapper.writeValueAsString(result);
            }
            body = body.trim();
            if (body.startsWith("[") && body.endsWith("]")) {
                JSONArray array = JSON.parseArray(body);
                return Result.success(array);
            }
            if (body.startsWith("{") && body.endsWith("}")) {
                JSONObject responseObj = JSON.parseObject(body);
                if (responseObj.containsKey("ok")) {
                    Boolean ok = responseObj.getBoolean("ok");
                    if (!ok) {
                        return Result.fail(responseObj.getString("error"));
                    }
                    // 成功
                    return Result.success(responseObj.getOrDefault("data", null));
                } else if (responseObj.containsKey("error")) {
                    // 兼容
                    return Result.fail(responseObj.getString("error"));
                }
                return Result.success(responseObj);
            }
            return Result.success(body);
        } catch (JSONException e) {
            log.error("result: {}", result, e);
            return Result.fail("数据解析异常");
        }
    }


    /**
     * 去除socket通知无用参数。仅保留需要传递的参数
     */
    private PushMessageForm form2PushMessage(PushMessageForm from) {
        PushMessageForm message = new PushMessageForm();
        message.setAction(from.getAction());
        message.setTarget(from.getTarget());
        message.setParams(from.getParams());
        message.setRoomIds(from.getRoomIds());
        return message;
    }

    /**
     * 发送消息给客户端
     */
    public void pushMessageToClient(String eventName, String pushMessage, String sessionId) {
        SocketIOClient client = SocketClientContext.clientMap.get(sessionId);
        if (client == null) {
            throw new BizException("客户端不存在或客户端不在线 SessionId=[{" + sessionId + "}]！");
        }
        client.sendEvent(eventName, pushMessage);
    }


    public SocketRelationMapVO getSocketInfo() {
        SocketRelationMapVO relationMapVO = SocketRelationMapVO.builder()
                .clientTopicMap(SocketClientContext.clientTopicMap)
                .roomMap(SocketClientContext.roomMap)
                .policeMap(SocketClientContext.policeMap)
                .escortMap(SocketClientContext.escortMap)
                .heatBeatNumMap(SocketClientContext.heatBeatNumMap)
                .clientUserInfoMap(SocketClientContext.clientUserInfoMap)
                .terminalMap(SocketClientContext.terminalMap)
                .serialNumberMap(SocketClientContext.serialNumberMap)
                .macAddressMap(SocketClientContext.macAddressMap)
                .build();
        relationMapVO.setClientIds(SocketClientContext.clientMap.keySet());
        return relationMapVO;
    }

    /**
     * 按条件发送消息到客户端
     */
    public int pushMessageWithCondition(PushMessageForm condition) {
        return this.pushMessageWithCondition0(condition, this::form2PushMessage);
    }

    /**
     * 按条件发送消息到客户端，并等待回复
     */
    public List<PushMessageAckVO> pushMessageWithConditionWaitReply(PushMessageForm condition) {
        return this.customPushMessageWithConditionWaitReply0(condition, this::form2PushMessage);
    }

    /**
     * 按条件发送消息到客户端，使用自定义的对象
     */
    public void customPushMessageWithCondition(CustomPushMessageConditionDTO condition) {
        this.pushMessageWithCondition0(condition, CustomPushMessageConditionDTO::getMessage);
    }

    /**
     * 按条件发送消息到客户端，使用自定义的对象，并等待回复
     */
    public List<PushMessageAckVO> customPushMessageWithConditionWaitReply(CustomPushMessageConditionDTO condition) {
        return this.customPushMessageWithConditionWaitReply0(condition, CustomPushMessageConditionDTO::getMessage);
    }

    /**
     * 按条件发送消息到客户端
     *
     * @param condition          筛选条件
     * @param messageGetFunction 发送的消息
     * @return 发送成功的客户端数
     */
    private <T extends PushMessageForm> int pushMessageWithCondition0(T condition,
                                                                      Function<T, Object> messageGetFunction) {
        String eventName = StringUtils.defaultIfBlank(condition.getEventName(), TOPIC_KEEP_ALIVE);
        Set<String> sessionIds = this.getSessionIdsWithCondition(condition);
        if (CollectionUtils.isEmpty(sessionIds)) {
            log.warn("筛选发送的客户端为空：{}", GlobalObjectMapper.writeValueAsString(condition));
            return 0;
        }
        Object data = messageGetFunction.apply(condition);
        String message = GlobalObjectMapper.writeValueAsString(data);
        this.logPrintln(() -> log.info("socket发送客户端不等待回复，筛选条件：{}，发送消息：{}，发送的客户端：{}",
                GlobalObjectMapper.writeValueAsString(condition), message, sessionIds));
        int successCount = 0;
        for (String sessionId : sessionIds) {
            try {
                this.pushMessageToClient(eventName, message, sessionId);
                successCount++;
            } catch (Exception e) {
                log.error("{} 发送消息失败", sessionId, e);
            }
        }
        return successCount;
    }

    /**
     * 按条件发送消息到客户端，等待回复
     *
     * @param condition          筛选条件
     * @param messageGetFunction 发送的消息
     */
    private <T extends PushMessageForm> List<PushMessageAckVO> customPushMessageWithConditionWaitReply0(
            T condition, Function<T, Object> messageGetFunction) {
        Integer awaitSeconds = condition.getAwaitSeconds();
        String eventName = StringUtils.defaultIfBlank(condition.getEventName(), TOPIC_KEEP_ALIVE);
        Set<String> sessionIds = this.getSessionIdsWithCondition(condition);
        if (CollectionUtils.isEmpty(sessionIds)) {
            log.warn("筛选待发送的客户端为空：{}", GlobalObjectMapper.writeValueAsString(condition));
            return Collections.emptyList();
        }
        Map<String, PushMessageAckVO> ackResultMap = new HashMap<>();
        Object data = messageGetFunction.apply(condition);
        String message = GlobalObjectMapper.writeValueAsString(data);
        this.logPrintln(() -> log.info("socket发送客户端等待回复，筛选条件：{}，发送消息：{}，发送的客户端：{}",
                GlobalObjectMapper.writeValueAsString(condition), message, sessionIds));
        log.info("socket发送客户端等待回复，筛选条件：{}，发送消息：{}，发送的客户端：{}",
                GlobalObjectMapper.writeValueAsString(condition), message, sessionIds);
        CountDownLatch countDownLatch = new CountDownLatch(sessionIds.size());
        for (String sessionId : sessionIds) {
            SocketIOClient socketIOClient = SocketClientContext.clientMap.get(sessionId);
            if (Objects.isNull(socketIOClient)) {
                log.error("客户端{}不存在", sessionId);
                continue;
            }
            socketIOClient.sendEvent(eventName, new LcmAckCallback<Object>(Object.class, awaitSeconds, sessionId) {
                @Override
                public void onSuccessExt(Object result, String sessionId0) {
                    logPrintln(() -> log.info("收到客户端[{}]回复：{}", sessionId0, result));
                    log.info("收到客户端[{}]回复：{}", sessionId0, result);
                    PushMessageAckVO pushMessageAckVO = parseCallback(result);
                    pushMessageAckVO.setSessionId(sessionId0);
                    ackResultMap.put(sessionId0, pushMessageAckVO);
                    countDownLatch.countDown();
                }
            }, message);
        }
        try {
            // noinspection ResultOfMethodCallIgnored
            countDownLatch.await(awaitSeconds, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("", e);
        }
        // 补充超时的客户端
        for (String sessionId : sessionIds) {
            ackResultMap.putIfAbsent(sessionId, new PushMessageAckVO(sessionId, false, "客户端ack回复响应超时"));
        }
        return new ArrayList<>(ackResultMap.values());
    }

    /**
     * 打印日志消息
     */
    private void logPrintln(Runnable runnable) {
        if (logPrintln) {
            runnable.run();
        }
    }

    /**
     * 获取发送的客户端sessionId集合
     */
    private Set<String> getSessionIdsWithCondition(PushMessageForm condition) {
        Set<String> allSessionIdSet = new HashSet<>();
        // 条件
        Set<Long> userIdSet = this.getSet(condition.getUserIds());
        Set<Integer> roleIdSet = this.getSet(condition.getRoleIds());
        Set<String> rolepostSet = this.getSet(condition.getRolepostList());
        Set<String> prisonIdSet = this.getSet(condition.getPrisonIds());
        Set<Long> userIdBlacklistSet = this.getSet(condition.getUserIdBlacklist());
        Set<String> serialNumberSet = this.getSet(condition.getSerialNumbers());
        Set<String> roomIdSet = this.getSet(condition.getRoomIds());
        Set<String> macAddressSet = this.getSet(condition.getMacAddress());
        Set<String> escortIdSet = this.getSet(condition.getEscortIds());
        Set<String> sessionIdSet = this.getSet(condition.getSessionIds());
        String terminal = condition.getTerminal();
        System.out.println("SocketClientContext.clientMap.keySet()：" + SocketClientContext.clientMap.keySet());
        System.out.println("sessionIds：" + sessionIdSet);
        for (String sessionId : SocketClientContext.clientMap.keySet()) {
            // 终端过滤
            if (StringUtils.isNotBlank(terminal)) {
                String terminalType = SocketClientContext.terminalMap.get(sessionId);
                if (StringUtils.isBlank(terminalType) || !Objects.equals(terminalType, terminal)) {
                    continue;
                }
            }
            // 用户id过滤
            if (this.isNotEmpty(userIdSet)) {
                User user = SocketClientContext.clientUserInfoMap.get(sessionId);
                if (Objects.isNull(user) || !userIdSet.contains(user.getUserid())) {
                    continue;
                }
            }
            // 角色id过滤
            if (this.isNotEmpty(roleIdSet)) {
                User user = SocketClientContext.clientUserInfoMap.get(sessionId);
                if (Objects.isNull(user) || !roleIdSet.contains(user.getRoleId())) {
                    continue;
                }
            }
            // 角色岗位过滤
            if (this.isNotEmpty(rolepostSet)) {
                User user = SocketClientContext.clientUserInfoMap.get(sessionId);
                if (Objects.isNull(user) || !rolepostSet.contains(user.getRolepost())) {
                    continue;
                }
            }
            // 监所id过滤
            if (this.isNotEmpty(prisonIdSet)) {
                User user = SocketClientContext.clientUserInfoMap.get(sessionId);
                if (Objects.isNull(user) || !prisonIdSet.contains(user.getPrisonId())) {
                    continue;
                }
            }
            // 黑名单过滤
            if (this.isNotEmpty(userIdBlacklistSet)) {
                User user = SocketClientContext.clientUserInfoMap.get(sessionId);
                if (Objects.isNull(user) || userIdBlacklistSet.contains(user.getUserid())) {
                    continue;
                }
            }
            // 序列号过滤
            if (this.isNotEmpty(serialNumberSet)) {
                String serialNumber = SocketClientContext.serialNumberMap.get(sessionId);
                if (this.isNullOrNoContain(serialNumber, serialNumberSet)) {
                    continue;
                }
            }
            // 监室号过滤
            if (this.isNotEmpty(roomIdSet)) {
                String roomId = SocketClientContext.roomMap.get(sessionId);
                if (this.isNullOrNoContain(roomId, roomIdSet)) {
                    continue;
                }
            }
            // mac地址过滤
            if (this.isNotEmpty(macAddressSet)) {
                String macAddress = SocketClientContext.macAddressMap.get(sessionId);
                if (this.isNullOrNoContain(macAddress, macAddressSet)) {
                    continue;
                }
            }
            // 押解联防聊天任务Id过滤
            if (this.isNotEmpty(escortIdSet)) {
                String escortId = SocketClientContext.escortMap.get(sessionId);
                if (this.isNullOrNoContain(escortId, escortIdSet)) {
                    continue;
                }
            }
            // 客户端
            if (this.isNotEmpty(sessionIdSet)) {
                if (!sessionIdSet.contains(sessionId)) {
                    continue;
                }
            }
            allSessionIdSet.add(sessionId);
        }
        return allSessionIdSet;
    }

    /**
     * 获取集合
     */
    private <T> Set<T> getSet(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptySet();
        }
        return new HashSet<>(list);
    }

    /**
     * 判断集合是否为空
     */
    private <T> boolean isNotEmpty(Collection<T> collection) {
        return !CollectionUtils.isEmpty(collection);
    }

    /**
     * 判断是否为null 或者是否不包含
     */
    private <T> boolean isNullOrNoContain(T data, Collection<T> dataSet) {
        return Objects.isNull(data) || !dataSet.contains(data);
    }

}
