package com.gosuncn.isbs.domain.dto.socket;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 仓内屏推送消息，请求体
 *
 * <AUTHOR>
 * @see
 */
@Data
public class PushMessageForm {

    @ApiModelProperty(allowableValues = "web,android,external", example = "web")
    private String target;

    @NotBlank
    @ApiModelProperty(value = "指令", required = true)
    private String action;

    @ApiModelProperty("参数")
    private Object[] params;

    /**
     * 推送消息给客户端
     */
    @ApiModelProperty(value = "推送消息给客户端")
    private List<String> sessionIds;

    /**
     * 推送消息给监室
     */
    @ApiModelProperty(value = "推送消息给监室")
    private List<String> roomIds;

    /**
     * SocketActionConstants.PushMessageTerminalEnum.CNP.name()
     */
    @ApiModelProperty(value = "推送消息给监室时使用， 指定终端。区分出推送给仓内屏还是仓外屏", example = "CNP 、 CWP 、 PLATFORM 、EXTERNAL 、IHCS")
    private String terminal;

    /**
     * 推送消息给设备序列号
     */
    @ApiModelProperty(value = "推送消息给设备序列号")
    private List<String> serialNumbers;

    /**
     * 推送消息给监所下所有userid -平台web用
     */
    @ApiModelProperty(value = "推送消息给监所下所有userid -平台web用")
    private List<String> prisonIds;

    /**
     * 推送消息给用户 -平台web用
     */
    @ApiModelProperty(value = "推送消息给用户 -平台web用")
    private List<Long> userIds;

    /**
     * 推送消息给mac地址终端
     */
    @ApiModelProperty(value = "推送消息给mac地址终端")
    private List<String> macAddress;

    /**
     * 推送消息给角色 -平台web用
     */
    @ApiModelProperty(value = "推送消息给角色 -平台web用")
    private List<Integer> roleIds;

    /**
     * 推送消息给岗位 -平台web用
     */
    @ApiModelProperty(value = "推送消息给岗位 -平台web用")
    private List<String> rolepostList;

    /**
     * 推送用户黑名单，该名单内不推送
     */
    @ApiModelProperty(value = "推送用户黑名单，该名单内不推送")
    private List<Long> userIdBlacklist;

    /**
     * 押解联防聊天任务Id
     */
    @ApiModelProperty(value = "押解联防聊天任务Id")
    private List<String> escortIds;

    /**
     * 事件名称
     */
    @ApiModelProperty(value = "事件名称")
    private String eventName;

    // ------------------------------------------------------------------------------------------------------------------------

    /**
     * 等待响应时间
     */
    @JSONField(serialize = false)
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Integer awaitSeconds;

    // ------------------------------------------------------------------------------------------------------------------------

    public PushMessageForm setSessionId(String sessionId) {
        this.sessionIds = Lists.newArrayList(sessionId);
        return this;
    }


    public PushMessageForm() {

    }

    public PushMessageForm params(Object... params) {
        this.params = params;
        return this;
    }

    public PushMessageForm(String target, String action) {
        this.target = target;
        this.action = action;
    }

    public Integer getAwaitSeconds() {
        if (this.awaitSeconds == null || this.awaitSeconds <= 0) {
            return 7;
        } else {
            return this.awaitSeconds;
        }
    }


    @JsonIgnore
    @JSONField(serialize = false)
    public String getSessionId() {
        if (sessionIds == null || sessionIds.isEmpty()) {
            return null;
        }
        return sessionIds.get(0);
    }

}
