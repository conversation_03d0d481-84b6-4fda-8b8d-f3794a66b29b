package com.gosuncn.isbs.common.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.core.JsonTokenId;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.BeanPropertyWriter;
import com.fasterxml.jackson.databind.ser.BeanSerializerModifier;
import com.fasterxml.jackson.databind.ser.SerializerFactory;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.gosuncn.isbs.common.constant.DateTimeConstant;
import lombok.SneakyThrows;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.DateTimeException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;


/**
 * 全局jackson配置
 */
public class GlobalObjectMapper {

    private static final ObjectMapper OBJECT_MAPPER;

    static {
        OBJECT_MAPPER = Jackson2ObjectMapperBuilder.json()
                .timeZone(TimeZone.getDefault())
                .featuresToEnable(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature())
                .simpleDateFormat(DateTimeConstant.DATETIME_PATTERN_COMMON)
                .build();
        OBJECT_MAPPER.registerModule(getModule());
        SerializerFactory serializerFactory = OBJECT_MAPPER.getSerializerFactory()
                .withSerializerModifier(new BeanPropertyNullValueModifier());
        OBJECT_MAPPER.setSerializerFactory(serializerFactory);
    }

    /**
     * 该方法可用于将任何Java值序列化为String
     *
     * @param value java 对象值
     */
    @SneakyThrows
    public static String writeValueAsString(Object value) {
        return OBJECT_MAPPER.writeValueAsString(value);
    }

    /**
     * 方法从给定的JSON内容字符串反序列化JSON内容。
     *
     * @param content      JSON内容
     * @param valueTypeRef 反序列化对象TypeReference
     */
    @SneakyThrows
    public static <T> T readValue(String content, TypeReference<T> valueTypeRef) {
        return OBJECT_MAPPER.readValue(content, valueTypeRef);
    }

    /**
     * 方法从给定的JSON内容字符串反序列化JSON内容。
     *
     * @param content   JSON内容
     * @param valueType 反序列化对象type
     */
    @SneakyThrows
    public static <T> T readValue(String content, Class<T> valueType) {
        return OBJECT_MAPPER.readValue(content, valueType);
    }

    /**
     * 获取单例jackson序列化 ObjectMapper
     *
     * @return ObjectMapper
     */
    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER;
    }


    /**
     * 获取数据类型支持
     * <ul>
     *    <li>添加对 java8 时间格式序列化和反序列化支持</li>
     *    <li>添加对 LocalDateTime 类型的反序列化字符串去除毫秒数再序列化</li>
     *    <li>添加对 BigDecimal 去掉小数点后最后多余的0</li>
     * </ul>
     */
    private static Module getModule() {
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeConstant.DATETIME_FORMAT_COMMON))
                .addDeserializer(LocalDateTime.class, new CustomLocalDateTimeDeserializer())
                .addSerializer(LocalDate.class, new LocalDateSerializer(DateTimeConstant.DATE_FORMAT_COMMON))
                .addDeserializer(LocalDate.class, new LocalDateDeserializer(DateTimeConstant.DATE_FORMAT_COMMON))
                .addSerializer(LocalTime.class, new LocalTimeSerializer(DateTimeConstant.TIME_FORMAT_COMMON))
                .addDeserializer(LocalTime.class, new LocalTimeDeserializer(DateTimeConstant.TIME_FORMAT_COMMON))
                .addSerializer(BigDecimal.class, new BigDecimalSerializer());
        return simpleModule;
    }

    // -----------------------------------------------static class------------------------------------------------------------


    /**
     * 自定义LocalDateTime反序列化规则
     * <ul>
     *     <li>序列化时间如果带有毫秒数则直接去除</li>
     * </ul>
     */
    public static class CustomLocalDateTimeDeserializer extends LocalDateTimeDeserializer {

        public static final DateTimeFormatter DATETIME_FORMATTER = DateTimeConstant.DATETIME_FORMAT_COMMON;

        public CustomLocalDateTimeDeserializer() {
            super(DATETIME_FORMATTER);
        }

        @Override
        public LocalDateTime deserialize(JsonParser parser, DeserializationContext context) throws IOException {
            if (parser.hasTokenId(JsonTokenId.ID_STRING)) {
                String string = parser.getText().trim();
                if (string.isEmpty()) {
                    if (!isLenient()) {
                        return _failForNotLenient(parser, context, JsonToken.VALUE_STRING);
                    }
                    return null;
                }
                try {
                    // 如果包含毫秒，直接去除
                    String timeString = string;
                    int indexOf = string.indexOf(".");
                    if (indexOf != -1) {
                        timeString = string.substring(0, indexOf);
                    }
                    return LocalDateTime.parse(timeString, _formatter);
                } catch (DateTimeException e) {
                    return _handleDateTimeException(context, e, string);
                }
            }
            return super.deserialize(parser, context);
        }

    }

    public static class BigDecimalSerializer extends JsonSerializer<BigDecimal> {

        /**
         * 去除小数点最后的0格式模板
         */
        public static final String DECIMAL_POINT_LAST_ZERO_FORMAT = "0.###################################";

        @Override
        public void serialize(BigDecimal value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            gen.writeNumber(new DecimalFormat(DECIMAL_POINT_LAST_ZERO_FORMAT).format(value));
        }
    }


    /**
     * jackson字段的封装实体类设置对应的null值序列化
     */
    public static class BeanPropertyNullValueModifier extends BeanSerializerModifier {
        /**
         * Map null 值序列化为 {}
         */
        private static final MapNullValueSerializer MAP_NULL_VALUE_SERIALIZER = new MapNullValueSerializer();
        /**
         * 集合类 null 值序列化为 []
         */
        private static final CollectionNullValueSerializer COLLECTION_NULL_VALUE_SERIALIZER = new CollectionNullValueSerializer();
        /**
         * 数组 null 值序列化为 []
         */
        private static final ArrayNullValueSerializer ARRAY_NULL_VALUE_SERIALIZER = new ArrayNullValueSerializer();
        /**
         * 字符类型 null 值默认序列化为 ""
         */
        private static final CharSequenceNullValueSerializer CHAR_SEQUENCE_NULL_VALUE_SERIALIZER = new CharSequenceNullValueSerializer();

        @Override
        public List<BeanPropertyWriter> changeProperties(SerializationConfig config, BeanDescription beanDesc, List<BeanPropertyWriter> beanProperties) {
            for (BeanPropertyWriter beanProperty : beanProperties) {
                Class<?> rawClass = beanProperty.getType().getRawClass();

                if (CharSequence.class.isAssignableFrom(rawClass)) {
                    beanProperty.assignNullSerializer(CHAR_SEQUENCE_NULL_VALUE_SERIALIZER);
                    continue;
                }

                if (Map.class.isAssignableFrom(rawClass)) {
                    beanProperty.assignNullSerializer(MAP_NULL_VALUE_SERIALIZER);
                    continue;
                }

                if (Collection.class.isAssignableFrom(rawClass)) {
                    beanProperty.assignNullSerializer(COLLECTION_NULL_VALUE_SERIALIZER);
                    continue;
                }

                if (rawClass.isArray()) {
                    beanProperty.assignNullSerializer(ARRAY_NULL_VALUE_SERIALIZER);
                }
            }
            return beanProperties;
        }

    }

    //     ------------------------------------------ 分割线 --------------------------------------------------

    /**
     * Map null 值序列化为 {}
     */
    public static class MapNullValueSerializer extends JsonSerializer<Object> {

        @Override
        public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            gen.writeStartObject();
            gen.writeEndObject();
        }
    }


    /**
     * 集合类 null 值序列化为 []
     */
    public static class CollectionNullValueSerializer extends JsonSerializer<Object> {

        @Override
        public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            gen.writeStartArray();
            gen.writeEndArray();
        }
    }

    /**
     * 数组 null 值序列化为 []
     */
    public static class ArrayNullValueSerializer extends JsonSerializer<Object> {

        @Override
        public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            gen.writeStartArray();
            gen.writeEndArray();
        }
    }

    /**
     * 字符类型 null 值默认序列化为 ""
     */
    public static class CharSequenceNullValueSerializer extends JsonSerializer<Object> {

        @Override
        public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            gen.writeString("");
        }
    }

}
