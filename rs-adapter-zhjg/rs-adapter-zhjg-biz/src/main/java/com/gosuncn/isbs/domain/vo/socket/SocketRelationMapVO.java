package com.gosuncn.isbs.domain.vo.socket;

import com.gosuncn.isbs.domain.vo.auth.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SocketRelationMapVO {

    /**
     * 维系客户端与所订阅主题的映射。
     * 因为目前仓内屏socket使用每订阅一个主题都会新建出一个客户端。这样就无法精确发送消息给具体主题
     */
    @ApiModelProperty("维系客户端与所订阅主题的映射")
    private Map<String, String> clientTopicMap;

    /**
     * 用来存已连接的客户端
     */
    @ApiModelProperty("已连接的客户端的sessionId")
    private Set<String> clientIds;

    /**
     * 用来存已连接的监室信息
     */
    @ApiModelProperty("已连接的监室信息")
    private Map<String, String> roomMap;

    /**
     * 用来存已连接的角色信息
     */
    @ApiModelProperty("已连接的角色信息")
    private Map<String, String> policeMap;

    /**
     * 用来存已连接的押解联防聊天任务Id信息
     */
    @ApiModelProperty("已连接的押解联防聊天任务Id信息")
    private Map<String, String> escortMap;

    /**
     * 维护正常心跳的客户端
     */
    @ApiModelProperty("维护正常心跳的客户端")
    private Map<String, Long> heatBeatNumMap;

    /**
     * key clientId
     */
    @ApiModelProperty("key clientId")
    private Map<String, User> clientUserInfoMap;

    /**
     * 连接终端. cwp 仓外屏，cnp 仓内屏、 web
     */
    @ApiModelProperty("连接终端. cwp 仓外屏，cnp 仓内屏、 web")
    private Map<String, String> terminalMap;

    /**
     * 序列号
     */
    @ApiModelProperty("序列号")
    private Map<String, String> serialNumberMap;

    /**
     * mac地址
     */
    @ApiModelProperty("mac地址")
    private Map<String, String> macAddressMap;
}
