package com.gosuncn.isbs.domain.dto.socket;

import com.corundumstudio.socketio.AckCallback;
import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

/**
 * 对回调进行拓展、会话信息
 *
 * <AUTHOR>
 */
@Getter
@Setter
public abstract class LcmAckCallback<T> extends AckCallback<T> {

    private String sessionId;
    /**
     * 对这次请求标记
     */
    private String uuid;

    public LcmAckCallback(Class<T> resultClass) {
        super(resultClass);
        this.uuid = generateUUID();
    }

    public LcmAckCallback(Class<T> resultClass, int timeout) {
        super(resultClass, timeout);
        this.uuid = generateUUID();
    }

    public LcmAckCallback(Class<T> resultClass, int timeout, String sessionId) {
        super(resultClass, timeout);
        this.sessionId = sessionId;
        this.uuid = generateUUID();
    }

    public LcmAckCallback(Class<T> resultClass, String uuid) {
        super(resultClass);
        this.uuid = uuid;
    }

    @Override
    public void onSuccess(T result) {
        onSuccessExt(result, sessionId);
    }

    private static String generateUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 拓展内容
     */
    public abstract void onSuccessExt(T result, String sessionId);
}
