package com.gosuncn.isbs.service.socket;

import com.corundumstudio.socketio.SocketIOClient;
import com.gosuncn.isbs.common.constant.SocketEventConstant;
import com.gosuncn.isbs.common.socketio.SocketClientContext;
import com.gosuncn.isbs.domain.dto.socket.PushMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class HeartBeatThread implements Runnable {

	/**
	 * 服务器每10秒向每个在线的客户端推送消息
	 */
	@SuppressWarnings("static-access")
	@Override
	public void run() {
		while (true) {
			try {
				PushMessage pushMessage = new PushMessage();
				pushMessage.setContent("心跳检测，每隔十秒检测一次");
				for (SocketIOClient client : SocketClientContext.clientMap.values()) {
					client.sendEvent(SocketEventConstant.HEARTBEAT_EVENT, pushMessage);
				}
				Thread.currentThread().sleep(10000);
			} catch (Exception e) {
				log.error("", e);
			}
		}
	}

}
