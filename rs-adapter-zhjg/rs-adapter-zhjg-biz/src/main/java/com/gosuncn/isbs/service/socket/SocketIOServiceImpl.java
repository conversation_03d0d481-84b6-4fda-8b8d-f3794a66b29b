package com.gosuncn.isbs.service.socket;

import com.alibaba.fastjson.JSONObject;
import com.corundumstudio.socketio.SocketIOClient;
import com.corundumstudio.socketio.SocketIOServer;
import com.gosun.zhjg.prison.room.terminal.modules.app.service.PrisonEscortDefenseService;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.BaseDeviceInscreenDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.ConnectHandlerDTO;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.handler.StatusChangeHandler;
import com.gosuncn.isbs.common.constant.SocketActionConstants;
import com.gosuncn.isbs.common.socketio.SocketClientContext;
import com.gosuncn.isbs.common.util.GlobalObjectMapper;
import com.gosuncn.isbs.domain.dto.socket.EscortMessage;
import com.gosuncn.isbs.domain.dto.socket.PushMessage;
import com.gosuncn.isbs.domain.vo.auth.User;
import com.rs.framework.common.handler.ChainBuilder;
import com.rs.framework.common.handler.Handler;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.enums.DeviceStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.gosuncn.isbs.common.constant.SocketEventConstant.*;
import static com.gosuncn.isbs.common.socketio.SocketClientContext.getRealClientIp;

@Slf4j
@Service(value = "socketIOService")
@RequiredArgsConstructor
public class SocketIOServiceImpl implements SocketIOService {

    private final SocketIOServer socketIOServer;

    private final HeartBeatThread heartBeatThread;

    private final HeatBeatHandler heatBeatHandler;

    private final PrisonEscortDefenseService prisonEscortDefenseService;

    private final BaseDeviceInscreenDao baseDeviceInscreenDao;
    /**
     * 状态改变处理
     */
    private final StatusChangeHandler statusChangeHandler;
    private final ChainBuilder<ConnectHandlerDTO> chainBuilder;
    /**
     * 是否打印心跳, 可通过 CnpSocketController 控制
     */
    public static boolean LOG_HEARTBEAT = false;

    /**
     * Spring IoC容器创建之后，在加载SocketIOServiceImpl Bean之后启动
     */
    @PostConstruct
    private void autoStartup() {
        start();
    }


    /**
     * Spring IoC容器在销毁SocketIOServiceImpl Bean之前关闭,避免重启项目服务端口占用问题
     */
    @PreDestroy
    private void autoStop() {
        stop();
    }

    @Override
    public void start() {
        // 监听客户端连接
        socketIOServer.addConnectListener(client -> {
            String sessionId = client.getSessionId().toString();

            // 获取客户端源IP地址
            String clientIp = getRealClientIp(client);
            int clientPort = 0;
            if (client.getRemoteAddress() instanceof java.net.InetSocketAddress) {
                java.net.InetSocketAddress inetSocketAddress = (java.net.InetSocketAddress) client.getRemoteAddress();
                clientPort = inetSocketAddress.getPort();
            }

            Map<String, String> param = getParamsByClient(client);
            log.info("客户端连接，sessionId：{}，源IP：{}，端口：{}，连接参数：{}",
                    sessionId, clientIp, clientPort, GlobalObjectMapper.writeValueAsString(param));
            String eventName = param.getOrDefault("source", StringUtils.EMPTY);
            if (StringUtils.isNotBlank(eventName)) {
                SocketClientContext.clientTopicMap.put(sessionId, eventName);
            }
            SocketClientContext.clientMap.put(sessionId, client);
            SocketClientContext.heatBeatNumMap.put(sessionId, System.currentTimeMillis());
            // ------------------------------region 计划废弃-------------------------------------------------------
            if (param.containsKey("roomId") || param.containsKey("defenseId") || param.containsKey("policeId")) {
                String roomId = param.get("roomId");
                String defenseId = param.get("defenseId");
                String policeId = param.get("policeId");
                if (roomId != null) {
                    SocketClientContext.roomMap.put(sessionId, roomId);
                }
                if (defenseId != null) {
                    SocketClientContext.escortMap.put(sessionId, defenseId);
                }
                if (policeId != null) {
                    SocketClientContext.policeMap.put(sessionId, policeId);
                }
            }
            // ----------------------------------endregion--------------------------------------------------------
            String terminal = param.get("terminal");
            if (StringUtils.isNotBlank(terminal)) {
                if (SocketActionConstants.PushMessageTerminalEnum.PLATFORM.name().equals(terminal)
                        || SocketActionConstants.PushMessageTerminalEnum.IHCS.name().equals(terminal)) {
                    String token = param.get("token");
                    try {
                        User user = null;
                        SocketClientContext.clientUserInfoMap.put(sessionId, user);
                    } catch (Exception e) {
                        this.tokenIsErrorHandle(client, eventName, e);
                    }
                }
                SocketClientContext.terminalMap.put(sessionId, terminal);
            }
            // 序列号
            String serialNumber = param.get("serialNumber");
            if (StringUtils.isNotBlank(serialNumber)) {
                SocketClientContext.serialNumberMap.put(sessionId, serialNumber);
                changeStatus(client, DeviceStatusEnum.ONLINE.getCode(), serialNumber);
            }
            // mac地址
            String macAddress = param.get("macAddress");
            if (StringUtils.isNotBlank(macAddress)) {
                SocketClientContext.macAddressMap.put(sessionId, macAddress);
            }
            SocketClientContext.logClientInfo(sessionId, "客户端上线");
        });

        // 监听客户端断开连接
        socketIOServer.addDisconnectListener(client -> {
            log.info("客户端离线，sessionId：{}", client.getSessionId());
            // region 打印出掉线客户端信息
            String sessionId = client.getSessionId().toString();
            SocketClientContext.logClientInfo(sessionId, "客户端离线");
            // endregion
            SocketClientContext.clear(client);
            changeStatus(client, DeviceStatusEnum.OFFLINE.getCode(), null);

        });

        socketIOServer.addEventListener("disconnect", PushMessage.class,
                (client, data, ackSender) -> {
                    SocketClientContext.clear(client);
                    log.info("客户端断开连接，sessionId：{}", client.getSessionId());
                    changeStatus(client, DeviceStatusEnum.OFFLINE.getCode(), null);
                });
        // 监听心跳事件，与连接监听类似
        socketIOServer.addEventListener(HEARTBEAT_REPLY_EVENT, PushMessage.class,
                (client, data, ackSender) -> {
                    String sessionId = client.getSessionId().toString();
                    if (SocketClientContext.clientMap.containsKey(sessionId)) {
                        long currentTimeMillis = System.currentTimeMillis();
                        SocketClientContext.heatBeatNumMap.put(sessionId, currentTimeMillis);
                        if (LOG_HEARTBEAT) {
                            String serialNumber = SocketClientContext.serialNumberMap.get(sessionId);
                            String terminal = SocketClientContext.terminalMap.get(sessionId);
                            if (StringUtils.isNotBlank(serialNumber)) {
                                log.debug("心跳-{} {}. {}", serialNumber, terminal, sessionId);
                            }
                        }
                    }
                });

        // 监听app-押解联防-发送消息
        socketIOServer.addEventListener(ESCORT_MESSAGE_EVENT_SEND, EscortMessage.class,
                (client, data, ackSender) -> {
                    List<String> sessionIds = SocketClientContext.escortMap.entrySet()
                            .stream()
                            .filter(i -> Objects.equals(data.getDefenseId(), i.getValue()))
                            .map(Entry::getKey)
                            .collect(Collectors.toList());
                    for (String sessionId : sessionIds) {
                        SocketIOClient socketIOClient = SocketClientContext.clientMap.get(sessionId);
                        if (Objects.nonNull(socketIOClient)) {
                            socketIOClient.sendEvent(ESCORT_MESSAGE_EVENT_GET, data);
                        } else {
                            log.error("监听app-押解联防发送客户端不存在：{}", sessionId);
                        }
                    }
                    if (Objects.nonNull(data)) {
                        prisonEscortDefenseService.escortMessageSave(BeanUtils.toBean(data, com.gosun.zhjg.prison.room.terminal.config.socketio.EscortMessage.class));
                    } else {
                        log.error("监听app-押解联防接收客户端消息为空");
                    }
                });
        // web监所切换
        socketIOServer.addEventListener(PLATFORM_ROLE_CHANGE, JSONObject.class,
                (client, data, ackSender) -> {
                    String token = data.getString("token");
                    String clientId = client.getSessionId().toString();
                    String eventName = SocketClientContext.clientTopicMap.getOrDefault(clientId, StringUtils.EMPTY);
                    try {
                        User user = null;
                        SocketClientContext.clientUserInfoMap.put(clientId, user);
                    } catch (Exception e) {
                        this.tokenIsErrorHandle(client, eventName, e);
                    }
                });
        socketIOServer.start();
        new Thread(heartBeatThread).start();
        new Thread(heatBeatHandler).start();
    }

    private void changeStatus(SocketIOClient client, String status, String serialNumber) {
        String clientIp = getRealClientIp(client);
        //责任链处理连接后的逻辑
        Handler<ConnectHandlerDTO> chain = chainBuilder.buildChain(Arrays.asList(statusChangeHandler));
        ConnectHandlerDTO connectHandlerDTO = new ConnectHandlerDTO();
        connectHandlerDTO.setSerialNum(serialNumber);
        connectHandlerDTO.setIp(clientIp);
        connectHandlerDTO.setStatus(status);
        chain.handle(connectHandlerDTO);

    }

    /**
     * token认证失败处理
     */
    private void tokenIsErrorHandle(SocketIOClient client, String eventName, Exception e) {
        String sessionId = client.getSessionId().toString();
        log.error("客户端token认证失败，sessionId：{}，eventName: {}，", sessionId, eventName, e);
        client.sendEvent(eventName, "token认证失败，可能将无法接收到消息");
    }

    @Override
    public void stop() {
        if (socketIOServer != null) {
            socketIOServer.stop();
        }
    }


    /**
     * 此方法为获取client连接中的参数，可根据需求更改
     */
    private Map<String, String> getParamsByClient(SocketIOClient client) {
        // 从请求的连接中拿出参数
        Map<String, List<String>> params = client.getHandshakeData().getUrlParams();
        Map<String, String> result = new ConcurrentHashMap<>();
        params.forEach((k, v) -> {
            result.put(k, v.get(0));
        });
        result.put("sessionId", client.getSessionId().toString());
        return result;
    }


}
