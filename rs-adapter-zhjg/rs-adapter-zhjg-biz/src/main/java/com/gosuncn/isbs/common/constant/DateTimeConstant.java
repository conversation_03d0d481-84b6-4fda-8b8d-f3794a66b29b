package com.gosuncn.isbs.common.constant;

import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

/**
 * 与日期时间相关的常量。
 */
public interface DateTimeConstant {

    /**
     * 常用的日期时间格式
     */
    String DATETIME_PATTERN_COMMON = "yyyy-MM-dd HH:mm:ss";

    /**
     * 常用日期时间字符串转换器
     *
     * @see DateTimeConstant#DATETIME_PATTERN_COMMON
     */
    DateTimeFormatter DATETIME_FORMAT_COMMON = DateTimeFormatter.ofPattern(DATETIME_PATTERN_COMMON);

    // ------------------------------------------------分割线------------------------------------------------------------

    /**
     * 常用的日期格式
     */
    String DATE_PATTERN_COMMON = "yyyy-MM-dd";

    /**
     * 常用日期字符串转换器
     *
     * @see DateTimeConstant#DATE_PATTERN_COMMON
     */
    DateTimeFormatter DATE_FORMAT_COMMON = DateTimeFormatter.ofPattern(DATE_PATTERN_COMMON);

    // ------------------------------------------------分割线------------------------------------------------------------

    /**
     * 紧凑的日期格式
     */
    String DATE_PATTERN_COMPACT = "yyyyMMdd";

    /**
     * 紧凑日期字符串转换器
     *
     * @see DateTimeConstant#DATE_PATTERN_COMPACT
     */
    DateTimeFormatter DATE_FORMAT_COMPACT = DateTimeFormatter.ofPattern(DATE_PATTERN_COMPACT);

    // ------------------------------------------------分割线------------------------------------------------------------
    /**
     * 常用的年月格式
     */
    String YEAR_MATH_PATTERN_COMMON = "yyyy-MM";

    /**
     * 常用年月字符串转换器
     *
     * @see DateTimeConstant#YEAR_MATH_PATTERN_COMMON
     */
    DateTimeFormatter YEAR_MONTH_FORMAT_COMMON = DateTimeFormatter.ofPattern(YEAR_MATH_PATTERN_COMMON);
    // ------------------------------------------------分割线------------------------------------------------------------

    /**
     * 常用的时间格式
     */
    String TIME_PATTERN_COMMON = "HH:mm:ss";


    /**
     * 常用时间字符串转换器
     *
     * @see DateTimeConstant#TIME_PATTERN_COMMON
     */
    DateTimeFormatter TIME_FORMAT_COMMON = DateTimeFormatter.ofPattern(TIME_PATTERN_COMMON);

    // ------------------------------------------------分割线------------------------------------------------------------
    /**
     * 常用的时分格式
     */
    String HOURS_MINUTE_PATTERN_COMMON = "HH:mm";

    /**
     * 常用时分字符串转换器
     *
     * @see DateTimeConstant#HOURS_MINUTE_PATTERN_COMMON
     */
    DateTimeFormatter HOURS_MINUTE_FORMAT_COMMON = DateTimeFormatter.ofPattern(HOURS_MINUTE_PATTERN_COMMON);
    // ------------------------------------------------分割线------------------------------------------------------------

    /**
     * 紧凑的日期时间格式
     */
    String DATETIME_PATTERN_COMPACT = "yyyyMMddHHmmss";

    /**
     * 紧凑的日期时间格式字符串转换器
     *
     * @see DateTimeConstant#DATETIME_PATTERN_COMMON
     */
    DateTimeFormatter DATETIME_FORMAT_COMPACT = DateTimeFormatter.ofPattern(DATETIME_PATTERN_COMPACT);

    // ------------------------------------------------分割线------------------------------------------------------------

    /**
     * 只带年月的紧凑的格式
     */
    String YEAR_MONTH_PATTERN_COMPACT = "yyyyMM";

    /**
     * 只带年月的紧凑的格式字符串转换器
     *
     * @see DateTimeConstant#YEAR_MONTH_PATTERN_COMPACT
     */
    DateTimeFormatter YEAR_MONTH_FORMAT_COMPACT = DateTimeFormatter.ofPattern(YEAR_MONTH_PATTERN_COMPACT);

    // ------------------------------------------------分割线------------------------------------------------------------

    /**
     * 带时区的IOS8601时间类型
     */
    String OFFSET_IOS8601_DATE_TIME = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX";


    /**
     * 带时区和T分隔的时间类型
     */
    DateTimeFormatter DATETIME_FORMAT_OFFSET_IOS8601 = DateTimeFormatter.ofPattern(OFFSET_IOS8601_DATE_TIME);

    // ------------------------------------------------分割线------------------------------------------------------------

    /**
     * 中国时区编号。
     * <p>
     * 一般用于{@linkplain java.util.TimeZone#getTimeZone(String)}
     */
    String TIMEZONE_ID_CN = "Asia/Shanghai";

    /**
     * 中国时区 偏移的时间量
     */
    String OFFSET_ID_CN = "+08:00";

    /**
     * 中国时区 偏移的ZoneOffset
     */
    ZoneOffset ZONE_OFFSET_CN = ZoneOffset.of(OFFSET_ID_CN);


}
