package com.gosuncn.isbs.domain.vo.socket;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PushMessageAckVO {

    private String sessionId;

    private Boolean ok;

    private Object response;


    public Boolean getOk() {
        return this.ok != null && this.ok;
    }
}
