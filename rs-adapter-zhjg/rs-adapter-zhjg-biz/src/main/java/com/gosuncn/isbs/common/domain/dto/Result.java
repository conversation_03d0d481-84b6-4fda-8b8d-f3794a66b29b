package com.gosuncn.isbs.common.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 接口响应数据结构，用于对前端的请求进行响应封装
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Result<T> implements Serializable {

    private static final long serialVersionUID = 8518955172797076258L;

    @ApiModelProperty(value = "返回标识[0:成功,1:失败]", required = true)
    private Integer returnCode;

    @ApiModelProperty(value = "返回信息", required = true)
    private String returnMsg;

    @ApiModelProperty(value = "返回详情", required = true)
    private String returnDetail;

    @ApiModelProperty(value = "返回内容", required = true)
    private T data;

    @ApiModelProperty(value = "具体动态参数，需要在前端展示时用其进行替换，如果没有动态参数，此字段内容为空")
    private String msgParams;

    public final static int SUCCESS_CODE = 0;
    public final static int FAIL_CODE = 1;

    private final static String RETURN_MSG_SUCCESS_DEFAULT_VALUE = "执行成功";
    private final static String RETURN_MSG_FAIL_DEFAULT_VALUE = "操作失败";
    private final static String RETURN_DETAIL_FAIL_DEFAULT_VALUE = "Unhandle Exception!";

    /**
     * 返回响应成功结果
     */
    public static <T> Result<T> success(Integer returnCode, String returnMsg, T data) {
        return Result.<T>builder()
                .returnCode(returnCode)
                .returnMsg(returnMsg)
                .data(data)
                .build();
    }

    /**
     * 返回响应成功结果
     */
    public static <T> Result<T> success(T data) {
        return success(SUCCESS_CODE, RETURN_MSG_SUCCESS_DEFAULT_VALUE, data);
    }

    /**
     * 返回响应成功结果
     */
    public static <T> Result<T> success(String msg, T data) {
        return success(SUCCESS_CODE, msg, data);
    }

    /**
     * 返回响应成功结果
     */
    public static <T> Result<T> success() {
        return success(null);
    }

    // ---------------------------------------------------------------------------------------------------------------------------

    /**
     * 返回响应失败结果
     */
    public static <T> Result<T> fail() {
        return fail(FAIL_CODE, RETURN_MSG_FAIL_DEFAULT_VALUE, null);
    }

    /**
     * 返回响应失败结果
     */
    public static <T> Result<T> fail(String msg) {
        return fail(FAIL_CODE, msg, null);
    }

    /**
     * 返回响应失败结果
     */
    public static <T> Result<T> fail(int returnCode, String msg) {
        return fail(returnCode, msg, null);
    }

    /**
     * 返回响应失败结果
     */
    public static <T> Result<T> fail(int returnCode, String msg, String detail) {
        return Result.<T>builder()
                .returnCode(returnCode)
                .returnMsg(msg)
                .returnDetail(detail)
                .build();
    }

}
