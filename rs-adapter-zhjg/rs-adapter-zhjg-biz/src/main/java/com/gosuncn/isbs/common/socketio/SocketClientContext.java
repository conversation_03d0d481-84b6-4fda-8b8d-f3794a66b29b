package com.gosuncn.isbs.common.socketio;

import com.corundumstudio.socketio.SocketIOClient;
import com.gosuncn.isbs.common.constant.SocketActionConstants;
import com.gosuncn.isbs.domain.vo.auth.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;

import java.net.InetSocketAddress;
import java.net.SocketAddress;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class SocketClientContext {

    // 维系客户端与所订阅主题的映射。因为目前仓内屏socket使用每订阅一个主题都会新建出一个客户端。这样就无法精确发送消息给具体主题
    public static final Map<String, String> clientTopicMap = new ConcurrentHashMap<>();

    // 用来存已连接的客户端
    public static final Map<String, SocketIOClient> clientMap = new ConcurrentHashMap<>();

    // 用来存已连接的监室信息
    public static final Map<String, String> roomMap = new ConcurrentHashMap<>();

    // 用来存已连接的角色信息
    public static final Map<String, String> policeMap = new ConcurrentHashMap<>();

    // 用来存已连接的押解联防聊天任务Id信息
    public static final Map<String, String> escortMap = new ConcurrentHashMap<>();

    // 维护正常心跳的客户端
    public static final Map<String, Long> heatBeatNumMap = new ConcurrentHashMap<>();

    /**
     * key clientId
     */
    public static final Map<String, User> clientUserInfoMap = new ConcurrentHashMap<>();

    /**
     * 连接终端. cwp 仓外屏，cnp 仓内屏、 web
     */
    public static final Map<String, String> terminalMap = new ConcurrentHashMap<>();

    /**
     * 序列号
     */
    public static final Map<String, String> serialNumberMap = new ConcurrentHashMap<>();

    public static final Map<String, String> macAddressMap = new ConcurrentHashMap<>();

    /**
     * 清理指定会话
     */
    public static void clear(String sessionId) {
        disconnect(sessionId);
        clientMap.remove(sessionId);
        clientTopicMap.remove(sessionId);
        roomMap.remove(sessionId);
        heatBeatNumMap.remove(sessionId);
        escortMap.remove(sessionId);
        terminalMap.remove(sessionId);
        serialNumberMap.remove(sessionId);
        macAddressMap.remove(sessionId);
        clientUserInfoMap.remove(sessionId);
    }

    /**
     * 断开连接
     */
    public static void disconnect(String sessionId) {
        SocketIOClient socketIOClient = clientMap.get(sessionId);
        if (socketIOClient != null) {
            try {
                socketIOClient.disconnect();
            } catch (Exception e) {
                log.error("断开连接异常：{}", sessionId, e);
            }
        }
    }

    /**
     * 清理指定会话
     */
    public static void clear(SocketIOClient client) {
        clear(client.getSessionId().toString());
    }

    /**
     * 打印客户端信息
     */
    public static void logClientInfo(String sessionId, String message) {
        ImmutablePair<String, Integer> clientIpAddress = getClientIpAddress(clientMap.get(sessionId));
        if (StringUtils.isNotBlank(message)) {
            log.warn("客户端连接信息 ip：{}，端口：{}，sessionId：{}，message：{}",
                    clientIpAddress.left, clientIpAddress.right, sessionId, message);
        }
        String terminal = SocketClientContext.terminalMap.get(sessionId);
        if (StringUtils.isNotBlank(terminal)) {
            log.warn("Terminal: {}", terminal);
            if (SocketActionConstants.PushMessageTerminalEnum.PLATFORM.name().equals(terminal)) {
                User ijwtInfo = SocketClientContext.clientUserInfoMap.get(sessionId);
                if (ijwtInfo != null) {
                    log.warn("UserId: {}, name: {}, prisonId: {}, roleName: {}", ijwtInfo.getUserid(),
                            ijwtInfo.getName(), ijwtInfo.getPrisonId(), ijwtInfo.getRoleName());
                }
            } else if (SocketActionConstants.PushMessageTerminalEnum.CNP.name().equals(terminal)
                    || SocketActionConstants.PushMessageTerminalEnum.CWP.name().equals(terminal)) {
                log.warn("SerialNumber: {},{}", SocketClientContext.serialNumberMap.get(sessionId), message);
            } else if (SocketActionConstants.PushMessageTerminalEnum.EXTERNAL.name().equals(terminal)) {
                log.warn("External: {}", SocketClientContext.macAddressMap.get(sessionId));
            }
        }
    }

    /**
     * 获取客户端连接的ip地址和端口
     */
    public static ImmutablePair<String, Integer> getClientIpAddress(SocketIOClient socketIOClient) {
        if (socketIOClient == null) {
            return ImmutablePair.nullPair();
        }
        SocketAddress remoteAddress = socketIOClient.getRemoteAddress();
        if (remoteAddress instanceof InetSocketAddress) {
            InetSocketAddress inetSocketAddress = (InetSocketAddress) remoteAddress;
            String realClientIp = getRealClientIp(socketIOClient);
            int port = inetSocketAddress.getPort();
            return ImmutablePair.of(realClientIp, port);
        }
        return ImmutablePair.nullPair();
    }

    /**
     * 获取客户端真实IP地址
     * 优先级：HTTP头 > URL参数 > socket连接IP
     */
    public static synchronized String getRealClientIp(SocketIOClient client) {
        try {
            // 1. 优先从HTTP头中获取真实IP（代理服务器传递）
            String[] realIpHeaders = {// Akamai
                    "X-Client-Real-IP"
            };

            for (String headerName : realIpHeaders) {
                String ip = client.getHandshakeData().getSingleHeader(headerName);
                if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
                    // 处理可能的多IP情况（如X-Forwarded-For格式）
                    if (ip.contains(",")) {
                        ip = ip.split(",")[0].trim();
                    }
                    log.debug("从HTTP头 {} 获取到客户端真实IP: {}", headerName, ip);
                    return ip;
                }
            }

            // 2. 从URL参数中获取真实IP（向后兼容）
            Map<String, List<String>> urlParams = client.getHandshakeData().getUrlParams();
            if (urlParams != null) {
                // 检查常见的真实IP参数名
                String[] realIpParams = {"realIp", "real_ip", "clientIp", "client_ip", "originIp", "origin_ip"};
                for (String paramName : realIpParams) {
                    List<String> paramValues = urlParams.get(paramName);
                    if (paramValues != null && !paramValues.isEmpty()) {
                        String ip = paramValues.get(0);
                        if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
                            // 处理可能的多IP情况（如X-Forwarded-For格式）
                            if (ip.contains(",")) {
                                ip = ip.split(",")[0].trim();
                            }
                            log.debug("从URL参数 {} 获取到客户端真实IP: {}", paramName, ip);
                            return ip;
                        }
                    }
                }
            }

            // 3. 从socket连接中获取IP（可能是代理服务器IP）
            if (client.getRemoteAddress() instanceof java.net.InetSocketAddress) {
                java.net.InetSocketAddress inetSocketAddress = (java.net.InetSocketAddress) client.getRemoteAddress();
                String ip = inetSocketAddress.getAddress().getHostAddress();
                log.debug("从socket连接获取到客户端IP: {}", ip);
                return ip;
            }
        } catch (Exception e) {
            log.warn("获取客户端IP地址时发生异常", e);
        }

        return "unknown";
    }

}
