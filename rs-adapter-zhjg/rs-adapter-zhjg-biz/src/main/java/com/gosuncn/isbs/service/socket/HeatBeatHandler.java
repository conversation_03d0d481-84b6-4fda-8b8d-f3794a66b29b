package com.gosuncn.isbs.service.socket;

import com.gosuncn.isbs.common.socketio.SocketClientContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class HeatBeatHandler implements Runnable {

    /**
     * 心跳检测间隔
     */
    private static final long INTERVAL = 60000;

    @SuppressWarnings("static-access")
    @Override
    public void run() {
        while (true) {
            try {
                Thread.currentThread().sleep(INTERVAL);
                for (Map.Entry<String, Long> entry : SocketClientContext.heatBeatNumMap.entrySet()) {
                    Long heatBeatTime = entry.getValue();
                    String sessionId = entry.getKey();
                    if (System.currentTimeMillis() - INTERVAL >= heatBeatTime) {
                        SocketClientContext.logClientInfo(sessionId, "踢客户端下线");
                        SocketClientContext.clear(sessionId);
                    }
                }
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }
}
