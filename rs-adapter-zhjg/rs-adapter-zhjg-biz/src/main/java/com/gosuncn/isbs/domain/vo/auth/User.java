package com.gosuncn.isbs.domain.vo.auth;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by ace on 2017/9/10.
 */
@Data
public class User {

    /**
     * 用户id
     */
    private Long userid;

    /**
     * 用户名
     */
    private String name;

    /**
     * 警号
     */
    private String policeCode;

    /**
     * 民警id
     */
    private String policeId;

    /**
     * 民警名称
     */
    private String policeName;

    /**
     * 中队id
     */
    private String squadronId;
    /**
     * 中队名称
     */
    private String squadronName;
    /**
     * 监所id
     */
    private String prisonId;

    /***
     * 登录用户监所名
     */
    private String prisonName;

    /**
     * 登录用户名
     */
    private String username;

    /**
     * 登录用户密码
     */
    private String userpassword;

    /**
     * 登录用户角色
     */
    private List<SysRoleVO> roleList;

    /***
     * 登录角色id
     */
    private Integer roleId;

    /***
     * 登录角色姓名
     */
    private String roleName;
    /**
     * 拥有监所列表
     */
    private List<PrisonInfoVO> prisonList;

    /**
     * 在押人员编号
     */
    private String prisonerId;

    /**
     * 在押人员姓名
     */
    private String prisonerName;

    /***
     * 是否是属于支队 0:否  1:是
     */
    private String isZd;

    /**
     * token
     */
    @ApiModelProperty(value = "token")
    private String token;

    @ApiModelProperty("民警照片")
    private String photo;

	@ApiModelProperty("登录方式")
	private String loginType;

	@ApiModelProperty("人员类型")
	private String personnelType;

    @ApiModelProperty("监所类型")
    private Integer prisonType;

    /**
     * 岗位编码
     */
    private String rolepost;

	private Map<String, String> attributes;



}
