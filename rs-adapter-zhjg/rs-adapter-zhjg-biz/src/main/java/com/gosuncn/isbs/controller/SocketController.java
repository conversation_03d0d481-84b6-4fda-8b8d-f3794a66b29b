package com.gosuncn.isbs.controller;


import com.gosuncn.isbs.common.constant.SocketActionConstants;
import com.gosuncn.isbs.common.constant.TopicConstants;
import com.gosuncn.isbs.common.domain.dto.Result;
import com.gosuncn.isbs.common.socketio.SocketClientContext;
import com.gosuncn.isbs.domain.dto.socket.CustomPushMessageConditionDTO;
import com.gosuncn.isbs.domain.dto.socket.PushMessageForm;
import com.gosuncn.isbs.domain.vo.auth.User;
import com.gosuncn.isbs.domain.vo.socket.PushMessageAckVO;
import com.gosuncn.isbs.domain.vo.socket.SocketRelationMapVO;
import com.gosuncn.isbs.service.socket.SocketIOServiceImpl;
import com.gosuncn.isbs.service.socket.SocketService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api(tags = "Socket相关交互")
@RestController
@RequestMapping("/socket")
public class SocketController {

    @Autowired
    @Qualifier("isbsSocketService")
    private SocketService socketService;


    @ApiOperation(value = "debug-开启或关闭心跳日志记录")
    @PostMapping("debug/logHeartbeat")
    public Result<?> logHeartbeat() {
        SocketIOServiceImpl.LOG_HEARTBEAT = !SocketIOServiceImpl.LOG_HEARTBEAT;
        return Result.success(SocketIOServiceImpl.LOG_HEARTBEAT);
    }

    @ApiOperation(value = "debug-开启或关闭发送日志打印")
    @GetMapping("/log/push/isEnable")
    public Result<?> logIsEnable() {
        socketService.setLogPrintln(!socketService.isLogPrintln());
        return Result.success(socketService.isLogPrintln());
    }

    @ApiOperation(value = "推送消息给序列号-等待响应", notes = "{\"action\":\"getFaceCodesList\",\"serialNumbers\":[\"a0bfffeafada6d30\"],\"target\":\"android\",\"awaitSeconds\":7}" +
            "\r\n\r\n {\"action\":\"cleanStoreFace\",\"params\":[\"ALL\"],\"serialNumbers\":[\"a0bfffeafada6d30\"],\"target\":\"android\",\"awaitSeconds\":7}")
    @PostMapping("pushMessageToSerialNumberWaitReply")
    public Result<List<PushMessageAckVO>> pushMessageToSerialNumberWaitReply(@RequestBody @Valid PushMessageForm form) {
        if (form.getSerialNumbers() == null || form.getSerialNumbers().isEmpty()) {
            return Result.fail("序列号空");
        }
        List<PushMessageAckVO> ackMultiple = socketService.pushMessageToSerialNumberWaitReplyMultiple(form);
        return Result.success(ackMultiple);
    }

    @ApiOperation(value = "推送消息给客户端-等待响应")
    @PostMapping("pushMessageToClientWaitReply")
    public Result<?> pushMessageToClientWaitReply(@RequestBody @Validated PushMessageForm form) {
        if (StringUtils.isBlank(form.getSessionId())) {
            return Result.fail("客户端ID空");
        }
        List<PushMessageAckVO> ackMultiple = socketService.pushMessageWithConditionWaitReply(form);
        return Result.success(ackMultiple);
    }

    @ApiOperation(value = "推送消息给监室-等待响应")
    @PostMapping("pushMessageToRoomWaitReply")
    public Result<?> pushMessageToRoomWaitReply(@RequestBody @Validated PushMessageForm form) {
        if (StringUtils.isBlank(form.getTerminal()) || form.getRoomIds() == null || form.getRoomIds().isEmpty()) {
            return Result.fail();
        }
        List<PushMessageAckVO> ackMultiple = socketService.pushMessageToRoomWaitReplyMultiple(form);
        return Result.success(ackMultiple);
    }

    @ApiOperation(value = "推送消息给所有客户端-等待响应")
    @PostMapping("pushMessageToAllWaitReply")
    public Result<?> pushMessageToAllWaitReply(@RequestBody @Validated PushMessageForm form) {
        if (StringUtils.isBlank(form.getTerminal())) {
            return Result.fail();
        }
        List<PushMessageAckVO> ackMultiple = socketService.pushMessageWithConditionWaitReply(form);
        return Result.success(ackMultiple);
    }

    @ApiOperation(value = "获取所有客户端信息")
    @GetMapping("getAllClientInfo")
    @ApiImplicitParam(name = "terminal", allowableValues = "ALL, PLATFORM, CNP, CWP, EXTERNAL, IHCS", paramType = "query")
    public Result<?> getAllClientInfo(@RequestParam(required = false) String terminal) {
        if ("ALL".equals(terminal)) {
            terminal = null;
        }
        Map<String, String> r = SocketClientContext.clientTopicMap;
        List<String> collect = r.entrySet().stream()
                .filter(f -> TopicConstants.TOPIC_KEEP_ALIVE.equals(f.getValue()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        List<Map<String, Object>> records = new ArrayList<>();
        for (String sessionId : collect) {
            Map<String, Object> result = new HashMap<>();
            String _terminal = SocketClientContext.terminalMap.get(sessionId);
            if (!(StringUtils.isBlank(terminal) || terminal.equals(_terminal))) {
                continue;
            }
            result.put("sessionId", sessionId);
            result.put("terminal", _terminal);
            if (SocketActionConstants.PushMessageTerminalEnum.PLATFORM.name().equals(_terminal) ||
                    SocketActionConstants.PushMessageTerminalEnum.IHCS.name().equals(_terminal)) {
                User user = SocketClientContext.clientUserInfoMap.get(sessionId);
                if (user != null) {
                    result.put("prisonId", user.getPrisonId());
                    result.put("userId", user.getUserid());
                    result.put("name", user.getName());
                    result.put("username", user.getUsername());
                    if (SocketActionConstants.PushMessageTerminalEnum.PLATFORM.name().equals(_terminal)) {
                        result.put("roleId", user.getRoleId());
                        result.put("roleName", user.getRoleName());
                        result.put("rolepost", user.getRolepost());
                    }
                }
            } else if (SocketActionConstants.PushMessageTerminalEnum.EXTERNAL.name().equals(_terminal)) {
                result.put("macAddress", SocketClientContext.macAddressMap.get(sessionId));
            } else {
                result.put("serialNumber", SocketClientContext.serialNumberMap.get(sessionId));
            }
            records.add(result);
        }
        return Result.success(records);
    }

    @ApiOperation(value = "推送消息给监室")
    @PostMapping("pushMessageToRoom")
    public Result<?> pushMessageToRoom(@RequestBody @Validated PushMessageForm form) {
        if (StringUtils.isBlank(form.getTerminal()) || form.getRoomIds() == null || form.getRoomIds().isEmpty()) {
            return Result.fail("监室或者终端为空");
        }
        socketService.pushMessageToRoom(form);
        return Result.success();
    }

    @ApiOperation(value = "推送消息给序列号")
    @PostMapping("pushMessageToSerialNumber")
    public Result<?> pushMessageToSerialNumber(@RequestBody @Validated PushMessageForm form) {
        if (form.getSerialNumbers() == null || form.getSerialNumbers().isEmpty()) {
            return Result.fail("序列号空");
        }
        socketService.pushMessageToSerialNumber(form);
        return Result.success();
    }

    @ApiOperation(value = "推送消息给某监所下所有用户 -平台，不等待响应")
    @PostMapping("pushMessageToPrison")
    public Result<?> pushMessageToPrison(@RequestBody @Validated PushMessageForm form) {
        if (form.getPrisonIds() == null || form.getPrisonIds().isEmpty()) {
            return Result.fail("监所编号为空");
        }
        socketService.pushMessageToPrison(form);
        return Result.success();
    }

    @ApiOperation(value = "推送消息给某监所下所有用户 -平台，等待响应")
    @PostMapping("pushMessageToPrisonWaitReplyMultiple")
    public Result<?> pushMessageToPrisonWaitReplyMultiple(@RequestBody @Validated PushMessageForm form) {
        if (form.getPrisonIds() == null || form.getPrisonIds().isEmpty()) {
            return Result.fail("监所编号空");
        }
        List<PushMessageAckVO> list = socketService.pushMessageToPrisonWaitReplyMultiple(form);
        return Result.success(list);
    }

    @ApiOperation(value = "推送消息给用户 -平台，不等待响应")
    @PostMapping("pushMessageToUser")
    public Result<?> pushMessageToUser(@RequestBody @Validated PushMessageForm form) {
        if (form.getUserIds() == null || form.getUserIds().isEmpty()) {
            return Result.fail("userId空");
        }
        socketService.pushMessageToUser(form);
        return Result.success();
    }

    @ApiOperation(value = "推送消息给用户 -平台，等待响应")
    @PostMapping("pushMessageToUserWaitReplyMultiple")
    public Result<?> pushMessageToUserWaitReplyMultiple(@RequestBody @Validated PushMessageForm form) {
        if (form.getUserIds() == null || form.getUserIds().isEmpty()) {
            return Result.fail("userId空");
        }
        List<PushMessageAckVO> list = socketService.pushMessageToUserWaitReplyMultiple(form);
        return Result.success(list);
    }

    @ApiOperation(value = "推送消息给拥有该角色或岗位用户 -平台，不等待响应")
    @PostMapping("pushMessageToRole")
    public Result<?> pushMessageToRole(@RequestBody @Validated PushMessageForm form) {
        int effect = socketService.pushMessageToRole(form);
        return Result.success(effect);
    }

    @ApiOperation(value = "推送消息给拥有该角色或岗位用户 -平台，等待响应")
    @PostMapping("pushMessageToRoleWaitReplyMultiple")
    public Result<List<PushMessageAckVO>> pushMessageToRoleWaitReplyMultiple(@RequestBody @Validated PushMessageForm form) {
        List<PushMessageAckVO> list = socketService.pushMessageToRoleWaitReplyMultiple(form);
        return Result.success(list);
    }


    @ApiOperation(value = "发送消息给客户端不期待回应")
    @PostMapping("/pushMessageToClient")
    public Result<Void> pushMessageToClient(@RequestBody @Validated PushMessageForm form) {
        socketService.pushMessageToClient(form);
        return Result.success();
    }

    // ------------------------------------------------------------------------------------------------------------
    @ApiOperation(value = "获取当前的socketio连接信息")
    @GetMapping("/getSocketInfo")
    public Result<SocketRelationMapVO> getSocketInfo() {
        return Result.success(socketService.getSocketInfo());
    }

    @ApiOperation(value = "按指定条件发送指定消息格式到客户端")
    @PostMapping("/push/message/withCondition")
    public Result<Void> pushMessageWithCondition(@RequestBody PushMessageForm condition) {
        socketService.pushMessageWithCondition(condition);
        return Result.success();
    }

    @ApiOperation(value = "按指定条件发送指定消息格式到客户端-等待响应")
    @PostMapping("/push/message/withCondition/waitReply")
    public Result<List<PushMessageAckVO>> pushMessageWithConditionWaitReply(@RequestBody PushMessageForm condition) {
        return Result.success(socketService.pushMessageWithConditionWaitReply(condition));
    }


    @ApiOperation(value = "按指定条件发送自定义消息格式到客户端")
    @PostMapping("/custom/push/message/withCondition")
    public Result<Void> customPushMessageWithCondition(@RequestBody CustomPushMessageConditionDTO condition) {
        socketService.customPushMessageWithCondition(condition);
        return Result.success();
    }

    @ApiOperation(value = "按指定条件发送自定义消息格式到客户端-等待响应")
    @PostMapping("/custom/push/message/withCondition/waitReply")
    public Result<List<PushMessageAckVO>> customPushMessageWithConditionWaitReply(
            @RequestBody CustomPushMessageConditionDTO condition) {
        return Result.success(socketService.customPushMessageWithConditionWaitReply(condition));
    }

    /**
     * {@link} SocketActionConstants
     */
    @ApiOperation(value = "帮助！action参数说明")
    @GetMapping("help")
    public Result<?> help() {
        Map<String, String> map = new HashMap<>();
        map.put("getSerial", "获取仓内屏序列号，无参数");
        map.put("getStoreFaceCode", "获取储存的了哪些人员的人脸照片");
        map.put("getFaceCodesList", "来邦SDK中人脸列表");
        map.put("getStoreFaceBase64Img", "获取储存的指定人员的人脸base64照片。 参数1：police-d36e625f5abb432e809bbc2d2711793e");
        map.put("cleanStoreFace", "清除录入的人脸文件。 参数1：police-d36e625f5abb432e809bbc2d2711793e 或者ALL 清理监室所有");
        map.put("cleanStoreFaceByType", "清除录入的人脸文件。 根据类型清理。参数 police、prisoner");
        map.put("deleteFace", "清除录入的人脸");
        map.put("getConfig", "查看配置信息，SERVER_IP：服务器IP");
        map.put("web_homePageReload", "web刷新");
        map.put("hideFloat", "隐藏悬浮窗 arg1 true显示， false隐藏");
        map.put("getTemperature", "获取温度，3个可选择参数，arg1 获取n个温度， arg2 间隔毫秒数获取一次， arg3 没获取一次温度的超时毫秒");
        map.put("global_message", "全局消息");
        map.put("终端类型枚举（terminal）", "CNP 、 CWP 、 PLATFORM");
        return Result.success(map);
    }
}
