package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


@Data
public class RotatingStaffAuditDTO {
    @ApiModelProperty(value = "审核id，多个逗号隔开",required = true)
    @NotBlank(message = "审核id 不能为空")
    private String id;

    @JsonProperty("groupOpinion")
    @ApiModelProperty(value = "科组长审核意见",required = true)
    @NotNull(message = "科组长审核意见 不能为空")
    private Integer groupOpinion;

    @JsonProperty("remark")
    @ApiModelProperty(value = "备注")
    private String remark;
}
