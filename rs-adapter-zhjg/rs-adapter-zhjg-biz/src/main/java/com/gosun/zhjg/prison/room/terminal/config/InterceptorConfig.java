package com.gosun.zhjg.prison.room.terminal.config;

import com.gosun.zhjg.common.util.Log4jUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * @ClassName: InterceptorConfig
 * @Author: ren
 * @Description:
 * @CreateTIme: 2019/6/8 0008 下午 3:52
 **/
@Configuration
public class InterceptorConfig implements WebMvcConfigurer {

    @Autowired
    private UserTokenInterceptor userTokenInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        Log4jUtils.getInstance(getClass()).info("配置Token解析拦截器");
        registry.addInterceptor(userTokenInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns("/swagger-*/**"
                        , "/v2/api-docs"
                        , "/swagger-resources/configuration/security"
                        , "/swagger-resources/**"
                        , "/webjars/**"
                        , "/error");
    }
}
