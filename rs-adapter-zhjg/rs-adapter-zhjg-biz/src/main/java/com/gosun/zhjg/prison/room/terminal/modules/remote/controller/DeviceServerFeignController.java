package com.gosun.zhjg.prison.room.terminal.modules.remote.controller;

import com.alibaba.fastjson.JSONObject;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.BaseDeviceInscreenDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.BaseDeviceInscreenVO;
import com.gosun.zhjg.prison.room.terminal.modules.remote.feign.DeviceServerFeign;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "zhjg-device-server")
@RestController
@Slf4j
@RequestMapping
public class DeviceServerFeignController {

    @Autowired(required = false)
    private DeviceServerFeign deviceServerFeign;

    @Autowired
    private BaseDeviceInscreenDao deviceInscreenDao;

    /**
     * 渲染页面前-根据序列号获取监室信息,设备ip等<br/>
     * 2021年11月9日 ：去除对device-server工程依赖。获取监室信息是在仓内屏初始化阶段容易出现故障<br/>
     * <p>
     * {@link com.gosun.zhjg.device.server.modules.inscreen.controller.BaseDeviceInscreenController.getBySerialNum(String)}
     *
     * @param serialNum
     * @return
     */
    @RequestMapping(value = "/inscreen/basedeviceinscreen/getBySerialNum/{serialNum}", method = RequestMethod.GET)
    @ApiImplicitParam(paramType = "path", name = "serialNum", value = "序列号", required = true, dataType = "String")
    public R<?> getBySerialNum(@PathVariable("serialNum") String serialNum) {
//		R<?> r = deviceServerFeign.getBySerialNum(serialNum);
//		return r;
        BaseDeviceInscreenVO serial = deviceInscreenDao.getBySerialNum(serialNum);
        if (serial == null) {
            log.warn("=====> 序列号获取监室信息 serialNum={} 未找到监室信息！", serialNum);
            return R.ResponseError("未找到监室信息");
        }
        log.info("=====> 序列号获取监室信息 serialNum{}, roomName={} ({})", serialNum, serial.getRoomName(), serial.getId());
        return R.ResponseResult(serial);
    }

    /**
     * 渲染页面前-仓内屏人脸录入失败日志记录表-插入日志
     * <p>
     * {@link com.gosun.zhjg.device.server.modules.inscreen.controller.BaseDeviceInscreenLogController.save(BaseDeviceInscreenLogSaveDto)} 仓内屏人脸录入失败日志记录表-插入日志
     */
    @Deprecated
    @RequestMapping(value = "/inscreen/basedeviceinscreenlog/insert", method = RequestMethod.POST)
    @ApiOperation(value = "仓内屏人脸录入失败日志记录表-插入日志", responseContainer = "Map")
    public R<?> basedeviceinscreenlogSave(@RequestBody JSONObject form) {
//		R<?> r = deviceServerFeign.basedeviceinscreenlogSave(form);
//		return r;
        log.error("仓内屏人脸录入失败日志记录表-插入日志 form={}", form.toString());
        return R.ResponseOk();
    }
}
