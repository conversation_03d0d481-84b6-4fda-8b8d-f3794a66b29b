package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 事务类型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-13 09:50:07
 */
@Data
@TableName("room_work_type")
public class RoomWorkTypeEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @TableId(type = IdType.ASSIGN_UUID)

    private String id;
    /**
     * 事务名字
     */
    private String name;
    /**
     * 事务内容
     */
    private String content;

    /**
     * 监所编号
     */
    private String prisonId;
}
