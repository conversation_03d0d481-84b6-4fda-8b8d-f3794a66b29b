package com.gosun.zhjg.prison.room.terminal.common.service;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * 字典转换类
 *
 * <AUTHOR> on 2020/3/4
 * @email <EMAIL>
 * @date 2020/3/4
 */
@Service
public class DictService {

    @Autowired
    private RedisTemplate redisTemplate;

    private static final String REDIS_DICT_VALUE = "DICT";



    /**
     * 返回字典对应的值
     *
     * @param typeId
     * @param value
     * @return
     */
    public String dictValue(String typeId, String value) {
    	Object valueObject=redisTemplate.opsForValue().get(REDIS_DICT_VALUE + ":" + typeId+":"+value);
    	if(valueObject!=null) {
    		return valueObject.toString();
    	}else {
    		return "";
    	}

    }
//    public String dictValue(String typeId, String value) {
//        if (typeId != null && !typeId.equals("") && value != null && !value.equals("")) {
//            List<BaseDictDataVO> list = this.findByTypeCode(typeId);
//            if (list != null && list.size() > 0) {
//                for (BaseDictDataVO vo : list) {
//                    if (value.equals(vo.getDataCode())) {
//                        return vo.getDataName();
//                    }
//                }
//            }
//            return value;
//        }
//        return "";
//    }

//    /**
//     * 根据 name 返回 code
//     *
//     * @param typeId
//     * @param name
//     * @return
//     */
//    public String getNameByCode(String typeId, String name) {
//        if (typeId != null && !typeId.equals("") && name != null && !name.equals("")) {
//            List<BaseDictDataVO> list = this.findByTypeCode(typeId);
//            if (list.size() > 0) {
//                for (BaseDictDataVO vo : list) {
//                    if (name.equals(vo.getDataCode())) {
//                        return vo.getDataName();
//                    }
//                }
//            }
//            return name;
//        }
//        return "";
//    }

}
