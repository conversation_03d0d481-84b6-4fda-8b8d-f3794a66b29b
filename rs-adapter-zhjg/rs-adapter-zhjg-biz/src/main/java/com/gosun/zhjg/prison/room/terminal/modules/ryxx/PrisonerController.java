package com.gosun.zhjg.prison.room.terminal.modules.ryxx;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.pm.PrisonerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台 - 在所人员")
@RestController
@RequestMapping("/base/prisoner")
@Validated
public class PrisonerController {

    @Resource
    private PrisonerService prisonerService;

    @GetMapping("/getPrisonerList")
    @ApiOperation(value = "根据监室号查询人员信息")
    public CommonResult<List<PrisonerVwRespVO>> getPrisonerByJsh(@RequestParam("roomId") String roomId) {
        return success(prisonerService.getPrisonerListByJsh(roomId));
    }

}
