package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 面对面配置-分页查询
 * <AUTHOR>
 * @date 2024/3/9
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FaceToFaceConfigPageDTO extends AbstractPageQueryForm {

    @ApiModelProperty(value = "监所编码", hidden = true)
    private String prisonId;

    @ApiModelProperty(value = "状态(0禁用，1启用)")
    private Integer status;

    @ApiModelProperty(value = "登记内容，支持模糊匹配")
    private String content;

    @ApiModelProperty(value = "检查项目，支持模糊匹配")
    private String checkItems;

}
