package com.gosun.zhjg.prison.room.terminal.modules.jobhandle;

import com.gosun.zhjg.prison.room.terminal.modules.socket.service.TerminalEnrollFaceJob;
import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.handler.IJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 巡检
 * 仓内外屏定时去检查各设备人脸录入情况。对遗漏的进行补录
 * 建议cron：0 0 3 * * ?
 *
 * <AUTHOR>
 * @date 2022/11/24 19:36
 */
@Slf4j
@Component
public class TerminalEnrollFaceHandler extends IJobHandler implements InitializingBean {

    @Autowired
    private TerminalEnrollFaceJob terminalEnrollFaceJob;

    @Override
    public void afterPropertiesSet() {
        XxlJobExecutor.registJobHandler("TerminalEnrollFaceHandler", this);
    }

    @Override
    public void execute() {
        log.info("人脸录入巡检执行-TerminalEnrollFaceHandler");
        try {
            terminalEnrollFaceJob.job();
        } catch (Exception e) {
            log.error("人脸录入巡检任务执行异常", e);
            throw e;
        }
    }
}
