package com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/20 15:08
 */
@Data
public class TalkbackCallVO {
    /**
     * 服务地址
     */
    @ApiModelProperty("服务地址")
    private String serverUrl;
    /**
     * 本机号码
     */
    @ApiModelProperty("本机号码")
    private String localNum;
    /**
     * 本机密码
     */
    @ApiModelProperty("本机密码")
    private String localPassword;
    /**
     * 呼叫号码
     */
    @ApiModelProperty("呼叫号码")
    private String targetNum;
    /**
     * 通话记录id
     */
    private String historyId;
}
