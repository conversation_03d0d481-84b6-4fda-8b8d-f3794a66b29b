package com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.Data;

import java.io.Serializable;

/**
 * 终端版本管理
 *
 * <AUTHOR>
 * @date 2023/8/18 15:25
 */
@Data
@TableName("acp_pm_terminal_version_management")
public class TerminalVersionManagementEntity extends BaseDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * $column.comments
     */
    @TableId(type = IdType.ASSIGN_UUID)

    private String id;
    /**
     * 包名
     */
    private String packageName;
    /**
     * 软件包种类，区分包。固定前缀
     */
    private String fixed;
    /**
     * 版本号
     */
    private String versionNumber;
    /**
     * 主版本号
     */
    private Integer majorVersion;
    /**
     * 次版本号
     */
    private Integer minorVersion;
    /**
     * 修补版本号
     */
    private Integer patchVersion;
    /**
     * 地市缩写
     */
    private String cityAbbr;
    /**
     * 系统缩写
     */
    private String sysAbbr;
    /**
     * 机器缩写
     */
    private String machineAbbr;
    /**
     * 兼容版本
     */
    private String competingVersions;
    /**
     * 版本说明
     */
    private String releaseNotes;
    /**
     * 软件包状态：0缺失、1存在
     */
    private Integer state;
    /**
     * 第4位版本号，地市有
     */
    private Integer patchVersion2;
    /**
     * 终端类型,字典：TERMINAL_SYSTEM_TERMINAL_TYPE
     */
    private String terminalType;
    /**
     * 更新的包类型（upgrade_type） apk、web 字典：TERMINAL_SYSTEM_PACKAGE_TYPE
     */
    private String packageType;
    private String url;
}
