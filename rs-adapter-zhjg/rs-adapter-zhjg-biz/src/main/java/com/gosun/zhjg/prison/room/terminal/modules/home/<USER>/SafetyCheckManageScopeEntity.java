package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import java.util.Date;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 安全检查管理-范围设置(SafetyCheckManageScope)实体类
 *
 * <AUTHOR>
 * @since 2024-10-28 19:22:15
 */
@Data
@TableName("safety_check_manage_scope")
public class SafetyCheckManageScopeEntity implements Serializable {
    private static final long serialVersionUID = 666451554570240402L;
    /**
     * 监所编号
     */
    @TableId(type = IdType.ASSIGN_UUID)

    private String Id;

    private String prisonId;
    /**
     * 监室id列表
     */
    private String roomId;
    /**
     * 监室名称列表
     */
    private String roomName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人id
     */
    private String createUserId;
    /**
     * 创建人
     */
    private String createUserName;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 更新人id
     */
    private String updateUserId;
    /**
     * 更新人
     */
    private String updateUserName;


}

