package com.gosun.zhjg.prison.room.terminal.modules.app.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.common.util.FileUrlUtils;
import com.gosun.zhjg.prison.room.terminal.modules.app.dao.ScreenCandidDao;
import com.gosun.zhjg.prison.room.terminal.modules.app.entity.CnpLogEntity;
import com.gosun.zhjg.prison.room.terminal.modules.app.entity.ScreenCandidEntity;
import com.gosun.zhjg.prison.room.terminal.modules.app.service.CnpLogService;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.controller.CnpFaceController;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.CnpFaceForm;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.CnpFaceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.UUID;

/**
 * 仓内屏日志记录
 *
 * <AUTHOR>
 * @date 2021-11-09 21:13:40
 */
@Api(value = "仓内屏日志记录", tags = "仓内屏日志记录")
@Slf4j
@RestController
@RequestMapping("app/cnplog")
public class CnpLogController {

	@Autowired
	private CnpLogService cnpLogService;

	@Autowired
	private ScreenCandidDao screenCandidDao;

	@Autowired
	private CnpFaceController cnpFaceController;
	@Autowired
	private CnpFaceService cnpFaceService;

	@ApiOperation("关闭或启用日志写入数据库")
	@PostMapping("enableLog")
	public R<?> enableLog() {
		CnpLogService.ENABLE_WRITE_LOG = !CnpLogService.ENABLE_WRITE_LOG;
		return R.ResponseResult(CnpLogService.ENABLE_WRITE_LOG);
	}

	@ApiOperation("日志写入")
	@PostMapping
	public R<?> log(@RequestBody CnpLogEntity entity) {
		boolean f = cnpLogService.log(entity);
		return R.ResponseResult(f);
	}

	/**
	 * 记录已下发人脸照片
	 *
	 * 对应表：screen_candid。这里依然进行维护。之前这块代码在zhjg-basic-business中/photo/insertFace下，这里迁移过来
	 *
	 * @param faceFlag 1人脸图片，2抓拍图片
	 * @return
	 */
	@ApiOperation("记录已下发人脸照片")
	@PostMapping("logScreenPhoto")
	public R<?> screenPhoto(@RequestBody ScreenCandidEntity entity) {
		String filePath = entity.getFilePath();
		entity.setId(UUID.randomUUID().toString().replaceAll("-", ""));
		entity.setFilePath(FileUrlUtils.cutPath(entity.getFilePath()));
		if (entity.getRybh() != null && entity.getRybh().contains("-")) {
			String[] s = entity.getRybh().split("-");
			entity.setPersonType(s[0]);
			entity.setRybh(FileUrlUtils.cutPath(s[1]));
		}
		entity.setUpdateTime(new Date());
		entity.setIsDel(0);

		if (entity.getFaceFlag() != null && entity.getFaceFlag() == 1) {
			ScreenCandidEntity t = new ScreenCandidEntity();
			t.setIsDel(1);
			// 人脸照片保持最多1张有效
			screenCandidDao.update(t, new QueryWrapper<ScreenCandidEntity>().eq("rybh", entity.getRybh()).eq("serial_num", entity.getSerialNum()).eq("face_flag", entity.getFaceFlag()));
		}
		screenCandidDao.insert(entity);

		// 人脸照片 回写到《人脸信息库维护》cnp_face表
		// entity.getFaceFlag() == 1 标识人脸补录
		if (entity.getFaceFlag() == 1 && "camera".equals(entity.getOrigin())) {
			new Thread(new Runnable() {
				@Override
				public void run() {
					Integer personnelTypeNum = 2;
					if (entity.getPersonType() != null && entity.getPersonType().startsWith(CnpFaceController.PersonnelType_Prefix_Police)) {
						personnelTypeNum = 1;
					}
					// 能进入到这里 说明人脸补录成功。只需要修改该人员该设备录入状态即可。不用再进行重复的人脸下发
					// 下面的 cnpFaceForm.setSkipImportSerialNumbers 避免重复下发
					cnpFaceService.saveCnpFaceImportStatus(personnelTypeNum, entity.getRybh(), entity.getSerialNum(), true, "补录成功");

					CnpFaceForm cnpFaceForm = new CnpFaceForm();
					cnpFaceForm.setPhoto(filePath);
					cnpFaceForm.setPersonnelId(entity.getRybh());
					cnpFaceForm.setPersonnelType(entity.getPersonType());
					cnpFaceForm.setOrigin("device");
					cnpFaceForm.setSkipImportSerialNumbers(Lists.newArrayList(entity.getSerialNum()));
					try {
						R<?> r = cnpFaceController.importFace(cnpFaceForm);
						log.info("人员照片补录同步人脸库：{}", JSON.toJSONString(r));
					} catch (Exception e) {
						log.error("人员照片补录同步人脸库异常", e);
					}
				}
			}).start();
		}
		return R.ResponseOk();
	}
}
