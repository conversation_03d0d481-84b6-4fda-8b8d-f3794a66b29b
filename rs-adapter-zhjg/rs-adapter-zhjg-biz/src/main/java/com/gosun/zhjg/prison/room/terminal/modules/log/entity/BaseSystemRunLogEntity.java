package com.gosun.zhjg.prison.room.terminal.modules.log.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * base_system_run_log 实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date: 2023-12-07
 */
@Data
@TableName("base_system_run_log")
public class BaseSystemRunLogEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @TableId(type = IdType.UUID)
    private String id;
    /**
     * 日志名称
     */
    private String name;
    /**
     * 日志生成时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    private Date createTime;
    /**
     * 日志所属服务
     */
    private String serviceName;
    /**
     * 日志路径
     */
    private String logPath;
    /**
     * 删除标志
     */
    private Integer delFlag;

    /***
     * 日志内容
     */
    private String content;
}
