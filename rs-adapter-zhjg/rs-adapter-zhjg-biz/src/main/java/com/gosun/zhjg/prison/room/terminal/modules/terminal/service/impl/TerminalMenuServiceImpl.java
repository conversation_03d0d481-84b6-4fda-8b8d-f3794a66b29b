package com.gosun.zhjg.prison.room.terminal.modules.terminal.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gosun.zhjg.basic.business.modules.room.vo.AreaPrisonRoomPageVO;
import com.gosun.zhjg.common.constant.TerminalMenuCodeConstants;
import com.gosun.zhjg.common.enums.PlatformEnum;
import com.gosun.zhjg.common.enums.login.PersonnelTypeEnum;
import com.gosun.zhjg.common.exception.BaseException;
import com.gosun.zhjg.common.util.TreeLcmUtils;
import com.gosun.zhjg.prison.room.terminal.modules.home.service.BaseSystemRuleConfigService;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dao.jsyw.TerminalMenuDao;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dao.jsyw.TerminalMenuPrisonLinkDao;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dto.TerminalMenuPageDto;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dto.TerminalMenuSaveDto;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.entity.TerminalMenuEntity;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.entity.TerminalMenuPrisonLinkEntity;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.service.TerminalMenuService;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalMenuVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service("terminalMenuService")
public class TerminalMenuServiceImpl implements TerminalMenuService {

	@Autowired
	private TerminalMenuDao terminalMenuDao;
	@Autowired
	private TerminalMenuPrisonLinkDao terminalMenuPrisonLinkDao;
	@Autowired
	private BaseSystemRuleConfigService baseSystemRuleConfigService;

	@Override
	public void saveTerminalMenu(TerminalMenuSaveDto dto, String userid, String name) {
		TerminalMenuEntity entity = new TerminalMenuEntity();
		entity.setMenuName(dto.getMenuName());
		entity.setCode(dto.getCode());
		entity.setParentId(dto.getParentId());
		entity.setParentNodes(dto.getParentNodes());
		entity.setTerminalType(dto.getTerminalType());
		entity.setPersonnelType(dto.getPersonnelType());
		entity.setSortOrder(dto.getSortOrder());
		entity.setDelFlag(dto.getDelFlag());
		entity.setUpdateUserId(userid);
		entity.setUpdateUserName(name);
		entity.setCreateTime(new Date());
		terminalMenuDao.insert(entity);
	}

	@Override
	public List<TerminalMenuVO> findByPage(TerminalMenuPageDto form, Page page) {
		List<TerminalMenuVO> records = terminalMenuDao.findByPage(form, page);
		return records;
	}

	@Override
	public TerminalMenuVO findOneById(String id) {
		TerminalMenuVO vo = terminalMenuDao.findOneById(id);
		return vo;
	}

	@Override
	public List<TerminalMenuVO> buildTerminalMenuTree(TerminalMenuPageDto form) {
		List<TerminalMenuVO> records = terminalMenuDao.selectAllMenusAndMarkUnitOwned(form);
		for (TerminalMenuVO vo : records) {
			vo.setEnabled(vo.getLinkId() != null);
		}
		List<TerminalMenuVO> tree = TreeLcmUtils.build("-1", records);
		return tree;
	}

	/**
	 * 全量保存菜单关联
	 *
	 * @param prisonId
	 * @param selectedMenuIdList 用户勾选的菜单的id集合
	 * @param dbSavedMenuIdList  数据库保存的菜单的id集合
	 * @param userId
	 * @param name
	 */
	@Transactional(rollbackFor = Exception.class)
	public void saveMenuPrisonLink(String prisonId, List<String> selectedMenuIdList, List<String> dbSavedMenuIdList, String userid, String name) {
		List<String> canceledMenuIdList = dbSavedMenuIdList.stream().filter(f -> !selectedMenuIdList.contains(f)).collect(Collectors.toList());
		if (!canceledMenuIdList.isEmpty()) {
			// 库中存在，本次保存不存在的进行取消关联
			//@formatter:off
			terminalMenuPrisonLinkDao.delete(new LambdaQueryWrapper<TerminalMenuPrisonLinkEntity>()
					.eq(TerminalMenuPrisonLinkEntity::getOrgCode, prisonId)
					.in(TerminalMenuPrisonLinkEntity::getMenuId, canceledMenuIdList)
			);
			//@formatter:on
		}
		Date now = new Date();
		for (String menuId : selectedMenuIdList) {
			// 库中不存在插入
			if (!dbSavedMenuIdList.contains(menuId)) {
				TerminalMenuPrisonLinkEntity entity = new TerminalMenuPrisonLinkEntity();
				entity.setId(UUID.randomUUID().toString().replaceAll("-", ""));
				entity.setMenuId(menuId);
				terminalMenuPrisonLinkDao.insert(entity);
			}
		}
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void saveCnpPoliceMenuPrisonLink(String prisonId, List<String> menuIdList, String userid, String name) {
		TerminalMenuPageDto form = new TerminalMenuPageDto();
		form.setPrisonId(prisonId);
		form.setTerminalType(PlatformEnum.CNP.getValue());
		form.setPersonnelType(PersonnelTypeEnum.POLICE.getValue());
		List<String> dbSavedMenuIdList = terminalMenuDao.selectPrisonMenuIdList(form);
		this.saveMenuPrisonLink(prisonId, menuIdList, dbSavedMenuIdList, userid, name);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void saveCnpPrisonerMenuPrisonLink(String prisonId, List<String> menuIdList, String userid, String name) {
		TerminalMenuPageDto form = new TerminalMenuPageDto();
		form.setPrisonId(prisonId);
		form.setTerminalType(PlatformEnum.CNP.getValue());
		form.setPersonnelType(PersonnelTypeEnum.PRISONER.getValue());
		List<String> dbSavedMenuIdList = terminalMenuDao.selectPrisonMenuIdList(form);
		this.saveMenuPrisonLink(prisonId, menuIdList, dbSavedMenuIdList, userid, name);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void saveCwpMenuPrisonLink(String prisonId, List<String> menuIdList, String userid, String name) {
		TerminalMenuPageDto form = new TerminalMenuPageDto();
		form.setPrisonId(prisonId);
		form.setTerminalType(PlatformEnum.CWP.getValue());
		List<String> dbSavedMenuIdList = terminalMenuDao.selectPrisonMenuIdList(form);
		this.saveMenuPrisonLink(prisonId, menuIdList, dbSavedMenuIdList, userid, name);
	}

	/**
	 * 判断是否拥有某菜单权限
	 *
	 * @param prisonId
	 * @param menuCode
	 * @return
	 */
	@Override
	public boolean checkPrisonHasMenuPermission(String prisonId, String menuCode) {
		return terminalMenuDao.checkPrisonHasMenuPermission(prisonId, menuCode) > 0;
	}

	@Override
	public void checkRoomListHasMenuPermissionThrowException(List<String> roomIds, String menuCode) {
		if (roomIds == null) {
			roomIds = Collections.emptyList();
		}
		List<AreaPrisonRoomPageVO> rooms = Collections.emptyList();
		List<String> permissionPrisonIdList = Collections.emptyList();
		if (!roomIds.isEmpty()) {
			rooms = terminalMenuDao.selectRoomList(roomIds);
			// 校验内屏是否拥有菜单权限
			List<String> prisonIds = rooms.stream().map(AreaPrisonRoomPageVO::getPrisonId).distinct().collect(Collectors.toList());
			if (!prisonIds.isEmpty()) {
				// 查询出拥有该菜单权限的监所id集合
				permissionPrisonIdList = terminalMenuDao.selectHasMenuPrisonIdList(TerminalMenuCodeConstants.CNP_JSDM);
			}
		}
		for (String roomId : roomIds) {
			Optional<AreaPrisonRoomPageVO> optional = rooms.stream().filter(f -> Objects.equals(f.getId(), roomId)).findFirst();
			if (optional.isPresent()) {
				if (!permissionPrisonIdList.contains(optional.get().getPrisonId())) {
					// 未拥有权限
					throw new BaseException(String.format("%s所在监室未拥有相关仓内屏菜单权限", optional.get().getRoomName()));
				}
			} else {
				// 未找到监室信息
				throw new BaseException(String.format("%s所在监室未拥有相关仓内屏菜单权限", roomId));
			}
		}
	}

}
