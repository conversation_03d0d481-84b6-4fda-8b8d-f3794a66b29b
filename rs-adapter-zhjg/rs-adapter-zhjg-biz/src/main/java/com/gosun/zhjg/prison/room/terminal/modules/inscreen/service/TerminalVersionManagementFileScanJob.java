package com.gosun.zhjg.prison.room.terminal.modules.inscreen.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gosun.zhjg.basic.business.modules.file.dto.GetFileListDto;
import com.gosun.zhjg.basic.business.modules.file.feign.FileApi;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.constant.TerminalVersionManagementConstants;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.TerminalVersionManagementDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.TerminalVersionManagementEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

import static com.gosun.zhjg.prison.room.terminal.modules.inscreen.constant.TerminalVersionManagementConstants.SysAbbrTerminalTypeMap;

/**
 * 同步终端升级包ftp下文件到库
 *
 * <AUTHOR>
 * @date 2023/8/18 11:04
 */
@Slf4j
@Component
public class TerminalVersionManagementFileScanJob implements Runnable {

    @Autowired(required = false)
    private FileApi fileApi;

    @Autowired
    private TerminalVersionManagementDao terminalVersionManagementDao;

    @Override
    public void run() {
        R<List<String>> fileListRes = fileApi.getFileList(new GetFileListDto(TerminalVersionManagementConstants.Package_Folder, TerminalVersionManagementConstants.Package_File_Name_Regex));
        if (!Integer.valueOf(200).equals(fileListRes.getStatus()) || fileListRes.getData() == null) {
            return;
        }
        List<String> fileList = fileListRes.getData();
        log.debug("Ftp文件列表 {}", JSON.toJSONString(fileList));
        List<TerminalVersionManagementEntity> dbPackageList = this.selectDBPackageList();
        // 将ftp文件同步到库
        for (String fileName : fileList) {
            TerminalVersionManagementEntity entity = parseFileName2Entity(fileName);
            this.saveOrUpdate(entity, dbPackageList);
        }
        // 对比库中文件。如果实际文件不存在则标记丢失
        for (TerminalVersionManagementEntity entity : dbPackageList) {
            entity.setUpdateTime(new Date());
            String packageName = entity.getPackageName();
            if (fileList.contains(packageName) && (entity.getState() == null || entity.getState().equals(0))) {
                // 文件存在，且库中状态为丢失，则重新修改状态
                entity.setState(1);
                terminalVersionManagementDao.updateById(entity);
            } else if (!fileList.contains(packageName) && (entity.getState() == null || entity.getState().equals(1))) {
                // 文件不存在，且库中状态为存在。则标记为丢失
                entity.setState(0);
                terminalVersionManagementDao.updateById(entity);
            }
        }
    }

    /**
     * 查询数据库中所有包
     *
     * @return
     */
    private List<TerminalVersionManagementEntity> selectDBPackageList() {
        return terminalVersionManagementDao.selectList(new LambdaQueryWrapper<TerminalVersionManagementEntity>()
                .select(TerminalVersionManagementEntity::getId, TerminalVersionManagementEntity::getPackageName, TerminalVersionManagementEntity::getState)
        ).stream().collect(Collectors.toList());
    }

    private void saveOrUpdate(TerminalVersionManagementEntity entity, List<TerminalVersionManagementEntity> dbPackageList) {
        if (entity == null) {
            return;
        }
        String packageName = entity.getPackageName();
        List<String> idList = dbPackageList.stream().filter(f -> packageName != null && packageName.equals(f.getPackageName())).map(TerminalVersionManagementEntity::getId).collect(Collectors.toList());
        if (idList.isEmpty()) {
            terminalVersionManagementDao.insert(entity);
        } else {
            // 更新
            for (String id : idList) {
                entity.setId(id);
                entity.setUpdateTime(new Date());
                terminalVersionManagementDao.updateById(entity);
            }
        }
    }

    /**
     * 将文件名字解析成对讲
     *
     * @param fileName
     * @return
     */
    public static TerminalVersionManagementEntity parseFileName2Entity(String fileName) {
        Matcher matcher = TerminalVersionManagementConstants.Package_Pattern.matcher(fileName);
        if (!matcher.find()) {
            return null;
        }
        // 文件名，包名
        // 软件包种类，区分包。固定前缀
        String fixed = matcher.group(1);
        // versionContent =  ZH_SACP_v2.2.39.37 或者 SACP_v2.2.39.37
        String[] versionContent = matcher.group(2).split("_");
        // 版本号。(最后一个元素)
        String versionNumber = versionContent[versionContent.length - 1];
        Matcher versionMatcher = TerminalVersionManagementConstants.Version_Pattern.matcher(versionNumber);
        if (!versionMatcher.matches()) {
            // 无效的版本号
            return null;
        }
        String cityAbbr = null;
        String sysAbbr = null;
        String machineAbbr = null;
//region 解析 地市、系统、机器。其中 地市和机器可能为空。系统是可枚举项. 另外补充版本号中如果有4位 则一定有“地市缩写”
        if (versionContent.length == 2) {
            // WSW_v2.2.20
            sysAbbr = versionContent[0];
        } else if (versionContent.length == 4) {
            // ZH_WS_LB_v2.2.20
            cityAbbr = versionContent[0];
            sysAbbr = versionContent[1];
            machineAbbr = versionContent[2];
        } else if (versionContent.length == 3) {
            // ZH_WS_v2.2.20 或者 WS_LB_v2.2.20
            // 其中"WS"为可枚举项目
            String t0 = versionContent[0];
            String t1 = versionContent[1];
            Set<String> sets = SysAbbrTerminalTypeMap.keySet();
            for (String sys : sets) {
                if (sys.equals(t0) || (sys + "W").equals(t0)) {
                    // 第一位是系统，则第二位是机器类型
                    sysAbbr = t0;
                    machineAbbr = t1;
                    break;
                } else if (sys.equals(t1) || (sys + "W").equals(t1)) {
                    // 第二位是系统，则第一位是地市
                    sysAbbr = t1;
                    cityAbbr = t0;
                    break;
                }
            }
        }
//endregion
        if (StringUtils.isBlank(sysAbbr)) {
            // 未识别的系统
            return null;
        }
        log.info("{}  {}  {} 机器缩写：{}", cityAbbr, sysAbbr, versionNumber, machineAbbr);
        TerminalVersionManagementEntity entity = new TerminalVersionManagementEntity();
        entity.setPackageName(fileName);
        entity.setFixed(fixed);
        entity.setVersionNumber(versionNumber);
        entity.setMajorVersion(Integer.valueOf(versionMatcher.group(1)));
        entity.setMinorVersion(Integer.valueOf(versionMatcher.group(2)));
        entity.setPatchVersion(Integer.valueOf(versionMatcher.group(3)));
        entity.setCityAbbr(cityAbbr);
        entity.setSysAbbr(sysAbbr);
        entity.setMachineAbbr(machineAbbr);
        entity.setCompetingVersions(null);
        entity.setReleaseNotes(null);
        entity.setState(1);
        // 第4位版本号，默认0
        entity.setPatchVersion2(Integer.valueOf((versionMatcher.groupCount() >= 4 && StringUtils.isNotBlank(versionMatcher.group(4))) ? versionMatcher.group(4) : "0"));
        boolean packageTypeIsApk = SysAbbrTerminalTypeMap.containsKey(sysAbbr);
        if (packageTypeIsApk) {
            entity.setPackageType("apk");
            entity.setTerminalType(SysAbbrTerminalTypeMap.get(sysAbbr));
        } else {
            entity.setPackageType("web");
            entity.setTerminalType(SysAbbrTerminalTypeMap.get(sysAbbr.substring(0, sysAbbr.length() - 1)));
        }
        return entity;
    }
}
