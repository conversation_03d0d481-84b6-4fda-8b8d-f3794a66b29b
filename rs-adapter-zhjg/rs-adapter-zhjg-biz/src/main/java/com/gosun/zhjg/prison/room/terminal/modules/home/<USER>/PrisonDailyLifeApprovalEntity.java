package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 一日生活支队审批表
 *
 * <AUTHOR>
 * @date 2024/9/19
 */
@Data
@TableName("prison_daily_life_approval")
@ApiModel(value = "一日生活支队审批表", description = "一日生活支队审批表")
public class PrisonDailyLifeApprovalEntity implements Serializable {


    @TableId(value = "id")
    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "日常生活ID")
    private String dailyLifeId;

    /**
     * {@link com.gosun.zhjg.prison.room.terminal.common.enums.ApprovalStatusEnum}
     */
    @ApiModelProperty(value = "审批状态，1待审批；2审批通过；3审批不通过")
    private Integer approvalStatus;

    @ApiModelProperty(value = "审批时间")
    private Date approvalTime;

    /**
     * {@link com.gosun.zhjg.prison.room.terminal.common.enums.ApprovalResultEnum}
     */
    @ApiModelProperty(value = "审批结果")
    private Integer approvalResult;

    @ApiModelProperty(value = "审批意见")
    private String opinion;

    @ApiModelProperty(value = "审批人ID")
    private String approvalPersonId;

    @ApiModelProperty(value = "审批人姓名")
    private String approvalPersonName;

    /**
     * {@link com.gosun.zhjg.prison.room.terminal.common.enums.DelFlagEnum}
     */
    @ApiModelProperty(value = "删除标识，0正常、1删除。默认0")
    private Integer delFlag;

    @ApiModelProperty(value = "创建人ID")
    private String createPersonId;

    @ApiModelProperty(value = "创建人姓名")
    private String createPersonName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "监所ID")
    private String prisonId;

}
