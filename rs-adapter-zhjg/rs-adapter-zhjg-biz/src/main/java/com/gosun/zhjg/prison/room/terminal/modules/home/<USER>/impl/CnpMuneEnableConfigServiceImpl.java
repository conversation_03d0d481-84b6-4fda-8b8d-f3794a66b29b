package com.gosun.zhjg.prison.room.terminal.modules.home.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gosun.zhjg.common.constant.KafkaTopicsConstants;
import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.prison.room.terminal.modules.home.dao.CnpMuneEnableConfigDao;
import com.gosun.zhjg.prison.room.terminal.modules.home.dto.CnpMuneEnableConfigSaveDto;
import com.gosun.zhjg.prison.room.terminal.modules.home.entity.CnpMuneEnableConfigEntity;
import com.gosun.zhjg.prison.room.terminal.modules.home.service.CnpMuneEnableConfigService;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.SocketService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service("cnpMuneEnableConfigService")
public class CnpMuneEnableConfigServiceImpl implements CnpMuneEnableConfigService {

	@Autowired
	private CnpMuneEnableConfigDao cnpMuneEnableConfigDao;

	@Autowired
	private KafkaTemplate<String, String> kafkaTemplate;

	@Autowired
	private SocketService socketService;

	private final static String TOPIC = "cnp_mune_enable_config_change";

	/**
	 * 全量保存，会删除再插入
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public void saveCnpMuneEnableConfig(CnpMuneEnableConfigSaveDto[] dto0, String userId, String userName) {
		// 根据room_id删除
		List<String> roomIds = Arrays.stream(dto0).map(CnpMuneEnableConfigSaveDto::getRoomId).distinct().collect(Collectors.toList());
		cnpMuneEnableConfigDao.delete(new QueryWrapper<CnpMuneEnableConfigEntity>().in("room_id", roomIds));

		// 插入
		for (CnpMuneEnableConfigSaveDto dto : dto0) {
			if(StringUtils.isBlank(dto.getMenuCode())) {
				continue;
			}
			CnpMuneEnableConfigEntity entity = new CnpMuneEnableConfigEntity();
			entity.setId(UUID.randomUUID().toString().replaceAll("-", ""));
			entity.setMenuCode(dto.getMenuCode());
			entity.setRoomId(dto.getRoomId());
			entity.setEnable(1);
			entity.setDelFlag(0);
			entity.setUpdateUserid(userId);
			entity.setUpdateUsername(userName);
			entity.setUpdateTime(new Date());
			cnpMuneEnableConfigDao.insert(entity);
		}
		// socket发送变更
		Map<String, List<CnpMuneEnableConfigSaveDto>> collect = Arrays.stream(dto0).collect(Collectors.groupingBy(CnpMuneEnableConfigSaveDto::getRoomId));

		collect.forEach((k, v) -> {
//			PushMessage pushMessage = new PushMessage();
			List<String> menuCodeCollect = v.stream().map(CnpMuneEnableConfigSaveDto::getMenuCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
			List<String> serialNumbers = socketService.getCnpSerialNumberByRoomId(Arrays.asList(k));
			PushMessageForm pushFrom = new PushMessageForm();
			pushFrom.setSerialNumbers(serialNumbers);
			Object[] objects = new Object[1];
			objects[0]=JSON.toJSONString(menuCodeCollect);
			pushFrom.setParams(objects);
			pushFrom.setTarget(SocketActionConstants.PushMessageTargetEnum.web.name());
			pushFrom.setTerminal(SocketActionConstants.PushMessageTerminalEnum.CNP.name());
			pushFrom.setAction(TOPIC);
			String form = JSON.toJSONString(pushFrom);
			kafkaTemplate.send(KafkaTopicsConstants.Kafkatopic_PushMessageToSerialNumber, form);
		});
	}

	@Override
	public void deleteByIds(String[] ids, String userId, String userName) {
		for (String id : ids) {
			CnpMuneEnableConfigEntity entity = new CnpMuneEnableConfigEntity();
			entity.setId(id);
			entity.setDelFlag(1);
			entity.setUpdateTime(new Date());
			entity.setUpdateUserid(userId);
			entity.setUpdateUsername(userName);
			cnpMuneEnableConfigDao.updateById(entity);
		}
	}

	@Override
	public List<String> getMenuCodes(String roomId) {
		// @formatter:off
		List<String> menuCodes = cnpMuneEnableConfigDao.selectList(new QueryWrapper<CnpMuneEnableConfigEntity>().eq("room_id", roomId)
				.eq("enable", 1)
				.eq("del_flag", 0)
				.select("menu_code")
				).stream().map(CnpMuneEnableConfigEntity::getMenuCode).collect(Collectors.toList());
		// @formatter:on

		return menuCodes;
	}
}
