package com.gosun.zhjg.prison.room.terminal.modules.home.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 面对面管理-仓内屏配置保存DTO
 *
 * <AUTHOR>
 * @date 2025/1/11
 */
@Data
@ApiModel("面对面管理-仓内屏配置保存DTO")
public class FaceToFaceConfigSaveDTO implements Serializable {

    @ApiModelProperty(value = "主键ID，更新必填")
    private String id;

    @ApiModelProperty(value = "业务登记信息",example = "1、点名报数；2、了解前一天室内相关情况、疾病人员情况；3、安全检查、卫生检查；4、布置监室相关工作；5、强调纪律的重要 性；6、每日就餐前背诵监规；7、集体教育。")
    private String content;

    @ApiModelProperty(value = "检查内容",example = "1.了解昨日室内情况、疾病人员情况。2.安全检查。3.卫生检查。4.布置监室相关工作。5.强调纪律的重要性。")
    private String checkItems;

    @ApiModelProperty(value = "状态(0-禁用,1-启用)",example = "1")
    private Integer status;

    @ApiModelProperty(value = "登记时间，默认为空，空表示系统当前时间")
    private String registerTime;

    @ApiModelProperty(value = "登记人用户ID，默认为空，空表示系统当前登录用户ID")
    private String registerUserId;

    @ApiModelProperty(value = "登记人用户姓名，默认为空，空表示系统当前登录用户姓名")
    private String registerName;

    @ApiModelProperty(value = "监所ID，默认-1")
    private String prisonId;

    @ApiModelProperty(value = "监室ID，默认-1")
    private String roomId;

}

