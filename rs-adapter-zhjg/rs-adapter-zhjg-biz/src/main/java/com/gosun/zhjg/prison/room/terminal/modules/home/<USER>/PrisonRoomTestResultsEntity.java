package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * prison_room_test_results-测评结果表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-07 17:30:04
 */
@Data
@TableName("prison_room_test_results")
public class PrisonRoomTestResultsEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @TableId(type = IdType.ASSIGN_UUID)

    private String id;
    /**
     * 在押人员id
     */
    private String prisonerId;
    /**
     * 在押人员name
     */
    private String prisonerName;
    /**
     * 试卷id
     */
    private String testId;
    /**
     * 得分详情
     */
    private String details;
    /**
     * 测评得分
     */
    private Float score;
    /**
     * 测评结果
     */
    private String result;
    /**
     * 测评时间
     */
    private Date testTime;

    /**
     * 监所编号
     */
    private String prisonId;
}
