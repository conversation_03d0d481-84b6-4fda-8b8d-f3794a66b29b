package com.gosun.zhjg.prison.room.terminal.modules.home.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 谈话教育任务
 * <AUTHOR>
 * @date 2025/3/11 9:34
 */
@NoArgsConstructor
@Data
public class TalkTaskVO implements Serializable {

    private static final long serialVersionUID = -7012481469654964464L;

    private String id;

    private String rybh;

    private String prisonId;

    private String dataSource;

    private String talkType;

    private String talkStatus;

    private Date createTime;

    private String createUserId;

    private String createUserName;

    private Date updateTime;

    private String updateUserId;

    private String updateUserName;

    private String thirdId;
}
