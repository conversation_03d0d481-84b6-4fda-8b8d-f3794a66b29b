package com.gosun.zhjg.prison.room.terminal.modules.home.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 面对面管理-配置启用DTO
 *
 * <AUTHOR>
 * @date 2025/1/11
 */
@Data
@ApiModel("面对面管理-配置启用DTO")
public class FaceToFaceConfigEnableDTO implements Serializable {

    @ApiModelProperty(value = "主键ID，必填",required = true,example = "255D846F30684269B1B0BB921973D676")
    private String id;

    @ApiModelProperty(value = "状态(0-禁用,1-启用)")
    private Integer status;

}

