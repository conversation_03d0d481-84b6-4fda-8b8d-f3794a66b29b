package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 一日生活 制度模板-父表。
 *
 * <AUTHOR>
 * @date 2023/4/17 14:15
 */
@Data
@TableName("prison_daily_life_template")
public class PrisonDailyLifeTemplateEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * $column.comments
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * $column.comments
     */
    private String prisonId;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * $column.comments
     */
    private String createUserId;
    /**
     * $column.comments
     */
    private String createUserName;
    /**
     * $column.comments
     */
    private Date createTime;
    /**
     * $column.comments
     */
    private String updateUserId;
    /**
     * $column.comments
     */
    private String updateUserName;
    /**
     * $column.comments
     */
    private Date updateTime;
    /**
     * $column.comments
     */
    @TableLogic
    private Integer delFlag;
}
