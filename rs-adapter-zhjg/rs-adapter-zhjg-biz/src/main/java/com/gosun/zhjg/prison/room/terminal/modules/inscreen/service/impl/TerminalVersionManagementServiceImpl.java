package com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.TerminalVersionInfoDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.TerminalVersionInfoEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.TerminalService;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.TerminalVersionManagementService;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.TerminalGetConfigInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TerminalVersionManagementServiceImpl implements TerminalVersionManagementService {

	@Autowired
	private TerminalService terminalService;
	@Autowired
	private TerminalVersionInfoDao terminalVersionInfoDao;

	/**
	 * 根据ip同步设备版本信息
	 *
	 * @param ip
	 * @return
	 */
	public TerminalVersionInfoEntity syncVersionInfo(String ip) {
		TerminalGetConfigInfoVO configInfo;
		try {
			JSONObject jsonObject = terminalService.getConfigInfo(ip);
			configInfo = JSON.parseObject(jsonObject.toJSONString(), TerminalGetConfigInfoVO.class);
			if (configInfo == null || StringUtils.isBlank(configInfo.getDevSerial())) {
//                log.warn("获取配置信息异常");
				return null;
			}
		} catch (Exception ex) {
//            log.warn("", ex);
			return null;
		}
		TerminalVersionInfoEntity entity = new TerminalVersionInfoEntity();
		entity.setSerialNumber(configInfo.getDevSerial());
		entity.setIp(configInfo.getDevIp());
		entity.setApkVersion(configInfo.getAppVersion());
		entity.setWebVersion(configInfo.getWebVersion());
		entity.setCompatibleWebVersions(configInfo.getMatchVer());
		entity.setApkUpgradeTime(configInfo.getApkUpgradeTime());
		entity.setWebUpgradeTime(configInfo.getWebUpgradeTime());
		int effectRow = terminalVersionInfoDao.updateById(entity);
		if (effectRow == 0) {
			terminalVersionInfoDao.insert(entity);
		}
		return entity;
	}
}
