package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;

/**
 * rotating_staff_prisoner 实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date: 2024-10-28
 */
@Data
@TableName("rotating_staff_prisoner")
public class RotatingStaffPrisonerEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    private String id;
    /**
     * 轮值管理编号
     */
    private String staffId;
    /**
     * 人员编号
     */
    private String prisonerId;
    /**
     * 轮值日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date staffDate;
    /**
     * 人员序号
     */
    private Integer staffSortNo;

    /***
     * 周名
     */
    private String weekName;

    /***
     * 删除标志 1-删除
     */
    private Integer delFlag;

    /***
     * 1-待审核 2-审核通过
     */
    private Integer status;
}
