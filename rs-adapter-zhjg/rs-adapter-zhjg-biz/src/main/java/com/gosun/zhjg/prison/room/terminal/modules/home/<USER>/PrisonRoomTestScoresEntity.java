package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * prison_room_test_scores-测评分数表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-07 17:30:04
 */
@Data
@TableName("prison_room_test_scores")
public class PrisonRoomTestScoresEntity  {
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @TableId(type = IdType.ASSIGN_UUID)

    private String id;
    /**
     * 情况
     */
    private String situation;
    /**
     * 得分起
     */
    private Integer scoreStart;
    /**
     * 得分止
     */
    private Integer scoreEnd;
    /**
     * 试卷id
     */
    private String testId;
}
