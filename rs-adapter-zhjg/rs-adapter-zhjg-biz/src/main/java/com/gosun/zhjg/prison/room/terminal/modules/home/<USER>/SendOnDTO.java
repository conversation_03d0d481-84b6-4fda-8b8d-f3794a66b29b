package com.gosun.zhjg.prison.room.terminal.modules.home.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@ApiModel("信件管理-寄出")
public class SendOnDTO {

    @ApiModelProperty("审核编号 批量逗号隔开")
    @NotBlank(message = "审核编号 不能为空")
    private String id;

    @JsonProperty("receivePerson")
    @ApiModelProperty(value = "收信人",required = true)
    @NotBlank(message = "收信人 不能为空")
    private String receivePerson;

    @JsonProperty("relation")
    @ApiModelProperty(value = "关系",required = true)
    @NotBlank(message = "关系 不能为空")
    private String relation;

    @JsonProperty("mailNo")
    @ApiModelProperty(value = "信件邮编")
    private String mailNo;

    @JsonProperty("receivePrison")
    @ApiModelProperty(value = "收信单位")
    private String receivePrison;

    @JsonProperty("receiveAddress")
    @ApiModelProperty(value = "收信地址")
    private String receiveAddress;

    @JsonProperty("sendPerson")
    @ApiModelProperty(value = "寄出人",required = true)
    @NotBlank(message = "寄出人 不能为空")
    private String sendPerson;

    @JsonProperty("sendTime")
    @ApiModelProperty(value = "寄出时间",required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "寄出时间 不能为空")
    private Date sendTime;

    @JsonProperty("sendRemark")
    @ApiModelProperty(value = "寄出备注")
    private String sendRemark;



}
