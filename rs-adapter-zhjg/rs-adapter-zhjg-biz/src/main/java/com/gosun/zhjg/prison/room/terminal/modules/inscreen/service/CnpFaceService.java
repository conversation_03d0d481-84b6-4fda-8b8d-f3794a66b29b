package com.gosun.zhjg.prison.room.terminal.modules.inscreen.service;

import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.CnpFaceImportStatusEntity;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.PushMessageAckVO;

import java.util.List;

import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.CnpFaceForm;

public interface CnpFaceService {

    void batchSendFace(CnpFaceForm form);

    /**
     * 获取人员照片
     *
     * @param personnelType 人员类型 1警员、2在押人员
     * @param personnelId   人员编号、警员id
     * @return
     */
    String getPersonnelPhoto(Integer personnelType, String personnelId);

    PushMessageAckVO enrollFace(Integer personnelType, String personnelId, String photo, String serialNumber);

    void asyncEnrollFace(Integer personnelType, String personnelId, String personnelPhoto, List<String> serialNumbers);

    /**
     * 仓内外屏人脸照片导入状态 保存
     *
     * @param personnelType 人员类型 1警员、2在押人员
     * @param personnelId
     * @param serialNumber
     * @param state         导入状态 1成功、0失败
     * @param response
     * @return 新增的时的id
     */
    void saveCnpFaceImportStatus(Integer personnelType, String personnelId, String serialNumber, boolean state, String response);

    /**
     * 获取人脸库维护中的 照片
     *
     * @param personnelType
     * @param personnelId
     * @return
     */
    String getCnpFacePhoto(Integer personnelType, String personnelId);

    /**
     * 根据序列号，查询改设备下发状态
     *
     * @param serialNumber
     * @return
     */
    List<CnpFaceImportStatusEntity> getCnpFaceImportStatusBySerialNumber(String serialNumber);
}
