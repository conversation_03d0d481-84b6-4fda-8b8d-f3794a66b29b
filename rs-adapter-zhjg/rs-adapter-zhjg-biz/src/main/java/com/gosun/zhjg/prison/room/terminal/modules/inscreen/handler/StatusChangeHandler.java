package com.gosun.zhjg.prison.room.terminal.modules.inscreen.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.BaseDeviceInscreenDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.ConnectHandlerDTO;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.BaseDeviceInscreenEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.TerminalService;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.TerminalVersionManagementService;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.handler.AbstractHandler;
import com.rs.module.base.dao.pm.device.BaseDeviceDao;
import com.rs.module.base.entity.pm.device.BaseDeviceDO;
import com.rs.module.base.enums.DeviceStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @ClassName StatusChangeHandler
 * @Description 状态改变
 * <AUTHOR>
 * @Date 2025/7/15 10:43
 * @Version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StatusChangeHandler extends AbstractHandler<ConnectHandlerDTO> {
    private final BaseDeviceDao baseDeviceDao;
    private final BaseDeviceInscreenDao baseDeviceInscreenDao;
    private final TerminalVersionManagementService terminalVersionManagementService;
    private final TerminalService terminalService;

    @Override
    protected ConnectHandlerDTO doHandle(ConnectHandlerDTO connectHandlerDTO) {

        BaseDeviceDO baseDeviceDO = baseDeviceDao.selectOne(new LambdaQueryWrapper<BaseDeviceDO>()
                .eq(BaseDeviceDO::getIpAddress, connectHandlerDTO.getIp()));

        if (StringUtils.isBlank(connectHandlerDTO.getIp())) {
            throw new ServerException("ip不能为空");
        }
        if (baseDeviceDO == null) {
            throw new ServerException("设备不存在");
        }
        if (connectHandlerDTO.getStatus().equals(DeviceStatusEnum.ONLINE.getCode())) {
            baseDeviceDO.setOnlineTime(new Date());
        }
        baseDeviceDO.setDeviceStatus(DeviceStatusEnum.ONLINE.getCode());
        baseDeviceDao.updateById(baseDeviceDO);
        connectHandlerDTO.setBaseDeviceDO(baseDeviceDO);
        BaseDeviceInscreenEntity baseDeviceInscreenEntity = null;
        List<BaseDeviceInscreenEntity> baseDeviceInscreenEntityList = baseDeviceInscreenDao.selectList(new LambdaQueryWrapper<BaseDeviceInscreenEntity>()
                .eq(BaseDeviceInscreenEntity::getDeviceIp, connectHandlerDTO.getIp())
        );
        if (!baseDeviceInscreenEntityList.isEmpty()) {
            baseDeviceInscreenEntity = baseDeviceInscreenEntityList.get(0);
        }
        if (baseDeviceInscreenEntity == null) {
            throw new ServerException("终端设备不存在");
        }
        //更新设备号
        baseDeviceInscreenEntity.setSerialNumber(connectHandlerDTO.getSerialNum());
        baseDeviceInscreenDao.updateById(baseDeviceInscreenEntity);
        connectHandlerDTO.setBaseDeviceInscreenEntity(baseDeviceInscreenEntity);
        try {
            //同步版本信息
            terminalVersionManagementService.syncVersionInfo(connectHandlerDTO.getIp());
        } catch (Exception e) {

        }
        return connectHandlerDTO;
    }
}
