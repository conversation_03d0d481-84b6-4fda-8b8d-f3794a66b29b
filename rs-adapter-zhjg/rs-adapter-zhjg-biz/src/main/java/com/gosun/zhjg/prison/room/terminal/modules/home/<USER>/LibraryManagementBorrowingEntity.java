package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("library_management_borrowing")
public class LibraryManagementBorrowingEntity implements Serializable {
    //图书借阅
    //id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    //借阅人名
    private String personnel;

    //借阅人id
    private String personnelId;

    //图书id
    private String libraryId;

    //监室id
    private String roomId;

    //登记时间
    private Date createTime;

    //状态
    private String status;

    //监所编号
    private String prisonId;

    //更新人
    private String updateUser;

    //更新时间
    private Date updateTime;

    //更新人id
    private String updateUserId;
}
