package com.gosun.zhjg.prison.room.terminal.common;

import com.gosun.zhjg.common.enums.TerminalSignatureConfirmBusinessTypeEnum;
import lombok.Getter;

/**
 * 仓内屏待办事项
 * {@link TerminalSignatureConfirmBusinessTypeEnum}
 */
@Getter
public enum CnpWorkRemindTypeEnum {
    FYDJ("1","服药确认"),
    PRISON_HEALTH_CHECK("2","入所健康检查"),
    GRTZ("3","个人通知"),
    QLYWGZ("4","权利义务告知"),
    GOODS_CONSIGNMENT("5","物品顾送-签名确认"),
    CONFLICT_CONSIGNMENT("6","社会矛盾化解-签名确认"),
    MONEY_REY("7","钱款顾送-签名确认")

    ;


    private String key;

    private String value;

    CnpWorkRemindTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValue(String index) {
        if (index == null) {
            return null;
        }
        for (RoomRepresentStatusEnum c : RoomRepresentStatusEnum.values()) {
            if (index.contains(c.getKey())) {
                return c.getKey();
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
