package com.gosun.zhjg.prison.room.terminal.modules.inscreen.service;

import com.alibaba.fastjson.JSONObject;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.TerminalGetConfigInfoVO;

public interface TerminalService {

    /**
     * 获取终端配置端口
     *
     * @return
     */
    int getTerminalServicePort();

    /**
     * 根据ip获取设备信息
     *
     * @param ip 设备ip
     * @return
     */
    JSONObject getConfigInfo(String ip);

    /**
     * 升级包、web或者android
     *
     * @param ip          设备ip
     * @param packageType 1、apk;2:web包
     * @param updateVer
     * @param downLoadUrl
     */
    void updateApp(String ip, int packageType, String updateVer, String downLoadUrl);
    void updateApp(String ip, int packageType, String updateVer, String downLoadUrl, String callbackArgs, boolean isAnsy);

    /**
     * 删除人脸
     *
     * @param ip
     * @param id police-a392616ee634413695d78c2f3c2d58e1
     */
    void userDelete(String ip, String id);

    /**
     * 重启设备
     *
     * @param ip
     */
    void reStartApp(String ip);


    void syncSerialAndMac(String deviceId,String prisonId);


    void saveConfigInfo(JSONObject object) throws Exception;
}
