package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
* non_involved_items 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2024-06-07
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="NonInvolvedItemsSearchDTO对象", description="非涉案物品")
public class NonInvolvedItemsSearchDTO extends AbstractPageQueryForm implements Serializable {

   @JsonProperty("id")
   @ApiModelProperty(value = "主键")
   private String id;

   @JsonProperty("roomId")
   @ApiModelProperty(value = "监室号")
   private String roomId;

   @JsonProperty("roomName")
   @ApiModelProperty(value = "监室名字")
   private String roomName;

   @JsonProperty("prisonerId")
   @ApiModelProperty(value = "人员编号")
   private String prisonerId;

   @JsonProperty("prisonerName")
   @ApiModelProperty(value = "人员姓名")
   private String prisonerName;

   @JsonProperty("itemName")
   @ApiModelProperty(value = "物品名称")
   private String itemName;

   @JsonProperty("itemStatus")
   @ApiModelProperty(value = "物品状态")
   private Integer itemStatus;

   @JsonProperty("depositTimeStart")
   @ApiModelProperty(value = "存入时间 开始")
   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private Date depositTimeStart;

   @JsonProperty("depositTimeEnd")
   @ApiModelProperty(value = "存入时间 结束")
   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private Date depositTimeEnd;

   @JsonProperty("removalTimeStart")
   @ApiModelProperty(value = "取出时间-开始")
   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private Date removalTimeStart;

   @JsonProperty("removalTimeEnd")
   @ApiModelProperty(value = "取出时间-结束")
   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private Date removalTimeEnd;


}