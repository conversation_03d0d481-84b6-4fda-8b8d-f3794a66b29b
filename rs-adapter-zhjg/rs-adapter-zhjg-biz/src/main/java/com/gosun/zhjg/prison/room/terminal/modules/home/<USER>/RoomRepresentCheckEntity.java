package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.*;

import lombok.Data;

/**
 * room_represent_check 实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date: 2024-01-10
 */
@Data
@TableName("room_represent_check")
public class RoomRepresentCheckEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @TableId(type = IdType.UUID)
    private String id;
    /**
     * 点名批次号
     */
    private String presentNo;
    /**
     * 监室号
     */
    private String roomId;
    /**
     * 监室名字
     */
    private String roomName;
    /**
     * 人员编号
     */
    private String prisonerId;
    /**
     * 人员姓名
     */
    private String prisonerName;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /***
     * 补录状态
     */
    @ApiModelProperty(value = "补录状态")
    private Integer status;

    /***
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;
}
