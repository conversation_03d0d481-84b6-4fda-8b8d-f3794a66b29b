package com.gosun.zhjg.prison.room.terminal.common.enums;

import lombok.Getter;

/**
 * 生活制度启用或停用
 *
 * <AUTHOR>
 * @date 2024/9/18
 */
@Getter
public enum PrisonDailyLifeEnableFlagEnum {

    ENABLE(1,"启用"),
    DISABLE(0,"停用");

    private final Integer flag;
    private final String name;

    PrisonDailyLifeEnableFlagEnum(Integer flag, String name){
        this.flag = flag;
        this.name = name;
    }

}
