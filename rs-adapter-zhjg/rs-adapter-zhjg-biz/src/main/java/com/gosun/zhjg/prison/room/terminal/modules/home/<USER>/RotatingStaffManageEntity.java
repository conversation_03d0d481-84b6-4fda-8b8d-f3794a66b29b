package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * rotating_staff_manage 实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date: 2024-10-28
 */
@Data
@TableName("rotating_staff_manage")
public class RotatingStaffManageEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    private String id;
    /**
     * 监室号
     */
    private String roomId;
    /**
     * 操作人
     */
    private String operatePerson;
    /**
     * 操作时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date operateTime;
    /**
     * 布建理由
     */
    private String constructionReason;
    /**
     * 任务描述
     */
    private String taskDesc;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 创建用户编号
     */
    private String createUserId;
    /**
     * 创建用户
     */
    private String createUserName;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private Date updateTime;
    /**
     * 更新用户编号
     */
    private String updateUserId;
    /**
     * 更新人
     */
    private String updateUserName;
    /**
     * 0-待审核 1-审批通过 2-审批不通过
     */
    private Integer status;
    /**
     * 科组长审核意见
     */
    private Integer groupOpinion;
    /**
     * 审核人
     */
    private String groupPerson;
    /**
     * 审核时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date groupTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标志 1-删除
     */
    private Integer delFlag;
}
