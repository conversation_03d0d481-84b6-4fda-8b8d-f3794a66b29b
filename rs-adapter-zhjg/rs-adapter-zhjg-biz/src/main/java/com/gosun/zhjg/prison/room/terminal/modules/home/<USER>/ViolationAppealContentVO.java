package com.gosun.zhjg.prison.room.terminal.modules.home.vo;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.gosun.zhjg.prison.room.terminal.common.enums.ApprovalStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 违规申诉内容 VO
 */
@Data
@ApiModel(value="ViolationAppealContentVO对象", description="违规申诉内容")
public class ViolationAppealContentVO implements Serializable {
    @ApiModelProperty(value = "违规审核ID")
    private String id;

    @JsonProperty("eventType")
    @ApiModelProperty(value = "事件类型")
    private String eventType;

    @JsonProperty("eventTypeName")
    @ApiModelProperty(value = "事件类型名称")
    private String eventTypeName;

    @JsonProperty("eventDetail")
    @ApiModelProperty(value = "简要情况")
    private String eventDetail;

    @JsonProperty("createTime")
    @ApiModelProperty(value = "违规登记时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    private Date createTime;

    @JsonProperty("createUserName")
    @ApiModelProperty(value = "违规登记人名称")
    private String createUserName;

    @JsonProperty("prisonerNames")
    @ApiModelProperty(value = "违规人员名称")
    private String prisonerNames;

    @JsonProperty("srcType")
    @ApiModelProperty(value = "来源类型（1：违规登记 2：所情管理）")
    private int srcType;

    @JsonProperty("prisonerId")
    @ApiModelProperty(value = "被监管人员ID")
    private String prisonerId;

    @JsonProperty("prisonerName")
    @ApiModelProperty(value = "被监管人员姓名")
    private String prisonerName;

    @JsonProperty("roomId")
    @ApiModelProperty(value = "监室号")
    private String roomId;

    @JsonProperty("roomName")
    @ApiModelProperty(value = "监室名称")
    private String roomName;

    @JsonProperty("reviewTime")
    @ApiModelProperty(value = "审核时间")
    private Date reviewTime;

    @JsonProperty("appealTime")
    @ApiModelProperty(value = "申诉时间")
    private Date appealTime;

    @JsonProperty("reviewUserName")
    @ApiModelProperty(value = "审核人姓名")
    private String reviewUserName;

    @JsonProperty("reviewStatus")
    @ApiModelProperty(value = "审核状态（1:待审核, 2:通过, 3:驳回）")
    private int reviewStatus;

    @ApiModelProperty(value = "审核状态（1:待审核, 2:通过, 3:驳回）")
    private String reviewStatusName;

    // 添加翻译方法
    public void setReviewStatusName(Integer reviewStatus) {
        if (reviewStatus == null) {
            this.reviewStatusName = "未知状态";
        } else if (reviewStatus == ApprovalStatusEnum.NOT_APPROVE.getStatus()) {
            this.reviewStatusName = ApprovalStatusEnum.NOT_APPROVE.getName();
        } else if (reviewStatus == ApprovalStatusEnum.PASS.getStatus()) {
            this.reviewStatusName = ApprovalStatusEnum.PASS.getName();
        } else if (reviewStatus == ApprovalStatusEnum.NOT_PASS.getStatus()) {
            this.reviewStatusName = ApprovalStatusEnum.NOT_PASS.getName();
        } else {
            this.reviewStatusName = "未知状态";
        }
    }
}
