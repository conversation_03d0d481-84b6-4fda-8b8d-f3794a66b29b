package com.gosun.zhjg.prison.room.terminal.modules.home.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* FamilyPhoneNumberVO 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2024-06-06
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="FamilyPhoneNumberVO对象", description="仓内屏-亲情电话表")
public class FamilyPhoneNumberVO implements Serializable {

    private static final long serialVersionUID = -2524149904210611728L;

    @JsonProperty("id")
    @ApiModelProperty(value = "主键")
    private String id;

    @JsonProperty("relName")
    @ApiModelProperty(value = "与通话人员关系")
    private String relName;

    @JsonProperty("relCode")
    @ApiModelProperty(value = "与通话人员关系")
    private String relCode;

    @JsonProperty("prisonerId")
    @ApiModelProperty(value = "在押人员编号")
    private String prisonerId;

    @ApiModelProperty("姓名")
    private String prisonerName;

    @ApiModelProperty("监室名字")
    private String roomName;

    @JsonProperty("telNum")
    @ApiModelProperty(value = "电话号码")
    private String telNum;

    @JsonProperty("applyTime")
    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime;

    @JsonProperty("status")
    @ApiModelProperty(value = "0-未通过 1-通过")
    private Integer status;

    @JsonProperty("String")
    @ApiModelProperty(value = "状态 中文")
    private String statusDisplayName;

    @JsonProperty("applyStatus")
    @ApiModelProperty(value = "1-待审核 2-审核通过 3-审核驳回")
    private Integer applyStatus;

    @JsonProperty("applyStatusDisplayName")
    @ApiModelProperty(value = "审核状态 中文")
    private String applyStatusDisplayName;

    @JsonProperty("approveUser")
    @ApiModelProperty(value = "审核人userId")
    private String approveUser;

    @JsonProperty("approveName")
    @ApiModelProperty(value = "审核人姓名")
    private String approveName;

    @JsonProperty("approveTime")
    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approveTime;

    @JsonProperty("reason")
    @ApiModelProperty(value = "审核理由")
    private String reason;

    @JsonProperty("delFlag")
    @ApiModelProperty(value = "删除标志 0 -删除")
    private Integer delFlag;

    @JsonProperty("prisonId")
    @ApiModelProperty(value = "监所编号")
    private String prisonId;

    /**
     * 通话对象
     */
    @ApiModelProperty(value = "通话对象）")
    private String telObject;

    @ApiModelProperty(value = "电话时间（通话日期）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date telTime;

    @ApiModelProperty(value = "入所时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date enterTime;

    @ApiModelProperty("案件类别")
    private String caseCategory;

    @ApiModelProperty("案件类别-名称")
    private String caseCategoryName;

    @ApiModelProperty("拘留起始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date detainStartTime;

    @ApiModelProperty("拘留截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date detainEndTime;

    @ApiModelProperty("关押期限")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date durationDetention;

    @ApiModelProperty( "档案编号")
    private String archivesId;

    @ApiModelProperty("数据来源1：PC,2：仓内屏")
    private Integer dataSource;

    @ApiModelProperty("数据来源1：PC,2：仓内屏")
    private String dataSourceName;

    @ApiModelProperty("经办人")
    private String handlerName;

    @ApiModelProperty("经办时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date handlerTime;

}
