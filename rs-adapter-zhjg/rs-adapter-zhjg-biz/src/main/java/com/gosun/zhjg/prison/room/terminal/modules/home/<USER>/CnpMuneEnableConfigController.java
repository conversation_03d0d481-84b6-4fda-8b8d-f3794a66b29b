package com.gosun.zhjg.prison.room.terminal.modules.home.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.gosun.zhjg.common.controller.BaseController;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.common.util.FormValidateUtils;
import com.gosun.zhjg.prison.room.terminal.modules.home.dto.CnpMuneEnableConfigSaveDto;
import com.gosun.zhjg.prison.room.terminal.modules.home.service.CnpMuneEnableConfigService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

/**
 * 仓内屏启用配置
 *
 * <AUTHOR>
 *
 */
@Api(tags = "仓内屏启用配置")
@RestController
@RequestMapping("home/cnpmuneenableconfig")
public class CnpMuneEnableConfigController extends BaseController {
	@Autowired
	private CnpMuneEnableConfigService cnpMuneEnableConfigService;
//	@Autowired
//	private CnpMuneEnableConfigDao cnpMuneEnableConfigDao;

	/**
	 * 信息
	 */
	@RequestMapping(value = "/getMenuCodes/{roomId}", method = RequestMethod.GET)
	@ApiOperation(value = "获取启用监室启用菜单编码")
	@ApiImplicitParam(paramType = "path", name = "roomId", value = "监室ID", required = true, dataType = "String")
	public R<?> getMenuCodes(@PathVariable("roomId") String roomId) {
		List<String> menuCodes = cnpMuneEnableConfigService.getMenuCodes(roomId);
		return R.ResponseResult(menuCodes);
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	@ApiOperation(value = "保存更新", responseContainer = "Map")
	public R<?> save(@RequestBody CnpMuneEnableConfigSaveDto[] cnpMuneEnableConfig) {
		FormValidateUtils.validate(cnpMuneEnableConfig);
		String userid = this.getUser().getUserid();
		String username = this.getUser().getName();
		cnpMuneEnableConfigService.saveCnpMuneEnableConfig(cnpMuneEnableConfig, userid, username);
		return R.ResponseOk("保存成功");
	}

	/**
	 * 删除
	 */
	@RequestMapping(value = "/delete", method = RequestMethod.POST)
	@ApiOperation(value = "删除", responseContainer = "Map")
	@ApiImplicitParam(paramType = "body", name = "ids", value = "编号", required = true, allowMultiple = true, dataType = "String")
	public R<?> delete(@RequestBody String[] ids) {
		String userid = this.getUser().getUserid();
		String username = this.getUser().getName();
		cnpMuneEnableConfigService.deleteByIds(ids, userid, username);
		return R.ResponseOk("删除成功");
	}
}
