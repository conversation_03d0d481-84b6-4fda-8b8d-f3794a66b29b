package com.gosun.zhjg.prison.room.terminal.modules.home.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: l<PERSON><PERSON>feng
 * @date: 2023/8/28 9:46
 */
@Data
public class PrisonerReportRepairContentVO {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("状态(0-禁用,1-启用)")
    private Integer status;

    @ApiModelProperty("报修内容")
    private String contentName;

    @ApiModelProperty("报修原因")
    private String reasonName;

}
