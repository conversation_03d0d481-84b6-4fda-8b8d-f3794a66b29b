package com.gosun.zhjg.prison.room.terminal.modules.app.service;

import java.util.Date;
import java.util.UUID;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.gosun.zhjg.prison.room.terminal.modules.app.dao.CnpLogDao;
import com.gosun.zhjg.prison.room.terminal.modules.app.entity.CnpLogEntity;

import lombok.extern.slf4j.Slf4j;

/**
 * 写入日志，如果日志堆积会丢弃
 * 
 * <AUTHOR>
 *
 */
@Slf4j
@Component
public class CnpLogService implements Runnable {

	private static BlockingQueue<CnpLogEntity> logQueue = new LinkedBlockingQueue<CnpLogEntity>(1024);

	@Autowired
	private CnpLogDao cnpLogDao;

	public static boolean ENABLE_WRITE_LOG = true;

	@PostConstruct
	public void init() {
		try {
			new Thread(this).start();
		} catch (Exception e) {
			log.error("", e);
		}
	}

	@Override
	public void run() {
		while (true) {
			try {
				CnpLogEntity entity = logQueue.take();
				if (ENABLE_WRITE_LOG) {
					cnpLogDao.insert(entity);
				}
			} catch (Exception e) {
				log.error("", e);
			}
		}
	}

	/**
	 * 
	 * @param entity
	 * @return
	 */
	public boolean log(CnpLogEntity entity) {
		if (!ENABLE_WRITE_LOG) {
			return false;
		}
		entity.setId(UUID.randomUUID().toString().replaceAll("-", ""));
		if (entity.getLogTime() == null) {
			entity.setLogTime(new Date());
		}
		if (entity.getErrLevel() == null) {
			entity.setErrLevel(entity.getErrLevel().toUpperCase());
		}
		return logQueue.offer(entity);
	}

}
