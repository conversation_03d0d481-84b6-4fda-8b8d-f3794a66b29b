package com.gosun.zhjg.prison.room.terminal.common.constant;

public interface PrisonRoomConstant {
    /**
     * 警员类型
     */
    String police="police";
    /**
     * 在押人员类型
     */
    String prisoner="prisoner";
    /**
     * 停用
     */
    String wait_public="0";
    /**
     * 已执行
     */
    String operate_public="1";
    /**
     * 已撤销
     */
    String operate_cancel="2";
    /**
     * 家属会见
     */
    String JSHJ="1";
    /**
     * 家属会见
     */
    String JSHJ_STR="家属会见";
    /**
     * 律师会见
     */
    String LSHJ="2";
    /**
     * 律师会见
     */
    String LSHJ_STR="律师会见";
    /**
     * 提讯提押
     */
    String TXTY="3";
    /**
     * 提讯提押
     */
    String TXTY_STR="提讯提押";
    /**
     *管教留言
     */
    String GJLY="4";
    /**
     *管教留言
     */
    String GJLY_STR="管教留言";
    /**
     * 未完成
     */
    String UN_FINISH="0";
    /**
     * 已提交
     */
    String SUBMITTED="1";
    /**
     * 已忽略
     */
    String IGNORED="2";
    /**
     * 已完成
     */
    String FINISH="3";
    /**
     * 已下架
     */
    Integer OFF_SALE=0;
    /**
     * 已上架
     */
    Integer ON_SALE=1;
    /**
     * 报告报修类型  0：设备报修；1：异常情况报告
     */
    String SBBX="0";
    /**
     * 设备报修字符串
     */
    String SBBX_STR="设备报修";
    /**
     * 报告报修类型  0：设备报修；1：异常情况报告
     */
    String YCQKBG="1";
    /**
     * 异常情况报告字符串
     */
    String YCQKBG_STR="异常情况报告";

}
