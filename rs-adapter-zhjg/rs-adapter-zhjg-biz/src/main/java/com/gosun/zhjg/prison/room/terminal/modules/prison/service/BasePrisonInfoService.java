package com.gosun.zhjg.prison.room.terminal.modules.prison.service;

import com.gosun.zhjg.basic.business.modules.prison.dto.BasePrisonInfoAddRolesForm;
import com.gosun.zhjg.basic.business.modules.prison.dto.BasePrisonInfoSaveForm;
import com.gosun.zhjg.basic.business.modules.prison.vo.BasePrisonInfoVO;
import com.gosun.zhjg.common.vo.TreeNode;

import java.util.List;
import java.util.Map;

/**
 * 监所基础信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-08-12 19:44:28
 */
public interface BasePrisonInfoService {


	void save(BasePrisonInfoSaveForm form);

	void update(BasePrisonInfoSaveForm form);


	void delete(String [] ids);

	List<Map<String, Object>> prisonTypeList();


	/**
	 * 监所关联角色
	 * @param form
	 */
	void addRoles(BasePrisonInfoAddRolesForm form);

	/**
	 * 监所解除关联角色
	 * @param form
	 */
	void deleteRoles(BasePrisonInfoAddRolesForm form);

	/**
	 * 获取详情
	 * <AUTHOR>
	 * @date 2025/5/22 11:33
	 * @param [id]
	 * @return com.gosun.zhjg.basic.business.modules.prison.vo.BasePrisonInfoVO
	 */
    BasePrisonInfoVO getDetails(String id);

	/**
	 *
	 * @param code
	 * @return
	 */
	List<BasePrisonInfoVO>  branchList(String code);

	/**
	 * 机构树
	 * <AUTHOR>
	 * @date 2025/5/27 15:05
	 * @param [name]
	 * @return java.util.List<com.gosun.zhjg.common.vo.TreeNode>
	 */
	List<TreeNode> cityTree(String name);
}
