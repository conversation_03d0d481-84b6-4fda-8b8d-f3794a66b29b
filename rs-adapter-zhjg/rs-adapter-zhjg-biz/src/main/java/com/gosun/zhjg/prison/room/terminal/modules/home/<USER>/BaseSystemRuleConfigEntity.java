package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 基础系统配置
 *
 * <AUTHOR>
 * @date 2024/12/12 17:43
 */
@Data
@TableName("acp_sys_basic_rule_config")
public class BaseSystemRuleConfigEntity extends BaseDO implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * $column.comments
	 */
	@TableId(type = IdType.ASSIGN_UUID)

	private String id;

	/**
	 * 是否默认项。1是0否
	 */
	private Integer defaultFlag;
	/**
	 * 配置项key。与单位代码组合唯一
	 */
	private String key;
	/**
	 * 配置项值
	 */
	private String value;
	/**
	 * 业务分组。字典：SYSTEM_RULE_CONFIG_TYPE
	 */
	private String type;
	/**
	 * 适用平台。CNP、CWP、PLATFORM
	 */
	private String platform;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 拓展属性1
	 */
	private String ext1;

}
