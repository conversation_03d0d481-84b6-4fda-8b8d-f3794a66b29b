package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName CnpUserDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/7/15 20:29
 * @Version 1.0
 */
@NoArgsConstructor
@Data
public class CnpUserDTO {

    @JsonProperty("data")
    private DataDTO data;
    @JsonProperty("returnCode")
    private Integer returnCode;
    @JsonProperty("returnMsg")
    private String returnMsg;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JsonProperty("curPage")
        private Integer curPage;
        @JsonProperty("isPage")
        private Integer isPage;
        @JsonProperty("pageSize")
        private Integer pageSize;
        @JsonProperty("rows")
        private List<RowsDTO> rows;
        @JsonProperty("total")
        private Integer total;

        @NoArgsConstructor
        @Data
        public static class RowsDTO {
            @JsonProperty("effectiveEndTime")
            private Integer effectiveEndTime;
            @JsonProperty("effectiveStartTime")
            private Integer effectiveStartTime;
            @JsonProperty("faceCode")
            private String faceCode;
            @JsonProperty("faceId")
            private Integer faceId;
            @JsonProperty("faceLibCode")
            private Integer faceLibCode;
            @JsonProperty("groupId")
            private Integer groupId;
            @JsonProperty("imagePath")
            private String imagePath;
            @JsonProperty("registerTime")
            private Long registerTime;
            @JsonProperty("updateTime")
            private Integer updateTime;
            @JsonProperty("userName")
            private String userName;
        }
    }
}
