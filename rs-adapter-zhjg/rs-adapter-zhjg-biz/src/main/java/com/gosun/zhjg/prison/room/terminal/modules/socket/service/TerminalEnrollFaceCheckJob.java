package com.gosun.zhjg.prison.room.terminal.modules.socket.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.common.util.PageUtils;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.BaseDeviceInscreenDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.CnpFaceDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.BaseDeviceInscreenEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.CnpfacePrisonerVO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.impl.CnpServiceImpl;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.PushMessageAckVO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.SocketRelationMapVO;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 仓内屏人脸下发增加任务对未下发过的在押人员进行补录，不同于巡检
 * <p>
 * 1. 只针对在押人员
 * 2. 只处理《人脸库管理/导入台账》状态空的。（即 该人员关联的设备，无下发记录的人员）
 *
 * <AUTHOR>
 * @date 2023/6/25 10:02
 */
@Slf4j
@Component
public class TerminalEnrollFaceCheckJob {
    @Autowired
    private CnpService cnpService;
    @Autowired
    private BaseDeviceInscreenDao baseDeviceInscreenDao;
    @Autowired
    private CnpFaceDao cnpFaceDao;

    @Autowired
    private SocketService socketService;

    public void job() {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("TerminalEnrollFaceCheckJob-job run! uuid={}", uuid);
        List<BaseDeviceInscreenEntity> deviceList = this.selectCnpDeviceList();
        // 离线
        List<String> offlineInfoList = new ArrayList<>();
        // 失败
        Map<String, PushMessageAckVO> errAskMap = new HashMap<>();
        int pageIndex = 0;
        int index = 0;
        int size = deviceList.size();
        List<BaseDeviceInscreenEntity> page;
        do {
            ++pageIndex;
            // 10条 10条进行检查下发
            page = PageUtils.page(deviceList, pageIndex, 10);
            List<String> serialNumberList = page.stream().map(BaseDeviceInscreenEntity::getSerialNumber).collect(Collectors.toList());
            if (serialNumberList == null || serialNumberList.isEmpty()) {
                break;
            }
            SocketRelationMapVO socketInfo = socketService.getSocketInfo();
            // 查询出 《人脸库管理/导入台账》状态空的数据。（即 该人员关联的设备，无下发记录的人员）
            List<CnpfacePrisonerVO> notImportRecords = cnpFaceDao.findCnpPrisonerNotImportRecords(serialNumberList);
            for (String serialNumber : serialNumberList) {
                ++index;
                XxlJobHelper.log("");
                XxlJobHelper.log("当前检查下发进度 {}/{} - {}", index, size, serialNumber);
                log.info("{}-当前检查下发进度 {}/{} - {}", uuid, index, size, serialNumber);
                if (StringUtils.isEmpty(serialNumber)) {
                    continue;
                }
                // 该设备上关联的所有客户端
                List<Map.Entry<String, String>> deviceClient = socketInfo.getSerialNumberMap().entrySet().stream().filter(f -> serialNumber.equals(f.getValue())).collect(Collectors.toList());
                if (deviceClient.isEmpty()) {
                    // 记录离线的设备
                    offlineInfoList.add(serialNumber);
                    XxlJobHelper.log("{}-设备离线", serialNumber);
                    continue;
                }
                // 该设备 待下发人脸集合
                List<CnpfacePrisonerVO> waitEnrollPrisonerList = notImportRecords.stream().filter(f -> serialNumber.equals(f.getSerialNumber())).collect(Collectors.toList());
                if (waitEnrollPrisonerList.isEmpty()) {
                    continue;
                }
                // 遍历该设备每个客户端
                for (Map.Entry<String, String> client : deviceClient) {
                    String clientId = client.getKey();
                    String terminal = socketInfo.getTerminalMap().get(clientId);
                    if (!SocketActionConstants.PushMessageTerminalEnum.CNP.name().equals(terminal)) {
                        // 非仓内屏跳出
                        continue;
                    }
                    for (CnpfacePrisonerVO waitFace : waitEnrollPrisonerList) {
                        // 下发
                        String rybhAppCode = CnpServiceImpl.PRISONER_CODE_PREFIX + waitFace.getPersonnelId();
                        PushMessageAckVO ackVO = cnpService.enrollFaceSync(rybhAppCode, waitFace.getPhoto(), waitFace.getPrisonerPhoto(), clientId, serialNumber);
                        if (!ackVO.getOk()) {
                            errAskMap.put(serialNumber, ackVO);
                        }
                    }
                }
            }
        } while (page != null && page.size() > 0);
        // 打印离线设备
        for (BaseDeviceInscreenEntity entity : deviceList) {
            if (offlineInfoList.contains(entity.getSerialNumber())) {
                XxlJobHelper.log("人脸检查录入任务离线设备-{},{},{}", entity.getSerialNumber(), entity.getDeviceIp(), entity.getDeviceName());
                log.warn("{}-人脸检查录入任务离线设备-{},{},{}", uuid, entity.getSerialNumber(), entity.getDeviceIp(), entity.getDeviceName());
            }
        }
        // 打印失败设备
        for (BaseDeviceInscreenEntity entity : deviceList) {
            PushMessageAckVO ackVO = errAskMap.get(entity.getSerialNumber());
            if (ackVO != null && !ackVO.getOk()) {
                XxlJobHelper.log("人脸检查录入任务失败设备-{},{},{} {}", entity.getSerialNumber(), ackVO.getResponse(255));
                log.warn("{}-人脸检查录入任务失败设备-{},{},{} {}", uuid, entity.getSerialNumber(), entity.getDeviceIp(), entity.getDeviceName(), ackVO.getResponse(255));
            }
        }
        XxlJobHelper.log("人脸检查录入任务完成-{}, 离线设备数量：{}, 失败设备数量：{}, 总设备数量: {}", uuid, offlineInfoList.size(), errAskMap.keySet().size(), deviceList.size());
        log.warn("人脸检查录入任务完成-{}, 离线设备数量：{}, 失败设备数量：{}, 总设备数量: {}", uuid, offlineInfoList.size(), errAskMap.keySet().size(), deviceList.size());
    }

    /**
     * 查询仓内屏设备
     *
     * @return
     */
    public List<BaseDeviceInscreenEntity> selectCnpDeviceList() {
        List<BaseDeviceInscreenEntity> list = baseDeviceInscreenDao.selectList(new LambdaQueryWrapper<BaseDeviceInscreenEntity>()
                .isNotNull(BaseDeviceInscreenEntity::getSerialNumber)
                .and(a -> a.isNull(BaseDeviceInscreenEntity::getDeviceType).or().eq(BaseDeviceInscreenEntity::getDeviceType, 1))
                .select(BaseDeviceInscreenEntity::getSerialNumber, BaseDeviceInscreenEntity::getDeviceIp, BaseDeviceInscreenEntity::getDeviceName));
        return list;
    }
}
