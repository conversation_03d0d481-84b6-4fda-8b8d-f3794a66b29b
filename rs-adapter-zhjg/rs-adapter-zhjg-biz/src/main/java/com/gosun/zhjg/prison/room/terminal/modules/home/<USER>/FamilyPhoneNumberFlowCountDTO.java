package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 流程对象信息
 * <AUTHOR>
 * @date 2025/3/3 9:14
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value = "FamilyPhoneNumberFlowCountDTO对象")
public class FamilyPhoneNumberFlowCountDTO implements Serializable {

    private static final long serialVersionUID = -4816634233366016087L;

    @NotBlank(message = "监所编号 prisonId 不能为空")
    @ApiModelProperty("监所编号")
    private String prisonId;

    @ApiModelProperty("监视号 roomIds 字符串拼接，多个逗号分隔")
    private String roomIds;

    @ApiModelProperty("监视号")
    private String roomName;

}

