package com.gosun.zhjg.prison.room.terminal.modules.home.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("仓外屏配置")
public class CwpConfigVO {
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("出入登记-管教认证 1-开启")
    private Integer policeAuthFlag;

    @ApiModelProperty("出入登记-在押人员认证 1-开启")
    private Integer prisonerAuthFlag;

    @ApiModelProperty("是否有仓外屏 1-是")
    private Integer isCwp;

    @ApiModelProperty("是否有仓内屏 1-是")
    private Integer  isCnp;
}
