package com.gosun.zhjg.prison.room.terminal.common.utils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;

/**
 * 日期工具类
 * <AUTHOR>
 * @date 2025/2/27 13:57
 */
public class DateUtil {

   public static ZoneId zone =  ZoneId.of("Asia/Shanghai");

   /**
    * 本月 开始时间
    * <AUTHOR>
    * @date 2025/2/27 13:56
    * @param [date]
    * @return java.util.Date
    */
    public static Date getMonthStartTime( ) {
        LocalDate today = LocalDate.now();
        // 本月开始时间（精确到 00:00:00）
        LocalDateTime startOfMonth = today.withDayOfMonth(1).atStartOfDay();
        // 本月结束时间（精确到 23:59:59）
        // 定义格式化器（精确到时分秒）
        Date dateStart = Date.from(startOfMonth.atZone(zone).toInstant());
        return dateStart;
    }

    /**
     * 本月 结束时间
     * <AUTHOR>
     * @date 2025/2/27 13:29
     * @param []
     * @return java.util.Date
     */
    public static Date getMonthEndTime() {
        LocalDate today = LocalDate.now();
        // 本月开始时间（精确到 00:00:00）
        LocalDateTime startOfMonth = today.withDayOfMonth(1).atStartOfDay();
        // 本月结束时间（精确到 23:59:59）
        LocalDateTime endOfMonth = today.with(TemporalAdjusters.lastDayOfMonth())
                .atTime(23, 59, 59);
        // 定义格式化器（精确到时分秒）
        Date dateEnd = Date.from(endOfMonth.atZone(zone).toInstant());
        return dateEnd;
    }
}
