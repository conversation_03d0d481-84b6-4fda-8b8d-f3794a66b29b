package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
* letter_send 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2024-10-11
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="LetterSendDTO对象", description="信件管理-寄信申请")
public class LetterSendDTO implements Serializable {

    @JsonProperty("prisonerId")
    @ApiModelProperty(value = "人员编号",required = true)
    @NotBlank(message = "人员编号 不能为空")
    private String prisonerId;

}

