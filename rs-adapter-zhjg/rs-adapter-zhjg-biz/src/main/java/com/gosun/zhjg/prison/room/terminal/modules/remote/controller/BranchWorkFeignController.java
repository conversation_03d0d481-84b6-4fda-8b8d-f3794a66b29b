package com.gosun.zhjg.prison.room.terminal.modules.remote.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.prison.room.terminal.modules.remote.feign.BranchWorkFeign;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

@Api(tags = "zhjg-branch-work")
@RestController
@RequestMapping
public class BranchWorkFeignController {

	@Autowired(required = false)
	private BranchWorkFeign branchWorkFeign;

	/**
	 * 渲染页面前-获取在押人员数据照片、id录入人脸
	 *
	 * {@link com.gosun.zhjg.branch.work.modules.supervise.controller.BranchSuperviseController.getPrisonRoomPolice(String, String, String, String)}
	 *
	 * @return
	 */
	@ApiOperation(value = "根据监室id获取在押人员数据")
	@GetMapping("/business/supervise/getPolicePrisoner")
	public R<?> getPrisonRoomPolice(@RequestParam(value = "roomId", required = false) String roomId, @RequestParam(value = "prisonId", required = false) String prisonId, @RequestParam(value = "prisonerId", required = false) String prisonerId, @RequestParam(value = "prisonerName", required = false) String prisonerName) {
		R<?> r = branchWorkFeign.getPrisonRoomPolice(roomId, prisonId, prisonerId, prisonerName);
		return r;
	}

	/**
	 *
	 * 民警登录-人脸补录-获取列表
	 *
	 * {@link com.gosun.zhjg.branch.work.modules.supervise.controller.BranchSuperviseController.getPrisonerNotFace(String)}
	 *
	 * @return
	 */
	@ApiOperation(value = "根据监室id获取仓内屏未录入人脸的在押人员数据")
	@ApiImplicitParam(paramType = "path", name = "roomId", value = "监室id", dataType = "String")
	@GetMapping("/business/supervise/getPrisonerNotFace/{roomId}")
	public R<?> getPrisonerNotFace(@PathVariable("roomId") String roomId){
		R<?> r = branchWorkFeign.getPrisonerNotFace(roomId);
		return r;
	}
}
