package com.gosun.zhjg.prison.room.terminal.modules.app.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class VideoMeetingCallCnpForm {

    @NotBlank(message = "callRoomId 不能为空")
    @ApiModelProperty("呼叫的监室")
    private String callRoomId;

    @NotBlank(message = "serialNumber 不能为空")
    @ApiModelProperty("当前发出请求设备序列号")
    private String serialNumber;
}
