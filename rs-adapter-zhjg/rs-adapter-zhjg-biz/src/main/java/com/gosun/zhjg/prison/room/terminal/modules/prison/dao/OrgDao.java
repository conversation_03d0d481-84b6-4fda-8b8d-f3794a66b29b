package com.gosun.zhjg.prison.room.terminal.modules.prison.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gosun.zhjg.common.vo.TreeNode;
import com.gosun.zhjg.prison.room.terminal.modules.prison.entity.OrgDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 实战平台-监管管理-机构管理 DO
 *
 * <AUTHOR>
 */
@Mapper
public interface OrgDao extends BaseMapper<OrgDO> {

    /**
     * 城市
     * <AUTHOR>
     * @date 2025/5/27 15:09
     * @param []
     * @return java.util.List<com.gosun.zhjg.common.vo.TreeNode>
     */
    List<TreeNode> cityTree();
}
