package com.gosun.zhjg.prison.room.terminal.modules.app.service.impl;


import com.gosun.zhjg.common.util.UUIDUtils;
import com.gosun.zhjg.prison.app.modules.yjlf.vo.PrisonSendReceiveMessageVO;
import com.gosun.zhjg.prison.room.terminal.config.socketio.EscortMessage;
import com.gosun.zhjg.prison.room.terminal.modules.app.dao.PrisonEscortMessageDao;
import com.gosun.zhjg.prison.room.terminal.modules.app.entity.PrisonSendReceiveMessageEntity;
import com.gosun.zhjg.prison.room.terminal.modules.app.service.PrisonEscortDefenseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
*<AUTHOR>
*@email <EMAIL>
*@date 2020-07-23
*/
@Slf4j
@Service
@RequiredArgsConstructor
public class PrisonEscortDefenseServiceImpl implements PrisonEscortDefenseService {

    private final PrisonEscortMessageDao prisonEscortMessageDao;



    @Override
    public List<PrisonSendReceiveMessageVO> messageList(String id) {
        return prisonEscortMessageDao.messageList(id);
    }

    /**
     * 复制前人代码逻辑，剥离socket
     */
    @Override
    public String escortMessageSave(EscortMessage escortMessage) {
        Integer maxOrder = 0;
        if (maxOrder == null) {
            maxOrder = 0;
        }
        PrisonSendReceiveMessageEntity entity = new PrisonSendReceiveMessageEntity();
        entity.setId(UUIDUtils.generateShortUuid());
        entity.setOrderCode(maxOrder + 1);
        entity.setDefenseId(escortMessage.getDefenseId());
        entity.setDelFlag(0);
        entity.setSendContent(escortMessage.getSendContent());
        entity.setSendName(escortMessage.getSendName());
        entity.setSendTime(new Date());
        entity.setPhoto(escortMessage.getPhoto());
        int insert = prisonEscortMessageDao.insert(entity);
        if (insert > 0) {
            log.info("{} ======================" + "发送消息保存成功", LocalDateTime.now());
        }
        return entity.getId();
    }
}
