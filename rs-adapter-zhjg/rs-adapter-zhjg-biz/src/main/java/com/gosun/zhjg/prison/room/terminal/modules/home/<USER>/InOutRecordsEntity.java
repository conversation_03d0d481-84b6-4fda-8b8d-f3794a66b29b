package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * in_out_records-出入登记记录表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-13 10:00:40
 */
@Data
@TableName("in_out_records")
public class InOutRecordsEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监室号
     */
    private String roomId;
    /**
     * 监室名
     */
    private String roomName;
    /**
     * 发起人id
     */
    private String prisonerId;
    /**
     * 发起人名称
     */
    private String prisonerName;
    /**
     * 发起时间
     */
    private Date registrationTime;
    /**
     * 人脸照片
     */
    private String photo;
}
