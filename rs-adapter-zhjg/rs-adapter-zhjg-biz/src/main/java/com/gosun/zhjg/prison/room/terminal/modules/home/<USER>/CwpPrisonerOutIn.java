package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gosun.zhjg.common.enums.CwpPrisonerOutInReasonCodeEnum;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.*;

import lombok.Data;

/**
 * cwp_prisoner_out_in 实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date: 2022-04-25
 */
@Data
@TableName("cwp_prisoner_out_in")
public class CwpPrisonerOutIn implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @TableId(type = IdType.UUID)
    private String id;
    /**
     * 在押人员编号
     */
    private String prisonerId;
    /**
     * 带出事由
     */
    private String reason;
    /**
     * 在押人员姓名
     */
    private String prisonerName;
    /**
     * 监室号
     */
    private String roomId;
    /**
     * 监室名
     */
    private String roomName;
    /**
     * 带出状态 带出-0 带入-1
     */
    private Integer status;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;
    /**
     * 创建人
     */
    private String createUser;

    /**
     * 人脸信息
     */
    private String photo;

    /***
     *
     */
    private String deviceName;

    /***
     * 1-平台 2-仓外屏
     */
    private Integer dataSource;

    /***
     * 0-未补录 1-已补录 2-无需补录
     */
    private Integer operateStatus;

    /***
     * 人脸信息-被监管人员
     */
    private String prisonerPhoto;

    /***
     * 补录民警编号
     */
    private String additionRecordePoliceId;

    /***
     * 补录民警
     */
    private String additionRecordePolice;

    /***
     * 补录时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date additionRecordeTime;

    /***
     * 登录账号
     */
    private String loginPolice;

    /***
     * 监所编号
     */
    private String prisonId;

    /***
     * 管教民警编号
     */
    private String createUserId;

    /**
     * 事由、业务类型。字典：PRISONER_OUT_IN_REASON
     * {@link CwpPrisonerOutInReasonCodeEnum}
     */
    private String reasonCode;
    /**
     * 具体事由内容；提讯就是取提讯事由；提解就是取提解原因
     */
    private String reasonContent;
    /**
     * 关联业务id
     */
    private String businessId;
    /**
     * 业务时间；提讯日期、提解时间、会见时间；（律师会见取会见开始时间）
     */
    private Date businessTime;
}
