package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 巡诊预约配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-21 15:41:41
 */
@Data
@TableName("prison_inspection_book_config")
public class PrisonInspectionBookConfigEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监室id
     */
    private String roomId;

    /***
     * 监所编号
     */
    private String prisonId;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 1:仓内屏预约巡诊时间段，2仓外屏巡诊时间段
     */
    private Integer type;
}
