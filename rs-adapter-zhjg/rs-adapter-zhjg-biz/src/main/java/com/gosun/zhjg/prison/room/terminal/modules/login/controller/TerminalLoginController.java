package com.gosun.zhjg.prison.room.terminal.modules.login.controller;

import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.prison.room.terminal.modules.login.dto.LoginUserInfoDto;
import com.gosun.zhjg.prison.room.terminal.modules.login.service.TerminalLoginService;
import com.gosun.zhjg.prison.room.terminal.modules.login.vo.LoginInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 仓内屏登录
 *
 * <AUTHOR> on 2020/6/8
 * @email <EMAIL>
 * @date 2020/6/8
 */
@RestController
@RequestMapping("terminal")
@Api(value = "仓内屏登录", tags = "仓内屏登录")
@Slf4j
public class TerminalLoginController {

    @Autowired
    private TerminalLoginService loginService;

    @ApiOperation("登陆")
    @PostMapping(value = "/api/login")
    public R login(@RequestBody LoginUserInfoDto loginUserInfoDto) {
        R r=new R();
        try {
            LoginInfoVO infoVO=loginService.login(loginUserInfoDto);
            if(infoVO!=null) {
                r.setData(infoVO);
                r.setMessage("登录成功");
                r.setStatus(200);
                return r;
            }
            r.setMessage("无法查找到信息");
            r.setStatus(505);
            return r;
        }catch (Exception e){
            e.printStackTrace();
        }
        r.setMessage("出错了，请联系管理员");
        r.setStatus(500);
        return r;
    }
}
