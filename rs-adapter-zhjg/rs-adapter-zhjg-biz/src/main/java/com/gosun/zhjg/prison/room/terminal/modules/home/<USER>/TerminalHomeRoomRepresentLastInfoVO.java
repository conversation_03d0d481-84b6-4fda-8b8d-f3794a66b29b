package com.gosun.zhjg.prison.room.terminal.modules.home.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 监室终端首页-监室监室点名-上次点名情况
 *
 * <AUTHOR>
 * @date 2023/3/29 16:22
 */
@Data
public class TerminalHomeRoomRepresentLastInfoVO {

    @ApiModelProperty(value = "批次号")
    private String presentNo;

    @ApiModelProperty(value = "点名时间")
    private Date startTime;

    @ApiModelProperty(value = "监室数量")
    private Integer roomCount;

    @ApiModelProperty(value = "应点总人数")
    private Integer sumInNum;

    @ApiModelProperty(value = "未点名人数")
    private Integer sumErrorNum;
}
