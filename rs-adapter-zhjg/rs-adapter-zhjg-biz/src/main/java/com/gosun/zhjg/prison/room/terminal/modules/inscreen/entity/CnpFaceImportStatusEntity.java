package com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 仓内外屏人脸照片导入状态
 *
 * <AUTHOR>
 * @date 2023/3/22 13:54
 */
@Data
@TableName("acp_pm_cnp_face_import_status")
public class CnpFaceImportStatusEntity extends BaseDO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    private String serialNumber;
    /**
     * 人员类型 1警员、2在押人员
     */
    private Integer personnelType;
    /**
     * 人员编号、警员id
     */
    private String personnelCode;
    /**
     * 导入状态 1成功、0失败
     */
    private Integer importState;
    /**
     * 失败描述
     */
    private String msg;

}
