package com.gosun.zhjg.prison.room.terminal.common;

import java.lang.reflect.Method;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.concurrent.CopyOnWriteArrayList;

import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.toolkit.SystemClock;
import com.gosun.zhjg.common.context.PageSortMapping;
import com.gosun.zhjg.common.context.PageSortMapping.PageSort;
import com.gosun.zhjg.common.util.PageSortUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 用于解析和方便获取储存映射字典
 *
 * <AUTHOR>
 *
 */
@Slf4j
@Component
public class PageSortContext implements InitializingBean {

	@Autowired
	private SqlSessionFactory sqlSessionFactory;
	@Autowired
	private ApplicationContext applicationContext;
	/**
	 * 映射字典
	 */
	public static PageSortContext MAPPING_CACHE;
	/**
	 * 标记初始化完成
	 */
	public static boolean INITIALIZATION_COMPLETED;

	private Map<String, Map<String, String>> mappingCache = new HashMap<String, Map<String, String>>();
	private List<PageSort> pageSortCache = new CopyOnWriteArrayList<PageSort>();

	@Override
	public void afterPropertiesSet() throws Exception {

		long start = SystemClock.now();
		MAPPING_CACHE = applicationContext.getBean(PageSortContext.class);

		Collection<Class<?>> mappers = sqlSessionFactory.getConfiguration().getMapperRegistry().getMappers();
		for (Class<?> clazz : mappers) {
//			if (clazz != com.gosun.zhjg.prison.work.modules.xz.dao.jsyw.PrisonNoticeDao.class) {
//				continue;
//			}
			Method[] methods = clazz.getDeclaredMethods();
			for (Method m : methods) {
				PageSortMapping ma = m.getAnnotation(PageSortMapping.class);
				Class<?>[] parameterTypes = m.getParameterTypes();
				for (Class<?> argClazz : parameterTypes) {
					if (com.gosun.zhjg.common.entity.AbstractPageQueryForm.class.isAssignableFrom(argClazz)) {
						// 优先从VO中拿一次，再从DTO中拿
						Class<?> voClass = PageSortUtil.getMethodGenricReturnType(m);
						if (Object.class != voClass) {
							PageSort mapping = PageSortUtil.reflectMapping(voClass);
							mapping.setClassName(clazz.getName());
							mapping.setMethodName(m.getName());
							this.put(mapping, false);
						}
						PageSort mapping = PageSortUtil.reflectMapping(argClazz);
						mapping.setClassName(clazz.getName());
						mapping.setMethodName(m.getName());
						this.put(mapping, false);
						if (ma != null && ma.ignore()) {
							mapping.setIgnore(true); // 标记出忽略方法 @PageSortMapping(ignore = true)
						}
						break;
					}
				}
			}
		}
		INITIALIZATION_COMPLETED = true;
		long timing = SystemClock.now() - start;
		log.info("解析排序映射花费 {}s", timing / 1000.0);
	}

	/**
	 * 获取页面传递的排序属性与xml映射的字段
	 *
	 * @param methodId 比如 com.gosun.zhjg.prison.work.modules.gj.dao.jsyw.PrisonRoomChangeDao.findByPage
	 * @param field
	 * @return
	 */
	public Map<String, String> getMapping(String methodId) {
		return mappingCache.get(methodId);
	}

	public String getMapping(String methodId, String field) {
		Map<String, String> map = mappingCache.get(methodId);
		return map == null ? null : map.get(field);
	}

	public PageSort getPageSort(String methodId) {
		Optional<PageSort> optional = pageSortCache.stream().filter(f -> methodId.equals(f.getMethodId())).findFirst();
		if (optional.isPresent()) {
			return optional.get();
		}
		return null;
	}

	/**
	 * 强制替换map
	 *
	 * @param mapping
	 * @return
	 */
	public PageSortContext putForce(PageSort mapping) {
		if (mapping != null) {
			String methodId = mapping.getClassName() + "." + mapping.getMethodName();
			mappingCache.put(methodId, mapping.getCache());
			Iterator<PageSort> iterator = pageSortCache.iterator();
			while (iterator.hasNext()) {
				PageSort next = iterator.next();
				if (methodId.equals(next.getMethodId())) {
					pageSortCache.remove(next);
					break;
				}
			}
			pageSortCache.add(mapping);
		}
		return this;
	}

	/**
	 *
	 * @param mapping
	 * @param replace 是否会覆盖旧的map Value
	 * @return
	 */
	public PageSortContext put(PageSort mapping, boolean replace) {
		if (mapping != null) {
			String methodId = mapping.getClassName() + "." + mapping.getMethodName();
			Optional<PageSort> optional = pageSortCache.stream().filter(f -> methodId.equals(f.getMethodId())).findFirst();
			if (optional.isPresent()) {
				PageSort pageSort = optional.get();
				Map<String, String> cache = pageSort.getCache();
				if (cache == null) {
					cache = new HashMap<>();
					pageSort.setCache(cache);
				}
				Map<String, String> map = mapping.getCache();
				if (map != null) {
					Iterator<Entry<String, String>> iterator = map.entrySet().iterator();
					while (iterator.hasNext()) {
						Entry<String, String> entry = iterator.next();
						if (replace) {
							cache.put(entry.getKey(), entry.getValue());
						} else if (cache.containsKey(entry.getKey())) {
							cache.put(entry.getKey(), entry.getValue());
						}
					}
				}
				if (mapping.getIgnore() != null) {
					pageSort.setIgnore(mapping.getIgnore());
				}
				mappingCache.put(methodId, cache);
			} else {
				mappingCache.put(methodId, mapping.getCache());
				pageSortCache.add(mapping);
			}
		}
		return this;
	}

	/**
	 * 缓存字典
	 *
	 * @param mapping
	 * @return
	 */
	public PageSortContext put(PageSort... mapping) {
		for (PageSort p : mapping) {
			put(p, true);
		}
		return this;
	}
}
