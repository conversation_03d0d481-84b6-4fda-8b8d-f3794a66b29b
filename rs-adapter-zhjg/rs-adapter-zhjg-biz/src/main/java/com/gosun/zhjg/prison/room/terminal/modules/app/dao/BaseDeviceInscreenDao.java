package com.gosun.zhjg.prison.room.terminal.modules.app.dao;//package com.gosun.zhjg.prison.room.terminal.modules.app.dao;
//
//import org.apache.ibatis.annotations.Mapper;
//import org.apache.ibatis.annotations.Param;
//import org.apache.ibatis.annotations.Select;
//
//import com.gosun.zhjg.prison.room.terminal.modules.app.vo.BaseDeviceInscreenVO;
//
//@Mapper
//public interface BaseDeviceInscreenDao {
//
//	@Select("select a.id, a.serial_number, a.device_id, a.device_ip, a.room_id, a.device_num, a.host_num, a.address_ip, b.room_name from acp_pm_device_inscreen a left join acp_pm_area_prison_room b on a.room_id = b.id where a.serial_number = #{serialNum} limit 1")
//	BaseDeviceInscreenVO getBySerialNum(@Param("serialNum") String serialNum);
//}
