package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * prisoner_suggestion_notice-投诉建议通知表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-15 14:13:15
 */
@Data
@TableName("prisoner_suggestion_notice")
public class PrisonerSuggestionNoticeEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @TableId(type = IdType.ASSIGN_UUID)

    private String id;
    /**
     * 监室号
     */
    private String roomId;
    /**
     * 监室名
     */
    private String roomName;
    /**
     * 发起人id
     */
    private String createUserId;
    /**
     * 发起人名字
     */
    private String createUserName;
    /**
     * 发起时间
     */
    private Date createTime;
    /**
     * 内容
     */
    private String content;
}
