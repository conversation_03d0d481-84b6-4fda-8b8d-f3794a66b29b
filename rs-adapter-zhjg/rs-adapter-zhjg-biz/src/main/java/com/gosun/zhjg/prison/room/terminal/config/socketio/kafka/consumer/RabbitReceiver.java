package com.gosun.zhjg.prison.room.terminal.config.socketio.kafka.consumer;

import com.alibaba.fastjson.JSON;
import com.gosun.zhjg.common.constant.KafkaTopicsConstants;
import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.common.util.UUIDUtils;
import com.gosun.zhjg.prison.room.terminal.common.constant.SocketEventConstant;
import com.gosun.zhjg.prison.room.terminal.config.socketio.EscortMessage;
import com.gosun.zhjg.prison.room.terminal.config.socketio.PushMessage;
import com.gosun.zhjg.prison.room.terminal.modules.app.dao.PrisonEscortMessageDao;
import com.gosun.zhjg.prison.room.terminal.modules.app.entity.PrisonSendReceiveMessageEntity;
import com.gosun.zhjg.prison.room.terminal.modules.home.entity.RoomRepresentEntity;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.CustomPushMessageConditionDTO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.CnpService;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.SocketService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.gosun.zhjg.prison.room.terminal.common.constant.SocketEventConstant.ESCORT_MESSAGE_EVENT_SEND;

@Component
@Slf4j
public class RabbitReceiver {


    @Autowired
    private PrisonEscortMessageDao prisonEscortMessageDao;

    @Autowired
    private CnpService cnpService;

    @Autowired
    private SocketService socketService;


//    @RabbitListener(queues = "notice_update")
//    public void updateWarderRoom(String record) {
//        Optional<?> kafkaMessage = Optional.ofNullable(record);
//        if (kafkaMessage.isPresent()) {
//            Object message = kafkaMessage.get();
//            PushMessage message1 = new PushMessage();
//            message1.setContent(message.toString());
//            TerminalNotificationEntity entity = JSON.parseObject(message.toString(), TerminalNotificationEntity.class);
//            List<String> roomIds = new ArrayList<>();
//            roomIds.add(entity.getRoomId());
//            try {
//                socketIOService.pushMessageToRoom(message1, "notice_update", roomIds);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//    }


//    @RabbitListener(queues = "end_represent")
//    public void endRepresent(String record) {
//        Optional<?> kafkaMessage = Optional.ofNullable(record);
//        if (kafkaMessage.isPresent()) {
//            Object message = kafkaMessage.get();
//            PushMessage message1 = new PushMessage();
//            message1.setContent(message.toString());
//            List<String> roomIds = Arrays.asList(message.toString().split(","));
//            try {
//                socketIOService.pushMessageToRoom(message1, "end_represent", roomIds);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//    }

    @RabbitListener(queues = "represent_start_auto")
    public void representStartAuto(String record) {
        Optional<?> kafkaMessage = Optional.ofNullable(record);
        if (kafkaMessage.isPresent()) {
            Object message = kafkaMessage.get();
            PushMessage message1 = new PushMessage();
            message1.setContent(message.toString());
            RoomRepresentEntity room = JSON.parseObject(message.toString(), RoomRepresentEntity.class);
            List<String> roomIds = new ArrayList<>();
            roomIds.add(room.getRoomId());
            CustomPushMessageConditionDTO condition = new CustomPushMessageConditionDTO();
            condition.setRoomIds(roomIds);
            condition.setEventName(SocketEventConstant.REPRESENT_START);
            condition.setMessage(message1);
            socketService.pushMessageCustomWithCondition(condition);
        }
    }

//    @RabbitListener(queues = "insert_represent_config")
//    public void autoCreateRepresent(String record) {
//        Optional<?> kafkaMessage = Optional.ofNullable(record);
//        if (kafkaMessage.isPresent()) {
//            Object message = kafkaMessage.get();
//            try {
//                RoomRepresentConfigEntity entity = JSON.parseObject(message.toString(), RoomRepresentConfigEntity.class);
//                scheduledService.doStartTask(entity);
//            } catch (Exception e) {
//                log.error("KAFKA消费异常", e);
//            }
//        }
//    }


    @RabbitListener(queues = "escort_event_handle_notice")
    public void escortEventHandleNotice(String record) {
        Optional<?> kafkaMessage = Optional.ofNullable(record);
        if (kafkaMessage.isPresent()) {
            Object message = kafkaMessage.get();
            EscortMessage escortMessage = JSON.parseObject(message.toString(), EscortMessage.class);
            PrisonSendReceiveMessageEntity entity = new PrisonSendReceiveMessageEntity();
            entity.setId(UUIDUtils.generateShortUuid());
            Integer maxOrder = 0;
            if (maxOrder == null) {
                maxOrder = 0;
            }
            entity.setOrderCode(maxOrder + 1);
            entity.setDefenseId(escortMessage.getDefenseId());
            entity.setDelFlag(0);
            entity.setSendContent(escortMessage.getSendContent());
            entity.setSendName(escortMessage.getSendName());
            entity.setSendTime(new Date());
            // 推送消息
            CustomPushMessageConditionDTO condition = new CustomPushMessageConditionDTO();
            condition.setEventName(ESCORT_MESSAGE_EVENT_SEND);
            condition.setMessage(escortMessage);
            condition.setEscortIds(Collections.singletonList(escortMessage.getDefenseId()));
            try {
                socketService.pushMessageCustomWithCondition(condition);
                int insert = prisonEscortMessageDao.insert(entity);
                if (insert < 0) {
                    throw new Exception("数据插入异常");
                }
            } catch (Exception e) {
                log.error("KAFKA消费异常", e);
            }
        }
    }

//    @RabbitListener(queues = "work_arrange")
//    public void workArrange(String record) {
//        Optional<?> kafkaMessage = Optional.ofNullable(record);
//        if (kafkaMessage.isPresent()) {
//            Object message = kafkaMessage.get();
//            PushMessage message1 = new PushMessage();
//            message1.setContent(message.toString());
//            String roomId = message.toString();

    /// /            try {
    /// /                scheduledService.doStartTask1(roomId);
    /// /            } catch (Exception e) {
    /// /                e.printStackTrace();
    /// /            }
//            List<String> roomIds = new ArrayList<>();
//            roomIds.add(roomId);
//            try {
//                socketIOService.pushMessageToRoom(message1, "work_arrange_notice", roomIds);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//    }

//    @RabbitListener(queues = "book_config")
//    public void bookConfig(String record) {
//        Optional<String> kafkaMessage = Optional.ofNullable(record);
//        if (kafkaMessage.isPresent()) {
//            try {
//                String message = kafkaMessage.get();
//                PushMessageForm form = JSON.parseObject(message, PushMessageForm.class);
//                if (form == null || StringUtils.isBlank(form.getTerminal()) || form.getSerialNumbers() == null || form.getSerialNumbers().size() == 0) {
//                    log.error("参数异常");
//                } else {
//                    socketService.pushMessageToSerialNumber(form);
//                }
//            } catch (Exception e) {
//                log.error("", e);
//            }
//        }
//    }


    @RabbitListener(queues = "business_notice_del")
    public void businessNoticeDel(String record) {
        Optional<?> kafkaMessage = Optional.ofNullable(record);
        if (kafkaMessage.isPresent()) {
            Object message = kafkaMessage.get();
            String policeId = message.toString();
            PushMessage message1 = new PushMessage();
            message1.setContent(policeId);
            // 推送消息
            CustomPushMessageConditionDTO condition = new CustomPushMessageConditionDTO();
            condition.setEventName(SocketEventConstant.BUSINESS_NOTICE);
            condition.setMessage(message1);
            condition.setPrisonIds(Collections.singletonList(String.valueOf(policeId)));
            socketService.pushMessageCustomWithCondition(condition);
        }
    }

    @RabbitListener(queues = "prison_event_change")
    public void prisonEventSave(String record) {
        Optional<?> kafkaMessage = Optional.ofNullable(record);
        if (kafkaMessage.isPresent()) {
            Object message = kafkaMessage.get();
            String eventId = message.toString();
            PushMessage message1 = new PushMessage();
            message1.setContent(eventId);
            CustomPushMessageConditionDTO condition = new CustomPushMessageConditionDTO();
            condition.setEventName(SocketEventConstant.PRISON_EVENT_CHANGE);
            condition.setMessage(message1);
            socketService.pushMessageCustomWithCondition(condition);
        }
    }

    @RabbitListener(queues = "prison_warning_change")
    public void prisonWarningSave(String record) {
        Optional<?> kafkaMessage = Optional.ofNullable(record);
        if (kafkaMessage.isPresent()) {
            Object message = kafkaMessage.get();
            String warningId = message.toString();
            PushMessage message1 = new PushMessage();
            message1.setContent(warningId);
            CustomPushMessageConditionDTO condition = new CustomPushMessageConditionDTO();
            condition.setEventName(SocketEventConstant.PRISON_WARNING_CHANGE);
            condition.setMessage(message1);
            socketService.pushMessageCustomWithCondition(condition);
        }
    }

    @RabbitListener(queues = "bigScreen_notice")
    public void bigScreenCall(String record) {
        Optional<?> kafkaMessage = Optional.ofNullable(record);
        if (kafkaMessage.isPresent()) {
            Object message = kafkaMessage.get();
            String msg = message.toString();
            PushMessage message1 = new PushMessage();
            message1.setContent(msg);
            CustomPushMessageConditionDTO condition = new CustomPushMessageConditionDTO();
            condition.setEventName(SocketEventConstant.BIG_SCREEN_NOTICE);
            condition.setMessage(message1);
            socketService.pushMessageCustomWithCondition(condition);
        }
    }

/*
    @RabbitListener(queues = "photo_add")
    public void roomPhotoAdd(String record) {
        log.info("---------------------------->receive photo_add message");
        Optional<?> kafkaMessage = Optional.ofNullable(record);
        if (kafkaMessage.isPresent()) {
            Object message = kafkaMessage.get();
            List<Map<String, Object>> mapList = (List<Map<String, Object>>) JSON.parseObject(message.toString(), List.class);
            //设置监室id
            List<String> roomIds = new ArrayList<>();
            if (mapList.size() > 0) {
                Map<String, Object> map1 = mapList.get(0);
                String roomId = map1.get("roomId") == null ? null : map1.get("roomId").toString();
                roomIds.add(roomId);
            }
            //结构体去除监室号
            mapList.remove(0);
            PushMessage message1 = new PushMessage();
            message1.setContent(JSON.toJSONString(mapList));
            log.info("---------------------------->receive photo_add message =" + message1);
            try {
                socketIOService.pushMessageToRoom(message1, "photo_add", roomIds);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @RabbitListener(queues = "photo_delete")
    public void photoDelete(String record) {
        log.info("---------------------------->receive photo_delete message");
        Optional<?> kafkaMessage = Optional.ofNullable(record);
        if (kafkaMessage.isPresent()) {
            Object message = kafkaMessage.get();
            List<Map<String, Object>> mapList = (List<Map<String, Object>>) JSON.parseObject(message.toString(), List.class);
            //设置监室id
            List<String> roomIds = new ArrayList<>();
            if (mapList.size() > 0) {
                Map<String, Object> map1 = mapList.get(0);
                String roomId = map1.get("roomId") == null ? null : map1.get("roomId").toString();
                roomIds.add(roomId);
            }
            //结构体去除监室号
            mapList.remove(0);
            PushMessage message1 = new PushMessage();
            message1.setContent(JSON.toJSONString(mapList));
            log.info("---------------------------->receive photo_delete message =" + message1);
            try {
                socketIOService.pushMessageToRoom(message1, "photo_delete", roomIds);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
*/


    /**
     * 监室调整通知终端
     *
     * @param record
     */
    @RabbitListener(queues = KafkaTopicsConstants.Kafkatopic_RoomChange2)
    public void roomChange2(String record) {
        log.info("---------------------------->receive {} message", KafkaTopicsConstants.Kafkatopic_RoomChange2);
        Optional<?> kafkaMessage = Optional.ofNullable(record);
        if (kafkaMessage.isPresent()) {
            Object message = kafkaMessage.get();
            try {
                log.info(message + "");
                List<String> array = JSON.parseArray(message.toString(), String.class);
                cnpService.roomChange(array);
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }

    /**
     * 人员信息变动通知终端
     *
     * @param record
     */
    @RabbitListener(queues = KafkaTopicsConstants.Kafkatopic_PrisonerChange)
    public void prisonerChange(String record) {
        log.info("---------------------------->receive {} message", KafkaTopicsConstants.Kafkatopic_PrisonerChange);
        Optional<?> kafkaMessage = Optional.ofNullable(record);
        if (kafkaMessage.isPresent()) {
            Object message = kafkaMessage.get();
            try {
                log.info(message + "");
                cnpService.prisonerChange(message.toString());
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }

    /**
     * 照片变动通知终端
     *
     * @param record
     */
    @RabbitListener(queues = KafkaTopicsConstants.Kafkatopic_ZPChange)
    public void zpChange(String record) {
        log.info("---------------------------->receive {} message", KafkaTopicsConstants.Kafkatopic_ZPChange);
        Optional<?> kafkaMessage = Optional.ofNullable(record);
        if (kafkaMessage.isPresent()) {
            Object message = kafkaMessage.get();
            try {
                log.info(message + "");
                cnpService.zpChange(message.toString());
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }

    /**
     * 监室终端-推送消息给序列号
     *
     * @param record
     */
    @RabbitListener(queues = KafkaTopicsConstants.Kafkatopic_PushMessageToSerialNumber)
    public void pushMessageToSerialNumber(String record) {
        log.info("---------------------------->receive {} message", KafkaTopicsConstants.Kafkatopic_PushMessageToSerialNumber);
        Optional<String> kafkaMessage = Optional.ofNullable(record);
        if (kafkaMessage.isPresent()) {
            try {
                String message = kafkaMessage.get();
                log.info(message);
                PushMessageForm form = JSON.parseObject(message, PushMessageForm.class);
                if (form == null || StringUtils.isBlank(form.getTerminal()) || form.getSerialNumbers() == null || form.getSerialNumbers().size() == 0) {
                    log.error("参数异常");
                } else {
                    socketService.pushMessageToSerialNumber(form);
                }
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }

    /**
     * 监室终端-推送消息给监室
     *
     * @param record
     */
    @RabbitListener(queues = KafkaTopicsConstants.Kafkatopic_PushMessageToRoom)
    public void pushMessageToRoom(String record) {
        log.info("---------------------------->receive {} message", KafkaTopicsConstants.Kafkatopic_PushMessageToRoom);
        Optional<String> kafkaMessage = Optional.ofNullable(record);
        if (kafkaMessage.isPresent()) {
            try {
                String message = kafkaMessage.get();
                log.info(message);
                PushMessageForm form = JSON.parseObject(message, PushMessageForm.class);
                if (form == null || StringUtils.isBlank(form.getTerminal()) || form.getRoomIds() == null || form.getRoomIds().size() == 0) {
                    log.error("参数异常");
                }
                socketService.pushMessageToRoom(form);
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }

    /**
     * 平台-推送消息给监所下所有用户
     *
     * @param record
     */
    @RabbitListener(queues = KafkaTopicsConstants.Kafkatopic_PushMessageToPrison)
    public void pushMessageToPrison(String record) {
        log.info("---------------------------->receive {} message", KafkaTopicsConstants.Kafkatopic_PushMessageToPrison);
        Optional<String> kafkaMessage = Optional.ofNullable(record);
        if (kafkaMessage.isPresent()) {
            try {
                String message = kafkaMessage.get();
                log.info(message);
                PushMessageForm form = JSON.parseObject(message, PushMessageForm.class);
                if (form == null || StringUtils.isBlank(form.getAction()) || form.getPrisonIds() == null || form.getPrisonIds().size() == 0) {
                    log.error("参数异常");
                }
                form.setTerminal(SocketActionConstants.PushMessageTerminalEnum.PLATFORM.name());
                socketService.pushMessageToPrison(form);
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }

    /**
     * 平台-推送消息给用户
     *
     * @param record
     */
    @RabbitListener(queues = KafkaTopicsConstants.Kafkatopic_PushMessageToUser)
    public void pushMessageToUser(String record) {
        log.info("---------------------------->receive {} message", KafkaTopicsConstants.Kafkatopic_PushMessageToUser);
        Optional<String> kafkaMessage = Optional.ofNullable(record);
        if (kafkaMessage.isPresent()) {
            try {
                String message = kafkaMessage.get();
                log.info(message);
                PushMessageForm form = JSON.parseObject(message, PushMessageForm.class);
                if (form == null || StringUtils.isBlank(form.getAction()) || form.getUserIds() == null || form.getUserIds().size() == 0) {
                    log.error("参数异常");
                }
                form.setTerminal(SocketActionConstants.PushMessageTerminalEnum.PLATFORM.name());
                socketService.pushMessageToUser(form);
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }

    /**
     * 平台推送到对外服务终端设备
     *
     * @param record
     */

    @RabbitListener(queues = KafkaTopicsConstants.Kafkatopic_PushMessageToExternal)
    public void pushMessageToExternal(String record) {
        log.info("---------------------------->receive {} message", KafkaTopicsConstants.Kafkatopic_PushMessageToExternal);
        Optional<String> kafkaMessage = Optional.ofNullable(record);
        if (kafkaMessage.isPresent()) {
            try {
                String message = kafkaMessage.get();
                log.info(message);
                PushMessageForm form = JSON.parseObject(message, PushMessageForm.class);
                if (form == null || StringUtils.isBlank(form.getAction()) || form.getMacAddress() == null || form.getMacAddress().size() == 0) {
                    log.error("参数异常");
                }
                form.setTerminal(SocketActionConstants.PushMessageTerminalEnum.EXTERNAL.name());
                socketService.pushMessageToExternal(form);
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }
}

