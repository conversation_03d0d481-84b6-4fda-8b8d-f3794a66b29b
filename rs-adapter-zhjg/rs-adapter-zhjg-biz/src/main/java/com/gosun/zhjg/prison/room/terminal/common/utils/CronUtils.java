package com.gosun.zhjg.prison.room.terminal.common.utils;

import java.text.SimpleDateFormat;
import java.util.Calendar;

/**
 * cron表达式转换类
 *
 * <AUTHOR> on 2020/5/18
 * @email <EMAIL>
 * @date 2020/5/18
 */
public class CronUtils {

    //"ss mm HH dd MM ? yyyy"
    private static final SimpleDateFormat sdf = new SimpleDateFormat("mm HH");

    /***
     * convert Date to cron, eg "0 07 10 15 1 ?"
     * @return
     */
    public static String getCron(String hour,String minute) {
        return "0 "+minute+" "+hour+" * * ?";
    }

    /***
     * 按周执行
     * convert Date to cron, eg "0 15 10 ? * 1,2?"
     * @return
     */
    public static String getCronWeek(String hour,String minute,String week) {
        return "0 "+minute+" "+hour+" ? "+" * "+week;
    }

    /***
     * 执行一次
     * convert Date to cron, eg "0 15 10 ? * 1,2?"
     * @return
     */
    public static String getCronOne(String hour,String minute) {
        Calendar now = Calendar.getInstance();
        int year = now.get(Calendar.YEAR);
        int month = now.get(Calendar.MONTH) + 1;
        int day = now.get(Calendar.DAY_OF_MONTH);
        return "0 "+minute+" "+hour+" "+day+" "+month+" ?";
    }

}
