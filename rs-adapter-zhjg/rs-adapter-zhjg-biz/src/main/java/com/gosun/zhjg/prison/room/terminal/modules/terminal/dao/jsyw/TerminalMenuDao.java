package com.gosun.zhjg.prison.room.terminal.modules.terminal.dao.jsyw;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gosun.zhjg.basic.business.modules.room.vo.AreaPrisonRoomPageVO;
import com.gosun.zhjg.common.constant.TerminalMenuCodeConstants;
import com.gosun.zhjg.common.enums.PlatformEnum;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dto.TerminalMenuPageDto;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.entity.TerminalMenuEntity;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalMenuVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 终端-仓内外屏菜单表
 *
 * <AUTHOR>
 * @date 2024/12/12 10:58
 */
@Mapper
public interface TerminalMenuDao extends BaseMapper<TerminalMenuEntity> {

	List<TerminalMenuVO> findByPage(@Param("form") TerminalMenuPageDto form, Page page);

	TerminalMenuVO findOneById(@Param("id") String id);

	/**
	 * 查询出所有菜单，标记出监所是否有权限
	 *
	 * @param form
	 * @return
	 */
	List<TerminalMenuVO> selectAllMenusAndMarkUnitOwned(@Param("form") TerminalMenuPageDto form);

	/**
	 * 查询监所拥有的菜单列表
	 *
	 * @param form
	 * @return
	 */
	List<TerminalMenuVO> selectPrisonMenuList(@Param("form") TerminalMenuPageDto form);

	/**
	 * 查询用户拥有的菜单ID列表
	 *
	 * @param form
	 * @return
	 */
	List<String> selectPrisonMenuIdList(@Param("form") TerminalMenuPageDto form);

	List<AreaPrisonRoomPageVO> selectRoomList(@Param("roomIdList") List<String> roomIdList);

	/**
	 * 查询拥有某菜单的监所ID列表
	 *
	 * @param menuCode
	 * @return
	 */
	List<String> selectHasMenuPrisonIdList(@Param("menuCode") String menuCode);

	/**
	 * 查询菜单权限是否拥有
	 *
	 * @param prisonId
	 * @param menuCode
	 * @return
	 */
	Integer checkPrisonHasMenuPermission(@Param("prisonId") String prisonId, @Param("menuCode") String menuCode);

	/**
	 * 查询监所拥有的菜单权限
	 *
	 * @param prisonId
	 * @param terminalType {@link PlatformEnum}
	 * @param menuCodeList {@link TerminalMenuCodeConstants}
	 * @return
	 */
	List<String> selectPrisonPermissionMenu(@Param("prisonId") String prisonId, @Param("terminalType") String terminalType, @Param("menuCodeList") List<String> menuCodeList);

}
