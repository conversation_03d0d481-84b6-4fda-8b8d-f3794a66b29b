package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 报告报修表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-20 17:01:33
 */
@Data
@TableName("prisoner_report_repair")
public class PrisonerReportRepairEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @TableId(type = IdType.ASSIGN_UUID)

    private String id;
    /**
     * 申请人id
     */
    private String applicantId;
    /**
     * 申请人姓名
     */
    private String applicantName;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 监室名
     */
    private String roomName;
    /**
     * 报修物品
     */
    private String items;
    /**
     * 描述
     */
    private String description;
    /**
     * 申请时间
     */
    private Date applyTime;
    /**
     * 状态（0：未完成，1：已提交，2：已忽略）
     */
    private String status;
    /**
     * 提交时间
     */
    private Date submitTime;
    /**
     * 类型（0：设备报修；1：异常情况报告）
     */
    private String type;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 报告保修号
     */
    private String repairParentNo;

    /**
     * 报修内容id
     */
    private String contentId;

    /**
     * 报修原因id集合(逗号分割)
     */
    private String reasonIds;
}
