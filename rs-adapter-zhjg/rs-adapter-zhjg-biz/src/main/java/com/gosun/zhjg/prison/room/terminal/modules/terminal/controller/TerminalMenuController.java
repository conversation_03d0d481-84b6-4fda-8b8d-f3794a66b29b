package com.gosun.zhjg.prison.room.terminal.modules.terminal.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gosun.zhjg.common.config.UserContext;
import com.gosun.zhjg.common.enums.PlatformEnum;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dao.jsyw.TerminalMenuDao;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dto.TerminalMenuPageDto;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.service.TerminalMenuService;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalMenuVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 终端-仓内外屏菜单表
 *
 * <AUTHOR>
 * @date 2024/12/12 11:07
 */
@Api(tags = "终端-仓内外屏菜单表")
@RestController
@RequestMapping("terminal/terminalmenu")
public class TerminalMenuController {

	@Autowired
	private TerminalMenuService terminalMenuService;
	@Autowired
	private TerminalMenuDao terminalMenuDao;

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	@ApiOperation(value = "终端-仓内外屏菜单表-分页列表", responseContainer = "List", response = TerminalMenuVO.class)
	public R<?> list(TerminalMenuPageDto form) {
		Page page = null;
		if (form.getPaging() == null || form.getPaging()) {
			page = form.trainToPage();
		}
		List<TerminalMenuVO> records = terminalMenuService.findByPage(form, page);
		if (page == null) {
			page = new Page(1, records.size(), records.size());
		}
		page.setRecords(records);
		return R.ResponsePage(page);
	}

	/**
	 * 信息
	 */
	@RequestMapping(value = "/info/{id}", method = RequestMethod.GET)
	@ApiOperation(value = "终端-仓内外屏菜单表-根据编号查找记录", responseContainer = "Map", response = TerminalMenuVO.class)
	@ApiImplicitParam(paramType = "path", name = "id", value = "编号", required = true, dataType = "String")
	public R<?> info(@PathVariable("id") String id) {
		TerminalMenuVO vo = terminalMenuService.findOneById(id);
		return R.ResponseResult(vo);
	}

	/**
	 * 终端-仓内外屏菜单树
	 */
	@RequestMapping(value = "/menuTree", method = RequestMethod.GET)
	@ApiOperation(value = "终端-仓内外屏菜单树", responseContainer = "List", response = TerminalMenuVO.class)
	public R<TerminalMenuVO> menuTree(@ApiParam(hidden = true) TerminalMenuPageDto form) {
		form.setPrisonId(UserContext.getJwtInfo().getPrisonId());
		List<TerminalMenuVO> reocrds = terminalMenuService.buildTerminalMenuTree(form);
		return R.ResponseResult(reocrds);
	}


	@RequestMapping(value = "/cnp/list", method = RequestMethod.GET)
	@ApiOperation(value = "终端-仓内屏菜单列表", response = TerminalMenuVO.class)
	public R<?> cnpList(TerminalMenuPageDto form) {
		form.setTerminalType(PlatformEnum.CNP.getValue());
		List<TerminalMenuVO> records = terminalMenuDao.selectPrisonMenuList(form);
		return R.ResponseResult(records);
	}

	@RequestMapping(value = "/cwp/list", method = RequestMethod.GET)
	@ApiOperation(value = "终端-仓外屏菜单列表", response = TerminalMenuVO.class)
	public R<?> cwpList(TerminalMenuPageDto form) {
		form.setTerminalType(PlatformEnum.CWP.getValue());
		List<TerminalMenuVO> records = terminalMenuDao.selectPrisonMenuList(form);
		return R.ResponseResult(records);
	}
}
