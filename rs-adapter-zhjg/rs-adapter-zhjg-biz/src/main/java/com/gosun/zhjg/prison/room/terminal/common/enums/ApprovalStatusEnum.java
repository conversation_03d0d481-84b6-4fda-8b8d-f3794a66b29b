package com.gosun.zhjg.prison.room.terminal.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * 数据库审批状态枚举
 *
 * <AUTHOR>
 * @date 2024/9/18
 */
@Getter
public enum ApprovalStatusEnum {

    /**
     * 1：待审批
     */
    NOT_APPROVE(1,"待审批"),
    /**
     * 2：审批通过
     */
    PASS(2,"审批通过"),
    /**
     * 3：审批不通过
     */
    NOT_PASS(3,"审批不通过");

    private final Integer status;
    private final String name;

    ApprovalStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public static Optional<ApprovalStatusEnum> getStatusByCode(int status) {
        return Arrays.stream(ApprovalStatusEnum.values())
                .filter(enumValue -> enumValue.getStatus().equals(status))
                .findFirst();
    }
}
