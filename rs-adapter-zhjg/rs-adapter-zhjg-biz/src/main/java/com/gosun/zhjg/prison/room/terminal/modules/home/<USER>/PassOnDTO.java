package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel("信件转交")
@Data
public class PassOnDTO {
    @ApiModelProperty(value = "信件编号",required = true)
    @NotBlank(message = "信件编号 不能为空")
    private String id;

    @ApiModelProperty(value = "转交人",required = true)
    @NotBlank(message = "转交人 不能为空")
    private String passUser;

    @ApiModelProperty(value = "转交时间",required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "转交时间 不能为空")
    private Date passTime;

    @ApiModelProperty("转交备注")
    private String passRemark;
}
