package com.gosun.zhjg.prison.room.terminal.modules.home.vo;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gosun.zhjg.common.context.PageSortMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
* NonInvolvedItemsVO 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2024-06-07
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="NonInvolvedItemsVO对象", description="非涉案物品")
public class NonInvolvedItemsVO implements Serializable {


    @JsonProperty("id")
    @ApiModelProperty(value = "主键")
    private String id;

    @JsonProperty("prisonerId")
    @ApiModelProperty(value = "人员编号")
    private String prisonerId;

    @JsonProperty("roomId")
    @ApiModelProperty(value = "监室号")
    private String roomId;

    @JsonProperty("roomName")
    @ApiModelProperty(value = "监室名字")
    private String roomName;

    @JsonProperty("prisonerName")
    @PageSortMapping(value = "xm")
    @ApiModelProperty(value = "人员姓名")
    private String prisonerName;

    @JsonProperty("itemName")
    @ApiModelProperty(value = "物品名称")
    private String itemName;

    @JsonProperty("itemSourceCode")
    @ApiModelProperty(value = "物品来源code")
    private String itemSourceCode;

    @JsonProperty("itemSourceCodeDisplayName")
    @ApiModelProperty(value = "物品来源中文")
    private String itemSourceCodeDisplayName;

    @JsonProperty("itemStatus")
    @ApiModelProperty(value = "物品状态")
    private Integer itemStatus;

    @JsonProperty("itemStatusDisplayName")
    @ApiModelProperty(value = "物品状态中文")
    private String itemStatusDisplayName;

    @JsonProperty("storageLocation")
    @ApiModelProperty(value = "存放位置 ")
    private String storageLocation;

    @JsonProperty("depositTime")
    @ApiModelProperty(value = "存入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date depositTime;

    @JsonProperty("manager")
    @ApiModelProperty(value = "管理人")
    private String manager;

    @JsonProperty("remarks")
    @ApiModelProperty(value = "备注")
    private String remarks;

    @JsonProperty("photoUrl")
    @ApiModelProperty(value = "照片url")
    private String photoUrl;

    @JsonProperty("photoName")
    @ApiModelProperty(value = "照片名字")
    private String photoName;

    @JsonProperty("removalTime")
    @ApiModelProperty(value = "取出时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date removalTime;

    @JsonProperty("removalUserTel")
    @ApiModelProperty(value = "取出人")
    private String removalUserTel;

    @JsonProperty("createTime")
    @ApiModelProperty(value = " 登记时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonProperty("createUserId")
    @ApiModelProperty(value = "登记人id")
    private String createUserId;

    @JsonProperty("createUser")
    @ApiModelProperty(value = "登记人")
    private String createUser;

    @JsonProperty("delFlag")
    @ApiModelProperty(value = "删除标志 1-删除")
    private Integer delFlag;

    @JsonProperty("prisonId")
    @ApiModelProperty(value = "监所编号 ")
    private String prisonId;

}
