package com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@TableName("acp_pm_device_inscreen_update_log")
@Data
@ApiModel("监室仓内外屏的更新日志")
public class DeviceInscreenUpdateLogEntity extends BaseDO {
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    //监室号
    private String roomId;

    //更新后的设备编号
    private String deviceId;

}
