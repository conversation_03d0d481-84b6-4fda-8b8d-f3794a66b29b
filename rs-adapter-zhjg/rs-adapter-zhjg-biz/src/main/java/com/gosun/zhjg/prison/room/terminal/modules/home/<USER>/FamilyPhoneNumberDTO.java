package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * family_phone_number 实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date: 2024-06-06
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value = "FamilyPhoneNumberDTO对象", description = "仓内屏-亲情电话表")
public class FamilyPhoneNumberDTO implements Serializable {

    private static final long serialVersionUID = -765539707841387510L;

    //@NotBlank(message = "与通话人员关系 不能为空")
    //@ApiModelProperty(value = "与通话人员关系",required = true)
    //private String relName;

    @JsonProperty("relCode")
    @NotBlank(message = "与通话人员关系 不能为空")
    @ApiModelProperty(value = "与通话人员关系 relCode",required = true)
    private String relCode;


    @JsonProperty("prisonerId")
    @ApiModelProperty(value = "在押人员编号",required = true)
    @NotBlank(message = "在押人员编号 不能为空")
    private String prisonerId;


    @JsonProperty("telNum")
    @ApiModelProperty(value = "电话号码",required = true)
    @NotBlank(message = "电话号码 不能为空")
    private String telNum;


    /**
     *
     */
    @ApiModelProperty(value = "通话对象")
    private String telObject;


    /**
     * 电话时间（通话时间）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "通话时间")
    private Date telTime;


}

