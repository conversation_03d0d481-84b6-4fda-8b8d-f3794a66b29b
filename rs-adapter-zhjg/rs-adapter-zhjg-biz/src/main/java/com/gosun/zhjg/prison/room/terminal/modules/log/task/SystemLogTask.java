package com.gosun.zhjg.prison.room.terminal.modules.log.task;


import com.gosun.zhjg.common.util.DateUtils;
import com.gosun.zhjg.common.util.UUIDUtils;
import com.gosun.zhjg.prison.room.terminal.modules.log.dao.jsyw.BaseSystemRunLogDao;
import com.gosun.zhjg.prison.room.terminal.modules.log.entity.BaseSystemRunLogEntity;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.util.ArrayDeque;
import java.util.Date;
import java.util.Deque;

@Component
@Slf4j
public class SystemLogTask {
    @Autowired
    private BaseSystemRunLogDao systemRunLogDao;

    @Value("${spring.application.name}")
    private String applicationName;

    @XxlJob("workLogTask")
    public void insertLog(){

        String workingDirectory = System.getProperty("user.dir");
        System.out.println("Current working directory: " + workingDirectory);
        String logFilePath = workingDirectory+"\\"+"logs"+"\\"+applicationName+".log";
        // 创建File对象
        File file = new File(logFilePath);
        if(file.exists()){
            try (BufferedReader reader = new BufferedReader(new FileReader(logFilePath))) {
                // 读取日志内容
                Deque<String> lines = new ArrayDeque<>();
                String line;
                StringBuilder stringBuilder = new StringBuilder("");
                while ((line = reader.readLine()) != null) {
                    lines.add(line);
                    if (lines.size() > 1000) {
                        lines.removeFirst();
                    }
                }

                for (String logLine : lines) {
                    if(StringUtils.isNotBlank(logLine)){
                        stringBuilder.append(logLine);
                    }

                }
                // 处理每一行日志
                BaseSystemRunLogEntity entity = new BaseSystemRunLogEntity();
                entity.setId(UUIDUtils.generateShortUuid());
                entity.setLogPath(logFilePath);
                entity.setCreateTime(new Date());
                entity.setServiceName(applicationName);
                entity.setName(DateUtils.format(new Date(),"yyyyMMddHHmm")+applicationName+".log");
                entity.setDelFlag(0);
                entity.setContent(stringBuilder.toString());
                this.systemRunLogDao.insert(entity);
                log.info(applicationName+".log系统运行日志生成成功");
                // 清空日志文件
                PrintWriter writer = new PrintWriter(new BufferedWriter(new FileWriter(logFilePath)));
                writer.print("");
                writer.close();
                log.info(applicationName+".log日志文件内容已清空");
                String logFileErrPath = workingDirectory+"\\"+"logs"+"\\"+applicationName+"-err.log";
                // 清空日志文件
                // 创建File对象
                File file2 = new File(logFileErrPath);
                if(file2.exists()){
                    PrintWriter writerErr = new PrintWriter(new BufferedWriter(new FileWriter(logFileErrPath)));
                    writerErr.print("");
                    writerErr.close();
                    log.info(applicationName+"-err.log日志文件内容已清空");
                }
            } catch (IOException e) {
                log.error(e+"");
            }
        }else {
            log.error(applicationName+".log============》日志文件不存在");
        }

    }
}
