package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
* prison_online_disease 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2024-04-12
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="PrisonOnlineDiseaseSearchDTO对象", description="仓内屏-在线报病")
public class PrisonOnlineDiseaseSearchDTO extends AbstractPageQueryForm implements Serializable {


   @JsonProperty("id")
   @ApiModelProperty(value = "主键")
   private String id;

   @JsonProperty("prisonerId")
   @ApiModelProperty(value = "在押人员编号")
   private String prisonerId;

   @JsonProperty("prisonerName")
   @ApiModelProperty(value = "在押人员姓名")
   private String prisonerName;

   @JsonProperty("roomId")
   @ApiModelProperty(value = "监室号")
   private String roomId;

   @JsonProperty("roomName")
   @ApiModelProperty(value = "监室名字")
   private String roomName;

   @ApiModelProperty(value = "预约时间 开始")
   @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private Date createBeginTime;

   @ApiModelProperty(value = "预约时间 结束")
   @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private Date createEndTime;




}
