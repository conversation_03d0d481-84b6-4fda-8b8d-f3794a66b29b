package com.gosun.zhjg.prison.room.terminal.common.utils;

import java.util.UUID;

/**
 * uuid生成工具
 * 
 * <AUTHOR>
 */
public class UuidUtil {
	private UuidUtil(){}

	/**
	 * 获取32位的uuid
	 *
	 * @return
	 */
	public static String uuid32() {
		return uuid36().replaceAll("-", "");
	}

	/**
	 * 获取原始的uuid，长度为36位
	 *
	 * @return
	 */
	public static String uuid36() {
		return UUID.randomUUID().toString();
	}

	/**
	 * 获取32位全部大写的uuid
	 *
	 * @return
	 */
	public static String uuid32WithUppercase() {
		return uuid32().toUpperCase();
	}

	/**
	 * 获取32位全部大写uuid指定偏移量后的字符串
	 *
	 * @param beginIndex
	 * @return
	 */
	public static String subUuid32WithUppercase(Integer beginIndex) {
		return uuid32WithUppercase().substring(beginIndex);
	}

	/**
	 * 获取32位全部大写uuid指定偏移量后的字符串
	 *
	 * @param beginIndex
	 * @param endIndex
	 * @return
	 */
	public static String subUuid32WithUppercase(Integer beginIndex, Integer endIndex) {
		return uuid32WithUppercase().substring(beginIndex, endIndex);
	}

	/**
	 * 获取32位全部小写的uuid
	 *
	 * @return
	 */
	public static String uuid32WithLowercase() {
		return uuid32().toLowerCase();
	}

	/**
	 * 获取32位全部小写uuid指定偏移量后的字符串
	 *
	 * @param beginIndex
	 * @return
	 */
	public static String subUuid32WithLowercase(Integer beginIndex) {
		return uuid32WithLowercase().substring(beginIndex);
	}

	/**
	 * 获取32位全部小写uuid指定偏移量后的字符串
	 *
	 * @param beginIndex
	 * @param endIndex
	 * @return
	 */
	public static String subUuid32WithLowercase(Integer beginIndex, Integer endIndex) {
		return uuid32WithLowercase().substring(beginIndex, endIndex);
	}

}
