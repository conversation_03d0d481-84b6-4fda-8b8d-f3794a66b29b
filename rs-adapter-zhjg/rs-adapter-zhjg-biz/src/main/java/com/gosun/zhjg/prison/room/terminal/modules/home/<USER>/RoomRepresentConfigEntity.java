package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;

import java.util.Date;

/**
 * room_represent_config-点名配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-06-30 09:29:58
 */
@Data
@TableName("room_represent_config")
public class RoomRepresentConfigEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @TableId(type = IdType.ASSIGN_UUID)

    private String id;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 监室name
     */
    private String roomName;
    /**
     * 配置周期
     */
    private String configPeriod;
    /**
     * 配置时间
     */
    private String startTime;
    /**
     * 创建人id
     */
    private String operateUserId;
    /**
     * 创建人name
     */
    private String operateUserName;
    /**
     * 创建时间
     */
    private Date operateTime;

    private String cron;

    /***
     *任务名称
     */
    private String taskName;

    /***
     *0：启用 1：未启用
     */
    private Integer status;

    /***
     *计划周期code 1-自定义 2-每天 3-执行一次
     */
    private String configPeriodCode;

    /***
     * 监所编号
     */
    private String prisonId;

    /***
     *配置的结束时间
     */
    private String endTime;

    /***
     * 结束时间的定时任务表达式
     */
    private String endCron;

    /***
     * 有效期
     */
    private Integer expiryDate;
}
