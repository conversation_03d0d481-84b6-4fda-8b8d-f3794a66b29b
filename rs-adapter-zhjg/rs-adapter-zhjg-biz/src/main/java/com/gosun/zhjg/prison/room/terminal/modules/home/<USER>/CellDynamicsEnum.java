package com.gosun.zhjg.prison.room.terminal.modules.home.enums;

import lombok.Getter;

/**
 * 反映监视动态选择类型
 * <AUTHOR>
 * @date 2025/3/8 15:02
 */
@Getter
public enum CellDynamicsEnum {

    POLICE("1","主协管民警"),
    LEADER("2","所领导");
    private final String status;
    private final String name;

    CellDynamicsEnum(String status, String name) {
        this.status = status;
        this.name = name;
    }
}
