package com.gosun.zhjg.prison.room.terminal.common.utils;


import com.alibaba.fastjson.JSONObject;
import com.gosun.zhjg.common.util.UUIDUtils;
import com.gosun.zhjg.prison.room.terminal.config.FtpConfig;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.utils.PdfMerger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.http.entity.ContentType;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URL;

@Slf4j
@Component
public class PdfUtil {

    @Resource(name = "remoteRestTemplate")
    RestTemplate restTemplate;

    public String getCompoundPdf (FtpConfig ftpConfig, String[] pdfArray) {
        String[] pdfUrls = pdfArray;
        String outputPdfFile = UUIDUtils.generateShortUuid() +".pdf";
        String httpPath="";
        try {
            PdfDocument resultPdf = new PdfDocument(new PdfWriter(new FileOutputStream(outputPdfFile)));
            PdfMerger merger = new PdfMerger(resultPdf);

            for (String pdfUrl : pdfUrls) {
                URL url = new URL(pdfUrl);
                try (InputStream inputStream = url.openStream()) {
                    PdfDocument pdf = new PdfDocument(new PdfReader(inputStream));
                    merger.merge(pdf, 1, pdf.getNumberOfPages());
                    pdf.close();
                }
            }

            resultPdf.close();
            log.info("PDF files merged successfully.");

            try (FileInputStream fis = new FileInputStream(outputPdfFile)) {
                MultipartFile file = new MockMultipartFile(ContentType.APPLICATION_OCTET_STREAM.toString(), fis);
                String path = "http://" + "VCPS-OSS-SERVICE" + "/files"+""+"/pdf"+"uploadByTerminalVersion";
                MultiValueMap<String,Object> map = new LinkedMultiValueMap<>();
                // 将multipartFile转换成byte资源进行传输
                ByteArrayResource resource = new ByteArrayResource(file.getBytes()) {
                    @Override
                    public String getFilename() {
                        return outputPdfFile;
                    }
                };

                HttpHeaders httpHeaders = new HttpHeaders();
                MediaType type = MediaType.parseMediaType("multipart/form-data");
                map.add("file",resource);
                httpHeaders.setContentType(type);
                httpHeaders.setContentLength(file.getSize());
                httpHeaders.setContentDispositionFormData("media",outputPdfFile);
                HttpEntity<MultiValueMap<String, Object>> objectHttpEntity = new HttpEntity<>(map, httpHeaders);
                ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(path, objectHttpEntity, String.class);
                log.info(stringResponseEntity.toString());
                String body = stringResponseEntity.getBody();
                JSONObject jsonObject = JSONObject.parseObject(body);
                JSONObject data = jsonObject.getJSONObject("data");
                httpPath = data.getString("relativePath");
                log.info("上传成功===" + httpPath);
            }

            log.info("PDF file uploaded to FTP PDF directory successfully.");
        } catch (Exception e) {
           log.error("合并pdf失败"+e.toString());
        }
        return httpPath;
    }

}
