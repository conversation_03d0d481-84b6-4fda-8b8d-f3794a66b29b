package com.gosun.zhjg.prison.room.terminal.modules.app.controller;

import com.gosun.zhjg.common.controller.BaseController;
import com.gosun.zhjg.common.msg.ResultVO;
import com.gosun.zhjg.prison.room.terminal.config.socketio.EscortMessage;
import com.gosun.zhjg.prison.room.terminal.modules.app.service.PrisonEscortDefenseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
*<AUTHOR>
*@email <EMAIL>
*@date 2020-08-05
*/

@Api(value = "app/message", tags = "收发信息")
@RestController
@RequestMapping("/app/escort")
@RequiredArgsConstructor
public class EscortController extends BaseController {

    private final PrisonEscortDefenseService prisonEscortDefenseService;


    // /* <AUTHOR>
    // @ApiOperation(value = "测试信息")
    // @GetMapping("test/")
    // public R test() {
    //     EscortMessage escortMessage = new EscortMessage();
    //     escortMessage.setSendContent("测试");
    //     escortMessage.setDefenseId("GXtuem7V");
    //     escortMessage.setSendName("蒋超鹏");
    //     socketIOService.pushMessageToUse2r(escortMessage,ESCORT_MESSAGE_EVENT_GET);
    //     return R.ResponseResult(escortMessage);
    // }

    @ApiOperation(value = "消息保存")
    @PostMapping("/message/save")
    public ResultVO<String> escortMessageSave(@RequestBody EscortMessage escortMessage) {
        return ResultVO.success(prisonEscortDefenseService.escortMessageSave(escortMessage));
    }


}
