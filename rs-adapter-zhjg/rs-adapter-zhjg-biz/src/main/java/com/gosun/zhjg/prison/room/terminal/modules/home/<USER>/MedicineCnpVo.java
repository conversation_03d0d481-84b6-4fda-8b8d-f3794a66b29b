package com.gosun.zhjg.prison.room.terminal.modules.home.vo;

import java.io.Serializable;
import java.util.Date;

import com.gosun.zhjg.common.context.PageSortMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 仓内屏服药管理台账表(MedicineCnp)Vo
 *
 * <AUTHOR>
 * @since 2023-11-03 17:46:02
 */
@Data
@ApiModel("仓内屏服药管理台账表vo")
public class MedicineCnpVo implements Serializable {
    private static final long serialVersionUID = -32187601932992776L;

    @ApiModelProperty("主键id")
    private String id;

    @ApiModelProperty("监室号")
    private String roomId;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("人员编号")
    private String prisonerId;

    @ApiModelProperty("人员姓名")
    private String prisonerName;

    @PageSortMapping("medication_time")
    @ApiModelProperty("服药日期")
    private String takeDate;

    /**
     * 该列的排序废弃
     */
    @PageSortMapping("TO_CHAR(medication_time, 'HH24:MI:SS')")
    @ApiModelProperty("服药时分秒")
    private String takeTime;

    @ApiModelProperty("服药时间")
    private Date medicationTime;

    @ApiModelProperty("拿药类型")
    private String getMedicineType;

    @ApiModelProperty("拿药类型名称")
    private String getMedicineTypeDisplayName;

    @ApiModelProperty("监所代码")
    private String prisonId;

    @ApiModelProperty("服药视频地址")
    private String url;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("签名地址")
    private String signUrl;


}