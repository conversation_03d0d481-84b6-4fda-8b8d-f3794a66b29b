package com.gosun.zhjg.prison.room.terminal.modules.terminal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备版本升级日志-批次
 *
 * <AUTHOR>
 * @date 2024/3/11 19:12
 */
@Data
@TableName("acp_pm_terminal_version_upgrade_log_batch")
public class TerminalVersionUpgradeLogBatchEntity extends BaseDO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@TableId(type = IdType.ASSIGN_UUID)
	private String id;
	/**
	 * 终端类型,字典：TERMINAL_SYSTEM_TERMINAL_TYPE
	 */
	private String terminalType;
	/**
	 * 更新包名
	 */
	private String packageName;
	/**
	 * 更新的包类型（upgrade_type） apk、web 字典：TERMINAL_SYSTEM_PACKAGE_TYPE
	 */
	private String packageType;
	/**
	 * 本次更新版本
	 */
	private String upgradeVersion;

	/**
	 * 更新时间
	 */
	private Date upgradeTime;

	/**
	 * 关联版本id
	 */
	private String versionManagementId;
	/**
	 * 版本说明
	 */
	private String releaseNotes;

}
