package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw;

import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.TerminalServiceIPVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TerminalServiceDao {

    /**
     * 获取IP不为空的终端设备（仓内外屏和对外服务终端）
     * @param prisonId
     * @return
     */
    List<TerminalServiceIPVo> getDeviceList(@Param("deviceId")String deviceId,@Param("prisonId") String prisonId);

    /**
     * 更新mac地址
     * @param deviceId 设备id
     * @param macAddress
     * @return
     */
    Integer updateMacAddress(@Param("deviceId")String deviceId,@Param("macAddress")String macAddress);


    /**
     * 更新序列号
     * @param deviceId 设备id
     * @param serialNumber
     * @return
     */
    Integer updateSerialNumber(@Param("deviceId")String deviceId,@Param("serialNumber")String serialNumber);
}
