package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * DTO for sacp_prison_violation_event
 */
@Data
@ApiModel("违规事件DTO")
public class ViolationEventDto extends AbstractPageQueryForm  implements Serializable {


    @ApiModelProperty(value = "监室号,逗号分隔（,）")
    private String roomIds;

    @ApiModelProperty(value = "监室名称")
    private String roomName;

    @ApiModelProperty(value = "违规登记开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeStart;

    @ApiModelProperty(value = "违规登记结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;

    @ApiModelProperty(value = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "来源类型（1：违规登记 2：所情管理）")
    private int srcType;


    @ApiModelProperty(value = "被监管人员编号")
    @NotNull(message = "被监管人员编号不能为空")
    private String prisonerId;

    @ApiModelProperty(value = "被监管人员姓名")
    private String prisonerName;

    @ApiModelProperty(value = "监所ID")
    @NotNull(message = "监所ID不能为空")
    private String prisonId;

    @ApiModelProperty(value = "审核状态（1:待审核, 2:通过, 3:驳回）")
    private int reviewStatus;

    @ApiModelProperty(value = "违规审核开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTimeStart;

    @ApiModelProperty(value = "违规审核结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTimeEnd;

    @ApiModelProperty(value = "违规申诉开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date appealTimeStart;

    @ApiModelProperty(value = "违规申诉结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date appealTimeEnd;
}
