package com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * rtc对讲记录表
 *
 * <AUTHOR>
 * @date 2023/8/10 18:44
 */
@Data
@TableName("rtc_call_history")
public class RtcCallHistoryEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * $column.comments
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * $column.comments
     */
    private String prisonId;
    /**
     * web对讲：webTalk
     */
    private String callType;
    /**
     * 会议状态 0：未开始 1：进行中 2：已结束 3：已取消
     */
    private Integer meetingState;
    /**
     * 发起呼叫人员
     */
    private String callerUserId;
    /**
     * 呼叫方设备序列号
     */
    private String callerSerialNumber;
    /**
     * 呼叫方名称
     */
    private String callerName;
    /**
     * $column.comments
     */
    private Integer callerRoleId;
    /**
     * 呼叫方角色
     */
    private String callerRoleName;
    /**
     * 接受方监室
     */
    private String receiverRoomId;
    /**
     * 接受方设备序列号
     */
    private String receiverSerialNumber;
    /**
     * 接受方名称
     */
    private String receiverName;
    /**
     * 呼叫时间
     */
    private Date callTime;
    /**
     * 呼叫挂断时间
     */
    private Date callEndTime;
    /**
     * $column.comments
     */
    private Long rtcRoomId;
    /**
     * RTC房间名称
     */
    private String rtcRoomName;
    /**
     * $column.comments
     */
    private Long rtcMeetingId;
    /**
     * RTC会议名称
     */
    private String rtcMeetingName;

    private Long callerDevId;
    private Long receiverDevId;
    /**
     * $column.comments
     */
    private Date createTime;

    /**
     * 对讲时长。单位秒
     */
    private Long callDurationSec;
}
