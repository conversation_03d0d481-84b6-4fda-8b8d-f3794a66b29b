package com.gosun.zhjg.prison.room.terminal.modules.inscreen.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;

@Service
public class NetworkCheckService {

    private static final Logger logger = LoggerFactory.getLogger(NetworkCheckService.class);

    /**
     * 检查指定的 IP 和端口是否可以建立 TCP 连接。
     * 这种方法是检查服务可用性的最安全、最可靠的方式。
     *
     * @param ip      目标主机的 IP 地址
     * @param port    目标主机的端口号
     * @param timeout 连接超时时间，单位为毫秒
     * @return 如果连接成功则返回 true，否则返回 false
     */
    public boolean isHostConnectable(String ip, int port, int timeout) {
        // 使用 try-with-resources 语句，这是确保资源（如此处的 Socket）
        // 被正确关闭的最佳实践。无论代码块是正常结束还是因异常退出，
        // Java 都会自动调用 socket.close() 方法，从而“释放资源”。
        try (Socket socket = new Socket()) {
            // InetSocketAddress 封装了 IP 地址和端口号
            InetSocketAddress endpoint = new InetSocketAddress(ip, port);

            // 发起连接，并指定超时时间。如果超时，会抛出 SocketTimeoutException。
            socket.connect(endpoint, timeout);

            // 如果 connect 方法执行完毕且没有抛出异常，说明连接成功。
            logger.info("成功连接到 {}:{}", ip, port);
            return true;
        } catch (IOException e) {
            // 捕获所有 IO 相关的异常，例如：
            // - java.net.ConnectException: Connection refused (端口未开放)
            // - java.net.SocketTimeoutException: connect timed out (连接超时)
            // - java.net.NoRouteToHostException: No route to host (主机不可达)
            logger.warn("无法连接到 {}:{} - 错误信息: {}", ip, port, e.getMessage());
            return false;
        }
    }
}
