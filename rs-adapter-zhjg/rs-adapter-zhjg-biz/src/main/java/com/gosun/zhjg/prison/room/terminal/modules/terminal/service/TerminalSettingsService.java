package com.gosun.zhjg.prison.room.terminal.modules.terminal.service;

import com.gosun.zhjg.prison.room.terminal.modules.terminal.dto.TerminalSettingsCnpDto;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dto.TerminalSettingsCwpDto;


/**
 * 终端-仓内外屏菜单表
 *
 * <AUTHOR>
 * @date 2024/12/12 11:01
 */
public interface TerminalSettingsService {

	/**
	 * 终端配置-仓内屏配置-详情
	 *
	 * @param prisonId
	 * @return
	 */
	TerminalSettingsCnpDto getCnpSettingsInfo(String prisonId);

	/**
	 * 终端配置-仓内屏配置-保存
	 *
	 * @param form
	 */
	void saveCnpSettings(TerminalSettingsCnpDto form, String userid, String name);

	/**
	 * 终端配置-仓外屏配置-详情
	 *
	 * @param prisonId
	 * @return
	 */
	TerminalSettingsCwpDto getCwpSettingsInfo(String prisonId);

	/**
	 * 终端配置-仓外屏配置-保存
	 *
	 * @param form
	 */
	void saveCwpSettings(TerminalSettingsCwpDto form, String userid, String name);
}

