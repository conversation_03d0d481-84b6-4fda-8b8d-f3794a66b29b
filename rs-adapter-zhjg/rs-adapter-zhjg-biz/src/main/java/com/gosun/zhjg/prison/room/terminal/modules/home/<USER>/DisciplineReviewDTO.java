package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel("信件管理-管教审核")
@Data
public class DisciplineReviewDTO {
    @ApiModelProperty("审核编号 批量逗号隔开")
    @NotBlank(message = "审核编号 不能为空")
    private String id;

    @ApiModelProperty("管教-审核状态 1-通过 2-另行处理")
    @NotBlank(message = "管教-审核状态 不能为空")
    private String policeCheck;

    @ApiModelProperty("审核人")
    @NotBlank(message = "审核人 不能为空")
    private String policeCheckUser;

    @ApiModelProperty("审核时间")
    @NotNull(message = "审核时间 不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date policeCheckTime;

    @ApiModelProperty("管教意见")
    private String policeCheckOpinion;

    @ApiModelProperty("处置情况")
    private String disposeSituation;

    @ApiModelProperty("处理人")
    private String disposeUser;

    @ApiModelProperty("处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date disposeTime;
}
