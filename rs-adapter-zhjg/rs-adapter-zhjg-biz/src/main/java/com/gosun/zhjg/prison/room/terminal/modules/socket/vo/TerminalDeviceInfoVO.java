package com.gosun.zhjg.prison.room.terminal.modules.socket.vo;

import lombok.Data;

@Data
public class TerminalDeviceInfoVO {

    /**
     * base_device 设备表id
     */
    private String deviceId;

    /**
     * C_SBZTDM: 001 在线， 002 离线， 003 故障
     */
    private String deviceStatus;

    private String serialNumber;

    private String macAddress;

    //socket注册的标识，仓内外屏是序列号，访客会见终端是mac地址
    private String socketMark;
}
