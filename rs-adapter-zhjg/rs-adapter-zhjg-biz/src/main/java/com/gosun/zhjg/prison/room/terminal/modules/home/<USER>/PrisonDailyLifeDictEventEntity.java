package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 一日生活 事务选项字典
 *
 * <AUTHOR>
 * @date 2023/4/17 14:14
 */
@Data
@TableName("prison_daily_life_dict_event")
public class PrisonDailyLifeDictEventEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * $column.comments
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 事务名称
     */
    private String eventName;
    /**
     * 事务启用状态，0禁用；1启用
     */
    private Integer enabled;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 更新人id
     */
    private String updateUserId;
    /**
     * 更新人名称
     */
    private String updateUserName;
}
