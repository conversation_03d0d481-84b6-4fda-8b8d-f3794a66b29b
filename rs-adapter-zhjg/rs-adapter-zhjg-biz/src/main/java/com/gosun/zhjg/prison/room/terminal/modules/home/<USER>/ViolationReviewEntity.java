package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 实体类表示违规登记审核表
 */
@Data
@TableName("sacp_prison_violation_review")
public class ViolationReviewEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 审核表唯一标识
     */
    @TableId(type = IdType.ASSIGN_UUID)

    private String id;

    /**
     * 关联违规登记ID
     */
    private String violationEventId;

    /**
     * 被监管人员编号
     */
    private String prisonerId;

    /**
     * 被监管人员名称
     */
    private String prisonerName;

    /**
     * 申诉时间
     */
    private Date appealTime;

    /**
     * 审核状态（1:待审核, 2:通过, 3:驳回）
     */
    private int reviewStatus;

    /**
     * 审核意见
     */
    private String reviewOpinion;

    /**
     * 审核人ID
     */
    private Integer reviewUserId;

    /**
     * 审核人姓名
     */
    private String reviewUserName;

    /**
     * 审核时间
     */
    private Date reviewTime;

    /**
     * 记录创建时间
     */
    private Date createTime;

    /**
     * 记录更新时间
     */
    private Date updateTime;

    /**
     * 逻辑删除标志（0: 正常, 1: 已删除）
     */
    private int delFlag;

    /**
     * 申诉理由
     */
    private String appealOpinion;

    /**
     * 监所编号
     */
    private String prisonId;

    /**
     * 监室名称
     */
    private String roomId;

    /**
     * 监室名称
     */
    private String roomName;


}
