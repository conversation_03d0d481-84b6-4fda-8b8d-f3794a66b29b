package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gosun.zhjg.common.dto.BaseFileSaveDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
* letter_manage 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2024-09-27
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="LetterManageDTO对象", description="收信管理-收件登记")
public class LetterManageDTO implements Serializable {
    @ApiModelProperty(value = "编号 编辑时候传")
    private String id;

    @JsonProperty("prisonerId")
    @ApiModelProperty(value = "人员编号",required = true)
    @NotBlank(message = "人员编号 不能为空")
    private String prisonerId;

    @JsonProperty("sendMailUser")
    @ApiModelProperty(value = "送信人姓名",required = true)
    @NotBlank(message = "送信人姓名 不能为空")
    private String sendMailUser;

    @JsonProperty("relation")
    @ApiModelProperty(value = "关系",required = true)
    @NotBlank(message = "关系 不能为空")
    private String relation;

    @ApiModelProperty(value = "来信单位")
    private String sendPrison;

    @JsonProperty("sendAddress")
    @ApiModelProperty(value = "来信地址")
    private String sendAddress;

    @JsonProperty("mailNo")
    @ApiModelProperty(value = "信件邮编")
    private String mailNo;

    @JsonProperty("sendDate")
    @ApiModelProperty(value = "来信日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendDate;

    @JsonProperty("registerUser")
    @ApiModelProperty(value = "登记人",required = true)
    @NotBlank(message = "登记人 不能为空")
    private String registerUser;

    @JsonProperty("registerTime")
    @ApiModelProperty(value = "登记时间",required = true)
    @NotNull(message = "登记时间 不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date registerTime;

    @ApiModelProperty(value = "附件-信件内容",required = true)
    @NotEmpty(message = "信件内容-不能为空")
    List<BaseFileSaveDto> messageFile = new ArrayList<>();


}

