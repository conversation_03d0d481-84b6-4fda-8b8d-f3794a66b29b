package com.gosun.zhjg.prison.room.terminal.modules.home.vo;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * RightsObligationsVO 实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date: 2024-06-18
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value = "RightsObligationsVO对象", description = "监室智能终端-权利义务")
public class RightsObligationsVO implements Serializable {


    @JsonProperty("id")
    @ApiModelProperty(value = "主键")
    private String id;

    @JsonProperty("prisonerId")
    @ApiModelProperty(value = "人员编号")
    private String prisonerId;

    @JsonProperty("prisonerName")
    @ApiModelProperty(value = "人员姓名")
    private String prisonerName;

    @JsonProperty("roomId")
    @ApiModelProperty(value = "监室号")
    private String roomId;

    @JsonProperty("roomName")
    @ApiModelProperty(value = "监室名字")
    private String roomName;

    @JsonProperty("entryTime")
    @ApiModelProperty(value = "入所时间")
    private Date entryTime;

    @JsonProperty("signStatus")
    @ApiModelProperty(value = "签名状态 1-已完成")
    private Integer signStatus;

    @JsonProperty("signStatusDisplayName")
    @ApiModelProperty(value = "签名状态中文")
    private String signStatusDisplayName;

    @JsonProperty("fingerprintStatus")
    @ApiModelProperty(value = "指纹状态 1-已完成")
    private Integer fingerprintStatus;

    @JsonProperty("fingerprintStatusDisplayName")
    @ApiModelProperty(value = "指纹状态中文")
    private String fingerprintStatusDisplayName;

    @JsonProperty("createTime")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonProperty("createUserId")
    @ApiModelProperty(value = "创建用户编号")
    private String createUserId;

    @JsonProperty("createUserName")
    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @JsonProperty("updateTime")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @JsonProperty("updateUserId")
    @ApiModelProperty(value = "更新人用户编号")
    private String updateUserId;

    @JsonProperty("updateUserName")
    @ApiModelProperty(value = "更新人")
    private String updateUserName;

    @JsonProperty("prisonId")
    @ApiModelProperty(value = "单位代码")
    private String prisonId;

    @JsonProperty("distributionStatus")
    @ApiModelProperty(value = "下发状态")
    private Integer distributionStatus;

    @JsonProperty("distributionStatusDisplayName")
    @ApiModelProperty(value = "下发状态中文")
    private String distributionStatusDisplayName;

    @JsonProperty("noticeLetterUrl")
    @ApiModelProperty(value = "告知书url")
    private String noticeLetterUrl;

    @JsonProperty("signImageUrl")
    @ApiModelProperty(value = "签名图片url")
    private String signImageUrl;

    @JsonProperty("signPdfUrl")
    @ApiModelProperty(value = "签名pdf url")
    private String signPdfUrl;

    @JsonProperty("fingerprintPdfUrl")
    @ApiModelProperty(value = "指纹转pdf url")
    private String fingerprintPdfUrl;

    @JsonProperty("fingerprintImageUrl")
    @ApiModelProperty(value = "指纹图片url")
    private String fingerprintImageUrl;

    @JsonProperty("noticePdfUrl")
    @ApiModelProperty(value = "告知书合成pdf")
    private String noticePdfUrl;

    @JsonProperty("delFlag")
    @ApiModelProperty(value = " 删除标志")
    private Integer delFlag;

    @JsonProperty("signTime ")
    @ApiModelProperty(value = "签名时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signTime;

    @JsonProperty("fingerprintTime")
    @ApiModelProperty(value = "指纹时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fingerprintTime;

}
