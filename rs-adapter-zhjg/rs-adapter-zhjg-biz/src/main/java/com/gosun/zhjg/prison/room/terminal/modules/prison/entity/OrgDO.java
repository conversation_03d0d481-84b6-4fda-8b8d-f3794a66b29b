package com.gosun.zhjg.prison.room.terminal.modules.prison.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.Date;

/**
 * 实战平台-监管管理-机构管理 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_org")
@KeySequence("acp_pm_org_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrgDO extends BaseDO {
private static final long serialVersionUID = 1L;

    /**
     * 主键（与BSP的uac_org表的ID一对一关系）
     */
    @TableId(type = IdType.INPUT)
    private String id;

    /**
     * 简称
     */
    private String sname;

    /**
     * 全称
     */
    private String fname;

    /**
     * 拼音码
     */
    private String scode;

    /**
     * 简拼码
     */
    private String jpcode;

    /**
     * 所属城市ID
     */
    @TableField(value = "city_id")
    private String cityCode;

    @TableField(value = "CITY_NAME")
    private String cityName;

    @TableField(value = "code")
    private String orgCode;

    @TableField(value = "name")
    private String orgName;

    @TableField(value = "region_id")
    private String regCode;

    @TableField(value = "region_name")
    private String regName;

    /**
     * 上级机构ID
     */
    private String parentId;

    /**
     * 单位类型（字典：ZD_ORG_TYPE）
     */
    private String orgType;

    /**
     * 地址
     */
    private String address;

    /**
     * 是否正式机构(0否1是)
     */
    private String isFormal;

    /**
     * 是否停用(0否1是)
     */
    private String isDisabled;

    /**
     * 排序Id
     */
    private Integer orderId;

    /**
     * 所领导名称
     */
    private String leaderName;

    /**
     * 是否分支
     */
    private Integer isBranch;

    /**
     * 所属支队
     */
    private String branchId;

    /**
     * 办公电话
     */
    private String officeTel;

    /**
     * 传真电话
     */
    private String czdh;

    /**
     * 邮政编码
     */
    private String postCode;

    /**
     * 电子邮件
     */
    private String email;

    /**
     * 监所等级（字典： ZD_GAJSDJ）
     */
    private String jsdj;

    /**
     * 监所规模（字典：ZD_GM）
     */
    private String jsgm;

    /**
     * 民警总数
     */
    private Integer mjzs;

    /**
     * 编制人数
     */
    private Integer bzrs;

    /**
     * 监室数
     */
    private Integer jsh;

    /**
     * 设计容量
     */
    private Integer sjrl;

    /**
     * 协警数
     */
    private Integer xj;

    /**
     * 职工数
     */
    private Integer zg;

    /**
     * 文职数
     */
    private Integer wz;

    /**
     * 建设时间
     */
    private Date jssj;

    /**
     * 启用时间
     */
    private Date qysj;

    /**
     * 单人监室数
     */
    private Integer drjs;

    /**
     * 总建筑面积
     */
    private BigDecimal zjzmj;

    /**
     * 监区面积
     */
    private BigDecimal jqmj;

    /**
     * 驻所武警数
     */
    private Integer zswjs;

    /**
     * 询问室数
     */
    private Integer xws;

    /**
     * 律师会见室
     */
    private Integer lxhjs;

    /**
     * 家属会见室
     */
    private Integer jshjs;

    /**
     * 医生数量
     */
    private Integer yssl;

    /**
     * 护士数量
     */
    private Integer hssl;


    /**
     * 经度
     */
    private String jd;

    /**
     * 纬度
     */
    private String wd;

    /**
     * 地图X坐标
     */
    private String mapPostion;

    /**
     * 地图id
     */
    private String mapId;

    /**
     * 标签位置
     */
    private String mapTag;

    /**
     * 提示框位置
     */
    private String tskwz;
    /**
     * 地址中文
     */
    private String dzName;


    /**
     * 图片地址
     */
    private String photoUrl;

    /**
     * 值班开始时间
     */
    private LocalTime zbkssj;

    /**
     * 值班结束时间
     */
    private LocalTime zbjssj;

}
