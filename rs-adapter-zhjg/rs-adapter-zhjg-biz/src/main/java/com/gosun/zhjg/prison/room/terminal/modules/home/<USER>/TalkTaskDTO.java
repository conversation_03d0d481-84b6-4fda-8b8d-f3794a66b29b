package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 谈话教育
 * <AUTHOR>
 * @date 2025/3/10 9:23
 */
@NoArgsConstructor
@Data
public class TalkTaskDTO implements Serializable {

    private static final long serialVersionUID = 4006895245641452043L;

    private String dataSource;

    private String prisonId;

    private String rybh;

    private String talkType;

    private String thirdId;
}
