package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * terminal_prison_business_public-所务公开
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-06-12 09:19:35
 */
@Data
@TableName("terminal_prison_business_public")
public class TerminalPrisonBusinessPublicEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_UUID)

    private String id;
    /**
     * 标题
     */
    private String title;
    /**
     * 类型
     */
    private String businessType;
    /**
     * 内容
     * {@code @TableField(value = "\"context\"")} context 在达梦数据库是必须加双引号
     */
    @TableField(value = "\"context\"")
    private String context;
    /**
     * 发布人
     */
    private String publisher;
    /**
     * 发布时间
     */
    private Date publicTime;
    /**
     * 状态
     */
    private String status;
    /**
     * 创建人id
     */
    private String createUserId;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建时间
     */
    private Date createTime;

    /***
     * 监所编号
     */
    private String prisonId;

}
