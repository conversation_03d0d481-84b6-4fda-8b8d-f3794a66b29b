package com.gosun.zhjg.prison.room.terminal.modules.inscreen.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gosun.zhjg.common.controller.BaseController;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.common.msg.ResultVO;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.BaseDeviceInscreenPageDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.BaseDeviceTerminalPageDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.BaseDeviceTerminalTypeDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.BaseRoomDeviceTerminalDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.ZhjgBaseDeviceInscreenService;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 仓内屏
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-09-15 10:55:29
 */
@Slf4j
@Api(value = "仓内屏", tags = "仓内屏")
@RestController
@RequestMapping("inscreen/basedeviceinscreen")
public class BaseDeviceInscreenController extends BaseController {
    @Autowired
    private ZhjgBaseDeviceInscreenService baseDeviceInscreenService;

    /**
     * 列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ApiOperation(value = "仓内屏-分页列表", responseContainer = "List", response = BaseDeviceInscreenPageVO.class)
    public R<BaseDeviceInscreenPageVO> list(@ApiParam(hidden = true) BaseDeviceInscreenPageDto pageDto) {
        try {
            Page page = pageDto.trainToPage();
            List<BaseDeviceInscreenPageVO> list = baseDeviceInscreenService.findByPage(page, pageDto);
            page.setRecords(list);
            return R.ResponsePage(page);
        } catch (Exception e) {
            e.printStackTrace();
            return R.ResponseError(e.getMessage());
        }
    }

    /**
     * 信息
     */
    @RequestMapping(value = "/info/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "仓内屏-根据编号查找记录", responseContainer = "Map", response = BaseDeviceInscreenVO.class)
    @ApiImplicitParam(paramType = "path", name = "id", value = "编号", required = true, dataType = "String")
    public R<BaseDeviceInscreenVO> info(@PathVariable("id") String id) {
        BaseDeviceInscreenVO baseDeviceInscreen = baseDeviceInscreenService.findOneById(id);
        return (new R<BaseDeviceInscreenVO>()).ResultData(baseDeviceInscreen);
    }

//    /**
//     * 根据序列号查找记录
//     */
//    @RequestMapping(value = "/getBySerialNum/{serialNum}", method = RequestMethod.GET)
//    @ApiOperation(value = "仓内屏-根据序列号查找记录", responseContainer = "Map", response = BaseDeviceInscreenVO.class)
//    @ApiImplicitParam(paramType = "path", name = "serialNum", value = "序列号", required = true, dataType = "String")
//    public R<BaseDeviceInscreenVO> getBySerialNum(@PathVariable("serialNum") String serialNum) {
//        BaseDeviceInscreenVO baseDeviceInscreen = baseDeviceInscreenService.getBySerialNum(serialNum);
//        return (new R<BaseDeviceInscreenVO>()).ResultData(baseDeviceInscreen);
//    }

    /**
     * 根据监室查找记录
     */
    @RequestMapping(value = "/getByRoom/{roomId}", method = RequestMethod.GET)
    @ApiOperation(value = "仓内屏-根据监室查找记录", responseContainer = "Map", response = BaseDeviceInscreenVO.class)
    @ApiImplicitParam(paramType = "path", name = "roomId", value = "监室号", required = true, dataType = "String")
    public R<BaseDeviceInscreenVO> getByRoom(@PathVariable("roomId") String roomId) {
        BaseDeviceInscreenVO baseDeviceInscreen = baseDeviceInscreenService.getByRoom(roomId);
        return (new R<BaseDeviceInscreenVO>()).ResultData(baseDeviceInscreen);
    }

    /**
     * 根据设备号查找记录
     */
    @RequestMapping(value = "/getByDeviceNum/{deviceNum}", method = RequestMethod.GET)
    @ApiOperation(value = "仓内屏-根据设备号查找记录", responseContainer = "Map", response = BaseDeviceInscreenVO.class)
    @ApiImplicitParam(paramType = "path", name = "deviceNum", value = "序列号", required = true, dataType = "Integer")
    public R<BaseDeviceInscreenVO> getByDeviceNum(@PathVariable("deviceNum") Integer deviceNum) {
        BaseDeviceInscreenVO baseDeviceInscreen = baseDeviceInscreenService.getByDeviceNum(deviceNum);
        return (new R<BaseDeviceInscreenVO>()).ResultData(baseDeviceInscreen);
    }

//    /**
//     * 保存
//     */
//    @RequestMapping(value = "/save", method = RequestMethod.POST)
//    @ApiOperation(value = "仓内屏-信息保存", responseContainer = "Map")
//    public R save(@RequestBody BaseDeviceInscreenSaveDto baseDeviceInscreen) {
//        baseDeviceInscreenService.saveBaseDeviceInscreen(baseDeviceInscreen, getUser().getUserid(), getUser().getName());
//        return R.ResponseOk("保存成功");
//    }
//
//    /**
//     * 修改
//     */
//    @RequestMapping(value = "/update", method = RequestMethod.POST)
//    @ApiOperation(value = "仓内屏-信息更新", responseContainer = "Map")
//    public R update(@RequestBody BaseDeviceInscreenUpdateDto baseDeviceInscreen) {
//        baseDeviceInscreenService.updateBaseDeviceInscreen(baseDeviceInscreen, getUser().getUserid(), getUser().getName());
//        return R.ResponseOk("更新成功");
//    }
//
//    /**
//     * 删除
//     */
//    @RequestMapping(value = "/delete", method = RequestMethod.POST)
//    @ApiOperation(value = "仓内屏-删除", responseContainer = "Map")
//    @ApiImplicitParam(paramType = "body", name = "ids", value = "编号", required = true, allowMultiple = true, dataType = "String")
//    public R delete(@RequestBody String[] ids) {
//        baseDeviceInscreenService.deleteByIds(ids);
//        return R.ResponseOk("删除成功");
//    }


    @GetMapping("getRoomDeviceList")
    @ApiOperation(value = "获取监室终端设备列表",response = BaseDeviceTerminalVo.class)
    public ResultVO getRoomDeviceList(BaseDeviceTerminalPageDto dto) {
        Page page = dto.trainToPage();
        dto.setPrisonId(getUser().getPrisonId());
        List<BaseDeviceTerminalVo> list = baseDeviceInscreenService.getRoomDeviceList(page,dto);
        page.setRecords(list);
        return ResultVO.page(page);
    }

    @PostMapping ("getRoomDeviceListByPost")
    @ApiOperation(value = "获取监室终端设备列表-跨服务调用",response = BaseDeviceTerminalVo.class)
    public R getRoomDeviceListByPost(@RequestBody BaseDeviceTerminalPageDto dto) {
        Page page = dto.trainToPage();
        List<BaseDeviceTerminalVo> list = baseDeviceInscreenService.getRoomDeviceList(page,dto);
        page.setRecords(list);
        return R.ResponsePage(page);
    }

    @GetMapping("getRoomDeviceByRoomId")
    @ApiOperation(value = "通过监室号获取监室终端设备",responseContainer = "Map",response = BaseDeviceTerminalInfoVo.class)
    public ResultVO getRoomDeviceByRoomId(String roomId) {
        return ResultVO.success(baseDeviceInscreenService.getRoomDeviceByRoomId(roomId));
    }

    @GetMapping("getDeviceListByType")
    @ApiOperation(value = "通过设备类型获取设备列表",response = BaseDeviceTerminalInfoVo.class)
    public ResultVO getDeviceListByType(BaseDeviceTerminalTypeDto dto){
        Page page = dto.trainToPage();
        dto.setPrisonId(getUser().getPrisonId());
        List<BaseDeviceTerminalInfoVo> list = baseDeviceInscreenService.getDeviceListByType(page,dto);
        page.setRecords(list);
        return ResultVO.page(page);
    }

    @PostMapping("updateRoomDevice")
    @ApiOperation("修改监室仓内外屏挂靠配置")
    public ResultVO updateRoomDevice(@RequestBody BaseRoomDeviceTerminalDto dto){
        return baseDeviceInscreenService.updateRoomDevice(dto,getUser());
    }

    /**
     * 根据序列号获取仓内屏相关监室、设备、配置等信息
     *
     * @param serialNum 序列号
     * @return
     */
    @ApiOperation("根据序列号获取仓内屏相关监室、设备、配置等信息")
    @RequestMapping(value = "/getCnpInfo/{serialNum}", method = RequestMethod.GET)
    @ApiImplicitParam(paramType = "path", name = "serialNum", value = "序列号", required = true, dataType = "String")
    public R<?> getCnpInfo(@PathVariable("serialNum") String serialNum) {
        CnpDeviceInfoVO vo = baseDeviceInscreenService.getCnpInfo(serialNum);
        log.info("=====> 仓内屏序列号获取监室信息 serialNum{}, roomName={} ({})", serialNum, vo.getRoomName(), vo.getId());
        return R.ResponseResult(vo);
    }

    /**
     * 根据序列号获取仓外屏相关监室、设备、配置等信息
     *
     * @param serialNum 序列号
     * @return
     */
    @ApiOperation("根据序列号获取仓外屏相关监室、设备、配置等信息")
    @RequestMapping(value = "/getCwpInfo/{serialNum}", method = RequestMethod.GET)
    @ApiImplicitParam(paramType = "path", name = "serialNum", value = "序列号", required = true, dataType = "String")
    public R<?> getCwpInfo(@PathVariable("serialNum") String serialNum) {
        CwpInfoVO vo = baseDeviceInscreenService.getCwpInfo(serialNum);
        List<CwpDeviceInfoVO> records = vo.getList() == null ? Collections.emptyList() : vo.getList();
        String roomName = records.stream().map(CwpDeviceInfoVO::getRoomName).collect(Collectors.joining(","));
        log.info("=====> 仓外屏序列号获取监室信息 serialNum{}, roomName={}", serialNum, roomName);

        return R.ResponseResult(vo);
    }

}
