package com.gosun.zhjg.prison.room.terminal.common.utils;



import com.alibaba.fastjson.JSONObject;
import com.gosun.zhjg.prison.room.terminal.config.FtpConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import sun.misc.BASE64Decoder;

import javax.annotation.Resource;
import java.io.*;
import java.util.Base64;

/**
 * @program: trunk
 * @description: 用于Base84解码编码
 * @author: TangTao
 * @create: 2019-09-03 14:38
 **/
@Slf4j
@Component
public class Base64Util {

    @Resource(name = "remoteRestTemplate")
    RestTemplate restTemplate;

    private static Base64Util utils = null;

    private Base64Util(){

    }

    /**
     * 机能概要:单利 ，懒汉模式
     * @return
     */
    public static Base64Util getInstance(){
        if(utils == null){
            synchronized (Base64Util.class) {
                if(utils == null ){
                    utils = new Base64Util();
                }
            }
        }
        return utils;
    }
    /**
     * 机能概要:获取文件的大小
     * @param inFile 文件
     * @return 文件的大小
     */
    public static int getFileSize(File inFile){
        InputStream in = null;

        try {
            in = new FileInputStream(inFile);
            //文件长度
            int len = in.available();
            return len;
        }catch (Exception e) {
            // TODO: handle exception
        }finally{
            try {
                in.close();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        return -1;
    }

    /**
     * 机能概要:将文件转化为base64
     * @return
     * @throws Exception
     */
    public static String file2Base64(File inFile){

        //将文件转化为字节码
        byte [] bytes = copyFile2Byte(inFile);
        if(bytes == null){
            return null;
        }

        //base64,将字节码转化为base64的字符串
        String result = Base64.getEncoder().encodeToString(bytes);
        return result;
    }

    /**
     * 机能概要:将文件转化为字节码
     * @param inFile
     * @return
     */
    private static byte [] copyFile2Byte(File inFile){
        InputStream in = null;

        try {
            in = new FileInputStream(inFile);
            //文件长度
            int len = in.available();

            //定义数组
            byte [] bytes = new byte[len];

            //读取到数组里面
            in.read(bytes);
            return bytes;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }finally{
            try {
                if(in != null){
                    in.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 机能概要:将字符串转化为文件
     */
    public String base64ToFile(FtpConfig ftpConfig, String newName, String savePath, String photoBase){
        try {
            // 解码，然后将字节转换为文件
            byte[] bytes = new BASE64Decoder().decodeBuffer(photoBase); // 将字符串转换为byte数组
            return copyByte2File(ftpConfig,newName,savePath,bytes);
        } catch (Exception ioe) {
            ioe.printStackTrace();
        }
        return "";
    }
    /**
     * 机能概要:将字节码转化为文件,并上传服务器
     */
    public String copyByte2File(FtpConfig ftpConfig, String newName, String savePath, byte[] bytes){
        String httpPath="";
        try {
            long l = System.currentTimeMillis();
            System.out.println("上传开始"+l);
            InputStream inputStream = new ByteArrayInputStream(bytes);
            MultipartFile file = new MockMultipartFile(ContentType.APPLICATION_OCTET_STREAM.toString(), inputStream);
            String path = "http://" + "VCPS-OSS-SERVICE" + "/files"+savePath+"/"+"upload";
            MultiValueMap<String,Object> map = new LinkedMultiValueMap<>();
            // 将multipartFile转换成byte资源进行传输
            ByteArrayResource resource = new ByteArrayResource(file.getBytes()) {
                @Override
                public String getFilename() {
                    return newName;
                }
            };
            HttpHeaders httpHeaders = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("multipart/form-data");
            map.add("file",resource);
            httpHeaders.setContentType(type);
            httpHeaders.setContentLength(file.getSize());
            httpHeaders.setContentDispositionFormData("media",newName);
            HttpEntity<MultiValueMap<String, Object>> objectHttpEntity = new HttpEntity<>(map, httpHeaders);
            ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(path, objectHttpEntity, String.class);
            log.info(stringResponseEntity.toString());
            String body = stringResponseEntity.getBody();
            JSONObject jsonObject = JSONObject.parseObject(body);
            JSONObject data = jsonObject.getJSONObject("data");
            httpPath = data.getString("relativePath");
            log.info("上传成功===" + httpPath);
            long l2 = System.currentTimeMillis();
            System.out.println("上传结束耗时"+(l2-l));
//            httpPath=FtpUtil.uploadByConfig(ftpConfig,newName, savePath, file.getInputStream(),dataBase);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            log.error("",e);
            e.printStackTrace();
        }
        return httpPath;
    }

    /**
     * 二进制转换base64
     * @return
     * @throws IOException
     */
    //byte[]转base64
    public static String byte2Base64StringFun(byte[] b){
        return org.apache.commons.codec.binary.Base64.encodeBase64String(b);
    }
}