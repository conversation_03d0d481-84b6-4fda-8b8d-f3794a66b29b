package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 监室值日
 *
 * <AUTHOR>
 * @date 2024/11/1 8:59
 */
@Data
@TableName("terminal_room_day_duty")
public class TerminalRoomDayDutyEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@TableId(type = IdType.ASSIGN_UUID)

	private String id;
	/**
	 * 单位代码
	 */
	private String prisonId;
	/**
	 * 监室号
	 */
	private String roomId;
	/**
	 * 值班日期
	 */
	private Date dutyDate;
	/**
	 * 排班人
	 */
	private String assignerUserId;
	/**
	 * 排班人
	 */
	private String assigner;
	/**
	 * 排班时间
	 */
	private Date assignedTime;
}
