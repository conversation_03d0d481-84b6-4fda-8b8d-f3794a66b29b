package com.gosun.zhjg.prison.room.terminal.modules.prison.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.PinyinUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.gosun.zhjg.basic.business.modules.prison.dto.BasePrisonInfoAddRolesForm;
import com.gosun.zhjg.basic.business.modules.prison.dto.BasePrisonInfoSaveForm;
import com.gosun.zhjg.basic.business.modules.prison.vo.BasePrisonInfoVO;
import com.gosun.zhjg.common.util.TreeUtil;
import com.gosun.zhjg.common.vo.TreeNode;
import com.gosun.zhjg.prison.room.terminal.common.service.DictService;
import com.gosun.zhjg.prison.room.terminal.modules.prison.dao.OrgDao;
import com.gosun.zhjg.prison.room.terminal.modules.prison.entity.OrgDO;
import com.gosun.zhjg.prison.room.terminal.modules.prison.service.BasePrisonInfoService;
import com.rs.enums.PrisonTypeEnum;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.framework.mybatis.util.UacUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.sql.SQLException;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service("basePrisonInfoService")
@Slf4j
public class BasePrisonInfoServiceImpl implements BasePrisonInfoService {

    @Autowired
    private OrgDao orgDao;

    @Autowired
    private DictService dictService;

    @Value("${file-url-prefix}")
    private String pathPrefix;


    @Override
    public List<Map<String, Object>> prisonTypeList() {
        List<Map<String, Object>> maps = new ArrayList<>();
        PrisonTypeEnum[] values = PrisonTypeEnum.values();
        for (PrisonTypeEnum prisonTypeEnum : values) {
            HashMap<String, Object> map = new HashMap<>();
            map.put("code", prisonTypeEnum.getValue());
            map.put("name", prisonTypeEnum.getDesc());
            map.put("isBranch", prisonTypeEnum.getIsZd());
            maps.add(map);
        }
        return maps;
    }

    @Override
    public void addRoles(BasePrisonInfoAddRolesForm form) {
        try {
            Db db = UacUserUtil.getDb();
            SessionUser user = SessionUserUtil.getSessionUser();
            List<Entity> entityList = new ArrayList<>();
            form.getRoleIds().forEach( e->{
                entityList.add( Entity.create("uac_role_org")
                        .set("ID", String.valueOf( IdUtil.getSnowflake().nextId()))
                        .set("role_id", e)
                        .set("org_id", form.getPrisonId())
                        .set("add_user", user.getIdCard())
                        .set("add_time", new Date()));
            });
            // 执行批量插入（使用默认数据源）
            db.insert(entityList);
        } catch (Exception e) {
            log.error("管理机构管理角色失败", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void deleteRoles(BasePrisonInfoAddRolesForm form) {
        try {
            UacUserUtil.getDb().del(  Entity.create("uac_role_org").set("role_id", form.getRoleIds()).set("org_id", form.getPrisonId()));
        } catch (Exception e) {
            log.error("删除机构管理角色失败", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public BasePrisonInfoVO getDetails(String id) {
        OrgDO orgDO = orgDao.selectById(id);
        if( orgDO == null){
            return null;
        }
        BasePrisonInfoVO vo = new BasePrisonInfoVO();
        BeanUtils.copyProperties(orgDO, vo);
        vo.setId(orgDO.getId());
        vo.setPrisonName(orgDO.getOrgName());
        vo.setPrisonAbbreviation(orgDO.getSname());
        vo.setOrgId(orgDO.getId());
        vo.setLeaderName(orgDO.getLeaderName());
        vo.setPrisonType(orgDO.getOrgType());
        vo.setLeaderName(orgDO.getLeaderName());
        vo.setDz(orgDO.getRegCode());
        vo.setXz(orgDO.getAddress());
        vo.setDh(orgDO.getOfficeTel());
        vo.setCz(orgDO.getCzdh());
        vo.setYzbm(orgDO.getPostCode());
        vo.setDzxx(orgDO.getEmail());
        vo.setCreateTime(orgDO.getAddTime());
        vo.setUpdateTime(orgDO.getUpdateTime());
        vo.setPrisonTypeDisplayName(PrisonTypeEnum.getDesc(vo.getPrisonType()));

        DateTimeFormatter customFormatter = DateTimeFormatter.ofPattern("HH:mm");
        if(orgDO.getZbkssj() != null){
            vo.setZbkssj(customFormatter.format( orgDO.getZbkssj()));
        }

        if(orgDO.getZbjssj() != null){
            vo.setZbjssj(customFormatter.format( orgDO.getZbjssj()));
        }

        //private String branchIdName; //所属支队名字
        if(StrUtil.isNotBlank(vo.getPhotoUrl())){
            vo.setPhotoUrl(pathPrefix + vo.getPhotoUrl());
        }
        vo.setDeleteAble( false);
        try {
            long count = UacUserUtil.getDb().queryNumber("select count(1) from  uac_user_role role where role.org_id = ?", orgDO.getId()).longValue();
            if(count == 0){
                vo.setDeleteAble( true);
            }
        } catch (Exception e) {
            // 记录日志或进行其他处理
           log.error("获取角色关联信息失败", e);
        }


        if(StrUtil.isNotBlank(orgDO.getJsdj())){
            vo.setJsdjDisplayName(dictService.dictValue("ZD_GAJSDJ", orgDO.getJsdj()));
        }

        if(StrUtil.isNotBlank(orgDO.getJsgm())){
            vo.setJsdjDisplayName(dictService.dictValue("ZD_GM", orgDO.getJsgm()));
        }

        return vo;
    }

    @Override
    public List<BasePrisonInfoVO>  branchList(String code) {
        List<OrgDO> list = orgDao.selectList(new LambdaQueryWrapper<OrgDO>().eq(OrgDO::getOrgType, "05"));
        List<BasePrisonInfoVO> result = new ArrayList<>();
        for (OrgDO orgDO : list) {
            BasePrisonInfoVO vo = new BasePrisonInfoVO();
            BeanUtils.copyProperties(orgDO, vo);
            vo.setId(orgDO.getId());
            vo.setPrisonName(orgDO.getOrgName());
            vo.setPrisonAbbreviation(orgDO.getSname());
            vo.setOrgId(orgDO.getId());
            vo.setLeaderName(orgDO.getLeaderName());
            vo.setPrisonType(orgDO.getOrgType());
            vo.setLeaderName(orgDO.getLeaderName());
            vo.setDz(orgDO.getRegCode());
            vo.setXz(orgDO.getAddress());
            vo.setDh(orgDO.getOfficeTel());
            vo.setCz(orgDO.getCzdh());
            vo.setYzbm(orgDO.getPostCode());
            vo.setDzxx(orgDO.getEmail());
            vo.setCreateTime(orgDO.getAddTime());
            vo.setUpdateTime(orgDO.getUpdateTime());
            vo.setPrisonTypeDisplayName(PrisonTypeEnum.getDesc(vo.getPrisonType()));
            //private String branchIdName; //所属支队名字
            if(StrUtil.isNotBlank(vo.getPhotoUrl())){
                vo.setPhotoUrl(pathPrefix + vo.getPhotoUrl());
            }
            vo.setIsBranch(1);
            if(StrUtil.isNotBlank(code) && !vo.getOrgId().startsWith(code)){
                continue;
            }
            result.add(vo);
        }
        return result;

    }

    @Override
    public List<TreeNode> cityTree(String name) {
        List<TreeNode> treeNodes = TreeUtil.buildByRecursive(orgDao.cityTree(), "");
        return treeNodes;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(BasePrisonInfoSaveForm form) {
        // 校验ID是否存在。
        Assert.isNull(orgDao.selectById(form.getId()), "机构编码已存在");
        OrgDO orgDO = buildOrgDO(form);
        orgDao.insert(orgDO);
        try {
            //所领导用户id Integer leaderId;
            UacUserUtil.getDb().insert(buildEntity(form).setTableName("uac_org"));
        } catch (SQLException e) {
            log.error("bsp-uac-org插入失败", e);
            throw new RuntimeException(e);
        }

    }

    /**
     * 构建bsp uac_org Entity
     * <AUTHOR>
     * @date 2025/5/21 19:49
     * @param [form]
     * @return cn.hutool.db.Entity
     */
    private Entity buildEntity(BasePrisonInfoSaveForm form){
        String cityId = null, cityName = null,  regName = null;
        if(StrUtil.isNotBlank(form.getDz()) && form.getDz().length() > 4){
            cityId = form.getDz().substring(0, 4) + "00";
        }
        if(StrUtil.isNotBlank(form.getDzName()) && form.getDzName().split("/").length == 3){
            regName = form.getDzName().split("/")[2].trim();
            cityName= form.getDzName().split("/")[1].trim();
        }

        SessionUser user = SessionUserUtil.getSessionUser();
        return Entity.create()
                .set("id", form.getId())
                .set("name", form.getPrisonName())
                .set("code", form.getId())
                .set("sname", form.getPrisonAbbreviation())
                .set("fname", form.getPrisonName())
                .set("scode", StrUtil.isNotBlank( form.getPrisonName()) ? PinyinUtil.getAllPinyin(form.getPrisonName()) : null)
                .set("jpcode", StrUtil.isNotBlank( form.getPrisonName()) ? PinyinUtil.getSimplePinyin(form.getPrisonName()) : null)
                .set("city_id", cityId)
                .set("city_name", cityName)
                .set("region_id", form.getDz())
                .set("region_name", regName)
                .set("parent_id", null)
                .set("address", form.getXz())
                .set("office_tel", form.getDh())
                .set("is_formal", 1)
                .set("is_disabled", 0)
                .set("order_id", null)
                .set("isdel", 0)
                .set("add_user", user.getIdCard())
                .set("add_time", new Date())
                .set("update_user", user.getIdCard())
                .set("update_time", new Date())
                .set("org_type", form.getPrisonType())
                .set("is_sync", 0)
                //.set("org_category", null)
                .set("unit_no", form.getId())
                .set("haszfq", 1)
                //.set("is_business_org", null)
                .set("bjdh", form.getDh())
                .set("email", form.getDzxx())
                .set("czdh", form.getCz());
                //.set("intro", null)
                //.set("orgpath", null)

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(BasePrisonInfoSaveForm form) {
         UacUserUtil.getDb();
        // 校验ID是否存在。
        OrgDO orgOld = orgDao.selectById(form.getId());
        Assert.notNull(orgOld, "传入机构编码异常，系统不存在");
        OrgDO orgDO = buildOrgDO(form);
        orgDao.updateById(orgDO);
        try {
            //所领导用户id Integer leaderId;
            Entity entity = buildEntity(form);
            entity.set("add_user", orgOld.getAddUser());
            entity.set("add_time", orgOld.getAddTime());
            UacUserUtil.getDb().update(entity, Entity.create("uac_org").set("id", form.getId()));
        } catch (SQLException e) {
            log.error("bsp-uac-org更新失败", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void delete(String[] ids) {
        orgDao.delete(new LambdaQueryWrapperX<OrgDO>().in(OrgDO::getId, Arrays.asList(ids)));
        try {
            UacUserUtil.getDb().update(
                    Entity.create().set("isdel", 1)
                            .set("update_time", new Date())
                            .set("update_user", SessionUserUtil.getSessionUser().getIdCard())
                            .set("update_time", new Date()),
                    Entity.create("uac_org").set("id", ids));
        } catch (Exception e) {
            log.error("删除异常", e);
            throw  new RuntimeException(e);
        }
    }


    /**
     * 构建org DO
     * <AUTHOR>
     * @date 2025/5/20 9:26
     * @param [form]
     * @return com.gosun.zhjg.prison.room.terminal.modules.prison.entity.OrgDO
     */
    private OrgDO buildOrgDO(BasePrisonInfoSaveForm form){
        OrgDO orgDO = new OrgDO();
        BeanUtils.copyProperties(form, orgDO);
        orgDO.setOrgName(form.getPrisonName());
        orgDO.setFname(form.getPrisonName());
        orgDO.setOrgCode(form.getId());
        orgDO.setSname(form.getPrisonAbbreviation());
        orgDO.setOrgType(form.getPrisonType());
        orgDO.setAddress(form.getXz());
        orgDO.setOfficeTel(form.getDh());
        orgDO.setCzdh(form.getCz());
        orgDO.setPostCode(form.getYzbm());
        orgDO.setEmail(form.getDzxx());

        if(StrUtil.isNotBlank(form.getPrisonName())){
            orgDO.setScode(PinyinUtil.getAllPinyin(form.getPrisonName()));
        }

        if(StrUtil.isNotBlank(form.getPrisonName())){
            orgDO.setJpcode(PinyinUtil.getSimplePinyin(form.getPrisonName()));
        }

        if(StrUtil.isNotBlank(form.getZbkssj())){
            orgDO.setZbkssj(LocalTime.parse(form.getZbkssj()));
        }

        if(StrUtil.isNotBlank(form.getZbjssj())){
            orgDO.setZbjssj(LocalTime.parse(form.getZbjssj()));
        }

        if(StrUtil.isNotBlank(form.getDz()) && form.getDz().length() > 4){
            String cityCode = form.getDz().substring(0, 4) + "00";
            orgDO.setCityCode(cityCode);
            orgDO.setRegCode(form.getDz());
        }

        if(StrUtil.isNotBlank(form.getDzName()) && form.getDzName().split("/").length == 3){
            orgDO.setRegName(form.getDzName().split("/")[2].trim());
            orgDO.setCityName(form.getDzName().split("/")[1].trim());
        }

        return orgDO;
    }

}
