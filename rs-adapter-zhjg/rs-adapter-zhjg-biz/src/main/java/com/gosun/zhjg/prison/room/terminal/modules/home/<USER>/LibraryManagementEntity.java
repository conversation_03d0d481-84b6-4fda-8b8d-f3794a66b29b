package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel("图书管理")
@TableName("library_management")
public class LibraryManagementEntity implements Serializable {

    //主键id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    //名字
    private String name;

    //作者
    private String author;

    //出版社
    private String press;

    //图书数量
    private Integer count=0;

    //图书数量
    private Integer stockCount=0;

    //封面
    private String cover;

    //简介
    private String profile;

    //状态
    private String status;

    //监所编号
    private String prisonId;

    //删除标记
    private Integer delFlag;

    //上架时间
    private Date createTime;

    //上架人
    private String createUser;

    //上架人id
    private String createUserId;

    //更新人
    private String updateUser;

    //更新时间
    private Date updateTime;

    //更新人id
    private String updateUserId;
}
