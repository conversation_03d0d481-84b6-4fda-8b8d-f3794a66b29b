package com.gosun.zhjg.prison.room.terminal.modules.terminal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dto.TerminalMenuPageDto;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dto.TerminalMenuSaveDto;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalMenuVO;

import java.util.List;


/**
 * 终端-仓内外屏菜单表
 *
 * <AUTHOR>
 * @date 2024/12/12 11:01
 */
public interface TerminalMenuService {

	List<TerminalMenuVO> findByPage(TerminalMenuPageDto form, Page page);

	TerminalMenuVO findOneById(String id);

	List<TerminalMenuVO> buildTerminalMenuTree(TerminalMenuPageDto form);

	void saveTerminalMenu(TerminalMenuSaveDto dto, String userid, String name);

	void saveCnpPoliceMenuPrisonLink(String prisonId, List<String> menuIdList, String userid, String name);

	void saveCnpPrisonerMenuPrisonLink(String prisonId, List<String> menuIdList, String userid, String name);

	void saveCwpMenuPrisonLink(String prisonId, List<String> menuIdList, String userid, String name);

	boolean checkPrisonHasMenuPermission(String prisonId, String menuCode);

	void checkRoomListHasMenuPermissionThrowException(List<String> roomIds, String menuCode);
}

