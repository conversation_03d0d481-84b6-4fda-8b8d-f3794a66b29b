package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 监室值日-记录（某天有某班次）
 *
 * <AUTHOR>
 * @date 2024/11/1 8:59
 */
@Data
@TableName("terminal_room_day_duty_records")
public class TerminalRoomDayDutyRecordsEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@TableId(type = IdType.ASSIGN_UUID)

	private String id;
	/**
	 * 单位代码
	 */
	private String prisonId;
	/**
	 * 监室号
	 */
	private String roomId;
	/**
	 * terminal_room_day_duty 表 id
	 */
	private String dutyId;
	/**
	 * 值班日期
	 */
	private Date dutyDate;
	/**
	 * 班次id
	 */
	private String shiftId;
	/**
	 * 班次名称
	 */
	private String shiftName;
	/**
	 * 班次版本号
	 */
	private Long shiftVersionNo;
	/**
	 * 排班人
	 */
	private String assignerUserId;
	/**
	 * 排班人、最后提交人
	 */
	private String assigner;
	/**
	 * 排班时间、最后提交时间
	 */
	private Date assignedTime;
}
