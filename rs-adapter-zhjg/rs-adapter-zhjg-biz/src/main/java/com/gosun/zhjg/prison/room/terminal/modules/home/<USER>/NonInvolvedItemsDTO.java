package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * non_involved_items 实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date: 2024-06-07
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value = "NonInvolvedItemsDTO对象", description = "非涉案物品")
public class  NonInvolvedItemsDTO implements Serializable {

    @JsonProperty("prisonerId")
    @NotBlank(message = "人员编号 不能为空")
    @ApiModelProperty(value = "人员编号",required = true)
    private String prisonerId;


    @JsonProperty("itemName")
    @ApiModelProperty(value = "物品名称",required = true)
    @NotBlank(message = "物品名称 不能为空")
    private String itemName;


    @JsonProperty("itemSourceCode")
    @ApiModelProperty(value = "物品来源code")
    private String itemSourceCode;


    @JsonProperty("storageLocation")
    @ApiModelProperty(value = "存放位置",required = true)
    @NotBlank(message = "存放位置 不能为空")
    private String storageLocation;


    @JsonProperty("depositTime")
    @ApiModelProperty(value = "存入时间",required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "存入时间 不能为空")
    private Date depositTime;

    @JsonProperty("manager")
    @ApiModelProperty(value = "操作人")
    private String manager;


    @JsonProperty("remarks")
    @ApiModelProperty(value = "备注")
    private String remarks;


    @JsonProperty("photoUrl")
    @ApiModelProperty(value = "照片url",required = true)
    @NotBlank(message = "照片url 不能为空")
    private String photoUrl;


    @JsonProperty("photoName")
    @ApiModelProperty(value = "照片名字",required = true)
    private String photoName;



}

