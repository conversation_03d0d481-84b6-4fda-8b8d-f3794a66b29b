package com.gosun.zhjg.prison.room.terminal.modules.home.enums;

import lombok.Getter;

/**
 * 申请预约
 * <AUTHOR>
 * @date 2025/3/7 17:20
 */
@Getter
public enum ReserveApplyEnum {

    //1：巡诊预约 2：会见申请 3：谈话申请-反映监视动态 4：法律救助申请

    ROUNDS_APPOINTMENT(1,"巡诊预约"),
    APPLICATION_MEETING(2,"会见申请"),
    TALK__APPLY(3,"反映监视动态"),//谈话申请
    LEGAL_AID_APPLICATION(4,"法律救助申请");

    private final Integer status;
    private final String name;

    ReserveApplyEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }
}
