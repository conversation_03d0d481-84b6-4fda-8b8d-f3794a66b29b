package com.gosun.zhjg.prison.room.terminal.modules.app.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gosun.zhjg.prison.app.modules.yjlf.vo.PrisonSendReceiveMessageVO;
import com.gosun.zhjg.prison.room.terminal.modules.app.entity.PrisonSendReceiveMessageEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
*<AUTHOR>
*@email <EMAIL>
*@date 2020-08-05
*/


@Mapper
public interface PrisonEscortMessageDao extends BaseMapper<PrisonSendReceiveMessageEntity> {
   Integer getMaxOrder();

    /***
     * 获取收发信息
     * @param id
     * @return
     */
    List<PrisonSendReceiveMessageVO> messageList(@Param("id") String id);
}
