package com.gosun.zhjg.prison.room.terminal.common;

import lombok.Getter;

/**
 * 仓外屏监室动态类型
*<AUTHOR>
*@email <EMAIL>
*@date 2022/4/24
*/

@Getter
public enum NoticeTypeEnum {
    /**
     * 带入带出
     */
    dr("0", "带入"),
    /**
     * 带出
     */
    dc("1", "带出"),

    /**
     * 家属会见
     */
    jshj("2", "家属会见"),

    /**
     * 律师会见
     */
    lshj("3", "律师会见"),

    /**
     * 提讯
     */
    tx("4", "提讯"),
    /**
     * 提解
     */
    tj("5", "提解"),

    /**
     * 管教留言
     */
    gjly("6", "管教留言");



    private final String code;
    private final String msg;

    NoticeTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getValue(String code) {
        for (NoticeTypeEnum ele : values()) {
            if(ele.getCode().equals(code))
                return ele.getMsg();
        }
        return null;
    }
}
