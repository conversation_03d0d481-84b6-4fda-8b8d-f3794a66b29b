package com.gosun.zhjg.basic.business.modules.file.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gosun.zhjg.basic.business.modules.file.vo.BaseFileVo;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.TerminalVersionManagementsDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.feign.TerminalVersionManagementApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Api(tags = "终端版本管理及版本升级运维相关功能")
@RequestMapping("file/terminalversionmanagement")
@RestController
public class TerminalVersionManagementFileController {
    @Autowired
    private TerminalVersionManagementApi terminalVersionManagementApi;
    @Autowired
    private FileStorageService fileStorageService;

    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    @ApiOperation(value = "上传文件")
    public R<?> upload(@RequestParam("file") MultipartFile file
            , @RequestParam(value = "releaseNotes", required = false) String releaseNotes
            , @RequestParam(value = "competingVersions", required = false) String competingVersions
        ) {
        String fileName = file.getOriginalFilename();
        TerminalVersionManagementsDto form = new TerminalVersionManagementsDto();
        form.setPackageName(fileName);
        // 检测包名是否正确
        R<?> r = terminalVersionManagementApi.checkPackageName(form);
        if (!Integer.valueOf(200).equals(r.getStatus())) {
            return r;
        }
        if (r.getData() == null) {
            return R.ResponseError("服务异常");
        }
        // 文件上传
        FileInfo upload = fileStorageService.of(file)
                .setHashCalculatorMd5()
                .setSaveFilename(file.getOriginalFilename()) //设置保存的文件名，不需要可以不写，会随机生成
                .upload();//将文件上传到对应地方
        form.setPackageUrl(upload.getUrl());
        form.setReleaseNotes(releaseNotes);
        form.setCompetingVersions(competingVersions);
        r = terminalVersionManagementApi.saveByPackageName(form);
        return r;
    }
}
