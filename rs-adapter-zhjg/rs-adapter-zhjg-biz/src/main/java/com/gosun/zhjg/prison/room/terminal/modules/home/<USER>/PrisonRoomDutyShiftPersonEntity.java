package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 监室值班班次人员表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-06-03 09:00:15
 */
@Data
@TableName("prison_room_duty_shift_person")
public class PrisonRoomDutyShiftPersonEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @TableId(type = IdType.ASSIGN_UUID)

    private String id;
    /**
     * 所属班次
     */
    private String shiftId;
    /**
     * 人员编号
     */
    private String prisonerId;
    /**
     * 人员名称
     */
    private String prisonerName;
    /**
     * 最后更新人ID
     */
    private String updateUserId;
    /**
     * 最后更新人
     */
    private String updateUserName;
    /**
     * 最后更新时间
     */
    private Date updateTime;
    /**
     * 删除标记
     */
    @TableLogic
    private Integer delFlag;
    /**
     * 签到情况
     */
    private String mettingStatus;
    /**
     * 照片
     */
    private String photo;
}
