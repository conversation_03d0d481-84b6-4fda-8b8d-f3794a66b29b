package com.gosun.zhjg.prison.room.terminal.modules.home.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("监室内务生活秩序安排人员关联")
public class TerminalRoomInteriorLivePrisoner {

    /**
     * 主键
     */
    @TableId(type = IdType.UUID)
    @ApiModelProperty("主键id")
    private String id;

    /**
     * 监室内务生活安排id
     */
    @ApiModelProperty("监室内务生活安排id")
    private String interiorLiveId;

    /**
     * 在押人员编号
     */
    @ApiModelProperty("在押人员编号")
    private String prisonerId;

    /**
     * 列标题
     */
    @ApiModelProperty("列标题")
    private String cellTitle;

    /**
     * 列值
     */
    @ApiModelProperty("列值")
    private String cellValue;

    /**
     * 列号
     */
    @ApiModelProperty("列号")
    private Integer cellIndex;

    /**
     * 行号
     */
    @ApiModelProperty("行号")
    private Integer rowIndex;
}
