package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * rights_obligations 实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date: 2024-06-18
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value = "RightsObligationsDTO对象", description = "监室智能终端-权利义务")
public class RightsObligationsDTO implements Serializable {

    @JsonProperty("prisonerId")
    @ApiModelProperty(value = "人员编号 多个逗号隔开")
    @NotBlank(message = "人员编号 不能为空")
    private String prisonerId;


}

