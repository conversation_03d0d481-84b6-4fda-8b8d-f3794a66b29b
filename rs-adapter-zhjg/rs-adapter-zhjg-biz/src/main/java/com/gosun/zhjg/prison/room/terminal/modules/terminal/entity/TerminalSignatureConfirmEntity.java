package com.gosun.zhjg.prison.room.terminal.modules.terminal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 终端签名确认
 *
 * <AUTHOR>
 * @date 2024/4/12 11:20
 */
@Data
@TableName("terminal_signature_confirm")
public class TerminalSignatureConfirmEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * $column.comments
	 */
	@TableId(type = IdType.ASSIGN_UUID)

	private String id;
	/**
	 * 单位代码
	 */
	private String prisonId;
	/**
	 * 下发签名给人员编号
	 */
	private String prisonerId;
	/**
	 * 人员姓名
	 */
	private String prisonerName;
	/**
	 * 业务类型 healthCheck 入所健康检、
	 */
	private String businessType;
	/**
	 * 关联业务主键
	 */
	private String linkId;
	/**
	 * 是否已签名 1已签名、0未
	 */
	private Integer state;
	/**
	 * 确认时间
	 */
	private Date confirmTime;
	/**
	 * 签名图片地址
	 */
	private String signatureUrl;
	/**
	 * 人员签名后平台第一次查阅或收到通知时间
	 */
	private Date signatureReviewedAt;
	/**
	 * $column.comments
	 */
	private Date createTime;
}
