package com.gosun.zhjg.prison.room.terminal.modules.home.controller;

import com.gosun.zhjg.common.config.UserContext;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.prison.room.terminal.modules.home.dto.BaseSystemRuleConfigKeyValueDto;
import com.gosun.zhjg.prison.room.terminal.modules.home.dto.BaseSystemRuleConfigQueryDto;
import com.gosun.zhjg.prison.room.terminal.modules.home.service.BaseSystemRuleConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@Api(tags = "基础系统配置")
@RestController
@RequestMapping("home/basesystemruleconfig")
public class BaseSystemRuleConfigController {

	@Autowired
	private BaseSystemRuleConfigService baseSystemRuleConfigService;

	@GetMapping("/getValue")
	@ApiOperation("获取值")
	public R<?> getValue(@Validated BaseSystemRuleConfigQueryDto form) {
		String prisonId = form.getPrisonId();
		String key = form.getKey();
		if (StringUtils.isBlank(prisonId)) {
			prisonId = UserContext.getJwtInfo().getPrisonId();
		}
		String value = baseSystemRuleConfigService.getValue(prisonId, key);
		BaseSystemRuleConfigKeyValueDto data = new BaseSystemRuleConfigKeyValueDto(key, value);
		return R.ResponseResult(data);
	}

	@GetMapping("/getMultiItemValue")
	@ApiOperation("获取多项的值")
	public R<?> getMultiItemValue(@Validated BaseSystemRuleConfigQueryDto form) {
		String prisonId = form.getPrisonId();
		List<String> keyList = Arrays.asList(org.springframework.util.StringUtils.delimitedListToStringArray(form.getKey(), ","));
		if (StringUtils.isBlank(prisonId)) {
			prisonId = UserContext.getJwtInfo().getPrisonId();
		}
		List<BaseSystemRuleConfigKeyValueDto> multiItemValue = baseSystemRuleConfigService.getMultiItemValue(prisonId, keyList);
		return R.ResponseResult(multiItemValue);
	}
}
