package com.gosun.zhjg.prison.room.terminal.modules.home.vo;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gosun.zhjg.common.context.PageSortMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
* PrisonOnlineDiseaseVO 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2024-04-12
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="PrisonOnlineDiseaseVO对象", description="仓内屏-在线报病")
public class PrisonOnlineDiseaseVO implements Serializable {


    @JsonProperty("id")
    @ApiModelProperty(value = "主键")
    private String id;

    @JsonProperty("prisonId")
    @ApiModelProperty(value = "监所编号")
    private String prisonId;

    @JsonProperty("prisonerId")
    @ApiModelProperty(value = "在押人员编号")
    private String prisonerId;

    @JsonProperty("prisonerName")
    @ApiModelProperty(value = "在押人员姓名")
    private String prisonerName;

    @JsonProperty("roomId")
    @ApiModelProperty(value = "监室号")
    private String roomId;

    @JsonProperty("roomName")
    @PageSortMapping(prefix = "b.")
    @ApiModelProperty(value = "监室名字")
    private String roomName;

    @JsonProperty("createTime")
    @ApiModelProperty(value = "预约时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonProperty("diseaseTypeId")
    @ApiModelProperty(value = "疾病类型id 多个逗号隔开")
    private String diseaseTypeId;

    @JsonProperty("diseaseTypeDisplayName")
    @ApiModelProperty(value = "疾病类型 中文")
    private String diseaseTypeDisplayName;

    @JsonProperty("diseaseSymptomId")
    @ApiModelProperty(value = "疾病症状id，多个逗号隔开")
    private String diseaseSymptomId;

    @JsonProperty("diseaseSymptomIdDisplayName")
    @ApiModelProperty(value = "疾病症状 中文")
    private String diseaseSymptomIdDisplayName;

    @JsonProperty("createUser")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    @JsonProperty("createUserName")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

}
