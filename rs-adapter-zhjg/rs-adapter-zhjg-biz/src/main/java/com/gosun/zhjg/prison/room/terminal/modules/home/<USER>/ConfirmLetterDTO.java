package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel("收信确认")
public class ConfirmLetterDTO {
    @ApiModelProperty(value = "收信编号",required = true)
    @NotBlank(message = "收信编号 不能为空")
    private String id;

    @ApiModelProperty(value = "收信签名",required = true)
    @NotBlank(message = "收信签名 不能为空")
    private String signUrl;

}
