package com.gosun.zhjg.prison.room.terminal.common.utils;

import org.springframework.beans.BeanUtils;

/**
 * <p>
 * Bean工具类
 * </p>
 *
 * <AUTHOR>
 * @date 2021/8/31 10:37:30
 */
public class BeanUtilTool {
    private BeanUtilTool() {
    }

    /**
     * 复制对象
     *
     * @param source 源 要复制的对象
     * @param target 目标 复制到此对象
     * @param <T>    泛型
     * @return 新的对象
     */
    public static <T> T copy(Object source, Class<T> target) {
        if (source == null || target == null) {
            return null;
        }
        try {
            T newInstance = target.newInstance();
            BeanUtils.copyProperties(source, newInstance);
            return newInstance;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
