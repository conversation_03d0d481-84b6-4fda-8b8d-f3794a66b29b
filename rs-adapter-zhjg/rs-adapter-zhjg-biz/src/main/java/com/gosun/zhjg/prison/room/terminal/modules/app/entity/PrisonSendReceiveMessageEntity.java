package com.gosun.zhjg.prison.room.terminal.modules.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020-08-05
 */

@Data
@TableName( value ="prison_send_receive_message" )
public class PrisonSendReceiveMessageEntity implements Serializable {


	private static final long serialVersionUID = -2534410887893252835L;
	/**
	 * 主键
	 */
	@TableId(type = IdType.ASSIGN_UUID)

	private String id;

	/**
	 * 押解联防编号
	 */
   	@ApiModelProperty(value = "押解联防编号" )
	private String defenseId;


	/**
	 * 发送消息内容
	 */
   	@ApiModelProperty(value = "发送消息内容" )
	private String sendContent;

	/**
	 * 发送消息时间
	 */
   	@ApiModelProperty(value = "发送消息时间" )
	private Date sendTime;

	/**
	 * 发送消息名字
	 */
   	@ApiModelProperty(value = "发送消息名字" )
	private String sendName;

	/**
	 * 删除标志
	 */
   	@ApiModelProperty(value = "删除标志" )
	private Integer delFlag;

   	@ApiModelProperty("排序标志")
   	private int orderCode;

   	@ApiModelProperty("图片")
   	private String photo;

}
