package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * room_represent-监室点名表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-06-30 09:29:58
 */
@Data
@TableName("room_represent")
public class RoomRepresentEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @TableId(type = IdType.ASSIGN_UUID)

    private String id;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 监室name
     */
    private String roomName;
    /**
     * 监室总关押数
     */
    private Integer allInNum;
    /**
     * 监室在押数
     */
    private Integer inNum;
    /**
     * 监室外出数
     */
    private Integer outNum;
    /**
     * 点名时间起
     */
    private Date startTime;
    /**
     * 点名时间止
     */
    private Date endTime;
    /**
     * 点名状态
     */
    private String presentStatus;
    /**
     * 已点人数
     */
    private Integer presentNum;
    /**
     * 异常人数
     */
    private Integer errorNum;
    /**
     * 异常处置结果
     */
    private String errorHandleResult;
    /**
     * 发起人
     */
    private String operatePeopleId;
    /**
     * 发起人名字
     */
    private String operatePeopleName;
    /**
     * 发起时间
     */
    private Date operateTime;
    /**
     * 监区id
     */
    private String areaId;
    /**
     * 监区name
     */
    private String areaName;
    private String prisonId;

    /***
     * 点名批号
     */
    private String presentNo;

    /***
     * 点名类型
     */
    private Integer presentType;

    /***
     * 有效期
     */
    private Integer expiryDate;

    /***
     * 1-视频点名
     */
    private Integer isVideo;

    /***
     * 点名测温正常人数
     */
    private Integer temperatureNum = 0;

    /***
     * 点名测温异常人数
     */
    private Integer temperatureErrNum = 0;

    /***
     * 报备人数
     */
    private Integer reportNum;
}
