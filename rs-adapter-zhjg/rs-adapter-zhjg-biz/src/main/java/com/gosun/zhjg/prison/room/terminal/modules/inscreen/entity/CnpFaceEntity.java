package com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 仓内外屏人脸管理
 *
 * <AUTHOR>
 * @date 2023/2/23 13:58
 */
@Data
@TableName("acp_pm_cnp_face")
public class CnpFaceEntity extends BaseDO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 人员类型 1警员、2在押人员
     */
    private Integer personnelType;
    /**
     * 人员编号、警员id
     */
    private String personnelCode;
    /**
     * 图片地址
     */
    private String photo;
    /**
     * 失败描述
     */
    private String msg;

}
