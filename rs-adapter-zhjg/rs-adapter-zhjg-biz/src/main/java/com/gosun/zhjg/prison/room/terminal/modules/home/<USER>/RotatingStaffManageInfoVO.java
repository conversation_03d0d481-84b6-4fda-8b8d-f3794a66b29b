package com.gosun.zhjg.prison.room.terminal.modules.home.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.gosun.zhjg.prison.room.terminal.modules.home.dto.RotatingStaffManageDateDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class RotatingStaffManageInfoVO {
    private String id;

    @JsonProperty("roomId")
    @ApiModelProperty(value = "监室号",required = true)
    @NotBlank(message = "监室号 不能为空")
    private String roomId;

    @ApiModelProperty(value = "监室名字")
    private String roomName;

    @JsonProperty(value = "operatePerson")
    @ApiModelProperty(value = "操作人",required = true)
    @NotBlank(message = "操作人 不能为空")
    private String operatePerson;

    @JsonProperty(value = "operateTime")
    @ApiModelProperty(value = "操作时间",required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "操作时间 不能为空")
    private Date operateTime;

    @JsonProperty(value = "constructionReason")
    @ApiModelProperty(value = "布建理由",required = true)
    @NotBlank(message = "布建理由 不能为空")
    private String constructionReason;

    @JsonProperty(value = "taskDesc")
    @ApiModelProperty(value = "任务描述",required = true)
    @NotBlank(message = "任务描述 不能为空")
    private String taskDesc;

    @JsonProperty("updateTime")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @JsonProperty("updateUserId")
    @ApiModelProperty(value = "更新用户编号")
    private String updateUserId;

    @JsonProperty("updateUserName")
    @ApiModelProperty(value = "更新人")
    private String updateUserName;

    @JsonProperty("status")
    @ApiModelProperty(value = "0-待审核 1-审批通过 2-审批不通过")
    private Integer status;

    @JsonProperty("groupOpinion")
    @ApiModelProperty(value = "科组长审核意见")
    private Integer groupOpinion;

    @JsonProperty("groupPerson")
    @ApiModelProperty(value = "审核人")
    private String groupPerson;

    @JsonProperty("groupTime")
    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date groupTime;

    @JsonProperty("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("科组长意见")
    private String groupOpinionDisplayName;


    @JsonProperty("dateDTOS")
    @NotEmpty(message = "轮值员安排集合 不能为空")
    @ApiModelProperty(value = "轮值员安排集合",required = true)
    private List<RotatingStaffManageDateDTO> dateDTOS;
}
