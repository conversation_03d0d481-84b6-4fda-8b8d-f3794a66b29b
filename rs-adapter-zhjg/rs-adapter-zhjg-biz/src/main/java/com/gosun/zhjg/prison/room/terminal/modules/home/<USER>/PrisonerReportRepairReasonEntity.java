package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Builder;
import java.util.Date;

/**
 * @Title prisoner_report_repair_reason 
 * @Description  
 * <AUTHOR> 
 * @Date 2023-08-25 
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName( "prisoner_report_repair_reason" )
public class PrisonerReportRepairReasonEntity  implements Serializable {

	private static final long serialVersionUID =  5196036968851697180L;

   	@TableField("id")
	@ApiModelProperty("主键")
	@TableId(value = "id", type = IdType.UUID)
	private String id;

   	@TableField("content_id")
	@ApiModelProperty("报修内容id")
	private String contentId;

   	@TableField("seq")
	@ApiModelProperty("序号")
	private Integer seq;

   	@TableField("reason_name")
	@ApiModelProperty("报修原因")
	private String reasonName;

   	@TableField("create_time")
	@ApiModelProperty("创建时间")
	private Date createTime;

}
