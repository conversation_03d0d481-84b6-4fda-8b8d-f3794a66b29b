package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * prison_room_test_options-测评选项表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-07 17:30:04
 */
@Data
@TableName("prison_room_test_options")
public class PrisonRoomTestOptionsEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @TableId(type = IdType.ASSIGN_UUID)

    private String id;
    /**
     * 选项名称
     */
    private String name;
    /**
     * 选项标识
     */
    private String identify;
    /**
     * 标准一分值
     */
    private BigDecimal standardOneScore;
    /**
     * 标准二分值
     */
    private BigDecimal standardTwoScore;
    /**
     * 试卷id
     */
    private String testId;
    /**
     * 排序标识
     */
    private Integer sort;
}
