package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * terminal_goods_problem-问题反馈表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-08-03 20:24:17
 */
@Data
@TableName("terminal_goods_problem")
public class TerminalGoodsProblemEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_UUID)

    private String id;
    /**
     * 反馈人id
     */
    private String operateUserId;
    /**
     * 反馈人姓名
     */
    private String operateUserName;
    /**
     * 预定时间
     */
    private Date reserveTime;
    /**
     * 预定金额（元）
     */
    private BigDecimal reserveMoney;
    /**
     * 预定商品
     */
    private String reserveGoods;
    /**
     * 反馈问题
     */
    private String problem;
    /**
     * 反馈时间
     */
    private Date feedbackTime;
    /**
     * 审核类型（0：待审核，1：已审核）
     */
    private String auditType;


    private Date auditTime;

    private String auditUser;

    private String auditUserId;

    private String prisonId;
}
