package com.gosun.zhjg.prison.room.terminal.modules.socket.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.common.constant.TopicConstants;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.common.msg.ResultVO;
import com.gosun.zhjg.common.util.FileUrlUtils;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dao.CnpSocketDao;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.DeviceInitializeDto;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushVoiceBroadcastForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.*;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.impl.CnpServiceImpl;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.DeviceInscreenVO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.PushMessageAckVO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.SocketRelationMapVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "与仓内屏相关交互")
@Slf4j
@RestController
@RequestMapping("cnp/socket")
public class CnpSocketController {

    @Autowired
    private CnpSocketDao socketDao;
    @Autowired
    private SocketService socketService;
    @Autowired
    private DeviceInitializeQueue deviceInitializeQueue;

    @Autowired
    private CnpServiceImpl cnpService;
    @Value("${file-url-prefix}")
    private String pathPrefix;

    @ApiOperation(value = "获取当前建立Socket的连接")
    @GetMapping("getCurrentConnectedRoom")
    public R<?> getCurrentConnectedRoom() {
        List<JSONObject> list = new ArrayList<>();
        SocketRelationMapVO socketInfo = socketService.getSocketInfo();
        List<String> currentTopicClient = socketInfo.getClientTopicMap()
                .entrySet()
                .stream()
                .filter(f -> f.getValue().equals(TopicConstants.TOPIC_KEEP_ALIVE))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        List<String> collect = socketInfo.getRoomMap()
                .entrySet()
                .stream()
                .filter(f -> currentTopicClient.contains(f.getKey()))
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());

        List<JSONObject> allRoom = socketDao.getAllRoom();
        for (String roomId : collect) {
            JSONObject obj = new JSONObject();
            obj.put("roomId", roomId);
            JSONObject room = allRoom.stream().filter(f -> roomId != null && roomId.equals(f.getString("id"))).findFirst().orElse(new JSONObject());
            obj.put("roomName", room.getString("room_name"));
            list.add(obj);
        }
        JSONObject resp = new JSONObject();
        resp.put("size", list.size());
        resp.put("list", list);
        return R.ResponseResult(resp);
    }

    @Deprecated
    @ApiOperation(value = "获取当前没有建立Socket的连接的监室")
    @GetMapping("getCurrentNotConnectedRoom")
    public R<?> getCurrentNotConnectedRoom() {
        List<JSONObject> list = new ArrayList<>();
        SocketRelationMapVO socketInfo = socketService.getSocketInfo();
        List<String> currentTopicClient = socketInfo.getClientTopicMap()
                .entrySet()
                .stream()
                .filter(f -> f.getValue().equals(TopicConstants.TOPIC_KEEP_ALIVE))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        List<String> collect = socketInfo.getRoomMap()
                .entrySet()
                .stream()
                .filter(f -> currentTopicClient.contains(f.getKey()))
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());
        List<JSONObject> allRoom = socketDao.getAllRoom();
        List<DeviceInscreenVO> allDeviceInscreen = socketDao.getAllDeviceInscreen();
        for (JSONObject roomInfo : allRoom) {
            String roomId = roomInfo.getString("id");
            if (collect.contains(roomId)) {
                continue;
            }
            list.add(roomInfo);
            // 带出IP，方便重启对设备重启操作
            DeviceInscreenVO room = allDeviceInscreen.stream().filter(f -> roomId != null && roomId.equals(f.getRoomId())).findFirst().orElse(new DeviceInscreenVO());
            roomInfo.put("deviceIp", room.getDeviceIp());
        }
        JSONObject resp = new JSONObject();
        resp.put("size", list.size());
        resp.put("list", list);
        return R.ResponseResult(resp);
    }

    @ApiOperation(value = "重启初始化")
    @PostMapping("device/init")
    public R<?> deviceInitialize(@RequestBody DeviceInitializeDto form, HttpServletRequest request) {
//        form.setDeviceIp(ClientUtil.getClientIp(request));
        deviceInitializeQueue.addQueue(form);
        log.info("deviceInitialize: {}", JSON.toJSONString(form));
        return R.ResponseResult(true);
    }

    @ApiOperation(value = "重启初始化-队列剩余")
    @GetMapping("device/initQueue")
    public R<?> initializeQueue() {
        List<DeviceInitializeDto> queue = deviceInitializeQueue.queue();
        JSONObject resp = new JSONObject();
        resp.put("size", queue.size());
        resp.put("list", queue);
        return R.ResponseResult(resp);
    }

    @ApiOperation(value = "重启初始化-清空队列")
    @GetMapping("device/cleanQueue")
    public R<?> cleanQueue() {
        deviceInitializeQueue.cleanQueue();
        return R.ResponseOk();
    }

    @ApiOperation(value = "获取当前上下文储存roomMap")
    @GetMapping("context/roomMap")
    public R<?> contextRoomMap() {
        SocketRelationMapVO socketInfo = socketService.getSocketInfo();
        Map<String, String> roomMap = socketInfo.getRoomMap();
        return R.ResponseResult(roomMap);
    }

    @ApiOperation(value = "获取当前上下文储存clientMap")
    @GetMapping("context/clientMap")
    public R<?> contextClientMap() {
        SocketRelationMapVO socketInfo = socketService.getSocketInfo();
        Set<String> sessionId = socketInfo.getClientIds();
        return R.ResponseResult(sessionId);
    }

    @ApiOperation(value = "获取当前上下文储存clientTopicMap")
    @GetMapping("context/clientTopicMap")
    public R<?> contextClientTopicMap() {
        SocketRelationMapVO socketInfo = socketService.getSocketInfo();
        Map<String, String> r = socketInfo.getClientTopicMap();
        return R.ResponseResult(r);
    }


    /**
     * 获取温度
     *
     * @param roomId
     * @param serialNumber 设备序列号，可选
     * @param maxWaitTime  等待时间
     * @return map -> value
     */
    @ApiOperation(value = "获取温度", notes = "根据监室号或者序列号，返回第一个")
    @GetMapping("getTemperature")
    public ResultVO<?> getTemperature(@RequestParam(value = "roomId", required = false) String roomId, @RequestParam(value = "serialNumber", required = false) String serialNumber, @RequestParam(value = "maxWaitTime", required = false) Long maxWaitTime) {
        PushMessageForm form = new PushMessageForm();
        form.setAction(SocketActionConstants.getTemperature);
        form.setParams(new Object[]{null, null, maxWaitTime});
        form.setTarget(SocketActionConstants.PushMessageTargetEnum.android.name());
        form.setTerminal(SocketActionConstants.PushMessageTerminalEnum.CNP.name());

        List<PushMessageAckVO> pushMessageAckVOS = null;
        if (StringUtils.isNotBlank(roomId)) {
            form.setRoomIds(Lists.newArrayList(roomId));
            pushMessageAckVOS = socketService.pushMessageToRoomWaitReplyMultiple(form);
        } else if (StringUtils.isNotBlank(serialNumber)) {
            form.setSerialNumbers(Lists.newArrayList(serialNumber));
            pushMessageAckVOS = socketService.pushMessageToSerialNumberWaitReplyMultiple(form);
        } else {
            return ResultVO.error("参数错误", null);
        }

        log.debug("form={}", JSON.toJSONString(form));
        if (pushMessageAckVOS != null && pushMessageAckVOS.size() > 0) {
            PushMessageAckVO v = null;
            for (PushMessageAckVO t : pushMessageAckVOS) {
                if (t.getOk()) {
                    JSONArray r = (JSONArray) t.getResponse();
                    if (r != null && r.size() > 0) {
                        HashMap<String, Object> hashMap = new HashMap<String, Object>() {
                            {
                                put("value", Float.parseFloat(r.get(0).toString()));
                            }
                        };
                        return ResultVO.success(hashMap);
                    }
                }
                v = t;
            }
            if (v != null) {
                return ResultVO.error(v.getResponse() + "", null);
            }
        }
        log.debug(JSON.toJSONString(pushMessageAckVOS));
        return ResultVO.error();
    }


    @ApiOperation("人脸操作-录入人脸照片到监室")
    @PostMapping("chackAndEnrollFaceByImgByRoomId")
    public R<?> chackAndEnrollFaceByImgByRoomId(String rybhAppCode, String photo, String roomId, String terminal) {
        if (!photo.startsWith("http")) {
            photo = FileUrlUtils.concatUrl(photo, pathPrefix);
        }
        List<String> photoUrlReference = Lists.newArrayList(photo);
        cnpService.enrollFaceByImgByRoomId(rybhAppCode, photoUrlReference, roomId, terminal, true);
        return R.ResponseOk();
    }


    @ApiOperation(value = "据序列号删除设备内人员照片，同时清除cnp_face表")
    @PostMapping("cleanStoreFaceBySerialNumber")
    public R<?> cleanStoreFaceBySerialNumber(String rybhAppCode, String serialNumber) {
        boolean sign = cnpService.cleanStoreFaceBySerialNumber(rybhAppCode, serialNumber);
        return R.ResponseResult(sign);
    }


    @ApiOperation(value = "根据监室号删除设备内人员照片(需要指定是内屏 外屏)，同时清除cnp_face表")
    @PostMapping("cleanStoreFaceByRoomId")
    public R<?> cleanStoreFaceByRoomId(String rybhAppCode, String roomId, String terminal) {
        boolean sign = cnpService.cleanStoreFaceByRoomId(rybhAppCode, roomId, terminal);
        return R.ResponseResult(sign);
    }

    @ApiOperation(value = "根据监室号获取仓内屏序列号")
    @GetMapping("getCnpSerialNumberByRoomId")
    public R<?> getCnpSerialNumberByRoomId(@RequestParam(value = "roomIds", required = false) List<String> roomIds) {
        List<String> serialNumbers = socketDao.getCnpSerialNumberByRoomId(roomIds);
        return R.ResponseResult(serialNumbers);
    }

    @ApiOperation(value = "根据监室号获取仓外屏序列号")
    @GetMapping("getCwpSerialNumberByRoomId")
    public R<?> getCwpSerialNumberByRoomId(@RequestParam(value = "roomIds", required = false) List<String> roomIds) {
        List<String> serialNumbers = socketDao.getCwpSerialNumberByRoomId(roomIds);
        return R.ResponseResult(serialNumbers);
    }

    @ApiOperation(value = "根据监室号获取仓内屏和仓外屏序列号")
    @GetMapping("getCnpCwpSerialNumberByRooomId")
    public R<?> getCnpCwpSerialNumberByRooomId(@RequestParam(value = "roomIds", required = false) List<String> roomIds) {
        List<String> serialNumbers = socketDao.getCnpCwpSerialNumberByRooomId(roomIds);
        return R.ResponseResult(serialNumbers);
    }


    @PostMapping("testPrisonerChange")
    @ApiOperation(value = "测试人员变动推送", notes = "{\"rybh\":\"4401001112205315491\",\"oldRoomId\":\"\",\"updateType\":\"INSERT\",\"sendTime\":1669085842230,\"oldRyzt\":\"10\"}")
    public R<?> testPrisonerChange(@RequestBody String data) {
        cnpService.prisonerChange(data);
        return R.ResponseOk();
    }

    @PostMapping("testRoomChange")
    @ApiOperation(value = "测试监室变动推送")
    public R<?> testRoomChange(@RequestBody List<String> roomList) {
        cnpService.roomChange(roomList);
        return R.ResponseOk();
    }

    @PostMapping("testZpChange")
    @ApiOperation(value = "测试照片变动推送", notes = "{\"updateType\":\"INSERT\",\"rybh\":\"440400011111545654\",\"zpxh\":\"0\",\"sendTime\":1673512221688}")
    public R<?> testZpChange(@RequestBody String data) {
        cnpService.zpChange(data);
        return R.ResponseOk();
    }

    @PostMapping("deletePersonnelPhoto")
    @ApiOperation(value = "照片-清理照片")
    public R<?> deletePersonnelPhoto(String prisonerId, @RequestParam(required = false) String oldRoomId) {
        cnpService.deletePersonnelPhoto(prisonerId, oldRoomId);
        return R.ResponseOk();
    }

    @PostMapping("sendPersonnelPhoto")
    @ApiOperation(value = "照片-下发照片")
    public R<?> sendPersonnelPhoto(String prisonerId) {
        cnpService.sendPersonnelPhoto(prisonerId);
        return R.ResponseOk();
    }

    @Autowired
    private TerminalOnlineStateJob terminalOnlineStateJob;

    @ApiOperation("同步仓内外屏在线状态到设备表")
    @GetMapping("syncTerminalOnlineState")
    public R<?> syncTerminalOnlineState() {
        terminalOnlineStateJob.job();
        return R.ResponseOk();
    }

    @ApiOperation(value = "测试-清理所有设备中所有人脸照片-谨慎调用")
    @PostMapping("testCleanAllFace")
    public R<?> cleanAllFace(@RequestBody(required = false) PushMessageForm form) {
        if (form == null) {
            form = new PushMessageForm();
        }
        SocketRelationMapVO socketInfo = socketService.getSocketInfo();
        Map<String, String> serialNumberMap = socketInfo.getSerialNumberMap();
        List<String> serialNumberList = new ArrayList<>(serialNumberMap.values());
        if (serialNumberList.isEmpty()) {
            return R.ResponseError("无在线设备");
        }
        form.params("ALL");
        form.setAction("cleanStoreFace");
        form.setSerialNumbers(serialNumberList);
        form.setTarget("android");
        List<PushMessageAckVO> ackMultiple = socketService.pushMessageToSerialNumberWaitReplyMultiple(form);
        Map<String, Object> voMap = new HashMap<>();
        List<PushMessageAckVO> okAckCollect = ackMultiple.stream().filter(f -> f.getOk() != null && f.getOk()).collect(Collectors.toList());
        voMap.put("okCount", okAckCollect.size());
        List<String> okSerialNumber = okAckCollect.stream()
                .map(m -> serialNumberMap.get(m.getSessionId()))
                .collect(Collectors.toList());
        voMap.put("okSerialNumber", okSerialNumber);
        List<PushMessageAckVO> errAckCollect = ackMultiple.stream().filter(f -> f.getOk() == null || !f.getOk()).collect(Collectors.toList());
        voMap.put("errCount", errAckCollect.size());
        List<String> errSerialNumber = errAckCollect.stream()
                .map(m -> serialNumberMap.get(m.getSessionId()))
                .collect(Collectors.toList());
        voMap.put("errSerialNumber", errSerialNumber);
        voMap.put("ackMultiple", ackMultiple);
        return R.ResponseResult(voMap);
    }

    @Autowired
    private TerminalEnrollFaceJob terminalEnrollFaceJob;
    @Autowired
    private TerminalEnrollFaceCheckJob terminalEnrollFaceCheckJob;

    @ApiOperation(value = "测试-照片下发巡检")
    @PostMapping("testTerminalEnrollFaceJob")
    public R<?> terminalEnrollFaceJob() {
        terminalEnrollFaceJob.job();
        return R.ResponseOk();
    }

    @ApiOperation(value = "测试-照片检测下发")
    @PostMapping("terminalEnrollFaceCheckJob")
    public R<?> terminalEnrollFaceCheckJob() {
        terminalEnrollFaceCheckJob.job();
        return R.ResponseOk();
    }

    @ApiOperation(value = "测试-跳过民警照片录入")
    @PostMapping("testSkipPoliceEnrollFace")
    public R<?> testSkipPoliceEnrollFace(@RequestParam("flag") Integer flag) {
        CnpServiceImpl.TEST_SKIP_POLICE_ENROLL_FACE = (flag > 0);
        return R.ResponseOk();
    }

    @ApiOperation(value = "测试-获取在线离线设备情况")
    @PostMapping("deviceInscreenInfo")
    public R<?> deviceInscreenInfo() {
        SocketRelationMapVO socketInfo = socketService.getSocketInfo();
        Collection<String> onlineSerialNumberList = socketInfo.getSerialNumberMap().values();
        List<DeviceInscreenVO> allDeviceInscreen = socketDao.getAllDeviceInscreen();
        List<DeviceInscreenVO> onlineInfoList = new ArrayList<>();
        List<DeviceInscreenVO> offlineInfoList = new ArrayList<>();
        List<String> offlineSerialNumberList = new ArrayList<>();
        for (DeviceInscreenVO obj : allDeviceInscreen) {
            String serialNumber = obj.getSerialNumber();
            if (onlineSerialNumberList.contains(serialNumber)) {
                onlineInfoList.add(obj);
            } else {
                offlineInfoList.add(obj);
                offlineSerialNumberList.add(obj.getSerialNumber());
            }
        }
        Map<String, Object> map = new HashMap<>();
        map.put("onlineInfoList", onlineInfoList);
        map.put("onlineInfoListCount", onlineInfoList.size());
        map.put("offlineInfoList", offlineInfoList);
        map.put("offlineInfoListCount", offlineInfoList.size());
        map.put("offlineSerialNumberList", offlineSerialNumberList);
        return R.ResponseResult(map);
    }

    /**
     * 推送仓内屏 语音广播
     *
     * @param form
     * @return
     */
    @ApiOperation(value = "推送仓内屏 语音广播")
    @PostMapping("pushVoiceBroadcast")
    public R<?> pushVoiceBroadcast(@RequestBody @Validated PushVoiceBroadcastForm form) {
        cnpService.pushVoiceBroadcast(form);
        return R.ResponseOk();
    }

    /**
     * 尝试提升亮度下发照片
     *
     * @param rybhAppCode
     * @param photoUrl
     * @param serialNumbe
     * @return
     */
    @Deprecated
    @ApiOperation(value = "尝试提升亮度下发照片")
    @PostMapping("tryEnrollFace")
    public Object tryEnrollFace(@RequestParam String rybhAppCode, String photoUrl, @RequestParam String serialNumbe) {
        for (String sessionId : socketService.getSessionBySerialNumber(Lists.newArrayList(serialNumbe))) {
            PushMessageAckVO pushMessageAckVO = cnpService.tryEnrollFace(rybhAppCode, Lists.newArrayList(photoUrl), sessionId, serialNumbe);
            return pushMessageAckVO;
        }
        return R.ResponseError("无在线客户端");
    }

}
