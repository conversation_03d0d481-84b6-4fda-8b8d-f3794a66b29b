package com.gosun.zhjg.prison.room.terminal.modules.prison.controller;

import com.gosun.zhjg.basic.business.modules.prison.dto.BasePrisonInfoAddRolesForm;
import com.gosun.zhjg.basic.business.modules.prison.dto.BasePrisonInfoSaveForm;
import com.gosun.zhjg.basic.business.modules.prison.vo.BasePrisonInfoVO;
import com.rs.module.base.vo.DeleteRequestBody;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.common.msg.ResultVO;
import com.gosun.zhjg.common.vo.TreeNode;
import com.gosun.zhjg.prison.room.terminal.modules.prison.service.BasePrisonInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 监所基础信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-08-12 19:44:28
 */
@Api(value = "机构管理", tags = "机构管理")
@RestController
@RequestMapping("base/baseprisoninfo")
public class BasePrisonInfoController {

	@Autowired
	private BasePrisonInfoService basePrisonInfoService;


	/**
	 * 机构类型
	 */
	@RequestMapping(value = "/prisonTypeList", method = RequestMethod.GET)
	@ApiOperation(value = "机构管理-机构类型", responseContainer = "List")
	public ResultVO<?> prisonTypeList() {
		return ResultVO.success(basePrisonInfoService.prisonTypeList());
	}

	/**
	 * 获取详情信息
	 */
	@RequestMapping(value = "/info/{id}", method = RequestMethod.GET)
	@ApiOperation(value = "详情", responseContainer = "Map", response = BasePrisonInfoVO.class)
	@ApiImplicitParam(paramType = "path", name = "id", value = "编号", required = true, dataType = "String")
	public R<BasePrisonInfoVO> info(@PathVariable("id") String id) {
		BasePrisonInfoVO vo = basePrisonInfoService.getDetails(id);
		return R.ResponseResult(vo);
	}

	@RequestMapping(value = "/branchList", method = RequestMethod.GET)
	@ApiOperation(value = "机构管理-支队列表", responseContainer = "List")
	public ResultVO<List<BasePrisonInfoVO>> branchList(@RequestParam(required = false) String code) {
		return ResultVO.success(basePrisonInfoService.branchList(code));
	}

	@RequestMapping(value = "/cityTree", method = RequestMethod.GET)
	@ApiOperation(value = "机构管理-行政区域树", responseContainer = "List")
	public ResultVO<List<TreeNode>> cityTree(@RequestParam(value = "name",required = false) String name) {
		List<TreeNode> treeNodes = basePrisonInfoService.cityTree(name);
		return ResultVO.success(treeNodes);
	}

	@ApiOperation("保存")
	@PostMapping("save")
	public R<?> save(@RequestBody @Validated BasePrisonInfoSaveForm form) {
		basePrisonInfoService.save(form);
		return R.ResponseOk();
	}

	@ApiOperation(value = "更新", notes = "优先使用oldId属性进行更新，不存在时才去用id更新")
	@PostMapping("update")
	public R<?> update(@RequestBody @Validated BasePrisonInfoSaveForm form) {
		basePrisonInfoService.update(form);
		return R.ResponseOk();
	}


	@ApiOperation(value = "删除", notes = "优先使用oldId属性进行更新，不存在时才去用id更新")
	@PostMapping("delete")
	public R<?> delete(@RequestBody @Validated DeleteRequestBody form) {
		basePrisonInfoService.delete(form.getIds());
		return R.ResponseOk();
	}

	@RequestMapping(value = "/deleteRoles", method = RequestMethod.POST)
	@ApiOperation(value = "监所解除关联角色")
	public R<?> deleteRoles(@RequestBody @Validated BasePrisonInfoAddRolesForm form) {
		basePrisonInfoService.deleteRoles(form);
		return R.ResponseOk();
	}

	@RequestMapping(value = "/addRoles", method = RequestMethod.POST)
	@ApiOperation(value = "监所关联角色", responseContainer = "Map")
	public R<?> addRoles(@RequestBody @Validated BasePrisonInfoAddRolesForm form) {
		basePrisonInfoService.addRoles(form);
		return R.ResponseOk();
	}



}
