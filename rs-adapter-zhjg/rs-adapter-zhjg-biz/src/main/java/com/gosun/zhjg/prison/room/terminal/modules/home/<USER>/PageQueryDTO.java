package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2023/8/28 9:54
 */
@ApiModel(description = "分页实体")
@Data
public class PageQueryDTO {

    @NotNull(message = "curPage不能为空")
    @ApiModelProperty(value = "当前页", required = true)
    private Integer curPage;

    @NotNull(message = "pageSize不能为空")
    @ApiModelProperty(value = "分页大小", required = true)
    private Integer pageSize;

}
