package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * reserve_apply-预约申请表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-14 14:48:09
 */
@Data
@TableName("reserve_apply")
public class ReserveApplyEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @TableId(type = IdType.ASSIGN_UUID)

    private String id;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 监室名
     */
    private String roomName;
    /**
     * 发起人id
     */
    private String promoterId;
    /**
     * 发起人姓名
     */
    private String promoterName;
    /**
     * 发起时间
     */
    private Date promoteTime;
    /**
     * 申请类型（1：巡诊预约 2：会见申请 3：谈话申请（反映监视动态） 4：法律救助申请）
     */
    private Integer applyType;
    /**
     * 字典值 （病症、会见类型等字典值）
     */
    private String dictCode;
    /**
     * 备注说明
     */
    private String remark;

	/**
	 * 已审批？ -1不需要审批，2未审批，1通过，0驳回
	 */
	private Integer approvad;
	/**
	 * 审批人
	 */
	private String approveUserid;

	private String approveUsername;
	/**
	 * 审批时间
	 */
	private Date approveTime;

    /**
     * 反映内容具体详情
     * <AUTHOR>
     * @date 2025/3/22 10:47
     * @param
     * @return
     */
    private String applyContent;
}
