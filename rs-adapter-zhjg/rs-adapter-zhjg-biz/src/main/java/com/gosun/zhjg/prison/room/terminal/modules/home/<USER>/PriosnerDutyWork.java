package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.*;

import lombok.Data;

/**
 * priosner_duty_work 实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date: 2022-04-24
 */
@Data
@TableName("priosner_duty_work")
public class PriosnerDutyWork implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * id
     */
    @TableId(type = IdType.UUID)
    private String id;
    /**
     * 1:监事值日2:卫生值日3:劳动值日
     */
    private Integer type;
    /**
     * 值班人员
     */
    private String name;
    /**
     * 值班人员编号
     */
    private String prisonerId;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;
    /**
     * 创建人
     */
    private String createUser;

    /***
     * 监所编号
     */
    private String roomId;

}
