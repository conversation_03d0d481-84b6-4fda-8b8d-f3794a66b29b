package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import java.util.Date;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 安全检查管理-要求设置(SafetyCheckManageRequire)实体类
 *
 * <AUTHOR>
 * @since 2024-10-28 19:21:54
 */
@Data
@TableName("safety_check_manage_require")
public class SafetyCheckManageRequireEntity implements Serializable {
    private static final long serialVersionUID = -27475704399742433L;
    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_UUID)

    private String id;
    /**
     * 要求名称
     */
    private String requireName;
    /**
     * 要求内容
     */
    private String requireContent;
    /**
     * 监所编号
     */
    private String prisonId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人id
     */
    private String createUserId;
    /**
     * 创建人姓名
     */
    private String createUserName;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 更新人id
     */
    private String updateUserId;
    /**
     * 更新人姓名
     */
    private String updateUserName;


}

