package com.gosun.zhjg.prison.room.terminal.common;

import lombok.Getter;

/**
 * 点名类型枚举
*<AUTHOR>
*@email <EMAIL>
*@date 2022/7/8
*/
@Getter
public enum RoomRepresentTypeEnum {
    dsdm("定时点名","1"),
    spdm("视频点名","2"),
    lsdm("临时点名","0");

    private String key;

    private String value;

    RoomRepresentTypeEnum(String value, String key) {
        this.key = key;
        this.value = value;
    }

    // 普通方法
    public static String getValue(String index) {
        for (RoomRepresentTypeEnum c : RoomRepresentTypeEnum.values()) {
            if (index.contains(c.getKey())) {
                return c.value;
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
