package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 一日生活 任务关联监室
 *
 * <AUTHOR>
 * @date 2023/4/17 14:15
 */
@Data
@TableName("prison_daily_life_room")
public class PrisonDailyLifeRoomEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * prison_daily_life表id
     */
    @TableId(type = IdType.ASSIGN_UUID)

    private String dailyLifeId;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 删除标志，0未删除，1删除
     */
    private Integer delFlag;
}
