package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

public class BatchReviewRequestDto {

    @NotNull
    @ApiModelProperty(value = "审核状态（ 2:通过, 3:驳回）")
    private String action;

    @NotNull
    private String[] ids;


    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String[] getIds() {
        return ids;
    }

    public void setIds(String[] ids) {
        this.ids = ids;
    }
}