package com.gosun.zhjg.prison.room.terminal.common.utils;

import com.gosun.zhjg.common.util.UUIDUtils;
import com.lowagie.text.Document;
import com.lowagie.text.Image;
import com.lowagie.text.pdf.PdfWriter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import java.io.*;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Component
public class PhotoToPdfUtil {

    @Autowired
    private PhotoUploadUtil photoUploadUtil;

    public String imagesUrlToPdf(List<String> imagesUrls){
        String path = null;
        try {
            if (CollectionUtils.isEmpty(imagesUrls)) {
                return null;
            }
            ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();
            Document doc = new Document();
            PdfWriter.getInstance(doc, pdfOutputStream);
            doc.open();
            for (String imagesFile : imagesUrls) {
                if (imagesFile == null) {
                    return null;
                }
                InputStream is = new BufferedInputStream(new URL(imagesFile).openStream());
                ByteArrayOutputStream bos = new ByteArrayOutputStream();//创建输出流对象
                byte[] b = new byte[1024];
                int len;
                while ((len = is.read(b)) != -1) {
                    bos.write(b, 0, len);
                }
                byte[] imageDate = bos.toByteArray();
                bos.close();
                Image image = Image.getInstance(imageDate);

                float width = doc.getPageSize().getWidth() - doc.leftMargin() - doc.rightMargin();
                float height = image.getHeight() * width / image.getWidth();
                image.scaleAbsolute(width, height);
                image.setAlignment(Image.ALIGN_CENTER);
                doc.add(image);
            }
            doc.close();
            String s=null;
            byte[] bytes = pdfOutputStream.toByteArray();
            s= Base64Util.byte2Base64StringFun((byte[]) bytes);
            try {
                Map<String, String> map = new HashMap<>();
                map.put("newName", UUIDUtils.generateShortUuid()+".pdf");
                map.put("savePath","/rights-duo-pdf");
                map.put("photoBase",s);
                path = photoUploadUtil.prisonerPhotoUpload(map);
            }catch (Exception e){
                log.info("权利义务合成pdf图片上传失败！");
                e.printStackTrace();
            }

        }catch (Exception e){
            e.printStackTrace();
            log.error("照片合成PDF失败!",e);
        }
        return path;
    }

}
