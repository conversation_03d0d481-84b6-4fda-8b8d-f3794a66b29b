package com.gosun.zhjg.prison.room.terminal.modules.home.vo;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
* PrisonerOutInMessageVO 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2023-08-15
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="PrisonerOutInMessageVO对象", description="带入带出-消息提醒表")
public class PrisonerOutInMessageVO implements Serializable {

    @JsonProperty("id")
    @ApiModelProperty(value = "主键")
    private String id;

    @JsonProperty("prisonerName")
    @ApiModelProperty(value = "在押人员姓名")
    private String prisonerName;

    @JsonProperty("frontPhoto")
    @ApiModelProperty(value = "在押人员头像")
    private String frontPhoto;

    @JsonProperty("type")
    @ApiModelProperty(value = "1-带出 2-带入")
    private Integer type;

    @JsonProperty("content")
    @ApiModelProperty(value = "消息内容")
    private String content;

    @JsonProperty("status")
    @ApiModelProperty(value = "1-已处理")
    private Integer status;

    @JsonProperty("busnessType")
    @ApiModelProperty(value = "1-提讯 2-家属会见 3-律师会见")
    private Integer busnessType;


    @JsonProperty("busnessTypeName")
    @ApiModelProperty(value = "业务类型")
    private String busnessTypeName;

    @JsonProperty("createTime")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    private Date createTime;

}
