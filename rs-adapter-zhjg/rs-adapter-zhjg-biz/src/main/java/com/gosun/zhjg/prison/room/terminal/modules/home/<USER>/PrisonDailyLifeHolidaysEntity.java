package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 一日生活制度-节假日表
 *
 * <AUTHOR>
 * @date 2024/9/19
 */
@Data
@TableName("prison_daily_life_holidays")
@ApiModel(description = "一日生活制度-节假日表")
public class PrisonDailyLifeHolidaysEntity {

    @TableId(value = "date", type = IdType.INPUT)
    @ApiModelProperty(value = "节假日日期,也是主键ID", required = true)
    private String date;

    @ApiModelProperty(value = "节假日名称")
    private String name;

    @ApiModelProperty(value = "节假日所属年份")
    private String year;

    @ApiModelProperty(value = "节假日类型。1周末；2元旦；3春节；4清明节；5劳动节；6端午节；7中秋节；8国庆节；9妇女节")
    private String type;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "节假日是否补班工作。0放假1工作", required = true)
    private Integer work;

    @ApiModelProperty(value = "是否为整日假期。0是1否", required = true)
    private Integer wholeDay;

    @ApiModelProperty(value = "放假开始时间，用于非整日假期，默认为空")
    private Date startTime;

    @ApiModelProperty(value = "放假结束时间，用于非整日假期，默认为空")
    private Date endTime;

    @ApiModelProperty(value = "数据创建时间", required = true)
    private Date createTime;

    @ApiModelProperty(value = "数据更新时间")
    private Date updateTime;
}
