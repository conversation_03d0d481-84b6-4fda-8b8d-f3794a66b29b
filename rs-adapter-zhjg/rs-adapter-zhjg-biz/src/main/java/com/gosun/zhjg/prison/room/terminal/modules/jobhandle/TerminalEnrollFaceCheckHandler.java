package com.gosun.zhjg.prison.room.terminal.modules.jobhandle;

import com.gosun.zhjg.prison.room.terminal.modules.socket.service.TerminalEnrollFaceCheckJob;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.TerminalEnrollFaceJob;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.handler.IJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 仓内屏人脸下发增加任务对未下发过的在押人员进行补录，不同于巡检
 * <p>
 * 1. 只针对在押人员
 * 2. 只处理《人脸库管理/导入台账》状态空的。（即 该人员关联的设备，无下发记录的人员）
 *
 * <AUTHOR>
 * @date 2023/6/25 10:02
 */
@Slf4j
@Component
public class TerminalEnrollFaceCheckHandler extends IJobHandler implements InitializingBean {

    @Autowired
    private TerminalEnrollFaceCheckJob job;

    @Override
    public void afterPropertiesSet() {
        XxlJobExecutor.registJobHandler("TerminalEnrollFaceCheckHandler", this);
    }

    @Override
    public void execute() {
        log.info("人脸检查录入执行-TerminalEnrollFaceCheckHandler");
        try {
            if (TerminalEnrollFaceJob.isRunning.get()) {
                XxlJobHelper.log("终端人脸补录任务巡检已在执行中，退出本次人脸检查任务");
                log.warn("终端人脸补录任务巡检已在执行中，退出本次人脸检查任务");
                return;
            }
            job.job();
        } catch (Exception e) {
            log.error("人脸检查录入任务执行异常", e);
            throw e;
        }
    }
}
