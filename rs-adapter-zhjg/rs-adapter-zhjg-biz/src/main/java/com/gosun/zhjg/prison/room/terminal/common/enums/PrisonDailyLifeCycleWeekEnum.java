package com.gosun.zhjg.prison.room.terminal.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * 自定义周期时，显示星期几的枚举
 *
 * <AUTHOR>
 * @date 2024/9/20
 */
@Getter
public enum PrisonDailyLifeCycleWeekEnum {

    MONDAY("1","星期一"),
    TUESDAY("2","星期二"),
    WEDNESDAY("3","星期三"),
    THURSDAY("4","星期四"),
    FRIDAY("5","星期五"),
    SATURDAY("6","星期六"),
    SUNDAY("7","星期日"),
    ;

    private final String code;
    private final String name;

    PrisonDailyLifeCycleWeekEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Optional<PrisonDailyLifeCycleWeekEnum> findMatch(String code) {
        return Arrays.stream(PrisonDailyLifeCycleWeekEnum.values())
                .filter(e -> Objects.equals(e.getCode(), code))
                .findFirst();
    }
}
