package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * cwp_doctor_visit 实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date: 2022-04-26
 */
@Data
@TableName("cwp_doctor_visit")
public class CwpDoctorVisit implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @TableId(type = IdType.UUID)
    private String id;
    /**
     * 巡诊状态-0-正常 1-异常
     */
    private String status;
    /**
     * 重病号人数
     */
    private Integer seriousIllnessNum;
    /**
     * 普通病号人数
     */
    private Integer commonIllnessNum;
    /**
     * 情况说明
     */
    private String remarks;
    /**
     * 监室号
     */
    private String roomId;
    /**
     * 监室名
     */
    private String roomName;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private Date updateTime;
    /**
     * 创建人
     */
    private String createUser;
}
