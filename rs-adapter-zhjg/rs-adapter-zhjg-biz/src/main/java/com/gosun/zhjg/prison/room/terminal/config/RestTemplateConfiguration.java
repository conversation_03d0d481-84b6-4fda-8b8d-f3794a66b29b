package com.gosun.zhjg.prison.room.terminal.config;

import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.ResponseErrorHandler;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.List;

@Getter
@Setter
@Slf4j
@Configuration
public class RestTemplateConfiguration {

	private Integer readTimeout = 60000;
	private Integer connectTimeout = 60000;
	private Integer connectionRequestTimeout = 60000;
	private Integer maxPooledConnection = 200;
	private String supportedProtocol = "TLSv1.2";

	@Bean
	@LoadBalanced
	@ConditionalOnClass(RestTemplate.class)
	public RestTemplate remoteRestTemplate(RestTemplateBuilder restTemplateBuilder) throws Exception {
		// 初始化连接工厂
		HttpComponentsClientHttpRequestFactory httpRequestFactory = initialHttpRequestFactory();
		// 构建RestTemplate
		RestTemplate restTemplate = restTemplateBuilder.build();
		restTemplate.setRequestFactory(httpRequestFactory);
		List<HttpMessageConverter<?>> list = restTemplate.getMessageConverters();
		for (HttpMessageConverter<?> converter : list) {
			if (converter instanceof StringHttpMessageConverter) {
				((StringHttpMessageConverter) converter).setDefaultCharset(StandardCharsets.UTF_8);
				break;
			}
		}
		restTemplate.setErrorHandler(new ResponseErrorHandler() {

			@Override
			public boolean hasError(@NonNull ClientHttpResponse response) throws IOException {
				return true;
			}

			@Override
			public void handleError(@NonNull ClientHttpResponse response) throws IOException {
				if (response.getStatusCode() != HttpStatus.OK) {
					log.error("REST SERVICE ERROR, Status Code:[{}], Response Status Text:[{}]", response.getStatusCode(), response.getStatusText());
				}
			}
		});
		return restTemplate;
	}

	/**
	 * 初始化连接工厂
	 *
	 * @return HttpComponentsClientHttpRequestFactory
	 * @throws Exception Exception
	 */
	private HttpComponentsClientHttpRequestFactory initialHttpRequestFactory() throws Exception {
		// 创建http客户端请求工厂
		HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
		httpRequestFactory.setReadTimeout(getReadTimeout());
		httpRequestFactory.setConnectTimeout(getConnectTimeout());
		httpRequestFactory.setConnectionRequestTimeout(getConnectionRequestTimeout());
		// 注册Https和Http，并绕过Https证书验证
		SSLConnectionSocketFactory sslConnectionSocketFactory = new SSLConnectionSocketFactory(initialSslContext(), NoopHostnameVerifier.INSTANCE);
		Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create().register("http", PlainConnectionSocketFactory.INSTANCE).register("https", sslConnectionSocketFactory).build();
		PoolingHttpClientConnectionManager clientConnectionManager = new PoolingHttpClientConnectionManager(registry);
		clientConnectionManager.setMaxTotal(getMaxPooledConnection());
		CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslConnectionSocketFactory).setConnectionManager(clientConnectionManager).setConnectionManagerShared(true).build();
		httpRequestFactory.setHttpClient(httpClient);
		return httpRequestFactory;
	}

	/**
	 * 初始化SSL上下文
	 *
	 * @return SSLContext
	 * @throws Exception Exception
	 */
	private SSLContext initialSslContext() throws Exception {
		SSLContext sslContext = SSLContext.getInstance(getSupportedProtocol());
		X509TrustManager trustManager = new X509TrustManager() {
			@Override
			public void checkClientTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {

			}

			@Override
			public void checkServerTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
			}

			@Override
			public X509Certificate[] getAcceptedIssuers() {
				return null;
			}
		};
		sslContext.init(null, new TrustManager[] { trustManager }, null);
		return sslContext;
	}
}
