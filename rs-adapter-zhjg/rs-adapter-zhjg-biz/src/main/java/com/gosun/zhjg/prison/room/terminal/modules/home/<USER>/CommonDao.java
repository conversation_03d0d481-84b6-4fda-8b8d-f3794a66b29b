package com.gosun.zhjg.prison.room.terminal.modules.home.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.HashMap;
import java.util.List;

@Mapper
public interface CommonDao {

    @Select("select prison_type from base_prison_info where id = #{prisonId}")
    Integer getPrisonType(@Param("prisonId") String prisonId);

    /**
     * 获取监所名称
     *
     * @param prisonId
     * @return
     */
    @Select("select prison_name from base_prison_info where id = #{prisonId}")
    String getPrisonName(@Param("prisonId") String prisonId);

    @Deprecated
    List<HashMap<String, String>> batchQueryPrisonerName(@Param("rybhList") List<String> rybhList);

    /**
     * map key：人员编号， value姓名
     *
     * @param rybhList
     * @return
     */
    default HashMap<String, String> batchQueryPrisonerNameExt(@Param("rybhList") List<String> rybhList) {
        if (rybhList == null || rybhList.isEmpty()) {
            return new HashMap<>();
        }
        HashMap<String, String> records = new HashMap<>();
        List<HashMap<String, String>> hashMaps = this.batchQueryPrisonerName(rybhList);
        for (HashMap<String, String> map : hashMaps) {
            records.put(map.get("rybh"), map.get("xm"));
        }
        return records;
    }



    /**
     * 获取人员单位代码
     *
     * @param prisonerId
     * @return
     */
    @Select("select dwdm from vw_acp_pm_prisoner_in where rybh = #{prisonerId}")
    String getPrisonerPrisonId(@Param("prisonerId") String prisonerId);

    /**
     * 查询监室所属监所
     *
     * @param roomId
     * @return
     */
    @Select("select prison_id from acp_pm_area_prison_room where id = #{roomId}")
    String getRoomPrisonId(@Param("roomId") String roomId);



    /**
     * 查询监所下的设备序列号
     *
     * @param prisonId
     * @return
     */
    List<String> getSerialNumberByPrisonId(@Param("prisonId") String prisonId, @Param("deviceType") Integer deviceType);
}
