package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * terminal_notification-消息通知
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-06-15 10:28:08
 */
@Data
@TableName("terminal_notification")
public class TerminalNotificationEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_UUID)

    private String id;
    /**
     * 通知类型
     */
    private String type;
    /**
     * 监室号
     */
    private String roomId;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 被监管人员编号
     */
    private String prisonerId;
    /**
     * 被监管人员名称
     */
    private String prisonerName;
    /**
     * 执行时间
     */
    private Date executionTime;
    /**
     * 通知内容
     * {@code @TableField(value = "\"context\"")} context 在达梦数据库是必须加双引号
     */
    @TableField(value = "\"context\"")
    private String context;
    /**
     * 发布时间
     */
    private Date publicTime;
    /**
     * 发布人
     */
    private String publisher;
    /**
     * 发布人id
     */
    private Integer publisherId;
    /**
     * 创建人id
     */
    private String createUserId;
    /**
     * 创建人
     */
    private String createUserName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 发布状态
     */
    private String publicStatus;
    /**
     * 播放次数
     */
    private Integer broadcastNum;

    /**
     * 删除标志
     */
    private Integer delFlag;

    /***
     * 1-已读 0-未读
     */
    private Integer readStatus;

    /***
     * 监所编号
     */
    private String prisonId;
}
