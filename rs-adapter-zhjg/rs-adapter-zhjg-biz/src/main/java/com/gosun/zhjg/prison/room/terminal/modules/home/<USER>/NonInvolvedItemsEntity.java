package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.*;

import lombok.Data;

/**
 * non_involved_items 实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date: 2024-06-07
 */
@Data
@TableName("non_involved_items")
public class NonInvolvedItemsEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @TableId(type = IdType.UUID)
    private String id;
    /**
     * 人员编号
     */
    private String prisonerId;
    /**
     * 物品名称
     */
    private String itemName;
    /**
     * 物品来源code
     */
    private String itemSourceCode;
    /**
     * 物品状态
     */
    private Integer itemStatus;
    /**
     * 存放位置
     */
    private String storageLocation;
    /**
     * 存入时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date depositTime;
    /**
     * 管理人
     */
    private String manager;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 照片url
     */
    private String photoUrl;
    /**
     * 照片名字
     */
    private String photoName;
    /**
     * 取出时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date removalTime;
    /**
     * 取出人以及联系方式
     */
    private String removalUserTel;
    /**
     * 登记时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 登记人id
     */
    private String createUserId;
    /**
     * 登记人
     */
    private String createUser;
    /**
     * 删除标志 1-删除
     */
    private Integer delFlag;
    /**
     * 监所编号
     */
    private String prisonId;
}
