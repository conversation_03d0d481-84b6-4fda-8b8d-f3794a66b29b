package com.gosun.zhjg.prison.room.terminal.modules.home.service;

import com.gosun.zhjg.prison.room.terminal.modules.home.dto.BaseSystemRuleConfigKeyValueDto;
import com.gosun.zhjg.prison.room.terminal.modules.home.entity.BaseSystemRuleConfigEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 基础系统配置
 *
 * <AUTHOR>
 * @date 2024/12/12 17:53
 */
public interface BaseSystemRuleConfigService {

	List<BaseSystemRuleConfigEntity> get(@NotBlank String prisonId, @NotBlank String key);

	String getValue(@NotBlank String prisonId, @NotBlank String key);

	/**
	 * 获取多项的值，不存在时仍补充出对象结构
	 *
	 * @param prisonId
	 * @param keyList
	 * @return
	 */
	List<BaseSystemRuleConfigKeyValueDto> getMultiItemValue(@NotBlank String prisonId, @NotEmpty List<String> keyList);

	void setValue(@NotBlank String prisonId, @NotBlank String key, @NotNull String value);

	void setValue(@NotBlank String prisonId, @NotBlank String key, @NotNull String value, String userid, String name);

	void set(@NotNull BaseSystemRuleConfigEntity entity);
}

