package com.gosun.zhjg.prison.room.terminal.modules.socket.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

/**
 * 仓内屏仓外屏请求初始化
 *
 * <AUTHOR>
 */
@Data
public class DeviceInitializeDto {
    /**
     * com.gosun.zhjg.common.enums.login.LoginTypeEnum CWP, CNP
     */
    private String terminal;

    @Deprecated
    private List<String> roomIds;

    private String serialNumber;

//    /**
//     * 设备ip
//     */
//    @Deprecated
//    private String deviceIp;

    @JsonIgnore
    @JSONField(serialize = false)
    public String getRoomId() {
        if (this.roomIds == null || this.roomIds.size() == 0) {
            return null;
        }
        return this.roomIds.get(0);
    }
}
