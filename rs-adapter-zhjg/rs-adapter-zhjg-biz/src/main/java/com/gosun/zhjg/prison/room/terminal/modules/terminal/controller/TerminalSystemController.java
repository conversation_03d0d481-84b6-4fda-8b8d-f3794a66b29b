package com.gosun.zhjg.prison.room.terminal.modules.terminal.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.gosun.zhjg.common.config.UserContext;
import com.gosun.zhjg.common.constant.DictionaryConstants;
import com.gosun.zhjg.common.exception.BaseException;
import com.gosun.zhjg.common.msg.ResultVO;
import com.gosun.zhjg.common.util.CommonServiceUtils;
import com.gosun.zhjg.prison.room.terminal.common.service.DictService;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.constant.TerminalVersionManagementConstants;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.TerminalVersionInfoDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.TerminalVersionManagementDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.TerminalVersionUpgradeLogDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.TerminalVersionManagementPageDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.TerminalVersionManagementsDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.TerminalVersionUpgradeLogSaveDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.TerminalVersionInfoEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.TerminalVersionManagementEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.TerminalVersionUpgradeLogEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.TerminalService;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.TerminalVersionManagementFileScanJob;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.TerminalVersionManagementService;
import com.gosun.zhjg.prison.room.terminal.modules.jobhandle.TerminalUpgradeLogStateHandler;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.SocketService;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.SocketRelationMapVO;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dao.jsyw.TerminalSystemDao;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dao.jsyw.TerminalVersionUpgradeLogBatchDao;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dto.*;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.entity.TerminalVersionUpgradeLogBatchEntity;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalSystemDeviceVO;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalSystemUpgradeLogBatchVO;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalSystemUpgradeLogVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 智能终端管理系统
 *
 * <AUTHOR>
 * @date 2024/2/28 10:03
 */
@Slf4j
@Api(tags = "智能终端管理系统")
@RequestMapping("terminalsystem")
@RestController
public class TerminalSystemController {

	@Autowired
	private TerminalSystemDao terminalSystemDao;
	@Autowired
	private DictService dictService;
	@Value("${file-url-prefix}")
	private String pathPrefix;
	@Autowired
	private TerminalVersionManagementDao terminalVersionManagementDao;
	@Autowired
	private TerminalService terminalService;
	@Autowired
	private TerminalVersionUpgradeLogBatchDao terminalVersionUpgradeLogBatchDao;
	@Autowired
	private TerminalVersionUpgradeLogDao terminalVersionUpgradeLogDao;
	@Autowired
	private TerminalVersionManagementFileScanJob terminalVersionManagementFileScanJob;
	@Autowired
	private TerminalUpgradeLogStateHandler terminalUpgradeLogStateHandler;
	@Autowired
	private TerminalVersionInfoDao terminalVersionInfoDao;
	@Autowired
	private TerminalVersionManagementService terminalVersionManagementService;

	@Autowired
	private SocketService socketService;

	@SuppressWarnings({"unchecked", "rawtypes"})
	@RequestMapping(value = "/device/cnp/list", method = RequestMethod.GET)
	@ApiOperation(value = "智能终端管理系统-设备列表-仓内屏", responseContainer = "List", response = TerminalSystemDeviceVO.class)
	public ResultVO<?> deviceCnpList(@ApiParam(hidden = true) TerminalSystemDeviceQueryDto form) {
		form.setDeviceType(1);
		form.setPrisonId(UserContext.getJwtInfo().getPrisonId());
		Page page = null;
		if (form.isPageing()) {
			page = form.trainToPage();
		}
		List<TerminalSystemDeviceVO> records = terminalSystemDao.findDeviceCnpCwpList(form, page);
		SocketRelationMapVO socketInfo = socketService.getSocketInfo();
		Collection<String> onlineSerialNumber = socketInfo.getSerialNumberMap().values();
		for (TerminalSystemDeviceVO vo : records) {
			vo.setOnlineState(0);
			if (vo.getSerialNumber() != null) {
				if (onlineSerialNumber.contains(vo.getSerialNumber())) {
					vo.setOnlineState(1);
				}
			}
			// 各终端配置服务页面
			if (StringUtils.isNotBlank(vo.getDeviceIp()) && terminalService.getTerminalServicePort() > 0) {
				vo.setTerminalServicePage(String.format("http://%s:%s", vo.getDeviceIp(), terminalService.getTerminalServicePort()));
			}
		}
		if (page == null) {
			page = new Page<>(1, records.size(), records.size());
			page.setTotal(records.size());
		}
		page.setRecords(records);
		return ResultVO.page(page);
	}

	@SuppressWarnings({"unchecked", "rawtypes"})
	@RequestMapping(value = "/device/cwp/list", method = RequestMethod.GET)
	@ApiOperation(value = "智能终端管理系统-设备列表-仓外屏", responseContainer = "List", response = TerminalSystemDeviceVO.class)
	public ResultVO<?> deviceCwpList(@ApiParam(hidden = true) TerminalSystemDeviceQueryDto form) {
		form.setDeviceType(2);
		form.setPrisonId(UserContext.getJwtInfo().getPrisonId());
		Page page = null;
		if (form.isPageing()) {
			page = form.trainToPage();
		}
		SocketRelationMapVO socketInfo = socketService.getSocketInfo();
		List<TerminalSystemDeviceVO> records = terminalSystemDao.findDeviceCnpCwpList(form, page);
		Collection<String> onlineSerialNumber = socketInfo.getSerialNumberMap().values();
		for (TerminalSystemDeviceVO vo : records) {
			vo.setOnlineState(0);
			if (vo.getSerialNumber() != null) {
				if (onlineSerialNumber.contains(vo.getSerialNumber())) {
					vo.setOnlineState(1);
				}
			}
			// 各终端配置服务页面
			if (StringUtils.isNotBlank(vo.getDeviceIp()) && terminalService.getTerminalServicePort() > 0) {
				vo.setTerminalServicePage(String.format("http://%s:%s", vo.getDeviceIp(), terminalService.getTerminalServicePort()));
			}
		}
		if (page == null) {
			page = new Page<>(1, records.size(), records.size());
			page.setTotal(records.size());
		}
		page.setRecords(records);
		return ResultVO.page(page);
	}

	@RequestMapping(value = "/device/trjjzd/list", method = RequestMethod.GET)
	@ApiOperation(value = "智能终端管理系统-设备列表-提人交接终端", responseContainer = "List", response = TerminalSystemDeviceVO.class)
	public ResultVO<?> deviceTrjjzdList(@ApiParam(hidden = true) TerminalSystemDeviceQueryDto form) {
		form.setPrisonId(UserContext.getJwtInfo().getPrisonId());
		Page page = null;
		if (form.isPageing()) {
			page = form.trainToPage();
		}
		List<TerminalSystemDeviceVO> records = terminalSystemDao.findDeviceTrjjzdList(form, page);
		SocketRelationMapVO socketInfo = socketService.getSocketInfo();
		Collection<String> onlineSerialNumber = socketInfo.getMacAddressMap().values();
		for (TerminalSystemDeviceVO vo : records) {
			if (vo.getSerialNumber() != null) {
				if (onlineSerialNumber.contains(vo.getSerialNumber())) {
					vo.setOnlineState(1);
				}
			}
			vo.setOnlineState(0);
			// 各终端配置服务页面
			if (StringUtils.isNotBlank(vo.getDeviceIp()) && terminalService.getTerminalServicePort() > 0) {
				vo.setTerminalServicePage(String.format("http://%s:%s", vo.getDeviceIp(), terminalService.getTerminalServicePort()));
			}
		}
		if (page == null) {
			page = new Page<>(1, records.size(), records.size());
			page.setTotal(records.size());
		}
		page.setRecords(records);
		return ResultVO.page(page);
	}

	/**
	 * 重启
	 *
	 * @param form
	 * @return
	 */
	@RequestMapping(value = "/device/reboot", method = RequestMethod.POST)
	@ApiOperation(value = "智能终端管理系统-设备-重启")
	public ResultVO<?> deviceReboot(@RequestBody TerminalSystemDeviceRebootDto form) {
		String virtualId = form.getVirtualId();
		if (StringUtils.isBlank(virtualId)) {
			return ResultVO.error("virtualId不能为空", null);
		}
		String device = this.parseVirtualId(virtualId);
		String terminalType = this.parseVirtualTerminalType(virtualId);
		TerminalSystemDeviceQueryDto queryDto = new TerminalSystemDeviceQueryDto();
		queryDto.setDeviceList(Lists.newArrayList(device));
		List<TerminalSystemDeviceVO> deviceList = this.findDeviceList(terminalType, queryDto, null);
		if (CollectionUtils.isEmpty(deviceList)) {
			return ResultVO.error("未找到要重启的设备信息", null);
		}
		List<String> ipList = deviceList.stream().map(TerminalSystemDeviceVO::getDeviceIp).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
		if (CollectionUtils.isEmpty(ipList)) {
			return ResultVO.error("未找到设备ip信息", null);
		}
		ipList.forEach(f ->
		{
			terminalService.reStartApp(f);
		});
		return ResultVO.success();
	}

	@RequestMapping(value = "/device/face/delete", method = RequestMethod.POST)
	@ApiOperation(value = "智能终端管理系统-设备-删除人脸")
	public ResultVO<?> deviceFaceDelete(@RequestBody @Validated TerminalSystemDeviceBatchDto form) {
		final List<String> virtualIdList = form.getVirtualIdList();
		if (virtualIdList.size() == 1) {
			// 单个同步操作
			String virtualId = form.getVirtualIdList().get(0);
			String device = this.parseVirtualId(virtualId);
			String terminalType = this.parseVirtualTerminalType(virtualId);
			TerminalSystemDeviceQueryDto queryDto = new TerminalSystemDeviceQueryDto();
			queryDto.setDeviceList(Lists.newArrayList(device));
			List<TerminalSystemDeviceVO> deviceList = this.findDeviceList(terminalType, queryDto, null);
			if (CollectionUtils.isEmpty(deviceList)) {
				return ResultVO.error("未找到设备信息", null);
			}
			for (TerminalSystemDeviceVO vo : deviceList) {
				if (StringUtils.isBlank(vo.getDeviceIp()) || "null".equals(vo.getDeviceIp())) {
					return ResultVO.error(String.format("设备'%s'未配置ip信息，删除失败", vo.getDeviceName()), null);
				}
				terminalService.userDelete(vo.getDeviceIp(), "ALL");
			}
		} else if (!virtualIdList.isEmpty()) {
			// 多个异步
			new Thread(new Runnable() {
				@Override
				public void run() {
					Map<String, List<String>> collect = virtualIdList.stream().collect(Collectors.groupingBy(TerminalSystemController::parseVirtualTerminalType));
					for (Map.Entry<String, List<String>> entry : collect.entrySet()) {
						String terminalType = entry.getKey();
						List<String> device = entry.getValue().stream().map(TerminalSystemController::parseVirtualId).collect(Collectors.toList());
						TerminalSystemDeviceQueryDto queryDto = new TerminalSystemDeviceQueryDto();
						queryDto.setDeviceList(Lists.newArrayList(device));
						List<TerminalSystemDeviceVO> deviceList = findDeviceList(terminalType, queryDto, null);
						if (deviceList != null) {
							for (TerminalSystemDeviceVO vo : deviceList) {
								if (StringUtils.isBlank(vo.getDeviceIp()) || "null".equals(vo.getDeviceIp())) {
									continue;
								}
								try {
									terminalService.userDelete(vo.getDeviceIp(), "ALL");
								} catch (BaseException ex) {
									log.error(String.format("清空人脸失败 %s, %s, %s", vo.getDeviceName(), vo.getDeviceIp(), vo.getVirtualId()), ex.getCause() == null ? ex : ex.getCause());
								} catch (Exception ex) {
									log.error(String.format("清空人脸失败 %s, %s, %s", vo.getDeviceName(), vo.getDeviceIp(), vo.getVirtualId()), ex);
								}
							}
						}
					}
				}
			}).start();
		}
		return ResultVO.success();
	}

	@RequestMapping(value = "/deviceAll/face/delete", method = RequestMethod.POST)
	@ApiOperation(value = "智能终端管理系统-设备-删除全部设备全部人脸", notes = "{\"terminalType\":\"0008\"}")
	public ResultVO<?> deviceAllFaceDelete(@RequestBody @Validated JSONObject form) {
		final String terminalType = form.getString("terminalType");
		final String prisonId = UserContext.getJwtInfo().getPrisonId();
		if (StringUtils.isBlank(terminalType)) {
			return ResultVO.error("无效的参数", null);
		}
		Thread thread = new Thread(new Runnable() {
			@Override
			public void run() {
				TerminalSystemDeviceQueryDto queryDto = new TerminalSystemDeviceQueryDto();
				queryDto.setPrisonId(prisonId);
				List<TerminalSystemDeviceVO> deviceList = findDeviceList(terminalType, queryDto, null);
				if (deviceList != null) {
					for (TerminalSystemDeviceVO vo : deviceList) {
						if (StringUtils.isBlank(vo.getDeviceIp()) || "null".equals(vo.getDeviceIp())) {
							continue;
						}
						try {
							terminalService.userDelete(vo.getDeviceIp(), "ALL");
						} catch (BaseException ex) {
							log.error(String.format("清空人脸失败 %s, %s, %s", vo.getDeviceName(), vo.getDeviceIp(), vo.getVirtualId()), ex.getCause() == null ? ex : ex.getCause());
						} catch (Exception ex) {
							log.error(String.format("清空人脸失败 %s, %s, %s", vo.getDeviceName(), vo.getDeviceIp(), vo.getVirtualId()), ex);
						}
					}
				}
			}
		});
		thread.start();
		return ResultVO.success();
	}

	@RequestMapping(value = "/upgradelog/batch/list", method = RequestMethod.GET)
	@ApiOperation(value = "智能终端管理系统-更新台账-批次列表", responseContainer = "List", response = TerminalSystemUpgradeLogBatchVO.class)
	public ResultVO<?> upgradelogBatchList(@ApiParam(hidden = true) TerminalSystemUpgradeLogQueryDto form) {
		form.setPrisonId(UserContext.getJwtInfo().getPrisonId());
		Page page = form.trainToPage();
		List<TerminalSystemUpgradeLogBatchVO> records = terminalSystemDao.upgradelogBatchList(form, page);
		for (TerminalSystemUpgradeLogBatchVO vo : records) {
			vo.setTerminalTypeDisplayName(dictService.dictValue(DictionaryConstants.TERMINAL_SYSTEM_TERMINAL_TYPE, vo.getTerminalType()));
			vo.setPackageTypeDisplayName(dictService.dictValue(DictionaryConstants.TERMINAL_SYSTEM_PACKAGE_TYPE, vo.getPackageType()));
		}
		page.setRecords(records);
		return ResultVO.page(page);
	}

	@RequestMapping(value = "/upgradelog/batch/info/{id}", method = RequestMethod.GET)
	@ApiOperation(value = "智能终端管理系统-更新台账-批次列表-详情", response = TerminalSystemUpgradeLogBatchVO.class)
	public ResultVO<?> upgradelogBatchInfo(@PathVariable("id") String id) {
		TerminalSystemUpgradeLogBatchVO vo = terminalSystemDao.upgradelogBatchInfo(id);
		return ResultVO.success(vo);
	}

	@RequestMapping(value = "/upgradelog/list", method = RequestMethod.GET)
	@ApiOperation(value = "智能终端管理系统-更新台账-批次-详细列表", responseContainer = "List", response = TerminalSystemUpgradeLogVO.class)
	public ResultVO<?> upgradelogList(@ApiParam(hidden = true) TerminalSystemUpgradeLogQueryDto form) {
		Page page = form.trainToPage();
		List<TerminalSystemUpgradeLogVO> records = terminalSystemDao.upgradelogList(form, page);
		for (TerminalSystemUpgradeLogVO vo : records) {
			vo.setTerminalTypeDisplayName(dictService.dictValue(DictionaryConstants.TERMINAL_SYSTEM_TERMINAL_TYPE, vo.getTerminalType()));
			vo.setPackageTypeDisplayName(dictService.dictValue(DictionaryConstants.TERMINAL_SYSTEM_PACKAGE_TYPE, vo.getPackageType()));
		}
		page.setRecords(records);
		return ResultVO.page(page);
	}

	/**
	 * 智能终端管理系统-包-列表
	 */
	@RequestMapping(value = "/package/list", method = RequestMethod.GET)
	@ApiOperation(value = "智能终端管理系统-包-列表", responseContainer = "List", response = TerminalVersionManagementsDto.class)
	public ResultVO<?> packageList(@ApiParam(hidden = true) TerminalVersionManagementPageDto form) {
		Page page = null;
		if (form.getPage() == null || form.getPage()) {
			page = form.trainToPage();
		}
		List<TerminalVersionManagementsDto> records = terminalVersionManagementDao.findByPage(form, page);
		for (TerminalVersionManagementsDto vo : records) {
			vo.setPackageUrl(this.getPackageUrl(vo.getPackageName()));
			vo.setTerminalTypeDisplayName(dictService.dictValue(DictionaryConstants.TERMINAL_SYSTEM_TERMINAL_TYPE, vo.getTerminalType()));
			vo.setPackageTypeDisplayName(dictService.dictValue(DictionaryConstants.TERMINAL_SYSTEM_PACKAGE_TYPE, vo.getPackageType()));
		}
		if (page == null) {
			page = new Page<>(1, records.size());
			page.setTotal(records.size());
		}
		page.setRecords(records);
		return ResultVO.page(page);
	}

	/**
	 * 根据包名获取ftp访问路径
	 *
	 * @param packageName
	 * @return
	 */
	private String getPackageUrl(String packageName) {
		String packageUrl = pathPrefix + TerminalVersionManagementConstants.Package_Folder + "/" + packageName;
		try {
			// 格式化url
			packageUrl = new URI(packageUrl).normalize().toString();
		} catch (Exception ex) {
		}
		return packageUrl;
	}

	/**
	 * 智能终端管理系统-包-详情
	 */
	@RequestMapping(value = "/package/info/{id}", method = RequestMethod.GET)
	@ApiOperation(value = "智能终端管理系统-包-详情", responseContainer = "Map", response = TerminalVersionManagementsDto.class)
	@ApiImplicitParam(paramType = "path", name = "id", value = "编号", required = true, dataType = "String")
	public ResultVO<?> packageInfo(@PathVariable("id") String id) {
		TerminalVersionManagementsDto vo = terminalVersionManagementDao.findOneById(id);
		return ResultVO.success(vo);
	}

	@Transactional(rollbackFor = Exception.class)
	@RequestMapping(value = "upgrade", method = RequestMethod.POST)
	@ApiOperation(value = "智能终端管理系统-升级", notes = "{\"packageId\":\"6a5a67d616954dadba67d6b43786f098\",\"deviceList\":[\"0008_CmiWlV1c\"]}")
	public ResultVO<?> upgrade(@RequestBody @Validated TerminalSystemUpgradeDto form) {
		final String prisonId = UserContext.getJwtInfo().getPrisonId();
		//@formatter:off
		TerminalVersionManagementEntity packageEntity = terminalVersionManagementDao.selectOne(new LambdaQueryWrapper<TerminalVersionManagementEntity>()
				.eq(TerminalVersionManagementEntity::getId, form.getPackageId())
				.select(TerminalVersionManagementEntity::getId
						, TerminalVersionManagementEntity::getTerminalType
						, TerminalVersionManagementEntity::getPackageType
						, TerminalVersionManagementEntity::getPackageName
						, TerminalVersionManagementEntity::getState
						, TerminalVersionManagementEntity::getReleaseNotes
						, TerminalVersionManagementEntity::getVersionNumber
				)
		);
		//@formatter:on
//region 参数校验
		CommonServiceUtils.isEffectiveObj(packageEntity, form.getPackageId());
		// 1、apk;2:web包
		int packageType = 0;
		if ("apk".equals(packageEntity.getPackageType())) {
			packageType = 1;
		} else if ("web".equals(packageEntity.getPackageType())) {
			packageType = 2;
		} else {
			return ResultVO.error("无效的包类型", null);
		}
		if (packageEntity.getState() == null || packageEntity.getState() == 0) {
			return ResultVO.error("包不存在", null);
		}
		if (StringUtils.isBlank(packageEntity.getVersionNumber())) {
			return ResultVO.error("未找到该包的版本信息", null);
		}
//endregion
		// 去除设备信息中的无用内容 "_" 参考 com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalSystemDeviceVO.virtualId
		List<String> formDeviceList = form.getDeviceList();
		List<String> deviceList = formDeviceList.stream().filter(StringUtils::isNotBlank).map(TerminalSystemController::parseVirtualId).collect(Collectors.toList());
		// 查询出关联的设备信息
		TerminalSystemDeviceQueryDto queryDto = new TerminalSystemDeviceQueryDto();
		queryDto.setDeviceList(deviceList);
		List<TerminalSystemDeviceVO> deviceInfoList = new ArrayList<>();
		// 查询设备ip
		if ("0008".equals(packageEntity.getTerminalType()) || "0015".equals(packageEntity.getTerminalType())) {
			// 仓内外屏 设备
			deviceInfoList = terminalSystemDao.findDeviceCnpCwpList(queryDto, null);
		} else if ("0023".equals(packageEntity.getTerminalType())) {
			// 提人交接终端 设备
			deviceInfoList = terminalSystemDao.findDeviceTrjjzdList(queryDto, null);
		}
		if (deviceInfoList.isEmpty()) {
			return ResultVO.error("未找到要更新的设备信息", null);
		}

//region DB
		Date now = new Date();
		TerminalVersionUpgradeLogBatchEntity batchEntity = new TerminalVersionUpgradeLogBatchEntity();
		batchEntity.setTerminalType(packageEntity.getTerminalType());
		batchEntity.setPackageName(packageEntity.getPackageName());
		batchEntity.setPackageType(packageEntity.getPackageType());
		batchEntity.setUpgradeVersion(packageEntity.getVersionNumber());
		batchEntity.setUpgradeTime(new  Date());

		batchEntity.setVersionManagementId(packageEntity.getId());
		batchEntity.setReleaseNotes(packageEntity.getReleaseNotes());
		terminalVersionUpgradeLogBatchDao.insert(batchEntity);
		for (String virtualId : formDeviceList) {
			Optional<TerminalSystemDeviceVO> opt = deviceInfoList.stream().filter(f -> virtualId != null && virtualId.equals(f.getVirtualId())).findFirst();
			if (!opt.isPresent()) {
				continue;
			}
			TerminalSystemDeviceVO deviceInfo = opt.get();
			TerminalVersionUpgradeLogEntity logEntity = new TerminalVersionUpgradeLogEntity();
			logEntity.setId(UUID.randomUUID().toString().replaceAll("-", ""));
			logEntity.setSerialNumber(deviceInfo.getSerialNumber());
			logEntity.setIp(deviceInfo.getDeviceIp());
			logEntity.setApkVersion(deviceInfo.getApkVersion());
			logEntity.setWebVersion(deviceInfo.getWebVersion());
			logEntity.setUpgradeType(packageEntity.getPackageType());
			logEntity.setUpgradeVersion(packageEntity.getVersionNumber());
			logEntity.setOk(2);
			logEntity.setBatchId(batchEntity.getId());
			logEntity.setTerminalType(packageEntity.getTerminalType());
			logEntity.setDeviceName(deviceInfo.getDeviceName());
			logEntity.setAreaName(deviceInfo.getAreaName());
			terminalVersionUpgradeLogDao.insert(logEntity);
		}
//endregion

//region 升级
		{
			final String updateVer = packageEntity.getVersionNumber();
			final String downLoadUrl = this.getPackageUrl(packageEntity.getPackageName());
			final int packageType_final = packageType;
			final String batchId = batchEntity.getId();
			final List<TerminalSystemDeviceVO> deviceInfoList0 = deviceInfoList;
			// 已升级的设备ip。避免多设备ip配置重复 影响
			final List<String> upgradedIpList = new ArrayList<>();
			new Thread(new Runnable() {
				@Override
				public void run() {
					for (TerminalSystemDeviceVO dv : deviceInfoList0) {
						String ip = dv.getDeviceIp();
						try {
							if (StringUtils.isBlank(ip)) {
								throw new BaseException("设备id为空");
							}
							if (upgradedIpList.contains(ip)) {
								continue;
							}
							TerminalSystemCallbackArgsDto callbackArgs = new TerminalSystemCallbackArgsDto();
							callbackArgs.setBatchId(batchId);
							callbackArgs.setLinkId(dv.getLinkId());
							callbackArgs.setTerminalType(dv.getTerminalType());
							terminalService.updateApp(ip, packageType_final, updateVer, downLoadUrl, JSON.toJSONString(callbackArgs), true);
							upgradedIpList.add(ip);
						} catch (Exception ex) {
							try {
								String errMsg = (ex.getCause() == null ? ex : ex.getCause()).getMessage();
								errMsg = errMsg.substring(0, Math.min(errMsg.length(), 200));

								TerminalVersionUpgradeLogEntity entity2 = new TerminalVersionUpgradeLogEntity();
								entity2.setOk(0);
								entity2.setErrMsg(errMsg);
								//@formatter:off
								terminalVersionUpgradeLogDao.update(entity2, new LambdaQueryWrapper<TerminalVersionUpgradeLogEntity>()
										.eq(TerminalVersionUpgradeLogEntity::getBatchId, batchId)
										.eq(TerminalVersionUpgradeLogEntity::getSerialNumber, dv.getSerialNumber())
								);
								//@formatter:on
							} catch (Exception ignored) {
								log.error("", ignored);
							}
						}
					}
				}
			}).start();
		}
//endregion
		Map<String, Object> retData = new HashMap<>();
		retData.put("id", batchEntity.getId());
		return ResultVO.success(retData);
	}

	@RequestMapping(value = "/package/syncFtp", method = RequestMethod.GET)
	@ApiOperation(value = "智能终端管理系统-包-同步-扫描文件到表")
	public ResultVO<?> syncFtp() {
		terminalVersionManagementFileScanJob.run();
		return ResultVO.success();
	}

	@RequestMapping(value = "/device/syncVersionInfo", method = RequestMethod.GET)
	@ApiImplicitParam(name = "terminalType", value = "终端类型-固定（0008 仓内屏、0015 仓外屏、0023 提人交接终端。字典：TERMINAL_SYSTEM_TERMINAL_TYPE）", paramType = "query")
	@ApiOperation(value = "智能终端管理系统-设备-同步-同步设备版本信息")
	public ResultVO<?> syncVersionInfo(@RequestParam(value = "terminalType", required = true) String terminalType) {
		Thread thread = new Thread(new Runnable() {
			@Override
			public void run() {
				// 查询出关联的设备信息
				TerminalSystemDeviceQueryDto queryDto = new TerminalSystemDeviceQueryDto();
				List<TerminalSystemDeviceVO> deviceInfoList = new ArrayList<>();
				// 查询设备ip
				if ("0008".equals(terminalType) || "0015".equals(terminalType)) {
					// 仓内外屏 设备
					deviceInfoList = terminalSystemDao.findDeviceCnpCwpList(queryDto, null);
				} else if ("0023".equals(terminalType)) {
					// 提人交接终端 设备
					deviceInfoList = terminalSystemDao.findDeviceTrjjzdList(queryDto, null);
				}
				List<String> ipList =
						deviceInfoList.stream().map(TerminalSystemDeviceVO::getDeviceIp).filter(f -> StringUtils.isNotBlank(f) && !"null".equals(f)).distinct().collect(Collectors.toList());
				log.info("同步设备版本信息 {}", JSON.toJSONString(ipList));
				for (String ip : ipList) {
					try {
						terminalVersionManagementService.syncVersionInfo(ip);
					} catch (Exception ex) {
						log.error("", ex);
					}
				}
			}
		});
		thread.start();
		return ResultVO.success();
	}

	@RequestMapping(value = "upgradelog/upload", method = RequestMethod.POST)
	//@formatter:off
	@ApiOperation(value = "智能终端管理系统-升级日志-设备上报", notes = "{\"apkUpgradeTime\":1711450865000,\"apkVersion\":\"3.0.0-SP4\",\"callbackArgs\":\"{\\\"batchId\\\":\\\"753b062b7397499594036da50f0b3ff1\\\",\\\"linkId\\\":\\\"CmiWlV1c\\\",\\\"terminalType\\\":\\\"0008\\\"}\",\"errMsg\":\"\",\"ip\":\"**************\",\"ok\":1,\"serialNumber\":\"H01GXX231115242345\",\"terminalType\":\"0008\",\"upgradeTime\":1711450865000,\"upgradeType\":\"apk\",\"upgradeVersion\":\"v3.0.0-SP4\",\"webUpgradeTime\":1710830958000,\"webVersion\":\"v3.1.4\",\"compatibleWebVersions\":\"能兼容的web包版本\"}")
	//@formatter:on
	public ResultVO<?> upgradelogUpload(@RequestBody TerminalVersionUpgradeLogSaveDto form) {

		TerminalSystemCallbackArgsDto callbackArgsJson = null;
		String batchId = null;
		if (StringUtils.isNotBlank(form.getCallbackArgs())) {
			try {
				callbackArgsJson = JSON.parseObject(form.getCallbackArgs(), TerminalSystemCallbackArgsDto.class);
				if (callbackArgsJson != null) {
					batchId = StringUtils.isBlank(callbackArgsJson.getBatchId()) ? null : callbackArgsJson.getBatchId();
				}
			} catch (Exception ex) {
				log.error("", ex);
			}
		}
		TerminalVersionUpgradeLogEntity entity = new TerminalVersionUpgradeLogEntity();
		entity.setSerialNumber(form.getSerialNumber());
		entity.setIp(form.getIp());
		entity.setApkVersion(form.getApkVersion());
		entity.setWebVersion(form.getWebVersion());
		entity.setUpgradeType(form.getUpgradeType());
		entity.setUpgradeVersion(form.getUpgradeVersion());
		entity.setOk(form.getOk());
		entity.setErrMsg(form.getErrMsg());

		if (StringUtils.isBlank(batchId)) {
			// 最好的情况是不走该代码块。该块可考虑去除
			TerminalSystemUpgradeLogQueryDto queryForm = new TerminalSystemUpgradeLogQueryDto();
			queryForm.setIp(form.getIp());
			queryForm.setOk(2);
			batchId = terminalSystemDao.selectUpgradeLogBatchId(queryForm);
		}
		if (StringUtils.isBlank(batchId)) {
			log.warn("智能终端管理系统设备升级状态上报，未找到该版本对应批次 {}", JSON.toJSONString(form));
		} else {
			//@formatter:off
			terminalVersionUpgradeLogDao.update(entity, new LambdaQueryWrapper<TerminalVersionUpgradeLogEntity>()
					.eq(TerminalVersionUpgradeLogEntity::getIp, form.getIp())
					.eq(TerminalVersionUpgradeLogEntity::getBatchId, batchId)
			);
			//@formatter:on

			// 将其他批次 进行中的批次修改为失败
			TerminalVersionUpgradeLogEntity entity2 = new TerminalVersionUpgradeLogEntity();
			entity2.setOk(0);
			//@formatter:off
			terminalVersionUpgradeLogDao.update(entity2,  new LambdaQueryWrapper<TerminalVersionUpgradeLogEntity>()
					.eq(TerminalVersionUpgradeLogEntity::getIp, form.getIp())
					.eq(TerminalVersionUpgradeLogEntity::getOk, 2)
					.ne(TerminalVersionUpgradeLogEntity::getBatchId, batchId)
			);
			//@formatter:on

			// 更新 terminal_version_info 表。会写上最新的设备信息
			TerminalVersionInfoEntity infoEntity = new TerminalVersionInfoEntity();
			infoEntity.setSerialNumber(form.getSerialNumber());
			infoEntity.setIp(form.getIp());
			infoEntity.setCompatibleWebVersions(form.getCompatibleWebVersions());
			infoEntity.setApkUpgradeTime(form.getApkUpgradeTime());
			infoEntity.setWebUpgradeTime(form.getWebUpgradeTime());
			infoEntity.setUpdateTime(new Date());
			if ("apk".equals(form.getUpgradeType())) {
				infoEntity.setApkVersion(form.getUpgradeVersion());
				infoEntity.setWebVersion(form.getWebVersion());
			} else if ("web".equals(form.getUpgradeType())) {
				infoEntity.setWebVersion(form.getUpgradeVersion());
				infoEntity.setApkVersion(form.getApkVersion());
			}
			int effectRow = terminalVersionInfoDao.updateById(infoEntity);
			if (effectRow == 0) {
				terminalVersionInfoDao.insert(infoEntity);
			}
		}
		return ResultVO.success();
	}

	/**
	 * 终端运维升级状态同步。定时将 超出指定时间(${@link TerminalUpgradeLogStateHandler#upgradeLogStatePeriod})以前“进行中”的状态的 修改为升级失败
	 *
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "upgradelog/handler", method = RequestMethod.GET)
	@ApiOperation(value = "智能终端管理系统-升级'进行中'状态置为'失败'")
	public ResultVO<?> execute() throws Exception {
		terminalUpgradeLogStateHandler.execute();
		return ResultVO.success();
	}

	@ApiOperation(value = "智能终端管理系统-全局倒计时配置")
	@GetMapping(value = "/getConfig")
	public ResultVO<?> getConfig() {
		int config = terminalVersionInfoDao.getConfig();
		if(config==0){
		  config = 120;	//默认120s
		}
		return ResultVO.success(config);
	}

	private List<TerminalSystemDeviceVO> findDeviceList(String terminalType, TerminalSystemDeviceQueryDto queryDto, Page page) {
		List<TerminalSystemDeviceVO> deviceInfoList = new ArrayList<>();
		// 查询设备ip
		if ("0008".equals(terminalType) || "0015".equals(terminalType)) {
			// 仓内外屏 设备
			deviceInfoList = terminalSystemDao.findDeviceCnpCwpList(queryDto, page);
		} else if ("0023".equals(terminalType)) {
			// 提人交接终端 设备
			deviceInfoList = terminalSystemDao.findDeviceTrjjzdList(queryDto, page);
		} else {
			return null;
		}
		return deviceInfoList;
	}

	private static String parseVirtualId(String virtualId) {
		return virtualId.contains("_") ? virtualId.split("_")[1] : virtualId;
	}

	private static String parseVirtualTerminalType(String virtualId) {
		return virtualId.contains("_") ? virtualId.split("_")[0] : null;
	}

}
