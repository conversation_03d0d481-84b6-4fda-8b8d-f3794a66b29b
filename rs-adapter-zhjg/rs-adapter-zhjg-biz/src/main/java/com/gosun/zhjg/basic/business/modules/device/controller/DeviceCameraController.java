package com.gosun.zhjg.basic.business.modules.device.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gosun.zhjg.common.controller.BaseController;
import com.gosun.zhjg.common.msg.R;
import com.rs.module.acp.controller.admin.pm.vo.BaseDeviceCameraPageDto;
import com.rs.module.acp.controller.admin.pm.vo.BaseDeviceCameraPageVO;
import com.rs.module.acp.dao.pm.BaseDeviceCameraDao;
import com.rs.module.acp.service.pm.BaseDeviceCameraService;
import com.rs.module.base.vo.RoomVideoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备-摄像机
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-11-17 10:28:39
 */
@Api(value = "设备-摄像机", tags = "设备-摄像机")
@RestController
@RequestMapping("device/basedevice/camera")
public class DeviceCameraController extends BaseController {
    @Autowired
    private BaseDeviceCameraService baseDeviceCameraService;
    @Autowired
    private BaseDeviceCameraDao baseDeviceCameraDao;

    /**
     * 列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ApiOperation(value = "设备-摄像机-分页列表", responseContainer = "List", response = BaseDeviceCameraPageVO.class)
    public R<BaseDeviceCameraPageVO> list(@ApiParam(hidden = true) BaseDeviceCameraPageDto pageDto) {
        Page page = pageDto.trainToPage();
        List<BaseDeviceCameraPageVO> list=baseDeviceCameraService.findByPage(page,pageDto);
        page.setRecords(list);
        return (new R<BaseDeviceCameraPageVO>()).ResultPage(page);
    }

    /**
     * 信息
     */
    @RequestMapping(value = "/info/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "设备-摄像机-根据编号查找记录", responseContainer = "Map", response = BaseDeviceCameraPageVO.class)
    @ApiImplicitParam(paramType = "path", name = "id", value = "编号", required = true, dataTypeClass = String.class)
    public R<BaseDeviceCameraPageVO> info(@PathVariable("id") String id) {
        BaseDeviceCameraPageVO baseDeviceCamera =  baseDeviceCameraService.findOneById(id);
        return (new R<BaseDeviceCameraPageVO>()).ResultData(baseDeviceCamera);
    }

    @GetMapping(value = "/roomVideo/{roomId}")
    @ApiOperation(value = "监室关联视频", responseContainer = "List", response = RoomVideoVO.class)
    @ApiImplicitParam(paramType = "path", name = "roomId", value = "监室id", required = true, dataTypeClass = String.class)
    public R<List<RoomVideoVO>> roomVideo(@PathVariable("roomId") String roomId) {
    	List<RoomVideoVO> roomVideo = baseDeviceCameraDao.roomVideo(roomId);
    	return R.ResponseResult(roomVideo);
    }
}
