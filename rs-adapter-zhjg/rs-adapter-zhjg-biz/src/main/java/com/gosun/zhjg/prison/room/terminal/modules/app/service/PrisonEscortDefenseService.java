package com.gosun.zhjg.prison.room.terminal.modules.app.service;


import com.gosun.zhjg.prison.app.modules.yjlf.vo.PrisonSendReceiveMessageVO;
import com.gosun.zhjg.prison.room.terminal.config.socketio.EscortMessage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
*<AUTHOR>
*@email <EMAIL>
*@date 2020-07-23
*/

public interface PrisonEscortDefenseService {

    /***
     * 获取收发信息
     * @param id
     * @return
     */
    List<PrisonSendReceiveMessageVO> messageList(@Param("id") String id);

    String escortMessageSave(EscortMessage escortMessage);
}
