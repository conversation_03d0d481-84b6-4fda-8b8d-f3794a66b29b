package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 一日生活 制度模板 关联事务
 *
 * <AUTHOR>
 * @date 2023/4/17 14:16
 */
@Data
@TableName("prison_daily_life_template_event")
public class PrisonDailyLifeTemplateEventEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * $column.comments
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * $column.comments
     */
    private String templateId;
    /**
     * 时间范围开始。示例 01:12
     */
    private String startTime;
    /**
     * 时间范围截至
     */
    private String endTime;
    /**
     * 结束时间是否跨天。1跨天
     */
    private Integer endTimeSpan;
    /**
     * 逗号分隔，事务选项Id
     */
    private String eventId;
    /**
     * 逗号分隔，事务名称。仅做参考
     */
    private String eventName;
    /**
     * 是否启用语音播报。1 启用
     */
    private Integer voice;
    /**
     * $column.comments
     */
    @TableLogic
    private Integer delFlag;
}
