package com.gosun.zhjg.prison.room.terminal.modules.prison.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 监所基础信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-08-12 19:44:28
 */
@Data
@TableName("base_prison_info")
public class BasePrisonInfoEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 监所id
     */
	@TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 监所名称
     */
    private String prisonName;
    /**
     * 监所简称
     */
    private String prisonAbbreviation;
    /**
     * 组织机构id
     */
    private Integer orgId;
    /**
     * 状态
     */
    private String status;
    /**
     * 删除标记
     */
    @TableLogic
    private Integer delFlag;
    /**
     * 所领导用户id
     */
    private Integer leaderId;
    /**
     * 所领导名称
     */
    private String leaderName;
    
	/**
	 * $column.comments
	 */
	private Date createTime;
	/**
	 * $column.comments
	 */
	private Integer createUserId;
	/**
	 * $column.comments
	 */
	private String createUserName;
	/**
	 * $column.comments
	 */
	private Date updateTime;
	/**
	 * $column.comments
	 */
	private Integer updateUserId;
	/**
	 * $column.comments
	 */
	private String updateUserName;
	/**
	 * $column.comments
	 */
	private Integer prisonType;

	/***
	 * 所属支队
	 */
	@TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
	private String branchId;

	/***
	 * 0-非支队 1-支队
	 */
	private Integer isBranch;

	/**
	 * 地址
	 */
	private String dz;
	/**
	 * 详址
	 */
	private String xz;
	/**
	 * 电话
	 */
	private String dh;
	/**
	 * 传真
	 */
	private String cz;
	/**
	 * 邮政编码
	 */
	private String yzbm;
	/**
	 * 电子信箱
	 */
	private String dzxx;
	/**
	 * 监所等级
	 */
	private String jsdj;
	/**
	 * 监所规模
	 */
	private String jsgm;
	/**
	 * 民警总数
	 */
	private Integer mjzs;
	/**
	 * 编制人数
	 */
	private Integer bzrs;
	/**
	 * 监室数
	 */
	private Integer jsh;
	/**
	 * 设计容量
	 */
	private Integer sjrl;
	/**
	 * 协警
	 */
	private Integer xj;
	/**
	 * 职工
	 */
	private Integer zg;
	/**
	 * 文职
	 */
	private Integer wz;
	/**
	 * 建设时间
	 */
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JSONField(format="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	private Date jssj;
	/**
	 * 启用时间
	 */
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JSONField(format="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	private Date qysj;
	/**
	 * 单人监室
	 */
	private Integer drjs;
	/**
	 * 总建筑面积
	 */
	private Integer zjzmj;
	/**
	 * 监区面积
	 */
	private Integer jqmj;
	/**
	 * 驻所武警数
	 */
	private Integer zswjs;
	/**
	 * 询问室
	 */
	private Integer xws;
	/**
	 * 律师会见室
	 */
	private Integer lxhjs;
	/**
	 * 家属会见室
	 */
	private Integer jshjs;
	/**
	 * 医生数量
	 */
	private Integer yssl;
	/**
	 * 经度
	 */
	private String jd;
	/**
	 * 纬度
	 */
	private String wd;
	/**
	 * 地图坐标
	 */
	private String mapPostion;
	/**
	 * 地图id
	 */
	private String mapId;
	/**
	 * 标签位置
	 */
	private String mapTag;
	/**
	 * 提示框位置
	 */
	private String tskwz;

	/***
	 * 护士数量
	 */
	private Integer hssl;

	/**
	 * 地址中文
	 */
	private String dzName;

	/***
	 * 监所图片
	 */
	private String photoUrl;

	private String zbkssj;

	private String zbjssj;

}
