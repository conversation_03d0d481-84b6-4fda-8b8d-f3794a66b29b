package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
@ApiModel("科组长审核")
public class GroupReviewDTO {

    @ApiModelProperty("审核编号 批量逗号隔开")
    @NotBlank(message = "审核编号 不能为空")
    private String id;

    @ApiModelProperty("科/组长-审核状态 1-通过 2-不通过")
    @NotBlank(message = "科/组长-审核状态 不能为空")
    private String groupCheck;

    @ApiModelProperty("审核人")
    private String groupCheckUser;

    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date groupCheckTime;

    @ApiModelProperty("科/组长意见")
    private String groupCheckOpinion;
}
