package com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gosun.zhjg.common.exception.BaseException;
import com.gosun.zhjg.common.util.HttpUtil;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.TerminalServiceDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.TerminalService;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.TerminalServiceIPVo;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.TerminalServiceSerialAndMacVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 终端（内外屏）内置服务
 *
 * <AUTHOR>
 * @date 2023/9/11 19:32
 */
@Slf4j
@Service
public class TerminalServiceImpl implements TerminalService {

    /**
     * 内外屏 设备上服务 端口
     */
    @Value("${terminal.service.port:6897}")
    private int terminalServicePort;

    @Override
    public int getTerminalServicePort() {
        return this.terminalServicePort;
    }

    @Value("${terminal.service.timeout:5000}")
    private int timeout;

    @Autowired
    private TerminalServiceDao terminalServiceDao;

    @Override
    public JSONObject getConfigInfo(String ip) {
        try {
            String serviceUrl = String.format("%s/getConfigInfo", this.getServiceUrl(ip));
            String response = HttpUtil.Get(serviceUrl, timeout);
            return this.parseData(response, JSONObject.class);
        } catch (Exception e) {
            throw new BaseException("获取配置信息异常");
        }
    }

    /**
     * 升级
     *
     * @param ip          设备ip
     * @param packageType 1、apk;2:web包
     * @param updateVer   版本号
     * @param downLoadUrl
     */
    @Override
    public void updateApp(String ip, int packageType, String updateVer, String downLoadUrl) {
        this.updateApp(ip, packageType, updateVer, downLoadUrl, null, true);
    }

    /**
     * 升级
     *
     * @param ip           设备ip
     * @param packageType  1、apk;2:web包
     * @param updateVer    版本号
     * @param downLoadUrl  包下载地址
     * @param callbackArgs 任意参数，设备上报会携带该参数。可空
     * @param isAnsy       是否异步
     */
    @Override
    public void updateApp(String ip, int packageType, String updateVer, String downLoadUrl, String callbackArgs, boolean isAnsy) {
        try {
			log.warn("终端内置服务-设备更新 {}, {}, {}, {}, {}", ip, packageType, updateVer, downLoadUrl, callbackArgs);
            String serviceUrl = String.format("%s/updateApp", this.getServiceUrl(ip));
            JSONObject jsonData = new JSONObject();
            jsonData.put("packageType", packageType);
            jsonData.put("updateVer", updateVer);
            jsonData.put("downLoadUrl", downLoadUrl);
            jsonData.put("callbackArgs", callbackArgs);
            jsonData.put("isAnsy", isAnsy);
            String response = HttpUtil.PostJSON(serviceUrl, jsonData.toJSONString(), timeout);
            log.info(response);
            // {"data":"请求成功","returnCode":0,"returnMsg":"操作成功"}
            this.verifyResponseCode(response);
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            throw new BaseException("更新app异常", e);
        }
    }

	/**
	 * 删除人脸
	 *
	 * @param ip ip
	 * @param id police-a392616ee634413695d78c2f3c2d58e1
	 */
	@Override
	public void userDelete(String ip, String id) {
		try {
			log.warn("终端内置服务-人脸删除 {}, {}", ip, id);
			String serviceUrl = String.format("%s/systemManager/user/delete/%s", this.getServiceUrl(ip), id);
			String response = HttpUtil.Get(serviceUrl, timeout);
			log.info(response);
			// {"data":"删除成功","returnCode":0,"returnMsg":"操作成功"}
			this.verifyResponseCode(response);
		} catch (BaseException e) {
			throw e;
		} catch (Exception e) {
			throw new BaseException("删除人脸异常", e);
		}
	}

    /**
     * 重启
     *
     * @param ip
     */
    @Override
    public void reStartApp(String ip) {
        try {
            log.warn("终端内置服务-设备重启 {}", ip);
            String serviceUrl = String.format("%s/reStartApp", this.getServiceUrl(ip));
            String response = HttpUtil.Get(serviceUrl, timeout);
            log.info(response);
        } catch (Exception e) {
            throw new BaseException("重启app异常", e);
        }
    }

    @Override
    public void syncSerialAndMac(String deviceId,String prisonId) {
        List<TerminalServiceIPVo> list = terminalServiceDao.getDeviceList(deviceId,prisonId);
        for (TerminalServiceIPVo vo : list) {
            if (isIP(vo.getIpAddress())) {
                try {
                    String serviceUrl = String.format("%s/getConfigInfo", this.getServiceUrl(vo.getIpAddress()));
                    String response = HttpUtil.Get(serviceUrl, timeout);
                    TerminalServiceSerialAndMacVo macVo = this.parseData(response, TerminalServiceSerialAndMacVo.class);
                    if (macVo==null){
                        continue;
                    }
                    //仓内外屏
                    if (vo.getDeviceTypeId().equals("0008") || vo.getDeviceTypeId().equals("00015")){
                        if (StringUtils.isNotBlank(macVo.getDevSerial()) && ! macVo.getDevSerial().equals(vo.getSerialNumber())){
                            terminalServiceDao.updateSerialNumber(vo.getId(),macVo.getDevSerial());
                        }
                    }else {
                        if (StringUtils.isNotBlank(macVo.getMac()) && !macVo.getMac().equals(vo.getMacAddress())){
                            terminalServiceDao.updateMacAddress(vo.getId(),macVo.getMac());
                        }
                    }

                } catch (Exception e) {
                    log.error("序列号和mac地址同步失败！设备为d：{},ip为:{}",vo.getId(),vo.getIpAddress(),e);
                }
            }
        }

    }

    @Override
    public void saveConfigInfo(JSONObject object) throws Exception {
        String ip = object.getString("ip");
        if (StringUtils.isBlank(ip) || !isIP(ip)) {
            throw new RuntimeException("ip格式不正确！");
        }
        String serviceUrl = String.format("%s/saveServerConfig", this.getServiceUrl(ip));
        object.remove("ip");
        String response = HttpUtil.PostJSON(serviceUrl, object.toJSONString(), timeout);
        if (StringUtils.isBlank(response)) {
            throw new RuntimeException("接口返回空！");
        }
        JSONObject obj = JSON.parseObject(response);
        if (Objects.isNull(obj.getInteger("returnCode")) || 0 != obj.getInteger("returnCode")) {
            throw new RuntimeException("保存失败！返回：" + response);
        }
    }

    private String getServiceUrl(String ip) {
        return "http://" + ip + ":" + terminalServicePort;
    }

    /**
     * 从响应中解析出data
     *
     * @param response
     * @param clazz
     * @param <T>
     * @return
     */
    private <T> T parseData(String response, Class<T> clazz) {
        JSONObject obj = JSON.parseObject(response);
        T data = obj.getObject("data", clazz);
        return data;
    }

    /**
     * 校验接口响应状态
     *
     * @param response
     */
    private void verifyResponseCode(String response) {
        JSONObject obj = JSON.parseObject(response);
        if (!"0".equals(obj.getString("returnCode"))) {
            String returnMsg = obj.getString("returnMsg");
            if (returnMsg == null || returnMsg.isEmpty()) {
                returnMsg = response;
            }
            throw new BaseException(returnMsg);
        }
    }

    /**
     * 判断ip地址格式是否正确
     * @param addr
     * @return
     */
    private boolean isIP(String addr) {
        if (addr.length() < 7 || addr.length() > 15 || "".equals(addr)) {
            return false;
        }
        /**
         * 判断IP格式和范围
         */
        String rexp =
                "([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}";

        Pattern pat = Pattern.compile(rexp);

        Matcher mat = pat.matcher(addr);

        boolean ipAddress = mat.find();

        return ipAddress;
    }


}
