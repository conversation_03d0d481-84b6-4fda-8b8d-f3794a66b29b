package com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备版本信息表
 *
 * <AUTHOR>
 * @date 2023/9/6 15:50
 */
@Data
@TableName("acp_pm_terminal_version_info")
public class TerminalVersionInfoEntity extends BaseDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 序列号
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    private String serialNumber;
    /**
     * ip
     */
    private String ip;
    /**
     * 当前apk包版本
     */
    private String apkVersion;
    /**
     * 当前web包版本
     */
    private String webVersion;
    /**
     * 能兼容的web包版本
     */
    private String compatibleWebVersions;
    /**
     * apk更新时间
     */
    private Date apkUpgradeTime;
    /**
     * web更新时间
     */
    private Date webUpgradeTime;

}
