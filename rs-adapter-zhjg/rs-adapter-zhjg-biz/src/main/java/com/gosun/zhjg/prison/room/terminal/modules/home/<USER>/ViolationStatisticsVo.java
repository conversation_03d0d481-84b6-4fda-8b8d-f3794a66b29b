package com.gosun.zhjg.prison.room.terminal.modules.home.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 违规申诉内容 VO
 */
@Data
@ApiModel(value="ViolationAppealContentVO对象", description="违规数据统计")
public class ViolationStatisticsVo implements Serializable {
    @ApiModelProperty(value = "今日审核通过")
    private String todayPass;

    @ApiModelProperty(value = "今日审核驳回")
    private String todayReject;

    @ApiModelProperty(value = "本月审核总数")
    private String monthTotal;

    @ApiModelProperty(value = "所选监室待审核")
    private String roomUnreviewed;

}
