package com.gosun.zhjg.prison.room.terminal.modules.msg.service.impl;

import com.alibaba.fastjson.JSON;
import com.gosun.zhjg.common.constant.KafkaTopicsConstants;
import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.prison.room.terminal.config.socketio.PushMessage;
import com.gosun.zhjg.prison.room.terminal.config.socketio.kafka.product.KafkaProduct;
import com.gosun.zhjg.prison.room.terminal.config.socketio.kafka.product.RabbitProduct;
import com.gosun.zhjg.prison.room.terminal.modules.msg.dto.SendMsgDto;
import com.gosun.zhjg.prison.room.terminal.modules.msg.service.SendMsgService;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.SocketService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("sendMsgService")
public class SendMsgServiceImpl implements SendMsgService {

    @Autowired
    private RabbitProduct rabbitProduct;

    @Autowired
    private SocketService socketService;

    @Override
    public R sendMsg(SendMsgDto dto) {
        PushMessage message1=new PushMessage();
        message1.setContent(dto.getMsg());
        try {
            List<String> serialNumbers = socketService.getCnpSerialNumberByRoomId(dto.getRoomIds());
            PushMessageForm pushFrom = new PushMessageForm();
            pushFrom.setSerialNumbers(serialNumbers);
            pushFrom.setTarget(SocketActionConstants.PushMessageTargetEnum.web.name());
            pushFrom.setTerminal(SocketActionConstants.PushMessageTerminalEnum.CNP.name());
            pushFrom.setAction(SocketActionConstants.swSendMsg);
            Object[] params =new Object[1];
            params[0]=dto.getMsg();
            pushFrom.setParams(params);
            String form = JSON.toJSONString(pushFrom);
            rabbitProduct.sendMsg(KafkaTopicsConstants.Kafkatopic_PushMessageToSerialNumber, form);
        }catch (Exception e) {
            e.printStackTrace();
            return R.ResponseError(101, "消息发送失败!");
        }
        return R.ResponseOk("消息发送成功:"+dto.getMsg());
    }
}
