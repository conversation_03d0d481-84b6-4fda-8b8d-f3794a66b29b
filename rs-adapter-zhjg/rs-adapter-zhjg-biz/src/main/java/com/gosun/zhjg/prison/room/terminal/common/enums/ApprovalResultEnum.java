package com.gosun.zhjg.prison.room.terminal.common.enums;

import lombok.Getter;

/**
 * 数据库审批结果枚举
 *
 * <AUTHOR>
 * @date 2024/9/18
 */
@Getter
public enum ApprovalResultEnum {

    /**
     * 1：同意
     */
    AGREE(1,"同意"),
    /**
     * 2：不同意
     */
    DISAGREE(2,"不同意"),
    ;

    private final Integer status;
    private final String name;

    ApprovalResultEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }
}
