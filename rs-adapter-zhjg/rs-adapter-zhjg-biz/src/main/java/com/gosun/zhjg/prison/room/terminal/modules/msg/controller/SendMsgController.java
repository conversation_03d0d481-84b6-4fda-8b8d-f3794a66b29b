package com.gosun.zhjg.prison.room.terminal.modules.msg.controller;

import com.gosun.zhjg.common.controller.BaseController;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.prison.room.terminal.modules.msg.dto.SendMsgDto;
import com.gosun.zhjg.prison.room.terminal.modules.msg.service.SendMsgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "三维地图-消息播报发送", tags = "三维地图-消息播报发送")
@RestController
@RequestMapping("msg")
public class SendMsgController extends BaseController {

    @Autowired
    private SendMsgService sendMsgService;

    /**
     * 列表
     */
    @RequestMapping(value = "/sendMsg", method = RequestMethod.POST)
    @ApiOperation(value = "消息播报发送")
    public R sendMsg(@RequestBody SendMsgDto dto) {
        return sendMsgService.sendMsg(dto);
    }

}
