package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * prisoner_out_in_message 实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date: 2023-08-15
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value = "PrisonerOutInMessageDTO对象", description = "带入带出-消息提醒表")
public class PrisonerOutInMessageDTO implements Serializable {


    @JsonProperty("prisonerId")
    @ApiModelProperty(value = "在押人员编号")
    private String prisonerId;


    @JsonProperty("type")
    @ApiModelProperty(value = "1-带出 2-带入")
    private Integer type;


    @JsonProperty("content")
    @ApiModelProperty(value = "消息内容")
    private String content;


    @JsonProperty("status")
    @ApiModelProperty(value = "1-已处理")
    private Integer status;


    @JsonProperty("busnessType")
    @ApiModelProperty(value = "1-提讯 2-家属会见 3-律师会见")
    private Integer busnessType;


    @JsonProperty("createUser")
    @ApiModelProperty(value = "创建账号")
    private String createUser;


    @JsonProperty("prisonId")
    @ApiModelProperty(value = "监所编号")
    private String prisonId;



}

