package com.gosun.zhjg.prison.room.terminal.modules.remote.feign;

import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.gosun.zhjg.common.msg.R;

@FeignClient(value = "zhjg-prison-work")
public interface PrisonWorkFeign {

	/**
	 * 未登录首页-值班-获取一周值班表
	 * 
	 * {@link com.gosun.zhjg.prison.work.modules.gj.controller.PrisonRoomDutyController.list(PrisonRoomDutyPageDto)}
	 */
	@RequestMapping(value = "/gj/prisonroomduty/list", method = RequestMethod.GET)
	public R<?> prisonroomdutyList(@RequestParam Map<String, Object> form);

	/**
	 * 未登录首页-床位-监室床位-列表
	 * 
	 * {@link com.gosun.zhjg.prison.work.modules.gj.controller.BasePrisonerBedController.roomBedListOther(BasePrisonerBedOtherDto)}
	 */
	@RequestMapping(value = "/gj/basePrisonerBed/roomBedListOther", method = RequestMethod.GET)
	public R<?> roomBedListOther(@RequestParam Map<String, Object> form);

	/**
	 * 民警登录-人脸补录-获取列表
	 * 
	 * {@link com.gosun.zhjg.prison.work.modules.gj.controller.FaceToFaceManageController.facingPage(String)}
	 * 
	 * @return
	 */
	@GetMapping("/gj/facetofacemanage/findByRoomInfo")
	public R<?> facetofacemanageFacingPage(@RequestParam("id") String id);
}
