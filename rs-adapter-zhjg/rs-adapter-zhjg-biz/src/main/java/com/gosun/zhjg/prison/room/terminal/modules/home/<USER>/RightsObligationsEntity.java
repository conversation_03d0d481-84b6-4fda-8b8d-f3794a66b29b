package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.*;

import lombok.Data;

/**
 * rights_obligations 实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date: 2024-06-18
 */
@Data
@TableName("rights_obligations")
public class RightsObligationsEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @TableId(type = IdType.UUID)
    private String id;
    /**
     * 人员编号
     */
    private String prisonerId;
    /**
     * 签名状态 1-已完成
     */
    private Integer signStatus;
    /**
     * 指纹状态 1-已完成
     */
    private Integer fingerprintStatus;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 创建用户编号
     */
    private String createUserId;
    /**
     * 创建人
     */
    private String createUserName;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 更新人用户编号
     */
    private String updateUserId;
    /**
     * 更新人
     */
    private String updateUserName;
    /**
     * 单位代码
     */
    private String prisonId;
    /**
     * 下发状态
     */
    private Integer distributionStatus;
    /**
     * 告知书url
     */
    private String noticeLetterUrl;
    /**
     * 签名图片url
     */
    private String signImageUrl;
    /**
     * 签名pdf url
     */
    private String signPdfUrl;
    /**
     * 指纹转pdf url
     */
    private String fingerprintPdfUrl;
    /**
     * 指纹图片url
     */
    private String fingerprintImageUrl;
    /**
     * 告知书合成pdf
     */
    private String noticePdfUrl;
    /**
     * 删除标志
     */
    private Integer delFlag;
    /**
     * 签名时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date signTime;
    /**
     * 指纹时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date fingerprintTime;
}
