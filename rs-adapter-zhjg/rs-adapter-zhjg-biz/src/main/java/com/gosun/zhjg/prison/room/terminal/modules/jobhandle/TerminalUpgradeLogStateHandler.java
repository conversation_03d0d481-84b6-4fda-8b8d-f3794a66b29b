package com.gosun.zhjg.prison.room.terminal.modules.jobhandle;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.TerminalVersionUpgradeLogDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.TerminalVersionUpgradeLogEntity;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.handler.IJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 智能终端管理系统-升级'进行中'状态置为'失败'。定时将 超出指定时间(${@link TerminalUpgradeLogStateHandler#upgradeLogStatePeriod})以前“进行中”的状态的 修改为升级失败
 * <p>
 * "0 4/10 * * * ?" 10分钟执行一次
 *
 * <AUTHOR>
 * @date 2024/3/21 8:57
 */
@Slf4j
@Component
public class TerminalUpgradeLogStateHandler extends IJobHandler implements InitializingBean {

	@Autowired
	private TerminalVersionUpgradeLogDao terminalVersionUpgradeLogDao;

	@Value("${terminal.upgradeLogState.period:15}")
	public Integer upgradeLogStatePeriod;

	@Override
	public void afterPropertiesSet() throws Exception {
		XxlJobExecutor.registJobHandler("TerminalUpgradeLogStateHandler", this);
	}

	@Override
	public void execute() throws Exception {
		int period = this.upgradeLogStatePeriod;

		String jobParam = XxlJobHelper.getJobParam();
		if (StringUtils.isNotBlank(jobParam)) {
			String[] arr = jobParam.split("#");
			if (arr.length > 0 && NumberUtils.isNumber(arr[0])) {
				period = Integer.parseInt(arr[0]);
			}
		}

		Date now = new Date();
		Date date = new Date(now.getYear(), now.getMonth(), now.getDate(), now.getHours(), now.getMinutes() - period, now.getSeconds());
		//@formatter:off
		List<TerminalVersionUpgradeLogEntity> terminalVersionUpgradeLogEntities = terminalVersionUpgradeLogDao.selectList(new LambdaQueryWrapper<TerminalVersionUpgradeLogEntity>()
						.lt(TerminalVersionUpgradeLogEntity::getAddTime, date)
						.eq(TerminalVersionUpgradeLogEntity::getOk, 2)
						.select(TerminalVersionUpgradeLogEntity::getId)
				);
		//@formatter:on
		List<String> ids = terminalVersionUpgradeLogEntities.stream().map(TerminalVersionUpgradeLogEntity::getId).collect(Collectors.toList());
		if (!ids.isEmpty()) {
			TerminalVersionUpgradeLogEntity entity = new TerminalVersionUpgradeLogEntity();
			entity.setOk(0);
			terminalVersionUpgradeLogDao.update(entity, new LambdaQueryWrapper<TerminalVersionUpgradeLogEntity>().in(TerminalVersionUpgradeLogEntity::getId, ids));
		}
	}
}
