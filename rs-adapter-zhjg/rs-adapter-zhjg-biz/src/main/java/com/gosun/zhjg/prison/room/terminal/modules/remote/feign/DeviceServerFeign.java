package com.gosun.zhjg.prison.room.terminal.modules.remote.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.alibaba.fastjson.JSONObject;
import com.gosun.zhjg.common.msg.R;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

/**
 * 
 * @param serialNum
 * @return
 */
@FeignClient(value = "zhjg-device-server")
public interface DeviceServerFeign {

	/**
	 * 渲染页面前-根据序列号获取监室信息,设备ip等
	 * 
	 * {@link com.gosun.zhjg.device.server.modules.inscreen.controller.BaseDeviceInscreenController.getBySerialNum(String)}
	 * 
	 * @return
	 */
	@RequestMapping(value = "/inscreen/basedeviceinscreen/getBySerialNum/{serialNum}", method = RequestMethod.GET)
	@ApiImplicitParam(paramType = "path", name = "serialNum", value = "序列号", required = true, dataType = "String")
	public R<?> getBySerialNum(@PathVariable("serialNum") String serialNum);

	/**
	 * 渲染页面前-仓内屏人脸录入失败日志记录表-插入日志
	 * 
	 * {@link com.gosun.zhjg.device.server.modules.inscreen.controller.BaseDeviceInscreenLogController.save(BaseDeviceInscreenLogSaveDto)} 仓内屏人脸录入失败日志记录表-插入日志
	 */
	@RequestMapping(value = "/inscreen/basedeviceinscreenlog/insert", method = RequestMethod.POST)
	@ApiOperation(value = "仓内屏人脸录入失败日志记录表-插入日志", responseContainer = "Map")
	public R<?> basedeviceinscreenlogSave(@RequestBody JSONObject form);
}
