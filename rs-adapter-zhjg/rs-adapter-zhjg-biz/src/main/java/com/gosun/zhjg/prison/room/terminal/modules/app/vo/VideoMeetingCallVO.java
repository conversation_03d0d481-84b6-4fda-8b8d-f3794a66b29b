package com.gosun.zhjg.prison.room.terminal.modules.app.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 视频会议相关
 *
 * <AUTHOR>
 * @date 2022/7/12 11:38
 */
@Data
public class VideoMeetingCallVO {

    @ApiModelProperty("true 邀请方，false，受邀方")
    private Boolean intiter;

    @ApiModelProperty("视频远端设备编号")
    private Long remoteDevId;

//    private String ws;
//
//    @ApiModelProperty(value = "会议ID", required = false)
//    private Long meetingId;
//
//    @ApiModelProperty("参会连接信息-设备编号")
//    private Long devId;
//
//    @ApiModelProperty("参会连接信息")
//    private String username;
//
//    @ApiModelProperty("参会连接信息")
//    private String password;
}
