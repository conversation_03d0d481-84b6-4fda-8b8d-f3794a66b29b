package com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备版本升级日志
 *
 * <AUTHOR>
 * @date 2023/9/6 15:50
 */
@Data
@TableName("acp_pm_terminal_version_upgrade_log")
public class TerminalVersionUpgradeLogEntity extends BaseDO implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_UUID)

    private String id;

    /**
     * 序列号
     */
    private String serialNumber;
    /**
     * $column.comments
     */
    private String ip;
    /**
     * 之前apk包版本
     */
    private String apkVersion;
    /**
     * 之前web包版本
     */
    private String webVersion;
    /**
     * 更新的包类型 (packageType)   apk、web
     */
    private String upgradeType;
    /**
     * 要更新版本
     */
    private String upgradeVersion;
    /**
     * 是否更新成功 1、0、2进行中
     */
    private Integer ok;


    /**
     * acp_pm_terminal_version_upgrade_log_batch表id
     */
    private String batchId;
    private String errMsg;
    private String terminalType;
    private String deviceName;
    private String areaName;
}
