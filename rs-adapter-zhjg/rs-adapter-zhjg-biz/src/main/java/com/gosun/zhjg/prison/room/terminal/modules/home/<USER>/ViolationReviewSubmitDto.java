package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * DTO for sacp_prison_violation_event
 */
@Data
@ApiModel("违规申诉提交")
public class ViolationReviewSubmitDto{

    @NotNull
    @ApiModelProperty(value = "关联违规登记ID")
    private String violationEventId;

    @NotNull
    @ApiModelProperty(value = "被监管人员编号")
    private String prisonerId;

    @NotNull
    @ApiModelProperty(value = "被监管人员姓名")
    private String prisonerName;

    @NotNull
    @ApiModelProperty(value = "监室号")
    private String roomId;

    @NotNull
    @ApiModelProperty(value = "监室名称")
    private String roomName;


    @ApiModelProperty(value = "申诉时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date appealTime;

    @ApiModelProperty(value = "申诉理由")
    private String appealOpinion;

    @ApiModelProperty(value = "审核状态")
    private String reviewStatus;

    @NotNull
    @ApiModelProperty(value = "监所编码")
    private String prisonId;

    @ApiModelProperty(value = "登记时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

} 