package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2023/8/25 17:01
 */
@Data
public class PrisonerReportRepairContentSaveDTO {

    @ApiModelProperty(value = "报修内容id(不传为新增)")
    private String id;

    @NotNull(groups = ValidInsertGroup.class, message = "报修内容不能为空")
    @ApiModelProperty(value = "报修内容", required = true)
    private String contentName;

    @ApiModelProperty(value = "监所编码", hidden = true)
    private String prisonId;

    @Valid
    @ApiModelProperty(value = "报修原因列表", required = true)
    private List<PrisonerReportRepairReasonSaveDTO> reasonList;

}
