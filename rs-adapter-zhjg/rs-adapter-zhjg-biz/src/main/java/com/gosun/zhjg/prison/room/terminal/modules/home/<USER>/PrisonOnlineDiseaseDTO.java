package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
* prison_online_disease 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2024-04-12
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="PrisonOnlineDiseaseDTO对象", description="仓内屏-在线报病")
public class PrisonOnlineDiseaseDTO implements Serializable {


    @JsonProperty("prisonerId")
    @ApiModelProperty(value = "在押人员编号",required = true)
    @NotBlank(message = "在押人员编号 不能为空")
    private String prisonerId;

    @JsonProperty("roomId")
    @ApiModelProperty(value = "监室号",required = true)
    @NotBlank(message = "监室号 不能为空")
    private String roomId;

    @JsonProperty("diseaseTypeId")
    @ApiModelProperty(value = "疾病类型id 多个逗号隔开",required = true)
    @NotBlank(message = "疾病类型 不能为空")
    private String diseaseTypeId;


    @JsonProperty("diseaseSymptomId")
    @ApiModelProperty(value = "疾病症状id，多个逗号隔开",required = true)
    @NotBlank(message = "疾病症状 不能为空")
    private String diseaseSymptomId;


}

