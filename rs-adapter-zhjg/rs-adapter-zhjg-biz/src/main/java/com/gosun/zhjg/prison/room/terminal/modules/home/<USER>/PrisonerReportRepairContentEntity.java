package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Builder;
import java.util.Date;

/**
 * @Title 设备设施报修表
 * @Description  
 * <AUTHOR> 
 * @Date 2023-08-25 
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName( "prisoner_report_repair_content" )
public class PrisonerReportRepairContentEntity  implements Serializable {

	private static final long serialVersionUID =  1617414992010896257L;

   	@TableField("id")
	@ApiModelProperty("主键")
	@TableId(value = "id", type = IdType.UUID)
	private String id;

   	@TableField("status")
	@ApiModelProperty("状态(0-禁用,1-启用)")
	private Integer status;

   	@TableField("content_name")
	@ApiModelProperty("报修内容")
	private String contentName;

   	@TableField("prison_id")
	@ApiModelProperty("监所编码")
	private String prisonId;

   	@TableField("create_time")
	@ApiModelProperty("创建时间")
	private Date createTime;

   	@TableField("update_time")
	@ApiModelProperty("更新时间")
	private Date updateTime;

}
