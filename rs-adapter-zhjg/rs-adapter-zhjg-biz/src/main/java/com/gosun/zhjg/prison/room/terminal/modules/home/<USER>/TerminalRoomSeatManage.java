package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("监室内务座位安排")
public class TerminalRoomSeatManage {

    /**
     * 主键
     */
    @TableId(type = IdType.UUID)
    @ApiModelProperty("主键id")
    private String id;

    /**
     * 监室id
     */
    @ApiModelProperty("监室id")
    private String roomId;

    /**
     * 监所id
     */
    @ApiModelProperty("监所id")
    private String prisonId;

    /**
     * 监室内务座位安排表格标题
     */
    @ApiModelProperty("监室内务座位安排表格标题")
    private String title;

    /**
     * 更新/设置人id
     */
    @ApiModelProperty("更新/设置人id")
    private String updateUserId;

    /**
     * 更新/设置人姓名
     */
    @ApiModelProperty("更新/设置人姓名")
    private String updateUserName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 更新/设置时间
     */
    @ApiModelProperty("更新/设置时间")
    private LocalDateTime updateTime;
}
