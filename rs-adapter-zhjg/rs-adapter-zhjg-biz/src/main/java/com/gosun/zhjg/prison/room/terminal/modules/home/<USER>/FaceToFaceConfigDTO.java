package com.gosun.zhjg.prison.room.terminal.modules.home.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.gosun.zhjg.prison.room.terminal.modules.home.entity.PrisonFaceToFaceConfigEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * 面对面管理-仓内屏配置返回DTO
 *
 * <AUTHOR>
 * @date 2025/1/11
 */
@Data
@ApiModel("面对面管理-仓内屏配置返回DTO")
public class FaceToFaceConfigDTO implements Serializable {

    @ApiModelProperty(value = "主键ID", required = true)
    private String id;

    @ApiModelProperty(value = "检查内容")
    private String checkItems;

    @ApiModelProperty(value = "状态(0-禁用,1-启用)")
    private Integer status;

    @ApiModelProperty(value = "监所ID，默认-1")
    private String prisonId;

    @ApiModelProperty(value = "监室ID")
    private String roomId;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "创建用户ID")
    private String createUserId;

    @ApiModelProperty(value = "创建用户姓名")
    private String createUserName;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "更新用户ID")
    private String updateUserId;

    @ApiModelProperty(value = "更新用户姓名")
    private String updateUserName;

    /**
     * 根据 PO 对象生成 DTO 对象
     * @param source PO 对象
     * @return DTO 对象
     */
    public static FaceToFaceConfigDTO of(PrisonFaceToFaceConfigEntity source) {
        if (source == null) {
            return null;
        }
        FaceToFaceConfigDTO target = new FaceToFaceConfigDTO();
        BeanUtils.copyProperties(source,target);
        return target;
    }

}

