package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
* non_involved_items 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2024-06-07
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="NonInvolvedItemsUpdateDTO对象", description="编辑")
public class NonInvolvedItemsUpdateDTO implements Serializable {

    @JsonProperty("id")
    @ApiModelProperty(value = "主键",required = true)
    @NotBlank(message = "编辑id不能为空")
    private String id;

    @JsonProperty("itemStatus")
    @NotNull(message = "物品状态 不能为空")
    @ApiModelProperty(value = "物品状态",required = true)
    private Integer itemStatus;

    @JsonProperty("removalTime")
    @ApiModelProperty(value = "取出时间",required = true)
    @NotNull(message = "取出时间 不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date removalTime;

    @JsonProperty("removalUserTel")
    @NotBlank(message = "取出人 不能为空")
    @ApiModelProperty(value = "取出人以及联系方式",required = true)
    private String removalUserTel;


}

