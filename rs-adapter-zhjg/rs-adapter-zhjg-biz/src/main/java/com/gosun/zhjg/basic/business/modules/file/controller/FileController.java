package com.gosun.zhjg.basic.business.modules.file.controller;


import com.gosun.zhjg.basic.business.modules.file.dto.GetFileListDto;
import com.gosun.zhjg.basic.business.modules.file.vo.BaseFileVo;
import com.gosun.zhjg.common.msg.R;
import io.swagger.annotations.Api;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * ftp文件管理
 *
 * <AUTHOR>
 */
@Api(value = "ftp文件管理", tags = "ftp文件管理")
@RestController
@RequestMapping("/file")
public class FileController {

    @Autowired
    private FileStorageService fileStorageService;

    /**
     * @param file
     * @param directory 文件储存目录，可为空
     * @param fileName  实际储存文件名，可为空
     * @return
     * @throws IOException
     */
    //上传文件（ftp + nginx)（多文件上传或单文件上传）
    @PostMapping("/ftp/upload")
    public R upload(@RequestParam("file") MultipartFile file, @RequestParam(value = "directory", required = false) String directory
            , @RequestParam(value = "fileName", required = false) String fileName) {
        BaseFileVo baseFileVo = new BaseFileVo();
        String httpPath;
        try {
            httpPath = uploadFile(file);
            if (httpPath == null) {
                return R.ResponseError();
            } else {
                baseFileVo.setAccessUrl(httpPath);
                baseFileVo.setOriginalFilename(file.getOriginalFilename());
                return R.ResponseResult(baseFileVo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return R.ResponseError(e.getMessage());
        }
    }


    public String uploadFile(MultipartFile file) {
        FileInfo fileInfo = fileStorageService.of(file)
                .setHashCalculatorMd5()
                .setSaveFilename(file.getOriginalFilename()) //设置保存的文件名，不需要可以不写，会随机生成
                .upload();  //将文件上传到对应地方
        return fileInfo.getUrl();
    }

    /**
     * 获取指定目录下文件列表
     *
     * @return
     * @throws IOException
     */
    @PostMapping("/ftp/getFileList")
    public R<?> getFileList(@RequestBody GetFileListDto form) throws IOException {
        List<String> fileList = null;
        return R.ResponseResult(fileList);
    }
}
