package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 监室值日-值日事务管理
 *
 * <AUTHOR>
 * @date 2024/11/1 8:58
 */
@Data
@TableName("terminal_room_day_duty_shift_manage")
public class TerminalRoomDayDutyShiftManageEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@TableId(type = IdType.ASSIGN_UUID)

	private String id;
	/**
	 * 单位代码
	 */
	private String prisonId;
	/**
	 * 值日内务名称
	 */
	private String shiftName;
	/**
	 * 生效日期
	 */
	private Date effectiveStartDate;
	/**
	 * 失效日期，不包含该日期
	 */
	private Date effectiveEndDate;
	/**
	 * 批次号、版本号
	 */
	private Long versionNo;
	/**
	 * $column.comments
	 */
	private String createUserId;
	/**
	 * $column.comments
	 */
	private String createUserName;
	/**
	 * $column.comments
	 */
	private Date createTime;
	/**
	 * $column.comments
	 */
	private String updateUserId;
	/**
	 * $column.comments
	 */
	private String updateUserName;
	/**
	 * $column.comments
	 */
	private Date updateTime;
	/**
	 * 排序字段，从小到大
	 */
	private Integer sortOrder;
}
