package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * person_represent-个人点名表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-06-30 09:29:58
 */
@Data
@TableName("person_represent")
public class PersonRepresentEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 在押人员id
     */
    private String prisonerId;
    /**
     * 姓名
     */
    private String prisonerName;
    /**
     * 签到状态
     */
    private String signStatus;
    /**
     * 是否外出（1：是
     */
    private String isOut;
    /**
     * 外出原因
     */
    private String outReason;
    /**
     * 签到时间
     */
    private Date signTime;
    /**
     * 关联监室点名表id
     */
    private String roomPresentId;

    /***
     * 温度
     */
    private String temperature;

    /***
     * 1-正常 2-异常
     */
    private Integer temperatureState;

    /***
     * 报备状态
     */
    private Integer isReport;
}
