package com.gosun.zhjg.prison.room.terminal.modules.jobhandle;

import com.gosun.zhjg.prison.room.terminal.modules.socket.service.TerminalOnlineStateJob;
import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.handler.IJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 仓内外屏在线状态同步
 * <p>
 * "0 0 0/2 * * ?"
 *
 * <AUTHOR>
 * @date 2022/12/27 16:31
 */
@Slf4j
@Component
public class TerminalOnlineStateHandler extends IJobHandler implements InitializingBean {

    @Autowired
    private TerminalOnlineStateJob terminalOnlineStateJob;

    @Override
    public void afterPropertiesSet() throws Exception {
        XxlJobExecutor.registJobHandler("TerminalOnlineStateHandler", this);
    }

    @Override
    public void execute() throws Exception {
        terminalOnlineStateJob.job();
    }
}
