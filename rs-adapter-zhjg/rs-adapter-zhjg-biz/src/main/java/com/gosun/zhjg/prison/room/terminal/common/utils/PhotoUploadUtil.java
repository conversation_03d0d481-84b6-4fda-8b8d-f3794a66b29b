package com.gosun.zhjg.prison.room.terminal.common.utils;


import com.gosun.zhjg.prison.room.terminal.config.FtpConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.io.IOException;
import java.util.Map;


/**
 * <AUTHOR>
 * ftp上传工具
 */
@Component
@Slf4j
public class PhotoUploadUtil {
    @Autowired
    private FtpConfig ftpConfig;

    @Autowired
    private Base64Util base64Util;

    public String prisonerPhotoUpload(Map map) throws IOException {
        String newName = map.get("newName").toString();
        String savePath = map.get("savePath").toString();
        String photoBase = map.get("photoBase").toString();
        String httpPath = base64Util.base64ToFile(ftpConfig, newName, savePath, photoBase);
        return httpPath;
    }
}
