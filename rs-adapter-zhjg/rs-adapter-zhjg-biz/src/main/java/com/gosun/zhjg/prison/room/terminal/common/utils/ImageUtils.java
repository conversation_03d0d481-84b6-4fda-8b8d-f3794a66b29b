package com.gosun.zhjg.prison.room.terminal.common.utils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

public class ImageUtils {

    public void test() throws IOException {
        File file = new File("C:\\Users\\<USER>\\Pictures\\wanglei.jpg");
        BufferedImage image = ImageIO.read(file);
        this.lumAdjustment(image, 1.30f);
        File file2 = new File("C:\\Users\\<USER>\\Pictures\\wanglei2.jpg");
        file2.createNewFile();
        ImageIO.write(image, "JPG", file2);
    }

    /**
     * 图片亮度调整
     *
     * @param image
     * @param param
     * @throws IOException
     */
    public static void lumAdjustment(BufferedImage image, float param) throws IOException {
        if (image == null) {
            return;
        } else {
            int rgb, R, G, B;
            for (int i = 0; i < image.getWidth(); i++) {
                for (int j = 0; j < image.getHeight(); j++) {
                    rgb = image.getRGB(i, j);
                    R = (int) (((rgb >> 16) & 0xff) * param);
                    G = (int) (((rgb >> 8) & 0xff) * param);
                    B = (int) ((rgb & 0xff) * param);
                    rgb = ((clamp(255) & 0xff) << 24) | ((clamp(R) & 0xff) << 16) | ((clamp(G) & 0xff) << 8)
                            | ((clamp(B) & 0xff));
                    image.setRGB(i, j, rgb);
                }
            }
        }
    }

    public static BufferedImage lumAdjustment2(BufferedImage image, float param) throws IOException {
        if (image == null) {
            return null;
        } else {
            BufferedImage img2 = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_3BYTE_BGR);
            int rgb, R, G, B;
            for (int i = 0; i < image.getWidth(); i++) {
                for (int j = 0; j < image.getHeight(); j++) {
                    rgb = image.getRGB(i, j);
                    R = (int) (((rgb >> 16) & 0xff) * param);
                    G = (int) (((rgb >> 8) & 0xff) * param);
                    B = (int) ((rgb & 0xff) * param);
                    rgb = ((clamp(255) & 0xff) << 24) | ((clamp(R) & 0xff) << 16) | ((clamp(G) & 0xff) << 8)
                            | ((clamp(B) & 0xff));
                    img2.setRGB(i, j, rgb);
                }
            }
            return img2;
        }
    }

    // 判断a,r,g,b值，大于256返回256，小于0则返回0,0到256之间则直接返回原始值
    private static int clamp(int rgb) {
        if (rgb > 255)
            return 255;
        if (rgb < 0)
            return 0;
        return rgb;
    }

}
