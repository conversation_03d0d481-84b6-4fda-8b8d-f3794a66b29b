package com.gosun.zhjg.prison.room.terminal.modules.login.service.impl;

import com.gosun.zhjg.prison.room.terminal.common.constant.PrisonRoomConstant;
import com.gosun.zhjg.prison.room.terminal.modules.login.dao.TerminalLoginDao;
import com.gosun.zhjg.prison.room.terminal.modules.login.dto.LoginUserInfoDto;
import com.gosun.zhjg.prison.room.terminal.modules.login.service.TerminalLoginService;
import com.gosun.zhjg.prison.room.terminal.modules.login.vo.LoginInfoVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 登录service实现类
 *
 * <AUTHOR> on 2020/6/10
 * @email <EMAIL>
 * @date 2020/6/10
 */
@Service
public class TerminalLoginServiceImpl implements TerminalLoginService {

    @Autowired
    private TerminalLoginDao loginDao;
    
    @Override
    public LoginInfoVO login(LoginUserInfoDto loginUserInfoDto) {
        String appCode=loginUserInfoDto.getAppCode();
        LoginInfoVO infoVO=null;
        if(!StringUtils.isEmpty(appCode)){
            //判断为民警还是在押人员
            String type =appCode.split("-")[0];
            String code =appCode.split("-")[1];
            if(type.equals(PrisonRoomConstant.police)){
                //警员，查询警员表
                infoVO=loginDao.findByPolice(code);
            }else {
                //在押人员，查询在押人员表
                infoVO=loginDao.findByPrisoner(code);
            }
            if(infoVO!=null){
                infoVO.setType(type);
                infoVO.setCode(code);
            }
            return infoVO;
        }
        return null;
    }
}