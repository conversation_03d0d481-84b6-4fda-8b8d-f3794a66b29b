package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class FamilyPhoneNumberApproveDTO {

    @ApiModelProperty(value = "审批id",required = true)
    @NotBlank(message = "审批id 不能为空,多个")
    private String id;

    @ApiModelProperty("审批意见")
    private String reason;

    @ApiModelProperty(value = "2-审核通过 3-驳回",required = true)
    @NotNull(message = "审批意见 不能为空")
    private Integer approveStatus;
}
