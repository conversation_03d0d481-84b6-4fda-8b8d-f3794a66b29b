package com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 仓内屏entity
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-09-15 10:55:29
 */
@Data
@TableName("acp_pm_device_inscreen")
public class BaseDeviceInscreenEntity extends BaseDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 序列号
     */
    private String serialNumber;
    /**
     * 设备编号
     */
    private String deviceId;
    /**
     * 设备ip
     */
    private String deviceIp;
    /**
     * 监室号
     */
    private String roomId;
    /**
     * 设备编号
     */
    private int deviceNum;
    /**
     * 主机编号
     */
    private int hostNum;
    /**
     * 地址盒IP
     */
    private String addressIp;

    /**
     * 设备类型 1仓内屏，2仓外屏
     */
    //设备名称
    private String deviceName;

    private Integer deviceType;

}
