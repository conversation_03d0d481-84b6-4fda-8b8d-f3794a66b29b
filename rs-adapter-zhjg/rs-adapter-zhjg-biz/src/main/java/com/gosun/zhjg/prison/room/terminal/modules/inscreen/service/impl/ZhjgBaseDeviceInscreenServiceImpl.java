package com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.gosun.zhjg.auth.common.util.jwt.IJWTInfo;
import com.gosun.zhjg.common.constant.BaseSystemRuleConfigConstants;
import com.gosun.zhjg.common.constant.KafkaTopicsConstants;
import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.common.constant.WdaDictionaryConstants;
import com.gosun.zhjg.common.enums.DeviceTerminalTypeEnum;
import com.gosun.zhjg.common.exception.BaseException;
import com.gosun.zhjg.common.msg.ResultVO;
import com.gosun.zhjg.prison.room.terminal.common.service.DictService;
import com.gosun.zhjg.prison.room.terminal.config.socketio.kafka.product.RabbitProduct;
import com.gosun.zhjg.prison.room.terminal.modules.home.dto.BaseSystemRuleConfigKeyValueDto;
import com.gosun.zhjg.prison.room.terminal.modules.home.service.BaseSystemRuleConfigService;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.BaseDeviceInscreenDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.DeviceInscreenUpdateLogDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.BaseDeviceInscreenPageDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.BaseDeviceTerminalPageDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.BaseDeviceTerminalTypeDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.BaseRoomDeviceTerminalDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.BaseDeviceInscreenEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.DeviceInscreenUpdateLogEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.ZhjgBaseDeviceInscreenService;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.*;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.rs.framework.common.entity.OpsDicCode;
import com.rs.framework.mybatis.util.DicUtil;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.base.enums.AreaTypeEnum;
import com.rs.module.base.service.pm.AreaService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service("zhjgBaseDeviceInscreenService")
public class ZhjgBaseDeviceInscreenServiceImpl implements ZhjgBaseDeviceInscreenService {

    private static final Logger logger = LoggerFactory.getLogger(ZhjgBaseDeviceInscreenServiceImpl.class);
    @Autowired
    private BaseDeviceInscreenDao deviceInscreenDao;

    @Autowired
    private DeviceInscreenUpdateLogDao deviceInscreenUpdateLogDao;

    @Autowired
    private DictService dictService;

    @Autowired
    private BaseSystemRuleConfigService baseSystemRuleConfigService;

    @Autowired
    private RabbitProduct rabbitProduct;
    @Autowired
    private AreaService areaService;

    @Override
    public List<BaseDeviceInscreenPageVO> findByPage(Page page, BaseDeviceInscreenPageDto pageDto) {
        return deviceInscreenDao.findByPage(page, pageDto);
    }

    @Override
    public BaseDeviceInscreenVO findOneById(String id) {
        return deviceInscreenDao.findOneById(id);
    }

    @Override
    public BaseDeviceInscreenVO getBySerialNum(String serialNum) {
        return deviceInscreenDao.getBySerialNum(serialNum);
    }

    @Override
    public BaseDeviceInscreenVO getByDeviceNum(Integer deviceNum) {
        return deviceInscreenDao.getByDeviceNum(deviceNum);
    }

    @Override
    public BaseDeviceInscreenVO getByRoom(String roomId) {
        return deviceInscreenDao.getByRoom(roomId);
    }

    @Override
    public List<BaseDeviceTerminalVo> getRoomDeviceList(Page page, BaseDeviceTerminalPageDto dto) {
        List<BaseDeviceTerminalVo> list = deviceInscreenDao.getRoomDeviceList(page, dto);
        return list;
    }

    @Override
    public Map getRoomDeviceByRoomId(String roomId) {
        HashMap<String, Object> map = new HashMap<>();
        BaseDeviceTerminalInfoVo internalDevice = deviceInscreenDao.getDeviceInscreenByRoom(roomId, DeviceTerminalTypeEnum.CNP.getKey());
        if (internalDevice != null && StringUtils.isNotBlank(internalDevice.getDeviceStatus())) {
            internalDevice.setDeviceStatusDisplayName(dictService.dictValue(WdaDictionaryConstants.C_SBZTDM, internalDevice.getDeviceStatus()));
        }
        //仓内屏
        map.put("internalDevice", internalDevice);
        BaseDeviceTerminalInfoVo warehouseDevice = deviceInscreenDao.getDeviceInscreenByRoom(roomId, DeviceTerminalTypeEnum.CWP.getKey());
        if (warehouseDevice != null && StringUtils.isNotBlank(warehouseDevice.getDeviceStatus())) {
            warehouseDevice.setDeviceStatusDisplayName(dictService.dictValue(WdaDictionaryConstants.C_SBZTDM, warehouseDevice.getDeviceStatus()));
        }
        //仓外屏
        map.put("warehouseDevice", warehouseDevice);
        return map;
    }

    @Override
    public List<BaseDeviceTerminalInfoVo> getDeviceListByType(Page page, BaseDeviceTerminalTypeDto dto) {
        List<BaseDeviceTerminalInfoVo> list = deviceInscreenDao.getDeviceListByType(page, dto);
        for (BaseDeviceTerminalInfoVo vo : list) {
            List<String> roomNameList = deviceInscreenDao.getRoomNameByDeviceId(vo.getDeviceId());
            StringBuffer roomName = new StringBuffer();
            for (int i = 0; i < roomNameList.size(); i++) {
                if (i < roomNameList.size() - 1) {
                    roomName.append(roomNameList.get(i)).append("、");
                } else {
                    roomName.append(roomNameList.get(i));
                }
            }
            vo.setRoomName(roomName.toString());
            vo.setBindCount(roomNameList.size());
        }
        return list;
    }

    @Override
    public ResultVO updateRoomDevice(BaseRoomDeviceTerminalDto dto, IJWTInfo user) {
        Date date = new Date();
        if (dto.getDeviceType() == DeviceTerminalTypeEnum.CNP.getKey()) {
            String deviceId = dto.getDeviceId();
            String roomId = dto.getRoomId();
            if (StringUtils.isBlank(dto.getDeviceId())) {
                deviceId = dto.getOldDeviceId();
                roomId = null;
            }
            deviceInscreenDao.updateRoomTerminal(roomId, deviceId, user.getUserid());
        } else {
            if (StringUtils.isNotBlank(dto.getDeviceId())) {
                QueryWrapper<BaseDeviceInscreenEntity> qw = new QueryWrapper<>();
                qw.eq("device_id", dto.getDeviceId());
                qw.eq("device_type", dto.getDeviceType());
                List<BaseDeviceInscreenEntity> inscreenEntityList = deviceInscreenDao.selectList(qw);
                if (inscreenEntityList.isEmpty() || inscreenEntityList.size() < 1) {
                    return ResultVO.error("监室终端配置表中没有设备配置！", null);
                }
                String roomTerminal = deviceInscreenDao.getRoomTerminal(null, dto.getRoomId(), dto.getDeviceType());
                if (StringUtils.isNotBlank(roomTerminal)) {
                    return ResultVO.error("监室已配置仓外屏设备，请刷新页面！", null);
                }
                Boolean isHad = false;
                for (BaseDeviceInscreenEntity entity : inscreenEntityList) {
                    if (StringUtils.isBlank(entity.getRoomId())) {
                        entity.setRoomId(dto.getRoomId());
                        deviceInscreenDao.updateById(entity);
                        isHad = true;
                        break;
                    }
                }
                if (!isHad) {
                    BaseDeviceInscreenEntity entity = inscreenEntityList.get(0);
                    BaseDeviceInscreenEntity entity1 = new BaseDeviceInscreenEntity();
                    CopyOptions copyOptions = CopyOptions.create();
                    copyOptions.setIgnoreNullValue(true);
                    copyOptions.setIgnoreProperties("id");
                    BeanUtil.copyProperties(entity, entity1, copyOptions);
                    entity1.setRoomId(dto.getRoomId());
                    deviceInscreenDao.insert(entity1);
                }
            } else {
                if (StringUtils.isNotBlank(dto.getOldDeviceId())) {
                    QueryWrapper<BaseDeviceInscreenEntity> qw = new QueryWrapper<>();
                    qw.eq("device_id", dto.getOldDeviceId());
                    qw.eq("device_type", dto.getDeviceType());
                    qw.eq("room_id", dto.getRoomId());
                    LambdaUpdateWrapper<BaseDeviceInscreenEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                    lambdaUpdateWrapper.set(BaseDeviceInscreenEntity::getRoomId, null)
                            .eq(BaseDeviceInscreenEntity::getDeviceId, dto.getOldDeviceId())
                            .eq(BaseDeviceInscreenEntity::getDeviceType, dto.getDeviceType())
                            .eq(BaseDeviceInscreenEntity::getRoomId, dto.getRoomId());
                    if("2".equals(dto.getDeviceType())){
                        lambdaUpdateWrapper.set(BaseDeviceInscreenEntity::getIsDel,1);
                    }
                    deviceInscreenDao.update(null, lambdaUpdateWrapper);

                }
            }
        }
        DeviceInscreenUpdateLogEntity updateLogEntity = new DeviceInscreenUpdateLogEntity();
        updateLogEntity.setRoomId(dto.getRoomId());
        updateLogEntity.setDeviceId(StringUtils.isNotBlank(dto.getDeviceId()) ? dto.getDeviceId() : null);
        deviceInscreenUpdateLogDao.insert(updateLogEntity);
        //刷新仓内外屏页面及人脸数据
        refreshDevice(dto);
        return ResultVO.success("配置成功！");
    }


    public void refreshDevice(BaseRoomDeviceTerminalDto dto) {
        try {
            String terminal = null;
            List<String> serialNumbers = new ArrayList<>();
            if (dto.getDeviceType() == DeviceTerminalTypeEnum.CNP.getKey()) {
                terminal = SocketActionConstants.PushMessageTerminalEnum.CNP.name();
            } else {
                terminal = SocketActionConstants.PushMessageTerminalEnum.CWP.name();
            }
            if (StringUtils.isNotBlank(dto.getDeviceId())) {
                List<BaseDeviceInscreenVO> deviceInscreenVOList = deviceInscreenDao.getByDeviceId(dto.getDeviceId());
                for (BaseDeviceInscreenVO vo : deviceInscreenVOList) {
                    if (StringUtils.isNotBlank(vo.getSerialNumber())) {
                        serialNumbers.add(vo.getSerialNumber());
                    }
                }
            }
            if (StringUtils.isNotBlank(dto.getOldDeviceId())) {
                List<BaseDeviceInscreenVO> deviceInscreenVOList = deviceInscreenDao.getByDeviceId(dto.getOldDeviceId());
                for (BaseDeviceInscreenVO vo : deviceInscreenVOList) {
                    if (StringUtils.isNotBlank(vo.getSerialNumber())) {
                        serialNumbers.add(vo.getSerialNumber());
                    }
                }
            }
            PushMessageForm pushFrom = new PushMessageForm();
            pushFrom.setSerialNumbers(serialNumbers);
            pushFrom.setTarget(SocketActionConstants.PushMessageTargetEnum.web.name());
            pushFrom.setTerminal(terminal);
            pushFrom.setAction(SocketActionConstants.webReload);
            String form = JSON.toJSONString(pushFrom);
            rabbitProduct.sendMsg(KafkaTopicsConstants.Kafkatopic_PushMessageToSerialNumber, form);
        } catch (Exception e) {
            logger.error("Error occurred while refreshing device: ", e);
        }
    }

//    @Override
//    public void saveBaseDeviceInscreen(BaseDeviceInscreenSaveDto dto, String userid, String userName) {
//        BaseDeviceInscreenEntity entity = new BaseDeviceInscreenEntity();
//        entity.setId(UUIDUtils.generateShortUuid());
//        entity.setSerialNumber(dto.getSerialNumber());
//        entity.setDeviceId(dto.getDeviceId());
//        entity.setDeviceIp(dto.getDeviceIp());
//        entity.setRoomId(dto.getRoomId());
//        deviceInscreenDao.insert(entity);
//    }
//
//    @Override
//    public void updateBaseDeviceInscreen(BaseDeviceInscreenUpdateDto dto, String userid, String userName) {
//        BaseDeviceInscreenEntity entity = deviceInscreenDao.selectById(dto.getId());
//        entity.setSerialNumber(dto.getSerialNumber());
//        entity.setDeviceId(dto.getDeviceId());
//        entity.setDeviceIp(dto.getDeviceIp());
//        entity.setRoomId(dto.getRoomId());
//        deviceInscreenDao.updateById(entity);
//    }
//
//    @Override
//    public void deleteByIds(String[] ids) {
//        List<BaseDeviceInscreenEntity> entityList = deviceInscreenDao.selectBatchIds(Arrays.asList(ids));
//        for (BaseDeviceInscreenEntity entity : entityList) {
//            deviceInscreenDao.deleteById(entity.getId());
//        }
//    }


    @Override
    public CnpDeviceInfoVO getCnpInfo(String serialNum) {
        // 基本设备信息
        CnpDeviceInfoVO vo = deviceInscreenDao.getCnpDeviceInfo(serialNum);
        if (vo == null) {
            logger.warn("=====> 仓内屏序列号获取监室信息 serialNum={} 未找到监室信息！", serialNum);
            throw new BaseException("未找到监室信息");
        }
        //@formatter:off 系统配置
		List<BaseSystemRuleConfigKeyValueDto> ruleConfigList = baseSystemRuleConfigService.getMultiItemValue(vo.getPrisonId(),
				Lists.newArrayList(BaseSystemRuleConfigConstants.TERMINAL_CNP_PAGE_OPERATION_TIME
                    , BaseSystemRuleConfigConstants.TERMINAL_CNP_SCREEN_PROTECTION_ENABLE
				));
		vo.setRuleConfigList(ruleConfigList);
		//@formatter:on
        vo.setAppCode(getAppCode(vo.getPrisonId()));
        AreaDO areaDO = areaService.lambdaQuery()
                .eq(AreaDO::getOrgCode, vo.getPrisonId())
                .eq(AreaDO::getAreaType, AreaTypeEnum.DETENTION_FACILITY.getCode())
                .one();
        if (areaDO != null) {
            vo.setPrisonName(areaDO.getAreaName());
        }

        return vo;
    }

    @Override
    public CwpInfoVO getCwpInfo(String serialNum) {
        // 基本设备信息
        List<CwpDeviceInfoVO> records = deviceInscreenDao.getCwpDeviceInfo(serialNum);
        if (records == null || records.isEmpty()) {
            logger.warn("=====> 仓外屏序列号获取监室信息 serialNum={} 未找到监室信息！", serialNum);
            throw new BaseException("未找到监室信息");
        }
        String prisonId = records.get(0).getPrisonId();
        //@formatter:off 系统配置
		List<BaseSystemRuleConfigKeyValueDto> ruleConfigList = baseSystemRuleConfigService.getMultiItemValue(prisonId,
				Lists.newArrayList(BaseSystemRuleConfigConstants.TERMINAL_CWP_PAGE_OPERATION_TIME
						, BaseSystemRuleConfigConstants.TERMINAL_CWP_SHOW_PRISONER_PHOTO_NAME
						, BaseSystemRuleConfigConstants.TERMINAL_CWP_HOME_PAGE_TALKBACK_ENABLE
				));
        String appCode = "";
        records.forEach(record -> {
            AreaDO areaDO = areaService.getPrisonByOrgCode(record.getPrisonId());
            if (areaDO != null){
                record.setPrisonName(areaDO.getAreaName());
            }
        });
        CwpInfoVO vo = new CwpInfoVO();
        vo.setList(records);
        vo.setAppCode(getAppCode(records.get(0).getPrisonId()));
        vo.setRuleConfigList(ruleConfigList);
        return vo;

    }
    private static String getAppCode(String orgCode) {
        String appCode = "";
        List<OpsDicCode> opsDicCodeList = DicUtil.getDic("ZD_APP_CODE_REF", "bsp");
        for (OpsDicCode opsDicCode : opsDicCodeList) {
            if (orgCode.equals(opsDicCode.getCode())) {
                appCode = opsDicCode.getName();
                break;
            }
        }
        if (StringUtils.isBlank(appCode)) {
            throw new BaseException("未找到appCode,请检查bsp字典ZD_APP_CODE_REF:"+JSON.toJSONString(opsDicCodeList)+"orgCode:"+orgCode);
        }
        return appCode;
    }

}
