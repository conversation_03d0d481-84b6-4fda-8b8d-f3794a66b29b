package com.gosun.zhjg.prison.room.terminal.modules.inscreen.controller;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.text.Collator;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.imageio.ImageIO;

import org.apache.commons.lang3.StringUtils;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.util.StringUtil;
import com.google.common.collect.Lists;
import com.gosun.zhjg.basic.business.modules.file.feign.FileApi;
import com.gosun.zhjg.basic.business.modules.file.vo.BaseFileVo;
import com.gosun.zhjg.common.config.UserContext;
import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.common.controller.BaseController;
import com.gosun.zhjg.common.enums.UserIsPoliceEnum;
import com.gosun.zhjg.common.exception.BaseException;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.common.util.FileUrlUtils;
import com.gosun.zhjg.prison.room.terminal.common.constant.PrisonRoomConstant;
import com.gosun.zhjg.prison.room.terminal.common.utils.ImageUtils;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.BaseDeviceInscreenDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.CnpFaceDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.CnpFaceForm;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.CnpFacePageDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.CnpFaceEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.CnpFaceService;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.CnpFaceCountVO;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.CnpFaceDeviceInscreenVO;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.CnpFaceImportStateVO;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.CnpfacePrisonerVO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.SocketService;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.impl.CnpServiceImpl;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.PushMessageAckVO;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.framework.mybatis.util.BspDbUtil;

import cn.hutool.core.codec.Base64;
import cn.hutool.db.Entity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;


@Api(tags = "人脸库管理")
@RestController
@Slf4j
@RequestMapping("inscreen/cnpface")
public class CnpFaceController extends BaseController {

    @Value("${file-url-prefix}")
    private String pathPrefix;

    @Autowired
    private CnpFaceDao cnpFaceDao;

    @Autowired
    private SocketService socketService;

    @Autowired
    private CnpFaceService cnpFaceService;

    @Autowired(required = false)
    private FileApi fileApi;
    
    @Autowired
    private FileStorageService fileStorageService;

    @Autowired
    private BspApi bspApi;

    public static final String PersonnelType_Prefix_Police = "police";
    private final String PersonnelType_Prefix_Prisoner = "prisoner";
    private final String PersonnelType_Separator = "_";
    @Autowired
    private BaseDeviceInscreenDao baseDeviceInscreenDao;


    @RequestMapping(value = "/prisonerPage", method = RequestMethod.GET)
    @ApiOperation(value = "人脸库管理-被监管人员照片补录照片列表", responseContainer = "List", response = CnpfacePrisonerVO.class)
    public R<CnpfacePrisonerVO> prisonerPage(@ApiParam(hidden = true) CnpFacePageDto form) {
        form.setPrisonId(UserContext.getJwtInfo().getPrisonId());
        Page page = form.trainToPage();
        List<CnpfacePrisonerVO> list = cnpFaceDao.findPrisonerPage(page, form);
        for (CnpfacePrisonerVO vo : list) {
            if (StringUtils.isBlank(vo.getPhoto())) {
                vo.setPhoto(vo.getPrisonerPhoto());
            }
            vo.setPhoto(FileUrlUtils.concatUrl(vo.getPhoto(), pathPrefix));
            vo.setPersonnelType(PersonnelType_Prefix_Prisoner);
        }
        page.setRecords(list);
        return R.ResponsePage(page);
    }

    /**
     * 列表
     */
    @RequestMapping(value = "/importStatePage", method = RequestMethod.GET)
    @ApiOperation(value = "人脸库管理-导入台账", responseContainer = "List", response = CnpFaceImportStateVO.class)
    public R<CnpFaceImportStateVO> importStatePage(@ApiParam(hidden = true) CnpFacePageDto form) {
        if (StringUtils.isBlank(form.getPrisonId())) {
            form.setPrisonId(UserContext.getJwtInfo().getPrisonId());
        }
        List<CnpFaceImportStateVO> list;
        Page page = form.trainToPage();
        if (PersonnelType_Prefix_Prisoner.equals(form.getPersonnelType())) {
            // 查询被监管人员
            list = cnpFaceDao.findPrisonerImportStatePage(page, form);
            for (CnpFaceImportStateVO vo : list) {
                vo.setPersonnelTypeDisplayName("被监管人员");
                vo.setPersonnelType(form.getPersonnelType());
            }
        } else if (form.getPersonnelType() != null && form.getPersonnelType().startsWith(PersonnelType_Prefix_Police + PersonnelType_Separator) && form.getPersonnelType().length() > 7) {
            // 查询民警
            String ispolice = form.getPersonnelType().split("_")[1];
            form.setIspolice(Integer.parseInt(ispolice));
            list = cnpFaceDao.findPoliceImportStatePage(page, form);
            for (CnpFaceImportStateVO vo : list) {
                vo.setPersonnelTypeDisplayName(UserIsPoliceEnum.getDesc(vo.getPersonnelType()));
                vo.setPersonnelType(form.getPersonnelType());
            }
        } else {
            return R.ResponseError(400, "参数错误 personnelType");
        }
        page.setRecords(list);
        return R.ResponsePage(page);
    }

    @RequestMapping(value = "/importFace", method = RequestMethod.POST)
    @ApiOperation(value = "导入照片", responseContainer = "List")
    public R<?> importFace(@RequestBody CnpFaceForm form) {
        if (StringUtils.isBlank(form.getPhoto())) {
            return R.ResponseError(400, "参数错误 photo不能为空");
        }
        Integer personnelType;
        if (PersonnelType_Prefix_Prisoner.equals(form.getPersonnelType())) {
            personnelType = 2;
        } else if (form.getPersonnelType() != null && form.getPersonnelType().startsWith(PersonnelType_Prefix_Police)) {
            personnelType = 1;
        } else {
            return R.ResponseError(400, "参数错误 personnelType");
        }
        CnpFaceEntity entity = new CnpFaceEntity();
        String photo = FileUrlUtils.cutPath(form.getPhoto());
        entity.setPhoto(photo);
        entity.setUpdateTime(new Date());
        int updateRows = cnpFaceDao.update(entity, new LambdaQueryWrapper<CnpFaceEntity>()
                .eq(CnpFaceEntity::getPersonnelType, personnelType)
                .eq(CnpFaceEntity::getPersonnelCode, form.getPersonnelId()));
        if (updateRows == 0) {
            entity.setId(UUID.randomUUID().toString().replaceAll("-", ""));
            entity.setPersonnelType(personnelType);
            entity.setPersonnelCode(form.getPersonnelId());
            cnpFaceDao.insert(entity);
        }
        
        if (form.getPersonnelType().startsWith(PersonnelType_Prefix_Police)) {
            BspDbUtil.updateUserFacePic(photo, form.getPersonnelId());
        }

        //同步人脸数据到bsp
//        Map<String, Object> resultMap = bspApi.registerFace(form.getPersonnelId(), photo, personnelType);
//        if(resultMap != null && resultMap.containsKey("success") && (Boolean)resultMap.get("success") == false) {
//        	return R.responseError(resultMap.get("detailedMsg").toString());
//        }

        // 异步下发到指定设备
        new Thread(new Runnable() {
            @Override
            public void run() {
                List<CnpFaceDeviceInscreenVO> records = null;
                if (PersonnelType_Prefix_Prisoner.equals(form.getPersonnelType())) {
                    records = cnpFaceDao.getPrisonerAssociatedDevices(form.getPersonnelId());
                } else if (form.getPersonnelType() != null && form.getPersonnelType().startsWith(PersonnelType_Prefix_Police)) {
                    records = cnpFaceDao.getPoliceAssociatedDevices(form.getPersonnelId(), null);
                }
                if (records != null && records.size() > 0) {
                    // skipImportSerialNumbers 需要忽略录入的设备
                    List<String> skipImportSerialNumbers = form.getSkipImportSerialNumbers() == null ? new ArrayList<>() : form.getSkipImportSerialNumbers();
                    List<String> serialNumbers = records.stream().map(CnpFaceDeviceInscreenVO::getSerialNumber)
                            .filter(f -> StringUtils.isNotBlank(f) && !skipImportSerialNumbers.contains(f))
                            .distinct().collect(Collectors.toList());
                    cnpFaceService.asyncEnrollFace(personnelType, form.getPersonnelId(), form.getPhoto(), serialNumbers);
                }
            }
        }).start();
        return R.ResponseOk();
    }
    
    @PostMapping(value = "/importFaceBatch")
    @ApiOperation(value = "批量导入人脸照片")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "facePhotoPath", value = "人脸图片路径", required = true, dataType = "String"),
            @ApiImplicitParam(name = "personnelType", value = "人员类型(民警: police_1, 辅警: police_0, 其他: police_-1, 被监管人员: prisoner)",
            	required = true, dataType = "String")
    })
    public R<?> importFaceBatch(@RequestParam("facePhotoPath") String facePhotoPath,
    		@RequestParam("personnelType") String personnelType) {
    	File faceFile = new File(facePhotoPath);
    	if(faceFile.exists()) {
    		int count = 0;
    		if(faceFile.isDirectory()) {
    			for(File photoFile : faceFile.listFiles()) {
    				CnpFaceForm form = buildCnpFaceForm(photoFile, personnelType);
    				if(form != null) {
    					importFace(form);
    					count ++;
    				}
    			}
    		}
    		else {
    			CnpFaceForm form = buildCnpFaceForm(faceFile, personnelType);
    			if(form != null) {
    				importFace(form);
    				count ++;
				}
    		}
    		
    		return R.ResponseOk("共导入[" + count + "]张人脸图片");
    	}
    	else {
    		return R.responseError("找不到人脸图片路径");
    	}
    }
    
    /**
     * 构建人脸Form
     * @param faceFile File 人脸图片文件
     * @param personnelType String 人员类型(民警: police_1, 辅警: police_0, 其他: police_-1, 被监管人员: prisoner)
     * @return CnpFaceForm
     */
    private CnpFaceForm buildCnpFaceForm(File faceFile, String personnelType) {
    	if(faceFile != null && faceFile.exists()) {
    		
    		//获取人员Id
        	String photoFileName = faceFile.getName();
    		int pos = photoFileName.lastIndexOf(".");
    		String personnelId = pos > 0 ? photoFileName.substring(0, pos) : photoFileName;
    		String suffix = photoFileName.substring(pos + 1);
    		String fileName = StringUtil.getGuid32() + "." + suffix;
    		
    		//上传人脸图片
        	FileInfo fileInfo = fileStorageService.of(faceFile)
                    .setHashCalculatorMd5()
                    .setSaveFilename(fileName)
                    .upload();
        	
        	//构建人脸Form
    		CnpFaceForm form = new CnpFaceForm();
    		form.setPersonnelId(personnelId);
    		form.setPersonnelType(personnelType);
    		form.setPhoto(fileInfo.getUrl());
    		
    		return form;
    	}
    	
		return null;
    }
    
    @GetMapping(value = "/updateUserFaceUrl")
    @ApiOperation(value = "更新用户人脸图片地址")
    public R<?> updateUserFaceUrl() {
    	try {
    		List<Entity> userFaceList = BspDbUtil.getAllUserFace();
        	for(Entity entity : userFaceList) {
        		String id = entity.getStr("ID");
        		String faceImage = entity.getStr("FACE_IMAGE");
        		String faceUrl = entity.getStr("FACE_URL");
        		if(StringUtil.isEmpty(faceUrl) && StringUtil.isNotEmpty(faceImage)) {
        			if(faceImage.startsWith("http")) {
        				BspDbUtil.updateUserFaceUrl(faceUrl, id);
        			}
        			else {
        				String fileId = StringUtil.getGuid32();
        				String suffix = faceImage.startsWith("data:image/png") ? "png" : "jpeg";
        				String fileName = fileId + "." + suffix;
        				String faceImageStr = faceImage.replaceFirst("^(?:data:image/\\w+;base64,)", "");
        				byte[] faceImageBytes = Base64.decode(faceImageStr);
        				
        				//上传人脸图片
        	        	FileInfo fileInfo = fileStorageService.of(faceImageBytes)
        	                    .setHashCalculatorMd5()
        	                    .setSaveFilename(fileName)
        	                    .upload();
        	        	BspDbUtil.updateUserFaceUrl(fileInfo.getUrl(), id);
        			}
        		}
        	}
        	
        	return R.ResponseOk();
    	}
    	catch(Exception e) {
    		return R.responseError("初始化Base64人脸照片发生异常！异常信息：" + e.getMessage());
    	}
    }

    @GetMapping(value = "/syncFace")
    @ApiOperation(value = "同步人脸照片至bsp人脸库")
    public R<?> syncFace(String id){
    	CnpFaceEntity cnpFace = cnpFaceDao.selectById(id);
    	if(cnpFace != null) {
    		Map<String, Object> resultMap = bspApi.registerFace(cnpFace.getPersonnelCode(), cnpFace.getPhoto(), cnpFace.getPersonnelType());
    		Boolean isSuccess = Boolean.valueOf((String)resultMap.get("success"));
    		if(isSuccess) {
    			return R.ResponseOk();
    		}
    		else {
    			if(resultMap.containsKey("detailedMsg")) {
    				return R.responseError((String)resultMap.get("detailedMsg"));
    			}
    			else if(resultMap.containsKey("msg")) {
    				return R.responseError((String)resultMap.get("msg"));
    			}
    			else {
    				return R.responseError("人脸注册失败");
    			}
    		}
    	}
    	else {
    		return R.responseError("找不到人脸数据");
    	}
    }

    @RequestMapping(value = "rerecordFace", method = RequestMethod.POST)
    @ApiOperation(value = "导入台账-重新下发-同步录入到1台设备", responseContainer = "List", notes = "{\"personnelId\":\"965fcf202a714274b432a24998152d23\",\"personnelType\":\"police_-1\",\"serialNumbers\":[\"O08A0B10004\"]}")
    public R<?> rerecordFace(@RequestBody CnpFaceForm form) {
        if (form.getSerialNumbers() == null || form.getSerialNumbers().isEmpty()) {
            return R.ResponseError(400, "参数错误 serialNumber");
        }
        String serialNumber = form.getSerialNumbers().get(0);
        List<String> clientIds = socketService.getSessionBySerialNumber(Lists.newArrayList(serialNumber));
        if (clientIds.isEmpty()) {
            return R.ResponseError("设备不在线");
        }
        Integer personnelType;
        String personnelId = form.getPersonnelId();
        if (PersonnelType_Prefix_Prisoner.equals(form.getPersonnelType())) {
            personnelType = 2;
        } else if (form.getPersonnelType() != null && form.getPersonnelType().startsWith(PersonnelType_Prefix_Police)) {
            personnelType = 1;
        } else {
            return R.ResponseError(400, "参数错误 personnelType");
        }
        String personnelPhoto = form.getPhoto();
        if (StringUtils.isBlank(form.getPhoto())) {
            personnelPhoto = cnpFaceService.getPersonnelPhoto(personnelType, personnelId);
            if (StringUtils.isBlank(personnelPhoto)) {
                return R.ResponseError("未找到该人员照片");
            }
        }
        PushMessageAckVO pushMessageAckVO = cnpFaceService.enrollFace(personnelType, personnelId, personnelPhoto, serialNumber);
        if (pushMessageAckVO.getOk()) {
            return R.ResponseOk();
        } else {
            return R.ResponseError(pushMessageAckVO.getResponse(255));
        }
    }

    @RequestMapping(value = "/sendFace", method = RequestMethod.POST)
    @ApiOperation(value = "下发照片-不等待结果 下发到多个设备", responseContainer = "List")
    public R<?> sendFace(@RequestBody CnpFaceForm form) {
        if (form.getSerialNumbers() == null || form.getSerialNumbers().isEmpty()) {
            return R.ResponseError(400, "参数错误 serialNumber");
        }
        Integer personnelType;
        String personnelId = form.getPersonnelId();
        if (PersonnelType_Prefix_Prisoner.equals(form.getPersonnelType())) {
            personnelType = 2;
        } else if (form.getPersonnelType() != null && form.getPersonnelType().startsWith(PersonnelType_Prefix_Police)) {
            personnelType = 1;
        } else {
            return R.ResponseError(400, "参数错误 personnelType");
        }
        String personnelPhoto = cnpFaceService.getPersonnelPhoto(personnelType, personnelId);
        if (StringUtils.isBlank(personnelPhoto)) {
            return R.ResponseError("未找到该人员照片");
        }
        // 异步下发到指定设备
        cnpFaceService.asyncEnrollFace(personnelType, personnelId, personnelPhoto, form.getSerialNumbers());
        return R.ResponseOk();
    }

    /**
     * 下发照片-可选择设备列表
     *
     * @return
     */
    @RequestMapping(value = "/deviceList", method = RequestMethod.GET)
    @ApiOperation(value = "下发照片-可选择设备列表", responseContainer = "List")
    public R<?> deviceList(CnpFaceForm form, @RequestParam(value = "deviceName", required = false) String deviceName, @RequestParam(value = "deviceType", required = false) Integer deviceType) {
        String personnelId = form.getPersonnelId();
        List<CnpFaceDeviceInscreenVO> records;
        if (PersonnelType_Prefix_Prisoner.equals(form.getPersonnelType())) {
            records = cnpFaceDao.getPrisonerAssociatedDevices(personnelId);
        } else if (form.getPersonnelType() != null && form.getPersonnelType().startsWith(PersonnelType_Prefix_Police)) {
            String tPersonnelId = personnelId;
            String prisonId = StringUtils.isBlank(personnelId) ? UserContext.getJwtInfo().getPrisonId() : null;
            records = cnpFaceDao.getPoliceAssociatedDevices(tPersonnelId, prisonId);
        } else {
            return R.ResponseError(400, "参数错误 personnelType");
        }
        if (StringUtils.isNotBlank(deviceName)) {
            records = records.stream().filter(f -> f.getDeviceName() != null && f.getDeviceName().contains(deviceName)).collect(Collectors.toList());
        }
        for (CnpFaceDeviceInscreenVO vo : records) {
            if (vo.getDeviceType() == null) {
                vo.setDeviceType(1); // 设备类型为空的设备默认展示位仓内屏
            }
        }
        // 去重，针对外屏  外屏的设备信息在储存到acp_pm_device_inscreen表时，如果关联了多个监室 会存在同一序列号的多条记录
        records = records.stream().filter(f -> StringUtils.isNotBlank(f.getSerialNumber())).collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getSerialNumber()))), ArrayList::new));
        if (deviceType != null) {
            records = records.stream().filter(f -> deviceType.toString().equals(f.getDeviceType() + "")).collect(Collectors.toList());
        }
        // 中文排序
        Comparator<Object> compare = Collator.getInstance(java.util.Locale.CHINA);
        records.sort(new Comparator<CnpFaceDeviceInscreenVO>() {
            @Override
            public int compare(CnpFaceDeviceInscreenVO o1, CnpFaceDeviceInscreenVO o2) {
                return compare.compare(o1.getDeviceName() + "", o2.getDeviceName() + "");
            }
        });
        return R.ResponseResult(records);
    }

    /**
     * 下发照片-可选择设备列表
     *
     * @return
     */
    @RequestMapping(value = "/count", method = RequestMethod.GET)
    @ApiOperation(value = "首页统计", responseContainer = "List")
    public R<?> count(@RequestParam(value = "prisonId", required = false) String prisonId) {
        if (StringUtils.isBlank(prisonId)) {
            prisonId = UserContext.getJwtInfo().getPrisonId();
        }
        List<CnpFaceCountVO> records = new ArrayList<>();
        List<CnpFaceCountVO> perTypeUserCountList = cnpFaceDao.getPerTypeUserCount(prisonId);
        // 民警用户数
        Integer policeCount = perTypeUserCountList.stream().filter(f -> "1".equals(f.getMark())).map(CnpFaceCountVO::getCount).findFirst().orElse(0);
        records.add(new CnpFaceCountVO("mj", policeCount));
        // 辅警用户数
        Integer assistantCount = perTypeUserCountList.stream().filter(f -> "0".equals(f.getMark())).map(CnpFaceCountVO::getCount).findFirst().orElse(0);
        records.add(new CnpFaceCountVO("fj", assistantCount));
        // 其他用户数
        Integer otherCount = perTypeUserCountList.stream().filter(f -> "-1".equals(f.getMark())).map(CnpFaceCountVO::getCount).findFirst().orElse(0);
        records.add(new CnpFaceCountVO("qt", otherCount));
        // null用户数
        Integer nullCount = perTypeUserCountList.stream().filter(f -> f.getMark() == null || "null".equals(f.getMark())).map(CnpFaceCountVO::getCount).findFirst().orElse(0);
        records.add(new CnpFaceCountVO("null", nullCount));
        // 被监管人员数
        Integer prisonerCounts = cnpFaceDao.getPrisonerCount(prisonId);
        records.add(new CnpFaceCountVO("zyry", prisonerCounts));
        // 本月新录入照片数
        Date monthSt = new Date();
        monthSt = new Date(monthSt.getYear(), monthSt.getMonth(), 1);
        Integer policeCnpFaceCount = cnpFaceDao.getPoliceCnpFaceCountByUpdateTime(prisonId, monthSt, null);
        Integer prisonerCnpFaceCount = cnpFaceDao.getPrisonerCnpFaceCountByUpdateTime(prisonId, monthSt, null);
        records.add(new CnpFaceCountVO("month_import", policeCnpFaceCount + prisonerCnpFaceCount));
        return R.ResponseResult(records);
    }

    /**
     * 测试用
     *
     * @return
     */
    @ApiOperation(value = "测试-读取亮度参数")
    @GetMapping("test/getEnrollFaceConfig")
    public R<?> getEnrollFaceConfig() {
        return R.ResponseResult("counter=" + CnpServiceImpl.enrollFaceAmendCounter + "    ,rate=" + CnpServiceImpl.enrollFaceAmendRate);
    }

    /**
     * 测试用
     *
     * @param enrollFaceAmendCounter
     * @param enrollFaceAmendRate
     * @return
     */
    @ApiOperation(value = "测试-设置亮度参数")
    @GetMapping("test/setEnrollFaceConfig")
    public R<?> setEnrollFaceConfig(int enrollFaceAmendCounter, int enrollFaceAmendRate) {
        CnpServiceImpl.enrollFaceAmendCounter = enrollFaceAmendCounter;
        CnpServiceImpl.enrollFaceAmendRate = enrollFaceAmendRate;
        return getEnrollFaceConfig();
    }

    @ApiOperation(value = "测试-调节图片亮度返回调整后图片地址")
    @PostMapping("/test/adjustImage")
    public R<?> enrollUpload(@RequestBody MultipartFile file, @RequestParam("rate") Float rate) throws Exception {
        BufferedImage bufferedImage = ImageIO.read(file.getInputStream());
        String photoPath = this.adjustImageAndUploadFtp(bufferedImage, rate);
        return R.ResponseResult(photoPath);
    }

    /**
     * 调整图片亮度 ，上传ftp
     *
     * @param image
     * @param lumRate 需要调节亮度到该比率
     * @return
     * @throws Exception
     */
    @Deprecated
    private String adjustImageAndUploadFtp(BufferedImage image, float lumRate) throws Exception {
        BufferedImage image1 = ImageUtils.lumAdjustment2(image, lumRate);
        File tempFile = File.createTempFile("zhjg-", ".jpg");
        try {
            ImageIO.write(image1, "JPG", tempFile);
            try (FileInputStream fileInput = new FileInputStream(tempFile)) {
                MultipartFile file = new MockMultipartFile("file", "file.jpg", "image/jpeg", fileInput);
                R<BaseFileVo> r = fileApi.uploadFile(file, ("terminal-face/test"), "tmp.jpg");
                BaseFileVo response = r.getData();
                log.debug("文件传响应：{}", JSON.toJSONString(r));
                if (response != null) {
                    if (StringUtils.isNotBlank(response.getAccessUrl())) {
                        return response.getAccessUrl();
                    }
                    if (StringUtils.isNotBlank(response.getFilePath())) {
                        // 兼容
                        return response.getFilePath();
                    }
                }
                throw new BaseException("文件上传异常");
            }
        } finally {
            if (tempFile != null) {
                try {
                    tempFile.delete();
                } catch (Exception e) {

                }
            }
        }
    }

    /**
     * 批量下发照片
     * 在押人员传递 serialNumbers 参数不为空，下发到指定serialNumbers设备中，否则下发到人员关联监室的设备中
     *
     * @param form 在押人员：{"personnelType":"prisoner","personnelIdList":["441500111072403","441500111202307140906"],"serialNumbers":null}
     * @param form 民警：{"personnelType":"police","personnelIdList":["6c076c04f486439ba9517df4f74d0cd0","c5a9397abb234cd8a0c2709c18a72ab2"],"serialNumbers":["H01GXX231110242345","VEQVWM6FRD"]}
     * @return
     */
    @RequestMapping(value = "/batchSendFace", method = RequestMethod.POST)
    //@formatter:off
    @ApiOperation(value = "下发照片-批量", notes = "在押人员：{\"personnelType\":\"prisoner\",\"personnelIdList\":[\"441500111072403\",\"441500111202307140906\"],\"serialNumbers\":null}"
        + "\r\n民警：{\"personnelType\":\"police\",\"personnelIdList\":[\"6c076c04f486439ba9517df4f74d0cd0\",\"c5a9397abb234cd8a0c2709c18a72ab2\"],\"serialNumbers\":[\"H01GXX231110242345\",\"VEQVWM6FRD\"]}"
    )
    //@formatter:on
    public R<?> batchSendFace(@RequestBody CnpFaceForm form) {
        if (form.getPersonnelIdList().isEmpty()) {
            return R.ResponseError(400, "参数错误 personnelIdList");
        }
        if (PersonnelType_Prefix_Prisoner.equals(form.getPersonnelType()) || (form.getPersonnelType() != null && form.getPersonnelType().startsWith(PersonnelType_Prefix_Police))) {
            cnpFaceService.batchSendFace(form);
            return R.ResponseOk();
        }else {
            return R.ResponseError(400, "参数错误 personnelType");
        }
    }

    /**
     * 测试-录入照片到设备
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/test/importFace", method = RequestMethod.POST)
    @ApiOperation(value = "测试-录入照片到设备")
    public Object testImportFace(@RequestBody CnpFaceForm form) {
        if (StringUtils.isBlank(form.getPhoto())) {
            return R.ResponseError(400, "照片不能为空");
        }
        List<String> clientIds = socketService.getSessionBySerialNumber(form.getSerialNumbers());
        if (clientIds.isEmpty()) {
            return R.ResponseError(500, "终端不存在或不在线");
        }
        String personnelType = PrisonRoomConstant.prisoner;
        if (form.getPersonnelType() != null && "police".equals(form.getPersonnelType())) {
            personnelType = PrisonRoomConstant.police;
        }
        PushMessageForm pushMessageForm = new PushMessageForm();
        pushMessageForm.setSessionIds(clientIds);
        pushMessageForm.setTerminal("CNP");
        pushMessageForm.setTarget(SocketActionConstants.PushMessageTargetEnum.android.name());
        pushMessageForm.setAction(SocketActionConstants.enrollFaceByImg);
        pushMessageForm.params(form.getPhoto(), (personnelType + "-" + form.getPersonnelId()));
        PushMessageAckVO ackMessage = socketService.pushMessageToClientWaitReply(pushMessageForm); // 推送给其中一个客户端
        return R.ResponseResult(ackMessage);
    }

}
