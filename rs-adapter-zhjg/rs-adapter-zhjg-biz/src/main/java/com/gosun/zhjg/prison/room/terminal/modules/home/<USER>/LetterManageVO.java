package com.gosun.zhjg.prison.room.terminal.modules.home.vo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gosun.zhjg.common.vo.BaseFileVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


/**
* LetterManageVO 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2024-09-27
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="LetterManageVO对象", description="收信管理")
public class LetterManageVO implements Serializable {


    @JsonProperty("id")
    @ApiModelProperty(value = "主键")
    private String id;

    @JsonProperty("prisonerId")
    @ApiModelProperty(value = "人员编号")
    private String prisonerId;

    @JsonProperty("prisonerName")
    @ApiModelProperty(value = "人员姓名")
    private String prisonerName;

    @JsonProperty("roomId")
    @ApiModelProperty(value = "监室号")
    private String roomId;

    @JsonProperty("roomName")
    @ApiModelProperty(value = "监室名字")
    private String roomName;

    @JsonProperty("sendMailUser")
    @ApiModelProperty(value = "送信人姓名")
    private String sendMailUser;

    @JsonProperty("relation")
    @ApiModelProperty(value = "关系")
    private String relation;

    @JsonProperty("relationDisplayName")
    @ApiModelProperty(value = "关系 中文")
    private String relationDisplayName;

    @JsonProperty("sendAddress")
    @ApiModelProperty(value = "来信地址")
    private String sendAddress;

    @JsonProperty("sendDate")
    @ApiModelProperty(value = "来信日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendDate;

    @JsonProperty("registerTime")
    @ApiModelProperty(value = "登记时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date registerTime;

    @JsonProperty("prisonId")
    @ApiModelProperty(value = "监所编号")
    private String prisonId;

    @JsonProperty("signUrl")
    @ApiModelProperty(value = "签名图片")
    private String signUrl;

    @JsonProperty("policeCheck")
    @ApiModelProperty(value = "管教-审核状态 1-通过 2-另行处理")
    private String policeCheck;

    @JsonProperty("policeCheckDisplayName")
    @ApiModelProperty(value = "管教-审核状态 1-通过 2-另行处理")
    private String policeCheckDisplayName;

    @JsonProperty("policeCheckUser")
    @ApiModelProperty(value = "管教-审核人")
    private String policeCheckUser;

    @JsonProperty("policeCheckTime")
    @ApiModelProperty(value = "管教-审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date policeCheckTime;

    @JsonProperty("policeCheckOpinion")
    @ApiModelProperty(value = "管教-管教意见")
    private String policeCheckOpinion;

    @JsonProperty("disposeSituation")
    @ApiModelProperty(value = "另行处理登记-处置情况")
    private String disposeSituation;

    @JsonProperty("disposeUser")
    @ApiModelProperty(value = "另行处理登记-处理人")
    private String disposeUser;

    @JsonProperty("disposeTime")
    @ApiModelProperty(value = "另行处理登记-处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date disposeTime;

    @JsonProperty("groupCheck")
    @ApiModelProperty(value = "科组长-审核状态 1-通过 2-不通过")
    private String groupCheck;

    @JsonProperty("groupCheckDisplayName")
    @ApiModelProperty(value = "科组长-审核状态 1-通过 2-不通过")
    private String groupCheckDisplayName;

    @JsonProperty("groupCheckUser")
    @ApiModelProperty(value = "科组长-审核人")
    private String groupCheckUser;

    @JsonProperty("groupCheckTime")
    @ApiModelProperty(value = "科组长-审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date groupCheckTime;

    @JsonProperty("groupCheckOpinion")
    @ApiModelProperty(value = "科组长意见")
    private String groupCheckOpinion;

    @JsonProperty("leaderCheck")
    @ApiModelProperty(value = "所领导-审核状态 1-通过 2-不通过")
    private String leaderCheck;

    @JsonProperty("leaderCheckDisplayName")
    @ApiModelProperty(value = "所领导-审核状态 1-通过 2-不通过")
    private String leaderCheckDisplayName;

    @JsonProperty("leaderCheckUser")
    @ApiModelProperty(value = "所领导-审核人")
    private String leaderCheckUser;

    @JsonProperty("leaderCheckTime")
    @ApiModelProperty(value = "所领导-审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date leaderCheckTime;

    @JsonProperty("leaderCheckOpinion")
    @ApiModelProperty(value = "所领导意见")
    private String leaderCheckOpinion;

    @JsonProperty("receiveTime")
    @ApiModelProperty(value = "签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receiveTime;

    @JsonProperty("status")
    @ApiModelProperty(value = "信件状态 1-另行处理 2-待转交 3-待确认 4-已确认")
    private Integer status;

    @JsonProperty("statusDisplayName")
    @ApiModelProperty(value = "信件状态 中文")
    private String statusDisplayName;

    @JsonProperty("passUser")
    @ApiModelProperty(value = "转交人")
    private String passUser;

    @JsonProperty("passTime")
    @ApiModelProperty(value = "转交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date passTime;

    @JsonProperty("passRemark")
    @ApiModelProperty(value = "转交备注")
    private String passRemark;

    @JsonProperty("delFlag")
    @ApiModelProperty(value = "删除标志 1-删除")
    private Integer delFlag;

    @JsonProperty("createUserId")
    @ApiModelProperty(value = "创建用户编号")
    private String createUserId;

    @JsonProperty("createUser")
    @ApiModelProperty(value = "创建用户")
    private String createUser;

    @JsonProperty("createTime")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonProperty("updateUserId")
    @ApiModelProperty(value = "更新用户编号")
    private String updateUserId;

    @JsonProperty("updateUser")
    @ApiModelProperty(value = "更新用户")
    private String updateUser;

    @JsonProperty("updateTime")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @JsonProperty("mailNo")
    @ApiModelProperty(value = "信件邮编")
    private String mailNo;

    @JsonProperty("registerUser")
    @ApiModelProperty(value = "登记人")
    private String registerUser;

    @JsonProperty("checkStatus")
    @ApiModelProperty(value = "审核流程状态 1-管教待审核 2-管教审核通过 3-科组长待审核 4-科组长审核通过 5-所领导待审核 6-所领导审核通过 7-所领导审核不通过")
    private Integer checkStatus;

    @JsonProperty("checkStatusDisplayName")
    @ApiModelProperty(value = "审核流程状态中文")
    private String checkStatusDisplayName;

    @JsonProperty("sendPrison")
    @ApiModelProperty(value = "来信单位")
    private String sendPrison;

    @ApiModelProperty(value = "附件-信件内容",required = true)
    List<BaseFileVo> messageFile = new ArrayList<>();

}
