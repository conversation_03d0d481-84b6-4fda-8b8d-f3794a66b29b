package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 监室值日-记录-关联人员表（某天某班次有某人）
 *
 * <AUTHOR>
 * @date 2024/11/1 8:59
 */
@Data
@TableName("terminal_room_day_duty_records_prisoner")
public class TerminalRoomDayDutyRecordsPrisonerEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@TableId(type = IdType.ASSIGN_UUID)

	private String id;
	/**
	 * 值班记录表 terminal_room_day_duty_records 表 id
	 */
	private String recordsId;
	/**
	 * 值班时间
	 */
	private Date dutyDate;
	/**
	 * 值班人员
	 */
	private String prisonerId;
	/**
	 * 值班人员
	 */
	private String prisonerName;
	/**
	 * 更新时间
	 */
	private Date updateTime;
}
