package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class TalkbackCallForm {

    /**
     * 来源： 平台：PLATFORM
     */
    @NotBlank(message = "src 不能为空")
    private String src;

    @NotBlank(message = "targetRoomId 不能为空")
    @ApiModelProperty("需要呼叫的监室")
    private String targetRoomId;
}
