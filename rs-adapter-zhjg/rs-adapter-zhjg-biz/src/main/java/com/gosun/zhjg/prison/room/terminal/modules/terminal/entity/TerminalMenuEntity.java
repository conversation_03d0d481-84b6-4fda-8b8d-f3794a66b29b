package com.gosun.zhjg.prison.room.terminal.modules.terminal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gosun.zhjg.common.constant.TerminalMenuCodeConstants;
import com.gosun.zhjg.common.enums.PlatformEnum;
import com.gosun.zhjg.common.enums.login.PersonnelTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 终端-仓内外屏菜单表
 *
 * <AUTHOR>
 * @date 2024/12/12 10:57
 */
@Data
@TableName("acp_pm_terminal_menu")
public class TerminalMenuEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@TableId(type = IdType.ASSIGN_UUID)

	private String id;
	/**
	 * 菜单名称
	 */
	private String menuName;
	/**
	 * 编码
	 * {@link TerminalMenuCodeConstants}
	 */
	private String code;
	/**
	 * 父级节点ID，-1表根节点
	 */
	private String parentId;
	/**
	 * 节点全路径，前后逗号隔开
	 */
	private String parentNodes;
	/**
	 * 终端类型 CNP、CWP
	 * {@link PlatformEnum}
	 */
	private String terminalType;
	/**
	 * 人员类型，区分民警还是被监管人员菜单 POLICE 、PRISONER
	 * {@link PersonnelTypeEnum}
	 */
	private String personnelType;
	/**
	 * 排序字段，从小到大
	 */
	private Integer sortOrder;
	/**
	 * $column.comments
	 */
	private Date createTime;
	/**
	 * $column.comments
	 */
	private String updateUserId;
	/**
	 * $column.comments
	 */
	private String updateUserName;
	/**
	 * $column.comments
	 */
	private Date updateTime;
	/**
	 * $column.comments
	 */
	@TableLogic
	private Integer delFlag;
}
