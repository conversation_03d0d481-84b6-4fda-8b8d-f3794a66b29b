package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
* rotating_staff_manage 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2024-10-28
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="RotatingStaffManageSearchDTO对象", description="轮值员安排管理")
public class RotatingStaffManageSearchDTO extends AbstractPageQueryForm implements Serializable {


   @JsonProperty("id")
   @ApiModelProperty(value = "主键")
   private String id;

   @JsonProperty("roomId")
   @ApiModelProperty(value = "监室号")
   private String roomId;

   @JsonProperty("roomName")
   @ApiModelProperty(value = "监室号 中文")
   private String roomName;

   @JsonProperty("operatePerson")
   @ApiModelProperty(value = "排班人")
   private String operatePerson;

   @ApiModelProperty("审核状态")
   private Integer status;

   @JsonProperty("staffStartTime")
   @ApiModelProperty(value = "轮值开始时间")
   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
   @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private Date staffStartTime;

   @JsonProperty("staffEndTime")
   @ApiModelProperty(value = "轮值结束时间")
   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
   @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private Date staffEndTime;

   @JsonProperty("operateStartTime")
   @ApiModelProperty(value = "设置开始时间")
   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
   @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private Date operateStartTime;

   @JsonProperty("operateEndTime")
   @ApiModelProperty(value = "设置结束时间")
   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
   @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private Date operateEndTime;

   @ApiModelProperty("1-安排")
   private Integer isManager;
}
