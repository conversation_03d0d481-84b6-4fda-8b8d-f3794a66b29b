package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
* rotating_staff_manage 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2024-10-28
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="RotatingStaffManageDTO对象", description="轮值员安排管理")
public class RotatingStaffManageDTO implements Serializable {

    @JsonProperty("roomId")
    @ApiModelProperty(value = "监室号",required = true)
    @NotBlank(message = "监室号 不能为空")
    private String roomId;

    @JsonProperty(value = "operatePerson")
    @ApiModelProperty(value = "操作人",required = true)
    @NotBlank(message = "操作人 不能为空")
    private String operatePerson;

    @JsonProperty(value = "operateTime")
    @ApiModelProperty(value = "操作时间",required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "操作时间 不能为空")
    private Date operateTime;

    @JsonProperty(value = "constructionReason")
    @ApiModelProperty(value = "布建理由",required = true)
    @NotBlank(message = "布建理由 不能为空")
    private String constructionReason;

    @JsonProperty(value = "taskDesc")
    @ApiModelProperty(value = "任务描述",required = true)
    @NotBlank(message = "任务描述 不能为空")
    private String taskDesc;

    @JsonProperty("dateDTOS")
    @NotEmpty(message = "轮值员安排集合 不能为空")
    @ApiModelProperty(value = "轮值员安排集合",required = true)
    private List<RotatingStaffManageDateDTO> dateDTOS;


}

