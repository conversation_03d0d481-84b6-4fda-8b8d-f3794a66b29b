package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import java.util.Date;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 安全检查管理-组设置(SafetyCheckManageGroup)实体类
 *
 * <AUTHOR>
 * @since 2024-10-28 19:21:25
 */
@Data
@TableName("safety_check_manage_group")
public class SafetyCheckManageGroupEntity implements Serializable {
    private static final long serialVersionUID = 445094235428247560L;
    /**
     * id主键
     */
    @TableId(type = IdType.ASSIGN_UUID)

    private String id;
    /**
     * 组名
     */
    private String groupName;
    /**
     * 组长id
     */
    private String leaderId;
    /**
     * 组长姓名
     */
    private String leaderName;
    /**
     * 组员id列表
     */
    private String memberIds;
    /**
     * 组员姓名列表
     */
    private String memberNames;
    /**
     * 监所编号
     */
    private String prisonId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人id
     */
    private String createUserId;
    /**
     * 创建人
     */
    private String createUserName;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 更新人id
     */
    private String updateUserId;
    /**
     * 更新人姓名
     */
    private String updateUserName;


}

