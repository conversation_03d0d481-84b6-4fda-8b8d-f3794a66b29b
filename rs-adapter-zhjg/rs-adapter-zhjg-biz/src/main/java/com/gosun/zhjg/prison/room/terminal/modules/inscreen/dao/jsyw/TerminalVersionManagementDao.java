package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.TerminalVersionInfoPageDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.TerminalVersionManagementPageDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.TerminalVersionManagementsDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.TerminalVersionUpgradeLogPageDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.TerminalVersionUpgradeLogSaveDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.TerminalVersionManagementEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.TerminalVersionInfoVO;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.TerminalVersionUpgradeLogVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 终端版本管理
 *
 * <AUTHOR>
 * @date 2023/8/18 15:26
 */
@Mapper
public interface TerminalVersionManagementDao extends BaseMapper<TerminalVersionManagementEntity> {

    List<TerminalVersionManagementsDto> findByPage(@Param("form") TerminalVersionManagementPageDto form, Page page);

    TerminalVersionManagementsDto findOneById(@Param("id") String id);

    /**
     * 查询 设备版本信息
     *
     * @param form
     * @param page
     * @return
     */
    List<TerminalVersionInfoVO> terminalVersionPage(@Param("form") TerminalVersionInfoPageDto form, Page page);

    /**
     * 查询升级日志
     *
     * @param form
     * @param page
     * @return
     */
    List<TerminalVersionUpgradeLogVO> terminalUpgradeLogPage(@Param("form") TerminalVersionUpgradeLogPageDto form, Page page);

}
