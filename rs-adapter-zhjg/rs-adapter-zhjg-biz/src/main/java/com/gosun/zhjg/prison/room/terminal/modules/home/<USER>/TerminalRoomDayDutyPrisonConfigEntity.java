package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 监室值日-监所配置
 *
 * <AUTHOR>
 * @date 2024/11/1 8:59
 */
@Data
@TableName("terminal_room_day_duty_prison_config")
public class TerminalRoomDayDutyPrisonConfigEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@TableId(type = IdType.ASSIGN_UUID)

	private String id;
	/**
	 * 单位代码
	 */
	private String prisonId;
	/**
	 * 监室值日语音提醒开启 1开启、0关闭
	 */
	private Integer dutyRemindEnable;
	/**
	 * 语音播报静音时段，支持多个，格式：00:00:00-10:00:00,
	 */
	private String silentTime;
	/**
	 * 默认播报时间
	 */
	private String defaultBroadcastTime;
	/**
	 * $column.comments
	 */
	private String updateUserId;
	/**
	 * $column.comments
	 */
	private String updateUserName;
	/**
	 * $column.comments
	 */
	private Date updateTime;
	/**
	 * $column.comments
	 */
	@TableLogic
	private Integer delFlag;
}
