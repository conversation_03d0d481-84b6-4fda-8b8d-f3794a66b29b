package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * rights_obligations 实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date: 2024-06-18
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value = "RightsObligationsSearchDTO对象", description = "监室智能终端-权利义务")
public class RightsObligationsSearchDTO extends AbstractPageQueryForm implements Serializable {


    @JsonProperty("id")
    @ApiModelProperty(value = "主键")
    private String id;

    @JsonProperty("prisonerId")
    @ApiModelProperty(value = "人员编号")
    private String prisonerId;

    @JsonProperty("prisonerName")
    @ApiModelProperty(value = "人员姓名")
    private String prisonerName;

    @JsonProperty("roomId")
    @ApiModelProperty(value = "监室号 多个逗号隔开")
    private String roomId;

    @JsonProperty("roomName")
    @ApiModelProperty(value = "监室名字")
    private String roomName;

    @JsonProperty("signStatus")
    @ApiModelProperty(value = "签名状态 1-已完成 2-未完成")
    private Integer signStatus;

    @JsonProperty("fingerprintStatus")
    @ApiModelProperty(value = "指纹状态 1-已完成 2-未完成")
    private Integer fingerprintStatus;

    @JsonProperty("createTimeStart")
    @ApiModelProperty(value = "上次操作时间-开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeStart;

    @JsonProperty("createTimeEnd")
    @ApiModelProperty(value = "上次操作时间-结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;

    @JsonProperty("entryTimeStart")
    @ApiModelProperty(value = "入所时间-开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date entryTimeStart;

    @JsonProperty("entryTimeEnd")
    @ApiModelProperty(value = "入所时间-结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date entryTimeEnd;


}