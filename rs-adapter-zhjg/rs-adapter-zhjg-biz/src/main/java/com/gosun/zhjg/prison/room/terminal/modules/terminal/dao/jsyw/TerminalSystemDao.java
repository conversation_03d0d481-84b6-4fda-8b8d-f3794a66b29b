package com.gosun.zhjg.prison.room.terminal.modules.terminal.dao.jsyw;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dto.TerminalSystemDeviceQueryDto;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dto.TerminalSystemUpgradeLogQueryDto;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalSystemDeviceVO;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalSystemUpgradeLogBatchVO;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalSystemUpgradeLogVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TerminalSystemDao {

	/**
	 * 仓内外屏列表
	 *
	 * @param form
	 * @param page
	 * @return
	 */
	List<TerminalSystemDeviceVO> findDeviceCnpCwpList(@Param("form") TerminalSystemDeviceQueryDto form, Page page);

	/**
	 * 提人交接终端列表
	 *
	 * @param form
	 * @param page
	 * @return
	 */
	List<TerminalSystemDeviceVO> findDeviceTrjjzdList(@Param("form") TerminalSystemDeviceQueryDto form, Page page);

	/**
	 * 未绑定设备列表
	 *
	 * @param form
	 * @param page
	 * @return
	 */
	@Deprecated
	List<TerminalSystemDeviceVO> findDeviceUnboundList(@Param("form") TerminalSystemDeviceQueryDto form, Page page);

	/**
	 * 更新台账-列表
	 *
	 * @param form
	 * @param page
	 * @return
	 */
	List<TerminalSystemUpgradeLogBatchVO> upgradelogBatchList(@Param("form") TerminalSystemUpgradeLogQueryDto form, Page page);

	TerminalSystemUpgradeLogBatchVO upgradelogBatchInfo(@Param("id") String id);

	/**
	 * 更新台账-批次-详细列表
	 *
	 * @param form
	 * @param page
	 * @return
	 */
	List<TerminalSystemUpgradeLogVO> upgradelogList(@Param("form") TerminalSystemUpgradeLogQueryDto form, Page page);

	/**
	 * 查询最新一次 升级日志 批次号
	 *
	 * @param form
	 * @return
	 */
	String selectUpgradeLogBatchId(@Param("form") TerminalSystemUpgradeLogQueryDto form);
}
