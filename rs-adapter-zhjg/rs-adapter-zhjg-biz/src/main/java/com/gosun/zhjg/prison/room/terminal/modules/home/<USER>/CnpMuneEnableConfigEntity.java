package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 *
 * <AUTHOR>
 *
 */
@Data
@TableName("cnp_mune_enable_config")
public class CnpMuneEnableConfigEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@TableId(type = IdType.ASSIGN_UUID)

	private String id;
	/**
	 * 菜单编码
	 */
	private String menuCode;
	/**
	 * $column.comments
	 */
	private String roomId;
	/**
	 * 是否启用
	 */
	private Integer enable;
	/**
	 * $column.comments
	 */
	@TableLogic
	private Integer delFlag;
	/**
	 * $column.comments
	 */
	private String updateUserid;
	/**
	 * $column.comments
	 */
	private String updateUsername;
	/**
	 * $column.comments
	 */
	private Date updateTime;
}
