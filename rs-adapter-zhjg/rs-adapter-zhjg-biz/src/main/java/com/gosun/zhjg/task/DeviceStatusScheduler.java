package com.gosun.zhjg.task;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gosun.zhjg.common.enums.DeviceStatusEnum;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.BaseDeviceInscreenDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.ConnectHandlerDTO;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.BaseDeviceInscreenEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.handler.SendFaceHandler;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.NetworkCheckService;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.TerminalService;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.TerminalVersionManagementService;
import com.rs.module.base.dao.pm.device.BaseDeviceDao;
import com.rs.module.base.entity.pm.device.BaseDeviceDO;
import com.rs.module.base.enums.DeviceTypeEnum;
import com.rs.util.DicUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.locks.ReentrantLock;

/**
 * @ClassName DeviceStatusScheduler
 * @Description 内网屏设备状态定时任务
 * <AUTHOR>
 * @Date 2025/7/14 20:04
 * @Version 1.0
 */
@Component
@Log4j2
public class DeviceStatusScheduler {

    /**
     * 设备状态更新锁，防止并发执行
     */
    private final ReentrantLock updateDeviceStatusLock = new ReentrantLock();

    @Autowired
    private BaseDeviceDao baseDeviceDao;
    @Autowired
    private BaseDeviceInscreenDao baseDeviceInscreenDao;
    @Autowired
    NetworkCheckService networkCheckService;
    @Autowired
    SendFaceHandler sendFaceHandler;
    @Autowired
    TerminalService terminalService;
    @Autowired
    TerminalVersionManagementService terminalVersionManagementService;


    @Scheduled(fixedDelay = 1000 * 60)
    public void updateDeviceStatus() {
        // 尝试获取锁，如果获取不到则快速失败
        if (!updateDeviceStatusLock.tryLock()) {
            log.warn("设备状态更新任务正在执行中，跳过本次执行");
            return;
        }

        try {
            log.info("开始执行设备状态更新任务");
            List<BaseDeviceDO> baseDeviceDOList = baseDeviceDao.selectList(
                    new LambdaQueryWrapper<BaseDeviceDO>()
                            .in(BaseDeviceDO::getDeviceTypeId, DeviceTypeEnum.INDOOR_TERMINAL.getCode(), DeviceTypeEnum.SCREEN_OUTSIDE_THE_WAREHOUSE.getCode())
            );
            log.info("查询到设备数量: {}", baseDeviceDOList.size());

            int processedCount = 0;
            int onlineCount = 0;
            int offlineCount = 0;
            int damagedCount = 0;

            for (BaseDeviceDO baseDeviceDO : baseDeviceDOList) {
                processedCount++;
                log.debug("正在处理设备 [{}/{}]: ID={}, IP={}", processedCount, baseDeviceDOList.size(),
                         baseDeviceDO.getId(), baseDeviceDO.getIpAddress());
                if (StringUtils.isNotEmpty(baseDeviceDO.getIpAddress())) {
                    log.debug("检测设备连通性: IP={}, 端口=6897, 超时=300ms", baseDeviceDO.getIpAddress());
                    boolean ipReachable = networkCheckService.isHostConnectable(baseDeviceDO.getIpAddress(), 6897, 300);
                    if (ipReachable) {
                        log.debug("设备在线: IP={}", baseDeviceDO.getIpAddress());
                        baseDeviceDO.setDeviceStatus(DeviceStatusEnum.ONLINE.getValue());
                        onlineCount++;
                        //设备在线，批量下发人脸
                        ConnectHandlerDTO connectHandlerDTO = new ConnectHandlerDTO();
                        connectHandlerDTO.setBaseDeviceDO(baseDeviceDO);
                        connectHandlerDTO.setIp(baseDeviceDO.getIpAddress());
                        BaseDeviceInscreenEntity baseDeviceInscreenEntity = null;
                        List<BaseDeviceInscreenEntity> baseDeviceInscreenEntityList = baseDeviceInscreenDao.selectList(new LambdaQueryWrapper<BaseDeviceInscreenEntity>()
                                .eq(BaseDeviceInscreenEntity::getDeviceIp, connectHandlerDTO.getIp())
                        );
                        if (!baseDeviceInscreenEntityList.isEmpty()) {
                            baseDeviceInscreenEntity = baseDeviceInscreenEntityList.get(0);
                        }
                        try {
                            if (baseDeviceInscreenEntity != null && StringUtils.isNotEmpty(baseDeviceInscreenEntity.getSerialNumber())) {
                                log.debug("开始下发人脸数据: IP={}, 序列号={}", baseDeviceDO.getIpAddress(), baseDeviceInscreenEntity.getSerialNumber());
                                connectHandlerDTO.setSerialNum(baseDeviceInscreenEntity.getSerialNumber());
                                connectHandlerDTO.setBaseDeviceInscreenEntity(baseDeviceInscreenEntity);
                                sendFaceHandler.handle(connectHandlerDTO);
                                log.debug("人脸数据下发完成: IP={}", baseDeviceDO.getIpAddress());
                            } else {
                                log.debug("跳过人脸下发，设备信息不完整: IP={}", baseDeviceDO.getIpAddress());
                            }
                        } catch (Exception e) {
                            log.error("人脸数据下发失败: IP={}, 错误信息: {}", baseDeviceDO.getIpAddress(), e.getMessage(), e);
                        }
                        try {
                            log.debug("开始同步设备配置信息: IP={}", baseDeviceDO.getIpAddress());
                            JSONObject configInfo = terminalService.getConfigInfo(baseDeviceDO.getIpAddress());
                            String translate = DicUtils.translate("ZD_ZDIP", baseDeviceDO.getOrgCode());
                            if (StringUtils.isNotEmpty(translate) && !"null".equals(translate)) {
                                log.debug("更新设备配置: IP={}, 机构代码={}, 服务器IP={}", baseDeviceDO.getIpAddress(), baseDeviceDO.getOrgCode(), translate);
                                configInfo.put("serverIp", translate);
                                configInfo.put("ip", baseDeviceDO.getIpAddress());
                                if (DeviceTypeEnum.INDOOR_TERMINAL.getCode().equals(baseDeviceDO.getDeviceTypeId())) {
                                    configInfo.put("serverPort", "9503");
                                    log.debug("设置室内终端端口: 9503");
                                }
                                if (DeviceTypeEnum.SCREEN_OUTSIDE_THE_WAREHOUSE.getCode().equals(baseDeviceDO.getDeviceTypeId())) {
                                    configInfo.put("serverPort", "9502");
                                    log.debug("设置仓外屏端口: 9502");
                                }
                                terminalService.saveConfigInfo(configInfo);
                                log.debug("设备配置保存完成: IP={}", baseDeviceDO.getIpAddress());
                            } else {
                                log.debug("跳过配置更新，未找到机构IP配置: 机构代码={}", baseDeviceDO.getOrgCode());
                            }
                        } catch (Exception e) {
                            log.error("设备配置同步失败: IP={}, 错误信息: {}", baseDeviceDO.getIpAddress(), e.getMessage(), e);
                        }

                    } else {
                        log.debug("设备离线: IP={}", baseDeviceDO.getIpAddress());
                        baseDeviceDO.setDeviceStatus(DeviceStatusEnum.OFF_LINE.getValue());
                        offlineCount++;
                    }
                } else {
                    log.debug("设备IP地址为空，标记为损坏: 设备ID={}", baseDeviceDO.getId());
                    baseDeviceDO.setDeviceStatus(DeviceStatusEnum.DAMAGED.getValue());
                    damagedCount++;
                }

                try {
                    baseDeviceDao.updateById(baseDeviceDO);
                    log.debug("设备状态更新成功: ID={}, IP={}, 状态={}",
                             baseDeviceDO.getId(), baseDeviceDO.getIpAddress(), baseDeviceDO.getDeviceStatus());
                } catch (Exception e) {
                    log.error("设备状态更新失败: ID={}, IP={}, 错误信息: {}",
                             baseDeviceDO.getId(), baseDeviceDO.getIpAddress(), e.getMessage(), e);
                }
            }

            log.info("设备状态更新任务执行完成 - 总计: {}, 在线: {}, 离线: {}, 损坏: {}",
                    processedCount, onlineCount, offlineCount, damagedCount);
        } catch (Exception e) {
            log.error("设备状态更新任务执行异常: {}", e.getMessage(), e);
        } finally {
            // 确保在任何情况下都释放锁
            updateDeviceStatusLock.unlock();
        }
    }

}
