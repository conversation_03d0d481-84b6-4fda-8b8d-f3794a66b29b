package com.gosun.zhjg.prison.room.terminal.modules.terminal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 终端-仓内外屏菜单关联监所表
 *
 * <AUTHOR>
 * @date 2024/12/12 10:57
 */
@Data
@TableName("acp_pm_terminal_menu_org")
public class TerminalMenuPrisonLinkEntity extends BaseDO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@TableId(type = IdType.ASSIGN_UUID)

	private String id;

	/**
	 * 菜单id，acp_pm_terminal_menu表
	 */
	private String menuId;


}
