package com.gosun.zhjg.prison.room.terminal.modules.socket.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.gosun.zhjg.basic.business.modules.file.feign.FileApi;
import com.gosun.zhjg.basic.business.modules.file.vo.BaseFileVo;
import com.gosun.zhjg.common.constant.DictionaryDataCodeConstants;
import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.common.constant.SocketActionConstants.PushMessageTargetEnum;
import com.gosun.zhjg.common.exception.BaseException;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.common.util.FileUrlUtils;
import com.gosun.zhjg.common.util.SkipSSLSimpleClientHttpRequestFactory;
import com.gosun.zhjg.prison.room.terminal.common.constant.PrisonRoomConstant;
import com.gosun.zhjg.prison.room.terminal.common.utils.ImageUtils;
import com.gosun.zhjg.prison.room.terminal.modules.app.entity.CnpLogEntity;
import com.gosun.zhjg.prison.room.terminal.modules.app.service.CnpLogService;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.BaseDeviceInscreenDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.CnpFaceDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.CnpFaceImportStatusDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.CnpFaceEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.CnpFaceImportStatusEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.CnpFaceService;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.TerminalVersionManagementService;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dao.CnpSocketDao;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.DeviceInitializeDto;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushVoiceBroadcastForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.CnpService;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.SocketService;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.PersonalInfoVO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.PushMessageAckVO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.SocketRelationMapVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.client.ClientHttpRequest;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URI;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CnpServiceImpl implements CnpService {

    @Autowired
    private SocketService socketService;

    @Value("${file-url-prefix}")
    private String pathPrefix;

    @Autowired
    private CnpSocketDao cnpSocketDao;

    @Autowired
    private CnpLogService cnpLogService;

    /**
     * 人脸照片录入失败，修正次数
     */

    public static int enrollFaceAmendCounter;

    @Value("${ENROLL-FACE-AMEND-COUNTER:12}")
    public void setEnrollFaceAmendCounter(int t_enrollFaceAmendCounter) {
        enrollFaceAmendCounter = t_enrollFaceAmendCounter;
    }

    /**
     * 人脸照片录入失败，修正图片质量增长率
     */
    public static int enrollFaceAmendRate;

    @Value("${ENROLL-FACE-AMEND-RATE:10}")
    public void setEnrollFaceAmendRate(int t_enrollFaceAmendRate) {
        enrollFaceAmendRate = t_enrollFaceAmendRate;
    }

    @Autowired(required = false)
    private FileApi fileApi;

    @Autowired
    private CnpFaceDao cnpFaceDao;

    /**
     * 仓内屏内警员编号的前缀
     */
    private final String POLICE_CODE_PREFIX = PrisonRoomConstant.police + "-";
    public static final String PRISONER_CODE_PREFIX = PrisonRoomConstant.prisoner + "-";

    final static Object FACE_LOCK = new Object();

    /**
     * 当前系统内终端。有些地市只有内屏， 只在这一个地方改一下，不需要再下面改很多地方
     */
    private static final List<String> terminalList = Lists.newArrayList(SocketActionConstants.PushMessageTerminalEnum.CNP.name(), SocketActionConstants.PushMessageTerminalEnum.CWP.name());

    @Autowired
    private CnpFaceService cnpFaceService;

    /**
     * 测试 是否跳过民警照片录入
     */
    public static boolean TEST_SKIP_POLICE_ENROLL_FACE = false;

    @Autowired
    private CnpFaceImportStatusDao cnpFaceImportStatusDao;
    @Autowired
    private BaseDeviceInscreenDao baseDeviceInscreenDao;
    @Autowired
    private TerminalVersionManagementService terminalVersionManagementService;

    /**
     * 仓内屏重启初始化操作
     */
    @Override
    public void rebootInitialize(DeviceInitializeDto dto) {
        String terminalName = SocketActionConstants.PushMessageTerminalEnum.CNP.name().equals(dto.getTerminal()) ? "仓内屏" : (SocketActionConstants.PushMessageTerminalEnum.CWP.name().equals(dto.getTerminal()) ? "仓外屏" : dto.getTerminal());
        String logMsg = String.format("%s初始化[%s]", terminalName, dto.getSerialNumber());
        log.info(logMsg);
        CnpLogEntity entity = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.INFO, "RebootInitialize", logMsg, dto.getTerminal());
        entity.setSerialNum(dto.getSerialNumber());
        cnpLogService.log(entity);

        // 同步设备apk、web版本
        this.syncVersionInfo(dto.getSerialNumber());

        /*
        if (goRtcConfig.getEnable()) {
            try {
                //设备视频会议登录
                this.rtcLogin(dto);
            } catch (Exception ex) {
                log.error("", ex);
            }
        }
        */

        // 人脸下发
        this.enrollFaceSync(dto);
    }

    @Override
    public void roomChange(List<String> roomList) {
        log.info("监室调整：{}", JSON.toJSONString(roomList));
        for (String terminal : terminalList) {
            DeviceInitializeDto dto = new DeviceInitializeDto();
            dto.setTerminal(terminal);
            for (String roomId : roomList) {
                List<String> serialNumbers = socketService.getSerialNumberByRoomIds(Lists.newArrayList(roomId), terminal);
                if (serialNumbers.isEmpty()) {
                    // 记录日志
                    CnpLogEntity entity = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.WARN, "RoomChange2", String.format("未找到监室%s注册%s设备", roomId, terminal));
                    entity.setSerialNum(roomId);
                    cnpLogService.log(entity);
                } else {
                    for (String serialNumber : serialNumbers) {
                        dto.setSerialNumber(serialNumber);
                        this.enrollFaceSync(dto);
                    }
                }
            }
        }
    }

    /**
     * 人员信息变动通知
     *
     * @param msg {"rybh":"4401001112205315491","oldRoomId":"","updateType":"INSERT","sendTime":1669085842230,"oldRyzt":"10"}
     */
    @Deprecated
    @Override
    public void prisonerChange(String msg) {
        JSONObject data = JSON.parseObject(msg);
        String rybh = data.getString("rybh");
        String rybhAppCode = PRISONER_CODE_PREFIX + rybh;
        String oldRoomId = data.getString("oldRoomId");
        PersonalInfoVO prisonerInfo = cnpSocketDao.getPrisonerInfo(rybh);
        boolean deleteOpt = ("DELETE".equals(data.getString("updateType")));
        boolean roomChange = (oldRoomId != null && (prisonerInfo == null || !oldRoomId.equals(prisonerInfo.getRoomId())));
        log.debug("{}人员入所deleteOpt:{}, oldRoomId:{}, roomChange:{}, prisonerInfo:{}！", rybh, deleteOpt, oldRoomId, roomChange, JSON.toJSONString(prisonerInfo));
        // 如果监室做了调整或者人员被删除了。清理该人员在旧监室照片
        if ((deleteOpt || roomChange) && StringUtils.isNotBlank(oldRoomId)) {
            this.cleanStoreFaceByRoomId(rybhAppCode, oldRoomId);
        }
        if (!deleteOpt && prisonerInfo != null && StringUtils.isNotBlank(prisonerInfo.getRoomId())) {
            if ("11".equals(prisonerInfo.getRyzt())) {
                this.cleanStoreFaceByRoomId(rybhAppCode, prisonerInfo.getRoomId());
            } else if ("10".equals(prisonerInfo.getRyzt())) {
                if (StringUtils.isNotBlank(prisonerInfo.getRoomId()) && StringUtils.isNotBlank(prisonerInfo.getPhoto())) {
                    List<String> photoUrlReference = Lists.newArrayList(prisonerInfo.getPhoto());
                    for (String terminal : terminalList) {
                        try {
                            this.chackAndEnrollFaceByImgByRoomId(rybhAppCode, photoUrlReference, prisonerInfo.getRoomId(), terminal);
                        } catch (Exception e) {
                            log.error("", e);
                            CnpLogEntity entity = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.ERROR, "face", "人员入所录入照片异常," + e.getMessage(), rybhAppCode, photoUrlReference.get(0));
                            entity.setSerialNum(prisonerInfo.getRoomId());
                            cnpLogService.log(entity);
                        }
                    }
                }
            }
        }
    }

    /**
     * 照片变动
     *
     * @param msg {"updateType":"INSERT","rybh":"440400011111545654","zpxh":"0","sendTime":1673512221688}
     */
    @Deprecated
    @Override
    public void zpChange(String msg) {
        JSONObject data = JSON.parseObject(msg);
        String zpxh = data.getString("zpxh");
        String updateType = (data.getString("updateType") + "").toUpperCase();
        if (!"0".equals(zpxh)) {
            // 不是正面照
            return;
        }
        if (!"INSERT".equals(updateType) && !"UPDATE".equals(updateType)) {
            return;
        }
        String rybh = data.getString("rybh");
        String rybhAppCode = PRISONER_CODE_PREFIX + rybh;
        PersonalInfoVO prisonerInfo = cnpSocketDao.getPrisonerInfoIn(rybh);
        if (prisonerInfo == null || StringUtils.isAnyBlank(prisonerInfo.getRoomId(), prisonerInfo.getPhoto())) {
            log.info("人脸照片变动未找到照片或监室信息 {} {}", updateType, rybh);
            return;
        }
        log.info("人脸照片变动 {} {}, roomId={}", updateType, rybh, prisonerInfo.getRoomId());
        CnpLogEntity entity = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.INFO, "face", ("人脸照片变动-" + updateType), rybh);
        entity.setSerialNum(prisonerInfo.getRoomId());
        cnpLogService.log(entity);
        for (String terminal : terminalList) {
            List<String> photoUrlReference = Lists.newArrayList(prisonerInfo.getPhoto());
            try {
//                this.enrollFaceByImgByRoomId(rybhAppCode, photoUrlReference, prisonerInfo.getRoomId(), terminal, true);
                this.chackAndEnrollFaceByImgByRoomId(rybhAppCode, photoUrlReference, prisonerInfo.getRoomId(), terminal);
            } catch (Exception e) {
                log.error("", e);
                entity = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.ERROR, "face", ("(" + terminal + ")人脸照片变动-" + updateType + " 添加人脸异常, ") + e.getMessage(), rybhAppCode, photoUrlReference.get(0), prisonerInfo.getRoomId());
                entity.setSerialNum(prisonerInfo.getRoomId());
                cnpLogService.log(entity);
            }
        }
    }

    /**
     * 下发人员照片。会检查在所状态
     * 人员入所照片下发、监室调整
     *
     * @param prisonerId 人员编号
     */
    @Override
    public void sendPersonnelPhoto(String prisonerId) {
        if (StringUtils.isBlank(prisonerId)) {
            return;
        }
        PersonalInfoVO prisonerInfo = cnpSocketDao.getPrisonerInfo(prisonerId);
        String roomId = null;
        String photo = null;
        if (prisonerInfo != null) {
            if (DictionaryDataCodeConstants.RYZT_OUT.equals(prisonerInfo.getRyzt())) {
                log.debug("人员出所，忽略照片下发。{}", prisonerId);
                return;
            }
            roomId = prisonerInfo.getRoomId();
            photo = prisonerInfo.getPhoto();
            if (StringUtils.isBlank(photo) || "null".equals(photo)) {
                // 人员表照片空，再次到 照片表查询
                photo = cnpSocketDao.getPrisonerPhotoInPhotoTable(prisonerId);
            }
        }
        if (StringUtils.isBlank(roomId) || StringUtils.isBlank(photo) || "null".equals(photo)) {
            log.debug("监室号、或照片空，忽略照片下发。{}", prisonerId);
            return;
        }
        String rybhAppCode = PRISONER_CODE_PREFIX + prisonerId;
        String photoUrl = FileUrlUtils.concatUrl(photo, pathPrefix);
        List<String> photoUrlReference = Lists.newArrayList(photoUrl);
        for (String terminal : terminalList) {
            try {
                log.info("人员入所-下发照片 {}-{}, {}, {}", terminal, roomId, prisonerId, photoUrl);
                this.enrollFaceByImgByRoomId(rybhAppCode, photoUrlReference, roomId, terminal, true);
            } catch (Exception e) {
                String message = e.getMessage();
                if (message != null && message.contains("无在线客户端")) {
                    log.warn("{}-{}：无在线客户端", terminal, roomId);
                } else {
                    log.error("", e);
                    message = e.getMessage();
                }
                CnpLogEntity entity = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.ERROR, "face", "人员入所录入照片异常," + message, rybhAppCode, photoUrlReference.get(0));
                entity.setSerialNum(prisonerInfo.getRoomId());
                cnpLogService.log(entity);
            }
        }
    }

    /**
     * 照片删除。会检查在所状态
     * 人员出所照片删除，监室调整
     *
     * @param prisonerId 人员编号
     * @param oldRoomId  旧监室号。没有的话从出所表中获取
     */
    @Override
    public void deletePersonnelPhoto(String prisonerId, String oldRoomId) {
        if (StringUtils.isBlank(prisonerId)) {
            return;
        }
        PersonalInfoVO prisonerInfo = cnpSocketDao.getPrisonerInfo(prisonerId);
        if (prisonerInfo != null) {
            if (DictionaryDataCodeConstants.RYZT_IN.equals(prisonerInfo.getRyzt())) {
                log.debug("人员在所，忽略照片清理。{}", prisonerId);
                return;
            }
            if (StringUtils.isBlank(oldRoomId)) {
                oldRoomId = prisonerInfo.getRoomId();
            }
        }
        if (StringUtils.isBlank(oldRoomId)) {
            // 监室号空，结束
            log.debug("监室号空，忽略照片清理。{}", prisonerId);
            return;
        }
        // 推送删除照片
        log.info("人员出所-清理照片 {}, {}", prisonerId, oldRoomId);
        String rybhAppCode = PRISONER_CODE_PREFIX + prisonerId;
        this.cleanStoreFaceByRoomId(oldRoomId, rybhAppCode);
    }

    @Override
    public void enrollFaceSync(DeviceInitializeDto dto) {
        try {
            synchronized (FACE_LOCK) {
                this.enrollFace(dto);
            }
        } catch (Exception e) {
            log.error("", e);
            CnpLogEntity entity = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.ERROR, "face", "人脸照片下发未捕获异常", e.getMessage());
            entity.setSerialNum(dto.getSerialNumber());
            cnpLogService.log(entity);
        }
    }

    @Deprecated
    private void enrollFace(DeviceInitializeDto dto) {
        List<String> roomIds = cnpSocketDao.getRoomIdBySerialNumber(dto.getSerialNumber());
        if (roomIds == null || roomIds.isEmpty()) {
            String msg = String.format("未找到序列号%s关联监室信息", dto.getSerialNumber());
            log.warn(msg);
            CnpLogEntity entity = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.ERROR, "face", msg);
            entity.setSerialNum(dto.getSerialNumber());
            cnpLogService.log(entity);
            return;
        }
        String roomId = roomIds.get(0);
        List<PushMessageAckVO> deviceFaceCodeList = null;
        Exception exception = null;
        try {
            // 根据序列号获取所有在线的设备上已录入的人脸列表
            deviceFaceCodeList = this.getDeviceFaceCodeList(dto);
        } catch (Exception ex) {
            exception = ex;
            log.error("", ex);
        } finally {
            log.debug("{}deviceFaceCodeList={}", dto.getSerialNumber(), JSON.toJSONString(deviceFaceCodeList));
            if (deviceFaceCodeList == null || deviceFaceCodeList.isEmpty()) {
                String msg = "getFaceCodesList指令异常";
                log.warn(msg); // 主要是“客户端不存在或客户端不在线”
                if (exception != null && exception.getMessage() != null) {
                    msg = msg + "," + (exception.getMessage().length() > 100 ? exception.getMessage().substring(0, 100) : exception.getMessage());
                }
                CnpLogEntity entity = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.ERROR, "face", msg);
                entity.setSerialNum(dto.getSerialNumber());
                cnpLogService.log(entity);
                return;
            }
        }
        for (PushMessageAckVO faceCodeAck : deviceFaceCodeList) {
            String sessionId = faceCodeAck.getSessionId();
            if (!faceCodeAck.getOk()) {
                // 列表获取超时或其他异常，这里记录日志
                CnpLogEntity entity = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.ERROR, "face", "读取储存人脸照片列表异常：" + faceCodeAck.getResponse(), null, sessionId);
                entity.setSerialNum(dto.getSerialNumber());
                cnpLogService.log(entity);
                continue;
            }
            // 设备上已储存人脸列表 ["police-123456"]
            JSONArray storeFaceCode = (JSONArray) faceCodeAck.getResponse();
            log.debug("{}储存人脸 {}", dto.getSerialNumber(), JSON.toJSONString(storeFaceCode));
            // 待下发人员照片集合
            List<PersonalInfoVO> listPrsonnelFace = new ArrayList<>();
            // 如果是仓内屏需要下发对应监室下所有人员照片信息
            if (SocketActionConstants.PushMessageTerminalEnum.CNP.name().equals(dto.getTerminal())) {
                List<PersonalInfoVO> listPrisonerFace = cnpSocketDao.getPrisonerPhotoByRoom(roomId);
                listPrsonnelFace.addAll(listPrisonerFace);
            } else if (SocketActionConstants.PushMessageTerminalEnum.CWP.name().equals(dto.getTerminal())) {
                // 仓外屏
                for (String rid : roomIds) {
                    List<PersonalInfoVO> listPrisonerFace = cnpSocketDao.getPrisonerPhotoByRoom(rid);
                    listPrsonnelFace.addAll(listPrisonerFace);
                }
            }
            if (!TEST_SKIP_POLICE_ENROLL_FACE) {
                // 获取对应监所下所有警员照片信息，仓内屏仓外屏都需要下发人脸
                List<PersonalInfoVO> listPolceFace = cnpSocketDao.getPrisonPolicePhotoByRoom(roomId);
                listPrsonnelFace.addAll(listPolceFace);
            }
//            PersonalInfoVO test_prisoner_info = new PersonalInfoVO();
//            test_prisoner_info.setPhoto("/lichunming/face/lcm1.jpg");
//            test_prisoner_info.setRybh("lcm_sffsssffs1");
//            test_prisoner_info.setType("prisoner");
//            listPrsonnelFace.add(test_prisoner_info);
            this.enrollFaceByImg(dto, listPrsonnelFace, storeFaceCode, sessionId);
        }
    }

    /**
     * 录入人脸照片。同时清理之前存在现在不存在人脸
     *
     * @param photoList     {rybh，photo } 。 设备中应储存人脸列表
     * @param storeFaceCode 已经储存的人员列表（PRISONER_CODE_PREFIX + p.getString("rybh") 格式）
     * @param sessionId
     */
    private void enrollFaceByImg(DeviceInitializeDto dto, List<PersonalInfoVO> photoList, JSONArray storeFaceCode, String sessionId) {
        // 查询出状态表 某设备已经记录状态的人员
        List<CnpFaceImportStatusEntity> cnpFaceImportStatusList = cnpFaceService.getCnpFaceImportStatusBySerialNumber(dto.getSerialNumber());
        for (PersonalInfoVO p : photoList) {
            String rybhAppCode;
            Integer personnelType;
            String personnelId = p.getRybh();
            if ("police".equals(p.getType())) {
                rybhAppCode = POLICE_CODE_PREFIX + personnelId;
                personnelType = 1;
            } else {
                rybhAppCode = PRISONER_CODE_PREFIX + personnelId;
                personnelType = 2;
            }
            if (storeFaceCode != null && storeFaceCode.stream().anyMatch(n -> n.equals(rybhAppCode))) {
                // 该人员照片已经在设备。则检查状态表 cnp_face_import_status 是否已经有了。如果没有补上成功状态。如果已经有了 则再再检查状态是否成功，如果不是成功同时也修改状态
                Optional<CnpFaceImportStatusEntity> op = cnpFaceImportStatusList.stream().filter(f -> personnelType.equals(f.getPersonnelType()) && (personnelId != null && personnelId.equals(f.getPersonnelCode()))).findFirst();
                boolean f = false;
                if (op.isPresent()) {
                    CnpFaceImportStatusEntity statusEntity = op.get();
                    if (statusEntity.getImportState() == null || "0".equals(statusEntity.getImportState() + "")) {
                        f = true;
                    }
                } else {
                    f = true;
                }
                if (f) {
                    cnpFaceService.saveCnpFaceImportStatus(personnelType, personnelId, dto.getSerialNumber(), true, "ok");
                }
            }
            if (storeFaceCode != null && storeFaceCode.stream().noneMatch(n -> n.equals(rybhAppCode))) {
                String originalPhoto = null;
                if (StringUtils.isNotBlank(p.getPhoto()) && !"null".equals(p.getPhoto())) {
                    originalPhoto = FileUrlUtils.concatUrl(p.getPhoto(), pathPrefix);
                }
                String photo = originalPhoto;
                // 是否优先使用补录照片下发
                boolean useCnpFace = false;
                // 优先 补录照片
                List<CnpFaceEntity> cnpFaceEntities = cnpFaceDao.selectList(new LambdaQueryWrapper<CnpFaceEntity>().eq(CnpFaceEntity::getPersonnelType, personnelType).eq(CnpFaceEntity::getPersonnelCode, personnelId).select(CnpFaceEntity::getId, CnpFaceEntity::getPhoto));
                if (cnpFaceEntities.size() > 0) {
                    CnpFaceEntity cnpFace = cnpFaceEntities.get(0);
                    if (cnpFace != null && StringUtils.isNotBlank(cnpFace.getPhoto()) && !"null".equals(cnpFace.getPhoto())) {
                        photo = FileUrlUtils.concatUrl(cnpFace.getPhoto(), pathPrefix);
                        log.info("人员\"{}\"优先使用补录照片下发 - {}", rybhAppCode, photo);
                        useCnpFace = true;
                    }
                }
                PushMessageForm f = new PushMessageForm(PushMessageTargetEnum.android.name(), SocketActionConstants.enrollFaceByImg).setSessionId(sessionId);
                if (StringUtils.isBlank(photo) || "null".equals(photo)) {
                    log.info("人员\"{}\"照片空", rybhAppCode);
                    CnpLogEntity entity = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.ERROR, "face", "人脸照片下发异常：照片空", rybhAppCode, sessionId);
                    entity.setSerialNum(dto.getSerialNumber());
                    cnpLogService.log(entity);
                    // 记录状态
                    this.saveCnpFaceImportStatus(rybhAppCode, null, dto.getSerialNumber(), false, "人脸照片下发异常：照片空");
                    continue;
                }
                String photoUrl = photo;
                f.params(photoUrl, rybhAppCode);
                faceOptSleep();
                PushMessageAckVO t_1 = socketService.pushMessageToClientWaitReply(f);

//                // 目的产生随机错误
//                if ((System.currentTimeMillis() % 4) != 0) {
//                    t_1.setOk(false);
//                    t_1.setResponse("我是测试错误信息" + System.currentTimeMillis());
//                }

                if (useCnpFace && !t_1.getOk() && StringUtils.isNotBlank(originalPhoto) && !"null".equals(originalPhoto)) {
                    // 如果使用补录照片录入失败，再次尝试使用原图录入
                    log.info("人员\"{}\"使用补录照片下发失败，再次尝试使用原图下发。上次失败原因-{}", rybhAppCode, t_1.getResponse(Integer.MAX_VALUE));
                    photoUrl = originalPhoto;
                    f.params(originalPhoto, rybhAppCode);
                    faceOptSleep();
                    t_1 = socketService.pushMessageToClientWaitReply(f);
                }
                CnpLogEntity entity;
                List<String> photoUrlReference = Lists.newArrayList(photoUrl); // 使用集合的目的，对象引用传递，为了让tryEnrollFace 方法中修改的照片带出来
                if (t_1.getOk()) {
                    log.info("人员\"{}\"照片下发成功 - {}", rybhAppCode, photoUrl);
                    // 原图或者补录的照片第一次就成功了。记录直接记录到  cnp_face表
                    this.saveCnpFaceImportStatus(rybhAppCode, photoUrl, dto.getSerialNumber(), true, "ok");
                } else {
                    // !"1".equals(personnelType + "")  民警不进行调整亮度
                    if (enrollFaceAmendCounter > 0 && !"1".equals(personnelType + "")) {
                        log.info("人员\"{}\"照片下发失败，再次尝试提升亮度下发。上次失败原因-{}", rybhAppCode, t_1.getResponse(Integer.MAX_VALUE));
                        // 原图或者补录的照片失败了。调整亮度再次尝试录入
                        t_1 = this.tryEnrollFace(rybhAppCode, photoUrlReference, sessionId, dto.getSerialNumber());
                        photoUrl = photoUrlReference.get(0);
                        log.info("人员\"{}\"提升亮度下发{}-{}", rybhAppCode, (t_1.getOk() ? "成功" : "失败"), t_1.getResponse(Integer.MAX_VALUE));
                    } else {
                        log.info("人员\"{}\"照片下发失败。失败原因-{}", rybhAppCode, t_1.getResponse(Integer.MAX_VALUE));
                        this.saveCnpFaceImportStatus(rybhAppCode, photoUrl, dto.getSerialNumber(), false, t_1.getResponse(255));
                    }
                }
                if (t_1.getOk()) {
                    entity = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.INFO, "face", "人脸照片下发成功", rybhAppCode, sessionId, photoUrl);
                } else {
                    // 如果异常android端会记录。这里可能会重复记录
                    entity = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.ERROR, "face", ("人脸照片下发异常：" + t_1.getResponse()), rybhAppCode, sessionId, photoUrl);
                }
                entity.setSerialNum(dto.getSerialNumber());
                cnpLogService.log(entity);
            }
        }
        log.debug("{}开始清理照片 storeFaceCode.size={}, photoList.size={}", dto.getSerialNumber(), (storeFaceCode == null ? "null" : storeFaceCode.size()), photoList.size());
        // 清理照片。之前录入但是现在已经没有的
        if (storeFaceCode != null) {
            List<String> collect = photoList.stream().map(m -> m.getRybh()).collect(Collectors.toList());
            for (Object rybhObj : storeFaceCode) {
                String rybh = rybhObj + "";
                // substring是因为返回的设备人脸列表是包含police- 等前缀
                if (collect.contains(rybh.substring(rybh.indexOf("-") + 1))) {
                    continue;
                }
                faceOptSleep();
                PushMessageForm form2 = new PushMessageForm(PushMessageTargetEnum.android.name(), SocketActionConstants.cleanStoreFace).setSessionId(sessionId);
                form2.params(rybh);
                PushMessageAckVO ask = socketService.pushMessageToClientWaitReply(form2);
                // 记录删除照片日志
                CnpLogEntity entity;
                if (ask.getOk()) {
                    entity = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.INFO, "face", "人脸照片删除成功", rybh, sessionId);
                } else {
                    entity = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.ERROR, "face", ("人脸照片删除异常：" + ask.getResponse()), rybh, sessionId);
                }
                entity.setSerialNum(dto.getSerialNumber());
                cnpLogService.log(entity);
            }
        }
        log.debug("{}清理照片结束", dto.getSerialNumber());
        // 清理状态表 cnp_face_import_status 已经不是该仓的人的状态
        List<String> waitDeleted = new ArrayList<>();
        for (CnpFaceImportStatusEntity entity : cnpFaceImportStatusList) {
            final String tempType = ("1".equals(entity.getPersonnelType() + "")) ? POLICE_CODE_PREFIX : PRISONER_CODE_PREFIX;
            final String type = tempType.replace("-", "");
            boolean match = photoList.stream().noneMatch(f -> type.equals(f.getType()) && entity.getPersonnelCode() != null && entity.getPersonnelCode().equals(f.getRybh()));
            if (match) {
                waitDeleted.add(entity.getId());
            }
        }
        if (!waitDeleted.isEmpty()) {
            cnpFaceImportStatusDao.deleteBatchIds(waitDeleted);
        }
    }

    /**
     * 再次尝试录入照片
     *
     * @param rybhAppCode
     * @param photoUrlReference 使用集合的目的，对象引用传递，为了让tryEnrollFace 方法中修改的照片带出来
     * @param sessionId
     * @param serialNumbe
     * @return
     */
    @Override
    public PushMessageAckVO tryEnrollFace(String rybhAppCode, List<String> photoUrlReference, String sessionId, String serialNumbe) {
        String photoPath = photoUrlReference.get(0);
        PushMessageAckVO errAsk = null;
        if (rybhAppCode.startsWith(POLICE_CODE_PREFIX)) {
            // 民警不进行 重试
            errAsk = new PushMessageAckVO(sessionId);
            errAsk.setOk(false);
            errAsk.setResponse("民警不进行重试");
            this.saveCnpFaceImportStatus(rybhAppCode, photoPath, serialNumbe, false, errAsk.getResponse(255));
            return errAsk;
        }
        if (enrollFaceAmendCounter <= 0) {
            errAsk = new PushMessageAckVO(sessionId);
            errAsk.setOk(false);
            errAsk.setResponse("enrollFaceAmendCounter <= 0");
            this.saveCnpFaceImportStatus(rybhAppCode, photoPath, serialNumbe, false, errAsk.getResponse(255));
            return errAsk;
        }
        try {
            BufferedImage image = ImageIORead(photoPath);
            if (image == null) {
                throw new RuntimeException(String.format("读取照片失败（%s）！", photoPath));
            }
            for (int i = 0; i < enrollFaceAmendCounter; i++) {
                // float rate = 1 + (i + 1) * enrollFaceAmendRate * 0.01f;
                // 避免浮点数运算时的舍入误差
                BigDecimal rateDecimal = BigDecimal.valueOf(1).add(BigDecimal.valueOf(i + 1).multiply(BigDecimal.valueOf(enrollFaceAmendRate)).multiply(BigDecimal.valueOf(0.01)));
                float rate = rateDecimal.floatValue();
                photoPath = this.adjustImageAndUploadFtp(image, rate, rybhAppCode);
                log.debug("人员{}修正照片亮度重新录入 rate: {}", rybhAppCode, rate);
                PushMessageForm f = new PushMessageForm(PushMessageTargetEnum.android.name(), SocketActionConstants.enrollFaceByImg).setSessionId(sessionId);
                f.params(photoPath, rybhAppCode);
                PushMessageAckVO ask = socketService.pushMessageToClientWaitReply(f);
                photoUrlReference.set(0, photoPath); // 图片变动，让方法调用者 可以取到最新的图片地址
                if (ask.getOk()) {
                    // 成功
                    this.saveCnpFaceImportStatus(rybhAppCode, photoPath, serialNumbe, true, "ok");
                    return ask;
                }
                errAsk = ask;
                // 失败继续 提高亮度录入
            }
            // 多次失败
            this.saveCnpFaceImportStatus(rybhAppCode, photoPath, serialNumbe, false, errAsk.getResponse(255));
            return errAsk;
        } catch (Exception ex) {
            log.error("人员{}修正照片亮度重新录入异常", rybhAppCode, ex);
            errAsk = new PushMessageAckVO(sessionId);
            errAsk.setOk(false);
            String errMsg = ex.getMessage() + (ex.getCause() == null ? "" : (" Cause: " + ex.getCause().getClass().getName() + "::" + ex.getCause().getMessage()));
            errAsk.setResponse(errMsg);
            this.saveCnpFaceImportStatus(rybhAppCode, photoPath, serialNumbe, false, errAsk.getResponse(255));
            return errAsk;
        }
    }

    /**
     * 从url中读取图片
     *
     * @param photoPath
     * @return
     * @throws Exception
     */
    private BufferedImage ImageIORead(String photoPath) throws Exception {
        try {
            //        BufferedImage image = ImageIO.read(new URL(photoPath));
            URI targetUri = new URI(photoPath);
            SkipSSLSimpleClientHttpRequestFactory requestFactory = new SkipSSLSimpleClientHttpRequestFactory();
            requestFactory.setReadTimeout(3000);
            requestFactory.setConnectTimeout(10000);
            ClientHttpRequest delegate = requestFactory.createRequest(targetUri, HttpMethod.resolve("GET"));
            ClientHttpResponse clientHttpResponse = null;
            try {
                clientHttpResponse = delegate.execute();
                InputStream inputStream = clientHttpResponse.getBody();
                BufferedImage image = ImageIO.read(inputStream);
                return image;
            } finally {
                if (clientHttpResponse != null) {
                    try {
                        clientHttpResponse.close();
                    } catch (Exception e) {
                    }
                }
            }
        } catch (Exception ex) {
            log.error("", ex);
        }
        return null;
    }

    /**
     * 照片录入
     *
     * @param rybhAppCode
     * @param photo1
     * @param photo2
     * @param sessionId
     * @param serialNumbe
     * @return
     */
    public PushMessageAckVO enrollFaceSync(String rybhAppCode, String photo1, String photo2, String sessionId, String serialNumbe) {
        synchronized (FACE_LOCK) {
            return this.enrollFace(rybhAppCode, photo1, photo2, sessionId, serialNumbe);
        }
    }

    /**
     * 照片录入
     * 尽量不要直接使用
     *
     * @param rybhAppCode
     * @param photo1      第一优先级照片
     * @param photo2      第二优先级照片
     * @param sessionId
     * @param serialNumbe
     * @return
     */
    public PushMessageAckVO enrollFace(String rybhAppCode, String photo1, String photo2, String sessionId, String serialNumbe) {
        if (StringUtils.isNotBlank(photo1) && !"null".equals(photo1)) {
            photo1 = FileUrlUtils.concatUrl(photo1, pathPrefix);
        }
        if (StringUtils.isNotBlank(photo2) && !"null".equals(photo2)) {
            photo2 = FileUrlUtils.concatUrl(photo2, pathPrefix);
        }
        PushMessageAckVO errAsk;
        String photoUrl = photo1;
        PushMessageForm f = new PushMessageForm(PushMessageTargetEnum.android.name(), SocketActionConstants.enrollFaceByImg).setSessionId(sessionId);
        if (StringUtils.isBlank(photoUrl) || "null".equals(photoUrl)) {
            log.info("人员\"{}\"照片空", rybhAppCode);
            errAsk = new PushMessageAckVO(sessionId);
            errAsk.setOk(false);
            errAsk.setResponse("照片空");
        } else {
            f.params(photoUrl, rybhAppCode);
            errAsk = socketService.pushMessageToClientWaitReply(f);
        }
        if (!errAsk.getOk() && (StringUtils.isNotBlank(photo2) && !"null".equals(photo2))) {
            // 使用第二优先级图片
            log.info("人员\"{}\"使用第二优先级照片录入。上次失败原因-{}", rybhAppCode, errAsk.getResponse(Integer.MAX_VALUE));
            photoUrl = photo2;
            f.params(photoUrl, rybhAppCode);
            errAsk = socketService.pushMessageToClientWaitReply(f);
        }
        if (errAsk.getOk()) {
            log.info("人员\"{}\"照片录入成功", rybhAppCode);
            this.saveCnpFaceImportStatus(rybhAppCode, photoUrl, serialNumbe, true, "ok");
        } else {
            // 民警不进行调整亮度
            if (StringUtils.isNotBlank(photoUrl) && !"null".equals(photoUrl) && !rybhAppCode.startsWith(POLICE_CODE_PREFIX)) {
                // 两次都失败，尝试
                log.info("人员\"{}\"照片录入失败，继续多次提高亮度尝试。上次失败原因-{}", rybhAppCode, errAsk.getResponse(Integer.MAX_VALUE));
                List<String> photoUrlReference = Lists.newArrayList(photoUrl);
                errAsk = this.tryEnrollFace(rybhAppCode, photoUrlReference, sessionId, serialNumbe);
                if (errAsk.getOk()) {
                    log.info("人员\"{}\"多次提高亮度尝试下发成功", rybhAppCode);
                } else {
                    log.info("人员\"{}\"多次提高亮度尝试下发失败。失败原因-{}", rybhAppCode, errAsk.getResponse(Integer.MAX_VALUE));
                }
            } else {
                log.info("人员\"{}\"照片录入失败。失败原因-{}", rybhAppCode, errAsk.getResponse(Integer.MAX_VALUE));
                this.saveCnpFaceImportStatus(rybhAppCode, photoUrl, serialNumbe, false, errAsk.getResponse(255));
            }
        }
        return errAsk;
    }

    /**
     * 记录导入 台账
     *
     * @param rybhAppCode
     * @param photo
     * @param serialNumbe
     * @param success
     * @param response
     */
    private void saveCnpFaceImportStatus(String rybhAppCode, String photo, String serialNumbe, boolean success, String response) {
        Integer personnelType = null;
        String personnelId = null;
        if (rybhAppCode.startsWith(POLICE_CODE_PREFIX)) {
            personnelType = 1;
            personnelId = rybhAppCode.replaceFirst(POLICE_CODE_PREFIX, "");
        } else if (rybhAppCode.startsWith(PRISONER_CODE_PREFIX)) {
            personnelType = 2;
            personnelId = rybhAppCode.replaceFirst(PRISONER_CODE_PREFIX, "");
        }
        if (personnelType == null || StringUtils.isBlank(personnelId)) {
            log.warn("人脸库管理-导入状态保存 人员类型空::{}", rybhAppCode);
            return;
        }
        cnpFaceService.saveCnpFaceImportStatus(personnelType, personnelId, serialNumbe, success, response);
    }

    /**
     * 调整图片亮度 ，上传ftp
     *
     * @param image
     * @param lumRate 需要调节亮度到该比率
     * @return
     * @throws Exception
     */
    private String adjustImageAndUploadFtp(BufferedImage image, float lumRate, String rybhAppCode) throws Exception {
        BufferedImage image1 = ImageUtils.lumAdjustment2(image, lumRate);
        File tempFile = File.createTempFile("zhjg-", ".jpg");
        try {
            String[] rybhAppCodeArr = rybhAppCode.split("-");
            ImageIO.write(image1, "JPG", tempFile);
            try (FileInputStream fileInput = new FileInputStream(tempFile)) {
                MultipartFile file = new MockMultipartFile("file", "file.jpg", "image/jpeg", fileInput);
                R<BaseFileVo> r = fileApi.uploadFile(file, ("terminal-face/" + rybhAppCodeArr[0]), (rybhAppCodeArr[1] + ".jpg"));
                BaseFileVo response = r.getData();
                log.debug("文件传响应：{}", JSON.toJSONString(r));
                if (response != null) {
                    if (StringUtils.isNotBlank(response.getAccessUrl())) {
                        return response.getAccessUrl();
                    }
                    if (StringUtils.isNotBlank(response.getFilePath())) {
                        // 兼容
                        return response.getFilePath();
                    }
                }
                throw new BaseException("文件上传异常");
            }
        } finally {
            if (tempFile != null) {
                try {
                    tempFile.delete();
                } catch (Exception e) {

                }
            }
        }
    }

    /**
     * 对人脸SDK操作每次操作后停顿下。 同时也不允许对sdk并发操作
     */
    private void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (Exception e) {
        }
    }

    private void faceOptSleep() {
        this.sleep(0L);
    }

    /**
     * 获取设备上已经储存的人员照片
     *
     * @param dto
     * @return
     */
    @Override
    public List<PushMessageAckVO> getDeviceFaceCodeList(DeviceInitializeDto dto) {
        synchronized (FACE_LOCK) {
            List<PushMessageAckVO> deviceFaceCodeList = null;
            try {
                PushMessageForm from1 = new PushMessageForm(PushMessageTargetEnum.android.name(), SocketActionConstants.getFaceCodesList);
                from1.setTerminal(dto.getTerminal());
                if (dto.getSerialNumber() != null && dto.getSerialNumber().length() > 0) {
                    // 优先根据序列号
                    from1.setSerialNumbers(Lists.newArrayList(dto.getSerialNumber()));
                    deviceFaceCodeList = socketService.pushMessageToSerialNumberWaitReplyMultiple(from1);
                } else if (dto.getRoomId() != null && dto.getRoomId().length() > 0) {
                    from1.setRoomIds(dto.getRoomIds());
                    from1.setTerminal(dto.getTerminal());
                    deviceFaceCodeList = socketService.pushMessageToRoomWaitReplyMultiple(from1);
                }
                return deviceFaceCodeList;
            } catch (Exception e) {
                log.error("", e);
            }
            if (deviceFaceCodeList == null || deviceFaceCodeList.size() == 0) {
                return null;
            }
            return null;
        }
    }

    @Override
    public List<PushMessageAckVO> getDeviceFaceCodeList(String terminal, String serialNumber, String roomId) {
        DeviceInitializeDto dto = new DeviceInitializeDto();
        dto.setTerminal(terminal);
        dto.setSerialNumber(serialNumber);
        dto.setRoomIds(Lists.newArrayList(roomId));
        return this.getDeviceFaceCodeList(dto);
    }

    /**
     * 根据会话id同步录入人脸照片
     *
     * @param rybhAppCode
     * @param photo
     * @param sessionId
     */
    @Override
    public void enrollFaceByImgBySessionId(String rybhAppCode, String photo, String sessionId) {
        if (photo == null || photo.length() == 0 || "null".equals(photo.trim())) {
            throw new IllegalArgumentException("照片为空");
        }
        try {
            synchronized (FACE_LOCK) {
                PushMessageForm f = new PushMessageForm(PushMessageTargetEnum.android.name(), SocketActionConstants.enrollFaceByImg).setSessionId(sessionId);
                if (!photo.startsWith("http")) {
                    photo = FileUrlUtils.concatUrl(photo, pathPrefix);
                }
                f.params(photo, rybhAppCode);
                faceOptSleep();
                PushMessageAckVO t_1 = socketService.pushMessageToClientWaitReply(f);
                if (!t_1.getOk()) {
                    throw new RuntimeException(t_1.getResponse() + "");
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public void chackAndEnrollFaceByImgByRoomId(String rybhAppCode, List<String> photoUrlReference, String roomId, String terminal) {
        this.enrollFaceByImgByRoomId(rybhAppCode, photoUrlReference, roomId, terminal, false);
    }

    public void enrollFaceByImgByRoomId(String rybhAppCode, List<String> photoUrlReference, String roomId, String terminal, boolean force) {
        try {
            log.debug("into 方法 chackAndEnrollFaceByImgByRoomId::人员{} photoUrlReference:{}, roomId:{}, terminal:{}", rybhAppCode, JSON.toJSONString(photoUrlReference), roomId, terminal);
            String photo = photoUrlReference.get(0);
            String originalPhoto = null;
            if (StringUtils.isNotBlank(photo) && !"null".equals(photo)) {
                if (!photo.startsWith("http")) {
                    photo = FileUrlUtils.concatUrl(photo, pathPrefix);
                }
                originalPhoto = photo;
            }
            photo = originalPhoto;
            Integer personnelType = this.parseCnpFacePersonnelType(rybhAppCode);
            String rybh = rybhAppCode;
            if (rybhAppCode.contains("-")) {
                rybh = rybhAppCode.split("-")[1];
            }
            boolean useCnpFace = false;
            String cnpFacePhoto = cnpFaceService.getCnpFacePhoto(personnelType, rybh);
            if (StringUtils.isNotBlank(cnpFacePhoto)) {
                photo = cnpFacePhoto;
                useCnpFace = true;
                log.info("人员\"{}\"查找到存在补录照片，优先使用补录照片录入", rybh);
            }
            photoUrlReference.set(0, photo);
            List<String> clientIds = new ArrayList<>();
            Map<String, PushMessageAckVO> clientDeviceFaceCode = new HashMap<>();
            // 获取监室对应的客户端，force == true时会获取设备已录入人脸
            if (force) {
                clientIds = socketService.getSessionByRoomIds(Lists.newArrayList(roomId), terminal);
                if (clientIds.isEmpty()) {
                    log.info("方法 chackAndEnrollFaceByImgByRoomId::无在线客户端 roomId:{}, terminal:{}", roomId, terminal);
                    throw new RuntimeException(String.format("监室%s(%s)无在线客户端", roomId, terminal));
                }
            } else {
                List<PushMessageAckVO> deviceFaceCodeList = this.getDeviceFaceCodeList(terminal, null, roomId);
                if (deviceFaceCodeList == null) {
                    throw new RuntimeException("读取人脸录入列表异常");
                }
                PushMessageAckVO t_ask = null;
                for (PushMessageAckVO ask : deviceFaceCodeList) {
                    if (!ask.getOk()) {
                        log.warn(ask.getResponse() + "");
                        t_ask = ask;
                        continue;
                    }
                    clientIds.add(ask.getSessionId());
                    clientDeviceFaceCode.put(ask.getSessionId(), ask);
                }
                if (clientIds.isEmpty()) {
                    String errM = String.format("监室%s(%s)%s", roomId, terminal, (t_ask == null ? "无在线客户端" : ("读取人脸录入列表异常：" + t_ask.getResponse())));
                    log.info(errM);
                    throw new RuntimeException(errM);
                }
            }
            SocketRelationMapVO socketInfo = socketService.getSocketInfo();
            Map<String, String> serialNumberMap = socketInfo.getSerialNumberMap();
            // 遍历该人员当前所在监室的每个终端
            for (String sessionId : clientIds) {
                // 当前会话的序列号
                String serialNumber = serialNumberMap.get(sessionId);
                if (StringUtils.isBlank(serialNumber)) {
                    // 这种情况存在理论不合理的
                    log.info("方法 chackAndEnrollFaceByImgByRoomId::人员{}serialNumber isBlank. roomId={}", rybhAppCode, roomId);
                    serialNumber = roomId;
                }
                boolean alreadyExist = false; // 设备上是否已存在该人员照片
                if (!force) {
                    JSONArray storeFaceCode = (JSONArray) clientDeviceFaceCode.get(sessionId).getResponse();
                    if (storeFaceCode != null) {
                        alreadyExist = storeFaceCode.contains(rybhAppCode); // 设备上是否存在该人员照片
                    }
                }
                if (alreadyExist) {
                    // 照片已经存在
                    log.info("方法 chackAndEnrollFaceByImgByRoomId::人员{}照片已存在 {}", rybhAppCode, serialNumber);
                } else {
                    if (StringUtils.isBlank(photo) && StringUtils.isBlank(originalPhoto)) {
                        this.saveCnpFaceImportStatus(rybhAppCode, null, serialNumber, false, "照片空");
                        continue;
                    }
                    boolean ok = false;
                    Exception error = null;
                    try {
                        this.faceOptSleep();
                        this.enrollFaceByImgBySessionId(rybhAppCode, photo, sessionId);
                        ok = true;
                    } catch (Exception ex) {
                        log.error("人员\"{}\" {}", rybhAppCode, photo, ex);
                        error = ex;
                    }
                    log.info("方法 chackAndEnrollFaceByImgByRoomId::人员{}录入照片到{} {}{}", rybhAppCode, terminal, serialNumber, (ok ? "成功" : "失败"));
                    if (useCnpFace && !ok) {
                        // 如果刚才使用补录照片录入，并且录入失败。则使用原图照片再次尝试
                        log.info("人员\"{}\"优先使用补录照片录入失败，再次尝试使用原图录入", rybh);
                        photo = originalPhoto;
                        photoUrlReference.set(0, photo);
                        try {
                            this.faceOptSleep();
                            this.enrollFaceByImgBySessionId(rybhAppCode, photo, sessionId);
                            ok = true;
                        } catch (Exception ex) {
                        }
                        log.info("方法 chackAndEnrollFaceByImgByRoomId::人员{}录入照片到{} {}{}", rybhAppCode, terminal, serialNumber, (ok ? "成功" : "失败"));
                    }
                    if (ok) {
                        this.saveCnpFaceImportStatus(rybhAppCode, photo, serialNumber, true, "ok");
                        CnpLogEntity entity = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.INFO, "face", "人脸照片下发成功", rybhAppCode, photo, roomId);
                        entity.setSerialNum(serialNumber);
                        cnpLogService.log(entity);
                    } else {
                        // !"1".equals(personnelType + "")  民警不进行调整亮度
                        if (enrollFaceAmendCounter > 0 && !"1".equals(personnelType + "")) {
                            log.info("人员\"{}\"尝试提高亮度继续录入", rybh);
                            // 提高亮度继续录入
                            PushMessageAckVO t_1 = this.tryEnrollFace(rybhAppCode, photoUrlReference, sessionId, serialNumber);
                            if (!t_1.getOk()) {
                                // 录入不通过，抛出异常让上级处理
//                            throw new RuntimeException(t_1.getResponse(255));
                                CnpLogEntity entity = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.ERROR, "face", "人脸照片下发失败", rybhAppCode, photo, roomId);
                                entity.setSerialNum(serialNumber);
                                cnpLogService.log(entity);
                            }
                        } else {
                            // String photoUrl = photoUrlReference.get(0);
                            String photoUrl = photo;
                            String errMsg = (error == null || error.getMessage() == null) ? "人脸照片下发失败" : (error.getMessage().length() > 200 ? error.getMessage().substring(0, 200) : error.getMessage());
                            this.saveCnpFaceImportStatus(rybhAppCode, photoUrl, serialNumber, false, errMsg);
                            CnpLogEntity entity = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.ERROR, "face", errMsg, rybhAppCode, photo, roomId);
                            entity.setSerialNum(serialNumber);
                            cnpLogService.log(entity);
                        }
                    }
// endregion
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    /**
     * 根据会话id同步删除照片
     *
     * @param rybhAppCode
     * @param sessionId
     */
    @Override
    public void cleanStoreFaceBySessionId(String rybhAppCode, String sessionId) {
        try {
            synchronized (FACE_LOCK) {
                PushMessageForm form2 = new PushMessageForm(PushMessageTargetEnum.android.name(), SocketActionConstants.cleanStoreFace).setSessionId(sessionId);
                form2.params(rybhAppCode);
                PushMessageAckVO ask = socketService.pushMessageToClientWaitReply(form2);
                if (!ask.getOk()) {
                    throw new RuntimeException(ask.getResponse() + "");
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 删除 cnp_face 表某个设备上某人员照片记录
     *
     * @param rybhAppCode
     * @param serialNumber
     */
    private void deleteCnpFace(String rybhAppCode, String serialNumber) {
        // TODO
    }

    /**
     * 根据会话删除会记录成功日志
     *
     * @param rybhAppCode
     * @param sessionId
     * @param logSerialNum
     */
    public void cleanStoreFaceBySessionIdLog(String rybhAppCode, String sessionId, String logSerialNum) {
        this.cleanStoreFaceBySessionId(rybhAppCode, sessionId);
        CnpLogEntity entity = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.ERROR, "face", "人脸照片删除成功", rybhAppCode, sessionId);
        entity.setSerialNum(logSerialNum);
        cnpLogService.log(entity);
    }

    /**
     * 根据序列号删除设备内人员照片，同时清除 cnp_face 表
     *
     * @param rybhAppCode
     * @param serialNumber
     * @return
     */
    @Override
    public boolean cleanStoreFaceBySerialNumber(String rybhAppCode, String serialNumber) {
        List<String> sessionIds = socketService.getSessionBySerialNumber(Lists.newArrayList(serialNumber));
        if (sessionIds == null || sessionIds.isEmpty()) {
            log.warn("未找到客户端 {}: {}", serialNumber, rybhAppCode);
        }
        boolean ok = true;
        for (String sessionId : sessionIds) {
            try {
                this.cleanStoreFaceBySessionIdLog(rybhAppCode, sessionId, serialNumber);
                this.deleteCnpFace(rybhAppCode, serialNumber);
                this.faceOptSleep();
            } catch (Exception e) {
                ok = false;
                log.error("", e);
            }
        }
        return ok;
    }

    /**
     * 根据监室号删除设备内人员照片(需要指定是内屏 外屏)，同时清除 cnp_face 表
     *
     * @param roomId
     * @param terminal
     * @param rybhAppCode
     */
    @Override
    public boolean cleanStoreFaceByRoomId(String roomId, String rybhAppCode, String terminal) {
        List<String> sessionIds = socketService.getSessionByRoomIds(Lists.newArrayList(roomId), terminal);
        if (sessionIds == null || sessionIds.isEmpty()) {
            log.warn("未找到客户端 {}-{}: {}", terminal, roomId, rybhAppCode);
            return false;
        }
        boolean ok = true;
        SocketRelationMapVO socketInfo = socketService.getSocketInfo();
        Map<String, String> serialNumberMap = socketInfo.getSerialNumberMap();
        for (String sessionId : sessionIds) {
            String serialNumber = serialNumberMap.get(sessionId);
            try {
                this.cleanStoreFaceBySessionIdLog(rybhAppCode, sessionId, StringUtils.isBlank(serialNumber) ? roomId : serialNumber);
                this.deleteCnpFace(rybhAppCode, serialNumber);
                this.faceOptSleep();
            } catch (Exception e) {
                ok = false;
                log.error("", e);
            }
            this.faceOptSleep();
        }
        return ok;
    }

    /**
     * 根据监室号删除设备内人员照片(同时删除内屏 外屏)，同时清除 cnp_face 表
     *
     * @param roomId
     * @param rybhAppCode
     */
    private void cleanStoreFaceByRoomId(String roomId, String rybhAppCode) {
        if (terminalList.contains(SocketActionConstants.PushMessageTerminalEnum.CNP.name())) {
            try {
                this.cleanStoreFaceByRoomId(roomId, rybhAppCode, SocketActionConstants.PushMessageTerminalEnum.CNP.name());
            } catch (Exception ex) {
                log.error("", ex);
            }
        }
        if (terminalList.contains(SocketActionConstants.PushMessageTerminalEnum.CWP.name())) {
            // 删除仓外屏
            List<String> cwpSerialNumberList = cnpSocketDao.getCwpSerialNumberByRoomId(Lists.newArrayList(roomId));
            if (!cwpSerialNumberList.isEmpty()) {
                for (String serialNumber : cwpSerialNumberList) {
                    try {
                        this.cleanStoreFaceBySerialNumber(rybhAppCode, serialNumber);
                    } catch (Exception e) {
                        log.error("", e);
                    }
                }
            }
        }
    }

    /**
     * 根据 仓内屏的人员code歇息出他的人员类型 对应cnp_face表的类型 1警员 2在押
     *
     * @param rybhAppCode
     * @return
     */
    private Integer parseCnpFacePersonnelType(String rybhAppCode) {
        if (rybhAppCode == null) {
            return null;
        }
        Integer personnelType = null;
        if (rybhAppCode.startsWith(POLICE_CODE_PREFIX)) {
            personnelType = 1;
        } else if (rybhAppCode.startsWith(PRISONER_CODE_PREFIX)) {
            personnelType = 2;
        }
        return personnelType;
    }

    @Override
    public List<PushMessageAckVO> pushVoiceBroadcast(PushVoiceBroadcastForm form) {
        PushMessageForm from1 = new PushMessageForm(PushMessageTargetEnum.web.name(), SocketActionConstants.VoiceBroadcast);
        from1.setTerminal(SocketActionConstants.PushMessageTerminalEnum.CNP.name());
        from1.setRoomIds(form.getRoomIds());
        from1.params(form);
        form.setRoomIds(null);
        List<PushMessageAckVO> deviceFaceCodeList = socketService.pushMessageToRoomWaitReplyMultiple(from1);
        return deviceFaceCodeList;
    }

    /**
     * 终端版本信息
     *
     * @param serialNumber
     */
    private void syncVersionInfo(String serialNumber) {
        String ip = null;
        try {
            Optional<String> optional = baseDeviceInscreenDao.getDeviceIpBySerialNumber(serialNumber).stream().filter(StringUtils::isNotBlank).findFirst();
            if (!optional.isPresent()) {
                log.warn(String.format("同步版本信息失败!设备[%s]ip信息配置丢失", serialNumber));
            }
            ip = optional.get();
            terminalVersionManagementService.syncVersionInfo(ip);
        } catch (Exception ex) {
            log.warn(String.format("同步版本信息失败!设备[%s]ip[%s]", serialNumber, ip), ex);
        }
    }
}
