package com.gosun.zhjg.prison.room.terminal.modules.socket.service;

import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.DeviceInitializeDto;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushVoiceBroadcastForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.PushMessageAckVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface CnpService {

    /**
     * 仓内屏重启做初始化操作
     */
    void rebootInitialize(DeviceInitializeDto dto);

    void roomChange(List<String> roomList);

    /**
     * 人员信息变动通知
     *
     * @param msg
     */
    @Deprecated
    void prisonerChange(String msg);

    /**
     * 照片变动通知
     *
     * @param msg
     */
    @Deprecated
    void zpChange(String msg);

    /**
     * 下发人员照片。会检查在所状态
     * 人员入所照片下发、监室调整
     *
     * @param prisonerId 人员编号
     */
    void sendPersonnelPhoto(@NotNull String prisonerId);

    /**
     * 照片删除。会检查在所状态
     * 人员出所照片删除，监室调整
     *
     * @param prisonerId 人员编号
     * @param oldRoomId  旧监室号。没有的话从出所表中获取
     */
    void deletePersonnelPhoto(@NotNull String prisonerId, String oldRoomId);

    List<PushMessageAckVO> getDeviceFaceCodeList(String terminal, String serialNumber, String roomId);

    List<PushMessageAckVO> getDeviceFaceCodeList(DeviceInitializeDto dto);

    void enrollFaceSync(DeviceInitializeDto dto);

    PushMessageAckVO enrollFaceSync(String rybhAppCode, String photo1, String photo2, String sessionId, String serialNumbe);

    boolean cleanStoreFaceBySerialNumber(String rybhAppCode, String serialNumber);

    boolean cleanStoreFaceByRoomId(String rybhAppCode, String roomId, String terminal);

    void cleanStoreFaceBySessionId(String rybhAppCode, String sessionId);

    void chackAndEnrollFaceByImgByRoomId(String rybhAppCode, List<String> photoUrlReference, String roomId, String terminal);

    void enrollFaceByImgBySessionId(String rybhAppCode, String photo, String sessionId);

    /**
     * 推送仓内屏语音广播
     *
     * @param form
     */
    List<PushMessageAckVO> pushVoiceBroadcast(@RequestBody @Validated PushVoiceBroadcastForm form);

    /**
     * 尝试提升亮度下发照片
     *
     * @param rybhAppCode
     * @param photoUrlReference
     * @param sessionId
     * @param serialNumbe
     * @return
     */
    PushMessageAckVO tryEnrollFace(String rybhAppCode, List<String> photoUrlReference, String sessionId, String serialNumbe);
}
