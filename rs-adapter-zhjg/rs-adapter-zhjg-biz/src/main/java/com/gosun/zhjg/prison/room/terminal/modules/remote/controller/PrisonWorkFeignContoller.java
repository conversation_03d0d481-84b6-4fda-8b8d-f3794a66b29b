package com.gosun.zhjg.prison.room.terminal.modules.remote.controller;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.prison.room.terminal.modules.remote.feign.PrisonWorkFeign;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "zhjg-prison-work")
@RestController
@RequestMapping
public class PrisonWorkFeignContoller {

	@Autowired(required = false)
	private PrisonWorkFeign prisonWorkFeign;

	/**
	 * 未登录首页-值班-获取一周值班表
	 *
	 * {@link com.gosun.zhjg.prison.work.modules.gj.controller.PrisonRoomDutyController.list(PrisonRoomDutyPageDto)}
	 */
	@RequestMapping(value = "/gj/prisonroomduty/list", method = RequestMethod.GET)
	@ApiOperation(value = "未登录首页-值班-获取一周值班表", responseContainer = "List")
	public R<?> prisonroomdutyList(@RequestParam Map<String, Object> form){
		R<?> r = prisonWorkFeign.prisonroomdutyList(form);
		return r;
	}

	/**
	 * 未登录首页-床位-监室床位-列表
	 *
	 * {@link com.gosun.zhjg.prison.work.modules.gj.controller.BasePrisonerBedController.roomBedListOther(BasePrisonerBedOtherDto)}
	 */
	@RequestMapping(value = "/gj/basePrisonerBed/roomBedListOther", method = RequestMethod.GET)
	public R<?> roomBedListOther(@RequestParam Map<String, Object> form){
		R<?> r = prisonWorkFeign.roomBedListOther(form);
		return r;
	}

	/**
	 * 民警登录-人脸补录-获取列表
	 *
	 * {@link com.gosun.zhjg.prison.work.modules.gj.controller.FaceToFaceManageController.facingPage(String)}
	 *
	 * @return
	 */
	@GetMapping("/gj/facetofacemanage/findByRoomInfo")
	public R<?> facetofacemanageFacingPage(@RequestParam("id") String id){
		R<?> r = prisonWorkFeign.facetofacemanageFacingPage(id);
		return r;
	}
}
