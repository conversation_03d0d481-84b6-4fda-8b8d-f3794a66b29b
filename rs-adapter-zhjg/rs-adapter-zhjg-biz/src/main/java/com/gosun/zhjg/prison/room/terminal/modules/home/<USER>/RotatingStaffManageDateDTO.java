package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class RotatingStaffManageDateDTO {

    @ApiModelProperty(value = "轮值日期",required = true)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date staffDate;

    @ApiModelProperty("轮值管理表编号")
    private String staffId;

    @ApiModelProperty(value = "周名",required = true)
    private String weekName;

    @ApiModelProperty("1-待审核 2-已审核")
    private Integer status;

    @ApiModelProperty(value = "轮值人员信息",required = true)
    private List<RotatingStaffPrisonerDTO> prisonerDTOS;
}
