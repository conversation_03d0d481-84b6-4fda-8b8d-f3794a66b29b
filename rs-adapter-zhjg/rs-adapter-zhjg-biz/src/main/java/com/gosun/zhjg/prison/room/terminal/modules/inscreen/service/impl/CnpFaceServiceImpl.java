package com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.common.util.FileUrlUtils;
import com.gosun.zhjg.prison.room.terminal.common.constant.PrisonRoomConstant;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.BaseDeviceInscreenDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.CnpFaceDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.CnpFaceImportStatusDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.CnpFaceForm;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.CnpFacePageDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.BaseDeviceInscreenEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.CnpFaceEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.CnpFaceImportStatusEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.CnpFaceService;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.CnpfacePrisonerVO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.SocketService;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.PushMessageAckVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CnpFaceServiceImpl implements CnpFaceService {


    @Value("${file-url-prefix}")
    private String pathPrefix;

    @Autowired
    private CnpFaceDao cnpFaceDao;

    @Autowired
    private CnpFaceImportStatusDao cnpFaceImportStatusDao;

    @Autowired
    private SocketService socketService;

    @Autowired
    private BaseDeviceInscreenDao baseDeviceInscreenDao;

    @Override
    public String getPersonnelPhoto(Integer personnelType, String personnelId) {
        // 从人脸库查询该人员照片
        String photo = null;
        CnpFaceEntity cnpFace = cnpFaceDao.selectOne(new LambdaQueryWrapper<CnpFaceEntity>().eq(CnpFaceEntity::getPersonnelType, personnelType).eq(CnpFaceEntity::getPersonnelCode, personnelId)
                .select(CnpFaceEntity::getId, CnpFaceEntity::getPhoto));
        if (cnpFace != null) {
            photo = cnpFace.getPhoto();
        }
        // 从对应人员表查询照片
        if (StringUtils.isBlank(photo)) {
            if ("1".equals(personnelType + "")) {
                photo = cnpFaceDao.getPolicePhoto(personnelId);
            } else if ("2".equals(personnelType + "")) {
                photo = cnpFaceDao.getPrisonerPhoto(personnelId);
            }
        }
        if (StringUtils.isNotBlank(photo)) {
            photo = FileUrlUtils.concatUrl(photo, pathPrefix);
        }
        return photo;
    }

    @Override
    public PushMessageAckVO enrollFace(Integer personnelType, String personnelId, String personnelPhoto, String serialNumber) {
        String enrollFaceId;
        if ("1".equals(personnelType + "")) {
            enrollFaceId = PrisonRoomConstant.police + "-" + personnelId;
        } else if ("2".equals(personnelType + "")) {
            enrollFaceId = PrisonRoomConstant.prisoner + "-" + personnelId;
        } else {
            PushMessageAckVO ackVo = new PushMessageAckVO();
            ackVo.setOk(false);
            ackVo.setResponse("无效的参数personnelType");
            return ackVo;
        }
        List<String> clientIds = socketService.getSessionBySerialNumber(Lists.newArrayList(serialNumber));
        PushMessageAckVO ackMessage = new PushMessageAckVO();
        if (clientIds.isEmpty()) {
            ackMessage.setOk(false);
            ackMessage.setResponse("客户端不存在或客户端不在线");
//            return ackMessage;
        } else {
            PushMessageForm pushForm = new PushMessageForm();
            pushForm.setSessionIds(clientIds);
            pushForm.setTarget(SocketActionConstants.PushMessageTargetEnum.android.name());
            pushForm.setAction(SocketActionConstants.enrollFaceByImg);
            pushForm.params(personnelPhoto, enrollFaceId);
            ackMessage = socketService.pushMessageToClientWaitReply(pushForm); // 推送给其中一个客户端
        }
        String response = ackMessage.getResponse(255);
        this.saveCnpFaceImportStatus(personnelType, personnelId, serialNumber, ackMessage.getOk(), ackMessage.getOk() ? "ok" : response);
        return ackMessage;
    }

    @Override
    public void asyncEnrollFace(Integer personnelType, String personnelId, String personnelPhoto, List<String> serialNumbers) {
        new Thread(() -> {
            for (String serialNumber : serialNumbers) {
                try {
                    PushMessageAckVO ackVO = enrollFace(personnelType, personnelId, personnelPhoto, serialNumber);
                    log.info("下发照片到设备{}-{}-Response:{}", (ackVO.getOk() ? "成功" : "失败"), serialNumber, ackVO.getResponse(255));
                } catch (Exception e) {
                    log.error("下发照片到设备异常{}", serialNumber, e);
                }
            }
        }).start();
    }

    @Override
    public void saveCnpFaceImportStatus(Integer personnelType, String personnelId, String serialNumber, boolean state, String response) {
        CnpFaceImportStatusEntity statusEntity = new CnpFaceImportStatusEntity();
        statusEntity.setImportState(state ? 1 : 0);
        statusEntity.setMsg(response);
        statusEntity.setUpdateTime(new Date());
        int updateRows = cnpFaceImportStatusDao.update(statusEntity, new LambdaQueryWrapper<CnpFaceImportStatusEntity>()
                .eq(CnpFaceImportStatusEntity::getSerialNumber, serialNumber)
                .eq(CnpFaceImportStatusEntity::getPersonnelType, personnelType)
                .eq(CnpFaceImportStatusEntity::getPersonnelCode, personnelId)
        );
        if (updateRows == 0) {
            statusEntity.setId(UUID.randomUUID().toString().replaceAll("-", ""));
            statusEntity.setPersonnelType(personnelType);
            statusEntity.setPersonnelCode(personnelId);
            statusEntity.setSerialNumber(serialNumber);
            cnpFaceImportStatusDao.insert(statusEntity);
        }
    }

    /**
     * 获取人脸库维护中的 照片
     *
     * @param personnelType
     * @param personnelId
     * @return
     */
    @Override
    public String getCnpFacePhoto(Integer personnelType, String personnelId) {
        List<CnpFaceEntity> cnpFaceEntities = cnpFaceDao.selectList(new LambdaQueryWrapper<CnpFaceEntity>().eq(CnpFaceEntity::getPersonnelType, personnelType)
                .eq(CnpFaceEntity::getPersonnelCode, personnelId)
                .select(CnpFaceEntity::getId, CnpFaceEntity::getPhoto));
        if (cnpFaceEntities.isEmpty()) {
            return null;
        }
        CnpFaceEntity cnpFaceEntity = cnpFaceEntities.get(0);
        if (cnpFaceEntity == null) {
            return null;
        }
        if (StringUtils.isNotBlank(cnpFaceEntity.getPhoto()) && !"null".equals(cnpFaceEntity.getPhoto().trim())) {
            String photo = FileUrlUtils.concatUrl(cnpFaceEntity.getPhoto(), pathPrefix);
            if (StringUtils.isNotBlank(photo)) {
                return photo;
            }
        }
        return null;
    }

    /**
     * 根据序列号，查询改设备下发状态
     *
     * @param serialNumber
     * @return
     */
    @Override
    public List<CnpFaceImportStatusEntity> getCnpFaceImportStatusBySerialNumber(String serialNumber) {
        List<CnpFaceImportStatusEntity> list = cnpFaceImportStatusDao.selectList(new LambdaQueryWrapper<CnpFaceImportStatusEntity>()
                .eq(CnpFaceImportStatusEntity::getSerialNumber, serialNumber)
                .select(CnpFaceImportStatusEntity::getId, CnpFaceImportStatusEntity::getPersonnelCode, CnpFaceImportStatusEntity::getPersonnelType, CnpFaceImportStatusEntity::getImportState)
        );
        return list;
    }

    @Override
    public void batchSendFace(CnpFaceForm form) {
        List<String> formSerialNumbers = form.getSerialNumbers() == null ? Lists.newArrayList() :
                form.getSerialNumbers().stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        form.setSerialNumbers(formSerialNumbers);
        final int personnelType;
        if ("prisoner".equals(form.getPersonnelType())) {
            personnelType = 2;
            // 在押人员
            CnpFacePageDto form2 = new CnpFacePageDto();
            form2.setPersonnelIdList(form.getPersonnelIdList());
            final List<CnpfacePrisonerVO> faceList = cnpFaceDao.findPrisonerPage(null, form2);
            new Thread(() -> {
                List<BaseDeviceInscreenEntity> deviceList = null;
                boolean useFormParams = true;
                // 如果表单中传递了序列号，则下发到指定的序列号，否则下发到在押人员关联监室的设备
                if (formSerialNumbers.isEmpty()) {
                    List<String> roomIds = faceList.stream().map(CnpfacePrisonerVO::getRoomId).distinct().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                    if (roomIds.isEmpty()) {
                        return;
                    }
                    //@formatter:off
                    deviceList = baseDeviceInscreenDao.selectList(new LambdaQueryWrapper<BaseDeviceInscreenEntity>()
                            .in(BaseDeviceInscreenEntity::getRoomId, roomIds)
                            .in(BaseDeviceInscreenEntity::getDeviceType, Lists.newArrayList(1, 2))
                            .select(BaseDeviceInscreenEntity::getId, BaseDeviceInscreenEntity::getSerialNumber, BaseDeviceInscreenEntity::getRoomId)
                    );
                    //@formatter:on
                    useFormParams = false;
                }
                for (CnpfacePrisonerVO face : faceList) {
                    //@formatter:off
                    List<String> serialNumbers = useFormParams ? formSerialNumbers : deviceList.stream().filter(f -> face.getRoomId() != null && face.getRoomId().equals(f.getRoomId()))
                            .map(BaseDeviceInscreenEntity::getSerialNumber)
                            .filter(StringUtils::isNotBlank)
                            .distinct().collect(Collectors.toList());
                    //@formatter:on
                    for (String serialNumber : serialNumbers) {
                        try {
                            String photo = StringUtils.isBlank(face.getPhoto()) ? face.getPrisonerPhoto() : face.getPhoto();
                            photo = FileUrlUtils.concatUrl(photo, pathPrefix);
                            PushMessageAckVO ackVO = enrollFace(personnelType, face.getPersonnelId(), photo, serialNumber);
                            log.info("下发照片到设备{}-{}-Response:{}", (ackVO.getOk() ? "成功" : "失败"), serialNumber, ackVO.getResponse(255));
                        } catch (Exception e) {
                            log.error("下发照片到设备异常{}", serialNumber, e);
                        }
                    }
                }
            }).start();
        } else if (form.getPersonnelType() != null && form.getPersonnelType().startsWith("police")) {
            personnelType = 1;
            // 民警
            final List<CnpFaceEntity> faceList = cnpFaceDao.findPolicePage(form.getPersonnelIdList());
            new Thread(() -> {
                for (String serialNumber : formSerialNumbers) {
                    for (CnpFaceEntity face : faceList) {
                        try {
                            PushMessageAckVO ackVO = enrollFace(personnelType, face.getPersonnelCode(), face.getPhoto(), serialNumber);
                            log.info("下发照片到设备{}-{}-Response:{}", (ackVO.getOk() ? "成功" : "失败"), serialNumber, ackVO.getResponse(255));
                        } catch (Exception e) {
                            log.error("下发照片到设备异常{}", serialNumber, e);
                        }
                    }
                }
            }).start();
        }
    }
}
