package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * prison_room_test_questions-测评题目表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-07 17:30:04
 */
@Data
@TableName("prison_room_test_questions")
public class PrisonRoomTestQuestionsEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @TableId(type = IdType.ASSIGN_UUID)

    private String id;
    /**
     * 题目
     */
    private String title;
    /**
     * 试卷id
     */
    private String testId;
    /**
     * 选项标准
     */
    private Integer standardOptions;
}
