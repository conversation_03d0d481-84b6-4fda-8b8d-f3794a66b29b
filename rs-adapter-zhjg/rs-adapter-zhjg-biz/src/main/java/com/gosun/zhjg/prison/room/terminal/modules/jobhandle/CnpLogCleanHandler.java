package com.gosun.zhjg.prison.room.terminal.modules.jobhandle;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gosun.zhjg.prison.room.terminal.modules.app.dao.CnpLogDao;
import com.gosun.zhjg.prison.room.terminal.modules.app.entity.CnpLogEntity;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.handler.IJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 仓内屏日志表 cnp_log 定时清理
 *
 * <AUTHOR>
 * @date 2022/9/6 14:20
 */
@Slf4j
@Component
public class CnpLogCleanHandler extends IJobHandler implements InitializingBean {

    @Autowired
    private CnpLogDao cnpLogDao;

    @Override
    public void afterPropertiesSet() throws Exception {
        XxlJobExecutor.registJobHandler("CnpLogCleanHandler", this);
    }

    @Override
    public void execute() throws Exception {
        String params = XxlJobHelper.getJobParam();
        this.job(params);
    }

    public void job(String params) throws Exception {
        // 保留日志天数
        int keepDays = 10;
        if (NumberUtils.isNumber(params)) {
            keepDays = Integer.parseInt(params);
        }
        Date now = new Date();
        Date date = new Date(now.getYear(), now.getMonth(), now.getDate() - keepDays);
        String dateFmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
        log.warn("仓内屏日志清理-\"{}\"前", dateFmt);
        XxlJobHelper.log("仓内屏日志清理-\"{}\"前", dateFmt);
        cnpLogDao.delete(new LambdaQueryWrapper<CnpLogEntity>().lt(CnpLogEntity::getLogTime, date));
    }

}
