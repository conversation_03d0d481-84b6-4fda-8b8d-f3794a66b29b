package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.CnpFacePageDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.CnpFaceEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.CnpFaceCountVO;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.CnpFaceDeviceInscreenVO;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.CnpFaceImportStateVO;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.CnpfacePrisonerVO;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.framework.mybatis.util.UacUserUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

@Mapper
public interface CnpFaceDao extends BaseMapper<CnpFaceEntity> {

    /**
     * 人脸库管理-用户照片补录列表
     *
     * @param page
     * @param form
     * @return
     */
    default List<CnpFaceEntity> findPolicePage(List<String> personnelIdList){
        LambdaQueryWrapperX<CnpFaceEntity> query = new LambdaQueryWrapperX<>();
        query.in(CnpFaceEntity::getPersonnelCode, personnelIdList);
        return selectList(query);

    }

    /**
     * 人脸库管理-被监管人员人脸补录列表
     *
     * @param page
     * @param form
     * @return
     */
    List<CnpfacePrisonerVO> findPrisonerPage(Page page, @Param("form") CnpFacePageDto form);

    /**
     * 人脸库管理-用户人脸补录列表
     *
     * @param page
     * @param form
     * @return
     */
    List<CnpFaceImportStateVO> findPoliceImportStatePage(Page page, @Param("form") CnpFacePageDto form);

    /**
     * 人脸库管理-被监管人员人脸补录列表
     *
     * @param page
     * @param form
     * @return
     */
    List<CnpFaceImportStateVO> findPrisonerImportStatePage(Page page, @Param("form") CnpFacePageDto form);

    /**
     * 查询 在押人员关联仓内屏设备 未进行导入过的数据
     * 《人脸库管理/导入台账》状态空的。（即 该人员关联的设备，无下发记录的人员）
     *
     * @param serialNumberList
     * @return
     */
    List<CnpfacePrisonerVO> findCnpPrisonerNotImportRecords(@Param("serialNumberList") List<String> serialNumberList);

    /**
     * 获取人员照片
     *
     * @param id
     * @return
     */
    @Select("select photo from base_police_info where id = #{id}")
    String getPolicePhoto(@Param("id") String id);

    /**
     * 获取在押人员照片
     *
     * @param rybh
     * @return
     */
    @Select("select front_photo from vw_acp_pm_prisoner where rybh = #{rybh}")
    String getPrisonerPhoto(@Param("rybh") String rybh);

    /**
     * 获取在押人员所关联的设备
     *
     * @param rybh
     * @return
     */
    List<CnpFaceDeviceInscreenVO> getPrisonerAssociatedDevices(@Param("rybh") String rybh);

    /**
     * 获取用户关联的设备
     *
     * @param policeId
     * @return
     */
    default List<CnpFaceDeviceInscreenVO> getPoliceAssociatedDevices(@Param("policeId") String policeId, @Param("prisonId") String prisonId) {
        if (StringUtils.isEmpty(policeId)) {
            return getPoliceAssociatedDevicesByOrgCode(null, prisonId);
        }
        return getPoliceAssociatedDevicesByOrgCode(UacUserUtil.getUserBySfzh(policeId).getOrgId(), prisonId);
    }

    List<CnpFaceDeviceInscreenVO> getPoliceAssociatedDevicesByOrgCode(@Param("orgCode") String orgCode, @Param("prisonId") String prisonId);

    /**
     * 获取各用户类型梳理
     *
     * @param prisonId
     * @return
     */
    @Deprecated
    List<CnpFaceCountVO> getPerTypeUserCount(@Param("prisonId") String prisonId);

    /**
     * 获取监所下在所 在押人员数量
     *
     * @param prisonId
     * @return
     */
    @Deprecated
    Integer getPrisonerCount(@Param("prisonId") String prisonId);

    /**
     * 获取民警 在时间段内新增人脸数量
     *
     * @return
     */
    Integer getPoliceCnpFaceCountByUpdateTime(@Param("prisonId") String prisonId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 获取在押人员 在时间段内新增人脸数量
     *
     * @return
     */
    Integer getPrisonerCnpFaceCountByUpdateTime(@Param("prisonId") String prisonId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
