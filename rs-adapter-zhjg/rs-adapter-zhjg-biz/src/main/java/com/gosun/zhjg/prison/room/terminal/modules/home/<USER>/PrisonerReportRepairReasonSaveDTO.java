package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: l<PERSON><PERSON>feng
 * @date: 2023/8/25 17:51
 */
@Data
public class PrisonerReportRepairReasonSaveDTO {

    @ApiModelProperty("报修原因id（不传为新增）")
    private String id;

    @NotNull(groups = ValidInsertGroup.class, message = "报修原因不能为空")
    @ApiModelProperty(value = "报修原因",required = true)
    private String reasonName;

}
