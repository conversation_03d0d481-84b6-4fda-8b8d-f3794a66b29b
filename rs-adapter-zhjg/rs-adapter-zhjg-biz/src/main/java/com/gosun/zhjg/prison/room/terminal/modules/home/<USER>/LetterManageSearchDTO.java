package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
* letter_manage 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2024-09-27
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="LetterManageSearchDTO对象", description="收信管理")
public class LetterManageSearchDTO extends AbstractPageQueryForm implements Serializable {


   @JsonProperty("id")
   @ApiModelProperty(value = "主键")
   private String id;

   @JsonProperty("prisonerId")
   @ApiModelProperty(value = "人员编号")
   private String prisonerId;

   @JsonProperty("prisonerName")
   @ApiModelProperty(value = "人员姓名")
   private String prisonerName;

   @JsonProperty("roomId")
   @ApiModelProperty(value = "监室号")
   private String roomId;

   @JsonProperty("roomName")
   @ApiModelProperty(value = "监室名字")
   private String roomName;

   @JsonProperty("status")
   @ApiModelProperty(value = "信件状态 1-另行处理 2-待转交 3-待确认 4-已确认")
   private Integer status;

   @JsonProperty("checkStatus")
   @ApiModelProperty(value = "审核流程状态 1-管教待审核 2-管教审核通过 3-科组长待审核 4-科组长审核通过 5-所领导待审核 6-所领导审核通过 7-所领导审核不通过")
   private Integer checkStatus;

   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
   @ApiModelProperty(value = "登记时间-开始")
   @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private Date registerStartTime;

   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
   @ApiModelProperty(value = "登记时间-结束")
   @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private Date registerEndTime;

   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
   @ApiModelProperty(value = "处理时间-开始")
   @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private Date disposeStartTime;

   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
   @ApiModelProperty(value = "处理时间-结束")
   @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private Date disposeEndTime;

   @JsonProperty("readStatus")
   @ApiModelProperty(value = "0-未读 1-已读 ")
   private Integer readStatus;

}
