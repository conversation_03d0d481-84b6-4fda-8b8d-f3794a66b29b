package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
* rotating_staff_prisoner 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2024-10-28
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="RotatingStaffPrisonerDTO对象", description="轮值员管理-轮值人员")
public class RotatingStaffPrisonerDTO implements Serializable {

    @JsonProperty("prisonerId")
    @ApiModelProperty(value = "人员编号",required = true)
    private String prisonerId;

    @ApiModelProperty(value = "在押人员姓名")
    private String prisonerName;

    @ApiModelProperty(value = "在押人员照片")
    private String frontPhoto;

    @ApiModelProperty("重病号")
    private String sickType;

    @ApiModelProperty("风险等级")
    private String riskLevelCode;

    @JsonProperty("staffSortNo")
    @ApiModelProperty(value = "人员序号",required = true)
    private Integer staffSortNo;

}

