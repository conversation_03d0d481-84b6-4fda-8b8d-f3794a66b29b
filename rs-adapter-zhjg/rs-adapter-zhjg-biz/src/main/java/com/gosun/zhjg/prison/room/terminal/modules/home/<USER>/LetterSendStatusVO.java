package com.gosun.zhjg.prison.room.terminal.modules.home.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("寄信状态列表")
public class LetterSendStatusVO {

    @ApiModelProperty(value = "监室号")
    private String roomName;

    @JsonProperty("prisonerName")
    @ApiModelProperty(value = "申请人")
    private String prisonerName;

    @JsonProperty("applyTime")
    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime;

    @ApiModelProperty(value = "寄信状态")
    private String sendStatusName;

    private Integer status;
    /**
     * 审核流程状态 1-管教待审核 2-管教审核通过 3-科组长待审核 4-科组长审核通过 5-所领导待审核 6-所领导审核通过 7-所领导审核不通过
     */
    private Integer checkStatus;


}
