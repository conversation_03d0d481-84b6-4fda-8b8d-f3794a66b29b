package com.gosun.zhjg.prison.room.terminal.modules.home.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.gosun.zhjg.prison.room.terminal.modules.home.dao.BaseSystemRuleConfigDao;
import com.gosun.zhjg.prison.room.terminal.modules.home.dto.BaseSystemRuleConfigKeyValueDto;
import com.gosun.zhjg.prison.room.terminal.modules.home.entity.BaseSystemRuleConfigEntity;
import com.gosun.zhjg.prison.room.terminal.modules.home.service.BaseSystemRuleConfigService;
import com.rs.framework.mybatis.util.UacUserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Validated
@Service("baseSystemRuleConfigService")
public class BaseSystemRuleConfigServiceImpl implements BaseSystemRuleConfigService {

	@Autowired
	private BaseSystemRuleConfigDao baseSystemRuleConfigDao;

	/**
	 * 获取指定配置的值
	 * 优先获取单位自己的配置排序在前
	 *
	 * @param key
	 * @param prisonId
	 * @return
	 */
	@Override
	public List<BaseSystemRuleConfigEntity> get(@NotBlank String prisonId, @NotBlank String key) {
		//@formatter:off
		@SuppressWarnings("unchecked")
		List<BaseSystemRuleConfigEntity> records = baseSystemRuleConfigDao.selectList(new LambdaQueryWrapper<BaseSystemRuleConfigEntity>()
				.eq(BaseSystemRuleConfigEntity::getKey, key)
				.in(BaseSystemRuleConfigEntity::getOrgCode, Lists.newArrayList("default", prisonId))
				.orderByAsc(BaseSystemRuleConfigEntity::getDefaultFlag)
		);
		//@formatter:on
		return records;
	}

	/**
	 * 获取指定配置的值
	 * 优先获取单位自己的配置，其次获取默认配置
	 *
	 * @param key
	 * @param prisonId
	 * @return
	 */
	@Override
	public String getValue(@NotBlank String prisonId, @NotBlank String key) {
		//@formatter:off
		@SuppressWarnings("unchecked")
		List<BaseSystemRuleConfigEntity> records = baseSystemRuleConfigDao.selectList(new LambdaQueryWrapper<BaseSystemRuleConfigEntity>()
				.eq(BaseSystemRuleConfigEntity::getKey, key)
				.in(BaseSystemRuleConfigEntity::getOrgCode, Lists.newArrayList("default", prisonId))
				.select(BaseSystemRuleConfigEntity::getId, BaseSystemRuleConfigEntity::getValue, BaseSystemRuleConfigEntity::getOrgCode)
				.orderByAsc(BaseSystemRuleConfigEntity::getDefaultFlag)
		);
		//@formatter:on
		if (records.isEmpty()) {
			return null;
		}
		// 注意上面sql查询时是进行了排序的，这里直接取第一项
		return records.get(0).getValue();
	}

	/**
	 * 获取多项的值，不存在时仍补充出对象结构
	 *
	 * @param prisonId
	 * @param keyList
	 * @return
	 */
	@Override
	public List<BaseSystemRuleConfigKeyValueDto> getMultiItemValue(@NotBlank String prisonId, @NotEmpty List<String> keyList) {
		//@formatter:off
		@SuppressWarnings("unchecked")
		List<BaseSystemRuleConfigEntity> records = baseSystemRuleConfigDao.selectList(new LambdaQueryWrapper<BaseSystemRuleConfigEntity>()
				.in(BaseSystemRuleConfigEntity::getKey, keyList)
				.in(BaseSystemRuleConfigEntity::getOrgCode, Lists.newArrayList("default", prisonId))
				.select(BaseSystemRuleConfigEntity::getId, BaseSystemRuleConfigEntity::getKey, BaseSystemRuleConfigEntity::getValue, BaseSystemRuleConfigEntity::getOrgCode)
				.orderByAsc(BaseSystemRuleConfigEntity::getDefaultFlag)
		);
		//@formatter:on
		List<BaseSystemRuleConfigKeyValueDto> result = new ArrayList<>();
		for (String key : keyList) {
			// 注意上面sql查询时是进行了排序的，这里直接取第一项
			Optional<BaseSystemRuleConfigEntity> optional = records.stream().filter(record -> (key != null && key.equals(record.getKey()))).findFirst();
			if (optional.isPresent()) {
				BaseSystemRuleConfigEntity item = optional.get();
				BaseSystemRuleConfigKeyValueDto vo = new BaseSystemRuleConfigKeyValueDto(item.getKey(), item.getValue());
				result.add(vo);
			} else {
				result.add(new BaseSystemRuleConfigKeyValueDto(key, null));
			}
		}
		return result;
	}

	@Override
	public void setValue(@NotBlank String prisonId, @NotBlank String key, @NotNull String value) {
		BaseSystemRuleConfigEntity entity = new BaseSystemRuleConfigEntity();
		entity.setKey(key);
		entity.setValue(value);
		entity.setOrgCode(prisonId);
		this.set(entity);
	}

	@Override
	public void setValue(@NotBlank String prisonId, @NotBlank String key, @NotNull String value, String userid, String name) {
		BaseSystemRuleConfigEntity entity = new BaseSystemRuleConfigEntity();
		entity.setKey(key);
		entity.setValue(value);
		this.set(entity);
	}

	@Override
	public void set(@NotNull BaseSystemRuleConfigEntity entity) {
		//@formatter:off
		int effectRow = baseSystemRuleConfigDao.update(entity, new LambdaQueryWrapper<BaseSystemRuleConfigEntity>()
						.eq(BaseSystemRuleConfigEntity::getKey, entity.getKey())
						.eq(BaseSystemRuleConfigEntity::getOrgCode, UacUserUtil.getSessionOrgCode())
		);
		//@formatter:on
		if (effectRow == 0) {
			entity.setDefaultFlag(0);
			baseSystemRuleConfigDao.insert(entity);
		}
	}

}
