package com.gosun.zhjg.prison.room.terminal.common;

import lombok.Getter;

/**
*<AUTHOR>
*@email <EMAIL>
*@date 2022/6/6
*/

@Getter
public enum PrisonerDutyWorkEnum {
    /**
     * 监室值日
     */
    js(1, "监室值日"),
    /**
     * 卫生值日
     */
    ws(2, "卫生值日"),

    /**
     * 劳动值日
     */
    ld(3, "劳动值日");




    private final Integer code;
    private final String msg;

    PrisonerDutyWorkEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getValue(Integer code) {
        for (PrisonerDutyWorkEnum ele : values()) {
            if(ele.getCode().equals(code))
                return ele.getMsg();
        }
        return null;
    }
}
