package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * prison_room_assessment_test-测评试卷表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-07 17:30:04
 */
@Data
@TableName("prison_room_assessment_test")
public class PrisonRoomAssessmentTestEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 主题
     */
    private String subject;
    /**
     * 说明
     */
    private String instructions;
    /**
     * 计分规则
     */
    private String scoringRules;
    /**
     * 标准分
     */
    private BigDecimal standardScore;
    /**
     * 启用标志
     */
    private String useStatus;
    /**
     * 发布人id
     */
    private String publishUserId;
    /**
     * 发布人名字
     */
    private String publishUserName;
    /**
     * 发布时间
     */
    private Date publishTime;

    /***
     * 监所编号
     */
    private String prisonId;
}
