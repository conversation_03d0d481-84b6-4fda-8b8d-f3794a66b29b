package com.gosun.zhjg.prison.room.terminal.modules.terminal.controller;

import com.gosun.zhjg.common.config.UserContext;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dto.TerminalSettingsCnpDto;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dto.TerminalSettingsCwpDto;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.service.TerminalSettingsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 终端-終端设置
 *
 * <AUTHOR>
 * @date 2024/12/12 11:07
 */
@Api(tags = "终端-终端配置")
@RestController
@RequestMapping("terminal/settings")
public class TerminalSettingsController {

	@Autowired
	private TerminalSettingsService terminalSettingsService;

	/**
	 * 终端配置-仓内屏配置-详情
	 */
	@RequestMapping(value = "/cnp/info", method = RequestMethod.GET)
	@ApiOperation(value = "终端配置-仓内屏配置-详情", response = TerminalSettingsCnpDto.class)
	public R<?> cnpInfo() {
		String prisonId = UserContext.getJwtInfo().getPrisonId();
		TerminalSettingsCnpDto vo = terminalSettingsService.getCnpSettingsInfo(prisonId);
		return R.ResponseResult(vo);
	}

	/**
	 * 终端配置-仓内屏配置-保存
	 */
	@RequestMapping(value = "/cnp/save", method = RequestMethod.POST)
	@ApiOperation(value = "终端配置-仓内屏配置-保存", response = TerminalSettingsCnpDto.class)
	public R<?> cnpSave(@RequestBody TerminalSettingsCnpDto form) {
		form.setPrisonId(UserContext.getJwtInfo().getPrisonId());
		String userid = UserContext.getJwtInfo().getUserid();
		String name = UserContext.getJwtInfo().getName();
		terminalSettingsService.saveCnpSettings(form, userid, name);
		return R.ResponseOk();
	}

	/**
	 * 终端配置-仓外屏配置-详情
	 */
	@RequestMapping(value = "/cwp/info", method = RequestMethod.GET)
	@ApiOperation(value = "终端配置-仓外屏配置-详情", response = TerminalSettingsCwpDto.class)
	public R<?> cwpInfo() {
		String prisonId = UserContext.getJwtInfo().getPrisonId();
		TerminalSettingsCwpDto vo = terminalSettingsService.getCwpSettingsInfo(prisonId);
		return R.ResponseResult(vo);
	}

	/**
	 * 终端配置-仓外屏配置-保存
	 */
	@RequestMapping(value = "/cwp/save", method = RequestMethod.POST)
	@ApiOperation(value = "终端配置-仓外屏配置-保存", response = TerminalSettingsCwpDto.class)
	public R<?> cwpSave(@RequestBody TerminalSettingsCwpDto form) {
		form.setPrisonId(UserContext.getJwtInfo().getPrisonId());
		String userid = UserContext.getJwtInfo().getUserid();
		String name = UserContext.getJwtInfo().getName();
		terminalSettingsService.saveCwpSettings(form, userid, name);
		return R.ResponseOk();
	}
}
