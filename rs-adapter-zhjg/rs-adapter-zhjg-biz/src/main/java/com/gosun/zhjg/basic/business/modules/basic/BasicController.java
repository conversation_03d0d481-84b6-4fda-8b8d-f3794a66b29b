package com.gosun.zhjg.basic.business.modules.basic;

import com.gosun.zhjg.common.msg.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Api(value = "基础", tags = "基础")
@RestController
@RequestMapping("/basic")
public class BasicController {

	@RequestMapping(value = "/currentDate", method = RequestMethod.GET)
	@ApiOperation(value = "服务器当前时间", responseContainer = "Map")
	public R<?> currentDate() {
		Map<String, Object> result = new HashMap<String, Object>();
		Date date = new Date();
		result.put("formative", date);
		result.put("now", date.getTime());
		return R.ResponseResult(result);
	}

}
