package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 一日生活 任务调度配置
 *
 * <AUTHOR>
 * @date 2023/4/17 14:14
 */
@Data
@TableName("prison_daily_life")
public class PrisonDailyLifeEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * $column.comments
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 模板id。prison_daily_life_template表
     */
    private String templateId;
    /**
     * 名称
     */
    private String jobName;
    /**
     * 周期 仅用于页面展示 0自定义、1每天
     */
    private Integer cycle;
    /**
     * 自定义周期天数，星期几到星期几，1,2,3,4,5,6,7
     */
    private String cycleArgs;
    /**
     * 是否启用。1启用
     */
    private Integer enableFlag;
    /**
     * 创建数据的用户id
     */
    private String createUserId;
    /**
     * 创建数据的用户名称
     */
    private String createUserName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新数据的用户id
     */
    private String updateUserId;
    /**
     * 更新数据的用户名称
     */
    private String updateUserName;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 删除标识。0正常、1删除
     */
    @TableLogic
    private Integer delFlag;
}
