package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * prisoner_out_in_message 实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date: 2023-08-15
 */
@Data
@TableName("prisoner_out_in_message")
public class PrisonerOutInMessageEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 带入带出
     */
    @TableId(type = IdType.UUID)
    private String id;
    /**
     * 在押人员编号
     */
    private String prisonerId;
    /**
     * 1-带出 2-带入
     */
    private Integer type;
    /**
     * 消息内容
     */
    private String content;
    /**
     * 1-已处理
     */
    private Integer status;
    /**
     * 1-提讯 2-家属会见 3-律师会见
     */
    private Integer busnessType;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    private Date createTime;
    /**
     * 创建账号
     */
    private String createUser;
    /**
     * 监所编号
     */
    private String prisonId;
}
