package com.gosun.zhjg.basic.business.modules.file.controller;

import com.gosun.zhjg.basic.business.modules.file.vo.BaseFileVo;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.common.util.FileUrlUtils;
import com.gosun.zhjg.prison.room.terminal.modules.app.entity.CnpLogEntity;
import com.gosun.zhjg.prison.room.terminal.modules.app.entity.ScreenCandidEntity;
import com.gosun.zhjg.prison.room.terminal.modules.app.feign.CnpLogFeign;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

/**
 * 仓内屏照片管理
 *
 * <AUTHOR>
 */
@Api(value = "仓内屏照片管理", tags = "仓内屏照片管理")
@RestController
@Slf4j
@RequestMapping("/photo")
public class ScreenPhotoController {


    @Autowired
    CnpLogFeign cnpLogFeign;

    @Autowired
    private FileStorageService fileStorageService;

    @Value("${file-url-prefix}")
    private String pathPrefix;

    // 上传抓拍图片
    @PostMapping("/candidUpload")
    public R<?> candidUpload(@RequestBody MultipartFile file, @RequestHeader Map<String, String> requestHeader) {
        R<?> r = candidUpload(file, requestHeader, false);
        return r;
    }

    // 上传人脸录入图片
    @PostMapping("/enrollUpload")
    public R<?> enrollUpload(@RequestBody MultipartFile file, @RequestHeader Map<String, String> requestHeader) {
        R<?> r = candidUpload(file, requestHeader, true);
        return r;
    }

    /**
     * 上传并记录
     *
     * @param file
     * @param faceFlag true?人脸拍照图片：抓拍图片
     * @return
     * @throws IOException
     */
    public R<?> candidUpload(MultipartFile file, Map<String, String> requestHeader, boolean faceFlag) {
        String httpPath = null;
        Exception exception = null;
        try {
            httpPath = uploadFile(file);
        } catch (Exception e) {
            exception = e;
            log.error(faceFlag ? "人脸图片FTP上传异常" : "抓拍图片FTP上传异常", e);
        }
        // /sdcard/lonbon/eiface/img/police-88809e056ce04d94855e0c459c765535/image_0.png
        String filename = file.getOriginalFilename();
        String rybh = requestHeader.get("rybh");
        if (rybh == null) {
            // TODO, 文件路径中截取
        }
        if (StringUtils.isBlank(httpPath)) {
            CnpLogEntity entity = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.ERROR, "face", faceFlag ? "人脸" : "抓拍" + "图片FTP上传异常", rybh, (exception == null ? null : exception.getMessage()));
            entity.setSerialNum(requestHeader.get("serial-num"));
            cnpLogFeign.log(entity);
            return R.ResponseError();
        }
        ScreenCandidEntity entity = new ScreenCandidEntity();
        try {
            entity.setFaceFlag(faceFlag ? 1 : 2);
            entity.setRybh(rybh);
            entity.setFilePath(httpPath);
            entity.setFileName(filename);
            entity.setSerialNum(requestHeader.get("serial-num"));
            entity.setOrigin("camera"); // 为了让 cnpLogFeign.screenPhoto(entity); 接口 区分出 调用来自这里
            cnpLogFeign.screenPhoto(entity);
            if (faceFlag) {
                CnpLogEntity entity2 = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.INFO, "face", "人脸补录成功", rybh, null, httpPath);
                entity2.setSerialNum(requestHeader.get("serial-num"));
                cnpLogFeign.log(entity2);
            }
        } catch (Exception e) {
            String msg = faceFlag ? "人脸补录调用异常" : "人脸抓拍回写异常";
            log.error(msg, e);
        }
//        if (StringUtils.isNotBlank(httpPath)) {
//            entity.setFilePath(FileUrlUtils.concatUrl(httpPath, pathPrefix));
//        }
        return R.ResponseResult(entity);
    }

    public String uploadFile(MultipartFile file) {
        FileInfo fileInfo = fileStorageService.of(file)
                .setHashCalculatorMd5()
                .setSaveFilename(file.getOriginalFilename()) //设置保存的文件名，不需要可以不写，会随机生成
                .upload();  //将文件上传到对应地方
        return fileInfo.getUrl();
    }

}
