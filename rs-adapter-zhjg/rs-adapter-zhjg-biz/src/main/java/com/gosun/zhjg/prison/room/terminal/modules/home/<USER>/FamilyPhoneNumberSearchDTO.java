package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.gosun.zhjg.common.entity.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * family_phone_number 实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date: 2024-06-06
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value = "FamilyPhoneNumberSearchDTO对象", description = "仓内屏-亲情电话表")
public class FamilyPhoneNumberSearchDTO extends AbstractPageQueryForm implements Serializable {

    private static final long serialVersionUID = 2115152175987108886L;

    @JsonProperty("prisonerId")
    @ApiModelProperty(value = "在押人员编号")
    private String prisonerId;

    @JsonProperty("prisonerName")
    @ApiModelProperty(value = "在押人员姓名")
    private String prisonerName;

    @ApiModelProperty("监室编号")
    private String roomId;

    @ApiModelProperty("监室名字")
    private String roomName;

    @JsonProperty("applyStartTime")
    @ApiModelProperty(value = "申请时间 开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyStartTime;

    @JsonProperty("applyEndTime")
    @ApiModelProperty(value = "申请时间 结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyEndTime;

    @JsonProperty("applyStatus")
    @ApiModelProperty(value = "1-待审核 2-审核通过 3-审核驳回,多个逗号隔开")
    private String applyStatus;

    @NotBlank(message = "监所编号 prisonId 不能为空")
    @ApiModelProperty("监所编号")
    private String prisonId;

    @ApiModelProperty("监视号 roomIds 字符串拼接，多个逗号分隔")
    private String roomIds;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("入所时间-起始 yyyy-MM-dd HH:mm:ss")
    private Date entryStartTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("入所时间-结束 yyyy-MM-dd HH:mm:ss")
    private Date entryEndTime;

}