package com.gosun.zhjg.prison.room.terminal.modules.home.vo;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
* RotatingStaffManageVO 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2024-10-28
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="RotatingStaffManageVO对象", description="轮值员安排管理")
public class RotatingStaffManageVO implements Serializable {


    @JsonProperty("id")
    @ApiModelProperty(value = "主键")
    private String id;

    @JsonProperty("roomId")
    @ApiModelProperty(value = "监室号")
    private String roomId;

    @JsonProperty("roomName")
    @ApiModelProperty(value = "监室号 中文")
    private String roomName;

    @JsonProperty("operatePerson")
    @ApiModelProperty(value = "操作人")
    private String operatePerson;

    @ApiModelProperty("轮值日期 多个逗号隔开")
    private String staffDateStr;

    @ApiModelProperty("轮值员 多个逗号隔开")
    private String staffPrisonerStr;

    @JsonProperty("operateTime")
    @ApiModelProperty(value = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateTime;

    @JsonProperty("constructionReason")
    @ApiModelProperty(value = "布建理由")
    private String constructionReason;

    @JsonProperty("taskDesc")
    @ApiModelProperty(value = "任务描述")
    private String taskDesc;

    @JsonProperty("createTime")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonProperty("createUserId")
    @ApiModelProperty(value = "创建用户编号")
    private String createUserId;

    @JsonProperty("createUserName")
    @ApiModelProperty(value = "创建用户")
    private String createUserName;

    @JsonProperty("updateTime")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @JsonProperty("updateUserId")
    @ApiModelProperty(value = "更新用户编号")
    private String updateUserId;

    @JsonProperty("updateUserName")
    @ApiModelProperty(value = "更新人")
    private String updateUserName;

    @JsonProperty("status")
    @ApiModelProperty(value = "0-待审核 1-审批通过 2-审批不通过")
    private Integer status;

    @JsonProperty("statusDisplayName")
    @ApiModelProperty(value = "审批状态-中文")
    private String statusDisplayName;

    @JsonProperty("groupOpinion")
    @ApiModelProperty(value = "科组长审核意见")
    private Integer groupOpinion;

    @JsonProperty("groupPerson")
    @ApiModelProperty(value = "审核人")
    private String groupPerson;

    @JsonProperty("groupTime")
    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date groupTime;

    @JsonProperty("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @JsonProperty("delFlag")
    @ApiModelProperty(value = "删除标志 1-删除")
    private Integer delFlag;

}
