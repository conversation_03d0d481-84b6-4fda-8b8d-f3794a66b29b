package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
* rotating_staff_manage 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2024-10-28
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="RotatingStaffManageUpdateDTO对象", description="编辑")
public class RotatingStaffManageUpdateDTO implements Serializable {

    @JsonProperty("id")
    @ApiModelProperty(value = "主键")
    private String id;

    @JsonProperty("constructionReason")
    @ApiModelProperty(value = "布建理由")
    private String constructionReason;

    @JsonProperty("taskDesc")
    @ApiModelProperty(value = "任务描述")
    private String taskDesc;

}

