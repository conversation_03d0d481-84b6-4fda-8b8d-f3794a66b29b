package com.gosun.zhjg.prison.room.terminal.modules.socket.service;

import com.google.common.collect.Lists;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dao.CnpSocketDao;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.SocketRelationMapVO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.TerminalDeviceInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class TerminalOnlineStateJob {

    @Autowired
    private CnpSocketDao cnpSocketDao;

    @Autowired
    private SocketService socketService;

    private final String CNP_DEVICE_TYPE_ID = "0008";

    private final String CWP_DEVICE_TYPE_ID = "0015";

    //访客终端
    private final String FKZD_DEVICE_TYPE_ID = "0020";

    //会见终端
    private final String HJZD_DEVICE_TYPE_ID = "0021";

    private final String ONLINE_STATE = "001";

    private final String OFFLINE_STATE = "002";

    @Autowired
    private TerminalrefReshAppService terminalrefReshAppService;


    //    @Scheduled(cron = "0 0 0/2 * * ?")
    public void job() {
        log.info(this.getClass().getSimpleName().concat(" job run!"));
        syncTerminalOnlineState(CNP_DEVICE_TYPE_ID);
        syncTerminalOnlineState(CWP_DEVICE_TYPE_ID);
        syncTerminalOnlineState(FKZD_DEVICE_TYPE_ID);
        syncTerminalOnlineState(HJZD_DEVICE_TYPE_ID);
    }

    public void syncTerminalOnlineState(String deviceTypeId) {
        SocketRelationMapVO socketInfo = socketService.getSocketInfo();
        Collection<String> onlineSerialNumber;
        if (deviceTypeId.equals(FKZD_DEVICE_TYPE_ID) || deviceTypeId.equals(HJZD_DEVICE_TYPE_ID)) {
            onlineSerialNumber = socketInfo.getMacAddressMap().values();
        } else {
            onlineSerialNumber = socketInfo.getSerialNumberMap().values();
        }
        List<String> changeOnlineDeviceIdList = new ArrayList<>();
        List<String> changeofflineDeviceIdList = new ArrayList<>();
        List<TerminalDeviceInfoVO> terminalDeviceList = cnpSocketDao.getTerminalDeviceInfo(deviceTypeId);
        for (TerminalDeviceInfoVO info : terminalDeviceList) {
            if (StringUtils.isBlank(info.getSocketMark())) {
                continue;
            }
            boolean onLine = onlineSerialNumber.contains(info.getSocketMark());
            if (onLine && ONLINE_STATE.equals(info.getDeviceStatus())) {
                // 设备在线，数据库状态也在线，忽略
                continue;
            }
            if (onLine && !ONLINE_STATE.equals(info.getDeviceStatus())) {
                // 设备在线，数据库状态不是在线，则修改为在线
                changeOnlineDeviceIdList.add(info.getDeviceId());
                //设备页面刷新
                terminalrefReshAppService.refreshApp(deviceTypeId,info.getSocketMark());
                cnpSocketDao.setDeviceOnlineTime(info.getDeviceId(),new Date());
                continue;
            }
            if (!onLine && !ONLINE_STATE.equals(info.getDeviceStatus())) {
                // 设备离线，数据库状态不是在线，忽略
                continue;
            }
            if (!onLine && ONLINE_STATE.equals(info.getDeviceStatus())) {
                // 设备离线，数据库状态在线，则修改数据库为离线
                changeofflineDeviceIdList.add(info.getDeviceId());
                cnpSocketDao.setDeviceOfflineTime(info.getDeviceId());
                continue;
            }
        }
        Lists.partition(changeOnlineDeviceIdList, 20).forEach(lt -> {
            cnpSocketDao.updateDeviceOnlineState(ONLINE_STATE, lt);
        });
        Lists.partition(changeofflineDeviceIdList, 20).forEach(lt -> {
            cnpSocketDao.updateDeviceOnlineState(OFFLINE_STATE, lt);
        });
    }


}
