package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.RtcCallHistoryPageDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.RtcCallHistoryEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.RtcCallHistoryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * rtc对讲记录表
 *
 * <AUTHOR>
 * @date 2023/8/10 18:44
 */
@Mapper
public interface RtcCallHistoryDao extends BaseMapper<RtcCallHistoryEntity> {

    List<RtcCallHistoryVO> findByPage(@Param("form") RtcCallHistoryPageDto form, Page page);
}
