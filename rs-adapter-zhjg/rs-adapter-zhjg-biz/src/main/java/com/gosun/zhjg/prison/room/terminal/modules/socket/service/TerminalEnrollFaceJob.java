package com.gosun.zhjg.prison.room.terminal.modules.socket.service;

import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.prison.room.terminal.modules.app.entity.CnpLogEntity;
import com.gosun.zhjg.prison.room.terminal.modules.app.service.CnpLogService;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dao.CnpSocketDao;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.DeviceInitializeDto;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.DeviceInscreenVO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.SocketRelationMapVO;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 仓内外屏定时去检查各设备人脸录入情况。对遗漏的进行补录
 *
 * <AUTHOR>
 * @date 2022/11/24 9:09
 */
@Slf4j
@Component
public class TerminalEnrollFaceJob {
    @Autowired
    private CnpService cnpService;
    @Autowired
    private CnpLogService cnpLogService;

    @Autowired
    private CnpSocketDao socketDao;

    @Autowired
    private SocketService socketService;

    public static final AtomicBoolean isRunning = new AtomicBoolean(false);

    //    @Scheduled(cron = "${terminal-enroll-face-job.cron:0 0 3 * * ?}")

    /**
     * 1. 保证该方法只能同时被一个线程调用，多余的线程直接结束
     * 2. 该方法巡检过程中。 TerminalEnrollFaceCheckHandler 任务停止执行。他们目的一致
     */
    public void job() {
        if (isRunning.compareAndSet(false, true)) {
            // 当前没有线程正在执行该方法，将 isRunning 设置为 true，表示当前线程正在执行该方法
            try {
                doSomething();
            } finally {
                // 方法执行完毕，将 isRunning 设置为 false，表示该方法可以被其他线程执行
                isRunning.set(false);
            }
        } else {
            // 当前有线程正在执行该方法，多余的线程退出
            try {
                XxlJobHelper.log("终端人脸补录任务巡检已在执行中，退出本次线程");
                log.warn("终端人脸补录任务巡检已在执行中，退出本次线程");
            } catch (Exception e) {
            }
            return;
        }
    }

    private void doSomething() {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("TerminalEnrollFaceJob-job run! uuid={}", uuid);
        List<DeviceInscreenVO> allDeviceInscreen = socketDao.getAllDeviceInscreen();
        List<String> offlineInfoList = new ArrayList<>();
        List<String> allSerialNumber = allDeviceInscreen.stream().map(DeviceInscreenVO::getSerialNumber).distinct().collect(Collectors.toList());
        int index = 0;
        int size = allSerialNumber.size();
        SocketRelationMapVO socketInfo = socketService.getSocketInfo();
        for (String serialNumber : allSerialNumber) {
            ++index;
            XxlJobHelper.log("");
            XxlJobHelper.log("当前巡检进度 {}/{} - {}", index, size, serialNumber);
            log.info("{}-当前巡检进度 {}/{} - {}", uuid, index, size, serialNumber);
            if (StringUtils.isBlank(serialNumber)) {
                continue;
            }
            // 该设备上关联的所有客户端
            List<Map.Entry<String, String>> deviceClient = socketInfo.getSerialNumberMap().entrySet()
                    .stream()
                    .filter(f -> Objects.equals(serialNumber, f.getValue()))
                    .collect(Collectors.toList());
            if (deviceClient.isEmpty()) {
                // 记录离线的设备
                offlineInfoList.add(serialNumber);
                XxlJobHelper.log("{}-设备离线", serialNumber);
                continue;
            }
            for (Map.Entry<String, String> client : deviceClient) {
                String clientId = client.getKey();
                // 连接终端. CWP 仓外屏，CNP 仓内屏、 WEB
                String terminal = socketInfo.getTerminalMap().get(clientId);
                if (StringUtils.isBlank(terminal)) {
                    XxlJobHelper.log("{}-获取终端类型异常", serialNumber);
                    offlineInfoList.add(serialNumber);
                    continue;
                }
                if (!SocketActionConstants.PushMessageTerminalEnum.CNP.name().equals(terminal) && !SocketActionConstants.PushMessageTerminalEnum.CWP.name().equals(terminal)) {
                    continue;
                }
                // 记录日志
                CnpLogEntity entity = CnpLogEntity.newCnpLogEntity(CnpLogEntity.CnpLogErrLevelEnum.INFO, "TerminalEnrollFaceJob", "终端人脸补录任务执行", clientId);
                entity.setSerialNum(serialNumber);
                cnpLogService.log(entity);
                // RUN
                DeviceInitializeDto dto = new DeviceInitializeDto();
                dto.setTerminal(terminal);
                dto.setSerialNumber(serialNumber);
                cnpService.enrollFaceSync(dto);
            }
        }
        XxlJobHelper.log("");
        // 打印离线设备
        for (DeviceInscreenVO deviceInfo : allDeviceInscreen) {
            if (offlineInfoList.contains(deviceInfo.getSerialNumber())) {
                XxlJobHelper.log("离线设备-{}-{}-{}-{}", deviceInfo.getSerialNumber(), deviceInfo.getDeviceIp(), deviceInfo.getRoomId(), deviceInfo.getRoomName());
                log.warn("巡检离线设备-{}-{}-{}-{}-{}", uuid, deviceInfo.getSerialNumber(), deviceInfo.getDeviceIp(), deviceInfo.getRoomId(), deviceInfo.getRoomName());
            }
        }
        XxlJobHelper.log("终端人脸补录任务巡检完成-{}，离线设备数量：{}/{}", uuid, offlineInfoList.size(), allDeviceInscreen.size());
        log.warn("终端人脸补录任务巡检完成-{}，离线设备数量：{}/{}", uuid, offlineInfoList.size(), allDeviceInscreen.size());
    }
}
