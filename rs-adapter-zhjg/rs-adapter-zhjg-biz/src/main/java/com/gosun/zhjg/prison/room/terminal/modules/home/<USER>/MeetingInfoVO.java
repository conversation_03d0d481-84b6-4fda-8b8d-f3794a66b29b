package com.gosun.zhjg.prison.room.terminal.modules.home.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="律师会见/家属会见信息", description="仓外平台-信息查询-内部管理信息")
public class MeetingInfoVO implements Serializable {

    @JsonProperty("kssj")
    @ApiModelProperty(value = "会见开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date kssj;

    @JsonProperty("content")
    @ApiModelProperty(value = "会见信息")
    private String meetingInfo;

}
