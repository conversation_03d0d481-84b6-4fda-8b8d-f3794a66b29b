package com.gosun.zhjg.prison.room.terminal.modules.home.vo;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
* LetterSendVO 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2024-10-11
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="LetterSendVO对象", description="信件管理-寄信申请")
public class LetterSendVO implements Serializable {


    @JsonProperty("id")
    @ApiModelProperty(value = "主键")
    private String id;

    @JsonProperty("prisonerId")
    @ApiModelProperty(value = "人员编号")
    private String prisonerId;

    @JsonProperty("prisonerName")
    @ApiModelProperty(value = "人员姓名")
    private String prisonerName;

    @JsonProperty("roomId")
    @ApiModelProperty(value = "监室号")
    private String roomId;

    @JsonProperty("roomName")
    @ApiModelProperty(value = "监室名字")
    private String roomName;

    @JsonProperty("applyTime")
    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime;

    @JsonProperty("prisonId")
    @ApiModelProperty(value = "监所编号")
    private String prisonId;


    @JsonProperty("policeCheck")
    @ApiModelProperty(value = "管教-审核状态 1-通过 2-另行处理")
    private String policeCheck;

    @JsonProperty("policeCheckDisplayName")
    @ApiModelProperty(value = "管教-审核状态 1-通过 2-另行处理")
    private String policeCheckDisplayName;

    @JsonProperty("groupCheckDisplayName")
    @ApiModelProperty(value = "科组长-审核状态 1-通过 2-不通过")
    private String groupCheckDisplayName;

    @JsonProperty("leaderCheckDisplayName")
    @ApiModelProperty(value = "所领导-审核状态 1-通过 2-不通过")
    private String leaderCheckDisplayName;

    @JsonProperty("policeCheckUser")
    @ApiModelProperty(value = "管教-审核人")
    private String policeCheckUser;

    @JsonProperty("policeCheckTime")
    @ApiModelProperty(value = "管教-审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date policeCheckTime;

    @JsonProperty("policeCheckOpinion")
    @ApiModelProperty(value = "管教-管教意见")
    private String policeCheckOpinion;

    @JsonProperty("disposeSituation")
    @ApiModelProperty(value = "另行处理登记-处置情况")
    private String disposeSituation;

    @JsonProperty("disposeUser")
    @ApiModelProperty(value = "另行处理登记-处理人")
    private String disposeUser;

    @JsonProperty("disposeTime")
    @ApiModelProperty(value = "另行处理登记-处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date disposeTime;

    @JsonProperty("groupCheck")
    @ApiModelProperty(value = "科组长-审核状态 1-通过 2-不通过")
    private String groupCheck;

    @JsonProperty("groupCheckUser")
    @ApiModelProperty(value = "科组长-审核人")
    private String groupCheckUser;

    @JsonProperty("groupCheckTime")
    @ApiModelProperty(value = "科组长-审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date groupCheckTime;

    @JsonProperty("groupCheckOpinion")
    @ApiModelProperty(value = "科组长意见")
    private String groupCheckOpinion;

    @JsonProperty("leaderCheck")
    @ApiModelProperty(value = "所领导-审核状态 1-通过 2-不通过")
    private String leaderCheck;

    @JsonProperty("leaderCheckUser")
    @ApiModelProperty(value = "所领导-审核人")
    private String leaderCheckUser;

    @JsonProperty("leaderCheckTime")
    @ApiModelProperty(value = "所领导-审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date leaderCheckTime;

    @JsonProperty("leaderCheckOpinion")
    @ApiModelProperty(value = "所领导意见")
    private String leaderCheckOpinion;

    @JsonProperty("receivePerson")
    @ApiModelProperty(value = "收信人")
    private String receivePerson;

    @JsonProperty("relation")
    @ApiModelProperty(value = "关系")
    private String relation;

    @JsonProperty("mailNo")
    @ApiModelProperty(value = "信件邮编")
    private String mailNo;

    @JsonProperty("receivePrison")
    @ApiModelProperty(value = "收信单位")
    private String receivePrison;

    @JsonProperty("receiveAddress")
    @ApiModelProperty(value = "收信地址")
    private String receiveAddress;

    @JsonProperty("sendPerson")
    @ApiModelProperty(value = "寄出人")
    private String sendPerson;

    @JsonProperty("sendTime")
    @ApiModelProperty(value = "寄出时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTime;

    @JsonProperty("sendRemark")
    @ApiModelProperty(value = "寄出备注")
    private String sendRemark;

    @JsonProperty("delFlag")
    @ApiModelProperty(value = "删除标志 1-删除")
    private Integer delFlag;

    @JsonProperty("createUserId")
    @ApiModelProperty(value = "创建用户编号")
    private String createUserId;

    @JsonProperty("createUser")
    @ApiModelProperty(value = "创建用户")
    private String createUser;

    @JsonProperty("createTime")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonProperty("updateUserId")
    @ApiModelProperty(value = "更新用户编号")
    private String updateUserId;

    @JsonProperty("updateUser")
    @ApiModelProperty(value = "更新用户")
    private String updateUser;

    @JsonProperty("updateTime")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @JsonProperty("status")
    @ApiModelProperty(value = "信件状态 1-另行处理 2-待寄出 3-已寄出")
    private Integer status;

    @JsonProperty("checkStatus")
    @ApiModelProperty(value = "审核流程状态 1-管教待审核 2-管教审核通过 3-科组长待审核 4-科组长审核通过 5-所领导待审核 6-所领导审核通过 7-所领导审核不通过")
    private Integer checkStatus;

    @JsonProperty("relationDisplayName")
    @ApiModelProperty(value = "关系 中文")
    private String relationDisplayName;

    @JsonProperty("statusDisplayName")
    @ApiModelProperty(value = "信件状态 中文")
    private String statusDisplayName;

    @JsonProperty("checkStatusDisplayName")
    @ApiModelProperty(value = "审核流程状态中文")
    private String checkStatusDisplayName;

}
