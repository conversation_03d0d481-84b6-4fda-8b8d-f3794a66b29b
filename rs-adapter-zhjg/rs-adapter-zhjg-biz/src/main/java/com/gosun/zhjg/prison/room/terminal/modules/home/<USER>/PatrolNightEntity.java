package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("patrol_night")
public class PatrolNightEntity {
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    private String patrolmanId;

    private String patrolmanName;

    private String prisonId;

    private String roomId;

    private Date patrolTime;

    private String status;

    private String details;

    private Date updateTime;

    private String updateId;

    private String updateName;

    /**
     * 数据来源
     */
    private String dataSource;

}
