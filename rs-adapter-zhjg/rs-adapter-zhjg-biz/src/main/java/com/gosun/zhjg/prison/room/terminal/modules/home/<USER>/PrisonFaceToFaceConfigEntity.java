package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 面对面管理-仓内屏配置表
 * <AUTHOR>
 * @date 2025/3/6
 */
@Data
@TableName("prison_face_to_face_config")
@ApiModel(value = "面对面管理-仓内屏配置表", description = "面对面管理-仓内屏配置表")
public class PrisonFaceToFaceConfigEntity {

    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "id", required = true)
    private String id;

    @ApiModelProperty(value = "业务登记信息")
    private String content;

    @ApiModelProperty(value = "检查内容")
    private String checkItems;

    @ApiModelProperty(value = "状态(0-禁用,1-启用)")
    private Integer status;

    @ApiModelProperty(value = "登记时间")
    private String registerTime;

    @ApiModelProperty(value = "登记人用户ID")
    private String registerUserId;

    @ApiModelProperty(value = "登记人用户姓名")
    private String registerName;

    @ApiModelProperty(value = "监所ID，默认-1")
    private String prisonId;

    @ApiModelProperty(value = "监室ID")
    private String roomId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建用户ID")
    private String createUserId;

    @ApiModelProperty(value = "创建用户姓名")
    private String createUserName;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "更新用户ID")
    private String updateUserId;

    @ApiModelProperty(value = "更新用户姓名")
    private String updateUserName;
}
