package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * family_phone_number 实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date: 2024-06-06
 */
@Data
@TableName("family_phone_number")
public class FamilyPhoneNumberEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @TableId(type = IdType.UUID)
    private String id;
    /**
     * 与通话人员关系
     */
    private String relName;

    private String relCode;
    /**
     * 在押人员编号
     */
    private String prisonerId;
    /**
     * 电话号码
     */
    private String telNum;

    /**
     * 通话对象
     */
    private String telObject;

    /**
     * 电话时间（通话时间）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date telTime;

    /**
     * 申请时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime;
    /**
     * 0-未通过 1-通过
     */
    private Integer status;
    /**
     * 1-待审核 2-审核通过 3-审核驳回
     */
    private Integer applyStatus;
    /**
     * 审核人userId
     */
    private String approveUser;
    /**
     * 审核人姓名
     */
    private String approveName;
    /**
     * 审核时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approveTime;
    /**
     * 审核理由
     */
    private String reason;
    /**
     * 删除标志 0 -删除
     */
    private Integer delFlag;
    /**
     * 监所编号
     */
    private String prisonId;

    /**
     * 数据来源
     * {@link com.gosun.zhjg.prison.room.terminal.common.enums.DataSourceEnum}
     */
    private Integer dataSource;

    //办理人
    private String handlerName;

    //办理时间
    private Date handlerTime;
}
