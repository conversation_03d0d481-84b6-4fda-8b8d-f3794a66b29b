package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * 仓内屏服药管理台账表(MedicineCnp)实体类
 *
 * <AUTHOR>
 * @since 2023-11-03 17:40:09
 */
@TableName(value = "medicine_cnp")
@Data
public class MedicineCnpEntity implements Serializable {
    private static final long serialVersionUID = -23906149238156390L;
    /**
    * 主键id
    */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
    * 监室号
    */
    private String roomId;
    /**
    * 人员编号
    */
    private String prisonerId;
    /**
    * 人员姓名
    */
    private String prisonerName;
    /**
    * 服药日期
    */
    private Date medicationTime;
    /**
    * 拿药类型
    */
    private String getMedicineType;
    /**
    * 监所代码
    */
    private String prisonId;
    /**
    * 服药视频地址
    */
    private String url;
    /**
    * 创建时间
    */
    private Date createTime;


    private String signUrl;

}
