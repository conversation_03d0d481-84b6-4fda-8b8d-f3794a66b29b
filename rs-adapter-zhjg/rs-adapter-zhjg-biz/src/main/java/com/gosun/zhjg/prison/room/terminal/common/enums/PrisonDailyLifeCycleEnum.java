package com.gosun.zhjg.prison.room.terminal.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * 一日生活制度周期枚举
 *
 * <AUTHOR>
 * @date 2024/9/19
 */
@Getter
public enum PrisonDailyLifeCycleEnum {
    /**
     * 一日生活制度的周期枚举
     */
    EVERY_DAY(0, "每天"),
    CUSTOM(1, "自定义"),
    HOLIDAY(2, "节假日"),
    ;

    private final Integer code;
    private final String name;

    PrisonDailyLifeCycleEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Optional<PrisonDailyLifeCycleEnum> findMatch(Integer code) {
        return Arrays.stream(PrisonDailyLifeCycleEnum.values())
                .filter(e -> Objects.equals(e.getCode(), code))
                .findFirst();
    }
}
