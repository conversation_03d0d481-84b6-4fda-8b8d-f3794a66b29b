package com.gosun.zhjg.prison.room.terminal.modules.inscreen.controller;

import com.alibaba.fastjson.JSONObject;
import com.gosun.zhjg.common.config.UserContext;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.common.msg.ResultVO;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.TerminalService;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.TerminalGetConfigInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Api(tags = "终端内置服务")
@RequestMapping("inscreen/terminalservice")
@RestController
public class TerminalServiceController {

	@Autowired
	private TerminalService terminalService;

	@RequestMapping(value = "/getConfigInfo", method = RequestMethod.GET)
	@ApiOperation(value = "获取配置信息异常")
	public R<?> getConfigInfo(String ip) {
		if (StringUtils.isBlank(ip)) {
			return R.ResponseError("ip不能为空！");
		}
		return R.ResponseResult(terminalService.getConfigInfo(ip));
	}

	@RequestMapping(value = "/updateApp", method = RequestMethod.POST)
	@ApiOperation(value = "升级")
	public R<?> updateApp(String ip, int packageType, String updateVer, String downLoadUrl) {
		terminalService.updateApp(ip, packageType, updateVer, downLoadUrl);
		return R.ResponseOk();
	}

	@RequestMapping(value = "/systemManager/user/delete", method = RequestMethod.POST)
	@ApiOperation(value = "删除人脸")
	public R<?> userDelete(String ip, String id) {
		terminalService.userDelete(ip, id);
		return R.ResponseOk();
	}

	@RequestMapping(value = "/reStartApp", method = RequestMethod.GET)
	@ApiOperation(value = "重启")
	public R<?> reStartApp(String ip) {
		if (StringUtils.isBlank(ip)) {
			return R.ResponseError("ip不能为空！");
		}
		terminalService.reStartApp(ip);
		return R.ResponseOk();
	}

	@GetMapping("syncSerialAndMac")
	@ApiOperation(value = "同步序列号和mac地址", notes = "prisonId不传就从token中获取")
	public ResultVO syncSerialAndMac(String deviceId, String prisonId) {
		try {
			if (StringUtils.isBlank(prisonId)) {
				prisonId = UserContext.getJwtInfo().getPrisonId();
			}
			terminalService.syncSerialAndMac(deviceId, prisonId);
			return ResultVO.success("同步成功！");
		} catch (Exception e) {
			return ResultVO.error("同步失败！", e.getMessage());
		}
	}

	@PostMapping("saveConfigInfo")
	@ApiOperation(value = "保存配置",notes = "json中需加ip")
	public R saveConfigInfo(@RequestBody JSONObject object){
		try {
			terminalService.saveConfigInfo(object);
			return R.ResponseOk();
		}catch (Exception e){
			log.error("保存配置失败！", e);
			return R.ResponseError(e.getMessage());
		}
	}
}
