package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import lombok.Data;

/**
 * prison_online_disease 实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date: 2024-04-12
 */
@Data
@TableName("prison_online_disease")
public class PrisonOnlineDiseaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    private String id;
    /**
     * 监所编号
     */
    private String prisonId;
    /**
     * 在押人员编号
     */
    private String prisonerId;
    /**
     * 监室号
     */
    private String roomId;
    /**
     * 预约时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;
    /**
     * 疾病类型id 多个逗号隔开
     */
    private String diseaseTypeId;
    /**
     * 疾病症状id，多个逗号隔开
     */
    private String diseaseSymptomId;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建人姓名
     */
    private String createUserName;
}
