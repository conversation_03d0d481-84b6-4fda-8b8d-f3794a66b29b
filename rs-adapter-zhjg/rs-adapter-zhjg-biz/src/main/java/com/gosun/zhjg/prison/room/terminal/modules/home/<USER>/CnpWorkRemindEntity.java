package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import java.util.Date;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
/**
 * 仓内屏-待办提醒(CnpWorkRemind)实体类
 *
 * <AUTHOR>
 * @since 2024-04-08 10:11:49
 */
@Data
@TableName("cnp_work_remind")
public class CnpWorkRemindEntity implements Serializable {
    private static final long serialVersionUID = -95547782970394639L;
    /**
     * id
     */     
    private String id;
    /**
     * 业务类型:1:服药确认
     */     
    private String businessType;
    /**
     * 业务关联id
     */     
    private String businessId;
    /**
     * 监室id
     */     
    private String roomId;
    /**
     * 人员编号
     */     
    private String prisonerId;
    /**
     * 处理状态：0未处理，1：已处理
     */     
    private Integer status;
    /**
     * 提醒内容
     */     
    private String content;
    /**
     * 扩展字段
     */     
    private String extend;
    /**
     * 监所编号
     */     
    private String prisonId;
    /**
     * 创建时间
     */     
    private Date createTime;
    /**
     * 创建人id
     */     
    private String createUserId;
    /**
     * 创建人
     */     
    private String createUser;
    /**
     * 更新时间
     */     
    private Date updateTime;
    /**
     * 更新人id
     */     
    private String updateUserId;
    /**
     * 更新人
     */     
    private String updateUser;


}

