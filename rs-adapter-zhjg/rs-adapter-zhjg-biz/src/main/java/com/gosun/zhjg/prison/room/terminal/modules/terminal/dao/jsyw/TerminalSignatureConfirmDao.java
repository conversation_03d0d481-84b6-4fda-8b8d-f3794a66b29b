package com.gosun.zhjg.prison.room.terminal.modules.terminal.dao.jsyw;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.entity.TerminalSignatureConfirmEntity;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalSignatureConfirmVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 终端签名确认
 *
 * <AUTHOR>
 * @date 2024/4/12 11:20
 */
@Mapper
public interface TerminalSignatureConfirmDao extends BaseMapper<TerminalSignatureConfirmEntity> {

	List<TerminalSignatureConfirmVO> findTerminalSignatureTask(String businessType, String linkId);
}
