package com.gosun.zhjg.prison.room.terminal.config;//package com.gosun.security.auth.client.config;

import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.gosun.zhjg.auth.common.util.jwt.IJWTInfo;
import com.gosun.zhjg.auth.common.util.jwt.JWTInfo;
import com.gosun.zhjg.common.config.UserContext;
import com.gosun.zhjg.common.util.Log4jUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: UserTokenInterceptor
 * @Author: ren
 * @Description:
 * @CreateTIme: 2019/6/8 0008 下午 3:41
 **/
@Component
public class UserTokenInterceptor implements HandlerInterceptor {



    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        UserContext.remove();
        String token = request.getParameter("access_token");
        if (token != null) {
            SessionUser sessionUser = SessionUserUtil.getSessionUser();
            JWTInfo ijwtInfo = new JWTInfo();
            ijwtInfo.setUserid(sessionUser.getLoginId());
            ijwtInfo.setIdcard(sessionUser.getIdCard());
            ijwtInfo.setName(sessionUser.getName());
            ijwtInfo.setPrisonId(sessionUser.getOrgCode());
            ijwtInfo.setPrisonName(sessionUser.getOrgName());
            UserContext.putJwtInfo(token, ijwtInfo);
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        UserContext.remove();
        Log4jUtils.getInstance(getClass()).debug("清除数据...........");
    }
}
