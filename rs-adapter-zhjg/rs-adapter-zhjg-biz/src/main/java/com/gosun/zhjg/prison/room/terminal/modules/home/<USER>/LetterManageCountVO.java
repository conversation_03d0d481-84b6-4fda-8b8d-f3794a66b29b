package com.gosun.zhjg.prison.room.terminal.modules.home.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("统计")
public class LetterManageCountVO {
    @ApiModelProperty("管教待审核")
    private int disciplineNum;

    @ApiModelProperty("科/组长待审核")
    private int groupNum;

    @ApiModelProperty("所领导待审核")
    private int leaderNum;

    @ApiModelProperty("待转交/待寄出")
    private int sendNum;
}
