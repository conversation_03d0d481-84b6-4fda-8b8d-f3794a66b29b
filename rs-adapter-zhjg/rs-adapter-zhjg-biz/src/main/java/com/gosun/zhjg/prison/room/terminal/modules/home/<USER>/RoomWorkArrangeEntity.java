package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 监室事务安排
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-13 09:50:07
 */
@Data
@TableName("room_work_arrange")
public class RoomWorkArrangeEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @TableId(type = IdType.ASSIGN_UUID)

    private String id;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 终止时间
     */
    private String endTime;
    /**
     * 事务
     */
    private String workContent;
}
