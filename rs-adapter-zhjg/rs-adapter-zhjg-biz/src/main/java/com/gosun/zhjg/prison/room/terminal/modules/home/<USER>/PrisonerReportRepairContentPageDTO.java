package com.gosun.zhjg.prison.room.terminal.modules.home.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: lih<PERSON>feng
 * @date: 2023/8/28 9:51
 */
@Data
public class PrisonerReportRepairContentPageDTO extends PageQueryDTO {

    @ApiModelProperty(value = "监所编码", hidden = true)
    private String prisonId;

    @ApiModelProperty(value = "状态", hidden = true)
    private Integer status;

    @ApiModelProperty(value = "报修内容名称")
    private String contentName;

}
