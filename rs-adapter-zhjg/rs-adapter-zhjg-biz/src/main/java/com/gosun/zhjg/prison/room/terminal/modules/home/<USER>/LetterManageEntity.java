package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import lombok.Data;

/**
 * letter_manage 实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date: 2024-09-27
 */
@Data
@TableName("letter_manage")
public class LetterManageEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    private String id;
    /**
     * 人员编号
     */
    private String prisonerId;
    /**
     * 送信人姓名
     */
    private String sendMailUser;
    /**
     * 关系
     */
    private String relation;
    /***
     *来信单位
     */
    private String sendPrison;
    /**
     * 来信地址
     */
    private String sendAddress;
    /**
     * 来信日期
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date sendDate;
    /**
     * 登记时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date registerTime;
    /**
     * 监所编号
     */
    private String prisonId;
    /**
     * 签名图片
     */
    private String signUrl;
    /**
     * 管教-审核状态 1-通过 2-另行处理
     */
    private String policeCheck;
    /**
     * 管教-审核人
     */
    private String policeCheckUser;
    /**
     * 管教-审核时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date policeCheckTime;
    /**
     * 管教-管教意见
     */
    private String policeCheckOpinion;
    /**
     * 另行处理登记-处置情况
     */
    private String disposeSituation;
    /**
     * 另行处理登记-处理人
     */
    private String disposeUser;
    /**
     * 另行处理登记-处理时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date disposeTime;
    /**
     * 科组长-审核状态 1-通过 2-不通过
     */
    private String groupCheck;
    /**
     * 科组长-审核人
     */
    private String groupCheckUser;
    /**
     * 科组长-审核时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date groupCheckTime;
    /**
     * 科组长意见
     */
    private String groupCheckOpinion;
    /**
     * 所领导-审核状态 1-通过 2-不通过
     */
    private String leaderCheck;
    /**
     * 所领导-审核人
     */
    private String leaderCheckUser;
    /**
     * 所领导-审核时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date leaderCheckTime;
    /**
     * 所领导意见
     */
    private String leaderCheckOpinion;
    /**
     * 签收时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date receiveTime;
    /**
     * 信件状态 1-另行处理 2-待转交 3-待确认 4-已确认
     */
    private Integer status;
    /**
     * 转交人
     */
    private String passUser;
    /**
     * 转交时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date passTime;
    /**
     * 转交备注
     */
    private String passRemark;
    /**
     * 删除标志 1-删除
     */
    private Integer delFlag;
    /**
     * 创建用户编号
     */
    private String createUserId;
    /**
     * 创建用户
     */
    private String createUser;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;
    /**
     * 更新用户编号
     */
    private String updateUserId;
    /**
     * 更新用户
     */
    private String updateUser;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;
    /**
     * 信件邮编
     */
    private String mailNo;
    /**
     * 登记人
     */
    private String registerUser;
    /**
     * 审核流程状态 1-管教待审核 2-科组长待审核 3-科组长审核通过 4-科组长审核不通过 5-所领导待审核 6-所领导审核通过 7-所领导审核不通过
     */
    private Integer checkStatus;

    /***
     * 0-未读 1-已读
     */
    private Integer readStatus;
}
