package com.gosun.zhjg.prison.room.terminal.modules.home.vo;

import lombok.Data;

@Data
public class TerminalHomeCountVO {
    public TerminalHomeCountVO() {

    }

    public TerminalHomeCountVO(String mark, Number value) {
        this.mark = mark;
        if (value != null) {
            this.value = value.longValue();
        }
    }

    public TerminalHomeCountVO(String mark, Number value, String name) {
        this.mark = mark;
        if (value != null) {
            this.value = value.longValue();
        }
        this.name = name;
    }

    private String mark;
    private Long value;

    private String name;
}

