package com.gosun.zhjg.prison.room.terminal.modules.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gosun.zhjg.common.enums.CwpPrisonerOutInReasonCodeEnum;
import com.gosun.zhjg.common.enums.CwpPrisonerOutInStatusStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 带入带出
 *
 * <AUTHOR>
 * @date 2025/3/21 10:17
 */
@Data
@TableName("cwp_prisoner_out_in_status")
public class CwpPrisonerOutInStatusEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * $column.comments
	 */
	@TableId(type = IdType.ASSIGN_UUID)
	private String id;
	/**
	 * 在押人员编号
	 */
	private String prisonerId;
	/**
	 * 单位代码
	 */
	private String prisonId;
	/**
	 * 人员状态：1待带出，2待带回，3已完成
	 * {@link  CwpPrisonerOutInStatusStatusEnum}
	 */
	private Integer status;
	/**
	 * 事由、业务类型。字典：PRISONER_OUT_IN_REASON
	 * {@link CwpPrisonerOutInReasonCodeEnum}
	 */
	private String reasonCode;
	/**
	 * 具体事由内容；提讯就是取提讯事由；提解就是取提解原因
	 */
	private String reasonContent;
	/**
	 * 关联业务id
	 */
	private String businessId;
	/**
	 * 业务时间；提讯日期、提解时间、会见时间；（律师会见取会见开始时间）
	 */
	private Date businessTime;
	/**
	 * $column.comments
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;
}
