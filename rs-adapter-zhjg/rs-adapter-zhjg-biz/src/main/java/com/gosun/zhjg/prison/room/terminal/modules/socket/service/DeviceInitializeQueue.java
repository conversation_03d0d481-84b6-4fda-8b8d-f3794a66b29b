package com.gosun.zhjg.prison.room.terminal.modules.socket.service;

import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.DeviceInitializeDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;

@Slf4j
@Component
public class DeviceInitializeQueue implements Runnable {

	private static BlockingQueue<DeviceInitializeDto> initBlocQueue = new LinkedBlockingQueue<DeviceInitializeDto>(1024);

	@Autowired
	private CnpService cnpService;

	@PostConstruct
	public void init() {
		try {
			new Thread(this).start();
		} catch (Exception e) {
			log.error("", e);
		}
	}

	public void addQueue(DeviceInitializeDto dto) {
		initBlocQueue.add(dto);
	}

	/**
	 * 队列剩余
	 * 
	 * @return
	 */
	public List<DeviceInitializeDto> queue() {
		List<DeviceInitializeDto> collect = initBlocQueue.stream().collect(Collectors.toList());
		return collect;
	}

	public void cleanQueue() {
		initBlocQueue.clear();
		return;
	}

	@Override
	public void run() {
		while (true) {
			try {
				DeviceInitializeDto dto = initBlocQueue.take();
				cnpService.rebootInitialize(dto);
				Thread.sleep(0L);
			} catch (Exception e) {
				log.error("", e);
			}
		}
	}
}
