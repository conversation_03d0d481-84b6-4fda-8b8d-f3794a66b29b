package com.gosun.zhjg.prison.room.terminal.modules.terminal.service.impl;

import com.google.common.collect.Lists;
import com.gosun.zhjg.common.constant.BaseSystemRuleConfigConstants;
import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.common.enums.DeviceTerminalTypeEnum;
import com.gosun.zhjg.common.enums.PlatformEnum;
import com.gosun.zhjg.common.enums.login.PersonnelTypeEnum;
import com.gosun.zhjg.common.util.TreeLcmUtils;
import com.gosun.zhjg.prison.room.terminal.modules.home.dao.CommonDao;
import com.gosun.zhjg.prison.room.terminal.modules.home.dto.BaseSystemRuleConfigKeyValueDto;
import com.gosun.zhjg.prison.room.terminal.modules.home.service.BaseSystemRuleConfigService;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.SocketService;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dto.TerminalMenuPageDto;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dto.TerminalSettingsCnpDto;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.dto.TerminalSettingsCwpDto;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.service.TerminalMenuService;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.service.TerminalSettingsService;
import com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalMenuVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 终端配置
 *
 * <AUTHOR>
 * @date 2024/12/13 8:56
 */
@Slf4j
@Service
public class TerminalSettingsServiceImpl implements TerminalSettingsService {

	@Autowired
	private BaseSystemRuleConfigService baseSystemRuleConfigService;
	@Autowired
	private TerminalMenuService terminalMenuService;
	@Autowired
	private SocketService socketService;
	@Autowired
	private CommonDao commonDao;

	@Override
	public TerminalSettingsCnpDto getCnpSettingsInfo(String prisonId) {
		TerminalSettingsCnpDto vo = new TerminalSettingsCnpDto();
		vo.setPrisonId(prisonId);
		// 民警菜单
		TerminalMenuPageDto form = new TerminalMenuPageDto();
		form.setPrisonId(prisonId);
		form.setTerminalType(PlatformEnum.CNP.getValue());
		form.setPersonnelType(PersonnelTypeEnum.POLICE.getValue());
		vo.setPoliceMenuTreeList(terminalMenuService.buildTerminalMenuTree(form));
		// 被监管人菜单
		form.setPersonnelType(PersonnelTypeEnum.PRISONER.getValue());
		vo.setPrisonerMenuTreeList(terminalMenuService.buildTerminalMenuTree(form));
		// 系统配置
		//@formatter:off
		List<BaseSystemRuleConfigKeyValueDto> multiItemValue = baseSystemRuleConfigService.getMultiItemValue(prisonId,
				Lists.newArrayList(BaseSystemRuleConfigConstants.TERMINAL_CNP_PAGE_OPERATION_TIME
					, BaseSystemRuleConfigConstants.TERMINAL_CNP_SCREEN_PROTECTION_ENABLE
				));
		//@formatter:on
		vo.setRuleConfigList(multiItemValue);
		return vo;
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void saveCnpSettings(TerminalSettingsCnpDto form, String userid, String name) {
		String prisonId = form.getPrisonId();
		List<String> selectedMenuIdList = new ArrayList<>();
		// 民警菜单
		List<TerminalMenuVO> policeMenuTreeList = form.getPoliceMenuTreeList();
		TreeLcmUtils.forEach(policeMenuTreeList, menu ->
		{
			if (menu.getEnabled() != null && menu.getEnabled()) {
				selectedMenuIdList.add(menu.getId());
			}
		});
		terminalMenuService.saveCnpPoliceMenuPrisonLink(prisonId, selectedMenuIdList, userid, name);
		// 被监管人员菜单
		selectedMenuIdList.clear();
		List<TerminalMenuVO> prisonerMenuTreeList = form.getPrisonerMenuTreeList();
		TreeLcmUtils.forEach(prisonerMenuTreeList, menu ->
		{
			if (menu.getEnabled() != null && menu.getEnabled()) {
				selectedMenuIdList.add(menu.getId());
			}
		});
		terminalMenuService.saveCnpPrisonerMenuPrisonLink(prisonId, selectedMenuIdList, userid, name);
		// 其他 key、value配置。例如：页面操作时限配置
		List<BaseSystemRuleConfigKeyValueDto> ruleConfigList = form.getRuleConfigList();
		if (ruleConfigList != null) {
			for (BaseSystemRuleConfigKeyValueDto config : ruleConfigList) {
				if (config.getValue() != null) {
					baseSystemRuleConfigService.setValue(prisonId, config.getKey(), config.getValue(), userid, name);
				}
			}
		}
		// 推送变动
		this.dataChangeNotify(prisonId, SocketActionConstants.PushMessageTerminalEnum.CNP);
	}

	@Override
	public TerminalSettingsCwpDto getCwpSettingsInfo(String prisonId) {
		TerminalSettingsCwpDto vo = new TerminalSettingsCwpDto();
		vo.setPrisonId(prisonId);
		// 菜单
		TerminalMenuPageDto form = new TerminalMenuPageDto();
		form.setPrisonId(prisonId);
		form.setTerminalType(PlatformEnum.CWP.getValue());
		vo.setMenuTreeList(terminalMenuService.buildTerminalMenuTree(form));
		// 系统配置
		//@formatter:off
		List<BaseSystemRuleConfigKeyValueDto> multiItemValue = baseSystemRuleConfigService.getMultiItemValue(prisonId,
				Lists.newArrayList(BaseSystemRuleConfigConstants.TERMINAL_CWP_PAGE_OPERATION_TIME
						, BaseSystemRuleConfigConstants.TERMINAL_CWP_SHOW_PRISONER_PHOTO_NAME
				));
		//@formatter:on
		vo.setRuleConfigList(multiItemValue);
		return vo;
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void saveCwpSettings(TerminalSettingsCwpDto form, String userid, String name) {
		String prisonId = form.getPrisonId();
		List<String> selectedMenuIdList = new ArrayList<>();
		// 民警菜单
		List<TerminalMenuVO> menuListTreeList = form.getMenuTreeList();
		TreeLcmUtils.forEach(menuListTreeList, menu ->
		{
			if (menu.getEnabled() != null && menu.getEnabled()) {
				selectedMenuIdList.add(menu.getId());
			}
		});
		terminalMenuService.saveCwpMenuPrisonLink(prisonId, selectedMenuIdList, userid, name);
		// 其他 key、value配置。例如：页面操作时限配置
		List<BaseSystemRuleConfigKeyValueDto> ruleConfigList = form.getRuleConfigList();
		if (ruleConfigList != null) {
			for (BaseSystemRuleConfigKeyValueDto config : ruleConfigList) {
				if (config.getValue() != null) {
					baseSystemRuleConfigService.setValue(prisonId, config.getKey(), config.getValue(), userid, name);
				}
			}
		}
		// 推送变动
		this.dataChangeNotify(prisonId, SocketActionConstants.PushMessageTerminalEnum.CWP);
	}

	public void dataChangeNotify(String prisonId, SocketActionConstants.PushMessageTerminalEnum terminalEnum) {
		Integer deviceType = -99;
		if (terminalEnum.equals(SocketActionConstants.PushMessageTerminalEnum.CNP)) {
			deviceType = DeviceTerminalTypeEnum.CNP.getKey();
		} else if (terminalEnum.equals(SocketActionConstants.PushMessageTerminalEnum.CWP)) {
			deviceType = DeviceTerminalTypeEnum.CWP.getKey();
		}
		// 查询该监所下所有仓内屏序列号
		List<String> cnpSerialNumberList = commonDao.getSerialNumberByPrisonId(prisonId, deviceType);
		if (cnpSerialNumberList.isEmpty()) {
			return;
		}
		cnpSerialNumberList = cnpSerialNumberList.stream().distinct().collect(Collectors.toList());
		PushMessageForm form = new PushMessageForm();
		form.setSerialNumbers(cnpSerialNumberList);
		form.setAction(SocketActionConstants.web_TerminalSettingsChange);
		form.setTarget(SocketActionConstants.PushMessageTargetEnum.web.name());
		form.setTerminal(terminalEnum.name());
		new Thread(() ->
		{
			socketService.pushMessageToSerialNumber(form);
		}).start();
	}
}
