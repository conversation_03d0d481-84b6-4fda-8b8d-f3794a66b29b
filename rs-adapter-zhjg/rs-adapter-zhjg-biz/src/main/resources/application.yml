server:
  port: ${conf.server.port.bsp}
  max-http-header-size: 102400

# 日志
logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径

# spring
spring:
  application:
    name: zhjg-prison-room-terminal
  profiles:
    active: conf,dev,sdk,as
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Feign 等会存在重复定义的服务

  # Spring Data Redis 配置
  data:
    redis:
      repositories:
        enabled: false # 项目未使用到 Spring Data Redis 的 Repository，所以直接禁用，保证启动速度



  # Jackson 配置项
  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 LocalDateTime 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 1611460870.401，而是直接 1611460870401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean

  # 放行的路由
  matchers:
    ignores: ${conf.matchers.ignores}

# 接口文档配置
springdoc:
  api-docs:
    enabled: true # 1. 是否开启 Swagger 接文档的元数据
    path: /v3/api-docs
  swagger-ui:
    enabled: true # 2.1 是否开启 Swagger 文档的官方 UI 界面
    path: /swagger-ui
  default-flat-param-object: true # 参见 https://doc.xiaominfo.com/docs/faq/v4/knife4j-parameterobject-flat-param 文档

# MyBatis Plus 的配置项
mybatis-plus:
  mapper-locations:
  - classpath*:mapper/**/*.xml
  configuration:
    map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
  global-config:
    db-config:
      id-type: NONE # “智能”模式，基于 IdTypeEnvironmentPostProcessor + 数据源的类型，自动适配成 AUTO、INPUT 模式。
      #      id-type: AUTO # 自增 ID，适合 MySQL 等直接自增的数据库
      #      id-type: INPUT # 用户输入 ID，适合 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库
      #      id-type: ASSIGN_ID # 分配 ID，默认使用雪花算法。注意，Oracle、PostgreSQL、Kingbase、DB2、H2 数据库时，需要去除实体类上的 @KeySequence 注解
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
    banner: false # 关闭控制台的 Banner 打印
  type-aliases-package: com.rs.dao,com.bsp.dao,com.gosun.zhjg.dao
  encryptor:
    password: XDV71a+xqStEA3WH # 加解密的秘钥，可使用 https://www.imaegoo.com/2020/aes-key-generator/ 网站生成


##########################   bsp相关配置  ##########################
bsp:
  snowflake:
    worker-id: ${conf.snowflake.worker-id}
    datacenter-id: ${conf.snowflake.datacenter-id}
  token:
    url: ${conf.bsp.token.url}
system-mark: ${conf.system-mark}


########################## 医疗系统相关配置 ##########################
rs:
  info:
    version: 1.0.0
    base-package: com.rs.adapter.bsp
  web:
    admin-ui:
      url: http://dashboard.rs # Admin 管理后台
  swagger:
    title: bsp适配器
    description: 提供bsp适配器的所有功能
    version: ${rs.info.version}
    base-package: com.rs.adapter.bsp
debug: ${conf.debug}
---
spring:
  # Spring Cloud Nacos 配置
  cloud:
    nacos:
      discovery: # 【配置中心】配置项
        enabled: ${conf.nacos.enabled}
        server-addr: ${conf.nacos.ip}:${conf.nacos.port} # Nacos 服务器地址
        username: ${conf.nacos.username}
        password: ${conf.nacos.password}
        namespace: ${conf.nacos.namespace}
        group: ${conf.nacos.group}
        metadata:
          version: 1.0.0 # 服务实例的版本号，可用于灰度发布
---
###文件存储配置
dromara:
  x-file-storage: #文件存储配置
    default-platform: minio-1 #默认使用的存储平台
    thumbnail-suffix: ".min.jpg" #缩略图后缀，例如【.min.jpg】【.png】
    #对应平台的配置写在这里，注意缩进要对齐
    minio:
      - platform: minio-1 # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: ${conf.dromara.x-file-storage.access-key}
        secret-key: ${conf.dromara.x-file-storage.secret-key}
        end-point: ${conf.dromara.x-file-storage.end-point}
        bucket-name: ${conf.dromara.x-file-storage.bucket-name}
        domain:  ${conf.dromara.x-file-storage.domain}
        base-path: # 基础路径
easy-trans:
  #启用redis缓存 如果不用redis请设置为false
  is-enable-redis: true
  #启用全局翻译(拦截所有responseBody进行自动翻译)，如果对于性能要求很高可关闭此配置
  is-enable-global: true
  #启用平铺模式
  is-enable-tile: true
---
bean-searcher:
  params:
    sort: bsort
    order: border
    pagination:
      # 起始页，不配置默认为0，这里配置为1，是为了兼容element UI的分页组件
      start: 1
      default-size: 1000
      max-allowed-size: 1000000
  sql:
    dialect: MySQL

#前端的配置，通过zhjg-basic-business/BaseSystemInfoController/info接口返回
frontend:
  properties:
    # 上传方式 oss/ftp
    uploadMethod: oss
    # 密码加密方式 sm2/des
    encryptMethod: sm2
    # 服药视频最小录制时长(单位：秒)
    minTakeMedicineRecordTime: 12
    # 服药视频来源 warehouseScreen/camera
    takeMedicineVideoSource: camera

# Web中间件
server:
  servlet:
    container: tomcat
  # 东方通license路径
  tongweb:
    license:
      path: classpath:license.dat
  # 普元中间件license
  primetonLicense:
##################公共配置结束#################################


########zuul服务相关配置开始#######################################
auth:
  user:
    token-header: token
  client:
    id: zhjg-zuul
    secret: 123456
    token-header: x-client-token

#文件服务器地址，对应nginx文件服务器路径配置
file-url-prefix: http://***************:6060/file/

ftp:
  FTP_URLPATH: http://***************:6060/file/

#kafka配置，在zuul网关中还需单独配置节点信息


#spring.cloud.config.overrideNone: true


#token有限期时间,zuul用到
token:
  expire:
    open: false
    time: 604800

nofiller:
  dirs: download,external
  methods: deviceMonitor/deviceWarning

#国密改造
GlobalVariableConfig:
  #开关
  enable: false
  tenantId: dxe6l4s
  appId: bd8a356fe3bf4e
  secret: 991E6EC23935DD7F54AF20848E47CCF4
  url: https://dc1.huawei.wcsp:32443
  workdir: /home/<USER>
  keyId: "00002953081527169781781"


# 日志读取路径
#logging:
#  path: logs/

#高云人脸配置，当客户端版开启时，服务器端的配置ip，port，basementId，similarity可以随便填
goanalyse-face:
  isopen: true
  #02-23北京演示补充，其他不需要加
  open: true
  client:
    #客户端版本开关和端口
    open: true
    port: 6897
  ip: *************
  port: 7088
  basementId: 2
  #认证校验通过相识度
  similarity: 65

# sm2国密适配
sm2:
  privateKey: cd783848609da63a6393f68dd9d75c91132b8bc1714fd055eaf822068a2485cd

ipSection:
  zd-interval: ************-*************
  yk-interval: ************-***********
  ek-interval: ***********-************
  yj-interval: ***********-************
  ej-interval: ***********-************
  isUse: false

########zuul服务相关配置结束#######################################


########app服务相关配置开始#######################################

## 地图云服务请求地址接口请求地址
## 根据经纬度获取地址集
geo-coder-url: *************/CloudMap/address-services/v1/reversed-geocoder

########app服务相关配置结束#######################################



################auth服务配置开始#######################################
# PKI应用密匙
pki:
  appId: zhxjg
  gatewayURL: http://**********:6180/
  authURL: http://**********:6180/MessageService

jwt:
  #token-header: token
  expire: 86400
  rsa-secret: xx1WET12^%3^(WE45

client:
  rsa-secret: x2318^^(*WRYQWR(QW&T

################auth服务配置结束#######################################

################bussiness服务配置开始#######################################
#视频联网配置 code = 1 已经部署过的 2-新部署的采用
sply:
  ip:  *************
  port: 9998
  serverName: vid-video-deal-service
  rootId: 4404001110100010001
  code: 1
  treeCode: '001001'

video:
  getOtherRealTimeStream: http://${sply.ip}:${sply.port}/${sply.serverName}/realTimeStream/getOtherRealTimeStream
  getFlvRealTimeStream: http://${sply.ip}:${sply.port}/${sply.serverName}/realTimeStream/getFlvRealTimeStream
  recordFileOperRequestTransfer: http://${sply.ip}:${sply.port}/${sply.serverName}/govideoRecordDeal/recordFileOperRequestTransfer
  getSearchRecordRequest: http://*************:${sply.port}/${sply.serverName}/govideoRecordDeal/getSearchRecordRequest
  recordFileUrl: http://**************:11980
  getGoVideoOrGoVlinkRecordRequest: http://${sply.ip}:${sply.port}/${sply.serverName}/govideoRecordDeal/getGoVideoOrGoVlinkRecordRequest
  ptzCtrl: http://${sply.ip}:${sply.port}/${sply.serverName}/realTimeStream/ptzCtrl
  turnToDefaultPreset: http://${sply.ip}:${sply.port}/${sply.serverName}/realTimeStream/turnToDefaultPreset
  presetCtrl: http://${sply.ip}:${sply.port}/${sply.serverName}/realTimeStream/presetCtrl
  savePtzLock: http://${sply.ip}:${sply.port}/${sply.serverName}/cmvsPtzLock/savePtzLock
  ptzCtrl2: http://${sply.ip}:${sply.port}/${sply.serverName}/openSuperior/ptzCtrl
  getRealTimeStreamForWeb: http://${sply.ip}:${sply.port}/${sply.serverName}/realTimeStream/getRealTimeStreamForWeb
  govidoToken: http://${sply.ip}:${sply.port}/${sply.serverName}/uap/checkLogin
  getRealTimeStreamForApp: http://${sply.ip}:${sply.port}/${sply.serverName}/app/getRealTimeStreamForApp
  uapUserName: admin
  uapPassword: admin1
  getChannelTree: http://${sply.ip}:${sply.port}/${sply.serverName}/vidChannel/getChannelTree
  goVideoPtzCtrl: http://${sply.ip}:${sply.port}/${sply.serverName}/realTimeStream/ptzCtrl
  recordMonthRetrievalRequest: http://${sply.ip}:${sply.port}/${sply.serverName}/govideoRecordDeal/recordMonthRetrievalRequest
  recordFileOperRequestTransfer2: http://${sply.ip}:${sply.port}/${sply.serverName}/govideoRecordDeal/recordFileOperRequestTransfer
  queryRecordRequest: http://${sply.ip}:${sply.port}/${sply.serverName}/govideoRecordDeal/getGoVideoOrGoVlinkRecordRequest
  govDevChnnInfoAv: http://${sply.ip}:${sply.port}/${sply.serverName}/govDevChnn/info/av
  getSingleDevChnnRequest: http://${sply.ip}:${sply.port}/govideo-service/GoCloud-Base/GoVideo/GetSingleDevChnnRequest
  govDevChnnInfo: http://${sply.ip}:${sply.port}/${sply.serverName}/govDevChnn/info/basic
  addTreeNodeRequest: http://${sply.ip}:${sply.port}/govideo-service/GoCloud-Base/GoVideo/AddTreeNodeRequest
  clearTreeNodeRequest: http://${sply.ip}:${sply.port}/govideo-service/GoCloud-Base/GoVideo/ClearTreeNodeRequest
  moveTreeNodeLeafRequest: http://${sply.ip}:${sply.port}/govideo-service/GoCloud-Base/GoVideo/MoveTreeNodeLeafRequest
  setDevChnnRequest: http://${sply.ip}:${sply.port}/govideo-service/GoCloud-Base/GoVideo/SetDevChnnRequest
  deleteTreeNodeRequest: http://${sply.ip}:${sply.port}/govideo-service/GoCloud-Base/GoVideo/DeleteTreeNodeRequest
  getPresetByChannelId: http://${sply.ip}:${sply.port}/${sply.serverName}/realTimeStream/getPresetByChannelId
  deletePreset: http://${sply.ip}:${sply.port}/${sply.serverName}/realTimeStream/deletePreset
  getChannelByPage:  http://${sply.ip}:${sply.port}/${sply.serverName}/vidChannel/getChannelByPage
  getChannelIds: http://${sply.ip}:${sply.port}/${sply.serverName}/govDevChnnView/getChannelIds
  getAllDeviceRequest: http://${sply.ip}:${sply.port}/govideo-service/GoCloud-Base/GoVideo/GetAllDeviceRequest

#数据对账查询中间库表开关
dataVerification:
  middleTableEnabled: true

# 对讲文件同步
intercom-ftp:
  FTP_ADDRESS: *************
  FTP_PORT: 21
  FTP_USERNAME: ftp3
  FTP_PASSWORD: A123qweqwe
  FTP_BASEPATH: /intercomFiles/
  FTP_URLPATH: http://*************/file/


################bussiness服务配置开始#######################################

################device服务配置开始#######################################
#视频异动图片保存地址
alarm-move-path: E:\\ftp-file\\test

HK-CONFIG:
  PLATFORM-8700:
    APPKEY: b2a77a33
    SECRET: 123
    OPENAPI-IP-PORT-HTTP: http://127.0.0.1:443
    OPENAPI-IP-PORT-HTTPS: https://127.0.0.1:443
    #    获取默认用户UUID信息接口
    ITF-ADDRESS-GET-DEFAULT-USER_UUID: /openapi/service/base/user/getDefaultUserUuid
    #    获取平台所有子系统UUID信息接口
    ITF-ADDRESS-POST-PLATSUBSYSTEM_UUID: /openapi/service/base/res/getPlatSubsytem
    #    通过子系统UUID获取子系统的事件类型接口
    ITF-ADDRESS-POST-EVENT-TYPES: /openapi/service/eps/getEventTypes
    #    事件订阅接口
    ITF-ADDRESS-POST-SUBSCRIBE-FROM_MQ: /openapi/service/eps/subscribeEventsFromMQEx
    #    根据人员ID集获取人员接口
    ITF-ADDRESS-POST-GET-PERSONINFO-BYIDS: /openapi/service/base/person/getPersonInfosByIds
    #    门禁子系统编码
    DOOR-SUB-SYSTEM-CODE: 3145728
    #    分页获取门禁设备接口
    DOOR-DEVICE-PAGE: /openapi/service/acs/res/getACDevices
    #    分页获取门禁点
    DOOR-INDEX-PAGE: /openapi/service/acs/res/getDoors
    #    根据门禁设备UUID集获取门禁点
    DOOR-INDEX-BY-DEVICE-UUID: /openapi/service/acs/res/getDoorsByDeviceUuids
    #    根据门禁点UUID集获取门禁点状态
    DOOR-STATUS-BY-UUID: /openapi/service/acs/status/getDoorStatusByUuids
    #    同步反控门禁点
    SINGLE-CTRL: /openapi/service/acs/control/synControl
    #    获取门组信息
    GET-DOOR-GROUPS: /openapi/service/acs/group/getDoorGroups
    #    根据门组UUID集获取门禁点
    GET-DOORS-BY-GROUP-UUIDS: /openapi/service/acs/group/getDoorsByGroupUuids
    #    异步反控门禁点
    GROUP_CRTL: /openapi/service/acs/control/asynControl
    #    添加人员
    ADD_USER_INFO: /openapi/service/base/person/addPersonInfo
    #   删除人员
    DEL_USER: /openapi/service/base/person/deletePersonInfos
    #    分页获取人员
    PAGE_PERSONS: /openapi/service/base/person/getPersonInfosEx
    #    人员添加权限
    ADD_AUTHORITIES: /openapi/service/acs/auth/addAuthoritiesByPersonIds
    #    分页获取卡片
    GET_CARD_INFOS: /openapi/service/base/card/getCardInfos
    #    根据人员ID集获取卡片
    GET_CARD_BY_PERSONS: /openapi/service/base/card/getCardInfosByPersonIds
    #    卡片开卡
    OPEN_CARD: /openapi/service/base/card/openCard
    #    卡片退卡
    RETURN_CARD: /openapi/service/base/card/returnCard
    #    根据人员ID集分页获取门禁权限
    GET_AUTHORITIES_BY_PERSONIDS: /openapi/service/acs/auth/getAuthoritiesByPersonIds
    #    预约登记（临时登记）
    TEMPORARY_REGISTER: /openapi/service/rvs/register/temporaryRegister
    #    根据被访人ID集分页获取历史登记信息
    GET_REGISTERS_BY_PERSONIDS: /openapi/service/rvs/record/getRegistersByPersonIds
    #    根据被访人ID集分页获取历史访客
    GET_VISITORS_BY_PERSONIDS: /openapi/service/rvs/record/getVisitorsByPersonIds
    #    被访人集合
    INTERVIEWEE: 123,456,12121
    #分页获取门禁点历史事件
    GET_DOOR_EVENTS_HISTORY: /openapi/service/acs/event/getDoorEventsHistory
    #门禁点UUID集
    DOOR_UUIDS: "da,dd"
    #根据人员ID集删除全部权限
    DEL_AUTHORITIES_BY_PERSONIDS: /openapi/service/acs/auth/delAuthoritiesByPersonIds
    #获取部门信息
    GET_DEPT_INFOS: /openapi/service/base/dept/getDeptInfos
    #部门id
    DEPT_UUID:
    #添加卡片
    ADD_CARDS: /openapi/service/base/dept/getDeptInfos
    #指定人员的门禁权限下载
    downloadAuthorityByPersonIds: /openapi/service/base/dept/getDeptInfos
    #计划模板id
    planUuid:
    #设备uuid集
    deviceUuids:
    #修改人员
    modifyPersonInfo: /openapi/service/base/person/modifyPersonInfo

    #报警主机sdk地址
  INFRARED:
    DDL-PATH: D:\note\6.第三方文档\人脸摄像头对接\CH-HCNetSDKV6.1.6.3_build20200925_Win64\CH-HCNetSDKV6.1.6.3_build20200925_Win64\库文件\
    1k:
      PORT: 8000
      IP: ***********
      USER-NAME: admin
      PASSWORD: admin12345
    2k:
      PORT: 8000
      IP: ************
      USER-NAME: admin
      PASSWORD: admin12345
    1J:
      PORT: 8000
      IP: ************
      USER-NAME: admin
      PASSWORD: admin12345
    PC:
      PORT: 8000
      IP: *************
    2J:
      PORT: 8000
      IP: ************
      USER-NAME: admin
      PASSWORD: admin12345
  FACE-CAMERA:
    DDL-PATH: D:\note\6.第三方文档\人脸摄像头对接\CH-HCNetSDKV6.1.6.3_build20200925_Win64\CH-HCNetSDKV6.1.6.3_build20200925_Win64\库文件\
    USERNAME: admin
    PASSWORD: Admiin12345
    PORT: 8000
  FACE-DOOR:
    DDL-PATH: D:\workSpace\company\file\二看人脸门禁\CH-HCNetSDKV6.1.6.4_build20201231_Win64\CH-HCNetSDKV6.1.6.4_build20201231_win64\库文件\

LONBON:
  laibangServer1: ************** #来邦地址盒地址
  laibangServer2: ************ #来邦地址盒地址
  intercomHost1: 100000 #来帮对讲主机编号
  dll-path: D:\lonbonDLL\

#弘视通信tcp连接
Eutro-Tcp:
  enable: true
  ip: ***********
  port: 8088
  username: admin
  password: 888888
  login-success-reply: <value><struct><member><name>Flag</name><value><boolean>1</boolean></value></member></struct></value>
  heartbeat: <KeepLive><value><struct><member><name>Alive</name><value><i4>1</i4></value></member></struct></value></KeepLive>
  alarm: <Alarm>
  alarm-end: </Alarm>

leader-ship:
  deviceCodes:
    - 4404001110200020032
    - 4404001110200020029

hk:
  artemis:
    host: ************:443
    appKey: 2263
    appSecret: on6VBpUaYYu

hk-zhaf:
  artemis:
    host: ***************:443
    appKey: 226
    appSecret: on6VBpU
    eventRcvPort: http://***************:8667

#巡视管控配置
hk-xsgk:
  #1-海康安防管理平台SJCA V2.0.0版本 2-其它版本如汕尾
  version: 1
  #名单库 id，通过添加自定义名单库返回
  libIb: 6
  #所属目录，获取方法见【获取监区目录】 目录名为其他的indexCode值
  orgCode: cdec5b49ed164f6090c186eee6c3074c

# 海康能力开放平台路径
Hik-OpenApi:
  artemis_path: '/artemis'
  modeling_path: '/api/aibasic/v1/face/imageDetectionAndModeling'
  query_image_path: '/api/aiapplication/v1/face/queryByImageModelWithPage'
  get_lib_path: '/api/aiapplication/v1/face/faceLib/getPersonLib'
  queryIoChannel: /api/nvcac/v1/channel/queryDefenceChannel
  queryDoorChannel: /api/nvcac/v1/channel/queryDoorChannel
  doorEvents: /api/acs/v1/door/events
  queryDefenceChannel: /api/nvcac/v1/channel/queryDefenceChannel
  queryIpisTalkChannel: /api/nvcac/v1/channel/queryIpisTalkChannel
  queryGridChannel: /api/nvcac/v1/channel/queryGridChannel
  Door:
    userName: admin
    passWord: Gykss#1234
    port: 8000
    #海康门禁SDK库文件存放路径，linux系统必须是放在/usr/lib目录下，否在加载不到库文件
    windowsDDLPath: D:\hikDoorSDK\
    linuxDDLPath: /usr/lib/HiKDoorTest/SDK/
  visitor:
    #被访人员id，可在海康平台上新增一个会见终端的人员
    receptionistId: admin
    #访客权限组，访客终端使用大门和会见门禁可叫海康单独建立一个会见组
    privilegeGroupIds: 12313
    #会见终端访客权限组
    meetingGroupIds: 12313

hKDoorEventsHandlerHours: 24
door:
  is-active: 1

# 世邦
shibang:
  enabled: true
  base-url: http://**************
  web-sip-url: **************:5066
  username: ming
  password: Abc123456
  leftKeyPushWarnEnable: false
  callStatePushCnpEnable: false

#心率检测
localsense:
  open: false
  ip: ************
  port: 48300
  username: admin
  password: Aa123456
  tagid: 30001

#智能配电箱
intelligentbox:
  enabled: false
  ip: http://szzgkon.vicp.io:1026
  username: ns001
  password: ns001@123

#视频会见
videomeet:
  enabled: true
  ip: http://szzgkon.vicp.io:1026
  username: admin
  password: 123456

#海康isapiAB门配置
HkIsapi:
  door:
    ips: *************,*************,*************,*************
    userName: admin
    passWord: jxwj1234
    #进门门禁ip
    enterDoorIp: *************
    #出门门禁ip
    outDoorIp: *************
################device服务配置开始#######################################

################reception######################################################

#81系统的数据订阅、财务与回写相关接口
sys-81:
  appid: 4404000000001
  secret: EE0DB68D96F0496388D9518D231D3196
  #81的IP和端口
  base-url: http://*************:8088
  # 数据回写开关
  data-fall-black-on-off: false
  # 数据回写重推开关
  #data-repeat-fall-back-on-off: true
  #数据回写开关，关闭指定业务的数据回写，默认为空，格式参考，修改配置需重启才生效
  #data-fall-black-off-businessName: 律师会见,提讯,家属会见,提解,律师会见预约,家属会见预约
  data-fall-black-off-businessName:
  #登录和心跳接口
  oauth:
    login-url: ${sys-81.base-url}/api/oauth/login
    refresh-url: ${sys-81.base-url}/api/oauth/heartbeat
  #订阅配置实际部署
  data-reception-api:
    increment-url: ${sys-81.base-url}/api/data/wffzryxxxt/subscribe
    unSubscribe-url: ${sys-81.base-url}/api/data/wffzryxxxt/unSubscribe
    subscribeAll-url: ${sys-81.base-url}/api/data/wffzryxxxt/subscribeAll
    callback-url: ${sys-81.base-url}/api/wda/sync/callBack
    getDataCount-url: ${sys-81.base-url}/api/data/wffzryxxxt/getDataCount
    increment-url-writeDb: ${sys-81.base-url}/api/data/writeDatabase/subscribe
    subscribeAll-url-writeDb: ${sys-81.base-url}/api/data/writeDatabase/subscribeAll
    unSubscribe-url-writeDb: ${sys-81.base-url}/api/data/writeDatabase/unSubscribe
    writeDbUrl: ********************************************************************
    writeDbType: 3
    writeUsername: root
    writePassword: root
    arraignDecode: ${sys-81.base-url}/api/jg/txCard/decode
    subscribe-fetchInNeed-url: ${sys-81.base-url}/api/data/wffzryxxxt/subscribe/fetchInNeed
    subscribe-fetchInNeed-url-writeDb: ${sys-81.base-url}/api/data/writeDatabase/subscribe/fetchInNeed
    getSubscribedDataItems: ${sys-81.base-url}/api/data/wffzryxxxt/getSubscribedDataItems
    #在押人员委托关系查询
    lsglKss-query: ${sys-81.base-url}/api/jg/lsgl/kss/query
  #回写接口地址
  fall-back-url:
    #  安全检查
    aqjc-add: ${sys-81.base-url}/api/jg/aqjc/kss/add
    aqjc-update: http://*************:8088/api/jg/aqjc/kss/update
    aqjc-del: ${sys-81.base-url}/api/jg/aqjc/kss/del
    #  面对面管理
    mdmgl-add: ${sys-81.base-url}/api/jg/mdmgl/kss/add
    mdmgl-del: ${sys-81.base-url}/api/jg/mdmgl/kss/del
    #  提讯
    tx-add: ${sys-81.base-url}/api/jg/tx/kss/add
    tx_update: ${sys-81.base-url}/api/jg/tx/kss/update
    tx-del: ${sys-81.base-url}/api/jg/tx/kss/del
    #  律师会见
    lshj-add: ${sys-81.base-url}/api/jg/lshj/kss/add
    lshj-update: ${sys-81.base-url}/api/jg/lshj/kss/update
    lshj-del: ${sys-81.base-url}/api/jg/tx/kss/del
    #  家属会见
    jshj-add: ${sys-81.base-url}/api/jg/jshj/kss/add
    jshj-update: ${sys-81.base-url}/api/jg/jshj/kss/update
    jshj-del: ${sys-81.base-url}/api/jg/jshj/kss/del
    #  提解
    tj-add: ${sys-81.base-url}/api/jg/tj/kss/add
    tj-update: ${sys-81.base-url}/api/jg/tj/kss/update
    tj-del: ${sys-81.base-url}/api/jg/tj/kss/del
    #  械具使用
    xjsy-add: ${sys-81.base-url}/api/jg/xjsy/kss/add
    xjsy-update: ${sys-81.base-url}/api/jg/xjsy/kss/update
    xjsy-del: ${sys-81.base-url}/api/jg/xjsy/kss/del
    #  谈话教育
    thjy-add: ${sys-81.base-url}/api/jg/thjy/kss/add
    #现场需替换179.214的ip
    thjy-update: http://***************:8088/api/jg/thjy/kss/update
    thjy-del: ${sys-81.base-url}/api/jg/thjy/kss/del
    #  集体教育
    jtjy-add: ${sys-81.base-url}/api/jg/jtjy/kss/add
    #现场需替换179.214的ip
    jtjy-update: http://***************:8088/api/jg/jtjy/kss/update
    jtjy-del: ${sys-81.base-url}/api/jg/jtjy/kss/del
    #  耳目管理
    emgl-add: ${sys-81.base-url}/api/jg/emgl/kss/add
    emgl-update: ${sys-81.base-url}/api/jg/emgl/kss/update
    emgl-del: ${sys-81.base-url}/api/jg/emgl/kss/del
    #  耳目反映情况
    emfy-add: ${sys-81.base-url}/api/jg/emfy/kss/add
    emfy-del: ${sys-81.base-url}/api/jg/emfy/kss/del
    #  监室调整
    jstc-add: ${sys-81.base-url}/api/jg/jstz/kss/add
    jstc-del: ${sys-81.base-url}/api/jg/jstz/kss/del
    #  风险评估
    fxpg-add: ${sys-81.base-url}/api/jg/fxpg/kss/add
    #现场需替换IP(***************)成179.214
    fxpg-update: http://***************:8088/api/jg/fxpg/kss/update
    fxpg-del: ${sys-81.base-url}/api/jg/fxpg/kss/del
    #  领导巡视
    ldxs-add: ${sys-81.base-url}/api/jg/ldxs/kss/add
    ldxs-del: ${sys-81.base-url}/api/jg/ldxs/kss/del
    #  违规登记
    wgdj-add: ${sys-81.base-url}/api/jg/wgdj/kss/add
    wgdj-jls-add: ${sys-81.base-url}/api/jg/wgdj/jls/add
    wgdj-jds-add: ${sys-81.base-url}/api/jg/wgdj/jds/add
    wgdj-update: ${sys-81.base-url}/api/jg/wgdj/kss/update
    wgdj-jls-update: ${sys-81.base-url}/api/jg/wgdj/jls/update
    wgdj-jds-update: ${sys-81.base-url}/api/jg/wgdj/jds/update
    wgdj-del: ${sys-81.base-url}/api/jg/wgdj/kss/del
    wgdj-jls-del: ${sys-81.base-url}/api/jg/wgdj/jls/del
    wgdj-jds-del: ${sys-81.base-url}/api/jg/wgdj/jds/del
    #  重(特)病号管理
    zbh-add: ${sys-81.base-url}/api/jg/zbhgl/kss/add
    zbh-del: ${sys-81.base-url}/api/jg/zbhgl/kss/del
    zbh-update: ${sys-81.base-url}/api/jg/zbhgl/kss/update
    #  所内就医
    snjy-add: ${sys-81.base-url}/api/jg/snjy/kss/add
    snjy-update: ${sys-81.base-url}/api/jg/snjy/kss/update
    snjy-del: ${sys-81.base-url}/api/jg/snjy/kss/del
    #律师会见预约
    lsjy-update: ${sys-81.base-url}/api/jg/lshj/kss/lsjy/update
    #家属会见预约
    jsjy-update: ${sys-81.base-url}/api/jg/jshj/kss/jsjy/update
    #药品基本信息
    ypjbxx-add: ${sys-81.base-url}/api/jg/ypjbxx/kss/add
    ypjbxx-update: ${sys-81.base-url}/api/jg/ypjbxx/kss/update
    ypjbxx-del: ${sys-81.base-url}/api/jg/ypjbxx/kss/del
    #律师委托数新增
    lsglKss-add: ${sys-81.base-url}/api/jg/lsgl/kss/add
    #在押人员律师委托撤销
    lsglKss-revoke: ${sys-81.base-url}/api/jg/lsgl/kss/update
    # 送钱送物
    sqsw-add: ${sys-81.base-url}/api/jg/sqsw/kss/add
    sqsw-update: ${sys-81.base-url}/api/jg/sqsw/kss/update
    sqsw-del: ${sys-81.base-url}/api/jg/sqsw/kss/del

  #财务系统接口
  financial-system-api:
    #  用户验证
    access: ${sys-81.base-url}/api/cwapi/gxxcx/zhyz
    #  获取商品
    get-goods-list: ${sys-81.base-url}/api/cwapi/gxxcx/sp
    #  商品详情
    get-goods-info: ${sys-81.base-url}/api/cwapi/gxxcx/spxq
    #  订购记录
    order-record: ${sys-81.base-url}/api/cwapi/gxxcx/dgjl
    #  订购记录详情
    order-record-info: ${sys-81.base-url}/api/cwapi/gxxcx/dgjlxq
    #  家属顾送数据
    family-send-record: ${sys-81.base-url}/api/cwapi/gxxcx/jsgs
    #  获取商品类型
    get-goods-type: ${sys-81.base-url}/api/cwapi/gxxcx/splx
    #  账户余额数据
    account-balances: ${sys-81.base-url}/api/cwapi/gxxcx/zhye
    #  出所结算
    settlement: ${sys-81.base-url}/api/cwapi/gxxcx/csjs
    #  商品预定
    goods-reserve: ${sys-81.base-url}/api/cwapi/gxxcx/spyd
    #  账户变动
    account-change: ${sys-81.base-url}/api/cwapi/gxxcx/zhbd
################reception######################################################

################terminal######################################################
#财务系统默认cid（财务人员id）
default-cid: 123
#财务系统默认单位代码（珠海一看）
#default-unit:
#财务系统查看账户变动时间范围（默认一年）
default-workdate: 365

socketio:
  host: 0.0.0.0
  port: 9182
  # 设置最大每帧处理数据的长度，防止他人利用大数据来攻击服务器
  maxFramePayloadLength: 1048576
  # 设置http交互最大内容长度
  maxHttpContentLength: 1048576
  # socket连接数大小（如只监听一个端口boss线程组为1即可）
  bossCount: 1
  workCount: 100
  allowCustomRequests: true
  # 协议升级超时时间（毫秒），默认10秒。HTTP握手升级为ws协议超时时间
  upgradeTimeout: 1000000
  # Ping消息超时时间（毫秒），默认60秒，这个时间间隔内没有接收到心跳消息就会发送超时事件
  pingTimeout: 6000000
  # Ping消息间隔（毫秒），默认25秒。客户端向服务器发送一条心跳消息间隔
  pingInterval: 25000
rights-obligations: /rights-pdf/告知书.pdf
rights-obligations-tmp-path: D:\pdf-test\
################terminal######################################################

################work######################################################
# 领导巡视配置
Leader-Patrol:
  inCamera: '4404001110200020032'
  outCamera: '4404001110200020029'
  morningIn: '8:30'
  morningOut: '12:00'
  afternoonIn: '12:01'
  afternoonOut: '21:00'
# 不予收押人员报表
zh-reject:
  title: '珠海公安监管场所未收押收治入所人员情况统计表'

# 巡控人员库id
Person-Pool:
  personLibId: 'test'
  personLibLeaderId: 'test1'

#提讯解码演示开关 true为演示环境
arraignDecodeDemonstrate: true

#档案系统后台服务地址-smart-archive微服务url
smart-archive:
  url: http://*************:9675

# 每日配餐报表
zh-catering:
  title: '今日配餐统计表'

# 巡控登记班次模板
cBcdm:
  one: '01'
  two: '02'
  three: '03'
  four: '04'
  oneTime: '06:00-12:00'
  twoTime: '12:00-18:00'
  threeTime: '18:00-23:59:59'
  fourTime: '00:00-06:00'

#防疫核查
epidemic:
  isOpen: false
  checkRounteDateNum: 14
  checkRuleA: '1,4,7,14,21'  #核酸检测规则A
  checkRuleB: '7,14,21'      #核酸检测规则B

# 对接视频云以图搜档
archives:
  ip: **************
  port: 9999
  uapPort: 8906
  taskId: BK257090108778348544
  username: admin
  password: Gxx@6228
################work######################################################

################oss######################################################
#图片下载IP替换，默认关闭(当前南非使用，关闭情况下不影响其他地区)
imageIpReplacement:
  enable: false
  #外网ip
  externalIp: ************
  #内网ip
  intranetIp: ***********

## OSS 配置信息
oss:
  #不支持热更
  server:
    endpoint: http://***************:9000
    accessKey: gosuncn-oss
    secretKey: gosuncn-oss@2021
    access-endpoint: http://***************:6060
    access-prefix: /file
  image:
    size:
      #上传的图片大小限制（单位M）,前端也有大小限制，前端的限制不能大于这里的限制
      limit: 12
  #支持热更
  file:
    type:
      #文件上传支持的格式列表
      list: doc/docx/xls/xlsx/txt/rar/zip/ppt/pptx/jpeg/jpg/png/pdf/bmp/gif/tif/rtf/csv/mp4/mov/avi/flv/wmv/rm/ram/swf/mp3/WAV/FLAC/APE/ALAC/WavPack/tiff/raw/7z/cab/arj/lzh/tar/gz/ace/uue/bzjar/iso/mpq/apk

# en-US表示英文,zh-CN表示简体中文,zh-TW表示繁体中文 (前后端统一使用此配置项)
system:
  language: "en-US"

distributed:
  query:
    limit: 1000

################oss######################################################

################ices 谈话教育子系统######################################################
ices:
  app:
    # 系统名称
    appName: 公安监所智能谈话系统
    # 版权
    copyRight: copyright@高新兴科技集团股份有限公司
    # 登出倒计时，9分钟没操作(540秒），则弹出60秒倒计时
    logoutCountDownBefore: 540
    # 登出倒计时，默认是10分钟=600秒
    logoutCountDown: 60
    # 用于调试出来开发环境,默认是false。
    isVConsole: true
    # 用于app代理获取图片前缀
    file-url-prefix:
  # 录像录像设备账号密码
  video-device:
    username: admin
    password: admin@123

################ices######################################################

################isbs 智慧监管基础服务配置######################################################
isbs:
  third:
    # 认证信息
    secret: u8y7t3o92

################isbs######################################################
##############################################广州卫健委健康信息查询####################################################
health:
  data:
    guangzhou:
      # 请求服务系统地址
      url: http://192.168.77.81:9873
      # request类型接口代理前缀
      request-proxy-path: /request/${health.data.guangzhou.service-id}
      # 授权码
      app-code: WGVqqu
      # 数据加解密密钥
      secret-key: 0123456789abcdef
      # 在请求服务系统配置任务时生成，由请求服务系统提供
      service-id: 10001
      # 门诊诊疗信息-请求头签名
      mzzl-info-sign: F59FD46D8DD3E532F469F6561FCE0FB0
      # 住院诊疗信息-请求头签名
      zyzl-info-sign: B23A98B694984DE65733899D3E9FF358
      # 检验报告-请求头签名
      jybg-info-sign: CC2A465064C53A5C0FEFFA722EE9D21C
      # 检查报告-请求头签名
      jcbg-info-sign: 12233C4A12B845A481626A1AA3C0C4BE
      # 体检项目报告-请求头签名
      tjxmbg-info-sign: D21D62B57091557EEA8CCBDDE9E2814A
# 医疗系统药品出入库是否使用流程
workflow:
  ihcs:
    medicine:
      enabled: true

################rbac 权限系统服务配置######################################################
rbac:
  #超级管理员账号
  super-admin: sysadmin
  #菜单替换ip配置
  menu:
    replaces:
      REPLACE_BASE_IP: *************

################rbac######################################################




################AI 服务配置######################################################
ai:
  url: "http://**************:190"
  apiKey:
    #基础知识库
    base: "app-u8bwK4JiPjuIXeleN0KjszY4"
    #谈话教育库
    talk-edu: "app-NeRTSJAPQTOzG9GRAXa39MJY"
    #聊天助手
    chat: "app-HEamvMjkhFYQiyv91DnNulHA"
################AI 服务配置######################################################


################ 仓内屏屏等终端 服务器信息配置 ######################################################
# 项目部署只需修改，nginxIp和zuulServerIp
terminal:
  #nginxIp
  nginxIp: *************
  #zuul服务IP
  zuulServerIp: *************
################AI 服务配置######################################################

xxl:
  job:
    enabled: ${conf.xxl.enabled}
    admin:
      addresses: ${conf.xxl.admin.addresses}
      username: ${conf.xxl.admin.username}
      password: ${conf.xxl.admin.password}
    executor:
      appName: ${spring.application.name}
      ip: ${conf.xxl.executor.ip}
      logPath: ${conf.xxl.executor.logPath}
    accessToken: default_token
---
spring:
  # RabbitMQ配置
  rabbitmq:
    listener:
      simple:
        acknowledge-mode: manual        # 消费端消息确认 自动确认消息
        max-concurrency: ${conf.rabbitmq.maxConcurrency}  # 消费端最大并发数
        concurrency: ${conf.rabbitmq.concurrency}         # 消费者最小数量
        prefetch: ${conf.rabbitmq.prefetch}               # 单个请求中处理的消息最大个数
---
spring:
  servlet:
    multipart:
      enabled: true
      max-file-size: 1024MB
      max-request-size: 1024MB
