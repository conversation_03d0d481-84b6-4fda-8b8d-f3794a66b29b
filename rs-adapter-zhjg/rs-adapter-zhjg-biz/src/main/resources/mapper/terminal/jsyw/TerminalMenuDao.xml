<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gosun.zhjg.prison.room.terminal.modules.terminal.dao.jsyw.TerminalMenuDao">
    <sql id="all_entity_columns">
        ${prefix}.id,
        ${prefix}
        .
        menu_name,
        ${prefix}
        .
        code,
        ${prefix}
        .
        parent_id,
        ${prefix}
        .
        parent_nodes,
        ${prefix}
        .
        terminal_type,
        ${prefix}
        .
        personnel_type,
        ${prefix}
        .
        sort_order,
        ${prefix}
        .
        add_time,
        ${prefix}
        .
        update_user_id,
        ${prefix}
        .
        update_user_name,
        ${prefix}
        .
        update_time,
    </sql>

    <select id="findByPage" resultType="com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalMenuVO">
        SELECT
        <include refid="all_entity_columns">
            <property name="prefix" value="a"/>
        </include>
        FROM acp_pm_terminal_menu a
        <where>
            and a.is_del = 0
        </where>
    </select>

    <select id="findOneById" resultType="com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalMenuVO">
        SELECT
        <include refid="all_entity_columns">
            <property name="prefix" value="a"/>
        </include>
        FROM acp_pm_terminal_menu a
        where a.id = #{id}
    </select>

    <select id="selectAllMenusAndMarkUnitOwned"
            resultType="com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalMenuVO">
        select
        a.id,
        a.menu_name,
        a.code,
        a.parent_id,
        a.terminal_type,
        a.personnel_type,
        a.sort_order,
        b.id as link_id
        from acp_pm_terminal_menu a
        left join acp_pm_terminal_menu_org b on a.id = b.menu_id and b.org_code = #{form.prisonId} and b.is_del = 0
        where a.is_del = 0
        <if test="form.terminalType != null and form.terminalType != ''">
            and a.terminal_type = #{form.terminalType}
        </if>
        <if test="form.personnelType != null and form.personnelType != ''">
            and a.personnel_type = #{form.personnelType}
        </if>
        order by a.sort_order
    </select>

    <select id="selectPrisonMenuList"
            resultType="com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalMenuVO">
        select
        a.id,
        a.menu_name,
        a.code,
        a.parent_id,
        a.terminal_type,
        a.personnel_type
        from acp_pm_terminal_menu a
        inner join acp_pm_terminal_menu_org b on a.id = b.menu_id
        where a.is_del = 0 and b.is_del = 0
        and b.org_code = #{form.prisonId}
        <if test="form.terminalType != null and form.terminalType != ''">
            and a.terminal_type = #{form.terminalType}
        </if>
        <if test="form.personnelType != null and form.personnelType != ''">
            and a.personnel_type = #{form.personnelType}
        </if>
        order by a.sort_order
    </select>

    <select id="selectPrisonMenuIdList" resultType="java.lang.String">
        select
        b.menu_id
        from acp_pm_terminal_menu a
        inner join acp_pm_terminal_menu_org b on a.id = b.menu_id
        where a.is_del = 0 and b.is_del = 0
        and b.org_code = #{form.prisonId}
        <if test="form.terminalType != null and form.terminalType != ''">
            and a.terminal_type = #{form.terminalType}
        </if>
        <if test="form.personnelType != null and form.personnelType != ''">
            and a.personnel_type = #{form.personnelType}
        </if>
    </select>

    <select id="checkPrisonHasMenuPermission" resultType="java.lang.Integer">
        select count(1)
        from acp_pm_terminal_menu a
                 inner join acp_pm_terminal_menu_org b on a.id = b.menu_id
            and b.org_code = #{prisonId}
            and a.code = #{menuCode}
            and a.is_del = 0
            and b.is_del = 0
    </select>

    <select id="selectPrisonPermissionMenu" resultType="java.lang.String">
        select a.code from acp_pm_terminal_menu a
        inner join acp_pm_terminal_menu_org b on a.id = b.menu_id
        and b.org_code = #{prisonId}
        <if test="terminalType != null and terminalType != ''">
            and a.terminal_type = #{terminalType}
        </if>
        <if test="menuCodeList != null and menuCodeList.size > 0">
            and a.code in
            <foreach collection="menuCodeList" item="menuCode" open="(" separator="," close=")">
                #{menuCode}
            </foreach>
        </if>
        and a.is_del = 0
        and b.is_del = 0
    </select>

    <select id="selectRoomList" resultType="com.gosun.zhjg.basic.business.modules.room.vo.AreaPrisonRoomPageVO">
        select id, room_name, org_code as prison_id
        from acp_pm_acp_pm_area_prison_room
    </select>

    <select id="selectHasMenuPrisonIdList" resultType="java.lang.String">
        select b.org_code
        from acp_pm_terminal_menu a
                 inner join acp_pm_terminal_menu_org b on a.id = b.menu_id
            and a.code = #{menuCode}
            and a.is_del = 0
            and b.is_del = 0
    </select>
</mapper>
