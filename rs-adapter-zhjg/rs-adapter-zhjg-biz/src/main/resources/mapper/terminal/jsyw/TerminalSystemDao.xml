<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gosun.zhjg.prison.room.terminal.modules.terminal.dao.jsyw.TerminalSystemDao">

    <select id="findDeviceCnpCwpList" resultType="com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalSystemDeviceVO">
        select
            b.id as link_id,
            b.device_id,
            b.device_name,
            b.serial_number,
            (case when b.device_type = 1 then '0008' when b.device_type = 2 then '0015' end) as terminal_type,
            apr.org_code as prison_id,
            apr.id as area_id,
            apr.room_name as area_name,
            b.device_ip as device_ip,
            info.apk_version,
            info.web_version,
            info.upgrade_time,
            info.apk_upgrade_time,
            info.web_upgrade_time
        from acp_pm_device_inscreen b
        inner join acp_pm_area_prison_room apr on apr.id = b.room_id
        left join acp_pm_terminal_version_info info on info.serial_number = b.serial_number
        <where>
            apr.status = '1'
            <if test="form.prisonId != null and form.prisonId != ''">
                and apr.org_code = #{form.prisonId}
            </if>
            <if test="form.deviceType != null and form.deviceType != ''">
                and b.device_type = #{form.deviceType}
            </if>
            <if test="form.deviceName != null and form.deviceName != ''">
                and b.device_name like concat('%',#{form.deviceName},'%')
            </if>
            <if test="form.serialNumber != null and form.serialNumber != ''">
                and b.serial_number like concat('%',#{form.serialNumber},'%')
            </if>
            <if test="form.areaName != null and form.areaName != ''">
                and apr.room_name like concat('%',#{form.areaName},'%')
            </if>
            <if test="form.apkVersion != null and form.apkVersion != ''">
                and info.apk_version like concat('%',#{form.apkVersion},'%')
            </if>
            <if test="form.webVersion != null and form.webVersion != ''">
                and info.web_version like concat('%',#{form.webVersion},'%')
            </if>
            <if test="form.upgradeTimeStart != null and form.upgradeTimeEnd != null">
                and (
                    (info.apk_upgrade_time &gt;= #{form.upgradeTimeStart} and info.apk_upgrade_time &lt; #{form.upgradeTimeEnd})
                    or (info.web_upgrade_time &gt;= #{form.upgradeTimeStart} and info.web_upgrade_time &lt; #{form.upgradeTimeEnd})
                )
            </if>
            <if test="form.apkUpgradeTimeStart != null">
                and info.apk_upgrade_time &gt;= #{form.apkUpgradeTimeStart}
            </if>
            <if test="form.apkUpgradeTimeEnd != null">
                and info.apk_upgrade_time &lt; #{form.apkUpgradeTimeEnd}
            </if>
            <if test="form.webUpgradeTimeStart != null">
                and info.web_upgrade_time &gt;= #{form.webUpgradeTimeStart}
            </if>
            <if test="form.webUpgradeTimeEnd != null">
                and info.web_upgrade_time &lt; #{form.webUpgradeTimeEnd}
            </if>
            <if test="form.deviceList != null and form.deviceList.size > 0">
                and b.id in (<foreach collection="form.deviceList" item="did" separator=",">#{did}</foreach>)
            </if>
        </where>
        order by apr.id
    </select>

    <select id="findDeviceTrjjzdList" resultType="com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalSystemDeviceVO">
        select
            a.id as link_id,
            a.id as device_id,
            a.device_name,
            a.mac_address as serial_number,
            a.device_type_id as terminal_type,
            a.org_code as prison_id,
            a.area_id,
            (select ba.area_name from base_area ba where ba.id = a.area_id) as area_name,
            a.ip_address as device_ip,
            info.apk_version,
            info.web_version,
            info.upgrade_time,
            info.apk_upgrade_time,
            info.web_upgrade_time
        from
        acp_pm_device a
            left join acp_pm_terminal_version_info info on info.serial_number = a.mac_address
        where
            a.device_type_id = '0023'
            <if test="form.prisonId != null and form.prisonId != ''">
                and a.org_code = #{form.prisonId}
            </if>
            <if test="form.deviceName != null and form.deviceName != ''">
                and a.device_name like concat('%',#{form.deviceName},'%')
            </if>
            <if test="form.serialNumber != null and form.serialNumber != ''">
                and a.mac_address like concat('%',#{form.serialNumber},'%')
            </if>
            <if test="form.areaName != null and form.areaName != ''">
                and a.area_id in (select ba.id from base_area ba where ba.area_name like concat('%',#{form.areaName},'%'))
            </if>
            <if test="form.apkVersion != null and form.apkVersion != ''">
                and info.apk_version like concat('%',#{form.apkVersion},'%')
            </if>
            <if test="form.webVersion != null and form.webVersion != ''">
                and info.web_version like concat('%',#{form.webVersion},'%')
            </if>
            <if test="form.upgradeTimeStart != null and form.upgradeTimeEnd != null">
                and (
                (info.apk_upgrade_time &gt;= #{form.upgradeTimeStart} and info.apk_upgrade_time &lt; #{form.upgradeTimeEnd})
                or (info.web_upgrade_time &gt;= #{form.upgradeTimeStart} and info.web_upgrade_time &lt; #{form.upgradeTimeEnd})
                )
            </if>
            <if test="form.apkUpgradeTimeStart != null">
                and info.apk_upgrade_time &gt;= #{form.apkUpgradeTimeStart}
            </if>
            <if test="form.apkUpgradeTimeEnd != null">
                and info.apk_upgrade_time &lt; #{form.apkUpgradeTimeEnd}
            </if>
            <if test="form.webUpgradeTimeStart != null">
                and info.web_upgrade_time &gt;= #{form.webUpgradeTimeStart}
            </if>
            <if test="form.webUpgradeTimeEnd != null">
                and info.web_upgrade_time &lt; #{form.webUpgradeTimeEnd}
            </if>
            <if test="form.deviceList != null and form.deviceList.size > 0">
                and a.id in (<foreach collection="form.deviceList" item="did" separator=",">#{did}</foreach>)
            </if>
        order by a.device_name
    </select>

    <select id="findDeviceUnboundList" resultType="com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalSystemDeviceVO">
        select b.id                                                        as acp_pm_device_inscreen_id,
               b.device_id,
               b.device_name,
               b.serial_number,
               info.ip                                                     as device_ip,
               concat('web:', info.web_version, ' apk:', info.apk_version) as version,
               info.upgrade_time
        from acp_pm_device_inscreen b
                 left join acp_pm_terminal_version_info info on info.serial_number = b.serial_number
        where (b.room_id is null or b.room_id = '')
    </select>

    <select id="upgradelogBatchList" resultType="com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalSystemUpgradeLogBatchVO">
        SELECT a.id,
            a.terminal_type,
            a.package_name,
            a.package_type,
            a.upgrade_version,
            a.upgrade_time,
            a.version_management_id,
            a.add_time,
            (concat((select count(1) from acp_pm_terminal_version_upgrade_log t where t.batch_id = a.id and t.ok = 1), '/', (select count(1) from acp_pm_terminal_version_upgrade_log t where t.batch_id = a.id) )) as upgrade_info_display_name
        from acp_pm_terminal_version_upgrade_log_batch a
        <where>
            <if test="form.prisonId != null and form.prisonId != ''">
                and a.org_code = #{form.prisonId}
            </if>
            <if test="form.terminalType != null and form.terminalType != ''">
                and a.terminal_type = #{form.terminalType}
            </if>
            <if test="form.packageType != null and form.packageType != ''">
                and a.package_type = #{form.packageType}
            </if>
            <if test="form.packageName != null and form.packageName != ''">
                and a.package_name like concat('%',#{form.packageName},'%')
            </if>
            <if test="form.upgradeVersion != null and form.upgradeVersion != ''">
                and a.upgrade_version like concat('%',#{form.upgradeVersion},'%')
            </if>
            <if test="form.upgradeTimeStart != null">
                and a.upgrade_time &gt;= #{form.upgradeTimeStart}
            </if>
            <if test="form.upgradeTimeEnd != null">
                and a.upgrade_time &lt; #{form.upgradeTimeEnd}
            </if>
        </where>
        order by a.upgrade_time desc
    </select>

    <select id="upgradelogBatchInfo" resultType="com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalSystemUpgradeLogBatchVO">
        SELECT a.id,
               a.terminal_type,
               a.package_name,
               a.package_type,
               a.upgrade_version,
               a.upgrade_time,
               a.version_management_id,
               a.release_notes,
               a.add_time,
               (concat((select count(1) from acp_pm_terminal_version_upgrade_log t where t.batch_id = a.id and t.ok = 1), '/', (select count(1) from acp_pm_terminal_version_upgrade_log t where t.batch_id = a.id) )) as upgrade_info_display_name
        from acp_pm_terminal_version_upgrade_log_batch a
        where id = #{id}
    </select>

    <select id="upgradelogList" resultType="com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalSystemUpgradeLogVO">
        select
            a.id,
            a.serial_number,
            a.ip,
            a.apk_version,
            a.web_version,
            a.upgrade_type as package_type,
            a.upgrade_version,
            a.ok,
            a.upgrade_time,
            a.add_time,
            a.err_msg,
            a.batch_id,
            a.terminal_type,
            a.device_name,
            a.area_name
        from acp_pm_terminal_version_upgrade_log a
        <where>
            <if test="form.batchId != null and form.batchId != ''">
                and a.batch_id = #{form.batchId}
            </if>
            <if test="form.serialNumber != null and form.serialNumber != ''">
                and a.serial_number like concat('%',#{form.serialNumber},'%')
            </if>
            <if test="form.deviceName != null and form.deviceName != ''">
                and a.device_name like concat('%',#{form.deviceName},'%')
            </if>
            <if test="form.areaName != null and form.areaName != ''">
                and a.area_name like concat('%',#{form.areaName},'%')
            </if>
        </where>
        order by a.upgrade_time
    </select>

    <select id="selectUpgradeLogBatchId" resultType="java.lang.String">
        select a.batch_id
        from acp_pm_terminal_version_upgrade_log a
        <where>
            <if test="form.ip != null and form.ip != ''">
                and a.ip = #{form.ip}
            </if>
            <if test="form.ok != null">
                and a.ok = #{form.ok}
            </if>
            <if test="form.serialNumber != null and form.serialNumber != ''">
                and a.serial_number = #{form.serialNumber}
            </if>
        </where>
        order by add_time desc
        limit 1
    </select>
</mapper>
