<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gosun.zhjg.prison.room.terminal.modules.terminal.dao.jsyw.TerminalSignatureConfirmDao">
    <select id="findTerminalSignatureTask" resultType="com.gosun.zhjg.prison.room.terminal.modules.terminal.vo.TerminalSignatureConfirmVO">
        SELECT id,
               prison_id,
               prisoner_id,
               prisoner_name,
               business_type,
               link_id,
               state,
               confirm_time,
               signature_url,
               signature_reviewed_at,
               add_time
        from terminal_signature_confirm
        where business_type = #{businessType} and link_id = #{linkId}
        order by add_time desc
    </select>
</mapper>
