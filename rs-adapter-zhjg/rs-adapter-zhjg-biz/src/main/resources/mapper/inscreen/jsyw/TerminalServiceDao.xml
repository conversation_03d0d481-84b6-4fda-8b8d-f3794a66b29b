<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.TerminalServiceDao">

    <select id="getDeviceList" resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.TerminalServiceIPVo">
        select a.id,a.device_type_id,a.ip_address,a.mac_address,bdi.serial_number from acp_pm_device a
            left join acp_pm_device_inscreen bdi on a.id=bdi.device_id
             where
              a.ip_address is not null and a.ip_address!=''
              <if test="prisonId!=null and prisonId!=''">
                  and a.prison_id=#{prisonId}
              </if>
              <if test="deviceId!=null and deviceId !=''">
                   and a.id=#{deviceId}
              </if>
              and device_type_id in('0008','0015','0020','0021')
    </select>

    <update id="updateMacAddress">
        update acp_pm_device set mac_address=#{macAddress} where id=#{deviceId}
    </update>

    <update id="updateSerialNumber">
        update acp_pm_device_inscreen set serial_number=#{serialNumber} where device_id=#{deviceId}
    </update>
</mapper>
