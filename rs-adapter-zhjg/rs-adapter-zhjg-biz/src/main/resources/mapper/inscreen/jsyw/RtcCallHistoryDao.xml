<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.RtcCallHistoryDao">

    <sql id="page_entity_columns">
        ${prefix}.id,
        ${prefix}.prison_id,
        ${prefix}.call_type,
        ${prefix}.meeting_state,
        ${prefix}.caller_user_id,
        ${prefix}.caller_serial_number,
        ${prefix}.caller_name,
        ${prefix}.caller_role_id,
        ${prefix}.caller_role_name,
        ${prefix}.receiver_room_id,
        ${prefix}.receiver_serial_number,
        ${prefix}.receiver_name,
        ${prefix}.call_time,
        ${prefix}.call_end_time,
        ${prefix}.call_duration_sec,
        ${prefix}.rtc_room_id,
        ${prefix}.rtc_room_name,
        ${prefix}.rtc_meeting_id,
        ${prefix}.rtc_meeting_name,
        ${prefix}.add_time
    </sql>

    <select id="findByPage" resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.RtcCallHistoryVO">
        SELECT
        <include refid="page_entity_columns">
            <property name="prefix" value="a"/>
        </include>
        FROM
        rtc_call_history a
        <where>
            <if test="form.receiverRoomId != null and form.receiverRoomId != ''">
                and a.receiver_room_id = #{form.receiverRoomId}
            </if>
            <if test="form.prisonId != null and form.prisonId != ''">
                and a.prison_id = #{form.prisonId}
            </if>
            <if test="form.callType != null and form.callType != ''">
                and a.call_type = #{form.callType}
            </if>
            <if test="form.callerUserId != null">
                and a.caller_user_id = #{form.callerUserId}
            </if>
            <if test="form.meetingState != null">
                and a.meeting_state = #{form.meetingState}
            </if>
            <if test="form.rtcMeetingId != null">
                and a.rtc_meeting_id = #{form.rtcMeetingId}
            </if>
            <if test="form.rtcRoomId != null">
                and a.rtc_room_id = #{form.rtcRoomId}
            </if>
            <if test="form.receiverSerialNumber != null and form.receiverSerialNumber != ''">
                and a.receiver_serial_number = #{form.receiverSerialNumber}
            </if>
            <if test="form.callerSerialNumber != null and form.callerSerialNumber != ''">
                and a.caller_serial_number = #{form.receiverSerialNumber}
            </if>
            <if test="form.startTime != null">
                and a.call_time &gt;= #{form.startTime}
            </if>
            <if test="form.endTime != null">
                and a.call_time &lt; #{form.endTime}
            </if>
        </where>
        order by a.add_time desc
    </select>


</mapper>
