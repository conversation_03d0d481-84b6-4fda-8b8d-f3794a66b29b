<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.BaseDeviceInscreenDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.BaseDeviceInscreenEntity"
               id="baseDeviceInscreenMap">
        <result property="id" column="id"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="deviceId" column="device_id"/>
        <result property="deviceIp" column="device_ip"/>
        <result property="roomId" column="room_id"/>
        <result property="deviceNum" column="device_num"/>
        <result property="hostNum" column="host_num"/>
        <result property="addressIp" column="address_ip"/>
    </resultMap>

    <sql id="table_name">acp_pm_device_inscreen</sql>

    <sql id="all_entity_columns">
     ${prefix}.id,
	 ${prefix}.serial_number,
	 ${prefix}.device_id,
	 ${prefix}.device_ip,
	 ${prefix}.room_id,
	 ${prefix}.device_num,
	 ${prefix}.host_num,
	 ${prefix}.device_type,
	 ${prefix}.address_ip,
	 ${prefix}.device_name,
	 ${prefix}.add_time,
	 ${prefix}.create_user_id,
	 ${prefix}.update_time,
	 ${prefix}.update_user_id
	 </sql>

    <sql id="page_entity_columns">
     ${prefix}.id,
	 ${prefix}.serial_number,
	 ${prefix}.device_id,
	 ${prefix}.device_ip,
	 ${prefix}.room_id,
	 ${prefix}.device_num,
	 ${prefix}.host_num,
	 ${prefix}.device_type,
	 ${prefix}.address_ip,
	 ${prefix}.device_name,
	 ${prefix}.add_time,
	 ${prefix}.create_user_id,
	 ${prefix}.update_time,
	 ${prefix}.update_user_id
	 </sql>

    <select id="findByPage" resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.BaseDeviceInscreenPageVO">
        SELECT
        <include refid="page_entity_columns">
            <property name="prefix" value="a"/>
        </include>
        ,b.room_name
        FROM
        <include refid="table_name"/>
        a
        left join acp_pm_area_prison_room b on a.room_id = b.id
        <where>
            <if test="form.serialNumber != null and form.serialNumber != ''">
                and a.serial_number = #{form.serialNumber}
            </if>
            <if test="form.deviceId != null and form.deviceId != ''">
                and a.device_id = #{form.deviceId}
            </if>
            <if test="form.roomId != null and form.roomId != ''">
                and a.room_id = #{form.roomId}
            </if>
            <if test="form.deviceIp != null and form.deviceIp != ''">
                and a.device_ip = #{form.deviceIp}
            </if>
            <if test="form.id != null and form.id != ''">
                and a.id = #{form.id}
            </if>
            <if test="form.deviceType != null">
                and a.device_type = #{form.deviceType}
            </if>
            <if test="form.excludeEmptyRoom != null and form.excludeEmptyRoom">
                and b.id is not null
            </if>
        </where>

    </select>

    <select id="findOneById" resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.BaseDeviceInscreenVO">
        SELECT
        <include refid="all_entity_columns">
            <property name="prefix" value="a"/>
        </include>
        FROM
        <include refid="table_name"/>
        a
        where id = #{id}
    </select>

    <!--
    <select id="getBySerialNum" resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.BaseDeviceInscreenVO">
        SELECT
        <include refid="all_entity_columns">
            <property name="prefix" value="a"/>
        </include>
        ,b.room_name
        FROM
        <include refid="table_name"/>
        a
        left join acp_pm_area_prison_room b on a.room_id = b.id
        where a.serial_number = #{serialNum} limit 1
    </select>
    -->

    <select id="getByRoom" resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.BaseDeviceInscreenVO">
        SELECT
        <include refid="all_entity_columns">
            <property name="prefix" value="a"/>
        </include>
        ,b.room_name
        FROM
        <include refid="table_name"/>
        a
        left join acp_pm_area_prison_room b on a.room_id = b.id
        where a.room_id = #{roomId} limit 1
    </select>

    <select id="getByDeviceNum" resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.BaseDeviceInscreenVO">
        SELECT
        <include refid="all_entity_columns">
            <property name="prefix" value="a"/>
        </include>
        ,b.room_name,b.org_code as prison_id
        FROM
        <include refid="table_name"/>
        a
        left join acp_pm_area_prison_room b on a.room_id = b.id
        where a.device_num = #{deviceNum} limit 1
    </select>

    <select id="getRoomDeviceList" resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.BaseDeviceTerminalVo">
        select t3.id as roomId,t3.room_name as roomName,t1.internalName,t1.warehouseName,t1.internalId,t1.warehouseId,t2.update_time,t2.update_user_name
        from acp_pm_area_prison_room t3
        left join (select distinct(a.room_id),a.room_id as roomId,
        case
        when a.device_type = 1 then a.device_name
        else b.device_name
        end as internalName,
        case
        when a.device_type = 1 then a.device_id
        else b.device_id
        end as internalId,
        case
        when a.device_type = 2 then a.device_name
        else b.device_name
        end as warehouseName,
        case
        when a.device_type = 2 then a.device_id
        else b.device_id
        end as warehouseId
        from
        acp_pm_device_inscreen a
        left join acp_pm_device_inscreen b on
        a.room_id = b.room_id
        and a.id != b.id) t1 on t3.id =t1.roomId
        left join
             (select t5.room_id, t5.update_time, t5.update_user_name
              from acp_pm_device_inscreen_update_log t5
                       inner join (select room_id, max(update_time) as update_time
                                   from acp_pm_device_inscreen_update_log
                                   where update_time is not null
                                   group by room_id) t6 on t5.room_id = t6.room_id and t5.update_time = t6.update_time) t2
             on t3.id = t2.room_id
       <where>
           t3.status='1'
           <if test="form.prisonId!=null and form.prisonId!=''">
               and t3.org_code=#{form.prisonId}
           </if>
           <if test="form.roomId!=null and form.roomId!=''">
               and t3.id=#{form.roomId}
           </if>
           <if test="form.roomName!=null and form.roomName!=''">
               and t3.room_name like concat('%',#{form.roomName},'%')
           </if>
           <if test="form.internalName!=null and form.internalName!=''">
               and t1.internalName like concat('%',#{form.internalName},'%')
           </if>
           <if test="form.warehouseName!=null and form.warehouseName!=''">
               and t1.warehouseName like concat('%',#{form.warehouseName},'%')
           </if>
       </where>
       order by t3.id
    </select>

    <select id="getDeviceInscreenByRoom" resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.BaseDeviceTerminalInfoVo">
        select a.id as deviceId ,a.device_name,a.device_status as deviceStatus,a.ip_address as deviceIp ,b.serial_number,b.device_num,b.host_num,b.address_ip as addressIp
        ,b.dev_user_name,b.dev_password,d.room_name
        from acp_pm_device a
        left join acp_pm_device_inscreen b on a.id =b.device_id
        left join acp_pm_area_prison_room d on b.room_id =d.id
        where b.room_id=#{roomId}
        <if test="deviceType==1">
            and a.device_type_id = '0008'
        </if>
        <if test="deviceType==2">
            and a.device_type_id = '0015'
        </if>
    </select>

    <select id="getDeviceInscreenBySerialNumber" resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.BaseDeviceTerminalInfoVo">
        select a.id as deviceId ,a.device_name,a.device_status as deviceStatus,a.ip_address as deviceIp ,b.serial_number,b.device_num,b.host_num,b.address_ip as addressIp, b.device_type
        ,b.dev_user_name,b.dev_password
        ,d.room_name
        from acp_pm_device a
        inner join acp_pm_device_inscreen b on a.id =b.device_id
        left join acp_pm_area_prison_room d on b.room_id =d.id
        where b.serial_Number = #{serialNumber}
    </select>

    <select id="getDeviceListByType" resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.BaseDeviceTerminalInfoVo">
        select a.id as deviceId,a.device_name as deviceName,a.device_status as deviceStatus,a.ip_address as deviceIp
        from acp_pm_device a
        where
        a.org_code=#{form.prisonId}
        and a.is_del = 0
        <if test="form.deviceType==1">
            and a.device_type_id = '0008'
        </if>
        <if test="form.deviceType==2">
            and a.device_type_id = '0015'
        </if>
        <if test="form.deviceName!=null and form.deviceName!=''">
            and a.device_name like concat('%',#{form.deviceName},'%')
        </if>
        <if test="form.deviceIp!=null and form.deviceIp!=''">
            and a.ip_address like concat('%',#{form.deviceIp},'%')
        </if>
        order by a.id
    </select>

    <select id="getRoomNameByDeviceId" resultType="String">
      select b.room_name from acp_pm_device_inscreen a
      left join acp_pm_area_prison_room b on a.room_id =b.id
	   where (a.room_id !='' and a.room_id is not null) and a.device_id=#{deviceId}
    </select>

    <update id="updateRoomTerminal">
        update acp_pm_device_inscreen set
            <choose>
                <when test="roomId==null or roomId==''">
                    room_id=null,
                </when>
                <otherwise>
                    room_id=#{roomId},
                </otherwise>
            </choose>
            update_time=now(),update_user_id =#{updateUserId} where device_id=#{deviceId}
    </update>

    <delete id="deleteRoomTerminal">
        DELETE FROM acp_pm_device_inscreen where room_id=#{roomId} and device_type=#{deviceType}
    </delete>

    <insert id="addUpdateLog">
        INSERT INTO acp_pm_device_inscreen_update_log (id, room_id, device_id, update_time, update_user_id, update_user_name) VALUES(#{form.id}, #{form.roomId}, #{form.deviceId},#{form.updateTime}, #{form.updateUserId},  #{form.updateUserName});
    </insert>


    <select id="getRoomTerminal" resultType="String">
        select id from acp_pm_device_inscreen
        <where>
            <if test="deviceId!=null and deviceId!=''">
                and device_id=#{deviceId}
            </if>
            <if test="roomId!=null and roomId !=''">
                and room_id=#{roomId}
            </if>
            <if test="roomId!=null">
                and device_type=#{deviceType}
            </if>
        </where>
    </select>

    <select id="getFjDeviceByAreaId"
            resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.BaseDeviceInscreenVO">
        select a.id, a.serial_number , a.device_id , a.device_num, b.area_id as room_id , (select apr.room_name from acp_pm_area_prison_room apr where apr.id = b.area_id)
        from acp_pm_device_inscreen a
                 inner join acp_pm_device b on a.device_id = b.id
        where b.area_id = #{roomId} and a.device_type = 3
        order by b.area_id
    </select>

    <select id="getCnpDeviceByRoomId"
            resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.BaseDeviceInscreenVO">
        select a.id, a.serial_number , a.device_id , a.device_num, a.room_id , (select apr.room_name from acp_pm_area_prison_room apr where apr.id = a.room_id)
        from acp_pm_device_inscreen a
        where a.room_id = #{roomId} and a.device_type = 1
        order by a.room_id
    </select>

    <select id="getCnpDeviceInfo" resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.CnpDeviceInfoVO">
        select a.id,
               a.serial_number,
               a.device_name,
               b.org_code as prison_id,
               b.id as room_id,
               b.room_name
        from acp_pm_device_inscreen a
        inner join acp_pm_area_prison_room b on a.room_id = b.id
        where a.serial_number = #{serialNum}
            and a.device_type = 1
        order by b.id, a.update_time desc
        limit 1
    </select>

    <select id="getCwpDeviceInfo" resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.CwpDeviceInfoVO">
        select a.id,
               a.serial_number,
               a.device_name,
               b.org_code as prison_id,
               b.room_type,
               b.id as room_id,
               b.room_name
                , b.room_sex
                , b.room_level
                , (select count(1) from vw_acp_pm_prisoner_in bpi where bpi.jsh = b.id) as prisoner_count
        from acp_pm_device_inscreen a
        inner join acp_pm_area_prison_room b on a.room_id = b.id
        where a.serial_number = #{serialNum}
            and a.device_type = 2
        order by b.id, a.update_time desc
    </select>
</mapper>
