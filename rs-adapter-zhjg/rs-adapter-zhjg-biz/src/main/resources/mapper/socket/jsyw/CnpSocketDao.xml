<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gosun.zhjg.prison.room.terminal.modules.socket.dao.CnpSocketDao">
    <select id="getCnpSerialNumberByRoomId" resultType="String">
        select serial_number from acp_pm_device_inscreen where
        room_id in (<foreach collection="roomIds" item="roomId" separator=",">
        #{roomId}
    </foreach>)
        and (device_type is null or device_type = 1)
    </select>

    <select id="getCnpSerialNumberByPrisonId" resultType="String">
        select a.serial_number from acp_pm_device_inscreen a left join acp_pm_area_prison_room b on a.room_id = b.id
        where b.prison_id = #{prisonId}
    </select>

    <select id="getCwpSerialNumberByRoomId" resultType="String">
        select serial_number from acp_pm_device_inscreen where
        room_id in (<foreach collection="roomIds" item="roomId" separator=",">
        #{roomId}
    </foreach>)
        and device_type = 2
    </select>

    <select id="getCnpCwpSerialNumberByRooomId" resultType="String">
        select serial_number from acp_pm_device_inscreen where
        room_id in (<foreach collection="roomIds" item="roomId" separator=",">
        #{roomId}
    </foreach>)
        and (device_type is null or device_type in (1, 2))
    </select>

    <select id="getTerminalDeviceInfo"
            resultType="com.gosun.zhjg.prison.room.terminal.modules.socket.vo.TerminalDeviceInfoVO">
        select a.id as device_id, a.device_status,a.mac_address, b.serial_number,
        case when a.device_type_id='0020' or a.device_type_id='0021' then a.mac_address
        when a.device_type_id='0008' or a.device_type_id='0015' then b.serial_number
        end as socketMark
        from acp_pm_device a
        left join acp_pm_device_inscreen b on a.id = b.device_id
        where (a.is_del is null or a.is_del = 0) and a.device_type_id = #{deviceTypeId}
    </select>

    <update id="updateDeviceOnlineState">
        update base_device set device_status = #{deviceStatus} where id in (<foreach collection="deviceIds" separator="," item="id">#{id}</foreach>)
    </update>

    <select id="getPrisonerPhotoInPhotoTable" resultType="java.lang.String">
        select zp from vw_acp_pm_prisoner_photo where rybh = #{rybh} and zpxh = '0' order by jbsj desc limit 1
    </select>
</mapper>
