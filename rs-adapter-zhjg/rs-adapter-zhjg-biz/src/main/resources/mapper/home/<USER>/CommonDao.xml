<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gosun.zhjg.prison.room.terminal.modules.home.dao.CommonDao">

    <select id="batchQueryPrisonerName" resultType="java.util.HashMap">
        select rybh, xm from vw_acp_pm_prisoner bp where rybh in (<foreach collection="rybhList" separator="," item="rybh">#{rybh}</foreach>)
    </select>







    <select id="getSerialNumberByPrisonId" resultType="String">
        select serial_number
        from acp_pm_device_inscreen
        where room_id in (select apr.id from acp_pm_area_prison_room apr where apr.org_code = #{prisonId})
          and device_type = #{deviceType}
          and serial_number != ''
          and serial_number is not null
    </select>
</mapper>
