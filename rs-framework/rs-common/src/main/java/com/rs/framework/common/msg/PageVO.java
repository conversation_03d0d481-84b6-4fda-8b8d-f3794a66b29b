package com.rs.framework.common.msg;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 分页实体
 * <AUTHOR>
 * @date 2025年5月14日
 * @param <T>
 */
@Getter
@Setter
@ApiModel(description = "返回分页实体")
public class PageVO<T> {
    @ApiModelProperty(value = "总数", required = true)
    private long total;

    @ApiModelProperty(value = "内容", required = true)
    private List<T> rows;

    public static <T> PageVO<T> of(long total, List<T> rows) {
        PageVO<T> page = new PageVO<>();
        page.setTotal(total);
        page.setRows(rows);
        return page;
    }

    public static <T> PageVO<T> empty() {
        return of(0L,null);
    }
}
