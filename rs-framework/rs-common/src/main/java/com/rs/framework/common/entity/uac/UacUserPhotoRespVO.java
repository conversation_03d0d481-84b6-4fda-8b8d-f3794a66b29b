package com.rs.framework.common.entity.uac;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 用户头像 Response VO")
@Data
public class UacUserPhotoRespVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("用户id")
    private String id;
    @ApiModelProperty("头像")
    private String photo;
}
