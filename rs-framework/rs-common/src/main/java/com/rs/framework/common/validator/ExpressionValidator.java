package com.rs.framework.common.validator;

import com.rs.framework.common.annotation.ExpressionValid;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class ExpressionValidator implements ConstraintValidator<ExpressionValid, Object> {
    private String expression;

    @Override
    public void initialize(ExpressionValid constraintAnnotation) {
        this.expression = constraintAnnotation.expression();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName("JavaScript");
        try {
            return Boolean.parseBoolean(engine.eval(expression).toString());
        } catch (Exception e) {
            return false;
        }
    }
}
