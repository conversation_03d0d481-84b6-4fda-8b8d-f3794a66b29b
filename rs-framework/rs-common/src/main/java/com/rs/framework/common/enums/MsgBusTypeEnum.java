package com.rs.framework.common.enums;

import cn.hutool.core.util.StrUtil;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 业务消息代码区间，解决：解决各模块业务消息代码定义，避免重复，在此只声明
 * <p>
 * 一共 6~9 位，分成四段
 * <p>
 * 第一段，3 位，一级业务模块
 * 101 - 管教业务
 * 102 - 窗口业务
 * 103 - 监室事务
 * 104 - 卷宗管理
 * 199 - 其他业务
 * x - 预留
 * 第二段，3 位，二级具体业务
 * 001 - 戒具使用
 * 002 - 风险评估
 * 003 - 单独关押
 * 004 - 社会矛盾化解
 * ... - ...
 * 第三段，3 位，二级具体业务子模块
 * 推荐自增
 * 不限制规则。
 */
public enum MsgBusTypeEnum {

    // ========== 管教业务 ==========
    GJ_JJSY("101_001", "戒具使用"),
    GJ_FXPG("101_002", "风险评估"),
    GJ_DDGY("101_003", "单独关押"),
    GJ_SSHJ("101_004", "社会矛盾化解"),
    GJ_JB("101_005", "禁闭"),
    GJ_MDMDJ("101_006", "面对面登记"),
    GJ_JSTZ("101_007", "监室调整"),
    GJ_TSCSQ("101_008", "特殊餐"),
    GJ_YGGL("101_009", "严管管理"),
    GJ_CFGL("101_010", "惩罚管理"),
    GJ_XXYDJ("101_011", "信息员管理"),
    GJ_GDJS("101_012", "过渡监室"),
    GJ_JLGL("101_014", "奖励管理"),


    // ========== 窗口业务 ==========
    CK_TXX("102_001", "提讯"),
    CK_TX("102_002", "提询"),
    CK_TJ("102_003", "提解"),
    CK_LSHJ("102_004", "律师会见"),
    CK_SGLSHJ("102_005", "使馆领事会见"),
    CK_JSHJ("102_006", "家属会见"),
    CK_JSDXSPHJ("102_007", "家属单向视频会见"),

    // ========== 监室事务 ==========
    JS_CWGL("103_001", "床位管理"),
    JS_RCQJ("103_002", "日常清监"),
    JS_AQDJC("103_003", "安全大检查"),

    // ========== 卷宗管理 ==========
    JZ_JYSH("104_001", "借阅审核"),
    JZ_SJSB("104_002", "事件上报"),
    JZ_SJHT("104_003", "事件退回"),
    JZ_DBM("104_004", "待编目"),

    JY_LDSP("105_001", "待领导审批"),
    LD_LDXS("106_001", "领导巡视"),
    XK_ZBDD("107_001", "值班督导"),

    // ========== 医疗业务 ==========
    YL_LDXS("108_001", "药品顾送"),

    //羁押管理
    JY_ZFGL_LSFX("109_001","留所服刑");


    private String code;
    private String name;

    MsgBusTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }


    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (StrUtil.isBlank(code)) {
            return null;
        }
        if (!MAP_BY_CODE.containsKey(code)) {
            return null;
        }
        return MAP_BY_CODE.get(code).getName();
    }

    private static final Map<String, MsgBusTypeEnum> MAP_BY_CODE = Arrays.stream(values())
            .collect(Collectors.toMap(MsgBusTypeEnum::getCode, Function.identity()));

    public static final List<String> getDamAllBusTypeList() {
        return Arrays.asList(JZ_JYSH.getCode(), JZ_SJSB.getCode(), JZ_SJHT.getCode(), JZ_DBM.getCode());
    }

    public static final List<String> getDamApproveBusTypeList() {
        return Arrays.asList(JZ_JYSH.getCode(), JZ_SJSB.getCode(), JZ_SJHT.getCode());
    }

    public static final List<String> getDamCatalogBusTypeList() {
        return Arrays.asList(JZ_DBM.getCode());
    }

}
