package com.rs.framework.common.util.http;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.bsp.common.util.RequestUtil;

/**
 * ip处理工具类
 * <AUTHOR>
 * @date 2025年6月14日
 */
public class IpUtils {

	/**
	 * 获取请求ip地址
	 * @return String
	 */
	public static String getClientIp() {
		ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
		if(servletRequestAttributes != null) {
			HttpServletRequest request = servletRequestAttributes.getRequest();
			return RequestUtil.getClientIp(request);
		}
		return null;
	}
}
