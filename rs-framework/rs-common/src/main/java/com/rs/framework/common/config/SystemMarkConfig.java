package com.rs.framework.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName SystemMarkConfig
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/25 17:23
 * @Version 1.0
 */
@Configuration
@ConfigurationProperties(prefix = "bsp")
public class SystemMarkConfig {

    private String systemMark;

    public String getSystemMark() {
        return systemMark;
    }

    public void setSystemMark(String systemMark) {
        this.systemMark = systemMark;
    }
}
