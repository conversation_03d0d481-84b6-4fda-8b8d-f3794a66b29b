package com.rs.framework.common.handler;

import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName ChainBuilder
 * 
 * <AUTHOR>
 * @Date 2025/3/20 19:28
 * @Version 1.0
 */
@Component
public class ChainBuilder<T> {
    public Handler<T> buildChain(List<Handler<T>> handlers) {
        if (handlers == null || handlers.isEmpty()) {
            return null;
        }
        for (int i = 0; i < handlers.size() - 1; i++) {
            handlers.get(i).setNextHandler(handlers.get(i + 1));
        }
        return handlers.get(0);
    }
}
