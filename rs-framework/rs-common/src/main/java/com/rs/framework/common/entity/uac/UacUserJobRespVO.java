package com.rs.framework.common.entity.uac;

import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "管理后台 - 用户岗位关联 Response VO")
@Data
public class UacUserJobRespVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("用户Id")
    private String userId;
    @ApiModelProperty("机构Id")
    private String orgId;
    @ApiModelProperty("岗位Id")
    private String jobId;
    @ApiModelProperty("有效期类型：01:永久，02：临时")
    private String validType;
    @ApiModelProperty("有效开始时间")
    private Date beginTime;
    @ApiModelProperty("有效结束时间")
    private Date endTime;
    @ApiModelProperty("是否默认岗位(0:否1:是)")
    private String isDefault;
}
