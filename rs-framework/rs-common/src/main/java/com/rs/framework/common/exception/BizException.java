package com.rs.framework.common.exception;

import org.springframework.http.HttpStatus;

/**
 * 业务基础异常
 */
public class BizException extends RuntimeException {

	public BizException() {
    }

    public BizException(String message) {
        super(message);
    }


    public BizException(Integer errorCode, String errorMsg) {
        super(errorMsg);
        this.errorMsg = errorMsg;
        this.errorCode=errorCode;
    }

    public BizException(String message, Throwable cause) {
        super(message, cause);
    }


    /**
     * 状态码
     */
    private HttpStatus status;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 业务异常自定义错误码
     */
    private Integer errorCode;

}
