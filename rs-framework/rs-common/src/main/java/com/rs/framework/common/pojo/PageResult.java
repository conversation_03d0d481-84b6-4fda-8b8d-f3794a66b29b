package com.rs.framework.common.pojo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.util.object.BeanUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 分页结果
 *
 * @param <T>
 * <AUTHOR>
 */
@Schema(description = "分页结果")
@Data
public final class PageResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "数据", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<T> list;

    @Schema(description = "总量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long total;

    public PageResult() {
    }

    public PageResult(List<T> list, Long total) {
        this.list = list;
        this.total = total;
    }

    public PageResult(Long total) {
        this.list = new ArrayList<>();
        this.total = total;
    }

    public static <T> PageResult<T> empty() {
        return new PageResult<>(0L);
    }

    public static <T> PageResult<T> empty(Long total) {
        return new PageResult<>(total);
    }

    public static <T> PageResult<T> fromPage(Page<T> page) {
        if (page != null) {
            PageResult<T> p = new PageResult<T>(page.getRecords(), page.getTotal());
            return p;
        }
        return null;
    }

    public PageResult toBean(Class targetType) {
        return BeanUtils.toBean(this, targetType);
    }


}
