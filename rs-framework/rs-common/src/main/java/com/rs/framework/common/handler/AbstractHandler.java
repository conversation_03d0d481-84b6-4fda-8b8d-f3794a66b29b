package com.rs.framework.common.handler;

/**
 * @ClassName AbstractHandler
 * 
 * <AUTHOR>
 * @Date 2025/3/20 19:28
 * @Version 1.0
 */
public abstract class AbstractHandler<T> implements <PERSON>ler<T> {
    private Handler<T> nextHandler;

    @Override
    public void setNextHandler(Handler<T> nextHandler) {
        this.nextHandler = nextHandler;
    }

    @Override
    public T handle(T input) {
        T result = doHandle(input);
        if (nextHandler != null) {
            return nextHandler.handle(result);
        }
        return result;
    }

    protected abstract T doHandle(T input);
}
