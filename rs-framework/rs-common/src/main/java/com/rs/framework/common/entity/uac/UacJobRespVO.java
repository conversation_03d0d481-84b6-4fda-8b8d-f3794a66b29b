package com.rs.framework.common.entity.uac;

import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 岗位 Response VO")
@Data
public class UacJobRespVO  implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("ID")
    private String id;
    @ApiModelProperty("岗位名称")
    private String name;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("是否删除(0否1是)")
    private String isdel;
    @ApiModelProperty("应用ID")
    private String addId;
    @ApiModelProperty("排序")
    private Integer orderId;
    @ApiModelProperty("是否禁用(0:否1:是)")
    private String isDisabled;
}
