package com.rs.framework.common.entity.uac;

import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 用户角色 Response VO")
@Data
public class UacUserRoleRespVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("用户Id")
    private String userId;
    @ApiModelProperty("角色Id")
    private String roleId;
    @ApiModelProperty("机构id")
    private String orgId;
    @ApiModelProperty("角色类型(01:系统角色ID、02:通用角色ID)")
    private String type;
    @ApiModelProperty("是否同步数据(0否1是)")
    private String isSync;
}
