package com.rs.framework.common.annotation;


import com.rs.framework.common.validator.ExpressionValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = {ExpressionValidator.class})
public @interface ExpressionValid {
    String message() default "参数校验不通过";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    String expression();
}
