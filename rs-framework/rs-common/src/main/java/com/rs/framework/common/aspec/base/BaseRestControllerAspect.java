package com.rs.framework.common.aspec.base;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * @ClassName BaseRestControllerAspect
 * 
 * <AUTHOR>
 * @Date 2025/3/29 21:51
 * @Version 1.0
 */
@Aspect
@Component
@Order(1) // 可以根据需要设置顺序
public abstract class BaseRestControllerAspect<T> {

    @Autowired
    protected T processor;

    @Around("execution(* @org.springframework.web.bind.annotation.RestController *.*(..))")
    public Object processFields(ProceedingJoinPoint joinPoint) throws Throwable {
        // 执行目标方法
        Object result = joinPoint.proceed();

        // 如果结果是对象，则处理字段
        if (result != null) {
            result = processResult(result);
        }

        return result;
    }

    protected abstract Object processResult(Object result) throws Throwable;
}
