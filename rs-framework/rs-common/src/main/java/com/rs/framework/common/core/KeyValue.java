package com.rs.framework.common.core;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Key Value 的键值对
 * <AUTHOR>
 *
 * @param <K>
 * @param <V>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KeyValue<K, V> implements Serializable {

    private static final long serialVersionUID = 1L;
    
	private K key;
	
    private V value;
}
