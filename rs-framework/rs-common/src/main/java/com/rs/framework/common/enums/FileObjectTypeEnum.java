package com.rs.framework.common.enums;

/**
 * @ClassName FileObjectTypeEnum
 * 
 * <AUTHOR>
 * @Date 2025/3/28 17:18
 * @Version 1.0
 */
public enum FileObjectTypeEnum {
    cgjc("cgjc", "常规检查"),
    xdt("xdt", "心电图"),
    bc("bc", "B超"),
    dr("dr", "DR"),
    ;

    private String code;
    private String desc;

    FileObjectTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
