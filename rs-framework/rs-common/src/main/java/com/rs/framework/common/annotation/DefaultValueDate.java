package com.rs.framework.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DefaultValueDate {
    String value() default ""; // 默认值，Date 类型，格式为 "yyyy-MM-dd"
    Class<?> service() default Object.class; // 服务类的类型
    String method() default ""; // 服务类中用于查询的方法名称
    String paramField() default ""; // 作为参数的字段名称
}
