package com.rs.framework.common.util.object;

import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @ClassName ClassUtils
 * <AUTHOR>
 * @Date 2025/3/26 19:45
 * @Version 1.0
 */
public class ClassUtils {

    public static boolean isComplexType(Class<?> type) {
        return !type.isPrimitive() &&
                !type.equals(String.class) &&
                !type.equals(Date.class) &&
                !type.equals(Map.class) &&
                !type.equals(LinkedHashMap.class) &&
                !type.equals(HashMap.class) &&
                ((type.getName().startsWith("com.bsp") ||
                        type.getName().startsWith("com.rs") ||
                        type.getName().startsWith("java.util")
                                &&
                                (
                                        !type.isEnum() && !type.isAnnotation() && !type.isInterface()
                                )
                ));
    }
    public static boolean isComplexType3(Class<?> type) {
        return
                (type.isPrimitive() ||
                        type.equals(String.class) ||
                        type.equals(Date.class) ||
                        type.equals(Integer.class));
    }

    public static boolean isComplexType4(Class<?> type) {
        return
                ((type.getName().startsWith("com.bsp") ||
                        type.getName().startsWith("com.rs") ||
                        type.getName().startsWith("java.util")) &&
                                (
                                        !type.isEnum() && !type.isAnnotation() && !type.isInterface()
                                ));
    }

}
