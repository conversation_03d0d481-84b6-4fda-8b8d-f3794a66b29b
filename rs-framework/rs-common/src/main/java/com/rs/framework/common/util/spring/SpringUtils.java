package com.rs.framework.common.util.spring;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Objects;

import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * Spring 工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class SpringUtils extends SpringUtil {

    /**
     * 是否为生产环境
     *
     * @return 是否生产环境
     */
    public static boolean isProd() {
        String activeProfile = getActiveProfile();
        return Objects.equals("prod", activeProfile);
    }

    /**
     * 打印程序启动日志
     * @param application ConfigurableApplicationContext
     * @throws UnknownHostException
     * <AUTHOR>
     * @date 2025年3月14日
     */
    public static void printStartLog(ConfigurableApplicationContext application) throws UnknownHostException{
    	Environment env = application.getEnvironment();
		String ip = InetAddress.getLocalHost().getHostAddress();
		String name = env.getProperty("spring.application.name");
		String port = env.getProperty("server.port");
		log.info("\n----------------------------------------------------------\n\t" +
	                "Application [" + name + "] is running! Access URLs:\n\t" +
	                "【Local】 \thttp://localhost:" + port + "/\n\t" +
	                "【External】 \thttp://" + ip + ":" + port + "/\n\t" +
	                "【druid】 \thttp://" + ip + ":" + port + "/druid/login.html\n\t" +
	                "【Swagger】 \thttp://" + ip + ":" + port + "/doc.html\n" +
	                "----------------------------------------------------------");
    }
}
