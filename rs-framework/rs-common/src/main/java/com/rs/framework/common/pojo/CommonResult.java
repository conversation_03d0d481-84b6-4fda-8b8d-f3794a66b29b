package com.rs.framework.common.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.rs.framework.common.exception.ErrorCode;
import com.rs.framework.common.exception.ServiceException;
import com.rs.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.rs.framework.common.exception.util.ServiceExceptionUtil;
import lombok.Data;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Objects;

/**
 * 通用返回
 *
 * @param <T> 数据泛型
 * <AUTHOR>
 */
@Data
public class CommonResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
     * 错误码
     *
     * @see ErrorCode#getCode()
     */
    private Integer code;
    private Integer returnCode=0;
    private Integer status;

    /**
     * 返回数据
     */
    private T data;

    /**
     * 错误提示，用户可阅读
     *
     * @see ErrorCode#getMsg() ()
     */
    private String msg;
    private String message;

    /**
     * 是否成功
     */
    @SuppressWarnings("unused")
	private Boolean success;

    /**
     * 将传入的 result 对象，转换成另外一个泛型结果的对象
     *
     * 因为 A 方法返回的 CommonResult 对象，不满足调用其的 B 方法的返回，所以需要进行转换。
     *
     * @param result 传入的 result 对象
     * @param <T> 返回的泛型
     * @return 新的 CommonResult 对象
     */
    public static <T> CommonResult<T> error(CommonResult<?> result) {
        return error(result.getCode(), result.getMsg());
    }
    public static <T> CommonResult<T> error(String result) {
        return error(500, result);
    }
    public static <T> CommonResult<T> error(T data) {
        CommonResult<T> result = new CommonResult<>();
        result.code = 500;
        result.data = data;
        result.msg = "";
        result.returnCode = 1;
        return result;
    }
    public static <T> CommonResult<T> error(Integer code,T data) {
        CommonResult<T> result = new CommonResult<>();
        result.code = code;
        result.data = data;
        result.msg = "";
        result.returnCode = 1;
        return result;
    }

    public static <T> CommonResult<T> error(Integer code, String message) {
        if (GlobalErrorCodeConstants.SUCCESS.getCode().equals(code))  {
            throw new IllegalArgumentException("code 必须是错误的！");
        }
        CommonResult<T> result = new CommonResult<>();
        result.code = code;
        result.msg = message;
        result.message = message;
        result.success = false;
        result.returnCode = 1;
        return result;
    }

    public static <T> CommonResult<T> error(ErrorCode errorCode, Object... params) {
        if (GlobalErrorCodeConstants.SUCCESS.getCode().equals(errorCode.getCode()))  {
            throw new IllegalArgumentException("code 必须是错误的！");
        }
        CommonResult<T> result = new CommonResult<>();
        result.code = errorCode.getCode();
        result.msg = ServiceExceptionUtil.doFormat(errorCode.getCode(), errorCode.getMsg(), params);
        result.success = false;
        result.returnCode = 1;
        return result;
    }

    public static <T> CommonResult<T> error(ErrorCode errorCode) {
        return error(errorCode.getCode(), errorCode.getMsg());
    }

    public static <T> CommonResult<T> success(T data, String msg) {
        CommonResult<T> result = new CommonResult<>();
        result.code = GlobalErrorCodeConstants.SUCCESS.getCode();
        result.status = 200;
        result.data = data;
        result.msg = msg;
        result.message = msg;
        result.success = true;
        result.returnCode = 0;
        return result;
    }

    public static <T> CommonResult<T> success(T data) {
    	return success(data, "");
    }

    @SuppressWarnings("rawtypes")
	public static CommonResult success() {
    	return success("请求成功", "");
    }

    public static boolean isSuccess(Integer code) {
        return Objects.equals(code, GlobalErrorCodeConstants.SUCCESS.getCode());
    }

    public boolean isSuccess() {
        return isSuccess(code);
    }

    @JsonIgnore // 避免 jackson 序列化
    public boolean isError() {
        return !isSuccess();
    }

    // ========= 和 Exception 异常体系集成 =========

    /**
     * 判断是否有异常。如果有，则抛出 {@link ServiceException} 异常
     */
    public void checkError() throws ServiceException {
        if (isSuccess()) {
            return;
        }
        // 业务异常
        throw new ServiceException(code, msg);
    }

    /**
     * 判断是否有异常。如果有，则抛出 {@link ServiceException} 异常
     * 如果没有，则返回 {@link #data} 数据
     */
    @JsonIgnore // 避免 jackson 序列化
    public T getCheckedData() {
        checkError();
        return data;
    }

    public static <T> CommonResult<T> error(ServiceException serviceException) {
        return error(serviceException.getCode(), serviceException.getMessage());
    }

    public static ResponseEntity<byte[]> fileStream(byte[] bot, String fileName, MediaType mediaType) {

        HttpHeaders headers = new HttpHeaders();
        if (mediaType.toString().equals("application/octet-stream")) {
            try {
                headers.setContentDispositionFormData("attachment", fileName = java.net.URLEncoder.encode(fileName, "UTF-8"));
            } catch (UnsupportedEncodingException e) {

            }
        } else {
            try {
                headers.setContentDispositionFormData("inline", fileName = java.net.URLEncoder.encode(fileName, "UTF-8"));
            } catch (UnsupportedEncodingException e) {

            }
        }

        headers.setContentType(mediaType);
        return ResponseEntity
                .ok()
                .headers(headers)
                .contentLength(bot.length)
                .contentType(MediaType.parseMediaType(mediaType.toString()))
                .body(bot);
    }

}
