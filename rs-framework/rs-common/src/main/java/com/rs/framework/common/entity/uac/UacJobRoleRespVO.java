package com.rs.framework.common.entity.uac;

import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 岗位角色关联 Response VO")
@Data
public class UacJobRoleRespVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("岗位Id")
    private String jobId;
    @ApiModelProperty("角色Id")
    private String roleId;
    @ApiModelProperty("角色类型(01:角色ID、02:角色组ID)")
    private String type;
}
