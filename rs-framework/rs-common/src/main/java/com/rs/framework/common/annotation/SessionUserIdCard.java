package com.rs.framework.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface SessionUserIdCard {
    String value() default ""; // 默认值，String 类型
    Class<?> service() default Object.class; // 服务类的类型
    String method() default ""; // 服务类中用于查询的方法名称
    String paramField() default ""; // 作为参数的字段名称
}
