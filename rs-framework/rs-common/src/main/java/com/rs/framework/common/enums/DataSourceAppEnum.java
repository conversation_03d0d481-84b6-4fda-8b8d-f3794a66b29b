package com.rs.framework.common.enums;

/**
 * 数据来源字段
 * <AUTHOR>
 * @Date 2025/5/19 20:00
 */
public enum DataSourceAppEnum {
    ACP("1", "实战平台"),
    CWP("2", "仓外屏"),
    CNP("3", "仓内屏");

    DataSourceAppEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;
    public String getCode() {
        return code;
    }
    public String getName() {
        return name;
    }
}
