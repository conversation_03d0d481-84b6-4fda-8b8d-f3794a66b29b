package com.rs.framework.common.enums;

/**
 * RPC 相关的枚举
 *
 * 虽然放在 yudao-spring-boot-starter-rpc 会相对合适，但是每个 API 模块需要使用到，所以暂时只好放在此处
 *
 * <AUTHOR>
 */
public class RpcConstants {

	/** RPC API 的前缀 */
    public static final String RPC_API_PREFIX = "/rpc-api";

    /** RPC API调用时的 header 用户参数名称*/
    public static final String RPC_API_USER_HEADER = "loginUser";
    
    /** RPC API调用时的 header Token参数名称*/
    public static final String RPC_API_TOKEN_HEADER = "rpc-token";
    
    /** RPC API调用时的 header 应用代码参数名称*/
    public static final String RPC_API_APP_CODE_HEADER = "appCode";
}
