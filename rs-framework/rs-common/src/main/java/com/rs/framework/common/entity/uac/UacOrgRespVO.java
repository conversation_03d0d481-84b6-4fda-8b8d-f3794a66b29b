package com.rs.framework.common.entity.uac;

import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 机构 Response VO")
@Data
public class UacOrgRespVO implements TransPojo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("批量Id")
    private String name;
    @ApiModelProperty("附加属性")
    private String code;
    @ApiModelProperty("简称")
    private String sname;
    @ApiModelProperty("全称")
    private String fname;
    @ApiModelProperty("拼音码")
    private String scode;
    @ApiModelProperty("简拼码")
    private String jpcode;
    @ApiModelProperty("文书号简称")
    private String otherSname;
    @ApiModelProperty("功能Id")
    private String cityId;
    @ApiModelProperty("区域id")
    private String regionId;
    @ApiModelProperty("URL地址")
    private String regionName;
    @ApiModelProperty("父Id")
    private String parentId;
    @ApiModelProperty("地址")
    private String address;
    @ApiModelProperty("办公电话")
    private String officeTel;
    @ApiModelProperty("是否正式机构(0否1是)")
    private String isFormal;
    @ApiModelProperty("是否停用(0否1是)")
    private String isDisabled;
    @ApiModelProperty("排序Id")
    private Integer orderId;
    @ApiModelProperty("是否删除(0否1是)")
    private String isdel;
    @ApiModelProperty("单位类型(01:隶属局单位02:直属局单位03:总署单位)ZD_ORG_TYPE")
    private String orgType;
    @ApiModelProperty("是否同步数据(0否1是)")
    private String isSync;
    @ApiModelProperty("单位编号()")
    private String unitNo;
    @ApiModelProperty("单位警种")
    private String orgCategory;
    @ApiModelProperty("是否具有执法权")
    private String haszfq;
    @ApiModelProperty("是否业务部门")
    private String isBusinessOrg;
    @ApiModelProperty("报警电话")
    private String bjdh;
    @ApiModelProperty("电子邮件")
    private String email;
    @ApiModelProperty("传真电话")
    private String czdh;
    @ApiModelProperty("单位介绍")
    private String intro;
    @ApiModelProperty("单位全路径#")
    private String orgpath;
}
