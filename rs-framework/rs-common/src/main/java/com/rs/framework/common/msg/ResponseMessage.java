package com.rs.framework.common.msg;

import org.springframework.http.HttpStatus;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import lombok.Data;

/**
 * 响应实体
 * <AUTHOR>
 * @date 2025年5月14日
 * @param <T>
 */
@Data
public class ResponseMessage<T> {

    public static final String DEFAULT_SUCCESS_MESSAGE = "操作成功!";
    public static final String DEFAULT_ERROR_MESSAGE = "操作失败!";
    public final static int SUCCESS = 0;
    public final static int FAIL = 1;

    /**
     * 状态码:
     * 200 - 成功
     * 400 - 表单校验异常
     * 401 - 权限异常
     * 500 - 系统内部异常
     */
    private Integer status;

    /**
     * 返回信息
     */
    protected String message;

    /**
     * 返回数据对象
     */
    private T data;

    /**
     * 分页信息
     */
    private Page<T> page;
    
    /**
     * 返回标识[0:成功,1:失败]
     */
    private int returnCode;

    public ResponseMessage<T> status(Integer status) {
        this.status = status;
        return this;
    }

    public ResponseMessage<T> data(T data) {
        this.data = data;
        return this;
    }

    public ResponseMessage<T> message(String message) {
        this.message = message;
        return this;
    }

    public ResponseMessage<T> page(Page<T> page) {
        ResponseMessage<T> message = new ResponseMessage<T>();
        message.setStatus(HttpStatus.OK.value());
        message.setMessage(DEFAULT_SUCCESS_MESSAGE);
        message.setPage(page);
        return message;
    }

    /**
     *  默认成功接口
     * @return
     */
    public ResponseMessage<T> defaultSuccessMessage() {
        status = HttpStatus.OK.value();
        message = DEFAULT_SUCCESS_MESSAGE;
        return this;
    }

    /**
     *  默认错误接口
     * @return
     */
    public ResponseMessage<T> defaultErrorMessage() {
        status = HttpStatus.INTERNAL_SERVER_ERROR.value();
        message = DEFAULT_ERROR_MESSAGE;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public T getData() {
        return data;
    }
}