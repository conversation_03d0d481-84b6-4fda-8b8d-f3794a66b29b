<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<parent>
		<groupId>com.rs</groupId>
		<artifactId>rs-framework</artifactId>
		<version>${rs.version}</version>
	</parent>
	
	<artifactId>rs-starter-rpc</artifactId>
	<packaging>jar</packaging>
	<name>${project.artifactId}</name>
	<description>监所管理-技术组件-远程方法调用组件，基于 Feign 实现服务之间的调用</description>
	
	<dependencies>
		
		<!-- RPC 远程调用相关 begin -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-loadbalancer</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>
		<dependency>
			<groupId>io.github.openfeign</groupId>
			<artifactId>feign-okhttp</artifactId>
		</dependency>
		<!-- RPC 远程调用相关 end -->
		
		<!-- bsp 相关 begin -->
		<dependency>
			<groupId>com.bsp</groupId>
			<artifactId>bsp-common</artifactId>
			<exclusions>
				<exclusion>
					<groupId>cn.hutool</groupId>
		  			<artifactId>hutool-all-u</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.bsp</groupId>
			<artifactId>bsp-security</artifactId>
		</dependency>
		<!-- bsp 相关 end -->
		
		<!-- 工具类相关 begin -->
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-common</artifactId>
		</dependency>
		<dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
        <!-- 工具类相关 end -->
	</dependencies>
</project>