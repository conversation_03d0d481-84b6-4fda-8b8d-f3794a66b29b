package com.rs.framework.rpc.core;

/**
 * 用户token上下文
 * <AUTHOR>
 * @date 2025年6月23日
 */
public class UserTokenContext {

	private static final ThreadLocal<String> tokenThreadLocal = new ThreadLocal<>();
	
	public static void setToken(String token) {
		tokenThreadLocal.set(token);
	}
	
	public static String getToken() {
		return tokenThreadLocal.get();
	}
	
	public static void remove() {
		tokenThreadLocal.remove();
	}
}
