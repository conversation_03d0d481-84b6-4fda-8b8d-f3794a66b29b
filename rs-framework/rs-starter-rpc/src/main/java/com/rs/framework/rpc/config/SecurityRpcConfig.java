package com.rs.framework.rpc.config;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.client.OAuth2RestTemplate;
import org.springframework.security.oauth2.client.resource.OAuth2ProtectedResourceDetails;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;
import org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationDetails;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.alibaba.fastjson.JSON;
import com.bsp.common.cache.RedisClient;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.CustomUser;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.cons.RedisConstants;
import com.rs.framework.common.enums.RpcConstants;
import com.rs.framework.common.util.json.JsonUtils;

import feign.RequestInterceptor;
import feign.RequestTemplate;

/**
 * RCP使用的security配置
 * <AUTHOR>
 * @date 2025年3月20日
 */
@Configuration
@EnableFeignClients
public class SecurityRpcConfig {
	
	@Value("${bsp.token.url}")
	private String accessTokenUri;

	@Value("${bsp.token.clientId:app}")
	private String clientId;

	@Value("${bsp.token.clientSecret:123456}")
	private String clientSecret;

	@Value("${bsp.token.grantType:client_credentials}")
	private String grantType;

	@Value("${bsp.oauth2.client.expires:1800}")
	private int expires;
	
	/**
	 * 获取Token的请求
	 * @return OAuth2ProtectedResourceDetails
	 */
	private OAuth2ProtectedResourceDetails resource() {
		ClientCredentialsResourceDetails resourceDetails = new ClientCredentialsResourceDetails();
		resourceDetails.setAccessTokenUri(accessTokenUri);
		resourceDetails.setClientId(clientId);
		resourceDetails.setClientSecret(clientSecret);
		resourceDetails.setGrantType(grantType);
		return resourceDetails;
	}
	
	/**
	 * 构建新的Token
	 * @return String
	 */
	private String newToken() {
		return new OAuth2RestTemplate(resource()).getAccessToken().getValue();
	}
	
	/**
	 * 请求拦截类
	 * @return RequestInterceptor
	 */
	@Bean
	public RequestInterceptor loginUserRequestInterceptor() {
		return new RequestInterceptor() {
			public void apply(RequestTemplate template) {
				SessionUser user = null;
				
		        try {
		        	//处理前端传递的应用代码参数
		        	ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();            
		            if(attributes != null) {
		            	HttpServletRequest request = attributes.getRequest();
			            String appCode = request.getHeader(RpcConstants.RPC_API_APP_CODE_HEADER);
			            if(StringUtil.isNotEmpty(appCode)) {
			                template.header(RpcConstants.RPC_API_APP_CODE_HEADER, appCode);
			            }
		            }
		            
		            //从请求中获取认证信息
		            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
		            if(authentication != null) {
		            	if(authentication.getDetails() instanceof OAuth2AuthenticationDetails) {
		            		OAuth2AuthenticationDetails details = (OAuth2AuthenticationDetails)authentication.getDetails();
		            		template.header("Authorization", "Bearer " + details.getTokenValue());
		            	}
		            	else {
		            		template.header("Authorization", "Bearer " + newToken());
		            	}
		            	
		            	//获取会话用户
		            	Object principal = authentication.getPrincipal();
		            	if (principal.getClass().equals(CustomUser.class)) {
		            		user = JSON.parseObject(principal.toString(), SessionUser.class);
		    			}
		            }
		            else {
		            	String token = RedisClient.get(RedisConstants.CLIENT_CREDENTIALS_EXPIRES);
		            	if(token != null && RedisClient.get(RedisConstants.BSP_ACCESS_TOKNE + token) != null) {
		            		template.header("Authorization", "Bearer " + token);
		            	}
		            	else {
		            		String newToken = newToken();
		            		template.header("Authorization", "Bearer " + newToken);
		            		RedisClient.set(RedisConstants.CLIENT_CREDENTIALS_EXPIRES, newToken, expires);
		            	}
		            }
		            
		            //在请求头中放置用户信息
		            if(user != null) {
		            	String userStr = JsonUtils.toJsonString(user);
		            	userStr = URLEncoder.encode(userStr, StandardCharsets.UTF_8.name());
		                template.header(RpcConstants.RPC_API_USER_HEADER, userStr);
		                String token = SessionUserUtil.getAccessToken();
		                template.header(RpcConstants.RPC_API_TOKEN_HEADER, token);
		                template.query("access_token", token);
		            }
		        } catch (Exception e) {
		        }
		    }
		};
	}
}
