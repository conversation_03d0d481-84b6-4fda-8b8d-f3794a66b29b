package com.rs.framework.rpc.core;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.enums.RpcConstants;
import com.rs.framework.common.util.json.JsonUtils;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.SneakyThrows;

/**
 * 登录用户请求拦截类（Feign 请求时，将 {@link LoginUser} 设置到 header 中，继续透传给被调用的服务）
 *
 * <AUTHOR>
 * @date 2025年3月20日
 */
public class LoginUserRequestInterceptor implements RequestInterceptor {

    @Override
    @SneakyThrows
    public void apply(RequestTemplate template) {
        try {
        	//处理前端传递的应用代码参数
        	ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();            
            if(attributes != null) {
            	HttpServletRequest request = attributes.getRequest();
	            String appCode = request.getHeader(RpcConstants.RPC_API_APP_CODE_HEADER);
	            if(StringUtil.isNotEmpty(appCode)) {
	                template.header(RpcConstants.RPC_API_APP_CODE_HEADER, appCode);
	            }
            }
            
            //获取会话用户
            SessionUser user = null;
            try {
            	user = SessionUserUtil.getSessionUser();
            }
            catch(Exception e) {}
            
            //传递会话
            if (user == null) {
            	String token = UserTokenContext.getToken();
            	if(StringUtil.isNotEmpty(token)) {
            		template.header(RpcConstants.RPC_API_TOKEN_HEADER, token);
                    template.query("access_token", token);
            	}
            } else {
                String userStr = JsonUtils.toJsonString(user);
                userStr = URLEncoder.encode(userStr, StandardCharsets.UTF_8.name());
                template.header(RpcConstants.RPC_API_USER_HEADER, userStr);
                String token = SessionUserUtil.getAccessToken();
                template.header(RpcConstants.RPC_API_TOKEN_HEADER, token);
                template.query("access_token", token);
            }
        } catch (Exception e) {
        }
    }
}
