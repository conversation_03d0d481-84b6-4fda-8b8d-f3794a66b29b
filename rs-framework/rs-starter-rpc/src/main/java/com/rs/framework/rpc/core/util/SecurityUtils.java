package com.rs.framework.rpc.core.util;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.bsp.security.model.SessionUser;
import com.rs.framework.common.enums.RpcConstants;
import com.rs.framework.common.util.json.JsonUtils;

/**
 * RCP远程调用安全工具类
 * <AUTHOR>
 * @date 2025年3月20日
 */
public class SecurityUtils {

	/**
	 * 获取Rpc远程调用的当前会话用户
	 * @return SessionUser
	 * <AUTHOR>
	 * @date 2025年3月20日
	 */
	public static SessionUser getRpcSessionUser() {
		SessionUser user = null;
		
		if(RequestContextHolder.currentRequestAttributes() != null) {
			HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.currentRequestAttributes()).getRequest();
			String loginUser = request.getHeader(RpcConstants.RPC_API_USER_HEADER);
			try {
				loginUser = URLDecoder.decode(loginUser, StandardCharsets.UTF_8.name());
				user = JsonUtils.parseObject(loginUser, SessionUser.class);
			}
			catch(Exception e) {
				e.printStackTrace();
			}
		}
		
		return user;
	}
	
	/**
	 * 获取Rpc远程调用的当前Token
	 * @return String
	 * <AUTHOR>
	 * @date 2025年4月14日
	 */
	public static String getRpcToken() {
		String token = null;
		
		if(RequestContextHolder.currentRequestAttributes() != null) {
			HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.currentRequestAttributes()).getRequest();
			token = request.getHeader(RpcConstants.RPC_API_TOKEN_HEADER);
		}
		
		return token;
	}
}
