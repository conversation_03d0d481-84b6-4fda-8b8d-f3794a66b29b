<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.rs</groupId>
		<artifactId>rs-framework</artifactId>
		<version>${rs.version}</version>
	</parent>

	<artifactId>rs-starter-web</artifactId>
	<packaging>jar</packaging>
	<name>${project.artifactId}</name>
	<description>监所管理-技术组件-web组件，全局异常、API 日志、脱敏、错误码等</description>

	<dependencies>
		<!-- spring 相关 begin -->
		<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
		<!-- spring 相关 end -->

		<!-- web 相关 begin -->
		<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
            <scope>provided</scope> <!-- 设置为 provided，主要是 GlobalExceptionHandler 使用 -->
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>  <!-- 接口文档 -->
            <artifactId>springdoc-openapi-ui</artifactId>
        </dependency>
		<!-- web 相关 end -->

		<!-- 工具类相关 begin -->
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-common</artifactId>
		</dependency>
		<!-- 工具类相关 end -->

		<!-- 业务组件 begin -->
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-module-infra-api</artifactId>
		</dependency>
		<!-- 业务组件 end -->

		<!-- 框架组件 begin -->
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-starter-mybatis</artifactId>
		</dependency>
		<!-- 框架组件 end -->
		<!-- 文档 begin -->
		<dependency>
			<groupId>com.github.xiaoymin</groupId>
			<artifactId>knife4j-spring-boot-starter</artifactId>
		</dependency>
		<!-- 文档 end -->
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
		</dependency>

	</dependencies>
</project>
