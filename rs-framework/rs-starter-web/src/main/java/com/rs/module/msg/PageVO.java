package com.rs.module.msg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


/**
*<AUTHOR>
*@email <EMAIL>
*@date 2021/12/23
*/

@Getter
@Setter
@ApiModel(description = "返回分页实体")
public class PageVO<T> {
    @ApiModelProperty(value = "总数", required = true)
    private int total;

    @ApiModelProperty(value = "内容", required = true)
    private List<T> rows;
}
