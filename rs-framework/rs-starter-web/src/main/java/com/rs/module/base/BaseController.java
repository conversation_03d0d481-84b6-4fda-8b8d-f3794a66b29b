package com.rs.module.base;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName BaseController
 *
 * <AUTHOR>
 * @Date 2025/3/14 18:48
 * @Version 1.0
 */
@Api(tags = "01swagger设置token")
@RestController
@RequestMapping("/swagger")
public class BaseController {

    @Value("${bsp.token.url:http://*************:999/bsp-uac/oauth/token}")
    private String url;

    /**
     * 登录
     */
    @GetMapping("/login")
    @ApiImplicitParam(name = "script", value = "")
    public CommonResult<JSONObject> login(String login){
        Map<String,Object> user = new HashMap<>();
        user.put("username","admin");
        user.put("password","y93bjoQhsjSYSAVw19dTMFOKaIL139w7ZBFcZH8zKMQ=");
        user.put("client_id","user_client");
        user.put("client_secret","user_client");
        user.put("scope","trust");
        user.put("grant_type","password");
        user.put("app_mark","bsp");
        String post = HttpUtil.post(url, user);
        String script = "var token=ke.response.data.data.access_token;  ke.global.setParameter('access_token',token); alert('获取token成功');";
        JSONObject jsonObject = JSONObject.parseObject(post);
        jsonObject.put("script",script);
        return CommonResult.success(jsonObject);
    }
}
