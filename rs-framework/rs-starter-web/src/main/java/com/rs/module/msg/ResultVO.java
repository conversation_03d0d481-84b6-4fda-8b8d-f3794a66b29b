package com.rs.module.msg;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(description = "基本返回实体")
public class ResultVO<T> implements Serializable {
    private static final long serialVersionUID = 6260548303366126059L;

    @ApiModelProperty(value = "返回标识[0:成功,1:失败]", required = true)
    private int returnCode;

    @ApiModelProperty(value = "返回信息", required = true)
    private String returnMsg;

    @ApiModelProperty(value = "返回详情", required = true)
    private String returnDetail;

    @ApiModelProperty(value = "返回内容", required = true)
    private T data;

    public final static int SUCCESS = 0;

    public final static int FAIL = 1;

    /**
     * 返回响应成功结果
     * *
     * * @param data
     * * @return
     */

    public static <T> ResultVO<T> success(T data) {
        ResultVO<T> result = new ResultVO<T>();
        result.setReturnCode(SUCCESS);
        result.setReturnMsg("请求成功");
        result.setData(data);
        return result;
    }

	@SuppressWarnings("rawtypes")
	public static ResultVO success() {
		ResultVO result = new ResultVO();
        result.setReturnCode(SUCCESS);
        result.setReturnMsg("请求成功");
        return result;
	}

	public static <T> ResultVO<PageVO<T>> page(Page<T> page) {
		PageVO<T> p = new PageVO<T>();
		p.setRows(page.getRecords());
		p.setTotal((int) page.getTotal());
		return success(p);
	}

    /**
     * 返回响应成功结果
     *
     * @param msg
     * @param data
     * @return
     */
    public static <T> ResultVO<T> success(String msg, T data) {
        ResultVO<T> result = new ResultVO<T>();
        result.setReturnCode(SUCCESS);
        result.setReturnMsg(msg);
        result.setData(data);
        return result;
    }

    /**
     * 返回响应失败结果
     *
     * @return
     */
    public static <T> ResultVO<T> error() {
        ResultVO<T> result = new ResultVO<T>();
        result.setReturnCode(FAIL);
        result.setReturnMsg("未知错误，请联系管理员");
        result.setReturnDetail("Unhandle Exception!");
        return result;
    }

    /**
     * 返回响应失败结果
     * <p>
     * * @param msg
     * * @return
     */
    public static <T> ResultVO<T> error(String msg, String detail) {
        ResultVO<T> result = new ResultVO<T>();
        result.setReturnCode(FAIL);
        result.setReturnMsg(msg);
        result.setReturnDetail(detail);
        return result;
    }

    /**
     * 返回响应失败结果
     * <p>
     * * @param msg
     * * @return
     */
    public static <T> ResultVO<T> error(String msg) {
        ResultVO<T> result = new ResultVO<T>();
        result.setReturnCode(FAIL);
        result.setReturnMsg(msg);
        result.setReturnDetail(null);
        return result;
    }

}
