package com.rs.framework.web.config;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.converters.ConverterKeyBuild;
import com.alibaba.excel.converters.DefaultConverterLoader;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.DateSerializer;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializerBase;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.rs.framework.web.utils.LocalDateTimeUtil;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;

import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * @Author: tzx
 * @Date: 2022/8/2 16:54
 */
@Configuration
public class JacksonConfig {
    private String pattern ="yyyy-MM-dd HH:mm:ss";

    /**
     * 默认设置所有long类型 序列化返回前端 全变成string
     *           所有LocalDatetime 序列号返回前端  全部格式化
     * @return
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {
        return builder -> {
            builder.serializerByType(BigDecimal.class, bigDecimalSerializer());
            builder.serializerByType(LocalDateTime.class, localDateTimeSerializer());
            builder.featuresToEnable(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN);
            builder.deserializerByType(LocalDateTime.class, localDateTimeDeserializer());
            builder.serializationInclusion(com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS);
            builder.visibility(PropertyAccessor.FIELD, JsonAutoDetect.Visibility.ANY);
            builder.visibility(PropertyAccessor.GETTER, JsonAutoDetect.Visibility.NONE);
            builder.visibility(PropertyAccessor.SETTER, JsonAutoDetect.Visibility.NONE);
            builder.visibility(PropertyAccessor.CREATOR, JsonAutoDetect.Visibility.NONE);
            builder.serializerByType(Date.class, new DateSerializer(false, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")));
            // 注册 CustomDateDeserializer
            SimpleModule module = new SimpleModule();
            module.addDeserializer(Date.class, new CustomDateDeserializer());
            builder.modules(module);
        };
    }

    /**
     * Jackson格式化BigDecimal类型数据
     * @return
     */
    public ToStringSerializerBase bigDecimalSerializer() {
        return new ToStringSerializerBase(BigDecimal.class) {
            protected final static int MAX_BIG_DECIMAL_SCALE = 9999;
            @Override
            public String valueToString(Object value) {
                throw new IllegalStateException();
            }

            @Override
            public void serialize(Object value, JsonGenerator gen, SerializerProvider provider) throws IOException {
                if (gen.isEnabled(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN)) {
                    final BigDecimal bd = (BigDecimal) value;
                    // 24-Aug-2016, tatu: [core#315] prevent possible DoS vector, so we need this
                    if (!_verifyBigDecimalRange(gen, bd)) {
                        // ... but wouldn't it be nice to trigger error via generator? Alas,
                        // no method to do that. So we'll do...
                        final String errorMsg = String.format(
                                "Attempt to write plain `java.math.BigDecimal` (see JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN) with illegal scale (%d): needs to be between [-%d, %d]",
                                bd.scale(), MAX_BIG_DECIMAL_SCALE, MAX_BIG_DECIMAL_SCALE);
                        provider.reportMappingProblem(errorMsg);
                    }
//                    text = bd.toPlainString();
                    gen.writeNumber(bd);
                } else {
                    gen.writeString(value.toString());
                }
            }
            // 24-Aug-2016, tatu: [core#315] prevent possible DoS vector, so we need this
            protected boolean _verifyBigDecimalRange(JsonGenerator gen, BigDecimal value) throws IOException {
                int scale = value.scale();
                return ((scale >= -MAX_BIG_DECIMAL_SCALE) && (scale <= MAX_BIG_DECIMAL_SCALE));
            }
        };
    }

    /**
     * localDatetime格式化
     * @return
     */
    @Bean
    public LocalDateTimeSerializer localDateTimeSerializer(){
        return new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(pattern));
    }

    ///**
    // * LocalTime等的格式化
    // * @return
    // */
    //@Bean
    //public ObjectMapper objectMapper() {
    //    ObjectMapper objectMapper = new ObjectMapper();
    //    objectMapper.registerModule(new JavaTimeModule());
    //    return objectMapper;
    //}


    /**
     * localDatetime格式化
     * @return
     */
    @Bean
    public JsonDeserializer<LocalDateTime> localDateTimeDeserializer(){
        final String pattern = this.pattern;
        return new JsonDeserializer<LocalDateTime>(){
            @SneakyThrows
            @Override
            public LocalDateTime deserialize(JsonParser p, DeserializationContext context) throws IOException {
                String value = p.getText();
                if (StrUtil.isEmpty(value)) {
                    return null;
                }
                Class<?> aClass = p.getCurrentValue().getClass();
                Field field = ReflectUtil.getField(aClass, p.getCurrentName());
                String format = pattern;
                JsonFormat annotation = field.getAnnotation(JsonFormat.class);
                if (annotation != null) {
                    format = StringUtils.defaultIfEmpty(annotation.pattern(), pattern);
                }
                return LocalDateTimeUtil.parseDate(value, format);
            }
        };
    }

    @Bean
    public Converter<String, LocalDateTime> stringToLocalDateTimeConverter() {
        /** EasyExcel时间格式配置 */
        EasyExcelLocalDateTimeConverter dateTimeConverter = new EasyExcelLocalDateTimeConverter();
        DefaultConverterLoader.loadDefaultReadConverter().put(ConverterKeyBuild.buildKey(dateTimeConverter.supportJavaTypeKey(), dateTimeConverter.supportExcelTypeKey()), dateTimeConverter);
        DefaultConverterLoader.loadDefaultWriteConverter().put(ConverterKeyBuild.buildKey(dateTimeConverter.supportJavaTypeKey(), dateTimeConverter.supportExcelTypeKey()), dateTimeConverter);
        DefaultConverterLoader.loadDefaultWriteConverter().put(ConverterKeyBuild.buildKey(dateTimeConverter.supportJavaTypeKey()), dateTimeConverter);
        EasyExcelLocalTimeConverter timeConverter = new EasyExcelLocalTimeConverter();
        DefaultConverterLoader.loadDefaultReadConverter().put(ConverterKeyBuild.buildKey(timeConverter.supportJavaTypeKey(), timeConverter.supportExcelTypeKey()), timeConverter);
        DefaultConverterLoader.loadDefaultWriteConverter().put(ConverterKeyBuild.buildKey(timeConverter.supportJavaTypeKey(), timeConverter.supportExcelTypeKey()), timeConverter);
        DefaultConverterLoader.loadDefaultWriteConverter().put(ConverterKeyBuild.buildKey(timeConverter.supportJavaTypeKey()), timeConverter);
        /***/
        return new Converter<String, LocalDateTime>() {
            @Override
            public LocalDateTime convert(String value) {
                return LocalDateTimeUtil.parseDateByLength(value);
            }
        };
    }

    @Bean
    public Converter<String, LocalTime> stringToLocalTimeConverter() {
        return new Converter<String, LocalTime>(){

            @Override
            public LocalTime convert(String value) {
                if (StrUtil.isNotBlank(value)) {
                    return LocalTime.parse(value);
                }
                return null;
            }
        };
    }
    public static class CustomDateDeserializer extends JsonDeserializer<Date> {

        public CustomDateDeserializer() {
        }

        @Override
        public Date deserialize(JsonParser p, DeserializationContext ctxt)
                throws java.io.IOException, com.fasterxml.jackson.core.JsonProcessingException {
            String date = p.getText();
            try {
                if (StringUtils.isNotBlank(date)){
                    // 首先尝试解析时间戳格式
                    if (isTimestamp(date)) {
                        try {
                            long timestamp = Long.parseLong(date);
                            return new Date(timestamp);
                        } catch (NumberFormatException e) {
                            // 如果时间戳解析失败，继续尝试其他格式
                        }
                    }

                    // 尝试解析日期时间格式
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    try {
                        Date parse = dateFormat.parse(date);
                        return parse;
                    } catch (ParseException e) {
                        // 尝试解析日期格式
                        dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                        Date parse = dateFormat.parse(date);
                        return parse;
                    }
                }else {
                    return null;
                }
            } catch (java.text.ParseException e) {
                throw new RuntimeException(e);
            }
        }

        /**
         * 判断字符串是否为时间戳格式
         * @param str 待判断的字符串
         * @return true表示是时间戳格式
         */
        private boolean isTimestamp(String str) {
            if (StringUtils.isBlank(str)) {
                return false;
            }
            // 时间戳通常是10位（秒）或13位（毫秒）的数字
            return str.matches("^\\d{10}$") || str.matches("^\\d{13}$");
        }
    }
}
