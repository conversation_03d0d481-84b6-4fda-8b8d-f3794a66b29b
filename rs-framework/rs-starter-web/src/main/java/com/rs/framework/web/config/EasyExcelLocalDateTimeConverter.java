package com.rs.framework.web.config;

import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rs.framework.web.utils.LocalDateTimeUtil;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Component
public class EasyExcelLocalDateTimeConverter implements Converter<LocalDateTime> {

   @Override
   public Class<LocalDateTime> supportJavaTypeKey() {
       return LocalDateTime.class;
   }

   @Override
   public CellDataTypeEnum supportExcelTypeKey() {
       return CellDataTypeEnum.STRING;
   }

   @Override
   public LocalDateTime convertToJavaData(ReadCellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
       return LocalDateTimeUtil.parseDate(cellData.getStringValue(), getFormat(contentProperty));
   }

   @Override
   public WriteCellData<LocalDateTime> convertToExcelData(LocalDateTime value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
       return new WriteCellData<>(value.format(DateTimeFormatter.ofPattern(getFormat(contentProperty))));
   }

   private String getFormat(ExcelContentProperty contentProperty) {
       JsonFormat annotation = contentProperty.getField().getAnnotation(JsonFormat.class);
       String format = DatePattern.NORM_DATETIME_PATTERN;
       if (annotation != null) {
           format = annotation.pattern();
       }
       return format;
   }
}
