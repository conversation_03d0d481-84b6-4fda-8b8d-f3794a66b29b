package com.rs.framework.web.config;


import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.util.Map;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.ActiveSpan;

import com.bsp.common.cache.RedisClient;
import com.rs.framework.common.exception.ServiceException;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.servlet.ServletUtils;
import com.rs.framework.web.core.handler.GlobalExceptionHandlerX;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.log4j.Log4j2;

/**
 * XSS过滤处理
 *
 */
@Log4j2
public class XssHttpServletRequestWrapper extends HttpServletRequestWrapper {
    HttpServletResponse response;
    /**
     * 没被包装过的HttpServletRequest（特殊场景，需要自己过滤）
     */
    private final byte[] body;
    public XssHttpServletRequestWrapper(HttpServletRequest request, Map<String, Object> parameterMap,HttpServletResponse response) {
        super(request);
        this.response = response;
        BufferedReader reader = null;
        try {
            reader = request.getReader();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        StringWriter writer = new StringWriter();
        char[] buf = new char[8192];

        int read;
        while(true) {
            try {
                if (!((read = reader.read(buf)) != -1)) {
                    break;
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            writer.write(buf, 0, read);
        }

        this.body = writer.getBuffer().toString().getBytes();
        log.info("body:{}", new String(this.body));
        ActiveSpan.info("body"+ new String(this.body));
        String bodyString = new String(this.body);
        if (StringUtils.isNotBlank(bodyString) && !bodyString.trim().startsWith("[")) {
        	try {
        		String tokenFromQueryString = HttpUtils.getTokenFromQueryString(request.getQueryString());
                if (StringUtils.isNotBlank(tokenFromQueryString) &&
                        (request.getRequestURL().toString().endsWith("create")
                        )) {
                    tokenFromQueryString = "repeat:" + tokenFromQueryString + ":" + DigestUtils.md5Hex(bodyString);
                    Boolean exists = RedisClient.exists(tokenFromQueryString);
                    if (exists) {
                        ServiceException exception = new ServiceException(500, "请勿重复提交");
                        GlobalExceptionHandlerX globalExceptionHandler = SpringUtil.getBean(GlobalExceptionHandlerX.class);
                        CommonResult<?> result = globalExceptionHandler.allExceptionHandler(request, exception);
                        ServletUtils.writeJSON(response, result);
                        return;
                    }else {
                        RedisClient.set(tokenFromQueryString, "", 10);
                    }
                }
        	}
        	catch(Exception e) {
        		throw new RuntimeException(e);
        	}
        }
    }

    public String getBody() {
        return new String(this.body, StandardCharsets.UTF_8);
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(this.body);
        return new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return false;
            }
            @Override

            public boolean isReady() {
                return false;
            }
            @Override

            public void setReadListener(ReadListener readListener) {
            }
            @Override

            public int read() {
                return byteArrayInputStream.read();
            }
        };
    }
    @Override

    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(this.getInputStream()));
    }


}
