
package com.rs.framework.web.core.filter;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSON;
import com.bsp.common.cache.RedisClient;
import com.bsp.common.util.StringUtil;
import com.bsp.sdk.log.model.LogOper;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.mybatis.context.ParamterContextHolder;
import com.rs.framework.web.config.StartupRunner;
import com.rs.framework.web.config.XssHttpServletRequestWrapper;
import com.rs.framework.web.core.context.LogContextHolder;
import com.rs.framework.web.core.vo.PathsVO;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.ActiveSpan;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * XSS过滤
 *
 * <AUTHOR>
 */
@Log4j2
public class XssFilter implements Filter {




    @Override
    public void init(FilterConfig config) {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        ParamterContextHolder.clearContext();
        LogContextHolder.clearContext();

        try {
            SessionUser user = SessionUserUtil.getSessionUser();
            ActiveSpan.info("用户:" + user.getIdCard());
            log.info("请求用户:" + JSON.toJSONString(user));

        } catch (Exception e) {

        }
        HttpServletRequest httpRequest = (HttpServletRequest) request;



        log.info("路径:" + httpRequest.getRequestURL());
        Map<String, Object> paramterMap = new LinkedHashMap<>();

        if (StringUtils.isNotBlank(httpRequest.getQueryString())) {
            ActiveSpan.info("QueryString:" + httpRequest.getQueryString());
            log.info("QueryString:" + httpRequest.getQueryString());
            // httpRequest.getQueryString() 转 map
            Map<String, String> queryMap = new LinkedHashMap<>();
            for (String str : httpRequest.getQueryString().split("&")) {
                String[] arr = str.split("=");
                if (arr.length == 2) {
                    queryMap.put(arr[0], arr[1]);
                }
            }
            paramterMap.putAll(queryMap);

        }
        //form 表单转成 字符串，排除文件
        Map<String, String[]> parameterMap = httpRequest.getParameterMap();
        Map<String, String> formMap = new LinkedHashMap<>();
        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            String key = entry.getKey();
            if (entry.getValue().length > 0) {
                formMap.put(key, StringUtils.join(entry.getValue(), ","));
            } else {
                formMap.put(key, "");
            }
        }
        if (!formMap.isEmpty()) {
            paramterMap.putAll(formMap);
        }
        ActiveSpan.info("form:" + JSON.toJSONString(formMap));
        log.info("QueryString:" + JSON.toJSONString(formMap));

        ParamterContextHolder.setContext(paramterMap);
        String bodyString = "";
        boolean isJsonBodyRequest = "application/json".equals(httpRequest.getContentType()) || "application/json;charset=UTF-8".equals(httpRequest.getContentType());
        if (httpRequest instanceof HttpServletRequest && isJsonBodyRequest) {
            XssHttpServletRequestWrapper requestWrapper = new XssHttpServletRequestWrapper((HttpServletRequest) httpRequest, paramterMap, (HttpServletResponse) response);
            bodyString = requestWrapper.getBody();
            chain.doFilter(requestWrapper, response);
        } else {
            chain.doFilter(request, response);
        }
        try {
            /**
             * 添加操作日志
             */
            PathsVO pathsVO = StartupRunner.pathsMap.get(httpRequest.getRequestURI());
            LogOper logOper = LogContextHolder.getContext();
            if (pathsVO != null && logOper != null) {
                SessionUser user = SessionUserUtil.getSessionUser();
                String userId = user.getId() + bodyString;
                String md5 = DigestUtil.md5Hex(userId);
                boolean isMd5Exists = RedisClient.exists(md5);
                if (!isMd5Exists) {
                    RedisClient.set(md5, "1", 10);
                    String addTime = logOper.getAddTime();
                    DateTime dateTime = DateUtil.parse(addTime);
                    logOper.setOperateDuration(DateUtil.between(dateTime, DateUtil.date(), DateUnit.MS));
                    logOper.setId(StringUtil.getGuid32());
                    logOper.setUserId(user.getId());
                    logOper.setLoginId(user.getLoginId());
                    logOper.setUserName(user.getName());
                    logOper.setIdCard(user.getIdCard());
                    logOper.setUserAgent(httpRequest.getHeader("User-Agent"));
                    logOper.setTerminalType(0);
                    logOper.setAddTime(DateUtil.now());
                    logOper.setOperateUrl(httpRequest.getRequestURI());
                    logOper.setOperateTime(DateUtil.now());
                    logOper.setOrgCode(user.getOrgCode());
                    logOper.setOrgName(user.getOrgName());
                    logOper.setSystemMark(HttpUtils.getAppCode());
                    logOper.setOperateType(pathsVO.getOperateType());
                    logOper.setTitle(pathsVO.getSummary());
                    logOper.setContent(pathsVO.getSummary());
                    logOper.setContent(pathsVO.getSummary());
                    logOper.setOperateTime(DateUtil.now());
                    logOper.setStartTime(System.currentTimeMillis());
                    logOper.setOperateCondition(JSON.toJSONString(formMap));
                    logOper.setReqParams(bodyString);
                    try {
                        logOper.setTerminalId(ServletUtil.getClientIP((HttpServletRequest) request));
                    } catch (Exception e) {
                    }

                    logOper.send();
                }

            }
        } catch (Exception e) {
        }

    }

    @Override
    public void destroy() {
    }


}
