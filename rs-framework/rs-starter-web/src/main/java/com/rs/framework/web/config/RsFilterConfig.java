package com.rs.framework.web.config;

import com.rs.framework.web.core.filter.XssFilter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.DispatcherType;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName FilterConfig
 * <AUTHOR>
 * @Date 2025/3/17 10:11
 * @Version 1.0
 */
@Configuration
public class RsFilterConfig {

    @Value("${as.log.ignore:}")
    String ignoreUrls;
    @Value("${conf.matchers.ignores:}")
    String ignores;
    @Bean
    public FilterRegistrationBean<XssFilter> xssFilterRegistration() {
        FilterRegistrationBean<XssFilter> registration = new FilterRegistrationBean<>();
        registration.setDispatcherTypes(DispatcherType.REQUEST);
        registration.setFilter(new XssFilter());
        registration.addUrlPatterns("/*");
        registration.setName("xssFilter");
        registration.setOrder(Integer.MAX_VALUE);

        Map<String, String> initParameters = new HashMap<>();

        //设置xxs 忽略地址
        String excludes = ignoreUrls;
        if (StringUtils.isNotEmpty(excludes)) {
            excludes += ","+ignores;
        } else {
            excludes = ignores;
        }
        initParameters.put("excludes", excludes);
        registration.setInitParameters(initParameters);
        return registration;
    }
}
