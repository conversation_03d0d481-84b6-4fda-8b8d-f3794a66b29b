package com.rs.framework.web.core.listener;

import org.springframework.boot.web.server.WebServer;
import org.springframework.boot.web.servlet.context.ServletWebServerInitializedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
public class PortListener implements ApplicationListener<ServletWebServerInitializedEvent> {

    private int port;

    @Override
    public void onApplicationEvent(ServletWebServerInitializedEvent event) {
        WebServer webServer = event.getWebServer();
        this.port = webServer.getPort();
    }

    public int getPort() {
        return port;
    }
}
