package com.rs.framework.web.config;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.Contact;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.*;

@Configuration
@EnableSwagger2WebMvc
public class Knife4jConfig {
    @Value("${bsp.token.url:http://*************:999/bsp-uac/oauth/token}")
    private String url;

    @Bean(value = "swaggerApi")
    public Docket swaggerApi() {
        List<Parameter> pars = new ArrayList<>();
        Map<String, Object> user = new HashMap<>();
        user.put("username", "admin");
        user.put("password", "y93bjoQhsjSYSAVw19dTMFOKaIL139w7ZBFcZH8zKMQ=");
        user.put("client_id", "user_client");
        user.put("client_secret", "user_client");
        user.put("scope", "trust");
        user.put("grant_type", "password");
        user.put("app_mark", "bsp");
        try {
            //获取本机ip
            String localIpAddress = getLocalIpAddress();
            if (localIpAddress.contains("192.168.3")) {
                String post = HttpUtil.post(url, user, 5000);
                JSONObject jsonObject = JSONObject.parseObject(post);
                ParameterBuilder tokenPar = new ParameterBuilder();
                tokenPar.name("access_token").description("令牌").defaultValue(jsonObject.getString("access_token")).modelRef(new ModelRef("string")).parameterType("query").required(false).build();
                pars.add(tokenPar.build());
            }
        } catch (Exception e) {

        }

        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(new ApiInfoBuilder()
                        .title("RESTful APIs")
                        .description("#RESTful APIs")
                        .termsOfServiceUrl("https://www.gxx.com")
                        .contact(new Contact("高新兴", "www.gxx.com", "<EMAIL>"))
                        .version("1.0")
                        .build())
                //分组名称
                .groupName("1.0版本")
                .select()
                //这里指定Controller扫描包路径
                .apis(RequestHandlerSelectors.basePackage("com.rs.module")
                        .or(RequestHandlerSelectors.basePackage("com.rs.adapter")
                                .or(RequestHandlerSelectors.basePackage("com.gosun")
                                .or(RequestHandlerSelectors.basePackage("com.gosuncn")
                                ))))
                .paths(PathSelectors.any())
                .build()
                .globalOperationParameters(pars);
    }

    public static String getLocalIpAddress() {
        try {
            InetAddress localhost = InetAddress.getLocalHost();
            if (!localhost.getHostAddress().equals("127.0.0.1")) {
                return localhost.getHostAddress();
            }
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }

        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface ni = interfaces.nextElement();
                Enumeration<InetAddress> addresses = ni.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress addr = addresses.nextElement();
                    if (addr != null
                            && addr.getHostAddress() != null
                            && !addr.isLoopbackAddress()
                            && addr.getAddress().length == 4) { // 只获取IPv4地址
                        return addr.getHostAddress();
                    }
                }
            }
        } catch (SocketException e) {
            e.printStackTrace();
        }
        return "无法获取IP地址";
    }

}
