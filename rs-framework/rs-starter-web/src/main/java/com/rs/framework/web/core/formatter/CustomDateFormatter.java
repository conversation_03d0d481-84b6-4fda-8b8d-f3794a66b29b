package com.rs.framework.web.core.formatter;

import org.springframework.format.Formatter;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class CustomDateFormatter implements Formatter<Date> {

    private static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final String DATE_PATTERN = "yyyy-MM-dd";

    private final SimpleDateFormat dateTimeFormat = new SimpleDateFormat(DATE_TIME_PATTERN);
    private final SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_PATTERN);

    @Override
    public Date parse(String text, Locale locale) throws ParseException {
        try {
            return dateTimeFormat.parse(text);
        } catch (ParseException e) {
            return dateFormat.parse(text);
        }
    }

    @Override
    public String print(Date object, Locale locale) {
        if (object.getHours() == 0 && object.getMinutes() == 0 && object.getSeconds() == 0) {
            return dateFormat.format(object);
        } else {
            return dateTimeFormat.format(object);
        }
    }
}

