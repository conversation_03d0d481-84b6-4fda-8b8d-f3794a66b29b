package com.rs.framework.web.core.context;

import com.bsp.sdk.log.model.LogOper;

/**
 * @ClassName ContextHolder
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/26 21:47
 * @Version 1.0
 */
public class LogContextHolder {

    // 使用ThreadLocal存储上下文信息
    private static final ThreadLocal<LogOper> context = new ThreadLocal<>();

    // 设置上下文
    public static void setContext(LogOper value) {
        context.set(value);
    }

    // 获取上下文
    public static LogOper getContext() {
        return context.get();
    }

    // 清除上下文
    public static void clearContext() {
        context.remove();
    }
}
