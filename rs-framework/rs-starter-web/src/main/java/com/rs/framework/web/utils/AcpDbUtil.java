package com.rs.framework.web.utils;

import cn.hutool.db.Db;
import com.rs.framework.mybatis.config.GlobalConstant;
import com.rs.framework.mybatis.util.DatasourceUtil;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;

/**
 * @ClassName UacUerUtil
 * <AUTHOR>
 * @Date 2025/3/28 14:20
 * @Version 1.0
 */
@Slf4j
public class AcpDbUtil {

    /**
     * @return
     */

    public static Db getDb() {
        DataSource ds = DatasourceUtil.getDataSource(GlobalConstant.DEFAULT_DATASOURCE_KEY);
        Db use = Db.use(ds);
        return use;
    }

}
