package com.rs.framework.web.core.handler;

import cn.hutool.core.date.DateUtil;
import com.bsp.sdk.log.LogClient;
import com.bsp.sdk.log.model.LogOper;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.web.core.context.LogContextHolder;
import com.rs.framework.web.core.util.WebFrameworkUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * 全局响应结果（ResponseBody）处理器
 * <p>
 * 不同于在网上看到的很多文章，会选择自动将 Controller 返回结果包上 {@link CommonResult}，
 * 在 onemall 中，是 Controller 在返回时，主动自己包上 {@link CommonResult}。
 * 原因是，GlobalResponseBodyHandler 本质上是 AOP，它不应该改变 Controller 返回的数据结构
 * <p>
 * 目前，GlobalResponseBodyHandler 的主要作用是，记录 Controller 的返回结果，
 */
@SuppressWarnings("rawtypes")
@ControllerAdvice
@Log4j2
public class GlobalResponseBodyHandler implements ResponseBodyAdvice {

    @Override
    public boolean supports(MethodParameter returnType, Class converterType) {
        if (returnType.getMethod() == null) {
            return false;
        }
        // 只拦截返回结果为 CommonResult 类型
        return isGrandChildOfCommonResult(returnType.getMethod().getReturnType());
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class selectedConverterType,
                                  ServerHttpRequest request, ServerHttpResponse response) {
        // 记录 Controller 结果
        WebFrameworkUtils.setCommonResult(((ServletServerHttpRequest) request).getServletRequest(), (CommonResult<?>) body);
        try {
            LogOper logOper = LogClient.build().operLogBuilder();
            logOper.setAddTime(DateUtil.now());
            LogContextHolder.setContext(logOper);
        } catch (Exception e) {
            log.error("[beforeBodyWrite][记录 Controller 结果时，发生异常，其中 body: {}，response: {}]", body, response, e);
        }
        return body;
    }

    public static boolean isGrandChildOfCommonResult(Class<?> clazz) {
        if (clazz == null || clazz == Object.class) {
            return false;
        }
        if (clazz == CommonResult.class) {
            return true;
        }
        Class<?> superClass = clazz.getDeclaringClass();
        while (superClass != null && superClass != Object.class) {
            if (superClass == CommonResult.class) {
                return true;
            }
            superClass = superClass.getSuperclass();
        }
        return false;
    }
}
