package com.rs.framework.web.config;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.rs.framework.web.utils.LocalDateTimeUtil;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.LocalTime;

@Component
public class EasyExcelLocalTimeConverter implements Converter<LocalTime> {

   @Override
   public Class<LocalTime> supportJavaTypeKey() {
       return LocalTime.class;
   }

   @Override
   public CellDataTypeEnum supportExcelTypeKey() {
       return CellDataTypeEnum.STRING;
   }

   @Override
   public LocalTime convertToJavaData(ReadCellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
       return LocalDateTimeUtil.parseTime(cellData.getStringValue());
   }

   @Override
   public WriteCellData<LocalDateTime> convertToExcelData(LocalTime value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
       return new WriteCellData<>(LocalDateTimeUtil.format(value));
   }
}
