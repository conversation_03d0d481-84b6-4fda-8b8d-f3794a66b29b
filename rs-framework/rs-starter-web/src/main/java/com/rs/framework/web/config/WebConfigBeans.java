package com.rs.framework.web.config;


import com.rs.framework.web.core.formatter.CustomDateFormatter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


@Configuration
public class WebConfigBeans implements WebMvcConfigurer {

	@Bean
	public CustomDateFormatter customDateFormatter() {
		return new CustomDateFormatter();
	}

	@Override
	public void addFormatters(FormatterRegistry registry) {
		registry.addFormatter(customDateFormatter());
	}




}
