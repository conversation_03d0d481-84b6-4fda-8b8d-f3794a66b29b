package ${packageName};

/**
* 系统字典枚举类
*/
public enum ${enumName} {
<#list dictList as dict>
    /**
    * ${dict.comment}
    */
${dict.dictCode}("${dict.dictName}", "${dict.name}", "${dict.value}")<#if dict_has_next>,</#if>
</#list>;

private final String dictName;
private final String name;
private final String value;

${enumName}(String dictName, String name, String value) {
this.dictName = dictName;
this.name = name;
this.value = value;
}

public String getDictName() {
return dictName;
}

public String getName() {
return name;
}

public String getValue() {
return value;
}
}
