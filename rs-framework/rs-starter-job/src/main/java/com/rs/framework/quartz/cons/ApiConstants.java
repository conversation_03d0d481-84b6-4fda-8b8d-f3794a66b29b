package com.rs.framework.quartz.cons;

/**
 * xxl-job常量类
 * <AUTHOR>
 * @date 2025年6月14日
 */
public class ApiConstants {

    /** ###################### 任务 ####################  **/


    /**
     * 任务
     */
    public static final String JOB_PREFIX = "/jobinfo";

    /**
     * 获取任务列表
     */
    public static final String JOB_PAGE_LIST = JOB_PREFIX + "/pageList";

    /**
     * 启动
     */
    public static final String START_JOB_URL = JOB_PREFIX + "/start";

    /**
     * 新增
     */
    public static final String ADD_JOB_URL = JOB_PREFIX + "/add";

    /**
     * 更新
     */
    public static final String UPDATE_JOB_URL = JOB_PREFIX + "/update";

    /**
     * 删除
     */
    public static final String REMOVE_JOB_URL = JOB_PREFIX + "/remove";

    /**
     * 停止
     */
    public static final String STOP_JOB_URL = JOB_PREFIX + "/stop";

    /**
     * 执行一次
     */
    public static final String TRIGGER_JOB_URL = JOB_PREFIX + "/trigger";

    /**
     * 获取执行时间
     */
    public static final String NEXT_TRIGGER_TIME = JOB_PREFIX + "/nextTriggerTime";

    /** ###################### 执行器 ####################  **/

    /**
     * 执行器
     */
    public static final String EXECUTOR_PREFIX = "/jobgroup";

    /**
     * 获取执行器列表
     */
    public static final String EXECUTOR_PAGE_LIST = EXECUTOR_PREFIX + "/pageList";

    /** ###################### 日志 ####################  **/

    /**
     * 任务
     */
    public static final String LOG_PREFIX = "/joblog";

    /**
     * 获取执行日志列表
     */
    public static final String LOG_PAGE_LIST = LOG_PREFIX + "/pageList";

    /**
     * 获取执行日志
     */
    public static final String LOG_DETAIL_CAT = LOG_PREFIX + "/logDetailCat";

    /**
     * 终止
     */
    public static final String KILL_JOB_URL = LOG_PREFIX + "/logKill";
}
