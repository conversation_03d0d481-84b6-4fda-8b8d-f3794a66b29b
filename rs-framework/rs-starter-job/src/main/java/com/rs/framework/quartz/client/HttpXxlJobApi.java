package com.rs.framework.quartz.client;

import java.util.HashMap;
import java.util.Map;

import com.bsp.common.util.StringUtil;
import com.rs.framework.quartz.config.XxlJobProperties;
import com.rs.framework.quartz.cons.ApiConstants;
import com.rs.framework.quartz.entity.LogResult;
import com.rs.framework.quartz.entity.XxlJobInfo;
import com.xxl.job.core.biz.model.ReturnT;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * xxl-job api接口
 * <AUTHOR>
 * @date 2025年6月14日
 */
@Slf4j
public class HttpXxlJobApi {

	private XxlJobProxy xxlJobProxy;

    public HttpXxlJobApi(XxlJobProperties properties) {
        this.xxlJobProxy = new XxlJobProxy(properties);
    }

    public XxlJobProxy getXxlJobProxy() {
        return xxlJobProxy;
    }
    
    /* ############# 任务基本操作 ########### */
    
    /**
     * 新增任务
     * @param jobInfo XxlJobInfo 任务信息
     * @return ReturnT<String>
     */
    public ReturnT<String> add(XxlJobInfo jobInfo) {
    	return execute(jobInfo, ApiConstants.ADD_JOB_URL, "新增任务");
    }
    
    /**
     * 新增任务
     * @param jobInfo XxlJobInfo 任务信息
     * @return ReturnT<String>
     */
    public ReturnT<String> update(XxlJobInfo jobInfo) {
    	return execute(jobInfo, ApiConstants.UPDATE_JOB_URL, "更新任务");
    }
    
    /**
     * 删除任务
     * @param id Integer 任务Id
     * @return ReturnT<String>
     */
    public ReturnT<String> remove(Integer id) {
    	return execute(id, ApiConstants.REMOVE_JOB_URL, "删除任务");
    }
    
    /**
     * 启动任务
     * @param id Integer 任务Id
     * @return ReturnT<String>
     */
    public ReturnT<String> start(Integer id) {
    	return execute(id, ApiConstants.START_JOB_URL, "启动任务");
    }
    
    /**
     * 停止任务
     * @param id Integer 任务Id
     * @return ReturnT<String>
     */
    public ReturnT<String> stop(Integer id) {
    	return execute(id, ApiConstants.STOP_JOB_URL, "停止任务");
    }
    
    /**
     * 执行一次
     * @param id Integer 任务Id
     * @param executorParam String 执行参数(默认1次)
     * @param addressList String 执行器列表
     * @return ReturnT<String>
     */
    public ReturnT<String> triggerJob(int id, String executorParam, String addressList) {
        Map<String, Object> param = new HashMap<>(0);
        param.put("id", id);
        param.put("executorParam", executorParam == null ? "1" : executorParam);
        param.put("addressList", addressList);

        return execute(param, ApiConstants.TRIGGER_JOB_URL, "执行任务");
    }
    
    /**
     * 获取下次执行时间
     * @param scheduleType String 调度类型
     * @param scheduleConf String 调度表达式
     * @return ReturnT<String>
     */
    public ReturnT<String> nextTriggerTime(String scheduleType, String scheduleConf) {
        Map<String, Object> param = new HashMap<>(0);
        param.put("scheduleType", scheduleType);
        param.put("scheduleConf", scheduleConf);

        return execute(param, ApiConstants.NEXT_TRIGGER_TIME, "获取执行时间");
    }
    
    /* ############# 执行器 ########### */
    
    /**
     * 获取执行器列表
     * @param start int 起始值
     * @param length int 长度
     * @param appname String 应用名称
     * @param title String 标题
     * @return Map<String, Object>
     */
    public Map<String, Object> jobGroupPageList(int start, int length, String appname, String title) {
        Map<String, Object> param = new HashMap<>(0);
        param.put("start", start);
        param.put("length", length);
        param.put("appname", appname);
        param.put("title", title);
        
        return executeMap(param, ApiConstants.EXECUTOR_PAGE_LIST, "获取执行器列表");
    }
    
    /* ############# 任务 ########### */
    
    /**
     * 获取任务列表
     * @param start int 起始值
     * @param length int 长度
     * @param jobGroup int 执行器
     * @param jobDesc String 任务描述
     * @return Map<String, Object>
     */
    public Map<String, Object> jobPageList(int start, int length, int jobGroup, String jobDesc) {
        Map<String, Object> param = new HashMap<>(0);
        param.put("start", start);
        param.put("length", length);
        param.put("jobGroup", jobGroup);
        param.put("jobDesc", jobDesc);
        param.put("triggerStatus", "-1");
        
        return executeMap(param, ApiConstants.JOB_PAGE_LIST, "获取任务列表");
    }
    
    /**
     * 判断任务是否存在
     * @param jobGroup int 执行器
     * @param executorHandler String 任务处理器
     * @return boolean
     */
    public boolean isJobExist(int jobGroup, String executorHandler) {
    	Map<String, Object> param = new HashMap<>(0);
        param.put("start", 0);
        param.put("length", 10);
        param.put("jobGroup", jobGroup);
        param.put("executorHandler", executorHandler);
        param.put("triggerStatus", "-1");
        
    	Map<String, Object> jobMap = executeMap(param, ApiConstants.JOB_PAGE_LIST, "获取任务列表");
    	String recordsTotal = String.valueOf(jobMap.get("recordsTotal"));
    	if(StringUtil.isNotEmpty(recordsTotal)) {
    		try {
    			if(Integer.valueOf(recordsTotal) > 0) {
        			log.error(buildJobMsg("任务已存在：jobGroup={}, executorHandler={}"), jobGroup, executorHandler);
        			return true;
        		}
    		}
    		catch(Exception e) {
    			log.error(buildJobMsg("查询任务列表失败：{}"), e.getMessage());
    		}    		
    	}
    	else {
    		log.error(buildJobMsg("查询任务列表失败：{}"), jobMap.get("msg"));
    	}
    	
    	return false;
    }
    
    /* ############# 日志 ########### */
    
    /**
     * 获取日志列表
     * @param start int 起始值
     * @param length int 长度
     * @param jobGroup int 执行器
     * @param jobId int 任务Id
     * @param logStatus int 任务状态
     * @param filterTime String 过滤时间
     * @return Map<String, Object>
     */
    public Map<String, Object> logPageList(int start, int length, int jobGroup, int jobId,
            int logStatus, String filterTime) {
		Map<String, Object> param = new HashMap<>(0);
		param.put("start", start);
		param.put("length", length);
		param.put("jobGroup", jobGroup);
		param.put("jobId", jobId);
		param.put("logStatus", logStatus);
		param.put("filterTime", filterTime);
		
		return executeMap(param, ApiConstants.LOG_PAGE_LIST, "获取日志列表");
	}
    
    /**
     * 获取日志详情
     * @param logId long 日志Id
     * @param fromLineNum int 起始行号
     * @return ReturnT<LogResult>
     */
    public ReturnT<LogResult> logDetailCat(long logId, int fromLineNum) {
    	ReturnT<LogResult> result = null;
    	
        Map<String, Object> param = new HashMap<>(0);
        param.put("logId", logId);
        param.put("fromLineNum", fromLineNum);
        
        try {
            result = xxlJobProxy.httpPost(ApiConstants.LOG_DETAIL_CAT, param, new TypeReference<ReturnT<LogResult>>() {}.getType());
        }
        catch (Exception e) {
            log.error(buildJobMsg("获取日志详情获取失败：{}"), e);
            result = new ReturnT<>(500, ExceptionUtil.getMessage(e));
        }
        
        return result;
    }
    
    /**
     * 终止任务
     * @param logId long 任务Id
     * @return ReturnT<String>
     */
    public ReturnT<String> logKill(long logId) {
        Map<String, Object> param = new HashMap<>(0);
        param.put("id", logId);
        
        return execute(param, ApiConstants.KILL_JOB_URL, "终止任务");
    }
    
    /**
     * 执行任务
     * @param jobInfo XxlJobInfo 任务信息
     * @param executeUrl String 执行地址
     * @param operName String 操作名称
     * @return ReturnT<String>
     */
    private ReturnT<String> execute(XxlJobInfo jobInfo, String executeUrl, String operName) {
    	Map<String, Object> param = BeanUtil.beanToMap(jobInfo);
    	return execute(param, executeUrl, operName);
    }
    
    /**
     * 执行任务
     * @param id Integer 任务Id
     * @param executeUrl String 执行地址
     * @param operName String 操作名称
     * @return ReturnT<String>
     */
    private ReturnT<String> execute(Integer id, String executeUrl, String operName) {
    	Map<String, Object> param = MapUtil.of("id", id);
    	return execute(param, executeUrl, operName);
    }
    
    /**
     * 执行任务
     * @param param Map<String, Object> 任务参数
     * @param executeUrl String 执行地址
     * @param operName String 操作名称
     * @return ReturnT<String>
     */
    private ReturnT<String> execute(Map<String, Object> param, String executeUrl, String operName) {
        ReturnT<String> result = null;
        
        try {
            result = xxlJobProxy.httpPost(executeUrl, param, ReturnT.class);
        }
        catch (Exception e) {
            log.error(buildJobMsg(operName + "失败：{}"), e);
            result = new ReturnT<>(500, ExceptionUtil.getMessage(e));
        }
        
        return result;
    }
    
    /**
     * 执行任务
     * @param param Map<String, Object> 任务参数
     * @param executeUrl String 执行地址
     * @param operName String 操作名称
     * @return ReturnT<String>
     */
    private Map<String, Object> executeMap(Map<String, Object> param, String executeUrl, String operName) {
    	Map<String, Object> result = null;
        
        try {
            result = xxlJobProxy.httpPost(executeUrl, param, Map.class);
        }
        catch (Exception e) {
            log.error(buildJobMsg(operName + "失败：{}"), e);
        }
        
        return result;
    }
    
    /**
     * 构建job提示信息
     * @param msg String 提示信息
     * @return String
     */
    private String buildJobMsg(String msg) {
        return "【xxl-job】提示: " + msg;
    }
}
