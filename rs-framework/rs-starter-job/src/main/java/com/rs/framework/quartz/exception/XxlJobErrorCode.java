package com.rs.framework.quartz.exception;

import com.rs.framework.common.exception.ErrorCode;

/**
 * xxl-job异常代码
 * <AUTHOR>
 * @date 2025年6月14日
 */
public interface XxlJobErrorCode {


    // ========== 客户端错误段 ==========

    ErrorCode XXL_BAD_REQUEST = new ErrorCode(400, "请求参数不正确");
    
    ErrorCode XXL_UNAUTHORIZED = new ErrorCode(401, "xxl-job账号未登录");
    
    ErrorCode XXL_INTERNAL_SERVER_ERROR = new ErrorCode(500, "系统异常");
    
    ErrorCode XXL_NETWORK_ERROR = new ErrorCode(501, "网络异常，请稍后重试");
}
