package com.rs.framework.quartz.config;

import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.rs.framework.quartz.client.HttpXxlJobApi;
import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;

import lombok.extern.slf4j.Slf4j;

/**
 * XXL-Job 自动配置类
 *
 * <AUTHOR>
 */
@EnableAutoConfiguration
@ConditionalOnClass(XxlJobSpringExecutor.class)
@ConditionalOnProperty(prefix = "xxl.job", name = "enabled", havingValue = "true", matchIfMissing = true)
@Configuration
@EnableConfigurationProperties({XxlJobProperties.class})
@EnableScheduling // 开启 Spring 自带的定时任务
@Slf4j
public class RsXxlJobAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public XxlJobExecutor xxlJobExecutor(XxlJobProperties properties) {
        log.info("[xxlJobExecutor][初始化 XXL-Job 执行器的配置]");
        XxlJobProperties.AdminProperties admin = properties.getAdmin();
        XxlJobProperties.ExecutorProperties executor = properties.getExecutor();

        // 初始化执行器
        XxlJobExecutor xxlJobExecutor = new XxlJobSpringExecutor();
        xxlJobExecutor.setIp(executor.getIp());
        xxlJobExecutor.setPort(executor.getPort());
        xxlJobExecutor.setAppname(executor.getAppName());
        xxlJobExecutor.setLogPath(executor.getLogPath());
        xxlJobExecutor.setLogRetentionDays(executor.getLogRetentionDays());
        xxlJobExecutor.setAdminAddresses(admin.getAddresses());
        xxlJobExecutor.setAccessToken(properties.getAccessToken());
        return xxlJobExecutor;
    }

    @Bean
    @ConditionalOnMissingBean
    public HttpXxlJobApi httpXxlJobApi(XxlJobProperties properties) {
    	String userName = properties.getAdmin().getUsername();
        if (userName == null) {
            throw new RuntimeException("xxl.job.admin.username-调度器中心用户名不为空");
        }
        
        String passWord = properties.getAdmin().getPassword();
        if (passWord == null) {
            throw new RuntimeException("xxl.job.admin.password-调度器中心密码不为空");
        }
        
        HttpXxlJobApi httpXxlJobApi = new HttpXxlJobApi(properties);
        
        if (httpXxlJobApi.getXxlJobProxy().getAccessCookie() != null) {
            log.info("[httpXxlJobApi][初始化 XXL-Job 通信客户端 成功]");
        }
        else {
            log.error("[httpXxlJobApi][初始化 XXL-Job 通信客户端 失败]");
        }
        
        return httpXxlJobApi;
    }
}
