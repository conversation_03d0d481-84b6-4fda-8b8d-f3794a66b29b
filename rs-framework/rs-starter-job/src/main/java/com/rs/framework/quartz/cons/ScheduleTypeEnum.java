package com.rs.framework.quartz.cons;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 运行策略
 */
@Getter
@AllArgsConstructor
public enum ScheduleTypeEnum {
	NONE("1", "NONE", "未指定"),
	FIX_RATE("2", "FIX_RATE", "周期运行"),
	CRON("3", "CRON", "CRON表达式");

	private String value;
	private String xxlValue;
	private String desc;

	public static ScheduleTypeEnum getByValue(String value) {
		for (ScheduleTypeEnum scheduleTypeEnum : ScheduleTypeEnum.values()) {
			if(scheduleTypeEnum.getValue().equals(value)) {
				return scheduleTypeEnum;
			}
		}
		return null;
	}
}