package com.rs.framework.quartz.client;

import java.lang.reflect.Type;
import java.net.HttpCookie;
import java.util.HashMap;
import java.util.Map;

import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.quartz.config.XxlJobProperties;
import com.rs.framework.quartz.exception.XxlJobErrorCode;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * XxlJob代理类
 * <AUTHOR>
 * @date 2025年6月14日
 */
@Data
@Slf4j
public class XxlJobProxy {

	//登录地址
	private static final String LOGIN_URL = "/login";
	
	//登录认证标识
	public static final String LOGIN_IDENTITY_KEY = "XXL_JOB_LOGIN_IDENTITY";
	
	//内容类型
	public static final String contentType = "application/x-www-form-urlencoded; charset=UTF-8";
	
	//xxl-job地址
	private String addresses;
	
	//用户名
	private final String username;
	
	//密码
	private final String password;
	
	//cookie
	private HttpCookie accessCookie;
	
	/**
	 * 构造函数
	 * @param properties XxlJobProperties xxl属性配置
	 */
	public XxlJobProxy(XxlJobProperties properties) {
		username = properties.getAdmin().getUsername();
		password = properties.getAdmin().getPassword();
		addresses = properties.getAdmin().getAddresses();
		login();
	}
	
	/**
	 * 登录XxlJob
	 * @return boolean
	 */
	private boolean login() {
		Map<String, Object> params = new HashMap<>(2);
        params.put("userName", username);
        params.put("password", password);
        String url = addresses + LOGIN_URL;
        
        try {
        	HttpResponse execute = HttpRequest.post(url)
                    .contentType(contentType)
                    .form(params)
                    .execute();
        	CommonResult<?> result = JSONUtil.toBean(execute.body(), CommonResult.class);
        	if(result.getCode() != 200) {
        		log.error("login failed: {}", JSONUtil.toJsonStr(result));
        		return false;
        	}
        	HttpCookie cookie = execute.getCookie(LOGIN_IDENTITY_KEY);
        	if(cookie != null) {
        		accessCookie = cookie;
        	}
		}
        catch (Exception e) {
			log.error("[XxlJobProxy] login http request failed"
                    + " url: {}, params: {}, errorMsg: {}", url, params, e.getMessage());
		}
        
        return true;
	}
	
	/**
	 * 执行Post请求
	 * @param <T>
	 * @param url String 请求地址
	 * @param paramValues Map<String, Object> 请求参数
	 * @param responseType Type 响应类型
	 * @return T
	 * @throws Exception
	 */
	public <T> T httpPost(String url, Map<String, Object> paramValues, Type responseType) throws Exception {
        HttpResponse execute = getHttpResponse(url, paramValues);
        
        // cookie 失效
        if (execute.getStatus() == HttpStatus.HTTP_MOVED_TEMP) {
            accessCookie = null;
            throw new ServerException(XxlJobErrorCode.XXL_NETWORK_ERROR);
        }
        
        String resultJson = execute.body();
        
        try {
            return JSONUtil.toBean(resultJson, responseType, false);
        }
        catch (Exception e) {
            log.error("xxl-job remoting (url=" + url + ") response content invalid(" + resultJson + ").", e);
            throw e;
        }
    }
	
	/**
	 * 获取响应体
	 * @param url String 请求地址
	 * @param paramValues Map<String, Object> 请求参数
	 * @return HttpResponse
	 */
	private HttpResponse getHttpResponse(String url, Map<String, Object> paramValues) {
        if (accessCookie == null) {
            login();
        }
        HttpRequest request = HttpRequest.post(addresses + url)
                .contentType(contentType)
                .cookie(accessCookie)
                .form(paramValues);
        
        HttpResponse execute = null;
        
        try {
            execute = request.execute();
        } 
        catch (Exception e) {
            log.error("获取响应体异常，请求体：\n {}，异常信息：{}", request.toString(), e.getMessage());
            throw e;
        }
        
        return execute;
    }
}
