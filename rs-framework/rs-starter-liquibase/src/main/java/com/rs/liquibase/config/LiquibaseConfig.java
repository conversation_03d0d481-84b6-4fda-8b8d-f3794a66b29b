package com.rs.liquibase.config;

/**
 * @ClassName LiquibaseConfig
 *
 * <AUTHOR>
 * @Date 2024/3/4 16:35
 * @Version 1.0
 */

import liquibase.integration.spring.SpringLiquibase;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.DefaultResourceLoader;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * Liquibase配置类
 *
 * <AUTHOR> YangLinWei
 * @createTime: 2022/4/28 4:48 下午
 * @version: 1.0.0
 */
@Slf4j
@Configuration
public class LiquibaseConfig {

    /*** 指定liquibase的版本 **/
    @Value("${dc.version:main}")
    private String contexts;

    /*** 是否开启liquibase **/
    @Value("${dc.liquibase.enable:true}")
    private Boolean enable;

    /*** liquibase用到的两张表 **/
    private static final String DATABASE_CHANGE_LOG_TABLE = "lqb_changelog";
    private static final String DATABASE_CHANGE_LOG_LOCK_TABLE = "lqb_lock";

    @Resource
    private DataSource dataSource;

    /**
     * liquibase bean声明
     */
    @Bean
    public SpringLiquibase liquibase() {
        SpringLiquibase liquibase = new SpringLiquibase();
        // Liquibase文件路径
        liquibase.setChangeLog("classpath:db/master.xml");
        liquibase.setDataSource(dataSource);
        if (StringUtils.isNotBlank(contexts)) {
            liquibase.setContexts(contexts);
        }
        liquibase.setShouldRun(enable);
        liquibase.setResourceLoader(new DefaultResourceLoader());
        // 覆盖Liquibase changelog表名
        liquibase.setDatabaseChangeLogTable(DATABASE_CHANGE_LOG_TABLE);
        liquibase.setDatabaseChangeLogLockTable(DATABASE_CHANGE_LOG_LOCK_TABLE);
        liquibase.setClearCheckSums(true);
        liquibase.setTestRollbackOnUpdate(false); // 禁用测试回滚// 禁用测试流程
        return liquibase;
    }
}
