<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.rs</groupId>
		<artifactId>rs-framework</artifactId>
		<version>${rs.version}</version>
	</parent>

	<artifactId>rs-starter-oss</artifactId>
	<packaging>jar</packaging>
	<name>${project.artifactId}</name>
	<description>监所管理-技术组件-存储组件，基于x-file-storage实现</description>

	<dependencies>
		<dependency>
			<groupId>org.dromara.x-file-storage</groupId>
			<artifactId>x-file-storage-spring</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>hutool-core</artifactId>
					<groupId>cn.hutool</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>io.minio</groupId>
			<artifactId>minio</artifactId>
		</dependency>
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
		</dependency>
		<!-- web 相关 begin -->
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>hutool-all-u</artifactId>
					<groupId>cn.hutool</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- web 相关 end -->

		<!-- 工具类相关 begin -->
		<dependency>
			<groupId>cn.hutool</groupId>
		  	<artifactId>hutool-all-u</artifactId>
		</dependency>
		<!-- 工具类相关 end -->
	</dependencies>
</project>
