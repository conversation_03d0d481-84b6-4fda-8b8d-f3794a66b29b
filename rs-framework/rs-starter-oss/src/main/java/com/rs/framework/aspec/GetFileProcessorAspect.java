package com.rs.framework.aspec;

import com.rs.framework.processor.GetFileProcessor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName DatabaseFieldProcessorAspect
 * 
 * <AUTHOR>
 * @Date 2025/3/22 10:50
 * @Version 1.0
 */
@Aspect
@Component
public class GetFileProcessorAspect {

    @Autowired
    private GetFileProcessor getFileProcessor;

    @Around("execution(* @org.springframework.web.bind.annotation.RestController *.*(..))")
    public Object processDatabaseFields(ProceedingJoinPoint joinPoint) throws Throwable {
        // 执行目标方法
        Object result = joinPoint.proceed();

        // 如果结果是对象，则处理字段
        if (result != null) {
            result = getFileProcessor.process(result);
        }

        return result;
    }
}
