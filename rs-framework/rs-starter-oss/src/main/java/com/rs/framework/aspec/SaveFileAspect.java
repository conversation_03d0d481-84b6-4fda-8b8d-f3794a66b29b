package com.rs.framework.aspec;

import com.rs.framework.annotation.SaveFile;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.oss.entity.FileDetail;
import com.rs.module.oss.service.FileDetailService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.dromara.x.file.storage.core.FileInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.List;

@Aspect
@Component
public class SaveFileAspect {

    @Autowired
    FileDetailService fileDetailService;

    @AfterReturning(pointcut = "@annotation(org.springframework.web.bind.annotation.PostMapping)", returning = "result")
    public void saveFilesAfterReturning(JoinPoint joinPoint, Object result) throws IOException, IllegalAccessException {
        if (result != null && result instanceof CommonResult) {
            CommonResult<?> commonResult = (CommonResult<?>) result;

            if (commonResult != null && commonResult.isSuccess()) {
                Object data = commonResult.getData();
                if (data instanceof String && data != null) {

                    // 获取方法参数列表
                    Object[] args = joinPoint.getArgs();
                    for (int i = 0; i < args.length; i++) {
                        // 检查参数是否有SaveFile注解
                        Object paramValue = args[i];
                        if (paramValue != null) {
                            Field[] fields = paramValue.getClass().getDeclaredFields();
                            for (Field field : fields) {
                                if (field.isAnnotationPresent(SaveFile.class)) {
                                    SaveFile annotation = field.getAnnotation(SaveFile.class);
                                    String objectType = annotation.objectType();
                                    delFile(objectType, data.toString());
                                    field.setAccessible(true);
                                    Object fieldValue = field.get(paramValue);
                                    if (fieldValue instanceof List) {
                                        List<?> fileList = (List<?>) fieldValue;
                                        for (Object fileObj : fileList) {
                                            if (fileObj instanceof FileInfo) {
                                                FileInfo fileInfo = (FileInfo) fileObj;
                                                saveFile(fileInfo, objectType, data.toString());
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private void saveFile(FileInfo fileInfo, String objectType, String objectId) throws IOException {
        fileInfo.setObjectId(objectId);
        fileInfo.setObjectType(objectType);
        try {
            fileDetailService.save(fileInfo);
        } catch (Exception e) {
            FileDetail fileDetail = fileDetailService.getById(fileInfo.getId());
            if (fileDetail != null) {
                fileDetail.setObjectType(objectType);
                fileDetail.setObjectId(objectId);
                fileDetailService.update(fileDetailService.toFileInfo(fileDetail));
            }
        }
    }

    private void delFile(String objectType, String objectId) throws IOException {
        if (!StringUtils.isEmpty(objectId)) {
            fileDetailService.remove(new LambdaQueryWrapperX<FileDetail>()
                    .eqIfPresent(FileDetail::getObjectId, objectId)
                    .eqIfPresent(FileDetail::getObjectType, objectType));
        }

    }
}
