package com.rs.framework.processor;

import com.rs.framework.annotation.GetFile;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.oss.entity.FileDetail;
import com.rs.module.oss.service.FileDetailService;
import org.dromara.x.file.storage.core.FileInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;


/**
 * @ClassName DatabaseFieldProcessor
 * <AUTHOR>
 * @Date 2025/3/22 10:27
 * @Version 1.0
 */
@Component
public class GetFileProcessor {
    private static final Logger LOGGER = LoggerFactory.getLogger(GetFileProcessor.class);
    @Autowired
    private ApplicationContext applicationContext;

    public <T> T process(T object) throws Exception {
        Object data = null;
        if (object == null) {
            return object;
        }
        if (object instanceof CommonResult) {
            if (((CommonResult) object).isSuccess()) {
                data = ((CommonResult) object).getData();
            }
        }
        if (data == null) {
            return object;
        }
        // 判断 data 是否为 List 类型
        try {
            if (data instanceof java.util.List) {
                java.util.List<?> dataList = (java.util.List<?>) data;
                for (Object item : dataList) {
                    if (item != null) {
                        processFields(item);
                    }
                }
            } else {
                processFields(data);
            }
        } catch (Exception e) {
            return object;
        }
        return object;
    }

    private void processFields(Object data) throws Exception {
        try {
            Field[] fields = data.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (field.isAnnotationPresent(GetFile.class)) {
                    GetFile annotation = field.getAnnotation(GetFile.class);
                    String referenceField = annotation.value();
                    String objectType = annotation.objectType();
                    field.setAccessible(true);
                    Field reference = data.getClass().getDeclaredField(referenceField);
                    reference.setAccessible(true);
                    Object referenceValue = reference.get(data);
                    if (referenceValue != null) {
                        FileDetailService fileDetailService = applicationContext.getBean(FileDetailService.class);
                        List<FileDetail> fileDetailList = fileDetailService.list(new LambdaQueryWrapperX<FileDetail>()
                                .eqIfPresent(FileDetail::getObjectId, referenceValue)
                                .eqIfPresent(FileDetail::getObjectType, objectType));
                        if ("List".equals(field.getType().getSimpleName())) {
                            List<FileInfo> fileInfoList = new ArrayList<>();
                            if (fileDetailList != null && !fileDetailList.isEmpty()) {
                                fileDetailList.forEach(fileDetail -> {
                                    try {
                                        fileInfoList.add(fileDetailService.toFileInfo(fileDetail));
                                    } catch (Exception e) {
                                        LOGGER.error("GetFileProcessor 执行失败", e);
                                    }

                                });
                            }
                            field.set(data, fileInfoList);
                        }else {
                            if (fileDetailList != null && !fileDetailList.isEmpty()) {
                                FileDetail fileDetail = fileDetailList.get(0);
                                try {
                                    field.set(data, fileDetailService.toFileInfo(fileDetail));
                                } catch (Exception e) {
                                    LOGGER.error("GetFileProcessor 执行失败", e);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("GetFileProcessor 执行失败", e);
        }
    }
}
