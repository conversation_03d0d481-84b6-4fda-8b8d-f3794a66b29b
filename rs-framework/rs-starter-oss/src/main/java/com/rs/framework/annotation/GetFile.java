package com.rs.framework.annotation;

/**
 * @ClassName ShellHandler
 * 
 * <AUTHOR>
 * @Date 2025/3/25 20:46
 * @Version 1.0
 */
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface GetFile {
    String objectType() default ""; // 业务类型
    String value(); // 字段ID的名称
}
