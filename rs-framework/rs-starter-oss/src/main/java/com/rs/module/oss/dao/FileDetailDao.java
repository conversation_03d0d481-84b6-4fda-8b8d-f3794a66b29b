package com.rs.module.oss.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.oss.entity.FileDetail;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @ClassName FileDetailDao
 * 
 * <AUTHOR>
 * @Date 2025/3/24 13:53
 * @Version 1.0
 */
@Mapper
public interface FileDetailDao extends IBaseDao<FileDetail> {


}
