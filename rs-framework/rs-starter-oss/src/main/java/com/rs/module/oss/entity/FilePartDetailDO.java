package com.rs.module.oss.entity;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;

/**
 * 文件分片信息表，仅在手动分片上传时使用 DO
 *
 * <AUTHOR>
 */
@TableName("file_part_detail")
@KeySequence("file_part_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FilePartDetailDO {
private static final long serialVersionUID = 1L;
    public static final String COL_ID = "id";
    public static final String COL_UPLOAD_ID = "upload_id";
    /**
     * 分片id
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 存储平台
     */
    private String platform;
    /**
     * 上传ID，仅在手动分片上传时使用
     */
    private String uploadId;
    /**
     * 分片 ETag
     */
    private String eTag;
    /**
     * 分片号。每一个上传的分片都有一个分片号，一般情况下取值范围是1~10000
     */
    private Integer partNumber;
    /**
     * 文件大小，单位字节
     */
    private Long partSize;
    /**
     * 哈希信息
     */
    private String hashInfo;

    private Date createTime;

}
