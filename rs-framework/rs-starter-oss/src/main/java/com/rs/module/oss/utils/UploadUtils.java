package com.rs.module.oss.utils;

import cn.hutool.extra.spring.SpringUtil;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;

import java.io.File;
import java.util.Base64;

/**
 * @ClassName FileUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/6/13 14:38
 * @Version 1.0
 */
public class UploadUtils {
    //上传文件
    public static String upload(File file){
        FileStorageService fileStorageService = SpringUtil.getBean(FileStorageService.class);
        return fileStorageService.of(file).upload().getUrl();
    }

    /**
     * 上传Base64数据
     * @param fileBase64 图片base64数据
     * @return
     */
    public static String uploadByBase64(String fileBase64, String fileName){
        // 去除Base64前缀
        String[] parts = fileBase64.split(",");
        String pureBase64 = parts.length > 1 ? parts[1] : parts[0];
        FileStorageService fileStorageService = SpringUtil.getBean(FileStorageService.class);
        byte[] imageBytes = Base64.getMimeDecoder().decode(pureBase64);
        FileInfo upload = fileStorageService.of(imageBytes).setHashCalculatorMd5().setOriginalFilename(fileName).upload();
        System.out.println(upload);
        return upload.getUrl();
    }

    /**
     * 上传Base64数据
     * @param fileBase64 去除base64前缀的图片base64数据
     * @return
     */
    public static String uploadByBase(String fileBase64, String fileName){
        FileStorageService fileStorageService = SpringUtil.getBean(FileStorageService.class);
        byte[] imageBytes = Base64.getMimeDecoder().decode(fileBase64);
        FileInfo upload = fileStorageService.of(imageBytes).setHashCalculatorMd5().setOriginalFilename(fileName).upload();
        System.out.println(upload);
        return upload.getUrl();
    }
}
