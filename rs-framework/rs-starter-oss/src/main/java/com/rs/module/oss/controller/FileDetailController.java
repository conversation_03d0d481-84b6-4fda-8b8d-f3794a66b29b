package com.rs.module.oss.controller;

import cn.hutool.core.util.IdUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.oss.entity.FileDetail;
import com.rs.module.oss.service.FileDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

@Api(tags = "02[通用]文件管理")
@RestController
public class FileDetailController {

    private static final Map<String, MediaType> mediaTypeMap = new HashMap<>();

    static {
        mediaTypeMap.put("txt", MediaType.TEXT_PLAIN);
        mediaTypeMap.put("html", MediaType.TEXT_HTML);
        mediaTypeMap.put("css", MediaType.TEXT_PLAIN);
        mediaTypeMap.put("xml", MediaType.APPLICATION_XML);
        mediaTypeMap.put("json", MediaType.APPLICATION_JSON);
        mediaTypeMap.put("pdf", MediaType.APPLICATION_PDF);
        mediaTypeMap.put("PDF", MediaType.APPLICATION_PDF);
        mediaTypeMap.put("jpg", MediaType.IMAGE_JPEG);
        mediaTypeMap.put("svg", MediaType.IMAGE_JPEG);
        mediaTypeMap.put("jpeg", MediaType.IMAGE_JPEG);
        mediaTypeMap.put("png", MediaType.IMAGE_PNG);
        mediaTypeMap.put("gif", MediaType.IMAGE_GIF);
    }

    @Autowired
    private FileStorageService fileStorageService;

    @Autowired
    private FileDetailService fileDetailService;

    /**
     * 上传文件
     */
    @PostMapping("/upload")
    public FileInfo upload(MultipartFile file) {
        return fileStorageService.of(file).upload();
    }

    /**
     * 上传文件，成功返回文件 url
     */
    @PostMapping("/v2/upload")
    public CommonResult<FileInfo> upload2(MultipartFile file, HttpServletRequest request) {
        FileInfo fileInfo = fileStorageService.of(file)
                .setHashCalculatorMd5()
                .setObjectId(request.getParameter("objId"))   //关联对象id，为了方便管理，不需要可以不写
                .setObjectType(request.getParameter("objType")) //关联对象类型，为了方便管理，不需要可以不写
                .upload();  //将文件上传到对应地方
        return CommonResult.success(fileInfo);
    }

    /**
     * 上传文件，成功返回文件 url
     * @param fileBase64 base64数据
     * @return
     * @throws Exception
     */
    @PostMapping("/v2/uploadByBase64")
    public CommonResult<FileInfo> uploadByBase64(String fileBase64, String fileName) throws Exception {
        // 去除Base64前缀
        String[] parts = fileBase64.split(",");
        String pureBase64 = parts.length > 1 ? parts[1] : parts[0];
        byte[] imageBytes = Base64.getMimeDecoder().decode(pureBase64);
        FileInfo fileInfo = fileStorageService.of(imageBytes)
                .setHashCalculatorMd5()
                .setOriginalFilename(fileName)
                .upload();  //将文件上传到对应地方
        return CommonResult.success(fileInfo);
    }

    /**
     * 上传文件，成功返回文件 url
     * @param fileBase64 去除base64前缀的base64数据
     * @return
     * @throws Exception
     */
    @PostMapping("/v2/uploadByBase")
    public CommonResult<FileInfo> upload2(String fileBase64, String fileName) throws Exception {
        byte[] imageBytes = Base64.getMimeDecoder().decode(fileBase64);
        FileInfo fileInfo = fileStorageService.of(imageBytes)
                .setHashCalculatorMd5()
                .setOriginalFilename(fileName)
                .upload();  //将文件上传到对应地方
        return CommonResult.success(fileInfo);
    }

    /**
     * 上传文件，成功返回文件 url
     */
    @PostMapping("/face/upload")
    public CommonResult<FileInfo> face(MultipartFile file, HttpServletRequest request) {
        String originalFilename = IdUtil.fastSimpleUUID() + ".jpg";

        FileInfo fileInfo = fileStorageService.of(file)
                .setHashCalculatorMd5()
                .setSaveFilename(originalFilename) //设置保存的文件名，不需要可以不写，会随机生成
                .setObjectId(request.getParameter("objId"))   //关联对象id，为了方便管理，不需要可以不写
                .setObjectType(request.getParameter("objType")) //关联对象类型，为了方便管理，不需要可以不写
                .upload();  //将文件上传到对应地方
        return CommonResult.success(fileInfo);
    }

    /**
     * 上传文件，成功返回文件 url
     */
    @PostMapping("/uploadBatch")
    public CommonResult<List<FileInfo>> uploadBatch(@RequestParam("files") MultipartFile[] files, HttpServletRequest request) {
        List<FileInfo> fileInfoList = new ArrayList<>();
        for (MultipartFile file : files) {
            FileInfo fileInfo = fileStorageService.of(file)
                    .setHashCalculatorMd5()
                    .setSaveFilename(file.getOriginalFilename()) //设置保存的文件名，不需要可以不写，会随机生成
                    .setObjectId(request.getParameter("objId"))   //关联对象id，为了方便管理，不需要可以不写
                    .setObjectType(request.getParameter("objType")) //关联对象类型，为了方便管理，不需要可以不写
                    .upload();  //将文件上传到对应地方
            fileInfoList.add(fileInfo);
        }

        return CommonResult.success(fileInfoList);
    }

    /**
     * 上传图片，成功返回文件信息
     * 图片处理使用的是 https://github.com/coobird/thumbnailator
     */
    @PostMapping("/upload-image")
    public FileInfo uploadImage(MultipartFile file) {
        return fileStorageService.of(file)
                .image(img -> img.size(1000, 1000))  //将图片大小调整到 1000*1000
                .thumbnail(th -> th.size(200, 200))  //再生成一张 200*200 的缩略图
                .upload();
    }

    /**
     * 上传文件，成功返回文件 url
     */
    @ApiOperation(value = "获得文件信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "objType", value = "对象类型"),
            @ApiImplicitParam(name = "objId", value = "对象id")}
    )
    @GetMapping("/listFile")
    public CommonResult<List<FileDetail>> listFile(String objId, String objType) {
        return CommonResult.success(fileDetailService.list(new LambdaQueryWrapperX<FileDetail>()
                .eqIfPresent(FileDetail::getObjectId, objId)
                .eqIfPresent(FileDetail::getObjectType, objType)));
    }

    //下载
    @ApiOperation(value = "下载文件")
    @ApiImplicitParams(
            @ApiImplicitParam(name = "url", value = "文件url")
    )
    @GetMapping("/download")
    public ResponseEntity<byte[]> download(String url) {
        byte[] bytes = fileStorageService.download(url).bytes();
        //返回bytes，下载
        FileInfo byUrl = fileDetailService.getByUrl(url);
        MediaType mediaType = mediaTypeMap.getOrDefault(byUrl.getExt(), MediaType.APPLICATION_OCTET_STREAM);
        return CommonResult.fileStream(bytes, byUrl.getFilename(), mediaType);
    }


}

