package com.rs.module.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;


@Configuration
public class FileStorageConfig {

    @Value("${dromara.x-file-storage.default-platform}")
    private String defaultPlatform;

    @Value("${dromara.x-file-storage.thumbnail-suffix}")
    private String thumbnailSuffix;

    @Value("${dromara.x-file-storage.minio[0].platform}")
    private String minioPlatform;

    @Value("${dromara.x-file-storage.minio[0].enable-storage}")
    private boolean enableStorage;

    @Value("${dromara.x-file-storage.minio[0].access-key}")
    private String accessKey;

    @Value("${dromara.x-file-storage.minio[0].secret-key}")
    private String secretKey;

    @Value("${dromara.x-file-storage.minio[0].end-point}")
    private String endPoint;

    @Value("${dromara.x-file-storage.minio[0].bucket-name}")
    private String bucketName;

    @Value("${dromara.x-file-storage.minio[0].domain}")
    private String domain;

    @Value("${dromara.x-file-storage.minio[0].base-path}")
    private String basePath;

    // Getters and setters

    public String getDefaultPlatform() {
        return defaultPlatform;
    }

    public void setDefaultPlatform(String defaultPlatform) {
        this.defaultPlatform = defaultPlatform;
    }

    public String getThumbnailSuffix() {
        return thumbnailSuffix;
    }

    public void setThumbnailSuffix(String thumbnailSuffix) {
        this.thumbnailSuffix = thumbnailSuffix;
    }

    public String getMinioPlatform() {
        return minioPlatform;
    }

    public void setMinioPlatform(String minioPlatform) {
        this.minioPlatform = minioPlatform;
    }

    public boolean isEnableStorage() {
        return enableStorage;
    }

    public void setEnableStorage(boolean enableStorage) {
        this.enableStorage = enableStorage;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        this.endPoint = endPoint;
    }

    public String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getBasePath() {
        return basePath;
    }

    public void setBasePath(String basePath) {
        this.basePath = basePath;
    }
}

