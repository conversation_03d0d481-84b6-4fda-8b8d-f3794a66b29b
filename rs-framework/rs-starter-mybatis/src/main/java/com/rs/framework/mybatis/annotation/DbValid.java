package com.rs.framework.mybatis.annotation;


import com.rs.framework.mybatis.config.GlobalConstant;
import com.rs.framework.mybatis.validator.DbValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = {DbValidator.class})
public @interface DbValid {
    String message() default "参数校验不通过";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    String sql();
    String datasource() default GlobalConstant.DEFAULT_DATASOURCE_KEY; // 数据源

}
