package com.rs.framework.mybatis.authdata.handler;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.handler.DataPermissionHandler;
import com.rs.framework.common.util.security.PermissionUtil;
import com.rs.framework.mybatis.authdata.annotation.DataPermission;
import com.rs.framework.mybatis.authdata.aop.DataPermissionContextHolder;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import org.springframework.security.access.AccessDeniedException;

/**
 * 数据权限处理
 */
@Slf4j
public class CommonDataPermissionHandler implements DataPermissionHandler {

    public CommonDataPermissionHandler() {
    }

    @Override
    public Expression getSqlSegment(Expression where, String mappedStatementId) {
        DataPermission dataPermission = DataPermissionContextHolder.get();
        if (null == dataPermission || !dataPermission.enable()) {
            return null;
        }
        //数据权限过滤SQL
        String dataPermSql = PermissionUtil.getListPermissionSql(dataPermission.mark());
        if (StrUtil.isBlank(dataPermSql)) {
            throw new AccessDeniedException("数据权限异常，请检查配置");
        }
        Expression sqlSegmentExpression = null;
        try {
            if (StrUtil.isBlank(dataPermSql)) {
                return new AndExpression(where, CCJSqlParserUtil.parseCondExpression("1=0"));
            }
            sqlSegmentExpression = CCJSqlParserUtil.parseCondExpression(dataPermSql);
        } catch (Exception e) {
            log.error("解析数据权限脚本异常，脚本：{}, 异常信息：{}", dataPermSql, e.getMessage());
            try {
                return new AndExpression(where, CCJSqlParserUtil.parseCondExpression("1=0"));
            } catch (JSQLParserException e1) {
                e1.printStackTrace();
            }
        }
        if (where == null) {
            return sqlSegmentExpression;
        }
        return new AndExpression(where, sqlSegmentExpression);

    }
}
