package com.rs.framework.mybatis.validator;

import com.rs.framework.common.util.servlet.ServletUtils;
import com.rs.framework.mybatis.annotation.IdValid;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class IdValidator implements ConstraintValidator<IdValid, Object> {
    IdValid annotation;
    @Override
    public void initialize(IdValid constraintAnnotation) {
        this.annotation = constraintAnnotation;
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        //获取HttpRequest
        HttpServletRequest request = ServletUtils.getRequest();
        if (request.getRequestURL().toString().contains("create")) {
            if (StringUtils.isNotEmpty(value.toString())) {
                return false;
            }
        }
        return true;
    }
}
