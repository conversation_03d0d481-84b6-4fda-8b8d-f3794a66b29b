package com.rs.framework.mybatis.util;

import cn.hutool.core.util.IdUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;

import com.bsp.common.util.CollectionUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.mybatis.config.GlobalConstant;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * @ClassName UacUerUtil
 * <AUTHOR>
 * @Date 2025/3/28 14:20
 * @Version 1.0
 */
@Slf4j
public class BspDbUtil {
	
    /**
     * 更新用户人脸图片
     * @param url String 人脸图片地址
     * @param idCard String 证件号码
     */
    public static void updateUserFacePic(String url, String idCard) {
        Entity where = Entity.create();
        where.setTableName("uac_user_face");
        where.set("ID_CARD", idCard);
        
        try {
            List<Entity> faceList = getDb().find(where);
            if(CollectionUtil.isNotNull(faceList)) {
            	Entity faceEntity = faceList.get(0);
            	faceEntity.set("FACE_URL", url);
            	faceEntity.set("UPDATE_TIME ", new Date()); 
            	Entity faceWhere = Entity.create();
            	faceWhere.set("ID", faceEntity.getStr("ID"));
            	getDb().update(faceEntity, faceWhere);
            }
            else {
            	where.set("FACE_URL", url);
                where.set("ADD_TIME", new Date());
                where.set("UPDATE_TIME ", new Date());
                where.set("ID", IdUtil.fastSimpleUUID());
                getDb().insert(where);
            }           
        }
        catch (SQLException e) {
        	log.error("bsp更新用户人脸图片异常！异常信息：{}", e.getMessage());
        }
    }
    
    /**
     * 更新用户人脸图片
     * @param url String 人脸图片地址
     * @param idCard String 证件号码
     */
    public static void updateUserFaceUrl(String url, String id) {
        Entity where = Entity.create();
        where.setTableName("uac_user_face");
        where.set("ID", id);        
        
        try {
        	Entity faceEntity = Entity.create();
        	faceEntity.set("FACE_URL", url);
        	getDb().update(faceEntity, where);
        }
        catch (SQLException e) {
        	log.error("bsp更新用户人脸图片地址异常！异常信息：{}", e.getMessage());
        }
    }

    /**
     * 获取bsp所有人脸数据
     * @return List<Entity>
     */
    public static List<Entity> getAllUserFace() {
    	List<Entity> userFaceList = null;
    	Entity where = Entity.create();
    	where.setTableName("uac_user_face");
        
    	try {
    		userFaceList = getDb().find(where);
    	}
    	catch (SQLException e) {
    		log.error("bsp更新用户人脸图片异常！异常信息：{}", e.getMessage());
    	}
         
    	return userFaceList;
    }

    /**
     * 获取bsp数据源
     * @return Db
     */
    public static Db getDb() {
        DataSource ds = DatasourceUtil.getDataSource(GlobalConstant.BSP_DATASOURCE_KEY);
        Db use = Db.use(ds);
        return use;
    }

    public static String getSessionOrgCode() {
        return SessionUserUtil.getSessionUser().getOrgCode();
    }


    public static String getParam(String paramName) {
        Entity where = Entity.create();
        where.setTableName("ops_set_param");
        where.set("NAME", paramName);
        try {
            Entity entity = getDb().get(where);
            return entity.getStr("PARAM_VALUE");
        } catch (SQLException e) {
        }
        return null;
    }
}
