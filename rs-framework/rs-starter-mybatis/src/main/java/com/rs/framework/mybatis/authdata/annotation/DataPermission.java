package com.rs.framework.mybatis.authdata.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataPermission {

	/**
     * 开启数据权限
     */
    boolean enable() default true;

    /**
     * 数据权限标识
     */
    String mark();

    /**
     * 追加数据权限的表的别名
     */
    String alias() default "";
}
