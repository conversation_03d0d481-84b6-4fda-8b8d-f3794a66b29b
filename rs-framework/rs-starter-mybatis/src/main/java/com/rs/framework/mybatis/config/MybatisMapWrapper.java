package com.rs.framework.mybatis.config;

import java.util.Map;

import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.wrapper.MapWrapper;

public class MybatisMapWrapper extends MapWrapper{

	public MybatisMapWrapper(MetaObject metaObject, Map<String, Object> map) {
		super(metaObject, map);
	}
	@Override
	public String findProperty(String name, boolean useCamelCaseMapping) {
	    return null == name ? "":name.toLowerCase();
	 }
}
