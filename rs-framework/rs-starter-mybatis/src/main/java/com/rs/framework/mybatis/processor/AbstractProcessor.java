package com.rs.framework.mybatis.processor;

import com.rs.framework.common.annotation.Format;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.ClassUtils;
import com.rs.framework.mybatis.annotation.Query;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import java.lang.reflect.Field;
import java.util.*;

public abstract class AbstractProcessor {
    protected static final Logger LOGGER = LoggerFactory.getLogger(AbstractProcessor.class);

    @Autowired
    protected ApplicationContext applicationContext;
    protected static final int MAX_RECURSION_DEPTH = 3; // 新增常量
    private final Map<Class<?>, Field[]> fieldCache = new HashMap<>();

    public <T> T process(T object) throws Exception {
        Object data = null;
        if (object == null) {
            return object;
        }
        if (object instanceof CommonResult) {
            CommonResult<?> commonResult = (CommonResult<?>) object;
            if (commonResult.isSuccess()) {
                data = commonResult.getData();
            }
        } else {
            return object;
        }
        if (data == null) {
            return object;
        }
        try {
            if (data instanceof List) {
                List<?> dataList = (List<?>) data;
                for (Object item : dataList) {
                    if (item != null) {
                        processFields(item, 1); // 初始深度设为1
                    }
                }
            } else {
                processFields(data, 1); // 初始深度设为1
            }
        } catch (Exception e) {
            LOGGER.error("Error processing object", e);
            return object;
        }
        return object;
    }

    protected void processFields(Object data, int currentDepth) throws Exception {
        if (data == null || currentDepth > MAX_RECURSION_DEPTH) { // 使用常量
            return;
        }

        Field[] fields = getFieldCache(data.getClass());
        for (Field field : fields) {
            field.setAccessible(true);

            if (field.isAnnotationPresent(Query.class)) {
                handleQueryAnnotation(field, data);
            }
            if (field.isAnnotationPresent(Format.class)) {
                handleFormatAnnotation(field, data);
            }
            Object fieldValue = field.get(data);
            if (fieldValue != null) {
                if (field.getType().isArray()) {
                    try {
                        for (Object element : (Object[]) fieldValue) {
                            if (element != null && currentDepth < MAX_RECURSION_DEPTH && ClassUtils.isComplexType(element.getClass())) { // 使用常量
                                processFields(element, currentDepth + 1);
                            }
                        }
                    } catch (Exception e) {
                        // 异常处理保持原逻辑
                    }
                } else if (Collection.class.isAssignableFrom(field.getType())) {
                    for (Object element : (Collection<?>) fieldValue) {
                        if (element != null && currentDepth < MAX_RECURSION_DEPTH  && ClassUtils.isComplexType(element.getClass())) { // 使用常量
                            processFields(element, currentDepth + 1);
                        }
                    }
                } else if (ClassUtils.isComplexType(fieldValue.getClass())) {
                    if (currentDepth < MAX_RECURSION_DEPTH) { // 使用常量
                        processFields(fieldValue, currentDepth + 1);
                    }
                }
            }
        }
    }

    private Field[] getFieldCache(Class<?> clazz) {
        return fieldCache.computeIfAbsent(clazz, c -> getAllFields(c).toArray(new Field[0]));
    }

    protected abstract void handleQueryAnnotation(Field field, Object data) throws Exception;

    protected abstract void handleFormatAnnotation(Field field, Object data) throws Exception;

    private List<Field> getAllFields(Class<?> clazz) {
        List<Field> allFields = new ArrayList<>();
        while (clazz != null && ClassUtils.isComplexType(clazz)) {
            allFields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }
        return allFields;
    }
}
