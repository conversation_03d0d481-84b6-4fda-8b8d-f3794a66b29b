package com.rs.framework.mybatis.processor;

import cn.hutool.db.Db;
import cn.hutool.extra.template.Template;
import cn.hutool.extra.template.TemplateConfig;
import cn.hutool.extra.template.TemplateEngine;
import cn.hutool.extra.template.TemplateUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.rs.framework.common.util.object.ClassUtils;
import com.rs.framework.mybatis.annotation.DbHalder;
import com.rs.framework.mybatis.util.DatasourceUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Component
public class AfterDbHalderProcessor {
    private static final Logger LOGGER = LoggerFactory.getLogger(AfterDbHalderProcessor.class);
    private final ExpressionParser parser = new SpelExpressionParser();
    public void process(ProceedingJoinPoint joinPoint, DbHalder dbHalder, Object result) throws Exception {

        // 获取 MethodSignature
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        String sql = dbHalder.sql();
        String datasource = dbHalder.datasource();
        TemplateEngine engine = TemplateUtil.createEngine(new TemplateConfig());
        Template templateWhere = engine.getTemplate(sql);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.setSerializationInclusion(JsonInclude.Include.ALWAYS);
        // 获取目标方法的参数
        Object[] args = joinPoint.getArgs();
        Map<String, Object> map = new HashMap<>();
        for (int i = 0; i < args.length; i++) {
            map.put("req",objectMapper.convertValue(args[i], Map.class));
        }
        // 获取方法的返回类型
        Class<?> returnType = methodSignature.getReturnType();

        // 根据返回类型决定调用的方法
        if (!void.class.equals(returnType) && result != null) {
            if (ClassUtils.isComplexType3(returnType)) {
                map.put("resp", result);
            }
            if (ClassUtils.isComplexType4(returnType)) {
                map.put("resp", objectMapper.convertValue(result, Map.class));
            }
        }

        sql = templateWhere.render(map);
        if (sql.contains("where")) {
            LOGGER.info("DbHalderProcessor sql:{}", sql);
            DataSource ds = DatasourceUtil.getDataSource(datasource);
            Db use = Db.use(ds);
            use.execute(sql);
        }else {
            LOGGER.info("不包含where 不执行DbHalderProcessor sql:{}", sql);
        }


    }
}
