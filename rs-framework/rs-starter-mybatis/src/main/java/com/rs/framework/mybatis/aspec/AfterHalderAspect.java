package com.rs.framework.mybatis.aspec;
import com.rs.framework.mybatis.annotation.AfterHalder;
import com.rs.framework.mybatis.processor.AfterHalderProcessor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class AfterHalderAspect {

    @Autowired
    private AfterHalderProcessor afterHalderProcessor;

    @Around("@annotation(afterHalder)")
    public Object processAfterHalder(ProceedingJoinPoint joinPoint, AfterHalder afterHalder) throws Throwable {
        // 执行目标方法并获取返回值
        Object result = joinPoint.proceed(joinPoint.getArgs());

        // 处理注解逻辑，传入参数和返回值
        afterHalderProcessor.process(joinPoint, afterHalder, result);

        return result;
    }
}
