package com.rs.framework.mybatis.util;

import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.rs.framework.mybatis.config.GlobalConstant;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.List;


/**
 * @ClassName QueryUtil
 * <AUTHOR>
 * @Date 2025/3/28 14:20
 * @Version 1.0
 */
@Slf4j
public class QueryUtil {
    public static String getFmQuery(String mark) {
        DataSource ds = DatasourceUtil.getDataSource(GlobalConstant.BSP_DATASOURCE_KEY);
        Db use = Db.use(ds);
        Entity where = Entity.create();
        where.setTableName("com_fm_query");
        where.set("MARK", mark);
        try {
            List<Entity> entities = use.find(where);
            if (entities != null && entities.size() > 0) {
                return entities.get(0).getStr("SCRIPT");
            }
        } catch (SQLException e) {
            log.error("getFmQuery", e);
        }
        return null;
    }

}
