package com.rs.framework.mybatis.entity;

import com.rs.framework.common.util.json.JsonUtils;
import com.rs.framework.common.util.object.BeanUtils;
import lombok.Data;

import java.util.Optional;

/**
 * @ClassName BaseVO
 * <AUTHOR>
 * @Date 2025/4/8 11:29
 * @Version 1.0
 */
@Data
public class BaseVO {
    public <T> T toBean(Class<T> clazz) {
        return BeanUtils.toBean(this, clazz);
    }

    public <T> Optional<T> toOptional(T value) {
        try {
            return Optional.ofNullable(value);
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    public String toJsonString() {
        return JsonUtils.toJsonString(this);
    }
}
