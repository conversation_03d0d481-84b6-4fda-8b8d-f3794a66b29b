package com.rs.framework.mybatis.util;

import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.rs.framework.mybatis.config.GlobalConstant;
import com.rs.framework.mybatis.entity.PrisonerDO;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.List;

/**
 * @ClassName PrisonerUtils
 * 
 * <AUTHOR>
 * @Date 2025/3/29 14:20
 * @Version 1.0
 */
public class PrisonerUtils {
    /**
     * 根据jgrybm获取人员信息
     * @param jgrybm
     * @return
     */
    public static  PrisonerDO getPrisonerByJgrybm(String jgrybm) {
        DataSource ds = DatasourceUtil.getDataSource(GlobalConstant.DEFAULT_DATASOURCE_KEY);
        Db use = Db.use(ds);
        Entity where = Entity.create();
        where.setTableName("vw_acp_pm_prisoner");
        where.set("jgrybm", jgrybm);
        List<PrisonerDO> prisonerDOList = null;
        try {
            prisonerDOList = use.findAll(where, PrisonerDO.class);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        if (prisonerDOList != null && prisonerDOList.size() > 0) {
            return prisonerDOList.get(0);
        }
        return null;
    }
}
