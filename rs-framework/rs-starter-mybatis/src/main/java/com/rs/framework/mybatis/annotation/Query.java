package com.rs.framework.mybatis.annotation;

import com.rs.framework.mybatis.config.GlobalConstant;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @ClassName FetchFromDatabase
 *
 * <AUTHOR>
 * @Date 2025/3/22 10:26
 * @Version 1.0
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Query {
    String datasource() default GlobalConstant.DEFAULT_DATASOURCE_KEY; // 数据源
    String sql() default ""; // sql
    Class<?> beanClass() default Object.class;
    boolean isTile() default false;
    String ignore() default "id";
    String lbMark() default "";

}
