package com.rs.framework.mybatis.aspec;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.rs.framework.mybatis.processor.DefaultValueProcessor;
import com.rs.framework.mybatis.processor.PermissionProcessor;

@Aspect
@Component
public class RequestParameterProcessorAspect {

    @Autowired
    private DefaultValueProcessor defaultValueProcessor;

    @Autowired
    private PermissionProcessor permissionProcessor;

    @Around("execution(* @org.springframework.web.bind.annotation.RestController *.*(..))")
    public Object processRequestParameters(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取方法参数
        Object[] args = joinPoint.getArgs();

        // 处理带有 @DefaultValue 注解的字段
        for (Object arg : args) {
            if (arg != null) {
                defaultValueProcessor.process(arg);
                permissionProcessor.process(arg);
            }
        }

        // 执行目标方法
        return joinPoint.proceed(args);
    }
}
