package com.rs.framework.mybatis.core.handler;


import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.cons.CommonConstants;
import com.rs.framework.mybatis.entity.BaseDO;
import com.rs.framework.mybatis.entity.BaseDO_;
import com.rs.framework.rpc.core.util.SecurityUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Date;
import java.util.Objects;

import javax.servlet.http.HttpServletRequest;

/**
 * 通用参数填充实现类
 * <p>
 * 如果没有显式的对通用参数进行赋值，这里会对通用参数进行填充、赋值
 *
 * <AUTHOR>
 */
public class DefaultDBFieldHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        if (Objects.nonNull(metaObject)) {
            try {

            	//基础DO
                if (metaObject.getOriginalObject() instanceof BaseDO) {
                    BaseDO baseDO = (BaseDO) metaObject.getOriginalObject();
                    baseDO.setIsDel(false);
                    baseDO.setUpdateTime(new Date());
                    // 创建时间为空，则以当前时间为插入时间
                    if (Objects.isNull(baseDO.getAddTime())) {
                        baseDO.setAddTime(new Date());
                    }
                    // 更新时间为空，则以当前时间为更新时间
                    if (Objects.isNull(baseDO.getUpdateTime())) {
                        baseDO.setUpdateTime(new Date());
                    }

                    SessionUser user = SessionUserUtil.getSessionUser();
                    if (user == null) {
                        SecurityUtils.getRpcSessionUser();
                    }
                    if (user != null) {
                        // 当前登录用户不为空，创建人为空，则当前登录用户为创建人
                        if (Objects.isNull(baseDO.getAddUser())) {
                            baseDO.setAddUser(user.getIdCard());
                        }
                        if (Objects.isNull(baseDO.getAddUserName())) {
                            baseDO.setAddUserName(user.getName());
                        }
                        if (Objects.isNull(baseDO.getOrgCode())) {
                            baseDO.setOrgCode(user.getOrgCode());
                        }
                        if (Objects.isNull(baseDO.getOrgName())) {
                            baseDO.setOrgName(user.getOrgName());
                        }
                        if (Objects.isNull(baseDO.getRegCode())) {
                            baseDO.setRegCode(user.getRegCode());
                        }
                        if (Objects.isNull(baseDO.getRegName())) {
                            baseDO.setRegName(user.getRegName());
                        }
                        if (Objects.isNull(baseDO.getCityName())) {
                            baseDO.setCityName(user.getCityName());
                        }
                        if (Objects.isNull(baseDO.getCityCode())) {
                            baseDO.setCityCode(user.getCityCode());
                        }
                    }
                    baseDO.setUpdateUserName(user.getName());
                    baseDO.setUpdateUser(user.getIdCard());
                }
                
                //精简版DO
                else if (metaObject.getOriginalObject() instanceof BaseDO_) {
                    BaseDO_ baseDO = (BaseDO_) metaObject.getOriginalObject();
                    SessionUser user = SessionUserUtil.getSessionUser();
                    // 创建时间为空，则以当前时间为插入时间
                    if (Objects.isNull(baseDO.getAddTime())) {
                        baseDO.setAddTime(new Date());
                    }
                    if (user == null) {
                        SecurityUtils.getRpcSessionUser();
                    }
                    if (user != null) {
                        // 当前登录用户不为空，创建人为空，则当前登录用户为创建人
                        if (Objects.isNull(baseDO.getAddUser())) {
                            baseDO.setAddUser(user.getIdCard());
                        }
                        if (Objects.isNull(baseDO.getAddUserName())) {
                            baseDO.setAddUserName(user.getName());
                        }
                        if (Objects.isNull(baseDO.getOrgCode())) {
                            baseDO.setOrgCode(user.getOrgCode());
                        }
                        if (Objects.isNull(baseDO.getOrgName())) {
                            baseDO.setOrgName(user.getOrgName());
                        }
                        if (Objects.isNull(baseDO.getRegCode())) {
                            baseDO.setRegCode(user.getRegCode());
                        }
                        if (Objects.isNull(baseDO.getRegName())) {
                            baseDO.setRegName(user.getRegName());
                        }
                    }
                }
                
                //处理数据来源
                HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder
                		.getRequestAttributes()).getRequest();
                if(request != null) {               	
                	String datasource = request.getHeader(CommonConstants.DB_PROP_DATA_SOURCE);
                	if(StringUtil.isNotEmpty(datasource)) {
                		if(metaObject.hasSetter(CommonConstants.DB_PROP_DATA_SOURCE)) {
                			metaObject.setValue(CommonConstants.DB_PROP_DATA_SOURCE, datasource);
                		}
                	}
                }
            } catch (Exception e) {
            }
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        try {
            if (Objects.nonNull(metaObject) && metaObject.getOriginalObject() instanceof BaseDO) {
                BaseDO baseDO = (BaseDO) metaObject.getOriginalObject();
                // 创建时间为空，则以当前时间为插入时间
                baseDO.setUpdateTime(new Date());
                SessionUser user = SessionUserUtil.getSessionUser();
                if (user == null) {
                    user = SecurityUtils.getRpcSessionUser();
                }
                if (user != null) {
                    // 当前登录用户不为空，创建人为空，则当前登录用户为创建人
                    baseDO.setUpdateUser(user.getIdCard());
                    baseDO.setUpdateUserName(user.getName());
                }
            }
        } catch (Exception e) {

        }
    }
}
