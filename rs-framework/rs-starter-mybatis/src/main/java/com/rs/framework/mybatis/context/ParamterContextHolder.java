package com.rs.framework.mybatis.context;


import java.util.Map;

/**
 * @ClassName ParamterContextHolder
 * <AUTHOR>
 * @Date 2024/5/26 21:47
 * @Version 1.0
 */
public class ParamterContextHolder {

    // 使用ThreadLocal存储上下文信息
    private static final ThreadLocal<Map<String, Object>> context = new ThreadLocal<>();

    // 设置上下文
    public static void setContext(Map<String, Object> value) {
        context.set(value);
    }

    // 获取上下文
    public static Map<String, Object> getContext() {
        return context.get();
    }

    // 清除上下文
    public static void clearContext() {
        context.remove();
    }
}
