package com.rs.framework.mybatis.processor;

import org.springframework.stereotype.Component;

import com.bsp.common.util.StringUtil;
import com.rs.framework.common.pojo.PageParam;
import com.rs.framework.common.util.security.PermissionUtil;

/**
 * 权限处理工具类
 * <AUTHOR>
 * @date 2025年3月31日
 */
@Component
public class PermissionProcessor {

	public void process(Object object) throws IllegalAccessException{
		if (object != null) {
			
			//处理分页权限脚本
			if(object instanceof PageParam) {
				PageParam pageParam = (PageParam)object;
				if(pageParam != null) {
					if(StringUtil.isNotEmpty(pageParam.getPermMark())) {
						String permSql = PermissionUtil.getListPermissionSql(pageParam.getPermMark());
						pageParam.setPermSql(permSql);
					}
				}
			}
        }
	}
}
