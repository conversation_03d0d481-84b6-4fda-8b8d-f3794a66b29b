package com.rs.framework.mybatis.annotation;

import com.rs.framework.mybatis.config.GlobalConstant;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DbHalder {
    String datasource() default GlobalConstant.DEFAULT_DATASOURCE_KEY; // 数据源
    String sql(); // sql
}
