package com.rs.framework.mybatis.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.rs.framework.common.entity.OpsDicCode;
import com.rs.framework.mybatis.config.GlobalConstant;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.List;

/**
 * @ClassName DicUtil
 * <AUTHOR>
 * @Date 2025/3/28 14:20
 * @Version 1.0
 */
@Slf4j
public class DicUtil {
    public static List<OpsDicCode> getDicDesc(String dicName, String systemMark) {
        DataSource ds = DatasourceUtil.getDataSource(GlobalConstant.BSP_DATASOURCE_KEY);
        Db use = Db.use(ds);
        Entity where = Entity.create();
        where.setTableName(GlobalConstant.OPS_DIC_CODE_TABLE);
        where.set("dic_name", dicName);
        String sql = "select * from " + GlobalConstant.OPS_DIC_CODE_TABLE + " where dic_id in (" +
                "select id from " + GlobalConstant.OPS_DIC_TABLE + " where name = ? and IS_DEL = 0 and app_id in (" +
                "select id from uac_app where code = '" + systemMark + "'" +
                ")" +
                ") and IS_DISABLED=0 and IS_DEL=0 order by ORDER_ID desc ";
        try {
            List<OpsDicCode> opsDicCodeList = use.query(sql, OpsDicCode.class, dicName);

            if (CollectionUtil.isEmpty(opsDicCodeList)) {
                String publicDicSql = "select * from " + GlobalConstant.OPS_DIC_CODE_TABLE + " where dic_id in (" +
                        "select id from " + GlobalConstant.OPS_DIC_TABLE + " where name = ? and IS_DEL = 0 and app_id IS NULL"+
                        ") and IS_DISABLED=0 and IS_DEL=0 order by ORDER_ID desc ";
                opsDicCodeList = use.query(publicDicSql, OpsDicCode.class, dicName);
            }
            return opsDicCodeList;
        } catch (SQLException e) {
            log.error("获得字典失败：", e);
        }
        return new java.util.ArrayList<>();
    }

    public static List<OpsDicCode> getDicAsc(String dicName, String systemMark) {
        DataSource ds = DatasourceUtil.getDataSource(GlobalConstant.BSP_DATASOURCE_KEY);
        Db use = Db.use(ds);
        Entity where = Entity.create();
        where.setTableName(GlobalConstant.OPS_DIC_CODE_TABLE);
        where.set("dic_name", dicName);
        String sql = "select * from " + GlobalConstant.OPS_DIC_CODE_TABLE + " where dic_id in (" +
                "select id from " + GlobalConstant.OPS_DIC_TABLE + " where name = ? and IS_DEL = 0 AND app_id in (" +
                "select id from uac_app where code = '" + systemMark + "'" +
                ")" +
                ") and IS_DISABLED=0 and IS_DEL=0 order by ORDER_ID asc ";
        try {
            List<OpsDicCode> opsDicCodeList = use.query(sql, OpsDicCode.class, dicName);
            if (CollectionUtil.isEmpty(opsDicCodeList)) {
                String publicDicSql = "select * from " + GlobalConstant.OPS_DIC_CODE_TABLE + " where dic_id in (" +
                        "select id from " + GlobalConstant.OPS_DIC_TABLE + " where name = ? and IS_DEL = 0 and app_id IS NULL" +
                        ") and IS_DISABLED=0 and IS_DEL=0 order by ORDER_ID asc ";
                opsDicCodeList = use.query(publicDicSql, OpsDicCode.class, dicName);
            }
            return opsDicCodeList;
        } catch (SQLException e) {
            log.error("获得字典失败：", e);
        }
        return new java.util.ArrayList<>();
    }

    public static List<OpsDicCode> getDic(String dicName, String systemMark) {

        return getDicAsc(dicName, systemMark);
    }
}
