
package com.rs.framework.mybatis.processor;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import cn.hutool.extra.template.Template;
import cn.hutool.extra.template.TemplateConfig;
import cn.hutool.extra.template.TemplateEngine;
import cn.hutool.extra.template.TemplateUtil;
import com.bsp.common.util.StringUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.rs.framework.common.util.object.ClassUtils;
import com.rs.framework.mybatis.annotation.Query;
import com.rs.framework.mybatis.util.DatasourceUtil;
import com.rs.framework.mybatis.util.QueryUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Log4j2
@Component
public class QueryProcessor extends AbstractProcessor {

    @Override
    protected void handleQueryAnnotation(Field field, Object data) throws Exception {
        Query annotation = field.getAnnotation(Query.class);
        if (annotation == null) {
            LOGGER.warn("Field {} does not have @Query annotation", field.getName());
            return;
        }

        String datasource = annotation.datasource();
        Class<?> beanClass = annotation.beanClass();
        String sql = annotation.sql();
        boolean tile = annotation.isTile();
        String ignore = annotation.ignore();
        String lbMark = annotation.lbMark();
        if (StringUtils.isEmpty(sql) && StringUtils.isNotEmpty(lbMark)) {
            sql = QueryUtil.getFmQuery(lbMark);
            if (StringUtils.isNotEmpty(sql)) {
                sql = sql.replace("#", "$");
            }
        }
        // Validate SQL template
        if (StringUtil.isEmpty(sql)) {
            throw new IllegalArgumentException("SQL template is empty for field: " + field.getName());
        }

        TemplateEngine engine = TemplateUtil.createEngine(new TemplateConfig());
        Template templateWhere = engine.getTemplate(sql);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.setSerializationInclusion(JsonInclude.Include.ALWAYS);
        Map<String, Object> map = objectMapper.convertValue(data, Map.class);
        sql = templateWhere.render(map);
        LOGGER.info("Processed SQL for field {}: {}", field.getName(), sql);

        DataSource ds = DatasourceUtil.getDataSource(datasource);
        if (ds == null) {
            throw new RuntimeException("Datasource " + datasource + " not found for field: " + field.getName());
        }
        Db use = Db.use(ds);

        if (beanClass.getName().equals(Object.class.getName())) {
            List<Map<String, Object>> queryList = new ArrayList<>();
            List<Entity> query = use.query(sql);
            if (query != null && !query.isEmpty()) {
                for (Entity item : query) {
                    Map<String, Object> itemMap = new HashMap<>();
                    for (String key : item.keySet()) {
                        itemMap.put(StrUtil.toCamelCase(key), item.get(key));
                    }
                    queryList.add(itemMap);
                }

                if (field.getType().isAssignableFrom(List.class)) {
                    field.set(data, queryList);
                    // 递归处理查询结果中的每个 Map 对象
                    for (Map<String, Object> itemMap : queryList) {
                        processFields(itemMap, 1);
                    }
                } else {
                    if (!queryList.isEmpty()) {
                        if (field.getType().isAssignableFrom(Map.class)) {
                            field.set(data, queryList.get(0));
                            if (ClassUtils.isComplexType(queryList.get(0).getClass())) {
                                processFields(queryList.get(0), 1);
                            }
                        } else if (field.getType().isAssignableFrom(String.class)) {
                            field.set(data, queryList.get(0));
                        }
                    }
                }
            }
        } else {
            List<?> query = null;
            try {
                query = use.query(sql, beanClass);
            } catch (Exception e) {
                log.error("Error executing query for field {}: {}", field.getName(), sql, e);
                throw new RuntimeException("Query execution failed for field: " + field.getName(), e);
            }
            if (query != null && !query.isEmpty()) {
                if (field.getType().isAssignableFrom(List.class)) {
                    field.set(data, query);
                    // 递归处理查询结果中的每个对象
                    for (Object item : query) {
                        if (ClassUtils.isComplexType(item.getClass())) {
                            processFields(item, 1);
                        }
                    }
                } else {
                    field.set(data, query.get(0));
                    if (tile) {
                        String[] ignores = ignore.split(",");
                        CopyOptions copyOptions = CopyOptions.create().setIgnoreProperties(ignores);
                        copyOptions.setIgnoreNullValue(true);
                        BeanUtil.copyProperties(query.get(0), data, ignores);
                    }
                    if (ClassUtils.isComplexType(query.get(0).getClass())) {
                        processFields(query.get(0), 1);
                    }
                }
            }
        }
    }

    @Override
    protected void handleFormatAnnotation(Field field, Object data) throws Exception {

    }

}
