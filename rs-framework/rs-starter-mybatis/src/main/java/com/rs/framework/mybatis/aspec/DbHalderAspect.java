package com.rs.framework.mybatis.aspec;
import com.rs.framework.mybatis.annotation.DbHalder;
import com.rs.framework.mybatis.processor.AfterDbHalderProcessor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class DbHalderAspect {

    @Autowired
    private AfterDbHalderProcessor dbHalderProcessor;

    @Around("@annotation(dbHalder)")
    public Object processAfterHalder(ProceedingJoinPoint joinPoint, DbHalder dbHalder) throws Throwable {
        // 执行目标方法并获取返回值
        Object result = joinPoint.proceed(joinPoint.getArgs());

        // 处理注解逻辑，传入参数和返回值
        dbHalderProcessor.process(joinPoint, dbHalder, result);

        return result;
    }
}
