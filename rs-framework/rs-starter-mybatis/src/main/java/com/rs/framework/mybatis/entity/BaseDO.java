package com.rs.framework.mybatis.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.rs.framework.common.util.json.JsonUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.type.BooleanToIntegerTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.Optional;

/**
 * @ClassName BaseDO
 * <AUTHOR>
 * @Date 2025/3/12 17:18
 * @Version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BaseDO extends Model<BaseDO> implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableField(value = "CITY_NAME", fill = FieldFill.INSERT)
    private String cityName;

    @TableField(value = "CITY_CODE", fill = FieldFill.INSERT)
    private String cityCode;

    @TableField(value = "REG_NAME", fill = FieldFill.INSERT)
    private String regName;

    @TableField(value = "REG_CODE", fill = FieldFill.INSERT)
    private String regCode;

    @TableField(value = "ORG_NAME", fill = FieldFill.INSERT)
    private String orgName;

    @TableField(value = "ORG_CODE", fill = FieldFill.INSERT)
    private String orgCode;

    @TableField(value = "IS_DEL", fill = FieldFill.INSERT, typeHandler = BooleanToIntegerTypeHandler.class)
    @TableLogic
    private Boolean isDel;

    @TableField(value = "ADD_USER", fill = FieldFill.INSERT)
    private String addUser;

    @TableField(value = "ADD_USER_NAME", fill = FieldFill.INSERT)
    private String addUserName;

    @TableField(value = "ADD_TIME", fill = FieldFill.INSERT)
    private Date addTime;

    @TableField(value = "UPDATE_USER", fill = FieldFill.INSERT_UPDATE)
    private String updateUser;


    @TableField(value = "UPDATE_USER_NAME", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    public <T> T toBean(Class<T> clazz) {
        return BeanUtils.toBean(this, clazz);
    }

    public <T> Optional<T> toOptional(T value) {
        try {
            return Optional.ofNullable(value);
        } catch (Exception e) {
            return Optional.empty();
        }
    }
    public String toJsonString() {
        return JsonUtils.toJsonString(this);
    }

}
