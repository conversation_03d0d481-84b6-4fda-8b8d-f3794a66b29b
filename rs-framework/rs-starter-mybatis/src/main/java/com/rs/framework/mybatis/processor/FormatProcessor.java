package com.rs.framework.mybatis.processor;

import cn.hutool.core.bean.BeanUtil;
import com.rs.framework.common.annotation.Format;
import com.rs.framework.common.util.object.ClassUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;

@Component
public class FormatProcessor extends AbstractProcessor {

    private static final Logger logger = LoggerFactory.getLogger(FormatProcessor.class);

    @Autowired
    private ApplicationContext applicationContext;

    @Override
    protected void handleFormatAnnotation(Field field, Object data) throws Exception {
        Format annotation = field.getAnnotation(Format.class);
        String referenceField = annotation.value();
        Class<?> serviceClass = annotation.service();
        String methodName = annotation.method();
        Class<?> beanClass = annotation.toBean();

        try {
            // 获取引用字段的值
            Field reference = data.getClass().getDeclaredField(referenceField);
            reference.setAccessible(true);
            Object referenceValue = reference.get(data);

            if (referenceValue != null) {
                // 调用服务方法获取值
                Object serviceBean = applicationContext.getBean(serviceClass);
                Method method = serviceClass.getMethod(methodName, referenceValue.getClass());
                Object fetchedValue = method.invoke(serviceBean, referenceValue);

                // 检查 fetchedValue 是否是 List 类型
                if (fetchedValue instanceof List) {
                    field.set(data, fetchedValue);
                    List<?> fetchedList = (List<?>) fetchedValue;
                    for (Object item : fetchedList) {
                        if (item != null && ClassUtils.isComplexType(item.getClass())) {
                            processFields(item, 1);
                        }
                    }
                } else {
                    // 设置格式化后的值
                    if (!beanClass.equals(Object.class)) {
                        Object bean = BeanUtil.toBean(fetchedValue, beanClass);
                        field.set(data, bean);
                    } else {
                        field.set(data, fetchedValue);
                    }
                    // 递归处理格式化后的值
                    if (fetchedValue != null) {
                        if (ClassUtils.isComplexType(fetchedValue.getClass())) {
                            processFields(fetchedValue, 1);
                        }
                    }

                }
            }
        } catch (NoSuchFieldException | IllegalAccessException | NoSuchMethodException |
                 java.lang.reflect.InvocationTargetException e) {
            logger.error("Error processing field: {}", field.getName(), e);
            throw new RuntimeException("Error processing field: " + field.getName(), e);
        }
    }

    @Override
    protected void handleQueryAnnotation(Field field, Object data) throws Exception {

    }

}
