package com.rs.framework.mybatis.processor;

import com.rs.framework.mybatis.annotation.BeforeHalder;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Component
public class BeforeHalderProcessor {
    @Autowired
    private ApplicationContext applicationContext;

    public void process(JoinPoint joinPoint, BeforeHalder beforeHalder) throws Exception {
        // 获取注解中的 serviceClass 和 methodName
        Class<?> serviceClass = beforeHalder.serviceClass();
        String methodName = beforeHalder.methodName();

        // 获取目标方法的参数
        Object[] args = joinPoint.getArgs();

        // 获取 MethodSignature
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();

        // 获取方法的参数类型
        Class<?>[] parameterTypes = methodSignature.getParameterTypes();

        // 获取方法的返回类型
        Class<?> returnType = methodSignature.getReturnType();

        // 获取服务实例
        Object serviceBean = applicationContext.getBean(serviceClass);

        // 根据返回类型决定调用的方法
        if (void.class.equals(returnType)) {
            // 返回类型是 void，调用的服务方法只需要接受目标方法的参数
            Method method = serviceClass.getMethod(methodName, parameterTypes);
            method.invoke(serviceBean, args);
        } else {
            // 返回类型不是 void，调用的服务方法需要接受目标方法的参数和返回值
            Method method = serviceClass.getMethod(methodName, parameterTypes);
            method.invoke(serviceBean, args);
        }
    }
}
