package com.rs.framework.common.util.security;

import com.bsp.sdk.perm.QueryRuleUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.mybatis.util.RsQueryRuleUtil;

/**
 * 权限工具类
 * <AUTHOR>
 * @date 2025年3月25日
 */
public class PermissionUtil {

	/**
	 * 获取列表浏览权限脚本
	 * @param permMark String 权限标识
	 * @return String
	 * <AUTHOR>
	 * @date 2025年3月25日
	 */
	public static String getListPermissionSql(String permMark) {
		String permissionSql = "1 = 2";
		SessionUser user = SessionUserUtil.getSessionUser();
		if(user != null) {
			permissionSql = RsQueryRuleUtil.getRuleSql(permMark, user.getRoleIds(), user.getRoleGroupIds());
			permissionSql = QueryRuleUtil.formatRuleSql(permissionSql, user);
		}

		return permissionSql;
	}
}
