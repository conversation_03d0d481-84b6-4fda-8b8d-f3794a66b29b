package com.rs.framework.mybatis.msg;

import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.config.SystemMarkConfig;
import com.rs.framework.common.entity.uac.UacOrgRespVO;
import com.rs.framework.common.util.spring.SpringUtils;
import com.rs.framework.mybatis.util.UacUserUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.log4j.Log4j2;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName Message
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/25 16:55
 * @Version 1.0
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@Log4j2
public class Message {
    /**
     * 工作提醒
     */
    String title;
    /**
     * 工作提醒内容
     */
    String content;
    /**
     * 工作提醒地址
     */
    String url;
    /**
     * 来源应用
     */
    String fApp;
    /**
     * 流程实例Id
     */
    String actInstId;
    /**
     * 批次Id
     */
    String pcid;

    /**
     * 业务编号
     */
    String ywbh;

    /**
     * 业务类型
     */
    String busType;
    /**
     * 扩展数据
     */
    String extendData;


    List<String> receiveUserList;

    private void sendAlertMsg(String fXxpt) {

        SystemMarkConfig systemMarkConfig = SpringUtils.getBean(SystemMarkConfig.class);
        try {
            SessionUser sessionUser = SessionUserUtil.getSessionUser();
            String systemMark = systemMarkConfig.getSystemMark();
            String fUser = sessionUser.getIdCard();
            String fUserName = sessionUser.getName();
            String fOrgCode = sessionUser.getOrgCode();
            String fOrgName = sessionUser.getOrgName();
            List<ReceiveUser> receiveUsers = receiveUserList.stream()
                    .distinct()
                    .filter(sfzh -> !sfzh.equals(fUser))
                    .map(UacUserUtil::getUserBySfzh)
                    .filter(user -> user != null)
                    .map(user -> {
                        UacOrgRespVO org = UacUserUtil.getOrgById(user.getOrgId());
                        return new ReceiveUser(user.getIdCard(), user.getOrgId(), user.getMobile(), user.getName(), org.getName());
                    })
                    .collect(Collectors.toList());

            SendMessageUtil.sendAlertMsg(title, content, url, fApp, fUser, fUserName, fOrgCode, fOrgName, actInstId, pcid, fXxpt, ywbh, receiveUsers, systemMark, busType, extendData);

        } catch (Exception e) {
            log.error("获取sessionUser失败", e);
        }
    }

    public void sendAlertMsgFromPc() {
        sendAlertMsg("pc");
    }

    public void sendAlertMsgFromApp() {
        sendAlertMsg("app");
    }

}
