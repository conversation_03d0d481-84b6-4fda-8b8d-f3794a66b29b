package com.rs.framework.mybatis.util;

import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.entity.uac.UacOrgRespVO;
import com.rs.framework.common.entity.uac.UacUserJobRespVO;
import com.rs.framework.common.entity.uac.UacUserPhotoRespVO;
import com.rs.framework.common.entity.uac.UacUserRespVO;
import com.rs.framework.mybatis.config.GlobalConstant;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName UacUerUtil
 * <AUTHOR>
 * @Date 2025/3/28 14:20
 * @Version 1.0
 */
@Slf4j
public class UacUserUtil {
    /**
     * 根据岗位id获取用户信息
     *
     * @param jobId
     * @return
     */
    public static List<UacUserRespVO> getUserByJob(String jobId) {
        Entity where = Entity.create();
        where.setTableName("UAC_USER_JOB");
        where.set("JOB_ID", jobId);
        try {
            List<UacUserJobRespVO> uacUserJobRespVOList = getDb().find(where, UacUserJobRespVO.class);
            return uacUserJobRespVOList.stream()
                    .map(UacUserJobRespVO::getUserId)
                    .map(userId -> {
                        Entity whereUacUser = Entity.create();
                        try {
                            whereUacUser.setTableName("UAC_USER");
                            whereUacUser.set("ISDEL", "0");
                            whereUacUser.set("ID", userId);
                            List<UacUserRespVO> uacUserRespVOList = getDb().find(whereUacUser, UacUserRespVO.class);
                            if (!uacUserRespVOList.isEmpty()) {
                                return uacUserRespVOList.get(0);
                            }
                        } catch (SQLException e) {
                            log.error("获得用户", e);
                        }
                        return null;
                    })
                    .collect(Collectors.toList());
        } catch (SQLException e) {
            log.error("获得所内就医-报病字典管理列表-字典", e);
        }
        return new java.util.ArrayList<>();
    }

    /**
     * 获取机构下的岗位人员
     *
     * @param jobId
     * @param orgId
     * @return
     */
    public static List<UacUserRespVO> getUserByJobAndOrg(String jobId, String orgId) {
        List<UacUserRespVO> userByJob = getUserByJob(jobId);
        return userByJob.stream()
                .filter(user -> orgId.equals(user.getOrgId()))
                .collect(Collectors.toList());
    }

    /**
     * 根据身份证号获取用户信息
     *
     * @param sfzh
     * @return
     */
    public static UacUserRespVO getUserBySfzh(String sfzh) {

        Entity where = Entity.create();
        where.setTableName("UAC_USER");
        where.set("ID_CARD", sfzh);
        where.set("ISDEL", "0");
        where.set("IS_DISABLED", "0");
        List<UacUserRespVO> uacUserRespVOList = null;
        try {
            uacUserRespVOList = getDb().find(where, UacUserRespVO.class);
        } catch (SQLException e) {
            log.error("获得用户", e);
        }
        if (!uacUserRespVOList.isEmpty()) {
            return uacUserRespVOList.get(0);
        }
        return null;
    }

    /**
     * 根据身份证号获取用户信息
     *
     * @param sfzhList
     * @return
     */
    public static List<UacUserRespVO> getUserBySfzhs(List<String> sfzhList) {

        Entity where = Entity.create();
        where.setTableName("UAC_USER");
        where.set("ID_CARD", sfzhList);
        where.set("ISDEL", "0");
        where.set("IS_DISABLED", "0");
        List<UacUserRespVO> uacUserRespVOList = null;
        try {
            uacUserRespVOList = getDb().find(where, UacUserRespVO.class);
        } catch (SQLException e) {
            log.error("获得用户", e);
        }
        if (!uacUserRespVOList.isEmpty()) {
            return uacUserRespVOList;
        }
        return null;
    }

    /**
     * 根据id获取用户信息
     *
     * @return
     */
    public static UacUserRespVO getUserById(String id) {

        Entity where = Entity.create();
        where.setTableName("UAC_USER");
        where.set("ID", id);
        where.set("ISDEL", "0");
        where.set("IS_DISABLED", "0");
        List<UacUserRespVO> uacUserRespVOList = null;
        List<UacUserPhotoRespVO> uacUserPhotoRespVOList = null;
        try {
            uacUserRespVOList = getDb().find(where, UacUserRespVO.class);
            Entity wherePhoto = Entity.create();
            wherePhoto.setTableName("uac_user_photo");
            wherePhoto.set("ID", id);
            uacUserPhotoRespVOList = getDb().find(wherePhoto, UacUserPhotoRespVO.class);
        } catch (SQLException e) {
            log.error("获得用户", e);
        }
        if (!uacUserRespVOList.isEmpty()) {
            UacUserRespVO uacUserRespVO = uacUserRespVOList.get(0);
            if (!uacUserPhotoRespVOList.isEmpty()) {
                uacUserRespVO.setPhotoVO(uacUserPhotoRespVOList.get(0));
            }
            return uacUserRespVO;
        }
        return null;
    }

    /**
     * 根据机构id获取机构信息
     *
     * @param orgId
     * @return
     */

    public static UacOrgRespVO getOrgById(String orgId) {
        Entity where = Entity.create();
        where.setTableName("UAC_ORG");
        where.set("ISDEL", "0");
        where.set("ID", orgId);
        try {
            List<UacOrgRespVO> uacOrgRespVOList = getDb().find(where, UacOrgRespVO.class);
            if (!uacOrgRespVOList.isEmpty()) {
                return uacOrgRespVOList.get(0);
            }
        } catch (SQLException e) {
            log.error("获得机构", e);
        }
        return null;
    }

    /**
     * @return
     */

    public static Db getDb() {
        DataSource ds = DatasourceUtil.getDataSource(GlobalConstant.BSP_DATASOURCE_KEY);
        Db use = Db.use(ds);
        return use;
    }

    public static String getSessionOrgCode() {
        return SessionUserUtil.getSessionUser().getOrgCode();
    }

}
