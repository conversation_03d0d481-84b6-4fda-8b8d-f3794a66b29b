package com.rs.framework.mybatis.authdata.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.DataPermissionHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.DataPermissionInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.rs.framework.mybatis.authdata.annotation.DataPermission;
import com.rs.framework.mybatis.authdata.aop.DataPermissionAnnotationInterceptor;
import com.rs.framework.mybatis.authdata.handler.CommonDataPermissionHandler;
import org.aopalliance.intercept.MethodInterceptor;
import org.springframework.aop.Pointcut;
import org.springframework.aop.support.ComposablePointcut;
import org.springframework.aop.support.DefaultPointcutAdvisor;
import org.springframework.aop.support.annotation.AnnotationMatchingPointcut;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Configuration
@ComponentScan("com.rs.*")
@ConditionalOnProperty(prefix = "bsp.data-permission", name = "enable", havingValue = "true", matchIfMissing = false)
public class AuthorityAutoConfiguration {

	@Bean
    DataPermissionHandler dataPermissionHandler() {
        return new CommonDataPermissionHandler();
    }

	@Bean
    DefaultPointcutAdvisor dataPermissionAnnotationAdvisor() {
        //限定类级别的切点
        Pointcut cpc = new AnnotationMatchingPointcut(DataPermission.class, true);
        //限定方法级别的切点
        Pointcut mpc = new AnnotationMatchingPointcut(null, DataPermission.class, true);
        //组合切面(并集)
        Pointcut pointcut = new ComposablePointcut(cpc).union(mpc);
        //拦截器
        MethodInterceptor interceptor = new DataPermissionAnnotationInterceptor();
        //切面类
        DefaultPointcutAdvisor advisor = new DefaultPointcutAdvisor();
        advisor.setPointcut(pointcut);
        advisor.setAdvice(interceptor);
        return advisor;
    }

	@Bean
    DataPermissionInterceptor dataPermissionDatabaseInterceptor(MybatisPlusInterceptor interceptor, DataPermissionHandler dataPermissionHandler) {
		DataPermissionInterceptor inner = new DataPermissionInterceptor(dataPermissionHandler);

		//加入到现有的拦截器集合中
        List<InnerInterceptor> inners = new ArrayList<>(interceptor.getInterceptors());
        inners.add(0, inner);
        interceptor.setInterceptors(inners);
        return inner;
    }
}
