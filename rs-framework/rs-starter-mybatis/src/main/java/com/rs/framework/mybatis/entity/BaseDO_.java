package com.rs.framework.mybatis.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.rs.framework.common.util.json.JsonUtils;
import com.rs.framework.common.util.object.BeanUtils;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Optional;

/**
 * 基础DO
 * <AUTHOR>
 * @date 2025年4月8日
 */
@Data
public class BaseDO_ implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableField(value = "REG_NAME",fill = FieldFill.INSERT)
    private String regName;

    @TableField(value = "REG_CODE",fill = FieldFill.INSERT)
    private String regCode;

    @TableField(value = "ORG_NAME",fill = FieldFill.INSERT)
    private String orgName;

    @TableField(value = "ORG_CODE",fill = FieldFill.INSERT)
    private String orgCode;

    @TableField(value = "ADD_USER",fill = FieldFill.INSERT)
    private String addUser;

    @TableField(value = "ADD_USER_NAME",fill = FieldFill.INSERT)
    private String addUserName;

    @TableField(value = "ADD_TIME",fill = FieldFill.INSERT)
    private Date addTime;

    public <T> T toBean(Class<T> clazz) {
        return BeanUtils.toBean(this, clazz);
    }

    public <T> Optional<T> toOptional(T value) {
        try {
            return Optional.ofNullable(value);
        } catch (Exception e) {
            return Optional.empty();
        }
    }
    public String toJsonString() {
        return JsonUtils.toJsonString(this);
    }
}
