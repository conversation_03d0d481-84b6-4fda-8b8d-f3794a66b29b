package com.rs.framework.mybatis.util;


import com.rs.framework.common.exception.ServiceException;

import java.util.Collection;
import java.util.Map;
import java.util.function.Supplier;

public abstract class BizAssert {


    /**
     * 断言一个布尔表达式为 {@code false}
     *
     * @param expression 表达式 -布尔表达式
     * @param message    断言失败时使用的异常消息
     */
    public static void isFalse(boolean expression, String message) {
        if (expression) {
            throw new ServiceException(500,message);
        }
    }

    /**
     * 断言一个布尔表达式为 {@code false}
     *
     * @param expression        表达式 -布尔表达式
     * @param exceptionSupplier 断言失败时使用的异常Supplier
     */
    public static void isFalse(boolean expression, Supplier<? extends RuntimeException> exceptionSupplier) {
        if (expression) {
            throw nullSafeGet(exceptionSupplier);
        }
    }


    /**
     * 断言一个布尔表达式为 {@code true}
     *
     * @param expression 布尔表达式
     * @param message    断言失败时要使用的异常消息
     */
    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            throw new ServiceException(500,message);
        }
    }

    /**
     * 断言一个布尔表达式为 {@code true}
     *
     * @param expression        表达式 -布尔表达式
     * @param exceptionSupplier 断言失败时使用的异常Supplier
     */
    public static void isTrue(boolean expression, Supplier<? extends RuntimeException> exceptionSupplier) {
        if (!expression) {
            throw nullSafeGet(exceptionSupplier);
        }
    }


    /**
     * 断言一个对象是 {@code null}
     *
     * @param object  要检查的对象
     * @param message 断言失败时使用的异常消息
     */
    public static void isNull(Object object, String message) {
        if (object != null) {
            throw new ServiceException(500,message);
        }
    }

    /**
     * 断言一个对象是 {@code null}
     *
     * @param object            要检查的对象
     * @param exceptionSupplier 断言失败时使用的异常Supplier
     */
    public static void isNull(Object object, Supplier<? extends RuntimeException> exceptionSupplier) {
        if (object != null) {
            throw nullSafeGet(exceptionSupplier);
        }
    }


    /**
     * 断言一个对象不是 {@code null}
     *
     * @param object  要检查的对象
     * @param message 断言失败时使用的异常消息
     */
    public static void notNull(Object object, String message) {
        if (object == null) {
            throw new ServiceException(500,message);
        }
    }

    /**
     * 断言一个对象不是 {@code null}
     *
     * @param object            要检查的对象
     * @param exceptionSupplier 断言失败时使用的异常Supplier
     */
    public static void notNull(Object object, Supplier<? extends RuntimeException> exceptionSupplier) {
        if (object == null) {
            throw nullSafeGet(exceptionSupplier);
        }
    }

    // ------------------------------------------ not blank CharSequence -----------------------------------------------

    /**
     * 断言一个可读序列字符不为 {@code null} 或空字符(包含全是空格字符)
     * null    ->  不通过
     * ""      ->  不通过
     * "   "   ->  不通过
     *
     * @param text    可读序列字符
     * @param message 断言失败时使用的异常消息
     */
    public static void notBlank(CharSequence text, String message) {
        if (isBlankCharSequence(text)) {
            throw new ServiceException(500,message);
        }
    }

    /**
     * 断言一个可读序列字符不为 {@code null} 或空字符(包含全是空格字符)
     * null    ->  不通过
     * ""      ->  不通过
     * "   "   ->  不通过
     *
     * @param text              可读序列字符
     * @param exceptionSupplier 断言失败时使用的异常Supplier
     */
    public static void notBlank(CharSequence text, Supplier<? extends RuntimeException> exceptionSupplier) {
        if (isBlankCharSequence(text)) {
            throw nullSafeGet(exceptionSupplier);
        }
    }

    // ------------------------------------------ is blank CharSequence ------------------------------------------------

    /**
     * 断言一个可读序列字符为 {@code null} 或空字符(包含全是空格字符)
     * null    ->  通过
     * ""      ->  通过
     * "   "   ->  通过
     *
     * @param text    可读序列字符
     * @param message 断言失败时使用的异常消息
     */
    public static void isBlank(CharSequence text, String message) {
        if (isNotBlankCharSequence(text)) {
            throw new ServiceException(500,message);

        }
    }

    /**
     * 断言一个可读序列字符为 {@code null} 或空字符(包含全是空格字符)
     * null    ->  通过
     * ""      ->  通过
     * "   "   ->  通过
     *
     * @param text              可读序列字符
     * @param exceptionSupplier 断言失败时使用的异常Supplier
     */
    public static void isBlank(CharSequence text, Supplier<? extends RuntimeException> exceptionSupplier) {
        if (isNotBlankCharSequence(text)) {
            throw nullSafeGet(exceptionSupplier);
        }
    }

    // ------------------------------------------ not empty CharSequence -----------------------------------------------

    /**
     * 断言一个可读序列字符不为 {@code null} 或空字符(不包含全是空格的字符)
     * null    ->  不通过
     * ""      ->  不通过
     * "   "   ->  通过
     *
     * @param text    可读序列字符
     * @param message 断言失败时使用的异常消息
     */
    public static void notEmpty(CharSequence text, String message) {
        if (isCharSequenceEmpty(text)) {
            throw new ServiceException(500,message);
        }
    }

    /**
     * 断言一个可读序列字符不为 {@code null} 或空字符(不包含全是空格的字符)
     * null    ->  不通过
     * ""      ->  不通过
     * "   "   ->  通过
     *
     * @param text              可读序列字符
     * @param exceptionSupplier 断言失败时使用的异常Supplier
     */
    public static void notEmpty(CharSequence text, Supplier<? extends RuntimeException> exceptionSupplier) {
        if (isCharSequenceEmpty(text)) {
            throw nullSafeGet(exceptionSupplier);
        }
    }

    // ------------------------------------------ is empty CharSequence ------------------------------------------------

    /**
     * 断言一个可读序列字符为 {@code null} 或空字符(不包含全是空格的字符)
     * null    ->  通过
     * ""      ->  通过
     * "   "   ->  不通过
     *
     * @param text    可读序列字符
     * @param message 断言失败时使用的异常消息
     */
    public static void isEmpty(CharSequence text, String message) {
        if (!isCharSequenceEmpty(text)) {
            throw new ServiceException(500,message);
        }
    }

    /**
     * 断言一个可读序列字符为 {@code null} 或空字符(不包含全是空格的字符)
     * null    ->  通过
     * ""      ->  通过
     * "   "   ->  不通过
     *
     * @param text              可读序列字符
     * @param exceptionSupplier 断言失败时使用的异常Supplier
     */
    public static void isEmpty(CharSequence text, Supplier<? extends RuntimeException> exceptionSupplier) {
        if (!isCharSequenceEmpty(text)) {
            throw nullSafeGet(exceptionSupplier);
        }
    }


    // ------------------------------------------ not empty array ------------------------------------------------------

    /**
     * 断言数组包含元素
     *
     * @param array   待检查数组
     * @param message 断言失败时使用的异常消息
     */
    public static void notEmpty(Object[] array, String message) {
        if (isArrayEmpty(array)) {
            throw new ServiceException(500,message);
        }
    }

    /**
     * 断言数组包含元素
     *
     * @param array             待检查数组
     * @param exceptionSupplier 断言失败时使用的异常Supplier
     */
    public static void notEmpty(Object[] array, Supplier<? extends RuntimeException> exceptionSupplier) {
        if (isArrayEmpty(array)) {
            throw nullSafeGet(exceptionSupplier);
        }
    }

    // ------------------------------------------ is empty array -------------------------------------------------------

    /**
     * 断言数组不包含元素
     *
     * @param array   待检查数组
     * @param message 断言失败时使用的异常消息
     */
    public static void isEmpty(Object[] array, String message) {
        if (!isArrayEmpty(array)) {
            throw new ServiceException(500,message);
        }
    }


    /**
     * 断言数组不包含元素
     *
     * @param array             待检查数组
     * @param exceptionSupplier 断言失败时使用的异常Supplier
     */
    public static void isEmpty(Object[] array, Supplier<? extends RuntimeException> exceptionSupplier) {
        if (!isArrayEmpty(array)) {
            throw nullSafeGet(exceptionSupplier);
        }
    }

    // ------------------------------------------ not empty collection -------------------------------------------------

    /**
     * 断言一个集合包含元素
     *
     * @param collection 待检查集合
     * @param message    断言失败时使用的异常消息
     */
    public static void notEmpty(Collection<?> collection, String message) {
        if (isCollectionEmpty(collection)) {
            throw new ServiceException(500,message);
        }
    }

    /**
     * 断言集合包含元素
     *
     * @param collection        待检查数组
     * @param exceptionSupplier 断言失败时使用的异常Supplier
     */
    public static void notEmpty(Collection<?> collection, Supplier<? extends RuntimeException> exceptionSupplier) {
        if (isCollectionEmpty(collection)) {
            throw nullSafeGet(exceptionSupplier);
        }
    }

    // ------------------------------------------ is empty collection --------------------------------------------------

    /**
     * 断言一个集合不包含元素
     *
     * @param collection 待检查集合
     * @param message    断言失败时使用的异常消息
     */
    public static void isEmpty(Collection<?> collection, String message) {
        if (!isCollectionEmpty(collection)) {
            throw new ServiceException(500,message);
        }
    }

    /**
     * 断言集合不包含元素
     *
     * @param collection        待检查集合
     * @param exceptionSupplier 断言失败时使用的异常Supplier
     */
    public static void isEmpty(Collection<?> collection, Supplier<? extends RuntimeException> exceptionSupplier) {
        if (!isCollectionEmpty(collection)) {
            throw nullSafeGet(exceptionSupplier);
        }
    }

    // ------------------------------------------ not empty map --------------------------------------------------------

    /**
     * 断言map包含元素
     *
     * @param map     待检查map
     * @param message 断言失败时使用的异常消息
     */
    public static void notEmpty(Map<?, ?> map, String message) {
        if (isMapEmpty(map)) {
            throw new ServiceException(500,message);
        }
    }

    /**
     * 断言map包含元素
     *
     * @param map               待检查map
     * @param exceptionSupplier 断言失败时使用的异常Supplier
     */
    public static void notEmpty(Map<?, ?> map, Supplier<? extends RuntimeException> exceptionSupplier) {
        if (isMapEmpty(map)) {
            throw nullSafeGet(exceptionSupplier);
        }
    }

    // ------------------------------------------ is empty map ---------------------------------------------------------

    /**
     * 断言map不包含元素
     *
     * @param map     待检查map
     * @param message 断言失败时使用的异常消息
     */
    public static void isEmpty(Map<?, ?> map, String message) {
        if (!isMapEmpty(map)) {
            throw new ServiceException(500,message);
        }
    }

    /**
     * 断言一个 map 不包含元素
     *
     * @param map               待检查map
     * @param exceptionSupplier 断言失败时使用的异常Supplier
     */
    public static void isEmpty(Map<?, ?> map, Supplier<? extends RuntimeException> exceptionSupplier) {
        if (!isMapEmpty(map)) {
            throw nullSafeGet(exceptionSupplier);
        }
    }

    // ------------------------------------------------ private --------------------------------------------------------

    /**
     * 判断集合是否为空
     *
     * @param collection 集合
     * @return 为空则true
     */
    private static boolean isCollectionEmpty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }

    /**
     * 判断 Map是否为空
     *
     * @param map Map
     * @return 为空则true
     */
    private static boolean isMapEmpty(Map<?, ?> map) {
        return map == null || map.isEmpty();
    }


    /**
     * 判断数组是否为空
     *
     * @param array 数组
     * @return 为空则true
     */
    private static boolean isArrayEmpty(Object[] array) {
        return array == null || array.length == 0;
    }

    /**
     * 判断CharSequence是否是nul或者空字符串
     *
     * @param cs 待判断CharSequence
     * @return 为null或者空则true
     */
    private static boolean isCharSequenceEmpty(CharSequence cs) {
        return cs == null || cs.length() == 0;
    }

    /**
     * 安全的获取消息Supplier
     *
     * @param exceptionSupplier 消息Supplier
     * @return 消息
     */
    private static RuntimeException nullSafeGet(Supplier<? extends RuntimeException> exceptionSupplier) {
        return exceptionSupplier != null ? exceptionSupplier.get() : new ServiceException(500,"exceptionSupplier is null");
    }

    /**
     * 判断字符串是否为null和空字符串(包含全空格字符串)
     *
     * @param cs 需要判断的字符串
     * @return 是否是null和空字符串
     */
    private static boolean isBlankCharSequence(final CharSequence cs) {
        final int strLen = length(cs);
        if (strLen == 0) {
            return true;
        }
        for (int i = 0; i < strLen; i++) {
            if (!Character.isWhitespace(cs.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断字符串是否不为null和不是空字符串(包含全空格字符串)
     *
     * @param cs 需要判断的字符串
     * @return 是否是null和空字符串
     */
    private static boolean isNotBlankCharSequence(final CharSequence cs) {
        return !isBlankCharSequence(cs);
    }

    /**
     * 获取CharSequence长度,null值为0
     *
     * @param cs 待判断CharSequence
     * @return 长度
     */
    private static int length(final CharSequence cs) {
        return cs == null ? 0 : cs.length();
    }
}
