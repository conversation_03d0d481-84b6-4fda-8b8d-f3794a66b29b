package com.rs.framework.mybatis.aspec;

import com.rs.framework.common.aspec.base.BaseRestControllerAspect;
import com.rs.framework.mybatis.processor.FormatProcessor;
import com.rs.framework.mybatis.processor.QueryProcessor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * @ClassName DatabaseFieldProcessorAspect
 * 
 * <AUTHOR>
 * @Date 2025/3/22 10:50
 * @Version 1.0
 */
@Aspect
@Component
public class QueryProcessorAspect extends BaseRestControllerAspect<QueryProcessor> {

    @Autowired
    public QueryProcessorAspect(QueryProcessor processor) {
        super.processor = processor;
    }


    @Override
    protected Object processResult(Object result) throws Throwable {
        return processor.process(result);
    }
}
