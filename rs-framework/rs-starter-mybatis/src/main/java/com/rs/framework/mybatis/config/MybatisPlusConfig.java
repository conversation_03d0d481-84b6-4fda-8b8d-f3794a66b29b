package com.rs.framework.mybatis.config;

import cn.hutool.db.GlobalDbConfig;
import com.baomidou.dynamic.datasource.creator.DataSourceProperty;
import com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator;
import com.baomidou.dynamic.datasource.creator.druid.DruidConfig;
import com.baomidou.dynamic.datasource.provider.AbstractJdbcDataSourceProvider;
import com.baomidou.dynamic.datasource.provider.DynamicDataSourceProvider;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.bsp.common.util.SnowflakeIdUtil;
import com.rs.framework.mybatis.core.handler.DefaultDBFieldHandler;
import com.rs.framework.mybatis.util.DatasourceUtil;
import lombok.extern.log4j.Log4j2;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * MyBatisPlus配置类
 * <AUTHOR>
 *
 */
@Configuration
@MapperScan(basePackages = {"com.bsp.**.dao", "com.rs.**.dao","com.gosun.**.dao"})
@Log4j2
public class MybatisPlusConfig {
    static {
        System.setProperty("druid.mysql.usePingMethod","false");
    }
    @Autowired
    private DynamicDataSourceProperties dynamicDataSourceProperties;
    // 数据源创建
    @Resource
    private DefaultDataSourceCreator dataSourceCreator;
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {

        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor();
        //paginationInnerInterceptor.setDbType(DbType.MYSQL);
        paginationInnerInterceptor.setOptimizeJoin(Boolean.TRUE);

        interceptor.addInnerInterceptor(paginationInnerInterceptor);
        return interceptor;
    }

//    @Bean
//    public ConfigurationCustomizer  configurationCustomizer() {
//    	return new ConfigurationCustomizer() {
//			@Override
//			public void customize(MybatisConfiguration configuration) {
//				configuration.setObjectWrapperFactory(new MapWrapperFactory());
//			}
//		};
//    }

    /**
     * 自定义 id 生成器
     *
     * @return
     */
    @Bean
    public IdentifierGenerator identifierGenerator() {
        return new CustomIdGenerator();
    }

    public static class CustomIdGenerator implements IdentifierGenerator{
        @Override
        public Number nextId(Object entity) {
            return null;
        }
        @Override
        public String nextUUID(Object entity) {
            return SnowflakeIdUtil.getGuid();
        }
    }

    @Bean
    public MetaObjectHandler defaultMetaObjectHandler() {
        return new DefaultDBFieldHandler(); // 自动填充参数类
    }

    @Bean
    public DynamicDataSourceProvider dynamicDataSourceProvider() {
        GlobalDbConfig.setCaseInsensitive(false);    // 自定义表单hutool包配置，设置返回的字段名大小写敏感
        DataSourceProperty datasource = dynamicDataSourceProperties.getDatasource().get(GlobalConstant.DEFAULT_DATASOURCE_KEY);
        log.info("默认数据源：{}，{}，{}", datasource.getUrl(), datasource.getUsername(), datasource.getPassword());
        return new AbstractJdbcDataSourceProvider(dataSourceCreator, datasource.getUrl(), datasource.getUsername(), datasource.getPassword()) {

            @Override
            protected Map<String, DataSourceProperty> executeStmt(Statement statement) throws SQLException {
                Map<String, DataSourceProperty> map = new HashMap<>(16);
                // yml配置的数据源
                Map<String, DataSourceProperty> datasourceMap = dynamicDataSourceProperties.getDatasource();
                log.info("yml配置的数据源初始化完成，共加载{}个数据源", datasourceMap.size());
                for (DataSourceProperty dataSourceProperty : datasourceMap.values()) {
                    // 测试连接，如果连接不上则跳过，避免启动失败
                    log.info("测试数据源连接：{}", dataSourceProperty.getUrl());
                    try {
                        if (!DatasourceUtil.testConnection(dataSourceProperty.getUrl(), dataSourceProperty.getUsername(), dataSourceProperty.getPassword())) {
                            continue;
                        }
                    } catch (Exception e) {
                        continue;
                    }
                    log.info("数据源连接测试通过，添加到数据源池中");

                    DruidConfig druidConfig = dataSourceProperty.getDruid();
                    druidConfig.setInitialSize(10); // 初始化大小
                    druidConfig.setMaxActive(200);// 最大连接池
                    druidConfig.setMinIdle(10);// 最小连接池
                    druidConfig.setMaxWait(6000); //最大等待超时时间
                    druidConfig.setPoolPreparedStatements(false); // 是否缓存preparedStatement，也就是PSCache  官方建议MySQL下建议关闭   个人建议如果想用SQL防火墙 建议打开
                    druidConfig.setMaxPoolPreparedStatementPerConnectionSize(20);//是否缓存preparedStatement，也就是PSCache  官方建议MySQL下建议关闭   个人建议如果想用SQL防火墙 建议打开
                    druidConfig.setTimeBetweenEvictionRunsMillis(6000L);// 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
                    druidConfig.setMinEvictableIdleTimeMillis(30000L); //  配置一个连接在池中最小生存的时间，单位是毫秒
                    if (dataSourceProperty.getUrl().contains("oracle")) {
                        druidConfig.setValidationQuery("SELECT 1 FROM DUAL"); //测试链接 如果是oracle 语句不一样
                    } else {
                        druidConfig.setValidationQuery("SELECT 1 "); //测试链接 语句
                    }
                    druidConfig.setTestWhileIdle(true);
                    druidConfig.setTestOnReturn(false);
                    druidConfig.setTestOnBorrow(false);
                    druidConfig.setFilters("stat,slf4j"); // #   配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
                    druidConfig.setUseGlobalDataSourceStat(true);
                    Properties properties = new Properties();
                    properties.put("druid.stat.mergeSql", true); //打开mergeSql功能
                    properties.put("druid.stat.slowSqlMillis", true); // 打开慢sql 记录功能
                    druidConfig.setConnectionProperties(properties);
                }
                map.putAll(datasourceMap);
                log.info("数据源初始化完成，共加载{}个数据源", map.size());
                return map;
            }
        };
    }

}
