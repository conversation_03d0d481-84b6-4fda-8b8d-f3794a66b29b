package com.rs.framework.mybatis.runner;

import cn.hutool.db.Entity;
import cn.hutool.db.Page;
import cn.hutool.db.PageResult;
import cn.hutool.db.SqlConnRunner;
import cn.hutool.db.dialect.Dialect;
import cn.hutool.db.sql.SqlBuilder;
import com.rs.framework.mybatis.handler.RsPageResultHandler;

import java.sql.Connection;
import java.sql.SQLException;

public class RsSqlConnRunner extends SqlConnRunner {
    public RsSqlConnRunner(Dialect dialect) {
        super(dialect);
    }

    public RsSqlConnRunner(String driverClassName) {
        super(driverClassName);
    }

    @Override
    public PageResult<Entity> page(Connection conn, SqlBuilder sqlBuilder, Page page) throws SQLException {
        final RsPageResultHandler pageResultHandler = new RsPageResultHandler(
                new PageResult<>(page.getPageNumber(), page.getPageSize(), (int) count(conn, sqlBuilder)),
                this.caseInsensitive);
        return page(conn, sqlBuilder, page, pageResultHandler);
    }


}
