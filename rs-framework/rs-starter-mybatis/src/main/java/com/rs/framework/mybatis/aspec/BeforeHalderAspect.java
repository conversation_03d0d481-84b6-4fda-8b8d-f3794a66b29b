package com.rs.framework.mybatis.aspec;

import com.rs.framework.mybatis.annotation.BeforeHalder;
import com.rs.framework.mybatis.processor.BeforeHalderProcessor;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class BeforeHalderAspect {

    @Autowired
    private BeforeHalderProcessor beforeHalderProcessor;

    @Before("@annotation(beforeHalder)")
    public void beforeAdvice(JoinPoint joinPoin, BeforeHalder beforeHalder) throws Throwable {
        // 处理注解逻辑，传入参数和返回值
        beforeHalderProcessor.process(joinPoin, beforeHalder);
    }
}
