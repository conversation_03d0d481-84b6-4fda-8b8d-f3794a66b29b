package com.rs.framework.mybatis.util;

import cn.hutool.extra.template.Template;
import cn.hutool.extra.template.TemplateConfig;
import cn.hutool.extra.template.TemplateEngine;
import cn.hutool.extra.template.TemplateUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * @ClassName DicUtil
 * <AUTHOR>
 * @Date 2025/3/28 14:20
 * @Version 1.0
 */
@Slf4j
public class SqlUtil {
    public static String getSql(String mark, Object data) {
        String sql = "";
        if (StringUtils.isNotEmpty(mark)) {
            sql = QueryUtil.getFmQuery(mark);
            if (StringUtils.isNotEmpty(sql)) {
                sql = sql.replace("#", "$");
            }
        }
        TemplateEngine engine = TemplateUtil.createEngine(new TemplateConfig());
        Template templateWhere = engine.getTemplate(sql);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.setSerializationInclusion(JsonInclude.Include.ALWAYS);
        Map<String, Object> map = objectMapper.convertValue(data, Map.class);
        sql = templateWhere.render(map);
        return sql;
    }

}
