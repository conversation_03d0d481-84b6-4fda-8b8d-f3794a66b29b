package com.rs.framework.mybatis.validator;

import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import cn.hutool.extra.template.Template;
import cn.hutool.extra.template.TemplateConfig;
import cn.hutool.extra.template.TemplateEngine;
import cn.hutool.extra.template.TemplateUtil;
import com.rs.framework.mybatis.annotation.DbValid;
import com.rs.framework.mybatis.context.ParamterContextHolder;
import com.rs.framework.mybatis.util.DatasourceUtil;

import javax.sql.DataSource;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.sql.SQLException;
import java.util.List;

public class DbValidator implements ConstraintValidator<DbValid, Object> {
    DbValid annotation;
    @Override
    public void initialize(DbValid constraintAnnotation) {
        this.annotation = constraintAnnotation;
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        String datasource = annotation.datasource();
        String sql = annotation.sql();
        TemplateEngine engine = TemplateUtil.createEngine(new TemplateConfig());
        Template templateWhere = engine.getTemplate(sql);
        sql = templateWhere.render(ParamterContextHolder.getContext());
        DataSource ds = DatasourceUtil.getDataSource(datasource);
        Db use = Db.use(ds);
        try {
            List<Entity> query = use.query(sql);
            if (query.size() > 0) {
                return false;
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return true;
    }
}
