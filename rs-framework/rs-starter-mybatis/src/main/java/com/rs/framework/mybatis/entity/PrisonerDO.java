package com.rs.framework.mybatis.entity;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 人员信息
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrisonerDO {

    /**
     * id
     */
    private String id;
    /**
     * pro_code
     */
    private String proCode;
    /**
     * pro_name
     */
    private String proName;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 人员编号
     */
    private String rybh;
    /**
     * 别名
     */
    private String bm;
    /**
     * 备注
     */
    private String bz;
    /**
     * 出所去向
     */
    private String csqx;
    /**
     * 出生日期
     */
    private Date csrq;
    /**
     * 出所时间
     */
    private Date cssj;
    /**
     * 出所原因
     */
    private String csyy;
    /**
     * 床位号
     */
    private String cwh;
    /**
     * 单位代码
     */
    private String dwdm;
    /**
     * 风险等级
     */
    private String fxdj;
    /**
     * 国籍
     */
    private String gj;
    /**
     * 关押期限
     */
    private Date gyqx;
    /**
     * 工作单位
     */
    private String gzdw;
    /**
     * 户籍地
     */
    private String hjd;
    /**
     * 户籍地详址
     */
    private String hjdxz;
    /**
     * 婚姻状况
     */
    private String hyzk;
    /**
     * 籍贯
     */
    private String jg;
    /**
     * 经办人
     */
    private String jbr;
    /**
     * 经办时间
     */
    private Date jbsj;
    /**
     * 简要案情
     */
    private String jyaq;
    /**
     * 健康状况
     */
    private String jkzk;
    /**
     * 监室号
     */
    private String jsh;
    /**
     * 拘留日期
     */
    private Date jlrq;
    /**
     * 民族
     */
    private String mz;
    /**
     * 入所时间
     */
    private Date rssj;
    /**
     * 入所原因
     */
    private String rsyy;
    /**
     * 人员状态
     */
    private String ryzt;
    /**
     * 识别服号
     */
    private String sbfh;
    /**
     * 身份
     */
    private String sf;
    /**
     * 身份核实
     */
    private String sfhs;
    /**
     * 身高
     */
    private String sg;
    /**
     * 收押凭证
     */
    private String sypz;
    /**
     * 收押凭证文书号
     */
    private String sypzwsh;
    /**
     * 同案编号
     */
    private String tabh;
    /**
     * 特殊身份
     */
    private String tssf;
    /**
     * 体重
     */
    private String tz;
    /**
     * 特长(专长)
     */
    private String tc;
    /**
     * 文化程度
     */
    private String whcd;
    /**
     * 姓名
     */
    private String xm;
    /**
     * 姓名拼音
     */
    private String xmpy;
    /**
     * 现住址
     */
    private String xzz;
    /**
     * 现住址详址
     */
    private String xzzxz;
    /**
     * 足长
     */
    private String zc;
    /**
     * 证件号码
     */
    private String zjhm;
    /**
     * 证件类型
     */
    private String zjlx;
    /**
     * 职务
     */
    private String zw;
    /**
     * 职务级别
     */
    private String zwjb;
    /**
     * 职业
     */
    private String zy;
    /**
     * 政治面貌
     */
    private String zzmm;
    /**
     * 最终处置日期
     */
    private Date zzczrq;
    /**
     * 正面照片
     */
    private String frontPhoto;
    /**
     * 左侧照片
     */
    private String leftPhoto;
    /**
     * 右侧照片
     */
    private String rightPhoto;
    /**
     * 涉嫌罪名
     */
    private String sxzm;
    /**
     * 档案编号
     */
    private String dabh;
    /**
     * 办案单位
     */
    private String badw;
    /**
     * 办案单位类型
     */
    private String badwlx;
    /**
     * 办案人
     */
    private String bar;
    /**
     * 办案人联系方式
     */
    private String barlxff;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 监区id
     */
    private String areaId;
    /**
     * 性别
     */
    private String xb;
    /**
     * 诉讼环节
     */
    private String sshj;
    /**
     * 管理类别
     */
    private String gllb;
    /**
     * 最终处置结果
     */
    private String zzczjg;
    /**
     * 刑期起始日期
     */
    private Date xqqsrq;
    /**
     * 刑期截止日期
     */
    private Date xqjzrq;
    /**
     * 案件编号
     */
    private String ajbh;
    /**
     * 案件类别
     */
    private String ajlb;
    /**
     * 送押单位
     */
    private String sydw;


}
