package com.rs.framework.mybatis.processor;

import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.annotation.*;
import com.rs.framework.common.util.object.ClassUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Date;

@Component
public class DefaultValueProcessor {
    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultValueProcessor.class);
    private static final int MAX_RECURSION_DEPTH = 5;

    @Autowired
    private ApplicationContext applicationContext;

    public void process(Object object) throws IllegalAccessException {
        process(object, 0);
    }

    private void process(Object object, int depth) throws IllegalAccessException {
        if (object == null) {
            return;
        }

        // 检查递归深度，防止栈溢出
        if (depth >= MAX_RECURSION_DEPTH) {
            LOGGER.warn("递归深度达到最大限制 {}，停止处理对象: {}", MAX_RECURSION_DEPTH, object.getClass().getName());
            return;
        }

        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field : fields) {
            try {
                if (field.isAnnotationPresent(DefaultValueDate.class)) {
                    processDefaultValueDate(field, object);
                } else if (field.isAnnotationPresent(DefaultValueInteger.class)) {
                    processDefaultValueInteger(field, object);
                } else if (field.isAnnotationPresent(DefaultValueString.class)) {
                    processDefaultValueString(field, object);
                } else if (field.isAnnotationPresent(SessionUserIdCard.class)) {
                    processSessionUser(field, object);
                } else if (field.isAnnotationPresent(SessionUserName.class)) {
                    processSessionUserName(field, object);
                } else if (ClassUtils.isComplexType4(field.getType())) {
                    field.setAccessible(true);
                    Object fieldValue = field.get(object);
                    if (fieldValue != null) {
                        if (fieldValue instanceof Collection) {
                            for (Object item : (Collection<?>) fieldValue) {
                                if ((ClassUtils.isComplexType4(item.getClass()))) {
                                    process(item, depth + 1);
                                }
                            }
                        } else {
                            if ((ClassUtils.isComplexType4(fieldValue.getClass()))) {
                                process(fieldValue, depth + 1);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                LOGGER.error("处理默认值失败", e);
            }
        }
    }


    private void processDefaultValueDate(Field field, Object object) throws IllegalAccessException {
        DefaultValueDate annotation = field.getAnnotation(DefaultValueDate.class);
        field.setAccessible(true);
        if (field.get(object) == null) {
            String value = annotation.value();
            Class<?> serviceClass = annotation.service();
            String methodName = annotation.method();
            String paramField = annotation.paramField();

            if (serviceClass != Object.class && !methodName.isEmpty()) {
                try {
                    Object serviceBean = applicationContext.getBean(serviceClass);
                    Method method;
                    if (!paramField.isEmpty()) {
                        // 获取带有参数的方法
                        Field paramObjectField = object.getClass().getDeclaredField(paramField);
                        paramObjectField.setAccessible(true);
                        Object paramObject = paramObjectField.get(object);
                        if (paramObject != null) {
                            Class<?> paramType = paramObjectField.getType();
                            method = serviceClass.getMethod(methodName, paramType);
                            Object defaultValue = method.invoke(serviceBean, paramObject);
                            field.set(object, defaultValue);
                        } else {
                            LOGGER.warn("参数字段 {} 为空，跳过方法调用", paramField);
                        }
                    } else {
                        // 获取不带参数的方法
                        method = serviceClass.getMethod(methodName);
                        Object defaultValue = method.invoke(serviceBean);
                        field.set(object, defaultValue);
                    }
                } catch (Exception e) {
                    LOGGER.error("动态调用服务方法失败", e);
                }
            } else if (!value.isEmpty()) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    field.set(object, sdf.parse(value));
                } catch (ParseException e) {
                    LOGGER.error("解析日期格式失败: " + value, e);
                    field.set(object, new Date());
                }
            } else {
                field.set(object, new Date());
            }
        }
    }

    private void processDefaultValueInteger(Field field, Object object) throws IllegalAccessException {
        DefaultValueInteger annotation = field.getAnnotation(DefaultValueInteger.class);
        field.setAccessible(true);
        if (field.get(object) == null) {
            int value = annotation.value();
            Class<?> serviceClass = annotation.service();
            String methodName = annotation.method();
            String paramField = annotation.paramField();

            if (serviceClass != Object.class && !methodName.isEmpty()) {
                try {
                    Object serviceBean = applicationContext.getBean(serviceClass);
                    Method method;
                    if (!paramField.isEmpty()) {
                        // 获取带有参数的方法
                        Field paramObjectField = object.getClass().getDeclaredField(paramField);
                        paramObjectField.setAccessible(true);
                        Object paramObject = paramObjectField.get(object);
                        if (paramObject != null) {
                            Class<?> paramType = paramObjectField.getType();
                            method = serviceClass.getMethod(methodName, paramType);
                            Object defaultValue = method.invoke(serviceBean, paramObject);
                            field.set(object, defaultValue);
                        } else {
                            LOGGER.warn("参数字段 {} 为空，跳过方法调用", paramField);
                        }
                    } else {
                        // 获取不带参数的方法
                        method = serviceClass.getMethod(methodName);
                        Object defaultValue = method.invoke(serviceBean);
                        field.set(object, defaultValue);
                    }
                } catch (Exception e) {
                    LOGGER.error("动态调用服务方法失败", e);
                }
            } else {
                field.set(object, value);
            }
        }
    }

    private void processDefaultValueString(Field field, Object object) throws IllegalAccessException {
        DefaultValueString annotation = field.getAnnotation(DefaultValueString.class);
        field.setAccessible(true);
        if (field.get(object) == null || "".equals(field.get(object).toString())) {
            String value = annotation.value();
            Class<?> serviceClass = annotation.service();
            String methodName = annotation.method();
            String paramField = annotation.paramField();
            String staticParam = annotation.staticParam();

            if (serviceClass != Object.class && !methodName.isEmpty()) {
                try {
                    Object serviceBean = applicationContext.getBean(serviceClass);
                    Method method;
                    if (!staticParam.isEmpty()) {
                        // 获取带有静态参数的方法
                        method = serviceClass.getMethod(methodName, String.class);
                        Object defaultValue = method.invoke(serviceBean, staticParam);
                        field.set(object, defaultValue);
                    } else if (!paramField.isEmpty()) {
                        // 获取带有参数的方法
                        Field paramObjectField = object.getClass().getDeclaredField(paramField);
                        paramObjectField.setAccessible(true);
                        Object paramObject = paramObjectField.get(object);
                        if (paramObject != null) {
                            Class<?> paramType = paramObjectField.getType();
                            method = serviceClass.getMethod(methodName, paramType);
                            Object defaultValue = method.invoke(serviceBean, paramObject);
                            field.set(object, defaultValue);
                        } else {
                            LOGGER.warn("参数字段 {} 为空，跳过方法调用", paramField);
                        }
                    } else {
                        // 获取不带参数的方法
                        method = serviceClass.getMethod(methodName);
                        Object defaultValue = method.invoke(serviceBean);
                        field.set(object, defaultValue);
                    }
                } catch (Exception e) {
                    LOGGER.error("动态调用服务方法失败", e);
                }
            } else {
                field.set(object, value);
            }
        }
    }

    private void processSessionUser(Field field, Object object) throws IllegalAccessException {
        SessionUserIdCard annotation = field.getAnnotation(SessionUserIdCard.class);
        field.setAccessible(true);
        if (field.get(object) == null || "".equals(field.get(object).toString())) {
            String value = annotation.value();
            Class<?> serviceClass = annotation.service();
            String methodName = annotation.method();
            String paramField = annotation.paramField();

            if (serviceClass != Object.class && !methodName.isEmpty()) {
                try {
                    Object serviceBean = applicationContext.getBean(serviceClass);
                    Method method;
                    if (!paramField.isEmpty()) {
                        // 获取带有参数的方法
                        Field paramObjectField = object.getClass().getDeclaredField(paramField);
                        paramObjectField.setAccessible(true);
                        Object paramObject = paramObjectField.get(object);
                        if (paramObject != null) {
                            Class<?> paramType = paramObjectField.getType();
                            method = serviceClass.getMethod(methodName, paramType);
                            Object defaultValue = method.invoke(serviceBean, paramObject);
                            field.set(object, defaultValue);
                        } else {
                            LOGGER.warn("参数字段 {} 为空，跳过方法调用", paramField);
                        }
                    } else {
                        // 获取不带参数的方法
                        method = serviceClass.getMethod(methodName);
                        Object defaultValue = method.invoke(serviceBean);
                        field.set(object, defaultValue);
                    }
                } catch (Exception e) {
                    LOGGER.error("动态调用服务方法失败", e);
                }
            } else {
                try {
                    SessionUser user = SessionUserUtil.getSessionUser();
                    if (user != null) {
                        field.set(object, user.getIdCard());
                    } else {
                        field.set(object, value);
                    }
                } catch (Exception e) {
                    LOGGER.error("获取用户信息失败", e);
                }
            }
        }
    }

    private void processSessionUserName(Field field, Object object) throws IllegalAccessException {
        SessionUserName annotation = field.getAnnotation(SessionUserName.class);
        field.setAccessible(true);
        if (field.get(object) == null || "".equals(field.get(object).toString())) {
            String value = annotation.value();
            Class<?> serviceClass = annotation.service();
            String methodName = annotation.method();
            String paramField = annotation.paramField();

            if (serviceClass != Object.class && !methodName.isEmpty()) {
                try {
                    Object serviceBean = applicationContext.getBean(serviceClass);
                    Method method;
                    if (!paramField.isEmpty()) {
                        // 获取带有参数的方法
                        Field paramObjectField = object.getClass().getDeclaredField(paramField);
                        paramObjectField.setAccessible(true);
                        Object paramObject = paramObjectField.get(object);
                        if (paramObject != null) {
                            Class<?> paramType = paramObjectField.getType();
                            method = serviceClass.getMethod(methodName, paramType);
                            Object defaultValue = method.invoke(serviceBean, paramObject);
                            field.set(object, defaultValue);
                        } else {
                            LOGGER.warn("参数字段 {} 为空，跳过方法调用", paramField);
                        }
                    } else {
                        // 获取不带参数的方法
                        method = serviceClass.getMethod(methodName);
                        Object defaultValue = method.invoke(serviceBean);
                        field.set(object, defaultValue);
                    }
                } catch (Exception e) {
                    LOGGER.error("动态调用服务方法失败", e);
                }
            } else {
                try {
                    SessionUser user = SessionUserUtil.getSessionUser();
                    if (user != null) {
                        field.set(object, user.getName());
                    } else {
                        field.set(object, value);
                    }
                } catch (Exception e) {
                    LOGGER.error("获取用户信息失败", e);
                }
            }
        }
    }

}
