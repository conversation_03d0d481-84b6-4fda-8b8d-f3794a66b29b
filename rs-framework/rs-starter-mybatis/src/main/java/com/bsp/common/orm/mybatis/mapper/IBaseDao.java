//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.bsp.common.orm.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import java.util.Map;

import com.rs.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface IBaseDao<T> extends BaseMapperX<T> {
    List<T> getListBy(Map<String, Object> paramMap);

    List<T> findListBy(Map<String, Object> paramMap);

    Page<T> getPageBy(Page<?> page, @Param("cm") Map<String, Object> paramMap);

    @Select({"${sql}"})
    List<Map<String, Object>> getResultBySql(@Param("sql") String sql);
}

