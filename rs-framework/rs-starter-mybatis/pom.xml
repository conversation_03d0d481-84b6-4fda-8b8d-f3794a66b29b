<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.rs</groupId>
		<artifactId>rs-framework</artifactId>
		<version>${rs.version}</version>
	</parent>

	<artifactId>rs-starter-mybatis</artifactId>
	<packaging>jar</packaging>
	<name>${project.artifactId}</name>
	<description>监所管理-技术组件-mybatis组件，提供mybatis相关依赖及配置</description>

	<dependencies>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all-u</artifactId>
		</dependency>
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-common</artifactId>
		</dependency>

		<dependency>
			<groupId>com.bsp</groupId>
			<artifactId>bsp-common</artifactId>
			<exclusions>
				<exclusion>
					<groupId>cn.hutool</groupId>
		  			<artifactId>hutool-all-u</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>dynamic-datasource-spring-boot-starter</artifactId> <!-- 多数据源 -->
		</dependency>

		<dependency>
			<groupId>com.github.yulichang</groupId>
			<artifactId>mybatis-plus-join-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.bsp</groupId>
			<artifactId>bsp-security</artifactId>
		</dependency>

		<!-- 技术组件 begin -->
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-starter-rpc</artifactId>
		</dependency>
		<!-- 技术组件 end -->

	</dependencies>
</project>
