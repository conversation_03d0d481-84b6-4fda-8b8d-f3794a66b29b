package com.rs.module.rgf.controller.admin.rm.vo;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rs.module.rgf.entity.rm.QuestionConfirmDO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 需求问题 Response VO")
@Data
public class QuestionRespVO {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("是否删除(0:否,1:是)")
    private String isDel;
    @ApiModelProperty("添加时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date addTime;
    @ApiModelProperty("添加人")
    private String addUser;
    @ApiModelProperty("添加人姓名")
    private String addUserName;
    @ApiModelProperty("更新人")
    private String updateUser;
    @ApiModelProperty("所属市级代码")
    private String cityCode;
    @ApiModelProperty("所属市级名称")
    private String cityName;
    @ApiModelProperty("区域代码")
    private String regCode;
    @ApiModelProperty("区域名称")
    private String regName;
    @ApiModelProperty("机构编号")
    private String orgCode;
    @ApiModelProperty("机构名称")
    private String orgName;
    @ApiModelProperty("评审计划Id")
    private String planId;
    @ApiModelProperty("问题标题")
    private String title;
    @ApiModelProperty("问题内容(HTML带标签)")
    private String content;
    @ApiModelProperty("问题内容(纯文本)")
    private String contentText;
    @ApiModelProperty("是否禁用(0否1是)")
    private String isDisabled;
    @ApiModelProperty("状态(0已提交,1已确认,2已关闭)")
    private Short status;
    @ApiModelProperty("回复次数")
    private Short reTimes;
    @ApiModelProperty("最后回复时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastReTime;
    @ApiModelProperty("浏览次数")
    private Short visitTimes;
    @ApiModelProperty("联系方式")
    private String mobile;
    @ApiModelProperty("附件")
    private String attachment;
    @ApiModelProperty("是否允许确认")
    private boolean canConfirm;    
    @ApiModelProperty("问题确认轨迹")
    private List<QuestionConfirmDO> confirmList;
}
