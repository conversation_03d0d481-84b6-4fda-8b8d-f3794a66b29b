package com.rs.module.rgf.controller.admin.rm;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.rgf.controller.admin.rm.vo.ReplyCommentListReqVO;
import com.rs.module.rgf.controller.admin.rm.vo.ReplyCommentPageReqVO;
import com.rs.module.rgf.controller.admin.rm.vo.ReplyCommentRespVO;
import com.rs.module.rgf.controller.admin.rm.vo.ReplyCommentSaveReqVO;
import com.rs.module.rgf.entity.rm.ReplyCommentDO;
import com.rs.module.rgf.entity.uac.UacUserDO;
import com.rs.module.rgf.service.rm.ReplyCommentService;
import com.rs.module.rgf.service.uac.UacUserService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

@Api(tags = "管理后台 - 需求回复评论")
@RestController
@RequestMapping("/rgf/rm/reply-comment")
@Validated
public class ReplyCommentController {

    @Resource
    private ReplyCommentService replyCommentService;
    
    @Resource
    private UacUserService userService;

    @PostMapping("/create")
    @ApiOperation(value = "创建需求回复评论")
    @LogRecordAnnotation(bizModule = "rgf:replyComment:create", operateType = LogOperateType.CREATE, title = "创建需求回复评论",
    success = "创建需求回复评论成功", fail = "创建需求回复评论失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createReplyComment(@Valid @RequestBody ReplyCommentSaveReqVO createReqVO) {
    	createReqVO.setUserName(SessionUserUtil.getSessionUser().getName());
        return success(replyCommentService.createReplyComment(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新需求回复评论")
    @LogRecordAnnotation(bizModule = "rgf:replyComment:update", operateType = LogOperateType.UPDATE, title = "更新需求回复评论",
    success = "更新需求回复评论成功", fail = "更新需求回复评论失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateReplyComment(@Valid @RequestBody ReplyCommentSaveReqVO updateReqVO) {
        replyCommentService.updateReplyComment(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除需求回复评论")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "rgf:replyComment:delete", operateType = LogOperateType.DELETE, title = "删除需求回复评论",
    success = "删除需求回复评论成功", fail = "删除需求回复评论失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteReplyComment(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           replyCommentService.deleteReplyComment(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得需求回复评论")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "rgf:replyComment:get", operateType = LogOperateType.QUERY, title = "获取需求回复评论", bizNo = "{{#id}}", success = "获取需求回复评论成功", fail = "获取需求回复评论失败", extraInfo = "{{#id}}")
    public CommonResult<ReplyCommentRespVO> getReplyComment(@RequestParam("id") String id) {
        ReplyCommentDO replyComment = replyCommentService.getReplyComment(id);
        return success(BeanUtils.toBean(replyComment, ReplyCommentRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得需求回复评论分页")
    @LogRecordAnnotation(bizModule = "rgf:replyComment:page", operateType = LogOperateType.QUERY, title = "获得需求回复评论分页",
    success = "获得需求回复评论分页成功", fail = "获得需求回复评论分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<ReplyCommentRespVO>> getReplyCommentPage(@RequestBody @Valid ReplyCommentPageReqVO pageReqVO) {
        PageResult<ReplyCommentDO> pageResult = replyCommentService.getReplyCommentPage(pageReqVO);
        
        //处理用户照片
    	List<ReplyCommentDO> replyCommentList = pageResult.getList();
    	if(pageReqVO.isShowUserPhoto() && !replyCommentList.isEmpty()) {
    		Set<String> idCards = replyCommentList.stream().filter(item -> StringUtil.isNotEmpty(item.getAddUser()))
    				.map(ReplyCommentDO::getAddUser).collect(Collectors.toSet());
    		List<UacUserDO> userList = userService.getUserPhotoByIdCard(idCards);
    		Map<String, String> userMap = userList.stream().collect(Collectors.toMap(UacUserDO::getIdCard, UacUserDO::getPhoto));
    		if(CollectionUtil.isNotNull(userMap)) {
	    		for(ReplyCommentDO comment : replyCommentList) {
	    			if(userMap.containsKey(comment.getAddUser())) {
	    				comment.setUserPhoto(userMap.get(comment.getAddUser()));
					}		
	    		}
    		}
    	}
    	
        return success(BeanUtils.toBean(pageResult, ReplyCommentRespVO.class));
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得需求回复评论列表")
    @LogRecordAnnotation(bizModule = "rgf:replyComment:list", operateType = LogOperateType.QUERY, title = "获得需求回复评论列表",
    success = "获得需求回复评论列表成功", fail = "获得需求回复评论列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<ReplyCommentRespVO>> getReplyCommentList(@Valid ReplyCommentListReqVO listReqVO) {
    List<ReplyCommentDO> list = replyCommentService.getReplyCommentList(listReqVO);
        return success(BeanUtils.toBean(list, ReplyCommentRespVO.class));
    }

}
