package com.rs.module.rgf.entity.rm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 需求回复评论 DO
 *
 * <AUTHOR>
 */
@TableName("rgf_rm_reply_comment")
@KeySequence("rgf_rm_reply_comment_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReplyCommentDO extends BaseDO {
	private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 评审计划Id
     */
    private String planId;
    /**
     * 问题ID
     */
    private String questionId;
    /**
     * 评论ID
     */
    private String commentId;
    /**
     * 被评论用户ID
     */
    private String toUserId;
    /**
     * 被评论用户姓名
     */
    private String toName;
    /**
     * 回复ID
     */
    private String replyId;
    /**
     * 被评论ID
     */
    private String toCommentId;
    /**
     * 评论用户姓名
     */
    private String userName;
    /**
     * 评论内容
     */
    private String content;
    /**
     * 是否禁用(0否1是)
     */
    private String isDisabled;
    /**
     * 支持数
     */
    private Short supportTimes;
    /**
     * 反对数
     */
    private Short againstTimes;
    /**
     * 评论数
     */
    private Short commentTimes;

    /**
     * 用户照片
     */
    @TableField(exist = false)
    private String userPhoto;
}
