package com.rs.module.rgf.dao.rm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.cons.CommonConstants;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.rgf.entity.rm.MaterialDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.rgf.controller.admin.rm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 需求素材 Dao
*
* <AUTHOR>
*/
@Mapper
public interface MaterialDao extends IBaseDao<MaterialDO> {


    default PageResult<MaterialDO> selectPage(MaterialPageReqVO reqVO) {
        Page<MaterialDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<MaterialDO> materialPage = selectPage(page, new LambdaQueryWrapperX<MaterialDO>()
            .betweenIfPresent(MaterialDO::getAddTime, reqVO.getAddTime())
            .eqIfPresent(MaterialDO::getAddUser, reqVO.getAddUser())
            .eqIfPresent(MaterialDO::getUpdateUser, reqVO.getUpdateUser())
            .eqIfPresent(MaterialDO::getCityCode, reqVO.getCityCode())
            .likeIfPresent(MaterialDO::getCityName, reqVO.getCityName())
            .eqIfPresent(MaterialDO::getRegCode, reqVO.getRegCode())
            .likeIfPresent(MaterialDO::getRegName, reqVO.getRegName())
            .eqIfPresent(MaterialDO::getOrgCode, reqVO.getOrgCode())
            .likeIfPresent(MaterialDO::getOrgName, reqVO.getOrgName())
            .likeIfPresent(MaterialDO::getMaterName, reqVO.getMaterName())
            .eqIfPresent(MaterialDO::getSubSystem, reqVO.getSubSystem())
            .eqIfPresent(MaterialDO::getAttachment, reqVO.getAttachment())
            .eqIfPresent(MaterialDO::getUrl, reqVO.getUrl())
            .eqIfPresent(MaterialDO::getRemark, reqVO.getRemark())
            .orderByDesc(MaterialDO::getId));
            return new PageResult<>(materialPage.getRecords(), materialPage.getTotal());
    }
    default List<MaterialDO> selectList(MaterialListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MaterialDO>()
            .betweenIfPresent(MaterialDO::getAddTime, reqVO.getAddTime())
            .eqIfPresent(MaterialDO::getAddUser, reqVO.getAddUser())
            .eqIfPresent(MaterialDO::getUpdateUser, reqVO.getUpdateUser())
            .eqIfPresent(MaterialDO::getCityCode, reqVO.getCityCode())
            .likeIfPresent(MaterialDO::getCityName, reqVO.getCityName())
            .eqIfPresent(MaterialDO::getRegCode, reqVO.getRegCode())
            .likeIfPresent(MaterialDO::getRegName, reqVO.getRegName())
            .eqIfPresent(MaterialDO::getOrgCode, reqVO.getOrgCode())
            .likeIfPresent(MaterialDO::getOrgName, reqVO.getOrgName())
            .likeIfPresent(MaterialDO::getMaterName, reqVO.getMaterName())
            .eqIfPresent(MaterialDO::getSubSystem, reqVO.getSubSystem())
            .eqIfPresent(MaterialDO::getAttachment, reqVO.getAttachment())
            .eqIfPresent(MaterialDO::getUrl, reqVO.getUrl())
            .eqIfPresent(MaterialDO::getRemark, reqVO.getRemark())
        .orderByDesc(MaterialDO::getId));    }


    }
