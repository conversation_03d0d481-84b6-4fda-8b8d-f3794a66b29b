package com.rs.module.rgf.service.rm;

import java.util.List;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.rgf.entity.rm.QuestionConfirmDO;

/**
 * 需求问题确认 Service 接口
 * <AUTHOR>
 * @date 2025年4月8日
 */
public interface QuestionConfirmService extends IBaseService<QuestionConfirmDO> {

    /**
     * 创建需求问题确认
     *
     * @param questionConfirmDO 创建信息
     */
	void createQuestionConfirm(QuestionConfirmDO questionConfirmDO);

    /**
     * 删除需求问题确认
     *
     * @param id 编号
     */
    void deleteQuestionConfirm(String id);

    /**
     * 获得需求问题确认
     *
     * @param id 编号
     * @return 需求问题
     */
    QuestionConfirmDO getQuestionConfirm(String id);
    
    /**
     * 获取需求问题的确认记录
     * @param questionId String 问题Id
     * @return List<QuestionConfirmDO>
     */
    List<QuestionConfirmDO> selectListByQuestionId(String questionId);

    /**
     * 删除问题的确认记录
     * @param questionId String 问题Id
     * @return int
     */
    int deleteByQuestionId(String questionId);
}
