package com.rs.module.rgf.controller.admin.rm.vo;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 需求素材列表 Request VO")
@Data
public class MaterialListReqVO {

    @ApiModelProperty("是否删除(0:否,1:是)")
    private String isDel;

    @ApiModelProperty("添加时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date[] addTime;

    @ApiModelProperty("添加人")
    private String addUser;

    @ApiModelProperty("更新人")
    private String updateUser;

    @ApiModelProperty("所属市级代码")
    private String cityCode;

    @ApiModelProperty("所属市级名称")
    private String cityName;

    @ApiModelProperty("区域代码")
    private String regCode;

    @ApiModelProperty("区域名称")
    private String regName;

    @ApiModelProperty("机构编号")
    private String orgCode;

    @ApiModelProperty("机构名称")
    private String orgName;

    @ApiModelProperty("素材名称")
    private String materName;

    @ApiModelProperty("所属系统")
    private String subSystem;

    @ApiModelProperty("附件")
    private String attachment;
    
    @ApiModelProperty("状态(0已提交,1已部署)")
    private Short status;

    @ApiModelProperty("在线访问地址")
    private String url;

    @ApiModelProperty("素材简介")
    private String remark;

}
