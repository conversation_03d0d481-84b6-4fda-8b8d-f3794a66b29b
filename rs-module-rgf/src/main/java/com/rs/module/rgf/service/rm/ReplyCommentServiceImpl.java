package com.rs.module.rgf.service.rm;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.rgf.controller.admin.rm.vo.ReplyCommentListReqVO;
import com.rs.module.rgf.controller.admin.rm.vo.ReplyCommentPageReqVO;
import com.rs.module.rgf.controller.admin.rm.vo.ReplyCommentSaveReqVO;
import com.rs.module.rgf.dao.rm.ReplyCommentDao;
import com.rs.module.rgf.entity.rm.QuestionReplyDO;
import com.rs.module.rgf.entity.rm.ReplyCommentDO;


/**
 * 需求回复评论 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ReplyCommentServiceImpl extends BaseServiceImpl<ReplyCommentDao, ReplyCommentDO> implements ReplyCommentService {

    @Resource
    private ReplyCommentDao replyCommentDao;
    
    @Resource
    private QuestionReplyService questionReplyService;

    @Override
    public String createReplyComment(ReplyCommentSaveReqVO createReqVO) {
        // 插入
        ReplyCommentDO replyComment = BeanUtils.toBean(createReqVO, ReplyCommentDO.class);
        replyCommentDao.insert(replyComment);
        
        // 更新问题回复评论数
        QuestionReplyDO questionReply = questionReplyService.getById(createReqVO.getReplyId());
        if(questionReply != null) {
        	questionReply.setCommentTimes((short)(questionReply.getCommentTimes() + 1));
            questionReplyService.updateById(questionReply);
        }
        
        // 更新回复评论评论数
        if(StringUtil.isNotEmpty(createReqVO.getCommentId())) {
        	ReplyCommentDO toReplyComment = getById(createReqVO.getCommentId());
        	if(toReplyComment != null) {
        		toReplyComment.setCommentTimes((short)(toReplyComment.getCommentTimes() + 1));
                updateById(toReplyComment);
            }
        }
        
        // 返回
        return replyComment.getId();
    }

    @Override
    public void updateReplyComment(ReplyCommentSaveReqVO updateReqVO) {
        // 校验存在
        validateReplyCommentExists(updateReqVO.getId());
        // 更新
        ReplyCommentDO updateObj = BeanUtils.toBean(updateReqVO, ReplyCommentDO.class);
        replyCommentDao.updateById(updateObj);
    }

    @Override
    public void deleteReplyComment(String id) {
        // 校验存在
        validateReplyCommentExists(id);
        // 删除
        replyCommentDao.deleteById(id);
    }

    private void validateReplyCommentExists(String id) {
        if (replyCommentDao.selectById(id) == null) {
            throw new ServerException("需求回复评论数据不存在");
        }
    }

    @Override
    public ReplyCommentDO getReplyComment(String id) {
        return replyCommentDao.selectById(id);
    }

    @Override
    public PageResult<ReplyCommentDO> getReplyCommentPage(ReplyCommentPageReqVO pageReqVO) {
        return replyCommentDao.selectPage(pageReqVO);
    }

    @Override
    public List<ReplyCommentDO> getReplyCommentList(ReplyCommentListReqVO listReqVO) {
        return replyCommentDao.selectList(listReqVO);
    }


}
