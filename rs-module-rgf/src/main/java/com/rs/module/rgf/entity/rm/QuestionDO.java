package com.rs.module.rgf.entity.rm;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 需求问题 DO
 *
 * <AUTHOR>
 */
@TableName("rgf_rm_question")
@KeySequence("rgf_rm_question_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionDO extends BaseDO {
	private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 评审计划Id
     */
    private String planId;
    /**
     * 问题标题
     */
    private String title;
    /**
     * 问题内容(HTML带标签)
     */
    private String content;
    /**
     * 问题内容(纯文本)
     */
    private String contentText;
    /**
     * 是否禁用(0否1是)
     */
    private String isDisabled;
    /**
     * 状态(0已提交,1已确认,2已关闭)
     */
    private Short status;
    /**
     * 回复次数
     */
    private Short reTimes;
    /**
     * 最后回复时间
     */
    private Date lastReTime;
    /**
     * 浏览次数
     */
    private Short visitTimes;
    /**
     * 联系方式
     */
    private String mobile;
    /**
     * 下级确认角色
     */
    private String nextConfirmRole;
    /**
     * 确认用户
     */
    private String confirmUser;
    /**
     * 确认用户姓名
     */
    private String confirmUserName;
    /**
     * 确认机构编号
     */
    private String confirmOrgCode;
    /**
     * 确认机构名称
     */
    private String confirmOrgName;
    /**
     * 确认时间
     */
    private Date confirmTime;
    /**
     * 附件
     */
    private String attachment;    
    /**
     * 是否允许确认
     */
    @TableField(exist = false)
    private boolean canConfirm;    
    /**
     * 问题确认轨迹
     */
    @TableField(exist = false)
    private List<QuestionConfirmDO> confirmList;
}
