package com.rs.module.rgf.util;

import com.bsp.common.cache.DicUtil;
import com.fhs.common.utils.StringUtil;
import com.rs.framework.common.util.http.HttpUtils;

/**
 * @ClassName DicUtils
 * <AUTHOR>
 * @Date 2025/6/24 15:39
 * @Version 1.0
 */
public class DicUtils {
    public static String translate(String dicName, String code) {
        String dicCName = DicUtil.translate(HttpUtils.getAppCode(), dicName, code);
        if (StringUtil.isEmpty(dicCName) || "null".equals(dicCName)) {
            dicCName = DicUtil.translate("bsp", dicName, code);
        }
        return dicCName;
    }
}
