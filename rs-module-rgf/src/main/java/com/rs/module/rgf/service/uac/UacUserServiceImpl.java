package com.rs.module.rgf.service.uac;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.bsp.common.cons.CommonConstants;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.rs.framework.mybatis.config.GlobalConstant;
import com.rs.module.rgf.dao.uac.UacUserDao;
import com.rs.module.rgf.entity.uac.UacUserDO;

/**
 * 用户服务实现类
 * <AUTHOR>
 * @date 2025年3月24日
 */
@Service
@DS(GlobalConstant.BSP_DATASOURCE_KEY)
public class UacUserServiceImpl extends BaseServiceImpl<UacUserDao, UacUserDO> implements UacUserService{

	/**
	 * 根据身份证号码查询用户照片
	 * @param idCards Set<String> 身份证号码
	 * @return List<UacUserDO>
	 * <AUTHOR>
	 * @date 2025年3月24日
	 */
	@Override
	public List<UacUserDO> getUserPhotoByIdCard(Set<String> idCards){
		return baseMapper.getUserPhotoByIdCard(idCards);
	}
	
	/**
	 * 根据机构代码与角色编号查询用户信息
	 * @param orgCode String 机构代码
	 * @param roleCode String 角色编号
	 * @return List<UacUserDO>
	 * <AUTHOR>
	 * @date 2025年3月24日
	 */
	public List<UacUserDO> getUserByOrgAndRole(String orgCode, String roleCode) {
		return baseMapper.getUserByOrgAndRole(orgCode, roleCode, null);
	}

	/**
	 * 根据机构代码与角色编号查询用户信息
	 * @param orgCode String 机构代码
	 * @param roleCode String 角色编号
	 * @param post String 岗位
	 * @return List<UacUserDO>
	 * <AUTHOR>
	 * @date 2025年5月24日
	 */
	@Override
	public List<UacUserDO> getUserByOrgAndRole(String orgCode, String roleCode, String post) {
		if(StringUtil.isNotEmpty(post)) {
			List<UacUserDO> userList = new ArrayList<>();
			String[] postArr = StringUtil.splitString(post, CommonConstants.DEFAULT_SPLIT_STR);
			for(String p : postArr) {
				List<UacUserDO> ul = baseMapper.getUserByOrgAndRole(orgCode, roleCode, p);
				userList.addAll(ul);
			}
			return userList.stream().distinct().collect(Collectors.toList());
		}
		
		return null;
	}

	/**
	 * 根据身份证号码获取用户
	 * @param idCard String 身份证号码
	 * @return UacUserDO
	 */
	@Override
	public UacUserDO getUserByIdCard(String idCard) {
		return  baseMapper.selectOne("ID_CARD", idCard);
	}
}
