package com.rs.module.rgf.service.rm;

import java.util.List;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.rgf.controller.admin.rm.vo.MaterialListReqVO;
import com.rs.module.rgf.controller.admin.rm.vo.MaterialPageReqVO;
import com.rs.module.rgf.controller.admin.rm.vo.MaterialSaveReqVO;
import com.rs.module.rgf.entity.rm.MaterialDO;

/**
 * 需求素材 Service 接口
 *
 * <AUTHOR>
 */
public interface MaterialService extends IBaseService<MaterialDO> {

    /**
     * 创建需求素材
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMaterial(@Valid MaterialSaveReqVO createReqVO);

    /**
     * 更新需求素材
     *
     * @param updateReqVO 更新信息
     */
    void updateMaterial(@Valid MaterialSaveReqVO updateReqVO);

    /**
     * 删除需求素材
     *
     * @param id 编号
     */
    void deleteMaterial(String id);

    /**
     * 获得需求素材
     *
     * @param id 编号
     * @return 需求素材
     */
    MaterialDO getMaterial(String id);

    /**
    * 获得需求素材分页
    *
    * @param pageReqVO 分页查询
    * @return 需求素材分页
    */
    PageResult<MaterialDO> getMaterialPage(MaterialPageReqVO pageReqVO);

    /**
    * 获得需求素材列表
    *
    * @param listReqVO 查询条件
    * @return 需求素材列表
    */
    List<MaterialDO> getMaterialList(MaterialListReqVO listReqVO);


}
