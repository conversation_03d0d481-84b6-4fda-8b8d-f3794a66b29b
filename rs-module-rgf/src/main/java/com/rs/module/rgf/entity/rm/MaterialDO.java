package com.rs.module.rgf.entity.rm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 需求素材 DO
 *
 * <AUTHOR>
 */
@TableName("rgf_rm_material")
@KeySequence("rgf_rm_material_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialDO extends BaseDO {
	private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 素材名称
     */
    private String materName;
    /**
     * 所属系统
     */
    private String subSystem;
    /**
     * 附件
     */
    private String attachment;
    /**
     * 状态(0已提交,1已部署)
     */
    private Short status;
    /**
     * 在线访问地址
     */
    private String url;
    /**
     * 素材简介
     */
    private String remark;

}
