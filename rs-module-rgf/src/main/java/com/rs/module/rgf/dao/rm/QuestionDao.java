package com.rs.module.rgf.dao.rm;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.rgf.controller.admin.rm.vo.QuestionListReqVO;
import com.rs.module.rgf.controller.admin.rm.vo.QuestionPageReqVO;
import com.rs.module.rgf.entity.rm.QuestionDO;

/**
 * 需求问题 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionDao extends IBaseDao<QuestionDO> {

	default PageResult<QuestionDO> selectPage(QuestionPageReqVO reqVO) {
		Page<QuestionDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());	
		LambdaQueryWrapperX<QuestionDO> wrapper = new LambdaQueryWrapperX<QuestionDO>()
			.betweenIfPresent(QuestionDO::getAddTime, reqVO.getAddTime())
			.eqIfPresent(QuestionDO::getAddUser, reqVO.getAddUser())
			.likeIfPresent(QuestionDO::getAddUserName, reqVO.getAddUserName())
			.eqIfPresent(QuestionDO::getUpdateUser, reqVO.getUpdateUser())
			.eqIfPresent(QuestionDO::getCityCode, reqVO.getCityCode())
			.likeIfPresent(QuestionDO::getCityName, reqVO.getCityName())
			.eqIfPresent(QuestionDO::getRegCode, reqVO.getRegCode())
			.likeIfPresent(QuestionDO::getRegName, reqVO.getRegName())
			.eqIfPresent(QuestionDO::getOrgCode, reqVO.getOrgCode())
			.likeIfPresent(QuestionDO::getOrgName, reqVO.getOrgName())
			.eqIfPresent(QuestionDO::getPlanId, reqVO.getPlanId())
			.likeIfPresent(QuestionDO::getTitle, reqVO.getTitle())
			.eqIfPresent(QuestionDO::getContent, reqVO.getContent())
			.eqIfPresent(QuestionDO::getContentText, reqVO.getContentText())
			.eqIfPresent(QuestionDO::getIsDisabled, reqVO.getIsDisabled())
			.eqIfPresent(QuestionDO::getStatus, reqVO.getStatus())
			.eqIfPresent(QuestionDO::getReTimes, reqVO.getReTimes())
			.betweenIfPresent(QuestionDO::getLastReTime, reqVO.getLastReTime())
			.eqIfPresent(QuestionDO::getVisitTimes, reqVO.getVisitTimes())
			.eqIfPresent(QuestionDO::getMobile, reqVO.getMobile())
			.eqIfPresent(QuestionDO::getAttachment, reqVO.getAttachment());
		if(reqVO.getOrderFields() != null) {
			page.setOrders(reqVO.getOrderFields());
		}
		else {
			wrapper.orderByDesc(QuestionDO::getAddTime);
		}		
		Page<QuestionDO> questionPage = selectPage(page, wrapper);
		return new PageResult<>(questionPage.getRecords(), questionPage.getTotal());
	}

	default List<QuestionDO> selectList(QuestionListReqVO reqVO) {
		return selectList(new LambdaQueryWrapperX<QuestionDO>()
				.betweenIfPresent(QuestionDO::getAddTime, reqVO.getAddTime())
				.eqIfPresent(QuestionDO::getAddUser, reqVO.getAddUser())
				.eqIfPresent(QuestionDO::getUpdateUser, reqVO.getUpdateUser())
				.eqIfPresent(QuestionDO::getCityCode, reqVO.getCityCode())
				.likeIfPresent(QuestionDO::getCityName, reqVO.getCityName())
				.eqIfPresent(QuestionDO::getRegCode, reqVO.getRegCode())
				.likeIfPresent(QuestionDO::getRegName, reqVO.getRegName())
				.eqIfPresent(QuestionDO::getOrgCode, reqVO.getOrgCode())
				.likeIfPresent(QuestionDO::getOrgName, reqVO.getOrgName())
				.eqIfPresent(QuestionDO::getPlanId, reqVO.getPlanId())
				.eqIfPresent(QuestionDO::getTitle, reqVO.getTitle())
				.eqIfPresent(QuestionDO::getContent, reqVO.getContent())
				.eqIfPresent(QuestionDO::getContentText, reqVO.getContentText())
				.eqIfPresent(QuestionDO::getIsDisabled, reqVO.getIsDisabled())
				.eqIfPresent(QuestionDO::getStatus, reqVO.getStatus())
				.eqIfPresent(QuestionDO::getReTimes, reqVO.getReTimes())
				.betweenIfPresent(QuestionDO::getLastReTime, reqVO.getLastReTime())
				.eqIfPresent(QuestionDO::getVisitTimes, reqVO.getVisitTimes())
				.eqIfPresent(QuestionDO::getMobile, reqVO.getMobile())
				.eqIfPresent(QuestionDO::getAttachment, reqVO.getAttachment()).orderByDesc(QuestionDO::getId));
	}
	
    /**
     * 问题记录-查询问题分页数据
     * @param page Page<Map<String, Object>> 分页参数
     * @param pageReqVO QuestionReplyPageReqVO 问题分页VO
     * @return Page<QuestionDO>
     * <AUTHOR>
     * @date 2025年3月22日
     */
    public Page<QuestionDO> getPage(Page<Map<String, Object>> page, @Param("cm") QuestionPageReqVO reqVO);

	/**
	 * 获取问题反馈总数TOP10(按用户排名)
	 * @param limit int 条数限制
	 * @return List<Map<String, Object>>
	 * <AUTHOR>
	 * @date 2025年3月19日
	 */
	public List<Map<String, Object>> getWtfkzsTop10Data(int limit);
	
	/**
	 * 获取问题反馈总数TOP10(获取具体用户问题反馈情况)
	 * @param addUser String 添加用户
	 * @return List<Map<String, Object>>
	 * <AUTHOR>
	 * @date 2025年3月19日
	 */
	public List<Map<String, Object>> getWtfkzsUserData(String addUser);
	
	/**
	 * 各单位问题反馈总数分析
	 * @return List<Map<String, Object>>
	 * <AUTHOR>
	 * @date 2025年3月19日
	 */
	public List<Map<String, Object>> getGdwWtfkzsData();
}
