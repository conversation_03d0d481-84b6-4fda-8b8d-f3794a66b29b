package com.rs.module.rgf.dao.rm;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.rgf.entity.rm.QuestionConfirmDO;

/**
 * 需求问题确认
 * <AUTHOR>
 * @date 2025年4月8日
 */
@Mapper
public interface QuestionConfirmDao extends IBaseDao<QuestionConfirmDO> {

    default List<QuestionConfirmDO> selectListByQuestionId(String questionId) {
        return selectList(new LambdaQueryWrapperX<QuestionConfirmDO>().eq(QuestionConfirmDO::getQuestionId, questionId)
        		.orderByDesc(QuestionConfirmDO::getAddTime));
    }

    default int deleteByQuestionId(String questionId) {
        return delete(new LambdaQueryWrapperX<QuestionConfirmDO>().eq(QuestionConfirmDO::getQuestionId, questionId));
    }
}
