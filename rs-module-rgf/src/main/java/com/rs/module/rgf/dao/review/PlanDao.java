package com.rs.module.rgf.dao.review;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.rgf.controller.admin.review.vo.PlanListReqVO;
import com.rs.module.rgf.controller.admin.review.vo.PlanPageReqVO;
import com.rs.module.rgf.entity.review.PlanDO;

/**
 * 评审计划 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface PlanDao extends IBaseDao<PlanDO> {

	default PageResult<PlanDO> selectPage(PlanPageReqVO reqVO) {
		Page<PlanDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
		Page<PlanDO> planPage = selectPage(page,
				new LambdaQueryWrapperX<PlanDO>().eqIfPresent(PlanDO::getIsDel, reqVO.getIsDel())
						.betweenIfPresent(PlanDO::getAddTime, reqVO.getAddTime())
						.eqIfPresent(PlanDO::getAddUser, reqVO.getAddUser())
						.eqIfPresent(PlanDO::getUpdateUser, reqVO.getUpdateUser())
						.eqIfPresent(PlanDO::getCityCode, reqVO.getCityCode())
						.likeIfPresent(PlanDO::getCityName, reqVO.getCityName())
						.eqIfPresent(PlanDO::getRegCode, reqVO.getRegCode())
						.likeIfPresent(PlanDO::getRegName, reqVO.getRegName())
						.eqIfPresent(PlanDO::getOrgCode, reqVO.getOrgCode())
						.likeIfPresent(PlanDO::getOrgName, reqVO.getOrgName())
						.eqIfPresent(PlanDO::getMaterId, reqVO.getMaterId())
						.eqIfPresent(PlanDO::getIsDisabled, reqVO.getIsDisabled())
						.eqIfPresent(PlanDO::getStatus, reqVO.getStatus())
						.betweenIfPresent(PlanDO::getStartDate, reqVO.getStartDate())
						.betweenIfPresent(PlanDO::getEndDate, reqVO.getEndDate())
						.eqIfPresent(PlanDO::getRemark, reqVO.getRemark()).orderByDesc(PlanDO::getId));
		return new PageResult<>(planPage.getRecords(), planPage.getTotal());
	}

	default List<PlanDO> selectList(PlanListReqVO reqVO) {
		return selectList(new LambdaQueryWrapperX<PlanDO>().eqIfPresent(PlanDO::getIsDel, reqVO.getIsDel())
				.betweenIfPresent(PlanDO::getAddTime, reqVO.getAddTime())
				.eqIfPresent(PlanDO::getAddUser, reqVO.getAddUser())
				.eqIfPresent(PlanDO::getUpdateUser, reqVO.getUpdateUser())
				.eqIfPresent(PlanDO::getCityCode, reqVO.getCityCode())
				.likeIfPresent(PlanDO::getCityName, reqVO.getCityName())
				.eqIfPresent(PlanDO::getRegCode, reqVO.getRegCode())
				.likeIfPresent(PlanDO::getRegName, reqVO.getRegName())
				.eqIfPresent(PlanDO::getOrgCode, reqVO.getOrgCode())
				.likeIfPresent(PlanDO::getOrgName, reqVO.getOrgName())
				.eqIfPresent(PlanDO::getMaterId, reqVO.getMaterId())
				.eqIfPresent(PlanDO::getIsDisabled, reqVO.getIsDisabled())
				.eqIfPresent(PlanDO::getStatus, reqVO.getStatus())
				.betweenIfPresent(PlanDO::getStartDate, reqVO.getStartDate())
				.betweenIfPresent(PlanDO::getEndDate, reqVO.getEndDate())
				.eqIfPresent(PlanDO::getRemark, reqVO.getRemark()).orderByDesc(PlanDO::getId));
	}

	// ==================== 首页 ====================
	
	/**
	 * 首页-获取待办事项评审计划
	 * @param limit int 条数限制
	 * @return List<Map<String, Object>>
	 * <AUTHOR>
	 * @date 2025年3月18日
	 */
	public List<Map<String, Object>> getDbsxPlan(int limit);
	
	/**
	 * 首页-获取评审计划分组用户
	 * @param planId String 评审计划Id
	 * @return List<Map<String, Object>>
	 * <AUTHOR>
	 * @date 2025年3月18日
	 */
	public List<Map<String, Object>> getPlanGroupUsers(String planId);
	
	
	// ==================== 问题记录 ====================
	
	
	/**
	 * 问题记录-查询评审计划详情
	 * @param planId String 评审计划Id
	 * @return Map<String, Object>
	 * <AUTHOR>
	 * @date 2025年3月21日
	 */
	public Map<String, Object> getWtjlPlanInfo(String planId);
}
