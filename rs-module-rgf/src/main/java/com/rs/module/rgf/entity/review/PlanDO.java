package com.rs.module.rgf.entity.review;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 评审计划 DO
 *
 * <AUTHOR>
 */
@TableName("rgf_review_plan")
@KeySequence("rgf_review_plan_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlanDO extends BaseDO {
	private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 评审素材
     */
    private String materId;
    /**
     * 是否禁用(0否1是)
     */
    private Short isDisabled;
    /**
     * 评审状态(0待评审1评审中2已关闭)
     */
    private Short status;
    /**
     * 评审开始日期
     */
    private Date startDate;
    /**
     * 评审结束日期
     */
    private Date endDate;
    /**
     * 评审简介
     */
    private String remark;

}
