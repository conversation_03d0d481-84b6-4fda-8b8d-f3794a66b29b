package com.rs.module.rgf.controller.admin.rm.vo;

import java.util.Date;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 需求素材 Response VO")
@Data
public class MaterialRespVO implements TransPojo {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("是否删除(0:否,1:是)")
    private String isDel;
    @ApiModelProperty("添加时间")
    private Date addTime;
    @ApiModelProperty("添加人")
    private String addUser;
    @ApiModelProperty("更新人")
    private String updateUser;
    @ApiModelProperty("所属市级代码")
    private String cityCode;
    @ApiModelProperty("所属市级名称")
    private String cityName;
    @ApiModelProperty("区域代码")
    private String regCode;
    @ApiModelProperty("区域名称")
    private String regName;
    @ApiModelProperty("机构编号")
    private String orgCode;
    @ApiModelProperty("机构名称")
    private String orgName;
    @ApiModelProperty("素材名称")
    private String materName;
    @Trans(type = TransType.DICTIONARY,key = "ZD_RS_SUB_SYSTEM")
    @ApiModelProperty("所属系统")
    private String subSystem;
    @ApiModelProperty("附件")
    private String attachment;
    @ApiModelProperty("状态(0已提交,1已部署)")
    private Short status;
    @ApiModelProperty("在线访问地址")
    private String url;
    @ApiModelProperty("素材简介")
    private String remark;
}
