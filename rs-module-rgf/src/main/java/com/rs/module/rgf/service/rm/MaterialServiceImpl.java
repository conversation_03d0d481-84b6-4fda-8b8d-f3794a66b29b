package com.rs.module.rgf.service.rm;

import java.util.List;

import javax.annotation.Resource;

import com.rs.module.rgf.cons.CommonConstants;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.rgf.controller.admin.rm.vo.MaterialListReqVO;
import com.rs.module.rgf.controller.admin.rm.vo.MaterialPageReqVO;
import com.rs.module.rgf.controller.admin.rm.vo.MaterialSaveReqVO;
import com.rs.module.rgf.dao.rm.MaterialDao;
import com.rs.module.rgf.entity.rm.MaterialDO;


/**
 * 需求素材 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MaterialServiceImpl extends BaseServiceImpl<MaterialDao, MaterialDO> implements MaterialService {

    @Resource
    private MaterialDao materialDao;

    @Override
    public String createMaterial(MaterialSaveReqVO createReqVO) {
        // 插入
        MaterialDO material = BeanUtils.toBean(createReqVO, MaterialDO.class);
        material.setStatus(CommonConstants.CONSTANTS_MATERIAL_NOT_DEPLOY);
        materialDao.insert(material);
        // 返回
        return material.getId();
    }

    @Override
    public void updateMaterial(MaterialSaveReqVO updateReqVO) {
        // 校验存在
        validateMaterialExists(updateReqVO.getId());
        // 更新
        MaterialDO updateObj = BeanUtils.toBean(updateReqVO, MaterialDO.class);
        materialDao.updateById(updateObj);
    }

    @Override
    public void deleteMaterial(String id) {
        // 校验存在
        validateMaterialExists(id);
        // 删除
        materialDao.deleteById(id);
    }

    private void validateMaterialExists(String id) {
        if (materialDao.selectById(id) == null) {
            throw new ServerException("需求素材数据不存在");
        }
    }

    @Override
    public MaterialDO getMaterial(String id) {
        return materialDao.selectById(id);
    }

    @Override
    public PageResult<MaterialDO> getMaterialPage(MaterialPageReqVO pageReqVO) {
        return materialDao.selectPage(pageReqVO);
    }

    @Override
    public List<MaterialDO> getMaterialList(MaterialListReqVO listReqVO) {
        return materialDao.selectList(listReqVO);
    }


}
