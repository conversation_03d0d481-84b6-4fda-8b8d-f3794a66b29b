package com.rs.module.rgf.dao.rm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.rgf.entity.rm.ReplyCommentDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.rgf.controller.admin.rm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.bsp.common.util.StringUtil;

/**
 * 需求回复评论 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface ReplyCommentDao extends IBaseDao<ReplyCommentDO> {

	default PageResult<ReplyCommentDO> selectPage(ReplyCommentPageReqVO reqVO) {
		Page<ReplyCommentDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
		LambdaQueryWrapperX<ReplyCommentDO> lqw = new LambdaQueryWrapperX<ReplyCommentDO>()
				.betweenIfPresent(ReplyCommentDO::getAddTime, reqVO.getAddTime())
				.eqIfPresent(ReplyCommentDO::getAddUser, reqVO.getAddUser())
				.eqIfPresent(ReplyCommentDO::getUpdateUser, reqVO.getUpdateUser())
				.eqIfPresent(ReplyCommentDO::getCityCode, reqVO.getCityCode())
				.likeIfPresent(ReplyCommentDO::getCityName, reqVO.getCityName())
				.eqIfPresent(ReplyCommentDO::getRegCode, reqVO.getRegCode())
				.likeIfPresent(ReplyCommentDO::getRegName, reqVO.getRegName())
				.eqIfPresent(ReplyCommentDO::getOrgCode, reqVO.getOrgCode())
				.likeIfPresent(ReplyCommentDO::getOrgName, reqVO.getOrgName())
				.eqIfPresent(ReplyCommentDO::getPlanId, reqVO.getPlanId())
				.eqIfPresent(ReplyCommentDO::getQuestionId, reqVO.getQuestionId())
				.eqIfPresent(ReplyCommentDO::getToUserId, reqVO.getToUserId())
				.likeIfPresent(ReplyCommentDO::getToName, reqVO.getToName())
				.eqIfPresent(ReplyCommentDO::getReplyId, reqVO.getReplyId())
				.eqIfPresent(ReplyCommentDO::getToCommentId, reqVO.getToCommentId())
				.likeIfPresent(ReplyCommentDO::getUserName, reqVO.getUserName())
				.eqIfPresent(ReplyCommentDO::getContent, reqVO.getContent())
				.eqIfPresent(ReplyCommentDO::getIsDisabled, reqVO.getIsDisabled())
				.eqIfPresent(ReplyCommentDO::getSupportTimes, reqVO.getSupportTimes())
				.eqIfPresent(ReplyCommentDO::getAgainstTimes, reqVO.getAgainstTimes())
				.eqIfPresent(ReplyCommentDO::getCommentTimes, reqVO.getCommentTimes());
		if (StringUtil.isEmpty(reqVO.getCommentId())) {
			lqw.isNull(ReplyCommentDO::getCommentId);
		} else {
			lqw.eq(ReplyCommentDO::getCommentId, reqVO.getCommentId());
		}
		Page<ReplyCommentDO> replyCommentPage = selectPage(page, lqw.orderByDesc(ReplyCommentDO::getAddTime));
		return new PageResult<>(replyCommentPage.getRecords(), replyCommentPage.getTotal());
	}

	default List<ReplyCommentDO> selectList(ReplyCommentListReqVO reqVO) {
		return selectList(
				new LambdaQueryWrapperX<ReplyCommentDO>()
						.betweenIfPresent(ReplyCommentDO::getAddTime, reqVO.getAddTime())
						.eqIfPresent(ReplyCommentDO::getAddUser, reqVO.getAddUser())
						.eqIfPresent(ReplyCommentDO::getUpdateUser, reqVO.getUpdateUser())
						.eqIfPresent(ReplyCommentDO::getCityCode, reqVO.getCityCode())
						.likeIfPresent(ReplyCommentDO::getCityName, reqVO.getCityName())
						.eqIfPresent(ReplyCommentDO::getRegCode, reqVO.getRegCode())
						.likeIfPresent(ReplyCommentDO::getRegName, reqVO.getRegName())
						.eqIfPresent(ReplyCommentDO::getOrgCode, reqVO.getOrgCode())
						.likeIfPresent(ReplyCommentDO::getOrgName, reqVO.getOrgName())
						.eqIfPresent(ReplyCommentDO::getPlanId, reqVO.getPlanId())
						.eqIfPresent(ReplyCommentDO::getQuestionId, reqVO.getQuestionId())
						.eqIfPresent(ReplyCommentDO::getCommentId, reqVO.getCommentId())
						.eqIfPresent(ReplyCommentDO::getToUserId, reqVO.getToUserId())
						.likeIfPresent(ReplyCommentDO::getToName, reqVO.getToName())
						.eqIfPresent(ReplyCommentDO::getReplyId, reqVO.getReplyId())
						.eqIfPresent(ReplyCommentDO::getToCommentId, reqVO.getToCommentId())
						.likeIfPresent(ReplyCommentDO::getUserName, reqVO.getUserName())
						.eqIfPresent(ReplyCommentDO::getContent, reqVO.getContent())
						.eqIfPresent(ReplyCommentDO::getIsDisabled, reqVO.getIsDisabled())
						.eqIfPresent(ReplyCommentDO::getSupportTimes, reqVO.getSupportTimes())
						.eqIfPresent(ReplyCommentDO::getAgainstTimes, reqVO.getAgainstTimes())
						.eqIfPresent(ReplyCommentDO::getCommentTimes, reqVO.getCommentTimes())
						.orderByDesc(ReplyCommentDO::getId));
	}
}
