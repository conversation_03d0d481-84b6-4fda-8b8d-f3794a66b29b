package com.rs.module.rgf.dao.review;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.rgf.entity.review.UserDO;

/**
 * 评审人员
 *
 * <AUTHOR>
 */
@Mapper
public interface UserDao extends IBaseDao<UserDO> {

    default List<UserDO> selectListByPlanId(String planId) {
        return selectList(new LambdaQueryWrapperX<UserDO>().eq(UserDO::getPlanId, planId));
    }

    default int deleteByPlanId(String planId) {
        return delete(new LambdaQueryWrapperX<UserDO>().eq(UserDO::getPlanId, planId));
    }

}
