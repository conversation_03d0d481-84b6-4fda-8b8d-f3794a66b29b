package com.rs.module.rgf.service.uac;

import java.util.List;
import java.util.Set;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.rgf.entity.uac.UacUserDO;

/**
 * 用户服务接口
 * <AUTHOR>
 * @date 2025年3月24日
 */
public interface UacUserService extends IBaseService<UacUserDO>{

	/**
	 * 根据身份证号码查询用户照片
	 * @param idCards Set<String> 身份证号码
	 * @return List<UserDO>
	 * <AUTHOR>
	 * @date 2025年3月24日
	 */
	public List<UacUserDO> getUserPhotoByIdCard(Set<String> idCards);
	
	/**
	 * 根据机构代码与角色编号查询用户信息
	 * @param orgCode String 机构代码
	 * @param roleCode String 角色编号
	 * @return List<UacUserDO>
	 * <AUTHOR>
	 * @date 2025年3月24日
	 */
	public List<UacUserDO> getUserByOrgAndRole(String orgCode, String roleCode);

	/**
	 * 根据机构代码与角色编号查询用户信息
	 * @param orgCode String 机构代码
	 * @param roleCode String 角色编号
	 * @param post String 岗位
	 * @return List<UacUserDO>
	 * <AUTHOR>
	 * @date 2025年5月24日
	 */
	public List<UacUserDO> getUserByOrgAndRole(String orgCode, String roleCode, String post);

	/**
	 * 根据身份证号码获取用户
	 * @param idCard String 身份证号码
	 * @return UacUserDO
	 */
	public UacUserDO getUserByIdCard(String idCard);
}
