package com.rs.module.rgf.entity.rm;

import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 需求问题回复 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 需求问题回复新增/修改 Request VO")
@TableName("rgf_rm_question_reply")
@KeySequence("rgf_rm_question_reply_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionReplyDO extends BaseDO {
	private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;
    /**
     * 评审计划Id
     */
    private String planId;
    /**
     * 问题Id
     */
    @ApiModelProperty("问题Id")
    private String questionId;
    /**
     * 回复内容(HTML带标签)
     */
    @ApiModelProperty("回复内容(HTML带标签)")
    private String content;
    /**
     * 问题内容(纯文本)
     */
    @ApiModelProperty("问题内容(纯文本)")
    private String contentText;
    /**
     * 是否禁用(0否1是)
     */
    @ApiModelProperty("是否禁用(0否1是)")
    private String isDisabled;
    /**
     * 支持数
     */
    @ApiModelProperty("支持数")
    private Short supportTimes;
    /**
     * 反对数
     */
    @ApiModelProperty("反对数")
    private Short againstTimes;
    /**
     * 评论数
     */
    @ApiModelProperty("评论数")
    private Short commentTimes;
    /**
     * 附件
     */
    @ApiModelProperty("附件")
    private String attachment;

    @ApiModelProperty("信息平台")
    @TableField(exist = false)
    private String xxpt;
}
