package com.rs.module.rgf.service.rm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.rgf.controller.admin.rm.vo.QuestionReplyPageReqVO;
import com.rs.module.rgf.controller.admin.rm.vo.QuestionReplyRespVO;
import com.rs.module.rgf.entity.rm.QuestionReplyDO;

/**
 * 需求问题回复 Service 接口
 * <AUTHOR>
 * @date 2025年3月22日
 */
public interface QuestionReplyService extends IBaseService<QuestionReplyDO>{

	/**
     * 问题记录-查询问题回复分页数据
     * @param pageReqVO QuestionReplyPageReqVO 问题回复分页VO
     * @return PageResult<QuestionReplyRespVO>
     * <AUTHOR>
     * @date 2025年3月22日
     */
    public PageResult<QuestionReplyRespVO> getWtjlQuestionReplyPageData(QuestionReplyPageReqVO pageReqVO);
}
