package com.rs.module.rgf.util;

import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.rgf.cons.CommonConstants;
import com.rs.module.rgf.cons.ConfirmFlowNode;

import java.util.List;

/**
 * 需求收集反馈系统工具类
 * <AUTHOR>
 * @date 2025年3月26日
 */
public class RgfUtil {

	/**
	 * 字典翻译
	 * @param dicName String 字典名称
	 * @param dicCode String 字典代码
	 * @return String
	 * <AUTHOR>
	 * @date 2025年3月26日
	 */
	public static String translate(String dicName, String dicCode) {
		return DicUtils.translate(dicName, dicCode);
	}

	/**
	 * 获取子系统名称
	 * @param subSystem String 子系统代码
	 * @return String
	 * <AUTHOR>
	 * @date 2025年3月26日
	 */
	public static String getSubSystemName(String subSystem) {
		return translate(CommonConstants.DIC_SUB_SYSTEM, subSystem);
	}

	/**
	 * 构建评审计划消息地址
	 * @param planId String 计划Id
	 * @return String
	 * <AUTHOR>
	 * @date 2025年3月26日
	 */
	public static String buildPlanMsgUrl(String planId) {
		return buildPlanMsgUrl(planId, "");
	}

	/**
	 * 构建评审计划消息地址
	 * @param planId String 计划Id
	 * @param questionId String 问题Id
	 * @return String
	 * <AUTHOR>
	 * @date 2025年3月26日
	 */
	public static String buildPlanMsgUrl(String planId, String questionId) {
		String url = CommonConstants.BASE_URL_PLAN;
		if(StringUtil.isNotEmpty(planId)) {
			url += "?planId=" + planId;
		}
		if(StringUtil.isNotEmpty(questionId)) {
			url += "&questionId=" + questionId;
		}
		return url;
	}

	/**
	 * 判断需求问题是否需要多级确认
	 * @param confirmAdminRoles String[] 可直接确认的角色
	 * @return boolean
	 */
	public static boolean isNeedMultiConfirm(String[] confirmAdminRoles) {
		if(confirmAdminRoles != null) {
			SessionUser user = SessionUserUtil.getSessionUser();
	    	for(String roleCode : confirmAdminRoles) {
	    		if(user.getRoleCodes().contains(roleCode)) {
	    			return false;
	    		}
	    	}
		}

		return true;
	}

	/**
	 * 获取当前问题确认流程节点
	 * @param nodeList List<ConfirmFlowNode> 所有问题确认流程节点
	 * @param roleCode 当前角色代码
	 * @return ConfirmFlowNode
	 */
	public static ConfirmFlowNode getCurrentConfirmFlowNode(List<ConfirmFlowNode> nodeList, String roleCode) {
		ConfirmFlowNode currentNode = null;
		if(CollectionUtil.isNotNull(nodeList)) {
			for(ConfirmFlowNode node: nodeList) {
				if(node.getRoleCode().equals(roleCode)) {
					currentNode = node;
					break;
				}
			}
		}

		return currentNode;
	}
}
