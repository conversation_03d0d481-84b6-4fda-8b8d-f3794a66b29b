package com.rs.module.rgf.job.review;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.bsp.sdk.mongodb.MongodbClient;
import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.sdk.msg.model.MsgVo;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.mongodb.BasicDBObject;
import com.rs.module.rgf.cons.CommonConstants;
import com.rs.module.rgf.controller.admin.review.vo.PlanListReqVO;
import com.rs.module.rgf.entity.review.PlanDO;
import com.rs.module.rgf.entity.review.UserDO;
import com.rs.module.rgf.entity.rm.MaterialDO;
import com.rs.module.rgf.service.review.PlanService;
import com.rs.module.rgf.service.rm.MaterialService;
import com.rs.module.rgf.service.rm.QuestionService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 评审计划Job
 * <AUTHOR>
 * @date 2025年3月19日
 */
@Component
@Slf4j
public class PlanJob {

	@Autowired
	private PlanService planService;
	@Autowired
	private MaterialService materialService;
	@Autowired
	private MongodbClient mongodbClient;
	@Autowired
	private QuestionService questionService;

	@Value("${bsp.mongodb.databaseName}")
	private String databaseName;
	@Value("${system-mark}")
	private String systemMark;

	@XxlJob("planStatusChange")
	public void planStatusChange() {
		try {
			PlanListReqVO planListReqVO = new PlanListReqVO();
			planListReqVO.setIsDisabled(CommonConstants.CONSTANTS_PLAN_ENABLE);
			List<PlanDO> planList = planService.getPlanList(planListReqVO);
			for (PlanDO planDO : planList) {
				MaterialDO material = materialService.getMaterial(planDO.getMaterId());
				// 需要发送消息的人员列表
				List<UserDO> userList = planService.getUserListByPlanId(planDO.getId());
				List<ReceiveUser> receiveUserList = new ArrayList<>();
				if (CollUtil.isNotEmpty(userList)) {
					for (UserDO userDO : userList) {
						receiveUserList.add(new ReceiveUser(userDO.getReviewUserIdCard(), userDO.getOrgCode()));
					}
				}
				// 判断评审计划是否已开始
				if (DateUtil.compare(DateUtil.date(), planDO.getStartDate()) >= 0
						&& CommonConstants.CONSTANTS_PLAN_STATUS_WAIT.equals(planDO.getStatus())) {
					planDO.setStatus(CommonConstants.CONSTANTS_PLAN_STATUS_RUNNING);
					planService.updateById(planDO);
					// 给评审用户发送待办消息
					if (CollUtil.isNotEmpty(receiveUserList)) {
						String title = "评审计划:" + material.getMaterName() + "已开始！";
						String content = "评审计划:" + material.getMaterName() + "已开始，请查看！";
						String url = "/#/psjh/question?planId=" + planDO.getId();
						SendMessageUtil.sendTodoMsg(title, content, url, systemMark, planDO.getAddUser(),
								planDO.getAddUserName(), planDO.getOrgCode(), planDO.getOrgName(), null,
								planDO.getId(), "pc", planDO.getId(), receiveUserList, null,
								planService.getExtendData(material.getSubSystem()));
					}
				}
				// 判断评审计划是否已关闭
				if (DateUtil.compare(DateUtil.date(), planDO.getEndDate()) >= 0) {
					// 将评审计划下的问题状态改为已关闭
					planDO.setStatus(CommonConstants.CONSTANTS_PLAN_STATUS_CLOSE);
					planService.updateById(planDO);
					// 将已发送的消息状态修改为已处理和已读
					planService.handleCloseMsg(planDO.getId());
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			XxlJobHelper.log("任务执行失败:" + e.getMessage());
		}
	}

	/**
	 * 评审计划即将关闭提醒任务
	 */
	@XxlJob("planDueDateAlert")
	public void planDueDateAlert() {
		try {
			PlanListReqVO planListReqVO = new PlanListReqVO();
			planListReqVO.setIsDisabled(CommonConstants.CONSTANTS_PLAN_ENABLE);
			planListReqVO.setStatus(CommonConstants.CONSTANTS_PLAN_STATUS_RUNNING);
			List<PlanDO> planList = planService.getPlanList(planListReqVO);
			for (PlanDO planDO : planList) {
				if (DateUtil.between(DateUtil.date(), planDO.getEndDate(), DateUnit.HOUR) <= 24) {
                    MaterialDO material = materialService.getMaterial(planDO.getMaterId());
					List<UserDO> userList = planService.getUserListByPlanId(planDO.getId());
					// 给未处理的评审用户发送提醒消息
					if (CollUtil.isNotEmpty(userList)) {
						// 判断是否已经发过24小时通知消息，已发送过则不再发送重复消息
						List<MsgVo> todoMsg = getNotProcTodoMsg(planDO.getId());
						List<MsgVo> alertMsg = getNotReadAlertMsg(planDO.getId());
						List<String> todoUserIdList = todoMsg.stream().map(todo -> todo.getrUser()).collect(Collectors.toList());
						List<String> alertUserIdList = alertMsg.stream().map(todo -> todo.getrUser()).collect(Collectors.toList());
						List<ReceiveUser> receiveUserList = new ArrayList<>();
						for (UserDO userDO : userList) {
							if (todoUserIdList.contains(userDO.getReviewUserIdCard()) && alertUserIdList.contains(userDO.getReviewUserIdCard()))
								receiveUserList.add(new ReceiveUser(userDO.getReviewUserIdCard(), userDO.getOrgCode()));
						}
						if (CollUtil.isNotEmpty(receiveUserList)) {
							String title = "评审计划:" + material.getMaterName() + "即将截止";
							String content = "评审计划:" + material.getMaterName() + "距离截止剩余不足24小时，请及时处理";
							String url = "/#/psjh/question?planId=" + planDO.getId();
							SendMessageUtil.sendAlertMsg(title, content, url, systemMark, planDO.getAddUser(),
									planDO.getAddUserName(), planDO.getOrgCode(), planDO.getOrgName(), null,
									planDO.getId(), "pc", planDO.getId(), receiveUserList, null,
									planService.getExtendData(material.getSubSystem()));
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			XxlJobHelper.log("任务执行失败:" + e.getMessage());
		}
	}

	/**
	 * 获取未处理的待办消息
	 * @param planId
	 * @return
	 */
	private List<MsgVo> getNotProcTodoMsg(String planId) {
		BasicDBObject query = new BasicDBObject();
		query.append("isdel", com.rs.framework.common.cons.CommonConstants.CONSTANTS_FALSE);
		query.append("ywbh", planId);
		query.append("isProc", com.rs.framework.common.cons.CommonConstants.CONSTANTS_FALSE);
		return mongodbClient.get(query, databaseName, "msg_todo");
	}

	/**
	 * 获取未读的提醒消息
	 * @param planId
	 * @return
	 */
	private List<MsgVo> getNotReadAlertMsg(String planId) {
		BasicDBObject query = new BasicDBObject();
		query.append("isdel", com.rs.framework.common.cons.CommonConstants.CONSTANTS_FALSE);
		query.append("ywbh", planId);
		query.append("isRead", com.rs.framework.common.cons.CommonConstants.CONSTANTS_FALSE);
		return mongodbClient.get(query, databaseName, "msg_alert");
	}
}
