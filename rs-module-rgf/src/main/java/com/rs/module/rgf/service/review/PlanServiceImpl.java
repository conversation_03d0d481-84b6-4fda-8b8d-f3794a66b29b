package com.rs.module.rgf.service.review;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.sdk.mongodb.MongodbClient;
import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.mongodb.BasicDBObject;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.rgf.cons.CommonConstants;
import com.rs.module.rgf.controller.admin.review.vo.PlanListReqVO;
import com.rs.module.rgf.controller.admin.review.vo.PlanPageReqVO;
import com.rs.module.rgf.controller.admin.review.vo.PlanSaveReqVO;
import com.rs.module.rgf.controller.admin.rm.vo.QuestionListReqVO;
import com.rs.module.rgf.dao.review.PlanDao;
import com.rs.module.rgf.dao.review.UserDao;
import com.rs.module.rgf.dao.rm.QuestionDao;
import com.rs.module.rgf.entity.review.PlanDO;
import com.rs.module.rgf.entity.review.UserDO;
import com.rs.module.rgf.entity.rm.MaterialDO;
import com.rs.module.rgf.entity.rm.QuestionDO;
import com.rs.module.rgf.service.rm.MaterialService;
import com.rs.module.rgf.util.DicUtils;
import com.rs.module.rgf.util.RgfUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 评审计划 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PlanServiceImpl extends BaseServiceImpl<PlanDao, PlanDO> implements PlanService {

    @Resource
    private PlanDao planDao;
    @Resource
    private UserDao userDao;
    @Resource
    private QuestionDao questionDao;
    @Resource
    private MaterialService materialService;
    @Resource
    private MongodbClient mongodbClient;

    @Value("${bsp.mongodb.databaseName}")
    private String databaseName;
    @Value("${system-mark}")
    private String systemMark;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createPlan(PlanSaveReqVO createReqVO) {
        // 插入
        PlanDO plan = BeanUtils.toBean(createReqVO, PlanDO.class);
        plan.setStatus(CommonConstants.CONSTANTS_PLAN_STATUS_WAIT);
        plan.setIsDisabled(CommonConstants.CONSTANTS_PLAN_DISABLE);
        planDao.insert(plan);
        // 插入子表
        createUserList(plan.getId(), createReqVO.getUsers());
        //
        SendMessageUtil.ProcessTodoMsg(plan.getId(), plan.getAddUser(), "rgf");
        // 返回
        return plan.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePlan(PlanSaveReqVO updateReqVO) {
        // 校验存在
        validatePlanExists(updateReqVO.getId());
        // 更新
        PlanDO updateObj = BeanUtils.toBean(updateReqVO, PlanDO.class);
        planDao.updateById(updateObj);

        // 更新子表
        updateUserList(updateReqVO.getId(), updateReqVO.getUsers());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePlan(String id) {
        // 校验存在
        validatePlanExists(id);
        // 删除
        planDao.deleteById(id);

        // 删除子表
        deleteUserByPlanId(id);
    }

    private void validatePlanExists(String id) {
        if (planDao.selectById(id) == null) {
            throw new ServerException("评审计划数据不存在");
        }
    }

    @Override
    public PlanDO getPlan(String id) {
        return planDao.selectById(id);
    }

    @Override
    public PageResult<PlanDO> getPlanPage(PlanPageReqVO pageReqVO) {
        return planDao.selectPage(pageReqVO);
    }

    @Override
    public List<PlanDO> getPlanList(PlanListReqVO listReqVO) {
        return planDao.selectList(listReqVO);
    }

    @Override
    public void planEnable(String id) {
        PlanDO planDO = planDao.selectById(id);
        planDO.setIsDisabled(CommonConstants.CONSTANTS_PLAN_ENABLE);
        if (DateUtil.compare(DateUtil.date(), planDO.getStartDate()) >= 0
                && CommonConstants.CONSTANTS_PLAN_STATUS_WAIT.equals(planDO.getStatus())) {
            planDO.setStatus(CommonConstants.CONSTANTS_PLAN_STATUS_RUNNING);
        }
        if (DateUtil.compare(DateUtil.date(), planDO.getEndDate()) >= 0) {
            planDO.setStatus(CommonConstants.CONSTANTS_PLAN_STATUS_CLOSE);
        }
        updateById(planDO);
        if (CommonConstants.CONSTANTS_PLAN_STATUS_RUNNING.equals(planDO.getStatus())) {
            List<UserDO> userList = getUserListByPlanId(id);
            if (CollUtil.isNotEmpty(userList)) {
                MaterialDO material = materialService.getMaterial(planDO.getMaterId());
                List<ReceiveUser> receiveUserList = new ArrayList<>();
                for (UserDO userDO : userList) {
                    receiveUserList.add(new ReceiveUser(userDO.getReviewUserIdCard(), userDO.getOrgCode()));
                }
                String title = material.getMaterName() + "已开始！";
                String content = material.getMaterName() + "已开始，请查看！";
                String url = "/#/psjh/question?planId=" + planDO.getId();
                SendMessageUtil.sendTodoMsg(title, content, url, "rgf",
                        planDO.getAddUser(), planDO.getAddUserName(), planDO.getOrgCode(), planDO.getOrgName(),
                        "pc", planDO.getId(), receiveUserList);
            }
        }
    }

    @Override
    public void planDisable(String id) {
        update(new LambdaUpdateWrapper<PlanDO>()
                .eq(PlanDO::getId, id)
                .set(PlanDO::getIsDisabled, CommonConstants.CONSTANTS_PLAN_DISABLE));
        // 将已发送的消息状态修改为已删除
        BasicDBObject filter = new BasicDBObject();
        filter.put("ywbh", id);
        BasicDBObject updateObj = new BasicDBObject("isdel", "1");
        mongodbClient.update(filter, updateObj, "msg_todo", databaseName);
    }

    @Override
    public void planClose(String id) {
        update(new LambdaUpdateWrapper<PlanDO>()
                .eq(PlanDO::getId, id)
                .set(PlanDO::getStatus, CommonConstants.CONSTANTS_PLAN_STATUS_CLOSE));
        handleCloseMsg(id);
    }

    public void handleCloseMsg(String planId){
        // 需要发送消息的人员列表
        List<UserDO> userList = getUserListByPlanId(planId);
        List<ReceiveUser> receiveUserList = new ArrayList<>();
        if (CollUtil.isNotEmpty(userList)) {
            for (UserDO userDO : userList) {
                receiveUserList.add(new ReceiveUser(userDO.getReviewUserIdCard(), userDO.getOrgCode()));
            }
        }
        PlanDO planDO = getPlan(planId);
        MaterialDO material = materialService.getMaterial(planDO.getMaterId());
        QuestionListReqVO questionListReqVO = new QuestionListReqVO();
        questionListReqVO.setPlanId(planId);
        List<QuestionDO> questionList = questionDao.selectList(questionListReqVO);
        List<String> questionIds = questionList.stream().map(question ->
                question.getId()).collect(Collectors.toList());
        BasicDBObject filter = new BasicDBObject();
        questionIds.add(planId);
        BasicDBObject in = new BasicDBObject();
        in.put("$in", questionIds);
        filter.put("ywbh", in);
        BasicDBObject updateObj = new BasicDBObject("isProc", com.rs.framework.common.cons.CommonConstants.CONSTANTS_TRUE);
        updateObj.put("idRead", com.rs.framework.common.cons.CommonConstants.CONSTANTS_TRUE);
        mongodbClient.update(filter, updateObj, "msg_todo", databaseName);
        mongodbClient.update(filter, updateObj, "msg_alert", databaseName);
        // 向评审人员发送关闭消息
        String title = "评审计划:" + material.getMaterName() + "已关闭！请知悉";
        String url = RgfUtil.buildPlanMsgUrl(planDO.getId());
        SendMessageUtil.sendAlertMsg(title, title, url, systemMark, planDO.getAddUser(),
                planDO.getAddUserName(), planDO.getOrgCode(), planDO.getOrgName(), null,
                planDO.getId(), "pc", planDO.getId(), receiveUserList, null,
                getExtendData(material.getSubSystem()));
    }

    public String getExtendData(String subSystem) {
        String subSystemName = DicUtils.translate("ZD_RS_SUB_SYSTEM", subSystem);
        return JSONUtil.createObj().put("subSystemName", subSystemName).toString();
    }


    // ==================== 子表（评审人员） ====================

    @Override
    public List<UserDO> getUserListByPlanId(String planId) {
        return userDao.selectListByPlanId(planId);
    }

    private void createUserList(String planId, List<UserDO> list) {
        list.forEach(o -> o.setPlanId(planId));
        list.forEach(o -> userDao.insert(o));
    }

    private void updateUserList(String planId, List<UserDO> list) {
        deleteUserByPlanId(planId);
		list.forEach(o -> o.setId(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createUserList(planId, list);
    }

    private void deleteUserByPlanId(String planId) {
        userDao.deleteByPlanId(planId);
    }


    // ==================== 首页 ====================


	/**
	 * 获取待办事项评审计划
	 * @param limit int 条数限制
	 * @return List<Map<String, Object>>
	 * <AUTHOR>
	 * @date 2025年3月18日
	 */
	public List<Map<String, Object>> getDbsxPlan(int limit){
		return planDao.getDbsxPlan(limit);
	}

	/**
	 * 获取评审计划分组用户
	 * @param planId String 评审计划Id
	 * @return List<Map<String, Object>>
	 * <AUTHOR>
	 * @date 2025年3月18日
	 */
	public List<Map<String, Object>> getPlanGroupUsers(String planId){
		return planDao.getPlanGroupUsers(planId);
	}


	// ==================== 问题记录 ====================


	/**
	 * 问题记录-查询评审计划详情
	 * @param planId String 评审计划Id
	 * @return Map<String, Object>
	 * <AUTHOR>
	 * @date 2025年3月21日
	 */
	public Map<String, Object> getWtjlPlanInfo(String planId){
		return planDao.getWtjlPlanInfo(planId);
	}
}
