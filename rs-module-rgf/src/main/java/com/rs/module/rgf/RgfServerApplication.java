package com.rs.module.rgf;

import java.net.UnknownHostException;

import org.dromara.x.file.storage.spring.EnableFileStorage;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.mzt.bsp.logapi.starter.annotation.EnableLogRecord;
import com.rs.framework.common.util.spring.SpringUtils;

/**
 * 需求收集反馈系统启动类
 * <AUTHOR>
 * @date 2025年3月15日
 */
@SpringBootApplication(exclude = {DruidDataSourceAutoConfigure.class, MongoAutoConfiguration.class, MongoDataAutoConfiguration.class})
@ComponentScan(value = {"com.rs.*", "com.bsp.*"})
@EnableLogRecord(systemMark = "rgf")
@EnableFileStorage
public class RgfServerApplication {

	public static void main(String[] args) throws UnknownHostException {
		ConfigurableApplicationContext application = SpringApplication.run(RgfServerApplication.class, args);
		SpringUtils.printStartLog(application);
	}
}
