package com.rs.module.rgf.service.rm;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.google.common.collect.Lists;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.rgf.config.RgfConfig;
import com.rs.module.rgf.cons.CommonConstants;
import com.rs.module.rgf.cons.ConfirmFlowNode;
import com.rs.module.rgf.controller.admin.rm.vo.QuestionListReqVO;
import com.rs.module.rgf.controller.admin.rm.vo.QuestionPageReqVO;
import com.rs.module.rgf.controller.admin.rm.vo.QuestionSaveReqVO;
import com.rs.module.rgf.dao.rm.QuestionDao;
import com.rs.module.rgf.dao.rm.QuestionReplyDao;
import com.rs.module.rgf.entity.review.PlanDO;
import com.rs.module.rgf.entity.rm.MaterialDO;
import com.rs.module.rgf.entity.rm.QuestionConfirmDO;
import com.rs.module.rgf.entity.rm.QuestionDO;
import com.rs.module.rgf.entity.rm.QuestionReplyDO;
import com.rs.module.rgf.entity.uac.UacUserDO;
import com.rs.module.rgf.service.review.PlanService;
import com.rs.module.rgf.service.uac.UacUserService;
import com.rs.module.rgf.util.DicUtils;
import com.rs.module.rgf.util.RgfUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 需求问题 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuestionServiceImpl extends BaseServiceImpl<QuestionDao, QuestionDO> implements QuestionService {

    @Resource
    private QuestionDao questionDao;
    @Resource
    private QuestionReplyDao questionReplyDao;

    @Resource
    private PlanService planService;

    @Resource
    private MaterialService materialService;

    @Resource
    private QuestionConfirmService confirmService;

    @Value("${system-mark}")
    private String systemMark;

    @Resource
    private RgfConfig rgfConfig;

	@Resource
	private UacUserService userService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createQuestion(QuestionSaveReqVO createReqVO) {
        QuestionDO question = BeanUtils.toBean(createReqVO, QuestionDO.class);

        // 插入
        questionDao.insert(question);

        // 插入子表
//        createQuestionReplyList(question.getId(), createReqVO.getQuestionReplys());
        // 返回
        return question.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateQuestion(QuestionSaveReqVO updateReqVO) {
        // 校验存在
        validateQuestionExists(updateReqVO.getId());
        // 更新
        QuestionDO updateObj = BeanUtils.toBean(updateReqVO, QuestionDO.class);
        questionDao.updateById(updateObj);

        // 更新子表
        updateQuestionReplyList(updateReqVO.getId(), updateReqVO.getQuestionReplys());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteQuestion(String id) {
        // 校验存在
        validateQuestionExists(id);
        // 删除
        questionDao.deleteById(id);

        // 删除子表
        deleteQuestionReplyByQuestionId(id);
    }

    private void validateQuestionExists(String id) {
        if (questionDao.selectById(id) == null) {
            throw new ServerException("需求问题数据不存在");
        }
    }

    @Override
    public QuestionDO getQuestion(String id) {
        return questionDao.selectById(id);
    }

    @Override
    public PageResult<QuestionDO> getQuestionPage(QuestionPageReqVO pageReqVO) {
        return questionDao.selectPage(pageReqVO);
    }

    @Override
    public List<QuestionDO> getQuestionList(QuestionListReqVO listReqVO) {
        return questionDao.selectList(listReqVO);
    }

    public PageResult<QuestionDO> getPage(QuestionPageReqVO pageReqVO) {
    	Page<Map<String, Object>> page = new Page<Map<String, Object>>(pageReqVO.getPageNo(), pageReqVO.getPageSize());

    	//处理排序
    	if(pageReqVO.getOrderFields() != null) {
			page.setOrders(pageReqVO.getOrderFields());
		}
		else {
			page.addOrder(new OrderItem("add_time", false));
		}

    	return PageResult.fromPage(baseMapper.getPage(page, pageReqVO));
    }


    // ==================== 子表（需求问题回复） ====================

    @Override
    public List<QuestionReplyDO> getQuestionReplyListByQuestionId(String questionId) {
        return questionReplyDao.selectListByQuestionId(questionId);
    }

    private void createQuestionReplyList(String questionId, List<QuestionReplyDO> list) {
        list.forEach(o -> o.setQuestionId(questionId));
        list.forEach(o -> questionReplyDao.insert(o));
    }

    @SuppressWarnings("deprecation")
	public void createQuestionReply(QuestionReplyDO questionReplyDO) {
    	questionReplyDao.insert(questionReplyDO);

    	//更新问题回复数量
    	int num = questionReplyDao.selectCount(new QueryWrapper<QuestionReplyDO>().eq("question_id", questionReplyDO.getQuestionId()));
    	QuestionDO question = questionDao.selectById(questionReplyDO.getQuestionId());
    	question.setReTimes((short)num);
    	updateById(question);

    	//评审计划和素材
    	PlanDO plan = planService.getById(questionReplyDO.getPlanId());
    	MaterialDO material = materialService.getById(plan.getMaterId());

    	//发送通知消息
    	String title = String.format("【%s】已回答问题【%s】，请及时查看",
    			questionReplyDO.getAddUserName(), question.getTitle());
    	String content = String.format("在评审素材【%s】中提的问题【%s】已被【%s】回答，请及时查看",
    			material.getMaterName(), question.getTitle(), questionReplyDO.getAddUserName());
    	String url = RgfUtil.buildPlanMsgUrl(questionReplyDO.getPlanId(), questionReplyDO.getQuestionId());
    	List<ReceiveUser> userList = Lists.newArrayList(new ReceiveUser(question.getAddUser(), question.getOrgCode()));
    	String subSystemName = RgfUtil.getSubSystemName(material.getSubSystem());
    	String extendData = JSONUtil.createObj().put("subSystemName", subSystemName).toString();
    	SendMessageUtil.sendAlertMsg(title, content, url, systemMark, questionReplyDO.getAddUser(),
    			questionReplyDO.getAddUserName(), questionReplyDO.getOrgCode(), questionReplyDO.getOrgName(),
    			null, null, questionReplyDO.getXxpt(), questionReplyDO.getId(), userList, null, extendData);
    }

    private void updateQuestionReplyList(String questionId, List<QuestionReplyDO> list) {
        deleteQuestionReplyByQuestionId(questionId);
		list.forEach(o -> o.setId(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createQuestionReplyList(questionId, list);
    }

    private void deleteQuestionReplyByQuestionId(String questionId) {
        questionReplyDao.deleteByQuestionId(questionId);
    }


    // ==================== 首页 ====================


	/**
	 * 获取问题反馈总数TOP10(按用户排名)
	 * @param limit int 条数限制
	 * @return List<Map<String, Object>>
	 * <AUTHOR>
	 * @date 2025年3月19日
	 */
	public List<Map<String, Object>> getWtfkzsTop10Data(int limit) {
		return questionDao.getWtfkzsTop10Data(limit);
	}

	/**
	 * 获取问题反馈总数TOP10(获取具体用户问题反馈情况)
	 * @param addUser String 添加用户
	 * @return List<Map<String, Object>>
	 * <AUTHOR>
	 * @date 2025年3月19日
	 */
	public List<Map<String, Object>> getWtfkzsUserData(String addUser) {
		return questionDao.getWtfkzsUserData(addUser);
	}

	/**
	 * 各单位问题反馈总数分析
	 * @return List<Map<String, Object>>
	 * <AUTHOR>
	 * @date 2025年3月19日
	 */
	public List<Map<String, Object>> getGdwWtfkzsData() {
		return questionDao.getGdwWtfkzsData();
	}


	// ==================== 评审计划-问题记录 ====================


	/**
	 * 确认问题
	 * @param ids String 待确认的问题Id
	 * @param xxpt String 处理问题的平台
	 * <AUTHOR>
	 * @date 2025年3月24日
	 */
	@SuppressWarnings("deprecation")
	public void confirmQuestion(String ids, String xxpt) {
		List<String> idList = StringUtil.splitString2List(ids);
		QueryWrapper<QuestionDO> wrapper = new QueryWrapper<QuestionDO>().in("id", idList);
		List<QuestionDO> questionList = questionDao.selectList(wrapper);
		SessionUser user = SessionUserUtil.getSessionUser();
		for(QuestionDO question : questionList) {
			//处理待办消息
			SendMessageUtil.ProcessTodoMsg(question.getId(), user.getIdCard(), xxpt);

			//多级确认
			if(rgfConfig.isMultiConfirm() && StringUtil.isNotEmpty(question.getNextConfirmRole())){
				if(user.getRoleCodes().contains(question.getNextConfirmRole())) {
					List<ConfirmFlowNode> nodeList = rgfConfig.getConfirmFlow();
					ConfirmFlowNode node = RgfUtil.getCurrentConfirmFlowNode(nodeList, question.getNextConfirmRole());

					//确认轨迹
					if(node != null) {
						QuestionConfirmDO confirm = new QuestionConfirmDO();
						confirm.setId(StringUtil.getGuid32());
						confirm.setPlanId(question.getPlanId());
						confirm.setQuestionId(question.getId());
						confirm.setConfirmRoleCode(node.getRoleCode());
						confirm.setConfirmRoleName(node.getRoleName());
						confirmService.save(confirm);

						//当前节点索引
						int currentIndex = node.getIndex();

						//确认流程完成
						if(currentIndex == nodeList.size()) {
							question.setNextConfirmRole(null);
							finishQuestionConfirm(question, user);
						}

						//下级确认
						else {
							question.setNextConfirmRole(nodeList.get(currentIndex).getRoleCode());

							// 给下级确认人发送待办消息
							List<UacUserDO> userList = null;
							if(node.isSamePost()) {
								userList = userService.getUserByOrgAndRole(user.getOrgCode(),
										nodeList.get(currentIndex).getRoleCode(), user.getPost());
							}
							else {
								userList = userService.getUserByOrgAndRole(user.getOrgCode(),
										nodeList.get(currentIndex).getRoleCode());
							}
							List<ReceiveUser> receiveUserList = new ArrayList<>();
							for (UacUserDO userDO : userList) {
								receiveUserList.add(new ReceiveUser(userDO.getIdCard(), userDO.getOrgCode()));
							}
							if (CollUtil.isNotEmpty(receiveUserList)) {
								PlanDO planDO = planService.getPlan(question.getPlanId());
								MaterialDO material = materialService.getMaterial(planDO.getMaterId());
								String subSystemName = DicUtils.translate("ZD_RS_SUB_SYSTEM", material.getSubSystem());
								String extendData = JSONUtil.createObj().put("subSystemName", subSystemName).toString();
								String content = "评审计划:" + material.getMaterName() + "已有评审人员提问，请及时确认";
								String url = RgfUtil.buildPlanMsgUrl(planDO.getId());
								SendMessageUtil.sendTodoMsg(content, content, url, systemMark, user.getIdCard(), user.getName(),
										user.getOrgCode(), user.getOrgName(), null, question.getId(),
										xxpt, question.getId(), receiveUserList, null, extendData);
							}
						}
					}
				}
			}
			else {
				finishQuestionConfirm(question, user);
			}
		}

		questionDao.updateBatch(questionList);
	}

	/**
	 * 完成问题确认
	 * @param question QuestionDO 待确认的问题
	 * @param user SessionUser 当前用户
	 */
	private void finishQuestionConfirm(QuestionDO question, SessionUser user) {
		question.setStatus(CommonConstants.CONSTANTS_QUESTION_STATUS_CONFIRM);
		question.setConfirmOrgCode(user.getOrgCode());
		question.setConfirmOrgName(user.getOrgName());
		question.setConfirmTime(new Date());
		question.setConfirmUser(user.getIdCard());
		question.setConfirmUserName(user.getName());
	}
}
