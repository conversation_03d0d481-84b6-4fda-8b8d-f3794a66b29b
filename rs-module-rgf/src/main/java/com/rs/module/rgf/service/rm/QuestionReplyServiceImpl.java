package com.rs.module.rgf.service.rm;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.rgf.controller.admin.rm.vo.QuestionReplyPageReqVO;
import com.rs.module.rgf.controller.admin.rm.vo.QuestionReplyRespVO;
import com.rs.module.rgf.dao.rm.QuestionReplyDao;
import com.rs.module.rgf.entity.rm.QuestionReplyDO;

/**
 * 需求问题回复 Service 实现类
 * <AUTHOR>
 * @date 2025年3月22日
 */
@Service
public class QuestionReplyServiceImpl extends BaseServiceImpl<QuestionReplyDao, QuestionReplyDO> implements QuestionReplyService{

    /**
     * 问题记录-查询问题回复分页数据
     * @param pageReqVO QuestionReplyPageReqVO 问题回复分页VO
     * @return PageResult<QuestionReplyRespVO>
     * <AUTHOR>
     * @date 2025年3月22日
     */
    public PageResult<QuestionReplyRespVO> getWtjlQuestionReplyPageData(QuestionReplyPageReqVO pageReqVO){
    	Page<Map<String, Object>> page = new Page<Map<String, Object>>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
    	if(pageReqVO.getOrderFields() != null) {
			page.setOrders(pageReqVO.getOrderFields());
		}
		else {
			page.addOrder(new OrderItem("add_time", false));
		}
    	return PageResult.fromPage(baseMapper.getWtjlQuestionReplyPageData(page, pageReqVO));
    }
}
