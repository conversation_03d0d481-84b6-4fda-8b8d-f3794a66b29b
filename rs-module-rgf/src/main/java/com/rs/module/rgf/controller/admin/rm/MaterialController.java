package com.rs.module.rgf.controller.admin.rm;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.fastjson.JSONObject;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.rgf.cons.CommonConstants;
import com.rs.module.rgf.controller.admin.rm.vo.MaterialListReqVO;
import com.rs.module.rgf.controller.admin.rm.vo.MaterialPageReqVO;
import com.rs.module.rgf.controller.admin.rm.vo.MaterialRespVO;
import com.rs.module.rgf.controller.admin.rm.vo.MaterialSaveReqVO;
import com.rs.module.rgf.entity.rm.MaterialAttachment;
import com.rs.module.rgf.entity.rm.MaterialDO;
import com.rs.module.rgf.service.rm.MaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 需求素材")
@RestController
@RequestMapping("/rgf/rm/material")
@Validated
public class MaterialController {

    private static final Logger log = LoggerFactory.getLogger(MaterialController.class);
    @Resource
    private MaterialService materialService;

    @Resource
    private FileStorageService fileStorageService;

    // 当前文件路径
    @Value("${material.download-path}")
    private String downloadPath;
    @Value("${material.unzip-path}")
    private String unzipPath;
    // nginx前端静态资源路径
    @Value("${material.nginx.port}")
    private String nginxPort;
    @Value("${material.nginx.ip}")
    private String nginxIp;



    @PostMapping("/create")
    @ApiOperation(value = "创建需求素材")
    @LogRecordAnnotation(bizModule = "rgf:material:create", operateType = LogOperateType.CREATE, title = "创建需求素材",
    success = "创建需求素材成功", fail = "创建需求素材失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createMaterial(@Valid @RequestBody MaterialSaveReqVO createReqVO) {
        return success(materialService.createMaterial(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新需求素材")
    @LogRecordAnnotation(bizModule = "rgf:material:update", operateType = LogOperateType.UPDATE, title = "更新需求素材",
    success = "更新需求素材成功", fail = "更新需求素材失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateMaterial(@Valid @RequestBody MaterialSaveReqVO updateReqVO) {
        materialService.updateMaterial(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除需求素材")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "rgf:material:delete", operateType = LogOperateType.DELETE, title = "删除需求素材",
    success = "删除需求素材成功", fail = "删除需求素材失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteMaterial(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           materialService.deleteMaterial(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得需求素材")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "rgf:material:get", operateType = LogOperateType.QUERY, title = "获取需求素材",
            bizNo = "{{#id}}", success = "获取需求素材成功", fail = "获取需求素材失败", extraInfo = "{{#id}}")
    public CommonResult<MaterialRespVO> getMaterial(@RequestParam("id") String id) {
        MaterialDO material = materialService.getMaterial(id);
        return success(BeanUtils.toBean(material, MaterialRespVO.class));
    }

    @GetMapping("/page")
    @ApiOperation(value = "获得需求素材分页")
    @LogRecordAnnotation(bizModule = "rgf:material:page", operateType = LogOperateType.QUERY, title = "获得需求素材分页",
    success = "获得需求素材分页成功", fail = "获得需求素材分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<MaterialRespVO>> getMaterialPage(@Valid MaterialPageReqVO pageReqVO) {
        PageResult<MaterialDO> pageResult = materialService.getMaterialPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MaterialRespVO.class));
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得需求素材列表")
    @LogRecordAnnotation(bizModule = "rgf:material:list", operateType = LogOperateType.QUERY, title = "获得需求素材列表",
    success = "获得需求素材列表成功", fail = "获得需求素材列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<MaterialRespVO>> getMaterialList(@Valid MaterialListReqVO listReqVO) {
    List<MaterialDO> list = materialService.getMaterialList(listReqVO);
        return success(BeanUtils.toBean(list, MaterialRespVO.class));
    }

    @GetMapping("/deploy")
    @ApiOperation(value = "部署需求素材")
    @LogRecordAnnotation(bizModule = "rgf:material:list", operateType = LogOperateType.QUERY, title = "部署需求素材",
            bizNo = "{{#id}}", success = "部署需求素材成功", fail = "部署需求素材失败", extraInfo = "{{#id}}")
    public CommonResult<String> deploy(@RequestParam("id") String id) {
        MaterialDO material = materialService.getById(id);
        MaterialAttachment attachment = JSONObject.parseObject(material.getAttachment(), MaterialAttachment.class);
        try {
            // 从minio下载zip文件到本地
            String downloadPath = System.getProperty("user.dir") + "/" + this.downloadPath;
            log.info("downloadPath:" + downloadPath + attachment.getFileName());
            FileInfo fileInfo = fileStorageService.getFileInfoByUrl(attachment.getObjectName());
            fileStorageService.download(fileInfo).file(downloadPath + attachment.getFileName());
            // 解压zip到nginx静态资源目录
            String nginxVisitPath = unzipFile(downloadPath + attachment.getFileName(), unzipPath);
            // 更新访问地址
            material.setUrl("http://" + nginxIp + ":" + nginxPort + "/" + nginxVisitPath);
            material.setStatus(CommonConstants.CONSTANTS_MATERIAL_DEPLOY);
            materialService.updateById(material);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return success("");
    }

    /**
     * 解压ZIP文件并获取根文件夹名称
     *
     * @param zipFilePath ZIP 文件路径
     * @param unzipPath   解压后的文件路径
     * @throws Exception 如果解压失败
     */
    private static String unzipFile(String zipFilePath, String unzipPath) throws Exception {
        String unzipName = resolveRootName(FileUtil.file(zipFilePath), CharsetUtil.CHARSET_GBK);
        unzip(zipFilePath, unzipPath);
        return unzipName;
    }

    /**
     * 获取压缩包的逻辑根目录名
     * @param zipFile ZIP文件
     * @param charset 编码
     * @return 根目录名（若无明确根目录，则返回压缩包文件名）
     */
    public static String resolveRootName(File zipFile, Charset charset) {
        // 获取压缩包名称
        String defaultRoot = FileUtil.mainName(zipFile);
        try (ZipFile zip = new ZipFile(zipFile, charset)) {
            // 获取ZIP文件所有条目的枚举
            Enumeration<? extends ZipEntry> entries = zip.entries();
            String commonPrefix = null;
            // 遍历所有ZIP条目
            while (entries.hasMoreElements()) {
                // 获取当前条目名称（含路径）
                String entryName = entries.nextElement().getName();
                // 首次遍历时直接赋值，后续遍历时计算共同前缀
                if (commonPrefix == null) {
                    commonPrefix = entryName;
                } else {
                    // 获取当前公共前缀与当前条目的新公共前缀
                    commonPrefix = findCommonPrefix(commonPrefix, entryName);
                }
            }
            // 提取根目录逻辑
            if (StrUtil.isBlank(commonPrefix)) {
                // 无公共前缀，直接返回
                return defaultRoot;
            } else {
                // 查找第一个目录分隔符位置
                int firstSeparator = commonPrefix.indexOf('/');
                if (firstSeparator != -1) {
                    // 公共前缀包含目录分隔符，截取第一级目录名称
                    return commonPrefix.substring(0, firstSeparator);
                } else {
                    // 公共前缀不含分隔符
                    return commonPrefix;
                }
            }
        } catch (Exception e) {
            // 发生异常时返回压缩包文件名
            return defaultRoot;
        }
    }

    /**
     * 查找公共前缀
     * @param a
     * @param b
     * @return
     */
    private static String findCommonPrefix(String a, String b) {
        int minLen = Math.min(a.length(), b.length());
        for (int i = 0; i < minLen; i++) {
            if (a.charAt(i) != b.charAt(i)) {
                return a.substring(0, i);
            }
        }
        return a.substring(0, minLen);
    }

    /**
     * 解压ZIP文件，在windows中压缩中文文件或文件夹后使用hutool的工具类会报错
     * @param zipFilePath 压缩包所在路径
     * @param unzipPath 解压路径
     */
    private static void unzip(String zipFilePath, String unzipPath) {
        try (ZipFile zipFile = new ZipFile(zipFilePath, Charset.forName("GBK"))) {
            Enumeration<? extends ZipEntry> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = entries.nextElement();
                File file = new File(unzipPath, entry.getName());
                if (entry.isDirectory()) {
                    file.mkdirs();
                } else {
                    try (InputStream is = zipFile.getInputStream(entry);
                         FileOutputStream fos = new FileOutputStream(file)) {
                        byte[] buffer = new byte[1024];
                        int len;
                        while ((len = is.read(buffer)) > 0) {
                            fos.write(buffer, 0, len);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    

}
