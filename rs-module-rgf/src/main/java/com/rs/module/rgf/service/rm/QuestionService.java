package com.rs.module.rgf.service.rm;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.rgf.controller.admin.rm.vo.QuestionListReqVO;
import com.rs.module.rgf.controller.admin.rm.vo.QuestionPageReqVO;
import com.rs.module.rgf.controller.admin.rm.vo.QuestionSaveReqVO;
import com.rs.module.rgf.entity.rm.QuestionDO;
import com.rs.module.rgf.entity.rm.QuestionReplyDO;

/**
 * 需求问题 Service 接口
 *
 * <AUTHOR>
 */
public interface QuestionService extends IBaseService<QuestionDO> {

    /**
     * 创建需求问题
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createQuestion(@Valid QuestionSaveReqVO createReqVO);

    /**
     * 更新需求问题
     *
     * @param updateReqVO 更新信息
     */
    void updateQuestion(@Valid QuestionSaveReqVO updateReqVO);

    /**
     * 删除需求问题
     *
     * @param id 编号
     */
    void deleteQuestion(String id);

    /**
     * 获得需求问题
     *
     * @param id 编号
     * @return 需求问题
     */
    QuestionDO getQuestion(String id);

    /**
    * 获得需求问题分页
    *
    * @param pageReqVO 分页查询
    * @return 需求问题分页
    */
    PageResult<QuestionDO> getQuestionPage(QuestionPageReqVO pageReqVO);

    /**
    * 获得需求问题列表
    *
    * @param listReqVO 查询条件
    * @return 需求问题列表
    */
    List<QuestionDO> getQuestionList(QuestionListReqVO listReqVO);
    
    /**
     * 获得需求问题分页
     * @param pageReqVO QuestionPageReqVO 问题分页VO
     * @return PageResult<QuestionDO>
     * <AUTHOR>
     * @date 2025年3月26日
     */
    PageResult<QuestionDO> getPage(QuestionPageReqVO pageReqVO);


    // ==================== 子表（需求问题回复） ====================
    
    void createQuestionReply(QuestionReplyDO questionReplyDO);

    /**
     * 获得需求问题回复列表
     *
     * @param questionId 问题Id
     * @return 需求问题回复列表
     */
    List<QuestionReplyDO> getQuestionReplyListByQuestionId(String questionId);
    
    
    // ==================== 首页 ====================

    
	/**
	 * 获取问题反馈总数TOP10(按用户排名)
	 * @param limit int 条数限制
	 * @return List<Map<String, Object>>
	 * <AUTHOR>
	 * @date 2025年3月19日
	 */
	public List<Map<String, Object>> getWtfkzsTop10Data(int limit);
	
	/**
	 * 获取问题反馈总数TOP10(获取具体用户问题反馈情况)
	 * @param addUser String 添加用户
	 * @return List<Map<String, Object>>
	 * <AUTHOR>
	 * @date 2025年3月19日
	 */
	public List<Map<String, Object>> getWtfkzsUserData(String addUser);
	
	/**
	 * 各单位问题反馈总数分析
	 * @return List<Map<String, Object>>
	 * <AUTHOR>
	 * @date 2025年3月19日
	 */
	public List<Map<String, Object>> getGdwWtfkzsData();
	
	
	// ==================== 评审计划-问题记录 ====================
	
	
	/**
	 * 确认问题
	 * @param ids String 待确认的问题Id
	 * @param xxpt String 处理问题的平台
	 * <AUTHOR>
	 * @date 2025年3月24日
	 */
	public void confirmQuestion(String ids, String xxpt);
}
