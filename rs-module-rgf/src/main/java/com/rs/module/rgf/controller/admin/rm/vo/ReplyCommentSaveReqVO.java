package com.rs.module.rgf.controller.admin.rm.vo;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 需求回复评论新增/修改 Request VO")
@Data
public class ReplyCommentSaveReqVO {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("是否删除(0:否,1:是)")
    private String isDel;

    @ApiModelProperty("添加时间")
    private Date addTime;

    @ApiModelProperty("添加人")
    private String addUser;

    @ApiModelProperty("更新人")
    private String updateUser;

    @ApiModelProperty("所属市级代码")
    private String cityCode;

    @ApiModelProperty("所属市级名称")
    private String cityName;

    @ApiModelProperty("区域代码")
    private String regCode;

    @ApiModelProperty("区域名称")
    private String regName;

    @ApiModelProperty("机构编号")
    private String orgCode;

    @ApiModelProperty("机构名称")
    private String orgName;

    @ApiModelProperty("评审计划Id")
    private String planId;

    @ApiModelProperty("问题ID")
    private String questionId;

    @ApiModelProperty("评论ID")
    private String commentId;

    @ApiModelProperty("被评论用户ID")
    private String toUserId;

    @ApiModelProperty("被评论用户姓名")
    private String toName;

    @ApiModelProperty("回复ID")
    private String replyId;

    @ApiModelProperty("被评论ID")
    private String toCommentId;

    @ApiModelProperty("评论用户姓名")
    private String userName;

    @ApiModelProperty("评论内容")
    private String content;

    @ApiModelProperty("是否禁用(0否1是)")
    private String isDisabled;

    @ApiModelProperty("支持数")
    private Short supportTimes;

    @ApiModelProperty("反对数")
    private Short againstTimes;

    @ApiModelProperty("评论数")
    private Short commentTimes;

}
