package com.rs.module.rgf.controller.admin.review;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.rgf.controller.admin.review.vo.PlanListReqVO;
import com.rs.module.rgf.controller.admin.review.vo.PlanPageReqVO;
import com.rs.module.rgf.controller.admin.review.vo.PlanRespVO;
import com.rs.module.rgf.controller.admin.review.vo.PlanSaveReqVO;
import com.rs.module.rgf.entity.review.PlanDO;
import com.rs.module.rgf.entity.review.UserDO;
import com.rs.module.rgf.service.review.PlanService;
import com.rs.module.rgf.util.DicUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 评审计划")
@RestController
@RequestMapping("/rgf/review/plan")
@Validated
public class PlanController {

    @Resource
    private PlanService planService;

    @PostMapping("/create")
    @ApiOperation(value = "创建评审计划")
    @LogRecordAnnotation(bizModule = "rgf:plan:create", operateType = LogOperateType.CREATE, title = "创建评审计划",
    success = "创建评审计划成功", fail = "创建评审计划失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPlan(@Valid @RequestBody PlanSaveReqVO createReqVO) {
        return success(planService.createPlan(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新评审计划")
    @LogRecordAnnotation(bizModule = "rgf:plan:update", operateType = LogOperateType.UPDATE, title = "更新评审计划",
    success = "更新评审计划成功", fail = "更新评审计划失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePlan(@Valid @RequestBody PlanSaveReqVO updateReqVO) {
        planService.updatePlan(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除评审计划")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "rgf:plan:delete", operateType = LogOperateType.DELETE, title = "删除评审计划",
    success = "删除评审计划成功", fail = "删除评审计划失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePlan(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           planService.deletePlan(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得评审计划")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "rgf:plan:get", operateType = LogOperateType.QUERY, title = "获取评审计划",
            bizNo = "{{#id}}", success = "获取评审计划成功", fail = "获取评审计划失败", extraInfo = "{{#id}}")
    public CommonResult<PlanRespVO> getPlan(@RequestParam("id") String id) {
        PlanDO plan = planService.getPlan(id);
        return success(BeanUtils.toBean(plan, PlanRespVO.class));
    }

    @GetMapping("/page")
    @ApiOperation(value = "获得评审计划分页")
    @LogRecordAnnotation(bizModule = "rgf:plan:page", operateType = LogOperateType.QUERY, title = "获得评审计划分页",
    success = "获得评审计划分页成功", fail = "获得评审计划分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<PlanRespVO>> getPlanPage(@Valid PlanPageReqVO pageReqVO) {
        PageResult<PlanDO> pageResult = planService.getPlanPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PlanRespVO.class));
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得评审计划列表")
    @LogRecordAnnotation(bizModule = "rgf:plan:list", operateType = LogOperateType.QUERY, title = "获得评审计划列表",
    success = "获得评审计划列表成功", fail = "获得评审计划列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<PlanRespVO>> getPlanList(@Valid PlanListReqVO listReqVO) {
    List<PlanDO> list = planService.getPlanList(listReqVO);
        return success(BeanUtils.toBean(list, PlanRespVO.class));
    }

    @GetMapping("/enable")
    @ApiOperation(value = "评审计划列表启动")
    @LogRecordAnnotation(bizModule = "rgf:plan:enable", operateType = LogOperateType.UPDATE, title = "评审计划列表启动",
            success = "评审计划列表启动成功", fail = "评审计划列表启动失败", extraInfo = "{{#id}}")
    public CommonResult<String> enable(@RequestParam("id") String id) {
        planService.planEnable(id);
        return success("");
    }

    @GetMapping("/disable")
    @ApiOperation(value = "评审计划列表禁用")
    @LogRecordAnnotation(bizModule = "rgf:plan:disable", operateType = LogOperateType.UPDATE, title = "评审计划列表禁用",
            success = "评审计划列表禁用成功", fail = "评审计划列表禁用失败", extraInfo = "{{#id}}")
    public CommonResult<String> disable(@RequestParam("id") String id) {
        planService.planDisable(id);
        return success("");
    }

    @GetMapping("/close")
    @ApiOperation(value = "评审计划列表关闭")
    @LogRecordAnnotation(bizModule = "rgf:plan:close", operateType = LogOperateType.UPDATE, title = "评审计划列表关闭",
            success = "评审计划列表关闭成功", fail = "评审计划列表关闭失败", extraInfo = "{{#id}}")
    public CommonResult<String> close(@RequestParam("id") String id) {
        planService.planClose(id);
        return success("");
    }

    // ==================== 子表（评审人员） ====================

    @GetMapping("/user/list-by-plan-id")
    @ApiOperation(value = "获得评审人员列表")
    @ApiImplicitParam(name = "planId", value = "评审计划Id")
    public CommonResult<List<UserDO>> getUserListByPlanId(@RequestParam("planId") String planId) {
        return success(planService.getUserListByPlanId(planId));
    }

    // ==================== 首页 ====================

    @GetMapping("/getDbsxPlan")
    @ApiOperation(value = "首页-获取待办事项评审计划")
    @ApiImplicitParam(name = "limit", value = "条数限制")
    public CommonResult<List<Map<String, Object>>> getDbsxPlan(@RequestParam(value = "limit", defaultValue = "6") Integer limit) {
        return success(planService.getDbsxPlan(limit));
    }

    @GetMapping("/getPlanGroupUsers")
    @ApiOperation(value = "首页-获取评审计划分组用户")
    @ApiImplicitParam(name = "planId", value = "评审计划Id")
    public CommonResult<List<Map<String, Object>>> getPlanGroupUsers(@RequestParam("planId") String planId) {
        return success(planService.getPlanGroupUsers(planId));
    }

    // ==================== 问题记录 ====================

    @GetMapping("/getWtjlPlanInfo")
    @ApiOperation(value = "问题记录-查询评审计划详情")
    @ApiImplicitParam(name = "planId", value = "评审计划Id")
    public CommonResult<Map<String, Object>> getWtjlPlanInfo(@RequestParam("planId") String planId) {
    	Map<String, Object> planInfoMap = planService.getWtjlPlanInfo(planId);
    	String subSystemName = DicUtils.translate("ZD_RS_SUB_SYSTEM", (String)planInfoMap.get("subSystem"));
    	planInfoMap.put("subSystemName", subSystemName);
        return success(planInfoMap);
    }
}
