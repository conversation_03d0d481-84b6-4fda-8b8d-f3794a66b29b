package com.rs.module.rgf.service.rm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.rgf.controller.admin.rm.vo.ReplyCommentListReqVO;
import com.rs.module.rgf.controller.admin.rm.vo.ReplyCommentPageReqVO;
import com.rs.module.rgf.controller.admin.rm.vo.ReplyCommentSaveReqVO;
import com.rs.module.rgf.entity.rm.ReplyCommentDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 需求回复评论 Service 接口
 *
 * <AUTHOR>
 */
public interface ReplyCommentService extends IBaseService<ReplyCommentDO> {

    /**
     * 创建需求回复评论
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createReplyComment(@Valid ReplyCommentSaveReqVO createReqVO);

    /**
     * 更新需求回复评论
     *
     * @param updateReqVO 更新信息
     */
    void updateReplyComment(@Valid ReplyCommentSaveReqVO updateReqVO);

    /**
     * 删除需求回复评论
     *
     * @param id 编号
     */
    void deleteReplyComment(String id);

    /**
     * 获得需求回复评论
     *
     * @param id 编号
     * @return 需求回复评论
     */
    ReplyCommentDO getReplyComment(String id);

    /**
    * 获得需求回复评论分页
    *
    * @param pageReqVO 分页查询
    * @return 需求回复评论分页
    */
    PageResult<ReplyCommentDO> getReplyCommentPage(ReplyCommentPageReqVO pageReqVO);

    /**
    * 获得需求回复评论列表
    *
    * @param listReqVO 查询条件
    * @return 需求回复评论列表
    */
    List<ReplyCommentDO> getReplyCommentList(ReplyCommentListReqVO listReqVO);


}
