package com.rs.module.rgf.service.rm;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.module.rgf.dao.rm.QuestionConfirmDao;
import com.rs.module.rgf.entity.rm.QuestionConfirmDO;


/**
 * 需求问题 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuestionConfirmServiceImpl extends BaseServiceImpl<QuestionConfirmDao, QuestionConfirmDO> implements QuestionConfirmService {

    @Resource
    private QuestionConfirmDao questionConfirmDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createQuestionConfirm(QuestionConfirmDO questionConfirmDO) {
    	questionConfirmDao.insert(questionConfirmDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteQuestionConfirm(String id) {
        questionConfirmDao.deleteById(id);
    }

    @Override
    public QuestionConfirmDO getQuestionConfirm(String id) {
        return questionConfirmDao.selectById(id);
    }
    
    /**
     * 获取需求问题的确认记录
     * @param questionId String 问题Id
     * @return List<QuestionConfirmDO>
     */
    @Override
    public List<QuestionConfirmDO> selectListByQuestionId(String questionId){
    	return questionConfirmDao.selectListByQuestionId(questionId);
    }

    /**
     * 删除问题的确认记录
     * @param questionId String 问题Id
     * @return int
     */
    @Override
    public int deleteByQuestionId(String questionId) {
    	return questionConfirmDao.deleteByQuestionId(questionId);
    }
}
