package com.rs.module.rgf.controller.admin.rm;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.rgf.config.RgfConfig;
import com.rs.module.rgf.cons.CommonConstants;
import com.rs.module.rgf.cons.ConfirmFlowNode;
import com.rs.module.rgf.controller.admin.rm.vo.*;
import com.rs.module.rgf.entity.review.PlanDO;
import com.rs.module.rgf.entity.rm.*;
import com.rs.module.rgf.entity.uac.UacUserDO;
import com.rs.module.rgf.service.review.PlanService;
import com.rs.module.rgf.service.rm.*;
import com.rs.module.rgf.service.uac.UacUserService;
import com.rs.module.rgf.util.DicUtils;
import com.rs.module.rgf.util.RgfUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 需求问题")
@RestController
@RequestMapping("/rgf/rm/question")
@Validated
public class QuestionController {

    @Resource
    private QuestionService questionService;

    @Resource
    private QuestionReplyService questionReplyService;

    @Resource
    private ReplyCommentService replyCommentService;

    @Resource
    private UacUserService userService;

    @Resource
    private PlanService planService;

    @Resource
    private MaterialService materialService;

    @Resource
    private QuestionConfirmService confirmService;

    @Value("${system-mark}")
    private String systemMark;

    @Resource
    private RgfConfig rgfConfig;

    @PostMapping("/create")
    @ApiOperation(value = "创建需求问题")
    @LogRecordAnnotation(bizModule = "rgf:question:create", operateType = LogOperateType.CREATE, title = "创建需求问题",
    success = "创建需求问题成功", fail = "创建需求问题失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createQuestion(@Valid @RequestBody QuestionSaveReqVO createReqVO) {
        createReqVO.setXxpt("pc");

        //待接收消息的用户
        List<UacUserDO> userList = null;

        //设置下级确认角色
        if(rgfConfig.isMultiConfirm() && RgfUtil.isNeedMultiConfirm(rgfConfig.getConfirmAdminRoles())) {
        	List<ConfirmFlowNode> nodeList = rgfConfig.getConfirmFlow();
        	if(CollectionUtil.isNotNull(nodeList)) {
        		ConfirmFlowNode firstNode = nodeList.get(0);

        		//获取消息通知用户
        		SessionUser user = SessionUserUtil.getSessionUser();
        		if(firstNode.isSamePost()) {
        			userList = userService.getUserByOrgAndRole(user.getOrgCode(), firstNode.getRoleCode(), user.getPost());
        		}
        		else {
        			userList = userService.getUserByOrgAndRole(user.getOrgCode(), firstNode.getRoleCode(), null);
        		}

        		//设置下级角色
        		if(CollectionUtil.isNotNull(userList)) {
        			createReqVO.setNextConfirmRole(firstNode.getRoleCode());
        		}
        	}
        }

        //创建问题
        String questionId = questionService.createQuestion(createReqVO);
        createReqVO.setId(questionId);

        //推送消息
        if(CollectionUtil.isNotNull(userList)) {
        	sendQuestionConfirmMsg(createReqVO.getPlanId(), createReqVO, userList);
        }
        else {
        	sendQuestionConfirmMsg(createReqVO.getPlanId(), createReqVO);
        }

        return success(questionId);
    }

    /**
     * 给所负责人发送问题确认消息
     * @param planId String 计划Id
     * @param questionReqVO QuestionSaveReqVO
     */
    private void sendQuestionConfirmMsg(String planId, QuestionSaveReqVO questionReqVO){
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        List<UacUserDO> userList = userService.getUserByOrgAndRole(sessionUser.getOrgCode(), CommonConstants.ROLE_CODE_SFZR);
        sendQuestionConfirmMsg(planId, questionReqVO, userList);
    }

    /**
     * 给所负责人发送问题确认消息
     * @param planId String 计划Id
     * @param questionReqVO QuestionSaveReqVO
     * @param userList List<UacUserDO> 接收用户
     */
    @SuppressWarnings("deprecation")
	private void sendQuestionConfirmMsg(String planId, QuestionSaveReqVO questionReqVO, List<UacUserDO> userList){
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        List<ReceiveUser> receiveUserList = new ArrayList<>();
        for (UacUserDO userDO : userList) {
            receiveUserList.add(new ReceiveUser(userDO.getIdCard(), userDO.getOrgCode()));
        }
        if (CollUtil.isNotEmpty(receiveUserList)) {
            PlanDO planDO = planService.getPlan(planId);
            MaterialDO material = materialService.getMaterial(planDO.getMaterId());
            String subSystemName = DicUtils.translate("ZD_RS_SUB_SYSTEM", material.getSubSystem());
            String extendData = JSONUtil.createObj().put("subSystemName", subSystemName).toString();
            String content = "评审计划:" + material.getMaterName() + "已有评审人员提问，请及时确认";
            String url = RgfUtil.buildPlanMsgUrl(planDO.getId());
            SendMessageUtil.sendTodoMsg(content, content, url, systemMark, sessionUser.getIdCard(), sessionUser.getName(),
                    sessionUser.getOrgCode(), sessionUser.getOrgName(), null, questionReqVO.getId(),
                    questionReqVO.getXxpt(), questionReqVO.getId(), receiveUserList, null, extendData);
        }
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新需求问题")
    @LogRecordAnnotation(bizModule = "rgf:question:update", operateType = LogOperateType.UPDATE, title = "更新需求问题",
    success = "更新需求问题成功", fail = "更新需求问题失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateQuestion(@Valid @RequestBody QuestionSaveReqVO updateReqVO) {
        questionService.updateQuestion(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除需求问题")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "rgf:question:delete", operateType = LogOperateType.DELETE, title = "删除需求问题",
    success = "删除需求问题成功", fail = "删除需求问题失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteQuestion(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           questionService.deleteQuestion(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得需求问题")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "rgf:question:get", operateType = LogOperateType.QUERY, title = "获取需求问题", bizNo = "{{#id}}", success = "获取需求问题成功", fail = "获取需求问题失败", extraInfo = "{{#id}}")
    public CommonResult<QuestionRespVO> getQuestion(@RequestParam("id") String id) {
        QuestionDO question = questionService.getQuestion(id);
        return success(BeanUtils.toBean(question, QuestionRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得需求问题分页")
    @LogRecordAnnotation(bizModule = "rgf:question:page", operateType = LogOperateType.QUERY, title = "获得需求问题分页",
    success = "获得需求问题分页成功", fail = "获得需求问题分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<QuestionRespVO>> getQuestionPage(@RequestBody @Valid QuestionPageReqVO pageReqVO) {
        PageResult<QuestionDO> pageResult = questionService.getPage(pageReqVO);
        SessionUser user = SessionUserUtil.getSessionUser();
        List<QuestionDO> pageList = pageResult.getList();
        for(QuestionDO question : pageList) {

        	//需多级确认
            if(rgfConfig.isMultiConfirm()) {

            	//判断当前用户是否允许确认
            	if(StringUtil.isNotEmpty(question.getNextConfirmRole())) {
            		if(user.getRoleCodes().contains(question.getNextConfirmRole())){
            			question.setCanConfirm(true);
            		}
            		else {
            			question.setCanConfirm(false);
            		}
            	}
            	else {
            		question.setCanConfirm(true);
            	}

            	//绑定历史确认轨迹
        		List<QuestionConfirmDO> confirmList = confirmService.selectListByQuestionId(question.getId());
        		question.setConfirmList(confirmList);

        		//添加当前轨迹
        		ConfirmFlowNode node = RgfUtil.getCurrentConfirmFlowNode(rgfConfig.getConfirmFlow(), question.getNextConfirmRole());
        		if(node != null && confirmList != null && !confirmList.isEmpty()) {
        			QuestionConfirmDO nextConfirmDO = new QuestionConfirmDO();
        			nextConfirmDO.setConfirmRoleCode(node.getRoleCode());
        			nextConfirmDO.setConfirmRoleName(node.getRoleName());
        			confirmList.add(nextConfirmDO);
        		}
            }
            else {
            	question.setCanConfirm(true);
            }
        }

        return success(BeanUtils.toBean(pageResult, QuestionRespVO.class));
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得需求问题列表")
    @LogRecordAnnotation(bizModule = "rgf:question:list", operateType = LogOperateType.QUERY, title = "获得需求问题列表",
    success = "获得需求问题列表成功", fail = "获得需求问题列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<QuestionRespVO>> getQuestionList(@Valid QuestionListReqVO listReqVO) {
    List<QuestionDO> list = questionService.getQuestionList(listReqVO);
        return success(BeanUtils.toBean(list, QuestionRespVO.class));
    }

    // ==================== 子表（需求问题回复） ====================

    @GetMapping("/question-reply/list-by-question-id")
    @ApiOperation(value = "获得需求问题回复列表")
    @ApiImplicitParam(name = "questionId", value = "问题Id")
    public CommonResult<List<QuestionReplyDO>> getQuestionReplyListByQuestionId(@RequestParam("questionId") String questionId) {
        return success(questionService.getQuestionReplyListByQuestionId(questionId));
    }

    // ==================== 首页 ====================

    @GetMapping("/getWtfkzsTop10Data")
    @ApiOperation(value = "获取问题反馈总数TOP10(按用户排名)")
    @ApiImplicitParam(name = "limit", value = "条数限制")
    public CommonResult<List<Map<String, Object>>> getWtfkzsTop10Data(@RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        return success(questionService.getWtfkzsTop10Data(limit));
    }

    @GetMapping("/getGdwWtfkzsData")
    @ApiOperation(value = "各单位问题反馈总数分析")
    public CommonResult<List<Map<String, Object>>> getGdwWtfkzsData() {
        return success(questionService.getGdwWtfkzsData());
    }

    // ==================== 评审计划-问题记录 ====================

    @PostMapping("/replyPage")
    @ApiOperation(value = "获得问题回复分页")
    @LogRecordAnnotation(bizModule = "rgf:wtjl:question:replyPage", operateType = LogOperateType.QUERY, title = "获得问题回复分页",
    success = "获得问题回复分页成功", fail = "获得问题回复分页分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<QuestionReplyRespVO>> getQuestionReplyPage(@RequestBody @Valid QuestionReplyPageReqVO pageReqVO) {
    	PageResult<QuestionReplyRespVO> pageResult = questionReplyService.getWtjlQuestionReplyPageData(pageReqVO);

    	//处理用户照片
    	List<QuestionReplyRespVO> questionReplyList = pageResult.getList();
    	if(pageReqVO.isShowUserPhoto() && !questionReplyList.isEmpty()) {
    		Set<String> idCards = questionReplyList.stream().filter(item -> StringUtil.isNotEmpty(item.getAddUser()))
    				.map(QuestionReplyRespVO::getAddUser).collect(Collectors.toSet());
    		List<UacUserDO> userList = userService.getUserPhotoByIdCard(idCards);
    		Map<String, String> userMap = userList.stream().collect(Collectors.toMap(UacUserDO::getIdCard, UacUserDO::getPhoto));
    		if(CollectionUtil.isNotNull(userMap)) {
	    		for(QuestionReplyRespVO reply : questionReplyList) {
	    			if(userMap.containsKey(reply.getAddUser())) {
						reply.setUserPhoto(userMap.get(reply.getAddUser()));
					}
	    		}
    		}
    	}

        return success(BeanUtils.toBean(pageResult, QuestionReplyRespVO.class));
    }

    @PostMapping("/wtjlConfirm")
    @ApiOperation(value = "确认问题")
    @LogRecordAnnotation(bizModule = "rgf:wtjl:question:confirm", operateType = LogOperateType.QUERY, title = "确认问题",
    success = "确认问题成功", fail = "确认问题失败", extraInfo = "{TO_JSON{#ids}}")
    public CommonResult<PageResult<QuestionReplyRespVO>> confirmQuestion(@RequestParam("ids") String ids) {
    	questionService.confirmQuestion(ids, "pc");
    	return success(null);
    }

    @PostMapping("/wtjlClose")
    @ApiOperation(value = "关闭问题")
    @LogRecordAnnotation(bizModule = "rgf:wtjl:question:close", operateType = LogOperateType.QUERY, title = "关闭问题",
    success = "关闭问题成功", fail = "关闭问题失败", extraInfo = "{TO_JSON{#ids}}")
    public CommonResult<PageResult<QuestionReplyRespVO>> closeQuestion(@RequestParam("id") String id) {
    	QuestionDO question = questionService.getById(id);
    	if(question != null) {
    		question.setStatus(CommonConstants.CONSTANTS_QUESTION_STATUS_CLOSE);
    		question.setUpdateTime(new Date());
    		question.setUpdateUser(SessionUserUtil.getSessionUser().getIdCard());
    		questionService.updateById(question);
    		return success(null);
    	}
    	else {
    		return CommonResult.error("问题关闭失败！找不到需求问题");
    	}
    }

    @PostMapping("/questionReplyAppraise")
    @ApiOperation(value = "评价问题回复")
    @LogRecordAnnotation(bizModule = "rgf:wtjl:question:appraise", operateType = LogOperateType.QUERY, title = "评价问题回复",
    success = "评价问题回复成功", fail = "评价问题回复失败", extraInfo = "{TO_JSON{#ids}}")
    public CommonResult<PageResult<QuestionReplyRespVO>> appraiseQuestion(
    		@RequestParam(value = "commentId", required = false) String commentId,
    		@RequestParam(value = "replyId", required = false) String replyId,
    		@RequestParam("isSupport") String isSupport) {
    	if(StringUtil.isNotEmpty(replyId)){
    		QuestionReplyDO questionReply = questionReplyService.getById(replyId);
        	if(questionReply != null) {
        		if(StringUtil.getBoolean(isSupport)) {
        			questionReply.setSupportTimes((short)(questionReply.getSupportTimes() + 1));
        		}
        		else {
        			questionReply.setAgainstTimes((short)(questionReply.getAgainstTimes() + 1));
        		}
        		questionReplyService.updateById(questionReply);
        		return success(null);
        	}
        	else {
        		return CommonResult.error("问题回复评价失败！找不到需求问题的回复");
        	}
    	}
    	else if(StringUtil.isNotEmpty(commentId)) {
    		ReplyCommentDO replyComment = replyCommentService.getById(commentId);
        	if(replyComment != null) {
        		if(StringUtil.getBoolean(isSupport)) {
        			replyComment.setSupportTimes((short)(replyComment.getSupportTimes() + 1));
        		}
        		else {
        			replyComment.setAgainstTimes((short)(replyComment.getAgainstTimes() + 1));
        		}
        		replyCommentService.updateById(replyComment);
        		return success(null);
        	}
        	else {
        		return CommonResult.error("回复评论评价失败！找不到需求问题回复的评论");
        	}
    	}
    	else {
    		return CommonResult.error("问题评价失败！参数不正确");
    	}
    }

    @PostMapping("/createQuestionReply")
    @ApiOperation(value = "创建需求问题回复")
    @LogRecordAnnotation(bizModule = "rgf:question:reply:create", operateType = LogOperateType.CREATE, title = "创建需求问题回复",
    success = "创建需求问题回复成功", fail = "创建需求问题回复失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createQuestionReply(@Valid @RequestBody QuestionReplyDO questionReplyDO) {
    	questionReplyDO.setXxpt("pc");
    	questionService.createQuestionReply(questionReplyDO);
        return success(null);
    }

    @GetMapping("/getQuestionDetail")
    @ApiOperation(value = "获得需求问题详情")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "rgf:question:get", operateType = LogOperateType.QUERY, title = "获取需求问题", bizNo = "{{#id}}", success = "获取需求问题成功", fail = "获取需求问题失败", extraInfo = "{{#id}}")
    public CommonResult<QuestionRespVO> getQuestionDetail(@RequestParam("id") String id) {
        QuestionDO question = questionService.getQuestion(id);
        if(question != null) {
        	question.setVisitTimes((short)(question.getVisitTimes() + 1));
        	question.setUpdateTime(new Date());
        	questionService.updateById(question);
        }
        return success(BeanUtils.toBean(question, QuestionRespVO.class));
    }

}
