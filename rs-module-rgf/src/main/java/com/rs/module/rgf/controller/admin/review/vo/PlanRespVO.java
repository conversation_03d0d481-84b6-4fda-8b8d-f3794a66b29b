package com.rs.module.rgf.controller.admin.review.vo;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 评审计划 Response VO")
@Data
public class PlanRespVO {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("是否删除(0:否,1:是)")
    private String isDel;
    @ApiModelProperty("添加时间")
    private Date addTime;
    @ApiModelProperty("添加人")
    private String addUser;
    @ApiModelProperty("更新人")
    private String updateUser;
    @ApiModelProperty("所属市级代码")
    private String cityCode;
    @ApiModelProperty("所属市级名称")
    private String cityName;
    @ApiModelProperty("区域代码")
    private String regCode;
    @ApiModelProperty("区域名称")
    private String regName;
    @ApiModelProperty("机构编号")
    private String orgCode;
    @ApiModelProperty("机构名称")
    private String orgName;
    @ApiModelProperty("评审素材")
    private String materId;
    @ApiModelProperty("是否禁用")
    private Short isDisabled;
    @ApiModelProperty("评审状态")
    private Short status;
    @ApiModelProperty("评审开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;
    @ApiModelProperty("评审结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;
    @ApiModelProperty("评审简介")
    private String remark;
}
