package com.rs.module.rgf.entity.review;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 评审人员 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 评审人员新增/修改 Request VO")
@TableName("rgf_review_user")
@KeySequence("rgf_review_user_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDO extends BaseDO {
	private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;
    /**
     * 评审单位代码
     */
    @ApiModelProperty("评审单位代码")
    private String reviewOrgCode;
    /**
     * 评审计划Id
     */
    @ApiModelProperty("评审计划Id")
    private String planId;
    /**
     * 评审单位名称
     */
    @ApiModelProperty("评审单位名称")
    private String reviewOrgName;
    /**
     * 评审用户
     */
    @ApiModelProperty("评审用户")
    private String reviewUserId;
    /**
     * 评审用户身份证号
     */
    @ApiModelProperty("评审用户身份证号")
    private String reviewUserIdCard;
    /**
     * 评审用户姓名
     */
    @ApiModelProperty("评审用户姓名")
    private String reviewUserName;

}
