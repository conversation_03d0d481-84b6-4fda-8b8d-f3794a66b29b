package com.rs.module.rgf.dao.rm;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.rgf.controller.admin.rm.vo.QuestionReplyPageReqVO;
import com.rs.module.rgf.controller.admin.rm.vo.QuestionReplyRespVO;
import com.rs.module.rgf.entity.rm.QuestionReplyDO;

/**
 * 需求问题回复
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionReplyDao extends IBaseDao<QuestionReplyDO> {

    default List<QuestionReplyDO> selectListByQuestionId(String questionId) {
        return selectList(new LambdaQueryWrapperX<QuestionReplyDO>().eq(QuestionReplyDO::getQuestionId, questionId));
    }

    default int deleteByQuestionId(String questionId) {
        return delete(new LambdaQueryWrapperX<QuestionReplyDO>().eq(QuestionReplyDO::getQuestionId, questionId));
    }

    /**
     * 问题记录-查询问题回复分页数据
     * @param page Page<Map<String, Object>> 分页参数
     * @param pageReqVO QuestionReplyPageReqVO 问题回复分页VO
     * @return Page<QuestionReplyRespVO>
     * <AUTHOR>
     * @date 2025年3月22日
     */
    public Page<QuestionReplyRespVO> getWtjlQuestionReplyPageData(Page<Map<String, Object>> page,
    		@Param("cm") QuestionReplyPageReqVO pageReqVO);
}
