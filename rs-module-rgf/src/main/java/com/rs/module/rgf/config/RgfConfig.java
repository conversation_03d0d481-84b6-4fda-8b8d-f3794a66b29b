package com.rs.module.rgf.config;

import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import com.rs.module.rgf.cons.ConfirmFlowNode;

import lombok.Data;

/**
 * 需求收集反馈系统配置类
 * <AUTHOR>
 * @date 2025年4月8日
 */
@Data
@Component
@ConfigurationProperties(prefix = "conf.rgf")
public class RgfConfig {
	
	//启用多级确认
	private boolean multiConfirm;

	//需求收集确认流程
	private List<ConfirmFlowNode> confirmFlow;
	
	//直接确认角色
	private String[] confirmAdminRoles;
}
