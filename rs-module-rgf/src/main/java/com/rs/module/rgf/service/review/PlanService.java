package com.rs.module.rgf.service.review;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.rgf.controller.admin.review.vo.PlanListReqVO;
import com.rs.module.rgf.controller.admin.review.vo.PlanPageReqVO;
import com.rs.module.rgf.controller.admin.review.vo.PlanSaveReqVO;
import com.rs.module.rgf.entity.review.PlanDO;
import com.rs.module.rgf.entity.review.UserDO;

/**
 * 评审计划 Service 接口
 *
 * <AUTHOR>
 */
public interface PlanService extends IBaseService<PlanDO> {

    /**
     * 创建评审计划
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPlan(@Valid PlanSaveReqVO createReqVO);

    /**
     * 更新评审计划
     *
     * @param updateReqVO 更新信息
     */
    void updatePlan(@Valid PlanSaveReqVO updateReqVO);

    /**
     * 删除评审计划
     *
     * @param id 编号
     */
    void deletePlan(String id);

    /**
     * 获得评审计划
     *
     * @param id 编号
     * @return 评审计划
     */
    PlanDO getPlan(String id);

    /**
    * 获得评审计划分页
    *
    * @param pageReqVO 分页查询
    * @return 评审计划分页
    */
    PageResult<PlanDO> getPlanPage(PlanPageReqVO pageReqVO);

    /**
    * 获得评审计划列表
    *
    * @param listReqVO 查询条件
    * @return 评审计划列表
    */
    List<PlanDO> getPlanList(PlanListReqVO listReqVO);

	/**
	 * 评审计划启用
	 *
	 * @param id 编号
	 * @return
	 */
	void planEnable(String id);

	/**
	 * 评审计划禁用
	 *
	 * @param id 编号
	 * @return
	 */
	void planDisable(String id);

	/**
	 * 评审计划关闭
	 *
	 * @param id 编号
	 * @return
	 */
	void planClose(String id);

	/**
	 * 处理关闭信息
	 * @param planId
	 */
	void handleCloseMsg(String planId);

	String getExtendData(String subSystem);

    // ==================== 子表（评审人员） ====================

    /**
     * 获得评审人员列表
     *
     * @param planId 评审计划Id
     * @return 评审人员列表
     */
    List<UserDO> getUserListByPlanId(String planId);

    
    // ==================== 首页 ====================
    
    
	/**
	 * 获取待办事项评审计划
	 * @param limit int 条数限制
	 * @return List<Map<String, Object>>
	 * <AUTHOR>
	 * @date 2025年3月18日
	 */
	public List<Map<String, Object>> getDbsxPlan(int limit);
	
	/**
	 * 获取评审计划分组用户
	 * @param planId String 评审计划Id
	 * @return List<Map<String, Object>>
	 * <AUTHOR>
	 * @date 2025年3月18日
	 */
	public List<Map<String, Object>> getPlanGroupUsers(String planId);
	
	
	// ==================== 问题记录 ====================
	
	
	/**
	 * 问题记录-查询评审计划详情
	 * @param planId String 评审计划Id
	 * @return Map<String, Object>
	 * <AUTHOR>
	 * @date 2025年3月21日
	 */
	public Map<String, Object> getWtjlPlanInfo(String planId);
}
