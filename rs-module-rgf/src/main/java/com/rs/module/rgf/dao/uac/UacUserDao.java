package com.rs.module.rgf.dao.uac;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.rgf.entity.uac.UacUserDO;

/**
 * bsp认证中心-用户Dao
 * <AUTHOR>
 * @date 2025年3月24日
 */
@Mapper
public interface UacUserDao extends IBaseDao<UacUserDO>{

	/**
	 * 根据身份证号码查询用户照片
	 * @param idCards Set<String> 身份证号码
	 * @return List<UserDO>
	 * <AUTHOR>
	 * @date 2025年3月24日
	 */
	public List<UacUserDO> getUserPhotoByIdCard(@Param("idCards") Set<String> idCards);

	/**
	 * 根据机构代码与角色编号查询用户信息
	 * @param orgCode String 机构代码
	 * @param roleCode String 角色编号
	 * @param post String 岗位
	 * @return List<UacUserDO>
	 * <AUTHOR>
	 * @date 2025年3月24日
	 */
	public List<UacUserDO> getUserByOrgAndRole(@Param("orgCode") String orgCode, @Param("roleCode") String roleCode,
			@Param("post") String post);
}
