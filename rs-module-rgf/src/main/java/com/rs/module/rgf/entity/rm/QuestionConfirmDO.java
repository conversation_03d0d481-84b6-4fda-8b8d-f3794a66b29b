package com.rs.module.rgf.entity.rm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO_;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 需求问题确认 DO
 *
 * <AUTHOR>
 */
@TableName("rgf_rm_question_confirm")
@KeySequence("rgf_rm_question_confirm_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionConfirmDO extends BaseDO_ {
	private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 评审计划Id
     */
    private String planId;
    /**
     * 需求问题Id
     */
    private String questionId;
    /**
     * 确认角色代码
     */
    private String confirmRoleCode;
    /**
     * 确认角色名称
     */
    private String confirmRoleName;
}
