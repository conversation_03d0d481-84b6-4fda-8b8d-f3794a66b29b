<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.rgf.dao.rm.QuestionDao">
	
	<sql id="searchSql">
        <if test='cm.planId != null and cm.planId != ""' >
         	and t.plan_id = #{cm.planId}
        </if>
        <if test='cm.title != null and cm.title != ""'>
            and t.title like '%${cm.title}%'
        </if>
        <if test='cm.addUser != null and cm.addUser != ""' >
            and t.add_user = #{cm.addUser}
        </if>
        <if test='cm.addUserName != null and cm.addUserName != ""' >
            and t.add_user_name like '%${cm.addUserName}%'
        </if>
        <if test='cm.orgCode != null and cm.orgCode != ""' >
            and t.org_code = #{cm.orgCode}
        </if>
        <if test='cm.permSql != null and cm.permSql != ""' >
            and ${cm.permSql}
        </if>
    </sql>
    
    <!-- 获取需求问题分页数据 -->
    <select id="getPage" parameterType="com.rs.module.rgf.controller.admin.rm.vo.QuestionPageReqVO"
    	resultType="com.rs.module.rgf.entity.rm.QuestionDO">
    	select * from rgf_rm_question t where t.is_del = '0' and t.is_disabled = '0'
    	<include refid="searchSql"/>    	
    </select>

	<!-- 首页-问题反馈总数TOP10(按用户排名) -->
    <select id="getWtfkzsTop10Data" parameterType="java.util.Map" resultType="java.util.Map">
        select t.add_user_name as "addUserName", count(1) as "questionNum" from rgf_rm_question t
        where t.is_del = '0'
		group by t.add_user, t.add_user_name order by "questionNum" desc
		<if test="limit != null and limit != ''"> limit #{limit} </if>
    </select>
    
    <!-- 首页-问题反馈总数TOP10(获取具体用户问题反馈情况) -->
    <select id="getWtfkzsUserData" parameterType="java.util.Map" resultType="java.util.Map">
        select m.mater_name as "materName", count(1) as "questionNum" from rgf_rm_question q, rgf_review_plan p, rgf_rm_material m
		where q.plan_id = p.id and p.mater_id = m.id and q.add_user = #{addUser}
		and q.is_del = '0' and p.is_del = '0' and m.is_del = '0'
		group by m.id, m.mater_name
    </select>
    
    <!-- 首页-各单位问题反馈总数分析 -->
    <select id="getGdwWtfkzsData" parameterType="java.util.Map" resultType="java.util.Map">
        select t.org_name as "orgName", count(1) as "questionNum" from rgf_rm_question t
        where t.is_del = '0'
		group by t.org_code, t.org_name order by "questionNum" desc
    </select>
    
    <!-- 问题记录-获取计划问题分页数据 -->
    <select id="getWtjlPageData" parameterType="java.util.Map" resultType="com.rs.module.rgf.controller.admin.rm.vo.QuestionRespVO">
		select * from rgf_rm_question t where t.plan_id = #{planId}
	</select>
</mapper>
