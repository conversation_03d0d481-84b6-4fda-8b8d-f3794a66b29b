<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.rgf.dao.uac.UacUserDao">
    
    <!-- 问题记录-查询问题回复分页数据 -->
    <select id="getUserPhotoByIdCard" resultType="com.rs.module.rgf.entity.uac.UacUserDO">
		SELECT u.ID as id, u.ID_CARD as "idCard", t.PHOTO as photo FROM uac_user_photo t, uac_user u
			where t.ID = u.ID and u.ID_CARD in 
			<foreach collection="idCards" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
	</select>

	<!-- 用户查询-获取某机构拥有某角色或岗位的用户 -->
	<select id="getUserByOrgAndRole" resultType="com.rs.module.rgf.entity.uac.UacUserDO">
		SELECT u.ID as id, u.ID_CARD as "idCard",u.NAME as name,u.ORG_CODE as "orgCode",u.ORG_NAME as "orgName"
		FROM uac_user u, uac_user_role ur, uac_role r
		where u.ID = ur.USER_ID
		  and ur.ROLE_ID = r.ID
		  and u.ORG_CODE = #{orgCode}
		  and r.CODE = #{roleCode}
		  <if test='post != null and post != ""' >
			  and instr(u.POST, #{post}) > 0
		  </if>
	</select>
</mapper>
