<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.rgf.dao.review.PlanDao">

	<!-- 首页-获取待办事项评审计划 -->
    <select id="getDbsxPlan" parameterType="java.util.Map" resultType="java.util.Map">
        select t.id, t.is_disabled as "isDisabled", t.status, m.mater_name as "materName",
        	(t.end_date - CURRENT_DATE) "endDays",
        	(t.start_date - CURRENT_DATE) "startDays",
			(select count(distinct(r.review_user_id)) from rgf_review_user r where r.plan_id = t.id and r.is_del = 0) as "userNum"
		from rgf_review_plan t LEFT JOIN rgf_rm_material m on t.mater_id = m.id and m.is_del = '0'
		where t.status = 1 and t.is_disabled = 0 and t.is_del = '0'
		order by t.start_date desc
		<if test="limit != null and limit != ''"> limit #{limit} </if>
    </select>
    
    <!-- 首页-获取评审计划分组用户 -->
    <select id="getPlanGroupUsers" parameterType="java.util.Map" resultType="java.util.Map">
        select t.review_org_code as "reviewOrgCode", t.review_org_name as "reviewOrgName",
			   ARRAY_TO_STRING(ARRAY_AGG(t.review_user_name), ',') as "reviewUserNames" from rgf_review_user t
		where t.plan_id = #{planId} and t.is_del = '0'
		GROUP BY t.review_org_code, t.review_org_name
    </select>
    
    <!-- 问题记录-查询评审计划详情 -->
    <select id="getWtjlPlanInfo" parameterType="java.util.Map" resultType="java.util.Map">
		select t.id, m.mater_name as "materName", m.id as "materId", m.url, m.sub_system as "subSystem",
			t.remark, t.is_disabled as "isDisabled", t.status,
			to_char(t.start_date, 'YYYY-MM-DD') as "startDate",
			to_char(t.end_date, 'YYYY-MM-DD') as "endDate",
			(select count(1) from rgf_rm_question q where q.plan_id = t.id and q.is_del = '0') as "qTimes",
			(select count(1) from rgf_rm_question_reply r where r.plan_id = t.id and r.is_del = '0') as "replyTimes",
			(select count(1) from rgf_rm_reply_comment c where c.plan_id = t.id and c.is_del = '0') as "commentTimes"
		from rgf_review_plan t, rgf_rm_material m
		where t.id = #{planId} and t.mater_id = m.id
		and t.is_del = '0' and m.is_del = '0'
	</select>
</mapper>
