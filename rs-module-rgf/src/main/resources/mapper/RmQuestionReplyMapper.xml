<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.rgf.dao.rm.QuestionReplyDao">
    
    <!-- 问题记录-查询问题回复分页数据 -->
    <select id="getWtjlQuestionReplyPageData" parameterType="com.rs.module.rgf.controller.admin.rm.vo.QuestionReplyPageReqVO"
    	resultType="com.rs.module.rgf.controller.admin.rm.vo.QuestionReplyRespVO">
		select q.TITLE AS title, q.status, p.* from rgf_rm_question_reply p
        	left join rgf_rm_question q on p.QUESTION_ID= q.ID
        	where q.is_del = '0' and p.is_del= '0'
        	<if test='cm.planId != null and cm.planId != ""' >and p.plan_id = #{cm.planId}</if>
        	<if test='cm.addUser != null and cm.addUser != ""' >and p.ADD_USER = #{cm.addUser}</if>
			<if test='cm.questionId != null and cm.questionId != ""' >and p.question_id = #{cm.questionId}</if>	
        	<if test='cm.content != null and cm.content != ""' > and p.CONTENT like '%${cm.content}%'</if>
	</select>
</mapper>
