# 字典表

```sql
CREATE TABLE `ops_dic` (
                           `ID` varchar(32) NOT NULL COMMENT '字典ID',
    `CAT_ID` varchar(32) DEFAULT NULL COMMENT '分类ID',
    `APP_ID` varchar(32) DEFAULT NULL COMMENT '应用id',
    `NAME` varchar(50) NOT NULL COMMENT '字典名称',
    `CNAME` varchar(50) DEFAULT NULL COMMENT '字典中文名称',
    `SYSTEM_MARK` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '所属应用标识(已弃用，用app_id关联-20210305)',
    `IS_REF` char(1) NOT NULL DEFAULT '0' COMMENT '是否引用',
    `IS_LOAD` char(1) NOT NULL DEFAULT '0' COMMENT '是否加载内存',
    `IS_DISABLED` char(1) NOT NULL DEFAULT '0' COMMENT '是否禁用',
    `TABLE_NAME` varchar(50) DEFAULT NULL COMMENT '业务表名称',
    `DB_ID` varchar(32) DEFAULT NULL COMMENT '数据源ID',
    `RES_ID` varchar(32) DEFAULT NULL COMMENT '资源ID',
    `FILTER_RULE` varchar(200) DEFAULT NULL COMMENT '过滤条件',
    `CODE_FIELD` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '代码列',
    `NAME_FIELD` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '名称列',
    `SPELL_FIELD` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拼音列',
    `REMARK` varchar(500) DEFAULT NULL COMMENT '备注',
    `JP_FIELD` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '简拼列',
    `ORDER_BY` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '排序SQL',
    `PK_FIELD` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '主键列名称',
    `IS_ENABLE_ADD` decimal(1,0) DEFAULT '0' COMMENT '是否可添加',
    `ORDER_FIELD` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '排序列',
    `IS_DEL` char(1) DEFAULT '0' COMMENT '是否已逻辑删除，0否1是，默认为0',
    `ADD_USER` varchar(32) DEFAULT NULL COMMENT '添加用户id',
    `ADD_TIME` datetime DEFAULT NULL COMMENT '用户添加操作时的系统日期时间',
    `UPDATE_USER` varchar(32) DEFAULT NULL COMMENT '更新用户id',
    `UPDATE_TIME` datetime DEFAULT NULL COMMENT '用户更新操作时的系统日期时间',
    `IS_PUBLIC` int DEFAULT '0' COMMENT '是否公共字典',
    `EXTEND_FIELD` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '扩展列 ',
    PRIMARY KEY (`ID`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='字典表';
```

# 字典编码表

```sql
CREATE TABLE `ops_dic_code` (
                                `ID` varchar(32) NOT NULL COMMENT '主键ID',
    `DIC_ID` varchar(32) DEFAULT NULL COMMENT '字典ID',
    `DIC_NAME` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '字典名称',
    `DIC_CNAME` varchar(50) DEFAULT NULL COMMENT '字典中文名称',
    `PARENT_ID` varchar(32) DEFAULT NULL COMMENT '父类ID',
    `CODE` varchar(50) DEFAULT NULL COMMENT '代码',
    `NAME` varchar(1000) DEFAULT NULL COMMENT '名称',
    `SCODE` varchar(1000) DEFAULT NULL COMMENT '拼音',
    `JP_CODE` varchar(500) DEFAULT NULL COMMENT '简拼',
    `ORDER_ID` decimal(10,0) DEFAULT '10000' COMMENT '排序字段',
    `REMARK` varchar(500) DEFAULT NULL COMMENT '备注',
    `IS_DEL` char(1) DEFAULT '0' COMMENT '是否已逻辑删除，0否1是，默认为0',
    `ADD_USER` varchar(32) DEFAULT NULL COMMENT '添加用户id',
    `ADD_TIME` datetime DEFAULT NULL COMMENT '用户添加操作时的系统日期时间',
    `UPDATE_USER` varchar(32) DEFAULT NULL COMMENT '更新用户id',
    `UPDATE_TIME` datetime DEFAULT NULL COMMENT '用户更新操作时的系统日期时间',
    `IS_DISABLED` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '0' COMMENT '是否禁用',
    PRIMARY KEY (`ID`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='字典编码表';
```

# 关联关系：字典表 是一条主数据，字典编码表是详细的编码，先插入字典表，再插入字典编码表

# 样例

```sql
INSERT INTO ops_dic ("id", "cat_id", "app_id", "name", "cname", "system_mark", "is_ref", "is_load",
                                "is_disabled", "table_name", "db_id", "res_id", "filter_rule", "code_field",
                                "name_field", "spell_field", "remark", "jp_field", "order_by", "pk_field",
                                "is_enable_add", "order_field", "is_del", "add_user", "add_time", "update_user",
                                "update_time", "is_public", "extend_field")
VALUES ('7216B627F99011EF861DD8C4979BC37D', '1897197418068447232', '1898925922199932928', 'ZD_SEX', '性别', NULL, '0',
        '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL,
        '2025-03-10 13:33:30', NULL, NULL, 0, NULL);

INSERT INTO ops_dic_code ("id", "dic_id", "dic_name", "dic_cname", "parent_id", "code", "name", "scode",
                                     "jp_code", "order_id", "remark", "is_del", "add_user", "add_time", "update_user",
                                     "update_time", "is_disabled")
VALUES ('9016B627F99011EF861DD8C4979BC878', '7216B627F99011EF861DD8C4979BC37D', 'ZD_SEX', NULL, '0', '0', '未知的性别', NULL, NULL, '1', NULL, '0',
        NULL, NULL, NULL, NULL, '0');
```

# 已有的appId

```text
1898925922199932928	智慧医疗系统	ihc
1901472171029565440	需求收集反馈系统	rgf
1901545604329377792	实战平台系统	acp
1906902626012893184	数字档案系统	dam
1910522342929469440	谈话教育系统	tem
1922182838644510720	仓内屏	cnp
1929800813891424256	测试	test
1931990040389488640	test111	test111
1934496429913542656	附物管理	ptm
1934968956884488192	北京市拘留所	bjsjls
1934985431515009024	北京市第三看守所	bjsdskss
1935267722900410368	北京市第二看守所	bjsdekss
1935267889611411456	北京市第六看守所	bjsdlkss
1935268030510665728	北京市强制隔离戒毒所	bjsqzgljds
1935268200216399872	北京市监护治疗中心（强制医疗所）	bjsjhzlzx
33fcac6846b843be9bb0341cca30ad83	基础支撑平台	bsp
912A8AA34B4A48499E2A313607F83706	智能审讯系统V3.0	sic
BEC768CECA20471F87BAB1B8A4C9A820	智能笔录系统V5.0	record
C29D4FC920784694BDAAAE61ADA251FA	配置平台V1.0	csp
```

# 请根据要求生成mysql 的insert 语句，ops_dic_code和ops_dic主键ID随机生成32位，防止主键冲突
