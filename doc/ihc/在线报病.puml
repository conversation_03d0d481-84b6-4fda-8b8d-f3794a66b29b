@startuml
'https://plantuml.com/sequence-diagram

autonumber

BSP  -> BSP: 配置预约审核列表(ihc:ylyysh)
仓内屏 -> 医疗系统后端: 提交在线报病
医疗系统前端 -> BSP: 加载预约审核列表(ihc:ylyysh)
医疗系统前端 -> 医疗系统前端: 点击审核
医疗系统前端 -> 医疗系统前端: 点击发起对讲
医疗系统前端 -> 医疗系统后端: 获取SIP的动态id、目标id
医疗系统前端 -> 仓内屏: 通过jssip.js 发起注册、呼叫
医疗系统前端 -> 仓内屏: 接通问诊
医疗系统前端 -> 仓内屏: 挂断
医疗系统前端 -> 医疗系统后端: 选择处理方式，提交数据
医疗系统前端 -> 医疗系统前端: 加载远程问诊列表(正在配置)
医疗系统前端 -> 医疗系统后端: 开具处方（过程中如果还要远程问诊，参考7）




@enduml
