# 监管业务标签注解使用方法

## 1. 注解简介

本注解通过注解方式实现自动打标签，支持灵活的条件配置、规则组合、依赖、有效期等功能，适用于监管业务各类实体的标签管理。

### 1.1 核心特性

- 支持多种条件类型（等于、包含、大于、小于等）
- 支持条件组合（AND/OR）
- 支持条件嵌套
- 支持规则优先级
- 支持规则互斥
- 支持标签有效期
- 支持标签依赖
- 支持自定义服务方法
- 支持 SpEL 表达式
- **支持多种标签场景（添加、删除、更新、替换、检查）**

### 1.2 使用场景

- 人员风险评估
- 行为特征标记
- 健康状况标记
- 改造表现评估
- 特殊关注标记
- **标签动态管理（自动删除过期或不适用的标签）**
- **标签替换更新（根据条件变化自动替换标签）**
- 其他业务场景

## 2. 核心注解

### 2.1 @AutoTag

用于标记需要自动打标签的实体类。

```java
@AutoTag(businessType = "PRISONER")
public class PrisonerDO extends BaseDO {
    // 实体字段
}
```

### 2.2 @TagRule

用于配置单个标签规则。

#### 2.2.1 基础用法

```java
// 性别标签
@TagRule(
    tag = TagEnum.MALE,
    conditions = {
        @TagCondition(
            type = TagConditionType.EQUALS
            // compareValue会自动使用TagEnum.MALE中的compareValue("1")
        )
    }
)
private String gender;

// 年龄标签
@TagRule(
    tag = TagEnum.ELDERLY,
    conditions = {
        @TagCondition(
            type = TagConditionType.GREATER_EQUALS,
            compareValue = "60"
        )
    }
)
private Integer age;
```

#### 2.2.2 多条件组合（AND）

```java
// 特殊照顾标签：年龄大于60岁且患有慢性病
@TagRule(
    tag = TagEnum.SPECIAL_CARE,
    conditions = {
        @TagCondition(
            type = TagConditionType.GREATER_EQUALS,
            compareValue = "60",
            groups = {
                @TagConditionGroup(
                    type = TagConditionType.CONTAINS,
                    compareValue = "慢性病"
                ),
                @TagConditionGroup(
                    type = TagConditionType.CONTAINS,
                    compareValue = "高血压"
                )
            },
            combineType = ConditionCombineType.AND
        )
    }
)
private String healthCondition;
```

#### 2.2.3 多条件组合（OR）

```java
// 高风险标签：具有任意一种危险倾向
@TagRule(
    tag = TagEnum.HIGH_RISK,
    conditions = {
        @TagCondition(
            type = TagConditionType.CONTAINS,
            groups = {
                @TagConditionGroup(
                    type = TagConditionType.CONTAINS,
                    compareValue = "暴力倾向"
                ),
                @TagConditionGroup(
                    type = TagConditionType.CONTAINS,
                    compareValue = "自伤倾向"
                ),
                @TagConditionGroup(
                    type = TagConditionType.CONTAINS,
                    compareValue = "脱逃倾向"
                )
            },
            combineType = ConditionCombineType.OR
        )
    }
)
private String behaviorRecord;
```

#### 2.2.4 复杂条件组合（AND/OR嵌套）

```java
// 特殊关注标签：年龄大于60岁且（患有慢性病或高血压或心脏病）
@TagRule(
    tag = TagEnum.SPECIAL_CARE,
    conditions = {
        @TagCondition(
            type = TagConditionType.GREATER_EQUALS,
            compareValue = "60",
            groups = {
                // 年龄大于60岁
                @TagConditionGroup(
                    type = TagConditionType.GREATER_EQUALS,
                    compareValue = "60"
                ),
                // 并且（以下条件满足任意一个）
                @TagConditionGroup(
                    type = TagConditionType.CONTAINS,
                    groups = {
                        @TagConditionGroup(
                            type = TagConditionType.CONTAINS,
                            compareValue = "慢性病"
                        ),
                        @TagConditionGroup(
                            type = TagConditionType.CONTAINS,
                            compareValue = "高血压"
                        ),
                        @TagConditionGroup(
                            type = TagConditionType.CONTAINS,
                            compareValue = "心脏病"
                        )
                    },
                    combineType = ConditionCombineType.OR
                )
            },
            combineType = ConditionCombineType.AND
        )
    }
)
private String healthCondition;
```

#### 2.2.5 IN/NOT_IN条件

```java
// 学历标签
@TagRule(
    tag = TagEnum.EDUCATION_HIGH,
    conditions = {
        @TagCondition(
            type = TagConditionType.IN,
            values = {"高中", "中专", "职高"}
        )
    }
)
private String education;

// 犯罪类型标签
@TagRule(
    tag = TagEnum.CRIME_VIOLENT,
    conditions = {
        @TagCondition(
            type = TagConditionType.NOT_IN,
            values = {"经济犯罪", "职务犯罪", "交通肇事"}
        )
    }
)
private String crimeType;
```

#### 2.2.6 SpEL表达式

```java
// 风险等级标签
@TagRule(
    tag = TagEnum.MEDIUM_RISK,
    conditions = {
        @TagCondition(
            type = TagConditionType.SPEL,
            spelExpression = "#value >= 50 && #value < 80"
        )
    }
)
private Integer riskLevel;

// 复杂条件标签
@TagRule(
    tag = TagEnum.COMPLEX_CONDITION,
    conditions = {
        @TagCondition(
            type = TagConditionType.SPEL,
            spelExpression = "#value != null && #value.length() > 5 && #value.contains('特殊')"
        )
    }
)
private String description;
```

#### 2.2.7 自定义服务方法

```java
// 使用自定义服务方法判断
@TagRule(
    tag = TagEnum.NEED_ATTENTION,
    conditions = {
        @TagCondition(
            type = TagConditionType.CUSTOM,
            serviceMethod = "needSpecialAttention",
            serviceClass = PrisonerTagService.class
        )
    }
)
private String behaviorRecord;

// 服务类示例
@Service
public class PrisonerTagService {
    public boolean needSpecialAttention(String behaviorRecord) {
        return behaviorRecord != null && 
               (behaviorRecord.contains("异常") || 
                behaviorRecord.contains("特殊"));
    }
}
```

#### 2.2.8 条件取反

```java
// 正常状态标签
@TagRule(
    tag = TagEnum.NORMAL,
    conditions = {
        @TagCondition(
            type = TagConditionType.CONTAINS,
            compareValue = "异常",
            reverse = true
        )
    }
)
private String status;
```

#### 2.2.9 规则优先级

```java
// 高风险标签（优先级高）
@TagRule(
    tag = TagEnum.HIGH_RISK,
    priority = 100,
    exclusive = true,
    conditions = {
        @TagCondition(
            type = TagConditionType.GREATER_EQUALS,
            compareValue = "80"
        )
    }
)
private Integer riskLevel;

// 中等风险标签（优先级低）
@TagRule(
    tag = TagEnum.MEDIUM_RISK,
    priority = 50,
    exclusive = true,
    conditions = {
        @TagCondition(
            type = TagConditionType.GREATER_EQUALS,
            compareValue = "50"
        )
    }
)
private Integer riskLevel;
```

#### 2.2.10 标签有效期

```java
// 临时风险标签（30天有效期）
@TagRule(
    tag = TagEnum.TEMPORARY_RISK,
    validDays = 30,
    conditions = {
        @TagCondition(
            type = TagConditionType.GREATER_EQUALS,
            compareValue = "80"
        )
    }
)
private Integer riskLevel;

// 特殊时期标签（指定过期时间）
@TagRule(
    tag = TagEnum.SPECIAL_PERIOD,
    expireTime = "2024-12-31 23:59:59",
    conditions = {
        @TagCondition(
            type = TagConditionType.CONTAINS,
            compareValue = "特殊时期"
        )
    }
)
private String status;
```

#### 2.2.11 标签依赖

```java
// 高风险详情标签（依赖高风险标签）
@TagRule(
    tag = TagEnum.HIGH_RISK_DETAIL,
    dependsOn = {"HIGH_RISK"},
    forceDependency = true,
    conditions = {
        @TagCondition(
            type = TagConditionType.GREATER_EQUALS,
            compareValue = "90"
        )
    }
)
private Integer riskLevel;

// 特殊照顾详情标签（非强制依赖）
@TagRule(
    tag = TagEnum.SPECIAL_CARE_DETAIL,
    dependsOn = {"SPECIAL_CARE"},
    forceDependency = false,
    conditions = {
        @TagCondition(
            type = TagConditionType.CONTAINS,
            compareValue = "严重"
        )
    }
)
private String healthCondition;
```

#### 2.2.12 标签场景类型（新功能）

支持多种标签操作场景，通过 `scenario` 属性指定：

##### ******** 删除标签场景（REMOVE）

当条件满足时删除指定标签：

```java
// 删除场景：当风险评分低于20时，删除高风险标签
@TagRule(
    tag = TagEnum.HIGH_RISK,
    scenario = TagScenarioType.REMOVE,
    conditions = {
        @TagCondition(
            type = TagConditionType.LESS_THAN,
            compareValue = "20"
        )
    },
    removeDescription = "风险评分低于20，自动删除高风险标签"
)
private Integer riskScore;

// 删除多个目标标签：当表现优秀时，删除所有负面标签
@TagRule(
    tag = TagEnum.PERFORMANCE_EXCELLENT,
    scenario = TagScenarioType.REMOVE,
    conditions = {
        @TagCondition(
            type = TagConditionType.EQUALS,
            compareValue = "EXCELLENT"
        )
    },
    removeTargets = {
        TagEnum.PERFORMANCE_VIOLATION,
        TagEnum.PERFORMANCE_PUNISHMENT,
        TagEnum.BEHAVIOR_AGGRESSIVE
    },
    removeDescription = "表现优秀，清除所有负面行为标签"
)
private String performance;
```

##### ******** 替换标签场景（REPLACE）

删除旧标签并添加新标签：

```java
// 替换场景：当健康状态改变时，替换医疗标签
@TagRule(
    tag = TagEnum.MEDICAL_REGULAR,
    scenario = TagScenarioType.REPLACE,
    conditions = {
        @TagCondition(
            type = TagConditionType.EQUALS,
            compareValue = "NORMAL"
        )
    },
    removeTargets = {
        TagEnum.MEDICAL_SPECIAL,
        TagEnum.MEDICAL_EMERGENCY
    },
    removeDescription = "健康状态正常，替换为常规医疗标签"
)
private String healthStatus;
```

##### ******** 更新标签场景（UPDATE）

更新现有标签：

```java
// 更新场景：当信息变更时，更新标签
@TagRule(
    tag = TagEnum.SPECIAL_CARE,
    scenario = TagScenarioType.UPDATE,
    conditions = {
        @TagCondition(
            type = TagConditionType.NOT_NULL
        )
    },
    removeDescription = "信息更新，刷新特殊照顾标签"
)
private String careLevel;
```

##### ******** 检查场景（CHECK）

仅检查条件，不执行标签操作（用于调试和监控）：

```java
// 检查场景：仅记录条件匹配结果
@TagRule(
    tag = TagEnum.NEED_ATTENTION,
    scenario = TagScenarioType.CHECK,
    conditions = {
        @TagCondition(
            type = TagConditionType.EQUALS,
            compareValue = "MONITOR"
        )
    },
    removeDescription = "监控模式，仅检查不执行操作"
)
private String monitorStatus;
```

##### ******** 添加场景（ADD）- 默认行为

默认场景，条件匹配时添加标签，不匹配时删除标签：

```java
// 添加场景（默认）：条件匹配时添加标签
@TagRule(
    tag = TagEnum.HIGH_RISK,
    // scenario = TagScenarioType.ADD, // 默认值，可省略
    conditions = {
        @TagCondition(
            type = TagConditionType.GREATER_EQUALS,
            compareValue = "80"
        )
    }
)
private Integer riskLevel;
```

### 2.3 @TagRuleGroup

用于配置规则组，支持 AND/OR 组合。

```java
// 特殊关注规则组
@TagRuleGroup(
    name = "特殊关注",
    description = "需要特殊关注的犯人",
    rules = {
        @TagRule(
            tag = TagEnum.SENSITIVE,
            conditions = {
                @TagCondition(
                    type = TagConditionType.CONTAINS,
                    compareValue = "敏感"
                )
            }
        ),
        @TagRule(
            tag = TagEnum.VIP,
            conditions = {
                @TagCondition(
                    type = TagConditionType.CONTAINS,
                    compareValue = "重要"
                )
            }
        )
    },
    combineType = RuleCombineType.OR
)
private String specialMark;
```

## 3. 条件类型说明

支持以下条件类型：

- `EQUALS`: 等于
- `NOT_EQUALS`: 不等于
- `CONTAINS`: 包含
- `NOT_CONTAINS`: 不包含
- `GREATER_THAN`: 大于
- `LESS_THAN`: 小于
- `GREATER_EQUALS`: 大于等于
- `LESS_EQUALS`: 小于等于
- `IN`: 在列表中
- `NOT_IN`: 不在列表中
- `IS_NULL`: 为空
- `NOT_NULL`: 不为空
- `REGEX`: 正则匹配
- `SPEL`: SpEL表达式
- `CUSTOM`: 自定义服务方法

## 4. 标签场景类型说明

支持以下标签操作场景：

### 4.1 场景类型

- `ADD`: 添加标签场景（默认）
  - 条件匹配时：添加标签
  - 条件不匹配时：删除标签
  - 适用于：常规的标签添加逻辑

- `REMOVE`: 删除标签场景
  - 条件匹配时：删除指定标签
  - 条件不匹配时：不执行操作
  - 适用于：条件满足时需要清理标签的场景

- `UPDATE`: 更新标签场景
  - 条件匹配时：先删除后添加（刷新标签）
  - 条件不匹配时：不执行操作
  - 适用于：标签信息需要更新的场景

- `REPLACE`: 替换标签场景
  - 条件匹配时：删除旧标签，添加新标签
  - 条件不匹配时：不执行操作
  - 适用于：标签类型需要转换的场景

- `CHECK`: 检查场景
  - 条件匹配时：仅记录日志
  - 条件不匹配时：仅记录日志
  - 适用于：调试、监控、验证场景

### 4.2 场景配置属性

- `scenario`: 指定标签场景类型，默认为 `ADD`
- `removeTargets`: 删除场景下的目标标签数组
  - 用于 `REMOVE` 和 `REPLACE` 场景
  - 如果为空，则删除 `tag()` 指定的标签
- `removeDescription`: 删除操作的描述信息
  - 用于日志记录和调试
  - 建议提供清晰的删除原因说明

### 4.3 场景使用建议

1. **添加场景（ADD）**：用于常规的标签管理
2. **删除场景（REMOVE）**：用于条件变化时的标签清理
3. **更新场景（UPDATE）**：用于标签信息的刷新
4. **替换场景（REPLACE）**：用于标签类型的转换
5. **检查场景（CHECK）**：用于调试和监控

## 5. 最佳实践

### 5.1 条件配置

1. 优先使用基础条件类型
2. 合理使用条件组合
3. 避免过深的条件嵌套
4. 使用 SpEL 表达式处理复杂逻辑
5. 使用自定义服务方法处理业务逻辑

### 5.2 规则配置

1. 合理设置规则优先级
2. 使用互斥规则避免冲突
3. 设置合适的标签有效期
4. 合理使用标签依赖
5. 使用规则组组织相关规则

### 5.3 标签场景配置

1. **选择合适的场景类型**
   - 常规标签管理使用 `ADD` 场景
   - 条件清理使用 `REMOVE` 场景
   - 标签转换使用 `REPLACE` 场景
   - 信息更新使用 `UPDATE` 场景
   - 调试验证使用 `CHECK` 场景

2. **删除场景注意事项**
   - 谨慎使用删除场景，避免误删重要标签
   - 为删除操作提供清晰的描述信息
   - 充分测试删除逻辑的正确性
   - 考虑删除操作的业务影响

3. **目标标签配置**
   - 使用 `removeTargets` 精确指定要删除的标签
   - 避免删除不相关的标签
   - 确保删除目标标签的合理性

4. **场景组合使用**
   - 可以在同一实体上使用不同场景的规则
   - 注意场景间的相互影响
   - 合理设置规则优先级

### 5.4 性能优化

1. 避免过多的条件嵌套
2. 使用短路优化
3. 合理使用缓存
4. 优化自定义服务方法
5. 避免复杂的 SpEL 表达式
6. **谨慎使用删除场景，避免频繁的标签操作**

## 6. 注意事项

### 6.1 基础注意事项

1. 标签规则优先级：数字越大优先级越高
2. 互斥规则：互斥的规则只会生效一个
3. 标签有效期：可以设置天数或具体时间
4. 标签依赖：可以设置强制依赖或非强制依赖
5. 缓存机制：注解会自动缓存标签数据，提高性能

### 6.2 标签场景注意事项

1. **删除场景安全性**
   - 删除操作不可逆，请谨慎使用
   - 建议在测试环境充分验证删除逻辑
   - 重要标签的删除需要额外的业务确认

2. **场景类型选择**
   - 默认使用 `ADD` 场景，除非有特殊需求
   - `REMOVE` 场景仅在确实需要删除标签时使用
   - `CHECK` 场景适用于调试和监控，不影响实际标签

3. **删除目标配置**
   - `removeTargets` 为空时，删除 `tag()` 指定的标签
   - 确保删除目标标签的存在性和合理性
   - 避免删除系统关键标签

4. **条件匹配逻辑**
   - `ADD` 场景：条件匹配时添加，不匹配时删除
   - `REMOVE` 场景：条件匹配时删除，不匹配时不操作
   - `REPLACE` 场景：条件匹配时替换，不匹配时不操作
   - `UPDATE` 场景：条件匹配时更新，不匹配时不操作
   - `CHECK` 场景：仅记录条件匹配结果

5. **日志和监控**
   - 删除操作会记录详细日志
   - 建议监控标签删除频率和数量
   - 使用 `removeDescription` 提供删除原因

## 7. 常见问题

### 7.1 标签规则不生效

1. 检查条件配置是否正确
2. 检查依赖的标签是否存在
3. 检查标签是否在有效期内
4. 检查是否有互斥规则冲突
5. 检查优先级设置是否合理
6. **检查标签场景类型是否正确**

### 7.2 删除场景问题

1. **标签删除不生效**
   - 检查删除条件是否匹配
   - 检查 `removeTargets` 配置是否正确
   - 检查目标标签是否存在
   - 查看删除操作日志

2. **误删标签问题**
   - 检查删除条件是否过于宽泛
   - 检查 `removeTargets` 是否包含不应删除的标签
   - 使用 `CHECK` 场景先验证条件逻辑
   - 在测试环境充分验证

3. **删除操作频繁**
   - 检查条件是否稳定
   - 避免在频繁变化的字段上使用删除场景
   - 考虑添加条件缓冲机制

### 7.3 性能问题

1. 检查条件嵌套是否过深
2. 检查 SpEL 表达式是否复杂
3. 检查自定义服务方法是否高效
4. 检查缓存是否生效
5. 检查规则数量是否过多
6. **检查删除场景是否导致频繁的标签操作**

### 7.4 调试方法

1. 使用日志打印条件解析过程
2. 使用断点调试 TagConditionParser
3. 检查标签服务调用日志
4. 查看标签数据变更记录
5. 使用监控工具分析性能
6. **使用 `CHECK` 场景验证条件逻辑**
7. **查看删除操作的详细日志**

### 7.5 场景选择指南

1. **何时使用删除场景**
   - 条件变化时需要清理过期标签
   - 状态转换时需要移除旧状态标签
   - 数据修正时需要删除错误标签

2. **何时使用替换场景**
   - 标签类型需要转换
   - 等级或级别发生变化
   - 状态从一种转换为另一种

3. **何时使用检查场景**
   - 调试标签规则逻辑
   - 监控条件匹配情况
   - 验证新规则的正确性

4. **何时使用更新场景**
   - 标签信息需要刷新
   - 时间戳需要更新
   - 标签内容需要重新计算

## 8. 完整使用示例

以下是一个包含多种标签场景的完整示例：

```java
@Data
@EqualsAndHashCode(callSuper = true)
@AutoTag(businessType = "PRISONER_MANAGEMENT")
public class PrisonerManagementDO extends BaseDO {

    private String jgrybm;

    // 添加场景：风险评估标签
    @TagRule(
        tag = TagEnum.HIGH_RISK,
        scenario = TagScenarioType.ADD, // 默认场景，可省略
        conditions = {
            @TagCondition(
                type = TagConditionType.GREATER_EQUALS,
                compareValue = "80"
            )
        }
    )
    private Integer riskScore;

    // 删除场景：当风险降低时删除高风险标签
    @TagRule(
        tag = TagEnum.HIGH_RISK,
        scenario = TagScenarioType.REMOVE,
        conditions = {
            @TagCondition(
                type = TagConditionType.LESS_THAN,
                compareValue = "20"
            )
        },
        removeDescription = "风险评分降低，删除高风险标签"
    )
    private Integer riskScore;

    // 替换场景：表现改善时替换负面标签
    @TagRule(
        tag = TagEnum.PERFORMANCE_EXCELLENT,
        scenario = TagScenarioType.REPLACE,
        conditions = {
            @TagCondition(
                type = TagConditionType.EQUALS,
                compareValue = "EXCELLENT"
            )
        },
        removeTargets = {
            TagEnum.PERFORMANCE_VIOLATION,
            TagEnum.PERFORMANCE_PUNISHMENT,
            TagEnum.BEHAVIOR_AGGRESSIVE
        },
        removeDescription = "表现优秀，替换所有负面标签"
    )
    private String performanceLevel;

    // 更新场景：健康信息变更时更新医疗标签
    @TagRule(
        tag = TagEnum.MEDICAL_SPECIAL,
        scenario = TagScenarioType.UPDATE,
        conditions = {
            @TagCondition(
                type = TagConditionType.CONTAINS,
                compareValue = "特殊治疗"
            )
        },
        removeDescription = "健康信息更新，刷新医疗标签"
    )
    private String healthStatus;

    // 检查场景：监控异常行为
    @TagRule(
        tag = TagEnum.NEED_ATTENTION,
        scenario = TagScenarioType.CHECK,
        conditions = {
            @TagCondition(
                type = TagConditionType.CONTAINS,
                compareValue = "异常"
            )
        },
        removeDescription = "监控异常行为，仅记录不操作"
    )
    private String behaviorRecord;

    // 复杂删除场景：刑期结束时清理所有刑期标签
    @TagRule(
        tag = TagEnum.SENTENCE_COMPLETED,
        scenario = TagScenarioType.REMOVE,
        conditions = {
            @TagCondition(
                type = TagConditionType.EQUALS,
                compareValue = "COMPLETED"
            )
        },
        removeTargets = {
            TagEnum.SENTENCE_LONG,
            TagEnum.SENTENCE_MEDIUM,
            TagEnum.SENTENCE_SHORT,
            TagEnum.SENTENCE_LIFE
        },
        removeDescription = "刑期结束，清理所有刑期相关标签"
    )
    private String sentenceStatus;
}
```

## 9. 版本更新说明

### v2.0.0 新增功能

1. **标签场景类型支持**
   - 新增 `TagScenarioType` 枚举
   - 支持 ADD、REMOVE、UPDATE、REPLACE、CHECK 五种场景
   - 在 `@TagRule` 注解中添加 `scenario` 属性

2. **删除标签功能**
   - 支持条件匹配时删除指定标签
   - 支持删除多个目标标签
   - 提供删除操作描述和日志记录

3. **标签替换功能**
   - 支持删除旧标签并添加新标签
   - 适用于标签类型转换场景

4. **调试和监控功能**
   - CHECK 场景支持条件验证
   - 详细的操作日志记录
   - 删除操作的原因追踪

### 向后兼容性

- 现有的标签规则无需修改，默认使用 ADD 场景
- 所有现有功能保持不变
- 新功能为可选配置

## 10. 联系方式

如有问题，请联系注解管理员或开发团队。
