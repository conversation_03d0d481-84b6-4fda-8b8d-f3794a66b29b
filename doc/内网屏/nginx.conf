
#user  nobody;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;
	underscores_in_headers on;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    #gzip  on;
    
    server {
        listen       8103;
        server_name  localhost;
		client_max_body_size 2048M;
		proxy_set_header Host $host;
		proxy_set_header Referer $http_referer;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header X-Real-IP $remote_addr;

        #charset koi8-r;

        #access_log  logs/host.access.log  main;

        location / {
            proxy_pass http://*************:6103/;
			
        }
		location /qyga/a/customform/zyFormProcessNode/getProcessAuditMsg {
            proxy_pass http://localhost:2999/api/getFlowableNode;
        }
        #error_page  404              /404.html;

        # redirect server error pages to the static page /50x.html
        #
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }

        # proxy the PHP scripts to Apache listening on 127.0.0.1:80
        #
        #location ~ \.php$ {
        #    proxy_pass   http://127.0.0.1;
        #}

        # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
        #
        #location ~ \.php$ {
        #    root           html;
        #    fastcgi_pass   127.0.0.1:9000;
        #    fastcgi_index  index.php;
        #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
        #    include        fastcgi_params;
        #}

        # deny access to .htaccess files, if Apache's document root
        # concurs with nginx's one
        #
        #location ~ /\.ht {
        #    deny  all;
        #}
    }


    # another virtual host using mix of IP-, name-, and port-based configuration
    #
    #server {
    #    listen       8000;
    #    listen       somename:8080;
    #    server_name  somename  alias  another.alias;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}


    # HTTPS server
    #
    #server {
    #    listen       443 ssl;
    #    server_name  localhost;

    #    ssl_certificate      cert.pem;
    #    ssl_certificate_key  cert.key;

    #    ssl_session_cache    shared:SSL:1m;
    #    ssl_session_timeout  5m;

    #    ssl_ciphers  HIGH:!aNULL:!MD5;
    #    ssl_prefer_server_ciphers  on;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}
	server {
        listen       8443 ssl;
        server_name  localhost;

        ssl_certificate      ..//cert//test.crt;
        ssl_certificate_key  ..//cert//test_no_pswd.key;

        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  5m;

        ssl_ciphers  HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers  on;
		underscores_in_headers on;
		proxy_pass_request_headers      on;
        location / {
            proxy_pass http://localhost:999;
        }
    }
	server {

        listen       9002;
        server_name  localhost;
	    client_max_body_size 1024M;
        #charset koi8-r;
        set_real_ip_from 127.0.0.1;
        real_ip_header X-Forwarded-For;
        real_ip_recursive on;
        #access_log  logs/host.access.log  main;
		underscores_in_headers on;
		proxy_pass_request_headers      on;
		location / {
            proxy_pass   http://*************:9111;
            proxy_next_upstream http_500 http_502 error timeout;
            proxy_set_header HOST $host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Kss-Upstream $upstream_addr;
            add_header serverURL $upstream_addr;
            add_header serverURLCode $upstream_status;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_hide_header X-Frame-Options;
        }
        location ^~ /api/zhjg-prison-room-terminal/ {
            rewrite ^/api/zhjg-prison-room-terminal/(.*) /$1 break;
            proxy_pass   http://*************:9111;
            proxy_next_upstream http_500 http_502 error timeout;
            proxy_set_header HOST $host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Kss-Upstream $upstream_addr;
            add_header serverURL $upstream_addr;
            add_header serverURLCode $upstream_status;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_hide_header X-Frame-Options;
        }
		location ^~ /api/zhjg-basic-business/ {
            rewrite ^/api/zhjg-basic-business/(.*) /$1 break;
            proxy_pass   http://*************:9111;
            proxy_next_upstream http_500 http_502 error timeout;
            proxy_set_header HOST $host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Kss-Upstream $upstream_addr;
            add_header serverURL $upstream_addr;
            add_header serverURLCode $upstream_status;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_hide_header X-Frame-Options;
        }
		location /api/zhjg-auth/jwt/terminal/cnp/login  {
            proxy_pass http://*************:9110/bsp/app/login/siwLogin;
            proxy_next_upstream http_500 http_502 error timeout;
            proxy_set_header HOST $host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Kss-Upstream $upstream_addr;
            add_header serverURL $upstream_addr;
            add_header serverURLCode $upstream_status;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_hide_header X-Frame-Options;
        }
		location /api/zhjg-auth/jwt/terminal/cnp/passwordLogin  {
            proxy_pass http://*************:9110/bsp/app/login/sowLogin;
            proxy_next_upstream http_500 http_502 error timeout;
            proxy_set_header HOST $host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Kss-Upstream $upstream_addr;
            add_header serverURL $upstream_addr;
            add_header serverURLCode $upstream_status;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_hide_header X-Frame-Options;
        }
		location /socket.io/  {
            proxy_pass http://localhost:9182/socket.io/;
            proxy_next_upstream http_500 http_502 error timeout;
            proxy_set_header HOST $host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Kss-Upstream $upstream_addr;
            add_header serverURL $upstream_addr;
            add_header serverURLCode $upstream_status;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_hide_header X-Frame-Options;
        }
		location ^~ /api/acp-com-app/ {
            rewrite ^/api/acp-com-app/(.*) /$1 break;
            proxy_pass   http://localhost:9100;
            proxy_next_upstream http_500 http_502 error timeout;
            proxy_set_header HOST $host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Kss-Upstream $upstream_addr;
            add_header serverURL $upstream_addr;
            add_header serverURLCode $upstream_status;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_hide_header X-Frame-Options;
        }
		location ^~ /api/ihc/ {
		    rewrite ^/api/ihc/(.*) /$1 break;
            proxy_pass   http://localhost:9200;
            proxy_next_upstream http_500 http_502 error timeout;
            proxy_set_header HOST $host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Kss-Upstream $upstream_addr;
            add_header serverURL $upstream_addr;
            add_header serverURLCode $upstream_status;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        location ^~ /api/app/pam/ {
            proxy_pass   http://localhost:9600/app/pam/;
            proxy_next_upstream http_500 http_502 error timeout;
            proxy_set_header HOST $host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Kss-Upstream $upstream_addr;
            add_header serverURL $upstream_addr;
            add_header serverURLCode $upstream_status;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }

		location /api/vcps-oss-service/files/ihcs/upload  {
            proxy_pass http://localhost:9200/v2/upload;
            proxy_next_upstream http_500 http_502 error timeout;
            proxy_set_header HOST $host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Kss-Upstream $upstream_addr;
            add_header serverURL $upstream_addr;
            add_header serverURLCode $upstream_status;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_hide_header X-Frame-Options;
        }
		
    }

}
