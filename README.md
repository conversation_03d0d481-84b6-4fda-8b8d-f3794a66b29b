# rs-master

监管实战平台主项目

## Getting started

To make it easy for you to get started with GitLab, here's a list of recommended next steps.

Already a pro? Just edit this README.md and make it your own. Want to make it easy? [Use the template at the bottom](#editing-this-readme)!

## Add your files

- [ ] [Create](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#create-a-file) or [upload](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#upload-a-file) files
- [ ] [Add files using the command line](https://docs.gitlab.com/ee/gitlab-basics/add-file.html#add-a-file-using-the-command-line) or push an existing Git repository with the following command:

```
cd existing_repo
git remote add origin https://192.168.160.225/pd-rs/code/rs-master.git
git branch -M main
git push -uf origin main
```
## 🐨 技术栈

### 微服务

| 项目                    | 说明              |
|-----------------------|-----------------|
| `rs-dependencies`     | Maven 依赖版本管理    |
| `rs-framework`        | Java 框架拓展       |
| `rs-gateway`          | 网关服务            |
| `rs-module-acp`       | 实战平台 Module 模块  |
| `rs-module-ihc`       | 医疗子系统 Module 模块 |
| `rs-module-infra`     |                 |
| `rs-module-rgf`       |                 |
| `rs-module-third-api` | 第三方对接           |

