<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.rs</groupId>
		<artifactId>rs-master</artifactId>
		<version>${rs.version}</version>
	</parent>

	<artifactId>rs-module-third-api</artifactId>
	<packaging>jar</packaging>
	<name>${project.artifactId}</name>
	<description>监所管理-第三方接口模块</description>

	<dependencies>

		<!-- 技术组件 begin -->

		<!-- 海康安全认证SDK -->
		<dependency>
			<groupId>com.hikvision.ga</groupId>
			<artifactId>artemis-http-client</artifactId>
			<version>1.1.14.RELEASE</version>
		</dependency>
		<!-- 清研手环 -->
		<dependency>
			<groupId>com.tsingoal</groupId>
			<artifactId>WebsocketClient</artifactId>
			<version>2.3.3</version>
		</dependency>
		<dependency>
			<groupId>org.java-websocket</groupId>
			<artifactId>Java-WebSocket</artifactId>
			<version>1.5.3</version> <!-- 请根据需要选择版本 -->
		</dependency>
		<!-- 技术组件 end -->

		<!-- web 相关 begin -->
<!--		<dependency>-->
<!--			<groupId>org.springframework</groupId>-->
<!--			<artifactId>spring-core</artifactId>-->
<!--			<scope>provided</scope> &lt;!&ndash; 设置为 provided，只有工具类需要使用到 &ndash;&gt;-->
<!--		</dependency>-->

		<!-- web 相关 end -->

		<!-- bsp 相关 begin -->

		<!-- bsp 相关 end -->

		<!-- DB 相关 begin -->

        <!-- DB 相关 end -->

		<!-- 工具类相关 begin -->
<!--		<dependency>-->
<!--			<groupId>cn.hutool</groupId>-->
<!--			<artifactId>hutool-all-u</artifactId>-->
<!--			<version>${hutool.version}</version>-->
<!--		</dependency>-->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>${lombok.version}</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.83</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.11</version>
		</dependency>
		<dependency>
		  	<groupId>org.bouncycastle</groupId>
 			<artifactId>bcprov-jdk15on</artifactId>
		  	<version>1.69</version>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>compile</scope>
		</dependency>

		<dependency>
			<groupId>org.junit.platform</groupId>
			<artifactId>junit-platform-launcher</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.junit.vintage</groupId>
			<artifactId>junit-vintage-engine</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-expression</artifactId>
			<scope>provided</scope> <!-- 设置为 provided，只有工具类需要使用到 -->
		</dependency>
	</dependencies>
</project>
