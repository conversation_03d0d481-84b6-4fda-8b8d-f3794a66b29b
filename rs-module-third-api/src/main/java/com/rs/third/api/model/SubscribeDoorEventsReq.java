package com.rs.third.api.model;

import lombok.Data;

import java.util.List;

/**
 * @Description: 订阅门禁事件请求参数
 * <AUTHOR>
 * @Date 2025/7/28 17:06
 */
@Data
public class SubscribeDoorEventsReq {
    /**
     * 事件类型列表
     * AccessControlType
     *
     */
    private List<String> eventTypes;

    /**
     * 订阅方式（1：事件回调（1s100条事件上限）；2：通过MQTT对接rmq；不填默认为1）
     */
    private Integer subWay;

    /**
     * 指定事件接收的地址，subWay选择事件回调时必填此参数。采用restful回调模式，支持http和https，
     * 样式如下：http://ip:port/eventRcv或者https://ip:port/eventRcv不超过1024个字符，事件接收地址由应用方负责按指定的规范提供，
     * 报文格式如附录D中事件报文格式，事件接收接口不需要认证
     * 事件推送的目标URL.
     */
    private String eventDest;

    /**
     * 订阅类型（订阅类型，0-订阅原始事件，1-联动事件，2-原始事件和联动事件，不填使用默认值0）
     */
    private Integer subType;

    /**
     * 事件级别列表
     * 事件等级，0-未配置，1-低，2-中，3-高；此处事件等级是指在事件联动中配置的等级；订阅类型为0时，此参数无效，使用默认值0；在订阅类型为1时，
     * 不填使用默认值[1,2,3]；在订阅类型为2时，不填使用默认值[0,1,2,3]；数组大小不超过32，事件等级大小不超过31
     */
    private List<Integer> eventLvl;

}
