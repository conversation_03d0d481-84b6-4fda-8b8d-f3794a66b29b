package com.rs.third.api.dto.haikang.tvwall;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class TvWallFloatWndsDTO {
    @JSONField(name = "floatwnd_list")
    private List<FloatWndInfoDTO> floatwndList;

    @Data
    public static class FloatWndInfoDTO {
        @JSONField(name = "id")
        private Integer id;

        @JSONField(name = "left")
        private Integer left;

        @JSONField(name = "top")
        private Integer top;

        @JSONField(name = "width")
        private Integer width;

        @JSONField(name = "height")
        private Integer height;

        @JSONField(name = "layer")
        private Integer layer;

        @JSONField(name = "dev_id")
        private Integer devId;

        @JSONField(name = "subwnd_num")
        private Integer subwndNum;

        @JSONField(name = "decoder_id")
        private Integer decoderId;

        @JSONField(name = "wndpos")
        private Integer wndpos;

        @JSONField(name = "wnd_id")
        private Integer wndId;

        @JSONField(name = "uri")
        private String uri;

        @JSONField(name = "attached_uri")
        private String attachedUri;

        @JSONField(name = "dlp_id")
        private Integer dlpId;

        @JSONField(name = "openwnd_mode")
        private Integer openwndMode;

        @JSONField(name = "dlp_row")
        private Integer dlpRow;

        @JSONField(name = "dlp_col")
        private Integer dlpCol;

        @JSONField(name = "device_wall_no")
        private Integer deviceWallNo;
    }
}
