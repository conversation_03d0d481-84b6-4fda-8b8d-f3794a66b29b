package com.rs.third.api.model.tvwall;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class TvWallAlarmAddReq {
    @JSONField(name = "event_name")
    private String eventName;

    @JSONField(name = "event_index")
    private String eventIndex;

    @JSO<PERSON>ield(name = "dlp_indexcode")
    private String dlpIndexcode;

    @JSONField(name = "event_level")
    private Integer eventLevel;

    @JSONField(name = "event_keep_time")
    private Integer eventKeepTime;

    @JSONField(name = "alarm_stream_type")
    private Integer alarmStreamType;

    @JSONField(name = "camera_list")
    private String cameraList;

    @JSONField(name = "wnd_list")
    private String wndList;

    @JSONField(name = "event_desc")
    private String eventDesc;
}
