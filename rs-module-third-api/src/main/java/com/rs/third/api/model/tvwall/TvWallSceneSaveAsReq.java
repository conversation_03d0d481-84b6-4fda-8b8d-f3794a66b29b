package com.rs.third.api.model.tvwall;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class TvWallSceneSaveAsReq {
    @JSONField(name = "name")
    private String name;

    @JSONField(name = "dlp_id")
    private Integer dlpId;

    @JSONField(name = "scene_id")
    private Integer sceneId;

    @JSONField(name = "device_scene")
    private Integer deviceScene;

    @JSONField(name = "copy_cycle")
    private Integer copyCycle;

    @JSONField(name = "copy_cycle_list")
    private String copyCycleList;
}