package com.rs.third.api.component.haikang;

import com.alibaba.fastjson.JSON;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.constant.Constants;
import com.rs.third.api.config.HaiKangProperties;
import com.rs.third.api.utils.DoorResourceMapGenerator;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 海康基础组件
 *
 * <AUTHOR>
 * @Date 2025/7/23 15:53
 */
@Slf4j
public class HaiKangBaseComponent {

    /**
     * 根据需求调整超时时间
     */
    static {
        //连接超时时间
        Constants.DEFAULT_TIMEOUT = 10000;
        //读取超时时间
        Constants.SOCKET_TIMEOUT = 60000;
    }

    private HaiKangProperties properties;

    public HaiKangBaseComponent(HaiKangProperties properties) {
        this.properties = properties;
    }

    public HaiKangProperties getProperties() {
        return properties;
    }

    public String sendRequest(Map<String, Object> paramMap, String url) throws Exception {
        String body = JSON.toJSON(paramMap).toString();
        final String getCamsApi = properties.getArtemisPath() + url;
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", getCamsApi);
            }
        };
        return ArtemisHttpUtil.doPostStringArtemis(properties.getConfig(), path, body, null, null, "application/json");
//        if (url.equals("/api/acs/v1/door/doControl")) {
//            List<String> doorIndexCodes = (List) paramMap.get("doorIndexCodes");
//            return getControlData(doorIndexCodes.get(0));
//        }
//        if (url.equals("/api/acs/v1/door/states")) {
//            return getStatesData(paramMap);
//        }
//        return getData();
    }

    private String getData() {
        Map<String, Object> data = new HashMap<>(0);
        data.put("code", "0");
        data.put("msg", "success");
        HashMap<Object, Object> objectHashMap = new HashMap<>(0);
        objectHashMap.put("total", 20);
        List<Map<String, Object>> mapList = DoorResourceMapGenerator.generateThirdPrisonDoorResourcesAsMap();
        objectHashMap.put("list", mapList);
        data.put("data", objectHashMap);
        return JSON.toJSONString(data);
    }

    private String getControlData(String doorIndexCode) {
        return "{\"code\":\"0\",\"msg\":\"success\",\"data\":[{\"doorIndexCode\":\"" + doorIndexCode + "\",\"controlResultCode\":0,\"controlResultDesc\":\"success\"}]}";
    }

    private String getStatesData(Map<String, Object> paramMap) {
        Map<String, Object> data = new HashMap<>(0);
        data.put("code", "0");
        data.put("msg", "success");
        Map<String, Object> dataList = new HashMap<>(0);
        List<Map<String, Object>> list = new ArrayList<>();
        List<String> doorIndexCodes = (List<String>) paramMap.get("doorIndexCodes");
        for (int i = 0; i < doorIndexCodes.size(); i++) {
            Map<String, Object> doorState = new HashMap<>(0);
            doorState.put("doorIndexCode", doorIndexCodes.get(i));
            int doorStateValue = i % 5;
            doorState.put("doorState", doorStateValue);
            list.add(doorState);
        }
        dataList.put("authDoorList", list);
        data.put("data", dataList);
        return JSON.toJSONString(data);
    }


}
