package com.rs.third.api.model.tvwall;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class TvWallFloatWndsDeleteReq {
    @JSONField(name = "floatwnd_list")
    private List<FloatWndDeleteItem> floatwndList;

    @Data
    public static class FloatWndDeleteItem {
        @JSONField(name = "dlp_id")
        private Integer dlpId;

        @JSONField(name = "floatwnd_id")
        private Integer floatwndId;
    }
}
