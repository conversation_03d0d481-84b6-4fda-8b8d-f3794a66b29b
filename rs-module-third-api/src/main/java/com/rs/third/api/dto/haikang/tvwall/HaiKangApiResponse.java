package com.rs.third.api.dto.haikang.tvwall;

/**
 * 海康API响应通用结构
 * @param <T> 数据类型
 */
public class HaiKangApiResponse<T> {
    private String code;
    private String msg;
    private T data;

    public HaiKangApiResponse(String code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    // getter和setter方法
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public boolean isSuccess() {
        return "0".equals(code);
    }
}
