package com.rs.third.api.config;

import com.hikvision.artemis.sdk.config.ArtemisConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "haikang")
@Data
public class HaiKangProperties {

    /**
     * API网关的后端服务上下文为：/artemis
     */
    private String artemisPath = "/artemis";
    /**
     * 海康平台区域根目录
     */
    private String region;
    /**
     * 实战平台组织编码
     */
    private String orgCode;
    /**
     * 海康平台API配置
     */
    private ArtemisConfig config;
    /**
     * 门禁事假回调链接地址
     */
    private String doorControlCallbackUrl;
    /**
     * 订阅的门禁事件，多个事件以逗号分隔
     */
    private String events;

}
