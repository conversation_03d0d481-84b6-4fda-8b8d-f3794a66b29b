package com.rs.third.api.dto.haikang.tvwall;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class WindowInfoDTO {
    @JSONField(name = "wnd_id")
    private Integer wndId;

    @JSONField(name = "wnd_uri")
    private String wndUri;

    @JSONField(name = "status")
    private Integer status;

    @JSONField(name = "camera")
    private CameraInfoDTO camera;

    @JSONField(name = "decoder")
    private DecoderInfoDTO decoder;
}
