package com.rs.third.api.dto.haikang.tvwall;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class TvWallFloatWndsDeleteDTO {
    @JSONField(name = "floatwnd_list")
    private List<DeleteResultDTO> floatwndList;

    @Data
    public static class DeleteResultDTO {
        @JSONField(name = "error_code")
        private Integer errorCode;

        @JSONField(name = "error_info")
        private String errorInfo;
    }
}