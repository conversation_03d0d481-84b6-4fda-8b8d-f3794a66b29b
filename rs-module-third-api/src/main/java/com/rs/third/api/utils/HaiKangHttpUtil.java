package com.rs.third.api.utils;

import com.alibaba.fastjson.JSON;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.rs.third.api.config.HaiKangProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * 海康Http工具类
 *
 * <AUTHOR>
 * @Date 2025/7/23 15:37
 */
public class HaiKangHttpUtil {

    private static HaiKangProperties haiKangProperties;

    public HaiKangHttpUtil(HaiKangProperties haiKangProperties) {
        HaiKangHttpUtil.haiKangProperties = haiKangProperties;
    }

    /**
     * 调用POST请求类型接口，
     * 接口实际url：https://ip:port/artemis/api/api/resource/v1/regions
     *
     * @return
     */
    public static String doPost(Map<String, String> paramMap, String url, String contentType) throws Exception {
        final String getCamsApi = haiKangProperties.getArtemisPath() + url;
        paramMap.put("pageNo", "1");
        paramMap.put("pageSize", "2");
        paramMap.put("treeCode", "0");
        String body = JSON.toJSON(paramMap).toString();
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", getCamsApi);
            }
        };
        return ArtemisHttpUtil.doPostStringArtemis(haiKangProperties.getConfig(), path, body, null, null, "application/json");
    }
}
