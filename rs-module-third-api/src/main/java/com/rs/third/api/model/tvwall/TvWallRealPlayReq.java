package com.rs.third.api.model.tvwall;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class TvWallRealPlayReq {
    @JSONField(name = "realplay_list")
    private List<RealPlayItem> realplayList;

    @Data
    public static class RealPlayItem {
        @JSONField(name = "wnd_uri")
        private String wndUri;

        @JSONField(name = "camera_indexcode")
        private String cameraIndexcode;

        @JSONField(name = "stream_type")
        private Integer streamType;
    }
}