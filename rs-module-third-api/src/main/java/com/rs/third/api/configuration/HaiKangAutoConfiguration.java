package com.rs.third.api.configuration;

import com.rs.third.api.component.haikang.HaiKangDoorControlComponent;
import com.rs.third.api.component.haikang.HaiKangTVWallControlComponent;
import com.rs.third.api.config.HaiKangProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(HaiKangProperties.class)
public class HaiKangAutoConfiguration {

    @Bean
    public HaiKangDoorControlComponent doorControlComponent(HaiKangProperties haiKangProperties) {
        return new HaiKangDoorControlComponent(haiKangProperties);
    }
    @Bean
    public HaiKangTVWallControlComponent tvwallControlComponent(HaiKangProperties haiKangProperties) {
        return new HaiKangTVWallControlComponent(haiKangProperties);
    }
}
