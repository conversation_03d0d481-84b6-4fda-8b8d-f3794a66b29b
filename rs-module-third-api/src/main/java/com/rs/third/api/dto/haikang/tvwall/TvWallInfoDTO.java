package com.rs.third.api.dto.haikang.tvwall;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class TvWallInfoDTO {
    @JSONField(name = "tvwall_id")
    private Integer tvwallId;

    @JSONField(name = "tvwall_name")
    private String tvwallName;

    @JSONField(name = "index_code")
    private String indexCode;

    @JSONField(name = "dlp_list")
    private List<DlpInfoDTO> dlpList;
}
