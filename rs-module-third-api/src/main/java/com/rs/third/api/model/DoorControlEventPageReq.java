package com.rs.third.api.model;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Description: 门禁控制点分页查询请求参数
 * <AUTHOR>
 * @Date 2025/7/23 16:59
 */
@Data
public class DoorControlEventPageReq {
    /**
     * 门禁点名称，模糊搜索，最大长度32，若包含中文，最大长度指不超过按照指定编码的字节长度，即getBytes("utf-8").length
     */
    private String doorName;
    /**
     * 门禁点唯一编号
     */
    private List<String> doorIndexCodes;
    /**
     * 开始时间（事件开始时间，采用ISO8601时间格式，与endTime配对使用，不能单独使用，时间范围最大不能超过3个月）
     */
    private Date startTime;
    /**
     * 结束时间（事件结束时间，采用ISO8601时间格式，与endTime配对使用，不能单独使用，时间范围最大不能超过3个月）
     */
    private Date endTime;
    /**
     * 入库开始时间，采用ISO8601时间格式，与receiveEndTime配对使用，不能单独使用，时间范围最大不能超过3个月
     */
    private Date receiveStartTime;
    /**
     * 入库结束时间，采用ISO8601时间格式，最大长度32个字符，与receiveStartTime配对使用，不能单独使用，时间范围最大不能超过3个月
     */
    private Date receiveEndTime;
    /**
     * 事件类型
     */
    private List<Integer> eventTypes;

    private Integer pageNo;
    private Integer pageSize;
    /**
     * 排序字段（支持doorName、eventTime填写排序的字段名称）排序字段,注意：排序字段必须是查询条件，否则返回参数错误
     */
    private String sort;
    /**
     * 降序升序,降序：desc 升序：asc
     */
    private String order;
}
