package com.rs.third.api.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 通用返回
 *
 * @param <T> 数据泛型
 * <AUTHOR>
 */
@Data
public class ResponeResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;
	/**
     * 状态码 = 0 成功，其他为失败
     */
    private Integer code;
    /**
     * 状态信息
     */
    private String msg;
    /**
     * 返回数据
     */
    private T data;
    /**
     * 是否成功
     */
	private Boolean success;

    public static <T> ResponeResult<T> error() {
        ResponeResult<T> result = new ResponeResult<>();
        result.code = 500;
        result.msg = "";
        result.success = false;
        return result;
    }

    public static <T> ResponeResult<T> success(T data) {
        ResponeResult<T> result = new ResponeResult<>();
        result.code = 0;
        result.data = data;
        result.msg = "操作成功";
        result.success = true;
        return result;
    }




}
