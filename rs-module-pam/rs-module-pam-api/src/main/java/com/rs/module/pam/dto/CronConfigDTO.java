package com.rs.module.pam.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/6/13 17:50
 */
@ApiModel(description = "管理后台 - 监所事务管理-心理测评-定时任务配置 Request VO")
@Data
public class CronConfigDTO {

    @ApiModelProperty(value = "开始时间", required = true)
    private Date startTime;

    @ApiModelProperty(value = "结束时间", required = true)
    private Date endTime;

    @ApiModelProperty(value = "类型", required = true)
    private String type;

    @ApiModelProperty(value = "间隔")
    private String num;

    @ApiModelProperty(value = "cron表达式")
    private String cron;
}
