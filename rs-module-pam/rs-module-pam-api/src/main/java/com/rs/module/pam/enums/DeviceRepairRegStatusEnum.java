package com.rs.module.pam.enums;

/**
 * 特殊餐申请状态枚举类
 *
 * <AUTHOR>
 * @Date 2025/5/25 16:23
 */
public enum DeviceRepairRegStatusEnum {
    DPD("01", "待派单"),
    DWX("02", "待维修"),
    YCL("03", "已处理"),
    YHL("04", "已忽略");

    DeviceRepairRegStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static  DeviceRepairRegStatusEnum  getByCode(String code) {
        for (DeviceRepairRegStatusEnum value : DeviceRepairRegStatusEnum.values()) {
            if(value.getCode().equals(code)){
                return value;
            }
        }
        return null;
    }
}
