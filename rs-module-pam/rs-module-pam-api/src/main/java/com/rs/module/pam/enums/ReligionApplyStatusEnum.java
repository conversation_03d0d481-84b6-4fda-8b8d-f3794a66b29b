package com.rs.module.pam.enums;

public enum  ReligionApplyStatusEnum {

    DSH("1", "待审批"),
    SHTG("2", "审核通过"),
    SHBTG("3", "审核不通过");

    ReligionApplyStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static ReligionApplyStatusEnum getByCode(String code) {
        for (ReligionApplyStatusEnum value : ReligionApplyStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("非法code");
    }
}
