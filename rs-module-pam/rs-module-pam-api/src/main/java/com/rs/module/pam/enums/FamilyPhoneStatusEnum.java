package com.rs.module.pam.enums;

/**
 * 特殊餐申请状态枚举类
 *
 * <AUTHOR>
 * @Date 2025/5/25 16:23
 */
public enum FamilyPhoneStatusEnum {
    DSH("01", "待审核"),
    SHTG("02", "审核通过"),
    SHBTG("03", "审核不通过");

    FamilyPhoneStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
