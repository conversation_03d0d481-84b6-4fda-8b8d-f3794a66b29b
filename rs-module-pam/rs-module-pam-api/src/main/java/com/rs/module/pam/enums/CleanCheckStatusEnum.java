package com.rs.module.pam.enums;

/**
 * 安全大检查状态枚举类
 * <AUTHOR>
 * @Date 2025/5/28 18:59
 */
public enum CleanCheckStatusEnum {

    DCL("01", "待处理"),
    DLDPS("02", "待领导批示"),
    YWC("03", "已完成");

    private final String code;
    private final String name;

    CleanCheckStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
