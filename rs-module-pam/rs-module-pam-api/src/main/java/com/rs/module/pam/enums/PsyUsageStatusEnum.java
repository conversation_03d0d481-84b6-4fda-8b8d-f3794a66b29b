package com.rs.module.pam.enums;

/**
 * 量表使用状态枚举类
 * <AUTHOR>
 * @Date 2025/6/10 20:41
 */
public enum PsyUsageStatusEnum {
    WWC("01", "未完成"),
    YQY("02", "已启用"),
    YTY("03", "已停用");

    private final String code;
    private final String name;

    PsyUsageStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
