package com.rs.module.pam.enums;

/**
 * 配餐类型枚举
 *
 * <AUTHOR>
 * @Date 2025/5/25 17:11
 */
public enum PublishBizTypeEnum {
    JSSWFB("01", "监室事务发布"),
    JYDB("02", "教育读本发布");

    private final String code;
    private final String name;

    PublishBizTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PublishBizTypeEnum getByType(String code) {
        for (PublishBizTypeEnum typeEnum : PublishBizTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        throw new RuntimeException("非法code");
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}