package com.rs.module.pam.enums;

/**
 * app收信记录枚举类
 *
 * <AUTHOR>
 * @Date 2025/5/25 16:23
 */
public enum LetterReceiveAppRecordTypeEnum {
    QB("1", "全部"),
    DQR("2", "待确认"),
    YQR("3", "已确认");

    LetterReceiveAppRecordTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static LetterReceiveAppRecordTypeEnum getByCode(String code) {
        for (LetterReceiveAppRecordTypeEnum value : LetterReceiveAppRecordTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
