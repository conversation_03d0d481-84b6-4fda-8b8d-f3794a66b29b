package com.rs.module.pam.enums;

/**
 * app借阅记录枚举类
 *
 * <AUTHOR>
 * @Date 2025/5/25 16:23
 */
public enum CommonAppRecordPeriodTypeEnum {
    QB("1", "全部"),
    J<PERSON>("2", "今天"),
    Z<PERSON>("3", "昨天"),
    JYZ("4", "近一周");

    CommonAppRecordPeriodTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static CommonAppRecordPeriodTypeEnum getByCode(String code) {
        for (CommonAppRecordPeriodTypeEnum value : CommonAppRecordPeriodTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
