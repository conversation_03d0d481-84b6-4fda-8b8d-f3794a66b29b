package com.rs.module.pam.enums;

public enum LetterPostJxspztEnum {
    DGJSH("01", "待管教审核"),
    DKZZSH("02", "待科/组长审核"),
    DSLDSH("03", "待所领导审核"),
    DJC("04", "待寄出"),
    YJC("05", "已寄出"),
    LXCL("06", "另行处理"),
    BTG("07", "不通过");

    LetterPostJxspztEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static LetterPostJxspztEnum getByCode(String code) {
        for (LetterPostJxspztEnum value : LetterPostJxspztEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
