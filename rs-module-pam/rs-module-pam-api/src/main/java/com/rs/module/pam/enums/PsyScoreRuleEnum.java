package com.rs.module.pam.enums;

/**
 * 计分规则枚举类
 * <AUTHOR>
 * @Date 2025/6/10 20:41
 */
public enum PsyScoreRuleEnum {
    XXJDF("01", "选项即得分"),
    ZWJF("02", "正误计分"),
    BJF("03","不计分");

    private final String code;
    private final String name;

    PsyScoreRuleEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
