package com.rs.module.pam.dto;

/**
 * <AUTHOR>
 * @Date 2025/6/15 18:06
 */

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class QuestionDTO {
    @ApiModelProperty(value = "总题数")
    private int total;
    @ApiModelProperty(value = "题号列表")
    private List<NumItem> numItems;
    @ApiModelProperty(value = "题目列表")
    private List<Question> questions;

    @Data
    public static class NumItem {
        @ApiModelProperty(value = "题目ID")
        private String questionId;
        @ApiModelProperty(value = "题目序号")
        private int th;
    }

    @Data
    public static class Question {
        @ApiModelProperty(value = "题目序号")
        private int th;
        @ApiModelProperty(value = "题目ID")
        private String questionId;
        @ApiModelProperty(value = "题目内容")
        private String questionText;
        @ApiModelProperty(value = "题目答案")
        private String answer;
        @ApiModelProperty(value = "题目分值")
        private BigDecimal score;
        @ApiModelProperty(value = "实际得分")
        private int actualScore;
        @ApiModelProperty(value = "题目选项列表")
        private List<Option> options;
    }

    @Data
    public static class Option {
        @ApiModelProperty(value = "题目ID")
        private String questionId;
        @ApiModelProperty(value = "选项编码")
        private String optionCode;
        @ApiModelProperty(value = "选项内容")
        private String optionText;
        @ApiModelProperty(value = "选项分值")
        private BigDecimal score;
        @ApiModelProperty(value = "是否选中")
        private boolean selected;
    }
}