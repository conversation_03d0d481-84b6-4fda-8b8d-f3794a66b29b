package com.rs.module.pam.enums;

/**
 * 配餐类型枚举
 *
 * <AUTHOR>
 * @Date 2025/5/25 17:11
 */
public enum CookMealTypeEnum {
    ORDINARY_MEAL("00", "普通餐"),
    HALAL_MEAL("01", "清真餐"),
    VEGETARIAN_MEAL("02", "素食餐"),
    HOLIDAY_MEAL("03", "节日餐"),
    RELIGIOUS_MEAL("04", "宗教餐"),
    DIET_MEAL("05", "保健餐"),
    ELDERLY_MEAL("06", "老年餐"),
    SICK_MEAL("07", "病号餐"),
    OTHER_MEAL("99", "其他餐");

    private final String code;
    private final String name;

    CookMealTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static CookMealTypeEnum getMealType(String code) {
        for (CookMealTypeEnum mealType : CookMealTypeEnum.values()) {
            if (mealType.getCode().equals(code)) {
                return mealType;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}