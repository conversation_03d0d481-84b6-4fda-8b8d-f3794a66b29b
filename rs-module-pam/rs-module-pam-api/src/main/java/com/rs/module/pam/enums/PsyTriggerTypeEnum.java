package com.rs.module.pam.enums;

/**
 * 心理测评 - 触发类型枚举
 * <AUTHOR>
 * @Date 2025/6/13 17:28
 */
public enum PsyTriggerTypeEnum {

    ZQCF("周期触发", "01", "01"),
    TJCF("条件触发", "02", "02"),
    DCCF("单次触发", "03", "03");


    private String name;
    private String code;
    private String planType;

    public static PsyTriggerTypeEnum getEnumByCode(String code) {
        for (PsyTriggerTypeEnum e : PsyTriggerTypeEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    PsyTriggerTypeEnum(String name, String code, String planType) {
        this.name = name;
        this.code = code;
        this.planType = planType;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public String getPlanType() {
        return planType;
    }
}
