package com.rs.module.pam.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/15 15:58
 */
@ApiModel(description = "管理后台 - 监所事务管理-心理测评-答题卡")
@Data
@Builder
public class AnswerCardDTO {

    @ApiModelProperty(value = "答题卡总分")
    private int totalScore;

    @ApiModelProperty(value = "测评结果")
    private String result;

    @ApiModelProperty(value = "量表ID")
    private String tableId;

    @ApiModelProperty(value = "题目列表")
    private List<AnswerCardItemDTO> items;

    @Data
    public static class AnswerCardItemDTO {
        @ApiModelProperty(value = "题目ID")
        private String questionId;
        @ApiModelProperty(value = "题目选项编码或者简答题答案")
        private String answerOptionCode;
        @ApiModelProperty(value = "题目得分")
        private int score;
    }
}
