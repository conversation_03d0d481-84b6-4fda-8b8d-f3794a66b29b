package com.rs.module.pam.enums;

/**
 * 特殊餐申请状态枚举类
 *
 * <AUTHOR>
 * @Date 2025/5/25 16:23
 */
public enum LetterReceiveStatusEnum {
    DGJSH("01", "待管教审核"),
    DKZZSH("02", "待科/组长审核"),
    DSLDSH("03", "待所领导审核"),
    DZJ("04", "待转交"),
    DQR("05", "待确认"),
    YQR("06", "已确认"),
    BTG("07", "不通过");

    LetterReceiveStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static  LetterReceiveStatusEnum  getByCode(String code) {
        for (LetterReceiveStatusEnum value : LetterReceiveStatusEnum.values()) {
            if(value.getCode().equals(code)){
                return value;
            }
        }
        return null;
    }
}
