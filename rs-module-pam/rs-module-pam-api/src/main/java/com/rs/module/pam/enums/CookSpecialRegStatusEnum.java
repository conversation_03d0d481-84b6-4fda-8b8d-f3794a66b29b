package com.rs.module.pam.enums;

/**
 * 特殊餐申请状态枚举类
 *
 * <AUTHOR>
 * @Date 2025/5/25 16:23
 */
public enum CookSpecialRegStatusEnum {
    DQR("00", "待确认"),
    DSH("01", "待审核"),
    SHTG("02", "审核通过"),
    SHBTG("03", "审核不通过");

    CookSpecialRegStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static CookSpecialRegStatusEnum getEnum(String code) {
        for (CookSpecialRegStatusEnum status : CookSpecialRegStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid CookSpecialRegStatusEnum code: " + code);
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
