package com.rs.module.pam.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 结果解析规则
 *
 * <AUTHOR>
 * @Date 2025/6/16 16:17
 */
@Data
public class ResultInterpRuleDTO {

    @ApiModelProperty(value = "最小值")
    private int min;
    @ApiModelProperty(value = "最大值")
    private int max;
    @ApiModelProperty(value = "结果")
    private String result;

    public ResultInterpRuleDTO getDTO(int score) {
        if (score >= min && score <= max) {
            return this;
        }
        return null;
    }
}
