package com.rs.module.pam.enums;

public enum  LetterPostJxztEnum {
    DJC("01", "待寄出"),
    YJC("02", "已寄出"),
    LXCL("03", "另行处理");

    LetterPostJxztEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
