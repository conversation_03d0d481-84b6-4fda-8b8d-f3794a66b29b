package com.rs.module.pam.enums;

/**
 * 报备状态
 *
 * <AUTHOR>
 * @Date 2025/5/19 20:20
 */
public enum ReportInfoStatusEnum {
    DSP("1", "待审批"), WQY("2", "未启用"), SXZ("3", "生效中"),
    YJS("4", "已结束"), SPBTG("5", "审批不通过"), YQWSH("6", "逾期未审核");

    ReportInfoStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
