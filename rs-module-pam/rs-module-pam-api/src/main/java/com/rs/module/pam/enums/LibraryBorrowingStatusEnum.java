package com.rs.module.pam.enums;

/**
 * 报备状态
 *
 * <AUTHOR>
 * @Date 2025/5/19 20:20
 */
public enum LibraryBorrowingStatusEnum {
    DSP("1", "待审批"), DFF("2", "待发放"), DGH("3", "待归还"),
    YGH("4", "已归还"), SHBTG("5", "审批不通过");

    LibraryBorrowingStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static LibraryBorrowingStatusEnum getByCode(String code) {
        for (LibraryBorrowingStatusEnum value : LibraryBorrowingStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
