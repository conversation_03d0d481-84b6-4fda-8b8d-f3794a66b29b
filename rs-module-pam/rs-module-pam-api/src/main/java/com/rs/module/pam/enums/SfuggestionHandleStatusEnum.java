package com.rs.module.pam.enums;

/**
 * 特殊餐申请状态枚举类
 *
 * <AUTHOR>
 * @Date 2025/5/25 16:23
 */
public enum SfuggestionHandleStatusEnum {
    WCL("01", "未处理"),
    YCL("12", "已处理");

    SfuggestionHandleStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
