package com.rs.module.pam.controller.admin.daily;

import com.bsp.security.util.SessionUserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.pam.controller.admin.daily.vo.*;
import com.rs.module.pam.entity.daily.LifeHolidaysDO;
import com.rs.module.pam.service.daily.LifeHolidaysService;

@Api(tags = "监所事务管理-一日生活制度节假日")
@RestController
@RequestMapping("/pam/daily/lifeHolidays")
@Validated
public class LifeHolidaysController {

    @Resource
    private LifeHolidaysService lifeHolidaysService;

    @PostMapping("/batchSave")
    @ApiOperation(value = "批量创建监所事务管理-一日生活制度节假日")
    public CommonResult<Boolean> batchSaveLifeHolidays(@Valid @RequestBody LifeHolidaysBatchSaveReqVO batchSaveReqVO) {
        lifeHolidaysService.batchSaveLifeHolidays(batchSaveReqVO);
        return success(true);
    }


    @PostMapping("/create")
    @ApiOperation(value = "创建监所事务管理-一日生活制度节假日")
    public CommonResult<String> createLifeHolidays(@Valid @RequestBody LifeHolidaysSaveReqVO createReqVO) {
        return success(lifeHolidaysService.createLifeHolidays(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新监所事务管理-一日生活制度节假日")
    public CommonResult<Boolean> updateLifeHolidays(@Valid @RequestBody LifeHolidaysSaveReqVO updateReqVO) {
        lifeHolidaysService.updateLifeHolidays(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除监所事务管理-一日生活制度节假日")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteLifeHolidays(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            lifeHolidaysService.deleteLifeHolidays(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得监所事务管理-一日生活制度节假日")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<LifeHolidaysRespVO> getLifeHolidays(@RequestParam("id") String id) {
        LifeHolidaysDO lifeHolidays = lifeHolidaysService.getLifeHolidays(id);
        return success(BeanUtils.toBean(lifeHolidays, LifeHolidaysRespVO.class));
    }

    /*@PostMapping("/page")
    @ApiOperation(value = "获得监所事务管理-一日生活制度节假日分页")
    public CommonResult<PageResult<LifeHolidaysRespVO>> getLifeHolidaysPage(@Valid @RequestBody LifeHolidaysPageReqVO pageReqVO) {
        PageResult<LifeHolidaysDO> pageResult = lifeHolidaysService.getLifeHolidaysPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LifeHolidaysRespVO.class));
    }*/

    @PostMapping("/list")
    @ApiOperation(value = "获得监所事务管理-一日生活制度节假日列表")
    public CommonResult<List<LifeHolidaysRespVO>> getLifeHolidaysList(@Valid @RequestBody LifeHolidaysListReqVO listReqVO) {
        if (StringUtils.isEmpty(listReqVO.getOrgCode())) {
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        List<LifeHolidaysDO> list = lifeHolidaysService.getLifeHolidaysList(listReqVO);
        return success(BeanUtils.toBean(list, LifeHolidaysRespVO.class));
    }
}
