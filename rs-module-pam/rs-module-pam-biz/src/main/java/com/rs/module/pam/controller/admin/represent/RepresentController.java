package com.rs.module.pam.controller.admin.represent;

import com.gosun.zhjg.common.msg.R;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.pam.controller.admin.represent.vo.extra.*;
import com.rs.module.pam.service.represent.RepresentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监室点名管理")
@RestController
@RequestMapping("/pam/represent")
@Validated
public class RepresentController {

    @Resource
    private RepresentService representService;


    @GetMapping("/selectById/{presentNo}")
    @ApiOperation(value = "获取监室点名详情")
    @LogRecordAnnotation(bizModule = "acp:represent", operateType = LogOperateType.QUERY, title = "获取监室点名详情",
            bizNo = "{{#dto.presentNo}}", success = "获取监室点名详情成功", fail = "错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#presentNo}}")
    @ApiImplicitParam(paramType = "path", name = "presentNo", value = "点名批号", required = true, dataType = "String")
    public CommonResult<List<RoomInfoRepresentVO>> getDetail(@PathVariable("presentNo") String presentNo) {
        return success(representService.selectById(presentNo));
    }

    @GetMapping(value = "/videoInfoList/{id}")
    @ApiOperation(value = "视频点名展示入口")
    @ApiImplicitParam(paramType = "path", name = "id", value = "列表id", required = true, dataType = "String")
    public CommonResult<RoomInfoRepresentVO> videoInfoList(@PathVariable("id") String id) {
        return success(representService.videoInfoList(id));
    }

    @PostMapping(value = "/confirmByVideo")
    @ApiOperation(value = "视频核实确认")
    @LogRecordAnnotation(bizModule = "acp:represent", operateType = LogOperateType.UPDATE, title = "监室点名-视频核实",
            bizNo = "{{#dto.id}}", success = "监室点名-视频核实成功", fail = "错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#dto}}")
    public CommonResult<?> confirmByVideo(@RequestBody ConfirmByVideoVO dto){
        return success(representService.confirmByVideo(dto));
    }

    @ApiOperation(value = "获取手动点名监室数据", responseContainer = "List", response = RoomInfoVO.class)
    @PostMapping("/getRepresentRoomInfo")
    public CommonResult getRepresentRoomInfo(@RequestBody RoomRepresentInfoVO reqVO) {
        return R.ResponseResult(representService.getRepresentInfo(reqVO.getRoomIds(), reqVO.getCode(), reqVO.getType()));
    }

    @ApiOperation(value = "手动点名")
    @PostMapping("/startRepresent")
    @LogRecordAnnotation(bizModule = "acp:represent", operateType = LogOperateType.CREATE, title = "监室点名-发起手动点名",
            success = "监室点名-发起手动点名成功", fail = "错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#roomPresentVO}}")
    public CommonResult startRepresent(@RequestBody RoomPresentVO roomPresentVO) {
        try {
            HandleRepresentVO vo = representService.startRepresent(roomPresentVO.getRooms(), roomPresentVO.getExpiryDate());
            return success(vo);
        } catch (Exception e) {
            e.printStackTrace();
            return error(e.getMessage());
        }
    }

    @ApiOperation(value = "结束点名-按照批号")
    @GetMapping("endRepresentByNo/{presentNo}")
    @ApiImplicitParam(paramType = "path", name = "presentNo", value = "点名批号", required = true, dataType = "String")
    public CommonResult endRepresentByNo(@PathVariable String presentNo) {
        representService.endRepresentByNo(presentNo);
        return success("结束成功");
    }


}
