package com.rs.module.pam.controller.admin.duty.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-监室值班记录关联签到 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RecordsSigninRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监室id")
    private String roomId;
    @ApiModelProperty("值班记录：pam_duty_records.id")
    private String recordsId;
    @ApiModelProperty("值班日期")
    private Date dutyDate;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("签到状态（字典：ZD_ZBGL_QDZT）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_ZBGL_QDZT")
    private String signinStatus;
    @ApiModelProperty("签到时间")
    private Date signinTime;
    @ApiModelProperty("抓拍图片地址")
    private String snapPictureUrl;
}
