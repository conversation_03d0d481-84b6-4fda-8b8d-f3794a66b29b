package com.rs.module.pam.entity.screen;

import com.rs.module.base.entity.pm.PrisonerTagDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CommonMeetingDO {

    @ApiModelProperty("预约添加时间")
    private Date addTime;

    @ApiModelProperty("机构编码")
    private String orgCode;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监室号")
    private String jsh;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("人员姓名")
    private String xm;

    @ApiModelProperty("头像")
    private String frontPhoto;

    @ApiModelProperty("诉讼环节")
    private String sshj;

    @ApiModelProperty("涉嫌罪名")
    private String sxzm;

    @ApiModelProperty("关押期限")
    private Date gyqx;

    @ApiModelProperty("会见室id")
    private String hjs;

    @ApiModelProperty("会见室名称")
    private String hjsName;

    @ApiModelProperty("预约会见开始时间")
    private String st;

    @ApiModelProperty("预约会见结束时间")
    private String et;

    @ApiModelProperty("业务类型")
    private String businessType;

    @ApiModelProperty("业务类型名称")
    private String businessTypeName;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("状态名称")
    private String statusName;
}
