package com.rs.module.pam.service.psy;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanListReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanSaveReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalPushTargePageReqVO;
import com.rs.module.pam.dto.CronConfigDTO;
import com.rs.module.pam.entity.psy.EvalPlanDO;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * 监所事务管理-心理测评计划 Service 接口
 *
 * <AUTHOR>
 */
public interface EvalPlanService extends IBaseService<EvalPlanDO> {

    /**
     * 创建监所事务管理-心理测评计划
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createOrUpdate(EvalPlanSaveReqVO createReqVO);

    /**
     * 删除监所事务管理-心理测评计划
     *
     * @param id 编号
     */
    void deleteEvalPlan(String id);

    /**
     * 获得监所事务管理-心理测评计划
     *
     * @param id 编号
     * @return 监所事务管理-心理测评计划
     */
    EvalPlanDO getEvalPlan(String id);

    /**
     * 获得监所事务管理-心理测评计划列表
     *
     * @param listReqVO 查询条件
     * @return 监所事务管理-心理测评计划列表
     */
    List<EvalPlanDO> getEvalPlanList(EvalPlanListReqVO listReqVO);

    /**
     * 获得cron表达式下次执行时间
     *
     * @param cronConfigDTO cron配置
     * @return cron表达式下次执行时间
     */
    List<String> getLastExceTime(CronConfigDTO cronConfigDTO) throws ParseException;

    /**
     * 更新启用状态
     *
     * @param id
     * @param enableStatus
     */
    void updateEableStatus(String id, String enableStatus);

    /**
     * 发送计划
     *
     * @param jobId
     * @param logId
     */
    void sendPlan(String jobId, String logId);

    /**
     * 根据计划编码获取计划
     *
     * @return
     */
    EvalPlanDO getByPlanCode(String planCode);

    /**
     * 根据计划ID获取计划信息
     *
     * @param id
     * @return
     */
    EvalPlanRespVO getEvalPlanRespVO(String id);

    /**
     * 获取推送对象列表
     *
     * @param pageReqVO
     * @return
     */
    List<Map<String, String>> getPushTargeList(EvalPushTargePageReqVO pageReqVO);


    boolean checkPlanByTableId(String tableId);

}