package com.rs.module.pam.service.duty;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.google.common.collect.Lists;
import com.gosun.zhjg.common.util.PinyinUtils;
import com.rs.framework.common.exception.ServerException;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.pam.controller.admin.duty.vo.GroupSaveReqVO;
import com.rs.module.pam.dao.duty.GroupDao;
import com.rs.module.pam.entity.duty.GroupDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 监所事务管理-值班组 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GroupServiceImpl extends BaseServiceImpl<GroupDao, GroupDO> implements GroupService {

    @Resource
    private GroupDao groupDao;
    @Resource
    private PrisonerService prisonerService;

    @Override
    public String createGroup(GroupSaveReqVO createReqVO) {
        ArrayList<String> prisonerJgrybmList = Lists.newArrayList(createReqVO.getJgrybm1(), createReqVO.getJgrybm2());
        // 查询出人员姓名
        List<PrisonerInDO> prinsonerInList = prisonerService.getPrisonerInOneList(prisonerJgrybmList);
        Map<String, String> prisonerNameMap = prinsonerInList.stream()
                .collect(Collectors.toMap(PrisonerInDO::getJgrybm, PrisonerInDO::getXm));
        String prisonName1 = prisonerNameMap.get(createReqVO.getJgrybm1());
        String prisonName2 = prisonerNameMap.get(createReqVO.getJgrybm2());
        if (StringUtils.isBlank(prisonName1)) {
            throw new RuntimeException("人员1不存在");
        }
        if (StringUtils.isBlank(prisonName2)) {
            throw new RuntimeException("人员2不存在");
        }
        // 校验人员是否已经编组
        for (String jgrybm : prisonerJgrybmList) {
            Integer count = groupDao.selectCount(new LambdaQueryWrapper<GroupDO>()
                    .eq(GroupDO::getOrgCode, createReqVO.getOrgCode())
                    .eq(GroupDO::getRoomId, createReqVO.getRoomId())
                    .and(a -> a.eq(GroupDO::getJgrybm1, jgrybm)
                            .or().eq(GroupDO::getJgrybm2, jgrybm)
                    )
            );
            if (count > 0) {
                throw new RuntimeException(String.format("人员%s已编组", prisonerNameMap.get(jgrybm)));
            }
        }
        // 生成组序号
        Integer groupNo = groupDao.getRoomDutyGroupNo(createReqVO.getOrgCode(), createReqVO.getRoomId());
        if (groupNo == null) {
            groupNo = 1;
        } else {
            groupNo++;
        }
        // 生成组名
        if (StringUtils.isBlank(createReqVO.getGroupName())) {
            String groupName = (PinyinUtils.getFirstSpell(prisonName1.charAt(0) + String.valueOf(PinyinUtils.getFirstSpell(prisonName2).charAt(0))).toUpperCase());
            groupName = groupName + "值班组";
            // 检测是否组名重复。重复加1
            List<GroupDO> groupList = groupDao.getByGroupName(createReqVO.getOrgCode(), createReqVO.getRoomId(), groupName);
            if (!groupList.isEmpty()) {
                final String gName = groupName;
                List<String> groupNameList = groupList.stream().map(GroupDO::getGroupName).collect(Collectors.toList());
                OptionalLong optional = groupNameList.stream().map(m -> m.replaceFirst(gName, ""))
                        .filter(NumberUtils::isNumber).mapToLong(Long::valueOf).max();
                if (optional.isPresent()) {
                    groupName += optional.getAsLong() + 1;
                } else {
                    groupName += 2;
                }
            }
            createReqVO.setGroupName(groupName);
        }
        GroupDO groupDO = new GroupDO();
        groupDO.setRoomId(createReqVO.getRoomId());
        groupDO.setGroupName(createReqVO.getGroupName());
        groupDO.setGroupNo(groupNo);
        groupDO.setJgrybm1(createReqVO.getJgrybm1());
        groupDO.setJgrybm2(createReqVO.getJgrybm2());
        groupDao.insert(groupDO);
        return groupDO.getId();
    }

    @Override
    public void deleteGroup(String id) {
        // 校验存在
        validateGroupExists(id);
        // 删除
        groupDao.deleteById(id);
    }

    @Override
    public List<GroupDO> selectList(String orgCode, String roomId) {
        return groupDao.selectList(orgCode, roomId);
    }

    private void validateGroupExists(String id) {
        if (groupDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-值班组数据不存在");
        }
    }


}
