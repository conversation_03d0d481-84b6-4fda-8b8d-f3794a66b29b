package com.rs.module.pam.controller.admin.library.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-图书借阅申请 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LibraryBorrowingRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String ryxm;
    @ApiModelProperty("监室id")
    private String roomId;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("图书id")
    private String libraryId;
    @ApiModelProperty("图书名称")
    private String libraryName;
    @ApiModelProperty("申请时间")
    private Date applyTime;
    @ApiModelProperty("借阅截止时间")
    private Date borrowingDeadlineTime;
    @ApiModelProperty("发放时间")
    private Date distributionTime;
    @ApiModelProperty("归还时间")
    private Date returnTime;
    @ApiModelProperty("借阅状态（字典：ZD_TSJY_JYZT）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_TSJY_JYZT")
    private String status;
    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;
    @ApiModelProperty("审批人姓名")
    private String approverXm;
    @ApiModelProperty("审批时间")
    private Date approverTime;
    @ApiModelProperty("审批结果")
    private String approvalResult;
    @ApiModelProperty("审核意见")
    private String approvalComments;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;

    private String cityName;

    private String cityCode;

    private String regName;

    private String regCode;

    private String orgName;

    private String orgCode;

    private Boolean isDel;

    private String addUser;

    private String addUserName;

    private Date addTime;

    private String updateUser;

    private String updateUserName;

    private Date updateTime;
}
