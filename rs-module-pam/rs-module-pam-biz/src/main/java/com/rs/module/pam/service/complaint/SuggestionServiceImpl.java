package com.rs.module.pam.service.complaint;

import com.rs.module.pam.enums.SfuggestionHandleStatusEnum;
import com.rs.module.pam.util.CommonUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.pam.controller.admin.complaint.vo.*;
import com.rs.module.pam.entity.complaint.SuggestionDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.pam.dao.complaint.SuggestionDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 监所事务管理-投诉建议 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SuggestionServiceImpl extends BaseServiceImpl<SuggestionDao, SuggestionDO> implements SuggestionService {

    @Resource
    private SuggestionDao suggestionDao;

    @Override
    public String createSuggestion(SuggestionSaveReqVO createReqVO) {
        // 插入
        SuggestionDO suggestion = BeanUtils.toBean(createReqVO, SuggestionDO.class);
        suggestion.setHandleStatus(SfuggestionHandleStatusEnum.WCL.getCode());
        suggestionDao.insert(suggestion);
        // 返回
        return suggestion.getId();
    }

    @Override
    public void updateSuggestion(SuggestionSaveReqVO updateReqVO) {
        // 校验存在
        validateSuggestionExists(updateReqVO.getId());
        // 更新
        SuggestionDO updateObj = BeanUtils.toBean(updateReqVO, SuggestionDO.class);
        suggestionDao.updateById(updateObj);
    }

    @Override
    public void deleteSuggestion(String id) {
        // 校验存在
        validateSuggestionExists(id);
        // 删除
        suggestionDao.deleteById(id);
    }

    private void validateSuggestionExists(String id) {
        if (suggestionDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-投诉建议数据不存在");
        }
    }

    @Override
    public SuggestionDO getSuggestion(String id) {
        return suggestionDao.selectById(id);
    }

    @Override
    public PageResult<SuggestionDO> getSuggestionPage(SuggestionPageReqVO pageReqVO) {
        return suggestionDao.selectPage(pageReqVO);
    }

    @Override
    public List<SuggestionDO> getSuggestionList(SuggestionListReqVO listReqVO) {
        return suggestionDao.selectList(listReqVO);
    }

    @Override
    public PageResult<SuggestionDO> pageRecord(int pageNo, int pageSize, String jgrybm, String type) {
        Map<String, Date> commonAppRecordPeriod = CommonUtils.getCommonAppRecordPeriod(type);
        return suggestionDao.pageRecord(pageNo, pageSize, jgrybm,
                commonAppRecordPeriod.get("startTime"), commonAppRecordPeriod.get("endTime"));
    }


}
