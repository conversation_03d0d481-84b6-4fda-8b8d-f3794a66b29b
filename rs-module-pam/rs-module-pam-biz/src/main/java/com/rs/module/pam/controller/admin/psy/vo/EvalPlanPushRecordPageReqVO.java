package com.rs.module.pam.controller.admin.psy.vo;

import io.swagger.annotations.ApiModel;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评推送记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EvalPlanPushRecordPageReqVO extends PageParam {

    @ApiModelProperty("测评编号")
    private String evalNo;

    @ApiModelProperty("计划名称")
    private String planName;

    @ApiModelProperty("计划编码")
    private String planCode;

    @ApiModelProperty("计划类型")
    private String planType;

    @ApiModelProperty("量表ID")
    private String tableId;

    @ApiModelProperty("量表名称")
    private String tableName;

    @ApiModelProperty("填写状态")
    private String fillingStatus;

    @ApiModelProperty("测评得分")
    private BigDecimal score;

    @ApiModelProperty("测评结果")
    private String evalResults;

    @ApiModelProperty("排序属性")
    List<OrderItem> orderFields;
}
