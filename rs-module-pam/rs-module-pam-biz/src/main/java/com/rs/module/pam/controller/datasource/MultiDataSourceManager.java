package com.rs.module.pam.controller.datasource;


import com.alibaba.druid.pool.DruidDataSource;
import org.apache.ibatis.datasource.DataSourceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.lang.reflect.Field;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

@Component
public class MultiDataSourceManager extends AbstractMultiRoutingDataSource{


	private final static String DATASOURCES_PROPERTYNAME = "targetDataSources";
	public final String DEFAULT_DATASOURCE = "default_DataSource";

	@Autowired
	DataSource dataSource;

	@Override
	protected Object determineCurrentLookupKey() {
		return super.determineTargetDataSource();
	}

	/**
	 * 设置新的数据源
	 */
	@Override
	public void setTargetDataSources(Map<Object, Object> targetDataSources) {
		super.setTargetDataSources(targetDataSources);
		super.afterPropertiesSet();
	}

	@Override
	public void afterPropertiesSet() {
		 Map<Object, Object> targetDataSources = new HashMap<>();
		 targetDataSources.put(DEFAULT_DATASOURCE, dataSource);
		 setTargetDataSources(targetDataSources);
	}
	/**
	 * 添加数据源
	 * @param key
	 * @param dataSource
	 * @throws IllegalAccessException
	 * @throws NoSuchFieldException
	 * <AUTHOR>
	 * @date 2016-5-9
	 */
	public void addDataSource(String key, Object dataSource)throws IllegalAccessException, NoSuchFieldException {
		Map<Object, Object> targetDataSources = getValue(this);
		targetDataSources.put(key, dataSource);
		setTargetDataSources(targetDataSources);
	}

	/**
	 * 移除数据源
	 * @param key 数据源ID
	 * @throws IllegalAccessException
	 * @throws NoSuchFieldException
	 * <AUTHOR>
	 * @throws SQLException
	 * @date 2016-5-9
	 */
	public void removeDataSource(String key) throws IllegalAccessException,NoSuchFieldException, SQLException {

		Map<Object, Object> targetDataSources = getValue(this);
		if (DATASOURCES_PROPERTYNAME.equals(key)) {
			throw new DataSourceException("datasource name :" + key + " can't be removed!");
		}
		DataSource dataSource = (DataSource) targetDataSources.get(key);
		if(dataSource != null){
			if(dataSource instanceof DruidDataSource){
				DruidDataSource basicDataSource = (DruidDataSource) dataSource;
				basicDataSource.close();
			}

			dataSource = null;
			targetDataSources.remove(key);
			setTargetDataSources(targetDataSources);
		}
	}


	/**
	 * 获得所有数据源
	 * @return Map<Object, Object>
	 * @throws IllegalAccessException
	 * @throws NoSuchFieldException
	 * <AUTHOR>
	 * @date 2016-5-9
	 */
	public Map<Object, Object> getDataSources() throws IllegalAccessException,NoSuchFieldException {
		Map<Object, Object> dataSources = getValue(this);
		return dataSources;
	}

	/**
	 * 获得值
	 * @param instance
	 * @return
	 * @throws IllegalAccessException
	 * @throws NoSuchFieldException
	 * <AUTHOR>
	 * @date 2016-5-9
	 */
	@SuppressWarnings("unchecked")
	private static Map<Object, Object> getValue(Object instance)throws IllegalAccessException, NoSuchFieldException{
		Map<Object, Object> valMap = (Map<Object, Object>) getValue(instance, DATASOURCES_PROPERTYNAME);
		return valMap;
	}

	/**
	 *
	 * @param instance
	 * @param fieldName
	 * @return
	 * @throws IllegalAccessException
	 * @throws NoSuchFieldException
	 * <AUTHOR>
	 * @date 2016-5-9
	 */
	private static Object getValue(Object instance, String fieldName) throws IllegalAccessException, NoSuchFieldException {
		Field field = AbstractMultiRoutingDataSource.class.getDeclaredField(fieldName);
		field.setAccessible(true);
		return field.get(instance);
	}
}
