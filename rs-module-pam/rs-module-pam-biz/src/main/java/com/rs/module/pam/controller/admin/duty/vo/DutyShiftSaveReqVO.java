package com.rs.module.pam.controller.admin.duty.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监室值班班次新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DutyShiftSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("监室id")
    private String roomId;

    private List<ShiftSaveReqVO> shiftList;

}
