package com.rs.module.pam.service.library;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.pam.cons.CommonConstants;
import com.rs.module.pam.controller.admin.library.vo.*;
import com.rs.module.pam.controller.app.vo.AppLibraryBorrowingSaveReqVO;
import com.rs.module.pam.dao.library.LibraryBorrowingDao;
import com.rs.module.pam.entity.library.LibraryBorrowingDO;
import com.rs.module.pam.entity.library.LibraryMgtDO;
import com.rs.module.pam.enums.LibraryBorrowingStatusEnum;
import com.rs.module.pam.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;


/**
 * 监所事务管理-图书借阅申请 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class LibraryBorrowingServiceImpl extends BaseServiceImpl<LibraryBorrowingDao, LibraryBorrowingDO> implements LibraryBorrowingService {

    @Resource
    private LibraryBorrowingDao libraryBorrowingDao;

    @Resource
    private LibraryBorrowingHandleService libraryBorrowingHandleService;

    @Resource
    private LibraryMgtService libraryMgtService;

    @Resource
    private BorrowingConfigService borrowingConfigService;


    private static String defKey = "tushujieyueliucheng";

    @Override
    public String createLibraryBorrowing(LibraryBorrowingSaveReqVO createReqVO) {
        // 插入
        LibraryBorrowingDO libraryBorrowing = BeanUtils.toBean(createReqVO, LibraryBorrowingDO.class);
        libraryBorrowingDao.insert(libraryBorrowing);
        // 返回
        return libraryBorrowing.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLibraryBorrowing(LibraryBorrowingSaveReqVO updateReqVO) {
        // 校验存在
        //validateLibraryBorrowingExists(updateReqVO.getId());
        LibraryBorrowingDO libraryBorrowingDO = libraryBorrowingDao.selectById(updateReqVO.getId());
        if (libraryBorrowingDO == null) {
            throw new ServerException("监所事务管理-图书借阅申请数据不存在");
        }
        // 更新
        LibraryBorrowingDO updateObj = BeanUtils.toBean(updateReqVO, LibraryBorrowingDO.class);
        libraryBorrowingDao.updateById(updateObj);
        // 新增操作表
        if (!StringUtils.equals(libraryBorrowingDO.getStatus(), updateObj.getStatus())) {
            SessionUser sessionUser = SessionUserUtil.getSessionUser();
            LibraryBorrowingHandleSaveReqVO libraryBorrowingHandleSaveReqVO = new LibraryBorrowingHandleSaveReqVO();
            libraryBorrowingHandleSaveReqVO.setStatus(updateObj.getStatus());
            libraryBorrowingHandleSaveReqVO.setBorrowingId(updateObj.getId());
            libraryBorrowingHandleSaveReqVO.setHandleTime(new Date());
            libraryBorrowingHandleSaveReqVO.setHandleUser(sessionUser.getName());
            libraryBorrowingHandleSaveReqVO.setHandleUserSfzh(sessionUser.getIdCard());
            libraryBorrowingHandleService.createLibraryBorrowingHandle(libraryBorrowingHandleSaveReqVO);
        }

    }

    @Override
    public void deleteLibraryBorrowing(String id) {
        // 校验存在
        validateLibraryBorrowingExists(id);
        // 删除
        libraryBorrowingDao.deleteById(id);
    }

    private void validateLibraryBorrowingExists(String id) {
        if (libraryBorrowingDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-图书借阅申请数据不存在");
        }
    }

    @Override
    public LibraryBorrowingDO getLibraryBorrowing(String id) {
        LibraryBorrowingDO libraryBorrowingDO = libraryBorrowingDao.selectById(id);
        if (Objects.nonNull(libraryBorrowingDO)) {
            LibraryMgtDO libraryMgt = libraryMgtService.getLibraryMgt(libraryBorrowingDO.getLibraryId());
            libraryBorrowingDO.setLibraryName(Objects.isNull(libraryMgt) ? "" : libraryMgt.getName());
        }
        return libraryBorrowingDO;
    }

    @Override
    public PageResult<LibraryBorrowingDO> getLibraryBorrowingPage(LibraryBorrowingPageReqVO pageReqVO) {
        return libraryBorrowingDao.selectPage(pageReqVO);
    }

    @Override
    public List<LibraryBorrowingDO> getLibraryBorrowingList(LibraryBorrowingListReqVO listReqVO) {
        return libraryBorrowingDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String appCreateLibraryBorrowing(AppLibraryBorrowingSaveReqVO createReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        LibraryMgtDO libraryMgt = libraryMgtService.getLibraryMgt(createReqVO.getLibraryId());
        if (Objects.isNull(libraryMgt)) {
            throw new ServerException("监所事务管理-图书管理数据不存在");
        }
        // 同一人对同一本书在未归还时只能借一次
        LambdaQueryWrapper<LibraryBorrowingDO> wrapper = Wrappers.lambdaQuery(LibraryBorrowingDO.class).eq(LibraryBorrowingDO::getJgrybm, createReqVO.getJgrybm())
                .eq(LibraryBorrowingDO::getLibraryId, createReqVO.getLibraryId())
                .notIn(LibraryBorrowingDO::getStatus, LibraryBorrowingStatusEnum.YGH, LibraryBorrowingStatusEnum.SHBTG);
        Integer borrowingFlag = libraryBorrowingDao.selectCount(wrapper);
        if (Objects.nonNull(borrowingFlag) && borrowingFlag > 0) {
            throw new ServerException("不允许重复借阅");
        }

        if (Objects.isNull(libraryMgt.getBorrowedInventory()) || libraryMgt.getBorrowedInventory() < 1) {
            throw new ServerException("该图书已无可借库存");
        }
        Map<String, Integer> borrowingConfigMap = borrowingConfigService.getBorrowingConfigMap();
        Integer zdjyLimit = borrowingConfigMap.get(CommonConstants.ZDJY_LIMIT);
        // 判断是否超过个人最大借阅数
        int count = this.count(Wrappers.lambdaQuery(LibraryBorrowingDO.class)
                .eq(LibraryBorrowingDO::getJgrybm, createReqVO.getJgrybm())
                .in(LibraryBorrowingDO::getStatus, LibraryBorrowingStatusEnum.DSP.getCode(),
                        LibraryBorrowingStatusEnum.DFF.getCode(), LibraryBorrowingStatusEnum.DGH.getCode()));
        if (count >= zdjyLimit) {
            throw new ServerException("已超过个人最大借阅数");
        }
        //app申请借书
        LibraryBorrowingDO libraryBorrowing = BeanUtils.toBean(createReqVO, LibraryBorrowingDO.class);
        // 设置为待审批
        libraryBorrowing.setStatus(LibraryBorrowingStatusEnum.DSP.getCode());
        libraryBorrowing.setApplyTime(new Date());
        libraryBorrowingDao.insert(libraryBorrowing);
        //保存借书处理数据
        LibraryBorrowingHandleSaveReqVO libraryBorrowingHandleSaveReqVO = new LibraryBorrowingHandleSaveReqVO();
        libraryBorrowingHandleSaveReqVO.setStatus(libraryBorrowing.getStatus());
        libraryBorrowingHandleSaveReqVO.setBorrowingId(libraryBorrowing.getId());
        libraryBorrowingHandleSaveReqVO.setHandleTime(new Date());
        libraryBorrowingHandleSaveReqVO.setHandleUser(sessionUser.getName());
        libraryBorrowingHandleSaveReqVO.setHandleUserSfzh(sessionUser.getIdCard());
        libraryBorrowingHandleService.createLibraryBorrowingHandle(libraryBorrowingHandleSaveReqVO);
        //启动流程
        //todo 跳转链接
        String msgUrl = "/#/libraryBorrowing/borrowingManage";
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", libraryBorrowing.getId());
        JSONObject result = BspApprovalUtil.defaultStartProcess(HttpUtils.getAppCode() + "-" + defKey, libraryBorrowing.getId(), "图书借阅流程审批", msgUrl, variables);
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            libraryBorrowing.setActInstId(bpmTrail.getString("actInstId"));
            libraryBorrowing.setTaskId(bpmTrail.getString("taskId"));
            libraryBorrowingDao.updateById(libraryBorrowing);
            libraryMgtService.updateBorrowedInventory(createReqVO.getLibraryId(), -1);
        } else {
            throw new ServerException("流程启动失败");
        }

        return libraryBorrowing.getId();
    }

    @Override
    public void approvalLibraryBorrowing(LibraryBorrowingApprovalReqVO approvalReqVO) {
        LibraryBorrowingDO libraryBorrowingDO = libraryBorrowingDao.selectById(approvalReqVO.getId());
        if (libraryBorrowingDO == null) {
            throw new ServerException("监所事务管理-图书借阅申请数据不存在");
        }
        String nowStatus = LibraryBorrowingStatusEnum.getByCode(libraryBorrowingDO.getStatus()).getName();
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        // 待发放==审核通过
        if (LibraryBorrowingStatusEnum.DFF.getCode().equals(approvalReqVO.getStatus()) ||
                LibraryBorrowingStatusEnum.SHBTG.getCode().equals(approvalReqVO.getStatus())) {
            if (!LibraryBorrowingStatusEnum.DSP.getCode().equals(libraryBorrowingDO.getStatus())) {
                throw new ServerException("非法审批，当前为【" + nowStatus + "】状态");
            }
            //校验当前人有没有审批权限
            Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(libraryBorrowingDO.getTaskId(), sessionUser.getIdCard());
            if (!isApproval) {
                throw new ServerException("当前人无审批权限");
            }
            libraryBorrowingDO.setApproverXm(sessionUser.getName());
            libraryBorrowingDO.setApproverSfzh(sessionUser.getIdCard());
            libraryBorrowingDO.setApproverTime(new Date());
            libraryBorrowingDO.setApprovalComments(approvalReqVO.getApprovalComments());

            Map<String, Object> variables = new HashMap<>();
            variables.put("ywbh", libraryBorrowingDO.getId());
            //审批
            JSONObject nowApproveUser = new JSONObject();
            nowApproveUser.put("orgCode", sessionUser.getOrgCode());
            nowApproveUser.put("orgName", sessionUser.getOrgName());
            nowApproveUser.put("idCard", sessionUser.getId());
            nowApproveUser.put("name", sessionUser.getName());
            BspApproceStatusEnum bspApproceStatusEnum = LibraryBorrowingStatusEnum.DFF.getCode().equals(approvalReqVO.getStatus()) ?
                    BspApproceStatusEnum.PASSED_END : BspApproceStatusEnum.NOT_PASSED_END;
            boolean terminateTask = BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ||
                    BspApproceStatusEnum.NOT_PASSED_END.getCode() == bspApproceStatusEnum.getCode();
            JSONObject result = BspApprovalUtil.approvalProcess(HttpUtils.getAppCode() + "-" + defKey, libraryBorrowingDO.getActInstId(),
                    libraryBorrowingDO.getTaskId(), libraryBorrowingDO.getId(),
                    bspApproceStatusEnum, libraryBorrowingDO.getApprovalComments(), null, null, terminateTask,
                    variables, nowApproveUser, HttpUtils.getAppCode());
            log.info("=====result:{}", result);
            if (result.getIntValue("code") == HttpStatus.OK.value()) {
                JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
                libraryBorrowingDO.setTaskId(bpmTrail.getString("taskId"));
                if (LibraryBorrowingStatusEnum.SHBTG.getCode().equals(approvalReqVO.getStatus())) {
                    libraryMgtService.updateBorrowedInventory(libraryBorrowingDO.getLibraryId(), 1);
                }
            } else {
                throw new ServerException("流程启动失败");
            }
            libraryBorrowingDO.setApprovalResult("" + bspApproceStatusEnum.getCode());
        } else if (LibraryBorrowingStatusEnum.DGH.getCode().equals(approvalReqVO.getStatus())) {
            if (!LibraryBorrowingStatusEnum.DFF.getCode().equals(libraryBorrowingDO.getStatus())) {
                throw new ServerException("非法审批，当前为【" + nowStatus + "】状态");
            }
            if (Objects.isNull(approvalReqVO.getDistributionTime())) {
                throw new ServerException("发放时间不能为空");
            }
            // 待归还==已发放
            LibraryMgtDO tmp = libraryMgtService.getLibraryMgt(libraryBorrowingDO.getLibraryId());
            if (Objects.isNull(tmp.getBorrowedInventory()) || tmp.getBorrowedInventory() < 0) {
                throw new ServerException("该图书已无可发放库存");
            }
            // 发放时间
            libraryBorrowingDO.setDistributionTime(approvalReqVO.getDistributionTime());
            Map<String, Integer> borrowingConfigMap = borrowingConfigService.getBorrowingConfigMap();
            Integer duration = borrowingConfigMap.get(CommonConstants.ZDJY_DURATION);
            // 每天的毫秒数
            long millisecondsInDay = 24 * 60 * 60 * 1000L;
            libraryBorrowingDO.setBorrowingDeadlineTime(new Date(approvalReqVO.getDistributionTime().getTime() + (duration * millisecondsInDay)));
        } else if (LibraryBorrowingStatusEnum.YGH.getCode().equals(approvalReqVO.getStatus())) {
            if (!LibraryBorrowingStatusEnum.DGH.getCode().equals(libraryBorrowingDO.getStatus())) {
                throw new ServerException("非法审批，当前为【" + nowStatus + "】状态");
            }
            if (Objects.isNull(approvalReqVO.getReturnTime())) {
                throw new ServerException("归还时间不能为空");
            }
            // 已归还
            libraryMgtService.updateBorrowedInventory(libraryBorrowingDO.getLibraryId(), 1);
            libraryBorrowingDO.setReturnTime(approvalReqVO.getReturnTime());
        } else {
            throw new ServerException("非法状态参数");
        }
        libraryBorrowingDO.setStatus(approvalReqVO.getStatus());
        // 更新
        libraryBorrowingDao.updateById(libraryBorrowingDO);
        // 新增操作表

        LibraryBorrowingHandleSaveReqVO libraryBorrowingHandleSaveReqVO = new LibraryBorrowingHandleSaveReqVO();
        libraryBorrowingHandleSaveReqVO.setStatus(libraryBorrowingDO.getStatus());
        libraryBorrowingHandleSaveReqVO.setBorrowingId(libraryBorrowingDO.getId());
        libraryBorrowingHandleSaveReqVO.setHandleTime(new Date());
        libraryBorrowingHandleSaveReqVO.setHandleUser(sessionUser.getName());
        libraryBorrowingHandleSaveReqVO.setHandleUserSfzh(sessionUser.getIdCard());
        libraryBorrowingHandleService.createLibraryBorrowingHandle(libraryBorrowingHandleSaveReqVO);
    }

    @Override
    public JSONObject businessAnalysis(String orgCode) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        if (StringUtils.isEmpty(orgCode)) {
            orgCode = sessionUser.getOrgCode();
        }
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 获取本月的第一天 本月1号日期
        LocalDate firstDayOfMonth = today.withDayOfMonth(1);
        Date firstDayOfMonthDateTime = Date.from(firstDayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
        int dsp = 0, dff = 0, dgh = 0, ygh = 0, shbtg = 0;
        List<JSONObject> list = libraryBorrowingHandleService.getBusinessAnalysis(firstDayOfMonthDateTime, orgCode);
        for (JSONObject jsonObject : list) {
            if (LibraryBorrowingStatusEnum.DSP.getCode().equals(jsonObject.getString("status"))) {
                dsp = jsonObject.getIntValue("num");
            } else if (LibraryBorrowingStatusEnum.DFF.getCode().equals(jsonObject.getString("status"))) {
                dff = jsonObject.getIntValue("num");
            } else if (LibraryBorrowingStatusEnum.DGH.getCode().equals(jsonObject.getString("status"))) {
                dgh = jsonObject.getIntValue("num");
            } else if (LibraryBorrowingStatusEnum.YGH.getCode().equals(jsonObject.getString("status"))) {
                ygh = jsonObject.getIntValue("num");
            } else if (LibraryBorrowingStatusEnum.SHBTG.getCode().equals(jsonObject.getString("status"))) {
                shbtg = jsonObject.getIntValue("num");
            }
        }

        JSONObject result = new JSONObject();
        result.put("byjysq", dsp);
        result.put("bywff", dff - dgh);
        result.put("byygh", ygh);
        result.put("shbtg", shbtg);
        result.put("bywcl", dsp - dff - shbtg);
        return result;
    }

    @Override
    public PageResult<LibraryBorrowingDO> personalBorrowingRecordPage(int pageNo, int pageSize, String jgrybm, String type) {
        Map<String, Date> commonAppRecordPeriod = CommonUtils.getCommonAppRecordPeriod(type);
        Page<LibraryBorrowingDO> page = new Page<>(pageNo, pageSize);
        Page<LibraryBorrowingDO> result = libraryBorrowingDao.personalBorrowingRecordPage(page, jgrybm,
                commonAppRecordPeriod.get("startTime"), commonAppRecordPeriod.get("endTime"));
        return new PageResult<>(result.getRecords(), result.getTotal());
    }

}
