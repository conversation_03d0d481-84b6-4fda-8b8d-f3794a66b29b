package com.rs.module.pam.dao.device;

import java.util.*;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.entity.device.DeviceRepairRegDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.pam.controller.admin.device.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
 * 监所事务管理-设备报修登记 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceRepairRegDao extends IBaseDao<DeviceRepairRegDO> {


    default PageResult<DeviceRepairRegDO> selectPage(DeviceRepairRegPageReqVO reqVO) {
        Page<DeviceRepairRegDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<DeviceRepairRegDO> wrapper = new LambdaQueryWrapperX<DeviceRepairRegDO>()
                .eqIfPresent(DeviceRepairRegDO::getRepairNo, reqVO.getRepairNo())
                .eqIfPresent(DeviceRepairRegDO::getDataSources, reqVO.getDataSources())
                .eqIfPresent(DeviceRepairRegDO::getRoomId, reqVO.getRoomId())
                .likeIfPresent(DeviceRepairRegDO::getRoomName, reqVO.getRoomName())
                .eqIfPresent(DeviceRepairRegDO::getApplicantUserSfzh, reqVO.getApplicantUserSfzh())
                .likeIfPresent(DeviceRepairRegDO::getApplicantUserName, reqVO.getApplicantUserName())
                .betweenIfPresent(DeviceRepairRegDO::getApplicantTime, reqVO.getApplicantTime())
                .eqIfPresent(DeviceRepairRegDO::getRepairContent, reqVO.getRepairContent())
                .eqIfPresent(DeviceRepairRegDO::getDetailedLocation, reqVO.getDetailedLocation())
                .eqIfPresent(DeviceRepairRegDO::getRepairImgUrl, reqVO.getRepairImgUrl())
                .eqIfPresent(DeviceRepairRegDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(DeviceRepairRegDO::getDispatchTime, reqVO.getDispatchTime())
                .eqIfPresent(DeviceRepairRegDO::getDetailedUserSfzh, reqVO.getDetailedUserSfzh())
                .likeIfPresent(DeviceRepairRegDO::getDetailedUserName, reqVO.getDetailedUserName())
                .eqIfPresent(DeviceRepairRegDO::getHandleRemark, reqVO.getHandleRemark())
                .eqIfPresent(DeviceRepairRegDO::getHandleImgUrl, reqVO.getHandleImgUrl())
                .eqIfPresent(DeviceRepairRegDO::getHandleUserSfzh, reqVO.getHandleUserSfzh())
                .likeIfPresent(DeviceRepairRegDO::getHandleUserName, reqVO.getHandleUserName())
                .eqIfPresent(DeviceRepairRegDO::getStatus, reqVO.getStatus());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(DeviceRepairRegDO::getAddTime);
        }
        Page<DeviceRepairRegDO> deviceRepairRegPage = selectPage(page, wrapper);
        return new PageResult<>(deviceRepairRegPage.getRecords(), deviceRepairRegPage.getTotal());
    }

    default List<DeviceRepairRegDO> selectList(DeviceRepairRegListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DeviceRepairRegDO>()
                .eqIfPresent(DeviceRepairRegDO::getRepairNo, reqVO.getRepairNo())
                .eqIfPresent(DeviceRepairRegDO::getDataSources, reqVO.getDataSources())
                .eqIfPresent(DeviceRepairRegDO::getRoomId, reqVO.getRoomId())
                .likeIfPresent(DeviceRepairRegDO::getRoomName, reqVO.getRoomName())
                .eqIfPresent(DeviceRepairRegDO::getApplicantUserSfzh, reqVO.getApplicantUserSfzh())
                .likeIfPresent(DeviceRepairRegDO::getApplicantUserName, reqVO.getApplicantUserName())
                .betweenIfPresent(DeviceRepairRegDO::getApplicantTime, reqVO.getApplicantTime())
                .eqIfPresent(DeviceRepairRegDO::getRepairContent, reqVO.getRepairContent())
                .eqIfPresent(DeviceRepairRegDO::getDetailedLocation, reqVO.getDetailedLocation())
                .eqIfPresent(DeviceRepairRegDO::getRepairImgUrl, reqVO.getRepairImgUrl())
                .eqIfPresent(DeviceRepairRegDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(DeviceRepairRegDO::getDispatchTime, reqVO.getDispatchTime())
                .eqIfPresent(DeviceRepairRegDO::getDetailedUserSfzh, reqVO.getDetailedUserSfzh())
                .likeIfPresent(DeviceRepairRegDO::getDetailedUserName, reqVO.getDetailedUserName())
                .eqIfPresent(DeviceRepairRegDO::getHandleRemark, reqVO.getHandleRemark())
                .eqIfPresent(DeviceRepairRegDO::getHandleImgUrl, reqVO.getHandleImgUrl())
                .eqIfPresent(DeviceRepairRegDO::getHandleUserSfzh, reqVO.getHandleUserSfzh())
                .likeIfPresent(DeviceRepairRegDO::getHandleUserName, reqVO.getHandleUserName())
                .eqIfPresent(DeviceRepairRegDO::getStatus, reqVO.getStatus())
                .orderByDesc(DeviceRepairRegDO::getAddTime));
    }


    default PageResult<DeviceRepairRegDO> getDeviceRepairRegRecordPage(int pageNo, int pageSize, String applicantUserSfzh, Date startTime, Date endTime) {
        Page<DeviceRepairRegDO> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapperX<DeviceRepairRegDO> wrapper = new LambdaQueryWrapperX<DeviceRepairRegDO>()
                .eq(DeviceRepairRegDO::getApplicantUserSfzh, applicantUserSfzh);
        if (Objects.nonNull(startTime)) {
            wrapper.ge(DeviceRepairRegDO::getApplicantTime, startTime);
        }
        if (Objects.nonNull(endTime)) {
            wrapper.lt(DeviceRepairRegDO::getApplicantTime, endTime);
        }
        wrapper.orderByDesc(DeviceRepairRegDO::getApplicantTime);
        Page<DeviceRepairRegDO> deviceRepairRegPage = selectPage(page, wrapper);
        return new PageResult<>(deviceRepairRegPage.getRecords(), deviceRepairRegPage.getTotal());
    }

}
