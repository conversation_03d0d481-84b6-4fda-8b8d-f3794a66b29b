package com.rs.module.pam.controller.admin.daily.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/5/28 20:03
 */
@Data
@ApiModel(description = "管理后台 - 安全检查-领导批示 Request VO")
public class CleanleaderInstructionVO {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("所领导审核意见")
    private String leaderApprovalComments;

}
