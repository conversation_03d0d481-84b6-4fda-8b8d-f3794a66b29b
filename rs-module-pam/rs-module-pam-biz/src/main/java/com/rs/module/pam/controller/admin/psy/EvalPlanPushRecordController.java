package com.rs.module.pam.controller.admin.psy;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanPushRecordListReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanPushRecordRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanPushRecordSaveReqVO;
import com.rs.module.pam.entity.psy.EvalPlanPushRecordDO;
import com.rs.module.pam.enums.PsyFillingStatusEnum;
import com.rs.module.pam.service.psy.EvalPlanPushRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "心理测评-推送记录")
@RestController
@RequestMapping("/pam/psy/evalPlanPushRecord")
@Validated
public class EvalPlanPushRecordController {

    @Resource
    private EvalPlanPushRecordService evalPlanPushRecordService;

    @PostMapping("/create")
    @ApiOperation(value = "创建-心理测评推送记录")
    public CommonResult<String> createEvalPlanPushRecord(@Valid @RequestBody EvalPlanPushRecordSaveReqVO createReqVO) {
        return success(evalPlanPushRecordService.createEvalPlanPushRecord(createReqVO));
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得-心理测评推送记录")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<EvalPlanPushRecordRespVO> getEvalPlanPushRecord(@RequestParam("id") String id) {
        EvalPlanPushRecordDO evalPlanPushRecord = evalPlanPushRecordService.getEvalPlanPushRecord(id);
        return success(BeanUtils.toBean(evalPlanPushRecord, EvalPlanPushRecordRespVO.class));
    }


    @PostMapping("/list")
    @ApiOperation(value = "获得-心理测评推送记录列表")
    public CommonResult<List<EvalPlanPushRecordRespVO>> getEvalPlanPushRecordList(@Valid @RequestBody EvalPlanPushRecordListReqVO listReqVO) {
        return success(evalPlanPushRecordService.getEvalPlanPushRecordList(listReqVO));
    }

    @PostMapping("/getEvalPlanPushRecordListCount")
    @ApiOperation(value = "获得-心理测评推送记录统计数据")
    public CommonResult<Map<String, Long>> getEvalPlanPushRecordListCount(@RequestParam("planCode") String planCode) {
        Map<String, Long> map = new HashMap<>();
        PsyFillingStatusEnum[] values = PsyFillingStatusEnum.values();
        EvalPlanPushRecordListReqVO listReqVO = new EvalPlanPushRecordListReqVO();
        listReqVO.setPlanCode(planCode);
        for (PsyFillingStatusEnum value : values) {
            listReqVO.setFillingStatus(value.getCode());
            Long total = evalPlanPushRecordService.getEvalPlanPushRecordListCount(listReqVO);
            map.put(value.name(), total);
        }
        return success(map);
    }

}
