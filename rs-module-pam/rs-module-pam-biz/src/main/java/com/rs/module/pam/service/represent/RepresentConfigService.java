package com.rs.module.pam.service.represent;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.pam.controller.admin.represent.vo.RepresentConfigListReqVO;
import com.rs.module.pam.controller.admin.represent.vo.RepresentConfigPageReqVO;
import com.rs.module.pam.controller.admin.represent.vo.RepresentConfigSaveReqVO;
import com.rs.module.pam.entity.represent.RepresentConfigDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 监所事务管理-监室点名配置 Service 接口
 *
 * <AUTHOR>
 */
public interface RepresentConfigService extends IBaseService<RepresentConfigDO>{

    /**
     * 创建监所事务管理-监室点名配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createConfig(@Valid RepresentConfigSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-监室点名配置
     *
     * @param updateReqVO 更新信息
     */
    void updateConfig(@Valid RepresentConfigSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-监室点名配置
     *
     * @param id 编号
     */
    void deleteConfig(String id);

    /**
     * 获得监所事务管理-监室点名配置
     *
     * @param id 编号
     * @return 监所事务管理-监室点名配置
     */
    RepresentConfigDO getConfig(String id);

    /**
    * 获得监所事务管理-监室点名配置分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-监室点名配置分页
    */
    PageResult<RepresentConfigDO> getConfigPage(RepresentConfigPageReqVO pageReqVO);

    /**
    * 获得监所事务管理-监室点名配置列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-监室点名配置列表
    */
    List<RepresentConfigDO> getConfigList(RepresentConfigListReqVO listReqVO);


}
