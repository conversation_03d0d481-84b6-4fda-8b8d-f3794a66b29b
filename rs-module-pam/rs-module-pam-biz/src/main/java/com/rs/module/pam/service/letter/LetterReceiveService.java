package com.rs.module.pam.service.letter;

import java.util.*;
import javax.validation.*;
import com.rs.module.pam.controller.admin.letter.vo.*;
import com.rs.module.pam.controller.app.vo.AppConfirmLetterReqVO;
import com.rs.module.pam.entity.letter.LetterReceiveDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 监所事务管理-家属通信-收信登记 Service 接口
 *
 * <AUTHOR>
 */
public interface LetterReceiveService extends IBaseService<LetterReceiveDO>{

    /**
     * 创建监所事务管理-家属通信-收信登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createLetterReceive(@Valid LetterReceiveSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-家属通信-收信登记
     *
     * @param updateReqVO 更新信息
     */
    void updateLetterReceive(@Valid LetterReceiveSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-家属通信-收信登记
     *
     * @param id 编号
     */
    void deleteLetterReceive(String id);

    /**
     * 获得监所事务管理-家属通信-收信登记
     *
     * @param id 编号
     * @return 监所事务管理-家属通信-收信登记
     */
    LetterReceiveDO getLetterReceive(String id);

    /**
    * 获得监所事务管理-家属通信-收信登记分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-家属通信-收信登记分页
    */
    PageResult<LetterReceiveDO> getLetterReceivePage(LetterReceivePageReqVO pageReqVO);

    /**
    * 获得监所事务管理-家属通信-收信登记列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-家属通信-收信登记列表
    */
    List<LetterReceiveDO> getLetterReceiveList(LetterReceiveListReqVO listReqVO);


    void approvalLetterReceive(LetterReceiveApprovalReqVO approvalReqVO);

    void passLetter(PassLetterReqVO passLetterReqVO);

    void appConfirmLetter(AppConfirmLetterReqVO appConfirmLetterReqVO);

    PageResult<LetterReceiveDO> getAppLetterReceivePage(int pageNo, int pageSize, String jgrybm, String type);

}
