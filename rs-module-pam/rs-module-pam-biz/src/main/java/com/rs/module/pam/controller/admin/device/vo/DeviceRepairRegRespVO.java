package com.rs.module.pam.controller.admin.device.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-设备报修登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DeviceRepairRegRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("工单编号")
    private String repairNo;
    @ApiModelProperty("数据来源（字典：ZD_DATA_SOURCES）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_DATA_SOURCES")
    private String dataSources;
    @ApiModelProperty("监室ID")
    private String roomId;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("申请人")
    private String applicantUserSfzh;
    @ApiModelProperty("申请人姓名")
    private String applicantUserName;
    @ApiModelProperty("申请时间")
    private Date applicantTime;
    @ApiModelProperty("报修内容（字典：ZD_SBWXGL_BXNR）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_SBWXGL_BXNR")
    private String repairContent;
    @ApiModelProperty("详细地点")
    private String detailedLocation;
    @ApiModelProperty("报修照片地址")
    private String repairImgUrl;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("派单时间")
    private Date dispatchTime;
    @ApiModelProperty("派单人id")
    private String detailedUserSfzh;
    @ApiModelProperty("派单人姓名")
    private String detailedUserName;
    @ApiModelProperty("处理情况")
    private String handleRemark;
    @ApiModelProperty("处理照片地址")
    private String handleImgUrl;
    @ApiModelProperty("处理人身份证号")
    private String handleUserSfzh;
    @ApiModelProperty("处理人姓名")
    private String handleUserName;

    @ApiModelProperty("维修时间")
    private Date handleTime;

    @ApiModelProperty("报修状态（字典：ZD_SBWXGL_BXZT）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_SBWXGL_BXZT")
    private String status;

    private String cityName;

    private String cityCode;

    private String regName;

    private String regCode;

    private String orgName;

    private String orgCode;

    private Boolean isDel;

    private String addUser;

    private String addUserName;

    private Date addTime;

    private String updateUser;

    private String updateUserName;

    private Date updateTime;
}
