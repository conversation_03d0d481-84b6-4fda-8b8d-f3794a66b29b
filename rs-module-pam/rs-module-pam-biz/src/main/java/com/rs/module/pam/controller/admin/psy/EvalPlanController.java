package com.rs.module.pam.controller.admin.psy;

import cn.hutool.core.util.StrUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanListReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanSaveReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalPushTargePageReqVO;
import com.rs.module.pam.dto.CronConfigDTO;
import com.rs.module.pam.entity.psy.EvalPlanDO;
import com.rs.module.pam.service.psy.EvalPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "心理测评-心理测评计划")
@RestController
@RequestMapping("/pam/psy/evalPlan")
@Validated
public class EvalPlanController {

    @Resource
    private EvalPlanService evalPlanService;

    @PostMapping("/getLastExceTime")
    @ApiOperation(value = "心理测评-获取最新执行时间")
    public CommonResult<List> getLastExceTime(@Valid @RequestBody CronConfigDTO cronConfigDTO) throws ParseException {
        List<String> list = evalPlanService.getLastExceTime(cronConfigDTO);
        return success(list);
    }

    @PostMapping("/createOrUpdate")
    @ApiOperation(value = "创建或者更新-心理测评计划")
    public CommonResult<String> createOrUpdate(@Valid @RequestBody EvalPlanSaveReqVO createReqVO) {
        return success(evalPlanService.createOrUpdate(createReqVO));
    }

    @PostMapping("/updateEableStatus")
    @ApiOperation(value = "更新启用状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "编号", required = true),
            @ApiImplicitParam(name = "enableStatus", value = "状态(0: 禁用 1: 启用)", required = true)
    })
    public CommonResult updateEableStatus(@RequestParam("id") String id, @RequestParam("enableStatus") String enableStatus) {
        evalPlanService.updateEableStatus(id, enableStatus);
        return success();
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得-心理测评计划",response = EvalPlanRespVO.class)
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<EvalPlanRespVO> getEvalPlan(@RequestParam("id") String id) {
        EvalPlanRespVO evalPlan = evalPlanService.getEvalPlanRespVO(id);
        return success(evalPlan);
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得-心理测评计划列表")
    public CommonResult<List<EvalPlanRespVO>> getEvalPlanList(@Valid @RequestBody EvalPlanListReqVO listReqVO) {
        if (StrUtil.isEmpty(listReqVO.getOrgCode()) && !SessionUserUtil.getSessionUser().getIsAdmin()) {
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        List<EvalPlanDO> list = evalPlanService.getEvalPlanList(listReqVO);
        return success(BeanUtils.toBean(list, EvalPlanRespVO.class));
    }

    @PostMapping("/getPushTargeList")
    @ApiOperation(value = "获得-心理测评计划推送对象")
    public CommonResult getPushTargeList(@Valid @RequestBody EvalPushTargePageReqVO pageReqVO) {
        List<Map<String, String>> pushTargeList = evalPlanService.getPushTargeList(pageReqVO);
        return success(pushTargeList);
    }

    @GetMapping("/eval-plan-test-job")
    @ApiOperation(value = "心理测评定时任务测试")
    public CommonResult<Boolean> evalPlanTestJob(@RequestParam("jobId") String jobId,
                                                 @RequestParam("logId") String logId) {
        evalPlanService.sendPlan(jobId, logId);
        return success(true);
    }

}
