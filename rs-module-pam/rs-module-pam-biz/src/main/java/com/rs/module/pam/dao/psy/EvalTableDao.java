package com.rs.module.pam.dao.psy;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableListReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTablePageReqVO;
import com.rs.module.pam.entity.psy.EvalTableDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 监所事务管理-心理测评量表管理 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface EvalTableDao extends IBaseDao<EvalTableDO> {

    default PageResult<EvalTableDO> selectPage(EvalTablePageReqVO reqVO) {
        Page<EvalTableDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<EvalTableDO> wrapper = new LambdaQueryWrapperX<EvalTableDO>()
                .likeIfPresent(EvalTableDO::getName, reqVO.getName())
                .likeIfPresent(EvalTableDO::getTableNo, reqVO.getName())
                .likeIfPresent(EvalTableDO::getDescription, reqVO.getName())
                .eqIfPresent(EvalTableDO::getTableType, reqVO.getTableType())
                .eqIfPresent(EvalTableDO::getOrgCode, reqVO.getOrgCode());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(EvalTableDO::getAddTime);
        }
        Page<EvalTableDO> evalTablePage = selectPage(page, wrapper);
        return new PageResult<>(evalTablePage.getRecords(), evalTablePage.getTotal());
    }

    default List<EvalTableDO> selectList(EvalTableListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<EvalTableDO>()
                .likeIfPresent(EvalTableDO::getName, reqVO.getName())
                .likeIfPresent(EvalTableDO::getTableNo, reqVO.getName())
                .likeIfPresent(EvalTableDO::getDescription, reqVO.getName())
                .eqIfPresent(EvalTableDO::getTableType, reqVO.getTableType())
                .eqIfPresent(EvalTableDO::getUsageStatus, reqVO.getUsageStatus())
                .eqIfPresent(EvalTableDO::getOrgCode, reqVO.getOrgCode())
                .orderByDesc(EvalTableDO::getAddTime));
    }

    /**
     * 根据量表id删除关联题目
     *
     * @param tableId
     */
    @Delete("DELETE FROM pam_psy_eval_table_question USING pam_psy_eval_table_option WHERE " +
            "pam_psy_eval_table_question.table_id = #{tableId} AND pam_psy_eval_table_question.id = pam_psy_eval_table_option.question_id ")
    void deleteQuestion(@Param("tableId") String tableId);
}
