package com.rs.module.pam.controller.admin.psy.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评推送记录新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EvalPlanPushRecordSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("测评编号")
    @NotEmpty(message = "测评编号不能为空")
    private String evalNo;

    @ApiModelProperty("计划名称")
    @NotEmpty(message = "计划名称不能为空")
    private String planName;

    @ApiModelProperty("计划编码")
    @NotEmpty(message = "计划编码不能为空")
    private String planCode;

    @ApiModelProperty("计划类型")
    @NotEmpty(message = "计划类型不能为空")
    private String planType;

    @ApiModelProperty("计划创建人")
    @NotEmpty(message = "计划创建人不能为空")
    private String planAddUserName;

    @ApiModelProperty("量表ID")
    @NotEmpty(message = "量表ID不能为空")
    private String tableId;

    @ApiModelProperty("量表名称")
    @NotEmpty(message = "量表名称不能为空")
    private String tableName;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("推送时间")
    @NotNull(message = "推送时间不能为空")
    private Date pushTime;

    @ApiModelProperty("填写状态")
    @NotEmpty(message = "填写状态不能为空")
    private String fillingStatus;

    @ApiModelProperty("填写时间")
    private Date fillingTime;

    @ApiModelProperty("填写平台")
    private String fillingPlatform;

    @ApiModelProperty("测评得分")
    @NotNull(message = "测评得分不能为空")
    private BigDecimal score;

    @ApiModelProperty("测评结果")
    private String evalResults;

}
