package com.rs.module.pam.controller.admin.bed.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.annotation.GetFile;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.x.file.storage.core.FileInfo;

import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-监室床位布局 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LayoutRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("布局名称")
    private String layoutName;
    @ApiModelProperty("布局照片地址")
    private String layoutImageUrl;
    @ApiModelProperty("布局类型(01:底图,02:自定义)")
    private String layoutType;
    @ApiModelProperty("布局配置")
    private List<BedAreaConfigVO> bedAreaConfigs;
    @ApiModelProperty("布局宽度")
    private Integer width;
    @ApiModelProperty("布局高度")
    private Integer height;
    @ApiModelProperty("布局行数")
    private Integer layoutRow;
    @ApiModelProperty("布局列数")
    private Integer layoutColumn;
    @ApiModelProperty("排序")
    private Integer sort;
}
