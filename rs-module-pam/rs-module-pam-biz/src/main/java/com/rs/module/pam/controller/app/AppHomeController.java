package com.rs.module.pam.controller.app;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.entity.uac.UacUserRespVO;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.util.UacUserUtil;
import com.rs.module.base.constant.PoliceWarderUserTypeConstant;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderRespVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerAppRespVO;
import com.rs.module.base.controller.admin.pm.vo.RoomPoliceRespVO;
import com.rs.module.base.dao.pm.PrisonerInDao;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.entity.pm.PrisonerTagDO;
import com.rs.module.base.service.pm.PrisonRoomWarderService;
import com.rs.module.pam.controller.app.vo.AppDutyShiftRespVO;
import com.rs.module.pam.dao.screen.GjLargeScreenDao;
import com.rs.module.pam.service.duty.DutyService;
import com.rs.module.pam.service.duty.day.DayDutyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "仓外屏-首页")
@RestController
@RequestMapping("/app/pam/public/home")
@Validated
public class AppHomeController {

    @Resource
    private PrisonRoomWarderService roomWarderService;
    @Resource
    private PrisonerInDao prisonerInDao;
    @Resource
    private DutyService dutyService;
    @Resource
    private DayDutyService dayDutyService;
    @Resource
    private GjLargeScreenDao gjLargeScreenDao;

    @RequestMapping(value = "/getPolice", method = RequestMethod.GET)
    @ApiOperation(value = "获取主协管人员")
    public CommonResult<RoomPoliceRespVO> getPolice(@RequestParam("orgCode") String orgCode,
                                                         @RequestParam("roomId") String roomId) {
        List<PrisonRoomWarderRespVO> warderList = roomWarderService.getListByRoomId(orgCode, roomId);
        List<String> sfzhList = warderList.stream().map(warder -> warder.getPoliceSfzh()).collect(Collectors.toList());
        List<UacUserRespVO> userList = UacUserUtil.getUserBySfzhs(sfzhList);
        warderList.forEach(warder -> {
            for (UacUserRespVO user : userList) {
                if (warder.getPoliceSfzh().equals(user.getIdCard())) {
                    warder.setSex(user.getSex());
                }
            }
        });
        // 主管人员
        List<PrisonRoomWarderRespVO> sponsorList = warderList.stream().filter(warder ->
                PoliceWarderUserTypeConstant.MANAGER.equals(warder.getUserType())).collect(Collectors.toList());
        // 协管人员
        List<PrisonRoomWarderRespVO> assistList = warderList.stream().filter(warder ->
                PoliceWarderUserTypeConstant.ASSIST.equals(warder.getUserType())).collect(Collectors.toList());
        RoomPoliceRespVO roomPolice = new RoomPoliceRespVO();
        roomPolice.setAssistList(assistList);
        roomPolice.setSponsorList(sponsorList);
        return success(roomPolice);
    }

    @RequestMapping(value = "/getPrisonerIn", method = RequestMethod.GET)
    @ApiOperation(value = "获取在押人员数量")
    public CommonResult<JSONObject> getPrisonerIn(@RequestParam("orgCode") String orgCode,
                                                  @RequestParam("roomId") String roomId) {
        List<PrisonerInDO> prisonerList = prisonerInDao.getByJsh(orgCode, roomId);
        List<PrisonerInDO> sickList = prisonerInDao.getSickByRoom(orgCode, roomId);
        // 高风险人数
        long riskNum = prisonerList.stream().filter(prisoner ->
                !StringUtil.isNullBlank(prisoner.getFxdj()) && "1".equals(prisoner.getFxdj())).count();
        // 今日入所人数
        long toDayIn = prisonerList.stream().filter(prisoner -> DateUtil.isSameDay(new Date(), prisoner.getRssj())).count();

        JSONObject data = new JSONObject();
        data.put("count", prisonerList.size());
        data.put("sickNum", sickList.size());
        data.put("riskNum", riskNum);
        data.put("toDayInNum", toDayIn);
        return success(data);
    }

    @RequestMapping(value = "/getPrisonerOut", method = RequestMethod.GET)
    @ApiOperation(value = "获取外出人员数量")
    public CommonResult<JSONObject> getPrisonerOut(@RequestParam("orgCode") String orgCode,
                                                  @RequestParam("roomId") String roomId) {
        List<Map<String, Object>> prisonerList = prisonerInDao.getOutPrisonerByRoom(orgCode, roomId);

        // 出所就医人员数量
        long treatmentNum = prisonerList.stream().filter(prisoner -> "treatment".equals(prisoner.get("type"))).count();
        // 提讯人员数量
        long escortNum = prisonerList.stream().filter(prisoner -> "escort".equals(prisoner.get("type"))).count();
        // 提解人员数量
        long arraignmentNum = prisonerList.stream().filter(prisoner -> "arraignment".equals(prisoner.get("type"))).count();
        // 律师会见人员数量
        long lawyerNum = prisonerList.stream().filter(prisoner -> "lawyer".equals(prisoner.get("type"))).count();
        // 家属会见人员数量
        long familyNum = prisonerList.stream().filter(prisoner -> "family".equals(prisoner.get("type"))).count();
        // 其他人员数量
        long restNum = prisonerList.size()-treatmentNum-escortNum-arraignmentNum-lawyerNum-familyNum;


        JSONObject data = new JSONObject();
        data.put("count", prisonerList.size());
        data.put("treatmentNum", treatmentNum);
        data.put("escortNum", escortNum);
        data.put("arraignmentNum", arraignmentNum);
        data.put("lawyerNum", lawyerNum);
        data.put("familyNum", familyNum);
        data.put("restNum", restNum);
        return success(data);
    }

    @RequestMapping(value = "/getDuty", method = RequestMethod.GET)
    @ApiOperation(value = "获取今日值班信息")
    public CommonResult<List<AppDutyShiftRespVO>> getDuty(@RequestParam("orgCode") String orgCode,
                                                   @RequestParam("roomId") String roomId) {
        List<AppDutyShiftRespVO> appDutyShiftRespVOS = dutyService.dutyRecordsByDate(orgCode, roomId, new Date());
        System.out.println(appDutyShiftRespVOS);
        return success(appDutyShiftRespVOS);
    }

    @RequestMapping(value = "/getDayDuty", method = RequestMethod.GET)
    @ApiOperation(value = "获取今日值日信息")
    public CommonResult<List<AppDutyShiftRespVO>> getDayDuty(@RequestParam("orgCode") String orgCode,
                                                       @RequestParam("roomId") String roomId) {
        List<AppDutyShiftRespVO> appDutyShiftRespVOS = dayDutyService.dutyRecordsByDate(orgCode, roomId, new Date());
        return success(appDutyShiftRespVOS);
    }

    @RequestMapping(value = "/getPrisoner", method = RequestMethod.GET)
    @ApiOperation(value = "获取监室人员")
    public CommonResult<List<PrisonerAppRespVO>> getPrisoner(@RequestParam("orgCode") String orgCode,
                                                             @RequestParam("roomId") String roomId) {
        List<PrisonerInDO> prisonerList = prisonerInDao.getByJsh(orgCode, roomId);
        List<PrisonerAppRespVO> prisonerRespList = BeanUtils.toBean(prisonerList, PrisonerAppRespVO.class);
        List<String> jgrybmList = prisonerRespList.stream().map(prisoner -> prisoner.getJgrybm()).collect(Collectors.toList());
        List<PrisonerTagDO> tagDOList = gjLargeScreenDao.getTagListByJgrybmList(jgrybmList);
        for (PrisonerAppRespVO prisoner : prisonerRespList) {
            if (ObjectUtil.isNotEmpty(prisoner.getCsrq()))
                prisoner.setAge(DateUtil.ageOfNow(DateUtil.format(prisoner.getCsrq(), "yyyy-MM-dd")));
            List<String> tagList = new ArrayList<>();
            for (PrisonerTagDO tag : tagDOList) {
                if (tag.getJgrybm().equals(prisoner.getJgrybm())) {
                    tagList.add(tag.getTagName());
                }
            }
            prisoner.setTags(tagList);
        }
        return success(prisonerRespList);
    }



}
