package com.rs.module.pam.service.cook;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.cook.vo.TemplateListReqVO;
import com.rs.module.pam.controller.admin.cook.vo.TemplateRespVO;
import com.rs.module.pam.controller.admin.cook.vo.TemplateSaveReqVO;
import com.rs.module.pam.entity.cook.TemplateDO;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 监所事务管理-菜品模板 Service 接口
 *
 * <AUTHOR>
 */
public interface TemplateService extends IBaseService<TemplateDO> {

    /**
     * 创建监所事务管理-菜品模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createTemplate(TemplateSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-菜品模板
     *
     * @param updateReqVO 更新信息
     */
    void updateTemplate(TemplateSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-菜品模板
     *
     * @param id 编号
     */
    void deleteTemplate(String id);

    /**
     * 获得监所事务管理-菜品模板
     *
     * @param id 编号
     * @return 监所事务管理-菜品模板
     */
    TemplateRespVO getTemplate(String id);

    /**
     * 获得监所事务管理-菜品模板列表
     *
     * @param listReqVO 查询条件
     * @return 监所事务管理-菜品模板列表
     */
    List<TemplateDO> getTemplateList(TemplateListReqVO listReqVO);

    /**
     * 获得监所事务管理-菜品模板DO
     *
     * @param id
     * @return
     */
    TemplateDO getTemplateDO(String id);

    /**
     * 复用模板
     *
     * @param weekNo
     * @param weekDate
     * @param tempId
     */
    void multiplexing(String weekNo, String weekDate, String tempId);

    /**
     * 获得菜谱首页列表
     *
     * @param orgCode
     * @return
     */
    Map<String, Object> getIndexList(String orgCode);

    /**
     * 内屏获取一周菜谱
     * @param orgCode
     * @return
     */
    TemplateRespVO getAppTemplate(String orgCode);

    /**
     * 获得指定日期的菜谱内容
     * @param date
     * @param orgCode
     * @param mealPeriod 早餐/午餐/晚餐
     * @return
     */
    String getMealContent(LocalDate date, String orgCode, String mealPeriod);
}
