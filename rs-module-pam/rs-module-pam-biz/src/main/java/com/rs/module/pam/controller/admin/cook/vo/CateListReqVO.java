package com.rs.module.pam.controller.admin.cook.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 监所事务管理-菜品分类列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CateListReqVO extends BaseVO {

    @ApiModelProperty("菜品分类名称")
    private String cateName;
    @ApiModelProperty("监所编号")
    private String orgCode;

}
