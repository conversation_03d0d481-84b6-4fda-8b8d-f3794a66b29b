package com.rs.module.pam.dao.duty.day;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.entity.duty.day.DayDutyGroupDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 监所事务管理-值日组 Dao
*
* <AUTHOR>
*/
@Mapper
public interface DayDutyGroupDao extends IBaseDao<DayDutyGroupDO> {

    default List<DayDutyGroupDO> selectList(String orgCode, String roomId, List<String> ids) {
        return selectList(new LambdaQueryWrapperX<DayDutyGroupDO>()
                .eq(DayDutyGroupDO::getOrgCode, orgCode)
                .eq(DayDutyGroupDO::getRoomId, roomId)
                .in(DayDutyGroupDO::getId, ids));
    }

    default List<DayDutyGroupDO> selectList(String orgCode, String roomId) {
        return selectList(new LambdaQueryWrapperX<DayDutyGroupDO>()
                .eq(DayDutyGroupDO::getOrgCode, orgCode)
                .eq(DayDutyGroupDO::getRoomId, roomId));
    }

    default List<DayDutyGroupDO> getByGroupName(String orgCode, String roomId, String groupName) {
        return selectList(new LambdaQueryWrapperX<DayDutyGroupDO>()
                .eq(DayDutyGroupDO::getOrgCode, orgCode)
                .eq(DayDutyGroupDO::getRoomId, roomId)
                .eq(DayDutyGroupDO::getGroupName, groupName));
    }

    Integer getRoomDutyGroupNo(@Param("orgCode") String orgCode, @Param("roomId") String roomId);


}
