package com.rs.module.pam.controller.datasource;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.pool.DruidDataSourceFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.dto.OpsDatasourceDTO;
import org.apache.ibatis.datasource.DataSourceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;


public class DataSourceSimpleUtil {


	private static MultiDataSourceManager dataSourceManager = null;
//	private static DataSource dataSource = null;

	private static Logger logger = LoggerFactory.getLogger(DataSourceSimpleUtil.class);

	/**O
	 *
	 */
	static{
		dataSourceManager = ServiceLocator.getBean(MultiDataSourceManager.class);
//		dataSource = ServiceLocator.getBean(DataSource.class);
	}

	/**
	 * 获得数据源
	 * @param db 数据源实体表
	 * @return
	 * @throws Exception
	 * date 2020年4月27日
	 */
	public static DataSource getDataSource(OpsDatasourceDTO db) throws Exception {
		return getDataSource(db,null);
	}


	public static DataSource getDataSource(String dbId) throws Exception {
		//获得所有数据源
		Map<Object, Object>  dataSources = dataSourceManager.getDataSources();
		//返回数据源
		if(dataSources.containsKey(dbId)){
			//从管理工具类中获取datasource
			logger.info("直接从数据源MultiDataSourceManager管理工具中取出数据源。");
			return (DataSource) dataSources.get(dbId);
		} else {
			BspApi resDbService = ServiceLocator.getBean(BspApi.class);
			OpsDatasourceDTO resDb = resDbService.getDbById(dbId);
			if(null == resDb) {
				return null;
			}
			return getDataSource(resDb);
		}
	}

	/**
	 * 获得数据源
	 * @param db 数据源实体表
	 * @param connPros 数据库连接属性
	 * @return
	 * @throws Exception
	  * date 2020年4月27日
	 */
	private static DataSource getDataSource(OpsDatasourceDTO db, Map<String, String> connPros) throws Exception {
		DataSource dataSource = null;

		//获得所有数据源
		Map<Object, Object>  dataSources = dataSourceManager.getDataSources();
		//返回数据源
		if(dataSources.containsKey(db.getId())){
			//从管理工具类中获取datasource
			dataSource = (DataSource) dataSources.get(db.getId());
			logger.info("直接从数据源MultiDataSourceManager管理工具中取出数据源。");
			return dataSource;
		}

		synchronized (DataSourceSimpleUtil.class){
			dataSource = DruidDataSourceFactory.createDataSource(getDbPros(db));
			if(null != connPros){
				for(Entry<String, String> entry : connPros.entrySet()){
					((DruidDataSource) dataSource).addConnectionProperty(entry.getKey(), entry.getValue());
				}
			}
			((DruidDataSource) dataSource).setBreakAfterAcquireFailure(true);
			if(null != dataSource && isDataSourceSuccess(dataSource)){
				//将DataSource放入管理工具类中管理
				dataSourceManager.addDataSource(db.getId(), dataSource);
				logger.info("创建新数据连接源，并放入MultiDataSourceManager中。");
				return dataSource;
			}
		}

		return null;
	}

	private static boolean isDataSourceSuccess(DataSource dataSource) {

		Connection conn = null;
		try {
			conn = dataSource.getConnection();
			if(conn != null) {
				return true;
			}
		} catch (SQLException e) {
			logger.error("获取数据源失败!", e);
			throw new DataSourceException("获取数据源失败!", e);
		} finally {
			if (conn != null) {
				try {
					conn.close();
				} catch (SQLException e) {
				}
			}
		}
		return false;
	}

	/**
	 * 根据key移除DataSource
	 * @param key 数据源key值
	 * <AUTHOR>
	 * date 2020年4月27日
	 */
	public static void removeDataSource(String key){
		try {
			dataSourceManager.removeDataSource(key);
		} catch (Exception e) {
			logger.error("删除数据源出错!", e);
			// e.printStackTrace();
		}
	}

	/**
	 * 批量移除数据源
	 * @param keys key数组
	 * <AUTHOR>
	 * @date 2016-5-9
	 */
//	public static void removeDataSources(String[] keys){
//
//		if(keys != null && keys.length > 0){
//			for(String key : keys){
//				removeDataSource(key);
//			}
//		}
//	}

	/**
	 * 根据dbId获取数据库连接
	 * @param dbId
	 * @return
	 * <AUTHOR>
	 * date 2020年4月27日
	 */
//	public static Connection getConnection(String dbId){
//		try {
//			DataSource dataSource = getDataSource(dbId);
//			if(null != dataSource) {
//				return dataSource.getConnection();
//			}
//		} catch (Exception e) {
//			// e.printStackTrace();
//			logger.error("获取数据源失败!", e);
//		}
//		return null;
//	}

	/**
	 * 根据dbId获取数据库连接
	 * @param db
	 * @return
	 * <AUTHOR>
	 * date 2020年4月27日
	 */
//	public static Connection getConnection(OpsDatasource db){
//		try {
//			DataSource dataSource = getDataSource(db);
//			if(null != dataSource) {
//				return dataSource.getConnection();
//			}
//		} catch (Exception e) {
//			// e.printStackTrace();
//			logger.error("获取数据源失败!", e);
//		}
//		return null;
//	}

	/**
	 * 获得数据库配置属性信息
	 * @param db 数据源配置
	 * @return  Properties
	 * <AUTHOR>
	 * @date 2016-5-9
	 */
	private static Properties getDbPros(OpsDatasourceDTO db) throws DataSourceException{

		Properties pros = new Properties();

		//数据库驱动
		pros.put("driverClassName", db.getDriverClass());
		pros.put("url", db.getDbUrl());
		pros.put("username", db.getDbUser());
		pros.put("password", db.getDbPass());
		pros.put("maxWait", "5000");
		if(StringUtils.hasLength(db.getDbAttr())){
			JSONObject dbAttr = JSON.parseObject(db.getDbAttr());
			 for(Entry<String, Object> entry : dbAttr.entrySet()) {
				 pros.put(entry.getKey(), String.valueOf(entry.getValue()));
			 }
		}
		return pros;
	}

	/**
	 * 自动推断数据源
	 * @param dbId
	 * @return
	 * @throws Exception
	 * <AUTHOR>
	 * @date 2021/03/31
	 */
//	public static  DataSource deduceDataSource(String dbId) throws Exception {
//		DataSource ds = null;
//
//		if(dbId.startsWith(CommonConstants.BASIC_BUS_DB_PREFIX)) {
//			if(dataSource instanceof DynamicRoutingDataSource) {
//				String dbName = dbId.substring(CommonConstants.BASIC_BUS_DB_PREFIX.length());
//				DynamicRoutingDataSource drds = (DynamicRoutingDataSource) dataSource;
//				// 切换数据源
//				ds =  drds.getDataSource(dbName);
//			} else {
//				ds =  dataSource;
//			}
//		} else {
//			ds = DataSourceSimpleUtil.getDataSource(dbId);
//		}
//		return ds;
//	}

}
