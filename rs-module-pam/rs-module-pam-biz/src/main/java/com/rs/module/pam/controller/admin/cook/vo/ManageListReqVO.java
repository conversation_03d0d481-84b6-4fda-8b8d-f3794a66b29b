package com.rs.module.pam.controller.admin.cook.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 监所事务管理-菜品管理列表 Request VO")
@Data
public class ManageListReqVO {
    @ApiModelProperty("菜品名称")
    private String cookName;

    @ApiModelProperty("菜品分类ID")
    private String cateId;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("监所编号")
    private String orgCode;

}
