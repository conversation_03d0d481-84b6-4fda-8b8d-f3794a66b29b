package com.rs.module.pam.controller.admin.family;

import com.rs.framework.common.enums.DataSourceAppEnum;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.pam.controller.admin.family.vo.*;
import com.rs.module.pam.entity.family.FamilyPhoneDO;
import com.rs.module.pam.service.family.FamilyPhoneService;

@Api(tags = "监所事务管理-亲情电话")
@RestController
@RequestMapping("/pam/family/familyPhone")
@Validated
public class FamilyPhoneController {

    @Resource
    private FamilyPhoneService familyPhoneService;

    @PostMapping("/create")
    @ApiOperation(value = "创建监所事务管理-亲情电话(实战平台)")
    public CommonResult<String> createFamilyPhone(@Valid @RequestBody FamilyPhoneSaveReqVO createReqVO) {
        createReqVO.setDataSources(DataSourceAppEnum.ACP.getCode());
        return success(familyPhoneService.createFamilyPhone(createReqVO));
    }

    @PostMapping("/approval")
    @ApiOperation(value = "审批监所事务管理-亲情电话")
    public CommonResult<Boolean> approvalFamilyPhone(@Valid @RequestBody FamilyPhoneApprovalReqVO approvalReqVO) {
        familyPhoneService.approvalFamilyPhone(approvalReqVO);
        return success(true);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新监所事务管理-亲情电话")
    public CommonResult<Boolean> updateFamilyPhone(@Valid @RequestBody FamilyPhoneSaveReqVO updateReqVO) {
        familyPhoneService.updateFamilyPhone(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除监所事务管理-亲情电话")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteFamilyPhone(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            familyPhoneService.deleteFamilyPhone(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得监所事务管理-亲情电话")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<FamilyPhoneRespVO> getFamilyPhone(@RequestParam("id") String id) {
        FamilyPhoneDO familyPhone = familyPhoneService.getFamilyPhone(id);
        return success(BeanUtils.toBean(familyPhone, FamilyPhoneRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得监所事务管理-亲情电话分页")
    public CommonResult<PageResult<FamilyPhoneRespVO>> getFamilyPhonePage(@Valid @RequestBody FamilyPhonePageReqVO pageReqVO) {
        PageResult<FamilyPhoneDO> pageResult = familyPhoneService.getFamilyPhonePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, FamilyPhoneRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得监所事务管理-亲情电话列表")
    public CommonResult<List<FamilyPhoneRespVO>> getFamilyPhoneList(@Valid @RequestBody FamilyPhoneListReqVO listReqVO) {
        List<FamilyPhoneDO> list = familyPhoneService.getFamilyPhoneList(listReqVO);
        return success(BeanUtils.toBean(list, FamilyPhoneRespVO.class));
    }

    @GetMapping("/list10")
    @ApiOperation(value = "获得监所事务管理-亲情电话列表最近十条")
    @ApiImplicitParams ({
            @ApiImplicitParam(name = "jgrybm", value = "被监管人员编码"),
            @ApiImplicitParam(name = "id", value = "当前详情信息id, 最近十条不包含此条，可不传")
    })
    public CommonResult<List<FamilyPhoneRespVO>> getFamilyPhoneList10(@RequestParam("jgrybm") String jgrybm,
                                                                      @RequestParam(value = "id", required = false) String id) {
        List<FamilyPhoneDO> list = familyPhoneService.getFamilyPhoneList10(jgrybm, id);
        return success(BeanUtils.toBean(list, FamilyPhoneRespVO.class));
    }
}
