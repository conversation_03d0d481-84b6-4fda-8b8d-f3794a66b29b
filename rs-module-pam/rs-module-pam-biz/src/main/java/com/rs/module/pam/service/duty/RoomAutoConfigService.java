package com.rs.module.pam.service.duty;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.duty.vo.RoomAutoConfigSaveReqVO;
import com.rs.module.pam.entity.duty.RoomAutoConfigDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 监所事务管理-监室自动排班配置 Service 接口
 *
 * <AUTHOR>
 */
public interface RoomAutoConfigService extends IBaseService<RoomAutoConfigDO>{

    /**
     * 创建监所事务管理-监室自动排班配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createRoomAutoConfig(@Valid RoomAutoConfigSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-监室自动排班配置
     *
     * @param updateReqVO 更新信息
     */
    void updateRoomAutoConfig(@Valid RoomAutoConfigSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-监室自动排班配置
     *
     * @param id 编号
     */
    void deleteRoomAutoConfig(String id);

    /**
     * 获得监所事务管理-监室自动排班配置
     *
     * @param orgCode 机构编号
     * @return 监室自动排班配置
     */
    RoomAutoConfigDO getRoomAutoConfig(String orgCode, String roomId);



}
