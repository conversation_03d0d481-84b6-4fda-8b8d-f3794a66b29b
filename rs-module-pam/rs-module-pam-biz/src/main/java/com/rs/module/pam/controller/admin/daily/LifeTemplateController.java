package com.rs.module.pam.controller.admin.daily;

import com.bsp.security.util.SessionUserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.pam.controller.admin.daily.vo.*;
import com.rs.module.pam.entity.daily.LifeTemplateDO;
import com.rs.module.pam.entity.daily.LifeTemplateEventDO;
import com.rs.module.pam.service.daily.LifeTemplateService;

@Api(tags = "监所事务管理-一日生活制度模板")
@RestController
@RequestMapping("/pam/daily/lifeTemplate")
@Validated
public class LifeTemplateController {

    @Resource
    private LifeTemplateService lifeTemplateService;

    @PostMapping("/create")
    @ApiOperation(value = "创建监所事务管理-一日生活制度模板")
    public CommonResult<String> createLifeTemplate(@Valid @RequestBody LifeTemplateSaveReqVO createReqVO) {
        return success(lifeTemplateService.createLifeTemplate(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新监所事务管理-一日生活制度模板")
    public CommonResult<Boolean> updateLifeTemplate(@Valid @RequestBody LifeTemplateSaveReqVO updateReqVO) {
        lifeTemplateService.updateLifeTemplate(updateReqVO);
        return success(true);
    }

    @PostMapping("/update-one")
    @ApiOperation(value = "更新(单独更新)监所事务管理-一日生活制度模板")
    public CommonResult<Boolean> updateOneLifeTemplate(@Valid @RequestBody LifeTemplateOneSaveReqVO updateReqVO) {
        lifeTemplateService.updateOneLifeTemplate(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除监所事务管理-一日生活制度模板")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteLifeTemplate(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            lifeTemplateService.deleteLifeTemplate(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得监所事务管理-一日生活制度模板")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<LifeTemplateRespVO> getLifeTemplate(@RequestParam("id") String id) {
        LifeTemplateDO lifeTemplate = lifeTemplateService.getLifeTemplate(id);
        return success(BeanUtils.toBean(lifeTemplate, LifeTemplateRespVO.class));
    }

    /*@PostMapping("/page")
    @ApiOperation(value = "获得监所事务管理-一日生活制度模板分页")
    public CommonResult<PageResult<LifeTemplateRespVO>> getLifeTemplatePage(@Valid @RequestBody LifeTemplatePageReqVO pageReqVO) {
        PageResult<LifeTemplateDO> pageResult = lifeTemplateService.getLifeTemplatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LifeTemplateRespVO.class));
    }*/

    @PostMapping("/list")
    @ApiOperation(value = "获得监所事务管理-一日生活制度模板列表")
    public CommonResult<List<LifeTemplateRespVO>> getLifeTemplateList(@Valid @RequestBody LifeTemplateListReqVO listReqVO) {
        if (StringUtils.isEmpty(listReqVO.getOrgCode())) {
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        List<LifeTemplateDO> list = lifeTemplateService.getLifeTemplateList(listReqVO);
        return success(BeanUtils.toBean(list, LifeTemplateRespVO.class));
    }
    // ==================== 子表（监所事务管理-一日生活制度模板关联事务） ====================

    @GetMapping("/life-template-event/list-by-template-id")
    @ApiOperation(value = "获得监所事务管理-一日生活制度模板关联事务列表")
    @ApiImplicitParam(name = "templateId", value = "模板ID")
    public CommonResult<List<LifeTemplateEventDO>> getLifeTemplateEventListByTemplateId(@RequestParam("templateId") String templateId) {
        return success(lifeTemplateService.getLifeTemplateEventListByTemplateId(templateId));
    }

    @PostMapping("/life-template-event-update")
    @ApiOperation(value = "更新监所事务管理-一日生活制度模板事务选项更新")
    public CommonResult<Boolean> updateLifeTemplateEvent(@Valid @RequestBody LifeTemplateEventSaveReqVO updateReqVO) {
        lifeTemplateService.updateLifeTemplateEventById(updateReqVO);
        return success(true);
    }

}
