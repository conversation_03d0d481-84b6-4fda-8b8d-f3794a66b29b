package com.rs.module.pam.entity.bed;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

/**
 * 监所事务管理-监室床位布局 DO
 *
 * <AUTHOR>
 */
@TableName("pam_bed_layout")
@KeySequence("pam_bed_layout_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LayoutDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 布局名称
     */
    private String layoutName;
    /**
     * 布局照片地址
     */
    private String layoutImageUrl;
    /**
     * 布局配置
     */
    private String layoutConfig;
    /**
     * 布局类型(01:底图,02:自定义)
     */
    private String layoutType;
    /**
     * 布局宽度
     */
    private Integer width;
    /**
     * 布局高度
     */
    private Integer height;
    /**
     * 布局行数
     */
    private Integer layoutRow;
    /**
     * 布局列数
     */
    private Integer layoutColumn;
    /**
     * 排序
     */
    private Integer sort;

}
