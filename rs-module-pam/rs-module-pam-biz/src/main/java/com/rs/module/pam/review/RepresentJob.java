package com.rs.module.pam.review;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.StringUtil;
import com.gosun.zhjg.prison.room.terminal.config.socketio.PushMessage;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.CustomPushMessageConditionDTO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.SocketService;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.PushMessageAckVO;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.framework.rpc.core.UserTokenContext;
import com.rs.module.pam.config.AsyncTaskPoolConfig;
import com.rs.module.pam.cons.RoomRepresentConstant;
import com.rs.module.pam.cons.SocketEventConstant;
import com.rs.module.pam.controller.admin.represent.vo.extra.RoomInfoVO;
import com.rs.module.pam.controller.admin.represent.vo.extra.RoomPersonVO;
import com.rs.module.pam.dao.represent.PersonRepresentDao;
import com.rs.module.pam.dao.represent.RepresentDao;
import com.rs.module.pam.entity.represent.PersonRepresentDO;
import com.rs.module.pam.entity.represent.RepresentConfigDO;
import com.rs.module.pam.entity.represent.RepresentDO;
import com.rs.module.pam.service.represent.RepresentConfigService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;

/**
 * 监室点名Job--作废
 *
 */
@Component
@Slf4j
@Deprecated
public class RepresentJob {

    @Resource
    private RepresentConfigService representConfigService;
    @Resource
    private RepresentDao representDao;
    @Resource
    private PersonRepresentDao personRepresentDao;

    @Resource(name = AsyncTaskPoolConfig.TASK_EXECUTOR)
    private Executor executorService;

    @Resource
    private SocketService socketService;
    @Resource
    private BspApi bspApi;

    /**
     * 监室点名计划任务--作废
     */
    @XxlJob("representJob")
    @Transactional
    public void representJob() {
        try {
            JSONObject jobParam = JSONObject.parseObject(XxlJobHelper.getJobParam());
            System.out.println("=========================================");
            System.out.println(jobParam);
//            UserTokenContext.setToken(bspApi.getTempToken());
            Date now = new Date();
            RepresentConfigDO configDO = representConfigService.getById(jobParam.getString("id"));
            String presentNo = bspApi.executeByRuleCode("param_represent_code", null);
            String roomIdList = configDO.getRoomId();
            if(StringUtils.isNotBlank(roomIdList)){
                for(String roomId : roomIdList.split(",")){
                    RepresentDO represent = representDao.getNowRepresentByRoomId(roomId);
                    if(represent == null){
                        RepresentDO representDO = new RepresentDO();
                        RoomInfoVO info = representDao.getRoomInfoByRoomId(roomId);
                        if (info == null) {
                            log.error("点名监室"+roomId+"不存在");
                            continue;
                        }
                        // 检查该监室对应监所是否拥有“监室点名”菜单，如果没有则不执行点名，跳过
//                        boolean has = terminalMenuService.checkPrisonHasMenuPermission(info.getPrisonId(), TerminalMenuCodeConstants.CNP_JSDM);
//                        if (!has) {
//                            // 未拥有权限
//                            log.warn("自动点名  {}监室未配置菜单权限，自定点名跳过", info.getRoomName());
//                            continue;
//                        }
                        Integer sumNum = representDao.getCountInRoom(roomId);
                        Integer outNum = representDao.getNewCountInRoom(roomId);//外出人数

                        log.info("自动点名  {}({})监室关押{}人, 外出{}人", info.getRoomName(), info.getRoomId(), sumNum, outNum);
                        if (sumNum == 0) {
                            log.warn("空监室+退出点名");
                            continue;
                        }
                        representDO.setPresentNo(presentNo);
                        representDO.setAllInNum(sumNum);
                        representDO.setOutNum(outNum);
                        representDO.setInNum(sumNum - outNum);
                        representDO.setId(StringUtil.getGuid32());
                        representDO.setRoomId(roomId);
                        representDO.setRoomName(info.getRoomName());
                        representDO.setOperatePeopleId(configDO.getAddUser());
                        representDO.setOperatePeopleSfzh(configDO.getAddUser());
                        representDO.setOperatePeopleName(configDO.getAddUserName());
                        representDO.setStartTime(now);
                        representDO.setAddTime(now);
                        representDO.setPresentStatus(RoomRepresentConstant.IN_REPRESENT);
                        representDO.setPresentNum(0);
                        representDO.setErrorNum(0);
                        representDO.setOperateTime(now);
                        representDO.setPresentType(1);
                        representDO.setPrisonId(info.getPrisonId());
                        representDO.setOrgCode(info.getOrgCode());
                        representDO.setExpiryDate(configDO.getExpiryDate());
                        representDO.setEndTime(DateUtil.offsetMinute(new Date(), configDO.getExpiryDate()));
                        representDO.setAddUser(configDO.getAddUser());
                        representDO.setAddUserName(configDO.getAddUserName());
                        representDO.setOrgName(info.getOrgName());

//                        JSONObject message = new JSONObject();
//                        message.put("content", JSONObject.toJSONString(representDO));
                        PushMessage message = new PushMessage();
                        message.setContent(JSONObject.toJSONString(representDO));
                        List<String> roomIds = new ArrayList<>();
                        roomIds.add(roomId);
                        List<String> serialNumbers = socketService.getCnpSerialNumberByRoomId(roomIds);
//                        List<String> serialNumbers = Arrays.asList("2030006012504079");
                        List<String> sessionIds = socketService.getSessionBySerialNumber(serialNumbers);
                        List<String> list = representDao.checkIsNowRepresent(roomId);
                        if(list.isEmpty()){
                            int insert = representDao.insert(representDO);
                            log.info(representDO.getRoomId() + "后台自动点名监室插入成功" + insert);
                        }else {
                            log.info(representDO.getRoomId() + "后台自动点名监室已经插入成功，不要重复插入");
                        }
//                        log.info("自动点名+停止点名任务开始");
//                        scheduledService.doEndTask(representDO);
//                        log.info("自动点名+停止点名任务结束");
                        //插入人员签到信息
                        List<RoomPersonVO> personVos = representDao.getPersonInRoom(roomId);
                        for (RoomPersonVO personVo : personVos) {
                            PersonRepresentDO personEntity = new PersonRepresentDO();
                            personEntity.setAddTime(now);
                            personEntity.setAddUser(configDO.getAddUser());
                            personEntity.setAddUserName(configDO.getAddUserName());
                            personEntity.setOrgCode(info.getOrgCode());
                            personEntity.setOrgName(info.getOrgName());
                            personEntity.setPrisonerId(personVo.getPrisonerId());
                            personEntity.setJgrybm(personVo.getJgrybm());
                            personEntity.setJgryxm(personVo.getPrisonerName());
                            personEntity.setRoomPresentId(representDO.getId());
                            personEntity.setSignStatus(RoomRepresentConstant.PERSON_REPRESENT_WAIT);
                            if ("1".equals(personVo.getReport())) {
                                personEntity.setIsOut("1");
                                personEntity.setIsReport(1);
                                personEntity.setOutReason("已报备，不参与点名");
                            } else if ("1".equals(personVo.getOutArraignment())) {
                                personEntity.setIsOut("1");
                                personEntity.setOutReason("提讯");
                            } else if ("1".equals(personVo.getOutBringInterrogation())) {
                                personEntity.setIsOut("1");
                                personEntity.setOutReason("提询");
                            } else if ("1".equals(personVo.getOutConsularMeeting())) {
                                personEntity.setIsOut("1");
                                personEntity.setOutReason("领事会见");
                            } else if ("1".equals(personVo.getOutLawyerMeeting())) {
                                personEntity.setIsOut("1");
                                personEntity.setOutReason("律师会见");
                            } else if ("1".equals(personVo.getOutFamilyMeeting())) {
                                personEntity.setIsOut("1");
                                personEntity.setOutReason("家属会见");
                            } else if ("1".equals(personVo.getOutEscort())) {
                                personEntity.setIsOut("1");
                                personEntity.setOutReason("提解");
                            } else if ("1".equals(personVo.getOutPrisonTreatment())) {
                                personEntity.setIsOut("1");
                                personEntity.setOutReason("出所就医");
                            }
                            int insert = personRepresentDao.insert(personEntity);
                            log.info("后台插入"+info.getRoomName()+personVo.getPrisonerName()+"人员签到信息"+insert);
                        }

                        CustomPushMessageConditionDTO condition = new CustomPushMessageConditionDTO();
                        condition.setSessionIds(sessionIds);
                        condition.setAction(SocketEventConstant.REPRESENT_START);
                        condition.setMessage(message);
                        executorService.execute(() -> {
                            System.out.println("仓内屏连接参数：" + condition);
                            System.out.println("sessionIds：" + sessionIds);
                            Map<String, PushMessageAckVO> pushMessageAckMap =
                                    socketService.pushMessageCustomWithConditionWaitReplyMap(condition);
                            for (String sessionId : sessionIds) {
                                PushMessageAckVO pushMessageAckVO = pushMessageAckMap.get(sessionId);
                                if (pushMessageAckVO != null && pushMessageAckVO.getOk()) {
                                    log.info("自动点名开始成功");
                                } else {
                                    log.error("仓内屏连接失败：{}", JSON.toJSONString(pushMessageAckVO));
                                }
                            }
                        });
                    }else {
                        log.info("{}仓内屏已经调用入库数据接口存入数据", roomId);
                    }
                }
            }
            UserTokenContext.remove();
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.log("任务执行失败:" + e.getMessage());
        }
    }

}
