package com.rs.module.pam.service.integral;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.cons.CommonConstants;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.integral.vo.ModelConfigRespVO;
import com.rs.module.pam.controller.admin.integral.vo.ModelConfigSaveReqVO;
import com.rs.module.pam.dao.integral.ModelConfigDao;
import com.rs.module.pam.entity.integral.ModelConfigDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;


/**
 * 监所事务管理-积分模型配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ModelConfigServiceImpl extends BaseServiceImpl<ModelConfigDao, ModelConfigDO> implements ModelConfigService {

    @Resource
    private ModelConfigDao modelConfigDao;

    @Override
    public ModelConfigRespVO createModelConfig(ModelConfigSaveReqVO createReqVO) {
        // 插入
        ModelConfigDO modelConfig = BeanUtils.toBean(createReqVO, ModelConfigDO.class);
        if (StringUtil.isNullBlank(modelConfig.getIncValue())) {
            modelConfig.setIncValue(modelConfig.getIncValueDefault());
        }
        modelConfigDao.insert(modelConfig);
        // 返回
        return BeanUtils.toBean(modelConfig, ModelConfigRespVO.class);
    }

    @Override
    public ModelConfigRespVO updateModelConfig(ModelConfigSaveReqVO updateReqVO) {
        // 校验存在
        validateModelConfigExists(updateReqVO.getId());
        // 更新
        ModelConfigDO updateObj = BeanUtils.toBean(updateReqVO, ModelConfigDO.class);
        modelConfigDao.updateById(updateObj);
        // 返回
        return BeanUtils.toBean(updateObj, ModelConfigRespVO.class);
    }

    @Override
    public void deleteModelConfig(String id) {
        // 校验存在
        validateModelConfigExists(id);
        // 删除
        modelConfigDao.deleteById(id);
    }

    private void validateModelConfigExists(String id) {
        if (modelConfigDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-积分模型配置数据不存在");
        }
    }

    @Override
    public ModelConfigDO getModelConfig(String id) {
        return modelConfigDao.selectById(id);
    }

    @Override
    public List<ModelConfigDO> getAll() {
        return list(new LambdaQueryWrapper<ModelConfigDO>()
                .eq(ModelConfigDO::getStatus, CommonConstants.CONSTANTS_TRUE));
    }

}
