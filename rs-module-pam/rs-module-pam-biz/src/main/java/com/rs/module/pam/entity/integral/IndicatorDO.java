package com.rs.module.pam.entity.integral;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 监所事务管理-积分指标 DO
 *
 * <AUTHOR>
 */
@TableName("pam_integral_indicator")
@KeySequence("pam_integral_indicator_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_integral_indicator")
public class IndicatorDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 风险模型id
     */
    private String riskModelId;
    /**
     * 父类id
     */
    private String parentId;
    /**
     * 指标名称
     */
    private String name;
    /**
     * 指标权重
     */
    private BigDecimal weight;
    /**
     * 排序
     */
    private Integer orderId;

}
