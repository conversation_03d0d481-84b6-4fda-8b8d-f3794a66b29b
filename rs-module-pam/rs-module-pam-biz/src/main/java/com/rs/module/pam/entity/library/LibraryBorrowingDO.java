package com.rs.module.pam.entity.library;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-图书借阅申请 DO
 *
 * <AUTHOR>
 */
@TableName("pam_library_borrowing")
@KeySequence("pam_library_borrowing_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LibraryBorrowingDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String ryxm;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 图书id
     */
    private String libraryId;
    /**
     * 申请时间
     */
    private Date applyTime;
    /**
     * 借阅截止时间
     */
    private Date borrowingDeadlineTime;
    /**
     * 发放时间
     */
    private Date distributionTime;
    /**
     * 归还时间
     */
    private Date returnTime;
    /**
     * 借阅状态（字典：ZD_TSJY_JYZT）
     */
    private String status;
    /**
     * 审批人身份证号
     */
    private String approverSfzh;
    /**
     * 审批人姓名
     */
    private String approverXm;
    /**
     * 审批时间
     */
    private Date approverTime;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 审核意见
     */
    private String approvalComments;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

    /**
     *  图书名称
     */
    @TableField(exist = false)
    private String libraryName;

}
