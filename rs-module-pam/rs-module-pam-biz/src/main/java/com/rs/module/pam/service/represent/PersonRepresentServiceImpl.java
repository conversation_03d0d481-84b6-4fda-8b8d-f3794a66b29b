package com.rs.module.pam.service.represent;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.represent.vo.PersonRepresentListReqVO;
import com.rs.module.pam.controller.admin.represent.vo.PersonRepresentPageReqVO;
import com.rs.module.pam.controller.admin.represent.vo.PersonRepresentSaveReqVO;
import com.rs.module.pam.dao.represent.PersonRepresentDao;
import com.rs.module.pam.entity.represent.PersonRepresentDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 监所事务管理-人员点名记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PersonRepresentServiceImpl extends BaseServiceImpl<PersonRepresentDao, PersonRepresentDO> implements PersonRepresentService {

    @Resource
    private PersonRepresentDao personRepresentDao;

    @Override
    public String createPersonRepresent(PersonRepresentSaveReqVO createReqVO) {
        // 插入
        PersonRepresentDO represent = BeanUtils.toBean(createReqVO, PersonRepresentDO.class);
        personRepresentDao.insert(represent);
        // 返回
        return represent.getId();
    }

    @Override
    public void updatePersonRepresent(PersonRepresentSaveReqVO updateReqVO) {
        // 校验存在
        validatePersonRepresentExists(updateReqVO.getId());
        // 更新
        PersonRepresentDO updateObj = BeanUtils.toBean(updateReqVO, PersonRepresentDO.class);
        personRepresentDao.updateById(updateObj);
    }

    @Override
    public void deletePersonRepresent(String id) {
        // 校验存在
        validatePersonRepresentExists(id);
        // 删除
        personRepresentDao.deleteById(id);
    }

    private void validatePersonRepresentExists(String id) {
        if (personRepresentDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-人员点名记录数据不存在");
        }
    }

    @Override
    public PersonRepresentDO getPersonRepresent(String id) {
        return personRepresentDao.selectById(id);
    }

    @Override
    public PageResult<PersonRepresentDO> getPersonRepresentPage(PersonRepresentPageReqVO pageReqVO) {
        return personRepresentDao.selectPage(pageReqVO);
    }

    @Override
    public List<PersonRepresentDO> getPersonRepresentList(PersonRepresentListReqVO listReqVO) {
        return personRepresentDao.selectList(listReqVO);
    }


}
