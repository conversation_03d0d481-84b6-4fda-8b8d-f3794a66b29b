package com.rs.module.pam.controller.admin.duty;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.pam.controller.admin.duty.vo.GroupSaveReqVO;
import com.rs.module.pam.service.duty.GroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "值班管理-值班组")
@RestController
@RequestMapping("/pam/duty/group")
@Validated
public class GroupController {

    @Resource
    private GroupService groupService;

    @PostMapping("/create")
    @ApiOperation(value = "创建监所事务管理-值班组")
    @BusTrace(busType = BusTypeEnum.YEWU_JSZB, condition = "false", content = "{\"监室编号\":\"{{#createReqVO.roomId}}\"," +
            "\"监管人员1\":\"{{#createReqVO.jgryxm1}}\",\"监管人员2\":\"{{#createReqVO.jgryxm2}}\"}")
    public CommonResult<String> createGroup(@Valid @RequestBody GroupSaveReqVO createReqVO) {
        return success(groupService.createGroup(createReqVO));
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除监所事务管理-值班组")
    @ApiImplicitParam(name = "ids", value = "编号")
    @BusTrace(busType = BusTypeEnum.YEWU_JSZB, condition = "false", content = "{\"值班组id\":\"{{#ids}}\"}")
    public CommonResult<Boolean> deleteGroup(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           groupService.deleteGroup(id);
        }
        return success(true);
    }

}
