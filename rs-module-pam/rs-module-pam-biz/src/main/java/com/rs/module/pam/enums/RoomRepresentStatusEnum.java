package com.rs.module.pam.enums;

import lombok.Getter;

/**
 * 监室点名状态枚举
*/
@Getter
public enum RoomRepresentStatusEnum {
    ing("进行中","0"),
    err("异常","2"),
    ok("正常","1"),
    not("未开始","3");


    private String key;

    private String value;

    RoomRepresentStatusEnum(String value, String key) {
        this.key = key;
        this.value = value;
    }

    // 普通方法
    public static String getValue(String index) {
        for (RoomRepresentStatusEnum c : RoomRepresentStatusEnum.values()) {
            if (index.contains(c.getKey())) {
                return c.value;
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
