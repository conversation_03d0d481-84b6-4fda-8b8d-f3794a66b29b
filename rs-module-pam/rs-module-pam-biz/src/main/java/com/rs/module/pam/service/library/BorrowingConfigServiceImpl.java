package com.rs.module.pam.service.library;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.gosun.zhjg.common.util.StringUtil;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.framework.mybatis.util.BspDbUtil;
import com.rs.module.pam.cons.CommonConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.pam.controller.admin.library.vo.*;
import com.rs.module.pam.entity.library.BorrowingConfigDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.pam.dao.library.BorrowingConfigDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 监所事务管理-图书借阅配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BorrowingConfigServiceImpl extends BaseServiceImpl<BorrowingConfigDao, BorrowingConfigDO> implements BorrowingConfigService {

    @Resource
    private BorrowingConfigDao borrowingConfigDao;

    @Override
    public String createBorrowingConfig(BorrowingConfigSaveReqVO createReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        List<BorrowingConfigDO> borrowingConfigDOS = borrowingConfigDao.selectList(BorrowingConfigDO::getOrgCode, sessionUser.getOrgCode());
        if (CollectionUtil.isEmpty(borrowingConfigDOS)) {
            // 插入
            BorrowingConfigDO borrowingConfig = BeanUtils.toBean(createReqVO, BorrowingConfigDO.class);
            borrowingConfigDao.insert(borrowingConfig);
            // 返回
            return borrowingConfig.getId();
        }
        throw new ServerException("该监所存在借阅配置，无需重复创建");
    }

    @Override
    public void createBorrowingDefaultConfigByMgt() {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        List<BorrowingConfigDO> borrowingConfigDOS = borrowingConfigDao.selectList(BorrowingConfigDO::getOrgCode, sessionUser.getOrgCode());
        if (CollectionUtil.isEmpty(borrowingConfigDOS)) {
            BorrowingConfigSaveReqVO createReqVO = new BorrowingConfigSaveReqVO();
            // 若全局没配置，则给默认值
            int zdjyDuration = 1000, zdjyLimit = 1000;
            try {
                zdjyDuration = Integer.parseInt(BspDbUtil.getParam("ZDJY_DURATION"));
                zdjyLimit = Integer.parseInt(BspDbUtil.getParam("ZDJY_LIMIT"));
            } catch (Exception e) {
                log.error(e.getMessage());
            }
            //个人最大借阅时长（天）
            createReqVO.setMaximumBorrowingDuration(zdjyDuration);
            // 个人最大借阅数
            createReqVO.setMaximumBorrowingLimit(zdjyLimit);
            // 插入
            BorrowingConfigDO borrowingConfig = BeanUtils.toBean(createReqVO, BorrowingConfigDO.class);
            borrowingConfigDao.insert(borrowingConfig);
        }
    }

    @Override
    public void updateBorrowingConfig(BorrowingConfigSaveReqVO updateReqVO) {
        // 校验存在
        validateBorrowingConfigExists(updateReqVO.getId());
        // 更新
        BorrowingConfigDO updateObj = BeanUtils.toBean(updateReqVO, BorrowingConfigDO.class);
        borrowingConfigDao.updateById(updateObj);
    }

    @Override
    public void deleteBorrowingConfig(String id) {
        // 校验存在
        validateBorrowingConfigExists(id);
        // 删除
        borrowingConfigDao.deleteById(id);
    }

    private void validateBorrowingConfigExists(String id) {
        if (StringUtils.isEmpty(id)) {
            throw new ServerException("监所事务管理-图书借阅配置id不能为空");
        }
        if (borrowingConfigDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-图书借阅配置数据不存在");
        }
    }

    @Override
    public BorrowingConfigDO getBorrowingConfig(String id) {
        return borrowingConfigDao.selectById(id);
    }

    @Override
    public PageResult<BorrowingConfigDO> getBorrowingConfigPage(BorrowingConfigPageReqVO pageReqVO) {
        return borrowingConfigDao.selectPage(pageReqVO);
    }

    @Override
    public List<BorrowingConfigDO> getBorrowingConfigList(BorrowingConfigListReqVO listReqVO) {
        return borrowingConfigDao.selectList(listReqVO);
    }

    @Override
    public Map<String, Integer> getBorrowingConfigMap() {
        Map<String, Integer> map = new HashMap<>();
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        LambdaQueryWrapper<BorrowingConfigDO> query = Wrappers.lambdaQuery();
        query.eq(BorrowingConfigDO::getOrgCode, sessionUser.getOrgCode()).last(" limit 1 ");
        BorrowingConfigDO borrowingConfig = this.getOne(query);
        map.put(CommonConstants.ZDJY_LIMIT, Objects.nonNull(borrowingConfig) && Objects.nonNull(borrowingConfig.getMaximumBorrowingLimit()) ?
                borrowingConfig.getMaximumBorrowingLimit() : 0);
        map.put(CommonConstants.ZDJY_DURATION, Objects.nonNull(borrowingConfig) && Objects.nonNull(borrowingConfig.getMaximumBorrowingDuration()) ?
                borrowingConfig.getMaximumBorrowingDuration() : 0);
        return map;
    }

    @Override
    public BorrowingConfigDO getBorrowingConfigByOrgCode(String orgCode) {
        return this.getOne(new LambdaQueryWrapperX<BorrowingConfigDO>()
                .eq(BorrowingConfigDO::getOrgCode, orgCode).last(" limit 1 "));
    }


}
