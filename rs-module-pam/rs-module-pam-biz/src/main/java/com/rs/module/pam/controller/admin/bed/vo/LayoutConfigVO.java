package com.rs.module.pam.controller.admin.bed.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@ApiModel(description = "管理后台 - 监室床位区域布局详情 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LayoutConfigVO extends BaseVO implements TransPojo {

    @ApiModelProperty("布局id")
    private String id;

    @ApiModelProperty("布局照片地址")
    private String layoutUrl;

    @ApiModelProperty("床位号")
    private List<BedAreaConfigVO> bedAreaConfigs;

}
