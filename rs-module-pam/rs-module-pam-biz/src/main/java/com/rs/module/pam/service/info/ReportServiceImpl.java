package com.rs.module.pam.service.info;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.enums.DataSourceAppEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.info.vo.ReportApproveReqVO;
import com.rs.module.pam.controller.admin.info.vo.ReportHistoryRespVO;
import com.rs.module.pam.controller.admin.info.vo.ReportItemSaveReqVO;
import com.rs.module.pam.controller.admin.info.vo.ReportRespVO;
import com.rs.module.pam.controller.admin.info.vo.ReportSaveReqVO;
import com.rs.module.pam.dao.info.ReportDao;
import com.rs.module.pam.entity.info.ReportDO;
import com.rs.module.pam.enums.ReportInfoStatusEnum;
import com.rs.module.pam.enums.ReportTimeTypeEnum;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 监所事务管理-信息报备 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ReportServiceImpl extends BaseServiceImpl<ReportDao, ReportDO> implements ReportService {

    @Resource
    private ReportDao reportDao;

    @Override
    public String createReport(ReportSaveReqVO createReqVO) {
        // 插入
        ReportDO report = BeanUtils.toBean(createReqVO, ReportDO.class);
        report.setReportTime(new Date());
        publicHandler(createReqVO, report);
        reportDao.insert(report);
        // 返回
        return report.getId();
    }

    private void publicHandler(ReportSaveReqVO createReqVO, ReportDO report) {
        List<ReportItemSaveReqVO> items = createReqVO.getItems();
        boolean present = items.stream().filter(item -> item.getIsRollcall() == 0).findAny().isPresent();
        if (present) {
            report.setIsRollcall(0);
        } else {
            report.setIsRollcall(1);
        }
        Map<String, List<ReportItemSaveReqVO>> listMap =
                items.stream().collect(Collectors.groupingBy(ReportItemSaveReqVO::getReportType));
        List<String> resultList = new ArrayList<>();
        listMap.forEach((k, v) -> {
            resultList.add(k + ": " + CollUtil.join(v, "，", (item) -> item.getReportContent()));
        });
        if (resultList.size() > 0) {
            report.setReportContent(CollUtil.join(resultList, "\n"));
        }
        ReportInfoStatusEnum statusEnum = getStatusEnum(createReqVO.getReportTimeType(), createReqVO.getApprovalResult(),
                createReqVO.getDataSources(), createReqVO.getReportStartTime(), createReqVO.getReportEndTime());
        if (statusEnum != null) {
            report.setStatus(statusEnum.getCode());
        }
    }

    @Override
    public void updateReport(ReportSaveReqVO updateReqVO) {
        // 校验存在
        validateReportExists(updateReqVO.getId());
        // 更新
        ReportDO updateObj = BeanUtils.toBean(updateReqVO, ReportDO.class);
        reportDao.updateById(updateObj);
    }

    private void validateReportExists(String id) {
        if (reportDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-信息报备数据不存在");
        }
    }

    @Override
    public ReportDO getReport(String id) {
        return reportDao.selectById(id);
    }

    @Override
    public ReportRespVO getReportRespVO(String id) {
        ReportDO reportDO = reportDao.selectById(id);
        if (reportDO == null) {
            throw new ServerException("监所事务管理-信息报备数据不存在");
        }
        ReportRespVO reportRespVO = new ReportRespVO();
        BeanUtils.copyProperties(reportDO, reportRespVO);
        ReportInfoStatusEnum statusEnum = getStatusEnum(reportDO.getReportTimeType(), reportDO.getApprovalResult(), reportDO.getDataSources(),
                reportDO.getReportStartTime(), reportDO.getReportEndTime());
        reportRespVO.setStatus(statusEnum.getName());
        getReportLong(reportRespVO);
        return reportRespVO;
    }

    @Override
    public ReportRespVO getReportRespVOByJgrybmAndRoomId(String jgrybm, String roomId) {
        LambdaQueryWrapper<ReportDO> lambdaQuery = Wrappers.lambdaQuery(ReportDO.class);
        lambdaQuery.eq(ReportDO::getObjectId, jgrybm)
                .eq(ReportDO::getRoomId, roomId);
        List<ReportDO> reportDOS = reportDao.selectList(lambdaQuery);
        if (CollUtil.isEmpty(reportDOS)) {
            return null;
        }
        for (ReportDO reportDO : reportDOS) {
            ReportInfoStatusEnum statusEnum = getStatusEnum(reportDO.getReportTimeType(), reportDO.getApprovalResult(), reportDO.getDataSources(),
                    reportDO.getReportStartTime(), reportDO.getReportEndTime());
            if (statusEnum.getCode().equals(ReportInfoStatusEnum.SXZ.getCode())) {
                ReportRespVO reportRespVO = new ReportRespVO();
                BeanUtils.copyProperties(reportDO, reportRespVO);
                reportRespVO.setStatus(statusEnum.getName());
                return reportRespVO;
            }
        }
        return null;
    }

    private static void getReportLong(ReportRespVO bean) {
        if (ReportTimeTypeEnum.YJ.getCode().equals(bean.getReportTimeType())) {
            bean.setReportLong(ReportTimeTypeEnum.YJ.getName());
        } else {
            String start = DateUtil.formatDate(bean.getReportStartTime());
            String end = DateUtil.formatDate(bean.getReportEndTime());
            bean.setReportLong(start + "~" + end);
        }
    }

    @Override
    public List<ReportHistoryRespVO> getHistoryReport(String jgrybm) {
        LambdaQueryWrapper<ReportDO> lambdaQuery = Wrappers.lambdaQuery(ReportDO.class);
        lambdaQuery.select(ReportDO::getReportTime, ReportDO::getReportType, ReportDO::getReportContent,
                        ReportDO::getReportTimeType, ReportDO::getReportStartTime, ReportDO::getReportEndTime,
                        ReportDO::getIsRollcall)
                .eq(ReportDO::getObjectId, jgrybm).orderByDesc(ReportDO::getReportTime);
        List<ReportDO> reportDOS = reportDao.selectList(lambdaQuery);
        List<ReportHistoryRespVO> reportHistoryRespVOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(reportDOS)) {
            for (ReportDO reportDO : reportDOS) {
                ReportHistoryRespVO reportHistoryRespVO = new ReportHistoryRespVO();
                BeanUtils.copyProperties(reportDO, reportHistoryRespVO);
                String reportTimeType = reportDO.getReportTimeType();
                if (ReportTimeTypeEnum.YJ.getCode().equals(reportTimeType)) {
                    reportHistoryRespVO.setReportLong(ReportTimeTypeEnum.YJ.getName());
                } else {
                    Date reportStartTime = reportDO.getReportStartTime();
                    Date reportEndTime = reportDO.getReportEndTime();
                    String reportLong = DateUtil.formatDateTime(reportStartTime) + "~" + DateUtil.formatDateTime(reportEndTime);
                    reportHistoryRespVO.setReportLong(reportLong);
                }
                reportHistoryRespVOS.add(reportHistoryRespVO);
            }
        }
        return reportHistoryRespVOS;
    }

    @Override
    public void policeApprove(ReportApproveReqVO approveReqVO) {
        // 校验存在
        String id = approveReqVO.getId();
        ReportDO reportDO = reportDao.selectById(id);
        if (reportDO == null) {
            throw new ServerException("信息报备数据不存在");
        }
        ReportDO updateObj = new ReportDO();
        updateObj.setId(id);
        updateObj.setApprovalComments(approveReqVO.getApprovalComments());
        updateObj.setApprovalResult(approveReqVO.getApprovalResult());
        updateObj.setApproverTime(new Date());
        String approvalResult = approveReqVO.getApprovalResult();
        ReportInfoStatusEnum statusEnum = getStatusEnum(reportDO.getReportTimeType(), approvalResult, reportDO.getDataSources(),
                reportDO.getReportStartTime(), reportDO.getReportEndTime());
        updateObj.setStatus(statusEnum.getCode());
        reportDao.updateById(updateObj);
    }

    private ReportInfoStatusEnum getStatusEnum(String reportTimeType, String approvalResult, String dataSources, Date startTime, Date endTime) {
        Date now = new Date();
        if (DataSourceAppEnum.ACP.getCode().equals(dataSources)) {
            if (ReportTimeTypeEnum.YJ.getCode().equals(reportTimeType)) {
                return ReportInfoStatusEnum.SXZ;
            }
            if (startTime.before(now) && endTime.after(now)) {
                return ReportInfoStatusEnum.SXZ; // 生效中
            } else if (startTime.after(now)) {
                return ReportInfoStatusEnum.WQY; // 未启用
            } else if (endTime.before(now)) {
                return ReportInfoStatusEnum.YJS; // 已结束
            }
        } else {
            if ("1".equals(approvalResult)) {
                if (ReportTimeTypeEnum.YJ.getCode().equals(reportTimeType)) {
                    return ReportInfoStatusEnum.SXZ; // 生效中
                } else {
                    if (startTime.before(now) && endTime.after(now)) {
                        return ReportInfoStatusEnum.SXZ; // 生效中
                    } else if (startTime.after(now)) {
                        return ReportInfoStatusEnum.WQY; // 未启用
                    } else if (endTime.before(now)) {
                        return ReportInfoStatusEnum.YJS; // 已结束
                    }
                }
            } else if ("0".equals(approvalResult)) {
                return ReportInfoStatusEnum.SPBTG; // 审批不通过
            } else {
                if (ReportTimeTypeEnum.YJ.getCode().equals(reportTimeType)) {
                    return ReportInfoStatusEnum.DSP; // 生效中
                }
                if (endTime.before(now)) {
                    return ReportInfoStatusEnum.YQWSH; // 逾期未审核
                } else {
                    return ReportInfoStatusEnum.DSP; // 待审批
                }
            }
        }
        throw new ServerException("报备状态不正确");
    }
}
