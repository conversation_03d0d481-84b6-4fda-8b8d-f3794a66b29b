package com.rs.module.pam.controller.admin.library.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel(description = "管理后台 - 监所事务管理-图书管理新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LibraryMgtSXJReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("上架状态（1：已上架，0：已下架）")
    @NotEmpty(message = "上架状态（1：已上架，0：已下架）不能为空")
    private String status;

}
