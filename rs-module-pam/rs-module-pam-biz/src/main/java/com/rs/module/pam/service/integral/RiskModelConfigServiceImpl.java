package com.rs.module.pam.service.integral;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.integral.vo.RiskModelConfigDTO;
import com.rs.module.pam.controller.admin.integral.vo.RiskModelConfigSaveReqVO;
import com.rs.module.pam.dao.integral.RiskModelConfigDao;
import com.rs.module.pam.entity.integral.IndicatorDO;
import com.rs.module.pam.entity.integral.RiskModelConfigDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * 监所事务管理-风险模型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RiskModelConfigServiceImpl extends BaseServiceImpl<RiskModelConfigDao, RiskModelConfigDO> implements RiskModelConfigService {

    @Resource
    private RiskModelConfigDao riskModelConfigDao;
    @Resource
    private IndicatorService indicatorService;

    @Override
    public RiskModelConfigDTO createRiskModelConfig(RiskModelConfigSaveReqVO createReqVO) {
        // 插入
        RiskModelConfigDO riskModelConfig = BeanUtils.toBean(createReqVO, RiskModelConfigDO.class);
        riskModelConfig.setLevelConfig(JSONObject.toJSONString(createReqVO.getLevelConfigs()));
        riskModelConfigDao.insert(riskModelConfig);
        // 返回
        return BeanUtils.toBean(riskModelConfig, RiskModelConfigDTO.class);
    }

    @Override
    public RiskModelConfigDTO updateRiskModelConfig(RiskModelConfigDTO updateReqVO) {
        // 校验存在
        validateRiskModelConfigExists(updateReqVO.getId());
        // 更新
        RiskModelConfigDO updateObj = BeanUtils.toBean(updateReqVO, RiskModelConfigDO.class);
        updateObj.setLevelConfig(JSONObject.toJSONString(updateReqVO.getLevelConfigs()));
        riskModelConfigDao.updateById(updateObj);
        // 修改权重配置
        List<IndicatorDO> indicatorList = BeanUtils.toBean(updateReqVO.getIndicatorList(), IndicatorDO.class);
        indicatorService.updateBatchById(indicatorList);
        return BeanUtils.toBean(updateObj, RiskModelConfigDTO.class);
    }

    @Override
    public void deleteRiskModelConfig(String id) {
        // 校验存在
        validateRiskModelConfigExists(id);
        // 删除
        riskModelConfigDao.deleteById(id);
    }

    private void validateRiskModelConfigExists(String id) {
        if (riskModelConfigDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-风险模型数据不存在");
        }
    }

    @Override
    public RiskModelConfigDO getRiskModelConfig(String id) {
        return riskModelConfigDao.selectById(id);
    }

    @Override
    public List<RiskModelConfigDO> getRiskModelByOrgCode(String orgCode) {
        List<RiskModelConfigDO> configList = riskModelConfigDao.getByOrgCode(orgCode);
        if (CollUtil.isEmpty(configList)) {
            return new ArrayList<>();
        }
        return configList;
    }

}
