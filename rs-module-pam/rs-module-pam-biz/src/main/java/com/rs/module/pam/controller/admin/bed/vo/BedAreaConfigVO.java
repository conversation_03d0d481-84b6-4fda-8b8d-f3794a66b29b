package com.rs.module.pam.controller.admin.bed.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@ApiModel(description = "管理后台 - 监室床位区域布局详情 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BedAreaConfigVO extends BaseVO implements TransPojo {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("x1")
    private String x1;

    @ApiModelProperty("y1")
    private String y1;

    @ApiModelProperty("行数")
    private Integer layoutRow;

    @ApiModelProperty("列数")
    private Integer layoutColumn;

    @ApiModelProperty("床位摆放类型(0:横放,1:竖放)")
    private String bedType;

    @ApiModelProperty("实体床位数量")
    private Integer entityBedCount;

    @ApiModelProperty("是否可添加床位")
    private Boolean isAllowedAdd;

    @ApiModelProperty("添加床位按钮位置")
    private String addLocation;

    @ApiModelProperty("床位号")
    private List<PrisonerBedDetailsVO> bedList;


}
