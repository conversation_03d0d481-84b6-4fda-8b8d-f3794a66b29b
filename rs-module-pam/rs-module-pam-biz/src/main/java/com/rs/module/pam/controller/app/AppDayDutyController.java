package com.rs.module.pam.controller.app;

import cn.hutool.core.date.DateUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.base.service.pm.AreaService;
import com.rs.module.pam.controller.app.vo.AppDutyRespVO;
import com.rs.module.pam.service.duty.day.DayDutyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "值日管理-监室值日-仓内屏")
@RestController
@RequestMapping("/app/pam/public/day/duty/")
@Validated
public class AppDayDutyController {

    @Resource
    private DayDutyService dutyService;
    @Resource
    private AreaService areaService;

    @RequestMapping(value = "/dutyRecords", method = RequestMethod.GET)
    @ApiOperation(value = "获取排班记录")
    public CommonResult<List<AppDutyRespVO>> dutyRecords(@RequestParam("orgCode") String orgCode,
                                                         @RequestParam("roomId") String roomId) {
        Date now = new Date();
        Date startDate = DateUtil.beginOfWeek(now).toJdkDate();
        Date endDate = DateUtil.endOfWeek(now).toJdkDate();
        List<AppDutyRespVO> records = dutyService.appDutyRecords(orgCode, roomId, startDate, endDate);
        return success(records);
    }


}
