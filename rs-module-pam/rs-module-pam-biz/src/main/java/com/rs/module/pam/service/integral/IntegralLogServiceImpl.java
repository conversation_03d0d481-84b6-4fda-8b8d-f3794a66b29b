package com.rs.module.pam.service.integral;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.module.pam.dao.integral.IntegralLogDao;
import com.rs.module.pam.entity.integral.IntegralLogDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


/**
 * 监所事务管理-积分执行日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class IntegralLogServiceImpl extends BaseServiceImpl<IntegralLogDao, IntegralLogDO> implements IntegralLogService {

    @Resource
    private IntegralLogDao integralLogDao;


}
