package com.rs.module.pam.dao.daily;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.pam.entity.daily.CleanDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 监所事务管理-日常清监登记 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface CleanDao extends IBaseDao<CleanDO> {

    @Select("<script>" +
            "SELECT area_name FROM acp_pm_area " +
            "<where>" +
            "  <if test='areaCodes != null and !areaCodes.isEmpty()'>"  +
            "    id IN " +
            "    <foreach item='id' collection='areaCodes' open='(' separator=',' close=')'>" +
            "      #{id}" +
            "    </foreach>" +
            "  </if>" +
            "  OR " +
            "  <if test='areaCodes != null and !areaCodes.isEmpty()'>"  +
            "    area_code IN " +
            "    <foreach item='id' collection='areaCodes' open='(' separator=',' close=')'>" +
            "      #{id}" +
            "    </foreach>" +
            "  </if>" +
            "</where></script>")
    List<String> getAreaList(@Param("areaCodes") List<String> areaCodes);

    /**
     * 获取每个月没有打扫的监室号列表
     * @return
     */
    List<Map<String, String>> getNoCleanRoomIdList(@Param("checkType") String checkType);

    /**
     * 获取每个月没有进行的安全大检查
     */
    List<Map<String, String>> getNoSecurityCheckList();
}
