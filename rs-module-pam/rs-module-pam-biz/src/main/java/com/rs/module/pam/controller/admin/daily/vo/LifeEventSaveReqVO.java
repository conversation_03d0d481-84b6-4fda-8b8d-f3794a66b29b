package com.rs.module.pam.controller.admin.daily.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 监所事务管理-一日生活制度关联事务新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LifeEventSaveReqVO extends BaseVO{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @NotEmpty(message = "ID不能为空")
    private String id;
    /**
     * 一日生活制度ID
     */
    @ApiModelProperty("一日生活制度ID")
    @NotEmpty(message = "一日生活制度ID不能为空")
    private String dailyLifeId;
    /**
     * 时间范围开始。示例 01:12
     */
    @ApiModelProperty("时间范围开始。示例 01:12")
    @NotEmpty(message = "时间范围开始不能为空")
    private String startTime;
    /**
     * 时间范围截至
     */
    @ApiModelProperty("时间范围截至")
    @NotEmpty(message = "时间范围截至不能为空")
    private String endTime;
    /**
     * 结束时间是否跨天,1跨天
     */
    @ApiModelProperty("结束时间是否跨天,1跨天")
    @NotEmpty(message = "结束时间是否跨天不能为空")
    private Short endTimeSpan;
    /**
     * 事务ID（逗号分隔）
     */
    @ApiModelProperty("事务ID（逗号分隔）")
    @NotEmpty(message = "事务ID不能为空")
    private String eventId;
    /**
     * 事务名称（逗号分隔）
     */
    @ApiModelProperty("事务名称（逗号分隔）")
    @NotEmpty(message = "事务名称不能为空")
    private String eventName;
    /**
     * 是否启用语音播报(0:否,1:是)
     */
    @ApiModelProperty("是否启用语音播报(0:否,1:是)")
    @NotEmpty(message = "是否启用语音播报不能为空")
    private Short isEnabledVoice;

}
