package com.rs.module.pam.service.screen.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.pam.entity.screen.FocusOnPersonnelDO;
import com.rs.module.pam.service.screen.GjLargeScreenService;

import java.lang.reflect.Method;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

public class ZdgzryTask implements Callable<JSONObject> {

    private GjLargeScreenService gjLargeScreenService;

    private String methodName;

    private String code;

    private String codeType;

    private CountDownLatch countDownLatch;

    public ZdgzryTask(GjLargeScreenService gjLargeScreenService, String methodName, String code, String codeType, CountDownLatch countDownLatch) {
        this.gjLargeScreenService = gjLargeScreenService;
        this.methodName = methodName;
        this.code = code;
        this.codeType = codeType;
        this.countDownLatch = countDownLatch;
    }

    @Override
    public JSONObject call() {
        JSONObject jsonObject = new JSONObject();
        try {
            Method method = gjLargeScreenService.getClass().getMethod(methodName, int.class, int.class, String.class, String.class);
            Object invoke = method.invoke(gjLargeScreenService, 1, 0, code, codeType);
            JSONObject pageResult = JSON.parseObject(JSON.toJSONString(invoke));
            jsonObject.put(methodName, pageResult.getLongValue("total"));
        } catch (Exception e) {
            e.printStackTrace();
            jsonObject.put(methodName, 0);
        } finally {
            countDownLatch.countDown();
        }
        return jsonObject;
    }
}
