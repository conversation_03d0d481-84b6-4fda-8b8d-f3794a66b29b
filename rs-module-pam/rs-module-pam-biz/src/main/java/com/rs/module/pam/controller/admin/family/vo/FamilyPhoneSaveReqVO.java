package com.rs.module.pam.controller.admin.family.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-亲情电话新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FamilyPhoneSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("数据来源（字典：ZD_DATA_SOURCES）")
    private String dataSources;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("联系对象")
    private String telObject;

    @ApiModelProperty("与通话人员关系")
    private String relName;

    @ApiModelProperty("电话号码")
    private String telNum;

    @ApiModelProperty("通话日期")
    private Date telTime;

    @ApiModelProperty("经办人身份证号")
    private String handlerUserSfzh;

    @ApiModelProperty("经办人")
    private String handlerUserName;

    @ApiModelProperty("经办时间")
    private Date handlerTime;




}
