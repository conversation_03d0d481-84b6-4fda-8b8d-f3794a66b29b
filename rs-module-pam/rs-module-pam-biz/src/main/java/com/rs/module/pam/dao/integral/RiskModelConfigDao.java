package com.rs.module.pam.dao.integral;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.pam.entity.integral.RiskModelConfigDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 监所事务管理-风险模型 Dao
*
* <AUTHOR>
*/
@Mapper
public interface RiskModelConfigDao extends IBaseDao<RiskModelConfigDO> {

    default List<RiskModelConfigDO> getByOrgCode(String orgCode) {
        return selectList(new LambdaQueryWrapper<RiskModelConfigDO>()
                .eq(RiskModelConfigDO::getOrgCode, orgCode)
                .orderByAsc(RiskModelConfigDO::getOrderId));
    }

    default RiskModelConfigDO getByOrgCode(String orgCode, String type) {
        return selectOne(new LambdaQueryWrapper<RiskModelConfigDO>()
                .eq(RiskModelConfigDO::getOrgCode, orgCode)
                .eq(RiskModelConfigDO::getType, type));
    }

}
