package com.rs.module.pam.controller.app.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "仓内屏 -特殊餐List Request VO")
@Data
public class AppSpecialApplyListRespVO {

    @ApiModelProperty("配餐类型")
    private String mealType;

    @ApiModelProperty("配餐类型名称")
    private String mealTypeName;

    @ApiModelProperty("配餐开始日期")
    private Date mealStartTime;

    @ApiModelProperty("配餐结束时间")
    private Date mealEndTime;

    @ApiModelProperty("申请时间")
    private Date regTime;

    @ApiModelProperty("审批结果")
    private String regStatus;

    @ApiModelProperty("用餐时段，多选逗号分割（字典：ZD_PCGL_DSLX）")
    private String mealPeriod;

    @ApiModelProperty("指定日期，多选逗号分割")
    private String specifiedDate;

    @ApiModelProperty("申请原因")
    private String reason;

}
