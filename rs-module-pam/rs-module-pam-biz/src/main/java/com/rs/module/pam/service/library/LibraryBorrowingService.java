package com.rs.module.pam.service.library;

import java.util.*;
import javax.validation.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rs.module.pam.controller.admin.library.vo.*;
import com.rs.module.pam.controller.app.vo.AppLibraryBorrowingSaveReqVO;
import com.rs.module.pam.entity.library.LibraryBorrowingDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 监所事务管理-图书借阅申请 Service 接口
 *
 * <AUTHOR>
 */
public interface LibraryBorrowingService extends IBaseService<LibraryBorrowingDO>{

    /**
     * 创建监所事务管理-图书借阅申请
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createLibraryBorrowing(@Valid LibraryBorrowingSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-图书借阅申请
     *
     * @param updateReqVO 更新信息
     */
    void updateLibraryBorrowing(@Valid LibraryBorrowingSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-图书借阅申请
     *
     * @param id 编号
     */
    void deleteLibraryBorrowing(String id);

    /**
     * 获得监所事务管理-图书借阅申请
     *
     * @param id 编号
     * @return 监所事务管理-图书借阅申请
     */
    LibraryBorrowingDO getLibraryBorrowing(String id);

    /**
    * 获得监所事务管理-图书借阅申请分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-图书借阅申请分页
    */
    PageResult<LibraryBorrowingDO> getLibraryBorrowingPage(LibraryBorrowingPageReqVO pageReqVO);

    /**
    * 获得监所事务管理-图书借阅申请列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-图书借阅申请列表
    */
    List<LibraryBorrowingDO> getLibraryBorrowingList(LibraryBorrowingListReqVO listReqVO);


    String appCreateLibraryBorrowing(AppLibraryBorrowingSaveReqVO createReqVO);

    void approvalLibraryBorrowing(LibraryBorrowingApprovalReqVO approvalReqVO);

    JSONObject businessAnalysis(String orgCode);

    PageResult<LibraryBorrowingDO> personalBorrowingRecordPage(int pageNo, int pageSize, String jgrybm, String type);

}
