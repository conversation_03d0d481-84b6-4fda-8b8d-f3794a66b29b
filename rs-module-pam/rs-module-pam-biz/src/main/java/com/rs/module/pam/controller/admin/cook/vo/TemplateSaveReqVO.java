package com.rs.module.pam.controller.admin.cook.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-菜品模板新增/修改 Request VO")
@Data
public class TemplateSaveReqVO implements Serializable {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("模板名称")
    @NotEmpty(message = "模板名称不能为空")
    private String templateName;
    @ApiModelProperty("菜谱开始日期")
    private Date startTime;
    @ApiModelProperty("菜谱截至日期")
    private Date endTime;
    @ApiModelProperty("周编号 比如1月第一周 20230101")
    private String weekNo;
    @ApiModelProperty("是否复制")
    private String isCopy;
    @ApiModelProperty("复制ID")
    private String copyId;

    @ApiModelProperty("模板描述")
    private List<CookMeal> cookMeals;

    @ApiModelProperty("模板内容")
    private List<CookSub> cookSubs;

    @Data
    public static class CookMeal {
        private String name;
        private String weekId;
        private int sortNo;
    }

    @Data
    public static class CookSub {
        private String mealName;
        private int mealNo;
        private List<CookWeekSub> cookWeekSubs;
    }

    @Data
    public static class CookWeekSub {
        private String weekId;
        private List<Cook> cooks;
    }

    @Data
    public static class Cook {
        private String name;
        private String cateId;
        private String cateColor;
    }


}
