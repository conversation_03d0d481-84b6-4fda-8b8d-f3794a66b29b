package com.rs.module.pam.controller.admin.duty.day;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.pam.controller.admin.duty.day.vo.DayDutySaveVO;
import com.rs.module.pam.controller.admin.duty.day.vo.DayDutyVO;
import com.rs.module.pam.controller.admin.duty.vo.DutyPrisonerVO;
import com.rs.module.pam.service.duty.day.DayDutyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "值日管理-监室值日")
@RestController
@RequestMapping("/pam/day/duty")
@Validated
public class DayDutyController {

    @Resource
    private DayDutyService dayDutyService;


    @PostMapping("/create")
    @ApiOperation(value = "创建监室值日")
    @BusTrace(busType = BusTypeEnum.YEWU_JSZB, condition = "false", content = "{\"机构编号\":\"{{#createReqVO.orgCode}}\"," +
            "\"监室编号\":\"{{#createReqVO.roomId}}\",\"值日开始时间\":\"{{#createReqVO.startDate}}\",\"值日结束时间\":\"{{#createReqVO.endDate}}\"}")
    public CommonResult<String> create(@Valid @RequestBody DayDutySaveVO createReqVO) {
        dayDutyService.create(createReqVO);
        return success();
    }

    @RequestMapping(value = "/dutyRecords", method = RequestMethod.GET)
    @ApiOperation(value = "获取排班记录")
    @BusTrace(busType = BusTypeEnum.YEWU_JSZB, condition = "false", content = "{\"监室编号\":\"{{#roomId}}\"," +
            "\"值日开始时间\":\"{{#startDate}}\",\"值日结束时间\":\"{{#endDate}}\"}")
    public CommonResult<List<DayDutyVO>> dutyRecords(@RequestParam("orgCode") String orgCode,
                                                     @RequestParam("roomId") String roomId,
                                                     @RequestParam("startDate") Date startDate,
                                                     @RequestParam("endDate") Date endDate) {
        List<DayDutyVO> records = dayDutyService.dutyRecords(orgCode, roomId, startDate, endDate);
        return success(records);
    }

    @RequestMapping(value = "/prisonerList", method = RequestMethod.GET)
    @ApiOperation(value = "监室人员列表")
    @BusTrace(busType = BusTypeEnum.YEWU_JSZB, condition = "false", content = "{\"监室编号\":\"{{#roomId}}\"")
    public CommonResult prisonerList(@RequestParam("orgCode") String orgCode,
                                     @RequestParam("roomId") String roomId) {
        List<DutyPrisonerVO> list = dayDutyService.prisonerList(orgCode, roomId);
        return success(list);
    }

}
