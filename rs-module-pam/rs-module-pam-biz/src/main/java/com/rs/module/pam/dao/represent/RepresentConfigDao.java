package com.rs.module.pam.dao.represent;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.represent.vo.RepresentConfigListReqVO;
import com.rs.module.pam.controller.admin.represent.vo.RepresentConfigPageReqVO;
import com.rs.module.pam.entity.represent.RepresentConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* 监所事务管理-监室点名配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface RepresentConfigDao extends IBaseDao<RepresentConfigDO> {


    default PageResult<RepresentConfigDO> selectPage(RepresentConfigPageReqVO reqVO) {
        Page<RepresentConfigDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<RepresentConfigDO> wrapper = new LambdaQueryWrapperX<RepresentConfigDO>()
            .eqIfPresent(RepresentConfigDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(RepresentConfigDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(RepresentConfigDO::getConfigPeriod, reqVO.getConfigPeriod())
            .betweenIfPresent(RepresentConfigDO::getStartTime, reqVO.getStartTime())
            .eqIfPresent(RepresentConfigDO::getCron, reqVO.getCron())
            .eqIfPresent(RepresentConfigDO::getConfigPeriodCode, reqVO.getConfigPeriodCode())
            .likeIfPresent(RepresentConfigDO::getTaskName, reqVO.getTaskName())
            .eqIfPresent(RepresentConfigDO::getStatus, reqVO.getStatus())
            .betweenIfPresent(RepresentConfigDO::getEndTime, reqVO.getEndTime())
            .eqIfPresent(RepresentConfigDO::getEndCron, reqVO.getEndCron())
            .betweenIfPresent(RepresentConfigDO::getExpiryDate, reqVO.getExpiryDate())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(RepresentConfigDO::getAddTime);
        }
        Page<RepresentConfigDO> configPage = selectPage(page, wrapper);
        return new PageResult<>(configPage.getRecords(), configPage.getTotal());
    }

    default List<RepresentConfigDO> selectList(RepresentConfigListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<RepresentConfigDO>()
            .eqIfPresent(RepresentConfigDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(RepresentConfigDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(RepresentConfigDO::getConfigPeriod, reqVO.getConfigPeriod())
            .betweenIfPresent(RepresentConfigDO::getStartTime, reqVO.getStartTime())
            .eqIfPresent(RepresentConfigDO::getCron, reqVO.getCron())
            .eqIfPresent(RepresentConfigDO::getConfigPeriodCode, reqVO.getConfigPeriodCode())
            .likeIfPresent(RepresentConfigDO::getTaskName, reqVO.getTaskName())
            .eqIfPresent(RepresentConfigDO::getStatus, reqVO.getStatus())
            .betweenIfPresent(RepresentConfigDO::getEndTime, reqVO.getEndTime())
            .eqIfPresent(RepresentConfigDO::getEndCron, reqVO.getEndCron())
            .betweenIfPresent(RepresentConfigDO::getExpiryDate, reqVO.getExpiryDate())
        .orderByDesc(RepresentConfigDO::getAddTime));
    }


    List<RepresentConfigDO> findAll();

    @Select("select * from pam_represent_config where is_del = '0' and status = '0' ")
    List<RepresentConfigDO> getAllConfig();

    @Select("select * from pam_represent_config where is_del = '0' and org_code = #{orgCode} and start_time = #{startTime} ")
    List<RepresentConfigDO> getByOrgCodeAndTime(@Param("orgCode") String orgCode, @Param("startTime") String startTime);

    /**
     * 得到配置列表
     * @return
     */
    RepresentConfigDO getConfigListById(@Param("id") String id);
}
