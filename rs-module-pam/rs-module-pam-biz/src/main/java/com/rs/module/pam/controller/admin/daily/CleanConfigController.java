package com.rs.module.pam.controller.admin.daily;

import cn.hutool.core.util.StrUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.daily.vo.CleanConfigListReqVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanConfigRespVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanConfigSaveReqVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanConfigUpdateReqVO;
import com.rs.module.pam.entity.daily.CleanConfigDO;
import com.rs.module.pam.service.daily.CleanConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "安全检查-日常清监检查项配置")
@RestController
@RequestMapping("/pam/daily/cleanConfig")
@Validated
public class CleanConfigController {

    @Resource
    private CleanConfigService cleanConfigService;

    @PostMapping("/create")
    @ApiOperation(value = "创建安全检查-日常清监检查项配置")
    public CommonResult<String> createCleanConfig(@Valid @RequestBody CleanConfigSaveReqVO createReqVO) {
        cleanConfigService.createCleanConfig(createReqVO);
        return success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新安全检查-日常清监检查项配置")
    public CommonResult<Boolean> updateCleanConfig(@Valid @RequestBody CleanConfigUpdateReqVO updateReqVO) {
        cleanConfigService.updateCleanConfig(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除安全检查-日常清监检查项配置")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteCleanConfig(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            cleanConfigService.deleteCleanConfig(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得安全检查-日常清监检查项配置")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<CleanConfigRespVO> getCleanConfig(@RequestParam("id") String id) {
        CleanConfigDO cleanConfig = cleanConfigService.getCleanConfig(id);
        return success(BeanUtils.toBean(cleanConfig, CleanConfigRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得安全检查-日常清监检查项配置列表")
    public CommonResult<List<CleanConfigRespVO>> getCleanConfigList(@Valid @RequestBody CleanConfigListReqVO listReqVO) {
        if (StrUtil.isBlank(listReqVO.getOrgCode())) {
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        List<CleanConfigDO> list = cleanConfigService.getCleanConfigList(listReqVO);
        return success(BeanUtils.toBean(list, CleanConfigRespVO.class));
    }
}
