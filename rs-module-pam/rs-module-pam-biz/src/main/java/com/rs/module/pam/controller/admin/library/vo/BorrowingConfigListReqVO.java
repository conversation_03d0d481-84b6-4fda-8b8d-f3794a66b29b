package com.rs.module.pam.controller.admin.library.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 监所事务管理-图书借阅配置列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BorrowingConfigListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("个人最大借阅数")
    private Integer maximumBorrowingLimit;

    @ApiModelProperty("个人最大借阅时长（天）")
    private Integer maximumBorrowingDuration;

}
