package com.rs.module.pam.controller.admin.information.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-监室信息发布 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PublishEducationalReaderRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty("信息类型（字典：ZD_JYDBLX）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JYDBLX")
    private String infoType;
    @ApiModelProperty("信息内容")
    private String infoContentText;
    @ApiModelProperty("信息内容")
    private String infoContentHtml;
    @ApiModelProperty("登记经办人")
    private String operatorSfzh;
    @ApiModelProperty("登记经办人姓名")
    private String operatorXm;
    @ApiModelProperty("登记时间")
    private Date operatorTime;
    @ApiModelProperty("登记状态")
    private String status;
}
