package com.rs.module.pam.dao.psy;

import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanPushRecordListReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanPushRecordRespVO;
import com.rs.module.pam.controller.app.vo.AppPsyTableRecordRespVO;
import com.rs.module.pam.entity.psy.EvalPlanPushRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 监所事务管理-心理测评推送记录 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface EvalPlanPushRecordDao extends IBaseDao<EvalPlanPushRecordDO> {


    default List<EvalPlanPushRecordDO> selectList(EvalPlanPushRecordListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<EvalPlanPushRecordDO>()
                .eqIfPresent(EvalPlanPushRecordDO::getEvalNo, reqVO.getEvalNo())
                .likeIfPresent(EvalPlanPushRecordDO::getPlanName, reqVO.getPlanName())
                .eqIfPresent(EvalPlanPushRecordDO::getPlanCode, reqVO.getPlanCode())
                .eqIfPresent(EvalPlanPushRecordDO::getPlanType, reqVO.getPlanType())
                .likeIfPresent(EvalPlanPushRecordDO::getPlanAddUserName, reqVO.getPlanAddUserName())
                .eqIfPresent(EvalPlanPushRecordDO::getTableId, reqVO.getTableId())
                .likeIfPresent(EvalPlanPushRecordDO::getTableName, reqVO.getTableName())
                .eqIfPresent(EvalPlanPushRecordDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(EvalPlanPushRecordDO::getJgryxm, reqVO.getJgryxm())
                .betweenIfPresent(EvalPlanPushRecordDO::getPushTime, reqVO.getPushTime())
                .eqIfPresent(EvalPlanPushRecordDO::getFillingStatus, reqVO.getFillingStatus())
                .betweenIfPresent(EvalPlanPushRecordDO::getFillingTime, reqVO.getFillingTime())
                .eqIfPresent(EvalPlanPushRecordDO::getFillingPlatform, reqVO.getFillingPlatform())
                .eqIfPresent(EvalPlanPushRecordDO::getScore, reqVO.getScore())
                .eqIfPresent(EvalPlanPushRecordDO::getEvalResults, reqVO.getEvalResults())
                .orderByDesc(EvalPlanPushRecordDO::getAddTime));
    }


    List<AppPsyTableRecordRespVO> tableList(@Param("jgrybm") String jgrybm, @Param("tableTypes") List<String> tableTypes);


    List<EvalPlanPushRecordRespVO> getEvalPlanPushRecordList(@Param(Constants.WRAPPER) EvalPlanPushRecordListReqVO listReqVO);


    Long getEvalPlanPushRecordListCount(@Param(Constants.WRAPPER) EvalPlanPushRecordListReqVO listReqVO);


    List<String> existPushRecord(@Param("planCode") String planCode, @Param("tableIds") List<String> tableIds,
                                 @Param("jgrybms") Set<String> jgrybms);
}
