package com.rs.module.pam.service.letter;

import java.util.*;
import javax.validation.*;
import com.rs.module.pam.controller.admin.letter.vo.*;
import com.rs.module.pam.controller.app.vo.AppLetterPostSaveReqVO;
import com.rs.module.pam.entity.letter.LetterPostDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.entity.letter.LetterReceiveDO;

/**
 * 监所事务管理-家属通信-寄信登记 Service 接口
 *
 * <AUTHOR>
 */
public interface LetterPostService extends IBaseService<LetterPostDO>{

    /**
     * 创建监所事务管理-家属通信-寄信登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createLetterPost(@Valid LetterPostSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-家属通信-寄信登记
     *
     * @param updateReqVO 更新信息
     */
    void updateLetterPost(@Valid LetterPostSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-家属通信-寄信登记
     *
     * @param id 编号
     */
    void deleteLetterPost(String id);

    /**
     * 获得监所事务管理-家属通信-寄信登记
     *
     * @param id 编号
     * @return 监所事务管理-家属通信-寄信登记
     */
    LetterPostDO getLetterPost(String id);

    /**
    * 获得监所事务管理-家属通信-寄信登记分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-家属通信-寄信登记分页
    */
    PageResult<LetterPostDO> getLetterPostPage(LetterPostPageReqVO pageReqVO);

    /**
    * 获得监所事务管理-家属通信-寄信登记列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-家属通信-寄信登记列表
    */
    List<LetterPostDO> getLetterPostList(LetterPostListReqVO listReqVO);


    String appCreateLetterPost(AppLetterPostSaveReqVO appLetterPostSaveReqVO);


    void approvalLetterPost(LetterPostApprovalReqVO letterPostApprovalReqVO);

    void letterPost(LetterPostReqVO letterPostReqVO);

    PageResult<LetterPostDO> getAppLetterPostPage(int pageNo, int pageSize, String jgrybm, String type);
}
