package com.rs.module.pam.controller.admin.library;

import com.bsp.security.util.SessionUserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.pam.controller.admin.library.vo.*;
import com.rs.module.pam.entity.library.BorrowingConfigDO;
import com.rs.module.pam.service.library.BorrowingConfigService;

@Api(tags = "监所事务管理-图书借阅配置")
@RestController
@RequestMapping("/pam/library/borrowingConfig")
@Validated
public class BorrowingConfigController {

    @Resource
    private BorrowingConfigService borrowingConfigService;

    @PostMapping("/create")
    @ApiOperation(value = "创建或者更新监所事务管理-图书借阅配置")
    public CommonResult<String> createBorrowingConfig(@Valid @RequestBody BorrowingConfigSaveReqVO createReqVO) {
        return success(borrowingConfigService.createBorrowingConfig(createReqVO));
    }


    @PostMapping("/update")
    @ApiOperation(value = "更新监所事务管理-图书借阅配置")
    public CommonResult<Boolean> updateBorrowingConfig(@Valid @RequestBody BorrowingConfigSaveReqVO updateReqVO) {
        borrowingConfigService.updateBorrowingConfig(updateReqVO);
        return success(true);
    }

    @GetMapping("/get-one")
    @ApiOperation(value = "机构获得唯一监所事务管理-图书借阅配置")
    @ApiImplicitParam(name = "orgCode", value = "机构编号")
    public CommonResult<BorrowingConfigRespVO> getBorrowingConfigByOrgCode(
            @RequestParam(value = "orgCode", required = false) String orgCode) {
        if (StringUtils.isEmpty(orgCode)) {
            orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        }
        BorrowingConfigDO configDO = borrowingConfigService.getBorrowingConfigByOrgCode(orgCode);
        return success(BeanUtils.toBean(configDO, BorrowingConfigRespVO.class));
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得监所事务管理-图书借阅配置")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<BorrowingConfigRespVO> getBorrowingConfig(@RequestParam("id") String id) {
        BorrowingConfigDO borrowingConfig = borrowingConfigService.getBorrowingConfig(id);
        return success(BeanUtils.toBean(borrowingConfig, BorrowingConfigRespVO.class));
    }


    @GetMapping("/delete")
    @ApiOperation(value = "删除监所事务管理-图书借阅配置")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteBorrowingConfig(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            borrowingConfigService.deleteBorrowingConfig(id);
        }
        return success(true);
    }


    @PostMapping("/page")
    @ApiOperation(value = "获得监所事务管理-图书借阅配置分页")
    public CommonResult<PageResult<BorrowingConfigRespVO>> getBorrowingConfigPage(@Valid @RequestBody BorrowingConfigPageReqVO pageReqVO) {
        PageResult<BorrowingConfigDO> pageResult = borrowingConfigService.getBorrowingConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BorrowingConfigRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得监所事务管理-图书借阅配置列表")
    public CommonResult<List<BorrowingConfigRespVO>> getBorrowingConfigList(@Valid @RequestBody BorrowingConfigListReqVO listReqVO) {
        List<BorrowingConfigDO> list = borrowingConfigService.getBorrowingConfigList(listReqVO);
        return success(BeanUtils.toBean(list, BorrowingConfigRespVO.class));
    }

}
