package com.rs.module.pam.controller.admin.integral.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@ApiModel(description = "风险模型新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RiskModelConfigSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("风险模型名称")
    private String name;

    @ApiModelProperty("风险模型类型")
    private String type;

    @ApiModelProperty("等级配置")
    private List<RiskModelLevelVO> levelConfigs;

    @ApiModelProperty("排序")
    private Integer orderId;

}
