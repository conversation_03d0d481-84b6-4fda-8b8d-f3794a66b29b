package com.rs.module.pam.dao.represent;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.represent.vo.CheckListReqVO;
import com.rs.module.pam.controller.admin.represent.vo.CheckPageReqVO;
import com.rs.module.pam.entity.represent.CheckDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
* 监所事务管理-人脸库及人员库信息进行比对 Dao
*
* <AUTHOR>
*/
@Mapper
public interface CheckDao extends IBaseDao<CheckDO> {


    default PageResult<CheckDO> selectPage(CheckPageReqVO reqVO) {
        Page<CheckDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<CheckDO> wrapper = new LambdaQueryWrapperX<CheckDO>()
            .eqIfPresent(CheckDO::getPresentNo, reqVO.getPresentNo())
            .eqIfPresent(CheckDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(CheckDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(CheckDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(CheckDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(CheckDO::getStatus, reqVO.getStatus())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(CheckDO::getAddTime);
        }
        Page<CheckDO> checkPage = selectPage(page, wrapper);
        return new PageResult<>(checkPage.getRecords(), checkPage.getTotal());
    }
    default List<CheckDO> selectList(CheckListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CheckDO>()
            .eqIfPresent(CheckDO::getPresentNo, reqVO.getPresentNo())
            .eqIfPresent(CheckDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(CheckDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(CheckDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(CheckDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(CheckDO::getStatus, reqVO.getStatus())
        .orderByDesc(CheckDO::getAddTime));    }

    @Update("update pam_represent_check set status = 1,update_time = now() where present_no = #{presentNo} and jgrybm = #{jgrybm}")
    Integer updateCheckData(@Param("presentNo") String presentNo, @Param("jgrybm") String jgrybm);
}
