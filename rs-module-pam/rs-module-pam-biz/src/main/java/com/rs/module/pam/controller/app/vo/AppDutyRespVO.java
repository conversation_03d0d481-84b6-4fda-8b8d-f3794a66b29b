package com.rs.module.pam.controller.app.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-监室值班列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AppDutyRespVO extends BaseVO {

    @ApiModelProperty("机构编码")
    private String orgCode;

    @ApiModelProperty("监室编号")
    private String roomId;

    @ApiModelProperty("值班名称")
    private String dutyName;

    @ApiModelProperty("值班日期")
    private Date dutyDate;

    @ApiModelProperty("周几")
    private String weekday;

    @ApiModelProperty("值班列表")
    private List<AppDutyShiftRespVO> dutyShiftList;

}
