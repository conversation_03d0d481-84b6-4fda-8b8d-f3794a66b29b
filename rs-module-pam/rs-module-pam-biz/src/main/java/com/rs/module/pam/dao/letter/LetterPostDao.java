package com.rs.module.pam.dao.letter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.letter.vo.LetterPostListReqVO;
import com.rs.module.pam.controller.admin.letter.vo.LetterPostPageReqVO;
import com.rs.module.pam.entity.letter.LetterPostDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 监所事务管理-家属通信-寄信登记 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface LetterPostDao extends IBaseDao<LetterPostDO> {


    default PageResult<LetterPostDO> selectPage(LetterPostPageReqVO reqVO) {
        Page<LetterPostDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<LetterPostDO> wrapper = new LambdaQueryWrapperX<LetterPostDO>()
                .eqIfPresent(LetterPostDO::getDataSources, reqVO.getDataSources())
                .eqIfPresent(LetterPostDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(LetterPostDO::getReceiveMailUser, reqVO.getReceiveMailUser())
                .eqIfPresent(LetterPostDO::getRelation, reqVO.getRelation())
                .eqIfPresent(LetterPostDO::getReceiveAddress, reqVO.getReceiveAddress())
                .betweenIfPresent(LetterPostDO::getSendTime, reqVO.getSendTime())
                .eqIfPresent(LetterPostDO::getReceivePrison, reqVO.getReceivePrison())
                .eqIfPresent(LetterPostDO::getMailNo, reqVO.getMailNo())
                .eqIfPresent(LetterPostDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(LetterPostDO::getApplyTime, reqVO.getApplyTime())
                .eqIfPresent(LetterPostDO::getSignUrl, reqVO.getSignUrl())
                .eqIfPresent(LetterPostDO::getStatus, reqVO.getStatus())
                .eqIfPresent(LetterPostDO::getApproverStatus, reqVO.getApproverStatus())
                .eqIfPresent(LetterPostDO::getPassUser, reqVO.getPassUser())
                .betweenIfPresent(LetterPostDO::getPassTime, reqVO.getPassTime())
                .eqIfPresent(LetterPostDO::getPassRemark, reqVO.getPassRemark())
                .eqIfPresent(LetterPostDO::getGjApproverSfzh, reqVO.getGjApproverSfzh())
                .eqIfPresent(LetterPostDO::getGjApproverXm, reqVO.getGjApproverXm())
                .betweenIfPresent(LetterPostDO::getGjApproverTime, reqVO.getGjApproverTime())
                .eqIfPresent(LetterPostDO::getGjApprovalResult, reqVO.getGjApprovalResult())
                .eqIfPresent(LetterPostDO::getGjApprovalComments, reqVO.getGjApprovalComments())
                .eqIfPresent(LetterPostDO::getGroupApproverSfzh, reqVO.getGroupApproverSfzh())
                .eqIfPresent(LetterPostDO::getGroupApproverXm, reqVO.getGroupApproverXm())
                .betweenIfPresent(LetterPostDO::getGroupApproverTime, reqVO.getGroupApproverTime())
                .eqIfPresent(LetterPostDO::getGroupApprovalResult, reqVO.getGroupApprovalResult())
                .eqIfPresent(LetterPostDO::getGroupApprovalComments, reqVO.getGroupApprovalComments())
                .eqIfPresent(LetterPostDO::getLeaderApproverSfzh, reqVO.getLeaderApproverSfzh())
                .eqIfPresent(LetterPostDO::getLeaderApproverXm, reqVO.getLeaderApproverXm())
                .betweenIfPresent(LetterPostDO::getLeaderApproverTime, reqVO.getLeaderApproverTime())
                .eqIfPresent(LetterPostDO::getLeaderApprovalResult, reqVO.getLeaderApprovalResult())
                .eqIfPresent(LetterPostDO::getLeaderApprovalComments, reqVO.getLeaderApprovalComments())
                .eqIfPresent(LetterPostDO::getActInstId, reqVO.getActInstId())
                .eqIfPresent(LetterPostDO::getTaskId, reqVO.getTaskId());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(LetterPostDO::getAddTime);
        }
        Page<LetterPostDO> letterPostPage = selectPage(page, wrapper);
        return new PageResult<>(letterPostPage.getRecords(), letterPostPage.getTotal());
    }

    default List<LetterPostDO> selectList(LetterPostListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<LetterPostDO>()
                .eqIfPresent(LetterPostDO::getDataSources, reqVO.getDataSources())
                .eqIfPresent(LetterPostDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(LetterPostDO::getReceiveMailUser, reqVO.getReceiveMailUser())
                .eqIfPresent(LetterPostDO::getRelation, reqVO.getRelation())
                .eqIfPresent(LetterPostDO::getReceiveAddress, reqVO.getReceiveAddress())
                .betweenIfPresent(LetterPostDO::getSendTime, reqVO.getSendTime())
                .eqIfPresent(LetterPostDO::getReceivePrison, reqVO.getReceivePrison())
                .eqIfPresent(LetterPostDO::getMailNo, reqVO.getMailNo())
                .eqIfPresent(LetterPostDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(LetterPostDO::getApplyTime, reqVO.getApplyTime())
                .eqIfPresent(LetterPostDO::getSignUrl, reqVO.getSignUrl())
                .eqIfPresent(LetterPostDO::getStatus, reqVO.getStatus())
                .eqIfPresent(LetterPostDO::getApproverStatus, reqVO.getApproverStatus())
                .eqIfPresent(LetterPostDO::getPassUser, reqVO.getPassUser())
                .betweenIfPresent(LetterPostDO::getPassTime, reqVO.getPassTime())
                .eqIfPresent(LetterPostDO::getPassRemark, reqVO.getPassRemark())
                .eqIfPresent(LetterPostDO::getGjApproverSfzh, reqVO.getGjApproverSfzh())
                .eqIfPresent(LetterPostDO::getGjApproverXm, reqVO.getGjApproverXm())
                .betweenIfPresent(LetterPostDO::getGjApproverTime, reqVO.getGjApproverTime())
                .eqIfPresent(LetterPostDO::getGjApprovalResult, reqVO.getGjApprovalResult())
                .eqIfPresent(LetterPostDO::getGjApprovalComments, reqVO.getGjApprovalComments())
                .eqIfPresent(LetterPostDO::getGroupApproverSfzh, reqVO.getGroupApproverSfzh())
                .eqIfPresent(LetterPostDO::getGroupApproverXm, reqVO.getGroupApproverXm())
                .betweenIfPresent(LetterPostDO::getGroupApproverTime, reqVO.getGroupApproverTime())
                .eqIfPresent(LetterPostDO::getGroupApprovalResult, reqVO.getGroupApprovalResult())
                .eqIfPresent(LetterPostDO::getGroupApprovalComments, reqVO.getGroupApprovalComments())
                .eqIfPresent(LetterPostDO::getLeaderApproverSfzh, reqVO.getLeaderApproverSfzh())
                .eqIfPresent(LetterPostDO::getLeaderApproverXm, reqVO.getLeaderApproverXm())
                .betweenIfPresent(LetterPostDO::getLeaderApproverTime, reqVO.getLeaderApproverTime())
                .eqIfPresent(LetterPostDO::getLeaderApprovalResult, reqVO.getLeaderApprovalResult())
                .eqIfPresent(LetterPostDO::getLeaderApprovalComments, reqVO.getLeaderApprovalComments())
                .eqIfPresent(LetterPostDO::getActInstId, reqVO.getActInstId())
                .eqIfPresent(LetterPostDO::getTaskId, reqVO.getTaskId())
                .orderByDesc(LetterPostDO::getAddTime));
    }


    default PageResult<LetterPostDO> getAppLetterPostPage(int pageNo, int pageSize, String jgrybm, Date startTime, Date endTime) {
        Page<LetterPostDO> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapperX<LetterPostDO> wrapper = new LambdaQueryWrapperX<LetterPostDO>()
                .eq(LetterPostDO::getJgrybm, jgrybm);
        if (Objects.nonNull(startTime)) {
            wrapper.ge(LetterPostDO::getApplyTime, startTime);
        }
        if (Objects.nonNull(endTime)) {
            wrapper.lt(LetterPostDO::getApplyTime, endTime);
        }
        wrapper.orderByDesc(LetterPostDO::getApplyTime);

        Page<LetterPostDO> letterPostPage = selectPage(page, wrapper);
        return new PageResult<>(letterPostPage.getRecords(), letterPostPage.getTotal());
    }
}
