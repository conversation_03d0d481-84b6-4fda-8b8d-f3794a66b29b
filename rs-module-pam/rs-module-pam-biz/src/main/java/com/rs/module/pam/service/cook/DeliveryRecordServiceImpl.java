package com.rs.module.pam.service.cook;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.cook.vo.DeliveryRecordSaveReqVO;
import com.rs.module.pam.dao.cook.DeliveryRecordDao;
import com.rs.module.pam.entity.cook.DeliveryRecordDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 监所事务管理-发饭记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DeliveryRecordServiceImpl extends BaseServiceImpl<DeliveryRecordDao, DeliveryRecordDO> implements DeliveryRecordService {

    @Resource
    private DeliveryRecordDao deliveryRecordDao;

    @Override
    public String createDeliveryRecord(DeliveryRecordSaveReqVO createReqVO) {
        // 插入
        DeliveryRecordDO deliveryRecord = BeanUtils.toBean(createReqVO, DeliveryRecordDO.class);
        deliveryRecordDao.insert(deliveryRecord);
        // 返回
        return deliveryRecord.getId();
    }

    private void validateDeliveryRecordExists(String id) {
        if (deliveryRecordDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-发饭记录数据不存在");
        }
    }

    @Override
    public DeliveryRecordDO getDeliveryRecord(String id) {
        return deliveryRecordDao.selectById(id);
    }

    @Override
    public Map<String, DeliveryRecordDO> getDeliveryRecordByCondition(List<String> jgrybmList, String deliveryId) {
        LambdaQueryWrapper<DeliveryRecordDO> lambdaQuery = Wrappers.lambdaQuery(DeliveryRecordDO.class);
        lambdaQuery.select(DeliveryRecordDO::getJgrybm, DeliveryRecordDO::getStatus, DeliveryRecordDO::getId, DeliveryRecordDO::getDeliveryTime)
                .eq(DeliveryRecordDO::getDeliveryId, deliveryId)
                .in(DeliveryRecordDO::getJgrybm, jgrybmList);
        List<DeliveryRecordDO> deliveryRecordDOS = deliveryRecordDao.selectList(lambdaQuery);
        if (CollUtil.isNotEmpty(deliveryRecordDOS)) {
            return deliveryRecordDOS.stream().collect(Collectors.toMap(DeliveryRecordDO::getJgrybm, Function.identity()));
        }
        return Collections.emptyMap();
    }

    @Override
    public DeliveryRecordDO getDeliveryRecordByDeliveryId(String deliveryId, String jgrybm) {
        LambdaQueryWrapper<DeliveryRecordDO> lambdaQuery = Wrappers.lambdaQuery(DeliveryRecordDO.class);
        lambdaQuery.select(DeliveryRecordDO::getId, DeliveryRecordDO::getStatus)
                .eq(DeliveryRecordDO::getDeliveryId, deliveryId)
                .eq(DeliveryRecordDO::getJgrybm, jgrybm);
        return deliveryRecordDao.selectOne(lambdaQuery);
    }


}
