package com.rs.module.pam.controller.admin.attention.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-重点人员关注登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RoomPrisonerRespVO extends BaseVO implements TransPojo{

    private String id;

    private String orgCode;

    private String orgName;

    private String roomId;

    private String roomName;

    private List<PrisonerRespVO> prisonerList;

}
