package com.rs.module.pam.service.integral;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.rs.module.base.dao.pm.PrisonerInDao;
import com.rs.module.pam.controller.admin.integral.vo.RiskModelLevelVO;
import com.rs.module.pam.dao.attention.AttentionPrisonerDao;
import com.rs.module.pam.dao.integral.RiskModelConfigDao;
import com.rs.module.pam.dao.integral.ScoreDetailDao;
import com.rs.module.pam.entity.integral.RiskModelConfigDO;
import com.rs.module.pam.entity.integral.ScoreDetailDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 监所事务管理-积分明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ScoreDetailServiceImpl extends BaseServiceImpl<ScoreDetailDao, ScoreDetailDO> implements ScoreDetailService {

    @Resource
    private ScoreDetailDao scoreDetailDao;
    @Resource
    private RiskModelConfigDao riskModelConfigDao;
    @Resource
    private PrisonerInDao prisonerInDao;
    @Resource
    private AttentionPrisonerDao attentionPrisonerDao;


    @Override
    public Map<String, Object> getOrgScore(String orgCode, String roomCode) {
        Map<String, Object> result = new HashMap<>();
        BigDecimal score = scoreDetailDao.getOrgScore(orgCode);
        String type = "org";
        if (StringUtil.isNotEmpty(roomCode)) {
            type = "room";
        }
        RiskModelConfigDO riskModel = riskModelConfigDao.getByOrgCode(orgCode, type);
        List<RiskModelLevelVO> riskModelLevelVOS = new ArrayList<>();
        if (riskModel != null) {
            riskModelLevelVOS = JSONObject.parseArray(riskModel.getLevelConfig(), RiskModelLevelVO.class);
        }
        result.put("level", riskModelLevelVOS);
        result.put("score", score);
        result.put("treatment", prisonerInDao.getOutTreatmentByOrg(orgCode, roomCode));
        result.put("out", 0);
        result.put("attention", attentionPrisonerDao.getPrisonerCount(orgCode, roomCode));
        result.put("riskPrison", scoreDetailDao.getRiskPrisonerCount(orgCode, roomCode));
        return result;
    }

    @Override
    public List<Map<String, Object>> getSickRoomByOrg(String orgCode) {
        return scoreDetailDao.getSickRoomByOrg(orgCode);
    }

    @Override
    public List<Map<String, Object>> getSickPrisonerByOrg(String orgCode, String roomCode) {
        return scoreDetailDao.getSickPrisonerByOrg(orgCode, roomCode);
    }
}
