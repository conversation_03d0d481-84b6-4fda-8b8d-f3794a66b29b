package com.rs.module.pam.service.duty;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.dto.OrgRespDTO;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.cons.CommonConstants;
import com.rs.module.pam.controller.admin.duty.vo.RoomAutoConfigSaveReqVO;
import com.rs.module.pam.dao.duty.RoomAutoConfigDao;
import com.rs.module.pam.entity.duty.RoomAutoConfigDO;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 监所事务管理-监室自动排班配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RoomAutoConfigServiceImpl extends BaseServiceImpl<RoomAutoConfigDao, RoomAutoConfigDO> implements RoomAutoConfigService {

    @Resource
    private RoomAutoConfigDao roomAutoConfigDao;
    @Resource
    private BspApi bspApi;

    @Override
    public String createRoomAutoConfig(RoomAutoConfigSaveReqVO createReqVO) {
        // 插入
        RoomAutoConfigDO roomAutoConfig = BeanUtils.toBean(createReqVO, RoomAutoConfigDO.class);
        OrgRespDTO org = bspApi.getOrgByCode(createReqVO.getOrgCode());
        roomAutoConfig.setOrgName(org.getName());
        roomAutoConfigDao.insert(roomAutoConfig);
        // 返回
        return roomAutoConfig.getId();
    }

    @Override
    public void updateRoomAutoConfig(RoomAutoConfigSaveReqVO updateReqVO) {
        RoomAutoConfigDO configDO = roomAutoConfigDao.getByRoomId(updateReqVO.getOrgCode(), updateReqVO.getRoomId());

        if (ObjectUtil.isEmpty(configDO)) {
            RoomAutoConfigDO updateObj = BeanUtils.toBean(updateReqVO, RoomAutoConfigDO.class);
            roomAutoConfigDao.insert(updateObj);
        } else {
            configDO.setIsEnabled(updateReqVO.getIsEnabled());
            configDO.setSchedulingRule(updateReqVO.getSchedulingRule());
            roomAutoConfigDao.updateById(configDO);
        }
    }

    @Override
    public void deleteRoomAutoConfig(String id) {
        // 校验存在
        validateRoomAutoConfigExists(id);
        // 删除
        roomAutoConfigDao.deleteById(id);
    }

    private void validateRoomAutoConfigExists(String id) {
        if (roomAutoConfigDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-监室自动排班配置数据不存在");
        }
    }

    @Override
    public RoomAutoConfigDO getRoomAutoConfig(String orgCode, String roomId) {
        RoomAutoConfigDO autoConfigDO = getOne(new LambdaQueryWrapper<RoomAutoConfigDO>()
                .eq(RoomAutoConfigDO::getOrgCode, orgCode)
                .eq(RoomAutoConfigDO::getRoomId, roomId));
        if (ObjectUtil.isEmpty(autoConfigDO)) {
            return createDefaultAutoConfig(orgCode, roomId);
        }
        return autoConfigDO;
    }

    private RoomAutoConfigDO createDefaultAutoConfig(String orgCode, String roomId) {
        OrgRespDTO org = bspApi.getOrgByCode(orgCode);
        RoomAutoConfigDO config = new RoomAutoConfigDO();
        config.setCityCode(org.getCityId());
        config.setCityName(org.getCityName());
        config.setRegCode(org.getRegionId());
        config.setRegName(org.getRegionName());
        config.setOrgCode(orgCode);
        config.setOrgName(org.getName());
        config.setRoomId(roomId);
        config.setIsEnabled((short)1);
        config.setSchedulingRule(CommonConstants.AUTO_CONFIG_01 + "," + CommonConstants.AUTO_CONFIG_02);
        roomAutoConfigDao.insert(config);
        return config;
    }



}
