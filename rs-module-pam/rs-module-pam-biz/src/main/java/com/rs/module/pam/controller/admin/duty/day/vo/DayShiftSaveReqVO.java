package com.rs.module.pam.controller.admin.duty.day.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-值日班次新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DayShiftSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("班次名称")
    @NotEmpty(message = "班次名称不能为空")
    private String shiftName;

    @ApiModelProperty("排序")
    @NotNull(message = "排序不能为空")
    private Integer sort;

}
