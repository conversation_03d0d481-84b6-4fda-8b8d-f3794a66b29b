package com.rs.module.pam.controller.admin.cook.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-菜品管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ManagePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("菜品名称")
    private String cookName;

    @ApiModelProperty("菜品分类ID")
    private String cateId;

    @ApiModelProperty("排序")
    private Integer sort;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
