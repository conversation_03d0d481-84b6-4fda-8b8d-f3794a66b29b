package com.rs.module.pam.entity.cook;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 监所事务管理-发饭主 DO
 *
 * <AUTHOR>
 */
@TableName("pam_cook_delivery")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_cook_delivery")
public class DeliveryDO extends BaseDO {
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监室ID
     */
    private String roomId;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 菜谱日期
     */
    private String cookBookDate;
    /**
     * 监室总人数
     */
    private Integer roomTotalPersons;
    /**
     * 在监总人数
     */
    private Integer custodyTotalPersons;
    /**
     * 外出总人数
     */
    private Integer outgoingTotalPersons;
    /**
     * 已领饭人数
     */
    private Integer mealReceivedTotal;
    /**
     * 未领饭人数
     */
    private Integer mealUnreceivedTotal;
    /**
     * 留饭人数
     */
    private Integer mealReservedTotal;
    /**
     * 配餐时段（0：早餐，1：午餐，2：晚餐）
     */
    private String mealPeriod;
    /**
     * 当日食谱
     */
    private String cookbook;
    /**
     * 发饭时间
     */
    private Date deliveryTime;
    /**
     * 经办民警身份证号
     */
    private String operatePoliceSfzh;
    /**
     * 经办民警
     */
    private String operatePolice;
    /**
     * 经办时间
     */
    private Date operateTime;
    /**
     * 状态（0:发饭中，1：发饭完成）
     */
    private String status;

}
