package com.rs.module.pam.dao.duty;

import com.rs.module.pam.controller.admin.duty.vo.DutyPrisonerVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* 监所事务管理-监室值班 Dao
*
* <AUTHOR>
*/
@Mapper
public interface DutyPrisonerDao {


    /**
     * 监室下在所人员列表
     *
     * @param roomId
     * @return
     */
    List<DutyPrisonerVO> getPrisonerInByRoomId(@Param("orgCode") String orgCode, @Param("roomId") String roomId);

    /**
     * 监室下在所人员列表
     *
     * @param jgrybmList
     * @return
     */
    List<DutyPrisonerVO> getPrisonerInByJgrybms(@Param("jgrybmList") List<String> jgrybmList);

    /**
     * 根据人员编号集合查询人员信息
     *
     * @param jgrybmList
     * @return
     */
    List<DutyPrisonerVO> getPrisonerByJgrybms(@Param("jgrybmList") List<String> jgrybmList);

    /**
     * 查询人员监室调整到该监室时间
     *
     * @param roomId
     * @param startTime
     * @return
     */
    List<DutyPrisonerVO> selectPrisonerEntryRoomTime(@Param("orgCode") String orgCode,
                                                     @Param("roomId") String roomId,
                                                     @Param("startTime") Date startTime);
}
