package com.rs.module.pam.controller.admin.integral.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@ApiModel(description = "风险模型 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RiskModelConfigDTO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("机构编号")
    private String orgCode;
    @ApiModelProperty("机构名称")
    private String orgName;
    @ApiModelProperty("风险模型名称")
    private String name;
    @ApiModelProperty("风险模型类型")
    private String type;
    @ApiModelProperty("等级配置")
    private List<RiskModelLevelVO> levelConfigs;
    @ApiModelProperty("权重配置")
    private List<IndicatorRespVO> indicatorList;
    @ApiModelProperty("排序")
    private Integer orderId;
}
