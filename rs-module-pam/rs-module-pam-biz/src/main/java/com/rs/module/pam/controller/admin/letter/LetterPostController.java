package com.rs.module.pam.controller.admin.letter;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.pam.controller.admin.letter.vo.*;
import com.rs.module.pam.entity.letter.LetterPostDO;
import com.rs.module.pam.service.letter.LetterPostService;

@Api(tags = "监所事务管理-家属通信-寄信登记")
@RestController
@RequestMapping("/pam/letter/letterPost")
@Validated
public class LetterPostController {

    @Resource
    private LetterPostService letterPostService;

    /*@PostMapping("/create")
    @ApiOperation(value = "创建监所事务管理-家属通信-寄信登记")
    public CommonResult<String> createLetterPost(@Valid @RequestBody LetterPostSaveReqVO createReqVO) {
        return success(letterPostService.createLetterPost(createReqVO));
    }*/

    @PostMapping("/update")
    @ApiOperation(value = "更新监所事务管理-家属通信-寄信登记")
    public CommonResult<Boolean> updateLetterPost(@Valid @RequestBody LetterPostSaveReqVO updateReqVO) {
        letterPostService.updateLetterPost(updateReqVO);
        return success(true);
    }

    @PostMapping("/approval")
    @ApiOperation(value = "审批监所事务管理-家属通信-寄信登记审批")
    public CommonResult<Boolean> approvalLetterPost(@Valid @RequestBody LetterPostApprovalReqVO letterPostApprovalReqVO) {
        letterPostService.approvalLetterPost(letterPostApprovalReqVO);
        return success(true);
    }

    @PostMapping("/post")
    @ApiOperation(value = "监所事务管理-家属通信-寄信登记-寄出")
    public CommonResult<Boolean> letterPost(@Valid @RequestBody LetterPostReqVO letterPostReqVO) {
        letterPostService.letterPost(letterPostReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除监所事务管理-家属通信-寄信登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteLetterPost(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           letterPostService.deleteLetterPost(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得监所事务管理-家属通信-寄信登记")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<LetterPostRespVO> getLetterPost(@RequestParam("id") String id) {
        LetterPostDO letterPost = letterPostService.getLetterPost(id);
        return success(BeanUtils.toBean(letterPost, LetterPostRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得监所事务管理-家属通信-寄信登记分页")
    public CommonResult<PageResult<LetterPostRespVO>> getLetterPostPage(@Valid @RequestBody LetterPostPageReqVO pageReqVO) {
        PageResult<LetterPostDO> pageResult = letterPostService.getLetterPostPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LetterPostRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得监所事务管理-家属通信-寄信登记列表")
    public CommonResult<List<LetterPostRespVO>> getLetterPostList(@Valid @RequestBody LetterPostListReqVO listReqVO) {
        List<LetterPostDO> list = letterPostService.getLetterPostList(listReqVO);
        return success(BeanUtils.toBean(list, LetterPostRespVO.class));
    }
}
