package com.rs.module.pam.service.cook;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.cook.vo.DeliveryRecordSaveReqVO;
import com.rs.module.pam.entity.cook.DeliveryRecordDO;

import java.util.List;
import java.util.Map;

/**
 * 监所事务管理-发饭记录 Service 接口
 *
 * <AUTHOR>
 */
public interface DeliveryRecordService extends IBaseService<DeliveryRecordDO>{

    /**
     * 创建监所事务管理-发饭记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createDeliveryRecord(DeliveryRecordSaveReqVO createReqVO);

    /**
     * 获得监所事务管理-发饭记录
     *
     * @param id 编号
     * @return 监所事务管理-发饭记录
     */
    DeliveryRecordDO getDeliveryRecord(String id);

    /**
     *
     * @param jgrybmList
     * @param deliveryId
     * @return
     */
    Map<String, DeliveryRecordDO> getDeliveryRecordByCondition(List<String> jgrybmList, String deliveryId);

    /**
     * 根据配送单号和监管人员编码获取发饭记录
     * @param deliveryId
     * @param jgrybm
     * @return
     */
    DeliveryRecordDO getDeliveryRecordByDeliveryId(String deliveryId, String jgrybm);

}
