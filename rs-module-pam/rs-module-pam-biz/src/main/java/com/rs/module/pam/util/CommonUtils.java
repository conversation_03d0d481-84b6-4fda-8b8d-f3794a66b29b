package com.rs.module.pam.util;

import com.rs.module.pam.enums.CommonAppRecordPeriodTypeEnum;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class CommonUtils {

    /**
     *  根据CommonAppRecordPeriodTypeEnum枚举类型获取 1 全部，2 今天，3 昨天，4 近一周 的开始时间和结束时间
     * @param type
     * @return
     */
    public static Map<String, Date> getCommonAppRecordPeriod(String type){
        CommonAppRecordPeriodTypeEnum recordTypeEnum = CommonAppRecordPeriodTypeEnum.getByCode(type);
        if (Objects.isNull(recordTypeEnum)) {
            throw new RuntimeException("非法type类型");
        }
        //类型 1 全部，2 今天，3 昨天，4 近一周
        Date startTime = null;
        Date endTime = null;
        if (CommonAppRecordPeriodTypeEnum.JT.getCode().equals(type)) {
            LocalDate startLocal = LocalDate.now(); // 获取当前日期 yyyy-MM-dd
            LocalDate endLocal = LocalDate.now().plusDays(1L);
            startTime = Date.from(startLocal.atStartOfDay(ZoneId.systemDefault()).toInstant());
            endTime = Date.from(endLocal.atStartOfDay(ZoneId.systemDefault()).toInstant());
        } else if (CommonAppRecordPeriodTypeEnum.ZT.getCode().equals(type)) {
            LocalDate startLocal = LocalDate.now().plusDays(-1L);
            LocalDate endLocal = LocalDate.now();
            startTime = Date.from(startLocal.atStartOfDay(ZoneId.systemDefault()).toInstant());
            endTime = Date.from(endLocal.atStartOfDay(ZoneId.systemDefault()).toInstant());

        } else if (CommonAppRecordPeriodTypeEnum.JYZ.getCode().equals(type)) {
            LocalDate startLocal = LocalDate.now().plusDays(-6L);
            LocalDate endLocal = LocalDate.now().plusDays(1L);
            startTime = Date.from(startLocal.atStartOfDay(ZoneId.systemDefault()).toInstant());
            endTime = Date.from(endLocal.atStartOfDay(ZoneId.systemDefault()).toInstant());
        }
        Map<String, Date> result = new HashMap<>(2);
        result.put("startTime", startTime);
        result.put("endTime", endTime);
        return result;
    }
}
