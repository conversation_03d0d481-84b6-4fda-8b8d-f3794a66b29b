package com.rs.module.pam.controller.app;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.letter.vo.LetterReceiveRespVO;
import com.rs.module.pam.controller.app.vo.AppConfirmLetterReqVO;
import com.rs.module.pam.entity.letter.LetterReceiveDO;
import com.rs.module.pam.service.letter.LetterReceiveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监所事务管理-家属通信-收信登记")
@RestController
@RequestMapping("/app/pam/letter/letterReceive")
@Validated
public class AppLetterReceiveController {

    @Resource
    private LetterReceiveService letterReceiveService;


    @PostMapping("/confirm")
    @ApiOperation(value = "App-监所事务管理-家属通信-收信确认")
    public CommonResult<Boolean> appConfirmLetter(@Valid @RequestBody AppConfirmLetterReqVO appConfirmLetterReqVO) {
        letterReceiveService.appConfirmLetter(appConfirmLetterReqVO);
        return success(true);
    }

    @GetMapping("/page")
    @ApiOperation(value = "App-收信管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小"),
            @ApiImplicitParam(name = "jgrybm", value = "被监管人员编码"),
            @ApiImplicitParam(name = "type", value = "收信查询类型 1 全部，2 待确认，3 已确认")
    })
    public CommonResult<PageResult<LetterReceiveRespVO>> getAppLetterReceivePage(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                                                 @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                                                 @RequestParam("jgrybm") String jgrybm,
                                                                                 @RequestParam("type") String type) {
        PageResult<LetterReceiveDO> pageResult = letterReceiveService.getAppLetterReceivePage(pageNo, pageSize, jgrybm, type);
        return success(BeanUtils.toBean(pageResult, LetterReceiveRespVO.class));
    }

}
