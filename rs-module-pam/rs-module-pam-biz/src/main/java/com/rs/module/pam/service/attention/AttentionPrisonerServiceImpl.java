package com.rs.module.pam.service.attention;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.dto.OrgRespDTO;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.cons.CommonConstants;
import com.rs.module.pam.controller.admin.attention.vo.*;
import com.rs.module.pam.dao.attention.AttentionFeedbackDao;
import com.rs.module.pam.dao.attention.AttentionPrisonerDao;
import com.rs.module.pam.entity.attention.FeedbackDO;
import com.rs.module.pam.entity.attention.PrisonerDO;
import com.rs.module.base.util.BspApprovalUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 监所事务管理-重点人员关注登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AttentionPrisonerServiceImpl extends BaseServiceImpl<AttentionPrisonerDao, PrisonerDO> implements AttentionPrisonerService {

    @Resource
    private AttentionPrisonerDao attentionPrisonerDao;
    @Resource
    private AttentionFeedbackDao attentionFeedbackDao;
    @Resource
    private BspApi bspApi;
    private static final String defKey = "attention11010020250512000001";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createPrisoner(PrisonerSaveReqVO createReqVO) {
        // 插入
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        PrisonerDO prisoner = BeanUtils.toBean(createReqVO, PrisonerDO.class);
        prisoner.setRegStatus(CommonConstants.PRISONER_ARCHIVE_STATUS_WAIT);
        prisoner.setRegOperatorSfzh(sessionUser.getIdCard());
        prisoner.setRegOperatorXm(sessionUser.getName());
        prisoner.setRegTime(new Date());
        attentionPrisonerDao.insert(prisoner);

        String msgUrl = String.format("/#/roomManage/keypersonnel?id=%s&type=examine", prisoner.getId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", prisoner.getId());
//        variables.put("busType", BusTypeEnum.EVENT.getCode());
        JSONObject result = BspApprovalUtil.defaultStartProcess(defKey, prisoner.getId(), null, msgUrl, variables);
        JSONObject data = result.getJSONObject("data");
        JSONObject bpmTrail = data.getJSONObject("bpmTrail");
        prisoner.setActInstId(bpmTrail.getString("actInstId"));
        prisoner.setTaskId(bpmTrail.getString("taskId"));
        attentionPrisonerDao.updateById(prisoner);
        // 返回
        return prisoner.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePrisoner(PrisonerSaveReqVO updateReqVO) {
        // 校验存在
        validatePrisonerExists(updateReqVO.getId());
        // 更新
        PrisonerDO updateObj = BeanUtils.toBean(updateReqVO, PrisonerDO.class);
        attentionPrisonerDao.updateById(updateObj);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePrisoner(String id) {
        // 校验存在
        validatePrisonerExists(id);
        // 删除
        attentionPrisonerDao.deleteById(id);

        // 删除子表
        deleteFeedbackByAttentionId(id);
    }

    private void validatePrisonerExists(String id) {
        if (attentionPrisonerDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-重点人员关注登记数据不存在");
        }
    }

    @Override
    public PrisonerDO getPrisoner(String id) {
        return attentionPrisonerDao.selectById(id);
    }

    @Override
    public List<RoomPrisonerRespVO> getPrisonerByOrgCode(String orgCode) {
        List<RoomPrisonerRespVO> roomPrisonerList = new ArrayList<>();
        List<PrisonerRespVO> allPrisonerList = attentionPrisonerDao.getPrisonerByOrgCode(orgCode);
        if (CollectionUtil.isNull(allPrisonerList)) {
            return roomPrisonerList;
        }
        Map<String, List<PrisonerRespVO>> roomPrisoner = allPrisonerList.stream()
                .collect(Collectors.groupingBy(prisoner -> prisoner.getRoomId()));
        for (String roomId : roomPrisoner.keySet()) {
            RoomPrisonerRespVO roomPrisonerRespVO = new RoomPrisonerRespVO();
            List<PrisonerRespVO> prisonerList = roomPrisoner.get(roomId);
            roomPrisonerRespVO.setOrgCode(prisonerList.get(0).getOrgCode());
            roomPrisonerRespVO.setOrgName(prisonerList.get(0).getOrgName());
            roomPrisonerRespVO.setRoomId(roomId);
            roomPrisonerRespVO.setRoomName(prisonerList.get(0).getRoomName());
            roomPrisonerRespVO.setPrisonerList(prisonerList);
            roomPrisonerList.add(roomPrisonerRespVO);
        }
        return roomPrisonerList;
    }

    @Override
    public PageResult<PrisonerDO> getPrisonerPage(PrisonerPageReqVO pageReqVO) {
        return attentionPrisonerDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrisonerDO> getPrisonerList(PrisonerListReqVO listReqVO) {
        return attentionPrisonerDao.selectList(listReqVO);
    }

    @Override
    public void approvalProcess(String id, String approvalResult, String approvalComments) {
        PrisonerDO prisoner = attentionPrisonerDao.selectById(id);
        String msgTit = "";
        String msgUrl = "";

        // 判断流程审批状态
        BspApproceStatusEnum statusEnum = BspApproceStatusEnum.PASSED;
        Map<String, Object> variables = new HashMap<>();
        String result = CommonConstants.PRISONER_ARCHIVE_STATUS_PASSED;

        String comments = "通过";
        if (CommonConstants.PRISONER_ARCHIVE_STATUS_NOT_PASSED.equals(approvalResult)) {
                comments = "不通过";
            statusEnum = BspApproceStatusEnum.NOT_PASSED;
            result = CommonConstants.PRISONER_ARCHIVE_STATUS_NOT_PASSED;
            msgUrl = String.format("/#/roomManage/keypersonnel?id=%s&type=examine", prisoner.getId());
//            variables.put("eventTypeName", DicUtils.translate(CommonConstants.DIC_ZD_EVENT_SJLX, prisoner.getEventType()));
            variables.put("ywbh", id);
//            variables.put("busType", BusTypeEnum.EVENT_BACK.getCode());
        }
        // bsp审批流程必须要有审批意见
        if (StringUtil.isNullBlank(approvalComments)) {
            approvalComments = comments;
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        // 获取流程审批返回的流程实例ID和任务ID，并填充审批人信息
        JSONObject processResult = BspApprovalUtil.defaultApprovalProcess(defKey, prisoner.getActInstId(),
                prisoner.getTaskId(), id, statusEnum, approvalComments, msgTit, msgUrl, variables);
        JSONObject data = processResult.getJSONObject("data");
        JSONObject bpmTrail = data.getJSONObject("bpmTrail");
        prisoner.setId(id);
        prisoner.setActInstId(bpmTrail.getString("actInstId"));
        prisoner.setTaskId(bpmTrail.getString("taskId"));
        prisoner.setRegStatus(result);
        prisoner.setLeaderApprovalComments(approvalComments);
        prisoner.setLeaderApproverXm(sessionUser.getName());
        prisoner.setLeaderApproverSfzh(sessionUser.getIdCard());
        prisoner.setLeaderApproverTime(new Date());
        prisoner.setLeaderApprovalResult(approvalResult);
        attentionPrisonerDao.updateById(prisoner);
    }


    // ==================== 子表（监所事务管理-重点人员关注反馈） ====================

    @Override
    public List<FeedbackDO> getFeedbackListByAttentionId(String attentionId) {
        return attentionFeedbackDao.selectListByAttentionId(attentionId);
    }

    @Override
    public String createFeedback(FeedbackSaveReqVO createReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        FeedbackDO feedback = BeanUtils.toBean(createReqVO, FeedbackDO.class);
        feedback.setOperatorSfzh(sessionUser.getIdCard());
        feedback.setOperatorXm(sessionUser.getName());
        feedback.setOperatorTime(new Date());
        attentionFeedbackDao.insert(feedback);
        return feedback.getId();
    }

    @Override
    public Integer getPrisonerCount(String orgCode, String roomCode) {
        return attentionPrisonerDao.getPrisonerCount(orgCode, roomCode);
    }

    private void deleteFeedbackByAttentionId(String attentionId) {
        attentionFeedbackDao.deleteByAttentionId(attentionId);
    }

}
