package com.rs.module.pam.controller.admin.information.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-监室信息发布新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PublishSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("标题")
    @NotEmpty(message = "标题不能为空")
    private String title;

    @ApiModelProperty("信息类型（字典：ZD_JSXXFB_XXLX）")
    @NotEmpty(message = "信息类型（字典：ZD_JSXXFB_XXLX）不能为空")
    private String infoType;

    @ApiModelProperty("信息内容")
    @NotEmpty(message = "信息内容不能为空")
    private String infoContentText;

    @ApiModelProperty("信息内容")
    @NotEmpty(message = "信息内容不能为空")
    private String infoContentHtml;

    @ApiModelProperty("登记经办人")
    private String operatorSfzh;

    @ApiModelProperty("登记经办人姓名")
    private String operatorXm;

    @ApiModelProperty("登记时间")
    private Date operatorTime;

    @ApiModelProperty("登记状态")
    private String status;

    @ApiModelProperty("业务类型 01 监室信息发布  02 教育读本发布")
    private String bizType;

}
