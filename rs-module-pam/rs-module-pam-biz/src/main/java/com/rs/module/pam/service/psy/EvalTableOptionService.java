package com.rs.module.pam.service.psy;

import java.util.*;
import javax.validation.*;
import com.rs.module.pam.controller.admin.psy.vo.*;
import com.rs.module.pam.entity.psy.EvalTableOptionDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 监所事务管理-心理测评量表关联提目选项 Service 接口
 *
 * <AUTHOR>
 */
public interface EvalTableOptionService extends IBaseService<EvalTableOptionDO>{

    /**
     * 创建监所事务管理-心理测评量表关联提目选项
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createEvalTableOption(@Valid EvalTableOptionSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-心理测评量表关联提目选项
     *
     * @param updateReqVO 更新信息
     */
    void updateEvalTableOption(@Valid EvalTableOptionSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-心理测评量表关联提目选项
     *
     * @param id 编号
     */
    void deleteEvalTableOption(String id);

    /**
     * 获得监所事务管理-心理测评量表关联提目选项
     *
     * @param id 编号
     * @return 监所事务管理-心理测评量表关联提目选项
     */
    EvalTableOptionDO getEvalTableOption(String id);

    /**
    * 获得监所事务管理-心理测评量表关联提目选项分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-心理测评量表关联提目选项分页
    */
    PageResult<EvalTableOptionDO> getEvalTableOptionPage(EvalTableOptionPageReqVO pageReqVO);

    /**
    * 获得监所事务管理-心理测评量表关联提目选项列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-心理测评量表关联提目选项列表
    */
    List<EvalTableOptionDO> getEvalTableOptionList(EvalTableOptionListReqVO listReqVO);

    /**
     * 删除监所事务管理-心理测评量表关联提目选项
     * @param questionId
     */
    void deleteEvalTableOptionByQuestionId(String questionId);

    /**
     * 查询题目选项
     * @param questionId
     * @return
     */
    List<EvalTableOptionRespVO> getListByQuestionId(String questionId);
    /**
     * 查询题目选项
     * @param questionIds
     * @return
     */
    List<EvalTableOptionRespVO> getListByQuestionId(List<String> questionIds);

}
