package com.rs.module.pam.controller.admin.daily.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/5/28 20:03
 */
@Data
@ApiModel(description = "管理后台 - 安全检查-管教民警整改 Request VO")
public class CleanPoliceRectificationVO {


    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("整改时间")
    @NotNull(message = "整改时间不能为空")
    private Date rectificationrTime;
    @ApiModelProperty("整改情况")
    @NotBlank(message = "整改情况不能为空")
    private String rectificationrContent;

}
