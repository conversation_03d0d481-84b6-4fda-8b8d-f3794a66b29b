package com.rs.module.pam.controller.app.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.pam.controller.admin.duty.vo.DutyPrisonerVO;
import com.rs.module.pam.controller.admin.duty.vo.RecordsSigninRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-值班 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AppDutyShiftRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("班次id")
    private String id;
    @ApiModelProperty("班次名称")
    private String shiftName;
    @ApiModelProperty("开始时间")
    private String startTime;
    @ApiModelProperty("结束时间")
    private String endTime;
    @ApiModelProperty("签到有效期")
    private Short signinValidityPeriod;
    @ApiModelProperty("排序")
    private Integer sort;
    @ApiModelProperty("值班人员信息")
    private List<DutyPrisonerVO> prisonerList;
    @ApiModelProperty("签到记录")
    private List<RecordsSigninRespVO> singinList;
}
