package com.rs.module.pam.entity.attention;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import java.util.Date;

/**
 * 监所事务管理-重点人员关注登记 DO
 *
 * <AUTHOR>
 */
@TableName("pam_attention_prisoner")
@KeySequence("pam_attention_prisoner_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrisonerDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 开始关注日期
     */
    private Date beginTime;
    /**
     * 结束关注日期
     */
    private Date endTime;
    /**
     * 关注理由
     */
    private String attentionReason;
    /**
     * 关注频率类型（字典：ZD_ZDRYGZ_GZPLLX）
     */
    private String frequencyType;
    /**
     * 关注频率
     */
    private Integer frequency;
    /**
     * 关注层级（字典：ZD_ZDRYGZ_GZCJ）
     */
    private String attentionLevel;
    /**
     * 推送岗位，多个逗号分割
     */
    private String pushJobPositions;
    /**
     * 登记状态（字典：ZD_ZDRYGZ_GZZT）
     */
    private String regStatus;
    /**
     * 登记经办人
     */
    private String regOperatorSfzh;
    /**
     * 登记经办人姓名
     */
    private String regOperatorXm;
    /**
     * 登记时间
     */
    private Date regTime;
    /**
     * 所领导审批人身份证号
     */
    private String leaderApproverSfzh;
    /**
     * 所领导审批人姓名
     */
    private String leaderApproverXm;
    /**
     * 所领导审批时间
     */
    private Date leaderApproverTime;
    /**
     * 所领导审批结果
     */
    private String leaderApprovalResult;
    /**
     * 所领导审核意见
     */
    private String leaderApprovalComments;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

}
