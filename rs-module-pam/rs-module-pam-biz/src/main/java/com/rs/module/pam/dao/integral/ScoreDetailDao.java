package com.rs.module.pam.dao.integral;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.pam.entity.integral.ScoreDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
* 监所事务管理-积分明细 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ScoreDetailDao extends IBaseDao<ScoreDetailDO> {

    /**
     * 获取监所积分
     * @param orgCode
     * @return
     */
    BigDecimal getOrgScore(@Param("orgCode") String orgCode);

    /**
     * 获取风险人员数量
     * @param orgCode
     * @return
     */
    Integer getRiskPrisonerCount(@Param("orgCode") String orgCode, @Param("roomCode") String roomCode);

    /**
     * 获取监室风险分布情况
     * @param orgCode
     * @return
     */
    List<Map<String, Object>> getSickRoomByOrg(@Param("orgCode") String orgCode);

    /**
     * 获取人员风险分布情况
     * @param orgCode
     * @return
     */
    List<Map<String, Object>> getSickPrisonerByOrg(@Param("orgCode") String orgCode, @Param("roomCode") String roomCode);

}
