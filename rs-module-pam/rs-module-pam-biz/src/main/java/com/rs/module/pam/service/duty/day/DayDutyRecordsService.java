package com.rs.module.pam.service.duty.day;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.entity.duty.day.DayDutyRecordsDO;

import java.util.Date;
import java.util.List;

/**
 * 监所事务管理-监室值日记录 Service 接口
 *
 * <AUTHOR>
 */
public interface DayDutyRecordsService extends IBaseService<DayDutyRecordsDO>{

    /**
     * 获取值日记录列表
     * @param roomId
     * @param startDate
     * @param endDate
     * @return
     */
    List<DayDutyRecordsDO> selectList(String orgCode, String roomId, Date startDate, Date endDate);

}
