package com.rs.module.pam.controller.admin.duty.day.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-监室值日列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DayDutySaveVO extends BaseVO {

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 监室编号
     */
    private String roomId;

    /**
     * 值日列表
     */
    private List<DayDutyListRespVO> dutyList;

}
