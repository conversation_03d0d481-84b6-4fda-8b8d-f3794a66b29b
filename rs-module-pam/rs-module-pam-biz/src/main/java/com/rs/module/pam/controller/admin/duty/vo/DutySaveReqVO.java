package com.rs.module.pam.controller.admin.duty.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-监室值班新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DutySaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监室id")
    @NotEmpty(message = "监室id不能为空")
    private String roomId;

    @ApiModelProperty("值班日期")
    @NotNull(message = "值班日期不能为空")
    private Date dutyDate;

    @ApiModelProperty("班次数")
    private Short shiftNumber;

    @ApiModelProperty("排班人ID")
    @NotEmpty(message = "排班人ID不能为空")
    private String assignerUserSfzh;

    @ApiModelProperty("排班人名称")
    @NotEmpty(message = "排班人名称不能为空")
    private String assignerUserName;

    @ApiModelProperty("排班时间")
    @NotNull(message = "排班时间不能为空")
    private Date assignedTime;

}
