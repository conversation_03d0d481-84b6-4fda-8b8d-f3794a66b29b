package com.rs.module.pam.controller.app;

import cn.hutool.core.date.DateUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.base.service.pm.AreaService;
import com.rs.module.pam.controller.admin.duty.vo.RecordsSigninRespVO;
import com.rs.module.pam.controller.app.vo.AppDutyRespVO;
import com.rs.module.pam.controller.app.vo.AppDutyShiftRespVO;
import com.rs.module.pam.controller.app.vo.AppDutySigninReqVO;
import com.rs.module.pam.service.duty.DutyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "值班管理-监室值班-仓内屏")
@RestController
@RequestMapping("/app/pam/public/duty/")
@Validated
public class AppDutyController {

    @Resource
    private DutyService dutyService;
    @Resource
    private AreaService areaService;

    @RequestMapping(value = "/dutyRecords", method = RequestMethod.GET)
    @ApiOperation(value = "获取排班记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "roomId", value = "监室编号", required = true, dataType = "String", paramType = "query"),
    })
    public CommonResult<List<AppDutyRespVO>> dutyRecords(@RequestParam("orgCode") String orgCode,
                                                         @RequestParam("roomId") String roomId) {
        Date now = new Date();
        Date startDate = DateUtil.beginOfWeek(now).toJdkDate();
        Date endDate = DateUtil.endOfWeek(now).toJdkDate();
        List<AppDutyRespVO> records = dutyService.appDutyRecords(orgCode, roomId, startDate, endDate);
        return success(records);
    }

    /**
     * 仓内屏使用
     * 监室值班-获取当前值班详情
     */
    @RequestMapping(value = "/getNowShiftDetail", method = RequestMethod.GET)
    @ApiOperation(value = "监室值班-获取当前值班详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "roomId", value = "监室编号", required = true, dataType = "String", paramType = "query"),
    })
    public CommonResult<AppDutyShiftRespVO> getNowShiftDetail(@RequestParam("orgCode") String orgCode,
                                                              @RequestParam("roomId") String roomId) {
        return success(dutyService.getNowShiftDetail(orgCode, roomId));
    }

    /**
     * 仓内屏使用
     * 监室值班-签到
     */
    @RequestMapping(value = "/signin/save", method = RequestMethod.POST)
    @ApiOperation(value = "监室值班-签到")
    @BusTrace(busType = BusTypeEnum.YEWU_JSZB, condition = "false", content = "{\"机构编号\":\"{{#reqVO.orgCode}}\"," +
            "\"监室编号\":\"{{#reqVO.roomId}}\",\"签到人员编码\":\"{{#reqVO.jgrybm}}\"}")
    public CommonResult<List<RecordsSigninRespVO>> signinSave(@RequestBody AppDutySigninReqVO reqVO) {
        return success(dutyService.signinSave(reqVO));
    }


}
