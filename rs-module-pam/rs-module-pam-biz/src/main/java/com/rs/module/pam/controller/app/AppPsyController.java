package com.rs.module.pam.controller.app;

import com.rs.framework.common.enums.DataSourceAppEnum;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanPushRecordAnswerSaveReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTablePrewSimpleRespVO;
import com.rs.module.pam.controller.app.vo.AppPsyTableRecordRespVO;
import com.rs.module.pam.service.psy.EvalPlanPushRecordAnswerService;
import com.rs.module.pam.service.psy.EvalPlanPushRecordService;
import com.rs.module.pam.service.psy.EvalTableService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @Date 2025/6/16 23:59
 */
@Api(tags = "仓内屏-心理测评")
@RestController
@RequestMapping("/app/pam/psy")
@Validated
public class AppPsyController {

    @Resource
    private EvalTableService evalTableService;
    @Resource
    private EvalPlanPushRecordService evalPlanPushRecordService;
    @Resource
    private EvalPlanPushRecordAnswerService evalPlanPushRecordAnswerService;

    @PostMapping("/tableList")
    @ApiOperation(value = "心理测评-获取被监管人员测评量表", response = AppPsyTableRecordRespVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码"),
            @ApiImplicitParam(name = "tableType", value = "量表类型：抑郁量表：1，暴力量表：2，调查问卷：3，投票调查：4，其他：99", required = false)
    })
    public CommonResult tableList(@RequestParam("jgrybm") String jgrybm,
                                  @RequestParam(value = "tableType", required = false) String tableType) {
        List<AppPsyTableRecordRespVO> list = evalPlanPushRecordService.tableList(jgrybm, tableType);
        return success(list);
    }

    @PostMapping("/tablePreviewSimple")
    @ApiOperation(value = "心理测评-量表预览-极简模式", response = EvalTablePrewSimpleRespVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tableId", value = "量表ID"),
            @ApiImplicitParam(name = "sortOrder", value = "题号,默认1开始")
    })
    public CommonResult tablePreviewSimple(@RequestParam("tableId") String tableId, @RequestParam("sortOrder") Integer sortOrder) {
        return success(evalTableService.tablePreviewSimple(tableId));
    }

    @PostMapping("/create")
    @ApiOperation(value = "心理测评-创建-心理测评答题卡")
    public CommonResult<String> createEvalPlanPushRecordAnswer(@Valid @RequestBody EvalPlanPushRecordAnswerSaveReqVO createReqVO) {
        createReqVO.setDataSources(DataSourceAppEnum.CNP.getCode());
        return success(evalPlanPushRecordAnswerService.createEvalPlanPushRecordAnswer(createReqVO));
    }

}
