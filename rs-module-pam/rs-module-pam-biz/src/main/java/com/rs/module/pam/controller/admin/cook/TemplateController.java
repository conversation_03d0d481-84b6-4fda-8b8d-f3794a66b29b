package com.rs.module.pam.controller.admin.cook;

import cn.hutool.core.util.StrUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.cook.vo.TemplateListReqVO;
import com.rs.module.pam.controller.admin.cook.vo.TemplateListRespVO;
import com.rs.module.pam.controller.admin.cook.vo.TemplateRespVO;
import com.rs.module.pam.controller.admin.cook.vo.TemplateSaveReqVO;
import com.rs.module.pam.entity.cook.TemplateDO;
import com.rs.module.pam.service.cook.TemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监室配餐-菜谱管理")
@RestController
@RequestMapping("/pam/cook/template")
@Validated
public class TemplateController {

    @Resource
    private TemplateService templateService;

    @PostMapping("/create")
    @ApiOperation(value = "创建-菜品模板")
    public CommonResult<String> createTemplate(@Valid @RequestBody TemplateSaveReqVO createReqVO) {
        return success(templateService.createTemplate(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新-菜品模板")
    public CommonResult<Boolean> updateTemplate(@Valid @RequestBody TemplateSaveReqVO updateReqVO) {
        templateService.updateTemplate(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除-菜品模板")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteTemplate(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            templateService.deleteTemplate(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得-菜品模板")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<TemplateRespVO> getTemplate(@RequestParam("id") String id) {
        return success(templateService.getTemplate(id));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得-菜品模板列表")
    public CommonResult<List<TemplateListRespVO>> getTemplateList(@RequestBody TemplateListReqVO listReqVO) {
        listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        listReqVO.setIsCopy("0");
        List<TemplateDO> list = templateService.getTemplateList(listReqVO);
        List<TemplateListRespVO> respVOList = new LinkedList<>();
        for (TemplateDO templateDO : list) {
            if (StrUtil.isBlank(templateDO.getWeekNo())) {
                TemplateListRespVO respVO = BeanUtils.toBean(templateDO, TemplateListRespVO.class);
                respVOList.add(respVO);
            }
        }
        return success(respVOList);
    }

    @PostMapping("/multiplexing")
    @ApiOperation(value = "监室配餐-菜谱复用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "weekNo", value = "周编号"),
            @ApiImplicitParam(name = "tempId", value = "模板ID"),
            @ApiImplicitParam(name = "weekDate", value = "日期")
    })
    public CommonResult multiplexing(@RequestParam("weekNo") String weekNo,
                                     @RequestParam("tempId") String tempId,
                                     @RequestParam("weekDate") @NotBlank(message = "日期不能为空") String weekDate) {
        templateService.multiplexing(weekNo, weekDate, tempId);
        return success();
    }

    @GetMapping("/getIndexList")
    @ApiOperation(value = "监室配餐-一周食谱首页列表")
    public CommonResult<Map<String, Object>> getIndexList() {
        String orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        Map<String, Object> result = templateService.getIndexList(orgCode);
        return success(result);
    }
}
