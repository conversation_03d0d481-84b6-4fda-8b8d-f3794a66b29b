package com.rs.module.pam.entity.psy;

import lombok.*;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-心理测评计划推送记录答案 DO
 *
 * <AUTHOR>
 */
@TableName("pam_psy_eval_plan_push_record_answer")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_psy_eval_plan_push_record_answer")
public class EvalPlanPushRecordAnswerDO extends BaseDO {
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 测评编号
     */
    private String evalNo;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 提交时间
     */
    private Date submitTime;
    /**
     * 参考答案，选择题答案保存选项代码，多选题多个答案逗号分割，简答题保存答案
     */
    private String submitAnswer;
    /**
     * 测评得分
     */
    private BigDecimal score;
    /**
     * 测评结果
     */
    private String evalResults;
    /**
     * 作答用时
     */
    private LocalTime timeSpentAnswer;
    /**
     * 提交状态
     */
    private String status;
    /**
     * 数据来源
     */
    private String dataSources;

}
