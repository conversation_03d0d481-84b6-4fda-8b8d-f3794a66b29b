package com.rs.module.pam.service.bed;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.module.pam.dao.bed.PrisonerChangeDao;
import com.rs.module.pam.entity.bed.PrisonerChangeDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


/**
 * 监所事务管理-人员床位调整 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrisonerChangeServiceImpl extends BaseServiceImpl<PrisonerChangeDao, PrisonerChangeDO> implements PrisonerChangeService {

    @Resource
    private PrisonerChangeDao prisonerChangeDao;



}
