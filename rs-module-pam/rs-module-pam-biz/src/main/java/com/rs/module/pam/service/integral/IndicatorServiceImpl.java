package com.rs.module.pam.service.integral;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.integral.vo.IndicatorRespVO;
import com.rs.module.pam.controller.admin.integral.vo.IndicatorSaveReqVO;
import com.rs.module.pam.dao.integral.IndicatorDao;
import com.rs.module.pam.entity.integral.IndicatorDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;


/**
 * 监所事务管理-积分指标 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class IndicatorServiceImpl extends BaseServiceImpl<IndicatorDao, IndicatorDO> implements IndicatorService {

    @Resource
    private IndicatorDao indicatorDao;

    @Override
    public IndicatorRespVO createIndicator(IndicatorSaveReqVO createReqVO) {
        // 插入
        IndicatorDO indicator = BeanUtils.toBean(createReqVO, IndicatorDO.class);
        indicatorDao.insert(indicator);
        // 返回
        return BeanUtils.toBean(indicator, IndicatorRespVO.class);
    }

    @Override
    public IndicatorRespVO updateIndicator(IndicatorSaveReqVO updateReqVO) {
        // 校验存在
        validateIndicatorExists(updateReqVO.getId());
        // 更新
        IndicatorDO updateObj = BeanUtils.toBean(updateReqVO, IndicatorDO.class);
        indicatorDao.updateById(updateObj);
        return BeanUtils.toBean(updateObj, IndicatorRespVO.class);
    }

    @Override
    public void deleteIndicator(String id) {
        // 校验存在
        validateIndicatorExists(id);
        // 删除
        indicatorDao.deleteById(id);
    }

    private void validateIndicatorExists(String id) {
        if (indicatorDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-积分指标数据不存在");
        }
    }

    @Override
    public IndicatorDO getIndicator(String id) {
        return indicatorDao.selectById(id);
    }

    @Override
    public List<IndicatorDO> getIndicatorByOrgCode(String orgCode) {
        if (StringUtil.isNullBlank(orgCode)) {
            return Collections.emptyList();
        }
        return indicatorDao.getIndicatorByOrgCode(orgCode);
    }

    @Override
    public List<IndicatorDO> getIndicatorTypeByRiskModel(String riskModelId) {
        if (StringUtil.isNullBlank(riskModelId)) {
            return Collections.emptyList();
        }
        return indicatorDao.getIndicatorTypeByRiskModel(riskModelId);
    }

}
