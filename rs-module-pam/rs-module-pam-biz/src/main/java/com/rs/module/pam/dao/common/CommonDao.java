package com.rs.module.pam.dao.common;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.family.vo.FamilyPhoneListReqVO;
import com.rs.module.pam.controller.admin.family.vo.FamilyPhonePageReqVO;
import com.rs.module.pam.entity.common.PrintDocumentDO;
import com.rs.module.pam.entity.family.FamilyPhoneDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 监所事务管理-亲情电话 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface CommonDao {

    void batchSavePrintDocument(List<PrintDocumentDO> list);


    String getWsdataByGlid(@Param("glId") String glId, @Param("glType") String glType);

    void updateWsdataByGlid(@Param("glId") String glId, @Param("glType") String glType, @Param("wsdata") String wsdata);
}
