package com.rs.module.pam.controller.admin.religion.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-宗教申请新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ReligionApplyApprovalReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    @NotEmpty(message = "主键 不能为空")
    private String id;

    @ApiModelProperty("状态 字典：ZD_ZJLB_SQZT  2：通过  3：不通过")
    @NotEmpty(message = "状态不能为空")
    private String status;

    @ApiModelProperty("审核意见")
    private String approvalComments;


}
