package com.rs.module.pam.controller.admin.represent.vo.extra;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * 签到数据
 *
 */
@Data
@ApiModel("签到VO")
public class PrisonerSignVO {
    
    /**
     * 签到人id
     */
    @NotNull(message = "签到人id 不能为空!")
    @ApiModelProperty(value = "签到人id", required = true)
    private String personId;

    /**
     * 签到人名字
     */
    @NotNull(message = "签到人名字 不能为空!")
    @ApiModelProperty(value = "签到人名字", required = true)
    private String personName;

    /**
     * 监室id
     */
    @NotNull(message = "监室id 不能为空!")
    @ApiModelProperty(value = "监室id", required = true)
    private String roomId;

    /**
     * 照片
     */
    @NotNull(message = "照片 不能为空!")
    @ApiModelProperty(value = "照片", required = true)
    private String photo;

    @ApiModelProperty("温度")
    private String temperature;
}
