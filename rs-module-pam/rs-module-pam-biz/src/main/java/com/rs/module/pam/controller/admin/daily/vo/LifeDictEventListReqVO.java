package com.rs.module.pam.controller.admin.daily.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 监所事务管理-事务选项字典列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LifeDictEventListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("event_name")
    private String eventName;

    @ApiModelProperty("夹控标记（字典：ZD_CWGL_JKBJ）")
    private Short eventStatus;

    @ApiModelProperty("机构码")
    private String orgCode;

    @ApiModelProperty("县分局码")
    private String regCode;

    @ApiModelProperty("市局码")
    private String cityCode;

}
