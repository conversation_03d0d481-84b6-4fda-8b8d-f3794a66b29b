package com.rs.module.pam.entity.daily;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 监所事务管理-日常清监检查项配置 DO
 *
 * <AUTHOR>
 */
@TableName("pam_daily_clean_config")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CleanConfigDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 检查类型（字典：ZD_AQJCGL_JCLX）
     */
    private String checkType;
    /**
     * 检查项（字典：ZD_AQJCGL_JCX）
     */
    private String checkItem;

}
