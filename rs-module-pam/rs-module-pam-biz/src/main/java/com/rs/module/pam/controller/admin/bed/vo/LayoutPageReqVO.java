package com.rs.module.pam.controller.admin.bed.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-监室床位布局分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LayoutPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("布局名称")
    private String layoutName;

    @ApiModelProperty("布局照片地址")
    private String layoutImageUrl;

    @ApiModelProperty("布局配置")
    private String layoutConfig;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("排序属性")
    List<OrderItem> orderFields;
}
