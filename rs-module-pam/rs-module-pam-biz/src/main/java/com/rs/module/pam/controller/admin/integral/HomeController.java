package com.rs.module.pam.controller.admin.integral;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import com.rs.module.pam.controller.admin.integral.vo.IndicatorRespVO;
import com.rs.module.pam.controller.admin.integral.vo.IndicatorSaveReqVO;
import com.rs.module.pam.service.integral.ScoreDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "分级处遇-首页")
@RestController
@RequestMapping("/pam/integral/home")
@Validated
public class HomeController {

    @Resource
    private ScoreDetailService scoreDetailService;
    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;

    @GetMapping("/getOrgAndRoom")
    @ApiOperation(value = "获取监所与监室")
    public CommonResult getOrgAndRoom() {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        List<AreaPrisonRoomDO> roomList = areaPrisonRoomService.getRoomByOrgCode(sessionUser.getOrgCode());

        JSONArray result = new JSONArray();
        JSONObject orgJson = new JSONObject();
        orgJson.put("orgCode", sessionUser.getOrgCode());
        orgJson.put("roomCode", null);
        orgJson.put("name", sessionUser.getOrgName());
        result.add(orgJson);
        roomList.forEach(room -> {
            JSONObject roomJson = new JSONObject();
            roomJson.put("orgCode", room.getOrgCode());
            roomJson.put("roomCode", room.getRoomCode());
            roomJson.put("name", room.getRoomName());
            result.add(roomJson);
        });
        return success(result);
    }

    @GetMapping("/getOrgScore")
    @ApiOperation(value = "获取监所积分情况")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编码"),
            @ApiImplicitParam(name = "roomCode", value = "监室编码")
    })
    public CommonResult getOrgScore(@RequestParam("orgCode") String orgCode,
                                    @RequestParam(name = "roomCode", required = false) String roomCode) {
        return success(scoreDetailService.getOrgScore(orgCode, roomCode));
    }

    @GetMapping("/getSickRoom")
    @ApiOperation(value = "获取监室风险分布情况")
    @ApiImplicitParam(name = "orgCode", value = "机构编码")
    public CommonResult getSickRoom(@RequestParam("orgCode") String orgCode) {
        return success(scoreDetailService.getSickRoomByOrg(orgCode));
    }

    @GetMapping("/getSickPrisoner")
    @ApiOperation(value = "获取人员风险分布情况")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编码"),
            @ApiImplicitParam(name = "roomCode", value = "监室编码")
    })
    public CommonResult getSickPrisoner(@RequestParam("orgCode") String orgCode,
                                        @RequestParam(name = "roomCode", required = false) String roomCode) {
        return success(scoreDetailService.getSickPrisonerByOrg(orgCode, roomCode));
    }


}
