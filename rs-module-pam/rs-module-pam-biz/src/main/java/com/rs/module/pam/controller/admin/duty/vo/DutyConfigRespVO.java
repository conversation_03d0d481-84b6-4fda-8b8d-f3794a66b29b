package com.rs.module.pam.controller.admin.duty.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 监所事务管理-值班规则配置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DutyConfigRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("是否值班签到（1开启、0关闭）")
    private Short signinDutyEnable;
    @ApiModelProperty("值班签到语音提醒开启（1开启、0关闭）")
    private Short signinRemindEnable;
    @ApiModelProperty("语音播报静音时段，支持多个，格式：00:00:00-10:00:00,")
    private String silentTimeSlots;
    @ApiModelProperty("signin_validity_period")
    private Short signinValidityPeriod;
}
