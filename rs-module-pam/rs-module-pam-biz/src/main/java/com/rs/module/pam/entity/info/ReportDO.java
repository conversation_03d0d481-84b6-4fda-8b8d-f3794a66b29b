package com.rs.module.pam.entity.info;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 监所事务管理-信息报备 DO
 *
 * <AUTHOR>
 */
@TableName("pam_info_report")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 数据来源（字典：ZD_DATA_SOURCES）
     */
    private String dataSources;
    /**
     * 报备人
     */
    private String reporterId;
    /**
     * 报备人姓名
     */
    private String reporterName;
    /**
     * 报备时间
     */
    private Date reportTime;
    /**
     * 监室号ID
     */
    private String roomId;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 对象ID
     */
    private String objectId;
    /**
     * 报备对象名称
     */
    private String objectName;
    /**
     * 报备类别
     */
    private String reportType;
    /**
     * 报备事由
     */
    private String reportContent;
    /**
     * 报备时间类型（字典：ZD_XXBB_BBSJLX）
     */
    private String reportTimeType;
    /**
     * 报备开始时间
     */
    private Date reportStartTime;
    /**
     * 报备结束时间
     */
    private Date reportEndTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否参与点名
     */
    private Integer isRollcall;
    /**
     * 报备状态（字典：ZD_XXBB_BBZT）
     */
    private String status;
    /**
     * 审批人身份证号
     */
    private String approverSfzh;
    /**
     * 审批人姓名
     */
    private String approverXm;
    /**
     * 审批时间
     */
    private Date approverTime;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 审核意见
     */
    private String approvalComments;

}
