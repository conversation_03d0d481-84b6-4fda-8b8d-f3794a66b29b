package com.rs.module.pam.dao.represent;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.represent.vo.RepresentListReqVO;
import com.rs.module.pam.controller.admin.represent.vo.RepresentPageReqVO;
import com.rs.module.pam.controller.admin.represent.vo.extra.*;
import com.rs.module.pam.entity.represent.RepresentConfigDO;
import com.rs.module.pam.entity.represent.RepresentDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
* 监所事务管理-监室点名 Dao
*
* <AUTHOR>
*/
@Mapper
public interface RepresentDao extends IBaseDao<RepresentDO> {


    default PageResult<RepresentDO> selectPage(RepresentPageReqVO reqVO) {
        Page<RepresentDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<RepresentDO> wrapper = new LambdaQueryWrapperX<RepresentDO>()
            .eqIfPresent(RepresentDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(RepresentDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(RepresentDO::getAllInNum, reqVO.getAllInNum())
            .eqIfPresent(RepresentDO::getInNum, reqVO.getInNum())
            .eqIfPresent(RepresentDO::getOutNum, reqVO.getOutNum())
            .betweenIfPresent(RepresentDO::getStartTime, reqVO.getStartTime())
            .betweenIfPresent(RepresentDO::getEndTime, reqVO.getEndTime())
            .eqIfPresent(RepresentDO::getPresentStatus, reqVO.getPresentStatus())
            .eqIfPresent(RepresentDO::getPresentNum, reqVO.getPresentNum())
            .eqIfPresent(RepresentDO::getErrorNum, reqVO.getErrorNum())
            .eqIfPresent(RepresentDO::getErrorHandleResult, reqVO.getErrorHandleResult())
            .eqIfPresent(RepresentDO::getOperatePeopleSfzh, reqVO.getOperatePeopleSfzh())
            .likeIfPresent(RepresentDO::getOperatePeopleName, reqVO.getOperatePeopleName())
            .betweenIfPresent(RepresentDO::getOperateTime, reqVO.getOperateTime())
            .eqIfPresent(RepresentDO::getAreaId, reqVO.getAreaId())
            .likeIfPresent(RepresentDO::getAreaName, reqVO.getAreaName())
            .eqIfPresent(RepresentDO::getPresentNo, reqVO.getPresentNo())
            .eqIfPresent(RepresentDO::getPresentType, reqVO.getPresentType())
            .betweenIfPresent(RepresentDO::getExpiryDate, reqVO.getExpiryDate())
            .eqIfPresent(RepresentDO::getIsVideo, reqVO.getIsVideo())
            .eqIfPresent(RepresentDO::getTemperatureNum, reqVO.getTemperatureNum())
            .eqIfPresent(RepresentDO::getTemperatureErrNum, reqVO.getTemperatureErrNum())
            .eqIfPresent(RepresentDO::getReportNum, reqVO.getReportNum())
            .eqIfPresent(RepresentDO::getInitSource, reqVO.getInitSource())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(RepresentDO::getAddTime);
        }
        Page<RepresentDO> Page = selectPage(page, wrapper);
        return new PageResult<>(Page.getRecords(), Page.getTotal());
    }
    default List<RepresentDO> selectList(RepresentListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<RepresentDO>()
            .eqIfPresent(RepresentDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(RepresentDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(RepresentDO::getAllInNum, reqVO.getAllInNum())
            .eqIfPresent(RepresentDO::getInNum, reqVO.getInNum())
            .eqIfPresent(RepresentDO::getOutNum, reqVO.getOutNum())
            .betweenIfPresent(RepresentDO::getStartTime, reqVO.getStartTime())
            .betweenIfPresent(RepresentDO::getEndTime, reqVO.getEndTime())
            .eqIfPresent(RepresentDO::getPresentStatus, reqVO.getPresentStatus())
            .eqIfPresent(RepresentDO::getPresentNum, reqVO.getPresentNum())
            .eqIfPresent(RepresentDO::getErrorNum, reqVO.getErrorNum())
            .eqIfPresent(RepresentDO::getErrorHandleResult, reqVO.getErrorHandleResult())
            .eqIfPresent(RepresentDO::getOperatePeopleSfzh, reqVO.getOperatePeopleSfzh())
            .likeIfPresent(RepresentDO::getOperatePeopleName, reqVO.getOperatePeopleName())
            .betweenIfPresent(RepresentDO::getOperateTime, reqVO.getOperateTime())
            .eqIfPresent(RepresentDO::getAreaId, reqVO.getAreaId())
            .likeIfPresent(RepresentDO::getAreaName, reqVO.getAreaName())
            .eqIfPresent(RepresentDO::getPresentNo, reqVO.getPresentNo())
            .eqIfPresent(RepresentDO::getPresentType, reqVO.getPresentType())
            .betweenIfPresent(RepresentDO::getExpiryDate, reqVO.getExpiryDate())
            .eqIfPresent(RepresentDO::getIsVideo, reqVO.getIsVideo())
            .eqIfPresent(RepresentDO::getTemperatureNum, reqVO.getTemperatureNum())
            .eqIfPresent(RepresentDO::getTemperatureErrNum, reqVO.getTemperatureErrNum())
            .eqIfPresent(RepresentDO::getReportNum, reqVO.getReportNum())
            .eqIfPresent(RepresentDO::getInitSource, reqVO.getInitSource())
        .orderByDesc(RepresentDO::getAddTime));    }

    /**
     * 获取监室数据
     * @param code
     * @param userId
     * @param roomIds
     * @return
     */
    List<RoomInfoVO> getWarderRoomInfo(@Param("code") String code, @Param("userId") String userId,
                                       @Param("roomIds") String[] roomIds, @Param("orgCode") String orgCode);


    List<Map> getStatusData(@Param("roomId") String roomId);

    /***
     * 获取监室数据-新
     * @param roomId
     * @return
     */
    RoomInfoVO getRoomInfoByRoomId(@Param("roomId") String roomId);
    /**
     * 获取监室人数
     * @param roomId
     * @return
     */
    Integer getCountInRoom(@Param("roomId") String roomId);

    /***
     * 获取监室人数-新
     * @param roomId
     * @return
     */
    Integer getNewCountInRoom(@Param("roomId") String roomId);

    /**
     * 获取主管/协管/关注/所有监室监室数据
     * @param code
     * @param userId
     * @return
     */
    List<Map> getRoomIds(@Param("code") String code, @Param("userId") Integer userId, @Param("prisonId")String prisonId);

    /**
     * 获取今日点名结果数据
     * @param pageDto
     * @param page
     * @return
     */
    List<RoomInfoRepresentVO> getRepresentResult(Page page, @Param("form") RepresentPageReqVO pageDto);

    /***
     *
     * @param page
     * @param pageDto
     * @return
     */
//    List<TemperatureListPageVO> getTemperaturePageList(Page page, @Param("form") TemperatureListPageDTO pageDto);

    /**
     * 获取今日点名结果数据-不分页
     * @param pageDto
     * @return
     */
    List<RoomInfoRepresentVO> getRepresentResult(@Param("form") RepresentPageReqVO pageDto);

    /***
     * 获取该点名批次的人数返回值
     * @param presentNo
     * @return
     */
    RoomInfoRepresentVO getRepresentSum(@Param("presentNo") String presentNo);

    /***
     * 获取该点名批次的id和时间
     * @param presentNo
     * @return
     */
    RoomInfoRepresentVO getRepresentDate(@Param("presentNo") String presentNo);

//    /**
//     * 获取今日监室点名结果统计
//     * @return
//     */
//    Integer getRepresentResultCount(@Param("roomIds") String roomIds,@Param("status") String status);

    /**
     * 分页查询监室点名数据
     * @param page
     * @param form
     * @return
     */
    List<RepresentPageReqVO> findByPage(Page page, @Param("form") RepresentPageReqVO form);

    /**
     * 得到在押人员信息与外出情况
     * @param roomId
     * @return
     */
    List<RoomPersonVO> getPersonInRoom(@Param("roomId") String roomId);

    /**
     * 获取监室号对应的正在进行签到的数据
     * @param roomId
     * @return
     */
    RepresentDO getNowRepresentByRoomId(@Param("roomId") String roomId);

    @Select("select 1 from pam_represent a where room_id= #{roomId} and present_status='1' order by operate_time desc")
    List<String> checkIsNowRepresent(@Param("roomId") String roomId);

    @Select("select 1 from pam_represent where room_id = #{roomId} and present_no = #{no}")
    List<String> checkRepresentNo(@Param("no") String no,@Param("roomId") String roomId);

    @Select("SELECT * from pam_represent WHERE present_no = #{no} LIMIT 1")
    RepresentDO getRepresentByNo(@Param("no") String no);

    @Select("select room_name from acp_pm_area where id = #{roomId} LIMIT 1")
    String getRoomNameById(@Param("roomId") String roomId);


    /**
     * 手动点名刷新获取数据显示
     * @param roomIds
     * @return
     */
    List<RoomInfoRepresentVO> selectInRepresentByRoomIds(@Param("roomIds") List<String> roomIds);



    /**
     * 根据批号获取点名中的监室
     * @param presentNo
     * @return
     */
    List<String> endRepresentByNo(@Param("presentNo")String presentNo);

    /***
     * 根据点名批号获取详情
     * @param presentNo
     * @return
     */
    List<RoomInfoRepresentVO> selectRepresentById(@Param("presentNo") String presentNo);

    /***
     * 根据点名批号获取测温详情
     * @param id
     * @return
     */
    List<RoomInfoRepresentVO> selectTemperatureById(@Param("id") String id);

    /**
     * 判断该监室是否有点名中数据
     * @param roomId
     * @return
     */
    Integer getInRepresnetData(@Param("orgCode") String orgCode, @Param("roomId") String roomId);

    String getInRepresnetStatus(@Param("orgCode") String orgCode, @Param("roomId") String roomId);

    /**
     * 获取昨天的未点名完成的数据
     * @return
     */
    Integer updateUnFinishPresent(@Param("status") String status,@Param("result") String result);

    List<RepresentDO> getUnFinishPresent();

    /***
     * 仓外屏-今日点名统计
     * @param roomId
     * @return
     */
    List<RoomInfoRepresentVO> getCwpRepresentRoomResult(@Param("roomId") String roomId);

    /***
     * 根据监室号获取序列号
     * @param roomId
     * @return
     */
    @Select("SELECT A.serial_number " +
            "FROM acp_pm_device_inscreen A " +
            "WHERE " +
            "A.room_id = #{roomId} " +
            "LIMIT 1")
    String getSerialNumberByRoomId(@Param("roomId") String roomId);

    /***
     * 根据序列号返回同步基础信息
     * @param id
     * @return
     */
    InitBaseDataVO getInitBaseDataList(@Param("id") String id);

    /**
     * 获取在押人员
     * @param roomId
     * @return
     */
    List<InitBaseDataPersonVO> findPersonList(@Param("roomId") String roomId);


    RepresentConfigDO getOneTask(@Param("roomId") String roomId);

    /***
     * 根据批次号获取点名id
     * @param code
     * @param roomId
     * @return
     */
    @Select("select id from pam_represent where present_no = #{code} and room_id = #{roomId} LIMIT 1")
    String getRoomPresentIdByCode(@Param("code") String code,@Param("roomId") String roomId);

    @Select("SELECT xm from vw_acp_pm_prisoner_in WHERE jgrybm = #{id} LIMIT 1 ")
    String getPersonNameById(@Param("id") String id);

    @Select("SELECT a.photo,b.jgrybm as jgrybm,b.jsh as roomId,b.room_name,b.xm as name from acp_pm_cnp_face a LEFT JOIN vw_acp_pm_prisoner_in b on a.personnel_code = b.jgrybm WHERE personnel_code = #{jgrybm} ORDER BY update_time desc LIMIT 1")
    RoomRepresentCheckVO getCnpPhoto(@Param("jgrybm") String jgrybm);

    @Select("SELECT front_photo as photo,jgrybm as jgrybm,jsh as roomId,room_name,xm as name from vw_acp_pm_prisoner_in WHERE jgrybm = #{jgrybm} LIMIT 1")
    RoomRepresentCheckVO getPrisonerPhotoByPrisonerId(@Param("jgrybm") String jgrybm);

}
