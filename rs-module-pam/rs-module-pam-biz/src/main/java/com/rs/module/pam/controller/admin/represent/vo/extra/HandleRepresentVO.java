package com.rs.module.pam.controller.admin.represent.vo.extra;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * 手动点名返回vo
 *
 */
@Data
@ApiModel("手动点名返回vo")
public class HandleRepresentVO {
    
    /**
     * id
     */
    @ApiModelProperty(value = "监室卡片数据")
    private List<RoomInfoRepresentVO> rooms;

    /**
     * 进行中
     */
    @ApiModelProperty(value = "进行中数量")
    private Integer inRepresent;
    /**
     * 已结束
     */
    @ApiModelProperty(value = "已结束数量")
    private Integer endRepresent;
    /**
     * 异常待处置
     */
    @ApiModelProperty(value = "异常待处置数量")
    private Integer errorRepresent;

    /***
     * 有效期
     */
    private Integer expiryDate;
}
