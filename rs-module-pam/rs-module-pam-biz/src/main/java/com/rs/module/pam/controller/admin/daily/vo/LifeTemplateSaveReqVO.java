package com.rs.module.pam.controller.admin.daily.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import com.rs.module.pam.entity.daily.LifeTemplateEventDO;

import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-一日生活制度模板新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LifeTemplateSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("制度名称")
    @NotEmpty(message = "制度名称不能为空")
    private String templateName;

    @ApiModelProperty("监所事务管理-一日生活制度模板关联事务列表")
    private List<LifeTemplateEventDO> lifeTemplateEvents;

}
