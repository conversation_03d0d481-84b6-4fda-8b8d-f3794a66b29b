package com.rs.module.pam.controller.admin.psy.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;

/**
 *
 * <AUTHOR>
 * @Date 2025/6/20 16:16
 */
@ApiModel(description = "管理后台 - 监所事务管理- 题目排序 Request VO")
@Data
public class EvalTableQuestionOrderReqVO {

    @ApiModelProperty(value = "题目ID", required = true)
    private String questionId;

    @ApiModelProperty(value = "排序序号", required = true)
    @Min(value = 1, message = "排序序号必须大于0")
    private Integer sortOrder;
}
