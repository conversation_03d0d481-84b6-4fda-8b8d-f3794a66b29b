package com.rs.module.pam.controller.admin.duty;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.pam.controller.admin.duty.vo.DutyShiftSaveReqVO;
import com.rs.module.pam.controller.admin.duty.vo.ShiftRespVO;
import com.rs.module.pam.service.duty.ShiftService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "值班管理-值班班次")
@RestController
@RequestMapping("/pam/duty/shift")
@Validated
public class ShiftController {

    @Resource
    private ShiftService shiftService;

    /**
     * 监室值班-班次管理-保存编辑
     */
    @GetMapping("/getShift")
    @ApiOperation(value = "监室值班-获取班次信息")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "orgCode", value = "机构编号"),
            @ApiImplicitParam(name = "roomId", value = "监室id")
    })
    @BusTrace(busType = BusTypeEnum.YEWU_JSZB, condition = "false", content = "{\"监室编号\":\"{{#reqVO.roomId}}\"}")
    public CommonResult<List<ShiftRespVO>> getShift(@RequestParam("orgCode") String orgCode, @RequestParam("roomId") String roomId) {
        return success(shiftService.getShift(orgCode, roomId));
    }

    /**
     * 监室值班-班次管理-保存编辑
     */
    @PostMapping("/shiftManageSave")
    @ApiOperation(value = "监室值班-班次管理-保存编辑")
    @BusTrace(busType = BusTypeEnum.YEWU_JSZB, condition = "false", content = "{\"监室编号\":\"{{#reqVO.roomId}}\"}")
    public CommonResult<String> shiftManageSave(@RequestBody @Validated DutyShiftSaveReqVO reqVO) {
        shiftService.shiftManageSave(reqVO);
        return success("保存成功");
    }

}
