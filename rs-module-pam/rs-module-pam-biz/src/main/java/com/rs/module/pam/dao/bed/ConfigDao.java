package com.rs.module.pam.dao.bed;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.bed.vo.ConfigListReqVO;
import com.rs.module.pam.controller.admin.bed.vo.ConfigPageReqVO;
import com.rs.module.pam.entity.bed.ConfigDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 监所事务管理-监室床位配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ConfigDao extends IBaseDao<ConfigDO> {


    default PageResult<ConfigDO> selectPage(ConfigPageReqVO reqVO) {
        Page<ConfigDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ConfigDO> wrapper = new LambdaQueryWrapperX<ConfigDO>()
            .eqIfPresent(ConfigDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(ConfigDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(ConfigDO::getLayoutId, reqVO.getLayoutId())
            .likeIfPresent(ConfigDO::getLayoutName, reqVO.getLayoutName())
            .eqIfPresent(ConfigDO::getBedSpaceLength, reqVO.getBedSpaceLength())
            .eqIfPresent(ConfigDO::getBedSpaceWidth, reqVO.getBedSpaceWidth())
            .eqIfPresent(ConfigDO::getBedSpaceHeight, reqVO.getBedSpaceHeight())
            .eqIfPresent(ConfigDO::getBedSpaceBearing, reqVO.getBedSpaceBearing())
            .eqIfPresent(ConfigDO::getBedAutoConfig, reqVO.getBedAutoConfig())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(ConfigDO::getAddTime);
        }
        Page<ConfigDO> configPage = selectPage(page, wrapper);
        return new PageResult<>(configPage.getRecords(), configPage.getTotal());
    }
    default List<ConfigDO> selectList(ConfigListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ConfigDO>()
            .eqIfPresent(ConfigDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(ConfigDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(ConfigDO::getLayoutId, reqVO.getLayoutId())
            .likeIfPresent(ConfigDO::getLayoutName, reqVO.getLayoutName())
            .eqIfPresent(ConfigDO::getBedSpaceLength, reqVO.getBedSpaceLength())
            .eqIfPresent(ConfigDO::getBedSpaceWidth, reqVO.getBedSpaceWidth())
            .eqIfPresent(ConfigDO::getBedSpaceHeight, reqVO.getBedSpaceHeight())
            .eqIfPresent(ConfigDO::getBedSpaceBearing, reqVO.getBedSpaceBearing())
            .eqIfPresent(ConfigDO::getBedAutoConfig, reqVO.getBedAutoConfig())
        .orderByDesc(ConfigDO::getAddTime));
    }
    default ConfigDO getByRoomId(String orgCode, String roomId) {
        return selectOne(new LambdaQueryWrapper<ConfigDO>()
                .eq(ConfigDO::getOrgCode, orgCode)
                .eq(ConfigDO::getRoomId, roomId));
    }

}
