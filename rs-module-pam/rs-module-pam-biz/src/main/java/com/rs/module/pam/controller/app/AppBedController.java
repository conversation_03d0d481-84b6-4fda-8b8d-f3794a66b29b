package com.rs.module.pam.controller.app;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.pam.controller.admin.bed.vo.ConfigRespVO;
import com.rs.module.pam.service.bed.ConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "床位管理-床位信息-仓内屏")
@RestController
@RequestMapping("/app/pam/public/bed/")
@Validated
public class AppBedController {

    @Resource
    private ConfigService configService;

    @GetMapping("/getByRoomId")
    @ApiOperation(value = "获得监室床位配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编号"),
            @ApiImplicitParam(name = "roomId", value = "监室编号")
    })
    @BusTrace(busType = BusTypeEnum.YEWU_CWGL, condition = "false", content = "{\"监室编号\":\"{{#roomId}}\"}")
    public CommonResult<ConfigRespVO> getByRoomId(@RequestParam("orgCode") String orgCode,
                                                  @RequestParam("roomId") String roomId) {
        return success(configService.getByRoomId(orgCode, roomId));
    }

}
