package com.rs.module.pam.dao.daily;

import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.PageParam;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.pam.entity.daily.LifeRoomDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
 * 监所事务管理-一日生活制度关联监室
 *
 * <AUTHOR>
 */
@Mapper
public interface LifeRoomDao extends IBaseDao<LifeRoomDO> {

    default List<LifeRoomDO> selectListByDailyLifeId(String dailyLifeId) {
        return selectList(new LambdaQueryWrapperX<LifeRoomDO>().eq(LifeRoomDO::getDailyLifeId, dailyLifeId));
    }

    default int deleteByDailyLifeId(String dailyLifeId) {
        return delete(new LambdaQueryWrapperX<LifeRoomDO>().eq(LifeRoomDO::getDailyLifeId, dailyLifeId));
    }

}
