package com.rs.module.pam.entity.duty.day;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-值日班次 DO
 *
 * <AUTHOR>
 */
@TableName("pam_day_duty_shift")
@KeySequence("pam_day_duty_shift_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_day_duty_shift")
public class DayDutyShiftDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 班次名称
     */
    private String shiftName;
    /**
     * 生效日期
     */
    private Date effectiveStartDate;
    /**
     * 失效日期，不包含该日期
     */
    private Date effectiveEndDate;
    /**
     * 排序
     */
    private Integer sort;

}
