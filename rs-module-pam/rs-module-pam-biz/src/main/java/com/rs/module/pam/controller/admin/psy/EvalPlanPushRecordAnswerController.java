package com.rs.module.pam.controller.admin.psy;

import com.rs.framework.common.enums.DataSourceAppEnum;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.pam.controller.admin.psy.vo.*;
import com.rs.module.pam.entity.psy.EvalPlanPushRecordAnswerDO;
import com.rs.module.pam.service.psy.EvalPlanPushRecordAnswerService;

@Api(tags = "心理测评-计划推送记录答案")
@RestController
@RequestMapping("/pam/psy/evalPlanPushRecordAnswer")
@Validated
public class EvalPlanPushRecordAnswerController {

    @Resource
    private EvalPlanPushRecordAnswerService evalPlanPushRecordAnswerService;

    @PostMapping("/create")
    @ApiOperation(value = "创建-心理测评答题卡")
    public CommonResult<String> createEvalPlanPushRecordAnswer(@Valid @RequestBody EvalPlanPushRecordAnswerSaveReqVO createReqVO) {
        createReqVO.setDataSources(DataSourceAppEnum.ACP.getCode());
        return success(evalPlanPushRecordAnswerService.createEvalPlanPushRecordAnswer(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新-心理测评答题卡")
    public CommonResult<Boolean> updateEvalPlanPushRecordAnswer(@Valid @RequestBody EvalPlanPushRecordAnswerSaveReqVO updateReqVO) {
        evalPlanPushRecordAnswerService.updateEvalPlanPushRecordAnswer(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除-心理测评答题卡")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteEvalPlanPushRecordAnswer(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           evalPlanPushRecordAnswerService.deleteEvalPlanPushRecordAnswer(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得-心理测评答题卡")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<EvalPlanPushRecordAnswerRespVO> getEvalPlanPushRecordAnswer(@RequestParam("id") String id) {
        EvalPlanPushRecordAnswerDO evalPlanPushRecordAnswer = evalPlanPushRecordAnswerService.getEvalPlanPushRecordAnswer(id);
        return success(BeanUtils.toBean(evalPlanPushRecordAnswer, EvalPlanPushRecordAnswerRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得-心理测评答题卡分页")
    public CommonResult<PageResult<EvalPlanPushRecordAnswerRespVO>> getEvalPlanPushRecordAnswerPage(@Valid @RequestBody EvalPlanPushRecordAnswerPageReqVO pageReqVO) {
        PageResult<EvalPlanPushRecordAnswerDO> pageResult = evalPlanPushRecordAnswerService.getEvalPlanPushRecordAnswerPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EvalPlanPushRecordAnswerRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得-心理测评答题卡列表")
    public CommonResult<List<EvalPlanPushRecordAnswerRespVO>> getEvalPlanPushRecordAnswerList(@Valid @RequestBody EvalPlanPushRecordAnswerListReqVO listReqVO) {
        List<EvalPlanPushRecordAnswerDO> list = evalPlanPushRecordAnswerService.getEvalPlanPushRecordAnswerList(listReqVO);
        return success(BeanUtils.toBean(list, EvalPlanPushRecordAnswerRespVO.class));
    }
}
