package com.rs.module.pam.controller.admin.daily.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-一日生活制度分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LifePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("制度名称")
    private String name;

    @ApiModelProperty("周期设置（字典：ZD_YRSHZD_ZQSZ）")
    private String cycleSetting;

    @ApiModelProperty("cycle_config")
    private String cycleConfig;

    @ApiModelProperty("自定义时间")
    private String[] customizeTime;

    @ApiModelProperty("报备状态（字典：ZD_XXBB_BBZT）")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date[] approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("领导签名")
    private String approvalAutograph;

    @ApiModelProperty("领导签名日期")
    private Date[] approvalAutographTime;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
