package com.rs.module.pam.controller.admin.psy.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评量表管理列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EvalTableListReqVO extends BaseVO {

    @ApiModelProperty("量表名称")
    private String name;

    @ApiModelProperty("量表类型（字典：ZD_XLPC_LBLX）")
    private String tableType;

    @ApiModelProperty("使用状态（字典：ZD_PSY_SYZT）")
    private String usageStatus;

    @ApiModelProperty("监所编码")
    private String orgCode;

}
