package com.rs.module.pam.entity.daily;

import lombok.*;
import java.util.*;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 监所事务管理-一日生活制度关联监室 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 监所事务管理-一日生活制度关联监室新增/修改 Request VO")
@TableName("pam_daily_life_room")
@KeySequence("pam_daily_life_room_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LifeRoomDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;
    /**
     * 一日生活制度ID
     */
    @ApiModelProperty("一日生活制度ID")
    private String dailyLifeId;
    /**
     * 监室id
     */
    @ApiModelProperty("监室id")
    private String roomId;

    @ApiModelProperty("监室名称")
    @TableField(exist = false)
    private String roomName;

}
