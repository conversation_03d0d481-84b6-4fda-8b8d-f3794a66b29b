package com.rs.module.pam.controller.admin.cook.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@ApiModel(description = "管理后台 - 监所事务管理-特殊餐申请-医生审批-审批/修改 Request VO")
@Data
public class SpecialApplyLeaderApproveReqVO {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("所领导审批结果")
    @NotBlank(message = "所领导审批结果不能为空")
    private String leaderApprovalResult;
    @ApiModelProperty("所领导审核意见")
    private String leaderApprovalComments;

}
