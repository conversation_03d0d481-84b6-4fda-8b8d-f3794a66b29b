package com.rs.module.pam.dao.duty.day;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.entity.duty.day.DayDutyRecordsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* 监所事务管理-监室值日记录 Dao
*
* <AUTHOR>
*/
@Mapper
public interface DayDutyRecordsDao extends IBaseDao<DayDutyRecordsDO> {

    default List<DayDutyRecordsDO> selectList(String orgCode, String roomId, Date startDate, Date endDate) {
        return selectList(new LambdaQueryWrapperX<DayDutyRecordsDO>()
                .eq(DayDutyRecordsDO::getOrgCode, orgCode)
                .eq(DayDutyRecordsDO::getRoomId, roomId)
                .betweenIfPresent(DayDutyRecordsDO::getDutyDate, startDate, endDate));
    }

    /**
     * 删除指定日期后 排班情况
     *
     * @param roomId
     * @param dutyDate
     * @return
     */
    int deleteByDutyDate(@Param("orgCode") String orgCode, @Param("roomId") String roomId, @Param("dutyDate") Date dutyDate);

    /**
     * 删除指定监室指定人员的排班情况
     * @param dutyDate
     * @param jgrybmList
     * @return
     */
    int deleteDutyRecordsByPrisoner(@Param("dutyDate") Date dutyDate, @Param("jgrybmList") List<String> jgrybmList);


}
