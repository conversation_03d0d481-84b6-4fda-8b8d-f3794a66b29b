package com.rs.module.pam.entity.cook;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 监所事务管理-菜品模板 DO
 *
 * <AUTHOR>
 */
@TableName("pam_cook_template")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TemplateDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 周一菜谱
     */
    private String mondayCookbook;
    /**
     * 周二菜谱
     */
    private String tuesdayCookbook;
    /**
     * 周三菜谱
     */
    private String wednesdayCookbook;
    /**
     * 周四菜谱
     */
    private String thursdayCookbook;
    /**
     * 周五菜谱
     */
    private String fridayCookbook;
    /**
     * 周六菜谱
     */
    private String saturdayCookbook;
    /**
     * 周日菜谱
     */
    private String sundayCookbook;
    /**
     * 菜谱开始日期
     */
    private Date startTime;
    /**
     * 菜谱截至日期
     */
    private Date endTime;
    /**
     * 周编号 比如1月第一周 20230101
     */
    private String weekNo;
    /**
     * 是否复制
     */
    private String isCopy;
    /**
     * 复制ID
     */
    private String copyId;

}
