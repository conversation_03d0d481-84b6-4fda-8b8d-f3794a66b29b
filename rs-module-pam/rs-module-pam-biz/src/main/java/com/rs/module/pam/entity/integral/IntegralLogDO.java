package com.rs.module.pam.entity.integral;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 监所事务管理-积分执行日志 DO
 *
 * <AUTHOR>
 */
@TableName("pam_integral_log")
@KeySequence("pam_integral_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_integral_log")
public class IntegralLogDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @TableField(value = "CITY_NAME", fill = FieldFill.INSERT)
    private String cityName;

    @TableField(value = "CITY_CODE", fill = FieldFill.INSERT)
    private String cityCode;

    @TableField(value = "REG_NAME", fill = FieldFill.INSERT)
    private String regName;

    @TableField(value = "REG_CODE", fill = FieldFill.INSERT)
    private String regCode;

    @TableField(value = "ORG_NAME", fill = FieldFill.INSERT)
    private String orgName;

    @TableField(value = "ORG_CODE", fill = FieldFill.INSERT)
    private String orgCode;
    /**
     * 模型id
     */
    private String modelId;
    /**
     * 业务主键
     */
    private String businessId;
    /**
     * 积分明细ID
     */
    private String scoreId;
    /**
     * 日期值
     */
    private String dateValue;
    /**
     * 监室编号
     */
    private String roomCode;
    /**
     * 被监管人员编号
     */
    private String prisonerCode;
    /**
     * 创建时间
     */
    private Date handleTime;
    /**
     * 执行状态码
     */
    private String handleCode;
    /**
     * 执行状态  （0：成功 -1：失败）
     */
    private String handleStatus;
    /**
     * 执行参数
     */
    private String handleParams;
    /**
     * 执行日志
     */
    private String handleMsg;
    /**
     * 失败重试次数
     */
    private Integer handleFailRetryCount;

}
