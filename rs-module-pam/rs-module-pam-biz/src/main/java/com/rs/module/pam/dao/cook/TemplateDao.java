package com.rs.module.pam.dao.cook;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.cook.vo.TemplateListReqVO;
import com.rs.module.pam.entity.cook.TemplateDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 监所事务管理-菜品模板 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface TemplateDao extends IBaseDao<TemplateDO> {

    default List<TemplateDO> selectList(TemplateListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TemplateDO>()
                .eqIfPresent(TemplateDO::getOrgCode, reqVO.getOrgCode())
                .likeIfPresent(TemplateDO::getTemplateName, reqVO.getTemplateName())
                .orderByDesc(TemplateDO::getAddTime));
    }

    default TemplateDO getTempByWeek(String weekNo, String orgCode) {
        LambdaQueryWrapper<TemplateDO> query = Wrappers.lambdaQuery(TemplateDO.class);
        query.eq(TemplateDO::getWeekNo, weekNo).eq(TemplateDO::getOrgCode, orgCode);
        return selectOne(query);
    }

}
