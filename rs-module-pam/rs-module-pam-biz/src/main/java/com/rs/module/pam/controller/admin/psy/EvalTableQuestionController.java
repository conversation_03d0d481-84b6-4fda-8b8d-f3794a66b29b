package com.rs.module.pam.controller.admin.psy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableQuestionListReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableQuestionOrderReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableQuestionPageReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableQuestionRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableQuestionSaveReqVO;
import com.rs.module.pam.dao.common.CommonDao;
import com.rs.module.pam.entity.psy.EvalTableQuestionDO;
import com.rs.module.pam.service.psy.EvalTableQuestionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "心理测评-量表关联题目")
@RestController
@RequestMapping("/pam/psy/evalTableQuestion")
@Validated
public class EvalTableQuestionController {

    @Resource
    private EvalTableQuestionService evalTableQuestionService;

    @Resource
    private CommonDao commonDao;

    @PostMapping("/createOrUpdate")
    @ApiOperation(value = "心理测评-创建题目或者更新题目")
    public CommonResult<String> createEvalTableQuestion(@Valid @RequestBody EvalTableQuestionSaveReqVO createReqVO) {
        return success(evalTableQuestionService.createEvalTableQuestion(createReqVO));
    }

    @GetMapping("/delete")
    @ApiOperation(value = "心理测评-删除题目")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteEvalTableQuestion(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           evalTableQuestionService.deleteEvalTableQuestion(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得-心理测评量表关联题目")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<EvalTableQuestionRespVO> getEvalTableQuestion(@RequestParam("id") String id) {
        EvalTableQuestionRespVO evalTableQuestion = evalTableQuestionService.getEvalTableQuestionRespVO(id);
        return success(evalTableQuestion);
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得-心理测评量表关联题目分页带题目选项")
    public CommonResult<PageResult<EvalTableQuestionRespVO>> getEvalTableQuestionPage(
            @Valid @RequestBody EvalTableQuestionPageReqVO pageReqVO) {
        PageResult<EvalTableQuestionRespVO> pageResult = evalTableQuestionService.getEvalTableQuestionPage(pageReqVO);
        return success(pageResult);
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得-心理测评量表关联题目列表带题目选项")
    public CommonResult<List<EvalTableQuestionRespVO>> getEvalTableQuestionList(@Valid @RequestBody EvalTableQuestionListReqVO listReqVO) {
        List<EvalTableQuestionRespVO> list = evalTableQuestionService.getEvalTableQuestionListRespVO(listReqVO);
        return success(list);
    }

    @PostMapping("/questionOrder")
    @ApiOperation(value = "题目排序")
    public CommonResult questionOrder(@RequestBody List<EvalTableQuestionOrderReqVO> list) {
        evalTableQuestionService.questionOrder(list);
        return success();
    }

}
