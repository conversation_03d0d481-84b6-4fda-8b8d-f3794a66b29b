package com.rs.module.pam.service.screen;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.mybatis.util.BspDbUtil;
import com.rs.module.pam.dao.screen.ZdJydtScreenDao;
import com.rs.module.pam.service.screen.bo.ZdJydtJydtBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ZdJydtScreenServiceImpl implements ZdJydtScreenService {

    // 总队可以查看的机构数据；KSS：看守所，JLS：拘留所，JDS：戒毒所，JGYY：监管医院（强疗）
    private static final String tempCorpsViewCode = "KSS-110000112,KSS-110000113,KSS-110000116,JLS-110000121,JDS-110000131,JGYY-110000151";

    @Resource
    private ZdJydtScreenDao zdJydtScreenDao;

    @Resource
    private AqdtScreenService aqdtScreenService;

    @Override
    public JSONObject jydt() {
        //六所 当前羁押总数 昨日羁押总数
        //机构类型 羁押总数 昨日羁押总数
        // 昨日羁押总数 = 羁押总数-今日入所(今日入所 且 在所) + 今日出所(今日之前入所 且 今日出所)
        // (默认今日入所不可能今日出所，今日出所不可能今日入所)
        Map<String, ZdJydtJydtBO> result = new HashMap<>();
        result.put("all", new ZdJydtJydtBO());

        List<JSONObject> jyzs = zdJydtScreenDao.getJyzs(getAllOrgCode());
        Map<String, JSONObject> jyzsMap = jyzs.stream().collect(Collectors.toMap(a -> a.getString("org_code"), Function.identity()));
        Map<String, JSONObject> allPmOrgInfoMap = getAllPmOrgInfoMap();
        Map<String, String> orgCodeType = getAllOrgCodeType();
        for (String orgCode : getAllOrgCode()) {
            // 机构
            JSONObject pmOrgInfo = allPmOrgInfoMap.getOrDefault(orgCode, new JSONObject());
            JSONObject orgCodeJyzs = jyzsMap.getOrDefault(orgCode, new JSONObject());
            ZdJydtJydtBO zdJydtJydtBO = orgCodeJyzs.toJavaObject(ZdJydtJydtBO.class);
            // 所容量
            zdJydtJydtBO.setSjrl(pmOrgInfo.getLongValue("sjrl"));
            // 所民警总数
            zdJydtJydtBO.setMjzs(pmOrgInfo.getLongValue("mjzs"));
            zdJydtJydtBO.setOrgCode(orgCode);
            zdJydtJydtBO.setOrgName(pmOrgInfo.getString("name"));
            result.put(orgCode, zdJydtJydtBO);
            // 总数
            ZdJydtJydtBO all = result.get("all");
            all.setJyzs(all.getJyzs() + zdJydtJydtBO.getJyzs());
            all.setJrcs(all.getJrcs() + zdJydtJydtBO.getJrcs());
            all.setJrrs(all.getJrrs() + zdJydtJydtBO.getJrrs());
            all.setSjrl(all.getSjrl() + zdJydtJydtBO.getSjrl());
            all.setMjzs(all.getMjzs() + zdJydtJydtBO.getMjzs());
            // 机构类型
            String orgType = orgCodeType.get(orgCode);
            ZdJydtJydtBO orgTypeBo = result.get(orgType);
            if (Objects.isNull(orgTypeBo)) {
                orgTypeBo = new ZdJydtJydtBO();
                result.put(orgType, orgTypeBo);
            }
            orgTypeBo.setJyzs(orgTypeBo.getJyzs() + zdJydtJydtBO.getJyzs());
            orgTypeBo.setJrcs(orgTypeBo.getJrcs() + zdJydtJydtBO.getJrcs());
            orgTypeBo.setJrrs(orgTypeBo.getJrrs() + zdJydtJydtBO.getJrrs());
            orgTypeBo.setSjrl(orgTypeBo.getSjrl() + zdJydtJydtBO.getSjrl());
            orgTypeBo.setMjzs(orgTypeBo.getMjzs() + zdJydtJydtBO.getMjzs());
        }
        // 各监所押量情况
        List<ZdJydtJydtBO> gjsylqkList = new ArrayList<>();
        // 警押比
        List<ZdJydtJydtBO> jybList = new ArrayList<>();
        for (ZdJydtJydtBO value : result.values()) {
            // 昨日羁押总数
            value.setYesterdayJyzs(value.getJyzs() - value.getJrrs() + value.getJrcs());
            // 总数 较 昨日
            value.setCompareYesterday(value.getJyzs() - value.getYesterdayJyzs());
            // 押容比
            value.setYrb(String.format("%.2f", value.getJyzs() * 100f / value.getSjrl()));
            // 警押比
            //String.format("%.2f", value.getJyzs() * 1f / value.getMjzs())
            value.setJyb(new BigDecimal(value.getJyzs()).divide(new BigDecimal(value.getMjzs()), 2, BigDecimal.ROUND_HALF_UP));

            String orgType = orgCodeType.get(value.getOrgCode());
            if (StringUtils.isNotEmpty(orgType)) {
                gjsylqkList.add(value);
                jybList.add(value);
            }
        }
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(result));
        gjsylqkList.sort((a1, a2) -> Long.compare(a2.getJyzs(), a1.getJyzs()));
        jybList.sort((a1, a2) -> a2.getJyb().compareTo(a1.getJyb()));
        jsonObject.put("gjsylqkList", gjsylqkList);
        jsonObject.put("jybList", jybList);
        return jsonObject;
    }

    @Override
    public JSONObject ssjdfb() {
        return aqdtScreenService.ssjdfb(getAllOrgCode());
    }

    @Override
    public List<JSONObject> ajfbTop5() {
        return aqdtScreenService.ajfbTop5(getAllOrgCode());
    }

    @Override
    public JSONObject syAndCs() {
        JSONObject obj = zdJydtScreenDao.syAndCsObj(getAllOrgCode());
        if (Objects.nonNull(obj)) {
            obj.put("compare_yesterday_sy", obj.getIntValue("today_sy") - obj.getIntValue("yesterday_sy"));
            obj.put("compare_yesterday_cs", obj.getIntValue("today_cs") - obj.getIntValue("yesterday_cs"));
        }
        List<JSONObject> list = zdJydtScreenDao.syAndCsList(getAllOrgCode());
        JSONObject result = new JSONObject();
        result.put("obj", obj);
        result.put("array", list);
        return result;
    }


    private List<String> getAllOrgCode() {
        return new ArrayList<>(getAllOrgCodeType().keySet());
    }

    // 获取 机构类型 对应 机构码
    private Map<String, List<String>> getAllTypeOrgCode() {
        // 总队可以查看的机构数据；KSS：看守所，JLS：拘留所，JDS：戒毒所，JGYY：监管医院（强疗）
        String corpsViewCode = BspDbUtil.getParam("CORPS_VIEW_CODE");
        if (StringUtils.isEmpty(corpsViewCode)) {
            corpsViewCode = tempCorpsViewCode;
        }
        Map<String, List<String>> map = new HashMap<>();
        String[] split = corpsViewCode.split(",");
        for (String s : split) {
            String[] org = s.split("-");
            List<String> orgList = map.get(split[0]);
            if (CollectionUtil.isEmpty(orgList)) {
                orgList = new ArrayList<>();
                map.put(org[0], orgList);
            }
            orgList.add(org[1]);
        }
        return map;
    }

    // 获取 机构码 对应 机构类型
    private Map<String, String> getAllOrgCodeType() {
        // 总队可以查看的机构数据；KSS：看守所，JLS：拘留所，JDS：戒毒所，JGYY：监管医院（强疗）
        String corpsViewCode = BspDbUtil.getParam("CORPS_VIEW_CODE");
        if (StringUtils.isEmpty(corpsViewCode)) {
            corpsViewCode = tempCorpsViewCode;
        }
        Map<String, String> map = new HashMap<>();
        String[] split = corpsViewCode.split(",");
        for (String s : split) {
            String[] org = s.split("-");
            map.put(org[1], org[0]);
        }
        return map;
    }

    // 获取机构 - 容量 名称 等数据
    private Map<String, JSONObject> getAllPmOrgInfoMap() {
        List<JSONObject> allOrgInfo = zdJydtScreenDao.getAllOrgInfo(getAllOrgCode());
        return allOrgInfo.stream().collect(Collectors.toMap(a -> a.getString("code"), Function.identity()));
    }
}
