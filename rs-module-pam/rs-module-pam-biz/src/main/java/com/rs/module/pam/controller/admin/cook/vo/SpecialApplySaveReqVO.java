package com.rs.module.pam.controller.admin.cook.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-特殊餐申请新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SpecialApplySaveReqVO extends BaseVO{

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监室id")
    @NotEmpty(message = "监室id不能为空")
    private String roomId;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("配餐类型（字典：ZD_PCGL_PCLX ）")
    @NotEmpty(message = "配餐类型（字典：ZD_PCGL_PCLX ）不能为空")
    private String mealType;

    @ApiModelProperty("配餐开始日期")
    @NotNull(message = "配餐开始日期不能为空")
    private Date mealStartTime;

    @ApiModelProperty("配餐结束时间")
    @NotNull(message = "配餐结束时间不能为空")
    private Date mealEndTime;

    @ApiModelProperty("用餐时段，多选逗号分割（字典：ZD_PCGL_DSLX）")
    @NotEmpty(message = "用餐时段，多选逗号分割（字典：ZD_PCGL_DSLX）不能为空")
    private String mealPeriod;

    @ApiModelProperty("指定日期类型（字典：ZD_PCGL_ZDRQLX）")
    @NotEmpty(message = "指定日期类型（字典：ZD_PCGL_ZDRQLX）不能为空")
    private String specifiedDateType;

    @ApiModelProperty("指定日期，多选逗号分割")
    private String specifiedDate;

    @ApiModelProperty("申请原因")
    @NotEmpty(message = "申请原因不能为空")
    private String reason;



}
