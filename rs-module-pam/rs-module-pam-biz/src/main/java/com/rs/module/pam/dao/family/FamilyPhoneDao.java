package com.rs.module.pam.dao.family;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.family.vo.FamilyPhoneListReqVO;
import com.rs.module.pam.controller.admin.family.vo.FamilyPhonePageReqVO;
import com.rs.module.pam.entity.family.FamilyPhoneDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 监所事务管理-亲情电话 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface FamilyPhoneDao extends IBaseDao<FamilyPhoneDO> {


    default PageResult<FamilyPhoneDO> selectPage(FamilyPhonePageReqVO reqVO) {
        Page<FamilyPhoneDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<FamilyPhoneDO> wrapper = new LambdaQueryWrapperX<FamilyPhoneDO>()
                .eqIfPresent(FamilyPhoneDO::getDataSources, reqVO.getDataSources())
                .eqIfPresent(FamilyPhoneDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(FamilyPhoneDO::getTelObject, reqVO.getTelObject())
                .likeIfPresent(FamilyPhoneDO::getRelName, reqVO.getRelName())
                .eqIfPresent(FamilyPhoneDO::getTelNum, reqVO.getTelNum())
                .betweenIfPresent(FamilyPhoneDO::getApplyTime, reqVO.getApplyTime())
                .betweenIfPresent(FamilyPhoneDO::getTelTime, reqVO.getTelTime())
                .eqIfPresent(FamilyPhoneDO::getHandlerUserSfzh, reqVO.getHandlerUserSfzh())
                .likeIfPresent(FamilyPhoneDO::getHandlerUserName, reqVO.getHandlerUserName())
                .betweenIfPresent(FamilyPhoneDO::getHandlerTime, reqVO.getHandlerTime())
                .eqIfPresent(FamilyPhoneDO::getStatus, reqVO.getStatus())
                .eqIfPresent(FamilyPhoneDO::getApproverSfzh, reqVO.getApproverSfzh())
                .eqIfPresent(FamilyPhoneDO::getApproverXm, reqVO.getApproverXm())
                .betweenIfPresent(FamilyPhoneDO::getApproverTime, reqVO.getApproverTime())
                .eqIfPresent(FamilyPhoneDO::getApprovalResult, reqVO.getApprovalResult())
                .eqIfPresent(FamilyPhoneDO::getApprovalComments, reqVO.getApprovalComments())
                .eqIfPresent(FamilyPhoneDO::getActInstId, reqVO.getActInstId())
                .eqIfPresent(FamilyPhoneDO::getTaskId, reqVO.getTaskId());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(FamilyPhoneDO::getAddTime);
        }
        Page<FamilyPhoneDO> familyPhonePage = selectPage(page, wrapper);
        return new PageResult<>(familyPhonePage.getRecords(), familyPhonePage.getTotal());
    }

    default List<FamilyPhoneDO> selectList(FamilyPhoneListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<FamilyPhoneDO>()
                .eqIfPresent(FamilyPhoneDO::getDataSources, reqVO.getDataSources())
                .eqIfPresent(FamilyPhoneDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(FamilyPhoneDO::getTelObject, reqVO.getTelObject())
                .likeIfPresent(FamilyPhoneDO::getRelName, reqVO.getRelName())
                .eqIfPresent(FamilyPhoneDO::getTelNum, reqVO.getTelNum())
                .betweenIfPresent(FamilyPhoneDO::getApplyTime, reqVO.getApplyTime())
                .betweenIfPresent(FamilyPhoneDO::getTelTime, reqVO.getTelTime())
                .eqIfPresent(FamilyPhoneDO::getHandlerUserSfzh, reqVO.getHandlerUserSfzh())
                .likeIfPresent(FamilyPhoneDO::getHandlerUserName, reqVO.getHandlerUserName())
                .betweenIfPresent(FamilyPhoneDO::getHandlerTime, reqVO.getHandlerTime())
                .eqIfPresent(FamilyPhoneDO::getStatus, reqVO.getStatus())
                .eqIfPresent(FamilyPhoneDO::getApproverSfzh, reqVO.getApproverSfzh())
                .eqIfPresent(FamilyPhoneDO::getApproverXm, reqVO.getApproverXm())
                .betweenIfPresent(FamilyPhoneDO::getApproverTime, reqVO.getApproverTime())
                .eqIfPresent(FamilyPhoneDO::getApprovalResult, reqVO.getApprovalResult())
                .eqIfPresent(FamilyPhoneDO::getApprovalComments, reqVO.getApprovalComments())
                .eqIfPresent(FamilyPhoneDO::getActInstId, reqVO.getActInstId())
                .eqIfPresent(FamilyPhoneDO::getTaskId, reqVO.getTaskId())
                .orderByDesc(FamilyPhoneDO::getAddTime));
    }


    default PageResult<FamilyPhoneDO> getAppFamilyPhonePage(int pageNo, int pageSize, String jgrybm, Date startTime, Date endTime) {
        Page<FamilyPhoneDO> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapperX<FamilyPhoneDO> wrapper = new LambdaQueryWrapperX<FamilyPhoneDO>()
                .eq(FamilyPhoneDO::getJgrybm, jgrybm);
        if (Objects.nonNull(startTime)) {
            wrapper.ge(FamilyPhoneDO::getApplyTime, startTime);
        }
        if (Objects.nonNull(endTime)) {
            wrapper.lt(FamilyPhoneDO::getApplyTime, endTime);
        }
        wrapper.orderByDesc(FamilyPhoneDO::getApplyTime);

        Page<FamilyPhoneDO> letterPostPage = selectPage(page, wrapper);
        return new PageResult<>(letterPostPage.getRecords(), letterPostPage.getTotal());
    }
}
