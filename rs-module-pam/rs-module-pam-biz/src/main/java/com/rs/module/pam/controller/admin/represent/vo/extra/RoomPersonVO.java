package com.rs.module.pam.controller.admin.represent.vo.extra;

import lombok.Data;


@Data
public class RoomPersonVO {
    /**
     * 监管人员id
     */
    private String prisonerId;
    /**
     * 监管人员姓名
     */
    private String prisonerName;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /***
     * 报备信息
     */
    private String report;
    /**
     * 提讯
     */
    private String outArraignment;
    /**
     * 提询
     */
    private String outBringInterrogation;
    /**
     * 领事会见
     */
    private String outConsularMeeting;
    /**
     * 律师会见
     */
    private String outLawyerMeeting;
    /**
     * 家属会见
     */
    private String outFamilyMeeting;
    /**
     * 提解
     */
    private String outEscort;
    /**
     * 出所就医
     */
    private String outPrisonTreatment;
}
