package com.rs.module.pam.entity.bed;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import java.math.BigDecimal;

/**
 * 监所事务管理-监室床位配置 DO
 *
 * <AUTHOR>
 */
@TableName("pam_bed_config")
@KeySequence("pam_bed_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfigDO extends BaseDO {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 布局ID
     */
    private String layoutId;
    /**
     * 布局类型(01:底图,02:自定义)
     */
    private String layoutType;
    /**
     * 布局名称
     */
    private String layoutName;
    /**
     * 布局配置
     */
    private String layoutConfig;
    /**
     * 床位规格长度
     */
    private Integer bedSpaceLength;
    /**
     * 床位规格宽度
     */
    private Integer bedSpaceWidth;
    /**
     * 床位规格高度
     */
    private Integer bedSpaceHeight;
    /**
     * 床位规格承重
     */
    private BigDecimal bedSpaceBearing;
    /**
     * 自动床位配置（字典：ZD_CWGL_ZDCWPZ）
     */
    private String bedAutoConfig;
    /**
     * 布局行数
     */
    private Integer layoutRow;
    /**
     * 布局列数
     */
    private Integer layoutColumn;

}
