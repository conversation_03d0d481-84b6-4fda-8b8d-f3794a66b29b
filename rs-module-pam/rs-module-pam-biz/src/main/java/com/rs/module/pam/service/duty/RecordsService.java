package com.rs.module.pam.service.duty;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.entity.duty.RecordsDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 监所事务管理-监室值班记录 Service 接口
 *
 * <AUTHOR>
 */
public interface RecordsService extends IBaseService<RecordsDO>{


    /**
     * 获取值班记录列表
     * @param orgCode
     * @param roomId
     * @param startDate
     * @param endDate
     * @return
     */
    List<RecordsDO> selectList(@Param("orgCode") String orgCode, @Param("roomId") String roomId,
                               @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 获取指定监室当前时间的排班情况
     * @param orgCode
     * @param roomId
     * @param shiftId
     * @return
     */
    RecordsDO getNowRecords(@Param("orgCode") String orgCode, @Param("roomId") String roomId,
                            @Param("shiftId") String shiftId);


}
