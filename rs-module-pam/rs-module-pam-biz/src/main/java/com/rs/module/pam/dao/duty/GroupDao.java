package com.rs.module.pam.dao.duty;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.entity.duty.GroupDO;
import com.rs.module.pam.entity.duty.RecordsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* 监所事务管理-值班组 Dao
*
* <AUTHOR>
*/
@Mapper
public interface GroupDao extends IBaseDao<GroupDO> {

    default List<GroupDO> selectList(String orgCode, String roomId, List<String> ids) {
        return selectList(new LambdaQueryWrapperX<GroupDO>()
                .eq(GroupDO::getOrgCode, orgCode)
                .eq(GroupDO::getRoomId, roomId)
                .in(GroupDO::getId, ids));
    }

    default List<GroupDO> selectList(String orgCode, String roomId) {
        return selectList(new LambdaQueryWrapperX<GroupDO>()
                .eq(GroupDO::getOrgCode, orgCode)
                .eq(GroupDO::getRoomId, roomId));
    }

    default List<GroupDO> getByGroupName(String orgCode, String roomId, String groupName) {
        return selectList(new LambdaQueryWrapperX<GroupDO>()
                .eq(GroupDO::getOrgCode, orgCode)
                .eq(GroupDO::getRoomId, roomId)
                .eq(GroupDO::getGroupName, groupName));
    }

    Integer getRoomDutyGroupNo(@Param("orgCode") String orgCode, @Param("roomId") String roomId);

}
