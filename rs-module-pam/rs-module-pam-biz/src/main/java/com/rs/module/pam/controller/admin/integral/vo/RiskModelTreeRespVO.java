package com.rs.module.pam.controller.admin.integral.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@ApiModel(description = "风险模型结构树 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RiskModelTreeRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("风险模型名称")
    private String name;
    @ApiModelProperty("风险模型类型")
    private String type;
    @ApiModelProperty("排序")
    private Integer orderId;
    @ApiModelProperty("指标")
    private List<IndicatorTreeVO> children;
}
