package com.rs.module.pam.entity.represent;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 监所事务管理-人脸库及人员库信息进行比对 DO
 *
 * <AUTHOR>
 */
@TableName("pam_represent_check")
@KeySequence("pam_represent_check_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_represent_check")
public class CheckDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 点名批次号
     */
    private String presentNo;
    /**
     * 监室号
     */
    private String roomId;
    /**
     * 监室名字
     */
    private String roomName;
    /**
     * 监管人员id
     */
    private String prisonerId;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员名称
     */
    private String jgryxm;
    /**
     * 1-补录成功
     */
    private Integer status;

}
