package com.rs.module.pam.entity.library;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-图书管理 DO
 *
 * <AUTHOR>
 */
@TableName("pam_library_mgt")
@KeySequence("pam_library_mgt_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LibraryMgtDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 图书名称
     */
    private String name;
    /**
     * 作者
     */
    private String author;
    /**
     * 出版社
     */
    private String press;
    /**
     * 总库存
     */
    private Integer totalInventory;
    /**
     * 可借用库存
     */
    private Integer borrowedInventory;
    /**
     * 封面图片URL
     */
    private String coverImgUrl;
    /**
     * 备注
     */
    private String remark;
    /**
     * 上架状态（1：已上架，0：已下架）
     */
    private String status;

}
