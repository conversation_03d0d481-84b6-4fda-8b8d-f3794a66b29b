package com.rs.module.pam.controller.app.vo;

import com.rs.module.pam.controller.admin.info.vo.ReportItemSaveReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/19 19:58
 */
@ApiModel(description = "外屏-信息报备新增/修改 Request VO")
@Data
public class AppReportSaveSowReqVO {

    @ApiModelProperty("报备时间类型（字典：ZD_XXBB_BBSJLX）")
    @NotEmpty(message = "报备时间类型（字典：ZD_XXBB_BBSJLX）不能为空")
    private String reportTimeType;

    @ApiModelProperty("报备开始时间")
    private Date reportStartTime;

    @ApiModelProperty("报备结束时间")
    private Date reportEndTime;

    @ApiModelProperty("监室号")
    @NotEmpty(message = "监室号ID不能为空")
    private String roomId;

    @ApiModelProperty("监室名称")
    @NotEmpty(message = "监室名称不能为空")
    private String roomName;

    @ApiModelProperty("对象监管人员编码")
    @NotEmpty(message = "对象监管人员编码不能为空")
    private String objectId;

    @ApiModelProperty("报备对象名称")
    @NotEmpty(message = "报备对象名称不能为空")
    private String objectName;

    @ApiModelProperty("报备事由")
    private List<ReportItemSaveReqVO> items;

}
