package com.rs.module.pam.service.daily;

import cn.hutool.core.collection.CollectionUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.pam.entity.daily.LifeDictEventDO;
import com.rs.module.pam.service.daily.bo.LifeEventBO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import com.rs.module.pam.controller.admin.daily.vo.*;
import com.rs.module.pam.entity.daily.LifeTemplateDO;
import com.rs.module.pam.entity.daily.LifeTemplateEventDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.pam.dao.daily.LifeTemplateDao;
import com.rs.module.pam.dao.daily.LifeTemplateEventDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 监所事务管理-一日生活制度模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LifeTemplateServiceImpl extends BaseServiceImpl<LifeTemplateDao, LifeTemplateDO> implements LifeTemplateService {

    @Resource
    private LifeTemplateDao lifeTemplateDao;
    @Resource
    private LifeTemplateEventDao lifeTemplateEventDao;

    @Resource
    private LifeDictEventService lifeDictEventService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createLifeTemplate(LifeTemplateSaveReqVO createReqVO) {

        checkEventTime(createReqVO.getLifeTemplateEvents());
        // 插入
        LifeTemplateDO lifeTemplate = BeanUtils.toBean(createReqVO, LifeTemplateDO.class);
        lifeTemplateDao.insert(lifeTemplate);

        // 插入子表
        createLifeTemplateEventList(lifeTemplate.getId(), createReqVO.getLifeTemplateEvents());
        // 返回
        return lifeTemplate.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLifeTemplate(LifeTemplateSaveReqVO updateReqVO) {
        // 校验存在
        validateLifeTemplateExists(updateReqVO.getId());
        checkEventTime(updateReqVO.getLifeTemplateEvents());
        // 更新
        LifeTemplateDO updateObj = BeanUtils.toBean(updateReqVO, LifeTemplateDO.class);
        lifeTemplateDao.updateById(updateObj);

        // 更新子表
        updateLifeTemplateEventList(updateReqVO.getId(), updateReqVO.getLifeTemplateEvents());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLifeTemplate(String id) {
        // 校验存在
        validateLifeTemplateExists(id);
        // 删除
        lifeTemplateDao.deleteById(id);

        // 删除子表
        deleteLifeTemplateEventByTemplateId(id);
    }

    private void validateLifeTemplateExists(String id) {
        if (lifeTemplateDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-一日生活制度模板数据不存在");
        }
    }

    private void validateLifeTemplateEventExists(String id) {
        if (lifeTemplateEventDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-一日生活制度模板事务项数据不存在");
        }
    }

    @Override
    public LifeTemplateDO getLifeTemplate(String id) {
        return lifeTemplateDao.selectById(id);
    }

    @Override
    public PageResult<LifeTemplateDO> getLifeTemplatePage(LifeTemplatePageReqVO pageReqVO) {
        return lifeTemplateDao.selectPage(pageReqVO);
    }

    @Override
    public List<LifeTemplateDO> getLifeTemplateList(LifeTemplateListReqVO listReqVO) {
        return lifeTemplateDao.selectList(listReqVO);
    }


    // ==================== 子表（监所事务管理-一日生活制度模板关联事务） ====================

    @Override
    public List<LifeTemplateEventDO> getLifeTemplateEventListByTemplateId(String templateId) {
//        List<LifeTemplateEventDO> lifeTemplateEventDOS = lifeTemplateEventDao.selectListByTemplateId(templateId);
//        if (CollectionUtil.isNotEmpty(lifeTemplateEventDOS)) {
//            SessionUser sessionUser = SessionUserUtil.getSessionUser();
//            LifeDictEventListReqVO listReqVO = new LifeDictEventListReqVO();
//            listReqVO.setOrgCode(sessionUser.getOrgCode());
//            Map<String, String> eventMap = new HashMap<>();
//            List<LifeDictEventDO> lifeDictEventList = lifeDictEventService.getLifeDictEventList(listReqVO);
//            if (CollectionUtil.isNotEmpty(lifeDictEventList)) {
//                eventMap = lifeDictEventList.stream().collect(Collectors.toMap(LifeDictEventDO::getId,
//                        LifeDictEventDO::getEventName, (key1, key2) -> key2));
//            }
//            for (LifeTemplateEventDO lifeTemplateEventDO : lifeTemplateEventDOS) {
//                String eventId = lifeTemplateEventDO.getEventId();
//                List<String> eventIdList = Arrays.asList(eventId.split(","));
//                List<String> eventNameList = new ArrayList<>();
//                for (String eId : eventIdList) {
//                    eventNameList.add(eventMap.getOrDefault(eId, ""));
//                }
//                lifeTemplateEventDO.setEventName(CollectionUtil.join(eventNameList,","));
//            }
//        }
        return lifeTemplateEventDao.selectListByTemplateId(templateId);
    }

    @Override
    public void updateLifeTemplateEventById(LifeTemplateEventSaveReqVO updateReqVO) {
        // 校验存在
        validateLifeTemplateEventExists(updateReqVO.getId());
        // 更新
        LifeTemplateEventDO lifeTemplateEventDO = BeanUtils.toBean(updateReqVO, LifeTemplateEventDO.class);
        lifeTemplateEventDao.updateById(lifeTemplateEventDO);
    }

    @Override
    public void updateOneLifeTemplate(LifeTemplateOneSaveReqVO updateReqVO) {
        // 校验存在
        validateLifeTemplateExists(updateReqVO.getId());
        // 更新
        LifeTemplateDO updateObj = BeanUtils.toBean(updateReqVO, LifeTemplateDO.class);
        lifeTemplateDao.updateById(updateObj);
    }

    private void createLifeTemplateEventList(String templateId, List<LifeTemplateEventDO> list) {
        list.forEach(o -> o.setTemplateId(templateId));
        list.forEach(o -> lifeTemplateEventDao.insert(o));
    }

    private void updateLifeTemplateEventList(String templateId, List<LifeTemplateEventDO> list) {
        deleteLifeTemplateEventByTemplateId(templateId);
        list.forEach(o -> o.setId(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createLifeTemplateEventList(templateId, list);
    }

    private void deleteLifeTemplateEventByTemplateId(String templateId) {
        lifeTemplateEventDao.deleteByTemplateId(templateId);
    }

    private void checkEventTime(List<LifeTemplateEventDO> lifeEvents) {
        if (CollectionUtil.isNotEmpty(lifeEvents)) {
            List<LifeEventBO> list = new ArrayList<>();
            for (LifeTemplateEventDO lifeEvent : lifeEvents) {
                LifeEventBO lifeEventBO = new LifeEventBO();
                String startTime = lifeEvent.getStartTime();
                try {
                    LocalTime.parse(startTime);
                } catch (Exception e) {
                    throw new RuntimeException("开始时间（" + startTime + "）格式不正确");
                }
                String[] s = startTime.split(":");
                lifeEventBO.setH1(Integer.parseInt(s[0]));
                lifeEventBO.setM1(Integer.parseInt(s[1]));
                lifeEventBO.setT1(lifeEventBO.getH1() * 100 + lifeEventBO.getM1());
                String endTime = lifeEvent.getEndTime();
                try {
                    LocalTime.parse(endTime);
                } catch (Exception e) {
                    throw new RuntimeException("结束时间（" + endTime + "）格式不正确");
                }
                String[] e = endTime.split(":");
                String dw;
                if (Objects.nonNull(lifeEvent.getEndTimeSpan()) && lifeEvent.getEndTimeSpan().intValue() == 1) {
                    dw = "次日";
                    lifeEventBO.setH2(Integer.parseInt(e[0]) + 24);
                } else {
                    dw = "当日";
                    lifeEventBO.setH2(Integer.parseInt(e[0]));
                }
                lifeEventBO.setM2(Integer.parseInt(e[1]));
                lifeEventBO.setT2(lifeEventBO.getH2() * 100 + lifeEventBO.getM2());
                if (lifeEventBO.getT1() >= lifeEventBO.getT2()) {
                    throw new RuntimeException("开始时间（" + startTime + "）不能大于结束时间（" + endTime + "）");
                }
                lifeEventBO.setSjd(startTime + "~" + dw + endTime);
                list.add(lifeEventBO);
            }
            //升序
            list.sort(Comparator.comparing(LifeEventBO::getT2));

            for (int i = 1; i < list.size(); i++) {
                LifeEventBO before = list.get(i - 1);
                LifeEventBO now = list.get(i);
                if (before.getT2() > now.getT1()) {
                    throw new RuntimeException("事务选项时间段有交叉：时间段1：" + before.getSjd() + "，时间段2：" + now.getSjd());
                }
            }
        }
    }

}
