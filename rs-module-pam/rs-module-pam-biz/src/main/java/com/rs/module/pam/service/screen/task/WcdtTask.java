package com.rs.module.pam.service.screen.task;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.pam.service.screen.GjLargeScreenService;

import java.lang.reflect.Method;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

public class WcdtTask implements Callable<JSONObject> {

    private GjLargeScreenService gjLargeScreenService;

    private String methodName;

    private String code;

    private String codeType;

    private CountDownLatch countDownLatch;

    public WcdtTask(GjLargeScreenService gjLargeScreenService, String methodName, String code, String codeType, CountDownLatch countDownLatch) {
        this.gjLargeScreenService = gjLargeScreenService;
        this.methodName = methodName;
        this.code = code;
        this.codeType = codeType;
        this.countDownLatch = countDownLatch;
    }

    @Override
    public JSONObject call() {
        int result = 0;
        try {
            Method method = gjLargeScreenService.getClass().getMethod(methodName, String.class, String.class);
            result = (Integer) method.invoke(gjLargeScreenService, code, codeType);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            countDownLatch.countDown();
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(methodName, result);
        return jsonObject;
    }
}
