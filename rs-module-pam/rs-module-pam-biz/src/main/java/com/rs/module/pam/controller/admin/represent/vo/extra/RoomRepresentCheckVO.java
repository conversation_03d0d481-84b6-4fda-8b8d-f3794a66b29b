package com.rs.module.pam.controller.admin.represent.vo.extra;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "人脸比对")
public class RoomRepresentCheckVO {
    @ApiModelProperty(value = "监室号")
    private String roomId;

    @ApiModelProperty(value = "监室名")
    private String roomName;

    @ApiModelProperty("在押人员编号")
    private String prisonerId;

    @ApiModelProperty("在押人员姓名")
    private String prisonerName;

    @ApiModelProperty(hidden = true)
    private String photo;
}
