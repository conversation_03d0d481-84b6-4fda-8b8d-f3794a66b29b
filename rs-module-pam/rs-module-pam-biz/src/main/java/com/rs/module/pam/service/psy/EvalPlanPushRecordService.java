package com.rs.module.pam.service.psy;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanPushRecordListReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanPushRecordRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanPushRecordSaveReqVO;
import com.rs.module.pam.controller.app.vo.AppPsyTableRecordRespVO;
import com.rs.module.pam.entity.psy.EvalPlanPushRecordDO;

import java.util.List;
import java.util.Set;

/**
 * 监所事务管理-心理测评推送记录 Service 接口
 *
 * <AUTHOR>
 */
public interface EvalPlanPushRecordService extends IBaseService<EvalPlanPushRecordDO> {

    /**
     * 创建监所事务管理-心理测评推送记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createEvalPlanPushRecord(EvalPlanPushRecordSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-心理测评推送记录
     *
     * @param updateReqVO 更新信息
     */
    void updateEvalPlanPushRecord(EvalPlanPushRecordSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-心理测评推送记录
     *
     * @param id 编号
     */
    void deleteEvalPlanPushRecord(String id);

    /**
     * 获得监所事务管理-心理测评推送记录
     *
     * @param id 编号
     * @return 监所事务管理-心理测评推送记录
     */
    EvalPlanPushRecordDO getEvalPlanPushRecord(String id);

    /**
     * 获得监所事务管理-心理测评推送记录列表
     *
     * @param listReqVO 查询条件
     * @return 监所事务管理-心理测评推送记录列表
     */
    List<EvalPlanPushRecordRespVO> getEvalPlanPushRecordList(EvalPlanPushRecordListReqVO listReqVO);


    Long getEvalPlanPushRecordListCount(EvalPlanPushRecordListReqVO listReqVO);

    /**
     * 根据测评编号获取推送记录
     *
     * @param evalNo
     * @return
     */
    EvalPlanPushRecordDO getByEvalNo(String evalNo);

    /**
     * 内屏- 获得可以测试量表列表
     *
     * @param jgrybm
     * @param tableType
     * @return
     */
    List<AppPsyTableRecordRespVO> tableList(String jgrybm, String tableType);

    /**
     * 判断是否存在推送记录
     * @param planCode
     * @param tableIds
     * @param jgrybmsSet
     * @return
     */
    List<String> existPushRecord(String planCode, List<String> tableIds, Set<String> jgrybmsSet);
}
