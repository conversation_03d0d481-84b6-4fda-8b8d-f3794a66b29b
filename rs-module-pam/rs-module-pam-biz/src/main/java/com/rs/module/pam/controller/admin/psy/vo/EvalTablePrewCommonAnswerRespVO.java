package com.rs.module.pam.controller.admin.psy.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalTime;

/**
 * 答题卡预览-常规模式-待答案VO
 *
 * <AUTHOR>
 * @Date 2025/6/16 17:33
 */
@Data
public class EvalTablePrewCommonAnswerRespVO extends EvalTablePrewCommonRespVO {

    @ApiModelProperty("测评得分")
    private BigDecimal score;

    @ApiModelProperty("测评结果")
    private String evalResults;

    @ApiModelProperty("作答用时")
    private LocalTime timeSpentAnswer;
}
