package com.rs.module.pam.controller.admin.info;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.pam.controller.admin.info.vo.ReportApproveReqVO;
import com.rs.module.pam.controller.admin.info.vo.ReportHistoryRespVO;
import com.rs.module.pam.controller.admin.info.vo.ReportRespVO;
import com.rs.module.pam.controller.admin.info.vo.ReportSaveReqVO;
import com.rs.module.pam.service.info.ReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "信息报备")
@RestController
@RequestMapping("/pam/info/report")
@Validated
public class ReportController {

    @Resource
    private ReportService reportService;

    @PostMapping("/create")
    @ApiOperation(value = "创建-信息报备")
    public CommonResult<String> createReport(@Valid @RequestBody ReportSaveReqVO createReqVO) {
        return success(reportService.createReport(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新-信息报备")
    public CommonResult<Boolean> updateReport(@Valid @RequestBody ReportSaveReqVO updateReqVO) {
        reportService.updateReport(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得-信息报备")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ReportRespVO> getReport(@RequestParam("id") String id) {
        ReportRespVO bean = reportService.getReportRespVO(id);
        return success(bean);
    }

    @GetMapping("/getHistoryReport")
    @ApiOperation(value = "获得-信息报备历史记录")
    @ApiImplicitParam(name = "jgrybm", value = "监管人员编码")
    public CommonResult<List<ReportHistoryRespVO>> getHistoryReport(@RequestParam("jgrybm") String jgrybm) {
        List<ReportHistoryRespVO> historyRespVOS = reportService.getHistoryReport(jgrybm);
        return success(historyRespVOS);
    }

    @PostMapping("/approve")
    @ApiOperation(value = "信息报备审批")
    public CommonResult<Boolean> leaderApprove(@Valid @RequestBody ReportApproveReqVO approveReqVO) {
        reportService.policeApprove(approveReqVO);
        return success(true);
    }

}
