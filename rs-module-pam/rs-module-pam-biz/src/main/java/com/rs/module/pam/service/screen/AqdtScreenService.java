package com.rs.module.pam.service.screen;


import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.pam.entity.screen.FocusOnPersonnelDO;
import com.rs.module.pam.entity.screen.WgdjDO;

import java.util.List;

public interface AqdtScreenService {

    JSONObject jygl(String code);

    JSONObject jyrybhqs(String code);

    List<JSONObject> gjqryfb(String code);

    JSONObject nldfb(String code);

    JSONObject ssjdfb(List<String> list);

    List<JSONObject> ajfbTop5(List<String> list);

    JSONObject jycqtj(String code);

    JSONObject jqdcstj(String code);

    JSONObject fxryfb(String code);

    JSONObject fxryqs(String code, String type);

    JSONObject gzryCount(String code);

    PageResult<FocusOnPersonnelDO> gzryPage(int pageNo, int pageSize, String code, String bizType);

    JSONObject wgqs(String code, String type);

    PageResult<WgdjDO> wggjPage(int pageNo, int pageSize, String code, String type, String wglb, String handleStatus);

    JSONObject wggjCount(String code, String type, String wglb);
}
