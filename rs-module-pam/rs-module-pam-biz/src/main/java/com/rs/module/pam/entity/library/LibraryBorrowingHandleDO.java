package com.rs.module.pam.entity.library;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-图书借阅处理 DO
 *
 * <AUTHOR>
 */
@TableName("pam_library_borrowing_handle")
@KeySequence("pam_library_borrowing_handle_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LibraryBorrowingHandleDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 借阅id
     */
    private String borrowingId;
    /**
     * 借阅状态（字典：ZD_TSJY_JYZT）
     */
    private String status;
    /**
     * 处理人id
     */
    private String handleUserSfzh;
    /**
     * 处理人姓名
     */
    private String handleUser;
    /**
     * 处理时间
     */
    private Date handleTime;

}
