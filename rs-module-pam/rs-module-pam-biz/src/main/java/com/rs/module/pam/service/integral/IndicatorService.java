package com.rs.module.pam.service.integral;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.integral.vo.IndicatorRespVO;
import com.rs.module.pam.controller.admin.integral.vo.IndicatorSaveReqVO;
import com.rs.module.pam.entity.integral.IndicatorDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 监所事务管理-积分指标 Service 接口
 *
 * <AUTHOR>
 */
public interface IndicatorService extends IBaseService<IndicatorDO>{

    /**
     * 创建监所事务管理-积分指标
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    IndicatorRespVO createIndicator(@Valid IndicatorSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-积分指标
     *
     * @param updateReqVO 更新信息
     */
    IndicatorRespVO updateIndicator(@Valid IndicatorSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-积分指标
     *
     * @param id 编号
     */
    void deleteIndicator(String id);

    /**
     * 获得监所事务管理-积分指标
     *
     * @param id 编号
     * @return 监所事务管理-积分指标
     */
    IndicatorDO getIndicator(String id);

    /**
     * 获得监所事务管理-积分指标
     *
     * @param orgCode 机构编号
     * @return 监所事务管理-积分指标
     */
    List<IndicatorDO> getIndicatorByOrgCode(String orgCode);

    /**
     * 获得监所事务管理-积分指标
     *
     * @param riskModelId 风险模型编号
     * @return 监所事务管理-积分指标
     */
    List<IndicatorDO> getIndicatorTypeByRiskModel(String riskModelId);

}
