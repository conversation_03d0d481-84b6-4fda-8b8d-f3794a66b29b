package com.rs.module.pam.controller.admin.library;

import com.alibaba.druid.util.StringUtils;
import com.bsp.security.util.SessionUserUtil;
import com.gosun.zhjg.common.util.StringUtil;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.pam.controller.admin.library.vo.*;
import com.rs.module.pam.entity.library.LibraryMgtDO;
import com.rs.module.pam.service.library.LibraryMgtService;

@Api(tags = "监所事务管理-图书管理")
@RestController
@RequestMapping("/pam/library/libraryMgt")
@Validated
public class LibraryMgtController {

    @Resource
    private LibraryMgtService libraryMgtService;

    @PostMapping("/create")
    @ApiOperation(value = "创建监所事务管理-图书管理")
    public CommonResult<String> createLibraryMgt(@Valid @RequestBody LibraryMgtSaveReqVO createReqVO) {
        return success(libraryMgtService.createLibraryMgt(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新监所事务管理-图书管理")
    public CommonResult<Boolean> updateLibraryMgt(@Valid @RequestBody LibraryMgtSaveReqVO updateReqVO) {
        libraryMgtService.updateLibraryMgt(updateReqVO);
        return success(true);
    }

    @PostMapping("/shelvingAndDelisting")
    @ApiOperation(value = "上下架")
    public CommonResult<Boolean> shelvingAndDelisting(@Valid @RequestBody LibraryMgtSXJReqVO updateReqVO) {
        libraryMgtService.shelvingAndDelisting(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除监所事务管理-图书管理")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteLibraryMgt(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           libraryMgtService.deleteLibraryMgt(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得监所事务管理-图书管理")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<LibraryMgtRespVO> getLibraryMgt(@RequestParam("id") String id) {
        LibraryMgtDO libraryMgt = libraryMgtService.getLibraryMgt(id);
        return success(BeanUtils.toBean(libraryMgt, LibraryMgtRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得监所事务管理-图书管理分页")
    public CommonResult<PageResult<LibraryMgtRespVO>> getLibraryMgtPage(@Valid @RequestBody LibraryMgtPageReqVO pageReqVO) {
        if(StringUtils.isEmpty(pageReqVO.getOrgCode())){
            pageReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        PageResult<LibraryMgtDO> pageResult = libraryMgtService.getLibraryMgtPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LibraryMgtRespVO.class));
    }

    /*@PostMapping("/list")
    @ApiOperation(value = "获得监所事务管理-图书管理列表")
    public CommonResult<List<LibraryMgtRespVO>> getLibraryMgtList(@Valid @RequestBody LibraryMgtListReqVO listReqVO) {
        List<LibraryMgtDO> list = libraryMgtService.getLibraryMgtList(listReqVO);
        return success(BeanUtils.toBean(list, LibraryMgtRespVO.class));
    }*/
}
