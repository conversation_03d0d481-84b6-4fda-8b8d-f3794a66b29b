package com.rs.module.pam.entity.attention;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

/**
 * 监所事务管理-重点人员关注反馈 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 监所事务管理-重点人员关注反馈新增/修改 Request VO")
@TableName("pam_attention_feedback")
@KeySequence("pam_attention_feedback_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FeedbackDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;
    /**
     * 重点人员关注登记ID
     */
    @ApiModelProperty("重点人员关注登记ID")
    private String attentionId;
    /**
     * 反馈内容
     */
    @ApiModelProperty("反馈内容")
    private String feedbackContent;
    /**
     * 登记经办人
     */
    @ApiModelProperty("登记经办人")
    private String operatorSfzh;
    /**
     * 登记经办人姓名
     */
    @ApiModelProperty("登记经办人姓名")
    private String operatorXm;
    /**
     * 登记时间
     */
    @ApiModelProperty("登记时间")
    private Date operatorTime;

}
