package com.rs.module.pam.entity.duty;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 监所事务管理-监室值班记录关联签到 DO
 *
 * <AUTHOR>
 */
@TableName("pam_duty_records_signin")
@KeySequence("pam_duty_records_signin_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_duty_records_signin")
public class RecordsSigninDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 值班记录：pam_duty_records.id
     */
    private String recordsId;
    /**
     * 值班日期
     */
    private Date dutyDate;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 签到状态（字典：ZD_ZBGL_QDZT）
     */
    private String signinStatus;
    /**
     * 签到时间
     */
    private Date signinTime;
    /**
     * 签到是否有效
     */
    private Integer isSigninValid;
    /**
     * 抓拍图片地址
     */
    private String snapPictureUrl;

}
