package com.rs.module.pam.controller.admin.attention;

import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.pam.controller.admin.attention.vo.*;
import com.rs.module.pam.entity.attention.FeedbackDO;
import com.rs.module.pam.entity.attention.PrisonerDO;
import com.rs.module.pam.service.attention.AttentionPrisonerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "重点人员关注登记")
@RestController
@RequestMapping("/pam/attention/prisoner")
@Validated
public class AttentionPrisonerController {

    @Resource
    private AttentionPrisonerService attentionPrisonerService;

    @PostMapping("/create")
    @ApiOperation(value = "创建重点人员关注登记")
    @BusTrace(busType = BusTypeEnum.YEWU_ZDRYGZ, condition = "false", content = "{\"监管人员编号\":\"{{#createReqVO.jgrybm}}\"," +
            "\"关注开始时间\":\"{{#createReqVO.beginTime}}\",\"关注结束时间\":\"{{#createReqVO.endTime}}\",\"关注理由\":\"{{#createReqVO.attentionReason}}\"}")
    public CommonResult<String> createPrisoner(@Valid @RequestBody PrisonerSaveReqVO createReqVO) {
        return success(attentionPrisonerService.createPrisoner(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新重点人员关注登记", hidden = true)
    public CommonResult<Boolean> updatePrisoner(@Valid @RequestBody PrisonerSaveReqVO updateReqVO) {
        attentionPrisonerService.updatePrisoner(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除重点人员关注登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    @BusTrace(busType = BusTypeEnum.YEWU_ZDRYGZ, condition = "false", content = "{\"重点关注id\":\"{{#ids}}\"}")
    public CommonResult<Boolean> deletePrisoner(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           attentionPrisonerService.deletePrisoner(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得重点人员关注登记")
    @ApiImplicitParam(name = "id", value = "编号")
    @BusTrace(busType = BusTypeEnum.YEWU_ZDRYGZ, condition = "false", content = "{\"重点关注id\":\"{{#id}}\"}")
    public CommonResult<PrisonerRespVO> getPrisoner(@RequestParam("id") String id) {
        PrisonerDO prisoner = attentionPrisonerService.getPrisoner(id);
        return success(BeanUtils.toBean(prisoner, PrisonerRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得重点人员关注登记分页")
    public CommonResult<PageResult<PrisonerRespVO>> getPrisonerPage(@Valid @RequestBody PrisonerPageReqVO pageReqVO) {
        PageResult<PrisonerDO> pageResult = attentionPrisonerService.getPrisonerPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PrisonerRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得重点人员关注登记列表")
    public CommonResult<List<PrisonerRespVO>> getPrisonerList(@Valid @RequestBody PrisonerListReqVO listReqVO) {
        List<PrisonerDO> list = attentionPrisonerService.getPrisonerList(listReqVO);
        return success(BeanUtils.toBean(list, PrisonerRespVO.class));
    }

    @PostMapping("/approvalProcess")
    @ApiOperation(value = "审批重点关注人员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "重点关注记录编号"),
            @ApiImplicitParam(name = "approvalResult", value = "审批结果(1：同意，2：不同意)"),
            @ApiImplicitParam(name = "approvalComments", value = "审批意见")
    })
    @BusTrace(busType = BusTypeEnum.YEWU_ZDRYGZ, condition = "false", content = "{\"重点关注id\":\"{{#params.id}}\"," +
            "\"审批结果\":\"{{#params.approvalResult}}\",\"审批意见\":\"{{#params.approvalComments}}\"}")
    public CommonResult<Boolean> approvalProcess(@Valid @RequestBody JSONObject params) {
        String id = params.getString("id");
        String approvalResult = params.getString("approvalResult");
        String approvalComments = params.getString("approvalComments");
        attentionPrisonerService.approvalProcess(id, approvalResult, approvalComments);
        return success(true);
    }
    // ==================== 子表（重点人员关注反馈） ====================

    @PostMapping("/feedback/create")
    @ApiOperation(value = "创建重点人员关注反馈")
    public CommonResult<String> createFeedback(@Valid @RequestBody FeedbackSaveReqVO createReqVO) {
        return success(attentionPrisonerService.createFeedback(createReqVO));
    }

    @GetMapping("/feedback/list-by-attention-id")
    @ApiOperation(value = "获得重点人员关注反馈列表")
    @ApiImplicitParam(name = "attentionId", value = "重点人员关注登记ID")
    public CommonResult<List<FeedbackDO>> getFeedbackListByAttentionId(@RequestParam("attentionId") String attentionId) {
        return success(attentionPrisonerService.getFeedbackListByAttentionId(attentionId));
    }
    @GetMapping("getPrisonerByOrgCode")
    @ApiOperation(value = "获得机构下重点人员列表")
    @ApiImplicitParam(name = "orgCode", value = "机构编码")
    public CommonResult<List<RoomPrisonerRespVO>> getPrisonerByOrgCode(@RequestParam("orgCode") String orgCode) {
        return success(attentionPrisonerService.getPrisonerByOrgCode(orgCode));
    }
}
