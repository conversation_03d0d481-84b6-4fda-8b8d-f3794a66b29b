package com.rs.module.pam.controller.admin.information.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-监室信息发布分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PublishPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("信息类型（字典：ZD_JSXXFB_XXLX）")
    private String infoType;

    @ApiModelProperty("信息内容")
    private String infoContentText;

    @ApiModelProperty("信息内容")
    private String infoContentHtml;

    @ApiModelProperty("登记经办人")
    private String operatorSfzh;

    @ApiModelProperty("登记经办人姓名")
    private String operatorXm;

    @ApiModelProperty("登记时间")
    private Date[] operatorTime;

    @ApiModelProperty("登记状态")
    private String status;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;

    @ApiModelProperty("业务类型 01 监室信息发布  02 教育读本发布")
    private String bizType;
}
