package com.rs.module.pam.controller.admin.psy.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.module.pam.dto.QuestionDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评量表预览 Response VO")
@Data
public class EvalTablePrewCommonRespVO implements TransPojo {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("量表名称")
    private String name;
    @ApiModelProperty("量表编号")
    private String tableNo;
    @ApiModelProperty("使用状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_PSY_SYZT")
    private String usageStatus;
    @ApiModelProperty("量表类型（字典：ZD_XLPC_LBLX）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XLPC_LBLX")
    private String tableType;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("总题量")
    private Integer totalQuestionNumber;
    @ApiModelProperty("预计时长，单位分钟")
    private Integer estimatedDuration;
    @ApiModelProperty("单选题")
    private QuestionDTO dxt;
    @ApiModelProperty("多选题")
    private QuestionDTO fxt;
    @ApiModelProperty("简答题")
    private QuestionDTO jdt;

}
