package com.rs.module.pam.service.attention;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.pam.controller.admin.attention.vo.*;
import com.rs.module.pam.entity.attention.FeedbackDO;
import com.rs.module.pam.entity.attention.PrisonerDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 监所事务管理-重点人员关注登记 Service 接口
 *
 * <AUTHOR>
 */
public interface AttentionPrisonerService extends IBaseService<PrisonerDO>{

    /**
     * 创建监所事务管理-重点人员关注登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrisoner(@Valid PrisonerSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-重点人员关注登记
     *
     * @param updateReqVO 更新信息
     */
    void updatePrisoner(@Valid PrisonerSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-重点人员关注登记
     *
     * @param id 编号
     */
    void deletePrisoner(String id);

    /**
     * 获得监所事务管理-重点人员关注登记
     *
     * @param id 编号
     * @return 监所事务管理-重点人员关注登记
     */
    PrisonerDO getPrisoner(String id);

    /**
     * 获得每个监室的重点人员关注登记
     *
     * @param orgCode 机构编号
     * @return
     */
    List<RoomPrisonerRespVO> getPrisonerByOrgCode(String orgCode);

    /**
    * 获得监所事务管理-重点人员关注登记分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-重点人员关注登记分页
    */
    PageResult<PrisonerDO> getPrisonerPage(PrisonerPageReqVO pageReqVO);

    /**
    * 获得监所事务管理-重点人员关注登记列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-重点人员关注登记列表
    */
    List<PrisonerDO> getPrisonerList(PrisonerListReqVO listReqVO);


    /**
     * 重点关注人员审批流程
     */
    void approvalProcess(String id, String approvalResult, String approvalComments);

    // ==================== 子表（监所事务管理-重点人员关注反馈） ====================

    /**
     * 获得监所事务管理-重点人员关注反馈列表
     *
     * @param attentionId 重点人员关注登记ID
     * @return 监所事务管理-重点人员关注反馈列表
     */
    List<FeedbackDO> getFeedbackListByAttentionId(String attentionId);

    /**
     * 创建监所事务管理-重点人员关注反馈
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createFeedback(@Valid FeedbackSaveReqVO createReqVO);

    /**
     * 获得监所事务管理-重点人员数量
     * @param orgCode 机构编号
     * @param roomCode 监室编号
     * @return
     */
    Integer getPrisonerCount(String orgCode, String roomCode);

}
