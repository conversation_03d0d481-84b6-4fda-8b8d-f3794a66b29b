package com.rs.module.pam.controller.app.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "仓内屏 -特殊餐申请新增 Request VO")
@Data
public class AppSpecialApplySaveSowReqVO {

    @ApiModelProperty("监室id")
    @NotEmpty(message = "监室id不能为空")
    private String roomId;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("配餐类型（字典：ZD_PCGL_PCLX ）")
    @NotEmpty(message = "配餐类型（字典：ZD_PCGL_PCLX ）不能为空")
    private String mealType;

    @ApiModelProperty("配餐开始日期")
    private Date mealStartTime;

    @ApiModelProperty("配餐结束时间")
    private Date mealEndTime;

    @ApiModelProperty("用餐时段，多选逗号分割（字典：ZD_PCGL_DSLX）")
    @NotEmpty(message = "用餐时段，多选逗号分割（字典：ZD_PCGL_DSLX）不能为空")
    private String mealPeriod;

    @ApiModelProperty("指定日期类型（字典：ZD_PCGL_ZDRQLX）")
    @NotEmpty(message = "指定日期类型（字典：ZD_PCGL_ZDRQLX）不能为空")
    private String specifiedDateType;

    @ApiModelProperty("指定日期，多选逗号分割")
    private String specifiedDate;

    @ApiModelProperty("申请原因")
    @NotEmpty(message = "申请原因不能为空")
    private String reason;

    @ApiModelProperty("主管民警身份证号")
    @NotEmpty(message = "主管民警身份证号不能为空")
    private String regOperatorSfzh;

    @ApiModelProperty("主管民警姓名")
    @NotEmpty(message = "主管民警姓名不能为空")
    private String regOperatorXm;

}
