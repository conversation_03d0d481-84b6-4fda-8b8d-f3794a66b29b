package com.rs.module.pam.entity.daily;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-一日生活制度 DO
 *
 * <AUTHOR>
 */
@TableName("pam_daily_life")
@KeySequence("pam_daily_life_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LifeDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 制度名称
     */
    private String name;
    /**
     * 周期设置（字典：ZD_YRSHZD_ZQSZ）
     */
    private String cycleSetting;
    /**
     * cycle_config
     */
    private String cycleConfig;
    /**
     * 自定义时间
     */
    private String customizeTime;
    /**
     * 报备状态（字典：ZD_XXBB_BBZT）
     */
    private String status;
    /**
     * 审批人身份证号
     */
    private String approverSfzh;
    /**
     * 审批人姓名
     */
    private String approverXm;
    /**
     * 审批时间
     */
    private Date approverTime;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 领导签名
     */
    private String approvalAutograph;
    /**
     * 领导签名日期
     */
    private Date approvalAutographTime;
    /**
     * 审核意见
     */
    private String approvalComments;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

}
