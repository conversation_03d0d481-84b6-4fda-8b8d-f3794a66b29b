package com.rs.module.pam.controller.admin.psy.vo;

import io.swagger.annotations.ApiModel;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评量表关联提目分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EvalTableQuestionPageReqVO extends PageParam {

    @ApiModelProperty("量表ID")
    private String tableId;

    @ApiModelProperty("题目内容")
    private String questionText;

    @ApiModelProperty("题目类型（字典：ZD_XLPC_TMLX）")
    private String questionType;

    @ApiModelProperty("计分规则")
    private String scoreRule;

    @ApiModelProperty("排序属性")
    List<OrderItem> orderFields;
}
