package com.rs.module.pam.controller.admin.represent.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-人员点名记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PersonRepresentPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员名称")
    private String jgryxm;

    @ApiModelProperty("签到状态(1:已签到")
    private String signStatus;

    @ApiModelProperty("是否外出（1：是")
    private String isOut;

    @ApiModelProperty("外出原因")
    private String outReason;

    @ApiModelProperty("签到时间")
    private Date[] signTime;

    @ApiModelProperty("关联监室点名表id")
    private String roomPresentId;

    @ApiModelProperty("温度")
    private String temperature;

    @ApiModelProperty("1-视频点名")
    private Integer isVideo;

    @ApiModelProperty("视频确认民警编号")
    private String createUserSfzh;

    @ApiModelProperty("视频确认民警")
    private String createUser;

    @ApiModelProperty("视频确认时间")
    private Date[] confirmDate;

    @ApiModelProperty("1-正常 2-异常")
    private Integer temperatureState;

    @ApiModelProperty("1-报备")
    private Integer isReport;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
