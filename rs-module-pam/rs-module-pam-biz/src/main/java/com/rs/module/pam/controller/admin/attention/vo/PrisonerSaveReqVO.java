package com.rs.module.pam.controller.admin.attention.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.pam.entity.attention.FeedbackDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-重点人员关注登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonerSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("开始关注日期")
    @NotNull(message = "开始关注日期不能为空")
    private Date beginTime;

    @ApiModelProperty("结束关注日期")
    @NotNull(message = "结束关注日期不能为空")
    private Date endTime;

    @ApiModelProperty("关注理由")
    @NotEmpty(message = "关注理由不能为空")
    private String attentionReason;

    @ApiModelProperty("关注频率类型（字典：ZD_ZDRYGZ_GZPLLX）")
    @NotEmpty(message = "关注频率类型（字典：ZD_ZDRYGZ_GZPLLX）不能为空")
    private String frequencyType;

    @ApiModelProperty("关注频率")
    @NotNull(message = "关注频率不能为空")
    private Integer frequency;

    @ApiModelProperty("关注层级（字典：ZD_ZDRYGZ_GZCJ）")
    private String attentionLevel;

    @ApiModelProperty("推送岗位，多个逗号分割")
    private String pushJobPositions;

    @ApiModelProperty("登记状态（字典：ZD_ZDRYGZ_GZZT）")
    private String regStatus;

    @ApiModelProperty("登记经办人")
    private String regOperatorSfzh;

    @ApiModelProperty("登记经办人姓名")
    private String regOperatorXm;

    @ApiModelProperty("登记时间")
    private Date regTime;

    @ApiModelProperty("所领导审批人身份证号")
    private String leaderApproverSfzh;

    @ApiModelProperty("所领导审批人姓名")
    private String leaderApproverXm;

    @ApiModelProperty("所领导审批时间")
    private Date leaderApproverTime;

    @ApiModelProperty("所领导审批结果")
    private String leaderApprovalResult;

    @ApiModelProperty("所领导审核意见")
    private String leaderApprovalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

}
