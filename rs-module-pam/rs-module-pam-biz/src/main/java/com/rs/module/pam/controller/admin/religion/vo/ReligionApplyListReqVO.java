package com.rs.module.pam.controller.admin.religion.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-宗教申请列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ReligionApplyListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("数据来源")
    private String dataSources;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("信仰宗教")
    private String religion;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date[] approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

}
