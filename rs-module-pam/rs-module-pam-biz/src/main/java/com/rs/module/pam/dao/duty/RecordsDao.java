package com.rs.module.pam.dao.duty;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.entity.duty.RecordsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* 监所事务管理-监室值班记录 Dao
*
* <AUTHOR>
*/
@Mapper
public interface RecordsDao extends IBaseDao<RecordsDO> {


    default List<RecordsDO> selectList(String orgCode, String roomId, Date startDate, Date endDate) {
        return selectList(new LambdaQueryWrapperX<RecordsDO>()
                .eqIfPresent(RecordsDO::getOrgCode, orgCode)
                .eqIfPresent(RecordsDO::getRoomId, roomId)
                .betweenIfPresent(RecordsDO::getDutyDate, startDate, endDate));
    }

    /**
     * 删除指定日期后 排班情况
     *
     * @param roomId
     * @param dutyDate
     * @return
     */
    int deleteByDutyDate(@Param("orgCode") String orgCode, @Param("roomId") String roomId, @Param("dutyDate") Date dutyDate);

    /**
     * 删除指定监室指定人员的排班情况
     * @param dutyDate
     * @param jgrybmList
     * @return
     */
    int deleteDutyRecordsByPrisoner(@Param("orgCode") Date orgCode, @Param("roomId") String roomId,
                                    @Param("dutyDate") Date dutyDate, @Param("jgrybmList") List<String> jgrybmList);

    /**
     *  获取指定班次的当前排班情况
     * @param orgCode
     * @param roomId
     * @param shiftId
     * @return
     */
    RecordsDO getNowRecords(@Param("orgCode") String orgCode, @Param("roomId") String roomId, @Param("shiftId") String shiftId);


}
