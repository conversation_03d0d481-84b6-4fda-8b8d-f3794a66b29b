package com.rs.module.pam.controller.admin.device;

import com.rs.framework.common.enums.DataSourceAppEnum;
import com.rs.module.pam.enums.DeviceRepairRegStatusEnum;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.pam.controller.admin.device.vo.*;
import com.rs.module.pam.entity.device.DeviceRepairRegDO;
import com.rs.module.pam.service.device.DeviceRepairRegService;

@Api(tags = "监所事务管理-设备报修登记")
@RestController
@RequestMapping("/pam/device/deviceRepairReg")
@Validated
public class DeviceRepairRegController {

    @Resource
    private DeviceRepairRegService deviceRepairRegService;

    @PostMapping("/create")
    @ApiOperation(value = "创建监所事务管理-设备报修登记")
    public CommonResult<String> createDeviceRepairReg(@Valid @RequestBody DeviceRepairRegSaveReqVO createReqVO) {
        createReqVO.setDataSources(DataSourceAppEnum.ACP.getCode());
        // 实战平台新增 状态直接 待维修
        createReqVO.setStatus(DeviceRepairRegStatusEnum.DWX.getCode());
        return success(deviceRepairRegService.createDeviceRepairReg(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新监所事务管理-设备报修登记")
    public CommonResult<Boolean> updateDeviceRepairReg(@Valid @RequestBody DeviceRepairRegSaveReqVO updateReqVO) {
        deviceRepairRegService.updateDeviceRepairReg(updateReqVO);
        return success(true);
    }

    @PostMapping("/detailed")
    @ApiOperation(value = "派单-设备报修")
    public CommonResult<Boolean> approvalDeviceRepairReg(@Valid @RequestBody DeviceRepairRegApprovalReqVO approvalReqVO) {
        deviceRepairRegService.approvalDeviceRepairReg(approvalReqVO);
        return success(true);
    }

    @PostMapping("/handle")
    @ApiOperation(value = "处置-设备报修")
    public CommonResult<Boolean> handleDeviceRepairReg(@Valid @RequestBody DeviceRepairRegHandleReqVO handleReqVO) {
        deviceRepairRegService.handleDeviceRepairReg(handleReqVO);
        return success(true);
    }

    @PostMapping("/ignore/change")
    @ApiOperation(value = "忽略操作-设备报修")
    public CommonResult<Boolean> ignoreChangeDeviceRepairReg(@Valid @RequestBody DeviceRepairRegHandleReqVO handleReqVO) {
        deviceRepairRegService.ignoreChangeDeviceRepairReg(handleReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除监所事务管理-设备报修登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteDeviceRepairReg(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           deviceRepairRegService.deleteDeviceRepairReg(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得监所事务管理-设备报修登记")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<DeviceRepairRegRespVO> getDeviceRepairReg(@RequestParam("id") String id) {
        DeviceRepairRegDO deviceRepairReg = deviceRepairRegService.getDeviceRepairReg(id);
        return success(BeanUtils.toBean(deviceRepairReg, DeviceRepairRegRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得监所事务管理-设备报修登记分页")
    public CommonResult<PageResult<DeviceRepairRegRespVO>> getDeviceRepairRegPage(@Valid @RequestBody DeviceRepairRegPageReqVO pageReqVO) {
        PageResult<DeviceRepairRegDO> pageResult = deviceRepairRegService.getDeviceRepairRegPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DeviceRepairRegRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得监所事务管理-设备报修登记列表")
    public CommonResult<List<DeviceRepairRegRespVO>> getDeviceRepairRegList(@Valid @RequestBody DeviceRepairRegListReqVO listReqVO) {
        List<DeviceRepairRegDO> list = deviceRepairRegService.getDeviceRepairRegList(listReqVO);
        return success(BeanUtils.toBean(list, DeviceRepairRegRespVO.class));
    }
}
