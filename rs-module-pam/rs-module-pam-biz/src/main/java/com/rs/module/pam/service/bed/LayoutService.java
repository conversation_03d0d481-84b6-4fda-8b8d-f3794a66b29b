package com.rs.module.pam.service.bed;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.pam.controller.admin.bed.vo.LayoutListReqVO;
import com.rs.module.pam.controller.admin.bed.vo.LayoutPageReqVO;
import com.rs.module.pam.controller.admin.bed.vo.LayoutSaveReqVO;
import com.rs.module.pam.entity.bed.LayoutDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 监所事务管理-监室床位布局 Service 接口
 *
 * <AUTHOR>
 */
public interface LayoutService extends IBaseService<LayoutDO>{

    /**
     * 创建监所事务管理-监室床位布局
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createLayout(@Valid LayoutSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-监室床位布局
     *
     * @param updateReqVO 更新信息
     */
    void updateLayout(@Valid LayoutSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-监室床位布局
     *
     * @param id 编号
     */
    void deleteLayout(String id);

    /**
     * 获得监所事务管理-监室床位布局
     *
     * @param id 编号
     * @return 监所事务管理-监室床位布局
     */
    LayoutDO getLayout(String id);

    /**
    * 获得监所事务管理-监室床位布局分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-监室床位布局分页
    */
    PageResult<LayoutDO> getLayoutPage(LayoutPageReqVO pageReqVO);

    /**
    * 获得监所事务管理-监室床位布局列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-监室床位布局列表
    */
    List<LayoutDO> getLayoutList(LayoutListReqVO listReqVO);

}
