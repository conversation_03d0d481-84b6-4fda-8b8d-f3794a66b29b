package com.rs.module.pam.controller.admin.room.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 实战平台-监管管理-区域
 *
 * <AUTHOR>
 */
@Data
public class AreaPrisonRoomExportVO {
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 是否启用:1启用,0停用
     */
    private String statusName;
    /**
     * 关押量
     */
    private Integer imprisonmentAmount;
    /**
     * 监室类型
     */
    private String roomTypeName;
    /**
     * 人员性别
     */
    private String roomSexName;
    /**
     * 监区名称
     */
    private String areaName;
    /**
     * 监室面积
     */
    private BigDecimal roomArea;
    /**
     * 人均铺位面积
     */
    private BigDecimal avgBedsArea;
    /**
     * 主管民警
     */
    private String sponsorNames;
    /**
     * 协管民警
     */
    private String assistNames;

}
