package com.rs.module.pam.controller.admin.represent.vo.extra;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


@Data
@ApiModel("点名人员信息vo")
public class RepresentPersonVO {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 人员id
     */
    @ApiModelProperty(value = "监管人员编码")
    private String prisonerId;
    /**
     * 人员name
     */
    @ApiModelProperty(value = "监管人员名称")
    private String prisonerName;
    /**
     * 人员id
     */
    @ApiModelProperty(value = "监管人员编码")
    private String jgrybm;
    /**
     * 签到状态
     */
    @ApiModelProperty(value = "签到状态（0：未点，1：正常 2:外出 3：视频点名")
    private String signStatus;
    /**
     * 是否外出人员
     */
    @ApiModelProperty(value = "是否外出人员（1：是")
    private String isOut;
    /**
     * 外出原因
     */
    @ApiModelProperty(value = "外出原因")
    private String outReason;
    /**
     * 签到时间
     */
    @ApiModelProperty(value = "签到时间")
    private Date signTime;
    /**
     * 照片
     */
    @ApiModelProperty(value = "照片")
    private String frontPhoto;

    @ApiModelProperty("监室名")
    private String roomName;

    @ApiModelProperty("涉嫌罪名")
    private String suspectedCharges;

    @ApiModelProperty("诉讼环节")
    private String litigationLink;

    @ApiModelProperty("衣服号")
    private String clothingNumber;

    @ApiModelProperty("视频点名民警")
    private String createUser;

    @ApiModelProperty("1-视频点名")
    private Integer isVideo;

    @ApiModelProperty("测量温度")
    private String temperature;

    /***
     * 1-正常 2-异常
     */
    @ApiModelProperty("1-正常 2-异常")
    private Integer temperatureState;

    /***
     * 报备状态
     */
    private Integer isReport;
}
