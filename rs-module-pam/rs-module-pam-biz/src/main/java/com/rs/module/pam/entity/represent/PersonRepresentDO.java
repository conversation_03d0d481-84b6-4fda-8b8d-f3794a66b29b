package com.rs.module.pam.entity.represent;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 监所事务管理-人员点名记录 DO
 *
 * <AUTHOR>
 */
@TableName("pam_person_represent")
@KeySequence("pam_person_represent_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_person_represent")
public class PersonRepresentDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员id
     */
    private String prisonerId;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员名称
     */
    private String jgryxm;
    /**
     * 签到状态(1:已签到
     */
    private String signStatus;
    /**
     * 是否外出（1：是
     */
    private String isOut;
    /**
     * 外出原因
     */
    private String outReason;
    /**
     * 签到时间
     */
    private Date signTime;
    /**
     * 关联监室点名表id
     */
    private String roomPresentId;
    /**
     * 温度
     */
    private String temperature;
    /**
     * 1-视频点名
     */
    private Integer isVideo;
    /**
     * 视频确认民警编号
     */
    private String createUserSfzh;
    /**
     * 视频确认民警
     */
    private String createUser;
    /**
     * 视频确认时间
     */
    private Date confirmDate;
    /**
     * 1-正常 2-异常
     */
    private Integer temperatureState;
    /**
     * 1-报备
     */
    private Integer isReport;

}
