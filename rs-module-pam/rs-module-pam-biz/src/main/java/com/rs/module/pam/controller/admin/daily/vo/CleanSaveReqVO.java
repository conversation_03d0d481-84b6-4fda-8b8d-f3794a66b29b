package com.rs.module.pam.controller.admin.daily.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-日常清监登记新增/修改 Request VO")
@Data
public class CleanSaveReqVO {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("数据来源（字典：ZD_DATA_SOURCES）")
    @NotBlank(message = "数据来源（字典：ZD_DATA_SOURCES）不能为空")
    private String dataSources;

    @ApiModelProperty("检查监室，多个逗号分割")
    @NotBlank(message = "检查监室，多个逗号分割不能为空")
    private String checkRoomId;

    @ApiModelProperty("带队领导ID，多个逗号分割")
    private String leaderUserSfzh;

    @ApiModelProperty("带队领导名称，多个逗号分割")
    private String leaderUserName;

    @ApiModelProperty("参加民警，多个逗号分割")
    @NotBlank(message = "参加民警，多个逗号分割不能为空")
    private String involvementUserSfzh;

    @ApiModelProperty("参加民警ID，多个逗号分割")
    @NotBlank(message = "参加民警ID，多个逗号分割不能为空")
    private String involvementUserName;

    @ApiModelProperty("检查时间")
    @NotNull(message = "检查时间不能为空")
    private Date checkTime;

    @ApiModelProperty("检查类型(1: 日常清监检查; 2:安全大检查)")
    @NotBlank(message = "检查类型不能为空")
    private String checkType;

    @ApiModelProperty("检查内容项id")
    @NotBlank(message = "检查内容项id不能为空")
    private String checkItemId;

    @ApiModelProperty("检查内容")
    @NotBlank(message = "检查内容不能为空")
    private String checkContent;

    @ApiModelProperty("登记状态")
    private String status;

    @ApiModelProperty("是否存在违禁(0:否,1:是)")
    @NotBlank(message = "是否存在违禁(0:否,1:是)不能为空")
    private String isViolation;

    @ApiModelProperty("违禁情况")
    private String violationContent;

    @ApiModelProperty("违禁附件URL")
    private String violationAttachmentUrl;

    @ApiModelProperty("是否存在安全隐患(0:否,1:是)")
    @NotNull(message = "是否存在安全隐患(0:否,1:是)不能为空")
    private String isHiddenDanger;

    @ApiModelProperty("安全隐患情况")
    private String hiddenDangerContent;

    @ApiModelProperty("安全隐患附件URL")
    private String hiddenDangerAttachmentUrl;

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("其他参加人")
    private String otherParticipants;





}
