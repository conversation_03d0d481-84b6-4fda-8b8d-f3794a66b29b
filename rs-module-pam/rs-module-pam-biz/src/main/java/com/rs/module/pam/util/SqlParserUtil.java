package com.rs.module.pam.util;

import com.alibaba.druid.sql.ast.SQLExpr;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.ast.expr.SQLAllColumnExpr;
import com.alibaba.druid.sql.ast.expr.SQLBetweenExpr;
import com.alibaba.druid.sql.ast.expr.SQLBinaryOpExpr;
import com.alibaba.druid.sql.ast.expr.SQLCastExpr;
import com.alibaba.druid.sql.ast.expr.SQLIdentifierExpr;
import com.alibaba.druid.sql.ast.expr.SQLInListExpr;
import com.alibaba.druid.sql.ast.expr.SQLMethodInvokeExpr;
import com.alibaba.druid.sql.ast.expr.SQLPropertyExpr;
import com.alibaba.druid.sql.ast.statement.SQLExprTableSource;
import com.alibaba.druid.sql.ast.statement.SQLJoinTableSource;
import com.alibaba.druid.sql.ast.statement.SQLSelect;
import com.alibaba.druid.sql.ast.statement.SQLSelectItem;
import com.alibaba.druid.sql.ast.statement.SQLSelectQueryBlock;
import com.alibaba.druid.sql.ast.statement.SQLSelectStatement;
import com.alibaba.druid.sql.ast.statement.SQLSubqueryTableSource;
import com.alibaba.druid.sql.ast.statement.SQLTableSource;
import com.alibaba.druid.sql.dialect.postgresql.parser.PGSQLStatementParser;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2024/8/9 9:45
 */
public class SqlParserUtil {
    private static final String DANGEROUS_KEYWORDS_REGEX = "\\b(DELETE|UPDATE|DROP|ALTER)\\b|;|--|#|/\\*|\\*/";
    private static final Pattern pattern = Pattern.compile(DANGEROUS_KEYWORDS_REGEX, Pattern.CASE_INSENSITIVE);

    public static boolean isSafeQuery(String query) {
        return !pattern.matcher(query).find();
    }

    /**
     * 解析SQL查询语句的所有查询字段
     *
     * @param sql    SQL语句
     * @return 查询字段列表
     */
    public static List<String> parseSqlSelectFields(String sql) {
        List<String> fieldNames = new ArrayList<>();
        SQLStatement statement = parseSqlStatement(sql);
        if (statement instanceof SQLSelectStatement) {
            SQLSelectStatement selectStatement = (SQLSelectStatement) statement;
            SQLSelectQueryBlock queryBlock = selectStatement.getSelect().getQueryBlock();

            if (queryBlock != null) {
                List<SQLSelectItem> selectItems = queryBlock.getSelectList();
                for (SQLSelectItem item : selectItems) {
                    extractSelectField(item.getExpr(), fieldNames);
                }
            }
        }
        return fieldNames;
    }

    /**
     * 解析SQL语句获取WHERE条件中的字段
     */
    public static List<String> parseSqlGetWhereCondition(String sql, String dbType) {
        List<String> fieldNames = new ArrayList<>();
        SQLStatement statement = parseSqlStatement(sql);
        if (statement instanceof SQLSelectStatement) {
            SQLSelectStatement selectStatement = (SQLSelectStatement) statement;
            SQLExpr whereExpr = selectStatement.getSelect().getQueryBlock().getWhere();
            if (whereExpr != null) {
                extractFieldNames(whereExpr, fieldNames);
            }
        }
        return fieldNames;
    }

    /**
     * 解析SQL语句获取FROM子句中的表名
     */
    public static List<String> parseSqlGetTableNames(String sql, String dbType) {
        List<String> tableNames = new ArrayList<>();
        SQLStatement statement = parseSqlStatement(sql);
        if (statement instanceof SQLSelectStatement) {
            SQLSelectStatement selectStatement = (SQLSelectStatement) statement;
            SQLTableSource tableSource = selectStatement.getSelect().getQueryBlock().getFrom();
            extractTableNames(tableSource, tableNames);
        }
        return tableNames;
    }

    // 解析SQL语句为Statement对象
    private static SQLStatement parseSqlStatement(String sql) {
        return new PGSQLStatementParser(sql).parseStatement();
    }

    // 提取查询字段
    private static void extractSelectField(SQLExpr expr, List<String> fieldNames) {
        if (expr instanceof SQLIdentifierExpr) {
            fieldNames.add(((SQLIdentifierExpr) expr).getName());
        } else if (expr instanceof SQLPropertyExpr) {
            fieldNames.add(((SQLPropertyExpr) expr).getName());
        } else if (expr instanceof SQLAllColumnExpr) {
            fieldNames.add("*");
        } else if (expr instanceof SQLMethodInvokeExpr) {
            SQLMethodInvokeExpr methodExpr = (SQLMethodInvokeExpr) expr;
            fieldNames.add(methodExpr.getMethodName() + "()");
            // 处理函数参数中的字段
            for (SQLExpr arg : methodExpr.getArguments()) {
                extractSelectField(arg, fieldNames);
            }
        } else if (expr instanceof SQLBinaryOpExpr) {
            SQLBinaryOpExpr binaryExpr = (SQLBinaryOpExpr) expr;
            extractSelectField(binaryExpr.getLeft(), fieldNames);
            extractSelectField(binaryExpr.getRight(), fieldNames);
        } else if (expr instanceof SQLCastExpr) {
            SQLCastExpr castExpr = (SQLCastExpr) expr;
            extractSelectField(castExpr.getExpr(), fieldNames);
        }
        // 可根据需要添加更多表达式类型的处理
    }

    // 提取WHERE条件中的字段
    private static void extractFieldNames(SQLExpr expr, List<String> fieldNames) {
        if (expr instanceof SQLBinaryOpExpr) {
            SQLBinaryOpExpr binaryOpExpr = (SQLBinaryOpExpr) expr;
            extractFieldNames(binaryOpExpr.getLeft(), fieldNames);
            extractFieldNames(binaryOpExpr.getRight(), fieldNames);
        } else if (expr instanceof SQLIdentifierExpr) {
            fieldNames.add(((SQLIdentifierExpr) expr).getName());
        } else if (expr instanceof SQLInListExpr) {
            SQLInListExpr inListExpr = (SQLInListExpr) expr;
            extractFieldNames(inListExpr.getExpr(), fieldNames);
        } else if (expr instanceof SQLBetweenExpr) {
            SQLBetweenExpr betweenExpr = (SQLBetweenExpr) expr;
            extractFieldNames(betweenExpr.getTestExpr(), fieldNames);
        } else if (expr instanceof SQLPropertyExpr) {
            fieldNames.add(((SQLPropertyExpr) expr).getName());
        } else if (expr instanceof SQLMethodInvokeExpr) {
            SQLMethodInvokeExpr methodInvokeExpr = (SQLMethodInvokeExpr) expr;
            for (SQLExpr argument : methodInvokeExpr.getArguments()) {
                extractFieldNames(argument, fieldNames);
            }
        }
        // 可根据需要添加更多表达式类型的处理
    }

    // 提取表名
    private static void extractTableNames(SQLTableSource tableSource, List<String> tableNames) {
        if (tableSource == null) {
            return;
        }
        if (tableSource instanceof SQLExprTableSource) {
            SQLExprTableSource exprTableSource = (SQLExprTableSource) tableSource;
            SQLExpr expr = exprTableSource.getExpr();

            if (expr instanceof SQLIdentifierExpr) {
                tableNames.add(((SQLIdentifierExpr) expr).getName());
            } else if (expr instanceof SQLPropertyExpr) {
                tableNames.add(((SQLPropertyExpr) expr).getName());
            }
        } else if (tableSource instanceof SQLJoinTableSource) {
            SQLJoinTableSource joinTableSource = (SQLJoinTableSource) tableSource;
            extractTableNames(joinTableSource.getLeft(), tableNames);
            extractTableNames(joinTableSource.getRight(), tableNames);
        } else if (tableSource instanceof SQLSubqueryTableSource) {
            SQLSubqueryTableSource subqueryTableSource = (SQLSubqueryTableSource) tableSource;
            SQLSelect select = subqueryTableSource.getSelect();
            if (select != null && select.getQuery() instanceof SQLSelectQueryBlock) {
                SQLSelectQueryBlock queryBlock = (SQLSelectQueryBlock) select.getQuery();
                extractTableNames(queryBlock.getFrom(), tableNames);
            }
        }
    }

}
