package com.rs.module.pam.controller.admin.represent.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 监所事务管理-人脸库及人员库信息进行比对新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CheckSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("点名批次号")
    private String presentNo;

    @ApiModelProperty("监室号")
    @NotEmpty(message = "监室号不能为空")
    private String roomId;

    @ApiModelProperty("监室名字")
    private String roomName;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员名称")
    private String jgryxm;

    @ApiModelProperty("1-补录成功")
    private Integer status;

}
