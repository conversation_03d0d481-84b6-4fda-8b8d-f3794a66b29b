package com.rs.module.pam.controller.admin.represent.vo.extra;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "同步仓内屏 基本信息-任务配置VO")
public class InitBaseDataConfigVO {
    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("任务号")
    private String taskCode;

    private String roomId;

    @ApiModelProperty("定时任务表达式")
    private String cronStr;

    private String cron;

    @ApiModelProperty("1-自定义 2-每天 3-执行一次")
    private Integer configPeriodCode;

    @ApiModelProperty("有效期")
    private Integer expiryDate;

    private Date startTime;

}
