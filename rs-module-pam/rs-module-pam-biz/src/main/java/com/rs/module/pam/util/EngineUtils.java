package com.rs.module.pam.util;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.bsp.common.util.StringUtil;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.dto.OpsResourceDTO;
import com.rs.module.pam.cons.IntegralConstants;
import com.rs.module.pam.entity.integral.IntegralLogDO;
import com.rs.module.pam.entity.integral.ModelConfigDO;

public class EngineUtils {

	public static String appendLogSql(ModelConfigDO modelConfig, BspApi bspApi, IntegralLogDO indicatorLogDO) {
		StringBuffer sb = appendSelectSql(modelConfig, bspApi);
		String keyCol = modelConfig.getKeyCol();
		sb.append(" where ")
		  .append(keyCol)
		  .append("='")
		  .append(indicatorLogDO.getBusinessId())
		  .append("'");
		return sb.toString();
	}

	public static String appendSql(ModelConfigDO modelConfig, BspApi bspApi) {
		StringBuffer sb = appendSelectSql(modelConfig, bspApi);

		String conditionSql = modelConfig.getConditionSql();
		if(StringUtil.isEmpty(conditionSql)) {
			conditionSql = "1=1";
		}
		String incCol = modelConfig.getIncCol();

		sb.append(" where ")
		  .append(conditionSql)
		  .append(" and (")
		  .append(incCol)
		  .append(" between ? and ? )");
		return sb.toString();
	}

	private static StringBuffer appendSelectSql(ModelConfigDO modelConfig, BspApi bspApi) {
		String tableName;
		if(IntegralConstants.RES_TYPE_TABLE.equals(modelConfig.getResType())) {
			tableName = modelConfig.getResName();
		}else if (IntegralConstants.RES_TYPE_SCRIPT.equals(modelConfig.getResType())) {
			OpsResourceDTO resource = bspApi.getResourceById(modelConfig.getResId(), "");
			String script = resource.getScript();
			tableName = "(" + script + ") t1 ";
		}else {
			throw new RuntimeException("资源类型为空");
		}

		String keyCol = modelConfig.getKeyCol();
		String orgCodeCol = modelConfig.getOrgCodeCol();
		String roomCodeCol = modelConfig.getRoomCodeCol();
		String prisonerCodeCol = modelConfig.getPrisonerCodeCol();
		String businessDateCol = modelConfig.getBusinessDateCol();
		StringBuffer sb = new StringBuffer();
		sb.append("select ")
		  .append(keyCol)
		  .append("," + orgCodeCol);
		if(StringUtil.isNotEmpty(roomCodeCol)) {
			sb.append("," + roomCodeCol);
		}
		if(StringUtil.isNotEmpty(prisonerCodeCol)) {
			sb.append("," + prisonerCodeCol);
		}
		if(StringUtil.isNotEmpty(businessDateCol)) {
			sb.append("," + businessDateCol);
		}
		sb.append(" from " + tableName);
		return sb;
	}

	public static String dateInc(String begindateString){
		DateTime endDate = DateUtil.offsetDay(DateUtil.parseDate(begindateString), 1);
		return DateUtil.formatDate(endDate);
	}

}
