package com.rs.module.pam.service.cook;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.cook.vo.DeliveryRespVO;
import com.rs.module.pam.controller.app.vo.AppDeliveryPrisonerListVO;
import com.rs.module.pam.entity.cook.DeliveryDO;

import java.util.List;

/**
 * 监所事务管理-发饭主 Service 接口
 *
 * <AUTHOR>
 */
public interface DeliveryService extends IBaseService<DeliveryDO> {

    /**
     * 获得监所事务管理-发饭主
     *
     * @param id 编号
     * @return 监所事务管理-发饭主
     */
    DeliveryDO getDelivery(String id);

    /**
     * 获得监所事务管理-发饭数据
     *
     * @return
     */
    AppDeliveryPrisonerListVO getDeliveryPrisonerListVO(String deliveryId);

    /**
     * 开始发饭
     *
     * @param orgCode
     * @param roomId
     * @param roomName
     */
    String startDelivery(String orgCode, String roomId, String roomName);

    /**
     * 结束发饭
     *
     * @param deliveryId
     */
    void endDelivery(String deliveryId);

    /**
     * 留饭
     *
     * @param jgrybms
     * @param deliveryId
     */
    void keepDelivery(String jgrybms, String deliveryId);

    /**
     * 内屏-发饭人员列表
     *
     * @param deliveryId
     * @return
     */
    List<AppDeliveryPrisonerListVO.DeliveryPrisonerList> getNpDeliveryPrisonerListVO(String deliveryId);

    /**
     * 内屏确认发饭
     *
     * @param deliveryId
     * @param orgCode
     * @param roomId
     * @return
     */
    DeliveryRespVO confirmDelivery(String deliveryId, String orgCode, String roomId);

    /**
     * 内屏-刷脸领饭
     *
     * @param deliveryId
     * @param ryId
     */
    void sweepFaceDelivery(String deliveryId, String ryId);

    /**
     * 获取发饭详情
     *
     * @param id
     * @return
     */
    DeliveryRespVO getDeliveryRespVO(String id);
}
