package com.rs.module.pam.entity.device;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-设备报修登记 DO
 *
 * <AUTHOR>
 */
@TableName("pam_device_repair_reg")
@KeySequence("pam_device_repair_reg_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_device_repair_reg")
public class DeviceRepairRegDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 工单编号
     */
    private String repairNo;
    /**
     * 数据来源（字典：ZD_DATA_SOURCES）
     */
    private String dataSources;
    /**
     * 监室ID
     */
    private String roomId;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 申请人
     */
    private String applicantUserSfzh;
    /**
     * 申请人姓名
     */
    private String applicantUserName;
    /**
     * 申请时间
     */
    private Date applicantTime;
    /**
     * 报修内容（字典：ZD_SBWXGL_BXNR）
     */
    private String repairContent;
    /**
     * 详细地点
     */
    private String detailedLocation;
    /**
     * 报修照片地址
     */
    private String repairImgUrl;
    /**
     * 备注
     */
    private String remark;
    /**
     * 派单时间
     */
    private Date dispatchTime;
    /**
     * 派单人id
     */
    private String detailedUserSfzh;
    /**
     * 派单人姓名
     */
    private String detailedUserName;
    /**
     * 处理情况
     */
    private String handleRemark;
    /**
     * 处理照片地址
     */
    private String handleImgUrl;
    /**
     * 处理人身份证号
     */
    private String handleUserSfzh;
    /**
     * 处理人姓名
     */
    private String handleUserName;
    /**
     * 维修时间
     */
    private Date handleTime;
    /**
     * 报修状态（字典：ZD_SBWXGL_BXZT）
     */
    private String status;

}
