package com.rs.module.pam.controller.admin.daily.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

import com.rs.module.pam.entity.daily.LifeEventDO;
import com.rs.module.pam.entity.daily.LifeRoomDO;

@ApiModel(description = "管理后台 - 监所事务管理-一日生活制度新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LifeSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("制度名称")
    @NotEmpty(message = "制度名称不能为空")
    private String name;

    @ApiModelProperty("周期设置（字典：ZD_YRSHZD_ZQSZ）")
    @NotEmpty(message = "周期设置（字典：ZD_YRSHZD_ZQSZ）不能为空")
    private String cycleSetting;

    @ApiModelProperty("cycle_config")
    private String cycleConfig;

    @ApiModelProperty("自定义时间")
    private String customizeTime;


    @ApiModelProperty("监所事务管理-一日生活制度关联事务列表")
    private List<LifeEventDO> lifeEvents;

    @ApiModelProperty("监所事务管理-一日生活制度关联监室列表")
    private List<LifeRoomDO> lifeRooms;

}
