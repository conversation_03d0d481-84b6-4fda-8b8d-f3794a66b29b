package com.rs.module.pam.service.family;

import java.util.*;
import javax.validation.*;
import com.rs.module.pam.controller.admin.family.vo.*;
import com.rs.module.pam.entity.family.FamilyPhoneDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 监所事务管理-亲情电话 Service 接口
 *
 * <AUTHOR>
 */
public interface FamilyPhoneService extends IBaseService<FamilyPhoneDO>{

    /**
     * 创建监所事务管理-亲情电话
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createFamilyPhone(@Valid FamilyPhoneSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-亲情电话
     *
     * @param updateReqVO 更新信息
     */
    void updateFamilyPhone(@Valid FamilyPhoneSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-亲情电话
     *
     * @param id 编号
     */
    void deleteFamilyPhone(String id);

    /**
     * 获得监所事务管理-亲情电话
     *
     * @param id 编号
     * @return 监所事务管理-亲情电话
     */
    FamilyPhoneDO getFamilyPhone(String id);

    /**
    * 获得监所事务管理-亲情电话分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-亲情电话分页
    */
    PageResult<FamilyPhoneDO> getFamilyPhonePage(FamilyPhonePageReqVO pageReqVO);

    /**
    * 获得监所事务管理-亲情电话列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-亲情电话列表
    */
    List<FamilyPhoneDO> getFamilyPhoneList(FamilyPhoneListReqVO listReqVO);


    void approvalFamilyPhone(FamilyPhoneApprovalReqVO approvalReqVO);

    List<FamilyPhoneDO> getFamilyPhoneList10(String jgrybm, String id);

    PageResult<FamilyPhoneDO> getAppFamilyPhonePage(int pageNo, int pageSize, String jgrybm, String type);
}
