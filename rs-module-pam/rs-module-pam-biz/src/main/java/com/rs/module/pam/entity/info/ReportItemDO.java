package com.rs.module.pam.entity.info;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 监所事务管理-信息报备事项配置 DO
 *
 * <AUTHOR>
 */
@TableName("pam_info_report_item")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportItemDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 报备类别
     */
    private String reportType;
    /**
     * 报备事由
     */
    private String reportContent;
    /**
     * 状态（1：启用，0：禁用）
     */
    private String status;
    /**
     * 是否参与点名(1: 参与，0: 不参与)
     */
    private Integer isRollcall;

}
