package com.rs.module.pam.service.psy;

import java.util.*;
import javax.validation.*;
import com.rs.module.pam.controller.admin.psy.vo.*;
import com.rs.module.pam.entity.psy.EvalPlanPushRecordAnswerDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 监所事务管理-心理测评计划推送记录答案 Service 接口
 *
 * <AUTHOR>
 */
public interface EvalPlanPushRecordAnswerService extends IBaseService<EvalPlanPushRecordAnswerDO>{

    /**
     * 创建监所事务管理-心理测评计划推送记录答案
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createEvalPlanPushRecordAnswer(EvalPlanPushRecordAnswerSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-心理测评计划推送记录答案
     *
     * @param updateReqVO 更新信息
     */
    void updateEvalPlanPushRecordAnswer(@Valid EvalPlanPushRecordAnswerSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-心理测评计划推送记录答案
     *
     * @param id 编号
     */
    void deleteEvalPlanPushRecordAnswer(String id);

    /**
     * 获得监所事务管理-心理测评计划推送记录答案
     *
     * @param id 编号
     * @return 监所事务管理-心理测评计划推送记录答案
     */
    EvalPlanPushRecordAnswerDO getEvalPlanPushRecordAnswer(String id);

    /**
    * 获得监所事务管理-心理测评计划推送记录答案分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-心理测评计划推送记录答案分页
    */
    PageResult<EvalPlanPushRecordAnswerDO> getEvalPlanPushRecordAnswerPage(EvalPlanPushRecordAnswerPageReqVO pageReqVO);

    /**
    * 获得监所事务管理-心理测评计划推送记录答案列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-心理测评计划推送记录答案列表
    */
    List<EvalPlanPushRecordAnswerDO> getEvalPlanPushRecordAnswerList(EvalPlanPushRecordAnswerListReqVO listReqVO);


}
