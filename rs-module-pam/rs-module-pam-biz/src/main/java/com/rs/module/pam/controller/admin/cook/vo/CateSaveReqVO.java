package com.rs.module.pam.controller.admin.cook.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 监所事务管理-菜品分类新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CateSaveReqVO extends BaseVO{

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("菜品分类名称")
    @NotEmpty(message = "菜品分类名称不能为空")
    private String cateName;

    @ApiModelProperty("分类颜色")
//    @NotEmpty(message = "分类颜色不能为空")
    private String cateColor;

}
