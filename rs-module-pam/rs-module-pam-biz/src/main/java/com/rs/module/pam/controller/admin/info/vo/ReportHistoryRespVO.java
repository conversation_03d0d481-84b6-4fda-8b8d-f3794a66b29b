package com.rs.module.pam.controller.admin.info.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-信息报备历史记录 Response VO")
@Data
public class ReportHistoryRespVO {

    @ApiModelProperty("报备时间")
    private Date reportTime;
    @ApiModelProperty("报备事由")
    private String reportContent;
    @ApiModelProperty("是否参与点名")
    private Integer isRollcall;
    @ApiModelProperty("报备时效")
    private String reportLong;

}
