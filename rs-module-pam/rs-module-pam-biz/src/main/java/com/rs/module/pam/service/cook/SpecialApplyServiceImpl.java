package com.rs.module.pam.service.cook;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.bsp.common.cache.DicUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.base.vo.ApproveReqVO;
import com.rs.module.pam.controller.admin.cook.vo.SpecialApplyDoctorApproveReqVO;
import com.rs.module.pam.controller.admin.cook.vo.SpecialApplyLeaderApproveReqVO;
import com.rs.module.pam.controller.admin.cook.vo.SpecialApplySaveReqVO;
import com.rs.module.pam.controller.app.vo.AppSpecialApplySaveSowReqVO;
import com.rs.module.pam.dao.cook.SpecialApplyDao;
import com.rs.module.pam.entity.cook.SpecialApplyDO;
import com.rs.module.pam.enums.CookSpecialRegStatusEnum;
import com.rs.module.pam.enums.CookMealTypeEnum;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * 监所事务管理-特殊餐申请 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SpecialApplyServiceImpl extends BaseServiceImpl<SpecialApplyDao, SpecialApplyDO> implements SpecialApplyService {

    // 定义常量
    private static final Set<String> DOCTOR_ROLE_CODE = CollUtil.newHashSet("00001", "00002", "0004", "00005", "1003",
            "1100004", "1100006", "1100001", "1100009", "1100020");
    private static final String BASE_MSG_URL = "/#/catering/specialMealRequest";
    private static final String SPECIAL_COOK_DEF_KEY = "tscsqgjys";

    @Resource
    private SpecialApplyDao specialApplyDao;
    @Resource
    private PrisonerService prisonerService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createSpecialApply(SpecialApplySaveReqVO createReqVO) {
        // 校验监管人员编码是否存在未结束的特殊餐申请
        validateExistingApplication(createReqVO.getJgrybm());
        // 创建并保存特殊餐申请
        SpecialApplyDO specialApply = createAndSaveSpecialApply(createReqVO);
        // 设置流程相关参数
        ProcessParams processParams = determineProcessParams(specialApply.getMealType(), SessionUserUtil.getSessionUser(), specialApply);
        // 启动审批流程
        startApprovalProcess(specialApply, processParams);
        return specialApply.getId();
    }

    private void validateExistingApplication(String jgrybm) {
        String existedId = specialApplyDao.existUnfishCook(jgrybm);
        if (existedId != null) {
            throw new ServerException("监管人员编码已存在未结束的特殊餐申请");
        }
    }

    private SpecialApplyDO createAndSaveSpecialApply(SpecialApplySaveReqVO createReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        SpecialApplyDO specialApply = BeanUtils.toBean(createReqVO, SpecialApplyDO.class);
        specialApply.setRegOperatorXm(sessionUser.getName());
        specialApply.setRegOperatorSfzh(sessionUser.getIdCard());
        specialApply.setRegTime(new Date());
        specialApply.setRegStatus(CookSpecialRegStatusEnum.DSH.getCode());
        specialApplyDao.insert(specialApply);
        return specialApply;
    }

    private ProcessParams determineProcessParams(String mealType, SessionUser sessionUser, SpecialApplyDO specialApplyDO) {
        ProcessParams params = new ProcessParams();
        String roleCodes = sessionUser.getRoleCodes();
        if (CookMealTypeEnum.SICK_MEAL.getCode().equals(mealType)) {
            if (isDoctor(roleCodes)) {
                params.setMsgUrlSuffix("/leader");
                params.setPassCondition("leader");
                specialApplyDO.setDoctorApprovalResult("1");
                specialApplyDO.setDoctorApproverXm(sessionUser.getName());
                specialApplyDO.setDoctorApproverSfzh(sessionUser.getIdCard());
                specialApplyDO.setDoctorApproverTime(new Date());
            } else {
                params.setMsgUrlSuffix("/doctor");
                params.setPassCondition("doctor");
            }
        } else {
            if (!sessionUser.getIsAdmin() && isDoctor(roleCodes)) {
                throw new ServerException("非病号餐，不能由医生发起申请");
            }
            params.setMsgUrlSuffix("/leader");
            params.setPassCondition("leader");
        }
        return params;
    }

    private void startApprovalProcess(SpecialApplyDO specialApply, ProcessParams processParams) {
        // 构建消息URL
        String msgUrl = String.format(BASE_MSG_URL + processParams.getMsgUrlSuffix() + "?id=%s", specialApply.getId());
        // 获取囚犯信息
        PrisonerVwRespVO prisonerInfo = prisonerService.getPrisonerByJgrybm(specialApply.getJgrybm());
        // 准备流程变量
        Map<String, Object> variables = new HashMap<>();
        variables.put("mealType", CookMealTypeEnum.getMealType(specialApply.getMealType()).getName());
        variables.put("jgrmxm", prisonerInfo.getXm());
        variables.put("busType", MsgBusTypeEnum.GJ_TSCSQ.getCode());
        variables.put("passCondition", processParams.getPassCondition());
        // 启动流程
        Map<String, String> processResult = BspApprovalUtil.commonStartProcessMap(getDefKey(), specialApply.getId(), null,
                msgUrl, variables, HttpUtils.getAppCode());
        if (CollUtil.isEmpty(processResult)) {
            throw new ServerException("启动审批流程失败");
        }
        // 更新流程实例信息
        updateProcessInfo(specialApply, processResult);
    }

    private void updateProcessInfo(SpecialApplyDO specialApply, Map<String, String> result) {
        SpecialApplyDO updateObj = new SpecialApplyDO();
        updateObj.setId(specialApply.getId());
        updateObj.setRegStatus(CookSpecialRegStatusEnum.DSH.getCode());
        updateObj.setActInstId(result.get("actInstId"));
        updateObj.setTaskId(result.get("taskId"));
        if (specialApply.getDoctorApprovalResult() != null) {
            updateObj.setDoctorApprovalResult(specialApply.getDoctorApprovalResult());
        }
        if (specialApply.getDoctorApproverXm() != null) {
            updateObj.setDoctorApproverXm(specialApply.getDoctorApproverXm());
        }
        if (specialApply.getDoctorApproverSfzh() != null) {
            updateObj.setDoctorApproverSfzh(specialApply.getDoctorApproverSfzh());
        }
        if (specialApply.getDoctorApproverTime() != null) {
            updateObj.setDoctorApproverTime(specialApply.getDoctorApproverTime());
        }
        specialApplyDao.updateById(updateObj);
    }

    private boolean isDoctor(String roleCodes) {
        if (StrUtil.isBlank(roleCodes)) {
            return false;
        }
        String[] split = roleCodes.split(",");
        for (String code : split) {
            if (DOCTOR_ROLE_CODE.contains(code)) {
                return true;
            }
        }
        return false;
    }

    // 内部类用于封装流程参数
    private static class ProcessParams {
        private String msgUrlSuffix;
        private String passCondition;

        public String getMsgUrlSuffix() {
            return msgUrlSuffix;
        }

        public void setMsgUrlSuffix(String msgUrlSuffix) {
            this.msgUrlSuffix = msgUrlSuffix;
        }

        public String getPassCondition() {
            return passCondition;
        }

        public void setPassCondition(String passCondition) {
            this.passCondition = passCondition;
        }
    }


    @Override
    public void updateSpecialApply(SpecialApplySaveReqVO updateReqVO) {
        // 校验存在
        String existedId = specialApplyDao.existUnfishCook(updateReqVO.getJgrybm());
        if (existedId != null && !existedId.equals(updateReqVO.getId())) {
            throw new ServerException("监管人员编码已存在未结束的特殊餐申请");
        }
        // 更新
        SpecialApplyDO updateObj = BeanUtils.toBean(updateReqVO, SpecialApplyDO.class);
        specialApplyDao.updateById(updateObj);
    }

    @Override
    public void deleteSpecialApply(String id) {
        // 校验存在
        validateSpecialApplyExists(id);
        // 删除
        specialApplyDao.deleteById(id);
    }

    private void validateSpecialApplyExists(String id) {
        if (specialApplyDao.selectById(id) == null) {
            throw new ServerException("监室配餐管理-特殊餐申请数据不存在");
        }
    }

    @Override
    public SpecialApplyDO getSpecialApply(String id) {
        return specialApplyDao.selectById(id);
    }

    @Override
    public Boolean updateProcessStatus(String id, String regStatus, String actInstId) {
        SpecialApplyDO specialApplyDO = specialApplyDao.selectById(id);
        if (specialApplyDO == null) {
            throw new ServerException("监室配餐管理-特殊餐申请数据不存在");
        }
        SpecialApplyDO updateObj = new SpecialApplyDO();
        updateObj.setId(id);
        updateObj.setRegStatus(regStatus);
        if (StrUtil.isNotBlank(actInstId)) {
            updateObj.setActInstId(actInstId);
        }
        specialApplyDao.updateById(updateObj);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doctorApprove(SpecialApplyDoctorApproveReqVO approveReqVO) {
        SpecialApplyDO specialApplyDO = specialApplyDao.selectById(approveReqVO.getId());
        if (specialApplyDO == null) {
            throw new ServerException("监室配餐管理-特殊餐申请数据不存在");
        }
        if (!CookSpecialRegStatusEnum.DSH.getCode().equals(specialApplyDO.getRegStatus())) {
            throw new ServerException("特殊餐申请状态错误，不允许医生审批");
        }
        if (!CookMealTypeEnum.SICK_MEAL.getCode().equals(specialApplyDO.getMealType())) {
            throw new ServerException("配餐类型错误，不支持医生审批，仅支持病号餐");
        }
        if (StrUtil.isNotBlank(specialApplyDO.getDoctorApprovalResult())) {
            throw new ServerException("特殊餐申请已被" + specialApplyDO.getDoctorApproverXm() + "审批,请勿重复审批");
        }
        PrisonerVwRespVO respVO = prisonerService.getPrisonerByJgrybm(specialApplyDO.getJgrybm());
        Map<String, Object> variables = new HashMap<>();
        variables.put("mealType", CookMealTypeEnum.getMealType(specialApplyDO.getMealType()).getName());
        variables.put("jgrmxm", respVO.getXm());
        variables.put("busType", MsgBusTypeEnum.GJ_TSCSQ.getCode());
        String doctorApprovalResult = approveReqVO.getDoctorApprovalResult();
        BspApproceStatusEnum statusEnum;
        String msgUrl = String.format("/#/catering/specialMealRequest/leader?id=%s", specialApplyDO.getId());
        SpecialApplyDO updateObj = new SpecialApplyDO();
        BeanUtils.copyProperties(approveReqVO, updateObj);
        updateObj.setId(specialApplyDO.getId());
        boolean terminateTask;
        if ("1".equals(doctorApprovalResult)) {
            statusEnum = BspApproceStatusEnum.PASSED;
            terminateTask = false;
        } else {
            statusEnum = BspApproceStatusEnum.NOT_PASSED_END;
            updateObj.setRegStatus(CookSpecialRegStatusEnum.SHBTG.getCode());
            terminateTask = true;
        }
        ApproveReqVO reqVO = new ApproveReqVO();
        reqVO.setDefKey(getDefKey());
        reqVO.setActInstId(specialApplyDO.getActInstId());
        reqVO.setTaskId(specialApplyDO.getTaskId());
        reqVO.setId(specialApplyDO.getId());
        reqVO.setApproverSfzh(SessionUserUtil.getSessionUser().getIdCard());
        reqVO.setApproverXm(SessionUserUtil.getSessionUser().getName());
        reqVO.setApprovalComments(updateObj.getDoctorApprovalComments());
        Map<String, String> approvalResult = BspApprovalUtil.approvalProcessMap(reqVO, statusEnum, null, msgUrl,
                terminateTask, variables, null, HttpUtils.getAppCode());
        if (CollUtil.isEmpty(approvalResult)) {
            throw new ServerException("审批失败");
        }
        updateObj.setActInstId(approvalResult.get("actInstId"));
        updateObj.setTaskId(approvalResult.get("taskId"));
        updateObj.setDoctorApproverXm(SessionUserUtil.getSessionUser().getName());
        updateObj.setDoctorApproverSfzh(SessionUserUtil.getSessionUser().getIdCard());
        updateObj.setDoctorApproverTime(new Date());
        specialApplyDao.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void leaderApprove(SpecialApplyLeaderApproveReqVO approveReqVO) {
        SpecialApplyDO specialApplyDO = specialApplyDao.selectById(approveReqVO.getId());
        if (specialApplyDO == null) {
            throw new ServerException("监室配餐管理-特殊餐申请数据不存在");
        }
        if (!CookSpecialRegStatusEnum.DSH.getCode().equals(specialApplyDO.getRegStatus())) {
            throw new ServerException("特殊餐申请状态错误，不允许审批");
        }
        if (CookMealTypeEnum.SICK_MEAL.getCode().equals(specialApplyDO.getMealType())) {
            String doctorApprovalResult = specialApplyDO.getDoctorApprovalResult();
            if (StrUtil.isBlank(doctorApprovalResult) || !doctorApprovalResult.equals("1")) {
                throw new ServerException("配餐类型病号餐，医生审批结果必须为“通过”");
            }
        }
        if (StrUtil.isNotBlank(specialApplyDO.getLeaderApprovalResult())) {
            throw new ServerException("特殊餐申请已被" + specialApplyDO.getLeaderApproverXm() + "审批,请勿重复审批");
        }
        SpecialApplyDO updateObj = new SpecialApplyDO();
        BeanUtils.copyProperties(approveReqVO, updateObj);
        BspApproceStatusEnum statusEnum;
        if ("1".equals(approveReqVO.getLeaderApprovalResult())) {
            statusEnum = BspApproceStatusEnum.PASSED_END;
            updateObj.setRegStatus(CookSpecialRegStatusEnum.SHTG.getCode());
        } else {
            statusEnum = BspApproceStatusEnum.NOT_PASSED_END;
            updateObj.setRegStatus(CookSpecialRegStatusEnum.SHBTG.getCode());
        }

        ApproveReqVO reqVO = new ApproveReqVO();
        reqVO.setDefKey(getDefKey());
        reqVO.setActInstId(specialApplyDO.getActInstId());
        reqVO.setTaskId(specialApplyDO.getTaskId());
        reqVO.setId(specialApplyDO.getId());
        reqVO.setApproverSfzh(SessionUserUtil.getSessionUser().getIdCard());
        reqVO.setApproverXm(SessionUserUtil.getSessionUser().getName());
        reqVO.setApprovalComments(updateObj.getLeaderApprovalComments());
        Map<String, String> approvalResult = BspApprovalUtil.approvalProcessMap(reqVO, statusEnum, null, null,
                true, null, null, HttpUtils.getAppCode());
        if (CollUtil.isEmpty(approvalResult)) {
            throw new ServerException("审批失败");
        }
        updateObj.setActInstId(approvalResult.get("actInstId"));
        updateObj.setTaskId(approvalResult.get("taskId"));
        updateObj.setLeaderApproverXm(SessionUserUtil.getSessionUser().getName());
        updateObj.setLeaderApproverSfzh(SessionUserUtil.getSessionUser().getIdCard());
        updateObj.setLeaderApproverTime(new Date());
        updateObj.setId(specialApplyDO.getId());
        specialApplyDao.updateById(updateObj);
    }

    @Override
    public PrisonerVwRespVO getSpecialApplyPrisonerByJgrybm(String jgrybm) {
        return prisonerService.getPrisonerWithMedicalInformationByJgrybm(jgrybm);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String applySpecialMeal(AppSpecialApplySaveSowReqVO saveReqVO) {
        // 校验监管人员编码是否存在未结束的特殊餐申请
        validateExistingApplication(saveReqVO.getJgrybm());
        SpecialApplyDO specialApply = new SpecialApplyDO();
        BeanUtils.copyProperties(saveReqVO, specialApply);
        specialApply.setRegStatus(CookSpecialRegStatusEnum.DQR.getCode());
        specialApply.setRegTime(new Date());
        specialApplyDao.insert(specialApply);
        String simpleUUID = IdUtil.fastSimpleUUID();
        String mealTypeName = DicUtil.translate("ZD_PCGL_PCLX ", specialApply.getMealType());
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String title = String.format("%s提交了【%s】特殊餐申请，请进行确认！", sessionUser.getName(), mealTypeName);
        String content = "";                            //消息内容
        String url = "/discipline/catering/specialMealRequest?id=" + specialApply.getId() + "&jgrybm=" + specialApply.getJgrybm() + "&pcId=" + simpleUUID;                                //消息处理页面地址
        //来源应用
        String fUser = sessionUser.getIdCard();                        //来源用户身份证号
        String fUserName = sessionUser.getName();                      //来源用户姓名
        String fOrgCode = sessionUser.getOrgCode();                        //来源机构代码
        String fOrgName = sessionUser.getOrgName();                                //来源机构名称
        String fXxpt = "pc";
        List<ReceiveUser> receiveUserList = ListUtil.of(new ReceiveUser(saveReqVO.getRegOperatorSfzh(), fOrgCode));
        SendMessageUtil.sendTodoMsg(title, content, url, HttpUtils.getAppCode(), fUser, fUserName, fOrgCode, fOrgName,
                null, simpleUUID, fXxpt, "", receiveUserList, MsgBusTypeEnum.GJ_TSCSQ.getCode(), null);
        return specialApply.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmSpecialApply(String id, String success) {
        SpecialApplyDO specialApplyDO = specialApplyDao.selectById(id);
        if (specialApplyDO == null) {
            throw new ServerException("监室配餐管理-特殊餐申请数据不存在");
        }
        if (!CookSpecialRegStatusEnum.DQR.getCode().equals(specialApplyDO.getRegStatus())) {
            throw new ServerException("特殊餐申请状态错误，不允许确认");
        }
        // 确认成功
        if ("1".equals(success)) {
            // 设置流程相关参数
            ProcessParams processParams = determineProcessParams(specialApplyDO.getMealType(), SessionUserUtil.getSessionUser(), specialApplyDO);
            // 启动审批流程
            startApprovalProcess(specialApplyDO, processParams);
        } else {
            SpecialApplyDO updateObj = new SpecialApplyDO();
            updateObj.setId(id);
            updateObj.setRegStatus(CookSpecialRegStatusEnum.SHBTG.getCode());
            specialApplyDao.updateById(updateObj);
        }
    }

    @Override
    public List<Map<String, Object>> getMealType(List<String> jgrybms, String mealPeriods) {
        List<Map<String, Object>> mealType = specialApplyDao.getMealType(jgrybms, mealPeriods);
        if (CollUtil.isEmpty(mealType)){
            return Collections.emptyList();
        }
        return mealType;
    }

    private String getDefKey() {
        return HttpUtils.getAppCode() + '-' + SPECIAL_COOK_DEF_KEY;
    }


}
