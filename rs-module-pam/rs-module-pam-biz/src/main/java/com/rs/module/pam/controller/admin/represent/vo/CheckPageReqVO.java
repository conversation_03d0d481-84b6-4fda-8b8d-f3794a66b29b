package com.rs.module.pam.controller.admin.represent.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-人脸库及人员库信息进行比对分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CheckPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("点名批次号")
    private String presentNo;

    @ApiModelProperty("监室号")
    private String roomId;

    @ApiModelProperty("监室名字")
    private String roomName;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员名称")
    private String jgryxm;

    @ApiModelProperty("1-补录成功")
    private Integer status;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
