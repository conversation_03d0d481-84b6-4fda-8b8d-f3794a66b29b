package com.rs.module.pam.entity.represent;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 监所事务管理-监室点名配置 DO
 *
 * <AUTHOR>
 */
@TableName("pam_represent_config")
@KeySequence("pam_represent_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_represent_config")
public class RepresentConfigDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 监室name
     */
    private String roomName;
    /**
     * 配置周期
     */
    private String configPeriod;
    /**
     * 配置时间
     */
    private String startTime;
    /**
     * 定时任务表达式
     */
    private String cron;
    /**
     * xxl-job 任务ID
     */
    private Integer jobId;
    /**
     * 计划周期code 1-自定义 2-每天 3-执行一次
     */
    private String configPeriodCode;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 0：启用 1：未启用
     */
    private Integer status;
    /**
     * 配置的结束时间
     */
    private String endTime;
    /**
     * 结束时间的定时任务表达式
     */
    private String endCron;
    /**
     * 有效期
     */
    private Integer expiryDate;

}
