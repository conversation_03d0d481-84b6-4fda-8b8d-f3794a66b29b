package com.rs.module.pam.dao.represent;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.represent.vo.PersonRepresentListReqVO;
import com.rs.module.pam.controller.admin.represent.vo.PersonRepresentPageReqVO;
import com.rs.module.pam.controller.admin.represent.vo.extra.InRepresentPersonVO;
import com.rs.module.pam.controller.admin.represent.vo.extra.RepresentPersonVO;
import com.rs.module.pam.entity.represent.PersonRepresentDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
* 监所事务管理-人员点名记录 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PersonRepresentDao extends IBaseDao<PersonRepresentDO> {


    default PageResult<PersonRepresentDO> selectPage(PersonRepresentPageReqVO reqVO) {
        Page<PersonRepresentDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PersonRepresentDO> wrapper = new LambdaQueryWrapperX<PersonRepresentDO>()
            .eqIfPresent(PersonRepresentDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PersonRepresentDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(PersonRepresentDO::getSignStatus, reqVO.getSignStatus())
            .eqIfPresent(PersonRepresentDO::getIsOut, reqVO.getIsOut())
            .eqIfPresent(PersonRepresentDO::getOutReason, reqVO.getOutReason())
            .betweenIfPresent(PersonRepresentDO::getSignTime, reqVO.getSignTime())
            .eqIfPresent(PersonRepresentDO::getRoomPresentId, reqVO.getRoomPresentId())
            .eqIfPresent(PersonRepresentDO::getTemperature, reqVO.getTemperature())
            .eqIfPresent(PersonRepresentDO::getIsVideo, reqVO.getIsVideo())
            .eqIfPresent(PersonRepresentDO::getCreateUserSfzh, reqVO.getCreateUserSfzh())
            .eqIfPresent(PersonRepresentDO::getCreateUser, reqVO.getCreateUser())
            .betweenIfPresent(PersonRepresentDO::getConfirmDate, reqVO.getConfirmDate())
            .eqIfPresent(PersonRepresentDO::getTemperatureState, reqVO.getTemperatureState())
            .eqIfPresent(PersonRepresentDO::getIsReport, reqVO.getIsReport())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PersonRepresentDO::getAddTime);
        }
        Page<PersonRepresentDO> representPage = selectPage(page, wrapper);
        return new PageResult<>(representPage.getRecords(), representPage.getTotal());
    }
    default List<PersonRepresentDO> selectList(PersonRepresentListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PersonRepresentDO>()
            .eqIfPresent(PersonRepresentDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PersonRepresentDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(PersonRepresentDO::getSignStatus, reqVO.getSignStatus())
            .eqIfPresent(PersonRepresentDO::getIsOut, reqVO.getIsOut())
            .eqIfPresent(PersonRepresentDO::getOutReason, reqVO.getOutReason())
            .betweenIfPresent(PersonRepresentDO::getSignTime, reqVO.getSignTime())
            .eqIfPresent(PersonRepresentDO::getRoomPresentId, reqVO.getRoomPresentId())
            .eqIfPresent(PersonRepresentDO::getTemperature, reqVO.getTemperature())
            .eqIfPresent(PersonRepresentDO::getIsVideo, reqVO.getIsVideo())
            .eqIfPresent(PersonRepresentDO::getCreateUserSfzh, reqVO.getCreateUserSfzh())
            .eqIfPresent(PersonRepresentDO::getCreateUser, reqVO.getCreateUser())
            .betweenIfPresent(PersonRepresentDO::getConfirmDate, reqVO.getConfirmDate())
            .eqIfPresent(PersonRepresentDO::getTemperatureState, reqVO.getTemperatureState())
            .eqIfPresent(PersonRepresentDO::getIsReport, reqVO.getIsReport())
        .orderByDesc(PersonRepresentDO::getAddTime));
    }

    /**
     * 根据监室点名id返回对应的人员信息
     * @param representId
     * @return
     */
    List<RepresentPersonVO> getPersonsByRepresentId(@Param("representId") String representId);


    /***
     * 视频点名，异常人员列表
     * @param representId
     * @return
     */
    List<RepresentPersonVO> getErrPersonsByRepresentId(@Param("representId") String representId);


    /**
     * 得到签到情况
     * @param prisonerId 监管人员ID
     * @param representId 监室点名ID
     * @return
     */
    PersonRepresentDO getPersonSignStatus(@Param("prisonerId") String prisonerId,@Param("representId") String representId);

    /***
     *检测点名监室是否完成点名
     * @param roomId
     * @return
     */
    @Select("SELECT count(1) " +
            "FROM " +
            "pam_person_represent A " +
            "LEFT JOIN pam_represent b ON A.room_present_id = b.ID " +
            "WHERE b.room_id = #{roomId} " +
            "AND A.is_out IS NULL " +
            "AND A.sign_status = '0' " +
            "AND A.room_present_id IN ( SELECT ID FROM pam_represent WHERE present_status = '1' )")
    Integer checkIsLastSign(@Param("roomId") String roomId);

    /**
     * 根据监室名获取监室点名数据
     * @param roomId
     * @return
     */
    List<InRepresentPersonVO> getPersonsByRoomId(@Param("roomId") String roomId, @Param("second") String second);


    @Update("update pam_person_represent set " +
            "create_user_sfzh = #{idCard}, " +
            "create_user = #{name}, " +
            "confirm_date = #{confirmDate}, " +
            "is_video = '1', " +
            "sign_status = '1'  " +
            "where room_present_id = #{id} and jgrybm =#{jgrybm}")
    void updateSignStatusByVideo(@Param("id") String id, @Param("jgrybm") String jgrybm, @Param("idCard") String idCard, @Param("name") String name, @Param("confirmDate") Date confirmDate);

    @Select("SELECT count(1) from pam_person_represent " +
            "WHERE room_present_id = #{id}  and jgrybm = #{jgrybm} and sign_status = '1'")
    Integer checkeSignStatusByVideo(@Param("id") String id, @Param("jgrybm") String jgrybm);

}
