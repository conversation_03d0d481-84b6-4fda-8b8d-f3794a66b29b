package com.rs.module.pam.dao.psy;

import java.util.*;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.entity.psy.EvalPlanPushRecordAnswerDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.pam.controller.admin.psy.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
 * 监所事务管理-心理测评计划推送记录答案 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface EvalPlanPushRecordAnswerDao extends IBaseDao<EvalPlanPushRecordAnswerDO> {


    default PageResult<EvalPlanPushRecordAnswerDO> selectPage(EvalPlanPushRecordAnswerPageReqVO reqVO) {
        Page<EvalPlanPushRecordAnswerDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<EvalPlanPushRecordAnswerDO> wrapper = new LambdaQueryWrapperX<EvalPlanPushRecordAnswerDO>()
                .eqIfPresent(EvalPlanPushRecordAnswerDO::getEvalNo, reqVO.getEvalNo())
                .eqIfPresent(EvalPlanPushRecordAnswerDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(EvalPlanPushRecordAnswerDO::getJgryxm, reqVO.getJgryxm())
                .betweenIfPresent(EvalPlanPushRecordAnswerDO::getSubmitTime, reqVO.getSubmitTime())
                .eqIfPresent(EvalPlanPushRecordAnswerDO::getScore, reqVO.getScore())
                .eqIfPresent(EvalPlanPushRecordAnswerDO::getEvalResults, reqVO.getEvalResults())
                .eqIfPresent(EvalPlanPushRecordAnswerDO::getTimeSpentAnswer, reqVO.getTimeSpentAnswer())
                .eqIfPresent(EvalPlanPushRecordAnswerDO::getStatus, reqVO.getStatus());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(EvalPlanPushRecordAnswerDO::getAddTime);
        }
        Page<EvalPlanPushRecordAnswerDO> evalPlanPushRecordAnswerPage = selectPage(page, wrapper);
        return new PageResult<>(evalPlanPushRecordAnswerPage.getRecords(), evalPlanPushRecordAnswerPage.getTotal());
    }

    default List<EvalPlanPushRecordAnswerDO> selectList(EvalPlanPushRecordAnswerListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<EvalPlanPushRecordAnswerDO>()
                .eqIfPresent(EvalPlanPushRecordAnswerDO::getEvalNo, reqVO.getEvalNo())
                .eqIfPresent(EvalPlanPushRecordAnswerDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(EvalPlanPushRecordAnswerDO::getJgryxm, reqVO.getJgryxm())
                .betweenIfPresent(EvalPlanPushRecordAnswerDO::getStartTime, reqVO.getStartTime())
                .betweenIfPresent(EvalPlanPushRecordAnswerDO::getEndTime, reqVO.getEndTime())
                .betweenIfPresent(EvalPlanPushRecordAnswerDO::getSubmitTime, reqVO.getSubmitTime())
                .eqIfPresent(EvalPlanPushRecordAnswerDO::getSubmitAnswer, reqVO.getSubmitAnswer())
                .eqIfPresent(EvalPlanPushRecordAnswerDO::getScore, reqVO.getScore())
                .eqIfPresent(EvalPlanPushRecordAnswerDO::getEvalResults, reqVO.getEvalResults())
                .eqIfPresent(EvalPlanPushRecordAnswerDO::getTimeSpentAnswer, reqVO.getTimeSpentAnswer())
                .eqIfPresent(EvalPlanPushRecordAnswerDO::getStatus, reqVO.getStatus())
                .orderByDesc(EvalPlanPushRecordAnswerDO::getAddTime));
    }


}
