package com.rs.module.pam.controller.app;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.letter.vo.LetterPostRespVO;
import com.rs.module.pam.controller.app.vo.AppLetterPostSaveReqVO;
import com.rs.module.pam.entity.letter.LetterPostDO;
import com.rs.module.pam.service.letter.LetterPostService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监所事务管理-家属通信-寄信登记")
@RestController
@RequestMapping("/app/pam/letter/letterPost")
@Validated
public class AppLetterPostController {

    @Resource
    private LetterPostService letterPostService;

    @PostMapping("/create")
    @ApiOperation(value = "App-创建监所事务管理-家属通信-寄信登记")
    public CommonResult<String> appCreateLetterPost(@Valid @RequestBody AppLetterPostSaveReqVO appLetterPostSaveReqVO) {
        return success(letterPostService.appCreateLetterPost(appLetterPostSaveReqVO));
    }

    @GetMapping("/page")
    @ApiOperation(value = "App-寄信管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小"),
            @ApiImplicitParam(name = "jgrybm", value = "被监管人员编码"),
            @ApiImplicitParam(name = "type", value = "寄信周期类型 1 全部，2 今天，3 昨天，4 近一周")
    })
    public CommonResult<PageResult<LetterPostRespVO>> getAppLetterPostPage(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                                           @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                                           @RequestParam("jgrybm") String jgrybm,
                                                                           @RequestParam("type") String type) {
        PageResult<LetterPostDO> pageResult = letterPostService.getAppLetterPostPage(pageNo, pageSize, jgrybm, type);
        return success(BeanUtils.toBean(pageResult, LetterPostRespVO.class));
    }

}
