package com.rs.module.pam.controller.admin.duty.day.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-监室值日列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DayDutyListRespVO extends BaseVO {

    @ApiModelProperty("值日日期")
    private Date dutyDate;

    @ApiModelProperty("人员信息")
    private List<DayDutyInfoRespVO> dutyInfo;

}
