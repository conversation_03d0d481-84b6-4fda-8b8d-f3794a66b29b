package com.rs.module.pam.controller.admin.library.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-图书借阅申请新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LibraryBorrowingApprovalReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    @NotEmpty(message = "主键不能为空")
    private String id;

    @ApiModelProperty("发放时间")
    private Date distributionTime;

    @ApiModelProperty("归还时间")
    private Date returnTime;

    @ApiModelProperty("借阅状态（字典：ZD_TSJY_JYZT） 2：审批通过（待发放） 5：审批不通过  3：发放（待归还） 4：归还（已归还）")
    @NotEmpty(message = "借阅状态（字典：ZD_TSJY_JYZT）不能为空")
    private String status;

    @ApiModelProperty("审核意见")
    private String approvalComments;


}
