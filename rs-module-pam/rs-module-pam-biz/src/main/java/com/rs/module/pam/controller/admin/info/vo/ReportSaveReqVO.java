package com.rs.module.pam.controller.admin.info.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-信息报备新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportSaveReqVO extends BaseVO{

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("数据来源（字典：ZD_DATA_SOURCES）")
    @NotEmpty(message = "数据来源（字典：ZD_DATA_SOURCES）不能为空")
    private String dataSources;

    @ApiModelProperty("报备人")
    @NotEmpty(message = "报备人不能为空")
    private String reporterId;

    @ApiModelProperty("报备人姓名")
    @NotEmpty(message = "报备人姓名不能为空")
    private String reporterName;

    @ApiModelProperty("监室号ID")
    @NotEmpty(message = "监室号ID不能为空")
    private String roomId;

    @ApiModelProperty("监室名称")
    @NotEmpty(message = "监室名称不能为空")
    private String roomName;

    @ApiModelProperty("对象ID")
    @NotEmpty(message = "对象ID不能为空")
    private String objectId;

    @ApiModelProperty("报备对象名称")
    @NotEmpty(message = "报备对象名称不能为空")
    private String objectName;

    @ApiModelProperty("报备时间类型（字典：ZD_XXBB_BBSJLX）")
    @NotEmpty(message = "报备时间类型（字典：ZD_XXBB_BBSJLX）不能为空")
    private String reportTimeType;

    @ApiModelProperty("报备开始时间")
    private Date reportStartTime;

    @ApiModelProperty("报备结束时间")
    private Date reportEndTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("报备状态（字典：ZD_XXBB_BBZT）")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("报备事由")
    private List<ReportItemSaveReqVO> items;


}
