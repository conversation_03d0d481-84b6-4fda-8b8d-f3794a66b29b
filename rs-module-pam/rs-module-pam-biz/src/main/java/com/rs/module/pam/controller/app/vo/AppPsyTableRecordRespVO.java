package com.rs.module.pam.controller.app.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/6/17 09:33
 */
@Data
@ApiModel(description = "管理后台 - 监所事务管理-心理测评 Request VO")
public class AppPsyTableRecordRespVO {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("测评编号")
    private String evalNo;
    @ApiModelProperty("计划名称")
    private String planName;
    @ApiModelProperty("计划编码")
    private String planCode;
    @ApiModelProperty("计划类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_PSY_JHLX")
    private String planType;
    @ApiModelProperty("计划创建人")
    private String planAddUserName;
    @ApiModelProperty("量表ID")
    private String tableId;
    @ApiModelProperty("量表名称")
    private String tableName;
    @ApiModelProperty("预计时长，单位分钟")
    private Integer estimatedDuration;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("推送时间")
    private Date pushTime;
    @ApiModelProperty("填写状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_PSY_TXZT")
    private String fillingStatus;
    @ApiModelProperty("填写时间")
    private Date fillingTime;
    @ApiModelProperty("填写平台")
    private String fillingPlatform;
    @ApiModelProperty("测评得分")
    private BigDecimal score;
    @ApiModelProperty("测评结果")
    private String evalResults;

}
