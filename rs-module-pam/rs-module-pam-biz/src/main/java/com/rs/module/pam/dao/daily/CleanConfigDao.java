package com.rs.module.pam.dao.daily;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.daily.vo.CleanConfigListReqVO;
import com.rs.module.pam.entity.daily.CleanConfigDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 监所事务管理-日常清监检查项配置 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface CleanConfigDao extends IBaseDao<CleanConfigDO> {


    default List<CleanConfigDO> selectList(CleanConfigListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CleanConfigDO>()
                .eqIfPresent(CleanConfigDO::getCheckType, reqVO.getCheckType())
                .eqIfPresent(CleanConfigDO::getOrgCode, reqVO.getOrgCode())
                .orderByDesc(CleanConfigDO::getAddTime));
    }


}
