package com.rs.module.pam.cons;

/**
 * 积分日志处理结果码枚举
 */
public enum LogHandleCodeEnum {

	HANDLE_CODE_SUCCESS("0","成功"),
	HANDLE_CODE_DATE_VALUE_ERROR("1001","业务时间错误"),
	HANDLE_CODE_ORG_CODE_EMPTY("1002","机构编号为空");

	private LogHandleCodeEnum(String code, String name) {
		this.code = code;
		this.name = name;
	}

	private String code ;
	private String name ;
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	public static LogHandleCodeEnum getAdditionalTypeByCode(String code) {
		for(LogHandleCodeEnum em : LogHandleCodeEnum.values()) {
			if(em.getCode().equals(code)) {
				return em;
			}
		}
		return null;
	}

}
