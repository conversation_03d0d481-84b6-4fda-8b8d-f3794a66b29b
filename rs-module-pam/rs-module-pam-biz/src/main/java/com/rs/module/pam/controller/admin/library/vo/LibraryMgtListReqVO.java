package com.rs.module.pam.controller.admin.library.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 监所事务管理-图书管理列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LibraryMgtListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("图书名称")
    private String name;

    @ApiModelProperty("作者")
    private String author;

    @ApiModelProperty("出版社")
    private String press;

    @ApiModelProperty("总库存")
    private Integer totalInventory;

    @ApiModelProperty("可借用库存")
    private Integer borrowedInventory;

    @ApiModelProperty("封面图片URL")
    private String coverImgUrl;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("上架状态（1：已上架，0：已下架）")
    private String status;

}
