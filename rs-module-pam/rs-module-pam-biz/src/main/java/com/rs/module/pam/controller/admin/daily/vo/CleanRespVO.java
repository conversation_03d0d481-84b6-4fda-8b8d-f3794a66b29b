package com.rs.module.pam.controller.admin.daily.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-日常清监登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CleanRespVO extends BaseVO implements TransPojo{

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("数据来源（字典：ZD_DATA_SOURCES）")
    private String dataSources;
    @ApiModelProperty("检查监室，多个逗号分割")
    private String checkRoomId;
    @ApiModelProperty("检查监室名称，多个逗号分割")
    private String checkRoomName;
    @ApiModelProperty("带队领导ID，多个逗号分割")
    private String leaderUserSfzh;
    @ApiModelProperty("带队领导ID，多个逗号分割")
    private String leaderUserName;
    @ApiModelProperty("参加民警，多个逗号分割")
    private String involvementUserSfzh;
    @ApiModelProperty("参加民警ID，多个逗号分割")
    private String involvementUserName;
    @ApiModelProperty("检查时间")
    private Date checkTime;
    @ApiModelProperty("检查内容项ID,多个逗号分割")
    private String checkItemId;
    @ApiModelProperty("检查内容")
    private String checkContent;
    @ApiModelProperty("登记经办人")
    private String operatorSfzh;
    @ApiModelProperty("登记经办人姓名")
    private String operatorXm;
    @ApiModelProperty("登记状态（字典：ZD_ZDRYGZ_GZZT）")
    private String status;
    @ApiModelProperty("登记时间")
    private Date operatorTime;
    @ApiModelProperty("是否存在违禁(0:否,1:是)")
    private Short isViolation;
    @ApiModelProperty("违禁情况")
    private String violationContent;
    @ApiModelProperty("违禁附件URL")
    private String violationAttachmentUrl;
    @ApiModelProperty("是否存在安全隐患(0:否,1:是)")
    private Short isHiddenDanger;
    @ApiModelProperty("安全隐患情况")
    private String hiddenDangerContent;
    @ApiModelProperty("安全隐患附件URL")
    private String hiddenDangerAttachmentUrl;
    @ApiModelProperty("备注")
    private String remarks;
    @ApiModelProperty("整改时间")
    private Date rectificationrTime;
    @ApiModelProperty("整改情况")
    private String rectificationrContent;
    @ApiModelProperty("所领导审批人身份证号")
    private String leaderApproverSfzh;
    @ApiModelProperty("所领导审批人姓名")
    private String leaderApproverXm;
    @ApiModelProperty("所领导审批时间")
    private Date leaderApproverTime;
    @ApiModelProperty("所领导审核意见")
    private String leaderApprovalComments;
    @ApiModelProperty("检查类型(1: 日常清监检查; 2:安全大检查)")
    private String checkType;
    @ApiModelProperty("整改操作人身份证号")
    private String rectificationrOperSfz;
    @ApiModelProperty("整改操作人姓名")
    private String rectificationrOperXm;
}
