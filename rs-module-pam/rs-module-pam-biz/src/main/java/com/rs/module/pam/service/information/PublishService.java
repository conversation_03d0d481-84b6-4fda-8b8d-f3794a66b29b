package com.rs.module.pam.service.information;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.pam.controller.admin.information.vo.PublishListReqVO;
import com.rs.module.pam.controller.admin.information.vo.PublishPageReqVO;
import com.rs.module.pam.controller.admin.information.vo.PublishSaveReqVO;
import com.rs.module.pam.entity.information.PublishDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 监所事务管理-监室信息发布 Service 接口
 *
 * <AUTHOR>
 */
public interface PublishService extends IBaseService<PublishDO>{

    /**
     * 创建监所事务管理-监室信息发布
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPublish(@Valid PublishSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-监室信息发布
     *
     * @param updateReqVO 更新信息
     */
    void updatePublish(@Valid PublishSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-监室信息发布
     *
     * @param id 编号
     */
    void deletePublish(String id);

    /**
     * 获得监所事务管理-监室信息发布
     *
     * @param id 编号
     * @return 监所事务管理-监室信息发布
     */
    PublishDO getPublish(String id);

    /**
    * 获得监所事务管理-监室信息发布分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-监室信息发布分页
    */
    PageResult<PublishDO> getPublishPage(PublishPageReqVO pageReqVO);

    /**
    * 获得监所事务管理-监室信息发布列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-监室信息发布列表
    */
    List<PublishDO> getPublishList(PublishListReqVO listReqVO);


}
