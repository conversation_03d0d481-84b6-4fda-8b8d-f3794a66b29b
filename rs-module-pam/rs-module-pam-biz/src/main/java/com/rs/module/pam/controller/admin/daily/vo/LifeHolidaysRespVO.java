package com.rs.module.pam.controller.admin.daily.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-一日生活制度节假日 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LifeHolidaysRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("节假日日期")
    private Date holidaysDate;
    @ApiModelProperty("节假日日期")
    private String holidaysName;
}
