package com.rs.module.pam.service.duty.day;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.google.common.collect.Lists;
import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.common.util.DateUtils;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.feign.SocketPushFeign;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.duty.day.vo.DayDutyShiftSaveReqVO;
import com.rs.module.pam.controller.admin.duty.day.vo.DayShiftRespVO;
import com.rs.module.pam.controller.admin.duty.day.vo.DayShiftSaveReqVO;
import com.rs.module.pam.dao.duty.day.DayDutyDao;
import com.rs.module.pam.dao.duty.day.DayDutyRecordsDao;
import com.rs.module.pam.dao.duty.day.DayDutyShiftDao;
import com.rs.module.pam.entity.duty.day.DayDutyShiftDO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * 监所事务管理-值日班次 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DayDutyShiftServiceImpl extends BaseServiceImpl<DayDutyShiftDao, DayDutyShiftDO> implements DayDutyShiftService {

    @Resource
    private DayDutyDao dayDutyDao;
    @Resource
    private DayDutyRecordsDao dayDutyRecordsDao;
    @Resource
    private DayDutyShiftDao dayDutyShiftDao;
    @Resource
    private SocketPushFeign socketPushFeign;
    @Resource
    private BspApi bspApi;

    @Override
    public void shiftManageSave(DayDutyShiftSaveReqVO reqVO) {
        List<DayShiftSaveReqVO> shiftList = reqVO.getShiftList();
        String roomId = reqVO.getRoomId();
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        // 校验是保存还是编辑
        List<DayDutyShiftDO> dbShiftList = dayDutyShiftDao.selectList(sessionUser.getOrgCode(), reqVO.getRoomId());

        // 是否已经到了生效时间
        boolean effectiveStart = dbShiftList.isEmpty() || dbShiftList.get(0).getEffectiveStartDate().getTime() <= System.currentTimeMillis();
        // 班次名变动。记录出id后面进行更新
        List<String> shiftNameChangeIdList = new ArrayList<>();
        // 新旧交集的id。用于删除之前有现在没有的班次
        List<String> intersectIdList = new ArrayList<>();
        // 是否有版本变动。 修改了开始结束时间， 或增加和删除条目，认为是版本变动
        boolean versionChange = false;
        Date tomorrowDate = new Date(DateUtils.startOfDay().getTime() + 86400000L);
        // 本次新增的班次
        List<DayDutyShiftDO> insertList = new ArrayList<>();
        for (DayShiftSaveReqVO shift : shiftList) {
            Optional<DayDutyShiftDO> optional = dbShiftList.stream().filter(f ->
                    ObjectUtils.equals(shift.getId(), f.getId())).findFirst();
            if (optional.isPresent()) {
                DayDutyShiftDO obj = optional.get();
                intersectIdList.add(obj.getId());
                shift.setId(obj.getId());
                if (!ObjectUtils.equals(shift.getShiftName(), obj.getShiftName())) {
                    shiftNameChangeIdList.add(obj.getId());
                }
            } else {
                versionChange = true;
                insertList.add(BeanUtils.toBean(shift, DayDutyShiftDO.class));
            }
        }
        versionChange = versionChange || intersectIdList.size() != dbShiftList.size();
        if (versionChange && effectiveStart) {
            // 增加新版版
            for (DayShiftSaveReqVO shift : shiftList) {
                DayDutyShiftDO entity = new DayDutyShiftDO();
                entity.setRoomId(roomId);
                entity.setShiftName(shift.getShiftName());
                entity.setSort(shift.getSort());
                // 明天开始时间。明天有效
                entity.setEffectiveStartDate(tomorrowDate);
                dayDutyShiftDao.insert(entity);
            }
            // 将旧的 有效日期回填上。方便后续使用
            if (!dbShiftList.isEmpty()) {
                List<String> ids = dbShiftList.stream().map(DayDutyShiftDO::getId).collect(Collectors.toList());
                DayDutyShiftDO shiftDO = new DayDutyShiftDO();
                shiftDO.setEffectiveEndDate(tomorrowDate);
                dayDutyShiftDao.update(shiftDO, new LambdaQueryWrapper<DayDutyShiftDO>().in(DayDutyShiftDO::getId, ids));
            }
        }
        if (!versionChange || !effectiveStart) {
            // 删除之前有，现在没有的
            List<String> collect = dbShiftList.stream().filter(f -> !intersectIdList.contains(f.getId())).map(DayDutyShiftDO::getId).collect(Collectors.toList());
            if (!collect.isEmpty()) {
                dayDutyShiftDao.deleteBatchIds(collect);
            }
            // 更新
            for (DayShiftSaveReqVO shift : shiftList) {
                if (shiftNameChangeIdList.contains(shift.getId())) {
                    DayDutyShiftDO shiftDO = new DayDutyShiftDO();
                    shiftDO.setId(shift.getId());
                    shiftDO.setShiftName(shift.getShiftName());
                    shiftDO.setSort(shift.getSort());
                    dayDutyShiftDao.updateById(shiftDO);
                }
            }
            // 插入
            for (DayDutyShiftDO shift : insertList) {
                // 本次有新增的部分
                DayDutyShiftDO shiftDO = new DayDutyShiftDO();
                shiftDO.setRoomId(roomId);
                shiftDO.setShiftName(shift.getShiftName());
                shiftDO.setSort(shift.getSort());
                Date effectiveStartDate = dbShiftList.get(0).getEffectiveStartDate();
                shiftDO.setEffectiveStartDate(effectiveStartDate);
                dayDutyShiftDao.insert(shiftDO);
            }
        }
        // 任何修改 都将删除后面排班
        if (versionChange) {
            dayDutyRecordsDao.deleteByDutyDate(sessionUser.getOrgCode(), roomId, tomorrowDate);
            dayDutyDao.cleanTablePrisonRoomDuty(sessionUser.getOrgCode(), roomId, tomorrowDate);
        }

        // 检测到其他民警对值日班次进行了更改，将刷新界面”，此时出现一个按钮“确认
        PushMessageForm form = new PushMessageForm();
        form.setAction(SocketActionConstants.web_dutyShiftChange);
        form.setPrisonIds(Lists.newArrayList(sessionUser.getOrgCode()));
        form.setUserIdBlacklist(Lists.newArrayList(sessionUser.getId()));
        form.setTerminal(SocketActionConstants.PushMessageTerminalEnum.PLATFORM.name());
        try {
            socketPushFeign.pushMessageToPrison(form);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        // 班次变动通知仓内屏
    }

    @Override
    public List<DayShiftRespVO> getShift(String orgCode, String roomId) {
        List<DayDutyShiftDO> effectiveShift = dayDutyShiftDao.getEffectiveShift(orgCode, roomId);
        if (CollectionUtil.isEmpty(effectiveShift)) {
            effectiveShift = dayDutyShiftDao.createDefaultShift(orgCode, roomId);
        }
        return BeanUtils.toBean(effectiveShift, DayShiftRespVO.class);
    }

}
