package com.rs.module.pam.service.duty;

import cn.hutool.core.collection.CollectionUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.module.pam.dao.duty.RecordsDao;
import com.rs.module.pam.entity.duty.RecordsDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;


/**
 * 监所事务管理-监室值班记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RecordsServiceImpl extends BaseServiceImpl<RecordsDao, RecordsDO> implements RecordsService {

    @Resource
    private RecordsDao recordsDao;

    @Override
    public List<RecordsDO> selectList(String orgCode, String roomId, Date startDate, Date endDate) {
        List<RecordsDO> recordsList = recordsDao.selectList(orgCode, roomId, startDate, endDate);
        if (CollectionUtil.isNotEmpty(recordsList)) {
            return recordsList;
        }
        return Collections.emptyList();
    }

    @Override
    public RecordsDO getNowRecords(String orgCode, String roomId, String shiftId) {
        return recordsDao.getNowRecords(orgCode, roomId, shiftId);
    }


}
