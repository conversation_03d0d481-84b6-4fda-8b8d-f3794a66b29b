package com.rs.module.pam.entity.cook;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 监所事务管理-特殊餐申请 DO
 *
 * <AUTHOR>
 */
@TableName("pam_cook_special_apply")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpecialApplyDO extends BaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 配餐类型（字典：ZD_PCGL_PCLX ）
     */
    private String mealType;
    /**
     * 配餐开始日期
     */
    private Date mealStartTime;
    /**
     * 配餐结束时间
     */
    private Date mealEndTime;
    /**
     * 用餐时段，多选逗号分割（字典：ZD_PCGL_DSLX）
     */
    private String mealPeriod;
    /**
     * 指定日期类型（字典：ZD_PCGL_ZDRQLX）
     */
    private String specifiedDateType;
    /**
     * 指定日期，多选逗号分割
     */
    private String specifiedDate;
    /**
     * 申请原因
     */
    private String reason;
    /**
     * 登记状态
     */
    private String regStatus;
    /**
     * 登记经办人
     */
    private String regOperatorSfzh;
    /**
     * 登记经办人姓名
     */
    private String regOperatorXm;
    /**
     * 登记时间
     */
    private Date regTime;
    /**
     * 医生审批人身份证号
     */
    private String doctorApproverSfzh;
    /**
     * 医生审批人姓名
     */
    private String doctorApproverXm;
    /**
     * 医生审批时间
     */
    private Date doctorApproverTime;
    /**
     * 医生审批结果
     */
    private String doctorApprovalResult;
    /**
     * 医生审核意见
     */
    private String doctorApprovalComments;
    /**
     * 所领导审批人身份证号
     */
    private String leaderApproverSfzh;
    /**
     * 所领导审批人姓名
     */
    private String leaderApproverXm;
    /**
     * 所领导审批时间
     */
    private Date leaderApproverTime;
    /**
     * 所领导审批结果
     */
    private String leaderApprovalResult;
    /**
     * 所领导审核意见
     */
    private String leaderApprovalComments;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

}
