package com.rs.module.pam.service.info;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.info.vo.ReportApproveReqVO;
import com.rs.module.pam.controller.admin.info.vo.ReportHistoryRespVO;
import com.rs.module.pam.controller.admin.info.vo.ReportRespVO;
import com.rs.module.pam.controller.admin.info.vo.ReportSaveReqVO;
import com.rs.module.pam.entity.info.ReportDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 监所事务管理-信息报备 Service 接口
 *
 * <AUTHOR>
 */
public interface ReportService extends IBaseService<ReportDO>{

    /**
     * 创建监所事务管理-信息报备
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createReport(@Valid ReportSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-信息报备
     *
     * @param updateReqVO 更新信息
     */
    void updateReport(@Valid ReportSaveReqVO updateReqVO);

    /**
     * 获得监所事务管理-信息报备
     *
     * @param id 编号
     * @return 监所事务管理-信息报备
     */
    ReportDO getReport(String id);

    /**
     * 获得监所事务管理-信息报备
     *
     * @param id 编号
     * @return 监所事务管理-信息报备
     */
    ReportRespVO getReportRespVO(String id);

    /**
     * 获得监所事务管理-信息报备
     *
     * @param jgrybm 监管人员编号
     * @param roomId 监室编号
     * @return 监所事务管理-信息报备
     */
    ReportRespVO getReportRespVOByJgrybmAndRoomId(String jgrybm, String roomId);

    /**
     * 获得监所事务管理-信息报备历史
     * @param jgrybm
     * @return
     */
    List<ReportHistoryRespVO> getHistoryReport(String jgrybm);

    /**
     * 审批-信息报备
     * @param approveReqVO
     */
    void policeApprove(ReportApproveReqVO approveReqVO);

}
