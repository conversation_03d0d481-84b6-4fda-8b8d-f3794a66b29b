package com.rs.module.pam.service.duty;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.duty.vo.DutyConfigSaveReqVO;
import com.rs.module.pam.entity.duty.DutyConfigDO;

import javax.validation.Valid;

/**
 * 监所事务管理-值班规则配置 Service 接口
 *
 * <AUTHOR>
 */
public interface DutyConfigService extends IBaseService<DutyConfigDO>{

    /**
     * 创建值班规则配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createConfig(@Valid DutyConfigSaveReqVO createReqVO);

    /**
     * 更新值班规则配置
     *
     * @param updateReqVO 更新信息
     */
    void updateConfig(@Valid DutyConfigSaveReqVO updateReqVO);

    /**
     * 获得值班规则配置
     *
     * @param orgCode 机构编号
     * @return 值班规则配置
     */
    DutyConfigDO getConfig(String orgCode);


}
