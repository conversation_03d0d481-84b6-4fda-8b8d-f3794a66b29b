package com.rs.module.pam.controller.admin.daily.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-一日生活制度节假日列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LifeHolidaysListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("节假日日期")
    private Date[] holidaysDate;

    @ApiModelProperty("节假日日期")
    private String holidaysName;

    @ApiModelProperty("机构码")
    private String orgCode;

    @ApiModelProperty("县分局码")
    private String regCode;

    @ApiModelProperty("市局码")
    private String cityCode;

}
