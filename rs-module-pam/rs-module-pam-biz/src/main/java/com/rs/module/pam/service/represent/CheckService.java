package com.rs.module.pam.service.represent;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.pam.controller.admin.represent.vo.CheckListReqVO;
import com.rs.module.pam.controller.admin.represent.vo.CheckPageReqVO;
import com.rs.module.pam.controller.admin.represent.vo.CheckSaveReqVO;
import com.rs.module.pam.entity.represent.CheckDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 监所事务管理-人脸库及人员库信息进行比对 Service 接口
 *
 * <AUTHOR>
 */
public interface CheckService extends IBaseService<CheckDO>{

    /**
     * 创建监所事务管理-人脸库及人员库信息进行比对
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCheck(@Valid CheckSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-人脸库及人员库信息进行比对
     *
     * @param updateReqVO 更新信息
     */
    void updateCheck(@Valid CheckSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-人脸库及人员库信息进行比对
     *
     * @param id 编号
     */
    void deleteCheck(String id);

    /**
     * 获得监所事务管理-人脸库及人员库信息进行比对
     *
     * @param id 编号
     * @return 监所事务管理-人脸库及人员库信息进行比对
     */
    CheckDO getCheck(String id);

    /**
    * 获得监所事务管理-人脸库及人员库信息进行比对分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-人脸库及人员库信息进行比对分页
    */
    PageResult<CheckDO> getCheckPage(CheckPageReqVO pageReqVO);

    /**
    * 获得监所事务管理-人脸库及人员库信息进行比对列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-人脸库及人员库信息进行比对列表
    */
    List<CheckDO> getCheckList(CheckListReqVO listReqVO);


}
