package com.rs.module.pam.controller.admin.information;

import com.alibaba.druid.util.StringUtils;
import com.gosun.zhjg.common.util.StringUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.cons.CommonConstants;
import com.rs.module.pam.controller.admin.information.vo.PublishListReqVO;
import com.rs.module.pam.controller.admin.information.vo.PublishPageReqVO;
import com.rs.module.pam.controller.admin.information.vo.PublishRespVO;
import com.rs.module.pam.controller.admin.information.vo.PublishSaveReqVO;
import com.rs.module.pam.entity.information.PublishDO;
import com.rs.module.pam.enums.PublishBizTypeEnum;
import com.rs.module.pam.service.information.PublishService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监室信息发布")
@RestController
@RequestMapping("/pam/information/publish")
@Validated
public class PublishController {

    @Resource
    private PublishService publishService;

    @PostMapping("/create")
    @ApiOperation(value = "创建监室信息发布")
    public CommonResult<String> createPublish(@Valid @RequestBody PublishSaveReqVO createReqVO) {
        if(StringUtils.isEmpty(createReqVO.getBizType())){
            createReqVO.setBizType(PublishBizTypeEnum.JSSWFB.getCode());
        }
        return success(publishService.createPublish(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新监室信息发布")
    public CommonResult<Boolean> updatePublish(@Valid @RequestBody PublishSaveReqVO updateReqVO) {
        if(StringUtils.isEmpty(updateReqVO.getBizType())){
            updateReqVO.setBizType(PublishBizTypeEnum.JSSWFB.getCode());
        }
        publishService.updatePublish(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除监室信息发布")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deletePublish(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           publishService.deletePublish(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得监室信息发布")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<PublishRespVO> getPublish(@RequestParam("id") String id) {
        PublishDO publish = publishService.getPublish(id);
        return success(BeanUtils.toBean(publish, PublishRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得监室信息发布分页", hidden = true)
    public CommonResult<PageResult<PublishRespVO>> getPublishPage(@Valid @RequestBody PublishPageReqVO pageReqVO) {
        PageResult<PublishDO> pageResult = publishService.getPublishPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PublishRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得监室信息发布列表", hidden = true)
    public CommonResult<List<PublishRespVO>> getList(@Valid @RequestBody PublishListReqVO listReqVO) {
        List<PublishDO> list = publishService.getPublishList(listReqVO);
        return success(BeanUtils.toBean(list, PublishRespVO.class));
    }

    @PostMapping("/disOrEna")
    @ApiOperation(value = "信息发布启用或停用")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<String> disOrEna(@RequestParam(value = "id") String id) {
        PublishDO publish = publishService.getById(id);
        if (CommonConstants.INFORMATION_PUBLIC_STATUS_ENABLE.equals(publish.getStatus())) {
            publish.setStatus(CommonConstants.INFORMATION_PUBLIC_STATUS_DISABLE);
        } else {
            publish.setStatus(CommonConstants.INFORMATION_PUBLIC_STATUS_ENABLE);
        }
        publishService.updateById(publish);
        return success("操作成功");
    }

}
