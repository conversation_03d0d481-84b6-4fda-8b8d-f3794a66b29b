package com.rs.module.pam.controller.admin.psy.vo;

import io.swagger.annotations.ApiModel;

import java.time.LocalTime;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评计划推送记录答案分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EvalPlanPushRecordAnswerPageReqVO extends PageParam {

    @ApiModelProperty("测评编号")
    private String evalNo;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("提交时间")
    private Date[] submitTime;

    @ApiModelProperty("测评得分")
    private BigDecimal score;

    @ApiModelProperty("测评结果")
    private String evalResults;

    @ApiModelProperty("作答用时")
    private LocalTime timeSpentAnswer;

    @ApiModelProperty("提交状态")
    private String status;

    @ApiModelProperty("排序属性")
    List<OrderItem> orderFields;
}
