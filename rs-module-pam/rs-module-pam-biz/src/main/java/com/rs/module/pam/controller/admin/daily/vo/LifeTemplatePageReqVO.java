package com.rs.module.pam.controller.admin.daily.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-一日生活制度模板分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LifeTemplatePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("制度名称")
    private String templateName;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
