package com.rs.module.pam.controller.admin.psy.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评计划 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EvalPlanRespVO extends BaseVO implements TransPojo {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("计划名称")
    private String planName;
    @ApiModelProperty("计划编码")
    private String planCode;
    @ApiModelProperty("计划类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_PSY_JHLX")
    private String planType;
    @ApiModelProperty("计划说明")
    private String remark;
    @ApiModelProperty("量表ID")
    private String tableId;
    @ApiModelProperty("量表名称")
    private String tableName;
    @ApiModelProperty("触发类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_PSY_JHCFFS")
    private String triggerType;
    @ApiModelProperty("触发配置，json存储")
    private String triggerConfig;
    @ApiModelProperty("推送对象")
    private String pushTarget;
    @ApiModelProperty("推送对象名称")
    private String pushTargetName;
    @ApiModelProperty("推送对象姓名")
    private String pushTargetUserName;
    @ApiModelProperty("测评次数")
    private Integer evalNumber;
    @ApiModelProperty("完成时限")
    private Integer completionDeadline;
    @ApiModelProperty("启动消息推送")
    private String enableMessagePush;
    @ApiModelProperty("消息推送配置")
    private String messagePush;
    @ApiModelProperty("测评状态（字典：ZD_PSY_CPZT）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_PSY_CPZT")
    private String status;
    @ApiModelProperty("启用状态: 0-禁用 1-启用")
    private String enableStatus;
    @ApiModelProperty("创建时间")
    private Date addTime;
    @ApiModelProperty("创建人")
    private String addUserName;
    @ApiModelProperty("运行天数")
    private int runDays;
    @ApiModelProperty("累计完成填写份数")
    private int fillNums;
    @ApiModelProperty("当前未完成份数")
    private int noFillNums;
    @ApiModelProperty("已超期份数")
    private int overFillNums;

}
