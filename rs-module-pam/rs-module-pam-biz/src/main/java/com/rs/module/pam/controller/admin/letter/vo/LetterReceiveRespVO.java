package com.rs.module.pam.controller.admin.letter.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-家属通信-收信登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LetterReceiveRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("数据来源（字典：ZD_DATA_SOURCES）")
    private String dataSources;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("送信人姓名")
    private String sendMailUser;
    @ApiModelProperty("关系（字典：ZD_RYGXDM）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_RYGXDM")
    private String relation;
    @ApiModelProperty("来信地址")
    private String sendAddress;
    @ApiModelProperty("来信日期")
    private Date sendDate;
    @ApiModelProperty("来信单位")
    private String sendPrison;
    @ApiModelProperty("信件邮编")
    private String mailNo;
    @ApiModelProperty("信件内容url")
    private String mailUrl;
    @ApiModelProperty("登记时间")
    private Date registerTime;
    @ApiModelProperty("登记人")
    private String registerUserSfzh;
    @ApiModelProperty("登记人姓名")
    private String registerUserName;
    @ApiModelProperty("签名图片")
    private String signUrl;
    @ApiModelProperty("信件状态 （字典：ZD_JSTX_SXZT）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JSTX_SXZT")
    private String status;
    @ApiModelProperty("转交人")
    private String passUser;
    @ApiModelProperty("转交时间")
    private Date passTime;
    @ApiModelProperty("转交备注")
    private String passRemark;
    @ApiModelProperty("管教审批人身份证号")
    private String gjApproverSfzh;
    @ApiModelProperty("管教审批人姓名")
    private String gjApproverXm;
    @ApiModelProperty("管教审批时间")
    private Date gjApproverTime;
    @ApiModelProperty("管教审批结果")
    private String gjApprovalResult;
    @ApiModelProperty("管教审核意见")
    private String gjApprovalComments;
    @ApiModelProperty("科组长审批人身份证号")
    private String groupApproverSfzh;
    @ApiModelProperty("科组长审批人姓名")
    private String groupApproverXm;
    @ApiModelProperty("科组长审批时间")
    private Date groupApproverTime;
    @ApiModelProperty("科组长审批结果")
    private String groupApprovalResult;
    @ApiModelProperty("科组长审核意见")
    private String groupApprovalComments;
    @ApiModelProperty("所领导审批人身份证号")
    private String leaderApproverSfzh;
    @ApiModelProperty("所领导审批人姓名")
    private String leaderApproverXm;
    @ApiModelProperty("所领导审批时间")
    private Date leaderApproverTime;
    @ApiModelProperty("所领导审批结果")
    private String leaderApprovalResult;
    @ApiModelProperty("所领导审核意见")
    private String leaderApprovalComments;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;
    @ApiModelProperty("签收时间")
    private Date receiptTime;
    @ApiModelProperty("送信人联系电话")
    private String sendContactNumber;
}
