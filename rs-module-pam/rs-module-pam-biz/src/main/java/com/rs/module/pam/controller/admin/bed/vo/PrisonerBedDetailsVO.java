package com.rs.module.pam.controller.admin.bed.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-人员床位详情 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonerBedDetailsVO extends BedDetailsVO implements TransPojo {

    private String id;

    @ApiModelProperty("监管人员编号")
    private String jgrybm;

    @ApiModelProperty("监管人员编号")
    private String zjhm;

    @ApiModelProperty(value = "监管人员姓名", hidden = true)
    private String xm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("监管人员正面照")
    private String frontPhoto;

    @ApiModelProperty("添加时间")
    private Date addTime;

    @ApiModelProperty("入仓时间(天)")
    private Integer enterDay;

    @ApiModelProperty(value = "是否重病号")
    private Boolean isSick;

    private String fxdj;

    @ApiModelProperty("风险等级")
    @Trans(type = TransType.DICTIONARY,  key = "ZD_JGRY_FXDJ")
    private String riskLevel;



}
