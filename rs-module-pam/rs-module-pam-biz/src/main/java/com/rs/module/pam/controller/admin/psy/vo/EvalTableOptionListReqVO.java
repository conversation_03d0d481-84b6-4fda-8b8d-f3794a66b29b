package com.rs.module.pam.controller.admin.psy.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评量表关联提目选项列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EvalTableOptionListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("评量表ID")
    private String tableId;

    @ApiModelProperty("题目id")
    private String questionId;

    @ApiModelProperty("选项代码，A、B、C、D等")
    private String optionCode;

    @ApiModelProperty("选项内容")
    private String optionText;

    @ApiModelProperty("题目选项分值")
    private BigDecimal score;

    @ApiModelProperty("选项顺序")
    private Integer sortOrder;

}
