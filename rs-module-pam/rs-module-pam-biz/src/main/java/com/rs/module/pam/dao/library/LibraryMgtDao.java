package com.rs.module.pam.dao.library;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.cons.CommonConstants;
import com.rs.module.pam.entity.daily.LifeDO;
import com.rs.module.pam.entity.library.LibraryMgtDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.pam.controller.admin.library.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 监所事务管理-图书管理 Dao
*
* <AUTHOR>
*/
@Mapper
public interface LibraryMgtDao extends IBaseDao<LibraryMgtDO> {


    default PageResult<LibraryMgtDO> selectPage(LibraryMgtPageReqVO reqVO) {
        Page<LibraryMgtDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<LibraryMgtDO> wrapper = new LambdaQueryWrapperX<LibraryMgtDO>()
            .likeIfPresent(LibraryMgtDO::getName, reqVO.getName())
            .eqIfPresent(LibraryMgtDO::getAuthor, reqVO.getAuthor())
            .eqIfPresent(LibraryMgtDO::getPress, reqVO.getPress())
            .eqIfPresent(LibraryMgtDO::getTotalInventory, reqVO.getTotalInventory())
            .eqIfPresent(LibraryMgtDO::getBorrowedInventory, reqVO.getBorrowedInventory())
            .eqIfPresent(LibraryMgtDO::getCoverImgUrl, reqVO.getCoverImgUrl())
            .eqIfPresent(LibraryMgtDO::getRemark, reqVO.getRemark())
            .eqIfPresent(LibraryMgtDO::getStatus, reqVO.getStatus())
            .eqIfPresent(LibraryMgtDO::getOrgCode, reqVO.getOrgCode())
            .eqIfPresent(LibraryMgtDO::getRegCode, reqVO.getRegCode())
            .eqIfPresent(LibraryMgtDO::getCityCode, reqVO.getCityCode())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(LibraryMgtDO::getAddTime);
        }
        Page<LibraryMgtDO> libraryMgtPage = selectPage(page, wrapper);
        return new PageResult<>(libraryMgtPage.getRecords(), libraryMgtPage.getTotal());
    }
    default List<LibraryMgtDO> selectList(LibraryMgtListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<LibraryMgtDO>()
            .likeIfPresent(LibraryMgtDO::getName, reqVO.getName())
            .eqIfPresent(LibraryMgtDO::getAuthor, reqVO.getAuthor())
            .eqIfPresent(LibraryMgtDO::getPress, reqVO.getPress())
            .eqIfPresent(LibraryMgtDO::getTotalInventory, reqVO.getTotalInventory())
            .eqIfPresent(LibraryMgtDO::getBorrowedInventory, reqVO.getBorrowedInventory())
            .eqIfPresent(LibraryMgtDO::getCoverImgUrl, reqVO.getCoverImgUrl())
            .eqIfPresent(LibraryMgtDO::getRemark, reqVO.getRemark())
            .eqIfPresent(LibraryMgtDO::getStatus, reqVO.getStatus())
        .orderByDesc(LibraryMgtDO::getAddTime));    }


    void updateBorrowedInventory(@Param("libraryId") String libraryId, @Param("livraryCount") int livraryCount);


    default PageResult<LibraryMgtDO> getAppLibraryMgtPage(int pageNo, int pageSize, String orgCode){
        Page<LibraryMgtDO> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapperX<LibraryMgtDO> wrapper = new LambdaQueryWrapperX<LibraryMgtDO>()
                .eq(LibraryMgtDO::getStatus, CommonConstants.ENABLED)
                .eq(LibraryMgtDO::getOrgCode, orgCode);

        wrapper.orderByDesc(LibraryMgtDO::getAddTime);
        Page<LibraryMgtDO> libraryMgtPage = selectPage(page, wrapper);
        return new PageResult<>(libraryMgtPage.getRecords(), libraryMgtPage.getTotal());
    }
}
