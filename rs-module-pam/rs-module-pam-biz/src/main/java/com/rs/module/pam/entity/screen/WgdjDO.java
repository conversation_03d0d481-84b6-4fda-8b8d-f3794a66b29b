package com.rs.module.pam.entity.screen;

import lombok.Data;

import java.util.Date;

/**
 * 实战平台-巡视管控-违规登记 DO
 *
 * <AUTHOR>
 */
@Data
public class WgdjDO {
    /**
     * 主键
     */
    private String id;

    /**
     * 违规地方名称
     */
    private String addressName;
    /**
     * 违规时间
     */
    private Date addTime;
    /**
     * 违规类型
     */
    private String violationType;
    /**
     * 违规内容
     */
    private String violationContent;

    /**
     * 状态
     */
    private String handleStatus;

    /**
     * 违规类别 1人工巡查，2智能终端
     */
    private String wglb;
}
