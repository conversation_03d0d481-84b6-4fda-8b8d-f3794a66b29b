package com.rs.module.pam.controller.admin.duty.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel(description = "管理后台 - 监所事务管理-值班组新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GroupSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("机构编号")
    @NotEmpty(message = "机构编号不能为空")
    private String orgCode;

    @ApiModelProperty("监室id")
    @NotEmpty(message = "监室id不能为空")
    private String roomId;

    @ApiModelProperty("值班组名（规则：两个被监管人员的姓名的首字母组合）")
    private String groupName;

    @ApiModelProperty("值班组序号，监室下自增")
    private Integer groupNo;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm1;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm2;

}
