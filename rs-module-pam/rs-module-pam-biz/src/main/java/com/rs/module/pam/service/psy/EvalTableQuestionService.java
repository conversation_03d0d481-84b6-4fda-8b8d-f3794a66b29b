package com.rs.module.pam.service.psy;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableQuestionListReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableQuestionOrderReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableQuestionPageReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableQuestionRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableQuestionSaveReqVO;
import com.rs.module.pam.entity.psy.EvalTableQuestionDO;

import java.util.List;
import java.util.Map;

/**
 * 监所事务管理-心理测评量表关联提目 Service 接口
 *
 * <AUTHOR>
 */
public interface EvalTableQuestionService extends IBaseService<EvalTableQuestionDO> {

    /**
     * 创建监所事务管理-心理测评量表关联提目
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createEvalTableQuestion(EvalTableQuestionSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-心理测评量表关联提目
     *
     * @param updateReqVO 更新信息
     */
    void updateEvalTableQuestion(EvalTableQuestionSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-心理测评量表关联提目
     *
     * @param id 编号
     */
    void deleteEvalTableQuestion(String id);

    /**
     * 获得监所事务管理-心理测评量表关联提目
     *
     * @param id 编号
     * @return 监所事务管理-心理测评量表关联提目
     */
    EvalTableQuestionDO getEvalTableQuestion(String id);

    /**
     * 获得监所事务管理-心理测评量表关联提目分页
     *
     * @param pageReqVO 分页查询
     * @return 监所事务管理-心理测评量表关联提目分页
     */
    PageResult<EvalTableQuestionRespVO> getEvalTableQuestionPage(EvalTableQuestionPageReqVO pageReqVO);

    /**
     * 获得监所事务管理-心理测评量表关联提目列表
     *
     * @param listReqVO 查询条件
     * @return 监所事务管理-心理测评量表关联提目列表
     */
    List<EvalTableQuestionDO> getEvalTableQuestionList(EvalTableQuestionListReqVO listReqVO);


    /**
     * 获得监所事务管理-心理测评量表关联提目详情
     *
     * @param id
     * @return
     */
    EvalTableQuestionRespVO getEvalTableQuestionRespVO(String id);

    /**
     * 获得监所事务管理-心理测评量表关联提目列表详情
     *
     * @param listReqVO
     * @return
     */
    List<EvalTableQuestionRespVO> getEvalTableQuestionListRespVO(EvalTableQuestionListReqVO listReqVO);

    /**
     * 获得监所事务管理-心理测评量表关联提目
     *
     * @param tableId
     * @return
     */
    Map<String, EvalTableQuestionRespVO> getEvalTableQuestionMap(String tableId);

    /**
     * 题目排序
     *
     * @param list
     */
    void questionOrder(List<EvalTableQuestionOrderReqVO> list);
}
