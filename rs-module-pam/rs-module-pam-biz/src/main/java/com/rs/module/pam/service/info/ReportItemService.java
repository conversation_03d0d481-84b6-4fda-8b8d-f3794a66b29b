package com.rs.module.pam.service.info;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.info.vo.ReportItemListReqVO;
import com.rs.module.pam.controller.admin.info.vo.ReportItemSaveReqVO;
import com.rs.module.pam.entity.info.ReportItemDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 监所事务管理-信息报备事项配置 Service 接口
 *
 * <AUTHOR>
 */
public interface ReportItemService extends IBaseService<ReportItemDO>{

    /**
     * 创建监所事务管理-信息报备事项配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createReportItem(ReportItemSaveReqVO createReqVO);

    void createReportItem(List<ReportItemSaveReqVO> createReqVOS);

    /**
     * 更新监所事务管理-信息报备事项配置
     *
     * @param updateReqVO 更新信息
     */
    void updateReportItem(@Valid ReportItemSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-信息报备事项配置
     *
     * @param id 编号
     */
    void deleteReportItem(String id);

    /**
     * 获得监所事务管理-信息报备事项配置
     *
     * @param id 编号
     * @return 监所事务管理-信息报备事项配置
     */
    ReportItemDO getReportItem(String id);

    /**
    * 获得监所事务管理-信息报备事项配置列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-信息报备事项配置列表
    */
    List<ReportItemDO> getReportItemList(ReportItemListReqVO listReqVO);

    /**
     * 更新状态
     * @param id
     * @param status
     */
    void updateStatus(String id, String status);

}
