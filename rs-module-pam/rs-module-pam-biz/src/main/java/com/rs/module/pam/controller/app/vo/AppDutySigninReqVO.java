package com.rs.module.pam.controller.app.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 监所事务管理-监室值班列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AppDutySigninReqVO extends BaseVO {

    /**
     * 监室编号
     */
    @ApiModelProperty(value = "机构编号", required = true)
    @NotEmpty(message = "机构编号不能为空")
    private String orgCode;
    /**
     * 监室编号
     */
    @ApiModelProperty(value = "监室编号", required = true)
    @NotEmpty(message = "监室编号不能为空")
    private String roomId;
    /**
     * 班次编号
     */
//    @ApiModelProperty(value = "班次编号", required = true)
//    @NotEmpty(message = "班次编号不能为空")
//    private String shiftId;
    /**
     * 签到人
     */
    @ApiModelProperty(value = "签到人", required = true)
    @NotEmpty(message = "签到人不能为空")
    private String jgrybm;
    /**
     * 抓拍图片
     */
    @ApiModelProperty(value = "抓拍图片", required = true)
//    @NotEmpty(message = "抓拍图片不能为空")
    private String snapPicture;

}
