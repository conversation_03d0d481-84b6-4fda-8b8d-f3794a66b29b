package com.rs.module.pam.controller.admin.bed.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-人员床位调整新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonerChangeSaveReqVO extends BaseVO{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("监室id")
    @NotEmpty(message = "监室id不能为空")
    private String roomId;

    @ApiModelProperty("监室名称")
    @NotEmpty(message = "监室名称不能为空")
    private String roomName;

    @ApiModelProperty("床位调整数据")
    private List<PrisonerBedDetailsVO> bedList;

}