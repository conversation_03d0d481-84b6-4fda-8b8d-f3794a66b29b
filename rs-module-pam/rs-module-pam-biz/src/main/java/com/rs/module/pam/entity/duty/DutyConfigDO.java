package com.rs.module.pam.entity.duty;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-值班规则配置 DO
 *
 * <AUTHOR>
 */
@TableName("pam_duty_config")
@KeySequence("pam_duty_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_duty_config")
public class DutyConfigDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 是否值班签到（1开启、0关闭）
     */
    private Short signinDutyEnable;
    /**
     * 值班签到语音提醒开启（1开启、0关闭）
     */
    private Short signinRemindEnable;
    /**
     * 语音播报静音时段，支持多个，格式：00:00:00-10:00:00,
     */
    private String silentTimeSlots;
    /**
     * signin_validity_period
     */
    private Short signinValidityPeriod;

}
