package com.rs.module.pam.controller.admin.duty.day.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@ApiModel(description = "管理后台 - 监室值日班次新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DayDutyShiftSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("监室id")
    private String roomId;

    private List<DayShiftSaveReqVO> shiftList;

}
