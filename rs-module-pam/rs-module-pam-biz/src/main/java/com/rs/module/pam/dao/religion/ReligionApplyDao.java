package com.rs.module.pam.dao.religion;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.entity.religion.ReligionApplyDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.pam.controller.admin.religion.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 监所事务管理-宗教申请 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ReligionApplyDao extends IBaseDao<ReligionApplyDO> {


    default PageResult<ReligionApplyDO> selectPage(ReligionApplyPageReqVO reqVO) {
        Page<ReligionApplyDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ReligionApplyDO> wrapper = new LambdaQueryWrapperX<ReligionApplyDO>()
            .eqIfPresent(ReligionApplyDO::getDataSources, reqVO.getDataSources())
            .eqIfPresent(ReligionApplyDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(ReligionApplyDO::getReligion, reqVO.getReligion())
            .eqIfPresent(ReligionApplyDO::getRemark, reqVO.getRemark())
            .eqIfPresent(ReligionApplyDO::getStatus, reqVO.getStatus())
            .eqIfPresent(ReligionApplyDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(ReligionApplyDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(ReligionApplyDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(ReligionApplyDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(ReligionApplyDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(ReligionApplyDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(ReligionApplyDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(ReligionApplyDO::getAddTime);
        }
        Page<ReligionApplyDO> religionApplyPage = selectPage(page, wrapper);
        return new PageResult<>(religionApplyPage.getRecords(), religionApplyPage.getTotal());
    }
    default List<ReligionApplyDO> selectList(ReligionApplyListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ReligionApplyDO>()
            .eqIfPresent(ReligionApplyDO::getDataSources, reqVO.getDataSources())
            .eqIfPresent(ReligionApplyDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(ReligionApplyDO::getReligion, reqVO.getReligion())
            .eqIfPresent(ReligionApplyDO::getRemark, reqVO.getRemark())
            .eqIfPresent(ReligionApplyDO::getStatus, reqVO.getStatus())
            .eqIfPresent(ReligionApplyDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(ReligionApplyDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(ReligionApplyDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(ReligionApplyDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(ReligionApplyDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(ReligionApplyDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(ReligionApplyDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(ReligionApplyDO::getAddTime));    }


    }
