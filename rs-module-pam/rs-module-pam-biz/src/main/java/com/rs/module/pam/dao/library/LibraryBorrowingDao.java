package com.rs.module.pam.dao.library;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.entity.library.LibraryBorrowingDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.pam.controller.admin.library.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 监所事务管理-图书借阅申请 Dao
*
* <AUTHOR>
*/
@Mapper
public interface LibraryBorrowingDao extends IBaseDao<LibraryBorrowingDO> {


    default PageResult<LibraryBorrowingDO> selectPage(LibraryBorrowingPageReqVO reqVO) {
        Page<LibraryBorrowingDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<LibraryBorrowingDO> wrapper = new LambdaQueryWrapperX<LibraryBorrowingDO>()
            .eqIfPresent(LibraryBorrowingDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(LibraryBorrowingDO::getRyxm, reqVO.getRyxm())
            .eqIfPresent(LibraryBorrowingDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(LibraryBorrowingDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(LibraryBorrowingDO::getLibraryId, reqVO.getLibraryId())
            .betweenIfPresent(LibraryBorrowingDO::getApplyTime, reqVO.getApplyTime())
            .betweenIfPresent(LibraryBorrowingDO::getBorrowingDeadlineTime, reqVO.getBorrowingDeadlineTime())
            .betweenIfPresent(LibraryBorrowingDO::getDistributionTime, reqVO.getDistributionTime())
            .betweenIfPresent(LibraryBorrowingDO::getReturnTime, reqVO.getReturnTime())
            .eqIfPresent(LibraryBorrowingDO::getStatus, reqVO.getStatus())
            .eqIfPresent(LibraryBorrowingDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(LibraryBorrowingDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(LibraryBorrowingDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(LibraryBorrowingDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(LibraryBorrowingDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(LibraryBorrowingDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(LibraryBorrowingDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(LibraryBorrowingDO::getAddTime);
        }
        Page<LibraryBorrowingDO> libraryBorrowingPage = selectPage(page, wrapper);
        return new PageResult<>(libraryBorrowingPage.getRecords(), libraryBorrowingPage.getTotal());
    }
    default List<LibraryBorrowingDO> selectList(LibraryBorrowingListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<LibraryBorrowingDO>()
            .eqIfPresent(LibraryBorrowingDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(LibraryBorrowingDO::getRyxm, reqVO.getRyxm())
            .eqIfPresent(LibraryBorrowingDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(LibraryBorrowingDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(LibraryBorrowingDO::getLibraryId, reqVO.getLibraryId())
            .betweenIfPresent(LibraryBorrowingDO::getApplyTime, reqVO.getApplyTime())
            .betweenIfPresent(LibraryBorrowingDO::getBorrowingDeadlineTime, reqVO.getBorrowingDeadlineTime())
            .betweenIfPresent(LibraryBorrowingDO::getDistributionTime, reqVO.getDistributionTime())
            .betweenIfPresent(LibraryBorrowingDO::getReturnTime, reqVO.getReturnTime())
            .eqIfPresent(LibraryBorrowingDO::getStatus, reqVO.getStatus())
            .eqIfPresent(LibraryBorrowingDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(LibraryBorrowingDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(LibraryBorrowingDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(LibraryBorrowingDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(LibraryBorrowingDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(LibraryBorrowingDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(LibraryBorrowingDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(LibraryBorrowingDO::getAddTime));    }


    Page<LibraryBorrowingDO> personalBorrowingRecordPage(Page<LibraryBorrowingDO> page, @Param("jgrybm") String jgrybm,
            @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
