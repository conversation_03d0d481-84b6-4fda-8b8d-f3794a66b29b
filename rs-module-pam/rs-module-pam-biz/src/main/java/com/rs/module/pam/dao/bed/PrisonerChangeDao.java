package com.rs.module.pam.dao.bed;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.bed.vo.PrisonerChangePageReqVO;
import com.rs.module.pam.entity.bed.PrisonerChangeDO;
import org.apache.ibatis.annotations.Mapper;

/**
* 监所事务管理-人员床位调整 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PrisonerChangeDao extends IBaseDao<PrisonerChangeDO> {


    default PageResult<PrisonerChangeDO> selectPage(PrisonerChangePageReqVO reqVO) {
        Page<PrisonerChangeDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrisonerChangeDO> wrapper = new LambdaQueryWrapperX<PrisonerChangeDO>()
                .eq(PrisonerChangeDO::getOrgCode, reqVO.getOrgCode())
                .eq(PrisonerChangeDO::getRoomId, reqVO.getRoomId())
                .betweenIfPresent(PrisonerChangeDO::getAddTime, reqVO.getAddTime());
        if(reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        }else {
            wrapper.orderByDesc(PrisonerChangeDO::getAddTime);
        }
        Page<PrisonerChangeDO> changePage = selectPage(page, wrapper);
        return new PageResult<>(changePage.getRecords(), changePage.getTotal());
    }

    default PrisonerChangeDO getByRoomId(String roomId) {
        return selectOne(new LambdaQueryWrapper<PrisonerChangeDO>().eq(PrisonerChangeDO::getRoomId, roomId));
    }

}
