package com.rs.module.pam.controller.admin.integral.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

@ApiModel(description = "积分模型配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ModelConfigSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("模型名称")
    private String name;

    @ApiModelProperty("风险模型id")
    @NotEmpty(message = "风险模型id不能为空")
    private String riskModelId;

    @ApiModelProperty("指标类型id")
    @NotEmpty(message = "指标类型id不能为空")
    private String indicatorTypeId;

    @ApiModelProperty("指标id")
    private String indicatorId;

    @ApiModelProperty("计分类型(1：加分，2：减分)")
    private String scoreType;

    @ApiModelProperty("分值类型(1：数值，2：百分比)")
    private String scoreForm;

    @ApiModelProperty("分值")
    private BigDecimal score;

    @ApiModelProperty("数据源ID")
    private String dbId;

    @ApiModelProperty("数据资源类型  （01：数据表  02：资源脚本）")
    private String resType;

    @ApiModelProperty("数据资源ID")
    private String resId;

    @ApiModelProperty("数据资源名称")
    private String resName;

    @ApiModelProperty("主键字段")
    private String keyCol;

    @ApiModelProperty("增量字段")
    private String incCol;

    @ApiModelProperty("增量默认值")
    private String incValueDefault;

    @ApiModelProperty("当前增量值")
    private String incValue;

    @ApiModelProperty("业务时间字段")
    private String businessDateCol;

    @ApiModelProperty("监所编号字段")
    private String orgCodeCol;

    @ApiModelProperty("监室编号字段")
    private String roomCodeCol;

    @ApiModelProperty("监管人员编号字段")
    private String prisonerCodeCol;

    @ApiModelProperty("条件SQL")
    private String conditionSql;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("状态（1：启用，0：停用）")
    private String status;

}
