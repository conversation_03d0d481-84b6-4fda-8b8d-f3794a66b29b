package com.rs.module.pam.handler;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.StringUtil;
import com.google.common.collect.Lists;
import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.SocketService;
import com.rs.module.base.dto.pm.TableDataChangeDTO;
import com.rs.module.base.service.dataChangeHandler.AbstractTableDataChangeService;
import com.rs.module.pam.dao.represent.RepresentConfigDao;
import com.rs.module.pam.dao.represent.RepresentDao;
import com.rs.module.pam.entity.represent.RepresentConfigDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 点名定时任务增量变动websocket推送
 *
 */
@Slf4j
@Component
public class RoomPresentConfigAsyHandler extends AbstractTableDataChangeService {

    @Resource
    private RepresentConfigDao representConfigDao;
    @Resource
    private RepresentDao representDao;
    @Resource
    private SocketService socketService;

    @Override
    public List<String> listenTableList() {
        return Lists.newArrayList("pam_represent_config");
    }

    @Override
    public void handler(TableDataChangeDTO data) {
        System.out.println("点名计划数据变动触发：" + data);
        if (StringUtil.isNotEmpty(data.getPkId())) {
            RepresentConfigDO configDO = representConfigDao.selectById(data.getPkId());
            String roomIds = configDO.getRoomId();
            String[] split = roomIds.split(",");
            for (String roomId : split) {
                String serialNumber = representDao.getSerialNumberByRoomId(roomId);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("configPeriodCode", Integer.valueOf(configDO.getConfigPeriodCode()));
                jsonObject.put("cronStr", configDO.getCron());
                jsonObject.put("expiryDate", configDO.getExpiryDate());
                jsonObject.put("id", configDO.getId());
                jsonObject.put("roomId", roomId);
                if(configDO.getStatus() == 1){
                    jsonObject.put("updateType", "delete");
                }else {
                    jsonObject.put("updateType", data.getUpdateType().toLowerCase());
                }
                PushMessageForm pushFrom = new PushMessageForm();
                ArrayList<String> serialNumbers = new ArrayList<>();
                serialNumbers.add(serialNumber);
                pushFrom.setSerialNumbers(serialNumbers);
                pushFrom.setTarget(SocketActionConstants.PushMessageTargetEnum.web.name());
                pushFrom.setTerminal(SocketActionConstants.PushMessageTerminalEnum.CNP.name());
                pushFrom.setAction(SocketActionConstants.representTask);
                Object[] objects = new Object[1];
                objects[0] = JSONObject.toJSONString(jsonObject);
                pushFrom.setParams(objects);
                socketService.pushMessageToSerialNumber(pushFrom);
            }
        }
    }
}
