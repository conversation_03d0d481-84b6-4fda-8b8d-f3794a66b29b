package com.rs.module.pam.controller.admin.duty;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.pam.controller.admin.duty.vo.*;
import com.rs.module.pam.entity.duty.RoomAutoConfigDO;
import com.rs.module.pam.service.duty.DutyService;
import com.rs.module.pam.service.duty.RoomAutoConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "值班管理-监室值班")
@RestController
@RequestMapping("/pam/duty/")
@Validated
public class DutyController {

    @Resource
    private DutyService dutyService;
    @Resource
    private RoomAutoConfigService roomAutoConfigService;


    @PostMapping("/create")
    @ApiOperation(value = "创建监室值班")
    @BusTrace(busType = BusTypeEnum.YEWU_JSZB, condition = "false", content = "{\"机构编号\":\"{{#createReqVO.orgCode}}\"," +
            "\"监室编号\":\"{{#createReqVO.roomId}}\",\"值班开始时间\":\"{{#createReqVO.startDate}}\",\"值班结束时间\":\"{{#createReqVO.endDate}}\"}")
    public CommonResult<String> create(@Valid @RequestBody DutySaveVO createReqVO) {
        dutyService.create(createReqVO, false);
        return success();
    }

    @GetMapping("/getAutoShiftConfig")
    @ApiOperation(value = "获取自动排班规则配置")
    @ApiImplicitParam(name = "orgCode", value = "机构编号")
    public CommonResult<RoomAutoConfigRespVO> getAutoShiftConfig(@RequestParam("orgCode") String orgCode,
                                                                 @RequestParam("roomId") String roomId) {
        RoomAutoConfigDO roomAutoConfig = roomAutoConfigService.getRoomAutoConfig(orgCode, roomId);
        return success(BeanUtils.toBean(roomAutoConfig, RoomAutoConfigRespVO.class));
    }

    @PostMapping("/updateAutoConfig")
    @ApiOperation(value = "更新监室自动排班配置")
    public CommonResult<Boolean> updateRoomAutoConfig(@Valid @RequestBody RoomAutoConfigSaveReqVO updateReqVO) {
        roomAutoConfigService.updateRoomAutoConfig(updateReqVO);
        return success(true);
    }

    @RequestMapping(value = "/dutyRecords", method = RequestMethod.GET)
    @ApiOperation(value = "获取排班记录")
    @BusTrace(busType = BusTypeEnum.YEWU_JSZB, condition = "false", content = "{\"监室编号\":\"{{#roomId}}\"," +
            "\"值班开始时间\":\"{{#startDate}}\",\"值班结束时间\":\"{{#endDate}}\"}")
    public CommonResult<List<DutyVO>> dutyRecords(@RequestParam("orgCode") String orgCode,
                                                  @RequestParam("roomId") String roomId,
                                                  @RequestParam("startDate") Date startDate,
                                                  @RequestParam("endDate") Date endDate) {
        List<DutyVO> records = dutyService.dutyRecords(orgCode, roomId, startDate, endDate);
        return success(records);
    }

    @RequestMapping(value = "/prisonerList", method = RequestMethod.GET)
    @ApiOperation(value = "监室人员列表")
    @BusTrace(busType = BusTypeEnum.YEWU_JSZB, condition = "false", content = "{\"监室编号\":\"{{#roomId}}\"")
    public CommonResult prisonerList(@RequestParam("orgCode") String orgCode,
                                     @RequestParam("roomId") String roomId) {
        List<DutyPrisonerVO> list = dutyService.prisonerList(orgCode, roomId);
        return success(list);
    }

    @RequestMapping(value = "/autoShift", method = RequestMethod.GET)
    @ApiOperation(value = "自动排班")
    @BusTrace(busType = BusTypeEnum.YEWU_JSZB, condition = "false", content = "{\"机构编号\":\"{{#orgCode}}\"," +
            "\"监室编号\":\"{{#roomId}}\",\"值班开始时间\":\"{{#startDate}}\",\"值班结束时间\":\"{{#endDate}}\"}")
    public CommonResult autoShift(@RequestParam("orgCode") String orgCode,
                          @RequestParam("roomId") String roomId,
                          @RequestParam("startDate") Date startDate,
                          @RequestParam("endDate") Date endDate) {
        Date now = new Date();
        // 今天开始时间
        Date today = new Date(now.getYear(), now.getMonth(), now.getDate());
        if (today.compareTo(startDate) > 0) {
            // 排班只允许排今天及以后的
            startDate = today;
        }
        if (startDate.compareTo(endDate) > 0) {
            throw new RuntimeException("无效的查询时间");
        }
        DutyVO dutyVO = dutyService.autoShift(orgCode, roomId, startDate, endDate);
        return success(dutyVO);
    }

    @RequestMapping(value = "/getSignRecord", method = RequestMethod.GET)
    @ApiOperation(value = "获取值班签到记录")
    @BusTrace(busType = BusTypeEnum.YEWU_JSZB, condition = "false", content = "{\"机构编号\":\"{{#orgCode}}\"," +
            "\"监室编号\":\"{{#roomId}}\",\"值班开始时间\":\"{{#startDate}}\",\"值班结束时间\":\"{{#endDate}}\"}")
    public CommonResult getSignRecord(@RequestParam("orgCode") String orgCode,
                                  @RequestParam("roomId") String roomId,
                                  @RequestParam("dutyDate") Date dutyDate) {
        DutyVO signRecord = dutyService.getSignRecord(orgCode, roomId, dutyDate);
        return success(signRecord);
    }


}
