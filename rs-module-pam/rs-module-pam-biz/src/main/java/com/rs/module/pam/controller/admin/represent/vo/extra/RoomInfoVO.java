package com.rs.module.pam.controller.admin.represent.vo.extra;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@ApiModel("获取监室信息vo")
@EqualsAndHashCode(callSuper=false)
public class RoomInfoVO extends BaseVO {

    @ApiModelProperty(value = "机构编号")
    private String orgCode;

    @ApiModelProperty(value = "机构名称")
    private String orgName;

    @ApiModelProperty(value = "机构编号")
    private String prisonId;

    @ApiModelProperty(value = "监室id")
    private String roomId;

    @ApiModelProperty(value = "监室名称")
    private String roomName;

    @ApiModelProperty(value = "监区id")
    private String areaId;

    @ApiModelProperty(value = "监区名称")
    private String areaName;

    @ApiModelProperty(value = "监室总关押数")
    private Integer allInNum;

    @ApiModelProperty(value = "监室在押数")
    private Integer inNum;

    @ApiModelProperty(value = "监室外出数")
    private Integer outNum;

    @ApiModelProperty("监室报备数量")
    private Integer reportNum;

    @ApiModelProperty(value = "监室性别")
    private String roomSex;

    @ApiModelProperty(value = "点名状态(1：点名中，3：异常待处置")
    private Integer isInRepresent;

}
