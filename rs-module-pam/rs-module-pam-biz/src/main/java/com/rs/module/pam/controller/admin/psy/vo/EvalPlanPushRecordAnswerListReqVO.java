package com.rs.module.pam.controller.admin.psy.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评计划推送记录答案列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EvalPlanPushRecordAnswerListReqVO extends BaseVO {
    @ApiModelProperty("测评编号")
    private String evalNo;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("开始时间")
    private Date[] startTime;

    @ApiModelProperty("结束时间")
    private Date[] endTime;

    @ApiModelProperty("提交时间")
    private Date[] submitTime;

    @ApiModelProperty("参考答案，选择题答案保存选项代码，多选题多个答案逗号分割，简答题保存答案")
    private String submitAnswer;

    @ApiModelProperty("测评得分")
    private BigDecimal score;

    @ApiModelProperty("测评结果")
    private String evalResults;

    @ApiModelProperty("作答用时")
    private LocalTime timeSpentAnswer;

    @ApiModelProperty("提交状态")
    private String status;

}
