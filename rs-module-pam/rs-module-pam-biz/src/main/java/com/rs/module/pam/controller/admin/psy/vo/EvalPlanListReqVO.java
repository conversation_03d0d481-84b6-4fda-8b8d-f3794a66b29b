package com.rs.module.pam.controller.admin.psy.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评计划列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EvalPlanListReqVO extends BaseVO {

    @ApiModelProperty("计划名称")
    private String planName;

    @ApiModelProperty("计划编码")
    private String planCode;

    @ApiModelProperty("计划类型")
    private String planType;

    @ApiModelProperty("计划说明")
    private String remark;

    @ApiModelProperty("量表ID")
    private String tableId;

    @ApiModelProperty("量表名称")
    private String tableName;

    @ApiModelProperty("触发类型")
    private String triggerType;

    @ApiModelProperty("测评次数")
    private Integer evalNumber;

    @ApiModelProperty("测评状态（字典：ZD_PSY_CPZT）")
    private String status;

    @ApiModelProperty("启用状态")
    private String enableStatus;

    @ApiModelProperty("监所编码")
    private String orgCode;


}
