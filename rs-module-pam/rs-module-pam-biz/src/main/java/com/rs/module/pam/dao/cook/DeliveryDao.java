package com.rs.module.pam.dao.cook;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.pam.entity.cook.DeliveryDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 监所事务管理-发饭主 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface DeliveryDao extends IBaseDao<DeliveryDO> {

    List<Map<String, String>> selectBusinessTypeCount(@Param("roomId") String roomId);

    /**
     * 根据唯一标识查询发饭
     *
     * @return
     */
    DeliveryDO selectDeliveryByUnniQue(@Param("cookBookDate") String cookBookDate, @Param("roomId") String roomId,
                                   @Param("orgCode") String orgCode, @Param("mealPeriod") String mealPeriod);

    /**
     * 更新发饭统计信息
     * @param deliveryId
     */
    void updateDeliveryStatistics(String deliveryId);
}
