package com.rs.module.pam.controller.admin.cook;

import cn.hutool.core.util.StrUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.cook.vo.ManageListReqVO;
import com.rs.module.pam.controller.admin.cook.vo.ManageRespVO;
import com.rs.module.pam.controller.admin.cook.vo.ManageSaveReqVO;
import com.rs.module.pam.entity.cook.ManageDO;
import com.rs.module.pam.service.cook.ManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监室配餐-菜品管理")
@RestController
@RequestMapping("/pam/cook/manage")
@Validated
public class ManageController {

    @Resource
    private ManageService manageService;

    @PostMapping("/create")
    @ApiOperation(value = "创建-菜品")
    public CommonResult<String> createManage(@Valid @RequestBody ManageSaveReqVO createReqVO) {
        return success(manageService.createManage(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新-菜品")
    public CommonResult<Boolean> updateManage(@Valid @RequestBody ManageSaveReqVO updateReqVO) {
        manageService.updateManage(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除-菜品")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteManage(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           manageService.deleteManage(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得-菜品")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ManageRespVO> getManage(@RequestParam("id") String id) {
        ManageDO manage = manageService.getManage(id);
        return success(BeanUtils.toBean(manage, ManageRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得-菜品列表")
    public CommonResult<List<ManageRespVO>> getManageList(@Valid @RequestBody ManageListReqVO listReqVO) {
        if (StrUtil.isBlank(listReqVO.getOrgCode())) {
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        List<ManageDO> list = manageService.getManageList(listReqVO);
        return success(BeanUtils.toBean(list, ManageRespVO.class));
    }
}
