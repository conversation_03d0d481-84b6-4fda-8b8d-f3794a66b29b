package com.rs.module.pam.controller.admin.library.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-图书借阅配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BorrowingConfigPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("个人最大借阅数")
    private Integer maximumBorrowingLimit;

    @ApiModelProperty("个人最大借阅时长（天）")
    private Integer maximumBorrowingDuration;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
