package com.rs.module.pam.controller.admin.info.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-信息报备分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ReportPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("数据来源（字典：ZD_DATA_SOURCES）")
    private String dataSources;

    @ApiModelProperty("报备人")
    private String reporterId;

    @ApiModelProperty("报备人姓名")
    private String reporterName;

    @ApiModelProperty("报备时间")
    private Date[] reportTime;

    @ApiModelProperty("监室号ID")
    private String roomId;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("对象ID")
    private String objectId;

    @ApiModelProperty("报备对象名称")
    private String objectName;

    @ApiModelProperty("报备类别")
    private String reportType;

    @ApiModelProperty("报备事由")
    private String reportContent;

    @ApiModelProperty("报备时间类型（字典：ZD_XXBB_BBSJLX）")
    private String reportTimeType;

    @ApiModelProperty("报备开始时间")
    private Date[] reportStartTime;

    @ApiModelProperty("报备结束时间")
    private Date[] reportEndTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否参与点名")
    private Short isRollcall;

    @ApiModelProperty("报备状态（字典：ZD_XXBB_BBZT）")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date[] approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
