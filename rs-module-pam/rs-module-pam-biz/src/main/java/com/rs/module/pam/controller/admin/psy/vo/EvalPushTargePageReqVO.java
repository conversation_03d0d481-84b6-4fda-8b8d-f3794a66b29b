package com.rs.module.pam.controller.admin.psy.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date 2025/6/17 16:29
 */
@ApiModel(description = "管理后台 - 监所事务管理-心理测评计划推送人员 Request VO")
@Data
public class EvalPushTargePageReqVO {

    @ApiModelProperty(value = "计划ID", required = true)
    @NotBlank(message = "计划ID不能为空")
    private String planId;
    @ApiModelProperty(value = "推送人员名称")
    private String xm;
    @ApiModelProperty(value = "监室号")
    private String areaName;
    @ApiModelProperty(value = "填写状态：ZD_PSY_TXZT 字典")
    private String fillingStatus;

}
