package com.rs.module.pam.service.cook;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.cook.vo.ManageListReqVO;
import com.rs.module.pam.controller.admin.cook.vo.ManageSaveReqVO;
import com.rs.module.pam.dao.cook.ManageDao;
import com.rs.module.pam.entity.cook.CateDO;
import com.rs.module.pam.entity.cook.ManageDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 监所事务管理-菜品管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ManageServiceImpl extends BaseServiceImpl<ManageDao, ManageDO> implements ManageService {

    @Resource
    private ManageDao manageDao;

    @Resource
    private CateService cateService;

    @Override
    public String createManage(ManageSaveReqVO createReqVO) {
        // 校验重复
        validateManageExists(createReqVO);
        // 插入
        ManageDO manage = BeanUtils.toBean(createReqVO, ManageDO.class);
        manageDao.insert(manage);
        // 返回
        return manage.getId();
    }

    @Override
    public void updateManage(ManageSaveReqVO updateReqVO) {
        // 校验存在
        validateManageExists(updateReqVO);
        // 更新
        ManageDO updateObj = BeanUtils.toBean(updateReqVO, ManageDO.class);
        manageDao.updateById(updateObj);
    }

    private void validateManageExists(ManageSaveReqVO reqVO) {
        // 校验重复
        LambdaQueryWrapper<ManageDO> lambdaQuery = Wrappers.lambdaQuery(ManageDO.class);
        lambdaQuery.select(ManageDO::getId).eq(ManageDO::getCookName, reqVO.getCookName())
                .eq(ManageDO::getOrgCode, SessionUserUtil.getSessionUser().getOrgCode());
        ManageDO manageDO = manageDao.selectOne(lambdaQuery);
        if (manageDO != null && !manageDO.getId().equals(reqVO.getId())) {
            throw new ServerException("菜品已存在");
        }
    }

    @Override
    public void deleteManage(String id) {
        // 删除
        manageDao.deleteById(id);
    }

    private void validateManageExists(String id) {
        if (manageDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-菜品管理数据不存在");
        }
    }

    @Override
    public ManageDO getManage(String id) {
        return manageDao.selectById(id);
    }

    @Override
    public List<ManageDO> getManageList(ManageListReqVO listReqVO) {
        List<ManageDO> manageDOS = manageDao.selectList(listReqVO);
        List<String> collect = manageDOS.stream().map(ManageDO::getCateId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(collect)) {
            Wrapper<CateDO> wrapper = Wrappers.lambdaQuery(CateDO.class).in(CateDO::getId, collect);
            List<CateDO> list = cateService.list(wrapper);
            Map<String, CateDO> map = list.stream().collect(Collectors.toMap(CateDO::getId, Function.identity(), (k1, k2) -> k2));
            for (ManageDO manageDO : manageDOS) {
                manageDO.setCateInfo(map.get(manageDO.getCateId()));
            }
        }
        return manageDOS;
    }


}
