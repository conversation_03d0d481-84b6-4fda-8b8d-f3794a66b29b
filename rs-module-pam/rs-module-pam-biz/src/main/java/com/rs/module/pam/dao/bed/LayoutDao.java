package com.rs.module.pam.dao.bed;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.bed.vo.LayoutListReqVO;
import com.rs.module.pam.controller.admin.bed.vo.LayoutPageReqVO;
import com.rs.module.pam.entity.bed.LayoutDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 监所事务管理-监室床位布局 Dao
*
* <AUTHOR>
*/
@Mapper
public interface LayoutDao extends IBaseDao<LayoutDO> {


    default PageResult<LayoutDO> selectPage(LayoutPageReqVO reqVO) {
        Page<LayoutDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<LayoutDO> wrapper = new LambdaQueryWrapperX<LayoutDO>()
            .likeIfPresent(LayoutDO::getLayoutName, reqVO.getLayoutName())
            .eqIfPresent(LayoutDO::getLayoutImageUrl, reqVO.getLayoutImageUrl())
            .eqIfPresent(LayoutDO::getLayoutConfig, reqVO.getLayoutConfig())
            .eqIfPresent(LayoutDO::getSort, reqVO.getSort());
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(LayoutDO::getAddTime);
        }
        Page<LayoutDO> layoutPage = selectPage(page, wrapper);
        return new PageResult<>(layoutPage.getRecords(), layoutPage.getTotal());
    }
    default List<LayoutDO> selectList(LayoutListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<LayoutDO>()
            .likeIfPresent(LayoutDO::getLayoutName, reqVO.getLayoutName())
            .eqIfPresent(LayoutDO::getLayoutImageUrl, reqVO.getLayoutImageUrl())
            .eqIfPresent(LayoutDO::getLayoutConfig, reqVO.getLayoutConfig())
            .eqIfPresent(LayoutDO::getSort, reqVO.getSort())
        .orderByDesc(LayoutDO::getAddTime));    }


    }
