package com.rs.module.pam.controller.app;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.enums.DataSourceAppEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.pam.controller.admin.info.vo.ReportItemListReqVO;
import com.rs.module.pam.controller.admin.info.vo.ReportItemSaveReqVO;
import com.rs.module.pam.controller.admin.info.vo.ReportItemTreeRespVO;
import com.rs.module.pam.controller.admin.info.vo.ReportSaveReqVO;
import com.rs.module.pam.controller.app.vo.AppReportListSowRespVO;
import com.rs.module.pam.controller.app.vo.AppReportSaveReqVO;
import com.rs.module.pam.controller.app.vo.AppReportSaveSowReqVO;
import com.rs.module.pam.entity.info.ReportDO;
import com.rs.module.pam.entity.info.ReportItemDO;
import com.rs.module.pam.enums.ReportInfoStatusEnum;
import com.rs.module.pam.service.info.ReportItemService;
import com.rs.module.pam.service.info.ReportService;
import com.rs.module.pam.util.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "信息报备-仓内外屏")
@RestController
@RequestMapping("/app/pam/info/report")
@Validated
public class AppReportController {

    @Resource
    private ReportService reportService;
    @Resource
    private PrisonerService prisonerService;
    @Resource
    private ReportItemService reportItemService;

    @PostMapping("/create")
    @ApiOperation(value = "创建内屏-信息报备")
    public CommonResult<String> createReport(@Valid @RequestBody AppReportSaveReqVO appReportSaveReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        if (sessionUser == null) {
            throw new ServerException("未登录");
        }
        PrisonerVwRespVO prisoner = prisonerService.getPrisonerByJgrybm(sessionUser.getLoginId());
        if (prisoner == null) {
            throw new ServerException("找不到当前登录人信息");
        }
        ReportSaveReqVO createReqVO = new ReportSaveReqVO();
        BeanUtils.copyProperties(appReportSaveReqVO, createReqVO);
        createReqVO.setDataSources(DataSourceAppEnum.CNP.getCode());
        createReqVO.setReporterId(prisoner.getJgrybm());
        createReqVO.setReporterName(prisoner.getXm());
        createReqVO.setObjectId(prisoner.getJgrybm());
        createReqVO.setObjectName(prisoner.getXm());
        createReqVO.setRoomId(prisoner.getJsh());
        createReqVO.setRoomName(prisoner.getRoomName());
        createReqVO.setApproverXm(prisoner.getZgmjName());
        createReqVO.setApproverSfzh(prisoner.getZgmjSfzh());
        createReqVO.setStatus(ReportInfoStatusEnum.DSP.getCode());
        List<ReportItemSaveReqVO> items = appReportSaveReqVO.getItems();
        if (CollUtil.isEmpty(items)) {
            throw new ServerException("请选择报备事项");
        }
        createReqVO.setItems(items);
        return success(reportService.createReport(createReqVO));
    }

    @PostMapping("/sow/create")
    @ApiOperation(value = "创建外屏-特殊情况报备")
    public CommonResult<String> createReportSow(@Valid @RequestBody AppReportSaveSowReqVO appReportSaveSowReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        if (sessionUser == null) {
            throw new ServerException("未登录");
        }
        ReportSaveReqVO createReqVO = new ReportSaveReqVO();
        BeanUtils.copyProperties(appReportSaveSowReqVO, createReqVO);
        createReqVO.setDataSources(DataSourceAppEnum.CWP.getCode());
        createReqVO.setReporterId(sessionUser.getIdCard());
        createReqVO.setReporterName(sessionUser.getName());
        createReqVO.setStatus(ReportInfoStatusEnum.DSP.getCode());
        List<ReportItemSaveReqVO> items = appReportSaveSowReqVO.getItems();
        if (CollUtil.isEmpty(items)) {
            throw new ServerException("请选择报备事项");
        }
        createReqVO.setItems(items);
        return success(reportService.createReport(createReqVO));
    }

    @PostMapping("/sow/list")
    @ApiOperation(value = "获得-外屏-特殊情况报备记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "timeType", value = "时间段：1 全部，2 今天，3 昨天，4 近一周", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "roomId", value = "监室编号", dataType = "String", paramType = "query", required = true)
    })
    public CommonResult<List<AppReportListSowRespVO>> createReportSow(@RequestParam(value = "timeType") String timeType,
                                                                      @RequestParam(value = "roomId") String roomId) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        Map<String, Date> commonAppRecordPeriod = CommonUtils.getCommonAppRecordPeriod(timeType);
        LambdaQueryWrapper<ReportDO> wrapper = new LambdaQueryWrapper<ReportDO>();
        wrapper.eq(ReportDO::getDataSources, DataSourceAppEnum.CWP.getCode())
                .eq(ReportDO::getAddUser, sessionUser.getIdCard())
                .eq(ReportDO::getRoomId, roomId)
                .between(commonAppRecordPeriod.get("startTime") != null,
                        ReportDO::getReportTime, commonAppRecordPeriod.get("startTime"), commonAppRecordPeriod.get("endTime"));
        List<ReportDO> list = reportService.list(wrapper);
        if (CollUtil.isEmpty(list)) {
            return success(new LinkedList<>());
        }
        Set<String> jgrybms = list.stream().map(ReportDO::getObjectId).collect(Collectors.toSet());
        List<PrisonerInDO> prisonerList = prisonerService.getPrisonerInOneList(jgrybms);
        if (CollUtil.isEmpty(prisonerList)) {
            return success(new LinkedList<>());
        }
        Map<String, PrisonerInDO> prisonerMap = prisonerList.stream().collect(Collectors.toMap(PrisonerInDO::getJgrybm, item -> item));
        List<AppReportListSowRespVO> appReportListSowRespVOList = new LinkedList<>();
        for (ReportDO reportDO : list) {
            AppReportListSowRespVO appReportListSowRespVO = new AppReportListSowRespVO();
            BeanUtils.copyProperties(reportDO, appReportListSowRespVO);
            PrisonerInDO prisonerInDO = prisonerMap.get(reportDO.getObjectId());
            if (prisonerInDO != null) {
                appReportListSowRespVO.setXb(prisonerInDO.getXb());
                if (prisonerInDO.getCsrq() != null) {
                    //计算年龄
                    try {
                        appReportListSowRespVO.setAge(String.valueOf(new Date().getYear() - prisonerInDO.getCsrq().getYear()));
                    } catch (Exception e) {
                       e.printStackTrace();
                    }
                }
                appReportListSowRespVO.setXm(prisonerInDO.getXm());
            }
            appReportListSowRespVOList.add(appReportListSowRespVO);
        }
        return success(appReportListSowRespVOList);
    }

    @PostMapping("/listTree")
    @ApiOperation(value = "获得-信息报备事项配置树形结构")
    public CommonResult<List<ReportItemTreeRespVO>> getReportItemListTree() {
        List<ReportItemTreeRespVO> treeList = new LinkedList<>();
        ReportItemListReqVO reqVO = new ReportItemListReqVO();
        reqVO.setStatus("1");
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        if (sessionUser == null) {
            throw new ServerException("未登录");
        }
        reqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        List<ReportItemDO> list = reportItemService.getReportItemList(reqVO);
        if (CollUtil.isNotEmpty(list)) {
            Map<String, List<ReportItemDO>> listMap = list.stream()
                    .collect(Collectors.groupingBy(ReportItemDO::getReportType));
            listMap.forEach((key, value) -> {
                ReportItemTreeRespVO tree = new ReportItemTreeRespVO();
                tree.setReportType(key);
                tree.setChildren(BeanUtils.toBean(value, ReportItemTreeRespVO.class));
                treeList.add(tree);
            });
        }
        return success(treeList);
    }


}
