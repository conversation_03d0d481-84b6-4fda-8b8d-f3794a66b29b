package com.rs.module.pam.dao.integral;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.pam.entity.integral.IndicatorDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 监所事务管理-积分指标 Dao
*
* <AUTHOR>
*/
@Mapper
public interface IndicatorDao extends IBaseDao<IndicatorDO> {

    default List<IndicatorDO> getIndicatorByOrgCode(String orgCode) {
        return selectList(new LambdaQueryWrapper<IndicatorDO>()
                .eq(IndicatorDO::getOrgCode, orgCode)
                .orderByAsc(IndicatorDO::getOrderId));
    }

    default List<IndicatorDO> getIndicatorTypeByRiskModel(String riskModelId) {
        return selectList(new LambdaQueryWrapper<IndicatorDO>()
                .eq(IndicatorDO::getRiskModelId, riskModelId)
                .and(i -> i.eq(IndicatorDO::getParentId, "").or().isNull(IndicatorDO::getParentId))
                .orderByAsc(IndicatorDO::getOrderId));
    }

}
