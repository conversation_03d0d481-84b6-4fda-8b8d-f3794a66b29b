package com.rs.module.pam.controller.admin.psy.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.module.pam.dto.QuestionDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评量表预览 Response VO")
@Data
public class EvalTablePrewSimpleRespVO implements TransPojo {

    @ApiModelProperty("题目ID")
    private String id;
    @ApiModelProperty("量表ID")
    private String tableId;
    @ApiModelProperty("题目内容")
    private String questionText;
    @ApiModelProperty("题目类型（字典：ZD_XLPC_TMLX）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XLPC_TMLX")
    private String questionType;
    @ApiModelProperty("单题计分规则")
    @Trans(type = TransType.DICTIONARY, key = "ZD_DTJFGZ")
    private String scoreRule;
    @ApiModelProperty("参考答案，选择题答案保存选项代码，多选题多个答案逗号分割，简答题保存答案")
    private String answer;
    @ApiModelProperty("题目分值")
    private BigDecimal score;
    @ApiModelProperty("题目顺序")
    private Integer sortOrder;
    @ApiModelProperty(value = "每种类型题总数")
    private int totalType;
    @ApiModelProperty(value = "总题数")
    private int total;
    @ApiModelProperty("题号")
    private int th;
    @ApiModelProperty("题目选项")
    private List<EvalTableOptionRespVO> options;

}
