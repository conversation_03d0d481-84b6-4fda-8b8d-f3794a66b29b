package com.rs.module.pam.controller.admin.cook.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-发饭记录新增/修改 Request VO")
@Data
public class DeliveryRecordSaveReqVO {
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("菜谱名称")
    @NotNull(message = "菜谱名称不能为空")
    private Date cookbookDate;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("监室ID")
    @NotEmpty(message = "监室ID不能为空")
    private String roomId;

    @ApiModelProperty("监室名称")
    @NotEmpty(message = "监室名称不能为空")
    private String roomName;

    @ApiModelProperty("配餐类型（字典：ZD_PCGL_PCLX ）")
    @NotEmpty(message = "配餐类型（字典：ZD_PCGL_PCLX ）不能为空")
    private String mealType;

    @ApiModelProperty("当日食谱")
    @NotEmpty(message = "当日食谱不能为空")
    private String cookbook;

    @ApiModelProperty("发饭时间")
    @NotNull(message = "发饭时间不能为空")
    private Date deliveryTime;

    @ApiModelProperty("经办民警身份证号")
    @NotEmpty(message = "经办民警身份证号不能为空")
    private String operatePoliceSfzh;

    @ApiModelProperty("经办民警")
    @NotEmpty(message = "经办民警不能为空")
    private String operatePolice;

    @ApiModelProperty("经办时间")
    @NotNull(message = "经办时间不能为空")
    private Date operateTime;

    @ApiModelProperty("状态")
    @NotEmpty(message = "状态不能为空")
    private String status;

}
