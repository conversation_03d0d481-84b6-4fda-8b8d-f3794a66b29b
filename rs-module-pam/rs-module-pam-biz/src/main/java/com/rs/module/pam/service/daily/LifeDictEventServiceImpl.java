package com.rs.module.pam.service.daily;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.pam.controller.admin.daily.vo.*;
import com.rs.module.pam.entity.daily.LifeDictEventDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.pam.dao.daily.LifeDictEventDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 监所事务管理-事务选项字典 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LifeDictEventServiceImpl extends BaseServiceImpl<LifeDictEventDao, LifeDictEventDO> implements LifeDictEventService {

    @Resource
    private LifeDictEventDao lifeDictEventDao;

    @Override
    public String createLifeDictEvent(LifeDictEventSaveReqVO createReqVO) {
        // 插入
        LifeDictEventDO lifeDictEvent = BeanUtils.toBean(createReqVO, LifeDictEventDO.class);
        lifeDictEventDao.insert(lifeDictEvent);
        // 返回
        return lifeDictEvent.getId();
    }

    @Override
    public void updateLifeDictEvent(LifeDictEventSaveReqVO updateReqVO) {
        // 校验存在
        validateLifeDictEventExists(updateReqVO.getId());
        // 更新
        LifeDictEventDO updateObj = BeanUtils.toBean(updateReqVO, LifeDictEventDO.class);
        lifeDictEventDao.updateById(updateObj);
    }

    @Override
    public void deleteLifeDictEvent(String id) {
        // 校验存在
        validateLifeDictEventExists(id);
        // 删除
        lifeDictEventDao.deleteById(id);
    }

    private void validateLifeDictEventExists(String id) {
        if (lifeDictEventDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-事务选项字典数据不存在");
        }
    }

    @Override
    public LifeDictEventDO getLifeDictEvent(String id) {
        return lifeDictEventDao.selectById(id);
    }

    @Override
    public PageResult<LifeDictEventDO> getLifeDictEventPage(LifeDictEventPageReqVO pageReqVO) {
        return lifeDictEventDao.selectPage(pageReqVO);
    }

    @Override
    public List<LifeDictEventDO> getLifeDictEventList(LifeDictEventListReqVO listReqVO) {
        return lifeDictEventDao.selectList(listReqVO);
    }


}
