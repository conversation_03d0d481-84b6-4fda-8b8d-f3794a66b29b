package com.rs.module.pam.service.duty;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.duty.vo.RecordsSigninSaveReqVO;
import com.rs.module.pam.entity.duty.RecordsSigninDO;
import org.apache.ibatis.annotations.Param;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 监所事务管理-监室值班记录关联签到 Service 接口
 *
 * <AUTHOR>
 */
public interface RecordsSigninService extends IBaseService<RecordsSigninDO>{

    /**
     * 创建监所事务管理-监室值班记录关联签到
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createRecordsSignin(@Valid RecordsSigninSaveReqVO createReqVO);

    /**
     * 获取当前排班签到记录
     * @param orgCode
     * @param roomId
     * @param recordsId
     * @return
     */
    List<RecordsSigninDO> getNowSigninByRecordsId(@Param("orgCode") String orgCode, @Param("roomId") String roomId,
                                              @Param("recordsId") String recordsId);

    /**
     * 获取当前排班签到记录
     * @param orgCode
     * @param roomId
     * @return
     */
    List<RecordsSigninDO> getNowRecordsSignin(@Param("orgCode") String orgCode, @Param("roomId") String roomId);


    void deleteRecordsSignin(@Param("orgCode") String orgCode, @Param("roomId") String roomId,
                             @Param("recordsId") String recordsId, @Param("date") Date date);

}
