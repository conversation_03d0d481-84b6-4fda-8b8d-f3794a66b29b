package com.rs.module.pam.entity.integral;

import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;
import com.rs.framework.mybatis.type.BooleanToIntegerTypeHandler;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 监所事务管理-积分明细 DO
 *
 * <AUTHOR>
 */
@TableName("pam_integral_score_detail")
@KeySequence("pam_integral_score_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_integral_score_detail")
public class ScoreDetailDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @TableField(value = "IS_DEL", fill = FieldFill.INSERT, typeHandler = BooleanToIntegerTypeHandler.class)
    @TableLogic
    private Boolean isDel;
    /**
     * 机构编号
     */
    private String orgName;
    /**
     * 机构名称
     */
    private String orgCode;
    /**
     * 风险模型id
     */
    private String riskModelId;
    /**
     * 风险模型类型
     */
    private String riskModelType;
    /**
     * 指标类型id
     */
    private String indicatorTypeId;
    /**
     * 指标id
     */
    private String indicatorId;
    /**
     * 日期值
     */
    private String dateValue;
    /**
     * 监室编号
     */
    private String roomCode;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 被监管人员编号
     */
    private String prisonerCode;
    /**
     * 被监管人员姓名
     */
    private String prisonerName;
    /**
     * 业务数据ID
     */
    private String businessId;
    /**
     * 实际分值
     */
    private BigDecimal score;
    /**
     * 模型分值
     */
    private BigDecimal IndicatorScore;

}
