package com.rs.module.pam.controller.admin.cook.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-发饭主新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DeliverySaveReqVO extends BaseVO {
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监室ID")
    @NotEmpty(message = "监室ID不能为空")
    private String roomId;

    @ApiModelProperty("监室名称")
    @NotEmpty(message = "监室名称不能为空")
    private String roomName;

    @ApiModelProperty("菜谱名称")
    @NotNull(message = "菜谱名称不能为空")
    private Date cookbookDate;

    @ApiModelProperty("监室总人数")
    @NotNull(message = "监室总人数不能为空")
    private Integer roomTotalPersons;

    @ApiModelProperty("在监总人数")
    @NotNull(message = "在监总人数不能为空")
    private Integer custodyTotalPersons;

    @ApiModelProperty("外出总人数")
    @NotNull(message = "外出总人数不能为空")
    private Integer outgoingTotalPersons;

    @ApiModelProperty("已领饭人数")
    @NotNull(message = "已领饭人数不能为空")
    private Integer mealReceivedTotal;

    @ApiModelProperty("未领饭人数")
    @NotNull(message = "未领饭人数不能为空")
    private Integer mealUnreceivedTotal;

    @ApiModelProperty("留饭人数")
    @NotNull(message = "留饭人数不能为空")
    private Integer mealReservedTotal;

    @ApiModelProperty("配餐类型（字典：ZD_PCGL_PCLX ）")
    @NotEmpty(message = "配餐类型（字典：ZD_PCGL_PCLX ）不能为空")
    private String mealType;

    @ApiModelProperty("当日食谱")
    @NotEmpty(message = "当日食谱不能为空")
    private String cookbook;

    @ApiModelProperty("发饭时间")
    @NotNull(message = "发饭时间不能为空")
    private Date deliveryTime;

    @ApiModelProperty("经办民警身份证号")
    @NotEmpty(message = "经办民警身份证号不能为空")
    private String operatePoliceSfzh;

    @ApiModelProperty("经办民警")
    @NotEmpty(message = "经办民警不能为空")
    private String operatePolice;

    @ApiModelProperty("经办时间")
    @NotNull(message = "经办时间不能为空")
    private Date operateTime;

    @ApiModelProperty("状态")
    @NotEmpty(message = "状态不能为空")
    private String status;

}
