package com.rs.module.pam.dao.daily;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.entity.daily.LifeDO;
import com.rs.module.pam.entity.daily.LifeHolidaysDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.pam.controller.admin.daily.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 监所事务管理-一日生活制度节假日 Dao
*
* <AUTHOR>
*/
@Mapper
public interface LifeHolidaysDao extends IBaseDao<LifeHolidaysDO> {


    default PageResult<LifeHolidaysDO> selectPage(LifeHolidaysPageReqVO reqVO) {
        Page<LifeHolidaysDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<LifeHolidaysDO> wrapper = new LambdaQueryWrapperX<LifeHolidaysDO>()
            .betweenIfPresent(LifeHolidaysDO::getHolidaysDate, reqVO.getHolidaysDate())
            .likeIfPresent(LifeHolidaysDO::getHolidaysName, reqVO.getHolidaysName())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(LifeHolidaysDO::getAddTime);
        }
        Page<LifeHolidaysDO> lifeHolidaysPage = selectPage(page, wrapper);
        return new PageResult<>(lifeHolidaysPage.getRecords(), lifeHolidaysPage.getTotal());
    }
    default List<LifeHolidaysDO> selectList(LifeHolidaysListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<LifeHolidaysDO>()
            .betweenIfPresent(LifeHolidaysDO::getHolidaysDate, reqVO.getHolidaysDate())
            .likeIfPresent(LifeHolidaysDO::getHolidaysName, reqVO.getHolidaysName())
            .eqIfPresent(LifeHolidaysDO::getOrgCode, reqVO.getOrgCode())
            .eqIfPresent(LifeHolidaysDO::getRegCode, reqVO.getRegCode())
            .eqIfPresent(LifeHolidaysDO::getCityCode, reqVO.getCityCode())
        .orderByDesc(LifeHolidaysDO::getAddTime));    }


    }
