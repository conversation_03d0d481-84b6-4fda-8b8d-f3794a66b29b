package com.rs.module.pam.controller.admin.daily.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 监所事务管理-日常清监检查项配置列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CleanConfigListReqVO extends BaseVO {

    @ApiModelProperty("检查类型（字典：ZD_AQJCGL_JCLX）")
    private String checkType;

    @ApiModelProperty("监所编号")
    private String orgCode;

}
