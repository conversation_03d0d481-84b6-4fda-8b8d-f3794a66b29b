package com.rs.module.pam.schedule;


import com.alibaba.fastjson.JSON;
import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.SocketService;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.SocketRelationMapVO;
import com.rs.module.pam.controller.admin.represent.vo.extra.InitBaseDataConfigVO;
import com.rs.module.pam.controller.admin.represent.vo.extra.InitBaseDataVO;
import com.rs.module.pam.service.represent.RepresentService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 点名同步仓内屏定时数据接口
*/
@Component
@Slf4j
public class RoomPresentAsyTask {

    @Autowired
    private RepresentService presentService;
    @Autowired
    private SocketService socketService;


    private final static String TOPIC = "room_present_asy";

    /**
     * 数据同步到仓内屏数据库
     */
    @XxlJob("roomPresentAsyData")
    public List<InitBaseDataVO> asyData(){
        ArrayList<InitBaseDataVO> initBaseDataVOS = new ArrayList<>();
        SocketRelationMapVO socketInfo = socketService.getSocketInfo();
        List<String> list = new ArrayList<>(socketInfo.getSerialNumberMap().values());
        for(String serId:list){
            InitBaseDataVO initBaseDataVO = presentService.initBaseData(serId);
            //查找手动点名数据
            InitBaseDataConfigVO oneTask = presentService.getOneTask(initBaseDataVO.getRoomId());
            if(oneTask!=null){
                initBaseDataVO.getConfigVOS().add(oneTask);
            }
            initBaseDataVOS.add(initBaseDataVO);
            ArrayList<String> serialNumbers = new ArrayList<>();
            serialNumbers.add(serId);
            PushMessageForm pushFrom = new PushMessageForm();
            pushFrom.setSerialNumbers(serialNumbers);
            Object[] objects = new Object[1];
            objects[0]= JSON.toJSONString(initBaseDataVO);
            pushFrom.setParams(objects);
            pushFrom.setTarget(SocketActionConstants.PushMessageTargetEnum.web.name());
            pushFrom.setTerminal(SocketActionConstants.PushMessageTerminalEnum.CNP.name());
            pushFrom.setAction(TOPIC);
            socketService.pushMessageToSerialNumber(pushFrom);
        }
        return initBaseDataVOS;
    }

}
