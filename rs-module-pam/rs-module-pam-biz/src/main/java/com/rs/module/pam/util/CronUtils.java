package com.rs.module.pam.util;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import org.apache.commons.lang3.time.DateUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * cron表达式转换类
 *
 * <AUTHOR> on 2020/5/18
 * @email <EMAIL>
 * @date 2020/5/18
 */
public class CronUtils {

    //"ss mm HH dd MM ? yyyy"
    private static final SimpleDateFormat sdf = new SimpleDateFormat("mm HH");

    /***
     * convert Date to cron, eg "0 07 10 15 1 ?"
     * @return
     */
    public static String getCron(String hour, String minute) {
        return "0 " + minute + " " + hour + " * * ?";
    }

    /***
     * 按周执行
     * convert Date to cron, eg "0 15 10 ? * 1,2?"
     * @return
     */
    public static String getCronWeek(String hour, String minute, String week) {
        return "0 " + minute + " " + hour + " ? " + " * " + week;
    }

    /***
     * 执行一次
     * convert Date to cron, eg "0 15 10 ? * 1,2?"
     * @return
     */
    public static String getCronOne(String hour, String minute) {
        Calendar now = Calendar.getInstance();
        int year = now.get(Calendar.YEAR);
        int month = now.get(Calendar.MONTH) + 1;
        int day = now.get(Calendar.DAY_OF_MONTH);
        return "0 " + minute + " " + hour + " " + day + " " + month + " ?";
    }

    /**
     * 生成周期性的Cron表达式（带日期范围）
     *
     * @param startTime    开始时间（包含具体时间点）
     * @param endTime      结束日期（仅日期部分有效）
     * @param intervalDays 间隔天数（必须≥0）
     * @return 符合XXL-JOB规范的7位Cron表达式
     */
    public static String generateDailyCron(Date startTime, Date endTime, int intervalDays) {
        // 参数校验
        if (intervalDays < 0) {
            throw new IllegalArgumentException("间隔天数必须大于0");
        }
        if (intervalDays > 31) {
            throw new IllegalArgumentException("间隔天数必须<=31");
        }
        if (startTime.after(endTime)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
        if (DateUtils.addDays(startTime, intervalDays).after(endTime)) {
            throw new IllegalArgumentException("间隔天数过长，结束时间不能早于开始时间+间隔天数");
        }
        // 解析开始时间的时分秒
        Calendar cal = Calendar.getInstance();
        cal.setTime(startTime);
        int second = cal.get(Calendar.SECOND);
        int minute = cal.get(Calendar.MINUTE);
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        int startDay = cal.get(Calendar.DAY_OF_MONTH);
        // 构造Cron表达式
        String dayField;
        if (intervalDays == 0) {
            dayField = "*";  // 每天执行
        } else {
            dayField = startDay + "/" + intervalDays;  // 间隔天数执行
        }
        return String.format("%d  %d %d %s * ?", second, minute, hour, dayField);
    }

    /**
     * 生成每周周期的Cron表达式
     *
     * @param weekDays  执行的星期（1-7对应周一到周日，支持多选）
     * @param startTime 开始时间（包含具体时分秒）
     * @param endTime   结束时间（仅日期有效）
     * @return 符合XXL-JOB规范的7位Cron表达式
     */
    public static String generateWeeklyCron(String weekDays, Date startTime, Date endTime) {
        // 参数校验
        if (weekDays == null || weekDays.trim().isEmpty()) {
            throw new IllegalArgumentException("星期参数不能为空");
        }
        if (startTime.after(endTime)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
        List<DateTime> dateTimes = DateUtil.rangeToList(startTime, endTime, DateField.DAY_OF_YEAR, 1);
        Set<String> setList = new HashSet<>();
        for (DateTime dateTime : dateTimes) {
            setList.add(dateTime.dayOfWeek() + "");
        }
        // 解析开始时间的时分秒
        Calendar cal = Calendar.getInstance();
        cal.setTime(startTime);
        int second = cal.get(Calendar.SECOND);
        int minute = cal.get(Calendar.MINUTE);
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        // 转换星期格式（1-7 → MON-SUN）
        String cronWeekDays = convertWeekDays(weekDays, setList);
        return String.format("%d  %d %d ? * %s",
                second, minute, hour, cronWeekDays);
    }

    /**
     * 生成每月固定日期执行的Cron表达式
     *
     * @param dayTime   每月的哪一天
     * @param startTime 开始时间（提供具体的执行时分秒）
     * @param endTime   结束时间（仅用于校验，不参与表达式生成，可为null）
     * @return 7位Cron表达式
     */
    public static String generateMonthlyCron(int dayTime, Date startTime, Date endTime) {
        if (dayTime <= 0 || dayTime > 28) {
            throw new IllegalArgumentException("每月的哪一天必须在1-28之间");
        }
        // 校验时间范围
        if (endTime != null && startTime.after(endTime)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
        // 从开始时间中获取时分秒
        Calendar cal = Calendar.getInstance();
        cal.setTime(startTime);
        int second = cal.get(Calendar.SECOND);
        int minute = cal.get(Calendar.MINUTE);
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        int dayOfMonth = cal.get(Calendar.DAY_OF_MONTH);
        int dayOfMonthEndTime = DateUtil.dayOfMonth(endTime);
        if (dayTime < dayOfMonth || dayOfMonthEndTime < dayTime) {
            throw new IllegalArgumentException("开始时间和结束时间不在月份天数范围内");
        }
        // 构建表达式：秒 分 时 日 月 星期（用?） 年（省略，XXL-JOB用7位）
        return String.format("%d  %d %d %d * ?", second, minute, hour, dayTime);
    }

    public static String generateYearCron(Date dayTime, Date startTime, Date endTime) {
        // 校验日期
        if (dayTime == null) {
            throw new IllegalArgumentException("每年的哪一天不能为空");
        }
        // 校验时间范围
        if (endTime != null && startTime.after(endTime)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
        if (dayTime.after(endTime)) {
            throw new IllegalArgumentException("每年的某一天不能晚于结束时间");
        }
        if (dayTime.before(startTime)) {
            throw new IllegalArgumentException("每年的某一天不能早于开始时间");
        }
        // 从开始时间中获取时分秒
        Calendar cal = Calendar.getInstance();
        cal.setTime(dayTime);
        int minute = cal.get(Calendar.MINUTE);
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        int dayOfMonth = cal.get(Calendar.DAY_OF_MONTH);
        int month = cal.get(Calendar.MONTH);
        return yearlyOn(month, dayOfMonth, hour, minute);
    }

    /**
     * 生成每年执行的Cron表达式
     *
     * @return 7位Cron表达式（兼容XXL-JOB）
     */
    private static String yearlyOn(int month, int dayOfMonth, int hour, int minute) {
        // 参数校验
        if (month < 1 || month > 12) {
            throw new IllegalArgumentException("月份必须在1-12之间");
        }
        if (dayOfMonth < 1 || dayOfMonth > 31) {
            throw new IllegalArgumentException("日期必须在1-31之间");
        }
        // 校验时间范围
        if (hour < 0 || hour > 23 || minute < 0 || minute > 59) {
            throw new IllegalArgumentException("时间格式无效，应为HH:mm");
        }

        // 生成表达式：秒 分 时 日 月 周 年
        return String.format("0  %d %d %d %d ? *",
                minute, hour, dayOfMonth, month);
        // 注意：结束时间不在Cron中体现，由XXL-JOB独立控制
    }

    /**
     * 转换星期格式（1-7 → MON-SUN）
     *
     * @param weekDays 数字格式的星期（如"1,3,5"）
     */
    private static String convertWeekDays(String weekDays, Set<String> set) {
        Map<String, String> weekMap = new HashMap<>();
        weekMap.put("1", "MON");
        weekMap.put("2", "TUE");
        weekMap.put("3", "WED");
        weekMap.put("4", "THU");
        weekMap.put("5", "FRI");
        weekMap.put("6", "SAT");
        weekMap.put("7", "SUN");
        String[] days = weekDays.split(",");
        List<String> convertedDays = new ArrayList<>();
        for (String day : days) {
            if (!weekMap.containsKey(day.trim())) {
                throw new IllegalArgumentException("无效的星期参数: " + day);
            }
            if (!set.contains(day.trim())) {
                throw new IllegalArgumentException("开始时间和结束时间不在星期范围内: " + day);
            }
            convertedDays.add(weekMap.get(day.trim()));
        }
        return String.join(",", convertedDays);
    }

    /**
     * 生成一次性执行的Cron表达式
     *
     * @param executeTime 具体执行时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return 7位Cron表达式（适配XXL-JOB）
     */
    public static String generateOneTimeCron(Date executeTime) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(executeTime);

        // 提取时间组件
        int second = cal.get(Calendar.SECOND);
        int minute = cal.get(Calendar.MINUTE);
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        int day = cal.get(Calendar.DAY_OF_MONTH);
        int month = cal.get(Calendar.MONTH) + 1; // 月份+1校准
        int year = cal.get(Calendar.YEAR);
        int minuteAffer = minute + 5;

        return String.format("%d  %d,%d %d %d %d ? %d", second, minute, minuteAffer, hour, day, month, year);
    }


}