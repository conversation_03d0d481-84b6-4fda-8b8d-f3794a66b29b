package com.rs.module.pam.controller.admin.psy;

import cn.hutool.core.util.StrUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableListReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTablePageReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTablePrewCommonAnswerRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTablePrewCommonRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTablePrewSimpleAnswerRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTablePrewSimpleRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableSaveReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableSubmitReqVO;
import com.rs.module.pam.entity.psy.EvalTableDO;
import com.rs.module.pam.service.psy.EvalTableService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "心理测评-心理测评量表管理")
@RestController
@RequestMapping("/pam/psy/evalTable")
@Validated
public class EvalTableController {

    @Resource
    private EvalTableService evalTableService;

    @PostMapping("/create")
    @ApiOperation(value = "创建-心理测评量表管理")
    public CommonResult<String> createEvalTable(@Valid @RequestBody EvalTableSaveReqVO createReqVO) {
        return success(evalTableService.createEvalTable(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新-心理测评量表管理")
    public CommonResult<Boolean> updateEvalTable(@Valid @RequestBody EvalTableSaveReqVO updateReqVO) {
        evalTableService.updateEvalTable(updateReqVO);
        return success(true);
    }

    @PostMapping("/submit")
    @ApiOperation(value = "提交-心理测评量表管理")
    public CommonResult<Boolean> submitEvalTable(@RequestBody EvalTableSubmitReqVO submitReqVO) {
        evalTableService.submitEvalTable(submitReqVO);
        return success(true);
    }


    @PostMapping("/delete")
    @ApiOperation(value = "删除-心理测评量表管理")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteEvalTable(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            evalTableService.deleteEvalTable(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得-心理测评量表管理")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<EvalTableRespVO> getEvalTable(@RequestParam("id") String id) {
        EvalTableDO evalTable = evalTableService.getEvalTable(id);
        return success(BeanUtils.toBean(evalTable, EvalTableRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得-心理测评量表管理分页")
    public CommonResult<PageResult<EvalTableRespVO>> getEvalTablePage(@Valid @RequestBody EvalTablePageReqVO pageReqVO) {
        if (StrUtil.isEmpty(pageReqVO.getOrgCode()) && !SessionUserUtil.getSessionUser().getIsAdmin()) {
            pageReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        PageResult<EvalTableDO> pageResult = evalTableService.getEvalTablePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EvalTableRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得-心理测评量表管理列表")
    public CommonResult<List<EvalTableRespVO>> getEvalTableList(@Valid @RequestBody EvalTableListReqVO listReqVO) {
        if (StrUtil.isEmpty(listReqVO.getOrgCode()) && !SessionUserUtil.getSessionUser().getIsAdmin()) {
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        List<EvalTableDO> list = evalTableService.getEvalTableList(listReqVO);
        return success(BeanUtils.toBean(list, EvalTableRespVO.class));
    }

    @PostMapping("/updateUsageStatus")
    @ApiOperation(value = "更新-心理测评量表使用状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "编号"),
            @ApiImplicitParam(name = "usageStatus", value = "使用状态")
    })
    public CommonResult<Boolean> updateUsageStatus(@RequestParam("id") String id, @RequestParam("usageStatus") String usageStatus) {
        evalTableService.updateUsageStatus(id, usageStatus);
        return success();
    }

    @PostMapping("/tablePreviewCommon")
    @ApiOperation(value = "量表预览-常规模式", response = EvalTablePrewCommonRespVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "编号")
    })
    public CommonResult tablePreviewCommon(@RequestParam("id") String id) {
        EvalTablePrewCommonRespVO respVO = evalTableService.tablePreviewCommon(id);
        return success(respVO);
    }

    @PostMapping("/tablePreviewSimple")
    @ApiOperation(value = "量表预览-极简模式", response = EvalTablePrewSimpleRespVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "编号"),
            @ApiImplicitParam(name = "sortOrder", value = "题号,默认1开始")
    })
    public CommonResult tablePreviewSimple(@RequestParam("id") String id, @RequestParam("sortOrder") Integer sortOrder) {
        return success(evalTableService.tablePreviewSimple(id, sortOrder));
    }

    @PostMapping("/tablePreviewCommonAndAnswer")
    @ApiOperation(value = "量表预览-常规模式-带答案", response = EvalTablePrewCommonAnswerRespVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tableId", value = "量表ID"),
            @ApiImplicitParam(name = "answerId", value = "答题卡ID")
    })
    public CommonResult tablePreviewCommonAndAnswer(@RequestParam("tableId") String tableId, @RequestParam("answerId") String answerId) {
        EvalTablePrewCommonAnswerRespVO respVO = evalTableService.tablePreviewCommonAndAnswer(tableId, answerId);
        return success(respVO);
    }

    @PostMapping("/tablePreviewSimpleAndAnswer")
    @ApiOperation(value = "量表预览-极简模式-带答案", response = EvalTablePrewSimpleAnswerRespVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tableId", value = "量表ID"),
            @ApiImplicitParam(name = "answerId", value = "答题卡ID"),
            @ApiImplicitParam(name = "sortOrder", value = "题号,默认1开始")
    })
    public CommonResult tablePreviewSimpleAndAnswer(@RequestParam("tableId") String tableId, @RequestParam("answerId") String answerId,
                                                    @RequestParam("sortOrder") Integer sortOrder) {
        EvalTablePrewSimpleAnswerRespVO respVO = evalTableService.tablePreviewSimpleAndAnswer(tableId, answerId, sortOrder);
        return success(respVO);
    }


}
