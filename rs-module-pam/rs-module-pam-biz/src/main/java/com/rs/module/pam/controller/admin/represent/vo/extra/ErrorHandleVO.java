package com.rs.module.pam.controller.admin.represent.vo.extra;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * 异常处置
 *
 */
@Data
@ApiModel("异常处置VO")
public class ErrorHandleVO {
    
    /**
     * id
     */
    @NotNull(message = "id 不能为空!")
    @ApiModelProperty(value = "id", required = true)
    private String id;

    /**
     * 异常处置结果
     */
    @NotNull(message = "异常处置结果 不能为空!")
    @ApiModelProperty(value = "异常处置结果", required = true)
    private String errorMsg;
}
