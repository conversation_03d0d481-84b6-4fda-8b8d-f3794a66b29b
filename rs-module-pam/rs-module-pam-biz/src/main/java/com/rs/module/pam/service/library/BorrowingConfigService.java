package com.rs.module.pam.service.library;

import java.util.*;
import javax.validation.*;

import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.library.vo.*;
import com.rs.module.pam.entity.library.BorrowingConfigDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 监所事务管理-图书借阅配置 Service 接口
 *
 * <AUTHOR>
 */
public interface BorrowingConfigService extends IBaseService<BorrowingConfigDO>{

    /**
     * 创建监所事务管理-图书借阅配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createBorrowingConfig(@Valid BorrowingConfigSaveReqVO createReqVO);

    /**
     * 创建监所事务管理-图书借阅配置 (依据创建图书来判断是否有默认值  没有则创建)
     *
     */
    void createBorrowingDefaultConfigByMgt();

    /**
     * 更新监所事务管理-图书借阅配置
     *
     * @param updateReqVO 更新信息
     */
    void updateBorrowingConfig(@Valid BorrowingConfigSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-图书借阅配置
     *
     * @param id 编号
     */
    void deleteBorrowingConfig(String id);

    /**
     * 获得监所事务管理-图书借阅配置
     *
     * @param id 编号
     * @return 监所事务管理-图书借阅配置
     */
    BorrowingConfigDO getBorrowingConfig(String id);

    /**
    * 获得监所事务管理-图书借阅配置分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-图书借阅配置分页
    */
    PageResult<BorrowingConfigDO> getBorrowingConfigPage(BorrowingConfigPageReqVO pageReqVO);

    /**
    * 获得监所事务管理-图书借阅配置列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-图书借阅配置列表
    */
    List<BorrowingConfigDO> getBorrowingConfigList(BorrowingConfigListReqVO listReqVO);


    Map<String, Integer> getBorrowingConfigMap();

    BorrowingConfigDO getBorrowingConfigByOrgCode(String orgCode);
}
