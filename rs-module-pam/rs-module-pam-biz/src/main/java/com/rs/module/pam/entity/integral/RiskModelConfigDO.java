package com.rs.module.pam.entity.integral;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 监所事务管理-风险模型 DO
 *
 * <AUTHOR>
 */
@TableName("pam_integral_risk_model_config")
@KeySequence("pam_integral_risk_model_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_integral_risk_model_config")
public class RiskModelConfigDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 风险模型名称
     */
    private String name;
    /**
     * 风险模型类型
     */
    private String type;
    /**
     * 等级配置
     */
    private String levelConfig;
    /**
     * 排序
     */
    private Integer orderId;

}
