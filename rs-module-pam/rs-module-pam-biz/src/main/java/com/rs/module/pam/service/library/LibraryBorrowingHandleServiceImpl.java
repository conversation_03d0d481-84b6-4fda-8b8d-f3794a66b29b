package com.rs.module.pam.service.library;

import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.pam.controller.admin.library.vo.*;
import com.rs.module.pam.entity.library.LibraryBorrowingHandleDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.pam.dao.library.LibraryBorrowingHandleDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 监所事务管理-图书借阅处理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LibraryBorrowingHandleServiceImpl extends BaseServiceImpl<LibraryBorrowingHandleDao, LibraryBorrowingHandleDO> implements LibraryBorrowingHandleService {

    @Resource
    private LibraryBorrowingHandleDao libraryBorrowingHandleDao;

    @Override
    public String createLibraryBorrowingHandle(LibraryBorrowingHandleSaveReqVO createReqVO) {
        // 插入
        LibraryBorrowingHandleDO libraryBorrowingHandle = BeanUtils.toBean(createReqVO, LibraryBorrowingHandleDO.class);
        libraryBorrowingHandleDao.insert(libraryBorrowingHandle);
        // 返回
        return libraryBorrowingHandle.getId();
    }

    @Override
    public void updateLibraryBorrowingHandle(LibraryBorrowingHandleSaveReqVO updateReqVO) {
        // 校验存在
        validateLibraryBorrowingHandleExists(updateReqVO.getId());
        // 更新
        LibraryBorrowingHandleDO updateObj = BeanUtils.toBean(updateReqVO, LibraryBorrowingHandleDO.class);
        libraryBorrowingHandleDao.updateById(updateObj);
    }

    @Override
    public void deleteLibraryBorrowingHandle(String id) {
        // 校验存在
        validateLibraryBorrowingHandleExists(id);
        // 删除
        libraryBorrowingHandleDao.deleteById(id);
    }

    private void validateLibraryBorrowingHandleExists(String id) {
        if (libraryBorrowingHandleDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-图书借阅处理数据不存在");
        }
    }

    @Override
    public LibraryBorrowingHandleDO getLibraryBorrowingHandle(String id) {
        return libraryBorrowingHandleDao.selectById(id);
    }

    @Override
    public PageResult<LibraryBorrowingHandleDO> getLibraryBorrowingHandlePage(LibraryBorrowingHandlePageReqVO pageReqVO) {
        return libraryBorrowingHandleDao.selectPage(pageReqVO);
    }

    @Override
    public List<LibraryBorrowingHandleDO> getLibraryBorrowingHandleList(LibraryBorrowingHandleListReqVO listReqVO) {
        return libraryBorrowingHandleDao.selectList(listReqVO);
    }

    @Override
    public List<JSONObject> getBusinessAnalysis(Date time, String orgCode) {
        return libraryBorrowingHandleDao.getBusinessAnalysis(time, orgCode);
    }


}
