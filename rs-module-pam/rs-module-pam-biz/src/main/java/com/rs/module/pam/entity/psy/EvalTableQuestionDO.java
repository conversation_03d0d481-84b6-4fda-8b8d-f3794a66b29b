package com.rs.module.pam.entity.psy;

import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-心理测评量表关联提目 DO
 *
 * <AUTHOR>
 */
@TableName("pam_psy_eval_table_question")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_psy_eval_table_question")
public class EvalTableQuestionDO extends BaseDO {
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 量表ID
     */
    private String tableId;
    /**
     * 题目内容
     */
    private String questionText;
    /**
     * 题目类型（字典：ZD_XLPC_TMLX）
     */
    private String questionType;
    /**
     * 计分规则
     */
    private String scoreRule;
    /**
     * 参考答案，选择题答案保存选项代码，多选题多个答案逗号分割，简答题保存答案
     */
    private String answer;
    /**
     * 题目分值
     */
    private BigDecimal score;
    /**
     * 题目顺序
     */
    private Integer sortOrder;

}
