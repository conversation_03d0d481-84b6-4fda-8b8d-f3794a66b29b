package com.rs.module.pam.controller.admin.integral.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@ApiModel(description = "积分指标 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("风险模型id")
    private String riskModelId;
    @ApiModelProperty("父类id")
    private String parentId;
    @ApiModelProperty("指标名称")
    private String name;
    @ApiModelProperty("指标权重")
    private BigDecimal weight;
    @ApiModelProperty("排序")
    private Integer orderId;
}
