package com.rs.module.pam.controller.admin.religion.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-宗教申请新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ReligionApplySqReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;


    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("信仰宗教")
    @NotEmpty(message = "信仰宗教不能为空")
    private String religion;

    @ApiModelProperty("备注")
    private String remark;

}
