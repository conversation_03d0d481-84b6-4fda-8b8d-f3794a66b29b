package com.rs.module.pam.controller.admin.daily.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 监所事务管理-事务选项字典新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LifeDictEventSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("event_name")
    @NotEmpty(message = "event_name不能为空")
    private String eventName;

    @ApiModelProperty("停启用（字典：ZD_TQ_TYPE）")
    @NotNull(message = "停启用（字典：ZD_TQ_TYPE）不能为空")
    private Short eventStatus;

}
