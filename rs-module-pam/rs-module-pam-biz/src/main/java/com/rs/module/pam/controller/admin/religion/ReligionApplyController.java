package com.rs.module.pam.controller.admin.religion;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.pam.controller.admin.religion.vo.*;
import com.rs.module.pam.entity.religion.ReligionApplyDO;
import com.rs.module.pam.service.religion.ReligionApplyService;

@Api(tags = "监所事务管理-宗教申请")
@RestController
@RequestMapping("/pam/religion/religionApply")
@Validated
public class ReligionApplyController {

    @Resource
    private ReligionApplyService religionApplyService;

    @PostMapping("/create")
    @ApiOperation(value = "创建监所事务管理-宗教申请")
    public CommonResult<String> createReligionApply(@Valid @RequestBody ReligionApplySqReqVO createReqVO) {
        return success(religionApplyService.createReligionApply(createReqVO));
    }

    @PostMapping("/approval")
    @ApiOperation(value = "审批")
    public CommonResult<Boolean> approval(@Valid @RequestBody ReligionApplyApprovalReqVO applyApprovalReqVO) {
        religionApplyService.approval(applyApprovalReqVO);
        return success(true);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新监所事务管理-宗教申请")
    public CommonResult<Boolean> updateReligionApply(@Valid @RequestBody ReligionApplySaveReqVO updateReqVO) {
        religionApplyService.updateReligionApply(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除监所事务管理-宗教申请")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteReligionApply(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           religionApplyService.deleteReligionApply(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得监所事务管理-宗教申请")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ReligionApplyRespVO> getReligionApply(@RequestParam("id") String id) {
        ReligionApplyDO religionApply = religionApplyService.getReligionApply(id);
        return success(BeanUtils.toBean(religionApply, ReligionApplyRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得监所事务管理-宗教申请分页")
    public CommonResult<PageResult<ReligionApplyRespVO>> getReligionApplyPage(@Valid @RequestBody ReligionApplyPageReqVO pageReqVO) {
        PageResult<ReligionApplyDO> pageResult = religionApplyService.getReligionApplyPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ReligionApplyRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得监所事务管理-宗教申请列表")
    public CommonResult<List<ReligionApplyRespVO>> getReligionApplyList(@Valid @RequestBody ReligionApplyListReqVO listReqVO) {
        List<ReligionApplyDO> list = religionApplyService.getReligionApplyList(listReqVO);
        return success(BeanUtils.toBean(list, ReligionApplyRespVO.class));
    }
}
