package com.rs.module.pam.controller.admin.duty.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CurrentDutyRecordsInfoVO {

    @ApiModelProperty("值班记录id")
    private String id;

    @ApiModelProperty("班次id")
    private String shiftId;

    @ApiModelProperty("班次名称")
    private String shiftName;

    @ApiModelProperty("班次开始时间")
    private Date shiftStartTime;

    @ApiModelProperty("班次结束时间")
    private Date shiftEndTime;

    @ApiModelProperty("值班日期")
    private Date dutyDate;

    @ApiModelProperty("监管人员编码1")
    private String jgrybm1;

    @ApiModelProperty("监管人员编码2")
    private String jgrybm2;

    @ApiModelProperty("监管人员姓名1")
    private String jgryxm1;

    @ApiModelProperty("监管人员姓名2")
    private String jgryxm2;

}
