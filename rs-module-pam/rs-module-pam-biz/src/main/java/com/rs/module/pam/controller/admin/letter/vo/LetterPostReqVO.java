package com.rs.module.pam.controller.admin.letter.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-家属通信-寄信登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LetterPostReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("收信人姓名")
    @NotEmpty(message = "收信人姓名不能为空")
    private String receiveMailUser;

    @ApiModelProperty("关系")
    @NotEmpty(message = "关系不能为空")
    private String relation;

    @ApiModelProperty("收信地址")
    @NotEmpty(message = "收信地址不能为空")
    private String receiveAddress;

    @ApiModelProperty("来信日期")
    @NotNull(message = "来信日期不能为空")
    private Date sendTime;

    @ApiModelProperty("收信单位")
    private String receivePrison;

    @ApiModelProperty("信件邮编")
    private String mailNo;

    @ApiModelProperty("备注")
    private String remark;

}
