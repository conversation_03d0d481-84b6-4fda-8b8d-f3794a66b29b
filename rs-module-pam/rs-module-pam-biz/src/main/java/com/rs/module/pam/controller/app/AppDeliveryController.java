package com.rs.module.pam.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.cook.vo.DeliveryRespVO;
import com.rs.module.pam.controller.app.vo.AppDeliveryPrisonerListVO;
import com.rs.module.pam.entity.cook.DeliveryDO;
import com.rs.module.pam.service.cook.DeliveryService;
import com.rs.module.pam.util.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "内外屏-监室配餐-发饭")
@RestController
@RequestMapping("/app/pam")
@Validated
public class AppDeliveryController {

    @Resource
    private DeliveryService deliveryService;

    @ApiOperation(value = "外屏-监室配餐-发饭人员列表", response = AppDeliveryPrisonerListVO.class)
    @GetMapping("/cook/delivery/getDeliveryPrisonerList")
    @ApiImplicitParam(name = "deliveryId", value = "发放ID", required = true, dataType = "String", paramType = "query")
    public CommonResult<AppDeliveryPrisonerListVO> getDeliveryPrisonerListVO(@RequestParam("deliveryId") String deliveryId) {
        AppDeliveryPrisonerListVO appDeliveryPrisonerListVO = deliveryService.getDeliveryPrisonerListVO(deliveryId);
        return success(appDeliveryPrisonerListVO);
    }

    @ApiOperation(value = "外屏-监室配餐-开始发饭")
    @PostMapping("/cook/delivery/startDelivery")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "roomId", value = "监室ID", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "roomName", value = "监室名称", required = true, dataType = "String", paramType = "query")
    })
    public CommonResult<String> startDelivery(@RequestParam("orgCode") String orgCode,
                                              @RequestParam("roomId") String roomId,
                                              @RequestParam("roomName") String roomName) {
        return success(deliveryService.startDelivery(orgCode, roomId, roomName));
    }

    @ApiOperation(value = "外屏-监室配餐-结束发饭")
    @PostMapping("/cook/delivery/endDelivery")
    @ApiImplicitParam(name = "deliveryId", value = "发放ID", required = true, dataType = "String", paramType = "query")
    public CommonResult endDelivery(@RequestParam("deliveryId") String deliveryId) {
        deliveryService.endDelivery(deliveryId);
        return success();
    }

    @ApiOperation(value = "外屏-监室配餐-留饭")
    @PostMapping("/cook/delivery/keepDelivery")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybms", value = "监管人员编码，多个用逗号隔开", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "deliveryId", value = "发放ID", required = true, dataType = "String", paramType = "query")
    })
    public CommonResult keepDelivery(@RequestParam("jgrybms") String jgrybms, @RequestParam("deliveryId") String deliveryId) {
        deliveryService.keepDelivery(jgrybms, deliveryId);
        return success();
    }

    @PostMapping("/cook/delivery/list")
    @ApiOperation(value = "获得外屏-监室配餐-发饭记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "timeType", value = "时间段：1 全部，2 今天，3 昨天，4 近一周", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "roomId", value = "监室号", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "orgCode", value = "机构编号", dataType = "String", paramType = "query", required = true)
    })
    public CommonResult<List<DeliveryRespVO>> list(@RequestParam(value = "timeType") String timeType,
                                                   @RequestParam("roomId") String roomId,
                                                   @RequestParam("orgCode") String orgCode) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        Map<String, Date> commonAppRecordPeriod = CommonUtils.getCommonAppRecordPeriod(timeType);
        LambdaQueryWrapper<DeliveryDO> wrapper = new LambdaQueryWrapper<DeliveryDO>();
        wrapper.eq(DeliveryDO::getOrgCode, orgCode)
                .eq(DeliveryDO::getRoomId, roomId)
                .between(commonAppRecordPeriod.get("startTime") != null, DeliveryDO::getDeliveryTime,
                        commonAppRecordPeriod.get("startTime"), commonAppRecordPeriod.get("endTime"));
        List<DeliveryDO> list = deliveryService.list(wrapper);
        List<DeliveryRespVO> resultList = new ArrayList<>();
        for (DeliveryDO deliveryDO : list) {
            DeliveryRespVO deliveryRespVO = BeanUtils.toBean(deliveryDO, DeliveryRespVO.class);
            String status = deliveryRespVO.getStatus();
            if (status.equals("0")) {
                deliveryRespVO.setStatusName("发饭中");
            } else {
                deliveryRespVO.setStatusName("已发饭");
            }
            resultList.add(deliveryRespVO);
        }
        return success(resultList);
    }

    @ApiOperation(value = "内屏-监室配餐-发饭人员列表", response = AppDeliveryPrisonerListVO.class)
    @GetMapping("/public/cook/delivery/getNpDeliveryPrisonerListVO")
    @ApiImplicitParam(name = "deliveryId", value = "发放ID", required = true, dataType = "String", paramType = "query")
    public CommonResult<List<AppDeliveryPrisonerListVO.DeliveryPrisonerList>> getNpDeliveryPrisonerListVO(@RequestParam("deliveryId") String deliveryId) {
        List<AppDeliveryPrisonerListVO.DeliveryPrisonerList> appDeliveryPrisonerListVOS =
                deliveryService.getNpDeliveryPrisonerListVO(deliveryId);
        return success(appDeliveryPrisonerListVOS);
    }

    @ApiOperation(value = "内屏-监室配餐-确认发饭")
    @PostMapping("/public/cook/delivery/confirmDelivery")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "roomId", value = "监室ID", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "deliveryId", value = "发放ID", required = false, dataType = "String", paramType = "query")
    })
    public CommonResult<DeliveryRespVO> confirmDelivery(
            @RequestParam(value = "deliveryId", required = false) String deliveryId, @RequestParam("orgCode") String orgCode,
            @RequestParam("roomId") String roomId) {
        return success(deliveryService.confirmDelivery(deliveryId, orgCode, roomId));
    }

    @ApiOperation(value = "内屏-监室配餐-监管人员扫脸发饭")
    @PostMapping("/public/cook/delivery/sweepFaceDelivery")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "人员ID", value = "监管人员ID", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "deliveryId", value = "发放ID", required = false, dataType = "String", paramType = "query")
    })
    public CommonResult sweepFaceDelivery(@RequestParam(value = "deliveryId") String deliveryId,
                                          @RequestParam("ryId") String ryId) {
        deliveryService.sweepFaceDelivery(deliveryId, ryId);
        return success();
    }

}
