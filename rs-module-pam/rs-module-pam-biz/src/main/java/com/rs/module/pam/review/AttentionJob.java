package com.rs.module.pam.review;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.module.pam.cons.CommonConstants;
import com.rs.module.pam.entity.attention.PrisonerDO;
import com.rs.module.pam.service.attention.AttentionPrisonerService;
import com.rs.module.pam.service.daily.CleanService;
import com.rs.module.pam.service.psy.EvalPlanService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 监室事务Job
 *
 * <AUTHOR>
 * @date 2025年3月19日
 */
@Component
@Slf4j
public class AttentionJob {

    @Resource
    private AttentionPrisonerService prisonerService;
    @Resource
    private EvalPlanService evalPlanService;
    @Resource
    private CleanService cleanService;

    /**
     * 重点关注期限判断任务
     */
    @XxlJob("attentionEndDateJudge")
    public void attentionEndDateJudge() {
        try {
            List<PrisonerDO> prisonerList = prisonerService.list(new LambdaQueryWrapper<PrisonerDO>()
                    .in(PrisonerDO::getRegStatus, CommonConstants.PRISONER_ARCHIVE_STATUS_PASSED, CommonConstants.PRISONER_ARCHIVE_STATUS_WAIT));
            for (PrisonerDO prisonerDO : prisonerList) {
                if (ObjectUtil.isNotEmpty(prisonerDO.getEndTime()) &&
                        DateUtil.compare(prisonerDO.getEndTime(), DateUtil.date()) < 0) {
                    // 关注时间已过
                    if (CommonConstants.PRISONER_ARCHIVE_STATUS_PASSED.equals(prisonerDO.getRegStatus())) {
                        prisonerDO.setRegStatus(CommonConstants.PRISONER_ARCHIVE_STATUS_END);
                    } else if (CommonConstants.PRISONER_ARCHIVE_STATUS_WAIT.equals(prisonerDO.getRegStatus())) {
                        // 逾期未审批
                        prisonerDO.setRegStatus(CommonConstants.PRISONER_ARCHIVE_STATUS_OVERDUE);
                    }
                    prisonerService.updateById(prisonerDO);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.log("任务执行失败:" + e.getMessage());
        }
    }

//    /**
//     * 重点关注频率提醒任务
//     */
//    @XxlJob("attentionRemind")
//    public void attentionRemind() {
//        try {
//            List<PrisonerDO> prisonerList = prisonerService.list(new LambdaQueryWrapper<PrisonerDO>()
//                    .eq(PrisonerDO::getRegStatus, CommonConstants.PRISONER_ARCHIVE_STATUS_PASSED));
//            for (PrisonerDO prisonerDO : prisonerList) {
//                // 判断是否超期
//                if (ObjectUtil.isNotEmpty(prisonerDO.getEndTime()) && DateUtil.compare(prisonerDO.getEndTime(), DateUtil.date()) < 0) {
//                    prisonerDO.setRegStatus(CommonConstants.PRISONER_ARCHIVE_STATUS_END);
//                    prisonerService.updateById(prisonerDO);
//                    continue;
//                }
//                Integer frequency = prisonerDO.getFrequency();
//                String frequencyType = prisonerDO.getFrequencyType();
//
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            XxlJobHelper.log("任务执行失败:" + e.getMessage());
//        }
//    }

    /**
     * 一个月两次的安全大检查提醒
     */
    @XxlJob("cleanCheckMsg")
    public void cleanCheckMsg() {
        // 日常清监
        cleanService.sendMsgRcqj();
        // 安全大检查
        cleanService.sendMsgAqdjc();
    }

    /**
     * 心理测评计划推送
     */
    @XxlJob(CommonConstants.PSY_EXECTOR_JOBHANDLER)
    public void execPsyPlan() {
        // 推送心理测评计划
        long jobId = XxlJobHelper.getJobId();
        String logId = FileUtil.getPrefix(XxlJobHelper.getJobLogFileName());
        XxlJobHelper.log("开始推送心理测评计划, jobId=" + jobId + ", logId=" + logId);
        // 推送心理测评计划
        evalPlanService.sendPlan(String.valueOf(jobId), logId);
    }

}
