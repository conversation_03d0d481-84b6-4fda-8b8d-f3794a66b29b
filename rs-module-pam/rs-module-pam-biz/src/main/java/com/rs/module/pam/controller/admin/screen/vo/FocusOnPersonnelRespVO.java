package com.rs.module.pam.controller.admin.screen.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.base.entity.pm.PrisonerTagDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管教业务 - 重点关注人员 Response VO")
@Data
@EqualsAndHashCode(callSuper = false)
public class FocusOnPersonnelRespVO  extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("人员姓名")
    private String xm;

    @ApiModelProperty("头像")
    private String frontPhoto;

    @ApiModelProperty("监室号")
    private String jsh;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("诉讼环节")
    @Trans(type = TransType.DICTIONARY, key = "ZD_SSJD")
    private String sshj;

    @ApiModelProperty("涉嫌罪名")
    @Trans(type = TransType.DICTIONARY, key = "ZD_SXZM")
    private String sxzm;

    @ApiModelProperty("关押期限")
    private Date gyqx;

    @ApiModelProperty("人员标签")
    public List<PrisonerTagDO> tagList;

}
