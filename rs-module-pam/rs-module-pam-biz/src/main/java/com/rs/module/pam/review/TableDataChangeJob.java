package com.rs.module.pam.review;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.StringUtil;
import com.gosun.zhjg.prison.room.terminal.config.socketio.PushMessage;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.CustomPushMessageConditionDTO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.SocketService;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.PushMessageAckVO;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.framework.rpc.core.UserTokenContext;
import com.rs.module.base.service.pm.TableDataChangeService;
import com.rs.module.pam.config.AsyncTaskPoolConfig;
import com.rs.module.pam.cons.RoomRepresentConstant;
import com.rs.module.pam.cons.SocketEventConstant;
import com.rs.module.pam.controller.admin.represent.vo.extra.RoomInfoVO;
import com.rs.module.pam.controller.admin.represent.vo.extra.RoomPersonVO;
import com.rs.module.pam.dao.represent.PersonRepresentDao;
import com.rs.module.pam.dao.represent.RepresentDao;
import com.rs.module.pam.entity.represent.PersonRepresentDO;
import com.rs.module.pam.entity.represent.RepresentConfigDO;
import com.rs.module.pam.entity.represent.RepresentDO;
import com.rs.module.pam.service.represent.RepresentConfigService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 数据变动变动检测
 *
 */
@Component
@Slf4j
public class TableDataChangeJob {

    @Resource
    private TableDataChangeService tableDataChangeService;
    /**
     * job 方法执行状态
     */
    public static final AtomicBoolean IS_RUNNING = new AtomicBoolean(false);

    /**
     * 数据变动变动检测任务
     */
    @XxlJob("TableDataChangeJob")
    @Transactional
    public void TableDataChangeJob() {
        if (IS_RUNNING.compareAndSet(false, true)) {
            // 当前没有线程正在执行该方法，将 IS_RUNNING 设置为 true，表示当前线程正在执行该方法
            try {
                String uuid = StringUtil.getGuid32();
                log.debug("[{}]数据变动变动检测任务开始！", uuid);
                XxlJobHelper.log("[{}]数据变动变动检测任务开始！", uuid);
                long st = System.currentTimeMillis();
                tableDataChangeService.handler(null);
                double t = ((System.currentTimeMillis() - st) / 1000.0);
                log.debug("[{}]数据变动变动检测任务完成！用时：{}s", uuid, t);
                XxlJobHelper.log("[{}]数据变动变动检测任务完成！用时：{}s", uuid, t);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                // 方法执行完毕，将 IS_RUNNING 设置为 false，表示该方法可以被其他线程执行
                IS_RUNNING.set(false);
            }
        }
    }


}
