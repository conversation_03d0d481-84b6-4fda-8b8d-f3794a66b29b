package com.rs.module.pam.controller.admin.religion.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-宗教申请分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ReligionApplyPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("数据来源")
    @Trans(type = TransType.DICTIONARY, key = "ZD_DATA_SOURCES")
    private String dataSources;

    @ApiModelProperty("监管人员编码 字典：ZD_DATA_SOURCES")
    private String jgrybm;

    @ApiModelProperty("信仰宗教 字典：ZD_ZJLB_XYZJ")
    @Trans(type = TransType.DICTIONARY, key = "ZD_ZJLB_XYZJ")
    private String religion;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态 字典：ZD_ZJLB_SQZT")
    @Trans(type = TransType.DICTIONARY, key = "ZD_ZJLB_SQZT")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date[] approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
