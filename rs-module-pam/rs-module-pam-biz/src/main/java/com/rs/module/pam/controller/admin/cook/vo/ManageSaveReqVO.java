package com.rs.module.pam.controller.admin.cook.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel(description = "管理后台 - 监所事务管理-菜品管理新增/修改 Request VO")
@Data
public class ManageSaveReqVO {
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("菜品名称")
    @NotEmpty(message = "菜品名称不能为空")
    private String cookName;

    @ApiModelProperty("菜品分类ID")
    @NotEmpty(message = "菜品分类ID不能为空")
    private String cateId;

    @ApiModelProperty("排序")
    @NotNull(message = "排序不能为空")
    private Integer sort;

}
