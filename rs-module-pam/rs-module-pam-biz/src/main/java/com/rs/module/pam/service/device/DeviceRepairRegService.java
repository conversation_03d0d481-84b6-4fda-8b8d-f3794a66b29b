package com.rs.module.pam.service.device;

import java.util.*;
import javax.validation.*;
import com.rs.module.pam.controller.admin.device.vo.*;
import com.rs.module.pam.entity.device.DeviceRepairRegDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 监所事务管理-设备报修登记 Service 接口
 *
 * <AUTHOR>
 */
public interface DeviceRepairRegService extends IBaseService<DeviceRepairRegDO>{

    /**
     * 创建监所事务管理-设备报修登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createDeviceRepairReg(@Valid DeviceRepairRegSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-设备报修登记
     *
     * @param updateReqVO 更新信息
     */
    void updateDeviceRepairReg(@Valid DeviceRepairRegSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-设备报修登记
     *
     * @param id 编号
     */
    void deleteDeviceRepairReg(String id);

    /**
     * 获得监所事务管理-设备报修登记
     *
     * @param id 编号
     * @return 监所事务管理-设备报修登记
     */
    DeviceRepairRegDO getDeviceRepairReg(String id);

    /**
    * 获得监所事务管理-设备报修登记分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-设备报修登记分页
    */
    PageResult<DeviceRepairRegDO> getDeviceRepairRegPage(DeviceRepairRegPageReqVO pageReqVO);

    /**
    * 获得监所事务管理-设备报修登记列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-设备报修登记列表
    */
    List<DeviceRepairRegDO> getDeviceRepairRegList(DeviceRepairRegListReqVO listReqVO);


    void approvalDeviceRepairReg(DeviceRepairRegApprovalReqVO approvalReqVO);

    void handleDeviceRepairReg(DeviceRepairRegHandleReqVO handleReqVO);

    void ignoreChangeDeviceRepairReg(DeviceRepairRegHandleReqVO handleReqVO);

    PageResult<DeviceRepairRegDO> getDeviceRepairRegRecordPage(int pageNo, int pageSize, String applicantUserSfzh, String type);
}
