package com.rs.module.pam.controller.admin.letter.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-家属通信-收信登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LetterReceiveSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("送信人姓名")
    @NotEmpty(message = "送信人姓名不能为空")
    private String sendMailUser;

    @ApiModelProperty("关系（字典：ZD_RYGXDM）")
    @NotEmpty(message = "关系（字典：ZD_RYGXDM）不能为空")
    private String relation;

    @ApiModelProperty("来信地址")
    private String sendAddress;

    @ApiModelProperty("来信日期")
    private Date sendDate;

    @ApiModelProperty("来信单位")
    private String sendPrison;

    @ApiModelProperty("信件邮编")
    private String mailNo;

    @ApiModelProperty("信件内容url")
    private String mailUrl;

    @ApiModelProperty("送信人联系电话")
    private String sendContactNumber;

}
