package com.rs.module.pam.controller.admin.psy.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评量表关联提目列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EvalTableQuestionListReqVO extends BaseVO {

    @ApiModelProperty("量表ID")
    private String tableId;

    @ApiModelProperty("题目内容")
    private String questionText;

    @ApiModelProperty("题目类型（字典：ZD_XLPC_TMLX）")
    private String questionType;

    @ApiModelProperty("计分规则")
    private String scoreRule;

    @ApiModelProperty("排序")
    private Integer sortOrder;
}
