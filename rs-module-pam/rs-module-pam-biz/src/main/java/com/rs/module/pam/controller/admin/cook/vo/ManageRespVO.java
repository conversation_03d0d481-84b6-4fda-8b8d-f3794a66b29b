package com.rs.module.pam.controller.admin.cook.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.pam.entity.cook.CateDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 监所事务管理-菜品管理 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ManageRespVO extends BaseVO {
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("菜品名称")
    private String cookName;
    @ApiModelProperty("菜品分类ID")
    private String cateId;
    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("菜品分类")
    private CateDO cateInfo;
}
