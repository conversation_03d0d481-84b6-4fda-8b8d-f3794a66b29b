package com.rs.module.pam.controller.admin.daily.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 监所事务管理-一日生活制度模板 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LifeTemplateRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("制度名称")
    private String templateName;
}
