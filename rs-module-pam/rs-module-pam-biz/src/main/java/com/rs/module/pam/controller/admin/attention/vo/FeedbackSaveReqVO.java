package com.rs.module.pam.controller.admin.attention.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

/**
 * 监所事务管理-重点人员关注反馈 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 监所事务管理-重点人员关注反馈新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FeedbackSaveReqVO extends BaseDO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("重点人员关注登记ID")
    @NotEmpty(message = "重点人员关注登记ID不能为空")
    private String attentionId;

    @ApiModelProperty("反馈内容")
    @NotEmpty(message = "反馈内容不能为空")
    private String feedbackContent;

    @ApiModelProperty("登记经办人")
    private String operatorSfzh;

    @ApiModelProperty("登记经办人姓名")
    private String operatorXm;

    @ApiModelProperty("登记时间")
    private Date operatorTime;

}
