package com.rs.module.pam.dao.letter;

import java.util.*;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.entity.letter.LetterReceiveDO;
import com.rs.module.pam.enums.LetterReceiveAppRecordTypeEnum;
import com.rs.module.pam.enums.LetterReceiveStatusEnum;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.pam.controller.admin.letter.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
 * 监所事务管理-家属通信-收信登记 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface LetterReceiveDao extends IBaseDao<LetterReceiveDO> {


    default PageResult<LetterReceiveDO> selectPage(LetterReceivePageReqVO reqVO) {
        Page<LetterReceiveDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<LetterReceiveDO> wrapper = new LambdaQueryWrapperX<LetterReceiveDO>()
                .eqIfPresent(LetterReceiveDO::getDataSources, reqVO.getDataSources())
                .eqIfPresent(LetterReceiveDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(LetterReceiveDO::getSendMailUser, reqVO.getSendMailUser())
                .eqIfPresent(LetterReceiveDO::getRelation, reqVO.getRelation())
                .eqIfPresent(LetterReceiveDO::getSendAddress, reqVO.getSendAddress())
                .betweenIfPresent(LetterReceiveDO::getSendDate, reqVO.getSendDate())
                .eqIfPresent(LetterReceiveDO::getSendPrison, reqVO.getSendPrison())
                .eqIfPresent(LetterReceiveDO::getMailNo, reqVO.getMailNo())
                .eqIfPresent(LetterReceiveDO::getMailUrl, reqVO.getMailUrl())
                .betweenIfPresent(LetterReceiveDO::getRegisterTime, reqVO.getRegisterTime())
                .eqIfPresent(LetterReceiveDO::getRegisterUserSfzh, reqVO.getRegisterUserSfzh())
                .likeIfPresent(LetterReceiveDO::getRegisterUserName, reqVO.getRegisterUserName())
                .eqIfPresent(LetterReceiveDO::getSignUrl, reqVO.getSignUrl())
                .eqIfPresent(LetterReceiveDO::getStatus, reqVO.getStatus())
                .eqIfPresent(LetterReceiveDO::getPassUser, reqVO.getPassUser())
                .betweenIfPresent(LetterReceiveDO::getPassTime, reqVO.getPassTime())
                .eqIfPresent(LetterReceiveDO::getPassRemark, reqVO.getPassRemark())
                .eqIfPresent(LetterReceiveDO::getGjApproverSfzh, reqVO.getGjApproverSfzh())
                .eqIfPresent(LetterReceiveDO::getGjApproverXm, reqVO.getGjApproverXm())
                .betweenIfPresent(LetterReceiveDO::getGjApproverTime, reqVO.getGjApproverTime())
                .eqIfPresent(LetterReceiveDO::getGjApprovalResult, reqVO.getGjApprovalResult())
                .eqIfPresent(LetterReceiveDO::getGjApprovalComments, reqVO.getGjApprovalComments())
                .eqIfPresent(LetterReceiveDO::getGroupApproverSfzh, reqVO.getGroupApproverSfzh())
                .eqIfPresent(LetterReceiveDO::getGroupApproverXm, reqVO.getGroupApproverXm())
                .betweenIfPresent(LetterReceiveDO::getGroupApproverTime, reqVO.getGroupApproverTime())
                .eqIfPresent(LetterReceiveDO::getGroupApprovalResult, reqVO.getGroupApprovalResult())
                .eqIfPresent(LetterReceiveDO::getGroupApprovalComments, reqVO.getGroupApprovalComments())
                .eqIfPresent(LetterReceiveDO::getLeaderApproverSfzh, reqVO.getLeaderApproverSfzh())
                .eqIfPresent(LetterReceiveDO::getLeaderApproverXm, reqVO.getLeaderApproverXm())
                .betweenIfPresent(LetterReceiveDO::getLeaderApproverTime, reqVO.getLeaderApproverTime())
                .eqIfPresent(LetterReceiveDO::getLeaderApprovalResult, reqVO.getLeaderApprovalResult())
                .eqIfPresent(LetterReceiveDO::getLeaderApprovalComments, reqVO.getLeaderApprovalComments())
                .eqIfPresent(LetterReceiveDO::getActInstId, reqVO.getActInstId())
                .eqIfPresent(LetterReceiveDO::getTaskId, reqVO.getTaskId());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(LetterReceiveDO::getAddTime);
        }
        Page<LetterReceiveDO> letterReceivePage = selectPage(page, wrapper);
        return new PageResult<>(letterReceivePage.getRecords(), letterReceivePage.getTotal());
    }

    default List<LetterReceiveDO> selectList(LetterReceiveListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<LetterReceiveDO>()
                .eqIfPresent(LetterReceiveDO::getDataSources, reqVO.getDataSources())
                .eqIfPresent(LetterReceiveDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(LetterReceiveDO::getSendMailUser, reqVO.getSendMailUser())
                .eqIfPresent(LetterReceiveDO::getRelation, reqVO.getRelation())
                .eqIfPresent(LetterReceiveDO::getSendAddress, reqVO.getSendAddress())
                .betweenIfPresent(LetterReceiveDO::getSendDate, reqVO.getSendDate())
                .eqIfPresent(LetterReceiveDO::getSendPrison, reqVO.getSendPrison())
                .eqIfPresent(LetterReceiveDO::getMailNo, reqVO.getMailNo())
                .eqIfPresent(LetterReceiveDO::getMailUrl, reqVO.getMailUrl())
                .betweenIfPresent(LetterReceiveDO::getRegisterTime, reqVO.getRegisterTime())
                .eqIfPresent(LetterReceiveDO::getRegisterUserSfzh, reqVO.getRegisterUserSfzh())
                .likeIfPresent(LetterReceiveDO::getRegisterUserName, reqVO.getRegisterUserName())
                .eqIfPresent(LetterReceiveDO::getSignUrl, reqVO.getSignUrl())
                .eqIfPresent(LetterReceiveDO::getStatus, reqVO.getStatus())
                .eqIfPresent(LetterReceiveDO::getPassUser, reqVO.getPassUser())
                .betweenIfPresent(LetterReceiveDO::getPassTime, reqVO.getPassTime())
                .eqIfPresent(LetterReceiveDO::getPassRemark, reqVO.getPassRemark())
                .eqIfPresent(LetterReceiveDO::getGjApproverSfzh, reqVO.getGjApproverSfzh())
                .eqIfPresent(LetterReceiveDO::getGjApproverXm, reqVO.getGjApproverXm())
                .betweenIfPresent(LetterReceiveDO::getGjApproverTime, reqVO.getGjApproverTime())
                .eqIfPresent(LetterReceiveDO::getGjApprovalResult, reqVO.getGjApprovalResult())
                .eqIfPresent(LetterReceiveDO::getGjApprovalComments, reqVO.getGjApprovalComments())
                .eqIfPresent(LetterReceiveDO::getGroupApproverSfzh, reqVO.getGroupApproverSfzh())
                .eqIfPresent(LetterReceiveDO::getGroupApproverXm, reqVO.getGroupApproverXm())
                .betweenIfPresent(LetterReceiveDO::getGroupApproverTime, reqVO.getGroupApproverTime())
                .eqIfPresent(LetterReceiveDO::getGroupApprovalResult, reqVO.getGroupApprovalResult())
                .eqIfPresent(LetterReceiveDO::getGroupApprovalComments, reqVO.getGroupApprovalComments())
                .eqIfPresent(LetterReceiveDO::getLeaderApproverSfzh, reqVO.getLeaderApproverSfzh())
                .eqIfPresent(LetterReceiveDO::getLeaderApproverXm, reqVO.getLeaderApproverXm())
                .betweenIfPresent(LetterReceiveDO::getLeaderApproverTime, reqVO.getLeaderApproverTime())
                .eqIfPresent(LetterReceiveDO::getLeaderApprovalResult, reqVO.getLeaderApprovalResult())
                .eqIfPresent(LetterReceiveDO::getLeaderApprovalComments, reqVO.getLeaderApprovalComments())
                .eqIfPresent(LetterReceiveDO::getActInstId, reqVO.getActInstId())
                .eqIfPresent(LetterReceiveDO::getTaskId, reqVO.getTaskId())
                .orderByDesc(LetterReceiveDO::getAddTime));
    }


    default PageResult<LetterReceiveDO> getAppLetterReceivePage(int pageNo, int pageSize, String jgrybm, String type) {
        Page<LetterReceiveDO> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapperX<LetterReceiveDO> wrapper = new LambdaQueryWrapperX<LetterReceiveDO>()
                .eq(LetterReceiveDO::getJgrybm, jgrybm);
        if (LetterReceiveAppRecordTypeEnum.DQR.getCode().equals(type)) {
            wrapper.eq(LetterReceiveDO::getStatus, LetterReceiveStatusEnum.DQR.getCode());
        } else if (LetterReceiveAppRecordTypeEnum.YQR.getCode().equals(type)) {
            wrapper.eq(LetterReceiveDO::getStatus, LetterReceiveStatusEnum.YQR.getCode());
        }
        wrapper.orderByDesc(LetterReceiveDO::getSendDate);
        Page<LetterReceiveDO> letterReceivePage = selectPage(page, wrapper);
        return new PageResult<>(letterReceivePage.getRecords(), letterReceivePage.getTotal());
    }
}
