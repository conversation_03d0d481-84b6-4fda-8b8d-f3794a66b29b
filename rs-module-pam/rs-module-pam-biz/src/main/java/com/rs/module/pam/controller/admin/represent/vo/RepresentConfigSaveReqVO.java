package com.rs.module.pam.controller.admin.represent.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 监所事务管理-监室点名配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RepresentConfigSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("机构编号")
    private String orgCode;

    @ApiModelProperty("监室id")
    private String roomId;

    @ApiModelProperty("监室name")
    private String roomName;

    @ApiModelProperty("任务名称")
    private String taskName;

    @ApiModelProperty("状态(0：启用 1：未启用)")
    private Integer status;

    @ApiModelProperty("点名时间")
    private String startTime;

    @ApiModelProperty("有效期")
    private Integer expiryDate;

    @ApiModelProperty("计划周期（字典：ZD_JSDM_ZXZQLX）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JSDM_ZXZQLX")
    private String configPeriodCode;

    @ApiModelProperty("配置周期")
    private String configPeriod;

}
