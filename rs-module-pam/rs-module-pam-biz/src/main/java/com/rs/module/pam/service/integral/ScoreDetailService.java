package com.rs.module.pam.service.integral;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.entity.integral.ScoreDetailDO;

import java.util.List;
import java.util.Map;

/**
 * 监所事务管理-积分明细 Service 接口
 *
 * <AUTHOR>
 */
public interface ScoreDetailService extends IBaseService<ScoreDetailDO>{

    /**
     * 获取监所积分情况
     * @param orgCode
     * @return
     */
    Map<String, Object> getOrgScore(String orgCode, String roomCode);

    /**
     * 获取监室风险分布情况
     * @param orgCode
     * @return
     */
    List<Map<String, Object>> getSickRoomByOrg(String orgCode);

    /**
     * 获取人员风险分布情况
     * @param orgCode
     * @return
     */
    List<Map<String, Object>> getSickPrisonerByOrg(String orgCode, String roomCode);

}
