package com.rs.module.pam.dao.daily;

import java.util.*;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.entity.daily.LifeDO;
import com.rs.module.pam.entity.daily.LifeDictEventDO;
import com.rs.module.pam.enums.DailyLifeEventStatusEnum;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.pam.controller.admin.daily.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
 * 监所事务管理-事务选项字典 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface LifeDictEventDao extends IBaseDao<LifeDictEventDO> {


    default PageResult<LifeDictEventDO> selectPage(LifeDictEventPageReqVO reqVO) {
        Page<LifeDictEventDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<LifeDictEventDO> wrapper = new LambdaQueryWrapperX<LifeDictEventDO>()
                .likeIfPresent(LifeDictEventDO::getEventName, reqVO.getEventName())
                .eqIfPresent(LifeDictEventDO::getEventStatus, reqVO.getEventStatus());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(LifeDictEventDO::getAddTime);
        }
        Page<LifeDictEventDO> lifeDictEventPage = selectPage(page, wrapper);
        return new PageResult<>(lifeDictEventPage.getRecords(), lifeDictEventPage.getTotal());
    }

    default List<LifeDictEventDO> selectList(LifeDictEventListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<LifeDictEventDO>()
                .likeIfPresent(LifeDictEventDO::getEventName, reqVO.getEventName())
                .eq(LifeDictEventDO::getEventStatus, reqVO.getEventStatus())
                .eqIfPresent(LifeDictEventDO::getOrgCode, reqVO.getOrgCode())
                .eqIfPresent(LifeDictEventDO::getRegCode, reqVO.getRegCode())
                .eqIfPresent(LifeDictEventDO::getCityCode, reqVO.getCityCode())
                .orderByDesc(LifeDictEventDO::getAddTime));
    }


}
