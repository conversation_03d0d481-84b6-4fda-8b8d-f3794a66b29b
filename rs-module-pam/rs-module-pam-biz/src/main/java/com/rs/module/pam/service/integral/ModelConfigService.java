package com.rs.module.pam.service.integral;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.integral.vo.ModelConfigRespVO;
import com.rs.module.pam.controller.admin.integral.vo.ModelConfigSaveReqVO;
import com.rs.module.pam.entity.integral.ModelConfigDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 监所事务管理-积分模型配置 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelConfigService extends IBaseService<ModelConfigDO>{

    /**
     * 创建监所事务管理-积分模型配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    ModelConfigRespVO createModelConfig(@Valid ModelConfigSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-积分模型配置
     *
     * @param updateReqVO 更新信息
     */
    ModelConfigRespVO updateModelConfig(@Valid ModelConfigSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-积分模型配置
     *
     * @param id 编号
     */
    void deleteModelConfig(String id);

    /**
     * 获得监所事务管理-积分模型配置
     *
     * @param id 编号
     * @return 监所事务管理-积分模型配置
     */
    ModelConfigDO getModelConfig(String id);

    /**
     * 获取所有启动的积分模型
     * @return
     */
    List<ModelConfigDO> getAll();

}
