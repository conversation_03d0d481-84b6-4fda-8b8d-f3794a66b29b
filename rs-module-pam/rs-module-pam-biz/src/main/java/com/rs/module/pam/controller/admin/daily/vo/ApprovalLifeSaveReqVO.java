package com.rs.module.pam.controller.admin.daily.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.pam.entity.daily.LifeEventDO;
import com.rs.module.pam.entity.daily.LifeRoomDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-一日生活制度新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ApprovalLifeSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    @NotEmpty(message = "更新id不能为空")
    private String id;

    @ApiModelProperty("状态（字典：ZD_JSTX_CQDHSPZ）")
    @NotEmpty(message = "状态（字典：ZD_JSTX_CQDHSPZ）不能为空")
    private String status;

    @ApiModelProperty("领导签名")
    private String approvalAutograph;


    @ApiModelProperty("审核意见")
    private String approvalComments;

}
