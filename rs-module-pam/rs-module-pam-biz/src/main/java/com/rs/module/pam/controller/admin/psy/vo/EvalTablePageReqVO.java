package com.rs.module.pam.controller.admin.psy.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评量表管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EvalTablePageReqVO extends PageParam{

    @ApiModelProperty("量表名称")
    private String name;

    @ApiModelProperty("量表类型（字典：ZD_XLPC_LBLX）")
    private String tableType;

    @ApiModelProperty("使用状态（字典：ZD_PSY_SYZT）")
    private String usageStatus;

    @ApiModelProperty("监所编码")
    private String orgCode;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
