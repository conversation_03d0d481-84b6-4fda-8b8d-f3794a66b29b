package com.rs.module.pam.controller.admin.duty.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-监室值班列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DutyListRespVO extends BaseVO {

    @ApiModelProperty("值班日期")
    private Date dutyDate;

    @ApiModelProperty("人员信息")
    private List<DutyInfoRespVO> dutyInfo;

}
