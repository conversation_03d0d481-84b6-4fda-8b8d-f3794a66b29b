package com.rs.module.pam.controller.admin.represent.vo.extra;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * room_represent-正在点名人员信息vo
 *
 */
@Data
@ApiModel("正在点名人员信息vo")
public class InRepresentPersonVO {
    /**
     * 人员id
     */
    @ApiModelProperty(value = "人员id")
    private String prisonerId;
    /**
     * 人员name
     */
    @ApiModelProperty(value = "人员name")
    private String prisonerName;

    @ApiModelProperty(value = "监管人员编码")
    private String jgrybm;
    /**
     * 照片
     */
    @ApiModelProperty(value = "照片")
    private String frontPhoto;

    @ApiModelProperty("1-已签到")
    private String signStatus;

    @ApiModelProperty("1-外出")
    private String isOut;

    @ApiModelProperty("签到时间")
    private Date signTime;

    @ApiModelProperty("温度")
    private String temperature;

    @ApiModelProperty("外出原因")
    private String outReason;

}
