package com.rs.module.pam.entity.duty;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import java.util.Date;

/**
 * 监所事务管理-监室值班 DO
 *
 * <AUTHOR>
 */
@TableName("pam_duty")
@KeySequence("pam_duty_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DutyDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 值班日期
     */
    private Date dutyDate;
    /**
     * 班次数
     */
    private Short shiftNumber;
    /**
     * 排班人ID
     */
    private String assignerUserSfzh;
    /**
     * 排班人名称
     */
    private String assignerUserName;
    /**
     * 排班时间
     */
    private Date assignedTime;

}
