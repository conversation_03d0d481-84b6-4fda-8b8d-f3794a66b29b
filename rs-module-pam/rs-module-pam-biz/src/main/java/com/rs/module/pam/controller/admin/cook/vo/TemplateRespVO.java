package com.rs.module.pam.controller.admin.cook.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-菜品模板 Response VO")
@Data
public class TemplateRespVO {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("模板名称")
    private String templateName;
    @ApiModelProperty("模板描述")
    private List<TemplateSaveReqVO.CookMeal> cookMeals;
    @ApiModelProperty("模板内容")
    private List<TemplateSaveReqVO.CookSub> cookSubs;
    @ApiModelProperty("菜谱开始日期")
    private Date startTime;
    @ApiModelProperty("菜谱截至日期")
    private Date endTime;
    @ApiModelProperty("周编号 比如1月第一周 20230101")
    private String weekNo;
    @ApiModelProperty("是否复制")
    private String isCopy;
    @ApiModelProperty("复制来源ID")
    private String copyId;
    @ApiModelProperty("安排人")
    private String addUserName;
    @ApiModelProperty("安排时间")
    private Date addTime;
    @ApiModelProperty("复用模版名称")
    private String copyTemplateName;



}
