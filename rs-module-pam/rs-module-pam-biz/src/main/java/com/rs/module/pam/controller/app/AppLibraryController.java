package com.rs.module.pam.controller.app;

import cn.hutool.core.lang.Assert;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.device.vo.DeviceRepairRegPageReqVO;
import com.rs.module.pam.controller.admin.device.vo.DeviceRepairRegRespVO;
import com.rs.module.pam.controller.admin.library.vo.LibraryBorrowingRespVO;
import com.rs.module.pam.controller.admin.library.vo.LibraryMgtRespVO;
import com.rs.module.pam.controller.app.vo.AppLibraryBorrowingSaveReqVO;
import com.rs.module.pam.entity.device.DeviceRepairRegDO;
import com.rs.module.pam.entity.library.LibraryBorrowingDO;
import com.rs.module.pam.entity.library.LibraryMgtDO;
import com.rs.module.pam.service.library.LibraryBorrowingService;
import com.rs.module.pam.service.library.LibraryMgtService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监所事务管理-图书借阅申请")
@RestController
@RequestMapping("/app/pam/library")
@Validated
public class AppLibraryController {

    @Resource
    private LibraryBorrowingService libraryBorrowingService;

    @Resource
    private LibraryMgtService libraryMgtService;

    @PostMapping("/borrowing/create")
    @ApiOperation(value = "App-图书借阅申请")
    public CommonResult<String> createLibraryBorrowing(@Valid @RequestBody AppLibraryBorrowingSaveReqVO createReqVO) {
        return success(libraryBorrowingService.appCreateLibraryBorrowing(createReqVO));
    }

    @GetMapping("/borrowing/record-page")
    @ApiOperation(value = "App-个人借阅记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小"),
            @ApiImplicitParam(name = "jgrybm", value = "被监管人员编码"),
            @ApiImplicitParam(name = "type", value = "借阅周期类型 1 全部，2 今天，3 昨天，4 近一周")
    })
    public CommonResult<PageResult<LibraryBorrowingRespVO>> personalBorrowingRecordPage(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                                                        @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                                                        @RequestParam("jgrybm") String jgrybm,
                                                                                        @RequestParam("type") String type) {
        PageResult<LibraryBorrowingDO> pageResult = libraryBorrowingService.personalBorrowingRecordPage(pageNo, pageSize, jgrybm, type);
        return success(BeanUtils.toBean(pageResult, LibraryBorrowingRespVO.class));
    }

    @GetMapping("/mgt/library-page")
    @ApiOperation(value = "App-图书分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小")
    })
    public CommonResult<PageResult<LibraryMgtRespVO>> getAppLibraryMgtPage(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                                           @RequestParam(name = "pageSize", defaultValue = "10") int pageSize) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        PageResult<LibraryMgtDO> pageResult = libraryMgtService.getAppLibraryMgtPage(pageNo, pageSize, sessionUser.getOrgCode());
        return success(BeanUtils.toBean(pageResult, LibraryMgtRespVO.class));
    }

}
