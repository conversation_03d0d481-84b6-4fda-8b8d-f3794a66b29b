package com.rs.module.pam.dao.info;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.info.vo.ReportItemListReqVO;
import com.rs.module.pam.entity.info.ReportItemDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 监所事务管理-信息报备事项配置 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface ReportItemDao extends IBaseDao<ReportItemDO> {

    default List<ReportItemDO> selectList(ReportItemListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ReportItemDO>()
                .eqIfPresent(ReportItemDO::getReportType, reqVO.getReportType())
                .eqIfPresent(ReportItemDO::getReportContent, reqVO.getReportContent())
                .eqIfPresent(ReportItemDO::getOrgCode, reqVO.getOrgCode())
                .eqIfPresent(ReportItemDO::getStatus, reqVO.getStatus())
                .orderByDesc(ReportItemDO::getAddTime));
    }


}
