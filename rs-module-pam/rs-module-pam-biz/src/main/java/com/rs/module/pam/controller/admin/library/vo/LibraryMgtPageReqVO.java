package com.rs.module.pam.controller.admin.library.vo;

import io.swagger.annotations.ApiModel;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;

import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-图书管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LibraryMgtPageReqVO extends PageParam {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("图书名称")
    private String name;

    @ApiModelProperty("作者")
    private String author;

    @ApiModelProperty("出版社")
    private String press;

    @ApiModelProperty("总库存")
    private Integer totalInventory;

    @ApiModelProperty("可借用库存")
    private Integer borrowedInventory;

    @ApiModelProperty("封面图片URL")
    private String coverImgUrl;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("上架状态（1：已上架，0：已下架）")
    private String status;

    @ApiModelProperty("排序属性")
    List<OrderItem> orderFields;

    @ApiModelProperty("机构码")
    private String orgCode;

    @ApiModelProperty("县分局码")
    private String regCode;

    @ApiModelProperty("市局码")
    private String cityCode;

}
