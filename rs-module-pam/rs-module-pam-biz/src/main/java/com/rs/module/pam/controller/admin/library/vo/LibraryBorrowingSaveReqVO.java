package com.rs.module.pam.controller.admin.library.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-图书借阅申请新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LibraryBorrowingSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String ryxm;

    @ApiModelProperty("监室id")
    @NotEmpty(message = "监室id不能为空")
    private String roomId;

    @ApiModelProperty("监室名称")
    @NotEmpty(message = "监室名称不能为空")
    private String roomName;

    @ApiModelProperty("图书id")
    @NotEmpty(message = "图书id不能为空")
    private String libraryId;

    @ApiModelProperty("申请时间")
    @NotNull(message = "申请时间不能为空")
    private Date applyTime;

    @ApiModelProperty("借阅截止时间")
    private Date borrowingDeadlineTime;

    @ApiModelProperty("发放时间")
    private Date distributionTime;

    @ApiModelProperty("归还时间")
    private Date returnTime;

    @ApiModelProperty("借阅状态（字典：ZD_TSJY_JYZT）")
    @NotEmpty(message = "借阅状态（字典：ZD_TSJY_JYZT）不能为空")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

}
