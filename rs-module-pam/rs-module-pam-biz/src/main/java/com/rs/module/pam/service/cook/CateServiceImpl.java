package com.rs.module.pam.service.cook;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.cook.vo.CateListReqVO;
import com.rs.module.pam.controller.admin.cook.vo.CateSaveReqVO;
import com.rs.module.pam.dao.cook.CateDao;
import com.rs.module.pam.dao.cook.ManageDao;
import com.rs.module.pam.entity.cook.CateDO;
import com.rs.module.pam.entity.cook.ManageDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 监所事务管理-菜品分类 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CateServiceImpl extends BaseServiceImpl<CateDao, CateDO> implements CateService {

    @Resource
    private CateDao cateDao;
    @Resource
    private ManageDao manageDao;

    @Override
    public String createCate(CateSaveReqVO createReqVO) {
        // 校验重复
        validateCateExists(createReqVO);
        // 插入
        CateDO cate = BeanUtils.toBean(createReqVO, CateDO.class);
        cateDao.insert(cate);
        // 返回
        return cate.getId();
    }

    @Override
    public void updateCate(CateSaveReqVO updateReqVO) {
        // 校验存在
        validateCateExists(updateReqVO);
        // 更新
        CateDO updateObj = BeanUtils.toBean(updateReqVO, CateDO.class);
        cateDao.updateById(updateObj);
    }

    private void validateCateExists(CateSaveReqVO reqVO) {
        // 校验重复
        LambdaQueryWrapper<CateDO> lambdaQuery = Wrappers.lambdaQuery(CateDO.class);
        lambdaQuery.eq(CateDO::getCateName, reqVO.getCateName())
                .eq(CateDO::getOrgCode, SessionUserUtil.getSessionUser().getOrgCode());
        Integer count = cateDao.selectCount(lambdaQuery);
        if (count != null && count > 0) {
            throw new ServerException("菜品分类已存在");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCate(String id) {
        // 校验存在
        validateCateExists(id);
        // 删除
        cateDao.deleteById(id);
        // 级联删除菜品
        manageDao.delete(ManageDO::getCateId, id);
    }

    private void validateCateExists(String id) {
        if (cateDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-菜品分类数据不存在");
        }
    }

    @Override
    public CateDO getCate(String id) {
        return cateDao.selectById(id);
    }

    @Override
    public List<CateDO> getCateList(CateListReqVO listReqVO) {
        return cateDao.selectList(listReqVO);
    }


}
