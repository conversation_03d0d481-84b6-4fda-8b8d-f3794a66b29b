package com.rs.module.pam.controller.admin.family.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-亲情电话分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FamilyPhonePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("数据来源（字典：ZD_DATA_SOURCES）")
    private String dataSources;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("联系对象")
    private String telObject;

    @ApiModelProperty("与通话人员关系")
    private String relName;

    @ApiModelProperty("电话号码")
    private String telNum;

    @ApiModelProperty("申请时间")
    private Date[] applyTime;

    @ApiModelProperty("通话日期")
    private Date[] telTime;

    @ApiModelProperty("经办人身份证号")
    private String handlerUserSfzh;

    @ApiModelProperty("经办人")
    private String handlerUserName;

    @ApiModelProperty("经办时间")
    private Date[] handlerTime;

    @ApiModelProperty("报备状态（字典：ZD_XXBB_BBZT）")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date[] approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
