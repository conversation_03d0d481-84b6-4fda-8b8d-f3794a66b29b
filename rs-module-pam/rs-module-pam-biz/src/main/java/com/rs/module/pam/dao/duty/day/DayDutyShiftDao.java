package com.rs.module.pam.dao.duty.day;

import cn.hutool.core.date.DateUtil;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.entity.duty.day.DayDutyShiftDO;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
* 监所事务管理-值日班次 Dao
*
* <AUTHOR>
*/
@Mapper
public interface DayDutyShiftDao extends IBaseDao<DayDutyShiftDO> {

    /**
     * 获取机构下指定监室指定时间段内的以及值日排班信息
     *
     * @param orgCode
     * @param roomId
     * @param startDate
     * @param endDate
     * @return
     */
    List<DayDutyShiftDO> selectShiftByEffectiveDate(@Param("orgCode") String orgCode, @Param("roomId") String roomId,
                                             @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 获取机构下指定监室未失效的值日排班信息
     *
     * @param orgCode
     * @param roomId
     * @return
     */
    default List<DayDutyShiftDO> selectList(String orgCode, String roomId) {
        return selectList(new LambdaQueryWrapperX<DayDutyShiftDO>()
                .eq(DayDutyShiftDO::getOrgCode, orgCode)
                .eqIfPresent(DayDutyShiftDO::getRoomId, roomId)
                .isNull(DayDutyShiftDO::getEffectiveEndDate));
    }

    default List<DayDutyShiftDO> selectList(String orgCode, String roomId, List<String> ids) {
        return selectList(new LambdaQueryWrapperX<DayDutyShiftDO>()
                .eq(DayDutyShiftDO::getOrgCode, orgCode)
                .eq(DayDutyShiftDO::getRoomId, roomId)
                .in(DayDutyShiftDO::getId, ids));
    }

    /**
     * 获取机构下指定监室有效的排班信息
     *
     * @param orgCode
     * @param roomId
     * @return
     */
    default List<DayDutyShiftDO> getEffectiveShift(String orgCode, String roomId) {
        return selectList(new LambdaQueryWrapperX<DayDutyShiftDO>()
                .eq(DayDutyShiftDO::getOrgCode, orgCode)
                .eqIfPresent(DayDutyShiftDO::getRoomId, roomId)
                .isNull(DayDutyShiftDO::getEffectiveEndDate));
    }

    default List<DayDutyShiftDO> createDefaultShift(String orgCode, String roomId) {
        Date now = new Date();
        DayDutyShiftDO shift1 = new DayDutyShiftDO();
        shift1.setOrgCode(orgCode);
        shift1.setRoomId(roomId);
        shift1.setEffectiveStartDate(DateUtil.beginOfDay(DateUtil.beginOfYear(now)));
        DayDutyShiftDO shift2 = (DayDutyShiftDO) SerializationUtils.clone((Serializable) shift1);
        DayDutyShiftDO shift3 = (DayDutyShiftDO)SerializationUtils.clone((Serializable) shift1);
        DayDutyShiftDO shift4 = (DayDutyShiftDO)SerializationUtils.clone((Serializable) shift1);
        DayDutyShiftDO shift5 = (DayDutyShiftDO)SerializationUtils.clone((Serializable) shift1);
        shift1.setShiftName("日常清扫");
        shift1.setSort(1);
        shift2.setShiftName("垃圾处理");
        shift2.setSort(2);
        shift3.setShiftName("作息提醒");
        shift3.setSort(3);
        shift4.setShiftName("饮食管理");
        shift4.setSort(4);
        shift5.setShiftName("值日内务");
        shift5.setSort(5);

        List<DayDutyShiftDO> list = new ArrayList<>();
        list.add(shift1);
        list.add(shift2);
        list.add(shift3);
        list.add(shift4);
        list.add(shift5);
        insertBatch(list);
        return list;
    }

}
