package com.rs.module.pam.controller.admin.duty.vo;

import com.rs.module.base.controller.admin.pm.vo.PrisonerListVwRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-监室值班详情 Response VO")
@Data
public class DutyInfoRespVO {

    private String id;

    @ApiModelProperty("班次ID")
    private String shiftId;

    @ApiModelProperty("值班日期")
    private Date dutyDate;

    @ApiModelProperty("班次类型(0:之前, 1:开始, 2:未来)")
    private String shiftType;

    @ApiModelProperty("是否为值班组")
    private Boolean isGroup;

    @ApiModelProperty("值班组ID")
    private String groupId;

    @ApiModelProperty("值班组名称")
    private String groupName;

    @ApiModelProperty("值班组名称")
    private String name;

    @ApiModelProperty("值班人员信息")
    private List<DutyPrisonerVO> prisonerList;

}
