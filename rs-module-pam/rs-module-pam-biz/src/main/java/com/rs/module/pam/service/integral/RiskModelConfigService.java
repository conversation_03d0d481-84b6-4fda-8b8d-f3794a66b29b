package com.rs.module.pam.service.integral;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.integral.vo.RiskModelConfigDTO;
import com.rs.module.pam.controller.admin.integral.vo.RiskModelConfigSaveReqVO;
import com.rs.module.pam.entity.integral.RiskModelConfigDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 监所事务管理-风险模型 Service 接口
 *
 * <AUTHOR>
 */
public interface RiskModelConfigService extends IBaseService<RiskModelConfigDO>{

    /**
     * 创建监所事务管理-风险模型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    RiskModelConfigDTO createRiskModelConfig(@Valid RiskModelConfigSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-风险模型
     *
     * @param updateReqVO 更新信息
     */
    RiskModelConfigDTO updateRiskModelConfig(@Valid RiskModelConfigDTO updateReqVO);

    /**
     * 删除监所事务管理-风险模型
     *
     * @param id 编号
     */
    void deleteRiskModelConfig(String id);

    /**
     * 获得监所事务管理-风险模型
     *
     * @param id 编号
     * @return 监所事务管理-风险模型
     */
    RiskModelConfigDO getRiskModelConfig(String id);

    /**
     * 获得监所事务管理-风险模型
     *
     * @param orgCode 机构编号
     * @return 监所事务管理-风险模型
     */
    List<RiskModelConfigDO> getRiskModelByOrgCode(String orgCode);


}
