package com.rs.module.pam.controller.admin.daily.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@ApiModel(description = "管理后台 - 监所事务管理-日常清监检查项配置新增/修改 Request VO")
@Data
public class CleanConfigUpdateReqVO {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("检查类型（字典：ZD_AQJCGL_JCLX）")
    @NotBlank(message = "检查类型不能为空")
    private String checkType;

    @ApiModelProperty("检查项(字典：ZD_AQJCGL_JCX)")
    @NotBlank(message = "检查项不能为空")
    private String checkItem;

}
