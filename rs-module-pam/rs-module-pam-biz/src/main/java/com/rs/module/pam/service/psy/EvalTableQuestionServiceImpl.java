package com.rs.module.pam.service.psy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableOptionRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableOptionSaveReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableQuestionListReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableQuestionOrderReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableQuestionPageReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableQuestionRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableQuestionSaveReqVO;
import com.rs.module.pam.dao.psy.EvalTableQuestionDao;
import com.rs.module.pam.entity.psy.EvalTableDO;
import com.rs.module.pam.entity.psy.EvalTableOptionDO;
import com.rs.module.pam.entity.psy.EvalTableQuestionDO;
import com.rs.module.pam.enums.PsyQuestionTypeEnum;
import com.rs.module.pam.enums.PsyUsageStatusEnum;
import groovy.lang.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 监所事务管理-心理测评量表关联题目 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EvalTableQuestionServiceImpl extends BaseServiceImpl<EvalTableQuestionDao, EvalTableQuestionDO> implements EvalTableQuestionService {

    @Resource
    private EvalTableQuestionDao evalTableQuestionDao;
    @Resource
    @Lazy
    private EvalTableService evalTableService;
    @Resource
    private EvalTableOptionService evalTableOptionService;
    @Resource
    @Lazy
    private EvalPlanService evalPlanService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createEvalTableQuestion(EvalTableQuestionSaveReqVO createReqVO) {
        String tableId = createReqVO.getTableId();
        EvalTableDO evalTableDO = evalTableService.getById(tableId);
        if (evalTableDO == null) {
            throw new ServerException("心理测评量表不存在");
        }
        if (!PsyUsageStatusEnum.WWC.getCode().equals(evalTableDO.getUsageStatus())) {
            if (evalPlanService.checkPlanByTableId(tableId)) {
                throw new ServerException("该量表已被使用，不能添加题目");
            }
        }
        EvalTableQuestionDO evalTableQuestion = BeanUtils.toBean(createReqVO, EvalTableQuestionDO.class);
        if (StrUtil.isNotBlank(evalTableQuestion.getId())) {
            evalTableQuestion.setSortOrder(null);
            evalTableQuestionDao.updateById(evalTableQuestion);
        } else {
            Integer sortOrder = evalTableQuestionDao.getMaxSortByTableId(tableId);
            if (sortOrder == null) {
                evalTableQuestion.setSortOrder(1);
            } else {
                evalTableQuestion.setSortOrder(sortOrder + 1);
            }
            evalTableQuestionDao.insert(evalTableQuestion);
        }
        List<EvalTableOptionSaveReqVO> options = createReqVO.getOptions();
        if (CollUtil.isEmpty(options) && !PsyQuestionTypeEnum.Answer.getCode().equals(createReqVO.getQuestionType())) {
            throw new ServerException("请添加题目选项");
        }
        List<EvalTableOptionDO> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(options)) {
            for (EvalTableOptionSaveReqVO option : options) {
                EvalTableOptionDO optionDO = BeanUtils.toBean(option, EvalTableOptionDO.class);
                optionDO.setTableId(tableId);
                optionDO.setQuestionId(evalTableQuestion.getId());
                result.add(optionDO);
            }
            evalTableOptionService.deleteEvalTableOptionByQuestionId(evalTableQuestion.getId());
            // 插入
            evalTableOptionService.saveBatch(result);
        }
        // 更新量表题目总数
        Integer count = evalTableQuestionDao.selectCount(EvalTableQuestionDO::getTableId, tableId);
        evalTableService.updateTablTtotalQuestionNumber(tableId, count);
        return evalTableQuestion.getId();
    }

    @Override
    public void updateEvalTableQuestion(EvalTableQuestionSaveReqVO updateReqVO) {
        // 校验存在
        validateEvalTableQuestionExists(updateReqVO.getId());
        // 更新
        EvalTableQuestionDO updateObj = BeanUtils.toBean(updateReqVO, EvalTableQuestionDO.class);
        evalTableQuestionDao.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteEvalTableQuestion(String id) {
        EvalTableQuestionDO tableQuestionDO = evalTableQuestionDao.selectById(id);
        if (tableQuestionDO == null) {
            throw new ServerException("监所事务管理-心理测评量表关联题目数据不存在");
        }
        // 删除
        evalTableQuestionDao.deleteEvalTableQuestionDOById(id);
        evalTableOptionService.deleteEvalTableOptionByQuestionId(id);
        // 更新量表题目总数
        Integer count = evalTableQuestionDao.selectCount(EvalTableQuestionDO::getTableId, tableQuestionDO.getTableId());
        evalTableService.updateTablTtotalQuestionNumber(tableQuestionDO.getTableId(), count);
        // 更新排序
        evalTableQuestionDao.incrementOrderNumbers(tableQuestionDO.getTableId(), tableQuestionDO.getSortOrder());

    }

    private void validateEvalTableQuestionExists(String id) {
        if (evalTableQuestionDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-心理测评量表关联题目数据不存在");
        }
    }

    @Override
    public EvalTableQuestionDO getEvalTableQuestion(String id) {
        return evalTableQuestionDao.selectById(id);
    }

    @Override
    public PageResult<EvalTableQuestionRespVO> getEvalTableQuestionPage(EvalTableQuestionPageReqVO pageReqVO) {
        PageResult<EvalTableQuestionDO> pageResult = evalTableQuestionDao.selectPage(pageReqVO);
        PageResult<EvalTableQuestionRespVO> result = new PageResult<>();
        List<EvalTableQuestionRespVO> list = new ArrayList<>();
        List<EvalTableQuestionDO> pageResultList = pageResult.getList();
        if (CollUtil.isNotEmpty(pageResultList)) {
            List<String> quesionIds = pageResultList.stream().map(EvalTableQuestionDO::getId).collect(Collectors.toList());
            List<EvalTableOptionRespVO> options = evalTableOptionService.getListByQuestionId(quesionIds);
            Map<String, List<EvalTableOptionRespVO>> listMap = options.stream().collect(Collectors.groupingBy(EvalTableOptionRespVO::getQuestionId));
            for (EvalTableQuestionDO questionDO : pageResultList) {
                EvalTableQuestionRespVO questionRespVO = BeanUtils.toBean(questionDO, EvalTableQuestionRespVO.class);
                questionRespVO.setOptions(listMap.get(questionDO.getId()));
                list.add(questionRespVO);
            }
        }
        result.setTotal(pageResult.getTotal());
        result.setList(list);
        return result;
    }

    @Override
    public List<EvalTableQuestionDO> getEvalTableQuestionList(EvalTableQuestionListReqVO listReqVO) {
        return evalTableQuestionDao.selectList(listReqVO);
    }

    @Override
    public EvalTableQuestionRespVO getEvalTableQuestionRespVO(String id) {
        EvalTableQuestionDO questionDO = evalTableQuestionDao.selectById(id);
        if (questionDO == null) {
            throw new ServerException("监所事务管理-心理测评量表关联题目数据不存在");
        }
        EvalTableQuestionRespVO respVO = BeanUtils.toBean(questionDO, EvalTableQuestionRespVO.class);
        List<EvalTableOptionRespVO> options = evalTableOptionService.getListByQuestionId(questionDO.getId());
        respVO.setOptions(options);
        return respVO;
    }

    @Override
    public List<EvalTableQuestionRespVO> getEvalTableQuestionListRespVO(EvalTableQuestionListReqVO listReqVO) {
        List<EvalTableQuestionRespVO> questionRespVOS = evalTableQuestionDao.getEvalTableQuestionListRespVO(listReqVO);
        return questionRespVOS;
    }

    @Override
    public Map<String, EvalTableQuestionRespVO> getEvalTableQuestionMap(String tableId) {
        EvalTableQuestionListReqVO listReqVO = new EvalTableQuestionListReqVO();
        listReqVO.setTableId(tableId);
        List<EvalTableQuestionRespVO> questionRespVOS = evalTableQuestionDao.getEvalTableQuestionListRespVO(listReqVO);
        if (CollUtil.isNotEmpty(questionRespVOS)) {
            return questionRespVOS.stream()
                    .collect(Collectors.toMap(EvalTableQuestionRespVO::getId, Function.identity()));
        }
        return Collections.emptyMap();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void questionOrder(List<EvalTableQuestionOrderReqVO> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<EvalTableQuestionDO> result = new ArrayList<>();
        for (EvalTableQuestionOrderReqVO reqVO : list) {
            EvalTableQuestionDO evalTableQuestionDO = new EvalTableQuestionDO();
            evalTableQuestionDO.setId(reqVO.getQuestionId());
            evalTableQuestionDO.setSortOrder(reqVO.getSortOrder());
            result.add(evalTableQuestionDO);
        }
        evalTableQuestionDao.updateBatch(result);
    }


}
