package com.rs.module.pam.dao.daily;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.entity.daily.LifeDO;
import com.rs.module.pam.entity.daily.LifeHolidaysDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.pam.controller.admin.daily.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 监所事务管理-一日生活制度 Dao
*
* <AUTHOR>
*/
@Mapper
public interface LifeDao extends IBaseDao<LifeDO> {


    default PageResult<LifeDO> selectPage(LifePageReqVO reqVO) {
        Page<LifeDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<LifeDO> wrapper = new LambdaQueryWrapperX<LifeDO>()
            .likeIfPresent(LifeDO::getName, reqVO.getName())
            .eqIfPresent(LifeDO::getCycleSetting, reqVO.getCycleSetting())
            .eqIfPresent(LifeDO::getCycleConfig, reqVO.getCycleConfig())
            .betweenIfPresent(LifeDO::getCustomizeTime, reqVO.getCustomizeTime())
            .eqIfPresent(LifeDO::getStatus, reqVO.getStatus())
            .eqIfPresent(LifeDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(LifeDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(LifeDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(LifeDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(LifeDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(LifeDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(LifeDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(LifeDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(LifeDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(LifeDO::getAddTime);
        }
        Page<LifeDO> lifePage = selectPage(page, wrapper);
        return new PageResult<>(lifePage.getRecords(), lifePage.getTotal());
    }
    default List<LifeDO> selectList(LifeListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<LifeDO>()
            .likeIfPresent(LifeDO::getName, reqVO.getName())
            .eqIfPresent(LifeDO::getCycleSetting, reqVO.getCycleSetting())
            .eqIfPresent(LifeDO::getCycleConfig, reqVO.getCycleConfig())
            .betweenIfPresent(LifeDO::getCustomizeTime, reqVO.getCustomizeTime())
            .eqIfPresent(LifeDO::getStatus, reqVO.getStatus())
            .eqIfPresent(LifeDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(LifeDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(LifeDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(LifeDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(LifeDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(LifeDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(LifeDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(LifeDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(LifeDO::getTaskId, reqVO.getTaskId())
            .eqIfPresent(LifeDO::getOrgCode, reqVO.getOrgCode())
            .eqIfPresent(LifeDO::getRegCode, reqVO.getRegCode())
            .eqIfPresent(LifeDO::getCityCode, reqVO.getCityCode())
        .orderByDesc(LifeDO::getAddTime));    }

    List<LifeDO> selectLifeListByRoomId(@Param("roomId") String roomId);
}
