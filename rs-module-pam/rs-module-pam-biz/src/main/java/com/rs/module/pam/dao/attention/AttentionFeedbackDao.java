package com.rs.module.pam.dao.attention;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.entity.attention.FeedbackDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 监所事务管理-重点人员关注反馈
 *
 * <AUTHOR>
 */
@Mapper
public interface AttentionFeedbackDao extends IBaseDao<FeedbackDO> {

    default List<FeedbackDO> selectListByAttentionId(String attentionId) {
        return selectList(new LambdaQueryWrapperX<FeedbackDO>()
                .eq(FeedbackDO::getAttentionId, attentionId)
                .orderByDesc(FeedbackDO::getAddTime));
    }

    default int deleteByAttentionId(String attentionId) {
        return delete(new LambdaQueryWrapperX<FeedbackDO>().eq(FeedbackDO::getAttentionId, attentionId));
    }

}
