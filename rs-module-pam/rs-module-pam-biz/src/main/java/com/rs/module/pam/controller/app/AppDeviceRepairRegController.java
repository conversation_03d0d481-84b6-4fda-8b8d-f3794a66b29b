package com.rs.module.pam.controller.app;

import cn.hutool.core.lang.Assert;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.enums.DataSourceAppEnum;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.device.vo.DeviceRepairRegListReqVO;
import com.rs.module.pam.controller.admin.device.vo.DeviceRepairRegPageReqVO;
import com.rs.module.pam.controller.admin.device.vo.DeviceRepairRegRespVO;
import com.rs.module.pam.controller.admin.device.vo.DeviceRepairRegSaveReqVO;
import com.rs.module.pam.entity.device.DeviceRepairRegDO;
import com.rs.module.pam.enums.DeviceRepairRegStatusEnum;
import com.rs.module.pam.service.device.DeviceRepairRegService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监所事务管理-设备报修登记")
@RestController
@RequestMapping("/app/pam/device/deviceRepairReg")
@Validated
public class AppDeviceRepairRegController {

    @Resource
    private DeviceRepairRegService deviceRepairRegService;

    @PostMapping("/cnp/create")
    @ApiOperation(value = "APP-仓内屏-设备报修登记")
    public CommonResult<String> cnpCreateDeviceRepairReg(@Valid @RequestBody DeviceRepairRegSaveReqVO createReqVO) {
        createReqVO.setDataSources(DataSourceAppEnum.CNP.getCode());
        // 仓内屏新增 状态直接 待派单
        createReqVO.setStatus(DeviceRepairRegStatusEnum.DPD.getCode());
        return success(deviceRepairRegService.createDeviceRepairReg(createReqVO));
    }

    @PostMapping("/cwp/create")
    @ApiOperation(value = "APP-仓外屏-设备报修登记")
    public CommonResult<String> cwpCreateDeviceRepairReg(@Valid @RequestBody DeviceRepairRegSaveReqVO createReqVO) {
        // 仓外屏新增 状态直接 待维修
        createReqVO.setStatus(DeviceRepairRegStatusEnum.DWX.getCode());
        createReqVO.setDataSources(DataSourceAppEnum.CWP.getCode());
        return success(deviceRepairRegService.createDeviceRepairReg(createReqVO));
    }




    @GetMapping("/page")
    @ApiOperation(value = "APP-设备报修记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true),
            @ApiImplicitParam(name = "type", value = "记录周期类型 1 全部，2 今天，3 昨天，4 近一周", required = true)
    })
    public CommonResult<PageResult<DeviceRepairRegRespVO>> getDeviceRepairRegRecordPage(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                                                  @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                                                  @RequestParam("type") String type) {
        Assert.notBlank(type, "type不能为空");
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        PageResult<DeviceRepairRegDO> pageResult = deviceRepairRegService.getDeviceRepairRegRecordPage(pageNo, pageSize, sessionUser.getIdCard(), type);
        return success(BeanUtils.toBean(pageResult, DeviceRepairRegRespVO.class));
    }


}
