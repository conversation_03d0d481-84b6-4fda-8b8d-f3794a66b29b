package com.rs.module.pam.entity.screen;

import com.rs.module.base.entity.pm.PrisonerTagDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class FocusOnPersonnelDO {

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("人员姓名")
    private String xm;

    @ApiModelProperty("头像")
    private String frontPhoto;

    @ApiModelProperty("监室号")
    private String jsh;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("诉讼环节")
    private String sshj;

    @ApiModelProperty("涉嫌罪名")
    private String sxzm;

    @ApiModelProperty("关押期限")
    private Date gyqx;

    @ApiModelProperty("人员标签")
    public List<PrisonerTagDO> tagList;
}
