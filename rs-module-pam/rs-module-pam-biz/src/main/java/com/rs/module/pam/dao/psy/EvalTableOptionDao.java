package com.rs.module.pam.dao.psy;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.entity.psy.EvalTableOptionDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.pam.controller.admin.psy.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 监所事务管理-心理测评量表关联提目选项 Dao
*
* <AUTHOR>
*/
@Mapper
public interface EvalTableOptionDao extends IBaseDao<EvalTableOptionDO> {


    default PageResult<EvalTableOptionDO> selectPage(EvalTableOptionPageReqVO reqVO) {
        Page<EvalTableOptionDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<EvalTableOptionDO> wrapper = new LambdaQueryWrapperX<EvalTableOptionDO>()
            .eqIfPresent(EvalTableOptionDO::getTableId, reqVO.getTableId())
            .eqIfPresent(EvalTableOptionDO::getQuestionId, reqVO.getQuestionId())
            .eqIfPresent(EvalTableOptionDO::getOptionCode, reqVO.getOptionCode())
            .eqIfPresent(EvalTableOptionDO::getOptionText, reqVO.getOptionText())
            .eqIfPresent(EvalTableOptionDO::getScore, reqVO.getScore())
            .eqIfPresent(EvalTableOptionDO::getSortOrder, reqVO.getSortOrder());
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(EvalTableOptionDO::getAddTime);
        }
        Page<EvalTableOptionDO> evalTableOptionPage = selectPage(page, wrapper);
        return new PageResult<>(evalTableOptionPage.getRecords(), evalTableOptionPage.getTotal());
    }

    default List<EvalTableOptionDO> selectList(EvalTableOptionListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<EvalTableOptionDO>()
            .eqIfPresent(EvalTableOptionDO::getTableId, reqVO.getTableId())
            .eqIfPresent(EvalTableOptionDO::getQuestionId, reqVO.getQuestionId())
            .eqIfPresent(EvalTableOptionDO::getOptionCode, reqVO.getOptionCode())
            .eqIfPresent(EvalTableOptionDO::getOptionText, reqVO.getOptionText())
            .eqIfPresent(EvalTableOptionDO::getScore, reqVO.getScore())
            .eqIfPresent(EvalTableOptionDO::getSortOrder, reqVO.getSortOrder())
        .orderByDesc(EvalTableOptionDO::getAddTime));    }

    /**
     * 根据量表id删除关联选项
     * @param questionId
     */
    @Delete("DELETE FROM pam_psy_eval_table_option WHERE question_id = #{questionId}")
    void deleteEvalTableOptionByQuestionId(@Param("questionId") String questionId);
}
