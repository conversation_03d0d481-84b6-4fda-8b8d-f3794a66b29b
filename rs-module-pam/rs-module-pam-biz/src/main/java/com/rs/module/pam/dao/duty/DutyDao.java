package com.rs.module.pam.dao.duty;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.pam.controller.admin.duty.vo.CurrentDutyRecordsInfoVO;
import com.rs.module.pam.entity.duty.DutyDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* 监所事务管理-监室值班 Dao
*
* <AUTHOR>
*/
@Mapper
public interface DutyDao extends IBaseDao<DutyDO> {

    /**
     *
     *
     * @param roomId
     * @param dutyDate
     * @return
     */
    List<CurrentDutyRecordsInfoVO> getPrisonerCurrentDuty(@Param("orgCode") String orgCode, @Param("roomId") String roomId,
                                                          @Param("jgrybm") String jgrybm, @Param("dutyDate") Date dutyDate);

    /**
     * 清理 pam_duty 表，如果一个班次都没有则进行清理
     *
     * @param roomId
     * @param dutyDate
     * @return
     */
    int cleanTablePrisonRoomDuty(@Param("orgCode") String orgCode, @Param("roomId") String roomId,
                                 @Param("dutyDate") Date dutyDate);

    /**
     * 获取时间内 人员排班次数
     *
     * @param roomId
     * @param startTime
     * @param endTime
     * @return
     */
    List<Map<String, Object>> selectPrisonerWeekDutyCount(@Param("orgCode") String orgCode,
                                                          @Param("roomId") String roomId,
                                                          @Param("startTime") Date startTime,
                                                          @Param("endTime") Date endTime);


}
