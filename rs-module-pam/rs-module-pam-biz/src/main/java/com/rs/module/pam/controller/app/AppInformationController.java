package com.rs.module.pam.controller.app;

import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.entity.OpsDicCode;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.util.DicUtil;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.base.service.pm.AreaService;
import com.rs.module.pam.cons.CommonConstants;
import com.rs.module.pam.controller.admin.information.vo.PublishListReqVO;
import com.rs.module.pam.controller.admin.information.vo.PublishRespVO;
import com.rs.module.pam.entity.information.PublishDO;
import com.rs.module.pam.enums.PublishBizTypeEnum;
import com.rs.module.pam.service.information.PublishService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监室信息发布-仓内屏")
@RestController
@RequestMapping("/app/pam/public/information")
@Validated
public class AppInformationController {
    @Value("${system-mark}")
    private String systemMark;

    @Resource
    private PublishService publishService;
    @Resource
    private AreaService areaService;


    @PostMapping("/getPublishList")
    @ApiOperation(value = "获得监室信息发布列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roomId", value = "监室编号", required = true),
            @ApiImplicitParam(name = "businessType", value = "信息类型", required = true)
    })
    public CommonResult<List<PublishRespVO>> getPublishList(@Valid @RequestBody JSONObject params) {
        PublishListReqVO listReqVO = new PublishListReqVO();
        // 根据监室编号查询机构信息
        AreaDO roomId = areaService.getPrisonByRoomId(params.getString("roomId"));
        listReqVO.setOrgCode(roomId.getOrgCode());
        listReqVO.setInfoType(params.getString("businessType"));
        listReqVO.setStatus(CommonConstants.INFORMATION_PUBLIC_STATUS_ENABLE);
        listReqVO.setBizType(PublishBizTypeEnum.JSSWFB.getCode());
        List<PublishDO> list = publishService.getPublishList(listReqVO);
        return success(BeanUtils.toBean(list, PublishRespVO.class));
    }

    @GetMapping("/getInfoType")
    @ApiOperation(value = "获得监室信息类型")
    public CommonResult<List<OpsDicCode>> getInfoType() {
        return success(DicUtil.getDicAsc("ZD_JSXXFB_XXLX", systemMark));
    }

}
