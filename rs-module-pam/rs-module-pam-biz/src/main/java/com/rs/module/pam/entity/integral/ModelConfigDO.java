package com.rs.module.pam.entity.integral;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 监所事务管理-积分模型配置 DO
 *
 * <AUTHOR>
 */
@TableName("pam_integral_model_config")
@KeySequence("pam_integral_model_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_integral_model_config")
public class ModelConfigDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 模型名称
     */
    private String name;
    /**
     * 风险模型id
     */
    private String riskModelId;
    /**
     * 指标类型id
     */
    private String indicatorTypeId;
    /**
     * 指标id
     */
    private String indicatorId;
    /**
     * 计分类型(1：加分，2：减分)
     */
    private String scoreType;
    /**
     * 分值类型(1：数值，2：百分比)
     */
    private String scoreForm;
    /**
     * 分值
     */
    private BigDecimal score;
    /**
     * 数据源ID
     */
    private String dbId;
    /**
     * 数据资源类型  （01：数据表  02：资源脚本）
     */
    private String resType;
    /**
     * 数据资源ID
     */
    private String resId;
    /**
     * 数据资源名称
     */
    private String resName;
    /**
     * 主键字段
     */
    private String keyCol;
    /**
     * 增量字段
     */
    private String incCol;
    /**
     * 增量默认值
     */
    private String incValueDefault;
    /**
     * 当前增量值
     */
    private String incValue;
    /**
     * 业务时间字段
     */
    private String businessDateCol;
    /**
     * 监所编号字段
     */
    private String orgCodeCol;
    /**
     * 监室编号字段
     */
    private String roomCodeCol;
    /**
     * 监管人员编号字段
     */
    private String prisonerCodeCol;
    /**
     * 条件SQL
     */
    private String conditionSql;
    /**
     * 描述
     */
    private String description;
    /**
     * 状态（1：启用，0：停用）
     */
    private String status;

}
