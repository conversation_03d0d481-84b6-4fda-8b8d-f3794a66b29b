package com.rs.module.pam.service.psy;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.util.BspDbUtil;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanPushRecordAnswerListReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanPushRecordAnswerPageReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanPushRecordAnswerSaveReqVO;
import com.rs.module.pam.dao.common.CommonDao;
import com.rs.module.pam.dao.psy.EvalPlanPushRecordAnswerDao;
import com.rs.module.pam.dto.AnswerCardDTO;
import com.rs.module.pam.entity.psy.*;
import com.rs.module.pam.enums.PsyFillingStatusEnum;
import com.rs.module.pam.enums.PsyQuestionTypeEnum;
import com.rs.module.pam.enums.PsyTriggerTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.weaver.ast.Var;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 监所事务管理-心理测评计划推送记录答案 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class EvalPlanPushRecordAnswerServiceImpl extends BaseServiceImpl<EvalPlanPushRecordAnswerDao, EvalPlanPushRecordAnswerDO> implements EvalPlanPushRecordAnswerService {

    @Resource
    private EvalPlanPushRecordAnswerDao evalPlanPushRecordAnswerDao;
    @Resource
    private EvalPlanPushRecordService evalPlanPushRecordService;
    @Resource
    private EvalPlanService evalPlanService;
    @Resource
    private EvalTableService evalTableService;

    @Resource
    private CommonDao commonDao;
    @Resource
    private EvalTableOptionService evalTableOptionService;
    @Resource
    private EvalTableQuestionService evalTableQuestionService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createEvalPlanPushRecordAnswer(EvalPlanPushRecordAnswerSaveReqVO createReqVO) {
        // 答题
        String evalNo = createReqVO.getEvalNo();
        // 查询推送记录
        EvalPlanPushRecordDO recordDO = evalPlanPushRecordService.getByEvalNo(evalNo);
        if (recordDO == null) {
            throw new ServerException("监所事务管理-心理测评计划推送记录数据不存在,答题提交失败");
        }
        // 查询测评计划
        EvalPlanDO evalPlanDO = evalPlanService.getByPlanCode(recordDO.getPlanCode());
        if (evalPlanDO == null) {
            throw new ServerException("监所事务管理-心理测评计划数据不存在,答题提交失败");
        }
        // 查询测评量表
        EvalTableDO evalTable = evalTableService.getEvalTable(recordDO.getTableId());
        if (evalTable == null) {
            throw new ServerException("监所事务管理-心理测评量表数据不存在,答题提交失败");
        }
        Date now = new Date();
        // 判断是否已逾期， 若已逾期，则不允许提交答案
        if (DateUtil.offsetDay(recordDO.getPushTime(), evalPlanDO.getCompletionDeadline())
                .before(now)) {
            throw new ServerException("监所事务管理-【" + evalTable.getName() + "】测评已逾期,答题提交失败");
        }
        // 校验是否能多次答题
        if (evalPlanDO.getEvalNumber() != null && evalPlanDO.getEvalNumber() == 1) {
            Integer count = evalPlanPushRecordAnswerDao.selectCount(EvalPlanPushRecordAnswerDO::getEvalNo, evalNo);
            if (count > 0) {
                throw new ServerException("监所事务管理-【" + evalTable.getName() + "】只能提交一次答案,请查看是否已提交答案");
            }
        }
        EvalPlanPushRecordDO update = new EvalPlanPushRecordDO();
        update.setId(recordDO.getId());
        EvalPlanPushRecordAnswerDO dtk = BeanUtils.toBean(createReqVO, EvalPlanPushRecordAnswerDO.class);
        // 计算作答用时
        long between = DateUtil.between(createReqVO.getStartTime(), createReqVO.getEndTime(), DateUnit.SECOND);
        dtk.setTimeSpentAnswer(LocalTime.ofSecondOfDay(between));
        dtk.setSubmitTime(now);
        // 计算得分
        List<AnswerCardDTO.AnswerCardItemDTO> answerCardItemDTOS = createReqVO.getAnswerCardDTOS();
        // 判题
        AnswerCardDTO answerCardDTO = AnswerCardDTO.builder().items(answerCardItemDTOS).tableId(evalTable.getId()).build();
        evalTableService.judgeQuestion(answerCardDTO);
        // 得分
        dtk.setScore(BigDecimal.valueOf(answerCardDTO.getTotalScore()));
        dtk.setEvalResults(answerCardDTO.getResult());
        dtk.setSubmitAnswer(JSONUtil.toJsonStr(answerCardDTO.getItems()));
        dtk.setStatus("1");
        evalPlanPushRecordAnswerDao.insert(dtk);
        // 更新推送记录状态
        update.setScore(dtk.getScore());
        update.setEvalResults(dtk.getEvalResults());
        update.setFillingTime(now);
        update.setFillingStatus(PsyFillingStatusEnum.YTX.getCode());
        update.setFillingPlatform(createReqVO.getFillingPlatform());
        evalPlanPushRecordService.updateById(update);

        //药物滥用检测 文书信息写入
        getYwlyAnswerToWs(evalPlanDO, recordDO.getId(), answerCardDTO.getItems());
        // 返回
        return dtk.getId();
    }

    private void getYwlyAnswerToWs(EvalPlanDO evalPlanDO, String glid, List<AnswerCardDTO.AnswerCardItemDTO> items) {

        try {
            String planCode = BspDbUtil.getParam("PLAN_CODE");
            if (StringUtils.isEmpty(planCode)) {
                planCode = "P20250721004";
            }
            if (PsyTriggerTypeEnum.TJCF.getCode().equals(evalPlanDO.getTriggerType()) && planCode.equals(evalPlanDO.getPlanCode())) {
                String glType = "ywlydtjc";
                String fieldPerFix = "ywly_";
                String wsdata = commonDao.getWsdataByGlid(glid, glType);
                JSONObject jsonObject = JSON.parseObject(wsdata);
                Map<String, String> answerMap = items.stream().collect(Collectors.toMap(AnswerCardDTO.AnswerCardItemDTO::getQuestionId, AnswerCardDTO.AnswerCardItemDTO::getAnswerOptionCode, (a1, a2) -> a2));
                LambdaQueryWrapper<EvalTableOptionDO> wrapperOption = Wrappers.lambdaQuery(EvalTableOptionDO.class)
                        .eq(EvalTableOptionDO::getTableId, evalPlanDO.getTableId());
                List<EvalTableOptionDO> listOption = evalTableOptionService.list(wrapperOption);
                LambdaQueryWrapper<EvalTableQuestionDO> wrapperQuestion = Wrappers.lambdaQuery(EvalTableQuestionDO.class)
                        .eq(EvalTableQuestionDO::getTableId, evalPlanDO.getTableId());
                List<EvalTableQuestionDO> listQuestion = evalTableQuestionService.list(wrapperQuestion);
                Map<String, String> questionTypeMap = listQuestion.stream().collect(Collectors.toMap(EvalTableQuestionDO::getId, EvalTableQuestionDO::getQuestionType, (a1, a2) -> a2));
                jsonObject.put("tbrq", new SimpleDateFormat("yyyy年MM月dd日").format(new Date()));
                for (EvalTableOptionDO optionDO : listOption) {
                    String questionType = questionTypeMap.get(optionDO.getQuestionId());
                    String answer = answerMap.get(optionDO.getQuestionId());
                    String key = fieldPerFix + optionDO.getQuestionId() + "_" + optionDO.getOptionCode();
                    if (PsyQuestionTypeEnum.Radio.getCode().equals(questionType)) {
                        String value = org.apache.commons.lang3.StringUtils.isNoneEmpty(answer) && answer.equals(optionDO.getOptionCode()) ? "1" : "0";
                        jsonObject.put(key, value);
                    } else if (PsyQuestionTypeEnum.Multi.getCode().equals(questionType)) {
                        String value = org.apache.commons.lang3.StringUtils.isNoneEmpty(answer) && answer.contains(optionDO.getOptionCode()) ? "1" : "0";
                        jsonObject.put(key, value);
                    } else if (PsyQuestionTypeEnum.Answer.getCode().equals(questionType)) {
                        jsonObject.put(key, answer);
                    }
                }
                commonDao.updateWsdataByGlid(glid, glType, jsonObject.toJSONString());
            }
        } catch (Exception e) {
            log.error("药物滥用检测-文书更新失败：", e);
        }
    }

    @Override
    public void updateEvalPlanPushRecordAnswer(EvalPlanPushRecordAnswerSaveReqVO updateReqVO) {
        // 校验存在
        validateEvalPlanPushRecordAnswerExists(updateReqVO.getId());
        // 更新
        EvalPlanPushRecordAnswerDO updateObj = BeanUtils.toBean(updateReqVO, EvalPlanPushRecordAnswerDO.class);
        evalPlanPushRecordAnswerDao.updateById(updateObj);
    }

    @Override
    public void deleteEvalPlanPushRecordAnswer(String id) {
        // 校验存在
        validateEvalPlanPushRecordAnswerExists(id);
        // 删除
        evalPlanPushRecordAnswerDao.deleteById(id);
    }

    private void validateEvalPlanPushRecordAnswerExists(String id) {
        if (evalPlanPushRecordAnswerDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-心理测评计划推送记录答案数据不存在");
        }
    }

    @Override
    public EvalPlanPushRecordAnswerDO getEvalPlanPushRecordAnswer(String id) {
        return evalPlanPushRecordAnswerDao.selectById(id);
    }

    @Override
    public PageResult<EvalPlanPushRecordAnswerDO> getEvalPlanPushRecordAnswerPage(EvalPlanPushRecordAnswerPageReqVO pageReqVO) {
        return evalPlanPushRecordAnswerDao.selectPage(pageReqVO);
    }

    @Override
    public List<EvalPlanPushRecordAnswerDO> getEvalPlanPushRecordAnswerList(EvalPlanPushRecordAnswerListReqVO listReqVO) {
        return evalPlanPushRecordAnswerDao.selectList(listReqVO);
    }


}
