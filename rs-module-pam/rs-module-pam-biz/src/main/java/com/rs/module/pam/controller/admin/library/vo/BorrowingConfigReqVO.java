package com.rs.module.pam.controller.admin.library.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 监所事务管理-图书借阅配置列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BorrowingConfigReqVO extends BaseVO {
private static final long serialVersionUID = 1L;

    @ApiModelProperty("机构码")
    private String orgCode;

    @ApiModelProperty("县分局码")
    private String regCode;

    @ApiModelProperty("市局码")
    private String cityCode;

}
