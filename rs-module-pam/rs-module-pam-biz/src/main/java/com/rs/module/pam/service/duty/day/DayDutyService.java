package com.rs.module.pam.service.duty.day;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.duty.day.vo.DayDutySaveReqVO;
import com.rs.module.pam.controller.admin.duty.day.vo.DayDutySaveVO;
import com.rs.module.pam.controller.admin.duty.day.vo.DayDutyVO;
import com.rs.module.pam.controller.admin.duty.vo.DutyPrisonerVO;
import com.rs.module.pam.controller.app.vo.AppDutyRespVO;
import com.rs.module.pam.controller.app.vo.AppDutyShiftRespVO;
import com.rs.module.pam.entity.duty.day.DayDutyDO;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 监所事务管理-监室值日 Service 接口
 *
 * <AUTHOR>
 */
public interface DayDutyService extends IBaseService<DayDutyDO>{

    /**
     * 创建监所事务管理-监室值日
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    void create(@Valid DayDutySaveVO createReqVO);

    /**
     * 更新监所事务管理-监室值日
     *
     * @param updateReqVO 更新信息
     */
    void update(@Valid DayDutySaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-监室值日
     *
     * @param id 编号
     */
    void delete(String id);

    /**
     * 获得监所事务管理-监室值日
     *
     * @param id 编号
     * @return 监所事务管理-监室值日
     */
    DayDutyDO get(String id);

    /**
     * 监室值日-排班记录
     *
     * @param roomId 监室编号
     * @param startDate 值日开始时间
     * @param endDate 值日结束时间
     * @return
     */
    List<DayDutyVO> dutyRecords(String orgCode, String roomId, Date startDate, Date endDate);

    /**
     * 监室人员列表
     *
     * @param roomId 监室编号
     */
    List<DutyPrisonerVO> prisonerList(String orgCode, String roomId);

    /**
     * 监室值班-排班记录-仓内屏
     *
     * @param orgCode 机构编号
     * @param roomId 监室编号
     * @param startDate 值班开始时间
     * @param endDate 值班结束时间
     * @return
     */
    List<AppDutyRespVO> appDutyRecords(String orgCode, String roomId, Date startDate, Date endDate);

    /**
     * 获取指定日期的值日记录-仓外屏
     * @param orgCode
     * @param roomId
     * @param date
     * @return
     */
    List<AppDutyShiftRespVO> dutyRecordsByDate(String orgCode, String roomId, Date date);

}
