package com.rs.module.pam.dao.attention;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.attention.vo.PrisonerListReqVO;
import com.rs.module.pam.controller.admin.attention.vo.PrisonerPageReqVO;
import com.rs.module.pam.controller.admin.attention.vo.PrisonerRespVO;
import com.rs.module.pam.controller.admin.attention.vo.RoomPrisonerRespVO;
import com.rs.module.pam.entity.attention.PrisonerDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 监所事务管理-重点人员关注登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface AttentionPrisonerDao extends IBaseDao<PrisonerDO> {


    default PageResult<PrisonerDO> selectPage(PrisonerPageReqVO reqVO) {
        Page<PrisonerDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrisonerDO> wrapper = new LambdaQueryWrapperX<PrisonerDO>()
            .eqIfPresent(PrisonerDO::getJgrybm, reqVO.getJgrybm())
            .betweenIfPresent(PrisonerDO::getBeginTime, reqVO.getBeginTime())
            .betweenIfPresent(PrisonerDO::getEndTime, reqVO.getEndTime())
            .eqIfPresent(PrisonerDO::getAttentionReason, reqVO.getAttentionReason())
            .eqIfPresent(PrisonerDO::getFrequencyType, reqVO.getFrequencyType())
            .eqIfPresent(PrisonerDO::getFrequency, reqVO.getFrequency())
            .eqIfPresent(PrisonerDO::getAttentionLevel, reqVO.getAttentionLevel())
            .eqIfPresent(PrisonerDO::getPushJobPositions, reqVO.getPushJobPositions())
            .eqIfPresent(PrisonerDO::getRegStatus, reqVO.getRegStatus())
            .eqIfPresent(PrisonerDO::getRegOperatorSfzh, reqVO.getRegOperatorSfzh())
            .eqIfPresent(PrisonerDO::getRegOperatorXm, reqVO.getRegOperatorXm())
            .betweenIfPresent(PrisonerDO::getRegTime, reqVO.getRegTime())
            .eqIfPresent(PrisonerDO::getLeaderApproverSfzh, reqVO.getLeaderApproverSfzh())
            .eqIfPresent(PrisonerDO::getLeaderApproverXm, reqVO.getLeaderApproverXm())
            .betweenIfPresent(PrisonerDO::getLeaderApproverTime, reqVO.getLeaderApproverTime())
            .eqIfPresent(PrisonerDO::getLeaderApprovalResult, reqVO.getLeaderApprovalResult())
            .eqIfPresent(PrisonerDO::getLeaderApprovalComments, reqVO.getLeaderApprovalComments())
            .eqIfPresent(PrisonerDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(PrisonerDO::getTaskId, reqVO.getTaskId())
            .orderByDesc(PrisonerDO::getAddTime);

        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PrisonerDO::getAddTime);
        }
        Page<PrisonerDO> prisonerPage = selectPage(page, wrapper);
        return new PageResult<>(prisonerPage.getRecords(), prisonerPage.getTotal());
    }

    default List<PrisonerDO> selectList(PrisonerListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrisonerDO>()
            .eqIfPresent(PrisonerDO::getJgrybm, reqVO.getJgrybm())
            .betweenIfPresent(PrisonerDO::getBeginTime, reqVO.getBeginTime())
            .betweenIfPresent(PrisonerDO::getEndTime, reqVO.getEndTime())
            .eqIfPresent(PrisonerDO::getAttentionReason, reqVO.getAttentionReason())
            .eqIfPresent(PrisonerDO::getFrequencyType, reqVO.getFrequencyType())
            .eqIfPresent(PrisonerDO::getFrequency, reqVO.getFrequency())
            .eqIfPresent(PrisonerDO::getAttentionLevel, reqVO.getAttentionLevel())
            .eqIfPresent(PrisonerDO::getPushJobPositions, reqVO.getPushJobPositions())
            .eqIfPresent(PrisonerDO::getRegStatus, reqVO.getRegStatus())
            .eqIfPresent(PrisonerDO::getRegOperatorSfzh, reqVO.getRegOperatorSfzh())
            .eqIfPresent(PrisonerDO::getRegOperatorXm, reqVO.getRegOperatorXm())
            .betweenIfPresent(PrisonerDO::getRegTime, reqVO.getRegTime())
            .eqIfPresent(PrisonerDO::getLeaderApproverSfzh, reqVO.getLeaderApproverSfzh())
            .eqIfPresent(PrisonerDO::getLeaderApproverXm, reqVO.getLeaderApproverXm())
            .betweenIfPresent(PrisonerDO::getLeaderApproverTime, reqVO.getLeaderApproverTime())
            .eqIfPresent(PrisonerDO::getLeaderApprovalResult, reqVO.getLeaderApprovalResult())
            .eqIfPresent(PrisonerDO::getLeaderApprovalComments, reqVO.getLeaderApprovalComments())
            .eqIfPresent(PrisonerDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(PrisonerDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(PrisonerDO::getAddTime));
    }

    Integer getPrisonerCount(@Param("orgCode") String orgCode, @Param("roomCode") String roomCode);

    /**
     * 获取指定监所下的重点关注人员
     * @param orgCode
     * @return
     */
    List<PrisonerRespVO> getPrisonerByOrgCode(@Param("orgCode") String orgCode);

}
