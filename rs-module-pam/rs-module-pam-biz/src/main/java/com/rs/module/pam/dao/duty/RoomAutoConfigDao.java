package com.rs.module.pam.dao.duty;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.pam.entity.duty.RoomAutoConfigDO;
import org.apache.ibatis.annotations.Mapper;

/**
* 监所事务管理-监室自动排班配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface RoomAutoConfigDao extends IBaseDao<RoomAutoConfigDO> {

    default RoomAutoConfigDO getByRoomId(String orgCode, String roomId) {
        return selectOne(new LambdaQueryWrapper<RoomAutoConfigDO>()
                .eq(RoomAutoConfigDO::getOrgCode, orgCode)
                .eq(RoomAutoConfigDO::getRoomId, roomId));
    }


}
