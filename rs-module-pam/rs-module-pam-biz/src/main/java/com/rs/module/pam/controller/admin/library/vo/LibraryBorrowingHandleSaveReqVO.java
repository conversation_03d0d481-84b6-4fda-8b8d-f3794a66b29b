package com.rs.module.pam.controller.admin.library.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-图书借阅处理新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LibraryBorrowingHandleSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("借阅id")
    @NotEmpty(message = "借阅id不能为空")
    private String borrowingId;

    @ApiModelProperty("借阅状态（字典：ZD_TSJY_JYZT）")
    @NotEmpty(message = "借阅状态（字典：ZD_TSJY_JYZT）不能为空")
    private String status;

    @ApiModelProperty("处理人id")
    @NotEmpty(message = "处理人id不能为空")
    private String handleUserSfzh;

    @ApiModelProperty("处理人姓名")
    @NotEmpty(message = "处理人姓名不能为空")
    private String handleUser;

    @ApiModelProperty("处理时间")
    @NotNull(message = "处理时间不能为空")
    private Date handleTime;

}
