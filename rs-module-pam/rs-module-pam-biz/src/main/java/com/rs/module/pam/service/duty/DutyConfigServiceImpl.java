package com.rs.module.pam.service.duty;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.dto.OrgRespDTO;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.duty.vo.DutyConfigSaveReqVO;
import com.rs.module.pam.dao.duty.DutyConfigDao;
import com.rs.module.pam.entity.duty.DutyConfigDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


/**
 * 监所事务管理-值班规则配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DutyConfigServiceImpl extends BaseServiceImpl<DutyConfigDao, DutyConfigDO> implements DutyConfigService {

    @Resource
    private DutyConfigDao dutyConfigDao;
    @Resource
    private BspApi bspApi;

    @Override
    public String createConfig(DutyConfigSaveReqVO createReqVO) {
        // 插入
        DutyConfigDO configDO = BeanUtils.toBean(createReqVO, DutyConfigDO.class);
        if (StringUtil.isNullBlank(configDO.getId())) {
            dutyConfigDao.insert(configDO);
        } else {
            dutyConfigDao.updateById(configDO);
        }
        // 返回
        return configDO.getId();
    }

    @Override
    public void updateConfig(DutyConfigSaveReqVO updateReqVO) {
        // 校验存在
        validateConfigExists(updateReqVO.getId());
        // 更新
        DutyConfigDO updateObj = BeanUtils.toBean(updateReqVO, DutyConfigDO.class);
        dutyConfigDao.updateById(updateObj);
    }

    private void validateConfigExists(String id) {
        if (dutyConfigDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-值班规则配置数据不存在");
        }
    }

    @Override
    public DutyConfigDO getConfig(String orgCode) {
        DutyConfigDO configDO = getOne(new LambdaQueryWrapper<DutyConfigDO>().eq(DutyConfigDO::getOrgCode, orgCode), false);
        if (configDO == null) {
            return new DutyConfigDO();
        }
        return configDO;
    }

}
