package com.rs.module.pam.entity.daily;

import lombok.*;
import java.util.*;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 监所事务管理-一日生活制度模板关联事务 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 监所事务管理-一日生活制度模板关联事务新增/修改 Request VO")
@TableName("pam_daily_life_template_event")
@KeySequence("pam_daily_life_template_event_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LifeTemplateEventDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;
    /**
     * 模板ID
     */
    @ApiModelProperty("模板ID")
    private String templateId;
    /**
     * 时间范围开始。示例 01:12
     */
    @ApiModelProperty("时间范围开始。示例 01:12")
    private String startTime;
    /**
     * 时间范围截至
     */
    @ApiModelProperty("时间范围截至")
    private String endTime;
    /**
     * 结束时间是否跨天,1跨天
     */
    @ApiModelProperty("结束时间是否跨天,1跨天")
    private Short endTimeSpan;
    /**
     * 事务ID（逗号分隔）
     */
    @ApiModelProperty("事务ID（逗号分隔）")
    private String eventId;
    /**
     * 事务名称（逗号分隔）
     */
    @ApiModelProperty("事务名称（逗号分隔）")
    private String eventName;
    /**
     * 是否启用语音播报(0:否,1:是)
     */
    @ApiModelProperty("是否启用语音播报(0:否,1:是)")
    private Short isEnabledVoice;
    /**
     * 排序ID
     */
    @ApiModelProperty("排序ID")
    private Integer sort;

}
