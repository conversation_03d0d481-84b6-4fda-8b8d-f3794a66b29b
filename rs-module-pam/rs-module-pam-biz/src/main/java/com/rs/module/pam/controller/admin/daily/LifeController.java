package com.rs.module.pam.controller.admin.daily;

import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.daily.vo.*;
import com.rs.module.pam.entity.daily.LifeDO;
import com.rs.module.pam.entity.daily.LifeEventDO;
import com.rs.module.pam.entity.daily.LifeRoomDO;
import com.rs.module.pam.service.daily.LifeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监所事务管理-一日生活制度")
@RestController
@RequestMapping("/pam/daily/life")
@Validated
public class LifeController {

    @Resource
    private LifeService lifeService;

    @PostMapping("/create")
    @ApiOperation(value = "创建监所事务管理-一日生活制度")
    public CommonResult<String> createLife(@Valid @RequestBody LifeSaveReqVO createReqVO) {
        return success(lifeService.createLife(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新监所事务管理-一日生活制度")
    public CommonResult<Boolean> updateLife(@Valid @RequestBody LifeSaveReqVO updateReqVO) {
        lifeService.updateLife(updateReqVO);
        return success(true);
    }

    @PostMapping("/update-one")
    @ApiOperation(value = "更新(单独更新)监所事务管理-一日生活制度")
    public CommonResult<Boolean> updateOneLife(@Valid @RequestBody LifeOneSaveReqVO updateReqVO) {
        lifeService.updateOneLife(updateReqVO);
        return success(true);
    }

    @PostMapping("/approval-update")
    @ApiOperation(value = "审批监所事务管理-一日生活制度审批流程")
    public CommonResult<Boolean> approvalUpdateLife(@Valid @RequestBody ApprovalLifeSaveReqVO updateReqVO) {
        lifeService.approvalUpdateLife(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除监所事务管理-一日生活制度")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteLife(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            lifeService.deleteLife(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得监所事务管理-一日生活制度")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<LifeRespVO> getLife(@RequestParam("id") String id) {
        LifeDO life = lifeService.getLife(id);
        return success(BeanUtils.toBean(life, LifeRespVO.class));
    }

    /*@PostMapping("/page")
    @ApiOperation(value = "获得监所事务管理-一日生活制度分页")
    public CommonResult<PageResult<LifeRespVO>> getLifePage(@Valid @RequestBody LifePageReqVO pageReqVO) {
        PageResult<LifeDO> pageResult = lifeService.getLifePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LifeRespVO.class));
    }*/

    @PostMapping("/list")
    @ApiOperation(value = "获得监所事务管理-一日生活制度列表")
    public CommonResult<List<LifeRespVO>> getLifeList(@Valid @RequestBody LifeListReqVO listReqVO) {
        if (StringUtils.isEmpty(listReqVO.getOrgCode())) {
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        List<LifeDO> list = lifeService.getLifeList(listReqVO);
        return success(BeanUtils.toBean(list, LifeRespVO.class));
    }
    // ==================== 子表（监所事务管理-一日生活制度关联事务） ====================

    @GetMapping("/life-event/list-by-daily-life-id")
    @ApiOperation(value = "获得监所事务管理-一日生活制度关联事务列表")
    @ApiImplicitParam(name = "dailyLifeId", value = "一日生活制度ID")
    public CommonResult<List<LifeEventDO>> getLifeEventListByDailyLifeId(@RequestParam("dailyLifeId") String dailyLifeId) {
        return success(lifeService.getLifeEventListByDailyLifeId(dailyLifeId));
    }

    // ==================== 子表（监所事务管理-一日生活制度关联监室） ====================

    @GetMapping("/life-room/list-by-daily-life-id")
    @ApiOperation(value = "获得监所事务管理-一日生活制度关联监室列表")
    @ApiImplicitParam(name = "dailyLifeId", value = "一日生活制度ID")
    public CommonResult<List<LifeRoomDO>> getLifeRoomListByDailyLifeId(@RequestParam("dailyLifeId") String dailyLifeId) {
        return success(lifeService.getLifeRoomListByDailyLifeId(dailyLifeId));
    }

    @PostMapping("/life-event-update")
    @ApiOperation(value = "更新监所事务管理-一日生活制度事务选项")
    public CommonResult<Boolean> updateLifeEvent(@Valid @RequestBody LifeEventSaveReqVO updateReqVO) {
        lifeService.updateLifeEvent(updateReqVO);
        return success(true);
    }
    @GetMapping("/getLifeEventByRoomId")
    @ApiOperation(value = "获得监所事务管理-一日生活制度事项")
    @ApiImplicitParam(name = "roomId", value = "监室id")
    public CommonResult<List<LifeEventDO>> getLifeEventByRoomId(@RequestParam("roomId") String roomId) {
        return success(lifeService.getLifeEvent(roomId));
    }
}
