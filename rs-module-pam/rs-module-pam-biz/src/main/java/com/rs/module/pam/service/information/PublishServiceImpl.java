package com.rs.module.pam.service.information;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.cons.CommonConstants;
import com.rs.module.pam.controller.admin.information.vo.PublishListReqVO;
import com.rs.module.pam.controller.admin.information.vo.PublishPageReqVO;
import com.rs.module.pam.controller.admin.information.vo.PublishSaveReqVO;
import com.rs.module.pam.dao.information.AttentionPublishDao;
import com.rs.module.pam.entity.information.PublishDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


/**
 * 监所事务管理-监室信息发布 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PublishServiceImpl extends BaseServiceImpl<AttentionPublishDao, PublishDO> implements PublishService {

    @Resource
    private AttentionPublishDao publishDao;

    @Override
    public String createPublish(PublishSaveReqVO createReqVO) {
        // 插入
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        PublishDO publish = BeanUtils.toBean(createReqVO, PublishDO.class);
        publish.setOperatorSfzh(sessionUser.getIdCard());
        publish.setOperatorXm(sessionUser.getName());
        publish.setOperatorTime(new Date());
        publish.setStatus(CommonConstants.INFORMATION_PUBLIC_STATUS_DISABLE);
        publishDao.insert(publish);
        // 返回
        return publish.getId();
    }

    @Override
    public void updatePublish(PublishSaveReqVO updateReqVO) {
        // 校验存在
        validatePublishExists(updateReqVO.getId());
        // 更新
        PublishDO updateObj = BeanUtils.toBean(updateReqVO, PublishDO.class);
        publishDao.updateById(updateObj);
    }

    @Override
    public void deletePublish(String id) {
        // 校验存在
        validatePublishExists(id);
        // 删除
        publishDao.deleteById(id);
    }

    private void validatePublishExists(String id) {
        if (publishDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-监室信息发布数据不存在");
        }
    }

    @Override
    public PublishDO getPublish(String id) {
        return publishDao.selectById(id);
    }

    @Override
    public PageResult<PublishDO> getPublishPage(PublishPageReqVO pageReqVO) {
        return publishDao.selectPage(pageReqVO);
    }

    @Override
    public List<PublishDO> getPublishList(PublishListReqVO listReqVO) {
        return publishDao.selectList(listReqVO);
    }


}
