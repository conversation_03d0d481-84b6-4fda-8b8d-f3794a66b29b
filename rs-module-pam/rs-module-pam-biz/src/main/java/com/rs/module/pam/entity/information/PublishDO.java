package com.rs.module.pam.entity.information;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import java.util.Date;

/**
 * 监所事务管理-监室信息发布 DO
 *
 * <AUTHOR>
 */
@TableName("pam_information_publish")
@KeySequence("pam_information_publish_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PublishDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 标题
     */
    private String title;
    /**
     * 信息类型（字典：ZD_JSXXFB_XXLX）
     */
    private String infoType;
    /**
     * 信息内容
     */
    private String infoContentText;
    /**
     * 信息内容
     */
    private String infoContentHtml;
    /**
     * 登记经办人
     */
    private String operatorSfzh;
    /**
     * 登记经办人姓名
     */
    private String operatorXm;
    /**
     * 登记时间
     */
    private Date operatorTime;
    /**
     * 登记状态（1：启用，0：停用）
     */
    private String status;
    /**
     * 1 监室信息发布  2 教育读本发布
     */
    private String bizType;

}
