package com.rs.module.pam.service.device;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.cache.DicUtil;
import com.bsp.common.cache.RedisClient;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.gosun.zhjg.common.util.StringUtil;
import com.rs.framework.common.enums.DataSourceAppEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.util.BspDbUtil;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.base.service.sys.BusTraceService;
import com.rs.module.pam.controller.admin.device.vo.*;
import com.rs.module.pam.dao.device.DeviceRepairRegDao;
import com.rs.module.pam.entity.device.DeviceRepairRegDO;
import com.rs.module.pam.enums.DeviceRepairRegStatusEnum;
import com.rs.module.pam.util.CommonUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 监所事务管理-设备报修登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DeviceRepairRegServiceImpl extends BaseServiceImpl<DeviceRepairRegDao, DeviceRepairRegDO> implements DeviceRepairRegService {

    @Resource
    private DeviceRepairRegDao deviceRepairRegDao;

    @Resource
    private BusTraceService busTraceService;

    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public String createDeviceRepairReg(DeviceRepairRegSaveReqVO createReqVO) {
        // 工单编号  生成规则；SBBX+年月日+监室编码+00001
        String roomId = createReqVO.getRoomId();
        // "20250615"
        String nyr = LocalDate.now().toString().replace("-", "");
        String key = "device:sbbx:" + nyr + ":" + roomId;
        long index = RedisClient.incrementVal(key, 86400, 1);
        String strIndex = String.format("%05d", index);
        String repairNo = String.format("SBBX%s%s%s", nyr, roomId, strIndex);
        createReqVO.setRepairNo(repairNo);

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        createReqVO.setApplicantUserName(sessionUser.getName());
        createReqVO.setApplicantUserSfzh(sessionUser.getIdCard());
        createReqVO.setApplicantTime(new Date());
        // 插入
        DeviceRepairRegDO deviceRepairReg = BeanUtils.toBean(createReqVO, DeviceRepairRegDO.class);
        deviceRepairRegDao.insert(deviceRepairReg);
        // 返回
        return deviceRepairReg.getId();
    }

    @Override
    public void updateDeviceRepairReg(DeviceRepairRegSaveReqVO updateReqVO) {
        // 校验存在
        validateDeviceRepairRegExists(updateReqVO.getId());
        // 更新
        DeviceRepairRegDO updateObj = BeanUtils.toBean(updateReqVO, DeviceRepairRegDO.class);
        deviceRepairRegDao.updateById(updateObj);
    }

    @Override
    public void deleteDeviceRepairReg(String id) {
        // 校验存在
        validateDeviceRepairRegExists(id);
        // 删除
        deviceRepairRegDao.deleteById(id);
    }

    private void validateDeviceRepairRegExists(String id) {
        if (deviceRepairRegDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-设备报修登记数据不存在");
        }
    }

    @Override
    public DeviceRepairRegDO getDeviceRepairReg(String id) {
        return deviceRepairRegDao.selectById(id);
    }

    @Override
    public PageResult<DeviceRepairRegDO> getDeviceRepairRegPage(DeviceRepairRegPageReqVO pageReqVO) {
        return deviceRepairRegDao.selectPage(pageReqVO);
    }

    @Override
    public List<DeviceRepairRegDO> getDeviceRepairRegList(DeviceRepairRegListReqVO listReqVO) {
        return deviceRepairRegDao.selectList(listReqVO);
    }

    @Override
    public void approvalDeviceRepairReg(DeviceRepairRegApprovalReqVO approvalReqVO) {
        DeviceRepairRegDO deviceRepairRegDO = deviceRepairRegDao.selectById(approvalReqVO.getId());
        if (Objects.isNull(deviceRepairRegDO)) {
            throw new ServerException("监所事务管理-设备报修登记数据不存在");
        }
        if (!DataSourceAppEnum.CNP.getCode().equals(deviceRepairRegDO.getDataSources())) {
            throw new ServerException("非仓内屏报修，无需进行派单操作");
        }
        if (!DeviceRepairRegStatusEnum.DPD.getCode().equals(deviceRepairRegDO.getStatus())) {
            throw new ServerException("非派单状态，无需进行派单操作");
        }
        //非法审批人判断  0003：巡控民警   0005：管教民警
        String gjmjRolecode = BspDbUtil.getParam("GJMJ_ROLECODE");
        if (StringUtils.isEmpty(gjmjRolecode)) {
            gjmjRolecode = "0005";
        }
        String xkmjRolecode = BspDbUtil.getParam("XKMJ_ROLECODE");
        if (StringUtils.isEmpty(xkmjRolecode)) {
            xkmjRolecode = "0003";
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        if (!sessionUser.getRoleCodes().contains(xkmjRolecode) && !sessionUser.getRoleCodes().contains(gjmjRolecode)) {
            throw new ServerException("非法审批");
        }
        deviceRepairRegDO.setDetailedLocation(approvalReqVO.getDetailedLocation());
        deviceRepairRegDO.setRepairImgUrl(approvalReqVO.getRepairImgUrl());
        deviceRepairRegDO.setRemark(approvalReqVO.getRemark());
        deviceRepairRegDO.setStatus(DeviceRepairRegStatusEnum.DWX.getCode());
        deviceRepairRegDO.setDispatchTime(new Date());
        deviceRepairRegDO.setDetailedUserSfzh(sessionUser.getIdCard());
        deviceRepairRegDO.setDetailedUserName(sessionUser.getName());
        deviceRepairRegDao.updateById(deviceRepairRegDO);
    }

    @Override
    public void handleDeviceRepairReg(DeviceRepairRegHandleReqVO handleReqVO) {
        DeviceRepairRegDO deviceRepairRegDO = deviceRepairRegDao.selectById(handleReqVO.getId());
        if (Objects.isNull(deviceRepairRegDO)) {
            throw new ServerException("监所事务管理-设备报修登记数据不存在");
        }
        if (!DeviceRepairRegStatusEnum.DWX.getCode().equals(deviceRepairRegDO.getStatus())) {
            throw new ServerException("非待维修状态，无需进行已处理操作");
        }
        //非法审批人判断  0007：后勤民警
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String hqmgRoleCode = BspDbUtil.getParam("HQMJ_ROLECODE");
        if (StringUtils.isEmpty(hqmgRoleCode)) {
            hqmgRoleCode = "0007";
        }
        if (!sessionUser.getRoleCodes().contains(hqmgRoleCode)) {
            throw new ServerException("非法处置人");
        }
        deviceRepairRegDO.setStatus(DeviceRepairRegStatusEnum.YCL.getCode());
        deviceRepairRegDO.setHandleImgUrl(handleReqVO.getHandleImgUrl());
        deviceRepairRegDO.setHandleRemark(handleReqVO.getHandleRemark());
        deviceRepairRegDO.setHandleUserName(sessionUser.getName());
        deviceRepairRegDO.setHandleUserSfzh(sessionUser.getId());
        deviceRepairRegDO.setHandleTime(new Date());
        deviceRepairRegDao.updateById(deviceRepairRegDO);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("维修时间", sdf.format(deviceRepairRegDO.getHandleTime()));
        jsonObject.put("维修内容", DicUtil.translate("ZD_SBWXGL_BXNR", deviceRepairRegDO.getRepairContent()));
        jsonObject.put("维修人", sessionUser.getName());
        jsonObject.put("处理情况", deviceRepairRegDO.getHandleRemark());
        //业务日志写入 1、维修时间 2、维修内容 3、维修人 4、处理情况
        busTraceService.saveBusTrace(BusTypeEnum.YEWU_WXGD, jsonObject.toJSONString(), "",
                SessionUserUtil.getSessionUser().getOrgCode(),
                deviceRepairRegDO.getId());
    }

    @Override
    public void ignoreChangeDeviceRepairReg(DeviceRepairRegHandleReqVO handleReqVO) {
        DeviceRepairRegDO deviceRepairRegDO = deviceRepairRegDao.selectById(handleReqVO.getId());
        if (Objects.isNull(deviceRepairRegDO)) {
            throw new ServerException("监所事务管理-设备报修登记数据不存在");
        }
        if (!DataSourceAppEnum.CNP.getCode().equals(deviceRepairRegDO.getDataSources())) {
            throw new ServerException("非仓内屏报修，不允许进行忽略或取消忽略操作");
        }
        if (DeviceRepairRegStatusEnum.YHL.getCode().equals(deviceRepairRegDO.getStatus())) {
            deviceRepairRegDO.setStatus(DeviceRepairRegStatusEnum.DPD.getCode());
        } else if (DeviceRepairRegStatusEnum.DPD.getCode().equals(deviceRepairRegDO.getStatus())) {
            deviceRepairRegDO.setStatus(DeviceRepairRegStatusEnum.YHL.getCode());
        } else {
            throw new ServerException("当前为【" + DeviceRepairRegStatusEnum.getByCode(deviceRepairRegDO.getStatus()).getName() +
                    "】状态，不允许进行忽略或取消忽略操作");
        }
        deviceRepairRegDao.updateById(deviceRepairRegDO);
    }

    @Override
    public PageResult<DeviceRepairRegDO> getDeviceRepairRegRecordPage(int pageNo, int pageSize, String applicantUserSfzh, String type) {
        Map<String, Date> commonAppRecordPeriod = CommonUtils.getCommonAppRecordPeriod(type);
        return deviceRepairRegDao.getDeviceRepairRegRecordPage(pageNo, pageSize, applicantUserSfzh,
                commonAppRecordPeriod.get("startTime"), commonAppRecordPeriod.get("endTime"));
    }


}
