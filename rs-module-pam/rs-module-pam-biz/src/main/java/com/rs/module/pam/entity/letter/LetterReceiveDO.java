package com.rs.module.pam.entity.letter;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-家属通信-收信登记 DO
 *
 * <AUTHOR>
 */
@TableName("pam_letter_receive")
@KeySequence("pam_letter_receive_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LetterReceiveDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 数据来源（字典：ZD_DATA_SOURCES）
     */
    private String dataSources;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 送信人姓名
     */
    private String sendMailUser;
    /**
     * 关系（字典：ZD_RYGXDM）
     */
    private String relation;
    /**
     * 来信地址
     */
    private String sendAddress;
    /**
     * 来信日期
     */
    private Date sendDate;
    /**
     * 来信单位
     */
    private String sendPrison;
    /**
     * 信件邮编
     */
    private String mailNo;
    /**
     * 信件内容url
     */
    private String mailUrl;
    /**
     * 登记时间
     */
    private Date registerTime;
    /**
     * 登记人
     */
    private String registerUserSfzh;
    /**
     * 登记人姓名
     */
    private String registerUserName;
    /**
     * 签名图片
     */
    private String signUrl;
    /**
     * 信件状态 （字典：ZD_JSTX_SXZT）
     */
    private String status;
    /**
     * 转交人
     */
    private String passUser;
    /**
     * 转交时间
     */
    private Date passTime;
    /**
     * 转交备注
     */
    private String passRemark;
    /**
     * 管教审批人身份证号
     */
    private String gjApproverSfzh;
    /**
     * 管教审批人姓名
     */
    private String gjApproverXm;
    /**
     * 管教审批时间
     */
    private Date gjApproverTime;
    /**
     * 管教审批结果
     */
    private String gjApprovalResult;
    /**
     * 管教审核意见
     */
    private String gjApprovalComments;
    /**
     * 科组长审批人身份证号
     */
    private String groupApproverSfzh;
    /**
     * 科组长审批人姓名
     */
    private String groupApproverXm;
    /**
     * 科组长审批时间
     */
    private Date groupApproverTime;
    /**
     * 科组长审批结果
     */
    private String groupApprovalResult;
    /**
     * 科组长审核意见
     */
    private String groupApprovalComments;
    /**
     * 所领导审批人身份证号
     */
    private String leaderApproverSfzh;
    /**
     * 所领导审批人姓名
     */
    private String leaderApproverXm;
    /**
     * 所领导审批时间
     */
    private Date leaderApproverTime;
    /**
     * 所领导审批结果
     */
    private String leaderApprovalResult;
    /**
     * 所领导审核意见
     */
    private String leaderApprovalComments;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 签收时间
     */
    private Date receiptTime;
    /**
     * 送信人联系电话
     */
    private String sendContactNumber;

}
