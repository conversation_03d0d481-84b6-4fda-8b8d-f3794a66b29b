package com.rs.module.pam.controller.admin.daily.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 监所事务管理-日常清监检查项配置 Response VO")
@Data
public class CleanConfigRespVO implements TransPojo {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("检查类型（字典：ZD_AQJCGL_JCLX）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_AQJCGL_JCLX")
    private String checkType;
    @ApiModelProperty("检查项（字典：ZD_AQJCGL_JCX）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_AQJCGL_JCX")
    private String checkItem;
}
