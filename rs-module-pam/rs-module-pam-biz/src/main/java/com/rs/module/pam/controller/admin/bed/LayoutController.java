package com.rs.module.pam.controller.admin.bed;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.bed.vo.LayoutListReqVO;
import com.rs.module.pam.controller.admin.bed.vo.LayoutPageReqVO;
import com.rs.module.pam.controller.admin.bed.vo.LayoutRespVO;
import com.rs.module.pam.controller.admin.bed.vo.LayoutSaveReqVO;
import com.rs.module.pam.entity.bed.LayoutDO;
import com.rs.module.pam.service.bed.LayoutService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "床位管理-监室床位布局")
@RestController
@RequestMapping("/pam/bed/layout")
@Validated
public class LayoutController {

    @Resource
    private LayoutService layoutService;

    @PostMapping("/create")
    @ApiOperation(value = "创建监室床位布局")
    public CommonResult<String> createLayout(@Valid @RequestBody LayoutSaveReqVO createReqVO) {
        return success(layoutService.createLayout(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新监室床位布局")
    public CommonResult<Boolean> updateLayout(@Valid @RequestBody LayoutSaveReqVO updateReqVO) {
        layoutService.updateLayout(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除监室床位布局", hidden = true)
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteLayout(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           layoutService.deleteLayout(id);
        }
        return success(true);
    }

    @GetMapping("/getByOrgCode")
    @ApiOperation(value = "获得监室床位布局")
    @ApiImplicitParam(name = "orgCode", value = "机构编号")
    public CommonResult<JSONObject> getByOrgCode(@RequestParam("orgCode") String orgCode) {
        JSONObject result = new JSONObject();
        result.put("custom", BeanUtils.toBean(layoutService.list(new LambdaQueryWrapper<LayoutDO>()
                .eq(LayoutDO::getOrgCode, orgCode)
                .eq(LayoutDO::getLayoutType, "02")), LayoutRespVO.class));

        result.put("fixation", BeanUtils.toBean(layoutService.list(new LambdaQueryWrapper<LayoutDO>()
                .eq(LayoutDO::getOrgCode, orgCode)
                .eq(LayoutDO::getLayoutType, "01")), LayoutRespVO.class));
        return success(result);
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得监室床位布局分页", hidden = true)
    public CommonResult<PageResult<LayoutRespVO>> getLayoutPage(@Valid @RequestBody LayoutPageReqVO pageReqVO) {
        PageResult<LayoutDO> pageResult = layoutService.getLayoutPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LayoutRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得监室床位布局列表", hidden = true)
    public CommonResult<List<LayoutRespVO>> getLayoutList(@Valid @RequestBody LayoutListReqVO listReqVO) {
        List<LayoutDO> list = layoutService.getLayoutList(listReqVO);
        return success(BeanUtils.toBean(list, LayoutRespVO.class));
    }

}
