package com.rs.module.pam.dao.duty;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.pam.entity.duty.DutyConfigDO;
import org.apache.ibatis.annotations.Mapper;

/**
* 监所事务管理-值班规则配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface DutyConfigDao extends IBaseDao<DutyConfigDO> {

    default DutyConfigDO getByOrgCode(String orgCode) {
        return selectOne(new LambdaQueryWrapper<DutyConfigDO>()
                .eq(DutyConfigDO::getOrgCode, orgCode));
    }

    default DutyConfigDO createDefaultConfig(String orgCode) {
        DutyConfigDO dutyConfigDO = new DutyConfigDO();
        dutyConfigDO.setOrgCode(orgCode);
        dutyConfigDO.setSigninDutyEnable((short)1);
        dutyConfigDO.setSigninRemindEnable((short)1);
        dutyConfigDO.setSigninValidityPeriod((short)15);
        insert(dutyConfigDO);
        return dutyConfigDO;
    }

}
