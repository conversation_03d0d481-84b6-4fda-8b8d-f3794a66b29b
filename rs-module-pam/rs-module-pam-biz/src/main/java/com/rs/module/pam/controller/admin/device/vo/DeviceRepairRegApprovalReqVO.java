package com.rs.module.pam.controller.admin.device.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-派单 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DeviceRepairRegApprovalReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    @NotEmpty(message = "主键 不能为空")
    private String id;

    @ApiModelProperty("详细地点")
    private String detailedLocation;

    @ApiModelProperty("报修照片地址")
    private String repairImgUrl;

    @ApiModelProperty("备注")
    private String remark;

}
