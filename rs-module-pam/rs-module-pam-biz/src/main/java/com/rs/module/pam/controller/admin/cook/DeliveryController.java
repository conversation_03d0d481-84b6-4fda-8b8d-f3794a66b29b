package com.rs.module.pam.controller.admin.cook;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.pam.controller.admin.cook.vo.DeliveryRespVO;
import com.rs.module.pam.service.cook.DeliveryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监室配餐-发饭")
@RestController
@RequestMapping("/pam/cook/delivery")
@Validated
public class DeliveryController {

    @Resource
    private DeliveryService deliveryService;

    @GetMapping("/get")
    @ApiOperation(value = "获得监室配餐-发饭记录")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<DeliveryRespVO> getDeliveryRespVO(@RequestParam("id") String id) {
        DeliveryRespVO deliveryRecord = deliveryService.getDeliveryRespVO(id);
        return success(deliveryRecord);
    }



}
