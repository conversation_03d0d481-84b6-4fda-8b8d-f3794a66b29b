package com.rs.module.pam.controller.admin.psy.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.pam.dto.AnswerCardDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评计划推送记录答案新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EvalPlanPushRecordAnswerSaveReqVO extends BaseVO {

    @ApiModelProperty("主键(更新的时候传)")
    private String id;

    @ApiModelProperty("测评编号")
    private String evalNo;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("开始时间")
    @NotNull(message = "开始时间不能为空")
    private Date startTime;

    @ApiModelProperty("结束时间")
    @NotNull(message = "结束时间不能为空")
    private Date endTime;

    @ApiModelProperty("答题卡内容")
    @NotNull(message = "答题卡内容不能为空")
    private List<AnswerCardDTO.AnswerCardItemDTO> answerCardDTOS;

    @ApiModelProperty("填报平台(字典：ZD_DATA_SOURCES) 实战平台：1, 仓外屏：2, 仓内屏：3")
    @NotBlank(message = "填报平台不能为空")
    private String fillingPlatform;

    @ApiModelProperty("数据来源")
    private String dataSources;
}
