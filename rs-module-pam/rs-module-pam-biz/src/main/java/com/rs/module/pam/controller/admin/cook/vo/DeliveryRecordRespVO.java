package com.rs.module.pam.controller.admin.cook.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-发饭记录 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DeliveryRecordRespVO extends BaseVO implements TransPojo{
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("菜谱名称")
    private Date cookbookDate;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("监室ID")
    private String roomId;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("配餐类型（字典：ZD_PCGL_PCLX ）")
    private String mealType;
    @ApiModelProperty("当日食谱")
    private String cookbook;
    @ApiModelProperty("发饭时间")
    private Date deliveryTime;
    @ApiModelProperty("经办民警身份证号")
    private String operatePoliceSfzh;
    @ApiModelProperty("经办民警")
    private String operatePolice;
    @ApiModelProperty("经办时间")
    private Date operateTime;
    @ApiModelProperty("状态")
    private String status;
}
