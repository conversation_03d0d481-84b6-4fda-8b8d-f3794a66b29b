package com.rs.module.pam.controller.admin.represent.vo.extra;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel("同步仓内屏 基本信息VO")
public class InitBaseDataVO {

    private String orgCode;

    @ApiModelProperty("监室号")
    private String roomId;

    @ApiModelProperty("监室名")
    private String roomName;

    @ApiModelProperty("监室类型")
    private String roomType;

    @ApiModelProperty(value = "监室容量")
    private String planImprisonmentAmount;

    @ApiModelProperty("监室在押人数")
    private Integer prisonerCount;

    @ApiModelProperty(value = "主管民警")
    private String executivePolice;

    @ApiModelProperty(value = "协管民警")
    private String assistPolice;

    @ApiModelProperty("在押人员数据")
    private List<InitBaseDataPersonVO> personVOS;

    @ApiModelProperty("定时点名任务数据")
    private List<InitBaseDataConfigVO> configVOS;
}
