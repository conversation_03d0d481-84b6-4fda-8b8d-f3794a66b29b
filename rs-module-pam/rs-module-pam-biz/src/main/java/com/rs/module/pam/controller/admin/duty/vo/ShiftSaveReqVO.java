package com.rs.module.pam.controller.admin.duty.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-值班班次新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ShiftSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("班次名称")
    @NotEmpty(message = "班次名称不能为空")
    private String shiftName;

    @ApiModelProperty("开始时间类型（1：当天，2：次日）")
    private String startTimeType;

    @ApiModelProperty("开始时间")
    @NotNull(message = "开始时间不能为空")
    private String startTime;

    @ApiModelProperty("结束时间类型（1：当天，2：次日）")
    private String endTimeType;

    @ApiModelProperty("结束时间")
    @NotNull(message = "结束时间不能为空")
    private String endTime;

    @ApiModelProperty("排序")
    @NotNull(message = "排序不能为空")
    private Integer sort;

}
