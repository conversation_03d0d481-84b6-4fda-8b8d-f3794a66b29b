package com.rs.module.pam.controller.admin.duty;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.cons.CommonConstants;
import com.rs.module.pam.controller.admin.duty.vo.DutyConfigRespVO;
import com.rs.module.pam.controller.admin.duty.vo.DutyConfigSaveReqVO;
import com.rs.module.pam.controller.admin.duty.vo.RoomAutoConfigRespVO;
import com.rs.module.pam.controller.admin.duty.vo.RoomAutoConfigSaveReqVO;
import com.rs.module.pam.entity.duty.DutyConfigDO;
import com.rs.module.pam.entity.duty.RoomAutoConfigDO;
import com.rs.module.pam.entity.information.PublishDO;
import com.rs.module.pam.service.duty.DutyConfigService;
import com.rs.module.pam.service.duty.RoomAutoConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "值班管理-值班规则配置")
@RestController
@RequestMapping("/pam/duty/config")
@Validated
public class DutyConfigController {

    @Resource
    private DutyConfigService dutyConfigService;
    @Resource
    private RoomAutoConfigService roomAutoConfigService;

    @PostMapping("/create")
    @ApiOperation(value = "创建值班规则配置")
    public CommonResult<String> createConfig(@Valid @RequestBody DutyConfigSaveReqVO createReqVO) {
        return success(dutyConfigService.createConfig(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改值班规则配置", hidden = true)
    public CommonResult<Boolean> updateConfig(@Valid @RequestBody DutyConfigSaveReqVO updateReqVO) {
        dutyConfigService.updateConfig(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获取值班规则配置")
    @ApiImplicitParam(name = "orgCode", value = "机构编号")
    public CommonResult<DutyConfigRespVO> getConfig(@RequestParam("orgCode") String orgCode) {
        DutyConfigDO config = dutyConfigService.getConfig(orgCode);
        return success(BeanUtils.toBean(config, DutyConfigRespVO.class));
    }


}
