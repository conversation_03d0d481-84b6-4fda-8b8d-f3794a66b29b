package com.rs.module.pam.controller.admin.psy.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评推送记录 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EvalPlanPushRecordRespVO extends BaseVO implements TransPojo {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("测评编号")
    private String evalNo;
    @ApiModelProperty("计划名称")
    private String planName;
    @ApiModelProperty("计划编码")
    private String planCode;
    @ApiModelProperty("计划类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_PSY_JHLX")
    private String planType;
    @ApiModelProperty("计划创建人")
    private String planAddUserName;
    @ApiModelProperty("量表ID")
    private String tableId;
    @ApiModelProperty("量表名称")
    private String tableName;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("推送时间")
    private Date pushTime;
    @ApiModelProperty("填写状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_PSY_TXZT")
    private String fillingStatus;
    @ApiModelProperty("填写时间")
    private Date fillingTime;
    @ApiModelProperty("填写平台")
    private String fillingPlatform;
    @ApiModelProperty("测评得分")
    private BigDecimal score;
    @ApiModelProperty("测评结果")
    private String evalResults;
    @ApiModelProperty("监室号")
    private String roomName;

}
