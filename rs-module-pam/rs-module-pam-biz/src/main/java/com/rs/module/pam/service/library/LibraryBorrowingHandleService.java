package com.rs.module.pam.service.library;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.pam.controller.admin.library.vo.LibraryBorrowingHandleListReqVO;
import com.rs.module.pam.controller.admin.library.vo.LibraryBorrowingHandlePageReqVO;
import com.rs.module.pam.controller.admin.library.vo.LibraryBorrowingHandleSaveReqVO;
import com.rs.module.pam.entity.library.LibraryBorrowingHandleDO;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 监所事务管理-图书借阅处理 Service 接口
 *
 * <AUTHOR>
 */
public interface LibraryBorrowingHandleService extends IBaseService<LibraryBorrowingHandleDO>{

    /**
     * 创建监所事务管理-图书借阅处理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createLibraryBorrowingHandle(@Valid LibraryBorrowingHandleSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-图书借阅处理
     *
     * @param updateReqVO 更新信息
     */
    void updateLibraryBorrowingHandle(@Valid LibraryBorrowingHandleSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-图书借阅处理
     *
     * @param id 编号
     */
    void deleteLibraryBorrowingHandle(String id);

    /**
     * 获得监所事务管理-图书借阅处理
     *
     * @param id 编号
     * @return 监所事务管理-图书借阅处理
     */
    LibraryBorrowingHandleDO getLibraryBorrowingHandle(String id);

    /**
    * 获得监所事务管理-图书借阅处理分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-图书借阅处理分页
    */
    PageResult<LibraryBorrowingHandleDO> getLibraryBorrowingHandlePage(LibraryBorrowingHandlePageReqVO pageReqVO);

    /**
    * 获得监所事务管理-图书借阅处理列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-图书借阅处理列表
    */
    List<LibraryBorrowingHandleDO> getLibraryBorrowingHandleList(LibraryBorrowingHandleListReqVO listReqVO);


    List<JSONObject> getBusinessAnalysis(Date time, String orgCode);
}
