package com.rs.module.pam.controller.admin.device.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-处理 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DeviceRepairRegHandleReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    @NotEmpty(message = "主键 不能为空")
    private String id;

    @ApiModelProperty("处理情况")
    private String handleRemark;

    @ApiModelProperty("处理照片地址")
    private String handleImgUrl;

}
