package com.rs.module.pam.service.daily;

import java.util.*;
import javax.validation.*;
import com.rs.module.pam.controller.admin.daily.vo.*;
import com.rs.module.pam.entity.daily.LifeTemplateDO;
import com.rs.module.pam.entity.daily.LifeTemplateEventDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 监所事务管理-一日生活制度模板 Service 接口
 *
 * <AUTHOR>
 */
public interface LifeTemplateService extends IBaseService<LifeTemplateDO>{

    /**
     * 创建监所事务管理-一日生活制度模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createLifeTemplate(@Valid LifeTemplateSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-一日生活制度模板
     *
     * @param updateReqVO 更新信息
     */
    void updateLifeTemplate(@Valid LifeTemplateSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-一日生活制度模板
     *
     * @param id 编号
     */
    void deleteLifeTemplate(String id);

    /**
     * 获得监所事务管理-一日生活制度模板
     *
     * @param id 编号
     * @return 监所事务管理-一日生活制度模板
     */
    LifeTemplateDO getLifeTemplate(String id);

    /**
    * 获得监所事务管理-一日生活制度模板分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-一日生活制度模板分页
    */
    PageResult<LifeTemplateDO> getLifeTemplatePage(LifeTemplatePageReqVO pageReqVO);

    /**
    * 获得监所事务管理-一日生活制度模板列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-一日生活制度模板列表
    */
    List<LifeTemplateDO> getLifeTemplateList(LifeTemplateListReqVO listReqVO);


    // ==================== 子表（监所事务管理-一日生活制度模板关联事务） ====================

    /**
     * 获得监所事务管理-一日生活制度模板关联事务列表
     *
     * @param templateId 模板ID
     * @return 监所事务管理-一日生活制度模板关联事务列表
     */
    List<LifeTemplateEventDO> getLifeTemplateEventListByTemplateId(String templateId);

    void updateLifeTemplateEventById(LifeTemplateEventSaveReqVO updateReqVO);

    void updateOneLifeTemplate(LifeTemplateOneSaveReqVO updateReqVO);
}
