package com.rs.module.pam.service.screen;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.entity.OpsDicCode;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.util.DicUtil;
import com.rs.module.pam.dao.screen.AqdtScreenDao;
import com.rs.module.pam.entity.screen.FocusOnPersonnelDO;
import com.rs.module.pam.entity.screen.WgdjDO;
import com.rs.util.ThreadPoolUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AqdtScreenServiceImpl implements AqdtScreenService {

    @Resource
    private AqdtScreenDao aqdtScreenDao;

    @Resource
    private GjLargeScreenService gjLargeScreenService;

    @Override
    public JSONObject jygl(String code) {
        JSONObject result = new JSONObject();
        // 羁押总数
        JSONObject json = aqdtScreenDao.jyCount(code);
        json.put("woman_count", json.getIntValue("jyzs") - json.getIntValue("man_count")); //女
        result.putAll(json);
        DecimalFormat df = new DecimalFormat("0.00");
        if (0 == json.getIntValue("jyzs")) {
            result.put("manPercent", "0.00");
            result.put("womanPercent", "0.00");
        } else {
            String manPercent = df.format(json.getIntValue("man_count") * 100d / json.getIntValue("jyzs"));
            result.put("manPercent", manPercent);
            result.put("womanPercent", df.format(100 - Double.parseDouble(manPercent)));
        }

        JSONObject orgInfo = checkJson(aqdtScreenDao.getOrgInfo(code));
        int sjrl = orgInfo.getIntValue("sjrl");
        if (0 == sjrl) {
            result.put("yrbPercent", "0.00");
        } else {
            result.put("yrbPercent", String.format("%.2f", json.getIntValue("jyzs") * 100f / sjrl));
        }
        JSONObject jydt = aqdtScreenDao.jydt(code);
        JSONObject brInfo = new JSONObject();
        brInfo.put("sy", jydt.getIntValue("jrsy"));
        brInfo.put("compareSy", jydt.getIntValue("jrsy") - jydt.getIntValue("zrsy"));
        brInfo.put("cs", jydt.getIntValue("jrcs"));
        brInfo.put("compareCs", jydt.getIntValue("jrcs") - jydt.getIntValue("zrcs"));
        result.put("day", brInfo); // 今日

        JSONObject bzInfo = new JSONObject();
        bzInfo.put("sy", jydt.getIntValue("bzsy"));
        bzInfo.put("compareSy", jydt.getIntValue("bzsy") - jydt.getIntValue("szsy"));
        bzInfo.put("cs", jydt.getIntValue("bzcs"));
        bzInfo.put("compareCs", jydt.getIntValue("bzcs") - jydt.getIntValue("szcs"));
        result.put("week", bzInfo); // 本周

        JSONObject byInfo = new JSONObject();
        byInfo.put("sy", jydt.getIntValue("bysy"));
        byInfo.put("compareSy", jydt.getIntValue("bysy") - jydt.getIntValue("sysy"));
        byInfo.put("cs", jydt.getIntValue("bycs"));
        byInfo.put("compareCs", jydt.getIntValue("bycs") - jydt.getIntValue("sycs"));
        result.put("month", byInfo); // 本月

        return result;
    }

    @Override
    public JSONObject jyrybhqs(String code) {
        JSONObject result = new JSONObject();
        JSONObject json = aqdtScreenDao.jyCount(code);
        int jyzs = json.getIntValue("jyzs");
        List<String> oneYearMonth = aqdtScreenDao.getOneYearYearMonth();
        CountDownLatch cdl = new CountDownLatch(oneYearMonth.size() - 1);
        ThreadPoolExecutor pool = ThreadPoolUtil.getPool();
        ConcurrentHashMap<String, Integer> map = new ConcurrentHashMap<>();
        for (int i = 0; i < oneYearMonth.size() - 1; i++) {
            final int index = i;
            pool.execute(() -> {
                try {
                    JSONObject monthAfterZyInfo = aqdtScreenDao.getMonthAfterZyInfo(code, oneYearMonth.get(index));
                    map.put(oneYearMonth.get(index), jyzs - monthAfterZyInfo.getIntValue("after_zy") + monthAfterZyInfo.getIntValue("before_zy"));
                } catch (Exception e) {
                    log.error(e.getMessage());
                } finally {
                    cdl.countDown();
                }
            });
        }
        try {
            cdl.await(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        map.put(oneYearMonth.get(oneYearMonth.size() - 1), jyzs);
        List<JSONObject> monthSy = aqdtScreenDao.getMonthSy(code);
        if (null == monthSy) {
            monthSy = new ArrayList<>();
        }
        Map<String, Integer> rsMonthMap = monthSy.stream().collect(Collectors.toMap(k -> k.getString("year_month"),
                v -> v.getIntValue("month_rs_count")));
        result.put("yearMonthList", oneYearMonth);
        List<Integer> currentMonthNewRsList = new ArrayList<>();
        List<Integer> currentMonthZyCountList = new ArrayList<>();
        for (String yearMonth : oneYearMonth) {
            currentMonthNewRsList.add(rsMonthMap.getOrDefault(yearMonth, 0));
            currentMonthZyCountList.add(map.get(yearMonth));
        }
        result.put("currentMonthNewRsList", currentMonthNewRsList);
        result.put("currentMonthZyCountList", currentMonthZyCountList);
        return result;
    }

    @Override
    public List<JSONObject> gjqryfb(String code) {
        return aqdtScreenDao.gjqryfb(code);
    }

    @Override
    public JSONObject nldfb(String code) {
        return aqdtScreenDao.nldfb(code);
    }

    @Override
    public JSONObject ssjdfb(List<String> list) {
        JSONObject json = aqdtScreenDao.getGaJcyFySshjTj(list);
        List<JSONObject> allSdhj = aqdtScreenDao.getAllSshj(list);
        List<OpsDicCode> dic = DicUtil.getDic("ZD_KSS_SSJD", "bsp");
        Map<String, String> dicMap = dic.stream().collect(Collectors.toMap(OpsDicCode::getCode, OpsDicCode::getName, (k1, k2) -> k2));
        List<JSONObject> sshjList = new ArrayList<>();
        for (JSONObject jsonObject : allSdhj) {
            String sshjName = dicMap.get(jsonObject.getString("sshj"));
            if (StringUtils.isNotEmpty(sshjName)) {
                jsonObject.put("sshjName", sshjName);
                sshjList.add(jsonObject);
            }
        }
        JSONObject result = new JSONObject();
        result.put("left_sshj", json);
        result.put("right_array_sshj", sshjList);
        return result;
    }

    @Override
    public List<JSONObject> ajfbTop5(List<String> list) {
        List<JSONObject> allSxzm = aqdtScreenDao.getAllSxzm(list);
        List<OpsDicCode> dic = DicUtil.getDic("ZD_SXZM", "bsp");
        Map<String, String> dicMap = dic.stream().collect(Collectors.toMap(OpsDicCode::getCode, OpsDicCode::getName, (k1, k2) -> k2));
        List<JSONObject> result = new ArrayList<>();
        int index = 0;
        for (JSONObject jsonObject : allSxzm) {
            String sxzm = dicMap.get(jsonObject.getString("sxzm"));
            if (StringUtils.isNotEmpty(sxzm)) {
                index++;
                if (index > 5) {
                    break;
                }
                jsonObject.put("sxzmName", sxzm);
                result.add(jsonObject);
            }
        }
        dicMap = null;
        dic = null;
        return result;
    }

    @Override
    public JSONObject jycqtj(String code) {
        return aqdtScreenDao.jycqtj(code);
    }

    @Override
    public JSONObject jqdcstj(String code) {
        return aqdtScreenDao.jqdcstj(code);
    }

    @Override
    public JSONObject fxryfb(String code) {
        return aqdtScreenDao.fxryfb(code);
    }

    @Override
    public JSONObject fxryqs(String code, String type) {
        List<String> monthDayList;
        if ("1".equals(type)) {
            monthDayList = aqdtScreenDao.getOneWeekMonthDay();
        } else {
            monthDayList = aqdtScreenDao.getOneMonthMonthDay();
        }
        List<JSONObject> fxryqsList = aqdtScreenDao.getFxryqsList(code);
        Map<String, Integer> tempMap = fxryqsList.stream().collect(Collectors.toMap(k -> k.getString("map_key"), v -> v.getIntValue("total")));
        List<Integer> risk1List = new ArrayList<>();
        List<Integer> risk2List = new ArrayList<>();
        List<Integer> risk3List = new ArrayList<>();
        for (String s : monthDayList) {
            risk1List.add(tempMap.getOrDefault(s + "_1", 0));
            risk2List.add(tempMap.getOrDefault(s + "_2", 0));
            risk3List.add(tempMap.getOrDefault(s + "_3", 0));
        }
        JSONObject result = new JSONObject();
        result.put("monthDayList", monthDayList);
        result.put("risk1List", risk1List);
        result.put("risk2List", risk2List);
        result.put("risk3List", risk3List);
        return result;
    }

    @Override
    public JSONObject gzryCount(String code) {
        List<String> methodNames = new ArrayList<>(Arrays.asList("getZdgzryDdgy", "getZdgzryJjsy", "getZdgzryZdry",
                "getZdgzryLsgd", "getZdgzryFxry", "getZdgzryZbh"));
        return gjLargeScreenService.getZdgzryAllCount(code, "03", methodNames);
    }

    @Override
    public PageResult<FocusOnPersonnelDO> gzryPage(int pageNo, int pageSize, String code, String bizType) {
        List<String> methodNames;
        if (StringUtils.isEmpty(bizType) || "all".equals(bizType)) {
            methodNames = new ArrayList<>(Arrays.asList("getZdgzryDdgy", "getZdgzryJjsy", "getZdgzryZdry",
                    "getZdgzryLsgd", "getZdgzryFxry", "getZdgzryZbh"));
        } else {
            methodNames = Collections.singletonList(bizType);
        }
        return gjLargeScreenService.getZdgzryAllPage(pageNo, pageSize, code, "03", methodNames);
    }

    @Override
    public JSONObject wgqs(String code, String type) {
        List<String> monthDayList;
        if ("1".equals(type)) {
            monthDayList = aqdtScreenDao.getOneWeekMonthDay();
        } else {
            monthDayList = aqdtScreenDao.getOneMonthMonthDay();
        }
        List<JSONObject> list = aqdtScreenDao.getWgqs(code);
        List<Integer> violationList = new ArrayList<>();
        Map<String, Integer> tmpMap = list.stream().collect(Collectors.toMap(k -> k.getString("month_day"), v -> v.getIntValue("total")));
        for (String monthDay : monthDayList) {
            violationList.add(tmpMap.getOrDefault(monthDay, 0));
        }
        JSONObject result = new JSONObject();
        result.put("monthDayList", monthDayList);
        result.put("violationList", violationList);

        return result;
    }

    @Override
    public PageResult<WgdjDO> wggjPage(int pageNo, int pageSize, String code, String type, String wglb, String handleStatus) {
        Page<WgdjDO> page = new Page<>(pageNo, pageSize);
        Page<WgdjDO> pageResult = aqdtScreenDao.getWggjPage(page, code, type, wglb, handleStatus);

        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    @Override
    public JSONObject wggjCount(String code, String type, String wglb) {
        return aqdtScreenDao.getWggjCount(code, type, wglb);
    }

    private JSONObject checkJson(JSONObject json) {
        if (Objects.isNull(json)) {
            json = new JSONObject();
        }
        return json;
    }
}
