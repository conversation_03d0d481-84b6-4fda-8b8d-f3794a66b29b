package com.rs.module.pam.service.duty;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.duty.vo.DutyShiftSaveReqVO;
import com.rs.module.pam.controller.admin.duty.vo.ShiftRespVO;
import com.rs.module.pam.entity.duty.ShiftDO;

import java.util.List;

/**
 * 监所事务管理-值班班次 Service 接口
 *
 * <AUTHOR>
 */
public interface ShiftService extends IBaseService<ShiftDO>{


    /**
     * 监室值班-班次管理-保存编辑
     *
     * @param reqVO
     */
    void shiftManageSave(DutyShiftSaveReqVO reqVO);

    /**
     * 获取监室的值班班次
     * @param orgCode
     * @param roomId
     * @return
     */
    List<ShiftRespVO> getShift(String orgCode, String roomId);


}
