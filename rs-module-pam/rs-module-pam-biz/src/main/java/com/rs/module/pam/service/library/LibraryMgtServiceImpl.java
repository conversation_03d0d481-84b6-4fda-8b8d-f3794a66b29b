package com.rs.module.pam.service.library;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.pam.controller.admin.library.vo.*;
import com.rs.module.pam.entity.library.LibraryMgtDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.pam.dao.library.LibraryMgtDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 监所事务管理-图书管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LibraryMgtServiceImpl extends BaseServiceImpl<LibraryMgtDao, LibraryMgtDO> implements LibraryMgtService {

    @Resource
    private LibraryMgtDao libraryMgtDao;

    @Resource
    private BorrowingConfigService borrowingConfigService;

    @Override
    public String createLibraryMgt(LibraryMgtSaveReqVO createReqVO) {
        // 插入
        LibraryMgtDO libraryMgt = BeanUtils.toBean(createReqVO, LibraryMgtDO.class);
        libraryMgtDao.insert(libraryMgt);
        borrowingConfigService.createBorrowingDefaultConfigByMgt();
        // 返回
        return libraryMgt.getId();
    }

    @Override
    public void updateLibraryMgt(LibraryMgtSaveReqVO updateReqVO) {
        // 校验存在
        validateLibraryMgtExists(updateReqVO.getId());
        // 更新
        LibraryMgtDO updateObj = BeanUtils.toBean(updateReqVO, LibraryMgtDO.class);
        libraryMgtDao.updateById(updateObj);
    }

    @Override
    public void deleteLibraryMgt(String id) {
        // 校验存在
        validateLibraryMgtExists(id);
        // 删除
        libraryMgtDao.deleteById(id);
    }

    private void validateLibraryMgtExists(String id) {
        if (libraryMgtDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-图书管理数据不存在");
        }
    }

    @Override
    public LibraryMgtDO getLibraryMgt(String id) {
        return libraryMgtDao.selectById(id);
    }

    @Override
    public PageResult<LibraryMgtDO> getLibraryMgtPage(LibraryMgtPageReqVO pageReqVO) {
        return libraryMgtDao.selectPage(pageReqVO);
    }

    @Override
    public List<LibraryMgtDO> getLibraryMgtList(LibraryMgtListReqVO listReqVO) {
        return libraryMgtDao.selectList(listReqVO);
    }

    @Override
    public void updateBorrowedInventory(String libraryId, int livraryCount) {
        libraryMgtDao.updateBorrowedInventory(libraryId, livraryCount);
    }

    @Override
    public PageResult<LibraryMgtDO> getAppLibraryMgtPage(int pageNo, int pageSize, String orgCode) {
        return libraryMgtDao.getAppLibraryMgtPage(pageNo, pageSize, orgCode);
    }

    @Override
    public void shelvingAndDelisting(LibraryMgtSXJReqVO updateReqVO) {
        LibraryMgtDO libraryMgtDO = libraryMgtDao.selectById(updateReqVO.getId());
        if (libraryMgtDO == null) {
            throw new ServerException("监所事务管理-图书管理数据不存在");
        }
        libraryMgtDO.setStatus(updateReqVO.getStatus());
        libraryMgtDao.updateById(libraryMgtDO);
    }


}
