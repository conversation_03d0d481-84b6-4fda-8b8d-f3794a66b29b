package com.rs.module.pam.dao.duty;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.pam.entity.duty.RecordsSigninDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 监所事务管理-监室值班记录关联签到 Dao
*
* <AUTHOR>
*/
@Mapper
public interface RecordsSigninDao extends IBaseDao<RecordsSigninDO> {

    List<RecordsSigninDO> getNowSigninByRecordsId(@Param("orgCode") String orgCode, @Param("roomId") String roomId,
                                              @Param("recordsId") String recordsId);

    List<RecordsSigninDO> getNowRecordsSignin(@Param("orgCode") String orgCode, @Param("roomId") String roomId);

}
