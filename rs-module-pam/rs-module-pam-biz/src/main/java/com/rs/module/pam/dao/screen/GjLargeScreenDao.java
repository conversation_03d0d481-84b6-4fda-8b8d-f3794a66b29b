package com.rs.module.pam.dao.screen;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.base.entity.pm.PrisonerTagDO;
import com.rs.module.pam.cons.CommonConstants;
import com.rs.module.pam.controller.admin.library.vo.LibraryMgtListReqVO;
import com.rs.module.pam.controller.admin.library.vo.LibraryMgtPageReqVO;
import com.rs.module.pam.entity.library.LibraryMgtDO;
import com.rs.module.pam.entity.screen.CommonMeetingDO;
import com.rs.module.pam.entity.screen.FocusOnPersonnelDO;
import com.rs.module.pam.entity.screen.WgdjDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 监所事务管理-图书管理 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface GjLargeScreenDao {

    // 获取羁押动态羁押数据
    JSONObject getJydtJyxx(@Param("code") String code, @Param("codeType") String codeType);

    // 获取风险人员分布情况
    JSONObject getFxryfb(@Param("code") String code, @Param("codeType") String codeType);

    // 调整入监室数据
    JSONObject getTzrjxx(@Param("code") String code, @Param("codeType") String codeType);

    // 调整出监室数据
    JSONObject getTzcjxx(@Param("code") String code, @Param("codeType") String codeType);

    int getWcdtTx(@Param("code") String code, @Param("codeType") String codeType);

    int getWcdtTj(@Param("code") String code, @Param("codeType") String codeType);

    int getWcdtLshj(@Param("code") String code, @Param("codeType") String codeType);

    int getWcdtJshj(@Param("code") String code, @Param("codeType") String codeType);

    int getWcdtJshjVideo(@Param("code") String code, @Param("codeType") String codeType);

    int getWcdtSglshj(@Param("code") String code, @Param("codeType") String codeType);

    int getWcdtCsjy(@Param("code") String code, @Param("codeType") String codeType);

    //提解出所未归
    int tjcswgCount(@Param("code") String code, @Param("codeType") String codeType);

    // 出所就医未归
    int csjywgCount(@Param("code") String code, @Param("codeType") String codeType);

    // 临时出所未归
    //int lscswgCount(@Param("code") String code, @Param("codeType") String codeType);

    // 重点关注人员-单独关押
    Page<FocusOnPersonnelDO> getZdgzryDdgy(Page<FocusOnPersonnelDO> page, @Param("code") String code, @Param("codeType") String codeType);

    // 查询人员标签
    List<PrisonerTagDO> getTagListByJgrybmList(@Param("jgrybmList") List<String> jgrybmList);

    // 重点关注人员-戒具使用
    Page<FocusOnPersonnelDO> getZdgzryJjsy(Page<FocusOnPersonnelDO> page, @Param("code") String code, @Param("codeType") String codeType);

    // 重点关注人员-临时固定
    Page<FocusOnPersonnelDO> getZdgzryLsgd(Page<FocusOnPersonnelDO> page, @Param("code") String code, @Param("codeType") String codeType);

    // 重点关注人员-重点人员
    Page<FocusOnPersonnelDO> getZdgzryZdry(Page<FocusOnPersonnelDO> page, @Param("code") String code, @Param("codeType") String codeType);

    // 重点关注人员-外籍人员  中国'156', 台湾'158', 香港'344', 澳门'446'
    Page<FocusOnPersonnelDO> getZdgzryWjry(Page<FocusOnPersonnelDO> page, @Param("code") String code, @Param("codeType") String codeType);

    // 重点关注人员-今日到期
    Page<FocusOnPersonnelDO> getZdgzryJrdq(Page<FocusOnPersonnelDO> page, @Param("code") String code, @Param("codeType") String codeType);

    // 重点关注人员-重病号
    Page<FocusOnPersonnelDO> getZdgzryZbh(Page<FocusOnPersonnelDO> page, @Param("code") String code, @Param("codeType") String codeType);

    // 重点关注人员-风险人员
    Page<FocusOnPersonnelDO> getZdgzryFxry(Page<FocusOnPersonnelDO> page, @Param("code") String code, @Param("codeType") String codeType);

    // 重点关注人员-全部人员列表
    Page<FocusOnPersonnelDO> getZdgzryAll(Page<FocusOnPersonnelDO> page, @Param("list") List<String> list);

    // 获取监所的 待提出 待提回 数据
    JSONObject getDtcDthCountByOrgCode(@Param("orgCode") String orgCode);

    // 获取监所的今日会见列表
    Page<CommonMeetingDO> getTodayMeetingPage(Page<CommonMeetingDO> page, @Param("orgCode") String orgCode);


    List<JSONObject> getJsswItems(@Param("list") List<String> list, @Param("code") String code, @Param("codeType") String codeType);

    List<JSONObject> getJsswItemArray(@Param("list") List<String> list, @Param("code") String code, @Param("codeType") String codeType);

    Page<WgdjDO> getWggjPage(Page<WgdjDO> page, @Param("timeRange") String timeRange,
                             @Param("handleStatus") String handleStatus, @Param("code") String code,
                             @Param("codeType") String codeType);

    JSONObject getWggjAllCount(@Param("timeRange") String timeRange, @Param("code") String code, @Param("codeType") String codeType);


}
