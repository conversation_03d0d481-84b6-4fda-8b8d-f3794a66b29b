package com.rs.module.pam.controller.admin.screen;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.pam.service.screen.AqdtScreenService;
import com.rs.module.pam.service.screen.ZdJydtScreenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "大屏-数字孪生平台-总队羁押动态")
@RestController
@RequestMapping("/pam/screen/zdjydt")
@Validated
public class ZdJydtScreenController {

    @Autowired
    private ZdJydtScreenService zdJydtScreenService;


    @GetMapping("/jydt-ylqk-jyb")
    @ApiOperation(value = "羁押动态/各监所押量情况/警押比")
    public CommonResult<JSONObject> jydt() {
        return success(zdJydtScreenService.jydt());
    }

    @GetMapping("/ssjdfb")
    @ApiOperation(value = "诉讼阶段分布")
    public CommonResult<JSONObject> ssjdfb() {
        return success(zdJydtScreenService.ssjdfb());
    }

    @GetMapping("/ajfbTop5")
    @ApiOperation(value = "案件分布top5")
    public CommonResult<List<JSONObject>> ajfbTop5() {
        return success(zdJydtScreenService.ajfbTop5());
    }

    @GetMapping("/syAndCs")
    @ApiOperation(value = "新收人员-收押/出所人员-出所")
    public CommonResult<JSONObject> syAndCs() {
        return success(zdJydtScreenService.syAndCs());
    }

}
