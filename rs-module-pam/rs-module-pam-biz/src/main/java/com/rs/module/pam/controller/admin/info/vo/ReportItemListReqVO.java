package com.rs.module.pam.controller.admin.info.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 监所事务管理-信息报备事项配置列表 Request VO")
@Data
public class ReportItemListReqVO {

    @ApiModelProperty("报备类别")
    private String reportType;

    @ApiModelProperty("报备事由")
    private String reportContent;

    @ApiModelProperty("状态（1：启用，0：禁用）")
    private String status;

    @ApiModelProperty("是否参与点名(1: 参与，0: 不参与)")
    private Integer isRollcall;

    @ApiModelProperty("监所编号")
    private String orgCode;

}
