package com.rs.module.pam.dao.cook;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.cook.vo.CateListReqVO;
import com.rs.module.pam.entity.cook.CateDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 监所事务管理-菜品分类 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface CateDao extends IBaseDao<CateDO> {

    default List<CateDO> selectList(CateListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CateDO>()
                .eqIfPresent(CateDO::getOrgCode, reqVO.getOrgCode())
                .likeIfPresent(CateDO::getCateName, reqVO.getCateName())
                .orderByAsc(CateDO::getAddTime));
    }

}
