package com.rs.module.pam.service.daily;

import java.util.*;
import javax.validation.*;
import com.rs.module.pam.controller.admin.daily.vo.*;
import com.rs.module.pam.entity.daily.LifeHolidaysDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 监所事务管理-一日生活制度节假日 Service 接口
 *
 * <AUTHOR>
 */
public interface LifeHolidaysService extends IBaseService<LifeHolidaysDO>{

    /**
     * 创建监所事务管理-一日生活制度节假日
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createLifeHolidays(@Valid LifeHolidaysSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-一日生活制度节假日
     *
     * @param updateReqVO 更新信息
     */
    void updateLifeHolidays(@Valid LifeHolidaysSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-一日生活制度节假日
     *
     * @param id 编号
     */
    void deleteLifeHolidays(String id);

    /**
     * 获得监所事务管理-一日生活制度节假日
     *
     * @param id 编号
     * @return 监所事务管理-一日生活制度节假日
     */
    LifeHolidaysDO getLifeHolidays(String id);

    /**
    * 获得监所事务管理-一日生活制度节假日分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-一日生活制度节假日分页
    */
    PageResult<LifeHolidaysDO> getLifeHolidaysPage(LifeHolidaysPageReqVO pageReqVO);

    /**
    * 获得监所事务管理-一日生活制度节假日列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-一日生活制度节假日列表
    */
    List<LifeHolidaysDO> getLifeHolidaysList(LifeHolidaysListReqVO listReqVO);


    void batchSaveLifeHolidays(LifeHolidaysBatchSaveReqVO batchSaveReqVO);
}
