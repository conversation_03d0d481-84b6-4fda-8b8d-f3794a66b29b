package com.rs.module.pam.entity.duty;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import java.util.Date;

/**
 * 监所事务管理-监室值班记录 DO
 *
 * <AUTHOR>
 */
@TableName("pam_duty_records")
@KeySequence("pam_duty_records_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecordsDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 值班ID
     */
    private String dutyId;
    /**
     * 值班日期
     */
    private Date dutyDate;
    /**
     * 班次id
     */
    private String shiftId;
    /**
     * 班次名称
     */
    private String shiftName;
    /**
     * 班次开始时间
     */
    private Date shiftStartTime;
    /**
     * 班次结束时间
     */
    private Date shiftEndTime;
    /**
     * 值班组id
     */
    private String groupId;
    /**
     * 值班组名（规则：两个被监管人员的姓名的首字母组合）
     */
    private String groupName;
    /**
     * 监管人员编码
     */
    private String jgrybm1;
    /**
     * 监管人员编码
     */
    private String jgrybm2;
    /**
     * 排班人ID
     */
    private String assignerUserSfzh;
    /**
     * 排班人名称
     */
    private String assignerUserName;
    /**
     * 排班时间
     */
    private Date assignedTime;

}
