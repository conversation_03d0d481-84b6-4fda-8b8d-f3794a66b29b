package com.rs.module.pam.controller.admin.daily;

import com.bsp.security.util.SessionUserUtil;
import com.rs.module.pam.enums.DailyLifeEventStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.pam.controller.admin.daily.vo.*;
import com.rs.module.pam.entity.daily.LifeDictEventDO;
import com.rs.module.pam.service.daily.LifeDictEventService;

@Api(tags = "监所事务管理-事务选项字典")
@RestController
@RequestMapping("/pam/daily/lifeDictEvent")
@Validated
public class LifeDictEventController {

    @Resource
    private LifeDictEventService lifeDictEventService;

    @PostMapping("/create")
    @ApiOperation(value = "创建监所事务管理-事务选项字典")
    public CommonResult<String> createLifeDictEvent(@Valid @RequestBody LifeDictEventSaveReqVO createReqVO) {
        return success(lifeDictEventService.createLifeDictEvent(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新监所事务管理-事务选项字典")
    public CommonResult<Boolean> updateLifeDictEvent(@Valid @RequestBody LifeDictEventSaveReqVO updateReqVO) {
        lifeDictEventService.updateLifeDictEvent(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除监所事务管理-事务选项字典")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteLifeDictEvent(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            lifeDictEventService.deleteLifeDictEvent(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得监所事务管理-事务选项字典")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<LifeDictEventRespVO> getLifeDictEvent(@RequestParam("id") String id) {
        LifeDictEventDO lifeDictEvent = lifeDictEventService.getLifeDictEvent(id);
        return success(BeanUtils.toBean(lifeDictEvent, LifeDictEventRespVO.class));
    }

    /*@PostMapping("/page")
    @ApiOperation(value = "获得监所事务管理-事务选项字典分页")
    public CommonResult<PageResult<LifeDictEventRespVO>> getLifeDictEventPage(@Valid @RequestBody LifeDictEventPageReqVO pageReqVO) {
        PageResult<LifeDictEventDO> pageResult = lifeDictEventService.getLifeDictEventPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LifeDictEventRespVO.class));
    }*/

    @PostMapping("/list")
    @ApiOperation(value = "获得监所事务管理-事务选项字典列表")
    public CommonResult<List<LifeDictEventRespVO>> getLifeDictEventList(@Valid @RequestBody LifeDictEventListReqVO listReqVO) {
        if (StringUtils.isEmpty(listReqVO.getOrgCode())) {
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        if (Objects.isNull(listReqVO.getEventStatus())) {
            listReqVO.setEventStatus(Short.parseShort(DailyLifeEventStatusEnum.QY.getCode()));
        }
        List<LifeDictEventDO> list = lifeDictEventService.getLifeDictEventList(listReqVO);
        return success(BeanUtils.toBean(list, LifeDictEventRespVO.class));
    }
}
