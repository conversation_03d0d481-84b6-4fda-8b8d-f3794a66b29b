package com.rs.module.pam.controller.app.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/7/1 17:03
 */
@ApiModel(description = "外屏 - 信息报备 Request VO")
@Data
public class AppReportListSowRespVO implements TransPojo {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty("报备时间")
    private Date reportTime;
    @ApiModelProperty("报备事由")
    private String reportContent;
    @ApiModelProperty("报备状态（字典：ZD_XXBB_BBZT）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XXBB_BBZT")
    private String status;
    @ApiModelProperty("报备时间类型（字典：ZD_XXBB_BBSJLX）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XXBB_BBSJLX")
    private String reportTimeType;
    @ApiModelProperty("报备开始时间")
    private Date reportStartTime;
    @ApiModelProperty("报备结束时间")
    private Date reportEndTime;
    @ApiModelProperty("性别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XB", ref = "xbName")
    private String xb;
    private String xbName;
    @ApiModelProperty("年龄")
    private String age;
    @ApiModelProperty("姓名")
    private String xm;
}
