package com.rs.module.pam.controller.admin.duty.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel(description = "管理后台 - 监所事务管理-监室自动排班配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RoomAutoConfigRespVO extends BaseVO implements TransPojo {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("机构编号")
    private String orgCode;

    @ApiModelProperty("自动排班规则，多个逗号分割（字典：ZD_ZBGL_ZDPBGZ）")
    @Trans(type = TransType.DICTIONARY,key = "ZD_ZBGL_ZDPBGZ")
    private String schedulingRule;

    @ApiModelProperty("是否启用")
    private Short isEnabled;

}
