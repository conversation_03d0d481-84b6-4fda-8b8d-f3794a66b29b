package com.rs.module.pam.controller.admin.library;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.pam.controller.admin.library.vo.*;
import com.rs.module.pam.entity.library.LibraryBorrowingDO;
import com.rs.module.pam.service.library.LibraryBorrowingService;

@Api(tags = "监所事务管理-图书借阅申请")
@RestController
@RequestMapping("/pam/library/libraryBorrowing")
@Validated
public class LibraryBorrowingController {

    @Resource
    private LibraryBorrowingService libraryBorrowingService;

//    @PostMapping("/create")
//    @ApiOperation(value = "创建监所事务管理-图书借阅申请")
//    public CommonResult<String> createLibraryBorrowing(@Valid @RequestBody LibraryBorrowingSaveReqVO createReqVO) {
//        return success(libraryBorrowingService.createLibraryBorrowing(createReqVO));
//    }

    @PostMapping("/update")
    @ApiOperation(value = "更新监所事务管理-图书借阅申请")
    public CommonResult<Boolean> updateLibraryBorrowing(@Valid @RequestBody LibraryBorrowingSaveReqVO updateReqVO) {
        libraryBorrowingService.updateLibraryBorrowing(updateReqVO);
        return success(true);
    }

    @PostMapping("/approval")
    @ApiOperation(value = "审核")
    public CommonResult<Boolean> approvalLibraryBorrowing(@Valid @RequestBody LibraryBorrowingApprovalReqVO approvalReqVO) {
        libraryBorrowingService.approvalLibraryBorrowing(approvalReqVO);
        return success(true);
    }

    @GetMapping("/businessAnalysis")
    @ApiOperation(value = "业务统计")
    public CommonResult<Object> businessAnalysis(@RequestParam(required = false) String orgCode) {
        return success(libraryBorrowingService.businessAnalysis(orgCode));
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除监所事务管理-图书借阅申请")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteLibraryBorrowing(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           libraryBorrowingService.deleteLibraryBorrowing(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得监所事务管理-图书借阅申请")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<LibraryBorrowingRespVO> getLibraryBorrowing(@RequestParam("id") String id) {
        LibraryBorrowingDO libraryBorrowing = libraryBorrowingService.getLibraryBorrowing(id);
        return success(BeanUtils.toBean(libraryBorrowing, LibraryBorrowingRespVO.class));
    }

    /*@PostMapping("/page")
    @ApiOperation(value = "获得监所事务管理-图书借阅申请分页")
    public CommonResult<PageResult<LibraryBorrowingRespVO>> getLibraryBorrowingPage(@Valid @RequestBody LibraryBorrowingPageReqVO pageReqVO) {
        PageResult<LibraryBorrowingDO> pageResult = libraryBorrowingService.getLibraryBorrowingPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LibraryBorrowingRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得监所事务管理-图书借阅申请列表")
    public CommonResult<List<LibraryBorrowingRespVO>> getLibraryBorrowingList(@Valid @RequestBody LibraryBorrowingListReqVO listReqVO) {
        List<LibraryBorrowingDO> list = libraryBorrowingService.getLibraryBorrowingList(listReqVO);
        return success(BeanUtils.toBean(list, LibraryBorrowingRespVO.class));
    }*/
}
