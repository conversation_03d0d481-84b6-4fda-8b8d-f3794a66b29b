package com.rs.module.pam.dao.psy;

import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableQuestionListReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableQuestionPageReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableQuestionRespVO;
import com.rs.module.pam.entity.psy.EvalTableQuestionDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 监所事务管理-心理测评量表关联提目 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface EvalTableQuestionDao extends IBaseDao<EvalTableQuestionDO> {


    default PageResult<EvalTableQuestionDO> selectPage(EvalTableQuestionPageReqVO reqVO) {
        Page<EvalTableQuestionDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<EvalTableQuestionDO> wrapper = new LambdaQueryWrapperX<EvalTableQuestionDO>()
                .eqIfPresent(EvalTableQuestionDO::getTableId, reqVO.getTableId())
                .eqIfPresent(EvalTableQuestionDO::getQuestionType, reqVO.getQuestionType())
                .eqIfPresent(EvalTableQuestionDO::getScoreRule, reqVO.getScoreRule());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByAsc(EvalTableQuestionDO::getQuestionType, EvalTableQuestionDO::getSortOrder);
        }
        Page<EvalTableQuestionDO> evalTableQuestionPage = selectPage(page, wrapper);
        return new PageResult<>(evalTableQuestionPage.getRecords(), evalTableQuestionPage.getTotal());
    }

    default List<EvalTableQuestionDO> selectList(EvalTableQuestionListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<EvalTableQuestionDO>()
                .eqIfPresent(EvalTableQuestionDO::getTableId, reqVO.getTableId())
                .eqIfPresent(EvalTableQuestionDO::getQuestionText, reqVO.getQuestionText())
                .eqIfPresent(EvalTableQuestionDO::getQuestionType, reqVO.getQuestionType())
                .eqIfPresent(EvalTableQuestionDO::getScoreRule, reqVO.getScoreRule())
                .eqIfPresent(EvalTableQuestionDO::getScoreRule, reqVO.getScoreRule())
                .orderByAsc(EvalTableQuestionDO::getQuestionType, EvalTableQuestionDO::getSortOrder));
    }

    @Select("SELECT MAX(sort_order) FROM pam_psy_eval_table_question WHERE table_id = #{tableId} AND is_del = '0'")
    Integer getMaxSortByTableId(@Param("tableId") String tableId);

    @Update("UPDATE pam_psy_eval_table_question SET sort_order = sort_order  - 1 " +
            "WHERE table_id = #{tableId} AND sort_order  >= #{targetPosition}")
    void incrementOrderNumbers(@Param("tableId") String tableId,
                               @Param("targetPosition") int targetPosition);

    /**
     * 根据量表id删除关联选项
     *
     * @param id
     */
    @Delete("DELETE FROM pam_psy_eval_table_question WHERE id = #{id}")
    void deleteEvalTableQuestionDOById(@Param("id") String id);


    List<EvalTableQuestionRespVO> getEvalTableQuestionListRespVO(@Param(Constants.WRAPPER) EvalTableQuestionListReqVO pageReqVO);


}
