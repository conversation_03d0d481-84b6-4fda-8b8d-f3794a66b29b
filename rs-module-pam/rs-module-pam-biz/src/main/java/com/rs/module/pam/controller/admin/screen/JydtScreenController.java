package com.rs.module.pam.controller.admin.screen;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.pam.service.screen.AqdtScreenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.util.Collections;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "大屏-数字孪生平台-羁押动态")
@RestController
@RequestMapping("/pam/screen/jydt")
@Validated
public class JydtScreenController {

    @Resource
    private AqdtScreenService aqdtScreenService;

    @GetMapping("/jygl")
    @ApiOperation(value = "羁押概览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    public CommonResult<JSONObject> jygl(@RequestParam("code") String code) {
        Assert.notBlank(code, "code不能为空");
        return success(aqdtScreenService.jygl(code));
    }

    @GetMapping("/jyrybhqs")
    @ApiOperation(value = "羁押人员变化趋势")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    public CommonResult<JSONObject> jyrybhqs(@RequestParam("code") String code) {
        Assert.notBlank(code, "code不能为空");
        return success(aqdtScreenService.jyrybhqs(code));
    }

    @GetMapping("/gjqryfb")
    @ApiOperation(value = "各监区人员分布")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    public CommonResult<List<JSONObject>> gjqryfb(@RequestParam("code") String code) {
        Assert.notBlank(code, "code不能为空");
        return success(aqdtScreenService.gjqryfb(code));
    }

    @GetMapping("/nldfb")
    @ApiOperation(value = "年龄段分布")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    public CommonResult<JSONObject> nldfb(@RequestParam("code") String code) {
        Assert.notBlank(code, "code不能为空");
        return success(aqdtScreenService.nldfb(code));
    }

    @GetMapping("/ssjdfb")
    @ApiOperation(value = "诉讼阶段分布")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    public CommonResult<JSONObject> ssjdfb(@RequestParam("code") String code) {
        Assert.notBlank(code, "code不能为空");
        return success(aqdtScreenService.ssjdfb(Collections.singletonList(code)));
    }

    @GetMapping("/ajfbTop5")
    @ApiOperation(value = "案件分布top5")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    public CommonResult<List<JSONObject>> ajfbTop5(@RequestParam("code") String code) {
        Assert.notBlank(code, "code不能为空");
        return success(aqdtScreenService.ajfbTop5(Collections.singletonList(code)));
    }

    @GetMapping("/jycqtj")
    @ApiOperation(value = "羁押超期统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    public CommonResult<JSONObject> jycqtj(@RequestParam("code") String code) {
        Assert.notBlank(code, "code不能为空");
        return success(aqdtScreenService.jycqtj(code));
    }

    @GetMapping("/jqdcstj")
    @ApiOperation(value = "近期待出所统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    public CommonResult<JSONObject> jqdcstj(@RequestParam("code") String code) {
        Assert.notBlank(code, "code不能为空");
        return success(aqdtScreenService.jqdcstj(code));
    }

}
