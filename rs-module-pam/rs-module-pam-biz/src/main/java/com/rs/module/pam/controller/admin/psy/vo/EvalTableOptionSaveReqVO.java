package com.rs.module.pam.controller.admin.psy.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评量表关联提目选项新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EvalTableOptionSaveReqVO extends BaseVO{
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("选项代码，A、B、C、D等")
    private String optionCode;

    @ApiModelProperty("选项内容")
    @NotEmpty(message = "选项内容不能为空")
    private String optionText;

    @ApiModelProperty("题目选项分值")
    @NotNull(message = "题目选项分值不能为空")
    private BigDecimal score;

    @ApiModelProperty("选项顺序")
    @NotNull(message = "选项顺序不能为空")
    private Integer sortOrder;

}
