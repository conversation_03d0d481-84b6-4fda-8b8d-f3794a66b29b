package com.rs.module.pam.controller.admin.represent;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.represent.vo.RepresentConfigListReqVO;
import com.rs.module.pam.controller.admin.represent.vo.RepresentConfigPageReqVO;
import com.rs.module.pam.controller.admin.represent.vo.RepresentConfigRespVO;
import com.rs.module.pam.controller.admin.represent.vo.RepresentConfigSaveReqVO;
import com.rs.module.pam.entity.represent.RepresentConfigDO;
import com.rs.module.pam.service.represent.RepresentConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监室点名计划配置")
@RestController
@RequestMapping("/pam/represent/config")
@Validated
public class RepresentConfigController {

    @Resource
    private RepresentConfigService representConfigService;

    @PostMapping("/create")
    @ApiOperation(value = "创建监室点名计划")
    public CommonResult<String> createConfig(@Valid @RequestBody RepresentConfigSaveReqVO createReqVO) {
        return success(representConfigService.createConfig(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新监室点名计划", hidden = true)
    public CommonResult<Boolean> updateConfig(@Valid @RequestBody RepresentConfigSaveReqVO updateReqVO) {
        representConfigService.updateConfig(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除监室点名计划")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteConfig(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           representConfigService.deleteConfig(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得监室点名计划")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<RepresentConfigRespVO> getConfig(@RequestParam("id") String id) {
        RepresentConfigDO config = representConfigService.getConfig(id);
        return success(BeanUtils.toBean(config, RepresentConfigRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得监所事务管理-监室点名配置分页", hidden = true)
    public CommonResult<PageResult<RepresentConfigRespVO>> getConfigPage(@Valid @RequestBody RepresentConfigPageReqVO pageReqVO) {
        PageResult<RepresentConfigDO> pageResult = representConfigService.getConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RepresentConfigRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得监所事务管理-监室点名配置列表", hidden = true)
    public CommonResult<List<RepresentConfigRespVO>> getConfigList(@Valid @RequestBody RepresentConfigListReqVO listReqVO) {
        List<RepresentConfigDO> list = representConfigService.getConfigList(listReqVO);
        return success(BeanUtils.toBean(list, RepresentConfigRespVO.class));
    }
}
