package com.rs.module.pam.controller.admin.represent;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.represent.vo.CheckListReqVO;
import com.rs.module.pam.controller.admin.represent.vo.CheckPageReqVO;
import com.rs.module.pam.controller.admin.represent.vo.CheckRespVO;
import com.rs.module.pam.controller.admin.represent.vo.CheckSaveReqVO;
import com.rs.module.pam.entity.represent.CheckDO;
import com.rs.module.pam.service.represent.CheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监所事务管理-人脸库及人员库信息进行比对")
@RestController
@RequestMapping("/pam/represent/check")
@Validated
public class CheckController {

    @Resource
    private CheckService checkService;

    @PostMapping("/create")
    @ApiOperation(value = "创建监所事务管理-人脸库及人员库信息进行比对")
    public CommonResult<String> createCheck(@Valid @RequestBody CheckSaveReqVO createReqVO) {
        return success(checkService.createCheck(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新监所事务管理-人脸库及人员库信息进行比对")
    public CommonResult<Boolean> updateCheck(@Valid @RequestBody CheckSaveReqVO updateReqVO) {
        checkService.updateCheck(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除监所事务管理-人脸库及人员库信息进行比对")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteCheck(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           checkService.deleteCheck(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得监所事务管理-人脸库及人员库信息进行比对")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<CheckRespVO> getCheck(@RequestParam("id") String id) {
        CheckDO check = checkService.getCheck(id);
        return success(BeanUtils.toBean(check, CheckRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得监所事务管理-人脸库及人员库信息进行比对分页")
    public CommonResult<PageResult<CheckRespVO>> getCheckPage(@Valid @RequestBody CheckPageReqVO pageReqVO) {
        PageResult<CheckDO> pageResult = checkService.getCheckPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CheckRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得监所事务管理-人脸库及人员库信息进行比对列表")
    public CommonResult<List<CheckRespVO>> getCheckList(@Valid @RequestBody CheckListReqVO listReqVO) {
        List<CheckDO> list = checkService.getCheckList(listReqVO);
        return success(BeanUtils.toBean(list, CheckRespVO.class));
    }
}
