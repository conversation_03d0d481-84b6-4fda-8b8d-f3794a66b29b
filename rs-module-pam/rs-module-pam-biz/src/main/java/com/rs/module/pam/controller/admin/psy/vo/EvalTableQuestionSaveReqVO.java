package com.rs.module.pam.controller.admin.psy.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评量表关联提目新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EvalTableQuestionSaveReqVO extends BaseVO {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("量表ID")
    @NotBlank(message = "量表ID不能为空")
    private String tableId;

    @ApiModelProperty("题目内容")
    @NotBlank(message = "题目内容不能为空")
    private String questionText;

    @ApiModelProperty("题目类型（字典：ZD_XLPC_TMLX）")
    @NotEmpty(message = "题目类型（字典：ZD_XLPC_TMLX）不能为空")
    private String questionType;

    @ApiModelProperty("计分规则")
    private String scoreRule;

    @ApiModelProperty("参考答案，选择题答案保存选项代码，多选题多个答案逗号分割，简答题保存答案")
    private String answer;

    @ApiModelProperty("题目分值")
    @NotNull(message = "题目分值不能为空")
    private BigDecimal score;

    @ApiModelProperty("题目顺序")
    @NotNull(message = "题目顺序不能为空")
    private Integer sortOrder;

    @ApiModelProperty("题目选项")
    private List<EvalTableOptionSaveReqVO> options;
}
