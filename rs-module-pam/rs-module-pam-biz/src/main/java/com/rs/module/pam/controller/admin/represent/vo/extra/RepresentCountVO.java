package com.rs.module.pam.controller.admin.represent.vo.extra;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel("点名统计信息vo")
public class RepresentCountVO {
    /**
     * 进行中
     */
    @ApiModelProperty(value = "进行中")
    private Integer inRepresent;
    /**
     * 已结束
     */
    @ApiModelProperty(value = "已结束")
    private Integer endRepresent;
    /**
     * 异常待处置
     */
    @ApiModelProperty(value = "异常待处置")
    private Integer errorRepresent;
}
