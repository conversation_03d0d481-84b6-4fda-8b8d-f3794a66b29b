package com.rs.module.pam.controller.admin.cook.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@ApiModel(description = "管理后台 - 监所事务管理-特殊餐申请-医生审批-审批/修改 Request VO")
@Data
public class SpecialApplyDoctorApproveReqVO {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("医生审批结果")
    @NotBlank(message = "医生审批结果不能为空")
    private String doctorApprovalResult;
    @ApiModelProperty("医生审核意见")
    private String doctorApprovalComments;

}
