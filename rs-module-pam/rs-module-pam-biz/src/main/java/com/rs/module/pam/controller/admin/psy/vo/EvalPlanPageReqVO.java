package com.rs.module.pam.controller.admin.psy.vo;

import io.swagger.annotations.ApiModel;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;

import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评计划分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EvalPlanPageReqVO extends PageParam {
    @ApiModelProperty("计划名称")
    private String planName;

    @ApiModelProperty("计划编码")
    private String planCode;

    @ApiModelProperty("计划类型")
    private String planType;

    @ApiModelProperty("计划说明")
    private String remark;

    @ApiModelProperty("量表ID")
    private String tableId;

    @ApiModelProperty("量表名称")
    private String tableName;

    @ApiModelProperty("触发类型")
    private String triggerType;

    @ApiModelProperty("触发配置，json存储")
    private String triggerConfig;

    @ApiModelProperty("推送对象")
    private String pushTarget;

    @ApiModelProperty("测评次数")
    private Integer evalNumber;

    @ApiModelProperty("完成时限")
    private Integer completionDeadline;

    @ApiModelProperty("启动消息推送")
    private String enableMessagePush;

    @ApiModelProperty("消息推送配置")
    private String messagePush;

    @ApiModelProperty("报备状态（字典：ZD_XXBB_BBZT）")
    private String status;

    @ApiModelProperty("启用状态")
    private String enableStatus;

    @ApiModelProperty("排序属性")
    List<OrderItem> orderFields;
}
