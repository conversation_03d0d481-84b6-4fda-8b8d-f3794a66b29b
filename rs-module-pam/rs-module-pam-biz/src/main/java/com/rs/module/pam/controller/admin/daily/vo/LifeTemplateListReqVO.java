package com.rs.module.pam.controller.admin.daily.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 监所事务管理-一日生活制度模板列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LifeTemplateListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("制度名称")
    private String templateName;

    @ApiModelProperty("机构码")
    private String orgCode;

    @ApiModelProperty("县分局码")
    private String regCode;

    @ApiModelProperty("市局码")
    private String cityCode;

}
