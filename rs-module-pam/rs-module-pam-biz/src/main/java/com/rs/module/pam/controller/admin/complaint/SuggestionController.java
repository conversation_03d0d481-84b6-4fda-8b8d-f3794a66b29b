package com.rs.module.pam.controller.admin.complaint;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.pam.controller.admin.complaint.vo.*;
import com.rs.module.pam.entity.complaint.SuggestionDO;
import com.rs.module.pam.service.complaint.SuggestionService;

@Api(tags = "监所事务管理-投诉建议")
@RestController
@RequestMapping("/pam/complaint/suggestion")
@Validated
public class SuggestionController {

    @Resource
    private SuggestionService suggestionService;

    @PostMapping("/create")
    @ApiOperation(value = "创建监所事务管理-投诉建议")
    public CommonResult<String> createSuggestion(@Valid @RequestBody SuggestionSaveReqVO createReqVO) {
        return success(suggestionService.createSuggestion(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新监所事务管理-投诉建议")
    public CommonResult<Boolean> updateSuggestion(@Valid @RequestBody SuggestionSaveReqVO updateReqVO) {
        suggestionService.updateSuggestion(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除监所事务管理-投诉建议")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteSuggestion(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           suggestionService.deleteSuggestion(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得监所事务管理-投诉建议")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<SuggestionRespVO> getSuggestion(@RequestParam("id") String id) {
        SuggestionDO suggestion = suggestionService.getSuggestion(id);
        return success(BeanUtils.toBean(suggestion, SuggestionRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得监所事务管理-投诉建议分页")
    public CommonResult<PageResult<SuggestionRespVO>> getSuggestionPage(@Valid @RequestBody SuggestionPageReqVO pageReqVO) {
        PageResult<SuggestionDO> pageResult = suggestionService.getSuggestionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SuggestionRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得监所事务管理-投诉建议列表")
    public CommonResult<List<SuggestionRespVO>> getSuggestionList(@Valid @RequestBody SuggestionListReqVO listReqVO) {
        List<SuggestionDO> list = suggestionService.getSuggestionList(listReqVO);
        return success(BeanUtils.toBean(list, SuggestionRespVO.class));
    }
}
