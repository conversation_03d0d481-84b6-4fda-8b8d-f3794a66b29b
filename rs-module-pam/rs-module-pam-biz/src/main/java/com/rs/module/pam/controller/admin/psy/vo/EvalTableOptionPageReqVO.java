package com.rs.module.pam.controller.admin.psy.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评量表关联提目选项分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EvalTableOptionPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("评量表ID")
    private String tableId;

    @ApiModelProperty("题目id")
    private String questionId;

    @ApiModelProperty("选项代码，A、B、C、D等")
    private String optionCode;

    @ApiModelProperty("选项内容")
    private String optionText;

    @ApiModelProperty("题目选项分值")
    private BigDecimal score;

    @ApiModelProperty("选项顺序")
    private Integer sortOrder;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
