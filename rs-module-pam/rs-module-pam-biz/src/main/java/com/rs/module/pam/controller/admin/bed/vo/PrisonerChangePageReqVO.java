package com.rs.module.pam.controller.admin.bed.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-人员床位调整 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrisonerChangePageReqVO extends PageParam {
private static final long serialVersionUID = 1L;

    @ApiModelProperty("添加时间")
    private Date[] addTime;

    private String orgCode;

    @ApiModelProperty("监室id")
    private String roomId;

    @ApiModelProperty("排序属性")
    List<OrderItem> orderFields;

}
