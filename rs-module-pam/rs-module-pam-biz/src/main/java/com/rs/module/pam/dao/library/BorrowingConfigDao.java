package com.rs.module.pam.dao.library;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.entity.library.BorrowingConfigDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.pam.controller.admin.library.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 监所事务管理-图书借阅配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BorrowingConfigDao extends IBaseDao<BorrowingConfigDO> {


    default PageResult<BorrowingConfigDO> selectPage(BorrowingConfigPageReqVO reqVO) {
        Page<BorrowingConfigDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<BorrowingConfigDO> wrapper = new LambdaQueryWrapperX<BorrowingConfigDO>()
            .eqIfPresent(BorrowingConfigDO::getMaximumBorrowingLimit, reqVO.getMaximumBorrowingLimit())
            .eqIfPresent(BorrowingConfigDO::getMaximumBorrowingDuration, reqVO.getMaximumBorrowingDuration())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(BorrowingConfigDO::getAddTime);
        }
        Page<BorrowingConfigDO> borrowingConfigPage = selectPage(page, wrapper);
        return new PageResult<>(borrowingConfigPage.getRecords(), borrowingConfigPage.getTotal());
    }
    default List<BorrowingConfigDO> selectList(BorrowingConfigListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BorrowingConfigDO>()
            .eqIfPresent(BorrowingConfigDO::getMaximumBorrowingLimit, reqVO.getMaximumBorrowingLimit())
            .eqIfPresent(BorrowingConfigDO::getMaximumBorrowingDuration, reqVO.getMaximumBorrowingDuration())
        .orderByDesc(BorrowingConfigDO::getAddTime));    }


    }
