package com.rs.module.pam.service.duty;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.duty.vo.*;
import com.rs.module.pam.controller.app.vo.AppDutyRespVO;
import com.rs.module.pam.controller.app.vo.AppDutyShiftRespVO;
import com.rs.module.pam.controller.app.vo.AppDutySigninReqVO;
import com.rs.module.pam.entity.duty.DutyDO;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 监所事务管理-监室值班 Service 接口
 *
 * <AUTHOR>
 */
public interface DutyService extends IBaseService<DutyDO>{

    /**
     * 创建监所事务管理-监室值班
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    void create(@Valid DutySaveVO createReqVO, Boolean isAuto);

    /**
     * 更新监所事务管理-监室值班
     *
     * @param updateReqVO 更新信息
     */
    void update(@Valid DutySaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-监室值班
     *
     * @param id 编号
     */
    void delete(String id);

    /**
     * 获得监所事务管理-监室值班
     *
     * @param id 编号
     * @return 监所事务管理-监室值班
     */
    DutyDO get(String id);

    /**
     * 监室值班-排班记录
     *
     * @param orgCode 机构编号
     * @param roomId 监室编号
     * @param startDate 值班开始时间
     * @param endDate 值班结束时间
     * @return
     */
    List<DutyVO> dutyRecords(String orgCode, String roomId, Date startDate, Date endDate);

    /**
     * 监室人员列表
     *
     * @param roomId 监室编号
     */
    List<DutyPrisonerVO> prisonerList(String orgCode, String roomId);

    /**
     * 自动排班
     * @param orgCode 机构编号
     * @param roomId 监室编号
     * @param startDate 值班开始时间
     * @param endDate 值班结束时间
     * @return
     */
    DutyVO autoShift(String orgCode, String roomId, Date startDate, Date endDate);

    /**
     * 获取值班签到记录
     * @param orgCode 机构编号
     * @param roomId 监室编号
     * @param dutyDate 值班日期
     * @return
     */
    DutyVO getSignRecord(String orgCode, String roomId, Date dutyDate);

    /**
     * 监室值班-排班记录-仓内屏
     *
     * @param orgCode 机构编号
     * @param roomId 监室编号
     * @param startDate 值班开始时间
     * @return
     */
    List<AppDutyRespVO> appDutyRecords(String orgCode, String roomId, Date startDate, Date endDate);

    /**
     * 获取指定日期的值日记录-仓外屏
     * @param orgCode
     * @param roomId
     * @param date
     * @return
     */
    List<AppDutyShiftRespVO> dutyRecordsByDate(String orgCode, String roomId, Date date);

    /**
     * 获取当前值班详情
     *
     * @param orgCode 机构编号
     * @param roomId 监室编号
     * @return
     */
    AppDutyShiftRespVO getNowShiftDetail(String orgCode, String roomId);

    /**
     * 值班签到
     *
     * @param reqVO
     * @return
     */
    List<RecordsSigninRespVO> signinSave(AppDutySigninReqVO reqVO);

}
