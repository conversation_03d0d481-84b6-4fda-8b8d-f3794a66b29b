package com.rs.module.pam.dao.daily;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.entity.daily.LifeDO;
import com.rs.module.pam.entity.daily.LifeTemplateDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.pam.controller.admin.daily.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 监所事务管理-一日生活制度模板 Dao
*
* <AUTHOR>
*/
@Mapper
public interface LifeTemplateDao extends IBaseDao<LifeTemplateDO> {


    default PageResult<LifeTemplateDO> selectPage(LifeTemplatePageReqVO reqVO) {
        Page<LifeTemplateDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<LifeTemplateDO> wrapper = new LambdaQueryWrapperX<LifeTemplateDO>()
            .likeIfPresent(LifeTemplateDO::getTemplateName, reqVO.getTemplateName())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(LifeTemplateDO::getAddTime);
        }
        Page<LifeTemplateDO> lifeTemplatePage = selectPage(page, wrapper);
        return new PageResult<>(lifeTemplatePage.getRecords(), lifeTemplatePage.getTotal());
    }
    default List<LifeTemplateDO> selectList(LifeTemplateListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<LifeTemplateDO>()
            .likeIfPresent(LifeTemplateDO::getTemplateName, reqVO.getTemplateName())
            .eqIfPresent(LifeTemplateDO::getOrgCode, reqVO.getOrgCode())
            .eqIfPresent(LifeTemplateDO::getRegCode, reqVO.getRegCode())
            .eqIfPresent(LifeTemplateDO::getCityCode, reqVO.getCityCode())
        .orderByDesc(LifeTemplateDO::getAddTime));    }


    }
