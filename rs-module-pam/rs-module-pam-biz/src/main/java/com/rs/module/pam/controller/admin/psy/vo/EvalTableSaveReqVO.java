package com.rs.module.pam.controller.admin.psy.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.*;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评量表管理新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EvalTableSaveReqVO extends BaseVO {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("量表名称")
    @NotEmpty(message = "量表名称不能为空")
    private String name;

    @ApiModelProperty("量表状态（字典：ZD_XLPC_ZT）")
    @NotEmpty(message = "量表状态不能为空")
    private String usageStatus;

    @ApiModelProperty("量表类型（字典：ZD_XLPC_LBLX）")
    @NotEmpty(message = "量表类型（字典：ZD_XLPC_LBLX）不能为空")
    private String tableType;

    @ApiModelProperty("描述")
    @NotEmpty(message = "描述不能为空")
    private String description;

    @ApiModelProperty("总题量")
    private Integer totalQuestionNumber;

    @ApiModelProperty("预计时长，单位分钟")
    @Min(value = 1, message = "预计时长，单位分钟不能小于1")
    private Integer estimatedDuration;

}
