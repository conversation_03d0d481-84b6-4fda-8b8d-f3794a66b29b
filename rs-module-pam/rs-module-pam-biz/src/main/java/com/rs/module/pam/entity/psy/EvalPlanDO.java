package com.rs.module.pam.entity.psy;

import com.tencentcloudapi.tke.v20180525.models.Exec;
import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-心理测评计划 DO
 *
 * <AUTHOR>
 */
@TableName("pam_psy_eval_plan")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_psy_eval_plan")
public class EvalPlanDO extends BaseDO {

    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 计划名称
     */
    private String planName;
    /**
     * 计划编码
     */
    private String planCode;
    /**
     * 计划类型
     */
    private String planType;
    /**
     * 计划说明
     */
    private String remark;
    /**
     * 量表ID
     */
    private String tableId;
    /**
     * 量表名称
     */
    private String tableName;
    /**
     * 触发类型
     */
    private String triggerType;
    /**
     * 触发配置，json存储
     */
    private String triggerConfig;
    /**
     * 推送对象
     */
    private String pushTarget;
    /**
     * 测评次数
     */
    private Integer evalNumber;
    /**
     * 完成时限
     */
    private Integer completionDeadline;
    /**
     * 启动消息推送
     */
    private String enableMessagePush;
    /**
     * 消息推送配置
     */
    private String messagePush;
    /**
     * 测评状态（字典：ZD_PSY_CPZT）
     */
    private String status;
    /**
     * 启用状态
     */
    private String enableStatus;
    /**
     * xxl-job任务id
     */
    private String jobId;
    /**
     * 执行任务id
     */
    private String logId;
    /**
     * 计划开始时间
     */
    private Date firstExecTime;


}
