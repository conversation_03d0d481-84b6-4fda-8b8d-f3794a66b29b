package com.rs.module.pam.controller.app;

import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.daily.vo.*;
import com.rs.module.pam.entity.daily.LifeDO;
import com.rs.module.pam.entity.daily.LifeEventDO;
import com.rs.module.pam.entity.daily.LifeRoomDO;
import com.rs.module.pam.service.daily.LifeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监所事务管理-一日生活制度")
@RestController
@RequestMapping("/app/pam/public/daily/life")
@Validated
public class AppLifeController {

    @Resource
    private LifeService lifeService;



    @GetMapping("/event")
    @ApiOperation(value = "App-获得监所事务管理-一日生活制度事项")
    @ApiImplicitParam(name = "roomId", value = "监室id")
    public CommonResult<List<LifeEventDO>> getLifeEvent(@RequestParam("roomId") String roomId) {
        return success(lifeService.getLifeEvent(roomId));
    }



}
