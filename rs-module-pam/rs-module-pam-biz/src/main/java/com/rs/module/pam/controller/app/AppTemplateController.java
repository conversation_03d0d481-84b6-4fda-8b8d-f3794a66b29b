package com.rs.module.pam.controller.app;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.cache.DicUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.pam.controller.admin.cook.vo.TemplateRespVO;
import com.rs.module.pam.controller.app.vo.AppSpecialApplyListRespVO;
import com.rs.module.pam.controller.app.vo.AppSpecialApplySaveSowReqVO;
import com.rs.module.pam.entity.cook.SpecialApplyDO;
import com.rs.module.pam.enums.CookSpecialRegStatusEnum;
import com.rs.module.pam.service.cook.SpecialApplyService;
import com.rs.module.pam.service.cook.TemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "内外屏-监室配餐")
@RestController
@RequestMapping("/app/pam/cook/template")
@Validated
public class AppTemplateController {

    @Resource
    private TemplateService templateService;
    @Lazy
    @Resource
    private SpecialApplyService specialApplyService;

    @GetMapping("/get")
    @ApiOperation(value = "获得-一周食谱菜品模板")
    @ApiImplicitParam(name = "orgCode", value = "监所编码", required = true)
    public CommonResult<TemplateRespVO> getTemplate(@RequestParam("orgCode") String orgCode) {
        return success(templateService.getAppTemplate(orgCode));
    }

    @PostMapping("/applySpecialMeal")
    @ApiOperation(value = "内屏申请特殊餐")
    public CommonResult applySpecialMeal(@Valid @RequestBody AppSpecialApplySaveSowReqVO saveSowReqVO) {
        return success(specialApplyService.applySpecialMeal(saveSowReqVO));
    }

    @PostMapping("/getSpecialApplyRecords")
    @ApiOperation(value = "内屏获取-特殊申请记录")
    @ApiImplicitParam(name = "jgrybm", value = "监管人员编码", required = true)
    public CommonResult applySpecialMeal(@RequestParam("jgrybm") String jgrybm) {
        LambdaQueryWrapper<SpecialApplyDO> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(SpecialApplyDO::getJgrybm, jgrybm);
        List<SpecialApplyDO> list = specialApplyService.list(lambdaQuery);
        List<Map<String, Object>> listMap = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            Map<String, List<SpecialApplyDO>> mealTypeMap = list.stream().collect(Collectors.groupingBy(SpecialApplyDO::getMealType));
            mealTypeMap.forEach((key, value) -> {
                Map<String, Object> map = new HashMap<>();
                map.put("mealType", key);
                String mealTypeName = DicUtil.translate("ZD_PCGL_PCLX", key);
                map.put("mealTypeName", mealTypeName);
                List<AppSpecialApplyListRespVO> valueList = new ArrayList<>();
                for (SpecialApplyDO specialApplyDO : value) {
                    AppSpecialApplyListRespVO respVO = new AppSpecialApplyListRespVO();
                    respVO.setReason(specialApplyDO.getReason());
                    respVO.setMealType(specialApplyDO.getMealType());
                    respVO.setMealTypeName(mealTypeName);
                    respVO.setRegStatus(CookSpecialRegStatusEnum.getEnum(specialApplyDO.getRegStatus()).getName());
                    respVO.setRegTime(specialApplyDO.getRegTime());
                    respVO.setMealStartTime(specialApplyDO.getMealStartTime());
                    respVO.setMealEndTime(specialApplyDO.getMealEndTime());
                    String mealPeriod = specialApplyDO.getMealPeriod();
                    List<String> dslx = DicUtil.translate("ZD_PCGL_DSLX", CollUtil.toList(mealPeriod.split(",")));
                    if (CollUtil.isNotEmpty(dslx)) {
                        respVO.setMealPeriod(CollUtil.join(dslx, "、"));
                    }
                    String specifiedDateType = specialApplyDO.getSpecifiedDateType();
                    if ("1".equals(specifiedDateType)) {
                        respVO.setSpecifiedDate("全部");
                    } else {
                        String specifiedDate = specialApplyDO.getSpecifiedDate();
                        List<String> zdrqlx = DicUtil.translate("ZD_PCGL_CPZS", CollUtil.toList(specifiedDate.split(",")));
                        if (CollUtil.isNotEmpty(zdrqlx)) {
                            respVO.setSpecifiedDate(CollUtil.join(zdrqlx, "、"));
                        }
                    }
                    valueList.add(respVO);
                }
                map.put("list", valueList);
                listMap.add(map);
            });
        }
        return success(listMap);
    }


}
