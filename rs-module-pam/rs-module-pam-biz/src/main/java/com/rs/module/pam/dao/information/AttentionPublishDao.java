package com.rs.module.pam.dao.information;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.information.vo.PublishListReqVO;
import com.rs.module.pam.controller.admin.information.vo.PublishPageReqVO;
import com.rs.module.pam.entity.information.PublishDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 监所事务管理-监室信息发布 Dao
*
* <AUTHOR>
*/
@Mapper
public interface AttentionPublishDao extends IBaseDao<PublishDO> {


    default PageResult<PublishDO> selectPage(PublishPageReqVO reqVO) {
        Page<PublishDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PublishDO> wrapper = new LambdaQueryWrapperX<PublishDO>()
            .eqIfPresent(PublishDO::getTitle, reqVO.getTitle())
            .eqIfPresent(PublishDO::getInfoType, reqVO.getInfoType())
            .eqIfPresent(PublishDO::getInfoContentText, reqVO.getInfoContentText())
            .eqIfPresent(PublishDO::getInfoContentHtml, reqVO.getInfoContentHtml())
            .eqIfPresent(PublishDO::getOperatorSfzh, reqVO.getOperatorSfzh())
            .eqIfPresent(PublishDO::getOperatorXm, reqVO.getOperatorXm())
            .eqIfPresent(PublishDO::getBizType, reqVO.getBizType())
            .betweenIfPresent(PublishDO::getOperatorTime, reqVO.getOperatorTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PublishDO::getAddTime);
        }
        Page<PublishDO> publishPage = selectPage(page, wrapper);
        return new PageResult<>(publishPage.getRecords(), publishPage.getTotal());
    }
    default List<PublishDO> selectList(PublishListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PublishDO>()
            .eqIfPresent(PublishDO::getOrgCode, reqVO.getOrgCode())
            .eqIfPresent(PublishDO::getTitle, reqVO.getTitle())
            .eqIfPresent(PublishDO::getInfoType, reqVO.getInfoType())
            .eqIfPresent(PublishDO::getStatus, reqVO.getStatus())
            .eqIfPresent(PublishDO::getInfoContentText, reqVO.getInfoContentText())
            .eqIfPresent(PublishDO::getInfoContentHtml, reqVO.getInfoContentHtml())
            .eqIfPresent(PublishDO::getOperatorSfzh, reqVO.getOperatorSfzh())
            .eqIfPresent(PublishDO::getOperatorXm, reqVO.getOperatorXm())
            .eqIfPresent(PublishDO::getBizType, reqVO.getBizType())
            .betweenIfPresent(PublishDO::getOperatorTime, reqVO.getOperatorTime())
        .orderByDesc(PublishDO::getAddTime));
    }

}
