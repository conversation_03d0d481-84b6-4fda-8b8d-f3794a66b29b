package com.rs.module.pam.controller.admin.library.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-图书借阅处理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LibraryBorrowingHandlePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("借阅id")
    private String borrowingId;

    @ApiModelProperty("借阅状态（字典：ZD_TSJY_JYZT）")
    private String status;

    @ApiModelProperty("处理人id")
    private String handleUserSfzh;

    @ApiModelProperty("处理人姓名")
    private String handleUser;

    @ApiModelProperty("处理时间")
    private Date[] handleTime;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
