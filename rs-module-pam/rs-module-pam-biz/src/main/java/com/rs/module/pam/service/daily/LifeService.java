package com.rs.module.pam.service.daily;

import java.util.*;
import javax.validation.*;
import com.rs.module.pam.controller.admin.daily.vo.*;
import com.rs.module.pam.entity.daily.LifeDO;
import com.rs.module.pam.entity.daily.LifeEventDO;
import com.rs.module.pam.entity.daily.LifeRoomDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 监所事务管理-一日生活制度 Service 接口
 *
 * <AUTHOR>
 */
public interface LifeService extends IBaseService<LifeDO>{

    /**
     * 创建监所事务管理-一日生活制度
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createLife(@Valid LifeSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-一日生活制度
     *
     * @param updateReqVO 更新信息
     */
    void updateLife(@Valid LifeSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-一日生活制度
     *
     * @param id 编号
     */
    void deleteLife(String id);

    /**
     * 获得监所事务管理-一日生活制度
     *
     * @param id 编号
     * @return 监所事务管理-一日生活制度
     */
    LifeDO getLife(String id);

    /**
    * 获得监所事务管理-一日生活制度分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-一日生活制度分页
    */
    PageResult<LifeDO> getLifePage(LifePageReqVO pageReqVO);

    /**
    * 获得监所事务管理-一日生活制度列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-一日生活制度列表
    */
    List<LifeDO> getLifeList(LifeListReqVO listReqVO);


    // ==================== 子表（监所事务管理-一日生活制度关联事务） ====================

    /**
     * 获得监所事务管理-一日生活制度关联事务列表
     *
     * @param dailyLifeId 一日生活制度ID
     * @return 监所事务管理-一日生活制度关联事务列表
     */
    List<LifeEventDO> getLifeEventListByDailyLifeId(String dailyLifeId);

    // ==================== 子表（监所事务管理-一日生活制度关联监室） ====================

    /**
     * 获得监所事务管理-一日生活制度关联监室列表
     *
     * @param dailyLifeId 一日生活制度ID
     * @return 监所事务管理-一日生活制度关联监室列表
     */
    List<LifeRoomDO> getLifeRoomListByDailyLifeId(String dailyLifeId);

    void updateLifeEvent(LifeEventSaveReqVO updateReqVO);

    void approvalUpdateLife(ApprovalLifeSaveReqVO updateReqVO);

    void updateOneLife(LifeOneSaveReqVO updateReqVO);

    List<LifeEventDO> getLifeEvent(String roomId);
}
