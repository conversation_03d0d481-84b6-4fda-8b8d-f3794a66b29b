package com.rs.module.pam.controller.admin.library.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 监所事务管理-图书借阅配置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BorrowingConfigRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("个人最大借阅数")
    private Integer maximumBorrowingLimit;
    @ApiModelProperty("个人最大借阅时长（天）")
    private Integer maximumBorrowingDuration;
}
