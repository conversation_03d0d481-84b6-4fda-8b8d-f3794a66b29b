package com.rs.module.pam.service.duty.day;

import cn.hutool.core.collection.CollectionUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.module.pam.dao.duty.day.DayDutyRecordsDao;
import com.rs.module.pam.entity.duty.day.DayDutyRecordsDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;


/**
 * 监所事务管理-监室值日记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DayDutyRecordsServiceImpl extends BaseServiceImpl<DayDutyRecordsDao, DayDutyRecordsDO> implements DayDutyRecordsService {

    @Resource
    private DayDutyRecordsDao dayDutyRecordsDao;

    @Override
    public List<DayDutyRecordsDO> selectList(String orgCode, String roomId, Date startDate, Date endDate) {
        List<DayDutyRecordsDO> recordsList = dayDutyRecordsDao.selectList(orgCode, roomId, startDate, endDate);
        if (CollectionUtil.isNotEmpty(recordsList)) {
            return recordsList;
        }
        return Collections.emptyList();
    }

}
