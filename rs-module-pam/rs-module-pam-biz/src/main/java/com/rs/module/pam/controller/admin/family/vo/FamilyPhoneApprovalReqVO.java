package com.rs.module.pam.controller.admin.family.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-亲情电话审批 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FamilyPhoneApprovalReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    @NotEmpty(message = "主键不能为空")
    private String id;

    @ApiModelProperty("审核状态（字典：ZD_JSTX_CQDHSPZT）")
    @NotEmpty(message = "审核状态不能为空")
    private String status;

    @ApiModelProperty("审核意见")
    private String approvalComments;



}
