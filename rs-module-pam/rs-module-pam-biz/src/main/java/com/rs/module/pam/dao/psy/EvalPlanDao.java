package com.rs.module.pam.dao.psy;

import java.util.*;

import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.entity.psy.EvalPlanDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.pam.controller.admin.psy.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 监所事务管理-心理测评计划 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface EvalPlanDao extends IBaseDao<EvalPlanDO> {


    default PageResult<EvalPlanDO> selectPage(EvalPlanPageReqVO reqVO) {
        Page<EvalPlanDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<EvalPlanDO> wrapper = new LambdaQueryWrapperX<EvalPlanDO>()
                .likeIfPresent(EvalPlanDO::getPlanName, reqVO.getPlanName())
                .eqIfPresent(EvalPlanDO::getPlanCode, reqVO.getPlanCode())
                .eqIfPresent(EvalPlanDO::getPlanType, reqVO.getPlanType())
                .eqIfPresent(EvalPlanDO::getRemark, reqVO.getRemark())
                .eqIfPresent(EvalPlanDO::getTableId, reqVO.getTableId())
                .eqIfPresent(EvalPlanDO::getTriggerType, reqVO.getTriggerType())
                .eqIfPresent(EvalPlanDO::getTriggerConfig, reqVO.getTriggerConfig())
                .eqIfPresent(EvalPlanDO::getPushTarget, reqVO.getPushTarget())
                .eqIfPresent(EvalPlanDO::getEvalNumber, reqVO.getEvalNumber())
                .eqIfPresent(EvalPlanDO::getCompletionDeadline, reqVO.getCompletionDeadline())
                .eqIfPresent(EvalPlanDO::getEnableMessagePush, reqVO.getEnableMessagePush())
                .eqIfPresent(EvalPlanDO::getMessagePush, reqVO.getMessagePush())
                .eqIfPresent(EvalPlanDO::getStatus, reqVO.getStatus())
                .eqIfPresent(EvalPlanDO::getEnableStatus, reqVO.getEnableStatus());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(EvalPlanDO::getAddTime);
        }
        Page<EvalPlanDO> evalPlanPage = selectPage(page, wrapper);
        return new PageResult<>(evalPlanPage.getRecords(), evalPlanPage.getTotal());
    }

    default List<EvalPlanDO> selectList(EvalPlanListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<EvalPlanDO>()
                .likeIfPresent(EvalPlanDO::getPlanName, reqVO.getPlanName())
                .eqIfPresent(EvalPlanDO::getPlanCode, reqVO.getPlanCode())
                .eqIfPresent(EvalPlanDO::getPlanType, reqVO.getPlanType())
                .eqIfPresent(EvalPlanDO::getRemark, reqVO.getRemark())
                .eqIfPresent(EvalPlanDO::getTableId, reqVO.getTableId())
                .eqIfPresent(EvalPlanDO::getTriggerType, reqVO.getTriggerType())
                .eqIfPresent(EvalPlanDO::getEvalNumber, reqVO.getEvalNumber())
                .eqIfPresent(EvalPlanDO::getStatus, reqVO.getStatus())
                .eqIfPresent(EvalPlanDO::getEnableStatus, reqVO.getEnableStatus())
                .eqIfPresent(EvalPlanDO::getOrgCode, reqVO.getOrgCode())
                .orderByDesc(EvalPlanDO::getAddTime));
    }


    List<Map<String, String>> selectAreaByCode(@Param("areaCodes") Set<String> areaCodes);


    /**
     * 根据监室号查询jgrybm
     *
     * @param jqList
     * @param jsList
     * @param jgrybms
     * @return
     */
    List<Map<String, String>> selectJgrybm(@Param("jqList") Set<String> jqList,
                                           @Param("jsList") Set<String> jsList,
                                           @Param("jgrybms") Set<String> jgrybms,
                                           @Param("xm") String xm,
                                           @Param("areaName") String areaName);

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    EvalPlanRespVO getEvalPlanRespVO(@Param("id") String id);
}
