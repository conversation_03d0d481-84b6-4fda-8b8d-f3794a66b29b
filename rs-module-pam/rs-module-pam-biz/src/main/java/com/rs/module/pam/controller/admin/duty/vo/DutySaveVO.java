package com.rs.module.pam.controller.admin.duty.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-监室值班列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DutySaveVO extends BaseVO {

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 监室编号
     */
    private String roomId;

    /**
     * 值班列表
     */
    private List<DutyListRespVO> dutyList;

}
