package com.rs.module.pam.controller.admin.bed.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.x.file.storage.core.FileInfo;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-监室床位布局新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LayoutSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("布局名称")
    private String layoutName;

    @ApiModelProperty("布局底图url")
    private String layoutUrl;

    @ApiModelProperty("布局类型(01:固定,02:自定义)")
    @NotEmpty(message = "布局类型不能为空")
    private String layoutType;

    @ApiModelProperty("布局配置")
    @NotEmpty(message = "布局配置不能为空")
    private List<BedAreaConfigVO> layoutConfig;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("底图宽度")
    private Integer width;

    @ApiModelProperty("底图高度")
    private Integer height;

    @ApiModelProperty("行数")
    private String layoutRow;

    @ApiModelProperty("列数")
    private String layoutColumn;

}
