package com.rs.module.pam.service.religion;

import java.util.*;
import javax.validation.*;
import com.rs.module.pam.controller.admin.religion.vo.*;
import com.rs.module.pam.entity.religion.ReligionApplyDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 监所事务管理-宗教申请 Service 接口
 *
 * <AUTHOR>
 */
public interface ReligionApplyService extends IBaseService<ReligionApplyDO>{

    /**
     * 创建监所事务管理-宗教申请
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createReligionApply(@Valid ReligionApplySqReqVO createReqVO);

    /**
     * 更新监所事务管理-宗教申请
     *
     * @param updateReqVO 更新信息
     */
    void updateReligionApply(@Valid ReligionApplySaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-宗教申请
     *
     * @param id 编号
     */
    void deleteReligionApply(String id);

    /**
     * 获得监所事务管理-宗教申请
     *
     * @param id 编号
     * @return 监所事务管理-宗教申请
     */
    ReligionApplyDO getReligionApply(String id);

    /**
    * 获得监所事务管理-宗教申请分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-宗教申请分页
    */
    PageResult<ReligionApplyDO> getReligionApplyPage(ReligionApplyPageReqVO pageReqVO);

    /**
    * 获得监所事务管理-宗教申请列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-宗教申请列表
    */
    List<ReligionApplyDO> getReligionApplyList(ReligionApplyListReqVO listReqVO);


    void approval(ReligionApplyApprovalReqVO applyApprovalReqVO);
}
