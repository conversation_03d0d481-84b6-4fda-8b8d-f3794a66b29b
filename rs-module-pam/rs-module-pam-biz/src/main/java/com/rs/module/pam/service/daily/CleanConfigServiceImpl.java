package com.rs.module.pam.service.daily;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.daily.vo.CleanConfigListReqVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanConfigSaveReqVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanConfigUpdateReqVO;
import com.rs.module.pam.dao.daily.CleanConfigDao;
import com.rs.module.pam.entity.daily.CleanConfigDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * 监所事务管理-日常清监检查项配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CleanConfigServiceImpl extends BaseServiceImpl<CleanConfigDao, CleanConfigDO> implements CleanConfigService {

    @Resource
    private CleanConfigDao cleanConfigDao;

    @Override
    public void createCleanConfig(CleanConfigSaveReqVO createReqVO) {
        validateCleanConfigExists(createReqVO);
        List<String> list = createReqVO.getCheckItems();
        List<CleanConfigDO> cleanConfigList = new ArrayList<>();
        String[] split = createReqVO.getCheckType().split(",");
        for (String type : split) {
            for (String item : list) {
                CleanConfigDO cleanConfig = new CleanConfigDO();
                cleanConfig.setCheckType(type);
                cleanConfig.setCheckItem(item);
                cleanConfigList.add(cleanConfig);
            }
        }
        // 插入
        cleanConfigDao.insertBatch(cleanConfigList);
    }

    @Override
    public void updateCleanConfig(CleanConfigUpdateReqVO updateReqVO) {
        // 校验存在
        LambdaQueryWrapper<CleanConfigDO> query = Wrappers.lambdaQuery(CleanConfigDO.class);
        query.eq(CleanConfigDO::getCheckItem, updateReqVO.getCheckItem())
                .eq(CleanConfigDO::getCheckType, updateReqVO.getCheckType())
                .eq(CleanConfigDO::getOrgCode, SessionUserUtil.getSessionUser().getOrgCode());
        CleanConfigDO configDO = cleanConfigDao.selectOne(query);
        if (configDO != null && !configDO.getId().equals(updateReqVO.getId())) {
            throw new ServerException("日常清监检查项配置项数据已存在");
        }
        // 更新
        CleanConfigDO updateObj = BeanUtils.toBean(updateReqVO, CleanConfigDO.class);
        cleanConfigDao.updateById(updateObj);
    }

    @Override
    public void deleteCleanConfig(String id) {
        // 删除
        cleanConfigDao.deleteById(id);
    }

    private void validateCleanConfigExists(CleanConfigSaveReqVO createReqVO) {
        List<String> checkItems = createReqVO.getCheckItems();
        String[] split = createReqVO.getCheckType().split(",");
        for (String type : split) {
            for (String item : checkItems) {
                LambdaQueryWrapper<CleanConfigDO> query = Wrappers.lambdaQuery(CleanConfigDO.class);
                query.eq(CleanConfigDO::getCheckItem, item)
                        .eq(CleanConfigDO::getCheckType, type)
                        .eq(CleanConfigDO::getOrgCode, SessionUserUtil.getSessionUser().getOrgCode());
                CleanConfigDO configDO = cleanConfigDao.selectOne(query);
                if (configDO != null && !configDO.getId().equals(createReqVO.getId())) {
                    throw new ServerException("日常清监检查项配置项数据已存在");
                }
            }
        }
    }

    @Override
    public CleanConfigDO getCleanConfig(String id) {
        return cleanConfigDao.selectById(id);
    }

    @Override
    public List<CleanConfigDO> getCleanConfigList(CleanConfigListReqVO listReqVO) {
        return cleanConfigDao.selectList(listReqVO);
    }


}
