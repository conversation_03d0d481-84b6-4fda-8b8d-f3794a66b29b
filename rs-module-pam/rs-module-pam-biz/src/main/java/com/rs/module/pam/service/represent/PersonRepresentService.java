package com.rs.module.pam.service.represent;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.pam.controller.admin.represent.vo.PersonRepresentListReqVO;
import com.rs.module.pam.controller.admin.represent.vo.PersonRepresentPageReqVO;
import com.rs.module.pam.controller.admin.represent.vo.PersonRepresentSaveReqVO;
import com.rs.module.pam.entity.represent.PersonRepresentDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 监所事务管理-人员点名记录 Service 接口
 *
 * <AUTHOR>
 */
public interface PersonRepresentService extends IBaseService<PersonRepresentDO>{

    /**
     * 创建监所事务管理-人员点名记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPersonRepresent(@Valid PersonRepresentSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-人员点名记录
     *
     * @param updateReqVO 更新信息
     */
    void updatePersonRepresent(@Valid PersonRepresentSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-人员点名记录
     *
     * @param id 编号
     */
    void deletePersonRepresent(String id);

    /**
     * 获得监所事务管理-人员点名记录
     *
     * @param id 编号
     * @return 监所事务管理-人员点名记录
     */
    PersonRepresentDO getPersonRepresent(String id);

    /**
    * 获得监所事务管理-人员点名记录分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-人员点名记录分页
    */
    PageResult<PersonRepresentDO> getPersonRepresentPage(PersonRepresentPageReqVO pageReqVO);

    /**
    * 获得监所事务管理-人员点名记录列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-人员点名记录列表
    */
    List<PersonRepresentDO> getPersonRepresentList(PersonRepresentListReqVO listReqVO);


}
