package com.rs.module.pam.service.duty;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.duty.vo.RecordsSigninSaveReqVO;
import com.rs.module.pam.dao.duty.RecordsSigninDao;
import com.rs.module.pam.entity.duty.RecordsSigninDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;


/**
 * 监所事务管理-监室值班记录关联签到 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RecordsSigninServiceImpl extends BaseServiceImpl<RecordsSigninDao, RecordsSigninDO> implements RecordsSigninService {

    @Resource
    private RecordsSigninDao recordsSigninDao;

    @Override
    public String createRecordsSignin(RecordsSigninSaveReqVO createReqVO) {
        // 插入
        RecordsSigninDO recordsSignin = BeanUtils.toBean(createReqVO, RecordsSigninDO.class);
        recordsSigninDao.insert(recordsSignin);
        // 返回
        return recordsSignin.getId();
    }

    @Override
    public List<RecordsSigninDO> getNowSigninByRecordsId(String orgCode, String roomId, String recordsId) {
        return recordsSigninDao.getNowSigninByRecordsId(orgCode, roomId, recordsId);
    }

    @Override
    public List<RecordsSigninDO> getNowRecordsSignin(String orgCode, String roomId) {
        return recordsSigninDao.getNowRecordsSignin(orgCode, roomId);
    }

    @Override
    public void deleteRecordsSignin(String orgCode, String roomId, String recordsId, Date date) {
        recordsSigninDao.delete(new LambdaQueryWrapper<RecordsSigninDO>()
                .eq(RecordsSigninDO::getOrgCode, orgCode)
                .eq(RecordsSigninDO::getRoomId, roomId)
                .eq(RecordsSigninDO::getRecordsId, recordsId)
                .lt(RecordsSigninDO::getDutyDate, date));
    }


}
