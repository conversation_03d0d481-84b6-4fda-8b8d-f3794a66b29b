package com.rs.module.pam.dao.cook;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.cook.vo.ManageListReqVO;
import com.rs.module.pam.entity.cook.ManageDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 监所事务管理-菜品管理 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface ManageDao extends IBaseDao<ManageDO> {

    default List<ManageDO> selectList(ManageListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ManageDO>()
                .eqIfPresent(ManageDO::getOrgCode, reqVO.getOrgCode())
                .eqIfPresent(ManageDO::getCateId, reqVO.getCateId())
                .eqIfPresent(ManageDO::getSort, reqVO.getSort())
                .likeIfPresent(ManageDO::getCookName, reqVO.getCookName())
                .orderByDesc(ManageDO::getAddTime));
    }

}
