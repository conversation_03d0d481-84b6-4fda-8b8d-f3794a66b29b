package com.rs.module.pam.controller.admin.duty.day.vo;

import com.rs.module.pam.controller.admin.duty.vo.DutyPrisonerVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-监室值日详情 Response VO")
@Data
public class DayDutyInfoRespVO {

    private String id;

    @ApiModelProperty("班次ID")
    private String shiftId;

    @ApiModelProperty(value = "值日日期", hidden = true)
    private Date dutyDate;

    @ApiModelProperty("班次类型(0:之前, 1:开始, 2:未来)")
    private String shiftType;

    @ApiModelProperty("是否为值日组")
    private Boolean isGroup;

    @ApiModelProperty("值日组ID")
    private String groupId;

    @ApiModelProperty("值日组名称")
    private String groupName;

    @ApiModelProperty("值日人员信息")
    private List<DutyPrisonerVO> prisonerList;

}
