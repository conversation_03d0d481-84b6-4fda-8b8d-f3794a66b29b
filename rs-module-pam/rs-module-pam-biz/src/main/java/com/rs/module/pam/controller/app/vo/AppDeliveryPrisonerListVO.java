package com.rs.module.pam.controller.app.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/14 16:21
 */
@Data
@ApiModel(description = "内屏 - 监室配餐-发饭人员列表 Request VO")
public class AppDeliveryPrisonerListVO {

    @ApiModelProperty(value = "发饭状态（0:发饭中，1：发饭完成）")
    private String deliveryStatus;
    @ApiModelProperty(value = "待刷脸数量")
    private int toDoTotal;
    @ApiModelProperty(value = "已刷脸数量")
    private int doneTotal;
    @ApiModelProperty(value = "发饭人员列表")
    private List<DeliveryPrisonerList> deliveryPrisonerList;
    @ApiModelProperty(value = "未发饭人员列表")
    private List<DeliveryPrisonerList> noDeliveryPrisonerList;

    @Data
    public static class DeliveryPrisonerList {
        @ApiModelProperty(value = "监管人员编码")
        private String jgrybm;
        @ApiModelProperty(value = "姓名")
        private String xm;
        @ApiModelProperty(value = "年龄")
        private int age;
        @ApiModelProperty(value = "照片地址")
        private String frontPhoto;
        @ApiModelProperty(value = "性别")
        private String xb;
        @ApiModelProperty(value = "性别名称")
        private String xbName;
        @ApiModelProperty(value = "外出备注，多个备注以逗号分隔")
        private String outRemark;
        @ApiModelProperty(value = "配餐类型")
        private String mealType;
        @ApiModelProperty(value = "发饭状态(0:未发饭，1:已发饭,2:留饭)")
        private String status;
        @ApiModelProperty(value = "刷脸时间")
        private String faceTime;
    }

}
