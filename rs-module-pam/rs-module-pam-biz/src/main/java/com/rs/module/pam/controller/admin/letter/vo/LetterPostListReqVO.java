package com.rs.module.pam.controller.admin.letter.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-家属通信-寄信登记列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LetterPostListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("数据来源（字典：ZD_DATA_SOURCES）")
    private String dataSources;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("收信人姓名")
    private String receiveMailUser;

    @ApiModelProperty("关系")
    private String relation;

    @ApiModelProperty("收信地址")
    private String receiveAddress;

    @ApiModelProperty("来信日期")
    private Date[] sendTime;

    @ApiModelProperty("收信单位")
    private String receivePrison;

    @ApiModelProperty("信件邮编")
    private String mailNo;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("申请时间")
    private Date[] applyTime;

    @ApiModelProperty("签名图片")
    private String signUrl;

    @ApiModelProperty("信件状态 （字典：ZD_JSTX_JXZT）")
    private String status;

    @ApiModelProperty("审批状态（字典：ZD_JSTX_JXSPZT）")
    private String approverStatus;

    @ApiModelProperty("转交人")
    private String passUser;

    @ApiModelProperty("转交时间")
    private Date[] passTime;

    @ApiModelProperty("转交备注")
    private String passRemark;

    @ApiModelProperty("管教审批人身份证号")
    private String gjApproverSfzh;

    @ApiModelProperty("管教审批人姓名")
    private String gjApproverXm;

    @ApiModelProperty("管教审批时间")
    private Date[] gjApproverTime;

    @ApiModelProperty("管教审批结果")
    private String gjApprovalResult;

    @ApiModelProperty("管教审核意见")
    private String gjApprovalComments;

    @ApiModelProperty("科组长审批人身份证号")
    private String groupApproverSfzh;

    @ApiModelProperty("科组长审批人姓名")
    private String groupApproverXm;

    @ApiModelProperty("科组长审批时间")
    private Date[] groupApproverTime;

    @ApiModelProperty("科组长审批结果")
    private String groupApprovalResult;

    @ApiModelProperty("科组长审核意见")
    private String groupApprovalComments;

    @ApiModelProperty("所领导审批人身份证号")
    private String leaderApproverSfzh;

    @ApiModelProperty("所领导审批人姓名")
    private String leaderApproverXm;

    @ApiModelProperty("所领导审批时间")
    private Date[] leaderApproverTime;

    @ApiModelProperty("所领导审批结果")
    private String leaderApprovalResult;

    @ApiModelProperty("所领导审核意见")
    private String leaderApprovalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

}
