package com.rs.module.pam.service.cook;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.util.SessionUserUtil;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.rs.framework.common.entity.OpsDicCode;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.util.DicUtil;
import com.rs.module.pam.controller.admin.cook.vo.TemplateListReqVO;
import com.rs.module.pam.controller.admin.cook.vo.TemplateRespVO;
import com.rs.module.pam.controller.admin.cook.vo.TemplateSaveReqVO;
import com.rs.module.pam.dao.cook.TemplateDao;
import com.rs.module.pam.entity.cook.TemplateDO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.Month;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 监所事务管理-菜品模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TemplateServiceImpl extends BaseServiceImpl<TemplateDao, TemplateDO> implements TemplateService {

    private static final DateTimeFormatter[] FORMATTERS = {
            DateTimeFormatter.ofPattern("yyyy年M月d日"),
            DateTimeFormatter.ofPattern("yyyy年M月dd日"),
            DateTimeFormatter.ofPattern("yyyy年MM月dd日"),
            DateTimeFormatter.ofPattern("yyyy年MM月d日")
    };

    @Resource
    private TemplateDao templateDao;
    @Value("${system-mark}")
    private String systemMark;

    @Override
    public String createTemplate(TemplateSaveReqVO createReqVO) {
        TemplateDO templateDO = reqVOConvertTemplateDO(createReqVO);
        // 插入
        templateDao.insert(templateDO);
        // 返回
        return templateDO.getId();
    }

    @Override
    public void updateTemplate(TemplateSaveReqVO updateReqVO) {
        // 校验存在
        validateTemplateExists(updateReqVO.getId());
        TemplateDO templateDO = reqVOConvertTemplateDO(updateReqVO);
        // 更新
        templateDao.updateById(templateDO);
    }

    @Override
    public void deleteTemplate(String id) {
        // 校验存在
        validateTemplateExists(id);
        // 删除
        templateDao.deleteById(id);
    }

    private void validateTemplateExists(String id) {
        if (templateDao.selectById(id) == null) {
            throw new ServerException("菜品模板数据不存在");
        }
    }

    @Override
    public TemplateRespVO getTemplate(String id) {
        TemplateDO templateDO = templateDao.selectById(id);
        if (templateDO == null) {
            throw new ServerException("菜品模板数据不存在");
        }
        TemplateRespVO templateRespVO = new TemplateRespVO();
        BeanUtils.copyProperties(templateDO, templateRespVO);
        List<OpsDicCode> dicCodes = DicUtil.getDicDesc("ZD_PCGL_DSLX", systemMark);
        List<TemplateSaveReqVO.CookMeal> cookMeals = new java.util.LinkedList<>();
        for (OpsDicCode dicCode : dicCodes) {
            TemplateSaveReqVO.CookMeal cookMeal = new TemplateSaveReqVO.CookMeal();
            cookMeal.setName(dicCode.getName());
            cookMeal.setWeekId("1,2,3,4,5,6,7");
            cookMeal.setSortNo(Integer.valueOf(dicCode.getCode()));
            cookMeals.add(cookMeal);
        }
        List<TemplateSaveReqVO.CookMeal> meals = cookMeals.stream()
                .sorted(Comparator.comparing(TemplateSaveReqVO.CookMeal::getSortNo)).collect(Collectors.toList());
        List<TemplateSaveReqVO.CookSub> cookSubs = new java.util.LinkedList<>();
        for (TemplateSaveReqVO.CookMeal cookMeal : meals) {
            TemplateSaveReqVO.CookSub cookSub = new TemplateSaveReqVO.CookSub();
            cookSub.setMealName(cookMeal.getName());
            cookSub.setMealNo(cookMeal.getSortNo());
            List<TemplateSaveReqVO.CookWeekSub> cookWeekSubs = new java.util.LinkedList<>();
            String[] split = cookMeal.getWeekId().split(",");
            for (String str : split) {
                TemplateSaveReqVO.CookWeekSub cookWeekSub = new TemplateSaveReqVO.CookWeekSub();
                cookWeekSub.setWeekId(str);
                List<TemplateSaveReqVO.Cook> cooks = new java.util.LinkedList<>();
                switch (str) {
                    case "1":
                        cooks.addAll(getCookList(templateDO.getMondayCookbook(), cookMeal.getSortNo()));
                        break;
                    case "2":
                        cooks.addAll(getCookList(templateDO.getTuesdayCookbook(), cookMeal.getSortNo()));
                        break;
                    case "3":
                        cooks.addAll(getCookList(templateDO.getWednesdayCookbook(), cookMeal.getSortNo()));
                        break;
                    case "4":
                        cooks.addAll(getCookList(templateDO.getThursdayCookbook(), cookMeal.getSortNo()));
                        break;
                    case "5":
                        cooks.addAll(getCookList(templateDO.getFridayCookbook(), cookMeal.getSortNo()));
                        break;
                    case "6":
                        cooks.addAll(getCookList(templateDO.getSaturdayCookbook(), cookMeal.getSortNo()));
                        break;
                    case "7":
                        cooks.addAll(getCookList(templateDO.getSundayCookbook(), cookMeal.getSortNo()));
                        break;
                    default:
                        break;
                }
                cookWeekSub.setCooks(cooks);
                cookWeekSubs.add(cookWeekSub);
            }
            cookSub.setCookWeekSubs(cookWeekSubs);
            cookSubs.add(cookSub);
        }
        templateRespVO.setCookMeals(meals);
        templateRespVO.setCookSubs(cookSubs);
        // 处理复用模板
        if (StrUtil.isNotBlank(templateDO.getCopyId())) {
            TemplateDO copy = templateDao.selectById(templateDO.getCopyId());
            if (copy == null) {
                throw new ServerException("复用模板不存在");
            }
            templateRespVO.setCopyTemplateName(copy.getTemplateName());
        }
        return templateRespVO;
    }

    private List<TemplateSaveReqVO.Cook> getCookList(String data, int path) {
        JSON json = JSONUtil.parse(data);
        JSONArray objects = json.getByPath(String.valueOf(path), JSONArray.class);
        List<TemplateSaveReqVO.Cook> cookList = objects.toBean(new TypeReference<List<TemplateSaveReqVO.Cook>>() {});
        return cookList;
    }

    @Override
    public List<TemplateDO> getTemplateList(TemplateListReqVO listReqVO) {
        return templateDao.selectList(listReqVO);
    }

    @Override
    public TemplateDO getTemplateDO(String id) {
        return templateDao.selectById(id);
    }

    @Override
    public void multiplexing(String weekNo, String weekDate, String tempId) {
        TemplateDO templateDO = templateDao.selectById(tempId);
        if (templateDO == null) {
            throw new ServerException("菜谱模板不存在");
        }
        TemplateDO temp = templateDao.getTempByWeek(weekNo, SessionUserUtil.getSessionUser().getOrgCode());
        if (temp != null) {
            throw new ServerException("该周已存在菜谱，请勿重复创建");
        }
        TemplateDO add = new TemplateDO();
        BeanUtil.copyProperties(templateDO, add, "id");

        add.setWeekNo(weekNo);
        add.setCopyId(tempId);
        add.setIsCopy("1");

        String[] dates = weekDate.split("-");
        add.setStartTime(getDate(dates[0]));
        add.setEndTime(getDate(dates[1]));
        templateDao.insert(add);
    }

    @Override
    public Map<String, Object> getIndexList(String orgCode) {
        Map<String, Object> weekList = getWeekList(orgCode);
        return weekList;
    }

    @Override
    public TemplateRespVO getAppTemplate(String orgCode) {
        // 计算当前日期是当月的第几周
        String weekNo = getWeekNo(LocalDate.now());
        TemplateDO week = templateDao.getTempByWeek(weekNo, orgCode);
        if (week != null) {
            TemplateRespVO template = getTemplate(week.getId());
            if (template != null) {
                return template;
            }
        }
        return null;
    }

    @Override
    public String getMealContent(LocalDate currentDate, String orgCode, String mealPeriod) {
        // 计算当前日期是当月的第几周
        TemplateDO week = templateDao.getTempByWeek(getWeekNo(currentDate), orgCode);
        StringBuffer cooksStr = new StringBuffer("");
        if (week != null) {
            List<TemplateSaveReqVO.Cook> cooks = new LinkedList<>();
            // 获取当前日期是星期几
            // 根据星期几来决定添加哪些菜品
            switch (currentDate.getDayOfWeek()) {
                case MONDAY:
                    cooks.addAll(getCookList(week.getMondayCookbook(), Integer.valueOf(mealPeriod)));
                    break;
                case TUESDAY:
                    cooks.addAll(getCookList(week.getTuesdayCookbook(), Integer.valueOf(mealPeriod)));
                    break;
                case WEDNESDAY:
                    cooks.addAll(getCookList(week.getWednesdayCookbook(), Integer.valueOf(mealPeriod)));
                    break;
                case THURSDAY:
                    cooks.addAll(getCookList(week.getThursdayCookbook(), Integer.valueOf(mealPeriod)));
                    break;
                case FRIDAY:
                    cooks.addAll(getCookList(week.getFridayCookbook(), Integer.valueOf(mealPeriod)));
                    break;
                case SATURDAY:
                    cooks.addAll(getCookList(week.getSaturdayCookbook(), Integer.valueOf(mealPeriod)));
                    break;
                case SUNDAY:
                    cooks.addAll(getCookList(week.getSundayCookbook(), Integer.valueOf(mealPeriod)));
                    break;
                default:
                    break;
            }
            cooksStr.append(cooks.stream().map(TemplateSaveReqVO.Cook::getName).collect(Collectors.joining(",")));
        }
        return cooksStr.toString();
    }

    public static String getWeekNo(LocalDate date) {
        int year = date.getYear();
        Month month = date.getMonth();
        // 获取当月第一天
        LocalDate firstDayOfMonth = LocalDate.of(year, month, 1);
        // 获取当月第一个周一
        LocalDate firstMonday = firstDayOfMonth.with(TemporalAdjusters.firstInMonth(DayOfWeek.MONDAY));
        // 计算目标日期所在周的周数
        int weekOfMonth;
        if (date.isBefore(firstMonday)) {
            // 如果日期在第一个周一之前，属于上月的最后一周
            weekOfMonth = 1; // 也可以返回null或抛出异常，根据业务需求调整
        } else {
            // 计算距离第一个周一的周数
            long daysDiff = java.time.temporal.ChronoUnit.DAYS.between(firstMonday, date);
            weekOfMonth = (int) (daysDiff / 7) + 1;
        }
        // 格式化weekNo：yyyyMMww
        return String.format("%d%02d%02d", year, month.getValue(), weekOfMonth);
    }

    public Map<String, Object> getWeekList(String orgCode) {
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy");
        String currentYear = currentDate.format(formatter);
        int year = Integer.parseInt(currentYear);
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        LocalDate startOfWeek = getStartOfWeek();//获取当前周的第一天
        LocalDate endOfWeek = startOfWeek.plusDays(6);//获取当前周的最后一天
        for (int monthNo = 1; monthNo <= 12; monthNo++) {
            // 获取该月份的第一天
            LocalDate firstDayOfMonth = LocalDate.of(year, monthNo, 1);
            // 获取该月份的周一
            LocalDate monday = firstDayOfMonth.with(TemporalAdjusters.firstInMonth(DayOfWeek.MONDAY));
            // 获取该月份的最后一天
            LocalDate lastDayOfMonth = firstDayOfMonth.with(TemporalAdjusters.lastDayOfMonth());
            // 创建一个List来存储每一周的周一到周日的集合
            List<Map<String, Object>> weeksOfMonth = new ArrayList<>();
            // 遍历每一周
            Integer weekNum = 1;
            while (monday.isBefore(lastDayOfMonth) || monday.isEqual(lastDayOfMonth)) {
                // 获取该周的周一到周日的集合
                //   List<LocalDate> weekDates = new ArrayList<>();
                HashMap<String, Object> weekMap = new HashMap<>();
                LocalDate currentDay = monday;
                String dateStr = "";
                String isEdit = "0";
                boolean isThisWeek = false;
                StringBuilder stringBuilder = new StringBuilder(dateStr);
                for (int i = 0; i < 7; i++) {
                    if (i == 0) {
                        stringBuilder.append(currentDay.getMonth().getValue()).append("月").append(currentDay.getDayOfMonth()).append("日").append("-");
                        boolean after = currentDay.isAfter(endOfWeek);
                        boolean after1 = currentDay.isAfter(startOfWeek);
                        boolean before = currentDay.isBefore(endOfWeek);
                        if ((after1 || currentDay.isEqual(startOfWeek)) && (before || currentDay.equals(endOfWeek))) {
                            isThisWeek = true;
                        }
                        if (after || isThisWeek) {//本周以及本周以后可以编辑
                            isEdit = "1";
                        }
                    } else if (i == 6) {
                        stringBuilder.append(currentDay.getMonth().getValue()).append("月").append(currentDay.getDayOfMonth()).append("日");
                    }
                    currentDay = currentDay.plusDays(1);
                }
                String weekNo = year + "" + (monthNo < 10 ? "0" + monthNo : monthNo + "") + (weekNum < 10 ? "0" + weekNum : weekNum + "");
                weekMap.put("weekNo", weekNo);
                TemplateDO tempByWeek = templateDao.getTempByWeek(weekNo, orgCode);
                weekMap.put("tempId", tempByWeek == null ? "" : tempByWeek.getId());
                weekMap.put("name", tempByWeek == null ? "" : tempByWeek.getTemplateName());
                weekMap.put("weekDate", stringBuilder.toString());
                String weekNumStr = weekNum == 1 ? "一" : weekNum == 2 ? "二" : weekNum == 3 ? "三" : weekNum == 4 ? "四" : "五";
                weekMap.put("weekNumStr", "第" + weekNumStr + "周");
                weekMap.put("isEdit", isEdit);
                // 将该周的日期集合添加到月份的周集合中
                weeksOfMonth.add(weekMap);

                // 更新下一周的起始日期
                monday = monday.plusWeeks(1);
                weekNum++;
            }
            map.put(Month.of(monthNo).name().toLowerCase(), weeksOfMonth);
            map.put("autoFlag", "0");
//            map.put("autoFlag", this.autoDao.getAutoFlag(prisonId) == null ? 0 : this.autoDao.getAutoFlag(prisonId));
        }
        return map;
    }

    public LocalDate getStartOfWeek() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 获取当前日期的星期几
        DayOfWeek currentDayOfWeek = currentDate.getDayOfWeek();
        // 计算本周的起始日期和结束日期
        LocalDate startOfWeek = currentDate.minusDays(currentDayOfWeek.getValue() - DayOfWeek.MONDAY.getValue());
        return startOfWeek;
    }

    private static Date getDate(String dateStr) {
        int currentYear = LocalDate.now().getYear();
        String fullDateStr = currentYear + "年" + dateStr;
        for (DateTimeFormatter formatter : FORMATTERS) {
            try {
                LocalDate start = LocalDate.parse(fullDateStr, formatter);
                return Date.from(start.atStartOfDay(ZoneId.systemDefault()).toInstant());
            } catch (DateTimeParseException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    private TemplateDO reqVOConvertTemplateDO(TemplateSaveReqVO createReqVO) {
        TemplateDO templateDO = new TemplateDO();
        BeanUtils.copyProperties(createReqVO, templateDO);
        Map<String, String> dsMap = DicUtil.getDicDesc("ZD_PCGL_DSLX", systemMark).stream()
                .collect(Collectors.toMap(OpsDicCode::getName, OpsDicCode::getCode));
        List<TemplateSaveReqVO.CookMeal> cookMeals = createReqVO.getCookMeals();
        Map<String, TemplateSaveReqVO.CookSub> cookSubMap = createReqVO.getCookSubs().stream()
                .collect(Collectors.toMap(TemplateSaveReqVO.CookSub::getMealName, Function.identity()));
        Table<String, String, List<TemplateSaveReqVO.Cook>> table = HashBasedTable.create();
        for (TemplateSaveReqVO.CookMeal cookMeal : cookMeals) {
            TemplateSaveReqVO.CookSub cookSub = cookSubMap.get(cookMeal.getName());
            Map<String, TemplateSaveReqVO.CookWeekSub> cookWeekSubMap = cookSub.getCookWeekSubs().stream()
                    .collect(Collectors.toMap(TemplateSaveReqVO.CookWeekSub::getWeekId, Function.identity()));
            String weekId = cookMeal.getWeekId();
            String[] split = weekId.split(",");
            for (String str : split) {
                TemplateSaveReqVO.CookWeekSub cookWeekSub = cookWeekSubMap.get(str);
                List<TemplateSaveReqVO.Cook> cookList = table.get(str, dsMap.get(cookMeal.getName()));
                if (cookList == null) {
                    cookList = new java.util.LinkedList<>();
                    table.put(str, dsMap.get(cookMeal.getName()), cookList);
                }
                cookList.addAll(cookWeekSub.getCooks());
            }
        }
        Set<String> strings = table.rowKeySet();
        for (String str : strings) {
            switch (str) {
                case "1":
                    templateDO.setMondayCookbook(JSONUtil.toJsonStr(table.row(str)));
                    break;
                case "2":
                    templateDO.setTuesdayCookbook(JSONUtil.toJsonStr(table.row(str)));
                    break;
                case "3":
                    templateDO.setWednesdayCookbook(JSONUtil.toJsonStr(table.row(str)));
                    break;
                case "4":
                    templateDO.setThursdayCookbook(JSONUtil.toJsonStr(table.row(str)));
                    break;
                case "5":
                    templateDO.setFridayCookbook(JSONUtil.toJsonStr(table.row(str)));
                    break;
                case "6":
                    templateDO.setSaturdayCookbook(JSONUtil.toJsonStr(table.row(str)));
                    break;
                case "7":
                    templateDO.setSundayCookbook(JSONUtil.toJsonStr(table.row(str)));
                    break;
                default:
                    break;
            }
        }
        return templateDO;
    }

}
