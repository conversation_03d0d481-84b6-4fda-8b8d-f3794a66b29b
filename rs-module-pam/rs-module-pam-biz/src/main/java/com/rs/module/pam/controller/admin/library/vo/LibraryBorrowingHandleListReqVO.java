package com.rs.module.pam.controller.admin.library.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-图书借阅处理列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LibraryBorrowingHandleListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("借阅id")
    private String borrowingId;

    @ApiModelProperty("借阅状态（字典：ZD_TSJY_JYZT）")
    private String status;

    @ApiModelProperty("处理人id")
    private String handleUserSfzh;

    @ApiModelProperty("处理人姓名")
    private String handleUser;

    @ApiModelProperty("处理时间")
    private Date[] handleTime;

}
