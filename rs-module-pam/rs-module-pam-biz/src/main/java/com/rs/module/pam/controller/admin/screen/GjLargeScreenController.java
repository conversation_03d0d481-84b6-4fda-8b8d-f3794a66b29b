package com.rs.module.pam.controller.admin.screen;


import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.screen.vo.FocusOnPersonnelRespVO;
import com.rs.module.pam.controller.admin.screen.vo.JsswItemRespVO;
import com.rs.module.pam.controller.admin.screen.vo.WgdjRespVO;
import com.rs.module.pam.entity.screen.FocusOnPersonnelDO;
import com.rs.module.pam.entity.screen.WgdjDO;
import com.rs.module.pam.service.screen.GjLargeScreenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监所事务管理-管教值班大屏-大屏业务")
@RestController
@RequestMapping("/pam/screen/GjLargeScreen")
@Validated
public class GjLargeScreenController {

    @Resource
    private GjLargeScreenService gjLargeScreenService;

    @GetMapping("/jydt/jyrs")
    @ApiOperation(value = "羁押动态-羁押信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    public CommonResult<JSONObject> getJydtJyrs(@RequestParam("code") String code,
                                                @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        return success(gjLargeScreenService.getJydtJyrs(code, codeType));
    }

    @GetMapping("/jydt/jstz")
    @ApiOperation(value = "羁押动态-监室调整信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    public CommonResult<JSONObject> getJydtJstz(@RequestParam("code") String code,
                                                @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        return success(gjLargeScreenService.getJydtJstz(code, codeType));
    }

    @GetMapping("/fxryfb")
    @ApiOperation(value = "风险人员分布")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    public CommonResult<JSONObject> getFxryfb(@RequestParam("code") String code,
                                              @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        return success(gjLargeScreenService.getFxryfb(code, codeType));
    }



    @GetMapping("/getWcdtTx")
    @ApiOperation(value = "外出动态-提讯")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    public CommonResult<Integer> getWcdtTx(@RequestParam("code") String code,
                                           @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        return success(gjLargeScreenService.getWcdtTx(code, codeType));
    }

    @GetMapping("/getWcdtTj")
    @ApiOperation(value = "外出动态-提解")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    public CommonResult<Integer> getWcdtTj(@RequestParam("code") String code,
                                           @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        return success(gjLargeScreenService.getWcdtTj(code, codeType));
    }

    @GetMapping("/getWcdtLshj")
    @ApiOperation(value = "外出动态-律师会见")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    public CommonResult<Integer> getWcdtLshj(@RequestParam("code") String code,
                                             @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        return success(gjLargeScreenService.getWcdtLshj(code, codeType));
    }

    @GetMapping("/getWcdtJshj")
    @ApiOperation(value = "外出动态-家属会见")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    public CommonResult<Integer> getWcdtJshj(@RequestParam("code") String code,
                                             @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        return success(gjLargeScreenService.getWcdtJshj(code, codeType));
    }

    @GetMapping("/getWcdtSglshj")
    @ApiOperation(value = "外出动态-使馆领事会见")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    public CommonResult<Integer> getWcdtSglshj(@RequestParam("code") String code,
                                               @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        return success(gjLargeScreenService.getWcdtSglshj(code, codeType));
    }

    @GetMapping("/getWcdtCsjy")
    @ApiOperation(value = "外出动态-出所就医")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    public CommonResult<Integer> getWcdtCsjy(@RequestParam("code") String code,
                                             @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        return success(gjLargeScreenService.getWcdtCsjy(code, codeType));
    }

    @GetMapping("/getWcdtAllCount")
    @ApiOperation(value = "外出动态-全部信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    public CommonResult<JSONObject> getWcdtAll(@RequestParam("code") String code,
                                             @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        return success(gjLargeScreenService.getWcdtAll(code, codeType));
    }


    // 临时出所未归数字 -- 待出需求及开发

    // 违规告警 更多支持跳转  -- 待出需求及开发
    @GetMapping("/getWggjPage")
    @ApiOperation(value = "违规告警列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "timeRange", value = "周期类型 1本日 2本周 3本月 4本年 ", required = true),
            @ApiImplicitParam(name = "handleStatus", value = "处理类型 1全部 2未处理 3已处理 ", required = true),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true)
    })
    public CommonResult<PageResult<WgdjRespVO>> getWggjPage(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                              @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                              @RequestParam(name = "timeRange", defaultValue = "2") String timeRange,
                                                              @RequestParam(name = "handleStatus", defaultValue = "1") String handleStatus,
                                                              @RequestParam("code") String code,
                                                              @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        PageResult<WgdjDO> pageResult = gjLargeScreenService.getWggjPage(pageNo, pageSize, timeRange, handleStatus, code, codeType);
        return success(BeanUtils.toBean(pageResult, WgdjRespVO.class));
    }

    @GetMapping("/getWggjAllCount")
    @ApiOperation(value = "违规告警数据统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "timeRange", value = "周期类型 1本日 2本周 3本月 4本年 ", required = true)
    })
    public CommonResult<JSONObject> getWggjAllCount(
                                                            @RequestParam(name = "timeRange", defaultValue = "2") String timeRange,
                                                            @RequestParam("code") String code,
                                                            @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        return success(gjLargeScreenService.getWggjAllCount(timeRange, code, codeType));
    }

    // 监室监控

    // 监室事务 项总数查询
    @GetMapping("/jssw/itemCount")
    @ApiOperation(value = "监室事务项-各个总数查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    public CommonResult<List<JSONObject>> getJsswItemCount(@RequestParam("code") String code,
                                                           @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        return success(gjLargeScreenService.getJsswItemCount(code, codeType));
    }

    // 监室事务 项列表查询
    @GetMapping("/jssw/itemList")
    @ApiOperation(value = "监室事务项-各个列表查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "busType", value = "事项类型", required = true)
    })
    public CommonResult<List<JsswItemRespVO>> getJsswItemList(@RequestParam("code") String code,
                                                              @RequestParam(value = "codeType", defaultValue = "01") String codeType,
                                                              @RequestParam(value = "busType", defaultValue = "0") String busType) {
        Assert.notBlank(code, "code不能为空");
        return success(gjLargeScreenService.getJsswItemList(code, codeType, busType));
    }


    // 监室值班 调值班管理值班台账详情接口获取
    //  重点关注人员-单独关押
    @GetMapping("/getZdgzryDdgy")
    @ApiOperation(value = "重点关注人员-单独关押")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true)
    })
    public CommonResult<PageResult<FocusOnPersonnelRespVO>> getZdgzryDdgy(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                                          @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                                          @RequestParam("code") String code,
                                                                          @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        PageResult<FocusOnPersonnelDO> pageResult = gjLargeScreenService.getZdgzryDdgy(pageNo, pageSize, code, codeType);
        return success(BeanUtils.toBean(pageResult, FocusOnPersonnelRespVO.class));
    }

    //  重点关注人员-戒具使用
    @GetMapping("/getZdgzryJjsy")
    @ApiOperation(value = "重点关注人员-戒具使用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true)
    })
    public CommonResult<PageResult<FocusOnPersonnelRespVO>> getZdgzryJjsy(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                                          @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                                          @RequestParam("code") String code,
                                                                          @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        PageResult<FocusOnPersonnelDO> pageResult = gjLargeScreenService.getZdgzryJjsy(pageNo, pageSize, code, codeType);
        return success(BeanUtils.toBean(pageResult, FocusOnPersonnelRespVO.class));
    }

    //  重点关注人员-临时固定
    @GetMapping("/getZdgzryLsgd")
    @ApiOperation(value = "重点关注人员-临时固定")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true)
    })
    public CommonResult<PageResult<FocusOnPersonnelRespVO>> getZdgzryLsgd(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                                          @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                                          @RequestParam("code") String code,
                                                                          @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        PageResult<FocusOnPersonnelDO> pageResult = gjLargeScreenService.getZdgzryLsgd(pageNo, pageSize, code, codeType);
        return success(BeanUtils.toBean(pageResult, FocusOnPersonnelRespVO.class));
    }

    // 重点关注人员-重点人员
    @GetMapping("/getZdgzryZdry")
    @ApiOperation(value = "重点关注人员-重点人员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true)
    })
    public CommonResult<PageResult<FocusOnPersonnelRespVO>> getZdgzryZdry(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                                          @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                                          @RequestParam("code") String code,
                                                                          @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        PageResult<FocusOnPersonnelDO> pageResult = gjLargeScreenService.getZdgzryZdry(pageNo, pageSize, code, codeType);
        return success(BeanUtils.toBean(pageResult, FocusOnPersonnelRespVO.class));
    }

    // 重点关注人员-外籍人员
    @GetMapping("/getZdgzryWjry")
    @ApiOperation(value = "重点关注人员-外籍人员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true)
    })
    public CommonResult<PageResult<FocusOnPersonnelRespVO>> getZdgzryWjry(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                                          @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                                          @RequestParam("code") String code,
                                                                          @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        PageResult<FocusOnPersonnelDO> pageResult = gjLargeScreenService.getZdgzryWjry(pageNo, pageSize, code, codeType);
        return success(BeanUtils.toBean(pageResult, FocusOnPersonnelRespVO.class));
    }

    // 重点关注人员-今日到期
    @GetMapping("/getZdgzryJrdq")
    @ApiOperation(value = "重点关注人员-今日到期")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true)
    })
    public CommonResult<PageResult<FocusOnPersonnelRespVO>> getZdgzryJrdq(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                                          @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                                          @RequestParam("code") String code,
                                                                          @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        PageResult<FocusOnPersonnelDO> pageResult = gjLargeScreenService.getZdgzryJrdq(pageNo, pageSize, code, codeType);
        return success(BeanUtils.toBean(pageResult, FocusOnPersonnelRespVO.class));
    }

    // 重点关注人员-重病号
    @GetMapping("/getZdgzryZbh")
    @ApiOperation(value = "重点关注人员-重病号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true)
    })
    public CommonResult<PageResult<FocusOnPersonnelRespVO>> getZdgzryZbh(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                                          @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                                         @RequestParam("code") String code,
                                                                         @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        PageResult<FocusOnPersonnelDO> pageResult = gjLargeScreenService.getZdgzryZbh(pageNo, pageSize, code, codeType);
        return success(BeanUtils.toBean(pageResult, FocusOnPersonnelRespVO.class));
    }

    // 重点关注人员-风险人员
    @GetMapping("/getZdgzryFxry")
    @ApiOperation(value = "重点关注人员-风险人员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true)
    })
    public CommonResult<PageResult<FocusOnPersonnelRespVO>> getZdgzryFxry(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                                         @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                                         @RequestParam("code") String code,
                                                                         @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        PageResult<FocusOnPersonnelDO> pageResult = gjLargeScreenService.getZdgzryFxry(pageNo, pageSize, code, codeType);
        return success(BeanUtils.toBean(pageResult, FocusOnPersonnelRespVO.class));
    }

    // 重点关注人员-全部人员列表
    @GetMapping("/getZdgzryAllPage")
    @ApiOperation(value = "重点关注人员-全部人员列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true),
            @ApiImplicitParam(name = "bizType", value = "业务类型", required = false)
    })
    public CommonResult<PageResult<FocusOnPersonnelRespVO>> getZdgzryAllPage(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                                         @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                                         @RequestParam("code") String code,
                                                                         @RequestParam(value = "codeType", defaultValue = "01") String codeType,
                                                                         @RequestParam(name = "bizType", required = false) String bizType) {
        Assert.notBlank(code, "code不能为空");
        List<String> methodNames;
        if(StringUtils.isEmpty(bizType) || "all".equals(bizType)){
            methodNames = new ArrayList<>(Arrays.asList("getZdgzryDdgy", "getZdgzryJjsy", "getZdgzryZdry",
                    "getZdgzryWjry", "getZdgzryJrdq", "getZdgzryZbh"));
        } else {
            methodNames = Collections.singletonList(bizType);
        }
        PageResult<FocusOnPersonnelDO> pageResult = gjLargeScreenService.getZdgzryAllPage(pageNo, pageSize, code, codeType, methodNames);
        return success(BeanUtils.toBean(pageResult, FocusOnPersonnelRespVO.class));
    }

    @GetMapping("/getZdgzryAllCount")
    @ApiOperation(value = "重点关注人员-分类人员个数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    public CommonResult<JSONObject> getZdgzryAllCount(@RequestParam("code") String code,
                                               @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        List<String> methodNames = new ArrayList<>(Arrays.asList("getZdgzryDdgy", "getZdgzryJjsy", "getZdgzryZdry",
                "getZdgzryWjry", "getZdgzryJrdq", "getZdgzryZbh"));
        return success(gjLargeScreenService.getZdgzryAllCount(code, codeType, methodNames));
    }

    @GetMapping("/test")
    public CommonResult<Boolean> test(){
        gjLargeScreenService.test();
        return success(true);
    }


}
