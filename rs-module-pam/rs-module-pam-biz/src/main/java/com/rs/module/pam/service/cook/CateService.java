package com.rs.module.pam.service.cook;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.cook.vo.CateListReqVO;
import com.rs.module.pam.controller.admin.cook.vo.CateSaveReqVO;
import com.rs.module.pam.entity.cook.CateDO;

import java.util.List;

/**
 * 监所事务管理-菜品分类 Service 接口
 *
 * <AUTHOR>
 */
public interface CateService extends IBaseService<CateDO>{

    /**
     * 创建监所事务管理-菜品分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCate(CateSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-菜品分类
     *
     * @param updateReqVO 更新信息
     */
    void updateCate(CateSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-菜品分类
     *
     * @param id 编号
     */
    void deleteCate(String id);

    /**
     * 获得监所事务管理-菜品分类
     *
     * @param id 编号
     * @return 监所事务管理-菜品分类
     */
    CateDO getCate(String id);

    /**
    * 获得监所事务管理-菜品分类列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-菜品分类列表
    */
    List<CateDO> getCateList(CateListReqVO listReqVO);


}
