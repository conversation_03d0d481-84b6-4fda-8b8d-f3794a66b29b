package com.rs.module.pam.service.cook;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.cook.vo.ManageListReqVO;
import com.rs.module.pam.controller.admin.cook.vo.ManageSaveReqVO;
import com.rs.module.pam.entity.cook.ManageDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 监所事务管理-菜品管理 Service 接口
 *
 * <AUTHOR>
 */
public interface ManageService extends IBaseService<ManageDO>{

    /**
     * 创建监所事务管理-菜品管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createManage(@Valid ManageSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-菜品管理
     *
     * @param updateReqVO 更新信息
     */
    void updateManage(@Valid ManageSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-菜品管理
     *
     * @param id 编号
     */
    void deleteManage(String id);

    /**
     * 获得监所事务管理-菜品管理
     *
     * @param id 编号
     * @return 监所事务管理-菜品管理
     */
    ManageDO getManage(String id);

    /**
    * 获得监所事务管理-菜品管理列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-菜品管理列表
    */
    List<ManageDO> getManageList(ManageListReqVO listReqVO);


}
