package com.rs.module.pam.service.daily;

import java.util.*;
import javax.validation.*;
import com.rs.module.pam.controller.admin.daily.vo.*;
import com.rs.module.pam.entity.daily.LifeDictEventDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 监所事务管理-事务选项字典 Service 接口
 *
 * <AUTHOR>
 */
public interface LifeDictEventService extends IBaseService<LifeDictEventDO>{

    /**
     * 创建监所事务管理-事务选项字典
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createLifeDictEvent(@Valid LifeDictEventSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-事务选项字典
     *
     * @param updateReqVO 更新信息
     */
    void updateLifeDictEvent(@Valid LifeDictEventSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-事务选项字典
     *
     * @param id 编号
     */
    void deleteLifeDictEvent(String id);

    /**
     * 获得监所事务管理-事务选项字典
     *
     * @param id 编号
     * @return 监所事务管理-事务选项字典
     */
    LifeDictEventDO getLifeDictEvent(String id);

    /**
    * 获得监所事务管理-事务选项字典分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-事务选项字典分页
    */
    PageResult<LifeDictEventDO> getLifeDictEventPage(LifeDictEventPageReqVO pageReqVO);

    /**
    * 获得监所事务管理-事务选项字典列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-事务选项字典列表
    */
    List<LifeDictEventDO> getLifeDictEventList(LifeDictEventListReqVO listReqVO);


}
