package com.rs.module.pam.controller.admin.bed.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@ApiModel(description = "管理后台 - 监所事务管理-监室床位配置列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ConfigListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监室id")
    private String roomId;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("布局ID")
    private String layoutId;

    @ApiModelProperty("布局名称")
    private String layoutName;

    @ApiModelProperty("床位规格长度")
    private Integer bedSpaceLength;

    @ApiModelProperty("床位规格宽度")
    private Integer bedSpaceWidth;

    @ApiModelProperty("床位规格高度")
    private Integer bedSpaceHeight;

    @ApiModelProperty("床位规格承重")
    private BigDecimal bedSpaceBearing;

    @ApiModelProperty("自动床位配置（字典：ZD_CWGL_ZDCWPZ）")
    private String bedAutoConfig;

}
