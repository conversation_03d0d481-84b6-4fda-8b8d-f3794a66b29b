package com.rs.module.pam.controller.admin.info.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @Date 2025/5/20 10:30
 */
@ApiModel(description = "管理后台-信息报备审批 Request VO")
@Data
public class ReportApproveReqVO {

    @ApiModelProperty("主键ID")
    private String id;
    @ApiModelProperty("审批结果")
    private String approvalResult;
    @ApiModelProperty("审批意见")
    private String approvalComments;
    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;
    @ApiModelProperty("审批人姓名")
    private String approverXm;

}