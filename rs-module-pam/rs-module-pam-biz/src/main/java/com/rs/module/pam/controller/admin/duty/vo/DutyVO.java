package com.rs.module.pam.controller.admin.duty.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-监室值班列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DutyVO extends BaseVO {

    @ApiModelProperty("机构编码")
    private String orgCode;

    @ApiModelProperty("监室编号")
    private String roomId;

    @ApiModelProperty("开始时间")
    private Date startDate;

    @ApiModelProperty("结束时间")
    private Date endDate;

    @ApiModelProperty("班次列表")
    private List<ShiftRespVO> shiftList;

    @ApiModelProperty("值班列表")
    private List<DutyListRespVO> dutyList;

}
