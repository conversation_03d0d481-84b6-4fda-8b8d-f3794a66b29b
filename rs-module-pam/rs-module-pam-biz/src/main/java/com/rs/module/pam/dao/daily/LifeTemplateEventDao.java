package com.rs.module.pam.dao.daily;

import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.PageParam;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.pam.entity.daily.LifeTemplateEventDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
 * 监所事务管理-一日生活制度模板关联事务
 *
 * <AUTHOR>
 */
@Mapper
public interface LifeTemplateEventDao extends IBaseDao<LifeTemplateEventDO> {

    default List<LifeTemplateEventDO> selectListByTemplateId(String templateId) {
        return selectList(new LambdaQueryWrapperX<LifeTemplateEventDO>().eq(LifeTemplateEventDO::getTemplateId, templateId)
        .orderByAsc(LifeTemplateEventDO::getStartTime));
    }

    default int deleteByTemplateId(String templateId) {
        return delete(new LambdaQueryWrapperX<LifeTemplateEventDO>().eq(LifeTemplateEventDO::getTemplateId, templateId));
    }

}
