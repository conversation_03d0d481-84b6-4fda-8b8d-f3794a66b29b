package com.rs.module.pam.entity.duty;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import java.util.Date;

/**
 * 监所事务管理-值班班次 DO
 *
 * <AUTHOR>
 */
@TableName("pam_duty_shift")
@KeySequence("pam_duty_shift_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShiftDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 班次名称
     */
    private String shiftName;
    /**
     * 开始时间类型（1：当天，2：次日）
     */
    private String startTimeType;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间类型（1：当天，2：次日）
     */
    private String endTimeType;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 生效开始时间
     */
    private Date effectiveStartDate;
    /**
     * 生效结束时间
     */
    private Date effectiveEndDate;
    /**
     * 排序
     */
    private Integer sort;

}
