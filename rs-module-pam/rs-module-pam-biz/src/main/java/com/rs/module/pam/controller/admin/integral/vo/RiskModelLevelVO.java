package com.rs.module.pam.controller.admin.integral.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@ApiModel(description = "风险模型等级 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RiskModelLevelVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("级别名称")
    private String name;
    @ApiModelProperty("排序")
    private Integer orderId;
    @ApiModelProperty("阈值开始值")
    private String thresholdStartValue;
    @ApiModelProperty("阈值结束值")
    private String thresholdEndValue;
}
