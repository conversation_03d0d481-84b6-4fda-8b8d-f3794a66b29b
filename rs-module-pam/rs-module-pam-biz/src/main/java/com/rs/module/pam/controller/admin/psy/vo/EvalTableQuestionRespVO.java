package com.rs.module.pam.controller.admin.psy.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评量表关联提目 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EvalTableQuestionRespVO extends BaseVO implements TransPojo {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("量表ID")
    private String tableId;
    @ApiModelProperty("题目内容")
    private String questionText;
    @ApiModelProperty("题目类型（字典：ZD_XLPC_TMLX）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XLPC_TMLX")
    private String questionType;
    @ApiModelProperty("单题计分规则")
    @Trans(type = TransType.DICTIONARY, key = "ZD_DTJFGZ")
    private String scoreRule;
    @ApiModelProperty("参考答案，选择题答案保存选项代码，多选题多个答案逗号分割，简答题保存答案")
    private String answer;
    @ApiModelProperty("题目分值")
    private BigDecimal score;
    @ApiModelProperty("题目顺序")
    private Integer sortOrder;
    @ApiModelProperty("题目选项")
    private List<EvalTableOptionRespVO> options;

}
