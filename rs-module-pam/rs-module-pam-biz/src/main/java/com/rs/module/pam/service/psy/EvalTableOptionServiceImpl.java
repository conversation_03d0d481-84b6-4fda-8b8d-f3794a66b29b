package com.rs.module.pam.service.psy;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.framework.mybatis.core.query.QueryWrapperX;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.pam.controller.admin.psy.vo.*;
import com.rs.module.pam.entity.psy.EvalTableOptionDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.pam.dao.psy.EvalTableOptionDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 监所事务管理-心理测评量表关联提目选项 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EvalTableOptionServiceImpl extends BaseServiceImpl<EvalTableOptionDao, EvalTableOptionDO> implements EvalTableOptionService {

    @Resource
    private EvalTableOptionDao evalTableOptionDao;

    @Override
    public String createEvalTableOption(EvalTableOptionSaveReqVO createReqVO) {
        // 插入
        EvalTableOptionDO evalTableOption = BeanUtils.toBean(createReqVO, EvalTableOptionDO.class);
        evalTableOptionDao.insert(evalTableOption);
        // 返回
        return evalTableOption.getId();
    }

    @Override
    public void updateEvalTableOption(EvalTableOptionSaveReqVO updateReqVO) {
        // 校验存在
        validateEvalTableOptionExists(updateReqVO.getId());
        // 更新
        EvalTableOptionDO updateObj = BeanUtils.toBean(updateReqVO, EvalTableOptionDO.class);
        evalTableOptionDao.updateById(updateObj);
    }

    @Override
    public void deleteEvalTableOption(String id) {
        // 校验存在
        validateEvalTableOptionExists(id);
        // 删除
        evalTableOptionDao.deleteById(id);
    }

    private void validateEvalTableOptionExists(String id) {
        if (evalTableOptionDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-心理测评量表关联提目选项数据不存在");
        }
    }

    @Override
    public EvalTableOptionDO getEvalTableOption(String id) {
        return evalTableOptionDao.selectById(id);
    }

    @Override
    public PageResult<EvalTableOptionDO> getEvalTableOptionPage(EvalTableOptionPageReqVO pageReqVO) {
        return evalTableOptionDao.selectPage(pageReqVO);
    }

    @Override
    public List<EvalTableOptionDO> getEvalTableOptionList(EvalTableOptionListReqVO listReqVO) {
        return evalTableOptionDao.selectList(listReqVO);
    }

    @Override
    public void deleteEvalTableOptionByQuestionId(String questionId) {
        evalTableOptionDao.deleteEvalTableOptionByQuestionId(questionId);
    }

    @Override
    public List<EvalTableOptionRespVO> getListByQuestionId(String questionId) {
        Wrapper<EvalTableOptionDO> wrapper = new LambdaQueryWrapper<EvalTableOptionDO>()
                .eq(EvalTableOptionDO::getQuestionId, questionId)
                .orderByAsc(EvalTableOptionDO::getOptionCode);
        List<EvalTableOptionDO> list = evalTableOptionDao.selectList(wrapper);
        if (list != null && !list.isEmpty()) {
            List<EvalTableOptionRespVO> respVOList = new ArrayList<>();
            for (EvalTableOptionDO evalTableOptionDO : list) {
                EvalTableOptionRespVO respVO = BeanUtils.toBean(evalTableOptionDO, EvalTableOptionRespVO.class);
                respVOList.add(respVO);
            }
            return respVOList;
        }
        return Collections.emptyList();
    }

    @Override
    public List<EvalTableOptionRespVO> getListByQuestionId(List<String> questionIds) {
        Wrapper<EvalTableOptionDO> wrapper = new LambdaQueryWrapper<EvalTableOptionDO>()
                .in(EvalTableOptionDO::getQuestionId, questionIds)
                .orderByAsc(EvalTableOptionDO::getOptionCode);
        List<EvalTableOptionDO> list = evalTableOptionDao.selectList(wrapper);
        List<EvalTableOptionRespVO> respVOList = new ArrayList<>();
        if (list != null && !list.isEmpty()) {
            for (EvalTableOptionDO evalTableOptionDO : list) {
                EvalTableOptionRespVO respVO = BeanUtils.toBean(evalTableOptionDO, EvalTableOptionRespVO.class);
                respVOList.add(respVO);
            }
        }
        return respVOList;
    }
}
