package com.rs.module.pam.controller.admin.duty.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-监室值班记录关联签到新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RecordsSigninSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监室id")
    @NotEmpty(message = "监室id不能为空")
    private String roomId;

    @ApiModelProperty("值班记录：pam_duty_records.id")
    @NotEmpty(message = "值班记录：pam_duty_records.id不能为空")
    private String recordsId;

    @ApiModelProperty("值班日期")
    @NotNull(message = "值班日期不能为空")
    private Date dutyDate;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("签到状态（字典：ZD_ZBGL_QDZT）")
    @NotEmpty(message = "签到状态（字典：ZD_ZBGL_QDZT）不能为空")
    private String signinStatus;

    @ApiModelProperty("签到时间")
    private Date signinTime;

    @ApiModelProperty("抓拍图片地址")
    private String snapPictureUrl;

}
