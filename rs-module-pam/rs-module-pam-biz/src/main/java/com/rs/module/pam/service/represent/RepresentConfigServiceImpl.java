package com.rs.module.pam.service.represent;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.SocketService;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.quartz.client.HttpXxlJobApi;
import com.rs.framework.quartz.cons.ScheduleTypeEnum;
import com.rs.framework.quartz.entity.XxlJobInfo;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import com.rs.module.pam.controller.admin.represent.vo.RepresentConfigListReqVO;
import com.rs.module.pam.controller.admin.represent.vo.RepresentConfigPageReqVO;
import com.rs.module.pam.controller.admin.represent.vo.RepresentConfigSaveReqVO;
import com.rs.module.pam.dao.represent.RepresentConfigDao;
import com.rs.module.pam.entity.represent.RepresentConfigDO;
import com.rs.module.pam.util.CronUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 监所事务管理-监室点名配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RepresentConfigServiceImpl extends BaseServiceImpl<RepresentConfigDao, RepresentConfigDO> implements RepresentConfigService {

    @Resource
    private RepresentConfigDao representConfigDao;
    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;
    @Resource
    private HttpXxlJobApi httpXxlJobApi;
    @Resource
    private SocketService socketService;

    private Integer groupId = 5;

    @Override
    @Transactional
    public String createConfig(RepresentConfigSaveReqVO createReqVO) {
        // 插入
        RepresentConfigDO config = BeanUtils.toBean(createReqVO, RepresentConfigDO.class);
        List<String> roomIdList = Arrays.asList(createReqVO.getRoomId().split(","));

        // 判断所选监室是否已有该时间点的计划
        List<RepresentConfigDO> configList = representConfigDao.getByOrgCodeAndTime(createReqVO.getOrgCode(), createReqVO.getStartTime());
        for(RepresentConfigDO configDO : configList){
            List<String> roomIds = Arrays.asList(configDO.getRoomId().split(","));
            List<String> roomNames = Arrays.asList(configDO.getRoomName().split(","));
            for(String roomId : roomIdList){
                if (roomIds.contains(roomId) && !configDO.getId().equals(createReqVO.getId()))
                    throw new ServerException(roomNames.get(roomIds.indexOf(roomId)) + "在该时间点已存在点名计划");
            }
        }

        // 获取监室信息
        List<AreaPrisonRoomDO> roomList = areaPrisonRoomService.getAreaPrisonRoomList(roomIdList);
        config.setRoomName(roomList.stream().map(room -> room.getRoomName()).collect(Collectors.joining(",")));

        // 获取cron表达式
        DateTime date = DateUtil.parse(createReqVO.getStartTime() + ":00", "HH:mm:ss");
//        Date date = DateUtils.stringToDate(createReqVO.getStartTime(), "HH:mm:ss");
        Date endTime = DateUtil.offsetMinute(date, createReqVO.getExpiryDate());
        String endTimeStr = DateUtil.format(endTime, "HH:mm:ss");
        config.setEndTime(endTimeStr);
        String[] times = createReqVO.getStartTime().split(":");
        String[] endTimes = config.getEndTime().split(":");
        if (createReqVO.getConfigPeriodCode().equals("1")) {
            config.setCron(CronUtils.getCronWeek(times[0], times[1], config.getConfigPeriod()));
            config.setEndCron(CronUtils.getCronWeek(endTimes[0], endTimes[1], config.getConfigPeriod()));
        } else if (createReqVO.getConfigPeriodCode().equals("2")) {
            config.setCron(CronUtils.getCron(times[0], times[1]));
            config.setEndCron(CronUtils.getCron(endTimes[0], endTimes[1]));
        } else if (createReqVO.getConfigPeriodCode().equals("3")) {
            config.setCron(CronUtils.getCronOne(times[0], times[1]));
            config.setEndCron(CronUtils.getCronOne(endTimes[0], endTimes[1]));
        }
        saveOrUpdate(config);
        return config.getId();
    }

    private XxlJobInfo createXxlJobInfo(String jobName, Integer status, String cron, String jobHandler) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        XxlJobInfo xxlJobInfo = new XxlJobInfo();
        xxlJobInfo.setJobDesc(jobName);
        xxlJobInfo.setJobGroup(groupId);
        xxlJobInfo.setAuthor(sessionUser.getName());
        xxlJobInfo.setTriggerStatus(status);
        xxlJobInfo.setScheduleConf(cron);

        //运行策略类型
        xxlJobInfo.setScheduleType(ScheduleTypeEnum.CRON.getXxlValue());
        xxlJobInfo.setMisfireStrategy("DO_NOTHING");
        xxlJobInfo.setExecutorRouteStrategy("RANDOM");    //随机执行器执行
        xxlJobInfo.setExecutorHandler(jobHandler);
        xxlJobInfo.setExecutorParam("");
        xxlJobInfo.setExecutorBlockStrategy("SERIAL_EXECUTION");    //单机串行
        xxlJobInfo.setGlueType("BEAN");

        return xxlJobInfo;
    }

    @Override
    public void updateConfig(RepresentConfigSaveReqVO updateReqVO) {
        // 校验存在
        validateConfigExists(updateReqVO.getId());
        // 更新
        RepresentConfigDO updateObj = BeanUtils.toBean(updateReqVO, RepresentConfigDO.class);
        representConfigDao.updateById(updateObj);
    }

    @Override
    public void deleteConfig(String id) {
        // 校验存在
        RepresentConfigDO configDO = representConfigDao.selectById(id);
        if (configDO == null) {
            return;
        }
        // 删除xxl-job任务
        try {
            httpXxlJobApi.remove(configDO.getJobId());
        } catch (Exception e) {}
        // 删除
        representConfigDao.deleteById(id);
    }

    private void validateConfigExists(String id) {
        if (representConfigDao.selectById(id) == null) {
//            throw new ServerException("监所事务管理-监室点名配置数据不存在");
        }
    }

    @Override
    public RepresentConfigDO getConfig(String id) {
        return representConfigDao.selectById(id);
    }

    @Override
    public PageResult<RepresentConfigDO> getConfigPage(RepresentConfigPageReqVO pageReqVO) {
        return representConfigDao.selectPage(pageReqVO);
    }

    @Override
    public List<RepresentConfigDO> getConfigList(RepresentConfigListReqVO listReqVO) {
        return representConfigDao.selectList(listReqVO);
    }


}
