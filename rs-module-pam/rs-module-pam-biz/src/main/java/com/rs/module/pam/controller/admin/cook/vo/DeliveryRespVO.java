package com.rs.module.pam.controller.admin.cook.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-发饭主 Response VO")
@Data
public class DeliveryRespVO implements TransPojo {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监室ID")
    private String roomId;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("用餐时段")
    @Trans(type = TransType.DICTIONARY, key = "ZD_PCGL_DSLX")
    private String mealPeriod;
    @ApiModelProperty("菜谱名称")
    private String cookbookDate;
    @ApiModelProperty("监室总人数")
    private Integer roomTotalPersons;
    @ApiModelProperty("在监总人数")
    private Integer custodyTotalPersons;
    @ApiModelProperty("外出总人数")
    private Integer outgoingTotalPersons;
    @ApiModelProperty("已领饭人数")
    private Integer mealReceivedTotal;
    @ApiModelProperty("未领饭人数")
    private Integer mealUnreceivedTotal;
    @ApiModelProperty("留饭人数")
    private Integer mealReservedTotal;
    @ApiModelProperty("当日食谱")
    private String cookbook;
    @ApiModelProperty("发饭时间")
    private Date deliveryTime;
    @ApiModelProperty("经办民警身份证号")
    private String operatePoliceSfzh;
    @ApiModelProperty("经办民警")
    private String operatePolice;
    @ApiModelProperty("经办时间")
    private Date operateTime;
    @ApiModelProperty("状态(0: 发饭中，1: 已完成)")
    private String status;
    @ApiModelProperty("状态名称")
    private String statusName;
    @ApiModelProperty(value = "未发饭人员")
    private String noDeliveryPersons;
    @ApiModelProperty(value = "留饭人员")
    private String receiptPersons;
}
