package com.rs.module.pam.controller.admin.bed.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-监室床位配置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ChangeSaveRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键", hidden = true)
    private String id;
    @ApiModelProperty("机构编号")
    private String orgCode;
    @ApiModelProperty("监室id")
    private String roomId;
    @ApiModelProperty("布局信息")
    private List<BedAreaConfigVO> layoutConfigs;
}
