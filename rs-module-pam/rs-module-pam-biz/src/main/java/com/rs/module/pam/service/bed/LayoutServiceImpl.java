package com.rs.module.pam.service.bed;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.oss.entity.FileDetail;
import com.rs.module.oss.service.FileDetailService;
import com.rs.module.pam.controller.admin.bed.vo.*;
import com.rs.module.pam.dao.bed.ConfigDao;
import com.rs.module.pam.dao.bed.LayoutDao;
import com.rs.module.pam.entity.bed.ConfigDO;
import com.rs.module.pam.entity.bed.LayoutDO;
import org.dromara.x.file.storage.core.FileInfo;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 监所事务管理-监室床位布局 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LayoutServiceImpl extends BaseServiceImpl<LayoutDao, LayoutDO> implements LayoutService {

    @Resource
    private LayoutDao layoutDao;
    @Resource
    private ConfigDao configDao;
    @Resource
    private FileDetailService fileDetailService;

    @Override
    public String createLayout(LayoutSaveReqVO createReqVO) {
        // 插入
        LayoutDO layout = BeanUtils.toBean(createReqVO, LayoutDO.class);
        List<BedAreaConfigVO> layoutConfig = createReqVO.getLayoutConfig();
        layoutConfig.forEach(layoutDetail -> layoutDetail.setId(StringUtil.getGuid32()));
        layout.setLayoutConfig(JSONObject.toJSONString(layoutConfig));
        layout.setLayoutImageUrl(createReqVO.getLayoutUrl());
        layoutDao.insert(layout);

        // 插入附件信息
//        createFileList(layout.getId(), createReqVO.getFileInfo());
        // 返回
        return layout.getId();
    }

    @Override
    public void updateLayout(LayoutSaveReqVO updateReqVO) {
        // 校验存在
        validateLayoutExists(updateReqVO.getId());
        // 更新
        LayoutDO updateObj = BeanUtils.toBean(updateReqVO, LayoutDO.class);
        List<BedAreaConfigVO> layoutConfig = updateReqVO.getLayoutConfig();
        layoutConfig.forEach(layoutDetail -> {
            if (StringUtil.isNullBlank(layoutDetail.getId())) {
                layoutDetail.setId(StringUtil.getGuid32());
            }
        });
        updateObj.setLayoutConfig(JSONObject.toJSONString(layoutConfig));
        updateObj.setLayoutImageUrl(updateReqVO.getLayoutUrl());
        // 同步
//        List<ConfigDO> configDOS = configDao.selectList(new LambdaQueryWrapper<ConfigDO>()
//                .eq(ConfigDO::getLayoutId, updateObj.getId()));
//        configDOS.forEach(configDO -> {
//            configDO.setLayoutId(updateObj.getId());
//            configDO.setLayoutName(updateObj.getLayoutName());
//            configDO.setLayoutConfig(updateObj.getLayoutConfig());
//            configDao.updateById(configDO);
//        });
        // 插入附件信息
//        createFileList(updateObj.getId(), updateReqVO.getFileInfo());
        layoutDao.updateById(updateObj);
    }

    @Override
    public void deleteLayout(String id) {
        // 校验存在
        validateLayoutExists(id);
        // 删除
        layoutDao.deleteById(id);
    }

    private void validateLayoutExists(String id) {
        if (layoutDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-监室床位布局数据不存在");
        }
    }

    @Override
    public LayoutDO getLayout(String id) {
        return layoutDao.selectById(id);
    }

    @Override
    public PageResult<LayoutDO> getLayoutPage(LayoutPageReqVO pageReqVO) {
        return layoutDao.selectPage(pageReqVO);
    }

    @Override
    public List<LayoutDO> getLayoutList(LayoutListReqVO listReqVO) {
        return layoutDao.selectList(listReqVO);
    }

    private void createFileList(String objectId, FileInfo fileInfo) {
        fileDetailService.remove(new LambdaQueryWrapper<FileDetail>().eq(FileDetail::getObjectId, objectId));
        try {
            FileDetail fileDetail = fileDetailService.toFileDetail(fileInfo);
            fileDetailService.remove(new LambdaQueryWrapper<FileDetail>().eq(FileDetail::getObjectId, objectId));
            fileDetail.setObjectId(objectId);
            fileDetailService.saveOrUpdate(fileDetail);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }


}
