package com.rs.module.pam.entity.duty.day;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-监室值日 DO
 *
 * <AUTHOR>
 */
@TableName("pam_day_duty")
@KeySequence("pam_day_duty_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_day_duty")
public class DayDutyDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 值班日期
     */
    private Date dutyDate;
    /**
     * 班次数
     */
    private Short shiftNumber;
    /**
     * 排班人ID
     */
    private String assignerUserSfzh;
    /**
     * 排班人名称
     */
    private String assignerUserName;
    /**
     * 排班时间
     */
    private Date assignedTime;

}
