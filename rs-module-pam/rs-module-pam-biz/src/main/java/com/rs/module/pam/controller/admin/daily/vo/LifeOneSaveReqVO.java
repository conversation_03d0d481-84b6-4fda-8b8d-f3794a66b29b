package com.rs.module.pam.controller.admin.daily.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.pam.entity.daily.LifeEventDO;
import com.rs.module.pam.entity.daily.LifeRoomDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-一日生活制度新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LifeOneSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("制度名称")
    @NotEmpty(message = "制度名称不能为空")
    private String name;

    @ApiModelProperty("周期设置（字典：ZD_YRSHZD_ZQSZ）")
    @NotEmpty(message = "周期设置（字典：ZD_YRSHZD_ZQSZ）不能为空")
    private String cycleSetting;

    @ApiModelProperty("cycle_config")
    private String cycleConfig;

    @ApiModelProperty("自定义时间")
    private String customizeTime;

    @ApiModelProperty("报备状态（字典：ZD_XXBB_BBZT）")
    @NotEmpty(message = "报备状态（字典：ZD_XXBB_BBZT）不能为空")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("领导签名")
    private String approvalAutograph;

    @ApiModelProperty("领导签名日期")
    private Date approvalAutographTime;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

}
