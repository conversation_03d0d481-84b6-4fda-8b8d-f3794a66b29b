package com.rs.module.pam.entity.cook;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 监所事务管理-发饭记录 DO
 *
 * <AUTHOR>
 */
@TableName("pam_cook_delivery_record")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_cook_delivery_record")
public class DeliveryRecordDO extends BaseDO {
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 发饭ID
     */
    private String deliveryId;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 配餐类型（字典：ZD_PCGL_PCLX ）
     */
    private String mealType;
    /**
     * 当日食谱
     */
    private String cookbook;
    /**
     * 发饭时间
     */
    private Date deliveryTime;
    /**
     * 状态
     */
    private String status;

}
