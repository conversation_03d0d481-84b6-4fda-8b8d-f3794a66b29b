package com.rs.module.pam.service.duty.day;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.google.common.collect.Lists;
import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.feign.SocketPushFeign;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.date.DateUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.entity.pm.BaseDeviceInscreenDO;
import com.rs.module.acp.service.pm.BaseDeviceInscreenService;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.pam.controller.admin.duty.day.vo.*;
import com.rs.module.pam.controller.admin.duty.vo.DutyPrisonerVO;
import com.rs.module.pam.controller.app.vo.AppDutyRespVO;
import com.rs.module.pam.controller.app.vo.AppDutyShiftRespVO;
import com.rs.module.pam.dao.duty.DutyPrisonerDao;
import com.rs.module.pam.dao.duty.day.DayDutyDao;
import com.rs.module.pam.dao.duty.day.DayDutyGroupDao;
import com.rs.module.pam.dao.duty.day.DayDutyShiftDao;
import com.rs.module.pam.entity.duty.day.DayDutyDO;
import com.rs.module.pam.entity.duty.day.DayDutyGroupDO;
import com.rs.module.pam.entity.duty.day.DayDutyRecordsDO;
import com.rs.module.pam.entity.duty.day.DayDutyShiftDO;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 监所事务管理-监室值日 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DayDutyServiceImpl extends BaseServiceImpl<DayDutyDao, DayDutyDO> implements DayDutyService {

    @Resource
    private DayDutyDao dayDutyDao;
    @Resource
    private DayDutyRecordsService dayDutyRecordsService;
    @Resource
    private DayDutyShiftDao dayDutyShiftDao;
    @Resource
    private DayDutyGroupDao dayDutyGroupDao;
    @Resource
    private PrisonerService prisonerService;
    @Resource
    private BaseDeviceInscreenService inscreenService;
    @Resource
    private SocketPushFeign socketPushFeign;
    @Resource
    private DutyPrisonerDao dutyPrisonerDao;
    @Resource
    private BspApi bspApi;

    @Override
    public void create(DayDutySaveVO createReqVO) {
        // 插入
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String orgCode = createReqVO.getOrgCode();
        String roomId = createReqVO.getRoomId();
        Date now = new Date();
        List<DayDutyListRespVO> dutyList = createReqVO.getDutyList();
        List<DayDutyInfoRespVO> allDutyInfoList = dutyList.stream().filter(f -> f.getDutyInfo() != null)
                .map(DayDutyListRespVO::getDutyInfo)
                .flatMap(List::stream).collect(Collectors.toList());
        // 获取班次信息
        List<String> shiftIdList = allDutyInfoList.stream().map(DayDutyInfoRespVO::getShiftId).distinct().collect(Collectors.toList());
        List<DayDutyShiftDO> shiftList = dayDutyShiftDao.selectList(orgCode, roomId, shiftIdList);
        Map<String, DayDutyShiftDO> shiftMap = shiftList.stream()
                .collect(Collectors.toMap(
                        DayDutyShiftDO::getId,
                        obj -> obj,
                        (existing, replacement) -> replacement
                ));

        // 获取原值日信息
//        List<String> recordsIdList = allDutyInfoList.stream().map(DayDutyInfoRespVO::getId).distinct().collect(Collectors.toList());
//        List<DayDutyRecordsDO> recordsList = dayDutyRecordsService.listByIds(recordsIdList);
//        Map<String, DayDutyRecordsDO> recordsMap = recordsList.stream()
//                .collect(Collectors.toMap(
//                        DayDutyRecordsDO::getId,
//                        obj -> obj,
//                        (existing, replacement) -> replacement
//                ));

        // 获取值日组信息
        List<String> groupIdList = allDutyInfoList.stream().filter(duty -> duty.getIsGroup() != null && duty.getIsGroup())
                .map(DayDutyInfoRespVO::getGroupId).distinct().collect(Collectors.toList());
        Map<String, DayDutyGroupDO> groupMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(groupIdList)) {
            List<DayDutyGroupDO> groupList = dayDutyGroupDao.selectList(orgCode, roomId, groupIdList);
            groupMap = groupList.stream()
                    .collect(Collectors.toMap(
                            DayDutyGroupDO::getId,
                            obj -> obj,
                            (existing, replacement) -> replacement
                    ));
        }

        // 校验值日组与班次是否存在。未来的（未开始的）班次，如果是小组，则小组必须存在。填补出currentShift参数。
        for (DayDutyListRespVO duty : dutyList) {
            Date dutyDate = duty.getDutyDate();
            for (DayDutyInfoRespVO dutyInfo : duty.getDutyInfo()) {
                final String groupId = dutyInfo.getId();
                DayDutyShiftDO shiftDO = shiftMap.get(dutyInfo.getShiftId());
                if (ObjectUtil.isEmpty(shiftDO)) {
                    throw new RuntimeException(String.format("班次[%s]不存在", dutyInfo.getShiftId()));
                }
                // 校验班次有效时间，避免胡乱指定班次
                if ((shiftDO.getEffectiveStartDate().compareTo(dutyDate) <= 0 || DateUtil.isSameDay(shiftDO.getEffectiveStartDate(), dutyDate))
                        && (shiftDO.getEffectiveEndDate() == null || shiftDO.getEffectiveEndDate().compareTo(dutyDate) > 0)) {
                } else {
                    throw new RuntimeException(String.format("无效班次[%s]", shiftDO.getShiftName()));
                }
                // 班次是否还未开始
                boolean shiftFuture = dutyDate.compareTo(DateUtil.beginOfDay(now)) >= 0;
                dutyInfo.setShiftType(shiftFuture ? "2" : "0");
                if (StringUtils.isBlank(groupId)) {
                    continue;
                }
                // 校验未开始的班次，小组是否存在
                if (shiftFuture && ObjectUtil.isEmpty(groupMap.get(groupId))) {
                    String groupName = StringUtils.isNotBlank(dutyInfo.getGroupName()) ? dutyInfo.getGroupName() : groupId;
                    throw new RuntimeException(String.format("值日小组[%s]不存在", groupName));
                }
            }
        }

        // 校验人员是否在监室
        List<PrisonerInDO> prisonerInList = prisonerService.getPrisonerInList(orgCode, roomId);
        List<String> prisonerJgrybmList = prisonerInList.stream().map(PrisonerInDO::getJgrybm).collect(Collectors.toList());
        for (DayDutyInfoRespVO dutyInfo : allDutyInfoList) {
            if ("0".equals(dutyInfo.getShiftType()) || "1".equals(dutyInfo.getShiftType())) {
                // 已经过的班次不校验人员是否还存在
                continue;
            }
            List<String> jgrybmList = dutyInfo.getPrisonerList().stream().map(DutyPrisonerVO::getJgrybm).collect(Collectors.toList());
            boolean isGroup = false;
            final String groupId = dutyInfo.getGroupId();
            if (StringUtils.isNotBlank(groupId)) {
                isGroup = true;
                // 小组。目的校验小组的中的人是否还在监室。如果不再测移除小组
                DayDutyGroupDO groupDO = groupMap.get(groupId);
                if (ObjectUtil.isNotEmpty(groupDO)) {
                    jgrybmList = Lists.newArrayList(groupDO.getJgrybm1(), groupDO.getJgrybm2());
                } else {
                    // 已过的班次，可能会存在当前小组不存在的情况
                }
            }
            for (String jgrybm : jgrybmList) {
                if (StringUtils.isNotBlank(jgrybm) && !prisonerJgrybmList.contains(jgrybm)) {
                    if (isGroup) {
                        // 小组中存在某成员，已经离开该监室。移除小组
                        new Thread(() -> {
                            // 异步使事务失效
                            dayDutyGroupDao.deleteById(groupId);
                        }).start();
                    }
                    PrisonerVwRespVO prisoner = prisonerService.getPrisonerByJgrybm(jgrybm);
                    if (ObjectUtil.isNotEmpty(prisoner)) {
                        throw new RuntimeException(String.format("人员[%s]不存在", prisoner.getXm()));
                    } else {
                        throw new RuntimeException(String.format("人员[%s]已离开该监室", prisoner.getXm()));
                    }
                }
            }
        }

        for (DayDutyListRespVO dutyDto : dutyList) {
            List<DayDutyInfoRespVO> dutyInfoList = Optional.ofNullable(dutyDto.getDutyInfo()).orElse(Collections.EMPTY_LIST);
            // 排班日期
            Date dutyDate = dutyDto.getDutyDate();
            if (dutyInfoList.isEmpty()) {
                dayDutyDao.delete(new LambdaQueryWrapper<DayDutyDO>()
                        .eq(DayDutyDO::getOrgCode, orgCode)
                        .eq(DayDutyDO::getRoomId, roomId)
                        .eq(DayDutyDO::getDutyDate, dutyDate)
                );
                dayDutyRecordsService.remove(new LambdaQueryWrapper<DayDutyRecordsDO>()
                        .eq(DayDutyRecordsDO::getOrgCode, orgCode)
                        .eq(DayDutyRecordsDO::getRoomId, roomId)
                        .eq(DayDutyRecordsDO::getDutyDate, dutyDate)
                );
                continue;
            }
            boolean flag = false;
            DayDutyDO entity = dayDutyDao.selectOne(new LambdaQueryWrapper<DayDutyDO>()
                    .eq(DayDutyDO::getOrgCode, orgCode)
                    .eq(DayDutyDO::getRoomId, roomId)
                    .eq(DayDutyDO::getDutyDate, dutyDate)
                    .select(DayDutyDO::getId)
            );
            if (entity == null) {
                flag = true;
                entity = new DayDutyDO();
                entity.setId(StringUtil.getGuid32());
            }
            entity.setRoomId(roomId);
            entity.setDutyDate(dutyDate);
            entity.setAssignerUserSfzh(sessionUser.getIdCard());
            entity.setAssignerUserName(sessionUser.getName());
            entity.setAssignedTime(now);
            // 该监室当前的排班情况
            List<DayDutyRecordsDO> dbRecordsEntities = dayDutyRecordsService.list(new LambdaQueryWrapper<DayDutyRecordsDO>()
                    .eq(DayDutyRecordsDO::getOrgCode, orgCode)
                    .eq(DayDutyRecordsDO::getRoomId, roomId)
                    .eq(DayDutyRecordsDO::getDutyDate, dutyDate)
                    .select(DayDutyRecordsDO::getId, DayDutyRecordsDO::getShiftId)
            );
            List<String> recordsIds = new ArrayList<>();
            for (DayDutyInfoRespVO dutyInfoDto : dutyInfoList) {
                DayDutyRecordsDO recordsDO = new DayDutyRecordsDO();
                recordsDO.setRoomId(roomId);
                recordsDO.setDutyId(entity.getId());
                recordsDO.setDutyDate(dutyDate);
                recordsDO.setShiftId(dutyInfoDto.getShiftId());
                // 填充班次信息
                DayDutyShiftDO shiftDO = shiftMap.get(dutyInfoDto.getShiftId());
                if (ObjectUtil.isNotEmpty(shiftDO)) {
                    recordsDO.setShiftName(shiftDO.getShiftName());
                }
                // 填充值日人员信息
                recordsDO.setGroupId("");
                recordsDO.setGroupName("");
                List<DutyPrisonerVO> prisonerList = Optional.ofNullable(dutyInfoDto.getPrisonerList()).orElse(Collections.EMPTY_LIST);
                if (prisonerList != null && prisonerList.size() > 0) {
                    recordsDO.setJgrybm1(prisonerList.get(0).getJgrybm());
                    recordsDO.setJgrybm2("");
                    if (prisonerList.size() > 1) {
                        recordsDO.setJgrybm2(prisonerList.get(1).getJgrybm());
                    }
                }
                // 填充值日组信息
                if (StringUtils.isNotBlank(dutyInfoDto.getGroupId())) {
                    recordsDO.setGroupId(dutyInfoDto.getGroupId());
                    recordsDO.setGroupName(dutyInfoDto.getGroupName());
                    DayDutyGroupDO groupDO = groupMap.get(dutyInfoDto.getGroupId());
                    if (ObjectUtil.isNotEmpty(groupDO)) {
                        recordsDO.setGroupName(groupDO.getGroupName());
                        recordsDO.setJgrybm1(groupDO.getJgrybm1());
                        recordsDO.setJgrybm2(groupDO.getJgrybm2());
                    }
                }
                if (StringUtils.isBlank(recordsDO.getGroupId()) && StringUtils.isBlank(recordsDO.getJgrybm1()) &&
                        StringUtils.isBlank(recordsDO.getJgrybm2())) {
                    // 该值日无 组，无人员
                    continue;
                }
                recordsDO.setAssignerUserSfzh(sessionUser.getIdCard());
                recordsDO.setAssignerUserName(sessionUser.getName());
                recordsDO.setAssignedTime(now);
                Optional<DayDutyRecordsDO> opt = dbRecordsEntities.stream().filter(f -> f.getShiftId() != null &&
                        f.getShiftId().equals(dutyInfoDto.getShiftId())).findFirst();
                if (opt.isPresent()) {
                    recordsDO.setId(opt.get().getId());
                    dayDutyRecordsService.updateById(recordsDO);
                } else {
                    recordsDO.setId(StringUtil.getGuid32());
                    dayDutyRecordsService.save(recordsDO);
                }
                recordsIds.add(recordsDO.getId());
            }
            if (recordsIds.isEmpty()) {
                // 如果某天没有一个班次，则吧改天的排班从 prison_room_duty_v2 表也删除
                dayDutyDao.delete(new LambdaQueryWrapper<DayDutyDO>()
                        .eq(DayDutyDO::getRoomId, roomId)
                        .eq(DayDutyDO::getDutyDate, dutyDate)
                );
            } else {
                if (flag) {
                    dayDutyDao.insert(entity);
                } else {
                    dayDutyDao.updateById(entity);
                }
            }
            // 删除旧的和没有设置值日人员的值日数据
            List<String> deleteIds = dbRecordsEntities.stream().filter(f ->
                    !recordsIds.contains(f.getId())).map(DayDutyRecordsDO::getId).collect(Collectors.toList());
            if (!deleteIds.isEmpty()) {
                dayDutyRecordsService.removeByIds(deleteIds);
            }
        }
        // 通知内屏刷新提醒列表
        dutyChangeNotifyCnp(orgCode);
    }

//    private List<DayDutyShiftDO> createDefaultShift(String orgCode, String roomId) {
//        Date now = new Date();
//        DayDutyShiftDO morning = new DayDutyShiftDO();
//        OrgRespDTO org = bspApi.getOrgByCode(orgCode);
//        morning.setCityCode(org.getCityId());
//        morning.setCityName(org.getCityName());
//        morning.setRegCode(org.getRegionId());
//        morning.setRegName(org.getRegionName());
//        morning.setOrgCode(orgCode);
//        morning.setOrgName(org.getName());
//        morning.setRoomId(roomId);
//        morning.setEffectiveStartDate(now);
//        DayDutyShiftDO afternoon = (DayDutyShiftDO) SerializationUtils.clone((Serializable) morning);
//        DayDutyShiftDO night = (DayDutyShiftDO)SerializationUtils.clone((Serializable) morning);
//        morning.setShiftName("早班");
//        morning.setSort(1);
//        afternoon.setShiftName("午班");
//        afternoon.setSort(2);
//        night.setShiftName("晚班");
//        night.setSort(3);
//
//        List<DayDutyShiftDO> list = new ArrayList<>();
//        list.add(morning);
//        list.add(afternoon);
//        list.add(night);
//        dayDutyShiftDao.insertBatch(list);
//        return list;
//    }

    public void dutyChangeNotifyCnp(String orgCode) {
        // 查询该监所下所有仓内屏序列号
        List<BaseDeviceInscreenDO> inscreenList = inscreenService.getByOrgCode(orgCode);
        if (inscreenList.isEmpty()) {
            return;
        }

        List<String> numberList = inscreenList.stream().map(inscreen -> inscreen.getSerialNumber()).distinct().collect(Collectors.toList());
        PushMessageForm form = new PushMessageForm();
        form.setSerialNumbers(numberList);
        form.setAction(SocketActionConstants.web_prisonRoomDutyChange);
        form.setTarget(SocketActionConstants.PushMessageTargetEnum.web.name());
        form.setTerminal(SocketActionConstants.PushMessageTerminalEnum.CNP.name());
        new Thread(() -> {
            socketPushFeign.pushMessageToSerialNumber(form);
        }).start();
    }


    @Override
    public void update(DayDutySaveReqVO updateReqVO) {
        // 校验存在
        validateExists(updateReqVO.getId());
        // 更新
        DayDutyDO updateObj = BeanUtils.toBean(updateReqVO, DayDutyDO.class);
        dayDutyDao.updateById(updateObj);
    }

    @Override
    public void delete(String id) {
        // 校验存在
        validateExists(id);
        // 删除
        dayDutyDao.deleteById(id);
    }

    private void validateExists(String id) {
        if (dayDutyDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-监室值日数据不存在");
        }
    }

    @Override
    public DayDutyDO get(String id) {
        return dayDutyDao.selectById(id);
    }

    @Override
    public List<DayDutyVO> dutyRecords(String orgCode, String roomId, Date startDate, Date endDate) {
        Date now = new Date();
        // 获取值日记录
        List<DayDutyRecordsDO> recordList = dayDutyRecordsService.selectList(orgCode, roomId, startDate, endDate);
//        if (CollectionUtil.isEmpty(recordList)) {
//            return new ArrayList<>();
//        }
        // 获取值日组信息
        List<String> recordsGroupIdList = recordList.stream().map(record -> record.getGroupId())
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<DayDutyGroupDO> recordsGroupList = new ArrayList<>();
        Map<String, DayDutyGroupDO> recordsGroupMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(recordsGroupIdList)) {
            recordsGroupList = dayDutyGroupDao.selectList(orgCode, roomId, recordsGroupIdList);
            recordsGroupMap = recordsGroupList.stream()
                    .collect(Collectors.toMap(
                            DayDutyGroupDO::getId,
                            obj -> obj,
                            (existing, replacement) -> replacement));
        }
        // 获取排班中的人员信息
        List<String> jgrybmList = recordList.stream().flatMap(obj -> Stream.of(obj.getJgrybm1(), obj.getJgrybm2()))
                .collect(Collectors.toList());
        jgrybmList.addAll(recordsGroupList.stream().flatMap(obj -> Stream.of(obj.getJgrybm1(), obj.getJgrybm2())).collect(Collectors.toList()));
        jgrybmList = jgrybmList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        // 查询排班信息中的在所人员信息
        List<DutyPrisonerVO> prisonerInList = new ArrayList<>();
        if  (CollectionUtil.isNotEmpty(jgrybmList)) {
            List<DutyPrisonerVO> personerList = dutyPrisonerDao.getPrisonerInByJgrybms(jgrybmList);
            prisonerInList = BeanUtils.toBean(personerList, DutyPrisonerVO.class);
        }

        List<String> prisonerInIdList = prisonerInList.stream().map(prisoner -> prisoner.getJgrybm()).collect(Collectors.toList());
        Map<String, DutyPrisonerVO> prisonerInMap = prisonerInList.stream()
                .collect(Collectors.toMap(
                        DutyPrisonerVO::getJgrybm,
                        obj -> obj,
                        (existing, replacement) -> replacement));

        // 获取班次信息，并以生效日期与失效日期分组，判断班次是否变更过
        List<DayDutyShiftDO> shiftList = dayDutyShiftDao.selectShiftByEffectiveDate(orgCode, roomId, startDate, endDate);
        if(CollectionUtil.isEmpty(shiftList)) {
            shiftList = dayDutyShiftDao.createDefaultShift(orgCode, roomId);
        }
        // 根据生效日期对班次进行分组
        Map<Date, List<DayDutyShiftDO>> grouped = shiftList.stream()
                .collect(Collectors.groupingBy(
                        DayDutyShiftDO::getEffectiveStartDate,
                        TreeMap::new,
                        Collectors.toList()
                ));
        // 根据序号对班次进行排序
        List<List<DayDutyShiftDO>> collect = grouped.values().stream()
                .map(group -> group.stream()
                        .sorted(Comparator.comparing(DayDutyShiftDO::getSort))
                        .collect(Collectors.toList()))
                .collect(Collectors.toList());

        List<DayDutyVO> result = new ArrayList<>();
        for(List<DayDutyShiftDO> shiftRespVOS : collect) {
            DayDutyVO dutyVO = new DayDutyVO();
            dutyVO.setRoomId(roomId);
            if (CollectionUtil.isEmpty(shiftRespVOS)) {
                continue;
            }
            // 返回有效时间段内所有的天。需要注意不能返回查询时间外的天
            Date dutyDate = DateUtil.beginOfDay(shiftRespVOS.get(0).getEffectiveStartDate());
            if (startDate.compareTo(shiftRespVOS.get(0).getEffectiveStartDate()) > 0) {
                dutyDate = startDate;
            }
            Date dutyDateEnd = endDate;
            if (shiftRespVOS.get(0).getEffectiveEndDate() != null && endDate.compareTo(shiftRespVOS.get(0).getEffectiveEndDate()) >= 0) {
                dutyDateEnd = shiftRespVOS.get(0).getEffectiveEndDate();
                //  表的effective_end_date 不包含该天，需要减1
                dutyDateEnd = new Date(dutyDateEnd.getYear(), dutyDateEnd.getMonth(), dutyDateEnd.getDate() - 1);
            }
            // 记录离开监室的人员。后续删除
            List<String> roomOutJgrybmList = new ArrayList<>();
            // 遍历每天
            List<DayDutyListRespVO> list = new ArrayList<>();
            while (dutyDate.compareTo(dutyDateEnd) <= 0) {
                final Date finalDutyDate = dutyDate;
                DayDutyListRespVO dutyListRespVO = new DayDutyListRespVO();
                dutyListRespVO.setDutyDate(dutyDate);
                List<DayDutyRecordsDO> recordsInfoList = recordList.stream().filter(f -> f.getDutyDate() != null &&
                        finalDutyDate.compareTo(f.getDutyDate()) == 0).collect(Collectors.toList());

                List<DayDutyRecordsDO> completionRecordsList = new ArrayList<>();
                // 判断值日信息中的班次是否存在
                for (DayDutyShiftDO shift : shiftRespVOS) {
                    Optional<DayDutyRecordsDO> opt = recordsInfoList.stream().filter(f ->
                            f.getShiftId() != null && f.getShiftId().equals(shift.getId())).findFirst();
                    DayDutyRecordsDO recordsDO;
                    if (opt.isPresent()) {
                        completionRecordsList.add(recordsDO = opt.get());
                    } else {
                        recordsDO = new DayDutyRecordsDO();
                        recordsDO.setShiftId(shift.getId());
                        completionRecordsList.add(recordsDO);
                    }
                }

                List<DayDutyInfoRespVO> dutyInfoRespVOList = new ArrayList<>();
                for (DayDutyRecordsDO records : completionRecordsList) {
                    // 判断是否为未来班次，未开始的班次
                    boolean future = dutyDate.compareTo(DateUtil.beginOfDay(now)) >= 0;
                    DayDutyInfoRespVO dutyInfoRespVO = new DayDutyInfoRespVO();

                    List<String> dutyJgrybmList = new ArrayList<>();
                    List<DutyPrisonerVO> prisonerListRes = new ArrayList<>();
                    // 带出人员列表信息。优先从分组中拿，拿不到再从值日记录中拿
                    DayDutyGroupDO groupDO = recordsGroupMap.get(records.getGroupId());
                    if (ObjectUtil.isNotEmpty(groupDO)) {
                        dutyJgrybmList = Lists.newArrayList(groupDO.getJgrybm1(), groupDO.getJgrybm2());
                        records.setJgrybm1(groupDO.getJgrybm1());
                        records.setJgrybm2(groupDO.getJgrybm2());
                    } else {
                        dutyJgrybmList = Lists.newArrayList(records.getJgrybm1(), records.getJgrybm2());
                    }

                    for (String jgrybm : dutyJgrybmList) {
                        if (StringUtils.isNotBlank(jgrybm) && prisonerInMap.containsKey(jgrybm)) {
                            DutyPrisonerVO dutyPrisonerVO = prisonerInMap.get(jgrybm);
                            prisonerListRes.add(dutyPrisonerVO);
                        }
                    }

                    if (future) {
                        // 未来的值日记录中 需要排除不在该监室的人员
                        Iterator<DutyPrisonerVO> iterator = prisonerListRes.iterator();
                        while (iterator.hasNext()) {
                            DutyPrisonerVO p = iterator.next();
                            if (!prisonerInIdList.contains(p.getJgrybm())) {
                                if (!roomOutJgrybmList.contains(p.getJgrybm())) {
                                    roomOutJgrybmList.add(p.getJgrybm());
                                }
                                iterator.remove();
                            }
                        }
                    }
                    dutyJgrybmList = prisonerListRes.stream().map(DutyPrisonerVO::getJgrybm).collect(Collectors.toList());

                    dutyInfoRespVO.setShiftId(records.getShiftId());
                    dutyInfoRespVO.setPrisonerList(BeanUtils.toBean(prisonerListRes, DutyPrisonerVO.class));
                    // 判断当前时间是值日前、值日中还是值日后
                    dutyInfoRespVO.setShiftType("0");
                    if (dutyDate != null && DateUtil.beginOfDay(now).compareTo(dutyDate) <= 0) {
                        DateTime nowEndDate = DateUtil.endOfDay(now);
                        if (nowEndDate.compareTo(dutyDate) > 0) {
                            dutyInfoRespVO.setShiftType("1");
                        } else if (nowEndDate.compareTo(dutyDate) < 0) {
                            dutyInfoRespVO.setShiftType("2");
                        }
                    }
                    // 判断是否为值日组，并填充值日组信息
                    if (StringUtils.isNotBlank(records.getGroupId())) {
                        long inRoomCount = dutyJgrybmList.size();
                        if (inRoomCount < 2 || groupDO == null) {
                            // 如果值日时间还未到，但是组没有了。这种情况流程上不存在。因为删除组会级联删除未来的排班
                            dutyInfoRespVO.setIsGroup(false);
                        } else {
                            dutyInfoRespVO.setIsGroup(true);
                            dutyInfoRespVO.setGroupId(records.getGroupId());
                            dutyInfoRespVO.setGroupName(groupDO.getGroupName());
                            dutyInfoRespVO.getPrisonerList().forEach(p -> p.setIsGroup(true));
                        }
                    } else {
                        dutyInfoRespVO.setIsGroup(false);
                    }
                    dutyInfoRespVOList.add(dutyInfoRespVO);
                }
                dutyListRespVO.setDutyInfo(dutyInfoRespVOList);
                list.add(dutyListRespVO);
                // 加一天
                dutyDate = new Date(dutyDate.getYear(), dutyDate.getMonth(), dutyDate.getDate() + 1);
            }
            dutyVO.setDutyList(list);
            dutyVO.setShiftList(BeanUtils.toBean(shiftRespVOS, DayShiftRespVO.class));
            result.add(dutyVO);

            // 对离开监室的人员进行清理
            if (!roomOutJgrybmList.isEmpty()) {
                roomOutJgrybmList = roomOutJgrybmList.stream().distinct().collect(Collectors.toList());
                List<JSONObject> dtoList = new ArrayList<>();
                for (String jgrybm : roomOutJgrybmList) {
                    JSONObject dto0 = new JSONObject();
                    dto0.put("roomId", roomId);
                    dto0.put("jgrybm", jgrybm);
//                    dto0.setAutoShift(false);
                    dtoList.add(dto0);
                }
                roomOutJgrybmList = null;
                new Thread(() -> {
//                    this.roomOut(dtoList);
                }).start();
            }
        }

        return result;
    }

//    public void roomOut(JSONObject fromArray) {
//        // 冗余校验，人员是否在所
//        List<String> rybhList = Arrays.asList(fromArray).stream().map(PrisonRoomDutyV2RoomOutDto::getRybh).distinct().collect(Collectors.toList());
//        List<PrisonRoomDutyV2PrisonerListVO> prisonerList = prisonRoomDutyV2Dao.selectPrisonerByIds(rybhList);
//        List<PrisonRoomDutyV2RoomOutDto> fromList = new ArrayList<>();
//        for (PrisonRoomDutyV2RoomOutDto form : fromArray) {
//            final String rybh = form.getRybh();
//            if (StringUtils.isBlank(rybh)) {
//                continue;
//            }
//            Optional<PrisonRoomDutyV2PrisonerListVO> optional = prisonerList.stream().filter(f -> rybh.equals(f.getId())).findFirst();
//            if (!optional.isPresent()) {
//                // 人员不存在
//                fromList.add(form);
//                continue;
//            }
//            PrisonRoomDutyV2PrisonerListVO prisonerInfo = optional.get();
//            boolean prisonerIn = "10".equals(prisonerInfo.getRyzt());
//            form.setCurrentRoomId(prisonerInfo.getJsh());
//            form.setPrisonerIn(prisonerIn);
//            if (!prisonerIn || ObjectUtils.notEqual(form.getRoomId(), prisonerInfo.getJsh())) {
//                // 人员不再所 或者监室不一致
//                fromList.add(form);
//                continue;
//            }
//        }
//        // 清理值日记录
//        for (PrisonRoomDutyV2RoomOutDto form : fromList) {
//            deletePrisonerRecordAndGroup(form);
//        }
//    }

    /**
     * 监室人员列表，分组放在人员前面
     *
     * @param roomId
     * @return
     */
    @Override
    public List<DutyPrisonerVO> prisonerList(String orgCode, String roomId) {
        // 查询出监室所有人员
        List<DutyPrisonerVO> personerList = dutyPrisonerDao.getPrisonerInByRoomId(orgCode, roomId);
        List<DutyPrisonerVO> prisonerListVOS = BeanUtils.toBean(personerList, DutyPrisonerVO.class);
        // 查询本周人员值日数量
        List<Map<String, Object>> prisonerWeekDutyCountList = Collections.EMPTY_LIST;
        if (!prisonerListVOS.isEmpty()) {
            Date now = new Date();
            Date weekMonday = DateUtil.beginOfWeek(DateUtil.offsetDay(now, -1)).toJdkDate();
            Date weekSunday = DateUtil.endOfWeek(DateUtil.offsetDay(now, -1)).toJdkDate();
            prisonerWeekDutyCountList = dayDutyDao.selectPrisonerWeekDutyCount(orgCode, roomId, weekMonday, new Date(weekSunday.getTime() + 86400000 - 1));
        }
        for (DutyPrisonerVO vo : prisonerListVOS) {
//            vo.setIsGroup(false);
            vo.setWeekDutyCount(0);
            vo.setEntryDays(vo.getDays().intValue());
            for (Map<String, Object> countVO : prisonerWeekDutyCountList) {
                if (countVO.get("value") != null && countVO.get("value").equals(vo.getId())) {
                    vo.setWeekDutyCount(Integer.valueOf(String.valueOf(countVO.get("count"))));
                    break;
                }
            }
        }
        List<DayDutyGroupDO> dutyGroupList = dayDutyGroupDao.selectList(new LambdaQueryWrapper<DayDutyGroupDO>()
                .eq(DayDutyGroupDO::getOrgCode, orgCode)
                .eq(DayDutyGroupDO::getRoomId, roomId)
                .select(DayDutyGroupDO::getJgrybm1, DayDutyGroupDO::getJgrybm2, DayDutyGroupDO::getId, DayDutyGroupDO::getGroupName)
                .orderByDesc(DayDutyGroupDO::getAddTime)
        );
        // 这里面的人员是已经分组的，后需要从 prisonerListVOS 中剔除掉
        final Set<String> jgrybmSet = new HashSet<>();
        List<DutyPrisonerVO> records = new ArrayList<>();
        for (DayDutyGroupDO groupDO : dutyGroupList) {
            List<DutyPrisonerVO> groupPrisonerList = new ArrayList<>();
            prisonerListVOS.stream().filter(f -> groupDO.getJgrybm1() != null && groupDO.getJgrybm1().equals(f.getJgrybm())).findFirst().ifPresent(f -> {
                groupPrisonerList.add(f);
            });
            prisonerListVOS.stream().filter(f -> groupDO.getJgrybm2() != null && groupDO.getJgrybm2().equals(f.getJgrybm())).findFirst().ifPresent(f -> {
                groupPrisonerList.add(f);
            });
            if (groupPrisonerList.size() < 2) {
                // 小组人员不足2人。将小组移除
                System.out.println("小组人员不足2人。将小组移除：" + groupDO.getId() + groupDO.getGroupName());
                dayDutyGroupDao.deleteById(groupDO.getId());
                continue;
            }
            prisonerListVOS.removeAll(groupPrisonerList);
            DutyPrisonerVO pvo = new DutyPrisonerVO();
            pvo.setId(groupDO.getId());
            pvo.setGroupId(groupDO.getId());
            pvo.setName(groupDO.getGroupName());
            pvo.setIsGroup(true);
            jgrybmSet.add(groupDO.getJgrybm1());
            jgrybmSet.add(groupDO.getJgrybm2());
            groupPrisonerList.forEach(p -> p.setIsGroup(true));
            pvo.setPrisonerList(groupPrisonerList);
            records.add(pvo);
        }
        Iterator<DutyPrisonerVO> iterator = prisonerListVOS.iterator();
        while (iterator.hasNext()) {
            DutyPrisonerVO vo = iterator.next();
            if (jgrybmSet.contains(vo.getId())) {
                continue;
            }
            records.add(vo);
        }
        return records;
    }

    @Override
    public List<AppDutyRespVO> appDutyRecords(String orgCode, String roomId, Date startDate, Date endDate) {
        List<AppDutyRespVO> returnList = new ArrayList<>();

        List<DayDutyVO> records = this.dutyRecords(orgCode, roomId, startDate, endDate);
        // 所有的班次
        List<DayShiftRespVO> shiftList = records.stream().flatMap(obj -> Stream.of(obj.getShiftList()))
                .flatMap(List::stream)
                .collect(Collectors.toList());
        // 所有的值班信息
        List<DayDutyListRespVO> dutyList = records.stream().flatMap(obj -> Stream.of(obj.getDutyList()))
                .flatMap(List::stream)
                .collect(Collectors.toList());
        dutyList = dutyList.stream().sorted(Comparator.comparing(DayDutyListRespVO::getDutyDate)).collect(Collectors.toList());

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        for (DayDutyListRespVO dutyDto : dutyList) {
            AppDutyRespVO vo = new AppDutyRespVO();
            returnList.add(vo);

            vo.setDutyDate(dutyDto.getDutyDate());
            vo.setDutyName(sdf.format(dutyDto.getDutyDate()));
            vo.setRoomId(roomId);
            List<AppDutyShiftRespVO> dutyShiftList = new ArrayList<>();
            vo.setDutyShiftList(dutyShiftList);
            vo.setWeekday(DateUtils.getWeekOfDay(dutyDto.getDutyDate()));
            // 每个班次信息
            for (DayDutyInfoRespVO dtoDutyInfo : dutyDto.getDutyInfo()) {
                Optional<DayShiftRespVO> optional = shiftList.stream().filter(ff -> ObjectUtils.equals(dtoDutyInfo.getShiftId(), ff.getId())).findFirst();
                if (!optional.isPresent()) {
                    // 一定不会出现
                    continue;
                }
                DayShiftRespVO shiftManageDto = optional.get();
                AppDutyShiftRespVO shiftVO = new AppDutyShiftRespVO();
                dutyShiftList.add(shiftVO);
                shiftVO.setId(dtoDutyInfo.getShiftId());
                shiftVO.setShiftName(shiftManageDto.getShiftName());
                List<DutyPrisonerVO> personList = new ArrayList<>();
                shiftVO.setPrisonerList(personList);
                // 每个班次的值班人员
                for (DutyPrisonerVO dutyPrisonerInfo : dtoDutyInfo.getPrisonerList()) {
                    DutyPrisonerVO prisonerVO = new DutyPrisonerVO();
                    prisonerVO.setJgrybm(dutyPrisonerInfo.getJgrybm());
                    prisonerVO.setName(dutyPrisonerInfo.getName());
                    personList.add(prisonerVO);
                }
            }
        }
        // 填充数据，可能会出现 查询时间内没有班次时返回的数据会缺少一些天，将缺少天的数据补充
        if (endDate != null && startDate != null && endDate.compareTo(startDate) >= 0) {
            Date startTime = startDate;
            while (startTime.compareTo(endDate) <= 0) {
                final String dutyName = sdf.format(startTime);
                boolean match = returnList.stream().anyMatch(a -> dutyName.equals(a.getDutyName()));
                if (match) {
                    startTime = new Date(startTime.getTime() + 86400000);
                    continue;
                }
                AppDutyRespVO vo = new AppDutyRespVO();
                returnList.add(vo);
                vo.setDutyDate(startTime);
                vo.setDutyName(dutyName);
                vo.setRoomId(roomId);
                List<AppDutyShiftRespVO> dutyShiftList = new ArrayList<>();
                vo.setDutyShiftList(dutyShiftList);
                vo.setWeekday(DateUtils.getWeekOfDay(startTime));
                startTime = new Date(startTime.getTime() + 86400000);
            }
            returnList.sort(Comparator.comparing(AppDutyRespVO::getDutyDate));
        }
        return returnList;
    }

    public List<AppDutyShiftRespVO> dutyRecordsByDate(String orgCode, String roomId, Date date) {
        List<AppDutyShiftRespVO> returnList = new ArrayList<>();

        List<DayDutyShiftDO> shiftList = dayDutyShiftDao.selectShiftByEffectiveDate(orgCode, roomId, date, date);
        List<DayDutyRecordsDO> recordList = dayDutyRecordsService.selectList(orgCode, roomId, date, date);

        List<String> jgrybmList = recordList.stream().flatMap(obj -> Stream.of(obj.getJgrybm1(), obj.getJgrybm2()))
                .collect(Collectors.toList());
        jgrybmList = jgrybmList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<DutyPrisonerVO> personerList = new ArrayList<>();
        if  (CollectionUtil.isNotEmpty(jgrybmList)) {
            personerList = dutyPrisonerDao.getPrisonerInByJgrybms(jgrybmList);
        }
        Map<String, DutyPrisonerVO> prisonerInMap = personerList.stream()
                .collect(Collectors.toMap(
                        DutyPrisonerVO::getJgrybm,
                        obj -> obj,
                        (existing, replacement) -> replacement));
        for (DayDutyShiftDO shiftDO : shiftList) {
            AppDutyShiftRespVO appDutyShiftRespVO = new AppDutyShiftRespVO();
            appDutyShiftRespVO.setId(shiftDO.getId());
            appDutyShiftRespVO.setShiftName(shiftDO.getShiftName());
            appDutyShiftRespVO.setSort(shiftDO.getSort());
            List<DutyPrisonerVO> prisonerList = new ArrayList<>();
            for (DayDutyRecordsDO recordsDO : recordList) {
                if (ObjectUtils.equals(shiftDO.getId(), recordsDO.getShiftId())) {
                    if (StringUtils.isNotBlank(recordsDO.getJgrybm1()))
                        prisonerList.add(prisonerInMap.get(recordsDO.getJgrybm1()));
                    if (StringUtils.isNotBlank(recordsDO.getJgrybm2()))
                        prisonerList.add(prisonerInMap.get(recordsDO.getJgrybm2()));
                    break;
                }
            }
            appDutyShiftRespVO.setPrisonerList(prisonerList);
            returnList.add(appDutyShiftRespVO);
        }
        returnList.sort(Comparator.comparing(AppDutyShiftRespVO::getSort));
        return returnList;
    }

}
