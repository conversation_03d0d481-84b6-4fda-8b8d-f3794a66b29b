package com.rs.module.pam.controller.admin.integral;

import com.bsp.common.util.StringUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.dto.AppRespDTO;
import com.rs.adapter.bsp.api.dto.OpsDatasourceDTO;
import com.rs.adapter.bsp.api.dto.OpsResFieldDTO;
import com.rs.adapter.bsp.api.dto.OpsResourceDTO;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.integral.vo.ModelConfigRespVO;
import com.rs.module.pam.controller.admin.integral.vo.ModelConfigSaveReqVO;
import com.rs.module.pam.entity.integral.ModelConfigDO;
import com.rs.module.pam.service.integral.ModelConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "积分模型配置")
@RestController
@RequestMapping("/pam/integral/modelConfig")
@Validated
public class ModelConfigController {

    @Resource
    private ModelConfigService modelConfigService;
    @Resource
    private BspApi bspApi;

    @PostMapping("/create")
    @ApiOperation(value = "保存积分模型配置")
    @LogRecordAnnotation(bizModule = "pam:integral:modelConfig:create", operateType = LogOperateType.CREATE, title = "保存积分模型配置",
            success = "保存积分模型配置成功", fail = "错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<ModelConfigRespVO> createModelConfig(@Valid @RequestBody ModelConfigSaveReqVO createReqVO) {
        if (StringUtil.isNullBlank(createReqVO.getId())) {
            modelConfigService.createModelConfig(createReqVO);
        } else {
            modelConfigService.updateModelConfig(createReqVO);
        }
        return success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除积分模型配置")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "pam:integral:modelConfig:update", operateType = LogOperateType.CREATE, title = "删除积分模型配置",
            success = "删除积分模型配置成功", fail = "错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#ids}}")
    public CommonResult<Boolean> deleteModelConfig(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           modelConfigService.deleteModelConfig(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得积分模型配置")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ModelConfigRespVO> getModelConfig(@RequestParam("id") String id) {
        ModelConfigDO modelConfig = modelConfigService.getModelConfig(id);
        return success(BeanUtils.toBean(modelConfig, ModelConfigRespVO.class));
    }


    @GetMapping("/getDbByAppId")
    @ApiOperation(value = "获取应用所有数据源")
    @ApiImplicitParam(name = "systemMark", value = "系统标识")
    public CommonResult getDbByAppId(@RequestParam("systemMark") String systemMark){
        AppRespDTO app = bspApi.getAppByCode(systemMark);
        List<OpsDatasourceDTO> dbList = bspApi.getDbByAppId(app.getId());
        return success(dbList);
    }

    @GetMapping("/getResourcesByDbId")
    @ApiOperation(value = "根据数据源ID获取下属所有资源信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dbId", value = "数据源ID"),
            @ApiImplicitParam(name = "resType", value = "资源类型")
    })
    public CommonResult getResourcesByDbId(@RequestParam("dbId") String dbId, @RequestParam("resType") String resType){
        List<OpsResourceDTO> resourceList = bspApi.getResourcesByDbId(dbId, null, resType, 1, 100);
        return success(resourceList);
    }

    @GetMapping("/getFieldAllData")
    @ApiOperation(value = "根据资源ID获取资源信息")
    @ApiImplicitParam(name = "dbId", value = "数据源ID")
    public CommonResult getFieldAllData(@RequestParam("resId") String resId){
        List<OpsResFieldDTO> fieldList = bspApi.getFieldAllData(resId);
        return success(fieldList);
    }

}
