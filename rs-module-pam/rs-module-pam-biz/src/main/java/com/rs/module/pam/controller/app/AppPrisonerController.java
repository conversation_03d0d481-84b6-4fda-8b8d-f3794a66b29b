package com.rs.module.pam.controller.app;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.base.dao.pm.PrisonerInDao;
import com.rs.module.base.entity.pm.PrisonerInDO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "内外屏-人员信息")
@RestController
@RequestMapping("/app/pam/prisoner")
@Validated
public class AppPrisonerController {

    @Resource
    private PrisonerInDao prisonerInDao;


    @RequestMapping(value = "/getPrisonerIn", method = RequestMethod.GET)
    @ApiOperation(value = "获取在押人员数量")
    public CommonResult<JSONObject> getPrisonerIn(@RequestParam("orgCode") String orgCode,
                                                  @RequestParam("roomId") String roomId) {
        List<PrisonerInDO> prisonerList = prisonerInDao.getByJsh(orgCode, roomId);
        // 今日入所人数
        long toDayIn = prisonerList.stream().filter(prisoner -> DateUtil.isSameDay(new Date(), prisoner.getRssj())).count();
        // 高风险人数
        long riskNum = prisonerList.stream().filter(prisoner ->
                !StringUtil.isNullBlank(prisoner.getFxdj()) && "1".equals(prisoner.getFxdj())).count();
        // 重病号
        List<PrisonerInDO> sickList = prisonerInDao.getSickByRoom(orgCode, roomId);
        // 单独关押
        List<PrisonerInDO> aloneList = prisonerInDao.getAloneByRoom(orgCode, roomId);
        // 禁闭
        List<PrisonerInDO> confinementList = prisonerInDao.getConfinementByRoom(orgCode, roomId);
        // 戴戒具
        List<PrisonerInDO> equipmentList = prisonerInDao.getEquipmentByRoom(orgCode, roomId);
        // 外籍人
        long foreignerNum = prisonerList.stream().filter(prisoner -> !"".equals(prisoner.getGj())).count();


        JSONObject data = new JSONObject();
        data.put("count", prisonerList.size());
        data.put("sickNum", sickList.size());
        data.put("riskNum", riskNum);
        data.put("aloneNum", aloneList.size());
        data.put("confinementNum", confinementList.size());
        data.put("equipmentNum", equipmentList.size());
        data.put("foreignerNum", foreignerNum);
        data.put("toDayInNum", toDayIn);
        return success(data);
    }


}
