package com.rs.module.pam.controller.admin.screen.vo;

import com.alibaba.fastjson.JSONObject;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管教业务 - 违规登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class WgdjRespVO extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("违规地方名称")
    private String addressName;
    @ApiModelProperty("违规内容")
    private String violationContent;
    @ApiModelProperty("违规时间")
    private Date addTime;
    @ApiModelProperty("违规类型")
    private String violationType;
    @ApiModelProperty("违规状态 2 未处理 3 已处理")
    private String handleStatus;
    @ApiModelProperty("违规类别 2人工巡查，3智能终端")
    private String wglb;
}