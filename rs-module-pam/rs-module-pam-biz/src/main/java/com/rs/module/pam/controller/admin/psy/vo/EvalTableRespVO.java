package com.rs.module.pam.controller.admin.psy.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评量表管理 Response VO")
@Data
public class EvalTableRespVO implements TransPojo{

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("量表名称")
    private String name;
    @ApiModelProperty("量表编号")
    private String tableNo;
    @ApiModelProperty("使用状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_PSY_SYZT")
    private String usageStatus;
    @ApiModelProperty("量表类型（字典：ZD_XLPC_LBLX）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XLPC_LBLX")
    private String tableType;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("总题量")
    private Integer totalQuestionNumber;
    @ApiModelProperty("预计时长，单位分钟")
    private Integer estimatedDuration;
    @ApiModelProperty("计分规则")
    private String scoreRule;
    @ApiModelProperty("自定义计分规则")
    private String customScoreRule;
    @ApiModelProperty("结果解释规则")
    private String resultInterpRule;

}
