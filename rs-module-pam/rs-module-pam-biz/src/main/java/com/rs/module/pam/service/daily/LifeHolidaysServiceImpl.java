package com.rs.module.pam.service.daily;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.text.SimpleDateFormat;
import java.util.*;

import com.rs.module.pam.controller.admin.daily.vo.*;
import com.rs.module.pam.entity.daily.LifeHolidaysDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.pam.dao.daily.LifeHolidaysDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 监所事务管理-一日生活制度节假日 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LifeHolidaysServiceImpl extends BaseServiceImpl<LifeHolidaysDao, LifeHolidaysDO> implements LifeHolidaysService {

    @Resource
    private LifeHolidaysDao lifeHolidaysDao;

    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    public String createLifeHolidays(LifeHolidaysSaveReqVO createReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        LambdaQueryWrapper<LifeHolidaysDO> queryWrapper = Wrappers.lambdaQuery(LifeHolidaysDO.class);
        queryWrapper.eq(LifeHolidaysDO::getOrgCode, sessionUser.getOrgCode())
                .eq(LifeHolidaysDO::getHolidaysDate, createReqVO.getHolidaysDate());
        List<LifeHolidaysDO> lifeHolidaysDOS = lifeHolidaysDao.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(lifeHolidaysDOS)) {
            return lifeHolidaysDOS.get(0).getId();
        }
        // 插入
        LifeHolidaysDO lifeHolidays = BeanUtils.toBean(createReqVO, LifeHolidaysDO.class);
        lifeHolidaysDao.insert(lifeHolidays);
        // 返回
        return lifeHolidays.getId();
    }

    @Override
    public void updateLifeHolidays(LifeHolidaysSaveReqVO updateReqVO) {
        // 校验存在
        validateLifeHolidaysExists(updateReqVO.getId());
        // 更新
        LifeHolidaysDO updateObj = BeanUtils.toBean(updateReqVO, LifeHolidaysDO.class);
        lifeHolidaysDao.updateById(updateObj);
    }

    @Override
    public void deleteLifeHolidays(String id) {
        // 校验存在
        validateLifeHolidaysExists(id);
        // 删除
        lifeHolidaysDao.deleteById(id);
    }

    private void validateLifeHolidaysExists(String id) {
        if (lifeHolidaysDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-一日生活制度节假日数据不存在");
        }
    }

    @Override
    public LifeHolidaysDO getLifeHolidays(String id) {
        return lifeHolidaysDao.selectById(id);
    }

    @Override
    public PageResult<LifeHolidaysDO> getLifeHolidaysPage(LifeHolidaysPageReqVO pageReqVO) {
        return lifeHolidaysDao.selectPage(pageReqVO);
    }

    @Override
    public List<LifeHolidaysDO> getLifeHolidaysList(LifeHolidaysListReqVO listReqVO) {
        return lifeHolidaysDao.selectList(listReqVO);
    }

    @Override
    public void batchSaveLifeHolidays(LifeHolidaysBatchSaveReqVO batchSaveReqVO) {
        if(CollectionUtil.isEmpty(batchSaveReqVO.getHolidaysDates())){
            return;
        }
        List<Date> dates = batchSaveReqVO.getHolidaysDates();
        Map<String, Date> map = new HashMap<>();
        for (Date date : dates) {
            map.put(sdf.format(date), date);
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        LambdaQueryWrapper<LifeHolidaysDO> queryWrapper = Wrappers.lambdaQuery(LifeHolidaysDO.class);
        queryWrapper.eq(LifeHolidaysDO::getOrgCode, sessionUser.getOrgCode())
                .in(LifeHolidaysDO::getHolidaysDate, dates);
        List<LifeHolidaysDO> lifeHolidaysDOS = lifeHolidaysDao.selectList(queryWrapper);

        for (LifeHolidaysDO lifeHolidaysDO : lifeHolidaysDOS) {
            String key = sdf.format(lifeHolidaysDO.getHolidaysDate());
            if(Objects.nonNull(map.get(key))){
                dates.remove(map.get(key));
            }
        }
        for (Date date : dates) {
            // 插入
            LifeHolidaysSaveReqVO createReqVO = new LifeHolidaysSaveReqVO();
            createReqVO.setHolidaysDate(date);
            LifeHolidaysDO lifeHolidays = BeanUtils.toBean(createReqVO, LifeHolidaysDO.class);
            lifeHolidaysDao.insert(lifeHolidays);
        }

    }

}
