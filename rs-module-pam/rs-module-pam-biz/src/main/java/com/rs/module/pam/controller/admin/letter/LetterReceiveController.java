package com.rs.module.pam.controller.admin.letter;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.pam.controller.admin.letter.vo.*;
import com.rs.module.pam.entity.letter.LetterReceiveDO;
import com.rs.module.pam.service.letter.LetterReceiveService;

@Api(tags = "监所事务管理-家属通信-收信登记")
@RestController
@RequestMapping("/pam/letter/letterReceive")
@Validated
public class LetterReceiveController {

    @Resource
    private LetterReceiveService letterReceiveService;

    @PostMapping("/create")
    @ApiOperation(value = "创建监所事务管理-家属通信-收信登记")
    public CommonResult<String> createLetterReceive(@Valid @RequestBody LetterReceiveSaveReqVO createReqVO) {
        return success(letterReceiveService.createLetterReceive(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新监所事务管理-家属通信-收信登记")
    public CommonResult<Boolean> updateLetterReceive(@Valid @RequestBody LetterReceiveSaveReqVO updateReqVO) {
        letterReceiveService.updateLetterReceive(updateReqVO);
        return success(true);
    }

    @PostMapping("/approval")
    @ApiOperation(value = "审批监所事务管理-家属通信-审批")
    public CommonResult<Boolean> approvalLetterReceive(@Valid @RequestBody LetterReceiveApprovalReqVO approvalReqVO) {
        letterReceiveService.approvalLetterReceive(approvalReqVO);
        return success(true);
    }

    @PostMapping("/pass")
    @ApiOperation(value = "转交监所事务管理-家属通信-转交")
    public CommonResult<Boolean> passLetter(@Valid @RequestBody PassLetterReqVO passLetterReqVO) {
        letterReceiveService.passLetter(passLetterReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除监所事务管理-家属通信-收信登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteLetterReceive(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           letterReceiveService.deleteLetterReceive(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得监所事务管理-家属通信-收信登记")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<LetterReceiveRespVO> getLetterReceive(@RequestParam("id") String id) {
        LetterReceiveDO letterReceive = letterReceiveService.getLetterReceive(id);
        return success(BeanUtils.toBean(letterReceive, LetterReceiveRespVO.class));
    }

    /*@PostMapping("/page")
    @ApiOperation(value = "获得监所事务管理-家属通信-收信登记分页")
    public CommonResult<PageResult<LetterReceiveRespVO>> getLetterReceivePage(@Valid @RequestBody LetterReceivePageReqVO pageReqVO) {
        PageResult<LetterReceiveDO> pageResult = letterReceiveService.getLetterReceivePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LetterReceiveRespVO.class));
    }*/

    @PostMapping("/list")
    @ApiOperation(value = "获得监所事务管理-家属通信-收信登记列表")
    public CommonResult<List<LetterReceiveRespVO>> getLetterReceiveList(@Valid @RequestBody LetterReceiveListReqVO listReqVO) {
        List<LetterReceiveDO> list = letterReceiveService.getLetterReceiveList(listReqVO);
        return success(BeanUtils.toBean(list, LetterReceiveRespVO.class));
    }
}
