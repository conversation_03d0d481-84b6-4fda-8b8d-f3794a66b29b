package com.rs.module.pam.entity.duty;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-监室自动排班配置 DO
 *
 * <AUTHOR>
 */
@TableName("pam_duty_room_auto_config")
@KeySequence("pam_duty_room_auto_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_duty_room_auto_config")
public class RoomAutoConfigDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 自动排班规则，多个逗号分割（字典：ZD_ZBGL_ZDPBGZ）
     */
    private String schedulingRule;
    /**
     * 是否启用
     */
    private Short isEnabled;

}
