package com.rs.module.pam.service.screen.bo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ZdJydtJydtBO {

    private String orgCode;

    private String orgName;

    // 当前羁押总数
    private long jyzs;

    // 今日入所
    private long jrrs;

    // 今日出所
    private long jrcs;

    // 昨日羁押总数
    private long yesterdayJyzs;

    // 总数 较 昨日
    private long compareYesterday;

    // 押容比 百分比 保留两位小数
    private String yrb;

    // 监所容量
    private long sjrl;

    // 民警总数
    private long mjzs;

    // 警押比
    private BigDecimal jyb;
}
