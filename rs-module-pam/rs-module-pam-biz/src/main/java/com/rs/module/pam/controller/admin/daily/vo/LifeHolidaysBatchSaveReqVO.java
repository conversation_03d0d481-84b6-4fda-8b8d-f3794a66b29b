package com.rs.module.pam.controller.admin.daily.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-一日生活制度节假日批量创建")
@Data
@EqualsAndHashCode(callSuper = true)
public class LifeHolidaysBatchSaveReqVO extends BaseVO {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("节假日日期")
    private List<Date> holidaysDates;
}
