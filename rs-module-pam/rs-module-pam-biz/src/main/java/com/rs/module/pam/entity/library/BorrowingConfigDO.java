package com.rs.module.pam.entity.library;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-图书借阅配置 DO
 *
 * <AUTHOR>
 */
@TableName("pam_library_borrowing_config")
@KeySequence("pam_library_borrowing_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BorrowingConfigDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 个人最大借阅数
     */
    private Integer maximumBorrowingLimit;
    /**
     * 个人最大借阅时长（天）
     */
    private Integer maximumBorrowingDuration;

}
