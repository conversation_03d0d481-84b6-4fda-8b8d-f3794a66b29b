package com.rs.module.pam.service.info;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.info.vo.ReportItemListReqVO;
import com.rs.module.pam.controller.admin.info.vo.ReportItemSaveReqVO;
import com.rs.module.pam.dao.info.ReportItemDao;
import com.rs.module.pam.entity.info.ReportItemDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 监所事务管理-信息报备事项配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ReportItemServiceImpl extends BaseServiceImpl<ReportItemDao, ReportItemDO> implements ReportItemService {

    @Resource
    private ReportItemDao reportItemDao;

    @Override
    public String createReportItem(ReportItemSaveReqVO createReqVO) {
        ReportItemDO reportItem = saveItemDO(createReqVO);
        // 返回
        return reportItem.getId();
    }

    private ReportItemDO saveItemDO(ReportItemSaveReqVO createReqVO) {
        // 校验重复
        validateReportItemExists(createReqVO);
        // 插入
        ReportItemDO reportItem = BeanUtils.toBean(createReqVO, ReportItemDO.class);
        reportItemDao.insert(reportItem);
        return reportItem;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createReportItem(List<ReportItemSaveReqVO> createReqVOS) {
        for (ReportItemSaveReqVO createReqVO : createReqVOS) {
            if (createReqVO.getIsRollcall() == null) {
                throw new ServerException("是否参与点名不能为空");
            }
            saveItemDO(createReqVO);
        }
    }

    private void validateReportItemExists(ReportItemSaveReqVO reqVO) {
        // 校验重复
        LambdaQueryWrapper<ReportItemDO> lambdaQuery = Wrappers.lambdaQuery(ReportItemDO.class);
        lambdaQuery.select(ReportItemDO::getId)
                .eq(ReportItemDO::getReportType, reqVO.getReportType())
                .eq(ReportItemDO::getReportContent, reqVO.getReportContent());
        ReportItemDO reportItem = reportItemDao.selectOne(lambdaQuery);
        if (reportItem != null && !reportItem.getId().equals(reqVO.getId())) {
            throw new ServerException("信息报备事项配置数据已存在");
        }
        lambdaQuery.clear();
        lambdaQuery.select(ReportItemDO::getId)
                .eq(ReportItemDO::getReportContent, reqVO.getReportContent());
        ReportItemDO reportItem1 = reportItemDao.selectOne(lambdaQuery);
        if (reportItem1 != null && !reportItem1.getId().equals(reqVO.getId())) {
            throw new ServerException("信息报备事由配置数据已存在");
        }
    }

    @Override
    public void updateReportItem(ReportItemSaveReqVO updateReqVO) {
        // 校验存在
        validateReportItemExists(updateReqVO);
        // 更新
        ReportItemDO updateObj = BeanUtils.toBean(updateReqVO, ReportItemDO.class);
        reportItemDao.updateById(updateObj);
    }

    @Override
    public void deleteReportItem(String id) {
        // 校验存在
        validateReportItemExists(id);
        // 删除
        reportItemDao.deleteById(id);
    }

    private void validateReportItemExists(String id) {
        if (reportItemDao.selectById(id) == null) {
            throw new ServerException("信息报备事项配置数据不存在");
        }
    }

    @Override
    public ReportItemDO getReportItem(String id) {
        return reportItemDao.selectById(id);
    }

    @Override
    public List<ReportItemDO> getReportItemList(ReportItemListReqVO listReqVO) {
        return reportItemDao.selectList(listReqVO);
    }

    @Override
    public void updateStatus(String id, String status) {
        // 校验存在
        validateReportItemExists(id);
        ReportItemDO reportItemDO = new ReportItemDO();
        reportItemDO.setId(id);
        reportItemDO.setStatus(status);
        reportItemDao.updateById(reportItemDO);
    }


}
