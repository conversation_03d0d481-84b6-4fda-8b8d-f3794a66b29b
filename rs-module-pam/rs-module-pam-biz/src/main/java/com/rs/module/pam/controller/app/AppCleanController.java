package com.rs.module.pam.controller.app;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.enums.DataSourceAppEnum;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.daily.vo.CleanConfigListReqVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanConfigRespVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanPoliceRectificationVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanRespVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanSaveReqVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanleaderInstructionVO;
import com.rs.module.pam.controller.app.vo.AppCleanSaveReqVO;
import com.rs.module.pam.entity.daily.CleanConfigDO;
import com.rs.module.pam.entity.daily.CleanDO;
import com.rs.module.pam.service.daily.CleanConfigService;
import com.rs.module.pam.service.daily.CleanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.Date;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "仓外屏-安全检查-安全大检查、日常清监检查")
@RestController
@RequestMapping("/app/pam/daily/clean")
@Validated
public class AppCleanController {

    @Resource
    private CleanService cleanService;
    @Resource
    private CleanConfigService cleanConfigService;

    @PostMapping("/create")
    @ApiOperation(value = "创建-安全大检查、日常清监检查")
    public CommonResult<String> createClean(@Valid @RequestBody AppCleanSaveReqVO createReqVO) {
        CleanSaveReqVO cleanSaveReqVO = new CleanSaveReqVO();
        BeanUtils.copyProperties(createReqVO, cleanSaveReqVO);
        cleanSaveReqVO.setDataSources(DataSourceAppEnum.CWP.getCode());
        cleanSaveReqVO.setCheckTime(new Date());
        List<AppCleanSaveReqVO.CheckDetail> checkItems = createReqVO.getCheckItems();
        if (CollUtil.isEmpty(checkItems)) {
            throw new IllegalArgumentException("请选择检查项目");
        }
        // 是否违禁
        if (checkItems.stream().filter(checkItem -> "0".equals(checkItem.getStatus())).findFirst().isPresent()) {
            cleanSaveReqVO.setIsViolation("1");
            cleanSaveReqVO.setIsHiddenDanger("1");
        } else {
            cleanSaveReqVO.setIsViolation("0");
            cleanSaveReqVO.setIsHiddenDanger("0");
        }
        StringBuffer checkItemId = new StringBuffer("");
        StringBuffer violationContent = new StringBuffer("");
        StringBuffer checkContent = new StringBuffer("");
        for (int i = 0; i < checkItems.size(); i++) {
            AppCleanSaveReqVO.CheckDetail checkDetail = checkItems.get(i);
            checkItemId.append(checkDetail.getId());
            if ("0".equals(checkDetail.getStatus())) {
                violationContent.append(checkDetail.getCheckItemName() + "(检查内容：异常);");
                checkContent.append(checkDetail.getCheckItemName() + "(检查内容：异常);");
            } else {
                checkContent.append(checkDetail.getCheckItemName() + "(检查内容：正常);");
            }
            if (i < checkItems.size() - 1) {
                checkItemId.append(",");
                violationContent.append("\n");
                checkContent.append("\n");
            }
        }
        cleanSaveReqVO.setCheckItemId(checkItemId.toString());
        cleanSaveReqVO.setViolationContent(violationContent.toString());
        cleanSaveReqVO.setHiddenDangerContent(violationContent.toString());
        cleanSaveReqVO.setCheckContent(checkContent.toString());
        return success(cleanService.createClean(cleanSaveReqVO));
    }

    @PostMapping("/configList")
    @ApiOperation(value = "获得安全检查-日常清监检查项配置列表")
    public CommonResult<List<CleanConfigRespVO>> getCleanConfigList(@Valid @RequestBody CleanConfigListReqVO listReqVO) {
        if (StrUtil.isBlank(listReqVO.getOrgCode())) {
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        List<CleanConfigDO> list = cleanConfigService.getCleanConfigList(listReqVO);
        return success(BeanUtils.toBean(list, CleanConfigRespVO.class));
    }

}
