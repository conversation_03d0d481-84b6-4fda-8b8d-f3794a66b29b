package com.rs.module.pam.service.duty.day;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.duty.day.vo.DayDutyGroupSaveReqVO;
import com.rs.module.pam.entity.duty.GroupDO;
import com.rs.module.pam.entity.duty.day.DayDutyGroupDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 监所事务管理-值日组 Service 接口
 *
 * <AUTHOR>
 */
public interface DayDutyGroupService extends IBaseService<DayDutyGroupDO>{

    /**
     * 创建值日组
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createGroup(@Valid DayDutyGroupSaveReqVO createReqVO);

    /**
     * 删除值日组
     *
     * @param id 编号
     */
    void deleteGroup(String id);

    /**
     * 删除监所事务管理-值班组
     *
     * @param orgCode 编号
     * @param roomId 编号
     */
    List<DayDutyGroupDO> selectList(String orgCode, String roomId);

}
