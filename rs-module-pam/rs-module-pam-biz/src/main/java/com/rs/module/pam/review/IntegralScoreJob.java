package com.rs.module.pam.review;

import cn.hutool.core.date.DateUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.cache.RedisClient;
import com.bsp.common.util.StringUtil;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.dto.OrgRespDTO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.pam.cons.IntegralConstants;
import com.rs.module.pam.cons.LogHandleCodeEnum;
import com.rs.module.pam.controller.datasource.DataSourceSimpleUtil;
import com.rs.module.pam.entity.integral.*;
import com.rs.module.pam.service.integral.*;
import com.rs.module.pam.util.EngineUtils;
import com.rs.util.ThreadPoolUtil;
import com.xxl.job.core.context.XxlJobContext;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * 积分明细生成任务
 *
 */
@Component
@Slf4j
public class IntegralScoreJob {

    @Resource
    private RiskModelConfigService riskModelConfigService;
    @Resource
    private IndicatorService indicatorService;
    @Resource
    private ModelConfigService modelConfigService;
    @Resource
    private ScoreDetailService scoreDetailService;
    @Resource
    private IntegralLogService cpmIndicatorLogService;
    @Resource
    private PrisonerService prisonerService;
    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;
    @Resource
    private BspApi bspApi;

    private int CASE_MAX_COMMIT_VALUE = 1000;

    /**
     * 积分明细生成任务
     */
    @XxlJob("integral-score-item")
    @Transactional
    public void handler() {
        List<IndicatorDO> indicatorList = indicatorService.list();
        List<ModelConfigDO> modelList = modelConfigService.getAll();
        Map<String, IndicatorDO> indicatorMap = indicatorList.stream()
                .collect(Collectors.toMap(
                        IndicatorDO::getId,
                        obj -> obj,
                        (existing, replacement) -> replacement // 保留最后一个对象
                ));

        CountDownLatch latch = new CountDownLatch(modelList.size());
        //子线程中需要设置XxlJobContext,不然日志写入不了对应文件
        XxlJobContext xxlJobContext = XxlJobContext.getXxlJobContext();

        for (ModelConfigDO model : modelList) {
            if(dateStrCompare(model.getIncValue(), DateUtil.now()) >= 0) {
                latch.countDown();
                continue;
            }

            ThreadPoolUtil.getPool().execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        XxlJobContext.setXxlJobContext(xxlJobContext);
                        DataSource ds = DataSourceSimpleUtil.getDataSource(model.getDbId());
                        String sql = EngineUtils.appendSql(model, bspApi);
                        List<ScoreDetailDO> detailList = new ArrayList<>();
                        List<IntegralLogDO> logList = new ArrayList<>();

                        while (true) {
                            try {
                                //到达提交阈值
                                if(detailList.size() >= CASE_MAX_COMMIT_VALUE ) {
                                    scoreCaseDetailCommit(detailList, model,logList);
                                    detailList.clear();
                                    logList.clear();
                                }
                                IndicatorDO indicator = indicatorMap.get(model.getIndicatorTypeId());
                                String enddateString = setScoreCaseDetail(model, indicator, sql, ds, detailList, logList);
                                if(dateStrCompare(enddateString, DateUtil.now()) >= 0) {
                                    scoreCaseDetailCommit(detailList, model,logList);
                                    break;
                                }
                            } catch (Exception e) {
                                XxlJobHelper.log(e);
                                scoreCaseDetailCommit(detailList, model,logList);
                                e.printStackTrace();
                                break;
                            }
                        }
                    } catch (Exception e) {
                        XxlJobHelper.log(e);
                        e.printStackTrace();
                    } finally {
                        latch.countDown();
                    }
                }
            });
        }
    }

    @Transactional
    public void scoreCaseDetailCommit(List<ScoreDetailDO> detailList, ModelConfigDO modelConfig, List<IntegralLogDO> logList) {
        scoreDetailService.saveOrUpdateBatch(detailList);
        modelConfigService.updateById(modelConfig);
        cpmIndicatorLogService.saveBatch(logList);
    }

    private String setScoreCaseDetail(ModelConfigDO modelConfig, IndicatorDO indicator, String sql, DataSource ds,
            List<ScoreDetailDO> detailList, List<IntegralLogDO> logList) throws Exception {
        // 执行模型配置的脚本
        String begindateString = modelConfig.getIncValue();
        String enddateString = EngineUtils.dateInc(begindateString);
        XxlJobHelper.log("执行脚本：" + sql + "，时间范围：" + begindateString + "~" + enddateString);
        List<Entity> result = Db.use(ds).query(
                sql,
                DateUtil.parseDate(begindateString),
                DateUtil.parseDate(enddateString)
        );


        boolean hasRoomCode = StringUtil.isNotEmpty(modelConfig.getRoomCodeCol());
        boolean hasPrisonerCode = StringUtil.isNotEmpty(modelConfig.getPrisonerCodeCol());
        // 循环脚本执行结果，生成积分数据
        for(Entity entity : result) {
            // 机构编号
            String orgCode = entity.getStr(modelConfig.getOrgCodeCol());
            // 业务发生日期
            String dateValue = DateUtil.formatDate(entity.getDate(modelConfig.getBusinessDateCol()));
            // 执行日志
            IntegralLogDO integralLog = new IntegralLogDO();
            integralLog.setBusinessId(entity.getStr(modelConfig.getKeyCol()));
            integralLog.setOrgCode(orgCode);
            integralLog.setDateValue(dateValue);
            integralLog.setHandleParams(JSONObject.toJSONString(entity.entrySet()));
            integralLog.setModelId(modelConfig.getId());
            integralLog.setHandleTime(new Date());

            if(StringUtil.isEmpty(orgCode)) {
                integralLog.setOrgCode(modelConfig.getOrgCode());
                integralLog.setOrgName(modelConfig.getOrgName());
                this.setCpmIndicatorLog(logList, LogHandleCodeEnum.HANDLE_CODE_ORG_CODE_EMPTY, integralLog);
                XxlJobHelper.log("机构编号为空:" + modelConfig.getName());
                continue;
            }
            if(StringUtil.isEmpty(dateValue)) {
                this.setCpmIndicatorLog(logList, LogHandleCodeEnum.HANDLE_CODE_DATE_VALUE_ERROR, integralLog);
                XxlJobHelper.log("业务时间错误:" + modelConfig.getName());
                continue;
            }

            //获取机构信息
            OrgRespDTO org;
            String hget = RedisClient.hget(IntegralConstants.REDIS_KEY_ORGANIZATION, orgCode);
            if(StringUtil.isNotEmpty(hget)) {
                org = JSONObject.parseObject(hget, OrgRespDTO.class);
            }else {
                org = bspApi.getOrgByCode(orgCode);
                if(org == null) {
                    this.setCpmIndicatorLog(logList, LogHandleCodeEnum.HANDLE_CODE_ORG_CODE_EMPTY, integralLog);
                    XxlJobHelper.log(orgCode + "机构信息不存在");
                    continue;
                }else {
                    RedisClient.hset(IntegralConstants.REDIS_KEY_ORGANIZATION + ":" + orgCode, JSONObject.toJSONString(org), 24*60*60);
                }
            }
            RiskModelConfigDO riskModelConfig = riskModelConfigService.getById(modelConfig.getRiskModelId());
            // 积分详情
            ScoreDetailDO scoreDetail = new ScoreDetailDO();
            scoreDetail.setId(StringUtil.getGuid32());
            scoreDetail.setIsDel(false);
            scoreDetail.setOrgCode(org.getId());
            scoreDetail.setOrgName(org.getName());
            scoreDetail.setRiskModelId(modelConfig.getRiskModelId());
            scoreDetail.setRiskModelType(riskModelConfig.getType());
            scoreDetail.setIndicatorTypeId(modelConfig.getIndicatorTypeId());
            scoreDetail.setIndicatorId(modelConfig.getId());
            scoreDetail.setBusinessId(entity.getStr(modelConfig.getKeyCol()));
            scoreDetail.setDateValue(dateValue);
            scoreDetail.setScore(modelConfig.getScore().multiply(indicator.getWeight()));
            scoreDetail.setIndicatorScore(modelConfig.getScore());
            scoreDetail.setIndicatorId(modelConfig.getId());

            if(hasRoomCode) {
                String roomCode = entity.getStr(modelConfig.getRoomCodeCol());
                scoreDetail.setRoomCode(roomCode);
                AreaPrisonRoomDO roomDO = areaPrisonRoomService.getAreaPrisonRoom(roomCode);
                scoreDetail.setRoomName(roomDO.getRoomName());
            }
            if(hasPrisonerCode) {
                String prisonerCode = entity.getStr(modelConfig.getPrisonerCodeCol());
                scoreDetail.setPrisonerCode(prisonerCode);
                PrisonerVwRespVO prisoner = prisonerService.getPrisonerByJgrybm(prisonerCode);
                scoreDetail.setPrisonerName(prisoner.getXm());
            }

            detailList.add(scoreDetail);
            this.setCpmIndicatorLog(logList, LogHandleCodeEnum.HANDLE_CODE_SUCCESS, integralLog);
        }
        modelConfig.setIncValue(enddateString);
        return enddateString;
    }

    /**
     * 记录日志
     * @param logList
     * @param logHandleCodeEnum
     * @param integralLog
     */
    private void setCpmIndicatorLog(List<IntegralLogDO> logList, LogHandleCodeEnum logHandleCodeEnum, IntegralLogDO integralLog) {
        integralLog.setHandleCode(logHandleCodeEnum.getCode());
        integralLog.setHandleMsg(logHandleCodeEnum.getName());
        if(logHandleCodeEnum.getCode().equals(IntegralConstants.LOG_STATUS_SUCCESS)) {
            integralLog.setHandleStatus(IntegralConstants.LOG_STATUS_SUCCESS);
        }else {
            integralLog.setHandleStatus(IntegralConstants.LOG_STATUS_FAIL);
        }
        logList.add(integralLog);
    }

    private int dateStrCompare(String dateStr1 , String dateStr2) {
        long date1 = DateUtil.parseDate(dateStr1).getTime();
        long date2 = DateUtil.parseDate(dateStr2).getTime();
        if(date1 > date2 ) {
            return 1;
        }else if(date1 == date2) {
            return 0;
        }else {
            return -1;
        }
    }


}
