package com.rs.module.pam.controller.app.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-家属通信-收信确认 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AppConfirmLetterReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    @NotEmpty(message = "主键id不能为空")
    private String id;

    @ApiModelProperty("签名图片")
    @NotEmpty(message = "签名图片不能为空")
    private String signUrl;

}
