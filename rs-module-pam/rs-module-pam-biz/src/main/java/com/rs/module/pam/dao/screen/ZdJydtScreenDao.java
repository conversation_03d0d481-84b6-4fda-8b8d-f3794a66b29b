package com.rs.module.pam.dao.screen;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ZdJydtScreenDao {

    List<JSONObject> getAllOrgInfo(@Param("list") List<String> list);

    List<JSONObject> getJyzs(@Param("list") List<String> list);

    JSONObject syAndCsObj(@Param("list") List<String> list);

    List<JSONObject> syAndCsList(@Param("list") List<String> list);
}
