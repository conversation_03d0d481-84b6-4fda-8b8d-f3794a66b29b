package com.rs.module.pam.dao.cook;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.pam.entity.cook.SpecialApplyDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 监所事务管理-特殊餐申请 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface SpecialApplyDao extends IBaseDao<SpecialApplyDO> {

    @Select("SELECT id FROM pam_cook_special_apply WHERE jgrybm = #{jgrybm} AND reg_status IN ('00', '01','02') AND NOW() BETWEEN meal_start_time AND meal_end_time AND is_del = '0' LIMIT 1")
    String existUnfishCook(@Param("jgrybm") String jgrybm);

    List<Map<String, Object>> getMealType(@Param("jgrybms") List<String> jgrybms,
                                          @Param("mealPeriods") String mealPeriods);


}
