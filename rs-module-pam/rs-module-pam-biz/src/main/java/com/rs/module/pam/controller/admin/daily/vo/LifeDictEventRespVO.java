package com.rs.module.pam.controller.admin.daily.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 监所事务管理-事务选项字典 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LifeDictEventRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("event_name")
    private String eventName;
    @ApiModelProperty("夹控标记（字典：ZD_CWGL_JKBJ）")
    private Short eventStatus;
}
