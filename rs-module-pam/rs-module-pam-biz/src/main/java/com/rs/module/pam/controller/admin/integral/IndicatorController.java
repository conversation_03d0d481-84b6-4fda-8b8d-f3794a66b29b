package com.rs.module.pam.controller.admin.integral;

import com.bsp.common.util.StringUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.integral.vo.IndicatorRespVO;
import com.rs.module.pam.controller.admin.integral.vo.IndicatorSaveReqVO;
import com.rs.module.pam.entity.integral.IndicatorDO;
import com.rs.module.pam.service.integral.IndicatorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "分级处遇-积分指标")
@RestController
@RequestMapping("/pam/integral/indicator")
@Validated
public class IndicatorController {

    @Resource
    private IndicatorService indicatorService;

    @PostMapping("/create")
    @ApiOperation(value = "保存积分指标信息")
    @LogRecordAnnotation(bizModule = "pam:integral:indicator:create", operateType = LogOperateType.CREATE, title = "保存积分指标信息",
            success = "保存积分指标信息成功", fail = "错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<IndicatorRespVO> createIndicator(@Valid @RequestBody IndicatorSaveReqVO createReqVO) {
        IndicatorRespVO indicator = null;
        if (StringUtil.isNullBlank(createReqVO.getId())) {
            indicator = indicatorService.createIndicator(createReqVO);
        } else {
            indicator = indicatorService.updateIndicator(createReqVO);
        }
        return success(indicator);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除积分指标信息")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "pam:integral:indicator:update", operateType = LogOperateType.CREATE, title = "删除积分指标信息",
            success = "删除积分指标信息成功", fail = "错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#ids}}")
    public CommonResult<Boolean> deleteIndicator(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           indicatorService.deleteIndicator(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得积分指标信息")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<IndicatorRespVO> getIndicator(@RequestParam("id") String id) {
        IndicatorDO indicator = indicatorService.getIndicator(id);
        return success(BeanUtils.toBean(indicator, IndicatorRespVO.class));
    }

}
