package com.rs.module.pam.dao.info;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.controller.admin.info.vo.ReportListReqVO;
import com.rs.module.pam.controller.admin.info.vo.ReportPageReqVO;
import com.rs.module.pam.entity.info.ReportDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 监所事务管理-信息报备 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface ReportDao extends IBaseDao<ReportDO> {


    default PageResult<ReportDO> selectPage(ReportPageReqVO reqVO) {
        Page<ReportDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ReportDO> wrapper = new LambdaQueryWrapperX<ReportDO>()
                .eqIfPresent(ReportDO::getDataSources, reqVO.getDataSources())
                .eqIfPresent(ReportDO::getReporterId, reqVO.getReporterId())
                .likeIfPresent(ReportDO::getReporterName, reqVO.getReporterName())
                .betweenIfPresent(ReportDO::getReportTime, reqVO.getReportTime())
                .eqIfPresent(ReportDO::getRoomId, reqVO.getRoomId())
                .likeIfPresent(ReportDO::getRoomName, reqVO.getRoomName())
                .eqIfPresent(ReportDO::getObjectId, reqVO.getObjectId())
                .likeIfPresent(ReportDO::getObjectName, reqVO.getObjectName())
                .eqIfPresent(ReportDO::getReportType, reqVO.getReportType())
                .eqIfPresent(ReportDO::getReportContent, reqVO.getReportContent())
                .eqIfPresent(ReportDO::getReportTimeType, reqVO.getReportTimeType())
                .betweenIfPresent(ReportDO::getReportStartTime, reqVO.getReportStartTime())
                .betweenIfPresent(ReportDO::getReportEndTime, reqVO.getReportEndTime())
                .eqIfPresent(ReportDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ReportDO::getIsRollcall, reqVO.getIsRollcall())
                .eqIfPresent(ReportDO::getStatus, reqVO.getStatus())
                .eqIfPresent(ReportDO::getApproverSfzh, reqVO.getApproverSfzh())
                .eqIfPresent(ReportDO::getApproverXm, reqVO.getApproverXm())
                .betweenIfPresent(ReportDO::getApproverTime, reqVO.getApproverTime())
                .eqIfPresent(ReportDO::getApprovalResult, reqVO.getApprovalResult())
                .eqIfPresent(ReportDO::getApprovalComments, reqVO.getApprovalComments());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(ReportDO::getAddTime);
        }
        Page<ReportDO> reportPage = selectPage(page, wrapper);
        return new PageResult<>(reportPage.getRecords(), reportPage.getTotal());
    }

    default List<ReportDO> selectList(ReportListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ReportDO>()
                .eqIfPresent(ReportDO::getDataSources, reqVO.getDataSources())
                .eqIfPresent(ReportDO::getReporterId, reqVO.getReporterId())
                .likeIfPresent(ReportDO::getReporterName, reqVO.getReporterName())
                .betweenIfPresent(ReportDO::getReportTime, reqVO.getReportTime())
                .eqIfPresent(ReportDO::getRoomId, reqVO.getRoomId())
                .likeIfPresent(ReportDO::getRoomName, reqVO.getRoomName())
                .eqIfPresent(ReportDO::getObjectId, reqVO.getObjectId())
                .likeIfPresent(ReportDO::getObjectName, reqVO.getObjectName())
                .eqIfPresent(ReportDO::getReportType, reqVO.getReportType())
                .eqIfPresent(ReportDO::getReportContent, reqVO.getReportContent())
                .eqIfPresent(ReportDO::getReportTimeType, reqVO.getReportTimeType())
                .betweenIfPresent(ReportDO::getReportStartTime, reqVO.getReportStartTime())
                .betweenIfPresent(ReportDO::getReportEndTime, reqVO.getReportEndTime())
                .eqIfPresent(ReportDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ReportDO::getIsRollcall, reqVO.getIsRollcall())
                .eqIfPresent(ReportDO::getStatus, reqVO.getStatus())
                .eqIfPresent(ReportDO::getApproverSfzh, reqVO.getApproverSfzh())
                .eqIfPresent(ReportDO::getApproverXm, reqVO.getApproverXm())
                .betweenIfPresent(ReportDO::getApproverTime, reqVO.getApproverTime())
                .eqIfPresent(ReportDO::getApprovalResult, reqVO.getApprovalResult())
                .eqIfPresent(ReportDO::getApprovalComments, reqVO.getApprovalComments())
                .orderByDesc(ReportDO::getAddTime));
    }


}
