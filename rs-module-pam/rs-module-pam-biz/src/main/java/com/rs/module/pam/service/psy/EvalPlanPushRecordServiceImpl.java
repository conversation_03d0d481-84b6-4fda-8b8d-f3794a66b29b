package com.rs.module.pam.service.psy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanPushRecordListReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanPushRecordRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanPushRecordSaveReqVO;
import com.rs.module.pam.controller.app.vo.AppPsyTableRecordRespVO;
import com.rs.module.pam.dao.psy.EvalPlanPushRecordDao;
import com.rs.module.pam.entity.psy.EvalPlanPushRecordDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;


/**
 * 监所事务管理-心理测评推送记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EvalPlanPushRecordServiceImpl extends BaseServiceImpl<EvalPlanPushRecordDao, EvalPlanPushRecordDO> implements EvalPlanPushRecordService {

    @Resource
    private EvalPlanPushRecordDao evalPlanPushRecordDao;

    @Override
    public String createEvalPlanPushRecord(EvalPlanPushRecordSaveReqVO createReqVO) {
        // 插入
        EvalPlanPushRecordDO evalPlanPushRecord = BeanUtils.toBean(createReqVO, EvalPlanPushRecordDO.class);
        evalPlanPushRecordDao.insert(evalPlanPushRecord);
        // 返回
        return evalPlanPushRecord.getId();
    }

    @Override
    public void updateEvalPlanPushRecord(EvalPlanPushRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateEvalPlanPushRecordExists(updateReqVO.getId());
        // 更新
        EvalPlanPushRecordDO updateObj = BeanUtils.toBean(updateReqVO, EvalPlanPushRecordDO.class);
        evalPlanPushRecordDao.updateById(updateObj);
    }

    @Override
    public void deleteEvalPlanPushRecord(String id) {
        // 校验存在
        validateEvalPlanPushRecordExists(id);
        // 删除
        evalPlanPushRecordDao.deleteById(id);
    }

    private void validateEvalPlanPushRecordExists(String id) {
        if (evalPlanPushRecordDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-心理测评推送记录数据不存在");
        }
    }

    @Override
    public EvalPlanPushRecordDO getEvalPlanPushRecord(String id) {
        return evalPlanPushRecordDao.selectById(id);
    }

    @Override
    public List<EvalPlanPushRecordRespVO> getEvalPlanPushRecordList(EvalPlanPushRecordListReqVO listReqVO) {
        return evalPlanPushRecordDao.getEvalPlanPushRecordList(listReqVO);
    }

    @Override
    public Long getEvalPlanPushRecordListCount(EvalPlanPushRecordListReqVO listReqVO) {
        return evalPlanPushRecordDao.getEvalPlanPushRecordListCount(listReqVO);
    }

    @Override
    public EvalPlanPushRecordDO getByEvalNo(String evalNo) {
        return evalPlanPushRecordDao.selectOne(EvalPlanPushRecordDO::getEvalNo, evalNo);
    }

    @Override
    public List<AppPsyTableRecordRespVO> tableList(String jgrybm, String tableType) {
        List<String> tableTypes = new ArrayList<>();
        if (StrUtil.isBlank(tableType)) {
            tableTypes.add("1");
            tableTypes.add("2");
            tableTypes.add("3");
            tableTypes.add("99");
        } else {
            tableTypes.add(tableType);
        }
        List<AppPsyTableRecordRespVO> list = evalPlanPushRecordDao.tableList(jgrybm, tableTypes);
        return list;
    }

    @Override
    public List<String> existPushRecord(String planCode, List<String> tableIds, Set<String> jgrybmsSet) {
        List<String> result = evalPlanPushRecordDao.existPushRecord(planCode, tableIds, jgrybmsSet);
        if (CollUtil.isEmpty(result)) {
            return new ArrayList<>();
        }
        return result;
    }
}
