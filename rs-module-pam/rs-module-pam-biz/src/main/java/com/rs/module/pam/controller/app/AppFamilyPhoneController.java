package com.rs.module.pam.controller.app;

import com.rs.framework.common.enums.DataSourceAppEnum;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.family.vo.FamilyPhoneRespVO;
import com.rs.module.pam.controller.admin.family.vo.FamilyPhoneSaveReqVO;
import com.rs.module.pam.entity.family.FamilyPhoneDO;
import com.rs.module.pam.service.family.FamilyPhoneService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监所事务管理-亲情电话")
@RestController
@RequestMapping("/app/pam/family/familyPhone")
@Validated
public class AppFamilyPhoneController {

    @Resource
    private FamilyPhoneService familyPhoneService;

    @PostMapping("/create")
    @ApiOperation(value = "App-创建监所事务管理-亲情电话(仓内屏)")
    public CommonResult<String> createFamilyPhone(@Valid @RequestBody FamilyPhoneSaveReqVO createReqVO) {
        createReqVO.setDataSources(DataSourceAppEnum.CNP.getCode());
        return success(familyPhoneService.createFamilyPhone(createReqVO));
    }

    @GetMapping("/page")
    @ApiOperation(value = "App-亲情电话管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小"),
            @ApiImplicitParam(name = "jgrybm", value = "被监管人员编码"),
            @ApiImplicitParam(name = "type", value = "亲情电话周期类型 1 全部，2 今天，3 昨天，4 近一周")
    })
    public CommonResult<PageResult<FamilyPhoneRespVO>> getAppFamilyPhonePage(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                                             @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                                             @RequestParam("jgrybm") String jgrybm,
                                                                             @RequestParam("type") String type) {
        PageResult<FamilyPhoneDO> pageResult = familyPhoneService.getAppFamilyPhonePage(pageNo, pageSize, jgrybm, type);
        return success(BeanUtils.toBean(pageResult, FamilyPhoneRespVO.class));
    }
}
