package com.rs.module.pam.controller.admin.psy.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 监所事务管理-心理测评计划新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EvalPlanSaveReqVO extends BaseVO{

    @ApiModelProperty("主键(更新时必填)")
    private String id;

    @ApiModelProperty("计划名称")
    @NotEmpty(message = "计划名称不能为空")
    private String planName;

    @ApiModelProperty("计划说明")
    private String remark;

    @ApiModelProperty("量表ID,多个ID用逗号分隔")
    @NotEmpty(message = "量表ID不能为空")
    private String tableId;

    @ApiModelProperty("量表名称,多个名称用逗号分隔")
    @NotEmpty(message = "量表名称不能为空")
    private String tableName;

    @ApiModelProperty("触发类型")
    @NotEmpty(message = "触发类型不能为空")
    private String triggerType;

    @ApiModelProperty("触发配置，json存储")
    private String triggerConfig;

    @ApiModelProperty("推送对象")
    private String pushTarget;

    @ApiModelProperty("测评次数")
    @NotNull(message = "测评次数不能为空")
    private Integer evalNumber;

    @ApiModelProperty("完成时限")
    @NotNull(message = "完成时限不能为空")
    private Integer completionDeadline;

    @ApiModelProperty("启动消息推送")
    private String enableMessagePush;

    @ApiModelProperty("消息推送配置")
    private String messagePush;

    @ApiModelProperty("测评状态（字典：ZD_PSY_CPZT）")
    @NotEmpty(message = "测评状态（字典：ZD_PSY_CPZT）不能为空")
    private String status;

    @ApiModelProperty("启用状态")
    @NotNull(message = "启用状态不能为空")
    private String enableStatus;

}
