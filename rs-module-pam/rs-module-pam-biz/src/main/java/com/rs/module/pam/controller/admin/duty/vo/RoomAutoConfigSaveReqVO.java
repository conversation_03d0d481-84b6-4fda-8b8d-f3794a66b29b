package com.rs.module.pam.controller.admin.duty.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 监所事务管理-监室自动排班配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RoomAutoConfigSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("机构编号")
    @NotEmpty(message = "机构编号不能为空")
    private String orgCode;

    @ApiModelProperty("监室编号")
    @NotEmpty(message = "监室编号不能为空")
    private String roomId;

    @ApiModelProperty("自动排班规则，多个逗号分割（字典：ZD_ZBGL_ZDPBGZ）")
    @NotEmpty(message = "自动排班规则，多个逗号分割（字典：ZD_ZBGL_ZDPBGZ）不能为空")
    private String schedulingRule;

    @ApiModelProperty("是否启用")
    @NotNull(message = "是否启用不能为空")
    private Short isEnabled;

}
