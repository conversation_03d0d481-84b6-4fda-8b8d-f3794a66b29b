package com.rs.module.pam.service.daily;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.daily.vo.CleanConfigListReqVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanConfigSaveReqVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanConfigUpdateReqVO;
import com.rs.module.pam.entity.daily.CleanConfigDO;

import java.util.List;

/**
 * 监所事务管理-日常清监检查项配置 Service 接口
 *
 * <AUTHOR>
 */
public interface CleanConfigService extends IBaseService<CleanConfigDO>{

    /**
     * 创建监所事务管理-日常清监检查项配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    void createCleanConfig(CleanConfigSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-日常清监检查项配置
     *
     * @param updateReqVO 更新信息
     */
    void updateCleanConfig(CleanConfigUpdateReqVO updateReqVO);

    /**
     * 删除监所事务管理-日常清监检查项配置
     *
     * @param id 编号
     */
    void deleteCleanConfig(String id);

    /**
     * 获得监所事务管理-日常清监检查项配置
     *
     * @param id 编号
     * @return 监所事务管理-日常清监检查项配置
     */
    CleanConfigDO getCleanConfig(String id);

    /**
    * 获得监所事务管理-日常清监检查项配置列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-日常清监检查项配置列表
    */
    List<CleanConfigDO> getCleanConfigList(CleanConfigListReqVO listReqVO);


}
