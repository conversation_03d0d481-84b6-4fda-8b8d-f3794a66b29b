package com.rs.module.pam.service.letter;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.enums.DataSourceAppEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.pam.controller.admin.letter.vo.*;
import com.rs.module.pam.controller.app.vo.AppLetterPostSaveReqVO;
import com.rs.module.pam.dao.letter.LetterPostDao;
import com.rs.module.pam.entity.letter.LetterPostDO;
import com.rs.module.pam.enums.LetterPostJxspztEnum;
import com.rs.module.pam.enums.LetterPostJxztEnum;
import com.rs.module.pam.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;


/**
 * 监所事务管理-家属通信-寄信登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class LetterPostServiceImpl extends BaseServiceImpl<LetterPostDao, LetterPostDO> implements LetterPostService {

    @Resource
    private LetterPostDao letterPostDao;

    private static final String defKey = "shouxinguanliliucheng";

    @Override
    public String createLetterPost(LetterPostSaveReqVO createReqVO) {
        // 插入
        LetterPostDO letterPost = BeanUtils.toBean(createReqVO, LetterPostDO.class);
        letterPostDao.insert(letterPost);
        // 返回
        return letterPost.getId();
    }

    @Override
    public void updateLetterPost(LetterPostSaveReqVO updateReqVO) {
        // 校验存在
        validateLetterPostExists(updateReqVO.getId());
        // 更新
        LetterPostDO updateObj = BeanUtils.toBean(updateReqVO, LetterPostDO.class);
        letterPostDao.updateById(updateObj);
    }

    @Override
    public void deleteLetterPost(String id) {
        // 校验存在
        validateLetterPostExists(id);
        // 删除
        letterPostDao.deleteById(id);
    }

    private void validateLetterPostExists(String id) {
        if (letterPostDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-家属通信-寄信登记数据不存在");
        }
    }

    @Override
    public LetterPostDO getLetterPost(String id) {
        return letterPostDao.selectById(id);
    }

    @Override
    public PageResult<LetterPostDO> getLetterPostPage(LetterPostPageReqVO pageReqVO) {
        return letterPostDao.selectPage(pageReqVO);
    }

    @Override
    public List<LetterPostDO> getLetterPostList(LetterPostListReqVO listReqVO) {
        return letterPostDao.selectList(listReqVO);
    }

    @Override
    public String appCreateLetterPost(AppLetterPostSaveReqVO appLetterPostSaveReqVO) {
        // 仓内屏发起请求
        // 插入
        LetterPostDO letterPost = BeanUtils.toBean(appLetterPostSaveReqVO, LetterPostDO.class);
        letterPost.setDataSources(DataSourceAppEnum.CNP.getCode());
        letterPost.setApplyTime(new Date());
        letterPost.setStatus(LetterPostJxztEnum.DJC.getCode());
        letterPost.setApproverStatus(LetterPostJxspztEnum.DGJSH.getCode());
        letterPostDao.insert(letterPost);

        //启动流程
        String msgUrl = "/#/familyContact/send";
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", letterPost.getId());
        JSONObject result = BspApprovalUtil.defaultStartProcess(HttpUtils.getAppCode() + "-" + defKey, letterPost.getId(), "寄信流程审批", msgUrl, variables);
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            letterPost.setActInstId(bpmTrail.getString("actInstId"));
            letterPost.setTaskId(bpmTrail.getString("taskId"));
            letterPostDao.updateById(letterPost);
        } else {
            throw new ServerException("流程启动失败");
        }
        // 返回
        return letterPost.getId();
    }

    @Override
    public void approvalLetterPost(LetterPostApprovalReqVO approvalReqVO) {

        LetterPostDO letterPostDO = letterPostDao.selectById(approvalReqVO.getId());
        if (letterPostDO == null) {
            throw new ServerException("监所事务管理-家属通信-寄信登记数据不存在");
        }
        if (LetterPostJxspztEnum.DJC.getCode().equals(letterPostDO.getApproverStatus()) ||
                LetterPostJxspztEnum.YJC.getCode().equals(letterPostDO.getApproverStatus()) ||
                LetterPostJxspztEnum.LXCL.getCode().equals(letterPostDO.getApproverStatus()) ||
                LetterPostJxspztEnum.BTG.getCode().equals(letterPostDO.getApproverStatus())) {
            throw new ServerException("当前为【" + LetterPostJxspztEnum.getByCode(letterPostDO.getApproverStatus()).getName()
                    + "】业务流程，无需走审批流程");
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(letterPostDO.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }
        BspApproceStatusEnum bspApproceStatusEnum = BspApproceStatusEnum.getByCode(approvalReqVO.getResult());
        if (Objects.isNull(bspApproceStatusEnum)) {
            throw new ServerException("非法审批结果");
        }
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", letterPostDO.getId());
        variables.put("approval_status", BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ? "ty" : "bty");
        String approvalComments;
        // 管教审核
        if (LetterPostJxspztEnum.DGJSH.getCode().equals(letterPostDO.getApproverStatus())) {
            letterPostDO.setApproverStatus(BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ? LetterPostJxspztEnum.DJC.getCode()
                    : LetterPostJxspztEnum.DKZZSH.getCode());
            letterPostDO.setStatus(BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ? LetterPostJxztEnum.DJC.getCode()
                    : LetterPostJxztEnum.LXCL.getCode());
            letterPostDO.setGjApprovalComments(approvalReqVO.getApprovalComments());
            letterPostDO.setGjApprovalResult("" + bspApproceStatusEnum.getCode());
            letterPostDO.setGjApproverSfzh(sessionUser.getIdCard());
            letterPostDO.setGjApproverXm(sessionUser.getName());
            letterPostDO.setGjApproverTime(new Date());
        } else if (LetterPostJxspztEnum.DKZZSH.getCode().equals(letterPostDO.getApproverStatus())) {
            letterPostDO.setApproverStatus(BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ? LetterPostJxspztEnum.DJC.getCode()
                    : LetterPostJxspztEnum.DSLDSH.getCode());
            letterPostDO.setStatus(BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ? LetterPostJxztEnum.DJC.getCode()
                    : LetterPostJxztEnum.LXCL.getCode());
            letterPostDO.setGroupApprovalComments(approvalReqVO.getApprovalComments());
            letterPostDO.setGroupApprovalResult("" + bspApproceStatusEnum.getCode());
            letterPostDO.setGroupApproverSfzh(sessionUser.getIdCard());
            letterPostDO.setGroupApproverXm(sessionUser.getName());
            letterPostDO.setGroupApproverTime(new Date());
        } else if (LetterPostJxspztEnum.DSLDSH.getCode().equals(letterPostDO.getApproverStatus())) {
            letterPostDO.setApproverStatus(BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ?
                    LetterPostJxspztEnum.DJC.getCode() : LetterPostJxspztEnum.BTG.getCode());
            // 所领导不同意要结束流程
            if (BspApproceStatusEnum.NOT_PASSED.getCode() == bspApproceStatusEnum.getCode()) {
                bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
            }
            letterPostDO.setStatus(BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ? LetterPostJxztEnum.DJC.getCode()
                    : LetterPostJxztEnum.LXCL.getCode());
            letterPostDO.setLeaderApprovalComments(approvalReqVO.getApprovalComments());
            letterPostDO.setLeaderApprovalResult("" + bspApproceStatusEnum.getCode());
            letterPostDO.setLeaderApproverSfzh(sessionUser.getIdCard());
            letterPostDO.setLeaderApproverXm(sessionUser.getName());
            letterPostDO.setLeaderApproverTime(new Date());
        } else {
            throw new ServerException("非法审批状态");
        }
        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getId());
        nowApproveUser.put("name", sessionUser.getName());
        boolean terminateTask = BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ||
                BspApproceStatusEnum.NOT_PASSED_END.getCode() == bspApproceStatusEnum.getCode();
        JSONObject result = BspApprovalUtil.approvalProcess(HttpUtils.getAppCode() + "-" + defKey, letterPostDO.getActInstId(),
                letterPostDO.getTaskId(), letterPostDO.getId(),
                bspApproceStatusEnum, approvalReqVO.getApprovalComments(), null, null, terminateTask,
                variables, nowApproveUser, HttpUtils.getAppCode());
        log.info("=====result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            letterPostDO.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程启动失败");
        }
        letterPostDao.updateById(letterPostDO);

    }

    @Override
    public void letterPost(LetterPostReqVO letterPostReqVO) {
        LetterPostDO letterPostDO = letterPostDao.selectById(letterPostReqVO.getId());
        if (letterPostDO == null) {
            throw new ServerException("监所事务管理-家属通信-寄信登记数据不存在");
        }
        if (!LetterPostJxspztEnum.DJC.getCode().equals(letterPostDO.getApproverStatus())) {
            throw new ServerException("非待寄出状态，不能寄出");
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        letterPostDO.setReceiveMailUser(letterPostReqVO.getReceiveMailUser());
        letterPostDO.setRelation(letterPostReqVO.getRelation());
        letterPostDO.setReceiveAddress(letterPostReqVO.getReceiveAddress());
        letterPostDO.setSendTime(letterPostReqVO.getSendTime());
        letterPostDO.setReceivePrison(letterPostReqVO.getReceivePrison());
        letterPostDO.setMailNo(letterPostReqVO.getMailNo());
        letterPostDO.setRemark(letterPostReqVO.getRemark());
        letterPostDO.setStatus(LetterPostJxztEnum.YJC.getCode());
        letterPostDO.setApproverStatus(LetterPostJxspztEnum.YJC.getCode());
        letterPostDO.setPassUser(sessionUser.getName());
        letterPostDO.setPassTime(new Date());
        letterPostDao.updateById(letterPostDO);
    }

    @Override
    public PageResult<LetterPostDO> getAppLetterPostPage(int pageNo, int pageSize, String jgrybm, String type) {
        Map<String, Date> commonAppRecordPeriod = CommonUtils.getCommonAppRecordPeriod(type);
        return letterPostDao.getAppLetterPostPage(pageNo, pageSize, jgrybm,
                commonAppRecordPeriod.get("startTime"), commonAppRecordPeriod.get("endTime"));
    }


}
