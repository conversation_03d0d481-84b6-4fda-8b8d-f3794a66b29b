package com.rs.module.pam.entity.psy;

import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-心理测评推送记录 DO
 *
 * <AUTHOR>
 */
@TableName("pam_psy_eval_plan_push_record")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_psy_eval_plan_push_record")
public class EvalPlanPushRecordDO extends BaseDO {

    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 测评编号
     */
    private String evalNo;
    /**
     * 计划名称
     */
    private String planName;
    /**
     * 计划编码
     */
    private String planCode;
    /**
     * 计划类型
     */
    private String planType;
    /**
     * 计划创建人
     */
    private String planAddUserName;
    /**
     * 量表ID
     */
    private String tableId;
    /**
     * 量表名称
     */
    private String tableName;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 推送时间
     */
    private Date pushTime;
    /**
     * 填写状态
     */
    private String fillingStatus;
    /**
     * 填写时间
     */
    private Date fillingTime;
    /**
     * 填报平台(字典：ZD_DATA_SOURCES) 实战平台：1, 仓外屏：2, 仓内屏：3
     */
    private String fillingPlatform;
    /**
     * 测评得分
     */
    private BigDecimal score;
    /**
     * 测评结果
     */
    private String evalResults;

}
