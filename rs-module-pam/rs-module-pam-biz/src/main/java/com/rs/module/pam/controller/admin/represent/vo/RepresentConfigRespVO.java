package com.rs.module.pam.controller.admin.represent.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 监所事务管理-监室点名配置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RepresentConfigRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监室id")
    private String roomId;
    @ApiModelProperty("监室name")
    private String roomName;
    @ApiModelProperty("配置周期")
    private String configPeriod;
    @ApiModelProperty("配置时间")
    private String startTime;
    @ApiModelProperty("定时任务表达式")
    private String cron;
    @ApiModelProperty("定时任务表达式")
    private String cronStr;
    @ApiModelProperty("计划周期code 1-自定义 2-每天 3-执行一次")
    private String configPeriodCode;
    @ApiModelProperty("任务名称")
    private String taskName;
    @ApiModelProperty("0：启用 1：未启用")
    private Integer status;
    @ApiModelProperty(value = "0-启用 1-未")
    private String statusDisplayName;
    @ApiModelProperty("配置的结束时间")
    private String endTime;
    @ApiModelProperty("结束时间的定时任务表达式")
    private String endCron;
    @ApiModelProperty("有效期")
    private Integer expiryDate;
}
