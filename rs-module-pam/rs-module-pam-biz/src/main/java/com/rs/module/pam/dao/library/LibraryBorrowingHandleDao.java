package com.rs.module.pam.dao.library;

import java.util.*;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.entity.library.LibraryBorrowingHandleDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.pam.controller.admin.library.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 监所事务管理-图书借阅处理 Dao
*
* <AUTHOR>
*/
@Mapper
public interface LibraryBorrowingHandleDao extends IBaseDao<LibraryBorrowingHandleDO> {


    default PageResult<LibraryBorrowingHandleDO> selectPage(LibraryBorrowingHandlePageReqVO reqVO) {
        Page<LibraryBorrowingHandleDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<LibraryBorrowingHandleDO> wrapper = new LambdaQueryWrapperX<LibraryBorrowingHandleDO>()
            .eqIfPresent(LibraryBorrowingHandleDO::getBorrowingId, reqVO.getBorrowingId())
            .eqIfPresent(LibraryBorrowingHandleDO::getStatus, reqVO.getStatus())
            .eqIfPresent(LibraryBorrowingHandleDO::getHandleUserSfzh, reqVO.getHandleUserSfzh())
            .eqIfPresent(LibraryBorrowingHandleDO::getHandleUser, reqVO.getHandleUser())
            .betweenIfPresent(LibraryBorrowingHandleDO::getHandleTime, reqVO.getHandleTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(LibraryBorrowingHandleDO::getAddTime);
        }
        Page<LibraryBorrowingHandleDO> libraryBorrowingHandlePage = selectPage(page, wrapper);
        return new PageResult<>(libraryBorrowingHandlePage.getRecords(), libraryBorrowingHandlePage.getTotal());
    }
    default List<LibraryBorrowingHandleDO> selectList(LibraryBorrowingHandleListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<LibraryBorrowingHandleDO>()
            .eqIfPresent(LibraryBorrowingHandleDO::getBorrowingId, reqVO.getBorrowingId())
            .eqIfPresent(LibraryBorrowingHandleDO::getStatus, reqVO.getStatus())
            .eqIfPresent(LibraryBorrowingHandleDO::getHandleUserSfzh, reqVO.getHandleUserSfzh())
            .eqIfPresent(LibraryBorrowingHandleDO::getHandleUser, reqVO.getHandleUser())
            .betweenIfPresent(LibraryBorrowingHandleDO::getHandleTime, reqVO.getHandleTime())
        .orderByDesc(LibraryBorrowingHandleDO::getAddTime));    }


    List<JSONObject> getBusinessAnalysis(@Param("time") Date time, @Param("orgCode") String orgCode);
}
