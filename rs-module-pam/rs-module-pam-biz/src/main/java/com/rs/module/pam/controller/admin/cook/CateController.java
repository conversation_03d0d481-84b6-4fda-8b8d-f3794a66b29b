package com.rs.module.pam.controller.admin.cook;

import cn.hutool.core.util.StrUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.cook.vo.CateListReqVO;
import com.rs.module.pam.controller.admin.cook.vo.CateRespVO;
import com.rs.module.pam.controller.admin.cook.vo.CateSaveReqVO;
import com.rs.module.pam.entity.cook.CateDO;
import com.rs.module.pam.service.cook.CateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监室配餐-菜品分类")
@RestController
@RequestMapping("/pam/cook/cate")
@Validated
public class CateController {

    @Resource
    private CateService cateService;

    @PostMapping("/create")
    @ApiOperation(value = "创建-菜品分类")
    public CommonResult<String> createCate(@Valid @RequestBody CateSaveReqVO createReqVO) {
        return success(cateService.createCate(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新-菜品分类")
    public CommonResult<Boolean> updateCate(@Valid @RequestBody CateSaveReqVO updateReqVO) {
        cateService.updateCate(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除-菜品分类")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteCate(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            cateService.deleteCate(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得-菜品分类")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<CateRespVO> getCate(@RequestParam("id") String id) {
        CateDO cate = cateService.getCate(id);
        return success(BeanUtils.toBean(cate, CateRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得-菜品分类列表")
    public CommonResult<List<CateRespVO>> getCateList(@Valid @RequestBody CateListReqVO listReqVO) {
        if (StrUtil.isBlank(listReqVO.getOrgCode())) {
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        List<CateDO> list = cateService.getCateList(listReqVO);
        return success(BeanUtils.toBean(list, CateRespVO.class));
    }
}
