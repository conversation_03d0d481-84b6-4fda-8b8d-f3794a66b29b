package com.rs.module.pam.dao.screen;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.module.pam.entity.screen.WgdjDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AqdtScreenDao {

    JSONObject jyCount(@Param("code") String code);

    JSONObject jydt(@Param("code") String code);

    JSONObject getOrgInfo(@Param("code") String code);

    List<String> getOneYearYearMonth();

    List<String> getOneWeekMonthDay();

    List<String> getOneMonthMonthDay();

    // 获取月度收押
    List<JSONObject> getMonthSy(@Param("code") String code);

    // 获取某所 某月之后 的入所数 和出所数
    JSONObject getMonthAfterZyInfo(@Param("code") String code, @Param("yearMonth") String yearMonth);


    List<JSONObject> gjqryfb(@Param("code") String code);

    JSONObject nldfb(@Param("code") String code);

    JSONObject getGaJcyFySshjTj(@Param("list") List<String> list);

    List<JSONObject> getAllSshj(@Param("list") List<String> list);

    List<JSONObject> getAllSxzm(@Param("list") List<String> list);

    JSONObject jycqtj(@Param("code") String code);

    JSONObject jqdcstj(@Param("code") String code);

    JSONObject fxryfb(@Param("code") String code);

    List<JSONObject> getFxryqsList(@Param("code") String code);

    List<JSONObject> getWgqs(@Param("code") String code);

    Page<WgdjDO> getWggjPage(Page<WgdjDO> page, @Param("code") String code, @Param("type") String type,
                             @Param("wglb") String wglb, @Param("handleStatus") String handleStatus);

    JSONObject getWggjCount(@Param("code") String code, @Param("type") String type,
                             @Param("wglb") String wglb);
}
