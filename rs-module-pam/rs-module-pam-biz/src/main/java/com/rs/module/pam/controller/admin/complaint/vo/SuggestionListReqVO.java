package com.rs.module.pam.controller.admin.complaint.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 监所事务管理-投诉建议列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SuggestionListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("监室ID")
    private String roomId;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("投诉类型")
    private String complaintType;

    @ApiModelProperty("投诉内容")
    private String complaintContent;

    @ApiModelProperty("处理状态")
    private String handleStatus;

    @ApiModelProperty("处理人身份证号")
    private String handleUserSfzh;

    @ApiModelProperty("处理人姓名")
    private String handleUserName;

    @ApiModelProperty("处理反馈")
    private String handleFeedback;

}
