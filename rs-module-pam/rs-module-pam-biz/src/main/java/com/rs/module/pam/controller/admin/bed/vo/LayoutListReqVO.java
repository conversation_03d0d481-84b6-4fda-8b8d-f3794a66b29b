package com.rs.module.pam.controller.admin.bed.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 监所事务管理-监室床位布局列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LayoutListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("布局名称")
    private String layoutName;

    @ApiModelProperty("布局照片地址")
    private String layoutImageUrl;

    @ApiModelProperty("布局配置")
    private String layoutConfig;

    @ApiModelProperty("排序")
    private Integer sort;

}
