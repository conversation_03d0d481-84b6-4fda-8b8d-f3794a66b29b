package com.rs.module.pam.entity.family;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-亲情电话 DO
 *
 * <AUTHOR>
 */
@TableName("pam_family_phone_number")
@KeySequence("pam_family_phone_number_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_family_phone_number")
public class FamilyPhoneDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 数据来源（字典：ZD_DATA_SOURCES）
     */
    private String dataSources;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 联系对象
     */
    private String telObject;
    /**
     * 与通话人员关系
     */
    private String relName;
    /**
     * 电话号码
     */
    private String telNum;
    /**
     * 申请时间
     */
    private Date applyTime;
    /**
     * 通话日期
     */
    private Date telTime;
    /**
     * 经办人身份证号
     */
    private String handlerUserSfzh;
    /**
     * 经办人
     */
    private String handlerUserName;
    /**
     * 经办时间
     */
    private Date handlerTime;
    /**
     * 报备状态（字典：ZD_XXBB_BBZT）
     */
    private String status;
    /**
     * 审批人身份证号
     */
    private String approverSfzh;
    /**
     * 审批人姓名
     */
    private String approverXm;
    /**
     * 审批时间
     */
    private Date approverTime;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 审核意见
     */
    private String approvalComments;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

}
