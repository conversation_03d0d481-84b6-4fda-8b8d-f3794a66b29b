package com.rs.module.pam.controller.admin.cook.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 监所事务管理-菜品模板列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TemplateListReqVO extends BaseVO {

    @ApiModelProperty("模板名称")
    private String templateName;
    @ApiModelProperty("监所编号")
    private String orgCode;
    /**
     * 周编号 比如1月第一周 20230101
     */
    private String weekNo;
    /**
     * 是否复制
     */
    private String isCopy;

}
