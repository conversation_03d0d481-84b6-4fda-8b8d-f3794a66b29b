package com.rs.module.pam.service.represent;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.represent.vo.CheckListReqVO;
import com.rs.module.pam.controller.admin.represent.vo.CheckPageReqVO;
import com.rs.module.pam.controller.admin.represent.vo.CheckSaveReqVO;
import com.rs.module.pam.dao.represent.CheckDao;
import com.rs.module.pam.entity.represent.CheckDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 监所事务管理-人脸库及人员库信息进行比对 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CheckServiceImpl extends BaseServiceImpl<CheckDao, CheckDO> implements CheckService {

    @Resource
    private CheckDao checkDao;

    @Override
    public String createCheck(CheckSaveReqVO createReqVO) {
        // 插入
        CheckDO check = BeanUtils.toBean(createReqVO, CheckDO.class);
        checkDao.insert(check);
        // 返回
        return check.getId();
    }

    @Override
    public void updateCheck(CheckSaveReqVO updateReqVO) {
        // 校验存在
        validateCheckExists(updateReqVO.getId());
        // 更新
        CheckDO updateObj = BeanUtils.toBean(updateReqVO, CheckDO.class);
        checkDao.updateById(updateObj);
    }

    @Override
    public void deleteCheck(String id) {
        // 校验存在
        validateCheckExists(id);
        // 删除
        checkDao.deleteById(id);
    }

    private void validateCheckExists(String id) {
        if (checkDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-人脸库及人员库信息进行比对数据不存在");
        }
    }

    @Override
    public CheckDO getCheck(String id) {
        return checkDao.selectById(id);
    }

    @Override
    public PageResult<CheckDO> getCheckPage(CheckPageReqVO pageReqVO) {
        return checkDao.selectPage(pageReqVO);
    }

    @Override
    public List<CheckDO> getCheckList(CheckListReqVO listReqVO) {
        return checkDao.selectList(listReqVO);
    }


}
