package com.rs.module.pam.service.screen.task;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.pam.dao.screen.GjLargeScreenDao;

import java.lang.reflect.Method;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

public class CswgTask implements Callable<JSONObject> {

    private GjLargeScreenDao gjLargeScreenDao;

    private String methodName;

    private String code;

    private String codeType;

    private CountDownLatch countDownLatch;

    public CswgTask(GjLargeScreenDao gjLargeScreenDao, String methodName, String code, String codeType, CountDownLatch countDownLatch) {
        this.gjLargeScreenDao = gjLargeScreenDao;
        this.methodName = methodName;
        this.code = code;
        this.codeType = codeType;
        this.countDownLatch = countDownLatch;
    }

    @Override
    public JSONObject call() {
        int result = 0;
        try {
            Method method = gjLargeScreenDao.getClass().getMethod(methodName, String.class, String.class);
            result = (Integer) method.invoke(gjLargeScreenDao, code, codeType);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            countDownLatch.countDown();
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(methodName, result);
        return jsonObject;
    }
}
