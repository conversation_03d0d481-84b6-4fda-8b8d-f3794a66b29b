package com.rs.module.pam.controller.admin.library.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-图书借阅处理 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LibraryBorrowingHandleRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("借阅id")
    private String borrowingId;
    @ApiModelProperty("借阅状态（字典：ZD_TSJY_JYZT）")
    private String status;
    @ApiModelProperty("处理人id")
    private String handleUserSfzh;
    @ApiModelProperty("处理人姓名")
    private String handleUser;
    @ApiModelProperty("处理时间")
    private Date handleTime;
}
