package com.rs.module.pam.entity.psy;

import lombok.*;

import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-心理测评量表管理 DO
 *
 * <AUTHOR>
 */
@TableName("pam_psy_eval_table")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_psy_eval_table")
public class EvalTableDO extends BaseDO {
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 量表名称
     */
    private String name;
    /**
     * 量表类型（字典：ZD_XLPC_LBLX）
     */
    private String tableType;
    /**
     * 描述
     */
    private String description;
    /**
     * 总题量
     */
    private Integer totalQuestionNumber;
    /**
     * 预计时长，单位分钟
     */
    private Integer estimatedDuration;
    /**
     * 计分规则
     */
    private String scoreRule;
    /**
     * 自定义计分规则
     */
    private String customScoreRule;
    /**
     * 结果解释规则
     */
    private String resultInterpRule;
    /**
     * 使用状态
     */
    private String usageStatus;
    /**
     * 量表编号
     */
    private String tableNo;
}
