package com.rs.module.pam.entity.duty.day;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-值日组 DO
 *
 * <AUTHOR>
 */
@TableName("pam_day_duty_group")
@KeySequence("pam_day_duty_group_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_day_duty_group")
public class DayDutyGroupDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 值班组名（规则：两个被监管人员的姓名的首字母组合）
     */
    private String groupName;
    /**
     * 值班组序号，监室下自增
     */
    private Integer groupNo;
    /**
     * 监管人员编码
     */
    private String jgrybm1;
    /**
     * 监管人员编码
     */
    private String jgrybm2;

}
