package com.rs.module.pam.controller.admin.bed.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-监室床位配置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ConfigRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("机构编号")
    private String orgCode;
    @ApiModelProperty("监室id")
    private String roomId;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("布局ID")
    private String layoutId;
    @ApiModelProperty("布局类型")
    private String layoutType;
    @ApiModelProperty("布局名称")
    private String layoutName;
    @ApiModelProperty("布局背景图")
    private String layoutUrl;
    @ApiModelProperty("布局宽度")
    private Integer layoutWidth;
    @ApiModelProperty("布局高度")
    private Integer layoutHeight;
    @ApiModelProperty("布局行数")
    private Integer layoutRow;
    @ApiModelProperty("布局列数")
    private Integer layoutColumn;
    @ApiModelProperty("布局信息")
    private List<BedAreaConfigVO> layoutConfigs;
    @ApiModelProperty("调整人")
    private String changeUserName;
    @ApiModelProperty("调整时间")
    private Date changeDate;
    @ApiModelProperty("关押人数")
    private Integer PrisonerNum;
    @ApiModelProperty("床位规格长度")
    private Integer bedSpaceLength;
    @ApiModelProperty("床位规格宽度")
    private Integer bedSpaceWidth;
    @ApiModelProperty("床位规格高度")
    private Integer bedSpaceHeight;
    @ApiModelProperty("床位规格承重")
    private BigDecimal bedSpaceBearing;
    @ApiModelProperty("自动床位配置（字典：ZD_CWGL_ZDCWPZ）")
    private String bedAutoConfig;
    @ApiModelProperty("未安排床位人员信息")
    private List<PrisonerBedDetailsVO> notPlan;
}
