package com.rs.module.pam.service.religion;

import com.alibaba.fastjson.JSONObject;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.enums.DataSourceAppEnum;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.pam.enums.ReligionApplyStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.pam.controller.admin.religion.vo.*;
import com.rs.module.pam.entity.religion.ReligionApplyDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.pam.dao.religion.ReligionApplyDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 监所事务管理-宗教申请 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ReligionApplyServiceImpl extends BaseServiceImpl<ReligionApplyDao, ReligionApplyDO> implements ReligionApplyService {

    @Resource
    private ReligionApplyDao religionApplyDao;

    // 单位领导审批流程
    private static final String defKey = "yonghudanweilindao";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createReligionApply(ReligionApplySqReqVO createReqVO) {
        // 插入
        ReligionApplyDO religionApply = BeanUtils.toBean(createReqVO, ReligionApplyDO.class);
        religionApply.setDataSources(DataSourceAppEnum.ACP.getCode());
        religionApply.setStatus(ReligionApplyStatusEnum.DSH.getCode());
        religionApplyDao.insert(religionApply);

        //启动流程审批
        String msgUrl = "/#/discipline/religiousWorship";
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", religionApply.getId());
        JSONObject result = BspApprovalUtil.defaultStartProcess(HttpUtils.getAppCode() + "-" + defKey, religionApply.getId(), "宗教礼拜流程审批", msgUrl, variables);
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            religionApply.setActInstId(bpmTrail.getString("actInstId"));
            religionApply.setTaskId(bpmTrail.getString("taskId"));
            religionApplyDao.updateById(religionApply);
        } else {
            throw new ServerException("流程启动失败");
        }
        return religionApply.getId();
    }

    @Override
    public void updateReligionApply(ReligionApplySaveReqVO updateReqVO) {
        // 校验存在
        validateReligionApplyExists(updateReqVO.getId());
        // 更新
        ReligionApplyDO updateObj = BeanUtils.toBean(updateReqVO, ReligionApplyDO.class);
        religionApplyDao.updateById(updateObj);
    }

    @Override
    public void deleteReligionApply(String id) {
        // 校验存在
        validateReligionApplyExists(id);
        // 删除
        religionApplyDao.deleteById(id);
    }

    private void validateReligionApplyExists(String id) {
        if (religionApplyDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-宗教申请数据不存在");
        }
    }

    @Override
    public ReligionApplyDO getReligionApply(String id) {
        return religionApplyDao.selectById(id);
    }

    @Override
    public PageResult<ReligionApplyDO> getReligionApplyPage(ReligionApplyPageReqVO pageReqVO) {
        return religionApplyDao.selectPage(pageReqVO);
    }

    @Override
    public List<ReligionApplyDO> getReligionApplyList(ReligionApplyListReqVO listReqVO) {
        return religionApplyDao.selectList(listReqVO);
    }

    @Override
    public void approval(ReligionApplyApprovalReqVO applyApprovalReqVO) {
        ReligionApplyDO religionApplyDO = religionApplyDao.selectById(applyApprovalReqVO.getId());
        if (Objects.isNull(religionApplyDO)) {
            throw new ServerException("监所事务管理-宗教申请数据不存在");
        }
        if (!ReligionApplyStatusEnum.DSH.getCode().equals(religionApplyDO.getStatus())) {
            throw new ServerException("非待审批状态，不能进行审核操作");
        }
        religionApplyDO.setStatus(ReligionApplyStatusEnum.getByCode(applyApprovalReqVO.getStatus()).getCode());
        religionApplyDO.setApproverTime(new Date());
        religionApplyDO.setApprovalComments(applyApprovalReqVO.getApprovalComments());
        religionApplyDO.setApprovalResult(applyApprovalReqVO.getStatus());
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        religionApplyDO.setApproverSfzh(sessionUser.getIdCard());
        religionApplyDO.setApproverXm(sessionUser.getName());
        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(religionApplyDO.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }

        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", religionApplyDO.getId());
        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getId());
        nowApproveUser.put("name", sessionUser.getName());
        BspApproceStatusEnum bspApproceStatusEnum = ReligionApplyStatusEnum.SHTG.getCode().equals(religionApplyDO.getStatus()) ?
                BspApproceStatusEnum.PASSED_END : BspApproceStatusEnum.NOT_PASSED_END;
        JSONObject result = BspApprovalUtil.approvalProcess(HttpUtils.getAppCode() + "-" + defKey,
                religionApplyDO.getActInstId(), religionApplyDO.getTaskId(), religionApplyDO.getId(),
                bspApproceStatusEnum, religionApplyDO.getApprovalComments(), null, null, true,
                variables, nowApproveUser, "acp");
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            religionApplyDO.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程启动失败");
        }
        religionApplyDao.updateById(religionApplyDO);

    }


}
