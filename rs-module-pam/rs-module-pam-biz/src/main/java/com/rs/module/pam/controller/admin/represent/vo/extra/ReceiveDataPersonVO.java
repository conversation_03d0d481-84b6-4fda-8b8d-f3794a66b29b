package com.rs.module.pam.controller.admin.represent.vo.extra;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("接收离线数据-在押人员点名情况")
public class ReceiveDataPersonVO {
    @ApiModelProperty("人员编号")
    private String prisonerId;

    @ApiModelProperty("签到状态 1-签到")
    private String signStatus;

    @ApiModelProperty("签到时间")
    private Date signTime;

    @ApiModelProperty("温度-有就传")
    private String temperature;
}
