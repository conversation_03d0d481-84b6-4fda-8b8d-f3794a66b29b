package com.rs.module.pam.controller.admin.complaint.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 监所事务管理-投诉建议新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SuggestionSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("监室ID")
    @NotEmpty(message = "监室ID不能为空")
    private String roomId;

    @ApiModelProperty("监室名称")
    @NotEmpty(message = "监室名称不能为空")
    private String roomName;

    @ApiModelProperty("投诉类型 字典：ZD_TSJYLX")
    @NotEmpty(message = "投诉类型不能为空")
    private String complaintType;

    @ApiModelProperty("投诉内容 字典：ZD_TSJY_01，ZD_TSJY_02  存储字典name值")
    @NotEmpty(message = "投诉内容不能为空")
    private String complaintContent;


}
