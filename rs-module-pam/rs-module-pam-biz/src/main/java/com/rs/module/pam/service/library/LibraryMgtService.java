package com.rs.module.pam.service.library;

import java.util.*;
import javax.validation.*;
import com.rs.module.pam.controller.admin.library.vo.*;
import com.rs.module.pam.entity.library.LibraryMgtDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 监所事务管理-图书管理 Service 接口
 *
 * <AUTHOR>
 */
public interface LibraryMgtService extends IBaseService<LibraryMgtDO>{

    /**
     * 创建监所事务管理-图书管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createLibraryMgt(@Valid LibraryMgtSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-图书管理
     *
     * @param updateReqVO 更新信息
     */
    void updateLibraryMgt(@Valid LibraryMgtSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-图书管理
     *
     * @param id 编号
     */
    void deleteLibraryMgt(String id);

    /**
     * 获得监所事务管理-图书管理
     *
     * @param id 编号
     * @return 监所事务管理-图书管理
     */
    LibraryMgtDO getLibraryMgt(String id);

    /**
    * 获得监所事务管理-图书管理分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-图书管理分页
    */
    PageResult<LibraryMgtDO> getLibraryMgtPage(LibraryMgtPageReqVO pageReqVO);

    /**
    * 获得监所事务管理-图书管理列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-图书管理列表
    */
    List<LibraryMgtDO> getLibraryMgtList(LibraryMgtListReqVO listReqVO);


    void updateBorrowedInventory(String libraryId, int livraryCount);

    PageResult<LibraryMgtDO> getAppLibraryMgtPage(int pageNo, int pageSize, String orgCode);

    void shelvingAndDelisting(LibraryMgtSXJReqVO updateReqVO);
}
