package com.rs.module.pam.controller.admin.cook.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 监所事务管理-菜品分类 Response VO")
@Data
public class CateRespVO {
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("菜品分类名称")
    private String cateName;
    @ApiModelProperty("分类颜色")
    private String cateColor;
}
