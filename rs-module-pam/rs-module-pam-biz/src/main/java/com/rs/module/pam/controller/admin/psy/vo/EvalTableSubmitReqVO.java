package com.rs.module.pam.controller.admin.psy.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@ApiModel(description = "管理后台 - 心理测评量-新增/修改 Request VO")
@Data
public class EvalTableSubmitReqVO {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("计分规则(1:总分计分规则, 2:自定义计分规则, 3:不计分)")
    private String scoreRule;

    @ApiModelProperty("自定义计分规则")
    private String customScoreRule;

    @ApiModelProperty(value = "结果解释规则")
    private List<ResultRule> resultInterpRules;

    @Getter
    @Setter
    public static class ResultRule {
        @ApiModelProperty(value = "最小")
        private int min;
        @ApiModelProperty(value = "最大")
        private int max;
        @ApiModelProperty(value = "结果解释")
        private String result;
    }

}
