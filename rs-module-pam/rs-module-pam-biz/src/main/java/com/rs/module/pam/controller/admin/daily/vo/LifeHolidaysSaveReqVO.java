package com.rs.module.pam.controller.admin.daily.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-一日生活制度节假日新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LifeHolidaysSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("节假日日期")
    @NotNull(message = "节假日日期不能为空")
    private Date holidaysDate;

    @ApiModelProperty("节假日日期")
    private String holidaysName;

}
