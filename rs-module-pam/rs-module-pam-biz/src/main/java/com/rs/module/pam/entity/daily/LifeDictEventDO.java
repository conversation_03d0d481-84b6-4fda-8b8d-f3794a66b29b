package com.rs.module.pam.entity.daily;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-事务选项字典 DO
 *
 * <AUTHOR>
 */
@TableName("pam_daily_life_dict_event")
@KeySequence("pam_daily_life_dict_event_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LifeDictEventDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * event_name
     */
    private String eventName;
    /**
     * 夹控标记（字典：ZD_CWGL_JKBJ）
     */
    private Short eventStatus;

}
