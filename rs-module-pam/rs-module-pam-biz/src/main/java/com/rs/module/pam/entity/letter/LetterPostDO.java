package com.rs.module.pam.entity.letter;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-家属通信-寄信登记 DO
 *
 * <AUTHOR>
 */
@TableName("pam_letter_post")
@KeySequence("pam_letter_post_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_letter_post")
public class LetterPostDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 数据来源（字典：ZD_DATA_SOURCES）
     */
    private String dataSources;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 收信人姓名
     */
    private String receiveMailUser;
    /**
     * 关系
     */
    private String relation;
    /**
     * 收信地址
     */
    private String receiveAddress;
    /**
     * 来信日期
     */
    private Date sendTime;
    /**
     * 收信单位
     */
    private String receivePrison;
    /**
     * 信件邮编
     */
    private String mailNo;
    /**
     * 备注
     */
    private String remark;
    /**
     * 申请时间
     */
    private Date applyTime;
    /**
     * 签名图片
     */
    private String signUrl;
    /**
     * 信件状态 （字典：ZD_JSTX_JXZT）
     */
    private String status;
    /**
     * 审批状态（字典：ZD_JSTX_JXSPZT）
     */
    private String approverStatus;
    /**
     * 转交人
     */
    private String passUser;
    /**
     * 转交时间
     */
    private Date passTime;
    /**
     * 转交备注
     */
    private String passRemark;
    /**
     * 管教审批人身份证号
     */
    private String gjApproverSfzh;
    /**
     * 管教审批人姓名
     */
    private String gjApproverXm;
    /**
     * 管教审批时间
     */
    private Date gjApproverTime;
    /**
     * 管教审批结果
     */
    private String gjApprovalResult;
    /**
     * 管教审核意见
     */
    private String gjApprovalComments;
    /**
     * 科组长审批人身份证号
     */
    private String groupApproverSfzh;
    /**
     * 科组长审批人姓名
     */
    private String groupApproverXm;
    /**
     * 科组长审批时间
     */
    private Date groupApproverTime;
    /**
     * 科组长审批结果
     */
    private String groupApprovalResult;
    /**
     * 科组长审核意见
     */
    private String groupApprovalComments;
    /**
     * 所领导审批人身份证号
     */
    private String leaderApproverSfzh;
    /**
     * 所领导审批人姓名
     */
    private String leaderApproverXm;
    /**
     * 所领导审批时间
     */
    private Date leaderApproverTime;
    /**
     * 所领导审批结果
     */
    private String leaderApprovalResult;
    /**
     * 所领导审核意见
     */
    private String leaderApprovalComments;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

}
