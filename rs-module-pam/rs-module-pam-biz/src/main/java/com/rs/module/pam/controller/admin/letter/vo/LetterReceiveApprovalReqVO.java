package com.rs.module.pam.controller.admin.letter.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-家属通信-收信登记审批 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LetterReceiveApprovalReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    @NotEmpty(message = "主键id不能为空")
    private String id;

    @ApiModelProperty("审批结果 （字典：ZD_BPM_APPROVE_STATUS） 5：同意  2：不同意")
    @NotNull(message = "审批结果不能为空")
    private Short result;

    @ApiModelProperty("审核意见")
    private String approvalComments;

}
