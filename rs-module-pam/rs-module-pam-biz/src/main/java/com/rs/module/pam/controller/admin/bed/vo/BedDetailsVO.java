package com.rs.module.pam.controller.admin.bed.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 监所事务管理-监室布局详情 Response VO")
@Data
public class BedDetailsVO {


    @ApiModelProperty("床位号")
    private String cwh;

    @ApiModelProperty("是否为实体床位")
    private Boolean isEntity;

    @ApiModelProperty("床位行数")
    private Integer row;

    @ApiModelProperty("床位列数")
    private Integer column;


}
