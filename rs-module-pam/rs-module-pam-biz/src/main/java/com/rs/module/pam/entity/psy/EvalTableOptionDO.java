package com.rs.module.pam.entity.psy;

import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-心理测评量表关联提目选项 DO
 *
 * <AUTHOR>
 */
@TableName("pam_psy_eval_table_option")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_psy_eval_table_option")
public class EvalTableOptionDO extends BaseDO {
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 评量表ID
     */
    private String tableId;
    /**
     * 题目id
     */
    private String questionId;
    /**
     * 选项代码，A、B、C、D等
     */
    private String optionCode;
    /**
     * 选项内容
     */
    private String optionText;
    /**
     * 题目选项分值
     */
    private BigDecimal score;
    /**
     * 选项顺序
     */
    private Integer sortOrder;

}
