package com.rs.module.pam.service.cook;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.cache.RedisClient;
import com.bsp.common.cache.RedisEnum;
import com.bsp.common.cache.RedisUtils;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.util.SessionUserUtil;
import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.SocketService;
import com.rs.framework.common.core.KeyValue;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.pam.controller.admin.cook.vo.DeliveryRespVO;
import com.rs.module.pam.controller.app.vo.AppDeliveryPrisonerListVO;
import com.rs.module.pam.dao.cook.DeliveryDao;
import com.rs.module.pam.entity.cook.DeliveryDO;
import com.rs.module.pam.entity.cook.DeliveryRecordDO;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 监所事务管理-发饭主 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DeliveryServiceImpl extends BaseServiceImpl<DeliveryDao, DeliveryDO> implements DeliveryService {

    @Value("${system-mark}")
    private String systemMark;
    @Resource
    private DeliveryDao deliveryDao;
    @Resource
    private PrisonerService prisonerService;
    @Resource
    private TemplateService templateService;
    @Resource
    private SpecialApplyService specialApplyService;
    @Resource
    private DeliveryRecordService deliveryRecordService;
    @Resource
    private SocketService socketService;

    @Override
    public DeliveryDO getDelivery(String id) {
        return deliveryDao.selectById(id);
    }

    @Override
    public AppDeliveryPrisonerListVO getDeliveryPrisonerListVO(String deliveryId) {
        DeliveryDO deliveryDO = deliveryDao.selectById(deliveryId);
        AppDeliveryPrisonerListVO appDeliveryPrisonerListVO = new AppDeliveryPrisonerListVO();
        List<AppDeliveryPrisonerListVO.DeliveryPrisonerList> noDeliveryPrisonerList = new ArrayList<>();
        List<AppDeliveryPrisonerListVO.DeliveryPrisonerList> deliveryPrisonerList = new ArrayList<>();
        int toDoTotal = 0;
        int doneTotal = 0;
        Map<String, Set<String>> jgrybmToBusinessTypeSetMap = getJgrybmToBusinessTypeSet(deliveryDO.getRoomId());
        List<PrisonerInDO> personList = getPrisonerInList(deliveryDO.getOrgCode(), deliveryDO.getRoomId());
        if (personList != null) {
            List<String> jgrybms = personList.stream().map(PrisonerInDO::getJgrybm).collect(Collectors.toList());
            Map<String, String> mealTypeMap = getMealTypeMap(jgrybms, deliveryDO.getMealPeriod());
            Map<String, DeliveryRecordDO> recordByCondition = deliveryRecordService.getDeliveryRecordByCondition(jgrybms, deliveryId);
            String xbDicKey = RedisUtils.buildKey(RedisEnum.COM_DIC, new String[]{systemMark, "ZD_XB"});
            Map<String, String> xbDicMap = RedisClient.hgetAll(xbDicKey);
            for (PrisonerInDO person : personList) {
                AppDeliveryPrisonerListVO.DeliveryPrisonerList prisonerList = new AppDeliveryPrisonerListVO.DeliveryPrisonerList();
                prisonerList.setXm(person.getXm());
                prisonerList.setJgrybm(person.getJgrybm());
                prisonerList.setXb(person.getXb());
                prisonerList.setXbName(xbDicMap.get(person.getXb()));
                prisonerList.setFrontPhoto(person.getFrontPhoto());
                String mealType = mealTypeMap.get(person.getJgrybm());
                prisonerList.setMealType(mealType);
                if (person.getCsrq() != null) {
                    //计算年龄
                    try {
                        prisonerList.setAge(new Date().getYear() - person.getCsrq().getYear());
                    } catch (Exception e) {
                        log.error("计算年龄失败", e);
                    }
                }
                Set<String> strings = jgrybmToBusinessTypeSetMap.get(person.getJgrybm());
                if (CollUtil.isNotEmpty(strings)) {
                    prisonerList.setOutRemark(StrUtil.join(",", strings));
                }
                DeliveryRecordDO recordDO = recordByCondition.get(person.getJgrybm());
                if (recordDO == null) {
                    toDoTotal++;
                    prisonerList.setStatus("0");
                    noDeliveryPrisonerList.add(prisonerList);
                } else if ("2".equals(recordDO.getStatus())) {
                    toDoTotal++;
                    prisonerList.setStatus("2");
                    noDeliveryPrisonerList.add(prisonerList);
                } else if ("1".equals(recordDO.getStatus())) {
                    doneTotal++;
                    prisonerList.setStatus("1");
                    deliveryPrisonerList.add(prisonerList);
                }
            }
        }
        appDeliveryPrisonerListVO.setDeliveryPrisonerList(deliveryPrisonerList);
        appDeliveryPrisonerListVO.setNoDeliveryPrisonerList(noDeliveryPrisonerList);
        appDeliveryPrisonerListVO.setToDoTotal(toDoTotal);
        appDeliveryPrisonerListVO.setDoneTotal(doneTotal);
        appDeliveryPrisonerListVO.setDeliveryStatus(deliveryDO.getStatus());
        return appDeliveryPrisonerListVO;
    }

    @NotNull
    private Map<String, String> getMealTypeMap(List<String> jgrybms, String mealPeriod) {
        List<Map<String, Object>> mealTypes = specialApplyService.getMealType(jgrybms, mealPeriod);
        String dicKey = RedisUtils.buildKey(RedisEnum.COM_DIC, new String[]{systemMark, "ZD_PCGL_PCLX"});
        Map<String, String> dicMap = RedisClient.hgetAll(dicKey);
        Map<String, String> mealTypeMap = mealTypes.stream().collect(Collectors
                .toMap(m -> (String) m.get("jgrybm"), m -> dicMap.get(m.get("meal_type"))));
        return mealTypeMap;
    }

    @NotNull
    private Map<String, Set<String>> getJgrybmToBusinessTypeSet(String roomId) {
        List<Map<String, String>> resultMaps = deliveryDao.selectBusinessTypeCount(roomId);
        Map<String, Set<String>> jgrybmToBusinessTypeSet = new HashMap<>();
        String dicKey = RedisUtils.buildKey(RedisEnum.COM_DIC, new String[]{"ZD_GJXZDCSY"});
        Map<String, String> dicMap = RedisClient.hgetAll(dicKey);
        for (Map<String, String> map : resultMaps) {
            String businessType = map.get("business_type");
            String jgrybm = map.get("jgrybm");
            if (businessType == null || jgrybm == null || StrUtil.isBlank(businessType)) {
                continue; // 可选：跳过空值处理
            }
            String businessTypeName = dicMap.get(businessType);
            jgrybmToBusinessTypeSet.computeIfAbsent(jgrybm, k -> new HashSet<>())
                    .add(businessTypeName);
        }
        return jgrybmToBusinessTypeSet;
    }

    @Override
    public List<AppDeliveryPrisonerListVO.DeliveryPrisonerList> getNpDeliveryPrisonerListVO(String deliveryId) {
        List<AppDeliveryPrisonerListVO.DeliveryPrisonerList> resultList = new ArrayList<>();
        DeliveryDO deliveryDO = deliveryDao.selectById(deliveryId);
        Map<String, Set<String>> jgrybmToBusinessTypeSetMap = getJgrybmToBusinessTypeSet(deliveryDO.getRoomId());
        List<PrisonerInDO> personList = getPrisonerInList(deliveryDO.getOrgCode(), deliveryDO.getRoomId());
        if (personList != null) {
            List<String> jgrybms = personList.stream().map(PrisonerInDO::getJgrybm).collect(Collectors.toList());
            Map<String, String> mealTypeMap = getMealTypeMap(jgrybms, deliveryDO.getMealPeriod());
            Map<String, DeliveryRecordDO> recordByConditionMap = deliveryRecordService.getDeliveryRecordByCondition(jgrybms, deliveryId);
            for (PrisonerInDO person : personList) {
                AppDeliveryPrisonerListVO.DeliveryPrisonerList list = new AppDeliveryPrisonerListVO.DeliveryPrisonerList();
                list.setXm(person.getXm());
                list.setJgrybm(person.getJgrybm());
                list.setFrontPhoto(person.getFrontPhoto());
                Set<String> strings = jgrybmToBusinessTypeSetMap.get(person.getJgrybm());
                if (CollUtil.isNotEmpty(strings)) {
                    list.setOutRemark(StrUtil.join(",", strings));
                }
                list.setMealType(mealTypeMap.get(person.getJgrybm()));
                DeliveryRecordDO recordDO = recordByConditionMap.get(person.getJgrybm());
                if (recordDO == null) {
                    list.setStatus("0");
                } else if ("2".equals(recordDO.getStatus())) {
                    list.setStatus("2");
                } else if ("1".equals(recordDO.getStatus())) {
                    list.setStatus("1");
                    list.setFaceTime(DateUtil.format(recordDO.getDeliveryTime(), "HH:mm:ss"));
                }
                resultList.add(list);
            }
        }
        return resultList;
    }

    @Override
    public DeliveryRespVO confirmDelivery(String deliveryId, String orgCode, String roomId) {
        DeliveryDO deliveryDO = null;
        if (StrUtil.isNotBlank(deliveryId)) {
            deliveryDO = deliveryDao.selectById(deliveryId);
        } else {
            Date date = new Date();
            KeyValue<String, String> mealPeriod = getMealPeriod(date);
            String cookBookDate = DateUtil.format(date, "yyyy-MM-dd");
            deliveryDO = deliveryDao.selectDeliveryByUnniQue(cookBookDate, roomId, orgCode, mealPeriod.getKey());
        }
        if (deliveryDO == null) {
            throw new ServerException("不在发饭时间范围内");
        }
        if ("1".equals(deliveryDO.getStatus())) {
            throw new ServerException("已结束发放");
        }
        DeliveryRespVO deliveryRespVO = new DeliveryRespVO();
        BeanUtils.copyProperties(deliveryDO, deliveryRespVO);
        return deliveryRespVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sweepFaceDelivery(String deliveryId, String ryId) {
        DeliveryDO deliveryDO = deliveryDao.selectById(deliveryId);
        String jgrybm = prisonerService.getJgrybm(ryId);
        if (StrUtil.isBlank(jgrybm)) {
            jgrybm = ryId;
        }
        DeliveryRecordDO deliveryRecordDO = deliveryRecordService.getDeliveryRecordByDeliveryId(deliveryId, jgrybm);
        if (deliveryRecordDO == null) {
            deliveryRecordDO = new DeliveryRecordDO();
            deliveryRecordDO.setDeliveryId(deliveryId);
            deliveryRecordDO.setJgrybm(jgrybm);
            deliveryRecordDO.setCookbook(deliveryDO.getCookbook());
            deliveryRecordDO.setDeliveryTime(new Date());
            deliveryRecordDO.setStatus("1");
            deliveryRecordDO.setCityName(deliveryDO.getCityName());
            deliveryRecordDO.setCityCode(deliveryDO.getCityCode());
            deliveryRecordDO.setRegName(deliveryDO.getRegName());
            deliveryRecordDO.setRegCode(deliveryDO.getRegCode());
            deliveryRecordDO.setOrgName(deliveryDO.getOrgName());
            deliveryRecordDO.setOrgCode(deliveryDO.getOrgCode());
            deliveryRecordDO.setAddUser(deliveryDO.getAddUser());
            deliveryRecordDO.setAddUserName(deliveryDO.getAddUserName());
            deliveryRecordDO.setAddTime(new Date());
            deliveryRecordDO.setUpdateUser(deliveryDO.getUpdateUser());
            deliveryRecordDO.setUpdateUserName(deliveryDO.getUpdateUserName());
            deliveryRecordDO.setUpdateTime(new Date());
            Map<String, String> mealTypeMap = getMealTypeMap(CollUtil.toList(jgrybm), deliveryDO.getMealPeriod());
            deliveryRecordDO.setMealType(mealTypeMap.get(jgrybm));
        } else {
            deliveryRecordDO.setStatus("1");
            deliveryRecordDO.setDeliveryTime(new Date());
            deliveryRecordDO.setUpdateTime(new Date());
            deliveryRecordDO.setUpdateUser(deliveryDO.getAddUser());
            deliveryRecordDO.setUpdateUserName(deliveryDO.getAddUserName());
        }
        deliveryRecordService.saveOrUpdate(deliveryRecordDO);
        // 更新发放数据上的统计
        deliveryDao.updateDeliveryStatistics(deliveryId);
    }

    @Override
    public DeliveryRespVO getDeliveryRespVO(String id) {
        DeliveryDO deliveryDO = deliveryDao.selectById(id);
        if (deliveryDO == null) {
            throw new ServerException("发饭数据不存在");
        }
        DeliveryRespVO deliveryRespVO = new DeliveryRespVO();
        BeanUtils.copyProperties(deliveryDO, deliveryRespVO);
        List<PrisonerInDO> personList = getPrisonerInList(deliveryDO.getOrgCode(), deliveryDO.getRoomId());
        List<String> jgrybms = personList.stream().map(PrisonerInDO::getJgrybm).collect(Collectors.toList());
        Map<String, DeliveryRecordDO> recordByConditionMap = deliveryRecordService.getDeliveryRecordByCondition(jgrybms, id);
        List<String> noDeliveryPersons = new ArrayList<>(0);
        List<String> receiptPersons = new ArrayList<>(0);
        for (PrisonerInDO person : personList) {
            DeliveryRecordDO recordDO = recordByConditionMap.get(person.getJgrybm());
            if (recordDO == null) {
                noDeliveryPersons.add(person.getXm());
            } else if ("2".equals(recordDO.getStatus())) {
                receiptPersons.add(person.getXm());
            }
        }
        deliveryRespVO.setNoDeliveryPersons(noDeliveryPersons.stream().collect(Collectors.joining(",")));
        deliveryRespVO.setReceiptPersons(receiptPersons.stream().collect(Collectors.joining(",")));
        return deliveryRespVO;
    }

    @Override
    public String startDelivery(String orgCode, String roomId, String roomName) {
        Date date = new Date();
        // 获取用餐时段
        KeyValue<String, String> mealPeriod = getMealPeriod(date);
        String cookBookDate = DateUtil.format(date, "yyyy-MM-dd");
        DeliveryDO deliveryDO = deliveryDao.selectDeliveryByUnniQue(cookBookDate, roomId, orgCode, mealPeriod.getKey());
        if (deliveryDO == null) {
            deliveryDO = new DeliveryDO();
            deliveryDO.setCookBookDate(cookBookDate);
            deliveryDO.setRoomId(roomId);
            deliveryDO.setRoomName(roomName);
            deliveryDO.setDeliveryTime(date);
            deliveryDO.setOperatePoliceSfzh(SessionUserUtil.getSessionUser().getIdCard());
            deliveryDO.setOperatePolice(SessionUserUtil.getSessionUser().getName());
            deliveryDO.setOperateTime(date);
            deliveryDO.setStatus("0");
            deliveryDO.setMealReservedTotal(0);
            deliveryDO.setMealPeriod(mealPeriod.getKey());
            deliveryDO.setRoomTotalPersons(0);
            deliveryDO.setOutgoingTotalPersons(0);
            deliveryDO.setCustodyTotalPersons(0);
            deliveryDO.setMealReceivedTotal(0);
            deliveryDO.setMealUnreceivedTotal(0);
            deliveryDO.setOrgCode(orgCode);
            if (!orgCode.equals(SessionUserUtil.getSessionUser().getOrgCode())) {
                deliveryDO.setOrgCode(orgCode);
            }
            deliveryDO.setCookbook("");
            deliveryDao.insert(deliveryDO);
            final String id = deliveryDO.getId();
            ThreadUtil.execute(() -> {
                // 获取菜谱内容
                DeliveryDO update = new DeliveryDO();
                update.setId(id);
                LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                String mealContent = templateService.getMealContent(localDate, orgCode, mealPeriod.getKey());
                update.setCookbook(mealContent);
                // 统计人数
                List<PrisonerInDO> personList = getPrisonerInList(orgCode, roomId);
                List<Map<String, String>> resultMaps = deliveryDao.selectBusinessTypeCount(roomId);
                Map<String, Set<String>> jgrybmToBusinessTypeSet = new HashMap<>();
                for (Map<String, String> map : resultMaps) {
                    String businessType = map.get("business_type");
                    String jgrybm = map.get("jgrybm");
                    if (businessType == null || jgrybm == null || StrUtil.isBlank(businessType)) {
                        continue; // 可选：跳过空值处理
                    }
                    jgrybmToBusinessTypeSet.computeIfAbsent(businessType, k -> new HashSet<>())
                            .add(jgrybm);
                }
                int outgoingTotalPersons = 0;
                for (Map.Entry<String, Set<String>> entry : jgrybmToBusinessTypeSet.entrySet()) {
                    outgoingTotalPersons += entry.getValue().size();
                }
                update.setRoomTotalPersons(personList.size());
                update.setOutgoingTotalPersons(outgoingTotalPersons);
                update.setCustodyTotalPersons(personList.size() - outgoingTotalPersons);
                update.setMealReceivedTotal(0);
                update.setMealUnreceivedTotal(update.getRoomTotalPersons());
                deliveryDao.updateById(update);
            });
        }
        if (deliveryDO.getStatus().equals("0")) {
            // 开始发放
            // 发送socket消息
            List<String> serialNumbers = socketService.getCnpSerialNumberByRoomId(CollUtil.toList(roomId));
            PushMessageForm pushFrom = new PushMessageForm();
            pushFrom.setSerialNumbers(serialNumbers);
            pushFrom.setTarget(SocketActionConstants.PushMessageTargetEnum.web.name());
            pushFrom.setTerminal(SocketActionConstants.PushMessageTerminalEnum.CNP.name());
            pushFrom.setAction(SocketActionConstants.cook_delivery_task);
            Object[] objects = new Object[1];
            objects[0] = JSONObject.toJSONString(deliveryDO.getId());
            pushFrom.setParams(objects);
            try {
                log.error("向设备:" + serialNumbers + "发起发饭任务,参数:" + pushFrom);
                socketService.pushMessageToSerialNumber(pushFrom);
            } catch (Exception e) {
                log.error("客户端不存在或者离线：", e);
            }
        }
        // 更新发饭
        DeliveryDO updateObj = new DeliveryDO();
        updateObj.setId(deliveryDO.getId());
        updateObj.setStatus("0");
        deliveryDao.updateById(updateObj);
        return deliveryDO.getId();
    }

    private List<PrisonerInDO> getPrisonerInList(String orgCode, String roomId) {
        return prisonerService.getPrisonerInList(orgCode, roomId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void endDelivery(String deliveryId) {
        DeliveryDO deliveryDO = deliveryDao.selectById(deliveryId);
        if (deliveryDO == null) {
            throw new ServerException("发饭数据不存在");
        }
        if (deliveryDO.getStatus().equals("1")) {
            return;
        }
        DeliveryDO updateObj = new DeliveryDO();
        updateObj.setId(deliveryId);
        updateObj.setStatus("1");
        deliveryDao.updateById(updateObj);
        // 发送socket消息
        List<String> serialNumbers = socketService.getCnpSerialNumberByRoomId(CollUtil.toList(deliveryDO.getRoomId()));
        PushMessageForm pushFrom = new PushMessageForm();
        pushFrom.setSerialNumbers(serialNumbers);
        pushFrom.setTarget(SocketActionConstants.PushMessageTargetEnum.web.name());
        pushFrom.setTerminal(SocketActionConstants.PushMessageTerminalEnum.CNP.name());
        pushFrom.setAction(SocketActionConstants.cook_delivery_task_end);
        Object[] objects = new Object[1];
        objects[0] = JSONObject.toJSONString(deliveryDO.getId());
        pushFrom.setParams(objects);
        try {
            log.error("向设备:" + serialNumbers + "发起结束发饭任务,参数:" + pushFrom);
            socketService.pushMessageToSerialNumber(pushFrom);
        } catch (Exception e) {
            log.error("客户端不存在或者离线：", e);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void keepDelivery(String jgrybms, String deliveryId) {
        DeliveryDO deliveryDO = deliveryDao.selectById(deliveryId);
        if (deliveryDO == null) {
            throw new ServerException("发饭数据不存在");
        }
        List<String> arrayList = CollUtil.toList(jgrybms.split(","));
        Map<String, DeliveryRecordDO> deliveryRecordDOMap = deliveryRecordService.getDeliveryRecordByCondition(arrayList, deliveryId);
        List<PrisonerInDO> prisonerInOneList = prisonerService.getPrisonerInOneList(arrayList);
        Map<String, String> mealTypeMap = getMealTypeMap(arrayList, deliveryDO.getMealPeriod());
        Date date = new Date();
        List<DeliveryRecordDO> saveOrUpdateList = new ArrayList<>();
        for (PrisonerInDO prisonerInDO : prisonerInOneList) {
            DeliveryRecordDO deliveryRecordDO = deliveryRecordDOMap.get(prisonerInDO.getJgrybm());
            if (deliveryRecordDO == null) {
                deliveryRecordDO = new DeliveryRecordDO();
                deliveryRecordDO.setDeliveryId(deliveryId);
                deliveryRecordDO.setJgrybm(prisonerInDO.getJgrybm());
                deliveryRecordDO.setMealType(mealTypeMap.get(prisonerInDO.getJgrybm()));
                deliveryRecordDO.setCookbook(deliveryDO.getCookbook());
                deliveryRecordDO.setDeliveryTime(date);
                deliveryRecordDO.setStatus("2");
                saveOrUpdateList.add(deliveryRecordDO);
            }
        }
        deliveryRecordService.saveOrUpdateBatch(saveOrUpdateList);
        // 更新发放数据上的统计
        deliveryDao.updateDeliveryStatistics(deliveryId);
    }

    /**
     * 早餐时间：8：00~10：00
     * 午餐时间：11：00~13：00
     * 晚餐时间：17：00~19：00
     *
     * @param deliveryTime 需要判断的时间
     * @return 返回一个包含时间段的Map，如果不在任何时间段内则返回一个空的Map
     */
    private KeyValue<String, String> getMealPeriod(Date deliveryTime) {
        KeyValue<String, String> result = new KeyValue<>();
        if (isWithinTimeRange(deliveryTime, 8, 0, 10, 0)) {
            result.setKey("0");
            result.setValue("早餐");
        } else if (isWithinTimeRange(deliveryTime, 11, 0, 13, 0)) {
            result.setKey("1");
            result.setValue("午餐");
        } else if (isWithinTimeRange(deliveryTime, 17, 0, 19, 0)) {
            result.setKey("2");
            result.setValue("晚餐");
        } else {
            throw new ServerException("不在发饭时间范围内");
        }
        return result;
    }

    private boolean isWithinTimeRange(Date time, int startHour, int startMinute, int endHour, int endMinute) {
        java.util.Calendar calendar = java.util.Calendar.getInstance();
        calendar.setTime(time);
        int hour = calendar.get(java.util.Calendar.HOUR_OF_DAY);
        int minute = calendar.get(java.util.Calendar.MINUTE);
        return (hour > startHour || (hour == startHour && minute >= startMinute)) &&
                (hour < endHour || (hour == endHour && minute <= endMinute));
    }


}
