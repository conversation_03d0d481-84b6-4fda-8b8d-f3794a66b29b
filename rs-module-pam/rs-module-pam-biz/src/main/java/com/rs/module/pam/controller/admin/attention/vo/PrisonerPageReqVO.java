package com.rs.module.pam.controller.admin.attention.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-重点人员关注登记分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrisonerPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("开始关注日期")
    private Date[] beginTime;

    @ApiModelProperty("结束关注日期")
    private Date[] endTime;

    @ApiModelProperty("关注理由")
    private String attentionReason;

    @ApiModelProperty("关注频率类型（字典：ZD_ZDRYGZ_GZPLLX）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_ZDRYGZ_GZPLLX")
    private String frequencyType;

    @ApiModelProperty("关注频率")
    private Integer frequency;

    @ApiModelProperty("关注层级（字典：ZD_ZDRYGZ_GZCJ）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_ZDRYGZ_GZCJ")
    private String attentionLevel;

    @ApiModelProperty("推送岗位，多个逗号分割")
    private String pushJobPositions;

    @ApiModelProperty("登记状态（字典：ZD_ZDRYGZ_GZZT）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_ZDRYGZ_GZZT")
    private String regStatus;

    @ApiModelProperty("登记经办人")
    private String regOperatorSfzh;

    @ApiModelProperty("登记经办人姓名")
    private String regOperatorXm;

    @ApiModelProperty("登记时间")
    private Date[] regTime;

    @ApiModelProperty("所领导审批人身份证号")
    private String leaderApproverSfzh;

    @ApiModelProperty("所领导审批人姓名")
    private String leaderApproverXm;

    @ApiModelProperty("所领导审批时间")
    private Date[] leaderApproverTime;

    @ApiModelProperty("所领导审批结果")
    private String leaderApprovalResult;

    @ApiModelProperty("所领导审核意见")
    private String leaderApprovalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("排序属性")
    List<OrderItem> orderFields;
}
