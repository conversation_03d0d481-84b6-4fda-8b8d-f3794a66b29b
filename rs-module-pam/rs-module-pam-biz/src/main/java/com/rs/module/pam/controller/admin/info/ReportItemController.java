package com.rs.module.pam.controller.admin.info;

import cn.hutool.core.util.StrUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.info.vo.ReportItemListReqVO;
import com.rs.module.pam.controller.admin.info.vo.ReportItemRespVO;
import com.rs.module.pam.controller.admin.info.vo.ReportItemSaveReqVO;
import com.rs.module.pam.controller.admin.info.vo.ReportItemTreeRespVO;
import com.rs.module.pam.entity.info.ReportItemDO;
import com.rs.module.pam.service.info.ReportItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "信息报备事项配置")
@RestController
@RequestMapping("/pam/info/reportItem")
@Validated
public class ReportItemController {

    @Resource
    private ReportItemService reportItemService;

    @PostMapping("/create")
    @ApiOperation(value = "创建-信息报备事项配置")
    public CommonResult<String> createReportItem(@RequestBody List<ReportItemSaveReqVO> createReqVOS) {
        reportItemService.createReportItem(createReqVOS);
        return success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新-信息报备事项配置")
    public CommonResult<Boolean> updateReportItem(@Valid @RequestBody ReportItemSaveReqVO updateReqVO) {
        reportItemService.updateReportItem(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除-信息报备事项配置")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteReportItem(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            reportItemService.deleteReportItem(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得-信息报备事项配置")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ReportItemRespVO> getReportItem(@RequestParam("id") String id) {
        ReportItemDO reportItem = reportItemService.getReportItem(id);
        return success(BeanUtils.toBean(reportItem, ReportItemRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得-信息报备事项配置列表")
    public CommonResult<List<ReportItemRespVO>> getReportItemList(@RequestBody ReportItemListReqVO listReqVO) {
        if (StrUtil.isBlank(listReqVO.getOrgCode())) {
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        List<ReportItemDO> list = reportItemService.getReportItemList(listReqVO);
        return success(BeanUtils.toBean(list, ReportItemRespVO.class));
    }

    @PostMapping("/listTree")
    @ApiOperation(value = "获得-信息报备事项配置树形结构")
    public CommonResult<List<ReportItemTreeRespVO>> getReportItemListTree(@RequestBody ReportItemListReqVO listReqVO) {
        if (StrUtil.isBlank(listReqVO.getOrgCode())) {
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        listReqVO.setStatus("1");
        List<ReportItemTreeRespVO> treeList = new LinkedList<>();
        List<ReportItemDO> list = reportItemService.getReportItemList(listReqVO);
        Map<String, List<ReportItemDO>> listMap = list.stream().collect(Collectors.groupingBy(ReportItemDO::getReportType));
        listMap.forEach((key, value) -> {
            ReportItemTreeRespVO tree = new ReportItemTreeRespVO();
            tree.setReportType(key);
            tree.setChildren(BeanUtils.toBean(value, ReportItemTreeRespVO.class));
            treeList.add(tree);
        });
        return success(treeList);
    }

    @PostMapping("/updateStatus")
    @ApiOperation(value = "更新-信息报备事项配置状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "编号"),
            @ApiImplicitParam(name = "status", value = "状态（1：启用，0：禁用）")
    })
    public CommonResult updateStatus(@RequestParam("id") String id, @RequestParam("status") String status) {
        reportItemService.updateStatus(id, status);
        return success();
    }

}
