package com.rs.module.pam.controller.admin.represent.vo.extra;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("同步仓内屏 基本信息-在押人员VO")
public class InitBaseDataPersonVO {
    @ApiModelProperty("人员编号")
    private String prisonerId;

    @ApiModelProperty("人员姓名")
    private String prisonerName;

    @ApiModelProperty("人员性别")
    private String sex;

    @ApiModelProperty("0-在监 1-外出")
    private Integer outStatus;

    @ApiModelProperty("外出原因")
    private String outStatusName;

    @ApiModelProperty("人脸照片")
    private String photo;

    @ApiModelProperty("0-来邦 1-中研院")
    private Integer photoType;
}
