package com.rs.module.pam.controller.admin.screen.vo;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管教业务 - 重点关注人员 Response VO")
@Data
@EqualsAndHashCode(callSuper = false)
public class JsswItemRespVO {
    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("监室id")
    private String roomId;

    @ApiModelProperty("事项类型")
    private String busType;

    @ApiModelProperty("添加时间--时分")
    private String hm;

    @ApiModelProperty("监区编码")
    private String areaId;

    @ApiModelProperty("机构编码")
    private String orgCode;

    @ApiModelProperty("添加时间")
    private Date addTime;

    @ApiModelProperty("事项名称")
    private String busName;

    @ApiModelProperty("事项具体内容")
    private JSONObject content;


}
