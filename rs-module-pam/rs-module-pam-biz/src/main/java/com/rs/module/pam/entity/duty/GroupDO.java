package com.rs.module.pam.entity.duty;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

/**
 * 监所事务管理-值班组 DO
 *
 * <AUTHOR>
 */
@TableName("pam_duty_group")
@KeySequence("pam_duty_group_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 值班组名（规则：两个被监管人员的姓名的首字母组合）
     */
    private String groupName;
    /**
     * 值班组序号，监室下自增
     */
    private Integer groupNo;
    /**
     * 监管人员编码
     */
    private String jgrybm1;
    /**
     * 监管人员编码
     */
    private String jgrybm2;

}
