package com.rs.module.pam.controller.admin.bed.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

@ApiModel(description = "管理后台 - 监所事务管理-监室床位配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ConfigUpdateReqVO extends BaseVO{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("机构编号")
    @NotEmpty(message = "机构编号不能为空")
    private String orgCode;

    @ApiModelProperty("监室id")
    @NotEmpty(message = "监室id不能为空")
    private String roomId;

    @ApiModelProperty("自动床位配置（字典：ZD_CWGL_ZDCWPZ）")
    private String bedAutoConfig;

}
