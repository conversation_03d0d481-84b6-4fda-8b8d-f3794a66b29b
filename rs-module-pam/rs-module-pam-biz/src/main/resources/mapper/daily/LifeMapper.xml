<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.daily.LifeDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectLifeListByRoomId" parameterType="string" resultType="com.rs.module.pam.entity.daily.LifeDO">
        SELECT
            a.*
        FROM
            "pam_daily_life" a
            INNER JOIN "pam_daily_life_room" b ON a.ID = b.daily_life_id
        WHERE
            a.is_del=0 and b.is_del = 0 and a.status='02'
            and b.room_id = #{roomId}
            ORDER BY a.approver_time desc
    </select>


</mapper>
