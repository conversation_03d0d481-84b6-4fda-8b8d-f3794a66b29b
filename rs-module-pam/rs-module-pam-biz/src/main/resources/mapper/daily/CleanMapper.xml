<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.daily.CleanDao">

    <select id="getNoCleanRoomIdList" resultType="java.util.Map">
        WITH room_clean_counts AS (
        SELECT
        r.ID AS room_id,
        r.room_code,
        COALESCE ( COUNT ( pdc.ID ), 0 ) AS clean_count
        FROM
        acp_pm_area_prison_room r
        LEFT JOIN pam_daily_clean pdc ON r.room_code = ANY ( STRING_TO_ARRAY( pdc.check_room_id, ',' ) )
        AND pdc.check_type = #{checkType} AND pdc.is_del = '0'
        AND pdc.add_time >= DATE_TRUNC( 'month', CURRENT_DATE )
        AND pdc.add_time &lt; DATE_TRUNC( 'month', CURRENT_DATE ) + INTERVAL '1 month'
        WHERE r.is_del = '0'
        GROUP BY
        r.ID,
        r.room_code
        ) SELECT
        rcc.room_id,
        rcc.clean_count,
        appr.org_code,
        appr.room_name,
        appr.org_name
        FROM
        room_clean_counts rcc LEFT JOIN acp_pm_area_prison_room appr ON rcc.room_id = appr.id
        WHERE
        clean_count &lt;2
    </select>

    <select id="getNoSecurityCheckList" resultType="java.util.Map">
        WITH room_clean_counts AS (
        SELECT
        r.code,
        COALESCE (COUNT ( pdc.ID ), 0) AS clean_count
        FROM
        acp_pm_org r
        LEFT JOIN pam_daily_clean pdc ON r.code = pdc.org_code
        AND pdc.check_type = '2'
        AND pdc.add_time >= DATE_TRUNC( 'month', CURRENT_DATE )
        AND pdc.add_time &lt; DATE_TRUNC( 'month', CURRENT_DATE ) + INTERVAL '1 month'
        GROUP BY
        r.code
        )
        SELECT
        rcc.code AS org_code,
        rcc.clean_count,
        appr.name AS org_name
        FROM
        room_clean_counts rcc left join acp_pm_org appr on rcc.code = appr.code
        WHERE
        clean_count &lt;2
    </select>
</mapper>
