<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.duty.DutyPrisonerDao">

    <select id="getPrisonerInByRoomId" resultType="com.rs.module.pam.controller.admin.duty.vo.DutyPrisonerVO">
        select id as id,
        jgrybm as jgrybm,
        xm as name,
        rssj,
        front_photo as frontPhoto,
        (select id from acp_pm_cnp_face t1 where is_del = '0' and ppi.jgrybm = t1.personnel_code and t1.personnel_type = '2' limit 1) faceId,
        fxdj as riskLevel,
        case when (
            select count(1)
            from ihc_pm_severely_sick_manage t
            where t.business_status = '1'
            and t.jgrybm = ppi.jgrybm
        ) > 0 then true else false end as isSick
        from vw_acp_pm_prisoner_in ppi
        where org_code = #{orgCode}
        and jsh = #{roomId}
        order by rssj asc, xm asc
    </select>

    <select id="getPrisonerInByJgrybms" resultType="com.rs.module.pam.controller.admin.duty.vo.DutyPrisonerVO">
        select id as id,
        jgrybm as jgrybm,
        xm as name,
        rssj,
        front_photo as frontPhoto,
        (select id from acp_pm_cnp_face t1 where is_del = '0' and ppi.jgrybm = t1.personnel_code and t1.personnel_type = '2' limit 1) faceId,
        fxdj as riskLevel,
        ryzt,
        jsh,
        case when (
        select count(1)
        from ihc_pm_severely_sick_manage t
        where t.business_status = '1'
        and t.jgrybm = ppi.jgrybm
        ) > 0 then true else false end as isSick
        from vw_acp_pm_prisoner_in ppi
        where ppi.jgrybm in (
        <foreach collection="jgrybmList" item="jgrybm" separator=",">
            #{jgrybm}
        </foreach>
        )
    </select>

    <select id="getPrisonerByJgrybms" resultType="com.rs.module.pam.controller.admin.duty.vo.DutyPrisonerVO">
        select id as id,
        jgrybm as jgrybm,
        xm as name,
        rssj,
        front_photo as frontPhoto,
        (select id from acp_pm_cnp_face t1 where is_del = '0' and bpi.jgrybm = t1.personnel_code and t1.personnel_type = '2' limit 1) faceId,
        fxdj as riskLevel,
        ryzt,
        jsh,
        case when (
            select count(1)
            from ihc_pm_severely_sick_manage t
            where t.business_status = '1'
            and t.jgrybm = bpi.jgrybm
        ) > 0 then true else false end as isSick
        from vw_acp_pm_prisoner bpi
        where bpi.jgrybm in (
        <foreach collection="jgrybmList" item="jgrybm" separator=",">
            #{jgrybm}
        </foreach>
        )
    </select>

    <select id="selectPrisonerEntryRoomTime" resultType="com.rs.module.pam.controller.admin.duty.vo.DutyPrisonerVO">
        SELECT prc.jgrybm, prc.confirm_time as entry_room_time
        FROM acp_pm_prison_room_change prc
        WHERE prc.org_code = #{orgCode}
        and prc.new_room_id = #{roomId}
        and prc.confirm_time >= #{startTime}
        and prc.jgrybm in (select jgrybm from vw_acp_pm_prisoner_in where jsh = #{roomId})
        order by prc.confirm_time desc
    </select>

</mapper>
