<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.duty.RecordsDao">

    <sql id="all_entity_columns">
        id, org_code, org_name, room_id, duty_id, duty_date, shift_id, shift_name,shift_start_time,
        shift_end_time, group_id, group_name, jgrybm1, jgrybm2, assigner_user_sfzh, assigner_user_name,
        assigned_time
    </sql>

    <delete id="deleteByDutyDate">
        delete
        from pam_duty_records
        where org_code = #{orgCode}
        and room_id = #{roomId}
        and duty_date &gt;= #{dutyDate}
    </delete>

    <delete id="deleteDutyRecordsByPrisoner">
        delete
        from pam_duty_records
        where org_code = #{orgCode}
        and room_id = #{roomId}
        and (
            jgrybm1 in ( <foreach collection="jgrybmList" item="jgrybm" separator=","> #{jgrybm} </foreach> )
            or jgrybm2 in ( <foreach collection="jgrybmList" item="jgrybm" separator=",">  #{jgrybm} </foreach> )
        )
        and duty_date &gt;= #{dutyDate}
    </delete>

    <select id="getNowRecords" resultType="com.rs.module.pam.entity.duty.RecordsDO">
        select <include refid="all_entity_columns"/> from pam_duty_records
        where org_code = #{orgCode}
        and room_id = #{roomId}
        and shift_id = #{shiftId}
        and shift_start_time &lt;= NOW()
        and shift_end_time &gt;= NOW()
        order by add_time desc
        limit 1
    </select>

</mapper>