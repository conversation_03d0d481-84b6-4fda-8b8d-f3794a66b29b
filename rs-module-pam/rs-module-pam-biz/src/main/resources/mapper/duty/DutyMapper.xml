<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.duty.DutyDao">


    <select id="getPrisonerCurrentDuty" resultType="com.rs.module.pam.controller.admin.duty.vo.CurrentDutyRecordsInfoVO">
        SELECT a.id,
               a.duty_date,
               a.duty_id,
               a.duty_date,
               a.shift_id,
               a.shift_name,
               a.shift_start_time,
               a.shift_end_time,
               a.jgrybm1,
               a.jgrybm2,
               (select bp.xm from vw_acp_pm_prisoner_list bp where bp.jgrybm = a.jgrybm1 and bp.rybh != '') as jgryxm1,
               (select bp.xm from vw_acp_pm_prisoner_list bp where bp.jgrybm = a.jgrybm2 and bp.rybh != '') as jgryxm2
        FROM pam_duty_records a
            left join pam_duty_shift b on a.shift_id = b.id and b.is_del = '0'
        WHERE a.is_del = '0'
          AND a.org_code = #{orgCode}
          AND a.room_id = #{roomId}
          AND shift_end_time &gt; #{dutyDate}
          AND shift_start_time &lt;= #{dutyDate}
          AND (a.jgrybm1 = #{jgrybm} or a.jgrybm2 = #{jgrybm})
    </select>

    <delete id="cleanTablePrisonRoomDuty">
        DELETE
        from pam_duty
        where org_code = #{orgCode}
        and room_id = #{roomId}
        <if test="dutyDate != null">
            and duty_date &gt;= #{dutyDate}
        </if>
    </delete>

    <select id="selectPrisonerWeekDutyCount" resultType="java.util.HashMap">
        select value, count(1) as count from (
            select jgrybm1 as value
            from pam_duty_records
            where is_del = '0'
            and org_code = #{orgCode}
            and room_id = #{roomId}
            and duty_date &gt;= #{startTime}
            <if test="endTime != null">
                and duty_date &lt;= #{endTime}
            </if>
            union all
            select jgrybm2
            from pam_duty_records
            where is_del = '0'
            and org_code = #{orgCode}
            and room_id = #{roomId}
            and duty_date &gt;= #{startTime}
            <if test="endTime != null">
                and duty_date &lt;= #{endTime}
            </if>
        ) as t group by value
    </select>


</mapper>