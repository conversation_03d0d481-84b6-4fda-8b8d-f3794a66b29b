<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.duty.ShiftDao">

    <sql id="all_entity_columns">
        id, add_time, update_time, org_code, room_id, shift_name, start_time,
        end_time, effective_start_date, effective_end_date, sort
    </sql>

    <select id="selectShiftByEffectiveDate" resultType="com.rs.module.pam.entity.duty.ShiftDO">
        select <include refid="all_entity_columns"/> from pam_duty_shift
        where is_del = '0'
        and org_code = #{orgCode}
        and room_id = #{roomId}
        and (
            (#{startDate} &gt;= effective_start_date
            and (effective_end_date is null or #{startDate} &lt; effective_end_date
            ))
            or (#{endDate} &gt;= effective_start_date
            and (effective_end_date is null or #{endDate} &lt; effective_end_date
            ))
            or (effective_start_date &gt;= #{startDate} and effective_end_date &lt;= #{endDate})
        )
    </select>

    <select id="getNowShift" resultType="com.rs.module.pam.entity.duty.ShiftDO">
        select <include refid="all_entity_columns"/> from pam_duty_shift
                where is_del = '0'
                and org_code = #{orgCode}
                and room_id = #{roomId}
                and start_time &lt;= to_char(NOW(), 'HH24:MI')
                and end_time &gt;= to_char(NOW(), 'HH24:MI')
                and (effective_start_date &lt;= NOW()
                and (effective_end_date &gt;= NOW() OR effective_end_date IS NULL))
        order by add_time desc
        limit 1
    </select>

    <select id="getShift" resultType="com.rs.module.pam.entity.duty.ShiftDO">
        select <include refid="all_entity_columns"/> from pam_duty_shift
        where is_del = '0'
        and org_code = #{orgCode}
        and room_id = #{roomId}
        and (effective_start_date &lt;= #{date}
        and (effective_end_date &gt;= #{date} OR effective_end_date IS NULL))
        order by sort asc
    </select>

</mapper>
