<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.duty.day.DayDutyShiftDao">

    <sql id="all_entity_columns">
        id, add_time, update_time, org_code, room_id, shift_name,
        effective_start_date, effective_end_date, sort
    </sql>

    <select id="selectShiftByEffectiveDate" resultType="com.rs.module.pam.entity.duty.day.DayDutyShiftDO">
        select <include refid="all_entity_columns"/> from pam_day_duty_shift
        where is_del = '0'
        and org_code = #{orgCode}
        and room_id = #{roomId}
        and (
            (#{startDate} &gt;= effective_start_date
            and (effective_end_date is null or #{startDate} &lt; effective_end_date
            ))
            or (#{endDate} &gt;= effective_start_date
            and (effective_end_date is null or #{endDate} &lt; effective_end_date
            ))
            or (effective_start_date >= #{startDate} and effective_end_date &lt;= #{endDate})
        )
    </select>


</mapper>
