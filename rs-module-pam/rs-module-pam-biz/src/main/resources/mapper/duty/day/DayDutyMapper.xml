<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.duty.day.DayDutyDao">


    <delete id="cleanTablePrisonRoomDuty">
        DELETE
        from pam_day_duty
        where org_code = #{orgCode}
        and room_id = #{roomId}
        <if test="dutyDate != null">
            and duty_date &gt;= #{dutyDate}
        </if>
    </delete>

    <select id="selectPrisonerWeekDutyCount" resultType="java.util.HashMap">
        select value, count(1) as count from (
            select jgrybm1 as value
            from pam_day_duty_records
            where org_code = #{orgCode}
            and room_id = #{roomId}
            and duty_date &gt;= #{startTime}
            <if test="endTime != null">
                and duty_date &lt;= #{endTime}
            </if>
            union all
            select jgrybm2
            from pam_day_duty_records
            where org_code = #{orgCode}
            and room_id = #{roomId}
            and duty_date &gt;= #{startTime}
            <if test="endTime != null">
                and duty_date &lt;= #{endTime}
            </if>
        ) as t group by value
    </select>

</mapper>