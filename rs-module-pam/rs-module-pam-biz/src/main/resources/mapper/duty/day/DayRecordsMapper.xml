<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.duty.day.DayDutyRecordsDao">


    <delete id="deleteByDutyDate">
        delete
        from pam_day_duty_records
        where org_code = #{orgCode}
        and room_id = #{roomId}
        and duty_date &gt;= #{dutyDate}
    </delete>

    <delete id="deleteDutyRecordsByPrisoner">
        delete
        from pam_day_duty_records
        where (
            jgrybm1 in ( <foreach collection="jgrybmList" item="jgrybm" separator=","> #{jgrybm} </foreach> )
            or jgrybm2 in ( <foreach collection="jgrybmList" item="jgrybm" separator=",">  #{jgrybm} </foreach> )
        )
        and duty_date &gt;= #{dutyDate}
    </delete>

</mapper>