<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.duty.day.DayDutyGroupDao">

    <select id="getRoomDutyGroupNo" resultType="java.lang.Integer">
        select max(group_no) from pam_day_duty_group where org_code = #{orgCode} and room_id = #{roomId}
    </select>

</mapper>