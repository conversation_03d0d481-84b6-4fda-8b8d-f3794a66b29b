<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.duty.RecordsSigninDao">

    <sql id="all_entity_columns">
        id, add_time, update_time org_code,org_name,room_id,records_id, duty_date,
        jgrybm, signin_status, signin_time, snap_picture_url, is_signin_valid
    </sql>

    <select id="getNowSigninByRecordsId" resultType="com.rs.module.pam.entity.duty.RecordsSigninDO">
        select <include refid="all_entity_columns"/> from pam_duty_records_signin
        where org_code = #{orgCode}
        and room_id = #{roomId}
        and records_id = #{recordsId}
        and duty_date = CURRENT_DATE
    </select>

    <select id="getNowRecordsSignin" resultType="com.rs.module.pam.entity.duty.RecordsSigninDO">
        select <include refid="all_entity_columns"/> from pam_duty_records_signin
        where org_code = #{orgCode}
        and room_id = #{roomId}
        and records_id = (
            select id from pam_duty_records
            where org_code = #{orgCode}
            and room_id = #{roomId}
            and shift_start_time &lt;= NOW()
            and shift_end_time &gt;= NOW()
            limit 1
        )
        and duty_date = CURRENT_DATE
    </select>


</mapper>