<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.cook.DeliveryDao">

    <select id="selectBusinessTypeCount" resultType="java.util.Map">
        SELECT business_type, COUNT(1) AS nums,jgrybm
        FROM acp_gj_in_out_records
        WHERE room_id = #{roomId}
        AND status = '0'
        AND is_del = 0
        AND inout_type='02'
        GROUP BY business_type,jgrybm
    </select>

    <select id="selectDeliveryByUnniQue" resultType="com.rs.module.pam.entity.cook.DeliveryDO">
        SELECT id,status FROM pam_cook_delivery where cook_book_date = #{cookBookDate} AND is_del = 0
        AND room_id = #{roomId}
        AND org_code = #{orgCode}
        AND meal_period = #{mealPeriod}
    </select>

    <update id="updateDeliveryStatistics">
        WITH detail_stats AS (
        SELECT
        delivery_id,
        COUNT ( * ) FILTER ( WHERE status = '1' ) AS meal_received_total,
        COUNT ( * ) FILTER ( WHERE status = '2' ) AS meal_reserved_total
        FROM
        pam_cook_delivery_record
        WHERE
        delivery_id = #{deliveryId,jdbcType=VARCHAR}
        GROUP BY
        delivery_id
        ) UPDATE pam_cook_delivery M
        SET meal_received_total = COALESCE ( d.meal_received_total, 0 ),
        meal_reserved_total = COALESCE ( d.meal_reserved_total, 0 ),
        meal_unreceived_total = room_total_persons - COALESCE ( d.meal_received_total, 0 )
        FROM
        detail_stats d
        WHERE
        M.ID = d.delivery_id
        AND M.ID = #{deliveryId,jdbcType=VARCHAR}
    </update>

</mapper>
