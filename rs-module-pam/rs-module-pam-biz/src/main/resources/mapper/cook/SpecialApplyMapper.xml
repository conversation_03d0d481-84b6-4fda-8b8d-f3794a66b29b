<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.cook.SpecialApplyDao">
    <select id="getMealType" resultType="java.util.Map">
        SELECT
        meal_type,
        jgrybm
        FROM
        "pam_cook_special_apply"
        WHERE
        is_del = '0'
        AND NOW() BETWEEN meal_start_time
        AND meal_end_time
        AND reg_status = '02'
        AND position(#{mealPeriods,jdbcType=VARCHAR} IN meal_period) > 0
        <if test='jgrybms != null and !jgrybms.isEmpty()'>
            AND jgrybm IN
            <foreach item='id' collection='jgrybms' open='(' separator=',' close=')'>
                #{id}
            </foreach>
        </if>
        group by meal_type, jgrybm
    </select>
</mapper>
