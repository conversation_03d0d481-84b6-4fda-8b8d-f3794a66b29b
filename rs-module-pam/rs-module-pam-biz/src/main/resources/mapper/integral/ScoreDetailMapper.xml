<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.integral.ScoreDetailDao">

    <select id="getOrgScore" resultType="java.math.BigDecimal">
        select COALESCE(sum(score), 0) from pam_integral_score_detail
        where is_del = 0
          and org_code = #{orgCode}
          and risk_model_type = 'org'
    </select>

    <select id="getRiskPrisonerCount" resultType="java.lang.Integer">
        select count(distinct prisoner_code) from pam_integral_score_detail
        where is_del = 0
          and org_code = #{orgCode}
          and risk_model_type = 'prisoner'
          <if test="roomCode != null and roomCode != ''">
            and room_code = #{roomCode}
          </if>
    </select>

    <select id="getSickRoomByOrg" resultType="java.util.HashMap">
        select t1."name",count(t2.room_code) "value" from (
            SELECT
            "id",
            elem->>'name' AS "name",
            (elem->>'thresholdStartValue')::INT AS thresholdStartValue,
            (elem->>'thresholdEndValue')::INT AS thresholdEndValue,
            (elem->>'orderId')::INT AS order_id
            FROM pam_integral_risk_model_config
            CROSS JOIN jsonb_array_elements(level_config::jsonb) AS elem
            where type = 'room'
            and org_code = #{orgCode}
        ) t1 left join (
            select room_code,COALESCE(sum(score), 0) score
            from pam_integral_score_detail
            where is_del = 0
            and risk_model_type = 'room'
            and org_code = #{orgCode}
            group by room_code
        ) t2 on t2.score &gt;= t1.thresholdStartValue
                    and t2.score &lt; t1.thresholdEndValue
        group by t1."name"
    </select>

    <select id="getSickPrisonerByOrg" resultType="java.util.HashMap">
        select t1."name",count(t2.prisoner_code) "value" from (
            SELECT
                "id",
                elem->>'name' AS "name",
                (elem->>'thresholdStartValue')::INT AS thresholdStartValue,
                (elem->>'thresholdEndValue')::INT AS thresholdEndValue,
                (elem->>'orderId')::INT AS order_id
            FROM pam_integral_risk_model_config
                CROSS JOIN jsonb_array_elements(level_config::jsonb) AS elem
            where type = 'prisoner'
              and org_code = #{orgCode}
        ) t1 left join (
            select prisoner_code,COALESCE(sum(score), 0) score
            from pam_integral_score_detail
            where is_del = 0
            and risk_model_type = 'prisoner'
            and org_code = #{orgCode}
            <if test="roomCode != null and roomCode != ''">
                and room_code = #{roomCode}
            </if>
            group by prisoner_code
        ) t2 on t2.score &gt;= t1.thresholdStartValue
            and t2.score &lt; t1.thresholdEndValue
        group by t1."name"
    </select>

</mapper>
