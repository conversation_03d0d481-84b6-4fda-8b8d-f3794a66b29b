<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.common.CommonDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <insert id="batchSavePrintDocument" parameterType="list">
        insert into acp_gj_print_document(
        ID,
        is_del,
        add_time,
        add_user,
        add_user_name,
        update_time,
        update_user,
        update_user_name,
        city_code,
        city_name,
        reg_code,
        reg_name,
        org_code,
        org_name,
        gl_id,
        gl_type,
        wsdata
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},
            0,
            #{item.addTime},
            #{item.addUser},
            #{item.addUserName},
            #{item.updateTime},
            #{item.updateUser},
            #{item.updateUserName},
            #{item.cityCode},
            #{item.cityName},
            #{item.regCode},
            #{item.regName},
            #{item.orgCode},
            #{item.orgName},
            #{item.glId},
            #{item.glType},
            #{item.wsdata})
        </foreach>
    </insert>

    <select id="getWsdataByGlid" parameterType="string" resultType="string">
        SELECT wsdata FROM "acp_gj_print_document" where gl_id=#{glId} and gl_type=#{glType} ORDER BY id asc limit 1
    </select>

    <update id="updateWsdataByGlid" parameterType="string" >
        update acp_gj_print_document set wsdata=#{wsdata} where gl_id=#{glId} and gl_type=#{glType}
    </update>


</mapper>
