<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.psy.EvalPlanDao">


    <select id="getEvalPlanRespVO" resultType="com.rs.module.pam.controller.admin.psy.vo.EvalPlanRespVO">
        SELECT
        CASE WHEN p1.status = '02' THEN timestampdiff ( 'DAY', p1.first_exec_time :: TIMESTAMP, NOW( ) :: TIMESTAMP )
        ELSE 0
        END AS run_days,
        SUM(CASE WHEN p2.filling_status = '1' THEN 1 ELSE 0 END) AS fillNums,
        SUM(CASE WHEN p2.filling_status = '0' THEN 1 ELSE 0 END) AS noFillNums,
        SUM(CASE WHEN p2.filling_status = '0' AND NOW() > add_days_to_date(p2.push_time, p1.completion_deadline) THEN 1
        ELSE 0 END ) AS overFillNums
        FROM
        pam_psy_eval_plan p1
        LEFT JOIN pam_psy_eval_plan_push_record p2 ON p1.plan_code = p2.plan_code AND p2.is_del = '0'
        LEFT JOIN pam_psy_eval_plan_push_record_answer p3 ON p3.eval_no = p2.eval_no AND p3.is_del = '0'
        WHERE p1.is_del = '0' AND p1.id = #{id,jdbcType=VARCHAR}
        GROUP BY p1.id
    </select>

    <select id="selectJgrybm" resultType="java.util.Map">
        SELECT jgrybm,xm,org_code,room_name FROM vw_acp_pm_prisoner_list WHERE is_del = '0' AND ryzt = '10'
        <if test="xm != null and xm != ''">
             and xm LIKE CONCAT('%', #{xm}, '%')
        </if>
        <if test="areaName != null and areaName != ''">
             and (room_name LIKE CONCAT('%', #{areaName}, '%') OR jsh LIKE CONCAT('%', #{areaName}, '%'))
        </if>
        <if test="(jqList != null and !jqList.isEmpty()) or (jsList != null and !jsList.isEmpty()) or (jgrybms != null and !jgrybms.isEmpty())">
            AND (
            <trim prefixOverrides="OR">
                <if test='jqList != null and !jqList.isEmpty()'>
                    area_id IN
                    <foreach item='id' collection='jqList' open='(' separator=',' close=')'>
                        #{id}
                    </foreach>
                </if>
                <if test='jsList != null and !jsList.isEmpty()'>
                    OR jsh IN
                    <foreach item='id' collection='jsList' open='(' separator=',' close=')'>
                        #{id}
                    </foreach>
                </if>
                <if test='jgrybms != null and !jgrybms.isEmpty()'>
                    OR jgrybm IN
                    <foreach item='id' collection='jgrybms' open='(' separator=',' close=')'>
                        #{id}
                    </foreach>
                </if>
            </trim>
            )
        </if>

    </select>
    
    <select id="selectAreaByCode" resultType="java.util.Map">
        SELECT area_code,area_type,area_name FROM acp_pm_area where is_del = '0'
        AND ( 
        <if test='areaCodes != null and !areaCodes.isEmpty()'>
              id IN 
              <foreach item='id' collection='areaCodes' open='(' separator=',' close=')'>
                    #{id}
                  </foreach>
            </if>
        <if test='areaCodes != null and !areaCodes.isEmpty()'>
              OR area_code IN 
              <foreach item='id' collection='areaCodes' open='(' separator=',' close=')'>
                    #{id}
                  </foreach>
            </if>
        )
    </select>
</mapper>
