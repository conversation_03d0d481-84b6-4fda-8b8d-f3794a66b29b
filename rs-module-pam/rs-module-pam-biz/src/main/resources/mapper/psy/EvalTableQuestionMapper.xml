<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.psy.EvalTableQuestionDao">

    <resultMap id="getEvalTableQuestionListRespVOMap"
               type="com.rs.module.pam.controller.admin.psy.vo.EvalTableQuestionRespVO">
        <id property="id" column="id"/>
        <result property="tableId" column="table_id"/>
        <result property="questionText" column="question_text"/>
        <result property="questionType" column="question_type"/>
        <result property="scoreRule" column="score_rule"/>
        <result property="answer" column="answer"/>
        <result property="score" column="score"/>
        <result property="sortOrder" column="sort_order"/>
        <collection property="options" ofType="com.rs.module.pam.controller.admin.psy.vo.EvalTableOptionRespVO">
            <id property="id" column="option_id"/>
            <result property="tableId" column="option_table_id"/>
            <result property="questionId" column="question_id"/>
            <result property="optionCode" column="option_code"/>
            <result property="optionText" column="option_text"/>
            <result property="score" column="option_score"/>
            <result property="sortOrder" column="option_sort_order"/>
        </collection>
    </resultMap>

    <select id="getEvalTableQuestionListRespVO" resultMap="getEvalTableQuestionListRespVOMap">
        SELECT
        t."id",
        t.table_id,
        t.question_text,
        t.question_type,
        t.score_rule,
        t.answer,
        t.score,
        t.sort_order,
        t1.id AS option_id,
        t1.table_id AS option_table_id,
        t1.question_id,
        t1.option_code,
        t1.option_text,
        t1.score AS option_score,
        t1.sort_order AS option_sort_order
        FROM
        pam_psy_eval_table_question t LEFT JOIN pam_psy_eval_table_option t1 ON t.id = t1.question_id AND t1.is_del = '0'
        WHERE t.is_del = '0'
        <if test="ew.tableId != null and ew.tableId != ''">
            and t.table_id = #{ew.tableId}
        </if>
        <if test="ew.questionType != null and ew.questionType != ''">
            and t.question_type = #{ew.questionType}
        </if>
        order by t.sort_order, t1.sort_order ASC
    </select>
</mapper>
