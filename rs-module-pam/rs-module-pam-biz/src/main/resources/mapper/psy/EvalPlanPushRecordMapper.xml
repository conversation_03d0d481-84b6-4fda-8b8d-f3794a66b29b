<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.psy.EvalPlanPushRecordDao">


    <select id="tableList" resultType="com.rs.module.pam.controller.app.vo.AppPsyTableRecordRespVO">
        SELECT
        p1."id",
        p1.table_id,
        p1.plan_code,
        p1.jgrybm,
        p1.jgryxm,
        p1.filling_status,
        p1.push_time,
        p1.score,
        p1.eval_results,
        p1.eval_no,
        p1.plan_add_user_name,
        p2.NAME AS TABLE_NAME,
        p2.estimated_duration,
        p3.plan_name,
        p3.plan_type
        FROM
        pam_psy_eval_plan_push_record p1
        INNER JOIN pam_psy_eval_table p2 ON p1.table_id = p2."id"
        INNER JOIN pam_psy_eval_plan p3 ON p1.plan_code = p3.plan_code
        WHERE
        p1.jgrybm = #{jgrybm,jdbcType=VARCHAR}
        AND p2.table_type IN
        <foreach item='tableType' collection='tableTypes' open='(' separator=',' close=')'>
            #{tableType}
        </foreach>
        AND p1.is_del = '0'
        AND p2.is_del = '0'
        AND p3.is_del = '0'
        AND p3.status = '02'
        AND NOW( ) &lt;= add_days_to_date ( p1.push_time, p3.completion_deadline )
        AND ( ( p3.eval_number = '1' AND p1.filling_status = '0' ) OR p3.eval_number > 1 ) ORDER BY
        p1.push_time DESC
    </select>

    <select id="getEvalPlanPushRecordList" resultType="com.rs.module.pam.controller.admin.psy.vo.EvalPlanPushRecordRespVO">
        SELECT
        p1."id",
        p1.eval_no,
        p1.plan_name,
        p1.plan_code,
        p1.plan_type,
        p1.plan_add_user_name,
        p1.table_id,
        p1."table_name",
        p1.jgryxm,
        p1.jgrybm,
        p1.push_time,
        p1.filling_status,
        p1.filling_time,
        p1.filling_platform,
        p1.score,
        p1.eval_results,
        p2.room_name
        FROM
        pam_psy_eval_plan_push_record p1
        INNER JOIN vw_acp_pm_prisoner_list p2 ON p1.jgrybm = p2.jgrybm AND p2.ryzt = '10'
        INNER JOIN pam_psy_eval_plan p3 ON p1.plan_code = p3.plan_code
        WHERE p1.is_del = '0' AND p3.is_del = '0'
            <include refid="common_where_sql"/>
        order by p1.push_time desc
    </select>

    <select id="getEvalPlanPushRecordListCount" resultType="java.lang.Long">
        SELECT COUNT(*) FROM pam_psy_eval_plan_push_record p1
        INNER JOIN pam_psy_eval_plan p3 ON p1.plan_code = p3.plan_code
        WHERE p1.is_del = '0' AND p3.is_del = '0'
            <include refid="common_where_sql"/>
    </select>

    <sql id="common_where_sql">
        <if test="ew.planCode != null and ew.planCode != ''">
            and p1.plan_code = #{ew.planCode}
        </if>
        <if test="ew.jgryxm != null and ew.jgryxm != ''">
            and p1.jgryxm LIKE CONCAT('%',#{ew.jgryxm},'%')
        </if>
        <if test="ew.fillingStatus != null and ew.fillingStatus != '' and ew.fillingStatus != '2'">
            and p1.filling_status = #{ew.fillingStatus}
        </if>
        <if test="ew.fillingStatus != null and ew.fillingStatus != '' and ew.fillingStatus == '2'">
            AND NOW() > add_days_to_date (p1.push_time, p3.completion_deadline) AND p1.filling_status = '0'
        </if>
    </sql>

    <select id="existPushRecord" resultType="java.lang.String">
        SELECT p1.jgrybm FROM pam_psy_eval_plan_push_record p1
        INNER JOIN pam_psy_eval_plan p3 ON p1.plan_code = p3.plan_code
        WHERE p1.is_del = '0' AND p3.is_del = '0'
            AND p1.filling_status = '0' AND NOW() &lt;= add_days_to_date (p1.push_time, p3.completion_deadline)
            AND p1.plan_code = #{planCode}
            AND p1.table_id IN
            <foreach item='id' collection='tableIds' open='(' separator=',' close=')'>
                #{id}
            </foreach>
            AND p1.jgrybm IN
            <foreach item='id' collection='jgrybms' open='(' separator=',' close=')'>
                #{id}
            </foreach>
    </select>
</mapper>
