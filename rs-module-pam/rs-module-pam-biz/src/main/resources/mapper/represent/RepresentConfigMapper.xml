<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.represent.RepresentConfigDao">


    <select id="getConfigListById" resultType="com.rs.module.pam.entity.represent.RepresentConfigDO">
        SELECT
        t1.ID,
        t1.org_code,
        t1.org_name,
        t1.config_period,
        t1.config_period_code,
        t1.start_time,
        string_agg(t1.room_id, ',' ORDER BY t1.room_id asc ) as roomId,
        t1.room_name,
        t1.task_name,
        t1.expiry_date,
        t1.status
        FROM
        (
        SELECT A.ID,
        A.org_code,
        A.org_name,
        A.config_period,
        A.config_period_code,
        A.start_time,
        UNNEST ( STRING_TO_ARRAY( room_id, ',' ) ) AS room_id,
        A.room_name,
        A.task_name,
        A.expiry_date,
        A.status
        FROM
        pam_represent_config A
        ) t1
        INNER JOIN acp_pm_area_prison_room b ON t1.room_id = b.ID
        <if test="id!=null and id!=''">
            where t1.id = #{id}
        </if>
        GROUP BY t1.id,t1.org_code,t1.org_name,t1.config_period,t1.config_period_code,
                 t1.start_time,t1.room_name,t1.task_name,t1.expiry_date,t1.status
    </select>


</mapper>
