<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.represent.RepresentDao">

    <sql id="findRoomListByUserId">
        <if test='code == "wa"'>
            and a.id in (
            (select room_id from acp_pm_prison_room_warder b where b.status = '1' and b.police_id = #{userId})
            )
        </if>
        <!--<if test='code == "a"'>
            and a.id in (
            (select room_id from acp_pm_prison_room_warder b where b.user_type = 'a' and b.status = '1' and b.police_id = #{userId})
            )
        </if>-->
        <!--<if test='code == "g"'>
            and a.id in (
            select room_id from prison_police_room_care a
            left join base_police_info b on b.id=a.police_id where b.user_id = #{userId}
            )
        </if>-->
    </sql>

    <select id="getWarderRoomInfo" resultType="com.rs.module.pam.controller.admin.represent.vo.extra.RoomInfoVO">
        select a.id room_id,
               a.room_name,
               a.area_id,
               a.area_name,
               substr(a.room_sex,1,1) room_sex,
               a.org_code,
               (SELECT rp.present_status FROM pam_represent rp
                WHERE rp.room_id = A.ID
                ORDER BY rp.operate_time DESC LIMIT 1
                ) as isInRepresent
        from acp_pm_area_prison_room a
        where 1=1
        <if test="roomIds != null and roomIds.length > 0">
            and a.id in
            <foreach collection="roomIds" open="(" separator="," close=")" item="roomId">
                #{roomId}
            </foreach>
        </if>
        <include refid="findRoomListByUserId"/>
        <if test="orgCode != null">
            and org_code=#{orgCode}
        </if>
        order by a.id asc
    </select>

    <select id="getStatusData" resultType="java.util.Map">
        SELECT
            a.jgrybm,
            CASE

                WHEN ( SELECT COUNT(1) FROM acp_wb_arraignment li WHERE (li.return_time is null or li.return_time >= now()) AND A.jgrybm = li.jgrybm ) > 0
                    OR ( SELECT COUNT(1) FROM acp_wb_bring_interrogation li WHERE (li.return_time is null or li.return_time >= now()) AND A.jgrybm = li.jgrybm ) > 0
                    OR ( SELECT COUNT(1) FROM acp_wb_consular_meeting li WHERE (li.return_time is null or li.return_time >= now()) AND A.jgrybm = li.jgrybm ) > 0
                    OR ( SELECT COUNT(1) FROM acp_wb_escort li WHERE (li.return_time is null or li.return_time >= now()) AND A.jgrybm = li.jgrybm ) > 0
                    OR ( SELECT COUNT(1) FROM acp_wb_lawyer_meeting li WHERE (li.return_time is null or li.return_time >= now()) AND A.jgrybm = li.jgrybm ) > 0
                    OR ( SELECT COUNT(1) FROM acp_wb_family_meeting li WHERE (li.return_time is null or li.return_time >= now()) AND A.jgrybm = li.jgrybm ) > 0
                    OR (
                           SELECT COUNT(1)
                           FROM
                               acp_pm_out_prison_treatment li
                           WHERE
                               li.leave_time IS NOT NULL
                             AND ( li.back_operate_time IS NULL OR li.back_operate_time >= now() )
                             AND A.jgrybm = li.jgrybm
                       ) > 0 THEN
                    1 ELSE 0
                END AS outstatus,
            CASE WHEN ( SELECT COUNT(1) FROM pam_info_report pr
                        WHERE pr.status = '3'
                        AND pr.is_rollcall = '0'
                        AND A.jgrybm = pr.object_id
                        AND (now() BETWEEN pr.report_start_time AND pr.report_end_time or pr.report_time_type = '1') ) > 0
                THEN 1 ELSE 0 END AS report
        FROM
            vw_acp_pm_prisoner_in A
        where A.is_del = '0'
          AND A.jsh=#{roomId}
    </select>

    <select id="getRoomInfoByRoomId" resultType="com.rs.module.pam.controller.admin.represent.vo.extra.RoomInfoVO">
        select a.id room_id,a.room_name,a.org_code prison_id, a.org_code, a.org_name
        from acp_pm_area_prison_room a
        where a.is_del = '0'
        <if test="roomId !=null and roomId !=''">
            and a.id = #{roomId}
        </if>
        LIMIT 1
    </select>

    <select id="getCountInRoom" resultType="Integer">
        select count(*) from vw_acp_pm_prisoner_in a
        where a.is_del = '0'
          and a.jsh=#{roomId}
    </select>

    <select id="getNewCountInRoom" resultType="Integer">
        SELECT sum(count) from (
            SELECT COUNT(1) as count
            FROM acp_pm_lawyer_meeting li
                LEFT JOIN vw_acp_pm_prisoner_in A ON A.jgrybm = li.jgrybm and A.is_del = '0'
            WHERE (li.back_to_cell_time is null or li.back_to_cell_time >= now())
                AND A.jsh = #{roomId}

            UNION ALL

            SELECT COUNT(1) as count
            FROM acp_wb_family_meeting li
                LEFT JOIN vw_acp_pm_prisoner_in A ON A.jgrybm = li.jgrybm and A.is_del = '0'
            WHERE li.is_del = '0'
                AND (li.return_time is null or li.return_time >= now())
                AND A.jsh = #{roomId}
            UNION ALL

            SELECT COUNT(1) as count
            FROM acp_wb_arraignment li
                LEFT JOIN vw_acp_pm_prisoner_in A ON A.jgrybm = li.jgrybm and A.is_del = '0'
            WHERE li.is_del = '0'
                AND (li.return_time is null or li.return_time >= now())
                AND A.jsh = #{roomId}
            UNION ALL

            SELECT COUNT(1) as count
            FROM acp_wb_escort li
                LEFT JOIN vw_acp_pm_prisoner_in A ON A.jgrybm = li.jgrybm and A.is_del = '0'
            WHERE li.is_del = '0'
                AND (li.return_time is null or li.return_time >= now())
                AND A.jsh = #{roomId}

            UNION ALL

            SELECT COUNT(1) as count
            FROM acp_pm_out_prison_treatment li
                LEFT JOIN vw_acp_pm_prisoner_in A ON A.jgrybm = li.jgrybm and A.is_del = '0'
            WHERE li.is_del = '0'
                AND li.leave_time IS NOT NULL
                AND ( li.back_operate_time IS NULL OR li.back_operate_time >= now())
                AND A.jsh = #{roomId}
        )t1
    </select>

    <select id="selectRepresentById" resultType="com.rs.module.pam.controller.admin.represent.vo.extra.RoomInfoRepresentVO">
        SELECT
            a.id,
            a.room_id,
            a.room_name,
            a.all_in_num,
            a.in_num,
            a.out_num,
            a.report_num,
            a.start_time,
            a.end_time,
            a.present_status,
            a.present_num,
            a.error_num,
            a.error_handle_result,
            a.operate_people_sfzh,
            a.operate_people_name,
            a.operate_time,
            a.area_id,
            a.area_name,
            a.present_no,
            a.present_type,
            a.expiry_date,
            a.is_video,
            a.temperature_num,
            a.temperature_err_num
        ,b.room_sex,
        case when (a.present_status = '2' or a.present_status = '3') and to_char(a.start_time,'yyyy-mm-dd') = to_char(now(),'yyyy-mm-dd')
        then 1
        else 0
        end as isVideo
        FROM pam_represent a left join acp_pm_area_prison_room b on a.room_id = b.id and b.is_del = '0'
        where a.is_del = '0'
          and a.present_no = #{presentNo}
        order by b.id asc
    </select>

    <select id="getInRepresnetData" resultType="java.lang.Integer">
        select
            case when present_status in ('0', '1') then 1
                 else 0 end as status
        from (
            select present_status
             from pam_represent a
             where a.is_del = '0'
               and org_code = #{orgCode}
               and room_id=#{roomId}
             order by operate_time desc limit 1
        ) a
    </select>

    <select id="getInitBaseDataList" resultType="com.rs.module.pam.controller.admin.represent.vo.extra.InitBaseDataVO">
        SELECT
            b.id as id,
            b.ID AS roomId,
            b.room_name,
            b.org_code,
            b.room_type,
            b.plan_imprisonment_amount,
            COALESCE ( C.prisoner_sum, 0 ) AS prisonerCount,
            d.jgrybm AS prisonerId,
            d.xm AS prisonerName,
            d.xb AS sex,
            d.front_photo AS photo,
            (
                SELECT
                    string_agg ( police_name, ',' )
                FROM
                    acp_pm_prison_room_warder
                WHERE is_del = 0
                  AND status = '1'
                  AND user_type = 'a'
                  AND room_id = b.ID
            ) AS assistPolice,
            (
                SELECT
                    string_agg ( police_name, ',' )
                FROM
                    acp_pm_prison_room_warder
                WHERE is_del = 0
                  AND status = '1'
                  AND user_type = 'w'
                  AND room_id = b.ID
            ) AS executivePolice
        FROM
            acp_pm_device_inscreen A
                LEFT JOIN acp_pm_area_prison_room b ON b.is_del = 0 and A.room_id = b.ID
                LEFT JOIN ( SELECT COUNT ( * ) AS prisoner_sum, jsh FROM vw_acp_pm_prisoner_in where is_del = 0 GROUP BY jsh ) AS C ON C.jsh = b.ID
                LEFT JOIN vw_acp_pm_prisoner_in d on d.is_del = 0 and A.room_id = d.jsh
        WHERE A.serial_number = #{id} and A.is_del = 0 LIMIT 1
    </select>

    <select id="findPersonList" resultType="com.rs.module.pam.controller.admin.represent.vo.extra.InitBaseDataPersonVO">
        select
            a.id as prisonerId,
            a.jgrybm,
            a.xm as prisonerName,
            a.xb as sex,
            a.front_photo as photo,
            CASE
                WHEN ( SELECT COUNT ( 1 ) FROM acp_wb_arraignment li
                       WHERE li.is_del = '0'
                         AND (li.return_time is null or li.return_time >= now()) AND A.jgrybm = li.jgrybm ) > 0
                    THEN '提讯'
                WHEN
                     ( SELECT COUNT ( 1 ) FROM acp_wb_bring_interrogation li
                       WHERE li.is_del = '0'
                         AND (li.return_time is null or li.return_time >= now())AND A.jgrybm = li.jgrybm ) > 0
                    THEN '提询'
                WHEN
                    ( SELECT COUNT ( 1 ) FROM acp_wb_consular_meeting li
                       WHERE li.is_del = '0'
                         AND (li.return_time is null or li.return_time >= now()) AND A.jgrybm = li.jgrybm ) > 0
                    THEN '领事会见'
                WHEN
                    ( SELECT COUNT ( 1 ) FROM acp_wb_escort li
                       WHERE li.is_del = '0'
                         AND (li.return_time is null or li.return_time >= now()) AND A.jgrybm = li.jgrybm ) > 0
                    THEN '提解'
                WHEN
                    ( SELECT COUNT ( 1 ) FROM acp_wb_lawyer_meeting li
                       WHERE li.is_del = '0'
                         AND (li.return_time is null or li.return_time >= now()) AND A.jgrybm = li.jgrybm ) > 0
                    THEN '律师会见'
                WHEN
                    ( SELECT COUNT ( 1 ) FROM acp_wb_family_meeting li
                       WHERE li.is_del = '0'
                         AND (li.return_time is null or li.return_time >= now()) AND A.jgrybm = li.jgrybm ) > 0
                    THEN '家属会见'
                WHEN
                    (
                        SELECT COUNT( 1 )
                        FROM
                            acp_pm_out_prison_treatment li
                        WHERE li.is_del = '0'
                            AND li.leave_time IS NOT NULL
                            AND ( li.back_operate_time IS NULL OR li.back_operate_time >= now() )
                            AND A.jgrybm = li.jgrybm
                    ) > 0
                    THEN'出所就医'
                ELSE '' END AS outStatusName
        from vw_acp_pm_prisoner_in a
        where a.is_del = 0 and a.jsh =#{roomId}
    </select>

    <select id="getPersonInRoom" resultType="com.rs.module.pam.controller.admin.represent.vo.extra.RoomPersonVO">
        SELECT
            id prisoner_id,
            jgrybm,
            xm prisoner_name,
            CASE
                WHEN (
                         SELECT COUNT
                                ( 1 )
                         FROM
                             pam_info_report pr
                         WHERE pr.is_del = '0'
                           AND pr.status = '3'
                           AND pr.is_rollcall = '0'
                           AND A.jgrybm = pr.object_id
                           AND (now() BETWEEN pr.report_start_time
                                    AND pr.report_end_time or pr.report_time_type = '1')
                     ) > 0 THEN
                    1 ELSE 0
                END AS report,
            CASE

                WHEN ( SELECT COUNT ( 1 ) FROM acp_wb_arraignment li
                                          WHERE li.is_del = '0'
                                            AND (li.return_time is null or li.return_time >= now()) AND A.jgrybm = li.jgrybm ) > 0 THEN
                    1 ELSE 0
                END AS out_arraignment,
            CASE

                WHEN ( SELECT COUNT ( 1 ) FROM acp_wb_bring_interrogation li
                                          WHERE li.is_del = '0'
                                            AND (li.return_time is null or li.return_time >= now())AND A.jgrybm = li.jgrybm ) > 0 THEN
                    1 ELSE 0
                END AS out_bring_interrogation,
            CASE

                WHEN ( SELECT COUNT ( 1 ) FROM acp_wb_consular_meeting li
                                          WHERE li.is_del = '0'
                                            AND (li.return_time is null or li.return_time >= now()) AND A.jgrybm = li.jgrybm ) > 0 THEN
                    1 ELSE 0
                END AS out_consular_meeting,
            CASE

                WHEN ( SELECT COUNT ( 1 ) FROM acp_wb_escort li
                                          WHERE li.is_del = '0'
                                            AND (li.return_time is null or li.return_time >= now()) AND A.jgrybm = li.jgrybm ) > 0 THEN
                    1 ELSE 0
                END AS out_escort,
            CASE

                WHEN ( SELECT COUNT ( 1 ) FROM acp_wb_lawyer_meeting li
                                          WHERE li.is_del = '0'
                                            AND (li.return_time is null or li.return_time >= now()) AND A.jgrybm = li.jgrybm ) > 0 THEN
                    1 ELSE 0
                END AS out_lawyer_meeting,
            CASE

                WHEN ( SELECT COUNT ( 1 ) FROM acp_wb_family_meeting li
                                          WHERE li.is_del = '0'
                                            AND (li.return_time is null or li.return_time >= now()) AND A.jgrybm = li.jgrybm ) > 0 THEN
                    1 ELSE 0
                END AS out_family_meeting,
            CASE

                WHEN (
                         SELECT COUNT( 1 )
                         FROM
                             acp_pm_out_prison_treatment li
                         WHERE li.is_del = '0'
                           AND li.leave_time IS NOT NULL
                           AND ( li.back_operate_time IS NULL OR li.back_operate_time >= now() )
                           AND A.jgrybm = li.jgrybm
                     ) > 0 THEN
                    1 ELSE 0
                END AS out_prison_treatment
        FROM
            vw_acp_pm_prisoner_in A
        where A.is_del = '0' AND A.jsh=#{roomId}
    </select>

    <select id="getNowRepresentByRoomId" resultType="com.rs.module.pam.entity.represent.RepresentDO">
        select
            a.id,
            a.room_id,
            a.room_name,
            a.all_in_num,
            a.in_num,
            a.out_num,
            a.report_num,
            a.start_time,
            a.end_time,
            a.present_status,
            a.present_num,
            a.error_num,
            a.error_handle_result,
            a.operate_people_sfzh,
            a.operate_people_name,
            a.operate_time,
            a.area_id,
            a.area_name,
            a.present_no,
            a.present_type,
            a.expiry_date,
            a.is_video,
            a.temperature_num,
            a.temperature_err_num
        from pam_represent a where is_del = '0' AND room_id=#{roomId} and present_status = '1' order by operate_time desc limit 1
    </select>


    <select id="selectInRepresentByRoomIds" resultType="com.rs.module.pam.controller.admin.represent.vo.extra.RoomInfoRepresentVO">
        SELECT
            a.id,
            a.room_id,
            a.room_name,
            a.all_in_num,
            a.in_num,
            a.out_num,
            a.start_time,
            a.end_time,
            a.present_status,
            a.present_num normalNum,
            a.error_num,
            a.error_handle_result,
            a.operate_people_name,
            a.operate_time
        FROM pam_represent a
        <where>
            is_del = '0'
            <if test="roomIds!=null and roomIds.size>0">
                and a.room_id in
                <foreach collection="roomIds" open="(" separator="," close=")" item="roomId">
                    #{roomId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="endRepresentByNo" resultType="java.lang.String">
        select a.room_id from pam_represent a where a.present_no = #{presentNo} and a.present_status = '1'
    </select>

    <select id="getOneTask" resultType="com.rs.module.pam.controller.admin.represent.vo.extra.InitBaseDataConfigVO">
        SELECT A.ID,
               3 AS configPeriodCode,
               A.expiry_date,
               a.start_time
        FROM
            pam_represent A
        WHERE
            A.room_id = #{roomId}
          AND A.present_status = '1'
          AND start_time >= CURRENT_DATE LIMIT 1
    </select>

</mapper>
