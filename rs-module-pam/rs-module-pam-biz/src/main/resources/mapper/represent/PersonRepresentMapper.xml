<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.represent.PersonRepresentDao">

    <select id="getPersonsByRepresentId" resultType="com.rs.module.pam.controller.admin.represent.vo.extra.RepresentPersonVO">
        SELECT a.id,
               a.prisoner_id,
               a.jgryxm prisoner_name,
               a.jgrybm,
               a.sign_status,
               a.is_out,
               a.out_reason,
               a.sign_time,
               a.room_present_id,
               b.front_photo,
               b.room_name,
               b.sxzm AS suspected_charges,
               b.sshj AS litigation_link,
               a.create_user,
               a.is_video,
               a.temperature,
               a.temperature_state,
               a.is_report
        FROM pam_person_represent a
                 left join vw_acp_pm_prisoner_list b on b.jgrybm=a.jgrybm and b.is_del = '0'
        where a.is_del = '0' and a.room_present_id=#{representId}
    </select>

    <select id="getErrPersonsByRepresentId" resultType="com.rs.module.pam.controller.admin.represent.vo.extra.RepresentPersonVO">
        SELECT a.id,
               a.prisoner_id,
               a.jgryxm prisoner_name,
               a.jgrybm,
               a.sign_status,
               a.is_out,
               a.out_reason,
               a.sign_time,
               a.room_present_id,
               b.front_photo,
               b.room_name,
               b.sxzm AS suspected_charges,
               b.sshj AS litigation_link,
               b.sbfh as clothing_number
        FROM pam_person_represent a
            left join vw_acp_pm_prisoner_in b on b.jgrybm=a.jgrybm and b.is_del = '0'
        where a.is_del = '0' and a.room_present_id=#{representId} and sign_status = '0' and is_out is null
    </select>

    <select id="getPersonSignStatus" resultType="com.rs.module.pam.entity.represent.PersonRepresentDO">
        select
            a.id,
            a.org_code,
            a.org_name,
            a.prisoner_id,
            a.jgrybm,
            a.jgryxm,
            a.sign_status,
            a.is_out,
            a.is_report,
            a.out_reason,
            a.sign_time,
            a.room_present_id
        from pam_person_represent a
        where a.is_del = '0' and a.prisoner_id = #{prisonerId} and a.room_present_id = #{representId} LIMIT 1
    </select>

    <select id="getPersonsByRoomId" resultType="com.rs.module.pam.controller.admin.represent.vo.extra.InRepresentPersonVO">
        SELECT a.prisoner_id,a.jgryxm prisoner_name,a.jgrybm,a.sign_status,a.is_out,a.sign_time,a.temperature,a.out_reason,b.front_photo
        FROM pam_person_represent a
        left join vw_acp_pm_prisoner_in b on b.jgrybm = a.jgrybm and b.is_del = '0'
        where a.is_del = '0' and a.room_present_id in
        (select id from pam_represent where room_id=#{roomId} and present_status='1')
        <if test="second!=null">
            and a.sign_status = '0'
        </if>
        ORDER BY
        a.sign_time DESC,a.is_out desc
    </select>


</mapper>
