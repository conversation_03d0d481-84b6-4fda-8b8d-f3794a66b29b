<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.attention.AttentionPrisonerDao">

    <select id="getPrisonerCount" resultType="java.lang.Integer">
        select count(1) from pam_attention_prisoner t1, vw_acp_pm_prisoner_in t2
        where t1.is_del = '0'
        and t2.is_del = '0'
        and t1.reg_status = '3'
        and t1.jgrybm = t2.jgrybm
        <if test="orgCode != null and orgCode != ''">
            and t2.org_code = #{orgCode}
        </if>
        <if test="roomCode != null and roomCode != ''">
            and t2.jsh = #{roomCode}
        </if>
    </select>

    <select id="getPrisonerByOrgCode" resultType="com.rs.module.pam.controller.admin.attention.vo.PrisonerRespVO">
        select t1.id,t1.org_code,t1.org_name,t1.jgrybm,t1.begin_time,t1.end_time,
               t1.attention_reason,t1.frequency_type,t1.frequency,t1.reg_operator_sfzh,
               t1.reg_operator_xm,t1.reg_time,t2.jsh room_id,t2.room_name,t2.xm as jgryxm,t2.front_photo
        from pam_attention_prisoner t1, vw_acp_pm_prisoner_in t2
        where t1.jgrybm = t2.jgrybm
          and t1.is_del = '0'
          and t2.is_del = '0'
          and t1.org_code = #{orgCode}
          and t1.reg_status = '3'
    </select>

</mapper>
