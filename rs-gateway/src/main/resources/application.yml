server:
  port: ${conf.server.port.gateway}
  max-http-header-size: 102400

# 日志
logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径

# spring    
spring:
  application:
    name: gateway-server
  profiles:
    active: conf,dev
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Feign 等会存在重复定义的服务
  cloud:
    nacos:
      discovery:
        enabled: ${conf.nacos.enabled}
        server-addr: ${conf.nacos.ip}:${conf.nacos.port}        
        username: ${conf.nacos.username}
        password: ${conf.nacos.password}
        namespace: ${conf.nacos.namespace}
        group: ${conf.nacos.group}
    loadbalancer:
      ribbon:
        enabled: false
    gateway:
      httpclient:
        #全局的TCP连接超时时间默认时间是45秒，修改为10秒
        connect-timeout: 5000
        #全局的响应超时时间，网络链接后，后端服务多久不返回网关就报错 The response timeout. PT30S代表30秒的意思
        response-timeout: PT60s
        pool:
          #最大连接数
          max-connections: 10000
          #获取连接的超时时间，单位毫秒
          acquire-timeout: 5000
          #channel空闲时，最大的存活时间，如果为空，没有最大空闲时间
          max-idle-time: PT5S
          #设置固定链接池
          type: fixed
      discovery:
        locator:
          lowerCaseServiceId: true
          enabled: true        

########################## 监所实战平台相关配置 ##########################
rs:
  info:
    version: 1.0.0
