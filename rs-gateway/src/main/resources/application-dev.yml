--- ########################## 路由代理配置 ##########################
spring:
  cloud:
    # Spring Cloud Gateway 配置项，对应 GatewayProperties 类
    gateway:
      # 路由配置项，对应 RouteDefinition 数组
      routes:
        ### demo-server 服务(示例)
        - id: demo           # 路由的编号
          uri: lb://demo-server
          predicates:        # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/demo/**  # 配置，保证转发
          filters:
            - StripPrefix=1
        ### acp-server 服务(实战平台)
        - id: acp           # 路由的编号
          uri: lb://acp-server
          predicates:       # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/acp/**  # 配置，保证转发
          filters:
            - StripPrefix=1
        ### ihc-server 服务(医疗管理)
        - id: ihc           # 路由的编号
          uri: lb://ihc-server
          predicates:       # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/ihc/**  # 配置，保证转发
          filters:
            - StripPrefix=1
        ### integ-server 服务(系统集成)
        - id: integ           # 路由的编号
          uri: lb://integ-server
          predicates:       # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/integ/**  # 配置，保证转发
          filters:
            - StripPrefix=1
        ### rgf-server 服务(需求收集反馈)
        - id: rgf           # 路由的编号
          uri: lb://rgf-server
          predicates:       # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/rgf/**  # 配置，保证转发
          filters:
            - StripPrefix=1