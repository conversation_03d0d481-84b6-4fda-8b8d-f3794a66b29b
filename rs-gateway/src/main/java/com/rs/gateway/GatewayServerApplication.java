package com.rs.gateway;

import java.net.UnknownHostException;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ConfigurableApplicationContext;

import com.rs.framework.common.util.spring.SpringUtils;

/**
 * 网关启动类
 * <AUTHOR>
 * @date 2025年3月14日
 */
@SpringBootApplication
@EnableDiscoveryClient
public class GatewayServerApplication {

	public static void main(String[] args) throws UnknownHostException {
		ConfigurableApplicationContext application = SpringApplication.run(GatewayServerApplication.class, args);
		SpringUtils.printStartLog(application);
	}
}
