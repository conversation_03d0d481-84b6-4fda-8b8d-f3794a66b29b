package com.rs.gateway.util;

import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;

/**
 * 安全服务工具类
 * <AUTHOR>
 * @date 2025年3月15日
 */
public class SecurityUtils {

	private static final String AUTHORIZATION_HEADER = "Authorization";

    private static final String AUTHORIZATION_BEARER = "Bearer";
    
    private SecurityUtils() {}
    
    /**
     * 从请求中获取认证Token
     * @param exchange ServerWebExchange
     * @return String
     * <AUTHOR>
     * @date 2025年3月15日
     */
    public static String obtainAuthorization(ServerWebExchange exchange) {
        String authorization = exchange.getRequest().getHeaders().getFirst(AUTHORIZATION_HEADER);
        if (!StringUtils.hasText(authorization)) {
            return null;
        }
        int index = authorization.indexOf(AUTHORIZATION_BEARER + " ");
        if (index == -1) { // 未找到
            return null;
        }
        return authorization.substring(index + 7).trim();
    }
}
