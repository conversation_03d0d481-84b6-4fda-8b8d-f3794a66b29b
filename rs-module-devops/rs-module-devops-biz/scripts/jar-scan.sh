#!/bin/bash
# Jar文件扫描工具启动脚本 (Linux/Unix)
# 使用方法: ./jar-scan.sh <目录路径> [递归扫描:true/false] [计算SHA-1:true/false]

# 设置Java路径
if [ -z "$JAVA_HOME" ]; then
    JAVA_CMD=java
else
    JAVA_CMD="$JAVA_HOME/bin/java"
fi

# 设置类路径
CLASSPATH=".:../target/classes:../target/lib/*"

# 检查参数
if [ $# -eq 0 ]; then
    echo "错误: 请指定扫描目录"
    echo "用法: $0 <目录路径> [递归扫描:true/false] [计算SHA-1:true/false]"
    echo ""
    echo "示例:"
    echo "  $0 ./lib"
    echo "  $0 ./lib true true"
    echo "  $0 /opt/app/lib false false"
    exit 1
fi

# 运行Java程序
echo "启动Jar文件扫描工具..."
echo ""
$JAVA_CMD -cp "$CLASSPATH" com.rs.module.devops.cli.JarScanCLI "$@"
