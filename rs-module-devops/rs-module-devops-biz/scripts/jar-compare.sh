#!/bin/bash
# Jar文件对比工具启动脚本 (Linux/Unix)
# 使用方法: ./jar-compare.sh <基准目录> <目标目录> [计算SHA-1:true/false]

# 设置Java路径
if [ -z "$JAVA_HOME" ]; then
    JAVA_CMD=java
else
    JAVA_CMD="$JAVA_HOME/bin/java"
fi

# 设置类路径
CLASSPATH=".:../target/classes:../target/lib/*"

# 检查参数
if [ $# -lt 2 ]; then
    echo "错误: 请指定基准目录和目标目录"
    echo "用法: $0 <基准目录> <目标目录> [计算SHA-1:true/false]"
    echo ""
    echo "示例:"
    echo "  $0 ./baseline ./target"
    echo "  $0 ./baseline ./target true"
    echo "  $0 /opt/app/baseline /opt/app/target false"
    exit 1
fi

# 运行Java程序
echo "启动Jar文件对比工具..."
echo ""
$JAVA_CMD -cp "$CLASSPATH" com.rs.module.devops.cli.JarCompareCLI "$@"
