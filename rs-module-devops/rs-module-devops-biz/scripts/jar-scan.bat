@echo off
REM Jar文件扫描工具启动脚本 (Windows)
REM 使用方法: jar-scan.bat <目录路径> [递归扫描:true/false] [计算SHA-1:true/false]

setlocal

REM 设置Java路径（如果JAVA_HOME未设置）
if "%JAVA_HOME%"=="" (
    set JAVA_CMD=java
) else (
    set JAVA_CMD="%JAVA_HOME%\bin\java"
)

REM 设置类路径
set CLASSPATH=.;..\target\classes;..\target\lib\*

REM 检查参数
if "%1"=="" (
    echo 错误: 请指定扫描目录
    echo 用法: %0 ^<目录路径^> [递归扫描:true/false] [计算SHA-1:true/false]
    echo.
    echo 示例:
    echo   %0 .\lib
    echo   %0 .\lib true true
    echo   %0 C:\app\lib false false
    goto :end
)

REM 运行Java程序
echo 启动Jar文件扫描工具...
echo.
%JAVA_CMD% -cp "%CLASSPATH%" com.rs.module.devops.cli.JarScanCLI %*

:end
pause
