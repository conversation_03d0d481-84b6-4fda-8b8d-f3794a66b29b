@echo off
REM Jar文件对比工具启动脚本 (Windows)
REM 使用方法: jar-compare.bat <基准目录> <目标目录> [计算SHA-1:true/false]

setlocal

REM 设置Java路径（如果JAVA_HOME未设置）
if "%JAVA_HOME%"=="" (
    set JAVA_CMD=java
) else (
    set JAVA_CMD="%JAVA_HOME%\bin\java"
)

REM 设置类路径
set CLASSPATH=.;..\target\classes;..\target\lib\*

REM 检查参数
if "%1"=="" (
    echo 错误: 请指定基准目录和目标目录
    echo 用法: %0 ^<基准目录^> ^<目标目录^> [计算SHA-1:true/false]
    echo.
    echo 示例:
    echo   %0 .\baseline .\target
    echo   %0 .\baseline .\target true
    echo   %0 C:\app\baseline C:\app\target false
    goto :end
)

if "%2"=="" (
    echo 错误: 请指定目标目录
    echo 用法: %0 ^<基准目录^> ^<目标目录^> [计算SHA-1:true/false]
    goto :end
)

REM 运行Java程序
echo 启动Jar文件对比工具...
echo.
%JAVA_CMD% -cp "%CLASSPATH%" com.rs.module.devops.cli.JarCompareCLI %*

:end
pause
