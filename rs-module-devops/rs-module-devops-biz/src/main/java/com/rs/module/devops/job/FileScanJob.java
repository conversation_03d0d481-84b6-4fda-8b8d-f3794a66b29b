package com.rs.module.devops.job;

import com.rs.module.devops.config.DevOpsConfig;
import com.rs.module.devops.service.AppRestartService;
import com.rs.module.devops.service.DevOpsClientService;
import com.rs.module.devops.service.DevOpsServerService;
import com.rs.module.devops.vo.CompareReportVO;
import com.rs.module.devops.vo.FileInfoVO;
import com.rs.module.devops.vo.RestartResultVO;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 文件扫描定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class FileScanJob {

    @Autowired
    private DevOpsConfig devOpsConfig;

    @Autowired
    private DevOpsClientService devOpsClientService;

    @Autowired
    private DevOpsServerService devOpsServerService;

    @Autowired
    private AppRestartService appRestartService;

    @Autowired
    private TaskLockManager taskLockManager;

    /**
     * 任务名称常量
     */
    private static final String CLIENT_AUTO_SYNC_TASK = "CLIENT_AUTO_SYNC";
    private static final String CLEAN_EXPIRED_BACKUPS_TASK = "CLEAN_EXPIRED_BACKUPS";
    private static final String CLIENT_SCAN_TASK = "CLIENT_SCAN";
    private static final String SERVER_SCAN_TASK = "SERVER_SCAN";
    private static final String CLIENT_SYNC_WITH_RESTART_TASK = "CLIENT_SYNC_WITH_RESTART";
    private static final String SYNC_STATISTICS_TASK = "SYNC_STATISTICS";

    /**
     * 客户端文件扫描任务
     */
    @XxlJob("clientFileScanJob")
    public void clientFileScanJob() {
        // 尝试获取任务锁，如果获取失败则快速失败
        if (!taskLockManager.tryLock(CLIENT_SCAN_TASK)) {
            return;
        }

        try {
            String jobId = String.valueOf(XxlJobHelper.getJobId());
            XxlJobHelper.log("[{}] 客户端文件扫描任务开始", jobId);
            log.info("[{}] 客户端文件扫描任务开始", jobId);

            long startTime = System.currentTimeMillis();

            // 检查运行模式，如果是服务端模式则跳过执行
            if (devOpsConfig.isServerMode()) {
                XxlJobHelper.log("[{}] 当前为服务端模式，跳过客户端文件扫描任务", jobId);
                log.info("[{}] 当前为服务端模式，跳过客户端文件扫描任务", jobId);
                return;
            }

            // 检查是否启用客户端自动扫描
            if (!devOpsConfig.getFileSync().getEnableClientAutoSync()) {
                XxlJobHelper.log("[{}] 客户端自动同步已禁用", jobId);
                return;
            }

            // 扫描本地文件
            List<FileInfoVO> fileInfoList = devOpsClientService.scanLocalFiles();
            XxlJobHelper.log("[{}] 扫描完成，文件数量: {}", jobId, fileInfoList.size());

            long endTime = System.currentTimeMillis();
            double duration = (endTime - startTime) / 1000.0;

            XxlJobHelper.log("[{}] 客户端文件扫描任务完成，用时: {}秒", jobId, duration);
            log.info("[{}] 客户端文件扫描任务完成，用时: {}秒", jobId, duration);

        } catch (Exception e) {
            XxlJobHelper.log("[{}] 客户端文件扫描任务执行失败: {}", XxlJobHelper.getJobId(), e.getMessage());
            log.error("客户端文件扫描任务执行失败", e);
        } finally {
            // 释放任务锁
            taskLockManager.releaseLock(CLIENT_SCAN_TASK);
        }
    }

    /**
     * 服务端文件扫描任务
     */
    @XxlJob("serverFileScanJob")
    public void serverFileScanJob() {
        // 尝试获取任务锁，如果获取失败则快速失败
        if (!taskLockManager.tryLock(SERVER_SCAN_TASK)) {
            return;
        }

        try {
            String jobId = String.valueOf(XxlJobHelper.getJobId());
            XxlJobHelper.log("[{}] 服务端文件扫描任务开始", jobId);
            log.info("[{}] 服务端文件扫描任务开始", jobId);

            long startTime = System.currentTimeMillis();

            // 检查运行模式，如果是客户端模式则跳过执行
            if (devOpsConfig.isClientMode()) {
                XxlJobHelper.log("[{}] 当前为客户端模式，跳过服务端文件扫描任务", jobId);
                log.info("[{}] 当前为客户端模式，跳过服务端文件扫描任务", jobId);
                return;
            }

            // 检查是否启用服务端自动扫描
            if (!devOpsConfig.getFileSync().getEnableServerAutoScan()) {
                XxlJobHelper.log("[{}] 服务端自动扫描已禁用", jobId);
                return;
            }

            // 扫描服务端文件
            List<FileInfoVO> fileInfoList = devOpsServerService.scanServerFiles();
            XxlJobHelper.log("[{}] 扫描完成，文件数量: {}", jobId, fileInfoList.size());

            long endTime = System.currentTimeMillis();
            double duration = (endTime - startTime) / 1000.0;

            XxlJobHelper.log("[{}] 服务端文件扫描任务完成，用时: {}秒", jobId, duration);
            log.info("[{}] 服务端文件扫描任务完成，用时: {}秒", jobId, duration);

        } catch (Exception e) {
            XxlJobHelper.log("[{}] 服务端文件扫描任务执行失败: {}", XxlJobHelper.getJobId(), e.getMessage());
            log.error("服务端文件扫描任务执行失败", e);
        } finally {
            // 释放任务锁
            taskLockManager.releaseLock(SERVER_SCAN_TASK);
        }
    }

    /**
     * 客户端自动同步任务
     */
    @XxlJob("clientAutoSyncJob")
    public void clientAutoSyncJob() {
        // 尝试获取任务锁，如果获取失败则快速失败
        if (!taskLockManager.tryLock(CLIENT_AUTO_SYNC_TASK)) {
            return;
        }

        try {
            log.info("客户端自动同步任务开始执行");

            // 检查运行模式，如果是服务端模式则跳过执行
            if (devOpsConfig.isServerMode()) {
                log.info("当前为服务端模式，跳过客户端自动同步任务");
                return;
            }

            // 检查是否启用客户端自动同步
            if (!devOpsConfig.getFileSync().getEnableClientAutoSync()) {
                log.info("客户端自动同步已禁用，任务结束");
                return;
            }

            // 1. 扫描本地文件
            List<FileInfoVO> fileInfoList = devOpsClientService.scanLocalFiles();
            log.info("本地文件扫描完成，文件数量: {}", fileInfoList.size());

            // 2. 提交到服务端对比
            CompareReportVO compareReport = devOpsClientService.submitScanResult(fileInfoList);
            if (compareReport == null) {
                log.error("服务端对比失败");
                return;
            }

            log.info("对比完成: {}", compareReport.getSummary());

            // 3. 如果有差异，执行同步
            if (compareReport.getHasDifferences()) {
                log.info("发现文件差异，开始同步...");

                Boolean syncResult = devOpsClientService.executeSync(compareReport);
                if (syncResult) {
                    log.info("文件同步成功");

                    // 4. 文件同步成功后，自动重启相关应用
                    if (appRestartService.isRestartEnabled()) {
                        log.info("开始检查需要重启的应用...");

                        String clientIp = devOpsClientService.getClientIp();
                        RestartResultVO restartResult = appRestartService.autoRestartAfterSync(compareReport, syncResult, clientIp);

                        if (restartResult != null) {
                            log.info("应用重启完成: {}", restartResult.getSummary());

                            // 记录重启详情
                            if (restartResult.getRestartDetails() != null) {
                                for (RestartResultVO.AppRestartDetailVO detail : restartResult.getRestartDetails()) {
                                    if (detail.getSuccess()) {
                                        log.info("应用 {} 重启成功，耗时: {}ms",
                                                detail.getAppName(), detail.getExecutionTime());
                                    } else {
                                        log.error("应用 {} 重启失败: {}",
                                                detail.getAppName(), detail.getErrorMessage());
                                    }
                                }
                            }
                        } else {
                            log.info("无应用需要重启");
                        }
                    } else {
                        log.info("应用重启功能未启用");
                    }
                } else {
                    log.warn("文件同步过程中出现部分失败，跳过应用重启");
                }
            } else {
                log.info("文件已是最新，无需同步");
            }

            log.info("客户端自动同步任务执行完成");

        } catch (Exception e) {
            log.error("客户端自动同步任务执行失败", e);
        } finally {
            // 释放任务锁
            taskLockManager.releaseLock(CLIENT_AUTO_SYNC_TASK);
        }
    }

    /**
     * 清理过期备份文件任务
     */
    @Scheduled(cron = "#{@devOpsConfig.scheduledTasks.cleanExpiredBackupsCron}")
    public void cleanExpiredBackupsJob() {
        // 尝试获取任务锁，如果获取失败则快速失败
        if (!taskLockManager.tryLock(CLEAN_EXPIRED_BACKUPS_TASK)) {
            return;
        }

        try {
            log.info("清理过期备份文件任务开始执行");

            // 检查运行模式，如果是服务端模式则跳过执行
            if (devOpsConfig.isServerMode()) {
                log.info("当前为服务端模式，跳过清理过期备份文件任务");
                return;
            }

            // 清理过期备份文件
            Integer cleanedCount = devOpsClientService.cleanExpiredBackups();
            log.info("清理完成，共清理 {} 个过期备份文件", cleanedCount);

            log.info("清理过期备份文件任务执行完成");

        } catch (Exception e) {
            log.error("清理过期备份文件任务执行失败", e);
        } finally {
            // 释放任务锁
            taskLockManager.releaseLock(CLEAN_EXPIRED_BACKUPS_TASK);
        }
    }

    /**
     * 客户端自动同步并重启应用任务
     */
    @Scheduled(cron = "#{@devOpsConfig.scheduledTasks.clientAutoSyncCron}")
    @XxlJob("clientSyncWithRestartJob")
    public void clientSyncWithRestartJob() {
        // 尝试获取任务锁，如果获取失败则快速失败
        if (!taskLockManager.tryLock(CLIENT_SYNC_WITH_RESTART_TASK)) {
            return;
        }

        try {
            String jobId = String.valueOf(XxlJobHelper.getJobId());
            XxlJobHelper.log("[{}] 同步并重启任务开始", jobId);
            log.info("[{}] 同步并重启任务开始", jobId);

            long startTime = System.currentTimeMillis();

            // 检查运行模式，如果是服务端模式则跳过执行
            if (devOpsConfig.isServerMode()) {
                XxlJobHelper.log("[{}] 当前为服务端模式，跳过客户端同步并重启任务", jobId);
                log.info("[{}] 当前为服务端模式，跳过客户端同步并重启任务", jobId);
                return;
            }

            // 检查是否启用客户端自动同步
            if (!devOpsConfig.getFileSync().getEnableClientAutoSync()) {
                XxlJobHelper.log("[{}] 客户端自动同步已禁用", jobId);
                return;
            }

            // 检查是否启用应用重启功能
            if (!appRestartService.isRestartEnabled()) {
                XxlJobHelper.log("[{}] 应用重启功能未启用，使用普通同步任务", jobId);
                return;
            }

            // 1. 扫描本地文件
            List<FileInfoVO> fileInfoList = devOpsClientService.scanLocalFiles();
            XxlJobHelper.log("[{}] 本地文件扫描完成，文件数量: {}", jobId, fileInfoList.size());

            // 2. 提交到服务端对比
            CompareReportVO compareReport = devOpsClientService.submitScanResult(fileInfoList);
            if (compareReport == null) {
                XxlJobHelper.log("[{}] 服务端对比失败", jobId);
                log.error("[{}] 服务端对比失败", jobId);
                return;
            }

            XxlJobHelper.log("[{}] 对比完成: {}", jobId, compareReport.getSummary());

            // 3. 如果有差异，执行同步并重启
            if (compareReport.getHasDifferences()) {
                XxlJobHelper.log("[{}] 发现文件差异，开始同步并重启相关应用...", jobId);

                String clientIp = devOpsClientService.getClientIp();
                RestartResultVO restartResult = devOpsClientService.executeSyncWithRestart(compareReport);

                if (restartResult != null) {
                    XxlJobHelper.log("[{}] 同步并重启完成: {}", jobId, restartResult.getSummary());
                    log.info("[{}] 同步并重启完成: {}", jobId, restartResult.getSummary());

                    // 记录详细的重启结果
                    if (restartResult.getRestartDetails() != null) {
                        for (RestartResultVO.AppRestartDetailVO detail : restartResult.getRestartDetails()) {
                            if (detail.getSuccess()) {
                                XxlJobHelper.log("[{}] ✓ 应用 {} 重启成功，耗时: {}ms",
                                        jobId, detail.getAppName(), detail.getExecutionTime());
                            } else {
                                XxlJobHelper.log("[{}] ✗ 应用 {} 重启失败: {}",
                                        jobId, detail.getAppName(), detail.getErrorMessage());
                                log.error("[{}] 应用 {} 重启失败: {}",
                                        jobId, detail.getAppName(), detail.getErrorMessage());
                            }
                        }
                    }
                } else {
                    XxlJobHelper.log("[{}] 文件同步完成，无应用需要重启", jobId);
                }
            } else {
                XxlJobHelper.log("[{}] 文件已是最新，无需同步", jobId);
            }

            long endTime = System.currentTimeMillis();
            double duration = (endTime - startTime) / 1000.0;

            XxlJobHelper.log("[{}] 客户端同步并重启任务完成，用时: {}秒", jobId, duration);
            log.info("[{}] 客户端同步并重启任务完成，用时: {}秒", jobId, duration);

        } catch (Exception e) {
            XxlJobHelper.log("[{}] 客户端同步并重启任务执行失败: {}", XxlJobHelper.getJobId(), e.getMessage());
            log.error("客户端同步并重启任务执行失败", e);
        } finally {
            // 释放任务锁
            taskLockManager.releaseLock(CLIENT_SYNC_WITH_RESTART_TASK);
        }
    }

    /**
     * 获取同步统计信息任务
     */
    @XxlJob("syncStatisticsJob")
    public void syncStatisticsJob() {
        // 尝试获取任务锁，如果获取失败则快速失败
        if (!taskLockManager.tryLock(SYNC_STATISTICS_TASK)) {
            return;
        }

        try {
            String jobId = String.valueOf(XxlJobHelper.getJobId());
            XxlJobHelper.log("[{}] 同步统计信息任务开始", jobId);
            log.info("[{}] 同步统计信息任务开始", jobId);

            // 检查运行模式，如果是客户端模式则跳过执行
            if (devOpsConfig.isClientMode()) {
                XxlJobHelper.log("[{}] 当前为客户端模式，跳过同步统计信息任务", jobId);
                log.info("[{}] 当前为客户端模式，跳过同步统计信息任务", jobId);
                return;
            }

            // 获取统计信息
            Object statistics = devOpsServerService.getSyncStatistics(null);
            XxlJobHelper.log("[{}] 统计信息: {}", jobId, statistics.toString());

            XxlJobHelper.log("[{}] 同步统计信息任务完成", jobId);
            log.info("[{}] 同步统计信息任务完成", jobId);

        } catch (Exception e) {
            XxlJobHelper.log("[{}] 同步统计信息任务执行失败: {}", XxlJobHelper.getJobId(), e.getMessage());
            log.error("同步统计信息任务执行失败", e);
        } finally {
            // 释放任务锁
            taskLockManager.releaseLock(SYNC_STATISTICS_TASK);
        }
    }
}
