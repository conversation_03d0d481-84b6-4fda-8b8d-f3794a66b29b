package com.rs.module.devops.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 同步进度VO
 *
 * <AUTHOR>
 */
@ApiModel(description = "同步进度VO")
@Data
public class SyncProgressVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户端IP")
    private String clientIp;

    @ApiModelProperty(value = "同步开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date syncStartTime;

    @ApiModelProperty(value = "同步结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date syncEndTime;

    @ApiModelProperty(value = "同步状态")
    private String syncStatus;

    @ApiModelProperty(value = "总文件数")
    private Integer totalFiles;

    @ApiModelProperty(value = "待更新文件数")
    private Integer pendingFiles;

    @ApiModelProperty(value = "已更新文件数")
    private Integer completedFiles;

    @ApiModelProperty(value = "失败文件数")
    private Integer failedFiles;

    @ApiModelProperty(value = "跳过文件数")
    private Integer skippedFiles;

    @ApiModelProperty(value = "同步进度百分比")
    private Double progressPercentage;

    @ApiModelProperty(value = "当前处理的文件")
    private String currentFile;

    @ApiModelProperty(value = "同步速度（文件/秒）")
    private Double syncSpeed;

    @ApiModelProperty(value = "预计剩余时间（秒）")
    private Long estimatedRemainingTime;

    @ApiModelProperty(value = "文件详情列表")
    private List<FileProgressVO> fileDetails;

    @ApiModelProperty(value = "错误信息")
    private String errorMessage;

    @ApiModelProperty(value = "同步摘要")
    private String summary;

    /**
     * 文件进度VO
     */
    @ApiModel(description = "文件进度VO")
    @Data
    public static class FileProgressVO implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "文件相对路径")
        private String relativePath;

        @ApiModelProperty(value = "文件大小")
        private Long fileSize;

        @ApiModelProperty(value = "操作类型")
        private String operation; // ADD, UPDATE, DELETE

        @ApiModelProperty(value = "处理状态")
        private String status; // PENDING, PROCESSING, COMPLETED, FAILED, SKIPPED

        @ApiModelProperty(value = "开始时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date startTime;

        @ApiModelProperty(value = "结束时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date endTime;

        @ApiModelProperty(value = "处理耗时（毫秒）")
        private Long processingTime;

        @ApiModelProperty(value = "错误信息")
        private String errorMessage;

        @ApiModelProperty(value = "备份文件路径")
        private String backupPath;
    }

    public SyncProgressVO() {
        this.syncStartTime = new Date();
        this.syncStatus = "PENDING";
        this.totalFiles = 0;
        this.pendingFiles = 0;
        this.completedFiles = 0;
        this.failedFiles = 0;
        this.skippedFiles = 0;
        this.progressPercentage = 0.0;
        this.syncSpeed = 0.0;
    }

    public SyncProgressVO(String clientIp) {
        this();
        this.clientIp = clientIp;
    }

    /**
     * 计算进度百分比
     */
    public void calculateProgress() {
        if (totalFiles != null && totalFiles > 0) {
            int processed = (completedFiles != null ? completedFiles : 0) + 
                           (failedFiles != null ? failedFiles : 0) + 
                           (skippedFiles != null ? skippedFiles : 0);
            this.progressPercentage = (double) processed / totalFiles * 100;
        } else {
            this.progressPercentage = 0.0;
        }
    }

    /**
     * 计算同步速度
     */
    public void calculateSyncSpeed() {
        if (syncStartTime != null && completedFiles != null && completedFiles > 0) {
            long elapsedMillis = System.currentTimeMillis() - syncStartTime.getTime();
            long elapsedSeconds = elapsedMillis / 1000;
            if (elapsedSeconds > 0) {
                this.syncSpeed = (double) completedFiles / elapsedSeconds;
            }
        }
    }

    /**
     * 计算预计剩余时间
     */
    public void calculateEstimatedRemainingTime() {
        if (syncSpeed != null && syncSpeed > 0 && pendingFiles != null && pendingFiles > 0) {
            this.estimatedRemainingTime = (long) (pendingFiles / syncSpeed);
        } else {
            this.estimatedRemainingTime = 0L;
        }
    }

    /**
     * 生成同步摘要
     */
    public void generateSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("同步进度: ");
        
        if (totalFiles != null && totalFiles > 0) {
            sb.append(String.format("总计 %d 个文件，", totalFiles));
            sb.append(String.format("已完成 %d 个，", completedFiles != null ? completedFiles : 0));
            sb.append(String.format("失败 %d 个，", failedFiles != null ? failedFiles : 0));
            sb.append(String.format("跳过 %d 个，", skippedFiles != null ? skippedFiles : 0));
            sb.append(String.format("待处理 %d 个", pendingFiles != null ? pendingFiles : 0));
            
            if (progressPercentage != null) {
                sb.append(String.format("，进度 %.1f%%", progressPercentage));
            }
        } else {
            sb.append("无文件需要同步");
        }
        
        this.summary = sb.toString();
    }

    /**
     * 更新文件状态
     */
    public void updateFileStatus(String relativePath, String status, String errorMessage) {
        if (fileDetails != null) {
            for (FileProgressVO fileDetail : fileDetails) {
                if (fileDetail.getRelativePath().equals(relativePath)) {
                    fileDetail.setStatus(status);
                    fileDetail.setEndTime(new Date());

                    if (fileDetail.getStartTime() != null) {
                        long processingTime = fileDetail.getEndTime().getTime() - fileDetail.getStartTime().getTime();
                        fileDetail.setProcessingTime(processingTime);
                    }
                    
                    if (errorMessage != null) {
                        fileDetail.setErrorMessage(errorMessage);
                    }
                    break;
                }
            }
        }
        
        // 更新统计数据
        updateCounts();
        calculateProgress();
        calculateSyncSpeed();
        calculateEstimatedRemainingTime();
        generateSummary();
    }

    /**
     * 更新统计数据
     */
    private void updateCounts() {
        if (fileDetails != null) {
            completedFiles = 0;
            failedFiles = 0;
            skippedFiles = 0;
            pendingFiles = 0;
            
            for (FileProgressVO fileDetail : fileDetails) {
                switch (fileDetail.getStatus()) {
                    case "COMPLETED":
                        completedFiles++;
                        break;
                    case "FAILED":
                        failedFiles++;
                        break;
                    case "SKIPPED":
                        skippedFiles++;
                        break;
                    case "PENDING":
                    case "PROCESSING":
                        pendingFiles++;
                        break;
                }
            }
        }
    }

    /**
     * 判断同步是否完成
     */
    public boolean isCompleted() {
        return pendingFiles != null && pendingFiles == 0;
    }

    /**
     * 判断同步是否成功
     */
    public boolean isSuccess() {
        return isCompleted() && (failedFiles == null || failedFiles == 0);
    }
}
