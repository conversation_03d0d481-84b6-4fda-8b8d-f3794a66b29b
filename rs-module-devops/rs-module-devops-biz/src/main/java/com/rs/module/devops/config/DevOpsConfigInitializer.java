package com.rs.module.devops.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * DevOps配置初始化器
 * 用于在应用启动时检查配置是否正确加载
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class DevOpsConfigInitializer implements ApplicationRunner {

    @Autowired
    private DevOpsConfig devOpsConfig;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("=== DevOps配置初始化检查 ===");
        
        // 检查基本配置
        log.info("运行模式: {}", devOpsConfig.getRunMode());
        log.info("是否为客户端模式: {}", devOpsConfig.isClientMode());
        log.info("是否为服务端模式: {}", devOpsConfig.isServerMode());
        
        // 检查扫描配置
        if (devOpsConfig.getJarScan() != null) {
            log.info("扫描目录: {}", devOpsConfig.getJarScan().getScanDirectories());
            log.info("文件扩展名: {}", devOpsConfig.getJarScan().getFileExtensions());
        }
        
        // 检查应用重启配置
        if (devOpsConfig.getAppRestart() != null) {
            log.info("应用重启功能启用: {}", devOpsConfig.getAppRestart().getEnabled());
            
            Map<String, String> directoryAppMapping = devOpsConfig.getAppRestart().getDirectoryAppMapping();
            log.info("目录与应用映射关系: {}", directoryAppMapping);
            log.info("映射关系数量: {}", directoryAppMapping != null ? directoryAppMapping.size() : 0);
            
            if (directoryAppMapping != null && !directoryAppMapping.isEmpty()) {
                log.info("=== 映射关系详情 ===");
                directoryAppMapping.forEach((directory, appName) -> {
                    log.info("目录: [{}] -> 应用: [{}]", directory, appName);
                    log.info("目录长度: {}, 应用名长度: {}", directory.length(), appName.length());
                });
            } else {
                log.warn("未配置目录与应用的映射关系或配置为空");
            }
        }
        
        log.info("=== DevOps配置初始化检查完成 ===");
    }
}
