package com.rs.module.devops.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 文件信息VO
 *
 * <AUTHOR>
 */
@ApiModel(description = "文件信息VO")
@Data
public class FileInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "文件名")
    private String fileName;

    @ApiModelProperty(value = "文件大小（字节）")
    private Long fileSize;

    @ApiModelProperty(value = "文件修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastModified;

    @ApiModelProperty(value = "MD5校验值")
    private String md5Hash;

    @ApiModelProperty(value = "SHA1校验值")
    private String sha1Hash;

    @ApiModelProperty(value = "相对路径")
    private String relativePath;

    @ApiModelProperty(value = "是否为目录")
    private Boolean isDirectory;

    @ApiModelProperty(value = "文件扩展名")
    private String fileExtension;

    public FileInfoVO() {
    }

    public FileInfoVO(String filePath, String fileName, Long fileSize, Date lastModified,
                     String md5Hash, String sha1Hash, String relativePath, Boolean isDirectory, String fileExtension) {
        this.filePath = filePath;
        this.fileName = fileName;
        this.fileSize = fileSize;
        this.lastModified = lastModified;
        this.md5Hash = md5Hash;
        this.sha1Hash = sha1Hash;
        this.relativePath = relativePath;
        this.isDirectory = isDirectory;
        this.fileExtension = fileExtension;
    }
}
