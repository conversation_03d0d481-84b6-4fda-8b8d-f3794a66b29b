package com.rs.module.devops.service;

import com.rs.module.devops.vo.CompareReportVO;
import com.rs.module.devops.vo.FileInfoVO;

import java.util.List;

/**
 * DevOps服务端服务接口
 *
 * <AUTHOR>
 */
public interface DevOpsServerService {

    /**
     * 扫描服务端文件
     *
     * @return 文件信息列表
     */
    List<FileInfoVO> scanServerFiles();

    /**
     * 对比客户端和服务端文件
     *
     * @param clientFiles 客户端文件列表
     * @param clientIp 客户端IP
     * @return 对比报告
     */
    CompareReportVO compareFiles(List<FileInfoVO> clientFiles, String clientIp);

    /**
     * 获取文件内容
     *
     * @param filePath 文件路径
     * @return 文件字节数组
     */
    byte[] getFileContent(String filePath);

    /**
     * 检查文件是否存在
     *
     * @param filePath 文件路径
     * @return 是否存在
     */
    Boolean fileExists(String filePath);

    /**
     * 获取文件信息
     *
     * @param filePath 文件路径
     * @return 文件信息
     */
    FileInfoVO getFileInfo(String filePath);

    /**
     * 记录同步日志
     *
     * @param clientIp 客户端IP
     * @param operation 操作类型
     * @param filePath 文件路径
     * @param result 操作结果
     */
    void logSyncOperation(String clientIp, String operation, String filePath, Boolean result);

    /**
     * 获取同步统计信息
     *
     * @param clientIp 客户端IP（可选）
     * @return 统计信息
     */
    Object getSyncStatistics(String clientIp);
}
