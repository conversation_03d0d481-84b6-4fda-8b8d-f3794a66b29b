package com.rs.module.devops.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 应用重启配置VO
 *
 * <AUTHOR>
 */
@ApiModel(description = "应用重启配置VO")
@Data
public class AppRestartConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "是否启用应用重启功能")
    private Boolean enabled;

    @ApiModelProperty(value = "是否在Docker环境中运行")
    private Boolean dockerMode;

    @ApiModelProperty(value = "Docker主机命令前缀")
    private String dockerHostCommand;

    @ApiModelProperty(value = "命令执行超时时间（秒）")
    private Integer commandTimeout;

    @ApiModelProperty(value = "目录与应用的映射关系")
    private Map<String, String> directoryAppMapping;

    @ApiModelProperty(value = "重启命令模板")
    private String restartCommandTemplate;

    @ApiModelProperty(value = "是否启用重启前延迟")
    private Boolean enableRestartDelay;

    @ApiModelProperty(value = "重启前延迟时间（秒）")
    private Integer restartDelaySeconds;

    @ApiModelProperty(value = "是否记录重启日志")
    private Boolean enableRestartLog;
}
