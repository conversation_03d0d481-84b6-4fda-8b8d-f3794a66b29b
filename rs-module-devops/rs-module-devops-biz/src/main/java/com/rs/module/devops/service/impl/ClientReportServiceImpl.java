package com.rs.module.devops.service.impl;

import com.rs.module.devops.service.ClientReportService;
import com.rs.module.devops.vo.ClientReportVO;
import com.rs.module.devops.vo.CompareReportVO;
import com.rs.module.devops.vo.RestartResultVO;
import com.rs.module.devops.vo.SyncProgressVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 客户端报告管理服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ClientReportServiceImpl implements ClientReportService {

    /**
     * 客户端报告存储Map，key为客户端IP，value为客户端报告
     */
    private final Map<String, ClientReportVO> clientReports = new ConcurrentHashMap<>();

    @Override
    public void saveCompareReport(String clientIp, CompareReportVO compareReport) {
        log.info("保存客户端 {} 的对比报告", clientIp);
        
        ClientReportVO clientReport = clientReports.computeIfAbsent(clientIp, ClientReportVO::new);
        clientReport.updateCompareReport(compareReport);
        
        log.info("客户端 {} 对比报告已保存: {}", clientIp, compareReport.getSummary());
    }

    @Override
    public void updateSyncStatus(String clientIp, Boolean syncResult) {
        log.info("更新客户端 {} 的同步状态: {}", clientIp, syncResult ? "成功" : "失败");
        
        ClientReportVO clientReport = clientReports.get(clientIp);
        if (clientReport != null) {
            clientReport.updateSyncStatus(syncResult);
            log.info("客户端 {} 同步状态已更新", clientIp);
        } else {
            log.warn("未找到客户端 {} 的报告，无法更新同步状态", clientIp);
        }
    }

    @Override
    public void addRestartLog(String clientIp, RestartResultVO restartResult) {
        log.info("添加客户端 {} 的重启日志", clientIp);
        
        ClientReportVO clientReport = clientReports.get(clientIp);
        if (clientReport == null) {
            log.warn("未找到客户端 {} 的报告，创建新报告", clientIp);
            clientReport = new ClientReportVO(clientIp);
            clientReports.put(clientIp, clientReport);
        }

        // 转换重启结果为重启日志
        if (restartResult != null && restartResult.getRestartDetails() != null) {
            for (RestartResultVO.AppRestartDetailVO detail : restartResult.getRestartDetails()) {
                ClientReportVO.RestartLogVO restartLog = new ClientReportVO.RestartLogVO();
                restartLog.setRestartTime(new Date());
                restartLog.setAppName(detail.getAppName());
                restartLog.setSuccess(detail.getSuccess());
                restartLog.setErrorMessage(detail.getErrorMessage());
                restartLog.setExecutionTime(detail.getExecutionTime());
                restartLog.setRestartType("AUTO_RESTART");
                
                // 设置相关文件（从对比报告中获取）
                if (clientReport.getCompareReport() != null) {
                    List<String> relatedFiles = new ArrayList<>();
                    if (clientReport.getCompareReport().getFilesToAdd() != null) {
                        relatedFiles.addAll(clientReport.getCompareReport().getFilesToAdd()
                                .stream().map(f -> f.getRelativePath()).collect(Collectors.toList()));
                    }
                    if (clientReport.getCompareReport().getFilesToUpdate() != null) {
                        relatedFiles.addAll(clientReport.getCompareReport().getFilesToUpdate()
                                .stream().map(f -> f.getRelativePath()).collect(Collectors.toList()));
                    }
                    restartLog.setRelatedFiles(relatedFiles);
                }
                
                clientReport.addRestartLog(restartLog);
            }
        }
        
        log.info("客户端 {} 重启日志已添加", clientIp);
    }

    @Override
    public ClientReportVO getClientReport(String clientIp) {
        return clientReports.get(clientIp);
    }

    @Override
    public List<ClientReportVO> getAllClientReports() {
        return new ArrayList<>(clientReports.values());
    }

    @Override
    public Map<String, Object> getClientReportSummary() {
        Map<String, Object> summary = new HashMap<>();
        
        summary.put("totalClients", clientReports.size());
        summary.put("lastUpdateTime", new Date());
        
        Map<String, Integer> statusCount = new HashMap<>();
        statusCount.put("PENDING", 0);
        statusCount.put("COMPARED", 0);
        statusCount.put("SYNC_SUCCESS", 0);
        statusCount.put("SYNC_FAILED", 0);
        
        List<Map<String, Object>> clientSummaries = new ArrayList<>();
        
        for (ClientReportVO report : clientReports.values()) {
            // 统计状态
            String status = report.getSyncStatus();
            statusCount.put(status, statusCount.getOrDefault(status, 0) + 1);
            
            // 客户端摘要
            Map<String, Object> clientSummary = new HashMap<>();
            clientSummary.put("clientIp", report.getClientIp());
            clientSummary.put("status", status);
            clientSummary.put("statusDescription", report.getStatusDescription());
            clientSummary.put("lastUpdateTime", report.getLastUpdateTime());
            clientSummary.put("lastSyncTime", report.getLastSyncTime());
            
            if (report.getCompareReport() != null) {
                clientSummary.put("hasDifferences", report.getCompareReport().getHasDifferences());
                clientSummary.put("summary", report.getCompareReport().getSummary());
            }
            
            if (report.getRestartLogs() != null) {
                clientSummary.put("restartLogCount", report.getRestartLogs().size());
            }
            
            clientSummaries.add(clientSummary);
        }
        
        summary.put("statusCount", statusCount);
        summary.put("clients", clientSummaries);
        
        return summary;
    }

    @Override
    public Integer cleanExpiredReports(Integer retentionHours) {
        if (retentionHours == null || retentionHours <= 0) {
            retentionHours = 24; // 默认保留24小时
        }
        
        Date expireTime = new Date(System.currentTimeMillis() - retentionHours * 60 * 60 * 1000L);

        List<String> expiredClients = clientReports.entrySet().stream()
                .filter(entry -> entry.getValue().getLastUpdateTime().before(expireTime))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        
        for (String clientIp : expiredClients) {
            clientReports.remove(clientIp);
            log.info("清理过期的客户端报告: {}", clientIp);
        }
        
        log.info("清理完成，共清理 {} 个过期客户端报告", expiredClients.size());
        return expiredClients.size();
    }

    @Override
    public Boolean removeClientReport(String clientIp) {
        ClientReportVO removed = clientReports.remove(clientIp);
        if (removed != null) {
            log.info("删除客户端 {} 的报告", clientIp);
            return true;
        } else {
            log.warn("未找到客户端 {} 的报告", clientIp);
            return false;
        }
    }

    @Override
    public void updateSyncProgress(String clientIp, SyncProgressVO syncProgress) {
        log.info("更新客户端 {} 的同步进度", clientIp);

        ClientReportVO clientReport = clientReports.get(clientIp);
        if (clientReport == null) {
            log.warn("未找到客户端 {} 的报告，创建新报告", clientIp);
            clientReport = new ClientReportVO(clientIp);
            clientReports.put(clientIp, clientReport);
        }

        clientReport.updateSyncProgress(syncProgress);
        log.info("客户端 {} 同步进度已更新: {}", clientIp, syncProgress.getSummary());
    }

    @Override
    public SyncProgressVO getSyncProgress(String clientIp) {
        ClientReportVO clientReport = clientReports.get(clientIp);
        if (clientReport != null) {
            return clientReport.getSyncProgress();
        }
        return null;
    }

    @Override
    public Boolean pauseCompare(String clientIp, String reason) {
        log.info("暂停客户端 {} 的比较功能，原因: {}", clientIp, reason);

        ClientReportVO clientReport = clientReports.get(clientIp);
        if (clientReport == null) {
            log.info("客户端 {} 的报告不存在，创建新报告并暂停", clientIp);
            clientReport = new ClientReportVO(clientIp);
            clientReports.put(clientIp, clientReport);
        }

        clientReport.pauseCompare(reason);
        log.info("客户端 {} 的比较功能已暂停", clientIp);
        return true;
    }

    @Override
    public Boolean resumeCompare(String clientIp) {
        log.info("恢复客户端 {} 的比较功能", clientIp);

        ClientReportVO clientReport = clientReports.get(clientIp);
        if (clientReport == null) {
            log.warn("客户端 {} 的报告不存在，无法恢复", clientIp);
            return false;
        }

        clientReport.resumeCompare();
        log.info("客户端 {} 的比较功能已恢复", clientIp);
        return true;
    }

    @Override
    public Boolean isComparePaused(String clientIp) {
        ClientReportVO clientReport = clientReports.get(clientIp);
        if (clientReport != null) {
            return Boolean.TRUE.equals(clientReport.getComparePaused());
        }
        return false;
    }
}
