package com.rs.module.devops.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 应用重启结果VO
 *
 * <AUTHOR>
 */
@ApiModel(description = "应用重启结果VO")
@Data
public class RestartResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "重启时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date restartTime;

    @ApiModelProperty(value = "客户端IP")
    private String clientIp;

    @ApiModelProperty(value = "重启的应用列表")
    private List<AppRestartDetailVO> restartDetails;

    @ApiModelProperty(value = "总体重启结果")
    private Boolean overallSuccess;

    @ApiModelProperty(value = "重启摘要信息")
    private String summary;

    public RestartResultVO() {
        this.restartTime = new Date();
        this.overallSuccess = true;
    }

    @ApiModel(description = "应用重启详情VO")
    @Data
    public static class AppRestartDetailVO implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "应用名称")
        private String appName;

        @ApiModelProperty(value = "扫描目录")
        private String directory;

        @ApiModelProperty(value = "重启命令")
        private String restartCommand;

        @ApiModelProperty(value = "重启结果")
        private Boolean success;

        @ApiModelProperty(value = "错误信息")
        private String errorMessage;

        @ApiModelProperty(value = "命令输出")
        private String commandOutput;

        @ApiModelProperty(value = "执行耗时（毫秒）")
        private Long executionTime;
    }

    /**
     * 生成摘要信息
     */
    public void generateSummary() {
        if (restartDetails == null || restartDetails.isEmpty()) {
            this.summary = "无应用需要重启";
            return;
        }

        long successCount = restartDetails.stream().mapToLong(detail -> detail.getSuccess() ? 1 : 0).sum();
        long failCount = restartDetails.size() - successCount;

        this.summary = String.format("应用重启完成：成功 %d 个，失败 %d 个", successCount, failCount);
        this.overallSuccess = failCount == 0;
    }
}
