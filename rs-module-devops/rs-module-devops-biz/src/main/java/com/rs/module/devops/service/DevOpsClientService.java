package com.rs.module.devops.service;

import com.rs.module.devops.vo.CompareReportVO;
import com.rs.module.devops.vo.FileInfoVO;
import com.rs.module.devops.vo.RestartResultVO;
import com.rs.module.devops.vo.SyncProgressVO;

import java.util.List;

/**
 * DevOps客户端服务接口
 *
 * <AUTHOR>
 */
public interface DevOpsClientService {

    /**
     * 扫描本地文件
     *
     * @return 文件信息列表
     */
    List<FileInfoVO> scanLocalFiles();

    /**
     * 提交扫描结果到服务端
     *
     * @param fileInfoList 文件信息列表
     * @return 对比报告
     */
    CompareReportVO submitScanResult(List<FileInfoVO> fileInfoList);

    /**
     * 从服务端下载文件
     *
     * @param fileInfo 文件信息
     * @return 是否下载成功
     */
    Boolean downloadFile(FileInfoVO fileInfo);

    /**
     * 备份本地文件
     *
     * @param fileInfo 文件信息
     * @return 备份文件路径
     */
    String backupFile(FileInfoVO fileInfo);

    /**
     * 执行文件同步
     *
     * @param compareReport 对比报告
     * @return 同步结果
     */
    Boolean executeSync(CompareReportVO compareReport);

    /**
     * 清理过期备份文件
     *
     * @return 清理的文件数量
     */
    Integer cleanExpiredBackups();

    /**
     * 获取客户端IP地址
     *
     * @return IP地址
     */
    String getClientIp();

    /**
     * 验证文件完整性
     *
     * @param filePath 文件路径
     * @param expectedMd5 期望的MD5值
     * @return 是否验证通过
     */
    Boolean verifyFileIntegrity(String filePath, String expectedMd5);

    /**
     * 执行文件同步并自动重启相关应用
     *
     * @param compareReport 对比报告
     * @return 同步和重启结果
     */
    RestartResultVO executeSyncWithRestart(CompareReportVO compareReport);

    /**
     * 向服务端提交同步状态
     *
     * @param syncResult 同步结果
     * @return 是否提交成功
     */
    Boolean submitSyncStatus(Boolean syncResult);

    /**
     * 向服务端提交重启日志
     *
     * @param restartResult 重启结果
     * @return 是否提交成功
     */
    Boolean submitRestartLog(RestartResultVO restartResult);

    /**
     * 向服务端提交同步进度
     *
     * @param syncProgress 同步进度
     * @return 是否提交成功
     */
    Boolean submitSyncProgress(SyncProgressVO syncProgress);

    /**
     * 执行带进度跟踪的文件同步
     *
     * @param compareReport 对比报告
     * @return 同步结果
     */
    Boolean executeSyncWithProgress(CompareReportVO compareReport);

    /**
     * 根据日期后缀回退文件
     *
     * @param dateSuffix 日期后缀，如 "20250801"
     * @return 回退结果信息
     */
    List<String> rollbackFilesByDateSuffix(String dateSuffix);
}
