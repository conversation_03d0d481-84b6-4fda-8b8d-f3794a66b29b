package com.rs.module.devops.dto;

import com.rs.module.devops.vo.FileInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 文件对比请求DTO
 *
 * <AUTHOR>
 */
@ApiModel(description = "文件对比请求DTO")
@Data
public class CompareFilesRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户端文件列表", required = true)
    @NotNull(message = "客户端文件列表不能为空")
    private List<FileInfoVO> clientFiles;

    @ApiModelProperty(value = "客户端IP地址")
    private String clientIp;

    public CompareFilesRequestDTO() {
    }

    public CompareFilesRequestDTO(List<FileInfoVO> clientFiles, String clientIp) {
        this.clientFiles = clientFiles;
        this.clientIp = clientIp;
    }
}
