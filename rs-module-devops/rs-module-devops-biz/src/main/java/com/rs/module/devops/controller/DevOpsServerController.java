package com.rs.module.devops.controller;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.devops.dto.CompareFilesRequestDTO;
import com.rs.module.devops.dto.PauseCompareRequestDTO;
import com.rs.module.devops.dto.ResumeCompareRequestDTO;
import com.rs.module.devops.service.ClientReportService;
import com.rs.module.devops.service.DevOpsServerService;
import com.rs.module.devops.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * DevOps服务端控制器
 *
 * <AUTHOR>
 */
@Api(tags = "DevOps文件同步 - 服务端")
@RestController
@RequestMapping("/devops/server")
@Validated
@Slf4j
public class DevOpsServerController {

    @Autowired
    private DevOpsServerService devOpsServerService;

    @Autowired
    private ClientReportService clientReportService;

    @GetMapping("/scan")
    @ApiOperation(value = "扫描服务端文件")
    public CommonResult<List<FileInfoVO>> scanServerFiles(HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("服务端开始扫描文件，请求来自: {}", clientIp);

            List<FileInfoVO> fileInfoList = devOpsServerService.scanServerFiles();

            log.info("服务端扫描完成，文件数量: {}，请求来自: {}", fileInfoList.size(), clientIp);
            return CommonResult.success(fileInfoList, "扫描完成");
        } catch (Exception e) {
            log.error("扫描服务端文件时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("扫描失败: " + e.getMessage());
        }
    }

    @PostMapping("/compare")
    @ApiOperation(value = "对比客户端和服务端文件")
    public CommonResult<CompareReportVO> compareFiles(@RequestBody @Validated CompareFilesRequestDTO request,
                                                     HttpServletRequest httpRequest) {
        try {
            String clientIp = getClientIp(httpRequest);

            List<FileInfoVO> clientFiles = request.getClientFiles();
            String requestClientIp = request.getClientIp();

            // 优先使用请求中的IP，如果没有则使用HTTP请求的IP
            String actualClientIp = requestClientIp != null ? requestClientIp : clientIp;

            log.info("开始对比文件，客户端: {}，文件数量: {}", actualClientIp, clientFiles.size());

            // 检查客户端是否被暂停比较
            if (clientReportService.isComparePaused(actualClientIp)) {
                log.warn("客户端 {} 的比较功能已被暂停，拒绝执行比较", actualClientIp);
                return CommonResult.error("比较功能已暂停，请联系管理员恢复");
            }

            CompareReportVO compareReport = devOpsServerService.compareFiles(clientFiles, actualClientIp);

            log.info("文件对比完成，客户端: {}，{}", actualClientIp, compareReport.getSummary());
            return CommonResult.success(compareReport, "对比完成");
        } catch (Exception e) {
            log.error("对比文件时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("对比失败: " + e.getMessage());
        }
    }

    @GetMapping("/download")
    @ApiOperation(value = "下载文件")
    public ResponseEntity<byte[]> downloadFile(@RequestParam String filePath,
                                              HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("客户端 {} 请求下载文件: {}", clientIp, filePath);

            byte[] fileContent = devOpsServerService.getFileContent(filePath);
            if (fileContent == null) {
                log.warn("文件不存在或读取失败: {}", filePath);
                return ResponseEntity.notFound().build();
            }

            // 记录下载日志
            devOpsServerService.logSyncOperation(clientIp, "DOWNLOAD", filePath, true);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

            // 获取文件名
            String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
            headers.setContentDispositionFormData("attachment", encodedFileName);

            log.info("文件下载成功，客户端: {}，文件: {}，大小: {} 字节", clientIp, filePath, fileContent.length);

            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(fileContent.length)
                    .body(fileContent);
        } catch (Exception e) {
            log.error("下载文件时发生错误: {}", e.getMessage(), e);
            return ResponseEntity.status(500).build();
        }
    }

    @GetMapping("/file-exists")
    @ApiOperation(value = "检查文件是否存在")
    public CommonResult<Boolean> fileExists(@RequestParam String filePath,
                                           HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("客户端 {} 检查文件是否存在: {}", clientIp, filePath);

            Boolean exists = devOpsServerService.fileExists(filePath);

            log.info("文件存在性检查完成，客户端: {}，文件: {}，存在: {}", clientIp, filePath, exists);
            return CommonResult.success(exists, "检查完成");
        } catch (Exception e) {
            log.error("检查文件是否存在时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("检查失败: " + e.getMessage());
        }
    }

    @GetMapping("/file-info")
    @ApiOperation(value = "获取文件信息")
    public CommonResult<FileInfoVO> getFileInfo(@RequestParam String filePath,
                                               HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("客户端 {} 获取文件信息: {}", clientIp, filePath);

            FileInfoVO fileInfo = devOpsServerService.getFileInfo(filePath);

            if (fileInfo != null) {
                log.info("文件信息获取成功，客户端: {}，文件: {}", clientIp, filePath);
                return CommonResult.success(fileInfo, "获取成功");
            } else {
                log.warn("文件不存在，客户端: {}，文件: {}", clientIp, filePath);
                return CommonResult.error("文件不存在");
            }
        } catch (Exception e) {
            log.error("获取文件信息时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("获取失败: " + e.getMessage());
        }
    }

    @GetMapping("/statistics")
    @ApiOperation(value = "获取同步统计信息")
    public CommonResult<Object> getSyncStatistics(@RequestParam(required = false) String clientIp,
                                                  HttpServletRequest request) {
        try {
            String requestClientIp = getClientIp(request);
            log.info("获取同步统计信息，请求来自: {}，查询客户端: {}", requestClientIp, clientIp);

            Object statistics = devOpsServerService.getSyncStatistics(clientIp);

            log.info("统计信息获取成功，请求来自: {}", requestClientIp);
            return CommonResult.success(statistics, "获取成功");
        } catch (Exception e) {
            log.error("获取同步统计信息时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("获取失败: " + e.getMessage());
        }
    }

    @PostMapping("/log")
    @ApiOperation(value = "记录同步操作日志")
    public CommonResult<Void> logSyncOperation(@RequestParam String clientIp,
                                              @RequestParam String operation,
                                              @RequestParam String filePath,
                                              @RequestParam Boolean result,
                                              HttpServletRequest request) {
        try {
            String requestClientIp = getClientIp(request);
            log.info("记录同步日志，请求来自: {}，客户端: {}，操作: {}，文件: {}，结果: {}",
                    requestClientIp, clientIp, operation, filePath, result);

            devOpsServerService.logSyncOperation(clientIp, operation, filePath, result);

            return CommonResult.success(null, "记录成功");
        } catch (Exception e) {
            log.error("记录同步操作日志时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("记录失败: " + e.getMessage());
        }
    }

    @GetMapping("/health")
    @ApiOperation(value = "健康检查")
    public CommonResult<String> healthCheck(HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.debug("健康检查，请求来自: {}", clientIp);

            return CommonResult.success("OK", "服务正常");
        } catch (Exception e) {
            log.error("健康检查时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("服务异常: " + e.getMessage());
        }
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 如果是多级代理，取第一个IP
        if (ip != null && ip.contains(",")) {
            ip = ip.substring(0, ip.indexOf(",")).trim();
        }

        return ip;
    }

    @GetMapping("/client-report")
    @ApiOperation(value = "获取指定客户端的报告")
    public CommonResult<ClientReportVO> getClientReport(@RequestParam String clientIp,
                                                       HttpServletRequest request) {
        try {
            String requestClientIp = getClientIp(request);
            log.info("获取客户端报告，请求来自: {}，查询客户端: {}", requestClientIp, clientIp);

            ClientReportVO clientReport = clientReportService.getClientReport(clientIp);

            if (clientReport != null) {
                log.info("客户端报告获取成功，客户端: {}", clientIp);
                return CommonResult.success(clientReport, "获取成功");
            } else {
                log.warn("未找到客户端报告，客户端: {}", clientIp);
                return CommonResult.error("未找到客户端报告");
            }
        } catch (Exception e) {
            log.error("获取客户端报告时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("获取失败: " + e.getMessage());
        }
    }

    @GetMapping("/client-reports")
    @ApiOperation(value = "获取所有客户端报告")
    public CommonResult<List<ClientReportVO>> getAllClientReports(HttpServletRequest request) {
        try {
            String requestClientIp = getClientIp(request);
            log.info("获取所有客户端报告，请求来自: {}", requestClientIp);

            List<ClientReportVO> clientReports = clientReportService.getAllClientReports();

            log.info("客户端报告列表获取成功，共 {} 个客户端", clientReports.size());
            return CommonResult.success(clientReports, "获取成功");
        } catch (Exception e) {
            log.error("获取客户端报告列表时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("获取失败: " + e.getMessage());
        }
    }

    @GetMapping("/client-reports/summary")
    @ApiOperation(value = "获取客户端报告摘要")
    public CommonResult<Map<String, Object>> getClientReportSummary(HttpServletRequest request) {
        try {
            String requestClientIp = getClientIp(request);
            log.info("获取客户端报告摘要，请求来自: {}", requestClientIp);

            Map<String, Object> summary = clientReportService.getClientReportSummary();

            log.info("客户端报告摘要获取成功");
            return CommonResult.success(summary, "获取成功");
        } catch (Exception e) {
            log.error("获取客户端报告摘要时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("获取失败: " + e.getMessage());
        }
    }

    @PostMapping("/sync-status")
    @ApiOperation(value = "更新客户端同步状态")
    public CommonResult<Void> updateSyncStatus(@RequestParam String clientIp,
                                              @RequestParam Boolean syncResult,
                                              HttpServletRequest request) {
        try {
            String requestClientIp = getClientIp(request);
            log.info("更新同步状态，请求来自: {}，客户端: {}，结果: {}",
                    requestClientIp, clientIp, syncResult ? "成功" : "失败");

            clientReportService.updateSyncStatus(clientIp, syncResult);

            return CommonResult.success(null, "更新成功");
        } catch (Exception e) {
            log.error("更新同步状态时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("更新失败: " + e.getMessage());
        }
    }

    @PostMapping("/restart-log")
    @ApiOperation(value = "提交客户端重启日志")
    public CommonResult<Void> submitRestartLog(@RequestBody RestartResultVO restartResult,
                                              HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("提交重启日志，客户端: {}", clientIp);

            clientReportService.addRestartLog(clientIp, restartResult);

            log.info("重启日志提交成功，客户端: {}", clientIp);
            return CommonResult.success(null, "提交成功");
        } catch (Exception e) {
            log.error("提交重启日志时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("提交失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/client-report")
    @ApiOperation(value = "删除指定客户端的报告")
    public CommonResult<Void> removeClientReport(@RequestParam String clientIp,
                                                HttpServletRequest request) {
        try {
            String requestClientIp = getClientIp(request);
            log.info("删除客户端报告，请求来自: {}，目标客户端: {}", requestClientIp, clientIp);

            Boolean result = clientReportService.removeClientReport(clientIp);

            if (result) {
                return CommonResult.success(null, "删除成功");
            } else {
                return CommonResult.error("未找到客户端报告");
            }
        } catch (Exception e) {
            log.error("删除客户端报告时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("删除失败: " + e.getMessage());
        }
    }

    @PostMapping("/clean-expired-reports")
    @ApiOperation(value = "清理过期的客户端报告")
    public CommonResult<Integer> cleanExpiredReports(@RequestParam(defaultValue = "24") Integer retentionHours,
                                                    HttpServletRequest request) {
        try {
            String requestClientIp = getClientIp(request);
            log.info("清理过期报告，请求来自: {}，保留时间: {} 小时", requestClientIp, retentionHours);

            Integer cleanedCount = clientReportService.cleanExpiredReports(retentionHours);

            log.info("过期报告清理完成，共清理 {} 个", cleanedCount);
            return CommonResult.success(cleanedCount, "清理完成");
        } catch (Exception e) {
            log.error("清理过期报告时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("清理失败: " + e.getMessage());
        }
    }

    @PostMapping("/sync-progress")
    @ApiOperation(value = "更新客户端同步进度")
    public CommonResult<Void> updateSyncProgress(@RequestBody SyncProgressVO syncProgress,
                                                HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("更新同步进度，客户端: {}", clientIp);

            // 设置客户端IP
            syncProgress.setClientIp(clientIp);

            clientReportService.updateSyncProgress(clientIp, syncProgress);

            log.info("同步进度更新成功，客户端: {}，进度: {}%",
                    clientIp, syncProgress.getProgressPercentage());
            return CommonResult.success(null, "更新成功");
        } catch (Exception e) {
            log.error("更新同步进度时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("更新失败: " + e.getMessage());
        }
    }

    @GetMapping("/sync-progress")
    @ApiOperation(value = "获取客户端同步进度")
    public CommonResult<SyncProgressVO> getSyncProgress(@RequestParam String clientIp,
                                                       HttpServletRequest request) {
        try {
            String requestClientIp = getClientIp(request);
            log.info("获取同步进度，请求来自: {}，查询客户端: {}", requestClientIp, clientIp);

            SyncProgressVO syncProgress = clientReportService.getSyncProgress(clientIp);

            if (syncProgress != null) {
                log.info("同步进度获取成功，客户端: {}", clientIp);
                return CommonResult.success(syncProgress, "获取成功");
            } else {
                log.warn("未找到客户端 {} 的同步进度", clientIp);
                return CommonResult.error("未找到同步进度");
            }
        } catch (Exception e) {
            log.error("获取同步进度时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("获取失败: " + e.getMessage());
        }
    }

    @PostMapping("/pause-compare")
    @ApiOperation(value = "暂停指定客户端的比较功能")
    public CommonResult<Void> pauseCompare(@RequestBody @Validated PauseCompareRequestDTO request,
                                          HttpServletRequest httpRequest) {
        try {
            String requestClientIp = getClientIp(httpRequest);
            String targetClientIp = request.getClientIp();
            String reason = request.getReason();

            log.info("暂停比较请求，请求来自: {}，目标客户端: {}，原因: {}", requestClientIp, targetClientIp, reason);

            Boolean result = clientReportService.pauseCompare(targetClientIp, reason);

            if (result) {
                log.info("客户端 {} 的比较功能已暂停", targetClientIp);
                return CommonResult.success(null, "暂停成功");
            } else {
                log.error("暂停客户端 {} 的比较功能失败", targetClientIp);
                return CommonResult.error("暂停失败");
            }
        } catch (Exception e) {
            log.error("暂停比较功能时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("暂停失败: " + e.getMessage());
        }
    }

    @PostMapping("/resume-compare")
    @ApiOperation(value = "恢复指定客户端的比较功能")
    public CommonResult<Void> resumeCompare(@RequestBody @Validated ResumeCompareRequestDTO request,
                                           HttpServletRequest httpRequest) {
        try {
            String requestClientIp = getClientIp(httpRequest);
            String targetClientIp = request.getClientIp();

            log.info("恢复比较请求，请求来自: {}，目标客户端: {}", requestClientIp, targetClientIp);

            Boolean result = clientReportService.resumeCompare(targetClientIp);

            if (result) {
                log.info("客户端 {} 的比较功能已恢复", targetClientIp);
                return CommonResult.success(null, "恢复成功");
            } else {
                log.error("恢复客户端 {} 的比较功能失败", targetClientIp);
                return CommonResult.error("恢复失败");
            }
        } catch (Exception e) {
            log.error("恢复比较功能时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("恢复失败: " + e.getMessage());
        }
    }

    @GetMapping("/compare-status")
    @ApiOperation(value = "查询指定客户端的比较状态")
    public CommonResult<Map<String, Object>> getCompareStatus(@RequestParam String clientIp,
                                                             HttpServletRequest request) {
        try {
            String requestClientIp = getClientIp(request);
            log.info("查询比较状态，请求来自: {}，查询客户端: {}", requestClientIp, clientIp);

            Boolean isPaused = clientReportService.isComparePaused(clientIp);
            ClientReportVO clientReport = clientReportService.getClientReport(clientIp);

            Map<String, Object> status = new java.util.HashMap<>();
            status.put("clientIp", clientIp);
            status.put("comparePaused", isPaused);

            if (clientReport != null) {
                status.put("pauseTime", clientReport.getPauseTime());
                status.put("pauseReason", clientReport.getPauseReason());
                status.put("statusDescription", clientReport.getStatusDescription());
            } else {
                status.put("statusDescription", "未找到客户端报告");
            }

            log.info("比较状态查询成功，客户端: {}，暂停状态: {}", clientIp, isPaused);
            return CommonResult.success(status, "查询成功");
        } catch (Exception e) {
            log.error("查询比较状态时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("查询失败: " + e.getMessage());
        }
    }
}
