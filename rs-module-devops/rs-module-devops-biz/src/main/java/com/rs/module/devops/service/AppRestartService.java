package com.rs.module.devops.service;

import com.rs.module.devops.vo.CompareReportVO;
import com.rs.module.devops.vo.RestartResultVO;

import java.util.Set;

/**
 * 应用重启服务接口
 *
 * <AUTHOR>
 */
public interface AppRestartService {

    /**
     * 根据同步报告确定需要重启的应用
     *
     * @param compareReport 文件对比报告
     * @return 需要重启的应用名称集合
     */
    Set<String> determineAppsToRestart(CompareReportVO compareReport);

    /**
     * 重启指定的应用列表
     *
     * @param appNames 应用名称列表
     * @param clientIp 客户端IP
     * @return 重启结果
     */
    RestartResultVO restartApps(Set<String> appNames, String clientIp);

    /**
     * 重启单个应用
     *
     * @param appName 应用名称
     * @param directory 对应的目录
     * @return 重启详情
     */
    RestartResultVO.AppRestartDetailVO restartSingleApp(String appName, String directory);

    /**
     * 执行重启命令
     *
     * @param command 重启命令
     * @param appName 应用名称
     * @return 重启详情
     */
    RestartResultVO.AppRestartDetailVO executeRestartCommand(String command, String appName);

    /**
     * 检查应用重启功能是否启用
     *
     * @return 是否启用
     */
    Boolean isRestartEnabled();

    /**
     * 获取目录对应的应用名称
     *
     * @param directory 目录路径
     * @return 应用名称，如果没有配置则返回null
     */
    String getAppNameByDirectory(String directory);

    /**
     * 根据文件同步结果自动重启相关应用
     *
     * @param compareReport 文件对比报告
     * @param syncSuccess 同步是否成功
     * @param clientIp 客户端IP
     * @return 重启结果，如果没有需要重启的应用则返回null
     */
    RestartResultVO autoRestartAfterSync(CompareReportVO compareReport, Boolean syncSuccess, String clientIp);
}
