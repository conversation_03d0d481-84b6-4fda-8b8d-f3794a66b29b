package com.rs.module.devops.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 暂停比较请求DTO
 *
 * <AUTHOR>
 */
@ApiModel(description = "暂停比较请求DTO")
@Data
public class PauseCompareRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户端IP地址", required = true)
    @NotBlank(message = "客户端IP地址不能为空")
    private String clientIp;

    @ApiModelProperty(value = "暂停原因")
    private String reason;

    public PauseCompareRequestDTO() {
    }

    public PauseCompareRequestDTO(String clientIp, String reason) {
        this.clientIp = clientIp;
        this.reason = reason;
    }
}
