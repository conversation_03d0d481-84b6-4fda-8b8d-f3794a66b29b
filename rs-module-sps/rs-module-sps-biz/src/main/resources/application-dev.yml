--- ########################## 数据库相关配置 ##########################
spring:
  datasource:
    druid:
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
        login-username: # 控制台管理用户名和密码
        login-password:
      filter:
        stat:
          enabled: true
          log-slow-sql: ${conf.datasource.druid.log-slow-sql} # 慢 SQL 记录
          slow-sql-millis: ${conf.datasource.druid.slow-sql-millis}
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic:
      druid:
        initial-size: ${conf.datasource.dynamic.druid.initial-size} # 初始连接数
        min-idle:  ${conf.datasource.dynamic.druid.min-idle} # 最小连接池数量
        max-active:  ${conf.datasource.dynamic.druid.max-active} # 最大连接池数量
        max-wait:  ${conf.datasource.dynamic.druid.max-wait} # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 FROM DUAL # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
      datasource:
        master:
          url: ${conf.datasource.dynamic.master.url}
          username: ${conf.datasource.dynamic.master.username}
          password: ${conf.datasource.dynamic.master.password}
        bsp: #模拟从库，可根据需要修改
          lazy: true #开启懒加载，保证启动速度
          url: ${conf.datasource.dynamic.bsp.url}
          username: ${conf.datasource.dynamic.bsp.username}
          password: ${conf.datasource.dynamic.bsp.password}

  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  redis:
    host: ${conf.redis.host}
    port: ${conf.redis.port}
    database: ${conf.redis.database}
    password: ${conf.redis.password}
    timeout: ${conf.redis.timeout}  # 连接超时时长（毫秒）
    max-redirects: ${conf.redis.max-redirects}
    lettuce:
      pool:
        max-active: ${conf.redis.lettuce.pool.max-active}  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: ${conf.redis.lettuce.pool.max-wait}      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: ${conf.redis.lettuce.pool.max-idle}      # 连接池中的最大空闲连接
        min-idle: ${conf.redis.lettuce.pool.min-idle}      # 连接池中的最小空闲连接

--- #################### 监控相关配置 ####################

# Actuator 监控端点的配置项
management:
  endpoints:
    web:
      base-path: /actuator # Actuator 提供的 API 接口的根目录。默认为 /actuator
      exposure:
        include: '*' # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。
bsp:
  token:
    url: http://192.168.3.251:999/bsp-uac/oauth/token
