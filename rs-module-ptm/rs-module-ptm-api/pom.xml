<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<parent>
		<groupId>com.rs</groupId>
		<artifactId>rs-module-ptm</artifactId>
		<version>${rs.version}</version>
	</parent>
	
	<artifactId>rs-module-ptm-api</artifactId>
	<packaging>jar</packaging>
	<name>${project.artifactId}</name>
	<description>监所管理-附物事务管理-api，暴露给其它模块调用</description>
	
	<dependencies>
		<!-- web 相关 begin -->
		<dependency>
            <groupId>org.springdoc</groupId>  <!-- 接口文档 -->
            <artifactId>springdoc-openapi-ui</artifactId>
        </dependency>
		<!-- web 相关 end -->
		
		<!-- 技术组件 begin -->
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-starter-rpc</artifactId>
		</dependency>
		<!-- 技术组件 end -->
	</dependencies>
</project>