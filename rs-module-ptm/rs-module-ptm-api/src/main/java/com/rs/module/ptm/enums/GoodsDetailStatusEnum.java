package com.rs.module.ptm.enums;


public enum GoodsDetailStatusEnum {
    YDJ("01", "已登记"),
    CR("02","存入"),

    YDJDQC("03","已登记待取出"),

    QC("04","取出");


    GoodsDetailStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static GoodsDetailStatusEnum getByCode(String code) {
        GoodsDetailStatusEnum[] enums = GoodsDetailStatusEnum.values();
        for (GoodsDetailStatusEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
