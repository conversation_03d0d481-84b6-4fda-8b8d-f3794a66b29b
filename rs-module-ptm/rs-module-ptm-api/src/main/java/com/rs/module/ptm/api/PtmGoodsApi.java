package com.rs.module.ptm.api;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.ptm.vo.GoodsSaveReqVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(name = "ptm-server")
public interface PtmGoodsApi {

	/**
	 * 服务登记录入
	 * @param id
	 * @return
	 */
	@PostMapping( "/ptm/personal/goods/create")
	CommonResult<String> createGoods(@RequestBody GoodsSaveReqVO createReqVO);
}
