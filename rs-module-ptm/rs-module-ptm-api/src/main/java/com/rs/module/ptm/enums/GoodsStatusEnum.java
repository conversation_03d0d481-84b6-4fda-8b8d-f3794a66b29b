package com.rs.module.ptm.enums;

public enum GoodsStatusEnum {
    YDJ("01", "已登记"),
    YRK("02","已入库"),
    YCK("03","已出库");


    GoodsStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static GoodsStatusEnum getByCode(String code) {
        GoodsStatusEnum[] enums = GoodsStatusEnum.values();
        for (GoodsStatusEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
