package com.rs.module.ptm.enums;

public enum GoodsTakeoutMethodEnum {

    BRLQ("01", "本人领取"),
    TRDWLQ("02","他人代为领取"),
    YJ("03","邮寄"),
    YJZXCS("04","移交执行场所"),
    XH("05","销毁"),
    QT("05","其他");

    GoodsTakeoutMethodEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static GoodsTakeoutMethodEnum getByCode(String code) {
        GoodsTakeoutMethodEnum[] enums = GoodsTakeoutMethodEnum.values();
        for (GoodsTakeoutMethodEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
