package com.rs.module.ptm.enums;

public enum GoodsTakeoutReasonEnum {
    ZCCS("01", "正常出所"),
    YCCS("02","异常出所"),
    LSQW("03","临时取物"),
    LSQWKQ("04","临时取物（扣押）");

    GoodsTakeoutReasonEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static GoodsTakeoutReasonEnum getByCode(String code) {
        GoodsTakeoutReasonEnum[] enums = GoodsTakeoutReasonEnum.values();
        for (GoodsTakeoutReasonEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
