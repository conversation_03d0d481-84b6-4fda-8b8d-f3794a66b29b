package com.rs.module.ptm.enums;

/**
 * 格子状态
 * <AUTHOR>
 * @date 2025/6/25 14:59
 */
public enum CabinetGridStatusEnum {

    KX(1, "空闲"),

    ZY(2,"占用"),

    GZ(3,"故障");

    CabinetGridStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static CabinetGridStatusEnum getByCode(Integer code) {
        CabinetGridStatusEnum[] enums = CabinetGridStatusEnum.values();
        for (CabinetGridStatusEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
