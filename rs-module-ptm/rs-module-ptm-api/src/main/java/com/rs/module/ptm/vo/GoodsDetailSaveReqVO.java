package com.rs.module.ptm.vo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@ApiModel(description = "管理后台 - 附物管理-附物明细新增/修改 Request VO")
@Data
public class GoodsDetailSaveReqVO implements Serializable {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty(value = "附物ID",hidden = true)
    private String personalGoodsId;

    @ApiModelProperty(value= "监管人员编码",hidden = true)
    private String jgrybm;

    @ApiModelProperty(value = "监管人员姓名",hidden = true)
    private String jgryxm;

    @ApiModelProperty(value = "物品编号",hidden = true)
//    @NotEmpty(message = "物品编号不能为空")
    private String wpbh;

    @ApiModelProperty("物品名称")
    @NotEmpty(message = "物品名称不能为空")
    private String name;

    @ApiModelProperty("物品类型")
    @NotEmpty(message = "物品类型不能为空")
    private String goodsType;

    @ApiModelProperty("数量")
    @NotNull(message = "数量不能为空")
    private Integer quantity;

    @ApiModelProperty("数量单位")
    @NotEmpty(message = "数量单位不能为空")
    private String unit;

    @ApiModelProperty("存放位置，格子ID")
    private String storageLocation;

    @ApiModelProperty("存放位置-中文")
    private String storageLocationName;


    @ApiModelProperty("物品特征描述")
    private String features;

    @ApiModelProperty("照片存储地址")
    private List<String> photoPathList;

    @ApiModelProperty("物品照片数量")
    private Integer photoCount;

    @ApiModelProperty("备注")
    private String remark;


}
