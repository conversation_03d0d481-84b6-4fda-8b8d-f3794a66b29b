package com.rs.module.ptm.domain.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@ApiModel(description = "管理后台 - 附物管理-储物柜子信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CabinetRespVO extends BaseVO implements TransPojo{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("柜子名称")
    private String name;

    @ApiModelProperty("柜子编号，如A-01、B-12（区域-序号）")
    private String code;

    @ApiModelProperty("柜子型号")
    private String typeCode;

    @ApiModelProperty("柜子实际位置，如1楼储物间A区")
    private String cabinetLocation;

    @ApiModelProperty("所属区域ID")
    private String  areaId;

    @ApiModelProperty("所属区域名称")
    private String areaName;

    @ApiModelProperty("柜子IP")
    private String ip;

    @ApiModelProperty("柜子端口")
    private Integer port;

    @ApiModelProperty("柜子mac地址")
    private String mac;

    @ApiModelProperty("柜子总格子数，可动态配置（如20、30）")
    private Integer totalGrids;

    @ApiModelProperty("柜子状态：1-可用/2-维修中/3-停用（枚举值）")
    private Integer status;

    @ApiModelProperty("行")
    private Integer row;

    @ApiModelProperty("列")
    private Integer column;

    @ApiModelProperty("格子列表")
    private List<GridJsonVO> gridJsonList;

    @ApiModelProperty("是否主柜 1是0否")
    private Integer isMain;

    @ApiModelProperty("空闲")
    private Long idleTotal;

    @ApiModelProperty("占用")
    private Long occupiedTotal;

    @ApiModelProperty("故障")
    private Long faultyTotal;

    @ApiModelProperty("柜子排序号")
    private Integer cabinetSort;

    @ApiModelProperty("柜子URL")
    private String cabinetImgUrl;

    @ApiModelProperty("主柜子ID")
    private String mainCabinetId;

    @ApiModelProperty("柜子类型ID")
    private String typeId;

    @ApiModelProperty("版本号")
    private String copyright;

    @ApiModelProperty("开柜方式 1刷脸，2账号 3手势： 字典：ZD_FWGJ_KGFS")
    private String cabinetOpenMethod;

    @ApiModelProperty("操作时间-秒")
    private Integer operationTime;

    @ApiModelProperty("相机设置 旋转角度-秒")
    private Integer cameraSetting;

    @ApiModelProperty("版本号")
    private String versionNumber;

    //@TableField(value = "ORG_NAME", fill = FieldFill.INSERT)
    private String orgName;

}
