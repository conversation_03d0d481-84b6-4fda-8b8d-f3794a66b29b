package com.rs.module.ptm.service;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.ptm.domain.entity.GoodsDetailDO;
import com.rs.module.ptm.domain.vo.*;
import com.rs.module.ptm.vo.GoodsDetailSaveReqVO;

import javax.validation.Valid;
import java.util.List;

/**
 * 附物管理-附物明细 Service 接口
 *
 * <AUTHOR>
 */
public interface GoodsDetailService extends IBaseService<GoodsDetailDO>{

    /**
     * 创建附物管理-附物明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createGoodsDetail(@Valid GoodsDetailSaveReqVO createReqVO);

    /**
     * 更新附物管理-附物明细
     *
     * @param updateReqVO 更新信息
     */
    void updateGoodsDetail(@Valid GoodsDetailSaveReqVO updateReqVO);

    /**
     * 删除附物管理-附物明细
     *
     * @param id 编号
     */
    void deleteGoodsDetail(String id);

    /**
     * 获得附物管理-附物明细
     *
     * @param id 编号
     * @return 附物管理-附物明细
     */
    GoodsDetailDO getGoodsDetail(String id);

    /**
    * 获得附物管理-附物明细分页
    *
    * @param pageReqVO 分页查询
    * @return 附物管理-附物明细分页
    */
    PageResult<GoodsDetailDO> getGoodsDetailPage(GoodsDetailPageReqVO pageReqVO);

    /**
    * 获得附物管理-附物明细列表
    *
    * @param listReqVO 查询条件
    * @return 附物管理-附物明细列表
    */
    List<GoodsDetailDO> getGoodsDetailList(GoodsDetailListReqVO listReqVO);


    /**
     * 根据物品id获取附物明细列表
     * @param goodsId
     * @return
     */
    List<GoodsDetailRespVO> getGoodsDetailList(String goodsId);

    List<GoodsDetailRespVO> getDqwGoodsDetailList(String goodsId);

    /**
     * 根据人员编码获取待存物列表
     * <AUTHOR>
     * @date 2025/6/20 10:57
     * @param [jgrybm]
     * @return java.util.List<com.rs.module.ptm.domain.vo.GoodsDetailRespVO>
     */
    List<GoodsItemsStoredListRespVO> getItemsTdoStoredListByJgrybm(String jgrybm, String ip);

    List<GoodsDetailRespVO> wpInfoByGoodsId(String goodsId);


    /**
     * app 传入物品信息 修改状态
     * <AUTHOR>
     * @date 2025/6/21 16:25
     * @param [reqList]
     * @return java.lang.Boolean
     */
    Boolean depositItemsUpdateStatus(GoodsItemsStoredDepositReqVO reqVO);

    /**
     * 获取待取出列表
     * <AUTHOR>
     * @date 2025/6/26 16:23
     * @param [jgrybm, ip]
     * @return java.util.List<com.rs.module.ptm.domain.vo.GoodsItemsStoredListRespVO>
     */
    List<GoodsItemsStoredListRespVO> getTaskOutListByJgrybm(String jgrybm, String ip);

    /**
     * /取出物品信息
     * <AUTHOR>
     * @date 2025/6/26 19:04
     * @param [reqVO]
     * @return java.lang.Boolean
     */
    Boolean taskOutItemsUpdateStatus(GoodsItemsStoredDepositReqVO reqVO);

}
