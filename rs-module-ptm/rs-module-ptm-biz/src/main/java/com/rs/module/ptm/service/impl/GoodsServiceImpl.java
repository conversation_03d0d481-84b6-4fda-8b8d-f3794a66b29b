package com.rs.module.ptm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BpmApi;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.common.util.validation.ValidationUtils;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ptm.dao.GoodsDao;
import com.rs.module.ptm.dao.GoodsOutDao;
import com.rs.module.ptm.dao.GoodsOutDetailDao;
import com.rs.module.ptm.domain.entity.*;
import com.rs.module.ptm.domain.vo.*;
import com.rs.module.ptm.enums.GoodsDetailStatusEnum;
import com.rs.module.ptm.enums.GoodsStatusEnum;
import com.rs.module.ptm.service.GoodsDetailService;
import com.rs.module.ptm.service.GoodsInOutService;
import com.rs.module.ptm.service.GoodsOutService;
import com.rs.module.ptm.service.GoodsService;
import com.rs.module.ptm.vo.GoodsDetailSaveReqVO;
import com.rs.module.ptm.vo.GoodsSaveReqVO;
import com.rs.util.DicUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 附物管理-附物 Service 实现类
 *
 * <AUTHOR>
 */
@Service
//@Validated
public class GoodsServiceImpl extends BaseServiceImpl<GoodsDao, GoodsDO> implements GoodsService {

    @Resource
    private GoodsDao goodsDao;

    @Resource
    private GoodsDetailService goodsDetailService;

    @Resource
    private GoodsOutDetailDao goodsOutDetailDao;

    @Resource
    private GoodsOutService goodsOutService;

    @Resource
    private GoodsInOutService goodsInOutService;

    @Resource
    private GoodsOutDao goodsOutDao;

    @Resource
    private BpmApi bpmApi;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createGoods(GoodsSaveReqVO createReqVO) {
        // 插入
        GoodsDO goods = BeanUtils.toBean(createReqVO, GoodsDO.class);
        List<GoodsDetailSaveReqVO> goodsDetailSaveReqVOList = createReqVO.getGoodsDetailSaveReqVOList();
        //校验
        goodsDetailSaveReqVOList.forEach(ValidationUtils::validate);
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String idCard = sessionUser.getIdCard();
        String name = sessionUser.getName();
        goods.setStatus(GoodsStatusEnum.YDJ.getCode());
        goods.setOperatorSfzh(idCard);
        goods.setOperatorXm(name);
        goods.setOperatorTime(new Date());
        if (StringUtils.hasText(goods.getId())) {
            this.updateById(goods);
        } else {
            this.save(goods);
        }

        GoodsDO finalGoods = goods;
        List<String> detailIdList = new ArrayList<>();
        goodsDetailSaveReqVOList.forEach(goodsDetailSaveReqVO -> {
            goodsDetailSaveReqVO.setPersonalGoodsId(finalGoods.getId());
            goodsDetailSaveReqVO.setJgrybm(finalGoods.getJgrybm());
            goodsDetailSaveReqVO.setJgryxm(finalGoods.getJgryxm());
            detailIdList.add(goodsDetailService.createGoodsDetail(goodsDetailSaveReqVO));
        });
        goodsDetailService.lambdaQuery().in(GoodsDetailDO::getId, detailIdList).list().forEach(goodsInOutService::saveGoodsInOut);

        // 返回
        return goods.getId();
    }

    @Override
    public void updateGoods(GoodsSaveReqVO updateReqVO) {
        // 校验存在
        validateGoodsExists(updateReqVO.getId());
        // 更新
        GoodsDO updateObj = BeanUtils.toBean(updateReqVO, GoodsDO.class);
        goodsDao.updateById(updateObj);
    }

    @Override
    public void deleteGoods(String id) {
        // 校验存在
        validateGoodsExists(id);
        // 删除
        goodsDao.deleteById(id);
    }

    private void validateGoodsExists(String id) {
        if (goodsDao.selectById(id) == null) {
            throw new ServerException("附物管理-附物数据不存在");
        }
    }

    @Override
    public GoodsRespVO getGoods(String id) {
        GoodsDO goodsDO = goodsDao.selectById(id);
        if(goodsDO == null){
            GoodsInOutDO goodsInOut = goodsInOutService.getById(id);
            if(goodsInOut != null){
                goodsDO = goodsDao.selectById(goodsInOut.getBusinessId());
            }
        }


        if (goodsDO != null) {
            GoodsRespVO respVO = BeanUtils.toBean(goodsDO, GoodsRespVO.class);
            List<GoodsDetailRespVO> respVOList = goodsDetailService.getGoodsDetailList(goodsDO.getId());
            respVO.setGoodsDetailRespVOList(respVOList);
            return respVO;
        }
        return null;
    }

    @Override
    public PageResult<GoodsDO> getGoodsPage(GoodsPageReqVO pageReqVO) {
        return goodsDao.selectPage(pageReqVO);
    }

    @Override
    public List<GoodsDO> getGoodsList(GoodsListReqVO listReqVO) {
        return goodsDao.selectList(listReqVO);
    }

    @Override
    public List<GoodsRespVO> getDcwByJgrybm(String jgrybm) {
        return this.lambdaQuery()
                .eq(GoodsDO::getJgrybm, jgrybm)
                .eq(GoodsDO::getStatus, GoodsStatusEnum.YDJ.getCode())
                .orderByDesc(GoodsDO::getUpdateTime).list().stream().
                map(goodsDO -> this.getGoods(goodsDO.getId()))
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String storageGoods(String id) {
        GoodsDO goodsDO = this.getById(id);
        if (goodsDO == null) {
            throw new ServerException("附物管理-附物数据不存在");
        }
        List<GoodsDetailDO> goodsDetailList = goodsDetailService.lambdaQuery()
                .eq(GoodsDetailDO::getPersonalGoodsId, id)
                .eq(GoodsDetailDO::getStatus, GoodsDetailStatusEnum.YDJ.getCode())
                .orderByDesc(GoodsDetailDO::getUpdateTime)
                .list()
                .stream()
                .peek(goodsDetail -> goodsDetail.setStatus(GoodsDetailStatusEnum.CR.getCode())).collect(Collectors.toList());
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String idCard = sessionUser.getIdCard();
        String name = sessionUser.getName();
        goodsDO.setStatus(GoodsStatusEnum.YRK.getCode());
        goodsDO.setStorageSfzh(idCard);
        goodsDO.setStorageXm(name);
        goodsDO.setStorageTime(new Date());
        this.updateById(goodsDO);

        goodsDetailService.updateBatchById(goodsDetailList);
        goodsDetailList.forEach(goodsInOutService::saveGoodsInOut);
        return id;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String takeOutGoods(String id) {
        GoodsDO goodsDO = this.getById(id);
        if (goodsDO == null) {
            throw new ServerException("附物管理-附物数据不存在");
        }
        List<GoodsDetailDO> goodsDetailList = goodsDetailService.lambdaQuery()
                .eq(GoodsDetailDO::getPersonalGoodsId, id)
                .orderByDesc(GoodsDetailDO::getUpdateTime)
                .list();

        List<GoodsDetailDO> updateGoodsDetailList = new ArrayList<>();
        goodsDetailList.forEach(goodsDetailDO -> {
            if (GoodsDetailStatusEnum.CR.getCode().equals(goodsDetailDO.getStatus())) {
                goodsDetailDO.setStatus(GoodsDetailStatusEnum.QC.getCode());
                updateGoodsDetailList.add(goodsDetailDO);
            }
        });
        goodsDetailService.updateBatchById(updateGoodsDetailList);

        //全部取出才要改变状态
        if (goodsDetailList.stream().allMatch(goodsDetailDO -> GoodsDetailStatusEnum.QC.getCode().equals(goodsDetailDO.getStatus()))) {
            goodsDO.setStatus(GoodsStatusEnum.YCK.getCode());
        }
        return id;
    }

    @Override
    public GoodsRespVO getDetailsById(String id) {
        return this.getGoods(id);
    }

    @Override
    public GoodsRespVO getDqwGoodsDetailList(String id) {
        GoodsDO goodsDO = goodsDao.selectById(id);
        if (goodsDO != null) {
            GoodsRespVO respVO = BeanUtils.toBean(goodsDO, GoodsRespVO.class);
            List<GoodsDetailRespVO> dqwGoodsDetailList = goodsDetailService.getDqwGoodsDetailList(id);
            respVO.setGoodsDetailRespVOList(dqwGoodsDetailList);
            return respVO;
        }
        return null;
    }

    @Override
    public List<GoodsLogRespVO> goodsLogList(String id) {
        //存入和存入登记记录
        List<GoodsInOutDO> goodsInList = goodsInOutService.lambdaQuery().eq(GoodsInOutDO::getBusinessId, id)
                .list();
        List<String> wpbhList = goodsInList.stream().map(GoodsInOutDO::getWpbh).collect(Collectors.toList());
        List<GoodsLogRespVO> goodsLogRespVOS = new ArrayList<>();

        List<GoodsLogRespVO> goodsInLogs = goodsInList.stream().map(goodsIn -> {
            GoodsLogRespVO cr = new GoodsLogRespVO();
            cr.setId(goodsIn.getId());
            cr.setOperatorSfzh(goodsIn.getOperatorSfzh());
            cr.setOperatorXm(goodsIn.getOperatorXm());
            cr.setOperatorTime(goodsIn.getOperatorTime());
            cr.setBusinessType(goodsIn.getStatus());

            cr.setBusinessTypeName(DicUtils.translate("ZD_FWGLWPCFZT", goodsIn.getStatus()));
            cr.setGoodsInfo(DicUtils.translate("ZD_FWGLWPZL", goodsIn.getName()) + goodsIn.getQuantity()
                    + DicUtils.translate("ZD_WPDW", goodsIn.getUnit()));

            cr.setIsApproveTrack(0);

            return cr;
        }).collect(Collectors.toList());
        goodsLogRespVOS.addAll(goodsInLogs);

        List<String> goodsOutIdList = goodsOutService.lambdaQuery().eq(GoodsOutDO::getPersonalGoodsId, id).list().stream().map(GoodsOutDO::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(goodsOutIdList)) {
            //取出登记
            List<GoodsInOutDO> goodsInOutList = goodsInOutService.lambdaQuery().in(GoodsInOutDO::getBusinessId, goodsOutIdList)
                    .list();
            List<GoodsOutDetailDO> goodsOutDetailList = goodsOutDetailDao.selectList(new LambdaQueryWrapperX<GoodsOutDetailDO>().in(GoodsOutDetailDO::getWpbh, wpbhList));
            goodsOutDao.selectList(new LambdaQueryWrapperX<GoodsOutDO>().eq(GoodsOutDO::getPersonalGoodsId, id))
                    .forEach(goodsOut -> {
                        List<GoodsOutDetailDO> subOutGoodsDetailList = goodsOutDetailList.stream()
                                .filter(goodsOutDetailDO -> goodsOutDetailDO.getPersonalGoodsOutId().equals(goodsOut.getId())).collect(Collectors.toList());
                        List<GoodsInOutDO> subGoodsInOutList = goodsInOutList.stream().filter(goodsInOutDO -> goodsInOutDO.getBusinessId().equals(goodsOut.getId())).collect(Collectors.toList());
                        GoodsLogRespVO qcdj = new GoodsLogRespVO();
                        qcdj.setId(goodsOut.getId());
                        qcdj.setOperatorSfzh(goodsOut.getOperatorSfzh());
                        qcdj.setOperatorXm(goodsOut.getOperatorXm());
                        qcdj.setOperatorTime(goodsOut.getOperatorTime());
                        qcdj.setBusinessType(GoodsDetailStatusEnum.YDJDQC.getCode());
                        qcdj.setBusinessTypeName(GoodsDetailStatusEnum.YDJDQC.getName());
                        qcdj.setGoodsInfo(getGoodsInfo(subOutGoodsDetailList, subGoodsInOutList));
                        qcdj.setTakeoutMethod(goodsOut.getTakeoutMethod());
                        qcdj.setDocId(goodsOut.getId());
                        goodsLogRespVOS.add(qcdj);
                    });
            List<GoodsInOutDO> list = goodsInOutService.lambdaQuery().in(GoodsInOutDO::getBusinessId, goodsOutIdList)
                    .eq(GoodsInOutDO::getStatus, GoodsDetailStatusEnum.QC.getCode())
                    .list();
            List<GoodsLogRespVO> goodsOutLogs = list.stream().map(goodsIn -> {
                GoodsLogRespVO qc = new GoodsLogRespVO();
                qc.setId(goodsIn.getId());
                qc.setOperatorSfzh(goodsIn.getOperatorSfzh());
                qc.setOperatorXm(goodsIn.getOperatorXm());
                qc.setOperatorTime(goodsIn.getOperatorTime());
                qc.setBusinessType(goodsIn.getStatus());
                qc.setBusinessTypeName(DicUtils.translate("ZD_FWGLWPCFZT", goodsIn.getStatus()));
                qc.setGoodsInfo(DicUtils.translate("ZD_FWGLWPZL", goodsIn.getName()) + goodsIn.getQuantity()
                        + DicUtils.translate("ZD_WPDW", goodsIn.getUnit()));
                return qc;
            }).collect(Collectors.toList());
            goodsLogRespVOS.addAll(goodsOutLogs);
        }

        return goodsLogRespVOS.stream().sorted(Comparator.comparing(GoodsLogRespVO::getOperatorTime)).collect(Collectors.toList());
    }

    @Override
    public Object goodsLogListDetail(String id, String businessType) {
        //不是登记就是取出
        if (GoodsDetailStatusEnum.YDJ.getCode().equals(businessType)
                || GoodsDetailStatusEnum.CR.getCode().equals(businessType)) {
            return this.getDetailsById(id);
        } else {
            return goodsOutService.getDetailsById(id);
        }
    }

    @Override
    public List<GoodsLogRespVO> goodsLogListTotal(String id) {
        List<GoodsLogRespVO> goodsLogRespVOS = goodsLogList(id);
        return goodsLogRespVOS.stream().peek(goodsLog ->
                goodsLog.setLogsDetail(goodsLogListDetail(goodsLog.getId(), goodsLog.getBusinessType()))
        ).collect(Collectors.toList());
    }

    @Override
    public WpStatisticRespVO wpStatisticInfo(String wpbh) {
        WpStatisticRespVO respVO = new WpStatisticRespVO();
        List<GoodsInOutDO> list = goodsInOutService.lambdaQuery().eq(GoodsInOutDO::getWpbh, wpbh).list();
        List<GoodsDetailDO> goodsDetailDOList = goodsDetailService.list(new LambdaQueryWrapperX<GoodsDetailDO>().eq(GoodsDetailDO::getWpbh, wpbh));
        //照片信息
        List<String> photoPathList = new ArrayList<>();
        //物品照片数量
        Integer photoCount = 0;
        String storageLocationName = null;
        if(CollUtil.isNotEmpty(goodsDetailDOList)){
           String  photoPath =  goodsDetailDOList.get(0).getPhotoPath();
           if(StrUtil.isNotBlank(photoPath)){
               photoPathList = Arrays.asList(photoPath.split(","));
               photoCount = photoPathList.size();
            }
            storageLocationName = goodsDetailDOList.get(0).getStorageLocationName();
        }

        List<String> finalPhotoPathList = photoPathList;
        Integer finalPhotoCount = photoCount;
        String finalStorageLocationName = storageLocationName;
        list.forEach(goodsInOutDO -> {
            String status = goodsInOutDO.getStatus();
            GoodsInOutRespVO goodsInOutRespVO = BeanUtils.toBean(goodsInOutDO, GoodsInOutRespVO.class);
            goodsInOutRespVO.setPhotoPathList(finalPhotoPathList);
            goodsInOutRespVO.setPhotoCount(finalPhotoCount);
            goodsInOutRespVO.setStorageLocationName(finalStorageLocationName);
            if (GoodsDetailStatusEnum.YDJ.getCode().equals(status)) {
                respVO.setGoodsDjRespVO(goodsInOutRespVO);
            } else if (GoodsDetailStatusEnum.CR.getCode().equals(status)) {
                respVO.setGoodsInRespVO(goodsInOutRespVO);
            } else if (GoodsDetailStatusEnum.QC.getCode().equals(status)) {
                respVO.setGoodsOutRespVO(goodsInOutRespVO);
            }
        });
        list.stream().filter(goodsInOutDO -> GoodsDetailStatusEnum.YDJDQC.getCode().equals(goodsInOutDO.getStatus()))
                .findFirst().ifPresent(goodsInOutDO -> {
                    String businessId = goodsInOutDO.getBusinessId();
                    GoodsOutRespVO goodsOutRespVO = goodsOutService.getDetailsById(businessId);
                    List<GoodsOutDetailRespVO> filterGoodsOutDetailList = goodsOutRespVO.getGoodsOutDetailRespVOList().stream().filter(goodsOutDetailRespVO ->
                            goodsOutDetailRespVO.getWpbh().equals(goodsInOutDO.getWpbh())).collect(Collectors.toList());
                    goodsOutRespVO.setGoodsOutDetailRespVOList(filterGoodsOutDetailList);
                    if (StringUtils.hasText(goodsOutRespVO.getActInstId())) {
                        goodsOutRespVO.setApproveTrack(bpmApi.approveTrack(goodsOutRespVO.getActInstId()));
                    }
                    respVO.setGoodsOutDjRespVO(goodsOutRespVO);
                });

        //获取记录
        List<GoodsLogRespVO> trackList = list.stream().map(goodsInOutDO -> {
            GoodsLogRespVO goodsLogRespVO = new GoodsLogRespVO();
            BeanUtils.copyProperties(goodsInOutDO, goodsLogRespVO);
            goodsLogRespVO.setGoodsInfo(DicUtils.translate("ZD_FWGLWPZL", goodsInOutDO.getName()) +
                    goodsInOutDO.getQuantity() + DicUtils.translate("ZD_WPDW", goodsInOutDO.getUnit()));
            goodsLogRespVO.setBusinessType(goodsInOutDO.getStatus());
            goodsLogRespVO.setBusinessTypeName(DicUtils.translate("ZD_FWGLWPCFZT", goodsInOutDO.getStatus()));
            return goodsLogRespVO;
        }).sorted(Comparator.comparing(GoodsLogRespVO::getOperatorTime)).collect(Collectors.toList());
        respVO.setTrackList(trackList);
        return respVO;
    }

//    private String getGoodsInfo(List<GoodsDetailDO> goodsDetailList) {
//        Map<String, List<GoodsDetailDO>> group = goodsDetailList.stream().collect(Collectors.groupingBy(GoodsDetailDO::getName));
//        String goodsInfo = group.keySet().stream().map(name -> {
//            List<GoodsDetailDO> goodsDetailDOList = group.get(name);
//            //数量
//            int sum = goodsDetailDOList.stream().mapToInt(GoodsDetailDO::getQuantity).sum();
//            //名称
//            String nameDic = DicUtil.translate("ZD_FWGLWPZL", name);
//            //单位
//            String unitDic = DicUtil.translate("ZD_WPDW", name);
//            return nameDic + sum + unitDic;
//        }).collect(Collectors.joining(","));
//        return goodsInfo;
//    }

    private String getGoodsInfo(List<GoodsOutDetailDO> goodsOutDetailList, List<GoodsInOutDO> goodsInOutList) {
        //获取物品取出详情
        List<GoodsOutDetailRespVO> goodOutDetailRespVOList = goodsOutDetailList.stream().map(goodsOutDetail -> {
            GoodsInOutDO goodsDetailDO = goodsInOutList.stream().filter(goodsInOut -> goodsInOut.getWpbh().equals(goodsOutDetail.getWpbh()))
                    .findFirst().orElse(null);
            GoodsOutDetailRespVO goodsOutDetailRespVO = BeanUtils.toBean(goodsOutDetail, GoodsOutDetailRespVO.class);

            goodsOutDetailRespVO.setName(goodsDetailDO.getName());
            goodsOutDetailRespVO.setUnit(goodsDetailDO.getUnit());
            return goodsOutDetailRespVO;
        }).collect(Collectors.toList());


        Map<String, List<GoodsOutDetailRespVO>> group = goodOutDetailRespVOList.stream().collect(Collectors.groupingBy(GoodsOutDetailRespVO::getName));
        String goodsInfo = group.keySet().stream().map(name -> {
            List<GoodsOutDetailRespVO> goodsOutDetailRespVOS = group.get(name);
            //数量
            int sum = goodsOutDetailRespVOS.stream().mapToInt(GoodsOutDetailRespVO::getTakeoutQuantity).sum();
            String unit = goodsOutDetailRespVOS.stream().map(GoodsOutDetailRespVO::getUnit).findFirst().orElse(null);
            //名称
            String nameDic = DicUtils.translate("ZD_FWGLWPZL", name);
            //单位
            String unitDic = DicUtils.translate("ZD_WPDW", unit);
            return nameDic + sum + unitDic;
        }).collect(Collectors.joining(","));
        return goodsInfo;
    }


}
