package com.rs.module.ptm.service;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.ptm.domain.entity.CabinetDO;
import com.rs.module.ptm.domain.vo.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 附物管理-储物柜子信息 Service 接口
 *
 * <AUTHOR>
 */
public interface CabinetService extends IBaseService<CabinetDO>{

    /**
     * 创建附物管理-储物柜子信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCabinet(@Valid CabinetSaveReqVO createReqVO);

    /**
     * 更新附物管理-储物柜子信息
     *
     * @param updateReqVO 更新信息
     */
    void updateCabinet(@Valid CabinetSaveReqVO updateReqVO);

    /**
     * 删除附物管理-储物柜子信息
     *
     * @param id 编号
     */
    void deleteCabinet(String id);

    /**
     * 获得附物管理-储物柜子信息
     *
     * @param id 编号
     * @return 附物管理-储物柜子信息
     */
    CabinetRespVO getCabinet(String id);

    /**
    * 获得附物管理-储物柜子信息分页
    *
    * @param pageReqVO 分页查询
    * @return 附物管理-储物柜子信息分页
    */
    PageResult<CabinetDO> getCabinetPage(CabinetPageReqVO pageReqVO);

    /**
    * 获得附物管理-储物柜子信息列表
    *
    * @param listReqVO 查询条件
    * @return 附物管理-储物柜子信息列表
    */
    List<CabinetDO> getCabinetList(CabinetListReqVO listReqVO);


    List<AreaCabinetRespVO>getMainCabinetTree();


    /**
     * 获取格子状态信息
     * <AUTHOR>
     * @date 2025/7/7 21:23
     * @param [ip]
     * @return com.rs.module.ptm.domain.vo.GoodsGridStatusRespVO
     */
    GoodsGridStatusRespVO getCabinetGridStatusByIp(String ip);

    /**
     * 获取柜子APP信息
     * <AUTHOR>
     * @date 2025/7/9 15:39
     * @param [ip]
     * @return com.rs.module.ptm.domain.vo.AppCabinetRespVO
     */
    AppCabinetRespVO getAppCabinetInfo(String ip);

    /**
     * 获取柜子配置信息
     * <AUTHOR>
     * @date 2025/7/10 16:37
     * @param [ip]
     * @return com.rs.module.ptm.domain.vo.AppCabinetSettingRespVO
     */
    AppCabinetSettingRespVO getCabinetISetting(String ip);

    /**
     * 保存或者更新配置信息
     * <AUTHOR>
     * @date 2025/7/10 16:39
     * @param [appCabinetSettingRespVO]
     * @return java.lang.Boolean
     */
    Boolean updateCabinetSetting(AppCabinetSettingRespVO appCabinetSettingRespVO);
}
