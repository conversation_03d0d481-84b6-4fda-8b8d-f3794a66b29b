package com.rs.module.ptm.service.cabinet;

import java.util.*;
import javax.validation.*;
import com.rs.module.ptm.controller.admin.cabinet.vo.*;
import com.rs.module.ptm.entity.cabinet.FaultLogDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 附物管理-储物柜子故障日志 Service 接口
 *
 * <AUTHOR>
 */
public interface FaultLogService extends IBaseService<FaultLogDO>{

    /**
     * 创建附物管理-储物柜子故障日志
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createFaultLog(@Valid FaultLogSaveReqVO createReqVO);

    /**
     * 更新附物管理-储物柜子故障日志
     *
     * @param updateReqVO 更新信息
     */
    void updateFaultLog(@Valid FaultLogSaveReqVO updateReqVO);

    /**
     * 删除附物管理-储物柜子故障日志
     *
     * @param id 编号
     */
    void deleteFaultLog(String id);

    /**
     * 获得附物管理-储物柜子故障日志
     *
     * @param id 编号
     * @return 附物管理-储物柜子故障日志
     */
    FaultLogDO getFaultLog(String id);

    /**
    * 获得附物管理-储物柜子故障日志分页
    *
    * @param pageReqVO 分页查询
    * @return 附物管理-储物柜子故障日志分页
    */
    PageResult<FaultLogDO> getFaultLogPage(FaultLogPageReqVO pageReqVO);

    /**
    * 获得附物管理-储物柜子故障日志列表
    *
    * @param listReqVO 查询条件
    * @return 附物管理-储物柜子故障日志列表
    */
    List<FaultLogDO> getFaultLogList(FaultLogListReqVO listReqVO);


}
