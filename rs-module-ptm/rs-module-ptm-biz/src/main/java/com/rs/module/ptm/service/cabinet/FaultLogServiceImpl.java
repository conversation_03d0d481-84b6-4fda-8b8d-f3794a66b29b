package com.rs.module.ptm.service.cabinet;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.ptm.controller.admin.cabinet.vo.*;
import com.rs.module.ptm.entity.cabinet.FaultLogDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ptm.dao.cabinet.FaultLogDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 附物管理-储物柜子故障日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FaultLogServiceImpl extends BaseServiceImpl<FaultLogDao, FaultLogDO> implements FaultLogService {

    @Resource
    private FaultLogDao faultLogDao;

    @Override
    public String createFaultLog(FaultLogSaveReqVO createReqVO) {
        // 插入
        FaultLogDO faultLog = BeanUtils.toBean(createReqVO, FaultLogDO.class);
        faultLogDao.insert(faultLog);
        // 返回
        return faultLog.getId();
    }

    @Override
    public void updateFaultLog(FaultLogSaveReqVO updateReqVO) {
        // 校验存在
        validateFaultLogExists(updateReqVO.getId());
        // 更新
        FaultLogDO updateObj = BeanUtils.toBean(updateReqVO, FaultLogDO.class);
        faultLogDao.updateById(updateObj);
    }

    @Override
    public void deleteFaultLog(String id) {
        // 校验存在
        validateFaultLogExists(id);
        // 删除
        faultLogDao.deleteById(id);
    }

    private void validateFaultLogExists(String id) {
        if (faultLogDao.selectById(id) == null) {
            throw new ServerException("附物管理-储物柜子故障日志数据不存在");
        }
    }

    @Override
    public FaultLogDO getFaultLog(String id) {
        return faultLogDao.selectById(id);
    }

    @Override
    public PageResult<FaultLogDO> getFaultLogPage(FaultLogPageReqVO pageReqVO) {
        return faultLogDao.selectPage(pageReqVO);
    }

    @Override
    public List<FaultLogDO> getFaultLogList(FaultLogListReqVO listReqVO) {
        return faultLogDao.selectList(listReqVO);
    }


}
