package com.rs.module.ptm.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class GridJsonVO implements Serializable {

    private static final long serialVersionUID = 6556386262996454704L;

    @ApiModelProperty("格子Id")
    private String gridId;

    @ApiModelProperty("格子编码")
    private String gridCode;

    @ApiModelProperty("格子名称")
    private String gridName;

    @ApiModelProperty("格子名称")
    private String gridAllName;

    @ApiModelProperty("格子尺寸，如S（小）/M（中）/L（大）")
    private String gridSize;

    @ApiModelProperty("行")
    private Integer row;

    @ApiModelProperty("列")
    private Integer column;

    @ApiModelProperty("格子状态：1-空闲/2-占用/3-故障（枚举值）")
    private Integer status;



}
