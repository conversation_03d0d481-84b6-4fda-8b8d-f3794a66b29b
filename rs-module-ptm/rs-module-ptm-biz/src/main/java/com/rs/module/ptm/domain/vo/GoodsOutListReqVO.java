package com.rs.module.ptm.domain.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 附物管理-附物取出登记列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsOutListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("取出原因")
    private String takeoutReason;

    @ApiModelProperty("取出方式")
    private String takeoutMethod;

    @ApiModelProperty("取出备注")
    private String takeoutRemark;

    @ApiModelProperty("取出人姓名")
    private String takeoutPerson;

    @ApiModelProperty("取出人证件号码")
    private String takeoutIdNumber;

    @ApiModelProperty("取出人联系方式")
    private String takeoutContact;

    @ApiModelProperty("与被监管人关系")
    private String relationship;

    @ApiModelProperty("取出人家庭住址")
    private String takeoutAddress;

    @ApiModelProperty("证明材料上传地址（存储文件路径）")
    private String proofMaterialsPath;

    @ApiModelProperty("邮寄人姓名")
    private String senderName;

    @ApiModelProperty("邮寄人联系方式")
    private String senderContact;

    @ApiModelProperty("收件人姓名")
    private String recipientName;

    @ApiModelProperty("收件人联系方式")
    private String recipientContact;

    @ApiModelProperty("邮件单号")
    private String mailTrackingNumber;

    @ApiModelProperty("移交单位")
    private String transferUnit;

    @ApiModelProperty("销毁方式")
    private String destructionMethod;

    @ApiModelProperty("销毁人")
    private String destructionPerson;

    @ApiModelProperty("登记经办人")
    private String operatorSfzh;

    @ApiModelProperty("登记经办人姓名")
    private String operatorXm;

    @ApiModelProperty("登记经办时间")
    private Date[] operatorTime;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date[] approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审批人签名")
    private String approvalAutograph;

    @ApiModelProperty("审批人签名日期")
    private Date[] approvalAutographTime;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

}
