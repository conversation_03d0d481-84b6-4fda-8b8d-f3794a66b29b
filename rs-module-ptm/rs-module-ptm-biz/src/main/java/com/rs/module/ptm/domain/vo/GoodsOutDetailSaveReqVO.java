package com.rs.module.ptm.domain.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 附物管理-附物取出登记明细新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsOutDetailSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;

    private String id;
    @ApiModelProperty(value = "监管人员编码",hidden = true)
    private String jgrybm;

    @ApiModelProperty(value = "监管人员姓名",hidden = true)
    private String jgryxm;

    @ApiModelProperty(value = "附物取出ID",hidden = true)
    private String personalGoodsOutId;

    @ApiModelProperty(value = "物品编号")
    @NotEmpty(message = "物品编号不能为空")
    private String wpbh;

    @ApiModelProperty("取出数量")
    @NotNull(message = "取出数量不能为空")
    private Integer takeoutQuantity;

    @ApiModelProperty("备注")
    private String remark;

}
