package com.rs.module.ptm.service.impl;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ptm.dao.CabinetDao;
import com.rs.module.ptm.dao.CabinetTypeDao;
import com.rs.module.ptm.domain.entity.CabinetDO;
import com.rs.module.ptm.domain.entity.CabinetTypeDO;
import com.rs.module.ptm.domain.vo.CabinetTypeListReqVO;
import com.rs.module.ptm.domain.vo.CabinetTypePageReqVO;
import com.rs.module.ptm.domain.vo.CabinetTypeSaveReqVO;
import com.rs.module.ptm.service.CabinetTypeService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 附物管理-储物柜子类型信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CabinetTypeServiceImpl extends BaseServiceImpl<CabinetTypeDao, CabinetTypeDO> implements CabinetTypeService {

    @Resource
    private CabinetTypeDao cabinetTypeDao;

    @Resource
    private CabinetDao cabinetDao;

    @Override
    public String createCabinetType(CabinetTypeSaveReqVO createReqVO) {
        // 插入
        CabinetTypeDO cabinetType = BeanUtils.toBean(createReqVO, CabinetTypeDO.class);
        cabinetTypeDao.insert(cabinetType);
        // 返回
        return cabinetType.getId();
    }

    @Override
    public void updateCabinetType(CabinetTypeSaveReqVO updateReqVO) {
        // 校验存在
        validateCabinetTypeExists(updateReqVO.getId());
        // 更新
        CabinetTypeDO updateObj = BeanUtils.toBean(updateReqVO, CabinetTypeDO.class);
        cabinetTypeDao.updateById(updateObj);
    }

    @Override
    public void deleteCabinetType(String id) {
        // 校验存在
        validateCabinetTypeExists(id);
        CabinetTypeDO cabinetTypeDO = cabinetTypeDao.selectById(id);
        Assert.notNull(cabinetTypeDO, "附物管理-储物柜子类型信息数据不存在");
        // 删除
        Integer cabinetCount = cabinetDao.selectCount(new LambdaQueryWrapperX<CabinetDO>().eq(CabinetDO::getTypeCode, cabinetTypeDO.getTypeCode()));
        if(cabinetCount > 0){
            throw new RuntimeException("存在关联的柜子信息，请勿删除！");
        }
        cabinetTypeDao.deleteById(id);
    }

    private void validateCabinetTypeExists(String id) {
        if (cabinetTypeDao.selectById(id) == null) {
            throw new ServerException("附物管理-储物柜子类型信息数据不存在");
        }
    }

    @Override
    public CabinetTypeDO getCabinetType(String id) {
        return cabinetTypeDao.selectById(id);
    }

    @Override
    public PageResult<CabinetTypeDO> getCabinetTypePage(CabinetTypePageReqVO pageReqVO) {
        return cabinetTypeDao.selectPage(pageReqVO);
    }

    @Override
    public List<CabinetTypeDO> getCabinetTypeList(CabinetTypeListReqVO listReqVO) {
        return cabinetTypeDao.selectList(listReqVO);
    }

}
