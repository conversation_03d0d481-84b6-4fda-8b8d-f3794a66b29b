package com.rs.module.ptm.domain.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 附物管理-附物存入取出 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsInOutRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("存入取出类型，（01：存入，02：取出）")
    private String inOutType;
    @ApiModelProperty("业务ID（存入存附物主表ID,取出存附物取出主表ID）")
    private String businessId;
    @ApiModelProperty("物品编号")
    private String wpbh;
    @ApiModelProperty("物品名称")
    @Trans(type = TransType.DICTIONARY, key = "ZD_FWGLWPZL")
    private String name;

    @ApiModelProperty("物品类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_FWGLWPLX")
    private String goodsType;
    
    @ApiModelProperty("数量")
    private Integer quantity;
    @ApiModelProperty("数量单位")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WPDW")
    private String unit;
    @ApiModelProperty("存放位置，如A监区主柜B09号柜")
    private String storageLocation;
    @ApiModelProperty("存放位置，如A监区主柜B09号柜")
    private String storageLocationName;
    @ApiModelProperty("物品特征描述")
    private String features;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("取出数量")
    private Integer takeoutQuantity;
    @ApiModelProperty("取出原因")
    @Trans(type = TransType.DICTIONARY, key = "ZD_QCDJBQCYY")
    private String takeoutReason;
    @ApiModelProperty("取出方式")
    @Trans(type = TransType.DICTIONARY, key = "ZD_QCDJBQCFS")
    private String takeoutMethod;
    @ApiModelProperty("登记经办人")
    private String operatorSfzh;
    @ApiModelProperty("登记经办人姓名")
    private String operatorXm;
    @ApiModelProperty("登记经办时间")
    private Date operatorTime;


    @ApiModelProperty("照片存储地址")
    private List<String> photoPathList;

    @ApiModelProperty("物品照片数量")
    private Integer photoCount;
}
