package com.rs.module.ptm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ptm.dao.CabinetDao;
import com.rs.module.ptm.dao.CabinetTypeDao;
import com.rs.module.ptm.dao.GridDao;
import com.rs.module.ptm.domain.entity.CabinetDO;
import com.rs.module.ptm.domain.entity.CabinetTypeDO;
import com.rs.module.ptm.domain.entity.GridDO;
import com.rs.module.ptm.domain.vo.*;
import com.rs.module.ptm.enums.CabinetGridStatusEnum;
import com.rs.module.ptm.service.CabinetService;
import com.rs.module.ptm.service.GridService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 附物管理-储物柜子信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class CabinetServiceImpl extends BaseServiceImpl<CabinetDao, CabinetDO> implements CabinetService {

    @Resource
    private CabinetDao cabinetDao;

    @Resource
    private GridService gridService;



    @Resource
    private GridDao gridDao;

    @Resource
    private CabinetTypeDao cabinetTypeDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createCabinet(CabinetSaveReqVO createReqVO) {
        // 插入
        CabinetDO cabinet = BeanUtils.toBean(createReqVO, CabinetDO.class);
        CabinetTypeDO  cabinetType = cabinetTypeDao.selectById(createReqVO.getTypeId());
        Assert.notNull(cabinetType,"物品柜型号ID，传入有误");

        cabinet.setTypeCode(cabinetType.getTypeCode());
        cabinet.setTotalGrids(cabinetType.getMaxGridCount() );
        cabinet.setStatus(1);
        
        String prefix = StrUtil.format("{}-{}",cabinet.getAreaId(), cabinetType.getGridPrefixRule());
        Integer total = cabinetDao.selectCount(new LambdaQueryWrapper<CabinetDO>().like(CabinetDO::getCode, prefix));
        String code = incrementCode(String.valueOf( total));
        cabinet.setCode(StrUtil.format("{}-{}", prefix, code));
        cabinet.setOrgName(SessionUserUtil.getSessionUser().getOrgName());
        //@ApiModelProperty("柜子实际位置，如1楼储物间A区")
        //private String cabinetLocation;
        cabinetDao.insert(cabinet);

        //主柜布局配置可为空！
        if(cabinetType.getIsMain() == 1 && StrUtil.isBlank(cabinetType.getLayoutConfig())){
            return StrUtil.EMPTY;
        }

        //格子信息新增
        JSONArray jsonObject = null;
        try {
            jsonObject = JSON.parseArray(cabinetType.getLayoutConfig());
        } catch (Exception e) {
           log.error("json解析异常", e);
           throw new RuntimeException("布局配置异常请检查！");
        }
        List<GridDO> gridSaveReqVOList = new ArrayList<>();
        int count =0;
        for (Object o : jsonObject) {
            JSONObject object = (JSONObject) o;
            String gridName = object.getString("grid_code");
            //Integer row = object.getInteger("row");
            //Integer column = object.getInteger("column");
            String gridSize = object.getString("grid_size");
            GridDO grid = new GridDO();
            grid.setCabinetId(cabinet.getId());
            grid.setCabinetCode(cabinet.getCode());
            grid.setGridName(gridName);
            grid.setGridSize(gridSize);
            grid.setGridCode( cabinet.getCode() + "-" + incrementCode( String.valueOf(count )));
            grid.setStatus(CabinetGridStatusEnum.KX.getCode());
            grid.setMainCabinetId(cabinet.getMainCabinetId() );
            count++;
            gridSaveReqVOList.add(grid);
        }
        gridService.saveBatch(gridSaveReqVOList);
        return cabinet.getId();
    }


    // 编码递增逻辑
    private String incrementCode(String code) {
        // 移除前缀（如果有）
        String numericPart = code;
        // 转换为数字并递增
        long number = Long.parseLong(numericPart);
        number++;
        // 计算需要的长度（保持与原编码相同或更长）
        int length = Math.max(numericPart.length(), String.valueOf(number).length());
        // 格式化为指定长度的字符串
        String formatted = String.format("%0" + length + "d", number);
        return  formatted;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCabinet(CabinetSaveReqVO updateReqVO) {
        // 校验存在
        validateCabinetExists(updateReqVO.getId());
        CabinetDO cabinetDO = cabinetDao.selectById(updateReqVO.getId());
        Assert.notNull(cabinetDO,"传入Id有误");

        cabinetDO.setName(updateReqVO.getName());
        cabinetDO.setIp(updateReqVO.getIp());
        cabinetDO.setMac(updateReqVO.getMac());
        cabinetDO.setPort(updateReqVO.getPort());
        cabinetDO.setName(updateReqVO.getName() );
        cabinetDO.setAreaId( updateReqVO.getAreaId());
        cabinetDO.setAreaName(updateReqVO.getAreaName());
        cabinetDO.setIsMain( updateReqVO.getIsMain());
        cabinetDO.setMainCabinetId(updateReqVO.getMainCabinetId());
        cabinetDO.setCabinetSort( updateReqVO.getCabinetSort());
        cabinetDO.setCopyright(updateReqVO.getCopyright() );
        cabinetDO.setCabinetOpenMethod(updateReqVO.getCabinetOpenMethod() );
        cabinetDO.setOperationTime(updateReqVO.getOperationTime() );
        cabinetDO.setCameraSetting(updateReqVO.getCameraSetting() );
        cabinetDO.setVersionNumber(updateReqVO.getVersionNumber() );
        //@ApiModelProperty("柜子编号，如A-01、B-12（区域+序号）")
        //@NotEmpty(message = "柜子编号，如A-01、B-12（区域+序号）不能为空")
        //private String code;

        //@ApiModelProperty("柜子实际位置，如1楼储物间A区")
        //private String cabinetLocation;
        //@ApiModelProperty("柜子总格子数，可动态配置（如20格、30格）")
        //@NotNull(message = "柜子总格子数，可动态配置（如20格、30格）不能为空")
        //private Integer totalGrids;

        //@ApiModelProperty("柜子状态：1-可用/2-维修中/3-停用（枚举值）")
        //@NotNull(message = "柜子状态：1-可用/2-维修中/3-停用（枚举值）不能为空")
        //private Short status;

        // 更新
        //CabinetDO updateObj = BeanUtils.toBean(updateReqVO, CabinetDO.class);
        //CabinetTypeDO  cabinetType = cabinetTypeDao.selectById(updateReqVO.getId());
        //Assert.notNull(cabinetType,"物品柜型号ID，传入有误");
        cabinetDao.updateById(cabinetDO);

    }

    @Override
    public void deleteCabinet(String id) {
        // 校验存在
        validateCabinetExists(id);
        // 删除
        // 判断格子是否空闲，不空闲就允许删除。
        int count =  gridDao.selectCount (new LambdaQueryWrapperX<GridDO>().eq(GridDO::getCabinetId, id).in(GridDO::getStatus, Arrays.asList( CabinetGridStatusEnum.ZY.getCode()  )));
        if(count > 0){
            throw new RuntimeException("有占用中的格子，不允许删除");
        }
        cabinetDao.deleteById(id);
    }

    private void validateCabinetExists(String id) {
        if (cabinetDao.selectById(id) == null) {
            throw new ServerException("附物管理-储物柜子信息数据不存在");
        }
    }

    @Override
    public CabinetRespVO getCabinet(String id) {
        CabinetDO cabinetDO = cabinetDao.selectById(id);
        if(cabinetDO == null){
            return null;
        }
        CabinetRespVO cabinetRespVO = BeanUtils.toBean(cabinetDO, CabinetRespVO.class);

        GridListReqVO reqVO = new GridListReqVO();
        reqVO.setCabinetId( cabinetDO.getId());
        List<GridDO>  gridList =  gridService.getGridList(reqVO);
        Map<String, List<GridDO>> mapParentGrid = gridList.stream().collect(Collectors.groupingBy(GridDO::getCabinetId));

        Map<String, GridDO> mapGrid = convertToGridMap(gridList);
        //柜子型号
        List<CabinetTypeDO> cabinetTypeList = cabinetTypeDao.selectList();
        Map<String, CabinetTypeDO> mapType = convertToCabinetTypeMap(cabinetTypeList);
        setCabinetRespInfo(cabinetRespVO, mapType,  mapParentGrid);
        //List<GridJsonVO> gridJsonVOList = BeanUtils.toBean(list,GridJsonVO.class );
        //cabinetRespVO.setGridJsonList( gridJsonVOList);
        return cabinetRespVO;
    }

    @Override
    public PageResult<CabinetDO> getCabinetPage(CabinetPageReqVO pageReqVO) {
        return cabinetDao.selectPage(pageReqVO);
    }

    @Override
    public List<CabinetDO> getCabinetList(CabinetListReqVO listReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String orgCode = sessionUser.getOrgCode();
        listReqVO.setOrgCode(orgCode);
        return cabinetDao.selectList(listReqVO);
    }

    @Override
    public List<AreaCabinetRespVO> getMainCabinetTree() {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String orgCode = sessionUser.getOrgCode();
        List<CabinetDO> list = this.lambdaQuery().eq(CabinetDO::getOrgCode, orgCode).list();
        if(CollUtil.isEmpty(list)){
            return new ArrayList<>();
        }
        Map<String, String> areaMap =  convertToAreaMap(list);
        List<GridDO> gridList = gridDao.selectList(new LambdaQueryWrapperX<GridDO>().in(GridDO::getCabinetId, list.stream().map(CabinetDO::getId).collect(Collectors.toList())));
        Map<String, GridDO> mapGrid = convertToGridMap(gridList);
        Map<String, List<GridDO>> mapParentGrid = gridList.stream().collect(Collectors.groupingBy(GridDO::getCabinetId));
        //柜子型号
        List<CabinetTypeDO> cabinetTypeList = cabinetTypeDao.selectList();
        Map<String, CabinetTypeDO> mapType = convertToCabinetTypeMap(cabinetTypeList);


        //获取所有区域
        return list.stream().map(CabinetDO::getAreaId).distinct().map(areaId -> {
            AreaCabinetRespVO areaCabinetRespVO = new AreaCabinetRespVO();
            areaCabinetRespVO.setAreaId(areaId);
            areaCabinetRespVO.setId(areaId);
            areaCabinetRespVO.setAreaName(areaMap.get(areaId));
            //获取对应的物品柜
            List<CabinetRespVO> cabinetList = list.stream().filter(cabinetDO -> cabinetDO.getAreaId().equals(areaId))
                    .map(cabinetDO -> BeanUtils.toBean(cabinetDO, CabinetRespVO.class)
                    ).collect(Collectors.toList());

            Collections.sort(cabinetList, Comparator.comparingInt(CabinetRespVO::getCabinetSort));
            for (CabinetRespVO cabinetRespVO : cabinetList) {
                if("北京市第三看守所".equals(cabinetRespVO.getName())){
                    setCabinetRespInfo(cabinetRespVO, mapType,  mapParentGrid);
                }
            }
            areaCabinetRespVO.setCabinetList(cabinetList);
            return areaCabinetRespVO;
        }).collect(Collectors.toList());
    }

    @Override
    public GoodsGridStatusRespVO getCabinetGridStatusByIp(String ip) {
        List<CabinetDO> cabinetList = cabinetDao.selectList(new LambdaQueryWrapper<CabinetDO>().eq(CabinetDO::getIp, ip));
        GoodsGridStatusRespVO respVO = GoodsGridStatusRespVO.builder().total(0).free(0L).failure(0L).occupied(0L).build();
        if(CollUtil.isNotEmpty(cabinetList)){
            List<GridDO> gridList = gridDao.selectList(new LambdaQueryWrapperX<GridDO>().in(GridDO::getCabinetId,  cabinetList.stream().map( CabinetDO::getId).collect(Collectors.toList())));
            respVO.setFree(gridList.stream().filter( e-> e.getStatus().equals(CabinetGridStatusEnum.KX.getCode())).count());
            respVO.setFailure(gridList.stream().filter( e-> e.getStatus().equals(CabinetGridStatusEnum.GZ.getCode())).count());
            respVO.setOccupied(gridList.stream().filter( e-> e.getStatus().equals(CabinetGridStatusEnum.ZY.getCode())).count());
            respVO.setTotal(gridList.size());
        }
        return respVO;
    }

    @Override
    public AppCabinetRespVO getAppCabinetInfo(String ip) {
        List<CabinetDO> list = cabinetDao.selectList(new LambdaQueryWrapper<CabinetDO>().eq( CabinetDO::getIp, ip));
        if(CollUtil.isEmpty(list)){
            return null;
        }
        Map<String, String> areaMap =  convertToAreaMap(list);
        List<GridDO> gridList = gridDao.selectList(new LambdaQueryWrapperX<GridDO>().in(GridDO::getCabinetId, list.stream().map(CabinetDO::getId).collect(Collectors.toList())));
        Map<String, List<GridDO>> mapParentGrid = gridList.stream().collect(Collectors.groupingBy(GridDO::getCabinetId));

        //柜子型号
        List<CabinetTypeDO> cabinetTypeList = cabinetTypeDao.selectList();
        Map<String, CabinetTypeDO> mapType = convertToCabinetTypeMap(cabinetTypeList);

        //获取所有区域
        List<AreaCabinetRespVO>  respVOList = list.stream().map(CabinetDO::getAreaId).distinct().map(areaId -> {
            AreaCabinetRespVO areaCabinetRespVO = new AreaCabinetRespVO();
            areaCabinetRespVO.setAreaId(areaId);
            areaCabinetRespVO.setId(areaId);
            areaCabinetRespVO.setAreaName(areaMap.get(areaId));
            //获取对应的物品柜`
            List<CabinetRespVO> cabinetList = list.stream().filter(cabinetDO -> cabinetDO.getAreaId().equals(areaId))
                    .map(cabinetDO -> BeanUtils.toBean(cabinetDO, CabinetRespVO.class)
                    ).collect(Collectors.toList());

            Collections.sort(cabinetList, Comparator.comparingInt(CabinetRespVO::getCabinetSort));
            for (CabinetRespVO cabinetRespVO : cabinetList) {
                setCabinetRespInfo(cabinetRespVO, mapType, mapParentGrid);
            }
            areaCabinetRespVO.setCabinetList(cabinetList);
            return areaCabinetRespVO;
        }).collect(Collectors.toList());
        if(CollUtil.isEmpty(respVOList)){
            return null;
        }

        //APP端信息
        AppCabinetRespVO appCabinetRespVO = new AppCabinetRespVO();
        AreaCabinetRespVO cabinetRespVO =  respVOList.get(0);
        appCabinetRespVO.setId( cabinetRespVO.getId());
        appCabinetRespVO.setIdleTotal(0L);
        appCabinetRespVO.setOccupiedTotal(0L);
        appCabinetRespVO.setFaultyTotal(0L);
        appCabinetRespVO.setTotalGrids(0L);
        List<CabinetRespVO> cabinetRespVOList = new ArrayList<>();
        cabinetRespVO.getCabinetList().forEach(e->{
            if(CollUtil.isNotEmpty(e.getGridJsonList())){
                cabinetRespVOList.add(e);
            }
            appCabinetRespVO.setIdleTotal(e.getIdleTotal() == null ? appCabinetRespVO.getIdleTotal() : e.getIdleTotal() + appCabinetRespVO.getIdleTotal());
            appCabinetRespVO.setOccupiedTotal(e.getOccupiedTotal() == null ? appCabinetRespVO.getOccupiedTotal() : e.getOccupiedTotal() + appCabinetRespVO.getOccupiedTotal());
            appCabinetRespVO.setFaultyTotal(e.getFaultyTotal() == null ? appCabinetRespVO.getFaultyTotal() : e.getFaultyTotal() + appCabinetRespVO.getFaultyTotal());
            appCabinetRespVO.setTotalGrids(e.getTotalGrids() == null ? appCabinetRespVO.getTotalGrids() : e.getTotalGrids() + appCabinetRespVO.getTotalGrids());
        });
        appCabinetRespVO.setCabinetList(cabinetRespVOList);
        return appCabinetRespVO;
    }

    @Override
    public AppCabinetSettingRespVO getCabinetISetting(String ip) {
        // 一个ip对应一个主柜
        List<CabinetDO> cabinetList = cabinetDao.selectList(new LambdaQueryWrapper<CabinetDO>().eq(CabinetDO::getIp, ip).eq(CabinetDO::getIsMain, 1 ));
        List<CabinetDO> cabinetListVice = cabinetDao.selectList(new LambdaQueryWrapper<CabinetDO>().eq(CabinetDO::getIp, ip).eq(CabinetDO::getIsMain, 0 ).orderByDesc(CabinetDO::getAddTime));
        if(CollUtil.isNotEmpty(cabinetList)){
            CabinetDO cabinetDO = cabinetList.get(0);
            AppCabinetSettingRespVO appCabinetSettingRespVO = BeanUtils.toBean(cabinetDO,  AppCabinetSettingRespVO.class);
            appCabinetSettingRespVO.setCabinetConfig(String.valueOf( cabinetDO.getTotalGrids()));
            if(CollUtil.isNotEmpty(cabinetListVice)){
                cabinetListVice.forEach( e->{
                    appCabinetSettingRespVO.setCabinetConfig(appCabinetSettingRespVO.getCabinetConfig() + ";" + e.getTotalGrids() );
                });
            }
            return appCabinetSettingRespVO;
        }
        return null;
    }

    @Override
    public Boolean updateCabinetSetting(AppCabinetSettingRespVO appCabinetSettingRespVO) {
        CabinetDO cabinetDO = cabinetDao.selectById(appCabinetSettingRespVO.getId());
        if(cabinetDO == null){
            return false;
        }
        BeanUtils.copyProperties(appCabinetSettingRespVO, cabinetDO );
        cabinetDao.updateById(cabinetDO);
        return true;
    }

    /**
     * 设置格子信息
     * <AUTHOR>
     * @date 2025/6/25 16:19
     * @param [cabinetRespVO, mapType, mapGrid]
     * @return void
     */
    private void setCabinetRespInfo(CabinetRespVO cabinetRespVO, Map<String, CabinetTypeDO> mapType ,  Map<String, List<GridDO>> mapParentGrid){
        CabinetTypeDO cabinetTypeDO =  mapType.get(cabinetRespVO.getTypeCode());
        if(cabinetTypeDO == null){
            throw new RuntimeException("柜子型号数据被删除，请恢复!");
        }
        cabinetRespVO.setRow(cabinetTypeDO.getLayoutRow());
        cabinetRespVO.setColumn(cabinetTypeDO.getLayoutColumn());
        cabinetRespVO.setTypeId(cabinetTypeDO.getId());
        cabinetRespVO.setCabinetImgUrl(cabinetTypeDO.getCabinetImgUrl());

        String json = cabinetTypeDO.getLayoutConfig();
        if(StrUtil.isBlank(json)){
            return;
        }
        cabinetRespVO.setIdleTotal(0L);
        cabinetRespVO.setOccupiedTotal(0L);
        cabinetRespVO.setFaultyTotal(0L);

       List<GridDO> gridList =   mapParentGrid.get(cabinetRespVO.getId());
       if(CollUtil.isEmpty(gridList)){
           throw new RuntimeException("数据异常,请联系管理员处理！");
       }
       Map<String, GridDO> mapGrid = convertToGridMap(gridList);
        JSONArray array = null;
        try {
            array = JSON.parseArray(json);
        }catch (Exception e){
            log.error("JSON解析异常！");
            return;
        }
        List<GridJsonVO> gridJsonVoList = new ArrayList<>();
        //TODO 主柜信息
        for (Object o : array) {
            JSONObject jsonObject = (JSONObject) o;
            GridJsonVO gridJsonVO = new GridJsonVO();
            String gridCode = jsonObject.getString("grid_code");
            gridJsonVO.setRow(jsonObject.getInteger("row"));
            gridJsonVO.setColumn(jsonObject.getInteger("column"));
            GridDO gridDO = mapGrid.get(gridCode);
            if(gridDO != null){
                gridJsonVO.setGridCode(gridDO.getGridCode());
                gridJsonVO.setGridName(gridDO.getGridName());
                gridJsonVO.setGridAllName( cabinetRespVO.getName() + "-"  + gridDO.getGridName());
                gridJsonVO.setGridSize(gridDO.getGridSize());
                gridJsonVO.setStatus(gridDO.getStatus());
                gridJsonVO.setGridId(gridDO.getId());
            }
            gridJsonVoList.add(gridJsonVO);
        }
        cabinetRespVO.setGridJsonList(gridJsonVoList);
        try {
            Map<Integer, Long> countByStatusMap  = countByStatus(gridJsonVoList);
            cabinetRespVO.setIdleTotal(countByStatusMap.get(1) != null ? countByStatusMap.get(1) :  0L);
            cabinetRespVO.setOccupiedTotal(countByStatusMap.get(2) != null ? countByStatusMap.get(2) :  0L );
            cabinetRespVO.setFaultyTotal(countByStatusMap.get(3) != null ? countByStatusMap.get(3) :  0L );
        } catch (Exception e){
            log.error("异常信息", e);
        }
    }


    /**
     * 区域转换
     * <AUTHOR>
     * @date 2025/6/23 21:20
     * @param [cabinetList]
     * @return java.util.Map<java.lang.String,java.lang.String>
     */
    public Map<String, String> convertToAreaMap(List<CabinetDO> cabinetList) {
        return cabinetList.stream()
                .collect(Collectors.toMap(
                        CabinetDO::getAreaId,       // key: areaId
                        CabinetDO::getAreaName,     // value: areaName
                        (existing, replacement) -> existing  // 处理键冲突：保留第一个值
                ));
    }

    /**
     * 格子转换
     * <AUTHOR>
     * @date 2025/6/23 20:33
     * @param [cabinetList]
     * @return java.util.Map<java.lang.String,com.rs.module.ptm.domain.entity.GridDO>
     */
    public Map<String, GridDO> convertToGridMap(List<GridDO> cabinetList) {
        return cabinetList.stream()
                .collect(Collectors.toMap(
                        GridDO::getGridName,       // key: areaId
                        e ->e,     // value: areaName
                        (existing, replacement) -> existing  // 处理键冲突：保留第一个值
                ));
    }

    /**
     * 格子转换
     * <AUTHOR>
     * @date 2025/6/23 20:33
     * @param [cabinetList]
     * @return java.util.Map<java.lang.String,com.rs.module.ptm.domain.entity.GridDO>
     */
    public Map<String, CabinetTypeDO> convertToCabinetTypeMap(List<CabinetTypeDO> cabinetList) {
        return cabinetList.stream()
                .collect(Collectors.toMap(
                        CabinetTypeDO::getTypeCode,       // key: areaId
                        e ->e,     // value: areaName
                        (existing, replacement) -> existing  // 处理键冲突：保留第一个值
                ));
    }


    /**
     * 根据格子状态统计数量
     * @param gridList 格子列表
     * @return 状态码到数量的映射（1:空闲, 2:占用, 3:故障）
     */
    public Map<Integer, Long> countByStatus(List<GridJsonVO> gridList) {
        return gridList.stream()
                .collect(
                        Collectors.groupingBy(
                                GridJsonVO::getStatus,  // 按status分组
                                Collectors.counting()   // 统计每组数量
                        )
                );
    }



}
