package com.rs.module.ptm.domain.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 附物管理-储物柜子格子配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GridSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("所属柜子ID")
    @NotEmpty(message = "所属柜子ID不能为空")
    private String cabinetId;

    @ApiModelProperty("柜子编号，如A-01、B-12（区域-序号）")
    private String cabinetCode;

    @ApiModelProperty("格子编号，如A-01-01（柜子编号-格子序号）")
    @NotEmpty(message = "格子编号，如A-01-01（柜子编号-格子序号）不能为空")
    private String gridCode;

    @ApiModelProperty("格子尺寸，如S（小）/M（中）/L（大）")
    private String gridSize;

    @ApiModelProperty("格子状态：1-空闲/2-占用/3-故障（枚举值）")
    @NotNull(message = "格子状态：1-空闲/2-占用/3-故障（枚举值）不能为空")
    private Integer status;

    @ApiModelProperty("主柜格子ID")
    private String mainCabinetId;

    @ApiModelProperty("柜子名称")
    private String gridName;


}
