package com.rs.module.ptm.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 附物管理-储物柜子格子配置 DO
 *
 * <AUTHOR>
 */
@TableName("ptm_cabinet_grid")
@KeySequence("ptm_cabinet_grid_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ptm_cabinet_grid")
public class GridDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 所属柜子ID
     */
    private String cabinetId;

    /**
     * 主柜格子ID
     */
    private String mainCabinetId;
    /**
     * 柜子编号，如A-01、B-12（区域-序号）
     */
    private String cabinetCode;

    /**
     * 柜子名称
     */
    private String gridName;
    /**
     * 格子编号，如A-01-01（柜子编号-格子序号）
     */
    private String gridCode;
    /**
     * 格子尺寸，如S（小）/M（中）/L（大）
     */
    private String gridSize;
    /**
     * 格子状态：1-空闲/2-占用/3-故障（枚举值）
     */
    private Integer status;

}
