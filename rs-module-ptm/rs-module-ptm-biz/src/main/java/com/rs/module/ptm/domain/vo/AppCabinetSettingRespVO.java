package com.rs.module.ptm.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@ApiModel(description = "管理后台 - 附物管理-app-储物柜子信息-配置信息 Response VO")
@Data
public class AppCabinetSettingRespVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @NotBlank(message = "配置Id不能为空！")
    private String id;

    @ApiModelProperty("版本号")
    private String copyright;

    @ApiModelProperty("柜子名称-设备名称")
    private String name;

    @ApiModelProperty("开柜方式 1刷脸，2账号 3手势： 字典：ZD_FWGJ_KGFS")
    private String cabinetOpenMethod;

    @ApiModelProperty("监所名称")
    private String orgName;

    @ApiModelProperty("操作时间-秒")
    private Integer operationTime;

    @ApiModelProperty("相机设置 旋转角度-秒")
    private Integer cameraSetting;

    @ApiModelProperty("版本号")
    private String versionNumber;

    @ApiModelProperty("mac")
    private String mac;

    @ApiModelProperty("本机IP")
    private String ip;

    @ApiModelProperty("柜子配置信息 例如 13;20")
    private String cabinetConfig;
}
