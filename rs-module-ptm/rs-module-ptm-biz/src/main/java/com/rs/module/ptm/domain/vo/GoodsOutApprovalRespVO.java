package com.rs.module.ptm.domain.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 附物管理-附物取出登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsOutApprovalRespVO extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("取出物品信息")
    private String goodsInfo;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("取出原因")
    @Trans(type = TransType.DICTIONARY, key = "ZD_QCDJBQCYY")
    private String takeoutReason;
    @ApiModelProperty("取出方式")
    @Trans(type = TransType.DICTIONARY, key = "ZD_QCDJBQCFS")
    private String takeoutMethod;

    @ApiModelProperty("登记经办人")
    private String operatorSfzh;
    @ApiModelProperty("登记经办人姓名")
    private String operatorXm;
    @ApiModelProperty("登记经办时间")
    private Date operatorTime;
}
