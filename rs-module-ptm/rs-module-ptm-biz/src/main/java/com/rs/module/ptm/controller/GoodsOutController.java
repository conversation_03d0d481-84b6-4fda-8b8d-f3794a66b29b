package com.rs.module.ptm.controller;

import com.rs.module.ptm.domain.entity.GoodsOutDO;
import com.rs.module.ptm.domain.vo.*;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;
import com.rs.module.ptm.service.GoodsOutService;

@Api(tags = "附物管理-附物取出登记")
@RestController
@RequestMapping("/ptm/personal/goodsOut")
@Validated
public class GoodsOutController {

    @Resource
    private GoodsOutService goodsOutService;

    @PostMapping("/create")
    @ApiOperation(value = "创建附物管理-附物取出登记")
    public CommonResult<String> createGoodsOut(@Valid @RequestBody GoodsOutSaveReqVO createReqVO) {
        return success(goodsOutService.createGoodsOut(createReqVO));
    }

    @PostMapping("/getDeailsById")
    @ApiOperation(value = "根据取出登记表id获取取出明细")
    public CommonResult<GoodsOutRespVO> getDetailsById(@RequestParam("id") String id) {
        return success(goodsOutService.getDetailsById(id));
    }

    @PostMapping("/getDqwByJgrybm")
    @ApiOperation(value = "根据人员编码获取人员待取物信息")
    public CommonResult<List<GoodsOutRespVO>> getDqwByJgrybm(@RequestParam("jgrybm") String jgrybm) {
        return success(goodsOutService.getDqwByJgrybm(jgrybm));
    }

    @PostMapping("/takeOutGoodsApproval")
    @ApiOperation(value = "取物审批")
    public CommonResult<Boolean> takeOutGoodsApproval(@RequestBody GoodsOutApprovalVO approvalVO) {
        return success(goodsOutService.takeOutGoodsApproval(approvalVO));
    }

    @PostMapping("/takeOutGoodsApprovalPage")
    @ApiOperation(value = "取物待审批列表")
    public CommonResult<PageResult<GoodsOutApprovalRespVO>> takeOutGoodsApprovalPage(@Valid @RequestBody GoodsOutApprovalPageReqVO goodsOutApprovalPageReqVO) {
        return success(goodsOutService.takeOutGoodsApprovalPage(goodsOutApprovalPageReqVO));
    }

    @PostMapping("/takeOutGoods")
    @ApiOperation(value = "取物")
    public CommonResult<String> takeOutGoods(String id) {
        return success(goodsOutService.takeOutGoods(id));
    }







}
