package com.rs.module.ptm.domain.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * app端存放位置，状态修改   已登记后，改为已存入
 * <AUTHOR>
 * @date 2025/6/21 16:29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsItemsStoredDepositReqVO extends BaseVO implements TransPojo{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("监管人员编码")
    @NotBlank(message = "监管人员编码不能为空！")
    private String jgrybm;

    @ApiModelProperty("ip地址 （根据IP地址来分主柜柜子 客户端应该可以拿到） ")
    @NotBlank(message = "柜子的ip地址！")
    private String ip;

    @NotBlank(message = "存放位置地址！")
    @ApiModelProperty("存放位置")
    private String storageLocation;

    @ApiModelProperty("物品编号")
    //@NotEmpty(message = "物品编码不能为空！")
    private String wpbhList;

    @ApiModelProperty("物品编号")
    //@NotEmpty(message = "物品编码不能为空！")
    private List<String> wpbhArrayList;

}
