package com.rs.module.ptm.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ptm.domain.entity.GridDO;
import com.rs.module.ptm.domain.vo.GridListReqVO;
import com.rs.module.ptm.domain.vo.GridPageReqVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 附物管理-储物柜子格子配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface GridDao extends IBaseDao<GridDO> {


    default PageResult<GridDO> selectPage(GridPageReqVO reqVO) {
        Page<GridDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<GridDO> wrapper = new LambdaQueryWrapperX<GridDO>()
            .eqIfPresent(GridDO::getCabinetId, reqVO.getCabinetId())
            .eqIfPresent(GridDO::getCabinetCode, reqVO.getCabinetCode())
            .eqIfPresent(GridDO::getGridCode, reqVO.getGridNumber())
            .eqIfPresent(GridDO::getGridSize, reqVO.getGridSize())
            .eqIfPresent(GridDO::getStatus, reqVO.getStatus())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(GridDO::getAddTime);
        }
        Page<GridDO> gridPage = selectPage(page, wrapper);
        return new PageResult<>(gridPage.getRecords(), gridPage.getTotal());
    }
    default List<GridDO> selectList(GridListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<GridDO>()
            .eqIfPresent(GridDO::getCabinetId, reqVO.getCabinetId())
            .eqIfPresent(GridDO::getCabinetCode, reqVO.getCabinetCode())
            //.eqIfPresent(GridDO::getGridNumber, reqVO.getGridNumber())
            .eqIfPresent(GridDO::getGridSize, reqVO.getGridSize())
            .eqIfPresent(GridDO::getStatus, reqVO.getStatus())
        .orderByDesc(GridDO::getAddTime));    }


    }
