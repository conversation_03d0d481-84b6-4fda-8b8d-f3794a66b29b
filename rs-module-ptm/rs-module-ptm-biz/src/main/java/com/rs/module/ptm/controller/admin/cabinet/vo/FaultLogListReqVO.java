package com.rs.module.ptm.controller.admin.cabinet.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 附物管理-储物柜子故障日志列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FaultLogListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("所属柜子ID")
    private String cabinetId;

    @ApiModelProperty("cabinet_name")
    private String cabinetName;

    @ApiModelProperty("柜子编号，如A-01、B-12（区域-序号）")
    private String cabinetCode;

    @ApiModelProperty("格子编号，如A-01-01（柜子编号-格子编号）")
    private String gridCode;

    @ApiModelProperty("格子名称")
    private String gridName;

    @ApiModelProperty("格子状态：1-空闲/2-占用/3-故障（枚举值）")
    private Short status;

}
