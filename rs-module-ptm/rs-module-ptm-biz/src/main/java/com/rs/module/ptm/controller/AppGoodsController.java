package com.rs.module.ptm.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.ptm.domain.vo.*;
import com.rs.module.ptm.service.CabinetService;
import com.rs.module.ptm.service.GoodsDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;


@Api(tags = "附物柜APP端接口")
@RestController
@RequestMapping("/app/ptm/personal/")
@Validated
public class AppGoodsController {

    @Resource
    private GoodsDetailService goodsDetailService;

    @Resource
    private CabinetService cabinetService;

    @GetMapping("/getItemsTdoStoredListByJgrybm")
    @ApiOperation(value = "app根据人员编码获取人员待存入列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "被监管人员编码"),
            @ApiImplicitParam(name = "ip", value = "主柜ip")
    })
    @LogRecordAnnotation(
            bizModule = "ptm:AppGoods:getItemsTdoStoredListByJgrybm", operateType = LogOperateType.QUERY,
            title = "app根据人员编码获取人员待存入列表", success = "app根据人员编码获取人员待存入列表记录成功",
            fail = "app根据人员编码获取人员待存入列表记录失败", extraInfo = "{{#jgrybm}}")
    public CommonResult<List<GoodsItemsStoredListRespVO>> getItemsTdoStoredListByJgrybm(@RequestParam("jgrybm") String jgrybm,
                                                                                        @RequestParam(value = "ip") String ip) {
        // 询过过王杰，设计上只有一个主柜，所以过滤数据不需要根据柜子去过滤区分。 后续有问题产品背锅
        // 2025-06-25 情况有变，不止一个主柜  被产品坑一波了 需要根据ip返回的来区分不同主柜
        // 2025-06-26 咨询过王杰，物品大多，估计有误一个格子塞不下，要多开柜子的情况，暂不考虑
        List<GoodsItemsStoredListRespVO> respVOList = goodsDetailService.getItemsTdoStoredListByJgrybm(jgrybm, ip);
        return success(respVOList);
    }


    @GetMapping("/getCabinetGridStatusByIp")
    @ApiOperation(value = "app端获取格子状态信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ip", value = "主柜ip")
    })
    @LogRecordAnnotation(
            bizModule = "ptm:AppGoods:getCabinetGridStatusByIp", operateType = LogOperateType.QUERY,
            title = "app根据柜子IP获取柜子-格子状态", success = "app根据柜子IP获取柜子-格子状态-成功",
            fail = "app根据柜子IP获取柜子-格子状态-失败", extraInfo = "{{#jgrybm}}")
    public CommonResult<GoodsGridStatusRespVO> getCabinetGridStatusByIp(@RequestParam(value = "ip") String ip) {
        GoodsGridStatusRespVO respVOList = cabinetService.getCabinetGridStatusByIp(ip);
        return success(respVOList);
    }

    @PostMapping("/depositItemsUpdateStatus")
    @ApiOperation(value = "app端存入物品状态修改")
    @LogRecordAnnotation(
            bizModule = "ptm:AppGoods:depositItemsUpdateStatus", operateType = LogOperateType.UPDATE,
            title = "app端存入物品状态修改", success = "app端传入物品状态修改-记录成功",
            fail = "app端存入物品状态修改-记录失败", extraInfo = "{{#reqVO}}")
    public CommonResult<Boolean> depositItemsUpdateStatus(@RequestBody GoodsItemsStoredDepositReqVO reqVO){
        if(StrUtil.isNotBlank(reqVO.getWpbhList())){
            JSONArray array =  JSON.parseArray(reqVO.getWpbhList());
            List<String> result = new ArrayList<>();
            for (Object o : array) {
                JSONObject json = (JSONObject) o;
                result.add(json.getString("wpbh") );
            }
            reqVO.setWpbhArrayList(result);
        }
        Boolean flag = goodsDetailService.depositItemsUpdateStatus(reqVO);
        return success(flag);
    }

    @PostMapping("/taskOutItemsUpdateStatus")
    @ApiOperation(value = "app端取出物品状态修改")
    @LogRecordAnnotation(
            bizModule = "ptm:AppGoods:depositItemsUpdateStatus", operateType = LogOperateType.UPDATE,
            title = "app端取出物品状态修改", success = "app端传入物品状态修改-记录成功",
            fail = "app端取出物品状态修改-记录失败", extraInfo = "{{#json}}")
    public CommonResult<Boolean> taskOutItemsUpdateStatus(@RequestBody GoodsItemsStoredDepositReqVO reqVO) {
        if(StrUtil.isNotBlank(reqVO.getWpbhList())){
            JSONArray array =  JSON.parseArray(reqVO.getWpbhList());
            List<String> result = new ArrayList<>();
            for (Object o : array) {
                JSONObject json = (JSONObject) o;
                result.add(json.getString("wpbh") );
            }
            reqVO.setWpbhArrayList(result);
        }
        Boolean flag = goodsDetailService.taskOutItemsUpdateStatus(reqVO);
        return success(flag);
    }


    @GetMapping("/getTaskOutListByJgrybm")
    @ApiOperation(value = "app根据人员编码获取人员待取出列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "被监管人员编码"),
            @ApiImplicitParam(name = "ip", value = "主柜ip")
    })
    @LogRecordAnnotation(
            bizModule = "ptm:AppGoods:getTaskOutListByJgrybm", operateType = LogOperateType.QUERY,
            title = "app根据人员编码获取人员待取出列表", success = "app根据人员编码获取人员待存入列表记录成功",
            fail = "app根据人员编码获取人员待取出列表表记录失败", extraInfo = "{{#jgrybm}}")
    public CommonResult<List<GoodsItemsStoredListRespVO>> getTaskOutListByJgrybm(@RequestParam("jgrybm") String jgrybm,
                                                                                 @RequestParam(value = "ip") String ip) {
        List<GoodsItemsStoredListRespVO> respVOList = goodsDetailService.getTaskOutListByJgrybm(jgrybm, ip);
        return success(respVOList);
    }


    @GetMapping("/getCabinetInfo")
    @ApiOperation(value = "app根据主柜IP获取柜子信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ip", value = "主柜ip")
    })
    @LogRecordAnnotation(
            bizModule = "ptm:AppGoods:getCabinetInfo", operateType = LogOperateType.QUERY,
            title = "app根据主柜IP获取柜子信息-列表", success = "app根据主柜IP获取柜子信息-记录成功",
            fail = "app根据主柜IP获取柜子信息-记录失败", extraInfo = "{{#ip}}")
    public CommonResult<AppCabinetRespVO> getCabinetInfo(@RequestParam(value = "ip") String ip) {
        AppCabinetRespVO respVO = cabinetService.getAppCabinetInfo(ip);
        return success(respVO);
    }


    @GetMapping("/getCabinetISetting")
    @ApiOperation(value = "app根据IP获取柜子信息配置信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ip", value = "ip")
    })
    @LogRecordAnnotation(
            bizModule = "ptm:AppGoods:getCabinetISetting", operateType = LogOperateType.QUERY,
            title = "app根据IP获取柜子信息配置信息", success = "app根据IP获取柜子信息配置信息记录成功",
            fail = "app根据人员编码获取人员待取出列表表记录失败", extraInfo = "{{#ip}}")
    public CommonResult<AppCabinetSettingRespVO> getCabinetISetting(@RequestParam(value = "ip") String ip) {
        AppCabinetSettingRespVO respVO = cabinetService.getCabinetISetting(ip);
        return success(respVO);
    }

    @PostMapping("/updateCabinetSetting")
    @ApiOperation(value = "更新柜子配置信息")
    @LogRecordAnnotation(
            bizModule = "ptm:AppGoods:updateCabinetSetting", operateType = LogOperateType.QUERY,
            title = "更新柜子配置信息", success = "更新柜子配置信息-记录成功",
            fail = "更新柜子配置信息-记录失败")
    public CommonResult<Boolean> updateCabinetSetting(AppCabinetSettingRespVO appCabinetSettingRespVO) {
        Boolean flag = cabinetService.updateCabinetSetting(appCabinetSettingRespVO);
        return success(flag);
    }

}
