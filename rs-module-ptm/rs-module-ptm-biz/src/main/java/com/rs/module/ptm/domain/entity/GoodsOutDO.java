package com.rs.module.ptm.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 附物管理-附物取出登记 DO
 *
 * <AUTHOR>
 */
@TableName("ptm_personal_goods_out")
@KeySequence("ptm_personal_goods_out_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ptm_personal_goods_out")
public class GoodsOutDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 登记表id
     */
    private String personalGoodsId;
    /**
     * 监管人员编码
     */
    private String jgrybm;

    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 取出原因
     */
    private String takeoutReason;
    /**
     * 取出方式
     */
    private String takeoutMethod;
    /**
     * 取出备注
     */
    private String takeoutRemark;
    /**
     * 取出人姓名
     */
    private String takeoutPerson;
    /**
     * 取出人证件号码
     */
    private String takeoutIdNumber;
    /**
     * 取出人联系方式
     */
    private String takeoutContact;
    /**
     * 与被监管人关系
     */
    private String relationship;
    /**
     * 取出人家庭住址
     */
    private String takeoutAddress;
    /**
     * 证明材料上传地址（存储文件路径）
     */
    private String proofMaterialsPath;
    /**
     * 邮寄人姓名
     */
    private String senderName;
    /**
     * 邮寄人联系方式
     */
    private String senderContact;
    /**
     * 收件人姓名
     */
    private String recipientName;
    /**
     * 收件人联系方式
     */
    private String recipientContact;

    /**
     * 收件人地址
     */
    private String recipientAddress;
    /**
     * 邮件单号
     */
    private String mailTrackingNumber;
    /**
     * 移交单位
     */
    private String transferUnit;
    /**
     * 销毁方式
     */
    private String destructionMethod;
    /**
     * 销毁人
     */
    private String destructionPerson;
    /**
     * 登记经办人
     */
    private String operatorSfzh;
    /**
     * 登记经办人姓名
     */
    private String operatorXm;
    /**
     * 登记经办时间
     */
    private Date operatorTime;
    /**
     * 状态
     */
    private String status;
    /**
     * 审批人身份证号
     */
    private String approverSfzh;
    /**
     * 审批人姓名
     */
    private String approverXm;
    /**
     * 审批时间
     */
    private Date approverTime;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 审批人签名
     */
    private String approvalAutograph;
    /**
     * 审批人签名日期
     */
    private Date approvalAutographTime;
    /**
     * 审核意见
     */
    private String approvalComments;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

}
