package com.rs.module.ptm.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 附物管理-储物柜子类型信息 DO
 *
 * <AUTHOR>
 */
@TableName("ptm_storage_cabinet_type")
@KeySequence("ptm_storage_cabinet_type_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ptm_storage_cabinet_type")
public class CabinetTypeDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 类型编号,规则：
     */
    private String typeCode;
    /**
     * 类型名称
     */
    private String typeName;
    /**
     * 柜子型号
     */
    private String model;
    /**
     * 供应商
     */
    private String supplier;
    /**
     * 是否主柜
     */
    private Integer isMain;
    /**
     * 柜子格子编号前缀规则，如A-、B-（自动拼接序号）
     */
    private String gridPrefixRule;
    /**
     * 柜子默认格子数量，如20、30
     */
    private Integer defaultGridCount;
    /**
     * 柜子最大格子数量限制
     */
    private Integer maxGridCount;
    /**
     * 类型备注，如适用场景说明
     */
    private String remark;
    /**
     * 布局配置，json存储
     */
    private String layoutConfig;
    /**
     * 行数
     */
    private Integer layoutRow;
    /**
     * 列数
     */
    private Integer layoutColumn;

    /**
     * 柜子URL
     */
    private String cabinetImgUrl;

}
