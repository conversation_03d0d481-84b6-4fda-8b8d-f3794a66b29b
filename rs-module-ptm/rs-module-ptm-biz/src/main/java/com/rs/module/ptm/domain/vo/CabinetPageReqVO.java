package com.rs.module.ptm.domain.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel(description = "管理后台 - 附物管理-储物柜子信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CabinetPageReqVO extends PageParam{

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("柜子名称")
    private String name;

    @ApiModelProperty("柜子编号，如A-01、B-12（区域+序号）")
    private String code;

    @ApiModelProperty("柜子类型编号")
    private String typeCode;

    @ApiModelProperty("柜子实际位置，如1楼储物间A区")
    private String cabinetLocation;

    @ApiModelProperty("所属区域ID")
    private String areaId;

    @ApiModelProperty("所属区域名称")
    private String areaName;

    @ApiModelProperty("柜子IP")
    private String ip;

    @ApiModelProperty("柜子端口")
    private Integer port;

    @ApiModelProperty("柜子mac地址")
    private String mac;

    @ApiModelProperty("柜子总格子数，可动态配置（如20格、30格）")
    private Integer totalGrids;

    @ApiModelProperty("柜子状态：1-可用/2-维修中/3-停用（枚举值）")
    private Short status;

    @ApiModelProperty("是否主柜")
    private Short isMain;

    @ApiModelProperty("主柜子ID")
    private String mainCabinetId;

    @ApiModelProperty("柜子排序编号")
    private Integer cabinetSort;

    @ApiModelProperty("排序属性")
    List<OrderItem> orderFields;


}
