package com.rs.module.ptm.domain.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@ApiModel(description = "管理后台 - 附物管理-储物柜子信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AreaCabinetRespVO  extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监区id")
    private String areaId;

    @ApiModelProperty("监区名称")
    private String areaName;

    @ApiModelProperty("格子总数")
    private String totalGrids;

    @ApiModelProperty("物品柜列表")
    private List<CabinetRespVO> cabinetList;

}
