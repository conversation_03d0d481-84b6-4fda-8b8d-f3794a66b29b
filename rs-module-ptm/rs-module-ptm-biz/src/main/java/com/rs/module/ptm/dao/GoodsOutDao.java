package com.rs.module.ptm.dao;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ptm.domain.entity.GoodsOutDO;
import com.rs.module.ptm.domain.vo.GoodsOutPageReqVO;
import com.rs.module.ptm.domain.vo.GoodsOutListReqVO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 附物管理-附物取出登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface GoodsOutDao extends IBaseDao<GoodsOutDO> {


    default PageResult<GoodsOutDO> selectPage(GoodsOutPageReqVO reqVO) {
        Page<GoodsOutDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<GoodsOutDO> wrapper = new LambdaQueryWrapperX<GoodsOutDO>()
            .eqIfPresent(GoodsOutDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(GoodsOutDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(GoodsOutDO::getTakeoutReason, reqVO.getTakeoutReason())
            .eqIfPresent(GoodsOutDO::getTakeoutMethod, reqVO.getTakeoutMethod())
            .eqIfPresent(GoodsOutDO::getTakeoutRemark, reqVO.getTakeoutRemark())
            .eqIfPresent(GoodsOutDO::getTakeoutPerson, reqVO.getTakeoutPerson())
            .eqIfPresent(GoodsOutDO::getTakeoutIdNumber, reqVO.getTakeoutIdNumber())
            .eqIfPresent(GoodsOutDO::getTakeoutContact, reqVO.getTakeoutContact())
            .eqIfPresent(GoodsOutDO::getRelationship, reqVO.getRelationship())
            .eqIfPresent(GoodsOutDO::getTakeoutAddress, reqVO.getTakeoutAddress())
            .eqIfPresent(GoodsOutDO::getProofMaterialsPath, reqVO.getProofMaterialsPath())
            .likeIfPresent(GoodsOutDO::getSenderName, reqVO.getSenderName())
            .eqIfPresent(GoodsOutDO::getSenderContact, reqVO.getSenderContact())
            .likeIfPresent(GoodsOutDO::getRecipientName, reqVO.getRecipientName())
            .eqIfPresent(GoodsOutDO::getRecipientContact, reqVO.getRecipientContact())
            .eqIfPresent(GoodsOutDO::getMailTrackingNumber, reqVO.getMailTrackingNumber())
            .eqIfPresent(GoodsOutDO::getTransferUnit, reqVO.getTransferUnit())
            .eqIfPresent(GoodsOutDO::getDestructionMethod, reqVO.getDestructionMethod())
            .eqIfPresent(GoodsOutDO::getDestructionPerson, reqVO.getDestructionPerson())
            .eqIfPresent(GoodsOutDO::getOperatorSfzh, reqVO.getOperatorSfzh())
            .eqIfPresent(GoodsOutDO::getOperatorXm, reqVO.getOperatorXm())
            .betweenIfPresent(GoodsOutDO::getOperatorTime, reqVO.getOperatorTime())
            .eqIfPresent(GoodsOutDO::getStatus, reqVO.getStatus())
            .eqIfPresent(GoodsOutDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(GoodsOutDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(GoodsOutDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(GoodsOutDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(GoodsOutDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(GoodsOutDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(GoodsOutDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(GoodsOutDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(GoodsOutDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(GoodsOutDO::getAddTime);
        }
        Page<GoodsOutDO> goodsOutPage = selectPage(page, wrapper);
        return new PageResult<>(goodsOutPage.getRecords(), goodsOutPage.getTotal());
    }
    default List<GoodsOutDO> selectList(GoodsOutListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<GoodsOutDO>()
            .eqIfPresent(GoodsOutDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(GoodsOutDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(GoodsOutDO::getTakeoutReason, reqVO.getTakeoutReason())
            .eqIfPresent(GoodsOutDO::getTakeoutMethod, reqVO.getTakeoutMethod())
            .eqIfPresent(GoodsOutDO::getTakeoutRemark, reqVO.getTakeoutRemark())
            .eqIfPresent(GoodsOutDO::getTakeoutPerson, reqVO.getTakeoutPerson())
            .eqIfPresent(GoodsOutDO::getTakeoutIdNumber, reqVO.getTakeoutIdNumber())
            .eqIfPresent(GoodsOutDO::getTakeoutContact, reqVO.getTakeoutContact())
            .eqIfPresent(GoodsOutDO::getRelationship, reqVO.getRelationship())
            .eqIfPresent(GoodsOutDO::getTakeoutAddress, reqVO.getTakeoutAddress())
            .eqIfPresent(GoodsOutDO::getProofMaterialsPath, reqVO.getProofMaterialsPath())
            .likeIfPresent(GoodsOutDO::getSenderName, reqVO.getSenderName())
            .eqIfPresent(GoodsOutDO::getSenderContact, reqVO.getSenderContact())
            .likeIfPresent(GoodsOutDO::getRecipientName, reqVO.getRecipientName())
            .eqIfPresent(GoodsOutDO::getRecipientContact, reqVO.getRecipientContact())
            .eqIfPresent(GoodsOutDO::getMailTrackingNumber, reqVO.getMailTrackingNumber())
            .eqIfPresent(GoodsOutDO::getTransferUnit, reqVO.getTransferUnit())
            .eqIfPresent(GoodsOutDO::getDestructionMethod, reqVO.getDestructionMethod())
            .eqIfPresent(GoodsOutDO::getDestructionPerson, reqVO.getDestructionPerson())
            .eqIfPresent(GoodsOutDO::getOperatorSfzh, reqVO.getOperatorSfzh())
            .eqIfPresent(GoodsOutDO::getOperatorXm, reqVO.getOperatorXm())
            .betweenIfPresent(GoodsOutDO::getOperatorTime, reqVO.getOperatorTime())
            .eqIfPresent(GoodsOutDO::getStatus, reqVO.getStatus())
            .eqIfPresent(GoodsOutDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(GoodsOutDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(GoodsOutDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(GoodsOutDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(GoodsOutDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(GoodsOutDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(GoodsOutDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(GoodsOutDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(GoodsOutDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(GoodsOutDO::getAddTime));    }


    }
