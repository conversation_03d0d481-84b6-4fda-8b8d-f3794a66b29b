package com.rs.module.ptm.domain.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@ApiModel(description = "app根据人员编码获取人员待存取列表")
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsItemsStoredListRespVO extends BaseVO implements TransPojo{

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("存放位置格子ID")
    private String storageLocation;

    @ApiModelProperty("存放位置-名称")
    private String storageLocationName;

    @ApiModelProperty("列表返回")
    private List<GoodsDetailRespVO> resultList;


    @ApiModelProperty("没有选择物品信息的")
    private NotStorageLocation notStorageLocation;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    static public class  NotStorageLocation {

        @ApiModelProperty("物品编号")
        private String wpbh;

        @ApiModelProperty("物品名称")
        @Trans(type = TransType.DICTIONARY, key = "ZD_FWGLWPZL")
        private String name;

        @ApiModelProperty("物品类型")
        @Trans(type = TransType.DICTIONARY, key = "ZD_FWGLWPLX")
        private String goodsType;

        @ApiModelProperty("数量")
        private Integer quantity;

        @ApiModelProperty("数量单位")
        @Trans(type = TransType.DICTIONARY, key = "ZD_WPDW")
        private String unit;
    }


}
