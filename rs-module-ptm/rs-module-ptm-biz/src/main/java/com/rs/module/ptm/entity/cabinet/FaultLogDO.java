package com.rs.module.ptm.entity.cabinet;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 附物管理-储物柜子故障日志 DO
 *
 * <AUTHOR>
 */
@TableName("ptm_cabinet_fault_log")
@KeySequence("ptm_cabinet_fault_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ptm_cabinet_fault_log")
public class FaultLogDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 所属柜子ID
     */
    private String cabinetId;
    /**
     * cabinet_name
     */
    private String cabinetName;
    /**
     * 柜子编号，如A-01、B-12（区域-序号）
     */
    private String cabinetCode;
    /**
     * 格子编号，如A-01-01（柜子编号-格子编号）
     */
    private String gridCode;
    /**
     * 格子名称
     */
    private String gridName;
    /**
     * 格子状态：1-空闲/2-占用/3-故障（枚举值）
     */
    private Short status;

}
