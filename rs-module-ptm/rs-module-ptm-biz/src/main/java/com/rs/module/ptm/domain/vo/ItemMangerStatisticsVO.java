package com.rs.module.ptm.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description = "物品管理-台账统计页面")
@Data
public class ItemMangerStatisticsVO implements Serializable {

    private static final long serialVersionUID = 1375721171915158021L;

    @ApiModelProperty("入所待存物总数")
    private String inPoliceTodoItemsStoredTotal;

    @ApiModelProperty("出所待取物总数")
    private String outPoliceTodoItemsStoredTotal;

    @ApiModelProperty("累计存物数量")
    private String cumulativeStorageTotal;

    @ApiModelProperty("现存物品数量")
    private String existingStorageTotal;

    @ApiModelProperty("物品柜总量")
    private String cabinetStorageTotal;

    @ApiModelProperty("占用物品柜数量")
    private String occupancyCabinetStorageTotal;

    @ApiModelProperty("空闲物品柜数量")
    private String freeCabinetStorageTotal;

    @ApiModelProperty("故障物品柜数量")
    private String malfunctionCabinetStorageTotal;
}
