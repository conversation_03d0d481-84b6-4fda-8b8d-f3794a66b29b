package com.rs.module.ptm.controller;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.ptm.domain.entity.GoodsDO;
import com.rs.module.ptm.domain.vo.GoodsLogRespVO;
import com.rs.module.ptm.domain.vo.GoodsRespVO;
import com.rs.module.ptm.domain.vo.WpStatisticRespVO;
import com.rs.module.ptm.service.GoodsService;
import com.rs.module.ptm.vo.GoodsSaveReqVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;


@Api(tags = "附物管理-附物")
@RestController
@RequestMapping("/ptm/personal/goods")
@Validated
public class GoodsController {

    @Resource
    private GoodsService goodsService;

    @PostMapping("/storageGoods")
    @ApiOperation(value = "存物")
    public CommonResult<String> storageGoods(@RequestParam("id") String id) {
        return success(goodsService.storageGoods(id));
    }

//    @PostMapping("/takeOutGoods")
//    @ApiOperation(value = "取物")
//    public CommonResult<String> takeOutGoods(String id) {
//        return success(goodsService.takeOutGoods(id));
//    }

    @PostMapping("/create")
    @ApiOperation(value = "附物登记")
    public CommonResult<String> createGoods(@Valid @RequestBody GoodsSaveReqVO createReqVO) {
        return success(goodsService.createGoods(createReqVO));
    }

    @GetMapping("/getDqwGoodsDetailList")
    @ApiOperation(value = "物品登记表明细")
    public CommonResult<GoodsRespVO> getDqwGoodsDetailList(@RequestParam("id") String id) {
        return success(goodsService.getDqwGoodsDetailList(id));
    }


    @GetMapping("/getDetailsById")
    @ApiOperation(value = "物品登记表明细")
    @ApiImplicitParam(name = "id", value = "物品登记表id")
    public CommonResult<GoodsRespVO> getDetailsById(@RequestParam("id") String id) {
        return success(goodsService.getDetailsById(id));
    }


    @GetMapping("/getDcwByJgrybm")
    @ApiOperation(value = "app端根据监管人员编号获得待存物列表")
    @ApiImplicitParam(name = "jgrybm", value = "监管人员编码")
    public CommonResult<List<GoodsRespVO>> getDcwByJgrybm(@RequestParam("jgrybm") String jgrybm) {
        return success(goodsService.getDcwByJgrybm(jgrybm));
    }

    @GetMapping("/goodsLogList")
    @ApiOperation(value = "根据登记表获取物品存取记录")
    @ApiImplicitParam(name = "id", value = "id")
    public CommonResult<List<GoodsLogRespVO>> goodsLogList(@RequestParam("id") String id) {
        return success(goodsService.goodsLogList(id));
    }

    @GetMapping("/goodsLogListDetail")
    @ApiOperation(value = "获取存取记录详情 这里可能是存入登记表的id也可能是取出登记表的Id 要用businessType区分")
    public CommonResult<Object> goodsLogListDetail(@RequestParam("id") String id,@RequestParam("businessType") String businessType) {
        return success(goodsService.goodsLogListDetail(id,businessType));
    }


    @GetMapping("/goodsLogListTotal")
    @ApiOperation(value = "获取总的存取记录")
    @ApiImplicitParam(name = "id", value = "id")
    public CommonResult<List<GoodsLogRespVO>> goodsLogListTotal(@RequestParam("id") String id) {
        return success(goodsService.goodsLogListTotal(id));
    }

    @GetMapping("/getGoodsIdByJgrybm")
    @ApiOperation(value = "根据监管人员编码获取登记表id")
    @ApiImplicitParam(name = "jgrybm", value = "jgrybm")
    public CommonResult<String> getGoodsIdByJgrybm(@RequestParam("jgrybm") String jgrybm) {
        return success(goodsService.lambdaQuery()
                .eq(GoodsDO::getJgrybm,jgrybm)
                .list().stream().map(GoodsDO::getId).findFirst().orElse(null)
        );
    }

    @GetMapping("/wpStatisticInfo")
    @ApiOperation(value = "根据物品编号展示物品信息")
    @ApiImplicitParam(name = "wpbh", value = "wpbh")
    public CommonResult<WpStatisticRespVO> wpStatisticInfo(@RequestParam("wpbh") String wpbh) {
        return success(goodsService.wpStatisticInfo(wpbh));
    }

}
