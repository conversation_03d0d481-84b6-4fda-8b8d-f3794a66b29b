package com.rs.module.ptm.service.impl;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ptm.dao.GridDao;
import com.rs.module.ptm.domain.entity.GridDO;
import com.rs.module.ptm.domain.vo.GridListReqVO;
import com.rs.module.ptm.domain.vo.GridPageReqVO;
import com.rs.module.ptm.domain.vo.GridSaveReqVO;
import com.rs.module.ptm.domain.vo.GridUpdateReqVO;
import com.rs.module.ptm.service.GridService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 附物管理-储物柜子格子配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GridServiceImpl extends BaseServiceImpl<GridDao, GridDO> implements GridService {

    @Resource
    private GridDao gridDao;

    @Override
    public String createGrid(GridSaveReqVO createReqVO) {
        // 插入
        GridDO grid = BeanUtils.toBean(createReqVO, GridDO.class);
        gridDao.insert(grid);
        // 返回
        return grid.getId();
    }

    @Override
    public void updateGrid(GridSaveReqVO updateReqVO) {
        // 校验存在
        validateGridExists(updateReqVO.getId());
        // 更新
        GridDO updateObj = BeanUtils.toBean(updateReqVO, GridDO.class);
        gridDao.updateById(updateObj);
    }

    @Override
    public void deleteGrid(String id) {
        // 校验存在
        validateGridExists(id);
        // 删除
        gridDao.deleteById(id);
    }

    private void validateGridExists(String id) {
        if (gridDao.selectById(id) == null) {
            throw new ServerException("附物管理-储物柜子格子配置数据不存在");
        }
    }

    @Override
    public GridDO getGrid(String id) {
        return gridDao.selectById(id);
    }

    @Override
    public PageResult<GridDO> getGridPage(GridPageReqVO pageReqVO) {
        return gridDao.selectPage(pageReqVO);
    }

    @Override
    public List<GridDO> getGridList(GridListReqVO listReqVO) {
        return gridDao.selectList(listReqVO);
    }

    @Override
    public void updateCabinet(GridUpdateReqVO updateReqVO) {
        GridDO gridDO = gridDao.selectById(updateReqVO.getId() );
        Assert.notNull(gridDO, "传入ID不存在" + updateReqVO.getId());
        gridDO.setStatus( updateReqVO.getStatus());
        gridDao.updateById( gridDO);
    }

}
