package com.rs.module.ptm.domain.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel(description = "管理后台 - 附物管理-储物柜子类型信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CabinetTypePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("类型编号,规则：")
    private String typeCode;

    @ApiModelProperty("类型名称")
    private String typeName;

    @ApiModelProperty("柜子型号")
    private String model;

    @ApiModelProperty("供应商")
    private String supplier;

    @ApiModelProperty("是否主柜")
    private Short isMain;

    @ApiModelProperty("柜子格子编号前缀规则，如A-、B-（自动拼接序号）")
    private String gridPrefixRule;

    @ApiModelProperty("柜子默认格子数量，如20、30")
    private Integer defaultGridCount;

    @ApiModelProperty("柜子最大格子数量限制")
    private Integer maxGridCount;

    @ApiModelProperty("类型备注，如适用场景说明")
    private String remark;

    @ApiModelProperty("布局配置，json存储")
    private String layoutConfig;

    @ApiModelProperty("行数")
    private Integer layoutRow;

    @ApiModelProperty("列数")
    private Integer layoutColumn;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
