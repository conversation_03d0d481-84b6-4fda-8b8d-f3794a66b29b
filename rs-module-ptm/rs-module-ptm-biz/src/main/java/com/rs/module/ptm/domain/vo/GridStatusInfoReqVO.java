package com.rs.module.ptm.domain.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@ApiModel(description = "管理后台 - 附物管理-储物柜子格子配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GridStatusInfoReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @NotNull(message = "格子ID")
    private String id;

    @ApiModelProperty("格子状态：1正常  2")
    @NotNull(message = "格子状态：不能为空")
    private Integer status;

}
