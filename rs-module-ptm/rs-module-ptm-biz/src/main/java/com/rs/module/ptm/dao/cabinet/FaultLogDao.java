package com.rs.module.ptm.dao.cabinet;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ptm.entity.cabinet.FaultLogDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ptm.controller.admin.cabinet.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 附物管理-储物柜子故障日志 Dao
*
* <AUTHOR>
*/
@Mapper
public interface FaultLogDao extends IBaseDao<FaultLogDO> {


    default PageResult<FaultLogDO> selectPage(FaultLogPageReqVO reqVO) {
        Page<FaultLogDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<FaultLogDO> wrapper = new LambdaQueryWrapperX<FaultLogDO>()
            .eqIfPresent(FaultLogDO::getCabinetId, reqVO.getCabinetId())
            .likeIfPresent(FaultLogDO::getCabinetName, reqVO.getCabinetName())
            .eqIfPresent(FaultLogDO::getCabinetCode, reqVO.getCabinetCode())
            .eqIfPresent(FaultLogDO::getGridCode, reqVO.getGridCode())
            .likeIfPresent(FaultLogDO::getGridName, reqVO.getGridName())
            .eqIfPresent(FaultLogDO::getStatus, reqVO.getStatus())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(FaultLogDO::getAddTime);
        }
        Page<FaultLogDO> faultLogPage = selectPage(page, wrapper);
        return new PageResult<>(faultLogPage.getRecords(), faultLogPage.getTotal());
    }
    default List<FaultLogDO> selectList(FaultLogListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<FaultLogDO>()
            .eqIfPresent(FaultLogDO::getCabinetId, reqVO.getCabinetId())
            .likeIfPresent(FaultLogDO::getCabinetName, reqVO.getCabinetName())
            .eqIfPresent(FaultLogDO::getCabinetCode, reqVO.getCabinetCode())
            .eqIfPresent(FaultLogDO::getGridCode, reqVO.getGridCode())
            .likeIfPresent(FaultLogDO::getGridName, reqVO.getGridName())
            .eqIfPresent(FaultLogDO::getStatus, reqVO.getStatus())
        .orderByDesc(FaultLogDO::getAddTime));    }


    }
