package com.rs.module.ptm.domain.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@ApiModel(description = "管理后台 - 附物管理-储物柜子格子配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class WpStatisticRespVO extends BaseVO {

    @ApiModelProperty(value = "物品登记")
    private GoodsInOutRespVO goodsDjRespVO;

    @ApiModelProperty(value = "物品存入")
    private GoodsInOutRespVO goodsInRespVO;

    @ApiModelProperty(value = "物品取出登记")
    private GoodsOutRespVO goodsOutDjRespVO;

    @ApiModelProperty(value = "物品取出")
    private GoodsInOutRespVO goodsOutRespVO;

    @ApiModelProperty(value = "流程轨迹")
    private List<GoodsLogRespVO> trackList;
}
