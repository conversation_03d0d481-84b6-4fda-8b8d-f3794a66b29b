package com.rs.module.ptm.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 附物管理-附物明细 DO
 *
 * <AUTHOR>
 */
@TableName("ptm_personal_goods_detail")
@KeySequence("ptm_personal_goods_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ptm_personal_goods_detail")
public class GoodsDetailDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 附物ID
     */
    private String personalGoodsId;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 物品编号
     */
    private String wpbh;
    /**
     * 物品名称
     */
    private String name;

    /**
     * 物品名称翻译
     */
    private String nameName;
    /**
     * 物品类型
     */
    private String goodsType;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 数量单位
     */
    private String unit;
    /**
     * 存放位置，如A监区主柜B09号柜
     */
    private String storageLocation;
    /**
     * 物品特征描述
     */
    private String features;
    /**
     * 照片存储地址
     */
    private String photoPath;
    /**
     * 物品照片数量
     */
    private Integer photoCount;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态
     */
    private String status;
    /**
     * 登记经办人
     */
    private String operatorSfzh;
    /**
     * 登记经办人姓名
     */
    private String operatorXm;
    /**
     * 登记经办时间
     */
    private Date operatorTime;

    /**
     * 存放位置-中文
     */
    private String storageLocationName;


}
