package com.rs.module.ptm.service;

import java.util.*;
import javax.validation.*;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.ptm.domain.entity.GoodsDetailDO;
import com.rs.module.ptm.domain.entity.GoodsInOutDO;
import com.rs.module.ptm.domain.vo.GoodsInOutListReqVO;
import com.rs.module.ptm.domain.vo.GoodsInOutPageReqVO;
import com.rs.module.ptm.domain.vo.GoodsInOutSaveReqVO;
/**
 * 附物管理-附物存入取出 Service 接口
 *
 * <AUTHOR>
 */
public interface GoodsInOutService extends IBaseService<GoodsInOutDO>{

    /**
     * 创建附物管理-附物存入取出
     * @return 编号
     */
    String saveGoodsInOut(GoodsDetailDO goodsDetailDO);

    /**
     * 创建附物管理-附物存入取出
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createGoodsInOut(@Valid GoodsInOutSaveReqVO createReqVO);

    /**
     * 更新附物管理-附物存入取出
     *
     * @param updateReqVO 更新信息
     */
    void updateGoodsInOut(@Valid GoodsInOutSaveReqVO updateReqVO);

    /**
     * 删除附物管理-附物存入取出
     *
     * @param id 编号
     */
    void deleteGoodsInOut(String id);

    /**
     * 获得附物管理-附物存入取出
     *
     * @param id 编号
     * @return 附物管理-附物存入取出
     */
    GoodsInOutDO getGoodsInOut(String id);

    /**
    * 获得附物管理-附物存入取出分页
    *
    * @param pageReqVO 分页查询
    * @return 附物管理-附物存入取出分页
    */
    PageResult<GoodsInOutDO> getGoodsInOutPage(GoodsInOutPageReqVO pageReqVO);

    /**
    * 获得附物管理-附物存入取出列表
    *
    * @param listReqVO 查询条件
    * @return 附物管理-附物存入取出列表
    */
    List<GoodsInOutDO> getGoodsInOutList(GoodsInOutListReqVO listReqVO);


}
