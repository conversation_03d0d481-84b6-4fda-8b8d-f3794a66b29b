package com.rs.module.ptm.domain.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel(description = "管理后台 - 附物管理-储物柜子类型信息新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CabinetTypeSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("类型编号,规则：")
    @NotEmpty(message = "类型编号,规则：不能为空")
    private String typeCode;

    @ApiModelProperty("类型名称")
    @NotEmpty(message = "类型名称不能为空")
    private String typeName;

    @ApiModelProperty("柜子型号")
    private String model;

    @ApiModelProperty("供应商")
    //@NotEmpty(message = "供应商不能为空")
    private String supplier;

    @ApiModelProperty("是否主柜")
    @NotNull(message = "是否主柜不能为空")
    private Short isMain;

    @ApiModelProperty("柜子格子编号前缀规则，如A-、B-（自动拼接序号）")
    private String gridPrefixRule;

    @ApiModelProperty("柜子默认格子数量，如20、30")
    private Integer defaultGridCount;

    @ApiModelProperty("柜子最大格子数量限制")
    private Integer maxGridCount;

    @ApiModelProperty("类型备注，如适用场景说明")
    private String remark;

    @ApiModelProperty("布局配置，json存储")
    @NotEmpty(message = "布局配置，json存储不能为空")
    private String layoutConfig;

    @ApiModelProperty("行数")
    private Integer layoutRow;

    @ApiModelProperty("列数")
    private Integer layoutColumn;

    @ApiModelProperty("柜子URL")
    private String cabinetImgUrl;

}
