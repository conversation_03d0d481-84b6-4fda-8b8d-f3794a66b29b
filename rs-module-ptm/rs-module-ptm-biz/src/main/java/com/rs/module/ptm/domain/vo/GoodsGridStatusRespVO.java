package com.rs.module.ptm.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description = "柜子状态-格子-状态-信息")
@Data
@Builder
public class GoodsGridStatusRespVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("总量")
    private Integer total;

    @ApiModelProperty("空闲")
    private Long free;

    @ApiModelProperty("故障")
    private Long failure;

    @ApiModelProperty("占用")
    private Long occupied;


}
