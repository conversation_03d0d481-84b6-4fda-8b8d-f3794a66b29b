package com.rs.module.ptm.dao;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ptm.domain.entity.GoodsOutDetailDO;
import com.rs.module.ptm.domain.vo.GoodsOutDetailListReqVO;
import com.rs.module.ptm.domain.vo.GoodsOutDetailPageReqVO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 附物管理-附物取出登记明细 Dao
*
* <AUTHOR>
*/
@Mapper
public interface GoodsOutDetailDao extends IBaseDao<GoodsOutDetailDO> {


    default PageResult<GoodsOutDetailDO> selectPage(GoodsOutDetailPageReqVO reqVO) {
        Page<GoodsOutDetailDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<GoodsOutDetailDO> wrapper = new LambdaQueryWrapperX<GoodsOutDetailDO>()
            .eqIfPresent(GoodsOutDetailDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(GoodsOutDetailDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(GoodsOutDetailDO::getPersonalGoodsOutId, reqVO.getPersonalGoodsOutId())
            .eqIfPresent(GoodsOutDetailDO::getWpbh, reqVO.getWpbh())
            .eqIfPresent(GoodsOutDetailDO::getTakeoutQuantity, reqVO.getTakeoutQuantity())
            .eqIfPresent(GoodsOutDetailDO::getRemark, reqVO.getRemark())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(GoodsOutDetailDO::getAddTime);
        }
        Page<GoodsOutDetailDO> goodsOutDetailPage = selectPage(page, wrapper);
        return new PageResult<>(goodsOutDetailPage.getRecords(), goodsOutDetailPage.getTotal());
    }
    default List<GoodsOutDetailDO> selectList(GoodsOutDetailListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<GoodsOutDetailDO>()
            .eqIfPresent(GoodsOutDetailDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(GoodsOutDetailDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(GoodsOutDetailDO::getPersonalGoodsOutId, reqVO.getPersonalGoodsOutId())
            .eqIfPresent(GoodsOutDetailDO::getWpbh, reqVO.getWpbh())
            .eqIfPresent(GoodsOutDetailDO::getTakeoutQuantity, reqVO.getTakeoutQuantity())
            .eqIfPresent(GoodsOutDetailDO::getRemark, reqVO.getRemark())
        .orderByDesc(GoodsOutDetailDO::getAddTime));    }


    }
