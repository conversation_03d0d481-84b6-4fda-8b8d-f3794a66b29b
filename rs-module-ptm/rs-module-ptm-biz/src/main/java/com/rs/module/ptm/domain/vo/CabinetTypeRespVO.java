package com.rs.module.ptm.domain.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 附物管理-储物柜子类型信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CabinetTypeRespVO extends BaseVO implements TransPojo{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("类型编号,规则：")
    private String typeCode;

    @ApiModelProperty("类型名称")
    private String typeName;

    @ApiModelProperty("柜子型号")
    private String model;

    @ApiModelProperty("供应商")
    private String supplier;

    @ApiModelProperty("是否主柜")
    private Short isMain;

    @ApiModelProperty("柜子格子编号前缀规则，如A-、B-（自动拼接序号）")
    private String gridPrefixRule;

    @ApiModelProperty("柜子默认格子数量，如20、30")
    private Integer defaultGridCount;

    @ApiModelProperty("柜子最大格子数量限制")
    private Integer maxGridCount;

    @ApiModelProperty("类型备注，如适用场景说明")
    private String remark;

    @ApiModelProperty("布局配置，json存储")
    private String layoutConfig;

    @ApiModelProperty("行数")
    private Integer layoutRow;

    @ApiModelProperty("列数")
    private Integer layoutColumn;

    @ApiModelProperty("柜子URL")
    private String cabinetImgUrl;

}
