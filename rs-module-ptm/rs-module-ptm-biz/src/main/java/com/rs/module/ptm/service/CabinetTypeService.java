package com.rs.module.ptm.service;

import java.util.*;
import javax.validation.*;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.ptm.domain.entity.CabinetTypeDO;
import com.rs.module.ptm.domain.vo.CabinetTypeListReqVO;
import com.rs.module.ptm.domain.vo.CabinetTypePageReqVO;
import com.rs.module.ptm.domain.vo.CabinetTypeSaveReqVO;

/**
 * 附物管理-储物柜子类型信息 Service 接口
 *
 * <AUTHOR>
 */
public interface CabinetTypeService extends IBaseService<CabinetTypeDO>{

    /**
     * 创建附物管理-储物柜子类型信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCabinetType(@Valid CabinetTypeSaveReqVO createReqVO);

    /**
     * 更新附物管理-储物柜子类型信息
     *
     * @param updateReqVO 更新信息
     */
    void updateCabinetType(@Valid CabinetTypeSaveReqVO updateReqVO);

    /**
     * 删除附物管理-储物柜子类型信息
     *
     * @param id 编号
     */
    void deleteCabinetType(String id);

    /**
     * 获得附物管理-储物柜子类型信息
     *
     * @param id 编号
     * @return 附物管理-储物柜子类型信息
     */
    CabinetTypeDO getCabinetType(String id);

    /**
    * 获得附物管理-储物柜子类型信息分页
    *
    * @param pageReqVO 分页查询
    * @return 附物管理-储物柜子类型信息分页
    */
    PageResult<CabinetTypeDO> getCabinetTypePage(CabinetTypePageReqVO pageReqVO);

    /**
    * 获得附物管理-储物柜子类型信息列表
    *
    * @param listReqVO 查询条件
    * @return 附物管理-储物柜子类型信息列表
    */
    List<CabinetTypeDO> getCabinetTypeList(CabinetTypeListReqVO listReqVO);


}
