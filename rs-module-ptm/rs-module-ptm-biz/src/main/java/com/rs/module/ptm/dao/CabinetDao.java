package com.rs.module.ptm.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ptm.domain.entity.CabinetDO;
import com.rs.module.ptm.domain.vo.CabinetListReqVO;
import com.rs.module.ptm.domain.vo.CabinetPageReqVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 附物管理-储物柜子信息 Dao
*
* <AUTHOR>
*/
@Mapper
public interface CabinetDao extends IBaseDao<CabinetDO> {



    default PageResult<CabinetDO> selectPage(CabinetPageReqVO reqVO) {
        Page<CabinetDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<CabinetDO> wrapper = new LambdaQueryWrapperX<CabinetDO>()
                .likeIfPresent(CabinetDO::getName, reqVO.getName())
                .eqIfPresent(CabinetDO::getCode, reqVO.getCode())
                .eqIfPresent(CabinetDO::getTypeCode, reqVO.getTypeCode())
                .eqIfPresent(CabinetDO::getCabinetLocation, reqVO.getCabinetLocation())
                .eqIfPresent(CabinetDO::getAreaId, reqVO.getAreaId())
                .likeIfPresent(CabinetDO::getAreaName, reqVO.getAreaName())
                .eqIfPresent(CabinetDO::getIp, reqVO.getIp())
                .eqIfPresent(CabinetDO::getPort, reqVO.getPort())
                .eqIfPresent(CabinetDO::getMac, reqVO.getMac())
                .eqIfPresent(CabinetDO::getTotalGrids, reqVO.getTotalGrids())
                .eqIfPresent(CabinetDO::getStatus, reqVO.getStatus())
                .eqIfPresent(CabinetDO::getIsMain, reqVO.getIsMain())
                .eqIfPresent(CabinetDO::getMainCabinetId, reqVO.getMainCabinetId())
                .eqIfPresent(CabinetDO::getCabinetSort, reqVO.getCabinetSort())
                ;
        if(reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        }else {
            wrapper.orderByDesc(CabinetDO::getAddTime);
        }
        Page<CabinetDO> cabinetPage = selectPage(page, wrapper);
        return new PageResult<>(cabinetPage.getRecords(), cabinetPage.getTotal());
    }


    default List<CabinetDO> selectList(CabinetListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CabinetDO>()
                .likeIfPresent(CabinetDO::getName, reqVO.getName())
                .eqIfPresent(CabinetDO::getCode, reqVO.getCode())
                .eqIfPresent(CabinetDO::getTypeCode, reqVO.getTypeCode())
                .eqIfPresent(CabinetDO::getCabinetLocation, reqVO.getCabinetLocation())
                .eqIfPresent(CabinetDO::getAreaId, reqVO.getAreaId())
                .likeIfPresent(CabinetDO::getAreaName, reqVO.getAreaName())
                .eqIfPresent(CabinetDO::getIp, reqVO.getIp())
                .eqIfPresent(CabinetDO::getPort, reqVO.getPort())
                .eqIfPresent(CabinetDO::getMac, reqVO.getMac())
                .eqIfPresent(CabinetDO::getMac, reqVO.getMac())
                .eqIfPresent(CabinetDO::getTotalGrids, reqVO.getTotalGrids())
                .eqIfPresent(CabinetDO::getStatus, reqVO.getStatus())
                .eqIfPresent(CabinetDO::getIsMain, reqVO.getIsMain())
                .eqIfPresent(CabinetDO::getOrgCode, reqVO.getOrgCode())
                .eqIfPresent(CabinetDO::getMainCabinetId, reqVO.getMainCabinetId())
                .eqIfPresent(CabinetDO::getCabinetSort, reqVO.getCabinetSort())
                .orderByDesc(CabinetDO::getAddTime));    }


    }
