package com.rs.module.ptm.domain.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 附物管理")
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsLogRespVO extends BaseVO implements TransPojo {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "业务类型")
//    @Trans(type = TransType.DICTIONARY, key = "ZD_FWGLWPCFZT")
    private String businessType;

    @ApiModelProperty(value = "业务类型名称")
    private String businessTypeName;

    @ApiModelProperty(value = "存取物品信息")
    private String goodsInfo;

    @ApiModelProperty("登记经办人")
    private String operatorSfzh;
    @ApiModelProperty("登记经办人姓名")
    private String operatorXm;
    @ApiModelProperty("登记经办时间")
    private Date operatorTime;

    @ApiModelProperty("存取记录")
    private Object logsDetail;

    @ApiModelProperty("是否有审批轨迹 1是0否")
    private Integer isApproveTrack;

    @ApiModelProperty("取出方式 02 他人待领取 ")
    private String takeoutMethod;

    @ApiModelProperty("文书预览传入的ID")
    private String docId;



}
