package com.rs.module.ptm.domain.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 附物管理-附物取出登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsOutSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("取出原因")
    @NotEmpty(message = "取出原因不能为空")
    private String takeoutReason;

    @ApiModelProperty("登记表id")
    private String personalGoodsId;

    @ApiModelProperty("取出方式")
    @NotEmpty(message = "取出方式不能为空")
    private String takeoutMethod;

    @ApiModelProperty("取出备注")
    private String takeoutRemark;

    @ApiModelProperty("取出人姓名")
    private String takeoutPerson;

    @ApiModelProperty("取出人证件号码")
    private String takeoutIdNumber;

    @ApiModelProperty("取出人联系方式")
    private String takeoutContact;

    @ApiModelProperty("与被监管人关系")
    private String relationship;

    @ApiModelProperty("取出人家庭住址")
    private String takeoutAddress;

    @ApiModelProperty("证明材料上传地址（存储文件路径）")
    private List<String> proofMaterialsPathList;

    @ApiModelProperty("邮寄人姓名")
    private String senderName;

    @ApiModelProperty("邮寄人联系方式")
    private String senderContact;

    @ApiModelProperty("收件人姓名")
    private String recipientName;

    @ApiModelProperty("收件人联系方式")
    private String recipientContact;

    @ApiModelProperty("收件人地址")
    private String recipientAddress;

    @ApiModelProperty("邮件单号")
    private String mailTrackingNumber;

    @ApiModelProperty("移交单位")
    private String transferUnit;

    @ApiModelProperty("销毁方式")
    private String destructionMethod;

    @ApiModelProperty("销毁人")
    private String destructionPerson;



    @ApiModelProperty(value = "审批人身份证号",hidden = true)
    private String approverSfzh;

    @ApiModelProperty(value = "审批人姓名",hidden = true)
    private String approverXm;

    @ApiModelProperty(value = "审批时间",hidden = true)
    private Date approverTime;

    @ApiModelProperty(value ="审批结果",hidden = true)
    private String approvalResult;

    @ApiModelProperty(value = "审批人签名",hidden = true)
    private String approvalAutograph;

    @ApiModelProperty(value = "审批人签名日期",hidden = true)
    private Date approvalAutographTime;

    @ApiModelProperty(value = "审核意见",hidden = true)
    private String approvalComments;

    @ApiModelProperty("物品取出详情信息")
    List<GoodsOutDetailSaveReqVO> goodsOutDetailSaveReqVOList;

    @ApiModelProperty(value = "ACT流程实例Id",hidden = true)
    private String actInstId;

    @ApiModelProperty(value = "任务ID",hidden = true)
    private String taskId;

}
