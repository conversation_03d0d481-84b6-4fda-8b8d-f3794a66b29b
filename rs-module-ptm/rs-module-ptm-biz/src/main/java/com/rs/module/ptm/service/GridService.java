package com.rs.module.ptm.service;

import java.util.*;
import javax.validation.*;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.ptm.domain.entity.GridDO;
import com.rs.module.ptm.domain.vo.GridListReqVO;
import com.rs.module.ptm.domain.vo.GridPageReqVO;
import com.rs.module.ptm.domain.vo.GridSaveReqVO;
import com.rs.module.ptm.domain.vo.GridUpdateReqVO;

/**
 * 附物管理-储物柜子格子配置 Service 接口
 *
 * <AUTHOR>
 */
public interface GridService extends IBaseService<GridDO>{

    /**
     * 创建附物管理-储物柜子格子配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createGrid(@Valid GridSaveReqVO createReqVO);

    /**
     * 更新附物管理-储物柜子格子配置
     *
     * @param updateReqVO 更新信息
     */
    void updateGrid(@Valid GridSaveReqVO updateReqVO);

    /**
     * 删除附物管理-储物柜子格子配置
     *
     * @param id 编号
     */
    void deleteGrid(String id);

    /**
     * 获得附物管理-储物柜子格子配置
     *
     * @param id 编号
     * @return 附物管理-储物柜子格子配置
     */
    GridDO getGrid(String id);

    /**
    * 获得附物管理-储物柜子格子配置分页
    *
    * @param pageReqVO 分页查询
    * @return 附物管理-储物柜子格子配置分页
    */
    PageResult<GridDO> getGridPage(GridPageReqVO pageReqVO);

    /**
    * 获得附物管理-储物柜子格子配置列表
    *
    * @param listReqVO 查询条件
    * @return 附物管理-储物柜子格子配置列表
    */
    List<GridDO> getGridList(GridListReqVO listReqVO);

    /**
     * 修改格子状态
     * @param updateReqVO
     */
    void updateCabinet(GridUpdateReqVO updateReqVO);
}
