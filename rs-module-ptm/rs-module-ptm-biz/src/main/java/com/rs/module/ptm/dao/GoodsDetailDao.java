package com.rs.module.ptm.dao;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ptm.domain.entity.GoodsDetailDO;
import com.rs.module.ptm.domain.vo.GoodsDetailListReqVO;
import com.rs.module.ptm.domain.vo.GoodsDetailPageReqVO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 附物管理-附物明细 Dao
*
* <AUTHOR>
*/
@Mapper
public interface GoodsDetailDao extends IBaseDao<GoodsDetailDO> {


    default PageResult<GoodsDetailDO> selectPage(GoodsDetailPageReqVO reqVO) {
        Page<GoodsDetailDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<GoodsDetailDO> wrapper = new LambdaQueryWrapperX<GoodsDetailDO>()
            .eqIfPresent(GoodsDetailDO::getPersonalGoodsId, reqVO.getPersonalGoodsId())
            .eqIfPresent(GoodsDetailDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(GoodsDetailDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(GoodsDetailDO::getWpbh, reqVO.getWpbh())
            .likeIfPresent(GoodsDetailDO::getName, reqVO.getName())
            .eqIfPresent(GoodsDetailDO::getGoodsType, reqVO.getGoodsType())
            .eqIfPresent(GoodsDetailDO::getQuantity, reqVO.getQuantity())
            .eqIfPresent(GoodsDetailDO::getUnit, reqVO.getUnit())
            .eqIfPresent(GoodsDetailDO::getStorageLocation, reqVO.getStorageLocation())
            .eqIfPresent(GoodsDetailDO::getFeatures, reqVO.getFeatures())
            .eqIfPresent(GoodsDetailDO::getPhotoPath, reqVO.getPhotoPath())
            .eqIfPresent(GoodsDetailDO::getPhotoCount, reqVO.getPhotoCount())
            .eqIfPresent(GoodsDetailDO::getRemark, reqVO.getRemark())
            .eqIfPresent(GoodsDetailDO::getStatus, reqVO.getStatus())
            .eqIfPresent(GoodsDetailDO::getOperatorSfzh, reqVO.getOperatorSfzh())
            .eqIfPresent(GoodsDetailDO::getOperatorXm, reqVO.getOperatorXm())
            .betweenIfPresent(GoodsDetailDO::getOperatorTime, reqVO.getOperatorTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(GoodsDetailDO::getAddTime);
        }
        Page<GoodsDetailDO> goodsDetailPage = selectPage(page, wrapper);
        return new PageResult<>(goodsDetailPage.getRecords(), goodsDetailPage.getTotal());
    }
    default List<GoodsDetailDO> selectList(GoodsDetailListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<GoodsDetailDO>()
            .eqIfPresent(GoodsDetailDO::getPersonalGoodsId, reqVO.getPersonalGoodsId())
            .eqIfPresent(GoodsDetailDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(GoodsDetailDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(GoodsDetailDO::getWpbh, reqVO.getWpbh())
            .likeIfPresent(GoodsDetailDO::getName, reqVO.getName())
            .eqIfPresent(GoodsDetailDO::getGoodsType, reqVO.getGoodsType())
            .eqIfPresent(GoodsDetailDO::getQuantity, reqVO.getQuantity())
            .eqIfPresent(GoodsDetailDO::getUnit, reqVO.getUnit())
            .eqIfPresent(GoodsDetailDO::getStorageLocation, reqVO.getStorageLocation())
            .eqIfPresent(GoodsDetailDO::getFeatures, reqVO.getFeatures())
            .eqIfPresent(GoodsDetailDO::getPhotoPath, reqVO.getPhotoPath())
            .eqIfPresent(GoodsDetailDO::getPhotoCount, reqVO.getPhotoCount())
            .eqIfPresent(GoodsDetailDO::getRemark, reqVO.getRemark())
            .eqIfPresent(GoodsDetailDO::getStatus, reqVO.getStatus())
            .eqIfPresent(GoodsDetailDO::getOperatorSfzh, reqVO.getOperatorSfzh())
            .eqIfPresent(GoodsDetailDO::getOperatorXm, reqVO.getOperatorXm())
            .betweenIfPresent(GoodsDetailDO::getOperatorTime, reqVO.getOperatorTime())
        .orderByDesc(GoodsDetailDO::getAddTime));    }


    default GoodsDetailDO getByWpbh(String wpbh) {
        return selectOne(new LambdaQueryWrapperX<GoodsDetailDO>()
                .eqIfPresent(GoodsDetailDO::getWpbh,wpbh).orderByDesc(GoodsDetailDO::getAddTime));
    }
}
