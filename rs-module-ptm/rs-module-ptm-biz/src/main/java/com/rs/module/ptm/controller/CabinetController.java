package com.rs.module.ptm.controller;

import cn.hutool.core.net.NetUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ptm.domain.entity.CabinetDO;
import com.rs.module.ptm.domain.vo.*;
import com.rs.module.ptm.service.CabinetService;
import com.rs.module.ptm.service.GridService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.InetSocketAddress;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;


@Api(tags = "附物管理-储物柜子信息")
@RestController
@RequestMapping("/ptm/storage/cabinet")
@Validated
public class CabinetController {

    @Resource
    private CabinetService cabinetService;

    @Resource
    private GridService gridService;

    @PostMapping("/create")
    @ApiOperation(value = "创建附物管理-储物柜子信息")
    public CommonResult<String> createCabinet(@Valid @RequestBody CabinetSaveReqVO createReqVO) {
        return success(cabinetService.createCabinet(createReqVO));
    }

    @PostMapping("/setGridStatusInfo")
    @ApiOperation(value = "设置格子状态")
    public CommonResult<Boolean> setGridStatusInfo(@Valid @RequestBody GridUpdateReqVO updateReqVO) {
        gridService.updateCabinet(updateReqVO);
        return success(true);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新附物管理-储物柜子信息")
    public CommonResult<Boolean> updateCabinet(@Valid @RequestBody CabinetSaveReqVO updateReqVO) {
        cabinetService.updateCabinet(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除附物管理-储物柜子信息")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteCabinet(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           cabinetService.deleteCabinet(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得附物管理-储物柜子信息")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<CabinetRespVO> getCabinet(@RequestParam("id") String id) {
        CabinetRespVO cabinet = cabinetService.getCabinet(id);
        return success(cabinet);
    }

    @PostMapping("/getMainCabinetTree")
    @ApiOperation(value = "获得附物管理-主柜树")
    public CommonResult<List<AreaCabinetRespVO>> getMainCabinetTree() {
        return success(cabinetService.getMainCabinetTree());
    }

    @PostMapping("/getCabineList")
    @ApiOperation(value = "获得附物管理-储物柜子列表")
    public CommonResult<List<CabinetRespStreamlineVO>> getCabinetList(@Valid @RequestBody CabinetListReqVO cabinetListReq) {
        List<CabinetDO> cabinetList = cabinetService.getCabinetList(cabinetListReq);
        return success(BeanUtils.toBean(cabinetList, CabinetRespStreamlineVO.class));
    }

    @PostMapping("/testCabinetConnect")
    @ApiOperation(value = "物品柜连接测试")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ip", value = "IP地址"),
            @ApiImplicitParam(name = "port", value = "端口")
    })
    public CommonResult<Boolean> testCabinetConnect(
            @RequestParam("ip") String ip,
            @RequestParam(value = "port") Integer port
    ) {
        // 创建 InetSocketAddress 对象
        InetSocketAddress address = new InetSocketAddress(ip, port);
        int timeout = 3000; // 超时时间，单位毫秒
        boolean isPortOpen = NetUtil.isOpen(address, timeout);
        return success(isPortOpen);
    }


}
