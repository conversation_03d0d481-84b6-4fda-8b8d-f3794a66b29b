package com.rs.module.ptm.dao;

import java.util.*;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ptm.domain.entity.GoodsInOutDO;
import com.rs.module.ptm.domain.vo.GoodsInOutListReqVO;
import com.rs.module.ptm.domain.vo.GoodsInOutPageReqVO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
 * 附物管理-附物存入取出 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface GoodsInOutDao extends IBaseDao<GoodsInOutDO> {


    default PageResult<GoodsInOutDO> selectPage(GoodsInOutPageReqVO reqVO) {
        Page<GoodsInOutDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<GoodsInOutDO> wrapper = new LambdaQueryWrapperX<GoodsInOutDO>()
                .eqIfPresent(GoodsInOutDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(GoodsInOutDO::getJgryxm, reqVO.getJgryxm())
                .eqIfPresent(GoodsInOutDO::getInOutType, reqVO.getInOutType())
                .eqIfPresent(GoodsInOutDO::getBusinessId, reqVO.getBusinessId())
                .eqIfPresent(GoodsInOutDO::getWpbh, reqVO.getWpbh())
                .likeIfPresent(GoodsInOutDO::getName, reqVO.getName())
                .eqIfPresent(GoodsInOutDO::getGoodsType, reqVO.getGoodsType())
                .eqIfPresent(GoodsInOutDO::getQuantity, reqVO.getQuantity())
                .eqIfPresent(GoodsInOutDO::getUnit, reqVO.getUnit())
                .eqIfPresent(GoodsInOutDO::getStorageLocation, reqVO.getStorageLocation())
                .eqIfPresent(GoodsInOutDO::getFeatures, reqVO.getFeatures())
                .eqIfPresent(GoodsInOutDO::getRemark, reqVO.getRemark())
                .eqIfPresent(GoodsInOutDO::getStatus, reqVO.getStatus())
                .eqIfPresent(GoodsInOutDO::getTakeoutQuantity, reqVO.getTakeoutQuantity())
                .eqIfPresent(GoodsInOutDO::getTakeoutReason, reqVO.getTakeoutReason())
                .eqIfPresent(GoodsInOutDO::getTakeoutMethod, reqVO.getTakeoutMethod())
                .eqIfPresent(GoodsInOutDO::getOperatorSfzh, reqVO.getOperatorSfzh())
                .eqIfPresent(GoodsInOutDO::getOperatorXm, reqVO.getOperatorXm())
                .betweenIfPresent(GoodsInOutDO::getOperatorTime, reqVO.getOperatorTime());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(GoodsInOutDO::getAddTime);
        }
        Page<GoodsInOutDO> goodsInOutPage = selectPage(page, wrapper);
        return new PageResult<>(goodsInOutPage.getRecords(), goodsInOutPage.getTotal());
    }

    default List<GoodsInOutDO> selectList(GoodsInOutListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<GoodsInOutDO>()
                .eqIfPresent(GoodsInOutDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(GoodsInOutDO::getJgryxm, reqVO.getJgryxm())
                .eqIfPresent(GoodsInOutDO::getInOutType, reqVO.getInOutType())
                .eqIfPresent(GoodsInOutDO::getBusinessId, reqVO.getBusinessId())
                .eqIfPresent(GoodsInOutDO::getWpbh, reqVO.getWpbh())
                .likeIfPresent(GoodsInOutDO::getName, reqVO.getName())
                .eqIfPresent(GoodsInOutDO::getGoodsType, reqVO.getGoodsType())
                .eqIfPresent(GoodsInOutDO::getQuantity, reqVO.getQuantity())
                .eqIfPresent(GoodsInOutDO::getUnit, reqVO.getUnit())
                .eqIfPresent(GoodsInOutDO::getStorageLocation, reqVO.getStorageLocation())
                .eqIfPresent(GoodsInOutDO::getFeatures, reqVO.getFeatures())
                .eqIfPresent(GoodsInOutDO::getRemark, reqVO.getRemark())
                .eqIfPresent(GoodsInOutDO::getStatus, reqVO.getStatus())
                .eqIfPresent(GoodsInOutDO::getTakeoutQuantity, reqVO.getTakeoutQuantity())
                .eqIfPresent(GoodsInOutDO::getTakeoutReason, reqVO.getTakeoutReason())
                .eqIfPresent(GoodsInOutDO::getTakeoutMethod, reqVO.getTakeoutMethod())
                .eqIfPresent(GoodsInOutDO::getOperatorSfzh, reqVO.getOperatorSfzh())
                .eqIfPresent(GoodsInOutDO::getOperatorXm, reqVO.getOperatorXm())
                .betweenIfPresent(GoodsInOutDO::getOperatorTime, reqVO.getOperatorTime())
                .orderByDesc(GoodsInOutDO::getAddTime));
    }


}
