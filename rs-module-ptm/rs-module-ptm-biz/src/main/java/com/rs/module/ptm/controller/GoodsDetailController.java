package com.rs.module.ptm.controller;

import com.rs.module.ptm.domain.entity.GoodsDetailDO;
import com.rs.module.ptm.domain.vo.*;
import com.rs.module.ptm.service.GoodsDetailService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;


@Api(tags = "附物管理-附物明细")
@RestController
@RequestMapping("/ptm/personal/goodsDetail")
@Validated
public class GoodsDetailController {

    @Resource
    private GoodsDetailService goodsDetailService;

    @GetMapping("/getDqwGoodsDetailList")
    @ApiOperation(value = "根据物品登记表id获得待取物列表")
    public CommonResult<List<GoodsDetailRespVO>> getDqwGoodsDetailList(@RequestParam("goodsId") String goodsId) {
        return success(goodsDetailService.getDqwGoodsDetailList(goodsId));
    }

    @GetMapping("/wpInfoByGoodsId")
    @ApiOperation(value = "根据物品登记表id获取物品")
    @ApiImplicitParam(name = "goodsId", value = "goodsId")
    public CommonResult<List<GoodsDetailRespVO>> wpInfoByGoodsId(@RequestParam("goodsId") String goodsId) {
        return success(goodsDetailService.wpInfoByGoodsId(goodsId));
    }
}
