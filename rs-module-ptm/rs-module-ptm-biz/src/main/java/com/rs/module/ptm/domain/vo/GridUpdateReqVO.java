package com.rs.module.ptm.domain.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@ApiModel(description = "柜子状态设置")
@Data
@EqualsAndHashCode(callSuper = true)
public class GridUpdateReqVO extends BaseVO{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("格子状态：1正常  3-故障")
    @NotNull(message = "格子状态：1正常  3-故障 不能为空")
    private Integer status;


}
