package com.rs.module.ptm.domain.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 附物管理-附物取出登记明细列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsOutDetailListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("附物取出ID")
    private String personalGoodsOutId;

    @ApiModelProperty("物品编号")
    private String wpbh;

    @ApiModelProperty("取出数量")
    private Integer takeoutQuantity;

    @ApiModelProperty("备注")
    private String remark;

}
