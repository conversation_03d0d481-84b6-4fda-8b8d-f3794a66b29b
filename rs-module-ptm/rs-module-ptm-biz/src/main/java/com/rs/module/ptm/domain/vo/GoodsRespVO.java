package com.rs.module.ptm.domain.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 附物管理-附物 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WPDJBCFZT")
    private String status;
    @ApiModelProperty("登记经办人")
    private String operatorSfzh;
    @ApiModelProperty("登记经办人姓名")
    private String operatorXm;
    @ApiModelProperty("登记经办时间")
    private Date operatorTime;

    @ApiModelProperty("存放人身份证号")
    private String storageSfzh;
    @ApiModelProperty("存放人姓名")
    private String storageXm;
    @ApiModelProperty("存放时间")
    private Date storageTime;

    @ApiModelProperty("物品明细")
    private List<GoodsDetailRespVO> goodsDetailRespVOList;
}
