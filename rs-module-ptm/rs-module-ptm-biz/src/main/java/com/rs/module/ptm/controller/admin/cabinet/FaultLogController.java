package com.rs.module.ptm.controller.admin.cabinet;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.ptm.controller.admin.cabinet.vo.*;
import com.rs.module.ptm.entity.cabinet.FaultLogDO;
import com.rs.module.ptm.service.cabinet.FaultLogService;

@Api(tags = "附物管理-储物柜子故障日志")
@RestController
@RequestMapping("/ptm/cabinet/faultLog")
@Validated
public class FaultLogController {

    @Resource
    private FaultLogService faultLogService;

    @PostMapping("/create")
    @ApiOperation(value = "创建附物管理-储物柜子故障日志")
    public CommonResult<String> createFaultLog(@Valid @RequestBody FaultLogSaveReqVO createReqVO) {
        return success(faultLogService.createFaultLog(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新附物管理-储物柜子故障日志")
    public CommonResult<Boolean> updateFaultLog(@Valid @RequestBody FaultLogSaveReqVO updateReqVO) {
        faultLogService.updateFaultLog(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除附物管理-储物柜子故障日志")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteFaultLog(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           faultLogService.deleteFaultLog(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得附物管理-储物柜子故障日志")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<FaultLogRespVO> getFaultLog(@RequestParam("id") String id) {
        FaultLogDO faultLog = faultLogService.getFaultLog(id);
        return success(BeanUtils.toBean(faultLog, FaultLogRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得附物管理-储物柜子故障日志分页")
    public CommonResult<PageResult<FaultLogRespVO>> getFaultLogPage(@Valid @RequestBody FaultLogPageReqVO pageReqVO) {
        PageResult<FaultLogDO> pageResult = faultLogService.getFaultLogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, FaultLogRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得附物管理-储物柜子故障日志列表")
    public CommonResult<List<FaultLogRespVO>> getFaultLogList(@Valid @RequestBody FaultLogListReqVO listReqVO) {
        List<FaultLogDO> list = faultLogService.getFaultLogList(listReqVO);
        return success(BeanUtils.toBean(list, FaultLogRespVO.class));
    }
}
