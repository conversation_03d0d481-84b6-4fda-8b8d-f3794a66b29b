package com.rs.module.ptm.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 附物管理-储物柜子信息 DO
 *
 * <AUTHOR>
 */
@TableName("ptm_storage_cabinet")
@KeySequence("ptm_storage_cabinet_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ptm_storage_cabinet")
public class CabinetDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 柜子名称
     */
    private String name;
    /**
     * 柜子编号，如A-01、B-12（区域+序号）
     */
    private String code;
    /**
     * 柜子类型编号
     */
    private String typeCode;
    /**
     * 柜子实际位置，如1楼储物间A区
     */
    private String cabinetLocation;
    /**
     * 所属区域ID
     */
    private String areaId;
    /**
     * 所属区域名称
     */
    private String areaName;
    /**
     * 柜子IP
     */
    private String ip;
    /**
     * 柜子端口
     */
    private Integer port;
    /**
     * 柜子mac地址
     */
    private String mac;
    /**
     * 柜子总格子数，可动态配置（如20格、30格）
     */
    private Integer totalGrids;
    /**
     * 柜子状态：1-可用/2-维修中/3-停用（枚举值）
     */
    private Integer status;
    /**
     * 是否主柜
     */
    private Integer isMain;
    /**
     * 主柜子ID
     */
    private String mainCabinetId;
    /**
     * 柜子排序编号
     */
    private Integer cabinetSort;

    @ApiModelProperty("版本号")
    private String copyright;

    @ApiModelProperty("开柜方式 1刷脸，2账号 3手势： 字典：ZD_FWGJ_KGFS")
    private String cabinetOpenMethod;

    @ApiModelProperty("操作时间-秒")
    private Integer operationTime;

    @ApiModelProperty("相机设置 旋转角度-秒")
    private Integer cameraSetting;

    @ApiModelProperty("版本号")
    private String versionNumber;

    //@TableField(value = "ORG_NAME", fill = FieldFill.INSERT)
    private String orgName;

}
