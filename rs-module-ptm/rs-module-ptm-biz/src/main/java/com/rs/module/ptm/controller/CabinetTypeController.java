package com.rs.module.ptm.controller;

import com.bsp.common.util.SettingUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ptm.domain.entity.CabinetTypeDO;
import com.rs.module.ptm.domain.vo.CabinetTypeListReqVO;
import com.rs.module.ptm.domain.vo.CabinetTypePageReqVO;
import com.rs.module.ptm.domain.vo.CabinetTypeRespVO;
import com.rs.module.ptm.domain.vo.CabinetTypeSaveReqVO;
import com.rs.module.ptm.service.CabinetTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Locale;

import static com.rs.framework.common.pojo.CommonResult.success;


@Api(tags = "附物管理-储物柜子类型信息")
@RestController
@RequestMapping("/ptm/storage/cabinetType")
@Validated
public class CabinetTypeController {

    @Resource
    private CabinetTypeService cabinetTypeService;

    @PostMapping("/create")
    @ApiOperation(value = "创建附物管理-储物柜子类型信息")
    public CommonResult<String> createCabinetType(@Valid @RequestBody CabinetTypeSaveReqVO createReqVO) {
        return success(cabinetTypeService.createCabinetType(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新附物管理-储物柜子类型信息")
    public CommonResult<Boolean> updateCabinetType(@Valid @RequestBody CabinetTypeSaveReqVO updateReqVO) {
        cabinetTypeService.updateCabinetType(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除附物管理-储物柜子类型信息")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteCabinetType(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           cabinetTypeService.deleteCabinetType(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得附物管理-储物柜子类型信息")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<CabinetTypeRespVO> getCabinetType(@RequestParam("id") String id) {
        CabinetTypeDO cabinetType = cabinetTypeService.getCabinetType(id);
        return success(BeanUtils.toBean(cabinetType, CabinetTypeRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得附物管理-储物柜子类型信息分页")
    public CommonResult<PageResult<CabinetTypeRespVO>> getCabinetTypePage(@Valid @RequestBody CabinetTypePageReqVO pageReqVO) {
        PageResult<CabinetTypeDO> pageResult = cabinetTypeService.getCabinetTypePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CabinetTypeRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得附物管理-储物柜子类型信息列表")
    public CommonResult<List<CabinetTypeRespVO>> getCabinetTypeList(@Valid @RequestBody CabinetTypeListReqVO listReqVO) {
        List<CabinetTypeDO> list = cabinetTypeService.getCabinetTypeList(listReqVO);
        return success(BeanUtils.toBean(list, CabinetTypeRespVO.class));
    }

    @GetMapping("/getImgInfoByType")
    @ApiOperation(value = "获得附物管理-根据型号获取图片逗号分割")
    @ApiImplicitParam(name = "code", value = "物品柜型号")
    public CommonResult<String> getImgInfoByType(@RequestParam("code") String code) {
        return success(SettingUtil.getParamValue("FWGL-GZ-" + code.toUpperCase(Locale.ROOT), "ptm"));
    }

}
