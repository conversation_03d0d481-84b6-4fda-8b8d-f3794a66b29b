package com.rs.module.ptm.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ptm.domain.entity.CabinetTypeDO;
import com.rs.module.ptm.domain.vo.CabinetTypeListReqVO;
import com.rs.module.ptm.domain.vo.CabinetTypePageReqVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 附物管理-储物柜子类型信息 Dao
 * <AUTHOR>
 */
@Mapper
public interface CabinetTypeDao extends IBaseDao<CabinetTypeDO> {

    default PageResult<CabinetTypeDO> selectPage(CabinetTypePageReqVO reqVO) {
        Page<CabinetTypeDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<CabinetTypeDO> wrapper = new LambdaQueryWrapperX<CabinetTypeDO>().eqIfPresent(CabinetTypeDO::getTypeCode, reqVO.getTypeCode()).likeIfPresent(CabinetTypeDO::getTypeName, reqVO.getTypeName()).eqIfPresent(CabinetTypeDO::getModel, reqVO.getModel()).eqIfPresent(CabinetTypeDO::getSupplier, reqVO.getSupplier()).eqIfPresent(CabinetTypeDO::getIsMain, reqVO.getIsMain()).eqIfPresent(CabinetTypeDO::getGridPrefixRule, reqVO.getGridPrefixRule()).eqIfPresent(CabinetTypeDO::getDefaultGridCount, reqVO.getDefaultGridCount()).eqIfPresent(CabinetTypeDO::getMaxGridCount, reqVO.getMaxGridCount()).eqIfPresent(CabinetTypeDO::getRemark, reqVO.getRemark()).eqIfPresent(CabinetTypeDO::getLayoutConfig, reqVO.getLayoutConfig()).eqIfPresent(CabinetTypeDO::getLayoutRow, reqVO.getLayoutRow()).eqIfPresent(CabinetTypeDO::getLayoutColumn, reqVO.getLayoutColumn());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(CabinetTypeDO::getAddTime);
        }
        Page<CabinetTypeDO> cabinetTypePage = selectPage(page, wrapper);
        return new PageResult<>(cabinetTypePage.getRecords(), cabinetTypePage.getTotal());
    }

    default List<CabinetTypeDO> selectList(CabinetTypeListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CabinetTypeDO>().eqIfPresent(CabinetTypeDO::getTypeCode, reqVO.getTypeCode()).likeIfPresent(CabinetTypeDO::getTypeName, reqVO.getTypeName()).eqIfPresent(CabinetTypeDO::getModel, reqVO.getModel()).eqIfPresent(CabinetTypeDO::getSupplier, reqVO.getSupplier()).eqIfPresent(CabinetTypeDO::getIsMain, reqVO.getIsMain()).eqIfPresent(CabinetTypeDO::getGridPrefixRule, reqVO.getGridPrefixRule()).eqIfPresent(CabinetTypeDO::getDefaultGridCount, reqVO.getDefaultGridCount()).eqIfPresent(CabinetTypeDO::getMaxGridCount, reqVO.getMaxGridCount()).eqIfPresent(CabinetTypeDO::getRemark, reqVO.getRemark()).eqIfPresent(CabinetTypeDO::getLayoutConfig, reqVO.getLayoutConfig()).eqIfPresent(CabinetTypeDO::getLayoutRow, reqVO.getLayoutRow()).eqIfPresent(CabinetTypeDO::getLayoutColumn, reqVO.getLayoutColumn()).orderByDesc(CabinetTypeDO::getAddTime));
    }

}
