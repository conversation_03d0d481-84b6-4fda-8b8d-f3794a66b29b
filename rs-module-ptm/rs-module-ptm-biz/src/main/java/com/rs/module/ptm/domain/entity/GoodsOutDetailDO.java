package com.rs.module.ptm.domain.entity;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 附物管理-附物取出登记明细 DO
 *
 * <AUTHOR>
 */
@TableName("ptm_personal_goods_out_detail")
@KeySequence("ptm_personal_goods_out_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ptm_personal_goods_out_detail")
public class GoodsOutDetailDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 附物取出ID
     */
    private String personalGoodsOutId;
    /**
     * 物品编号
     */
    private String wpbh;
    /**
     * 取出数量
     */
    private Integer takeoutQuantity;
    /**
     * 备注
     */
    private String remark;

//    /**
//     * status
//     */
//    private String status;



}
