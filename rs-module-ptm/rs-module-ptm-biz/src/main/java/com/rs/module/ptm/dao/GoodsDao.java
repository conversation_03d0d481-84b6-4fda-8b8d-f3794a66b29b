package com.rs.module.ptm.dao;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ptm.domain.entity.GoodsDO;
import com.rs.module.ptm.domain.vo.GoodsListReqVO;
import com.rs.module.ptm.domain.vo.GoodsPageReqVO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 附物管理-附物 Dao
*
* <AUTHOR>
*/
@Mapper
public interface GoodsDao extends IBaseDao<GoodsDO> {


    default PageResult<GoodsDO> selectPage(GoodsPageReqVO reqVO) {
        Page<GoodsDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<GoodsDO> wrapper = new LambdaQueryWrapperX<GoodsDO>()
            .eqIfPresent(GoodsDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(GoodsDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(GoodsDO::getStatus, reqVO.getStatus())
            .eqIfPresent(GoodsDO::getOperatorSfzh, reqVO.getOperatorSfzh())
            .eqIfPresent(GoodsDO::getOperatorXm, reqVO.getOperatorXm())
            .betweenIfPresent(GoodsDO::getOperatorTime, reqVO.getOperatorTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(GoodsDO::getAddTime);
        }
        Page<GoodsDO> goodsPage = selectPage(page, wrapper);
        return new PageResult<>(goodsPage.getRecords(), goodsPage.getTotal());
    }
    default List<GoodsDO> selectList(GoodsListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<GoodsDO>()
            .eqIfPresent(GoodsDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(GoodsDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(GoodsDO::getStatus, reqVO.getStatus())
            .eqIfPresent(GoodsDO::getOperatorSfzh, reqVO.getOperatorSfzh())
            .eqIfPresent(GoodsDO::getOperatorXm, reqVO.getOperatorXm())
            .betweenIfPresent(GoodsDO::getOperatorTime, reqVO.getOperatorTime())
        .orderByDesc(GoodsDO::getAddTime));    }


    }
