package com.rs.adapter.bsp.api;

import com.alibaba.fastjson.JSONObject;
import com.rs.adapter.bsp.api.dto.bpm.ProcessCmdDTO;
import com.rs.adapter.bsp.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC服务-BPM API")
public interface BpmApi {

    String PREFIX = ApiConstants.PREFIX + "/bpm";

    @PostMapping(PREFIX + "/approve/getStartApproveUser")
    @Operation(summary = "启动时获取审批用户")
    @Parameters({
            @Parameter(name = "defKey", description = "流程定义key", required = true),
            @Parameter(name = "startUserId", description = "流程启动用户ID", required = true),
            @Parameter(name = "formProcessVar", description = "", required = true)
    })
    public JSONObject getStartApproveUser(@RequestParam("defKey") String defKey,
                                          @RequestParam("startUserId") String startUserId,
                                          @RequestParam("formProcessVar") String formProcessVar);


    @PostMapping(PREFIX + "/approve/getApproveUser")
    @Operation(summary = "获取流程审批用户")
    @Parameters({
            @Parameter(name = "actInstId", description = "ACT流程实例", required = true),
            @Parameter(name = "userId", description = "执行用户ID", required = true)
    })
    public JSONObject getApproveUser(@RequestParam("actInstId") String actInstId,
                                                   @RequestParam("userId") String userId,
                                     @RequestParam("formProcessVar") String formProcessVar);


    @PostMapping(PREFIX + "/approve/startProcess")
    @Operation(summary = "启动流程审批")
    @Parameters({
            @Parameter(name = "processCmd", description = "流程执行实体", required = true)
    })
    public JSONObject startProcess(@RequestBody ProcessCmdDTO processCmd);


    @PostMapping(PREFIX + "/approve/approvalProcess")
    @Operation(summary = "流程审批")
    @Parameters({
            @Parameter(name = "processCmd", description = "流程执行实体", required = true)
    })
    public JSONObject approvalProcess(@RequestBody ProcessCmdDTO processCmd);


    @PostMapping(PREFIX + "/approve/checkIsApproveAuthority")
    @Operation(summary = "判断用户是否具有权限审批")
    @Parameters({
            @Parameter(name = "taskId", description = "流程任务ID", required = true),
            @Parameter(name = "userId", description = "执行用户身份证号", required = true)
    })
    public Boolean checkIsApproveAuthority(@RequestParam("taskId") String taskId,
                                                       @RequestParam("userId") String userId);


    @PostMapping(PREFIX + "/approve/approveTrack")
    @Operation(summary = "获取流程轨迹")
    @Parameters({
            @Parameter(name = "actInstId", description = "ACT流程实例", required = true),
    })
    public JSONObject approveTrack(@RequestParam("actInstId") String actInstId);


    @PostMapping(PREFIX + "/approve/isFinishProcinst")
    @Operation(summary = "判断流程是否结束")
    @Parameters({
            @Parameter(name = "actInstId", description = "ACT流程实例", required = true),
    })
    public Boolean isFinishProcinst(@RequestParam("actInstId") String actInstId);

    @PostMapping(PREFIX + "/approve/taskIdentityLinks")
    @Operation(summary = "获取待审批人数据")
    @Parameters({
            @Parameter(name = "actInstId", description = "ACT流程实例", required = true),
    })
    public JSONObject taskIdentityLinks(@RequestParam("actInstId") String actInstId, String param);
}
