package com.rs.adapter.bsp.api.dto;

import java.util.ArrayList;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "RPC服务-应用角色分类Response DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RoleCatRespDTO extends BaseDTO{

	@Schema(description = "主键")
    String id;
	
	@Schema(description = "分类名称")
    String name;
	
	@Schema(description = "排序Id")
    String orderId;
	
	@Schema(description = "角色集合")
    List<RoleRespDTO> rolelist = new ArrayList<>();

    public List<RoleRespDTO> getRolelist() {
        return rolelist;
    }

    public void setRolelist(List<RoleRespDTO> rolelist) {
        this.rolelist = rolelist;
    }
}
