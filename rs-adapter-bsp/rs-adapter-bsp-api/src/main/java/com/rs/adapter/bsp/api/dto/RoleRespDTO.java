package com.rs.adapter.bsp.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "RPC服务-角色Response DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RoleRespDTO extends BaseDTO {
	
	@Schema(description = "主键")
	String id;
	
	@Schema(description = "系统标识")
	String systemMark;
	
	@Schema(description = "分类Id")
	String catId;
	
	@Schema(description = "分类名称")
	String catName;
	
	@Schema(description = "角色名称")
	String name;
	
	@Schema(description = "角色代码")
	String code;
	
	@Schema(description = "是否可级联分配(0否1是)")
	String isAssign;
	
	@Schema(description = "角色级别")
	String rank;
	
	@Schema(description = "是否管理员")
	String isAdmin;
	
	@Schema(description = "排序Id")
	String orderId;
}
