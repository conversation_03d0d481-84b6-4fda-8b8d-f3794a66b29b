package com.rs.adapter.bsp.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "RPC服务-应用Response DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppRespDTO extends BaseDTO{
	
	@Schema(description = "主键")
    String id;
    
	@Schema(description = "应用名称")
    String name;
    
	@Schema(description = "应用代码")
    String code;
    
	@Schema(description = "主页")
    String defaultPage;
    
	@Schema(description = "是否禁用(0否1是)")
    String isDisabled;
    
	@Schema(description = "排序Id")
    Integer orderId;    
}
