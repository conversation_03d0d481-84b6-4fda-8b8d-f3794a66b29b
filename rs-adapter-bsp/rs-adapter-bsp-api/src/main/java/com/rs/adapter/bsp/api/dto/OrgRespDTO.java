package com.rs.adapter.bsp.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "RPC服务-机构Response DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrgRespDTO extends BaseDTO{
	
	@Schema(description = "主键")
    String id;
	
	@Schema(description = "机构名称")
    String name;
	
	@Schema(description = "机构编码")
    String code;
	
	@Schema(description = "机构简称")
    String sname;
	
	@Schema(description = "机构全称")
    String fname;
	
	@Schema(description = "机构拼音码")
    String scode;
	
	@Schema(description = "机构简拼码")
    String jpcode;
	
	@Schema(description = "机构其它名称")
    String otherSname;
	
	@Schema(description = "机构地市Id")
    String cityId;
	
	@Schema(description = "机构地市名称")
    String cityName;
	
	@Schema(description = "机构区域Id")
    String regionId;
	
	@Schema(description = "机构区域名称")
    String regionName;
	
	@Schema(description = "父机构Id")
    String parentId;
	
	@Schema(description = "父机构名称")
    String parentName;
	
	@Schema(description = "是否正式机构(0否1是)")
    String isFormal;
	
	@Schema(description = "是否停用(0否1是)")
    String isDisabled;
	
	@Schema(description = "排序Id")
    String orderId;

    @Schema(description = "机构类型")
    String orgType;

    @Schema(description = "电子邮件")
    String email;

    @Schema(description = "地址")
    String address;

    @Schema(description = "办公电话")
    String officeTel;

    @Schema(description = "单位介绍")
    String intro;
}
