package com.rs.adapter.bsp.api;

import java.util.List;
import java.util.Set;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.rs.adapter.bsp.api.dto.UserRespDTO;
import com.rs.adapter.bsp.enums.ApiConstants;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC服务-用户")
public interface UserApi {

	String PREFIX = ApiConstants.PREFIX + "/user";
	
	@GetMapping(PREFIX + "/getUserPhotoByIdCard")
	@Operation(summary = "获取用户照片信息")
	@Parameter(name = "idCards", description = "身份证号码", example = "[540123200012304104]", required = true)
	public List<UserRespDTO> getUserPhotoByIdCard(@RequestParam("idCards") Set<String> idCards);

	@GetMapping(PREFIX + "/getUserByOrgAndRole")
	@Operation(summary = "根据机构代码与角色编号查询用户信息")
	@Parameters({
		@Parameter(name = "orgCode", description = "机构代码", example = "789111000", required = true),
		@Parameter(name = "roleCode", description = "角色编号", example = "900001", required = true)
	})
	public List<UserRespDTO> getUserByOrgAndRole(@RequestParam("orgCode") String orgCode,
			@RequestParam("roleCode") String roleCode);

	@GetMapping(PREFIX + "/getUserByOrgAndPost")
	@Operation(summary = "根据机构代码与岗位字典编码查询用户信息")
	@Parameters({
			@Parameter(name = "orgCode", description = "机构代码", example = "789111000", required = true),
			@Parameter(name = "postCode", description = "岗位字典 ZD_POST 编码(01 窗口岗,02 巡控岗,03 管教岗,04 医务岗,05 综合岗,06 所领导,07 押解岗,08 后勤岗,09 收押岗,10 看守岗,11 教育康复岗,12 收戒岗,13 医疗岗,14 康复岗)", example = "", required = true)
	})
	public List<UserRespDTO> getUserByOrgAndPost(@RequestParam("orgCode") String orgCode, @RequestParam("postCode") String postCode);

	@GetMapping(PREFIX + "/getUserByNowUserOrgAndPost")
	@Operation(summary = "查询当前用户机构代码指定岗位字典编码用户信息")
	@Parameters({
			@Parameter(name = "postCode", description = "岗位字典 ZD_POST 编码(01 窗口岗,02 巡控岗,03 管教岗,04 医务岗,05 综合岗,06 所领导,07 押解岗,08 后勤岗,09 收押岗,10 看守岗,11 教育康复岗,12 收戒岗,13 医疗岗,14 康复岗)", example = "", required = true)
	})
	public List<UserRespDTO> getUserByNowUserOrgAndPost(@RequestParam("postCode") String postCode);
}
