package com.rs.adapter.bsp.api.dto;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString(callSuper = true)
public class OpsResFieldDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;
    private String resId;
    private String fieldName;
    private String fieldCname;
    private String fieldType;
    private Integer jdbcType;
    private String fieldLength;
    private String dataPrecision;
    private String dataScale;
    private String isPk;
    private String isNullable;
    private String defaultValue;
    private String dicName;
    private Integer orderId;
    private String remark;

}
