package com.rs.adapter.bsp.enums;

public enum BspApproceBackTypeEnum {

    LAST(Short.valueOf((short) 1), "退回上一步"),
    START(Short.valueOf((short) 2), "退回发起人"),
    ASSIGN(Short.valueOf((short) 3), "指定退回节点"),
    RECOVER(Short.valueOf((short) 4), "任意节点");

    BspApproceBackTypeEnum(short code, String name) {
        this.code = code;
        this.name = name;
    }

    private short code;
    private String name;

    public short getCode() {
        return code;
    }
    public String getName() {
        return name;
    }

    public static BspApproceBackTypeEnum getByCode(short code) {
        for (BspApproceBackTypeEnum bspApproceStatusEnum : BspApproceBackTypeEnum.values()) {
            if (bspApproceStatusEnum.getCode() == code) {
                return bspApproceStatusEnum;
            }
        }
        return null;
    }

    public static String getNameByCode(short code) {
        for (BspApproceBackTypeEnum bspApproceStatusEnum : BspApproceBackTypeEnum.values()) {
            if (bspApproceStatusEnum.getCode() == code) {
                return bspApproceStatusEnum.getName();
            }
        }
        return null;
    }

}
