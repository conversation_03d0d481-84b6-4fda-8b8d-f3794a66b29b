package com.rs.adapter.bsp.api.dto;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "RPC服务-行政区划Response DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AreaRespDTO extends BaseDTO{
	
	@Schema(description = "主键")
    String id;
	
	@Schema(description = "名称")
    String name;
	
	@Schema(description = "区域类型(ZD_REG_TYPE)")
    String regType;
	
	@Schema(description = "拼音码")
    String scode;
	
	@Schema(description = "简拼码")
    String jpcode;
	
	@Schema(description = "简称")
    String sname;
	
	@Schema(description = "父Id")
    String parentId;
	
	@Schema(description = "父名称")
    String parentName;
	
	@Schema(description = "排序Id")
    String orderId;
	
	@Schema(description = "子行政区划")
    List<AreaRespDTO> children;
}
