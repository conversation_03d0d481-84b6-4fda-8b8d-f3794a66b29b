package com.rs.adapter.bsp.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "RPC服务-应用功能Response DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FunctionRespDTO extends BaseDTO{
	
	@Schema(description = "主键")
    String id;
	
	@Schema(description = "应用Id")
    String appId;
	
	@Schema(description = "功能名称")
    String name;
	
	@Schema(description = "功能标识")
    String mark;
	
	@Schema(description = "URL地址")
    String url;
	
	@Schema(description = "功能类型(0:普通|1:配置型)")
    String funcType;
	
	@Schema(description = "排序Id")
    String orderId;
	
	@Schema(description = "是否禁用(0否1是)")
    boolean disabled;
	
	@Schema(description = "是否访问控制(0否1是)")
    boolean accessControl;
}
