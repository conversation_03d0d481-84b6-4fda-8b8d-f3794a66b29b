package com.rs.adapter.bsp.api.dto;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "RPC服务-用户Response DTO")
@Data
public class UserRespDTO {

	@Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;
	
	@Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
	private String name;
	
	@Schema(description = "身份证号码", requiredMode = Schema.RequiredMode.REQUIRED, example = "540123200012304104")
    private String idCard;
	
	@Schema(description = "照片(base64编码)", requiredMode = Schema.RequiredMode.REQUIRED)
    private String photo;
	
	@ApiModelProperty("机构编码")
	private String orgCode;
	
	@ApiModelProperty("机构名称")
	private String orgName;
}
