package com.rs.adapter.bsp.api;

import java.util.List;
import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.rs.adapter.bsp.enums.ApiConstants;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC服务-BSP SDK")
public interface BspSdk {

	String PREFIX = ApiConstants.PREFIX + "/sdk";
	
	@PostMapping(PREFIX + "/getParamValue")
	@Operation(summary = "获取系统参数值")
	@Parameters({
		@Parameter(name = "systemMark", description = "系统标识", required = true),
		@Parameter(name = "paramName", description = "参数名称", required = true)
	})
	public String getParamValue(@RequestParam("systemMark") String systemMark,
			@RequestParam("paramName") String paramName);
	
	@PostMapping(PREFIX + "/translate")
	@Operation(summary = "字典翻译")
	@Parameters({
		@Parameter(name = "systemMark", description = "系统标识", required = true),
		@Parameter(name = "dicName", description = "字典名称", required = true),
		@Parameter(name = "codes", description = "字典代码，多个以逗号分隔", required = true)
	})
	public List<String> translate(@RequestParam("systemMark") String systemMark,
			@RequestParam("dicName") String dicName, @RequestParam("codes") String codes);
	
	@PostMapping(PREFIX + "/translateList")
	@Operation(summary = "字典翻译")
	@Parameters({
		@Parameter(name = "systemMark", description = "系统标识", required = true),
		@Parameter(name = "dicName", description = "字典名称", required = true),
		@Parameter(name = "codeList", description = "字典代码集合", required = true)
	})
	public List<String> translate(@RequestParam("systemMark") String systemMark,
			@RequestParam("dicName") String dicName, @RequestParam("codeList") List<String> codeList);
	
	@PostMapping(PREFIX + "/translateMapList")
	@Operation(summary = "字典翻译")
	@Parameters({
		@Parameter(name = "systemMark", description = "系统标识", required = true),
		@Parameter(name = "listStr", description = "字典数据，多个以逗号分隔"),
		@Parameter(name = "dicNames", description = "字典名称，多个以逗号分隔", required = true),
		@Parameter(name = "dicFields", description = "字典字段，多个以逗号分隔", required = true)
	})
	public List<Map<String, Object>> translateMapList(@RequestParam("systemMark") String systemMark,
			@RequestParam("listStr") String listStr, @RequestParam("dicNames") String dicNames,
			@RequestParam("dicFields") String dicFields);
	
	@PostMapping(PREFIX + "/translateMapList/longData")
	@Operation(summary = "字典翻译")
	@Parameters({
		@Parameter(name = "queryParam", description = "查询参数", required = true)
	})
	public List<Map<String, Object>> translateMapListLongData(@RequestBody MultiValueMap<String,String> queryParam);
	
	@PostMapping(PREFIX + "/translateEntityList")
	@Operation(summary = "字典翻译")
	@Parameters({
		@Parameter(name = "systemMark", description = "系统标识", required = true),
		@Parameter(name = "listStr", description = "字典数据，多个以逗号分隔"),
		@Parameter(name = "dicNames", description = "字典名称，多个以逗号分隔", required = true),
		@Parameter(name = "dicFields", description = "字典字段，多个以逗号分隔", required = true)
	})
	public <T> List<Map<String, Object>> translateEntityList(@RequestParam("systemMark") String systemMark,
			@RequestParam("listStr") String listStr, @RequestParam("dicNames") String dicNames,
			@RequestParam("dicFields") String dicFields);
	
	@PostMapping(PREFIX + "/getDic")
	@Operation(summary = "获取字典数据")
	@Parameters({
		@Parameter(name = "systemMark", description = "系统标识", required = true),
		@Parameter(name = "dicNames", description = "字典名称，多个以逗号分隔", required = true)
	})
	public <T> Map<Object, Object> getDic(@RequestParam("systemMark") String systemMark,
			@RequestParam("dicNames") String dicNames);
}
