package com.rs.adapter.bsp.api;

import java.util.List;
import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.alibaba.fastjson.JSONObject;
import com.rs.adapter.bsp.api.dto.AppRespDTO;
import com.rs.adapter.bsp.api.dto.AreaRespDTO;
import com.rs.adapter.bsp.api.dto.FuncOperRespDTO;
import com.rs.adapter.bsp.api.dto.FunctionRespDTO;
import com.rs.adapter.bsp.api.dto.OpsDatasourceDTO;
import com.rs.adapter.bsp.api.dto.OpsResFieldDTO;
import com.rs.adapter.bsp.api.dto.OpsResourceDTO;
import com.rs.adapter.bsp.api.dto.OrgRespDTO;
import com.rs.adapter.bsp.api.dto.OrgUserRespDTO;
import com.rs.adapter.bsp.api.dto.RoleCatRespDTO;
import com.rs.adapter.bsp.api.dto.RoleRespDTO;
import com.rs.adapter.bsp.api.dto.UserRespDTO;
import com.rs.adapter.bsp.enums.ApiConstants;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC服务-BSP API")
public interface BspApi {

	String PREFIX = ApiConstants.PREFIX + "/api";

	@PostMapping(PREFIX + "/oauth/getTokenByClient")
	@Operation(summary = "使用客户端模式获取Token")
	@Parameters({
		@Parameter(name = "clientId", description = "第三方客户端唯一凭证(通过申请获取)", required = true),
		@Parameter(name = "clientSecret", description = "客户端秘钥(与凭证一起申请获取)", required = true)
	})
	public Map<String, Object> getTokenByClient(@RequestParam("clientId") String clientId,
			@RequestParam("clientSecret") String clientSecret);

	@PostMapping(PREFIX + "/oauth/getTokenByPassword")
	@Operation(summary = "使用密码模式获取Token")
	@Parameters({
		@Parameter(name = "userName", description = "用户名，使用CryptoUtils.encrypt()加密", required = true),
		@Parameter(name = "password", description = "密码(md5摘要)", required = true),
		@Parameter(name = "appMark", description = "应用标识", required = true)
	})
	public Map<String, Object> getTokenByPassword(@RequestParam("userName") String userName,
			@RequestParam("password") String password, @RequestParam("appMark") String appMark);

	@PostMapping(PREFIX + "/oauth/getTokenByIdCard")
	@Operation(summary = "使用身份证模式获取Token")
	@Parameters({
		@Parameter(name = "idCard", description = "身份证号，使用CryptoUtils.encrypt()加密", required = true),
		@Parameter(name = "password", description = "密码(md5摘要)", required = true),
		@Parameter(name = "appMark", description = "应用标识", required = true)
	})
	public Map<String, Object> getTokenByIdCard(@RequestParam("idCard") String idCard,
			@RequestParam("password") String password, @RequestParam("appMark") String appMark);

	@PostMapping(PREFIX + "/oauth/getTempToken")
	@Operation(summary = "获取临时Token")
	public String getTempToken();

	@PostMapping(PREFIX + "/config/app/getAllAppPageData")
	@Operation(summary = "获取全部应用分页数据")
	@Parameters({
		@Parameter(name = "pageNo", description = "当前页号，默认为1", example  = "1"),
		@Parameter(name = "pageSize", description = "每页多少条，默认为10", example = "10")
	})
	public List<AppRespDTO> getAllAppPageData(@RequestParam("pageNo") Integer pageNo,
			@RequestParam("pageSize") Integer pageSize);

	@PostMapping(PREFIX + "/config/app/getAppByCode")
	@Operation(summary = "根据应用代码获取应用")
	@Parameters({
		@Parameter(name = "code", description = "应用代码")
	})
	public AppRespDTO getAppByCode(@RequestParam("code") String code);

	@PostMapping(PREFIX + "/perm/rolecat/getAppAllCats")
	@Operation(summary = "获取应用全部角色分类")
	public List<RoleCatRespDTO> getAppAllCats();

	@PostMapping(PREFIX + "/perm/role/getCatRoles")
	@Operation(summary = "全部角色分类和角色")
	public List<RoleCatRespDTO> getCatRoles();

	@PostMapping(PREFIX + "/perm/role/getRoleByCode")
	@Operation(summary = "根据角色代码获取角色")
	@Parameters({
		@Parameter(name = "code", description = "角色代码")
	})
	public RoleRespDTO getRoleByCode(@RequestParam("code") String code);

	@PostMapping(PREFIX + "/perm/role/getRolePageData")
	@Operation(summary = "获取全部角色分页数据")
	@Parameters({
		@Parameter(name = "pageNo", description = "当前页号，默认为1", example = "1"),
		@Parameter(name = "pageSize", description = "每页多少条，默认为10", example = "10")
	})
	public List<RoleRespDTO> getRolePageData(@RequestParam("pageNo") Integer pageNo,
			@RequestParam("pageSize") Integer pageSize);

	@PostMapping(PREFIX + "/perm/perm/getRolesByUserId")
	@Operation(summary = "获取用户授权的角色")
	@Parameters({
		@Parameter(name = "userId", description = "用户Id")
	})
	public List<RoleRespDTO> getRolesByUserId(@RequestParam("userId") String userId);

	@PostMapping(PREFIX + "/perm/getUserFuncOpers")
	@Operation(summary = "获取用户可以访问的功能操作")
	@Parameters({
		@Parameter(name = "funcId", description = "功能Id"),
		@Parameter(name = "funcMark", description = "功能标识"),
		@Parameter(name = "userId", description = "用户Id")
	})
	public List<FuncOperRespDTO> getUserFuncOpers(@RequestParam("funcId") String funcId,
			@RequestParam("funcMark") String funcMark, @RequestParam("userId") String userId);

	@PostMapping(PREFIX + "/config/funcoper/getAllFuncOpers")
	@Operation(summary = "获取功能包含的全部操作")
	@Parameters({
		@Parameter(name = "funcId", description = "功能Id"),
		@Parameter(name = "funcMark", description = "功能标识")
	})
	public List<FuncOperRespDTO> getAllFuncOpers(@RequestParam("funcId") String funcId,
			@RequestParam("funcMark") String funcMark);

	@PostMapping(PREFIX + "/config/funcoper/getFuncOpers")
	@Operation(summary = "获取功能包含的可用操作")
	@Parameters({
		@Parameter(name = "funcId", description = "功能Id"),
		@Parameter(name = "funcMark", description = "功能标识")
	})
	public List<FuncOperRespDTO> getFuncOpers(@RequestParam("funcId") String funcId,
			@RequestParam("funcMark") String funcMark);

	@PostMapping(PREFIX + "/config/func/getAppAllFuncPageData")
	@Operation(summary = "获取应用注册的全部功能")
	@Parameters({
		@Parameter(name = "pageNo", description = "当前页号，默认为1", example = "1"),
		@Parameter(name = "pageSize", description = "每页多少条，默认为10", example = "10"),
		@Parameter(name = "appId", description = "应用Id"),
		@Parameter(name = "appCode", description = "应用代码")
	})
	public List<FunctionRespDTO> getAppAllFuncPageData(@RequestParam("pageNo") Integer pageNo,
			@RequestParam("pageSize") Integer pageSize, @RequestParam("appId") String appId,
			@RequestParam("appCode") String appCode);

	@PostMapping(PREFIX + "/config/func/getAppFuncPageData")
	@Operation(summary = "获取应用注册的可能功能")
	@Parameters({
		@Parameter(name = "pageNo", description = "当前页号，默认为1", example = "1"),
		@Parameter(name = "pageSize", description = "每页多少条，默认为10", example = "10"),
		@Parameter(name = "appId", description = "应用Id"),
		@Parameter(name = "appCode", description = "应用代码")
	})
	public List<FunctionRespDTO> getAppFuncPageData(@RequestParam("pageNo") Integer pageNo,
			@RequestParam("pageSize") Integer pageSize, @RequestParam("appId") String appId,
			@RequestParam("appCode") String appCode);

	@PostMapping(PREFIX + "/config/func/getFuncByMark")
	@Operation(summary = "根据功能标识获取功能")
	@Parameters({
		@Parameter(name = "mark", description = "功能标识")
	})
	public FunctionRespDTO getFuncByMark(@RequestParam("mark") String mark);

	@PostMapping(PREFIX + "/userorg/area/getAllAreas")
	@Operation(summary = "获取全部行政区划")
	public List<AreaRespDTO> getAllAreas();

	@PostMapping(PREFIX + "/userorg/area/getAreaByCode")
	@Operation(summary = "根据区划代码获取行政区划")
	@Parameters({
		@Parameter(name = "areaCode", description = "区划代码")
	})
	public AreaRespDTO getAreaByCode(@RequestParam("areaCode") String areaCode);

	@PostMapping(PREFIX + "/userorg/user/getUserNameIdCardById")
	@Operation(summary = "根据用户Id获取用户姓名和身份证号码")
	@Parameters({
		@Parameter(name = "ids", description = "用户Id，多个时以逗号分隔")
	})
	public List<UserRespDTO> getUserNameIdCardById(@RequestParam("ids") String ids);

	@PostMapping(PREFIX + "/userorg/user/getUserNameIdCardByOrgAndRole")
	@Operation(summary = "获取某机构指定角色的用户姓名和身份证号码")
	@Parameters({
		@Parameter(name = "orgId", description = "机构Id"),
		@Parameter(name = "roleId", description = "角色Id")
	})
	public List<UserRespDTO> getUserNameIdCardByOrgAndRole(@RequestParam("orgId") String orgId,
			@RequestParam("roleId") String roleId);

	@PostMapping(PREFIX + "/userorg/user/getOrgAllUserPageData")
	@Operation(summary = "获取机构全部用户分页数据")
	@Parameters({
		@Parameter(name = "pageNo", description = "当前页号，默认为1", example = "1"),
		@Parameter(name = "pageSize", description = "每页多少条，默认为10", example = "10"),
		@Parameter(name = "orgCode", description = "机构代码")
	})
	public List<OrgUserRespDTO> getOrgAllUserPageData(@RequestParam("pageNo") Integer pageNo,
			@RequestParam("pageSize") Integer pageSize, @RequestParam("orgCode") String orgCode);

	@PostMapping(PREFIX + "/userorg/user/getOrgUserPageData")
	@Operation(summary = "获取机构可用用户分页数据")
	@Parameters({
		@Parameter(name = "pageNo", description = "当前页号，默认为1", example = "1"),
		@Parameter(name = "pageSize", description = "每页多少条，默认为10", example = "10"),
		@Parameter(name = "orgCode", description = "机构代码")
	})
	public Map<String, Object> getOrgUserPageData(@RequestParam("pageNo") Integer pageNo,
			@RequestParam("pageSize") Integer pageSize, @RequestParam("orgCode") String orgCode);

	@PostMapping(PREFIX + "/userorg/user/selectUserByDivision")
	@Operation(summary = "根据区划（区域Id或机构Id）查找用户")
	@Parameters({
		@Parameter(name = "regIds", description = "区域Id或区域代码,多个以逗号分隔"),
		@Parameter(name = "orgIds", description = "机构Id或机构代码,多个以逗号分隔")
	})
	public List<OrgUserRespDTO> selectUserByDivision(@RequestParam("regIds") String regIds,
			@RequestParam("orgIds") String orgIds);

	@PostMapping(PREFIX + "/userorg/user/getUserByUserId")
	@Operation(summary = "根据用户Id获取用户")
	@Parameters({
		@Parameter(name = "userId", description = "用户Id")
	})
	public OrgUserRespDTO getUserByUserId(@RequestParam("userId") String userId);

	@PostMapping(PREFIX + "/userorg/user/getUserByLoginId")
	@Operation(summary = "根据登录帐号获取用户")
	@Parameters({
		@Parameter(name = "loginId", description = "登录账号")
	})
	public OrgUserRespDTO getUserByLoginId(@RequestParam("loginId") String loginId);

	@PostMapping(PREFIX + "/userorg/user/getUserByLoginIdPwd")
	@Operation(summary = "根据登录帐号和密码获取用户")
	@Parameters({
		@Parameter(name = "loginId", description = "登录账号"),
		@Parameter(name = "password", description = "登录密码(无需加密)")
	})
	public OrgUserRespDTO getUserByLoginIdPwd(@RequestParam("loginId") String loginId,
			@RequestParam("password") String password);

	@PostMapping(PREFIX + "/userorg/user/getUserByIdCard")
	@Operation(summary = "根据身份证号码获取用户")
	@Parameters({
		@Parameter(name = "idCard", description = "身份证号码")
	})
	public OrgUserRespDTO getUserByIdCard(@RequestParam("idCard") String idCard);

	@PostMapping(PREFIX + "/userorg/user/getUserByIdCards")
	@Operation(summary = "根据身份证号码获取用户")
	@Parameters({
		@Parameter(name = "idCards", description = "身份证号码，多个时以逗号分隔")
	})
	public List<OrgUserRespDTO> getUserByIdCards(@RequestParam("idCards") String idCards);

	@PostMapping(PREFIX + "/userorg/user/getUserNameByIdCard")
	@Operation(summary = "根据身份证号码获取用户姓名")
	@Parameters({
		@Parameter(name = "idCard", description = "身份证号码")
	})
	public List<String> getUserNameByIdCard(@RequestParam("idCard") String idCard);

	@PostMapping(PREFIX + "/userorg/user/getUserNameByIdCards")
	@Operation(summary = "根据身份证号码批量获取用户姓名")
	@Parameters({
		@Parameter(name = "idCards", description = "身份证号码，多个时以逗号分隔")
	})
	public List<String> getUserNameByIdCards(@RequestParam("idCards") String idCards);

	@PostMapping(PREFIX + "/userorg/user/getUserByOrgAndRole")
	@Operation(summary = "获取某机构指定角色的用户")
	@Parameters({
		@Parameter(name = "orgId", description = "机构Id"),
		@Parameter(name = "roleId", description = "角色Id")
	})
	public List<OrgUserRespDTO> getUserByOrgAndRole(@RequestParam("orgId") String orgId,
			@RequestParam("roleId") String roleId);

	@PostMapping(PREFIX + "/userorg/user/getUserByRegAndRole")
	@Operation(summary = "获取某区域指定角色的用户")
	@Parameters({
		@Parameter(name = "regId", description = "区域Id"),
		@Parameter(name = "roleId", description = "角色Id")
	})
	public List<OrgUserRespDTO> getUserByRegAndRole(@RequestParam("regId") String regId,
			@RequestParam("roleId") String roleId);

	@PostMapping(PREFIX + "/userorg/user/getUserByRegAndOrgName")
	@Operation(summary = "根据区域Id和机构名称查找用户")
	@Parameters({
		@Parameter(name = "regId", description = "区域Id"),
		@Parameter(name = "orgName", description = "机构名称，支持模糊查询")
	})
	public List<OrgUserRespDTO> getUserByRegAndOrgName(@RequestParam("regId") String regId,
			@RequestParam("orgName") String orgName);

	@PostMapping(PREFIX + "/userorg/org/getOrgByCode")
	@Operation(summary = "根据机构代码获取机构")
	@Parameters({
		@Parameter(name = "orgCode", description = "机构代码")
	})
	public OrgRespDTO getOrgByCode(@RequestParam("orgCode") String orgCode);

	@PostMapping(PREFIX + "/userorg/org/getAllOrgPageData")
	@Operation(summary = "获取全部机构分页数据")
	@Parameters({
		@Parameter(name = "pageNo", description = "当前页号，默认为1", example = "1"),
		@Parameter(name = "pageSize", description = "每页多少条，默认为10", example = "10")
	})
	public List<OrgRespDTO> getAllOrgPageData(@RequestParam("pageNo") Integer pageNo,
			@RequestParam("pageSize") Integer pageSize);

	@PostMapping(PREFIX + "/userorg/org/getOrgPageData")
	@Operation(summary = "获取可用机构分页数据")
	@Parameters({
		@Parameter(name = "map", description = "机构map参数(pageNo|pageSize|orgId|orgName|regId|regName|cityId|cityName)")
	})
	public List<OrgRespDTO> getOrgPageData(@RequestBody Map<String, Object> map);

	@PostMapping(PREFIX + "/userorg/org/getAllOrgByParentId")
	@Operation(summary = "获取全部子机构数据")
	@Parameters({
		@Parameter(name = "parentId", description = "父机构Id")
	})
	public List<OrgRespDTO> getAllOrgByParentId(@RequestParam("parentId") String parentId);

	@PostMapping(PREFIX + "/userorg/org/getChildOrgsByParentId")
	@Operation(summary = "获取可用子机构数据")
	@Parameters({
		@Parameter(name = "parentId", description = "父机构Id")
	})
	public List<OrgRespDTO> getChildOrgsByParentId(@RequestParam("parentId") String parentId);

	@PostMapping(PREFIX + "/userorg/org/getAllOrgs")
	@Operation(summary = "获取全部机构数据")
	public List<OrgRespDTO> getAllOrgs() ;

	@PostMapping(PREFIX + "/userorg/org/getOrgDataByParams")
	@Operation(summary = "根据参数获取机构数据")
	@Parameters({
		@Parameter(name = "map", description = "机构Map参数(name|code|regId|regName|cityId|cityName|isDisabled|regIds|cityIds)")
	})
	public List<OrgRespDTO> getOrgDataByParams(@RequestBody Map<String, Object> map);

	@PostMapping(PREFIX + "/userorg/org/getRegOrgAllPageData")
	@Operation(summary = "获取区域全部机构分页数据")
	@Parameters({
		@Parameter(name = "pageNo", description = "当前页号，默认为1", example = "1"),
		@Parameter(name = "pageSize", description = "每页多少条，默认为10", example = "10"),
		@Parameter(name = "regCode", description = "区域代码", example = "10")
	})
	public List<OrgRespDTO> getRegOrgAllPageData(@RequestParam("pageNo") Integer pageNo,
			@RequestParam("pageSize") Integer pageSize, @RequestParam("regCode") String regCode);

	@PostMapping(PREFIX + "/com/fillrule/execute")
	@Operation(summary = "根据规则代码获取系统编号")
	@Parameters({
		@Parameter(name = "ruleCode", description = "规则代码"),
		@Parameter(name = "formData", description = "表单数据")
	})
	public String executeByRuleCode(@RequestParam("ruleCode") String ruleCode,
			@RequestParam(name = "formData", required = false) String formData);

	@PostMapping(PREFIX + "/com/form/handle/executeFmQuery")
	@Operation(summary ="根据标识执行自定义查询data返回单条")
	@Parameters({
			@Parameter(name = "paramMap", description = "自定义查询标识及其他参数")
	})
	JSONObject executeFmQuery(@RequestParam("mark") String mark,@RequestParam(name="paramMap",required = false) Map<String, Object> paramMap);

	@PostMapping(PREFIX + "/com/form/handle/executeMultiQuery")
	@Operation(summary ="根据标识执行自定义查询data返回多条")
	@Parameters({
			@Parameter(name = "paramMap", description = "自定义查询标识及其他参数")
	})
	JSONObject executeMultiQuery(@RequestParam("mark") String mark,@RequestParam(name = "paramMap",required = false) Map<String, Object> paramMap);





	@PostMapping(PREFIX + "/resource/ops/res/datasource/getDbByAppId")
	@Operation(summary ="获取全部数据源信息")
	@Parameter(name = "appId", description = "应用ID")
	List<OpsDatasourceDTO> getDbByAppId(@RequestParam("appId") String appId);


	@PostMapping(PREFIX + "/resource/ops/res/datasource/getDbById")
	@Operation(summary ="根据ID获取数据源信息")
	@Parameter(name = "dbId", description = "")
	OpsDatasourceDTO getDbById(@RequestParam("dbId") String dbId);


	@PostMapping(PREFIX + "/resource/ops/res/resource/getResourcesByDbId")
	@Operation(summary ="根据数据源ID获取下属所有资源信息")
	@Parameters({
			@Parameter(name = "dbId", description = "数据源ID"),
			@Parameter(name = "resCName", description = "资源中文名称"),
			@Parameter(name = "resType", description = "资源类型 01：数据表，02：查询脚本"),
			@Parameter(name = "pageNo", description = "起始页码,默认1"),
			@Parameter(name = "pageSize", description = "每页数量,默认10"),
	})
	List<OpsResourceDTO> getResourcesByDbId(@RequestParam("dbId") String dbId,
											@RequestParam(name = "resCName", required = false) String resCName,
											@RequestParam("resType") String resType,
											@RequestParam("pageNo") Integer pageNo,
											@RequestParam("pageSize") Integer pageSize);


	@PostMapping(PREFIX + "/resource/ops/res/resource/getResourceById")
	@Operation(summary ="根据ID或资源代码获取资源信息")
	@Parameters({
			@Parameter(name = "resourceId", description = "资源ID(ID与代码必传其一)"),
			@Parameter(name = "resourceCode", description = "资源编码")
	})
	OpsResourceDTO getResourceById(@RequestParam("resourceId") String resourceId,
								   @RequestParam("resourceCode")String resourceCode);


	@PostMapping(PREFIX + "/resource/ops/res/resource/getFieldAllData")
	@Operation(summary ="根据ID或资源代码获取资源信息")
	@Parameter(name = "resId", description = "资源ID")
	List<OpsResFieldDTO> getFieldAllData(@RequestParam("resId") String resId);

	@PostMapping(PREFIX + "/uac/user/facemgr/face/registerByUrl")
	@Operation(summary = "用户人脸注册")
	@Parameters({
			@Parameter(name = "idCard", description = "用户身份证号码|在押人员编码"),
			@Parameter(name = "faceUrl", description = "人脸地址"),
			@Parameter(name = "userType", description = "用户类型(1:警员,2:在押人员"),
	})
	public Map<String, Object> registerFace(@RequestParam("idCard") String idCard,
			@RequestParam("faceUrl") String faceUrl, @RequestParam("userType") Integer userType);
}
