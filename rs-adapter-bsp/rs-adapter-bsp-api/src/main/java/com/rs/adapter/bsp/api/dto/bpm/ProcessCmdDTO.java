package com.rs.adapter.bsp.api.dto.bpm;

import com.bsp.sdk.msg.model.ReceiveUser;
import com.bsp.security.model.SessionUser;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/***
 * 流程执行对象
 * <AUTHOR>
 *
 */
@Data
public class ProcessCmdDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * ACT流程实例Id
	 */
	private String actInstId;
	/**
	 * 流程定义Id
	 */
	private String actDefId;
	/**
	 * 流程定义key
	 */
	private String actDefKey;
	/**
	 * 流程id
	 */
	private String taskId;
	/**
	 * 业务id
	 */
	private String businessId;
	/**
	 * 审批意见（数字）(com.rs.adapter.bsp.enums.BspApproveStatusEnum)
	 */
	private short isApprove;
	/**
	 * 审批意见（文字）(com.rs.adapter.bsp.enums.BspApproveStatusEnum)
	 */
	private String isApproveStr;
	/**
	 * 审批意见（文字）
	 */
	private String approvalContent;
	/**
	 * 审批日期
	 */
	private Date approvalDate;
	/**
	 * 执行用户ID（此处传身份证号）
	 */
	private String assigneeUserId;
	/**
	 * 执行用户名称
	 */
	private String assigneeUserName;
	/**
	 * 执行机构id
	 */
	private String assigneeOrgId;
	/**
	 * 执行机构名称
	 */
	private String assigneeOrgName;
	/**
	 * 执行区域Id
	 */
	private String assigneeAreaId;
	/**
	 * 执行区域名称
	 */
	private String assigneeAreaName;
	/**
	 * 执行市局ID
	 */
	private String assigneeCityId;
	/**
	 * 下一级审批人信息
	 */
	private List<ReceiveUser> candidateUsers;
	/**
	 * 委派用户
	 */
	private ReceiveUser delegateUser;
	/**
	 * 是否委派
	 */
	private boolean isDelegateTask = false;
	/**
	 * 是否退查
	 */
	private boolean isBack = false;
	/**
	 * 退回类型（com.rs.adapter.bsp.enums.BspApproceBackTypeEnum）
	 */
	private short backType = 0;

	/**
	 * 退回节点Id
	 */
	private String backNodeId;
	/**
	 *  是否结束
	 */
	private boolean isTerminateTask = false;
	/**
	 * 变量
	 */
	private Map<String, Object> variables = new HashMap<String, Object>();
	/**
	 * bsp流程信息
	 */
	private List<DefinitionDTO> definition;
	/**
	 * 抄送用户
	 */
	private List<ReceiveUser> csldbh;
	/**
	 * 来源应用
	 */
	private String fApp;

	/**
	 *来源消息平台 pc/app
	 */
	private String fXxpt;
	/**
	 * 自定义标题
	 */
	private String msgTit;

	/**
	 * 消息地址
	 */
	private String msgUrl;

	@Override
	public String toString() {
		return "ProcessCmdDTO{" +
				"actDefId='" + actDefId + '\'' +
				", actDefKey='" + actDefKey + '\'' +
				", taskId='" + taskId + '\'' +
				", businessId='" + businessId + '\'' +
				", isApprove=" + isApprove +
				", approvalContent='" + approvalContent + '\'' +
				", approvalDate=" + approvalDate +
				", assigneeUserId='" + assigneeUserId + '\'' +
				", assigneeUserName='" + assigneeUserName + '\'' +
				", assigneeOrgId='" + assigneeOrgId + '\'' +
				", assigneeOrgName='" + assigneeOrgName + '\'' +
				", assigneeAreaId='" + assigneeAreaId + '\'' +
				", assigneeAreaName='" + assigneeAreaName + '\'' +
				", assigneeCityId='" + assigneeCityId + '\'' +
				", candidateUsers=" + candidateUsers +
				", delegateUser=" + delegateUser +
				", isDelegateTask=" + isDelegateTask +
				", isBack=" + isBack +
				", backType=" + backType +
				", backNodeId='" + backNodeId + '\'' +
				", isTerminateTask=" + isTerminateTask +
				", variables=" + variables +
				", definition=" + definition +
				", csldbh=" + csldbh +
				", fApp='" + fApp + '\'' +
				", fXxpt='" + fXxpt + '\'' +
				", msgTit='" + msgTit + '\'' +
				", msgUrl='" + msgUrl + '\'' +
				'}';
	}
}