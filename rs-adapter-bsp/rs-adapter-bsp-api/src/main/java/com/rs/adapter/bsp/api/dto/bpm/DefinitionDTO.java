package com.rs.adapter.bsp.api.dto.bpm;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 工作流管理-流程定义表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-08-31
 */
@Data
public class DefinitionDTO implements Serializable,Cloneable  {

	private static final long serialVersionUID = 1L;

	/**
	 * 流程名称
	 */
	private String name;
	/**
	 * 流程KEY
	 */
	private String defKey;

}