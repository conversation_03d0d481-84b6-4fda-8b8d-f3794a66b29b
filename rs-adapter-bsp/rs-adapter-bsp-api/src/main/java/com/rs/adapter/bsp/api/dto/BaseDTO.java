package com.rs.adapter.bsp.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 基准DTO
 * <AUTHOR>
 * @date 2025年4月14日
 */
@Data
public class BaseDTO {

	@Schema(description = "添加用户")
	String addUser;
    
	@Schema(description = "添加时间")
    String addTime;
    
	@Schema(description = "更新用户")
    String updateUser;
    
	@Schema(description = "更新时间")
    String updateTime;
    
	@Schema(description = "是否删除(0否1是)")
    String isdel;
}
