package com.rs.adapter.bsp.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "RPC服务-机构用户Response DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrgUserRespDTO extends BaseDTO{

	@Schema(description = "主键")
    String id;
	
	@Schema(description = "地市Id")
    String cityId;
	
	@Schema(description = "地市名称")
    String cityName;
	
	@Schema(description = "地市代码")
    String cityCode;
	
	@Schema(description = "区域Id")
    String regionId;
	
	@Schema(description = "区域名称")
    String regionName;
	
	@Schema(description = "区域代码")
    String regionCode;
	
	@Schema(description = "机构Id")
    String orgId;
	
	@Schema(description = "机构名称")
    String orgName;
	
	@Schema(description = "机构代码")
    String orgCode;
	
	@Schema(description = "登录名")
    String loginId;
	
	@Schema(description = "登录密码")
    String password;
	
	@Schema(description = "姓名")
    String name;
	
	@Schema(description = "性别")
    String sex;
	
	@Schema(description = "身份证号码")
    String idCard;
	
	@Schema(description = "拼音码")
    String scode;
	
	@Schema(description = "最后访问时间")
    String lastVisit;
	
	@Schema(description = "是否禁用(0否1是)")
    String isDisabled;
	
	@Schema(description = "排序Id")
    String orderId;
	
	@Schema(description = "是否辅警")
    String isFj;
	
	@Schema(description = "邮箱")
    String email;
	
	@Schema(description = "手机号")
    String mobile;
	
	@Schema(description = "职位")
    String position;
}
