package com.rs.adapter.bsp.api.dto;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString(callSuper = true)
public class OpsResourceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;
    private String systemMark;
    private String resName;
    private String resCname;
    private String resType;
    private String remark;
    private String isDel;
    private String appId;
    private String dbId;
    private String tableId;
    private String scriptId;
    private String script;
    private String interfaceId;
    private String interfaceContent;

}
