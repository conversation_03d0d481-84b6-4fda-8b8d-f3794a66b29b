package com.rs.adapter.bsp.api.dto;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Data
@ToString(callSuper = true)
public class OpsDatasourceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;
    private String dsName;
    private String dbType;
    private String dbUser;
    private String dbPass;
    private String dbUrl;
    private String remark;
    private String isDel;
    private String addUser;
    private Date addTime;
    private String updateUser;
    private Date updateTime;
    private String dbAttr;
    private String driverClass;
    private String systemMark;
    private String appId;
    private String dbOther;

}

