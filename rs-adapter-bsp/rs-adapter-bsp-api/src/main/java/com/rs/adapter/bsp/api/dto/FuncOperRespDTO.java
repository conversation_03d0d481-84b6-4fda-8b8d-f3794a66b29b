package com.rs.adapter.bsp.api.dto;

import java.util.List;
import java.util.Map;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "RPC服务-功能操作Response DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FuncOperRespDTO extends BaseDTO{

	@Schema(description = "主键")
    String id;
	
	@Schema(description = "应用Id")
    String appId;
	
	@Schema(description = "操作名称")
    String name;
	
	@Schema(description = "功能代码")
    String code;
	
	@Schema(description = "父Id")
    String parentId;
    
    @Schema(description = "功能Id")
    String funcId;
    
    @Schema(description = "功能信息")
    Map<String, Object> function;
    
    @Schema(description = "URL地址")
    String url;
    
    @Schema(description = "打开模式")
    String openMode;
    
    @Schema(description = "是否禁用(0否1是)")
    String disabled;
    
    @Schema(description = "排序Id")
    Integer orderId;
    
    @Schema(description = "操作标识")
    String mark;
    
    @Schema(description = "子操作")
    List<Map<String, Object>> children;
}
