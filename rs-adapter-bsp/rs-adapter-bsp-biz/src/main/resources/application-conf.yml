conf:
  server:
    ip: *************
    port:
      bsp: 9110
  snowflake:
    worker-id: 1 		#全局唯一guid生成器终端ID,最大值为31，最小值为1
    datacenter-id: 2 	#全局唯一guid生成器数据中心ID,最大值为31，最小值为1
  system-mark: bsp
  matchers:
    ignores: /doc.html/**,/swagger/**,/rpc-api/**,/bsp/app/**
  debug: false
  datasource:
    druid:
      log-slow-sql: true
      slow-sql-millis: 100
    dynamic:
      druid:
        initial-size: 1
        min-idle: 1 		# 最小连接池数量
        max-active: 20 		# 最大连接池数量
        max-wait: 600000 	# 配置获取连接等待超时的时间，单位：毫秒
      master:
        url: jdbc:mysql://${conf.server.ip}:3336/bsp_v1.1?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&nullCatalogMeansCurrent=true&rewriteBatchedStatements=true # MySQL Connector/J 8.X 连接的示例
        username: root
        password: sundun_bsp
      acp:
        url: jdbc:postgresql://${conf.server.ip}:5432/rs_v1?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&nullCatalogMeansCurrent=true&rewriteBatchedStatements=true # MySQL Connector/J 8.X 连接的示例
        username: postgres
        password: Go@123456
  mongodb:
    uri: mongodb://${conf.server.ip}:27111/bsp
    hosts:
  redis:
    host: ${conf.server.ip}
    port: 6399
    database: 3
    password: redisbsp
    timeout: 6000  # 连接超时时长（毫秒）
    max-redirects: 3
    lettuce:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接

---
conf:
  nacos:
    enabled: true
    ip: ${conf.server.ip}
    port: 8848
    username: nacos
    password: nacos@gxx
    namespace: rs
    group: DEFAULT_GROUP
---
conf:
  dromara:
    x-file-storage:
      enable-storage: true
      access-key: admin
      secret-key: admin123456
      end-point: http://${conf.server.ip}:9010
      bucket-name: acp
      domain: http://${conf.server.ip}:9010/acp/
      base-path:
---
conf:
  bsp:
    baseUrl: http://*************:1910
    token:
      url: http://*************:1910/oauth/token
  bpm:
    baseUrl: http://*************:1911

