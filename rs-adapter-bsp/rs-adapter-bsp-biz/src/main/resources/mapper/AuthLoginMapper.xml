<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.adapter.bsp.dao.AuthLoginDao">

	<!-- 根据设备序列号获取监所Id -->
	<select id="getPrisonIdBySerialNumber" resultType="com.rs.adapter.bsp.entity.PrisonDO">
		select b.org_code as prisonId, b.org_name as prisonName from acp_pm_device_inscreen a 
			inner join acp_pm_device b on a.device_id = b.id 
		where a.serial_number = #{serialNumber} limit 1
	</select>

</mapper>
