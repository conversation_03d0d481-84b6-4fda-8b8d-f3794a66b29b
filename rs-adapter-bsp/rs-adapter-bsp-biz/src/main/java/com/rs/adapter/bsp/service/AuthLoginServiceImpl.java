package com.rs.adapter.bsp.service;

import org.springframework.stereotype.Service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.adapter.bsp.dao.AuthLoginDao;
import com.rs.adapter.bsp.entity.PrisonDO;
import com.rs.adapter.bsp.vo.SiwLoginInfoVo;

/**
 * 登录认证服务实现
 * <AUTHOR>
 * @date 2025年5月15日
 */
@Service
@DS("acp")
public class AuthLoginServiceImpl extends BaseServiceImpl<AuthLoginDao, SiwLoginInfoVo> implements AuthLoginService{

	/**
	 * 根据设备序列号获取监所Id
	 * @param serialNumber String 设备序列号
	 * @return PrisonDO
	 */
	public PrisonDO getPrisonIdBySerialNumber(String serialNumber) {
		return baseMapper.getPrisonIdBySerialNumber(serialNumber);
	}
}
