package com.rs.adapter.bsp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.rs.framework.mybatis.entity.BaseDO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 机构DO
 * <AUTHOR>
 * @date 2025年5月15日
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UacOrgDO extends BaseDO{
	private static final long serialVersionUID = -2015441053438075602L;

	@ApiModelProperty(name = "id", value = "id", allowEmptyValue=true)
    @TableId("ID")
    private String id;

    @ApiModelProperty(name = "unitNo", value = "机构代码")
    @TableField("UNIT_NO")
    private String unitNo;

    @ApiModelProperty(name = "name", value = "批量Id", allowEmptyValue=true)
    @TableField("NAME")
    private String name;

    @ApiModelProperty(name = "code", value = "附加属性", allowEmptyValue=true)
    @TableField("CODE")
    private String code;

    @ApiModelProperty(name = "sname", value = "简称", allowEmptyValue=true)
    @TableField("SNAME")
    private String sname;

    @ApiModelProperty(name = "fname", value = "全称", allowEmptyValue=true)
    @TableField("FNAME")
    private String fname;

    @ApiModelProperty(name = "scode", value = "拼音码", allowEmptyValue=true)
    @TableField("SCODE")
    private String scode;

    @ApiModelProperty(name = "jpcode", value = "简拼码", allowEmptyValue=true)
    @TableField("JPCODE")
    private String jpcode;

    @ApiModelProperty(name = "otherSname", value = "文书号简称", allowEmptyValue=true)
    @TableField("OTHER_SNAME")
    private String otherSname;

    @ApiModelProperty(name = "cityId", value = "功能Id", allowEmptyValue=true)
    @TableField("CITY_ID")
    private String cityId;

    @ApiModelProperty(name = "cityName", value = "地市名称", allowEmptyValue=true)
    @TableField("CITY_NAME")
    private String cityName;

    @ApiModelProperty(name = "regionId", value = "区域id", allowEmptyValue=true)
    @TableField("REGION_ID")
    private String regionId;

    @ApiModelProperty(name = "regionName", value = "区域名称", allowEmptyValue=true)
    @TableField("REGION_NAME")
    private String regionName;

    @ApiModelProperty(name = "parentId", value = "父Id", allowEmptyValue=true)
    @TableField("PARENT_ID")
    private String parentId;

    @ApiModelProperty(name = "address", value = "地址", allowEmptyValue=true)
    @TableField("ADDRESS")
    private String address;

    @ApiModelProperty(name = "officeTel", value = "办公电话", allowEmptyValue=true)
    @TableField("OFFICE_TEL")
    private String officeTel;

    @ApiModelProperty(name = "isFormal", value = "是否正式机构(0否1是)", allowEmptyValue=true)
    @TableField("IS_FORMAL")
    private String isFormal;

    @ApiModelProperty(name = "haszfq", value = "是否具有执法权(0否1是)", allowEmptyValue = true)
    @TableField("haszfq")
    private String haszfq;

    @ApiModelProperty(name = "isDisabled", value = "是否停用(0否1是)", allowEmptyValue=true)
    @TableField("IS_DISABLED")
    private String isDisabled;

    @ApiModelProperty(name = "orderId", value = "排序Id", allowEmptyValue=true)
    @TableField("ORDER_ID")
    private Integer orderId;

    @ApiModelProperty(name = "orgType", value = "机构类型", allowEmptyValue=true)
    @TableField("ORG_TYPE")
    private String orgType;

    @ApiModelProperty(name = "orgCategory", value = "警种", allowEmptyValue=true)
    @TableField(value = "ORG_CATEGORY")
    private String orgCategory;

    @ApiModelProperty(name = "parentName", value = "上级机构名称", allowEmptyValue=true)
    @TableField(exist = false)
    private String parentName;

    @ApiModelProperty(name = "bjdh", value = "报警电话", allowEmptyValue=true)
    @TableField(value = "BJDH")
    private String bjdh;
    
    @ApiModelProperty(name = "email", value = "电子邮件", allowEmptyValue=true)
    @TableField(value = "EMAIL")
    private String email;
    
    @ApiModelProperty(name = "czdh", value = "传真电话", allowEmptyValue=true)
    @TableField(value = "CZDH")
    private String czdh;
    
    @ApiModelProperty(name = "intro", value = "单位介绍", allowEmptyValue=true)
    @TableField(value = "INTRO")
    private String intro;
    
    @ApiModelProperty(name = "orgPath", value = "单位路径", allowEmptyValue=true)
    @TableField(value = "orgPath")
    private String orgPath;
}
