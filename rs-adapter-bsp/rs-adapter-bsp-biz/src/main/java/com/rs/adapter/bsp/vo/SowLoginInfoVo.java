package com.rs.adapter.bsp.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 仓外屏登录用户信息Vo
 * <AUTHOR>
 * @date 2025年5月14日
 */
@Data
public class SowLoginInfoVo implements Serializable {
	private static final long serialVersionUID = 4300100459924937954L;

	@ApiModelProperty(value = "用户id")
    private String userid;

	@ApiModelProperty(value = "身份证号")
    private String idcard;

	@ApiModelProperty(value = "用户名")
    private String name;

	@ApiModelProperty(value = "警号")
    private String policeCode;

	@ApiModelProperty(value = "民警id")
    private String policeId;

	@ApiModelProperty(value = "民警名称")
    private String policeName;

	@ApiModelProperty(value = "中队id")
    private String squadronId;

	@ApiModelProperty(value = "中队名称")
    private String squadronName;

	@ApiModelProperty(value = "监所id")
    private String prisonId;

	@ApiModelProperty(value = "登录用户监所名")
    private String prisonName;
	
	@ApiModelProperty(value = "身份证号")
    private String prisonAbbreviation;

	@ApiModelProperty(value = "登录用户名")
    private String username;

	@ApiModelProperty(value = "登录用户密码")
    private String userpassword;

//	@ApiModelProperty(value = "登录用户角色")
//    private List<SysRoleVO> roleList;

	@ApiModelProperty(value = "登录角色id")
    private Integer roleId;

	@ApiModelProperty(value = "登录角色姓名")
    private String roleName;

//	@ApiModelProperty(value = "拥有监所列表")
//    private List<PrisonInfoVo> prisonList;

	@ApiModelProperty(value = "在押人员编号")
    private String prisonerId;

	@ApiModelProperty(value = "在押人员姓名")
    private String prisonerName;

	@ApiModelProperty(value = "是否是属于支队 0:否  1:是")
    private String isZd;

    @ApiModelProperty(value = "token")
    private String token;

    @ApiModelProperty("民警照片")
    private String photo;

	@ApiModelProperty("登录方式")
	private String loginType;

	@ApiModelProperty("人员类型")
	private String personnelType;

    @ApiModelProperty("监所类型")
    private String prisonType;
}
