package com.rs.adapter.bsp;

import java.net.UnknownHostException;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.mzt.bsp.logapi.starter.annotation.EnableLogRecord;
import com.rs.framework.common.util.spring.SpringUtils;

/**
 * bsp适配器启动程序
 * <AUTHOR>
 * @date 2025年4月13日
 */
@SpringBootApplication(exclude = {DruidDataSourceAutoConfigure.class, MongoAutoConfiguration.class,
		MongoDataAutoConfiguration.class, HibernateJpaAutoConfiguration.class})
@ComponentScan(value = {"com.rs.*", "com.bsp.*"})
@EnableLogRecord(systemMark = "bsp")
public class BspAdapterApplication {

	public static void main(String[] args) throws UnknownHostException {
		ConfigurableApplicationContext application = SpringApplication.run(BspAdapterApplication.class, args);
		SpringUtils.printStartLog(application);
	}
}
