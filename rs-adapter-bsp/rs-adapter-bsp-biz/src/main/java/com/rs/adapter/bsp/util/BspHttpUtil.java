package com.rs.adapter.bsp.util;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.StringUtil;
import com.rs.framework.rpc.core.util.SecurityUtils;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;

/**
 * http接口调用工具类
 * <AUTHOR>
 * @date 2025年4月14日
 */
public class BspHttpUtil {
	
	/**
	 * 处理带参数的post请求
	 * @param url String 请求地址
	 * @param paramMap Map<String, Object> 请求参数
	 * @return String
	 */
	public static String HttpRequestPost(String url, Map<String, Object> paramMap) {
		HttpRequest postRequest = HttpRequest.post(url);
		bindToken(postRequest);
		HttpResponse execute = postRequest.form(paramMap).execute();
		return execute.body();
	}
	
	/**
	 * 处理post请求
	 * @param url String 请求地址
	 * @return String
	 */
	public static String HttpRequestPost(String url) {
		return HttpRequestPost(url, null);
	}
	
	/**
	 * 处理带参数的post请求(body)
	 * @param url String 请求地址
	 * @param body String 请求体参数
	 * @return String
	 */
	public static String HttpRequestPostBody(String url, String body) {
		HttpRequest postRequest = HttpRequest.post(url);
		bindToken(postRequest);
		HttpResponse execute = postRequest.body(body).execute();
		return execute.body();
	}
	
	/**
	 * 处理post请求(body)
	 * @param url String 请求地址
	 * @return String
	 */
	public static String HttpRequestPostBody(String url) {
		return HttpRequestPostBody(url, new JSONObject().toJSONString());
	}

	/**
	 * 处理带参数的get请求
	 * @param url String 请求地址
	 * @param paramMap Map<String, Object> 请求参数
	 * @return String
	 */
	public static String HttpRequestGet(String url, Map<String, Object> paramMap) {
		HttpRequest getRequest = HttpRequest.get(url);
		bindToken(getRequest);
		HttpResponse execute = getRequest.form(paramMap).execute();
		return execute.body();
	}
	
	/**
	 * 处理带参数的get请求
	 * @param url String 请求地址
	 * @return String
	 */
	public static String HttpRequestGet(String url) {
		return HttpRequestGet(url, null);
	}
	
	/**
	 * 绑定请求Token
	 * @param request HttpRequest
	 */
	public static void bindToken(HttpRequest request) {
		HttpServletRequest servletRequest = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
				.getRequest();
		String authorization = servletRequest.getHeader("Authorization");
		if(StringUtil.isNotEmpty(authorization)) {
			request.header(Header.AUTHORIZATION, authorization);
		}
		else {
			String token = SecurityUtils.getRpcToken();
			if(StringUtil.isNotEmpty(token)) {
				request.header(Header.AUTHORIZATION, "Bearer " + token);
			}
		}
	}
}
