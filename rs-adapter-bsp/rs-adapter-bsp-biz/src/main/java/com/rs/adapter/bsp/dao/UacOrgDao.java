package com.rs.adapter.bsp.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.adapter.bsp.entity.UacOrgDO;

/**
 * bsp认证中心-机构Dao
 * <AUTHOR>
 * @date 2025年5月15日
 */
@Mapper
public interface UacOrgDao extends IBaseDao<UacOrgDO>{

	/**
	 * 根据机构代码查询机构
	 * @param code String 机构代码
	 * @return UacOrgDO
	 */
	public UacOrgDO getOrgByCode(@Param("code") String code);
}
