package com.rs.adapter.bsp.service;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.adapter.bsp.entity.PrisonDO;
import com.rs.adapter.bsp.vo.SiwLoginInfoVo;

/**
 * 登录认证接口
 * <AUTHOR>
 * @date 2025年5月15日
 */
public interface AuthLoginService extends IBaseService<SiwLoginInfoVo> {

	/**
	 * 根据设备序列号获取监所Id
	 * @param serialNumber String 设备序列号
	 * @return PrisonDO
	 */
	public PrisonDO getPrisonIdBySerialNumber(String serialNumber);
}
