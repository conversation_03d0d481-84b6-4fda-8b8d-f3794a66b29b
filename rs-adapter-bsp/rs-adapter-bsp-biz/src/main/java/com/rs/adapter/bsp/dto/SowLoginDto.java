package com.rs.adapter.bsp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 仓外屏登录信息Dto
 * <AUTHOR>
 * @date 2025年5月14日
 */
@Data
@ApiModel("仓外屏登录dto")
public class SowLoginDto {

	@ApiModelProperty(value = "人员类型")
	private String personnelType;

	@ApiModelProperty(value = "登录编码")
	private String code;

	@ApiModelProperty(value = "设备序列号")
	private String serialNumber;
}
