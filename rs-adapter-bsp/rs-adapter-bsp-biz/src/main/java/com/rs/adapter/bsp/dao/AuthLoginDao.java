package com.rs.adapter.bsp.dao;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.adapter.bsp.entity.PrisonDO;
import com.rs.adapter.bsp.vo.SiwLoginInfoVo;

/**
 * 监所管理-登录Dao
 * <AUTHOR>
 * @date 2025年5月15日
 */
public interface AuthLoginDao extends IBaseDao<SiwLoginInfoVo> {

	/**
	 * 根据设备序列号获取监所Id
	 * @param serialNumber String 设备序列号
	 * @return PrisonDO
	 */
	public PrisonDO getPrisonIdBySerialNumber(String serialNumber);
}
