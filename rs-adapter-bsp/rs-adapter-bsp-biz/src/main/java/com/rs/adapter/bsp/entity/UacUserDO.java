package com.rs.adapter.bsp.entity;

import com.rs.framework.mybatis.entity.BaseDO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户DO
 * <AUTHOR>
 * @date 2025年3月24日
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UacUserDO extends BaseDO{
	private static final long serialVersionUID = 1L;
	
	@ApiModelProperty("主键")
    private String id;
	
	@ApiModelProperty("身份证号码")
    private String idCard;
	
	@ApiModelProperty("照片")
    private String photo;

	@ApiModelProperty("姓名")
	private String name;

	@ApiModelProperty("机构编码")
	private String orgCode;
	
	@ApiModelProperty("机构名称")
	private String orgName;
}
