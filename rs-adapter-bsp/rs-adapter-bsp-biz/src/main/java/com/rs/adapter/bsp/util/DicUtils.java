package com.rs.adapter.bsp.util;

import com.bsp.common.cache.DicUtil;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.util.http.HttpUtils;

import java.util.List;
import java.util.Map;

/**
 * @ClassName DicUtils
 * <AUTHOR>
 * @Date 2025/6/24 15:39
 * @Version 1.0
 */
public class DicUtils {
    public static List<String> translate(String dicName, List<String> codes) {
        List<String> translate = DicUtil.translate(HttpUtils.getAppCode(), dicName, codes);
        if (translate == null || translate.isEmpty()) {
            translate = DicUtil.translate("bsp", dicName, codes);
        }
        return translate;
    }


    public static String translate(String dicName, String code) {
        String translate = DicUtil.translate(HttpUtils.getAppCode(), dicName, code);
        if (StringUtil.isEmpty(translate) || "null".equals(translate)) {
            translate = DicUtil.translate("bsp", dicName, code);
        }
        return translate;
    }


    public static <T> List<Map<String, Object>> translate(List<T> list, String[] dicNames, String[] dicFields) {
        List<Map<String, Object>> translate = DicUtil.translate(HttpUtils.getAppCode(), list, dicNames, dicFields);
        if (translate == null || translate.isEmpty()) {
            translate = DicUtil.translate("bsp", list, dicNames, dicFields);
        }
        return translate;
    }


    public static <T> List<Map<String, Object>> translate(List<T> list, String dicName, String dicField) {
        List<Map<String, Object>> translate = DicUtil.translate(list, HttpUtils.getAppCode(), dicName, dicField);
        if (translate == null || translate.isEmpty()) {
            translate = DicUtil.translate(list, "bsp", dicName, dicField);
        }
        return translate;
    }

}
