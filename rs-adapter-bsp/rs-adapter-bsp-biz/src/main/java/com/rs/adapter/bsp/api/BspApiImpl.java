package com.rs.adapter.bsp.api;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.StringUtil;
import com.rs.adapter.bsp.api.dto.*;
import com.rs.adapter.bsp.cons.BaseConstants;
import com.rs.adapter.bsp.util.BspHttpUtil;
import com.rs.adapter.bsp.util.ResultUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * RPC服务-API接口实现类，提供RESTful API接口，给Feign调用
 *
 * <AUTHOR>
 * @date 2025年4月14日
 */
@RestController
@Validated
@Api(tags = "bsp - api服务")
public class BspApiImpl implements BspApi {

    private static final Logger log = LoggerFactory.getLogger(BspApiImpl.class);
    @Value("${bsp.baseUrl}")
    private String baseUrl;

    @Value("${bsp.clientId:app}")
    private String clientId;

    @Value("${bsp.clientSecret:123456}")
    private String clientSecret;

    @ApiOperation("使用客户端模式获取Token")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "clientId", value = "第三方客户端唯一凭证(通过申请获取)", required = true),
            @ApiImplicitParam(name = "clientSecret", value = "客户端秘钥(与凭证一起申请获取)", required = true)
    })
    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> getTokenByClient(String clientId, String clientSecret) {
        String uri = "/oauth/token";
        Map<String, Object> map = new HashMap<>();
        map.put("grant_type", "client_credentials");
        map.put("client_id", clientId);
        map.put("client_secret", clientSecret);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        Map<String, Object> map1 = JSONUtil.toBean(s, Map.class);
        return map1;
    }

    @ApiOperation("使用密码模式获取Token")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userName", value = "用户名，使用CryptoUtils.encrypt()加密", required = true),
            @ApiImplicitParam(name = "password", value = "密码(md5摘要)", required = true),
            @ApiImplicitParam(name = "appMark", value = "应用标识", required = true)
    })
    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> getTokenByPassword(String userName, String password, String appMark) {
        String uri = "/oauth/token";
        Map<String, Object> map = new HashMap<>();
        map.put("grant_type", "password");
        map.put("client_id", "user_client");
        map.put("client_secret", "user_client");
        map.put("username", userName);
        map.put("password", password);
        map.put("appMark", appMark);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        Map<String, Object> map1 = JSONUtil.toBean(s, Map.class);
        return map1;
    }

    @ApiOperation("使用身份证模式获取Token")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idCard", value = "身份证号，使用CryptoUtils.encrypt()加密", required = true),
            @ApiImplicitParam(name = "password", value = "密码(md5摘要)", required = true),
            @ApiImplicitParam(name = "appMark", value = "应用标识", required = true)
    })
    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> getTokenByIdCard(String idCard, String password, String appMark) {
        String uri = "/oauth/token";
        Map<String, Object> map = new HashMap<>();
        map.put("grant_type", "password");
        map.put("client_id", "user_client");
        map.put("client_secret", "user_client");
        map.put("username", idCard);
        map.put("password", password);
        map.put("auth_type", "id_card");
        map.put("appMark", appMark);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        Map<String, Object> map1 = JSONUtil.toBean(s, Map.class);
        return map1;
    }

    @PostMapping(PREFIX + "/oauth/getTempToken")
    @Operation(summary = "获取临时Token")
    public String getTempToken() {
        Map<String, Object> map = getTokenByClient(clientId, clientSecret);
        if (map != null && map.containsKey("access_token")) {
            return String.valueOf(map.get("access_token"));
        }
        return null;
    }

    @ApiOperation("获取全部应用分页数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "当前页号，默认为1", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页多少条，默认为10", defaultValue = "10")
    })
    @Override
    public List<AppRespDTO> getAllAppPageData(Integer pageNo, Integer pageSize) {
        String uri = "/api/v1/config/app/getAllAppPageData";
        Map<String, Object> map = new HashMap<>();
        map.put("pageNo", pageNo);
        map.put("pageSize", pageSize);
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        JSONObject data = jsonObject.getJSONObject("data");
        List<AppRespDTO> appList = ResultUtil.transList(data, "records", AppRespDTO.class);
        return appList;
    }

    @ApiOperation("根据应用代码获取应用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "应用代码")
    })
    @Override
    public AppRespDTO getAppByCode(String code) {
        String uri = "/api/v1/config/app/getAppByCode";
        Map<String, Object> map = new HashMap<>();
        map.put("code", code);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        AppRespDTO app = ResultUtil.trans(jsonObject, "data", AppRespDTO.class);
        return app;
    }

    @ApiOperation("获取应用全部角色分类")
    @Override
    public List<RoleCatRespDTO> getAppAllCats() {
        String uri = "/api/v1/perm/rolecat/getAppAllCats";
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri);
        JSONObject jsonObject = JSONObject.parseObject(s);
        List<RoleCatRespDTO> appCat = ResultUtil.transList(jsonObject, "data", RoleCatRespDTO.class);
        return appCat;
    }

    @ApiOperation("全部角色分类和角色")
    @Override
    public List<RoleCatRespDTO> getCatRoles() {
        String uri = "/api/v1/perm/role/getCatRoles";
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri);
        JSONObject jsonObject = JSONObject.parseObject(s);
        List<RoleCatRespDTO> appCat = ResultUtil.transList(jsonObject, "data", RoleCatRespDTO.class);
        return appCat;
    }

    @ApiOperation("根据角色代码获取角色")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "角色代码")
    })
    @Override
    public RoleRespDTO getRoleByCode(String code) {
        String uri = "/api/v1/perm/role/getRoleByCode";
        Map<String, Object> map = new HashMap<>();
        map.put("code", code);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        RoleRespDTO roles = ResultUtil.trans(jsonObject, "data", RoleRespDTO.class);
        return roles;
    }

    @ApiOperation("获取全部角色分页数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "当前页号，默认为1", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页多少条，默认为10", defaultValue = "10")
    })
    @Override
    public List<RoleRespDTO> getRolePageData(Integer pageNo, Integer pageSize) {
        String uri = "/api/v1/perm/role/getRolePageData";
        Map<String, Object> map = new HashMap<>();
        map.put("pageNo", pageNo);
        map.put("pageSize", pageSize);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        JSONObject data = jsonObject.getJSONObject("data");
        List<RoleRespDTO> roleList = ResultUtil.transList(data, "records", RoleRespDTO.class);
        return roleList;
    }

    @ApiOperation("获取用户授权的角色")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户Id")
    })
    @Override
    public List<RoleRespDTO> getRolesByUserId(String userId) {
        String uri = "/api/v1/perm/perm/getUserRoles";
        Map<String, Object> map = new HashMap<>();
        map.put("userId", userId);
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        List<RoleRespDTO> roleList = ResultUtil.transList(jsonObject, "data", RoleRespDTO.class);
        return roleList;
    }

    @ApiOperation("获取用户可以访问的功能操作")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "funcId", value = "功能Id"),
            @ApiImplicitParam(name = "funcMark", value = "功能标识"),
            @ApiImplicitParam(name = "userId", value = "用户Id")
    })
    @Override
    public List<FuncOperRespDTO> getUserFuncOpers(String funcId, String funcMark, String userId) {
        String uri = "/api/v1/perm/perm/getUserFuncOpers";
        Map<String, Object> map = new HashMap<>();
        map.put("funcId", funcId);
        map.put("funcMark", funcMark);
        map.put("userId", userId);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        List<FuncOperRespDTO> functionOperation = ResultUtil.transList(jsonObject, "data", FuncOperRespDTO.class);
        return functionOperation;
    }

    @ApiOperation("获取功能包含的全部操作")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "funcId", value = "功能Id"),
            @ApiImplicitParam(name = "funcMark", value = "功能标识")
    })
    @Override
    public List<FuncOperRespDTO> getAllFuncOpers(String funcId, String funcMark) {
        String uri = "/api/v1/config/funcoper/getAllFuncOpers";
        Map<String, Object> map = new HashMap<>();
        map.put("funcId", funcId);
        map.put("funcMark", funcMark);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        return ResultUtil.transList(jsonObject, "data", FuncOperRespDTO.class);
    }

    @ApiOperation("获取功能包含的可用操作")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "funcId", value = "功能Id"),
            @ApiImplicitParam(name = "funcMark", value = "功能标识")
    })
    @Override
    public List<FuncOperRespDTO> getFuncOpers(String funcId, String funcMark) {
        String uri = "/api/v1/config/funcoper/getFuncOpers";
        Map<String, Object> map = new HashMap<>();
        map.put("funcId", funcId);
        map.put("funcMark", funcMark);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        return ResultUtil.transList(jsonObject, "data", FuncOperRespDTO.class);
    }

    @ApiOperation("获取应用注册的全部功能")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "当前页号，默认为1", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页多少条，默认为10", defaultValue = "10"),
            @ApiImplicitParam(name = "appId", value = "应用Id"),
            @ApiImplicitParam(name = "appCode", value = "应用代码")
    })
    @Override
    public List<FunctionRespDTO> getAppAllFuncPageData(Integer pageNo, Integer pageSize, String appId, String appCode) {
        String uri = "/api/v1/config/func/getAppAllFuncPageData";
        Map<String, Object> map = new HashMap<>();
        map.put("pageNo", pageNo);
        map.put("pageSize", pageSize);
        map.put("appId", appId);
        map.put("appCode", appCode);
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        JSONObject data = jsonObject.getJSONObject("data");
        List<FunctionRespDTO> applicationFuncs = ResultUtil.transList(data, "records", FunctionRespDTO.class);
        return applicationFuncs;
    }

    @ApiOperation("获取应用注册的可能功能")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "当前页号，默认为1", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页多少条，默认为10", defaultValue = "10"),
            @ApiImplicitParam(name = "appId", value = "应用Id"),
            @ApiImplicitParam(name = "appCode", value = "应用代码")
    })
    @Override
    public List<FunctionRespDTO> getAppFuncPageData(Integer pageNo, Integer pageSize, String appId, String appCode) {
        String uri = "/api/v1/config/func/getAppFuncPageData";
        Map<String, Object> map = new HashMap<>();
        map.put("pageNo", pageNo);
        map.put("pageSize", pageSize);
        map.put("appId", appId);
        map.put("appCode", appCode);
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        JSONObject data = jsonObject.getJSONObject("data");
        List<FunctionRespDTO> applicationFuncs = ResultUtil.transList(data, "records", FunctionRespDTO.class);
        return applicationFuncs;
    }

    @ApiOperation("根据功能标识获取功能")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mark", value = "功能标识")
    })
    @Override
    public FunctionRespDTO getFuncByMark(String mark) {
        String uri = "/api/v1/config/func/getFuncByMark";
        Map<String, Object> map = new HashMap<>();
        map.put("mark", mark);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        FunctionRespDTO function = ResultUtil.trans(jsonObject, "data", FunctionRespDTO.class);
        return function;
    }

    @ApiOperation("获取全部行政区划")
    @Override
    public List<AreaRespDTO> getAllAreas() {
        String uri = "/api/v1/userorg/area/getAllAreas";
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri);
        JSONObject jsonObject = JSONObject.parseObject(s);
        List<AreaRespDTO> areas = ResultUtil.transList(jsonObject, "data", AreaRespDTO.class);
        return areas;
    }

    @ApiOperation("根据区划代码获取行政区划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "areaCode", value = "区划代码")
    })
    @Override
    public AreaRespDTO getAreaByCode(String areaCode) {
        String uri = "/api/v1/userorg/area/getAreaByCode";
        Map<String, Object> map = new HashMap<>();
        map.put("areaCode", areaCode);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        AreaRespDTO area = null;
        if (jsonObject.containsKey("data")) {
            String data = jsonObject.getString("data");
            area = JSONObject.parseObject(data, AreaRespDTO.class);
        }
        return area;
    }

    @ApiOperation("根据用户Id获取用户姓名和身份证号码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "用户Id，多个时以逗号分隔")
    })
    @Override
    public List<UserRespDTO> getUserNameIdCardById(String ids) {
        String uri = "/api/v1/userorg/user/getUserNameIdCardById";
        Map<String, Object> map = new HashMap<>();
        map.put("ids", ids);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        List<UserRespDTO> user = ResultUtil.transList(jsonObject, "data", UserRespDTO.class);
        return user;
    }

    @ApiOperation("获取某机构指定角色的用户姓名和身份证号码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgId", value = "机构Id"),
            @ApiImplicitParam(name = "roleId", value = "角色Id")
    })
    @Override
    public List<UserRespDTO> getUserNameIdCardByOrgAndRole(String orgId, String roleId) {
        String uri = "/api/v1/userorg/user/getUserNameIdCardByOrgAndRole";
        Map<String, Object> map = new HashMap<>();
        map.put("orgId", orgId);
        map.put("roleId", roleId);
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        List<UserRespDTO> user = ResultUtil.transList(jsonObject, "data", UserRespDTO.class);
        return user;
    }

    @ApiOperation("获取机构全部用户分页数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "当前页号，默认为1", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页多少条，默认为10", defaultValue = "10"),
            @ApiImplicitParam(name = "orgCode", value = "机构代码")
    })
    @Override
    public List<OrgUserRespDTO> getOrgAllUserPageData(Integer pageNo, Integer pageSize, String orgCode) {
        String uri = "/api/v1/userorg/user/getOrgAllUserPageData";
        Map<String, Object> map = new HashMap<>();
        map.put("pageNo", pageNo);
        map.put("pageSize", pageSize);
        map.put("orgCode", orgCode);
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        JSONObject data = jsonObject.getJSONObject("data");
        List<OrgUserRespDTO> regOrg = ResultUtil.transList(data, "records", OrgUserRespDTO.class);
        return regOrg;
    }

    @ApiOperation("获取机构可用用户分页数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "当前页号，默认为1", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页多少条，默认为10", defaultValue = "10"),
            @ApiImplicitParam(name = "orgCode", value = "机构代码")
    })
    @Override
    public Map<String, Object> getOrgUserPageData(Integer pageNo, Integer pageSize, String orgCode) {
        String uri = "/api/v1/userorg/user/getOrgUserPageData";
        Map<String, Object> map = new HashMap<>();
        map.put("pageNo", pageNo);
        map.put("pageSize", pageSize);
        map.put("orgCode", orgCode);
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        Map<String, Object> regOrg = ResultUtil.trans(jsonObject, "data", Map.class);
        return regOrg;
    }

    @ApiOperation("根据区划（区域Id或机构Id）查找用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "regIds", value = "区域Id或区域代码,多个以逗号分隔"),
            @ApiImplicitParam(name = "orgIds", value = "机构Id或机构代码,多个以逗号分隔")
    })
    @Override
    public List<OrgUserRespDTO> selectUserByDivision(String regIds, String orgIds) {
        String uri = "/api/v1/userorg/user/selectUserByDivision";
        Map<String, Object> map = new HashMap<>();
        if (StringUtil.isNotEmpty(regIds)) {
            map.put("regIds", regIds);
        }
        if (StringUtil.isNotEmpty(orgIds)) {
            map.put("orgIds", orgIds);
        }
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        List<OrgUserRespDTO> regOrg = ResultUtil.transList(jsonObject, "data", OrgUserRespDTO.class);
        return regOrg;
    }

    @ApiOperation("根据用户Id获取用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户Id")
    })
    @Override
    public OrgUserRespDTO getUserByUserId(String userId) {
        String uri = "/api/v1/userorg/user/getUserByUserId";
        Map<String, Object> map = new HashMap<>();
        map.put("userId", userId);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        OrgUserRespDTO orgUser = ResultUtil.trans(jsonObject, "data", OrgUserRespDTO.class);
        return orgUser;
    }

    @ApiOperation("根据登录帐号获取用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "loginId", value = "登录账号")
    })
    @Override
    public OrgUserRespDTO getUserByLoginId(String loginId) {
        String uri = "/api/v1/userorg/user/getUserByLoginId";
        Map<String, Object> map = new HashMap<>();
        map.put("loginId", loginId);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        OrgUserRespDTO orgUser = ResultUtil.trans(jsonObject, "data", OrgUserRespDTO.class);
        return orgUser;
    }

    @ApiOperation("根据登录帐号和密码获取用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "loginId", value = "登录账号"),
            @ApiImplicitParam(name = "password", value = "登录密码(无需加密)")
    })
    @Override
    public OrgUserRespDTO getUserByLoginIdPwd(String loginId, String password) {
        String uri = "/api/v1/userorg/user/getUserByLoginIdPwd";
        Map<String, Object> map = new HashMap<>();
        map.put("loginId", loginId);
        map.put("password", password);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        OrgUserRespDTO orgUser = ResultUtil.trans(jsonObject, "data", OrgUserRespDTO.class);
        return orgUser;
    }

    @ApiOperation("根据身份证号码获取用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idCard", value = "身份证号码")
    })
    @Override
    public OrgUserRespDTO getUserByIdCard(String idCard) {
        String uri = "/api/v1/userorg/user/getUserByIdCard";
        Map<String, Object> map = new HashMap<>();
        map.put("idCard", idCard);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        OrgUserRespDTO orgUser = ResultUtil.trans(jsonObject, "data", OrgUserRespDTO.class);
        return orgUser;
    }

    @ApiOperation("根据身份证号码获取用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idCards", value = "身份证号码，多个时以逗号分隔")
    })
    @Override
    public List<OrgUserRespDTO> getUserByIdCards(String idCards) {
        String uri = "/api/v1/userorg/user/getUserByIdCards";
        Map<String, Object> map = new HashMap<>();
        map.put("idCard", idCards);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        List<OrgUserRespDTO> orgUser = ResultUtil.transList(jsonObject, "data", OrgUserRespDTO.class);
        return orgUser;
    }

    @ApiOperation("根据身份证号码获取用户姓名")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idCard", value = "身份证号码")
    })
    @Override
    public List<String> getUserNameByIdCard(String idCard) {
        String uri = "/api/v1/userorg/user/getUserNameByIdCard";
        Map<String, Object> map = new HashMap<>();
        map.put("idCard", idCard);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        List<String> orgUser = ResultUtil.transList(jsonObject, "data", String.class);
        return orgUser;
    }

    @ApiOperation("根据身份证号码批量获取用户姓名")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idCards", value = "身份证号码，多个时以逗号分隔")
    })
    @Override
    public List<String> getUserNameByIdCards(String idCards) {
        String uri = "/api/v1/userorg/user/getUserNameByIdCards";
        Map<String, Object> map = new HashMap<>();
        map.put("idCard", idCards);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        List<String> orgUser = ResultUtil.transList(jsonObject, "data", String.class);
        return orgUser;
    }

    @ApiOperation("获取某机构指定角色的用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgId", value = "机构Id"),
            @ApiImplicitParam(name = "roleId", value = "角色Id")
    })
    @Override
    public List<OrgUserRespDTO> getUserByOrgAndRole(String orgId, String roleId) {
        String uri = "/api/v1/userorg/user/getUserByOrgAndRole";
        Map<String, Object> map = new HashMap<>();
        map.put("orgId", orgId);
        map.put("roleId", roleId);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        List<OrgUserRespDTO> orgUser = ResultUtil.transList(jsonObject, "data", OrgUserRespDTO.class);
        return orgUser;
    }

    @ApiOperation("获取某区域指定角色的用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "regId", value = "区域Id"),
            @ApiImplicitParam(name = "roleId", value = "角色Id")
    })
    @Override
    public List<OrgUserRespDTO> getUserByRegAndRole(String regId, String roleId) {
        String uri = "/api/v1/userorg/user/getUserByRegAndRole";
        Map<String, Object> map = new HashMap<>();
        map.put("regId", regId);
        map.put("roleId", roleId);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        List<OrgUserRespDTO> orgUser = ResultUtil.transList(jsonObject, "data", OrgUserRespDTO.class);
        return orgUser;
    }

    @ApiOperation("根据区域Id和机构名称查找用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "regId", value = "区域Id"),
            @ApiImplicitParam(name = "orgName", value = "机构名称，支持模糊查询")
    })
    @Override
    public List<OrgUserRespDTO> getUserByRegAndOrgName(String regId, String orgName) {
        String uri = "/api/v1/userorg/user/getUserByRegAndOrgName";
        Map<String, Object> map = new HashMap<>();
        map.put("regId", regId);
        map.put("orgName", orgName);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        List<OrgUserRespDTO> orgUserList = ResultUtil.transList(jsonObject, "data", OrgUserRespDTO.class);
        return orgUserList;
    }

    @ApiOperation("根据机构代码获取机构")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构代码")
    })
    @Override
    public OrgRespDTO getOrgByCode(String orgCode) {
        String uri = "/api/v1/userorg/org/getOrgByCode";
        Map<String, Object> map = new HashMap<>();
        map.put("orgCode", orgCode);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        OrgRespDTO orgList = ResultUtil.trans(jsonObject, "data", OrgRespDTO.class);
        return orgList;
    }

    @ApiOperation("获取全部机构分页数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "当前页号，默认为1", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页多少条，默认为10", defaultValue = "10")
    })
    @Override
    public List<OrgRespDTO> getAllOrgPageData(Integer pageNo, Integer pageSize) {
        String uri = "/api/v1/userorg/org/getAllOrgPageData";
        Map<String, Object> map = new HashMap<>();
        map.put("pageNo", pageNo);
        map.put("pageSize", pageSize);
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        JSONObject data = jsonObject.getJSONObject("data");
        List<OrgRespDTO> orgList = ResultUtil.transList(data, "records", OrgRespDTO.class);
        return orgList;
    }

    @ApiOperation("获取可用机构分页数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "map", value = "机构map参数(pageNo|pageSize|orgId|orgName|regId|regName|cityId|cityName)")
    })
    @Override
    public List<OrgRespDTO> getOrgPageData(@RequestBody Map<String, Object> map) {
        String uri = "/api/v1/userorg/org/getOrgPageData";
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        JSONObject data = jsonObject.getJSONObject("data");
        List<OrgRespDTO> orgList = ResultUtil.transList(data, "records", OrgRespDTO.class);
        return orgList;
    }

    @ApiOperation("获取全部子机构数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "parentId", value = "父机构Id")
    })
    @Override
    public List<OrgRespDTO> getAllOrgByParentId(String parentId) {
        String uri = "/api/v1/userorg/org/getAllChildOrgs";
        Map<String, Object> map = new HashMap<>();
        map.put("parentId", parentId);
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        List<OrgRespDTO> orgList = ResultUtil.transList(jsonObject, "data", OrgRespDTO.class);
        return orgList;
    }

    @ApiOperation("获取可用子机构数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "parentId", value = "父机构Id")
    })
    @Override
    public List<OrgRespDTO> getChildOrgsByParentId(String parentId) {
        String uri = "/api/v1/userorg/org/getChildOrgs";
        Map<String, Object> map = new HashMap<>();
        map.put("parentId", parentId);
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        List<OrgRespDTO> orgList = ResultUtil.transList(jsonObject, "data", OrgRespDTO.class);
        return orgList;
    }

    @ApiOperation("获取全部机构数据")
    @Override
    public List<OrgRespDTO> getAllOrgs() {
        String uri = "/api/v1/userorg/org/getAllOrgs";
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri);
        JSONObject jsonObject = JSONObject.parseObject(s);
        List<OrgRespDTO> orgList = ResultUtil.transList(jsonObject, "data", OrgRespDTO.class);
        return orgList;
    }

    @ApiOperation("根据参数获取机构数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "map", value = "机构Map参数(name|code|regId|regName|cityId|cityName|isDisabled|regIds|cityIds)")
    })
    @Override
    public List<OrgRespDTO> getOrgDataByParams(@RequestBody Map<String, Object> map) {
        String uri = "/api/v1/userorg/org/getOrgDataByParams";
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        List<OrgRespDTO> orgList = ResultUtil.transList(jsonObject, "data", OrgRespDTO.class);
        return orgList;
    }

    @ApiOperation("获取区域全部机构分页数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "当前页号，默认为1", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页多少条，默认为10", defaultValue = "10"),
            @ApiImplicitParam(name = "regCode", value = "区域代码", defaultValue = "10")
    })
    @Override
    public List<OrgRespDTO> getRegOrgAllPageData(Integer pageNo, Integer pageSize, String regCode) {
        String uri = "/api/v1/userorg/org/getRegOrgAllPageData";
        Map<String, Object> map = new HashMap<>();
        map.put("pageNo", pageNo);
        map.put("pageSize", pageSize);
        map.put("regCode", regCode);
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        List<OrgRespDTO> regOrgList = ResultUtil.transList(jsonObject.getJSONObject("data"), "records", OrgRespDTO.class);
        return regOrgList;
    }

    @ApiOperation("根据规则代码获取系统编号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ruleCode", value = "规则代码", required = true),
            @ApiImplicitParam(name = "formData", value = "表单数据", required = false)
    })
    @Override
    public String executeByRuleCode(String ruleCode, String formData) {
        if (formData == null) {
            formData = new JSONObject().toJSONString();
        }
        //计算开始结束的耗时
        long startTime = System.currentTimeMillis();
        String uri = "/com/fillrule/execute/" + ruleCode;
        log.info("执行规则，uri:{},formData:{}", baseUrl + uri, formData);
        String s = BspHttpUtil.HttpRequestPostBody(baseUrl + uri, formData);
        log.info("执行结果:{}", s);
        long endTime = System.currentTimeMillis();
        log.info("耗时:{}ms", endTime - startTime);
        if (StringUtil.isNotEmpty(s)) {
            JSONObject jsonObject = JSONObject.parseObject(s);
            return jsonObject.getString("data");
        }
        return null;
    }

    @ApiOperation("根据标识执行自定义查询data返回单条")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "paramMap", value = "自定义查询标识及其他参数")
    })
    @Override
    public JSONObject executeFmQuery(String mark, Map<String, Object> paramMap) {
        String uri = "/com/form/handle/executeFmQuery?mark=" + mark;
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, paramMap);
        if (StringUtil.isNotEmpty(s)) {
            JSONObject jsonObject = JSONObject.parseObject(s);
            return jsonObject;
        }
        return null;
    }

    @ApiOperation("根据标识执行自定义查询data返回多条")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "paramMap", value = "自定义查询标识及其他参数")
    })
    @Override
    public JSONObject executeMultiQuery(String mark, Map<String, Object> paramMap) {
        String uri = "/com/form/handle/executeMultiQuery?mark=" + mark;
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, paramMap);
        if (StringUtil.isNotEmpty(s)) {
            JSONObject jsonObject = JSONObject.parseObject(s);
            return jsonObject;
        }
        return null;
    }


    @ApiOperation("获取全部数据源信息")
    @ApiImplicitParam(name = "appId", value = "应用ID")
    @Override
    public List<OpsDatasourceDTO> getDbByAppId(String appId) {
        String uri = "/ops/res/datasource/getDbByAppId";
        Map<String, Object> map = new HashMap<>();
        map.put("appId", appId);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        if (!jsonObject.containsKey("data") || !"200".equals(jsonObject.getString("code"))) {
            log.error(jsonObject.getString("msg"));
            return null;
        }
        return JSONObject.parseArray(jsonObject.getString("data"), OpsDatasourceDTO.class);
    }

    @ApiOperation("数据源ID")
    @ApiImplicitParam(name = "dbId", value = "数据源ID")
    @Override
    public OpsDatasourceDTO getDbById(String dbId) {
        String uri = "/ops/res/datasource/getDbById";
        Map<String, Object> map = new HashMap<>();
        map.put("dbId", dbId);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        if (!jsonObject.containsKey("data") || !"200".equals(jsonObject.getString("code"))) {
            log.error(jsonObject.getString("msg"));
            return null;
        }
        return JSONObject.parseObject(jsonObject.getString("data"), OpsDatasourceDTO.class);
    }

    @ApiOperation("根据数据源ID获取下属所有资源信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dbId", value = "数据源ID"),
            @ApiImplicitParam(name = "resCName", value = "资源中文名称", required = false),
            @ApiImplicitParam(name = "resType", value = "资源类型 01：数据表，02：查询脚本"),
            @ApiImplicitParam(name = "pageNo", value = "起始页码,默认1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量,默认10"),
    })
    @Override
    public List<OpsResourceDTO> getResourcesByDbId(String dbId, String resCName, String resType, Integer pageNo, Integer pageSize) {
        String uri = "/ops/res/resource/getResourcesByDbId";
        Map<String, Object> map = new HashMap<>();
        map.put("dbId", dbId);
        map.put("resCName", resCName);
        map.put("resType", resType);
        map.put("pageNo", pageNo);
        map.put("pageSize", pageSize);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        if (!jsonObject.containsKey("rows") || !"200".equals(jsonObject.getString("code"))) {
            log.error(jsonObject.getString("msg"));
            return null;
        }
        return JSONObject.parseArray(jsonObject.getString("rows"), OpsResourceDTO.class);
    }

    @ApiOperation("根据ID或资源代码获取资源信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "resourceId", value = "资源ID(ID与代码必传其一)"),
            @ApiImplicitParam(name = "resourceCode", value = "资源编码")
    })
    @Override
    public OpsResourceDTO getResourceById(String resourceId, String resourceCode) {
        String uri = "/ops/res/resource/getResourceById";
        Map<String, Object> map = new HashMap<>();
        if (StringUtils.hasText(resourceId)) {
            map.put("resourceId", resourceId);
        }
        if (StringUtils.hasText(resourceCode)) {
            map.put("resourceCode", resourceCode);
        }
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        if (!jsonObject.containsKey("data") || !"200".equals(jsonObject.getString("code"))) {
            log.error(jsonObject.getString("msg"));
            return null;
        }
        return JSONObject.parseObject(jsonObject.getString("data"), OpsResourceDTO.class);
    }

    @ApiOperation("根据资源ID获取下属所有字段信息")
    @ApiImplicitParam(name = "resId", value = "资源ID")
    @Override
    public List<OpsResFieldDTO> getFieldAllData(String resId) {
        String uri = "/ops/res/field/getFieldAllData";
        Map<String, Object> map = new HashMap<>();
        map.put("resId", resId);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        if (!jsonObject.containsKey("rows") || !"200".equals(jsonObject.getString("code"))) {
            log.error(jsonObject.getString("msg"));
            return null;
        }
        return JSONObject.parseArray(jsonObject.getString("rows"), OpsResFieldDTO.class);
    }

    @SuppressWarnings("unchecked")
    @ApiOperation("用户人脸注册")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idCard", value = "用户身份证号码|在押人员编码"),
            @ApiImplicitParam(name = "faceUrl", value = "人脸地址"),
            @ApiImplicitParam(name = "userType", value = "用户类型(0:警员,1:在押人员"),
    })
    @Override
    public Map<String, Object> registerFace(String idCard, String faceUrl, Integer userType) {
        String uri = "/uac/user/facemgr/face/registerByUrl";
        Map<String, Object> map = new HashMap<>();
        map.put("idCard", idCard);
        map.put("faceUrl", faceUrl);
        if (userType == 1) {
            map.put("userType", BaseConstants.BSP_USER_TYPE_POLICE);
        } else {
            map.put("userType", BaseConstants.BSP_USER_TYPE_OTHER);
        }
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, map);
        Map<String, Object> map1 = JSONUtil.toBean(s, Map.class);
        return map1;
    }
}
