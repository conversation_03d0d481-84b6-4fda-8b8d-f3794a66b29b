package com.rs.adapter.bsp.controller.app;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SmUtil;
import cn.hutool.http.HttpStatus;
import com.bsp.common.cons.CommonConstants;
import com.bsp.common.util.StringUtil;
import com.rs.adapter.bsp.cons.LoginTypeEnum;
import com.rs.adapter.bsp.cons.PersonnelTypeEnum;
import com.rs.adapter.bsp.cons.RsConstants;
import com.rs.adapter.bsp.dto.SiwLoginDto;
import com.rs.adapter.bsp.dto.SowLoginDto;
import com.rs.adapter.bsp.entity.PrisonDO;
import com.rs.adapter.bsp.entity.UacOrgDO;
import com.rs.adapter.bsp.service.AuthLoginService;
import com.rs.adapter.bsp.service.UacOrgService;
import com.rs.adapter.bsp.util.BspHttpUtil;
import com.rs.adapter.bsp.util.DicUtils;
import com.rs.adapter.bsp.vo.SiwLoginInfoVo;
import com.rs.adapter.bsp.vo.SowLoginInfoVo;
import com.rs.framework.common.msg.R;
import com.rs.framework.common.msg.ResultVO;
import com.rs.framework.common.util.json.JsonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@Api(tags = "app - 登录控制器")
@RestController
@RequestMapping("/bsp/app/login")
@Validated
@Slf4j
public class LoginController {

    @Value("${bsp.baseUrl}")
    private String baseUrl;

    @Autowired
    private AuthLoginService loginService;

    @Autowired
    private UacOrgService orgService;

    @ApiOperation("仓内屏登录")
    @PostMapping("/siwLogin")
    public R<?> siwLogin(@RequestBody SiwLoginDto loginDto) {
        R<SiwLoginInfoVo> r = new R<SiwLoginInfoVo>();

        try {
            String appCode = loginDto.getAppCode();
            String userNameArr[] = appCode.replaceFirst("-", CommonConstants.DEFAULT_SPLIT_STR)
                    .split(CommonConstants.DEFAULT_SPLIT_STR);
            if (userNameArr.length < 2) {
                return R.responseError("app-仓内屏登录失败-登录参数不正确！");
            }

            String loginType = userNameArr[0];
            String loginId = userNameArr[1];

            //获取Token
            Map<String, Object> tokenMap = getToken(RsConstants.LOGIN_METHOD_SIW, appCode, loginId);

            //有响应结果
            if (!tokenMap.isEmpty()) {
                SiwLoginInfoVo vo = new SiwLoginInfoVo();
                String token = (String) tokenMap.get("access_token");
                if (StringUtil.isNotEmpty(token)) {

                    //构建基本数据
                    vo.setCode(loginId);
                    vo.setType(loginType);
                    vo.setName((String) tokenMap.get("name"));
                    vo.setPhoto((String) tokenMap.get("photo"));
                    vo.setToken(token);
                    vo.setJg((String) tokenMap.get("jg"));
                    vo.setMz((String) tokenMap.get("mz"));
                    vo.setSex((String) tokenMap.get("sex"));
                    vo.setZjhm((String) tokenMap.get("idCard"));

                    //字典翻译(民族、性别、籍贯)
                    if (StringUtil.isNotEmpty(vo.getMz())) {
                        vo.setMzName(DicUtils.translate( "ZD_MZ", vo.getMz()));
                    }
                    if (StringUtil.isNotEmpty(vo.getSex())) {
                        vo.setSexName(DicUtils.translate( "ZD_XB", vo.getSex()));
                    }
                    if (StringUtil.isNotEmpty(vo.getJg())) {
                        vo.setJgName(DicUtils.translate( "ZD_JG", vo.getJg()));
                    }

                    //构建监所数据
                    if (StringUtil.isNotEmpty(loginDto.getSerialNumber())) {
                        PrisonDO prisonDO = loginService.getPrisonIdBySerialNumber(loginDto.getSerialNumber());
                        if (prisonDO != null) {
                            vo.setPrisonId(prisonDO.getPrisonId());
                            vo.setPrisonName(prisonDO.getPrisonName());
                        }
                    } else {
                        vo.setPrisonId((String) tokenMap.get("orgCode"));
                        vo.setPrisonName((String) tokenMap.get("orgName"));
                    }

                    //警员登录
                    if (RsConstants.LOGIN_TYPE_POLICE.equalsIgnoreCase(loginType)) {
                        vo.setPoliceCode((String) tokenMap.get("loginId"));
                        vo.setUserId((String) tokenMap.get("id"));
                        vo.setUserName((String) tokenMap.get("name"));
                    }

                    //在押人员登录
                    else {
                        vo.setUserName((String) tokenMap.get("name"));
                        vo.setPrisonerId((String) tokenMap.get("loginId"));
                        vo.setPrisonerName((String) tokenMap.get("name"));
                        vo.setCode((String) tokenMap.get("loginId"));
                    }

                    r.setData(vo);
                    r.setMessage("登录成功");
                    r.setStatus(HttpStatus.HTTP_OK);
                    r.setReturnCode(R.SUCCESS);

                    return r;
                } else {
                    String msg = (String) tokenMap.get("msg");
                    r.setMessage("app-仓内屏登录失败！" + msg);
                    r.setStatus(HttpStatus.HTTP_INTERNAL_ERROR);
                    r.setReturnCode(R.FAIL);
                }
            } else {
                r.setMessage("app-仓内屏登录失败-登录获取token异常");
                r.setStatus(HttpStatus.HTTP_INTERNAL_ERROR);
                r.setReturnCode(R.FAIL);
            }
        } catch (Exception e) {
            log.error("app-仓内屏登录发生异常：{}", e);
            r.setMessage("app-仓内屏登录发生异常");
            r.setStatus(HttpStatus.HTTP_INTERNAL_ERROR);
            r.setReturnCode(R.FAIL);
        }

        return r;
    }

    /**
     * 获取bsp的token
     *
     * @param loginCategory String 登录类型
     * @param username      String 登录用户名
     * @param password      String 登录密码
     * @return Map<String, Object>
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> getToken(String loginCategory, String username, String password) {

        //认证密码
        password = Base64.encode(SmUtil.sm3().digest(password, "utf-8"));

        //构建参数
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("grant_type", "password");
        paramMap.put("client_id", "user_client");
        paramMap.put("client_secret", "user_client");
        paramMap.put("login_category", loginCategory);
        paramMap.put("username", username);
        paramMap.put("password", password);

        String resultStr = BspHttpUtil.HttpRequestPost(baseUrl + "/oauth/token", paramMap);
        Map<String, Object> resultMap = JsonUtils.parseObject(resultStr, Map.class);

        return resultMap;
    }

    @ApiOperation("仓外屏登录")
    @PostMapping("/sowLogin")
    public ResultVO<?> sowLogin(@RequestBody SowLoginDto loginDto) {
        SowLoginInfoVo vo = new SowLoginInfoVo();

        try {
            if (PersonnelTypeEnum.POLICE.getValue().equals(loginDto.getPersonnelType())) {

                //获取Token
                Map<String, Object> tokenMap = getToken(RsConstants.LOGIN_METHOD_SOW, loginDto.getCode(), loginDto.getCode());

                //有响应结果
                if (!tokenMap.isEmpty()) {
                    String token = (String) tokenMap.get("access_token");
                    if (StringUtil.isNotEmpty(token)) {
                        vo.setLoginType(LoginTypeEnum.CWP.getValue());
                        vo.setPersonnelType(loginDto.getPersonnelType());
                        vo.setName((String) tokenMap.get("name"));
                        vo.setUsername((String) tokenMap.get("name"));
                        vo.setUserid((String) tokenMap.get("id"));
                        vo.setIdcard((String) tokenMap.get("idCard"));
                        vo.setPoliceCode((String) tokenMap.get("loginId"));
                        vo.setPoliceName((String) tokenMap.get("name"));
                        vo.setPoliceId(loginDto.getCode());
                        vo.setToken(token);

                        //构建监所数据
                        if (StringUtil.isNotEmpty(loginDto.getSerialNumber())) {
                            PrisonDO prisonDO = loginService.getPrisonIdBySerialNumber(loginDto.getSerialNumber());
                            if (prisonDO != null) {
                                vo.setPrisonId(prisonDO.getPrisonId());
                                vo.setPrisonName(prisonDO.getPrisonName());
                            }
                        } else {
                            vo.setPrisonId((String) tokenMap.get("orgCode"));
                            vo.setPrisonName((String) tokenMap.get("orgName"));
                        }

                        //设置监所类型
                        if (StringUtil.isNotEmpty(vo.getPrisonId())) {
                            UacOrgDO org = orgService.getOrgByCode(vo.getPrisonId());
                            if (org != null) {
                                vo.setPrisonType(org.getOrgType());
                            }
                        }
                    } else {
                        String msg = (String) tokenMap.get("msg");
                        return ResultVO.error("app-仓外屏登录-登录获取token！" + msg);
                    }
                } else {
                    return ResultVO.error("app-仓外屏登录-登录获取token异常");
                }
            } else {
                return ResultVO.error("app-仓外屏登录-限制登录");
            }
        } catch (Exception e) {
            log.error("app-仓外屏登录发生异常：{}", e);
            return ResultVO.error("app-仓外屏登录发生异常");
        }

        return ResultVO.success(vo);
    }

    @SuppressWarnings("unchecked")
    @ApiOperation("注销登录")
    @PostMapping("/logout")
    public ResultVO<?> logout(@RequestParam(value = "token", required = true) String token) {
        try {
            //构建参数
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("access_token", token);

            String resultStr = BspHttpUtil.HttpRequestPost(baseUrl + "/oauth/logout", paramMap);
            Map<String, Object> resultMap = JsonUtils.parseObject(resultStr, Map.class);

            if (!resultMap.isEmpty()) {
                boolean isSuccess = (Boolean) resultMap.get("success");
                if (isSuccess) {
                    return ResultVO.success("注销成功");
                } else {
                    String msg = (String) resultMap.get("msg");
                    return ResultVO.error("app-注销登录失败！" + msg);
                }
            } else {
                return ResultVO.error("app-注销登录发生异常！");
            }
        } catch (Exception e) {
            log.error("app-注销登录发生异常：{}", e);
            return ResultVO.error("app-注销登录发生异常");
        }
    }
}
