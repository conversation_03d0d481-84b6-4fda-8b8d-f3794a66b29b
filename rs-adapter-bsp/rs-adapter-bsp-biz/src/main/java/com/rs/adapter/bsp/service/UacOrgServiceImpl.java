package com.rs.adapter.bsp.service;

import org.springframework.stereotype.Service;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.adapter.bsp.dao.UacOrgDao;
import com.rs.adapter.bsp.entity.UacOrgDO;

/**
 * 用户服务实现类
 * <AUTHOR>
 * @date 2025年3月24日
 */
@Service
public class UacOrgServiceImpl extends BaseServiceImpl<UacOrgDao, UacOrgDO> implements UacOrgService{
	
	/**
	 * 根据机构代码查询机构
	 * @param code String 机构代码
	 * @return UacOrgDO
	 */
	public UacOrgDO getOrgByCode(String code) {
		return baseMapper.getOrgByCode(code);
	}
}
