package com.rs.adapter.bsp.dao;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.adapter.bsp.entity.UacUserDO;

/**
 * bsp认证中心-用户Dao
 * <AUTHOR>
 * @date 2025年3月24日
 */
@Mapper
public interface UacUserDao extends IBaseDao<UacUserDO>{

	/**
	 * 根据身份证号码查询用户照片
	 * @param idCards Set<String> 身份证号码
	 * @return List<UserDO>
	 * <AUTHOR>
	 * @date 2025年3月24日
	 */
	public List<UacUserDO> getUserPhotoByIdCard(@Param("idCards") Set<String> idCards);

	/**
	 * 根据机构代码与角色编号查询用户信息
	 * @param orgCode 机构代码
	 * @param roleCode 角色编号
	 * @return List<UserDO>
	 * <AUTHOR>
	 * @date 2025年3月24日
	 */
	public List<UacUserDO> getUserByOrgAndRole(@Param("orgCode") String orgCode, @Param("roleCode") String roleCode);

	/**
	 * 根据机构代码与岗位字典编码查询用户信息
	 * @param orgCode 机构代码
	 * @param postCode 岗位字典 ZD_POST 编码
	 * @return
	 */
	public List<UacUserDO> getUserByOrgAndPost(@Param("orgCode") String orgCode, @Param("postCode") String postCode);
}
