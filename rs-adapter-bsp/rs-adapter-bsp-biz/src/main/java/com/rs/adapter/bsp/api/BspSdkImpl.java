package com.rs.adapter.bsp.api;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.cache.DicUtil;
import com.bsp.common.cache.RedisClient;
import com.bsp.common.cons.CommonConstants;
import com.bsp.common.util.SettingUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.MultiValueMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * RPC服务-SDK接口实现类，提供RESTful API接口，给Feign调用
 *
 * <AUTHOR>
 * @date 2025年4月14日
 */
@RestController
@Validated
@Api(tags = "bsp - sdk服务")
public class BspSdkImpl implements BspSdk{

	@ApiOperation("获取系统参数值")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "systemMark", value = "系统标识", required = true),
		@ApiImplicitParam(name = "paramName", value = "参数名称", required = true)
	})
	@Override
	public String getParamValue(String systemMark, String paramName) {
        return SettingUtil.getParamValue(paramName, systemMark);
    }

	@ApiOperation("字典翻译")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "systemMark", value = "系统标识", required = true),
		@ApiImplicitParam(name = "dicName", value = "字典名称", required = true),
		@ApiImplicitParam(name = "codes", value = "字典代码，多个以逗号分隔", required = true)
	})
	@Override
	public List<String> translate(String systemMark, String dicName, String codes) {
		return DicUtil.translate(systemMark, dicName, ListUtil.toList(StrUtil.split(codes, CommonConstants.DEFAULT_SPLIT_STR)));
	}

	@ApiOperation("字典翻译")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "systemMark", value = "系统标识", required = true),
		@ApiImplicitParam(name = "dicName", value = "字典名称", required = true),
		@ApiImplicitParam(name = "codeList", value = "字典代码集合", required = true)
	})
	@Override
	public List<String> translate(String systemMark, String dicName, List<String> codeList) {
		return DicUtil.translate(systemMark, dicName, codeList);
	}

	@ApiOperation("字典翻译")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "systemMark", value = "系统标识", required = true),
		@ApiImplicitParam(name = "listStr", value = "字典数据，多个以逗号分隔"),
		@ApiImplicitParam(name = "dicNames", value = "字典名称，多个以逗号分隔"),
		@ApiImplicitParam(name = "dicFields", value = "字典字段，多个以逗号分隔")
	})
	@Override
	public List<Map<String, Object>> translateMapList(String systemMark, String listStr, String dicNames, String dicFields) {
		return DicUtil.translateMulti(systemMark, JSONUtil.parseArray(listStr)
				.toBean(new TypeReference<List<Map<String, Object>>>(){}),
				StringUtils.split(dicNames, CommonConstants.DEFAULT_SPLIT_STR),
				StringUtils.split(dicFields, CommonConstants.DEFAULT_SPLIT_STR));
	}

	@ApiOperation("字典翻译")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "queryParam", value = "查询参数", required = true)
	})
	@Override
	public List<Map<String, Object>> translateMapListLongData(@RequestBody MultiValueMap<String,String> queryParam) {
		List<String> listStr = (List<String>) queryParam.get("listStr");
		return DicUtil.translateMulti(listStr.get(3), JSONUtil.parseArray(listStr.get(0)).toBean(
				new TypeReference<List<Map<String, Object>>>(){}),
				StringUtils.split(listStr.get(1), CommonConstants.DEFAULT_SPLIT_STR),
				StringUtils.split(listStr.get(2), CommonConstants.DEFAULT_SPLIT_STR));
	}

	@ApiOperation("字典翻译")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "systemMark", value = "系统标识", required = true),
		@ApiImplicitParam(name = "listStr", value = "字典数据，多个以逗号分隔"),
		@ApiImplicitParam(name = "dicNames", value = "字典名称，多个以逗号分隔"),
		@ApiImplicitParam(name = "dicFields", value = "字典字段，多个以逗号分隔")
	})
	@Override
	public <T> List<Map<String, Object>> translateEntityList(String systemMark, String listStr, String dicNames, String dicFields) {
		return DicUtil.translate(systemMark, JSONUtil.parseArray(listStr).toBean(
				new TypeReference<List<T>>(){}),
				StringUtils.split(dicNames, CommonConstants.DEFAULT_SPLIT_STR),
				StringUtils.split(dicFields, CommonConstants.DEFAULT_SPLIT_STR));
	}

	@ApiOperation("获取字典数据")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "systemMark", value = "系统标识", required = true),
		@ApiImplicitParam(name = "dicNames", value = "字典名称，多个以逗号分隔")
	})
	@Override
	public <T> Map<Object, Object> getDic(String systemMark, String dicNames) {
		Map<String, String> hget = RedisClient.hgetAll("com:dic:" + systemMark + ":" + dicNames);
		return JSONObject.parseObject(JSONObject.toJSONString(hget), new TypeReference<Map<String, Object>>(){});
	}
}
