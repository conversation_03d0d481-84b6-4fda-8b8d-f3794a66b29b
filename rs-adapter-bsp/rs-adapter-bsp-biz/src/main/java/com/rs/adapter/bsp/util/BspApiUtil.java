package com.rs.adapter.bsp.util;

import com.bsp.common.util.StringUtil;
import com.rs.framework.common.util.http.HttpUtils;

/**
 * bsp接口调用工具类
 * <AUTHOR>
 * @date 2025年6月23日
 */
public class BspApiUtil {
	
	/**
	 * 构建流程定义Key
	 * @param defKey String 原始流程定义Key
	 * @return String
	 */
	public static String buildDefKey(String defKey) {
		String appCode = HttpUtils.getAppCode();
		if(StringUtil.isNotEmpty(appCode) && StringUtil.isNotEmpty(defKey)) {
			String prefixKey = appCode + "-";
			if(!defKey.startsWith(prefixKey)) {
				return prefixKey + defKey;
			}
		}
		return defKey;
	}
	
	/**
	 * 构建流程定义Key
	 * @param defKeys String 原始流程定义Key，多个时逗号分隔
	 * @return String
	 */
	public static String buildDefKeys(String defKeys) {
		String appCode = HttpUtils.getAppCode();
		if(StringUtil.isNotEmpty(appCode) && StringUtil.isNotEmpty(defKeys)) {
			String prefixKey = appCode + "-";
			String[] keys = defKeys.split(",");
			for(int i = 0; i < keys.length; i ++) {
				if(!keys[i].startsWith(prefixKey)) {
					keys[i] = prefixKey + keys[i];
				}
			}
			return String.join(",", keys);
		}
		return defKeys;
	}
}
