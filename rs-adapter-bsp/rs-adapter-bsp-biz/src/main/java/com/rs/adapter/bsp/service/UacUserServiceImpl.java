package com.rs.adapter.bsp.service;

import java.util.List;
import java.util.Set;

import com.bsp.security.util.SessionUserUtil;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.adapter.bsp.dao.UacUserDao;
import com.rs.adapter.bsp.entity.UacUserDO;

/**
 * 用户服务实现类
 * <AUTHOR>
 * @date 2025年3月24日
 */
@Service
public class UacUserServiceImpl extends BaseServiceImpl<UacUserDao, UacUserDO> implements UacUserService{
	
	/**
	 * 根据身份证号码查询用户照片
	 * @param idCards Set<String> 身份证号码
	 * @return List<UserDO>
	 * <AUTHOR>
	 * @date 2025年3月24日
	 */
	public List<UacUserDO> getUserPhotoByIdCard(@Param("idCards") Set<String> idCards){
		return baseMapper.getUserPhotoByIdCard(idCards);
	}

	/**
	 * 根据机构代码与角色编号查询用户信息
	 * @param orgCode 机构代码
	 * @param roleCode 角色编号
	 * @return List<UserDO>
	 * <AUTHOR>
	 * @date 2025年3月24日
	 */
	@Override
	public List<UacUserDO> getUserByOrgAndRole(String orgCode, String roleCode) {
		return baseMapper.getUserByOrgAndRole(orgCode, roleCode);
	}

	@Override
	public List<UacUserDO> getUserByOrgAndPost(String orgCode, String postCode) {
		return baseMapper.getUserByOrgAndPost(orgCode, postCode);
	}
	@Override
	public List<UacUserDO> getUserByNowOrgAndPost(String postCode) {
		return baseMapper.getUserByOrgAndPost(SessionUserUtil.getSessionUser().getOrgCode(), postCode);
	}
}
