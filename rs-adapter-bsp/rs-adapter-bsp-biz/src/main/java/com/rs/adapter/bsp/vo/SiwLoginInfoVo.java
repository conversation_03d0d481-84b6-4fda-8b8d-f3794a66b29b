package com.rs.adapter.bsp.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 仓内屏登录用户信息Vo
 * <AUTHOR>
 * @date 2025年5月14日
 */
@Data
public class SiwLoginInfoVo implements Serializable {
	private static final long serialVersionUID = -3837462643326991516L;

	@ApiModelProperty(value = "名称")
	private String name;

	@ApiModelProperty(value = "照片")
	private String photo;

	@ApiModelProperty(value = "编码")
	private String code;

	@ApiModelProperty(value = "类型")
	private String type;

	@ApiModelProperty(value = "监所编码")
	private String prisonId;

	@ApiModelProperty(value = "监所编码")
	private String prisonName;

	@ApiModelProperty(value = "用户id")
	private String userId;

	@ApiModelProperty(value = "警号", hidden = true)
	private String policeCode;

	@ApiModelProperty("用户名")
	private String userName;

	@ApiModelProperty("token")
	private String token;

	@ApiModelProperty(value = "在押人员编号")
	private String prisonerId;

	@ApiModelProperty(value = "在押人员姓名")
	private String prisonerName;

	@ApiModelProperty(value = "性别")
	private String sex;
	
	@ApiModelProperty(value = "性别名称")
	private String sexName;

	@ApiModelProperty(value = "民族")
	private String mz;
	
	@ApiModelProperty(value = "民族名称")
	private String mzName;
	
	@ApiModelProperty(value = "籍贯")
	private String jg;
	
	@ApiModelProperty(value = "籍贯名称")
	private String jgName;
	
	@ApiModelProperty(value = "证件号码")
	private String zjhm;
}
