package com.rs.adapter.bsp.vo;

import com.alibaba.fastjson.JSONObject;

/**
 * 登录用户信息接口
 * <AUTHOR>
 * @date 2025年5月15日
 */
public interface ILoginInfo {

	/**
     * 获取用户id
     *
     * @return
     */
    int getUserid();

    /**
     * 名称
     *
     * @return
     */
    String getName();

    /**
     * 获取警号
     *
     * @return
     */
    String getPoliceCode();

    /**
     * 获取民警id
     *
     * @return
     */
    String getPoliceId();

    /**
     * 获取民警名称
     *
     * @return
     */
    String getPoliceName();

    /**
     * 获取中队id
     *
     * @return
     */
    String getSquadronId();

    /**
     * 获取中队名称
     *
     * @return
     */
    String getSquadronName();

    /**
     * 获取监所id
     *
     * @return
     */
    String getPrisonId();

    /**
     * 获取监所id
     *
     * @return
     */
    String getPrisonName();

    /**
     * 获取登录用户名
     *
     * @return
     */
    String getUsername();

    /***
     * 获取登录角色id
     * @return
     */
    Integer getRoleId();

    /***
     * 获取登录角色姓名
     * @return
     */
    String getRoleName();

    /**
     * 获取登录用户名
     *
     * @return
     */
//    List<SysRoleVO> getRoleList();

    /***
     * 获取登录用户监所
     * @return
     */
//    List<PrisonInfoVo> getPrisonList();
    
    /**
     * 是否属于支队
     */
    String getIsZd();

    /***
     * 图片
     * @return
     */
    String getPhoto();

    /**
     * 登录方式
     * @return
     */
    String getLoginType();
    
    /**
     * 人员类型
     * @return
     */
    String getPersonnelType();
    
    /**
     * 拓展属性
     * @return
     */
    JSONObject getAttributes ();

    /**
     * 角色岗位
     * @return
     */
    String getRolepost();

    /**
     * 监所类型
     * @return
     */
    Integer getPrisonType();

    /**
     * 监所Id
     * @return
     */
    String getPrisonerId();

    /**
     * 监所名称
     * @return
     */
    String getPrisonerName();
}
