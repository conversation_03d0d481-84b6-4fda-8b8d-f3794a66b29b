package com.rs.adapter.bsp.cons;

/**
 * 基础常量类
 * <AUTHOR>
 * @date 2025年7月10日
 */
public class BaseConstants {

	/** 存储auth 客户端token缓存键值,值为{@value} */
	public static final String CLIENT_CREDENTIALS_EXPIRES = "rs:client_credentials_expires";

	/** BSP存储客户端token目录,值为{@value} */
	public static final String BSP_ACCESS_TOKNE = "access:";
	
	/** bsp用户类型-警员,值为{@value} */
	public static final int BSP_USER_TYPE_POLICE = 0;
	
	/** bsp用户类型-其它,值为{@value} */
	public static final int BSP_USER_TYPE_OTHER = 1;
}
