package com.rs.adapter.bsp.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * 结果集处理工具类
 * <AUTHOR>
 * @date 2025年4月14日
 */
public class ResultUtil {

	/**
	 * 将JSON结果集中指定key的对象转换成指定类
	 * @param <T>
	 * @param json JSONObject JSON结果集
	 * @param key String 指定key
	 * @param itemCls Class 指定类
	 * @return T
	 */
	@SuppressWarnings("unchecked")
	public static <T> T trans(JSONObject json, String key, Class<?> itemCls) {
		JSONObject data = json.getJSONObject(key);
		if(data != null) {
			return (T) JSON.parseObject(data.toJSONString(), itemCls);
		}
		return null;
	}
	
	/**
	 * 将JSON结果集中指定key的数组对象转换成集合
	 * @param <T>
	 * @param json JSONObject JSON结果集
	 * @param key String 指定key
	 * @param itemCls Class 指定类
	 * @return List <T>
	 */
	public static <T> List<T> transList(JSONObject json, String key, Class<T> itemCls) {
		JSONArray data = json.getJSONArray(key);
		if(data != null) {
			return JSON.parseArray(data.toJSONString(), itemCls);
		}
		return null;
	}
}
