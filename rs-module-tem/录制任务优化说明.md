# 录制任务优化说明

## 问题描述
原始的 `grabber.start()` 方法执行缓慢，主要原因包括：
1. 网络连接延迟
2. 流初始化时间长
3. 缺少超时配置
4. 缺少性能优化参数

## 优化方案

### 1. 异步初始化
- 将 `grabber.start()` 放在异步线程中执行
- 主线程立即返回，不阻塞用户操作
- 提供状态检查接口，可以查询初始化进度

### 2. 连接优化
- 添加连接超时配置（10秒）
- 添加读取超时配置（5秒）
- 使用TCP传输协议（适用于RTSP）
- 优化缓冲区设置

### 3. 重试机制
- 最多重试3次
- 递增等待时间（1秒、2秒、3秒）
- 详细的错误日志记录

### 4. 性能监控
- 记录初始化耗时
- 记录录制帧率
- 提供性能报告

## 使用方法

### 1. 启动录制
```http
POST /admin/video/recorder/start
{
    "inputUrl": "rtsp://192.168.1.103:554/stream07",
    "outputPath": "/path/to/output.mp4",
    "talkId": "talk123"
}
```

### 2. 检查初始化状态
```http
GET /admin/video/recorder/status/{taskId}
```

### 3. 停止录制
```http
GET /admin/video/recorder/stop/{taskId}
```

## 配置参数

### FFmpeg优化参数
- `timeout`: 连接超时时间（微秒）
- `stimeout`: 读取超时时间（微秒）
- `rtsp_transport`: 传输协议（tcp/udp）
- `buffer_size`: 缓冲区大小
- `max_delay`: 最大延迟
- `fflags`: 标志设置

### 录制器优化参数
- `preset`: 编码预设（ultrafast）
- `tune`: 调优选项（zerolatency）
- `profile`: 编码配置文件（baseline）
- 自动码率调整（基于分辨率）

## 性能提升

### 优化前
- 初始化时间：10-30秒
- 阻塞主线程
- 无重试机制
- 无性能监控

### 优化后
- 异步初始化：立即返回
- 连接时间：3-10秒（带重试）
- 详细的性能监控
- 自动错误恢复

## 监控指标

### 任务级别指标
- 初始化耗时
- 录制时长
- 录制帧数
- 平均帧率
- 错误信息

### 系统级别指标
- 并发任务数
- 成功率
- 平均性能

## 故障排除

### 常见问题
1. **连接超时**
   - 检查网络连接
   - 验证URL格式
   - 增加超时时间

2. **初始化失败**
   - 查看错误日志
   - 检查流媒体服务器状态
   - 验证认证信息

3. **录制质量问题**
   - 调整码率设置
   - 检查网络带宽
   - 优化编码参数

### 日志分析
- 查看初始化日志
- 监控帧率变化
- 分析错误模式

## 扩展建议

### 1. 连接池
- 复用连接资源
- 减少初始化开销

### 2. 负载均衡
- 分布式录制
- 动态资源分配

### 3. 存储优化
- 分段录制
- 实时上传
- 压缩优化

## 注意事项

1. **资源管理**
   - 及时释放FFmpeg资源
   - 监控内存使用
   - 清理临时文件

2. **并发控制**
   - 限制同时录制任务数
   - 避免资源竞争

3. **错误处理**
   - 完善异常捕获
   - 提供用户友好的错误信息
   - 自动恢复机制
