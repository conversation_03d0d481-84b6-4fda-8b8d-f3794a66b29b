package com.rs.module.tem.controller.admin.video.recorder;

import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;

/**
 * 录制任务测试类
 * 
 * <AUTHOR>
 */
@SpringBootTest
@Log4j2
public class RecordingTaskTest {

    @Test
    public void testAsyncInitialization() {
        String testUrl = "rtsp://192.168.1.103:554/stream07";
        String outputPath = System.getProperty("java.io.tmpdir") + "/test_recording.mp4";
        
        try {
            log.info("开始测试异步初始化");
            
            // 创建录制任务
            long startTime = System.currentTimeMillis();
            RecordingTask task = new RecordingTask(testUrl, outputPath);
            long createTime = System.currentTimeMillis();
            
            log.info("任务创建耗时: {} ms", createTime - startTime);
            
            // 等待初始化完成
            boolean initialized = task.waitForInitialization(30);
            long initTime = System.currentTimeMillis();
            
            log.info("初始化耗时: {} ms", initTime - createTime);
            log.info("初始化结果: {}", initialized);
            
            if (!initialized) {
                Exception error = task.getInitializationError();
                if (error != null) {
                    log.error("初始化失败: {}", error.getMessage(), error);
                }
            }
            
            // 清理
            task.stopRecording();
            
        } catch (Exception e) {
            log.error("测试失败", e);
        } finally {
            // 清理测试文件
            File testFile = new File(outputPath);
            if (testFile.exists()) {
                testFile.delete();
            }
        }
    }
    
    @Test
    public void testInvalidUrl() {
        String invalidUrl = "invalid://test.url";
        String outputPath = System.getProperty("java.io.tmpdir") + "/test_recording.mp4";
        
        try {
            new RecordingTask(invalidUrl, outputPath);
            log.error("应该抛出异常但没有抛出");
        } catch (IllegalArgumentException e) {
            log.info("正确捕获到无效URL异常: {}", e.getMessage());
        } catch (Exception e) {
            log.error("捕获到意外异常", e);
        }
    }
}
