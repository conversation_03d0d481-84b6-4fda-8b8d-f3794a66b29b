package com.rs.module.tem.util;

import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * RTSP连接测试类
 * 
 * <AUTHOR>
 */
@SpringBootTest
@Log4j2
public class RTSPConnectionTest {

    @Test
    public void testRTSPDiagnostic() {
        String rtspUrl = "rtsp://***********:554/stream07";
        
        log.info("开始测试RTSP连接: {}", rtspUrl);
        
        RTSPDiagnosticUtils.DiagnosticResult result = 
            RTSPDiagnosticUtils.diagnoseRTSPConnection(rtspUrl);
        
        String report = result.getFullReport();
        log.info("诊断报告:\n{}", report);
        
        // 断言基本检查
        assert result.getRtspUrl().equals(rtspUrl);
        assert result.getHost().equals("***********");
        assert result.getPort() == 554;
    }
    
    @Test
    public void testMultipleRTSPUrls() {
        String[] testUrls = {
            "rtsp://***********:554/stream07",
            "rtsp://admin:admin@***********:554/stream07",
            "rtsp://***********/stream07"  // 默认端口
        };
        
        for (String url : testUrls) {
            log.info("测试URL: {}", url);
            RTSPDiagnosticUtils.DiagnosticResult result = 
                RTSPDiagnosticUtils.diagnoseRTSPConnection(url);
            log.info("结果: 网络可达={}, FFmpeg可连接={}", 
                    result.isNetworkReachable(), result.isFfmpegConnectable());
        }
    }
}
