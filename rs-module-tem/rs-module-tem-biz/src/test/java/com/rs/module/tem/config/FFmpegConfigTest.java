package com.rs.module.tem.config;

import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

/**
 * FFmpeg配置测试类
 * 
 * <AUTHOR>
 */
@SpringBootTest
@SpringJUnitConfig
@Log4j2
public class FFmpegConfigTest {

    @Test
    public void testFFmpegConfigInitialization() {
        try {
            FFmpegConfig config = new FFmpegConfig();
            config.initFFmpeg();
            log.info("FFmpeg配置测试通过");
        } catch (Exception e) {
            log.error("FFmpeg配置测试失败", e);
            throw e;
        }
    }
}
