CREATE TABLE tem_talk_education (
    id VARCHAR(32) PRIMARY KEY,
    prisoner_id VARCHAR(32) NOT NULL COMMENT '在押人员ID',
    prisoner_name VARCHAR(50) NOT NULL COMMENT '在押人员姓名',
    talker_id VARCHAR(32) NOT NULL COMMENT '谈话人ID',
    talker_name VARCHAR(50) NOT NULL COMMENT '谈话人姓名',
    talk_time TIMESTAMP NOT NULL COMMENT '谈话时间',
    talk_location VARCHAR(100) NOT NULL COMMENT '谈话地点',
    content TEXT NOT NULL COMMENT '谈话内容',
    effect VARCHAR(500) COMMENT '谈话效果',
    remark VARCHAR(500) COMMENT '备注',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(32) COMMENT '创建人',
    update_by VARCHAR(32) COMMENT '更新人',
    deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除'
) COMMENT '谈话教育记录表';

CREATE INDEX idx_prisoner_id ON tem_talk_education(prisoner_id);
CREATE INDEX idx_talker_id ON tem_talk_education(talker_id);
CREATE INDEX idx_talk_time ON tem_talk_education(talk_time); 