package com.rs.module.tem.dao.tag;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.tem.entity.tag.TalkTagDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.tem.controller.admin.tag.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 谈话教育-谈话人员标签库 Dao
*
* <AUTHOR>
*/
@Mapper
public interface TalkTagDao extends IBaseDao<TalkTagDO> {


    default PageResult<TalkTagDO> selectPage(TalkTagPageReqVO reqVO) {
        Page<TalkTagDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<TalkTagDO> wrapper = new LambdaQueryWrapperX<TalkTagDO>()
            .likeIfPresent(TalkTagDO::getTagName, reqVO.getTagName())
            .eqIfPresent(TalkTagDO::getRemarks, reqVO.getRemarks())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(TalkTagDO::getAddTime);
        }
        Page<TalkTagDO> talkTagPage = selectPage(page, wrapper);
        return new PageResult<>(talkTagPage.getRecords(), talkTagPage.getTotal());
    }
    default List<TalkTagDO> selectList(TalkTagListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TalkTagDO>()
            .likeIfPresent(TalkTagDO::getTagName, reqVO.getTagName())
            .eqIfPresent(TalkTagDO::getRemarks, reqVO.getRemarks())
        .orderByDesc(TalkTagDO::getAddTime));    }


    }
