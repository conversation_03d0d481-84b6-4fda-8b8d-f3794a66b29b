package com.rs.module.tem.controller.admin.talk.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 谈话教育-谈话提问助手列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TalkHelperListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("谈话助手内容")
    private String helperContent;

}
