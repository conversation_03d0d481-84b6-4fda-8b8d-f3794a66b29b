package com.rs.module.tem.service.template;

import java.util.*;
import javax.validation.*;
import com.rs.module.tem.controller.admin.template.vo.*;
import com.rs.module.tem.entity.template.TalkTemplateDO;
import com.rs.module.tem.entity.template.TalkTemplateContentDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 谈话教育-谈话模板 Service 接口
 *
 * <AUTHOR>
 */
public interface TalkTemplateService extends IBaseService<TalkTemplateDO>{

    /**
     * 创建谈话教育-谈话模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createTalkTemplate(@Valid TalkTemplateSaveReqVO createReqVO);

    /**
     * 更新谈话教育-谈话模板
     *
     * @param updateReqVO 更新信息
     */
    void updateTalkTemplate(@Valid TalkTemplateSaveReqVO updateReqVO);

    /**
     * 删除谈话教育-谈话模板
     *
     * @param id 编号
     */
    void deleteTalkTemplate(String id);

    /**
     * 获得谈话教育-谈话模板
     *
     * @param id 编号
     * @return 谈话教育-谈话模板
     */
    TalkTemplateDO getTalkTemplate(String id);

    /**
    * 获得谈话教育-谈话模板分页
    *
    * @param pageReqVO 分页查询
    * @return 谈话教育-谈话模板分页
    */
    PageResult<TalkTemplateDO> getTalkTemplatePage(TalkTemplatePageReqVO pageReqVO);

    /**
    * 获得谈话教育-谈话模板列表
    *
    * @param listReqVO 查询条件
    * @return 谈话教育-谈话模板列表
    */
    List<TalkTemplateDO> getTalkTemplateList(TalkTemplateListReqVO listReqVO);


    // ==================== 子表（谈话教育-谈话模板内容） ====================

    /**
     * 获得谈话教育-谈话模板内容列表
     *
     * @param templateId 谈话模板id
     * @return 谈话教育-谈话模板内容列表
     */
    List<TalkTemplateContentDO> getTalkTemplateContentListByTemplateId(String templateId);

}
