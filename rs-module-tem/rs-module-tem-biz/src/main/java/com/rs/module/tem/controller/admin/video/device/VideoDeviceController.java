package com.rs.module.tem.controller.admin.video.device;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.tem.controller.admin.video.vo.VideoDeviceListReqVO;
import com.rs.module.tem.controller.admin.video.vo.VideoDevicePageReqVO;
import com.rs.module.tem.controller.admin.video.vo.VideoDeviceRespVO;
import com.rs.module.tem.controller.admin.video.vo.VideoDeviceSaveReqVO;
import com.rs.module.tem.entity.video.VideoDeviceDO;
import com.rs.module.tem.service.video.VideoDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "谈话教育-终端管理")
@RestController
@RequestMapping("/tem/video/videoDevice")
@Validated
public class VideoDeviceController {

    @Resource
    private VideoDeviceService videoDeviceService;

    @PostMapping("/create")
    @ApiOperation(value = "创建谈话教育-终端管理")
    public CommonResult<String> createVideoDevice(@Valid @RequestBody VideoDeviceSaveReqVO createReqVO) {
        return success(videoDeviceService.createVideoDevice(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新谈话教育-终端管理")
    public CommonResult<Boolean> updateVideoDevice(@Valid @RequestBody VideoDeviceSaveReqVO updateReqVO) {
        videoDeviceService.updateVideoDevice(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除谈话教育-终端管理")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteVideoDevice(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            videoDeviceService.deleteVideoDevice(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得谈话教育-终端管理")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<VideoDeviceRespVO> getVideoDevice(@RequestParam("id") String id) {
        VideoDeviceDO videoDevice = videoDeviceService.getVideoDevice(id);
        return success(BeanUtils.toBean(videoDevice, VideoDeviceRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得谈话教育-终端管理分页")
    public CommonResult<PageResult<VideoDeviceRespVO>> getVideoDevicePage(@Valid @RequestBody VideoDevicePageReqVO pageReqVO) {
        PageResult<VideoDeviceDO> pageResult = videoDeviceService.getVideoDevicePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, VideoDeviceRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得谈话教育-终端管理列表")
    public CommonResult<List<VideoDeviceRespVO>> getVideoDeviceList(@Valid @RequestBody VideoDeviceListReqVO listReqVO) {
        List<VideoDeviceDO> list = videoDeviceService.getVideoDeviceList(listReqVO);
        return success(BeanUtils.toBean(list, VideoDeviceRespVO.class));
    }

    @GetMapping("/getTestVideoUrl")
    @ApiOperation(value = "获得谈话教育终端测试url")
    public CommonResult<String> getTestVideoUrl(String ip) {

        return success(videoDeviceService.getTestVideoUrl(ip));
    }
}
