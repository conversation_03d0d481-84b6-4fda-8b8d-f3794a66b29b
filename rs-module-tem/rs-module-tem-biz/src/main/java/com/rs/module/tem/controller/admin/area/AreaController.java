package com.rs.module.tem.controller.admin.area;

import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.AreaListRespVO;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.base.enums.AreaTypeEnum;
import com.rs.module.base.service.pm.AreaService;
import com.rs.module.base.service.pm.device.DeviceAuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-监管管理-区域")
@RestController
@RequestMapping("/base/area")
@Validated
@Slf4j
public class AreaController {
    @Resource
    private AreaService areaService;
    @Resource
    private DeviceAuthService devAuthService;

    @GetMapping("/getTalkingRoomList")
    @ApiOperation(value = "获得谈话室")
    public CommonResult<List<AreaListRespVO>> getTalkingRoomList() {
        List<AreaListRespVO> areaListByOrgCode = areaService.getAreaListByOrgCode(SessionUserUtil.getSessionUser().getOrgCode(), AreaTypeEnum.TALKING_ROOM.getCode());
        if (areaListByOrgCode.isEmpty()) {
            AreaDO ys = areaService.getById("ys");
            areaListByOrgCode.add(BeanUtils.toBean(ys, AreaListRespVO.class));
        }
        return success(areaListByOrgCode);
    }


}
