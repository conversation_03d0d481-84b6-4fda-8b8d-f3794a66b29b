package com.rs.module.tem.service.video.recorder;

import com.rs.module.tem.controller.admin.video.recorder.RecordingTask;
import com.rs.module.tem.service.talk.TalkRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;

@Service
public class VideoRecorderServiceImpl implements VideoRecorderService {

    private final ConcurrentHashMap<String, RecordingTask> tasks = new ConcurrentHashMap<>();

    @Autowired
    @Qualifier("recorderThreadPool")
    private Executor executor;

    @Autowired
    private TalkRecordService talkRecordService;

    @Override
    public RecordingTask startRecording(String inputUrl, String outputPath, String talkId) {
        File outputFile = new File(outputPath);
        File parentDir = outputFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {

            parentDir.mkdirs();
        }

        RecordingTask task = new RecordingTask(inputUrl, outputPath);
        if (talkId != null && !talkId.isEmpty()) {
            task.setTalkId(talkId); // 假设 RecordingTask 支持设置 talkId
        }

        tasks.put(task.getTaskId(), task);
        task.setRunning(true);

        executor.execute(task);

        return task;
    }

    @Override
    public boolean stopRecording(String taskId) {
        RecordingTask task = tasks.get(taskId);
        if (task == null || !task.isRunning()) {
            return false;
        }

        task.stopRecording();
        tasks.remove(taskId);
        return true;
    }

    @Override
    public boolean sendHeartbeat(String taskId) {
        RecordingTask task = tasks.get(taskId);
        if (task == null) {
            return false;
        }

        task.updateHeartbeat();
        return true;
    }

    @Override
    public Optional<RecordingTask> getTask(String taskId) {
        return Optional.ofNullable(tasks.get(taskId));
    }

    @Override
    public List<RecordingTask> getAllTasks() {
        return new ArrayList<>(tasks.values());
    }

    @Override
    public boolean isTaskInitialized(String taskId) {
        RecordingTask task = tasks.get(taskId);
        return task != null && task.isInitialized();
    }

    @Override
    @Scheduled(fixedRate = 20000)
    public void checkHeartbeats() {
        tasks.forEach((taskId, task) -> {
            if (task.isHeartbeatTimeout(10)) {
                stopRecording(taskId);
            }
        });
    }
}
