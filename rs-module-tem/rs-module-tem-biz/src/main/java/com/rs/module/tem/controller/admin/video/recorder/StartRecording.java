package com.rs.module.tem.controller.admin.video.recorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;


/**
 * @ClassName StartRecording
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/6/11 20:05
 * @Version 1.0
 */
@Data
@ApiModel("开始录像请求参数")
public class StartRecording {
    @ApiModelProperty(value = "输入流地址", required = true)
    @NotNull
    @NotEmpty
    public String inputUrl;

    @ApiModelProperty(value = "输出路径（保留字段，非必填）", required = true)
    public String outputPath;

    @ApiModelProperty(value = "谈话ID", required = true)
    public String talkId;
}
