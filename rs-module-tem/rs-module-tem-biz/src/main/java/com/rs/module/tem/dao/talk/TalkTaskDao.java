package com.rs.module.tem.dao.talk;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.tem.entity.talk.TalkTaskDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.tem.controller.admin.talk.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 谈话教育-谈话任务 Dao
*
* <AUTHOR>
*/
@Mapper
public interface TalkTaskDao extends IBaseDao<TalkTaskDO> {


    default PageResult<TalkTaskDO> selectPage(TalkTaskPageReqVO reqVO) {
        Page<TalkTaskDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<TalkTaskDO> wrapper = new LambdaQueryWrapperX<TalkTaskDO>()
            .eqIfPresent(TalkTaskDO::getTalkCode, reqVO.getTalkCode())
            .eqIfPresent(TalkTaskDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(TalkTaskDO::getRyxm, reqVO.getRyxm())
            .eqIfPresent(TalkTaskDO::getTalkReason, reqVO.getTalkReason())
            .eqIfPresent(TalkTaskDO::getTalkUserSfzh, reqVO.getTalkUserSfzh())
            .likeIfPresent(TalkTaskDO::getTalkUserName, reqVO.getTalkUserName())
            .eqIfPresent(TalkTaskDO::getJsh, reqVO.getJsh())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(TalkTaskDO::getAddTime);
        }
        Page<TalkTaskDO> talkTaskPage = selectPage(page, wrapper);
        return new PageResult<>(talkTaskPage.getRecords(), talkTaskPage.getTotal());
    }
    default List<TalkTaskDO> selectList(TalkTaskListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TalkTaskDO>()
            .eqIfPresent(TalkTaskDO::getTalkCode, reqVO.getTalkCode())
            .eqIfPresent(TalkTaskDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(TalkTaskDO::getRyxm, reqVO.getRyxm())
            .eqIfPresent(TalkTaskDO::getTalkReason, reqVO.getTalkReason())
            .eqIfPresent(TalkTaskDO::getTalkUserSfzh, reqVO.getTalkUserSfzh())
            .likeIfPresent(TalkTaskDO::getTalkUserName, reqVO.getTalkUserName())
            .eqIfPresent(TalkTaskDO::getJsh, reqVO.getJsh())
        .orderByDesc(TalkTaskDO::getAddTime));    }


    }
