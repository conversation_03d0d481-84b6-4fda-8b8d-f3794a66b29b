package com.rs.module.tem.controller.admin.video.recorder;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.tem.util.RTSPDiagnosticUtils;
import com.rs.module.tem.util.EncoderTestUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.*;

/**
 * RTSP诊断控制器
 * 
 * <AUTHOR>
 */
@Api(tags = "RTSP诊断")
@RestController
@RequestMapping("/admin/video/diagnostic")
@Log4j2
public class DiagnosticController {

    /**
     * 诊断RTSP连接
     *
     * @param rtspUrl RTSP URL
     * @return 诊断结果
     */
    @ApiOperation(value = "诊断RTSP连接")
    @GetMapping("/rtsp")
    public CommonResult<String> diagnoseRTSP(
            @ApiParam(value = "RTSP URL", required = true, example = "rtsp://***********:554/stream07")
            @RequestParam String rtspUrl) {
        try {
            log.info("开始诊断RTSP连接: {}", rtspUrl);
            
            RTSPDiagnosticUtils.DiagnosticResult result = RTSPDiagnosticUtils.diagnoseRTSPConnection(rtspUrl);
            String report = result.getFullReport();
            
            log.info("RTSP诊断完成:\n{}", report);
            
            return CommonResult.success(report);
            
        } catch (Exception e) {
            log.error("RTSP诊断失败", e);
            return CommonResult.error("诊断失败: " + e.getMessage());
        }
    }

    /**
     * 快速测试RTSP连接
     *
     * @param rtspUrl RTSP URL
     * @return 连接状态
     */
    @ApiOperation(value = "快速测试RTSP连接")
    @GetMapping("/rtsp/quick")
    public CommonResult<Boolean> quickTestRTSP(
            @ApiParam(value = "RTSP URL", required = true)
            @RequestParam String rtspUrl) {
        try {
            RTSPDiagnosticUtils.DiagnosticResult result = RTSPDiagnosticUtils.diagnoseRTSPConnection(rtspUrl);
            boolean success = result.isFfmpegConnectable();
            
            return CommonResult.success(success);
            
        } catch (Exception e) {
            log.error("快速测试失败", e);
            return CommonResult.error("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试可用的编码器
     *
     * @return 可用编码器列表
     */
    @ApiOperation(value = "测试可用编码器")
    @GetMapping("/encoders")
    public CommonResult<String> testEncoders() {
        try {
            log.info("开始测试可用编码器");

            String[] availableEncoders = EncoderTestUtils.getAvailableEncoders();

            StringBuilder result = new StringBuilder();
            result.append("=== 编码器测试结果 ===\n");

            if (availableEncoders.length > 0) {
                result.append("可用编码器:\n");
                for (String encoder : availableEncoders) {
                    result.append("- ").append(encoder).append("\n");
                }
            } else {
                result.append("警告: 没有找到可用的编码器\n");
            }

            return CommonResult.success(result.toString());

        } catch (Exception e) {
            log.error("编码器测试失败", e);
            return CommonResult.error("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试录制器创建
     *
     * @return 测试结果
     */
    @ApiOperation(value = "测试录制器创建")
    @GetMapping("/recorder")
    public CommonResult<String> testRecorderCreation() {
        try {
            log.info("开始测试录制器创建");

            String testPath = System.getProperty("java.io.tmpdir") + "/encoder_test.mp4";

            org.bytedeco.javacv.FFmpegFrameRecorder testRecorder =
                EncoderTestUtils.createCompatibleRecorder(testPath, 640, 480, 25.0);

            if (testRecorder != null) {
                testRecorder.stop();
                testRecorder.release();

                // 删除测试文件
                java.io.File testFile = new java.io.File(testPath);
                if (testFile.exists()) {
                    testFile.delete();
                }

                return CommonResult.success("录制器创建成功！可以正常录制视频。");
            } else {
                return CommonResult.error("录制器创建失败！所有编码器配置都不可用。");
            }

        } catch (Exception e) {
            log.error("录制器测试失败", e);
            return CommonResult.error("测试失败: " + e.getMessage());
        }
    }
}
