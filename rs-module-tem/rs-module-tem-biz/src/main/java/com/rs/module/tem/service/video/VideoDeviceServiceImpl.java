package com.rs.module.tem.service.video;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.BizException;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.tem.controller.admin.video.vo.VideoDeviceListReqVO;
import com.rs.module.tem.controller.admin.video.vo.VideoDevicePageReqVO;
import com.rs.module.tem.controller.admin.video.vo.VideoDeviceSaveReqVO;
import com.rs.module.tem.dao.video.VideoDeviceDao;
import com.rs.module.tem.entity.video.VideoDeviceDO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 谈话教育-终端管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class VideoDeviceServiceImpl extends BaseServiceImpl<VideoDeviceDao, VideoDeviceDO> implements VideoDeviceService {

    @Value("${conf.video.stream:1985/rtc/v1/whep/?app=live&stream=living}")
    private String videoStream;

    @Resource
    private VideoDeviceDao videoDeviceDao;

    @Override
    public String createVideoDevice(VideoDeviceSaveReqVO createReqVO) {
        checkUnique(createReqVO, createReqVO.getId());
        // 插入
        VideoDeviceDO videoDevice = BeanUtils.toBean(createReqVO, VideoDeviceDO.class);
        videoDeviceDao.insert(videoDevice);
        // 返回
        return videoDevice.getId();
    }

    @Override
    public void updateVideoDevice(VideoDeviceSaveReqVO updateReqVO) {
        // 校验存在
        validateVideoDeviceExists(updateReqVO.getId());
        // 更新
        VideoDeviceDO updateObj = BeanUtils.toBean(updateReqVO, VideoDeviceDO.class);
        videoDeviceDao.updateById(updateObj);
    }

    @Override
    public void deleteVideoDevice(String id) {
        // 校验存在
        validateVideoDeviceExists(id);
        // 删除
        videoDeviceDao.deleteById(id);
    }

    private void validateVideoDeviceExists(String id) {
        if (videoDeviceDao.selectById(id) == null) {
            throw new ServerException("谈话教育-终端管理数据不存在");
        }
    }

    @Override
    public VideoDeviceDO getVideoDevice(String id) {
        return videoDeviceDao.selectById(id);
    }

    @Override
    public PageResult<VideoDeviceDO> getVideoDevicePage(VideoDevicePageReqVO pageReqVO) {
        return videoDeviceDao.selectPage(pageReqVO);
    }

    @Override
    public List<VideoDeviceDO> getVideoDeviceList(VideoDeviceListReqVO listReqVO) {
        return videoDeviceDao.selectList(listReqVO);
    }

    private void checkUnique(VideoDeviceSaveReqVO dto, String id) {
        Integer count = videoDeviceDao.selectCount(
                Wrappers.lambdaQuery(VideoDeviceDO.class)
                        .eq(VideoDeviceDO::getTalkRoomId, dto.getTalkRoomId())
                        .ne(StrUtil.isNotBlank(id), VideoDeviceDO::getId, id));
        if (count > 0) {
            throw new BizException(dto.getTalkRoomName() + "已绑定设备！不能重复绑定");
        }
    }


    @Override
    public String getTestVideoUrl(String ip) {
        return "http://" + ip + ":" + videoStream;
    }

    @Override
    public VideoDeviceDO getVideoDeviceByRoomId(String roomId) {

        return lambdaQuery().eq(VideoDeviceDO::getTalkRoomId, roomId)
                .one();
    }
}
