package com.rs.module.tem.util;

import lombok.extern.log4j.Log4j2;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FFmpegFrameRecorder;

/**
 * FFmpeg工具类
 * 
 * <AUTHOR>
 */
@Log4j2
public class FFmpegUtils {

    /**
     * 配置FFmpeg抓取器的优化选项
     *
     * @param grabber FFmpeg抓取器
     * @param connectionTimeoutSeconds 连接超时时间（秒）
     * @param readTimeoutSeconds 读取超时时间（秒）
     */
    public static void configureGrabberOptions(FFmpegFrameGrabber grabber,
                                             int connectionTimeoutSeconds,
                                             int readTimeoutSeconds) {
        try {
            // 设置网络超时（微秒） - 使用新的参数名
            grabber.setOption("stimeout", String.valueOf(connectionTimeoutSeconds * 1000000L));

            // 设置读取超时（微秒）
            grabber.setOption("rw_timeout", String.valueOf(readTimeoutSeconds * 1000000L));

            // RTSP特定优化选项
            configureRTSPOptions(grabber);

            // 网络优化选项
            grabber.setOption("buffer_size", "1024000"); // 设置缓冲区大小1MB
            grabber.setOption("max_delay", "500000"); // 最大延迟500ms
            grabber.setOption("fflags", "nobuffer"); // 禁用缓冲以减少延迟

            // 重连选项
            grabber.setOption("reconnect", "1"); // 启用自动重连
            grabber.setOption("reconnect_at_eof", "1"); // 在EOF时重连
            grabber.setOption("reconnect_streamed", "1"); // 对流媒体启用重连
            grabber.setOption("reconnect_delay_max", "2"); // 最大重连延迟2秒

            log.debug("FFmpeg抓取器选项配置完成");
        } catch (Exception e) {
            log.warn("配置FFmpeg抓取器选项时出现警告: {}", e.getMessage());
        }
    }

    /**
     * 配置RTSP特定选项
     *
     * @param grabber FFmpeg抓取器
     */
    public static void configureRTSPOptions(FFmpegFrameGrabber grabber) {
        try {
            // 优先使用TCP传输，更稳定
            grabber.setOption("rtsp_transport", "tcp");

            // RTSP标志设置 - 强制客户端模式
            grabber.setOption("rtsp_flags", "prefer_tcp");

            // 用户代理设置
            grabber.setOption("user_agent", "JavaCV-RTSP-Client");

            // 允许的媒体类型
            grabber.setOption("allowed_media_types", "video+audio");

            // 重排序缓冲区大小
            grabber.setOption("reorder_queue_size", "0");

            // 强制客户端模式，避免监听模式
            grabber.setOption("listen", "0");

            // 禁用服务器模式
            grabber.setOption("rtsp_listen", "0");

            log.debug("RTSP特定选项配置完成");
        } catch (Exception e) {
            log.warn("配置RTSP选项时出现警告: {}", e.getMessage());
        }
    }

    /**
     * 尝试不同的传输协议连接RTSP
     *
     * @param grabber FFmpeg抓取器
     * @param connectionTimeoutSeconds 连接超时时间（秒）
     * @param readTimeoutSeconds 读取超时时间（秒）
     * @return 成功的传输协议，如果都失败则返回null
     */
    public static String tryDifferentTransports(FFmpegFrameGrabber grabber,
                                              int connectionTimeoutSeconds,
                                              int readTimeoutSeconds) {
        String[] transports = {"tcp", "udp", "http"};

        for (String transport : transports) {
            try {
                log.info("尝试使用传输协议: {}", transport);

                // 重新配置选项 - 使用新的参数名
                grabber.setOption("stimeout", String.valueOf(connectionTimeoutSeconds * 1000000L));
                grabber.setOption("rw_timeout", String.valueOf(readTimeoutSeconds * 1000000L));
                grabber.setOption("rtsp_transport", transport);

                // 强制客户端模式
                grabber.setOption("listen", "0");
                grabber.setOption("rtsp_listen", "0");

                // 根据传输协议调整其他选项
                if ("tcp".equals(transport)) {
                    grabber.setOption("rtsp_flags", "prefer_tcp");
                } else if ("udp".equals(transport)) {
                    grabber.setOption("rtsp_flags", "");
                    grabber.setOption("max_delay", "500000");
                }

                grabber.start();
                log.info("使用传输协议 {} 连接成功", transport);
                return transport;

            } catch (Exception e) {
                log.warn("传输协议 {} 连接失败: {}", transport, e.getMessage());
                try {
                    grabber.stop();
                } catch (Exception ignored) {}
            }
        }

        return null;
    }

    /**
     * 针对特定RTSP设备的优化配置
     *
     * @param grabber FFmpeg抓取器
     * @param connectionTimeoutSeconds 连接超时时间（秒）
     * @param readTimeoutSeconds 读取超时时间（秒）
     */
    public static void configureForSpecificDevice(FFmpegFrameGrabber grabber,
                                                int connectionTimeoutSeconds,
                                                int readTimeoutSeconds) {
        try {
            // 基础超时设置
            grabber.setOption("stimeout", String.valueOf(connectionTimeoutSeconds * 1000000L));
            grabber.setOption("rw_timeout", String.valueOf(readTimeoutSeconds * 1000000L));

            // 强制TCP传输
            grabber.setOption("rtsp_transport", "tcp");

            // 客户端模式设置
            grabber.setOption("listen", "0");
            grabber.setOption("rtsp_listen", "0");
            grabber.setOption("rtsp_flags", "prefer_tcp");

            // 用户代理
            grabber.setOption("user_agent", "JavaCV-RTSP-Client/1.0");

            // 缓冲区设置
            grabber.setOption("buffer_size", "1048576"); // 1MB
            grabber.setOption("max_delay", "500000"); // 500ms

            // 禁用不必要的功能
            grabber.setOption("fflags", "nobuffer+fastseek");
            grabber.setOption("flags", "low_delay");

            // 重连设置
            grabber.setOption("reconnect", "1");
            grabber.setOption("reconnect_at_eof", "1");
            grabber.setOption("reconnect_streamed", "1");
            grabber.setOption("reconnect_delay_max", "2");

            log.info("特定设备优化配置完成");
        } catch (Exception e) {
            log.warn("配置特定设备选项时出现警告: {}", e.getMessage());
        }
    }

    /**
     * 配置FFmpeg录制器的优化选项
     *
     * @param recorder FFmpeg录制器
     * @param videoBitrate 视频码率
     */
    public static void configureRecorderOptions(FFmpegFrameRecorder recorder, int videoBitrate) {
        try {
            // 视频编码优化 - 使用x264编码器的正确参数
            recorder.setVideoOption("preset", "ultrafast"); // 快速编码预设
            recorder.setVideoOption("tune", "zerolatency"); // 零延迟调优

            // 修复profile设置 - 使用正确的格式
            recorder.setVideoOption("profile:v", "baseline"); // 视频profile
            // 或者使用数字格式
            // recorder.setVideoOption("profile", "66"); // baseline = 66

            // 设置码率
            recorder.setVideoBitrate(videoBitrate);

            // GOP设置
            recorder.setGopSize(30); // 关键帧间隔

            // 像素格式设置
            recorder.setPixelFormat(org.bytedeco.ffmpeg.global.avutil.AV_PIX_FMT_YUV420P);

            log.debug("FFmpeg录制器选项配置完成，视频码率: {} bps", videoBitrate);
        } catch (Exception e) {
            log.warn("配置FFmpeg录制器选项时出现警告: {}", e.getMessage());
        }
    }

    /**
     * 检查输入URL的格式
     * 
     * @param inputUrl 输入URL
     * @return 是否为有效的流媒体URL
     */
    public static boolean isValidStreamUrl(String inputUrl) {
        if (inputUrl == null || inputUrl.trim().isEmpty()) {
            return false;
        }
        
        String url = inputUrl.toLowerCase().trim();
        return url.startsWith("rtsp://") || 
               url.startsWith("rtmp://") || 
               url.startsWith("http://") || 
               url.startsWith("https://") ||
               url.startsWith("udp://") ||
               url.startsWith("tcp://");
    }

    /**
     * 获取推荐的视频码率（基于分辨率）
     * 
     * @param width 视频宽度
     * @param height 视频高度
     * @return 推荐的码率（bps）
     */
    public static int getRecommendedBitrate(int width, int height) {
        int pixels = width * height;

        if (pixels <= 640 * 480) { // 480p
            return 1000000; // 1Mbps
        } else if (pixels <= 1280 * 720) { // 720p
            return 2000000; // 2Mbps
        } else if (pixels <= 1920 * 1080) { // 1080p
            return 4000000; // 4Mbps
        } else { // 4K或更高
            return 8000000; // 8Mbps
        }
    }

    /**
     * 配置兼容性更好的录制器选项
     *
     * @param recorder FFmpeg录制器
     * @param videoBitrate 视频码率
     */
    public static void configureCompatibleRecorderOptions(FFmpegFrameRecorder recorder, int videoBitrate) {
        try {
            // 使用更兼容的编码设置
            recorder.setVideoOption("preset", "medium"); // 使用medium预设，更稳定
            recorder.setVideoOption("crf", "23"); // 恒定质量因子

            // 设置码率
            recorder.setVideoBitrate(videoBitrate);
            recorder.setVideoOption("maxrate", String.valueOf(videoBitrate));
            recorder.setVideoOption("bufsize", String.valueOf(videoBitrate * 2));

            // GOP设置
            recorder.setGopSize(50); // 增大GOP，提高压缩效率

            // 像素格式
            recorder.setPixelFormat(org.bytedeco.ffmpeg.global.avutil.AV_PIX_FMT_YUV420P);

            // 不设置可能有问题的profile选项
            log.debug("兼容性录制器选项配置完成，视频码率: {} bps", videoBitrate);
        } catch (Exception e) {
            log.warn("配置兼容性录制器选项时出现警告: {}", e.getMessage());
        }
    }

    /**
     * 配置简化的录制器选项（最小配置）
     *
     * @param recorder FFmpeg录制器
     * @param videoBitrate 视频码率
     */
    public static void configureMinimalRecorderOptions(FFmpegFrameRecorder recorder, int videoBitrate) {
        try {
            // 最简化的配置，避免兼容性问题
            recorder.setVideoBitrate(videoBitrate);
            recorder.setGopSize(25);
            recorder.setPixelFormat(org.bytedeco.ffmpeg.global.avutil.AV_PIX_FMT_YUV420P);

            log.debug("简化录制器选项配置完成，视频码率: {} bps", videoBitrate);
        } catch (Exception e) {
            log.warn("配置简化录制器选项时出现警告: {}", e.getMessage());
        }
    }
}
