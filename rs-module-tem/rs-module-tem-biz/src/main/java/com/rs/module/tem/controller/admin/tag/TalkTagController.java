package com.rs.module.tem.controller.admin.tag;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.tem.controller.admin.tag.vo.TalkTagListReqVO;
import com.rs.module.tem.controller.admin.tag.vo.TalkTagRespVO;
import com.rs.module.tem.controller.admin.tag.vo.TalkTagSaveReqVO;
import com.rs.module.tem.entity.tag.TalkTagDO;
import com.rs.module.tem.service.tag.TalkTagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 谈话教育-谈话人员标签库")
@RestController
@RequestMapping("/tem/tag/talkTag")
@Validated
public class TalkTagController {

    @Resource
    private TalkTagService talkTagService;

    @PostMapping("/create")
    @ApiOperation(value = "创建谈话教育-谈话人员标签库")
    @LogRecordAnnotation(bizModule = "tem:talkTag:create", operateType = LogOperateType.CREATE, title = "创建谈话教育-谈话人员标签库",
    success = "创建谈话教育-谈话人员标签库成功", fail = "创建谈话教育-谈话人员标签库失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createTalkTag(@Valid @RequestBody TalkTagSaveReqVO createReqVO) {
        return success(talkTagService.createTalkTag(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新谈话教育-谈话人员标签库")
    @LogRecordAnnotation(bizModule = "tem:talkTag:update", operateType = LogOperateType.UPDATE, title = "更新谈话教育-谈话人员标签库",
    success = "更新谈话教育-谈话人员标签库成功", fail = "更新谈话教育-谈话人员标签库失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateTalkTag(@Valid @RequestBody TalkTagSaveReqVO updateReqVO) {
        talkTagService.updateTalkTag(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除谈话教育-谈话人员标签库")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "tem:talkTag:delete", operateType = LogOperateType.DELETE, title = "删除谈话教育-谈话人员标签库",
    success = "删除谈话教育-谈话人员标签库成功", fail = "删除谈话教育-谈话人员标签库失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteTalkTag(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           talkTagService.deleteTalkTag(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得谈话教育-谈话人员标签库")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<TalkTagRespVO> getTalkTag(@RequestParam("id") String id) {
        TalkTagDO talkTag = talkTagService.getTalkTag(id);
        return success(BeanUtils.toBean(talkTag, TalkTagRespVO.class));
    }

//    @PostMapping("/page")
//    @ApiOperation(value = "获得谈话教育-谈话人员标签库分页")
//    public CommonResult<PageResult<TalkTagRespVO>> getTalkTagPage(@Valid @RequestBody TalkTagPageReqVO pageReqVO) {
//        PageResult<TalkTagDO> pageResult = talkTagService.getTalkTagPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, TalkTagRespVO.class));
//    }
//
    @PostMapping("/list")
    @ApiOperation(value = "获得谈话教育-谈话人员标签库列表")
    public CommonResult<List<TalkTagRespVO>> getTalkTagList(@Valid @RequestBody TalkTagListReqVO listReqVO) {
        List<TalkTagDO> list = talkTagService.getTalkTagList(listReqVO);
        return success(BeanUtils.toBean(list, TalkTagRespVO.class));
    }
}
