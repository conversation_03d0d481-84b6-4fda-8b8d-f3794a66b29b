package com.rs.module.tem.service;

import com.rs.adapter.bsp.api.BspApi;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @ClassName ConmonService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/18 15:41
 * @Version 1.0
 */
@Service
@AllArgsConstructor
public class ConmonService {
    public final BspApi bspApi;

    //获取序列号
    public String getSerialNumber(String ruleCode) {
        return bspApi.executeByRuleCode(ruleCode, null);
    }
}
