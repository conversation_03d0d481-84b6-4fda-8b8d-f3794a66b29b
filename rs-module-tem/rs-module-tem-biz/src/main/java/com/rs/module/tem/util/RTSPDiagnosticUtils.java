package com.rs.module.tem.util;

import lombok.extern.log4j.Log4j2;
import org.bytedeco.javacv.FFmpegFrameGrabber;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketTimeoutException;

/**
 * RTSP诊断工具类
 *
 * <AUTHOR>
 */
@Log4j2
public class RTSPDiagnosticUtils {

    /**
     * 诊断RTSP连接问题
     *
     * @param rtspUrl RTSP URL
     * @return 诊断结果
     */
    public static DiagnosticResult diagnoseRTSPConnection(String rtspUrl) {
        DiagnosticResult result = new DiagnosticResult();
        result.setRtspUrl(rtspUrl);

        try {
            // 1. 解析URL
            if (!parseRTSPUrl(rtspUrl, result)) {
                return result;
            }

            // 2. 测试网络连接
            testNetworkConnection(result);

            // 3. 测试RTSP连接
            testRTSPConnection(result);

            // 4. 测试FFmpeg连接
            testFFmpegConnection(result);

        } catch (Exception e) {
            result.addError("诊断过程异常: " + e.getMessage());
            log.error("RTSP诊断异常", e);
        }

        return result;
    }

    /**
     * 解析RTSP URL
     */
    private static boolean parseRTSPUrl(String rtspUrl, DiagnosticResult result) {
        try {
            if (!rtspUrl.toLowerCase().startsWith("rtsp://")) {
                result.addError("URL格式错误: 不是有效的RTSP URL");
                return false;
            }

            // 解析主机和端口
            String urlPart = rtspUrl.substring(7); // 移除 "rtsp://"
            String[] parts = urlPart.split("/");
            String hostPort = parts[0];

            if (hostPort.contains(":")) {
                String[] hostPortParts = hostPort.split(":");
                result.setHost(hostPortParts[0]);
                result.setPort(Integer.parseInt(hostPortParts[1]));
            } else {
                result.setHost(hostPort);
                result.setPort(554); // RTSP默认端口
            }

            result.addInfo("URL解析成功 - 主机: " + result.getHost() + ", 端口: " + result.getPort());
            return true;

        } catch (Exception e) {
            result.addError("URL解析失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 测试网络连接
     */
    private static void testNetworkConnection(DiagnosticResult result) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(result.getHost(), result.getPort()), 5000);
            result.addInfo("网络连接测试成功");
            result.setNetworkReachable(true);
        } catch (SocketTimeoutException e) {
            result.addError("网络连接超时: " + e.getMessage());
        } catch (IOException e) {
            result.addError("网络连接失败: " + e.getMessage());
        }
    }

    /**
     * 测试RTSP连接
     */
    private static void testRTSPConnection(DiagnosticResult result) {
        // 这里可以添加更详细的RTSP协议测试
        // 暂时跳过，因为需要实现RTSP协议细节
        result.addInfo("RTSP协议测试跳过（需要详细实现）");
    }

    /**
     * 测试FFmpeg连接
     */
    private static void testFFmpegConnection(DiagnosticResult result) {
        FFmpegFrameGrabber grabber = null;
        try {
            grabber = new FFmpegFrameGrabber(result.getRtspUrl());

            // 设置较短的超时时间进行测试 - 使用新的参数名
            grabber.setOption("stimeout", "5000000"); // 5秒连接超时
            grabber.setOption("rw_timeout", "3000000"); // 3秒读写超时

            // 尝试不同的传输协议
            String[] transports = {"tcp", "udp"};

            for (String transport : transports) {
                try {
                    grabber.setOption("rtsp_transport", transport);

                    // 强制客户端模式，避免监听模式错误
                    grabber.setOption("listen", "0");
                    grabber.setOption("rtsp_listen", "0");
                    grabber.setOption("rtsp_flags", "prefer_tcp");

                    long startTime = System.currentTimeMillis();
                    grabber.start();
                    long endTime = System.currentTimeMillis();

                    result.addInfo("FFmpeg连接成功 (传输协议: " + transport + ", 耗时: " + (endTime - startTime) + "ms)");
                    result.setFfmpegConnectable(true);
                    result.setWorkingTransport(transport);

                    // 获取流信息
                    result.addInfo("视频尺寸: " + grabber.getImageWidth() + "x" + grabber.getImageHeight());
                    result.addInfo("帧率: " + grabber.getFrameRate());
                    result.addInfo("音频通道: " + grabber.getAudioChannels());

                    grabber.stop();
                    return; // 成功则退出

                } catch (Exception e) {
                    result.addError("FFmpeg连接失败 (传输协议: " + transport + "): " + e.getMessage());
                    try {
                        grabber.stop();
                    } catch (Exception ignored) {}
                }
            }

        } catch (Exception e) {
            result.addError("FFmpeg测试异常: " + e.getMessage());
        } finally {
            if (grabber != null) {
                try {
                    grabber.release();
                } catch (Exception ignored) {}
            }
        }
    }

    /**
     * 诊断结果类
     */
    public static class DiagnosticResult {
        private String rtspUrl;
        private String host;
        private int port;
        private boolean networkReachable = false;
        private boolean ffmpegConnectable = false;
        private String workingTransport;
        private StringBuilder info = new StringBuilder();
        private StringBuilder errors = new StringBuilder();

        public void addInfo(String message) {
            info.append("[INFO] ").append(message).append("\n");
            log.info(message);
        }

        public void addError(String message) {
            errors.append("[ERROR] ").append(message).append("\n");
            log.error(message);
        }

        public String getFullReport() {
            StringBuilder report = new StringBuilder();
            report.append("=== RTSP连接诊断报告 ===\n");
            report.append("URL: ").append(rtspUrl).append("\n");
            report.append("主机: ").append(host).append("\n");
            report.append("端口: ").append(port).append("\n");
            report.append("网络可达: ").append(networkReachable).append("\n");
            report.append("FFmpeg可连接: ").append(ffmpegConnectable).append("\n");
            if (workingTransport != null) {
                report.append("工作传输协议: ").append(workingTransport).append("\n");
            }
            report.append("\n--- 详细信息 ---\n");
            report.append(info.toString());
            if (errors.length() > 0) {
                report.append("\n--- 错误信息 ---\n");
                report.append(errors.toString());
            }
            return report.toString();
        }

        // Getters and Setters
        public String getRtspUrl() { return rtspUrl; }
        public void setRtspUrl(String rtspUrl) { this.rtspUrl = rtspUrl; }
        public String getHost() { return host; }
        public void setHost(String host) { this.host = host; }
        public int getPort() { return port; }
        public void setPort(int port) { this.port = port; }
        public boolean isNetworkReachable() { return networkReachable; }
        public void setNetworkReachable(boolean networkReachable) { this.networkReachable = networkReachable; }
        public boolean isFfmpegConnectable() { return ffmpegConnectable; }
        public void setFfmpegConnectable(boolean ffmpegConnectable) { this.ffmpegConnectable = ffmpegConnectable; }
        public String getWorkingTransport() { return workingTransport; }
        public void setWorkingTransport(String workingTransport) { this.workingTransport = workingTransport; }
    }
}
