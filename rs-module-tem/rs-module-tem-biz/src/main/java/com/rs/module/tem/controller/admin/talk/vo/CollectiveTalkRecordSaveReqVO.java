package com.rs.module.tem.controller.admin.talk.vo;

import com.rs.framework.common.annotation.DefaultValueDate;
import com.rs.framework.common.annotation.DefaultValueString;
import com.rs.framework.common.annotation.SessionUserIdCard;
import com.rs.framework.common.annotation.SessionUserName;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.tem.service.ConmonService;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 谈话教育-集体谈话教育新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CollectiveTalkRecordSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("谈话记录编号(规则：T+9位单位编号+8位日期+6位序号)")
    @DefaultValueString(service = ConmonService.class, method = "getSerialNumber",staticParam = "thjl_no")
    private String talkCode;

    @ApiModelProperty("集体谈话教育的时间段-开始")
    @NotNull(message = "集体谈话教育的时间段不能为空")
    private Date talkEduStartTime;

    @ApiModelProperty("集体谈话教育的时间段-结束")
    @NotNull(message = "集体谈话教育的时间段不能为空")
    private Date talkEduEndTime;

    @ApiModelProperty("集体谈话教育所涉及的范围")
    private String talkEduScope;

    @ApiModelProperty("进行授课的人员姓名")
    @NotEmpty(message = "进行授课的人员姓名不能为空")
    private String instructorName;

    @ApiModelProperty("授课人的职务")
    private String instructorPosition;

    @ApiModelProperty("接受集体谈话教育的人数")
    @NotNull(message = "接受集体谈话教育的人数不能为空")
    private Integer numberOfRecipients;

    @ApiModelProperty("集体谈话教育的课题")
    @NotEmpty(message = "集体谈话教育的课题不能为空")
    private String talkEduTopic;

    @ApiModelProperty("授课内容的摘要")
    @NotEmpty(message = "授课内容的摘要不能为空")
    private String summaryOfContent;

    @ApiModelProperty("在押人员对集体谈话教育的反应")
    @NotEmpty(message = "在押人员对集体谈话教育的反应不能为空")
    private String detaineesResponse;

    @SessionUserIdCard
    @ApiModelProperty("登记经办人")
    private String operatorSfzh;

    @SessionUserName
    @ApiModelProperty("登记经办人姓名")
    private String operatorXm;

    @DefaultValueDate
    @ApiModelProperty("办理会见登记的时间")
    private Date operatorTime;

}
