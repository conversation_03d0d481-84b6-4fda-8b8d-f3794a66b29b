package com.rs.module.tem.controller.admin.talk.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 谈话教育-谈话提问助手分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TalkHelperPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("谈话助手内容")
    private String helperContent;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
