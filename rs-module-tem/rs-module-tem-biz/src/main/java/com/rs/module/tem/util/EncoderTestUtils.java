package com.rs.module.tem.util;

import lombok.extern.log4j.Log4j2;
import org.bytedeco.javacv.FFmpegFrameRecorder;

/**
 * 编码器测试工具类
 * 
 * <AUTHOR>
 */
@Log4j2
public class EncoderTestUtils {

    /**
     * 测试不同的H.264编码器配置
     * 
     * @param outputPath 输出路径
     * @param width 视频宽度
     * @param height 视频高度
     * @param frameRate 帧率
     * @return 可用的录制器配置
     */
    public static FFmpegFrameRecorder createCompatibleRecorder(String outputPath, 
                                                             int width, 
                                                             int height, 
                                                             double frameRate) {
        FFmpegFrameRecorder recorder = null;
        
        // 配置1: 最简配置
        try {
            log.info("尝试最简H.264配置...");
            recorder = new FFmpegFrameRecorder(outputPath, width, height, 0);
            recorder.setVideoCodec(org.bytedeco.ffmpeg.global.avcodec.AV_CODEC_ID_H264);
            recorder.setFormat("mp4");
            recorder.setFrameRate(frameRate);
            recorder.setPixelFormat(org.bytedeco.ffmpeg.global.avutil.AV_PIX_FMT_YUV420P);
            
            // 最基本的码率设置
            int bitrate = FFmpegUtils.getRecommendedBitrate(width, height);
            recorder.setVideoBitrate(bitrate);
            
            recorder.start();
            log.info("最简H.264配置成功");
            return recorder;
            
        } catch (Exception e) {
            log.warn("最简H.264配置失败: {}", e.getMessage());
            if (recorder != null) {
                try { recorder.release(); } catch (Exception ignored) {}
            }
        }
        
        // 配置2: 使用libx264编码器
        try {
            log.info("尝试libx264编码器...");
            recorder = new FFmpegFrameRecorder(outputPath, width, height, 0);
            recorder.setVideoCodecName("libx264");
            recorder.setFormat("mp4");
            recorder.setFrameRate(frameRate);
            recorder.setPixelFormat(org.bytedeco.ffmpeg.global.avutil.AV_PIX_FMT_YUV420P);
            
            int bitrate = FFmpegUtils.getRecommendedBitrate(width, height);
            recorder.setVideoBitrate(bitrate);
            
            // 使用更安全的x264选项
            recorder.setVideoOption("preset", "fast");
            recorder.setVideoOption("crf", "28");
            
            recorder.start();
            log.info("libx264编码器配置成功");
            return recorder;
            
        } catch (Exception e) {
            log.warn("libx264编码器配置失败: {}", e.getMessage());
            if (recorder != null) {
                try { recorder.release(); } catch (Exception ignored) {}
            }
        }
        
        // 配置3: 使用MPEG4编码器作为备选
        try {
            log.info("尝试MPEG4编码器作为备选...");
            recorder = new FFmpegFrameRecorder(outputPath, width, height, 0);
            recorder.setVideoCodec(org.bytedeco.ffmpeg.global.avcodec.AV_CODEC_ID_MPEG4);
            recorder.setFormat("mp4");
            recorder.setFrameRate(frameRate);
            recorder.setPixelFormat(org.bytedeco.ffmpeg.global.avutil.AV_PIX_FMT_YUV420P);
            
            int bitrate = FFmpegUtils.getRecommendedBitrate(width, height);
            recorder.setVideoBitrate(bitrate);
            
            recorder.start();
            log.info("MPEG4编码器配置成功");
            return recorder;
            
        } catch (Exception e) {
            log.warn("MPEG4编码器配置失败: {}", e.getMessage());
            if (recorder != null) {
                try { recorder.release(); } catch (Exception ignored) {}
            }
        }
        
        // 配置4: 最后尝试默认编码器
        try {
            log.info("尝试默认编码器...");
            recorder = new FFmpegFrameRecorder(outputPath, width, height, 0);
            recorder.setFormat("mp4");
            recorder.setFrameRate(frameRate);
            
            int bitrate = FFmpegUtils.getRecommendedBitrate(width, height);
            recorder.setVideoBitrate(bitrate);
            
            recorder.start();
            log.info("默认编码器配置成功");
            return recorder;
            
        } catch (Exception e) {
            log.error("所有编码器配置都失败: {}", e.getMessage());
            if (recorder != null) {
                try { recorder.release(); } catch (Exception ignored) {}
            }
        }
        
        return null;
    }

    /**
     * 测试编码器是否可用
     * 
     * @param codecName 编码器名称
     * @return 是否可用
     */
    public static boolean testEncoder(String codecName) {
        FFmpegFrameRecorder testRecorder = null;
        try {
            testRecorder = new FFmpegFrameRecorder("test.mp4", 640, 480, 0);
            testRecorder.setVideoCodecName(codecName);
            testRecorder.setFormat("mp4");
            testRecorder.setFrameRate(25);
            
            // 不实际启动，只测试配置
            log.info("编码器 {} 配置测试通过", codecName);
            return true;
            
        } catch (Exception e) {
            log.warn("编码器 {} 不可用: {}", codecName, e.getMessage());
            return false;
        } finally {
            if (testRecorder != null) {
                try { testRecorder.release(); } catch (Exception ignored) {}
            }
        }
    }

    /**
     * 获取可用的编码器列表
     * 
     * @return 可用编码器名称数组
     */
    public static String[] getAvailableEncoders() {
        String[] encoders = {"libx264", "h264", "mpeg4", "libxvid"};
        return java.util.Arrays.stream(encoders)
                .filter(EncoderTestUtils::testEncoder)
                .toArray(String[]::new);
    }
}
