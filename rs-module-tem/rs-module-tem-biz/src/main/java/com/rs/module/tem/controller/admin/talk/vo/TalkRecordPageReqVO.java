package com.rs.module.tem.controller.admin.talk.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 谈话教育-个人谈话教育分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TalkRecordPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("记录类型 （字典： ZD_THJY_THFS）")
    private String recordType;

    @ApiModelProperty("谈话记录编号(规则：T+9位单位编号+8位日期+6位序号)")
    private String talkCode;

    @ApiModelProperty("谈话原因（字典：ZD_THJY_THYY）")
    private String talkReason;

    @ApiModelProperty("谈话室id")
    private String talkRoomId;

    @ApiModelProperty("谈话室名称")
    private String talkRoomName;

    @ApiModelProperty("谈话开始时间")
    private Date[] startTime;

    @ApiModelProperty("谈话结束时间")
    private Date[] endTime;

    @ApiModelProperty("谈话内容文本")
    private String talkContent;

    @ApiModelProperty("谈话小结")
    private String talkSummary;

    @ApiModelProperty("标签id，多个,分割")
    private String tagId;

    @ApiModelProperty("标签名称，多个,分割")
    private String tagName;

    @ApiModelProperty("心理评估（字典：	ZD_THJY_THPJ）")
    private String psychologyAssess;

    @ApiModelProperty("岗位协同（字典：	ZD_THJY_GWXT）")
    private String jobCollaboration;

    @ApiModelProperty("推送用户，多个,分割")
    private String pushUserid;

    @ApiModelProperty("推送用户名称，多个,分割")
    private String pushUserName;

    @ApiModelProperty("签名图片地址")
    private String signUrl;

    @ApiModelProperty("捺印图片地址")
    private String sealUrl;

    @ApiModelProperty("签名捺印文件地址")
    private String signSealUrl;

    @ApiModelProperty("视频url2")
    private String videoUrl;

    @ApiModelProperty("视频url")
    private String videoUrl2;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
    /**
     * 来源
     */
    private String taskSource;
    /**
     * 监管人员编码
     */
    private String jgrybm;
}
