package com.rs.module.tem.controller.admin.talk;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.tem.controller.admin.talk.vo.TalkTaskRespVO;
import com.rs.module.tem.controller.admin.talk.vo.TalkTaskSaveReqVO;
import com.rs.module.tem.entity.talk.TalkTaskDO;
import com.rs.module.tem.service.talk.TalkTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 谈话教育-谈话任务")
@RestController
@RequestMapping("/tem/talk/talkTask")
@Validated
public class TalkTaskController {

    @Resource
    private TalkTaskService talkTaskService;

    @PostMapping("/create")
    @ApiOperation(value = "创建谈话教育-谈话任务")
    @LogRecordAnnotation(bizModule = "tem:talkTask:create", operateType = LogOperateType.CREATE, title = "创建谈话教育-谈话任务",
    success = "创建谈话教育-谈话任务成功", fail = "创建谈话教育-谈话任务失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createTalkTask(@Valid @RequestBody TalkTaskSaveReqVO createReqVO) {
        return success(talkTaskService.createTalkTask(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新谈话教育-谈话任务")
    @LogRecordAnnotation(bizModule = "tem:talkTask:update", operateType = LogOperateType.UPDATE, title = "更新谈话教育-谈话任务",
    success = "更新谈话教育-谈话任务成功", fail = "更新谈话教育-谈话任务失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateTalkTask(@Valid @RequestBody TalkTaskSaveReqVO updateReqVO) {
        talkTaskService.updateTalkTask(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除谈话教育-谈话任务")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "tem:talkTask:delete", operateType = LogOperateType.DELETE, title = "删除谈话教育-谈话任务",
    success = "删除谈话教育-谈话任务成功", fail = "删除谈话教育-谈话任务失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteTalkTask(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           talkTaskService.deleteTalkTask(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得谈话教育-谈话任务")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<TalkTaskRespVO> getTalkTask(@RequestParam("id") String id) {
        TalkTaskDO talkTask = talkTaskService.getTalkTask(id);
        return success(BeanUtils.toBean(talkTask, TalkTaskRespVO.class));
    }

    /**
     * 获取数据库时间
     * @return
     */

    @GetMapping("/getCurrentTime")
    @ApiOperation(value = "获得谈话教育-谈话任务-获取当前时间")
    public CommonResult<Date> getCurrentTime() {
        return success(new Date());
    }


//    @PostMapping("/page")
//    @ApiOperation(value = "获得谈话教育-谈话任务分页")
//    public CommonResult<PageResult<TalkTaskRespVO>> getTalkTaskPage(@Valid @RequestBody TalkTaskPageReqVO pageReqVO) {
//        PageResult<TalkTaskDO> pageResult = talkTaskService.getTalkTaskPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, TalkTaskRespVO.class));
//    }
//
//    @PostMapping("/list")
//    @ApiOperation(value = "获得谈话教育-谈话任务列表")
//    public CommonResult<List<TalkTaskRespVO>> getTalkTaskList(@Valid @RequestBody TalkTaskListReqVO listReqVO) {
//        List<TalkTaskDO> list = talkTaskService.getTalkTaskList(listReqVO);
//        return success(BeanUtils.toBean(list, TalkTaskRespVO.class));
//    }


}
