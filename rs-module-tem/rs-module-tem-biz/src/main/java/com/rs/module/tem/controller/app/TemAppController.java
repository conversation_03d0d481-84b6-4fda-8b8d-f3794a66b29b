package com.rs.module.tem.controller.app;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.tem.controller.admin.talk.vo.TalkRecordRespVO;
import com.rs.module.tem.controller.app.record.GetRecordListByJgrybmVO;
import com.rs.module.tem.entity.talk.TalkRecordDO;
import com.rs.module.tem.service.talk.TalkRecordService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Validated
@RestController
@AllArgsConstructor
@RequestMapping("/app/tem")
@Api(tags = "谈话教育APP相关的接口")
public class TemAppController {

    private final TalkRecordService talkRecordService;

    /**
     * @return 谈话记录列表
     */
    @PostMapping("/getRecordListByJgrybm")
    public CommonResult<List<TalkRecordRespVO>> getRecordListByJgrybm(@Validated @RequestBody GetRecordListByJgrybmVO getRecordListByJgrybmVO) {
        List<TalkRecordDO> recordListByJgrybm = talkRecordService.getRecordListByJgrybm(getRecordListByJgrybmVO);
        return CommonResult.success(BeanUtils.toBean(recordListByJgrybm, TalkRecordRespVO.class));
    }
}
