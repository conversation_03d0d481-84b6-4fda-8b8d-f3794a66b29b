package com.rs.module.tem.dao.talk;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.tem.entity.talk.TalkHelperDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.tem.controller.admin.talk.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 谈话教育-谈话提问助手 Dao
*
* <AUTHOR>
*/
@Mapper
public interface TalkHelperDao extends IBaseDao<TalkHelperDO> {


    default PageResult<TalkHelperDO> selectPage(TalkHelperPageReqVO reqVO) {
        Page<TalkHelperDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<TalkHelperDO> wrapper = new LambdaQueryWrapperX<TalkHelperDO>()
            .eqIfPresent(TalkHelperDO::getHelperContent, reqVO.getHelperContent())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(TalkHelperDO::getAddTime);
        }
        Page<TalkHelperDO> talkHelperPage = selectPage(page, wrapper);
        return new PageResult<>(talkHelperPage.getRecords(), talkHelperPage.getTotal());
    }
    default List<TalkHelperDO> selectList(TalkHelperListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TalkHelperDO>()
            .eqIfPresent(TalkHelperDO::getHelperContent, reqVO.getHelperContent())
        .orderByDesc(TalkHelperDO::getAddTime));    }


    }
