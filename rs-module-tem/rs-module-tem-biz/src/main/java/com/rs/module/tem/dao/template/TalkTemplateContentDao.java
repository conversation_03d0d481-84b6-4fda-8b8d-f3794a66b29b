package com.rs.module.tem.dao.template;

import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.PageParam;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.tem.entity.template.TalkTemplateContentDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
 * 谈话教育-谈话模板内容
 *
 * <AUTHOR>
 */
@Mapper
public interface TalkTemplateContentDao extends IBaseDao<TalkTemplateContentDO> {

    default List<TalkTemplateContentDO> selectListByTemplateId(String templateId) {
        return selectList(new LambdaQueryWrapperX<TalkTemplateContentDO>().eq(TalkTemplateContentDO::getTemplateId, templateId));
    }

    default int deleteByTemplateId(String templateId) {
        return delete(new LambdaQueryWrapperX<TalkTemplateContentDO>().eq(TalkTemplateContentDO::getTemplateId, templateId));
    }

}
