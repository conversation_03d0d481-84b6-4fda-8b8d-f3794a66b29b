package com.rs.module.tem.controller.admin.tag.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 谈话教育-谈话人员标签库列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TalkTagListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("标签名称")
    private String tagName;

    @ApiModelProperty("会见相关的备注信息")
    private String remarks;

}
