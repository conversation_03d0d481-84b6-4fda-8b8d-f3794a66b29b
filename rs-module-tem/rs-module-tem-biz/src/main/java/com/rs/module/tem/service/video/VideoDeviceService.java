package com.rs.module.tem.service.video;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.tem.controller.admin.video.vo.VideoDeviceListReqVO;
import com.rs.module.tem.controller.admin.video.vo.VideoDevicePageReqVO;
import com.rs.module.tem.controller.admin.video.vo.VideoDeviceSaveReqVO;
import com.rs.module.tem.entity.video.VideoDeviceDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 谈话教育-终端管理 Service 接口
 *
 * <AUTHOR>
 */
public interface VideoDeviceService extends IBaseService<VideoDeviceDO> {

    /**
     * 创建谈话教育-终端管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createVideoDevice(@Valid VideoDeviceSaveReqVO createReqVO);

    /**
     * 更新谈话教育-终端管理
     *
     * @param updateReqVO 更新信息
     */
    void updateVideoDevice(@Valid VideoDeviceSaveReqVO updateReqVO);

    /**
     * 删除谈话教育-终端管理
     *
     * @param id 编号
     */
    void deleteVideoDevice(String id);

    /**
     * 获得谈话教育-终端管理
     *
     * @param id 编号
     * @return 谈话教育-终端管理
     */
    VideoDeviceDO getVideoDevice(String id);

    /**
     * 获得谈话教育-终端管理分页
     *
     * @param pageReqVO 分页查询
     * @return 谈话教育-终端管理分页
     */
    PageResult<VideoDeviceDO> getVideoDevicePage(VideoDevicePageReqVO pageReqVO);

    /**
     * 获得谈话教育-终端管理列表
     *
     * @param listReqVO 查询条件
     * @return 谈话教育-终端管理列表
     */
    List<VideoDeviceDO> getVideoDeviceList(VideoDeviceListReqVO listReqVO);


    public String getTestVideoUrl(String ip);

    public VideoDeviceDO getVideoDeviceByRoomId(String roomId);
}
