package com.rs.module.tem.controller.admin.dic;

import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.mybatis.util.BspDbUtil;
import com.rs.framework.mybatis.util.DatasourceUtil;
import io.swagger.annotations.Api;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api(tags = "管理后台 - 字典初始化")
@RestController
@RequestMapping("/dic")
@Validated
@Log4j2
public class DicController {


    @PostMapping("/init")
    public void get(MultipartFile file, String systemMark) {

        //从file 获取字符串
        String str = "";
        try {
            str = new String(file.getBytes());
            JSONObject jsonObject = JSONObject.parseObject(str);
            JSONObject data = jsonObject.getJSONObject("data");
            for (String key : data.keySet()) {

            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @PostMapping("/cleanDic")
    public void cleanDic() {
        String appIds[] = {"1934968956884488192", "1936290224611135488", "1936290459194363904", "1936290761251360768", "1936291060116492288"};
        String targetAppId = "1934985431515009024";
        for (String appId : appIds) {
            Entity entity = new Entity();
            entity.setTableName("ops_dic");
            entity.set("APP_ID", appId);
            try {
                List<Entity> entities = BspDbUtil.getDb().find(entity);
                for (Entity entity1 : entities) {
                    String name = entity1.getStr("NAME");
                    if (StringUtils.isNotEmpty(name)) {
                        Entity entity2 = new Entity();
                        entity2.setTableName("ops_dic");
                        entity2.set("APP_ID", targetAppId);
                        entity2.set("NAME", name);
                        long count = BspDbUtil.getDb().count(entity2);
                        if (count == 0) {
                            log.info("缺少字典：" + name);
                        }

                        Entity delEntity = new Entity();
                        delEntity.setTableName("ops_dic_code");
                        delEntity.set("DIC_ID", entity1.getStr("ID"));
                        BspDbUtil.getDb().del(delEntity);
                        BspDbUtil.getDb().del(entity1);

                    }


                }
            } catch (SQLException e) {

            }

        }

    }

    //初始化菜单
    @PostMapping("/initMenu")
    public void initMenu() {
        DataSource ds = DatasourceUtil.getDataSource("master");
        Db use = Db.use(ds);
        try {
            List<Entity> query = use.query("select * from acp_pm_terminal_menu where parent_id = '-1'");
            query.forEach(entity -> {
                String url = "http://localhost:1910/uac/menu/save?access_token=0685d269-db24-48a0-89d5-de577a4fa2cf";
                Map<String, Object> param = new HashMap<>();
                param.put("id", entity.getStr("id"));
                param.put("isDisabled", 0);
                param.put("openPublic", 0);
                param.put("appId", "1934985431515009024");
                param.put("openMode", 0);
                param.put("code", "bjsdskss:" + entity.getStr("code"));
                param.put("name", entity.getStr("menu_name"));
                param.put("url", "/dr");

                String parentId = entity.getStr("parent_id");
                if (!"-1".equals(parentId)) {
                    param.put("parentId", parentId);
                }

                String personnelType = entity.getStr("personnel_type");
                if ("PRISONER".equals(personnelType)) {
                    param.put("useType", 2);
                }
                if ("POLICE".equals(personnelType)) {
                    param.put("useType", 1);
                }
                param.put("isExternal", 0);

                String terminalType = entity.getStr("terminal_type");
                if ("CNP".equals(terminalType)) {
                    param.put("moduleId", "1948997146665684992");
                }
                if ("CWP".equals(terminalType)) {
                    param.put("moduleId", "1948997280635949056");
                }
                param.put("orderId", entity.getStr("sort_order"));
                String post = HttpUtil.post(url, param);
                log.info(post);
            });
        } catch (SQLException e) {
            log.error(e);
        }
        try {
            List<Entity> query = use.query("select * from acp_pm_terminal_menu where parent_id != '-1'");
            query.forEach(entity -> {
                String url = "http://localhost:1910/uac/menu/save?access_token=0685d269-db24-48a0-89d5-de577a4fa2cf";
                Map<String, Object> param = new HashMap<>();
                param.put("id", entity.getStr("id"));
                param.put("isDisabled", 0);
                param.put("openPublic", 0);
                param.put("appId", "1934985431515009024");
                param.put("openMode", 0);
                param.put("code", "bjsdskss:" + entity.getStr("code"));
                param.put("name", entity.getStr("menu_name"));
                param.put("url", "/dr");

                String parentId = entity.getStr("parent_id");
                if (!"-1".equals(parentId)) {
                    param.put("parentId", parentId);
                }

                String personnelType = entity.getStr("personnel_type");
                if ("PRISONER".equals(personnelType)) {
                    param.put("useType", 2);
                }
                if ("POLICE".equals(personnelType)) {
                    param.put("useType", 1);
                }
                param.put("isExternal", 0);

                String terminalType = entity.getStr("terminal_type");
                if ("CNP".equals(terminalType)) {
                    param.put("moduleId", "1948997146665684992");
                }
                if ("CWP".equals(terminalType)) {
                    param.put("moduleId", "1948997280635949056");
                }
                param.put("orderId", entity.getStr("sort_order"));
                String post = HttpUtil.post(url, param);
                log.info(post);
            });
        } catch (SQLException e) {
            log.error(e);
        }
    }
}
