package com.rs.module.tem.controller.admin.tag.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 谈话教育-谈话人员标签库新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TalkTagSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("标签名称")
    @NotEmpty(message = "标签名称不能为空")
    private String tagName;

    @ApiModelProperty("会见相关的备注信息")
    private String remarks;

}
