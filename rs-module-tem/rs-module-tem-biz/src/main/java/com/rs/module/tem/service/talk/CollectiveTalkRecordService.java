package com.rs.module.tem.service.talk;

import java.util.*;
import javax.validation.*;
import com.rs.module.tem.controller.admin.talk.vo.*;
import com.rs.module.tem.entity.talk.CollectiveTalkRecordDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 谈话教育-集体谈话教育 Service 接口
 *
 * <AUTHOR>
 */
public interface CollectiveTalkRecordService extends IBaseService<CollectiveTalkRecordDO>{

    /**
     * 创建谈话教育-集体谈话教育
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCollectiveTalkRecord(@Valid CollectiveTalkRecordSaveReqVO createReqVO);

    /**
     * 更新谈话教育-集体谈话教育
     *
     * @param updateReqVO 更新信息
     */
    void updateCollectiveTalkRecord(@Valid CollectiveTalkRecordSaveReqVO updateReqVO);

    /**
     * 删除谈话教育-集体谈话教育
     *
     * @param id 编号
     */
    void deleteCollectiveTalkRecord(String id);

    /**
     * 获得谈话教育-集体谈话教育
     *
     * @param id 编号
     * @return 谈话教育-集体谈话教育
     */
    CollectiveTalkRecordDO getCollectiveTalkRecord(String id);

    /**
    * 获得谈话教育-集体谈话教育分页
    *
    * @param pageReqVO 分页查询
    * @return 谈话教育-集体谈话教育分页
    */
    PageResult<CollectiveTalkRecordDO> getCollectiveTalkRecordPage(CollectiveTalkRecordPageReqVO pageReqVO);

    /**
    * 获得谈话教育-集体谈话教育列表
    *
    * @param listReqVO 查询条件
    * @return 谈话教育-集体谈话教育列表
    */
    List<CollectiveTalkRecordDO> getCollectiveTalkRecordList(CollectiveTalkRecordListReqVO listReqVO);


}
