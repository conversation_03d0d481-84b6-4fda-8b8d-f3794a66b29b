package com.rs.module.tem.entity.talk;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 谈话教育-集体谈话教育 DO
 *
 * <AUTHOR>
 */
@TableName("tem_talk_group_record")
@KeySequence("tem_talk_group_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CollectiveTalkRecordDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 谈话记录编号(规则：T+9位单位编号+8位日期+6位序号)
     */
    private String talkCode;
    /**
     * 集体谈话教育的时间段
     */
    private Date talkEduStartTime;
    /**
     * 集体谈话教育的时间段
     */
    private Date talkEduEndTime;
    /**
     * 集体谈话教育所涉及的范围
     */
    private String talkEduScope;
    /**
     * 进行授课的人员姓名
     */
    private String instructorName;
    /**
     * 授课人的职务
     */
    private String instructorPosition;
    /**
     * 接受集体谈话教育的人数
     */
    private Integer numberOfRecipients;
    /**
     * 集体谈话教育的课题
     */
    private String talkEduTopic;
    /**
     * 授课内容的摘要
     */
    private String summaryOfContent;
    /**
     * 在押人员对集体谈话教育的反应
     */
    private String detaineesResponse;
    /**
     * 登记经办人
     */
    private String operatorSfzh;
    /**
     * 登记经办人姓名
     */
    private String operatorXm;
    /**
     * 办理会见登记的时间
     */
    private Date operatorTime;

}
