package com.rs.module.tem.service.talk;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.base.service.pm.AreaService;
import com.rs.module.base.service.pm.device.DeviceAuthService;
import com.rs.module.tem.controller.admin.talk.vo.TalkRecordListReqVO;
import com.rs.module.tem.controller.admin.talk.vo.TalkRecordPageReqVO;
import com.rs.module.tem.controller.admin.talk.vo.TalkRecordSaveReqVO;
import com.rs.module.tem.controller.app.record.GetRecordListByJgrybmVO;
import com.rs.module.tem.dao.talk.TalkRecordDao;
import com.rs.module.tem.dao.talk.TalkTaskDao;
import com.rs.module.tem.entity.talk.TalkRecordDO;
import com.rs.module.tem.entity.talk.TalkTaskDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;


/**
 * 谈话教育-个人谈话教育 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TalkRecordServiceImpl extends BaseServiceImpl<TalkRecordDao, TalkRecordDO> implements TalkRecordService {

    @Resource
    private TalkRecordDao talkRecordDao;

    @Resource
    private TalkTaskDao talkTaskDao;
    @Resource
    private DeviceAuthService devAuthService;

    @Resource
    private AreaService areaService;

    @Override
    public String createTalkRecord(TalkRecordSaveReqVO createReqVO) {
        // 插入
        TalkRecordDO talkRecord = BeanUtils.toBean(createReqVO, TalkRecordDO.class);
        TalkTaskDO talkTaskDO = talkTaskDao.selectOne(TalkTaskDO::getTalkCode, talkRecord.getTalkCode());

        //更新谈话记录表的jgrybm
        talkRecord.setJgrybm(talkTaskDO.getJgrybm());

        //谈话原因
        talkTaskDO.setTalkReason(createReqVO.getTalkReason());
        //谈话待办
        talkRecord.setTaskSource(talkTaskDO.getTaskSource());
        talkRecord.setVideoUrl(talkTaskDO.getVideoUrl());

        talkRecordDao.insert(talkRecord);
        // 返回
        return talkRecord.getId();
    }

    @Override
    public void updateTalkRecord(TalkRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateTalkRecordExists(updateReqVO.getId());
        // 更新
        TalkRecordDO updateObj = BeanUtils.toBean(updateReqVO, TalkRecordDO.class);
        talkRecordDao.updateById(updateObj);
    }

    @Override
    public void deleteTalkRecord(String id) {
        // 校验存在
        validateTalkRecordExists(id);
        // 删除
        talkRecordDao.deleteById(id);
    }

    private void validateTalkRecordExists(String id) {
        if (talkRecordDao.selectById(id) == null) {
            throw new ServerException("谈话教育-个人谈话教育数据不存在");
        }
    }

    @Override
    public TalkRecordDO getTalkRecord(String id) {
        return talkRecordDao.selectById(id);
    }

    @Override
    public PageResult<TalkRecordDO> getTalkRecordPage(TalkRecordPageReqVO pageReqVO) {
        return talkRecordDao.selectPage(pageReqVO);
    }

    @Override
    public List<TalkRecordDO> getTalkRecordList(TalkRecordListReqVO listReqVO) {
        return talkRecordDao.selectList(listReqVO);
    }

    @Override
    public AreaDO getThs(HttpServletRequest request) {
        String ip = ServletUtil.getClientIP(request);
        if (StringUtils.isEmpty(ip)) {
            throw new ServerException("获取ip失败");
        }
        AreaDO areaByIP = devAuthService.getOneAreaByIP(ip, "0024");
        if (areaByIP == null) {
            //获取演示的监区
            areaByIP = areaService.getById("1100001210100040001");
        }
        return areaByIP;
    }

    @Override
    public List<TalkRecordDO> getPersonTaskRecordEndListByJgrybm(String jgrybm, String talkReason) {
        return talkRecordDao.selectList(new LambdaQueryWrapper<TalkRecordDO>()
                .like(StrUtil.isNotBlank(talkReason), TalkRecordDO::getTalkReason, talkReason)
                .eq(TalkRecordDO::getJgrybm, jgrybm).isNotNull(TalkRecordDO::getEndTime).orderByDesc(TalkRecordDO::getEndTime));
    }


    @Override
    public List<TalkRecordDO> getRecordListByJgrybm(GetRecordListByJgrybmVO getRecordListByJgrybmVO) {
        LambdaQueryWrapper<TalkRecordDO> wrapper = new LambdaQueryWrapper<TalkRecordDO>()
                .eq(TalkRecordDO::getJgrybm, getRecordListByJgrybmVO.getJgrybm());

        // 根据rangType设置日期过滤条件
        String rangType = getRecordListByJgrybmVO.getRangType();
        if (StrUtil.isNotBlank(rangType)) {
            Date[] dateRange = getRecordListByJgrybmVO.calculateDateRange();
            if (dateRange != null && dateRange.length == 2) {
                wrapper.ge(TalkRecordDO::getAddTime, dateRange[0])
                        .le(TalkRecordDO::getAddTime, dateRange[1]);
            }
        }
        return talkRecordDao.selectList(wrapper.orderByDesc(TalkRecordDO::getAddTime));
    }

}
