/*
 * @Author: 丁永泽 <EMAIL>
 * @Date: 2025-04-10 10:19:33
 * @LastEditors: 丁永泽 <EMAIL>
 * @LastEditTime: 2025-04-11 08:45:04
 * @FilePath: \rs-master\rs-module-tem\rs-module-tem-biz\src\main\java\com\rs\module\tem\TemServerApplication.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.rs.module.tem;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.mzt.bsp.logapi.starter.annotation.EnableLogRecord;
import com.rs.framework.common.util.spring.SpringUtils;
import org.dromara.x.file.storage.spring.EnableFileStorage;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.net.UnknownHostException;
import java.util.concurrent.Executor;

/**
 * 谈话管理系统启动类
 * <AUTHOR>
 *
 */
@SpringBootApplication(exclude = {DruidDataSourceAutoConfigure.class, MongoAutoConfiguration.class, MongoDataAutoConfiguration.class})
@ComponentScan(value = {"com.rs.*", "com.bsp.*"})
@EnableLogRecord(systemMark = "tem")
@EnableFileStorage
@EnableScheduling
public class TemServerApplication {

	public static void main(String[] args) throws UnknownHostException {
		System.out.println(System.getProperty("user.dir"));
		ConfigurableApplicationContext application = SpringApplication.run(TemServerApplication.class, args);
		SpringUtils.printStartLog(application);

	}
	@Bean("recorderThreadPool")
	public Executor recorderThreadPool() {
		ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
		executor.setCorePoolSize(5);
		executor.setMaxPoolSize(10);
		executor.setQueueCapacity(25);
		executor.setThreadNamePrefix("recorder-");
		executor.initialize();
		return executor;
	}
}
