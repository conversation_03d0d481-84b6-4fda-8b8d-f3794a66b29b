package com.rs.module.tem.service.tag;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.tem.controller.admin.tag.vo.*;
import com.rs.module.tem.entity.tag.TalkTagDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.tem.dao.tag.TalkTagDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 谈话教育-谈话人员标签库 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TalkTagServiceImpl extends BaseServiceImpl<TalkTagDao, TalkTagDO> implements TalkTagService {

    @Resource
    private TalkTagDao talkTagDao;

    @Override
    public String createTalkTag(TalkTagSaveReqVO createReqVO) {
        // 插入
        TalkTagDO talkTag = BeanUtils.toBean(createReqVO, TalkTagDO.class);
        talkTagDao.insert(talkTag);
        // 返回
        return talkTag.getId();
    }

    @Override
    public void updateTalkTag(TalkTagSaveReqVO updateReqVO) {
        // 校验存在
        validateTalkTagExists(updateReqVO.getId());
        // 更新
        TalkTagDO updateObj = BeanUtils.toBean(updateReqVO, TalkTagDO.class);
        talkTagDao.updateById(updateObj);
    }

    @Override
    public void deleteTalkTag(String id) {
        // 校验存在
        validateTalkTagExists(id);
        // 删除
        talkTagDao.deleteById(id);
    }

    private void validateTalkTagExists(String id) {
        if (talkTagDao.selectById(id) == null) {
            throw new ServerException("谈话教育-谈话人员标签库数据不存在");
        }
    }

    @Override
    public TalkTagDO getTalkTag(String id) {
        return talkTagDao.selectById(id);
    }

    @Override
    public PageResult<TalkTagDO> getTalkTagPage(TalkTagPageReqVO pageReqVO) {
        return talkTagDao.selectPage(pageReqVO);
    }

    @Override
    public List<TalkTagDO> getTalkTagList(TalkTagListReqVO listReqVO) {
        return talkTagDao.selectList(listReqVO);
    }


}
