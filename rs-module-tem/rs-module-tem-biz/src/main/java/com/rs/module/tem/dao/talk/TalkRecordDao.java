package com.rs.module.tem.dao.talk;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.tem.controller.admin.talk.vo.TalkRecordListReqVO;
import com.rs.module.tem.controller.admin.talk.vo.TalkRecordPageReqVO;
import com.rs.module.tem.entity.talk.TalkRecordDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 谈话教育-个人谈话教育 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface TalkRecordDao extends IBaseDao<TalkRecordDO> {


    default PageResult<TalkRecordDO> selectPage(TalkRecordPageReqVO reqVO) {
        Page<TalkRecordDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<TalkRecordDO> wrapper = new LambdaQueryWrapperX<TalkRecordDO>()
                .eqIfPresent(TalkRecordDO::getRecordType, reqVO.getRecordType())
                .eqIfPresent(TalkRecordDO::getTalkCode, reqVO.getTalkCode())
                .eqIfPresent(TalkRecordDO::getTalkReason, reqVO.getTalkReason())
                .eqIfPresent(TalkRecordDO::getTalkRoomId, reqVO.getTalkRoomId())
                .likeIfPresent(TalkRecordDO::getTalkRoomName, reqVO.getTalkRoomName())
                .betweenIfPresent(TalkRecordDO::getStartTime, reqVO.getStartTime())
                .betweenIfPresent(TalkRecordDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(TalkRecordDO::getTalkContent, reqVO.getTalkContent())
                .eqIfPresent(TalkRecordDO::getTalkSummary, reqVO.getTalkSummary())
                .eqIfPresent(TalkRecordDO::getTagId, reqVO.getTagId())
                .likeIfPresent(TalkRecordDO::getTagName, reqVO.getTagName())
                .eqIfPresent(TalkRecordDO::getPsychologyAssess, reqVO.getPsychologyAssess())
                .eqIfPresent(TalkRecordDO::getJobCollaboration, reqVO.getJobCollaboration())
                .eqIfPresent(TalkRecordDO::getPushUserid, reqVO.getPushUserid())
                .likeIfPresent(TalkRecordDO::getPushUserName, reqVO.getPushUserName())
                .eqIfPresent(TalkRecordDO::getSignUrl, reqVO.getSignUrl())
                .eqIfPresent(TalkRecordDO::getSealUrl, reqVO.getSealUrl())
                .eqIfPresent(TalkRecordDO::getSignSealUrl, reqVO.getSignSealUrl())
                .eqIfPresent(TalkRecordDO::getVideoUrl, reqVO.getVideoUrl())
                .eqIfPresent(TalkRecordDO::getVideoUrl2, reqVO.getVideoUrl2());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(TalkRecordDO::getAddTime);
        }
        Page<TalkRecordDO> talkRecordPage = selectPage(page, wrapper);
        return new PageResult<>(talkRecordPage.getRecords(), talkRecordPage.getTotal());
    }

    default List<TalkRecordDO> selectList(TalkRecordListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TalkRecordDO>()
                .eqIfPresent(TalkRecordDO::getRecordType, reqVO.getRecordType())
                .eqIfPresent(TalkRecordDO::getTalkCode, reqVO.getTalkCode())
                .eqIfPresent(TalkRecordDO::getTalkReason, reqVO.getTalkReason())
                .eqIfPresent(TalkRecordDO::getTalkRoomId, reqVO.getTalkRoomId())
                .likeIfPresent(TalkRecordDO::getTalkRoomName, reqVO.getTalkRoomName())
                .betweenIfPresent(TalkRecordDO::getStartTime, reqVO.getStartTime())
                .betweenIfPresent(TalkRecordDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(TalkRecordDO::getTalkContent, reqVO.getTalkContent())
                .eqIfPresent(TalkRecordDO::getTalkSummary, reqVO.getTalkSummary())
                .eqIfPresent(TalkRecordDO::getTagId, reqVO.getTagId())
                .likeIfPresent(TalkRecordDO::getTagName, reqVO.getTagName())
                .eqIfPresent(TalkRecordDO::getPsychologyAssess, reqVO.getPsychologyAssess())
                .eqIfPresent(TalkRecordDO::getJobCollaboration, reqVO.getJobCollaboration())
                .eqIfPresent(TalkRecordDO::getPushUserid, reqVO.getPushUserid())
                .likeIfPresent(TalkRecordDO::getPushUserName, reqVO.getPushUserName())
                .eqIfPresent(TalkRecordDO::getSignUrl, reqVO.getSignUrl())
                .eqIfPresent(TalkRecordDO::getSealUrl, reqVO.getSealUrl())
                .eqIfPresent(TalkRecordDO::getSignSealUrl, reqVO.getSignSealUrl())
                .eqIfPresent(TalkRecordDO::getVideoUrl, reqVO.getVideoUrl())
                .eqIfPresent(TalkRecordDO::getVideoUrl2, reqVO.getVideoUrl2())
                .orderByDesc(TalkRecordDO::getAddTime));
    }


}
