package com.rs.module.tem.service.video.recorder;


import com.rs.module.tem.controller.admin.video.recorder.RecordingTask;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface VideoRecorderService {
    RecordingTask startRecording(String inputUrl, String outputPath, String talkId);
    boolean stopRecording(String taskId);
    boolean sendHeartbeat(String taskId);
    Optional<RecordingTask> getTask(String taskId);
    List<RecordingTask> getAllTasks();
    void checkHeartbeats();
    boolean isTaskInitialized(String taskId);
}

