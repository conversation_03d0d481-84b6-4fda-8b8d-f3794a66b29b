package com.rs.module.tem.controller.admin.talk;

import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpUtil;
import com.rs.framework.mybatis.util.BspDbUtil;
import com.rs.module.acp.entity.pm.TerminalVirtualAccountStatusDO;
import com.rs.module.acp.service.pm.TerminalVirtualAccountStatusService;
import com.rs.module.oss.utils.UploadUtils;
import com.rs.module.tem.entity.talk.TalkTaskDO;
import com.rs.module.tem.service.talk.TalkTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Api(tags = "推送处理")
@RestController
@AllArgsConstructor
@RequestMapping("/tem/notification")
@Validated
public class NotificationController {

    private final TerminalVirtualAccountStatusService terminalVirtualAccountStatusService;
    private final TalkTaskService talkTaskService;


    @PostMapping("/ky")
    @ApiOperation(value = "快鱼推送回调")
    public Map<String, Object> ky(HttpServletRequest request) {
        String type = request.getParameter("jsondata[type]");
        String sourceid = request.getParameter("jsondata[sourceid]");
        String targetid = request.getParameter("jsondata[targetid]");
        String state = request.getParameter("jsondata[state]");
        String file = request.getParameter("jsondata[file]");
        //设备对讲 0停止
        if (("2").equals(type) && ("0").equals(state)) {
            new Thread(() -> {
                TerminalVirtualAccountStatusDO terminalVirtualAccountStatusDO = terminalVirtualAccountStatusService.lambdaQuery()
                        .eq(TerminalVirtualAccountStatusDO::getVirtualId, sourceid)
                        .eq(TerminalVirtualAccountStatusDO::getTargetId, targetid).one();
                TalkTaskDO talkTask = talkTaskService.getTalkTask(terminalVirtualAccountStatusDO.getBusinessId());
                String saveFilePath = System.getProperty("user.dir") + "/recordings/" + talkTask.getId() + ".mp4";
                File saveFile = new File(saveFilePath);
                File parentDir = saveFile.getParentFile();
                if (parentDir != null && !parentDir.exists()) {
                    parentDir.mkdirs();
                }
                try {
                    String kyIp = BspDbUtil.getParam("KY_IP");
                    String url = "http://" + kyIp + "/recordings/" + file;
                    HttpUtil.downloadFile(url, saveFilePath);
                    talkTask.setVideoUrl(UploadUtils.upload(new File(saveFilePath)));
                    talkTaskService.updateById(talkTask);
                } finally {
                    terminalVirtualAccountStatusService.removeById(terminalVirtualAccountStatusDO.getId());
                    FileUtil.del(saveFilePath);
                }
            }).start();
        }
        Map<String, Object> map = new HashMap<>();
        map.put("res", "1");
        return map;
    }

}
