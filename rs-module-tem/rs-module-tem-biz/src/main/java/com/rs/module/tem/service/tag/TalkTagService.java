package com.rs.module.tem.service.tag;

import java.util.*;
import javax.validation.*;
import com.rs.module.tem.controller.admin.tag.vo.*;
import com.rs.module.tem.entity.tag.TalkTagDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 谈话教育-谈话人员标签库 Service 接口
 *
 * <AUTHOR>
 */
public interface TalkTagService extends IBaseService<TalkTagDO>{

    /**
     * 创建谈话教育-谈话人员标签库
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createTalkTag(@Valid TalkTagSaveReqVO createReqVO);

    /**
     * 更新谈话教育-谈话人员标签库
     *
     * @param updateReqVO 更新信息
     */
    void updateTalkTag(@Valid TalkTagSaveReqVO updateReqVO);

    /**
     * 删除谈话教育-谈话人员标签库
     *
     * @param id 编号
     */
    void deleteTalkTag(String id);

    /**
     * 获得谈话教育-谈话人员标签库
     *
     * @param id 编号
     * @return 谈话教育-谈话人员标签库
     */
    TalkTagDO getTalkTag(String id);

    /**
    * 获得谈话教育-谈话人员标签库分页
    *
    * @param pageReqVO 分页查询
    * @return 谈话教育-谈话人员标签库分页
    */
    PageResult<TalkTagDO> getTalkTagPage(TalkTagPageReqVO pageReqVO);

    /**
    * 获得谈话教育-谈话人员标签库列表
    *
    * @param listReqVO 查询条件
    * @return 谈话教育-谈话人员标签库列表
    */
    List<TalkTagDO> getTalkTagList(TalkTagListReqVO listReqVO);


}
