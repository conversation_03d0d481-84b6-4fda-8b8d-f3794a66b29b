package com.rs.module.tem.service.talk;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.tem.controller.admin.talk.vo.CollectiveTalkRecordListReqVO;
import com.rs.module.tem.controller.admin.talk.vo.CollectiveTalkRecordPageReqVO;
import com.rs.module.tem.controller.admin.talk.vo.CollectiveTalkRecordSaveReqVO;
import com.rs.module.tem.dao.talk.CollectiveTalkRecordDao;
import com.rs.module.tem.entity.talk.CollectiveTalkRecordDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 谈话教育-集体谈话教育 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CollectiveTalkRecordServiceImpl extends BaseServiceImpl<CollectiveTalkRecordDao, CollectiveTalkRecordDO> implements CollectiveTalkRecordService {

    @Resource
    private CollectiveTalkRecordDao collectiveTalkRecordDao;

    @Override
    public String createCollectiveTalkRecord(CollectiveTalkRecordSaveReqVO createReqVO) {
        // 插入
        CollectiveTalkRecordDO collectiveTalkRecord = BeanUtils.toBean(createReqVO, CollectiveTalkRecordDO.class);
        collectiveTalkRecordDao.insert(collectiveTalkRecord);
        // 返回
        return collectiveTalkRecord.getId();
    }

    @Override
    public void updateCollectiveTalkRecord(CollectiveTalkRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateCollectiveTalkRecordExists(updateReqVO.getId());
        // 更新
        CollectiveTalkRecordDO updateObj = BeanUtils.toBean(updateReqVO, CollectiveTalkRecordDO.class);
        collectiveTalkRecordDao.updateById(updateObj);
    }

    @Override
    public void deleteCollectiveTalkRecord(String id) {
        // 校验存在
        validateCollectiveTalkRecordExists(id);
        // 删除
        collectiveTalkRecordDao.deleteById(id);
    }

    private void validateCollectiveTalkRecordExists(String id) {
        if (collectiveTalkRecordDao.selectById(id) == null) {
            throw new ServerException("谈话教育-集体谈话教育数据不存在");
        }
    }

    @Override
    public CollectiveTalkRecordDO getCollectiveTalkRecord(String id) {
        return collectiveTalkRecordDao.selectById(id);
    }

    @Override
    public PageResult<CollectiveTalkRecordDO> getCollectiveTalkRecordPage(CollectiveTalkRecordPageReqVO pageReqVO) {
        return collectiveTalkRecordDao.selectPage(pageReqVO);
    }

    @Override
    public List<CollectiveTalkRecordDO> getCollectiveTalkRecordList(CollectiveTalkRecordListReqVO listReqVO) {
        return collectiveTalkRecordDao.selectList(listReqVO);
    }


}
