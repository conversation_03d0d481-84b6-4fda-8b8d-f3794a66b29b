package com.rs.module.tem.service.talk;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.tem.controller.admin.talk.vo.TalkTaskListReqVO;
import com.rs.module.tem.controller.admin.talk.vo.TalkTaskPageReqVO;
import com.rs.module.tem.controller.admin.talk.vo.TalkTaskSaveReqVO;
import com.rs.module.tem.dao.talk.TalkTaskDao;
import com.rs.module.tem.entity.talk.TalkTaskDO;
import com.rs.module.tem.enums.TaskSource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 谈话教育-谈话任务 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TalkTaskServiceImpl extends BaseServiceImpl<TalkTaskDao, TalkTaskDO> implements TalkTaskService {

    @Resource
    BspApi bspApi;

    @Resource
    private TalkTaskDao talkTaskDao;

    @Override
    public String createTalkTask(TalkTaskSaveReqVO createReqVO) {
        // 插入
        TalkTaskDO talkTask = BeanUtils.toBean(createReqVO, TalkTaskDO.class);
        String thjlNo = bspApi.executeByRuleCode("thjl_no", null);
        talkTask.setTalkCode(thjlNo);
        talkTask.setTaskSource(TaskSource.THDB.getValue());
        talkTaskDao.insert(talkTask);
        // 返回
        return talkTask.getId();
    }

    @Override
    public void updateTalkTask(TalkTaskSaveReqVO updateReqVO) {
        // 校验存在
        validateTalkTaskExists(updateReqVO.getId());
        // 更新
        TalkTaskDO updateObj = BeanUtils.toBean(updateReqVO, TalkTaskDO.class);
        talkTaskDao.updateById(updateObj);
    }

    @Override
    public void deleteTalkTask(String id) {
        // 校验存在
        validateTalkTaskExists(id);
        // 删除
        talkTaskDao.deleteById(id);
    }

    private void validateTalkTaskExists(String id) {
        if (talkTaskDao.selectById(id) == null) {
            throw new ServerException("谈话教育-谈话任务数据不存在");
        }
    }

    @Override
    public TalkTaskDO getTalkTask(String id) {
        return talkTaskDao.selectById(id);
    }

    @Override
    public PageResult<TalkTaskDO> getTalkTaskPage(TalkTaskPageReqVO pageReqVO) {
        return talkTaskDao.selectPage(pageReqVO);
    }

    @Override
    public List<TalkTaskDO> getTalkTaskList(TalkTaskListReqVO listReqVO) {
        return talkTaskDao.selectList(listReqVO);
    }

    @Override
    public TalkTaskDO getTalkTaskByTalkCode(String talkCode) {
        return talkTaskDao.selectOne(TalkTaskDO::getTalkCode, talkCode);
    }


}
