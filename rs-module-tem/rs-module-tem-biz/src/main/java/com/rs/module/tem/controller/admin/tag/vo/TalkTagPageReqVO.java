package com.rs.module.tem.controller.admin.tag.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 谈话教育-谈话人员标签库分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TalkTagPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("标签名称")
    private String tagName;

    @ApiModelProperty("会见相关的备注信息")
    private String remarks;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
