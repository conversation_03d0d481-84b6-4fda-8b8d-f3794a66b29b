package com.rs.module.tem.dao.talk;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.tem.entity.talk.CollectiveTalkRecordDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.tem.controller.admin.talk.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 谈话教育-集体谈话教育 Dao
*
* <AUTHOR>
*/
@Mapper
public interface CollectiveTalkRecordDao extends IBaseDao<CollectiveTalkRecordDO> {


    default PageResult<CollectiveTalkRecordDO> selectPage(CollectiveTalkRecordPageReqVO reqVO) {
        Page<CollectiveTalkRecordDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<CollectiveTalkRecordDO> wrapper = new LambdaQueryWrapperX<CollectiveTalkRecordDO>()
            .eqIfPresent(CollectiveTalkRecordDO::getTalkCode, reqVO.getTalkCode())
            .betweenIfPresent(CollectiveTalkRecordDO::getTalkEduStartTime, reqVO.getTalkEduStartTime())
            .betweenIfPresent(CollectiveTalkRecordDO::getTalkEduEndTime, reqVO.getTalkEduEndTime())
            .eqIfPresent(CollectiveTalkRecordDO::getTalkEduScope, reqVO.getTalkEduScope())
            .likeIfPresent(CollectiveTalkRecordDO::getInstructorName, reqVO.getInstructorName())
            .eqIfPresent(CollectiveTalkRecordDO::getInstructorPosition, reqVO.getInstructorPosition())
            .eqIfPresent(CollectiveTalkRecordDO::getNumberOfRecipients, reqVO.getNumberOfRecipients())
            .eqIfPresent(CollectiveTalkRecordDO::getTalkEduTopic, reqVO.getTalkEduTopic())
            .eqIfPresent(CollectiveTalkRecordDO::getSummaryOfContent, reqVO.getSummaryOfContent())
            .eqIfPresent(CollectiveTalkRecordDO::getDetaineesResponse, reqVO.getDetaineesResponse())
            .eqIfPresent(CollectiveTalkRecordDO::getOperatorSfzh, reqVO.getOperatorSfzh())
            .eqIfPresent(CollectiveTalkRecordDO::getOperatorXm, reqVO.getOperatorXm())
            .betweenIfPresent(CollectiveTalkRecordDO::getOperatorTime, reqVO.getOperatorTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(CollectiveTalkRecordDO::getAddTime);
        }
        Page<CollectiveTalkRecordDO> collectiveTalkRecordPage = selectPage(page, wrapper);
        return new PageResult<>(collectiveTalkRecordPage.getRecords(), collectiveTalkRecordPage.getTotal());
    }
    default List<CollectiveTalkRecordDO> selectList(CollectiveTalkRecordListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CollectiveTalkRecordDO>()
            .eqIfPresent(CollectiveTalkRecordDO::getTalkCode, reqVO.getTalkCode())
            .betweenIfPresent(CollectiveTalkRecordDO::getTalkEduStartTime, reqVO.getTalkEduStartTime())
            .betweenIfPresent(CollectiveTalkRecordDO::getTalkEduEndTime, reqVO.getTalkEduEndTime())
            .eqIfPresent(CollectiveTalkRecordDO::getTalkEduScope, reqVO.getTalkEduScope())
            .likeIfPresent(CollectiveTalkRecordDO::getInstructorName, reqVO.getInstructorName())
            .eqIfPresent(CollectiveTalkRecordDO::getInstructorPosition, reqVO.getInstructorPosition())
            .eqIfPresent(CollectiveTalkRecordDO::getNumberOfRecipients, reqVO.getNumberOfRecipients())
            .eqIfPresent(CollectiveTalkRecordDO::getTalkEduTopic, reqVO.getTalkEduTopic())
            .eqIfPresent(CollectiveTalkRecordDO::getSummaryOfContent, reqVO.getSummaryOfContent())
            .eqIfPresent(CollectiveTalkRecordDO::getDetaineesResponse, reqVO.getDetaineesResponse())
            .eqIfPresent(CollectiveTalkRecordDO::getOperatorSfzh, reqVO.getOperatorSfzh())
            .eqIfPresent(CollectiveTalkRecordDO::getOperatorXm, reqVO.getOperatorXm())
            .betweenIfPresent(CollectiveTalkRecordDO::getOperatorTime, reqVO.getOperatorTime())
        .orderByDesc(CollectiveTalkRecordDO::getAddTime));    }


    }
