package com.rs.module.tem.controller.admin.video.recorder;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.tem.entity.talk.TalkTaskDO;
import com.rs.module.tem.service.talk.TalkRecordService;
import com.rs.module.tem.service.talk.TalkTaskService;
import com.rs.module.tem.service.video.recorder.VideoRecorderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@Log4j2
@RestController
@RequestMapping("/api/recorder")
@Api(tags = "谈话教育-谈话终端-录音录像")
public class VideoRecorderController {

    @Autowired
    private VideoRecorderService videoRecorderService;
    @Autowired
    TalkRecordService talkRecordService;
    @Autowired
    TalkTaskService talkTaskService;

    /**
     * 启动录像
     *
     * @param startRecording
     * @return
     */
    @ApiOperation(value = "启动录像")
    @PostMapping("/start")
    public CommonResult<RecordingTaskVO> startRecording(@Validated @RequestBody StartRecording startRecording) {
        try {
            TalkTaskDO talkTask = talkTaskService.getTalkTask(startRecording.getTalkId());
            if (talkTask == null) {
                return CommonResult.error("谈话任务不存在");
            }
            if (StringUtils.isBlank(startRecording.getOutputPath())) {
                startRecording.setOutputPath(System.getProperty("user.dir") + "/recordings/" + startRecording.getTalkId() + ".mp4");
            }
            RecordingTask task = videoRecorderService.startRecording(startRecording.getInputUrl(), startRecording.getOutputPath(), startRecording.getTalkId());
            RecordingTaskVO taskVO = new RecordingTaskVO();
            taskVO.setTaskId(task.getTaskId());
            return CommonResult.success(taskVO);
        } catch (Exception e) {
            log.error("启动失败", e);
        }
        return CommonResult.error("启动失败");
    }

    /**
     * 停止录像
     *
     * @param taskId
     * @return
     */
    @GetMapping("/stop/{taskId}")
    @ApiOperation(value = "停止录像")
    public CommonResult<Boolean> stopRecording(@PathVariable String taskId) {
        try {
            return CommonResult.success(videoRecorderService.stopRecording(taskId));
        } catch (Exception e) {
            log.error("停止失败", e);
        }
        return CommonResult.error("停止失败");
    }

    /**
     * 检查任务初始化状态
     *
     * @param taskId
     * @return
     */
    @GetMapping("/status/{taskId}")
    @ApiOperation(value = "检查任务初始化状态")
    public CommonResult<Boolean> checkTaskStatus(@PathVariable String taskId) {
        try {
            boolean initialized = videoRecorderService.isTaskInitialized(taskId);
            return CommonResult.success(initialized);
        } catch (Exception e) {
            log.error("检查状态失败", e);
        }
        return CommonResult.error("检查状态失败");
    }

    /**
     * 测试RTSP连接（临时测试接口）
     *
     * @param rtspUrl
     * @return
     */
    @GetMapping("/test-rtsp")
    @ApiOperation(value = "测试RTSP连接")
    public CommonResult<String> testRTSPConnection(@RequestParam String rtspUrl) {
        try {
            log.info("测试RTSP连接: {}", rtspUrl);

            // 使用诊断工具测试连接
            com.rs.module.tem.util.RTSPDiagnosticUtils.DiagnosticResult result =
                com.rs.module.tem.util.RTSPDiagnosticUtils.diagnoseRTSPConnection(rtspUrl);

            String report = result.getFullReport();

            if (result.isFfmpegConnectable()) {
                return CommonResult.success("连接成功!\n" + report);
            } else {
                return CommonResult.error("连接失败!\n" + report);
            }

        } catch (Exception e) {
            log.error("测试RTSP连接失败", e);
            return CommonResult.error("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试修复后的RTSP连接
     *
     * @param rtspUrl
     * @return
     */
    @GetMapping("/test-rtsp-fixed")
    @ApiOperation(value = "测试修复后的RTSP连接")
    public CommonResult<String> testFixedRTSPConnection(@RequestParam String rtspUrl) {
        try {
            log.info("测试修复后的RTSP连接: {}", rtspUrl);

            org.bytedeco.javacv.FFmpegFrameGrabber grabber = null;
            try {
                grabber = new org.bytedeco.javacv.FFmpegFrameGrabber(rtspUrl);

                // 使用新的优化配置
                com.rs.module.tem.util.FFmpegUtils.configureForSpecificDevice(grabber, 15, 10);

                long startTime = System.currentTimeMillis();
                grabber.start();
                long endTime = System.currentTimeMillis();

                // 获取流信息
                String info = String.format(
                    "连接成功!\n耗时: %d ms\n视频尺寸: %dx%d\n帧率: %.2f\n音频通道: %d",
                    endTime - startTime,
                    grabber.getImageWidth(),
                    grabber.getImageHeight(),
                    grabber.getFrameRate(),
                    grabber.getAudioChannels()
                );

                grabber.stop();
                return CommonResult.success(info);

            } finally {
                if (grabber != null) {
                    try {
                        grabber.release();
                    } catch (Exception ignored) {}
                }
            }

        } catch (Exception e) {
            log.error("测试修复后的RTSP连接失败", e);
            return CommonResult.error("连接失败: " + e.getMessage());
        }
    }

    /**
     * 获取录制任务详细状态
     *
     * @param taskId
     * @return
     */
    @GetMapping("/detail/{taskId}")
    @ApiOperation(value = "获取录制任务详细状态")
    public CommonResult<String> getTaskDetail(@PathVariable String taskId) {
        try {
            Optional<RecordingTask> taskOpt = videoRecorderService.getTask(taskId);
            if (!taskOpt.isPresent()) {
                return CommonResult.error("任务不存在");
            }

            RecordingTask task = taskOpt.get();
            StringBuilder detail = new StringBuilder();
            detail.append("任务ID: ").append(task.getTaskId()).append("\n");
            detail.append("运行状态: ").append(task.isRunning() ? "运行中" : "已停止").append("\n");
            detail.append("初始化状态: ").append(task.isInitialized() ? "已初始化" : "未初始化").append("\n");

            if (task.getInitializationError() != null) {
                detail.append("初始化错误: ").append(task.getInitializationError().getMessage()).append("\n");
            }

            return CommonResult.success(detail.toString());

        } catch (Exception e) {
            log.error("获取任务详情失败", e);
            return CommonResult.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 发送心跳
     *
     * @param taskId
     * @return
     */
    @PostMapping("/heartbeat/{taskId}")
    @ApiOperation(value = "发送心跳")
    public CommonResult<Boolean> sendHeartbeat(@PathVariable String taskId) {
        log.debug("Received heartbeat for task: {}", taskId);
        try {
            return CommonResult.success(videoRecorderService.sendHeartbeat(taskId));
        } catch (Exception e) {
            log.error("心跳失败：{}", e.getMessage(), e);
        }
        return CommonResult.error("心跳失败");
    }

//    @GetMapping("/task/{taskId}")
//    public Map<String, Object> getTask(@PathVariable String taskId) {
//        Map<String, Object> response = new HashMap<>();
//        try {
//            Optional<RecordingTask> taskOptional = videoRecorderService.getTask(taskId);
//            if (taskOptional.isPresent()) {
//                response.put("success", true);
//                response.put("task", taskOptional.get());
//            } else {
//                response.put("success", false);
//                response.put("message", "Task not found");
//            }
//        } catch (Exception e) {
//            response.put("success", false);
//            response.put("message", "Failed to get task: " + e.getMessage());
//        }
//        return response;
//    }
//
//    @GetMapping("/tasks")
//    public Map<String, Object> getAllTasks() {
//        Map<String, Object> response = new HashMap<>();
//        try {
//            List<RecordingTask> tasks = videoRecorderService.getAllTasks();
//            response.put("success", true);
//            response.put("tasks", tasks);
//        } catch (Exception e) {
//            response.put("success", false);
//            response.put("message", "Failed to get tasks: " + e.getMessage());
//        }
//        return response;
//    }
}

