package com.rs.module.tem.entity.talk;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import com.rs.module.base.tag.annotation.AutoTag;
import com.rs.module.base.tag.annotation.MapperClass;
import com.rs.module.base.tag.annotation.TagCondition;
import com.rs.module.base.tag.annotation.TagRule;
import com.rs.module.base.tag.enums.TagConditionType;
import com.rs.module.base.tag.enums.TagEnum;
import com.rs.module.tem.dao.talk.TalkTaskDao;
import lombok.*;

/**
 * 谈话教育-谈话任务 DO
 *
 * <AUTHOR>
 */
@TableName("tem_talk_task")
@KeySequence("tem_talk_task_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@AutoTag(businessType = "tem_talk_task")
@MapperClass(TalkTaskDao.class)
public class TalkTaskDO extends BaseDO {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 谈话记录编号(规则：T+9位单位编号+8位日期+6位序号)
     */
    private String talkCode;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 在押人员名称，顾送药专药专用的人
     */
    private String ryxm;
    /**
     * 谈话原因（字典：）
     */
    @TagRule(
            tag = TagEnum.MALE,
            conditions = {
                    @TagCondition(
                            type = TagConditionType.NOT_EQUALS
                    )
            }
    )
    private String talkReason;
    /**
     * 谈话管教用户身份证号
     */
    private String talkUserSfzh;
    /**
     * 谈话管教用户身份证号
     */
    private String talkUserName;
    /**
     * 监室号
     */
    private String jsh;

    /**
     * 来源
     */
    private String taskSource;

    /**
     * 视频地址
     */
    private String videoUrl;
}
