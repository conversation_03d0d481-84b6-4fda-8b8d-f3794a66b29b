package com.rs.module.tem.dao.video;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.tem.controller.admin.video.vo.VideoDeviceListReqVO;
import com.rs.module.tem.controller.admin.video.vo.VideoDevicePageReqVO;
import com.rs.module.tem.entity.video.VideoDeviceDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 谈话教育-终端管理 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface VideoDeviceDao extends IBaseDao<VideoDeviceDO> {


    default PageResult<VideoDeviceDO> selectPage(VideoDevicePageReqVO reqVO) {
        Page<VideoDeviceDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<VideoDeviceDO> wrapper = new LambdaQueryWrapperX<VideoDeviceDO>()
                .eqIfPresent(VideoDeviceDO::getPrisonId, reqVO.getPrisonId())
                .likeIfPresent(VideoDeviceDO::getDeviceName, reqVO.getDeviceName())
                .eqIfPresent(VideoDeviceDO::getDeviceModel, reqVO.getDeviceModel())
                .eqIfPresent(VideoDeviceDO::getTalkRoomId, reqVO.getTalkRoomId())
                .likeIfPresent(VideoDeviceDO::getTalkRoomName, reqVO.getTalkRoomName())
                .eqIfPresent(VideoDeviceDO::getPrisonerDeviceIp, reqVO.getPrisonerDeviceIp())
                .eqIfPresent(VideoDeviceDO::getWorkerDeviceIp, reqVO.getWorkerDeviceIp());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(VideoDeviceDO::getAddTime);
        }
        Page<VideoDeviceDO> videoDevicePage = selectPage(page, wrapper);
        return new PageResult<>(videoDevicePage.getRecords(), videoDevicePage.getTotal());
    }

    default List<VideoDeviceDO> selectList(VideoDeviceListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<VideoDeviceDO>()
                .eqIfPresent(VideoDeviceDO::getPrisonId, reqVO.getPrisonId())
                .likeIfPresent(VideoDeviceDO::getDeviceName, reqVO.getDeviceName())
                .eqIfPresent(VideoDeviceDO::getDeviceModel, reqVO.getDeviceModel())
                .eqIfPresent(VideoDeviceDO::getTalkRoomId, reqVO.getTalkRoomId())
                .likeIfPresent(VideoDeviceDO::getTalkRoomName, reqVO.getTalkRoomName())
                .eqIfPresent(VideoDeviceDO::getPrisonerDeviceIp, reqVO.getPrisonerDeviceIp())
                .eqIfPresent(VideoDeviceDO::getWorkerDeviceIp, reqVO.getWorkerDeviceIp())
                .orderByDesc(VideoDeviceDO::getAddTime));
    }
}
