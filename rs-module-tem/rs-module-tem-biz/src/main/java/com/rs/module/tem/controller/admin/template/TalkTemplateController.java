package com.rs.module.tem.controller.admin.template;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.tem.controller.admin.template.vo.TalkTemplateListReqVO;
import com.rs.module.tem.controller.admin.template.vo.TalkTemplatePageReqVO;
import com.rs.module.tem.controller.admin.template.vo.TalkTemplateRespVO;
import com.rs.module.tem.controller.admin.template.vo.TalkTemplateSaveReqVO;
import com.rs.module.tem.entity.template.TalkTemplateContentDO;
import com.rs.module.tem.entity.template.TalkTemplateDO;
import com.rs.module.tem.service.template.TalkTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 谈话教育-谈话模板")
@RestController
@RequestMapping("/tem/template/talkTemplate")
@Validated
public class TalkTemplateController {

    @Resource
    private TalkTemplateService talkTemplateService;

    @PostMapping("/create")
    @ApiOperation(value = "创建谈话教育-谈话模板")
    @LogRecordAnnotation(bizModule = "tem:talkTemplate:create", operateType = LogOperateType.CREATE, title = "创建谈话教育-谈话模板",
    success = "创建谈话教育-谈话模板成功", fail = "创建谈话教育-谈话模板失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createTalkTemplate(@Valid @RequestBody TalkTemplateSaveReqVO createReqVO) {
        return success(talkTemplateService.createTalkTemplate(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新谈话教育-谈话模板")
    @LogRecordAnnotation(bizModule = "tem:talkTemplate:update", operateType = LogOperateType.UPDATE, title = "更新谈话教育-谈话模板",
    success = "更新谈话教育-谈话模板成功", fail = "更新谈话教育-谈话模板失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateTalkTemplate(@Valid @RequestBody TalkTemplateSaveReqVO updateReqVO) {
        talkTemplateService.updateTalkTemplate(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除谈话教育-谈话模板")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "tem:talkTemplate:delete", operateType = LogOperateType.DELETE, title = "删除谈话教育-谈话模板",
    success = "删除谈话教育-谈话模板成功", fail = "删除谈话教育-谈话模板失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteTalkTemplate(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           talkTemplateService.deleteTalkTemplate(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得谈话教育-谈话模板")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<TalkTemplateRespVO> getTalkTemplate(@RequestParam("id") String id) {
        TalkTemplateDO talkTemplate = talkTemplateService.getTalkTemplate(id);
        return success(BeanUtils.toBean(talkTemplate, TalkTemplateRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得谈话教育-谈话模板分页")
    public CommonResult<PageResult<TalkTemplateRespVO>> getTalkTemplatePage(@Valid @RequestBody TalkTemplatePageReqVO pageReqVO) {
        PageResult<TalkTemplateDO> pageResult = talkTemplateService.getTalkTemplatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TalkTemplateRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得谈话教育-谈话模板列表")
    public CommonResult<List<TalkTemplateRespVO>> getTalkTemplateList(@Valid @RequestBody TalkTemplateListReqVO listReqVO) {
        List<TalkTemplateDO> list = talkTemplateService.getTalkTemplateList(listReqVO);
        return success(BeanUtils.toBean(list, TalkTemplateRespVO.class));
    }
    // ==================== 子表（谈话教育-谈话模板内容） ====================

    @GetMapping("/talk-template-content/list-by-template-id")
    @ApiOperation(value = "获得谈话教育-谈话模板内容列表")
    @ApiImplicitParam(name = "templateId", value = "谈话模板id")
    public CommonResult<List<TalkTemplateContentDO>> getTalkTemplateContentListByTemplateId(@RequestParam("templateId") String templateId) {
        return success(talkTemplateService.getTalkTemplateContentListByTemplateId(templateId));
    }

}
