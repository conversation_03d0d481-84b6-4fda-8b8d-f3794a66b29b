package com.rs.module.tem.controller.admin.video.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 谈话教育-终端管理列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class VideoDeviceListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监所id")
    private String prisonId;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("设备型号")
    private String deviceModel;

    @ApiModelProperty("谈话室")
    private String talkRoomId;

    @ApiModelProperty("谈话室名称")
    private String talkRoomName;

    @ApiModelProperty("设备ip（被监管人侧）")
    private String prisonerDeviceIp;

    @ApiModelProperty("设备ip（工作人员侧）")
    private String workerDeviceIp;

}
