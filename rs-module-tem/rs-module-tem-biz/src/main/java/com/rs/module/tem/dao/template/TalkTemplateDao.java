package com.rs.module.tem.dao.template;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.tem.entity.template.TalkTemplateDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.tem.controller.admin.template.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 谈话教育-谈话模板 Dao
*
* <AUTHOR>
*/
@Mapper
public interface TalkTemplateDao extends IBaseDao<TalkTemplateDO> {


    default PageResult<TalkTemplateDO> selectPage(TalkTemplatePageReqVO reqVO) {
        Page<TalkTemplateDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<TalkTemplateDO> wrapper = new LambdaQueryWrapperX<TalkTemplateDO>()
            .eqIfPresent(TalkTemplateDO::getUseBusiness, reqVO.getUseBusiness())
            .eqIfPresent(TalkTemplateDO::getTemplateType, reqVO.getTemplateType())
            .eqIfPresent(TalkTemplateDO::getContent, reqVO.getContent())
            .likeIfPresent(TalkTemplateDO::getTemplateName, reqVO.getTemplateName())
            .betweenIfPresent(TalkTemplateDO::getLastUseTime, reqVO.getLastUseTime())
            .eqIfPresent(TalkTemplateDO::getUseCount, reqVO.getUseCount())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(TalkTemplateDO::getAddTime);
        }
        Page<TalkTemplateDO> talkTemplatePage = selectPage(page, wrapper);
        return new PageResult<>(talkTemplatePage.getRecords(), talkTemplatePage.getTotal());
    }
    default List<TalkTemplateDO> selectList(TalkTemplateListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TalkTemplateDO>()
            .eqIfPresent(TalkTemplateDO::getUseBusiness, reqVO.getUseBusiness())
            .eqIfPresent(TalkTemplateDO::getTemplateType, reqVO.getTemplateType())
            .eqIfPresent(TalkTemplateDO::getContent, reqVO.getContent())
            .likeIfPresent(TalkTemplateDO::getTemplateName, reqVO.getTemplateName())
            .betweenIfPresent(TalkTemplateDO::getLastUseTime, reqVO.getLastUseTime())
            .eqIfPresent(TalkTemplateDO::getUseCount, reqVO.getUseCount())
        .orderByDesc(TalkTemplateDO::getAddTime));    }


    }
