package com.rs.module.tem.entity.talk;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import java.util.Date;

/**
 * 谈话教育-个人谈话教育 DO
 *
 * <AUTHOR>
 */
@TableName("tem_talk_record")
@KeySequence("tem_talk_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor

public class TalkRecordDO extends BaseDO {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 记录类型 （字典： ZD_THJY_THFS）
     */
    private String recordType;
    /**
     * 谈话记录编号(规则：T+9位单位编号+8位日期+6位序号)
     */
    private String talkCode;
    /**
     * 谈话原因（字典：ZD_THJY_THYY）
     */

    private String talkReason;
    /**
     * 谈话室id
     */
    private String talkRoomId;
    /**
     * 谈话室名称
     */
    private String talkRoomName;
    /**
     * 谈话开始时间
     */
    private Date startTime;
    /**
     * 谈话结束时间
     */
    private Date endTime;
    /**
     * 谈话内容文本
     */
    private String talkContent;
    /**
     * 谈话小结
     */
    private String talkSummary;
    /**
     * 标签id，多个,分割
     */
    private String tagId;
    /**
     * 标签名称，多个,分割
     */
    private String tagName;
    /**
     * 心理评估（字典：	ZD_THJY_THPJ）
     */
    private String psychologyAssess;
    /**
     * 岗位协同（字典：	ZD_THJY_GWXT）
     */
    private String jobCollaboration;
    /**
     * 推送用户，多个,分割
     */
    private String pushUserid;
    /**
     * 推送用户名称，多个,分割
     */
    private String pushUserName;
    /**
     * 签名图片地址
     */
    private String signUrl;
    /**
     * 捺印图片地址
     */
    private String sealUrl;
    /**
     * 签名捺印文件地址
     */
    private String signSealUrl;
    /**
     * 视频url2
     */
    private String videoUrl;
    /**
     * 视频url
     */
    private String videoUrl2;

    /**
     * 来源
     */
    private String taskSource;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 时长
     **/
    private String timer;


}
