package com.rs.module.tem.controller.admin.talk;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.tem.controller.admin.talk.vo.CollectiveTalkRecordRespVO;
import com.rs.module.tem.controller.admin.talk.vo.CollectiveTalkRecordSaveReqVO;
import com.rs.module.tem.entity.talk.CollectiveTalkRecordDO;
import com.rs.module.tem.service.talk.CollectiveTalkRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 谈话教育-集体谈话教育")
@RestController
@RequestMapping("/tem/talk/collectiveTalkRecord")
@Validated
public class CollectiveTalkRecordController {

    @Resource
    private CollectiveTalkRecordService collectiveTalkRecordService;

    @PostMapping("/create")
    @ApiOperation(value = "创建谈话教育-集体谈话教育")
    @LogRecordAnnotation(bizModule = "tem:collectiveTalkRecord:create", operateType = LogOperateType.CREATE, title = "创建谈话教育-集体谈话教育", success = "创建谈话教育-集体谈话教育成功", fail = "创建谈话教育-集体谈话教育失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createCollectiveTalkRecord(
            @Valid @RequestBody CollectiveTalkRecordSaveReqVO createReqVO) {
        return success(collectiveTalkRecordService.createCollectiveTalkRecord(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新谈话教育-集体谈话教育")
    @LogRecordAnnotation(bizModule = "tem:collectiveTalkRecord:update", operateType = LogOperateType.UPDATE, title = "更新谈话教育-集体谈话教育", success = "更新谈话教育-集体谈话教育成功", fail = "更新谈话教育-集体谈话教育失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateCollectiveTalkRecord(
            @Valid @RequestBody CollectiveTalkRecordSaveReqVO updateReqVO) {
        collectiveTalkRecordService.updateCollectiveTalkRecord(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除谈话教育-集体谈话教育")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "tem:collectiveTalkRecord:delete", operateType = LogOperateType.DELETE, title = "删除谈话教育-集体谈话教育", success = "删除谈话教育-集体谈话教育成功", fail = "删除谈话教育-集体谈话教育失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteCollectiveTalkRecord(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {

            collectiveTalkRecordService.deleteCollectiveTalkRecord(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得谈话教育-集体谈话教育")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<CollectiveTalkRecordRespVO> getCollectiveTalkRecord(@RequestParam("id") String id) {
        CollectiveTalkRecordDO collectiveTalkRecord = collectiveTalkRecordService.getCollectiveTalkRecord(id);
        return success(BeanUtils.toBean(collectiveTalkRecord, CollectiveTalkRecordRespVO.class));
    }

    // @PostMapping("/page")
    // @ApiOperation(value = "获得谈话教育-集体谈话教育分页")
    // public CommonResult<PageResult<CollectiveTalkRecordRespVO>>
    // getCollectiveTalkRecordPage(@Valid @RequestBody CollectiveTalkRecordPageReqVO
    // pageReqVO) {
    // PageResult<CollectiveTalkRecordDO> pageResult =
    // collectiveTalkRecordService.getCollectiveTalkRecordPage(pageReqVO);
    // return success(BeanUtils.toBean(pageResult,
    // CollectiveTalkRecordRespVO.class));
    // }
    //
    // @PostMapping("/list")
    // @ApiOperation(value = "获得谈话教育-集体谈话教育列表")
    // public CommonResult<List<CollectiveTalkRecordRespVO>>
    // getCollectiveTalkRecordList(@Valid @RequestBody CollectiveTalkRecordListReqVO
    // listReqVO) {
    // List<CollectiveTalkRecordDO> list =
    // collectiveTalkRecordService.getCollectiveTalkRecordList(listReqVO);
    // return success(BeanUtils.toBean(list, CollectiveTalkRecordRespVO.class));
    // }
}
