package com.rs.module.tem.config;

import lombok.extern.log4j.Log4j2;
import org.bytedeco.javacv.FFmpegLogCallback;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

import static org.bytedeco.ffmpeg.global.avutil.*;

/**
 * FFmpeg配置类
 * 
 * <AUTHOR>
 */
@Configuration
@Log4j2
public class FFmpegConfig {

    @PostConstruct
    public void initFFmpeg() {
        // 设置FFmpeg日志回调
        FFmpegLogCallback.set();

        // 设置日志级别为警告，减少日志输出
        FFmpegLogCallback.setLevel(AV_LOG_WARNING);

        log.info("FFmpeg配置初始化完成");
    }
}
