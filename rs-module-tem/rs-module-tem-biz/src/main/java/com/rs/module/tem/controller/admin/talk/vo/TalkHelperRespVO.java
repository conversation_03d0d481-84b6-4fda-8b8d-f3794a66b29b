package com.rs.module.tem.controller.admin.talk.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 谈话教育-谈话提问助手 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TalkHelperRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("谈话助手内容")
    private String helperContent;
}
