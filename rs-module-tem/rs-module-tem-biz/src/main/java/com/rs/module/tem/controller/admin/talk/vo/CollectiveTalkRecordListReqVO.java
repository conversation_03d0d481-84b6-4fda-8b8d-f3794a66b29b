package com.rs.module.tem.controller.admin.talk.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 谈话教育-集体谈话教育列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CollectiveTalkRecordListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("谈话记录编号(规则：T+9位单位编号+8位日期+6位序号)")
    private String talkCode;

    @ApiModelProperty("集体谈话教育的时间段")
    private Date[] talkEduStartTime;

    @ApiModelProperty("集体谈话教育的时间段")
    private Date[] talkEduEndTime;

    @ApiModelProperty("集体谈话教育所涉及的范围")
    private String talkEduScope;

    @ApiModelProperty("进行授课的人员姓名")
    private String instructorName;

    @ApiModelProperty("授课人的职务")
    private String instructorPosition;

    @ApiModelProperty("接受集体谈话教育的人数")
    private Integer numberOfRecipients;

    @ApiModelProperty("集体谈话教育的课题")
    private String talkEduTopic;

    @ApiModelProperty("授课内容的摘要")
    private String summaryOfContent;

    @ApiModelProperty("在押人员对集体谈话教育的反应")
    private String detaineesResponse;

    @ApiModelProperty("登记经办人")
    private String operatorSfzh;

    @ApiModelProperty("登记经办人姓名")
    private String operatorXm;

    @ApiModelProperty("办理会见登记的时间")
    private Date[] operatorTime;

}
