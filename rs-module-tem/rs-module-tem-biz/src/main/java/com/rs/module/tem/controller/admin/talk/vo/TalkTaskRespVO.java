package com.rs.module.tem.controller.admin.talk.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.common.annotation.Format;
import com.rs.module.base.entity.RyVO;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 谈话教育-谈话任务 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TalkTaskRespVO extends RyVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("谈话记录编号(规则：T+9位单位编号+8位日期+6位序号)")
    private String talkCode;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("在押人员名称，顾送药专药专用的人")
    private String ryxm;
    @ApiModelProperty("谈话原因（字典：）")
    private String talkReason;
    @ApiModelProperty("谈话管教用户身份证号")
    private String talkUserSfzh;
    @ApiModelProperty("谈话管教用户身份证号")
    private String talkUserName;
    @ApiModelProperty("监室号")
    private String jsh;
    @Format(service = AreaPrisonRoomService.class, method = "getAreaPrisonRoomName", value = "jsh")
    private String roomName;
    /**
     * 来源
     */
    private String taskSource;

}
