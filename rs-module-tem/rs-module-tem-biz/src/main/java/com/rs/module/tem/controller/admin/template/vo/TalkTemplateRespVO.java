package com.rs.module.tem.controller.admin.template.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.annotation.Query;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 谈话教育-谈话模板 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TalkTemplateRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("适用业务（1：个人，2：集体）")
    private String useBusiness;
    @ApiModelProperty("模板类型（字典：ZD_THJY_THYY）")
    private String templateType;
    @ApiModelProperty("谈话模板内容")
    private String content;
    @ApiModelProperty("谈话模板名称")
    private String templateName;
    @ApiModelProperty("最后使用时间")
    private Date lastUseTime;
    @ApiModelProperty("使用次数")
    private Integer useCount;
    @Query(sql = "select * from tem_template_content where template_id = '${id}' order by px asc", beanClass = TalkTemplateContentRespVO.class)
    private List<TalkTemplateContentRespVO> talkTemplateContents;
}
