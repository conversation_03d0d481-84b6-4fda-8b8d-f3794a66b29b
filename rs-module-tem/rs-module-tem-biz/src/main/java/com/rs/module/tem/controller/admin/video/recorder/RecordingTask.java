package com.rs.module.tem.controller.admin.video.recorder;

import cn.hutool.core.io.FileUtil;
import com.rs.framework.common.util.spring.SpringUtils;
import com.rs.module.oss.utils.UploadUtils;
import com.rs.module.tem.entity.talk.TalkTaskDO;
import com.rs.module.tem.monitor.RecordingTaskMonitor;
import com.rs.module.tem.service.talk.TalkTaskService;
import com.rs.module.tem.util.EncoderTestUtils;
import com.rs.module.tem.util.FFmpegUtils;
import lombok.extern.log4j.Log4j2;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.Frame;

import java.io.File;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Log4j2
public class RecordingTask implements Runnable {

    private final String inputUrl;
    private final String outputPath;
    private final String taskId;
    private final AtomicBoolean isRunning = new AtomicBoolean(true);
    private volatile long lastHeartbeatTime = System.currentTimeMillis();

    private FFmpegFrameGrabber grabber;
    private FFmpegFrameRecorder recorder;

    private String talkId;

    // 连接超时时间（秒）
    private static final int CONNECTION_TIMEOUT = 10;
    // 读取超时时间（秒）
    private static final int READ_TIMEOUT = 5;
    // 最大重试次数
    private static final int MAX_RETRY_COUNT = 3;

    // 初始化状态
    private volatile boolean isInitialized = false;
    private volatile Exception initializationError = null;
    private CompletableFuture<Void> initializationFuture;

    // 性能监控
    private RecordingTaskMonitor monitor;

    public RecordingTask(String inputUrl, String outputPath) {
        this.inputUrl = convertToRtsp(inputUrl);
        this.outputPath = outputPath;
        this.taskId = java.util.UUID.randomUUID().toString();

        // 验证输入URL
        if (!FFmpegUtils.isValidStreamUrl(inputUrl)) {
            throw new IllegalArgumentException("无效的流媒体URL: " + inputUrl);
        }

        // 初始化性能监控
        this.monitor = SpringUtils.getBean(RecordingTaskMonitor.class);
        if (monitor != null) {
            monitor.startMonitoring(taskId);
        }

        // 启动异步初始化
        startAsyncInitialization();
    }

    /**
     * 启动异步初始化
     */
    private void startAsyncInitialization() {
        initializationFuture = CompletableFuture.runAsync(() -> {
            try {
                log.info("开始异步初始化录制任务，输入URL: {}", inputUrl);
                if (monitor != null) {
                    monitor.recordInitializationStart(taskId);
                }

                initializeGrabberWithRetry();
                createOutputDirectory();
                initializeRecorder();
                isInitialized = true;

                if (monitor != null) {
                    monitor.recordInitializationEnd(taskId, true, null);
                }
                log.info("录制任务异步初始化完成");
            } catch (Exception e) {
                initializationError = e;
                if (monitor != null) {
                    monitor.recordInitializationEnd(taskId, false, e.getMessage());
                }
                log.error("录制任务异步初始化失败: {}", e.getMessage(), e);
            }
        });
    }

    /**
     * 等待初始化完成
     */
    public boolean waitForInitialization(long timeoutSeconds) {
        try {
            initializationFuture.get(timeoutSeconds, TimeUnit.SECONDS);
            return isInitialized && initializationError == null;
        } catch (Exception e) {
            log.error("等待初始化超时或失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查是否已初始化
     */
    public boolean isInitialized() {
        return isInitialized;
    }

    /**
     * 获取初始化错误
     */
    public Exception getInitializationError() {
        return initializationError;
    }

    public String getTaskId() {
        return taskId;
    }

    public void updateHeartbeat() {
        this.lastHeartbeatTime = System.currentTimeMillis();
    }

    public boolean isHeartbeatTimeout(long timeoutSeconds) {
        return System.currentTimeMillis() - lastHeartbeatTime > timeoutSeconds * 1000;
    }

    @Override
    public void run() {
        log.info("开始录制任务，输入URL: {}, 输出路径: {}", inputUrl, outputPath);

        try {
            // 等待异步初始化完成
            if (!waitForInitialization(30)) { // 等待最多30秒
                if (initializationError != null) {
                    throw new Exception("初始化失败", initializationError);
                } else {
                    throw new Exception("初始化超时");
                }
            }

            log.info("录制器初始化完成，开始录制视频流");

            if (monitor != null) {
                monitor.recordRecordingStart(taskId);
            }

            // 录制循环
            recordVideoLoop();

        } catch (Exception e) {
            log.error("录制任务异常：{}", e.getMessage(), e);
        } finally {
            try {
                if (monitor != null) {
                    monitor.recordRecordingEnd(taskId);
                }
                releaseResources();
                // 保存视频
                uploadVideoFile();
            } finally {
                // 清理临时文件
                FileUtil.del(outputPath);
                if (monitor != null) {
                    monitor.stopMonitoring(taskId);
                }
                log.info("录制任务结束，临时文件已清理");
            }
        }
    }

    /**
     * 使用重试机制初始化抓取器
     */
    private void initializeGrabberWithRetry() throws Exception {
        Exception lastException = null;

        for (int attempt = 1; attempt <= MAX_RETRY_COUNT; attempt++) {
            try {
                log.info("第 {} 次尝试连接视频流: {}", attempt, inputUrl);

                grabber = new FFmpegFrameGrabber(inputUrl);

                long startTime = System.currentTimeMillis();

                // 使用针对特定设备的优化配置
                FFmpegUtils.configureForSpecificDevice(grabber, CONNECTION_TIMEOUT, READ_TIMEOUT);

                try {
                    grabber.start();
                    long endTime = System.currentTimeMillis();
                    log.info("视频流连接成功，耗时: {} ms", endTime - startTime);
                    return; // 成功则退出重试循环
                } catch (Exception e) {
                    // 如果特定配置失败，尝试不同的传输协议
                    log.warn("特定配置连接失败，尝试其他传输协议: {}", e.getMessage());
                    grabber.stop();

                    String workingTransport = FFmpegUtils.tryDifferentTransports(
                            grabber, CONNECTION_TIMEOUT, READ_TIMEOUT);

                    if (workingTransport != null) {
                        long endTime = System.currentTimeMillis();
                        log.info("视频流连接成功，使用传输协议: {}, 耗时: {} ms", workingTransport, endTime - startTime);
                        return; // 成功则退出重试循环
                    } else {
                        throw new Exception("所有传输协议都连接失败");
                    }
                }

            } catch (Exception e) {
                lastException = e;
                log.warn("第 {} 次连接失败: {}", attempt, e.getMessage());

                // 清理失败的grabber
                if (grabber != null) {
                    try {
                        grabber.release();
                    } catch (Exception ignored) {
                    }
                    grabber = null;
                }

                // 如果不是最后一次尝试，等待后重试
                if (attempt < MAX_RETRY_COUNT) {
                    try {
                        Thread.sleep(1000 * attempt); // 递增等待时间
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new Exception("连接被中断", ie);
                    }
                }
            }
        }

        throw new Exception("连接视频流失败，已重试 " + MAX_RETRY_COUNT + " 次", lastException);
    }

    /**
     * 创建输出目录
     */
    private void createOutputDirectory() {
        File outputFile = new File(outputPath);
        File parentDir = outputFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            boolean created = parentDir.mkdirs();
            log.info("创建输出目录: {}, 结果: {}", parentDir.getAbsolutePath(), created);
        }
    }

    /**
     * 初始化录制器
     */
    private void initializeRecorder() throws Exception {
        log.info("初始化录制器，视频尺寸: {}x{}, 音频通道: {}, 帧率: {}",
                grabber.getImageWidth(), grabber.getImageHeight(),
                grabber.getAudioChannels(), grabber.getFrameRate());

        // 设置帧率，确保不为0
        double frameRate = grabber.getFrameRate();
        if (frameRate <= 0) {
            frameRate = 25.0; // 默认25fps
            log.warn("源流帧率无效，使用默认帧率: {}", frameRate);
        }

        // 使用编码器测试工具创建兼容的录制器
        recorder = EncoderTestUtils.createCompatibleRecorder(
                outputPath,
                grabber.getImageWidth(),
                grabber.getImageHeight(),
                frameRate);

        if (recorder == null) {
            throw new Exception("无法创建兼容的录制器，所有编码器配置都失败");
        }

        // 设置音频（如果有）
        int sampleRate = grabber.getSampleRate();
        int audioChannels = grabber.getAudioChannels();
        if (sampleRate > 0 && audioChannels > 0) {
            try {
                // 重新创建录制器包含音频
                recorder.stop();
                recorder.release();

                recorder = new FFmpegFrameRecorder(outputPath,
                        grabber.getImageWidth(),
                        grabber.getImageHeight(),
                        audioChannels);

                recorder.setVideoCodec(org.bytedeco.ffmpeg.global.avcodec.AV_CODEC_ID_H264);
                recorder.setFormat("mp4");
                recorder.setFrameRate(frameRate);
                recorder.setSampleRate(sampleRate);
                recorder.setAudioCodec(org.bytedeco.ffmpeg.global.avcodec.AV_CODEC_ID_AAC);
                recorder.setPixelFormat(org.bytedeco.ffmpeg.global.avutil.AV_PIX_FMT_YUV420P);

                // 使用最简配置
                int bitrate = FFmpegUtils.getRecommendedBitrate(
                        grabber.getImageWidth(), grabber.getImageHeight());
                FFmpegUtils.configureMinimalRecorderOptions(recorder, bitrate);

                recorder.start();
                log.info("录制器启动成功（包含音频）");
            } catch (Exception e) {
                log.warn("音频配置失败，使用纯视频录制: {}", e.getMessage());
                // 如果音频配置失败，重新创建纯视频录制器
                recorder = EncoderTestUtils.createCompatibleRecorder(
                        outputPath,
                        grabber.getImageWidth(),
                        grabber.getImageHeight(),
                        frameRate);
                if (recorder == null) {
                    throw new Exception("创建纯视频录制器也失败");
                }
            }
        } else {
            log.info("录制器启动成功（纯视频）");
        }
    }

    /**
     * 录制视频循环
     */
    private void recordVideoLoop() throws Exception {
        long frameCount = 0;
        long lastLogTime = System.currentTimeMillis();
        long lastFrameTime = System.currentTimeMillis();
        int consecutiveNullFrames = 0;
        final int MAX_NULL_FRAMES = 100; // 最大连续空帧数
        final long FRAME_TIMEOUT = 30000; // 30秒无帧超时

        while (isRunning.get()) {
            try {
                Frame frame = grabber.grab();
                long currentTime = System.currentTimeMillis();

                if (frame != null) {
                    recorder.record(frame);
                    frameCount++;
                    lastFrameTime = currentTime;
                    consecutiveNullFrames = 0; // 重置空帧计数

                    if (monitor != null) {
                        monitor.incrementFrameCount(taskId);
                    }

                    // 每10秒记录一次状态
                    if (currentTime - lastLogTime > 10000) {
                        double fps = frameCount * 1000.0 / (currentTime - (lastLogTime - 10000));
                        log.info("录制进行中，已录制帧数: {}, 当前帧率: {:.2f} fps", frameCount, fps);
                        lastLogTime = currentTime;
                    }
                } else {
                    consecutiveNullFrames++;

                    // 检查是否超过最大空帧数或超时
                    if (consecutiveNullFrames >= MAX_NULL_FRAMES) {
                        log.warn("连续获取到 {} 个空帧，可能是流中断", consecutiveNullFrames);
                        break;
                    }

                    if (currentTime - lastFrameTime > FRAME_TIMEOUT) {
                        log.warn("超过 {} 秒未获取到帧，可能是流超时", FRAME_TIMEOUT / 1000);
                        break;
                    }

                    // 短暂等待，避免CPU占用过高
                    Thread.sleep(10);
                }

            } catch (Exception e) {
                log.error("录制过程中发生异常: {}", e.getMessage());

                // 如果是网络相关异常，尝试重连
                if (isNetworkException(e)) {
                    log.info("检测到网络异常，尝试重连...");
                    if (attemptReconnect()) {
                        log.info("重连成功，继续录制");
                        continue;
                    } else {
                        log.error("重连失败，停止录制");
                        break;
                    }
                } else {
                    // 其他异常，停止录制
                    throw e;
                }
            }
        }

        log.info("录制循环结束，总帧数: {}, 平均帧率: {:.2f} fps",
                frameCount,
                frameCount * 1000.0 / (System.currentTimeMillis() - (lastLogTime - 10000)));
    }

    /**
     * 判断是否为网络相关异常
     */
    private boolean isNetworkException(Exception e) {
        String message = e.getMessage().toLowerCase();
        return message.contains("timeout") ||
               message.contains("connection") ||
               message.contains("network") ||
               message.contains("socket");
    }

    /**
     * 尝试重连
     */
    private boolean attemptReconnect() {
        try {
            log.info("开始重连RTSP流...");

            // 释放当前连接
            if (grabber != null) {
                try {
                    grabber.stop();
                } catch (Exception ignored) {}
            }

            // 重新初始化
            grabber = new FFmpegFrameGrabber(inputUrl);
            FFmpegUtils.configureForSpecificDevice(grabber, CONNECTION_TIMEOUT, READ_TIMEOUT);
            grabber.start();

            log.info("RTSP流重连成功");
            return true;

        } catch (Exception e) {
            log.error("RTSP流重连失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 上传视频文件
     */
    private void uploadVideoFile() {
        try {
            if (talkId != null && new File(outputPath).exists()) {
                TalkTaskService taskService = SpringUtils.getBean(TalkTaskService.class);
                String upload = UploadUtils.upload(new File(outputPath));
                taskService.lambdaUpdate()
                        .set(TalkTaskDO::getVideoUrl, upload)
                        .eq(TalkTaskDO::getId, talkId)
                        .update();
                log.info("视频文件上传成功: {}", upload);
            }
        } catch (Exception e) {
            log.error("视频文件上传失败", e);
        }
    }

    public void stopRecording() {
        isRunning.set(false);
    }

    private void releaseResources() {
        try {
            if (recorder != null) {
                recorder.stop();
                recorder.release();
            }
        } catch (Exception e) {
            log.error("释放录制器失败：" + e.getMessage());
        }

        try {
            if (grabber != null) {
                grabber.stop();
                grabber.release();
            }
        } catch (Exception e) {
            log.error("释放抓取器失败：" + e.getMessage(), e);
        }
    }

    public boolean isRunning() {
        return isRunning.get();
    }

    public void setTalkId(String talkId) {
        this.talkId = talkId;
    }

    public void setRunning(boolean isRunning) {
        this.isRunning.set(isRunning);
    }
    public  String convertToRtsp(String url) {
        // 使用正则表达式匹配HTTP URL中的IP和端口
        Pattern pattern = Pattern.compile("http://([^:/]+):(\\d+)/.*");
        Matcher matcher = pattern.matcher(url);

        if (matcher.matches()) {
            String ip = matcher.group(1);
            String port = matcher.group(2);

            // 转换为RTSP URL
            return "rtsp://" + ip + ":554/stream07";
        } else {
            throw new IllegalArgumentException("Invalid HTTP URL format");
        }
    }
}
