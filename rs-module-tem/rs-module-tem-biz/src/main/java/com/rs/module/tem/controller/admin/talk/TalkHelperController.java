package com.rs.module.tem.controller.admin.talk;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.tem.controller.admin.talk.vo.TalkHelperRespVO;
import com.rs.module.tem.controller.admin.talk.vo.TalkHelperSaveReqVO;
import com.rs.module.tem.entity.talk.TalkHelperDO;
import com.rs.module.tem.service.talk.TalkHelperService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 谈话教育-谈话提问助手")
@RestController
@RequestMapping("/tem/talk/talkHelper")
@Validated
public class TalkHelperController {

    @Resource
    private TalkHelperService talkHelperService;

    @PostMapping("/create")
    @ApiOperation(value = "创建谈话教育-谈话提问助手")
    @LogRecordAnnotation(bizModule = "tem:talkHelper:create", operateType = LogOperateType.CREATE, title = "创建谈话教育-谈话提问助手",
    success = "创建谈话教育-谈话提问助手成功", fail = "创建谈话教育-谈话提问助手失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createTalkHelper(@Valid @RequestBody TalkHelperSaveReqVO createReqVO) {
        return success(talkHelperService.createTalkHelper(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新谈话教育-谈话提问助手")
    @LogRecordAnnotation(bizModule = "tem:talkHelper:update", operateType = LogOperateType.UPDATE, title = "更新谈话教育-谈话提问助手",
    success = "更新谈话教育-谈话提问助手成功", fail = "更新谈话教育-谈话提问助手失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateTalkHelper(@Valid @RequestBody TalkHelperSaveReqVO updateReqVO) {
        talkHelperService.updateTalkHelper(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除谈话教育-谈话提问助手")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "tem:talkHelper:delete", operateType = LogOperateType.DELETE, title = "删除谈话教育-谈话提问助手",
    success = "删除谈话教育-谈话提问助手成功", fail = "删除谈话教育-谈话提问助手失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteTalkHelper(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           talkHelperService.deleteTalkHelper(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得谈话教育-谈话提问助手")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<TalkHelperRespVO> getTalkHelper(@RequestParam("id") String id) {
        TalkHelperDO talkHelper = talkHelperService.getTalkHelper(id);
        return success(BeanUtils.toBean(talkHelper, TalkHelperRespVO.class));
    }

//    @PostMapping("/page")
//    @ApiOperation(value = "获得谈话教育-谈话提问助手分页")
//    public CommonResult<PageResult<TalkHelperRespVO>> getTalkHelperPage(@Valid @RequestBody TalkHelperPageReqVO pageReqVO) {
//        PageResult<TalkHelperDO> pageResult = talkHelperService.getTalkHelperPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, TalkHelperRespVO.class));
//    }
//
//    @PostMapping("/list")
//    @ApiOperation(value = "获得谈话教育-谈话提问助手列表")
//    public CommonResult<List<TalkHelperRespVO>> getTalkHelperList(@Valid @RequestBody TalkHelperListReqVO listReqVO) {
//        List<TalkHelperDO> list = talkHelperService.getTalkHelperList(listReqVO);
//        return success(BeanUtils.toBean(list, TalkHelperRespVO.class));
//    }
}
