package com.rs.module.tem.controller.admin.video.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 谈话教育-终端管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VideoDevicePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监所id")
    private String prisonId;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("设备型号")
    private String deviceModel;

    @ApiModelProperty("谈话室")
    private String talkRoomId;

    @ApiModelProperty("谈话室名称")
    private String talkRoomName;

    @ApiModelProperty("设备ip（被监管人侧）")
    private String prisonerDeviceIp;

    @ApiModelProperty("设备ip（工作人员侧）")
    private String workerDeviceIp;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
