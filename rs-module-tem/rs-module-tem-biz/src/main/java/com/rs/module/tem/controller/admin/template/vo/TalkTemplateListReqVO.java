package com.rs.module.tem.controller.admin.template.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 谈话教育-谈话模板列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TalkTemplateListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("适用业务（1：个人，2：集体）")
    private String useBusiness;

    @ApiModelProperty("模板类型（字典：ZD_THJY_THYY）")
    private String templateType;

    @ApiModelProperty("谈话模板内容")
    private String content;

    @ApiModelProperty("谈话模板名称")
    private String templateName;

    @ApiModelProperty("最后使用时间")
    private Date[] lastUseTime;

    @ApiModelProperty("使用次数")
    private Integer useCount;

}
