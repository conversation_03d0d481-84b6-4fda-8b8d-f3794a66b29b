package com.rs.module.tem.controller.admin.talk.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName KyNotification
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/6/14 21:24
 * @Version 1.0
 */
@NoArgsConstructor
@Data
public class KyNotificationVO {
    @JsonProperty("jsondata[uuid]")
    private String uuid;
    @JsonProperty("jsondata[type]")
    private String type;
    @JsonProperty("jsondata[time]")
    private String time;
    @JsonProperty("jsondata[sourceid]")
    private String sourceid;
    @JsonProperty("jsondata[targetid]")
    private String targetid;
    @JsonProperty("jsondata[state]")
    private String state;
    @JsonProperty("jsondata[scoreiir]")
    private String scoreiir;
    @JsonProperty("jsondata[file]")
    private String file;
    @JsonProperty("jsondata[sourcename]")
    private String sourcename;
    @JsonProperty("jsondata[targetname]")
    private String targetname;
    @JsonProperty("jsondata[sourceext]")
    private String sourceext;
    @JsonProperty("jsondata[targetext]")
    private String targetext;
    @JsonProperty("jsondata[extdata]")
    private String jextdata;
    @JsonProperty("jsondata[taskid]")
    private String taskid;
    @JsonProperty("jsondata[playlistid]")
    private String playlistid;
    @JsonProperty("jsondata[ishost]")
    private String ishost;
    @JsonProperty("jsondata[sourcepanel]")
    private String sourcepanel;
    @JsonProperty("jsondata[targetpanel]")
    private String targetpanel;
    @JsonProperty("jsondata[tasktype]")
    private String tasktype;
    @JsonProperty("jsondata[panelkey]")
    private String panelkey;
    @JsonProperty("jsondata[deviceinfo]")
    private String deviceinfo;
    @JsonProperty("jsondata[usertaskinfo]")
    private String usertaskinfo;
}
