package com.rs.module.tem.monitor;

import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 录制任务性能监控
 * 
 * <AUTHOR>
 */
@Component
@Log4j2
public class RecordingTaskMonitor {

    private final ConcurrentHashMap<String, TaskMetrics> taskMetrics = new ConcurrentHashMap<>();

    @Data
    public static class TaskMetrics {
        private String taskId;
        private long createTime;
        private long initStartTime;
        private long initEndTime;
        private long recordStartTime;
        private long recordEndTime;
        private AtomicLong frameCount = new AtomicLong(0);
        private volatile boolean initializationSuccess = false;
        private volatile String errorMessage;
        
        public long getInitializationDuration() {
            return initEndTime > 0 ? initEndTime - initStartTime : -1;
        }
        
        public long getRecordingDuration() {
            return recordEndTime > 0 ? recordEndTime - recordStartTime : 
                   (recordStartTime > 0 ? System.currentTimeMillis() - recordStartTime : -1);
        }
        
        public double getFrameRate() {
            long duration = getRecordingDuration();
            return duration > 0 ? (frameCount.get() * 1000.0) / duration : 0;
        }
    }

    /**
     * 开始监控任务
     */
    public void startMonitoring(String taskId) {
        TaskMetrics metrics = new TaskMetrics();
        metrics.setTaskId(taskId);
        metrics.setCreateTime(System.currentTimeMillis());
        taskMetrics.put(taskId, metrics);
        log.info("开始监控录制任务: {}", taskId);
    }

    /**
     * 记录初始化开始
     */
    public void recordInitializationStart(String taskId) {
        TaskMetrics metrics = taskMetrics.get(taskId);
        if (metrics != null) {
            metrics.setInitStartTime(System.currentTimeMillis());
        }
    }

    /**
     * 记录初始化结束
     */
    public void recordInitializationEnd(String taskId, boolean success, String errorMessage) {
        TaskMetrics metrics = taskMetrics.get(taskId);
        if (metrics != null) {
            metrics.setInitEndTime(System.currentTimeMillis());
            metrics.setInitializationSuccess(success);
            metrics.setErrorMessage(errorMessage);
            
            long duration = metrics.getInitializationDuration();
            log.info("任务 {} 初始化完成，耗时: {} ms, 成功: {}", taskId, duration, success);
        }
    }

    /**
     * 记录录制开始
     */
    public void recordRecordingStart(String taskId) {
        TaskMetrics metrics = taskMetrics.get(taskId);
        if (metrics != null) {
            metrics.setRecordStartTime(System.currentTimeMillis());
        }
    }

    /**
     * 记录录制结束
     */
    public void recordRecordingEnd(String taskId) {
        TaskMetrics metrics = taskMetrics.get(taskId);
        if (metrics != null) {
            metrics.setRecordEndTime(System.currentTimeMillis());
            
            long duration = metrics.getRecordingDuration();
            long frameCount = metrics.getFrameCount().get();
            double frameRate = metrics.getFrameRate();
            
            log.info("任务 {} 录制完成，耗时: {} ms, 帧数: {}, 平均帧率: {:.2f} fps", 
                    taskId, duration, frameCount, frameRate);
        }
    }

    /**
     * 增加帧计数
     */
    public void incrementFrameCount(String taskId) {
        TaskMetrics metrics = taskMetrics.get(taskId);
        if (metrics != null) {
            metrics.getFrameCount().incrementAndGet();
        }
    }

    /**
     * 获取任务指标
     */
    public TaskMetrics getTaskMetrics(String taskId) {
        return taskMetrics.get(taskId);
    }

    /**
     * 停止监控任务
     */
    public void stopMonitoring(String taskId) {
        TaskMetrics metrics = taskMetrics.remove(taskId);
        if (metrics != null) {
            log.info("停止监控录制任务: {}", taskId);
        }
    }

    /**
     * 打印所有任务的性能报告
     */
    public void printPerformanceReport() {
        log.info("=== 录制任务性能报告 ===");
        taskMetrics.forEach((taskId, metrics) -> {
            log.info("任务ID: {}", taskId);
            log.info("  初始化耗时: {} ms", metrics.getInitializationDuration());
            log.info("  录制时长: {} ms", metrics.getRecordingDuration());
            log.info("  录制帧数: {}", metrics.getFrameCount().get());
            log.info("  平均帧率: {:.2f} fps", metrics.getFrameRate());
            log.info("  初始化成功: {}", metrics.isInitializationSuccess());
            if (metrics.getErrorMessage() != null) {
                log.info("  错误信息: {}", metrics.getErrorMessage());
            }
            log.info("---");
        });
    }
}
