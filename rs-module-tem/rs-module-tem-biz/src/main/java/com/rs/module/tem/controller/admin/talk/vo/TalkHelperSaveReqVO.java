package com.rs.module.tem.controller.admin.talk.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 谈话教育-谈话提问助手新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TalkHelperSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("谈话助手内容")
    @NotEmpty(message = "谈话助手内容不能为空")
    private String helperContent;

}
