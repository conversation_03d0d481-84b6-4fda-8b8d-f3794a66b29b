package com.rs.module.tem.entity.template;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 谈话教育-谈话模板 DO
 *
 * <AUTHOR>
 */
@TableName("tem_template")
@KeySequence("tem_template_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TalkTemplateDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 适用业务（1：个人，2：集体）
     */
    private String useBusiness;
    /**
     * 模板类型（字典：ZD_THJY_THYY）
     */
    private String templateType;
    /**
     * 谈话模板内容
     */
    private String content;
    /**
     * 谈话模板名称
     */
    private String templateName;
    /**
     * 最后使用时间
     */
    private Date lastUseTime;
    /**
     * 使用次数
     */
    private Integer useCount;

}
