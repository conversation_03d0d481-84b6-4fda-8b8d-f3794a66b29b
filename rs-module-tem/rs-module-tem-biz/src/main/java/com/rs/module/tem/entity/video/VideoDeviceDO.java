package com.rs.module.tem.entity.video;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 谈话教育-终端管理 DO
 *
 * <AUTHOR>
 */
@TableName("tem_video_device")
@KeySequence("tem_video_device_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "tem_video_device")
public class VideoDeviceDO extends BaseDO {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监所id
     */
    private String prisonId;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 设备型号
     */
    private String deviceModel;
    /**
     * 谈话室
     */
    private String talkRoomId;
    /**
     * 谈话室名称
     */
    private String talkRoomName;
    /**
     * 设备ip（被监管人侧）
     */
    private String prisonerDeviceIp;
    /**
     * 设备ip（工作人员侧）
     */
    private String workerDeviceIp;



}
