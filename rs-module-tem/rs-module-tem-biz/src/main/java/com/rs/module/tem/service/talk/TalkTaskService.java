package com.rs.module.tem.service.talk;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.tem.controller.admin.talk.vo.TalkTaskListReqVO;
import com.rs.module.tem.controller.admin.talk.vo.TalkTaskPageReqVO;
import com.rs.module.tem.controller.admin.talk.vo.TalkTaskSaveReqVO;
import com.rs.module.tem.entity.talk.TalkTaskDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 谈话教育-谈话任务 Service 接口
 *
 * <AUTHOR>
 */
public interface TalkTaskService extends IBaseService<TalkTaskDO>{

    /**
     * 创建谈话教育-谈话任务
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createTalkTask(@Valid TalkTaskSaveReqVO createReqVO);

    /**
     * 更新谈话教育-谈话任务
     *
     * @param updateReqVO 更新信息
     */
    void updateTalkTask(@Valid TalkTaskSaveReqVO updateReqVO);

    /**
     * 删除谈话教育-谈话任务
     *
     * @param id 编号
     */
    void deleteTalkTask(String id);

    /**
     * 获得谈话教育-谈话任务
     *
     * @param id 编号
     * @return 谈话教育-谈话任务
     */
    TalkTaskDO getTalkTask(String id);

    /**
    * 获得谈话教育-谈话任务分页
    *
    * @param pageReqVO 分页查询
    * @return 谈话教育-谈话任务分页
    */
    PageResult<TalkTaskDO> getTalkTaskPage(TalkTaskPageReqVO pageReqVO);

    /**
    * 获得谈话教育-谈话任务列表
    *
    * @param listReqVO 查询条件
    * @return 谈话教育-谈话任务列表
    */
    List<TalkTaskDO> getTalkTaskList(TalkTaskListReqVO listReqVO);

    /**
     *
     * @param talkCode
     * @return
     */
    TalkTaskDO getTalkTaskByTalkCode(String talkCode);

}
