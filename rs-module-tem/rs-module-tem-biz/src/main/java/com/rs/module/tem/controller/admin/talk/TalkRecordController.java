package com.rs.module.tem.controller.admin.talk;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.enums.PrisonerQueryRyztEnum;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.tem.controller.admin.talk.vo.TalkRecordRespVO;
import com.rs.module.tem.controller.admin.talk.vo.TalkRecordSaveReqVO;
import com.rs.module.tem.controller.admin.video.vo.TalkRoomRespVO;
import com.rs.module.tem.entity.talk.TalkRecordDO;
import com.rs.module.tem.service.talk.TalkRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Slf4j
@Api(tags = "谈话教育-个人谈话教育")
@RestController
@RequestMapping("/tem/talk/talkRecord")
@Validated
public class TalkRecordController {

    @Resource
    private TalkRecordService talkRecordService;
    @Resource
    private PrisonerService prisonerService;

    @PostMapping("/create")
    @ApiOperation(value = "创建谈话教育-个人谈话教育")
    @LogRecordAnnotation(bizModule = "tem:talkRecord:create", operateType = LogOperateType.CREATE, title = "创建谈话教育-个人谈话教育",
            success = "创建谈话教育-个人谈话教育成功", fail = "创建谈话教育-个人谈话教育失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createTalkRecord(@Valid @RequestBody TalkRecordSaveReqVO createReqVO) {
        return success(talkRecordService.createTalkRecord(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新谈话教育-个人谈话教育")
    @LogRecordAnnotation(bizModule = "tem:talkRecord:update", operateType = LogOperateType.UPDATE, title = "更新谈话教育-个人谈话教育",
            success = "更新谈话教育-个人谈话教育成功", fail = "更新谈话教育-个人谈话教育失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateTalkRecord(@Valid @RequestBody TalkRecordSaveReqVO updateReqVO) {
        talkRecordService.updateTalkRecord(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除谈话教育-个人谈话教育")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "tem:talkRecord:delete", operateType = LogOperateType.DELETE, title = "删除谈话教育-个人谈话教育",
            success = "删除谈话教育-个人谈话教育成功", fail = "删除谈话教育-个人谈话教育失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteTalkRecord(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            talkRecordService.deleteTalkRecord(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得谈话教育-个人谈话教育")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<TalkRecordRespVO> getTalkRecord(@RequestParam("id") String id) {
        TalkRecordDO talkRecord = talkRecordService.getTalkRecord(id);
        TalkRecordRespVO talkRecordRespVO = BeanUtils.toBean(talkRecord, TalkRecordRespVO.class);
        talkRecordRespVO.setPrisoner(prisonerService.getPrisonerOne(null, talkRecord.getJgrybm(), PrisonerQueryRyztEnum.ALL));
        return success(talkRecordRespVO);
    }

    @GetMapping("/getThs")
    @ApiOperation(value = "获得谈话教育-个人谈话教育-获取谈话室")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<List<TalkRoomRespVO>> getThs(HttpServletRequest request) {
        List<TalkRoomRespVO> list = new ArrayList<>();
        TalkRoomRespVO talkRoomRespVO = BeanUtils.toBean(talkRecordService.getThs(request), TalkRoomRespVO.class);
        list.add(talkRoomRespVO);
        return success(list);
    }


    @PostMapping("/getPersonTaskRecordListByJgrybm")
    @ApiOperation(value = "获取个人已结束的谈话教育列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "人员编码"),
            @ApiImplicitParam(name = "talkReason", value = "谈话原因 字典：ZD_THJY_THYY")

    })
    public CommonResult<List<TalkRecordRespVO>> getPersonTaskRecordListByJgrybm(@RequestParam("jgrybm") String jgrybm, @RequestParam(value = "talkReason", required = false) String talkReason) {
        List<TalkRecordDO> list = talkRecordService.getPersonTaskRecordEndListByJgrybm(jgrybm, talkReason);
        return success(BeanUtils.toBean(list, TalkRecordRespVO.class));
    }

    @PostMapping("/kyNotification")
    @ApiOperation(value = "获取个人已结束的谈话教育列表")
    public CommonResult<?> kyNotification() {
        log.info("收到回调kyNotification");
        return success();
    }

//    @PostMapping("/page")
//    @ApiOperation(value = "获得谈话教育-个人谈话教育分页")
//    public CommonResult<PageResult<TalkRecordRespVO>> getTalkRecordPage(@Valid @RequestBody TalkRecordPageReqVO pageReqVO) {
//        PageResult<TalkRecordDO> pageResult = talkRecordService.getTalkRecordPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, TalkRecordRespVO.class));
//    }
//
//    @PostMapping("/list")
//    @ApiOperation(value = "获得谈话教育-个人谈话教育列表")
//    public CommonResult<List<TalkRecordRespVO>> getTalkRecordList(@Valid @RequestBody TalkRecordListReqVO listReqVO) {
//        List<TalkRecordDO> list = talkRecordService.getTalkRecordList(listReqVO);
//        return success(BeanUtils.toBean(list, TalkRecordRespVO.class));
//    }


}
