package com.rs.module.tem.controller.admin.video.recorder;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.tem.service.video.recorder.VideoRecorderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.log4j.Log4j2;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * 录制测试控制器
 *
 * <AUTHOR>
 */
@Api(tags = "录制测试")
@RestController
@RequestMapping("/admin/video/test")
@Log4j2
public class TestRecordingController {

    @Autowired
    private VideoRecorderService videoRecorderService;

    /**
     * 快速录制测试（录制10秒）
     *
     * @param rtspUrl RTSP URL
     * @return 测试结果
     */
    @PostMapping("/quick-record")
    @ApiOperation(value = "快速录制测试")
    public CommonResult<String> quickRecordTest(
            @ApiParam(value = "RTSP URL", required = true)
            @RequestParam String rtspUrl) {

        String outputPath = System.getProperty("java.io.tmpdir") + "/test_recording_" +
                           System.currentTimeMillis() + ".mp4";

        try {
            log.info("开始快速录制测试: {}", rtspUrl);

            // 启动录制
            RecordingTask task = videoRecorderService.startRecording(rtspUrl, outputPath, null);
            String taskId = task.getTaskId();

            // 等待初始化
            boolean initialized = task.waitForInitialization(30);
            if (!initialized) {
                videoRecorderService.stopRecording(taskId);
                return CommonResult.error("初始化失败: " +
                    (task.getInitializationError() != null ?
                     task.getInitializationError().getMessage() : "超时"));
            }

            log.info("录制任务初始化成功，开始录制10秒...");

            // 录制10秒
            Thread.sleep(10000);

            // 停止录制
            boolean stopped = videoRecorderService.stopRecording(taskId);

            String result = String.format(
                "录制测试完成!\n任务ID: %s\n输出文件: %s\n停止结果: %s",
                taskId, outputPath, stopped ? "成功" : "失败"
            );

            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("快速录制测试失败", e);
            return CommonResult.error("测试失败: " + e.getMessage());
        }
    }

    /**
     * 监控录制任务状态
     *
     * @param taskId 任务ID
     * @return 监控结果
     */
    @GetMapping("/monitor/{taskId}")
    @ApiOperation(value = "监控录制任务")
    public CommonResult<String> monitorTask(@PathVariable String taskId) {
        try {
            Optional<RecordingTask> taskOpt = videoRecorderService.getTask(taskId);
            if (!taskOpt.isPresent()) {
                return CommonResult.error("任务不存在");
            }

            RecordingTask task = taskOpt.get();
            StringBuilder status = new StringBuilder();

            status.append("=== 录制任务监控 ===\n");
            status.append("任务ID: ").append(task.getTaskId()).append("\n");
            status.append("运行状态: ").append(task.isRunning() ? "运行中" : "已停止").append("\n");
            status.append("初始化状态: ").append(task.isInitialized() ? "已完成" : "进行中").append("\n");

            if (task.getInitializationError() != null) {
                status.append("错误信息: ").append(task.getInitializationError().getMessage()).append("\n");
            }

            // 如果有性能监控数据，也显示出来
            // 这里可以扩展显示更多监控信息

            return CommonResult.success(status.toString());

        } catch (Exception e) {
            log.error("监控任务失败", e);
            return CommonResult.error("监控失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有录制任务状态
     *
     * @return 所有任务状态
     */
    @GetMapping("/all-tasks")
    @ApiOperation(value = "获取所有录制任务")
    public CommonResult<String> getAllTasks() {
        try {
            var tasks = videoRecorderService.getAllTasks();

            if (tasks.isEmpty()) {
                return CommonResult.success("当前没有录制任务");
            }

            StringBuilder result = new StringBuilder();
            result.append("=== 所有录制任务 ===\n");

            for (RecordingTask task : tasks) {
                result.append("任务ID: ").append(task.getTaskId()).append("\n");
                result.append("  状态: ").append(task.isRunning() ? "运行中" : "已停止").append("\n");
                result.append("  初始化: ").append(task.isInitialized() ? "完成" : "进行中").append("\n");
                result.append("---\n");
            }

            return CommonResult.success(result.toString());

        } catch (Exception e) {
            log.error("获取所有任务失败", e);
            return CommonResult.error("获取失败: " + e.getMessage());
        }
    }
}
