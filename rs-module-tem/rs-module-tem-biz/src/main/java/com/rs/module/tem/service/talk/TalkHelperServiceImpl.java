package com.rs.module.tem.service.talk;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.tem.controller.admin.talk.vo.*;
import com.rs.module.tem.entity.talk.TalkHelperDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.tem.dao.talk.TalkHelperDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 谈话教育-谈话提问助手 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TalkHelperServiceImpl extends BaseServiceImpl<TalkHelperDao, TalkHelperDO> implements TalkHelperService {

    @Resource
    private TalkHelperDao talkHelperDao;

    @Override
    public String createTalkHelper(TalkHelperSaveReqVO createReqVO) {
        // 插入
        TalkHelperDO talkHelper = BeanUtils.toBean(createReqVO, TalkHelperDO.class);
        talkHelperDao.insert(talkHelper);
        // 返回
        return talkHelper.getId();
    }

    @Override
    public void updateTalkHelper(TalkHelperSaveReqVO updateReqVO) {
        // 校验存在
        validateTalkHelperExists(updateReqVO.getId());
        // 更新
        TalkHelperDO updateObj = BeanUtils.toBean(updateReqVO, TalkHelperDO.class);
        talkHelperDao.updateById(updateObj);
    }

    @Override
    public void deleteTalkHelper(String id) {
        // 校验存在
        validateTalkHelperExists(id);
        // 删除
        talkHelperDao.deleteById(id);
    }

    private void validateTalkHelperExists(String id) {
        if (talkHelperDao.selectById(id) == null) {
            throw new ServerException("谈话教育-谈话提问助手数据不存在");
        }
    }

    @Override
    public TalkHelperDO getTalkHelper(String id) {
        return talkHelperDao.selectById(id);
    }

    @Override
    public PageResult<TalkHelperDO> getTalkHelperPage(TalkHelperPageReqVO pageReqVO) {
        return talkHelperDao.selectPage(pageReqVO);
    }

    @Override
    public List<TalkHelperDO> getTalkHelperList(TalkHelperListReqVO listReqVO) {
        return talkHelperDao.selectList(listReqVO);
    }


}
