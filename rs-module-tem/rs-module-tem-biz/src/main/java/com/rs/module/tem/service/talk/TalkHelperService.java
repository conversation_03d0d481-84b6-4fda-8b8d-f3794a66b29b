package com.rs.module.tem.service.talk;

import java.util.*;
import javax.validation.*;
import com.rs.module.tem.controller.admin.talk.vo.*;
import com.rs.module.tem.entity.talk.TalkHelperDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 谈话教育-谈话提问助手 Service 接口
 *
 * <AUTHOR>
 */
public interface TalkHelperService extends IBaseService<TalkHelperDO>{

    /**
     * 创建谈话教育-谈话提问助手
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createTalkHelper(@Valid TalkHelperSaveReqVO createReqVO);

    /**
     * 更新谈话教育-谈话提问助手
     *
     * @param updateReqVO 更新信息
     */
    void updateTalkHelper(@Valid TalkHelperSaveReqVO updateReqVO);

    /**
     * 删除谈话教育-谈话提问助手
     *
     * @param id 编号
     */
    void deleteTalkHelper(String id);

    /**
     * 获得谈话教育-谈话提问助手
     *
     * @param id 编号
     * @return 谈话教育-谈话提问助手
     */
    TalkHelperDO getTalkHelper(String id);

    /**
    * 获得谈话教育-谈话提问助手分页
    *
    * @param pageReqVO 分页查询
    * @return 谈话教育-谈话提问助手分页
    */
    PageResult<TalkHelperDO> getTalkHelperPage(TalkHelperPageReqVO pageReqVO);

    /**
    * 获得谈话教育-谈话提问助手列表
    *
    * @param listReqVO 查询条件
    * @return 谈话教育-谈话提问助手列表
    */
    List<TalkHelperDO> getTalkHelperList(TalkHelperListReqVO listReqVO);


}
