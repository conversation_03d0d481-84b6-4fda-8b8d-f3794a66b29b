package com.rs.module.tem.controller.admin.template.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@ApiModel(description = "管理后台 - 谈话教育-谈话模板新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TalkTemplateSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("适用业务（1：个人，2：集体）")
    @NotEmpty(message = "适用业务（1：个人，2：集体）不能为空")
    private String useBusiness;

    @ApiModelProperty("模板类型（字典：ZD_THJY_THYY）")
    @NotEmpty(message = "模板类型（字典：ZD_THJY_THYY）不能为空")
    private String templateType;

    @ApiModelProperty("谈话模板名称")
    @NotEmpty(message = "谈话模板名称不能为空")
    private String templateName;


    @ApiModelProperty("谈话教育-谈话模板内容列表")
    private List<TalkTemplateContentSaveReqVO> talkTemplateContents;

}
