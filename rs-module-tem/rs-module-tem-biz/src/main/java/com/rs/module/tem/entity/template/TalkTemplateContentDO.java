package com.rs.module.tem.entity.template;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 谈话教育-谈话模板内容 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 谈话教育-谈话模板内容新增/修改 Request VO")
@TableName("tem_template_content")
@KeySequence("tem_template_content_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TalkTemplateContentDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;
    /**
     * 谈话模板id
     */
    @ApiModelProperty("谈话模板id")
    private String templateId;
    /**
     * 内容组
     */
    @ApiModelProperty("内容组")
    private Integer contentGroup;
    /**
     * 内容类型 1=问 2=答
     */
    @ApiModelProperty("内容类型 1=问 2=答")
    private String contentType;
    /**
     * 谈话模板内容
     */
    @ApiModelProperty("谈话模板内容")
    private String content;

    private String px;

}
