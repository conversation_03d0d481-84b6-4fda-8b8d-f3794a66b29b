# RTSP连接问题解决方案

## 问题描述
RTSP流 `rtsp://***********:554/stream07` 在VLC中可以播放，但在录制程序中连接失败。

## 常见原因分析

### 1. 传输协议问题
**症状**: 连接超时或拒绝连接
**原因**: RTSP支持TCP和UDP两种传输协议，某些设备只支持特定协议
**解决方案**:
- 优先尝试TCP协议（更稳定）
- 如果TCP失败，尝试UDP协议
- 使用我们的自动协议切换功能

### 2. 超时设置问题
**症状**: 连接时间过长或超时
**原因**: 默认超时时间不适合网络环境
**解决方案**:
- 调整连接超时时间（当前设置10秒）
- 调整读取超时时间（当前设置5秒）
- 根据网络情况适当增加

### 3. 缓冲区设置问题
**症状**: 连接成功但播放卡顿或中断
**原因**: 缓冲区设置不当
**解决方案**:
- 调整缓冲区大小
- 设置合适的最大延迟
- 禁用不必要的缓冲

### 4. 认证问题
**症状**: 401 Unauthorized错误
**原因**: RTSP流需要用户名密码认证
**解决方案**:
- 在URL中包含认证信息: `rtsp://username:password@***********:554/stream07`
- 或使用FFmpeg的认证选项

### 5. 防火墙/网络问题
**症状**: 网络连接失败
**原因**: 防火墙阻止或网络不通
**解决方案**:
- 检查防火墙设置
- 测试网络连通性
- 确认端口开放

## 诊断步骤

### 1. 使用诊断接口
```http
GET /admin/video/diagnostic/rtsp?rtspUrl=rtsp://***********:554/stream07
```

### 2. 查看诊断报告
诊断报告会包含：
- URL解析结果
- 网络连通性测试
- 不同传输协议测试结果
- FFmpeg连接测试结果
- 流媒体信息（如果连接成功）

### 3. 根据诊断结果调整

## 配置优化

### 1. 针对您的RTSP流的优化配置
```java
// 在FFmpegUtils.configureRTSPOptions中添加
grabber.setOption("rtsp_transport", "tcp");  // 强制使用TCP
grabber.setOption("timeout", "15000000");    // 15秒连接超时
grabber.setOption("stimeout", "10000000");   // 10秒读取超时
grabber.setOption("user_agent", "JavaCV-RTSP-Client");
```

### 2. 网络优化
```java
grabber.setOption("buffer_size", "2048000");  // 增大缓冲区到2MB
grabber.setOption("max_delay", "1000000");    // 增加最大延迟到1秒
grabber.setOption("rtsp_flags", "prefer_tcp");
```

## 测试建议

### 1. 先使用诊断工具
在录制之前，先调用诊断接口确认连接状态：
```bash
curl "http://localhost:8080/admin/video/diagnostic/rtsp?rtspUrl=rtsp://***********:554/stream07"
```

### 2. 逐步测试
1. 测试网络连通性
2. 测试不同传输协议
3. 测试不同超时设置
4. 测试实际录制

### 3. 查看详细日志
启用DEBUG级别日志查看详细的连接过程：
```properties
logging.level.com.rs.module.tem.controller.admin.video.recorder=DEBUG
logging.level.com.rs.module.tem.util=DEBUG
```

## 常见错误及解决方案

### 错误1: Connection timed out
**解决方案**:
- 增加超时时间
- 检查网络连接
- 尝试不同传输协议

### 错误2: Protocol not found
**解决方案**:
- 检查URL格式
- 确认RTSP服务正常运行
- 尝试使用标准端口554

### 错误3: Server returned 401 Unauthorized
**解决方案**:
- 添加认证信息到URL
- 检查用户名密码
- 确认设备支持的认证方式

### 错误4: No route to host
**解决方案**:
- 检查IP地址是否正确
- 确认网络路由
- 检查防火墙设置

## 性能优化建议

### 1. 连接池
考虑实现RTSP连接池，复用连接减少初始化时间

### 2. 预连接
在需要录制前提前建立连接，减少等待时间

### 3. 监控告警
添加连接状态监控，及时发现问题

## 联系支持
如果问题仍然存在，请提供：
1. 完整的错误日志
2. 诊断报告结果
3. 网络环境信息
4. RTSP设备型号和配置
