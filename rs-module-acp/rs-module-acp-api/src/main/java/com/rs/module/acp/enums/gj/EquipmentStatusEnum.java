package com.rs.module.acp.enums.gj;

/**
 * 戒具使用状态
 */
public enum EquipmentStatusEnum {

    YDJ("01", "已登记"),

    CPDSP("02", "呈批待审批"),

    YCDSP("03", "延长待审批"),

    TQJCDSP("04", "提前解除待审批"),

    DDJ("05", "待登记"),

    JISY("06", "戒具使用中"),

    DJC("07", "待解除"),

    YJC("08", "已解除"),

    CPBTG("09", "呈批不通过");

    EquipmentStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static EquipmentStatusEnum getByCode(String code) {
        EquipmentStatusEnum[] enums = EquipmentStatusEnum.values();
        for (EquipmentStatusEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    private String code;

    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
