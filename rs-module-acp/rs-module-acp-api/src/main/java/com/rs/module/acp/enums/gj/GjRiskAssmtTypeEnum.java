package com.rs.module.acp.enums.gj;

/**
 * 风险评估类型
 * <AUTHOR>
 * @date 2025/6/15 20:46
 */
public enum GjRiskAssmtTypeEnum {

    RCFXPG("01", "日常风险评估"),
    XRSFXPG("02", "新入所风险评估"),
    GDQMFXPG("03", "过渡期满风险评估"),
    WGDJFXPG("04", "违规登记风险评估"),
    JJSYFXPG("05", "戒具使用风险评估"),
    ZBHDJFXPG("06", "重病号登记风险评估"),
    ZDRYGZFXPG("07", "重点人员关注风险评估"),
    SSBGFXPG("08", "诉讼变更风险评估");

    GjRiskAssmtTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static GjRiskAssmtTypeEnum getByCode(String code) {
        GjRiskAssmtTypeEnum[] enums = GjRiskAssmtTypeEnum.values();
        for (GjRiskAssmtTypeEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
