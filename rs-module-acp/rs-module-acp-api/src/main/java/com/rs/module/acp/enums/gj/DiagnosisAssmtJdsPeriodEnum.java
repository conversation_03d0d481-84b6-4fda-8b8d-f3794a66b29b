package com.rs.module.acp.enums.gj;

public enum DiagnosisAssmtJdsPeriodEnum {

    THREE_MONTH("01", "入所3个月评估", "INTERVAL ' 85 DAY'"),

    ONE_YEAR("02", "入所1年评估", "INTERVAL ' 1 year'"),

    TWO_YEAR("03", "入所2年评估", "INTERVAL ' 2 year' - INTERVAL ' 10 day'");

    DiagnosisAssmtJdsPeriodEnum(String code, String name, String time) {
        this.code = code;
        this.name = name;
        this.time = time;
    }

    private String code;

    private String name;

    private String time;

    public static DiagnosisAssmtJdsPeriodEnum getByCode(String code) {
        DiagnosisAssmtJdsPeriodEnum[] enums = DiagnosisAssmtJdsPeriodEnum.values();
        for (DiagnosisAssmtJdsPeriodEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getTime() {
        return time;
    }

}
