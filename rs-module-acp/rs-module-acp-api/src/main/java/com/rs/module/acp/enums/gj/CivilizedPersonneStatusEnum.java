package com.rs.module.acp.enums.gj;

public enum CivilizedPersonneStatusEnum {
    DPB("01", "待评比"),
    DSP("02", "待审批"),
    DCXPB("03", "待重新评比"),
    PBWC("04", "评比完成");

    CivilizedPersonneStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static CivilizedPersonneStatusEnum getByCode(String code) {
        for (CivilizedPersonneStatusEnum value : CivilizedPersonneStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("非法code");
    }
}
