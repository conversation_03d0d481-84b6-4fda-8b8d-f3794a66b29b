package com.rs.module.acp.enums.zh;

public enum IndicatorAssmtApprovalStatusEnum {

    DZDLDSP("01", "待中队领导审批"),
    DZWSP("02", "待政委审批"),
    YBJ("03", "已办结");


    IndicatorAssmtApprovalStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static IndicatorAssmtApprovalStatusEnum getByCode(String code) {
        for (IndicatorAssmtApprovalStatusEnum value : IndicatorAssmtApprovalStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("非法code");
    }
}
