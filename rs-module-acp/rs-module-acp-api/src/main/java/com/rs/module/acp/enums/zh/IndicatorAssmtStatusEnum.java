package com.rs.module.acp.enums.zh;

public enum IndicatorAssmtStatusEnum {

    DTX("01", "待填写"),
    DSP("02", "待审批"),
    SPWC("03", "审批完成");

    IndicatorAssmtStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static IndicatorAssmtStatusEnum getByCode(String code) {
        for (IndicatorAssmtStatusEnum value : IndicatorAssmtStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("非法code");
    }
}
