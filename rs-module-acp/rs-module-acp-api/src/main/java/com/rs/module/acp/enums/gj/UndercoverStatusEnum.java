package com.rs.module.acp.enums.gj;

/**
 * 耳目管理-状态信息
 * <AUTHOR>
 * @date 2025/5/26 17:09
 */
public enum UndercoverStatusEnum {

    YDJ("01", "已登记"),
    DSP("02", "布建待审核"),
    SPBTG("03", "审批不通过"),
    TJZ("04", "布建中"),
    TJCG("05", "已撤销"),
    CXDSH("06", "撤销待审核")

            ;

    UndercoverStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static UndercoverStatusEnum getByCode(String code) {
        UndercoverStatusEnum[] enums = UndercoverStatusEnum.values();
        for (UndercoverStatusEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
