package com.rs.module.acp.enums.zh;

public enum IndicatorTypeEnum {

    ZGF("01", "主观分指标"),
    JJF("02", "加减分指标");

    IndicatorTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static IndicatorTypeEnum getByCode(String code) {
        for (IndicatorTypeEnum value : IndicatorTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("非法code");
    }
}
