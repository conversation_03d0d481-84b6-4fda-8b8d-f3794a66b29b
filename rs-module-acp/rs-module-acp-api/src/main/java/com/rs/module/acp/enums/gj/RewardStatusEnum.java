package com.rs.module.acp.enums.gj;

/**
 * 奖励管理
 * <AUTHOR>
 * @date 2025/7/5 17:12
 */
public enum RewardStatusEnum {

    DSP("01", "待审核"),
    DDJJL("02", "待登记奖励"),
    YWJ("03", "已完结"),
    SPBTG("04", "审批不通过");

    RewardStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static RewardStatusEnum getByCode(String code) {
        RewardStatusEnum[] enums = RewardStatusEnum.values();
        for (RewardStatusEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
