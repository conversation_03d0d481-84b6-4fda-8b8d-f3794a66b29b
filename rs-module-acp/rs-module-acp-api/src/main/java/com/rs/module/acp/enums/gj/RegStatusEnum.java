package com.rs.module.acp.enums.gj;

/**
 * 登记状态代码枚举
 *
 * <AUTHOR>
 * @Date 2025/3/17 16:37
 */
public enum RegStatusEnum {

    YDJ("01", "已登记"),
    DSP("02", "待审批"),
    SPBTG("03", "审批不通过"),
    TJZ("04", "调解中"),
    TJCG("05", "调解完成"),
    YSQ("06", "已申请"),
    BTH("07", "被退回");

    RegStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static RegStatusEnum getByCode(String code) {
        RegStatusEnum[] enums = RegStatusEnum.values();
        for (RegStatusEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
