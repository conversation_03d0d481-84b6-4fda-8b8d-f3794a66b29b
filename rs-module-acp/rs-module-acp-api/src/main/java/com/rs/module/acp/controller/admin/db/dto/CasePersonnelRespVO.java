package com.rs.module.acp.controller.admin.db.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CasePersonnelRespVO {
    /**
     * 办案单位类型
     */
    private String badwlx;
    /**
     * 办案单位
     */
    @ApiModelProperty("办案单位")
    private String badw;
    /**
     * 办案人
     */
    @ApiModelProperty("办案人")
    private String bar;
    /**
     * 办案人联系方式
     */
    @ApiModelProperty("办案人联系方式")
    private String barlxff;
    /**
     * 办案人性别
     */
    @ApiModelProperty("办案人性别")
    private String barxb;
    /**
     * 办案人证件类型
     */
    @ApiModelProperty("办案人证件类型")
    private String barzjlx;
    /**
     * 办案人证件号码
     */
    @ApiModelProperty("办案人证件号码")
    private String barzjhm;

    @ApiModelProperty("办案环节")
    private String bahj;
}
