package com.rs.module.acp.enums.gj;

public enum AssistanceSolveCaseStatusEnum {

    DSH("01", "待审核"),
    DZD("02", "待转递"),
    DFK("03", "待反馈"),
    YWJ("04", "已完结"),
    SPBTG("05", "审批不通过");

    AssistanceSolveCaseStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;

    private String name;

    public static AssistanceSolveCaseStatusEnum getByCode(String code) {
        AssistanceSolveCaseStatusEnum[] enums = AssistanceSolveCaseStatusEnum.values();
        for (AssistanceSolveCaseStatusEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
