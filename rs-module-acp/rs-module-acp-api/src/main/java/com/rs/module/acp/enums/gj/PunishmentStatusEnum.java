package com.rs.module.acp.enums.gj;

/**
 * 处罚审批字典-状态码-ZD_GJ_PUNISHMENT_STATUS
 * <AUTHOR>
 * @date 2025/6/9 16:17
 */
public enum PunishmentStatusEnum {

    CPDSP("01", "呈批待审批"),

    YCDSP("02", "延长待审批"),

    TQJCDSP("03", "提前解除待审批"),

    DDJ("04", "待登记"),

    CFZ("05", "处罚中"),

    DJC("06", "待解除"),

    YJC("07", "已解除"),

    CPBTG("08", "呈批不通过"),

    CFDSX("09", "处罚待生效");


    PunishmentStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PunishmentStatusEnum getByCode(String code) {
        PunishmentStatusEnum[] enums = PunishmentStatusEnum.values();
        for (PunishmentStatusEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    private String code;

    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
