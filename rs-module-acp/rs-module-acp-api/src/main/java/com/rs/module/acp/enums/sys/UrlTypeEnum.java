package com.rs.module.acp.enums.sys;

/**
 * URL类型
 * <AUTHOR>
 * @date 2025/5/23 9:54
 */
public enum UrlTypeEnum {

    PLUGIN("1", "插件链接"),

    BROWSER("2", "浏览器链接"),

    HYPERLINK("3", "外链链接");




    UrlTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static UrlTypeEnum getByCode(String code) {
        UrlTypeEnum[] enums = UrlTypeEnum.values();
        for (UrlTypeEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
