package com.rs.module.acp.enums.gj;

/**
 * 监室过渡字典-状态码-ZD_GJ_TRANSITION_ROOM_STATUS
 * <AUTHOR>
 * @date 2025/6/9 16:17
 */
public enum TransitionRoomStatusEnum {

    GDZ("01", "过渡中"),

    JSDZZ("02", "监室调整审批中"),

    YCGDSPZ("03", "延长过渡审批中"),

    YJC("04", "已解除"),

    YCQDJC("05", "已超期待解除"),

    YSP("06", "已审批");

    TransitionRoomStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static TransitionRoomStatusEnum getByCode(String code) {
        TransitionRoomStatusEnum[] enums = TransitionRoomStatusEnum.values();
        for (TransitionRoomStatusEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    private String code;

    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
