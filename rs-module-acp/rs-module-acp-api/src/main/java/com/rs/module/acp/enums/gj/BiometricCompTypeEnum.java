package com.rs.module.acp.enums.gj;

public enum BiometricCompTypeEnum {

    bz("01", "比中"),
    wbz("02", "未比中");

    BiometricCompTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static BiometricCompTypeEnum getByCode(String code) {
        for (BiometricCompTypeEnum value : BiometricCompTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("非法code");
    }
}
