package com.rs.module.acp.enums.zh;

public enum  IndicatorObjectTypeEnum {

    GW("01", "岗位"),
    J<PERSON>("02", "角色"),
    YH("03", "用户");

    IndicatorObjectTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static IndicatorObjectTypeEnum getByCode(String code) {
        for (IndicatorObjectTypeEnum value : IndicatorObjectTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("非法code");
    }
}
