package com.rs.module.acp.enums.db;

public enum JxjsdjStatusEnum {

    DSWHYJ("01", "待所务会研究"),
    DLXNLRD("02", "待履行能力认定"),
    DHSZXD("03", "待核实执行地"),
    DSNGS("04", "待所内公示"),
    DSWHYJFH("05", "待所务会研究复核"),
    DGAJGSC("06", "待公安机关审查"),
    DJCYJD("07", "待检察院监督"),
    DFYSHCD("08", "待法院审核裁定"),
    YBJ("09", "已办结");

    JxjsdjStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static JxjsdjStatusEnum getByCode(String code) {
        for (JxjsdjStatusEnum value : JxjsdjStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("非法code");
    }
}
