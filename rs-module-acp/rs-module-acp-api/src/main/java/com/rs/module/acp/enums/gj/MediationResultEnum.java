package com.rs.module.acp.enums.gj;

/**
 * 调解结果枚举
 * <AUTHOR>
 * @Date 2025/4/9 13:57
 */
public enum MediationResultEnum {

    TJCG("1", "调解成功"),
    TJZH("2", "调解暂缓"),
    BZTJ("3", "不再调解");

    MediationResultEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static MediationResultEnum getByCode(String code) {
        MediationResultEnum[] enums = MediationResultEnum.values();
        for (MediationResultEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
