package com.rs.module.acp.enums.gj;

public enum PrintDocumentTypeEnum {

    kssyrbxjd("kssyrbxjd", "看守所人员表现鉴定"),
    jlsrybxjd("jlsrybxjd", "拘留所人员表现鉴定"),
    kssqlywgzs("kssqlywgzs", "看守所权利义务告知书"),
    jlsqlywgzs("jlsqlywgzs", "拘留所权利义务告知书"),
    jdrysltdpgb("jdrysltdpgb", "戒毒人员生理脱毒评估表"),
    jdrysxjkpgb("jdrysxjkpgb", "戒毒人员身心康复评估表"),
    jdryshhjysynlpgb("jdryshhjysynlpgb", "戒毒人员社会环境与适应能力评估表"),
    jdryxwbxpgb("jdryxwbxpgb", "戒毒人员行为表现评估表"),
    jdryxwbxlhkhjfb("jdryxwbxlhkhjfb", "戒毒人员行为表现量化考核记分表");


    PrintDocumentTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static PrintDocumentTypeEnum getByCode(String code) {
        for (PrintDocumentTypeEnum value : PrintDocumentTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("非法code");
    }
}
