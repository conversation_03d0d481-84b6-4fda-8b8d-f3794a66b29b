package com.rs.module.acp.enums.wb;

public final class WbConstants {


    //提讯 acp_wb_arraignment
    public final static String BUSINESS_TYPE_ARRAIGNMENT = "0";
    //提询 acp_wb_bring_interrogation
    public final static String BUSINESS_TYPE_BRING_INTERROGATION = "1";
    //提解 acp_wb_escort
    public final static String BUSINESS_TYPE_ESCORT = "2";
    //律师会见 acp_wb_lawyer_meeting
    public final static String BUSINESS_TYPE_LAWYER_MEETING = "3";
    //家属会见（当面会见） acp_wb_family_meeting
    public final static String BUSINESS_TYPE_FAMILY_MEETING = "4";
    //领事会见 acp_wb_consular_meeting
    public final static String BUSINESS_TYPE_CONSULAR_MEETING = "5";
    //家属单向视频会见 acp_wb_family_meeting_video
    public final static String BUSINESS_TYPE_FAMILY_MEETING_VIDEO = "6";
    //物品顾送 acp_wb_goods_delivery
    public final static String BUSINESS_TYPE_GOODS_DELIVERY = "7";
}
