package com.rs.module.acp.enums.gj;

public enum DiagnosisAssmtJdsTypeEnum {

    SLTD("01", "戒毒人员生理脱毒评估"),

    SXKF("02", "戒毒人员身心康复评估"),

    SHHJYSYNL("03", "戒毒人员社会环境与适应能力评估"),

    XWBX("04", "戒毒人员行为表现评估");

    DiagnosisAssmtJdsTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;

    private String name;

    public static DiagnosisAssmtJdsTypeEnum getByCode(String code) {
        DiagnosisAssmtJdsTypeEnum[] enums = DiagnosisAssmtJdsTypeEnum.values();
        for (DiagnosisAssmtJdsTypeEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }


    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
