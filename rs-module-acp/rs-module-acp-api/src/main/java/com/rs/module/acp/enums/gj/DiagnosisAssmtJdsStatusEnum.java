package com.rs.module.acp.enums.gj;

public enum DiagnosisAssmtJdsStatusEnum {

    DPG("01", "待评估"),
    DZDLDSP("02", "待中队领导审批"),
    DSLDSP("03", "待所领导审批"),
    SPTG("04", "审批通过"),
    SPBTG("05", "审批不通过");

    DiagnosisAssmtJdsStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;

    private String name;

    public static DiagnosisAssmtJdsStatusEnum getByCode(String code) {
        DiagnosisAssmtJdsStatusEnum[] enums = DiagnosisAssmtJdsStatusEnum.values();
        for (DiagnosisAssmtJdsStatusEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
