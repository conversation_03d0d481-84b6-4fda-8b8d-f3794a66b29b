package com.rs.module.acp.enums.gj;

public enum PerformanceStatusEnum {

    DSH("01", "待审批"),
    SHTG("02", "审核通过"),
    SHBTG("03", "审核不通过");

    PerformanceStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static PerformanceStatusEnum getByCode(String code) {
        for (PerformanceStatusEnum value : PerformanceStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("非法code");
    }
}
