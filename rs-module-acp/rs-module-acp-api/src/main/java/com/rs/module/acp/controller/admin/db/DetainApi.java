package com.rs.module.acp.controller.admin.db;


import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.admin.db.dto.CasePersonnelRespVO;
import com.rs.module.acp.enums.db.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.cloud.openfeign.FeignClient;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC服务-入所信息")
public interface DetainApi {

    String PREFIX = ApiConstants.PREFIX + "/medicine";

    @GetMapping(PREFIX + "/getCasePersonnelByjgrybm")
    @Operation(summary = "根据jgrybm查询获取办案人员信息")
    @Parameter(name = "jgrybm", description = "jgrybm", example = "110", required = true)
    CommonResult<CasePersonnelRespVO> getCasePersonnel(@RequestParam("jgrybm") String jgrybm);

}
