package com.rs.module.acp.enums.gj;

/**
 *  风险评估-状态
 * <AUTHOR>
 * @date 2025/5/26 17:09
 */
public enum GjRiskAssmtStatusEnum {

    YDJ("0", "已登记"),
    DSP("1", "待审核"),
    SPTG("2", "审批通过"),
    SPBTG("3", "审批不通过");

    GjRiskAssmtStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static GjRiskAssmtStatusEnum getByCode(String code) {
        GjRiskAssmtStatusEnum[] enums = GjRiskAssmtStatusEnum.values();
        for (GjRiskAssmtStatusEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
