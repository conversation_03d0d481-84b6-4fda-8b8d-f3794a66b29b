package com.rs.module.acp.enums.gj;

/**
 * 监室调整状态
 * <AUTHOR>
 * @date 2025/5/28 16:51
 */
public enum PrisonRoomStatusEnum {

    YDJ("01", "已登记"),
    DSP("02", "待审核"),
    SPBTG("03", "审批不通过"),
    SPTGDTJ("04", "审批通过待调监"),
    YWC("05", "已完成");

    PrisonRoomStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PrisonRoomStatusEnum getByCode(String code) {
        PrisonRoomStatusEnum[] enums = PrisonRoomStatusEnum.values();
        for (PrisonRoomStatusEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
