package com.rs.module.acp.enums.zh;

public enum IndicatorScoreTypeEnum {

    ADD("ADD", "加分"),
    SUBTRACT("SUBTRACT", "减分");

    IndicatorScoreTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static IndicatorScoreTypeEnum getByCode(String code) {
        for (IndicatorScoreTypeEnum value : IndicatorScoreTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("非法code");
    }
}
