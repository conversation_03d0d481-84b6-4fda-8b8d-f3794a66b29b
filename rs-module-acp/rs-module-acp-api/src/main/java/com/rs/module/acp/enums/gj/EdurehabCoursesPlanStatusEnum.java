package com.rs.module.acp.enums.gj;

public enum EdurehabCoursesPlanStatusEnum {

    DSP("01", "待审批"),
    SPBTG("02", "审批不通过"),
    SPYQ("03", "审批逾期"),
    SXZ("04", "生效中"),
    YGQ("05", "已过期");

    EdurehabCoursesPlanStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;

    private String name;

    public static EdurehabCoursesPlanStatusEnum getByCode(String code) {
        EdurehabCoursesPlanStatusEnum[] enums = EdurehabCoursesPlanStatusEnum.values();
        for (EdurehabCoursesPlanStatusEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
