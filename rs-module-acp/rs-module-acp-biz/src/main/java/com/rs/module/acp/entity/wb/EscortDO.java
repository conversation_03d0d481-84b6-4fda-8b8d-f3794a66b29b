package com.rs.module.acp.entity.wb;

import lombok.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-窗口业务-提解登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_wb_escort")
@KeySequence("acp_wb_escort_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EscortDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 第一位办案人ID
     */
    private String handler1Id;
    /**
     * 第一位办案人姓名
     */
    private String handler1Xm;
    /**
     * 第二位办案人ID
     */
    private String handler2Id;
    /**
     * 第二位办案人姓名
     */
    private String handler2Xm;
    /**
     * 第三位办案人ID
     */
    private String handler3Id;
    /**
     * 第三位办案人姓名
     */
    private String handler3Xm;
    /**
     * 审讯室ID
     */
    private String roomId;
    /**
     * 提解原因
     */
    private String escortReason;
    /**
     * 申请提解日期
     */
    private Date applyEscortDate;
    /**
     * 详细事由
     */
    private String detailReason;
    /**
     * 提解目的地
     */
    private String destination;
    /**
     * 提解文书号提解文书号
     */
    private String wsh;
    /**
     * 上传提讯凭证URL
     */
    private String evidenceUrl;
    /**
     * 备注
     */
    private String remark;
    /**
     * 提解机关类型
     */
    private String tjjglx;
    /**
     * 提解机关名称
     */
    private String tjjgmc;
    /**
     * 办理状态
     */
    private String status;
    /**
     * 带出民警身份证号
     */
    private String escortingPoliceSfzh;
    /**
     * 带出民警身份证号
     */
    private String escortingPolice;
    /**
     * 带出监室时间
     */
    private Date escortingTime;
    /**
     * 检查结果
     */
    private String inspectionResult;
    /**
     * 违禁物品登记
     */
    private String prohibitedItems;
    /**
     * 违禁物品登记照片存储地址
     */
    private String prohibitedItemsImgUrl;
    /**
     * 体表检查登记
     */
    private String physicalExam;
    /**
     * 体表检查登记照片存储地址
     */
    private String physicalExamImgUrl;
    /**
     * 异常情况登记
     */
    private String abnormalSituations;
    /**
     * 异常情况登记照片存储地址
     */
    private String abnormalSituationsImgUrl;
    /**
     * 是否查出违禁物品
     */
    private Short isProhibitedItems;
    /**
     * 检查时间
     */
    private Date inspectionTime;
    /**
     * 检查民警身份证号
     */
    private String inspectorSfzh;
    /**
     * 检查民警身份证号
     */
    private String inspector;
    /**
     * 提讯开始时间
     */
    private Date arraignmentStartTime;
    /**
     * 提讯结束时间
     */
    private Date arraignmentEndTime;
    /**
     * 带回检查人
     */
    private String returnInspectorSfzh;
    /**
     * 带回检查人
     */
    private String returnInspector;
    /**
     * 带回检查时间
     */
    private Date returnInspectionTime;
    /**
     * 带回检查结果
     */
    private String returnInspectionResult;
    /**
     * 带回违禁物品登记
     */
    private String returnProhibitedItems;
    /**
     * 带回体表检查登记
     */
    private String returnPhysicalExam;
    /**
     * 带回异常情况登记
     */
    private String returnAbnormalSituations;
    /**
     * 带回监室时间
     */
    private Date returnTime;
    /**
     * 带回民警姓名
     */
    private String returnPolice;
    /**
     * 带回民警身份证号
     */
    private String returnPoliceSfzh;
    /**
     * 带出操作人身份证号
     */
    private String escortingOperatorSfzh;
    /**
     * 带出操作人
     */
    private String escortingOperator;
    /**
     * 带出操作时间
     */
    private Date escortingOperatorTime;
    /**
     * 带回操作人身份证号
     */
    private String returnOperatorSfzh;
    /**
     * 带回操作人
     */
    private String returnOperator;
    /**
     * 带回操作时间
     */
    private Date returnOperatorTime;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

}
