package com.rs.module.acp.controller.admin.db;

import com.rs.module.acp.controller.admin.db.vo.sugstopdetention.*;
import com.rs.module.base.vo.ApproveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;


import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;
import com.rs.module.acp.entity.db.SugStopDetentionDO;
import com.rs.module.acp.service.db.SugStopDetentionService;

@Api(tags = "实战平台-收押业务-建议停拘登记")
@RestController
@RequestMapping("/acp/gj/sugStopDetention")
@Validated
public class SugStopDetentionController {

    @Resource
    private SugStopDetentionService sugStopDetentionService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-收押业务-建议停拘登记")
    public CommonResult<String> createSugStopDetention(@Valid @RequestBody SugStopDetentionSaveReqVO createReqVO) {
        return success(sugStopDetentionService.createSugStopDetention(createReqVO));
    }
    //收拘时 民警填信息后 医生填写信息
    //收拘后 医生填写信息 民警审批
    @PostMapping("/sjsPoliceSave")
    @ApiOperation(value = "实战平台-收押业务-收拘时民警保存-建议停拘登记")
    public CommonResult<String> sjsPoliceCreateSugStopDetention(@Valid @RequestBody SugStopDetentionPoliceSaveReqVO createReqVO) {
        createReqVO.setSaveType("0");
        try {
            return success(sugStopDetentionService.sjsPoliceCreateSugStopDetention(createReqVO));
        }catch (Exception e){
            return CommonResult.error(e.getMessage());
        }
    }
    @PostMapping("/sjsPoliceSubmit")
    @ApiOperation(value = "实战平台-收押业务-收拘时民警提交-建议停拘登记")
    public CommonResult<String> sjsPoliceSubmitSugStopDetention(@Valid @RequestBody SugStopDetentionPoliceSaveReqVO createReqVO) {
        createReqVO.setSaveType("1");
        try {
            return success(sugStopDetentionService.sjsPoliceCreateSugStopDetention(createReqVO));
        }catch (Exception e){
            return CommonResult.error(e.getMessage());
        }
    }
    @PostMapping("/sjsDoctorSave")
    @ApiOperation(value = "实战平台-收押业务-收拘时医生保存-建议停拘登记")
    public CommonResult<String> sjsDoctorCreateSugStopDetention(@Valid @RequestBody SugStopDetentionDoctorSaveReqVO createReqVO) {
        try{
            createReqVO.setSaveType("0");
            return success(sugStopDetentionService.sjsDoctorCreateSugStopDetention(createReqVO));
        }catch (Exception e){
            return CommonResult.error(e.getMessage());
        }
    }
    @PostMapping("/sjsDoctorSubmit")
    @ApiOperation(value = "实战平台-收押业务-收拘时医生提交-建议停拘登记医生")
    public CommonResult<String> sjsDoctorSubmitSugStopDetention(@Valid @RequestBody SugStopDetentionDoctorSaveReqVO createReqVO) {
        try{
            createReqVO.setSaveType("1");
            return success(sugStopDetentionService.sjsDoctorCreateSugStopDetention(createReqVO));
        }catch (Exception e){
            return CommonResult.error(e.getMessage());
        }
    }
    @PostMapping("/sjsLeaderApprove")
    @ApiOperation(value = "实战平台-收押业务-收拘时-领导审批")
    public CommonResult<Boolean> sjsLeaderApprove(@Valid @RequestBody ApproveReqVO approveReqVO) {
        try{
            return success(sugStopDetentionService.leaderApprove(approveReqVO));
        }catch (Exception e){
            return CommonResult.error(e.getMessage());
        }
    }
    @PostMapping("/sjsDirectorApprove")
    @ApiOperation(value = "实战平台-收押业务-收拘时-所长审批")
    public CommonResult<Boolean> sjsDirectorApprove(@Valid @RequestBody ApproveReqVO approveReqVO) {
        try {
            return success(sugStopDetentionService.directorApprove(approveReqVO));
        }catch (Exception e){
            return CommonResult.error(e.getMessage());
        }
    }
    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-收押业务-建议停拘登记")
    public CommonResult<Boolean> updateSugStopDetention(@Valid @RequestBody SugStopDetentionSaveReqVO updateReqVO) {
        sugStopDetentionService.updateSugStopDetention(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-收押业务-建议停拘登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteSugStopDetention(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           sugStopDetentionService.deleteSugStopDetention(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-收押业务-建议停拘登记")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<SugStopDetentionRespVO> getSugStopDetention(@RequestParam("id") String id) {
        SugStopDetentionDO sugStopDetention = sugStopDetentionService.getSugStopDetention(id);
        return success(BeanUtils.toBean(sugStopDetention, SugStopDetentionRespVO.class));
    }
    @PostMapping("/sjhDoctorSave")
    @ApiOperation(value = "实战平台-收押业务-收拘后医生保存-建议停拘登记")
    public CommonResult<String> sjhDoctorSave(@Valid @RequestBody SugStopDetentionSjhDoctorSaveReqVO createReqVO) {
        try{
            createReqVO.setSaveType("0");
            return success(sugStopDetentionService.sjhDoctorsave(createReqVO));
        }catch (Exception e){
            return CommonResult.error(e.getMessage());
        }
    }
    @PostMapping("/sjhDoctorSubmit")
    @ApiOperation(value = "实战平台-收押业务-收拘后医生保存-建议停拘登记")
    public CommonResult<String> sjhDoctorSubmit(@Valid @RequestBody SugStopDetentionSjhDoctorSaveReqVO createReqVO) {
        try{
            createReqVO.setSaveType("1");
            return success(sugStopDetentionService.sjhDoctorsave(createReqVO));
        }catch (Exception e){
            return CommonResult.error(e.getMessage());
        }
    }
    /*@PostMapping("/saveStartProcess")
    @ApiOperation(value = "保存并启动流程")
    public CommonResult<String> saveStartProcess(@Valid @RequestBody SugStopDetentionSaveReqVO createReqVO) {
        try{
            return success(sugStopDetentionService.startProcess(createReqVO));
        }catch (Exception e){
            return CommonResult.error(e.getMessage());
        }
    }*/
    @PostMapping("/sjhPoliceApprove")
    @ApiOperation(value = "实战平台-收押业务-收拘后-民警审批")
    public CommonResult<Boolean> sjhPoliceApprove(@Valid @RequestBody ApproveReqVO approveReqVO) {
        try{
            return success(sugStopDetentionService.policeApprove(approveReqVO));
        }catch (Exception e){
            return CommonResult.error(e.getMessage());
        }
    }
    @PostMapping("/sjhLeaderApprove")
    @ApiOperation(value = "实战平台-收押业务-收拘后-领导审批")
    public CommonResult<Boolean> sjhLeaderApprove(@Valid @RequestBody ApproveReqVO approveReqVO) {
        try{
            return success(sugStopDetentionService.leaderApprove(approveReqVO));
        }catch (Exception e){
            return CommonResult.error(e.getMessage());
        }
    }
    @PostMapping("/sjhDirectorApprove")
    @ApiOperation(value = "实战平台-收押业务-收拘后-所长审批")
    public CommonResult<Boolean> sjhDirectorApprove(@Valid @RequestBody ApproveReqVO approveReqVO) {
        try {
            return success(sugStopDetentionService.directorApprove(approveReqVO));
        }catch (Exception e){
            return CommonResult.error(e.getMessage());
        }
    }
    @PostMapping("/receiveProcess")
    @ApiOperation(value = "法综反馈意见接收处理")
    public CommonResult<Boolean> receiveProcess(@Valid @RequestBody ApproveReqVO approveReqVO) {
        try{
            return success(sugStopDetentionService.receiveProcess(approveReqVO));
        }catch (Exception e){
            return CommonResult.error(e.getMessage());
        }
    }
}
