package com.rs.module.acp.entity.gj;

import lombok.*;

import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-紧急出所登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_emergency_departure")
@KeySequence("acp_gj_emergency_departure_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_emergency_departure")
public class EmergencyDepartureDO extends BaseDO {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 就诊医院
     */
    private String hospital;
    /**
     * 病情说明
     */
    private String symptomDesc;
    /**
     * 经办民警身份证号
     */
    private String operatePoliceSfzh;
    /**
     * 经办民警
     */
    private String operatePolice;
    /**
     * 经办时间
     */
    private Date operateTime;

    @TableField(exist = false)
    private String xb;
    @TableField(exist = false)
    private String nl;

}
