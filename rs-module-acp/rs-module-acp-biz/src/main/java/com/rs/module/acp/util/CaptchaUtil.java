package com.rs.module.acp.util;

import com.rs.module.acp.controller.admin.zh.vo.CaptchaVO;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Random;

@Component
public class CaptchaUtil {

    public CaptchaVO generateMathCaptcha() {
        Random random = new Random();
        int a = random.nextInt(50);
        int b = random.nextInt(50 - a); // 总和不超过50

        String expression = a + " + " + b;
        String answer = String.valueOf(a + b);

        BufferedImage image = new BufferedImage(160, 60, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();

        // 抗锯齿
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 设置背景色（浅色系）
        g.setColor(getRandomColor(220, 250));
        g.fillRect(0, 0, 160, 60);

        // 添加噪点干扰
        for (int i = 0; i < 20; i++) {
            int x = random.nextInt(160);
            int y = random.nextInt(60);
            g.setColor(getRandomColor(150, 200));
            g.drawLine(x, y, x + 1, y + 1);
        }

        // 固定字体样式（增强可读性）
        Font baseFont = new Font("Arial", Font.BOLD, 24);
        g.setFont(baseFont);
        g.setColor(Color.BLACK);

        // 绘制公式文本（逐字符绘制以控制位置）
        String[] parts = expression.split(" ");
        int x = 15;

        for (String part : parts) {
            if (part.equals("+")) {
                g.setColor(new Color(180, 0, 0)); // 加号用红色区分
            } else {
                g.setColor(Color.BLACK);
            }

            g.drawString(part, x, 35);
            x += g.getFontMetrics().stringWidth(part) + 10; // 控制字符间距
        }

        // 绘制 "= ?"
        g.setColor(Color.BLACK);
        g.drawString("= ?", x, 35);

        // 释放资源
        g.dispose();

        // 转为 Base64 字符串
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            ImageIO.write(image, "png", outputStream);
        } catch (IOException e) {
            throw new RuntimeException("生成验证码失败");
        }

        String base64Image = Base64.getEncoder().encodeToString(outputStream.toByteArray());

        CaptchaVO captchaVO = new CaptchaVO();
        captchaVO.setExpression(expression);
        captchaVO.setAnswer(answer);
        captchaVO.setImageBase64(base64Image);

        return captchaVO;
    }

    private Color getRandomColor(int min, int max) {
        Random random = new Random();
        if (min > 255) min = 255;
        if (max > 255) max = 255;
        int r = random.nextInt(max - min + 1) + min;
        int g = random.nextInt(max - min + 1) + min;
        int b = random.nextInt(max - min + 1) + min;
        return new Color(r, g, b);
    }
}



