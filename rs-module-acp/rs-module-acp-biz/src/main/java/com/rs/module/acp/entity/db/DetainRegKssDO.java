package com.rs.module.acp.entity.db;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-羁押业务-看守所收押登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_db_in_record_kss")
@KeySequence("acp_db_detain_reg_kss_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_db_in_record_kss")
public class DetainRegKssDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 姓名
     */
    private String xm;
    /**
     * 姓名拼音
     */
    private String xmpy;
    /**
     * 别名
     */
    private String bm;
    /**
     * 性别
     */
    private String xb;
    /**
     * 出生日期
     */
    private Date csrq;
    /**
     * 证件类型
     */
    private String zjlx;
    /**
     * 证件号码
     */
    private String zjhm;
    /**
     * 国籍
     */
    private String gj;
    /**
     * 民族
     */
    private String mz;
    /**
     * 婚姻状况
     */
    private String hyzk;
    /**
     * 籍贯
     */
    private String jg;
    /**
     * 宗教信仰
     */
    private String zjxy;
    /**
     * 户籍地
     */
    private String hjd;
    /**
     * 户籍地详址
     */
    private String hjdxz;
    /**
     * 现住址
     */
    private String xzz;
    /**
     * 现住址详址
     */
    private String xzzxz;
    /**
     * 文化程度
     */
    private String whcd;
    /**
     * 政治面貌
     */
    private String zzmm;
    /**
     * 职业
     */
    private String zy;
    /**
     * 工作单位
     */
    private String gzdw;
    /**
     * 职务
     */
    private String zw;
    /**
     * 职务级别
     */
    private String zwjb;
    /**
     * 特长(专长)
     */
    private String tc;
    /**
     * 身份
     */
    private String sf;
    /**
     * 特殊身份
     */
    private String tssf;
    /**
     * 信息核查
     */
    private String xxhc;
    /**
     * 是否为在校学生
     */
    private String sfwxzxs;
    /**
     * 学校名称
     */
    private String xxmc;
    /**
     * 管理类别
     */
    private String gllb;
    /**
     * 案由代码
     */
    private String ajlbdm;
    /**
     * 案件类别名称
     */
    private String ajlb;
    /**
     * 案件编号
     */
    private String ajbh;
    /**
     * 人员编号
     */
    private String rybh;
    /**
     * 办案中心编号
     */
    private String bazxbh;
    /**
     * 同案编号
     */
    private String tabh;
    /**
     * 收押凭证
     */
    private String sypz;
    /**
     * 收押凭证文书号
     */
    private String sypzwsh;
    /**
     * 收押凭证文书号
     */
    private String sypzwsdz;
    /**
     * 入所时间
     */
    private Date rssj;
    /**
     * 入所原因
     */
    private String rsyy;
    /**
     * 诉讼环节
     */
    private String sshj;
    /**
     * 送押机关类型
     */
    private String syjglx;
    /**
     * 送押机关名称
     */
    private String syjgmc;
    /**
     * 送押单位
     */
    private String sydw;
    /**
     * 送押人
     */
    private String syr1;
    /**
     * 送押人固话
     */
    private String syrgh1;
    /**
     * 送押人手机
     */
    private String syrsj1;
    /**
     * 送押人
     */
    private String syr2;
    /**
     * 送押人固话
     */
    private String syrgh2;
    /**
     * 送押人手机
     */
    private String syrsj2;
    /**
     * 办案单位类型
     */
    private String badwlx;
    /**
     * 办案单位
     */
    private String badw;
    /**
     * 办案人
     */
    private String bar;
    /**
     * 办案人联系方式
     */
    private String barlxff;
    /**
     * 办案人性别
     */
    private String barxb;
    /**
     * 办案人证件类型
     */
    private String barzjlx;
    /**
     * 办案人证件号码
     */
    private String barzjhm;
    /**
     * 办案环节
     */
    private String bahj;
    /**
     * 羁押日期
     */
    private Date jyrq;
    /**
     * 拘留日期
     */
    private Date jlrq;
    /**
     * 逮捕日期
     */
    private Date dbrq;
    /**
     * 关押期限
     */
    private Date gyqx;
    /**
     * 法律文书号
     */
    private String flwsh;
    /**
     * 简要案情
     */
    private String jyaq;
    /**
     * 涉嫌罪名
     */
    private String sxzm;
    /**
     * 限制会见案件
     */
    private Short xzhjaj;
    /**
     * 限制会见案件
     */
    private Short xzhj;
    /**
     * 重刑犯
     */
    private String zxf;
    /**
     * 档案编号
     */
    private String dabh;
    /**
     * 监区id
     */
    private String areaId;
    /**
     * 监区名称
     */
    private String areaName;
    /**
     * 监室号
     */
    private String jsh;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 床位号
     */
    private String cwh;
    /**
     * 指纹编号
     */
    private String zwbh;
    /**
     * 附物编号
     */
    private String fwbh;
    /**
     * 经办人
     */
    private String jbr;
    /**
     * 经办时间
     */
    private Date jbsj;
    /**
     * 涉毒尿检初查结果
     */
    private String sdnjccjg;
    /**
     * 涉毒尿检单位
     */
    private String sdnjdw;
    /**
     * 涉毒尿检初查结果
     */
    private Date sdnjccsj;
    /**
     * 涉毒尿检检查人
     */
    private String sdnjjcr;
    /**
     * 备注
     */
    private String bz;
    /**
     * 正面照片
     */
    private String frontPhoto;
    /**
     * 办理状态
     */
    private String status;

    /**
     * 案事件相关人员编号
     */
    private String asjxgrybh;

    /**
     * 强制措施类型
     */
    private String qzcslx;

    private String spzt;

    private String approvalResult;

    /**
     * 手环ID
     */
    private String shid;

    /**
     *手环绑定状态
     */
    private String shbdzt;

    /**
     * 手环绑定时间
     */
    private Date sdbdsj;


    private String sfsm;


    private String rydh;


    private String smyy;


    private String smbz;


    private Date jjrq;


    private String jjyy;


    private String jjlqwp;


    private String rslx;

    private String jgrybm;


    @ApiModelProperty("收押民警姓名")
    private String symjxm;

    @ApiModelProperty("看守所联系电话")
    private String ksslxdh;

    private String currentStep;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    /**
     * 以下十个字段是针对二看收治增加的字段
     * @Date 2025/6/24 15:40
     */
    @ApiModelProperty("回执文书号")
    private String hzwsh;

    @ApiModelProperty("原押所-送押单位")
    private String yysSydw;

    @ApiModelProperty("原押所-送押民警")
    private String yysSymj;

    @ApiModelProperty("原押所-送押民警联系电话")
    private String yysSymjlxdh;

    @ApiModelProperty("原押所-表现")
    private String yysBx;

    @ApiModelProperty("送押民警")
    private String symj;

    @ApiModelProperty("是否佩戴眼镜")
    private String sfpdyj;

    @ApiModelProperty("收分配标识服")
    private String sfpbsf;

    @ApiModelProperty("带入监室人")
    private String drjsr;

    @ApiModelProperty("带入监室时间")
    private Date drjssj;

    @ApiModelProperty("标识服颜色")
    private String suitColor;


}
