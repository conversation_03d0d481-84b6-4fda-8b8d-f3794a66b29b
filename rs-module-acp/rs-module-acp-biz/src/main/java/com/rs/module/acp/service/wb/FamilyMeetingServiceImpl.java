package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.dao.wb.FamilyMeetingDao;
import com.rs.module.acp.entity.wb.FamilyMeetingDO;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.util.DicUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.*;


/**
 * 实战平台-窗口业务-家属会见登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FamilyMeetingServiceImpl extends BaseServiceImpl<FamilyMeetingDao, FamilyMeetingDO> implements FamilyMeetingService {

    @Resource
    private FamilyMeetingDao familyMeetingDao;

    @Autowired
    private WbCommonService wbCommonService;

    @Autowired
    private PrisonerService prisonerService;

    @Autowired
    private SocialRelationsService socialRelationsService;

    @Autowired
    private FamilyMeetingCompanionService familyMeetingCompanionService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createFamilyMeeting(FamilyMeetingSaveReqVO createReqVO) {

        //保存家属
        if(CollectionUtil.isNotEmpty(createReqVO.getFamilyList())){
            socialRelationsService.saveSocialRelationsByJgrybm(createReqVO.getFamilyList(),createReqVO.getJgrybm());
        }

        FamilyMeetingDO familyMeeting = BeanUtils.toBean(createReqVO, FamilyMeetingDO.class);

        familyMeeting.setId(StringUtil.getGuid32());

        if(CollectionUtil.isNotEmpty(createReqVO.getFamilyList())){
            List<SocialRelationsChildSaveReqVO> familyList = createReqVO.getFamilyList();

            familyMeeting.setFamilyMember1Id(familyList.get(0).getId());
            familyMeeting.setFamilyMember1Name(familyList.get(0).getName());
            familyMeeting.setFamilyMember1Gender(familyList.get(0).getGender());
            familyMeeting.setFamilyMember1IdType(familyList.get(0).getIdType());
            familyMeeting.setFamilyMember1IdNumber(familyList.get(0).getIdNumber());
            familyMeeting.setFamilyMember1Relationship(familyList.get(0).getRelationship());
            familyMeeting.setFamilyMember1Contact(familyList.get(0).getContact());
            familyMeeting.setFamilyMember1WorkUnit(familyList.get(0).getWorkUnit());
            familyMeeting.setFamilyMember1Address(familyList.get(0).getAddress());
            familyMeeting.setFamilyMember1Occupation(familyList.get(0).getOccupation());
            familyMeeting.setFamilyMember1ImageUrl(familyList.get(0).getImageUrl());
            familyMeeting.setFamilyMember1RelationsAttch(familyList.get(0).getRelationsAttch());


            if(familyList.size()>1){
                familyMeeting.setFamilyMember2Id(familyList.get(1).getId());
                familyMeeting.setFamilyMember2Name(familyList.get(1).getName());
                familyMeeting.setFamilyMember2Gender(familyList.get(1).getGender());
                familyMeeting.setFamilyMember2IdType(familyList.get(1).getIdType());
                familyMeeting.setFamilyMember2IdNumber(familyList.get(1).getIdNumber());
                familyMeeting.setFamilyMember2Relationship(familyList.get(1).getRelationship());
                familyMeeting.setFamilyMember2Contact(familyList.get(1).getContact());
                familyMeeting.setFamilyMember2WorkUnit(familyList.get(1).getWorkUnit());
                familyMeeting.setFamilyMember2Address(familyList.get(1).getAddress());
                familyMeeting.setFamilyMember2Occupation(familyList.get(1).getOccupation());
                familyMeeting.setFamilyMember2ImageUrl(familyList.get(1).getImageUrl());
                familyMeeting.setFamilyMember2RelationsAttch(familyList.get(1).getRelationsAttch());
            }
            if(familyList.size()>2){
                familyMeeting.setFamilyMember3Id(familyList.get(2).getId());
                familyMeeting.setFamilyMember3Name(familyList.get(2).getName());
                familyMeeting.setFamilyMember3Gender(familyList.get(2).getGender());
                familyMeeting.setFamilyMember3IdType(familyList.get(2).getIdType());
                familyMeeting.setFamilyMember3IdNumber(familyList.get(2).getIdNumber());
                familyMeeting.setFamilyMember3Relationship(familyList.get(2).getRelationship());
                familyMeeting.setFamilyMember3Contact(familyList.get(2).getContact());
                familyMeeting.setFamilyMember3WorkUnit(familyList.get(2).getWorkUnit());
                familyMeeting.setFamilyMember3Address(familyList.get(2).getAddress());
                familyMeeting.setFamilyMember3Occupation(familyList.get(2).getOccupation());
                familyMeeting.setFamilyMember3ImageUrl(familyList.get(2).getImageUrl());
                familyMeeting.setFamilyMember3RelationsAttch(familyList.get(2).getRelationsAttch());
            }
        }

        if(ObjectUtil.isNotEmpty(familyMeeting.getApprovalAttachmentPath())){
            String approvalAttachmentPath = wbCommonService.saveFile(null,familyMeeting.getApprovalAttachmentPath());
            familyMeeting.setApprovalAttachmentPath(approvalAttachmentPath);
        }
        if(CollectionUtil.isNotEmpty(createReqVO.getCompanionList())){
            //保存同行人信息
            familyMeetingCompanionService.saveCompanionListByFamilyMeetingId(createReqVO.getCompanionList(),familyMeeting.getId());
        }

        /***
         * 看守所人员，如果诉讼环节是【已决】，则需要进行审批，反之则直接到待签到状态
         * 拘留所人员，新增登记时，则需要审批
         */
        PrisonerVwRespVO prisonerVwRespVO = prisonerService.getPrisonerByJgrybm(createReqVO.getJgrybm());

        if("01".equals(prisonerVwRespVO.getBjgrylx())){
            //看守所
            if("36".equals(prisonerVwRespVO.getSshj())){
                familyMeeting.setStatus("10-1");
            }else {
                familyMeeting.setStatus("0");
            }
        }else if("02".equals(prisonerVwRespVO.getBjgrylx())){
            //拘留所
            familyMeeting.setStatus("10-1");
        }else {
            //其他被监管人员类型，业务暂时没有要求，先采取默认与看守所一样
            familyMeeting.setStatus("0");
        }

        // 发送消息
        wbCommonService.registrationReminder(JSONObject.parseObject(JSON.toJSONString(familyMeeting)), WbConstants.BUSINESS_TYPE_FAMILY_MEETING);

        //根据状态判断，是否发起审批
        if("10-1".equals(familyMeeting.getStatus())){
            Map<String, Object> variables = new HashMap<>();
            variables.put("ywbh", familyMeeting.getId());
            variables.put("busType", MsgBusTypeEnum.CK_JSDXSPHJ.getCode());
            String msgUrl = String.format("/familyMeeting/faceToFace?curId=%s&saveType=approval",familyMeeting.getId());
            JSONObject result = BspApprovalUtil.defaultStartProcess("chuangkouyewujiashuhuijianshenp",familyMeeting.getId(),"家属会见审批",msgUrl,variables);
//            JSONObject result = BspApprovalUtil.defaultStartProcess("chuangkouyewuceshiliuc1",familyMeeting.getId(),"",msgUrl,variables);
            JSONObject data = result.getJSONObject("data");
            JSONObject bpmTrail = data.getJSONObject("bpmTrail");
            familyMeeting.setActInstId(bpmTrail.getString("actInstId"));
            familyMeeting.setTaskId(bpmTrail.getString("taskId"));
        }else {
            WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(familyMeeting,WbInspectionResultsSaveReqVO.class);
            resultsSaveReqVO.setJgrybm(familyMeeting.getJgrybm());
            resultsSaveReqVO.setApplMeetingTime(familyMeeting.getApplyMeetingStartTime());
            resultsSaveReqVO.setBusinessSubType("0301");
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"03","out","0",false);
        }

        familyMeetingDao.insert(familyMeeting);

        return familyMeeting.getId();
    }

    @Override
    public void updateFamilyMeeting(FamilyMeetingSaveReqVO updateReqVO) {
        // 校验存在
        validateFamilyMeetingExists(updateReqVO.getId());
        // 更新
        FamilyMeetingDO updateObj = BeanUtils.toBean(updateReqVO, FamilyMeetingDO.class);
        familyMeetingDao.updateById(updateObj);
    }

    @Override
    public void deleteFamilyMeeting(String id) {
        // 校验存在
        validateFamilyMeetingExists(id);
        // 删除
        familyMeetingDao.deleteById(id);
    }

    private void validateFamilyMeetingExists(String id) {
        if (familyMeetingDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-家属会见登记数据不存在");
        }
    }

    @Override
    public FamilyMeetingDO getFamilyMeeting(String id) {
        return familyMeetingDao.selectById(id);
    }

    @Override
    public PageResult<FamilyMeetingDO> getFamilyMeetingPage(FamilyMeetingPageReqVO pageReqVO) {
        return familyMeetingDao.selectPage(pageReqVO);
    }

    @Override
    public List<FamilyMeetingDO> getFamilyMeetingList(FamilyMeetingListReqVO listReqVO) {
        return familyMeetingDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean signIn(String id, String checkInTime) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
//        ArraignmentDO arraignmentDO = signInInit(id);
        LambdaUpdateWrapper<FamilyMeetingDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(FamilyMeetingDO::getId, id).set(FamilyMeetingDO::getCheckInTime, DateUtil.parse(checkInTime,"yyyy-MM-dd HH:mm:ss"))
                .set(FamilyMeetingDO::getCheckInPoliceSfzh,sessionUser.getIdCard())
                .set(FamilyMeetingDO::getCheckInPolice, sessionUser.getName()).set(FamilyMeetingDO::getStatus,"1");
        return update(lambdaUpdateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean allocationRoom(String id, String roomId) {
        return update(new LambdaUpdateWrapper<FamilyMeetingDO>().eq(FamilyMeetingDO::getId,id).set(FamilyMeetingDO::getRoomId,roomId).set(FamilyMeetingDO::getStatus,"2"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean escortingInspect(FamilyMeetingSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        LambdaUpdateWrapper<FamilyMeetingDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(FamilyMeetingDO::getId, updateReqVO.getId())
                .set(FamilyMeetingDO::getEscortingPolice, updateReqVO.getEscortingPolice())
                .set(FamilyMeetingDO::getEscortingPoliceSfzh, updateReqVO.getEscortingPoliceSfzh())
                .set(FamilyMeetingDO::getEscortingTime, updateReqVO.getEscortingTime())
                .set(FamilyMeetingDO::getInspectionResult, updateReqVO.getInspectionResult())
                .set(FamilyMeetingDO::getInspectionTime, updateReqVO.getInspectionTime())
                .set(FamilyMeetingDO::getInspector, updateReqVO.getInspector())
                .set(FamilyMeetingDO::getInspectorSfzh, updateReqVO.getInspectorSfzh())
                .set(FamilyMeetingDO::getMeetingStartTime, updateReqVO.getMeetingStartTime())
                .set(FamilyMeetingDO::getStatus,"3");

        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorSfzh())){
            lambdaUpdateWrapper.set(FamilyMeetingDO::getEscortingOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(FamilyMeetingDO::getEscortingOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorTime())){
            lambdaUpdateWrapper.set(FamilyMeetingDO::getEscortingOperatorTime,new Date());
        }

        if("0".equals(updateReqVO.getDataSources())){
            FamilyMeetingDO familyMeetingDO = getById(updateReqVO.getId());
            WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(updateReqVO,WbInspectionResultsSaveReqVO.class);
            resultsSaveReqVO.setJgrybm(familyMeetingDO.getJgrybm());
            resultsSaveReqVO.setBusinessSubType("0301");
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"03","out","3",false);
        }

        return update(lambdaUpdateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean returnInspection(FamilyMeetingSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        LambdaUpdateWrapper<FamilyMeetingDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(FamilyMeetingDO::getId, updateReqVO.getId())
                .set(FamilyMeetingDO::getMeetingEndTime, updateReqVO.getMeetingEndTime())
                .set(FamilyMeetingDO::getReturnInspectorSfzh, updateReqVO.getReturnInspectorSfzh())
                .set(FamilyMeetingDO::getReturnInspector, updateReqVO.getReturnInspector())
                .set(FamilyMeetingDO::getReturnInspectionTime, updateReqVO.getReturnInspectionTime())
                .set(FamilyMeetingDO::getReturnInspectionResult, updateReqVO.getReturnInspectionResult())
                .set(FamilyMeetingDO::getReturnProhibitedItems, updateReqVO.getReturnProhibitedItems())
                .set(FamilyMeetingDO::getReturnPhysicalExam, updateReqVO.getReturnPhysicalExam())
                .set(FamilyMeetingDO::getReturnAbnormalSituations, updateReqVO.getReturnAbnormalSituations())
                .set(FamilyMeetingDO::getReturnTime, updateReqVO.getReturnTime())
                .set(FamilyMeetingDO::getReturnPolice, updateReqVO.getReturnPolice())
                .set(FamilyMeetingDO::getReturnPoliceSfzh, updateReqVO.getReturnPoliceSfzh())
                .set(FamilyMeetingDO::getStatus,"4");
        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorSfzh())){
            lambdaUpdateWrapper.set(FamilyMeetingDO::getReturnOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(FamilyMeetingDO::getReturnOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorTime())){
            lambdaUpdateWrapper.set(FamilyMeetingDO::getReturnOperatorTime,new Date());
        }

        if("0".equals(updateReqVO.getDataSources())){
            FamilyMeetingDO familyMeetingDO = getById(updateReqVO.getId());
            WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(updateReqVO,WbInspectionResultsSaveReqVO.class);
            resultsSaveReqVO.setJgrybm(familyMeetingDO.getJgrybm());
            resultsSaveReqVO.setApplMeetingTime(familyMeetingDO.getApplyMeetingEndTime());
            resultsSaveReqVO.setBusinessSubType("0301");
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"03","in","3",false);
        }
        return update(lambdaUpdateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean additionalRecording(FamilyMeetingSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        LambdaUpdateWrapper<FamilyMeetingDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(FamilyMeetingDO::getId, updateReqVO.getId())
                .set(FamilyMeetingDO::getCheckInTime, updateReqVO.getCheckInTime())
                .set(FamilyMeetingDO::getCheckInPoliceSfzh,sessionUser.getIdCard())
                .set(FamilyMeetingDO::getCheckInPolice, sessionUser.getName())
                .set(FamilyMeetingDO::getEscortingPolice, updateReqVO.getEscortingPolice())
                .set(FamilyMeetingDO::getEscortingPoliceSfzh, updateReqVO.getEscortingPoliceSfzh())
                .set(FamilyMeetingDO::getEscortingTime, updateReqVO.getEscortingTime())
                .set(FamilyMeetingDO::getInspectionResult, updateReqVO.getInspectionResult())
                .set(FamilyMeetingDO::getInspectionTime, updateReqVO.getInspectionTime())
                .set(FamilyMeetingDO::getInspector, updateReqVO.getInspector())
                .set(FamilyMeetingDO::getInspectorSfzh, updateReqVO.getInspectorSfzh())
                .set(FamilyMeetingDO::getMeetingStartTime, updateReqVO.getMeetingStartTime())
                .set(FamilyMeetingDO::getMeetingEndTime, updateReqVO.getMeetingEndTime())
                .set(FamilyMeetingDO::getReturnInspectorSfzh, updateReqVO.getReturnInspectorSfzh())
                .set(FamilyMeetingDO::getReturnInspector, updateReqVO.getReturnInspector())
                .set(FamilyMeetingDO::getReturnInspectionTime, updateReqVO.getReturnInspectionTime())
                .set(FamilyMeetingDO::getReturnInspectionResult, updateReqVO.getReturnInspectionResult())
                .set(FamilyMeetingDO::getReturnProhibitedItems, updateReqVO.getReturnProhibitedItems())
                .set(FamilyMeetingDO::getReturnPhysicalExam, updateReqVO.getReturnPhysicalExam())
                .set(FamilyMeetingDO::getReturnAbnormalSituations, updateReqVO.getReturnAbnormalSituations())
                .set(FamilyMeetingDO::getReturnTime, updateReqVO.getReturnTime())
                .set(FamilyMeetingDO::getReturnPolice, updateReqVO.getReturnPolice())
                .set(FamilyMeetingDO::getReturnPoliceSfzh, updateReqVO.getReturnPoliceSfzh())
                .set(FamilyMeetingDO::getStatus,"4");

        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorSfzh())){
            lambdaUpdateWrapper.set(FamilyMeetingDO::getEscortingOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(FamilyMeetingDO::getEscortingOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorTime())){
            lambdaUpdateWrapper.set(FamilyMeetingDO::getEscortingOperatorTime,new Date());
        }

        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorSfzh())){
            lambdaUpdateWrapper.set(FamilyMeetingDO::getReturnOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(FamilyMeetingDO::getReturnOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorTime())){
            lambdaUpdateWrapper.set(FamilyMeetingDO::getReturnOperatorTime,new Date());
        }

        if("0".equals(updateReqVO.getDataSources())){
            FamilyMeetingDO familyMeetingDO = getById(updateReqVO.getId());
            WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(updateReqVO,WbInspectionResultsSaveReqVO.class);
            resultsSaveReqVO.setJgrybm(familyMeetingDO.getJgrybm());
            resultsSaveReqVO.setBusinessSubType("0301");
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"03","out","3",true);
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"03","in","3",true);
        }

        return update(lambdaUpdateWrapper);
    }

    @Override
    public List<JSONObject> getOnSiteNumbering() {
        return familyMeetingDao.getOnSiteNumbering(LocalTime.now().getHour() < 12?"morning":"afternoon",SessionUserUtil.getSessionUser().getOrgCode());
    }

    @Override
    public PageResult<FamilyMeetingRespVO> getHistoryMeetingByJgrybm(String jgrybm, int pageNo, int pageSize) {
        Page<FamilyMeetingDO> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<FamilyMeetingDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(FamilyMeetingDO::getId,FamilyMeetingDO::getAddTime,FamilyMeetingDO::getFamilyMember1Name,
                FamilyMeetingDO::getFamilyMember2Name,FamilyMeetingDO::getFamilyMember3Name,FamilyMeetingDO::getMeetingStartTime,
                FamilyMeetingDO::getMeetingEndTime);
        lambdaQueryWrapper.eq(FamilyMeetingDO::getJgrybm,jgrybm)
                .orderByDesc(FamilyMeetingDO::getAddTime);

        Page<FamilyMeetingDO> familyMeetingDOPage = page(page,lambdaQueryWrapper);
        List<FamilyMeetingRespVO> familyMeetingRespVOList = BeanUtils.toBean(familyMeetingDOPage.getRecords(),FamilyMeetingRespVO.class);

        if(CollectionUtil.isEmpty(familyMeetingRespVOList)){
            return new PageResult<>(new ArrayList<>() ,0L);
        }

        for(FamilyMeetingDO familyMeetingDO:familyMeetingDOPage.getRecords()){
            //姓名
            StringBuilder familyMemberName = new StringBuilder();
            familyMemberName.append(familyMeetingDO.getFamilyMember1Name());
            familyMemberName.append("、");
            familyMemberName.append(familyMeetingDO.getFamilyMember2Name());
            if(ObjectUtil.isNotEmpty(familyMeetingDO.getFamilyMember3Name())){
                familyMemberName.append("、");
                familyMemberName.append(familyMeetingDO.getFamilyMember3Name());
            }
            //证件号码
            StringBuilder IdNumber = new StringBuilder();
            IdNumber.append(familyMeetingDO.getFamilyMember1IdNumber());
            IdNumber.append("、");
            IdNumber.append(familyMeetingDO.getFamilyMember2IdNumber());
            if(ObjectUtil.isNotEmpty(familyMeetingDO.getFamilyMember3IdNumber())){
                IdNumber.append("、");
                IdNumber.append(familyMeetingDO.getFamilyMember3IdNumber());
            }

            //社会关系
            StringBuilder relationship = new StringBuilder();
            if(ObjectUtil.isNotEmpty(familyMeetingDO.getFamilyMember1Relationship())){
                relationship.append(DicUtils.translate("ZD_GABBZ_SHGX",familyMeetingDO.getFamilyMember1Relationship()));
            }
            relationship.append("、");
            if(ObjectUtil.isNotEmpty(familyMeetingDO.getFamilyMember2Relationship())){
                relationship.append(DicUtils.translate("ZD_GABBZ_SHGX",familyMeetingDO.getFamilyMember2Relationship()));
            }
            if(ObjectUtil.isNotEmpty(familyMeetingDO.getFamilyMember3Relationship())){
                relationship.append("、");
                relationship.append(DicUtils.translate("ZD_GABBZ_SHGX",familyMeetingDO.getFamilyMember3Relationship()));
            }
            //会见时间
            StringBuilder meetingTime = new StringBuilder();
            if(ObjectUtil.isNotEmpty(familyMeetingDO.getMeetingStartTime())){
                meetingTime.append(DateUtil.format(familyMeetingDO.getMeetingStartTime(),"yyyy-MM-dd HH:mm"));
            }
            meetingTime.append("~");
            if(ObjectUtil.isNotEmpty(familyMeetingDO.getMeetingEndTime())){
                meetingTime.append(DateUtil.format(familyMeetingDO.getMeetingEndTime(),"HH:mm"));
            }

            for(FamilyMeetingRespVO familyMeetingRespVO:familyMeetingRespVOList){
                if(familyMeetingRespVO.getId().equals(familyMeetingDO.getId())){
                    familyMeetingRespVO.setFamilyName(familyMemberName.toString());
                    familyMeetingRespVO.setIdNumber(IdNumber.toString());
                    familyMeetingRespVO.setRelationship(relationship.toString());
                    familyMeetingRespVO.setMeetingTime(meetingTime.toString());
                    break;
                }
            }
        }

        return new PageResult<>(familyMeetingRespVOList ,familyMeetingDOPage.getTotal());
    }

    @Override
    public FamilyMeetingRespVO getFamilyMeetingById(String id) {

        FamilyMeetingDO familyMeetingDO = getById(id);

        FamilyMeetingRespVO familyMeetingRespVO = BeanUtils.toBean(familyMeetingDO,FamilyMeetingRespVO.class);

        if(ObjectUtil.isNotEmpty(familyMeetingRespVO.getApprovalAttachmentPath())){
            familyMeetingRespVO.setApprovalAttachmentPath(wbCommonService.getFile(familyMeetingRespVO.getApprovalAttachmentPath()));
        }

        //获取同行人信息
        familyMeetingRespVO.setCompanionList(familyMeetingCompanionService.getCompanionListByFamilyMeetingId(id));

        //家属列表
        List<SocialRelationsRespVO> familyList = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(familyMeetingDO.getFamilyMember1Id())){
            SocialRelationsRespVO socialRelationsRespVO = new SocialRelationsRespVO();
            socialRelationsRespVO.setId(familyMeetingDO.getFamilyMember1Id());
            socialRelationsRespVO.setName(familyMeetingDO.getFamilyMember1Name());
            socialRelationsRespVO.setGender(familyMeetingDO.getFamilyMember1Gender());
            socialRelationsRespVO.setIdType(familyMeetingDO.getFamilyMember1IdType());
            socialRelationsRespVO.setIdNumber(familyMeetingDO.getFamilyMember1IdNumber());
            socialRelationsRespVO.setRelationship(familyMeetingDO.getFamilyMember1Relationship());
            socialRelationsRespVO.setContact(familyMeetingDO.getFamilyMember1Contact());
            socialRelationsRespVO.setWorkUnit(familyMeetingDO.getFamilyMember1WorkUnit());
            socialRelationsRespVO.setAddress(familyMeetingDO.getFamilyMember1Address());
            socialRelationsRespVO.setOccupation(familyMeetingDO.getFamilyMember1Occupation());
            socialRelationsRespVO.setImageUrl(familyMeetingDO.getFamilyMember1ImageUrl());
            if(ObjectUtil.isNotEmpty(familyMeetingDO.getFamilyMember1RelationsAttch())){
                socialRelationsRespVO.setRelationsAttch(wbCommonService.getFile(familyMeetingDO.getFamilyMember1RelationsAttch()));
            }
            familyList.add(socialRelationsRespVO);
        }

        if(ObjectUtil.isNotEmpty(familyMeetingDO.getFamilyMember2Id())){
            SocialRelationsRespVO socialRelationsRespVO = new SocialRelationsRespVO();
            socialRelationsRespVO.setId(familyMeetingDO.getFamilyMember2Id());
            socialRelationsRespVO.setName(familyMeetingDO.getFamilyMember2Name());
            socialRelationsRespVO.setGender(familyMeetingDO.getFamilyMember2Gender());
            socialRelationsRespVO.setIdType(familyMeetingDO.getFamilyMember2IdType());
            socialRelationsRespVO.setIdNumber(familyMeetingDO.getFamilyMember2IdNumber());
            socialRelationsRespVO.setRelationship(familyMeetingDO.getFamilyMember2Relationship());
            socialRelationsRespVO.setContact(familyMeetingDO.getFamilyMember2Contact());
            socialRelationsRespVO.setWorkUnit(familyMeetingDO.getFamilyMember2WorkUnit());
            socialRelationsRespVO.setAddress(familyMeetingDO.getFamilyMember2Address());
            socialRelationsRespVO.setOccupation(familyMeetingDO.getFamilyMember2Occupation());
            socialRelationsRespVO.setImageUrl(familyMeetingDO.getFamilyMember2ImageUrl());
            if(ObjectUtil.isNotEmpty(familyMeetingDO.getFamilyMember2RelationsAttch())){
                socialRelationsRespVO.setRelationsAttch(wbCommonService.getFile(familyMeetingDO.getFamilyMember2RelationsAttch()));
            }
            familyList.add(socialRelationsRespVO);
        }

        if(ObjectUtil.isNotEmpty(familyMeetingDO.getFamilyMember3Id())){
            SocialRelationsRespVO socialRelationsRespVO = new SocialRelationsRespVO();
            socialRelationsRespVO.setId(familyMeetingDO.getFamilyMember3Id());
            socialRelationsRespVO.setName(familyMeetingDO.getFamilyMember3Name());
            socialRelationsRespVO.setGender(familyMeetingDO.getFamilyMember3Gender());
            socialRelationsRespVO.setIdType(familyMeetingDO.getFamilyMember3IdType());
            socialRelationsRespVO.setIdNumber(familyMeetingDO.getFamilyMember3IdNumber());
            socialRelationsRespVO.setRelationship(familyMeetingDO.getFamilyMember3Relationship());
            socialRelationsRespVO.setContact(familyMeetingDO.getFamilyMember3Contact());
            socialRelationsRespVO.setWorkUnit(familyMeetingDO.getFamilyMember3WorkUnit());
            socialRelationsRespVO.setAddress(familyMeetingDO.getFamilyMember3Address());
            socialRelationsRespVO.setOccupation(familyMeetingDO.getFamilyMember3Occupation());
            socialRelationsRespVO.setImageUrl(familyMeetingDO.getFamilyMember3ImageUrl());
            if(ObjectUtil.isNotEmpty(familyMeetingDO.getFamilyMember3RelationsAttch())){
                socialRelationsRespVO.setRelationsAttch(wbCommonService.getFile(familyMeetingDO.getFamilyMember3RelationsAttch()));
            }
            familyList.add(socialRelationsRespVO);
        }
        familyMeetingRespVO.setFamilyList(familyList);
        return familyMeetingRespVO;
    }
}
