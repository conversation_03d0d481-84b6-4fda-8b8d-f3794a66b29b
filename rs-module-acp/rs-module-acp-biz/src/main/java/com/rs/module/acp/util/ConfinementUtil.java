package com.rs.module.acp.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.DateUtil;
import com.rs.module.acp.controller.admin.gj.vo.confinement.ConfinementExtendRespVO;
import com.rs.module.acp.controller.admin.gj.vo.confinement.ConfinementFlowApproveTrackVO;
import com.rs.module.acp.controller.admin.gj.vo.confinement.ConfinementRegRespVO;
import com.rs.module.acp.controller.admin.gj.vo.confinement.ConfinementRemoveRespVO;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsRespVO;
import com.rs.module.acp.entity.gj.confinement.ConfinementExtendDO;
import com.rs.module.acp.entity.gj.confinement.ConfinementRegDO;
import com.rs.module.acp.entity.gj.confinement.ConfinementRemoveDO;
import com.rs.util.DicUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

public class ConfinementUtil {
    /**
     * 实战平台-管教业务-禁闭登记
     */
    public static final String FLOW_DEF_KEY_CONFINEMENT_REG = "guanjiaoyewujinbisuolingdaoshenpi";
    /**
     * 实战平台-管教业务-延长禁闭
     */
    public static final String FLOW_DEF_KEY_CONFINEMENT_EXTEND = "guanjiaoyewuyanchangjinbisuolingdaoshenpi";
    /**
     * 实战平台-管教业务-解除禁闭
     */
    public static final String FLOW_DEF_KEY_CONFINEMENT_REMOVE = "guanjiaoyewutiqianjiechujinbisuolingdaoshenpi";

    @AllArgsConstructor
    @Getter
    public enum ConfinementEnum {
        SUBMISSION("confinementSubmission", "禁闭呈批"),
        APPROVE("confinementApprove", "禁闭审批"),
        REG("confinementreg", "禁闭登记"),
        EXTENDSUBMISSION("extendSubmission", "延长禁闭呈批"),
        EXTENDAPPROVE("extendApprove", "延长禁闭审批"),
        REMOVESUBMISSION("removeSubmission", "解除禁闭呈批"),
        REMOVEAPPROVE("removeApprove", "解除禁闭审批"),
        REMOVEREG("removeReg", "解除禁闭登记");
        private String code;
        private String name;
        public static ConfinementEnum getByCode(String code){
            for(ConfinementEnum cnfinementEnum : ConfinementEnum.values()){
                if(Objects.equals(cnfinementEnum.getCode(),code)){
                    return cnfinementEnum;
                }
            }
            return null;
        }
        public static String getName(String code){
            ConfinementEnum cnfinementEnum = getByCode(code);
            return cnfinementEnum == null ? null : cnfinementEnum.getName();
        }
    }
    @AllArgsConstructor
    @Getter
    public enum ApprovalStatusEnum {
        PENDING_APPROVAL("01", "呈批待审批"),
        EXTEND_PENDING("02", "延长待审批"),
        REMOVE_PENDING("03", "提前解除待审批"),
        REG_PENDING("04", "待禁闭登记"),
        CONFINING("05", "禁闭中"),
        RELEASE_PENDING("06", "待解除"),
        RELEASED("07", "已解除"),
        APPROVAL_REJECTED("08", "呈批不通过");

        private String code;
        private String name;

        /**
         * 根据编码获取枚举实例
         */
        public static ApprovalStatusEnum getByCode(String code) {
            for (ApprovalStatusEnum status : values()) {
                if (Objects.equals(status.getCode(), code)) {
                    return status;
                }
            }
            return null;
        }
        /**
         * 根据编码获取描述名称
         */
        public static String getName(String code) {
            ApprovalStatusEnum status = getByCode(code);
            return status == null ? null : status.getName();
        }
    }

    /**
     * 根据流程轨迹JSONObject进行转换只处理审批节点数据
     * @param bspApprovalTrack
     * @return
     */
    public static List<ConfinementFlowApproveTrackVO> converBspApprovalTrack(JSONObject bspApprovalTrack,ConfinementEnum businessType){
        List<ConfinementFlowApproveTrackVO> list = new ArrayList<>();

        if (bspApprovalTrack == null || !bspApprovalTrack.getBoolean("success")) {
            throw new RuntimeException("请求审批结果出错");
        }

        JSONArray data = bspApprovalTrack.getJSONArray("data");
        if (data == null || data.isEmpty()) {
            throw new RuntimeException("请求审批结果为空");
        }
        for (Object obj: data) {
            JSONObject track = JSON.parseObject(JSON.toJSONString(obj));
            //ConfinementFlowApproveTrackVO confinementFlowApproveTrackVO = new ConfinementFlowApproveTrackVO();
            //String taskKey = track.getString("taskKey");
            String taskName = track.getString("taskName");
            if(taskName.contains("审批")){
                list.add(buildTrackNodeInfoFromBPM(track,businessType));
            }
        }
        return list;
    }

    /**
     * 从流程审批节点记录转换轨迹
     * @param track
     * @return
     */
    public static ConfinementFlowApproveTrackVO buildTrackNodeInfoFromBPM(JSONObject track,ConfinementEnum businessType){
        List<JSONObject> list = new ArrayList<>();
        int nodeStatus = -1;
        if(track.getInteger("status") == 1) {//已经审批完
            nodeStatus= 1;
            list.add(buildTrackNodeInfo("executeUserName", "审批人", track.getString("executeUserName")));
            list.add(buildTrackNodeInfo("approveTime", "审批时间", track.getString("endTime")));
            list.add(buildTrackNodeInfo("isApprove", "审批结果", "1".equals(track.getString("isApprove"))?"同意":"不同意"));
            list.add(buildTrackNodeInfo("approvalContent", "审批意见", track.getString("approvalContent")));
        }else{
            list.add(buildTrackNodeInfo("executeUserName", "审批人", ""));
            list.add(buildTrackNodeInfo("approveTime", "审批时间", ""));
            list.add(buildTrackNodeInfo("isApprove", "审批结果", ""));
            list.add(buildTrackNodeInfo("approvalContent", "审批意见", ""));
        }
        ConfinementFlowApproveTrackVO nodeInfo = new ConfinementFlowApproveTrackVO();
        nodeInfo.setNodeName(businessType.getName());
        nodeInfo.setNodeKey(businessType.getCode());
        nodeInfo.setNodeStatus(nodeStatus);
        nodeInfo.setNodeCreateTime(track.getString("createTime"));
        nodeInfo.setNodeInfo(list);
        return nodeInfo;
    };
    public static JSONObject buildTrackNodeInfo(String colName,String col,Object value){
        JSONObject nodeInfo = new JSONObject();
        nodeInfo.put("key",colName);
        nodeInfo.put("keyName",col);
        nodeInfo.put("val",value);
        return nodeInfo;
    };

    /**
     * 转换延长禁闭呈批信息
     * @param respVO
     * @return
     */
    public static ConfinementFlowApproveTrackVO buildTrackNodeInfoFromExtend(ConfinementExtendRespVO respVO){
        List<JSONObject> list = new ArrayList<>();
        list.add(buildTrackNodeInfo("addUserName","呈批人",respVO.getAddUserName()));
        list.add(buildTrackNodeInfo("addTime","呈批时间",respVO.getAddTime()));
        list.add(buildTrackNodeInfo("confinementReason","延长禁闭天数",respVO.getExtendDay()));
        ConfinementFlowApproveTrackVO nodeInfo = new ConfinementFlowApproveTrackVO();
        nodeInfo.setNodeName(ConfinementEnum.EXTENDSUBMISSION.getName());
        nodeInfo.setNodeKey(ConfinementEnum.EXTENDSUBMISSION.getCode());
        nodeInfo.setNodeStatus(1);
        nodeInfo.setNodeCreateTime(DateUtil.formatDate(respVO.getAddTime(),null));
        nodeInfo.setNodeInfo(list);
        return nodeInfo;
    };
    /**
     * 转换提前解除禁闭呈批信息
     * @param respVO
     * @return
     */
    public static ConfinementFlowApproveTrackVO buildTrackNodeInfoFromRemove(ConfinementRemoveRespVO respVO){
        List<JSONObject> list = new ArrayList<>();
        list.add(buildTrackNodeInfo("addUserName","呈批人",respVO.getAddUserName()));
        list.add(buildTrackNodeInfo("addTime","呈批时间",respVO.getAddTime()));
        list.add(buildTrackNodeInfo("confinementDays","提前解除理由",respVO.getRemoveReason()));
        ConfinementFlowApproveTrackVO nodeInfo = new ConfinementFlowApproveTrackVO();
        nodeInfo.setNodeName(ConfinementEnum.REMOVESUBMISSION.getName());
        nodeInfo.setNodeKey(ConfinementEnum.REMOVESUBMISSION.getCode());
        nodeInfo.setNodeStatus(1);
        nodeInfo.setNodeCreateTime(DateUtil.formatDate(respVO.getAddTime(),null));
        nodeInfo.setNodeInfo(list);
        return nodeInfo;
    };

    /**
     * 转换禁闭呈批信息
     * @param respVO
     * @return
     */
    public static ConfinementFlowApproveTrackVO buildTrackNodeInfoFromReg(ConfinementRegRespVO respVO){
        List<JSONObject> list = new ArrayList<>();
        list.add(buildTrackNodeInfo("addUserName","呈批人",respVO.getAddUserName()));
        list.add(buildTrackNodeInfo("addTime","呈批时间",respVO.getAddTime()));
        list.add(buildTrackNodeInfo("confinementReason","禁闭原因",DicUtils.translate("ZD_GJYW_JBYY", respVO.getConfinementReason())));
        list.add(buildTrackNodeInfo("confinementDays","禁闭天数",respVO.getConfinementDays()));
        list.add(buildTrackNodeInfo("roomName","禁闭监室",respVO.getRoomName()));
        ConfinementFlowApproveTrackVO nodeInfo = new ConfinementFlowApproveTrackVO();
        nodeInfo.setNodeName(ConfinementEnum.SUBMISSION.getName());
        nodeInfo.setNodeKey(ConfinementEnum.SUBMISSION.getCode());
        nodeInfo.setNodeStatus(1);
        nodeInfo.setNodeCreateTime(DateUtil.formatDate(respVO.getAddTime(),null));
        nodeInfo.setNodeInfo(list);
        return nodeInfo;
    }

    //转换InOutRecordsRespVO带入带出信息
    public static ConfinementFlowApproveTrackVO buildTrackNodeInfoFromInOutRecords(Date startDate ,Date removeRegCreateTime,List<InOutRecordsRespVO> listRespVO){
        ConfinementFlowApproveTrackVO nodeInfo = new ConfinementFlowApproveTrackVO();
        if(CollectionUtil.isNull(listRespVO)) return nodeInfo;
        List<JSONObject> list = new ArrayList<>();
        Date outRoomTime = null;
        Date inRoomTime = null;
        String inspectionResult = "";
        for (InOutRecordsRespVO respVO : listRespVO){
            if("01".equals(respVO.getInoutType())){//出监
                outRoomTime = respVO.getInoutTime();
            }else if("02".equals(respVO.getInoutType())){//入监
                inRoomTime = respVO.getInoutTime();
                inspectionResult = respVO.getInspectionResult();
            }

        }
        list.add(buildTrackNodeInfo("startDate","禁闭开始日期",startDate));
        list.add(buildTrackNodeInfo("outRoomTime","带出原监室时间",outRoomTime));
        list.add(buildTrackNodeInfo("confinementReason","带入禁闭监室时间",inRoomTime));
        list.add(buildTrackNodeInfo("confinementDays","检查结果","0".equals(inspectionResult)?"正常":"异常"));
        nodeInfo.setNodeName(ConfinementEnum.REG.getName());
        nodeInfo.setNodeKey(ConfinementEnum.REG.getCode());
        nodeInfo.setNodeStatus(1);
        nodeInfo.setNodeCreateTime(DateUtil.formatDate(removeRegCreateTime,null));
        nodeInfo.setNodeInfo(list);
        return nodeInfo;
    }
    public static ConfinementFlowApproveTrackVO buildTrackNodeInfoFromRemove(ConfinementRemoveRespVO respVO,Date outRoomTime){
        List<JSONObject> list = new ArrayList<>();

        list.add(buildTrackNodeInfo("startDate","登记人",respVO.getAddUserName()));
        list.add(buildTrackNodeInfo("outRoomTime","登记时间",respVO.getAddTime()));
        list.add(buildTrackNodeInfo("confinementReason","解除使用时间",outRoomTime));
        list.add(buildTrackNodeInfo("confinementDays","禁闭解除理由",respVO.getRemoveReason()));
        ConfinementFlowApproveTrackVO nodeInfo = new ConfinementFlowApproveTrackVO();
        nodeInfo.setNodeName(ConfinementEnum.REMOVEREG.getName());
        nodeInfo.setNodeKey(ConfinementEnum.REMOVEREG.getCode());
        nodeInfo.setNodeStatus(1);
        nodeInfo.setNodeCreateTime(DateUtil.formatDate(respVO.getAddTime(),null));
        nodeInfo.setNodeInfo(list);
        return nodeInfo;
    }
}
