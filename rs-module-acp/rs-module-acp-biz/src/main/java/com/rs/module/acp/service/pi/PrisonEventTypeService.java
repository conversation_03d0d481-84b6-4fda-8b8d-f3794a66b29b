package com.rs.module.acp.service.pi;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypeListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypePageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypeRespVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypeSaveReqVO;
import com.rs.module.acp.entity.pi.PrisonEventTypeDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-巡视管控-所情事件类型 Service 接口
 *
 * <AUTHOR>
 */
public interface PrisonEventTypeService extends IBaseService<PrisonEventTypeDO>{

    /**
     * 创建实战平台-巡视管控-所情事件类型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrisonEventType(@Valid PrisonEventTypeSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-所情事件类型
     *
     * @param updateReqVO 更新信息
     */
    void updatePrisonEventType(@Valid PrisonEventTypeSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-所情事件类型
     *
     * @param id 编号
     */
    void deletePrisonEventType(String id);

    /**
     * 获得实战平台-巡视管控-所情事件类型
     *
     * @param id 编号
     * @return 实战平台-巡视管控-所情事件类型
     */
    PrisonEventTypeDO getPrisonEventType(String id);

    /**
    * 获得实战平台-巡视管控-所情事件类型分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-巡视管控-所情事件类型分页
    */
    PageResult<PrisonEventTypeRespVO> getPrisonEventTypePage(PrisonEventTypePageReqVO pageReqVO);

    /**
    * 获得实战平台-巡视管控-所情事件类型列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-巡视管控-所情事件类型列表
    */
    List<PrisonEventTypeRespVO> getPrisonEventTypeList(PrisonEventTypeListReqVO listReqVO);

    /**
     * 实战平台-巡视管控-获得所情事件类型
     *
     * @param id 编号
     * @return 实战平台-巡视管控-所情事件类型
     */
    PrisonEventTypeRespVO getPrisonEventTypeById(String id);

    /**
     * 启用或禁用所情类型
     * @param id
     * @return
     */
    boolean changeStatus(String id);
}
