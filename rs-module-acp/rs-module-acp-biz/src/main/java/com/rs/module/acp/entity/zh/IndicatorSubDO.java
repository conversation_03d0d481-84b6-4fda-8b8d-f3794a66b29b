package com.rs.module.acp.entity.zh;

import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 综合管理-绩效考核子指标 DO
 *
 * <AUTHOR>
 */
@TableName("acp_zh_indicator_sub")
@KeySequence("acp_zh_indicator_sub_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_indicator_sub")
public class IndicatorSubDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 所属主指标ID
     */
    private String mainIndicatorId;
    /**
     * 是否必填
     */
    private Short isRequired;
    /**
     * 指标描述
     */
    private String description;
    /**
     * 加减分，字典：ADD：加分、SUBTRACT：扣分
     */
    private String addSubtract;
    /**
     * 分值类型，字典：FIXED：固定分，RANGE：范围分
     */
    private String scoreType;
    /**
     * 基础分值
     */
    private BigDecimal baseScore;
    /**
     * 最小分值
     */
    private BigDecimal minScore;
    /**
     * 最大分值（仅范围分有效）
     */
    private BigDecimal maxScore;
    /**
     * 排序序号
     */
    private Integer sortOrder;

}
