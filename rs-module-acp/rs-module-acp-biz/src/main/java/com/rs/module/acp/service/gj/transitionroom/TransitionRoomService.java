package com.rs.module.acp.service.gj.transitionroom;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.gj.vo.transitionroom.TransitionRoomRespVO;
import com.rs.module.acp.controller.admin.gj.vo.transitionroom.TransitionRoomSaveReqVO;
import com.rs.module.acp.entity.gj.TransitionRoomDO;
import com.rs.module.acp.enums.gj.TransitionRoomStatusEnum;

import javax.validation.Valid;

/**
 * 实战平台-管教业务-过渡监室管理 Service 接口
 *
 * <AUTHOR>
 */
public interface TransitionRoomService extends IBaseService<TransitionRoomDO>{

    /**
     * 创建实战平台-管教业务-过渡监室管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createTransitionRoom(@Valid TransitionRoomSaveReqVO createReqVO);

    /**
     * 获得实战平台-管教业务-过渡监室管理
     *
     * @param id 编号
     * @return 实战平台-管教业务-过渡监室管理
     */
    TransitionRoomRespVO getTransitionRoom(String id);

    /**
     * 更新人员状态信息
     * <AUTHOR>
     * @date 2025/6/18 11:36
     * @param [jgrybm, roomStatusEnum]
     * @return void
     */
    void updateStatus(String jgrybm, TransitionRoomStatusEnum roomStatusEnum);

}
