package com.rs.module.acp.controller.admin.gj.vo.face2face;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-面对面管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FaceToFacePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("禁闭监室id")
    private String roomId;

    @ApiModelProperty("数据来源（字典：ZD_DATA_SOURCES）")
    private String dataSources;

    @ApiModelProperty("检查民警身份证号")
    private String checkPoliceSfzh;

    @ApiModelProperty("检查民警身份证号")
    private String checkPolice;

    @ApiModelProperty("检查时间")
    private Date[] checkTime;

    @ApiModelProperty("情况记录")
    private String situationRecord;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
