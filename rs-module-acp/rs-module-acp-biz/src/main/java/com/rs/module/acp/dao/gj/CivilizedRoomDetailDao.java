package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.CivilizedRoomDetailListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.CivilizedRoomDetailPageReqVO;
import com.rs.module.acp.entity.gj.CivilizedRoomDetailDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-文明监室登记明细 Dao
*
* <AUTHOR>
*/
@Mapper
public interface CivilizedRoomDetailDao extends IBaseDao<CivilizedRoomDetailDO> {


    default PageResult<CivilizedRoomDetailDO> selectPage(CivilizedRoomDetailPageReqVO reqVO) {
        Page<CivilizedRoomDetailDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<CivilizedRoomDetailDO> wrapper = new LambdaQueryWrapperX<CivilizedRoomDetailDO>()
            .eqIfPresent(CivilizedRoomDetailDO::getCivilizedRoomId, reqVO.getCivilizedRoomId())
            .eqIfPresent(CivilizedRoomDetailDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(CivilizedRoomDetailDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(CivilizedRoomDetailDO::getNumberOfViolations, reqVO.getNumberOfViolations())
            .eqIfPresent(CivilizedRoomDetailDO::getSelectionReason, reqVO.getSelectionReason())
            .eqIfPresent(CivilizedRoomDetailDO::getAttUrl, reqVO.getAttUrl())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(CivilizedRoomDetailDO::getAddTime);
        }
        Page<CivilizedRoomDetailDO> civilizedRoomDetailPage = selectPage(page, wrapper);
        return new PageResult<>(civilizedRoomDetailPage.getRecords(), civilizedRoomDetailPage.getTotal());
    }
    default List<CivilizedRoomDetailDO> selectList(CivilizedRoomDetailListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CivilizedRoomDetailDO>()
            .eqIfPresent(CivilizedRoomDetailDO::getCivilizedRoomId, reqVO.getCivilizedRoomId())
            .eqIfPresent(CivilizedRoomDetailDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(CivilizedRoomDetailDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(CivilizedRoomDetailDO::getNumberOfViolations, reqVO.getNumberOfViolations())
            .eqIfPresent(CivilizedRoomDetailDO::getSelectionReason, reqVO.getSelectionReason())
            .eqIfPresent(CivilizedRoomDetailDO::getAttUrl, reqVO.getAttUrl())
        .orderByDesc(CivilizedRoomDetailDO::getAddTime));    }


    }
