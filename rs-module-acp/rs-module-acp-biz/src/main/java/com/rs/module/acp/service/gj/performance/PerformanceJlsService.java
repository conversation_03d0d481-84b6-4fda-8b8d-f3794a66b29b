package com.rs.module.acp.service.gj.performance;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.performancejls.PerformanceJlsListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.performancejls.PerformanceJlsPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.performancejls.PerformanceJlsSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.performancejls.PerformanceJssApprovalReqVO;
import com.rs.module.acp.entity.gj.PerformanceJlsDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-人员表现鉴定表-拘留所 Service 接口
 *
 * <AUTHOR>
 */
public interface PerformanceJlsService extends IBaseService<PerformanceJlsDO>{

    /**
     * 创建实战平台-管教业务-人员表现鉴定表-拘留所
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPerformanceJls(@Valid PerformanceJlsSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-人员表现鉴定表-拘留所
     *
     * @param updateReqVO 更新信息
     */
    void updatePerformanceJls(@Valid PerformanceJlsSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-人员表现鉴定表-拘留所
     *
     * @param id 编号
     */
    void deletePerformanceJls(String id);

    /**
     * 获得实战平台-管教业务-人员表现鉴定表-拘留所
     *
     * @param id 编号
     * @return 实战平台-管教业务-人员表现鉴定表-拘留所
     */
    PerformanceJlsDO getPerformanceJls(String id);

    /**
    * 获得实战平台-管教业务-人员表现鉴定表-拘留所分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-人员表现鉴定表-拘留所分页
    */
    PageResult<PerformanceJlsDO> getPerformanceJlsPage(PerformanceJlsPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-人员表现鉴定表-拘留所列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-人员表现鉴定表-拘留所列表
    */
    List<PerformanceJlsDO> getPerformanceJlsList(PerformanceJlsListReqVO listReqVO);


    void approval(PerformanceJssApprovalReqVO approvalReqVO);
}
