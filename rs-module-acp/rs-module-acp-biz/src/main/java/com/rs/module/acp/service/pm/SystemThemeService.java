package com.rs.module.acp.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.pm.vo.SystemThemeListReqVO;
import com.rs.module.acp.controller.admin.pm.vo.SystemThemePageReqVO;
import com.rs.module.acp.controller.admin.pm.vo.SystemThemeSaveReqVO;
import com.rs.module.acp.entity.pm.SystemThemeDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-主题-主题配置 Service 接口
 *
 * <AUTHOR>
 */
public interface SystemThemeService extends IBaseService<SystemThemeDO>{

    /**
     * 创建实战平台-主题-主题配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createSystemTheme(@Valid SystemThemeSaveReqVO createReqVO);

    /**
     * 更新实战平台-主题-主题配置
     *
     * @param updateReqVO 更新信息
     */
    void updateSystemTheme(@Valid SystemThemeSaveReqVO updateReqVO);

    /**
     * 删除实战平台-主题-主题配置
     *
     * @param id 编号
     */
    void deleteSystemTheme(String id);

    /**
     * 获得实战平台-主题-主题配置
     *
     * @param id 编号
     * @return 实战平台-主题-主题配置
     */
    SystemThemeDO getSystemTheme(String id);

    /**
    * 获得实战平台-主题-主题配置分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-主题-主题配置分页
    */
    PageResult<SystemThemeDO> getSystemThemePage(SystemThemePageReqVO pageReqVO);

    /**
    * 获得实战平台-主题-主题配置列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-主题-主题配置列表
    */
    List<SystemThemeDO> getSystemThemeList(SystemThemeListReqVO listReqVO);


}
