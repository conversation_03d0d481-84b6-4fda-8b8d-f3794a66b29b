package com.rs.module.acp.controller.app.gj.vo.inoutrecords;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 出入登记
 */
@Data
@ApiModel("出入登记-统计")
@NoArgsConstructor
@AllArgsConstructor
public class InOutStatisticDetailVO implements Serializable {

    private static final long serialVersionUID = 1082667630633084597L;

    @ApiModelProperty("业务类型（字典：ZD_GJXZDCSY）")
    private String businessType;

    @ApiModelProperty("记录数")
    private Integer nums;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;


}

