package com.rs.module.acp.controller.admin.ds.vo.sjbs;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-数据固化-每周会见律师排名新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class WeeklyLawyerTopSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("每周数据报送ID")
    private String weeklyDataSubmitId;
    @ApiModelProperty("固化日期")
    private String solidificationDate;
    @ApiModelProperty("固化开始日期")
    private String startDate;
    @ApiModelProperty("固化结束日期")
    private String endDate;
    @ApiModelProperty("律师ID")
    @NotEmpty(message = "律师ID不能为空")
    private String lsId;

    @ApiModelProperty("律师姓名")
    @NotEmpty(message = "律师姓名不能为空")
    private String xm;

    @ApiModelProperty("律师执业证号码")
    private String zyzhm;

    @ApiModelProperty("每周数据报送ID")
    @NotNull(message = "每周数据报送ID不能为空")
    private Integer hjcs;

    @ApiModelProperty("排名")
    @NotNull(message = "排名不能为空")
    private Integer pm;

}
