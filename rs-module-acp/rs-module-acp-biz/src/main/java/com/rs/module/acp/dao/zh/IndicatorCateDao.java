package com.rs.module.acp.dao.zh;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCatePageReqVO;
import com.rs.module.acp.entity.zh.IndicatorCateDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 综合管理-绩效考核指标分类 Dao
*
* <AUTHOR>
*/
@Mapper
public interface IndicatorCateDao extends IBaseDao<IndicatorCateDO> {


    default PageResult<IndicatorCateDO> selectPage(IndicatorCatePageReqVO reqVO) {
        Page<IndicatorCateDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<IndicatorCateDO> wrapper = new LambdaQueryWrapperX<IndicatorCateDO>()
            .likeIfPresent(IndicatorCateDO::getTypeName, reqVO.getTypeName())
            .eqIfPresent(IndicatorCateDO::getInitScore, reqVO.getInitScore())
            .eqIfPresent(IndicatorCateDO::getAssessorSfzh, reqVO.getAssessorSfzh())
            .likeIfPresent(IndicatorCateDO::getAssessorName, reqVO.getAssessorName())
            .eqIfPresent(IndicatorCateDO::getAssessedObjectType, reqVO.getAssessedObjectType())
            .eqIfPresent(IndicatorCateDO::getAssessedObjectId, reqVO.getAssessedObjectId())
            .likeIfPresent(IndicatorCateDO::getAssessedObjectName, reqVO.getAssessedObjectName())
            .eqIfPresent(IndicatorCateDO::getSortOrder, reqVO.getSortOrder())
            .eqIfPresent(IndicatorCateDO::getRemark, reqVO.getRemark())
            .eqIfPresent(IndicatorCateDO::getOrgCode, reqVO.getOrgCode())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByAsc(IndicatorCateDO::getSortOrder)
                    .orderByAsc(IndicatorCateDO::getAddTime);
        }
        Page<IndicatorCateDO> indicatorCatePage = selectPage(page, wrapper);
        return new PageResult<>(indicatorCatePage.getRecords(), indicatorCatePage.getTotal());
    }
    default List<IndicatorCateDO> selectList(IndicatorCateListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<IndicatorCateDO>()
            .likeIfPresent(IndicatorCateDO::getTypeName, reqVO.getTypeName())
            .eqIfPresent(IndicatorCateDO::getInitScore, reqVO.getInitScore())
            .eqIfPresent(IndicatorCateDO::getAssessorSfzh, reqVO.getAssessorSfzh())
            .likeIfPresent(IndicatorCateDO::getAssessorName, reqVO.getAssessorName())
            .eqIfPresent(IndicatorCateDO::getAssessedObjectType, reqVO.getAssessedObjectType())
            .eqIfPresent(IndicatorCateDO::getAssessedObjectId, reqVO.getAssessedObjectId())
            .likeIfPresent(IndicatorCateDO::getAssessedObjectName, reqVO.getAssessedObjectName())
            .eqIfPresent(IndicatorCateDO::getSortOrder, reqVO.getSortOrder())
            .eqIfPresent(IndicatorCateDO::getRemark, reqVO.getRemark())
            .eqIfPresent(IndicatorCateDO::getOrgCode, reqVO.getOrgCode())
        .orderByAsc(IndicatorCateDO::getSortOrder)
        .orderByAsc(IndicatorCateDO::getAddTime));    }


    }
