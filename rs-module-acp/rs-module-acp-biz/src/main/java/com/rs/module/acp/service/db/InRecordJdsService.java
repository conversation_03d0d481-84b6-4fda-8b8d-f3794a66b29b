package com.rs.module.acp.service.db;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.InRecordJdsDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.entity.db.InRecordJlsDO;

/**
 * 实战平台-收押业务-入所登记（戒毒所） Service 接口
 *
 * <AUTHOR>
 */
public interface InRecordJdsService extends IBaseService<InRecordJdsDO>{

    /**
     * 创建实战平台-收押业务-入所登记（戒毒所）
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createInRecordJds(@Valid InRecordJdsSaveReqVO createReqVO);

    /**
     * 更新实战平台-收押业务-入所登记（戒毒所）
     *
     * @param updateReqVO 更新信息
     */
    void updateInRecordJds(@Valid InRecordJdsSaveReqVO updateReqVO);

    /**
     * 删除实战平台-收押业务-入所登记（戒毒所）
     *
     * @param id 编号
     */
    void deleteInRecordJds(String id);

    /**
     * 获得实战平台-收押业务-入所登记（戒毒所）
     *
     * @param id 编号
     * @return 实战平台-收押业务-入所登记（戒毒所）
     */
    InRecordJdsDO getInRecordJds(String id);

    /**
    * 获得实战平台-收押业务-入所登记（戒毒所）分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-收押业务-入所登记（戒毒所）分页
    */
    PageResult<InRecordJdsDO> getInRecordJdsPage(InRecordJdsPageReqVO pageReqVO);

    /**
    * 获得实战平台-收押业务-入所登记（戒毒所）列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-收押业务-入所登记（戒毒所）列表
    */
    List<InRecordJdsDO> getInRecordJdsList(InRecordJdsListReqVO listReqVO);


    CombineRespVO getCombineInfo(String rybh);

    void updateLeaderApprovalStatus(LeaderApprovalStatusReqVO updateReqVO);

    InRecordJdsDO getPrisonerInfo(String rybh);

    String ifExists(InRecordJdsSaveReqVO updateReqVO);

    InRecordStatusVO getInRecordStatus(String rybh);
}
