package com.rs.module.acp.controller.admin.db.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-收押业务-随身物品登记列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PersonalEffectsListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("人员编号")
    private String rybh;

    @ApiModelProperty("人员姓名")
    private String ryxm;

    @ApiModelProperty("登记状态")
    private String status;

}
