package com.rs.module.acp.controller.admin.gj.vo.equipment;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 提前解除申请
 * <AUTHOR>
 * @date 2025/6/5 15:25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EquipmentUseRemoveApplyRegInfoVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("解除理由")
    @NotBlank(message = "解除理由不能为空！")
    private String removeReason;

    @ApiModelProperty("解除执行情况")
    private String removeExecuteSituation;

}
