package com.rs.module.acp.service.gj.diagnosiassmtjds;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds.*;
import com.rs.module.acp.entity.gj.MonthlyAssmtJdsDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.service.gj.diagnosiassmtjds.bo.AssmtCommonBO;

/**
 * 实战平台-管教业务-月度考核(戒毒所) Service 接口
 *
 * <AUTHOR>
 */
public interface MonthlyAssmtJdsService extends IBaseService<MonthlyAssmtJdsDO>{

    /**
     * 创建实战平台-管教业务-月度考核(戒毒所)
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMonthlyAssmtJds(@Valid MonthlyAssmtJdsSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-月度考核(戒毒所)
     *
     * @param updateReqVO 更新信息
     */
    void updateMonthlyAssmtJds(@Valid MonthlyAssmtJdsSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-月度考核(戒毒所)
     *
     * @param id 编号
     */
    void deleteMonthlyAssmtJds(String id);

    /**
     * 获得实战平台-管教业务-月度考核(戒毒所)
     *
     * @param id 编号
     * @return 实战平台-管教业务-月度考核(戒毒所)
     */
    MonthlyAssmtJdsDO getMonthlyAssmtJds(String id);

    /**
    * 获得实战平台-管教业务-月度考核(戒毒所)分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-月度考核(戒毒所)分页
    */
    PageResult<MonthlyAssmtJdsDO> getMonthlyAssmtJdsPage(MonthlyAssmtJdsPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-月度考核(戒毒所)列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-月度考核(戒毒所)列表
    */
    List<MonthlyAssmtJdsDO> getMonthlyAssmtJdsList(MonthlyAssmtJdsListReqVO listReqVO);


    List<MonthlyAssmtJdsDO> getMonthlyAssmtJdsListByInJds();

    List<AssmtCommonBO> getModel();

    void commit(MonthlyAssmtJdsCommitReqVO createReqVO);

    List<MonthlyAssmtJdsDO> getRecordByJgrybm(String jgrybm);

    Map<String, String> getAvgRecordByJgrybm(String jgrybm);
}
