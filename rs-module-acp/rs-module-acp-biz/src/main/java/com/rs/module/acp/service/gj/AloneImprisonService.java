package com.rs.module.acp.service.gj;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.gj.vo.aloneimprison.AloneImprisonApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.aloneimprison.AloneImprisonListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.aloneimprison.AloneImprisonPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.aloneimprison.AloneImprisonSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsACPSaveVO;
import com.rs.module.acp.entity.gj.AloneImprisonDO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 实战平台-管教业务-单独关押登记 Service 接口
 *
 * <AUTHOR>
 */
public interface AloneImprisonService extends IBaseService<AloneImprisonDO>{

    /**
     * 创建实战平台-管教业务-单独关押登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createAloneImprison(@Valid AloneImprisonSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-单独关押登记
     *
     * @param updateReqVO 更新信息
     */
    void updateAloneImprison(@Valid AloneImprisonSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-单独关押登记
     *
     * @param id 编号
     */
    void deleteAloneImprison(String id);

    /**
     * 获得实战平台-管教业务-单独关押登记
     *
     * @param id 编号
     * @return 实战平台-管教业务-单独关押登记
     */
    AloneImprisonDO getAloneImprison(String id);

    /**
    * 获得实战平台-管教业务-单独关押登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-单独关押登记分页
    */
    PageResult<AloneImprisonDO> getAloneImprisonPage(AloneImprisonPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-单独关押登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-单独关押登记列表
    */
    List<AloneImprisonDO> getAloneImprisonList(AloneImprisonListReqVO listReqVO);


    List<AreaPrisonRoomDO> getAloneImprisonRoomList(String nowRoomId);
    String save(@Valid AloneImprisonSaveReqVO createReqVO) throws Exception;
    boolean approve(AloneImprisonApproveReqVO approveReqVO) throws Exception;

    boolean saveInOutRecords(InOutRecordsACPSaveVO saveReqVO);

    //查询当前时间在单独关押期间的人员
    List<AloneImprisonDO> getAloneImprisonListNow();

    //查询当前人员是否已经单独关押
    boolean isExist(String jgrybm);

    void roomChangeAloneImprison(String jgrybm);

    void finishAloneImprison(String jgrybm,Date endTime);
}
