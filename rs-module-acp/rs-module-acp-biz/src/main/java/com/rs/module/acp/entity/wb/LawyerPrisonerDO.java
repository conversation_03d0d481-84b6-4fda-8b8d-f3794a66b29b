package com.rs.module.acp.entity.wb;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-窗口业务-律师关联被监管人员 DO
 *
 * <AUTHOR>
 */
@TableName("acp_wb_lawyer_prisoner")
@KeySequence("acp_wb_lawyer_prisoner_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LawyerPrisonerDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 律师表ID
     */
    private String lawyerId;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 委托类型
     */
    private String entrustType;
    /**
     * 委托阶段
     */
    private String entrustStage;
    /**
     * 委托人
     */
    private String principal;
    /**
     * 委托人证件号
     */
    private String principalId;
    /**
     * 委托书类型
     */
    private String powerOfAttorneyType;
    /**
     * 委托书存储路径
     */
    private String powerOfAttorneyUrl;
    /**
     * 介绍信编号
     */
    private String letterNumber;
    /**
     * 会见批准机关
     */
    private String meetingApprovalAuthority;
    /**
     * 委托状态
     */
    private String status;

}
