package com.rs.module.acp.service.gj.violationappeal;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.violationappeal.*;
import com.rs.module.acp.dao.gj.ViolationAppealDao;
import com.rs.module.acp.entity.gj.ViolationAppealDO;
import com.rs.module.acp.enums.gj.ViolationAppealStatusEnum;
import com.rs.module.acp.util.CommonUtils;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.util.ThreadPoolUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 实战平台-管教业务-违规申诉 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ViolationAppealServiceImpl extends BaseServiceImpl<ViolationAppealDao, ViolationAppealDO> implements ViolationAppealService {

    @Resource
    private ViolationAppealDao violationAppealDao;

    // 单位领导审批流程
    private static final String defKey = "yonghudanweilindao";

    @Override
    public String createViolationAppeal(ViolationAppealSaveReqVO createReqVO) {
        // 插入
        ViolationAppealDO violationAppeal = BeanUtils.toBean(createReqVO, ViolationAppealDO.class);
        violationAppealDao.insert(violationAppeal);
        // 返回
        return violationAppeal.getId();
    }

    @Override
    public void updateViolationAppeal(ViolationAppealSaveReqVO updateReqVO) {
        // 校验存在
        validateViolationAppealExists(updateReqVO.getId());
        // 更新
        ViolationAppealDO updateObj = BeanUtils.toBean(updateReqVO, ViolationAppealDO.class);
        violationAppealDao.updateById(updateObj);
    }

    @Override
    public void deleteViolationAppeal(String id) {
        // 校验存在
        validateViolationAppealExists(id);
        // 删除
        violationAppealDao.deleteById(id);
    }

    private void validateViolationAppealExists(String id) {
        if (violationAppealDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-违规申诉数据不存在");
        }
    }

    @Override
    public ViolationAppealDO getViolationAppeal(String id) {
        return violationAppealDao.getOneById(id);
    }

    @Override
    public PageResult<ViolationAppealDO> getViolationAppealPage(ViolationAppealPageReqVO pageReqVO) {
        return violationAppealDao.selectPage(pageReqVO);
    }

    @Override
    public List<ViolationAppealDO> getViolationAppealList(ViolationAppealListReqVO listReqVO) {
        return violationAppealDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String appeal(ViolationAppealAppCreateReqVO createReqVO) {
        LambdaQueryWrapper<ViolationAppealDO> wrapper = Wrappers.lambdaQuery(ViolationAppealDO.class).eq(ViolationAppealDO::getViolationRecordId, createReqVO.getViolationRecordId());
        Integer count = violationAppealDao.selectCount(wrapper);
        if (Objects.nonNull(count) && count > 0) {
            throw new RuntimeException("同一违规不能重复申诉");
        }
        // 插入
        ViolationAppealDO violationAppeal = BeanUtils.toBean(createReqVO, ViolationAppealDO.class);
        violationAppeal.setStatus(ViolationAppealStatusEnum.DSH.getCode());
        violationAppealDao.insert(violationAppeal);

        //启动流程审批
        String msgUrl = "/#/discipline/violationAppeals";
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", violationAppeal.getId());
        JSONObject result = BspApprovalUtil.defaultStartProcess(HttpUtils.getAppCode() + "-" + defKey, violationAppeal.getId(), "违规申诉流程审批", msgUrl, variables);
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            violationAppeal.setActInstId(bpmTrail.getString("actInstId"));
            violationAppeal.setTaskId(bpmTrail.getString("taskId"));
            violationAppealDao.updateById(violationAppeal);
        } else {
            throw new ServerException("流程启动失败");
        }
        return violationAppeal.getId();
    }

    @Override
    public List<ViolationAppealDO> getListByIds(List<String> idList) {
        return violationAppealDao.getListByIds(idList);
    }

    @Override
    public void approval(ViolationAppealApprovalReqVO appealApprovalReqVO) {
        List<String> idList = appealApprovalReqVO.getIdList();
        if (CollectionUtil.isEmpty(idList)) {
            throw new ServerException("批量审批id不能为空");
        }
        List<ViolationAppealDO> list = violationAppealDao.getListByIds(idList);
        if (CollectionUtil.isEmpty(list)) {
            throw new ServerException("实战平台-管教业务-违规申诉批量审批数据不存在");
        }

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(list.get(0).getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }
        String nowDefKey = HttpUtils.getAppCode() + "-" + defKey;
        List<ViolationAppealDO> approvalFailList = new ArrayList<>();
        List<ViolationAppealDO> approvalSuccessList = new ArrayList<>();
       // 多线程会导致 BspApprovalUtil.approvalProcess 里获取session失败  故此不用
        for (ViolationAppealDO violationAppealDO : list) {
            if (!ViolationAppealStatusEnum.DSH.getCode().equals(violationAppealDO.getStatus())) {
                approvalFailList.add(violationAppealDO);
            } else {
                Map<String, Object> variables = new HashMap<>();
                variables.put("ywbh", violationAppealDO.getId());
                //审批
                JSONObject nowApproveUser = new JSONObject();
                nowApproveUser.put("orgCode", sessionUser.getOrgCode());
                nowApproveUser.put("orgName", sessionUser.getOrgName());
                nowApproveUser.put("idCard", sessionUser.getId());
                nowApproveUser.put("name", sessionUser.getName());
                BspApproceStatusEnum bspApproceStatusEnum = ViolationAppealStatusEnum.SHTG.getCode().equals(appealApprovalReqVO.getStatus()) ?
                        BspApproceStatusEnum.PASSED_END : BspApproceStatusEnum.NOT_PASSED_END;
                JSONObject result = BspApprovalUtil.approvalProcess(nowDefKey,
                        violationAppealDO.getActInstId(), violationAppealDO.getTaskId(), violationAppealDO.getId(),
                        bspApproceStatusEnum, appealApprovalReqVO.getApprovalComments(), null, null, true,
                        variables, nowApproveUser, HttpUtils.getAppCode());
                log.info("=======result:{}", result);
                if (result.getIntValue("code") == HttpStatus.OK.value()) {
                    JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
                    violationAppealDO.setTaskId(bpmTrail.getString("taskId"));
                    violationAppealDO.setStatus(ViolationAppealStatusEnum.getByCode(appealApprovalReqVO.getStatus()).getCode());
                    violationAppealDO.setApproverTime(new Date());
                    violationAppealDO.setApprovalComments(appealApprovalReqVO.getApprovalComments());
                    violationAppealDO.setApprovalResult(appealApprovalReqVO.getStatus());
                    violationAppealDO.setApproverSfzh(sessionUser.getIdCard());
                    violationAppealDO.setApproverXm(sessionUser.getName());
                    approvalSuccessList.add(violationAppealDO);
                } else {
                    approvalFailList.add(violationAppealDO);
                }
            }
        }

        if (CollectionUtil.isNotEmpty(approvalSuccessList)) {
            violationAppealDao.updateBatch(approvalSuccessList);
        }
        if (CollectionUtil.isNotEmpty(approvalFailList)) {
            List<String> collect = approvalFailList.stream().map(ViolationAppealDO::getJgryxm).collect(Collectors.toList());
            throw new ServerException("【" + CollectionUtil.join(collect, ",") + "】的违规申诉失败，请联系管理员或进行单独审批");
        }

    }

    @Override
    public PageResult<ViolationAppealDO> applyList(int pageNo, int pageSize, String jgrybm, String type) {
        Page<ViolationAppealDO> page = new Page<>(pageNo, pageSize);
        Page<ViolationAppealDO> result = violationAppealDao.applyList(page, jgrybm, type);
        return new PageResult<>(result.getRecords(), result.getTotal());
    }

    @Override
    public PageResult<ViolationAppealDO> applyRecord(int pageNo, int pageSize, String jgrybm, String type) {
        Page<ViolationAppealDO> page = new Page<>(pageNo, pageSize);
        Map<String, Date> commonAppRecordPeriod = CommonUtils.getCommonAppRecordPeriod(type);
        Page<ViolationAppealDO> result = violationAppealDao.applyRecord(page, jgrybm,
                commonAppRecordPeriod.get("startTime"), commonAppRecordPeriod.get("endTime"));
        return new PageResult<>(result.getRecords(), result.getTotal());
    }


}
