package com.rs.module.acp.util;

import com.bsp.common.cache.DicUtil;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.util.DateUtil;

import java.util.Date;

public class SugDesusUtil {
    /**
     * 收拘时流程defkey
     */
    public static final String FLOW_DEF_KEY_WHEN_DETAINED = "shoujushijianyitingzhizhixingjuliu";
    /**
     * 收拘时流程defkey
     */
    public static final String FLOW_DEF_KEY_AFTER_DETAINED = "shoujuhoujianyitingzhizhixingjuliu";

    public static final String SJS = "01";
    public static final String SJH = "02";
    /**
     * 根据jtlx建停类型（01：收拘时，02：收拘后）返回流程defkey
     * @param jtlx
     * @return
     */
    public static String getFlowDefKeyByJtlx(String jtlx) {
        String defKey = FLOW_DEF_KEY_AFTER_DETAINED;
        if ("01".equals(jtlx)) {
            defKey = FLOW_DEF_KEY_WHEN_DETAINED;
        }
        return HttpUtils.getAppCode()+"-" + defKey;
    }
    public static String buildWszh(String orgCode, Date approverTime,String wsh){
        StringBuffer stringBuffer = new StringBuffer();
        String year = DateUtil.format(approverTime, "yyyy");
        String orgWsjc = DicUtil.translate("ZD-JGJC", orgCode);
        return String.format("%s停字[%s]%s号", orgWsjc, year, wsh);
    }
}
