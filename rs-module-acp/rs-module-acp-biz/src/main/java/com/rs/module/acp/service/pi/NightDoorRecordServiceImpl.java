package com.rs.module.acp.service.pi;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.pi.vo.NightDoorRecordSaveReqVO;
import com.rs.module.acp.dao.pi.NightDoorRecordDao;
import com.rs.module.acp.entity.pi.NightDoorRecordDO;
import com.rs.module.base.util.BspApprovalUtil;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


/**
 * 实战平台-巡视管控-夜间开启监室门 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class NightDoorRecordServiceImpl extends BaseServiceImpl<NightDoorRecordDao, NightDoorRecordDO> implements NightDoorRecordService {

    @Resource
    private NightDoorRecordDao nightDoorRecordDao;
    // 开门申请流程key
    private final String defKey = "yejiankaimenliucheng";
    // 信息补录流程key
    private final String supplementDefKey = "yejiankaimenxinxibulu";


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createNightDoorRecord(NightDoorRecordSaveReqVO createReqVO) {
        // 插入
        NightDoorRecordDO nightDoorRecord = BeanUtils.toBean(createReqVO, NightDoorRecordDO.class);
        nightDoorRecord.setStatus("01");
        nightDoorRecordDao.insert(nightDoorRecord);
        // 发起审批
        JSONObject result = "01".equals(nightDoorRecord.getApplyType()) ? initiateApproval(nightDoorRecord) :
                initiateSupplementApproval(nightDoorRecord);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            nightDoorRecord.setActInstId(bpmTrail.getString("actInstId"));
            nightDoorRecord.setTaskId(bpmTrail.getString("taskId"));
            nightDoorRecord.setStatus("01".equals(nightDoorRecord.getStatus()) ? "01" : "04");
            nightDoorRecordDao.updateById(nightDoorRecord);
        } else {
            throw new ServerException("流程启动失败");
        }

        // 返回
        return nightDoorRecord.getId();
    }

    private JSONObject initiateApproval(NightDoorRecordDO recordDO) {
        String msgTit = String.format("【审批】%s%s夜间开启%s的门禁，请及时处理！", recordDO.getOrgName(),
                recordDO.getApplyUser(), recordDO.getRoomName());
        String msgUrl = "/discipline/openJailRoomAtNight/update?id=" + recordDO.getId();
        return BspApprovalUtil.commonStartProcess(defKey, recordDO.getId(), msgTit, msgUrl, null, HttpUtils.getAppCode());
    }

    private JSONObject initiateSupplementApproval(NightDoorRecordDO recordDO) {
        String msgTit = String.format("【审批】%s%s对夜间开启信息进行补录，请及时处理！", recordDO.getOrgName(),
                recordDO.getApplyUser());
        String msgUrl = "/discipline/openJailRoomAtNight/update?id=" + recordDO.getId();
        return BspApprovalUtil.commonStartProcess(supplementDefKey, recordDO.getId(), msgTit, msgUrl, null, HttpUtils.getAppCode());
    }

    @Override
    public String updateNightDoorRecord(NightDoorRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateNightDoorRecordExists(updateReqVO.getId());
        // 更新
        NightDoorRecordDO updateObj = BeanUtils.toBean(updateReqVO, NightDoorRecordDO.class);
        nightDoorRecordDao.updateById(updateObj);
        // 返回
        return updateObj.getId();
    }

    @Override
    public void deleteNightDoorRecord(String id) {
        // 校验存在
        validateNightDoorRecordExists(id);
        // 删除
        nightDoorRecordDao.deleteById(id);
    }

    private void validateNightDoorRecordExists(String id) {
        if (nightDoorRecordDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-夜间开启监室门数据不存在");
        }
    }

    @Override
    public NightDoorRecordDO getNightDoorRecord(String id) {
        return nightDoorRecordDao.selectById(id);
    }

}
