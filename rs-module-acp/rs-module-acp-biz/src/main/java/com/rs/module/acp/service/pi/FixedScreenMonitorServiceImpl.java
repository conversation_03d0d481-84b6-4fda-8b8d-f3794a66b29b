package com.rs.module.acp.service.pi;

import com.rs.module.acp.controller.admin.pi.vo.fixedscreenmonitor.FixedScreenMonitorSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.rs.module.acp.entity.pi.FixedScreenMonitorDO;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.FixedScreenMonitorDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-巡视管控-定屏监控 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FixedScreenMonitorServiceImpl extends BaseServiceImpl<FixedScreenMonitorDao, FixedScreenMonitorDO> implements FixedScreenMonitorService {

    @Resource
    private FixedScreenMonitorDao fixedScreenMonitorDao;

    @Override
    public String createFixedScreenMonitor(FixedScreenMonitorSaveReqVO createReqVO) {
        // 插入
        FixedScreenMonitorDO fixedScreenMonitor = BeanUtils.toBean(createReqVO, FixedScreenMonitorDO.class);
        fixedScreenMonitorDao.insert(fixedScreenMonitor);
        // 返回
        return fixedScreenMonitor.getId();
    }

    @Override
    public void updateFixedScreenMonitor(FixedScreenMonitorSaveReqVO updateReqVO) {
        // 校验存在
        validateFixedScreenMonitorExists(updateReqVO.getId());
        // 更新
        FixedScreenMonitorDO updateObj = BeanUtils.toBean(updateReqVO, FixedScreenMonitorDO.class);
        fixedScreenMonitorDao.updateById(updateObj);
    }

    @Override
    public void deleteFixedScreenMonitor(String id) {
        // 校验存在
        validateFixedScreenMonitorExists(id);
        // 删除
        fixedScreenMonitorDao.deleteById(id);
    }

    private void validateFixedScreenMonitorExists(String id) {
        if (fixedScreenMonitorDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-定屏监控数据不存在");
        }
    }

    @Override
    public FixedScreenMonitorDO getFixedScreenMonitor(String id) {
        return fixedScreenMonitorDao.selectById(id);
    }


}
