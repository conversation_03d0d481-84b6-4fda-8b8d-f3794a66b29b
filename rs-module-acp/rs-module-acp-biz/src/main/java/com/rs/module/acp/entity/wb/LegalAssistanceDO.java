package com.rs.module.acp.entity.wb;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-窗口业务-法律帮助申请 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_legal_assistance")
@KeySequence("acp_gj_legal_assistance_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_legal_assistance")
public class LegalAssistanceDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 律师姓名
     */
    private String lawyerName;
    /**
     * 律师证件号码
     */
    private String lawyerIdNumber;
    /**
     * 律师执业证号码
     */
    private String lawyerPracticeLicenseNumber;
    /**
     * 律师所属单位
     */
    private String lawyerFirm;
    /**
     * 申请时间
     */
    private Date applyTime;
    /**
     * 申请帮助事项
     */
    private String assistanceMatter;
    /**
     * 是否需要办案机关许可
     */
    private Short isNeedCaseUnitAllowed;
    /**
     * 办案机关类型
     */
    private String caseUnitType;
    /**
     * 办案机关名称
     */
    private String caseUnitName;
    /**
     * 办案机关是否许可
     */
    private Short isCaseUnitAllowed;
    /**
     * 办案机关反馈
     */
    private String caseUnitFeedback;
    /**
     * 决定时间
     */
    private Date decisionTime;
    /**
     * 会见方式
     */
    private String meetingMethod;
    /**
     * 会见室ID
     */
    private String meetingRoomId;
    /**
     * 会见室名称
     */
    private String meetingRoomName;
    /**
     * 是否特殊会见
     */
    private Short isSpecialMeeting;
    /**
     * 带出监室时间
     */
    private Date escortingTime;
    /**
     * 带出检查结果
     */
    private String inspectionResult;
    /**
     * 会见开始时间
     */
    private Date meetingStartTime;
    /**
     * 会见结束时间
     */
    private Date meetingEndTime;
    /**
     * 带出民警身份证号
     */
    private String escortingPoliceSfzh;
    /**
     * 带出民警身份证号
     */
    private String escortingPolice;
    /**
     * 带回民警身份证号
     */
    private String returnPoliceSfzh;
    /**
     * 带回民警姓名
     */
    private String returnPolice;
    /**
     * 带回监室时间
     */
    private Date returnTime;
    /**
     * 会毕检查结果
     */
    private String returnInspectionResult;
    /**
     * 异常情况登记
     */
    private String abnormalSituations;
    /**
     * 监督民警身份证号
     */
    private String supervisingPoliceSfzh;
    /**
     * 监督民警姓名
     */
    private String supervisingPoliceName;
    /**
     * 律师是否有违规
     */
    private Short isLawyerViolation;
    /**
     * 律师是否告知在押人员异常行为
     */
    private Short isLawyerInformAbnormalBehav;
    /**
     * 律师违规情况
     */
    private String lawyerViolationDetails;
    /**
     * 在押人员异常行为情况
     */
    private String abnormalBehaviorDetails;

}
