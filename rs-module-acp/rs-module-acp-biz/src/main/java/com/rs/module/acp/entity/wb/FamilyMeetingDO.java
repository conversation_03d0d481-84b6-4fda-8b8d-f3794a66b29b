package com.rs.module.acp.entity.wb;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-窗口业务-家属会见登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_wb_family_meeting")
@KeySequence("acp_wb_family_meeting_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_wb_family_meeting")
public class FamilyMeetingDO extends BaseDO {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 第一位家属ID
     */
    private String familyMember1Id;
    /**
     * 第一位家属姓名
     */
    private String familyMember1Name;
    /**
     * 第一位家属性别
     */
    private String familyMember1Gender;
    /**
     * 第一位家属证件类型
     */
    private String familyMember1IdType;
    /**
     * 第一位家属证件号码
     */
    private String familyMember1IdNumber;
    /**
     * 第一位家属与被会见人社会关系
     */
    private String familyMember1Relationship;
    /**
     * 第一位家属联系方式
     */
    private String familyMember1Contact;
    /**
     * 第一位家属工作单位
     */
    private String familyMember1WorkUnit;
    /**
     * 第一位家属居住地址
     */
    private String familyMember1Address;
    /**
     * 第一位家属ID
     */
    private String familyMember2Id;
    /**
     * 第二位家属姓名
     */
    private String familyMember2Name;
    /**
     * 第二位家属性别
     */
    private String familyMember2Gender;
    /**
     * 第二位家属证件类型
     */
    private String familyMember2IdType;
    /**
     * 第二位家属的证件号码
     */
    private String familyMember2IdNumber;
    /**
     * 第二位家属与被会见人社会关系
     */
    private String familyMember2Relationship;
    /**
     * 第二位家属联系方式
     */
    private String familyMember2Contact;
    /**
     * 第二位家属工作单位
     */
    private String familyMember2WorkUnit;
    /**
     * 第二位家属居住地址
     */
    private String familyMember2Address;
    /**
     * 办案机关批准会见文书号
     */
    private String approvalDocumentNumber;
    /**
     * 会见批准材料附件上传后存储路径
     */
    private String approvalAttachmentPath;
    /**
     * 关于此次会见的备注信息
     */
    private String remarks;
    /**
     * 办理状态
     */
    private String status;
    /**
     * 会见所在房间编号
     */
    private String roomId;
    /**
     * 分配房间时间
     */
    private Date assignmentRoomTime;
    /**
     * 分配民警身份证号
     */
    private String assignmentPoliceSfzh;
    /**
     * assignment_police
     */
    private String assignmentPolice;
    /**
     * 签到时间
     */
    private Date checkInTime;
    /**
     * 签到用户身份证号
     */
    private String checkInPoliceSfzh;
    /**
     * 签到用户
     */
    private String checkInPolice;
    /**
     * 带出民警身份证号
     */
    private String escortingPoliceSfzh;
    /**
     * 带出民警身份证号
     */
    private String escortingPolice;
    /**
     * 带出监室时间
     */
    private Date escortingTime;
    /**
     * 带出操作人身份证号
     */
    private String escortingOperatorSfzh;
    /**
     * 带出操作人
     */
    private String escortingOperator;
    /**
     * 带出操作时间
     */
    private Date escortingOperatorTime;
    /**
     * 检查结果
     */
    private String inspectionResult;
    /**
     * 检查时间
     */
    private Date inspectionTime;
    /**
     * inspection_situation
     */
    private String inspectionSituation;
    /**
     * 检查民警身份证号
     */
    private String inspectorSfzh;
    /**
     * 检查民警身份证号
     */
    private String inspector;
    /**
     * 会见开始时间
     */
    private Date meetingStartTime;
    /**
     * 会见结束时间
     */
    private Date meetingEndTime;
    /**
     * 会毕检查人
     */
    private String returnInspectorSfzh;
    /**
     * 会毕检查人
     */
    private String returnInspector;
    /**
     * 会毕检查时间
     */
    private Date returnInspectionTime;
    /**
     * 会毕检查结果
     */
    private String returnInspectionResult;
    /**
     * 带回监室时间
     */
    private Date returnTime;
    /**
     * 带回民警身份证号
     */
    private String returnPoliceSfzh;
    /**
     * 带回民警姓名
     */
    private String returnPolice;
    /**
     * 带回操作人身份证号
     */
    private String returnOperatorSfzh;
    /**
     * 带回操作人
     */
    private String returnOperator;
    /**
     * 带回操作时间
     */
    private Date returnOperatorTime;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 预约会见开始时间
     */
    private Date applyMeetingStartTime;
    /**
     * 预约会见结束时间
     */
    private Date applyMeetingEndTime;
    /**
     * 带回违禁物品登记
     */
    private String returnProhibitedItems;
    /**
     * 带回体表检查登记
     */
    private String returnPhysicalExam;
    /**
     * 带回异常情况登记
     */
    private String returnAbnormalSituations;
    /**
     * 违禁物品登记
     */
    private String prohibitedItems;
    /**
     * 体表检查登记
     */
    private String physicalExam;
    /**
     * 异常情况登记
     */
    private String abnormalSituations;
    /**
     * 违禁物品登记照片存储地址
     */
    private String prohibitedItemsImgUrl;
    /**
     * 体表检查登记照片存储地址
     */
    private String physicalExamImgUrl;
    /**
     * 异常情况登记照片存储地址
     */
    private String abnormalSituationsImgUrl;
    /**
     * 第三位家属ID
     */
    private String familyMember3Id;
    /**
     * 第三位家属姓名
     */
    private String familyMember3Name;
    /**
     * 第三位家属性别
     */
    private String familyMember3Gender;
    /**
     * 第三位家属证件类型
     */
    private String familyMember3IdType;
    /**
     * 第三位家属的证件号码
     */
    private String familyMember3IdNumber;
    /**
     * 第三位家属与被会见人社会关系
     */
    private String familyMember3Relationship;
    /**
     * 第三位家属联系方式
     */
    private String familyMember3Contact;
    /**
     * 第三位家属工作单位
     */
    private String familyMember3WorkUnit;
    /**
     * 第三位家属居住地址
     */
    private String familyMember3Address;
    /**
     * 第一位家属职业
     */
    private String familyMember1Occupation;
    /**
     * 第二位家属职业
     */
    private String familyMember2Occupation;
    /**
     * 第三位家属职业
     */
    private String familyMember3Occupation;
    /**
     * 第一位家属照片
     */
    private String familyMember1ImageUrl;
    /**
     * 第二位家属照片
     */
    private String familyMember2ImageUrl;
    /**
     * 第三位家属照片
     */
    private String familyMember3ImageUrl;
    /**
     * 第一位家属关系证明附件
     */
    private String familyMember1RelationsAttch;
    /**
     * 第二位家属关系证明附件
     */
    private String familyMember2RelationsAttch;
    /**
     * 第三位家属关系证明附件
     */
    private String familyMember3RelationsAttch;
}
