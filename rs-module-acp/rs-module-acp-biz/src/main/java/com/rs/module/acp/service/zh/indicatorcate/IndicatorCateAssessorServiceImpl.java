package com.rs.module.acp.service.zh.indicatorcate;

import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateAssessorListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateAssessorPageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateAssessorSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.zh.IndicatorCateAssessorDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.zh.IndicatorCateAssessorDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 综合管理-绩效考核指标分类与考评人关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class IndicatorCateAssessorServiceImpl extends BaseServiceImpl<IndicatorCateAssessorDao, IndicatorCateAssessorDO> implements IndicatorCateAssessorService {

    @Resource
    private IndicatorCateAssessorDao indicatorCateAssessorDao;

    @Override
    public String createIndicatorCateAssessor(IndicatorCateAssessorSaveReqVO createReqVO) {
        // 插入
        IndicatorCateAssessorDO indicatorCateAssessor = BeanUtils.toBean(createReqVO, IndicatorCateAssessorDO.class);
        indicatorCateAssessorDao.insert(indicatorCateAssessor);
        // 返回
        return indicatorCateAssessor.getId();
    }

    @Override
    public void updateIndicatorCateAssessor(IndicatorCateAssessorSaveReqVO updateReqVO) {
        // 校验存在
        validateIndicatorCateAssessorExists(updateReqVO.getId());
        // 更新
        IndicatorCateAssessorDO updateObj = BeanUtils.toBean(updateReqVO, IndicatorCateAssessorDO.class);
        indicatorCateAssessorDao.updateById(updateObj);
    }

    @Override
    public void deleteIndicatorCateAssessor(String id) {
        // 校验存在
        validateIndicatorCateAssessorExists(id);
        // 删除
        indicatorCateAssessorDao.deleteById(id);
    }

    private void validateIndicatorCateAssessorExists(String id) {
        if (indicatorCateAssessorDao.selectById(id) == null) {
            throw new ServerException("综合管理-绩效考核指标分类与考评人关联数据不存在");
        }
    }

    @Override
    public IndicatorCateAssessorDO getIndicatorCateAssessor(String id) {
        return indicatorCateAssessorDao.selectById(id);
    }

    @Override
    public PageResult<IndicatorCateAssessorDO> getIndicatorCateAssessorPage(IndicatorCateAssessorPageReqVO pageReqVO) {
        return indicatorCateAssessorDao.selectPage(pageReqVO);
    }

    @Override
    public List<IndicatorCateAssessorDO> getIndicatorCateAssessorList(IndicatorCateAssessorListReqVO listReqVO) {
        return indicatorCateAssessorDao.selectList(listReqVO);
    }


}
