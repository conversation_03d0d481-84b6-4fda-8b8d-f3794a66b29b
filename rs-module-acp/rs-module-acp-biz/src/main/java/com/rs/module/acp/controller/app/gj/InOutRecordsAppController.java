package com.rs.module.acp.controller.app.gj;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsSaveReqVO;
import com.rs.module.acp.controller.app.gj.vo.inoutrecords.InOutRecordsBatchSaveReqVO;
import com.rs.module.acp.controller.app.gj.vo.inoutrecords.InOutStatisticVO;
import com.rs.module.acp.controller.app.gj.vo.inoutrecords.PrisonerInOutBusinessTypeRespVO;
import com.rs.module.acp.service.gj.InOutRecordsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import java.util.List;
import java.util.Map;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "仓内外屏-管教业务-出入登记")
@RestController
@RequestMapping("/app/acp/inOutRecords")
@Validated
public class InOutRecordsAppController {
    @Resource
    private InOutRecordsService inOutRecordsService;
    //查询指定监室内所有人员待带出记录及出入事由(拼接)
    @GetMapping("/getPaddingInOutRecords")
    @ApiOperation(value = "仓内外屏-管教业务-人员列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "roomId", value = "监室ID", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "inoutType", value = "进出监室类型: 01 出监,02 入监", required = true, dataType = "String", paramType = "query"),
    })
    public CommonResult<List<PrisonerInOutBusinessTypeRespVO>> getPaddingInOutRecords(@RequestParam String orgCode, @RequestParam String roomId,
             @RequestParam String inoutType) {
        return success(inOutRecordsService.getPaddingRoomInOutRecords(orgCode,roomId,inoutType));
    }

    @GetMapping("/statisticNum")
    @ApiOperation(value = "仓内外屏-管教业务-出入登记统计")
    public CommonResult<InOutStatisticVO> statisticNum(@RequestParam String orgCode,@RequestParam String roomId) {
        return success(inOutRecordsService.statisticNum(orgCode,roomId));
    }
    @PostMapping("/saveOutRecords")
    @ApiOperation(value = "仓内外屏-管教业务-带出登记")
    public CommonResult<String> saveOutRecords(@RequestBody InOutRecordsBatchSaveReqVO batchSaveReqVO) {
        try {
            return success(inOutRecordsService.saveOutRecordsAndInitInRecords(batchSaveReqVO));
        } catch (Exception e) {
            return CommonResult.error(e.getMessage());
        }
    }
    //带入登记
    @PostMapping("/saveInRecords")
    @ApiOperation(value = "仓内外屏-管教业务-带入登记")
    public CommonResult<String> saveInRecords(@RequestBody InOutRecordsBatchSaveReqVO batchSaveReqVO) {
        try {
            return success(inOutRecordsService.saveInRecords(batchSaveReqVO));
        } catch (Exception e) {
            return CommonResult.error(e.getMessage());
        }
    }
}
