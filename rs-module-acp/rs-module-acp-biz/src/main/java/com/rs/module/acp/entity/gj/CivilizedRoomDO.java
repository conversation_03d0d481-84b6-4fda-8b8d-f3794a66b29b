package com.rs.module.acp.entity.gj;

import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.CivilizedRoomDetailRespVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-文明监室登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_civilized_room")
@KeySequence("acp_gj_civilized_room_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_civilized_room")
public class CivilizedRoomDO extends BaseDO {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 评比月份
     */
    private String evalMonth;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 申请时间
     */
    private Date applyTime;
    /**
     * 申请人身份证号
     */
    private String applyUserSfzh;
    /**
     * 申请人
     */
    private String applyUser;
    /**
     * 状态
     */
    private String status;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

}
