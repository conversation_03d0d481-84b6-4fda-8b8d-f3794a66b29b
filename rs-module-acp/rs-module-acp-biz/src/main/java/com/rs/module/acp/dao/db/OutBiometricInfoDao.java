package com.rs.module.acp.dao.db;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.db.OutBiometricInfoDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-收押业务-出所生物特征信息 Dao
*
* <AUTHOR>
*/
@Mapper
public interface OutBiometricInfoDao extends IBaseDao<OutBiometricInfoDO> {


    default PageResult<OutBiometricInfoDO> selectPage(OutBiometricInfoPageReqVO reqVO) {
        Page<OutBiometricInfoDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<OutBiometricInfoDO> wrapper = new LambdaQueryWrapperX<OutBiometricInfoDO>()
            .eqIfPresent(OutBiometricInfoDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(OutBiometricInfoDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(OutBiometricInfoDO::getCjxmlx, reqVO.getCjxmlx())
            .eqIfPresent(OutBiometricInfoDO::getSwtz, reqVO.getSwtz())
            .eqIfPresent(OutBiometricInfoDO::getSwtzfj, reqVO.getSwtzfj())
            .eqIfPresent(OutBiometricInfoDO::getHyjg, reqVO.getHyjg())
            .eqIfPresent(OutBiometricInfoDO::getBz, reqVO.getBz())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(OutBiometricInfoDO::getAddTime);
        }
        Page<OutBiometricInfoDO> outBiometricInfoPage = selectPage(page, wrapper);
        return new PageResult<>(outBiometricInfoPage.getRecords(), outBiometricInfoPage.getTotal());
    }
    default List<OutBiometricInfoDO> selectList(OutBiometricInfoListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<OutBiometricInfoDO>()
            .eqIfPresent(OutBiometricInfoDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(OutBiometricInfoDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(OutBiometricInfoDO::getCjxmlx, reqVO.getCjxmlx())
            .eqIfPresent(OutBiometricInfoDO::getSwtz, reqVO.getSwtz())
            .eqIfPresent(OutBiometricInfoDO::getSwtzfj, reqVO.getSwtzfj())
            .eqIfPresent(OutBiometricInfoDO::getHyjg, reqVO.getHyjg())
            .eqIfPresent(OutBiometricInfoDO::getBz, reqVO.getBz())
        .orderByDesc(OutBiometricInfoDO::getAddTime));    }


    List<OutBiometricInfoDO> getByJgrybm(String jgrybm);
}
