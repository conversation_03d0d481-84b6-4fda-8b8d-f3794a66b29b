package com.rs.module.acp.service.gj.civilizedroom;

import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.CivilizedRoomDetailListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.CivilizedRoomDetailPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.CivilizedRoomDetailSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.CivilizedRoomDetailDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.CivilizedRoomDetailDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-文明监室登记明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CivilizedRoomDetailServiceImpl extends BaseServiceImpl<CivilizedRoomDetailDao, CivilizedRoomDetailDO> implements CivilizedRoomDetailService {

    @Resource
    private CivilizedRoomDetailDao civilizedRoomDetailDao;

    @Override
    public String createCivilizedRoomDetail(CivilizedRoomDetailSaveReqVO createReqVO) {
        // 插入
        CivilizedRoomDetailDO civilizedRoomDetail = BeanUtils.toBean(createReqVO, CivilizedRoomDetailDO.class);
        civilizedRoomDetailDao.insert(civilizedRoomDetail);
        // 返回
        return civilizedRoomDetail.getId();
    }

    @Override
    public void updateCivilizedRoomDetail(CivilizedRoomDetailSaveReqVO updateReqVO) {
        // 校验存在
        validateCivilizedRoomDetailExists(updateReqVO.getId());
        // 更新
        CivilizedRoomDetailDO updateObj = BeanUtils.toBean(updateReqVO, CivilizedRoomDetailDO.class);
        civilizedRoomDetailDao.updateById(updateObj);
    }

    @Override
    public void deleteCivilizedRoomDetail(String id) {
        // 校验存在
        validateCivilizedRoomDetailExists(id);
        // 删除
        civilizedRoomDetailDao.deleteById(id);
    }

    private void validateCivilizedRoomDetailExists(String id) {
        if (civilizedRoomDetailDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-文明监室登记明细数据不存在");
        }
    }

    @Override
    public CivilizedRoomDetailDO getCivilizedRoomDetail(String id) {
        return civilizedRoomDetailDao.selectById(id);
    }

    @Override
    public PageResult<CivilizedRoomDetailDO> getCivilizedRoomDetailPage(CivilizedRoomDetailPageReqVO pageReqVO) {
        return civilizedRoomDetailDao.selectPage(pageReqVO);
    }

    @Override
    public List<CivilizedRoomDetailDO> getCivilizedRoomDetailList(CivilizedRoomDetailListReqVO listReqVO) {
        return civilizedRoomDetailDao.selectList(listReqVO);
    }


}
