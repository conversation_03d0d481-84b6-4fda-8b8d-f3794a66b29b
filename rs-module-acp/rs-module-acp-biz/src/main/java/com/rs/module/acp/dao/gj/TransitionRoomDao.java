package com.rs.module.acp.dao.gj;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.transitionroom.TransitionRoomListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.transitionroom.TransitionRoomPageReqVO;
import com.rs.module.acp.entity.gj.TransitionRoomDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;

/**
* 实战平台-管教业务-过渡监室管理 Dao
*
* <AUTHOR>
*/
@Mapper
public interface TransitionRoomDao extends IBaseDao<TransitionRoomDO> {


    default PageResult<TransitionRoomDO> selectPage(TransitionRoomPageReqVO reqVO) {
        Page<TransitionRoomDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<TransitionRoomDO> wrapper = new LambdaQueryWrapperX<TransitionRoomDO>()
            .eqIfPresent(TransitionRoomDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(TransitionRoomDO::getJgryxm, reqVO.getJgryxm())
            .betweenIfPresent(TransitionRoomDO::getStartDate, reqVO.getStartDate())
            .betweenIfPresent(TransitionRoomDO::getEndDate, reqVO.getEndDate())
            .betweenIfPresent(TransitionRoomDO::getActualStartDate, reqVO.getActualStartDate())
            .betweenIfPresent(TransitionRoomDO::getActualEndDate, reqVO.getActualEndDate())
            .eqIfPresent(TransitionRoomDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(TransitionRoomDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(TransitionRoomDO::getIsExtend, reqVO.getIsExtend())
            .eqIfPresent(TransitionRoomDO::getStatus, reqVO.getStatus())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(TransitionRoomDO::getAddTime);
        }
        Page<TransitionRoomDO> transitionRoomPage = selectPage(page, wrapper);
        return new PageResult<>(transitionRoomPage.getRecords(), transitionRoomPage.getTotal());
    }
    default List<TransitionRoomDO> selectList(TransitionRoomListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TransitionRoomDO>()
            .eqIfPresent(TransitionRoomDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(TransitionRoomDO::getJgryxm, reqVO.getJgryxm())
            .betweenIfPresent(TransitionRoomDO::getStartDate, reqVO.getStartDate())
            .betweenIfPresent(TransitionRoomDO::getEndDate, reqVO.getEndDate())
            .betweenIfPresent(TransitionRoomDO::getActualStartDate, reqVO.getActualStartDate())
            .betweenIfPresent(TransitionRoomDO::getActualEndDate, reqVO.getActualEndDate())
            .eqIfPresent(TransitionRoomDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(TransitionRoomDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(TransitionRoomDO::getIsExtend, reqVO.getIsExtend())
            .eqIfPresent(TransitionRoomDO::getStatus, reqVO.getStatus())
        .orderByDesc(TransitionRoomDO::getAddTime));    }


    /**
     * 过渡超期状态更新
     * <AUTHOR>
     * @date 2025/6/19 16:54
     * @param [date]
     * @return int
     */
    int transitionPeriodUpdate(Date date);
}
