package com.rs.module.acp.service.gj.rightsobligationsnotice;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.rightsobligationsnotice.RightsObligationsNoticeListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.rightsobligationsnotice.RightsObligationsNoticePageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.rightsobligationsnotice.RightsObligationsNoticeSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.rightsobligationsnotice.RightsObligationsNoticeViewInfoRespVO;
import com.rs.module.acp.entity.gj.RightsObligationsNoticeDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-权力义务告知书 Service 接口
 *
 * <AUTHOR>
 */
public interface RightsObligationsNoticeService extends IBaseService<RightsObligationsNoticeDO>{

    /**
     * 创建实战平台-管教业务-权力义务告知书
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createRightsObligationsNotice(@Valid RightsObligationsNoticeSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-权力义务告知书
     *
     * @param updateReqVO 更新信息
     */
    void updateRightsObligationsNotice(@Valid RightsObligationsNoticeSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-权力义务告知书
     *
     * @param id 编号
     */
    void deleteRightsObligationsNotice(String id);

    /**
     * 获得实战平台-管教业务-权力义务告知书
     *
     * @param id 编号
     * @return 实战平台-管教业务-权力义务告知书
     */
    RightsObligationsNoticeDO getRightsObligationsNotice(String id);

    /**
    * 获得实战平台-管教业务-权力义务告知书分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-权力义务告知书分页
    */
    PageResult<RightsObligationsNoticeDO> getRightsObligationsNoticePage(RightsObligationsNoticePageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-权力义务告知书列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-权力义务告知书列表
    */
    List<RightsObligationsNoticeDO> getRightsObligationsNoticeList(RightsObligationsNoticeListReqVO listReqVO);


    String sign(RightsObligationsNoticeSaveReqVO createReqVO);

    RightsObligationsNoticeDO getByJgrybm(String jgrybm);

    RightsObligationsNoticeViewInfoRespVO getParams(String jgrybm);

}
