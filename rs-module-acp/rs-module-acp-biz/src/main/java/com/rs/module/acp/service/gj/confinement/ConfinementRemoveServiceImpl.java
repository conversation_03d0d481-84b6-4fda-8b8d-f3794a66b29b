package com.rs.module.acp.service.gj.confinement;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.cons.CommonConstants;
import com.rs.module.acp.controller.admin.gj.vo.confinement.*;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsRespVO;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsSaveReqVO;
import com.rs.module.acp.dao.gj.ConfinementRemoveDao;
import com.rs.module.acp.entity.gj.InOutRecordsDO;
import com.rs.module.acp.entity.gj.confinement.ConfinementExtendDO;
import com.rs.module.acp.entity.gj.confinement.ConfinementRegDO;
import com.rs.module.acp.entity.gj.confinement.ConfinementRemoveDO;
import com.rs.module.acp.service.gj.InOutRecordsService;
import com.rs.module.acp.util.ConfinementUtil;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.util.DicUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 实战平台-管教业务-解除禁闭呈批 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ConfinementRemoveServiceImpl extends BaseServiceImpl<ConfinementRemoveDao, ConfinementRemoveDO> implements ConfinementRemoveService {

    @Resource
    private ConfinementRemoveDao confinementRemoveDao;
    @Resource
    private ConfinementExtendService confinementExtendService;
    @Resource
    private ConfinementRegService confinementRegService;
    @Resource
    private InOutRecordsService inOutRecordsService;

    @Resource
    private PrisonerService prisonerService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createConfinementRemove(ConfinementRemoveSaveReqVO createReqVO) throws Exception{
        createReqVO.setId(StringUtil.getGuid());
        // 插入
        ConfinementRemoveDO confinementRemove = BeanUtils.toBean(createReqVO, ConfinementRemoveDO.class);
        //启动流程
        Map<String,String> processResult = BspApprovalUtil.commonStartProcessMap(ConfinementUtil.FLOW_DEF_KEY_CONFINEMENT_REMOVE,
                confinementRemove.getId(), "", "", null, HttpUtils.getAppCode());
        if(StringUtil.isEmpty(processResult.get("actInstId")) || StringUtil.isEmpty(processResult.get("taskId"))){
            throw new ServerException("启动流程失败");
        }
        confinementRemove.setActInstId(processResult.get("actInstId"));
        confinementRemove.setTaskId(processResult.get("taskId"));
        confinementRemove.setStatus(ConfinementUtil.ApprovalStatusEnum.REMOVE_PENDING.getCode());
        confinementRemoveDao.insert(confinementRemove);
        confinementRegService.updateStatus(confinementRemove.getConfinementId(), confinementRemove.getStatus());
        // 返回
        return confinementRemove.getId();
    }

    @Override
    public void updateConfinementRemove(ConfinementRemoveSaveReqVO updateReqVO) {
        // 校验存在
        validateConfinementRemoveExists(updateReqVO.getId());
        // 更新
        ConfinementRemoveDO updateObj = BeanUtils.toBean(updateReqVO, ConfinementRemoveDO.class);
        confinementRemoveDao.updateById(updateObj);
    }

    @Override
    public void deleteConfinementRemove(String id) {
        // 校验存在
        validateConfinementRemoveExists(id);
        // 删除
        confinementRemoveDao.deleteById(id);
    }

    private void validateConfinementRemoveExists(String id) {
        if (confinementRemoveDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-解除禁闭呈批数据不存在");
        }
    }

    @Override
    public ConfinementRemoveDO getConfinementRemove(String id) {
        return confinementRemoveDao.selectById(id);
    }

    @Override
    public PageResult<ConfinementRemoveDO> getConfinementRemovePage(ConfinementRemovePageReqVO pageReqVO) {
        return confinementRemoveDao.selectPage(pageReqVO);
    }

    @Override
    public List<ConfinementRemoveDO> getConfinementRemoveList(ConfinementRemoveListReqVO listReqVO) {
        return confinementRemoveDao.selectList(listReqVO);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean approve(ConfinementRemoveApproveVO approveReqVO) throws Exception {
        //从当前登录用户信息设置审批信息默认值
        if(StringUtil.isEmpty(approveReqVO.getApproverXm())) approveReqVO.setApproverXm(SessionUserUtil.getSessionUser().getName());
        if(StringUtil.isEmpty(approveReqVO.getApproverSfzh())) approveReqVO.setApproverSfzh(SessionUserUtil.getSessionUser().getIdCard());
        if(approveReqVO.getApproverTime() == null) approveReqVO.setApproverTime(new Date());
        ConfinementRemoveDO confinementRemoveDO = confinementRemoveDao.selectById(approveReqVO.getId());
        BspApproceStatusEnum isApprove = BspApproceStatusEnum.NOT_PASSED;
        String status = ConfinementUtil.ApprovalStatusEnum.APPROVAL_REJECTED.getCode();
        if(approveReqVO.getApprovalResult().equals("1")){//審批同意 ZD_DDGY_SPZT
            status = ConfinementUtil.ApprovalStatusEnum.RELEASED.getCode();
            isApprove = BspApproceStatusEnum.PASSED;
        }
        approveReqVO.setActInstId(confinementRemoveDO.getActInstId());
        approveReqVO.setTaskId(confinementRemoveDO.getTaskId());
        approveReqVO.setDefKey(ConfinementUtil.FLOW_DEF_KEY_CONFINEMENT_REMOVE);
        BeanUtil.copyProperties( approveReqVO,confinementRemoveDO);
        confinementRemoveDO.setStatus(status);
        //调用流程审批接口
        String msgTit = "";
        String msgUrl = "";
        Map<String,String> approvalResult = BspApprovalUtil.approvalProcessMap(approveReqVO,
                isApprove,
                msgTit,
                msgUrl,
                true,
                null,
                null,
                HttpUtils.getAppCode()
        );
        if(StringUtil.isEmpty(approvalResult.get("actInstId")) || StringUtil.isEmpty(approvalResult.get("taskId"))){
            throw new ServerException("审批流程失败");
        }
        ConfinementRegDO confinementRegDO = confinementRegService.getById(confinementRemoveDO.getConfinementId());
        long remainDay = DateUtil.betweenDay(confinementRegDO.getActualEndTime(), new Date(), true);
        if(approveReqVO.getApprovalResult().equals("1")){
            //更新实际禁闭结束时间
            confinementRegDO.setActualEndTime(approveReqVO.getApproverTime());
            confinementRegDO.setStatus(ConfinementUtil.ApprovalStatusEnum.RELEASED.getCode());
        }else{
            if(remainDay > 0){
                confinementRegDO.setStatus(ConfinementUtil.ApprovalStatusEnum.CONFINING.getCode());
            }else{
                confinementRegDO.setStatus(ConfinementUtil.ApprovalStatusEnum.RELEASE_PENDING.getCode());
            }
        }
        confinementRegService.updateById(confinementRegDO);
        confinementRemoveDao.updateById(confinementRemoveDO);
        String taskId = approvalResult.get("taskId");
        if(StringUtil.isNotEmpty(taskId)) {
            return true;
        }
        return false;
    }
    @Override
    public List<ConfinementFlowApproveTrackVO> getApproveTrack(String id){
        List<ConfinementFlowApproveTrackVO> list = new ArrayList<>();
        ConfinementRegDO confinementReg = confinementRegService.getConfinementReg(id);
        ConfinementRemoveDO confinementRemoveDO = getConfinementRemoveByConfinementId(id);
        //查询延长进行信息
        List<ConfinementExtendDO> confinementExtendDOList = confinementExtendService.getConfinementExtendByConfinementId(id);
        //查询历史轨迹 进行转换
        JSONObject regBspApprovalTrackVO = BspApprovalUtil.getBpmApi().approveTrack(confinementReg.getActInstId());
        //禁闭呈批
        list.add(ConfinementUtil.buildTrackNodeInfoFromReg(BeanUtils.toBean(confinementReg, ConfinementRegRespVO.class)));
        //审批轨迹构建
        list.addAll(ConfinementUtil.converBspApprovalTrack(regBspApprovalTrackVO,ConfinementUtil.ConfinementEnum.APPROVE));
        //呈批登记信息
        List<InOutRecordsDO> cpinOutRecordsDOList = inOutRecordsService.getListInOutRecordsByJgrybm(confinementReg.getJgrybm(),confinementReg.getId(),null);
        list.add(ConfinementUtil.buildTrackNodeInfoFromInOutRecords(confinementReg.getConfinementStartDate(),confinementReg.getAddTime(),BeanUtils.toBean(cpinOutRecordsDOList, InOutRecordsRespVO.class)));
        for (ConfinementExtendDO confinementExtendDO : confinementExtendDOList) {
            //延长禁闭呈批
            list.add(ConfinementUtil.buildTrackNodeInfoFromExtend(BeanUtils.toBean(confinementExtendDO, ConfinementExtendRespVO.class)));
            JSONObject regBspApprovalExtendTrackVO = BspApprovalUtil.getBpmApi().approveTrack(confinementExtendDO.getActInstId());
            list.addAll(ConfinementUtil.converBspApprovalTrack(regBspApprovalExtendTrackVO, ConfinementUtil.ConfinementEnum.EXTENDAPPROVE));
        }
        //解除信息
        list.add(ConfinementUtil.buildTrackNodeInfoFromRemove(BeanUtils.toBean(confinementRemoveDO, ConfinementRemoveRespVO.class)));
        JSONObject regBspApprovalExtendTrackVO = BspApprovalUtil.getBpmApi().approveTrack(confinementRemoveDO.getActInstId());
        list.addAll(ConfinementUtil.converBspApprovalTrack(regBspApprovalExtendTrackVO, ConfinementUtil.ConfinementEnum.REMOVEAPPROVE));
        //查询禁闭带入带出信息
        //List<InOutRecordsDO> inOutRecordsDOList = inOutRecordsService.getListInOutRecordsByJgrybm(confinementReg.getJgrybm(),confinementRemoveDO.getId(),null);
        list.add(ConfinementUtil.buildTrackNodeInfoFromRemove(BeanUtils.toBean(confinementRemoveDO, ConfinementRemoveRespVO.class),confinementRemoveDO.getAddTime()));
        return list;
    }

    @Override
    public ConfinementRemoveDO getConfinementRemoveByConfinementId(String id) {
        return confinementRemoveDao.selectOne(new LambdaQueryWrapper<ConfinementRemoveDO>().eq(ConfinementRemoveDO::getConfinementId, id)
                .eq(ConfinementRemoveDO::getIsDel, 0));
    }

    @Override
    public ConfinementDetailRespVO getDetail(String id) {
        ConfinementDetailRespVO detailRespVO = new ConfinementDetailRespVO();
        detailRespVO.setRegRespVO(BeanUtils.toBean(confinementRegService.getConfinementReg(id), ConfinementRegRespVO.class));
        detailRespVO.setExtendRespVOList(BeanUtils.toBean(confinementExtendService.getConfinementExtendByConfinementId(id), ConfinementExtendRespVO.class));
        detailRespVO.setRemoveRespVO(BeanUtils.toBean(getConfinementRemoveByConfinementId(id), ConfinementRemoveRespVO.class));
        return detailRespVO;
    }
    @Override
    public ConfinementDetailRespVO getDetailTrack(String id) {
        ConfinementDetailRespVO detailRespVO = new ConfinementDetailRespVO();
        ConfinementRegDO confinementReg = confinementRegService.getConfinementReg(id);
        ConfinementRegRespVO regRespVO = BeanUtils.toBean(confinementReg, ConfinementRegRespVO.class);
        regRespVO.setRoomName(DicUtils.translate("ZD_JSQY", regRespVO.getRoomId()));
        regRespVO.setOriginalRoomName(DicUtils.translate("ZD_JSQY", regRespVO.getOriginalRoomId()));
        regRespVO.setPunishmentMeasuresNames(DicUtils.translate( "ZD_GJCFNR", regRespVO.getPunishmentMeasures()));
        long remainDay = 0;
        if(null != confinementReg.getActualEndTime())
         remainDay = DateUtil.betweenDay(confinementReg.getActualEndTime(), new Date(), true);
        regRespVO.setResidualDay(remainDay);
        //查询延长进行信息
        List<ConfinementExtendDO> confinementExtendDOList = confinementExtendService.getConfinementExtendByConfinementId(id);
        ConfinementRemoveDO confinementRemoveDO = getConfinementRemoveByConfinementId(id);
        detailRespVO.setRegRespVO(regRespVO);
        detailRespVO.setExtendRespVOList(BeanUtils.toBean(confinementExtendDOList, ConfinementExtendRespVO.class));
        detailRespVO.setRemoveRespVO(BeanUtils.toBean(confinementRemoveDO, ConfinementRemoveRespVO.class));

        List<ConfinementFlowApproveTrackVO> list = new ArrayList<>();
        if(StringUtil.isEmpty(confinementReg.getActInstId())){
            return detailRespVO;
        }
        //查询历史轨迹 进行转换
        JSONObject regBspApprovalTrackVO = BspApprovalUtil.getBpmApi().approveTrack(confinementReg.getActInstId());
        //禁闭呈批
        list.add(ConfinementUtil.buildTrackNodeInfoFromReg(detailRespVO.getRegRespVO()));
        //审批轨迹构建
        list.addAll(ConfinementUtil.converBspApprovalTrack(regBspApprovalTrackVO,ConfinementUtil.ConfinementEnum.APPROVE));
        //呈批登记信息
        List<InOutRecordsDO> cpinOutRecordsDOList = inOutRecordsService.getListInOutRecordsByJgrybm(confinementReg.getJgrybm(),confinementReg.getId(),null);
        detailRespVO.setRegInOutRecordsRespVOList(BeanUtils.toBean(cpinOutRecordsDOList, InOutRecordsRespVO.class));
        list.add(ConfinementUtil.buildTrackNodeInfoFromInOutRecords(confinementReg.getConfinementStartDate(),confinementReg.getAddTime(),BeanUtils.toBean(cpinOutRecordsDOList, InOutRecordsRespVO.class)));
        for (ConfinementExtendDO confinementExtendDO : confinementExtendDOList) {
            if(StringUtil.isEmpty(confinementExtendDO.getActInstId())){
                continue;
            }
            //延长禁闭呈批
            list.add(ConfinementUtil.buildTrackNodeInfoFromExtend(BeanUtils.toBean(confinementExtendDO, ConfinementExtendRespVO.class)));
            JSONObject regBspApprovalExtendTrackVO = BspApprovalUtil.getBpmApi().approveTrack(confinementExtendDO.getActInstId());
            list.addAll(ConfinementUtil.converBspApprovalTrack(regBspApprovalExtendTrackVO, ConfinementUtil.ConfinementEnum.EXTENDAPPROVE));
        }
        if(confinementRemoveDO == null || StringUtil.isEmpty(confinementRemoveDO.getConfinementId())) {
            detailRespVO.setTrackList(list);
            return detailRespVO;
        }
        //解除信息
        list.add(ConfinementUtil.buildTrackNodeInfoFromRemove(BeanUtils.toBean(confinementRemoveDO, ConfinementRemoveRespVO.class)));
        if(StringUtil.isEmpty(confinementRemoveDO.getActInstId())){
            detailRespVO.setTrackList(list);
            return detailRespVO;
        }
        JSONObject regBspApprovalExtendTrackVO = BspApprovalUtil.getBpmApi().approveTrack(confinementRemoveDO.getActInstId());
        list.addAll(ConfinementUtil.converBspApprovalTrack(regBspApprovalExtendTrackVO, ConfinementUtil.ConfinementEnum.REMOVEAPPROVE));
        //查询禁闭带入带出信息
        List<InOutRecordsDO> inOutRecordsDOList = inOutRecordsService.getListInOutRecordsByJgrybm(confinementReg.getJgrybm(),confinementRemoveDO.getId(),"02");
        if(CollectionUtil.isNotNull(inOutRecordsDOList)){
            InOutRecordsDO inOutRecordsDO = inOutRecordsDOList.get(0);
            detailRespVO.setRemoveInOutRecordsRespVO(BeanUtils.toBean(inOutRecordsDO, InOutRecordsRespVO.class));
        }
        list.add(ConfinementUtil.buildTrackNodeInfoFromRemove(BeanUtils.toBean(confinementRemoveDO, ConfinementRemoveRespVO.class),confinementRemoveDO.getAddTime()));
        detailRespVO.setTrackList(list);
        return detailRespVO;
    }

    @Override
    public boolean saveInOutRecords(InOutRecordsConfinementRemoveSaveVO saveReqVO) {
        InOutRecordsSaveReqVO inOutRecordsSaveReqVO = BeanUtils.toBean(saveReqVO, InOutRecordsSaveReqVO.class);
        ConfinementRemoveDO confinementRemoveDO = getById(saveReqVO.getId());
        ConfinementRegDO confinementRegDO = confinementRegService.getConfinementReg(confinementRemoveDO.getConfinementId());
        inOutRecordsSaveReqVO.setId("");
        inOutRecordsSaveReqVO.setBusinessId(saveReqVO.getId());
        inOutRecordsSaveReqVO.setJgrybm(confinementRegDO.getJgrybm());
        PrisonerVwRespVO vwRespVO = prisonerService.getPrisonerByJgrybm(confinementRegDO.getJgrybm());
        inOutRecordsSaveReqVO.setJgryxm(vwRespVO.getXm());
        inOutRecordsSaveReqVO.setRoomId(vwRespVO.getJsh());
        inOutRecordsSaveReqVO.setBusinessType("11");
        inOutRecordsService.acpSaveInOutRecords(inOutRecordsSaveReqVO,confinementRegDO.getRoomId(),saveReqVO.getInTime());
        confinementRegDO.setStatus("07");//更新状态为已解除
        confinementRegDO.setExecuteSituation(saveReqVO.getExecuteSituation());
        confinementRegDO.setRemoveReason(saveReqVO.getRemoveReason());
        confinementRegService.updateById(confinementRegDO);
        return false;
    }

    @Override
    public ConfinementDetailRespVO initRemoveReason(String id) {
        ConfinementDetailRespVO detailRespVO = new ConfinementDetailRespVO();
        //detailRespVO.setRegRespVO(BeanUtils.toBean(confinementRegService.getConfinementReg(id), ConfinementRegRespVO.class));
        detailRespVO.setRemoveRespVO(BeanUtils.toBean(getConfinementRemoveByConfinementId(id), ConfinementRemoveRespVO.class));
        return detailRespVO;
    }
}
