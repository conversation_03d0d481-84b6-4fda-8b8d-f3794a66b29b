package com.rs.module.acp.entity.gj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 实战平台-管教业务-风险评估 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_risk_assmt")
@KeySequence("acp_gj_risk_assmt_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_risk_assmt")
public class GjRiskAssmtDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 评估类型 ZD_GJ_FXPG_TYPE
     */
    private String riskType;
    /**
     * 原风险等级
     */
    private String oldRiskLevel;
    /**
     * 评估风险等级  字典 ZD_GJ_FXPG_LEVEL
     */
    private String riskLevel;
    /**
     * 严管类别  字典 ZD_GJ_FXPG_STRICTLY
     */
    private String strictlyRegulatedCategory;
    /**
     * 评估理由
     */
    private String assmtReason;
    /**
     * 具体评估理由
     */
    private String specificAssmtReason;
    /**
     * 字典状态（字典：ZD_SP_STATUS ）
     */
    private String status;
    /**
     * 审批人身份证号
     */
    private String approverSfzh;
    /**
     * 审批人姓名
     */
    private String approverXm;
    /**
     * 审批时间
     */
    private Date approverTime;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 领导签名
     */
    private String approvalAutograph;
    /**
     * 领导签名日期
     */
    private Date approvalAutographTime;
    /**
     * 审核意见
     */
    private String approvalComments;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

}
