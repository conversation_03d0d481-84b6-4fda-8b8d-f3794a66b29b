package com.rs.module.acp.controller.admin.gj.vo.equipment;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-械具使用新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EquipmentUseSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("使用情形")
    @NotEmpty(message = "使用情形不能为空")
    private String useSituation;

    @ApiModelProperty("使用天数")
    @NotEmpty(message = "使用天数不能为空")
    private String useDays;

    @ApiModelProperty("械具种类")
    @NotEmpty(message = "械具种类不能为空")
    private String punishmentToolType;

    @ApiModelProperty("使用械具的理由")
    @NotEmpty(message = "使用械具的理由不能为空")
    private String useReason;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态")
    @NotEmpty(message = "状态不能为空")
    private String status;

    @ApiModelProperty("登记审批人身份证号")
    private String regApproverSfzh;

    @ApiModelProperty("登记审批人姓名")
    private String regApproverXm;

    @ApiModelProperty("登记审批时间")
    private Date regApproverTime;

    @ApiModelProperty("登记审批结果")
    private String regApprovalResult;

    @ApiModelProperty("登记领导签名")
    private String regApprovalAutograph;

    @ApiModelProperty("登记领导签名日期")
    private Date regApprovalAutographTime;

    @ApiModelProperty("登记审核意见")
    private String regApprovalComments;

    @ApiModelProperty("开始日期")
    private Date startTime;

    @ApiModelProperty("结束日期")
    private Date endTime;

    @ApiModelProperty("执行情况")
    private String executeSituation;

    @ApiModelProperty("使用登记时间")
    private Date useRegTime;

    @ApiModelProperty("使用登记人")
    private String useRegUser;

    @ApiModelProperty("延长理由")
    private String extendReason;

    @ApiModelProperty("延长天数")
    private Short extendDay;

    @ApiModelProperty("延长时间")
    private Date extendTime;

    @ApiModelProperty("延长登记人")
    private String extendRegUser;

    @ApiModelProperty("延长执行情况")
    private String extendExecuteSituation;

    @ApiModelProperty("延长审批人身份证号")
    private String extendApproverSfzh;

    @ApiModelProperty("延长审批人姓名")
    private String extendApproverXm;

    @ApiModelProperty("延长审批时间")
    private Date extendApproverTime;

    @ApiModelProperty("延长审批结果")
    private String extendApprovalResult;

    @ApiModelProperty("延长领导签名")
    private String extendApprovalAutograph;

    @ApiModelProperty("延长领导签名日期")
    private Date extendApprovalAutographTime;

    @ApiModelProperty("延长审核意见")
    private String extendApprovalComments;

    @ApiModelProperty("使用械具的理由")
    private String removeReason;

    @ApiModelProperty("解除日期")
    private Date removeTime;

    @ApiModelProperty("解除登记人")
    private String removeRegUser;

    @ApiModelProperty("解除执行情况")
    private String removeExecuteSituation;

    @ApiModelProperty("是否提前解除")
    private Short isRemove;

    @ApiModelProperty("解除审批人身份证号")
    private String removeApproverSfzh;

    @ApiModelProperty("解除审批人姓名")
    private String removeApproverXm;

    @ApiModelProperty("解除审批时间")
    private Date removeApproverTime;

    @ApiModelProperty("解除审批结果")
    private String removeApprovalResult;

    @ApiModelProperty("解除领导签名")
    private String removeApprovalAutograph;

    @ApiModelProperty("解除领导签名日期")
    private Date removeApprovalAutographTime;

    @ApiModelProperty("解除审核意见")
    private String removeApprovalComments;



}
