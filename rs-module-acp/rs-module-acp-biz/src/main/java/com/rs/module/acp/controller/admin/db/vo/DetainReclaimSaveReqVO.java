package com.rs.module.acp.controller.admin.db.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-羁押业务-收回登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DetainReclaimSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("入所时间")
//    @NotNull(message = "入所时间不能为空")
    private Date rssj;

    @ApiModelProperty("入所原因")
    @NotEmpty(message = "入所原因不能为空")
    private String rsyy;

    @ApiModelProperty("法律文书号")
    private String flwsh;

    @ApiModelProperty("收回原因")
    @NotEmpty(message = "收回原因不能为空")
    private String shyy;

    @ApiModelProperty("收回日期")
    @NotNull(message = "收回日期不能为空")
    private Date shrq;

    @ApiModelProperty("经办人身份证号")
//    @NotEmpty(message = "经办人身份证号不能为空")
    private String jbrsfzh;

    @ApiModelProperty("经办人")
    @NotEmpty(message = "经办人不能为空")
    private String jbr;

    @ApiModelProperty("经办时间")
    @NotEmpty(message = "经办时间不能为空")
    private String jbsj;

    @ApiModelProperty("登记状态")
    private String status;

    @ApiModelProperty("监室号")
    private String jsh;

    @ApiModelProperty("监室名称")
    private String roomName;

}
