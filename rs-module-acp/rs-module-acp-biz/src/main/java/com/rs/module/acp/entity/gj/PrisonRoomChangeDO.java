package com.rs.module.acp.entity.gj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import java.util.Date;

/**
 * 实战平台-管教业务--监室调整 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_prison_room_change")
@KeySequence("acp_gj_prison_room_change_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrisonRoomChangeDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 旧监室id
     */
    private String oldRoomId;
    /**
     * 新监室ID
     */
    private String newRoomId;
    /**
     * 旧监室名称
     */
    private String oldRoomName;
    /**
     * 新监室名称
     */
    private String newRoomName;
    /**
     * 调整原因
     */
    private String changeReason;
    /**
     * 监室调整时间
     */
    private Date roomChangeTime;
    /**
     * 是否调整（1：已调整，0：未调整）
     */
    private Integer isChange;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态
     */
    private String status;
    /**
     * 审批人身份证号
     */
    private String approverSfzh;
    /**
     * 审批人姓名
     */
    private String approverXm;
    /**
     * 审批时间
     */
    private Date approverTime;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 领导签名
     */
    private String approvalAutograph;
    /**
     * 领导签名日期
     */
    private Date approvalAutographTime;
    /**
     * 审核意见
     */
    private String approvalComments;
    /**
     * 带出民警身份证号
     */
    private String escortingPoliceSfzh;
    /**
     * 带出民警身份证号
     */
    private String escortingPolice;
    /**
     * 带出监室时间
     */
    private Date escortingTime;
    /**
     * 检查结果
     */
    private String inspectionResult;
    /**
     * 违禁物品登记
     */
    private String prohibitedItems;
    /**
     * 体表检查登记
     */
    private String physicalExam;
    /**
     * 异常情况登记
     */
    private String abnormalSituations;
    /**
     * 检查民警身份证号
     */
    private String inspectorSfzh;
    /**
     * 检查民警身份证号
     */
    private String inspector;
    /**
     * 带回监室时间
     */
    private Date returnTime;
    /**
     * 带回民警身份证号
     */
    private String returnPoliceSfzh;
    /**
     * 带回民警姓名
     */
    private String returnPolice;
    /**
     * 流程实例
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

    //来源业务ID
    private String sourceBusinessId;

    /**
     * 类型 0802:单独关押,0801:禁闭
     */
    private String businessType;

}
