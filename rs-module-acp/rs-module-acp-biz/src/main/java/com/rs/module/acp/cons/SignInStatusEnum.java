package com.rs.module.acp.cons;

/**
 * 签到状态枚举
 */
public enum SignInStatusEnum {

    NOT_SIGNED("01", "未签到"),
    SIGNED("02", "已签到"),
    RECOVERED("03", "已补签"),
    LATE("04", "超时签到");

    private final String code;
    private final String value;

    SignInStatusEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 状态码
     * @return 对应的枚举值
     */
    public static SignInStatusEnum getByCode(String code) {
        for (SignInStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据value获取枚举值
     *
     * @param value 状态名称
     * @return 对应的枚举值
     */
    public static SignInStatusEnum getByValue(String value) {
        for (SignInStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
}
