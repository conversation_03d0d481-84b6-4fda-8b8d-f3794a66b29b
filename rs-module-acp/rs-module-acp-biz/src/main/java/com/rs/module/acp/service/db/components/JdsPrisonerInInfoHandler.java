package com.rs.module.acp.service.db.components;

import com.rs.module.acp.controller.admin.wb.vo.DefaultCasePersonneSaveReqVO;
import com.rs.module.acp.controller.admin.wb.vo.SocialRelationsChildSaveReqVO;
import com.rs.module.acp.controller.admin.wb.vo.SocialRelationsListReqVO;
import com.rs.module.acp.controller.admin.wb.vo.SocialRelationsSaveReqVO;
import com.rs.module.acp.dao.db.InRecordJdsDao;
import com.rs.module.acp.dao.wb.SocialRelationsDao;
import com.rs.module.acp.entity.db.InRecordJdsDO;
import com.rs.module.acp.entity.wb.SocialRelationsDO;
import com.rs.module.acp.service.wb.CasePersonnelService;
import com.rs.module.acp.service.wb.SocialRelationsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
@Slf4j
@Service("jdsPrisonerInInfoHandler")
public class JdsPrisonerInInfoHandler implements PrisonerInInfoSyncHandler {

    @Resource
    private InRecordJdsDao inRecordJdsDao;

    @Resource
    private CasePersonnelService casePersonnelService;
    @Resource
    private SocialRelationsDao socialRelationsDao;
    @Resource
    private SocialRelationsService socialRelationsService;

    @Override
    public boolean syncPrisonerInInfo(String rybh) {
        // TODO: 调用戒毒所模块相关接口，推送入所信息
        System.out.println("正在向戒毒所模块推送入所人员信息，人员编号：" + rybh);
        return true; // 返回推送结果
    }

    @Override
    public void syncDataToTransitionRoom(String rybh, String orgCode,String id) {

    }

    @Override
    public void syncDataToCasePersonnel(String rybh, String orgCode, String id) {
//detainRegKssDao 查询该rybh数据
        try {
            InRecordJdsDO inRecordJdsDO = inRecordJdsDao.selectOne("jgrybm", rybh);

            List<DefaultCasePersonneSaveReqVO> createReqVO = new ArrayList<>();
            DefaultCasePersonneSaveReqVO defaultCasePersonneSaveReqVO = new DefaultCasePersonneSaveReqVO();
//        defaultCasePersonneSaveReqVO.setJh(inRecordJdsDO.getJh());
            defaultCasePersonneSaveReqVO.setXm(inRecordJdsDO.getBar());
            defaultCasePersonneSaveReqVO.setZjlx(inRecordJdsDO.getBar());
            defaultCasePersonneSaveReqVO.setZjhm(inRecordJdsDO.getBar());
            defaultCasePersonneSaveReqVO.setBadwdm(inRecordJdsDO.getBadw());
            defaultCasePersonneSaveReqVO.setBadwmc(inRecordJdsDO.getBadw());
            defaultCasePersonneSaveReqVO.setLxfs(inRecordJdsDO.getBarlxdh());
            defaultCasePersonneSaveReqVO.setXb(inRecordJdsDO.getBar());
//        defaultCasePersonneSaveReqVO.setZpUrl(inRecordJdsDO.getZpUrl());
//        defaultCasePersonneSaveReqVO.setGzzjUrl(inRecordJdsDO.getGzzjUrl());
//        defaultCasePersonneSaveReqVO.setTjjglx(inRecordJdsDO.getTjjglx());
            defaultCasePersonneSaveReqVO.setJgrybm(inRecordJdsDO.getJgrybm());
            createReqVO.add(defaultCasePersonneSaveReqVO);
            log.info("同步到实战平台-窗口业务-办案人员信息为:{}", defaultCasePersonneSaveReqVO.toJsonString());


            casePersonnelService.saveDefaultCasePersonnelList(createReqVO);
        } catch (Exception e) {
            log.info("同步到实战平台-窗口业务-办案人员信息为：{} 异常，{}", rybh,e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void syncToPtmGoods(String rybh, String orgCode, String id) {

    }

    @Override
    public void syncSocialRelations(String rybh, String orgCode, String id) {
        try {
            SocialRelationsListReqVO reqVO = new SocialRelationsListReqVO();
            reqVO.setJgrybm(rybh);
            List<SocialRelationsDO> list = socialRelationsDao.selectList(reqVO);
            SocialRelationsSaveReqVO createReqVO = new SocialRelationsSaveReqVO();
            List<SocialRelationsChildSaveReqVO> socialRelationList = new ArrayList<>();
            for (SocialRelationsDO socialRelationsDO : list) {
                createReqVO.setJgrybm(socialRelationsDO.getJgrybm());

                SocialRelationsChildSaveReqVO socialRelationsChildSaveReqVO = new SocialRelationsChildSaveReqVO();
//            socialRelationsChildSaveReqVO.setId(socialRelationsDO.getId());
                socialRelationsChildSaveReqVO.setName(socialRelationsDO.getName());
                socialRelationsChildSaveReqVO.setGender(socialRelationsDO.getGender());
                socialRelationsChildSaveReqVO.setIdType(socialRelationsDO.getIdType());
                socialRelationsChildSaveReqVO.setIdNumber(socialRelationsDO.getIdNumber());
                socialRelationsChildSaveReqVO.setRelationship(socialRelationsDO.getRelationship());
                socialRelationsChildSaveReqVO.setContact(socialRelationsDO.getContact());
                socialRelationsChildSaveReqVO.setWorkUnit(socialRelationsDO.getWorkUnit());
                socialRelationsChildSaveReqVO.setAddress(socialRelationsDO.getAddress());
                socialRelationsChildSaveReqVO.setOccupation(socialRelationsDO.getOccupation());
                socialRelationsChildSaveReqVO.setImageUrl(socialRelationsDO.getImageUrl());
                socialRelationsChildSaveReqVO.setRelationsAttch(socialRelationsDO.getRelationsAttch());
                socialRelationList.add(socialRelationsChildSaveReqVO);

            }
            createReqVO.setSocialRelationList(socialRelationList);
            socialRelationsService.createSocialRelations(createReqVO);
        } catch (Exception e) {
            log.error("syncSocialRelations  err,json:{}", e);
        }
    }
}

