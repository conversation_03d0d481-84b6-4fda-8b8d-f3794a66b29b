package com.rs.module.acp.service.sys;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.sys.vo.*;
import com.rs.module.acp.entity.sys.VbConfigCurrentDO;
import com.rs.module.acp.entity.sys.VbTaskDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-语音播报-待播报任务 Service 接口
 *
 * <AUTHOR>
 */
public interface VbTaskService extends IBaseService<VbTaskDO>{

    /**
     * 创建实战平台-语音播报-待播报任务
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createVbTask(@Valid VbTaskSaveReqVO createReqVO);

    /**
     * 更新实战平台-语音播报-待播报任务
     *
     * @param updateReqVO 更新信息
     */
    void updateVbTask(@Valid VbTaskSaveReqVO updateReqVO);

    /**
     * 删除实战平台-语音播报-待播报任务
     *
     * @param id 编号
     */
    void deleteVbTask(String id);

    /**
     * 获得实战平台-语音播报-待播报任务
     *
     * @param id 编号
     * @return 实战平台-语音播报-待播报任务
     */
    VbTaskDO getVbTask(String id);

    /**
    * 获得实战平台-语音播报-待播报任务分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-语音播报-待播报任务分页
    */
    PageResult<VbTaskDO> getVbTaskPage(VbTaskPageReqVO pageReqVO);

    /**
    * 获得实战平台-语音播报-待播报任务列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-语音播报-待播报任务列表
    */
    List<VbTaskDO> getVbTaskList(VbTaskListReqVO listReqVO);

    /**
     * 获取语音播报待处理列表
     * @param serialNumber String 设备序列号
     * @return List<VbTaskDO>
     */
    public List<VbTaskDO> getVbTaskListApp(String serialNumber);
    
    /**
     * 保存实时播报任务
     * @param currentDO VbConfigCurrentDO 实时播报配置
     */
    public void saveCurrentJob(VbConfigCurrentDO currentDO);
    
    /**
     * 获取待清理的播报任务
     * @return List<VbTaskDO>
     */
    public List<VbTaskDO> getNeedCleanTaskList();
}
