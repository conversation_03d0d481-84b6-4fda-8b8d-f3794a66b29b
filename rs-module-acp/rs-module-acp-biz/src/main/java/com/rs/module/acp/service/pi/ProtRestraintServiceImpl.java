package com.rs.module.acp.service.pi;
import cn.hutool.core.bean.BeanUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.cons.CommonConstants;
import com.rs.module.acp.controller.app.pi.vo.ProtRestraintAppPageReqVO;
import com.rs.module.acp.controller.app.pi.vo.ProtRestraintAppRespVO;
import com.rs.module.acp.controller.app.pi.vo.ProtRestraintAppSaveReqVO;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.base.vo.ApproveReqVO;
import com.rs.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.entity.pi.ProtRestraintDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.ProtRestraintDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-巡视管控-保护性约束 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ProtRestraintServiceImpl extends BaseServiceImpl<ProtRestraintDao, ProtRestraintDO> implements ProtRestraintService {

    private static final String FLOW_NAME = "";
    @Resource
    private ProtRestraintDao protRestraintDao;
    @Resource
    private PrisonerService prisonerService;
    @Override
    public String createProtRestraint(ProtRestraintSaveReqVO createReqVO)  throws Exception {
        if (isExist(createReqVO.getJgrybm())) throw new ServerException("当前人员已经存在约束时间范围的记录");
        ProtRestraintDO entity = BeanUtils.toBean(createReqVO, ProtRestraintDO.class);
        initDefault(entity);
        createStartProcess(entity);
        protRestraintDao.insert(entity);
        return entity.getId();
    }
    @Override
    public void updateProtRestraint(ProtRestraintSaveReqVO updateReqVO) {
        // 校验存在
        validateProtRestraintExists(updateReqVO.getId());
        // 更新
        ProtRestraintDO entity = BeanUtils.toBean(updateReqVO, ProtRestraintDO.class);
        initDefault(entity);
        protRestraintDao.updateById(entity);
    }

    @Override
    public void deleteProtRestraint(String id) {
        // 校验存在
        validateProtRestraintExists(id);
        // 删除
        protRestraintDao.deleteById(id);
    }

    private void validateProtRestraintExists(String id) {
        if (protRestraintDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-保护性约束数据不存在");
        }
    }

    @Override
    public ProtRestraintDO getProtRestraint(String id) {
        return protRestraintDao.selectById(id);
    }

    @Override
    public PageResult<ProtRestraintDO> getProtRestraintPage(ProtRestraintPageReqVO pageReqVO) {
        return protRestraintDao.selectPage(pageReqVO);
    }
    @Override
    public Boolean approve(ApproveReqVO approveReqVO) throws Exception  {
        initApproveInfo(approveReqVO);
        ProtRestraintDO entity = protRestraintDao.selectById(approveReqVO.getId());
        BspApproceStatusEnum isApprove = BspApproceStatusEnum.NOT_PASSED;
        String status = "03";
        if("1".equals(approveReqVO.getApprovalResult())){
            status = "02";
            isApprove = BspApproceStatusEnum.PASSED;
        }
        //approveReqVO.setActInstId(entity.getActInstId());
        //approveReqVO.setTaskId(entity.getTaskId());
        approveReqVO.setDefKey(FLOW_NAME);
        BeanUtil.copyProperties( approveReqVO,entity);
        entity.setStatus(status);
        String msgTit = "";
        String msgUrl = "";
        try{
            Map<String,String> approvalResult = BspApprovalUtil.approvalProcessMap(approveReqVO,
                    isApprove,
                    msgTit,
                    msgUrl,
                    true,
                    null,
                    null,
                    HttpUtils.getAppCode()
            );
            String taskId = approvalResult.get("taskId"); // 更新任务ID
            //entity.setTaskId(taskId);
        }catch (Exception e){
            log.error("审批失败",e);
            throw new Exception(e.getMessage());
        }
        protRestraintDao.updateById(entity);
        return true;
    }
    @Override
    public List<ProtRestraintDO> getProtRestraintList(ProtRestraintListReqVO listReqVO) {
        return protRestraintDao.selectList(listReqVO);
    }

    @Override
    public String appCreateProtRestraint(ProtRestraintAppSaveReqVO createReqVO) throws Exception{
        if(isExist(createReqVO.getJgrybm())) throw new ServerException("当前人员已经存在约束时间范围的记录");
        ProtRestraintDO entity = BeanUtils.toBean(createReqVO, ProtRestraintDO.class);
        initDefault(entity);
        //createStartProcess(entity);
        protRestraintDao.insert(entity);
        return entity.getId();
    }

    /**
     * 查询当前人员是否已经存在约束时间范围的记录
     * @param jgrybm
     * @return
     */
    @Override
    public boolean isExist(String jgrybm) {
        Date nowDate = new Date();
        int count = protRestraintDao.selectCount(new LambdaQueryWrapperX<ProtRestraintDO>()
                .eq(ProtRestraintDO::getJgrybm, jgrybm)
                        .in(ProtRestraintDO::getStatus, new String[]{"01", "03"})
                        .eq(ProtRestraintDO::getIsDel, 0)
                .le(ProtRestraintDO::getStartTime, nowDate)
                .ge(ProtRestraintDO::getEndTime, nowDate));
        return count > 0 ? true : false;
    }
    private void createStartProcess(ProtRestraintDO entity) throws Exception{
        Map<String, Object> variables = new HashMap<>();
        String msgTit = "";
        String msgUrl = "";
        //启动流程
        Map<String,String> processResult = BspApprovalUtil.commonStartProcessMap(FLOW_NAME, entity.getId(), msgTit, msgUrl, variables, HttpUtils.getAppCode());
        if(StringUtil.isEmpty(processResult.get("actInstId")) || StringUtil.isEmpty(processResult.get("taskId"))){
            log.error("启动流程失败");
            throw new Exception("启动流程失败");
        }
        //entity.setActInstId(processResult.get("actInstId"));
        //entity.setTaskId(processResult.get("taskId"));
    }
    /**
     * 查询当前时间类型指定监室的记录
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<ProtRestraintAppRespVO> getProtRestraintAppPage(ProtRestraintAppPageReqVO pageReqVO) {
        //根据日期类型日期类型 dateType 0:全部,1:今天,2:昨天,3:近一周,4:近一月 5:自定义 获取开始时间跟结束时间
        if(StringUtil.isNotEmpty(pageReqVO.getDateType())) {
            switch (pageReqVO.getDateType()) {
                case "0":
                    break;
                case "1":
                    Date[] date = DateUtil.getTimeRange(1);
                    pageReqVO.setStartTime(date[0]);
                    pageReqVO.setEndTime(date[0]);
                    break;
                case "2":
                    Date[] date1 = DateUtil.getTimeRange(2);
                    pageReqVO.setStartTime(date1[0]);
                    pageReqVO.setEndTime(date1[0]);
                    break;
                case "3":
                    Date[] date2 = DateUtil.getTimeRange(3);
                    pageReqVO.setStartTime(date2[0]);
                    pageReqVO.setEndTime(date2[0]);
            }
        }
        PageResult<ProtRestraintAppRespVO> pageResult = new PageResult<>();
        List<PrisonerInDO> personList = prisonerService.getPrisonerInList(pageReqVO.getOrgCode(), pageReqVO.getRoomId());
        if (personList == null) {
            return pageResult;
        }
        Map<String,PrisonerInDO> ryxxMap = new HashMap<>();
        for (PrisonerInDO person : personList) {
            ryxxMap.put(person.getJgrybm(),person);
        }
        List<String> rybmList = new ArrayList<>(ryxxMap.keySet());
        LambdaQueryWrapperX<ProtRestraintDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.inIfPresent(ProtRestraintDO::getJgrybm,rybmList);
        wrapper.geIfPresent(ProtRestraintDO::getAddTime,pageReqVO.getStartTime());
        wrapper.leIfPresent(ProtRestraintDO::getAddTime,pageReqVO.getEndTime());
        PageResult<ProtRestraintDO> pageResultpageResultDO = protRestraintDao.selectPage(pageReqVO,wrapper);
        //遍历pageResult 转换成 ProtRestraintAppRespVO 类型 并根据jgrybm 设置ProtRestraintAppRespVO的年龄(根据csrq 计算)跟性别 xb
        List<ProtRestraintAppRespVO> list = new ArrayList<>();
        for (ProtRestraintDO protRestraintDO : pageResultpageResultDO.getList()) {
            if(!ryxxMap.containsKey(protRestraintDO.getJgrybm())) continue;
            ProtRestraintAppRespVO protRestraintAppRespVO = BeanUtils.toBean(protRestraintDO, ProtRestraintAppRespVO.class);
            PrisonerInDO person = ryxxMap.get(protRestraintDO.getJgrybm());
            if(person != null){
                Instant instant = person.getCsrq().toInstant();
                ZoneId zoneId = ZoneId.systemDefault();
                LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime();
                protRestraintAppRespVO.setAge(DateUtil.calcAge(localDateTime).intValue());
                protRestraintAppRespVO.setXb(person.getXb());
                protRestraintAppRespVO.setFrontPhoto(person.getFrontPhoto());
                String statusName = "未审批";
                if("03".equals(protRestraintAppRespVO.getStatus())){
                    statusName = "审批通过";
                }else if("02".equals(protRestraintAppRespVO.getStatus())){
                    statusName = "审批不通过";
                }
                protRestraintAppRespVO.setStatusName(statusName);

            }
            list.add(protRestraintAppRespVO);
        }
        pageResult.setList(list);
        return pageResult;
    }
    private void initDefault(ProtRestraintDO entity){
        if(StringUtil.isEmpty(entity.getId())) entity.setId(StringUtil.getGuid());
        if(StringUtil.isEmpty(entity.getStatus())) entity.setStatus("01");
        if(StringUtil.isEmpty(entity.getApplicant())) entity.setApplicant(SessionUserUtil.getSessionUser().getName());
        if(StringUtil.isEmpty(entity.getApplicantSfzh())) entity.setApplicantSfzh(SessionUserUtil.getSessionUser().getIdCard());
    }
    private void initApproveInfo(ApproveReqVO approveReqVO){
        if(StringUtil.isEmpty(approveReqVO.getApproverXm())) approveReqVO.setApproverXm(SessionUserUtil.getSessionUser().getName());
        if(StringUtil.isEmpty(approveReqVO.getApproverSfzh())) approveReqVO.setApproverSfzh(SessionUserUtil.getSessionUser().getIdCard());
        if(approveReqVO.getApproverTime() == null) approveReqVO.setApproverTime(new Date());
    }

}
