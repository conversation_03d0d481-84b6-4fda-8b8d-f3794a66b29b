package com.rs.module.acp.service.gj.groupinout;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.groupinout.GroupInOutDetail;
import com.rs.module.acp.controller.admin.gj.vo.groupinout.GroupInOutRespVO;
import com.rs.module.acp.controller.admin.gj.vo.groupinout.GroupInOutSaveReqVO;
import com.rs.module.acp.controller.app.gj.vo.groupinout.AppGroupInOutStatisticVO;
import com.rs.module.acp.controller.app.gj.vo.inoutrecords.InOutStatisticVO;
import com.rs.module.acp.dao.gj.GroupInOutDao;
import com.rs.module.acp.dao.gj.GroupInOutPrisonerDao;
import com.rs.module.acp.dao.gj.GroupInOutRoomDao;
import com.rs.module.acp.entity.gj.GroupInOutDO;
import com.rs.module.acp.entity.gj.GroupInOutPrisonerDO;
import com.rs.module.acp.entity.gj.GroupInOutRoomDO;
import com.rs.module.acp.service.gj.InOutRecordsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 实战平台-管教业务-集体出入 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GroupInOutServiceImpl extends BaseServiceImpl<GroupInOutDao, GroupInOutDO> implements GroupInOutService {

    @Resource
    private GroupInOutDao groupInOutDao;
    @Resource
    private GroupInOutRoomDao groupInOutRoomDao;
    @Resource
    private GroupInOutPrisonerDao groupInOutPrisonerDao;
    @Resource
    private InOutRecordsService inOutRecordsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createGroupInOut(GroupInOutSaveReqVO createReqVO) {
        // 插入
        GroupInOutDO groupInOut = BeanUtils.toBean(createReqVO, GroupInOutDO.class);
        List<GroupInOutDetail> details = createReqVO.getDetails();
        if (CollUtil.isEmpty(details)) {
            throw new ServerException("详情不能为空");
        }
        List<GroupInOutPrisonerDO> inOutPrisonerDOS = CollUtil.newArrayList();
        List<GroupInOutRoomDO> groupInOutRoomDOS = CollUtil.newArrayList();
        String objectId = details.stream().map(GroupInOutDetail::getValue).distinct().collect(Collectors.joining(","));
        String objectName = details.stream().map(GroupInOutDetail::getName).distinct().collect(Collectors.joining(","));
        groupInOut.setObjectId(objectId);
        groupInOut.setObjectName(objectName);
        if ("0".equals(groupInOut.getIsRoom())) {
            String roomId = details.stream().map(GroupInOutDetail::getRoomCode).distinct().collect(Collectors.joining(","));
            String roomName = details.stream().map(GroupInOutDetail::getRoomName).distinct().collect(Collectors.joining(","));
            groupInOut.setRoomId(roomId);
            groupInOut.setRoomName(roomName);
            groupInOut.setObjectCount(details.size());
        } else {
            groupInOut.setRoomId(objectId);
            groupInOut.setRoomName(objectName);
            int total = 0;
            for (GroupInOutDetail detail : details) {
                AppGroupInOutStatisticVO groupInOutStatisticVO =
                        getGroupInOutStatisticVO(SessionUserUtil.getSessionUser().getOrgCode(), detail.getValue());
                total += groupInOutStatisticVO.getZjRyNum();
            }
            groupInOut.setObjectCount(total);
        }
        String id = groupInOut.getId();
        if (StrUtil.isBlank(id)) {
            groupInOutDao.insert(groupInOut);
        } else {
            groupInOutDao.updateById(groupInOut);
        }
        // 删除
        groupInOutDao.deleteRoom(groupInOut.getId());
        groupInOutDao.deleteJgry(groupInOut.getId());
        for (GroupInOutDetail detail : details) {
            // 按监室
            if ("1".equals(groupInOut.getIsRoom())) {
                GroupInOutRoomDO groupInOutRoomDO = new GroupInOutRoomDO();
                groupInOutRoomDO.setRoomName(detail.getName());
                groupInOutRoomDO.setRoomId(detail.getValue());
                groupInOutRoomDO.setGroupInOutId(groupInOut.getId());
                groupInOutRoomDOS.add(groupInOutRoomDO);
            }
            // 按人员
            if ("0".equals(groupInOut.getIsRoom())) {
                GroupInOutPrisonerDO groupInOutPrisonerDO = new GroupInOutPrisonerDO();
                groupInOutPrisonerDO.setJgryxm(detail.getName());
                groupInOutPrisonerDO.setJgrybm(detail.getValue());
                groupInOutPrisonerDO.setRoomId(detail.getRoomCode());
                groupInOutPrisonerDO.setRoomName(detail.getRoomName());
                groupInOutPrisonerDO.setGroupInOutId(groupInOut.getId());
                inOutPrisonerDOS.add(groupInOutPrisonerDO);
            }
        }
        if (CollUtil.isNotEmpty(inOutPrisonerDOS)) {
            groupInOutPrisonerDao.insertBatch(inOutPrisonerDOS);
        }
        if (CollUtil.isNotEmpty(groupInOutRoomDOS)) {
            groupInOutRoomDao.insertBatch(groupInOutRoomDOS);
        }
        // 返回
        return groupInOut.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteGroupInOut(String id) {
        // 校验存在
        validateGroupInOutExists(id);
        // 删除
        groupInOutDao.delete(id);
        groupInOutDao.deleteRoom(id);
        groupInOutDao.deleteJgry(id);
    }

    private void validateGroupInOutExists(String id) {
        if (groupInOutDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-集体出入数据不存在");
        }
    }

    @Override
    public GroupInOutDO getGroupInOut(String id) {
        return groupInOutDao.selectById(id);
    }

    @Override
    public GroupInOutRespVO getGroupInOutRespVO(String id) {
        GroupInOutDO groupInOutDO = groupInOutDao.selectById(id);
        if (groupInOutDO == null) {
            throw new ServerException("实战平台-管教业务-集体出入数据不存在");
        }
        String isRoom = groupInOutDO.getIsRoom();
        GroupInOutRespVO groupInOutRespVO = new GroupInOutRespVO();
        BeanUtils.copyProperties(groupInOutDO, groupInOutRespVO);
        List<GroupInOutDetail> details = CollUtil.newArrayList();
        if ("1".equals(isRoom)) {
            // 按监室
            List<GroupInOutRoomDO> groupInOutRoomDOS = groupInOutRoomDao.selectList(Wrappers.<GroupInOutRoomDO>query().lambda().eq(GroupInOutRoomDO::getGroupInOutId, id));
            if (CollUtil.isNotEmpty(groupInOutRoomDOS)) {
                for (GroupInOutRoomDO groupInOutRoomDO : groupInOutRoomDOS) {
                    GroupInOutDetail detail = new GroupInOutDetail();
                    detail.setName(groupInOutRoomDO.getRoomName());
                    detail.setValue(groupInOutRoomDO.getRoomId());
                    details.add(detail);
                }
            }
        }
        if ("0".equals(isRoom)) {
            // 按人员
            List<GroupInOutPrisonerDO> groupInOutPrisonerDOS = groupInOutPrisonerDao.selectList(Wrappers.<GroupInOutPrisonerDO>query().lambda().eq(GroupInOutPrisonerDO::getGroupInOutId, id));
            if (CollUtil.isNotEmpty(groupInOutPrisonerDOS)) {
                for (GroupInOutPrisonerDO groupInOutPrisonerDO : groupInOutPrisonerDOS) {
                    GroupInOutDetail detail = new GroupInOutDetail();
                    detail.setName(groupInOutPrisonerDO.getJgryxm());
                    detail.setValue(groupInOutPrisonerDO.getJgrybm());
                    detail.setRoomCode(groupInOutPrisonerDO.getRoomId());
                    detail.setRoomName(groupInOutPrisonerDO.getRoomName());
                    details.add(detail);
                }
            }
        }
        groupInOutRespVO.setDetails(details);
        return groupInOutRespVO;
    }

    @Override
    public AppGroupInOutStatisticVO getGroupInOutStatisticVO(String orgCode, String roomId) {
        InOutStatisticVO inOutStatisticVO = inOutRecordsService.statisticNum(orgCode, roomId);
        AppGroupInOutStatisticVO groupInOutStatisticVO = new AppGroupInOutStatisticVO();
        groupInOutStatisticVO.setRyNum(inOutStatisticVO.getPersonNum());
        groupInOutStatisticVO.setWcRyNum(inOutStatisticVO.getOutTotalNum());
        groupInOutStatisticVO.setZjRyNum(inOutStatisticVO.getPersonNum() - inOutStatisticVO.getOutTotalNum());
        return groupInOutStatisticVO;
    }
}
