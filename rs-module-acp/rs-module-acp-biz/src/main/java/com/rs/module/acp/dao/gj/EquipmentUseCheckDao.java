package com.rs.module.acp.dao.gj;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.acp.controller.admin.gj.vo.EquipmentUseCheckRespVO;
import com.rs.module.acp.entity.gj.EquipmentUseCheckDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* 实战平台-管教业务-戒具检查 Dao
*
* <AUTHOR>
*/
@Mapper
public interface EquipmentUseCheckDao extends IBaseDao<EquipmentUseCheckDO> {


    List<EquipmentUseCheckRespVO> getCheckList(@Param("useId") String useId);

    List<EquipmentUseCheckRespVO> getCheckByRoomCode(@Param("roomCode") String roomCode);

    List<EquipmentUseCheckRespVO> getCheckRecordList(@Param("roomCode") String roomCode,
                                                     @Param("startTime") Date startTime,
                                                     @Param("endTime") Date endTime);

    EquipmentUseCheckRespVO getByUseIdAndDate(@Param("useId") String useId, @Param("date") Date checkDate);

}
