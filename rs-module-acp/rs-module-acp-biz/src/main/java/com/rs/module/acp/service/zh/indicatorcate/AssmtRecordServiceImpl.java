package com.rs.module.acp.service.zh.indicatorcate;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.*;
import com.rs.module.acp.entity.zh.AssmttApprovalDO;
import com.rs.module.acp.entity.zh.AssmttApprovalRecordDO;
import com.rs.module.acp.enums.zh.IndicatorTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.rs.module.acp.entity.zh.AssmtRecordDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.zh.AssmtRecordDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 综合管理-绩效考核-记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AssmtRecordServiceImpl extends BaseServiceImpl<AssmtRecordDao, AssmtRecordDO> implements AssmtRecordService {

    @Resource
    private AssmtRecordDao assmtRecordDao;

    @Override
    public String createAssmtRecord(AssmtRecordSaveReqVO assmtRecordSaveReqVO) {
        // 插入
        AssmtRecordDO assmtRecord = BeanUtils.toBean(assmtRecordSaveReqVO, AssmtRecordDO.class);
        assmtRecordDao.insert(assmtRecord);
        // 返回
        return assmtRecord.getId();
    }

    @Override
    public void updateAssmtRecord(AssmtRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateAssmtRecordExists(updateReqVO.getId());
        // 更新
        AssmtRecordDO updateObj = BeanUtils.toBean(updateReqVO, AssmtRecordDO.class);
        assmtRecordDao.updateById(updateObj);
    }

    @Override
    public void deleteAssmtRecord(String id) {
        // 校验存在
        validateAssmtRecordExists(id);
        // 删除
        assmtRecordDao.deleteById(id);
    }

    private void validateAssmtRecordExists(String id) {
        if (assmtRecordDao.selectById(id) == null) {
            throw new ServerException("综合管理-绩效考核-记录数据不存在");
        }
    }

    @Override
    public AssmtRecordRespVO getAssmtRecord(String id) {
        AssmtRecordDO assmtRecordDO = assmtRecordDao.selectById(id);
        return BeanUtils.toBean(assmtRecordDO, AssmtRecordRespVO.class);
    }

    @Override
    public PageResult<AssmtRecordDO> getAssmtRecordPage(AssmtRecordPageReqVO pageReqVO) {
        return assmtRecordDao.selectPage(pageReqVO);
    }

    @Override
    public List<AssmtRecordDO> getAssmtRecordList(AssmtRecordListReqVO listReqVO) {
        return assmtRecordDao.selectList(listReqVO);
    }


}
