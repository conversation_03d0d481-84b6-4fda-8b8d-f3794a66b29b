package com.rs.module.acp.entity.zh;

import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 综合管理-绩效考核-考核审核记录 DO
 *
 * <AUTHOR>
 */
@TableName("acp_zh_assmtt_approval_record")
@KeySequence("acp_zh_assmtt_approval_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_assmtt_approval_record")
public class AssmttApprovalRecordDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 加减分审核ID
     */
    private String assmttApprovalId;
    /**
     * 指标类型ID
     */
    private String indicatorCateId;
    /**
     * 指标类型名称
     */
    private String indicatorCateName;
    /**
     * 所属主指标ID
     */
    private String mainIndicatorId;
    /**
     * 所属子指标ID
     */
    private String subIndicatorId;
    /**
     * 指标名称
     */
    private String indicatorName;
    /**
     * 指标描述
     */
    private String indicatorDescription;
    /**
     * 分值类型，字典：FIXED：固定分，RANGE：范围分
     */
    private String scoreType;
    /**
     * 加减分，字典：ADD：加分、SUBTRACT：扣分
     */
    private String addSubtract;
    /**
     * 基础分值
     */
    private BigDecimal baseScore;
    /**
     * 最小分值
     */
    private BigDecimal minScore;
    /**
     * 最大分值（仅范围分有效）
     */
    private BigDecimal maxScore;
    /**
     * 排序序号
     */
    private Integer sortOrder;
    /**
     * 绩效原因
     */
    private String assmtReason;
    /**
     * 绩效分数
     */
    private BigDecimal assmtScore;
    /**
     * 综合审核原因
     */
    private String zhApprovalReason;
    /**
     * 综合审核分数
     */
    private BigDecimal zhApprovalScore;
    /**
     * 最终审核分数
     */
    private BigDecimal zzApprovalScore;

}
