package com.rs.module.acp.dao.gj.conflict;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.acp.entity.gj.conflict.ConflictPrisonerDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 实战平台-管教业务-社会矛盾化解登记-关联在押人员 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface ConflictPrisonerDao extends IBaseDao<ConflictPrisonerDO> {

    @Delete("<script>" +
            "DELETE FROM acp_gj_conflict_prisoner " +
            "WHERE event_code IN " +
            "<foreach collection='eventCodes' item='eventCode' open='(' separator=',' close=')'>" +
            "#{eventCode}" +
            "</foreach>" +
            "</script>")
    void deleteByEventCode(@Param("eventCodes") List<String> eventCodes);
}
