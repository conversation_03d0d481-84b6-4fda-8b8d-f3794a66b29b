package com.rs.module.acp.service.zh.indicatorcate;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCatePageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateRespVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateSaveReqVO;
import com.rs.module.acp.entity.zh.IndicatorCateDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 综合管理-绩效考核指标分类 Service 接口
 *
 * <AUTHOR>
 */
public interface IndicatorCateService extends IBaseService<IndicatorCateDO>{

    /**
     * 创建综合管理-绩效考核指标分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createIndicatorCate(@Valid IndicatorCateSaveReqVO createReqVO);

    /**
     * 更新综合管理-绩效考核指标分类
     *
     * @param updateReqVO 更新信息
     */
    void updateIndicatorCate(@Valid IndicatorCateSaveReqVO updateReqVO);

    /**
     * 删除综合管理-绩效考核指标分类
     *
     * @param id 编号
     */
    void deleteIndicatorCate(String id);

    /**
     * 获得综合管理-绩效考核指标分类
     *
     * @param id 编号
     * @return 综合管理-绩效考核指标分类
     */
    IndicatorCateRespVO getIndicatorCate(String id);

    /**
    * 获得综合管理-绩效考核指标分类分页
    *
    * @param pageReqVO 分页查询
    * @return 综合管理-绩效考核指标分类分页
    */
    PageResult<IndicatorCateDO> getIndicatorCatePage(IndicatorCatePageReqVO pageReqVO);

    /**
    * 获得综合管理-绩效考核指标分类列表
    *
    * @param listReqVO 查询条件
    * @return 综合管理-绩效考核指标分类列表
    */
    List<IndicatorCateDO> getIndicatorCateList(IndicatorCateListReqVO listReqVO);


}
