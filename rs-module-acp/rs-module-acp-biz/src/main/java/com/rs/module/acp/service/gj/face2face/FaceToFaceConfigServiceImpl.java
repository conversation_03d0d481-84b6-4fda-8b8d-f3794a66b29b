package com.rs.module.acp.service.gj.face2face;

import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceConfigListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceConfigPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceConfigSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.FaceToFaceConfigDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.FaceToFaceConfigDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-面对面配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FaceToFaceConfigServiceImpl extends BaseServiceImpl<FaceToFaceConfigDao, FaceToFaceConfigDO> implements FaceToFaceConfigService {

    @Resource
    private FaceToFaceConfigDao faceToFaceConfigDao;

    @Override
    public String createFaceToFaceConfig(FaceToFaceConfigSaveReqVO createReqVO) {
        // 插入
        FaceToFaceConfigDO faceToFaceConfig = BeanUtils.toBean(createReqVO, FaceToFaceConfigDO.class);
        faceToFaceConfigDao.insert(faceToFaceConfig);
        // 返回
        return faceToFaceConfig.getId();
    }

    @Override
    public void updateFaceToFaceConfig(FaceToFaceConfigSaveReqVO updateReqVO) {
        // 校验存在
        validateFaceToFaceConfigExists(updateReqVO.getId());
        // 更新
        FaceToFaceConfigDO updateObj = BeanUtils.toBean(updateReqVO, FaceToFaceConfigDO.class);
        faceToFaceConfigDao.updateById(updateObj);
    }

    @Override
    public void deleteFaceToFaceConfig(String id) {
        // 校验存在
        validateFaceToFaceConfigExists(id);
        // 删除
        faceToFaceConfigDao.deleteById(id);
    }

    private void validateFaceToFaceConfigExists(String id) {
        if (faceToFaceConfigDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-面对面配置数据不存在");
        }
    }

    @Override
    public FaceToFaceConfigDO getFaceToFaceConfig(String id) {
        return faceToFaceConfigDao.selectById(id);
    }

    @Override
    public PageResult<FaceToFaceConfigDO> getFaceToFaceConfigPage(FaceToFaceConfigPageReqVO pageReqVO) {
        return faceToFaceConfigDao.selectPage(pageReqVO);
    }

    @Override
    public List<FaceToFaceConfigDO> getFaceToFaceConfigList(FaceToFaceConfigListReqVO listReqVO) {
        return faceToFaceConfigDao.selectList(listReqVO);
    }

    @Override
    public List<FaceToFaceConfigDO> selectListByOrgCode(String orgCode) {
        return faceToFaceConfigDao.selectListByOrgCode(orgCode);
    }


}
