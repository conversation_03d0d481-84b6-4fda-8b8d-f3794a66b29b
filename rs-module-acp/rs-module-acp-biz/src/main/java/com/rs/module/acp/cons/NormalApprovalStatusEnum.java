package com.rs.module.acp.cons;

/**
 * 普通审批状态，对于字典编码 ZD_SP_STATUS
 */
public enum NormalApprovalStatusEnum {

    PENDING("1", "待审批"),
    APPROVED("2", "同意"),
    REJECTED("3", "不同意");

    private final String code;
    private final String value;

    NormalApprovalStatusEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 状态码
     * @return 对应的枚举值
     */
    public static NormalApprovalStatusEnum getByCode(String code) {
        for (NormalApprovalStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据value获取枚举值
     *
     * @param value 状态值
     * @return 对应的枚举值
     */
    public static NormalApprovalStatusEnum getByValue(String value) {
        for (NormalApprovalStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
}