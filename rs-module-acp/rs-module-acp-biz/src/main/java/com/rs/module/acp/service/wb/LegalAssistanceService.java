package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.LegalAssistanceDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-法律帮助申请 Service 接口
 *
 * <AUTHOR>
 */
public interface LegalAssistanceService extends IBaseService<LegalAssistanceDO>{

    /**
     * 创建实战平台-窗口业务-法律帮助申请
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createLegalAssistance(@Valid LegalAssistanceSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-法律帮助申请
     *
     * @param updateReqVO 更新信息
     */
    void updateLegalAssistance(@Valid LegalAssistanceSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-法律帮助申请
     *
     * @param id 编号
     */
    void deleteLegalAssistance(String id);

    /**
     * 获得实战平台-窗口业务-法律帮助申请
     *
     * @param id 编号
     * @return 实战平台-窗口业务-法律帮助申请
     */
    LegalAssistanceDO getLegalAssistance(String id);

    /**
    * 获得实战平台-窗口业务-法律帮助申请分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-法律帮助申请分页
    */
    PageResult<LegalAssistanceDO> getLegalAssistancePage(LegalAssistancePageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-法律帮助申请列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-法律帮助申请列表
    */
    List<LegalAssistanceDO> getLegalAssistanceList(LegalAssistanceListReqVO listReqVO);

    /**
     * 根据ID获取法律援助信息
     * @param id
     * @return
     */
    LegalAssistanceRespVO getLegalAssistanceById(String id);

}
