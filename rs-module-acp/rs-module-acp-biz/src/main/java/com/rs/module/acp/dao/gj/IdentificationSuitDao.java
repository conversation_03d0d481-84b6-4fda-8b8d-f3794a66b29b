package com.rs.module.acp.dao.gj;

import com.rs.module.acp.controller.admin.gj.vo.identificationsuit.IdentificationSuitSaveReqVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 实战平台-管教业务-识别服管理 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface IdentificationSuitDao {


    // 获取已经使用过的标识符号
    List<String> getUsedSuitNumByRoomId(@Param("roomId") String roomId);


    void updateSbfhByJgrybm(@Param("jgrybm") String jgrybm, @Param("suitNum") String suitNum,
                            @Param("suitColor") String suitColor, @Param("tableName") String tableName);
}
