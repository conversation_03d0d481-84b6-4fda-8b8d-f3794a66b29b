package com.rs.module.acp.service.gj.equipment;

import com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseExtendListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseExtendPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseExtendSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.EquipmentUseExtendDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.EquipmentUseExtendDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-械具使用延长呈批 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EquipmentUseExtendServiceImpl extends BaseServiceImpl<EquipmentUseExtendDao, EquipmentUseExtendDO> implements EquipmentUseExtendService {

    @Resource
    private EquipmentUseExtendDao equipmentUseExtendDao;

    @Override
    public String createEquipmentUseExtend(EquipmentUseExtendSaveReqVO createReqVO) {
        // 插入
        EquipmentUseExtendDO equipmentUseExtend = BeanUtils.toBean(createReqVO, EquipmentUseExtendDO.class);
        equipmentUseExtendDao.insert(equipmentUseExtend);
        // 返回
        return equipmentUseExtend.getId();
    }

    @Override
    public void updateEquipmentUseExtend(EquipmentUseExtendSaveReqVO updateReqVO) {
        // 校验存在
        validateEquipmentUseExtendExists(updateReqVO.getId());
        // 更新
        EquipmentUseExtendDO updateObj = BeanUtils.toBean(updateReqVO, EquipmentUseExtendDO.class);
        equipmentUseExtendDao.updateById(updateObj);
    }

    @Override
    public void deleteEquipmentUseExtend(String id) {
        // 校验存在
        validateEquipmentUseExtendExists(id);
        // 删除
        equipmentUseExtendDao.deleteById(id);
    }

    private void validateEquipmentUseExtendExists(String id) {
        if (equipmentUseExtendDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-械具使用延长呈批数据不存在");
        }
    }

    @Override
    public EquipmentUseExtendDO getEquipmentUseExtend(String id) {
        return equipmentUseExtendDao.selectById(id);
    }

    @Override
    public PageResult<EquipmentUseExtendDO> getEquipmentUseExtendPage(EquipmentUseExtendPageReqVO pageReqVO) {
        return equipmentUseExtendDao.selectPage(pageReqVO);
    }

    @Override
    public List<EquipmentUseExtendDO> getEquipmentUseExtendList(EquipmentUseExtendListReqVO listReqVO) {
        return equipmentUseExtendDao.selectList(listReqVO);
    }


}
