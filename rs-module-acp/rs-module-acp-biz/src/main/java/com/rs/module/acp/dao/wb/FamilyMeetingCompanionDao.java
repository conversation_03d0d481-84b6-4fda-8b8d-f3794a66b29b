package com.rs.module.acp.dao.wb;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.FamilyMeetingCompanionDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-窗口业务-家属会见同行登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface FamilyMeetingCompanionDao extends IBaseDao<FamilyMeetingCompanionDO> {


    default PageResult<FamilyMeetingCompanionDO> selectPage(FamilyMeetingCompanionPageReqVO reqVO) {
        Page<FamilyMeetingCompanionDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<FamilyMeetingCompanionDO> wrapper = new LambdaQueryWrapperX<FamilyMeetingCompanionDO>()
            .eqIfPresent(FamilyMeetingCompanionDO::getFamilyMeetingId, reqVO.getFamilyMeetingId())
            .likeIfPresent(FamilyMeetingCompanionDO::getName, reqVO.getName())
            .eqIfPresent(FamilyMeetingCompanionDO::getGender, reqVO.getGender())
            .eqIfPresent(FamilyMeetingCompanionDO::getIdCard, reqVO.getIdCard())
            .eqIfPresent(FamilyMeetingCompanionDO::getCompanionType, reqVO.getCompanionType())
            .eqIfPresent(FamilyMeetingCompanionDO::getAttachmentUrl, reqVO.getAttachmentUrl())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(FamilyMeetingCompanionDO::getAddTime);
        }
        Page<FamilyMeetingCompanionDO> familyMeetingCompanionPage = selectPage(page, wrapper);
        return new PageResult<>(familyMeetingCompanionPage.getRecords(), familyMeetingCompanionPage.getTotal());
    }
    default List<FamilyMeetingCompanionDO> selectList(FamilyMeetingCompanionListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<FamilyMeetingCompanionDO>()
            .eqIfPresent(FamilyMeetingCompanionDO::getFamilyMeetingId, reqVO.getFamilyMeetingId())
            .likeIfPresent(FamilyMeetingCompanionDO::getName, reqVO.getName())
            .eqIfPresent(FamilyMeetingCompanionDO::getGender, reqVO.getGender())
            .eqIfPresent(FamilyMeetingCompanionDO::getIdCard, reqVO.getIdCard())
            .eqIfPresent(FamilyMeetingCompanionDO::getCompanionType, reqVO.getCompanionType())
            .eqIfPresent(FamilyMeetingCompanionDO::getAttachmentUrl, reqVO.getAttachmentUrl())
        .orderByDesc(FamilyMeetingCompanionDO::getAddTime));    }


    }
