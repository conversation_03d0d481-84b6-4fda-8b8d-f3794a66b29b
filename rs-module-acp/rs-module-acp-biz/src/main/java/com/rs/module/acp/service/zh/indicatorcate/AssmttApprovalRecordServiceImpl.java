package com.rs.module.acp.service.zh.indicatorcate;

import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmttApprovalRecordListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmttApprovalRecordPageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmttApprovalRecordSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.zh.AssmttApprovalRecordDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.zh.AssmttApprovalRecordDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 综合管理-绩效考核-考核审核记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AssmttApprovalRecordServiceImpl extends BaseServiceImpl<AssmttApprovalRecordDao, AssmttApprovalRecordDO> implements AssmttApprovalRecordService {

    @Resource
    private AssmttApprovalRecordDao assmttApprovalRecordDao;

    @Override
    public String createAssmttApprovalRecord(AssmttApprovalRecordSaveReqVO createReqVO) {
        // 插入
        AssmttApprovalRecordDO assmttApprovalRecord = BeanUtils.toBean(createReqVO, AssmttApprovalRecordDO.class);
        assmttApprovalRecordDao.insert(assmttApprovalRecord);
        // 返回
        return assmttApprovalRecord.getId();
    }

    @Override
    public void updateAssmttApprovalRecord(AssmttApprovalRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateAssmttApprovalRecordExists(updateReqVO.getId());
        // 更新
        AssmttApprovalRecordDO updateObj = BeanUtils.toBean(updateReqVO, AssmttApprovalRecordDO.class);
        assmttApprovalRecordDao.updateById(updateObj);
    }

    @Override
    public void deleteAssmttApprovalRecord(String id) {
        // 校验存在
        validateAssmttApprovalRecordExists(id);
        // 删除
        assmttApprovalRecordDao.deleteById(id);
    }

    private void validateAssmttApprovalRecordExists(String id) {
        if (assmttApprovalRecordDao.selectById(id) == null) {
            throw new ServerException("综合管理-绩效考核-考核审核记录数据不存在");
        }
    }

    @Override
    public AssmttApprovalRecordDO getAssmttApprovalRecord(String id) {
        return assmttApprovalRecordDao.selectById(id);
    }

    @Override
    public PageResult<AssmttApprovalRecordDO> getAssmttApprovalRecordPage(AssmttApprovalRecordPageReqVO pageReqVO) {
        return assmttApprovalRecordDao.selectPage(pageReqVO);
    }

    @Override
    public List<AssmttApprovalRecordDO> getAssmttApprovalRecordList(AssmttApprovalRecordListReqVO listReqVO) {
        return assmttApprovalRecordDao.selectList(listReqVO);
    }


}
