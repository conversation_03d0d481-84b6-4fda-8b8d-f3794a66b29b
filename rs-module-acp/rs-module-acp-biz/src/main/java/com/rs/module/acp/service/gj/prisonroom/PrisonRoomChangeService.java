package com.rs.module.acp.service.gj.prisonroom;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApprovalTraceVO;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsACPSaveVO;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.prisonroom.*;
import com.rs.module.acp.controller.app.gj.vo.prisonroom.PrisonRoomChangeAppListReqVO;
import com.rs.module.acp.controller.app.gj.vo.prisonroom.PrisonRoomChangeAppListVO;
import com.rs.module.acp.entity.gj.PrisonRoomChangeDO;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-管教业务--监室调整 Service 接口
 *
 * <AUTHOR>
 */
public interface PrisonRoomChangeService extends IBaseService<PrisonRoomChangeDO>{

    /**
     * 创建实战平台-管教业务--监室调整
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrisonRoomChange(@Valid PrisonRoomChangeSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务--监室调整
     *
     * @param updateReqVO 更新信息
     */
    void updatePrisonRoomChange(@Valid PrisonRoomChangeSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务--监室调整
     *
     * @param id 编号
     */
    void deletePrisonRoomChange(String id);

    /**
     * 获得实战平台-管教业务--监室调整
     *
     * @param id 编号
     * @return 实战平台-管教业务--监室调整
     */
    PrisonRoomChangeDO getPrisonRoomChange(String id);

    /**
    * 获得实战平台-管教业务--监室调整分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务--监室调整分页
    */
    PageResult<PrisonRoomChangeDO> getPrisonRoomChangePage(PrisonRoomChangePageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务--监室调整列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务--监室调整列表
    */
    List<PrisonRoomChangeDO> getPrisonRoomChangeList(PrisonRoomChangeListReqVO listReqVO);

    /**
     * 监室调整
     * @param id
     * @param status
     * @param actInstId
     * @param taskId
     * @return
     */
    Boolean updateProcessStatus(PrisonRoomChangeProcessVO processVO);

    /**
     * 批量监室调整
     * <AUTHOR>
     * @date 2025/5/28 16:50
     * @param [createReqVOList]
     * @return java.util.List<java.lang.String>
     */
    List<String> batchCreatePrisonRoomChangeList(List<PrisonRoomChangeSaveReqVO> createReqVOList);

    /**
     * 批量更新
     * <AUTHOR>
     * @date 2025/5/28 17:21
     * @param [updateReqVOList]
     * @return java.lang.Boolean
     */
    Boolean batchUpdatePrisonRoomChange(List<PrisonRoomChangeSaveReqVO> updateReqVOList);

    /**
     * 批量修改审批过程
     * <AUTHOR>
     * @date 2025/5/28 18:07
     * @param [processVOList]
     * @return java.lang.Boolean
     */
    Boolean batchUpdateProcessStatus(List<PrisonRoomChangeProcessVO> processVOList);

    /**
     * 获取记录人员列表
     * <AUTHOR>
     * @date 2025/5/28 19:34
     * @param [jgrybm]
     * @return java.util.List<com.rs.module.acp.controller.admin.gj.vo.prisonroom.PrisonRoomChangeRespVO>
     */
    PageResult<PrisonRoomChangeDO> getPrisonRoomChangeByJgrybm(String jgrybm,Long pageNo,Long pageSize);


    /**
     *
     * <AUTHOR>
     * @date 2025/5/29 9:46
     * @param [approveReqVO]
     * @return void
     */
    void leaderApprove(GjApproveReqVO approveReqVO);

    /**
     * 批量审批人员信息
     * @param approveReqList
     */
    void batchLeaderApprove(List<GjApproveReqVO> approveReqList);

    /**
     *  带入带出登记
     * <AUTHOR>
     * @date 2025/5/29 14:30
     * @param [changeInOutSaveReqVO]
     * @return void
     */
    void bringIntakeOutReg(PrisonRoomChangeInOutSaveReqVO changeInOutSaveReqVO);

    @Transactional(rollbackFor = Exception.class)
    void saveInOutRecords(InOutRecordsACPSaveVO saveReqVO);

    void appOutPerson(InOutRecordsSaveReqVO outRecordsSaveReqVO) throws Exception;

    void appInPerson(InOutRecordsSaveReqVO inRecordsSaveReqVO)throws Exception;

    /**
     * 获取
     * @param id
     * @return
     */
    List<GjApprovalTraceVO> getApproveTrack(String id);

    /**
     * 自动生成调整记录
     * <AUTHOR>
     * @date 2025/6/14 20:11
     * @param [vo]
     * @return
     */
    void automaticGenerationRecord(PrisonRoomChangAutoRecordVO vo);

    /**
     * 获取监室调整记录-app那边用到
     * <AUTHOR>
     * @date 2025/6/18 19:17
     * @param [pageReqVO]
     * @return com.rs.framework.common.pojo.PageResult<com.rs.module.acp.controller.app.gj.vo.prisonroom.PrisonRoomChangeAppListVO>
     */
    PageResult<PrisonRoomChangeAppListVO> getPrisonRoomChangeAppList(PrisonRoomChangeAppListReqVO pageReqVO);

    //根据 sourceBusinessId 进行查询监室调整记录
    PrisonRoomChangeDO getPrisonRoomChangeBySourceBusinessId(String sourceBusinessId);
}
