package com.rs.module.acp.service.sldxxfb;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.cache.RedisClient;
import com.bsp.common.cache.RedisEnum;
import com.bsp.common.cache.RedisUtils;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.framework.common.entity.OpsDicCode;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.util.DicUtil;
import com.rs.module.acp.cons.CommonConstants;
import com.rs.module.acp.controller.admin.sldxxfb.vo.WgdjVO;
import com.rs.module.acp.dao.sldxxfb.SldxxfbDao;
import com.rs.module.acp.util.SldxxfbUtil;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.service.pm.PrisonerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 实战平台-所领导信息发布 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SldxxfbServiceImpl extends BaseServiceImpl<SldxxfbDao, PrisonerInDO> implements SldxxfbService {

    @Resource
    private PrisonerService prisonerService;
    @Resource
    private BspApi bspApi;
    @Override
    public List<JSONObject> getAllPrisonerCount(Date endDate) {
        return baseMapper.getAllPrisonerCount(endDate);
    }

    @Override
    public List<JSONObject> getAllPrisonerInOneYear() {
        return baseMapper.getAllPrisonerInOneYear();
    }
    @Override
    public List<JSONObject> getAllPrisonerOutOneYear() {
        return baseMapper.getAllPrisonerOutOneYear();
    }
    @Override
    public Map<String, Integer> getInCount(String orgCode) {
        return baseMapper.getInCount(orgCode);
    }

    @Override
    public List<JSONObject> getAllPrisonerDesignAmount() {
        return baseMapper.getAllPrisonerDesignAmount();
    }

    @Override
    public Map<String, Integer> getOutCount(String orgCode) {
        return baseMapper.getOutCount(orgCode);
    }

    @Override
    public JSONObject zdsj() {
        JSONObject result = new JSONObject();
        //查询机构设计总关押量
        List<JSONObject> orgRoomList  = getAllPrisonerDesignAmount();
        Map<String, Integer> imprisonmentAmountMap= orgRoomList.stream()
                .collect(Collectors.toMap(item -> item.getString("orgcode"), item -> item.getInteger("amountcount")));
        List<JSONObject> list = getAllPrisonerCount(new Date());
        String dicKey = RedisUtils.buildKey(RedisEnum.COM_DIC, new String[]{"ZD_ORG_ID"});
        Map<String, String> orgCodeDicMap = RedisClient.hgetAll(dicKey);
        //for (Map.Entry<String, String> entry : map.entrySet()) {
        List<OpsDicCode> orgCodeList = DicUtil.getDic("ZD_ORG_ID", "acp");
        JSONObject jyb = new JSONObject();//警押比

        Map<String, Integer> inCountMap = getInCount(null);
        // 羁押动态
        JSONObject jydt = new JSONObject();
        jydt.put("jyrs", inCountMap.get("nowTotalCount"));//当前羁押总数
        jydt.put("yesterdayUpNum", inCountMap.get("yesterdayCount"));//昨日新羁押人数
        String kssOrgCodeStr = "110000114,110000112,110000113,110000116";//看守所机构编码字符串
        String jlsOrgCodeStr = "110000121";//拘留所机构编码字符串
        String jdsOrgCodeStr = "110000131";//戒毒所机构编码字符串
        String jgyyOrgCodeStr = "110000151";//监管医院机构编码字符串

        JSONObject kssInfo = new JSONObject();
        JSONObject jlsInfo = new JSONObject();
        JSONObject jdsInfo = new JSONObject();
        JSONObject jgyyInfo = new JSONObject();
        List<JSONObject> jybList = new ArrayList<>();
        List<JSONObject> gsjyrsList = new ArrayList<>();//各监所羁押情况
        for (Map<String, Object> jsonObject : list) {
            String orgCode = (String) jsonObject.getOrDefault("orgcode", "");
            if(StringUtil.isEmpty(orgCode)){
                continue;
            }
            Integer nowTotalNum = Optional.ofNullable(jsonObject.get("nowtotalnum"))
                    .map(o -> o instanceof Number ? ((Number) o).intValue() : Integer.parseInt(o.toString()))
                    .orElse(0);

            Integer historyTotalNum = Optional.ofNullable(jsonObject.get("historytotalnum"))
                    .map(o -> o instanceof Number ? ((Number) o).intValue() : Integer.parseInt(o.toString()))
                    .orElse(0);
            if (kssOrgCodeStr.contains(orgCode)) {
                SldxxfbUtil.updatePrisonInfo(kssInfo, orgCode, nowTotalNum, historyTotalNum, imprisonmentAmountMap);
            }
            if (jlsOrgCodeStr.contains(orgCode)) {
                SldxxfbUtil.updatePrisonInfo(jlsInfo, orgCode, nowTotalNum, historyTotalNum, imprisonmentAmountMap);
            }
            if (jdsOrgCodeStr.contains(orgCode)) {
                SldxxfbUtil.updatePrisonInfo(jdsInfo, orgCode, nowTotalNum, historyTotalNum, imprisonmentAmountMap);
            }
            if (jgyyOrgCodeStr.contains(orgCode)) {
                SldxxfbUtil.updatePrisonInfo(jgyyInfo, orgCode, nowTotalNum, historyTotalNum, imprisonmentAmountMap);
            }
            JSONObject jyrs = new JSONObject();
            jyrs.put("historyTotalNum", historyTotalNum);
            jyrs.put("orgName", orgCodeDicMap.get(orgCode));
            gsjyrsList.add(jyrs);
            JSONObject jybResult = new JSONObject();
            jybResult.put("nowTotalNum",nowTotalNum);
            jybResult.put("imprisonmentAmount", imprisonmentAmountMap.getOrDefault(orgCode, 0));
            SldxxfbUtil.countJybInfo(jybResult);
            jybList.add(jybResult);
        }
        jyb.put("result",jybList);
        SldxxfbUtil.updatePrisonInfo(kssInfo);
        SldxxfbUtil.updatePrisonInfo(jlsInfo);
        SldxxfbUtil.updatePrisonInfo(jdsInfo);
        SldxxfbUtil.updatePrisonInfo(jgyyInfo);
        jydt.put("kssInfo", kssInfo);
        jydt.put("jlsInfo", jlsInfo);
        jydt.put("jdsInfo", jdsInfo);
        jydt.put("jgyyInfo", jgyyInfo);

        //gsjyrsList 按historyTotalNum 降序排序
        gsjyrsList.sort(Comparator.comparingInt(o -> o.getInteger("historyTotalNum")));
        result.put("jydt", jydt);
        result.put("jydtName", "羁押动态");
        result.put("gsjyrsList", gsjyrsList);
        result.put("gsjyrsListName", "各监所押量情况");
        result.put("jyb", jyb);
        result.put("jybName", "警押比");
        //各所羁押人数（昨日）
        return result;
    }
    @Override
    public JSONObject xxry(String orgCode) {
        return SldxxfbUtil.buildResult(getInCount(orgCode), getAllPrisonerInOneYear());
    }
    @Override
    public JSONObject csry(String orgCode) {
        return SldxxfbUtil.buildResult(getOutCount(orgCode), getAllPrisonerOutOneYear());
    }


    @Override
    public JSONObject ssjd(String orgCode) {
        try {
            // 获取 appCode 和 scriptMark
            /*String appCode = HttpUtils.getAppCode();
            String scriptMark = appCode + ":kssssjdtj";

            // 查询数据
            JSONObject queryResult = bspApi.executeMultiQuery(scriptMark, null);
            JSONArray dataArray = queryResult.getJSONArray("data");

            if (CollectionUtil.isEmpty(dataArray)) {
                log.warn("executeFmQuery 返回数据为空");
                return new JSONObject();
            }

            // 解析数据行
            JSONObject rows = dataArray.getJSONObject(0);
            */
            List<JSONObject> queryResult =baseMapper.ssjd(orgCode);
            if(StringUtil.isEmpty(orgCode))
                return SldxxfbUtil.ssjdBuildMergedResult(queryResult);
            for (JSONObject rows :queryResult) {
                if(rows.getString("orgCode").equals(orgCode)) return SldxxfbUtil.ssjdBuildResult(rows);
            }
            return new JSONObject();
        } catch (Exception e) {
            log.error("执行统计数据时发生异常", e);
            return new JSONObject();
        }
    }
    @Override
    public PageResult<WgdjVO> getWggjPage(int pageNo, int pageSize, String timeRange, String handleStatus, String code, String codeType) {
        Page<WgdjVO> page = new Page<>(pageNo, pageSize);
        Page<WgdjVO> pageResult = baseMapper.getWggjPage(page, timeRange, handleStatus, code, codeType);

        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    @Override
    public JSONObject getWggjAllCount(String timeRange, String code, String codeType) {

        return baseMapper.getWggjAllCount(timeRange, code, codeType);
    }

    @Override
    public List<JSONObject> fxryqs(String orgCode, String timeRange) {
        return null;
    }

    @Override
    public JSONObject fxryfb(String orgCode) {
        return null;
    }

    @Override
    public List<JSONObject> wgqs(String orgCode, String timeRange) {
        return null;
    }

    @Override
    public JSONObject gzry(String orgCode) {
        return null;
    }
    @Override
    public JSONObject getRoomMonitorIndex(String roomId, String orgCode) {
        return baseMapper.getRoomMonitorIndex(roomId,orgCode);
    }
}
