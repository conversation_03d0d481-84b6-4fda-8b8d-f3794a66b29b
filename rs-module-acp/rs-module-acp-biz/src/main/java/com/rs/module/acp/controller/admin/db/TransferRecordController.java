package com.rs.module.acp.controller.admin.db;

import com.rs.module.acp.entity.db.TransferPrisonerDO;
import com.rs.module.acp.entity.db.TransferPrisonerInfoDO;
import com.rs.module.acp.service.db.TransferPrisonerService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.TransferRecordDO;
import com.rs.module.acp.service.db.TransferRecordService;

@Api(tags = "实战平台-羁押业务-转所登记")
@RestController
@RequestMapping("/acp/db/transferRecord")
@Validated
public class TransferRecordController {

    @Resource
    private TransferRecordService transferRecordService;

    @Resource
    private TransferPrisonerService transferPrisonerService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-羁押业务-转所登记")
    public CommonResult<String> createTransferRecord(@Valid @RequestBody TransferRecordSaveReqVO createReqVO) {
        return success(transferRecordService.createTransferRecord(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-羁押业务-转所登记")
    public CommonResult<Boolean> updateTransferRecord(@Valid @RequestBody TransferRecordSaveReqVO updateReqVO) {
        transferRecordService.updateTransferRecord(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-羁押业务-转所登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteTransferRecord(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           transferRecordService.deleteTransferRecord(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-羁押业务-转所登记")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<TransferRecordRespVO> getTransferRecord(@RequestParam("id") String id) {
        TransferRecordDO transferRecord = transferRecordService.getTransferRecord(id);
        TransferRecordRespVO transferRecordRespVO = BeanUtils.toBean(transferRecord, TransferRecordRespVO.class);

        //
        List<TransferPrisonerInfoDO> transferPrisonerInfoDOList = transferRecordService.getPrisonersByTransferRecordId(transferRecord.getId());
        transferRecordRespVO.setTransferPrisoners(transferPrisonerInfoDOList);
        return success(transferRecordRespVO);
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-羁押业务-转所登记分页")
    public CommonResult<PageResult<TransferRecordRespVO>> getTransferRecordPage(@Valid @RequestBody TransferRecordPageReqVO pageReqVO) {
        PageResult<TransferRecordDO> pageResult = transferRecordService.getTransferRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TransferRecordRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-羁押业务-转所登记列表")
    public CommonResult<List<TransferRecordRespVO>> getTransferRecordList(@Valid @RequestBody TransferRecordListReqVO listReqVO) {
        List<TransferRecordDO> list = transferRecordService.getTransferRecordList(listReqVO);
        return success(BeanUtils.toBean(list, TransferRecordRespVO.class));
    }
}
