package com.rs.module.acp.dao.zh;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorConfigListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorConfigPageReqVO;
import com.rs.module.acp.entity.zh.IndicatorConfigDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 综合管理-绩效考核截止日期设置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface IndicatorConfigDao extends IBaseDao<IndicatorConfigDO> {


    default PageResult<IndicatorConfigDO> selectPage(IndicatorConfigPageReqVO reqVO) {
        Page<IndicatorConfigDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<IndicatorConfigDO> wrapper = new LambdaQueryWrapperX<IndicatorConfigDO>()
            .eqIfPresent(IndicatorConfigDO::getExpiryDateType, reqVO.getExpiryDateType())
            .eqIfPresent(IndicatorConfigDO::getIntervalDays, reqVO.getIntervalDays())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(IndicatorConfigDO::getAddTime);
        }
        Page<IndicatorConfigDO> indicatorConfigPage = selectPage(page, wrapper);
        return new PageResult<>(indicatorConfigPage.getRecords(), indicatorConfigPage.getTotal());
    }
    default List<IndicatorConfigDO> selectList(IndicatorConfigListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<IndicatorConfigDO>()
            .eqIfPresent(IndicatorConfigDO::getExpiryDateType, reqVO.getExpiryDateType())
            .eqIfPresent(IndicatorConfigDO::getIntervalDays, reqVO.getIntervalDays())
        .orderByDesc(IndicatorConfigDO::getAddTime));    }


    }
