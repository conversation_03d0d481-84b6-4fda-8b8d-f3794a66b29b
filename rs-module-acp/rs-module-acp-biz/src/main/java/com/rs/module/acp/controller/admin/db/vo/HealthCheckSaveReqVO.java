package com.rs.module.acp.controller.admin.db.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-收押业务-入所健康检查登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class HealthCheckSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("人员编号")
    @NotEmpty(message = "人员编号不能为空")
    private String rybh;

    @ApiModelProperty("姓名")
    @NotEmpty(message = "姓名不能为空")
    private String name;

    @ApiModelProperty("健康状况")
//    @NotEmpty(message = "健康状况不能为空")
    private String jkzk;

    @ApiModelProperty("身高")
//    @NotNull(message = "身高不能为空")
    private String sg;

    @ApiModelProperty("体重")
//    @NotNull(message = "体重不能为空")
    private String tz;

    @ApiModelProperty("足长")
//    @NotNull(message = "足长不能为空")
    private String zc;

    @ApiModelProperty("体温")
//    @NotNull(message = "体温不能为空")
    private String tw;

    @ApiModelProperty("血型")
//    @NotEmpty(message = "血型不能为空")
    private String xx;

    @ApiModelProperty("血压收缩压")
//    @NotNull(message = "血压收缩压不能为空")
    private String xyssy;

    @ApiModelProperty("血压舒张压")
//    @NotNull(message = "血压舒张压不能为空")
    private String xyszy;

    @ApiModelProperty("心率")
//    @NotNull(message = "心率不能为空")
    private String xl;

    @ApiModelProperty("语言表达能力")
//    @NotEmpty(message = "语言表达能力不能为空")
    private String yybdnl;

    @ApiModelProperty("语言表达能力具体情况")
    private String yybdnljtqk;

    @ApiModelProperty("肢体活动是否正常")
//    @NotEmpty(message = "肢体活动是否正常不能为空")
    private String zthdsfzc;

    @ApiModelProperty("肢体活动具体情况")
    private String zthdjtqk;

    @ApiModelProperty("视力状况是否正常")
//    @NotEmpty(message = "视力状况是否正常不能为空")
    private String slzksfzc;

    @ApiModelProperty("视力具体情况")
    private String sljtqk;

    @ApiModelProperty("听力状况是否正常")
//    @NotEmpty(message = "听力状况是否正常不能为空")
    private String tlzksfzc;

    @ApiModelProperty("听力状况具体情况")
    private String tlzkjtqk;

    @ApiModelProperty("智力状况是否正常")
//    @NotEmpty(message = "智力状况是否正常不能为空")
    private String zlzksfzc;

    @ApiModelProperty("智力状况具体情况")
    private String zlzkjtqk;

    @ApiModelProperty("精神状况是否正常")
//    @NotEmpty(message = "精神状况是否正常不能为空")
    private String jszksfzc;

    @ApiModelProperty("精神状况具体情况")
    private String jszkjtqk;

    @ApiModelProperty("血常规")
    private String xcgscdz;

    @ApiModelProperty("心电图")
    private String xdtscdz;

    @ApiModelProperty("B超")
    private String bcscdz;

    @ApiModelProperty("胸片")
    private String xpscdz;

    @ApiModelProperty("胸部CT")
    private String xbctscdz;

    @ApiModelProperty("女性妊娠检查结果")
    private String nxrzjcg;

    @ApiModelProperty("有无吸毒史")
    private String ywdxs;

    @ApiModelProperty("有无严重传染病")
//    @NotEmpty(message = "有无严重传染病不能为空")
    private String ywyzcbrb;

    @ApiModelProperty("严重传染病名称")
    private String yzcbrbmc;

    @ApiModelProperty("既往病史")
//    @NotEmpty(message = "既往病史不能为空")
    private String jwbs;

    @ApiModelProperty("既往疾病类型")
    private String jwjblx;

    @ApiModelProperty("体表是否有外伤")
//    @NotEmpty(message = "体表是否有外伤不能为空")
    private String tbsfywss;

    @ApiModelProperty("外伤情况记录方式")
    private String wsqkjlfs;

    @ApiModelProperty("外伤情况")
    private String wsqkscdz;

    @ApiModelProperty("致伤部位")
    private String zswb;

    @ApiModelProperty("致伤日期")
    private Date zsrq;

    @ApiModelProperty("致伤原因")
    private String zsyy;

    @ApiModelProperty("医生意见")
//    @NotEmpty(message = "医生意见不能为空")
    private String ysyj;

    @ApiModelProperty("检查人身份证号")
//    @NotEmpty(message = "检查人身份证号不能为空")
    private String jcrsfzh;

    @ApiModelProperty("检查人")
//    @NotEmpty(message = "检查人不能为空")
    private String jcr;

    @ApiModelProperty("检查时间")
//    @NotNull(message = "检查时间不能为空")
    private Date jcsj;

    @ApiModelProperty("备注")
    private String bz;

    @ApiModelProperty("办理状态")
    @NotEmpty(message = "办理状态不能为空")
    private String status;

    @ApiModelProperty("伤情鉴定")
    private String sqjd;

    @ApiModelProperty("伤情鉴定")
    private List<InjuryAssessmentSaveReqVO> injuryAssessmentList;


    @ApiModelProperty("业务类型：看守所:kss;拘留所：jls")
    @NotEmpty(message = "业务类型不能为空")
    private String businessType;

    @ApiModelProperty("入所类型")
    private String rslx;

    @ApiModelProperty("精神状态")
    private String jszt;

}
