package com.rs.module.acp.controller.admin.db.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-收押业务-伤情鉴定列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class InjuryAssessmentListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("人员编号")
    private String rybh;

    @ApiModelProperty("序号")
    private String serialNumber;

    @ApiModelProperty("照片")
    private String zp;

    @ApiModelProperty("描述")
    private String ms;

}
