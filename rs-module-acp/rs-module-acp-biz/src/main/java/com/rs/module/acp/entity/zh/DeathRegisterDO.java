package com.rs.module.acp.entity.zh;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-综合管理-死亡登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_zh_death_register")
@KeySequence("acp_zh_death_register_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_death_register")
public class DeathRegisterDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 死亡地点
     */
    private String deathSite;
    /**
     * 死亡时间
     */
    private Date deathTime;
    /**
     * 现场处理情况
     */
    private String siteHandlingSituation;
    /**
     * 死亡人员姓名
     */
    private String jgryxm;
    /**
     * 死亡人员编号
     */
    private String jgrybm;
    /**
     * 是否需要死亡鉴定,0否、1是
     */
    private Short deathAppraise;
    /**
     * 当前节点 字典 ZD_ZHGL_SWGL
     */
    private String status;
    /**
     * 死亡鉴定经办时间
     */
    private Date appraiseHandleTime;
    /**
     * 死亡鉴定经办人
     */
    private String appraiseHandleUser;
    /**
     * 死亡鉴定经办人名称
     */
    private String appraiseHandleUserName;
    /**
     * 处理情况
     */
    private String handleSituation;
    /**
     * 死亡鉴定单位代码
     */
    private String deathAppraiseUnitCode;
    /**
     * 死亡鉴定单位名称
     */
    private String deathAppraiseUnitName;
    /**
     * 尸体处理经办时间
     */
    private Date corpseHandleHandleTime;
    /**
     * 尸体处理经办人
     */
    private String corpseHandleHandleUser;
    /**
     * 尸体处理经办人名称
     */
    private String corpseHandleHandleUserName;
    /**
     * 尸体处理情况
     */
    private String corpseHandleSituation;
    /**
     * 尸体处理时间
     */
    private Date corpseHandleTime;
    /**
     * 审批人身份证号
     */
    private String approverSfzh;
    /**
     * 审批人姓名
     */
    private String approverXm;
    /**
     * 审批时间
     */
    private Date approverTime;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 领导签名
     */
    private String approvalAutograph;
    /**
     * 领导签名日期
     */
    private Date approvalAutographTime;
    /**
     * 审核意见
     */
    private String approvalComments;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

}
