package com.rs.module.acp.service.pi;

import javax.validation.*;

import com.rs.module.acp.controller.admin.pi.vo.fixedscreenmonitor.FixedScreenMonitorSaveReqVO;
import com.rs.module.acp.entity.pi.FixedScreenMonitorDO;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-巡视管控-定屏监控 Service 接口
 *
 * <AUTHOR>
 */
public interface FixedScreenMonitorService extends IBaseService<FixedScreenMonitorDO>{

    /**
     * 创建实战平台-巡视管控-定屏监控
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createFixedScreenMonitor(@Valid FixedScreenMonitorSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-定屏监控
     *
     * @param updateReqVO 更新信息
     */
    void updateFixedScreenMonitor(@Valid FixedScreenMonitorSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-定屏监控
     *
     * @param id 编号
     */
    void deleteFixedScreenMonitor(String id);

    /**
     * 获得实战平台-巡视管控-定屏监控
     *
     * @param id 编号
     * @return 实战平台-巡视管控-定屏监控
     */
    FixedScreenMonitorDO getFixedScreenMonitor(String id);

}
