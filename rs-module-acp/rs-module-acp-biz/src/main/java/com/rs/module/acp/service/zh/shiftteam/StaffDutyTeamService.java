package com.rs.module.acp.service.zh.shiftteam;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.zh.vo.shiftteam.StaffDutyTeamAndPersonRespVO;
import com.rs.module.acp.controller.admin.zh.vo.shiftteam.StaffDutyTeamAndPersonSaveReqVO;
import com.rs.module.acp.controller.admin.zh.vo.shiftteam.StaffDutyTeamSaveReqVO;
import com.rs.module.acp.entity.zh.StaffDutyTeamDO;
import com.rs.module.base.controller.admin.zh.vo.staffduty.DutyManageVO;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.util.List;

/**
 * 值班模板班组信息 Service 接口
 *
 * <AUTHOR>
 */
public interface StaffDutyTeamService extends IBaseService<StaffDutyTeamDO>{

    @Transactional(rollbackFor = Exception.class)
    String saveStaffDutyTeamAndPerson(StaffDutyTeamAndPersonSaveReqVO saveReqVO);

    /**
     * 创建值班模板班组信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createStaffDutyTeam(@Valid StaffDutyTeamSaveReqVO createReqVO);

    /**
     * 更新值班模板班组信息
     *
     * @param updateReqVO 更新信息
     */
    void updateStaffDutyTeam(@Valid StaffDutyTeamSaveReqVO updateReqVO);

    /**
     * 删除值班模板班组信息
     *
     * @param id 编号
     */
    void deleteStaffDutyTeam(String id);

    /**
     * 获得值班模板班组信息
     *
     * @param id 编号
     * @return 值班模板班组信息
     */
    StaffDutyTeamDO getStaffDutyTeam(String id);

    StaffDutyTeamAndPersonRespVO getStaffDutyTeamAndPerson(String teamId,Integer templateType);

    List<StaffDutyTeamDO> getListByOrgCode(String orgCode,String shiftId);

    DutyManageVO shiftTeamIndex(String orgCode);

    List<StaffDutyTeamAndPersonRespVO> getStaffDutyTeamAndPersonByShiftId(String shiftId, String orgCode, Integer templateType);
}
