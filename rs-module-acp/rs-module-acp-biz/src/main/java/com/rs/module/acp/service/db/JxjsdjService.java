package com.rs.module.acp.service.db;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.db.vo.jxjsdj.*;
import com.rs.module.acp.entity.db.JxjsdjDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-收押业务-减刑登记 Service 接口
 *
 * <AUTHOR>
 */
public interface JxjsdjService extends IBaseService<JxjsdjDO>{

    /**
     * 创建实战平台-收押业务-减刑登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createJxjsdj(@Valid JxjsdjSaveReqVO createReqVO);

    /**
     * 更新实战平台-收押业务-减刑登记
     *
     * @param updateReqVO 更新信息
     */
    void updateJxjsdj(@Valid JxjsdjSaveReqVO updateReqVO);

    /**
     * 删除实战平台-收押业务-减刑登记
     *
     * @param id 编号
     */
    void deleteJxjsdj(String id);

    /**
     * 获得实战平台-收押业务-减刑登记
     *
     * @param id 编号
     * @return 实战平台-收押业务-减刑登记
     */
    JxjsdjRespVO getJxjsdj(String id);

    /**
    * 获得实战平台-收押业务-减刑登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-收押业务-减刑登记分页
    */
    PageResult<JxjsdjDO> getJxjsdjPage(JxjsdjPageReqVO pageReqVO);

    /**
    * 获得实战平台-收押业务-减刑登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-收押业务-减刑登记列表
    */
    List<JxjsdjDO> getJxjsdjList(JxjsdjListReqVO listReqVO);


    String dj(JxjsdjDjReqVO djReqVO);

    void swhyj(JxjsdjSnhyjReqVO snhyjReqVO);

    void lxnlrd(JxjsdjLxnlrdReqVO lxnlrdReqVO);

    void hszxd(JxjsdjHszxdReqVO hszxdReqVO);

    void sngs(JxjsdjSngsReqVO sngsReqVO);

    void swhyjfh(JxjsdjSnhyjfhReqVO snhyjfhReqVO);

    void gajgsc(@Valid JxjsdjGajgscReqVO gajgscReqVO);

    void jcyjd(JxjsdjJcyjdReqVO jcyjdReqVO);

    void fyshcd(JxjsdjFyshcdReqVO fyshcdReqVO);
}
