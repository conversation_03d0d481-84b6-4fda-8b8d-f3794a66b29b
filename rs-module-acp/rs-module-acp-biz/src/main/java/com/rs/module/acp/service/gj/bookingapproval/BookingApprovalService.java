package com.rs.module.acp.service.gj.bookingapproval;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.entity.gj.BookingApprovalDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-管教业务-预约审核管理 Service 接口
 *
 * <AUTHOR>
 */
public interface BookingApprovalService extends IBaseService<BookingApprovalDO>{

    /**
     * 创建实战平台-管教业务-预约审核管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createBookingApproval(@Valid BookingApprovalSaveReqVO createReqVO);


    /**
     * 删除实战平台-管教业务-预约审核管理
     *
     * @param id 编号
     */
    void deleteBookingApproval(String id);

    /**
     * 获得实战平台-管教业务-预约审核管理
     *
     * @param id 编号
     * @return 实战平台-管教业务-预约审核管理
     */
    BookingApprovalDO getBookingApproval(String id);

    /**
    * 获得实战平台-管教业务-预约审核管理分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-预约审核管理分页
    */
    PageResult<BookingApprovalDO> getBookingApprovalPage(BookingApprovalPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-预约审核管理列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-预约审核管理列表
    */
    List<BookingApprovalDO> getBookingApprovalList(BookingApprovalListReqVO listReqVO);

    /**
     * 审批信息
     * @param approveReqVO
     * @return
     */
    Boolean approve(GjApproveReqVO approveReqVO);
}
