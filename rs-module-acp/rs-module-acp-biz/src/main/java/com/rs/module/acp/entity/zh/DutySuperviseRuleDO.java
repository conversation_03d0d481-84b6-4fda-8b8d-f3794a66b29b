package com.rs.module.acp.entity.zh;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalTime;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 综合管理-值班管理-值班督导规则配置 DO
 *
 * <AUTHOR>
 */
@TableName("acp_zh_duty_supervise_rule")
@KeySequence("acp_zh_duty_supervise_rule_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_duty_supervise_rule")
public class DutySuperviseRuleDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 排班模板ID
     */
    private String staffDutyTemplateId;
    /**
     * 值班岗位ID
     */
    private String staffDutyPostId;
    /**
     * 值班模板关联角色ID
     */
    private String staffDutyRoleId;
    /**
     * 签到验证模式
     */
    private String signinValidMode;
    /**
     * 超时阀值
     */
    private Integer timeoutThreshold;
    /**
     * 超时推送对象
     */
    private String timeoutPushTarget;
    /**
     * 超时次数阀值
     */
    private Integer todayTimeoutCount;
    /**
     * 本日超时次数推送对象
     */
    private String todayTimeoutCountPushTarget;
    /**
     * 签到间隔
     */
    private Integer signinInterval;
    /**
     * 签到时段开始
     */
    private LocalTime signinStartTime;
    /**
     * 签到时段结束
     */
    private LocalTime signinEndTime;
    /**
     * 补签时间
     */
    private Integer lateSigninTime;

    @ApiModelProperty("超时推送对象身份证号")
    private String timeoutPushTargetSfzh;

    @ApiModelProperty("本日超时次数推送对象身份证号")
    private String todayTimeoutCountPushTargetSfzh;
}
