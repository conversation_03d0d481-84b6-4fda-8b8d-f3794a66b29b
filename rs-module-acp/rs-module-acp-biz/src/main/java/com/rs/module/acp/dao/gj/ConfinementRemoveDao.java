package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.confinement.ConfinementRemoveListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.confinement.ConfinementRemovePageReqVO;
import com.rs.module.acp.entity.gj.confinement.ConfinementRemoveDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-解除禁闭呈批 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ConfinementRemoveDao extends IBaseDao<ConfinementRemoveDO> {


    default PageResult<ConfinementRemoveDO> selectPage(ConfinementRemovePageReqVO reqVO) {
        Page<ConfinementRemoveDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ConfinementRemoveDO> wrapper = new LambdaQueryWrapperX<ConfinementRemoveDO>()
            .eqIfPresent(ConfinementRemoveDO::getConfinementId, reqVO.getConfinementId())
            .eqIfPresent(ConfinementRemoveDO::getRemoveReason, reqVO.getRemoveReason())
            .eqIfPresent(ConfinementRemoveDO::getStatus, reqVO.getStatus())
            .eqIfPresent(ConfinementRemoveDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(ConfinementRemoveDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(ConfinementRemoveDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(ConfinementRemoveDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(ConfinementRemoveDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(ConfinementRemoveDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(ConfinementRemoveDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(ConfinementRemoveDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(ConfinementRemoveDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(ConfinementRemoveDO::getAddTime);
        }
        Page<ConfinementRemoveDO> confinementRemovePage = selectPage(page, wrapper);
        return new PageResult<>(confinementRemovePage.getRecords(), confinementRemovePage.getTotal());
    }
    default List<ConfinementRemoveDO> selectList(ConfinementRemoveListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ConfinementRemoveDO>()
            .eqIfPresent(ConfinementRemoveDO::getConfinementId, reqVO.getConfinementId())
            .eqIfPresent(ConfinementRemoveDO::getRemoveReason, reqVO.getRemoveReason())
            .eqIfPresent(ConfinementRemoveDO::getStatus, reqVO.getStatus())
            .eqIfPresent(ConfinementRemoveDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(ConfinementRemoveDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(ConfinementRemoveDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(ConfinementRemoveDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(ConfinementRemoveDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(ConfinementRemoveDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(ConfinementRemoveDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(ConfinementRemoveDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(ConfinementRemoveDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(ConfinementRemoveDO::getAddTime));    }


    }
