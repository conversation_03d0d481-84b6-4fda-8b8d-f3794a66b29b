package com.rs.module.acp.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.pm.LeaderInspectionDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 领导巡视 Dao
*
* <AUTHOR>
*/
@Mapper
public interface LeaderInspectionDao extends IBaseDao<LeaderInspectionDO> {


    default PageResult<LeaderInspectionDO> selectPage(LeaderInspectionPageReqVO reqVO) {
        Page<LeaderInspectionDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<LeaderInspectionDO> wrapper = new LambdaQueryWrapperX<LeaderInspectionDO>()
            .eqIfPresent(LeaderInspectionDO::getXsr, reqVO.getXsr())
            .eqIfPresent(LeaderInspectionDO::getXslx, reqVO.getXslx())
            .eqIfPresent(LeaderInspectionDO::getXsddlx, reqVO.getXsddlx())
            .eqIfPresent(LeaderInspectionDO::getXsddCode, reqVO.getXsddCode())
            .likeIfPresent(LeaderInspectionDO::getXsddName, reqVO.getXsddName())
            .eqIfPresent(LeaderInspectionDO::getXsnr, reqVO.getXsnr())
            .eqIfPresent(LeaderInspectionDO::getXskssj, reqVO.getXskssj())
            .eqIfPresent(LeaderInspectionDO::getXsjssj, reqVO.getXsjssj())
            .eqIfPresent(LeaderInspectionDO::getXssffxwt, reqVO.getXssffxwt())
            .eqIfPresent(LeaderInspectionDO::getWttzr, reqVO.getWttzr())
            .likeIfPresent(LeaderInspectionDO::getWttzrName, reqVO.getWttzrName())
            .eqIfPresent(LeaderInspectionDO::getXsjgxx, reqVO.getXsjgxx())
            .eqIfPresent(LeaderInspectionDO::getWtczr, reqVO.getWtczr())
            .likeIfPresent(LeaderInspectionDO::getWtczrName, reqVO.getWtczrName())
            .eqIfPresent(LeaderInspectionDO::getCzsj, reqVO.getCzsj())
            .eqIfPresent(LeaderInspectionDO::getCzqk, reqVO.getCzqk())
            .eqIfPresent(LeaderInspectionDO::getStatus, reqVO.getStatus())
            .eqIfPresent(LeaderInspectionDO::getLdzw, reqVO.getLdzw())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(LeaderInspectionDO::getAddTime);
        }
        Page<LeaderInspectionDO> leaderInspectionPage = selectPage(page, wrapper);
        return new PageResult<>(leaderInspectionPage.getRecords(), leaderInspectionPage.getTotal());
    }
    default List<LeaderInspectionDO> selectList(LeaderInspectionListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<LeaderInspectionDO>()
            .eqIfPresent(LeaderInspectionDO::getXsr, reqVO.getXsr())
            .eqIfPresent(LeaderInspectionDO::getXslx, reqVO.getXslx())
            .eqIfPresent(LeaderInspectionDO::getXsddlx, reqVO.getXsddlx())
            .eqIfPresent(LeaderInspectionDO::getXsddCode, reqVO.getXsddCode())
            .likeIfPresent(LeaderInspectionDO::getXsddName, reqVO.getXsddName())
            .eqIfPresent(LeaderInspectionDO::getXsnr, reqVO.getXsnr())
            .eqIfPresent(LeaderInspectionDO::getXskssj, reqVO.getXskssj())
            .eqIfPresent(LeaderInspectionDO::getXsjssj, reqVO.getXsjssj())
            .eqIfPresent(LeaderInspectionDO::getXssffxwt, reqVO.getXssffxwt())
            .eqIfPresent(LeaderInspectionDO::getWttzr, reqVO.getWttzr())
            .likeIfPresent(LeaderInspectionDO::getWttzrName, reqVO.getWttzrName())
            .eqIfPresent(LeaderInspectionDO::getXsjgxx, reqVO.getXsjgxx())
            .eqIfPresent(LeaderInspectionDO::getWtczr, reqVO.getWtczr())
            .likeIfPresent(LeaderInspectionDO::getWtczrName, reqVO.getWtczrName())
            .eqIfPresent(LeaderInspectionDO::getCzsj, reqVO.getCzsj())
            .eqIfPresent(LeaderInspectionDO::getCzqk, reqVO.getCzqk())
            .eqIfPresent(LeaderInspectionDO::getStatus, reqVO.getStatus())
            .eqIfPresent(LeaderInspectionDO::getLdzw, reqVO.getLdzw())
        .orderByDesc(LeaderInspectionDO::getAddTime));    }


    }
