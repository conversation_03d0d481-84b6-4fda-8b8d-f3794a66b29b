package com.rs.module.acp.service.gj.edurehabcourses;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabActivityListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabActivityPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabActivityRespVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabActivitySaveReqVO;
import com.rs.module.acp.entity.gj.EdurehabActivityDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-教育康复活动 Service 接口
 *
 * <AUTHOR>
 */
public interface EdurehabActivityService extends IBaseService<EdurehabActivityDO>{

    /**
     * 创建实战平台-管教业务-教育康复活动
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createEdurehabActivity(@Valid EdurehabActivitySaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-教育康复活动
     *
     * @param updateReqVO 更新信息
     */
    void updateEdurehabActivity(@Valid EdurehabActivitySaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-教育康复活动
     *
     * @param id 编号
     */
    void deleteEdurehabActivity(String id);

    /**
     * 获得实战平台-管教业务-教育康复活动
     *
     * @param id 编号
     * @return 实战平台-管教业务-教育康复活动
     */
    EdurehabActivityRespVO getEdurehabActivity(String id);

    /**
    * 获得实战平台-管教业务-教育康复活动分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-教育康复活动分页
    */
    PageResult<EdurehabActivityDO> getEdurehabActivityPage(EdurehabActivityPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-教育康复活动列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-教育康复活动列表
    */
    List<EdurehabActivityDO> getEdurehabActivityList(EdurehabActivityListReqVO listReqVO);


    PageResult<EdurehabActivityDO> jykfcjqk(String jgrybm);
}
