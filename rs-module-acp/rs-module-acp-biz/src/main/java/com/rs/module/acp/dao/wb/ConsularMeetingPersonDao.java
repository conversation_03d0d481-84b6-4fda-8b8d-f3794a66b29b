package com.rs.module.acp.dao.wb;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.ConsularMeetingPersonDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-窗口业务-领事会见人员 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ConsularMeetingPersonDao extends IBaseDao<ConsularMeetingPersonDO> {


    default PageResult<ConsularMeetingPersonDO> selectPage(ConsularMeetingPersonPageReqVO reqVO) {
        Page<ConsularMeetingPersonDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ConsularMeetingPersonDO> wrapper = new LambdaQueryWrapperX<ConsularMeetingPersonDO>()
            .eqIfPresent(ConsularMeetingPersonDO::getConsularMeetingId, reqVO.getConsularMeetingId())
            .eqIfPresent(ConsularMeetingPersonDO::getRylx, reqVO.getRylx())
            .eqIfPresent(ConsularMeetingPersonDO::getJh, reqVO.getJh())
            .eqIfPresent(ConsularMeetingPersonDO::getXm, reqVO.getXm())
            .eqIfPresent(ConsularMeetingPersonDO::getZjlx, reqVO.getZjlx())
            .eqIfPresent(ConsularMeetingPersonDO::getZjhm, reqVO.getZjhm())
            .eqIfPresent(ConsularMeetingPersonDO::getBadwdm, reqVO.getBadwdm())
            .eqIfPresent(ConsularMeetingPersonDO::getBadwmc, reqVO.getBadwmc())
            .eqIfPresent(ConsularMeetingPersonDO::getLxfs, reqVO.getLxfs())
            .eqIfPresent(ConsularMeetingPersonDO::getXb, reqVO.getXb())
            .eqIfPresent(ConsularMeetingPersonDO::getZpUrl, reqVO.getZpUrl())
            .eqIfPresent(ConsularMeetingPersonDO::getGzzjUrl, reqVO.getGzzjUrl())
            .eqIfPresent(ConsularMeetingPersonDO::getGj, reqVO.getGj())
            .eqIfPresent(ConsularMeetingPersonDO::getWorkUnit, reqVO.getWorkUnit())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(ConsularMeetingPersonDO::getAddTime);
        }
        Page<ConsularMeetingPersonDO> consularMeetingPersonPage = selectPage(page, wrapper);
        return new PageResult<>(consularMeetingPersonPage.getRecords(), consularMeetingPersonPage.getTotal());
    }
    default List<ConsularMeetingPersonDO> selectList(ConsularMeetingPersonListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ConsularMeetingPersonDO>()
            .eqIfPresent(ConsularMeetingPersonDO::getConsularMeetingId, reqVO.getConsularMeetingId())
            .eqIfPresent(ConsularMeetingPersonDO::getRylx, reqVO.getRylx())
            .eqIfPresent(ConsularMeetingPersonDO::getJh, reqVO.getJh())
            .eqIfPresent(ConsularMeetingPersonDO::getXm, reqVO.getXm())
            .eqIfPresent(ConsularMeetingPersonDO::getZjlx, reqVO.getZjlx())
            .eqIfPresent(ConsularMeetingPersonDO::getZjhm, reqVO.getZjhm())
            .eqIfPresent(ConsularMeetingPersonDO::getBadwdm, reqVO.getBadwdm())
            .eqIfPresent(ConsularMeetingPersonDO::getBadwmc, reqVO.getBadwmc())
            .eqIfPresent(ConsularMeetingPersonDO::getLxfs, reqVO.getLxfs())
            .eqIfPresent(ConsularMeetingPersonDO::getXb, reqVO.getXb())
            .eqIfPresent(ConsularMeetingPersonDO::getZpUrl, reqVO.getZpUrl())
            .eqIfPresent(ConsularMeetingPersonDO::getGzzjUrl, reqVO.getGzzjUrl())
            .eqIfPresent(ConsularMeetingPersonDO::getGj, reqVO.getGj())
            .eqIfPresent(ConsularMeetingPersonDO::getWorkUnit, reqVO.getWorkUnit())
        .orderByDesc(ConsularMeetingPersonDO::getAddTime));    }


    }
