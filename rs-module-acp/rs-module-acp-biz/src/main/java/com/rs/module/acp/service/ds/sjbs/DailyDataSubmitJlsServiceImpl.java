package com.rs.module.acp.service.ds.sjbs;

import com.bsp.common.util.StringUtil;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.DailyDataSubmitJlsRespVO;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.DailyDataSubmitJlsSaveReqVO;
import com.rs.util.DateUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.ds.sjbs.DailyDataSubmitJlsDO;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.ds.sjbs.DailyDataSubmitJlsDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-数据固化-每日数据报送(拘留所) Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DailyDataSubmitJlsServiceImpl extends BaseServiceImpl<DailyDataSubmitJlsDao, DailyDataSubmitJlsDO> implements DailyDataSubmitJlsService {

    @Resource
    private DailyDataSubmitJlsDao dailyDataSubmitJlsDao;

    @Override
    public String createDailyDataSubmitJls(DailyDataSubmitJlsSaveReqVO createReqVO) {
        // 插入
        DailyDataSubmitJlsDO dailyDataSubmitJls = BeanUtils.toBean(createReqVO, DailyDataSubmitJlsDO.class);
        dailyDataSubmitJlsDao.insert(dailyDataSubmitJls);
        // 返回
        return dailyDataSubmitJls.getId();
    }

    @Override
    public void updateDailyDataSubmitJls(DailyDataSubmitJlsSaveReqVO updateReqVO) {
        // 校验存在
        validateDailyDataSubmitJlsExists(updateReqVO.getId());
        // 更新
        DailyDataSubmitJlsDO updateObj = BeanUtils.toBean(updateReqVO, DailyDataSubmitJlsDO.class);
        dailyDataSubmitJlsDao.updateById(updateObj);
    }

    @Override
    public void deleteDailyDataSubmitJls(String id) {
        // 校验存在
        validateDailyDataSubmitJlsExists(id);
        // 删除
        dailyDataSubmitJlsDao.deleteById(id);
    }

    private void validateDailyDataSubmitJlsExists(String id) {
        if (dailyDataSubmitJlsDao.selectById(id) == null) {
            throw new ServerException("实战平台-数据固化-每日数据报送(拘留所)数据不存在");
        }
    }

    @Override
    public DailyDataSubmitJlsDO getDailyDataSubmitJls(String id) {
        return dailyDataSubmitJlsDao.selectById(id);
    }
    @Override
    public void saveForStatistic(String orgCode,String startDate,String endDate){
        String solidificationDate =DateUtil.format(DateUtil.getYesterday(), DateUtil.DATE_PATTERN_OF_BAR);
        if(StringUtil.isEmpty(startDate)) startDate= solidificationDate;
        List<DailyDataSubmitJlsDO> list = dailyDataSubmitJlsDao.statisticNum(orgCode,startDate,endDate);
        //根据solidificationDate=当前时间减1天 查询数据
        dailyDataSubmitJlsDao.deleteByCondition(orgCode,startDate);
        dailyDataSubmitJlsDao.insertBatch( list);
    }

    @Override
    public DailyDataSubmitJlsDO getDailyDataSubmitJlsBySolidificationDate(String solidificationDate,String orgCode) {
        if(StringUtil.isEmpty(solidificationDate)) solidificationDate= DateUtil.format(DateUtil.getYesterday(), DateUtil.DATE_PATTERN_OF_BAR);
        DailyDataSubmitJlsDO entity = dailyDataSubmitJlsDao.selectOne(new LambdaQueryWrapperX<DailyDataSubmitJlsDO>().
                eq(DailyDataSubmitJlsDO::getSolidificationDate, solidificationDate).
                eq(DailyDataSubmitJlsDO::getOrgCode, orgCode).eq(DailyDataSubmitJlsDO::getIsDel, 0));
        return entity;
    }
}
