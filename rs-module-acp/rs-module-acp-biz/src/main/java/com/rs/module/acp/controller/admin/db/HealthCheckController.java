package com.rs.module.acp.controller.admin.db;

import com.rs.module.acp.entity.db.InjuryAssessmentDO;
import com.rs.module.acp.service.db.InjuryAssessmentService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.HealthCheckDO;
import com.rs.module.acp.service.db.HealthCheckService;

@Api(tags = "实战平台-收押业务-入所健康检查登记")
@RestController
@RequestMapping("/acp/db/healthCheck")
@Validated
public class HealthCheckController {

    @Resource
    private HealthCheckService healthCheckService;

    @Resource
    private InjuryAssessmentService injuryAssessmentService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-收押业务-入所健康检查登记")
    public CommonResult<String> createHealthCheck(@Valid @RequestBody HealthCheckSaveReqVO createReqVO) {
        return success(healthCheckService.createHealthCheck(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-收押业务-入所健康检查登记")
    public CommonResult<Boolean> updateHealthCheck(@Valid @RequestBody HealthCheckSaveReqVO updateReqVO) {
        healthCheckService.updateHealthCheck(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-收押业务-入所健康检查登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteHealthCheck(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           healthCheckService.deleteHealthCheck(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-收押业务-入所健康检查登记")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<HealthCheckRespVO> getHealthCheck(@RequestParam("id") String id) {
        HealthCheckDO healthCheck = healthCheckService.getHealthCheck(id);
        if(healthCheck == null){
            return success(new HealthCheckRespVO());
        }

        String rybh = healthCheck.getRybh();
        InjuryAssessmentListReqVO injuryAssessmentListReqVO = new InjuryAssessmentListReqVO();
        injuryAssessmentListReqVO.setRybh(rybh);
        List<InjuryAssessmentDO> injuryAssessmentList = injuryAssessmentService.getInjuryAssessmentList(injuryAssessmentListReqVO);
        List<InjuryAssessmentSaveReqVO> injuryAssessmentRespVOList = BeanUtils.toBean(injuryAssessmentList, InjuryAssessmentSaveReqVO.class);
        HealthCheckRespVO healthCheckRespVO = BeanUtils.toBean(healthCheck, HealthCheckRespVO.class);
        healthCheckRespVO.setInjuryAssessmentList(injuryAssessmentRespVOList);
        return success(healthCheckRespVO);
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-收押业务-入所健康检查登记分页")
    public CommonResult<PageResult<HealthCheckRespVO>> getHealthCheckPage(@Valid @RequestBody HealthCheckPageReqVO pageReqVO) {
        PageResult<HealthCheckDO> pageResult = healthCheckService.getHealthCheckPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, HealthCheckRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-收押业务-入所健康检查登记列表")
    public CommonResult<List<HealthCheckRespVO>> getHealthCheckList(@Valid @RequestBody HealthCheckListReqVO listReqVO) {
        List<HealthCheckDO> list = healthCheckService.getHealthCheckList(listReqVO);
        return success(BeanUtils.toBean(list, HealthCheckRespVO.class));
    }

    /**
     * 通过人员编号获取入所健康检查登记信息
     */
    @GetMapping("/getByRybh")
    @ApiOperation(value = "通过人员编号获取入所健康检查登记信息")
    public CommonResult<HealthCheckRespVO> getHealthCheckByRybh(@RequestParam("rybh") String rybh) {
        HealthCheckDO healthCheck = healthCheckService.getHealthCheckByRybh(rybh);
        if(healthCheck == null){
            return success(new HealthCheckRespVO());
        }

        InjuryAssessmentListReqVO injuryAssessmentListReqVO = new InjuryAssessmentListReqVO();
        injuryAssessmentListReqVO.setRybh(rybh);
        List<InjuryAssessmentDO> injuryAssessmentList = injuryAssessmentService.getInjuryAssessmentList(injuryAssessmentListReqVO);
        List<InjuryAssessmentSaveReqVO> injuryAssessmentRespVOList = BeanUtils.toBean(injuryAssessmentList, InjuryAssessmentSaveReqVO.class);
        HealthCheckRespVO healthCheckRespVO = BeanUtils.toBean(healthCheck, HealthCheckRespVO.class);
        healthCheckRespVO.setInjuryAssessmentList(injuryAssessmentRespVOList);

        return success(healthCheckRespVO);
    }
}
