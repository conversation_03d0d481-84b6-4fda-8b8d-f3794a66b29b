package com.rs.module.acp.controller.admin.gj.vo.face2face;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-面对面配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FaceToFaceConfigPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("检查项目")
    private String checkItem;

    @ApiModelProperty("是否禁用")
    private Short isDisabled;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
