package com.rs.module.acp.entity.db;

import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-收押业务-建议停拘登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_db_sug_stop_detention")
@KeySequence("acp_db_sug_stop_detention_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_db_sug_stop_detention")
public class SugStopDetentionDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 建停类型（01：收拘时，02：收拘后）
     */
    private String jtlx;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 姓名
     */
    private String xm;
    /**
     * 曾用名
     */
    private String cym;
    /**
     * 证件类型
     */
    private String zjlx;
    /**
     * 证件号码
     */
    private String zjhm;
    /**
     * 性别
     */
    private String xb;
    /**
     * 出生日期
     */
    private Date csrq;
    /**
     * 国籍
     */
    private String gj;
    /**
     * 特殊身份
     */
    private String tssf;
    /**
     * 民族
     */
    private String mz;
    /**
     * 政治面貌
     */
    private String zzmm;
    /**
     * 婚姻状况
     */
    private String hyzk;
    /**
     * 文化程度
     */
    private String whcd;
    /**
     * 籍贯
     */
    private String jg;
    /**
     * 身份
     */
    private String sf;
    /**
     * 工作单位
     */
    private String gzdw;
    /**
     * 职业
     */
    private String zy;
    /**
     * 户籍地
     */
    private String hjd;
    /**
     * 户籍地详址
     */
    private String hjdxz;
    /**
     * 现住址
     */
    private String xzz;
    /**
     * 现住址详址
     */
    private String xzzxz;
    /**
     * 决定拘留机关
     */
    private String jdjljg;
    /**
     * 办案单位类型
     */
    private String badwlx;
    /**
     * 办案单位
     */
    private String badw;
    /**
     * 送拘单位
     */
    private String sjdw;
    /**
     * 送拘人
     */
    private String sjr;
    /**
     * 送拘凭证
     */
    private String sjpz;
    /**
     * 送拘法律文书号
     */
    private String sjflwsh;
    /**
     * 联系电话
     */
    private String lxdh;
    /**
     * 案件类别代码
     */
    private String ajlbdm;
    /**
     * 案件类别
     */
    private String ajlb;
    /**
     * 送拘民警
     */
    private String sjmj;
    /**
     * 收拘类型
     */
    private String sjlx;
    /**
     * 拘留时长
     */
    private Integer jlsc;
    /**
     * 拘留起始日期
     */
    private Date jlqsrq;
    /**
     * 拘留截止日期
     */
    private Date jljzrq;
    /**
     * 简要案情
     */
    private String jyaq;
    /**
     * 备注
     */
    private String bz;
    /**
     * 身高
     */
    private Integer sg;
    /**
     * 体重
     */
    private Integer tz;
    /**
     * 足长
     */
    private Integer zc;
    /**
     * 体温
     */
    private BigDecimal tw;
    /**
     * 血压
     */
    private BigDecimal xy;
    /**
     * 脉搏
     */
    private BigDecimal mb;
    /**
     * 心肺
     */
    private String xf;
    /**
     * 心率
     */
    private BigDecimal xl;
    /**
     * 口音
     */
    private String ky;
    /**
     * 血型
     */
    private String xx;
    /**
     * 体型
     */
    private String tx;
    /**
     * 脸型
     */
    private String lx;
    /**
     * 语言表达能力
     */
    private String yybdnl;
    /**
     * 行走状况
     */
    private String xzzk;
    /**
     * 健康状况
     */
    private String jkzk;
    /**
     * 聋哑人
     */
    private String lyr;
    /**
     * 不能自理
     */
    private String bnzl;
    /**
     * 过敏史
     */
    private String gms;
    /**
     * 自述症状
     */
    private String zszzz;
    /**
     * 肺脏
     */
    private String fz;
    /**
     * 腹部
     */
    private String fb;
    /**
     * 体表特殊标志
     */
    private String tbtbsbz;
    /**
     * 既往病史
     */
    private String jwbs;
    /**
     * 有何传染病
     */
    private String yhcrb;
    /**
     * 本人病史
     */
    private String brbs;
    /**
     * 家庭病史
     */
    private String jtbs;
    /**
     * 血常规
     */
    private String xcg;
    /**
     * 心电图
     */
    private String xdt;
    /**
     * B超
     */
    private String bc;
    /**
     * 胸片
     */
    private String xp;
    /**
     * 建议停止执行拘留原因
     */
    private String jytzzxjjlyy;
    /**
     * 医生意见
     */
    private String ysyj;
    /**
     * 诊断证明
     */
    private String zdzm;
    /**
     * 住院治疗证明文件
     */
    private String zyzlzmwj;
    /**
     * 鉴定结论
     */
    private String jdlj;
    /**
     * 其他
     */
    private String qt;
    /**
     * 经办医生身份证号
     */
    private String jbyssfzh;
    /**
     * 经办医生
     */
    private String jbys;
    /**
     * 经办时间
     */
    private Date jbsj;
    /**
     * 办理状态
     */
    private String status;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 文书号
     */
    private String wsh;
    /**
     * 文书字号
     */
    private String wszh;
    /**
     * 经办人身份证号
     */
    private String jbrsfzh;
    /**
     * 经办人
     */
    private String jbr;
    /**
     * 审批人身份证号
     */
    private String sprsfzh;
    /**
     * 审批人
     */
    private String spr;
    /**
     * 医嘱
     */
    private String yz;
    /**
     * 医院诊断结果
     */
    private String yyzdjg;
    private Date rssj;
}
