package com.rs.module.acp.entity.sys;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 实战平台-语音播报-待播报任务 DO
 *
 * <AUTHOR>
 */
@TableName("acp_sys_vb_task")
@KeySequence("acp_sys_vb_task_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_sys_vb_task")
public class VbTaskDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 播报监所Id
     */
    private String vbPrisonId;
    /**
     * 播报监区Id
     */
    private String vbAreaId;
    /**
     * 播报监室Id
     */
    private String vbRoomId;
    /**
     * 设备序列号
     */
    private String vbSerialNumber;    
    /**
     * 播报类型(0:定时,1:实时)
     */
    private Short vbType;
    /**
     * 播报名称
     */
    private String vbName;
    /**
     * 播报内容
     */
    private String content;
    /**
     * 播报次数
     */
    private Short vbNum;
    /**
     * 优先级
     */
    private Short priority;
    /**
     * 播报开始时间
     */
    private Date startTime;
    /**
     * 播报结束时间
     */
    private Date endTime;
    /**
     * 状态(0:待播报,1:已播报,2:超时自动取消)
     */
    private Short status;
    /**
     * 播报时间
     */
    private Date vbTime;
    /**
     * 取消时间
     */
    private Date cancelTime;
    /**
     * 业务主键
     */
    private String businessId;
}
