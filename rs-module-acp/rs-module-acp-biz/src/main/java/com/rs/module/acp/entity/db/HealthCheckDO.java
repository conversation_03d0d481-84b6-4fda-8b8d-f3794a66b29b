package com.rs.module.acp.entity.db;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-收押业务-入所健康检查登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_db_health_check")
@KeySequence("acp_db_health_check_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_db_health_check")
public class HealthCheckDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 人员编号
     */
    private String rybh;
    /**
     * 姓名
     */
    private String name;
    /**
     * 健康状况
     */
    private String jkzk;
    /**
     * 身高
     */
    private String sg;
    /**
     * 体重
     */
    private String tz;
    /**
     * 足长
     */
    private String zc;
    /**
     * 体温
     */
    private String tw;
    /**
     * 血型
     */
    private String xx;
    /**
     * 血压收缩压
     */
    private String xyssy;
    /**
     * 血压舒张压
     */
    private String xyszy;
    /**
     * 心率
     */
    private String xl;
    /**
     * 语言表达能力
     */
    private String yybdnl;
    /**
     * 语言表达能力具体情况
     */
    private String yybdnljtqk;
    /**
     * 肢体活动是否正常
     */
    private String zthdsfzc;
    /**
     * 肢体活动具体情况
     */
    private String zthdjtqk;
    /**
     * 视力状况是否正常
     */
    private String slzksfzc;
    /**
     * 视力具体情况
     */
    private String sljtqk;
    /**
     * 听力状况是否正常
     */
    private String tlzksfzc;
    /**
     * 听力状况具体情况
     */
    private String tlzkjtqk;
    /**
     * 智力状况是否正常
     */
    private String zlzksfzc;
    /**
     * 智力状况具体情况
     */
    private String zlzkjtqk;
    /**
     * 精神状况是否正常
     */
    private String jszksfzc;
    /**
     * 精神状况具体情况
     */
    private String jszkjtqk;
    /**
     * 血常规
     */
    private String xcgscdz;
    /**
     * 心电图
     */
    private String xdtscdz;
    /**
     * B超
     */
    private String bcscdz;
    /**
     * 胸片
     */
    private String xpscdz;
    /**
     * 胸部CT
     */
    private String xbctscdz;
    /**
     * 女性妊娠检查结果
     */
    private String nxrzjcg;
    /**
     * 有无吸毒史
     */
    private String ywdxs;
    /**
     * 有无严重传染病
     */
    private String ywyzcbrb;
    /**
     * 严重传染病名称
     */
    private String yzcbrbmc;
    /**
     * 既往病史
     */
    private String jwbs;
    /**
     * 既往疾病类型
     */
    private String jwjblx;
    /**
     * 体表是否有外伤
     */
    private String tbsfywss;
    /**
     * 外伤情况记录方式
     */
    private String wsqkjlfs;
    /**
     * 外伤情况
     */
    private String wsqkscdz;
    /**
     * 致伤部位
     */
    private String zswb;
    /**
     * 致伤日期
     */
    private Date zsrq;
    /**
     * 致伤原因
     */
    private String zsyy;
    /**
     * 医生意见
     */
    private String ysyj;
    /**
     * 检查人身份证号
     */
    private String jcrsfzh;
    /**
     * 检查人
     */
    private String jcr;
    /**
     * 检查时间
     */
    private Date jcsj;
    /**
     * 备注
     */
    private String bz;
    /**
     * 办理状态
     */
    private String status;

    /**
     * 伤情鉴定
     */
    private String sqjd;

    private String jgrybm;

    @ApiModelProperty("精神状态")
    private String jszt;

}
