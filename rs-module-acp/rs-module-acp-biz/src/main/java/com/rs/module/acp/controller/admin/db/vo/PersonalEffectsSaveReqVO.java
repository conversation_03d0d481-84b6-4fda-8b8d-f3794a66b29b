package com.rs.module.acp.controller.admin.db.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-收押业务-随身物品登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PersonalEffectsSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("人员编号")
    @NotEmpty(message = "人员编号不能为空")
    private String rybh;

    @ApiModelProperty("人员姓名")
    @NotEmpty(message = "人员姓名不能为空")
    private String ryxm;

    @ApiModelProperty("登记状态")
    @NotEmpty(message = "登记状态不能为空")
    private String status;

}
