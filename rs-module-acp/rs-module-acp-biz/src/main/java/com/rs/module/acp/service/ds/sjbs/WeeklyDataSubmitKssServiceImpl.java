package com.rs.module.acp.service.ds.sjbs;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.DateUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyDataSubmitKssSaveReqVO;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyDataSubmitReqVO;
import com.rs.module.acp.entity.ds.sjbs.WeeklyDataSubmitKssDO;
import com.rs.module.acp.util.SjbsDateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.ds.sjbs.WeeklyDataSubmitKssDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;

import java.util.Date;
import java.util.List;


/**
 * 实战平台-数据固化-每周数据报送(看守所) Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WeeklyDataSubmitKssServiceImpl extends BaseServiceImpl<WeeklyDataSubmitKssDao, WeeklyDataSubmitKssDO> implements WeeklyDataSubmitKssService {

    @Resource
    private WeeklyDataSubmitKssDao weeklyDataSubmitKssDao;
    @Autowired
    private WeeklyLawyerTopService weeklyLawyerTopService;
    @Autowired
    private WeeklyPrisonerTopService weeklyPrisonerTopService;

    @Override
    public String createWeeklyDataSubmitKss(WeeklyDataSubmitKssSaveReqVO createReqVO) {
        // 插入
        WeeklyDataSubmitKssDO weeklyDataSubmitKss = BeanUtils.toBean(createReqVO, WeeklyDataSubmitKssDO.class);
        weeklyDataSubmitKssDao.insert(weeklyDataSubmitKss);
        // 返回
        return weeklyDataSubmitKss.getId();
    }

    @Override
    public void updateWeeklyDataSubmitKss(WeeklyDataSubmitKssSaveReqVO updateReqVO) {
        // 校验存在
        validateWeeklyDataSubmitKssExists(updateReqVO.getId());
        // 更新
        WeeklyDataSubmitKssDO updateObj = BeanUtils.toBean(updateReqVO, WeeklyDataSubmitKssDO.class);
        weeklyDataSubmitKssDao.updateById(updateObj);
    }

    @Override
    public void deleteWeeklyDataSubmitKss(String id) {
        // 校验存在
        validateWeeklyDataSubmitKssExists(id);
        // 删除
        weeklyDataSubmitKssDao.deleteById(id);
    }

    private void validateWeeklyDataSubmitKssExists(String id) {
        if (weeklyDataSubmitKssDao.selectById(id) == null) {
            throw new ServerException("实战平台-数据固化-每周数据报送(看守所)数据不存在");
        }
    }

    @Override
    public WeeklyDataSubmitKssDO getWeeklyDataSubmitKss(String id) {
        return weeklyDataSubmitKssDao.selectById(id);
    }

    @Override
    public void saveForStatistic(String orgCode, String startDate, String endDate) {
        String[] range = SjbsDateUtil.getLastWeekRangeStr();
        if(StringUtil.isEmpty(startDate)) startDate = range[0];
        if(StringUtil.isEmpty(endDate)) endDate = range[1];
        List<JSONObject> tjList = weeklyDataSubmitKssDao.statisticNumTj(orgCode, startDate, endDate);
        List<JSONObject> txList = weeklyDataSubmitKssDao.statisticNumTx(orgCode, startDate, endDate);
        List<WeeklyDataSubmitKssDO> meetingInfo = weeklyDataSubmitKssDao.statisticNumLawyerMeeting(orgCode, startDate, endDate);
        for (WeeklyDataSubmitKssDO entity : meetingInfo){
            fixTxData(txList,entity);
            fixTjData(tjList,entity);
            entity.setSolidificationDate(SjbsDateUtil.getYearWeekStr(startDate, endDate));
            entity.setEndDate(endDate);
            entity.setStartDate(startDate);
            entity.setId(StringUtil.getGuid());
        }
        List<WeeklyDataSubmitReqVO> weeklyDataSubmitList = BeanUtils.toBean(meetingInfo, WeeklyDataSubmitReqVO.class);
        weeklyDataSubmitKssDao.deleteByCondition(orgCode, startDate, endDate);
        weeklyDataSubmitKssDao.insertBatch(meetingInfo);
        weeklyLawyerTopService.saveForStatistic(weeklyDataSubmitList);
        weeklyPrisonerTopService.saveForStatistic(weeklyDataSubmitList);
    }
    private void fixTxData(List<JSONObject> txList, WeeklyDataSubmitKssDO entity){
        if(CollectionUtil.isNotNull(txList)){
            int qtCount = 0;
            int totalCount = 0;
            for (JSONObject jsonObject : txList){
                if(!entity.getOrgCode().equals(jsonObject.getString("orgcode"))) continue;
                String tjjglx = jsonObject.getString("tjjglx");
                Integer count = jsonObject.getInteger("count");
                switch (tjjglx){
                    case "1":
                        entity.setTxGajg(count);
                        break;
                    case "2":
                        entity.setTxJcy(count);
                        break;
                    case "3":
                        entity.setTxFy(count);
                        break;
                    case "4":
                    case "9":
                        qtCount += count;
                        break;
                }
                totalCount += count;
            }
            entity.setTxQtdw(qtCount);
            entity.setTx(totalCount);

        }
    }
    private void fixTjData(List<JSONObject> tjList, WeeklyDataSubmitKssDO entity){
        if(CollectionUtil.isNotNull(tjList)){
            int qtCount = 0;
            int totalCount = 0;
            for (JSONObject jsonObject : tjList){
                if(!entity.getOrgCode().equals(jsonObject.getString("orgcode"))) continue;
                String tjjglx = jsonObject.getString("tjjglx");
                Integer count = jsonObject.getInteger("count");
                switch (tjjglx){
                    case "1":
                        entity.setTjGajg(count);
                        break;
                    case "2":
                        entity.setTjJcy(count);
                        break;
                    case "3":
                        entity.setTjFy(count);
                        break;
                    case "4":
                    case "9":
                        qtCount += count;
                        break;
                }
                totalCount += count;
            }
            entity.setTjQtdw(qtCount);
            entity.setTj(totalCount);
        }
    }
    @Override
    public List<WeeklyDataSubmitKssDO> getWeeklyDataSubmitKssByDate(String startDate, String endDate, String orgCode) {
        String[] range = SjbsDateUtil.getLastWeekRangeStr();
        if(null == startDate) startDate = range[0];
        if(null == endDate) endDate = range[1];
        if(StringUtil.isEmpty(orgCode)) orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        List<WeeklyDataSubmitKssDO> list = weeklyDataSubmitKssDao.selectList(new LambdaQueryWrapperX<WeeklyDataSubmitKssDO>().
                eq(WeeklyDataSubmitKssDO::getStartDate, startDate).eq(WeeklyDataSubmitKssDO::getEndDate, endDate).
                eq(WeeklyDataSubmitKssDO::getOrgCode, orgCode).eq(WeeklyDataSubmitKssDO::getIsDel, 0));
        return list;
    }



}
