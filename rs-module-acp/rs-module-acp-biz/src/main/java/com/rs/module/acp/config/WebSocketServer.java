package com.rs.module.acp.config;

import com.rs.framework.mybatis.processor.FormatProcessor;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * WebSocketServer 类实现了 WebSocket 服务端的功能。
 * 它负责处理 WebSocket 连接的建立、关闭、消息接收和发送等操作。
 */
@Component
@ServerEndpoint("/websocket/{userId}")
public class WebSocketServer {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketServer.class);


    // 静态变量，用于记录当前在线连接数
    private static int onlineCount = 0;

    // 存储所有连接的 WebSocketServer 实例
    @Getter
    private static final CopyOnWriteArraySet<WebSocketServer> webSocketSet = new CopyOnWriteArraySet<>();

    // 当前连接的会话对象
    private Session session;

    // 客户端唯一标识符
    private String userId = "";

    /**
     * 连接建立成功时调用的方法。
     *
     * @param session 当前连接的会话对象
     * @param userId 客户端唯一标识符
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("userId") String userId) {
        this.session = session;
        webSocketSet.add(this);     // 将当前实例加入集合
        this.userId = userId;
        addOnlineCount();           // 在线数加1
        try {
            sendMessage("WebSocket 连接成功");  // 发送连接成功的消息
            logger.info("有新窗口开始监听:{},当前在线人数为:{}", userId, getOnlineCount());
        } catch (IOException e) {
            logger.error("websocket IO Exception");
        }
    }

    /**
     * 连接关闭时调用的方法。
     */
    @OnClose
    public void onClose() {
        webSocketSet.remove(this);  // 从集合中移除当前实例
        subOnlineCount();           // 在线数减1
        logger.info("释放的sid为：{}", userId);
        logger.info("有一个连接关闭！当前在线人数为{}", getOnlineCount());
    }

    /**
     * 接收到客户端消息时调用的方法。
     *
     * @param message 客户端发送的消息
     * @param session 当前连接的会话对象
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        logger.info("收到来自窗口{}的信息:{}", userId, message);
        // 群发消息
//        for (WebSocketServer item : webSocketSet) {
//            if (Objects.equals(item.userId, this.userId)) {
//                continue;
//            }
//            sendMessageToClient(item, message);
//        }
    }

    /**
     * 实现服务器主动推送消息的方法，并统一处理异常。
     *
     * @param client 要推送的客户端实例
     * @param message 要推送的消息
     */
    private void sendMessageToClient(WebSocketServer client, String message) {
        try {
            client.sendMessage(message);
        } catch (IOException e) {
            logger.error("向客户端 {} 发送消息时出错: {}", client.userId, message, e);
        }
    }

    /**
     * 群发自定义消息给指定的客户端。
     *
     * @param message 要发送的消息
     * @param userId 客户端唯一标识符，为 null 时发送给所有客户端
     * @throws IOException 如果发送消息时发生 I/O 错误
     */
    public static void sendInfo(String message, @PathParam("userId") String userId) throws IOException {
        logger.info("推送消息到窗口" + userId + "，推送内容:" + message);

        for (WebSocketServer item : webSocketSet) {
            try {
                if (userId == null) {
                    // 如果 sid 为 null，则发送给所有客户端
                    item.sendMessage(message);
                } else if (item.userId.equals(userId)) {
                    // 如果 sid 匹配，则只发送给该客户端
                    item.sendMessage(message);
                }
            } catch (IOException e) {
                logger.error("向客户端 {} 发送消息时出错: {}", item.userId, message, e);
            }
        }
    }

    /**
     * 发生错误时调用的方法。
     *
     * @param session 当前连接的会话对象
     * @param error 发生的错误
     */
    @OnError
    public void onError(Session session, Throwable error) {
        logger.error("发生错误");
        error.printStackTrace();
    }

    /**
     * 实现服务器主动推送消息的方法。
     *
     * @param message 要推送的消息
     * @throws IOException 如果发送消息时发生 I/O 错误
     */
    public void sendMessage(String message) throws IOException {
        this.session.getBasicRemote().sendText(message);
    }

    /**
     * 获取当前在线连接数。
     *
     * @return 当前在线连接数
     */
    public static synchronized int getOnlineCount() {
        return onlineCount;
    }

    /**
     * 增加在线连接数。
     */
    public static synchronized void addOnlineCount() {
        WebSocketServer.onlineCount++;
    }

    /**
     * 减少在线连接数。
     */
    public static synchronized void subOnlineCount() {
        WebSocketServer.onlineCount--;
    }
}
