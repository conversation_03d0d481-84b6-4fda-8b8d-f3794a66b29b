package com.rs.module.acp.service.gj.violationappeal;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.violationappeal.*;
import com.rs.module.acp.entity.gj.ViolationAppealDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-违规申诉 Service 接口
 *
 * <AUTHOR>
 */
public interface ViolationAppealService extends IBaseService<ViolationAppealDO>{

    /**
     * 创建实战平台-管教业务-违规申诉
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createViolationAppeal(@Valid ViolationAppealSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-违规申诉
     *
     * @param updateReqVO 更新信息
     */
    void updateViolationAppeal(@Valid ViolationAppealSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-违规申诉
     *
     * @param id 编号
     */
    void deleteViolationAppeal(String id);

    /**
     * 获得实战平台-管教业务-违规申诉
     *
     * @param id 编号
     * @return 实战平台-管教业务-违规申诉
     */
    ViolationAppealDO getViolationAppeal(String id);

    /**
    * 获得实战平台-管教业务-违规申诉分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-违规申诉分页
    */
    PageResult<ViolationAppealDO> getViolationAppealPage(ViolationAppealPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-违规申诉列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-违规申诉列表
    */
    List<ViolationAppealDO> getViolationAppealList(ViolationAppealListReqVO listReqVO);


    String appeal(ViolationAppealAppCreateReqVO createReqVO);

    List<ViolationAppealDO> getListByIds(List<String> idList);

    void approval(ViolationAppealApprovalReqVO appealApprovalReqVO);

    PageResult<ViolationAppealDO> applyList(int pageNo, int pageSize, String jgrybm, String type);

    PageResult<ViolationAppealDO> applyRecord(int pageNo, int pageSize, String jgrybm, String type);
}
