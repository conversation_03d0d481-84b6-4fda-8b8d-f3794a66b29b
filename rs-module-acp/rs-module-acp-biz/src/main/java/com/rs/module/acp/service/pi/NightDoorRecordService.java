package com.rs.module.acp.service.pi;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.pi.vo.NightDoorRecordSaveReqVO;
import com.rs.module.acp.entity.pi.NightDoorRecordDO;

import javax.validation.Valid;

/**
 * 实战平台-巡视管控-夜间开启监室门 Service 接口
 *
 * <AUTHOR>
 */
public interface NightDoorRecordService extends IBaseService<NightDoorRecordDO>{

    /**
     * 创建实战平台-巡视管控-夜间开启监室门
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createNightDoorRecord(@Valid NightDoorRecordSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-夜间开启监室门
     *
     * @param updateReqVO 更新信息
     */
    String updateNightDoorRecord(@Valid NightDoorRecordSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-夜间开启监室门
     *
     * @param id 编号
     */
    void deleteNightDoorRecord(String id);

    /**
     * 获得实战平台-巡视管控-夜间开启监室门
     *
     * @param id 编号
     * @return 实战平台-巡视管控-夜间开启监室门
     */
    NightDoorRecordDO getNightDoorRecord(String id);

}
