package com.rs.module.acp.controller.admin.gj.vo.equipment;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 提前解除申请
 * <AUTHOR>
 * @date 2025/6/5 15:25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EquipmentUseRemoveApplyReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("解除理由")
    @NotBlank(message = "解除理由不能为空！")
    private String removeReason;

    //@ApiModelProperty("解除日期")
    //private Date removeTime;

    @ApiModelProperty("解除执行情况")
    private String removeExecuteSituation;

    @ApiModelProperty("是否提前解除")
    private Integer isRemove;

    //@ApiModelProperty("状态")
    //@NotEmpty(message = "状态不能为空")
    //private String status;


    //@ApiModelProperty("审批人身份证号")
    //private String approverSfzh;
    //
    //@ApiModelProperty("审批人姓名")
    //private String approverXm;
    //
    //@ApiModelProperty("审批时间")
    //private Date approverTime;
    //
    //@ApiModelProperty("审批结果")
    //private String approvalResult;
    //
    //@ApiModelProperty("审批人签名")
    //private String approvalAutograph;
    //
    //@ApiModelProperty("审批人签名日期")
    //private Date approvalAutographTime;
    //
    //@ApiModelProperty("审核意见")
    //private String approvalComments;
    //
    //@ApiModelProperty("ACT流程实例Id")
    //private String actInstId;
    //
    //@ApiModelProperty("任务ID")
    //private String taskId;

}
