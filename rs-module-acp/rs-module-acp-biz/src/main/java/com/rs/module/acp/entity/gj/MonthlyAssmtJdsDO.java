package com.rs.module.acp.entity.gj;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-月度考核(戒毒所) DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_monthly_assmt_jds")
@KeySequence("acp_gj_monthly_assmt_jds_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_monthly_assmt_jds")
public class MonthlyAssmtJdsDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 推送时间
     */
    private Date pushTime;
    /**
     * 考核内容值1
     */
    private String assmtContentValue1;
    /**
     * 考核内容值2
     */
    private String assmtContentValue2;
    /**
     * 考核内容值3
     */
    private String assmtContentValue3;
    /**
     * 考核内容值4
     */
    private String assmtContentValue4;
    /**
     * 考核内容值5
     */
    private String assmtContentValue5;
    /**
     * 考核内容值6
     */
    private String assmtContentValue6;
    /**
     * 考核内容值7
     */
    private String assmtContentValue7;
    /**
     * 考核内容值8
     */
    private String assmtContentValue8;
    /**
     * 考核内容值9
     */
    private String assmtContentValue9;
    /**
     * 考核内容值10
     */
    private String assmtContentValue10;
    /**
     * 考核内容值10
     */
    private String assmtContentTotalScore;
    /**
     * 加分或扣分情况
     */
    private String bonusOrPenaltyPointsSituati;
    /**
     * 评估内容JSON
     */
    private String asstmContentJson;
    /**
     * 评估人身份证号
     */
    private String assmtUserSfzh;
    /**
     * 评估人姓名
     */
    private String assmtUser;
    /**
     * 评估时间
     */
    private String assmtTime;
    /**
     * 办理状态
     */
    private String status;
    /**
     * 确认时间
     */
    private Date confirmTime;
    /**
     * 在押人员签名地址
     */
    private String prisonerSignature;
    /**
     * 月度考核周期 yyyy-MM
     */
    private String monthPeriod;
    /**
     * 依据
     */
    private String according;
}
