package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.ArraignmentDO;
import com.rs.module.acp.entity.wb.BringInterrogationDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-提询登记 Service 接口
 *
 * <AUTHOR>
 */
public interface BringInterrogationService extends IBaseService<BringInterrogationDO>{

    /**
     * 创建实战平台-窗口业务-提询登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createBringInterrogation(@Valid BringInterrogationSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-提询登记
     *
     * @param updateReqVO 更新信息
     */
    void updateBringInterrogation(@Valid BringInterrogationSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-提询登记
     *
     * @param id 编号
     */
    void deleteBringInterrogation(String id);

    /**
     * 获得实战平台-窗口业务-提询登记
     *
     * @param id 编号
     * @return 实战平台-窗口业务-提询登记
     */
    BringInterrogationDO getBringInterrogation(String id);

    /**
     * 获得实战平台-窗口业务-提询登记分页
     *
     * @param pageReqVO 分页查询
     * @return 实战平台-窗口业务-提询登记分页
     */
    PageResult<BringInterrogationDO> getBringInterrogationPage(BringInterrogationPageReqVO pageReqVO);

    /**
     * 获得实战平台-窗口业务-提询登记列表
     *
     * @param listReqVO 查询条件
     * @return 实战平台-窗口业务-提询登记列表
     */
    List<BringInterrogationDO> getBringInterrogationList(BringInterrogationListReqVO listReqVO);

    /**
     * 获得实战平台-窗口业务-提询登记签到
     *
     * @param id 业务ID
     * @param checkInTime 签到时间
     */
    boolean signIn(String id,String checkInTime);

    /**
     * 获得实战平台-窗口业务-带出安检
     *
     * @param updateReqVO
     */
    boolean escortingInspect(BringInterrogationSaveReqVO updateReqVO);

    /**
     * 获得实战平台-窗口业务-会毕安检
     *
     * @param updateReqVO
     */
    boolean returnInspection(BringInterrogationSaveReqVO updateReqVO);
    /**
     * 获得实战平台-窗口业务-补录
     *
     * @param updateReqVO
     */
    boolean additionalRecording(BringInterrogationSaveReqVO updateReqVO);

    /**
     * 获得实战平台-窗口业务-提询登记
     *
     * @param id 编号
     * @return 实战平台-窗口业务-提询登记
     */
    BringInterrogationRespVO getBringInterrogationById(String id);

    /**
     * 获得实战平台-窗口业务-获取历史提询记录
     *
     * @param jgrybm 编号
     * @param pageNo 页码
     * @param pageSize 每页多少
     * @return 实战平台-窗口业务-获取历史提询记录
     */
    PageResult<BringInterrogationRespVO> getHistoryBringInterrogation(String jgrybm,int pageNo,int pageSize);

    /**
     * 获得实战平台-窗口业务-分配审讯室
     *
     * @param id 编号
     * @param roomId 审讯室ID
     * @return 实战平台-窗口业务-提询登记
     */
    boolean allocationRoom(String id,String roomId);


}
