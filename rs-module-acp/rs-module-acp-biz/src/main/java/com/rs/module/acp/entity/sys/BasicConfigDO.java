package com.rs.module.acp.entity.sys;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-系统基础配置 DO
 *
 * <AUTHOR>
 */
@TableName("acp_sys_basic_config")
@KeySequence("acp_sys_basic_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BasicConfigDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 系统名字
     */
    private String systemName;
    /**
     * 系统名字缩写
     */
    private String systemNameShort;
    /**
     * 系统上线日期
     */
    private Date onlineDate;
    /**
     * 登录页logo图片
     */
    private String logoLoginUrl;
    /**
     * 系统名称图片
     */
    private String systemNameUrl;
    /**
     * 版权信息
     */
    private String versionInfo;
    /**
     * 系统说明
     */
    private String systemInfo;
    /**
     * 登录页logo图片图片名字
     */
    private String logoLoginUrlName;
    /**
     * 系统名称图片名字
     */
    private String systemNameUrlName;
    /**
     * 1-pki 0-默认
     */
    private String loginType;
    /**
     * 公匙
     */
    private String publicKey;
    /**
     * 背景图
     */
    private String backgroundImage;
    /**
     * 登录页效果 1：警务蓝（默认） 2：科技蓝
     */
    private String loginPageEffect;
    /**
     * 是否展示插件列表 0：否 1：是
     */
    private String isShowPlugin;
    /**
     * 是否展示浏览器列表 0：否 1：是
     */
    private String isShowBrowser;
    /**
     * 是否展示超链接列表 0：否 1：是
     */
    private String isShowHyperlink;
    /**
     * base_system_info_url
     */
    private String baseSystemInfoUrl;

}
