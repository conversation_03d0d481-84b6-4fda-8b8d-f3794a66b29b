package com.rs.module.acp.entity.db;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-羁押业务-出所登记（看守所） DO
 *
 * <AUTHOR>
 */
@TableName("acp_db_out_record_kss")
@KeySequence("acp_db_out_record_kss_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_db_out_record_kss")
public class OutRecordKssDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 批准/执行机关
     */
    private String pzzxjg;
    /**
     * 批准/执行人
     */
    private String pzzxr;
    /**
     * 批准日期
     */
    private Date pzrq;
    /**
     * 出所时间
     */
    private Date cssj;
    /**
     * 出所原因
     */
    private String csyy;
    /**
     * 出所去向
     */
    private String csqx;
    /**
     * 出所凭证
     */
    private String cspz;
    /**
     * 出所凭证文书号
     */
    private String cspzwsh;
    /**
     * 出所凭证文书号
     */
    private String cspzwsdz;
    /**
     * 财物交接情况
     */
    private String cwjjqk;
    /**
     * 财物交接实体
     */
    private String cwjjst;
    /**
     * 档案材料移交情况
     */
    private String daclyjqk;
    /**
     * 档案材料移交实体
     */
    private String daclyjst;
    /**
     * 是否检查发现书信或者物品
     */
    private Short sfjcfxshhzwp;
    /**
     * 检查发现书信或者物品情况记录
     */
    private String jcfxsxhzwpqkjl;
    /**
     * 书信或者物品照片
     */
    private String sxhzwpzp;
    /**
     * 是否为他人捎带物品
     */
    private Short sfwtrsdwp;
    /**
     * 捎带物品类型
     */
    private String sdwplx;
    /**
     * 是否有帮其他人串供行为
     */
    private Short sfybzqtrcgxw;
    /**
     * 详细情况记录
     */
    private String xxqkjl;
    /**
     * 是否发现其他违法犯罪行为
     */
    private Short sffxqtwffzxs;
    /**
     * 其他违法犯罪行为
     */
    private String qtwffzxw;
    /**
     * 通报办案机关及办案机关回复情况
     */
    private String tbbajgjbajghfqk;
    /**
     * 转去公安监所编码
     */
    private String zqgajsbm;
    /**
     * 转去公安监所名称
     */
    private String zqgajsmc;
    /**
     * 经办人身份证号
     */
    private String jbrsfzh;
    /**
     * 经办人
     */
    private String jbr;
    /**
     * 经办时间
     */
    private Date jbsj;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 办理状态
     */
    private String spzt;
    /**
     * 办理状态
     */
    private String status;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 手环ID
     */
    private String shid;
    /**
     * 手环绑定状态
     */
    private String shbdzt;
    /**
     * 手环解除绑定时间
     */
    private Date sdjcbdsj;
    /**
     * 离所确认状态
     */
    private String lsqrzt;
    /**
     * 离所确认去向
     */
    private String lsqrqx;
    /**
     * 离所确认备注
     */
    private String lsqrbz;
    /**
     * 离所确认经办人身份证号
     */
    private String lsqrjbrsfzh;
    /**
     * 离所确认经办人身份证号
     */
    private String lsqrjbr;
    /**
     * 离所确认经办人身份证号
     */
    private Date lsqrjbsj;

    /**
     * 流程阶段
     */
    private String currentStep;

}
