package com.rs.module.acp.service.db;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.InRecordJlsDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-收押业务-入所登记（拘留所） Service 接口
 *
 * <AUTHOR>
 */
public interface InRecordJlsService extends IBaseService<InRecordJlsDO>{

    /**
     * 创建实战平台-收押业务-入所登记（拘留所）
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createInRecordJls(@Valid InRecordJlsSaveReqVO createReqVO);

    /**
     * 更新实战平台-收押业务-入所登记（拘留所）
     *
     * @param updateReqVO 更新信息
     */
    void updateInRecordJls(@Valid InRecordJlsSaveReqVO updateReqVO);

    /**
     * 删除实战平台-收押业务-入所登记（拘留所）
     *
     * @param id 编号
     */
    void deleteInRecordJls(String id);

    /**
     * 获得实战平台-收押业务-入所登记（拘留所）
     *
     * @param id 编号
     * @return 实战平台-收押业务-入所登记（拘留所）
     */
    InRecordJlsDO getInRecordJls(String id);

    /**
    * 获得实战平台-收押业务-入所登记（拘留所）分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-收押业务-入所登记（拘留所）分页
    */
    PageResult<InRecordJlsDO> getInRecordJlsPage(InRecordJlsPageReqVO pageReqVO);

    /**
    * 获得实战平台-收押业务-入所登记（拘留所）列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-收押业务-入所登记（拘留所）列表
    */
    List<InRecordJlsDO> getInRecordJlsList(InRecordJlsListReqVO listReqVO);


    CombineRespVO getCombineInfo(String rybh);

    void updateLeaderApprovalStatus(LeaderApprovalStatusReqVO updateReqVO);

    InRecordJlsDO getPrisonerInfo(String rybh);

    String ifExists(InRecordJlsSaveReqVO updateReqVO);

    InRecordStatusVO getInRecordStatus(String rybh);
}
