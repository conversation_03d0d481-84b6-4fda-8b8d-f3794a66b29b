package com.rs.module.acp.service.db;

import com.rs.module.acp.dao.db.TransferPrisonerDao;
import com.rs.module.acp.entity.db.TransferPrisonerDO;
import com.rs.module.acp.entity.db.TransferPrisonerInfoDO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.TransferRecordDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.db.TransferRecordDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-羁押业务-转所登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TransferRecordServiceImpl extends BaseServiceImpl<TransferRecordDao, TransferRecordDO> implements TransferRecordService {

    @Resource
    private TransferRecordDao transferRecordDao;

    @Resource
    private TransferPrisonerDao transferPrisonerDao;

    @Override
    public String createTransferRecord(TransferRecordSaveReqVO createReqVO) {
        // 插入
        TransferRecordDO transferRecord = BeanUtils.toBean(createReqVO, TransferRecordDO.class);
        transferRecord.setId(UUID.randomUUID().toString().replaceAll("-", ""));
        transferRecordDao.insert(transferRecord);

        //存储人员列表
        List<TransferPrisonersVO> transferPrisoners = createReqVO.getTransferPrisoners();
        for (TransferPrisonersVO transferPrisonersVO : transferPrisoners) {
            TransferPrisonerDO transferPrisoner = BeanUtils.toBean(transferPrisonersVO, TransferPrisonerDO.class);
            transferPrisoner.setId(UUID.randomUUID().toString().replaceAll("-", ""));
            transferPrisoner.setTransferRecordId(transferRecord.getId());
            transferPrisonerDao.insert(transferPrisoner);
        }

        // 返回
        return transferRecord.getId();
    }
   /* private boolean validPersonHasTrans(){
        boolean hasTrans = transferPrisonerDao.selectCount(new TransferPrisonerDO()) > 0;
        return hasTrans;
    }*/
    @Override
    public void updateTransferRecord(TransferRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateTransferRecordExists(updateReqVO.getId());
        // 更新
        TransferRecordDO updateObj = BeanUtils.toBean(updateReqVO, TransferRecordDO.class);
        transferRecordDao.updateById(updateObj);
    }

    @Override
    public void deleteTransferRecord(String id) {
        // 校验存在
        validateTransferRecordExists(id);
        // 删除
        transferRecordDao.deleteById(id);
    }

    private void validateTransferRecordExists(String id) {
        if (transferRecordDao.selectById(id) == null) {
            throw new ServerException("实战平台-羁押业务-转所登记数据不存在");
        }
    }

    @Override
    public TransferRecordDO getTransferRecord(String id) {
        return transferRecordDao.selectById(id);
    }

    @Override
    public PageResult<TransferRecordDO> getTransferRecordPage(TransferRecordPageReqVO pageReqVO) {
        return transferRecordDao.selectPage(pageReqVO);
    }

    @Override
    public List<TransferRecordDO> getTransferRecordList(TransferRecordListReqVO listReqVO) {
        return transferRecordDao.selectList(listReqVO);
    }

    @Override
    public List<TransferPrisonerInfoDO> getPrisonersByTransferRecordId(String id) {
        List<TransferPrisonerInfoDO> transferPrisonerInfoDOList = transferRecordDao.getPrisonersByTransferRecordId(id);
        return transferPrisonerInfoDOList;
    }


}
