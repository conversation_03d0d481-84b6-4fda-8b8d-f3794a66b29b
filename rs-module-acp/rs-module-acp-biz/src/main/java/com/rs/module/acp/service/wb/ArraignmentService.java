package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.ArraignmentDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-提讯登记 Service 接口
 *
 * <AUTHOR>
 */
public interface ArraignmentService extends IBaseService<ArraignmentDO>{

    /**
     * 创建实战平台-窗口业务-提讯登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createArraignment(@Valid ArraignmentSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-提讯登记
     *
     * @param updateReqVO 更新信息
     */
    void updateArraignment(@Valid ArraignmentSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-提讯登记
     *
     * @param id 编号
     */
    void deleteArraignment(String id);

    /**
     * 获得实战平台-窗口业务-提讯登记
     *
     * @param id 编号
     * @return 实战平台-窗口业务-提讯登记
     */
    ArraignmentDO getArraignment(String id);

    /**
    * 获得实战平台-窗口业务-提讯登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-提讯登记分页
    */
    PageResult<ArraignmentDO> getArraignmentPage(ArraignmentPageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-提讯登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-提讯登记列表
    */
    List<ArraignmentDO> getArraignmentList(ArraignmentListReqVO listReqVO);

    /**
     * 获得实战平台-窗口业务-提讯登记签到
     *
     * @param id 业务ID
     * @param checkInTime 签到时间
     */
    boolean signIn(String id,String checkInTime);

    /**
     * 获得实战平台-窗口业务-带出安检
     *
     * @param updateReqVO
     */
    boolean escortingInspect(ArraignmentSaveReqVO updateReqVO);

    /**
     * 获得实战平台-窗口业务-会毕安检
     *
     * @param updateReqVO
     */
    boolean returnInspection(ArraignmentSaveReqVO updateReqVO);
    /**
     * 获得实战平台-窗口业务-补录
     *
     * @param updateReqVO
     */
    boolean additionalRecording(ArraignmentSaveReqVO updateReqVO);

    /**
     * 获得实战平台-窗口业务-提讯登记
     *
     * @param id 编号
     * @return 实战平台-窗口业务-提讯登记
     */
    ArraignmentRespVO getArraignmentById(String id);

    /**
     * 获得实战平台-窗口业务-获取历史提讯记录
     *
     * @param jgrybm 编号
     * @param pageNo 页码
     * @param pageSize 每页多少
     * @return 实战平台-窗口业务-获取历史提讯记录
     */
    PageResult<ArraignmentRespVO> getHistoryArraignment(String jgrybm,int pageNo,int pageSize);

    /**
     * 获得实战平台-窗口业务-分配审讯室
     *
     * @param id 编号
     * @param roomId 审讯室ID
     * @return 实战平台-窗口业务-提讯登记
     */
    boolean allocationRoom(String id,String roomId);

}
