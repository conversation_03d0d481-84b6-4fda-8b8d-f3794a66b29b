package com.rs.module.acp.controller.admin.db.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "看守所收押登记其他信息VO")
@Data
public class DetainRegKssRestVO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("涉毒尿检初查结果")
    private String sdnjccjg;

    @ApiModelProperty("涉毒尿检单位")
    private String sdnjdw;

    @ApiModelProperty("涉毒尿检初查时间")
    private Date sdnjccsj;

    @ApiModelProperty("涉毒尿检检查人")
    private String sdnjjcr;

    @ApiModelProperty("备注")
    private String bz;
}
