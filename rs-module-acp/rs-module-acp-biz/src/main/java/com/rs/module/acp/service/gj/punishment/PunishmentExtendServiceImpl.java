package com.rs.module.acp.service.gj.punishment;

import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentExtendListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentExtendPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentExtendSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.PunishmentExtendDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.PunishmentExtendDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务延长处罚呈批 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PunishmentExtendServiceImpl extends BaseServiceImpl<PunishmentExtendDao, PunishmentExtendDO> implements PunishmentExtendService {

    @Resource
    private PunishmentExtendDao punishmentExtendDao;

    @Override
    public String createPunishmentExtend(PunishmentExtendSaveReqVO createReqVO) {
        // 插入
        PunishmentExtendDO punishmentExtend = BeanUtils.toBean(createReqVO, PunishmentExtendDO.class);
        punishmentExtendDao.insert(punishmentExtend);
        // 返回
        return punishmentExtend.getId();
    }

    @Override
    public void updatePunishmentExtend(PunishmentExtendSaveReqVO updateReqVO) {
        // 校验存在
        validatePunishmentExtendExists(updateReqVO.getId());
        // 更新
        PunishmentExtendDO updateObj = BeanUtils.toBean(updateReqVO, PunishmentExtendDO.class);
        punishmentExtendDao.updateById(updateObj);
    }

    @Override
    public void deletePunishmentExtend(String id) {
        // 校验存在
        validatePunishmentExtendExists(id);
        // 删除
        punishmentExtendDao.deleteById(id);
    }

    private void validatePunishmentExtendExists(String id) {
        if (punishmentExtendDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务延长处罚呈批数据不存在");
        }
    }

    @Override
    public PunishmentExtendDO getPunishmentExtend(String id) {
        return punishmentExtendDao.selectById(id);
    }

    @Override
    public PageResult<PunishmentExtendDO> getPunishmentExtendPage(PunishmentExtendPageReqVO pageReqVO) {
        return punishmentExtendDao.selectPage(pageReqVO);
    }

    @Override
    public List<PunishmentExtendDO> getPunishmentExtendList(PunishmentExtendListReqVO listReqVO) {
        return punishmentExtendDao.selectList(listReqVO);
    }


}
