package com.rs.module.acp.dao.db;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.db.OutPersonalEffectsSubDO;
import com.rs.module.acp.entity.db.PersonalEffectsInfoDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-收押业务-出所随身物品登记子 Dao
*
* <AUTHOR>
*/
@Mapper
public interface OutPersonalEffectsSubDao extends IBaseDao<OutPersonalEffectsSubDO> {


    default PageResult<OutPersonalEffectsSubDO> selectPage(OutPersonalEffectsSubPageReqVO reqVO) {
        Page<OutPersonalEffectsSubDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<OutPersonalEffectsSubDO> wrapper = new LambdaQueryWrapperX<OutPersonalEffectsSubDO>()
            .eqIfPresent(OutPersonalEffectsSubDO::getPersonalEffectsId, reqVO.getPersonalEffectsId())
            .eqIfPresent(OutPersonalEffectsSubDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(OutPersonalEffectsSubDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(OutPersonalEffectsSubDO::getWpmc, reqVO.getWpmc())
            .eqIfPresent(OutPersonalEffectsSubDO::getZl, reqVO.getZl())
            .eqIfPresent(OutPersonalEffectsSubDO::getHbzl, reqVO.getHbzl())
            .eqIfPresent(OutPersonalEffectsSubDO::getSl, reqVO.getSl())
            .eqIfPresent(OutPersonalEffectsSubDO::getWptz, reqVO.getWptz())
            .eqIfPresent(OutPersonalEffectsSubDO::getWpzp, reqVO.getWpzp())
            .eqIfPresent(OutPersonalEffectsSubDO::getWz, reqVO.getWz())
            .eqIfPresent(OutPersonalEffectsSubDO::getWpczqk, reqVO.getWpczqk())
            .eqIfPresent(OutPersonalEffectsSubDO::getXjzrgrzkrq, reqVO.getXjzrgrzkrq())
            .eqIfPresent(OutPersonalEffectsSubDO::getQcwpsl, reqVO.getQcwpsl())
            .eqIfPresent(OutPersonalEffectsSubDO::getBz, reqVO.getBz())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(OutPersonalEffectsSubDO::getAddTime);
        }
        Page<OutPersonalEffectsSubDO> outPersonalEffectsSubPage = selectPage(page, wrapper);
        return new PageResult<>(outPersonalEffectsSubPage.getRecords(), outPersonalEffectsSubPage.getTotal());
    }
    default List<OutPersonalEffectsSubDO> selectList(OutPersonalEffectsSubListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<OutPersonalEffectsSubDO>()
            .eqIfPresent(OutPersonalEffectsSubDO::getPersonalEffectsId, reqVO.getPersonalEffectsId())
            .eqIfPresent(OutPersonalEffectsSubDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(OutPersonalEffectsSubDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(OutPersonalEffectsSubDO::getWpmc, reqVO.getWpmc())
            .eqIfPresent(OutPersonalEffectsSubDO::getZl, reqVO.getZl())
            .eqIfPresent(OutPersonalEffectsSubDO::getHbzl, reqVO.getHbzl())
            .eqIfPresent(OutPersonalEffectsSubDO::getSl, reqVO.getSl())
            .eqIfPresent(OutPersonalEffectsSubDO::getWptz, reqVO.getWptz())
            .eqIfPresent(OutPersonalEffectsSubDO::getWpzp, reqVO.getWpzp())
            .eqIfPresent(OutPersonalEffectsSubDO::getWz, reqVO.getWz())
            .eqIfPresent(OutPersonalEffectsSubDO::getWpczqk, reqVO.getWpczqk())
            .eqIfPresent(OutPersonalEffectsSubDO::getXjzrgrzkrq, reqVO.getXjzrgrzkrq())
            .eqIfPresent(OutPersonalEffectsSubDO::getQcwpsl, reqVO.getQcwpsl())
            .eqIfPresent(OutPersonalEffectsSubDO::getBz, reqVO.getBz())
        .orderByDesc(OutPersonalEffectsSubDO::getAddTime));    }


    List<OutPersonalEffectsSubDO> getPersonalEffectsByPersonId(String rybh);
}
