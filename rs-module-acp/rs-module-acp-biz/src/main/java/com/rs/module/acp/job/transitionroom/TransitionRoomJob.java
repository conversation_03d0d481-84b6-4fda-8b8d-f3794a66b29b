package com.rs.module.acp.job.transitionroom;

import com.rs.module.acp.dao.gj.TransitionRoomDao;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 过渡期满后，更新后已解除
 * <AUTHOR>
 * @date 2025/6/19 16:51
 */
@Component
public class TransitionRoomJob {

    @Resource
    private TransitionRoomDao transitionRoomDao;

    /**
     * 更新过渡期慢，状态
     */
    @XxlJob("transitionPeriodUpdate")
    public void transitionPeriodUpdate() {
        // 待解除
        XxlJobHelper.log("-----开始-------");
        XxlJobHelper.log("过渡监室使用-过渡超期状态更新");
        try {
          transitionRoomDao.transitionPeriodUpdate(new Date());
        } catch (Exception e) {
            XxlJobHelper.log("过渡监室使用-待解除-过渡超期状态更新：{}", e.getMessage());
        }
        XxlJobHelper.log("过渡监室使用-待解除-过渡超期状态更新:-----结束------");
    }
}
