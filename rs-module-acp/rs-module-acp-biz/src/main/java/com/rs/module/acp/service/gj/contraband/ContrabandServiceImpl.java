package com.rs.module.acp.service.gj.contraband;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rs.module.acp.controller.admin.gj.vo.contraband.ContrabandListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.contraband.ContrabandPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.contraband.ContrabandSaveReqVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.ContrabandDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.ContrabandDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-违禁品登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ContrabandServiceImpl extends BaseServiceImpl<ContrabandDao, ContrabandDO> implements ContrabandService {

    @Resource
    private ContrabandDao contrabandDao;

    @Override
    public String createContraband(ContrabandSaveReqVO createReqVO) {
        // 插入
        ContrabandDO contraband = BeanUtils.toBean(createReqVO, ContrabandDO.class);
        contrabandDao.insert(contraband);
        // 返回
        return contraband.getId();
    }

    @Override
    public void updateContraband(ContrabandSaveReqVO updateReqVO) {
        // 校验存在
        validateContrabandExists(updateReqVO.getId());
        // 更新
        ContrabandDO updateObj = BeanUtils.toBean(updateReqVO, ContrabandDO.class);
        contrabandDao.updateById(updateObj);
    }

    @Override
    public void deleteContraband(String id) {
        // 校验存在
        validateContrabandExists(id);
        // 删除
        contrabandDao.deleteById(id);
    }

    private void validateContrabandExists(String id) {
        if (contrabandDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-违禁品登记数据不存在");
        }
    }

    @Override
    public ContrabandDO getContraband(String id) {
        return contrabandDao.selectById(id);
    }

    @Override
    public PageResult<ContrabandDO> getContrabandPage(ContrabandPageReqVO pageReqVO) {
        return contrabandDao.selectPage(pageReqVO);
    }

    @Override
    public List<ContrabandDO> getContrabandList(ContrabandListReqVO listReqVO) {
        return contrabandDao.selectList(listReqVO);
    }

    @Override
    public List<ContrabandDO> getContrabandhistoryList(String jgrybm, String id) {
        LambdaQueryWrapper<ContrabandDO> wrapper = Wrappers.lambdaQuery(ContrabandDO.class);
        wrapper.eq(ContrabandDO::getJgrybm, jgrybm);
        if (StringUtils.isNotEmpty(id)) {
            wrapper.ne(ContrabandDO::getId, id);
        }
        wrapper.orderByDesc(ContrabandDO::getCheckTime);
        return contrabandDao.selectList(wrapper);
    }


}
