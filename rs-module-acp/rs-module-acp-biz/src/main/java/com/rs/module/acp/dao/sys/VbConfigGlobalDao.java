package com.rs.module.acp.dao.sys;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.sys.VbConfigGlobalDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.sys.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-语音播报-全局配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface VbConfigGlobalDao extends IBaseDao<VbConfigGlobalDO> {


    default PageResult<VbConfigGlobalDO> selectPage(VbConfigGlobalPageReqVO reqVO) {
        Page<VbConfigGlobalDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<VbConfigGlobalDO> wrapper = new LambdaQueryWrapperX<VbConfigGlobalDO>()
            .eqIfPresent(VbConfigGlobalDO::getSilentTimeSlots, reqVO.getSilentTimeSlots())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(VbConfigGlobalDO::getAddTime);
        }
        Page<VbConfigGlobalDO> vbConfigGlobalPage = selectPage(page, wrapper);
        return new PageResult<>(vbConfigGlobalPage.getRecords(), vbConfigGlobalPage.getTotal());
    }
    default List<VbConfigGlobalDO> selectList(VbConfigGlobalListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<VbConfigGlobalDO>()
            .eqIfPresent(VbConfigGlobalDO::getSilentTimeSlots, reqVO.getSilentTimeSlots())
        .orderByDesc(VbConfigGlobalDO::getAddTime));    }


    }
