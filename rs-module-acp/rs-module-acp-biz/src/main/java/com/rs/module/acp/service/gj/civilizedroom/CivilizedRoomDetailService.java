package com.rs.module.acp.service.gj.civilizedroom;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.CivilizedRoomDetailListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.CivilizedRoomDetailPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.CivilizedRoomDetailSaveReqVO;
import com.rs.module.acp.entity.gj.CivilizedRoomDetailDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-文明监室登记明细 Service 接口
 *
 * <AUTHOR>
 */
public interface CivilizedRoomDetailService extends IBaseService<CivilizedRoomDetailDO>{

    /**
     * 创建实战平台-管教业务-文明监室登记明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCivilizedRoomDetail(@Valid CivilizedRoomDetailSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-文明监室登记明细
     *
     * @param updateReqVO 更新信息
     */
    void updateCivilizedRoomDetail(@Valid CivilizedRoomDetailSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-文明监室登记明细
     *
     * @param id 编号
     */
    void deleteCivilizedRoomDetail(String id);

    /**
     * 获得实战平台-管教业务-文明监室登记明细
     *
     * @param id 编号
     * @return 实战平台-管教业务-文明监室登记明细
     */
    CivilizedRoomDetailDO getCivilizedRoomDetail(String id);

    /**
    * 获得实战平台-管教业务-文明监室登记明细分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-文明监室登记明细分页
    */
    PageResult<CivilizedRoomDetailDO> getCivilizedRoomDetailPage(CivilizedRoomDetailPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-文明监室登记明细列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-文明监室登记明细列表
    */
    List<CivilizedRoomDetailDO> getCivilizedRoomDetailList(CivilizedRoomDetailListReqVO listReqVO);


}
