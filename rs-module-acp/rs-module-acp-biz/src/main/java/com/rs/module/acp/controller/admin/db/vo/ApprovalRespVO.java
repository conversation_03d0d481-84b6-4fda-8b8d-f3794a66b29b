package com.rs.module.acp.controller.admin.db.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "是否到达审批流程VO")
@Data
public class ApprovalRespVO{

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("是否发起审批")
    private boolean isApproval = false;

    public void setIsApproval(boolean isApproval) {
        this.isApproval = isApproval;
    }

}
