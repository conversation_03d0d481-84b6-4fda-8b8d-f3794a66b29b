package com.rs.module.acp.dao.db;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.db.dbSocialRelationsDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-收押业务-社会关系 Dao
*
* <AUTHOR>
*/
@Mapper
public interface dbSocialRelationsDao extends IBaseDao<dbSocialRelationsDO> {


    default PageResult<dbSocialRelationsDO> selectPage(dbSocialRelationsPageReqVO reqVO) {
        Page<dbSocialRelationsDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<dbSocialRelationsDO> wrapper = new LambdaQueryWrapperX<dbSocialRelationsDO>()
            .eqIfPresent(dbSocialRelationsDO::getRybh, reqVO.getRybh())
            .likeIfPresent(dbSocialRelationsDO::getName, reqVO.getName())
            .eqIfPresent(dbSocialRelationsDO::getGender, reqVO.getGender())
            .eqIfPresent(dbSocialRelationsDO::getIdType, reqVO.getIdType())
            .eqIfPresent(dbSocialRelationsDO::getIdNumber, reqVO.getIdNumber())
            .eqIfPresent(dbSocialRelationsDO::getRelationship, reqVO.getRelationship())
            .eqIfPresent(dbSocialRelationsDO::getContact, reqVO.getContact())
            .eqIfPresent(dbSocialRelationsDO::getWorkUnit, reqVO.getWorkUnit())
            .eqIfPresent(dbSocialRelationsDO::getAddress, reqVO.getAddress())
            .eqIfPresent(dbSocialRelationsDO::getOccupation, reqVO.getOccupation())
            .eqIfPresent(dbSocialRelationsDO::getImageUrl, reqVO.getImageUrl())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(dbSocialRelationsDO::getAddTime);
        }
        Page<dbSocialRelationsDO> dbSocialRelationsPage = selectPage(page, wrapper);
        return new PageResult<>(dbSocialRelationsPage.getRecords(), dbSocialRelationsPage.getTotal());
    }
    default List<dbSocialRelationsDO> selectList(dbSocialRelationsListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<dbSocialRelationsDO>()
            .eqIfPresent(dbSocialRelationsDO::getRybh, reqVO.getRybh())
            .likeIfPresent(dbSocialRelationsDO::getName, reqVO.getName())
            .eqIfPresent(dbSocialRelationsDO::getGender, reqVO.getGender())
            .eqIfPresent(dbSocialRelationsDO::getIdType, reqVO.getIdType())
            .eqIfPresent(dbSocialRelationsDO::getIdNumber, reqVO.getIdNumber())
            .eqIfPresent(dbSocialRelationsDO::getRelationship, reqVO.getRelationship())
            .eqIfPresent(dbSocialRelationsDO::getContact, reqVO.getContact())
            .eqIfPresent(dbSocialRelationsDO::getWorkUnit, reqVO.getWorkUnit())
            .eqIfPresent(dbSocialRelationsDO::getAddress, reqVO.getAddress())
            .eqIfPresent(dbSocialRelationsDO::getOccupation, reqVO.getOccupation())
            .eqIfPresent(dbSocialRelationsDO::getImageUrl, reqVO.getImageUrl())
        .orderByDesc(dbSocialRelationsDO::getAddTime));    }


    }
