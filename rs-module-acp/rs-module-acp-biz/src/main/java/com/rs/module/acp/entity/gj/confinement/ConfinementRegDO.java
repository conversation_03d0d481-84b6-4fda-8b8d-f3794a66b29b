package com.rs.module.acp.entity.gj.confinement;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-禁闭登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_confinement_reg")
@KeySequence("acp_gj_confinement_reg_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_confinement_reg")
public class ConfinementRegDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 数据来源（字典：ZD_DATA_SOURCES）
     */
    private String dataSources;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 禁闭天数
     */
    private Integer confinementDays;
    /**
     * 禁闭监室id
     */
    private String roomId;
    /**
     * 原监室ID
     */
    private String originalRoomId;
    /**
     * 禁闭原因 ZD_GJYW_JBYY
     */
    private String confinementReason;
    /**
     * 详细原因
     */
    private String detailedReason;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 状态
     */
    private String status;
    /**
     * 禁闭开始日期
     */
    private Date confinementStartDate;
    /**
     * 禁闭结束日期
     */
    private Date confinementEndDate;
    /**
     * 是否关联惩罚 0 否,1 是
     */
    private Integer isAssociatedPunishment;
    /**
     * 惩罚措施
     */
    private String punishmentMeasures;
    /**
     * 带出民警身份证号
     */
    private String escortingPoliceSfzh;
    /**
     * 带出民警身份证号
     */
    private String escortingPolice;
    /**
     * 带出监室时间
     */
    private Date escortingTime;
    /**
     * 检查结果
     */
    private String inspectionResult;
    /**
     * 违禁物品登记
     */
    private String prohibitedItems;
    /**
     * 体表检查登记
     */
    private String physicalExam;
    /**
     * 异常情况登记
     */
    private String abnormalSituations;
    /**
     * 违禁物品登记照片存储地址
     */
    private String prohibitedItemsImgUrl;
    /**
     * 体表检查登记照片存储地址
     */
    private String physicalExamImgUrl;
    /**
     * 异常情况登记照片存储地址
     */
    private String abnormalSituationsImgUrl;
    /**
     * 是否查出违禁物品
     */
    private Short isProhibitedItems;
    /**
     * 检查时间
     */
    private Date inspectionTime;
    /**
     * 检查民警身份证号
     */
    private String inspectorSfzh;
    /**
     * 检查民警身份证号
     */
    private String inspector;
    /**
     * 带入禁闭监室时间
     */
    private Date intoConfinementTime;
    /**
     * 审批人身份证号
     */
    private String approverSfzh;
    /**
     * 审批人姓名
     */
    private String approverXm;
    /**
     * 审批时间
     */
    private Date approverTime;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 审批人签名
     */
    private String approvalAutograph;
    /**
     * 审批人签名日期
     */
    private Date approvalAutographTime;
    /**
     * 审核意见
     */
    private String approvalComments;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 实际禁闭结束时间
     */
    private Date actualEndTime;
    /**
     * 解除原因
     */
    private String removeReason;
    /**
     * 执行情况
     */
    private String executeSituation;
}
