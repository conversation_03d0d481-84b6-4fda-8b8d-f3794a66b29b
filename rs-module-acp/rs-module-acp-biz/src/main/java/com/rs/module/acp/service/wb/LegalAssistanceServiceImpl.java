package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.LegalAssistanceDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.LegalAssistanceDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-法律帮助申请 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LegalAssistanceServiceImpl extends BaseServiceImpl<LegalAssistanceDao, LegalAssistanceDO> implements LegalAssistanceService {

    @Resource
    private LegalAssistanceDao legalAssistanceDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createLegalAssistance(LegalAssistanceSaveReqVO createReqVO) {
        // 插入
        LegalAssistanceDO legalAssistance = BeanUtils.toBean(createReqVO, LegalAssistanceDO.class);

        if(CollectionUtil.isNotEmpty(createReqVO.getSupervisingPoliceList())){
            List<String> supervisingPoliceSfzhList = new ArrayList<>();
            List<String> supervisingPoliceNameList =new ArrayList<>();
            for(JSONObject jsonObject:createReqVO.getSupervisingPoliceList()){
                supervisingPoliceSfzhList.add(jsonObject.getString("zjhm"));
                supervisingPoliceNameList.add(jsonObject.getString("xm"));
            }
            legalAssistance.setSupervisingPoliceSfzh(String.join(",",supervisingPoliceSfzhList));
            legalAssistance.setSupervisingPoliceName(String.join(",",supervisingPoliceNameList));

        }

        legalAssistanceDao.insert(legalAssistance);
        // 返回
        return legalAssistance.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLegalAssistance(LegalAssistanceSaveReqVO updateReqVO) {
        // 校验存在
        validateLegalAssistanceExists(updateReqVO.getId());
        // 更新
        LegalAssistanceDO updateObj = BeanUtils.toBean(updateReqVO, LegalAssistanceDO.class);

        if(CollectionUtil.isNotEmpty(updateReqVO.getSupervisingPoliceList())){
            List<String> supervisingPoliceSfzhList = new ArrayList<>();
            List<String> supervisingPoliceNameList =new ArrayList<>();
            for(JSONObject jsonObject:updateReqVO.getSupervisingPoliceList()){
                supervisingPoliceSfzhList.add(jsonObject.getString("zjhm"));
                supervisingPoliceNameList.add(jsonObject.getString("xm"));
            }
            updateObj.setSupervisingPoliceSfzh(String.join(",",supervisingPoliceSfzhList));
            updateObj.setSupervisingPoliceName(String.join(",",supervisingPoliceNameList));
        }

        legalAssistanceDao.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLegalAssistance(String id) {
        // 校验存在
        validateLegalAssistanceExists(id);
        // 删除
        legalAssistanceDao.deleteById(id);
    }

    private void validateLegalAssistanceExists(String id) {
        if (legalAssistanceDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-法律帮助申请数据不存在");
        }
    }

    @Override
    public LegalAssistanceDO getLegalAssistance(String id) {
        return legalAssistanceDao.selectById(id);
    }

    @Override
    public PageResult<LegalAssistanceDO> getLegalAssistancePage(LegalAssistancePageReqVO pageReqVO) {
        return legalAssistanceDao.selectPage(pageReqVO);
    }

    @Override
    public List<LegalAssistanceDO> getLegalAssistanceList(LegalAssistanceListReqVO listReqVO) {
        return legalAssistanceDao.selectList(listReqVO);
    }

    @Override
    public LegalAssistanceRespVO getLegalAssistanceById(String id) {
        LegalAssistanceDO legalAssistanceDO = legalAssistanceDao.selectById(id);
        LegalAssistanceRespVO respVO = BeanUtils.toBean(legalAssistanceDO,LegalAssistanceRespVO.class);

        if(ObjectUtil.isNotEmpty(legalAssistanceDO.getSupervisingPoliceSfzh())){
            List<JSONObject> supervisingPoliceList = new ArrayList<>();
            List<String> supervisingPoliceSfzhList = Arrays.asList(legalAssistanceDO.getSupervisingPoliceSfzh().split(","));
            List<String> supervisingPoliceNameList = Arrays.asList(legalAssistanceDO.getSupervisingPoliceName().split(","));
            for(int i=0;i<supervisingPoliceSfzhList.size();i++){
                JSONObject tempJson = new JSONObject();
                tempJson.put("xm",supervisingPoliceNameList.get(i));
                tempJson.put("zjhm",supervisingPoliceSfzhList.get(i));
                supervisingPoliceList.add(tempJson);
            }
            respVO.setSupervisingPoliceList(supervisingPoliceList);
        }
        return respVO;
    }
}
