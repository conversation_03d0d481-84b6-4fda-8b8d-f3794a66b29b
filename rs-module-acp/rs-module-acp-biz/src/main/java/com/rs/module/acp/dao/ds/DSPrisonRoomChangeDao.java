package com.rs.module.acp.dao.ds;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.ds.DSPrisonRoomChangeDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.ds.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 实战平台-数据固化-监所人员变动记录 Dao
*
* <AUTHOR>
*/
@Mapper
public interface DSPrisonRoomChangeDao extends IBaseDao<DSPrisonRoomChangeDO> {


    default PageResult<DSPrisonRoomChangeDO> selectPage(PrisonRoomChangePageReqVO reqVO) {
        Page<DSPrisonRoomChangeDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<DSPrisonRoomChangeDO> wrapper = new LambdaQueryWrapperX<DSPrisonRoomChangeDO>()
            .eqIfPresent(DSPrisonRoomChangeDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(DSPrisonRoomChangeDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(DSPrisonRoomChangeDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(DSPrisonRoomChangeDO::getRoomName, reqVO.getRoomName())
            .betweenIfPresent(DSPrisonRoomChangeDO::getInRoomTime, reqVO.getInRoomTime())
            .eqIfPresent(DSPrisonRoomChangeDO::getInRoomType, reqVO.getInRoomType())
            .eqIfPresent(DSPrisonRoomChangeDO::getInRoomBusinessId, reqVO.getInRoomBusinessId())
            .eqIfPresent(DSPrisonRoomChangeDO::getInRoomReason, reqVO.getInRoomReason())
            .betweenIfPresent(DSPrisonRoomChangeDO::getOutRoomTime, reqVO.getOutRoomTime())
            .eqIfPresent(DSPrisonRoomChangeDO::getOutRoomType, reqVO.getOutRoomType())
            .eqIfPresent(DSPrisonRoomChangeDO::getOutRoomBusinessId, reqVO.getOutRoomBusinessId())
            .eqIfPresent(DSPrisonRoomChangeDO::getOutRoomReason, reqVO.getOutRoomReason())
            .eqIfPresent(DSPrisonRoomChangeDO::getBatchId, reqVO.getBatchId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(DSPrisonRoomChangeDO::getAddTime);
        }
        Page<DSPrisonRoomChangeDO> prisonRoomChangePage = selectPage(page, wrapper);
        return new PageResult<>(prisonRoomChangePage.getRecords(), prisonRoomChangePage.getTotal());
    }
    default List<DSPrisonRoomChangeDO> selectList(PrisonRoomChangeListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DSPrisonRoomChangeDO>()
            .eqIfPresent(DSPrisonRoomChangeDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(DSPrisonRoomChangeDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(DSPrisonRoomChangeDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(DSPrisonRoomChangeDO::getRoomName, reqVO.getRoomName())
            .betweenIfPresent(DSPrisonRoomChangeDO::getInRoomTime, reqVO.getInRoomTime())
            .eqIfPresent(DSPrisonRoomChangeDO::getInRoomType, reqVO.getInRoomType())
            .eqIfPresent(DSPrisonRoomChangeDO::getInRoomBusinessId, reqVO.getInRoomBusinessId())
            .eqIfPresent(DSPrisonRoomChangeDO::getInRoomReason, reqVO.getInRoomReason())
            .betweenIfPresent(DSPrisonRoomChangeDO::getOutRoomTime, reqVO.getOutRoomTime())
            .eqIfPresent(DSPrisonRoomChangeDO::getOutRoomType, reqVO.getOutRoomType())
            .eqIfPresent(DSPrisonRoomChangeDO::getOutRoomBusinessId, reqVO.getOutRoomBusinessId())
            .eqIfPresent(DSPrisonRoomChangeDO::getOutRoomReason, reqVO.getOutRoomReason())
            .eqIfPresent(DSPrisonRoomChangeDO::getBatchId, reqVO.getBatchId())
        .orderByDesc(DSPrisonRoomChangeDO::getAddTime));    }

    /**
     * 获取指定人员在指定监室和时间段内的同监室人员列表
     *
     * @param query 查询条件
     * @return 同监室人员列表
     */
    List<DSPrisonRoomChangeRespVO> getPrisonRoommates(@Param("query") DSPrisonRoommateQueryReqVO query);
    List<DSPrisonRoomChangeRespVO> getPrisonRoommatesGrouped(@Param("query") DSPrisonRoommateQueryReqVO query);
    }
