package com.rs.module.acp.service.gj.civilizedroom;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.*;
import com.rs.module.acp.entity.gj.CivilizedRoomDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomPageWithViolationReqVO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;

/**
 * 实战平台-管教业务-文明监室登记 Service 接口
 *
 * <AUTHOR>
 */
public interface CivilizedRoomService extends IBaseService<CivilizedRoomDO>{

    /**
     * 创建实战平台-管教业务-文明监室登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCivilizedRoom(@Valid CivilizedRoomSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-文明监室登记
     *
     * @param updateReqVO 更新信息
     */
    void updateCivilizedRoom(@Valid CivilizedRoomSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-文明监室登记
     *
     * @param id 编号
     */
    void deleteCivilizedRoom(String id);

    /**
     * 获得实战平台-管教业务-文明监室登记
     *
     * @param id 编号
     * @return 实战平台-管教业务-文明监室登记
     */
    CivilizedRoomRespVO getCivilizedRoom(String id);

    /**
    * 获得实战平台-管教业务-文明监室登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-文明监室登记分页
    */
    PageResult<CivilizedRoomDO> getCivilizedRoomPage(CivilizedRoomPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-文明监室登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-文明监室登记列表
    */
    List<CivilizedRoomRespVO> getCivilizedRoomList(CivilizedRoomListReqVO listReqVO);


    void registEvaluation(CivilizedRoomRegistEvaluationReqVO reqVO);

    void approval(CivilizedRoomApprovalReqVO reqVO);

    void againEvaluation(CivilizedRoomRegistEvaluationReqVO reqVO);

}
