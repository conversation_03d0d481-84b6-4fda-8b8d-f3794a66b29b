package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.GoodsDeliveryDetailsDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-物品顾送明细 Service 接口
 *
 * <AUTHOR>
 */
public interface GoodsDeliveryDetailsService extends IBaseService<GoodsDeliveryDetailsDO>{

    /**
     * 创建实战平台-窗口业务-物品顾送明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createGoodsDeliveryDetails(@Valid GoodsDeliveryDetailsSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-物品顾送明细
     *
     * @param updateReqVO 更新信息
     */
    void updateGoodsDeliveryDetails(@Valid GoodsDeliveryDetailsSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-物品顾送明细
     *
     * @param id 编号
     */
    void deleteGoodsDeliveryDetails(String id);

    /**
     * 获得实战平台-窗口业务-物品顾送明细
     *
     * @param id 编号
     * @return 实战平台-窗口业务-物品顾送明细
     */
    GoodsDeliveryDetailsDO getGoodsDeliveryDetails(String id);

    /**
    * 获得实战平台-窗口业务-物品顾送明细分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-物品顾送明细分页
    */
    PageResult<GoodsDeliveryDetailsDO> getGoodsDeliveryDetailsPage(GoodsDeliveryDetailsPageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-物品顾送明细列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-物品顾送明细列表
    */
    List<GoodsDeliveryDetailsDO> getGoodsDeliveryDetailsList(GoodsDeliveryDetailsListReqVO listReqVO);


    /**
     * 实战平台-窗口业务-根据物品顾送ID保存物品明细表
     * @param goodsList
     * @param goodsDeliveryId
     * @return
     */
    boolean saveGoodsDeliveryDetailsListByGoodsDeliveryId(List<GoodsDeliveryDetailsSaveReqVO> goodsList,String goodsDeliveryId);

    /**
     * 实战平台-窗口业务-根据物品顾送ID获取物品明细列表
     * @param goodsDeliveryId
     * @return
     */
    List<GoodsDeliveryDetailsRespVO> getGoodsDeliveryDetailsListByGoodsDeliveryId(String goodsDeliveryId);
}
