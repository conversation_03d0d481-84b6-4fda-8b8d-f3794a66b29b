package com.rs.module.acp.dao.db;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.db.PersonalEffectsDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-收押业务-随身物品登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PersonalEffectsDao extends IBaseDao<PersonalEffectsDO> {


    default PageResult<PersonalEffectsDO> selectPage(PersonalEffectsPageReqVO reqVO) {
        Page<PersonalEffectsDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PersonalEffectsDO> wrapper = new LambdaQueryWrapperX<PersonalEffectsDO>()
            .eqIfPresent(PersonalEffectsDO::getRybh, reqVO.getRybh())
            .eqIfPresent(PersonalEffectsDO::getRyxm, reqVO.getRyxm())
            .eqIfPresent(PersonalEffectsDO::getStatus, reqVO.getStatus())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PersonalEffectsDO::getAddTime);
        }
        Page<PersonalEffectsDO> personalEffectsPage = selectPage(page, wrapper);
        return new PageResult<>(personalEffectsPage.getRecords(), personalEffectsPage.getTotal());
    }
    default List<PersonalEffectsDO> selectList(PersonalEffectsListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PersonalEffectsDO>()
            .eqIfPresent(PersonalEffectsDO::getRybh, reqVO.getRybh())
            .eqIfPresent(PersonalEffectsDO::getRyxm, reqVO.getRyxm())
            .eqIfPresent(PersonalEffectsDO::getStatus, reqVO.getStatus())
        .orderByDesc(PersonalEffectsDO::getAddTime));    }


    }
