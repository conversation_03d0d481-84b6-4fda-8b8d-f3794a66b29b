package com.rs.module.acp.controller.admin.gj.vo.common;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@ApiModel(description = "管理后台 - 管教业务-流程轨迹 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GjApprovalTraceVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("节点标识")
    private String nodeKey;

    @ApiModelProperty("节点名称")
    private String nodeName;

    @ApiModelProperty("节点状态是否完成 -1 未完成 ，1 完成")
    private Integer nodeStatus;

    @ApiModelProperty("节点时间")
    private String nodeCreateTime;

    @ApiModelProperty("字段信息")
    private List<TraceVO> nodeInfo;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    static public class  TraceVO {

        @ApiModelProperty("字段名")
        private String key;

        @ApiModelProperty("字段值")
        private String val;
    }

}
