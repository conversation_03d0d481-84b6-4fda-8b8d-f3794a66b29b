package com.rs.module.acp.dao.wb;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.LawyerMeetingCompanionDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-窗口业务-律师会见同行登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface LawyerMeetingCompanionDao extends IBaseDao<LawyerMeetingCompanionDO> {


    default PageResult<LawyerMeetingCompanionDO> selectPage(LawyerMeetingCompanionPageReqVO reqVO) {
        Page<LawyerMeetingCompanionDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<LawyerMeetingCompanionDO> wrapper = new LambdaQueryWrapperX<LawyerMeetingCompanionDO>()
            .eqIfPresent(LawyerMeetingCompanionDO::getLawyerMeetingId, reqVO.getLawyerMeetingId())
            .likeIfPresent(LawyerMeetingCompanionDO::getName, reqVO.getName())
            .eqIfPresent(LawyerMeetingCompanionDO::getGender, reqVO.getGender())
            .eqIfPresent(LawyerMeetingCompanionDO::getIdCard, reqVO.getIdCard())
            .eqIfPresent(LawyerMeetingCompanionDO::getCompanionType, reqVO.getCompanionType())
            .eqIfPresent(LawyerMeetingCompanionDO::getAttachmentUrl, reqVO.getAttachmentUrl())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(LawyerMeetingCompanionDO::getAddTime);
        }
        Page<LawyerMeetingCompanionDO> lawyerMeetingCompanionPage = selectPage(page, wrapper);
        return new PageResult<>(lawyerMeetingCompanionPage.getRecords(), lawyerMeetingCompanionPage.getTotal());
    }
    default List<LawyerMeetingCompanionDO> selectList(LawyerMeetingCompanionListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<LawyerMeetingCompanionDO>()
            .eqIfPresent(LawyerMeetingCompanionDO::getLawyerMeetingId, reqVO.getLawyerMeetingId())
            .likeIfPresent(LawyerMeetingCompanionDO::getName, reqVO.getName())
            .eqIfPresent(LawyerMeetingCompanionDO::getGender, reqVO.getGender())
            .eqIfPresent(LawyerMeetingCompanionDO::getIdCard, reqVO.getIdCard())
            .eqIfPresent(LawyerMeetingCompanionDO::getCompanionType, reqVO.getCompanionType())
            .eqIfPresent(LawyerMeetingCompanionDO::getAttachmentUrl, reqVO.getAttachmentUrl())
        .orderByDesc(LawyerMeetingCompanionDO::getAddTime));    }


    }
