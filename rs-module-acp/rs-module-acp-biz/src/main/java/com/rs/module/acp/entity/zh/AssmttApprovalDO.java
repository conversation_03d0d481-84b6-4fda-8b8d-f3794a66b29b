package com.rs.module.acp.entity.zh;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 综合管理-绩效考核-加减分考核审核 DO
 *
 * <AUTHOR>
 */
@TableName("acp_zh_assmtt_approval")
@KeySequence("acp_zh_assmtt_approval_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_assmtt_approval")
public class AssmttApprovalDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 考核记录ID
     */
    private String assmtRecordId;
    /**
     * 被考核人身份证号
     */
    private String assessedSfzh;
    /**
     * 被考核人姓名
     */
    private String assessedName;
    /**
     * 考核月份
     */
    private String assmtMonth;
    /**
     * 指标类型，01：自观指标，02：加减分指标
     */
    private String indicatorType;
    /**
     * 考核人身份证号
     */
    private String assessorSfzh;
    /**
     * 考核人姓名
     */
    private String assessorName;
    /**
     * 考核时间
     */
    private Date assessorTime;
    /**
     * 状态
     */
    private String status;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

}
