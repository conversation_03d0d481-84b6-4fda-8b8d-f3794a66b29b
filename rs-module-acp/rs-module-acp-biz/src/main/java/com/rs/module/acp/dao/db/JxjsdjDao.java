package com.rs.module.acp.dao.db;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.db.vo.jxjsdj.JxjsdjListReqVO;
import com.rs.module.acp.controller.admin.db.vo.jxjsdj.JxjsdjPageReqVO;
import com.rs.module.acp.entity.db.JxjsdjDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-收押业务-减刑登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface JxjsdjDao extends IBaseDao<JxjsdjDO> {


    default PageResult<JxjsdjDO> selectPage(JxjsdjPageReqVO reqVO) {
        Page<JxjsdjDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<JxjsdjDO> wrapper = new LambdaQueryWrapperX<JxjsdjDO>()
            .eqIfPresent(JxjsdjDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(JxjsdjDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(JxjsdjDO::getYwlx, reqVO.getYwlx())
            .eqIfPresent(JxjsdjDO::getJxyy, reqVO.getJxyy())
            .eqIfPresent(JxjsdjDO::getHgbx, reqVO.getHgbx())
            .eqIfPresent(JxjsdjDO::getLgbx, reqVO.getLgbx())
            .eqIfPresent(JxjsdjDO::getZdlgbx, reqVO.getZdlgbx())
            .eqIfPresent(JxjsdjDO::getQtyy, reqVO.getQtyy())
            .eqIfPresent(JxjsdjDO::getBz, reqVO.getBz())
            .betweenIfPresent(JxjsdjDO::getOperateTime, reqVO.getOperateTime())
            .eqIfPresent(JxjsdjDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(JxjsdjDO::getOperatePolice, reqVO.getOperatePolice())
            .eqIfPresent(JxjsdjDO::getAttUrl, reqVO.getAttUrl())
            .eqIfPresent(JxjsdjDO::getSwhbh, reqVO.getSwhbh())
            .eqIfPresent(JxjsdjDO::getFhswhbh, reqVO.getFhswhbh())
            .eqIfPresent(JxjsdjDO::getSwhzkrq, reqVO.getSwhzkrq())
            .eqIfPresent(JxjsdjDO::getSwhyjjg, reqVO.getSwhyjjg())
            .eqIfPresent(JxjsdjDO::getCxzkswhfhrq, reqVO.getCxzkswhfhrq())
            .eqIfPresent(JxjsdjDO::getFhyj, reqVO.getFhyj())
            .eqIfPresent(JxjsdjDO::getCcxpxlxnlrdjg, reqVO.getCcxpxlxnlrdjg())
            .eqIfPresent(JxjsdjDO::getCcxpxlxnlrdbz, reqVO.getCcxpxlxnlrdbz())
            .eqIfPresent(JxjsdjDO::getCcxpxlxnlrdcl, reqVO.getCcxpxlxnlrdcl())
            .eqIfPresent(JxjsdjDO::getCcxpxlxnlrdjbmjsfzh, reqVO.getCcxpxlxnlrdjbmjsfzh())
            .eqIfPresent(JxjsdjDO::getCcxpxlxnlrdjbmjxm, reqVO.getCcxpxlxnlrdjbmjxm())
            .eqIfPresent(JxjsdjDO::getCcxpxlxnlrdjbsj, reqVO.getCcxpxlxnlrdjbsj())
            .eqIfPresent(JxjsdjDO::getHszxddcpgjg, reqVO.getHszxddcpgjg())
            .eqIfPresent(JxjsdjDO::getHszxddcbz, reqVO.getHszxddcbz())
            .eqIfPresent(JxjsdjDO::getHszxddcjbmjsfzh, reqVO.getHszxddcjbmjsfzh())
            .eqIfPresent(JxjsdjDO::getHszxddcjbmjxm, reqVO.getHszxddcjbmjxm())
            .eqIfPresent(JxjsdjDO::getHszxddcjbsj, reqVO.getHszxddcjbsj())
            .eqIfPresent(JxjsdjDO::getHszxddccl, reqVO.getHszxddccl())
            .eqIfPresent(JxjsdjDO::getSngsksrq, reqVO.getSngsksrq())
            .eqIfPresent(JxjsdjDO::getSngsjsrq, reqVO.getSngsjsrq())
            .eqIfPresent(JxjsdjDO::getSfsdyy, reqVO.getSfsdyy())
            .eqIfPresent(JxjsdjDO::getSngsbz, reqVO.getSngsbz())
            .eqIfPresent(JxjsdjDO::getSngscl, reqVO.getSngscl())
            .eqIfPresent(JxjsdjDO::getSngsjbmjsfzh, reqVO.getSngsjbmjsfzh())
            .eqIfPresent(JxjsdjDO::getSngsjbmjxm, reqVO.getSngsjbmjxm())
            .eqIfPresent(JxjsdjDO::getSngsjbsj, reqVO.getSngsjbsj())
            .eqIfPresent(JxjsdjDO::getBqgajgrq, reqVO.getBqgajgrq())
            .eqIfPresent(JxjsdjDO::getGajgscrq, reqVO.getGajgscrq())
            .eqIfPresent(JxjsdjDO::getGajgscyj, reqVO.getGajgscyj())
            .eqIfPresent(JxjsdjDO::getGajgscbz, reqVO.getGajgscbz())
            .eqIfPresent(JxjsdjDO::getGajgsccl, reqVO.getGajgsccl())
            .eqIfPresent(JxjsdjDO::getGajgscjbmjsfzh, reqVO.getGajgscjbmjsfzh())
            .eqIfPresent(JxjsdjDO::getGajgscjbmjxm, reqVO.getGajgscjbmjxm())
            .eqIfPresent(JxjsdjDO::getGajgscjbsj, reqVO.getGajgscjbsj())
            .eqIfPresent(JxjsdjDO::getJcyjdtqrq, reqVO.getJcyjdtqrq())
            .eqIfPresent(JxjsdjDO::getJcyjdshrq, reqVO.getJcyjdshrq())
            .eqIfPresent(JxjsdjDO::getJcyjdyj, reqVO.getJcyjdyj())
            .eqIfPresent(JxjsdjDO::getJcyjdcl, reqVO.getJcyjdcl())
            .eqIfPresent(JxjsdjDO::getJcyjdjbmjsfzh, reqVO.getJcyjdjbmjsfzh())
            .eqIfPresent(JxjsdjDO::getJcyjdjbmjxm, reqVO.getJcyjdjbmjxm())
            .eqIfPresent(JxjsdjDO::getJcyjdjbsj, reqVO.getJcyjdjbsj())
            .eqIfPresent(JxjsdjDO::getFyshcdtqrq, reqVO.getFyshcdtqrq())
            .eqIfPresent(JxjsdjDO::getFyshcdrq, reqVO.getFyshcdrq())
            .eqIfPresent(JxjsdjDO::getFyshcdjg, reqVO.getFyshcdjg())
            .eqIfPresent(JxjsdjDO::getJxqk, reqVO.getJxqk())
            .eqIfPresent(JxjsdjDO::getXqjzrq, reqVO.getXqjzrq())
            .eqIfPresent(JxjsdjDO::getFyshcdcl, reqVO.getFyshcdcl())
            .eqIfPresent(JxjsdjDO::getFyshcdjbmjsfzh, reqVO.getFyshcdjbmjsfzh())
            .eqIfPresent(JxjsdjDO::getFyshcdjbmjxm, reqVO.getFyshcdjbmjxm())
            .eqIfPresent(JxjsdjDO::getFyshcdjbsj, reqVO.getFyshcdjbsj())
            .eqIfPresent(JxjsdjDO::getStatus, reqVO.getStatus())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(JxjsdjDO::getAddTime);
        }
        Page<JxjsdjDO> jxjsdjPage = selectPage(page, wrapper);
        return new PageResult<>(jxjsdjPage.getRecords(), jxjsdjPage.getTotal());
    }
    default List<JxjsdjDO> selectList(JxjsdjListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<JxjsdjDO>()
            .eqIfPresent(JxjsdjDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(JxjsdjDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(JxjsdjDO::getYwlx, reqVO.getYwlx())
            .eqIfPresent(JxjsdjDO::getJxyy, reqVO.getJxyy())
            .eqIfPresent(JxjsdjDO::getHgbx, reqVO.getHgbx())
            .eqIfPresent(JxjsdjDO::getLgbx, reqVO.getLgbx())
            .eqIfPresent(JxjsdjDO::getZdlgbx, reqVO.getZdlgbx())
            .eqIfPresent(JxjsdjDO::getQtyy, reqVO.getQtyy())
            .eqIfPresent(JxjsdjDO::getBz, reqVO.getBz())
            .betweenIfPresent(JxjsdjDO::getOperateTime, reqVO.getOperateTime())
            .eqIfPresent(JxjsdjDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(JxjsdjDO::getOperatePolice, reqVO.getOperatePolice())
            .eqIfPresent(JxjsdjDO::getAttUrl, reqVO.getAttUrl())
            .eqIfPresent(JxjsdjDO::getSwhbh, reqVO.getSwhbh())
            .eqIfPresent(JxjsdjDO::getFhswhbh, reqVO.getFhswhbh())
            .eqIfPresent(JxjsdjDO::getSwhzkrq, reqVO.getSwhzkrq())
            .eqIfPresent(JxjsdjDO::getSwhyjjg, reqVO.getSwhyjjg())
            .eqIfPresent(JxjsdjDO::getCxzkswhfhrq, reqVO.getCxzkswhfhrq())
            .eqIfPresent(JxjsdjDO::getFhyj, reqVO.getFhyj())
            .eqIfPresent(JxjsdjDO::getCcxpxlxnlrdjg, reqVO.getCcxpxlxnlrdjg())
            .eqIfPresent(JxjsdjDO::getCcxpxlxnlrdbz, reqVO.getCcxpxlxnlrdbz())
            .eqIfPresent(JxjsdjDO::getCcxpxlxnlrdcl, reqVO.getCcxpxlxnlrdcl())
            .eqIfPresent(JxjsdjDO::getCcxpxlxnlrdjbmjsfzh, reqVO.getCcxpxlxnlrdjbmjsfzh())
            .eqIfPresent(JxjsdjDO::getCcxpxlxnlrdjbmjxm, reqVO.getCcxpxlxnlrdjbmjxm())
            .eqIfPresent(JxjsdjDO::getCcxpxlxnlrdjbsj, reqVO.getCcxpxlxnlrdjbsj())
            .eqIfPresent(JxjsdjDO::getHszxddcpgjg, reqVO.getHszxddcpgjg())
            .eqIfPresent(JxjsdjDO::getHszxddcbz, reqVO.getHszxddcbz())
            .eqIfPresent(JxjsdjDO::getHszxddcjbmjsfzh, reqVO.getHszxddcjbmjsfzh())
            .eqIfPresent(JxjsdjDO::getHszxddcjbmjxm, reqVO.getHszxddcjbmjxm())
            .eqIfPresent(JxjsdjDO::getHszxddcjbsj, reqVO.getHszxddcjbsj())
            .eqIfPresent(JxjsdjDO::getHszxddccl, reqVO.getHszxddccl())
            .eqIfPresent(JxjsdjDO::getSngsksrq, reqVO.getSngsksrq())
            .eqIfPresent(JxjsdjDO::getSngsjsrq, reqVO.getSngsjsrq())
            .eqIfPresent(JxjsdjDO::getSfsdyy, reqVO.getSfsdyy())
            .eqIfPresent(JxjsdjDO::getSngsbz, reqVO.getSngsbz())
            .eqIfPresent(JxjsdjDO::getSngscl, reqVO.getSngscl())
            .eqIfPresent(JxjsdjDO::getSngsjbmjsfzh, reqVO.getSngsjbmjsfzh())
            .eqIfPresent(JxjsdjDO::getSngsjbmjxm, reqVO.getSngsjbmjxm())
            .eqIfPresent(JxjsdjDO::getSngsjbsj, reqVO.getSngsjbsj())
            .eqIfPresent(JxjsdjDO::getBqgajgrq, reqVO.getBqgajgrq())
            .eqIfPresent(JxjsdjDO::getGajgscrq, reqVO.getGajgscrq())
            .eqIfPresent(JxjsdjDO::getGajgscyj, reqVO.getGajgscyj())
            .eqIfPresent(JxjsdjDO::getGajgscbz, reqVO.getGajgscbz())
            .eqIfPresent(JxjsdjDO::getGajgsccl, reqVO.getGajgsccl())
            .eqIfPresent(JxjsdjDO::getGajgscjbmjsfzh, reqVO.getGajgscjbmjsfzh())
            .eqIfPresent(JxjsdjDO::getGajgscjbmjxm, reqVO.getGajgscjbmjxm())
            .eqIfPresent(JxjsdjDO::getGajgscjbsj, reqVO.getGajgscjbsj())
            .eqIfPresent(JxjsdjDO::getJcyjdtqrq, reqVO.getJcyjdtqrq())
            .eqIfPresent(JxjsdjDO::getJcyjdshrq, reqVO.getJcyjdshrq())
            .eqIfPresent(JxjsdjDO::getJcyjdyj, reqVO.getJcyjdyj())
            .eqIfPresent(JxjsdjDO::getJcyjdcl, reqVO.getJcyjdcl())
            .eqIfPresent(JxjsdjDO::getJcyjdjbmjsfzh, reqVO.getJcyjdjbmjsfzh())
            .eqIfPresent(JxjsdjDO::getJcyjdjbmjxm, reqVO.getJcyjdjbmjxm())
            .eqIfPresent(JxjsdjDO::getJcyjdjbsj, reqVO.getJcyjdjbsj())
            .eqIfPresent(JxjsdjDO::getFyshcdtqrq, reqVO.getFyshcdtqrq())
            .eqIfPresent(JxjsdjDO::getFyshcdrq, reqVO.getFyshcdrq())
            .eqIfPresent(JxjsdjDO::getFyshcdjg, reqVO.getFyshcdjg())
            .eqIfPresent(JxjsdjDO::getJxqk, reqVO.getJxqk())
            .eqIfPresent(JxjsdjDO::getXqjzrq, reqVO.getXqjzrq())
            .eqIfPresent(JxjsdjDO::getFyshcdcl, reqVO.getFyshcdcl())
            .eqIfPresent(JxjsdjDO::getFyshcdjbmjsfzh, reqVO.getFyshcdjbmjsfzh())
            .eqIfPresent(JxjsdjDO::getFyshcdjbmjxm, reqVO.getFyshcdjbmjxm())
            .eqIfPresent(JxjsdjDO::getFyshcdjbsj, reqVO.getFyshcdjbsj())
            .eqIfPresent(JxjsdjDO::getStatus, reqVO.getStatus())
        .orderByDesc(JxjsdjDO::getAddTime));    }


    }
