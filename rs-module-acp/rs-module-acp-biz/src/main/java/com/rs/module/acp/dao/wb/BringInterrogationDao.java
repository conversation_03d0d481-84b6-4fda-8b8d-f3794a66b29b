package com.rs.module.acp.dao.wb;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.BringInterrogationDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-窗口业务-提询登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BringInterrogationDao extends IBaseDao<BringInterrogationDO> {


    default PageResult<BringInterrogationDO> selectPage(BringInterrogationPageReqVO reqVO) {
        Page<BringInterrogationDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<BringInterrogationDO> wrapper = new LambdaQueryWrapperX<BringInterrogationDO>()
            .eqIfPresent(BringInterrogationDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(BringInterrogationDO::getHandler1Id, reqVO.getHandler1Id())
            .eqIfPresent(BringInterrogationDO::getHandler1Xm, reqVO.getHandler1Xm())
            .eqIfPresent(BringInterrogationDO::getHandler2Id, reqVO.getHandler2Id())
            .eqIfPresent(BringInterrogationDO::getHandler2Xm, reqVO.getHandler2Xm())
            .eqIfPresent(BringInterrogationDO::getHandler3Id, reqVO.getHandler3Id())
            .eqIfPresent(BringInterrogationDO::getHandler3Xm, reqVO.getHandler3Xm())
            .eqIfPresent(BringInterrogationDO::getRoomId, reqVO.getRoomId())
            .eqIfPresent(BringInterrogationDO::getArraignmentReason, reqVO.getArraignmentReason())
            .betweenIfPresent(BringInterrogationDO::getStartApplyArraignmentTime, reqVO.getStartApplyArraignmentTime())
            .betweenIfPresent(BringInterrogationDO::getEndApplyArraignmentTime, reqVO.getEndApplyArraignmentTime())
            .eqIfPresent(BringInterrogationDO::getArraignmentLetterNumber, reqVO.getArraignmentLetterNumber())
            .eqIfPresent(BringInterrogationDO::getEvidenceType, reqVO.getEvidenceType())
            .eqIfPresent(BringInterrogationDO::getEvidenceNumber, reqVO.getEvidenceNumber())
            .eqIfPresent(BringInterrogationDO::getEvidenceUrl, reqVO.getEvidenceUrl())
            .eqIfPresent(BringInterrogationDO::getMinorAgent, reqVO.getMinorAgent())
            .eqIfPresent(BringInterrogationDO::getMinorGuardian, reqVO.getMinorGuardian())
            .eqIfPresent(BringInterrogationDO::getTranslator, reqVO.getTranslator())
            .eqIfPresent(BringInterrogationDO::getOtherProfessionals, reqVO.getOtherProfessionals())
            .eqIfPresent(BringInterrogationDO::getProfessionalCertUrl, reqVO.getProfessionalCertUrl())
            .eqIfPresent(BringInterrogationDO::getTjjglx, reqVO.getTjjglx())
            .eqIfPresent(BringInterrogationDO::getTjjgmc, reqVO.getTjjgmc())
            .eqIfPresent(BringInterrogationDO::getStatus, reqVO.getStatus())
            .betweenIfPresent(BringInterrogationDO::getCheckInTime, reqVO.getCheckInTime())
            .eqIfPresent(BringInterrogationDO::getCheckInPoliceSfzh, reqVO.getCheckInPoliceSfzh())
            .eqIfPresent(BringInterrogationDO::getCheckInPolice, reqVO.getCheckInPolice())
            .eqIfPresent(BringInterrogationDO::getEscortingPoliceSfzh, reqVO.getEscortingPoliceSfzh())
            .eqIfPresent(BringInterrogationDO::getEscortingPolice, reqVO.getEscortingPolice())
            .betweenIfPresent(BringInterrogationDO::getEscortingTime, reqVO.getEscortingTime())
            .eqIfPresent(BringInterrogationDO::getEscortingOperatorSfzh, reqVO.getEscortingOperatorSfzh())
            .eqIfPresent(BringInterrogationDO::getEscortingOperator, reqVO.getEscortingOperator())
            .betweenIfPresent(BringInterrogationDO::getEscortingOperatorTime, reqVO.getEscortingOperatorTime())
            .eqIfPresent(BringInterrogationDO::getInspectionResult, reqVO.getInspectionResult())
            .eqIfPresent(BringInterrogationDO::getProhibitedItems, reqVO.getProhibitedItems())
            .eqIfPresent(BringInterrogationDO::getPhysicalExam, reqVO.getPhysicalExam())
            .eqIfPresent(BringInterrogationDO::getAbnormalSituations, reqVO.getAbnormalSituations())
            .eqIfPresent(BringInterrogationDO::getProhibitedItemsImgUrl, reqVO.getProhibitedItemsImgUrl())
            .eqIfPresent(BringInterrogationDO::getPhysicalExamImgUrl, reqVO.getPhysicalExamImgUrl())
            .eqIfPresent(BringInterrogationDO::getAbnormalSituationsImgUrl, reqVO.getAbnormalSituationsImgUrl())
            .eqIfPresent(BringInterrogationDO::getIsProhibitedItems, reqVO.getIsProhibitedItems())
            .betweenIfPresent(BringInterrogationDO::getInspectionTime, reqVO.getInspectionTime())
            .eqIfPresent(BringInterrogationDO::getInspectorSfzh, reqVO.getInspectorSfzh())
            .eqIfPresent(BringInterrogationDO::getInspector, reqVO.getInspector())
            .betweenIfPresent(BringInterrogationDO::getArraignmentStartTime, reqVO.getArraignmentStartTime())
            .betweenIfPresent(BringInterrogationDO::getArraignmentEndTime, reqVO.getArraignmentEndTime())
            .eqIfPresent(BringInterrogationDO::getReturnInspectorSfzh, reqVO.getReturnInspectorSfzh())
            .eqIfPresent(BringInterrogationDO::getReturnInspector, reqVO.getReturnInspector())
            .betweenIfPresent(BringInterrogationDO::getReturnInspectionTime, reqVO.getReturnInspectionTime())
            .eqIfPresent(BringInterrogationDO::getReturnInspectionResult, reqVO.getReturnInspectionResult())
            .betweenIfPresent(BringInterrogationDO::getReturnTime, reqVO.getReturnTime())
            .eqIfPresent(BringInterrogationDO::getReturnPoliceSfzh, reqVO.getReturnPoliceSfzh())
            .eqIfPresent(BringInterrogationDO::getReturnPolice, reqVO.getReturnPolice())
            .eqIfPresent(BringInterrogationDO::getReturnOperatorSfzh, reqVO.getReturnOperatorSfzh())
            .eqIfPresent(BringInterrogationDO::getReturnOperator, reqVO.getReturnOperator())
            .betweenIfPresent(BringInterrogationDO::getReturnOperatorTime, reqVO.getReturnOperatorTime())
            .eqIfPresent(BringInterrogationDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(BringInterrogationDO::getTaskId, reqVO.getTaskId())
            .eqIfPresent(BringInterrogationDO::getReturnProhibitedItems, reqVO.getReturnProhibitedItems())
            .eqIfPresent(BringInterrogationDO::getReturnPhysicalExam, reqVO.getReturnPhysicalExam())
            .eqIfPresent(BringInterrogationDO::getReturnAbnormalSituations, reqVO.getReturnAbnormalSituations())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(BringInterrogationDO::getAddTime);
        }
        Page<BringInterrogationDO> bringInterrogationPage = selectPage(page, wrapper);
        return new PageResult<>(bringInterrogationPage.getRecords(), bringInterrogationPage.getTotal());
    }
    default List<BringInterrogationDO> selectList(BringInterrogationListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BringInterrogationDO>()
            .eqIfPresent(BringInterrogationDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(BringInterrogationDO::getHandler1Id, reqVO.getHandler1Id())
            .eqIfPresent(BringInterrogationDO::getHandler1Xm, reqVO.getHandler1Xm())
            .eqIfPresent(BringInterrogationDO::getHandler2Id, reqVO.getHandler2Id())
            .eqIfPresent(BringInterrogationDO::getHandler2Xm, reqVO.getHandler2Xm())
            .eqIfPresent(BringInterrogationDO::getHandler3Id, reqVO.getHandler3Id())
            .eqIfPresent(BringInterrogationDO::getHandler3Xm, reqVO.getHandler3Xm())
            .eqIfPresent(BringInterrogationDO::getRoomId, reqVO.getRoomId())
            .eqIfPresent(BringInterrogationDO::getArraignmentReason, reqVO.getArraignmentReason())
            .betweenIfPresent(BringInterrogationDO::getStartApplyArraignmentTime, reqVO.getStartApplyArraignmentTime())
            .betweenIfPresent(BringInterrogationDO::getEndApplyArraignmentTime, reqVO.getEndApplyArraignmentTime())
            .eqIfPresent(BringInterrogationDO::getArraignmentLetterNumber, reqVO.getArraignmentLetterNumber())
            .eqIfPresent(BringInterrogationDO::getEvidenceType, reqVO.getEvidenceType())
            .eqIfPresent(BringInterrogationDO::getEvidenceNumber, reqVO.getEvidenceNumber())
            .eqIfPresent(BringInterrogationDO::getEvidenceUrl, reqVO.getEvidenceUrl())
            .eqIfPresent(BringInterrogationDO::getMinorAgent, reqVO.getMinorAgent())
            .eqIfPresent(BringInterrogationDO::getMinorGuardian, reqVO.getMinorGuardian())
            .eqIfPresent(BringInterrogationDO::getTranslator, reqVO.getTranslator())
            .eqIfPresent(BringInterrogationDO::getOtherProfessionals, reqVO.getOtherProfessionals())
            .eqIfPresent(BringInterrogationDO::getProfessionalCertUrl, reqVO.getProfessionalCertUrl())
            .eqIfPresent(BringInterrogationDO::getTjjglx, reqVO.getTjjglx())
            .eqIfPresent(BringInterrogationDO::getTjjgmc, reqVO.getTjjgmc())
            .eqIfPresent(BringInterrogationDO::getStatus, reqVO.getStatus())
            .betweenIfPresent(BringInterrogationDO::getCheckInTime, reqVO.getCheckInTime())
            .eqIfPresent(BringInterrogationDO::getCheckInPoliceSfzh, reqVO.getCheckInPoliceSfzh())
            .eqIfPresent(BringInterrogationDO::getCheckInPolice, reqVO.getCheckInPolice())
            .eqIfPresent(BringInterrogationDO::getEscortingPoliceSfzh, reqVO.getEscortingPoliceSfzh())
            .eqIfPresent(BringInterrogationDO::getEscortingPolice, reqVO.getEscortingPolice())
            .betweenIfPresent(BringInterrogationDO::getEscortingTime, reqVO.getEscortingTime())
            .eqIfPresent(BringInterrogationDO::getEscortingOperatorSfzh, reqVO.getEscortingOperatorSfzh())
            .eqIfPresent(BringInterrogationDO::getEscortingOperator, reqVO.getEscortingOperator())
            .betweenIfPresent(BringInterrogationDO::getEscortingOperatorTime, reqVO.getEscortingOperatorTime())
            .eqIfPresent(BringInterrogationDO::getInspectionResult, reqVO.getInspectionResult())
            .eqIfPresent(BringInterrogationDO::getProhibitedItems, reqVO.getProhibitedItems())
            .eqIfPresent(BringInterrogationDO::getPhysicalExam, reqVO.getPhysicalExam())
            .eqIfPresent(BringInterrogationDO::getAbnormalSituations, reqVO.getAbnormalSituations())
            .eqIfPresent(BringInterrogationDO::getProhibitedItemsImgUrl, reqVO.getProhibitedItemsImgUrl())
            .eqIfPresent(BringInterrogationDO::getPhysicalExamImgUrl, reqVO.getPhysicalExamImgUrl())
            .eqIfPresent(BringInterrogationDO::getAbnormalSituationsImgUrl, reqVO.getAbnormalSituationsImgUrl())
            .eqIfPresent(BringInterrogationDO::getIsProhibitedItems, reqVO.getIsProhibitedItems())
            .betweenIfPresent(BringInterrogationDO::getInspectionTime, reqVO.getInspectionTime())
            .eqIfPresent(BringInterrogationDO::getInspectorSfzh, reqVO.getInspectorSfzh())
            .eqIfPresent(BringInterrogationDO::getInspector, reqVO.getInspector())
            .betweenIfPresent(BringInterrogationDO::getArraignmentStartTime, reqVO.getArraignmentStartTime())
            .betweenIfPresent(BringInterrogationDO::getArraignmentEndTime, reqVO.getArraignmentEndTime())
            .eqIfPresent(BringInterrogationDO::getReturnInspectorSfzh, reqVO.getReturnInspectorSfzh())
            .eqIfPresent(BringInterrogationDO::getReturnInspector, reqVO.getReturnInspector())
            .betweenIfPresent(BringInterrogationDO::getReturnInspectionTime, reqVO.getReturnInspectionTime())
            .eqIfPresent(BringInterrogationDO::getReturnInspectionResult, reqVO.getReturnInspectionResult())
            .betweenIfPresent(BringInterrogationDO::getReturnTime, reqVO.getReturnTime())
            .eqIfPresent(BringInterrogationDO::getReturnPoliceSfzh, reqVO.getReturnPoliceSfzh())
            .eqIfPresent(BringInterrogationDO::getReturnPolice, reqVO.getReturnPolice())
            .eqIfPresent(BringInterrogationDO::getReturnOperatorSfzh, reqVO.getReturnOperatorSfzh())
            .eqIfPresent(BringInterrogationDO::getReturnOperator, reqVO.getReturnOperator())
            .betweenIfPresent(BringInterrogationDO::getReturnOperatorTime, reqVO.getReturnOperatorTime())
            .eqIfPresent(BringInterrogationDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(BringInterrogationDO::getTaskId, reqVO.getTaskId())
            .eqIfPresent(BringInterrogationDO::getReturnProhibitedItems, reqVO.getReturnProhibitedItems())
            .eqIfPresent(BringInterrogationDO::getReturnPhysicalExam, reqVO.getReturnPhysicalExam())
            .eqIfPresent(BringInterrogationDO::getReturnAbnormalSituations, reqVO.getReturnAbnormalSituations())
        .orderByDesc(BringInterrogationDO::getAddTime));    }


    }
