package com.rs.module.acp.controller.app.pi.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "仓内外屏-巡视管控-巡视登记-手动新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PatrolRecordAppSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("监室号")
    private String roomId;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("巡控人员身份证号")
    private String patrolStaffSfzh;

    @ApiModelProperty("巡控人员")
    private String patrolStaff;

    @ApiModelProperty("登记内容")
    @NotEmpty(message = "登记内容不能为空")
    private String recordContent;

    @ApiModelProperty("是否岗位协同：1-是/0-否")
    @NotNull(message = "是否岗位协同：1-是/0-否不能为空")
    private Short isPostCoordination;

    @ApiModelProperty("岗位协同，多选项 ZD_XSGKXTGW")
    private String coordinationPosts;

    @ApiModelProperty("岗位协同人员名称")
    private String coordinationPostsName;

    @ApiModelProperty("登记经办人")
    @NotEmpty(message = "登记经办人不能为空")
    private String operatorSfzh;

    @ApiModelProperty("登记经办人姓名")
    @NotEmpty(message = "登记经办人姓名不能为空")
    private String operatorXm;

    @ApiModelProperty("登记经办时间")
    @NotNull(message = "登记经办时间不能为空")
    private Date operatorTime;
    @ApiModelProperty("岗位协同推送对象证件号码")
    private String pushTargetIdCard;
    @ApiModelProperty("岗位协同推送对象")
    private String pushTarget;
    @ApiModelProperty("岗位协同推送内容")
    private String pushContent;
}
