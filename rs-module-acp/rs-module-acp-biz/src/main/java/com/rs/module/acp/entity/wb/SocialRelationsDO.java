package com.rs.module.acp.entity.wb;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-窗口业务-社会关系 DO
 *
 * <AUTHOR>
 */
@TableName("acp_wb_social_relations")
@KeySequence("acp_wb_social_relations_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_wb_social_relations")
public class SocialRelationsDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 第一位家属姓名
     */
    private String name;
    /**
     * 第一位家属性别
     */
    private String gender;
    /**
     * 第一位家属证件类型
     */
    private String idType;
    /**
     * 第一位家属证件号码
     */
    private String idNumber;
    /**
     * 第一位家属与被会见人社会关系
     */
    private String relationship;
    /**
     * 第一位家属联系方式
     */
    private String contact;
    /**
     * 第一位家属工作单位
     */
    private String workUnit;
    /**
     * 第一位家属居住地址
     */
    private String address;
    /**
     * 职业
     */
    private String occupation;
    /**
     * 照片
     */
    private String imageUrl;
    /**
     * 关系证明附件
     */
    private String relationsAttch;

}
