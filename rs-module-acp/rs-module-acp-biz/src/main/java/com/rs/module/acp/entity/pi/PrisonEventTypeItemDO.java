package com.rs.module.acp.entity.pi;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-巡视管控-所情事件类型明细项 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_prison_event_type_item")
@KeySequence("acp_pi_prison_event_type_item_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_prison_event_type_item")
public class PrisonEventTypeItemDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 事件项名称
     */
    private String itemName;
    /**
     * 上级ID
     */
    private String parentId;
    /**
     * 级别代码（1：一级，2：二级，3：三级）
     */
    private Integer levelCode;
    /**
     * 扣分值
     */
    private Integer deductPoint;
    /**
     * 排序号
     */
    private Integer orderId;
    /**
     * 所情事件类型ID
     */
    private String typeId;

}
