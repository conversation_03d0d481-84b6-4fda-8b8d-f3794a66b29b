package com.rs.module.acp.service.gj.conflict;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictFollowupRespVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictFollowupSaveReqVO;
import com.rs.module.acp.entity.gj.conflict.ConflictFollowupDO;

/**
 * 实战平台-管教业务-社会矛盾回访 Service 接口
 *
 * <AUTHOR>
 */
public interface ConflictFollowupService extends IBaseService<ConflictFollowupDO>{

    /**
     * 创建实战平台-管教业务-社会矛盾回访
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createConflictFollowup(ConflictFollowupSaveReqVO createReqVO);

    /**
     * 获得实战平台-管教业务-社会矛盾回访
     *
     * @param eventCode 编号
     * @return 实战平台-管教业务-社会矛盾回访
     */
    ConflictFollowupRespVO getConflictFollowup(String eventCode);


}
