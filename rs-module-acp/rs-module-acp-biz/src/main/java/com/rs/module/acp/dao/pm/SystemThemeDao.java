package com.rs.module.acp.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.pm.SystemThemeDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-主题-主题配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface SystemThemeDao extends IBaseDao<SystemThemeDO> {


    default PageResult<SystemThemeDO> selectPage(SystemThemePageReqVO reqVO) {
        Page<SystemThemeDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<SystemThemeDO> wrapper = new LambdaQueryWrapperX<SystemThemeDO>()
            .eqIfPresent(SystemThemeDO::getTheme, reqVO.getTheme())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(SystemThemeDO::getAddTime);
        }
        Page<SystemThemeDO> systemThemePage = selectPage(page, wrapper);
        return new PageResult<>(systemThemePage.getRecords(), systemThemePage.getTotal());
    }
    default List<SystemThemeDO> selectList(SystemThemeListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SystemThemeDO>()
            .eqIfPresent(SystemThemeDO::getTheme, reqVO.getTheme())
        .orderByDesc(SystemThemeDO::getAddTime));    }


    }
