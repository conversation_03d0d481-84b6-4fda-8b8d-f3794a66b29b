package com.rs.module.acp.service.gj.punishment;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentMeasureListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentMeasurePageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentMeasureSaveReqVO;
import com.rs.module.acp.entity.gj.PunishmentMeasureDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-处罚呈批关联措施 Service 接口
 *
 * <AUTHOR>
 */
public interface PunishmentMeasureService extends IBaseService<PunishmentMeasureDO>{

    /**
     * 创建实战平台-管教业务-处罚呈批关联措施
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPunishmentMeasure(@Valid PunishmentMeasureSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-处罚呈批关联措施
     *
     * @param updateReqVO 更新信息
     */
    void updatePunishmentMeasure(@Valid PunishmentMeasureSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-处罚呈批关联措施
     *
     * @param id 编号
     */
    void deletePunishmentMeasure(String id);

    /**
     * 获得实战平台-管教业务-处罚呈批关联措施
     *
     * @param id 编号
     * @return 实战平台-管教业务-处罚呈批关联措施
     */
    PunishmentMeasureDO getPunishmentMeasure(String id);

    /**
    * 获得实战平台-管教业务-处罚呈批关联措施分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-处罚呈批关联措施分页
    */
    PageResult<PunishmentMeasureDO> getPunishmentMeasurePage(PunishmentMeasurePageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-处罚呈批关联措施列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-处罚呈批关联措施列表
    */
    List<PunishmentMeasureDO> getPunishmentMeasureList(PunishmentMeasureListReqVO listReqVO);


}
