package com.rs.module.acp.entity.pi;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 实战平台-巡视管控-夜间开启监室门 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_night_door_record")
@KeySequence("acp_pi_night_door_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_night_door_record")
public class NightDoorRecordDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监室ID
     */
    private String roomId;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 开门原因
     */
    private String openReason;
    /**
     * 申请人类型
     */
    private String applyType;
    /**
     * 申请人身份证号
     */
    private String applyUserSfzh;
    /**
     * 申请人
     */
    private String applyUser;
    /**
     * 申请时间
     */
    private Date applyTime;
    /**
     * 申请开门日期
     */
    private Date applyOpenDoorDate;
    /**
     * 进入监室民辅警身份证号，多个用逗号分隔
     */
    private String entryPoliceSfzh;
    /**
     * 进入监室民辅警名称，多个用逗号分隔
     */
    private String entryPoliceName;
    /**
     * 监室外警戒民辅警身份证号，多个用逗号分隔
     */
    private String guardPoliceSfzh;
    /**
     * 监室外警戒民辅名称，多个用逗号分隔
     */
    private String guardPoliceName;
    /**
     * 实际开门时间
     */
    private Date actualOpenTime;
    /**
     * 实际关门时间
     */
    private Date actualCloseTime;
    /**
     * 开门时长（分钟）
     */
    private Integer durationMinutes;
    /**
     * 状态
     */
    private String status;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

}
