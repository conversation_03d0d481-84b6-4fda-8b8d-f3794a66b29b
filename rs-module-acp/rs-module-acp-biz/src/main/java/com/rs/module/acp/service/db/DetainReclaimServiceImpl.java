package com.rs.module.acp.service.db;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.DetainReclaimDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.db.DetainReclaimDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-羁押业务-收回登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DetainReclaimServiceImpl extends BaseServiceImpl<DetainReclaimDao, DetainReclaimDO> implements DetainReclaimService {

    @Resource
    private DetainReclaimDao detainReclaimDao;

    @Override
    public String createDetainReclaim(DetainReclaimSaveReqVO createReqVO) {
        // 根据监管人员编号查询已有记录
        DetainReclaimDO existing = getByJgrybh(createReqVO.getJgrybm());

        DetainReclaimDO detainReclaim;
        boolean isUpdate = false;

        if (existing != null) {
            // 更新已有记录
            detainReclaim = BeanUtils.toBean(createReqVO, DetainReclaimDO.class);
            detainReclaim.setId(existing.getId()); // 保留原ID
            detainReclaim.setRssj(existing.getRssj()); // 保留原有 rssj 字段
            detainReclaimDao.updateById(detainReclaim);
            isUpdate = true;
        } else {
            // 插入新记录
            detainReclaim = BeanUtils.toBean(createReqVO, DetainReclaimDO.class);
            detainReclaim.setId(UUID.randomUUID().toString().replaceAll("-", ""));
            if (detainReclaim.getRssj() == null) {
                detainReclaim.setRssj(new Date()); // 默认设置当前时间
            }
            detainReclaimDao.insert(detainReclaim);
        }

        return detainReclaim.getId();
    }


    @Override
    public void updateDetainReclaim(DetainReclaimSaveReqVO updateReqVO) {
        // 校验存在
        validateDetainReclaimExists(updateReqVO.getId());
        // 更新
        DetainReclaimDO updateObj = BeanUtils.toBean(updateReqVO, DetainReclaimDO.class);
        detainReclaimDao.updateById(updateObj);
    }

    @Override
    public void deleteDetainReclaim(String id) {
        // 校验存在
        validateDetainReclaimExists(id);
        // 删除
        detainReclaimDao.deleteById(id);
    }

    private void validateDetainReclaimExists(String id) {
        if (detainReclaimDao.selectById(id) == null) {
            throw new ServerException("实战平台-羁押业务-收回登记数据不存在");
        }
    }

    @Override
    public DetainReclaimDO getDetainReclaim(String id) {
        return detainReclaimDao.selectById(id);
    }

    @Override
    public PageResult<DetainReclaimDO> getDetainReclaimPage(DetainReclaimPageReqVO pageReqVO) {
        return detainReclaimDao.selectPage(pageReqVO);
    }

    @Override
    public List<DetainReclaimDO> getDetainReclaimList(DetainReclaimListReqVO listReqVO) {
        return detainReclaimDao.selectList(listReqVO);
    }

    @Override
    public DetainReclaimDO getByJgrybh(String jgrybh) {
        DetainReclaimListReqVO listReqVO = new DetainReclaimListReqVO();
        listReqVO.setJgrybm(jgrybh);
        List<DetainReclaimDO> list = detainReclaimDao.selectList(listReqVO);
        if(list!=null&& !list.isEmpty()){
            return list.get(0);
        }
        return null;
    }


}
