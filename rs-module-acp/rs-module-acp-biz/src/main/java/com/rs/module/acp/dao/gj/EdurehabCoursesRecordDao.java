package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesRecordListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesRecordPageReqVO;
import com.rs.module.acp.entity.gj.EdurehabCoursesRecordDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-教育康复课程记录 Dao
*
* <AUTHOR>
*/
@Mapper
public interface EdurehabCoursesRecordDao extends IBaseDao<EdurehabCoursesRecordDO> {


    default PageResult<EdurehabCoursesRecordDO> selectPage(EdurehabCoursesRecordPageReqVO reqVO) {
        Page<EdurehabCoursesRecordDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<EdurehabCoursesRecordDO> wrapper = new LambdaQueryWrapperX<EdurehabCoursesRecordDO>()
            .eqIfPresent(EdurehabCoursesRecordDO::getEdurehabCoursesPlanId, reqVO.getEdurehabCoursesPlanId())
            .betweenIfPresent(EdurehabCoursesRecordDO::getCoursesDate, reqVO.getCoursesDate())
            .eqIfPresent(EdurehabCoursesRecordDO::getAreaId, reqVO.getAreaId())
            .likeIfPresent(EdurehabCoursesRecordDO::getAreaName, reqVO.getAreaName())
            .eqIfPresent(EdurehabCoursesRecordDO::getTimeSlotCode, reqVO.getTimeSlotCode())
            .eqIfPresent(EdurehabCoursesRecordDO::getTimeSlotStartTime, reqVO.getTimeSlotStartTime())
            .eqIfPresent(EdurehabCoursesRecordDO::getTimeSlotEndTime, reqVO.getTimeSlotEndTime())
            .eqIfPresent(EdurehabCoursesRecordDO::getCoursesCode, reqVO.getCoursesCode())
            .likeIfPresent(EdurehabCoursesRecordDO::getCoursesName, reqVO.getCoursesName())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(EdurehabCoursesRecordDO::getAddTime);
        }
        Page<EdurehabCoursesRecordDO> edurehabCoursesRecordPage = selectPage(page, wrapper);
        return new PageResult<>(edurehabCoursesRecordPage.getRecords(), edurehabCoursesRecordPage.getTotal());
    }
    default List<EdurehabCoursesRecordDO> selectList(EdurehabCoursesRecordListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<EdurehabCoursesRecordDO>()
            .eqIfPresent(EdurehabCoursesRecordDO::getEdurehabCoursesPlanId, reqVO.getEdurehabCoursesPlanId())
            .betweenIfPresent(EdurehabCoursesRecordDO::getCoursesDate, reqVO.getCoursesDate())
            .eqIfPresent(EdurehabCoursesRecordDO::getAreaId, reqVO.getAreaId())
            .likeIfPresent(EdurehabCoursesRecordDO::getAreaName, reqVO.getAreaName())
            .eqIfPresent(EdurehabCoursesRecordDO::getTimeSlotCode, reqVO.getTimeSlotCode())
            .eqIfPresent(EdurehabCoursesRecordDO::getTimeSlotStartTime, reqVO.getTimeSlotStartTime())
            .eqIfPresent(EdurehabCoursesRecordDO::getTimeSlotEndTime, reqVO.getTimeSlotEndTime())
            .eqIfPresent(EdurehabCoursesRecordDO::getCoursesCode, reqVO.getCoursesCode())
            .likeIfPresent(EdurehabCoursesRecordDO::getCoursesName, reqVO.getCoursesName())
        .orderByDesc(EdurehabCoursesRecordDO::getAddTime));    }


    }
