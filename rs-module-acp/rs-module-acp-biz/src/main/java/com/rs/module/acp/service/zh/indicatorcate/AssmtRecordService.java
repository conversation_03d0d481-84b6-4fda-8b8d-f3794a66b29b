package com.rs.module.acp.service.zh.indicatorcate;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmtRecordListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmtRecordPageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmtRecordRespVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmtRecordSaveReqVO;
import com.rs.module.acp.entity.zh.AssmtRecordDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 综合管理-绩效考核-记录 Service 接口
 *
 * <AUTHOR>
 */
public interface AssmtRecordService extends IBaseService<AssmtRecordDO>{

    /**
     * 创建综合管理-绩效考核-记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createAssmtRecord(@Valid AssmtRecordSaveReqVO createReqVO);

    /**
     * 更新综合管理-绩效考核-记录
     *
     * @param updateReqVO 更新信息
     */
    void updateAssmtRecord(@Valid AssmtRecordSaveReqVO updateReqVO);

    /**
     * 删除综合管理-绩效考核-记录
     *
     * @param id 编号
     */
    void deleteAssmtRecord(String id);

    /**
     * 获得综合管理-绩效考核-记录
     *
     * @param id 编号
     * @return 综合管理-绩效考核-记录
     */
    AssmtRecordRespVO getAssmtRecord(String id);

    /**
    * 获得综合管理-绩效考核-记录分页
    *
    * @param pageReqVO 分页查询
    * @return 综合管理-绩效考核-记录分页
    */
    PageResult<AssmtRecordDO> getAssmtRecordPage(AssmtRecordPageReqVO pageReqVO);

    /**
    * 获得综合管理-绩效考核-记录列表
    *
    * @param listReqVO 查询条件
    * @return 综合管理-绩效考核-记录列表
    */
    List<AssmtRecordDO> getAssmtRecordList(AssmtRecordListReqVO listReqVO);


}
