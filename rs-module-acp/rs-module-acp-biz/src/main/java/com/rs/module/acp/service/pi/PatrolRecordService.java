package com.rs.module.acp.service.pi;

import java.util.*;
import javax.validation.*;

import com.rs.adapter.bsp.api.dto.UserRespDTO;
import com.rs.module.acp.controller.admin.pi.vo.patrolrecord.*;
import com.rs.module.acp.controller.app.pi.vo.PatrolRecordAppSaveReqVO;
import com.rs.module.acp.entity.pi.PatrolRecordDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.base.entity.pm.AreaDO;

/**
 * 实战平台-巡视管控-巡视登记 Service 接口
 *
 * <AUTHOR>
 */
public interface PatrolRecordService extends IBaseService<PatrolRecordDO>{

    /**
     * 创建实战平台-巡视管控-巡视登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPatrolRecord(@Valid PatrolRecordSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-巡视登记
     *
     * @param updateReqVO 更新信息
     */
    void updatePatrolRecord(@Valid PatrolRecordSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-巡视登记
     *
     * @param id 编号
     */
    void deletePatrolRecord(String id);

    /**
     * 获得实战平台-巡视管控-巡视登记
     *
     * @param id 编号
     * @return 实战平台-巡视管控-巡视登记
     */
    PatrolRecordDO getPatrolRecord(String id);

    String manuallyCreatePatrolRecord(PatrolRecordSaveReqVO createReqVO);

    List<AreaDO> getAreaByAreaType(String orgCode,boolean isDefault);

    String commonCreatePatrolRecord(PatrolRecordCommonSaveReqVO createReqVO);

    List<PatrolRecordDO> getPatrolRecordList(String operatorSfzh, Date startTime, Date endTime);

    //查询当前所定时开始配置
    List<UserRespDTO> getUserListByCoordinationPosts(String coordinationPostsCode) throws Exception;

    String manuallyRegSave(PatrolRecordRegReqVO regReqVO);

    void regSave(PatrolRecordSaveReqVO recordSaveReqVO);

    String appCreatePatrolRecord(PatrolRecordAppSaveReqVO regReqVO);

    List<PatrolRecordDO> getXkdjList(String xkdj);
}
