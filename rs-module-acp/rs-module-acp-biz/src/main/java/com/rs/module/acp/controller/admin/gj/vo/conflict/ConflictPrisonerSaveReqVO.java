package com.rs.module.acp.controller.admin.gj.vo.conflict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-社会矛盾化解登记-关联在押人员新增/修改 Request VO")
@Data
public class ConflictPrisonerSaveReqVO {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("事件ID")
    private String eventId;
    @ApiModelProperty("事件编号")
    private String eventCode;
    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;
    @ApiModelProperty("是否为当事人(0: 否，1:是)")
    @NotNull(message = "是否为当事人(0: 否，1:是)不能为空")
    private Boolean partner;
    @ApiModelProperty("姓名")
    private String xm;
    @ApiModelProperty("在押人员确认状态（0：待确认、1：已确认）")
    private Integer confirmStatus;
    @ApiModelProperty("确认时间")
    private Date confirmTime;
    @ApiModelProperty("在押人员签名地址")
    private String prisonerSignature;
    @ApiModelProperty("是否已评价（0：未评价，1：已评价）")
    private Integer isRated;
    @ApiModelProperty("满意度评分（1：满意：0：不满意）")
    private Integer satisfactionScore;
    @ApiModelProperty("回访状态（0：待回访，1：已回访）")
    private Integer followupStatus;

}
