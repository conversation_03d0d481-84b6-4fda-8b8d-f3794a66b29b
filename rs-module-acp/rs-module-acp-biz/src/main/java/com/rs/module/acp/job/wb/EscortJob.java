package com.rs.module.acp.job.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.module.acp.entity.wb.ArraignmentDO;
import com.rs.module.acp.entity.wb.EscortDO;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.acp.service.wb.ArraignmentService;
import com.rs.module.acp.service.wb.EscortService;
import com.rs.module.acp.service.wb.WbCommonService;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.sys.MsgAddVO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.util.MsgUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class EscortJob {

    @Autowired
    private EscortService escortService;

    @Autowired
    private WbCommonService wbCommonService;

    /**
     * 提解日期当天凌晨12:00后，状态未变更为【已完成】，则为异常
     */
    @XxlJob("abnormalResolution")
    public void abnormalResolution(){
        Map<String, PrisonerVwRespVO> prisonerMap = new HashMap<>();
        XxlJobHelper.log("提解任务：-----开始-------");
        XxlJobHelper.log("提解任务：提解日期当天凌晨12:00后，状态未变更为【已完成】，则为异常");
        try {
            LambdaQueryWrapper<EscortDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.select(EscortDO::getId,EscortDO::getJgrybm,EscortDO::getApplyEscortDate);
            lambdaQueryWrapper.in(EscortDO::getStatus, Arrays.asList("0","1"))
                    .le(EscortDO::getAddTime,new Date());
            Integer pageNo = 1;
            Integer pageSize = 500;
            while (true){
                Page<EscortDO> page = new Page<>(pageNo,pageSize);
                IPage<EscortDO> arraignmentDOIPage = escortService.page(page,lambdaQueryWrapper);
                if(CollectionUtil.isEmpty(arraignmentDOIPage.getRecords())){
                    break;
                }
                List<String> idList = arraignmentDOIPage.getRecords().stream().map(EscortDO::getId).collect(Collectors.toList());
                LambdaUpdateWrapper<EscortDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.in(EscortDO::getId,idList).set(EscortDO::getStatus,"99");
                escortService.update(lambdaUpdateWrapper);

                //发送消息
                for(EscortDO escort:arraignmentDOIPage.getRecords()){
                    XxlJobHelper.log("提解任务：-----发送补录待办消息---start----");
                    wbCommonService.additionalRecordingReminder(JSONObject.parseObject(JSON.toJSONString(escort)), WbConstants.BUSINESS_TYPE_ESCORT);
                    XxlJobHelper.log("提解任务：-----发送补录待办消息----end---");
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            XxlJobHelper.log("提解任务异常：{}",e.getMessage());
        }
        XxlJobHelper.log("提解任务:-----结束------");
    }
}
