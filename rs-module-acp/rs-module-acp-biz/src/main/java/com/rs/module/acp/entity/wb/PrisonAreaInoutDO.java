package com.rs.module.acp.entity.wb;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-窗口业务-出入监区登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_wb_prison_area_inout")
@KeySequence("acp_wb_prison_area_inout_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_wb_prison_area_inout")
public class PrisonAreaInoutDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 姓名
     */
    private String xm;
    /**
     * 证件类型
     */
    private String zjlx;
    /**
     * 性别
     */
    private String xb;
    /**
     * 证件号码
     */
    private String zjhm;
    /**
     * 联系方式
     */
    private String lxfs;
    /**
     * 进入监区原因
     */
    private String jrjqry;
    /**
     * 身份背景调查
     */
    private String sfbjdc;
    /**
     * 预计来访时间
     */
    private Date yjlfsj;
    /**
     * 预计离所时间
     */
    private Date yjlssj;
    /**
     * 车辆号码
     */
    private String clhm;
    /**
     * 照片存储url
     */
    private String zpUrl;
    /**
     * 进入监区时间
     */
    private Date jrjqsj;
    /**
     * 进入监区安全检查信息
     */
    private String jrjqaqjcxx;
    /**
     * 进入监区安全检查人身份证号
     */
    private String jrjqaqjcrsfzh;
    /**
     * 进入监区安全检查人姓名
     */
    private String jrjqaqjcrxm;
    /**
     * 离开监区时间
     */
    private Date lkjqsj;
    /**
     * 离开监区安全检查信息
     */
    private String lkjqaqjcxx;
    /**
     * 离开监区安全检查人身份证号
     */
    private String lkjqaqjcrsfzh;
    /**
     * 离开监区安全检查人姓名
     */
    private String lkjqaqjcrxm;
    /**
     * 办理状态
     */
    private String status;

}
