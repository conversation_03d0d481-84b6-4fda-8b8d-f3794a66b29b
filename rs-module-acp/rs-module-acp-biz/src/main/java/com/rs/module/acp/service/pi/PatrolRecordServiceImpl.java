package com.rs.module.acp.service.pi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.UserApi;
import com.rs.adapter.bsp.api.dto.UserRespDTO;
import com.rs.module.acp.controller.admin.pi.vo.patrolrecord.*;
import com.rs.module.acp.controller.app.pi.vo.PatrolRecordAppSaveReqVO;
import com.rs.module.acp.entity.gj.PunishmentExtendDO;
import com.rs.module.acp.util.InspectionControlUtil;
import com.rs.module.base.controller.admin.pm.vo.AreaListReqVO;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.base.service.pm.AreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.pi.PatrolRecordDO;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.PatrolRecordDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-巡视管控-巡视登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PatrolRecordServiceImpl extends BaseServiceImpl<PatrolRecordDao, PatrolRecordDO> implements PatrolRecordService {

    @Resource
    private PatrolRecordDao patrolRecordDao;
    @Autowired
    private AreaService areaService;
    @Autowired
    private UserApi userApi;
    @Override
    public String createPatrolRecord(PatrolRecordSaveReqVO createReqVO) {
        // 插入
        PatrolRecordDO patrolRecord = BeanUtils.toBean(createReqVO, PatrolRecordDO.class);
        patrolRecordDao.insert(patrolRecord);
        // 返回
        return patrolRecord.getId();
    }

    @Override
    public void updatePatrolRecord(PatrolRecordSaveReqVO updateReqVO) {
        // 校验存在
        validatePatrolRecordExists(updateReqVO.getId());
        // 更新
        PatrolRecordDO updateObj = BeanUtils.toBean(updateReqVO, PatrolRecordDO.class);
        patrolRecordDao.updateById(updateObj);
    }

    @Override
    public void deletePatrolRecord(String id) {
        // 校验存在
        validatePatrolRecordExists(id);
        // 删除
        patrolRecordDao.deleteById(id);
    }

    private void validatePatrolRecordExists(String id) {
        if (patrolRecordDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-巡视登记数据不存在");
        }
    }

    @Override
    public PatrolRecordDO getPatrolRecord(String id) {
        return patrolRecordDao.selectById(id);
    }

    /**
     * 页面手动登记保存
     * @param saveReqVO
     * @return
     */
    @Override
    public String manuallyCreatePatrolRecord(PatrolRecordSaveReqVO saveReqVO) {
        // 插入
        PatrolRecordDO patrolRecord = BeanUtils.toBean(saveReqVO, PatrolRecordDO.class);
        patrolRecord.setRecordType("02");//手动
        patrolRecord.setStatus("03");
        patrolRecordDao.insert(patrolRecord);

        return patrolRecord.getId();
    }

    /**
     * 岗位协同业务操作
     * @param saveReqVO
     */
    private void postCoordination(PatrolRecordSaveReqVO saveReqVO){
        if(saveReqVO.getIsPostCoordination() != 1) return;
        //推送消息
        saveReqVO.getCoordinationPosts();
        saveReqVO.getCoordinationPostsName();
    }

    /**
     * 查询当前所有的分控室
     * @param orgCode 指定orgCode isDefault=false
     * @param isDefault 未传入orgCode 是否赋值当前用户orgCode
     * @return
     */
    @Override
    public List<AreaDO> getAreaByAreaType(String orgCode,boolean isDefault){
        AreaListReqVO listReqVO = new AreaListReqVO();
        if(isDefault) orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        if(StringUtil.isNotEmpty(orgCode)) listReqVO.setOrgCode(orgCode);
        listReqVO.setAreaType("0038");
        listReqVO.setIsDel(0);
        return areaService.getAreaList(listReqVO);
    }
    /**
     * 第三方业务模块写入巡控任务
     * @param saveReqVO
     * @return
     */
    @Override
    public String commonCreatePatrolRecord(PatrolRecordCommonSaveReqVO saveReqVO) {
        // 插入
        PatrolRecordDO patrolRecord = BeanUtils.toBean(saveReqVO, PatrolRecordDO.class);
        patrolRecord.setRecordType("03");//其他模块业务写入
        patrolRecord.setStatus("01");
        patrolRecordDao.insert(patrolRecord);
        return patrolRecord.getId();
    }

    /**
     * 查询当前巡控人员指定时间段内的巡视登记记录
     * @param operatorSfzh
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public List<PatrolRecordDO> getPatrolRecordList(String operatorSfzh, Date startTime, Date endTime) {
        return patrolRecordDao.selectList(
                new LambdaQueryWrapper<PatrolRecordDO>()
                        .eq(PatrolRecordDO::getOperatorSfzh, operatorSfzh)
                        .le(PatrolRecordDO::getOperatorTime,  endTime)
                        .ge(PatrolRecordDO::getOperatorTime, startTime)
                        .eq(PatrolRecordDO::getIsDel, 0)
                        .orderByAsc(PatrolRecordDO::getOperatorTime)
        );
    }

    //定时任务生成巡视登记 area_type=‘03’ where org_code=''
    // 查询当前所巡控室配置，设置待办来源，待办推送人

    /*
        登记类型 字典定义：1：巡视登记，2：其他业务接口(所情管理，加戴戒具，一级重大风险)，系统推送
        巡控任务登记人
        Q1:一个所是否存在多个分控室  如何查询当前人员对应的分控室   巡控岗对应分控室设置功能
        Q2:岗位协同自动类型推送待办消息 ..勾选后是否展示对应人员可选进行推送
        Q3:消息规则:无监室号巡控任务 如何拼接消息
        Q4:岗位协同操作是否跟违规登记联动
     */

    //其他业务接口巡视登记

    //查询当前所定时开始配置

    /**
     * 根据岗位协同字典编码查询人员
     * @param coordinationPostsCode
     * @return
     * @throws Exception
     */
    @Override
    public List<UserRespDTO> getUserListByCoordinationPosts(String coordinationPostsCode) throws Exception {
        return new InspectionControlUtil(userApi).getUserListByCoordinationPosts(coordinationPostsCode);
    }

    /**
     * pc 手动新增
     * @param regReqVO
     * @return
     */
    @Override
    public String manuallyRegSave(PatrolRecordRegReqVO regReqVO){
        PatrolRecordDO entity = BeanUtils.toBean(regReqVO, PatrolRecordDO.class);
        entity.setRecordType("02");//手动
        entity.setStatus("03");
        SessionUser user = SessionUserUtil.getSessionUser();
        entity.setTodoSource("");//待办来源
        entity.setTodoReason("");//待办事由
        entity.setTodoPerson("");//待办推送人
        entity.setTodoPersonSfzh("");//待办推送人身份证号
        entity.setTodoPushTime(null);//待办推送时间
        entity.setTodoPost("");//待办推送岗位
        if(StringUtil.isEmpty(entity.getOperatorSfzh())) entity.setOperatorSfzh(user.getIdCard());//登记经办人
        if(StringUtil.isEmpty(entity.getOperatorXm())) entity.setOperatorXm(user.getName());
        if(StringUtil.isEmpty(entity.getPatrolStaffSfzh())) entity.setPatrolStaffSfzh(user.getIdCard());//登记经办人
        if(StringUtil.isEmpty(entity.getPatrolStaff())) entity.setPatrolStaff(user.getName());
        if(entity.getOperatorTime() == null) entity.setOperatorTime(new Date());

        new InspectionControlUtil(userApi).dealIsPostCoordination(entity);
        patrolRecordDao.insert(entity);
        return entity.getId();
    }

    /**
     * 定时推送/其他业务推送后巡视登记
     * @param recordSaveReqVO
     */
    @Override
    public void regSave(PatrolRecordSaveReqVO recordSaveReqVO){
        validatePatrolRecordExists(recordSaveReqVO.getId());
        SessionUser user = SessionUserUtil.getSessionUser();
        PatrolRecordDO entity = patrolRecordDao.selectById(recordSaveReqVO.getId());
        BeanUtils.copyProperties(recordSaveReqVO,entity);
        if(StringUtil.isEmpty(entity.getOperatorSfzh())) entity.setOperatorSfzh(user.getIdCard());//登记经办人
        if(StringUtil.isEmpty(entity.getOperatorXm())) entity.setOperatorXm(user.getName());
        if(entity.getOperatorTime() == null) entity.setOperatorTime(new Date());
        entity.setStatus("03");
        patrolRecordDao.updateById(entity);
        //InspectionControl
    }
    public void initOpertData(PatrolRecordDO entity){
        SessionUser user = SessionUserUtil.getSessionUser();
        if(StringUtil.isEmpty(entity.getOperatorSfzh())) entity.setOperatorSfzh(user.getIdCard());//登记经办人
        if(StringUtil.isEmpty(entity.getOperatorXm())) entity.setOperatorXm(user.getName());
        if(StringUtil.isEmpty(entity.getPatrolStaffSfzh())) entity.setPatrolStaffSfzh(user.getIdCard());//登记经办人
        if(StringUtil.isEmpty(entity.getPatrolStaff())) entity.setPatrolStaff(user.getName());
        if(entity.getOperatorTime() == null) entity.setOperatorTime(new Date());
    }
    @Override
    public String appCreatePatrolRecord(PatrolRecordAppSaveReqVO regReqVO) {
        PatrolRecordDO entity = BeanUtils.toBean(regReqVO, PatrolRecordDO.class);
        initOpertData(entity);
        entity.setRecordType("02");//手动
        entity.setStatus("03");
        entity.setTodoSource("");//待办来源
        entity.setTodoReason("");//待办事由
        entity.setTodoPerson("");//待办推送人
        entity.setTodoPersonSfzh("");//待办推送人身份证号
        entity.setTodoPushTime(null);//待办推送时间
        entity.setTodoPost("");//待办推送岗位
        // 根据当前监室id查询对应的分控室
        /*entity.setPatrolRoomId();
        entity.setPatrolRoomName();*/
        new InspectionControlUtil(userApi).dealIsPostCoordination(entity);
        patrolRecordDao.insert(entity);
        return entity.getId();
    }

    @Override
    public List<PatrolRecordDO> getXkdjList(String xkdj) {
        if (StringUtil.isEmpty(xkdj)) return new ArrayList<>();
        return patrolRecordDao.selectList(new LambdaQueryWrapper<PatrolRecordDO>().in(PatrolRecordDO::getId, xkdj.split(","))
                .orderByAsc(PatrolRecordDO::getUpdateTime));
    }
}
