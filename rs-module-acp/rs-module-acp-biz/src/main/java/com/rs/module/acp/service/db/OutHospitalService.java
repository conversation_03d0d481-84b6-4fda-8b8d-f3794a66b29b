package com.rs.module.acp.service.db;

import java.util.*;
import javax.validation.*;
import javax.validation.constraints.NotEmpty;

import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.OutHospitalDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-收押业务-出所就医 Service 接口
 *
 * <AUTHOR>
 */
public interface OutHospitalService extends IBaseService<OutHospitalDO>{

    /**
     * 创建实战平台-收押业务-出所就医
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createOutHospital(@Valid OutHospitalSaveReqVO createReqVO);

    /**
     * 更新实战平台-收押业务-出所就医
     *
     * @param updateReqVO 更新信息
     */
    void updateOutHospital(@Valid OutHospitalSaveReqVO updateReqVO);

    /**
     * 删除实战平台-收押业务-出所就医
     *
     * @param id 编号
     */
    void deleteOutHospital(String id);

    /**
     * 获得实战平台-收押业务-出所就医
     *
     * @param id 编号
     * @return 实战平台-收押业务-出所就医
     */
    OutHospitalDO getOutHospital(String id);

    /**
    * 获得实战平台-收押业务-出所就医分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-收押业务-出所就医分页
    */
    PageResult<OutHospitalDO> getOutHospitalPage(OutHospitalPageReqVO pageReqVO);

    /**
    * 获得实战平台-收押业务-出所就医列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-收押业务-出所就医列表
    */
    List<OutHospitalDO> getOutHospitalList(OutHospitalListReqVO listReqVO);


    String doctorRegister(@Valid OutHospitalSaveReqVO updateReqVO);

    String jjsyRegister(@Valid OutHospitalSaveReqVO updateReqVO);

    String qwap(@Valid OutHospitalSaveReqVO updateReqVO);

    OutHospitalDO getPrisonerInfo(@NotEmpty(message = "监管人员编码不能为空") String jgrybm,String currentStep);

    String leaderRegister(@Valid OutHospitalSaveReqVO updateReqVO);

    String confirmByBack(@Valid OutHospitalSaveReqVO updateReqVO);

    String confirmByLeave(@Valid OutHospitalSaveReqVO updateReqVO);

    String qwapBack(@Valid OutHospitalSaveReqVO updateReqVO);

    String leaderRegisterBack(@Valid OutHospitalSaveReqVO updateReqVO);
}
