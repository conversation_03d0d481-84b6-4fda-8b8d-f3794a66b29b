package com.rs.module.acp.dao.zh;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.zh.DutySuperviseRecordSigninDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.zh.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 综合管理-值班管理-值班督导记录签至 Dao
*
* <AUTHOR>
*/
@Mapper
public interface DutySuperviseRecordSigninDao extends IBaseDao<DutySuperviseRecordSigninDO> {


    default PageResult<DutySuperviseRecordSigninDO> selectPage(DutySuperviseRecordSigninPageReqVO reqVO) {
        Page<DutySuperviseRecordSigninDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<DutySuperviseRecordSigninDO> wrapper = new LambdaQueryWrapperX<DutySuperviseRecordSigninDO>()
            .eqIfPresent(DutySuperviseRecordSigninDO::getDutySuperviseRecordId, reqVO.getDutySuperviseRecordId())
            .eqIfPresent(DutySuperviseRecordSigninDO::getPoliceId, reqVO.getPoliceId())
            .likeIfPresent(DutySuperviseRecordSigninDO::getPoliceName, reqVO.getPoliceName())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(DutySuperviseRecordSigninDO::getAddTime);
        }
        Page<DutySuperviseRecordSigninDO> dutySuperviseRecordSigninPage = selectPage(page, wrapper);
        return new PageResult<>(dutySuperviseRecordSigninPage.getRecords(), dutySuperviseRecordSigninPage.getTotal());
    }
    default List<DutySuperviseRecordSigninDO> selectList(DutySuperviseRecordSigninListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DutySuperviseRecordSigninDO>()
            .eqIfPresent(DutySuperviseRecordSigninDO::getDutySuperviseRecordId, reqVO.getDutySuperviseRecordId())
            .eqIfPresent(DutySuperviseRecordSigninDO::getPoliceId, reqVO.getPoliceId())
            .likeIfPresent(DutySuperviseRecordSigninDO::getPoliceName, reqVO.getPoliceName())
        .orderByDesc(DutySuperviseRecordSigninDO::getAddTime));    }


    }
