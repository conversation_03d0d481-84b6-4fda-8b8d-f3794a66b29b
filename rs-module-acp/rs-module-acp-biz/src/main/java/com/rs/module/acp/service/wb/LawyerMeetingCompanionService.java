package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.LawyerMeetingCompanionDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-律师会见同行登记 Service 接口
 *
 * <AUTHOR>
 */
public interface LawyerMeetingCompanionService extends IBaseService<LawyerMeetingCompanionDO>{

    /**
     * 创建实战平台-窗口业务-律师会见同行登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createLawyerMeetingCompanion(@Valid LawyerMeetingCompanionSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-律师会见同行登记
     *
     * @param updateReqVO 更新信息
     */
    void updateLawyerMeetingCompanion(@Valid LawyerMeetingCompanionSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-律师会见同行登记
     *
     * @param id 编号
     */
    void deleteLawyerMeetingCompanion(String id);

    /**
     * 获得实战平台-窗口业务-律师会见同行登记
     *
     * @param id 编号
     * @return 实战平台-窗口业务-律师会见同行登记
     */
    LawyerMeetingCompanionDO getLawyerMeetingCompanion(String id);

    /**
    * 获得实战平台-窗口业务-律师会见同行登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-律师会见同行登记分页
    */
    PageResult<LawyerMeetingCompanionDO> getLawyerMeetingCompanionPage(LawyerMeetingCompanionPageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-律师会见同行登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-律师会见同行登记列表
    */
    List<LawyerMeetingCompanionDO> getLawyerMeetingCompanionList(LawyerMeetingCompanionListReqVO listReqVO);
    /**
     * 获得实战平台-窗口业务-保存律师会见同行登记列表
     *
     * @param lawyerMeetingId 会见登记ID
     * @param companionList 创建信息
     * @return 实战平台-窗口业务-律师会见同行登记列表
     */
    boolean saveLawyerMeetingCompanionList(String lawyerMeetingId,List<LawyerMeetingCompanionSaveReqVO> companionList);

    /**
     * 获得实战平台-窗口业务-获取律师会见同行人记录
     *
     * @param lawyerMeetingId 会见登记ID
     * @return 实战平台-窗口业务-律师会见同行登记列表
     */
    List<LawyerMeetingCompanionRespVO> getCompanionByLawyerMeetingId(String lawyerMeetingId);
}
