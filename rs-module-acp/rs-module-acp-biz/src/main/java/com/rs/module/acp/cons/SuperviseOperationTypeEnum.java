package com.rs.module.acp.cons;

/**
 * 督导操作类型枚举
 */
public enum SuperviseOperationTypeEnum {

    SUBMIT("01", "提交"),
    APPROVED("02", "审批"),
    REPRESENT("03", "申诉"),
    FEEDBACK("04", "反馈"),
    AFFIRM("05", "确认");

    private final String code;
    private final String value;

    SuperviseOperationTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 状态码
     * @return 对应的枚举值
     */
    public static SuperviseOperationTypeEnum getByCode(String code) {
        for (SuperviseOperationTypeEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据value获取枚举值
     *
     * @param value 状态值
     * @return 对应的枚举值
     */
    public static SuperviseOperationTypeEnum getByValue(String value) {
        for (SuperviseOperationTypeEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
}
