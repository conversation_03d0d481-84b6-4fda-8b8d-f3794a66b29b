package com.rs.module.acp.dao.wb;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.module.acp.controller.admin.wb.vo.MeetingNoticeQueryVO;
import com.rs.module.acp.controller.admin.wb.vo.MeetingNoticeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 *
 * <AUTHOR>
 */
@Mapper
public interface WbCommonDao  {


    List<JSONObject> getArraignmentRegistration(String orgCode);

    JSONObject getLawyerMeetingById(String id);

    JSONObject getConsularMeetingById(String id);

    List<JSONObject> getLawyerMeetingList(String applyMeetingStartTime);

    List<JSONObject> getFamilyRegistration(String orgCode);

    List<JSONObject> getConsularMeetingList(String applyMeetingStartTime);

    JSONObject getConsularTrajectory(String id);

    JSONObject getLawyerTrajectory(String id);
    JSONObject getArraignmentTrajectory(String id);

    JSONObject getBringInterrogationTrajectory(String id);

    JSONObject getEscortTrajectory(String id);

    JSONObject getFamilyMeetingTrajectory(String id);

    JSONObject getFamilyMeetingVideoTrajectory(String id);

    List<JSONObject> verificationPersonnel(@Param("jgrybm") String jgrybm, @Param("type")String type);

    IPage<JSONObject> getHistoryArraignmentPageByJgrybm(@Param("page") Page<JSONObject> page, @Param("jgrybm") String jgrybm);
    IPage<JSONObject> getHistoryBringInterrogationPageByJgrybm(@Param("page") Page<JSONObject> page, @Param("jgrybm") String jgrybm);
    IPage<JSONObject> getHistoryEscortPageByJgrybm(@Param("page") Page<JSONObject> page, @Param("jgrybm") String jgrybm);
    IPage<JSONObject> getHistoryFamilyMeetingPageByJgrybm(@Param("page") Page<JSONObject> page, @Param("jgrybm") String jgrybm);

    IPage<JSONObject> getHistoryLawyerMeetingPageByJgrybm(@Param("page") Page<JSONObject> page, @Param("jgrybm") String jgrybm);

    IPage<JSONObject> getHistoryConsularMeetingPageByJgrybm(@Param("page") Page<JSONObject> page, @Param("jgrybm") String jgrybm);

    IPage<JSONObject> getHistoryFamilyMeetingVideoPageByJgrybm(@Param("page") Page<JSONObject> page, @Param("jgrybm") String jgrybm);

    IPage<JSONObject> getHistoryGoodsDeliveryPageByJgrybm(@Param("page") Page<JSONObject> page, @Param("jgrybm") String jgrybm);

}
