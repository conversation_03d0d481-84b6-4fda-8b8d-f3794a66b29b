package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.EscortDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-提解登记 Service 接口
 *
 * <AUTHOR>
 */
public interface EscortService extends IBaseService<EscortDO>{

    /**
     * 创建实战平台-窗口业务-提解登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createEscort(@Valid EscortSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-提解登记
     *
     * @param updateReqVO 更新信息
     */
    void updateEscort(@Valid EscortSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-提解登记
     *
     * @param id 编号
     */
    void deleteEscort(String id);

    /**
     * 获得实战平台-窗口业务-提解登记
     *
     * @param id 编号
     * @return 实战平台-窗口业务-提解登记
     */
    EscortDO getEscort(String id);

    /**
    * 获得实战平台-窗口业务-提解登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-提解登记分页
    */
    PageResult<EscortDO> getEscortPage(EscortPageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-提解登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-提解登记列表
    */
    List<EscortDO> getEscortList(EscortListReqVO listReqVO);

    /**
     * 获得实战平台-窗口业务-带出安检
     *
     * @param updateReqVO
     */
    boolean escortingInspect(EscortSaveReqVO updateReqVO);

    /**
     * 获得实战平台-窗口业务-回所安检
     *
     * @param updateReqVO
     */
    boolean returnInspection(EscortSaveReqVO updateReqVO);

    /**
     * 获得实战平台-窗口业务-补录
     *
     * @param updateReqVO
     */
    boolean additionalRecording(EscortSaveReqVO updateReqVO);

    /**
     * 获得实战平台-窗口业务-提解登记
     *
     * @param id 编号
     * @return 实战平台-窗口业务-提解登记
     */
    EscortRespVO getEscortById(String id);

    /**
     * 获得实战平台-窗口业务-获取历史提解记录
     *
     * @param jgrybm 编号
     * @param pageNo 页码
     * @param pageSize 每页多少
     * @return 实战平台-窗口业务-获取历史提解记录
     */
    PageResult<EscortRespVO> getHistoryEscort(String jgrybm,int pageNo,int pageSize);
}
