package com.rs.module.acp.service.zh;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.zh.vo.SuperviseSaveReqVO;
import com.rs.module.acp.entity.zh.SuperviseDO;

import javax.validation.Valid;

/**
 * 综合管理-督导信息 Service 接口
 *
 * <AUTHOR>
 */
public interface SuperviseService extends IBaseService<SuperviseDO>{

    /**
     * 创建综合管理-督导信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createSupervise(@Valid SuperviseSaveReqVO createReqVO) throws Exception;

    /**
     * 督导审批
     * @param approveReqVO
     * @return
     * @throws Exception
     */
    boolean approve(JSONObject approveReqVO) throws Exception;

    /**
     * 督导确认
     * @param reqVO
     * @return
     * @throws Exception
     */
//    boolean affirm(JSONObject reqVO) throws Exception;

    /**
     * 更新综合管理-督导信息
     *
     * @param updateReqVO 更新信息
     */
    void updateSupervise(@Valid SuperviseSaveReqVO updateReqVO);

    /**
     * 删除综合管理-督导信息
     *
     * @param id 编号
     */
    void deleteSupervise(String id);

    /**
     * 获得综合管理-督导信息
     *
     * @param id 编号
     * @return 综合管理-督导信息
     */
    SuperviseDO getSupervise(String id);


}
