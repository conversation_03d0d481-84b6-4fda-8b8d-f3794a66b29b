package com.rs.module.acp.entity.gj;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-权力义务告知书 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_rights_obligations_notice")
@KeySequence("acp_gj_rights_obligations_notice_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_rights_obligations_notice")
public class RightsObligationsNoticeDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 签名url
     */
    private String signUrl;
    /**
     * 签名时间
     */
    private Date signTime;
    /**
     * 捺印url
     */
    private String fingerprintUrl;
    /**
     * 监管人员姓名
     */
    private String jgryxm;

}
