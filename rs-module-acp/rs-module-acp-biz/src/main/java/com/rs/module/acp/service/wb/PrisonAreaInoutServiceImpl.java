package com.rs.module.acp.service.wb;

import cn.hutool.core.util.ObjectUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.PrisonAreaInoutDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.PrisonAreaInoutDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-出入监区登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrisonAreaInoutServiceImpl extends BaseServiceImpl<PrisonAreaInoutDao, PrisonAreaInoutDO> implements PrisonAreaInoutService {

    @Resource
    private PrisonAreaInoutDao prisonAreaInoutDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createPrisonAreaInout(PrisonAreaInoutSaveReqVO createReqVO) {
        // 插入
        PrisonAreaInoutDO prisonAreaInout = BeanUtils.toBean(createReqVO, PrisonAreaInoutDO.class);

        if(ObjectUtil.isEmpty(prisonAreaInout.getStatus())){
            prisonAreaInout.setStatus("0");
        }

        prisonAreaInoutDao.insert(prisonAreaInout);
        // 返回
        return prisonAreaInout.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePrisonAreaInout(PrisonAreaInoutSaveReqVO updateReqVO) {
        // 校验存在
        validatePrisonAreaInoutExists(updateReqVO.getId());
        // 更新
        PrisonAreaInoutDO updateObj = BeanUtils.toBean(updateReqVO, PrisonAreaInoutDO.class);
        prisonAreaInoutDao.updateById(updateObj);
    }

    @Override
    public void deletePrisonAreaInout(String id) {
        // 校验存在
        validatePrisonAreaInoutExists(id);
        // 删除
        prisonAreaInoutDao.deleteById(id);
    }

    private void validatePrisonAreaInoutExists(String id) {
        if (prisonAreaInoutDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-出入监区登记数据不存在");
        }
    }

    @Override
    public PrisonAreaInoutDO getPrisonAreaInout(String id) {
        return prisonAreaInoutDao.selectById(id);
    }

    @Override
    public PageResult<PrisonAreaInoutDO> getPrisonAreaInoutPage(PrisonAreaInoutPageReqVO pageReqVO) {
        return prisonAreaInoutDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrisonAreaInoutDO> getPrisonAreaInoutList(PrisonAreaInoutListReqVO listReqVO) {
        return prisonAreaInoutDao.selectList(listReqVO);
    }


}
