package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import com.bsp.common.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.VisitorRegDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.VisitorRegDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-对外开放登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class VisitorRegServiceImpl extends BaseServiceImpl<VisitorRegDao, VisitorRegDO> implements VisitorRegService {

    @Resource
    private VisitorRegDao visitorRegDao;

    @Autowired
    private VisitorInfoService visitorInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createVisitorReg(VisitorRegSaveReqVO createReqVO) {
        // 插入
        VisitorRegDO visitorReg = BeanUtils.toBean(createReqVO, VisitorRegDO.class);
        visitorReg.setId(StringUtil.getGuid32());
        if(CollectionUtil.isNotEmpty(createReqVO.getPersonList())){
            visitorInfoService.savetVisitorInfoList(createReqVO.getPersonList(),visitorReg.getId());
        }
        visitorRegDao.insert(visitorReg);
        // 返回
        return visitorReg.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateVisitorReg(VisitorRegSaveReqVO updateReqVO) {
        // 校验存在
        validateVisitorRegExists(updateReqVO.getId());
        // 更新
        VisitorRegDO updateObj = BeanUtils.toBean(updateReqVO, VisitorRegDO.class);

        if(CollectionUtil.isNotEmpty(updateReqVO.getPersonList())){
            visitorInfoService.savetVisitorInfoList(updateReqVO.getPersonList(),updateReqVO.getId());
        }

        visitorRegDao.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteVisitorReg(String id) {
        // 校验存在
        validateVisitorRegExists(id);
        // 删除
        visitorRegDao.deleteById(id);
    }

    private void validateVisitorRegExists(String id) {
        if (visitorRegDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-对外开放登记数据不存在");
        }
    }

    @Override
    public VisitorRegDO getVisitorReg(String id) {
        return visitorRegDao.selectById(id);
    }

    @Override
    public PageResult<VisitorRegDO> getVisitorRegPage(VisitorRegPageReqVO pageReqVO) {
        return visitorRegDao.selectPage(pageReqVO);
    }

    @Override
    public List<VisitorRegDO> getVisitorRegList(VisitorRegListReqVO listReqVO) {
        return visitorRegDao.selectList(listReqVO);
    }

    @Override
    public VisitorRegRespVO getVisitorRegById(String id) {
        VisitorRegDO regDO = visitorRegDao.selectById(id);
        VisitorRegRespVO regRespVO = BeanUtils.toBean(regDO,VisitorRegRespVO.class);
        //获取人员信息
        regRespVO.setPersonList(visitorInfoService.getVisitorInfoListByVisitorRegId(id));
        return regRespVO;
    }
}
