package com.rs.module.acp.controller.app.gj;

import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.db.vo.InjuryAssessmentListReqVO;
import com.rs.module.acp.controller.admin.db.vo.InjuryAssessmentSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.doublecheck.DoubleCheckRespVO;
import com.rs.module.acp.controller.admin.gj.vo.doublecheck.DoubleCheckSaveReqVO;
import com.rs.module.acp.entity.db.InjuryAssessmentDO;
import com.rs.module.acp.entity.gj.DoubleCheckDO;
import com.rs.module.acp.service.db.InjuryAssessmentService;
import com.rs.module.acp.service.gj.doublecheck.DoubleCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-管教业务-双重检查登记")
@RestController
@RequestMapping("/app/acp/gj/doubleCheck")
@Validated
public class AppDoubleCheckController {

    @Resource
    private DoubleCheckService doubleCheckService;

    @Autowired
    private InjuryAssessmentService injuryAssessmentService;

    @PostMapping("/create")
    @ApiOperation(value = "App-创建双重检查登记")
    public CommonResult<String> createDoubleCheck(@Valid @RequestBody DoubleCheckSaveReqVO createReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        if (StringUtils.isEmpty(createReqVO.getOperatePolice())) {
            createReqVO.setOperatePolice(sessionUser.getName());
        }
        if (StringUtils.isEmpty(createReqVO.getOperatePoliceSfzh())) {
            createReqVO.setOperatePoliceSfzh(sessionUser.getIdCard());
        }
        if (Objects.isNull(createReqVO.getOperateTime())) {
            createReqVO.setOperateTime(new Date());
        }
        return success(doubleCheckService.createDoubleCheck(createReqVO));
    }


    @GetMapping("/page")
    @ApiOperation(value = "App-双重检查登记分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小"),
            @ApiImplicitParam(name = "type", value = "周期类型 1 全部，2 今天，3 昨天，4 近一周")
    })
    public CommonResult<PageResult<DoubleCheckRespVO>> getDoubleCheckPage(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                                          @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                                          @RequestParam("type") String type) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        PageResult<DoubleCheckDO> pageResult = doubleCheckService.getAppDoubleCheckPage(pageNo, pageSize, sessionUser.getIdCard(), type);
        return success(BeanUtils.toBean(pageResult, DoubleCheckRespVO.class));
    }


    @GetMapping("/wsqk")
    @ApiOperation(value = "App-外伤情况")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rybh", value = "人员编号", required = true)
    })
    public CommonResult<List<InjuryAssessmentSaveReqVO>> getDoubleCheckPage(@RequestParam("rybh") String rybh) {
        InjuryAssessmentListReqVO injuryAssessmentListReqVO = new InjuryAssessmentListReqVO();
        injuryAssessmentListReqVO.setRybh(rybh);
        List<InjuryAssessmentDO> injuryAssessmentList = injuryAssessmentService.getInjuryAssessmentList(injuryAssessmentListReqVO);
        List<InjuryAssessmentSaveReqVO> injuryAssessmentRespVOList = BeanUtils.toBean(injuryAssessmentList, InjuryAssessmentSaveReqVO.class);
        return success(injuryAssessmentRespVOList);
    }

}
