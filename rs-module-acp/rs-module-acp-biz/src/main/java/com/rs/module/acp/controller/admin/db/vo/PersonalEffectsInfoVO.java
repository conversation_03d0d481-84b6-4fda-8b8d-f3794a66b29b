package com.rs.module.acp.controller.admin.db.vo;

import com.rs.module.acp.entity.db.OutPersonalEffectsSubDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("随身物品信息VO")
public class PersonalEffectsInfoVO {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("取出原因")
    private String qcyy;

    @ApiModelProperty("取出方式")
    private String qcfs;

    @ApiModelProperty("备注")
    private String bz;

    @ApiModelProperty("经办人")
    private String jbr;

    @ApiModelProperty("经办时间")
    private Date jbsj;

    @ApiModelProperty("监管人员编号")
    private String jgrybm;

    @ApiModelProperty("财务交接情况")
    private String cwjjqk;

    @ApiModelProperty("财务交接手续")
    private String cwjjst;

    @ApiModelProperty("物品列表")
    private List<OutPersonalEffectsSubDO> personalEffectsList;
}

