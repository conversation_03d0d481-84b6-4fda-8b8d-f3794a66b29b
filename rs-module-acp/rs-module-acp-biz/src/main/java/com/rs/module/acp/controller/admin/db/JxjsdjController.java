package com.rs.module.acp.controller.admin.db;

import com.rs.module.acp.controller.admin.db.vo.jxjsdj.*;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.db.JxjsdjDO;
import com.rs.module.acp.service.db.JxjsdjService;

@Api(tags = "实战平台-收押业务-减刑登记")
@RestController
@RequestMapping("/acp/db/jxjsdj")
@Validated
public class JxjsdjController {

    @Resource
    private JxjsdjService jxjsdjService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-收押业务-减刑登记")
    public CommonResult<String> createJxjsdj(@Valid @RequestBody JxjsdjSaveReqVO createReqVO) {
        return success(jxjsdjService.createJxjsdj(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-收押业务-减刑登记")
    public CommonResult<Boolean> updateJxjsdj(@Valid @RequestBody JxjsdjSaveReqVO updateReqVO) {
        jxjsdjService.updateJxjsdj(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-收押业务-减刑登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteJxjsdj(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           jxjsdjService.deleteJxjsdj(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-收押业务-减刑登记")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<JxjsdjRespVO> getJxjsdj(@RequestParam("id") String id) {
        return success(jxjsdjService.getJxjsdj(id));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-收押业务-减刑登记分页")
    public CommonResult<PageResult<JxjsdjRespVO>> getJxjsdjPage(@Valid @RequestBody JxjsdjPageReqVO pageReqVO) {
        PageResult<JxjsdjDO> pageResult = jxjsdjService.getJxjsdjPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, JxjsdjRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-收押业务-减刑登记列表")
    public CommonResult<List<JxjsdjRespVO>> getJxjsdjList(@Valid @RequestBody JxjsdjListReqVO listReqVO) {
        List<JxjsdjDO> list = jxjsdjService.getJxjsdjList(listReqVO);
        return success(BeanUtils.toBean(list, JxjsdjRespVO.class));
    }

    @PostMapping("/dj")
    @ApiOperation(value = "登记")
    public CommonResult<String> dj(@Valid @RequestBody JxjsdjDjReqVO djReqVO) {
        return success(jxjsdjService.dj(djReqVO));
    }

    @PostMapping("/swhyj")
    @ApiOperation(value = "所务会研究")
    public CommonResult<Boolean> swhyj(@Valid @RequestBody JxjsdjSnhyjReqVO snhyjReqVO) {
        jxjsdjService.swhyj(snhyjReqVO);
        return success(true);
    }

    @PostMapping("/lxnlrd")
    @ApiOperation(value = "履行能力认定")
    public CommonResult<Boolean> lxnlrd(@Valid @RequestBody JxjsdjLxnlrdReqVO lxnlrdReqVO) {
        jxjsdjService.lxnlrd(lxnlrdReqVO);
        return success(true);
    }

    @PostMapping("/hszxd")
    @ApiOperation(value = "核实执行地")
    public CommonResult<Boolean> hszxd(@Valid @RequestBody JxjsdjHszxdReqVO hszxdReqVO) {
        jxjsdjService.hszxd(hszxdReqVO);
        return success(true);
    }

    @PostMapping("/sngs")
    @ApiOperation(value = "所内公示")
    public CommonResult<Boolean> sngs(@Valid @RequestBody JxjsdjSngsReqVO sngsReqVO) {
        jxjsdjService.sngs(sngsReqVO);
        return success(true);
    }

    @PostMapping("/swhyjfh")
    @ApiOperation(value = "所务会研究复核")
    public CommonResult<Boolean> swhyjfh(@Valid @RequestBody JxjsdjSnhyjfhReqVO snhyjfhReqVO) {
        jxjsdjService.swhyjfh(snhyjfhReqVO);
        return success(true);
    }

    @PostMapping("/gajgsc")
    @ApiOperation(value = "公安机关审查")
    public CommonResult<Boolean> gajgsc(@Valid @RequestBody JxjsdjGajgscReqVO gajgscReqVO) {
        jxjsdjService.gajgsc(gajgscReqVO);
        return success(true);
    }

    @PostMapping("/jcyjd")
    @ApiOperation(value = "检察院监督")
    public CommonResult<Boolean> jcyjd(@Valid @RequestBody JxjsdjJcyjdReqVO jcyjdReqVO) {
        jxjsdjService.jcyjd(jcyjdReqVO);
        return success(true);
    }

    @PostMapping("/fyshcd")
    @ApiOperation(value = "法院审核裁定")
    public CommonResult<Boolean> fyshcd(@Valid @RequestBody JxjsdjFyshcdReqVO fyshcdReqVO) {
        jxjsdjService.fyshcd(fyshcdReqVO);
        return success(true);
    }
}
