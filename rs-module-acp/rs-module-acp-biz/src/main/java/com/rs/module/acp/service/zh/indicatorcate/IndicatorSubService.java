package com.rs.module.acp.service.zh.indicatorcate;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorSubListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorSubPageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorSubSaveReqVO;
import com.rs.module.acp.entity.zh.IndicatorSubDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 综合管理-绩效考核子指标 Service 接口
 *
 * <AUTHOR>
 */
public interface IndicatorSubService extends IBaseService<IndicatorSubDO>{

    /**
     * 创建综合管理-绩效考核子指标
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createIndicatorSub(@Valid IndicatorSubSaveReqVO createReqVO);

    /**
     * 更新综合管理-绩效考核子指标
     *
     * @param updateReqVO 更新信息
     */
    void updateIndicatorSub(@Valid IndicatorSubSaveReqVO updateReqVO);

    /**
     * 删除综合管理-绩效考核子指标
     *
     * @param id 编号
     */
    void deleteIndicatorSub(String id);

    /**
     * 获得综合管理-绩效考核子指标
     *
     * @param id 编号
     * @return 综合管理-绩效考核子指标
     */
    IndicatorSubDO getIndicatorSub(String id);

    /**
    * 获得综合管理-绩效考核子指标分页
    *
    * @param pageReqVO 分页查询
    * @return 综合管理-绩效考核子指标分页
    */
    PageResult<IndicatorSubDO> getIndicatorSubPage(IndicatorSubPageReqVO pageReqVO);

    /**
    * 获得综合管理-绩效考核子指标列表
    *
    * @param listReqVO 查询条件
    * @return 综合管理-绩效考核子指标列表
    */
    List<IndicatorSubDO> getIndicatorSubList(IndicatorSubListReqVO listReqVO);


}
