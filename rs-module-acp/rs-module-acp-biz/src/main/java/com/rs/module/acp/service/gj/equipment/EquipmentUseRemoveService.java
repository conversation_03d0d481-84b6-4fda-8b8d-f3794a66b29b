package com.rs.module.acp.service.gj.equipment;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseRemoveListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseRemovePageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseRemoveSaveReqVO;
import com.rs.module.acp.entity.gj.EquipmentUseRemoveDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-械具使用解除呈批 Service 接口
 *
 * <AUTHOR>
 */
public interface EquipmentUseRemoveService extends IBaseService<EquipmentUseRemoveDO>{

    /**
     * 创建实战平台-管教业务-械具使用解除呈批
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createEquipmentUseRemove(@Valid EquipmentUseRemoveSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-械具使用解除呈批
     *
     * @param updateReqVO 更新信息
     */
    void updateEquipmentUseRemove(@Valid EquipmentUseRemoveSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-械具使用解除呈批
     *
     * @param id 编号
     */
    void deleteEquipmentUseRemove(String id);

    /**
     * 获得实战平台-管教业务-械具使用解除呈批
     *
     * @param id 编号
     * @return 实战平台-管教业务-械具使用解除呈批
     */
    EquipmentUseRemoveDO getEquipmentUseRemove(String id);

    /**
    * 获得实战平台-管教业务-械具使用解除呈批分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-械具使用解除呈批分页
    */
    PageResult<EquipmentUseRemoveDO> getEquipmentUseRemovePage(EquipmentUseRemovePageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-械具使用解除呈批列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-械具使用解除呈批列表
    */
    List<EquipmentUseRemoveDO> getEquipmentUseRemoveList(EquipmentUseRemoveListReqVO listReqVO);


}
