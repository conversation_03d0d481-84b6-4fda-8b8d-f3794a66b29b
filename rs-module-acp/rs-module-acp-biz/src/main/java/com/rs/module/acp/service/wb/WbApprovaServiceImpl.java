package com.rs.module.acp.service.wb;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.acp.cons.CommonConstants;
import com.rs.module.acp.controller.admin.wb.vo.WbApprovaReqVO;
import com.rs.module.acp.controller.admin.wb.vo.WbInspectionResultsSaveReqVO;
import com.rs.module.acp.dao.wb.WbApprovaDao;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.base.util.BspApprovalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
@Validated
public class WbApprovaServiceImpl implements WbApprovaService{

    @Resource
    private WbApprovaDao wbApprovaDao;

    @Autowired
    private WbCommonService wbCommonService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approve(WbApprovaReqVO approvaReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        JSONObject targetJson = wbApprovaDao.getApprovaInfo(approvaReqVO.getId(),approvaReqVO.getBusinessType());

        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(targetJson.getString("task_id"), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }

        Map<String,String> paramMap = getParamsByType(approvaReqVO.getBusinessType());

        //从当前登录用户信息设置审批信息默认值
        if(StringUtil.isEmpty(approvaReqVO.getApproverXm())) approvaReqVO.setApproverXm(sessionUser.getName());
        if(StringUtil.isEmpty(approvaReqVO.getApproverSfzh())) approvaReqVO.setApproverSfzh(sessionUser.getIdCard());
        if(approvaReqVO.getApproverTime() == null) approvaReqVO.setApproverTime(new Date());
        BspApproceStatusEnum isApprove = BspApproceStatusEnum.NOT_PASSED;
        String businessStatus = "";
        //1:审批通过，0：审批不通过
        if("1".equals(approvaReqVO.getApprovalResult())){
            isApprove = BspApproceStatusEnum.PASSED;
        }
        approvaReqVO.setActInstId(targetJson.getString("act_inst_id"));
        approvaReqVO.setTaskId(targetJson.getString("task_id"));
        approvaReqVO.setDefKey(paramMap.get("defKey"));

        String msgTit = "";
        String msgUrl = "";

        Map<String, Object> variables = new HashMap<>();
        variables.put("busType", paramMap.get("busType"));//ZD_MSG_BUSTYPE 字典对应code

        if(WbConstants.BUSINESS_TYPE_ESCORT.equals(approvaReqVO.getBusinessType())){
            variables.put("name",targetJson.getString("add_user_name"));
            variables.put("meetingTime",targetJson.getString("meeting_time"));
            variables.put("roomName",targetJson.getString("room_name"));
            variables.put("prisonerName",targetJson.getString("xm"));

            msgUrl = String.format("/window/provideSolutionsJls?curId=&saveType=sp",targetJson.getString("id"));
            msgTit = "提解总队领导审批";
        }

        Map<String,String> approvalResult = BspApprovalUtil.approvalProcessMap(approvaReqVO,
                isApprove,
                msgTit,
                msgUrl,
                "1".equals(approvaReqVO.getApprovalResult())?false:true,
                null,
                null,
                HttpUtils.getAppCode()
        );

        if(BspApprovalUtil.getBpmApi().isFinishProcinst(targetJson.getString("act_inst_id"))){
            //更新业务状态
            businessStatus = "1".equals(approvaReqVO.getApprovalResult())?paramMap.get("status"):"11";
            wbApprovaDao.updateStatus(approvaReqVO.getId(),businessStatus,approvaReqVO.getBusinessType());

            sendInOutRecord(targetJson,approvaReqVO.getBusinessType());

        }else {
            //更新业务状态
            wbApprovaDao.updateTask(approvaReqVO.getId(),approvalResult.get("taskId"),approvaReqVO.getBusinessType());

            if(WbConstants.BUSINESS_TYPE_ESCORT.equals(approvaReqVO.getBusinessType())){
                //拘留所的提解有两级审批，需要特殊处理下
                businessStatus = "1".equals(approvaReqVO.getApprovalResult())?"10-2":"11";
                wbApprovaDao.updateStatus(approvaReqVO.getId(),businessStatus,approvaReqVO.getBusinessType());
            }
        }

    }

    private Map<String,String> getParamsByType(String type){
        String appCode = HttpUtils.getAppCode();
        //（0：提讯、1：提询、2：提解、3：律师会见、4：家属会见、5：领事会见、6：家属单向视频会见）
        Map<String,String> paramMap = new HashMap<>();
        if(WbConstants.BUSINESS_TYPE_ARRAIGNMENT.equals(type)){
            paramMap.put("busType","102_001");
        }else if(WbConstants.BUSINESS_TYPE_BRING_INTERROGATION.equals(type)){
            paramMap.put("defKey",appCode+"-"+"chuangkouyewujuliustixunshenpi");
            paramMap.put("status","0");
            paramMap.put("busType","102_002");
        }else if(WbConstants.BUSINESS_TYPE_ESCORT.equals(type)){
            paramMap.put("defKey",appCode+"-"+"chuangkouyewujuliusuotijie");
            paramMap.put("status","0");
            paramMap.put("busType","102_003");
        }else if(WbConstants.BUSINESS_TYPE_LAWYER_MEETING.equals(type)){
            paramMap.put("busType","102_004");
        }else if(WbConstants.BUSINESS_TYPE_FAMILY_MEETING.equals(type)){
            paramMap.put("defKey",appCode+"-"+"chuangkouyewujiashuhuijianshenp");
            paramMap.put("status","0");
            paramMap.put("busType","102_006");
        }else if(WbConstants.BUSINESS_TYPE_CONSULAR_MEETING.equals(type)){
            paramMap.put("busType","102_005");
        }else if(WbConstants.BUSINESS_TYPE_FAMILY_MEETING_VIDEO.equals(type)){
            paramMap.put("defKey",appCode+"-"+"chuangkouyewujiashuhuijianshenp");
            paramMap.put("status","0");
            paramMap.put("busType","102_007");
        }

        return paramMap;
    }


    @Override
    public JSONObject getApproveTrack(String actInstId) {
        return BspApprovalUtil.getBpmApi().approveTrack(actInstId);
    }

    public void sendInOutRecord(JSONObject targetJson,String type){
        String busType = null;
        WbInspectionResultsSaveReqVO wbInspectionResultsSaveReqVO = new WbInspectionResultsSaveReqVO();
        wbInspectionResultsSaveReqVO.setId(targetJson.getString("id"));
        wbInspectionResultsSaveReqVO.setApplMeetingTime(targetJson.getDate("apply_meeting_time"));
        wbInspectionResultsSaveReqVO.setJgrybm(targetJson.getString("jgrybm"));
        if(WbConstants.BUSINESS_TYPE_ARRAIGNMENT.equals(type)){
        }else if(WbConstants.BUSINESS_TYPE_BRING_INTERROGATION.equals(type)){
            busType = "01";
            wbInspectionResultsSaveReqVO.setBusinessSubType("0102");
        }else if(WbConstants.BUSINESS_TYPE_ESCORT.equals(type)){
            busType = "02";
        }else if(WbConstants.BUSINESS_TYPE_LAWYER_MEETING.equals(type)){
            busType = "04";
        }else if(WbConstants.BUSINESS_TYPE_FAMILY_MEETING.equals(type)){
            busType = "03";
            wbInspectionResultsSaveReqVO.setBusinessSubType("0301");
        }else if(WbConstants.BUSINESS_TYPE_CONSULAR_MEETING.equals(type)){

        }else if(WbConstants.BUSINESS_TYPE_FAMILY_MEETING_VIDEO.equals(type)){

        }
        wbCommonService.saveInOutRecord(wbInspectionResultsSaveReqVO,busType,"out","0",false);
    }
}
