package com.rs.module.acp.service.zh.indicatorcate;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmttApprovalRecordListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmttApprovalRecordPageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmttApprovalRecordSaveReqVO;
import com.rs.module.acp.entity.zh.AssmttApprovalRecordDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 综合管理-绩效考核-考核审核记录 Service 接口
 *
 * <AUTHOR>
 */
public interface AssmttApprovalRecordService extends IBaseService<AssmttApprovalRecordDO>{

    /**
     * 创建综合管理-绩效考核-考核审核记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createAssmttApprovalRecord(@Valid AssmttApprovalRecordSaveReqVO createReqVO);

    /**
     * 更新综合管理-绩效考核-考核审核记录
     *
     * @param updateReqVO 更新信息
     */
    void updateAssmttApprovalRecord(@Valid AssmttApprovalRecordSaveReqVO updateReqVO);

    /**
     * 删除综合管理-绩效考核-考核审核记录
     *
     * @param id 编号
     */
    void deleteAssmttApprovalRecord(String id);

    /**
     * 获得综合管理-绩效考核-考核审核记录
     *
     * @param id 编号
     * @return 综合管理-绩效考核-考核审核记录
     */
    AssmttApprovalRecordDO getAssmttApprovalRecord(String id);

    /**
    * 获得综合管理-绩效考核-考核审核记录分页
    *
    * @param pageReqVO 分页查询
    * @return 综合管理-绩效考核-考核审核记录分页
    */
    PageResult<AssmttApprovalRecordDO> getAssmttApprovalRecordPage(AssmttApprovalRecordPageReqVO pageReqVO);

    /**
    * 获得综合管理-绩效考核-考核审核记录列表
    *
    * @param listReqVO 查询条件
    * @return 综合管理-绩效考核-考核审核记录列表
    */
    List<AssmttApprovalRecordDO> getAssmttApprovalRecordList(AssmttApprovalRecordListReqVO listReqVO);


}
