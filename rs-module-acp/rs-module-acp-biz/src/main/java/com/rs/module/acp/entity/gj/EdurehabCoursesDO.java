package com.rs.module.acp.entity.gj;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-教育康复课程 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_edurehab_courses")
@KeySequence("acp_gj_edurehab_courses_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_edurehab_courses")
public class EdurehabCoursesDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * courses_code
     */
    private String coursesCode;
    /**
     * courses_name
     */
    private String coursesName;
    /**
     * 分类颜色
     */
    private String coursesColor;
    /**
     * 是否启用
     */
    private Short isEnabled;

}
