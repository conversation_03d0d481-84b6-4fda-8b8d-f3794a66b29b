package com.rs.module.acp.listener.admin.device;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.StringUtils;
import com.rs.module.base.entity.pm.DeviceData;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
public class DeviceDataImportListener implements ReadListener<DeviceData> {
    private final List<DeviceData> cachedDataList;

    public DeviceDataImportListener(List<DeviceData> cachedDataList) {
        this.cachedDataList = cachedDataList;
    }

    @Override
    public void invoke(DeviceData data, AnalysisContext context) {
        //校验每一行数据的合规性
        int index = context.readRowHolder().getRowIndex() + 1;
        if (StrUtil.isBlank(data.getDeviceCode())) {
            throw new RuntimeException("设备编码不能为空（第"+index+"行）");
        }

        if (StrUtil.isBlank(data.getDeviceName())) {
            throw new RuntimeException("设备名称不能为空（第"+index+"行）");
        }

        if (StrUtil.isBlank(data.getDeviceType())) {
            throw new RuntimeException("设备类型编码不能为空（第"+index+"行）");
        }

        if (StrUtil.isBlank(data.getAreaId())) {
            throw new RuntimeException("所属区域编码不能为空（第"+index+"行）");
        }

        if (StrUtil.isBlank(data.getDeviceStatus())) {
            throw new RuntimeException("设备状态不能为空（第"+index+"行）");
        }

        cachedDataList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
//        System.out.println("导入的区域数据数量: " + cachedDataList.size());
        // 后续处理逻辑
//        for (AreaData area : cachedDataList) {
//            System.out.println(area); // 如果用了 @Data，可打印对象内容
//        }
    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        log.info("==== 表头原始内容（第一行） ====");
        headMap.forEach((key, value) -> {
            log.info("表头索引: " + key + ", 表头名称: " + value.getStringValue());
        });

        // 期待的字段头（第二行）
        List<String> expectedHeaders = Arrays.asList("设备编码*","设备名称*","设备类型编码*"	,"所属区域编码*",	"点位名称",	"设备状态*",	"通道id"	,"通道名称");

        // 从 context 中获取实际映射到 Java 字段的字段名（也就是第二行）
        List<String> actualHeaders = new ArrayList<>();

        Map<Integer, Head> headMapFromContext = context.readSheetHolder()
                .getExcelReadHeadProperty().getHeadMap();

        headMapFromContext.forEach((index, head) -> {
            List<String> headNames = head.getHeadNameList(); // 多层表头
            if (headNames != null && !headNames.isEmpty()) {
                String lastName = headNames.get(headNames.size() - 1); // 获取最后一层（第2行）
                if (!StringUtils.isEmpty(lastName)) {
                    actualHeaders.add(lastName);
                }
            }
        });

        // 打印调试信息
        log.info("==== 实际字段名（第二行） ====");
        actualHeaders.forEach(log::info);
        // 校验字段
        for (String expected : expectedHeaders) {
            if (!actualHeaders.contains(expected)) {
                //后续可扩展是否要校验excel 表头字段
//                throw new RuntimeException("缺少必需的表头字段: " + expected);
            }
        }
    }
}
