package com.rs.module.acp.dao.zh;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmttApprovalRecordListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmttApprovalRecordPageReqVO;
import com.rs.module.acp.entity.zh.AssmttApprovalRecordDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 综合管理-绩效考核-考核审核记录 Dao
*
* <AUTHOR>
*/
@Mapper
public interface AssmttApprovalRecordDao extends IBaseDao<AssmttApprovalRecordDO> {


    default PageResult<AssmttApprovalRecordDO> selectPage(AssmttApprovalRecordPageReqVO reqVO) {
        Page<AssmttApprovalRecordDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<AssmttApprovalRecordDO> wrapper = new LambdaQueryWrapperX<AssmttApprovalRecordDO>()
            .eqIfPresent(AssmttApprovalRecordDO::getAssmttApprovalId, reqVO.getAssmttApprovalId())
            .eqIfPresent(AssmttApprovalRecordDO::getIndicatorCateId, reqVO.getIndicatorCateId())
            .likeIfPresent(AssmttApprovalRecordDO::getIndicatorCateName, reqVO.getIndicatorCateName())
            .eqIfPresent(AssmttApprovalRecordDO::getMainIndicatorId, reqVO.getMainIndicatorId())
            .eqIfPresent(AssmttApprovalRecordDO::getSubIndicatorId, reqVO.getSubIndicatorId())
            .likeIfPresent(AssmttApprovalRecordDO::getIndicatorName, reqVO.getIndicatorName())
            .eqIfPresent(AssmttApprovalRecordDO::getIndicatorDescription, reqVO.getIndicatorDescription())
            .eqIfPresent(AssmttApprovalRecordDO::getScoreType, reqVO.getScoreType())
            .eqIfPresent(AssmttApprovalRecordDO::getBaseScore, reqVO.getBaseScore())
            .eqIfPresent(AssmttApprovalRecordDO::getMinScore, reqVO.getMinScore())
            .eqIfPresent(AssmttApprovalRecordDO::getMaxScore, reqVO.getMaxScore())
            .eqIfPresent(AssmttApprovalRecordDO::getSortOrder, reqVO.getSortOrder())
            .eqIfPresent(AssmttApprovalRecordDO::getAssmtReason, reqVO.getAssmtReason())
            .eqIfPresent(AssmttApprovalRecordDO::getAssmtScore, reqVO.getAssmtScore())
            .eqIfPresent(AssmttApprovalRecordDO::getZhApprovalReason, reqVO.getZhApprovalReason())
            .eqIfPresent(AssmttApprovalRecordDO::getZhApprovalScore, reqVO.getZhApprovalScore())
            .eqIfPresent(AssmttApprovalRecordDO::getZzApprovalScore, reqVO.getZzApprovalScore())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(AssmttApprovalRecordDO::getAddTime);
        }
        Page<AssmttApprovalRecordDO> assmttApprovalRecordPage = selectPage(page, wrapper);
        return new PageResult<>(assmttApprovalRecordPage.getRecords(), assmttApprovalRecordPage.getTotal());
    }
    default List<AssmttApprovalRecordDO> selectList(AssmttApprovalRecordListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AssmttApprovalRecordDO>()
            .eqIfPresent(AssmttApprovalRecordDO::getAssmttApprovalId, reqVO.getAssmttApprovalId())
            .eqIfPresent(AssmttApprovalRecordDO::getIndicatorCateId, reqVO.getIndicatorCateId())
            .likeIfPresent(AssmttApprovalRecordDO::getIndicatorCateName, reqVO.getIndicatorCateName())
            .eqIfPresent(AssmttApprovalRecordDO::getMainIndicatorId, reqVO.getMainIndicatorId())
            .eqIfPresent(AssmttApprovalRecordDO::getSubIndicatorId, reqVO.getSubIndicatorId())
            .likeIfPresent(AssmttApprovalRecordDO::getIndicatorName, reqVO.getIndicatorName())
            .eqIfPresent(AssmttApprovalRecordDO::getIndicatorDescription, reqVO.getIndicatorDescription())
            .eqIfPresent(AssmttApprovalRecordDO::getScoreType, reqVO.getScoreType())
            .eqIfPresent(AssmttApprovalRecordDO::getBaseScore, reqVO.getBaseScore())
            .eqIfPresent(AssmttApprovalRecordDO::getMinScore, reqVO.getMinScore())
            .eqIfPresent(AssmttApprovalRecordDO::getMaxScore, reqVO.getMaxScore())
            .eqIfPresent(AssmttApprovalRecordDO::getSortOrder, reqVO.getSortOrder())
            .eqIfPresent(AssmttApprovalRecordDO::getAssmtReason, reqVO.getAssmtReason())
            .eqIfPresent(AssmttApprovalRecordDO::getAssmtScore, reqVO.getAssmtScore())
            .eqIfPresent(AssmttApprovalRecordDO::getZhApprovalReason, reqVO.getZhApprovalReason())
            .eqIfPresent(AssmttApprovalRecordDO::getZhApprovalScore, reqVO.getZhApprovalScore())
            .eqIfPresent(AssmttApprovalRecordDO::getZzApprovalScore, reqVO.getZzApprovalScore())
        .orderByAsc(AssmttApprovalRecordDO::getAddTime));    }


    }
