package com.rs.module.acp.controller.admin.db;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.controller.admin.pm.vo.BizDataCorrectionDataVO;
import com.rs.module.acp.controller.enums.BizCorrectionBusinessTypeEnum;
import com.rs.module.acp.controller.enums.DetainRegKssDataTypeEnum;
import com.rs.module.acp.controller.enums.EntityConverter;
import com.rs.module.acp.entity.db.BiometricInfoDO;
import com.rs.module.acp.entity.db.DetainRegKssDO;
import com.rs.module.acp.entity.db.HealthCheckDO;
import com.rs.module.acp.entity.db.InjuryAssessmentDO;
import com.rs.module.acp.entity.pm.BizDataCorrectionApplyDO;
import com.rs.module.acp.entity.pm.BizDataCorrectionDO;
import com.rs.module.acp.service.db.BiometricInfoService;
import com.rs.module.acp.service.db.DetainRegKssService;
import com.rs.module.acp.service.db.HealthCheckService;
import com.rs.module.acp.service.pm.BizDataCorrectionApplyService;
import com.rs.module.acp.service.pm.BizDataCorrectionService;
import com.rs.module.acp.util.DataCompareUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "看守所收押信息变更")
@RestController
@RequestMapping("/acp/db/detainRegKss/correction")
@Validated
public class DetainRegKssCorrectionController {

    @Resource
    private BizDataCorrectionApplyService bizDataCorrectionApplyService;
    @Resource
    private BizDataCorrectionService bizDataCorrectionService;
    @Resource
    private DetainRegKssService detainRegKssService;
    @Resource
    private HealthCheckService healthCheckService;
    @Resource
    private BiometricInfoService biometricInfoService;

    @PostMapping("/changeSub")
    @ApiOperation(value = "看守所收押信息变更提交")
    @Transactional(rollbackFor = Exception.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "correctionDataType", value = "数据类型(01:基础信息,02:案件信息,03:办案人信息," +
                    "04:收押信息,05:其他信息,06:入所健康检查登记,07:生物信息采集)"),
            @ApiImplicitParam(name = "applyId", value = "变更申请id"),
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码"),
            @ApiImplicitParam(name = "xm", value = "监管人员姓名")
    })
    public CommonResult createDetainRegKss(@Valid @RequestBody JSONObject obj) {
        try {
            SessionUser sessionUser = SessionUserUtil.getSessionUser();
            DetainRegKssDataTypeEnum type = DetainRegKssDataTypeEnum.getTypeByCode(obj.getString("correctionDataType"));

            JSONObject dbData = null;
            String businessId = obj.getString("id");
            Object newData = EntityConverter.convertToEntity(type, obj);
            Object oldData = new Object();
            Map<String, Object> maps = new HashMap<>();
            switch (type) {
                case BASIC:
                case CASE:
                case POLICE:
                case DETAIN:
                case REST:
                    dbData = JSONObject.parseObject(JSONObject.toJSONString(detainRegKssService.getDetainRegKss(businessId)));
                    oldData = EntityConverter.convertToEntity(type, dbData);
                    maps.put("mainTable", DataCompareUtil.compareBasicData(newData, oldData));
                    break;
                case HEALTH:
                    dbData = JSONObject.parseObject(JSONObject.toJSONString(healthCheckService.getHealthCheck(businessId)));
                    oldData = EntityConverter.convertToEntity(type, dbData);
                    maps.put("mainTable", DataCompareUtil.compareBasicData(newData, oldData, "injuryAssessmentList"));
                    break;
                case BIOMETRIC:
                    // 获取数据库中的数据
//                    DetainRegKssBiometricVO detainRegKssExtraVO = JSONObject.parseObject(JSONObject.toJSONString(
//                            detainRegKssService.getDetainRegKss(businessId)), DetainRegKssBiometricVO.class);
//                    List<BiometricInfoDO> biometricDOList = biometricInfoService.getBiometricInfoByRybh(obj.getString("jgrybm"));
//                    detainRegKssExtraVO.setBiometricList(BeanUtils.toBean(biometricDOList, BiometricInfoRespVO.class));
                    dbData = JSONObject.parseObject(JSONObject.toJSONString(healthCheckService.getHealthCheck(businessId)));
//                    dbData = JSONObject.parseObject(JSONObject.toJSONString(detainRegKssExtraVO));
                    oldData = EntityConverter.convertToEntity(type, dbData);
//                    maps = DataCompareUtil.compareComplexData(newData, oldData, "biometricList");
                    maps.put("mainTable", DataCompareUtil.compareBasicData(newData, oldData, "biometricList"));
                    break;
            }
            System.out.println("变更内容：" + JSONObject.parseObject(JSONObject.toJSONString(maps)));
            String applyId = obj.getString("applyId");
            // 预生成申请记录
            if (StringUtil.isNullBlank(applyId)) {
                BizDataCorrectionApplyDO applyDO = new BizDataCorrectionApplyDO();
                applyDO.setJgrybm(obj.getString("jgrybm"));
                applyDO.setJgryxm(obj.getString("xm"));
                applyDO.setBusinessType(BizCorrectionBusinessTypeEnum.DETAIN_KSS.getCode());
                applyDO.setCorrectionType("1");
                applyDO.setStatus("01");
                applyDO.setOperator(sessionUser.getName());
                applyDO.setOperatorSfzh(sessionUser.getIdCard());
                applyDO.setOperatorTime(new Date());
                bizDataCorrectionApplyService.save(applyDO);
                applyId = applyDO.getId();
            }
            // 生成变更记录
            // 如果已有相同类型业务编号的数据，需要覆盖旧数据
            BizDataCorrectionDO correctionDO = bizDataCorrectionService.getOne(new LambdaQueryWrapper<BizDataCorrectionDO>()
                    .eq(BizDataCorrectionDO::getCorrectionApplyId, applyId)
                    .eq(BizDataCorrectionDO::getBusinessId, businessId)
                    .eq(BizDataCorrectionDO::getBusinessCode, obj.getString("correctionDataType")));
            if (ObjectUtil.isEmpty(correctionDO)) {
                correctionDO = new BizDataCorrectionDO();
                correctionDO.setCorrectionApplyId(applyId);
                correctionDO.setBusinessCode(type.getCode());
                correctionDO.setBusinessName(type.getName());
                correctionDO.setBusinessId(businessId);
            }

            correctionDO.setContent(JSONObject.toJSONString(obj));
            correctionDO.setHistoryContent(JSONObject.toJSONString(oldData));
            correctionDO.setChangedContent(JSONObject.toJSONString(maps));
            bizDataCorrectionService.saveOrUpdate(correctionDO);
            return success(correctionDO);
//            return success();
        } catch (Exception e) {
            e.printStackTrace();
            return error("变更信息提交失败!" + e.getMessage());
        }
    }

    @PostMapping("/apply")
    @ApiOperation(value = "看守所收押信息变更申请提交")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult apply(@Valid @RequestBody DetainRegKssCorrectionSaveVO saveVO) {
        List<BizDataCorrectionDataVO> correctionVOList = saveVO.getData();
        // 填充理由
        if (CollectionUtil.isNotNull(saveVO.getData())) {
            Map<String, List<BizDataCorrectionDataVO>> correctionMap = correctionVOList.stream()
                    .collect(Collectors.groupingBy(json -> json.getCorrectionId()));
            List<BizDataCorrectionDO> dbCorrectionList = bizDataCorrectionService.getByApplyId(saveVO.getApplyId());
            for (BizDataCorrectionDO dbCorrectionDO : dbCorrectionList) {
                List<BizDataCorrectionDataVO> correctionDataVOS = correctionMap.get(dbCorrectionDO.getId());
                JSONObject dbChangedData = JSONObject.parseObject(dbCorrectionDO.getChangedContent());
                for (String key : dbChangedData.keySet()) {
                    List<BizDataCorrectionDataVO> dbCorrectionDataList = JSONObject.parseArray(dbChangedData.getString(key),
                            BizDataCorrectionDataVO.class);
                    for (BizDataCorrectionDataVO dbCorrectionDataVO : dbCorrectionDataList) {
                        for (BizDataCorrectionDataVO correctionDataVO : correctionDataVOS) {
                            if (dbCorrectionDataVO.getId().equals(correctionDataVO.getId())) {
                                dbCorrectionDataVO.setReason(correctionDataVO.getReason());
                                dbCorrectionDataVO.setRemark(correctionDataVO.getRemark());
                                break;
                            }
                        }
                    }
                    dbChangedData.put(key, dbCorrectionDataList);
                }
                dbCorrectionDO.setChangedContent(JSONObject.toJSONString(dbChangedData));
            }
            bizDataCorrectionService.updateBatchById(dbCorrectionList);
        }
        // 修改申请状态
        BizDataCorrectionApplyDO applyDO = new BizDataCorrectionApplyDO();
        applyDO.setId(saveVO.getApplyId());
        applyDO.setActInstId(saveVO.getActInstId());
        applyDO.setTaskId(saveVO.getTaskId());
        applyDO.setStatus("02");
        bizDataCorrectionApplyService.updateById(applyDO);
        return success();
    }

    @PostMapping("/approve")
    @ApiOperation(value = "看守所收押信息变更申请审批")
    @Transactional(rollbackFor = Exception.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "applyId", value = "变更申请id"),
            @ApiImplicitParam(name = "status", value = "状态"),
            @ApiImplicitParam(name = "actInstId", value = "ACT流程实例Id"),
            @ApiImplicitParam(name = "taskId", value = "任务ID")
    })
    public CommonResult approve(@Valid @RequestBody JSONObject param) {
        // 修改业务信息
        if ("03".equals(param.getString("status"))) {
            List<BizDataCorrectionDO> list = bizDataCorrectionService.list(new LambdaQueryWrapper<BizDataCorrectionDO>()
                    .eq(BizDataCorrectionDO::getCorrectionApplyId, param.getString("applyId")));

            for (BizDataCorrectionDO correctionDO : list) {
                DetainRegKssDataTypeEnum type = DetainRegKssDataTypeEnum.getTypeByCode(correctionDO.getBusinessCode());
                // 根据变更内容组装修改内容实体
                JSONObject dataJson = new JSONObject();
                JSONObject correctionData = JSONObject.parseObject(correctionDO.getChangedContent());
                List<DetainRegKssChangeDataVO> mainTableDataList = JSONObject.parseArray(
                        correctionData.getString("mainTable"), DetainRegKssChangeDataVO.class);
                dataJson.put("id", correctionDO.getBusinessId());
                mainTableDataList.forEach(change -> dataJson.put(change.getField(), change.getNewValue()));

                System.out.println("变更审批成功，修改内容：" + dataJson);
                // 根据类型修改实际数据
                switch (type) {
                    case BASIC:
                    case CASE:
                    case POLICE:
                    case DETAIN:
                    case REST:
                        DetainRegKssDO detainRegKssDO = JSONObject.parseObject(JSONObject.toJSONString(dataJson),
                                DetainRegKssDO.class);
                        detainRegKssService.updateById(detainRegKssDO);
                        break;
                    case HEALTH:
                        HealthCheckDO healthCheckDO = JSONObject.parseObject(JSONObject.toJSONString(dataJson),
                                HealthCheckDO.class);
                        healthCheckService.updateById(healthCheckDO);
                        break;
                    case BIOMETRIC:
                        // 修改基本信息表数据
                        DetainRegKssDO biometricDetainRegKssDO = JSONObject.parseObject(JSONObject.toJSONString(dataJson),
                                DetainRegKssDO.class);
                        detainRegKssService.updateById(biometricDetainRegKssDO);

//                        // 修改生物信息采集表数据
//                        List<DetainRegKssChangeDataVO> biometricDataList = JSONObject.parseArray(
//                                correctionData.getString("biometricList"), DetainRegKssChangeDataVO.class);
//                        List<BiometricInfoDO> createList = new ArrayList<>();
//                        List<BiometricInfoDO> updateList = new ArrayList<>();
//                        List<String> deleteList = new ArrayList<>();
//                        for (DetainRegKssChangeDataVO detainRegKssChangeDataVO : biometricDataList) {
//                            BiometricInfoDO oldValue = JSONObject.parseObject(detainRegKssChangeDataVO.getOldValue(), BiometricInfoDO.class);
//                            BiometricInfoDO newValue = JSONObject.parseObject(detainRegKssChangeDataVO.getNewValue(), BiometricInfoDO.class);
//                            switch (detainRegKssChangeDataVO.getOperationType()) {
//                                case "created":
//                                    newValue.setJgrybm(newValue.getRybh());
//                                    newValue.setStatus("03");
//                                    createList.add(newValue);
//                                    break;
//                                case "updated":
//                                    updateList.add(newValue);
//                                    break;
//                                case "deleted":
//                                    deleteList.add(oldValue.getId());
//                                    break;
//                            }
//                        }
//                        if (CollectionUtil.isNotNull(createList))
//                            biometricInfoService.saveBatch(createList);
//                        if (CollectionUtil.isNotNull(updateList))
//                            biometricInfoService.updateBatchById(updateList);
//                        if (CollectionUtil.isNotNull(deleteList))
//                            biometricInfoService.removeByIds(deleteList);
                        break;
                }
            }
        }
        // 修改申请状态
        BizDataCorrectionApplyDO applyDO = new BizDataCorrectionApplyDO();
        applyDO.setId(param.getString("applyId"));
        applyDO.setActInstId(param.getString("actInstId"));
        applyDO.setTaskId(param.getString("taskId"));
        applyDO.setStatus(param.getString("status"));
        bizDataCorrectionApplyService.updateById(applyDO);

        return success();
    }

    @GetMapping("/getCorrectionList")
    @ApiOperation(value = "获取信息变更记录")
    public CommonResult getCorrectionList(@RequestParam("applyId") String applyId) {
        BizDataCorrectionApplyDO applyDO = bizDataCorrectionApplyService.getById(applyId);
        List<BizDataCorrectionDO> list = bizDataCorrectionService.list(new LambdaQueryWrapper<BizDataCorrectionDO>()
                .eq(BizDataCorrectionDO::getCorrectionApplyId, applyId));

        List<BizDataCorrectionDataVO> dataList = new ArrayList<>();
        for (BizDataCorrectionDO bizDataCorrectionDO : list) {
            JSONObject changedData = JSONObject.parseObject(bizDataCorrectionDO.getChangedContent());
            for (String key : changedData.keySet()) {
                List<BizDataCorrectionDataVO> correctionDataList = JSONObject.parseArray(changedData.getString(key),
                        BizDataCorrectionDataVO.class);
                for (BizDataCorrectionDataVO correctionDataVO : correctionDataList) {
                    correctionDataVO.setCorrectionId(bizDataCorrectionDO.getId());
                    correctionDataVO.setBusinessType(applyDO.getBusinessType());
                    correctionDataVO.setBusinessTypeName(BizCorrectionBusinessTypeEnum.getNameByCode(applyDO.getBusinessType()));
                    correctionDataVO.setDataType(bizDataCorrectionDO.getBusinessCode());
                    correctionDataVO.setDateTypeName(bizDataCorrectionDO.getBusinessName());
                }
                dataList.addAll(correctionDataList);
            }
        }
        return success(dataList);
    }




}
