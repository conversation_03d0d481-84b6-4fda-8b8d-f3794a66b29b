package com.rs.module.acp.service.gj.edurehabcourses;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.*;
import com.rs.module.acp.entity.gj.EdurehabCoursesPlanDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-教育康复课程计划 Service 接口
 *
 * <AUTHOR>
 */
public interface EdurehabCoursesPlanService extends IBaseService<EdurehabCoursesPlanDO>{

    /**
     * 创建实战平台-管教业务-教育康复课程计划
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createEdurehabCoursesPlan(@Valid EdurehabCoursesPlanSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-教育康复课程计划
     *
     * @param updateReqVO 更新信息
     */
    void updateEdurehabCoursesPlan(@Valid EdurehabCoursesPlanSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-教育康复课程计划
     *
     * @param id 编号
     */
    void deleteEdurehabCoursesPlan(String id);

    /**
     * 获得实战平台-管教业务-教育康复课程计划
     *
     * @param id 编号
     * @return 实战平台-管教业务-教育康复课程计划
     */
    EdurehabCoursesPlanRespVO getEdurehabCoursesPlan(String id);

    /**
    * 获得实战平台-管教业务-教育康复课程计划分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-教育康复课程计划分页
    */
    PageResult<EdurehabCoursesPlanDO> getEdurehabCoursesPlanPage(EdurehabCoursesPlanPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-教育康复课程计划列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-教育康复课程计划列表
    */
    List<EdurehabCoursesPlanDO> getEdurehabCoursesPlanList(EdurehabCoursesPlanListReqVO listReqVO);


    EdurehabCoursesPlanRespVO getPlanTime(String orgCode);

    List<JqAreaVO> getPlanArea(String orgCode);

    void approval(EdurehabCoursesPlanApprovalReqVO approvalReqVO);

    EdurehabCoursesPlanRespVO getByDate(String orgCode, Date startDate, Date endDate);
}
