package com.rs.module.acp.controller.admin.db.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 实战平台-人员采集信息 VO")
@Data
public class CollectedPersonDetailVO {

    /**
     * 警综人员编号不能为空
     */
    @ApiModelProperty("警综人员编号")
    private String JZRYBH;

    @ApiModelProperty("人员编号")
    private String RYBH;

    @ApiModelProperty("案件编号")
    private String AJBH;

    /**
     * 被采集人姓名不能为空
     */
    @ApiModelProperty("被采集人姓名")
    private String XM;

    @ApiModelProperty("被采集人姓名汉语拼音")
    private String XMHYPY;

    @ApiModelProperty("被采集人身份证号码")
    private String GMSFHM;

    @ApiModelProperty("常用证件代码")
    private String CYZJDM;

    @ApiModelProperty("常用证件中文说明")
    private String CYZJ;

    @ApiModelProperty("常用证件号码")
    private String ZJHM;
    /**
     * 性别代码不能为空
     */
    @ApiModelProperty("性别代码")
    private String XBDM;

    @ApiModelProperty("性别（汉字）")
    private String XB;
    /**
     * 出生日期不能为空
     */
    @ApiModelProperty("8位出生日期")
    private String CSRQ;
    /**
     * 国籍不能为空
     */
    @ApiModelProperty("国籍代码")
    private String GJDM;
    /**
     * 国籍（汉字）不能为空
     */
    @ApiModelProperty("国籍（汉字）")
    private String GJ;

    @ApiModelProperty("民族代码")
    private String MZDM;

    @ApiModelProperty("民族（汉字）")
    private String MZ;
    /**
     * 户籍地行政区划（汉字）不能为空
     */
    @ApiModelProperty("户籍地行政区划代码")
    private String HJD_XZQHDM;

    @ApiModelProperty("户籍地行政区划（汉字）")
    private String HJD_XZQH;
    /**
     * 户籍地详址不能为空
     */
    @ApiModelProperty("户籍地详址")
    private String HJD_DZMC;
    /**
     * 现住地行政区划代码不能为空
     */
    @ApiModelProperty("现住地行政区划代码")
    private String XZD_XZQHDM;

    @ApiModelProperty("现住地行政区划（汉字）")
    private String XZD_XZQH;
    /**
     * 现住地详址不能为空
     */
    @ApiModelProperty("现住地详址")
    private String XZD_DZMC;

    @ApiModelProperty("出生地行政区划代码")
    private String CSD_XZQHDM;

    @ApiModelProperty("出生地行政区划（汉字）")
    private String CSD_XZQH;

    @ApiModelProperty("出生地详址")
    private String CSD_DZMC;

    @ApiModelProperty("政治面貌代码")
    private String ZZMMDM;

    @ApiModelProperty("政治面貌（汉字）")
    private String ZZMM;

    @ApiModelProperty("婚姻状况代码")
    private String HYZKDM;

    @ApiModelProperty("婚姻状况（汉字）")
    private String HYZK;

    @ApiModelProperty("宗教信仰代码")
    private String ZJXYDM;

    @ApiModelProperty("宗教信仰（汉字）")
    private String ZJXY;

    @ApiModelProperty("学历代码")
    private String XLDM;

    @ApiModelProperty("学历（汉字）")
    private String XL;

    @ApiModelProperty("个人身份代码")
    private String GRSFDM;

    @ApiModelProperty("个人身份（汉字）")
    private String GRSF;

    @ApiModelProperty("特殊身份代码")
    private String TSSFDM;

    @ApiModelProperty("特殊身份（汉字）")
    private String TSSF;

    @ApiModelProperty("被采集人联系电话")
    private String LXDH;
    /**
     * 被采集人员类别代码不能为空
     */
    @ApiModelProperty("被采集人员类别代码")
    private String BCJRYLBDM;

    @ApiModelProperty("被采集人员类别（汉字）")
    private String BCJRYLB;
    /**
     * 采集人姓名不能为空
     */
    @ApiModelProperty("采集人姓名")
    private String CJR_XM;
    /**
     * 采集人身份证号不能为空
     */
    @ApiModelProperty("采集人身份证号")
    private String CJR_SFHM;

    @ApiModelProperty("采集人警号")
    private String CJR_JH;
    /**
     * 采集单位代码不能为空
     */
    @ApiModelProperty("采集单位代码")
    private String CJDW_GAJGJGDM;
    /**
     * 采集单位名称不能为空
     */
    @ApiModelProperty("采集单位名称")
    private String CJDW_DWMC;
    /**
     * 采集时间，14位，yyyyMMddHHmmss不能为空
     */
    @ApiModelProperty("采集时间，14位，yyyyMMddHHmmss")
    private String CJSJ;
}
