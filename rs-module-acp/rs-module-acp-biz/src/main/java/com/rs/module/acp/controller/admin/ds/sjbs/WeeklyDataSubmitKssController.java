package com.rs.module.acp.controller.admin.ds.sjbs;

import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyDataSubmitJlsRespVO;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyDataSubmitKssRespVO;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyDataSubmitKssSaveReqVO;
import com.rs.module.acp.entity.ds.sjbs.WeeklyDataSubmitJlsDO;
import com.rs.module.acp.service.ds.sjbs.WeeklyDataSubmitJlsService;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.ds.sjbs.WeeklyDataSubmitKssDO;
import com.rs.module.acp.service.ds.sjbs.WeeklyDataSubmitKssService;

@Api(tags = "实战平台-数据固化-每周数据报送(看守所)")
@RestController
@RequestMapping("/acp/ds/weeklyDataSubmitKss")
@Validated
public class WeeklyDataSubmitKssController {

    @Resource
    private WeeklyDataSubmitKssService weeklyDataSubmitKssService;
    @Resource
    private WeeklyDataSubmitJlsService weeklyDataSubmitJlsService;
/*
    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-数据固化-每周数据报送(看守所)")
    public CommonResult<String> createWeeklyDataSubmitKss(@Valid @RequestBody WeeklyDataSubmitKssSaveReqVO createReqVO) {
        return success(weeklyDataSubmitKssService.createWeeklyDataSubmitKss(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-数据固化-每周数据报送(看守所)")
    public CommonResult<Boolean> updateWeeklyDataSubmitKss(@Valid @RequestBody WeeklyDataSubmitKssSaveReqVO updateReqVO) {
        weeklyDataSubmitKssService.updateWeeklyDataSubmitKss(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-数据固化-每周数据报送(看守所)")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteWeeklyDataSubmitKss(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           weeklyDataSubmitKssService.deleteWeeklyDataSubmitKss(id);
        }
        return success(true);
    }*/

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-数据固化-每周数据报送(看守所)")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<WeeklyDataSubmitKssRespVO> getWeeklyDataSubmitKss(@RequestParam("id") String id) {
        WeeklyDataSubmitKssDO weeklyDataSubmitKss = weeklyDataSubmitKssService.getWeeklyDataSubmitKss(id);
        return success(BeanUtils.toBean(weeklyDataSubmitKss, WeeklyDataSubmitKssRespVO.class));
    }
    @GetMapping("/getByDate")
    @ApiOperation(value = "获得实战平台-数据固化-每周数据报送(看守所)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编号"),
            @ApiImplicitParam(name = "startDate", value = "开始时间"),
            @ApiImplicitParam(name = "endDate", value = "结束时间")
    })
    public CommonResult<List<WeeklyDataSubmitKssRespVO>> getByDate(
            @RequestParam(value = "orgCode",required = false) String orgCode,
            @RequestParam(value = "startDate",required = false) String startDate,
            @RequestParam(value = "endDate",required = false) String endDate) {
        List<WeeklyDataSubmitKssDO> data = weeklyDataSubmitKssService.getWeeklyDataSubmitKssByDate(startDate,endDate,orgCode);
        return success(BeanUtils.toBean(data, WeeklyDataSubmitKssRespVO.class));
    }

    @GetMapping("/test")
    @ApiOperation(value = "测试实战平台-数据固化-每周数据报送(看守所)")
    public CommonResult<String> test() {
        weeklyDataSubmitKssService.saveForStatistic(null,null,null);
        weeklyDataSubmitJlsService.saveForStatistic(null,null,null);
        return success();
    }
}
