package com.rs.module.acp.service.db;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.db.vo.sugstopdetention.*;
import com.rs.module.acp.entity.db.SugStopDetentionDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.base.vo.ApproveReqVO;

/**
 * 实战平台-收押业务-建议停拘登记 Service 接口
 *
 * <AUTHOR>
 */
public interface SugStopDetentionService extends IBaseService<SugStopDetentionDO>{

    /**
     * 创建实战平台-收押业务-建议停拘登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createSugStopDetention(@Valid SugStopDetentionSaveReqVO createReqVO);

    /**
     * 更新实战平台-收押业务-建议停拘登记
     *
     * @param updateReqVO 更新信息
     */
    void updateSugStopDetention(@Valid SugStopDetentionSaveReqVO updateReqVO);

    /**
     * 删除实战平台-收押业务-建议停拘登记
     *
     * @param id 编号
     */
    void deleteSugStopDetention(String id);

    /**
     * 获得实战平台-收押业务-建议停拘登记
     *
     * @param id 编号
     * @return 实战平台-收押业务-建议停拘登记
     */
    SugStopDetentionDO getSugStopDetention(String id);

    /**
     * 保存并启动流程
     * @param createReqVO
     * @return
     * @throws Exception
     */
    String startProcess(SugStopDetentionSaveReqVO createReqVO) throws Exception ;

    /**
     * 民警审批
     * @param approveReqVO
     * @return
     * @throws Exception
     */
    Boolean policeApprove(ApproveReqVO approveReqVO) throws Exception ;

    /**
     * 医生审批
     * @param approveReqVO
     * @return
     * @throws Exception
     */
    Boolean doctorApprove(ApproveReqVO approveReqVO) throws Exception ;

    /**
     * 领导审批
     * @param approveReqVO
     * @return
     * @throws Exception
     */
    Boolean leaderApprove(ApproveReqVO approveReqVO) throws Exception ;

    /**
     * 所长审批
     * @param approveReqVO
     * @return
     * @throws Exception
     */

    Boolean directorApprove(ApproveReqVO approveReqVO) throws Exception ;

    /**
     * 法综反馈意见接收处理
     * @param approveReqVO
     * @return
     * @throws Exception
     */

    Boolean receiveProcess(ApproveReqVO approveReqVO) throws Exception ;

    String sjsPoliceCreateSugStopDetention(SugStopDetentionPoliceSaveReqVO createReqVO) throws Exception;

    String sjsDoctorCreateSugStopDetention(SugStopDetentionDoctorSaveReqVO createReqVO) throws Exception;

    String sjhDoctorsave(SugStopDetentionSjhDoctorSaveReqVO createReqVO)throws Exception;
}
