package com.rs.module.acp.service.zh;

import javax.validation.*;

import com.rs.module.acp.controller.admin.zh.vo.MeetingRecordsSaveReqVO;
import com.rs.module.acp.entity.zh.MeetingRecordsDO;
import com.bsp.common.orm.mybatis.service.IBaseService;

import java.util.List;

/**
 * 实战平台-综合管理-会议记录 Service 接口
 *
 * <AUTHOR>
 */
public interface MeetingRecordsService extends IBaseService<MeetingRecordsDO>{

    /**
     * 创建实战平台-综合管理-会议记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMeetingRecords(@Valid MeetingRecordsSaveReqVO createReqVO);

    /**
     * 更新实战平台-综合管理-会议记录
     *
     * @param updateReqVO 更新信息
     */
    void updateMeetingRecords(@Valid MeetingRecordsSaveReqVO updateReqVO);

    /**
     * 删除实战平台-综合管理-会议记录
     *
     * @param id 编号
     */
    void deleteMeetingRecords(String id);

    /**
     * 获得实战平台-综合管理-会议记录
     *
     * @param id 编号
     * @return 实战平台-综合管理-会议记录
     */
    MeetingRecordsDO getMeetingRecords(String id);

    //根据类型查询会议记录
    List<MeetingRecordsDO> getListMeetingRecordsByCategoryId(String categoryId,String jgrybm);
}
