package com.rs.module.acp.controller.admin.db.vo.third.haixin;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class IrisVO implements TransPojo {


    private String id;

    @ApiModelProperty("图片url")
    private String url;

    @ApiModelProperty("虹膜缺失情况")
    @Trans(type = TransType.DICTIONARY,key = "ZD_HX_HMCJ_HMQSQK")
    private String hmqsqkdm;

    @ApiModelProperty("虹膜眼位")
    @Trans(type = TransType.DICTIONARY,key = "ZD_HX_HMCJ_YWLX")
    private String hmywdm;

    @ApiModelProperty("是否强制采集")
    @Trans(type = TransType.DICTIONARY,key = "ZD_TYSFDM")
    private String qzcjbs;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("信息质量得分")
    private String xxzldf;

}
