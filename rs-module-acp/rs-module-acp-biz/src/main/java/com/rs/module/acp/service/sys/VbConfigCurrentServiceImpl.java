package com.rs.module.acp.service.sys;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.sys.vo.VbConfigCurrentListReqVO;
import com.rs.module.acp.controller.admin.sys.vo.VbConfigCurrentPageReqVO;
import com.rs.module.acp.controller.admin.sys.vo.VbConfigCurrentSaveReqVO;
import com.rs.module.acp.dao.sys.VbConfigCurrentDao;
import com.rs.module.acp.entity.sys.VbConfigCurrentDO;


/**
 * 实战平台-语音播报-即时配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class VbConfigCurrentServiceImpl extends BaseServiceImpl<VbConfigCurrentDao, VbConfigCurrentDO> implements VbConfigCurrentService {

    @Resource
    private VbConfigCurrentDao vbConfigCurrentDao;

    @Resource
    private VbTaskService taskService;
    
    @Override
    public String createVbConfigCurrent(VbConfigCurrentSaveReqVO createReqVO) {
        // 插入
        VbConfigCurrentDO vbConfigCurrent = BeanUtils.toBean(createReqVO, VbConfigCurrentDO.class);
        vbConfigCurrentDao.insert(vbConfigCurrent);
        
        // 查询数据库即时播报任务
        VbConfigCurrentDO currentDO = getById(vbConfigCurrent.getId());
        
        // 保存播报任务
        taskService.saveCurrentJob(currentDO);
        
        // 返回
        return vbConfigCurrent.getId();
    }

    @Override
    public void updateVbConfigCurrent(VbConfigCurrentSaveReqVO updateReqVO) {
    	
        // 校验存在
        validateVbConfigCurrentExists(updateReqVO.getId());
        
        // 更新
        VbConfigCurrentDO updateObj = BeanUtils.toBean(updateReqVO, VbConfigCurrentDO.class);
        vbConfigCurrentDao.updateById(updateObj);
        
        // 查询数据库即时播报任务
        VbConfigCurrentDO currentDO = getById(updateObj.getId());
        
        // 保存播报任务
        taskService.saveCurrentJob(currentDO);
    }

    @Override
    public void deleteVbConfigCurrent(String id) {
        // 校验存在
        validateVbConfigCurrentExists(id);
        // 删除
        vbConfigCurrentDao.deleteById(id);
    }

    private void validateVbConfigCurrentExists(String id) {
        if (vbConfigCurrentDao.selectById(id) == null) {
            throw new ServerException("实战平台-语音播报-即时配置数据不存在");
        }
    }

    @Override
    public VbConfigCurrentDO getVbConfigCurrent(String id) {
        return vbConfigCurrentDao.selectById(id);
    }

    @Override
    public PageResult<VbConfigCurrentDO> getVbConfigCurrentPage(VbConfigCurrentPageReqVO pageReqVO) {
        return vbConfigCurrentDao.selectPage(pageReqVO);
    }

    @Override
    public List<VbConfigCurrentDO> getVbConfigCurrentList(VbConfigCurrentListReqVO listReqVO) {
        return vbConfigCurrentDao.selectList(listReqVO);
    }

    @Override
    public List<VbConfigCurrentDO> getWaittingCurrentConfig(String orgCode) {
    	Date now = new Date();
    	return vbConfigCurrentDao.selectList(new LambdaQueryWrapperX<VbConfigCurrentDO>()
    			.eqIfPresent(VbConfigCurrentDO::getOrgCode, orgCode)
    			.eq(VbConfigCurrentDO::getIsEnabled, 1)
    			.geIfPresent(VbConfigCurrentDO::getStartTime, now)
    			.leIfPresent(VbConfigCurrentDO::getEndTime, now)
    	);
    }
}
