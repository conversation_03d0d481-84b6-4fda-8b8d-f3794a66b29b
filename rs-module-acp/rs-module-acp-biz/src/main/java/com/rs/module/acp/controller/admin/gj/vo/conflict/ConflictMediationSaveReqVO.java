package com.rs.module.acp.controller.admin.gj.vo.conflict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-社会矛盾化解新增/修改 Request VO")
@Data
public class ConflictMediationSaveReqVO {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("事件编号")
    @NotEmpty(message = "事件编号不能为空")
    private String eventCode;

    @ApiModelProperty("主持人")
    @NotEmpty(message = "主持人不能为空")
    private String hostName;

    @ApiModelProperty("主持单位")
    @NotEmpty(message = "主持单位不能为空")
    private String hostOrg;

    @ApiModelProperty("调解地点")
    @NotEmpty(message = "调解地点不能为空")
    private String mediationLocation;

    @ApiModelProperty("调解情况")
    @NotEmpty(message = "调解情况不能为空")
    private String mediationState;

    @ApiModelProperty("调解结果（1：调解成功，2：调解暂缓，3：不用调解）")
    @NotEmpty(message = "调解结果（1：调解成功，2：调解暂缓，3：不用调解）不能为空")
    private String mediationResult;

    @ApiModelProperty("矛盾化解结果")
    private String resolutionResult;

    @ApiModelProperty("经验总结")
    private String experienceSummary;

    @ApiModelProperty("调解经办人身份证号")
    @NotEmpty(message = "调解经办人身份证号不能为空")
    private String mediationOperatorSfzh;

    @ApiModelProperty("调解经办人姓名")
    @NotEmpty(message = "调解经办人姓名不能为空")
    private String mediationOperatorXm;

    @ApiModelProperty("调解经办时间")
    private Date mediationOperatorTime;

    @ApiModelProperty("跟踪回访")
    private String followUpVisit;

    @ApiModelProperty("回访时间")
    private Date followUpTime;

    @ApiModelProperty("回访电话")
    private String followUpPhone;

    @ApiModelProperty("附件地址")
    private String attUrl;

    @ApiModelProperty("相关单位")
    private List<ConflictMediationOrgSaveReqVO> orgReqVOS;

}
