package com.rs.module.acp.dao.wb;

import java.util.*;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.LawyerMeetingDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
 * 实战平台-窗口业务-律师会见登记 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface LawyerMeetingDao extends IBaseDao<LawyerMeetingDO> {


    default PageResult<LawyerMeetingDO> selectPage(LawyerMeetingPageReqVO reqVO) {
        Page<LawyerMeetingDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<LawyerMeetingDO> wrapper = new LambdaQueryWrapperX<LawyerMeetingDO>()
                .eqIfPresent(LawyerMeetingDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(LawyerMeetingDO::getLawyer1Id, reqVO.getLawyer1Id())
                .likeIfPresent(LawyerMeetingDO::getLawyer1Name, reqVO.getLawyer1Name())
                .eqIfPresent(LawyerMeetingDO::getLawyer1Gender, reqVO.getLawyer1Gender())
                .eqIfPresent(LawyerMeetingDO::getLawyer1IdNumber, reqVO.getLawyer1IdNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer1Contact, reqVO.getLawyer1Contact())
                .eqIfPresent(LawyerMeetingDO::getLawyer1Type, reqVO.getLawyer1Type())
                .eqIfPresent(LawyerMeetingDO::getLawyer1PracticeLicenseNumber, reqVO.getLawyer1PracticeLicenseNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer1EntrustStage, reqVO.getLawyer1EntrustStage())
                .eqIfPresent(LawyerMeetingDO::getLawyer1Firm, reqVO.getLawyer1Firm())
                .eqIfPresent(LawyerMeetingDO::getLawyer1EntrustType, reqVO.getLawyer1EntrustType())
                .likeIfPresent(LawyerMeetingDO::getLawyer1PrincipalName, reqVO.getLawyer1PrincipalName())
                .eqIfPresent(LawyerMeetingDO::getLawyer1PrincipalIdNumber, reqVO.getLawyer1PrincipalIdNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer1PowerOfAttorneyType, reqVO.getLawyer1PowerOfAttorneyType())
                .eqIfPresent(LawyerMeetingDO::getLawyer1PowerOfAttorneyPath, reqVO.getLawyer1PowerOfAttorneyPath())
                .eqIfPresent(LawyerMeetingDO::getLawyer1PracticeCertificatePa, reqVO.getLawyer1PracticeCertificatePa())
                .eqIfPresent(LawyerMeetingDO::getLawyer1LetterNumber, reqVO.getLawyer1LetterNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer2Id, reqVO.getLawyer2Id())
                .likeIfPresent(LawyerMeetingDO::getLawyer2Name, reqVO.getLawyer2Name())
                .eqIfPresent(LawyerMeetingDO::getLawyer2Gender, reqVO.getLawyer2Gender())
                .eqIfPresent(LawyerMeetingDO::getLawyer2IdNumber, reqVO.getLawyer2IdNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer2Contact, reqVO.getLawyer2Contact())
                .eqIfPresent(LawyerMeetingDO::getLawyer2Type, reqVO.getLawyer2Type())
                .eqIfPresent(LawyerMeetingDO::getLawyer2PracticeLicenseNumber, reqVO.getLawyer2PracticeLicenseNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer2EntrustStage, reqVO.getLawyer2EntrustStage())
                .eqIfPresent(LawyerMeetingDO::getLawyer2Firm, reqVO.getLawyer2Firm())
                .eqIfPresent(LawyerMeetingDO::getLawyer2EntrustType, reqVO.getLawyer2EntrustType())
                .likeIfPresent(LawyerMeetingDO::getLawyer2PrincipalName, reqVO.getLawyer2PrincipalName())
                .eqIfPresent(LawyerMeetingDO::getLawyer2PrincipalIdNumber, reqVO.getLawyer2PrincipalIdNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer2PowerOfAttorneyType, reqVO.getLawyer2PowerOfAttorneyType())
                .eqIfPresent(LawyerMeetingDO::getLawyer2PowerOfAttorneyPath, reqVO.getLawyer2PowerOfAttorneyPath())
                .eqIfPresent(LawyerMeetingDO::getLawyer2PracticeCertificatePa, reqVO.getLawyer2PracticeCertificatePa())
                .eqIfPresent(LawyerMeetingDO::getLawyer2LetterNumber, reqVO.getLawyer2LetterNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer3Id, reqVO.getLawyer3Id())
                .likeIfPresent(LawyerMeetingDO::getLawyer3Name, reqVO.getLawyer3Name())
                .eqIfPresent(LawyerMeetingDO::getLawyer3Gender, reqVO.getLawyer3Gender())
                .eqIfPresent(LawyerMeetingDO::getLawyer3IdNumber, reqVO.getLawyer3IdNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer3Contact, reqVO.getLawyer3Contact())
                .eqIfPresent(LawyerMeetingDO::getLawyer3Type, reqVO.getLawyer3Type())
                .eqIfPresent(LawyerMeetingDO::getLawyer3PracticeLicenseNumber, reqVO.getLawyer3PracticeLicenseNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer3EntrustStage, reqVO.getLawyer3EntrustStage())
                .eqIfPresent(LawyerMeetingDO::getLawyer3Firm, reqVO.getLawyer3Firm())
                .eqIfPresent(LawyerMeetingDO::getLawyer3EntrustType, reqVO.getLawyer3EntrustType())
                .likeIfPresent(LawyerMeetingDO::getLawyer3PrincipalName, reqVO.getLawyer3PrincipalName())
                .eqIfPresent(LawyerMeetingDO::getLawyer3PrincipalIdNumber, reqVO.getLawyer3PrincipalIdNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer3PowerOfAttorneyType, reqVO.getLawyer3PowerOfAttorneyType())
                .eqIfPresent(LawyerMeetingDO::getLawyer3PowerOfAttorneyPath, reqVO.getLawyer3PowerOfAttorneyPath())
                .eqIfPresent(LawyerMeetingDO::getLawyer3PracticeCertificatePa, reqVO.getLawyer3PracticeCertificatePa())
                .eqIfPresent(LawyerMeetingDO::getLawyer3LetterNumber, reqVO.getLawyer3LetterNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer4Id, reqVO.getLawyer4Id())
                .likeIfPresent(LawyerMeetingDO::getLawyer4Name, reqVO.getLawyer4Name())
                .eqIfPresent(LawyerMeetingDO::getLawyer4Gender, reqVO.getLawyer4Gender())
                .eqIfPresent(LawyerMeetingDO::getLawyer4IdNumber, reqVO.getLawyer4IdNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer4Contact, reqVO.getLawyer4Contact())
                .eqIfPresent(LawyerMeetingDO::getLawyer4Type, reqVO.getLawyer4Type())
                .eqIfPresent(LawyerMeetingDO::getLawyer4PracticeLicenseNumber, reqVO.getLawyer4PracticeLicenseNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer4EntrustStage, reqVO.getLawyer4EntrustStage())
                .eqIfPresent(LawyerMeetingDO::getLawyer4Firm, reqVO.getLawyer4Firm())
                .eqIfPresent(LawyerMeetingDO::getLawyer4EntrustType, reqVO.getLawyer4EntrustType())
                .likeIfPresent(LawyerMeetingDO::getLawyer4PrincipalName, reqVO.getLawyer4PrincipalName())
                .eqIfPresent(LawyerMeetingDO::getLawyer4PrincipalIdNumber, reqVO.getLawyer4PrincipalIdNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer4PowerOfAttorneyType, reqVO.getLawyer4PowerOfAttorneyType())
                .eqIfPresent(LawyerMeetingDO::getLawyer4PowerOfAttorneyPath, reqVO.getLawyer4PowerOfAttorneyPath())
                .eqIfPresent(LawyerMeetingDO::getLawyer4PracticeCertificatePa, reqVO.getLawyer4PracticeCertificatePa())
                .eqIfPresent(LawyerMeetingDO::getLawyer4LetterNumber, reqVO.getLawyer4LetterNumber())
                .eqIfPresent(LawyerMeetingDO::getMeetingMethod, reqVO.getMeetingMethod())
                .betweenIfPresent(LawyerMeetingDO::getAppointmentTime, reqVO.getAppointmentTime())
                .eqIfPresent(LawyerMeetingDO::getAppointmentTimeSlot, reqVO.getAppointmentTimeSlot())
                .eqIfPresent(LawyerMeetingDO::getRoomId, reqVO.getRoomId())
                .eqIfPresent(LawyerMeetingDO::getApprovalAuthority, reqVO.getApprovalAuthority())
                .eqIfPresent(LawyerMeetingDO::getApprovalDocumentNumber, reqVO.getApprovalDocumentNumber())
                .eqIfPresent(LawyerMeetingDO::getApprovalAttachmentPath, reqVO.getApprovalAttachmentPath())
                .eqIfPresent(LawyerMeetingDO::getRemarks, reqVO.getRemarks())
                .eqIfPresent(LawyerMeetingDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(LawyerMeetingDO::getApplyMeetingStartTime, reqVO.getApplyMeetingStartTime())
                .betweenIfPresent(LawyerMeetingDO::getApplyMeetingEndTime, reqVO.getApplyMeetingEndTime())
                .betweenIfPresent(LawyerMeetingDO::getAssignmentRoomTime, reqVO.getAssignmentRoomTime())
                .eqIfPresent(LawyerMeetingDO::getAssignmentPoliceSfzh, reqVO.getAssignmentPoliceSfzh())
                .eqIfPresent(LawyerMeetingDO::getAssignmentPolice, reqVO.getAssignmentPolice())
                .betweenIfPresent(LawyerMeetingDO::getCheckInTime, reqVO.getCheckInTime())
                .eqIfPresent(LawyerMeetingDO::getCheckInPoliceSfzh, reqVO.getCheckInPoliceSfzh())
                .eqIfPresent(LawyerMeetingDO::getCheckInPolice, reqVO.getCheckInPolice())
                .eqIfPresent(LawyerMeetingDO::getEscortingPoliceSfzh, reqVO.getEscortingPoliceSfzh())
                .eqIfPresent(LawyerMeetingDO::getEscortingPolice, reqVO.getEscortingPolice())
                .betweenIfPresent(LawyerMeetingDO::getEscortingTime, reqVO.getEscortingTime())
                .eqIfPresent(LawyerMeetingDO::getEscortingOperatorSfzh, reqVO.getEscortingOperatorSfzh())
                .eqIfPresent(LawyerMeetingDO::getEscortingOperator, reqVO.getEscortingOperator())
                .betweenIfPresent(LawyerMeetingDO::getEscortingOperatorTime, reqVO.getEscortingOperatorTime())
                .eqIfPresent(LawyerMeetingDO::getInspectionResult, reqVO.getInspectionResult())
                .eqIfPresent(LawyerMeetingDO::getProhibitedItems, reqVO.getProhibitedItems())
                .eqIfPresent(LawyerMeetingDO::getPhysicalExam, reqVO.getPhysicalExam())
                .eqIfPresent(LawyerMeetingDO::getAbnormalSituations, reqVO.getAbnormalSituations())
                .eqIfPresent(LawyerMeetingDO::getProhibitedItemsImgUrl, reqVO.getProhibitedItemsImgUrl())
                .eqIfPresent(LawyerMeetingDO::getPhysicalExamImgUrl, reqVO.getPhysicalExamImgUrl())
                .eqIfPresent(LawyerMeetingDO::getAbnormalSituationsImgUrl, reqVO.getAbnormalSituationsImgUrl())
                .eqIfPresent(LawyerMeetingDO::getIsProhibitedItems, reqVO.getIsProhibitedItems())
                .betweenIfPresent(LawyerMeetingDO::getInspectionTime, reqVO.getInspectionTime())
                .eqIfPresent(LawyerMeetingDO::getInspectorSfzh, reqVO.getInspectorSfzh())
                .eqIfPresent(LawyerMeetingDO::getInspector, reqVO.getInspector())
                .betweenIfPresent(LawyerMeetingDO::getMeetingStartTime, reqVO.getMeetingStartTime())
                .betweenIfPresent(LawyerMeetingDO::getMeetingEndTime, reqVO.getMeetingEndTime())
                .eqIfPresent(LawyerMeetingDO::getReturnInspectorSfzh, reqVO.getReturnInspectorSfzh())
                .eqIfPresent(LawyerMeetingDO::getReturnInspector, reqVO.getReturnInspector())
                .betweenIfPresent(LawyerMeetingDO::getReturnInspectionTime, reqVO.getReturnInspectionTime())
                .eqIfPresent(LawyerMeetingDO::getReturnInspectionResult, reqVO.getReturnInspectionResult())
                .betweenIfPresent(LawyerMeetingDO::getReturnTime, reqVO.getReturnTime())
                .eqIfPresent(LawyerMeetingDO::getReturnPoliceSfzh, reqVO.getReturnPoliceSfzh())
                .eqIfPresent(LawyerMeetingDO::getReturnPolice, reqVO.getReturnPolice())
                .eqIfPresent(LawyerMeetingDO::getReturnOperatorSfzh, reqVO.getReturnOperatorSfzh())
                .eqIfPresent(LawyerMeetingDO::getReturnOperator, reqVO.getReturnOperator())
                .betweenIfPresent(LawyerMeetingDO::getReturnOperatorTime, reqVO.getReturnOperatorTime())
                .eqIfPresent(LawyerMeetingDO::getReturnProhibitedItems, reqVO.getReturnProhibitedItems())
                .eqIfPresent(LawyerMeetingDO::getReturnPhysicalExam, reqVO.getReturnPhysicalExam())
                .eqIfPresent(LawyerMeetingDO::getReturnAbnormalSituations, reqVO.getReturnAbnormalSituations())
                ;
        if(reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        }else {
            wrapper.orderByDesc(LawyerMeetingDO::getAddTime);
        }
        Page<LawyerMeetingDO> lawyerMeetingPage = selectPage(page, wrapper);
        return new PageResult<>(lawyerMeetingPage.getRecords(), lawyerMeetingPage.getTotal());
    }
    default List<LawyerMeetingDO> selectList(LawyerMeetingListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<LawyerMeetingDO>()
                .eqIfPresent(LawyerMeetingDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(LawyerMeetingDO::getLawyer1Id, reqVO.getLawyer1Id())
                .likeIfPresent(LawyerMeetingDO::getLawyer1Name, reqVO.getLawyer1Name())
                .eqIfPresent(LawyerMeetingDO::getLawyer1Gender, reqVO.getLawyer1Gender())
                .eqIfPresent(LawyerMeetingDO::getLawyer1IdNumber, reqVO.getLawyer1IdNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer1Contact, reqVO.getLawyer1Contact())
                .eqIfPresent(LawyerMeetingDO::getLawyer1Type, reqVO.getLawyer1Type())
                .eqIfPresent(LawyerMeetingDO::getLawyer1PracticeLicenseNumber, reqVO.getLawyer1PracticeLicenseNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer1EntrustStage, reqVO.getLawyer1EntrustStage())
                .eqIfPresent(LawyerMeetingDO::getLawyer1Firm, reqVO.getLawyer1Firm())
                .eqIfPresent(LawyerMeetingDO::getLawyer1EntrustType, reqVO.getLawyer1EntrustType())
                .likeIfPresent(LawyerMeetingDO::getLawyer1PrincipalName, reqVO.getLawyer1PrincipalName())
                .eqIfPresent(LawyerMeetingDO::getLawyer1PrincipalIdNumber, reqVO.getLawyer1PrincipalIdNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer1PowerOfAttorneyType, reqVO.getLawyer1PowerOfAttorneyType())
                .eqIfPresent(LawyerMeetingDO::getLawyer1PowerOfAttorneyPath, reqVO.getLawyer1PowerOfAttorneyPath())
                .eqIfPresent(LawyerMeetingDO::getLawyer1PracticeCertificatePa, reqVO.getLawyer1PracticeCertificatePa())
                .eqIfPresent(LawyerMeetingDO::getLawyer1LetterNumber, reqVO.getLawyer1LetterNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer2Id, reqVO.getLawyer2Id())
                .likeIfPresent(LawyerMeetingDO::getLawyer2Name, reqVO.getLawyer2Name())
                .eqIfPresent(LawyerMeetingDO::getLawyer2Gender, reqVO.getLawyer2Gender())
                .eqIfPresent(LawyerMeetingDO::getLawyer2IdNumber, reqVO.getLawyer2IdNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer2Contact, reqVO.getLawyer2Contact())
                .eqIfPresent(LawyerMeetingDO::getLawyer2Type, reqVO.getLawyer2Type())
                .eqIfPresent(LawyerMeetingDO::getLawyer2PracticeLicenseNumber, reqVO.getLawyer2PracticeLicenseNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer2EntrustStage, reqVO.getLawyer2EntrustStage())
                .eqIfPresent(LawyerMeetingDO::getLawyer2Firm, reqVO.getLawyer2Firm())
                .eqIfPresent(LawyerMeetingDO::getLawyer2EntrustType, reqVO.getLawyer2EntrustType())
                .likeIfPresent(LawyerMeetingDO::getLawyer2PrincipalName, reqVO.getLawyer2PrincipalName())
                .eqIfPresent(LawyerMeetingDO::getLawyer2PrincipalIdNumber, reqVO.getLawyer2PrincipalIdNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer2PowerOfAttorneyType, reqVO.getLawyer2PowerOfAttorneyType())
                .eqIfPresent(LawyerMeetingDO::getLawyer2PowerOfAttorneyPath, reqVO.getLawyer2PowerOfAttorneyPath())
                .eqIfPresent(LawyerMeetingDO::getLawyer2PracticeCertificatePa, reqVO.getLawyer2PracticeCertificatePa())
                .eqIfPresent(LawyerMeetingDO::getLawyer2LetterNumber, reqVO.getLawyer2LetterNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer3Id, reqVO.getLawyer3Id())
                .likeIfPresent(LawyerMeetingDO::getLawyer3Name, reqVO.getLawyer3Name())
                .eqIfPresent(LawyerMeetingDO::getLawyer3Gender, reqVO.getLawyer3Gender())
                .eqIfPresent(LawyerMeetingDO::getLawyer3IdNumber, reqVO.getLawyer3IdNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer3Contact, reqVO.getLawyer3Contact())
                .eqIfPresent(LawyerMeetingDO::getLawyer3Type, reqVO.getLawyer3Type())
                .eqIfPresent(LawyerMeetingDO::getLawyer3PracticeLicenseNumber, reqVO.getLawyer3PracticeLicenseNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer3EntrustStage, reqVO.getLawyer3EntrustStage())
                .eqIfPresent(LawyerMeetingDO::getLawyer3Firm, reqVO.getLawyer3Firm())
                .eqIfPresent(LawyerMeetingDO::getLawyer3EntrustType, reqVO.getLawyer3EntrustType())
                .likeIfPresent(LawyerMeetingDO::getLawyer3PrincipalName, reqVO.getLawyer3PrincipalName())
                .eqIfPresent(LawyerMeetingDO::getLawyer3PrincipalIdNumber, reqVO.getLawyer3PrincipalIdNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer3PowerOfAttorneyType, reqVO.getLawyer3PowerOfAttorneyType())
                .eqIfPresent(LawyerMeetingDO::getLawyer3PowerOfAttorneyPath, reqVO.getLawyer3PowerOfAttorneyPath())
                .eqIfPresent(LawyerMeetingDO::getLawyer3PracticeCertificatePa, reqVO.getLawyer3PracticeCertificatePa())
                .eqIfPresent(LawyerMeetingDO::getLawyer3LetterNumber, reqVO.getLawyer3LetterNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer4Id, reqVO.getLawyer4Id())
                .likeIfPresent(LawyerMeetingDO::getLawyer4Name, reqVO.getLawyer4Name())
                .eqIfPresent(LawyerMeetingDO::getLawyer4Gender, reqVO.getLawyer4Gender())
                .eqIfPresent(LawyerMeetingDO::getLawyer4IdNumber, reqVO.getLawyer4IdNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer4Contact, reqVO.getLawyer4Contact())
                .eqIfPresent(LawyerMeetingDO::getLawyer4Type, reqVO.getLawyer4Type())
                .eqIfPresent(LawyerMeetingDO::getLawyer4PracticeLicenseNumber, reqVO.getLawyer4PracticeLicenseNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer4EntrustStage, reqVO.getLawyer4EntrustStage())
                .eqIfPresent(LawyerMeetingDO::getLawyer4Firm, reqVO.getLawyer4Firm())
                .eqIfPresent(LawyerMeetingDO::getLawyer4EntrustType, reqVO.getLawyer4EntrustType())
                .likeIfPresent(LawyerMeetingDO::getLawyer4PrincipalName, reqVO.getLawyer4PrincipalName())
                .eqIfPresent(LawyerMeetingDO::getLawyer4PrincipalIdNumber, reqVO.getLawyer4PrincipalIdNumber())
                .eqIfPresent(LawyerMeetingDO::getLawyer4PowerOfAttorneyType, reqVO.getLawyer4PowerOfAttorneyType())
                .eqIfPresent(LawyerMeetingDO::getLawyer4PowerOfAttorneyPath, reqVO.getLawyer4PowerOfAttorneyPath())
                .eqIfPresent(LawyerMeetingDO::getLawyer4PracticeCertificatePa, reqVO.getLawyer4PracticeCertificatePa())
                .eqIfPresent(LawyerMeetingDO::getLawyer4LetterNumber, reqVO.getLawyer4LetterNumber())
                .eqIfPresent(LawyerMeetingDO::getMeetingMethod, reqVO.getMeetingMethod())
                .betweenIfPresent(LawyerMeetingDO::getAppointmentTime, reqVO.getAppointmentTime())
                .eqIfPresent(LawyerMeetingDO::getAppointmentTimeSlot, reqVO.getAppointmentTimeSlot())
                .eqIfPresent(LawyerMeetingDO::getRoomId, reqVO.getRoomId())
                .eqIfPresent(LawyerMeetingDO::getApprovalAuthority, reqVO.getApprovalAuthority())
                .eqIfPresent(LawyerMeetingDO::getApprovalDocumentNumber, reqVO.getApprovalDocumentNumber())
                .eqIfPresent(LawyerMeetingDO::getApprovalAttachmentPath, reqVO.getApprovalAttachmentPath())
                .eqIfPresent(LawyerMeetingDO::getRemarks, reqVO.getRemarks())
                .eqIfPresent(LawyerMeetingDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(LawyerMeetingDO::getApplyMeetingStartTime, reqVO.getApplyMeetingStartTime())
                .betweenIfPresent(LawyerMeetingDO::getApplyMeetingEndTime, reqVO.getApplyMeetingEndTime())
                .betweenIfPresent(LawyerMeetingDO::getAssignmentRoomTime, reqVO.getAssignmentRoomTime())
                .eqIfPresent(LawyerMeetingDO::getAssignmentPoliceSfzh, reqVO.getAssignmentPoliceSfzh())
                .eqIfPresent(LawyerMeetingDO::getAssignmentPolice, reqVO.getAssignmentPolice())
                .betweenIfPresent(LawyerMeetingDO::getCheckInTime, reqVO.getCheckInTime())
                .eqIfPresent(LawyerMeetingDO::getCheckInPoliceSfzh, reqVO.getCheckInPoliceSfzh())
                .eqIfPresent(LawyerMeetingDO::getCheckInPolice, reqVO.getCheckInPolice())
                .eqIfPresent(LawyerMeetingDO::getEscortingPoliceSfzh, reqVO.getEscortingPoliceSfzh())
                .eqIfPresent(LawyerMeetingDO::getEscortingPolice, reqVO.getEscortingPolice())
                .betweenIfPresent(LawyerMeetingDO::getEscortingTime, reqVO.getEscortingTime())
                .eqIfPresent(LawyerMeetingDO::getEscortingOperatorSfzh, reqVO.getEscortingOperatorSfzh())
                .eqIfPresent(LawyerMeetingDO::getEscortingOperator, reqVO.getEscortingOperator())
                .betweenIfPresent(LawyerMeetingDO::getEscortingOperatorTime, reqVO.getEscortingOperatorTime())
                .eqIfPresent(LawyerMeetingDO::getInspectionResult, reqVO.getInspectionResult())
                .eqIfPresent(LawyerMeetingDO::getProhibitedItems, reqVO.getProhibitedItems())
                .eqIfPresent(LawyerMeetingDO::getPhysicalExam, reqVO.getPhysicalExam())
                .eqIfPresent(LawyerMeetingDO::getAbnormalSituations, reqVO.getAbnormalSituations())
                .eqIfPresent(LawyerMeetingDO::getProhibitedItemsImgUrl, reqVO.getProhibitedItemsImgUrl())
                .eqIfPresent(LawyerMeetingDO::getPhysicalExamImgUrl, reqVO.getPhysicalExamImgUrl())
                .eqIfPresent(LawyerMeetingDO::getAbnormalSituationsImgUrl, reqVO.getAbnormalSituationsImgUrl())
                .eqIfPresent(LawyerMeetingDO::getIsProhibitedItems, reqVO.getIsProhibitedItems())
                .betweenIfPresent(LawyerMeetingDO::getInspectionTime, reqVO.getInspectionTime())
                .eqIfPresent(LawyerMeetingDO::getInspectorSfzh, reqVO.getInspectorSfzh())
                .eqIfPresent(LawyerMeetingDO::getInspector, reqVO.getInspector())
                .betweenIfPresent(LawyerMeetingDO::getMeetingStartTime, reqVO.getMeetingStartTime())
                .betweenIfPresent(LawyerMeetingDO::getMeetingEndTime, reqVO.getMeetingEndTime())
                .eqIfPresent(LawyerMeetingDO::getReturnInspectorSfzh, reqVO.getReturnInspectorSfzh())
                .eqIfPresent(LawyerMeetingDO::getReturnInspector, reqVO.getReturnInspector())
                .betweenIfPresent(LawyerMeetingDO::getReturnInspectionTime, reqVO.getReturnInspectionTime())
                .eqIfPresent(LawyerMeetingDO::getReturnInspectionResult, reqVO.getReturnInspectionResult())
                .betweenIfPresent(LawyerMeetingDO::getReturnTime, reqVO.getReturnTime())
                .eqIfPresent(LawyerMeetingDO::getReturnPoliceSfzh, reqVO.getReturnPoliceSfzh())
                .eqIfPresent(LawyerMeetingDO::getReturnPolice, reqVO.getReturnPolice())
                .eqIfPresent(LawyerMeetingDO::getReturnOperatorSfzh, reqVO.getReturnOperatorSfzh())
                .eqIfPresent(LawyerMeetingDO::getReturnOperator, reqVO.getReturnOperator())
                .betweenIfPresent(LawyerMeetingDO::getReturnOperatorTime, reqVO.getReturnOperatorTime())
                .eqIfPresent(LawyerMeetingDO::getReturnProhibitedItems, reqVO.getReturnProhibitedItems())
                .eqIfPresent(LawyerMeetingDO::getReturnPhysicalExam, reqVO.getReturnPhysicalExam())
                .eqIfPresent(LawyerMeetingDO::getReturnAbnormalSituations, reqVO.getReturnAbnormalSituations())
                .orderByDesc(LawyerMeetingDO::getAddTime));    }

    List<JSONObject> getPrisonerListByAjbh(@Param("ajbh") String ajbh,@Param("orgCode") String orgCode);

    List<JSONObject> timeOverlapJudgment(@Param("jgrybm") String jgrybm,@Param("startTime") String startTime);

    IPage<LawyerMeetingRespVO> getHistoryMeetingByLwayerId(@Param("page") Page<LawyerMeetingRespVO> page,@Param("lawyerId") String lawyerId);

    List<JSONObject> getOnSiteNumbering(@Param("type") String type,@Param("orgCode") String orgCode);

    List<JSONObject> getremoteNumbering(@Param("type") String type,@Param("orgCode") String orgCode);
}
