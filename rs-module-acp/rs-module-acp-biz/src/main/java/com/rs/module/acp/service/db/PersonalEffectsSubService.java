package com.rs.module.acp.service.db;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.PersonalEffectsSubDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-收押业务-随身物品登记子 Service 接口
 *
 * <AUTHOR>
 */
public interface PersonalEffectsSubService extends IBaseService<PersonalEffectsSubDO>{

    /**
     * 创建实战平台-收押业务-随身物品登记子
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPersonalEffectsSub(@Valid PersonalEffectsSubSaveReqVO createReqVO);

    /**
     * 更新实战平台-收押业务-随身物品登记子
     *
     * @param updateReqVO 更新信息
     */
    void updatePersonalEffectsSub(@Valid PersonalEffectsSubSaveReqVO updateReqVO);

    /**
     * 删除实战平台-收押业务-随身物品登记子
     *
     * @param id 编号
     */
    void deletePersonalEffectsSub(String id);

    /**
     * 获得实战平台-收押业务-随身物品登记子
     *
     * @param id 编号
     * @return 实战平台-收押业务-随身物品登记子
     */
    PersonalEffectsSubDO getPersonalEffectsSub(String id);

    /**
    * 获得实战平台-收押业务-随身物品登记子分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-收押业务-随身物品登记子分页
    */
    PageResult<PersonalEffectsSubDO> getPersonalEffectsSubPage(PersonalEffectsSubPageReqVO pageReqVO);

    /**
    * 获得实战平台-收押业务-随身物品登记子列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-收押业务-随身物品登记子列表
    */
    List<PersonalEffectsSubDO> getPersonalEffectsSubList(PersonalEffectsSubListReqVO listReqVO);


    boolean createPersonalEffectsSubBatch(@Valid List<PersonalEffectsSubSaveReqVO> createReqVOList);

    List<PersonalEffectsSubDO> getPersonalEffectsSubByRybh(String rybh);
}
