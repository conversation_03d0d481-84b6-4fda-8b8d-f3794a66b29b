package com.rs.module.acp.controller.admin.gj.vo.face2face;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-管教业务-面对面配置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FaceToFaceConfigRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("检查项目")
    private String checkItem;
    @ApiModelProperty("是否禁用")
    private Short isDisabled;
}
