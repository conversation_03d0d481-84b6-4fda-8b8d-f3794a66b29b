package com.rs.module.acp.controller.admin.gj.vo.equipment;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EquipmentExtendApprovalApplyReqVO extends BaseVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @NotBlank(message = "id不能为空！")
    private String id;

    @ApiModelProperty("延长理由")
    @NotBlank(message = "延长理由不能为空！")
    private String extendReason;

    //@NotEmpty(message = "延长天数不能为空！")
    @ApiModelProperty("延长天数")
    private Integer extendDay;

    //@ApiModelProperty("延长时间")
    //private Date extendTime;

    @ApiModelProperty("是否临时固定")
    private Integer isTempFixation;

    @ApiModelProperty("固定天数")
    private String fixationDays;

}
