package com.rs.module.acp.service.gj.biometriccomp;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.biometriccomp.*;
import com.rs.module.acp.entity.gj.BiometricCompDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-生物特征比对 Service 接口
 *
 * <AUTHOR>
 */
public interface BiometricCompService extends IBaseService<BiometricCompDO>{

    /**
     * 创建实战平台-管教业务-生物特征比对
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createBiometricComp(@Valid BiometricCompSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-生物特征比对
     *
     * @param updateReqVO 更新信息
     */
    void updateBiometricComp(@Valid BiometricCompSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-生物特征比对
     *
     * @param id 编号
     */
    void deleteBiometricComp(String id);

    /**
     * 获得实战平台-管教业务-生物特征比对
     *
     * @param id 编号
     * @return 实战平台-管教业务-生物特征比对
     */
    BiometricCompDO getBiometricComp(String id);

    /**
    * 获得实战平台-管教业务-生物特征比对分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-生物特征比对分页
    */
    PageResult<BiometricCompDO> getBiometricCompPage(BiometricCompPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-生物特征比对列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-生物特征比对列表
    */
    List<BiometricCompDO> getBiometricCompList(BiometricCompListReqVO listReqVO);


    String compareCreate(BiometricComPareSaveReqVO createReqVO);

    void compareFeedback(BiometricCompFkReqVO fkReqVO);
}
