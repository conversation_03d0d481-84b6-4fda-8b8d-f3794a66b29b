package com.rs.module.acp.controller.app.gj.vo.groupinout;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/7/11 14:22
 */
@Data
@ApiModel(description = "管理后台 - 实战平台-集体出入-统计")
public class AppGroupInOutStatisticVO {

    @ApiModelProperty(value = "总人数")
    private Integer ryNum;
    @ApiModelProperty(value = "在监人员总数")
    private Integer zjRyNum;
    @ApiModelProperty(value = "外出人员总数")
    private Integer wcRyNum;
}
