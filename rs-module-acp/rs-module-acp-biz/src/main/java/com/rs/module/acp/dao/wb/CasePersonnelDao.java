package com.rs.module.acp.dao.wb;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.CasePersonnelDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-窗口业务-办案人员 Dao
*
* <AUTHOR>
*/
@Mapper
public interface CasePersonnelDao extends IBaseDao<CasePersonnelDO> {


    default PageResult<CasePersonnelDO> selectPage(CasePersonnelPageReqVO reqVO) {
        Page<CasePersonnelDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<CasePersonnelDO> wrapper = new LambdaQueryWrapperX<CasePersonnelDO>()
            .eqIfPresent(CasePersonnelDO::getJh, reqVO.getJh())
            .eqIfPresent(CasePersonnelDO::getXm, reqVO.getXm())
            .eqIfPresent(CasePersonnelDO::getZjlx, reqVO.getZjlx())
            .eqIfPresent(CasePersonnelDO::getZjhm, reqVO.getZjhm())
            .eqIfPresent(CasePersonnelDO::getBadwdm, reqVO.getBadwdm())
            .eqIfPresent(CasePersonnelDO::getBadwmc, reqVO.getBadwmc())
            .eqIfPresent(CasePersonnelDO::getLxfs, reqVO.getLxfs())
            .eqIfPresent(CasePersonnelDO::getXb, reqVO.getXb())
            .eqIfPresent(CasePersonnelDO::getZpUrl, reqVO.getZpUrl())
            .eqIfPresent(CasePersonnelDO::getGzzjUrl, reqVO.getGzzjUrl())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(CasePersonnelDO::getAddTime);
        }
        Page<CasePersonnelDO> casePersonnelPage = selectPage(page, wrapper);
        return new PageResult<>(casePersonnelPage.getRecords(), casePersonnelPage.getTotal());
    }
    default List<CasePersonnelDO> selectList(CasePersonnelListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CasePersonnelDO>()
            .eqIfPresent(CasePersonnelDO::getJh, reqVO.getJh())
            .eqIfPresent(CasePersonnelDO::getXm, reqVO.getXm())
            .eqIfPresent(CasePersonnelDO::getZjlx, reqVO.getZjlx())
            .eqIfPresent(CasePersonnelDO::getZjhm, reqVO.getZjhm())
            .eqIfPresent(CasePersonnelDO::getBadwdm, reqVO.getBadwdm())
            .eqIfPresent(CasePersonnelDO::getBadwmc, reqVO.getBadwmc())
            .eqIfPresent(CasePersonnelDO::getLxfs, reqVO.getLxfs())
            .eqIfPresent(CasePersonnelDO::getXb, reqVO.getXb())
            .eqIfPresent(CasePersonnelDO::getZpUrl, reqVO.getZpUrl())
            .eqIfPresent(CasePersonnelDO::getGzzjUrl, reqVO.getGzzjUrl())
        .orderByDesc(CasePersonnelDO::getAddTime));    }


    }
