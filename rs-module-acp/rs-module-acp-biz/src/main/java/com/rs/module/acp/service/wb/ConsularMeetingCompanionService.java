package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.ConsularMeetingCompanionDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-领事外事会见同行登记 Service 接口
 *
 * <AUTHOR>
 */
public interface ConsularMeetingCompanionService extends IBaseService<ConsularMeetingCompanionDO>{

    /**
     * 创建实战平台-窗口业务-领事外事会见同行登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createConsularMeetingCompanion(@Valid ConsularMeetingCompanionSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-领事外事会见同行登记
     *
     * @param updateReqVO 更新信息
     */
    void updateConsularMeetingCompanion(@Valid ConsularMeetingCompanionSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-领事外事会见同行登记
     *
     * @param id 编号
     */
    void deleteConsularMeetingCompanion(String id);

    /**
     * 获得实战平台-窗口业务-领事外事会见同行登记
     *
     * @param id 编号
     * @return 实战平台-窗口业务-领事外事会见同行登记
     */
    ConsularMeetingCompanionDO getConsularMeetingCompanion(String id);

    /**
    * 获得实战平台-窗口业务-领事外事会见同行登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-领事外事会见同行登记分页
    */
    PageResult<ConsularMeetingCompanionDO> getConsularMeetingCompanionPage(ConsularMeetingCompanionPageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-领事外事会见同行登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-领事外事会见同行登记列表
    */
    List<ConsularMeetingCompanionDO> getConsularMeetingCompanionList(ConsularMeetingCompanionListReqVO listReqVO);

    /**
     * 实战平台-窗口业务-根据领事外事会见ID保存同行登记列表
     * @param compainList
     * @param consularMeetingId
     * @return
     */
    boolean saveConsularMeetingCompanionList(List<ConsularMeetingCompanionSaveReqVO> compainList,String consularMeetingId);

    /**
     * 实战平台-窗口业务-根据领事外事会见ID获取同行人登记列表
     * @param consularMeetingId
     * @return
     */
    List<ConsularMeetingCompanionRespVO> getConsularMeetingCompanionListByconsularMeetingId(String consularMeetingId);

}
