package com.rs.module.acp.dao.wb;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.SocialRelationsDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-窗口业务-社会关系 Dao
*
* <AUTHOR>
*/
@Mapper
public interface SocialRelationsDao extends IBaseDao<SocialRelationsDO> {


    default PageResult<SocialRelationsDO> selectPage(SocialRelationsPageReqVO reqVO) {
        Page<SocialRelationsDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<SocialRelationsDO> wrapper = new LambdaQueryWrapperX<SocialRelationsDO>()
            .eqIfPresent(SocialRelationsDO::getJgrybm, reqVO.getJgrybm())
            .likeIfPresent(SocialRelationsDO::getName, reqVO.getName())
            .eqIfPresent(SocialRelationsDO::getGender, reqVO.getGender())
            .eqIfPresent(SocialRelationsDO::getIdType, reqVO.getIdType())
            .eqIfPresent(SocialRelationsDO::getIdNumber, reqVO.getIdNumber())
            .eqIfPresent(SocialRelationsDO::getRelationship, reqVO.getRelationship())
            .eqIfPresent(SocialRelationsDO::getContact, reqVO.getContact())
            .eqIfPresent(SocialRelationsDO::getWorkUnit, reqVO.getWorkUnit())
            .eqIfPresent(SocialRelationsDO::getAddress, reqVO.getAddress())
            .eqIfPresent(SocialRelationsDO::getOccupation, reqVO.getOccupation())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(SocialRelationsDO::getAddTime);
        }
        Page<SocialRelationsDO> socialRelationsPage = selectPage(page, wrapper);
        return new PageResult<>(socialRelationsPage.getRecords(), socialRelationsPage.getTotal());
    }
    default List<SocialRelationsDO> selectList(SocialRelationsListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SocialRelationsDO>()
            .eqIfPresent(SocialRelationsDO::getJgrybm, reqVO.getJgrybm())
            .likeIfPresent(SocialRelationsDO::getName, reqVO.getName())
            .eqIfPresent(SocialRelationsDO::getGender, reqVO.getGender())
            .eqIfPresent(SocialRelationsDO::getIdType, reqVO.getIdType())
            .eqIfPresent(SocialRelationsDO::getIdNumber, reqVO.getIdNumber())
            .eqIfPresent(SocialRelationsDO::getRelationship, reqVO.getRelationship())
            .eqIfPresent(SocialRelationsDO::getContact, reqVO.getContact())
            .eqIfPresent(SocialRelationsDO::getWorkUnit, reqVO.getWorkUnit())
            .eqIfPresent(SocialRelationsDO::getAddress, reqVO.getAddress())
            .eqIfPresent(SocialRelationsDO::getOccupation, reqVO.getOccupation())
        .orderByDesc(SocialRelationsDO::getAddTime));    }


    }
