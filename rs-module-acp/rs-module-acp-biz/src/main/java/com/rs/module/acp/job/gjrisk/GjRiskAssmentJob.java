package com.rs.module.acp.job.gjrisk;

import com.rs.module.acp.service.gj.riskassmt.RiskAssmtTodoService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 管教风险评估
 * <AUTHOR>
 * @date 2025/6/15 18:51
 */
@Component
@Slf4j
public class GjRiskAssmentJob {

    @Resource
    private RiskAssmtTodoService riskAssmtTodoService;


    /**
     * 新入所评估
     * <AUTHOR>
     * @date 2025/6/15 20:17
     * @param []
     * @return void
     */
    @XxlJob("newEntrantJob")
    public void newEntrantJob() {
        XxlJobHelper.log("新入所评估:------开始------");
        try {
            riskAssmtTodoService.newEntrantJob();
        } catch (Exception e) {
            log.error("入所评估-推送失败", e);
            XxlJobHelper.log("新入所评估-状态任务异常：{}", e.getMessage());
        }
        XxlJobHelper.log("新入所评估:-----结束------");
    }


    /**
     *  重点人员关注评估
     * <AUTHOR>
     * @date 2025/6/19 17:34
     * @param []
     * @return void
     */
    @XxlJob("attentionPersonnel")
    public void attentionPersonnel() {
        XxlJobHelper.log("重点人员关注风险评估:------开始------");
        try {
            riskAssmtTodoService.attentionPersonnel();
        } catch (Exception e) {
            log.error("重点人员关注-推送失败", e);
            XxlJobHelper.log("重点人员关注-状态任务异常：{}", e.getMessage());
        }
        XxlJobHelper.log("重点人员关注:-----结束------");
    }
}
