package com.rs.module.acp.dao.zh;

import com.rs.module.acp.entity.zh.StaffDutyTeamPersonDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 值班模板班组人员信息 Dao
*
* <AUTHOR>
*/
@Mapper
public interface StaffDutyTeamPersonDao extends IBaseDao<StaffDutyTeamPersonDO> {

    void deleteByTeamId(@Param("teamId") String teamId);
}
