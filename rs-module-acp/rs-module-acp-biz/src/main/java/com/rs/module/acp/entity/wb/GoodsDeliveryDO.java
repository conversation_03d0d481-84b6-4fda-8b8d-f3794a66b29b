package com.rs.module.acp.entity.wb;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-窗口业务-物品顾送登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_wb_goods_delivery")
@KeySequence("acp_wb_goods_delivery_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_wb_goods_delivery")
public class GoodsDeliveryDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 顾送编号
     */
    private String deliveryNo;
    /**
     * 顾送日期
     */
    private Date deliveryDate;
    /**
     * 顾送人姓名
     */
    private String senderName;
    /**
     * 性别
     */
    private String gender;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 证件号码
     */
    private String idNumber;
    /**
     * 与被监管人关系
     */
    private String relationship;
    /**
     * 联系方式
     */
    private String contact;
    /**
     * 户籍地址
     */
    private String householdAddress;
    /**
     * 物品照片存储路径
     */
    private String goodsPhotoPath;
    /**
     * 物品信息
     */
    private String goodsInfo;
    /**
     * 物品总数
     */
    private Integer goodsTotal;
    /**
     * 状态
     */
    private String status;
    /**
     * 签收时间
     */
    private Date receiptTime;
    /**
     * 签名（存储签名文件路径或文本）
     */
    private String signature;
    /**
     * 拒签原因
     */
    private String rejectionReason;

}
