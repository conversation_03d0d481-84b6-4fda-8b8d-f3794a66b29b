package com.rs.module.acp.entity.zh;

import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 综合管理-绩效考核-记录 DO
 *
 * <AUTHOR>
 */
@TableName("acp_zh_assmt_record")
@KeySequence("acp_zh_assmt_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_assmt_record")
public class AssmtRecordDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 被考核人身份证号
     */
    private String assessedSfzh;
    /**
     * 被考核人姓名
     */
    private String assessedName;
    /**
     * 所属岗位
     */
    private String postName;
    /**
     * 考核月份
     */
    private String assmtMonth;
    /**
     * 指标类型ID
     */
    private String indicatorCateId;
    /**
     * 指标类型名称
     */
    private String indicatorCateName;
    /**
     * 截止日期
     */
    private Date expiryDate;
    /**
     * 加减分得分
     */
    private BigDecimal addsubtractScore;
    /**
     * 主观得分
     */
    private BigDecimal subjectiveScore;
    /**
     * 总得分
     */
    private BigDecimal totalScore;
    /**
     * 状态
     */
    private String status;

}
