package com.rs.module.acp.service.zh.deathregister;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.zh.vo.deathregister.DeathRegisterFilesSaveReqVO;
import com.rs.module.acp.entity.zh.DeathRegisterFilesDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-综合管理-死亡登记文件 Service 接口
 *
 * <AUTHOR>
 */
public interface DeathRegisterFilesService extends IBaseService<DeathRegisterFilesDO>{

    /**
     * 创建实战平台-综合管理-死亡登记文件
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createDeathRegisterFiles(@Valid DeathRegisterFilesSaveReqVO createReqVO);

    /**
     * 更新实战平台-综合管理-死亡登记文件
     *
     * @param updateReqVO 更新信息
     */
    void updateDeathRegisterFiles(@Valid DeathRegisterFilesSaveReqVO updateReqVO);

    /**
     * 删除实战平台-综合管理-死亡登记文件
     *
     * @param id 编号
     */
    void deleteDeathRegisterFiles(String id);

    /**
     * 获得实战平台-综合管理-死亡登记文件
     *
     * @param id 编号
     * @return 实战平台-综合管理-死亡登记文件
     */
    DeathRegisterFilesDO getDeathRegisterFiles(String id);

    void deleteAndSaveFile(String id, List<DeathRegisterFilesSaveReqVO> fileList);

    List<DeathRegisterFilesDO> getFileList(String deathRegisterId);
}
