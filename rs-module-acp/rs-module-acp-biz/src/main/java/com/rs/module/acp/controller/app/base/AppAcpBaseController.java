package com.rs.module.acp.controller.app.base;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomRespVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerAppRespVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.util.AgeCalculatorUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Validated
@RestController
@RequestMapping("/app/acp/base")
@Api(tags = "app端基础接口")
public class AppAcpBaseController {

    @Resource
    private PrisonerService prisonerService;

    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;

    @GetMapping("/getPrisonerList")
    @ApiOperation(value = "app端根据监室号查询人员信息")
    public CommonResult<List<PrisonerAppRespVO>> getPrisonerByJsh(@RequestParam("roomId") String roomId) {
        List<PrisonerVwRespVO> vwRespList = prisonerService.getPrisonerListByJsh(roomId);
        if(CollUtil.isNotEmpty(vwRespList)){
            List<PrisonerAppRespVO> respVOList =  BeanUtils.toBean(vwRespList, PrisonerAppRespVO.class);
            for (PrisonerAppRespVO prisonerVO : respVOList) {
                Integer age = AgeCalculatorUtil.calculateAge(prisonerVO.getCsrq());
                prisonerVO.setAge( age);
                if(prisonerVO.getRssj() != null){
                    prisonerVO.setRsts( DateUtil.betweenDay(prisonerVO.getRssj() , new Date(), true));
                }
            }
            return success(respVOList);
        }
        return success();
    }


    @PostMapping("/selectAreaPrisonRoom")
    @ApiOperation(value = "app端查询区域监室信息")
    @LogRecordAnnotation(bizModule = "acp:areaPrisonRoom:page", operateType = LogOperateType.QUERY, title = "获得实战平台-监管管理-区域监室分页",
            success = "获得实战平台-监管管理-区域监室分页成功", fail = "获得实战平台-监管管理-区域监室分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<AreaPrisonRoomRespVO>> getAreaPrisonRoomPage(@Valid @RequestBody AreaPrisonRoomPageReqVO pageReqVO) {
        pageReqVO.setExcludeSex(true);
        PageResult<AreaPrisonRoomDO> pageResult = areaPrisonRoomService.getAreaPrisonRoomPage(pageReqVO);
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        PageResult<AreaPrisonRoomRespVO> result = BeanUtils.toBean(pageResult, AreaPrisonRoomRespVO.class);
        if(result != null && CollUtil.isNotEmpty(result.getList())){
            for (AreaPrisonRoomRespVO room : result.getList()) {
                room.setPrisonerNum(prisonerService.getPrisonerCount(sessionUser.getOrgCode(), room.getRoomCode()));
            }
        }
        return success(result);
    }



}
