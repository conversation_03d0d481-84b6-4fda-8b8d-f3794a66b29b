package com.rs.module.acp.service.pi;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypeItemListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypeItemPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypeItemRespVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypeItemSaveReqVO;
import com.rs.module.acp.entity.pi.PrisonEventTypeItemDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-巡视管控-所情事件类型明细项 Service 接口
 *
 * <AUTHOR>
 */
public interface PrisonEventTypeItemService extends IBaseService<PrisonEventTypeItemDO>{

    /**
     * 创建实战平台-巡视管控-所情事件类型明细项
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrisonEventTypeItem(@Valid PrisonEventTypeItemSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-所情事件类型明细项
     *
     * @param updateReqVO 更新信息
     */
    void updatePrisonEventTypeItem(@Valid PrisonEventTypeItemSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-所情事件类型明细项
     *
     * @param id 编号
     */
    void deletePrisonEventTypeItem(String id);

    /**
     * 获得实战平台-巡视管控-所情事件类型明细项
     *
     * @param id 编号
     * @return 实战平台-巡视管控-所情事件类型明细项
     */
    PrisonEventTypeItemDO getPrisonEventTypeItem(String id);

    /**
    * 获得实战平台-巡视管控-所情事件类型明细项分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-巡视管控-所情事件类型明细项分页
    */
    PageResult<PrisonEventTypeItemDO> getPrisonEventTypeItemPage(PrisonEventTypeItemPageReqVO pageReqVO);

    /**
    * 获得实战平台-巡视管控-所情事件类型明细项列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-巡视管控-所情事件类型明细项列表
    */
    List<PrisonEventTypeItemDO> getPrisonEventTypeItemList(PrisonEventTypeItemListReqVO listReqVO);


    /**
     * 保存事件类型明细
     * @param eventItemList
     * @param typeId
     * @return
     */
    boolean savePrisonEventTypeTreeList(List<PrisonEventTypeItemSaveReqVO> eventItemList,String typeId);

    /**
     * 实战平台-巡视管控-根据所情配置ID删除所情事件类型明细项
     *
     * @param typeId
     */
    void deletePrisonEventTypeItemByTypeId(String typeId);

    /**
     * 根据所情配置ID，获取事件明细树形
     * @param typeId
     * @return
     */
    List<PrisonEventTypeItemRespVO> getPrisonEventTypeItemTreeByTypeId(String typeId);
}
