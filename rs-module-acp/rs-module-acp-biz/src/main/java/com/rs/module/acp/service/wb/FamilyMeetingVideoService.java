package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.FamilyMeetingVideoDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-单向视频家属会见 Service 接口
 *
 * <AUTHOR>
 */
public interface FamilyMeetingVideoService extends IBaseService<FamilyMeetingVideoDO>{

    /**
     * 创建实战平台-窗口业务-单向视频家属会见
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createFamilyMeetingVideo(@Valid FamilyMeetingVideoSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-单向视频家属会见
     *
     * @param updateReqVO 更新信息
     */
    void updateFamilyMeetingVideo(@Valid FamilyMeetingVideoSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-单向视频家属会见
     *
     * @param id 编号
     */
    void deleteFamilyMeetingVideo(String id);

    /**
     * 获得实战平台-窗口业务-单向视频家属会见
     *
     * @param id 编号
     * @return 实战平台-窗口业务-单向视频家属会见
     */
    FamilyMeetingVideoDO getFamilyMeetingVideo(String id);

    /**
    * 获得实战平台-窗口业务-单向视频家属会见分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-单向视频家属会见分页
    */
    PageResult<FamilyMeetingVideoDO> getFamilyMeetingVideoPage(FamilyMeetingVideoPageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-单向视频家属会见列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-单向视频家属会见列表
    */
    List<FamilyMeetingVideoDO> getFamilyMeetingVideoList(FamilyMeetingVideoListReqVO listReqVO);

    /**
     * 实战平台-窗口业务-家属单向视频排号信息
     * @return
     */
    List<JSONObject> getremoteNumbering();

    /**
     * 实战平台-窗口业务-校验被监管人员单向视频会见次数是否超过限制
     * @param jgrybm
     * @return
     */
    JSONObject limitNumber(String jgrybm);

    /**
     * 实战平台-窗口业务-通知家属
     * @param updateReqVO
     * @return
     */
    boolean notifyFamilyMembers(FamilyMeetingVideoSaveReqVO updateReqVO);

    /**
     * 实战平台-窗口业务-会见登记
     * @param updateReqVO
     * @return
     */
    boolean meetingRegister(FamilyMeetingVideoSaveReqVO updateReqVO);

    /**
     * 实战平台-窗口业务-根据ID获得单向视频家属会见
     * @param id
     * @return
     */
    FamilyMeetingVideoRespVO getFamilyMeetingVideoById(String id);

    /**
     * 根据监管人员编码获取监管人员历史单向视频会见记录
     * @param jgrybm
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageResult<FamilyMeetingVideoRespVO> getHistoryMeetingByJgrybm(String jgrybm,int pageNo,int pageSize);
}
