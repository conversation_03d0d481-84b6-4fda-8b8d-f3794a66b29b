package com.rs.module.acp.controller.admin.gj.vo.conflict;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-社会矛盾回访 Response VO")
@Data
public class ConflictFollowupRespVO extends BaseVO implements TransPojo{

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("事件ID")
    private String eventId;
    @ApiModelProperty("事件编号")
    private String eventCode;
    @ApiModelProperty("回访方式")
    private String followUpWay;
    @ApiModelProperty("收到锦旗数")
    private Integer receivedBannersCnt;
    @ApiModelProperty("收到牌匾数")
    private Integer receivedPlaquesCnt;
    @ApiModelProperty("收到感谢信数")
    private Integer receivedThankYouLettersCnt;
    @ApiModelProperty("履行纠纷款项金额")
    private BigDecimal disputePaymentAmount;
    @ApiModelProperty("停访息诉起数")
    private Integer ceaseVisitsAndEndLitigationCnt;
    @ApiModelProperty("回访时间")
    private Date followUpTime;
    @ApiModelProperty("回访情况")
    private String followUpInfo;
    @ApiModelProperty("回访经办人")
    private String followupOperatorSfzh;
    @ApiModelProperty("回访经办人姓名")
    private String followupOperatorXm;
    @ApiModelProperty("回访经办时间")
    private Date followupOperatorTime;
    @ApiModelProperty("附件地址")
    private String attUrl;
    @ApiModelProperty("备注")
    private String remark;
}
