package com.rs.module.acp.entity.db;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-羁押业务-收回登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_db_detain_reclaim")
@KeySequence("acp_db_detain_reclaim_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_db_detain_reclaim")
public class DetainReclaimDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 入所时间
     */
    private Date rssj;
    /**
     * 入所原因
     */
    private String rsyy;
    /**
     * 法律文书号
     */
    private String flwsh;
    /**
     * 收回原因
     */
    private String shyy;
    /**
     * 收回日期
     */
    private Date shrq;
    /**
     * 经办人身份证号
     */
    private String jbrsfzh;
    /**
     * 经办人
     */
    private String jbr;
    /**
     * 经办时间
     */
    private String jbsj;
    /**
     * 登记状态
     */
    private String status;

    /**
     * 监室号
     */
    private String jsh;

    /**
     * 监室名称
     *
     */
    private String roomName;

}
