package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.StringUtil;
import com.rs.module.acp.entity.wb.DefaultCasePersonneDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.CasePersonnelDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.CasePersonnelDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-办案人员 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CasePersonnelServiceImpl extends BaseServiceImpl<CasePersonnelDao, CasePersonnelDO> implements CasePersonnelService {

    @Resource
    private CasePersonnelDao casePersonnelDao;

    @Autowired
    private WbCommonService wbCommonService;

    @Autowired
    private DefaultCasePersonneService defaultCasePersonneService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createCasePersonnel(CasePersonnelSaveReqVO createReqVO) {
        createReqVO.setId(StringUtil.getGuid32());
        if(ObjectUtil.isNotEmpty(createReqVO.getGzzjUrl())){
            String gzzjUrl = wbCommonService.saveFile(null,createReqVO.getGzzjUrl());
            createReqVO.setGzzjUrl(gzzjUrl);
        }
        // 插入
        CasePersonnelDO casePersonnel = BeanUtils.toBean(createReqVO, CasePersonnelDO.class);
        casePersonnel.setTjjglx(createReqVO.getOrgType());
        casePersonnelDao.insert(casePersonnel);
        // 返回
        return casePersonnel.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCasePersonnel(CasePersonnelSaveReqVO updateReqVO) {
        // 校验存在
        CasePersonnelDO  casePersonnelDO = casePersonnelDao.selectById(updateReqVO.getId());
        if(ObjectUtil.isEmpty(casePersonnelDO)){
            throw new ServerException("实战平台-窗口业务-办案人员数据不存在");
        }

        if(ObjectUtil.isNotEmpty(updateReqVO.getGzzjUrl())){
            String gzzjUrl = wbCommonService.saveFile(ObjectUtil.isNotEmpty(casePersonnelDO.getGzzjUrl())?casePersonnelDO.getGzzjUrl():null
                    ,updateReqVO.getGzzjUrl());
            updateReqVO.setGzzjUrl(gzzjUrl);
        }

        // 更新
        CasePersonnelDO updateObj = BeanUtils.toBean(updateReqVO, CasePersonnelDO.class);
        updateObj.setTjjglx(updateReqVO.getOrgType());
        casePersonnelDao.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCasePersonnel(String id) {
        // 校验存在
        CasePersonnelDO casePersonnelDO = casePersonnelDao.selectById(id);
        if(ObjectUtil.isEmpty(casePersonnelDO)){
            throw new ServerException("实战平台-窗口业务-办案人员数据不存在");
        }
        if(ObjectUtil.isNotEmpty(casePersonnelDO.getGzzjUrl())){
            wbCommonService.deleteFile(casePersonnelDO.getGzzjUrl());
        }
        // 删除
        casePersonnelDao.deleteById(id);
    }

    private void validateCasePersonnelExists(String id) {
        if (casePersonnelDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-办案人员数据不存在");
        }
    }

    @Override
    public CasePersonnelDO getCasePersonnel(String id) {
        CasePersonnelDO casePersonnelDO = casePersonnelDao.selectById(id);
        if(ObjectUtil.isNotEmpty(casePersonnelDO.getGzzjUrl())){
            casePersonnelDO.setGzzjUrl(wbCommonService.getFile(casePersonnelDO.getGzzjUrl()));
        }
        return casePersonnelDO;
    }

    @Override
    public PageResult<CasePersonnelRespVO> getCasePersonnelPage(CasePersonnelPageReqVO pageReqVO) {
        PageResult<CasePersonnelDO> pageResult = casePersonnelDao.selectPage(pageReqVO);
        PageResult<CasePersonnelRespVO> resPage = BeanUtils.toBean(pageResult, CasePersonnelRespVO.class);
        for(CasePersonnelRespVO personnelRespVO:resPage.getList()){
            if(ObjectUtil.isNotEmpty(personnelRespVO.getGzzjUrl())){
                personnelRespVO.setGzzjUrl(wbCommonService.getFile(personnelRespVO.getGzzjUrl()));
            }
            for(CasePersonnelDO casePersonnelDO:pageResult.getList()){
                if(casePersonnelDO.getId().equals(personnelRespVO.getId())){
                    personnelRespVO.setOrgType(casePersonnelDO.getTjjglx());
                    break;
                }
            }
            personnelRespVO.setDisdicName(getTjjglxDic(personnelRespVO.getOrgType()));
        }

        return resPage;
    }

    @Override
    public List<CasePersonnelRespVO> getCasePersonnelList(CasePersonnelListReqVO listReqVO) {
        List<CasePersonnelDO> casePersonnelDOList = casePersonnelDao.selectList(listReqVO);
        List<CasePersonnelRespVO> casePersonnelRespVOList =  BeanUtils.toBean(casePersonnelDOList, CasePersonnelRespVO.class);
        for(CasePersonnelRespVO personnelRespVO:casePersonnelRespVOList){
            if(ObjectUtil.isNotEmpty(personnelRespVO.getGzzjUrl())){
                personnelRespVO.setGzzjUrl(wbCommonService.getFile(personnelRespVO.getGzzjUrl()));
            }
            for(CasePersonnelDO casePersonnelDO:casePersonnelDOList){
                if(casePersonnelDO.getId().equals(personnelRespVO.getId())){
                    personnelRespVO.setOrgType(casePersonnelDO.getTjjglx());
                    break;
                }
            }
        }

        return casePersonnelRespVOList;
    }

    @Override
    public List<CasePersonnelRespVO> getByIds(List<String> idList) {
        List<CasePersonnelDO> casePersonnelDOList = list(new LambdaQueryWrapper<CasePersonnelDO>()
                .select(CasePersonnelDO::getZjhm, CasePersonnelDO::getXm,CasePersonnelDO::getBadwdm,CasePersonnelDO::getBadwmc,
                        CasePersonnelDO::getZjlx,CasePersonnelDO::getLxfs,CasePersonnelDO::getZpUrl,
                        CasePersonnelDO::getGzzjUrl,CasePersonnelDO::getId,CasePersonnelDO::getTjjglx)
                .in(CasePersonnelDO::getId, idList));

        List<CasePersonnelRespVO> resList = BeanUtils.toBean(casePersonnelDOList,CasePersonnelRespVO.class);

        for(CasePersonnelRespVO personnelRespVO:resList){
            if(ObjectUtil.isNotEmpty(personnelRespVO.getGzzjUrl())){
                personnelRespVO.setGzzjUrl(wbCommonService.getFile(personnelRespVO.getGzzjUrl()));
            }
            for(CasePersonnelDO casePersonnelDO:casePersonnelDOList){
                if(casePersonnelDO.getId().equals(personnelRespVO.getId())){
                    personnelRespVO.setOrgType(casePersonnelDO.getTjjglx());
                    break;
                }
            }
            personnelRespVO.setDisdicName(getTjjglxDic(personnelRespVO.getOrgType()));
        }

        return resList;
    }

    @Override
    public CasePersonnelRespVO getCasePersonnelById(String id) {
        CasePersonnelDO casePersonnelDO = getById(id);
        CasePersonnelRespVO casePersonnelRespVO = BeanUtils.toBean(casePersonnelDO, CasePersonnelRespVO.class);
        casePersonnelRespVO.setOrgType(casePersonnelDO.getTjjglx());
        casePersonnelRespVO.setDisdicName(getTjjglxDic(casePersonnelDO.getTjjglx()));
        return casePersonnelRespVO;
    }

    private String getTjjglxDic(String tjjglx){
        String dicName = null;
        if("1".equals(tjjglx)){
            dicName = "ZD_BADW_GAJG";
        }else if("2".equals(tjjglx)){
            dicName ="ZD_BADW_JCY";
        }else if("3".equals(tjjglx)){
            dicName = "ZD_BADW_FY";
        }else if("4".equals(tjjglx)){
            dicName = "ZD_BADW_AQJG";
        }
        return dicName;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDefaultCasePersonnelList(List<DefaultCasePersonneSaveReqVO> createReqVO) {

        if(CollectionUtil.isEmpty(createReqVO)){
            throw new ServerException("办案人员不能为空！");
        }

        List<DefaultCasePersonneDO> personneDOList = BeanUtils.toBean(createReqVO,DefaultCasePersonneDO.class);

        for(DefaultCasePersonneDO personneDO:personneDOList){
            if(ObjectUtil.isNotEmpty(personneDO.getGzzjUrl())){
                personneDO.setGzzjUrl(wbCommonService.saveFile(null,personneDO.getGzzjUrl()));
            }
        }

        return defaultCasePersonneService.saveBatch(personneDOList);
    }

    @Override
    public List<CasePersonnelRespVO> getDefaultCasePersonnelList(String jgrybm) {
        LambdaQueryWrapper<DefaultCasePersonneDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DefaultCasePersonneDO::getJgrybm,jgrybm);
        List<DefaultCasePersonneDO> defaultCasePersonneDOList = defaultCasePersonneService.list(lambdaQueryWrapper);
        if(CollectionUtil.isEmpty(defaultCasePersonneDOList)){
            return  new ArrayList<>();
        }

        List<CasePersonnelRespVO> casePersonnelRespVOList = BeanUtils.toBean(defaultCasePersonneDOList,CasePersonnelRespVO.class);
        for(CasePersonnelRespVO personnelRespVO:casePersonnelRespVOList){
            if(ObjectUtil.isNotEmpty(personnelRespVO.getGzzjUrl())){
                personnelRespVO.setGzzjUrl(wbCommonService.getFile(personnelRespVO.getGzzjUrl()));
            }
        }

        return casePersonnelRespVOList;
    }
}
