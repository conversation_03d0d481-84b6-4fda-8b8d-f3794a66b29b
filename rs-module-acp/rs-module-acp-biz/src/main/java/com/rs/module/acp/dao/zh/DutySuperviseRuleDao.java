package com.rs.module.acp.dao.zh;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.zh.DutySuperviseRuleDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.zh.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 综合管理-值班管理-值班督导规则配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface DutySuperviseRuleDao extends IBaseDao<DutySuperviseRuleDO> {


    default PageResult<DutySuperviseRuleDO> selectPage(DutySuperviseRulePageReqVO reqVO) {
        Page<DutySuperviseRuleDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<DutySuperviseRuleDO> wrapper = new LambdaQueryWrapperX<DutySuperviseRuleDO>()
            .eqIfPresent(DutySuperviseRuleDO::getStaffDutyTemplateId, reqVO.getStaffDutyTemplateId())
            .eqIfPresent(DutySuperviseRuleDO::getStaffDutyPostId, reqVO.getStaffDutyPostId())
            .eqIfPresent(DutySuperviseRuleDO::getStaffDutyRoleId, reqVO.getStaffDutyRoleId())
            .eqIfPresent(DutySuperviseRuleDO::getSigninValidMode, reqVO.getSigninValidMode())
            .eqIfPresent(DutySuperviseRuleDO::getTimeoutThreshold, reqVO.getTimeoutThreshold())
            .eqIfPresent(DutySuperviseRuleDO::getTimeoutPushTarget, reqVO.getTimeoutPushTarget())
            .eqIfPresent(DutySuperviseRuleDO::getTodayTimeoutCount, reqVO.getTodayTimeoutCount())
            .eqIfPresent(DutySuperviseRuleDO::getTodayTimeoutCountPushTarget, reqVO.getTodayTimeoutCountPushTarget())
            .eqIfPresent(DutySuperviseRuleDO::getSigninInterval, reqVO.getSigninInterval())
            .eqIfPresent(DutySuperviseRuleDO::getSigninStartTime, reqVO.getSigninStartTime())
            .eqIfPresent(DutySuperviseRuleDO::getSigninEndTime, reqVO.getSigninEndTime())
            .eqIfPresent(DutySuperviseRuleDO::getLateSigninTime, reqVO.getLateSigninTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(DutySuperviseRuleDO::getAddTime);
        }
        Page<DutySuperviseRuleDO> dutySuperviseRulePage = selectPage(page, wrapper);
        return new PageResult<>(dutySuperviseRulePage.getRecords(), dutySuperviseRulePage.getTotal());
    }
    default List<DutySuperviseRuleDO> selectList(DutySuperviseRuleListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DutySuperviseRuleDO>()
            .eqIfPresent(DutySuperviseRuleDO::getStaffDutyTemplateId, reqVO.getStaffDutyTemplateId())
            .eqIfPresent(DutySuperviseRuleDO::getStaffDutyPostId, reqVO.getStaffDutyPostId())
            .eqIfPresent(DutySuperviseRuleDO::getStaffDutyRoleId, reqVO.getStaffDutyRoleId())
            .eqIfPresent(DutySuperviseRuleDO::getSigninValidMode, reqVO.getSigninValidMode())
            .eqIfPresent(DutySuperviseRuleDO::getTimeoutThreshold, reqVO.getTimeoutThreshold())
            .eqIfPresent(DutySuperviseRuleDO::getTimeoutPushTarget, reqVO.getTimeoutPushTarget())
            .eqIfPresent(DutySuperviseRuleDO::getTodayTimeoutCount, reqVO.getTodayTimeoutCount())
            .eqIfPresent(DutySuperviseRuleDO::getTodayTimeoutCountPushTarget, reqVO.getTodayTimeoutCountPushTarget())
            .eqIfPresent(DutySuperviseRuleDO::getSigninInterval, reqVO.getSigninInterval())
            .eqIfPresent(DutySuperviseRuleDO::getSigninStartTime, reqVO.getSigninStartTime())
            .eqIfPresent(DutySuperviseRuleDO::getSigninEndTime, reqVO.getSigninEndTime())
            .eqIfPresent(DutySuperviseRuleDO::getLateSigninTime, reqVO.getLateSigninTime())
        .orderByDesc(DutySuperviseRuleDO::getAddTime));    }


    }
