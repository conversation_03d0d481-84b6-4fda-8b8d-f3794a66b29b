package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalPageReqVO;
import com.rs.module.acp.entity.gj.BookingApprovalDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-预约审核管理 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BookingApprovalDao extends IBaseDao<BookingApprovalDO> {


    default PageResult<BookingApprovalDO> selectPage(BookingApprovalPageReqVO reqVO) {
        Page<BookingApprovalDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<BookingApprovalDO> wrapper = new LambdaQueryWrapperX<BookingApprovalDO>()
            .eqIfPresent(BookingApprovalDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(BookingApprovalDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(BookingApprovalDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(BookingApprovalDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(BookingApprovalDO::getBookingCategory, reqVO.getBookingCategory())
            .eqIfPresent(BookingApprovalDO::getServiceCategory, reqVO.getServiceCategory())
            .betweenIfPresent(BookingApprovalDO::getApplicationTime, reqVO.getApplicationTime())
            .eqIfPresent(BookingApprovalDO::getStatus, reqVO.getStatus())
            .eqIfPresent(BookingApprovalDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(BookingApprovalDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(BookingApprovalDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(BookingApprovalDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(BookingApprovalDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(BookingApprovalDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(BookingApprovalDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(BookingApprovalDO::getAddTime);
        }
        Page<BookingApprovalDO> bookingApprovalPage = selectPage(page, wrapper);
        return new PageResult<>(bookingApprovalPage.getRecords(), bookingApprovalPage.getTotal());
    }
    default List<BookingApprovalDO> selectList(BookingApprovalListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BookingApprovalDO>()
            .eqIfPresent(BookingApprovalDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(BookingApprovalDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(BookingApprovalDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(BookingApprovalDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(BookingApprovalDO::getBookingCategory, reqVO.getBookingCategory())
            .eqIfPresent(BookingApprovalDO::getServiceCategory, reqVO.getServiceCategory())
            .betweenIfPresent(BookingApprovalDO::getApplicationTime, reqVO.getApplicationTime())
            .eqIfPresent(BookingApprovalDO::getStatus, reqVO.getStatus())
            .eqIfPresent(BookingApprovalDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(BookingApprovalDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(BookingApprovalDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(BookingApprovalDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(BookingApprovalDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(BookingApprovalDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(BookingApprovalDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(BookingApprovalDO::getAddTime));    }


    }
