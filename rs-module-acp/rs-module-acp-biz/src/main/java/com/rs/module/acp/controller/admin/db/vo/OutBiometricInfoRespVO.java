package com.rs.module.acp.controller.admin.db.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-收押业务-出所生物特征信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class OutBiometricInfoRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("采集项目类型")
    private String cjxmlx;
    @ApiModelProperty("生物特征,存储采集系统返回JSON结构")
    private String swtz;
    @ApiModelProperty("生物特征附件")
    private String swtzfj;
    @ApiModelProperty("核验结果:0:待核验；1：核验通过；2：核验不通过;")
    private String hyjg;
    @ApiModelProperty("备注")
    private String bz;
}
