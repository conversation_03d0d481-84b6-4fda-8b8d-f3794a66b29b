package com.rs.module.acp.service.gj.punishment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.cons.CommonConstants;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApprovalTraceVO;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.*;
import com.rs.module.acp.dao.gj.PunishmentDao;
import com.rs.module.acp.dao.gj.PunishmentExtendDao;
import com.rs.module.acp.dao.gj.PunishmentRemoveDao;
import com.rs.module.acp.entity.gj.PunishmentDO;
import com.rs.module.acp.entity.gj.PunishmentExtendDO;
import com.rs.module.acp.entity.gj.PunishmentRemoveDO;
import com.rs.module.acp.enums.gj.PunishmentStatusEnum;
import com.rs.module.acp.util.EquipmentUseUtil;
import com.rs.module.acp.util.GjBusTraceUtil;
import com.rs.module.acp.util.PunishmentUtil;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.service.sys.BusTraceService;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.util.DicUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 实战平台-管教业务-处罚呈批 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class PunishmentServiceImpl extends BaseServiceImpl<PunishmentDao, PunishmentDO> implements PunishmentService {

    @Resource
    private PunishmentDao punishmentDao;

    @Resource
    private PunishmentExtendDao punishmentExtendDao;

    @Resource
    private PunishmentRemoveDao punishmentRemoveDao;

    private final String defKey = "guanjiaochengfaguanlikanshousuo";

    private final String defKeyExtend = "guanjiaochufachengpiyanchang";

    private final String defKeyRemove = "guanjiaochufachengpitiqian";

    private final String BUS_TYPE = "18";

    @Resource
    private PrisonerService prisonerService;

    @Resource
    private BusTraceService busTraceService;

    @Override
    @Transactional( rollbackFor = Exception.class)
    public String createPunishment(PunishmentSaveReqVO createReqVO) {
        // 插入
        PunishmentDO punishment = BeanUtils.toBean(createReqVO, PunishmentDO.class);
        punishment.setActualStartDate(createReqVO.getStartDate());
        punishment.setActualEndDate(createReqVO.getEndDate());
        punishment.setActualDuration(new BigDecimal( createReqVO.getDuration()));
        punishment.setIsAutoGenerate(0);
        punishment.setStatus(PunishmentStatusEnum.CPDSP.getCode());

        String busType = MsgBusTypeEnum.GJ_CFGL.getCode();
        String msgUrl = StrUtil.format("/#/discipline/sanctionMentManagement?curId={}&saveType=approve", punishment.getId());
        if(punishment.getSfyg() != null && 1 == punishment.getSfyg()){
            //严管监室 拘留所
            punishment.setReasonName(DicUtils.translate("ZD_GJYGYY", punishment.getReason()));
            busType = MsgBusTypeEnum.GJ_YGGL.getCode();
            msgUrl = StrUtil.format("/#/discipline/punishmentManagement?curId={}&saveType=approve", punishment.getId());
        } else {
            punishment.setReasonName(DicUtils.translate("ZD_GJCFYY", punishment.getReason()));
        }
        if(StrUtil.isNotBlank(punishment.getMeasures())){
            punishment.setMeasuresName(DicUtils.translate( "ZD_GJCFNR", punishment.getMeasures()));
        }
        punishmentDao.insert(punishment);


        MapBuilder<String, Object> variables = MapUtil.builder();
        variables.put("ywbh", punishment.getId()).put("busType", busType);
        JSONObject result = BspApprovalUtil.commonStartProcess(defKey, punishment.getId(), "【审批】处罚呈批", msgUrl, variables.build(), HttpUtils.getAppCode());
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            punishment.setActInstId(bpmTrail.getString("actInstId"));
            punishment.setTaskId(bpmTrail.getString("taskId"));
            punishment.setStatus(PunishmentStatusEnum.CPDSP.getCode());
            punishmentDao.updateById(punishment);
        } else {
            throw new ServerException("流程启动失败");
        }
        return punishment.getId();
    }

    @Override
    public PunishmentRespVO getPunishment(String id) {
        PunishmentDO punishmentDO = punishmentDao.selectById(id);
        PunishmentRespVO vo = BeanUtils.toBean(punishmentDO, PunishmentRespVO.class);
        if(vo.getEndDate() != null){
            vo.setRemainderDay(DateUtil.betweenDay( new Date(), vo.getEndDate(),true));
        }

        //延长信息
        List<PunishmentExtendDO> extendDOList = punishmentExtendDao
                .selectList(new LambdaQueryWrapper<PunishmentExtendDO>().eq(PunishmentExtendDO::getPunishmentId, vo.getId()).orderByDesc(PunishmentExtendDO::getUpdateTime));

        if(CollUtil.isNotEmpty(extendDOList )){
            vo.setExtendRespList( BeanUtils.toBean(extendDOList,PunishmentExtendRespVO.class ));
        }

        //拼接解除
        List<PunishmentRemoveDO> removeList = punishmentRemoveDao
                .selectList(new LambdaQueryWrapper<PunishmentRemoveDO>().eq(PunishmentRemoveDO::getPunishmentId, vo.getId()).orderByDesc(PunishmentRemoveDO::getUpdateTime));
        if(CollUtil.isNotEmpty(removeList)){
            for (PunishmentRemoveDO punishmentRemoveDO : removeList) {
                PunishmentRemoveRespVO removeRespVO =  BeanUtils.toBean(punishmentRemoveDO, PunishmentRemoveRespVO.class);
                removeRespVO.setAdvanceDay(vo.getRemainderDay());
                if(PunishmentStatusEnum.TQJCDSP.getCode().equals( punishmentDO.getStatus()) ){
                    removeRespVO.setAdvanceDay(vo.getRemainderDay());
                    vo.setRemoveRespVO(removeRespVO);
                    continue;
                }


                //提前解除
                if(punishmentRemoveDO.getApproverTime() != null){
                    if(PunishmentStatusEnum.YJC.getCode().equals(punishmentDO.getStatus()) && removeRespVO.getIsInAdvanceRemove() != null && removeRespVO.getIsInAdvanceRemove() ==  ( short) 1 ){
                        removeRespVO.setAdvanceDay(DateUtil.betweenDay(vo.getEndDate(),  punishmentRemoveDO.getUpdateTime(),true));
                    } else {
                        removeRespVO.setAdvanceDay(0L);
                    }
                    vo.setRemoveRespVO(removeRespVO);
                } else {
                    vo.setRemoveRegRespVO(removeRespVO);
                }

            }

        }

        return vo;
    }

    @Override
    public PageResult<PunishmentDO> getPunishmentPage(PunishmentPageReqVO pageReqVO) {
        return punishmentDao.selectPage(pageReqVO);
    }

    @Override
    public List<PunishmentDO> getPunishmentList(PunishmentListReqVO listReqVO) {
        return punishmentDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void leaderApprove(GjApproveReqVO approveReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        PunishmentDO entity =  punishmentDao.selectById(approveReqVO.getId());;
        if (entity == null) {
            throw new ServerException("监控信息不存在！");
        }

        PunishmentStatusEnum statusEnum = PunishmentStatusEnum.getByCode(entity.getStatus());
        if (!PunishmentStatusEnum.CPDSP.getCode().equals(statusEnum.getCode())) {
            throw new ServerException("该状态[" + statusEnum.getName() + "]不允许审批！");
        }

        String approvalResult = approveReqVO.getApprovalResult();
        BspApproceStatusEnum bspApproceStatusEnum;
        String status = PunishmentStatusEnum.DDJ.getCode();
        if (String.valueOf( BspApproceStatusEnum.PASSED_END.getCode()).equals(approvalResult)) {
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED_END;
        } else {
            bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
            status = PunishmentStatusEnum.CPBTG.getCode();
        }
        entity.setStatus(status);
        punishmentDao.updateById( entity);

        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(entity.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }

        entity.setApprovalComments(approveReqVO.getApprovalComments());
        entity.setApproverXm(sessionUser.getName());
        entity.setApproverSfzh(sessionUser.getIdCard());
        entity.setApprovalResult(approveReqVO.getApprovalResult());
        entity.setApproverTime(new Date());

        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", entity.getId());

        String busType = MsgBusTypeEnum.GJ_CFGL.getCode();
        if(entity.getSfyg() != null && 1 == entity.getSfyg()){
            //严管监室 拘留所
            busType = MsgBusTypeEnum.GJ_YGGL.getCode();
        }
        variables.put("busType", busType);
        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getIdCard());
        nowApproveUser.put("name", sessionUser.getName());
        boolean terminateTask = BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ||
                BspApproceStatusEnum.NOT_PASSED_END.getCode() == bspApproceStatusEnum.getCode();
        JSONObject result = BspApprovalUtil.approvalProcess(defKey, entity.getActInstId(), entity.getTaskId(), entity.getId(),
                bspApproceStatusEnum, entity.getApprovalComments(), null, null, terminateTask,
                variables, nowApproveUser, HttpUtils.getAppCode());
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            entity.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程审批失败");
        }

        if(bspApproceStatusEnum.getCode() == BspApproceStatusEnum.PASSED_END.getCode()){
            BusTypeEnum typeEnum =  entity.getSfyg() != null && entity.getSfyg() == 1 ?  BusTypeEnum.YEWU_YG : BusTypeEnum.YEWU_CF;
            busTraceService.saveBusTrace(typeEnum, GjBusTraceUtil.buildPunishmentBusTraceContent(entity),
                    entity.getJgrybm(),
                    SessionUserUtil.getSessionUser().getOrgCode(),
                    entity.getId());

            //发送禁止购物信息
            PunishmentUtil.sendNoShoppingAllowed(entity);
        }
        punishmentDao.updateById( entity);

        // 产品未提供
        //String apprName = "提前戒具使用审批";
        //sendMsg(apprName, passStatus, equipmentUse.getId(),equipmentUse.getJgrybm(), equipmentUse.getAddTime(), equipmentUse.getAddUserName(), statusName, equipmentUseExtendDO.getId());

    }

    @Override
    public void regInfo(PunishmentSaveRegInfoVO regInfoVO) {
        //待处罚登记
        PunishmentDO punishment =  punishmentDao.selectById(regInfoVO.getId());
        Assert.notNull(punishment, StrUtil.format( "记录不存在id:{}", regInfoVO.getId()));

        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        punishment.setExecutorSfzh( sessionUser.getIdCard());
        punishment.setExecutor(sessionUser.getName());
        punishment.setExecutorRegTime( new Date());
        punishment.setExecuteSituation(regInfoVO.getExecuteSituation());
        punishment.setStatus(PunishmentStatusEnum.CFZ.getCode());
        punishment.setActualStartDate(new Date());
        punishmentDao.updateById( punishment);

    }

    @Override
    @Transactional( rollbackFor = Exception.class)
    public String createExtend(PunishmentExtendSaveReqVO createReqVO) {
        // 插入
        PunishmentDO entity = punishmentDao.selectById( createReqVO.getId());
        Assert.notNull(entity, StrUtil.format("记录不存在！id:{}", entity.getId()));
        PunishmentExtendDO punishment = BeanUtils.toBean(createReqVO, PunishmentExtendDO.class);
        punishment.setPunishmentId( entity.getId());
        punishment.setId(null);
        punishment.setStatus(PunishmentStatusEnum.YCDSP.getCode());
        punishmentExtendDao.insert(punishment);
        //TODO 待前端-提供
        MapBuilder<String, Object> variables = MapUtil.builder();
        String busType = MsgBusTypeEnum.GJ_CFGL.getCode();
        String msgUrl = StrUtil.format("/#/discipline/sanctionMentManagement?curId={}&saveType=approve", punishment.getId());
        if(entity.getSfyg() != null && 1 == entity.getSfyg()){
            //严管监室 拘留所
            busType = MsgBusTypeEnum.GJ_YGGL.getCode();
            msgUrl = StrUtil.format("/#/discipline/punishmentManagement?curId={}&saveType=approve", punishment.getId());
        }
        variables.put("ywbh", punishment.getId()).put("busType", busType);

        JSONObject result = BspApprovalUtil.commonStartProcess(defKeyExtend, punishment.getId(), "【审批】处罚延长呈批", msgUrl, variables.build(), HttpUtils.getAppCode());
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            punishment.setActInstId(bpmTrail.getString("actInstId"));
            punishment.setTaskId(bpmTrail.getString("taskId"));
            punishment.setStatus(PunishmentStatusEnum.YCDSP.getCode());
            punishmentExtendDao.updateById(punishment);
            entity.setStatus(PunishmentStatusEnum.YCDSP.getCode() );
            punishmentDao.updateById( entity);
        } else {
            throw new ServerException("流程启动失败");
        }
        return entity.getId();
    }

    @Override
    public void approveExtend(GjApproveReqVO approveReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        PunishmentDO entity =  punishmentDao.selectById(approveReqVO.getId());;
        if (entity == null) {
            throw new ServerException("监控信息不存在！");
        }

        PunishmentStatusEnum statusEnum = PunishmentStatusEnum.getByCode(entity.getStatus());
        if (!PunishmentStatusEnum.YCDSP.getCode().equals(statusEnum.getCode())) {
            throw new ServerException("该状态[" + statusEnum.getName() + "]不允许审批！");
        }

        List<PunishmentExtendDO> punishmentExtendDOList = punishmentExtendDao.selectList(new LambdaQueryWrapper<PunishmentExtendDO>()
                .eq(PunishmentExtendDO::getStatus,PunishmentStatusEnum.YCDSP.getCode()).eq(PunishmentExtendDO::getPunishmentId, approveReqVO.getId()).orderByDesc(PunishmentExtendDO::getUpdateTime));
        if(CollUtil.isEmpty(punishmentExtendDOList)){
            throw new ServerException("未发起过延长记录");
        }

        PunishmentExtendDO extendDO = punishmentExtendDOList.get(0);
        String approvalResult = approveReqVO.getApprovalResult();
        BspApproceStatusEnum bspApproceStatusEnum;
        String status = PunishmentStatusEnum.CFZ.getCode();
        if (String.valueOf( BspApproceStatusEnum.PASSED_END.getCode()).equals(approvalResult)) {
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED_END;
        } else {
            bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
            //判断时间，是否是待解除
            status = PunishmentStatusEnum.CFZ.getCode();
        }

        //延长天数
        if(extendDO.getExtendDay() != null && BspApproceStatusEnum.PASSED_END.equals(bspApproceStatusEnum)){
            entity.setEndDate(DateUtil.offsetDay(entity.getEndDate(), extendDO.getExtendDay() ));
            entity.setDuration( entity.getDuration() + extendDO.getExtendDay() );
            entity.setActualEndDate(DateUtil.offsetDay(entity.getEndDate(), extendDO.getExtendDay() ));
            //禁止购物
            PunishmentUtil.sendNoShoppingAllowed(entity);
        }

        entity.setStatus(status);
        punishmentDao.updateById( entity);

        extendDO.setApprovalComments(approveReqVO.getApprovalComments());
        extendDO.setApproverXm(sessionUser.getName());
        extendDO.setApproverSfzh(sessionUser.getIdCard());
        extendDO.setApprovalResult(approveReqVO.getApprovalResult());
        extendDO.setApproverTime(new Date());
        extendDO.setStatus(status);
        punishmentExtendDao.updateById(extendDO);

        JSONObject result = BspApprovalUtil.approvalProcessAcp(defKeyExtend,
                extendDO.getActInstId(),
                extendDO.getTaskId(),
                extendDO.getId(),
                bspApproceStatusEnum,
                approveReqVO.getApprovalComments()  );
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            entity.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程审批失败");
        }
    }

    @Override
    public PageResult<PunishmentRespVO> getPunishmentHistoryByJgrybm(String jgrybm, Long pageNo, Long pageSize) {
        Page<PunishmentDO> page = new Page(pageNo, pageSize);
        Page<PunishmentDO> roomChangeDOList = punishmentDao.selectPage(page, new LambdaQueryWrapper<PunishmentDO>().eq(PunishmentDO::getJgrybm, jgrybm).
                in(PunishmentDO::getStatus,
                Arrays.asList(
                        PunishmentStatusEnum.YCDSP.getCode(),
                        PunishmentStatusEnum.CFZ.getCode(),
                        PunishmentStatusEnum.TQJCDSP.getCode(),
                        PunishmentStatusEnum.DJC.getCode(),
                        PunishmentStatusEnum.YJC.getCode()
                )).orderByDesc(PunishmentDO::getUpdateTime));
        PageResult<PunishmentDO> result =  new PageResult<PunishmentDO>(roomChangeDOList.getRecords(), roomChangeDOList.getTotal());
        PageResult<PunishmentRespVO> newResult = BeanUtils.toBean(result, PunishmentRespVO.class);
        newResult.getList().forEach(e->{
            if(StrUtil.isNotBlank(e.getReason())){
                // 处罚原因 看守所字典：ZD_GJCFYY   拘留所字典：ZD_GJYGYY
                if(e.getSfyg() != null && 1 == e.getSfyg()){
                    //严管监室 拘留所
                    e.setReasonName(DicUtils.translate("ZD_GJYGYY", e.getReason()));
                } else {
                    e.setReasonName(DicUtils.translate("ZD_GJCFYY", e.getReason()));
                }
            }
        });
        return newResult;
    }

    @Override
    public void createRemove(PunishmentRemoveSaveReqVO createReqVO) {
        // 插入
        PunishmentDO entity = punishmentDao.selectById( createReqVO.getId());

        Assert.notNull(entity, StrUtil.format("记录不存在！id:{}", entity.getId()));
        PunishmentRemoveDO punishment = BeanUtils.toBean(createReqVO, PunishmentRemoveDO.class);
        punishment.setId(null);
        punishment.setPunishmentId( entity.getId());
        punishment.setRemoveReason( createReqVO.getRemoveReason());
        punishment.setIsInAdvanceRemove((short) 1);
        punishment.setRemark(entity.getRemark());
        punishment.setSpecificRemoveReason(createReqVO.getSpecificRemoveReason());
        punishment.setStatus(PunishmentStatusEnum.TQJCDSP.getCode());
        punishmentRemoveDao.insert(punishment);

        String busType = MsgBusTypeEnum.GJ_CFGL.getCode();
        // 待前端-提供
        String msgUrl = StrUtil.format("/#/discipline/sanctionMentManagement?curId={}&saveType=approve", punishment.getId());
        if(entity.getSfyg() != null && entity.getSfyg() == 1){
            busType = MsgBusTypeEnum.GJ_YGGL.getCode();
            msgUrl = StrUtil.format("/#/discipline/punishmentManagement?curId={}&saveType=approve", punishment.getId());
        }

        //String msgUrl = entity.getSfyg() != null && entity.getSfyg() == 1 ? "严管" : "处罚";

        MapBuilder<String, Object> variables = MapUtil.builder();
        variables.put("ywbh", punishment.getId()).put("busType", busType);
        JSONObject result = BspApprovalUtil.commonStartProcess(defKeyRemove, punishment.getId(), "【审批】提前解除处罚呈批", msgUrl, variables.build(), HttpUtils.getAppCode());
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            punishment.setActInstId(bpmTrail.getString("actInstId"));
            punishment.setTaskId(bpmTrail.getString("taskId"));
            punishment.setStatus(PunishmentStatusEnum.TQJCDSP.getCode());

            punishmentRemoveDao.updateById(punishment);
            entity.setStatus(PunishmentStatusEnum.TQJCDSP.getCode() );
            entity.setIsInAdvanceRemove((short) 1);
            punishmentDao.updateById( entity);
        } else {
            throw new ServerException("流程启动失败");
        }
    }

    @Override
    public void apprRemove(GjApproveReqVO approveReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        PunishmentDO entity =  punishmentDao.selectById(approveReqVO.getId());
        Assert.notNull(entity, "记录不存在！");

        //待审批
        PunishmentStatusEnum statusEnum = PunishmentStatusEnum.getByCode(entity.getStatus());
        if (!PunishmentStatusEnum.TQJCDSP.getCode().equals(statusEnum.getCode())) {
            throw new ServerException("该状态[" + statusEnum.getName() + "]不允许审批！");
        }

        List<PunishmentRemoveDO> punishmentExtendDOList = punishmentRemoveDao.selectList(new LambdaQueryWrapper<PunishmentRemoveDO>()
                .eq(PunishmentRemoveDO::getStatus,PunishmentStatusEnum.TQJCDSP.getCode()).eq(PunishmentRemoveDO::getPunishmentId, approveReqVO.getId()).orderByDesc(PunishmentRemoveDO::getUpdateTime));
        if(CollUtil.isEmpty(punishmentExtendDOList)){
            throw new ServerException("未发起提前审批记录！");
        }

        PunishmentRemoveDO extendDO = punishmentExtendDOList.get(0);
        String approvalResult = approveReqVO.getApprovalResult();
        BspApproceStatusEnum bspApproceStatusEnum;
        String status = PunishmentStatusEnum.DJC.getCode();
        if (String.valueOf( BspApproceStatusEnum.PASSED_END.getCode()).equals(approvalResult)) {
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED_END;
        } else {
            bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
            //判断时间，是否是待解除
            status = PunishmentStatusEnum.CFZ.getCode();
        }
        entity.setStatus(status);
        punishmentDao.updateById( entity);

        extendDO.setApprovalComments(approveReqVO.getApprovalComments());
        extendDO.setApproverXm(sessionUser.getName());
        extendDO.setApproverSfzh(sessionUser.getIdCard());
        extendDO.setApprovalResult(approveReqVO.getApprovalResult());
        extendDO.setApproverTime(new Date());
        extendDO.setStatus(status);
        punishmentRemoveDao.updateById(extendDO);

        JSONObject result = BspApprovalUtil.approvalProcessAcp(defKeyExtend,
                extendDO.getActInstId(),
                extendDO.getTaskId(),
                extendDO.getId(),
                bspApproceStatusEnum,
                approveReqVO.getApprovalComments() );
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            entity.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程审批失败");
        }

    }

    @Override
    public List<GjApprovalTraceVO> getApproveTrack(String id) {
        List<GjApprovalTraceVO> result = new ArrayList<>();
        PunishmentDO entity =  punishmentDao.selectById(id);
        if(entity == null){
            return result;
        }
        String title = entity.getSfyg() != null && entity.getSfyg() == 1 ? "严管" : "处罚";
        //处罚呈批-start
        GjApprovalTraceVO approvalTraceVOcfsp = new GjApprovalTraceVO();
        approvalTraceVOcfsp.setNodeKey("cfsp");
        approvalTraceVOcfsp.setNodeName(StrUtil.format( "{}呈批", title));
        approvalTraceVOcfsp.setNodeStatus(1);
        approvalTraceVOcfsp.setNodeCreateTime(DateUtil.formatDateTime(entity.getAddTime()));
        List<GjApprovalTraceVO.TraceVO> nodeInfoList = new ArrayList<>();
        nodeInfoList.add(GjApprovalTraceVO.TraceVO.builder().key("呈批人").val(entity.getAddUserName()).build());
        nodeInfoList.add(GjApprovalTraceVO.TraceVO.builder().key("呈批时间").val( DateUtil.formatDateTime(entity.getAddTime())).build());
        if(entity.getSfyg() != null && 1 == entity.getSfyg()){
            //严管监室
            nodeInfoList.add(GjApprovalTraceVO.TraceVO.builder().key( title +"原因").val(DicUtils.translate("ZD_GJYGYY", entity.getReason())).build());
        } else {
            // 拘留所
            nodeInfoList.add(GjApprovalTraceVO.TraceVO.builder().key(title +"原因").val(DicUtils.translate("ZD_GJCFYY", entity.getReason())).build());
        }
        String penaltyPeriod = DateUtil.formatDate(entity.getStartDate()) + "至" + DateUtil.formatDate(entity.getEndDate());
        String timeStr = com.rs.util.DateUtil.getDateDifference(entity.getStartDate(),entity.getEndDate() );
        nodeInfoList.add(GjApprovalTraceVO.TraceVO.builder().key(title + "周期").val(StrUtil.format("{}({})", penaltyPeriod, timeStr)).build());
        String[] meassuresArray = entity.getMeasures().split(",");
        StringBuffer names = new StringBuffer();
        for (String s : meassuresArray) {
            if(names.length() > 0){
                names.append("，");
            }
            names.append( DicUtils.translate( "ZD_GJCFNR", s));
        }
        nodeInfoList.add(GjApprovalTraceVO.TraceVO.builder().key(title +"措施").val(names.toString()).build());
        approvalTraceVOcfsp.setNodeInfo(nodeInfoList);
        result.add(approvalTraceVOcfsp);
        //处罚呈批-end

        // 领导审批意见-Start
        if(StrUtil.isNotBlank(entity.getActInstId())){
            List<GjApprovalTraceVO> apprJjsyList = EquipmentUseUtil.converBspApprovalTrack(entity.getActInstId());
            if(CollUtil.isNotEmpty( apprJjsyList)){
                result.addAll(apprJjsyList);
            }
        }
        // 领导审批意见-end

        //使用登记 -start
        if(StrUtil.isNotBlank( entity.getExecutorSfzh())){
            GjApprovalTraceVO approvalTraceVOJjsyspdj = new GjApprovalTraceVO();
            approvalTraceVOJjsyspdj.setNodeKey("sydj");
            approvalTraceVOJjsyspdj.setNodeName(title + "登记");
            approvalTraceVOJjsyspdj.setNodeStatus(1);
            approvalTraceVOJjsyspdj.setNodeCreateTime(DateUtil.formatDateTime(entity.getExecutorRegTime()));
            List<GjApprovalTraceVO.TraceVO> nodeInfoListJjsyspdj  = new ArrayList<>();
            nodeInfoListJjsyspdj.add(GjApprovalTraceVO.TraceVO.builder().key("执行人").val(entity.getExecutor()).build());
            nodeInfoListJjsyspdj.add(GjApprovalTraceVO.TraceVO.builder().key("执行情况").val( entity.getExecuteSituation()).build());
            nodeInfoListJjsyspdj.add(GjApprovalTraceVO.TraceVO.builder().key("登记时间").val( DateUtil.formatDateTime(entity.getExecutorRegTime())).build());
            nodeInfoListJjsyspdj.add(GjApprovalTraceVO.TraceVO.builder().key("登记人").val(entity.getExecutor() ).build());
            approvalTraceVOJjsyspdj.setNodeInfo(nodeInfoListJjsyspdj );
            result.add( approvalTraceVOJjsyspdj);
        }
        //戒具使用登记 -end

        //延长审批 -Start
        List<PunishmentExtendDO> useExtendDOList = punishmentExtendDao.selectList(new LambdaQueryWrapper<PunishmentExtendDO>().eq(PunishmentExtendDO::getPunishmentId, entity.getId()));
        if(CollUtil.isNotEmpty(useExtendDOList)){
            useExtendDOList.forEach( e->{
                GjApprovalTraceVO approvalTraceVOTemp = new GjApprovalTraceVO();
                approvalTraceVOTemp.setNodeKey("ycjjsycp");
                approvalTraceVOTemp.setNodeName("延长"+title+"呈批");
                approvalTraceVOTemp.setNodeStatus(1);
                approvalTraceVOTemp.setNodeCreateTime(DateUtil.formatDateTime(e.getAddTime()));
                List<GjApprovalTraceVO.TraceVO> nodeInfoLisTemp  = new ArrayList<>();
                nodeInfoLisTemp.add(GjApprovalTraceVO.TraceVO.builder().key("呈批人").val(e.getAddUserName()).build());
                nodeInfoLisTemp.add(GjApprovalTraceVO.TraceVO.builder().key("呈批时间").val( DateUtil.formatDateTime(e.getAddTime())).build());
                nodeInfoLisTemp.add(GjApprovalTraceVO.TraceVO.builder().key("延长" + title + "天数").val(e.getExtendDay() + "天").build());
                nodeInfoLisTemp.add(GjApprovalTraceVO.TraceVO.builder().key("延长理由").val(e.getReason()).build());
                approvalTraceVOTemp.setNodeInfo(nodeInfoLisTemp);
                result.add( approvalTraceVOTemp);
                List<GjApprovalTraceVO> apprYLcList = EquipmentUseUtil.converBspApprovalTrack(e.getActInstId());
                if(CollUtil.isNotEmpty( apprYLcList)){
                    result.addAll(apprYLcList);
                }
            });

        }
        //延长戒具审批 -end

        //解除 戒具审批 -Start
        List<PunishmentRemoveDO> removeDOList = punishmentRemoveDao.selectList(new LambdaQueryWrapper<PunishmentRemoveDO>().eq(PunishmentRemoveDO::getPunishmentId, entity.getId()).orderByAsc(PunishmentRemoveDO::getAddTime));
        if(CollUtil.isNotEmpty(removeDOList)){
            removeDOList.forEach( e->{
                GjApprovalTraceVO approvalTraceVOTemp = new GjApprovalTraceVO();
                approvalTraceVOTemp.setNodeKey("tqjcjjsysp");
                approvalTraceVOTemp.setNodeName("提前解除" + title + "呈批");
                approvalTraceVOTemp.setNodeStatus(1);
                approvalTraceVOTemp.setNodeCreateTime(DateUtil.formatDateTime(entity.getAddTime()));
                List<GjApprovalTraceVO.TraceVO> nodeInfoLisTemp  = new ArrayList<>();
                nodeInfoLisTemp.add(GjApprovalTraceVO.TraceVO.builder().key("呈批人").val(e.getAddUserName()).build());
                nodeInfoLisTemp.add(GjApprovalTraceVO.TraceVO.builder().key("呈批时间").val( DateUtil.formatDateTime(e.getAddTime())).build());
                //严管和惩罚不一致 用的字段不一致  ZD_YGGLTQJCYY
                if(NumberUtil.isNumber(e.getRemoveReason()) ){
                    nodeInfoLisTemp.add(GjApprovalTraceVO.TraceVO.builder().key("提前解除理由").val( DicUtils.translate( "ZD_YGGLTQJCYY",e.getRemoveReason())).build());
                } else {
                    nodeInfoLisTemp.add(GjApprovalTraceVO.TraceVO.builder().key("提前解除理由").val(  e.getRemoveReason()).build());
                }
                approvalTraceVOTemp.setNodeInfo(nodeInfoLisTemp );
                result.add( approvalTraceVOTemp);

                if(StrUtil.isNotBlank(e.getActInstId() )){
                    List<GjApprovalTraceVO> apprYLcList = EquipmentUseUtil.converBspApprovalTrack(e.getActInstId());
                    if(CollUtil.isNotEmpty( apprYLcList)){
                        result.addAll(apprYLcList);
                    }
                } else {
                    //解除登记  -Start
                    GjApprovalTraceVO approvalTraceVOJjsyspdj = new GjApprovalTraceVO();
                    approvalTraceVOJjsyspdj.setNodeKey("jcjjsydj");
                    approvalTraceVOJjsyspdj.setNodeName("解除" + title+"使用登记");
                    approvalTraceVOJjsyspdj.setNodeStatus(1);
                    approvalTraceVOJjsyspdj.setNodeCreateTime(DateUtil.formatDateTime(entity.getUpdateTime()));
                    List<GjApprovalTraceVO.TraceVO> nodeInfoListJjsyspdj  = new ArrayList<>();
                    nodeInfoListJjsyspdj.add(GjApprovalTraceVO.TraceVO.builder().key("登记人").val(entity.getUpdateUserName()).build());
                    nodeInfoListJjsyspdj.add(GjApprovalTraceVO.TraceVO.builder().key("登记时间").val( DateUtil.formatDateTime(entity.getUpdateTime())).build());
                    nodeInfoListJjsyspdj.add(GjApprovalTraceVO.TraceVO.builder().key("执行情况").val( e.getSpecificRemoveReason()).build());
                    approvalTraceVOJjsyspdj.setNodeInfo(nodeInfoListJjsyspdj );
                    result.add( approvalTraceVOJjsyspdj);
                    //解除登记  -end
                }
            });
        }
        //解除  -end
        //Collections.sort(result, Comparator.comparing(
        //        GjApprovalTraceVO::getNodeCreateTime,
        //        (dateStr1, dateStr2) -> {
        //            try {
        //                return DateUtil.parse(dateStr2).compareTo(DateUtil.parse(dateStr1));
        //            } catch (Exception e) {
        //                throw new IllegalArgumentException("日期格式错误: " + e.getMessage(), e);
        //            }
        //        }
        //));


        return result;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeRegInfo(PunishmentSaveRemoveReqVO createReqVO) {
        PunishmentDO entity =  punishmentDao.selectById(createReqVO.getId());
        Assert.notNull(entity, "记录不存在！");
        PunishmentRemoveDO punishment = BeanUtils.toBean(createReqVO, PunishmentRemoveDO.class);
        punishment.setPunishmentId( entity.getId());
        punishment.setStatus(PunishmentStatusEnum.YJC.getCode());

        //判断是否到期new
        if(entity.getEndDate().getTime() > new Date().getTime()){
            punishment.setIsInAdvanceRemove((short) 0);
        } else {
            punishment.setIsInAdvanceRemove((short) 0);
        }
        entity.setActualEndDate(new Date());
        entity.setStatus(PunishmentStatusEnum.YJC.getCode());
       //List<PunishmentRemoveDO> punishmentRemoveDOList  =  punishmentRemoveDao.selectList(new LambdaQueryWrapper<PunishmentRemoveDO>()
       //         .eq(PunishmentRemoveDO::getPunishmentId, punishment.getPunishmentId())
       //         .eq(PunishmentRemoveDO::getStatus,PunishmentStatusEnum.DJC.getCode()));
       //if(CollUtil.isNotEmpty(punishmentRemoveDOList)){
       //    PunishmentRemoveDO punishmentRemoveDONew = punishmentRemoveDOList.get(0);
       //    punishmentRemoveDONew.setIsInAdvanceRemove(punishment.getIsInAdvanceRemove());
       //    punishmentRemoveDONew.setStatus(punishment.getStatus());
       //} else {
       //    punishmentRemoveDao.insert(punishment);
       //}

        //禁止购物
        PunishmentUtil.sendNoShoppingAllowed(entity);

        punishmentRemoveDao.insert(punishment);
        punishmentDao.updateById( entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveExternal(PunishmentSaveExternalInfoVO saveInfo) {
        PunishmentDO punishment = BeanUtils.toBean(saveInfo, PunishmentDO.class);
        punishment.setIsAutoGenerate(1);
        if(saveInfo.getStartDate() != null && saveInfo.getEndDate() != null){
            long hoursFromNow = DateUtil.between(saveInfo.getStartDate(), saveInfo.getEndDate(), DateUnit.DAY);
            punishment.setDuration(new Long(hoursFromNow).intValue());
            punishment.setActualDuration(new BigDecimal(hoursFromNow));
        }
        punishment.setStatus(PunishmentStatusEnum.CFZ.getCode());
        punishment.setActualEndDate(saveInfo.getEndDate());
        punishment.setActualStartDate(saveInfo.getStartDate());
        punishmentDao.insert(punishment);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExternalStatus(String id, PunishmentStatusEnum punEnum,Date endTime) {
        PunishmentDO punishmentDO = punishmentDao.selectById(id);
        if(punishmentDO == null){
            return false;
        }
        punishmentDO.setStatus( punEnum.getCode());
        if(endTime != null){
            punishmentDO.setEndDate( endTime);
        }
        return  punishmentDao.updateById(punishmentDO) > 0;
    }

}
