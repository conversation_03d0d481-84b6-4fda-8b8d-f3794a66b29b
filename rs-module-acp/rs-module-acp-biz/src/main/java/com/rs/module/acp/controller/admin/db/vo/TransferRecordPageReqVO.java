package com.rs.module.acp.controller.admin.db.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-羁押业务-转所登记分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TransferRecordPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("转所日期")
    private Date zsrq;

    @ApiModelProperty("转所人数")
    private Integer zsrs;

    @ApiModelProperty("'转所类型")
    private String zslx;

    @ApiModelProperty("转往单位")
    private String zwdw;

    @ApiModelProperty("押解方案")
    private String yjfa;

    @ApiModelProperty("押解民警身份证号")
    private String yjmjsfzh;

    @ApiModelProperty("押解民警")
    private String yjmj;

    @ApiModelProperty("经办人身份证号")
    private String jbrsfzh;

    @ApiModelProperty("经办人")
    private String jbr;

    @ApiModelProperty("经办时间")
    private Date jbsj;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
