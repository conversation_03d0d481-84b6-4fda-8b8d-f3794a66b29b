package com.rs.module.acp.entity.db;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-羁押业务-转所登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_db_transfer_record")
@KeySequence("acp_db_transfer_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_db_transfer_record")
public class TransferRecordDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 转所日期
     */
    private Date zsrq;
    /**
     * 转所人数
     */
    private Integer zsrs;
    /**
     * '转所类型
     */
    private String zslx;
    /**
     * 转往单位
     */
    private String zwdw;
    /**
     * 押解方案
     */
    private String yjfa;
    /**
     * 押解民警身份证号
     */
    private String yjmjsfzh;
    /**
     * 押解民警
     */
    private String yjmj;
    /**
     * 经办人身份证号
     */
    private String jbrsfzh;
    /**
     * 经办人
     */
    private String jbr;
    /**
     * 经办时间
     */
    private Date jbsj;

}
