package com.rs.module.acp.entity.db;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-收押业务-入所登记（拘留所） DO
 *
 * <AUTHOR>
 */
@TableName("acp_db_in_record_jls")
@KeySequence("acp_db_in_record_jls_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_db_in_record_jls")
public class InRecordJlsDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 姓名
     */
    private String xm;
    /**
     * 姓名拼音
     */
    private String xmpy;
    /**
     * 别名
     */
    private String bm;
    /**
     * 性别
     */
    private String xb;
    /**
     * 出生日期
     */
    private Date csrq;
    /**
     * 证件类型
     */
    private String zjlx;
    /**
     * 证件号码
     */
    private String zjhm;
    /**
     * 国籍
     */
    private String gj;
    /**
     * 民族
     */
    private String mz;
    /**
     * 婚姻状况
     */
    private String hyzk;
    /**
     * 籍贯
     */
    private String jg;
    /**
     * 宗教信仰
     */
    private String zjxy;
    /**
     * 户籍地
     */
    private String hjd;
    /**
     * 户籍地详址
     */
    private String hjdxz;
    /**
     * 现住址
     */
    private String xzz;
    /**
     * 现住址详址
     */
    private String xzzxz;
    /**
     * 文化程度
     */
    private String whcd;
    /**
     * 政治面貌
     */
    private String zzmm;
    /**
     * 职业
     */
    private String zy;
    /**
     * 工作单位
     */
    private String gzdw;
    /**
     * 身份
     */
    private String sf;
    /**
     * 特殊身份
     */
    private String tssf;
    /**
     * 信息核查
     */
    private String xxhc;
    /**
     * 是否为在校学生
     */
    private String sfwxzxs;
    /**
     * 学校名称
     */
    private String xxmc;
    /**
     * 案由代码
     */
    private String ajlbdm;
    /**
     * 案件类别名称
     */
    private String ajlb;
    /**
     * 案件编号
     */
    private String ajbh;
    /**
     * 人员编号
     */
    private String rybh;
    /**
     * 案事件相关人员编号
     */
    private String asjxgrybh;
    /**
     * 办案中心编号
     */
    private String bazxbh;
    /**
     * 送拘日期
     */
    private Date sjrq;
    /**
     * 送拘机关类型
     */
    private String sjjglx;
    /**
     * 送拘机关名称
     */
    private String sjjgmc;
    /**
     * 送拘人姓名1
     */
    private String sjrxm;
    /**
     * 送拘人联系电话1
     */
    private String sjrlxdh;
    /**
     * 送拘人姓名2
     */
    private String sjrxm2;
    /**
     * 送拘人联系电话2
     */
    private String sjrlxdh2;
    /**
     * 送拘民警姓名
     */
    private String sjmjxm;
    /**
     * 送拘民警联系电话
     */
    private String sjmjxmlxdh;
    /**
     * 办案单位类型
     */
    private String badwlx;
    /**
     * 办案单位
     */
    private String badw;
    /**
     * 拘留决定机关类型
     */
    private String jljdjglx;
    /**
     * 拘留决定机关名称
     */
    private String jljdjgmc;
    /**
     * 收拘凭证
     */
    private String sjpz;
    /**
     * 收拘凭证文书号
     */
    private String sjpzwsh;
    /**
     * 收拘凭证文书号
     */
    private String sjpzwsdz;
    /**
     * 档案编号
     */
    private String dabh;
    /**
     * 回执法律文书号
     */
    private String hzflwsh;
    /**
     * 入所原因
     */
    private String rsyy;
    /**
     * 入所日期
     */
    private Date rsrq;
    /**
     * 收拘民警
     */
    private String sjmj;
    /**
     * 拘留期限
     */
    private Integer jlqx;
    /**
     * 拘留起始日期
     */
    private Date jlqsrq;
    /**
     * 拘留截止日期
     */
    private Date jljzrq;
    /**
     * 管理类别
     */
    private String gllb;
    /**
     * 监区id
     */
    private String areaId;
    /**
     * 监区名称
     */
    private String areaName;
    /**
     * 监室号
     */
    private String jsh;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 简要案情
     */
    private String jyaq;
    /**
     * 同案人，多个逗号分割
     */
    private String tar;
    /**
     * 备注
     */
    private String bz;
    /**
     * 涉毒尿检初查结果
     */
    private String sdnjccjg;
    /**
     * 涉毒尿检单位
     */
    private String sdnjdw;
    /**
     * 涉毒尿检初查结果
     */
    private Date sdnjccsj;
    /**
     * 涉毒尿检检查人
     */
    private String sdnjjcr;
    /**
     * 手环ID
     */
    private String shid;
    /**
     * 手环绑定状态
     */
    private String shbdzt;
    /**
     * 手环绑定时间
     */
    private Date sdbdsj;
    /**
     * 是否涉密人员
     */
    private Short sfsm;
    /**
     * 人员代号
     */
    private String rydh;
    /**
     * 涉密原因
     */
    private String smyy;
    /**
     * 涉密备注
     */
    private String smbz;
    /**
     * 救济日期
     */
    private Date jjrq;
    /**
     * 救济原因
     */
    private String jjyy;
    /**
     * 救济领取物品
     */
    private String jjlqwp;
    /**
     * 入所类型
     */
    private String rslx;
    /**
     * 经办人
     */
    private String jbr;
    /**
     * 经办时间
     */
    private Date jbsj;
    /**
     * 办理状态
     */
    private String status;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 审批状态
     */
    private String spzt;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 当前步骤
     */
    private String currentStep;

}
