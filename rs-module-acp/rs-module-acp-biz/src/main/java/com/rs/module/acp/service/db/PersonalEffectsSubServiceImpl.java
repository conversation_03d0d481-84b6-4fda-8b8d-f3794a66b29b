package com.rs.module.acp.service.db;

import com.rs.module.acp.cons.DetainRegStatusEnum;
import com.rs.module.acp.cons.InProcessStageEnum;
import com.rs.module.acp.cons.LeaderApprovalStatusEnum;
import com.rs.module.acp.dao.db.DetainRegKssDao;
import com.rs.module.acp.entity.db.BiometricInfoDO;
import com.rs.module.acp.entity.db.DetainRegKssDO;
import com.rs.module.acp.entity.db.PersonalEffectsDO;
import com.rs.module.acp.service.db.components.RegistrationInfoService;
import com.rs.module.acp.service.db.components.RegistrationInfoServiceContext;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.PersonalEffectsSubDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.db.PersonalEffectsSubDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-收押业务-随身物品登记子 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PersonalEffectsSubServiceImpl extends BaseServiceImpl<PersonalEffectsSubDao, PersonalEffectsSubDO> implements PersonalEffectsSubService {

    @Resource
    private PersonalEffectsSubDao personalEffectsSubDao;

    @Resource
    private PersonalEffectsService  personalEffectsService;

    @Resource
    private BiometricInfoService biometricInfoService;

    @Resource
    private RegistrationInfoServiceContext registrationInfoServiceContext;

    @Override
    public String createPersonalEffectsSub(PersonalEffectsSubSaveReqVO createReqVO) {
        // 插入
        PersonalEffectsSubDO personalEffectsSub = BeanUtils.toBean(createReqVO, PersonalEffectsSubDO.class);
        personalEffectsSub.setId(UUID.randomUUID().toString().replace("-", ""));
        personalEffectsSub.setPersonalEffectsId(UUID.randomUUID().toString().replace("-", ""));

        personalEffectsSubDao.insert(personalEffectsSub);
        // 返回
        return personalEffectsSub.getId();
    }

    @Override
    public void updatePersonalEffectsSub(PersonalEffectsSubSaveReqVO updateReqVO) {
        // 校验存在
        validatePersonalEffectsSubExists(updateReqVO.getId());
        // 更新
        PersonalEffectsSubDO updateObj = BeanUtils.toBean(updateReqVO, PersonalEffectsSubDO.class);
        personalEffectsSubDao.updateById(updateObj);
    }

    @Override
    public void deletePersonalEffectsSub(String id) {
        // 校验存在
        validatePersonalEffectsSubExists(id);
        // 删除
        personalEffectsSubDao.deleteById(id);
    }

    private void validatePersonalEffectsSubExists(String id) {
        if (personalEffectsSubDao.selectById(id) == null) {
            throw new ServerException("实战平台-收押业务-随身物品登记子数据不存在");
        }
    }

    @Override
    public PersonalEffectsSubDO getPersonalEffectsSub(String id) {
        return personalEffectsSubDao.selectById(id);
    }

    @Override
    public PageResult<PersonalEffectsSubDO> getPersonalEffectsSubPage(PersonalEffectsSubPageReqVO pageReqVO) {
        return personalEffectsSubDao.selectPage(pageReqVO);
    }

    @Override
    public List<PersonalEffectsSubDO> getPersonalEffectsSubList(PersonalEffectsSubListReqVO listReqVO) {
        return personalEffectsSubDao.selectList(listReqVO);
    }

    /**
     * 批量创建个人物品子
     *
     * 此方法主要用于批量处理个人物品的存储，
     * 并根据列表中的数据更新或创建个人物品记录同时，它还会确保主表中存在相关记录，
     * 如果不存在，则创建新记录
     *
     * @param createReqVOList 个人物品请求VO列表，包含需要创建或更新的个人物品信息
     * @return 返回一个字符串列表，包含所有处理的个人物品记录的ID
     */
    @Override
    public boolean createPersonalEffectsSubBatch(List<PersonalEffectsSubSaveReqVO> createReqVOList) {
        //是否发起审批，为true时表示下一步进入领导审批流程
        boolean isApproval = false;
        if(createReqVOList==null|| createReqVOList.isEmpty()){
            return isApproval;
        }
        // 标记，是否已插入主表
        boolean isInsert = false;
        // 初始化ID列表，用于存储处理后的个人物品记录ID
        List<String> ids = new ArrayList<>();
        String id = "";
        //先删除物品列表，再插入
        String rybh = createReqVOList.get(0).getRybh();
        //入所类型
        String rslx = createReqVOList.get(0).getRslx();

        String bussinessType = createReqVOList.get(0).getBusinessType();
        if(rslx==null|| rslx.isEmpty()){
            RegistrationInfoService registrationInfoService = registrationInfoServiceContext.getService(bussinessType);
            rslx = registrationInfoService.getRslxInfo(rybh);
        }
        List<PersonalEffectsSubDO> list = getPersonalEffectsSubByRybh(rybh);
        if(list!=null&& !list.isEmpty()){
            List<String> idList = list.stream()
                    .map(PersonalEffectsSubDO::getId)
                    .collect(Collectors.toList());
            personalEffectsSubDao.deleteBatchIds(idList);
        }

        // 遍历个人物品子保存请求VO列表
        for (PersonalEffectsSubSaveReqVO createReqVO : createReqVOList){
            //04为无需物品登记，则子表不插入数据,其他状态的插入数据
            //当前实现无需登记的做法是前端传一条04状态的登记数据
            if(!Objects.equals(createReqVO.getStatus(), DetainRegStatusEnum.NOT_NEED_REGISTER.getCode())) {
                createReqVO.setId(UUID.randomUUID().toString().replace("-", ""));
                id = createPersonalEffectsSub(createReqVO);

                // 将处理后的个人物品记录ID添加到ID列表中
                ids.add(createReqVO.getId());
            }
            // 如果尚未插入主表，则执行插入操作 isInsert用于标志主表是否已插入数据
            if (!isInsert){
                // 根据人员编号查询个人物品列表
                PersonalEffectsListReqVO listReqVO = new PersonalEffectsListReqVO();
                listReqVO.setRybh(createReqVO.getRybh());
                List<PersonalEffectsDO> PersonalEffectsDOlist = personalEffectsService.getPersonalEffectsList(listReqVO);

                // 如果查询到个人物品记录，则更新记录，否则创建新记录
                if(PersonalEffectsDOlist !=null && !PersonalEffectsDOlist.isEmpty()){
                    PersonalEffectsDO personalEffectsDO = PersonalEffectsDOlist.get(0);
                    PersonalEffectsSaveReqVO effectsDO = new PersonalEffectsSaveReqVO();
                    effectsDO.setId(personalEffectsDO.getId());
                    effectsDO.setStatus(createReqVO.getStatus());
                    effectsDO.setRybh(personalEffectsDO.getRybh());
                    effectsDO.setRyxm(createReqVO.getRyxm());

                    personalEffectsService.updatePersonalEffects(effectsDO);
                }else{
                    PersonalEffectsSaveReqVO effectsSaveReqVO = new PersonalEffectsSaveReqVO();
                    // 使用Spring的Bean工具类复制属性，避免手动赋值
                    effectsSaveReqVO = BeanUtils.toBean(createReqVO, PersonalEffectsSaveReqVO.class);
                    // 生成新的UUID作为记录ID
                    effectsSaveReqVO.setId(UUID.randomUUID().toString().replace("-", ""));
                    effectsSaveReqVO.setStatus(createReqVO.getStatus());
                    personalEffectsService.createPersonalEffects(effectsSaveReqVO);
                }
                // 设置插入标记为true，表示已插入主表
                isInsert =  true;
            }
        }

        //判断是否流转到所领导审批
        if (createReqVOList.get(0).getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode()) 
                || createReqVOList.get(0).getStatus().equalsIgnoreCase(DetainRegStatusEnum.NOT_NEED_REGISTER.getCode())) {
            
            // 查询生物信息采集状态
            List<BiometricInfoDO> biometricInfoDOList = biometricInfoService.getBiometricInfoByRybh(rybh);
            String status = "";
            if (biometricInfoDOList != null && !biometricInfoDOList.isEmpty()) {
                status = biometricInfoDOList.get(0).getStatus();
            }

            // 根据生物信息采集状态更新流程阶段和审批状态
            if (status.equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())) {
                if(rslx!=null&&rslx.equalsIgnoreCase("02")){
                    RegistrationInfoService registrationInfoService = registrationInfoServiceContext.getService(bussinessType);
                    registrationInfoService.updateStateInfo(
                            rybh,
                            null,
                            InProcessStageEnum.COMPLETED.getCode()
                    );
                    isApproval = true;
                }else {
                    // 生物信息已提交，进入领导审批阶段
                    RegistrationInfoService registrationInfoService = registrationInfoServiceContext.getService(bussinessType);
                    registrationInfoService.updateStateInfo(
                            rybh,
                            LeaderApprovalStatusEnum.PENDING.getCode(),
                            InProcessStageEnum.PENDING_LEADER_APPROVAL.getCode()
                    );
                    isApproval = true;
                }
            } else if (status.equals("") || status.equalsIgnoreCase(DetainRegStatusEnum.DRAFT.getCode())) {
                // 生物信息未提交或为草稿状态，返回到生物信息采集阶段
                RegistrationInfoService registrationInfoService = registrationInfoServiceContext.getService(bussinessType);
                registrationInfoService.updateStateInfo(
                    rybh,
                    null,
                    InProcessStageEnum.PENDING_BIO_INFO_COLLECTION.getCode()
                );
            }
        }
        // 返回
        return isApproval;
    }

    @Override
    public List<PersonalEffectsSubDO> getPersonalEffectsSubByRybh(String rybh) {
        PersonalEffectsSubListReqVO listReqVO = new PersonalEffectsSubListReqVO();
        listReqVO.setRybh(rybh);
        List<PersonalEffectsSubDO> personalEffectsSubDOList = personalEffectsSubDao.selectList(listReqVO);
        return personalEffectsSubDOList;
    }


}
