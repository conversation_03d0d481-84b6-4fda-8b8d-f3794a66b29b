package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.printdocument.PrintDocumentListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.printdocument.PrintDocumentPageReqVO;
import com.rs.module.acp.entity.gj.PrintDocumentDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-文书打印预览 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PrintDocumentDao extends IBaseDao<PrintDocumentDO> {


    default PageResult<PrintDocumentDO> selectPage(PrintDocumentPageReqVO reqVO) {
        Page<PrintDocumentDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrintDocumentDO> wrapper = new LambdaQueryWrapperX<PrintDocumentDO>()
            .eqIfPresent(PrintDocumentDO::getGlId, reqVO.getGlId())
            .eqIfPresent(PrintDocumentDO::getGlType, reqVO.getGlType())
            .eqIfPresent(PrintDocumentDO::getWsdata, reqVO.getWsdata())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PrintDocumentDO::getAddTime);
        }
        Page<PrintDocumentDO> printDocumentPage = selectPage(page, wrapper);
        return new PageResult<>(printDocumentPage.getRecords(), printDocumentPage.getTotal());
    }
    default List<PrintDocumentDO> selectList(PrintDocumentListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrintDocumentDO>()
            .eqIfPresent(PrintDocumentDO::getGlId, reqVO.getGlId())
            .eqIfPresent(PrintDocumentDO::getGlType, reqVO.getGlType())
            .eqIfPresent(PrintDocumentDO::getWsdata, reqVO.getWsdata())
        .orderByDesc(PrintDocumentDO::getAddTime));    }


    }
