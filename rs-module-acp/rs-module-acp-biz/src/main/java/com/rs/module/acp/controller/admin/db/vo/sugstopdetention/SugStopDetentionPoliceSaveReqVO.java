package com.rs.module.acp.controller.admin.db.vo.sugstopdetention;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-建议停拘登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SugStopDetentionPoliceSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty(value = "保存类型 0 保存,1 提交",hidden = true)
    private String saveType;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("姓名")
    @NotEmpty(message = "姓名不能为空")
    private String xm;

    @ApiModelProperty("曾用名")
    private String cym;

    @ApiModelProperty("证件类型")
    @NotEmpty(message = "证件类型不能为空")
    private String zjlx;

    @ApiModelProperty("证件号码")
    @NotEmpty(message = "证件号码不能为空")
    private String zjhm;

    @ApiModelProperty("性别")
    @NotEmpty(message = "性别不能为空")
    private String xb;

    @ApiModelProperty("出生日期")
    @NotNull(message = "出生日期不能为空")
    private Date csrq;

    @ApiModelProperty("国籍")
    @NotEmpty(message = "国籍不能为空")
    private String gj;

    @ApiModelProperty("特殊身份")
    private String tssf;

    @ApiModelProperty("民族")
    @NotEmpty(message = "民族不能为空")
    private String mz;

    @ApiModelProperty("政治面貌")
    @NotEmpty(message = "政治面貌不能为空")
    private String zzmm;

    @ApiModelProperty("婚姻状况")
    @NotEmpty(message = "婚姻状况不能为空")
    private String hyzk;

    @ApiModelProperty("文化程度")
    @NotEmpty(message = "文化程度不能为空")
    private String whcd;

    @ApiModelProperty("籍贯")
    @NotEmpty(message = "籍贯不能为空")
    private String jg;

    @ApiModelProperty("身份")
    @NotEmpty(message = "身份不能为空")
    private String sf;

    @ApiModelProperty("工作单位")
    private String gzdw;

    @ApiModelProperty("职业")
    private String zy;

    @ApiModelProperty("户籍地")
    private String hjd;

    @ApiModelProperty("户籍地详址")
    @NotEmpty(message = "户籍地详址不能为空")
    private String hjdxz;

    @ApiModelProperty("现住址")
    @NotEmpty(message = "现住址不能为空")
    private String xzz;

    @ApiModelProperty("现住址详址")
    private String xzzxz;

    @ApiModelProperty("决定拘留机关")
    @NotEmpty(message = "决定拘留机关不能为空")
    private String jdjljg;

    @ApiModelProperty("办案单位类型")
    @NotEmpty(message = "办案单位类型不能为空")
    private String badwlx;

    @ApiModelProperty("办案单位")
    @NotEmpty(message = "办案单位不能为空")
    private String badw;

    @ApiModelProperty("送拘单位")
    @NotEmpty(message = "送拘单位不能为空")
    private String sjdw;

    @ApiModelProperty("送拘人")
    @NotEmpty(message = "送拘人不能为空")
    private String sjr;

    @ApiModelProperty("送拘凭证")
    @NotEmpty(message = "送拘凭证不能为空")
    private String sjpz;

    @ApiModelProperty("送拘法律文书号")
    @NotEmpty(message = "送拘法律文书号不能为空")
    private String sjflwsh;

    @ApiModelProperty("联系电话")
    @NotEmpty(message = "联系电话不能为空")
    private String lxdh;

    @ApiModelProperty("案件类别代码")
    @NotEmpty(message = "案件类别代码不能为空")
    private String ajlbdm;

    @ApiModelProperty("案件类别")
    @NotEmpty(message = "案件类别不能为空")
    private String ajlb;

    @ApiModelProperty("送拘民警")
    @NotEmpty(message = "送拘民警不能为空")
    private String sjmj;

    @ApiModelProperty("收拘类型")
    @NotEmpty(message = "收拘类型不能为空")
    private String sjlx;

    @ApiModelProperty("拘留时长")
    @NotNull(message = "拘留时长不能为空")
    private Integer jlsc;

    @ApiModelProperty("拘留起始日期")
    @NotNull(message = "拘留起始日期不能为空")
    private Date jlqsrq;

    @ApiModelProperty("拘留截止日期")
    @NotNull(message = "拘留截止日期不能为空")
    private Date jljzrq;

    @ApiModelProperty("简要案情")
    @NotEmpty(message = "简要案情不能为空")
    private String jyaq;

    @ApiModelProperty("备注")
    private String bz;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

}
