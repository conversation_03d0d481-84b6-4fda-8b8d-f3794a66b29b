package com.rs.module.acp.dao.sys;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.sys.VbTaskDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.sys.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
 * 实战平台-语音播报-待播报任务 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface VbTaskDao extends IBaseDao<VbTaskDO> {

	default PageResult<VbTaskDO> selectPage(VbTaskPageReqVO reqVO) {
		Page<VbTaskDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
		LambdaQueryWrapperX<VbTaskDO> wrapper = new LambdaQueryWrapperX<VbTaskDO>()
				.eqIfPresent(VbTaskDO::getVbPrisonId, reqVO.getVbPrisonId())
				.eqIfPresent(VbTaskDO::getVbAreaId, reqVO.getVbAreaId())
				.eqIfPresent(VbTaskDO::getVbRoomId, reqVO.getVbRoomId())
				.eqIfPresent(VbTaskDO::getVbType, reqVO.getVbType())
				.likeIfPresent(VbTaskDO::getVbName, reqVO.getVbName())
				.eqIfPresent(VbTaskDO::getContent, reqVO.getContent()).eqIfPresent(VbTaskDO::getVbNum, reqVO.getVbNum())
				.eqIfPresent(VbTaskDO::getPriority, reqVO.getPriority())
				.betweenIfPresent(VbTaskDO::getStartTime, reqVO.getStartTime())
				.betweenIfPresent(VbTaskDO::getEndTime, reqVO.getEndTime())
				.eqIfPresent(VbTaskDO::getStatus, reqVO.getStatus())
				.eqIfPresent(VbTaskDO::getVbSerialNumber, reqVO.getVbSerialNumber())
				.betweenIfPresent(VbTaskDO::getVbTime, reqVO.getVbTime())
				.betweenIfPresent(VbTaskDO::getCancelTime, reqVO.getCancelTime());
		if (reqVO.getOrderFields() != null) {
			page.setOrders(reqVO.getOrderFields());
		} else {
			wrapper.orderByDesc(VbTaskDO::getAddTime);
		}
		Page<VbTaskDO> vbTaskPage = selectPage(page, wrapper);
		return new PageResult<>(vbTaskPage.getRecords(), vbTaskPage.getTotal());
	}

	default List<VbTaskDO> selectList(VbTaskListReqVO reqVO) {
		return selectList(new LambdaQueryWrapperX<VbTaskDO>()
				.eqIfPresent(VbTaskDO::getVbPrisonId, reqVO.getVbPrisonId())
				.eqIfPresent(VbTaskDO::getVbAreaId, reqVO.getVbAreaId())
				.eqIfPresent(VbTaskDO::getVbRoomId, reqVO.getVbRoomId())
				.eqIfPresent(VbTaskDO::getVbType, reqVO.getVbType())
				.likeIfPresent(VbTaskDO::getVbName, reqVO.getVbName())
				.eqIfPresent(VbTaskDO::getContent, reqVO.getContent()).eqIfPresent(VbTaskDO::getVbNum, reqVO.getVbNum())
				.eqIfPresent(VbTaskDO::getPriority, reqVO.getPriority())
				.betweenIfPresent(VbTaskDO::getStartTime, reqVO.getStartTime())
				.betweenIfPresent(VbTaskDO::getEndTime, reqVO.getEndTime())
				.eqIfPresent(VbTaskDO::getStatus, reqVO.getStatus())
				.eqIfPresent(VbTaskDO::getVbSerialNumber, reqVO.getVbSerialNumber())
				.betweenIfPresent(VbTaskDO::getVbTime, reqVO.getVbTime())
				.betweenIfPresent(VbTaskDO::getCancelTime, reqVO.getCancelTime()).orderByDesc(VbTaskDO::getAddTime));
	}
}
