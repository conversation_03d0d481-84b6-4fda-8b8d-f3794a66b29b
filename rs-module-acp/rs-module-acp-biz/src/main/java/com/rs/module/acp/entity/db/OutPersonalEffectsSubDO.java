package com.rs.module.acp.entity.db;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-收押业务-出所随身物品登记子 DO
 *
 * <AUTHOR>
 */
@TableName("acp_db_out_personal_effects_sub")
@KeySequence("acp_db_out_personal_effects_sub_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_db_out_personal_effects_sub")
public class OutPersonalEffectsSubDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 随身物品ID
     */
    private String personalEffectsId;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 物品名称
     */
    private String wpmc;
    /**
     * 种类
     */
    private String zl;
    /**
     * 货币种类
     */
    private String hbzl;
    /**
     * 现金数量
     */
    private String sl;
    /**
     * 物品特征
     */
    private String wptz;
    /**
     * 物品照片
     */
    private String wpzp;
    /**
     * 位置
     */
    private String wz;
    /**
     * 物品处置情况
     */
    private String wpczqk;
    /**
     * 现金注入个人帐卡日期
     */
    private Date xjzrgrzkrq;
    /**
     * 取出物品数量
     */
    private Integer qcwpsl;
    /**
     * 备注
     */
    private String bz;

}
