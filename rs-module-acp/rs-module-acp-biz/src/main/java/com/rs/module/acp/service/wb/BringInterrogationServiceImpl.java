package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.wb.vo.ArraignmentVoucherSaveReqVO;
import com.rs.module.acp.controller.admin.wb.vo.BringInterrogationListReqVO;
import com.rs.module.acp.controller.admin.wb.vo.BringInterrogationPageReqVO;
import com.rs.module.acp.controller.admin.wb.vo.BringInterrogationRespVO;
import com.rs.module.acp.controller.admin.wb.vo.BringInterrogationSaveReqVO;
import com.rs.module.acp.controller.admin.wb.vo.CasePersonnelSaveReqVO;
import com.rs.module.acp.controller.admin.wb.vo.WbInspectionResultsSaveReqVO;
import com.rs.module.acp.dao.wb.BringInterrogationDao;
import com.rs.module.acp.entity.wb.BringInterrogationDO;
import com.rs.module.acp.entity.wb.CasePersonnelDO;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.acp.service.gj.InOutRecordsService;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.pm.AreaService;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.util.DicUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 实战平台-窗口业务-提询登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BringInterrogationServiceImpl extends BaseServiceImpl<BringInterrogationDao, BringInterrogationDO> implements BringInterrogationService {

    @Resource
    private BringInterrogationDao bringInterrogationDao;
    @Autowired
    private CasePersonnelService casePersonnelService;
    @Autowired
    private AreaService areaService;
    @Autowired
    private WbCommonService wbCommonService;
    @Autowired
    private PrisonerService prisonerService;
    @Autowired
    private CoopCasePersonnelService coopCasePersonnelService;

    @Autowired
    private InOutRecordsService inOutRecordsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createBringInterrogation(BringInterrogationSaveReqVO createReqVO) {

        List<CasePersonnelSaveReqVO> casePersonnelList = createReqVO.getCasePersonnelList();

        if (CollectionUtil.isEmpty(casePersonnelList) || casePersonnelList.size() < 2) {
            throw new ServerException("实战平台-窗口业务-提询登记办案人员数量不能小于2人");
        }

        BringInterrogationDO bringInterrogationDO = BeanUtils.toBean(createReqVO, BringInterrogationDO.class);

        bringInterrogationDO.setStartApplyArraignmentTime(createReqVO.getStartApplyArraignmentTime());
        bringInterrogationDO.setEndApplyArraignmentTime(createReqVO.getEndApplyArraignmentTime());

        bringInterrogationDO.setId(StringUtil.getGuid32());

        //判断casePersonnelList是否为空，则新增/更新数据插入到acp_wb_case_personnel 表
        for (CasePersonnelSaveReqVO casePersonnelSaveReqVO : casePersonnelList) {
            if(ObjectUtil.isEmpty(casePersonnelSaveReqVO.getId())){
                String casePersonnelId = casePersonnelService.createCasePersonnel(casePersonnelSaveReqVO);
                casePersonnelSaveReqVO.setId(casePersonnelId);
            }
        }

        bringInterrogationDO.setHandler1Id(casePersonnelList.get(0).getId());
        bringInterrogationDO.setHandler1Xm(casePersonnelList.get(0).getXm());
        bringInterrogationDO.setHandler2Id(casePersonnelList.get(1).getId());
        bringInterrogationDO.setHandler2Xm(casePersonnelList.get(1).getXm());
        if (casePersonnelList.size() > 2) {
            bringInterrogationDO.setHandler3Id(casePersonnelList.get(2).getId());
            bringInterrogationDO.setHandler3Xm(casePersonnelList.get(2).getXm());
        }

        Set<String> tjjgmcSet = new HashSet<>();
        casePersonnelList.forEach(x->{tjjgmcSet.add(x.getBadwmc());});
        bringInterrogationDO.setTjjgmc(String.join("、",tjjgmcSet));

        if(CollectionUtil.isNotEmpty(createReqVO.getCoopCasePersonnelList())){
            coopCasePersonnelService.saveCoopCasePersonnelList(createReqVO.getCoopCasePersonnelList(),bringInterrogationDO.getId());
        }


        //这里处理提讯凭证事情
        if(CollectionUtil.isNotEmpty(createReqVO.getVoucherList())){
            List<ArraignmentVoucherSaveReqVO> voucherList = createReqVO.getVoucherList();
            StringBuilder evidenceType = new StringBuilder();
            StringBuilder evidenceNumber = new StringBuilder();
            StringBuilder evidenceUrl = new StringBuilder();
            for(int i=0;i<voucherList.size();i++){
                evidenceType.append(voucherList.get(i).getEvidenceType());
                evidenceNumber.append(voucherList.get(i).getEvidenceNumber());
                if(ObjectUtil.isNotEmpty(voucherList.get(i).getEvidenceUrl())){
                    String objectId = wbCommonService.saveFile(null,voucherList.get(i).getEvidenceUrl());
                    if(ObjectUtil.isNotEmpty(objectId)){
                        evidenceUrl.append(objectId);
                    }
                }
                if(i<voucherList.size()-1){
                    evidenceType.append(",");
                    evidenceNumber.append(",");
                    evidenceUrl.append(",");
                }
            }
            bringInterrogationDO.setEvidenceType(evidenceType.toString());
            bringInterrogationDO.setEvidenceNumber(evidenceNumber.toString());
            bringInterrogationDO.setEvidenceUrl(evidenceUrl.toString());
        }

        //这里处理其他专业人员书面证明存储路径
        if(ObjectUtil.isNotEmpty(bringInterrogationDO.getProfessionalCertUrl())){
            String professionalCertUrl = wbCommonService.saveFile(null,bringInterrogationDO.getProfessionalCertUrl());
            bringInterrogationDO.setProfessionalCertUrl(professionalCertUrl);
        }

        bringInterrogationDO.setStatus("10-1");
        //需要进行审批
        PrisonerVwRespVO prisonerVwRespVO = prisonerService.getPrisonerByJgrybm(createReqVO.getJgrybm());
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", bringInterrogationDO.getId());
        variables.put("busType", MsgBusTypeEnum.CK_TJ.getCode());
        variables.put("roomName",prisonerVwRespVO.getRoomName());
        variables.put("prisonerName",prisonerVwRespVO.getXm());
        String meetingTime = DateUtil.format(bringInterrogationDO.getStartApplyArraignmentTime(),"yyyy-MM-dd HH:mm")
                +"~"+DateUtil.format(bringInterrogationDO.getEndApplyArraignmentTime(),"HH:mm");
        variables.put("meetingTime",meetingTime);
        String msgUrl = String.format("/InquiryJls/list?curId=%s&saveType=sp",bringInterrogationDO.getId());
        JSONObject result = BspApprovalUtil.defaultStartProcess(HttpUtils.getAppCode()+"-"+"chuangkouyewujuliustixunshenpi",bringInterrogationDO.getId(),"提询审批",msgUrl,variables);
        JSONObject data = result.getJSONObject("data");
        JSONObject bpmTrail = data.getJSONObject("bpmTrail");
        bringInterrogationDO.setActInstId(bpmTrail.getString("actInstId"));
        bringInterrogationDO.setTaskId(bpmTrail.getString("taskId"));


        bringInterrogationDao.insert(bringInterrogationDO);

        wbCommonService.registrationReminder(JSONObject.parseObject(JSON.toJSONString(bringInterrogationDO)), WbConstants.BUSINESS_TYPE_BRING_INTERROGATION);
        return bringInterrogationDO.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBringInterrogation(BringInterrogationSaveReqVO updateReqVO) {
        // 校验存在
        validateBringInterrogationExists(updateReqVO.getId());
        // 更新
        BringInterrogationDO updateObj = BeanUtils.toBean(updateReqVO, BringInterrogationDO.class);
        bringInterrogationDao.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBringInterrogation(String id) {
        // 校验存在
        validateBringInterrogationExists(id);
        // 删除
        bringInterrogationDao.deleteById(id);
    }

    private void validateBringInterrogationExists(String id) {
        if (bringInterrogationDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-提询登记数据不存在");
        }
    }

    @Override
    public BringInterrogationDO getBringInterrogation(String id) {
        return bringInterrogationDao.selectById(id);
    }

    @Override
    public PageResult<BringInterrogationDO> getBringInterrogationPage(BringInterrogationPageReqVO pageReqVO) {
        return bringInterrogationDao.selectPage(pageReqVO);
    }

    @Override
    public List<BringInterrogationDO> getBringInterrogationList(BringInterrogationListReqVO listReqVO) {
        return bringInterrogationDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean signIn(String id, String checkInTime) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
//        ArraignmentDO arraignmentDO = signInInit(id);
        LambdaUpdateWrapper<BringInterrogationDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(BringInterrogationDO::getId, id).set(BringInterrogationDO::getCheckInTime, DateUtil.parse(checkInTime,"yyyy-MM-dd HH:mm:ss"))
                .set(BringInterrogationDO::getCheckInPoliceSfzh,sessionUser.getIdCard())
                .set(BringInterrogationDO::getCheckInPolice, sessionUser.getName()).set(BringInterrogationDO::getStatus,"1");
        return update(lambdaUpdateWrapper);
    }

    private BringInterrogationDO signInInit(String id) {
        BringInterrogationDO bringInterrogationDO = getById(id);
        if (ObjectUtil.isEmpty(bringInterrogationDO)) {
            throw new ServerException("实战平台-窗口业务-提讯登记数据不存在");
        }
        List<String> casePersonnelIdList = new ArrayList<>();
        casePersonnelIdList.add(bringInterrogationDO.getHandler1Id());
        casePersonnelIdList.add(bringInterrogationDO.getHandler2Id());
        if (ObjectUtil.isNotEmpty(bringInterrogationDO.getHandler3Id())) {
            casePersonnelIdList.add(bringInterrogationDO.getHandler3Id());
        }
        List<CasePersonnelDO> casePersonnelDOList = casePersonnelService.list(new LambdaQueryWrapper<CasePersonnelDO>()
                .select(CasePersonnelDO::getZjhm, CasePersonnelDO::getXm).in(CasePersonnelDO::getId, casePersonnelIdList));

        String checkInPoliceSfzh = casePersonnelDOList.stream().map(x -> {
            return x.getZjhm();
        }).collect(Collectors.joining(","));
        String checkInPolice = casePersonnelDOList.stream().map(x -> {
            return x.getXm();
        }).collect(Collectors.joining(","));
        bringInterrogationDO.setCheckInPolice(checkInPolice);
        bringInterrogationDO.setCheckInPoliceSfzh(checkInPoliceSfzh);
        return bringInterrogationDO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean escortingInspect(BringInterrogationSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        LambdaUpdateWrapper<BringInterrogationDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(BringInterrogationDO::getId, updateReqVO.getId())
                .set(BringInterrogationDO::getEscortingPolice, updateReqVO.getEscortingPolice())
                .set(BringInterrogationDO::getEscortingPoliceSfzh, updateReqVO.getEscortingPoliceSfzh())
                .set(BringInterrogationDO::getEscortingTime, updateReqVO.getEscortingTime())
                .set(BringInterrogationDO::getInspectionResult, updateReqVO.getInspectionResult())
                .set(BringInterrogationDO::getProhibitedItems, updateReqVO.getProhibitedItems())
                .set(BringInterrogationDO::getPhysicalExam, updateReqVO.getPhysicalExam())
                .set(BringInterrogationDO::getAbnormalSituations, updateReqVO.getAbnormalSituations())
                .set(BringInterrogationDO::getInspectionTime, updateReqVO.getInspectionTime())
                .set(BringInterrogationDO::getInspector, updateReqVO.getInspector())
                .set(BringInterrogationDO::getInspectorSfzh, updateReqVO.getInspectorSfzh())
                .set(BringInterrogationDO::getArraignmentStartTime, updateReqVO.getArraignmentStartTime())
                .set(BringInterrogationDO::getStatus,"3");

        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorSfzh())){
            lambdaUpdateWrapper.set(BringInterrogationDO::getEscortingOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(BringInterrogationDO::getEscortingOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorTime())){
            lambdaUpdateWrapper.set(BringInterrogationDO::getEscortingOperatorTime,new Date());
        }

        if("0".equals(updateReqVO.getDataSources())){
            BringInterrogationDO bringInterrogationDO = getById(updateReqVO.getId());
            WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(updateReqVO,WbInspectionResultsSaveReqVO.class);
            resultsSaveReqVO.setJgrybm(bringInterrogationDO.getJgrybm());
            resultsSaveReqVO.setApplMeetingTime(bringInterrogationDO.getEndApplyArraignmentTime());
            resultsSaveReqVO.setBusinessSubType("0102");
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"01","out","3",false);
        }

        return update(lambdaUpdateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean returnInspection(BringInterrogationSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        LambdaUpdateWrapper<BringInterrogationDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(BringInterrogationDO::getId, updateReqVO.getId())
                .set(BringInterrogationDO::getArraignmentEndTime, updateReqVO.getArraignmentEndTime())
                .set(BringInterrogationDO::getReturnInspectorSfzh, updateReqVO.getReturnInspectorSfzh())
                .set(BringInterrogationDO::getReturnInspector, updateReqVO.getReturnInspector())
                .set(BringInterrogationDO::getReturnInspectionTime, updateReqVO.getReturnInspectionTime())
                .set(BringInterrogationDO::getReturnInspectionResult, updateReqVO.getReturnInspectionResult())
                .set(BringInterrogationDO::getReturnProhibitedItems, updateReqVO.getReturnProhibitedItems())
                .set(BringInterrogationDO::getReturnPhysicalExam, updateReqVO.getReturnPhysicalExam())
                .set(BringInterrogationDO::getReturnAbnormalSituations, updateReqVO.getReturnAbnormalSituations())
                .set(BringInterrogationDO::getReturnTime, updateReqVO.getReturnTime())
                .set(BringInterrogationDO::getReturnPolice, updateReqVO.getReturnPolice())
                .set(BringInterrogationDO::getReturnPoliceSfzh, updateReqVO.getReturnPoliceSfzh())
                .set(BringInterrogationDO::getStatus,"4");
        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorSfzh())){
            lambdaUpdateWrapper.set(BringInterrogationDO::getReturnOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(BringInterrogationDO::getReturnOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorTime())){
            lambdaUpdateWrapper.set(BringInterrogationDO::getReturnOperatorTime,new Date());
        }

        if("0".equals(updateReqVO.getDataSources())){
            BringInterrogationDO bringInterrogationDO = getById(updateReqVO.getId());
            WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(updateReqVO,WbInspectionResultsSaveReqVO.class);
            resultsSaveReqVO.setJgrybm(bringInterrogationDO.getJgrybm());
            resultsSaveReqVO.setBusinessSubType("0102");
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"01","in","3",false);
        }

        return update(lambdaUpdateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean additionalRecording(BringInterrogationSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        BringInterrogationDO bringInterrogationDO = signInInit(updateReqVO.getId());
        LambdaUpdateWrapper<BringInterrogationDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(BringInterrogationDO::getId, updateReqVO.getId())
                .set(BringInterrogationDO::getCheckInTime, updateReqVO.getCheckInTime())
                .set(BringInterrogationDO::getCheckInPoliceSfzh, bringInterrogationDO.getCheckInPoliceSfzh())
                .set(BringInterrogationDO::getCheckInPolice, bringInterrogationDO.getCheckInPolice())
                .set(BringInterrogationDO::getEscortingPolice, updateReqVO.getEscortingPolice())
                .set(BringInterrogationDO::getEscortingPoliceSfzh, updateReqVO.getEscortingPoliceSfzh())
                .set(BringInterrogationDO::getEscortingTime, updateReqVO.getEscortingTime())
                .set(BringInterrogationDO::getInspectionResult, updateReqVO.getInspectionResult())
                .set(BringInterrogationDO::getProhibitedItems, updateReqVO.getProhibitedItems())
                .set(BringInterrogationDO::getPhysicalExam, updateReqVO.getPhysicalExam())
                .set(BringInterrogationDO::getAbnormalSituations, updateReqVO.getAbnormalSituations())
                .set(BringInterrogationDO::getInspectionTime, updateReqVO.getInspectionTime())
                .set(BringInterrogationDO::getInspector, updateReqVO.getInspector())
                .set(BringInterrogationDO::getInspectorSfzh, updateReqVO.getInspectorSfzh())
                .set(BringInterrogationDO::getArraignmentStartTime, updateReqVO.getArraignmentStartTime())
                .set(BringInterrogationDO::getArraignmentEndTime, updateReqVO.getArraignmentEndTime())
                .set(BringInterrogationDO::getReturnInspectorSfzh, updateReqVO.getReturnInspectorSfzh())
                .set(BringInterrogationDO::getReturnInspector, updateReqVO.getReturnInspector())
                .set(BringInterrogationDO::getReturnInspectionTime, updateReqVO.getReturnInspectionTime())
                .set(BringInterrogationDO::getReturnInspectionResult, updateReqVO.getReturnInspectionResult())
                .set(BringInterrogationDO::getReturnProhibitedItems, updateReqVO.getReturnProhibitedItems())
                .set(BringInterrogationDO::getReturnPhysicalExam, updateReqVO.getReturnPhysicalExam())
                .set(BringInterrogationDO::getReturnAbnormalSituations, updateReqVO.getReturnAbnormalSituations())
                .set(BringInterrogationDO::getReturnTime, updateReqVO.getReturnTime())
                .set(BringInterrogationDO::getReturnPolice, updateReqVO.getReturnPolice())
                .set(BringInterrogationDO::getReturnPoliceSfzh, updateReqVO.getReturnPoliceSfzh())
                .set(BringInterrogationDO::getStatus,"4");

        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorSfzh())){
            lambdaUpdateWrapper.set(BringInterrogationDO::getEscortingOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(BringInterrogationDO::getEscortingOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorTime())){
            lambdaUpdateWrapper.set(BringInterrogationDO::getEscortingOperatorTime,new Date());
        }

        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorSfzh())){
            lambdaUpdateWrapper.set(BringInterrogationDO::getReturnOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(BringInterrogationDO::getReturnOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorTime())){
            lambdaUpdateWrapper.set(BringInterrogationDO::getReturnOperatorTime,new Date());
        }

        if("0".equals(updateReqVO.getDataSources())){
            WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(updateReqVO,WbInspectionResultsSaveReqVO.class);
            resultsSaveReqVO.setJgrybm(bringInterrogationDO.getJgrybm());
            resultsSaveReqVO.setBusinessSubType("0102");
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"01","out","3",true);
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"01","in","3",true);
        }

        return update(lambdaUpdateWrapper);
    }

    @Override
    public BringInterrogationRespVO getBringInterrogationById(String id) {
        BringInterrogationDO bringInterrogationDO = getById(id);
        if (ObjectUtil.isEmpty(bringInterrogationDO)) {
            throw new ServerException("实战平台-窗口业务-提讯登记数据不存在");
        }
        BringInterrogationRespVO bringInterrogationRespVO = BeanUtils.toBean(bringInterrogationDO, BringInterrogationRespVO.class);
        //组装办案民警信息

        List<String> casePersonnelIdList = new ArrayList<>();
        casePersonnelIdList.add(bringInterrogationDO.getHandler1Id());
        casePersonnelIdList.add(bringInterrogationDO.getHandler2Id());
        if (ObjectUtil.isNotEmpty(bringInterrogationDO.getHandler3Id())) {
            casePersonnelIdList.add(bringInterrogationDO.getHandler3Id());
        }

        bringInterrogationRespVO.setCasePersonnelList(casePersonnelService.getByIds(casePersonnelIdList));

        bringInterrogationRespVO.setCoopCasePersonnelList(coopCasePersonnelService.getListByTargetId(id));

        //这里处理提讯凭证事情
        List<ArraignmentVoucherSaveReqVO> voucherList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(bringInterrogationDO.getEvidenceType())){
            List<String> evidenceTypeList = Arrays.asList(bringInterrogationDO.getEvidenceType().split(","));
            List<String> evidenceNumberList = ObjectUtil.isNotEmpty(bringInterrogationDO.getEvidenceNumber())?
                    Arrays.asList(bringInterrogationDO.getEvidenceNumber().split(",")):new ArrayList<>();
            List<String> evidenceUrlList = ObjectUtil.isNotEmpty(bringInterrogationDO.getEvidenceUrl())?
                    Arrays.asList(bringInterrogationDO.getEvidenceUrl().split(",")):new ArrayList<>();
            for(int i=0;i<evidenceTypeList.size();i++){
                ArraignmentVoucherSaveReqVO voucher = new ArraignmentVoucherSaveReqVO();
                voucher.setEvidenceType(evidenceTypeList.get(i));
                if(CollectionUtil.isNotEmpty(evidenceNumberList)){
                    voucher.setEvidenceNumber(evidenceNumberList.get(i));
                }
                if(CollectionUtil.isNotEmpty(evidenceUrlList)){
                    if(ObjectUtil.isNotEmpty(evidenceUrlList.get(i))){
                        voucher.setEvidenceUrl(wbCommonService.getFile(evidenceUrlList.get(i)));
                    }
                }
                voucher.setEvidenceTypeName(DicUtils.translate("ZD_WB_TXPZLX",voucher.getEvidenceType()));
                voucherList.add(voucher);
            }
        }
        bringInterrogationRespVO.setVoucherList(voucherList);

        //书面证明
        if(ObjectUtil.isNotEmpty(bringInterrogationRespVO.getProfessionalCertUrl())){
            bringInterrogationRespVO.setProfessionalCertUrl(wbCommonService.getFile(bringInterrogationRespVO.getProfessionalCertUrl()));
        }

        return bringInterrogationRespVO;
    }

    @Override
    public PageResult<BringInterrogationRespVO> getHistoryBringInterrogation(String jgrybm,int pageNo,int pageSize) {
        Page<BringInterrogationDO> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<BringInterrogationDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(BringInterrogationDO::getAddTime,BringInterrogationDO::getHandler1Id,
                BringInterrogationDO::getHandler2Id,BringInterrogationDO::getHandler3Id,BringInterrogationDO::getArraignmentReason);
        lambdaQueryWrapper.eq(BringInterrogationDO::getJgrybm,jgrybm)
                .orderByDesc(BringInterrogationDO::getAddTime);

        Page<BringInterrogationDO> bringInterrogationDOPage = page(page,lambdaQueryWrapper);
        List<BringInterrogationRespVO> bringInterrogationRespVOList = BeanUtils.toBean(bringInterrogationDOPage.getRecords(),BringInterrogationRespVO.class);
        if(CollectionUtil.isEmpty(bringInterrogationRespVOList)){
            return new PageResult<>(new ArrayList<>() ,0L);
        }

        Set<String> handlerIdSet =  new HashSet<>();

        for(BringInterrogationRespVO bringInterrogationRespVO:bringInterrogationRespVOList){
            handlerIdSet.add(bringInterrogationRespVO.getHandler1Id());
            handlerIdSet.add(bringInterrogationRespVO.getHandler2Id());
            if(ObjectUtil.isNotEmpty(bringInterrogationRespVO.getHandler3Id())){
                handlerIdSet.add(bringInterrogationRespVO.getHandler3Id());
            }
        }

        List<String> handlerIdList = handlerIdSet.stream().collect(Collectors.toList());

        LambdaQueryWrapper<CasePersonnelDO> casePersonnelLambdaQueryWrapper = new LambdaQueryWrapper<>();
        casePersonnelLambdaQueryWrapper.select(CasePersonnelDO::getId,CasePersonnelDO::getXm,CasePersonnelDO::getBadwmc)
                .in(CasePersonnelDO::getId,handlerIdList);
        List<CasePersonnelDO> casePersonnelDOList = casePersonnelService.list(casePersonnelLambdaQueryWrapper);

        Map<String,CasePersonnelDO> casePersonnelMap = new HashMap<>();
        casePersonnelDOList.forEach(x->{casePersonnelMap.put(x.getId(),x);});

        for(BringInterrogationRespVO bringInterrogationRespVO:bringInterrogationRespVOList){
            StringBuilder handlerXm = new StringBuilder();
            Set<String> badwmcSet = new HashSet<>();

            if(casePersonnelMap.containsKey(bringInterrogationRespVO.getHandler1Id())){
                handlerXm.append(casePersonnelMap.get(bringInterrogationRespVO.getHandler1Id()).getXm());
                badwmcSet.add(casePersonnelMap.get(bringInterrogationRespVO.getHandler1Id()).getBadwmc());
            }

            if(casePersonnelMap.containsKey(bringInterrogationRespVO.getHandler2Id())){
                handlerXm.append("、");
                handlerXm.append(casePersonnelMap.get(bringInterrogationRespVO.getHandler2Id()).getXm());
                badwmcSet.add(casePersonnelMap.get(bringInterrogationRespVO.getHandler2Id()).getBadwmc());
            }

            if(ObjectUtil.isNotEmpty(bringInterrogationRespVO.getHandler3Id())){
                if(casePersonnelMap.containsKey(bringInterrogationRespVO.getHandler2Id())){
                    handlerXm.append("、");
                    handlerXm.append(casePersonnelMap.get(bringInterrogationRespVO.getHandler3Id()).getXm());
                    badwmcSet.add(casePersonnelMap.get(bringInterrogationRespVO.getHandler3Id()).getBadwmc());
                }
            }
            bringInterrogationRespVO.setHandlerXm(handlerXm.toString());
            bringInterrogationRespVO.setBadwmc(String.join("、",badwmcSet));
        }

        return new PageResult<>(bringInterrogationRespVOList,bringInterrogationDOPage.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean allocationRoom(String id, String roomId) {
        return update(new LambdaUpdateWrapper<BringInterrogationDO>()
                .eq(BringInterrogationDO::getId,id).set(BringInterrogationDO::getRoomId,roomId)
                .set(BringInterrogationDO::getStatus,"2"));
    }
}
