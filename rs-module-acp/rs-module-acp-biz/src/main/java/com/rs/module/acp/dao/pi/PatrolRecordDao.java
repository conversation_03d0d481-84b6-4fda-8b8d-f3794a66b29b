package com.rs.module.acp.dao.pi;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.pi.vo.patrolrecord.PatrolRecordListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.patrolrecord.PatrolRecordPageReqVO;
import com.rs.module.acp.entity.pi.PatrolRecordDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-巡视管控-巡视登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PatrolRecordDao extends IBaseDao<PatrolRecordDO> {

}
