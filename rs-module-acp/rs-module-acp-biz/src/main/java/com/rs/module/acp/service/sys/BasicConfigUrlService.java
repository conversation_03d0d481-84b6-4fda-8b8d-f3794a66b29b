package com.rs.module.acp.service.sys;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.sys.vo.BasicConfigSaveReqVO;
import com.rs.module.acp.entity.sys.BasicConfigUrlDO;

/**
 * 实战平台-系统基础配置链接 Service 接口
 *
 * <AUTHOR>
 */
public interface BasicConfigUrlService extends IBaseService<BasicConfigUrlDO>{


    /**
     * 保存配置信息
     * <AUTHOR>
     * @date 2025/5/23 10:36
     * @param [saveReqVO, systemId, urlType]
     * @return void
     */
    void saveConfigBatchInfo(BasicConfigSaveReqVO saveReqVO, String systemId);
}
