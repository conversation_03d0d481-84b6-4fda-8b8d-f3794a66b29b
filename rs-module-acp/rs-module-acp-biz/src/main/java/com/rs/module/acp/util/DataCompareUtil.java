package com.rs.module.acp.util;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.bsp.common.cache.DicUtil;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.fhs.core.trans.anno.Trans;
import io.swagger.annotations.ApiModelProperty;

import java.lang.reflect.Field;
import java.util.*;

public class DataCompareUtil {

    private static final String[] ID_FIELD_NAMES = {"id", "ID", "Id"};

    private static Object extractId(Object obj) throws Exception {
        for (String fieldName : ID_FIELD_NAMES) {
            try {
                Field field = obj.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                return field.get(obj);
            } catch (NoSuchFieldException ignored) {}
        }
        throw new IllegalArgumentException("未找到ID字段");
    }

    /**
     * 比较实体中内容，获取内容不一致的数据
     * @param obj1
     * @param obj2
     * @return
     * @throws IllegalAccessException
     */
    public static List<Map<String, Object>> compareBasicData(Object obj1, Object obj2, String ...fieldNames)
            throws IllegalAccessException {
        if (obj1 == null || obj2 == null) {
            throw new IllegalArgumentException("比较对象不能为null");
        }

        Class<?> clazz = obj1.getClass();
        if (!clazz.equals(obj2.getClass())) {
            throw new IllegalArgumentException("比较对象类型必须相同");
        }

        List<Map<String, Object>> diffMap = new ArrayList<>();
        Field[] fields = clazz.getDeclaredFields();
        List<Map<String, Object>> updateData = new ArrayList<>();
        for (Field field : fields) {
            field.setAccessible(true);
            if ("correctionDataType".equals(field.getName()) || (fieldNames != null && Arrays.asList(fieldNames).contains(field.getName()))) {
                continue;
            }
            JSONObject json1 = JSONObject.parseObject(JSONObject.toJSONString(obj1, SerializerFeature.WriteDateUseDateFormat));
            JSONObject json2 = JSONObject.parseObject(JSONObject.toJSONString(obj2, SerializerFeature.WriteDateUseDateFormat));
            String value1 = StringUtil.isNullBlank(json1.getString(field.getName())) ? "" : json1.getString(field.getName());
            String value2 = StringUtil.isNullBlank(json2.getString(field.getName())) ? "" : json2.getString(field.getName());

            if (!value1.equals(value2)) {
                Map<String, Object> valueMap = new HashMap<>();
                valueMap.put("id", StringUtil.getGuid32());
                valueMap.put("field", field.getName());
                valueMap.put("newValue", value1);
                valueMap.put("operationType", "updated");
                valueMap.put("oldValue", value2);
                // 通过注解获取字段注释
                if (field.isAnnotationPresent(ApiModelProperty.class)) {
                    ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                    valueMap.put("fieldName", annotation.value());
                }
                // 通过注解获取字段翻译值
                if (field.isAnnotationPresent(Trans.class)) {
                    Trans annotation = field.getAnnotation(Trans.class);
                    String key = annotation.key();
                    valueMap.put("newValueName", DicUtil.translate(key, value1));
                    valueMap.put("oldValueName", DicUtil.translate(key, value2));
                }
                diffMap.add(valueMap);
            }
        }
        return diffMap;
    }

    public static Map<String, Object> compareComplexData(Object newObj, Object dbObj, String ...fieldNames)
            throws IllegalAccessException {
        Map<String, Object> result = new HashMap<>();

        // 1. 比较主表字段
        List<Map<String, Object>> mainDiff = compareBasicData(newObj, dbObj, fieldNames);
        if (ObjectUtil.isNotNull(mainDiff)) {
            result.put("mainTable", mainDiff);
        }

        // 2. 比较子表集合
        try {
            for (String fieldName : fieldNames) {
                Field field = newObj.getClass().getDeclaredField(fieldName);
                // 通过注解获取字段注释
                String fieldNote = "";
                if (field.isAnnotationPresent(ApiModelProperty.class)) {
                    ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                    fieldNote = annotation.value();
                }
                if (Collection.class.isAssignableFrom(field.getType())) {
                    field.setAccessible(true);
                    Collection<Object> newCollection = (Collection<Object>) field.get(newObj);
                    Collection<Object> dbCollection = (Collection<Object>) field.get(dbObj);

                    if (newCollection != null && dbCollection != null) {
                        List<Map<String, Object>> collectionDiff = compareCollections(fieldName, fieldNote,
                                newCollection, dbCollection);
                        if (CollectionUtil.isNotNull(collectionDiff)) {
                            result.put(field.getName(), collectionDiff);
                        }
                    }
                } else {
                    field.setAccessible(true);
                    Object subNewObj = field.get(newObj);
                    Object subDbObj = field.get(dbObj);
                    List<Map<String, Object>> updateData = compareBasicData(subNewObj, subDbObj);
                    if (CollectionUtil.isNotNull(updateData)) {
                        result.put(field.getName(), updateData);
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("复杂类型比对异常", e);
        }
        return result;
    }

    private static List<Map<String, Object>> compareCollections(String field, String fieldName,
                                                          Collection<Object> newColl, Collection<Object> dbColl)
            throws Exception {
        List<Map<String, Object>> diffResult = new ArrayList<>();

        // 找出新增项
        List<Object> createdItems = new ArrayList<>();
        for (Object item : newColl) {
            Object id = extractId(item);
            if (id == null || dbColl.stream().noneMatch(dbItem -> {
                try { return id.equals(extractId(dbItem)); }
                catch (Exception e) { return false; }
            })) {
                createdItems.add(item);
            }
        }
        if (!createdItems.isEmpty()) {
            for (Object createdItem : createdItems) {
                JSONObject dataJson = new JSONObject();
                dataJson.put("id", StringUtil.getGuid32());
                dataJson.put("operationType", "created");
                dataJson.put("field", field);;
                dataJson.put("fieldName", fieldName);;
                dataJson.put("newValue", createdItem);
                dataJson.put("oldValue", null);
                diffResult.add(dataJson);
            }
        }

        // 找出删除项
        List<Object> deletedItems = new ArrayList<>();
        for (Object dbItem : dbColl) {
            Object dbId = extractId(dbItem);
            if (newColl.stream().noneMatch(newItem -> {
                try { return dbId.equals(extractId(newItem)); }
                catch (Exception e) { return false; }
            })) {
                deletedItems.add(dbItem);
            }
        }
        if (!deletedItems.isEmpty()) {
            for (Object deletedItem : deletedItems) {
                JSONObject dataJson = new JSONObject();
                dataJson.put("id", StringUtil.getGuid32());
                dataJson.put("operationType", "deleted");
                dataJson.put("field", field);;
                dataJson.put("fieldName", fieldName);;
                dataJson.put("newValue", null);
                dataJson.put("oldValue", deletedItem);
                diffResult.add(dataJson);
            }
        }

        // 找出更新项
//        Map<String, Object> updatedItems = new HashMap<>();
//        List<Map<String, Object>> updatedItems = new ArrayList<>();
        for (Object item : newColl) {
            Object id = extractId(item);
            if (id != null) {
                Optional<Object> dbItemOpt = dbColl.stream()
                        .filter(i -> {
                            try { return id.equals(extractId(i)); }
                            catch (Exception e) { return false; }
                        })
                        .findFirst();
                if (dbItemOpt.isPresent() && !deletedItems.contains(dbItemOpt.get()) && !Objects.equals(item, dbItemOpt.get())) {
//                    Map<String, Object> updateData = compareBasicData(item, dbItemOpt.get());
//                    if (ObjectUtil.isNotEmpty(updateData)) {
//
//                    }
                    Map<String, Object> updateData = new HashMap<>();
                    updateData.put("id", StringUtil.getGuid32());
                    updateData.put("operationType", "updated");
                    updateData.put("field", field);;
                    updateData.put("fieldName", fieldName);;
                    updateData.put("newValue", item);
                    updateData.put("oldValue", dbItemOpt.get());
                    diffResult.add(updateData);
                }
            }
        }

        return diffResult;
    }

    private static boolean containsInCollection(Object target, Collection<Object> collection) {
        return collection.stream()
                .anyMatch(item -> getId(item).equals(getId(target)));
    }

    private static Object getId(Object obj) {
        try {
            Field idField = obj.getClass().getDeclaredField("id");
            idField.setAccessible(true);
            return idField.get(obj);
        } catch (Exception e) {
            throw new RuntimeException("对象必须包含id字段", e);
        }
    }

}
