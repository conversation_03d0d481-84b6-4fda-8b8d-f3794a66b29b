package com.rs.module.acp.entity.db;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("随身物品信息实体")
public class PersonalEffectsInfoDO {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("取出原因")
    private String qcyy;

    /**
     * 取出方式
     */
    @ApiModelProperty("取出方式")
    private String qcfs;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String bz;

    /**
     * 经办人
     */
    @ApiModelProperty("经办人")
    private String jbr;

    /**
     * 经办时间
     */
    @ApiModelProperty("经办时间")
    private Date jbsj;

    @ApiModelProperty("物品列表")
    private List<OutPersonalEffectsSubDO> personalEffectsList;


}
