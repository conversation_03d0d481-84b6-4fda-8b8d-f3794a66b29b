package com.rs.module.acp.entity.ds;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-数据固化-监所人员变动记录 DO
 *
 * <AUTHOR>
 */
@TableName("acp_ds_prison_room_change")
@KeySequence("acp_ds_prison_room_change_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_ds_prison_room_change")
public class DSPrisonRoomChangeDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 入监室时间
     */
    private Date inRoomTime;
    /**
     * 入监室类型 0 入所,1 监室调整
     */
    private String inRoomType;
    /**
     * 入监室关联业务ID
     */
    private String inRoomBusinessId;
    /**
     * in_room_reason
     */
    private String inRoomReason;
    /**
     * 出监室时间
     */
    private Date outRoomTime;
    /**
     * 出监室类型 0 出所,1 监室调整
     */
    private String outRoomType;
    /**
     * 出监室关联业务ID
     */
    private String outRoomBusinessId;
    /**
     * 出监室原因
     */
    private String outRoomReason;
    /**
     * batch_id
     */
    private String batchId;

}
