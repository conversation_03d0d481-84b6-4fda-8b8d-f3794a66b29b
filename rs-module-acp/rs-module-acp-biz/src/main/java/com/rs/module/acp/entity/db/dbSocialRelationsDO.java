package com.rs.module.acp.entity.db;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-收押业务-社会关系 DO
 *
 * <AUTHOR>
 */
@TableName("acp_db_social_relations")
@KeySequence("acp_db_social_relations_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_db_social_relations")
public class dbSocialRelationsDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 人员编号
     */
    private String rybh;
    /**
     * 姓名
     */
    private String name;
    /**
     * 性别
     */
    private String gender;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 证件号码
     */
    private String idNumber;
    /**
     * 社会关系
     */
    private String relationship;
    /**
     * 联系方式
     */
    private String contact;
    /**
     * 工作单位
     */
    private String workUnit;
    /**
     * 居住地址
     */
    private String address;
    /**
     * 职业
     */
    private String occupation;
    /**
     * 照片
     */
    private String imageUrl;

    private String jgrybm;

}
