package com.rs.module.acp.dao.pi;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.pi.vo.shifthandover.ShiftHandoverListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.shifthandover.ShiftHandoverPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.shifthandover.ShiftHandoverStatisticsNumVO;
import com.rs.module.acp.entity.pi.ShiftHandoverDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
* 实战平台-巡视管控-巡控交接班登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ShiftHandoverDao extends IBaseDao<ShiftHandoverDO> {
    List<ShiftHandoverStatisticsNumVO> getPersonDistribution(@Param("areaId") String areaId,@Param("orgCode")String orgCode ,@Param("startTime") Date startTime, @Param("endTime") Date endTime);

}
