package com.rs.module.acp.service.gj.diagnosiassmtjds.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AssmtCommonBO {

    public AssmtCommonBO() {
    }

    public AssmtCommonBO(String keyCode, String keyName, String keyValue) {
        this.keyCode = keyCode;
        this.keyName = keyName;
        this.keyValue = keyValue;
    }

    @ApiModelProperty("code值")
    private String keyCode;

    @ApiModelProperty("code名称")
    private String keyName;

    @ApiModelProperty("0否 1是 或 分数")
    private String keyValue;
}
