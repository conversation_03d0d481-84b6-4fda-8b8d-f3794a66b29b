package com.rs.module.acp.dao.pi;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventPrisonerListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventPrisonerPageReqVO;
import com.rs.module.acp.entity.pi.PrisonEventPrisonerDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-巡视管控-所情事件在押人员 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PrisonEventPrisonerDao extends IBaseDao<PrisonEventPrisonerDO> {


    default PageResult<PrisonEventPrisonerDO> selectPage(PrisonEventPrisonerPageReqVO reqVO) {
        Page<PrisonEventPrisonerDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrisonEventPrisonerDO> wrapper = new LambdaQueryWrapperX<PrisonEventPrisonerDO>()
            .eqIfPresent(PrisonEventPrisonerDO::getEventId, reqVO.getEventId())
            .eqIfPresent(PrisonEventPrisonerDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PrisonEventPrisonerDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(PrisonEventPrisonerDO::getDeductPoint, reqVO.getDeductPoint())
            .eqIfPresent(PrisonEventPrisonerDO::getShouldDeductPoint, reqVO.getShouldDeductPoint())
            .eqIfPresent(PrisonEventPrisonerDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(PrisonEventPrisonerDO::getRoomName, reqVO.getRoomName())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PrisonEventPrisonerDO::getAddTime);
        }
        Page<PrisonEventPrisonerDO> prisonEventPrisonerPage = selectPage(page, wrapper);
        return new PageResult<>(prisonEventPrisonerPage.getRecords(), prisonEventPrisonerPage.getTotal());
    }
    default List<PrisonEventPrisonerDO> selectList(PrisonEventPrisonerListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrisonEventPrisonerDO>()
            .eqIfPresent(PrisonEventPrisonerDO::getEventId, reqVO.getEventId())
            .eqIfPresent(PrisonEventPrisonerDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PrisonEventPrisonerDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(PrisonEventPrisonerDO::getDeductPoint, reqVO.getDeductPoint())
            .eqIfPresent(PrisonEventPrisonerDO::getShouldDeductPoint, reqVO.getShouldDeductPoint())
            .eqIfPresent(PrisonEventPrisonerDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(PrisonEventPrisonerDO::getRoomName, reqVO.getRoomName())
        .orderByDesc(PrisonEventPrisonerDO::getAddTime));    }


    }
