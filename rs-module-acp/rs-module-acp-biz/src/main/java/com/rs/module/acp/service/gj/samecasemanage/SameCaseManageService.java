package com.rs.module.acp.service.gj.samecasemanage;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.samecasemanage.SameCaseManageListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.samecasemanage.SameCaseManagePageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.samecasemanage.SameCaseManageSaveReqVO;
import com.rs.module.acp.entity.gj.SameCaseManageDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.service.gj.samecasemanage.bo.SameCaseManageBO;

/**
 * 实战平台-管教业务-同案人员管理 Service 接口
 *
 * <AUTHOR>
 */
public interface SameCaseManageService extends IBaseService<SameCaseManageDO>{

    /**
     * 创建实战平台-管教业务-同案人员管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createSameCaseManage(@Valid SameCaseManageSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-同案人员管理
     *
     * @param updateReqVO 更新信息
     */
    void updateSameCaseManage(@Valid SameCaseManageSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-同案人员管理
     *
     * @param id 编号
     */
    void deleteSameCaseManage(String id);

    /**
     * 获得实战平台-管教业务-同案人员管理
     *
     * @param id 编号
     * @return 实战平台-管教业务-同案人员管理
     */
    SameCaseManageDO getSameCaseManage(String id);

    /**
    * 获得实战平台-管教业务-同案人员管理分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-同案人员管理分页
    */
    PageResult<SameCaseManageDO> getSameCaseManagePage(SameCaseManagePageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-同案人员管理列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-同案人员管理列表
    */
    List<SameCaseManageDO> getSameCaseManageList(SameCaseManageListReqVO listReqVO);


    PageResult<SameCaseManageBO> manageListPage(int pageNo, int pageSize, String jgrybm);
}
