package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.transitionroom.TransitionRoomExtendListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.transitionroom.TransitionRoomExtendPageReqVO;
import com.rs.module.acp.entity.gj.TransitionRoomExtendDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-过渡监室延长呈批 Dao
*
* <AUTHOR>
*/
@Mapper
public interface TransitionRoomExtendDao extends IBaseDao<TransitionRoomExtendDO> {


    default PageResult<TransitionRoomExtendDO> selectPage(TransitionRoomExtendPageReqVO reqVO) {
        Page<TransitionRoomExtendDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<TransitionRoomExtendDO> wrapper = new LambdaQueryWrapperX<TransitionRoomExtendDO>()
            .eqIfPresent(TransitionRoomExtendDO::getTransitionRoomId, reqVO.getTransitionRoomId())
            .eqIfPresent(TransitionRoomExtendDO::getReason, reqVO.getReason())
            .eqIfPresent(TransitionRoomExtendDO::getExtendDay, reqVO.getExtendDay())
            .eqIfPresent(TransitionRoomExtendDO::getStatus, reqVO.getStatus())
            .eqIfPresent(TransitionRoomExtendDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(TransitionRoomExtendDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(TransitionRoomExtendDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(TransitionRoomExtendDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(TransitionRoomExtendDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(TransitionRoomExtendDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(TransitionRoomExtendDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(TransitionRoomExtendDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(TransitionRoomExtendDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(TransitionRoomExtendDO::getAddTime);
        }
        Page<TransitionRoomExtendDO> transitionRoomExtendPage = selectPage(page, wrapper);
        return new PageResult<>(transitionRoomExtendPage.getRecords(), transitionRoomExtendPage.getTotal());
    }
    default List<TransitionRoomExtendDO> selectList(TransitionRoomExtendListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TransitionRoomExtendDO>()
            .eqIfPresent(TransitionRoomExtendDO::getTransitionRoomId, reqVO.getTransitionRoomId())
            .eqIfPresent(TransitionRoomExtendDO::getReason, reqVO.getReason())
            .eqIfPresent(TransitionRoomExtendDO::getExtendDay, reqVO.getExtendDay())
            .eqIfPresent(TransitionRoomExtendDO::getStatus, reqVO.getStatus())
            .eqIfPresent(TransitionRoomExtendDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(TransitionRoomExtendDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(TransitionRoomExtendDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(TransitionRoomExtendDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(TransitionRoomExtendDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(TransitionRoomExtendDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(TransitionRoomExtendDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(TransitionRoomExtendDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(TransitionRoomExtendDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(TransitionRoomExtendDO::getAddTime));    }


    }
