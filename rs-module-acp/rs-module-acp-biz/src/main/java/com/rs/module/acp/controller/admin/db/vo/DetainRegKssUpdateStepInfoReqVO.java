package com.rs.module.acp.controller.admin.db.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 更新阶段状态流转信息请求参数
 */
@Data
@ApiModel("更新阶段状态流转信息请求参数")
public class DetainRegKssUpdateStepInfoReqVO {
    @ApiModelProperty("监所类型")
    private String prison;

    @ApiModelProperty("人员编号")
    private String rybh;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("当前步骤")
    private String currentStep;

    @ApiModelProperty("审批状态")
    private String spzt;

    @ApiModelProperty("流程实例ID")
    private String actInstId;
}
