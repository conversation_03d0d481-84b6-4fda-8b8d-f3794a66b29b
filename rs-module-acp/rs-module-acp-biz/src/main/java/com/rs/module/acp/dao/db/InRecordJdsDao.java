package com.rs.module.acp.dao.db;

import java.util.*;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.db.InRecordJdsDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-收押业务-入所登记（戒毒所） Dao
*
* <AUTHOR>
*/
@Mapper
public interface InRecordJdsDao extends IBaseDao<InRecordJdsDO> {


    default PageResult<InRecordJdsDO> selectPage(InRecordJdsPageReqVO reqVO) {
        Page<InRecordJdsDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<InRecordJdsDO> wrapper = new LambdaQueryWrapperX<InRecordJdsDO>()
            .eqIfPresent(InRecordJdsDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(InRecordJdsDO::getXm, reqVO.getXm())
            .eqIfPresent(InRecordJdsDO::getXmpy, reqVO.getXmpy())
            .eqIfPresent(InRecordJdsDO::getBm, reqVO.getBm())
            .eqIfPresent(InRecordJdsDO::getXb, reqVO.getXb())
            .eqIfPresent(InRecordJdsDO::getCsrq, reqVO.getCsrq())
            .eqIfPresent(InRecordJdsDO::getZjlx, reqVO.getZjlx())
            .eqIfPresent(InRecordJdsDO::getZjhm, reqVO.getZjhm())
            .eqIfPresent(InRecordJdsDO::getGj, reqVO.getGj())
            .eqIfPresent(InRecordJdsDO::getMz, reqVO.getMz())
            .eqIfPresent(InRecordJdsDO::getHyzk, reqVO.getHyzk())
            .eqIfPresent(InRecordJdsDO::getJg, reqVO.getJg())
            .eqIfPresent(InRecordJdsDO::getZjxy, reqVO.getZjxy())
            .eqIfPresent(InRecordJdsDO::getHjd, reqVO.getHjd())
            .eqIfPresent(InRecordJdsDO::getHjdxz, reqVO.getHjdxz())
            .eqIfPresent(InRecordJdsDO::getXzz, reqVO.getXzz())
            .eqIfPresent(InRecordJdsDO::getXzzxz, reqVO.getXzzxz())
            .eqIfPresent(InRecordJdsDO::getWhcd, reqVO.getWhcd())
            .eqIfPresent(InRecordJdsDO::getZzmm, reqVO.getZzmm())
            .eqIfPresent(InRecordJdsDO::getZy, reqVO.getZy())
            .eqIfPresent(InRecordJdsDO::getGzdw, reqVO.getGzdw())
            .eqIfPresent(InRecordJdsDO::getSf, reqVO.getSf())
            .eqIfPresent(InRecordJdsDO::getTssf, reqVO.getTssf())
            .eqIfPresent(InRecordJdsDO::getXxhc, reqVO.getXxhc())
            .eqIfPresent(InRecordJdsDO::getSfwxzxs, reqVO.getSfwxzxs())
            .eqIfPresent(InRecordJdsDO::getXxmc, reqVO.getXxmc())
            .eqIfPresent(InRecordJdsDO::getAjlbdm, reqVO.getAjlbdm())
            .eqIfPresent(InRecordJdsDO::getAjlb, reqVO.getAjlb())
            .eqIfPresent(InRecordJdsDO::getAjbh, reqVO.getAjbh())
            .eqIfPresent(InRecordJdsDO::getRybh, reqVO.getRybh())
            .eqIfPresent(InRecordJdsDO::getAsjxgrybh, reqVO.getAsjxgrybh())
            .eqIfPresent(InRecordJdsDO::getBazxbh, reqVO.getBazxbh())
            .eqIfPresent(InRecordJdsDO::getSjrq, reqVO.getSjrq())
            .eqIfPresent(InRecordJdsDO::getSjjglx, reqVO.getSjjglx())
            .eqIfPresent(InRecordJdsDO::getSjjgmc, reqVO.getSjjgmc())
            .eqIfPresent(InRecordJdsDO::getSjrxm, reqVO.getSjrxm())
            .eqIfPresent(InRecordJdsDO::getSjrlxdh, reqVO.getSjrlxdh())
            .eqIfPresent(InRecordJdsDO::getSjrxm2, reqVO.getSjrxm2())
            .eqIfPresent(InRecordJdsDO::getSjrlxdh2, reqVO.getSjrlxdh2())
            .eqIfPresent(InRecordJdsDO::getSjmjxm, reqVO.getSjmjxm())
            .eqIfPresent(InRecordJdsDO::getSjmjxmlxdh, reqVO.getSjmjxmlxdh())
            .eqIfPresent(InRecordJdsDO::getBadwlx, reqVO.getBadwlx())
            .eqIfPresent(InRecordJdsDO::getBadw, reqVO.getBadw())
            .eqIfPresent(InRecordJdsDO::getSjjdjglx, reqVO.getSjjdjglx())
            .eqIfPresent(InRecordJdsDO::getSjjdjgmc, reqVO.getSjjdjgmc())
            .eqIfPresent(InRecordJdsDO::getSjpz, reqVO.getSjpz())
            .eqIfPresent(InRecordJdsDO::getSjpzwsh, reqVO.getSjpzwsh())
            .eqIfPresent(InRecordJdsDO::getSjpzwsdz, reqVO.getSjpzwsdz())
            .eqIfPresent(InRecordJdsDO::getDabh, reqVO.getDabh())
            .eqIfPresent(InRecordJdsDO::getHzflwsh, reqVO.getHzflwsh())
            .eqIfPresent(InRecordJdsDO::getRsyy, reqVO.getRsyy())
            .eqIfPresent(InRecordJdsDO::getRsrq, reqVO.getRsrq())
            .eqIfPresent(InRecordJdsDO::getSjmj, reqVO.getSjmj())
            .eqIfPresent(InRecordJdsDO::getSjqx, reqVO.getSjqx())
            .eqIfPresent(InRecordJdsDO::getSjqsrq, reqVO.getSjqsrq())
            .eqIfPresent(InRecordJdsDO::getSjjzrq, reqVO.getSjjzrq())
            .eqIfPresent(InRecordJdsDO::getGllb, reqVO.getGllb())
            .eqIfPresent(InRecordJdsDO::getSfpdyj, reqVO.getSfpdyj())
            .eqIfPresent(InRecordJdsDO::getAreaId, reqVO.getAreaId())
            .likeIfPresent(InRecordJdsDO::getAreaName, reqVO.getAreaName())
            .eqIfPresent(InRecordJdsDO::getJsh, reqVO.getJsh())
            .likeIfPresent(InRecordJdsDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(InRecordJdsDO::getFpcw, reqVO.getFpcw())
            .eqIfPresent(InRecordJdsDO::getSfpbsf, reqVO.getSfpbsf())
            .eqIfPresent(InRecordJdsDO::getDrjsr, reqVO.getDrjsr())
            .eqIfPresent(InRecordJdsDO::getDrjssj, reqVO.getDrjssj())
            .eqIfPresent(InRecordJdsDO::getJyaq, reqVO.getJyaq())
            .eqIfPresent(InRecordJdsDO::getTar, reqVO.getTar())
            .eqIfPresent(InRecordJdsDO::getBz, reqVO.getBz())
            .eqIfPresent(InRecordJdsDO::getSdnjccjg, reqVO.getSdnjccjg())
            .eqIfPresent(InRecordJdsDO::getSdnjdw, reqVO.getSdnjdw())
            .eqIfPresent(InRecordJdsDO::getSdnjccsj, reqVO.getSdnjccsj())
            .eqIfPresent(InRecordJdsDO::getSdnjjcr, reqVO.getSdnjjcr())
            .eqIfPresent(InRecordJdsDO::getShid, reqVO.getShid())
            .eqIfPresent(InRecordJdsDO::getShbdzt, reqVO.getShbdzt())
            .eqIfPresent(InRecordJdsDO::getSdbdsj, reqVO.getSdbdsj())
            .eqIfPresent(InRecordJdsDO::getSfsm, reqVO.getSfsm())
            .eqIfPresent(InRecordJdsDO::getRydh, reqVO.getRydh())
            .eqIfPresent(InRecordJdsDO::getSmyy, reqVO.getSmyy())
            .eqIfPresent(InRecordJdsDO::getSmbz, reqVO.getSmbz())
            .eqIfPresent(InRecordJdsDO::getJjrq, reqVO.getJjrq())
            .eqIfPresent(InRecordJdsDO::getJjyy, reqVO.getJjyy())
            .eqIfPresent(InRecordJdsDO::getJjlqwp, reqVO.getJjlqwp())
            .eqIfPresent(InRecordJdsDO::getRslx, reqVO.getRslx())
            .eqIfPresent(InRecordJdsDO::getJbr, reqVO.getJbr())
            .eqIfPresent(InRecordJdsDO::getJbsj, reqVO.getJbsj())
            .eqIfPresent(InRecordJdsDO::getStatus, reqVO.getStatus())
            .eqIfPresent(InRecordJdsDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(InRecordJdsDO::getSpzt, reqVO.getSpzt())
            .eqIfPresent(InRecordJdsDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(InRecordJdsDO::getTaskId, reqVO.getTaskId())
            .eqIfPresent(InRecordJdsDO::getCurrentStep, reqVO.getCurrentStep())
                // 这里是你新增的部分
                .eqIfPresent(InRecordJdsDO::getTc, reqVO.getTc())
                .eqIfPresent(InRecordJdsDO::getLxdh, reqVO.getLxdh())
                .eqIfPresent(InRecordJdsDO::getTabh, reqVO.getTabh())
                .eqIfPresent(InRecordJdsDO::getBadwdh, reqVO.getBadwdh())
                .eqIfPresent(InRecordJdsDO::getBar, reqVO.getBar())
                .eqIfPresent(InRecordJdsDO::getBarlxdh, reqVO.getBarlxdh())
                .eqIfPresent(InRecordJdsDO::getJyrq, reqVO.getJyrq())
                .eqIfPresent(InRecordJdsDO::getJlrq, reqVO.getJlrq())
                .eqIfPresent(InRecordJdsDO::getDbrq, reqVO.getDbrq())
                .eqIfPresent(InRecordJdsDO::getXzhjaj, reqVO.getXzhjaj())
                .eqIfPresent(InRecordJdsDO::getSpdw, reqVO.getSpdw())
                .eqIfPresent(InRecordJdsDO::getSpr, reqVO.getSpr())
                .eqIfPresent(InRecordJdsDO::getZrdw, reqVO.getZrdw())
                .eqIfPresent(InRecordJdsDO::getZxf, reqVO.getZxf())
                .eqIfPresent(InRecordJdsDO::getXsdpzl, reqVO.getXsdpzl())
                .eqIfPresent(InRecordJdsDO::getXdfs, reqVO.getXdfs())
                .eqIfPresent(InRecordJdsDO::getSffx, reqVO.getSffx())
                .eqIfPresent(InRecordJdsDO::getZwbh, reqVO.getZwbh())
                .eqIfPresent(InRecordJdsDO::getFwbh, reqVO.getFwbh())
                .eqIfPresent(InRecordJdsDO::getXingbing, reqVO.getXingbing())
                .eqIfPresent(InRecordJdsDO::getBrdh, reqVO.getBrdh())
                .eqIfPresent(InRecordJdsDO::getJslxdh, reqVO.getJslxdh())
                .eqIfPresent(InRecordJdsDO::getQtxzcf, reqVO.getQtxzcf())
                .eqIfPresent(InRecordJdsDO::getSjbz, reqVO.getSjbz())
                .eqIfPresent(InRecordJdsDO::getSnbh, reqVO.getSnbh())
                .eqIfPresent(InRecordJdsDO::getZrjd, reqVO.getZrjd())
                .eqIfPresent(InRecordJdsDO::getAzb, reqVO.getAzb())
                .eqIfPresent(InRecordJdsDO::getDah, reqVO.getDah())
                .eqIfPresent(InRecordJdsDO::getSyqx, reqVO.getSyqx())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(InRecordJdsDO::getAddTime);
        }
        Page<InRecordJdsDO> inRecordJdsPage = selectPage(page, wrapper);
        return new PageResult<>(inRecordJdsPage.getRecords(), inRecordJdsPage.getTotal());
    }
    default List<InRecordJdsDO> selectList(InRecordJdsListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<InRecordJdsDO>()
            .eqIfPresent(InRecordJdsDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(InRecordJdsDO::getXm, reqVO.getXm())
            .eqIfPresent(InRecordJdsDO::getXmpy, reqVO.getXmpy())
            .eqIfPresent(InRecordJdsDO::getBm, reqVO.getBm())
            .eqIfPresent(InRecordJdsDO::getXb, reqVO.getXb())
            .eqIfPresent(InRecordJdsDO::getCsrq, reqVO.getCsrq())
            .eqIfPresent(InRecordJdsDO::getZjlx, reqVO.getZjlx())
            .eqIfPresent(InRecordJdsDO::getZjhm, reqVO.getZjhm())
            .eqIfPresent(InRecordJdsDO::getGj, reqVO.getGj())
            .eqIfPresent(InRecordJdsDO::getMz, reqVO.getMz())
            .eqIfPresent(InRecordJdsDO::getHyzk, reqVO.getHyzk())
            .eqIfPresent(InRecordJdsDO::getJg, reqVO.getJg())
            .eqIfPresent(InRecordJdsDO::getZjxy, reqVO.getZjxy())
            .eqIfPresent(InRecordJdsDO::getHjd, reqVO.getHjd())
            .eqIfPresent(InRecordJdsDO::getHjdxz, reqVO.getHjdxz())
            .eqIfPresent(InRecordJdsDO::getXzz, reqVO.getXzz())
            .eqIfPresent(InRecordJdsDO::getXzzxz, reqVO.getXzzxz())
            .eqIfPresent(InRecordJdsDO::getWhcd, reqVO.getWhcd())
            .eqIfPresent(InRecordJdsDO::getZzmm, reqVO.getZzmm())
            .eqIfPresent(InRecordJdsDO::getZy, reqVO.getZy())
            .eqIfPresent(InRecordJdsDO::getGzdw, reqVO.getGzdw())
            .eqIfPresent(InRecordJdsDO::getSf, reqVO.getSf())
            .eqIfPresent(InRecordJdsDO::getTssf, reqVO.getTssf())
            .eqIfPresent(InRecordJdsDO::getXxhc, reqVO.getXxhc())
            .eqIfPresent(InRecordJdsDO::getSfwxzxs, reqVO.getSfwxzxs())
            .eqIfPresent(InRecordJdsDO::getXxmc, reqVO.getXxmc())
            .eqIfPresent(InRecordJdsDO::getAjlbdm, reqVO.getAjlbdm())
            .eqIfPresent(InRecordJdsDO::getAjlb, reqVO.getAjlb())
            .eqIfPresent(InRecordJdsDO::getAjbh, reqVO.getAjbh())
            .eqIfPresent(InRecordJdsDO::getRybh, reqVO.getRybh())
            .eqIfPresent(InRecordJdsDO::getAsjxgrybh, reqVO.getAsjxgrybh())
            .eqIfPresent(InRecordJdsDO::getBazxbh, reqVO.getBazxbh())
            .eqIfPresent(InRecordJdsDO::getSjrq, reqVO.getSjrq())
            .eqIfPresent(InRecordJdsDO::getSjjglx, reqVO.getSjjglx())
            .eqIfPresent(InRecordJdsDO::getSjjgmc, reqVO.getSjjgmc())
            .eqIfPresent(InRecordJdsDO::getSjrxm, reqVO.getSjrxm())
            .eqIfPresent(InRecordJdsDO::getSjrlxdh, reqVO.getSjrlxdh())
            .eqIfPresent(InRecordJdsDO::getSjrxm2, reqVO.getSjrxm2())
            .eqIfPresent(InRecordJdsDO::getSjrlxdh2, reqVO.getSjrlxdh2())
            .eqIfPresent(InRecordJdsDO::getSjmjxm, reqVO.getSjmjxm())
            .eqIfPresent(InRecordJdsDO::getSjmjxmlxdh, reqVO.getSjmjxmlxdh())
            .eqIfPresent(InRecordJdsDO::getBadwlx, reqVO.getBadwlx())
            .eqIfPresent(InRecordJdsDO::getBadw, reqVO.getBadw())
            .eqIfPresent(InRecordJdsDO::getSjjdjglx, reqVO.getSjjdjglx())
            .eqIfPresent(InRecordJdsDO::getSjjdjgmc, reqVO.getSjjdjgmc())
            .eqIfPresent(InRecordJdsDO::getSjpz, reqVO.getSjpz())
            .eqIfPresent(InRecordJdsDO::getSjpzwsh, reqVO.getSjpzwsh())
            .eqIfPresent(InRecordJdsDO::getSjpzwsdz, reqVO.getSjpzwsdz())
            .eqIfPresent(InRecordJdsDO::getDabh, reqVO.getDabh())
            .eqIfPresent(InRecordJdsDO::getHzflwsh, reqVO.getHzflwsh())
            .eqIfPresent(InRecordJdsDO::getRsyy, reqVO.getRsyy())
            .eqIfPresent(InRecordJdsDO::getRsrq, reqVO.getRsrq())
            .eqIfPresent(InRecordJdsDO::getSjmj, reqVO.getSjmj())
            .eqIfPresent(InRecordJdsDO::getSjqx, reqVO.getSjqx())
            .eqIfPresent(InRecordJdsDO::getSjqsrq, reqVO.getSjqsrq())
            .eqIfPresent(InRecordJdsDO::getSjjzrq, reqVO.getSjjzrq())
            .eqIfPresent(InRecordJdsDO::getGllb, reqVO.getGllb())
            .eqIfPresent(InRecordJdsDO::getSfpdyj, reqVO.getSfpdyj())
            .eqIfPresent(InRecordJdsDO::getAreaId, reqVO.getAreaId())
            .likeIfPresent(InRecordJdsDO::getAreaName, reqVO.getAreaName())
            .eqIfPresent(InRecordJdsDO::getJsh, reqVO.getJsh())
            .likeIfPresent(InRecordJdsDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(InRecordJdsDO::getFpcw, reqVO.getFpcw())
            .eqIfPresent(InRecordJdsDO::getSfpbsf, reqVO.getSfpbsf())
            .eqIfPresent(InRecordJdsDO::getDrjsr, reqVO.getDrjsr())
            .eqIfPresent(InRecordJdsDO::getDrjssj, reqVO.getDrjssj())
            .eqIfPresent(InRecordJdsDO::getJyaq, reqVO.getJyaq())
            .eqIfPresent(InRecordJdsDO::getTar, reqVO.getTar())
            .eqIfPresent(InRecordJdsDO::getBz, reqVO.getBz())
            .eqIfPresent(InRecordJdsDO::getSdnjccjg, reqVO.getSdnjccjg())
            .eqIfPresent(InRecordJdsDO::getSdnjdw, reqVO.getSdnjdw())
            .eqIfPresent(InRecordJdsDO::getSdnjccsj, reqVO.getSdnjccsj())
            .eqIfPresent(InRecordJdsDO::getSdnjjcr, reqVO.getSdnjjcr())
            .eqIfPresent(InRecordJdsDO::getShid, reqVO.getShid())
            .eqIfPresent(InRecordJdsDO::getShbdzt, reqVO.getShbdzt())
            .eqIfPresent(InRecordJdsDO::getSdbdsj, reqVO.getSdbdsj())
            .eqIfPresent(InRecordJdsDO::getSfsm, reqVO.getSfsm())
            .eqIfPresent(InRecordJdsDO::getRydh, reqVO.getRydh())
            .eqIfPresent(InRecordJdsDO::getSmyy, reqVO.getSmyy())
            .eqIfPresent(InRecordJdsDO::getSmbz, reqVO.getSmbz())
            .eqIfPresent(InRecordJdsDO::getJjrq, reqVO.getJjrq())
            .eqIfPresent(InRecordJdsDO::getJjyy, reqVO.getJjyy())
            .eqIfPresent(InRecordJdsDO::getJjlqwp, reqVO.getJjlqwp())
            .eqIfPresent(InRecordJdsDO::getRslx, reqVO.getRslx())
            .eqIfPresent(InRecordJdsDO::getJbr, reqVO.getJbr())
            .eqIfPresent(InRecordJdsDO::getJbsj, reqVO.getJbsj())
            .eqIfPresent(InRecordJdsDO::getStatus, reqVO.getStatus())
            .eqIfPresent(InRecordJdsDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(InRecordJdsDO::getSpzt, reqVO.getSpzt())
            .eqIfPresent(InRecordJdsDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(InRecordJdsDO::getTaskId, reqVO.getTaskId())
            .eqIfPresent(InRecordJdsDO::getCurrentStep, reqVO.getCurrentStep())
                // 这里是你新增的部分
                .eqIfPresent(InRecordJdsDO::getTc, reqVO.getTc())
                .eqIfPresent(InRecordJdsDO::getLxdh, reqVO.getLxdh())
                .eqIfPresent(InRecordJdsDO::getTabh, reqVO.getTabh())
                .eqIfPresent(InRecordJdsDO::getBadwdh, reqVO.getBadwdh())
                .eqIfPresent(InRecordJdsDO::getBar, reqVO.getBar())
                .eqIfPresent(InRecordJdsDO::getBarlxdh, reqVO.getBarlxdh())
                .eqIfPresent(InRecordJdsDO::getJyrq, reqVO.getJyrq())
                .eqIfPresent(InRecordJdsDO::getJlrq, reqVO.getJlrq())
                .eqIfPresent(InRecordJdsDO::getDbrq, reqVO.getDbrq())
                .eqIfPresent(InRecordJdsDO::getXzhjaj, reqVO.getXzhjaj())
                .eqIfPresent(InRecordJdsDO::getSpdw, reqVO.getSpdw())
                .eqIfPresent(InRecordJdsDO::getSpr, reqVO.getSpr())
                .eqIfPresent(InRecordJdsDO::getZrdw, reqVO.getZrdw())
                .eqIfPresent(InRecordJdsDO::getZxf, reqVO.getZxf())
                .eqIfPresent(InRecordJdsDO::getXsdpzl, reqVO.getXsdpzl())
                .eqIfPresent(InRecordJdsDO::getXdfs, reqVO.getXdfs())
                .eqIfPresent(InRecordJdsDO::getSffx, reqVO.getSffx())
                .eqIfPresent(InRecordJdsDO::getZwbh, reqVO.getZwbh())
                .eqIfPresent(InRecordJdsDO::getFwbh, reqVO.getFwbh())
                .eqIfPresent(InRecordJdsDO::getXingbing, reqVO.getXingbing())
                .eqIfPresent(InRecordJdsDO::getBrdh, reqVO.getBrdh())
                .eqIfPresent(InRecordJdsDO::getJslxdh, reqVO.getJslxdh())
                .eqIfPresent(InRecordJdsDO::getQtxzcf, reqVO.getQtxzcf())
                .eqIfPresent(InRecordJdsDO::getSjbz, reqVO.getSjbz())
                .eqIfPresent(InRecordJdsDO::getSnbh, reqVO.getSnbh())
                .eqIfPresent(InRecordJdsDO::getZrjd, reqVO.getZrjd())
                .eqIfPresent(InRecordJdsDO::getAzb, reqVO.getAzb())
                .eqIfPresent(InRecordJdsDO::getDah, reqVO.getDah())
                .eqIfPresent(InRecordJdsDO::getSyqx, reqVO.getSyqx())
        .orderByDesc(InRecordJdsDO::getAddTime));    }

    @InterceptorIgnore(illegalSql = "true")
    InRecordStatusVO getInRecordStatus(String rybh);

    void syncDataToPrisonerJdsIn(String id);
}
