package com.rs.module.acp.service.gj.assistancesolvecase;

import com.alibaba.fastjson.JSONObject;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.acp.controller.admin.gj.vo.assistancesolvecase.*;
import com.rs.module.acp.enums.gj.AssistanceSolveCaseStatusEnum;
import com.rs.module.base.util.BspApprovalUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.AssistanceSolveCaseDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.AssistanceSolveCaseDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-协助破案 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class AssistanceSolveCaseServiceImpl extends BaseServiceImpl<AssistanceSolveCaseDao, AssistanceSolveCaseDO> implements AssistanceSolveCaseService {

    @Resource
    private AssistanceSolveCaseDao assistanceSolveCaseDao;

    // 单位领导审批流程
    private static final String defKey = "yonghudanweilindao";

    @Override
    public String createAssistanceSolveCase(AssistanceSolveCaseSaveReqVO createReqVO) {
        // 插入
        AssistanceSolveCaseDO assistanceSolveCase = BeanUtils.toBean(createReqVO, AssistanceSolveCaseDO.class);
        assistanceSolveCaseDao.insert(assistanceSolveCase);
        // 返回
        return assistanceSolveCase.getId();
    }

    @Override
    public void updateAssistanceSolveCase(AssistanceSolveCaseSaveReqVO updateReqVO) {
        // 校验存在
        validateAssistanceSolveCaseExists(updateReqVO.getId());
        // 更新
        AssistanceSolveCaseDO updateObj = BeanUtils.toBean(updateReqVO, AssistanceSolveCaseDO.class);
        assistanceSolveCaseDao.updateById(updateObj);
    }

    @Override
    public void deleteAssistanceSolveCase(String id) {
        // 校验存在
        validateAssistanceSolveCaseExists(id);
        // 删除
        assistanceSolveCaseDao.deleteById(id);
    }

    private void validateAssistanceSolveCaseExists(String id) {
        if (assistanceSolveCaseDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-协助破案数据不存在");
        }
    }

    @Override
    public AssistanceSolveCaseDO getAssistanceSolveCase(String id) {
        return assistanceSolveCaseDao.selectById(id);
    }

    @Override
    public PageResult<AssistanceSolveCaseDO> getAssistanceSolveCasePage(AssistanceSolveCasePageReqVO pageReqVO) {
        return assistanceSolveCaseDao.selectPage(pageReqVO);
    }

    @Override
    public List<AssistanceSolveCaseDO> getAssistanceSolveCaseList(AssistanceSolveCaseListReqVO listReqVO) {
        return assistanceSolveCaseDao.selectList(listReqVO);
    }

    // 线索登记
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String clueRegist(AssistanceSolveCaseClueRegistReqVO createReqVO) {
        // 插入
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        createReqVO.setClueRegistUser(sessionUser.getName());
        createReqVO.setClueRegistUserSfzh(sessionUser.getIdCard());
        createReqVO.setClueRegistTime(new Date());

        AssistanceSolveCaseDO assistanceSolveCase = BeanUtils.toBean(createReqVO, AssistanceSolveCaseDO.class);
        assistanceSolveCase.setStatus(AssistanceSolveCaseStatusEnum.DSH.getCode());
        assistanceSolveCaseDao.insert(assistanceSolveCase);

        //启动流程审批
        String msgUrl = "/#/discipline/assistInSolvingCases/check?id=" + assistanceSolveCase.getId() + "&jgrybm=" + createReqVO.getJgrybm() + "&status=01";
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", assistanceSolveCase.getId());
        JSONObject result = BspApprovalUtil.defaultStartProcess(HttpUtils.getAppCode() + "-" + defKey, assistanceSolveCase.getId(), "协助破案流程审批", msgUrl, variables);
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            assistanceSolveCase.setActInstId(bpmTrail.getString("actInstId"));
            assistanceSolveCase.setTaskId(bpmTrail.getString("taskId"));
            assistanceSolveCaseDao.updateById(assistanceSolveCase);
        } else {
            throw new ServerException("流程启动失败");
        }
        // 返回
        return assistanceSolveCase.getId();
    }

    @Override
    public void clueApproval(AssistanceSolveCaseClueApprovalReqVO approvalReqVO) {
        AssistanceSolveCaseDO assistanceSolveCaseDO = assistanceSolveCaseDao.selectById(approvalReqVO.getId());
        if (Objects.isNull(assistanceSolveCaseDO)) {
            throw new ServerException("实战平台-管教业务-协助破案数据不存在");
        }
        if (!AssistanceSolveCaseStatusEnum.DSH.getCode().equals(assistanceSolveCaseDO.getStatus())) {
            throw new ServerException("非待审批状态，不能进行审核操作");
        }
        assistanceSolveCaseDO.setStatus(AssistanceSolveCaseStatusEnum.getByCode(approvalReqVO.getStatus()).getCode());
        assistanceSolveCaseDO.setApproverTime(new Date());
        assistanceSolveCaseDO.setApprovalComments(approvalReqVO.getApprovalComments());
        assistanceSolveCaseDO.setApprovalResult(approvalReqVO.getStatus());
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        assistanceSolveCaseDO.setApproverSfzh(sessionUser.getIdCard());
        assistanceSolveCaseDO.setApproverXm(sessionUser.getName());
        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(assistanceSolveCaseDO.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }

        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", assistanceSolveCaseDO.getId());
        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getId());
        nowApproveUser.put("name", sessionUser.getName());
        BspApproceStatusEnum bspApproceStatusEnum = AssistanceSolveCaseStatusEnum.DZD.getCode().equals(assistanceSolveCaseDO.getStatus()) ?
                BspApproceStatusEnum.PASSED_END : BspApproceStatusEnum.NOT_PASSED_END;
        JSONObject result = BspApprovalUtil.approvalProcess(HttpUtils.getAppCode() + "-" + defKey,
                assistanceSolveCaseDO.getActInstId(), assistanceSolveCaseDO.getTaskId(), assistanceSolveCaseDO.getId(),
                bspApproceStatusEnum, assistanceSolveCaseDO.getApprovalComments(), null, null, true,
                variables, nowApproveUser, "acp");
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            assistanceSolveCaseDO.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程启动失败");
        }
        assistanceSolveCaseDao.updateById(assistanceSolveCaseDO);
    }

    @Override
    public void clueForward(AssistanceSolveCaseForwardReqVO caseForwardReqVO) {
        AssistanceSolveCaseDO assistanceSolveCaseDO = assistanceSolveCaseDao.selectById(caseForwardReqVO.getId());
        if (Objects.isNull(assistanceSolveCaseDO)) {
            throw new ServerException("实战平台-管教业务-协助破案数据不存在");
        }
        if (!AssistanceSolveCaseStatusEnum.DZD.getCode().equals(assistanceSolveCaseDO.getStatus())) {
            throw new ServerException("非待转递状态，不能进行转递操作");
        }
        assistanceSolveCaseDO.setStatus(AssistanceSolveCaseStatusEnum.DFK.getCode());
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        assistanceSolveCaseDO.setForwardType(caseForwardReqVO.getForwardType());
        assistanceSolveCaseDO.setForwardName(caseForwardReqVO.getForwardName());
        assistanceSolveCaseDO.setForwardTime(caseForwardReqVO.getForwardTime());
        assistanceSolveCaseDO.setForwardUrl(caseForwardReqVO.getForwardUrl());
        assistanceSolveCaseDO.setForwardUser(sessionUser.getName());
        assistanceSolveCaseDO.setForwardUserSfzh(sessionUser.getIdCard());
        assistanceSolveCaseDO.setForwardUserTime(new Date());
        assistanceSolveCaseDao.updateById(assistanceSolveCaseDO);

    }

    @Override
    public void clueFeedback(AssistanceSolveCaseFeedbackReqVO feedbackReqVO) {
        AssistanceSolveCaseDO assistanceSolveCaseDO = assistanceSolveCaseDao.selectById(feedbackReqVO.getId());
        if (Objects.isNull(assistanceSolveCaseDO)) {
            throw new ServerException("实战平台-管教业务-协助破案数据不存在");
        }
        if (!AssistanceSolveCaseStatusEnum.DFK.getCode().equals(assistanceSolveCaseDO.getStatus())) {
            throw new ServerException("非待反馈状态，不能进行反馈操作");
        }
        assistanceSolveCaseDO.setStatus(AssistanceSolveCaseStatusEnum.YWJ.getCode());

        assistanceSolveCaseDO.setFeedbackReceiptType(feedbackReqVO.getFeedbackReceiptType());
        assistanceSolveCaseDO.setFeedbackReceiptName(feedbackReqVO.getFeedbackReceiptName());
        assistanceSolveCaseDO.setFeedbackReceiptTime(feedbackReqVO.getFeedbackReceiptTime());
        assistanceSolveCaseDO.setFeedbackTime(feedbackReqVO.getFeedbackTime());
        assistanceSolveCaseDO.setFeedbackCzqk(feedbackReqVO.getFeedbackCzqk());
        assistanceSolveCaseDO.setFeedbackPhajsl(feedbackReqVO.getFeedbackPhajsl());
        assistanceSolveCaseDO.setFeedbackZhwffzxyrsl(feedbackReqVO.getFeedbackZhwffzxyrsl());
        assistanceSolveCaseDO.setFeedbackFxztrysl(feedbackReqVO.getFeedbackFxztrysl());
        assistanceSolveCaseDO.setFeedbackJhzkzwsl(feedbackReqVO.getFeedbackJhzkzwsl());
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        assistanceSolveCaseDO.setFeedbackUser(sessionUser.getName());
        assistanceSolveCaseDO.setFeedbackUserSfzh(sessionUser.getIdCard());
        assistanceSolveCaseDO.setFeedbackUserTime(new Date());
        assistanceSolveCaseDao.updateById(assistanceSolveCaseDO);
    }


}
