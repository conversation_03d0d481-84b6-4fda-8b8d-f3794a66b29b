package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.exception.ServerException;
import com.rs.module.acp.entity.gj.InOutRecordsDO;
import com.rs.util.DicUtils;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsSaveReqVO;
import com.rs.module.acp.controller.admin.wb.vo.WbInspectionResultsSaveReqVO;
import com.rs.module.acp.dao.wb.WbCommonDao;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.acp.service.gj.InOutRecordsService;
import com.rs.module.base.controller.admin.pm.vo.AreaInfoRespVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.sys.MsgAddVO;
import com.rs.module.base.service.pm.AreaService;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.base.util.MsgUtil;
import com.rs.module.oss.entity.FileDetail;
import com.rs.module.oss.service.FileDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Validated
public class WbCommonServiceImpl implements WbCommonService {

    @Resource
    private WbCommonDao wbCommonDao;

    @Autowired
    private FileDetailService fileDetailService;

    @Autowired
    private PrisonerService prisonerService;

    @Autowired
    @Lazy
    private InOutRecordsService inOutRecordsService;

    @Autowired
    private AreaService areaService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveFile(String objectId,String fileJson) {
        String returnObjectId = null;
        //先删除
        if(ObjectUtil.isNotEmpty(objectId)){
            fileDetailService.remove(new LambdaQueryWrapper<FileDetail>().eq(FileDetail::getObjectId,objectId));
            returnObjectId = objectId;
        }else {
            returnObjectId = StringUtil.getGuid32();
        }

        if(ObjectUtil.isEmpty(fileJson) || "[]".equals(fileJson)){
            return null;
        }
        List<FileDetail> fileDetailList = new ArrayList<>();
        List<JSONObject> fileJsonList = JSONArray.parseArray(fileJson,JSONObject.class);
        for(JSONObject jsonObject:fileJsonList){
            FileDetail fileDetail = new FileDetail();
            fileDetail.setId(StringUtil.getGuid32());
            fileDetail.setObjectId(returnObjectId);
            fileDetail.setFilename(jsonObject.getString("fileName"));
            fileDetail.setSize(jsonObject.getLong("fileSize"));
            fileDetail.setContentType(jsonObject.getString("fileType"));
            fileDetail.setExt(jsonObject.getString("fileSuffix"));
            fileDetail.setUrl(jsonObject.getString("objectName"));
            fileDetail.setMetadata(jsonObject.toJSONString());
            fileDetailList.add(fileDetail);
        }
        fileDetailService.saveBatch(fileDetailList);
        return returnObjectId;
    }

    @Override
    public String getFile(String objectId) {
        List<FileDetail> fileDetailList = fileDetailService.list(new LambdaQueryWrapper<FileDetail>().eq(FileDetail::getObjectId,objectId));
        if(CollectionUtil.isEmpty(fileDetailList)){
            return null;
        }
        List<JSONObject> fileJsonList = new ArrayList<>();
        try {
            fileJsonList = fileDetailList.stream().map(x->{
                return JSONObject.parseObject(x.getMetadata());
            }).collect(Collectors.toList());
        }catch (Exception e){
            e.printStackTrace();
        }
        return JSON.toJSONString(fileJsonList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFile(String objectId) {
        return fileDetailService.remove(new LambdaQueryWrapper<FileDetail>().eq(FileDetail::getObjectId,objectId));
    }

    @Override
    public void saveInOutRecord(WbInspectionResultsSaveReqVO inspectionResultsVO, String businessType, String type,String status,boolean isAdditional) {
        InOutRecordsSaveReqVO inOutRecordsSaveReqVO = new InOutRecordsSaveReqVO();
        PrisonerVwRespVO vwRespVO = prisonerService.getPrisonerByJgrybm(inspectionResultsVO.getJgrybm());

        if("out".equals(type) && "3".equals(status)){
            inOutRecordsSaveReqVO = BeanUtils.toBean(inspectionResultsVO,InOutRecordsSaveReqVO.class);

            inOutRecordsSaveReqVO.setInoutPolice(inspectionResultsVO.getEscortingPolice());
            inOutRecordsSaveReqVO.setInoutPoliceSfzh(inspectionResultsVO.getEscortingPoliceSfzh());
            inOutRecordsSaveReqVO.setInoutTime(inspectionResultsVO.getEscortingTime());

            inOutRecordsSaveReqVO.setInspector(inspectionResultsVO.getInspector());
            inOutRecordsSaveReqVO.setInspectorSfzh(inspectionResultsVO.getInspectorSfzh());
            inOutRecordsSaveReqVO.setInspectionTime(inspectionResultsVO.getInspectionTime());

            inOutRecordsSaveReqVO.setInoutType("01");
        }else if("in".equals(type) && "3".equals(status)) {
            inOutRecordsSaveReqVO.setInspectionResult(inspectionResultsVO.getReturnInspectionResult());
            inOutRecordsSaveReqVO.setProhibitedItems(inspectionResultsVO.getReturnProhibitedItems());
            inOutRecordsSaveReqVO.setPhysicalExam(inspectionResultsVO.getReturnPhysicalExam());
            inOutRecordsSaveReqVO.setAbnormalSituations(inspectionResultsVO.getReturnAbnormalSituations());

            inOutRecordsSaveReqVO.setInoutPolice(inspectionResultsVO.getReturnPolice());
            inOutRecordsSaveReqVO.setInoutPoliceSfzh(inspectionResultsVO.getReturnPoliceSfzh());
            inOutRecordsSaveReqVO.setInoutTime(inspectionResultsVO.getReturnTime());

            inOutRecordsSaveReqVO.setInspector(inspectionResultsVO.getReturnInspector());
            inOutRecordsSaveReqVO.setInspectorSfzh(inspectionResultsVO.getReturnInspectorSfzh());
            inOutRecordsSaveReqVO.setInspectionTime(inspectionResultsVO.getReturnInspectionTime());

            inOutRecordsSaveReqVO.setInoutType("02");
        }else if("out".equals(type) && "0".equals(status)){
            inOutRecordsSaveReqVO.setInoutTime(inspectionResultsVO.getArraignmentStartTime());
            inOutRecordsSaveReqVO.setInoutType("01");
        }

        inOutRecordsSaveReqVO.setBusinessId(inspectionResultsVO.getId());
        inOutRecordsSaveReqVO.setBusinessType(businessType);
        inOutRecordsSaveReqVO.setDataSources("1");
        inOutRecordsSaveReqVO.setJgrybm(inspectionResultsVO.getJgrybm());
        inOutRecordsSaveReqVO.setJgryxm(vwRespVO.getXm());
        inOutRecordsSaveReqVO.setRoomId(vwRespVO.getJsh());

        inOutRecordsSaveReqVO.setSupervisorPolice(ObjectUtil.isNotEmpty(vwRespVO.getZgmjName())?vwRespVO.getZgmjName():"无法获取管教民警");
        inOutRecordsSaveReqVO.setSupervisorPoliceSfzh(ObjectUtil.isNotEmpty(vwRespVO.getZgmjSfzh())?vwRespVO.getZgmjSfzh():"无法获取管教民警身份证号");
        //inOutRecordsSaveReqVO.setInoutReason("11");
        inOutRecordsSaveReqVO.setId(null);
        inOutRecordsSaveReqVO.setStatus(status);
//        return inOutRecordsService.acpSaveInOutRecords(inOutRecordsSaveReqVO,vwRespVO.getJsh(),inOutRecordsSaveReqVO.getInoutTime());
//        return inOutRecordsService.createInOutRecords(inOutRecordsSaveReqVO);

        InOutRecordsDO inOutRecords = BeanUtils.toBean(inOutRecordsSaveReqVO, InOutRecordsDO.class);

        if(!isAdditional){
            if("0".equals(status) && "out".equals(type)){
                //待带出
                inOutRecordsService.save(inOutRecords);
            }else if("3".equals(status) && "out".equals(type)){

                LambdaQueryWrapper<InOutRecordsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.select(InOutRecordsDO::getId);
                lambdaQueryWrapper.eq(InOutRecordsDO::getBusinessId,inOutRecords.getBusinessId())
                        .eq(InOutRecordsDO::getBusinessType,inOutRecords.getBusinessType())
                        .eq(InOutRecordsDO::getInoutType,inOutRecords.getInoutType());
                InOutRecordsDO inOutRecordsDO = inOutRecordsService.getOne(lambdaQueryWrapper);

                //在pc端做了带出安检
                LambdaUpdateWrapper<InOutRecordsDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.eq(InOutRecordsDO::getBusinessId,inspectionResultsVO.getId())
                        .eq(InOutRecordsDO::getBusinessType,businessType)
                        .eq(InOutRecordsDO::getInoutType,type)
                        .set(InOutRecordsDO::getStatus,status)
                        .set(InOutRecordsDO::getInspectionResult,inspectionResultsVO.getInspectionResult())
                        .set(InOutRecordsDO::getPhysicalExam,inspectionResultsVO.getPhysicalExam())
                        .set(InOutRecordsDO::getProhibitedItems,inspectionResultsVO.getProhibitedItems())
                        .set(InOutRecordsDO::getAbnormalSituations,inspectionResultsVO.getAbnormalSituations())
                        .set(InOutRecordsDO::getInspectionTime,inspectionResultsVO.getInspectionTime())
                        .set(InOutRecordsDO::getInspector,inspectionResultsVO.getInspector())
                        .set(InOutRecordsDO::getInspectorSfzh,inspectionResultsVO.getInspectorSfzh());
                inOutRecordsService.update(lambdaUpdateWrapper);
                //然后再写入一个待带入的
                inOutRecords.setStatus("0");
                inOutRecords.setInoutType("02");
                //这里是预计带入时间
                inOutRecords.setInoutTime(inspectionResultsVO.getApplMeetingTime());
                inOutRecords.setOutBusinessId(ObjectUtil.isNotEmpty(inOutRecordsDO)?inOutRecordsDO.getId():null);
                inOutRecords.setId(StringUtil.getGuid32());
                inOutRecords.setInspectionResult(null);
                inOutRecords.setPhysicalExam(null);
                inOutRecords.setProhibitedItems(null);
                inOutRecords.setAbnormalSituations(null);
                inOutRecords.setInspectionTime(null);
                inOutRecords.setInspector(null);
                inOutRecords.setInspectorSfzh(null);
                inOutRecordsService.save(inOutRecords);
            }else if("3".equals(status) && "in".equals(type)){
                LambdaUpdateWrapper<InOutRecordsDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.eq(InOutRecordsDO::getBusinessId,inspectionResultsVO.getId())
                        .eq(InOutRecordsDO::getBusinessType,businessType)
                        .eq(InOutRecordsDO::getInoutType,type)
                        .set(InOutRecordsDO::getStatus,status)
                        .set(InOutRecordsDO::getInspectionResult,inspectionResultsVO.getReturnInspectionResult())
                        .set(InOutRecordsDO::getPhysicalExam,inspectionResultsVO.getReturnPhysicalExam())
                        .set(InOutRecordsDO::getProhibitedItems,inspectionResultsVO.getReturnProhibitedItems())
                        .set(InOutRecordsDO::getAbnormalSituations,inspectionResultsVO.getReturnAbnormalSituations())
                        .set(InOutRecordsDO::getInspectionTime,inspectionResultsVO.getReturnInspectionTime())
                        .set(InOutRecordsDO::getInspector,inspectionResultsVO.getReturnInspector())
                        .set(InOutRecordsDO::getInspectorSfzh,inspectionResultsVO.getReturnInspectorSfzh())
                        .set(InOutRecordsDO::getInoutTime,inspectionResultsVO.getApplMeetingTime());
                inOutRecordsService.update(lambdaUpdateWrapper);
            }
        }else {
            inOutRecordsService.save(inOutRecords);
        }

    }

//    @Override
//    public void saveInOutRecord(WbInspectionResultsSaveReqVO inspectionResultsVO, String businessType, String type, String status, boolean isAdditional) {
//
//    }

    @Override
    public void registrationReminder(JSONObject targerObject,String type) {
        MsgAddVO msgAddVO = new MsgAddVO();
        Map<String, Object> contentData = new HashMap<>();

        PrisonerVwRespVO prisonerVwRespVO = prisonerService.getPrisonerByJgrybm(targerObject.getString("jgrybm"));

        msgAddVO.setBusinessId(targerObject.getString("id"));
        msgAddVO.setJgrybm(targerObject.getString("jgrybm"));
        msgAddVO.setPcid(targerObject.getString("id"));

        contentData.put("roomName",prisonerVwRespVO.getRoomName());
        contentData.put("xm",prisonerVwRespVO.getXm());
        contentData.put("tjjgmc",targerObject.getString("tjjgmc"));

        String time = null;

        if(WbConstants.BUSINESS_TYPE_ARRAIGNMENT.equals(type)){
            //提讯
            msgAddVO.setMsgType("102_001");
            msgAddVO.setModuleCode("ACP_WB_TX_KSS_HJTX");
            msgAddVO.setUrl(String.format("/Inquiry/list?saveType=info&curId=%s",targerObject.getString("id")));
             time = DateUtil.format(targerObject.getDate("startApplyArraignmentTime"),"yyyy-MM-dd HH:mm:ss")
                    +"~"+DateUtil.format(targerObject.getDate("endApplyArraignmentTime"),"HH:mm:ss");

        }else if(WbConstants.BUSINESS_TYPE_BRING_INTERROGATION.equals(type)){
            //提询
            msgAddVO.setMsgType("102_002");
            msgAddVO.setModuleCode("ACP_WB_TX_KSS_HJTX");
            msgAddVO.setUrl(String.format(" /InquiryJls/list?curId=%s&saveType=info",targerObject.getString("id")));
            time = DateUtil.format(targerObject.getDate("startApplyArraignmentTime"),"yyyy-MM-dd HH:mm:ss")
                    +"~"+DateUtil.format(targerObject.getDate("endApplyArraignmentTime"),"HH:mm:ss");

        }else if(WbConstants.BUSINESS_TYPE_ESCORT.equals(type)){
            //提解
            msgAddVO.setMsgType("102_003");
            msgAddVO.setModuleCode("ACP_WB_TJ_KSS_HJTX");
            msgAddVO.setUrl(String.format("/window/provideSolutions?saveType=info&curId=%s",targerObject.getString("id")));
            time = DateUtil.format(targerObject.getDate("applyEscortDate"),"yyyy-MM-dd");

        }else if(WbConstants.BUSINESS_TYPE_LAWYER_MEETING.equals(type)){
            //律师会见
            msgAddVO.setMsgType("102_004");
            msgAddVO.setModuleCode("ACP_WB_LSHJ_HJTX");
            msgAddVO.setUrl(String.format("/lawyer/lawyerMeeting?curId=%s&saveType=detail",targerObject.getString("id")));
            time = DateUtil.format(targerObject.getDate("applyMeetingStartTime"),"yyyy-MM-dd HH:mm:ss")
                    +"~"+DateUtil.format(targerObject.getDate("applyMeetingEndTime"),"HH:mm:ss");
        }else if(WbConstants.BUSINESS_TYPE_FAMILY_MEETING.equals(type)){
            //家属会见
            msgAddVO.setMsgType("102_006");
            msgAddVO.setModuleCode("ACP_WB_JSHJ_KSS_HJTX");
            msgAddVO.setUrl("");
            time = DateUtil.format(targerObject.getDate("applyMeetingStartTime"),"yyyy-MM-dd HH:mm:ss")
                    +"~"+DateUtil.format(targerObject.getDate("applyMeetingEndTime"),"HH:mm:ss");
        }else if(WbConstants.BUSINESS_TYPE_CONSULAR_MEETING.equals(type)){
            //领事会见
            msgAddVO.setMsgType("102_005");
            msgAddVO.setModuleCode("ACP_WB_SGHJ_KSS_HJTX");
            msgAddVO.setUrl(String.format("/window/consulMeeting?curId=%s&saveType=detail",targerObject.getString("id")));
            time = DateUtil.format(targerObject.getDate("applyMeetingStartTime"),"yyyy-MM-dd HH:mm:ss")
                    +"~"+DateUtil.format(targerObject.getDate("applyMeetingEndTime"),"HH:mm:ss");
        }else if(WbConstants.BUSINESS_TYPE_FAMILY_MEETING_VIDEO.equals(type)){
            //家属单向视频会见
            msgAddVO.setMsgType("102_007");
            msgAddVO.setModuleCode("ACP_WB_JSDXSPHJ_KSS_HJTX");
            msgAddVO.setUrl("");
            time = DateUtil.format(targerObject.getDate("notificationMeetingDate"),"yyyy-MM-dd HH:mm:ss");
        }
        contentData.put("time",time);
        msgAddVO.setContentData(contentData);
        MsgUtil.sendMsg(msgAddVO);
    }

    @Override
    public void additionalRecordingReminder(JSONObject targerObject, String type) {
        MsgAddVO msgAddVO = new MsgAddVO();
        Map<String, Object> contentData = new HashMap<>();

        PrisonerVwRespVO prisonerVwRespVO = prisonerService.getPrisonerByJgrybm(targerObject.getString("jgrybm"));

        msgAddVO.setBusinessId(targerObject.getString("id"));
        msgAddVO.setJgrybm(targerObject.getString("jgrybm"));
        msgAddVO.setPcid(targerObject.getString("id"));

        contentData.put("roomName",prisonerVwRespVO.getRoomName());
        contentData.put("xm",prisonerVwRespVO.getXm());
        contentData.put("tjjgmc",targerObject.getString("tjjgmc"));

        String time = null;

        if(WbConstants.BUSINESS_TYPE_ARRAIGNMENT.equals(type)){
            //提讯
            msgAddVO.setMsgType("102_001");
            msgAddVO.setModuleCode("ACP_WB_TX_KSS_BLTX");
            msgAddVO.setUrl(String.format("/Inquiry/list?saveType=supplementary&curId=%s",targerObject.getString("id")));
            time = DateUtil.format(targerObject.getDate("startApplyArraignmentTime"),"yyyy-MM-dd HH:mm:ss")
                    +"~"+DateUtil.format(targerObject.getDate("endApplyArraignmentTime"),"HH:mm:ss");

        }else if(WbConstants.BUSINESS_TYPE_BRING_INTERROGATION.equals(type)){
            //提询
            msgAddVO.setMsgType("102_002");
            msgAddVO.setModuleCode("ACP_WB_TX_KSS_BLTX");
            msgAddVO.setUrl(String.format("/InquiryJls/list?curId=%s&saveType=supplementary",targerObject.getString("id")));
            time = DateUtil.format(targerObject.getDate("startApplyArraignmentTime"),"yyyy-MM-dd HH:mm:ss")
                    +"~"+DateUtil.format(targerObject.getDate("endApplyArraignmentTime"),"HH:mm:ss");
        }else if(WbConstants.BUSINESS_TYPE_ESCORT.equals(type)){
            //提解
            msgAddVO.setMsgType("102_003");
            msgAddVO.setModuleCode("ACP_WB_TJ_KSS_BLTX");
            msgAddVO.setUrl(String.format("/window/provideSolutions?saveType=supplementary&curId=%s",targerObject.getString("id")));
            time = DateUtil.format(targerObject.getDate("applyEscortDate"),"yyyy-MM-dd");

        }else if(WbConstants.BUSINESS_TYPE_LAWYER_MEETING.equals(type)){
            //律师会见
            msgAddVO.setMsgType("102_004");
            msgAddVO.setModuleCode("ACP_WB_LSHJ_BLTX");
            msgAddVO.setUrl(String.format("/window/lawyerMeeting?curId=%s&saveType=additionalRecording",targerObject.getString("id")));
            time = DateUtil.format(targerObject.getDate("applyMeetingStartTime"),"yyyy-MM-dd HH:mm:ss")
                    +"~"+DateUtil.format(targerObject.getDate("applyMeetingEndTime"),"HH:mm:ss");

        }else if(WbConstants.BUSINESS_TYPE_FAMILY_MEETING.equals(type)){
            //家属会见
            msgAddVO.setMsgType("102_006");
            msgAddVO.setModuleCode("ACP_WB_JSHJ_KSS_BLTX");
            msgAddVO.setUrl(String.format("/familyMeeting/faceToFace?curId=%s&saveType=additionalRecording",targerObject.getString("id")));
            time = DateUtil.format(targerObject.getDate("applyMeetingStartTime"),"yyyy-MM-dd HH:mm:ss")
                    +"~"+DateUtil.format(targerObject.getDate("applyMeetingEndTime"),"HH:mm:ss");
        }else if(WbConstants.BUSINESS_TYPE_CONSULAR_MEETING.equals(type)){
            //领事会见
            msgAddVO.setMsgType("102_005");
            msgAddVO.setModuleCode("ACP_WB_SGHJ_KSS_BLTX");
            msgAddVO.setUrl(String.format("/window/consulMeeting?curId=%s&saveType=additionalRecording",targerObject.getString("id")));
            time = DateUtil.format(targerObject.getDate("applyMeetingStartTime"),"yyyy-MM-dd HH:mm:ss")
                    +"~"+DateUtil.format(targerObject.getDate("applyMeetingEndTime"),"HH:mm:ss");
        }else if(WbConstants.BUSINESS_TYPE_FAMILY_MEETING_VIDEO.equals(type)){
            //家属单向视频会见
            msgAddVO.setMsgType("102_007");
            msgAddVO.setModuleCode("ACP_WB_JSDXSPHJ_KSS_BLTX");
            msgAddVO.setUrl(String.format("/familyMeeting/videoConference?curId=%s&saveType=additionalRecording",targerObject.getString("id")));
            time = DateUtil.format(targerObject.getDate("notificationMeetingDate"),"yyyy-MM-dd");
            return;
        }
        contentData.put("time",time);
        msgAddVO.setContentData(contentData);
        MsgUtil.sendMsg(msgAddVO);
    }

    @Override
    public List<JSONObject> getIdleInterrogationRoom(boolean isAll) {

        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        //2025-06-04 需求变动：提讯/询、家属会见、使馆会见：审讯室的忙闲状态判断：若会议室已被分配，则从分配时起，则状态为【使用中】，当会毕带回后，则状态变更为【空闲】；

        List<AreaInfoRespVO> areaList = areaService.getAreaByOrgCode(sessionUser.getOrgCode(),"0007");

        //查询已分配会见、但是又未处于完结状态的数据

        List<JSONObject> arraignmentList = wbCommonDao.getArraignmentRegistration(sessionUser.getOrgCode());

        Map<String,String> map = new HashMap<>();
        for(JSONObject arraignment:arraignmentList){
            map.put(arraignment.getString("room_id"),arraignment.getString("room_id"));
        }

        List<JSONObject> resList = new ArrayList<>();
        for(AreaInfoRespVO area:areaList){
            JSONObject temp = new JSONObject();
            temp.put("roomId",area.getAreaCode());
            temp.put("roomName",area.getAreaName());
            boolean idle = true;
            if(map.containsKey(area.getAreaCode())){
                idle = false;
            }else {
                idle = true;
            }
            temp.put("idle",idle);
            if(isAll){
                resList.add(temp);
            }else {
                if(idle){
                    resList.add(temp);
                }
            }
        }
        return resList;
    }

    @Override
    public List<JSONObject> getIdleLawyerMeetingRoom(String id,String type) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        List<AreaInfoRespVO> areaList = new ArrayList<>();

        JSONObject meetJson = new JSONObject();
        if(WbConstants.BUSINESS_TYPE_LAWYER_MEETING.equals(type)){
            meetJson = wbCommonDao.getLawyerMeetingById(id);
            areaList = areaService.getAreaByOrgCode(sessionUser.getOrgCode(), "0016");
        }else {
            meetJson = wbCommonDao.getConsularMeetingById(id);
            areaList = areaService.getAreaByOrgCode(sessionUser.getOrgCode(), "0040");
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        //开始时间、结束时间
        String startTimeStr = sdf.format(meetJson.getDate("apply_meeting_start_time"));
        LocalDateTime startTime = LocalDateTime.parse(startTimeStr, formatter);
        LocalDateTime endTime = LocalDateTime.parse(sdf.format(meetJson.getDate("apply_meeting_end_time")), formatter);
        List<JSONObject> meetingList = new ArrayList<>();
        if(WbConstants.BUSINESS_TYPE_LAWYER_MEETING.equals(type)){
             meetingList = wbCommonDao.getLawyerMeetingList(startTimeStr);
        }else {
            meetingList = wbCommonDao.getConsularMeetingList(startTimeStr);
        }

        //如果申请的时间与 会见室的时间有重叠，则认为会见室被占用
        Map<String, List<JSONObject>> roomMap = new HashMap<>();

        for (JSONObject meeting : meetingList) {
            List<JSONObject> tempList = new ArrayList<>();
            JSONObject temp = new JSONObject();
            temp.put("startTime", meeting.getDate("apply_meeting_start_time"));
            temp.put("endTime", meeting.getDate("apply_meeting_end_time"));
            if (roomMap.containsKey(meeting.getString("room_id"))) {
                tempList = roomMap.get(meeting.getString("room_id"));
                tempList.add(temp);
            } else {
                tempList.add(temp);
            }
            roomMap.put(meeting.getString("room_id"), tempList);
        }
        //规则：判断时间是否有重叠，如果时间有重叠，则认为该段时间，审讯室不空闲
        List<JSONObject> resList = new ArrayList<>();
        for (AreaInfoRespVO area : areaList) {
            JSONObject temp = new JSONObject();
            temp.put("roomId", area.getAreaCode());
            temp.put("roomName", area.getAreaName());
            boolean idle = true;
            if (roomMap.containsKey(area.getAreaCode())) {
                List<JSONObject> tempList = roomMap.get(area.getAreaCode());
                for (JSONObject arraignmentJson : tempList) {
                    if (ObjectUtil.isEmpty(arraignmentJson.getDate("startTime")) || ObjectUtil.isEmpty(arraignmentJson.getDate("endTime"))) {
                        //存在时间为空，则跳过本次判断
                        continue;
                    }
                    LocalDateTime compareStartTime = LocalDateTime.parse(sdf.format(arraignmentJson.getDate("startTime")), formatter);
                    LocalDateTime compareEndTime = LocalDateTime.parse(sdf.format(arraignmentJson.getDate("endTime")), formatter);
                    boolean isOverlapping = !(compareEndTime.isBefore(startTime) || endTime.isBefore(compareStartTime));
                    if (isOverlapping) {
                        idle = false;
                    }
                }
            }
            temp.put("idle", idle);
            if (idle) {
                resList.add(temp);
            }
        }
        return resList;
    }

    @Override
    public List<JSONObject> getIdleFamilyRoom(boolean isAll) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        List<AreaInfoRespVO> areaList = areaService.getAreaByOrgCode(sessionUser.getOrgCode(), "0015");

        List<JSONObject> consularList = wbCommonDao.getFamilyRegistration(sessionUser.getOrgCode());

        Map<String,String> map = new HashMap<>();
        for(JSONObject consular:consularList){
            map.put(consular.getString("room_id"),consular.getString("room_id"));
        }

        List<JSONObject> resList = new ArrayList<>();
        for(AreaInfoRespVO area:areaList){
            JSONObject temp = new JSONObject();
            temp.put("roomId",area.getAreaCode());
            temp.put("roomName",area.getAreaName());
            boolean idle = true;
            if(map.containsKey(area.getAreaCode())){
                idle = false;
            }else {
                idle = true;
            }
            temp.put("idle",idle);
            if(isAll){
                resList.add(temp);
            }else {
                if(idle){
                    resList.add(temp);
                }
            }
        }
        return resList;
    }

    @Override
    public List<JSONObject> getTrajectory(String targetId, String type) {

        List<JSONObject> trajectoryList = new ArrayList<>();

        JSONObject meetJosn = new JSONObject();
        if(WbConstants.BUSINESS_TYPE_ARRAIGNMENT.equals(type)){
            meetJosn = wbCommonDao.getArraignmentTrajectory(targetId);
        }else if(WbConstants.BUSINESS_TYPE_BRING_INTERROGATION.equals(type)){
            meetJosn = wbCommonDao.getBringInterrogationTrajectory(targetId);
        }else if(WbConstants.BUSINESS_TYPE_ESCORT.equals(type)){
            meetJosn = wbCommonDao.getEscortTrajectory(targetId);
        }else if(WbConstants.BUSINESS_TYPE_LAWYER_MEETING.equals(type)){
            meetJosn = wbCommonDao.getLawyerTrajectory(targetId);
        }else if(WbConstants.BUSINESS_TYPE_FAMILY_MEETING.equals(type)){
            meetJosn = wbCommonDao.getFamilyMeetingTrajectory(targetId);
        }else if(WbConstants.BUSINESS_TYPE_CONSULAR_MEETING.equals(type)){
            meetJosn = wbCommonDao.getConsularTrajectory(targetId);
        }else if(WbConstants.BUSINESS_TYPE_FAMILY_MEETING_VIDEO.equals(type)){
            meetJosn = wbCommonDao.getFamilyMeetingVideoTrajectory(targetId);
        }

        //登记节点
        if(meetJosn.containsKey("register_time") && ObjectUtil.isNotEmpty(meetJosn.getString("register_time"))){
            trajectoryList.add(registerNode(meetJosn,type));
        }

        //审核节点
        if(meetJosn.containsKey("act_inst_id") && ObjectUtil.isNotEmpty(meetJosn.getString("act_inst_id"))){
            //审核节点的数据，从bsp获取
            List<JSONObject> approvalList = getApprovalNode(meetJosn,type);
            if(CollectionUtil.isNotEmpty(approvalList)){
                trajectoryList.addAll(approvalList);
            }
        }

        //签到-分配会见室
        if(meetJosn.containsKey("check_in_time") && ObjectUtil.isNotEmpty(meetJosn.getString("check_in_time"))){
            //提讯、提询、提解没有这个节点
            trajectoryList.add(checkNode(meetJosn,type));
        }

        //律师会见-远程会见没有签到，但是有分配会见室
        if(WbConstants.BUSINESS_TYPE_LAWYER_MEETING.equals(type)){
            trajectoryList.add(checkNode(meetJosn,type));
        }

        //带出安检
        if(meetJosn.containsKey("inspection_time") && ObjectUtil.isNotEmpty(meetJosn.getString("inspection_time"))){
            trajectoryList.add(escortingNode(meetJosn,type));
        }
        //会毕安检
        if(meetJosn.containsKey("return_inspection_time") && ObjectUtil.isNotEmpty(meetJosn.getString("return_inspection_time"))){
            trajectoryList.add(returnNode(meetJosn,type));
        }

        return trajectoryList;
    }

    private List<JSONObject> getNodeInfo(JSONObject meetJosn,List<String> nodeStrList){
        List<JSONObject> nodeList = new ArrayList<>();
        for(String nodeStr:nodeStrList){
            String[] node = nodeStr.split("\\|\\|");
            JSONObject temp = new JSONObject();
            temp.put("key",node[0]);
            temp.put("val",meetJosn.getString(node[1]));
            nodeList.add(temp);
        }
        return nodeList;
    }

    private JSONObject registerNode(JSONObject meetJosn,String type){
        JSONObject node = new JSONObject();
        node.put("nodeName","会见登记");
        List<String> nodeStrList = new ArrayList<>();
        if(WbConstants.BUSINESS_TYPE_ARRAIGNMENT.equals(type)){
            nodeStrList = Arrays.asList("办案单位||meeting_unit","办案民警||meeting_person"
                    ,"提讯事由||reason","预约提讯时间||apply_meeting_time");
            translate(meetJosn,"ZD_WB_TXSY","reason");
            node.put("nodeName","提讯登记");
        }else if(WbConstants.BUSINESS_TYPE_BRING_INTERROGATION.equals(type)){
            nodeStrList = Arrays.asList("办案单位||meeting_unit","办案民警||meeting_person"
                    ,"提询事由||reason","预约提询时间||apply_meeting_time");
            translate(meetJosn,"ZD_WB_TXSY","reason");
            node.put("nodeName","提询登记");
        }else if(WbConstants.BUSINESS_TYPE_ESCORT.equals(type)){
            nodeStrList = Arrays.asList("办案单位||meeting_unit","办案民警||meeting_person"
                    ,"提解事由||reason","预约提解时间||apply_meeting_time");
            translate(meetJosn,"ZD_WB_TJSY","reason");
            node.put("nodeName","提解登记");
        }else if(WbConstants.BUSINESS_TYPE_LAWYER_MEETING.equals(type)){
            nodeStrList = Arrays.asList("律师姓名||meeting_person","执业证号码||lawyer_practice_license_number"
                    ,"会见方式||meeting_method","预约会见时间||apply_meeting_time","登记时间||register_time");
        }else if(WbConstants.BUSINESS_TYPE_FAMILY_MEETING.equals(type)){
            translate(meetJosn,"ZD_GABBZ_SHGX","relationship");
            nodeStrList = Arrays.asList("家属姓名||meeting_person","社会关系||relationship"
                    ,"预约会见时间||apply_meeting_time","会见人数||meeting_number");
        }else if(WbConstants.BUSINESS_TYPE_CONSULAR_MEETING.equals(type)){
            nodeStrList = Arrays.asList("会见人单位||meeting_unit","会见人||meeting_person","会见方式||meeting_method",
                    "预约会见时间||apply_meeting_time","登记时间||register_time");
        }else if(WbConstants.BUSINESS_TYPE_FAMILY_MEETING_VIDEO.equals(type)){
            translate(meetJosn,"ZD_GABBZ_SHGX","relationship");
            nodeStrList = Arrays.asList("家属姓名||meeting_person","社会关系||relationship"
                    ,"预约会见时间||apply_meeting_time","会见人数||meeting_number");
        }

        node.put("nodeKey","dj");
        node.put("nodeName","会见登记");
        node.put("nodeCreateTime",meetJosn.getString("register_time"));
        node.put("nodeInfo",getNodeInfo(meetJosn,nodeStrList));
        return node;
    }

    private JSONObject checkNode(JSONObject meetJosn,String type){

        JSONObject node = new JSONObject();
        node.put("nodeKey","qd_fphjs");
        List<String> nodeStrList = new ArrayList<>();

        if(WbConstants.BUSINESS_TYPE_LAWYER_MEETING.equals(type) && "2".equals(meetJosn.getString("meeting_method"))){
            node.put("nodeName","分配会见室");
            nodeStrList =  Arrays.asList("会见室||area_name"
                    ,"分配人||assignment_police","分配时间||assignment_room_time");
        }else {
            node.put("nodeName","签到-分配会见室");
            nodeStrList =  Arrays.asList("签到时间||check_in_time","会见室||area_name"
                    ,"分配人||assignment_police","分配时间||assignment_room_time");
        }
        node.put("nodeCreateTime",meetJosn.getString("check_in_time"));
        node.put("nodeInfo",getNodeInfo(meetJosn,nodeStrList));
        return node;
    }

    private JSONObject escortingNode(JSONObject meetJosn,String type){
        List<String> nodeStrList = new ArrayList<>();
        if(WbConstants.BUSINESS_TYPE_ARRAIGNMENT.equals(type)){
            nodeStrList =  Arrays.asList("带出监室时间||escorting_time","带出民警||escorting_police"
                    ,"提讯开始时间||meeting_start_time","检查时间||inspection_time","检查人||inspector","检查结果||inspection_result");
        }else if(WbConstants.BUSINESS_TYPE_BRING_INTERROGATION.equals(type)){
            nodeStrList =  Arrays.asList("带出监室时间||escorting_time","带出民警||escorting_police"
                    ,"提询开始时间||meeting_start_time","检查时间||inspection_time","检查人||inspector","检查结果||inspection_result");
        }else if(WbConstants.BUSINESS_TYPE_ESCORT.equals(type)){
            nodeStrList =  Arrays.asList("带出监室时间||escorting_time","带出民警||escorting_police"
                    ,"提解开始时间||meeting_start_time","检查时间||inspection_time","检查人||inspector","检查结果||inspection_result");
        }else {
            //家属、律师、领事的一样
            nodeStrList =  Arrays.asList("带出监室时间||escorting_time","带出民警||escorting_police"
                    ,"会见开始时间||meeting_start_time","检查时间||inspection_time","检查人||inspector","检查结果||inspection_result");
        }


        JSONObject node = new JSONObject();
        node.put("nodeKey","dcaj");
        node.put("nodeName","带出安检");
        node.put("nodeCreateTime",meetJosn.getString("escorting_operator_time"));
        node.put("nodeInfo",getNodeInfo(meetJosn,nodeStrList));
        return node;
    }

    private JSONObject returnNode(JSONObject meetJosn,String type){
        List<String> nodeStrList = new ArrayList<>();

        if(WbConstants.BUSINESS_TYPE_ARRAIGNMENT.equals(type)){
            nodeStrList = Arrays.asList("提讯结束时间||meeting_end_time","带回监室时间||return_time"
                    ,"带回民警||return_police","检查时间||return_inspection_time","检查人||inspector","检查结果||inspection_result");
        }else if(WbConstants.BUSINESS_TYPE_BRING_INTERROGATION.equals(type)){
            nodeStrList = Arrays.asList("提询结束时间||meeting_end_time","带回监室时间||return_time"
                    ,"带回民警||return_police","检查时间||return_inspection_time","检查人||inspector","检查结果||inspection_result");
        }else if(WbConstants.BUSINESS_TYPE_ESCORT.equals(type)){
            nodeStrList =  Arrays.asList("提解结束时间||meeting_end_time","带回监室时间||return_time"
                    ,"带回民警||return_police","检查时间||return_inspection_time","检查人||inspector","检查结果||inspection_result");
        }else {
            //家属、律师、领事的一样
            nodeStrList = Arrays.asList("会见结束时间||meeting_end_time","带回监室时间||return_time"
                    ,"带回民警||return_police","检查时间||return_inspection_time","检查人||inspector","检查结果||inspection_result");
        }

        JSONObject node = new JSONObject();
        node.put("nodeKey","hbaj");
        node.put("nodeName","会毕安检");
        node.put("nodeCreateTime",meetJosn.getString("escorting_operator_time"));
        node.put("nodeInfo",getNodeInfo(meetJosn,nodeStrList));
        return node;
    }

    private List<JSONObject> getApprovalNode(JSONObject meetJosn,String type){
        JSONObject trackJson = BspApprovalUtil.getBpmApi().approveTrack(meetJosn.getString("act_inst_id"));

        //trackJson 返回的流程数据是根据时间降序来排序的
        List<JSONObject> nodeList = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(trackJson) && trackJson.getInteger("code") == 200 ){
            List<JSONObject> trackList = JSONArray.parseArray(JSON.toJSONString(trackJson.get("data")),JSONObject.class);
            if(CollectionUtil.isEmpty(trackList)){
                return null;
            }
            Collections.reverse(trackList);

            //移除第一个节点(民警填报)
            trackList.remove(0);

            for(JSONObject trackNodeJson:trackList){
                //只需要已经审批过的节点
                if(trackNodeJson.getInteger("status") != 1){
                    continue;
                }
                JSONObject node = new JSONObject();
                node.put("nodeKey","");
                node.put("nodeName",trackNodeJson.getString("taskName"));
                node.put("nodeCreateTime",DateUtil.format(DateUtil.parse(trackNodeJson.getString("createTime"),
                        "yyyy-MM-dd HH:mm:ss"),"yyyy-MM-dd HH:mm:ss"));


                trackNodeJson.put("isApprove", BspApproceStatusEnum.getNameByCode(Short.parseShort(trackNodeJson.getString("isApprove"))));
                trackNodeJson.put("endTime", DateUtil.format(DateUtil.parse(trackNodeJson.getString("endTime"),
                        "yyyy-MM-dd HH:mm:ss"),"yyyy-MM-dd HH:mm:ss"));
                List<String> nodeStrList =  Arrays.asList("审核结果||isApprove","审核意见||approvalContent"
                        ,"审核人||executeUserName","审核时间||endTime");
                node.put("nodeInfo",getNodeInfo(trackNodeJson,nodeStrList));
                nodeList.add(node);
            }
        }
        return nodeList;
    }


    private void translate(JSONObject meetJosn,String dicName,String field) {
        String codeStr = meetJosn.getString(field);
        if(ObjectUtil.isNotEmpty(codeStr)){

            List<String> codeList = Arrays.asList(codeStr.split(","));
            StringBuilder valueStr = new StringBuilder();
            for(int i=0;i<codeList.size();i++){
                valueStr.append(DicUtils.translate(dicName,codeList.get(i)));
                if(i<codeList.size()-1){
                    valueStr.append("、");
                }
            }
            meetJosn.put(field,valueStr.toString());
        }
    }

    @Override
    public JSONObject verificationPersonnel(String jgrybm, String type) {
        JSONObject resJson = new JSONObject();

        List<JSONObject> businessList = wbCommonDao.verificationPersonnel(jgrybm,type);
        if(CollectionUtil.isEmpty(businessList)){
            resJson.put("prompt",false);
            return resJson;
        }
        resJson.put("prompt",true);
        PrisonerVwRespVO prisonerVwRespVO = prisonerService.getPrisonerByJgrybm(jgrybm);
        resJson.put("msg",String.format("被监管人员 %s 今日还存在以下业务，是否确认继续进行%s",prisonerVwRespVO.getXm(),DicUtils.translate("ZD_WB_YWLX",type)));
        resJson.put("businessList",businessList);
        return resJson;
    }


    @Override
    public PageResult<JSONObject> getHistoryMeetingByJgrybm(String jgrybm,int pageNo,int pageSize,String type) {

        IPage<JSONObject> ipage = getMeetingList(jgrybm,pageNo,pageSize,type);
        if(CollectionUtil.isEmpty(ipage.getRecords())){
            return new PageResult<>(new ArrayList<>(),ipage.getTotal());
        }

        List<JSONObject> nodeList = new ArrayList<>();
        for(JSONObject node:ipage.getRecords()){
            nodeList.add(getHistoryMeetingNode(node,type));
        }

        return new PageResult<>(nodeList,ipage.getTotal());
    }

    private IPage<JSONObject> getMeetingList(String jgrybm,int pageNo,int pageSize,String type){
        Page<JSONObject> page = new Page<>(ObjectUtil.isNotEmpty(pageNo)?pageNo:1, ObjectUtil.isNotEmpty(pageSize)?pageSize:10);
        IPage<JSONObject> ipage = null;
        if(WbConstants.BUSINESS_TYPE_ARRAIGNMENT.equals(type)){
            ipage = wbCommonDao.getHistoryArraignmentPageByJgrybm(page,jgrybm);
        }else if(WbConstants.BUSINESS_TYPE_BRING_INTERROGATION.equals(type)){
            ipage = wbCommonDao.getHistoryBringInterrogationPageByJgrybm(page,jgrybm);
        }else if(WbConstants.BUSINESS_TYPE_ESCORT.equals(type)){
            ipage = wbCommonDao.getHistoryEscortPageByJgrybm(page,jgrybm);
        }else if(WbConstants.BUSINESS_TYPE_LAWYER_MEETING.equals(type)){
            ipage = wbCommonDao.getHistoryLawyerMeetingPageByJgrybm(page,jgrybm);
        }else if(WbConstants.BUSINESS_TYPE_FAMILY_MEETING.equals(type)){
            ipage = wbCommonDao.getHistoryFamilyMeetingPageByJgrybm(page,jgrybm);
        }else if(WbConstants.BUSINESS_TYPE_CONSULAR_MEETING.equals(type)){
            ipage = wbCommonDao.getHistoryConsularMeetingPageByJgrybm(page,jgrybm);
        }else if(WbConstants.BUSINESS_TYPE_FAMILY_MEETING_VIDEO.equals(type)){
            ipage = wbCommonDao.getHistoryFamilyMeetingVideoPageByJgrybm(page,jgrybm);
        }else if(WbConstants.BUSINESS_TYPE_GOODS_DELIVERY.equals(type)){
            ipage = wbCommonDao.getHistoryGoodsDeliveryPageByJgrybm(page,jgrybm);
        }
        return ipage;
    }

    private JSONObject getHistoryMeetingNode(JSONObject meetJosn,String type){
        List<String> nodeStrList = new ArrayList<>();

        if(WbConstants.BUSINESS_TYPE_ARRAIGNMENT.equals(type)){
            nodeStrList =  Arrays.asList("办案单位||meeting_unit","办案人员||meeting_person"
                    ,"提讯事由||reason");
            //翻译提讯事由
            translate(meetJosn,"ZD_WB_TXSY","reason");
        }else if(WbConstants.BUSINESS_TYPE_BRING_INTERROGATION.equals(type)){
            nodeStrList =  Arrays.asList("办案单位||meeting_unit","办案人员||meeting_person"
                    ,"提询事由||reason");
            //翻译提询事由
            translate(meetJosn,"ZD_WB_TXSY","reason");

        }else if(WbConstants.BUSINESS_TYPE_ESCORT.equals(type)){
            nodeStrList =  Arrays.asList("办案单位||meeting_unit","办案人员||meeting_person"
                    ,"提解事由||reason");
            //翻译提解事由
            translate(meetJosn,"ZD_WB_TJSY","reason");
        }else if(WbConstants.BUSINESS_TYPE_LAWYER_MEETING.equals(type)){
            nodeStrList =  Arrays.asList("会见律师||meeting_person","会见时间||meeting_time"
                    ,"会见类型||meeting_method");
        }else if(WbConstants.BUSINESS_TYPE_FAMILY_MEETING.equals(type)){
            nodeStrList =  Arrays.asList("家属姓名||meeting_person","证件号码||meeting_person_sfzh",
                    "社会关系||relationship","会见时间||meeting_time");
            translate(meetJosn,"ZD_GABBZ_SHGX","relationship");
        }else if(WbConstants.BUSINESS_TYPE_CONSULAR_MEETING.equals(type)){
            nodeStrList =  Arrays.asList("会见人||meeting_person","会见时间||meeting_time"
                    ,"会见类型||meeting_method");
        }else if(WbConstants.BUSINESS_TYPE_FAMILY_MEETING_VIDEO.equals(type)){
            nodeStrList =  Arrays.asList("家属姓名||meeting_person","证件号码||meeting_person_sfzh",
                    "社会关系||relationship","会见时间||meeting_time");
            translate(meetJosn,"ZD_GABBZ_SHGX","relationship");
        }else if(WbConstants.BUSINESS_TYPE_GOODS_DELIVERY.equals(type)){
            nodeStrList =  Arrays.asList("顾送人||meeting_person","证件号码||meeting_person_sfzh",
                    "社会关系||relationship","签收状态||status");
            translate(meetJosn,"ZD_GABBZ_SHGX","relationship");
            translate(meetJosn,"ZD_WB_WPGSZT","status");
        }


        JSONObject node = new JSONObject();
        node.put("nodeKey","");
        node.put("nodeName","");
        node.put("nodebusinessId",meetJosn.getString("id"));
        node.put("nodeCreateTime",meetJosn.getString("node_create_time"));
        node.put("nodeInfo",getNodeInfo(meetJosn,nodeStrList));
        return node;
    }

    @Override
    public List<JSONObject> getMeetingRoomList(String type) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        List<AreaInfoRespVO> areaList = new ArrayList<>();

        if(WbConstants.BUSINESS_TYPE_LAWYER_MEETING.equals(type)){
            areaList = areaService.getAreaByOrgCode(sessionUser.getOrgCode(), "0016");
        }else {
           throw new ServerException("该类型不支持，请完善");
        }
        //规则：判断时间是否有重叠，如果时间有重叠，则认为该段时间，审讯室不空闲
        List<JSONObject> resList = new ArrayList<>();
        for (AreaInfoRespVO area : areaList) {
            JSONObject temp = new JSONObject();
            temp.put("roomId", area.getAreaCode());
            temp.put("roomName", area.getAreaName());
            resList.add(temp);
        }
        return resList;
    }
}
