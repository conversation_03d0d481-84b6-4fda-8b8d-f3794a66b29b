package com.rs.module.acp.entity.pi;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-巡视管控-违规登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_violation_record")
@KeySequence("acp_pi_violation_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_violation_record")
public class ViolationRecordDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 巡控室ID
     */
    private String patrolRoomId;
    /**
     * 巡控室名称
     */
    private String patrolRoomName;
    /**
     * 监室号
     */
    private String roomId;
    private String roomName;
    /**
     * 违规内容
     */
    private String violationContent;
    private String violationContentHtml;
    /**
     * 处置情况
     */
    private String disposalSituation;
    /**
     * 是否岗位协同：1-是/0-否
     */
    private Short isPostCoordination;
    /**
     * 岗位协同，多选项
     */
    private String coordinationPosts;
    /**
     * 岗位协同人员名称
     */
    private String coordinationPostsName;
    /**
     * 上传附件路径，存储附件文件路径
     */
    private String attachment;
    /**
     * 状态
     */
    private String status;
    /**
     * 登记经办人
     */
    private String operatorSfzh;
    /**
     * 登记经办人姓名
     */
    private String operatorXm;
    /**
     * 登记经办时间
     */
    private Date operatorTime;
    /**
     * 岗位推送对象证件号码
     */
    private String pushTargetIdCard;
    /**
     * 岗位推送对象
     */
    private String pushTarget;
    /**
     * 岗位推送内容
     */
    private String pushContent;
}
