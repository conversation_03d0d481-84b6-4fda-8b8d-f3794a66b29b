package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.gj.MonthlyAssmtDocumentJdsDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.gj.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-月度考核文书打印(戒毒所) Dao
*
* <AUTHOR>
*/
@Mapper
public interface MonthlyAssmtDocumentJdsDao extends IBaseDao<MonthlyAssmtDocumentJdsDO> {

}
