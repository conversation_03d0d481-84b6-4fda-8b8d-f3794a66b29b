package com.rs.module.acp.service.gj.doublecheck;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.doublecheck.DoubleCheckListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.doublecheck.DoubleCheckPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.doublecheck.DoubleCheckSaveReqVO;
import com.rs.module.acp.entity.gj.DoubleCheckDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-双重检查登记 Service 接口
 *
 * <AUTHOR>
 */
public interface DoubleCheckService extends IBaseService<DoubleCheckDO>{

    /**
     * 创建实战平台-管教业务-双重检查登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createDoubleCheck(@Valid DoubleCheckSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-双重检查登记
     *
     * @param updateReqVO 更新信息
     */
    void updateDoubleCheck(@Valid DoubleCheckSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-双重检查登记
     *
     * @param id 编号
     */
    void deleteDoubleCheck(String id);

    /**
     * 获得实战平台-管教业务-双重检查登记
     *
     * @param id 编号
     * @return 实战平台-管教业务-双重检查登记
     */
    DoubleCheckDO getDoubleCheck(String id);

    /**
    * 获得实战平台-管教业务-双重检查登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-双重检查登记分页
    */
    PageResult<DoubleCheckDO> getDoubleCheckPage(DoubleCheckPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-双重检查登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-双重检查登记列表
    */
    List<DoubleCheckDO> getDoubleCheckList(DoubleCheckListReqVO listReqVO);


    PageResult<DoubleCheckDO> getAppDoubleCheckPage(int pageNo, int pageSize, String idCard, String type);
}
