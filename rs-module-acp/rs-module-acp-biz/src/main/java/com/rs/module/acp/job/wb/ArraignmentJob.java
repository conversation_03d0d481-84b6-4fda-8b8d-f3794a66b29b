package com.rs.module.acp.job.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.module.acp.entity.wb.ArraignmentDO;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.acp.service.wb.ArraignmentService;
import com.rs.module.acp.service.wb.WbCommonService;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.sys.MsgAddVO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.util.MsgUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 提讯定时任务
 */
@Component
public class ArraignmentJob {

    @Autowired
    private ArraignmentService arraignmentService;

    @Autowired
    private WbCommonService wbCommonService;


    /**
     * 提讯登记24小时内，状态未变更为【已完成】，则为异常
     */
    @XxlJob("abnormalNotification")
    public void abnormalNotification(){
        Map<String, PrisonerVwRespVO> prisonerMap = new HashMap<>();
        XxlJobHelper.log("提讯任务：-----开始-------");
        XxlJobHelper.log("提讯任务：提讯登记24小时内，状态未变更为【已完成】，则为异常");
        try {
            Date queryTime = DateUtil.offsetHour(new Date(),-24);
            LambdaQueryWrapper<ArraignmentDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.select(ArraignmentDO::getId,ArraignmentDO::getJgrybm,ArraignmentDO::getStartApplyArraignmentTime);
            lambdaQueryWrapper.in(ArraignmentDO::getStatus, Arrays.asList("0","1","2","3"))
                    .le(ArraignmentDO::getAddTime,queryTime);
            Integer pageNo = 1;
            Integer pageSize = 500;
            while (true){
                Page<ArraignmentDO> page = new Page<>(pageNo,pageSize);
                IPage<ArraignmentDO> arraignmentDOIPage = arraignmentService.page(page,lambdaQueryWrapper);
                if(CollectionUtil.isEmpty(arraignmentDOIPage.getRecords())){
                    break;
                }
                List<String> idList = arraignmentDOIPage.getRecords().stream().map(ArraignmentDO::getId).collect(Collectors.toList());
                LambdaUpdateWrapper<ArraignmentDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.in(ArraignmentDO::getId,idList).set(ArraignmentDO::getStatus,"99");
                arraignmentService.update(lambdaUpdateWrapper);

                //发送消息
                for(ArraignmentDO arraignment:arraignmentDOIPage.getRecords()){
                    XxlJobHelper.log("提讯任务：-----发送补录待办消息---start----");
                    wbCommonService.additionalRecordingReminder(JSONObject.parseObject(JSON.toJSONString(arraignment)), WbConstants.BUSINESS_TYPE_ARRAIGNMENT);
                    XxlJobHelper.log("提讯任务：-----发送补录待办消息----end---");
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            XxlJobHelper.log("提讯任务异常：{}",e.getMessage());
        }
        XxlJobHelper.log("提讯任务:-----结束------");
    }
}
