package com.rs.module.acp.dao.wb;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.HallInfoDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-服务大厅信息发布 Dao
*
* <AUTHOR>
*/
@Mapper
public interface HallInfoDao extends IBaseDao<HallInfoDO> {


    default PageResult<HallInfoDO> selectPage(HallInfoPageReqVO reqVO) {
        Page<HallInfoDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<HallInfoDO> wrapper = new LambdaQueryWrapperX<HallInfoDO>()
            .eqIfPresent(HallInfoDO::getTitle, reqVO.getTitle())
            .eqIfPresent(HallInfoDO::getInfoContent, reqVO.getInfoContent())
            .eqIfPresent(HallInfoDO::getAttachmentType, reqVO.getAttachmentType())
            .eqIfPresent(HallInfoDO::getAttachmentUrl, reqVO.getAttachmentUrl())
            .eqIfPresent(HallInfoDO::getStatus, reqVO.getStatus())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(HallInfoDO::getAddTime);
        }
        Page<HallInfoDO> hallInfoPage = selectPage(page, wrapper);
        return new PageResult<>(hallInfoPage.getRecords(), hallInfoPage.getTotal());
    }
    default List<HallInfoDO> selectList(HallInfoListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<HallInfoDO>()
            .eqIfPresent(HallInfoDO::getTitle, reqVO.getTitle())
            .eqIfPresent(HallInfoDO::getInfoContent, reqVO.getInfoContent())
            .eqIfPresent(HallInfoDO::getAttachmentType, reqVO.getAttachmentType())
            .eqIfPresent(HallInfoDO::getAttachmentUrl, reqVO.getAttachmentUrl())
            .eqIfPresent(HallInfoDO::getStatus, reqVO.getStatus())
        .orderByDesc(HallInfoDO::getAddTime));    }


    }
