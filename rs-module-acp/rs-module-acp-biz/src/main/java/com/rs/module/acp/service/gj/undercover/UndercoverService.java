package com.rs.module.acp.service.gj.undercover;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.*;
import com.rs.module.acp.entity.gj.UndercoverDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-管教业务-耳目管理 Service 接口
 *
 * <AUTHOR>
 */
public interface UndercoverService extends IBaseService<UndercoverDO>{

    /**
     * 创建实战平台-管教业务-耳目管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createUndercover(@Valid UndercoverSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-耳目管理
     *
     * @param updateReqVO 更新信息
     */
    void updateUndercover(@Valid UndercoverSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-耳目管理
     *
     * @param id 编号
     */
    void deleteUndercover(String id);

    /**
     * 获得实战平台-管教业务-耳目管理
     *
     * @param id 编号
     * @return 实战平台-管教业务-耳目管理
     */
    UndercoverDO getUndercover(String id);

    /**
    * 获得实战平台-管教业务-耳目管理分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-耳目管理分页
    */
    PageResult<UndercoverDO> getUndercoverPage(UndercoverPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-耳目管理列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-耳目管理列表
    */
    List<UndercoverDO> getUndercoverList(UndercoverListReqVO listReqVO);

    /**
     * 更新流程信息
     * <AUTHOR>
     * @date 2025/5/26 18:01
     * @param [id, status, actInstId, taskId]
     * @return java.lang.Boolean
     */
    Boolean updateProcessStatus(String id, String status, String actInstId, String taskId);

    /**
     * 领导审批
     * <AUTHOR>
     * @date 2025/5/26 20:08
     * @param [approveReqVO]
     * @return void
     */
    void leaderApprove(GjApproveReqVO gjApproveReqVO);

    /**
     * 获取信息员布控记录
     * <AUTHOR>
     * @date 2025/5/27 10:37
     * @param [jgrybm]
     * @return java.util.List<com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverRespVO>
     */
    PageResult<UndercoverRecordVO> getUndercoverRespVOByJgrybm(String jgrybm,Integer pageNo, Integer pageSize);
}
