package com.rs.module.acp.controller.admin.db;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.OutBiometricInfoDO;
import com.rs.module.acp.service.db.OutBiometricInfoService;

@Api(tags = "实战平台-收押业务-出所生物特征信息")
@RestController
@RequestMapping("/acp/db/outBiometricInfo")
@Validated
public class OutBiometricInfoController {

    @Resource
    private OutBiometricInfoService outBiometricInfoService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-收押业务-出所生物特征信息")
    public CommonResult<String> createOutBiometricInfo(@Valid @RequestBody OutBiometricInfoSaveReqVO createReqVO) {
        return success(outBiometricInfoService.createOutBiometricInfo(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-收押业务-出所生物特征信息")
    public CommonResult<Boolean> updateOutBiometricInfo(@Valid @RequestBody OutBiometricInfoSaveReqVO updateReqVO) {
        outBiometricInfoService.updateOutBiometricInfo(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-收押业务-出所生物特征信息")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteOutBiometricInfo(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           outBiometricInfoService.deleteOutBiometricInfo(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-收押业务-出所生物特征信息")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<OutBiometricInfoRespVO> getOutBiometricInfo(@RequestParam("id") String id) {
        OutBiometricInfoDO outBiometricInfo = outBiometricInfoService.getOutBiometricInfo(id);
        return success(BeanUtils.toBean(outBiometricInfo, OutBiometricInfoRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-收押业务-出所生物特征信息分页")
    public CommonResult<PageResult<OutBiometricInfoRespVO>> getOutBiometricInfoPage(@Valid @RequestBody OutBiometricInfoPageReqVO pageReqVO) {
        PageResult<OutBiometricInfoDO> pageResult = outBiometricInfoService.getOutBiometricInfoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, OutBiometricInfoRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-收押业务-出所生物特征信息列表")
    public CommonResult<List<OutBiometricInfoRespVO>> getOutBiometricInfoList(@Valid @RequestBody OutBiometricInfoListReqVO listReqVO) {
        List<OutBiometricInfoDO> list = outBiometricInfoService.getOutBiometricInfoList(listReqVO);
        return success(BeanUtils.toBean(list, OutBiometricInfoRespVO.class));
    }

    /**
     * 通过人员编号获取该人员生物信息采集以及核验信息（一键核验）
     */
    @GetMapping("/getByJgrybm")
    @ApiOperation(value = "通过人员编号获取该人员生物信息采集以及核验信息（一键核验）")
    public CommonResult<List<OutBiometricInfoRespVO>> getByJgrybm(@RequestParam("jgrybm") String jgrybm) {
        //查询核验终端核验结果并存储到本地(接口待提供)

        //查询核验结果
        List<OutBiometricInfoRespVO> outBiometricInfo = outBiometricInfoService.getByJgrybm(jgrybm);
        return success(BeanUtils.toBean(outBiometricInfo, OutBiometricInfoRespVO.class));
    }
}
