package com.rs.module.acp.dao.gj.conflict;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.acp.entity.gj.conflict.ConflictRegDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 实战平台-管教业务-社会矛盾化解登记 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface ConflictRegDao extends IBaseDao<ConflictRegDO> {

    @Delete("<script>" +
            "DELETE FROM acp_gj_conflict_reg " +
            "WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int deleteConflictRegDOsByIds(@Param("ids") List<String> ids);}
