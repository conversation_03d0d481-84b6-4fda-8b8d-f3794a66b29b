package com.rs.module.acp.controller.admin.ds.vo.sjbs;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-数据固化-每周数据报送(拘留所) Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class WeeklyDataSubmitJlsRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("固化日期")
    private String solidificationDate;
    @ApiModelProperty("固化开始日期")
    private String startDate;
    @ApiModelProperty("固化结束日期")
    private String endDate;
    @ApiModelProperty("律师会见")
    private Integer lshj;
    @ApiModelProperty("律师会见-快速会见")
    private Integer lshjKshj;
    @ApiModelProperty("律师会见-现场会见")
    private Integer lshjXchj;
    @ApiModelProperty("律师会见-远程会见")
    private Integer lshjYchj;
    @ApiModelProperty("提讯")
    private Integer tx;
    @ApiModelProperty("提讯-公安机关")
    private Integer txGajg;
    @ApiModelProperty("提讯-检察院")
    private Integer txJcy;
    @ApiModelProperty("提讯-法院")
    private Integer txFy;
    @ApiModelProperty("提讯-其他单位")
    private Integer txQtdw;
    @ApiModelProperty("提解")
    private Integer tj;
    @ApiModelProperty("提解-公安机关")
    private Integer tjGajg;
    @ApiModelProperty("提解-检察院")
    private Integer tjJcy;
    @ApiModelProperty("提解-法院")
    private Integer tjFy;
    @ApiModelProperty("提解-其他单位")
    private Integer tjQtdw;
}
