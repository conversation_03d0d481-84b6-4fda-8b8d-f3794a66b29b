package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.VisitorInfoDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-来访人员信息 Service 接口
 *
 * <AUTHOR>
 */
public interface VisitorInfoService extends IBaseService<VisitorInfoDO>{

    /**
     * 创建实战平台-管教业务-来访人员信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createVisitorInfo(@Valid VisitorInfoSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-来访人员信息
     *
     * @param updateReqVO 更新信息
     */
    void updateVisitorInfo(@Valid VisitorInfoSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-来访人员信息
     *
     * @param id 编号
     */
    void deleteVisitorInfo(String id);

    /**
     * 获得实战平台-管教业务-来访人员信息
     *
     * @param id 编号
     * @return 实战平台-管教业务-来访人员信息
     */
    VisitorInfoDO getVisitorInfo(String id);

    /**
    * 获得实战平台-管教业务-来访人员信息分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-来访人员信息分页
    */
    PageResult<VisitorInfoDO> getVisitorInfoPage(VisitorInfoPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-来访人员信息列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-来访人员信息列表
    */
    List<VisitorInfoDO> getVisitorInfoList(VisitorInfoListReqVO listReqVO);

    /**
     * 保存人员信息列表
     * @param saveReqVOList
     * @param visitorRegId
     * @return
     */
    boolean savetVisitorInfoList(List<VisitorInfoSaveReqVO> saveReqVOList,String visitorRegId);

    /**
     * 根据对外开放信息ID，获取人员信息
     * @param visitorRegId
     * @return
     */
    List<VisitorInfoRespVO> getVisitorInfoListByVisitorRegId(String visitorRegId);

}
