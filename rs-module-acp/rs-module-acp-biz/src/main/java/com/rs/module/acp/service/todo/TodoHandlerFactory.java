package com.rs.module.acp.service.todo;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class TodoHandlerFactory {

    private final Map<TodoBusinessType, TodoHandler> handlerMap = new HashMap<>();

    public TodoHandlerFactory(List<TodoHandler> handlers) {
        // 初始化时将所有处理器注册到工厂中
        handlers.forEach(handler -> handlerMap.put(handler.getSupportedBusinessType(), handler));
    }

    public TodoHandler getHandler(TodoBusinessType businessType) {
        return handlerMap.get(businessType);
    }

    public List<TodoHandler> getAllHandlers() {
        return handlerMap.values().stream().collect(Collectors.toList());
    }
}
