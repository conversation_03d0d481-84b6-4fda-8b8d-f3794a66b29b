package com.rs.module.acp.controller.app.gj.vo.inoutrecords;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 出入登记
 */
@Data
@ApiModel("出入登记-统计")
@NoArgsConstructor
@AllArgsConstructor
public class InOutStatisticVO implements Serializable {

    private static final long serialVersionUID = 1082667630633084597L;

    @ApiModelProperty("监室总人数")
    private Integer personNum;

    @ApiModelProperty("外出总人数")
    private Integer outTotalNum;

    @ApiModelProperty("业务类型统计集合")
    private List<JSONObject> listBusinessTypeNum;


}

