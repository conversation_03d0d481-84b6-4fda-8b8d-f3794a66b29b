package com.rs.module.acp.entity.wb;

import lombok.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-窗口业务-提讯登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_wb_arraignment")
@KeySequence("acp_wb_arraignment_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ArraignmentDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 第一位办案人ID
     */
    private String handler1Id;
    /**
     * 第一位办案人姓名
     */
    private String handler1Xm;
    /**
     * 第二位办案人ID
     */
    private String handler2Id;
    /**
     * 第二位办案人姓名
     */
    private String handler2Xm;
    /**
     * 第三位办案人ID
     */
    private String handler3Id;
    /**
     * 第三位办案人姓名
     */
    private String handler3Xm;
    /**
     * 审讯室ID
     */
    private String roomId;
    /**
     * 提讯原因
     */
    private String arraignmentReason;
    /**
     * 申请提讯开始时间
     */
    private Date startApplyArraignmentTime;
    /**
     * 申请提讯结束时间
     */
    private Date endApplyArraignmentTime;
    /**
     * 介绍信编号
     */
    private String arraignmentLetterNumber;
    /**
     * 提讯凭证类型
     */
    private String evidenceType;
    /**
     * 所选的凭证证号
     */
    private String evidenceNumber;
    /**
     * 上传提讯凭证URL
     */
    private String evidenceUrl;
    /**
     * 未成年代理人
     */
    private String minorAgent;
    /**
     * 未成年监护人
     */
    private String minorGuardian;
    /**
     * 翻译人员
     */
    private String translator;
    /**
     * 其他专业人员
     */
    private String otherProfessionals;
    /**
     * 其他专业人员书面证明存储路径
     */
    private String professionalCertUrl;
    /**
     * 提解机关类型
     */
    private String tjjglx;
    /**
     * 提解机关名称
     */
    private String tjjgmc;
    /**
     * 办理状态
     */
    private String status;
    /**
     * 签到时间
     */
    private Date checkInTime;
    /**
     * 签到用户身份证号
     */
    private String checkInPoliceSfzh;
    /**
     * 签到用户
     */
    private String checkInPolice;
    /**
     * 带出民警身份证号
     */
    private String escortingPoliceSfzh;
    /**
     * 带出民警身份证号
     */
    private String escortingPolice;
    /**
     * 带出监室时间
     */
    private Date escortingTime;
    /**
     * 带出操作人身份证号
     */
    private String escortingOperatorSfzh;
    /**
     * 带出操作人
     */
    private String escortingOperator;
    /**
     * 带出操作时间
     */
    private Date escortingOperatorTime;
    /**
     * 检查结果
     */
    private String inspectionResult;
    /**
     * 违禁物品登记
     */
    private String prohibitedItems;
    /**
     * 体表检查登记
     */
    private String physicalExam;
    /**
     * 异常情况登记
     */
    private String abnormalSituations;
    /**
     * 违禁物品登记照片存储地址
     */
    private String prohibitedItemsImgUrl;
    /**
     * 体表检查登记照片存储地址
     */
    private String physicalExamImgUrl;
    /**
     * 异常情况登记照片存储地址
     */
    private String abnormalSituationsImgUrl;
    /**
     * 是否查出违禁物品
     */
    private Short isProhibitedItems;
    /**
     * 检查时间
     */
    private Date inspectionTime;
    /**
     * 检查民警身份证号
     */
    private String inspectorSfzh;
    /**
     * 检查民警身份证号
     */
    private String inspector;
    /**
     * 提讯开始时间
     */
    private Date arraignmentStartTime;
    /**
     * 提讯结束时间
     */
    private Date arraignmentEndTime;
    /**
     * 会毕检查人
     */
    private String returnInspectorSfzh;
    /**
     * 会毕检查人
     */
    private String returnInspector;
    /**
     * 会毕检查时间
     */
    private Date returnInspectionTime;
    /**
     * 会毕检查结果
     */
    private String returnInspectionResult;
    /**
     * 带回违禁物品登记
     */
    private String returnProhibitedItems;
    /**
     * 带回体表检查登记
     */
    private String returnPhysicalExam;
    /**
     * 带回异常情况登记
     */
    private String returnAbnormalSituations;
    /**
     * 带回监室时间
     */
    private Date returnTime;
    /**
     * 带回民警姓名
     */
    private String returnPolice;
    /**
     * 带回民警身份证号
     */
    private String returnPoliceSfzh;
    /**
     * 带回操作人身份证号
     */
    private String returnOperatorSfzh;
    /**
     * 带回操作人
     */
    private String returnOperator;
    /**
     * 带回操作时间
     */
    private Date returnOperatorTime;
}
