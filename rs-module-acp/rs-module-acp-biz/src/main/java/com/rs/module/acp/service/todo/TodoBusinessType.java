package com.rs.module.acp.service.todo;

public enum TodoBusinessType {
    CNP_MDHJQR("CNP_MDHJ", "矛盾化解确认"),
    CNP_FXGZS("CNP_FXGZS", "风险告知书");

    private final String description;
    private final String code;

    TodoBusinessType(String code, String description) {
        this.description = description;
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public String getCode() {
        return code;
    }

    @Override
    public String toString() {
        return code;
    }
}
