package com.rs.module.acp.service.wb;

import com.rs.module.acp.controller.admin.wb.vo.WbInOutSaveReqVO;
import com.rs.module.acp.controller.admin.wb.vo.WbInspectionResultsSaveReqVO;

/**
 * 窗口对外业务驱动api
 */
public interface WbBusinessApiService {


    /**
     * 终端带出、带入业务驱动窗口业务
     * @param saveReqVO
     * @param businessType 业务类型--对应business_type
     * @param inoutType 进出类型---对应 inout_type
     * @return
     */
    boolean inOutBusinessDrive(WbInOutSaveReqVO saveReqVO, String businessType, String inoutType);

}
