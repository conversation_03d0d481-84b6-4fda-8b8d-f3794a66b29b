package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.confinement.ConfinementRegListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.confinement.ConfinementRegPageReqVO;
import com.rs.module.acp.entity.gj.confinement.ConfinementRegDO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 实战平台-管教业务-禁闭登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ConfinementRegDao extends IBaseDao<ConfinementRegDO> {


    default PageResult<ConfinementRegDO> selectPage(ConfinementRegPageReqVO reqVO) {
        Page<ConfinementRegDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ConfinementRegDO> wrapper = new LambdaQueryWrapperX<ConfinementRegDO>()
            .eqIfPresent(ConfinementRegDO::getDataSources, reqVO.getDataSources())
            .eqIfPresent(ConfinementRegDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(ConfinementRegDO::getConfinementDays, reqVO.getConfinementDays())
            .eqIfPresent(ConfinementRegDO::getRoomId, reqVO.getRoomId())
            .eqIfPresent(ConfinementRegDO::getOriginalRoomId, reqVO.getOriginalRoomId())
            .eqIfPresent(ConfinementRegDO::getConfinementReason, reqVO.getConfinementReason())
            .eqIfPresent(ConfinementRegDO::getDetailedReason, reqVO.getDetailedReason())
            .eqIfPresent(ConfinementRegDO::getRemarks, reqVO.getRemarks())
            .eqIfPresent(ConfinementRegDO::getStatus, reqVO.getStatus())
            .betweenIfPresent(ConfinementRegDO::getConfinementStartDate, reqVO.getConfinementStartDate())
            .betweenIfPresent(ConfinementRegDO::getConfinementEndDate, reqVO.getConfinementEndDate())
            .eqIfPresent(ConfinementRegDO::getEscortingPoliceSfzh, reqVO.getEscortingPoliceSfzh())
            .eqIfPresent(ConfinementRegDO::getEscortingPolice, reqVO.getEscortingPolice())
            .betweenIfPresent(ConfinementRegDO::getEscortingTime, reqVO.getEscortingTime())
            .eqIfPresent(ConfinementRegDO::getInspectionResult, reqVO.getInspectionResult())
            .eqIfPresent(ConfinementRegDO::getProhibitedItems, reqVO.getProhibitedItems())
            .eqIfPresent(ConfinementRegDO::getPhysicalExam, reqVO.getPhysicalExam())
            .eqIfPresent(ConfinementRegDO::getAbnormalSituations, reqVO.getAbnormalSituations())
            .eqIfPresent(ConfinementRegDO::getProhibitedItemsImgUrl, reqVO.getProhibitedItemsImgUrl())
            .eqIfPresent(ConfinementRegDO::getPhysicalExamImgUrl, reqVO.getPhysicalExamImgUrl())
            .eqIfPresent(ConfinementRegDO::getAbnormalSituationsImgUrl, reqVO.getAbnormalSituationsImgUrl())
            .eqIfPresent(ConfinementRegDO::getIsProhibitedItems, reqVO.getIsProhibitedItems())
            .betweenIfPresent(ConfinementRegDO::getInspectionTime, reqVO.getInspectionTime())
            .eqIfPresent(ConfinementRegDO::getInspectorSfzh, reqVO.getInspectorSfzh())
            .eqIfPresent(ConfinementRegDO::getInspector, reqVO.getInspector())
            .betweenIfPresent(ConfinementRegDO::getIntoConfinementTime, reqVO.getIntoConfinementTime())
            .eqIfPresent(ConfinementRegDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(ConfinementRegDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(ConfinementRegDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(ConfinementRegDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(ConfinementRegDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(ConfinementRegDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(ConfinementRegDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(ConfinementRegDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(ConfinementRegDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(ConfinementRegDO::getAddTime);
        }
        Page<ConfinementRegDO> confinementRegPage = selectPage(page, wrapper);
        return new PageResult<>(confinementRegPage.getRecords(), confinementRegPage.getTotal());
    }
    default List<ConfinementRegDO> selectList(ConfinementRegListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ConfinementRegDO>()
            .eqIfPresent(ConfinementRegDO::getDataSources, reqVO.getDataSources())
            .eqIfPresent(ConfinementRegDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(ConfinementRegDO::getConfinementDays, reqVO.getConfinementDays())
            .eqIfPresent(ConfinementRegDO::getRoomId, reqVO.getRoomId())
            .eqIfPresent(ConfinementRegDO::getOriginalRoomId, reqVO.getOriginalRoomId())
            .eqIfPresent(ConfinementRegDO::getConfinementReason, reqVO.getConfinementReason())
            .eqIfPresent(ConfinementRegDO::getDetailedReason, reqVO.getDetailedReason())
            .eqIfPresent(ConfinementRegDO::getRemarks, reqVO.getRemarks())
            .eqIfPresent(ConfinementRegDO::getStatus, reqVO.getStatus())
            .betweenIfPresent(ConfinementRegDO::getConfinementStartDate, reqVO.getConfinementStartDate())
            .betweenIfPresent(ConfinementRegDO::getConfinementEndDate, reqVO.getConfinementEndDate())
            .eqIfPresent(ConfinementRegDO::getEscortingPoliceSfzh, reqVO.getEscortingPoliceSfzh())
            .eqIfPresent(ConfinementRegDO::getEscortingPolice, reqVO.getEscortingPolice())
            .betweenIfPresent(ConfinementRegDO::getEscortingTime, reqVO.getEscortingTime())
            .eqIfPresent(ConfinementRegDO::getInspectionResult, reqVO.getInspectionResult())
            .eqIfPresent(ConfinementRegDO::getProhibitedItems, reqVO.getProhibitedItems())
            .eqIfPresent(ConfinementRegDO::getPhysicalExam, reqVO.getPhysicalExam())
            .eqIfPresent(ConfinementRegDO::getAbnormalSituations, reqVO.getAbnormalSituations())
            .eqIfPresent(ConfinementRegDO::getProhibitedItemsImgUrl, reqVO.getProhibitedItemsImgUrl())
            .eqIfPresent(ConfinementRegDO::getPhysicalExamImgUrl, reqVO.getPhysicalExamImgUrl())
            .eqIfPresent(ConfinementRegDO::getAbnormalSituationsImgUrl, reqVO.getAbnormalSituationsImgUrl())
            .eqIfPresent(ConfinementRegDO::getIsProhibitedItems, reqVO.getIsProhibitedItems())
            .betweenIfPresent(ConfinementRegDO::getInspectionTime, reqVO.getInspectionTime())
            .eqIfPresent(ConfinementRegDO::getInspectorSfzh, reqVO.getInspectorSfzh())
            .eqIfPresent(ConfinementRegDO::getInspector, reqVO.getInspector())
            .betweenIfPresent(ConfinementRegDO::getIntoConfinementTime, reqVO.getIntoConfinementTime())
            .eqIfPresent(ConfinementRegDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(ConfinementRegDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(ConfinementRegDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(ConfinementRegDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(ConfinementRegDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(ConfinementRegDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(ConfinementRegDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(ConfinementRegDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(ConfinementRegDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(ConfinementRegDO::getAddTime));    }


    List<AreaPrisonRoomDO> getConfinementRoomList(@Param("nowRoomId") String nowRoomId, @Param("orgCode")String orgCode);
}
