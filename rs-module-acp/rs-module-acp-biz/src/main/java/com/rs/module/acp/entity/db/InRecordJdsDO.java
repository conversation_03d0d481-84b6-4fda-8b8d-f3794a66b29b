package com.rs.module.acp.entity.db;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-收押业务-入所登记（戒毒所） DO
 *
 * <AUTHOR>
 */
@TableName("acp_db_in_record_jds")
@KeySequence("acp_db_in_record_jds_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_db_in_record_jds")
public class InRecordJdsDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 姓名
     */
    private String xm;
    /**
     * 姓名拼音
     */
    private String xmpy;
    /**
     * 别名
     */
    private String bm;
    /**
     * 性别
     */
    private String xb;
    /**
     * 出生日期
     */
    private Date csrq;
    /**
     * 证件类型
     */
    private String zjlx;
    /**
     * 证件号码
     */
    private String zjhm;
    /**
     * 国籍
     */
    private String gj;
    /**
     * 民族
     */
    private String mz;
    /**
     * 婚姻状况
     */
    private String hyzk;
    /**
     * 籍贯
     */
    private String jg;
    /**
     * 宗教信仰
     */
    private String zjxy;
    /**
     * 户籍地
     */
    private String hjd;
    /**
     * 户籍地详址
     */
    private String hjdxz;
    /**
     * 现住址
     */
    private String xzz;
    /**
     * 现住址详址
     */
    private String xzzxz;
    /**
     * 文化程度
     */
    private String whcd;
    /**
     * 政治面貌
     */
    private String zzmm;
    /**
     * 职业
     */
    private String zy;
    /**
     * 工作单位
     */
    private String gzdw;
    /**
     * 身份
     */
    private String sf;
    /**
     * 特殊身份
     */
    private String tssf;
    /**
     * 信息核查
     */
    private String xxhc;
    /**
     * 是否为在校学生
     */
    private String sfwxzxs;
    /**
     * 学校名称
     */
    private String xxmc;
    /**
     * 案由代码
     */
    private String ajlbdm;
    /**
     * 案件类别名称
     */
    private String ajlb;
    /**
     * 案件编号
     */
    private String ajbh;
    /**
     * 人员编号
     */
    private String rybh;
    /**
     * 案事件相关人员编号
     */
    private String asjxgrybh;
    /**
     * 办案中心编号
     */
    private String bazxbh;
    /**
     * 送戒日期
     */
    private Date sjrq;
    /**
     * 送戒机关类型
     */
    private String sjjglx;
    /**
     * 送戒机关名称
     */
    private String sjjgmc;
    /**
     * 送戒人姓名1
     */
    private String sjrxm;
    /**
     * 送戒人联系电话1
     */
    private String sjrlxdh;
    /**
     * 送戒人姓名2
     */
    private String sjrxm2;
    /**
     * 送戒人联系电话2
     */
    private String sjrlxdh2;
    /**
     * 送戒民警姓名
     */
    private String sjmjxm;
    /**
     * 送戒民警联系电话
     */
    private String sjmjxmlxdh;
    /**
     * 办案单位类型
     */
    private String badwlx;
    /**
     * 办案单位
     */
    private String badw;
    /**
     * 收戒决定机关类型
     */
    private String sjjdjglx;
    /**
     * 收戒决定机关名称
     */
    private String sjjdjgmc;
    /**
     * 收戒凭证
     */
    private String sjpz;
    /**
     * 收戒凭证文书号
     */
    private String sjpzwsh;
    /**
     * 收戒凭证文书号
     */
    private String sjpzwsdz;
    /**
     * 档案编号
     */
    private String dabh;
    /**
     * 回执法律文书号
     */
    private String hzflwsh;
    /**
     * 入所原因
     */
    private String rsyy;
    /**
     * 入所日期
     */
    private Date rsrq;
    /**
     * 收戒民警
     */
    private String sjmj;
    /**
     * 收戒期限
     */
    private Date sjqx;
    /**
     * 收戒起始日期
     */
    private Date sjqsrq;
    /**
     * 收戒截止日期
     */
    private Date sjjzrq;
    /**
     * 管理类别
     */
    private String gllb;
    /**
     * 是否佩戴眼镜
     */
    private String sfpdyj;
    /**
     * 监区id
     */
    private String areaId;
    /**
     * 监区名称
     */
    private String areaName;
    /**
     * 监室号
     */
    private String jsh;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 分配床位
     */
    private String fpcw;
    /**
     * 收分配标识服
     */
    private String sfpbsf;
    /**
     * 带入戒室人
     */
    private String drjsr;
    /**
     * 带入戒室时间
     */
    private Date drjssj;
    /**
     * 简要案情
     */
    private String jyaq;
    /**
     * 同案人，多个逗号分割
     */
    private String tar;
    /**
     * 备注
     */
    private String bz;
    /**
     * 涉毒尿检初查结果
     */
    private String sdnjccjg;
    /**
     * 涉毒尿检单位
     */
    private String sdnjdw;
    /**
     * 涉毒尿检初查时间
     */
    private Date sdnjccsj;
    /**
     * 涉毒尿检检查人
     */
    private String sdnjjcr;
    /**
     * 手环ID
     */
    private String shid;
    /**
     * 手环绑定状态
     */
    private String shbdzt;
    /**
     * 手环绑定时间
     */
    private Date sdbdsj;
    /**
     * 是否涉密人员
     */
    private Short sfsm;
    /**
     * 人员代号
     */
    private String rydh;
    /**
     * 涉密原因
     */
    private String smyy;
    /**
     * 涉密备注
     */
    private String smbz;
    /**
     * 救济日期
     */
    private Date jjrq;
    /**
     * 救济原因
     */
    private String jjyy;
    /**
     * 救济领取物品
     */
    private String jjlqwp;
    /**
     * 入所类型
     */
    private String rslx;
    /**
     * 经办人
     */
    private String jbr;
    /**
     * 经办时间
     */
    private Date jbsj;
    /**
     * 办理状态
     */
    private String status;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 办理状态
     */
    private String spzt;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 当前步骤
     */
    private String currentStep;

    @ApiModelProperty("特长(专长)")
    private String tc;
    @ApiModelProperty("联系电话")
    private String lxdh;
    @ApiModelProperty("同案编号")
    private String tabh;
    @ApiModelProperty("办案单位电话")
    private String badwdh;
    @ApiModelProperty("办案人")
    private String bar;
    @ApiModelProperty("办案人联系电话")
    private String barlxdh;
    @ApiModelProperty("羁押日期")
    private Date jyrq;
    @ApiModelProperty("拘留日期")
    private Date jlrq;
    @ApiModelProperty("逮捕日期")
    private Date dbrq;
    @ApiModelProperty("限制会见案件")
    private Short xzhjaj;
    @ApiModelProperty("审批单位")
    private String spdw;
    @ApiModelProperty("审批人")
    private String spr;
    @ApiModelProperty("转入单位")
    private String zrdw;
    @ApiModelProperty("是否重刑犯")
    private String zxf;
    @ApiModelProperty("吸食毒品种类")
    private String xsdpzl;
    @ApiModelProperty("吸毒方式")
    private String xdfs;
    @ApiModelProperty("是否复吸")
    private String sffx;
    @ApiModelProperty("指纹编号")
    private String zwbh;
    @ApiModelProperty("附物编号")
    private String fwbh;
    @ApiModelProperty("性病")
    private String xingbing;
    @ApiModelProperty("本人电话")
    private String brdh;
    @ApiModelProperty("家属联系电话")
    private String jslxdh;
    @ApiModelProperty("其他行政处罚")
    private String qtxzcf;
    @ApiModelProperty("备注(收戒信息)")
    private String sjbz;
    @ApiModelProperty("所内编号")
    private String snbh;
    @ApiModelProperty("自愿戒毒")
    private String zrjd;
    @ApiModelProperty("艾滋病")
    private String azb;
    @ApiModelProperty("档案号")
    private String dah;

    @ApiModelProperty("收押期限")
    private Date syqx;

}
