package com.rs.module.acp.controller.admin.db.vo;

import com.rs.module.acp.controller.admin.pm.vo.BizDataCorrectionDataVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel(description = "看守所收押登记变更提交VO")
@Data
public class DetainRegKssCorrectionSaveVO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("信息变更Id")
    private String applyId;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("变更内容")
    private List<BizDataCorrectionDataVO> data;

}
