package com.rs.module.acp.service.zh;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.zh.vo.*;
import com.rs.module.acp.entity.zh.DutySuperviseRecordSigninDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.zh.DutySuperviseRecordSigninDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 综合管理-值班管理-值班督导记录签至 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DutySuperviseRecordSigninServiceImpl extends BaseServiceImpl<DutySuperviseRecordSigninDao, DutySuperviseRecordSigninDO> implements DutySuperviseRecordSigninService {

    @Resource
    private DutySuperviseRecordSigninDao dutySuperviseRecordSigninDao;

    @Override
    public String createDutySuperviseRecordSignin(DutySuperviseRecordSigninSaveReqVO createReqVO) {
        // 插入
        DutySuperviseRecordSigninDO dutySuperviseRecordSignin = BeanUtils.toBean(createReqVO, DutySuperviseRecordSigninDO.class);
        dutySuperviseRecordSigninDao.insert(dutySuperviseRecordSignin);
        // 返回
        return dutySuperviseRecordSignin.getId();
    }

    @Override
    public void updateDutySuperviseRecordSignin(DutySuperviseRecordSigninSaveReqVO updateReqVO) {
        // 校验存在
        validateDutySuperviseRecordSigninExists(updateReqVO.getId());
        // 更新
        DutySuperviseRecordSigninDO updateObj = BeanUtils.toBean(updateReqVO, DutySuperviseRecordSigninDO.class);
        dutySuperviseRecordSigninDao.updateById(updateObj);
    }

    @Override
    public void deleteDutySuperviseRecordSignin(String id) {
        // 校验存在
        validateDutySuperviseRecordSigninExists(id);
        // 删除
        dutySuperviseRecordSigninDao.deleteById(id);
    }

    private void validateDutySuperviseRecordSigninExists(String id) {
        if (dutySuperviseRecordSigninDao.selectById(id) == null) {
            throw new ServerException("综合管理-值班管理-值班督导记录签至数据不存在");
        }
    }

    @Override
    public DutySuperviseRecordSigninDO getDutySuperviseRecordSignin(String id) {
        return dutySuperviseRecordSigninDao.selectById(id);
    }

    @Override
    public PageResult<DutySuperviseRecordSigninDO> getDutySuperviseRecordSigninPage(DutySuperviseRecordSigninPageReqVO pageReqVO) {
        return dutySuperviseRecordSigninDao.selectPage(pageReqVO);
    }

    @Override
    public List<DutySuperviseRecordSigninDO> getDutySuperviseRecordSigninList(DutySuperviseRecordSigninListReqVO listReqVO) {
        return dutySuperviseRecordSigninDao.selectList(listReqVO);
    }


}
