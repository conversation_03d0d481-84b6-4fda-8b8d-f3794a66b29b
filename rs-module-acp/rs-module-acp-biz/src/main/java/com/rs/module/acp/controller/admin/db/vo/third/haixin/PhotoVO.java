package com.rs.module.acp.controller.admin.db.vo.third.haixin;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PhotoVO implements TransPojo {

    private String id;

    @ApiModelProperty("图片url")
    private String url;

    @ApiModelProperty("强制通过类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_HX_RXCJ_QZTGLX")
    private String forceCode;

    @ApiModelProperty("强制通过原因")
    private String forceReason;

    @ApiModelProperty("人像类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_HX_RXCJ_RXLX")
    private String ryzplxdm;

    @ApiModelProperty("信息质量得分")
    private String xxzldf;


}
