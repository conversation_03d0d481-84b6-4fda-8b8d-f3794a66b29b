package com.rs.module.acp.service.gj.transitionroom;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.gj.vo.transitionroom.TransitionRoomEvalSaveReqVO;
import com.rs.module.acp.entity.gj.TransitionRoomEvalDO;

/**
 * 实战平台-管教业务-过渡考核登记 Service 接口
 *
 * <AUTHOR>
 */
public interface TransitionRoomEvalService extends IBaseService<TransitionRoomEvalDO>{

    /**
     * 获得实战平台-管教业务-过渡考核登记
     *
     * @param id 编号
     * @return 实战平台-管教业务-过渡考核登记
     */
    TransitionRoomEvalDO getTransitionRoomEval(String id);

    /**
     * 考核登记信息
     * <AUTHOR>
     * @date 2025/6/16 17:13
     * @param [createReqVO]
     * @return boolean
     */
    boolean regAssessmentInfo(TransitionRoomEvalSaveReqVO createReqVO);
}
