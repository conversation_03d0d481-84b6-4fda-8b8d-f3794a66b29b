package com.rs.module.acp.dao.wb;

import java.util.*;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.ConsularDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
 * 实战平台-窗口业务-领事外事信息 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface ConsularDao extends IBaseDao<ConsularDO> {


    default PageResult<ConsularDO> selectPage(ConsularPageReqVO reqVO) {
        Page<ConsularDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ConsularDO> wrapper = new LambdaQueryWrapperX<ConsularDO>()
                .likeIfPresent(ConsularDO::getName, reqVO.getName())
                .eqIfPresent(ConsularDO::getGender, reqVO.getGender())
                .eqIfPresent(ConsularDO::getIdType, reqVO.getIdType())
                .eqIfPresent(ConsularDO::getIdNumber, reqVO.getIdNumber())
                .eqIfPresent(ConsularDO::getNationality, reqVO.getNationality())
                .eqIfPresent(ConsularDO::getContact, reqVO.getContact())
                .eqIfPresent(ConsularDO::getWorkUnit, reqVO.getWorkUnit())
                .eqIfPresent(ConsularDO::getImageUrl, reqVO.getImageUrl())
                .eqIfPresent(ConsularDO::getOrgCode, reqVO.getOrgCode());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(ConsularDO::getAddTime);
        }
        Page<ConsularDO> consularPage = selectPage(page, wrapper);
        return new PageResult<>(consularPage.getRecords(), consularPage.getTotal());
    }

    default List<ConsularDO> selectList(ConsularListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ConsularDO>()
                .likeIfPresent(ConsularDO::getName, reqVO.getName())
                .eqIfPresent(ConsularDO::getGender, reqVO.getGender())
                .eqIfPresent(ConsularDO::getIdType, reqVO.getIdType())
                .eqIfPresent(ConsularDO::getIdNumber, reqVO.getIdNumber())
                .eqIfPresent(ConsularDO::getNationality, reqVO.getNationality())
                .eqIfPresent(ConsularDO::getContact, reqVO.getContact())
                .eqIfPresent(ConsularDO::getWorkUnit, reqVO.getWorkUnit())
                .eqIfPresent(ConsularDO::getImageUrl, reqVO.getImageUrl())
                .orderByDesc(ConsularDO::getAddTime));
    }


}
