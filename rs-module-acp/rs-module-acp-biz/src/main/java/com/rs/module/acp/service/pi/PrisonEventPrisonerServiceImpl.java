package com.rs.module.acp.service.pi;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.StringUtil;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventPrisonerListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventPrisonerPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventPrisonerRespVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventPrisonerSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.entity.pi.PrisonEventPrisonerDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.PrisonEventPrisonerDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-巡视管控-所情事件在押人员 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrisonEventPrisonerServiceImpl extends BaseServiceImpl<PrisonEventPrisonerDao, PrisonEventPrisonerDO> implements PrisonEventPrisonerService {

    @Resource
    private PrisonEventPrisonerDao prisonEventPrisonerDao;

    @Override
    public String createPrisonEventPrisoner(PrisonEventPrisonerSaveReqVO createReqVO) {
        // 插入
        PrisonEventPrisonerDO prisonEventPrisoner = BeanUtils.toBean(createReqVO, PrisonEventPrisonerDO.class);
        prisonEventPrisonerDao.insert(prisonEventPrisoner);
        // 返回
        return prisonEventPrisoner.getId();
    }

    @Override
    public void updatePrisonEventPrisoner(PrisonEventPrisonerSaveReqVO updateReqVO) {
        // 校验存在
        validatePrisonEventPrisonerExists(updateReqVO.getId());
        // 更新
        PrisonEventPrisonerDO updateObj = BeanUtils.toBean(updateReqVO, PrisonEventPrisonerDO.class);
        prisonEventPrisonerDao.updateById(updateObj);
    }

    @Override
    public void deletePrisonEventPrisoner(String id) {
        // 校验存在
        validatePrisonEventPrisonerExists(id);
        // 删除
        prisonEventPrisonerDao.deleteById(id);
    }

    private void validatePrisonEventPrisonerExists(String id) {
        if (prisonEventPrisonerDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-所情事件在押人员数据不存在");
        }
    }

    @Override
    public PrisonEventPrisonerDO getPrisonEventPrisoner(String id) {
        return prisonEventPrisonerDao.selectById(id);
    }

    @Override
    public PageResult<PrisonEventPrisonerDO> getPrisonEventPrisonerPage(PrisonEventPrisonerPageReqVO pageReqVO) {
        return prisonEventPrisonerDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrisonEventPrisonerDO> getPrisonEventPrisonerList(PrisonEventPrisonerListReqVO listReqVO) {
        return prisonEventPrisonerDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean savePrisonEventPrisonerList(List<PrisonEventPrisonerSaveReqVO> saveReqVOList, String evenId) {
        List<PrisonEventPrisonerDO> prisonEventPrisonerDOList = BeanUtils.toBean(saveReqVOList,PrisonEventPrisonerDO.class);
        for(PrisonEventPrisonerDO prisonEventPrisonerDO:prisonEventPrisonerDOList){
            prisonEventPrisonerDO.setId(StringUtil.getGuid32());
            prisonEventPrisonerDO.setEventId(evenId);
        }
        return saveBatch(prisonEventPrisonerDOList);
    }

    @Override
    public List<PrisonEventPrisonerRespVO> getPrisonEventPrisonerListByEventId(String eventId) {
        LambdaQueryWrapper<PrisonEventPrisonerDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(PrisonEventPrisonerDO::getEventId,PrisonEventPrisonerDO::getJgrybm,
                PrisonEventPrisonerDO::getJgryxm,PrisonEventPrisonerDO::getDeductPoint,
                PrisonEventPrisonerDO::getShouldDeductPoint);
        lambdaQueryWrapper.eq(PrisonEventPrisonerDO::getEventId,eventId);
        List<PrisonEventPrisonerDO> eventPrisonerDOList = list(lambdaQueryWrapper);
        if(CollectionUtil.isEmpty(eventPrisonerDOList)){
            return new ArrayList<>();
        }
        return BeanUtils.toBean(eventPrisonerDOList,PrisonEventPrisonerRespVO.class);
    }
}
