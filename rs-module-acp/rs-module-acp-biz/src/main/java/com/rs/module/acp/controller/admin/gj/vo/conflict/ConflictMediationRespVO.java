package com.rs.module.acp.controller.admin.gj.vo.conflict;

import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-社会矛盾化解 Response VO")
@Data
public class ConflictMediationRespVO implements TransPojo{

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("事件ID")
    private String eventId;
    @ApiModelProperty("事件编号")
    private String eventCode;
    @ApiModelProperty("主持人")
    private String hostName;
    @ApiModelProperty("主持单位")
    private String hostOrg;
    @ApiModelProperty("调解次数")
    private Integer mediationCnt;
    @ApiModelProperty("调解地点")
    private String mediationLocation;
    @ApiModelProperty("调解情况")
    private String mediationState;
    @ApiModelProperty("调解结果（1：调解成功，2：调解暂缓，3：不用调解）")
    private String mediationResult;
    @ApiModelProperty("矛盾化解结果")
    private String resolutionResult;
    @ApiModelProperty("经验总结")
    private String experienceSummary;
    @ApiModelProperty("调解经办人身份证号")
    private String mediationOperatorSfzh;
    @ApiModelProperty("调解经办人姓名")
    private String mediationOperatorXm;
    @ApiModelProperty("调解经办时间")
    private Date mediationOperatorTime;
    @ApiModelProperty("跟踪回访")
    private String followUpVisit;
    @ApiModelProperty("回访时间")
    private Date followUpTime;
    @ApiModelProperty("回访电话")
    private String followUpPhone;
    @ApiModelProperty("附件地址")
    private String attUrl;

    @ApiModelProperty("相关单位")
    private List<ConflictMediationOrgRespVO> orgReqVOS;
}
