package com.rs.module.acp.controller.admin.db.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-收押业务-入所登记（拘留所）新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class InRecordJlsSaveReqVO extends BaseVO {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
//    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("姓名")
//    @NotEmpty(message = "姓名不能为空")
    private String xm;

    @ApiModelProperty("姓名拼音")
    private String xmpy;

    @ApiModelProperty("别名")
    private String bm;

    @ApiModelProperty("性别")
//    @NotEmpty(message = "性别不能为空")
    private String xb;

    @ApiModelProperty("出生日期")
//    @NotNull(message = "出生日期不能为空")
    private Date csrq;

    @ApiModelProperty("证件类型")
//    @NotEmpty(message = "证件类型不能为空")
    private String zjlx;

    @ApiModelProperty("证件号码")
//    @NotEmpty(message = "证件号码不能为空")
    private String zjhm;

    @ApiModelProperty("国籍")
//    @NotEmpty(message = "国籍不能为空")
    private String gj;

    @ApiModelProperty("民族")
//    @NotEmpty(message = "民族不能为空")
    private String mz;

    @ApiModelProperty("婚姻状况")
//    @NotEmpty(message = "婚姻状况不能为空")
    private String hyzk;

    @ApiModelProperty("籍贯")
//    @NotEmpty(message = "籍贯不能为空")
    private String jg;

    @ApiModelProperty("宗教信仰")
    private String zjxy;

    @ApiModelProperty("户籍地")
//    @NotEmpty(message = "户籍地不能为空")
    private String hjd;

    @ApiModelProperty("户籍地详址")
//    @NotEmpty(message = "户籍地详址不能为空")
    private String hjdxz;

    @ApiModelProperty("现住址")
//    @NotEmpty(message = "现住址不能为空")
    private String xzz;

    @ApiModelProperty("现住址详址")
//    @NotEmpty(message = "现住址详址不能为空")
    private String xzzxz;

    @ApiModelProperty("文化程度")
//    @NotEmpty(message = "文化程度不能为空")
    private String whcd;

    @ApiModelProperty("政治面貌")
//    @NotEmpty(message = "政治面貌不能为空")
    private String zzmm;

    @ApiModelProperty("职业")
    private String zy;

    @ApiModelProperty("工作单位")
    private String gzdw;

    @ApiModelProperty("身份")
//    @NotEmpty(message = "身份不能为空")
    private String sf;

    @ApiModelProperty("特殊身份")
    private String tssf;

    @ApiModelProperty("信息核查")
    private String xxhc;

    @ApiModelProperty("是否为在校学生")
    private String sfwxzxs;

    @ApiModelProperty("学校名称")
    private String xxmc;

    @ApiModelProperty("案由代码")
//    @NotEmpty(message = "案由代码不能为空")
    private String ajlbdm;

    @ApiModelProperty("案件类别名称")
//    @NotEmpty(message = "案件类别名称不能为空")
    private String ajlb;

    @ApiModelProperty("案件编号")
//    @NotEmpty(message = "案件编号不能为空")
    private String ajbh;

    @ApiModelProperty("人员编号")
    private String rybh;

    @ApiModelProperty("案事件相关人员编号")
    private String asjxgrybh;

    @ApiModelProperty("办案中心编号")
    private String bazxbh;

    @ApiModelProperty("送拘日期")
//    @NotNull(message = "送拘日期不能为空")
    private Date sjrq;

    @ApiModelProperty("送拘机关类型")
    private String sjjglx;

    @ApiModelProperty("送拘机关名称")
//    @NotEmpty(message = "送拘机关名称不能为空")
    private String sjjgmc;

    @ApiModelProperty("送拘人姓名1")
//    @NotEmpty(message = "送拘人姓名1不能为空")
    private String sjrxm;

    @ApiModelProperty("送拘人联系电话1")
//    @NotEmpty(message = "送拘人联系电话1不能为空")
    private String sjrlxdh;

    @ApiModelProperty("送拘人姓名2")
    private String sjrxm2;

    @ApiModelProperty("送拘人联系电话2")
    private String sjrlxdh2;

    @ApiModelProperty("送拘民警姓名")
//    @NotEmpty(message = "送拘民警姓名不能为空")
    private String sjmjxm;

    @ApiModelProperty("送拘民警联系电话")
//    @NotEmpty(message = "送拘民警联系电话不能为空")
    private String sjmjxmlxdh;

    @ApiModelProperty("办案单位类型")
//    @NotEmpty(message = "办案单位类型不能为空")
    private String badwlx;

    @ApiModelProperty("办案单位")
//    @NotEmpty(message = "办案单位不能为空")
    private String badw;

    @ApiModelProperty("拘留决定机关类型")
    private String jljdjglx;

    @ApiModelProperty("拘留决定机关名称")
    private String jljdjgmc;

    @ApiModelProperty("收拘凭证")
//    @NotEmpty(message = "收拘凭证不能为空")
    private String sjpz;

    @ApiModelProperty("收拘凭证文书号")
//    @NotEmpty(message = "收拘凭证文书号不能为空")
    private String sjpzwsh;

    @ApiModelProperty("收拘凭证文书号")
    private String sjpzwsdz;

    @ApiModelProperty("档案编号")
//    @NotEmpty(message = "档案编号不能为空")
    private String dabh;

    @ApiModelProperty("回执法律文书号")
    private String hzflwsh;

    @ApiModelProperty("入所原因")
//    @NotEmpty(message = "入所原因不能为空")
    private String rsyy;

    @ApiModelProperty("入所日期")
//    @NotNull(message = "入所日期不能为空")
    private Date rsrq;

    @ApiModelProperty("收拘民警")
//    @NotEmpty(message = "收拘民警不能为空")
    private String sjmj;

    @ApiModelProperty("拘留期限")
//    @NotNull(message = "拘留期限不能为空")
    private Integer jlqx;

    @ApiModelProperty("拘留起始日期")
//    @NotNull(message = "拘留起始日期不能为空")
    private Date jlqsrq;

    @ApiModelProperty("拘留截止日期")
    private Date jljzrq;

    @ApiModelProperty("管理类别")
    private String gllb;

    @ApiModelProperty("监区id")
//    @NotEmpty(message = "监区id不能为空")
    private String areaId;

    @ApiModelProperty("监区名称")
//    @NotEmpty(message = "监区名称不能为空")
    private String areaName;

    @ApiModelProperty("监室号")
//    @NotEmpty(message = "监室号不能为空")
    private String jsh;

    @ApiModelProperty("监室名称")
//    @NotEmpty(message = "监室名称不能为空")
    private String roomName;

    @ApiModelProperty("简要案情")
//    @NotEmpty(message = "简要案情不能为空")
    private String jyaq;

    @ApiModelProperty("同案人，多个逗号分割")
    private String tar;

    @ApiModelProperty("备注")
    private String bz;

    @ApiModelProperty("涉毒尿检初查结果")
    private String sdnjccjg;

    @ApiModelProperty("涉毒尿检单位")
    private String sdnjdw;

    @ApiModelProperty("涉毒尿检初查结果")
    private Date sdnjccsj;

    @ApiModelProperty("涉毒尿检检查人")
    private String sdnjjcr;

    @ApiModelProperty("手环ID")
    private String shid;

    @ApiModelProperty("手环绑定状态")
    private String shbdzt;

    @ApiModelProperty("手环绑定时间")
    private Date sdbdsj;

    @ApiModelProperty("是否涉密人员")
//    @NotNull(message = "是否涉密人员不能为空")
    private Short sfsm;

    @ApiModelProperty("人员代号")
    private String rydh;

    @ApiModelProperty("涉密原因")
    private String smyy;

    @ApiModelProperty("涉密备注")
    private String smbz;

    @ApiModelProperty("救济日期")
//    @NotNull(message = "救济日期不能为空")
    private Date jjrq;

    @ApiModelProperty("救济原因")
//    @NotEmpty(message = "救济原因不能为空")
    private String jjyy;

    @ApiModelProperty("救济领取物品")
    private String jjlqwp;

    @ApiModelProperty("入所类型")
//    @NotEmpty(message = "入所类型不能为空")
    private String rslx;

    @ApiModelProperty("经办人")
//    @NotEmpty(message = "经办人不能为空")
    private String jbr;

    @ApiModelProperty("经办时间")
//    @NotNull(message = "经办时间不能为空")
    private Date jbsj;

    @ApiModelProperty("办理状态")
//    @NotEmpty(message = "办理状态不能为空")
    private String status;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审批状态")
//    @NotEmpty(message = "审批状态不能为空")
    private String spzt;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("当前步骤")
//    @NotEmpty(message = "当前步骤不能为空")
    private String currentStep;

    @ApiModelProperty("社会关系")
    private List<dbSocialRelationsSaveReqVO> socialRelations;
}