package com.rs.module.acp.controller.admin.db.vo.jxjsdj;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-减刑登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class JxjsdjFyshcdReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("法院审核裁定提请日期")
    private Date fyshcdtqrq;

    @ApiModelProperty("法院审核裁定日期")
    private Date fyshcdrq;

    @ApiModelProperty("法院审核裁定结果（1、予以减刑或者假释；2、不予减刑或者假释）")
    private String fyshcdjg;

    @ApiModelProperty("减刑情况")
    private String jxqk;

    @ApiModelProperty("刑期截止日期")
    private Date xqjzrq;

    @ApiModelProperty("法院审核裁定材料")
    private String fyshcdcl;


}
