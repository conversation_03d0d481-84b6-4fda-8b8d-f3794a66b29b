package com.rs.module.acp.service.wb;

import com.alibaba.fastjson.JSONObject;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.dao.wb.WbHomeDao;
import com.rs.module.base.controller.admin.pm.vo.AreaInfoRespVO;
import com.rs.module.base.service.pm.AreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Validated
public class WbHomeServiceImpl implements WbHomeService {

    @Resource
    private WbHomeDao wbHomeDao;

    @Autowired
    private AreaService areaService;

    @Autowired
    private WbCommonService wbCommonService;

    @Override
    public List<JSONObject> getToDayMeetingUpdates() {
        return wbHomeDao.getToDayMeetingUpdates(SessionUserUtil.getSessionUser().getOrgCode());
    }

    @Override
    public JSONObject getMeetingRoomStatus() {

        //TODO 暂时无法判断审讯室、律师会见室、家属会见室的 暂停使用状态，咨询邱文新后：先保留在上面，后面会在【区域管理】里面做这块的配置
        /**
         *  status:状态
         *  0：空闲。1：使用中，2：暂停使用
         */
        JSONObject resJson = new JSONObject();
        //获取审讯室情况
        resJson.put("interrogationRoomSituation",getInterrogationRoomSituation());

        //获取律师会见室情况
        resJson.put("lawyerMeetingRoomSituation",getLawyerMeetingRoomSituation());

        //获取家属会见室情况
        resJson.put("familyRoomSituation",getFamilyRoomSituation());
        return resJson;
    }

    private JSONObject getInterrogationRoomSituation(){
        //获取审讯室情况
        JSONObject roomSituation = new JSONObject();
        int idleCount = 0;
        int inuseCount = 0;
        int pauseCount = 0;
        List<JSONObject> interrogationRoomList = wbCommonService.getIdleInterrogationRoom(true);
        for (JSONObject tria : interrogationRoomList) {
            if(tria.getBoolean("idle")){
                tria.put("status","0");
                tria.put("statusName","空闲");
                idleCount+=1;
            }else {
                tria.put("status","1");
                tria.put("statusName","使用中");
                inuseCount+=1;
            }
        }
        roomSituation.put("idleCount",idleCount);
        roomSituation.put("inuseCount",inuseCount);
        roomSituation.put("pauseCount",pauseCount);
        roomSituation.put("roomList",interrogationRoomList);
        roomSituation.put("roomType","提讯/询室");
        return roomSituation;
    }

    private JSONObject getLawyerMeetingRoomSituation(){
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        List<AreaInfoRespVO> areaList = areaService.getAreaByOrgCode(sessionUser.getOrgCode(), "0016");

        List<JSONObject> meetingList = wbHomeDao.getLawyerMeetingList();

        //如果查看的时间与 律师会见室的时间有重叠，则认为律师会见被占用
        Map<String, String> roomMap = new HashMap<>();
        for (JSONObject meeting : meetingList) {
            roomMap.put(meeting.getString("room_id"), meeting.getString("room_id"));
        }

        JSONObject roomSituation = new JSONObject();
        List<JSONObject> lawyerMeetingRoomList = new ArrayList<>();
        int idleCount = 0;
        int inuseCount = 0;
        int pauseCount = 0;
        for(AreaInfoRespVO area:areaList){
            JSONObject temp = new JSONObject();
            temp.put("roomId",area.getAreaCode());
            temp.put("roomName",area.getAreaName());
            if(roomMap.containsKey(area.getAreaCode())){
                temp.put("status","1");
                temp.put("statusName","使用中");
                inuseCount+=1;
            }else {
                temp.put("status","0");
                temp.put("statusName","空闲");
                idleCount+=1;
            }
            lawyerMeetingRoomList.add(temp);
        }

        roomSituation.put("idleCount",idleCount);
        roomSituation.put("inuseCount",inuseCount);
        roomSituation.put("pauseCount",pauseCount);
        roomSituation.put("roomList",lawyerMeetingRoomList);
        roomSituation.put("roomType","律师会见室");
        return roomSituation;
    }

    private JSONObject getFamilyRoomSituation(){
        //获取家属会见情况
        JSONObject roomSituation = new JSONObject();
        int idleCount = 0;
        int inuseCount = 0;
        int pauseCount = 0;
        List<JSONObject> familyRoomList = wbCommonService.getIdleFamilyRoom(true);
        for (JSONObject tria : familyRoomList) {
            if(tria.getBoolean("idle")){
                tria.put("status","0");
                tria.put("statusName","空闲");
                idleCount+=1;
            }else {
                tria.put("status","1");
                tria.put("statusName","使用中");
                inuseCount+=1;
            }
        }
        roomSituation.put("idleCount",idleCount);
        roomSituation.put("inuseCount",inuseCount);
        roomSituation.put("pauseCount",pauseCount);
        roomSituation.put("roomList",familyRoomList);
        roomSituation.put("roomType","家属会见室");
        return roomSituation;
    }
}
