package com.rs.module.acp.service.pm;

import java.util.*;
import javax.servlet.http.HttpServletRequest;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.UserDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-监管管理-用户 Service 接口
 *
 * <AUTHOR>
 */
public interface UserService extends IBaseService<UserDO>{

    /**
     * 创建实战平台-监管管理-用户
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createUser(@Valid UserSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-用户
     *
     * @param updateReqVO 更新信息
     */
    void updateUser(@Valid UserSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-用户
     *
     * @param id 编号
     */
    void deleteUser(String id);

    /**
     * 获得实战平台-监管管理-用户
     *
     * @param id 编号
     * @return 实战平台-监管管理-用户
     */
    UserDO getUser(String id);


    String mjSave(UserMJSaveReqVO reqVO, HttpServletRequest request) throws Exception;

    String fjSave(UserFJSaveReqVO reqVO, HttpServletRequest request) throws Exception;

    String ywSave(UserYWSaveReqVO reqVO, HttpServletRequest request) throws Exception;

    String gqSave(UserGQSaveReqVO reqVO, HttpServletRequest request) throws Exception;

    void syncToBspUser(UserDO entity, HttpServletRequest request) throws Exception;

    String syncFromToBspUser(String orgId, HttpServletRequest request) throws  Exception;

    void syncToBspUserBatch(String userIds, HttpServletRequest request) throws Exception;

    void deleteUserGq(String id);
}
