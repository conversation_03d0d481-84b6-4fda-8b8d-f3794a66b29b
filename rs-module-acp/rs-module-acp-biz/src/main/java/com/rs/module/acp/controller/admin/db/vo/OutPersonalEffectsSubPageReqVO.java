package com.rs.module.acp.controller.admin.db.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-出所随身物品登记子分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OutPersonalEffectsSubPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("随身物品ID")
    private String personalEffectsId;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("物品名称")
    private String wpmc;

    @ApiModelProperty("种类")
    private String zl;

    @ApiModelProperty("货币种类")
    private String hbzl;

    @ApiModelProperty("现金数量")
    private String sl;

    @ApiModelProperty("物品特征")
    private String wptz;

    @ApiModelProperty("物品照片")
    private String wpzp;

    @ApiModelProperty("位置")
    private String wz;

    @ApiModelProperty("物品处置情况")
    private String wpczqk;

    @ApiModelProperty("现金注入个人帐卡日期")
    private Date xjzrgrzkrq;

    @ApiModelProperty("取出物品数量")
    private Integer qcwpsl;

    @ApiModelProperty("备注")
    private String bz;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
