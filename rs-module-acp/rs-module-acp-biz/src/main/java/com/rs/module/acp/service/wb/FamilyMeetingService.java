package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.FamilyMeetingDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-家属会见登记 Service 接口
 *
 * <AUTHOR>
 */
public interface FamilyMeetingService extends IBaseService<FamilyMeetingDO>{

    /**
     * 创建实战平台-窗口业务-家属会见登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createFamilyMeeting(@Valid FamilyMeetingSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-家属会见登记
     *
     * @param updateReqVO 更新信息
     */
    void updateFamilyMeeting(@Valid FamilyMeetingSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-家属会见登记
     *
     * @param id 编号
     */
    void deleteFamilyMeeting(String id);

    /**
     * 获得实战平台-窗口业务-家属会见登记
     *
     * @param id 编号
     * @return 实战平台-窗口业务-家属会见登记
     */
    FamilyMeetingDO getFamilyMeeting(String id);

    /**
    * 获得实战平台-窗口业务-家属会见登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-家属会见登记分页
    */
    PageResult<FamilyMeetingDO> getFamilyMeetingPage(FamilyMeetingPageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-家属会见登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-家属会见登记列表
    */
    List<FamilyMeetingDO> getFamilyMeetingList(FamilyMeetingListReqVO listReqVO);

    /**
     * 获得实战平台-窗口业务-提家属会见签到
     *
     * @param id 业务ID
     * @param checkInTime 签到时间
     */
    boolean signIn(String id,String checkInTime);
    /**
     * 获得实战平台-窗口业务-分配会见室
     *
     * @param id 编号
     * @param roomId 会见室ID
     */
    boolean allocationRoom(String id,String roomId);

    /**
     * 获得实战平台-窗口业务-带出安检
     *
     * @param updateReqVO
     */
    boolean escortingInspect(FamilyMeetingSaveReqVO updateReqVO);

    /**
     * 获得实战平台-窗口业务-会毕安检
     *
     * @param updateReqVO
     */
    boolean returnInspection(FamilyMeetingSaveReqVO updateReqVO);
    /**
     * 获得实战平台-窗口业务-补录
     *
     * @param updateReqVO
     */
    boolean additionalRecording(FamilyMeetingSaveReqVO updateReqVO);
    /**
     * 实战平台-窗口业务-家属现场会见排号信息
     * @return
     */
    List<JSONObject> getOnSiteNumbering();

    /**
     * 获得实战平台-窗口业务-根据监管人员编码获取监管人员历史会见记录成功
     *
     * @param jgrybm 编号
     * @param pageNo 页码
     * @param pageSize 每页多少
     * @return 实战平台-窗口业务-获取历史律师会见记录
     */
    PageResult<FamilyMeetingRespVO> getHistoryMeetingByJgrybm(String jgrybm,int pageNo,int pageSize);

    /**
     * 获得实战平台-窗口业务-根据ID获得家属会见登记
     * @param id
     * @return
     */
    FamilyMeetingRespVO getFamilyMeetingById(String id);
}
