package com.rs.module.acp.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.StringUtil;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Value;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 智能腕表 清研
 */
public class QingyanLocalsenseHttpUtil {
    @Value("${conf.device.qysh.serverIp:*************}")
    private String serverIp;
    @Value("${conf.device.qysh.username:admin}")
    private String username;
    @Value("${conf.device.qysh.password:#LocalSense}")
    private String password;
    @Value("${conf.device.qysh.webPort:8180}")
    private String webPort;
    private final String LOGIN_URI = "/localsense/login";
    private final String salt = "abcdefghijklmnopqrstuvwxyz20191107salt";
    /**
     * 告警查询 url
     */
    private final String QUERY_ALARM_URI = "/localsense/alarm/getAlarmByParam";
    private String concatUrl(String url){
        serverIp = "*************";
        webPort = "8180";
        username = "admin";
        password = "#LocalSense";
        return "http://" +serverIp+":"+webPort+url;
    }
    private Map<String, String> initHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Accept", "application/json, text/plain, */*");
        headers.put("Accept-Language", "zh-CN,zh;q=0.9");
        headers.put("Connection", "keep-alive");
        headers.put("Content-Type", ContentType.JSON.getValue());
        return headers;
    }
    public String login() throws  Exception{
        String url = concatUrl(LOGIN_URI);
        JSONObject requestBody = new JSONObject();
        requestBody.put("username", "admin");
        //requestBody.put("password", "ca269f1717955a7f16c9b4f2b2c6aebe");
        requestBody.put("password", getPassWordMD5());

        // 发送请求
        String responseStr = HttpRequest.post(url)
                .addHeaders(initHeaders())
                .body(requestBody.toString())
                .timeout(30000)  // 30秒超时
                .execute()
                .body();
        JSONObject jsonObject = JSONObject.parseObject(responseStr);
        if(jsonObject.getInteger("code") != 200){
            throw new Exception("登录失败！" +jsonObject.toJSONString()) ;
        }
        return jsonObject.getString("data");
    }
    public JSONObject getAlarmByParam(String alarmType,String areaId,String beginTime,String endTime,Integer num,Integer page,String state)throws  Exception{
        // 获取token
        String token = null;
        try {
            token = login();
        } catch (Exception e) {
            throw e;
        }
        Map<String, Object> paramMap = initParams(alarmType, areaId, beginTime, endTime, num, page, state);
        Map<String, String> headers = initHeaders();
        headers.put("token", token);
        String url = concatUrl(QUERY_ALARM_URI);
        HttpResponse response = HttpRequest.post(url)
                .addHeaders(headers)
                .header("Content-Type", ContentType.JSON.getValue())  // 设置JSON内容类型
                .body(JSONObject.toJSONString(paramMap))  // 使用JSON格式的请求体
                .timeout(30000)  // 设置超时时间30秒
                .execute();
        String responseStr = response.body();
        JSONObject jsonObject = JSONObject.parseObject(responseStr);

        if (jsonObject.getInteger("code") != 200) {
            throw new Exception("查询失败！" + jsonObject.toJSONString());
        }
        return jsonObject.getJSONObject("data");
    }
    private Map<String, Object> initParams(String alarmType,String areaId,String beginTime,String endTime,Integer num,Integer page,String state){
         /*alarmType string 可选 告警类型 详见附录-数据字典-告警类型字典
        alarmTypes array[string] 告警类型集合 可选
        areaId string 区域id 可选
        beginTime integer <int64> 必需 开始时间 时间戳(毫秒)
                endTime integer <int64> 必需 结束时间 时间戳(毫秒)
                num integer <int32> 每页条数 必需 示例值: 10
        nums array[string] 对象编号集合 可选
        page integer <int32> 页号 必需 示例值: 1
        state string 可选 状态 0-未处理 1-已处理
        type string 可选 类型,查询告警 不需要*/
        if(num == null) num = 20;
        if(page == null) page = 1;
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("alarmType", alarmType);
        paramMap.put("areaId", areaId);
        //当前传入时间yyyy-MM-dd HH:mm:ss 转换为时间戳
        // 修改时间转换逻辑
        String beginTimeStr;
        if (StringUtil.isNotEmpty(beginTime)) {
            Date startDateTime = DateUtil.parse(beginTime, "yyyy-MM-dd HH:mm:ss");
            beginTimeStr = String.valueOf(startDateTime.getTime());
        } else {
            // 默认值：当日的开始时间（00:00:00）
            LocalDateTime startOfDay = LocalDateTimeUtil.beginOfDay(LocalDateTime.now());
            beginTimeStr = String.valueOf(LocalDateTimeUtil.toEpochMilli(startOfDay));
        }
        paramMap.put("beginTime", beginTimeStr);

        String endTimeStr;
        if (StringUtil.isNotEmpty(endTime)) {
            Date endDateTime = DateUtil.parse(endTime, "yyyy-MM-dd HH:mm:ss");
            endTimeStr = String.valueOf(endDateTime.getTime());
        } else {
            // 默认值：当前时间
            endTimeStr = String.valueOf(System.currentTimeMillis());
        }
        paramMap.put("endTime", endTimeStr);
        paramMap.put("num", num);
        paramMap.put("page", page);
        paramMap.put("state", state);
        return paramMap;
    }
    private String getPassWordMD5(){
        String temp = Hex.encodeHexString(DigestUtils.md5(password));
        return Hex.encodeHexString(DigestUtils.md5(temp+salt));
    }
   /* //查询体征数据
    private final String QUERY_TZ_DATA = "localsense/heartRate/obtainPhysicalData";
    private final String QUERY_TZ_TYPE = "1,2,3";
    public  Map<String, QYLocalsensePhysicalDataVO> obtainPhysicalData(String startTime,String endTime,String type,String queryNum) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("startTime", startTime);
        paramMap.put("endTime", endTime);
        if(StringUtil.isEmpty(type)) type = QUERY_TZ_TYPE;
        paramMap.put("type", type);
        paramMap.put("num", queryNum);
        String responseStr = HttpUtil.get(QUERY_TZ_DATA,paramMap);
        JSONObject jsonObject = JSONObject.parseObject(responseStr);
        if(jsonObject.getInteger("code") != 200){
            return null;
        }
        JSONObject resultData = jsonObject.getJSONObject("data");
        //血氧数据
        JSONArray bloodOxygenList = resultData.getJSONArray("bloodOxygenList");
        //心率数据
        JSONArray heartRateList = resultData.getJSONArray("heartRateList");
        //体温数据
        JSONArray temperatureList = resultData.getJSONArray("temperatureList");
        // 创建一个 map 来存储每个 num 对应的 QYLocalsensePhysicalDataVO 对象
        Map<String, QYLocalsensePhysicalDataVO> dataMap = new HashMap<>();

        // 处理血氧数据
        for (int i = 0; i < bloodOxygenList.size(); i++) {
            JSONObject bloodOxygenItem = bloodOxygenList.getJSONObject(i);
            String num = bloodOxygenItem.getString("num");
            Integer data = bloodOxygenItem.getInteger("data");

            dataMap.computeIfAbsent(num, k -> new QYLocalsensePhysicalDataVO(num)).setBloodOxygen(data);
        }

        // 处理心率数据
        for (int i = 0; i < heartRateList.size(); i++) {
            JSONObject heartRateItem = heartRateList.getJSONObject(i);
            String num = heartRateItem.getString("num");
            Integer data = heartRateItem.getInteger("data");

            dataMap.computeIfAbsent(num, k -> new QYLocalsensePhysicalDataVO(num)).setHeartRate(data);
        }

        // 处理体温数据
        for (int i = 0; i < temperatureList.size(); i++) {
            JSONObject temperatureItem = temperatureList.getJSONObject(i);
            String num = temperatureItem.getString("num");
            Integer data = temperatureItem.getInteger("data");

            dataMap.computeIfAbsent(num, k -> new QYLocalsensePhysicalDataVO(num)).setTemperature(data);
        }
        return dataMap;
    }*/
    public static void main(String[] args) {

        QingyanLocalsenseHttpUtil util = new QingyanLocalsenseHttpUtil();
        JSONObject result = null;
        try {
            result = util.getAlarmByParam(null, null, null, null, null, null, null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        System.out.println(result);
        /*String aa = "#LocalSense";
        String salt = "abcdefghijklmnopqrstuvwxyz20191107salt";
        String a = Hex.encodeHexString(DigestUtils.md5(aa));
        String b = Hex.encodeHexString(DigestUtils.md5(a+salt));
        System.out.println(b);*/
        //ca269f1717955a7f16c9b4f2b2c6aebe
    }
}
