package com.rs.module.acp.controller.app.sys.vo;

import java.util.Date;

import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-语音播报-待播报任务列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AppVbTaskListReqVO extends BaseVO {
	
    @ApiModelProperty("播报监所Id")
    private String vbPrisonId;

    @ApiModelProperty("播报监区Id")
    private String vbAreaId;

    @ApiModelProperty("播报监室Id")
    private String vbRoomId;

    @ApiModelProperty("播报设备序列号")
    private String vbSerialNumber;
    
    @ApiModelProperty("播报类型(0:定时,1:实时)")
    private Short vbType;

    @ApiModelProperty("播报名称")
    private String vbName;

    @ApiModelProperty("播报内容")
    private String content;

    @ApiModelProperty("播报次数")
    private Short vbNum;

    @ApiModelProperty("优先级")
    private Short priority;

    @ApiModelProperty("播报开始时间")
    private Date[] startTime;

    @ApiModelProperty("播报结束时间")
    private Date[] endTime;
}
