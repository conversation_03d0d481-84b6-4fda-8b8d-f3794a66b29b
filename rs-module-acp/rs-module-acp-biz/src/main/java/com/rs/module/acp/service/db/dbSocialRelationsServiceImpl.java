package com.rs.module.acp.service.db;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.dbSocialRelationsDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.db.dbSocialRelationsDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-收押业务-社会关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class dbSocialRelationsServiceImpl extends BaseServiceImpl<dbSocialRelationsDao, dbSocialRelationsDO> implements dbSocialRelationsService {

    @Resource
    private dbSocialRelationsDao dbSocialRelationsDao;

    @Override
    public String createdbSocialRelations(dbSocialRelationsSaveReqVO createReqVO) {
        // 插入
        dbSocialRelationsDO dbSocialRelations = BeanUtils.toBean(createReqVO, dbSocialRelationsDO.class);
        dbSocialRelations.setId(UUID.randomUUID().toString().replaceAll("-", ""));
        dbSocialRelations.setJgrybm(dbSocialRelations.getRybh());
        dbSocialRelationsDao.insert(dbSocialRelations);
        // 返回
        return dbSocialRelations.getId();
    }

    @Override
    public void updatedbSocialRelations(dbSocialRelationsSaveReqVO updateReqVO) {
        // 校验存在
        validatedbSocialRelationsExists(updateReqVO.getId());
        // 更新
        dbSocialRelationsDO updateObj = BeanUtils.toBean(updateReqVO, dbSocialRelationsDO.class);
        dbSocialRelationsDao.updateById(updateObj);
    }

    @Override
    public void deletedbSocialRelations(String id) {
        // 校验存在
        validatedbSocialRelationsExists(id);
        // 删除
        dbSocialRelationsDao.deleteById(id);
    }

    private void validatedbSocialRelationsExists(String id) {
        if (dbSocialRelationsDao.selectById(id) == null) {
            throw new ServerException("实战平台-收押业务-社会关系数据不存在");
        }
    }

    @Override
    public dbSocialRelationsDO getdbSocialRelations(String id) {
        return dbSocialRelationsDao.selectById(id);
    }

    @Override
    public PageResult<dbSocialRelationsDO> getdbSocialRelationsPage(dbSocialRelationsPageReqVO pageReqVO) {
        return dbSocialRelationsDao.selectPage(pageReqVO);
    }

    @Override
    public List<dbSocialRelationsDO> getdbSocialRelationsList(dbSocialRelationsListReqVO listReqVO) {
        return dbSocialRelationsDao.selectList(listReqVO);
    }


}
