package com.rs.module.acp.service.gj.civilizedroom;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.*;
import com.rs.module.acp.dao.gj.CivilizedRoomDao;
import com.rs.module.acp.entity.gj.CivilizedRoomDO;
import com.rs.module.acp.entity.gj.CivilizedRoomDetailDO;
import com.rs.module.acp.enums.gj.CivilizedRoomStatusEnum;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomRespVO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.util.ThreadPoolUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;


/**
 * 实战平台-管教业务-文明监室登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CivilizedRoomServiceImpl extends BaseServiceImpl<CivilizedRoomDao, CivilizedRoomDO> implements CivilizedRoomService {

    @Resource
    private CivilizedRoomDao civilizedRoomDao;

    @Resource
    private CivilizedRoomDetailService civilizedRoomDetailService;

    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;

    private static final String defKey = "yonghudanweilindao";

    @Override
    public String createCivilizedRoom(CivilizedRoomSaveReqVO createReqVO) {
        // 插入
        CivilizedRoomDO civilizedRoom = BeanUtils.toBean(createReqVO, CivilizedRoomDO.class);
        civilizedRoomDao.insert(civilizedRoom);
        // 返回
        return civilizedRoom.getId();
    }

    @Override
    public void updateCivilizedRoom(CivilizedRoomSaveReqVO updateReqVO) {
        // 校验存在
        validateCivilizedRoomExists(updateReqVO.getId());
        // 更新
        CivilizedRoomDO updateObj = BeanUtils.toBean(updateReqVO, CivilizedRoomDO.class);
        civilizedRoomDao.updateById(updateObj);
    }

    @Override
    public void deleteCivilizedRoom(String id) {
        // 校验存在
        validateCivilizedRoomExists(id);
        // 删除
        civilizedRoomDao.deleteById(id);
    }

    private void validateCivilizedRoomExists(String id) {
        if (civilizedRoomDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-文明监室登记数据不存在");
        }
    }

    @Override
    public CivilizedRoomRespVO getCivilizedRoom(String id) {
        CivilizedRoomDO civilizedRoomDO = civilizedRoomDao.selectById(id);
        CivilizedRoomRespVO civilizedRoomRespVO = BeanUtils.toBean(civilizedRoomDO, CivilizedRoomRespVO.class);
        if (Objects.nonNull(civilizedRoomRespVO)) {
            LambdaQueryWrapper<CivilizedRoomDetailDO> wrapper = Wrappers.lambdaQuery(CivilizedRoomDetailDO.class).eq(CivilizedRoomDetailDO::getCivilizedRoomId, id);
            wrapper.orderByAsc(CivilizedRoomDetailDO::getAddTime);
            civilizedRoomRespVO.setList(BeanUtils.toBean(civilizedRoomDetailService.list(wrapper), CivilizedRoomDetailRespVO.class));
        }
        return civilizedRoomRespVO;
    }

    @Override
    public PageResult<CivilizedRoomDO> getCivilizedRoomPage(CivilizedRoomPageReqVO pageReqVO) {
        return civilizedRoomDao.selectPage(pageReqVO);
    }

    @Override
    public List<CivilizedRoomRespVO> getCivilizedRoomList(CivilizedRoomListReqVO listReqVO) {
        List<CivilizedRoomDO> civilizedRoomDOS = civilizedRoomDao.selectList(listReqVO);
        List<CivilizedRoomRespVO> civilizedRoomRespVOS = BeanUtils.toBean(civilizedRoomDOS, CivilizedRoomRespVO.class);
        if (CollectionUtil.isNotEmpty(civilizedRoomRespVOS)) {
            List<String> ids = civilizedRoomDOS.stream().map(CivilizedRoomDO::getId).collect(Collectors.toList());
            LambdaQueryWrapper<CivilizedRoomDetailDO> wrapper = Wrappers.lambdaQuery(CivilizedRoomDetailDO.class).in(CivilizedRoomDetailDO::getCivilizedRoomId, ids);
            wrapper.orderByAsc(CivilizedRoomDetailDO::getAddTime);
            Map<String, List<CivilizedRoomDetailRespVO>> map = new HashMap<>();
            List<CivilizedRoomDetailRespVO> civilizedRoomDetailRespVOS = BeanUtils.toBean(civilizedRoomDetailService.list(wrapper), CivilizedRoomDetailRespVO.class);
            if (CollectionUtils.isNotEmpty(civilizedRoomDetailRespVOS)) {
                Set<String> set = new HashSet<>();
                for (CivilizedRoomDetailRespVO detailRespVO : civilizedRoomDetailRespVOS) {
                    set.add(detailRespVO.getRoomId());
                    List<CivilizedRoomDetailRespVO> tempList = map.get(detailRespVO.getCivilizedRoomId());
                    if (CollectionUtil.isEmpty(tempList)) {
                        tempList = new ArrayList<>();
                        map.put(detailRespVO.getCivilizedRoomId(), tempList);
                    }
                    tempList.add(detailRespVO);
                }
                List<AreaPrisonRoomDO> areaPrisonRoomList = areaPrisonRoomService.getAreaPrisonRoomList(new ArrayList<>(set));
                List<AreaPrisonRoomRespVO> roomList = BeanUtils.toBean(areaPrisonRoomList, AreaPrisonRoomRespVO.class);
                Map<String, AreaPrisonRoomRespVO> roomMap = new HashMap<>();
                if (CollectionUtil.isNotEmpty(roomList)) {
                    for (AreaPrisonRoomRespVO areaPrisonRoomRespVO : roomList) {
                        roomMap.put(areaPrisonRoomRespVO.getId(), areaPrisonRoomRespVO);
                    }
                }

                for (CivilizedRoomDetailRespVO civilizedRoomDetailRespVO : civilizedRoomDetailRespVOS) {
                    civilizedRoomDetailRespVO.setAreaPrisonRoomRespVO(roomMap.get(civilizedRoomDetailRespVO.getRoomId()));
                }

            }
            for (CivilizedRoomRespVO civilizedRoomRespVO : civilizedRoomRespVOS) {
                civilizedRoomRespVO.setList(map.get(civilizedRoomRespVO.getId()));
            }
        }
        return civilizedRoomRespVOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void registEvaluation(CivilizedRoomRegistEvaluationReqVO reqVO) {
        CivilizedRoomDO civilizedRoomDO = civilizedRoomDao.selectById(reqVO.getId());
        if (civilizedRoomDO == null) {
            throw new ServerException("实战平台-管教业务-文明监室登记数据不存在");
        }
        if (CollectionUtil.isEmpty(reqVO.getList())) {
            throw new ServerException("登记信息不能为空");
        }
        if (!CivilizedRoomStatusEnum.DPB.getCode().equals(civilizedRoomDO.getStatus())) {
            throw new ServerException("非待评比状态，不允许进行信息登记");
        }
        StringBuilder sb = new StringBuilder();
        for (CivilizedRoomDetailDO civilizedRoomDetailDO : reqVO.getList()) {
            civilizedRoomDetailDO.setCivilizedRoomId(reqVO.getId());
            sb.append(civilizedRoomDetailDO.getRoomName()).append("、");
        }
        String roomName = sb.toString();
        if (roomName.endsWith("、")) {
            roomName = roomName.substring(0, roomName.length() - 1);
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        civilizedRoomDO.setApplyTime(new Date());
        civilizedRoomDO.setApplyUser(sessionUser.getName());
        civilizedRoomDO.setApplyUserSfzh(sessionUser.getIdCard());
        civilizedRoomDO.setRoomName(roomName);
        // 提交申请
        //启动流程审批
        String msgUrl = "/#/discipline/civilizedCell";
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", reqVO.getId());
        variables.put("roomName", roomName);
        JSONObject result = BspApprovalUtil.defaultStartProcess(HttpUtils.getAppCode() + "-" + defKey, reqVO.getId(),
                "文明监室评比流程审批", msgUrl, variables);
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            civilizedRoomDO.setActInstId(bpmTrail.getString("actInstId"));
            civilizedRoomDO.setTaskId(bpmTrail.getString("taskId"));
            civilizedRoomDO.setStatus(CivilizedRoomStatusEnum.DSP.getCode());
            civilizedRoomDetailService.saveBatch(reqVO.getList());
            civilizedRoomDao.updateById(civilizedRoomDO);
        } else {
            throw new ServerException("流程启动失败");
        }

    }

    @Override
    public void approval(CivilizedRoomApprovalReqVO reqVO) {
        CivilizedRoomDO civilizedRoomDO = civilizedRoomDao.selectById(reqVO.getId());
        if (civilizedRoomDO == null) {
            throw new ServerException("实战平台-管教业务-文明监室登记数据不存在");
        }
        if (!CivilizedRoomStatusEnum.DSP.getCode().equals(civilizedRoomDO.getStatus())) {
            throw new ServerException("当前非待审批状态不能进行审批");
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(civilizedRoomDO.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }
        BspApproceStatusEnum bspApproceStatusEnum;
        if (StringUtils.equals(CivilizedRoomStatusEnum.PBWC.getCode(), reqVO.getApprovelResult())) {
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED_END;
        } else if (StringUtils.equals(CivilizedRoomStatusEnum.DCXPB.getCode(), reqVO.getApprovelResult())) {
            bspApproceStatusEnum = BspApproceStatusEnum.REJECT;
        } else {
            throw new ServerException("非法审批状态：" + reqVO.getApprovelResult());
        }
        civilizedRoomDO.setStatus(reqVO.getApprovelResult());

        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", civilizedRoomDO.getId());
        variables.put("roomName", civilizedRoomDO.getRoomName());
        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getId());
        nowApproveUser.put("name", sessionUser.getName());
        boolean terminateTask = BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode();
        JSONObject result = BspApprovalUtil.approvalProcess(HttpUtils.getAppCode() + "-" + defKey, civilizedRoomDO.getActInstId(), civilizedRoomDO.getTaskId(), civilizedRoomDO.getId(),
                bspApproceStatusEnum, reqVO.getApprovelComment(), null, null, terminateTask,
                variables, nowApproveUser, HttpUtils.getAppCode());
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            civilizedRoomDO.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程启动失败");
        }
        civilizedRoomDao.updateById(civilizedRoomDO);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void againEvaluation(CivilizedRoomRegistEvaluationReqVO reqVO) {
        CivilizedRoomDO civilizedRoomDO = civilizedRoomDao.selectById(reqVO.getId());
        if (civilizedRoomDO == null) {
            throw new ServerException("实战平台-管教业务-文明监室登记数据不存在");
        }
        if (!CivilizedRoomStatusEnum.DCXPB.getCode().equals(civilizedRoomDO.getStatus())) {
            throw new ServerException("当前非待重新评比状态，不能进行重新评比操作");
        }
        StringBuilder sb = new StringBuilder();
        for (CivilizedRoomDetailDO civilizedRoomDetailDO : reqVO.getList()) {
            civilizedRoomDetailDO.setCivilizedRoomId(reqVO.getId());
            sb.append(civilizedRoomDetailDO.getRoomName()).append("、");
        }
        String roomName = sb.toString();
        if (roomName.endsWith("、")) {
            roomName = roomName.substring(0, roomName.length() - 1);
        }
        civilizedRoomDO.setRoomName(roomName);
        civilizedRoomDetailService.remove(Wrappers.lambdaQuery(CivilizedRoomDetailDO.class)
                .eq(CivilizedRoomDetailDO::getCivilizedRoomId, civilizedRoomDO.getId()));

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", civilizedRoomDO.getId());
        variables.put("roomName", civilizedRoomDO.getRoomName());
        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getId());
        nowApproveUser.put("name", sessionUser.getName());
        JSONObject result = BspApprovalUtil.approvalProcess(HttpUtils.getAppCode() + "-" + defKey,
                civilizedRoomDO.getActInstId(), civilizedRoomDO.getTaskId(), civilizedRoomDO.getId(),
                BspApproceStatusEnum.PASSED, BspApproceStatusEnum.PASSED.getName(), null, null, false,
                variables, nowApproveUser, HttpUtils.getAppCode());
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            civilizedRoomDO.setTaskId(bpmTrail.getString("taskId"));
            civilizedRoomDO.setStatus(CivilizedRoomStatusEnum.DSP.getCode());
        } else {
            throw new ServerException("流程启动失败");
        }
        civilizedRoomDetailService.saveBatch(reqVO.getList());
        civilizedRoomDao.updateById(civilizedRoomDO);
    }

}
