package com.rs.module.acp.dao.db;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.db.OutHospitalDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-收押业务-出所就医 Dao
*
* <AUTHOR>
*/
@Mapper
public interface OutHospitalDao extends IBaseDao<OutHospitalDO> {


    default PageResult<OutHospitalDO> selectPage(OutHospitalPageReqVO reqVO) {
        Page<OutHospitalDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<OutHospitalDO> wrapper = new LambdaQueryWrapperX<OutHospitalDO>()
            .eqIfPresent(OutHospitalDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(OutHospitalDO::getJgryxm, reqVO.getJgryxm())
            .betweenIfPresent(OutHospitalDO::getAppointmentTime, reqVO.getAppointmentTime())
            .eqIfPresent(OutHospitalDO::getHospital, reqVO.getHospital())
            .eqIfPresent(OutHospitalDO::getSymptomDesc, reqVO.getSymptomDesc())
            .eqIfPresent(OutHospitalDO::getRemark, reqVO.getRemark())
            .eqIfPresent(OutHospitalDO::getCpywzr, reqVO.getCpywzr())
            .eqIfPresent(OutHospitalDO::getCpywzrxm, reqVO.getCpywzrxm())
            .eqIfPresent(OutHospitalDO::getJbr, reqVO.getJbr())
            .eqIfPresent(OutHospitalDO::getJbsj, reqVO.getJbsj())
            .eqIfPresent(OutHospitalDO::getYwzryj, reqVO.getYwzryj())
            .eqIfPresent(OutHospitalDO::getYwzrbz, reqVO.getYwzrbz())
            .eqIfPresent(OutHospitalDO::getYwzrsfzh, reqVO.getYwzrsfzh())
            .eqIfPresent(OutHospitalDO::getYwzrxm, reqVO.getYwzrxm())
            .eqIfPresent(OutHospitalDO::getYwzryjJbr, reqVO.getYwzryjJbr())
            .eqIfPresent(OutHospitalDO::getYwzryjJbsj, reqVO.getYwzryjJbsj())
            .eqIfPresent(OutHospitalDO::getYjdw, reqVO.getYjdw())
            .eqIfPresent(OutHospitalDO::getYjmj, reqVO.getYjmj())
            .eqIfPresent(OutHospitalDO::getYjfj, reqVO.getYjfj())
            .eqIfPresent(OutHospitalDO::getYwry, reqVO.getYwry())
            .eqIfPresent(OutHospitalDO::getDzjkbh, reqVO.getDzjkbh())
            .eqIfPresent(OutHospitalDO::getYjcph, reqVO.getYjcph())
            .eqIfPresent(OutHospitalDO::getZfjly, reqVO.getZfjly())
            .eqIfPresent(OutHospitalDO::getAqgkcs, reqVO.getAqgkcs())
            .eqIfPresent(OutHospitalDO::getSfxyybd, reqVO.getSfxyybd())
            .eqIfPresent(OutHospitalDO::getCfsj, reqVO.getCfsj())
            .eqIfPresent(OutHospitalDO::getJhdd, reqVO.getJhdd())
            .eqIfPresent(OutHospitalDO::getZyrysl, reqVO.getZyrysl())
            .eqIfPresent(OutHospitalDO::getMjrs, reqVO.getMjrs())
            .eqIfPresent(OutHospitalDO::getNybs, reqVO.getNybs())
            .eqIfPresent(OutHospitalDO::getDddd, reqVO.getDddd())
            .eqIfPresent(OutHospitalDO::getWyyy, reqVO.getWyyy())
            .eqIfPresent(OutHospitalDO::getYjlx, reqVO.getYjlx())
            .eqIfPresent(OutHospitalDO::getTbyq, reqVO.getTbyq())
            .eqIfPresent(OutHospitalDO::getSfghjj, reqVO.getSfghjj())
            .eqIfPresent(OutHospitalDO::getSfkqdzjk, reqVO.getSfkqdzjk())
            .eqIfPresent(OutHospitalDO::getSfkqzfjly, reqVO.getSfkqzfjly())
            .eqIfPresent(OutHospitalDO::getQwapJbr, reqVO.getQwapJbr())
            .eqIfPresent(OutHospitalDO::getQwapJbsj, reqVO.getQwapJbsj())
            .eqIfPresent(OutHospitalDO::getHsYjdw, reqVO.getHsYjdw())
            .eqIfPresent(OutHospitalDO::getHsYjmj, reqVO.getHsYjmj())
            .eqIfPresent(OutHospitalDO::getHsYjfj, reqVO.getHsYjfj())
            .eqIfPresent(OutHospitalDO::getHsYwry, reqVO.getHsYwry())
            .eqIfPresent(OutHospitalDO::getHsDzjkbh, reqVO.getHsDzjkbh())
            .eqIfPresent(OutHospitalDO::getHsYjcph, reqVO.getHsYjcph())
            .eqIfPresent(OutHospitalDO::getHsZfjly, reqVO.getHsZfjly())
            .eqIfPresent(OutHospitalDO::getHsAqgkcs, reqVO.getHsAqgkcs())
            .eqIfPresent(OutHospitalDO::getHsSfghjj, reqVO.getHsSfghjj())
            .eqIfPresent(OutHospitalDO::getHsSfkqdzjk, reqVO.getHsSfkqdzjk())
            .eqIfPresent(OutHospitalDO::getHsSfkqzfjly, reqVO.getHsSfkqzfjly())
            .eqIfPresent(OutHospitalDO::getSfhsqr, reqVO.getSfhsqr())
            .eqIfPresent(OutHospitalDO::getHsqrbz, reqVO.getHsqrbz())
            .eqIfPresent(OutHospitalDO::getHsQwapJbr, reqVO.getHsQwapJbr())
            .eqIfPresent(OutHospitalDO::getHsQwapJbsj, reqVO.getHsQwapJbsj())
            .eqIfPresent(OutHospitalDO::getStatus, reqVO.getStatus())
            .eqIfPresent(OutHospitalDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(OutHospitalDO::getTaskId, reqVO.getTaskId())
            .eqIfPresent(OutHospitalDO::getCurrentStep, reqVO.getCurrentStep())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(OutHospitalDO::getAddTime);
        }
        Page<OutHospitalDO> outHospitalPage = selectPage(page, wrapper);
        return new PageResult<>(outHospitalPage.getRecords(), outHospitalPage.getTotal());
    }
    default List<OutHospitalDO> selectList(OutHospitalListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<OutHospitalDO>()
            .eqIfPresent(OutHospitalDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(OutHospitalDO::getJgryxm, reqVO.getJgryxm())
            .betweenIfPresent(OutHospitalDO::getAppointmentTime, reqVO.getAppointmentTime())
            .eqIfPresent(OutHospitalDO::getHospital, reqVO.getHospital())
            .eqIfPresent(OutHospitalDO::getSymptomDesc, reqVO.getSymptomDesc())
            .eqIfPresent(OutHospitalDO::getRemark, reqVO.getRemark())
            .eqIfPresent(OutHospitalDO::getCpywzr, reqVO.getCpywzr())
            .eqIfPresent(OutHospitalDO::getCpywzrxm, reqVO.getCpywzrxm())
            .eqIfPresent(OutHospitalDO::getJbr, reqVO.getJbr())
            .eqIfPresent(OutHospitalDO::getJbsj, reqVO.getJbsj())
            .eqIfPresent(OutHospitalDO::getYwzryj, reqVO.getYwzryj())
            .eqIfPresent(OutHospitalDO::getYwzrbz, reqVO.getYwzrbz())
            .eqIfPresent(OutHospitalDO::getYwzrsfzh, reqVO.getYwzrsfzh())
            .eqIfPresent(OutHospitalDO::getYwzrxm, reqVO.getYwzrxm())
            .eqIfPresent(OutHospitalDO::getYwzryjJbr, reqVO.getYwzryjJbr())
            .eqIfPresent(OutHospitalDO::getYwzryjJbsj, reqVO.getYwzryjJbsj())
            .eqIfPresent(OutHospitalDO::getYjdw, reqVO.getYjdw())
            .eqIfPresent(OutHospitalDO::getYjmj, reqVO.getYjmj())
            .eqIfPresent(OutHospitalDO::getYjfj, reqVO.getYjfj())
            .eqIfPresent(OutHospitalDO::getYwry, reqVO.getYwry())
            .eqIfPresent(OutHospitalDO::getDzjkbh, reqVO.getDzjkbh())
            .eqIfPresent(OutHospitalDO::getYjcph, reqVO.getYjcph())
            .eqIfPresent(OutHospitalDO::getZfjly, reqVO.getZfjly())
            .eqIfPresent(OutHospitalDO::getAqgkcs, reqVO.getAqgkcs())
            .eqIfPresent(OutHospitalDO::getSfxyybd, reqVO.getSfxyybd())
            .eqIfPresent(OutHospitalDO::getCfsj, reqVO.getCfsj())
            .eqIfPresent(OutHospitalDO::getJhdd, reqVO.getJhdd())
            .eqIfPresent(OutHospitalDO::getZyrysl, reqVO.getZyrysl())
            .eqIfPresent(OutHospitalDO::getMjrs, reqVO.getMjrs())
            .eqIfPresent(OutHospitalDO::getNybs, reqVO.getNybs())
            .eqIfPresent(OutHospitalDO::getDddd, reqVO.getDddd())
            .eqIfPresent(OutHospitalDO::getWyyy, reqVO.getWyyy())
            .eqIfPresent(OutHospitalDO::getYjlx, reqVO.getYjlx())
            .eqIfPresent(OutHospitalDO::getTbyq, reqVO.getTbyq())
            .eqIfPresent(OutHospitalDO::getSfghjj, reqVO.getSfghjj())
            .eqIfPresent(OutHospitalDO::getSfkqdzjk, reqVO.getSfkqdzjk())
            .eqIfPresent(OutHospitalDO::getSfkqzfjly, reqVO.getSfkqzfjly())
            .eqIfPresent(OutHospitalDO::getQwapJbr, reqVO.getQwapJbr())
            .eqIfPresent(OutHospitalDO::getQwapJbsj, reqVO.getQwapJbsj())
            .eqIfPresent(OutHospitalDO::getHsYjdw, reqVO.getHsYjdw())
            .eqIfPresent(OutHospitalDO::getHsYjmj, reqVO.getHsYjmj())
            .eqIfPresent(OutHospitalDO::getHsYjfj, reqVO.getHsYjfj())
            .eqIfPresent(OutHospitalDO::getHsYwry, reqVO.getHsYwry())
            .eqIfPresent(OutHospitalDO::getHsDzjkbh, reqVO.getHsDzjkbh())
            .eqIfPresent(OutHospitalDO::getHsYjcph, reqVO.getHsYjcph())
            .eqIfPresent(OutHospitalDO::getHsZfjly, reqVO.getHsZfjly())
            .eqIfPresent(OutHospitalDO::getHsAqgkcs, reqVO.getHsAqgkcs())
            .eqIfPresent(OutHospitalDO::getHsSfghjj, reqVO.getHsSfghjj())
            .eqIfPresent(OutHospitalDO::getHsSfkqdzjk, reqVO.getHsSfkqdzjk())
            .eqIfPresent(OutHospitalDO::getHsSfkqzfjly, reqVO.getHsSfkqzfjly())
            .eqIfPresent(OutHospitalDO::getSfhsqr, reqVO.getSfhsqr())
            .eqIfPresent(OutHospitalDO::getHsqrbz, reqVO.getHsqrbz())
            .eqIfPresent(OutHospitalDO::getHsQwapJbr, reqVO.getHsQwapJbr())
            .eqIfPresent(OutHospitalDO::getHsQwapJbsj, reqVO.getHsQwapJbsj())
            .eqIfPresent(OutHospitalDO::getStatus, reqVO.getStatus())
            .eqIfPresent(OutHospitalDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(OutHospitalDO::getTaskId, reqVO.getTaskId())
            .eqIfPresent(OutHospitalDO::getCurrentStep, reqVO.getCurrentStep())
        .orderByDesc(OutHospitalDO::getAddTime));    }


    }
