package com.rs.module.acp.dao.db;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.db.ServeSentenceDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-收押业务-留所服刑 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ServeSentenceDao extends IBaseDao<ServeSentenceDO> {


    default PageResult<ServeSentenceDO> selectPage(ServeSentencePageReqVO reqVO) {
        Page<ServeSentenceDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ServeSentenceDO> wrapper = new LambdaQueryWrapperX<ServeSentenceDO>()
            .eqIfPresent(ServeSentenceDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(ServeSentenceDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(ServeSentenceDO::getDetainReason, reqVO.getDetainReason())
            .eqIfPresent(ServeSentenceDO::getDetainReasonDetails, reqVO.getDetainReasonDetails())
            .eqIfPresent(ServeSentenceDO::getRemark, reqVO.getRemark())
            .betweenIfPresent(ServeSentenceDO::getOperateTime, reqVO.getOperateTime())
            .eqIfPresent(ServeSentenceDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(ServeSentenceDO::getOperatePolice, reqVO.getOperatePolice())
            .eqIfPresent(ServeSentenceDO::getStatus, reqVO.getStatus())
            .eqIfPresent(ServeSentenceDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(ServeSentenceDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(ServeSentenceDO::getAddTime);
        }
        Page<ServeSentenceDO> serveSentencePage = selectPage(page, wrapper);
        return new PageResult<>(serveSentencePage.getRecords(), serveSentencePage.getTotal());
    }
    default List<ServeSentenceDO> selectList(ServeSentenceListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ServeSentenceDO>()
            .eqIfPresent(ServeSentenceDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(ServeSentenceDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(ServeSentenceDO::getDetainReason, reqVO.getDetainReason())
            .eqIfPresent(ServeSentenceDO::getDetainReasonDetails, reqVO.getDetainReasonDetails())
            .eqIfPresent(ServeSentenceDO::getRemark, reqVO.getRemark())
            .betweenIfPresent(ServeSentenceDO::getOperateTime, reqVO.getOperateTime())
            .eqIfPresent(ServeSentenceDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(ServeSentenceDO::getOperatePolice, reqVO.getOperatePolice())
            .eqIfPresent(ServeSentenceDO::getStatus, reqVO.getStatus())
            .eqIfPresent(ServeSentenceDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(ServeSentenceDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(ServeSentenceDO::getAddTime));    }


    }
