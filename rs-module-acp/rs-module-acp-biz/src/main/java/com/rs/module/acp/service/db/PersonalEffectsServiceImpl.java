package com.rs.module.acp.service.db;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.PersonalEffectsDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.db.PersonalEffectsDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-收押业务-随身物品登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PersonalEffectsServiceImpl extends BaseServiceImpl<PersonalEffectsDao, PersonalEffectsDO> implements PersonalEffectsService {

    @Resource
    private PersonalEffectsDao personalEffectsDao;

    @Override
    public String createPersonalEffects(PersonalEffectsSaveReqVO createReqVO) {
        // 插入
        PersonalEffectsDO personalEffects = BeanUtils.toBean(createReqVO, PersonalEffectsDO.class);
        personalEffectsDao.insert(personalEffects);
        // 返回
        return personalEffects.getId();
    }

    @Override
    public void updatePersonalEffects(PersonalEffectsSaveReqVO updateReqVO) {
        // 校验存在
        validatePersonalEffectsExists(updateReqVO.getId());
        // 更新
        PersonalEffectsDO updateObj = BeanUtils.toBean(updateReqVO, PersonalEffectsDO.class);
        personalEffectsDao.updateById(updateObj);
    }

    @Override
    public void deletePersonalEffects(String id) {
        // 校验存在
        validatePersonalEffectsExists(id);
        // 删除
        personalEffectsDao.deleteById(id);
    }

    private void validatePersonalEffectsExists(String id) {
        if (personalEffectsDao.selectById(id) == null) {
            throw new ServerException("实战平台-收押业务-随身物品登记数据不存在");
        }
    }

    @Override
    public PersonalEffectsDO getPersonalEffects(String id) {
        return personalEffectsDao.selectById(id);
    }

    @Override
    public PageResult<PersonalEffectsDO> getPersonalEffectsPage(PersonalEffectsPageReqVO pageReqVO) {
        return personalEffectsDao.selectPage(pageReqVO);
    }

    @Override
    public List<PersonalEffectsDO> getPersonalEffectsList(PersonalEffectsListReqVO listReqVO) {
        return personalEffectsDao.selectList(listReqVO);
    }

    @Override
    public PersonalEffectsDO getPersonalEffectsByRybh(String rybh) {
        PersonalEffectsDO personalEffect = personalEffectsDao.selectOne(PersonalEffectsDO::getRybh, rybh);
        return personalEffect;
    }


}
