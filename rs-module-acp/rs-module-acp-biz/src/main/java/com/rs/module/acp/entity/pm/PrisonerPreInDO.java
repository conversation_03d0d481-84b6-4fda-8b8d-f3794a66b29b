package com.rs.module.acp.entity.pm;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 嫌疑人（待入所）信息 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_prisoner_pre_in")
@KeySequence("acp_pm_prisoner_pre_in_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_prisoner_pre_in")
public class PrisonerPreInDO  {
private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监所编号
     */
    private String jsbh;
    /**
     * 网办人员编号 (法制系统唯一编号)
     */
    private String wbrybh;
    /**
     * 姓名
     */
    private String xm;
    /**
     * 姓名拼音
     */
    private String xmpy;
    /**
     * 入所日期
     */
    private Date rsrq;
    /**
     * 职务 字典值：ZW
     */
    private String zw;
    /**
     * 户籍地详址
     */
    private String hjdxz;
    /**
     * 现住地详址
     */
    private String xzdxz;
    /**
     * 工作单位
     */
    private String gzdw;
    /**
     * 状态（STATE） 字典值：STATE
     */
    private String state;
    /**
     * 别名
     */
    private String bm;
    /**
     * 同案编号
     */
    private String tabh;
    /**
     * 审批人
     */
    private String spr;
    /**
     * 审批单位
     */
    private String spdw;
    /**
     * 特殊身份 字典值：TSSF
     */
    private String tssf;
    /**
     * sdnjccjg
     */
    private String sdnjccjg;
    /**
     * 专长 字典值：ZC
     */
    private String zc;
    /**
     * 身高
     */
    private String sg;
    /**
     * 足长
     */
    private String zuc;
    /**
     * 证件号
     */
    private String zjh;
    /**
     * 性别 字典值：XB
     */
    private String xb;
    /**
     * 民族 字典值：MZ
     */
    private String mz;
    /**
     * 国籍 字典值：GJ
     */
    private String gj;
    /**
     * 文化程度 字典值：WHCD
     */
    private String whcd;
    /**
     * 政治面貌 字典值：ZZMM
     */
    private String zzmm;
    /**
     * 户籍地 字典值：XZQH
     */
    private String hjd;
    /**
     * 现住地 字典值：XZQH
     */
    private String xzd;
    /**
     * 职业 字典值：ZY
     */
    private String zy;
    /**
     * 籍贯 字典值：XZQH
     */
    private String jg;
    /**
     * xzhjaj
     */
    private String xzhjaj;
    /**
     * 人员管理类别 字典值：RYGLLB
     */
    private String gllb;
    /**
     * 身份 字典值：SF
     */
    private String sf;
    /**
     * 证件类型 字典值：ZJLX
     */
    private String zjlx;
    /**
     * 办案单位
     */
    private String badw;
    /**
     * 送押单位
     */
    private String sydw;
    /**
     * 送押人
     */
    private String syr;
    /**
     * 创建时间
     */
    private Date createtime;
    /**
     * 更新时间
     */
    private Date updatetime;
    /**
     * 简要案情
     */
    private String jyaq;
    /**
     * 案由 字典值：看守所：AJLB，拘留所：JLSAJLB
     */
    private String ay;
    /**
     * 婚姻状况 字典值：HYZK
     */
    private String hyzk;
    /**
     * czzt
     */
    private String czzt;
    /**
     * 出生日期
     */
    private Date csrq;
    /**
     * jljdjg
     */
    private String jljdjg;
    /**
     * gyts
     */
    private String gyts;
    /**
     * 拘留日期
     */
    private Date jlrq;
    /**
     * 关押期限
     */
    private Date gyqx;
    /**
     * 办案人电话
     */
    private String bardh;
    /**
     * syrsj
     */
    private String syrsj;
    /**
     * 收押凭证（SYPZ） 字典值：SYPZ
     */
    private String sypz;
    /**
     * 收押凭证文书号
     */
    private String sypzwsh;
    /**
     * 逮捕日期
     */
    private Date dbrq;
    /**
     * 办案环节 字典值：BAJD
     */
    private String bahj;
    /**
     * 联系电话
     */
    private String lxdh;
    /**
     * 监所类型
     */
    private String jslx;

}
