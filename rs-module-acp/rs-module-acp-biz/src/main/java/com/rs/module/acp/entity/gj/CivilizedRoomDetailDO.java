package com.rs.module.acp.entity.gj;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-文明监室登记明细 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_civilized_room_detail")
@KeySequence("acp_gj_civilized_room_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_civilized_room_detail")
public class CivilizedRoomDetailDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 文明监室登记ID
     */
    @ApiModelProperty("文明监室登记ID")
    private String civilizedRoomId;
    /**
     * room_id
     */
    @ApiModelProperty("room_id")
    @NotEmpty(message = "room_id不能为空")
    private String roomId;
    /**
     * 监室名称
     */
    @ApiModelProperty("监室名称")
    @NotEmpty(message = "监室名称不能为空")
    private String roomName;
    /**
     * 违规数
     */
    @ApiModelProperty("违规数")
    @NotNull(message = "违规数不能为空")
    private Integer numberOfViolations;
    /**
     * 评选理由
     */
    @ApiModelProperty("评选理由")
    @NotEmpty(message = "评选理由不能为空")
    private String selectionReason;
    /**
     * 附件地址
     */
    @ApiModelProperty("附件地址")
    private String attUrl;

}
