package com.rs.module.acp.service.zh.indicatorcate;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.db.Session;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.*;
import com.rs.module.acp.entity.zh.*;
import com.rs.module.acp.enums.gj.CivilizedRoomStatusEnum;
import com.rs.module.acp.enums.zh.IndicatorAssmtApprovalStatusEnum;
import com.rs.module.acp.enums.zh.IndicatorAssmtStatusEnum;
import com.rs.module.acp.enums.zh.IndicatorScoreTypeEnum;
import com.rs.module.acp.enums.zh.IndicatorTypeEnum;
import com.rs.module.base.util.BspApprovalUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.zh.AssmttApprovalDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 综合管理-绩效考核-加减分考核审核 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class AssmttApprovalServiceImpl extends BaseServiceImpl<AssmttApprovalDao, AssmttApprovalDO> implements AssmttApprovalService {

    @Resource
    private AssmttApprovalDao assmttApprovalDao;

    @Autowired
    private AssmtRecordService assmtRecordService;

    @Autowired
    private IndicatorCateAssessorService indicatorCateAssessorService;

    @Autowired
    private IndicatorService indicatorService;

    @Autowired
    private IndicatorSubService indicatorSubService;

    @Autowired
    private AssmttApprovalRecordService assmttApprovalRecordService;


    private static final String defKey = "jxkhsplc";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createAssmttApproval(AssmttApprovalSaveReqVO createReqVO) {

        AssmtRecordDO assmtRecord = assmtRecordService.getById(createReqVO.getAssmtRecordId());
        if (Objects.isNull(assmtRecord)) {
            throw new RuntimeException("考核记录不存在，不能进行考核填写！");
        }
        if (IndicatorTypeEnum.ZGF.getCode().equals(createReqVO.getIndicatorType())) {
            int count = this.count(Wrappers.lambdaQuery(AssmttApprovalDO.class)
                    .eq(AssmttApprovalDO::getAssmtMonth, assmtRecord.getAssmtMonth())
                    .eq(AssmttApprovalDO::getAssessedSfzh, assmtRecord.getAssessedSfzh())
                    .eq(AssmttApprovalDO::getIndicatorType, createReqVO.getIndicatorType()));
            if (count > 0) {
                throw new RuntimeException("主观分指标绩效每月只能填写一次");
            }
        }
        createReqVO.setAssessedName(assmtRecord.getAssessedName());
        createReqVO.setAssessedSfzh(assmtRecord.getAssessedSfzh());
        createReqVO.setAssmtMonth(assmtRecord.getAssmtMonth());
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        createReqVO.setAssessorSfzh(sessionUser.getIdCard());
        createReqVO.setAssessorName(sessionUser.getName());
        createReqVO.setAssessorTime(new Date());

        // 插入
        AssmttApprovalDO assmttApproval = BeanUtils.toBean(createReqVO, AssmttApprovalDO.class);
        assmttApproval.setStatus(IndicatorAssmtApprovalStatusEnum.DZDLDSP.getCode());
        assmttApprovalDao.insert(assmttApproval);
        // 插入考核记录
        List<AssmttApprovalRecordSaveReqVO> approvalRecordList = createReqVO.getApprovalRecordList();

        Set<String> subIndicatorIdList = new HashSet<>();
        for (AssmttApprovalRecordSaveReqVO recordSaveReqVO : approvalRecordList) {
            recordSaveReqVO.setId(null);
            recordSaveReqVO.setAssmttApprovalId(assmttApproval.getId());
            recordSaveReqVO.setIndicatorCateId(assmtRecord.getIndicatorCateId());
            recordSaveReqVO.setIndicatorCateName(assmtRecord.getIndicatorCateName());
            subIndicatorIdList.add(recordSaveReqVO.getSubIndicatorId());
        }

        List<IndicatorSubDO> indicatorSubDOS = indicatorSubService.listByIds(subIndicatorIdList);
        Set<String> mainIndicatorIdList = indicatorSubDOS.stream().map(IndicatorSubDO::getMainIndicatorId).collect(Collectors.toSet());
        List<IndicatorDO> indicatorDOS = indicatorService.listByIds(mainIndicatorIdList);
        Map<String, IndicatorDO> indicatorDOMap = indicatorDOS.stream().collect(Collectors.toMap(IndicatorDO::getId, Function.identity()));
        Map<String, IndicatorSubDO> indicatorSubDOMap = indicatorSubDOS.stream().collect(Collectors.toMap(IndicatorSubDO::getId, Function.identity()));
        for (AssmttApprovalRecordSaveReqVO recordSaveReqVO : approvalRecordList) {
            IndicatorSubDO indicatorSubDO = indicatorSubDOMap.get(recordSaveReqVO.getSubIndicatorId());
            if (Objects.isNull(indicatorSubDO)) {
                throw new RuntimeException("子指标不存在：" + recordSaveReqVO.getSubIndicatorId());
            }
            IndicatorDO indicatorDO = indicatorDOMap.get(indicatorSubDO.getMainIndicatorId());
            if (Objects.isNull(indicatorDO)) {
                throw new RuntimeException("主指标不存在：" + indicatorSubDO.getMainIndicatorId());
            }
            recordSaveReqVO.setMainIndicatorId(indicatorDO.getId());
            recordSaveReqVO.setIndicatorName(indicatorDO.getIndicatorName());
            recordSaveReqVO.setIndicatorDescription(indicatorSubDO.getDescription());
            recordSaveReqVO.setScoreType(indicatorSubDO.getScoreType());
            recordSaveReqVO.setBaseScore(indicatorSubDO.getBaseScore());
            recordSaveReqVO.setMinScore(indicatorSubDO.getMinScore());
            recordSaveReqVO.setMaxScore(indicatorSubDO.getMaxScore());
            recordSaveReqVO.setSortOrder(indicatorSubDO.getSortOrder());
            recordSaveReqVO.setAddSubtract(indicatorSubDO.getAddSubtract());
        }
        assmttApprovalRecordService.saveBatch(BeanUtils.toBean(approvalRecordList, AssmttApprovalRecordDO.class));

        //启动流程审批
        String msgUrl = "/#/";
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", assmttApproval.getId());
        JSONObject result = BspApprovalUtil.defaultStartProcess(HttpUtils.getAppCode() + "-" + defKey, assmttApproval.getId(),
                "绩效考核审批", msgUrl, variables);
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            assmttApproval.setActInstId(bpmTrail.getString("actInstId"));
            assmttApproval.setTaskId(bpmTrail.getString("taskId"));

            assmttApprovalDao.updateById(assmttApproval);
            if (IndicatorTypeEnum.ZGF.getCode().equals(createReqVO.getIndicatorType())) {
                assmtRecord.setStatus(IndicatorAssmtStatusEnum.DSP.getCode());
                assmtRecordService.updateById(assmtRecord);
            }
        } else {
            throw new ServerException("流程启动失败");
        }
        return assmttApproval.getId();
    }

    @Override
    public void updateAssmttApproval(AssmttApprovalSaveReqVO updateReqVO) {
        // 校验存在
        validateAssmttApprovalExists(updateReqVO.getId());
        // 更新
        AssmttApprovalDO updateObj = BeanUtils.toBean(updateReqVO, AssmttApprovalDO.class);
        assmttApprovalDao.updateById(updateObj);
    }

    @Override
    public void deleteAssmttApproval(String id) {
        // 校验存在
        validateAssmttApprovalExists(id);
        // 删除
        assmttApprovalDao.deleteById(id);
    }

    private void validateAssmttApprovalExists(String id) {
        if (assmttApprovalDao.selectById(id) == null) {
            throw new ServerException("综合管理-绩效考核-加减分考核审核数据不存在");
        }
    }

    @Override
    public AssmttApprovalDO getAssmttApproval(String id) {
        return assmttApprovalDao.selectById(id);
    }

    @Override
    public PageResult<AssmttApprovalDO> getAssmttApprovalPage(AssmttApprovalPageReqVO pageReqVO) {
        return assmttApprovalDao.selectPage(pageReqVO);
    }

    @Override
    public List<AssmttApprovalDO> getAssmttApprovalList(AssmttApprovalListReqVO listReqVO) {
        return assmttApprovalDao.selectList(listReqVO);
    }

    @Override
    public void checkAssessor(String assmtRecordId, String indicatorType) {

        AssmtRecordDO assmtRecord = assmtRecordService.getById(assmtRecordId);
        String indicatorCateId = assmtRecord.getIndicatorCateId();

        int assessorCount = indicatorCateAssessorService.count(Wrappers.lambdaQuery(IndicatorCateAssessorDO.class)
                .eq(IndicatorCateAssessorDO::getIndicatorCateId, indicatorCateId)
                .eq(IndicatorCateAssessorDO::getAssessorSfzh, SessionUserUtil.getSessionUser().getIdCard()));
        if (assessorCount == 0) {
            throw new RuntimeException("非考评人，不能进行绩效填写！");
        }
        int indicatorTypeCount = indicatorService.count(Wrappers.lambdaQuery(IndicatorDO.class)
                .eq(IndicatorDO::getIndicatorCateId, indicatorCateId)
                .eq(IndicatorDO::getIndicatorType, indicatorType).eq(IndicatorDO::getIsEnabled, (short) 1));

        if (indicatorTypeCount == 0) {
            throw new RuntimeException("该指标分类未配置" + IndicatorTypeEnum.getByCode(indicatorType).getName() + "，不能进行绩效填写！");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void zdApproval(AssmttZdldApprovalReqVO approvalReqVO) {
        AssmttApprovalDO assmttApprovalDO = assmttApprovalDao.selectById(approvalReqVO.getId());
        if (Objects.isNull(assmttApprovalDO)) {
            throw new ServerException("综合管理-绩效考核-加减分考核审核数据不存在");
        }

        if (!IndicatorAssmtApprovalStatusEnum.DZDLDSP.getCode().equals(assmttApprovalDO.getStatus())) {
            throw new ServerException("非【" + IndicatorAssmtApprovalStatusEnum.DZDLDSP.getName() + "】状态，不允许进行审批");
        }
        assmttApprovalDO.setStatus(IndicatorAssmtApprovalStatusEnum.DZWSP.getCode());

        assmttApprovalRecordService.updateBatchById(BeanUtils.toBean(approvalReqVO.getApprovalRecordList(), AssmttApprovalRecordDO.class));
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(assmttApprovalDO.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }

        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", assmttApprovalDO.getId());
        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getId());
        nowApproveUser.put("name", sessionUser.getName());
        JSONObject result = BspApprovalUtil.approvalProcess(HttpUtils.getAppCode() + "-" + defKey,
                assmttApprovalDO.getActInstId(), assmttApprovalDO.getTaskId(), assmttApprovalDO.getId(),
                BspApproceStatusEnum.PASSED, "同意", null, null, false,
                variables, nowApproveUser, "acp");
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            assmttApprovalDO.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程启动失败");
        }
        assmttApprovalDao.updateById(assmttApprovalDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void zwApproval(AssmttZwApprovalReqVO approvalReqVO) {
        AssmttApprovalDO assmttApprovalDO = assmttApprovalDao.selectById(approvalReqVO.getId());
        if (Objects.isNull(assmttApprovalDO)) {
            throw new ServerException("综合管理-绩效考核-加减分考核审核数据不存在");
        }
        if (!IndicatorAssmtApprovalStatusEnum.DZWSP.getCode().equals(assmttApprovalDO.getStatus())) {
            throw new ServerException("非【" + IndicatorAssmtApprovalStatusEnum.DZWSP.getName() + "】状态，不允许进行审批");
        }
        assmttApprovalDO.setStatus(IndicatorAssmtApprovalStatusEnum.YBJ.getCode());

        assmttApprovalRecordService.updateBatchById(BeanUtils.toBean(approvalReqVO.getApprovalRecordList(), AssmttApprovalRecordDO.class));
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(assmttApprovalDO.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }

        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", assmttApprovalDO.getId());
        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getId());
        nowApproveUser.put("name", sessionUser.getName());
        JSONObject result = BspApprovalUtil.approvalProcess(HttpUtils.getAppCode() + "-" + defKey,
                assmttApprovalDO.getActInstId(), assmttApprovalDO.getTaskId(), assmttApprovalDO.getId(),
                BspApproceStatusEnum.PASSED_END, "同意", null, null, true,
                variables, nowApproveUser, "acp");
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            assmttApprovalDO.setTaskId(bpmTrail.getString("taskId"));
            assmttApprovalDao.updateById(assmttApprovalDO);
            // 计算分值
            AssmtRecordDO assmtRecordDO = assmtRecordService.getById(assmttApprovalDO.getAssmtRecordId());
            List<String> recordIds = approvalReqVO.getApprovalRecordList().stream().map(AssmttZwApprovalRecordReqVO::getId).collect(Collectors.toList());
            List<AssmttApprovalRecordDO> approvalRecordDOS = assmttApprovalRecordService.listByIds(recordIds);
            BigDecimal total = new BigDecimal(0);
            for (AssmttApprovalRecordDO approvalRecordDO : approvalRecordDOS) {
                if (IndicatorScoreTypeEnum.ADD.getCode().equals(approvalRecordDO.getAddSubtract())) {
                    total = total.add(approvalRecordDO.getZzApprovalScore());
                } else {
                    total = total.subtract(approvalRecordDO.getZzApprovalScore());
                }
            }
            if (IndicatorTypeEnum.ZGF.getCode().equals(assmttApprovalDO.getIndicatorType())) {
                assmtRecordDO.setStatus(IndicatorAssmtStatusEnum.SPWC.getCode());
                assmtRecordDO.setSubjectiveScore(total);
            } else {
                assmtRecordDO.setAddsubtractScore(total);
            }
            assmtRecordDO.setTotalScore(assmtRecordDO.getTotalScore().add(total));
            assmtRecordService.updateById(assmtRecordDO);

        } else {
            throw new ServerException("流程启动失败");
        }
    }


    @Override
    public AssmtRecordRespVO getAssmtRecord(String id) {
        AssmtRecordDO assmtRecordDO = assmtRecordService.getById(id);
        AssmtRecordRespVO assmtRecordRespVO = BeanUtils.toBean(assmtRecordDO, AssmtRecordRespVO.class);
        if (Objects.nonNull(assmtRecordRespVO)) {
            List<AssmttApprovalDO> list = this.list(Wrappers.lambdaQuery(AssmttApprovalDO.class)
                    .eq(AssmttApprovalDO::getAssmtRecordId, assmtRecordRespVO.getId())
                    .orderByAsc(AssmttApprovalDO::getAddTime));
            List<AssmttApprovalRespVO> approvalRespList = BeanUtils.toBean(list, AssmttApprovalRespVO.class);
            AssmttApprovalRespVO subjectiveDetail = null;
            List<AssmttApprovalRespVO> addsubtractList = null;
            Map<String, List<AssmttApprovalRecordDO>> tempMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(approvalRespList)) {
                List<String> approvalIds = approvalRespList.stream().map(AssmttApprovalRespVO::getId).collect(Collectors.toList());
                List<AssmttApprovalRecordDO> approvalRecordDOSIds = assmttApprovalRecordService.list(Wrappers.lambdaQuery(AssmttApprovalRecordDO.class)
                        .in(AssmttApprovalRecordDO::getAssmttApprovalId, approvalIds));
                for (AssmttApprovalRecordDO approvalRecordDO : approvalRecordDOSIds) {

                    String assmttApprovalId = approvalRecordDO.getAssmttApprovalId();
                    List<AssmttApprovalRecordDO> approvalRecordDOS = tempMap.get(assmttApprovalId);
                    if (CollectionUtil.isEmpty(approvalRecordDOS)) {
                        approvalRecordDOS = new ArrayList<>();
                        tempMap.put(assmttApprovalId, approvalRecordDOS);
                    }
                    approvalRecordDOS.add(approvalRecordDO);
                }

                for (AssmttApprovalRespVO assmttApprovalRespVO : approvalRespList) {
                    List<AssmttApprovalRecordDO> approvalRecordDOS = tempMap.get(assmttApprovalRespVO.getId());
                    assmttApprovalRespVO.setApprovalRecordList(BeanUtils.toBean(approvalRecordDOS, AssmttApprovalRecordRespVO.class));
//                    if (CollectionUtil.isNotEmpty(approvalRecordDOS)) {
//                        List<AssmtApprovalMainIndicatorRespVO> mainIndicatorList = new ArrayList<>();
//                        Map<String, AssmtApprovalMainIndicatorRespVO> mainMap = new HashMap<>();
//                        for (AssmttApprovalRecordDO approvalRecordDO : approvalRecordDOS) {
//                            AssmtApprovalMainIndicatorRespVO mainRecord = mainMap.get(approvalRecordDO.getMainIndicatorId());
//                            if (Objects.isNull(mainRecord)) {
//                                mainRecord = BeanUtils.toBean(approvalRecordDO, AssmtApprovalMainIndicatorRespVO.class);
//                                mainMap.put(approvalRecordDO.getMainIndicatorId(), mainRecord);
//                                mainRecord.setSubIndicatorList(new ArrayList<>());
//                                mainIndicatorList.add(mainRecord);
//                            }
//                            mainRecord.getSubIndicatorList().add(BeanUtils.toBean(approvalRecordDO, AssmttApprovalRecordRespVO.class));
//                        }
//                        assmttApprovalRespVO.setMainIndicatorList(mainIndicatorList);
//                    }
                    if (IndicatorTypeEnum.ZGF.getCode().equals(assmttApprovalRespVO.getIndicatorType())) {
                        subjectiveDetail = assmttApprovalRespVO;
                    } else {
                        if (addsubtractList == null) {
                            addsubtractList = new ArrayList<>();
                        }
                        addsubtractList.add(assmttApprovalRespVO);
                    }
                }

            }
            assmtRecordRespVO.setSubjectiveDetail(subjectiveDetail);
            assmtRecordRespVO.setAddsubtractList(addsubtractList);
        }

        return assmtRecordRespVO;
    }


}
