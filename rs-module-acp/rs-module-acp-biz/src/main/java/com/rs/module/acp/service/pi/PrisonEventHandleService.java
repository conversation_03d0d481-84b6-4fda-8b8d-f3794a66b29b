package com.rs.module.acp.service.pi;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventHandleListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventHandlePageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventHandleRespVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventHandleSaveReqVO;
import com.rs.module.acp.entity.pi.PrisonEventHandleDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-巡视管控-所情处置 Service 接口
 *
 * <AUTHOR>
 */
public interface PrisonEventHandleService extends IBaseService<PrisonEventHandleDO>{

    /**
     * 创建实战平台-巡视管控-所情处置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrisonEventHandle(@Valid PrisonEventHandleSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-所情处置
     *
     * @param updateReqVO 更新信息
     */
    void updatePrisonEventHandle(@Valid PrisonEventHandleSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-所情处置
     *
     * @param id 编号
     */
    void deletePrisonEventHandle(String id);

    /**
     * 获得实战平台-巡视管控-所情处置
     *
     * @param id 编号
     * @return 实战平台-巡视管控-所情处置
     */
    PrisonEventHandleDO getPrisonEventHandle(String id);

    /**
    * 获得实战平台-巡视管控-所情处置分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-巡视管控-所情处置分页
    */
    PageResult<PrisonEventHandleDO> getPrisonEventHandlePage(PrisonEventHandlePageReqVO pageReqVO);

    /**
    * 获得实战平台-巡视管控-所情处置列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-巡视管控-所情处置列表
    */
    List<PrisonEventHandleDO> getPrisonEventHandleList(PrisonEventHandleListReqVO listReqVO);

    /**
     * 获取最新的处置记录
     * @param eventId
     * @return
     */
    List<PrisonEventHandleRespVO> getNewPrisonEventHandleListByEventId(String eventId);

}
