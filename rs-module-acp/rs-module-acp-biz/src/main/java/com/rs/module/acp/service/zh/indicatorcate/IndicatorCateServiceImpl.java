package com.rs.module.acp.service.zh.indicatorcate;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.util.SnowflakeIdUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.*;
import com.rs.module.acp.entity.zh.IndicatorCateAssessedDO;
import com.rs.module.acp.entity.zh.IndicatorCateAssessorDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.zh.IndicatorCateDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.zh.IndicatorCateDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 综合管理-绩效考核指标分类 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class IndicatorCateServiceImpl extends BaseServiceImpl<IndicatorCateDao, IndicatorCateDO> implements IndicatorCateService {

    @Resource
    private IndicatorCateDao indicatorCateDao;

    @Resource
    private IndicatorCateAssessedService indicatorCateAssessedService;

    @Resource
    private IndicatorCateAssessorService indicatorCateAssessorService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createIndicatorCate(IndicatorCateSaveReqVO createReqVO) {
        checkRepeat(createReqVO);
        createReqVO.setId(SnowflakeIdUtil.getGuid());
        if (CollectionUtil.isNotEmpty(createReqVO.getAssessedList())) {
            List<String> assessedObjectIdList = new ArrayList<>();
            List<String> assessedObjectNameList = new ArrayList<>();
            int index = 0;
            for (IndicatorCateAssessedSaveReqVO indicatorCateAssessedSaveReqVO : createReqVO.getAssessedList()) {
                assessedObjectIdList.add(indicatorCateAssessedSaveReqVO.getAssessedObjectId());
                assessedObjectNameList.add(indicatorCateAssessedSaveReqVO.getAssessedObjectName());
                indicatorCateAssessedSaveReqVO.setIndicatorCateId(createReqVO.getId());
                indicatorCateAssessedSaveReqVO.setAssessedObjectType(createReqVO.getAssessedObjectType());
                indicatorCateAssessedSaveReqVO.setId(null);
            }
            createReqVO.setAssessedObjectId(String.join(",", assessedObjectIdList));
            createReqVO.setAssessedObjectName(String.join(",", assessedObjectNameList));
        }

        if (CollectionUtil.isNotEmpty(createReqVO.getAssessorList())) {
            List<String> assessorSfzhList = new ArrayList<>();
            List<String> assessorNameList = new ArrayList<>();
            for (IndicatorCateAssessorSaveReqVO indicatorCateAssessorSaveReqVO : createReqVO.getAssessorList()) {
                assessorSfzhList.add(indicatorCateAssessorSaveReqVO.getAssessorSfzh());
                assessorNameList.add(indicatorCateAssessorSaveReqVO.getAssessorName());
                indicatorCateAssessorSaveReqVO.setIndicatorCateId(createReqVO.getId());
                indicatorCateAssessorSaveReqVO.setId(null);
            }
            createReqVO.setAssessorName(String.join(",", assessorNameList));
            createReqVO.setAssessorSfzh(String.join(",", assessorSfzhList));
        }

        // 插入
        IndicatorCateDO indicatorCate = BeanUtils.toBean(createReqVO, IndicatorCateDO.class);
        indicatorCateDao.insert(indicatorCate);
        if (CollectionUtil.isNotEmpty(createReqVO.getAssessedList())) {
            indicatorCateAssessedService.saveBatch(BeanUtils.toBean(createReqVO.getAssessedList(), IndicatorCateAssessedDO.class));
        }
        if (CollectionUtil.isNotEmpty(createReqVO.getAssessorList())) {
            indicatorCateAssessorService.saveBatch(BeanUtils.toBean(createReqVO.getAssessorList(), IndicatorCateAssessorDO.class));
        }

        // 返回
        return indicatorCate.getId();
    }

    private void checkRepeat(IndicatorCateSaveReqVO createReqVO){
        LambdaQueryWrapper<IndicatorCateDO> wrapperCount = Wrappers.lambdaQuery(IndicatorCateDO.class)
                .eq(IndicatorCateDO::getOrgCode, SessionUserUtil.getSessionUser().getOrgCode())
                .eq(IndicatorCateDO::getTypeName, createReqVO.getTypeName());
        if(StringUtils.isNotEmpty(createReqVO.getId())){
            wrapperCount.ne(IndicatorCateDO::getId, createReqVO.getId());
        }
        Integer count = indicatorCateDao.selectCount(wrapperCount);
        if (Objects.nonNull(count) && count > 0) {
            throw new RuntimeException("指标类型名称重复");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateIndicatorCate(IndicatorCateSaveReqVO updateReqVO) {
        // 校验存在
        validateIndicatorCateExists(updateReqVO.getId());
        checkRepeat(updateReqVO);
        if (CollectionUtil.isNotEmpty(updateReqVO.getAssessedList())) {
            List<String> assessedObjectIdList = new ArrayList<>();
            List<String> assessedObjectNameList = new ArrayList<>();
            for (IndicatorCateAssessedSaveReqVO indicatorCateAssessedSaveReqVO : updateReqVO.getAssessedList()) {
                assessedObjectIdList.add(indicatorCateAssessedSaveReqVO.getAssessedObjectId());
                assessedObjectNameList.add(indicatorCateAssessedSaveReqVO.getAssessedObjectName());
                indicatorCateAssessedSaveReqVO.setIndicatorCateId(updateReqVO.getId());
                indicatorCateAssessedSaveReqVO.setAssessedObjectType(updateReqVO.getAssessedObjectType());
                indicatorCateAssessedSaveReqVO.setId(null);
            }
            updateReqVO.setAssessedObjectId(String.join(",", assessedObjectIdList));
            updateReqVO.setAssessedObjectName(String.join(",", assessedObjectNameList));
        }

        if (CollectionUtil.isNotEmpty(updateReqVO.getAssessorList())) {
            List<String> assessorSfzhList = new ArrayList<>();
            List<String> assessorNameList = new ArrayList<>();
            for (IndicatorCateAssessorSaveReqVO indicatorCateAssessorSaveReqVO : updateReqVO.getAssessorList()) {
                assessorSfzhList.add(indicatorCateAssessorSaveReqVO.getAssessorSfzh());
                assessorNameList.add(indicatorCateAssessorSaveReqVO.getAssessorName());
                indicatorCateAssessorSaveReqVO.setIndicatorCateId(updateReqVO.getId());
                indicatorCateAssessorSaveReqVO.setId(null);
            }
            updateReqVO.setAssessorName(String.join(",", assessorNameList));
            updateReqVO.setAssessorSfzh(String.join(",", assessorSfzhList));
        }
        // 更新
        IndicatorCateDO updateObj = BeanUtils.toBean(updateReqVO, IndicatorCateDO.class);
        indicatorCateDao.updateById(updateObj);

        indicatorCateAssessedService.remove(Wrappers.lambdaQuery(IndicatorCateAssessedDO.class).eq(IndicatorCateAssessedDO::getIndicatorCateId, updateReqVO.getId()));
        indicatorCateAssessorService.remove(Wrappers.lambdaQuery(IndicatorCateAssessorDO.class).eq(IndicatorCateAssessorDO::getIndicatorCateId, updateReqVO.getId()));
        if (CollectionUtil.isNotEmpty(updateReqVO.getAssessedList())) {
            indicatorCateAssessedService.saveBatch(BeanUtils.toBean(updateReqVO.getAssessedList(), IndicatorCateAssessedDO.class));
        }
        if (CollectionUtil.isNotEmpty(updateReqVO.getAssessorList())) {
            indicatorCateAssessorService.saveBatch(BeanUtils.toBean(updateReqVO.getAssessorList(), IndicatorCateAssessorDO.class));
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteIndicatorCate(String id) {
        // 校验存在
        validateIndicatorCateExists(id);
        // 删除
        indicatorCateDao.deleteById(id);
        indicatorCateAssessedService.remove(Wrappers.lambdaQuery(IndicatorCateAssessedDO.class).eq(IndicatorCateAssessedDO::getIndicatorCateId, id));
        indicatorCateAssessorService.remove(Wrappers.lambdaQuery(IndicatorCateAssessorDO.class).eq(IndicatorCateAssessorDO::getIndicatorCateId, id));
    }

    private void validateIndicatorCateExists(String id) {
        if (indicatorCateDao.selectById(id) == null) {
            throw new ServerException("综合管理-绩效考核指标分类数据不存在");
        }
    }

    @Override
    public IndicatorCateRespVO getIndicatorCate(String id) {
        IndicatorCateDO indicatorCateDO = indicatorCateDao.selectById(id);
        IndicatorCateRespVO indicatorCateRespVO = BeanUtils.toBean(indicatorCateDO, IndicatorCateRespVO.class);
        if (Objects.nonNull(indicatorCateRespVO)) {
            IndicatorCateAssessedListReqVO assessedListReqVO = new IndicatorCateAssessedListReqVO();
            assessedListReqVO.setIndicatorCateId(id);
            List<IndicatorCateAssessedDO> indicatorCateAssessedList = indicatorCateAssessedService.getIndicatorCateAssessedList(assessedListReqVO);
            indicatorCateRespVO.setAssessedList(BeanUtils.toBean(indicatorCateAssessedList, IndicatorCateAssessedRespVO.class));
            IndicatorCateAssessorListReqVO assessorListReqVO = new IndicatorCateAssessorListReqVO();
            assessorListReqVO.setIndicatorCateId(id);
            List<IndicatorCateAssessorDO> indicatorCateAssessorList = indicatorCateAssessorService.getIndicatorCateAssessorList(assessorListReqVO);
            indicatorCateRespVO.setAssessorList(BeanUtils.toBean(indicatorCateAssessorList, IndicatorCateAssessorRespVO.class));
        }
        return indicatorCateRespVO;
    }

    @Override
    public PageResult<IndicatorCateDO> getIndicatorCatePage(IndicatorCatePageReqVO pageReqVO) {
        return indicatorCateDao.selectPage(pageReqVO);
    }

    @Override
    public List<IndicatorCateDO> getIndicatorCateList(IndicatorCateListReqVO listReqVO) {
        return indicatorCateDao.selectList(listReqVO);
    }


}
