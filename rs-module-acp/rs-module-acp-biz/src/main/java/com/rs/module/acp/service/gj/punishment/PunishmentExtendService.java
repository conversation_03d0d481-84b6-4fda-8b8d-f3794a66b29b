package com.rs.module.acp.service.gj.punishment;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentExtendListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentExtendPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentExtendSaveReqVO;
import com.rs.module.acp.entity.gj.PunishmentExtendDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务延长处罚呈批 Service 接口
 *
 * <AUTHOR>
 */
public interface PunishmentExtendService extends IBaseService<PunishmentExtendDO>{

    /**
     * 创建实战平台-管教业务延长处罚呈批
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPunishmentExtend(@Valid PunishmentExtendSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务延长处罚呈批
     *
     * @param updateReqVO 更新信息
     */
    void updatePunishmentExtend(@Valid PunishmentExtendSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务延长处罚呈批
     *
     * @param id 编号
     */
    void deletePunishmentExtend(String id);

    /**
     * 获得实战平台-管教业务延长处罚呈批
     *
     * @param id 编号
     * @return 实战平台-管教业务延长处罚呈批
     */
    PunishmentExtendDO getPunishmentExtend(String id);

    /**
    * 获得实战平台-管教业务延长处罚呈批分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务延长处罚呈批分页
    */
    PageResult<PunishmentExtendDO> getPunishmentExtendPage(PunishmentExtendPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务延长处罚呈批列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务延长处罚呈批列表
    */
    List<PunishmentExtendDO> getPunishmentExtendList(PunishmentExtendListReqVO listReqVO);


}
