package com.rs.module.acp.dao.zh;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorSubListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorSubPageReqVO;
import com.rs.module.acp.entity.zh.IndicatorSubDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 综合管理-绩效考核子指标 Dao
*
* <AUTHOR>
*/
@Mapper
public interface IndicatorSubDao extends IBaseDao<IndicatorSubDO> {


    default PageResult<IndicatorSubDO> selectPage(IndicatorSubPageReqVO reqVO) {
        Page<IndicatorSubDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<IndicatorSubDO> wrapper = new LambdaQueryWrapperX<IndicatorSubDO>()
            .eqIfPresent(IndicatorSubDO::getMainIndicatorId, reqVO.getMainIndicatorId())
            .eqIfPresent(IndicatorSubDO::getIsRequired, reqVO.getIsRequired())
            .eqIfPresent(IndicatorSubDO::getDescription, reqVO.getDescription())
            .eqIfPresent(IndicatorSubDO::getAddSubtract, reqVO.getAddSubtract())
            .eqIfPresent(IndicatorSubDO::getScoreType, reqVO.getScoreType())
            .eqIfPresent(IndicatorSubDO::getBaseScore, reqVO.getBaseScore())
            .eqIfPresent(IndicatorSubDO::getMinScore, reqVO.getMinScore())
            .eqIfPresent(IndicatorSubDO::getMaxScore, reqVO.getMaxScore())
            .eqIfPresent(IndicatorSubDO::getSortOrder, reqVO.getSortOrder())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(IndicatorSubDO::getAddTime);
        }
        Page<IndicatorSubDO> indicatorSubPage = selectPage(page, wrapper);
        return new PageResult<>(indicatorSubPage.getRecords(), indicatorSubPage.getTotal());
    }
    default List<IndicatorSubDO> selectList(IndicatorSubListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<IndicatorSubDO>()
            .eqIfPresent(IndicatorSubDO::getMainIndicatorId, reqVO.getMainIndicatorId())
            .eqIfPresent(IndicatorSubDO::getIsRequired, reqVO.getIsRequired())
            .eqIfPresent(IndicatorSubDO::getDescription, reqVO.getDescription())
            .eqIfPresent(IndicatorSubDO::getAddSubtract, reqVO.getAddSubtract())
            .eqIfPresent(IndicatorSubDO::getScoreType, reqVO.getScoreType())
            .eqIfPresent(IndicatorSubDO::getBaseScore, reqVO.getBaseScore())
            .eqIfPresent(IndicatorSubDO::getMinScore, reqVO.getMinScore())
            .eqIfPresent(IndicatorSubDO::getMaxScore, reqVO.getMaxScore())
            .eqIfPresent(IndicatorSubDO::getSortOrder, reqVO.getSortOrder())
        .orderByDesc(IndicatorSubDO::getAddTime));    }


    }
