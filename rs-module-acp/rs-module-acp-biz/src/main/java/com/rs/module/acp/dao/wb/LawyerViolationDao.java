package com.rs.module.acp.dao.wb;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.LawyerViolationDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-窗口业务-律师违规 Dao
*
* <AUTHOR>
*/
@Mapper
public interface LawyerViolationDao extends IBaseDao<LawyerViolationDO> {


    default PageResult<LawyerViolationDO> selectPage(LawyerViolationPageReqVO reqVO) {
        Page<LawyerViolationDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<LawyerViolationDO> wrapper = new LambdaQueryWrapperX<LawyerViolationDO>()
            .eqIfPresent(LawyerViolationDO::getLawyerId, reqVO.getLawyerId())
            .betweenIfPresent(LawyerViolationDO::getViolationTime, reqVO.getViolationTime())
            .eqIfPresent(LawyerViolationDO::getViolationType, reqVO.getViolationType())
            .eqIfPresent(LawyerViolationDO::getDescription, reqVO.getDescription())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(LawyerViolationDO::getAddTime);
        }
        Page<LawyerViolationDO> lawyerViolationPage = selectPage(page, wrapper);
        return new PageResult<>(lawyerViolationPage.getRecords(), lawyerViolationPage.getTotal());
    }
    default List<LawyerViolationDO> selectList(LawyerViolationListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<LawyerViolationDO>()
            .eqIfPresent(LawyerViolationDO::getLawyerId, reqVO.getLawyerId())
            .betweenIfPresent(LawyerViolationDO::getViolationTime, reqVO.getViolationTime())
            .eqIfPresent(LawyerViolationDO::getViolationType, reqVO.getViolationType())
            .eqIfPresent(LawyerViolationDO::getDescription, reqVO.getDescription())
        .orderByDesc(LawyerViolationDO::getAddTime));    }


    }
