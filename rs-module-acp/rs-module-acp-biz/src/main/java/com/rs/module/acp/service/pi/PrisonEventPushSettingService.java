package com.rs.module.acp.service.pi;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.pi.vo.prisonevent.*;
import com.rs.module.acp.entity.pi.PrisonEventPushSettingDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-巡视管控-所情事件推送设置 Service 接口
 *
 * <AUTHOR>
 */
public interface PrisonEventPushSettingService extends IBaseService<PrisonEventPushSettingDO>{

    /**
     * 创建实战平台-巡视管控-所情事件推送设置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrisonEventPushSetting(@Valid PrisonEventPushSettingSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-所情事件推送设置
     *
     * @param updateReqVO 更新信息
     */
    void updatePrisonEventPushSetting(@Valid PrisonEventPushSettingSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-所情事件推送设置
     *
     * @param id 编号
     */
    void deletePrisonEventPushSetting(String id);

    /**
     * 获得实战平台-巡视管控-所情事件推送设置
     *
     * @param id 编号
     * @return 实战平台-巡视管控-所情事件推送设置
     */
    PrisonEventPushSettingDO getPrisonEventPushSetting(String id);

    /**
    * 获得实战平台-巡视管控-所情事件推送设置分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-巡视管控-所情事件推送设置分页
    */
    PageResult<PrisonEventPushSettingDO> getPrisonEventPushSettingPage(PrisonEventPushSettingPageReqVO pageReqVO);

    /**
    * 获得实战平台-巡视管控-所情事件推送设置列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-巡视管控-所情事件推送设置列表
    */
    List<PrisonEventPushSettingDO> getPrisonEventPushSettingList(PrisonEventPushSettingListReqVO listReqVO);


    /**
     * 保存所情事件推送设置列表
     * @param pushSettingList
     * @param typeId
     * @return
     */
    boolean savePrisonEventPushSettingList(List<PrisonEventPushSettingSaveReqVO> pushSettingList,String typeId);

    /**
     * 实战平台-巡视管控-根据所情配置ID删除所情事件推送设置
     *
     * @param typeId
     */
    void deletePrisonEventPushSettingByTypeId(String typeId);

    /**
     * 根据所情配置ID获取所情事件推送设置
     * @param typeId
     * @return
     */
    List<PrisonEventPushSettingRespVO> getPrisonEventPushSettingListByTypeId(String typeId);
}
