package com.rs.module.acp.dao.wb;

import java.util.*;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.FamilyMeetingVideoDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
 * 实战平台-窗口业务-单向视频家属会见 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface FamilyMeetingVideoDao extends IBaseDao<FamilyMeetingVideoDO> {


    default PageResult<FamilyMeetingVideoDO> selectPage(FamilyMeetingVideoPageReqVO reqVO) {
        Page<FamilyMeetingVideoDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<FamilyMeetingVideoDO> wrapper = new LambdaQueryWrapperX<FamilyMeetingVideoDO>()
                .eqIfPresent(FamilyMeetingVideoDO::getJgrybm, reqVO.getJgrybm())
                .likeIfPresent(FamilyMeetingVideoDO::getFamilyMember1Name, reqVO.getFamilyMember1Name())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1Gender, reqVO.getFamilyMember1Gender())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1IdType, reqVO.getFamilyMember1IdType())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1IdNumber, reqVO.getFamilyMember1IdNumber())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1Relationship, reqVO.getFamilyMember1Relationship())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1Contact, reqVO.getFamilyMember1Contact())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1WorkUnit, reqVO.getFamilyMember1WorkUnit())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1Address, reqVO.getFamilyMember1Address())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1RelationsAttch, reqVO.getFamilyMember1RelationsAttch())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1ImageUrl, reqVO.getFamilyMember1ImageUrl())
                .likeIfPresent(FamilyMeetingVideoDO::getFamilyMember2Name, reqVO.getFamilyMember2Name())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2Id, reqVO.getFamilyMember2Id())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2Gender, reqVO.getFamilyMember2Gender())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2IdType, reqVO.getFamilyMember2IdType())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2IdNumber, reqVO.getFamilyMember2IdNumber())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2Relationship, reqVO.getFamilyMember2Relationship())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2Contact, reqVO.getFamilyMember2Contact())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2WorkUnit, reqVO.getFamilyMember2WorkUnit())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2Address, reqVO.getFamilyMember2Address())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2RelationsAttch, reqVO.getFamilyMember2RelationsAttch())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2ImageUrl, reqVO.getFamilyMember2ImageUrl())
                .eqIfPresent(FamilyMeetingVideoDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(FamilyMeetingVideoDO::getNotificationFamilyTime, reqVO.getNotificationFamilyTime())
                .betweenIfPresent(FamilyMeetingVideoDO::getNotificationMeetingDate, reqVO.getNotificationMeetingDate())
                .betweenIfPresent(FamilyMeetingVideoDO::getMeetingStartTime, reqVO.getMeetingStartTime())
                .betweenIfPresent(FamilyMeetingVideoDO::getMeetingEndTime, reqVO.getMeetingEndTime())
                .eqIfPresent(FamilyMeetingVideoDO::getRemarks, reqVO.getRemarks())
                .eqIfPresent(FamilyMeetingVideoDO::getApproverSfzh, reqVO.getApproverSfzh())
                .eqIfPresent(FamilyMeetingVideoDO::getApproverXm, reqVO.getApproverXm())
                .betweenIfPresent(FamilyMeetingVideoDO::getApproverTime, reqVO.getApproverTime())
                .eqIfPresent(FamilyMeetingVideoDO::getApprovalResult, reqVO.getApprovalResult())
                .eqIfPresent(FamilyMeetingVideoDO::getApprovalAutograph, reqVO.getApprovalAutograph())
                .betweenIfPresent(FamilyMeetingVideoDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
                .eqIfPresent(FamilyMeetingVideoDO::getApprovalComments, reqVO.getApprovalComments())
                .eqIfPresent(FamilyMeetingVideoDO::getActInstId, reqVO.getActInstId())
                .eqIfPresent(FamilyMeetingVideoDO::getTaskId, reqVO.getTaskId())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember3Id, reqVO.getFamilyMember3Id())
                .likeIfPresent(FamilyMeetingVideoDO::getFamilyMember3Name, reqVO.getFamilyMember3Name())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember3Gender, reqVO.getFamilyMember3Gender())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember3IdType, reqVO.getFamilyMember3IdType())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember3IdNumber, reqVO.getFamilyMember3IdNumber())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember3Relationship, reqVO.getFamilyMember3Relationship())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember3Contact, reqVO.getFamilyMember3Contact())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember3WorkUnit, reqVO.getFamilyMember3WorkUnit())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember3Address, reqVO.getFamilyMember3Address())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1Occupation, reqVO.getFamilyMember1Occupation())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2Occupation, reqVO.getFamilyMember2Occupation())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember3Occupation, reqVO.getFamilyMember3Occupation())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1Id, reqVO.getFamilyMember1Id());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(FamilyMeetingVideoDO::getAddTime);
        }
        Page<FamilyMeetingVideoDO> familyMeetingVideoPage = selectPage(page, wrapper);
        return new PageResult<>(familyMeetingVideoPage.getRecords(), familyMeetingVideoPage.getTotal());
    }

    default List<FamilyMeetingVideoDO> selectList(FamilyMeetingVideoListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<FamilyMeetingVideoDO>()
                .eqIfPresent(FamilyMeetingVideoDO::getJgrybm, reqVO.getJgrybm())
                .likeIfPresent(FamilyMeetingVideoDO::getFamilyMember1Name, reqVO.getFamilyMember1Name())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1Gender, reqVO.getFamilyMember1Gender())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1IdType, reqVO.getFamilyMember1IdType())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1IdNumber, reqVO.getFamilyMember1IdNumber())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1Relationship, reqVO.getFamilyMember1Relationship())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1Contact, reqVO.getFamilyMember1Contact())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1WorkUnit, reqVO.getFamilyMember1WorkUnit())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1Address, reqVO.getFamilyMember1Address())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1RelationsAttch, reqVO.getFamilyMember1RelationsAttch())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1ImageUrl, reqVO.getFamilyMember1ImageUrl())
                .likeIfPresent(FamilyMeetingVideoDO::getFamilyMember2Name, reqVO.getFamilyMember2Name())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2Id, reqVO.getFamilyMember2Id())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2Gender, reqVO.getFamilyMember2Gender())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2IdType, reqVO.getFamilyMember2IdType())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2IdNumber, reqVO.getFamilyMember2IdNumber())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2Relationship, reqVO.getFamilyMember2Relationship())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2Contact, reqVO.getFamilyMember2Contact())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2WorkUnit, reqVO.getFamilyMember2WorkUnit())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2Address, reqVO.getFamilyMember2Address())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2RelationsAttch, reqVO.getFamilyMember2RelationsAttch())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2ImageUrl, reqVO.getFamilyMember2ImageUrl())
                .eqIfPresent(FamilyMeetingVideoDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(FamilyMeetingVideoDO::getNotificationFamilyTime, reqVO.getNotificationFamilyTime())
                .betweenIfPresent(FamilyMeetingVideoDO::getNotificationMeetingDate, reqVO.getNotificationMeetingDate())
                .betweenIfPresent(FamilyMeetingVideoDO::getMeetingStartTime, reqVO.getMeetingStartTime())
                .betweenIfPresent(FamilyMeetingVideoDO::getMeetingEndTime, reqVO.getMeetingEndTime())
                .eqIfPresent(FamilyMeetingVideoDO::getRemarks, reqVO.getRemarks())
                .eqIfPresent(FamilyMeetingVideoDO::getApproverSfzh, reqVO.getApproverSfzh())
                .eqIfPresent(FamilyMeetingVideoDO::getApproverXm, reqVO.getApproverXm())
                .betweenIfPresent(FamilyMeetingVideoDO::getApproverTime, reqVO.getApproverTime())
                .eqIfPresent(FamilyMeetingVideoDO::getApprovalResult, reqVO.getApprovalResult())
                .eqIfPresent(FamilyMeetingVideoDO::getApprovalAutograph, reqVO.getApprovalAutograph())
                .betweenIfPresent(FamilyMeetingVideoDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
                .eqIfPresent(FamilyMeetingVideoDO::getApprovalComments, reqVO.getApprovalComments())
                .eqIfPresent(FamilyMeetingVideoDO::getActInstId, reqVO.getActInstId())
                .eqIfPresent(FamilyMeetingVideoDO::getTaskId, reqVO.getTaskId())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember3Id, reqVO.getFamilyMember3Id())
                .likeIfPresent(FamilyMeetingVideoDO::getFamilyMember3Name, reqVO.getFamilyMember3Name())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember3Gender, reqVO.getFamilyMember3Gender())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember3IdType, reqVO.getFamilyMember3IdType())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember3IdNumber, reqVO.getFamilyMember3IdNumber())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember3Relationship, reqVO.getFamilyMember3Relationship())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember3Contact, reqVO.getFamilyMember3Contact())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember3WorkUnit, reqVO.getFamilyMember3WorkUnit())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember3Address, reqVO.getFamilyMember3Address())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1Occupation, reqVO.getFamilyMember1Occupation())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember2Occupation, reqVO.getFamilyMember2Occupation())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember3Occupation, reqVO.getFamilyMember3Occupation())
                .eqIfPresent(FamilyMeetingVideoDO::getFamilyMember1Id, reqVO.getFamilyMember1Id())
                .orderByDesc(FamilyMeetingVideoDO::getAddTime));
    }


    List<JSONObject> getremoteNumbering(@Param("type") String type, @Param("orgCode") String orgCode);

    JSONObject limitNumber(@Param("jgrybm") String jgrybm,@Param("orgCode") String orgCode);
}
