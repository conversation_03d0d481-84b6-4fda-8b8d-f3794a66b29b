package com.rs.module.acp.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.pm.PrisonerPreInDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 嫌疑人（待入所）信息 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PrisonerPreInDao extends IBaseDao<PrisonerPreInDO> {


    default PageResult<PrisonerPreInDO> selectPage(PrisonerPreInPageReqVO reqVO) {
        Page<PrisonerPreInDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrisonerPreInDO> wrapper = new LambdaQueryWrapperX<PrisonerPreInDO>()
            .eqIfPresent(PrisonerPreInDO::getJsbh, reqVO.getJsbh())
            .eqIfPresent(PrisonerPreInDO::getWbrybh, reqVO.getWbrybh())
            .eqIfPresent(PrisonerPreInDO::getXm, reqVO.getXm())
            .eqIfPresent(PrisonerPreInDO::getXmpy, reqVO.getXmpy())
            .eqIfPresent(PrisonerPreInDO::getRsrq, reqVO.getRsrq())
            .eqIfPresent(PrisonerPreInDO::getZw, reqVO.getZw())
            .eqIfPresent(PrisonerPreInDO::getHjdxz, reqVO.getHjdxz())
            .eqIfPresent(PrisonerPreInDO::getXzdxz, reqVO.getXzdxz())
            .eqIfPresent(PrisonerPreInDO::getGzdw, reqVO.getGzdw())
            .eqIfPresent(PrisonerPreInDO::getState, reqVO.getState())
            .eqIfPresent(PrisonerPreInDO::getBm, reqVO.getBm())
            .eqIfPresent(PrisonerPreInDO::getTabh, reqVO.getTabh())
            .eqIfPresent(PrisonerPreInDO::getSpr, reqVO.getSpr())
            .eqIfPresent(PrisonerPreInDO::getSpdw, reqVO.getSpdw())
            .eqIfPresent(PrisonerPreInDO::getTssf, reqVO.getTssf())
            .eqIfPresent(PrisonerPreInDO::getSdnjccjg, reqVO.getSdnjccjg())
            .eqIfPresent(PrisonerPreInDO::getZc, reqVO.getZc())
            .eqIfPresent(PrisonerPreInDO::getSg, reqVO.getSg())
            .eqIfPresent(PrisonerPreInDO::getZuc, reqVO.getZuc())
            .eqIfPresent(PrisonerPreInDO::getZjh, reqVO.getZjh())
            .eqIfPresent(PrisonerPreInDO::getXb, reqVO.getXb())
            .eqIfPresent(PrisonerPreInDO::getMz, reqVO.getMz())
            .eqIfPresent(PrisonerPreInDO::getGj, reqVO.getGj())
            .eqIfPresent(PrisonerPreInDO::getWhcd, reqVO.getWhcd())
            .eqIfPresent(PrisonerPreInDO::getZzmm, reqVO.getZzmm())
            .eqIfPresent(PrisonerPreInDO::getHjd, reqVO.getHjd())
            .eqIfPresent(PrisonerPreInDO::getXzd, reqVO.getXzd())
            .eqIfPresent(PrisonerPreInDO::getZy, reqVO.getZy())
            .eqIfPresent(PrisonerPreInDO::getJg, reqVO.getJg())
            .eqIfPresent(PrisonerPreInDO::getXzhjaj, reqVO.getXzhjaj())
            .eqIfPresent(PrisonerPreInDO::getGllb, reqVO.getGllb())
            .eqIfPresent(PrisonerPreInDO::getSf, reqVO.getSf())
            .eqIfPresent(PrisonerPreInDO::getZjlx, reqVO.getZjlx())
            .eqIfPresent(PrisonerPreInDO::getBadw, reqVO.getBadw())
            .eqIfPresent(PrisonerPreInDO::getSydw, reqVO.getSydw())
            .eqIfPresent(PrisonerPreInDO::getSyr, reqVO.getSyr())
            .betweenIfPresent(PrisonerPreInDO::getCreatetime, reqVO.getCreatetime())
            .betweenIfPresent(PrisonerPreInDO::getUpdatetime, reqVO.getUpdatetime())
            .eqIfPresent(PrisonerPreInDO::getJyaq, reqVO.getJyaq())
            .eqIfPresent(PrisonerPreInDO::getAy, reqVO.getAy())
            .eqIfPresent(PrisonerPreInDO::getHyzk, reqVO.getHyzk())
            .eqIfPresent(PrisonerPreInDO::getCzzt, reqVO.getCzzt())
            .eqIfPresent(PrisonerPreInDO::getCsrq, reqVO.getCsrq())
            .eqIfPresent(PrisonerPreInDO::getJljdjg, reqVO.getJljdjg())
            .eqIfPresent(PrisonerPreInDO::getGyts, reqVO.getGyts())
            .eqIfPresent(PrisonerPreInDO::getJlrq, reqVO.getJlrq())
            .eqIfPresent(PrisonerPreInDO::getGyqx, reqVO.getGyqx())
            .eqIfPresent(PrisonerPreInDO::getBardh, reqVO.getBardh())
            .eqIfPresent(PrisonerPreInDO::getSyrsj, reqVO.getSyrsj())
            .eqIfPresent(PrisonerPreInDO::getSypz, reqVO.getSypz())
            .eqIfPresent(PrisonerPreInDO::getSypzwsh, reqVO.getSypzwsh())
            .eqIfPresent(PrisonerPreInDO::getDbrq, reqVO.getDbrq())
            .eqIfPresent(PrisonerPreInDO::getBahj, reqVO.getBahj())
            .eqIfPresent(PrisonerPreInDO::getLxdh, reqVO.getLxdh())
            .eqIfPresent(PrisonerPreInDO::getJslx, reqVO.getJslx())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PrisonerPreInDO::getCreatetime);
        }
        Page<PrisonerPreInDO> prisonerPreInPage = selectPage(page, wrapper);
        return new PageResult<>(prisonerPreInPage.getRecords(), prisonerPreInPage.getTotal());
    }
    default List<PrisonerPreInDO> selectList(PrisonerPreInListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrisonerPreInDO>()
            .eqIfPresent(PrisonerPreInDO::getJsbh, reqVO.getJsbh())
            .eqIfPresent(PrisonerPreInDO::getWbrybh, reqVO.getWbrybh())
            .eqIfPresent(PrisonerPreInDO::getXm, reqVO.getXm())
            .eqIfPresent(PrisonerPreInDO::getXmpy, reqVO.getXmpy())
            .eqIfPresent(PrisonerPreInDO::getRsrq, reqVO.getRsrq())
            .eqIfPresent(PrisonerPreInDO::getZw, reqVO.getZw())
            .eqIfPresent(PrisonerPreInDO::getHjdxz, reqVO.getHjdxz())
            .eqIfPresent(PrisonerPreInDO::getXzdxz, reqVO.getXzdxz())
            .eqIfPresent(PrisonerPreInDO::getGzdw, reqVO.getGzdw())
            .eqIfPresent(PrisonerPreInDO::getState, reqVO.getState())
            .eqIfPresent(PrisonerPreInDO::getBm, reqVO.getBm())
            .eqIfPresent(PrisonerPreInDO::getTabh, reqVO.getTabh())
            .eqIfPresent(PrisonerPreInDO::getSpr, reqVO.getSpr())
            .eqIfPresent(PrisonerPreInDO::getSpdw, reqVO.getSpdw())
            .eqIfPresent(PrisonerPreInDO::getTssf, reqVO.getTssf())
            .eqIfPresent(PrisonerPreInDO::getSdnjccjg, reqVO.getSdnjccjg())
            .eqIfPresent(PrisonerPreInDO::getZc, reqVO.getZc())
            .eqIfPresent(PrisonerPreInDO::getSg, reqVO.getSg())
            .eqIfPresent(PrisonerPreInDO::getZuc, reqVO.getZuc())
            .eqIfPresent(PrisonerPreInDO::getZjh, reqVO.getZjh())
            .eqIfPresent(PrisonerPreInDO::getXb, reqVO.getXb())
            .eqIfPresent(PrisonerPreInDO::getMz, reqVO.getMz())
            .eqIfPresent(PrisonerPreInDO::getGj, reqVO.getGj())
            .eqIfPresent(PrisonerPreInDO::getWhcd, reqVO.getWhcd())
            .eqIfPresent(PrisonerPreInDO::getZzmm, reqVO.getZzmm())
            .eqIfPresent(PrisonerPreInDO::getHjd, reqVO.getHjd())
            .eqIfPresent(PrisonerPreInDO::getXzd, reqVO.getXzd())
            .eqIfPresent(PrisonerPreInDO::getZy, reqVO.getZy())
            .eqIfPresent(PrisonerPreInDO::getJg, reqVO.getJg())
            .eqIfPresent(PrisonerPreInDO::getXzhjaj, reqVO.getXzhjaj())
            .eqIfPresent(PrisonerPreInDO::getGllb, reqVO.getGllb())
            .eqIfPresent(PrisonerPreInDO::getSf, reqVO.getSf())
            .eqIfPresent(PrisonerPreInDO::getZjlx, reqVO.getZjlx())
            .eqIfPresent(PrisonerPreInDO::getBadw, reqVO.getBadw())
            .eqIfPresent(PrisonerPreInDO::getSydw, reqVO.getSydw())
            .eqIfPresent(PrisonerPreInDO::getSyr, reqVO.getSyr())
            .betweenIfPresent(PrisonerPreInDO::getCreatetime, reqVO.getCreatetime())
            .betweenIfPresent(PrisonerPreInDO::getUpdatetime, reqVO.getUpdatetime())
            .eqIfPresent(PrisonerPreInDO::getJyaq, reqVO.getJyaq())
            .eqIfPresent(PrisonerPreInDO::getAy, reqVO.getAy())
            .eqIfPresent(PrisonerPreInDO::getHyzk, reqVO.getHyzk())
            .eqIfPresent(PrisonerPreInDO::getCzzt, reqVO.getCzzt())
            .eqIfPresent(PrisonerPreInDO::getCsrq, reqVO.getCsrq())
            .eqIfPresent(PrisonerPreInDO::getJljdjg, reqVO.getJljdjg())
            .eqIfPresent(PrisonerPreInDO::getGyts, reqVO.getGyts())
            .eqIfPresent(PrisonerPreInDO::getJlrq, reqVO.getJlrq())
            .eqIfPresent(PrisonerPreInDO::getGyqx, reqVO.getGyqx())
            .eqIfPresent(PrisonerPreInDO::getBardh, reqVO.getBardh())
            .eqIfPresent(PrisonerPreInDO::getSyrsj, reqVO.getSyrsj())
            .eqIfPresent(PrisonerPreInDO::getSypz, reqVO.getSypz())
            .eqIfPresent(PrisonerPreInDO::getSypzwsh, reqVO.getSypzwsh())
            .eqIfPresent(PrisonerPreInDO::getDbrq, reqVO.getDbrq())
            .eqIfPresent(PrisonerPreInDO::getBahj, reqVO.getBahj())
            .eqIfPresent(PrisonerPreInDO::getLxdh, reqVO.getLxdh())
            .eqIfPresent(PrisonerPreInDO::getJslx, reqVO.getJslx())
        .orderByDesc(PrisonerPreInDO::getCreatetime));    }


    }
