package com.rs.module.acp.dao.wb;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.PrisonAreaInoutDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-窗口业务-出入监区登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PrisonAreaInoutDao extends IBaseDao<PrisonAreaInoutDO> {


    default PageResult<PrisonAreaInoutDO> selectPage(PrisonAreaInoutPageReqVO reqVO) {
        Page<PrisonAreaInoutDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrisonAreaInoutDO> wrapper = new LambdaQueryWrapperX<PrisonAreaInoutDO>()
            .eqIfPresent(PrisonAreaInoutDO::getXm, reqVO.getXm())
            .eqIfPresent(PrisonAreaInoutDO::getZjlx, reqVO.getZjlx())
            .eqIfPresent(PrisonAreaInoutDO::getXb, reqVO.getXb())
            .eqIfPresent(PrisonAreaInoutDO::getZjhm, reqVO.getZjhm())
            .eqIfPresent(PrisonAreaInoutDO::getLxfs, reqVO.getLxfs())
            .eqIfPresent(PrisonAreaInoutDO::getJrjqry, reqVO.getJrjqry())
            .eqIfPresent(PrisonAreaInoutDO::getSfbjdc, reqVO.getSfbjdc())
            .eqIfPresent(PrisonAreaInoutDO::getYjlfsj, reqVO.getYjlfsj())
            .eqIfPresent(PrisonAreaInoutDO::getYjlssj, reqVO.getYjlssj())
            .eqIfPresent(PrisonAreaInoutDO::getClhm, reqVO.getClhm())
            .eqIfPresent(PrisonAreaInoutDO::getZpUrl, reqVO.getZpUrl())
            .eqIfPresent(PrisonAreaInoutDO::getJrjqsj, reqVO.getJrjqsj())
            .eqIfPresent(PrisonAreaInoutDO::getJrjqaqjcxx, reqVO.getJrjqaqjcxx())
            .eqIfPresent(PrisonAreaInoutDO::getJrjqaqjcrsfzh, reqVO.getJrjqaqjcrsfzh())
            .eqIfPresent(PrisonAreaInoutDO::getJrjqaqjcrxm, reqVO.getJrjqaqjcrxm())
            .eqIfPresent(PrisonAreaInoutDO::getLkjqsj, reqVO.getLkjqsj())
            .eqIfPresent(PrisonAreaInoutDO::getLkjqaqjcxx, reqVO.getLkjqaqjcxx())
            .eqIfPresent(PrisonAreaInoutDO::getLkjqaqjcrsfzh, reqVO.getLkjqaqjcrsfzh())
            .eqIfPresent(PrisonAreaInoutDO::getLkjqaqjcrxm, reqVO.getLkjqaqjcrxm())
            .eqIfPresent(PrisonAreaInoutDO::getStatus, reqVO.getStatus())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PrisonAreaInoutDO::getAddTime);
        }
        Page<PrisonAreaInoutDO> prisonAreaInoutPage = selectPage(page, wrapper);
        return new PageResult<>(prisonAreaInoutPage.getRecords(), prisonAreaInoutPage.getTotal());
    }
    default List<PrisonAreaInoutDO> selectList(PrisonAreaInoutListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrisonAreaInoutDO>()
            .eqIfPresent(PrisonAreaInoutDO::getXm, reqVO.getXm())
            .eqIfPresent(PrisonAreaInoutDO::getZjlx, reqVO.getZjlx())
            .eqIfPresent(PrisonAreaInoutDO::getXb, reqVO.getXb())
            .eqIfPresent(PrisonAreaInoutDO::getZjhm, reqVO.getZjhm())
            .eqIfPresent(PrisonAreaInoutDO::getLxfs, reqVO.getLxfs())
            .eqIfPresent(PrisonAreaInoutDO::getJrjqry, reqVO.getJrjqry())
            .eqIfPresent(PrisonAreaInoutDO::getSfbjdc, reqVO.getSfbjdc())
            .eqIfPresent(PrisonAreaInoutDO::getYjlfsj, reqVO.getYjlfsj())
            .eqIfPresent(PrisonAreaInoutDO::getYjlssj, reqVO.getYjlssj())
            .eqIfPresent(PrisonAreaInoutDO::getClhm, reqVO.getClhm())
            .eqIfPresent(PrisonAreaInoutDO::getZpUrl, reqVO.getZpUrl())
            .eqIfPresent(PrisonAreaInoutDO::getJrjqsj, reqVO.getJrjqsj())
            .eqIfPresent(PrisonAreaInoutDO::getJrjqaqjcxx, reqVO.getJrjqaqjcxx())
            .eqIfPresent(PrisonAreaInoutDO::getJrjqaqjcrsfzh, reqVO.getJrjqaqjcrsfzh())
            .eqIfPresent(PrisonAreaInoutDO::getJrjqaqjcrxm, reqVO.getJrjqaqjcrxm())
            .eqIfPresent(PrisonAreaInoutDO::getLkjqsj, reqVO.getLkjqsj())
            .eqIfPresent(PrisonAreaInoutDO::getLkjqaqjcxx, reqVO.getLkjqaqjcxx())
            .eqIfPresent(PrisonAreaInoutDO::getLkjqaqjcrsfzh, reqVO.getLkjqaqjcrsfzh())
            .eqIfPresent(PrisonAreaInoutDO::getLkjqaqjcrxm, reqVO.getLkjqaqjcrxm())
            .eqIfPresent(PrisonAreaInoutDO::getStatus, reqVO.getStatus())
        .orderByDesc(PrisonAreaInoutDO::getAddTime));    }


    }
