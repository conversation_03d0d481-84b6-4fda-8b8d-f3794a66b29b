package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.confinement.ConfinementExtendListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.confinement.ConfinementExtendPageReqVO;
import com.rs.module.acp.entity.gj.confinement.ConfinementExtendDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-延长禁闭呈批 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ConfinementExtendDao extends IBaseDao<ConfinementExtendDO> {


    default PageResult<ConfinementExtendDO> selectPage(ConfinementExtendPageReqVO reqVO) {
        Page<ConfinementExtendDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ConfinementExtendDO> wrapper = new LambdaQueryWrapperX<ConfinementExtendDO>()
            .eqIfPresent(ConfinementExtendDO::getConfinementId, reqVO.getConfinementId())
            .eqIfPresent(ConfinementExtendDO::getExtendReason, reqVO.getExtendReason())
            .eqIfPresent(ConfinementExtendDO::getExtendDay, reqVO.getExtendDay())
            .eqIfPresent(ConfinementExtendDO::getStatus, reqVO.getStatus())
            .eqIfPresent(ConfinementExtendDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(ConfinementExtendDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(ConfinementExtendDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(ConfinementExtendDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(ConfinementExtendDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(ConfinementExtendDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(ConfinementExtendDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(ConfinementExtendDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(ConfinementExtendDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(ConfinementExtendDO::getAddTime);
        }
        Page<ConfinementExtendDO> confinementExtendPage = selectPage(page, wrapper);
        return new PageResult<>(confinementExtendPage.getRecords(), confinementExtendPage.getTotal());
    }
    default List<ConfinementExtendDO> selectList(ConfinementExtendListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ConfinementExtendDO>()
            .eqIfPresent(ConfinementExtendDO::getConfinementId, reqVO.getConfinementId())
            .eqIfPresent(ConfinementExtendDO::getExtendReason, reqVO.getExtendReason())
            .eqIfPresent(ConfinementExtendDO::getExtendDay, reqVO.getExtendDay())
            .eqIfPresent(ConfinementExtendDO::getStatus, reqVO.getStatus())
            .eqIfPresent(ConfinementExtendDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(ConfinementExtendDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(ConfinementExtendDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(ConfinementExtendDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(ConfinementExtendDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(ConfinementExtendDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(ConfinementExtendDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(ConfinementExtendDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(ConfinementExtendDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(ConfinementExtendDO::getAddTime));    }


    }
