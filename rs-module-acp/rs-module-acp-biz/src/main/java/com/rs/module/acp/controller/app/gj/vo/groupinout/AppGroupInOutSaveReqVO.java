package com.rs.module.acp.controller.app.gj.vo.groupinout;

import com.rs.module.acp.controller.admin.gj.vo.groupinout.GroupInOutDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/11 15:33
 */
@ApiModel(description = "外屏-管教业务-集体出入新增 Request VO")
@Data
public class AppGroupInOutSaveReqVO {

    @ApiModelProperty("业务类型: 01-集体入监, 02-集体出监")
    @NotEmpty(message = "业务类型不能为空")
    private String businessType;
    @ApiModelProperty("详情")
    private List<GroupInOutDetail> details;
}
