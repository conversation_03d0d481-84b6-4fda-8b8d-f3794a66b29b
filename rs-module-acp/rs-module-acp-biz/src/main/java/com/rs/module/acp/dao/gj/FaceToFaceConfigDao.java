package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceConfigListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceConfigPageReqVO;
import com.rs.module.acp.entity.gj.FaceToFaceConfigDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-面对面配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface FaceToFaceConfigDao extends IBaseDao<FaceToFaceConfigDO> {


    default PageResult<FaceToFaceConfigDO> selectPage(FaceToFaceConfigPageReqVO reqVO) {
        Page<FaceToFaceConfigDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<FaceToFaceConfigDO> wrapper = new LambdaQueryWrapperX<FaceToFaceConfigDO>()
            .eqIfPresent(FaceToFaceConfigDO::getCheckItem, reqVO.getCheckItem())
            .eqIfPresent(FaceToFaceConfigDO::getIsDisabled, reqVO.getIsDisabled())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(FaceToFaceConfigDO::getAddTime);
        }
        Page<FaceToFaceConfigDO> faceToFaceConfigPage = selectPage(page, wrapper);
        return new PageResult<>(faceToFaceConfigPage.getRecords(), faceToFaceConfigPage.getTotal());
    }
    default List<FaceToFaceConfigDO> selectList(FaceToFaceConfigListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<FaceToFaceConfigDO>()
            .eqIfPresent(FaceToFaceConfigDO::getCheckItem, reqVO.getCheckItem())
            .eqIfPresent(FaceToFaceConfigDO::getIsDisabled, reqVO.getIsDisabled())
                .eqIfPresent(FaceToFaceConfigDO::getOrgCode, reqVO.getOrgCode())
                .eqIfPresent(FaceToFaceConfigDO::getRegCode, reqVO.getRegCode())
                .eqIfPresent(FaceToFaceConfigDO::getCityCode, reqVO.getCityCode())
        .orderByDesc(FaceToFaceConfigDO::getAddTime));    }

    default List<FaceToFaceConfigDO> selectListByOrgCode(String orgCode) {
        return selectList(new LambdaQueryWrapperX<FaceToFaceConfigDO>()
                .eq(FaceToFaceConfigDO::getIsDisabled, (short)1)
                .eq(FaceToFaceConfigDO::getOrgCode, orgCode)
                .orderByAsc(FaceToFaceConfigDO::getAddTime));    }

    }
