package com.rs.module.acp.controller.admin.db.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-收押业务-伤情鉴定 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class InjuryAssessmentRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("人员编号")
    private String rybh;
    @ApiModelProperty("序号")
    private String serialNumber;
    @ApiModelProperty("照片")
    private String zp;
    @ApiModelProperty("描述")
    private String ms;

    @ApiModelProperty("坐标")
    private String zb;
}
