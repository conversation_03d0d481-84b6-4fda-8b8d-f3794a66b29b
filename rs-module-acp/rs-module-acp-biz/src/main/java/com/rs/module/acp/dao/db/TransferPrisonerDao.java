package com.rs.module.acp.dao.db;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.db.TransferPrisonerDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-羁押业务-转所人员 Dao
*
* <AUTHOR>
*/
@Mapper
public interface TransferPrisonerDao extends IBaseDao<TransferPrisonerDO> {


    default PageResult<TransferPrisonerDO> selectPage(TransferPrisonerPageReqVO reqVO) {
        Page<TransferPrisonerDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<TransferPrisonerDO> wrapper = new LambdaQueryWrapperX<TransferPrisonerDO>()
            .eqIfPresent(TransferPrisonerDO::getTransferRecordId, reqVO.getTransferRecordId())
            .eqIfPresent(TransferPrisonerDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(TransferPrisonerDO::getJgryxm, reqVO.getJgryxm())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(TransferPrisonerDO::getAddTime);
        }
        Page<TransferPrisonerDO> transferPrisonerPage = selectPage(page, wrapper);
        return new PageResult<>(transferPrisonerPage.getRecords(), transferPrisonerPage.getTotal());
    }
    default List<TransferPrisonerDO> selectList(TransferPrisonerListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TransferPrisonerDO>()
            .eqIfPresent(TransferPrisonerDO::getTransferRecordId, reqVO.getTransferRecordId())
            .eqIfPresent(TransferPrisonerDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(TransferPrisonerDO::getJgryxm, reqVO.getJgryxm())
        .orderByDesc(TransferPrisonerDO::getAddTime));    }


    }
