package com.rs.module.acp.controller.admin.gj.vo.face2face;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.annotation.Query;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.util.Date;
import java.util.Map;

@ApiModel(description = "管理后台 - 实战平台-管教业务-面对面管理 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FaceToFaceRespVO extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("禁闭监室id")
    private String roomId;
    @ApiModelProperty("数据来源（字典：ZD_DATA_SOURCES）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_DATA_SOURCES")
    private String dataSources;
    @ApiModelProperty("检查民警身份证号")
    private String checkPoliceSfzh;
    @ApiModelProperty("检查民警身份证号")
    private String checkPolice;
    @ApiModelProperty("检查时间")
    private Date checkTime;
    @ApiModelProperty("情况记录")
    private String situationRecord;
    @ApiModelProperty("拍照")
    private String snapPhoto;

    @ApiModelProperty("监室名称")
    @Query(sql = "select * from acp_pm_area_prison_room where id = '${roomId}'")
    public Map<String, Object> roomInfo;

    private String cityName;

    private String cityCode;

    private String regName;

    private String regCode;

    private String orgName;

    private String orgCode;

    private Boolean isDel;

    private String addUser;

    private String addUserName;

    private Date addTime;

    private String updateUser;

    private String updateUserName;

    private Date updateTime;
}
