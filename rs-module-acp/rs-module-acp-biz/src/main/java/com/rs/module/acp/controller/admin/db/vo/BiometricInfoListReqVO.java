package com.rs.module.acp.controller.admin.db.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-收押业务-生物特征信息列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BiometricInfoListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("人员编号")
    private String rybh;

    @ApiModelProperty("采集项目类型")
    private String cjxmlx;

    @ApiModelProperty("生物特征,存储采集系统返回JSON结构")
    private String swtz;

    @ApiModelProperty("swtzfj")
    private String swtzfj;

    @ApiModelProperty("备注")
    private String bz;

}
