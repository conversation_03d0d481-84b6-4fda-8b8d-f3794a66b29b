package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bsp.common.util.StringUtil;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.pm.PrisonerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.GoodsDeliveryDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.GoodsDeliveryDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-物品顾送登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GoodsDeliveryServiceImpl extends BaseServiceImpl<GoodsDeliveryDao, GoodsDeliveryDO> implements GoodsDeliveryService {

    @Resource
    private GoodsDeliveryDao goodsDeliveryDao;

    @Autowired
    private GoodsDeliveryDetailsService goodsDeliveryDetailsService;

    @Autowired
    private WbCommonService wbCommonService;

    @Autowired
    private PrisonerService prisonerService;

    @Autowired
    private BspApi bspApi;

    @Value("${acp.business_no.goods_delivery_no:acp_goods_delivery_no}")
    private String goodsDeliveryNo;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createGoodsDelivery(GoodsDeliverySaveReqVO createReqVO) {

        GoodsDeliveryDO goodsDelivery = BeanUtils.toBean(createReqVO, GoodsDeliveryDO.class);
        goodsDelivery.setId(StringUtil.getGuid32());

        //物品总数
        Integer goodsTotal = 0;

        //保存顾送物品信息
        if(CollectionUtil.isNotEmpty(createReqVO.getGoodsList())){
            goodsDeliveryDetailsService.saveGoodsDeliveryDetailsListByGoodsDeliveryId(createReqVO.getGoodsList(),goodsDelivery.getId());

            for(GoodsDeliveryDetailsSaveReqVO deliveryDetailsSaveReqVO:createReqVO.getGoodsList()){
                if(ObjectUtil.isNotEmpty(deliveryDetailsSaveReqVO.getGoodsQuantity())){
                    goodsTotal += deliveryDetailsSaveReqVO.getGoodsQuantity();
                }
            }
        }
        goodsDelivery.setGoodsTotal(goodsTotal);

//        if(ObjectUtil.isNotEmpty(goodsDelivery.getGoodsPhotoPath())){
//            String goodsPhotoPath = wbCommonService.saveFile(null,goodsDelivery.getGoodsPhotoPath());
//            goodsDelivery.setGoodsPhotoPath(goodsPhotoPath);
//        }

        if(ObjectUtil.isEmpty(goodsDelivery.getJgryxm())){
            PrisonerVwRespVO prisonerVwRespVO = prisonerService.getPrisonerByJgrybm(goodsDelivery.getJgrybm());
            goodsDelivery.setJgryxm(prisonerVwRespVO.getXm());
        }

        //顾送日期默认当前时日期
        goodsDelivery.setDeliveryDate(new Date());

        //获取顾送编号
        goodsDelivery.setDeliveryNo(bspApi.executeByRuleCode(goodsDeliveryNo,null));

        goodsDelivery.setStatus("0");

        // 插入
        goodsDeliveryDao.insert(goodsDelivery);
        // 返回
        return goodsDelivery.getId();
    }

    @Override
    public void updateGoodsDelivery(GoodsDeliverySaveReqVO updateReqVO) {
        // 校验存在
        validateGoodsDeliveryExists(updateReqVO.getId());
        // 更新
        GoodsDeliveryDO updateObj = BeanUtils.toBean(updateReqVO, GoodsDeliveryDO.class);
        goodsDeliveryDao.updateById(updateObj);
    }

    @Override
    public void deleteGoodsDelivery(String id) {
        // 校验存在
        validateGoodsDeliveryExists(id);
        // 删除
        goodsDeliveryDao.deleteById(id);
    }

    private void validateGoodsDeliveryExists(String id) {
        if (goodsDeliveryDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-物品顾送登记数据不存在");
        }
    }

    @Override
    public GoodsDeliveryDO getGoodsDelivery(String id) {
        return goodsDeliveryDao.selectById(id);
    }

    @Override
    public PageResult<GoodsDeliveryDO> getGoodsDeliveryPage(GoodsDeliveryPageReqVO pageReqVO) {
        PageResult<GoodsDeliveryDO> pageResult = goodsDeliveryDao.selectPage(pageReqVO);
//        if(CollectionUtil.isNotEmpty(pageResult.getList())){
//            for(GoodsDeliveryDO goodsDeliveryDO:pageResult.getList()){
//                if(ObjectUtil.isNotEmpty(goodsDeliveryDO.getGoodsPhotoPath())){
//                    goodsDeliveryDO.setGoodsPhotoPath(wbCommonService.getFile(goodsDeliveryDO.getGoodsPhotoPath()));
//                }
//            }
//        }
        return pageResult;
    }

    @Override
    public List<GoodsDeliveryDO> getGoodsDeliveryList(GoodsDeliveryListReqVO listReqVO) {
        return goodsDeliveryDao.selectList(listReqVO);
    }

    @Override
    public GoodsDeliveryRespVO getGoodsDeliveryById(String id) {

        GoodsDeliveryDO goodsDeliveryDO = getById(id);

        GoodsDeliveryRespVO goodsDeliveryRespVO = BeanUtils.toBean(goodsDeliveryDO,GoodsDeliveryRespVO.class);

//        if(ObjectUtil.isNotEmpty(goodsDeliveryRespVO.getGoodsPhotoPath())){
//            goodsDeliveryRespVO.setGoodsPhotoPath(wbCommonService.getFile(goodsDeliveryRespVO.getGoodsPhotoPath()));
//        }

        goodsDeliveryRespVO.setGoodsList(goodsDeliveryDetailsService.getGoodsDeliveryDetailsListByGoodsDeliveryId(id));
        return goodsDeliveryRespVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean signfor(GoodsDeliverySaveReqVO updateReqVO) {
        LambdaUpdateWrapper<GoodsDeliveryDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(GoodsDeliveryDO::getStatus,updateReqVO.getStatus())
                .set(GoodsDeliveryDO::getSignature,updateReqVO.getSignature())
                .set(GoodsDeliveryDO::getRejectionReason,updateReqVO.getRejectionReason())
                .set(GoodsDeliveryDO::getReceiptTime,new Date());
        lambdaUpdateWrapper.eq(GoodsDeliveryDO::getId,updateReqVO.getId());
        return update(lambdaUpdateWrapper);
    }
}
