package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.VisitorRegDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-对外开放登记 Service 接口
 *
 * <AUTHOR>
 */
public interface VisitorRegService extends IBaseService<VisitorRegDO>{

    /**
     * 创建实战平台-窗口业务-对外开放登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createVisitorReg(@Valid VisitorRegSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-对外开放登记
     *
     * @param updateReqVO 更新信息
     */
    void updateVisitorReg(@Valid VisitorRegSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-对外开放登记
     *
     * @param id 编号
     */
    void deleteVisitorReg(String id);

    /**
     * 获得实战平台-窗口业务-对外开放登记
     *
     * @param id 编号
     * @return 实战平台-窗口业务-对外开放登记
     */
    VisitorRegDO getVisitorReg(String id);

    /**
    * 获得实战平台-窗口业务-对外开放登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-对外开放登记分页
    */
    PageResult<VisitorRegDO> getVisitorRegPage(VisitorRegPageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-对外开放登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-对外开放登记列表
    */
    List<VisitorRegDO> getVisitorRegList(VisitorRegListReqVO listReqVO);

    /**
     * 根据ID获取对外开发信息
     * @param id
     * @return
     */
    VisitorRegRespVO getVisitorRegById(String id);

}
