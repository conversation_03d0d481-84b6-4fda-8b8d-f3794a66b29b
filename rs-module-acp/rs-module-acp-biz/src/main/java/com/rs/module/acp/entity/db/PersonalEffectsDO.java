package com.rs.module.acp.entity.db;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-收押业务-随身物品登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_db_personal_effects")
@KeySequence("acp_db_personal_effects_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_db_personal_effects")
public class PersonalEffectsDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 人员编号
     */
    private String rybh;
    /**
     * 人员姓名
     */
    private String ryxm;
    /**
     * 登记状态
     */
    private String status;

}
