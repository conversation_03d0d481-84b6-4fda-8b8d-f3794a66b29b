package com.rs.module.acp.controller.admin.db.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-羁押业务-收回登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DetainReclaimRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("入所时间")
    private Date rssj;
    @ApiModelProperty("入所原因")
    @Trans(type = TransType.DICTIONARY,key = "ZD_KSS_RSYY")
    private String rsyy;
    @ApiModelProperty("法律文书号")
    private String flwsh;
    @ApiModelProperty("收回原因")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYYWSHYY")
    private String shyy;
    @ApiModelProperty("收回日期")
    private Date shrq;
    @ApiModelProperty("经办人身份证号")
    private String jbrsfzh;
    @ApiModelProperty("经办人")
    private String jbr;
    @ApiModelProperty("经办时间")
    private String jbsj;
    @ApiModelProperty("登记状态")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYZT")
    private String status;
    @ApiModelProperty("监室号")
    private String jsh;
    @ApiModelProperty("监室名称")
    private String roomName;
}
