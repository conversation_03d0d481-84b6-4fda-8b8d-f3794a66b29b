package com.rs.module.acp.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

public class SldxxfbUtil {
    private static final String KEY_TODAY_COUNT = "todaycount";
    private static final String KEY_YESTERDAY_COUNT = "yesterdaycount";
    private static final String KEY_DIFF_NUM = "diffNum";
    private static final String KEY_ONE_YEAR_MAP = "oneYearPrisonerCountMap";
    private static final String KEY_YEAR_COUNT = "yearcount";
    private static final String KEY_NOW_TOTAL_COUNT = "nowtotalcount";
    private static final String KEY_YESTERDAY_TOTAL_COUNT = "yesterdaytotalcount";




    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM");
    /**
     * 构建统一的 JSON 响应结果
     */
    public static JSONObject buildResult(Map<String, Integer> todayYesterdayCount, List<JSONObject> yearlyData) {
        JSONObject result = new JSONObject();

        // 获取今日/昨日数量
        Integer todayCount = todayYesterdayCount.getOrDefault(KEY_TODAY_COUNT, 0);
        Integer yesterdayCount = todayYesterdayCount.getOrDefault(KEY_YESTERDAY_COUNT, 0);

        // 构造过去12个月的数据
        Map<String, Integer> oneYearPrisonerCountMap = generateMonthlyMap(yearlyData);

        result.put(KEY_TODAY_COUNT, todayCount);
        result.put(KEY_YESTERDAY_COUNT, yesterdayCount);
        result.put(KEY_DIFF_NUM, todayCount - yesterdayCount);
        result.put(KEY_ONE_YEAR_MAP, oneYearPrisonerCountMap);

        return result;
    }
    /**
     * 生成包含过去12个月的 map，默认值为0
     */
    public static Map<String, Integer> generateMonthlyMap(List<JSONObject> sourceListData) {
        Map<String, Integer> resultMap = new HashMap<>();
        LocalDate now = LocalDate.now();

        for (int i = 0; i < 12; i++) {
            String yearMonth = now.minusMonths(i).format(FORMATTER);
            Integer count =0;
            for (JSONObject sourceData :sourceListData){
                if (sourceData.getString("month").equals(yearMonth)){
                    count = sourceData.getInteger("count");
                    break;
                }
            }
            resultMap.put(yearMonth, count);
        }
        return resultMap;
    }
    public static void updatePrisonInfo(JSONObject info, String orgCode, Integer nowTotalNum, Integer historyTotalNum, Map<String, Integer> imprisonmentAmountMap) {
        if (info.containsKey("nowTotalNum")) {
            info.put("nowTotalNum", info.getIntValue("nowTotalNum") + nowTotalNum);
            info.put("historyTotalNum", info.getIntValue("historyTotalNum") + historyTotalNum);
            info.put("imprisonmentAmount", info.getIntValue("imprisonmentAmount") + imprisonmentAmountMap.getOrDefault(orgCode, 0));
        } else {
            info.put("nowTotalNum", nowTotalNum);
            info.put("historyTotalNum", historyTotalNum);
            info.put("imprisonmentAmount", imprisonmentAmountMap.getOrDefault(orgCode, 0));
        }
    }

    public static double calculateUsageRate(int nowTotalNum, int imprisonmentAmount) {
        if (imprisonmentAmount <= 0) return 0.0;
        return (double) nowTotalNum / imprisonmentAmount * 100; // 百分比
    }

    public static int calculateNewAdditions(int nowTotalNum, int historyTotalNum) {
        return nowTotalNum - historyTotalNum;
    }

    public static void updatePrisonInfo(JSONObject info) {
        int nowTotalNum = info.getIntValue("nowTotalNum");
        int imprisonmentAmount = info.getIntValue("imprisonmentAmount");
        int historyTotalNum = info.getIntValue("historyTotalNum");

        // 计算使用率和新增人数
        double usageRate = calculateUsageRate(nowTotalNum, imprisonmentAmount);
        int newAdditions = calculateNewAdditions(nowTotalNum, historyTotalNum);

        // 更新 JSON 对象
        info.put("usageRate", Math.round(usageRate)); // 四舍五入保留整数
        info.put("newAdditions", newAdditions);

    }
    public static void countJybInfo(JSONObject info) {
        int nowTotalNum = info.getIntValue("nowTotalNum");
        int imprisonmentAmount = info.getIntValue("imprisonmentAmount");

        // 计算使用率和新增人数
        double usageRate = calculateUsageRate(nowTotalNum, imprisonmentAmount);

        // 更新 JSON 对象
        info.put("usageRate", Math.round(usageRate)); // 四舍五入保留整数

    }
    /**
     * 诉讼阶段
     */
    public enum SSJDDataSourceType {
        GA("ga", "公安", Arrays.asList("11", "12", "13", "14")),
        JCY("jcy", "检察院", Arrays.asList("21", "22", "23", "24")),
        FY("fy", "法院", Arrays.asList("31", "32", "33", "34", "35", "36", "37", "38")),
        QT("qt", "其他", Arrays.asList("99", "41", "42", "43", "44"));

        private final String code;
        private final String name;
        private final List<String> codes;

        SSJDDataSourceType(String code, String name, List<String> codes) {
            this.code = code;
            this.name = name;
            this.codes = codes;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public List<String> getCodes() {
            return codes;
        }
    }
    /**
     * 构建明细列表
     */
    public static  List<JSONObject> ssjdBuildDetailList(Map<String, String> detailMap, List<String> validCodes) {
        List<JSONObject> list = new ArrayList<>();
        if (detailMap == null || detailMap.isEmpty()) {
            return list;
        }

        for (String code : validCodes) {
            String value = detailMap.get(code);
            JSONObject item = new JSONObject();
            item.put("itemName", code); // 可替换为真实名称
            item.put("itemVal", value);
            list.add(item);
        }

        return list;
    }

    /**
     * 构建每种类型的详细数据映射
     */
    private static  Map<String, String> ssjdBuildData(JSONObject rows, String type) {
        Map<String, String> detailMap = new HashMap<>();
        JSONArray detail = rows.getJSONArray(getTotalDetailKey(type));
        if (detail != null) {
            for (Object obj : detail) {
                if (obj instanceof JSONObject) {
                    JSONObject item = (JSONObject) obj;
                    String sshjCode = item.getString("sshjCode");
                    String num = item.getString("num");
                    if (sshjCode != null && !sshjCode.isEmpty() && num != null) {
                        detailMap.put(sshjCode, num);
                    }
                }
            }
        }

        return detailMap;
    }

    /**
     * 获取总数键名
     */
    private static  String getTotalKey(String type) {
        return type + "_total";
    }

    /**
     * 获取详情键名
     */
    private static String getTotalDetailKey(String type) {
        return type + "_total_detail";
    }
    public static JSONObject ssjdBuildResult(JSONObject rows) {
        JSONObject result = new JSONObject();
        // 构建并填充每个类别数据
        for (SSJDDataSourceType dic : SSJDDataSourceType.values()) {
            JSONObject item = new JSONObject();
            item.put("itemName", dic.getName());
            item.put("itemVal", rows.getIntValue(getTotalKey(dic.getCode())));

            Map<String, String> detailMap = ssjdBuildData(rows, dic.getCode());
            List<JSONObject> detailList = ssjdBuildDetailList(detailMap, dic.getCodes());

            item.put("detailList", detailList);
            result.put(dic.getCode(), item);
        }
        return result;
    }
    /**
     * 将多行 rows 数据合并，并返回与 ssjdBuildResult 相同结构的 JSON
     *
     * @param rowList 多行数据列表
     * @return 合并后的结果 JSONObject
     */
    public static JSONObject ssjdBuildMergedResult(List<JSONObject> rowList) {
        JSONObject mergedResult = new JSONObject();

        // 遍历每个类型（GA, JCY, FY, QT）
        for (SSJDDataSourceType type : SSJDDataSourceType.values()) {
            JSONObject item = new JSONObject();
            item.put("itemName", type.getName());

            int totalValue = 0;
            List<JSONObject> detailList = new ArrayList<>();

            // 存储每个 detail code 的累计值
            Map<String, Integer> detailMap = new HashMap<>();

            // 遍历每一行数据
            for (JSONObject row : rowList) {
                // 构建该行的 detail 数据
                Map<String, String> rowDetailMap = ssjdBuildData(row, type.getCode());
                // 累加到总 map 中
                for (String code : type.getCodes()) {
                    String valStr = rowDetailMap.getOrDefault(code, "0");
                    int val = Integer.parseInt(valStr);
                    detailMap.put(code, detailMap.getOrDefault(code, 0) + val);
                }

                // 累加总数
                totalValue += row.getIntValue(getTotalKey(type.getCode()));
            }

            // 构建 detailList
            for (String code : type.getCodes()) {
                JSONObject detailItem = new JSONObject();
                detailItem.put("itemName", code); // 可替换为真实名称
                detailItem.put("itemVal", detailMap.getOrDefault(code, 0));
                detailList.add(detailItem);
            }

            item.put("itemVal", totalValue);
            item.put("detailList", detailList);
            mergedResult.put(type.getCode(), item);
        }

        return mergedResult;
    }

}
