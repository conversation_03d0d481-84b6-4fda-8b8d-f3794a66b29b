package com.rs.module.acp.job.gj;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.module.acp.controller.admin.gj.vo.EquipmentUseCheckRespVO;
import com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseRespWearVO;
import com.rs.module.acp.entity.gj.EquipmentUseCheckDO;
import com.rs.module.acp.service.gj.EquipmentUseCheckService;
import com.rs.module.acp.service.gj.equipment.EquipmentUseService;
import com.rs.module.acp.service.gj.riskassmt.RiskAssmtTodoService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 戒具使用检查
 */
@Component
@Slf4j
public class EquipmentUseCheckJob {

    @Resource
    private EquipmentUseService equipmentUseService;
    @Resource
    private EquipmentUseCheckService equipmentUseCheckService;


    /**
     *  戒具使用检查记录生成任务
     */
    @XxlJob("equipmentUseCheck")
    public void equipmentUseCheck() {
        XxlJobHelper.log("戒具使用检查记录生成任务:------开始------");
        try {
            List<EquipmentUseRespWearVO> wearEquipmentUse = equipmentUseService.getWearEquipmentUse(null, null);
            for (EquipmentUseRespWearVO use : wearEquipmentUse) {
                EquipmentUseCheckRespVO checkRespVO = equipmentUseCheckService.getByUseIdAndDate(use.getId(), new Date());
                if (ObjectUtil.isNotEmpty(checkRespVO)) {
                    continue;
                }
                Date now = new Date();
                EquipmentUseCheckDO checkDO = new EquipmentUseCheckDO();
                checkDO.setCheckTime(DateUtil.beginOfDay(now));
                checkDO.setEquipmentUseId(use.getId());
                checkDO.setOrgCode(use.getOrgCode());
                checkDO.setOrgName(use.getOrgName());
                checkDO.setAddTime(now);
                checkDO.setStatus("0");
                equipmentUseCheckService.save(checkDO);
            }
        } catch (Exception e) {
            log.error("戒具使用检查记录-生成失败", e);
            XxlJobHelper.log("戒具使用检查记录-生成任务异常：{}", e.getMessage());
        }
        XxlJobHelper.log("戒具使用检查记录生成任务:-----结束------");
    }
}
