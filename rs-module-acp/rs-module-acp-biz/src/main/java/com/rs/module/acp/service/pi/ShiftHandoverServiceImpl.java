package com.rs.module.acp.service.pi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.HttpUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.module.acp.controller.admin.pi.vo.shifthandover.*;
import com.rs.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.pi.ShiftHandoverDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.ShiftHandoverDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;

import static com.rs.framework.common.pojo.CommonResult.success;


/**
 * 实战平台-巡视管控-巡控交接班登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ShiftHandoverServiceImpl extends BaseServiceImpl<ShiftHandoverDao, ShiftHandoverDO> implements ShiftHandoverService {

    @Resource
    private ShiftHandoverDao shiftHandoverDao;

    @Autowired
    private BspApi bspApi;

    @Override
    public String createShiftHandover(ShiftHandoverSaveReqVO createReqVO) {
        // 插入
        ShiftHandoverDO shiftHandover = BeanUtils.toBean(createReqVO, ShiftHandoverDO.class);
        shiftHandoverDao.insert(shiftHandover);
        // 返回
        return shiftHandover.getId();
    }

    @Override
    public void updateShiftHandover(ShiftHandoverSaveReqVO updateReqVO) {
        // 校验存在
        validateShiftHandoverExists(updateReqVO.getId());
        // 更新
        ShiftHandoverDO updateObj = BeanUtils.toBean(updateReqVO, ShiftHandoverDO.class);
        shiftHandoverDao.updateById(updateObj);
    }

    @Override
    public void deleteShiftHandover(String id) {
        // 校验存在
        validateShiftHandoverExists(id);
        // 删除
        shiftHandoverDao.deleteById(id);
    }

    private void validateShiftHandoverExists(String id) {
        if (shiftHandoverDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-巡控交接班登记数据不存在");
        }
    }

    @Override
    public ShiftHandoverDO getShiftHandover(String id) {
        return shiftHandoverDao.selectById(id);
    }

    @Override
    public String shiftReg(ShiftRegReqVO regReqVO) {
        ShiftHandoverDO shiftHandover = BeanUtils.toBean(regReqVO, ShiftHandoverDO.class);
        shiftHandover.setStatus("01");//已交班 = 待接班
        shiftHandoverDao.insert(shiftHandover);
        return shiftHandover.getId();
    }

    @Override
    public Boolean successionReg(SuccessionRegReqVO regReqVO) {
        // 校验存在
        validateShiftHandoverExists(regReqVO.getId());
        ShiftHandoverDO updateObj = BeanUtils.toBean(regReqVO, ShiftHandoverDO.class);
        updateObj.setStatus("03");//已接班
        shiftHandoverDao.updateById(updateObj);
        return true;
    }

    @Override
    public ShiftHandoverStatisticsNumVO getPersonDistribution(String patrolRoomId, String orgCode) {
        String scriptMark = "acp:szptxsgkxkjjbryqk";
        Map<String, Object> paramMap = new HashMap<>();
        //paramMap.put("mark", scriptMark);
        //paramMap.put("user", SessionUserUtil.getSessionUser());
        JSONObject result = bspApi.executeMultiQuery(scriptMark,paramMap);
        JSONArray data = result.getJSONArray("data");
        ShiftHandoverStatisticsNumVO shiftHandoverStatisticsNumVO = new ShiftHandoverStatisticsNumVO();
        if (data == null || data.isEmpty()) {
            log.error("executeFmQuery error");
            return shiftHandoverStatisticsNumVO;
        }
        for (Object obj : data) {
            JSONObject rows = JSON.parseObject(JSON.toJSONString(obj));
            String businessType = rows.getString("businesstype");
            if (StringUtil.isEmpty(businessType)) {
                continue; // 跳过无效数据
            }

            switch (businessType) {
                case "totalNumber":
                    shiftHandoverStatisticsNumVO.setTotalNumber(rows.getInteger("nums"));
                    break;
                case "rollCall":
                    shiftHandoverStatisticsNumVO.setRollCall(rows.getInteger("nums"));
                    break;
                case "outForMedicalTreatment":
                    shiftHandoverStatisticsNumVO.setOutForMedicalTreatment(rows.getInteger("nums"));
                    break;
                case "arraignment":
                    shiftHandoverStatisticsNumVO.setArraignment(rows.getInteger("nums"));
                    break;
                case "lawyerToMeet":
                    shiftHandoverStatisticsNumVO.setLawyerToMeet(rows.getInteger("nums"));
                    break;
                case "familyMeeting":
                    shiftHandoverStatisticsNumVO.setFamilyMeeting(rows.getInteger("nums"));
                    break;
                case "suggestion":
                    shiftHandoverStatisticsNumVO.setSuggestion(rows.getInteger("nums"));
                    break;
                case "leaveButNotReturn":
                    shiftHandoverStatisticsNumVO.setLeaveButNotReturn(rows.getInteger("nums"));
                    break;
                default:
                    // 可选：记录未知类型日志
                    break;
            }
        }

        return shiftHandoverStatisticsNumVO;
    }
}
