package com.rs.module.acp.service.db;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.HealthCheckDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-收押业务-入所健康检查登记 Service 接口
 *
 * <AUTHOR>
 */
public interface HealthCheckService extends IBaseService<HealthCheckDO>{

    /**
     * 创建实战平台-收押业务-入所健康检查登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createHealthCheck(@Valid HealthCheckSaveReqVO createReqVO);

    /**
     * 更新实战平台-收押业务-入所健康检查登记
     *
     * @param updateReqVO 更新信息
     */
    void updateHealthCheck(@Valid HealthCheckSaveReqVO updateReqVO);

    /**
     * 删除实战平台-收押业务-入所健康检查登记
     *
     * @param id 编号
     */
    void deleteHealthCheck(String id);

    /**
     * 获得实战平台-收押业务-入所健康检查登记
     *
     * @param id 编号
     * @return 实战平台-收押业务-入所健康检查登记
     */
    HealthCheckDO getHealthCheck(String id);

    /**
    * 获得实战平台-收押业务-入所健康检查登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-收押业务-入所健康检查登记分页
    */
    PageResult<HealthCheckDO> getHealthCheckPage(HealthCheckPageReqVO pageReqVO);

    /**
    * 获得实战平台-收押业务-入所健康检查登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-收押业务-入所健康检查登记列表
    */
    List<HealthCheckDO> getHealthCheckList(HealthCheckListReqVO listReqVO);


    HealthCheckDO getHealthCheckByRybh(String rybh);
}
