package com.rs.module.acp.service.ds.sjbs;

import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyDataSubmitReqVO;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyPrisonerTopSaveReqVO;
import com.rs.module.acp.entity.ds.sjbs.WeeklyDataSubmitJlsDO;
import com.rs.module.acp.entity.ds.sjbs.WeeklyPrisonerTopDO;
import com.rs.module.acp.util.SjbsDateUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.ds.sjbs.WeeklyPrisonerTopDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 实战平台-数据固化-每周会见被监管人员排名 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WeeklyPrisonerTopServiceImpl extends BaseServiceImpl<WeeklyPrisonerTopDao, WeeklyPrisonerTopDO> implements WeeklyPrisonerTopService {

    @Resource
    private WeeklyPrisonerTopDao weeklyPrisonerTopDao;

    @Override
    public String createWeeklyPrisonerTop(WeeklyPrisonerTopSaveReqVO createReqVO) {
        // 插入
        WeeklyPrisonerTopDO weeklyPrisonerTop = BeanUtils.toBean(createReqVO, WeeklyPrisonerTopDO.class);
        weeklyPrisonerTopDao.insert(weeklyPrisonerTop);
        // 返回
        return weeklyPrisonerTop.getId();
    }

    @Override
    public void updateWeeklyPrisonerTop(WeeklyPrisonerTopSaveReqVO updateReqVO) {
        // 校验存在
        validateWeeklyPrisonerTopExists(updateReqVO.getId());
        // 更新
        WeeklyPrisonerTopDO updateObj = BeanUtils.toBean(updateReqVO, WeeklyPrisonerTopDO.class);
        weeklyPrisonerTopDao.updateById(updateObj);
    }

    @Override
    public void deleteWeeklyPrisonerTop(String id) {
        // 校验存在
        validateWeeklyPrisonerTopExists(id);
        // 删除
        weeklyPrisonerTopDao.deleteById(id);
    }

    private void validateWeeklyPrisonerTopExists(String id) {
        if (weeklyPrisonerTopDao.selectById(id) == null) {
            throw new ServerException("实战平台-数据固化-每周会见被监管人员排名数据不存在");
        }
    }

    @Override
    public WeeklyPrisonerTopDO getWeeklyPrisonerTop(String id) {
        return weeklyPrisonerTopDao.selectById(id);
    }

    @Override
    public void saveForStatistic(List<WeeklyDataSubmitReqVO> weeklyDataSubmitList){
        for (WeeklyDataSubmitReqVO weeklyDataSubmitReqVO: weeklyDataSubmitList) {
            List<WeeklyPrisonerTopDO> list = weeklyPrisonerTopDao.selectTop10Jgry(weeklyDataSubmitReqVO.getOrgCode(), weeklyDataSubmitReqVO.getStartDate(), weeklyDataSubmitReqVO.getEndDate());
            /*Map<String, List<WeeklyPrisonerTopDO>> orgListData = list.stream().collect(Collectors.groupingBy(WeeklyPrisonerTopDO::getOrgCode));
            for (Map.Entry<String, List<WeeklyPrisonerTopDO>> entry : orgListData.entrySet()) {
                List<WeeklyPrisonerTopDO> orgList = entry.getValue();
                int pm = 0;
                for (WeeklyPrisonerTopDO entity : orgList) {
                    weeklyPrisonerTopDao.deleteByCondition(entity.getOrgCode(), entity.getStartDate(), entity.getEndDate());
                    entity.setPm(pm++);
                    entity.setWeeklyDataSubmitId(weeklyDataSubmitReqVO.getId());
                }
                weeklyPrisonerTopDao.insertBatch(orgList);
            }*/
            int pm = 0;
            for (WeeklyPrisonerTopDO entity : list) {
                weeklyPrisonerTopDao.deleteByCondition(entity.getOrgCode(), entity.getStartDate(), entity.getEndDate());
                entity.setPm(pm++);
                entity.setWeeklyDataSubmitId(weeklyDataSubmitReqVO.getId());
                entity.setStartDate(weeklyDataSubmitReqVO.getStartDate());
                entity.setEndDate(weeklyDataSubmitReqVO.getEndDate());
                entity.setStartDate(entity.getSolidificationDate());
            }
            weeklyPrisonerTopDao.insertBatch(list);
        }
    }

    @Override
    public List<WeeklyPrisonerTopDO> getWeeklyPrisonerTopByDate(String startDate, String endDate, String orgCode) {
        String[] range = SjbsDateUtil.getLastWeekRangeStr();
        if(null == startDate) startDate = range[0];
        if(null == endDate) endDate = range[1];
        if(StringUtil.isEmpty(orgCode)) orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        List<WeeklyPrisonerTopDO> list = weeklyPrisonerTopDao.selectList(new LambdaQueryWrapperX<WeeklyPrisonerTopDO>().
                eq(WeeklyPrisonerTopDO::getStartDate, startDate).eq(WeeklyPrisonerTopDO::getEndDate, endDate).
                eq(WeeklyPrisonerTopDO::getOrgCode, orgCode).eq(WeeklyPrisonerTopDO::getIsDel, 0));
        return list;
    }
    @Override
    public List<WeeklyPrisonerTopDO> getByWeeklyDataSubmitId(String weeklyDataSubmitId) {
        List<WeeklyPrisonerTopDO> list = weeklyPrisonerTopDao.selectList(new LambdaQueryWrapperX<WeeklyPrisonerTopDO>().
                eq(WeeklyPrisonerTopDO::getWeeklyDataSubmitId, weeklyDataSubmitId).eq(WeeklyPrisonerTopDO::getIsDel, 0));
        return list;
    }
}
