package com.rs.module.acp.service.gj.conflict;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictPrisonerRespVO;
import com.rs.module.acp.entity.gj.conflict.ConflictPrisonerDO;

import java.util.List;

/**
 * 实战平台-管教业务-社会矛盾化解登记-关联在押人员 Service 接口
 *
 * <AUTHOR>
 */
public interface ConflictPrisonerService extends IBaseService<ConflictPrisonerDO>{


   List<ConflictPrisonerRespVO> getPrisonerVOByEventCode(String eventCode);

    void deleteByEventCode(String eventCode);

    boolean isRegistered(String jgrybm);

    boolean batchUpdateByEventCodeAndJgrybm(String eventCode, List<String> jgrybms);

    void deleteByEventCode(List<String> eventCodes);

    void updateConfirmStatus(String eventCode, Integer confirmStatus);

}
