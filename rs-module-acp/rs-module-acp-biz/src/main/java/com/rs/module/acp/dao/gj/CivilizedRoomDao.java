package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.CivilizedRoomListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.CivilizedRoomPageReqVO;
import com.rs.module.acp.entity.gj.CivilizedRoomDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-文明监室登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface CivilizedRoomDao extends IBaseDao<CivilizedRoomDO> {


    default PageResult<CivilizedRoomDO> selectPage(CivilizedRoomPageReqVO reqVO) {
        Page<CivilizedRoomDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<CivilizedRoomDO> wrapper = new LambdaQueryWrapperX<CivilizedRoomDO>()
            .eqIfPresent(CivilizedRoomDO::getEvalMonth, reqVO.getEvalMonth())
            .likeIfPresent(CivilizedRoomDO::getRoomName, reqVO.getRoomName())
            .betweenIfPresent(CivilizedRoomDO::getApplyTime, reqVO.getApplyTime())
            .eqIfPresent(CivilizedRoomDO::getApplyUserSfzh, reqVO.getApplyUserSfzh())
            .eqIfPresent(CivilizedRoomDO::getApplyUser, reqVO.getApplyUser())
            .eqIfPresent(CivilizedRoomDO::getStatus, reqVO.getStatus())
            .eqIfPresent(CivilizedRoomDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(CivilizedRoomDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(CivilizedRoomDO::getAddTime);
        }
        Page<CivilizedRoomDO> civilizedRoomPage = selectPage(page, wrapper);
        return new PageResult<>(civilizedRoomPage.getRecords(), civilizedRoomPage.getTotal());
    }
    default List<CivilizedRoomDO> selectList(CivilizedRoomListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CivilizedRoomDO>()
            .eqIfPresent(CivilizedRoomDO::getEvalMonth, reqVO.getEvalMonth())
            .likeIfPresent(CivilizedRoomDO::getEvalMonth, reqVO.getYear())
            .likeIfPresent(CivilizedRoomDO::getRoomName, reqVO.getRoomName())
            .betweenIfPresent(CivilizedRoomDO::getApplyTime, reqVO.getApplyTime())
            .eqIfPresent(CivilizedRoomDO::getOrgCode, reqVO.getOrgCode())
            .eqIfPresent(CivilizedRoomDO::getApplyUserSfzh, reqVO.getApplyUserSfzh())
            .eqIfPresent(CivilizedRoomDO::getApplyUser, reqVO.getApplyUser())
            .eqIfPresent(CivilizedRoomDO::getStatus, reqVO.getStatus())
        .orderByDesc(CivilizedRoomDO::getEvalMonth));    }

    List<CivilizedRoomDO> getNowPushInfo();

    }
