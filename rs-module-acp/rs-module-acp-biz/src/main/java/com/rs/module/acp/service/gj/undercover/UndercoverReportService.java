package com.rs.module.acp.service.gj.undercover;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverReportListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverReportPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverReportSaveReqVO;
import com.rs.module.acp.entity.gj.UndercoverReportDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-耳目反映情况 Service 接口
 *
 * <AUTHOR>
 */
public interface UndercoverReportService extends IBaseService<UndercoverReportDO>{

    /**
     * 创建实战平台-管教业务-耳目反映情况
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createUndercoverReport(@Valid UndercoverReportSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-耳目反映情况
     *
     * @param updateReqVO 更新信息
     */
    void updateUndercoverReport(@Valid UndercoverReportSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-耳目反映情况
     *
     * @param id 编号
     */
    void deleteUndercoverReport(String id);

    /**
     * 获得实战平台-管教业务-耳目反映情况
     *
     * @param id 编号
     * @return 实战平台-管教业务-耳目反映情况
     */
    UndercoverReportDO getUndercoverReport(String id);

    /**
    * 获得实战平台-管教业务-耳目反映情况分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-耳目反映情况分页
    */
    PageResult<UndercoverReportDO> getUndercoverReportPage(UndercoverReportPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-耳目反映情况列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-耳目反映情况列表
    */
    List<UndercoverReportDO> getUndercoverReportList(UndercoverReportListReqVO listReqVO);


}
