package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.ConsularMeetingPersonDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-领事会见人员 Service 接口
 *
 * <AUTHOR>
 */
public interface ConsularMeetingPersonService extends IBaseService<ConsularMeetingPersonDO>{

    /**
     * 创建实战平台-窗口业务-领事会见人员
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createConsularMeetingPerson(@Valid ConsularMeetingPersonSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-领事会见人员
     *
     * @param updateReqVO 更新信息
     */
    void updateConsularMeetingPerson(@Valid ConsularMeetingPersonSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-领事会见人员
     *
     * @param id 编号
     */
    void deleteConsularMeetingPerson(String id);

    /**
     * 获得实战平台-窗口业务-领事会见人员
     *
     * @param id 编号
     * @return 实战平台-窗口业务-领事会见人员
     */
    ConsularMeetingPersonDO getConsularMeetingPerson(String id);

    /**
    * 获得实战平台-窗口业务-领事会见人员分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-领事会见人员分页
    */
    PageResult<ConsularMeetingPersonDO> getConsularMeetingPersonPage(ConsularMeetingPersonPageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-领事会见人员列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-领事会见人员列表
    */
    List<ConsularMeetingPersonDO> getConsularMeetingPersonList(ConsularMeetingPersonListReqVO listReqVO);

    /**
     * 实战平台-窗口业务-根据领事会见ID保存领事会见人员列表
     * @param meetingPersonList
     * @param consularMeetingId
     * @return
     */
    boolean saveConsularMeetingPersonList(List<ConsularMeetingPersonSaveReqVO> meetingPersonList,String consularMeetingId);

    /**
     * 实战平台-窗口业务-根据领事会见ID获取领事会见人员列表
     * @param consularMeetingId
     * @return
     */
    List<ConsularMeetingPersonRespVO> getMeetingPersonListByConsularMeetingId(String consularMeetingId);
}
