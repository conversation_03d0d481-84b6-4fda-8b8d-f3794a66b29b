package com.rs.module.acp.dao.sldxxfb;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.acp.controller.admin.sldxxfb.vo.WgdjVO;
import com.rs.module.base.entity.pm.PrisonerInDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* 实战平台-所领导信息发布 Dao
*
* <AUTHOR>
*/
@Mapper
public interface SldxxfbDao extends IBaseDao<PrisonerInDO> {

    /**
     * 查询当前羁押人数（含各所统计及总体）
     *
     * @param endDate   指定日期，用于统计“昨日”数据
     * @return Map 结构列表，包含 org_code、nowTotalNum、historyTotalNum
     */
    List<JSONObject> getAllPrisonerCount(@Param("endDate") Date endDate);

    /**
     * 查询近一年按月分组的羁押人数
     *
     * @return Map 结构列表，包含 month 和 count
     */
    List<JSONObject> getAllPrisonerInOneYear();
    List<JSONObject> getAllPrisonerOutOneYear();
    /**
     * 查询今日、昨日、今年、当前羁押总人数
     *
     * @param params 包含 orgCode 参数（可选）
     * @return Map 结构，包含 today_count、yesterday_count、year_count、now_total_count、yesterdayTotalNum
     */
    Map<String, Integer> getInCount(@Param("orgCode") String orgCode);

    /**
     * 查询各监所设计关押量总数
     *
     * @return Map 结构列表，包含 org_code 和 plan_imprisonment_amount
     */
    List<JSONObject> getAllPrisonerDesignAmount();

    /**
     * 查询出所人数（今日、昨日、今年、当前）
     *
     * @param params 包含 orgCode 参数（可选）
     * @return Map 结构，包含 today_count、yesterday_count、year_count、now_total_count、yesterdayTotalNum
     */
    Map<String, Integer> getOutCount(@Param("orgCode") String orgCode);

    List<JSONObject> ssjd(@Param("orgCode") String orgCode);

    Page<WgdjVO> getWggjPage(Page<WgdjVO> page, @Param("timeRange") String timeRange,
                             @Param("handleStatus") String handleStatus, @Param("code") String code,
                             @Param("codeType") String codeType);

    JSONObject getWggjAllCount(@Param("timeRange") String timeRange, @Param("code") String code, @Param("codeType") String codeType);
    JSONObject getRoomMonitorIndex(@Param("roomId") String roomId,@Param("orgCode") String orgCode);
}
