package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFacePageReqVO;
import com.rs.module.acp.entity.gj.FaceToFaceDO;
import com.rs.module.acp.job.facetoface.bo.FaceToFaceBO;
import com.rs.module.base.entity.pm.PrisonRoomWarderDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-面对面管理 Dao
*
* <AUTHOR>
*/
@Mapper
public interface FaceToFaceDao extends IBaseDao<FaceToFaceDO> {


    default PageResult<FaceToFaceDO> selectPage(FaceToFacePageReqVO reqVO) {
        Page<FaceToFaceDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<FaceToFaceDO> wrapper = new LambdaQueryWrapperX<FaceToFaceDO>()
            .eqIfPresent(FaceToFaceDO::getRoomId, reqVO.getRoomId())
            .eqIfPresent(FaceToFaceDO::getDataSources, reqVO.getDataSources())
            .eqIfPresent(FaceToFaceDO::getCheckPoliceSfzh, reqVO.getCheckPoliceSfzh())
            .eqIfPresent(FaceToFaceDO::getCheckPolice, reqVO.getCheckPolice())
            .betweenIfPresent(FaceToFaceDO::getCheckTime, reqVO.getCheckTime())
            .eqIfPresent(FaceToFaceDO::getSituationRecord, reqVO.getSituationRecord())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(FaceToFaceDO::getAddTime);
        }
        Page<FaceToFaceDO> faceToFacePage = selectPage(page, wrapper);
        return new PageResult<>(faceToFacePage.getRecords(), faceToFacePage.getTotal());
    }
    default List<FaceToFaceDO> selectList(FaceToFaceListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<FaceToFaceDO>()
            .eqIfPresent(FaceToFaceDO::getRoomId, reqVO.getRoomId())
            .eqIfPresent(FaceToFaceDO::getDataSources, reqVO.getDataSources())
            .eqIfPresent(FaceToFaceDO::getCheckPoliceSfzh, reqVO.getCheckPoliceSfzh())
            .eqIfPresent(FaceToFaceDO::getCheckPolice, reqVO.getCheckPolice())
            .betweenIfPresent(FaceToFaceDO::getCheckTime, reqVO.getCheckTime())
            .eqIfPresent(FaceToFaceDO::getSituationRecord, reqVO.getSituationRecord())
        .orderByDesc(FaceToFaceDO::getAddTime));    }


    List<FaceToFaceBO> getNeedRemindRoomId();

}
