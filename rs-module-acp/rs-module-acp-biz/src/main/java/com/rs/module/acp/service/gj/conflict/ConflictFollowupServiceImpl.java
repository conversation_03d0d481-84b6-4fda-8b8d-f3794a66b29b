package com.rs.module.acp.service.gj.conflict;

import cn.hutool.core.bean.BeanUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictFollowupRespVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictFollowupSaveReqVO;
import com.rs.module.acp.dao.gj.conflict.ConflictFollowupDao;
import com.rs.module.acp.entity.gj.conflict.ConflictFollowupDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


/**
 * 实战平台-管教业务-社会矛盾回访 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ConflictFollowupServiceImpl extends BaseServiceImpl<ConflictFollowupDao, ConflictFollowupDO> implements ConflictFollowupService {

    @Resource
    private ConflictFollowupDao conflictFollowupDao;

    @Override
    public String createConflictFollowup(ConflictFollowupSaveReqVO createReqVO) {
        // 插入
        ConflictFollowupDO conflictFollowup = BeanUtils.toBean(createReqVO, ConflictFollowupDO.class);
        conflictFollowupDao.insert(conflictFollowup);
        // 返回
        return conflictFollowup.getId();
    }

    private void validateConflictFollowupExists(String id) {
        if (conflictFollowupDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-社会矛盾回访数据不存在");
        }
    }

    @Override
    public ConflictFollowupRespVO getConflictFollowup(String eventCode) {
        ConflictFollowupDO conflictFollowupDO = conflictFollowupDao.selectOne(ConflictFollowupDO::getEventCode, eventCode);
        if (conflictFollowupDO == null) {
            return null;
        }
        return BeanUtil.copyProperties(conflictFollowupDO, ConflictFollowupRespVO.class);
    }

}
