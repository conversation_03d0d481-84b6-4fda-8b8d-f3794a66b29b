package com.rs.module.acp.service.gj.performance;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.performancekss.PerformanceKssApprovalReqVO;
import com.rs.module.acp.controller.admin.gj.vo.performancekss.PerformanceKssListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.performancekss.PerformanceKssPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.performancekss.PerformanceKssSaveReqVO;
import com.rs.module.acp.entity.gj.PerformanceKssDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-人员表现鉴定表-看守所 Service 接口
 *
 * <AUTHOR>
 */
public interface PerformanceKssService extends IBaseService<PerformanceKssDO>{

    /**
     * 创建实战平台-管教业务-人员表现鉴定表-看守所
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPerformanceKss(@Valid PerformanceKssSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-人员表现鉴定表-看守所
     *
     * @param updateReqVO 更新信息
     */
    void updatePerformanceKss(@Valid PerformanceKssSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-人员表现鉴定表-看守所
     *
     * @param id 编号
     */
    void deletePerformanceKss(String id);

    /**
     * 获得实战平台-管教业务-人员表现鉴定表-看守所
     *
     * @param id 编号
     * @return 实战平台-管教业务-人员表现鉴定表-看守所
     */
    PerformanceKssDO getPerformanceKss(String id);

    /**
    * 获得实战平台-管教业务-人员表现鉴定表-看守所分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-人员表现鉴定表-看守所分页
    */
    PageResult<PerformanceKssDO> getPerformanceKssPage(PerformanceKssPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-人员表现鉴定表-看守所列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-人员表现鉴定表-看守所列表
    */
    List<PerformanceKssDO> getPerformanceKssList(PerformanceKssListReqVO listReqVO);


    void approval(PerformanceKssApprovalReqVO approvalReqVO);
}
