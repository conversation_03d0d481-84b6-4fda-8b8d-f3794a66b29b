package com.rs.module.acp.job.diagnosisassmtjds;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.druid.util.StringUtils;
import com.bsp.common.util.SnowflakeIdUtil;
import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.dto.UserRespDTO;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.util.BspDbUtil;
import com.rs.module.acp.entity.gj.DiagnosisAssmtJdsDO;
import com.rs.module.acp.entity.gj.MonthlyAssmtJdsDO;
import com.rs.module.acp.enums.gj.DiagnosisAssmtJdsPeriodEnum;
import com.rs.module.acp.enums.gj.DiagnosisAssmtJdsTypeEnum;
import com.rs.module.acp.enums.gj.MonthlyAssmtJdsStatus;
import com.rs.module.acp.service.gj.diagnosiassmtjds.DiagnosisAssmtJdsService;
import com.rs.module.acp.service.gj.diagnosiassmtjds.MonthlyAssmtJdsService;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderRespVO;
import com.rs.module.base.service.pm.PrisonRoomWarderService;
import com.rs.util.ThreadPoolUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DiagnosisAssmtJdsJob {

    @Autowired
    private DiagnosisAssmtJdsService diagnosisAssmtJdsService;

    @Autowired
    private BspApi bspApi;

    @Autowired
    private PrisonRoomWarderService prisonRoomWarderService;

    @Autowired
    private MonthlyAssmtJdsService monthlyAssmtJdsService;

    //诊断评估推送  每天判断
    @XxlJob("handleDiagnosisAssmtJds")
    public void handleDiagnosisAssmtJds() {

        //解析出满三个月 的 戒毒人员生理脱毒评估
        handleDiagnosisAssmtJds(DiagnosisAssmtJdsPeriodEnum.THREE_MONTH, DiagnosisAssmtJdsTypeEnum.SLTD);

        //解析出满一年 的 戒毒人员生理脱毒评估
        handleDiagnosisAssmtJds(DiagnosisAssmtJdsPeriodEnum.ONE_YEAR, DiagnosisAssmtJdsTypeEnum.SLTD);

        //解析出满两年 的 戒毒人员生理脱毒评估
        handleDiagnosisAssmtJds(DiagnosisAssmtJdsPeriodEnum.TWO_YEAR, DiagnosisAssmtJdsTypeEnum.SLTD);

        //解析出满一年 的 戒毒人员身心康复评估
        handleDiagnosisAssmtJds(DiagnosisAssmtJdsPeriodEnum.ONE_YEAR, DiagnosisAssmtJdsTypeEnum.SXKF);

        //解析出满两年 的 戒毒人员身心康复评估
        handleDiagnosisAssmtJds(DiagnosisAssmtJdsPeriodEnum.TWO_YEAR, DiagnosisAssmtJdsTypeEnum.SXKF);

        //解析出满一年 的 戒毒人员社会环境与适应能力评估
        handleDiagnosisAssmtJds(DiagnosisAssmtJdsPeriodEnum.ONE_YEAR, DiagnosisAssmtJdsTypeEnum.SHHJYSYNL);

        //解析出满两年 的 戒毒人员社会环境与适应能力评估
        handleDiagnosisAssmtJds(DiagnosisAssmtJdsPeriodEnum.TWO_YEAR, DiagnosisAssmtJdsTypeEnum.SHHJYSYNL);

        //解析出满一年 的 戒毒人员行为表新评估
        handleDiagnosisAssmtJds(DiagnosisAssmtJdsPeriodEnum.ONE_YEAR, DiagnosisAssmtJdsTypeEnum.XWBX);

        //解析出满两年 的 戒毒人员行为表新评估
        handleDiagnosisAssmtJds(DiagnosisAssmtJdsPeriodEnum.TWO_YEAR, DiagnosisAssmtJdsTypeEnum.XWBX);

    }

    //月度考核推送 每天判断  入所超过10天则推送
    @XxlJob("monthAssmtJds")
    public void monthAssmtJds() {

        // 小于20号不推送
//        int dayOfMonth = LocalDate.now().getDayOfMonth();
//        if (dayOfMonth < 20) {
//            return;
//        }

        // 从戒毒所机构 获取推送数据
        List<MonthlyAssmtJdsDO> monthlyAssmtJds = monthlyAssmtJdsService.getMonthlyAssmtJdsListByInJds();
        for (MonthlyAssmtJdsDO monthlyAssmtJd : monthlyAssmtJds) {
            monthlyAssmtJd.setAddTime(new Date());
            monthlyAssmtJd.setPushTime(new Date());
            monthlyAssmtJd.setStatus(MonthlyAssmtJdsStatus.DDJ.getCode());
        }
        monthlyAssmtJdsService.saveBatch(monthlyAssmtJds);
        ConcurrentHashMap<String, List<ReceiveUser>> map = new ConcurrentHashMap<>();
        Map<String, String> collect = monthlyAssmtJds.stream().collect(Collectors.toMap(MonthlyAssmtJdsDO::getRoomId, MonthlyAssmtJdsDO::getOrgCode, (o1, o2) -> o2));
        CountDownLatch cdl = new CountDownLatch(collect.size());
        ThreadPoolExecutor pool = ThreadPoolUtil.getPool();
        for (Map.Entry<String, String> entry : collect.entrySet()) {
            pool.execute(() -> {
                try {
                    List<PrisonRoomWarderRespVO> respVOS = prisonRoomWarderService.getListByRoomId(entry.getValue(), entry.getKey());
                    List<ReceiveUser> receiveUserList = respVOS.stream().map(p -> new ReceiveUser(p.getPoliceSfzh(), entry.getValue())).collect(Collectors.toList());
                    // roomId - 接收用户
                    map.put(entry.getKey(), receiveUserList);
                } catch (Exception e) {
                    log.error(e.getMessage());
                } finally {
                    cdl.countDown();
                }
            });
        }
        try {
            cdl.await(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error(e.getMessage());
        }
        // 发送消息
        String fUser = null, fUserName = "系统";
        for (MonthlyAssmtJdsDO monthlyAssmtJd : monthlyAssmtJds) {
            try {
                List<ReceiveUser> receiveUsers = map.get(monthlyAssmtJd.getRoomId());
                if (CollectionUtil.isNotEmpty(receiveUsers)) {
                    String title = "【" + monthlyAssmtJd.getJgryxm() + "】的" + monthlyAssmtJd.getMonthPeriod() + "考核推送，请进行考核登记";
                    // todo 跳转链接
                    String url = "/#/diagnosticEvaluation/monthlyAssessments";
                    SendMessageUtil.sendTodoMsg(title, title, url, "bjsqzgljds",
                            fUser, fUserName, monthlyAssmtJd.getOrgCode(), monthlyAssmtJd.getOrgName(),
                            "pc", monthlyAssmtJd.getId(), receiveUsers);
                }
            } catch (Exception e) {
                log.error("monthAssmtJds:", e);
            }
        }

    }


    private ConcurrentHashMap<String, List<ReceiveUser>> getOrgCodeList(List<DiagnosisAssmtJdsDO> list,
                                                                        DiagnosisAssmtJdsTypeEnum diagnosisAssmtJdsTypeEnum) {
        ConcurrentHashMap<String, List<ReceiveUser>> map = new ConcurrentHashMap<>();

        try {
            String ylmjRoleId = BspDbUtil.getParam("YLMJ_ROLEID");
            if(StringUtils.isEmpty(ylmjRoleId)){
                ylmjRoleId = "1c7zez16g0pzjhasbv86y9gusz9q8ygi";
            }
            final String finalYlmjRoleId = ylmjRoleId;
            if (DiagnosisAssmtJdsTypeEnum.XWBX.getCode().equals(diagnosisAssmtJdsTypeEnum.getCode())) {
                Map<String, String> collect = list.stream().collect(Collectors.toMap(DiagnosisAssmtJdsDO::getRoomId, DiagnosisAssmtJdsDO::getOrgCode, (o1, o2) -> o2));
                CountDownLatch cdl = new CountDownLatch(collect.size());
                ThreadPoolExecutor pool = ThreadPoolUtil.getPool();
                for (Map.Entry<String, String> entry : collect.entrySet()) {
                    pool.execute(() -> {
                        try {
                            List<PrisonRoomWarderRespVO> respVOS = prisonRoomWarderService.getListByRoomId(entry.getValue(), entry.getKey());
                            List<ReceiveUser> receiveUserList = respVOS.stream().map(p -> new ReceiveUser(p.getPoliceSfzh(), entry.getValue())).collect(Collectors.toList());
                            // roomId - 接收用户
                            map.put(entry.getKey(), receiveUserList);
                        } catch (Exception e) {
                            log.error(e.getMessage());
                        } finally {
                            cdl.countDown();
                        }
                    });
                }
                try {
                    cdl.await(10, TimeUnit.SECONDS);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }

            } else {
                List<String> orgList = new ArrayList<>(list.stream().collect(Collectors.toMap(DiagnosisAssmtJdsDO::getOrgCode, DiagnosisAssmtJdsDO::getOrgCode, (o1, o2) -> o2)).values());
                CountDownLatch cdl = new CountDownLatch(orgList.size());
                ThreadPoolExecutor pool = ThreadPoolUtil.getPool();
                for (String org : orgList) {
                    pool.execute(() -> {
                        try {
                            List<UserRespDTO> userRespDTOList = bspApi.getUserNameIdCardByOrgAndRole(org, finalYlmjRoleId);
                            List<ReceiveUser> receiveUsers = BeanUtils.toBean(userRespDTOList, ReceiveUser.class);
                            if(CollectionUtil.isNotEmpty(receiveUsers)){
                                receiveUsers.forEach(u->u.setOrgCode(org));
                            } else {
                                receiveUsers = new ArrayList<>();
                            }
                            // org - 接收用户
                            map.put(org, receiveUsers);
                        } catch (Exception e) {
                            log.error(e.getMessage());
                        } finally {
                            cdl.countDown();
                        }
                    });
                }
                try {
                    cdl.await(10, TimeUnit.SECONDS);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return map;
    }

    private void handleDiagnosisAssmtJds(DiagnosisAssmtJdsPeriodEnum diagnosisAssmtJdsPeriodEnum,
                                         DiagnosisAssmtJdsTypeEnum diagnosisAssmtJdsTypeEnum) {
        String fUser = null, fUserName = "系统";
        List<DiagnosisAssmtJdsDO> sltd01 = diagnosisAssmtJdsService.handleDiagnosisAssmtJds(diagnosisAssmtJdsPeriodEnum.getCode(),
                diagnosisAssmtJdsTypeEnum.getCode(), diagnosisAssmtJdsPeriodEnum.getTime());
        if (CollectionUtil.isNotEmpty(sltd01)) {
            ConcurrentHashMap<String, List<ReceiveUser>> orgUserMap = getOrgCodeList(sltd01, diagnosisAssmtJdsTypeEnum);
            String gjmjRolecode = BspDbUtil.getParam("GJMJ_ROLECODE");
            String ylmjRolecode = BspDbUtil.getParam("YLMJ_ROLECODE");
            for (DiagnosisAssmtJdsDO diagnosisAssmtJdsDO : sltd01) {
                diagnosisAssmtJdsDO.setId(SnowflakeIdUtil.getGuid());
                diagnosisAssmtJdsDO.setPushTime(new Date());

                String title = "【" + diagnosisAssmtJdsDO.getJgryxm() + "】" + diagnosisAssmtJdsPeriodEnum.getName()
                        + "的" + diagnosisAssmtJdsTypeEnum.getName();
                // todo 跳转链接
                String url = "/#/diagnosticEvaluation/disciplineMedical";
                List<ReceiveUser> receiveUsers;
                if (DiagnosisAssmtJdsTypeEnum.XWBX.getCode().equals(diagnosisAssmtJdsTypeEnum.getCode())) {
                    receiveUsers = orgUserMap.get(diagnosisAssmtJdsDO.getRoomId());
                    diagnosisAssmtJdsDO.setPushJobPositions(gjmjRolecode);
                } else {
                    diagnosisAssmtJdsDO.setPushJobPositions(ylmjRolecode);
                    receiveUsers = orgUserMap.get(diagnosisAssmtJdsDO.getOrgCode());
                }

                if (CollectionUtil.isNotEmpty(receiveUsers)) {
                    try {
                        SendMessageUtil.sendTodoMsg(title, title, url, "bjsqzgljds",
                                fUser, fUserName, diagnosisAssmtJdsDO.getOrgCode(), diagnosisAssmtJdsDO.getOrgName(),
                                "pc", diagnosisAssmtJdsDO.getId(), receiveUsers);
                    } catch (Exception e) {
                        log.error(e.getMessage());
                    }
                }

            }
            //保存数据
            diagnosisAssmtJdsService.saveBatch(sltd01);
        }
    }

}
