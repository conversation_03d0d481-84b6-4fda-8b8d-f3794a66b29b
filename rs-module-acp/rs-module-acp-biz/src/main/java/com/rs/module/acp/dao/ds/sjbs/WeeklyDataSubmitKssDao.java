package com.rs.module.acp.dao.ds.sjbs;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyDataSubmitKssRespVO;
import com.rs.module.acp.entity.ds.sjbs.WeeklyDataSubmitKssDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 实战平台-数据固化-每周数据报送(看守所) Dao
*
* <AUTHOR>
*/
@Mapper
public interface WeeklyDataSubmitKssDao extends IBaseDao<WeeklyDataSubmitKssDO> {
    /**
     * 律师会见记录统计
     * @param orgCode 机构编码
     * @param startDate 开始日期(yyyy-MM-dd)
     * @param endDate 结束日期(yyyy-MM-dd)
     * @return 统计结果
     */
    List<WeeklyDataSubmitKssDO> statisticNumLawyerMeeting(
            @Param("orgCode") String orgCode,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    /**
     * 看守所提询统计
     * @param orgCode 机构编码
     * @param startDate 开始日期(yyyy-MM-dd)
     * @param endDate 结束日期(yyyy-MM-dd)
     * @return 统计结果
     */
    List<JSONObject> statisticNumTx(
            @Param("orgCode") String orgCode,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    /**
     * 看守所提解统计
     * @param orgCode 机构编码
     * @param startDate 开始日期(yyyy-MM-dd)
     * @param endDate 结束日期(yyyy-MM-dd)
     * @return 统计结果
     */
    List<JSONObject> statisticNumTj(
            @Param("orgCode") String orgCode,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    int deleteByCondition(
            @Param("orgCode") String orgCode,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);
}
