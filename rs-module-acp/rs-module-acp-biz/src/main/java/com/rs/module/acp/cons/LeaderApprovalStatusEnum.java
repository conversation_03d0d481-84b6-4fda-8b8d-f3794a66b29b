package com.rs.module.acp.cons;

/**
 * 领导审批状态枚举
 */
public enum LeaderApprovalStatusEnum {
    
    PENDING("1", "待审批"),
    APPROVED("2", "同意"),
    REJECTED("3", "不同意");

    private final String code;
    private final String value;

    LeaderApprovalStatusEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 状态码
     * @return 对应的枚举值
     */
    public static LeaderApprovalStatusEnum getByCode(String code) {
        for (LeaderApprovalStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据value获取枚举值
     *
     * @param value 状态值
     * @return 对应的枚举值
     */
    public static LeaderApprovalStatusEnum getByValue(String value) {
        for (LeaderApprovalStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
}