package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.DefaultCasePersonneDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-办案人员 Service 接口
 *
 * <AUTHOR>
 */
public interface DefaultCasePersonneService extends IBaseService<DefaultCasePersonneDO>{

    /**
     * 创建实战平台-窗口业务-办案人员
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createDefaultCasePersonne(@Valid DefaultCasePersonneSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-办案人员
     *
     * @param updateReqVO 更新信息
     */
    void updateDefaultCasePersonne(@Valid DefaultCasePersonneSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-办案人员
     *
     * @param id 编号
     */
    void deleteDefaultCasePersonne(String id);

    /**
     * 获得实战平台-窗口业务-办案人员
     *
     * @param id 编号
     * @return 实战平台-窗口业务-办案人员
     */
    DefaultCasePersonneDO getDefaultCasePersonne(String id);

    /**
    * 获得实战平台-窗口业务-办案人员分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-办案人员分页
    */
    PageResult<DefaultCasePersonneDO> getDefaultCasePersonnePage(DefaultCasePersonnePageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-办案人员列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-办案人员列表
    */
    List<DefaultCasePersonneDO> getDefaultCasePersonneList(DefaultCasePersonneListReqVO listReqVO);


}
