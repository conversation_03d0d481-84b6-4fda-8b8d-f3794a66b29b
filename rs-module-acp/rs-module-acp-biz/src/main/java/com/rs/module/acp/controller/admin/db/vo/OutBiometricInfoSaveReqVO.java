package com.rs.module.acp.controller.admin.db.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-收押业务-出所生物特征信息新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class OutBiometricInfoSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("采集项目类型")
    @NotEmpty(message = "采集项目类型不能为空")
    private String cjxmlx;

    @ApiModelProperty("生物特征,存储采集系统返回JSON结构")
    private String swtz;

    @ApiModelProperty("生物特征附件")
    private String swtzfj;

    @ApiModelProperty("核验结果")
    private String hyjg;

    @ApiModelProperty("备注")
    private String bz;

}
