package com.rs.module.acp.job.sys;

import java.sql.Connection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.sql.DataSource;

import org.springframework.stereotype.Component;

import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.common.util.TemplateUtils;
import com.bsp.common.util.db.ConnectionFactory;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.SocketService;
import com.rs.module.acp.cons.VoiceBroadConstants;
import com.rs.module.acp.entity.sys.VbConfigCurrentDO;
import com.rs.module.acp.entity.sys.VbConfigTimedDO;
import com.rs.module.acp.entity.sys.VbTaskDO;
import com.rs.module.acp.service.sys.VbConfigCurrentService;
import com.rs.module.acp.service.sys.VbConfigGlobalService;
import com.rs.module.acp.service.sys.VbConfigTimedService;
import com.rs.module.acp.service.sys.VbTaskService;
import com.rs.module.acp.util.VoiceBroadUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;

/**
 * 语音播报-播报定时任务
 * <AUTHOR>
 * @date 2025年6月26日
 */
@Component
public class VoiceBroadcastJob {
	
	@Resource
	DataSource ds;
	
	@Resource
	private VbConfigTimedService timedService;
	
	@Resource
	private VbConfigCurrentService currentService;
	
	@Resource
	private VbConfigGlobalService globalService;
	
	@Resource
	private VbTaskService taskService;
	
	@Resource
	private SocketService socketService;

	/**
	 * 构建定时播报任务
	 */
	@XxlJob("buildTimedVbTask")
	public void buildTimedVbTask() {
		XxlJobHelper.log("构建定时播报任务：-----开始-------");
		Connection conn = null;
        
        try {
        	//清理播报任务
        	cleanVbTask();
        	
        	//获取待播报任务
        	List<VbConfigTimedDO> configList = timedService.getEnabledTimedConfig(null);
        	for(VbConfigTimedDO config : configList) {
        		String queryScript = config.getQueryScript();
        		List<Entity> dataList = Db.use(ds).query(queryScript, new HashMap<>());
        		if(CollectionUtil.isNotNull(configList)) {
	        		for(Entity entity : dataList) {
	        			VbTaskDO taskDo = new VbTaskDO();
	        			
	        			//复制定时配置的属性
	        			BeanUtil.copyProperties(config, taskDo, "id", "addTime");
	        			
	        			//复制查询结果的属性
	        			VoiceBroadUtil.updateTaskByEntity(taskDo, entity);
	        			
	        			//设置属性
	        			taskDo.setId(StringUtil.getGuid32());
	        			taskDo.setStatus(VoiceBroadConstants.JOB_STATUS_WAIT);
	        			taskDo.setVbType(VoiceBroadConstants.VB_TYPE_TIMED);
	        			taskDo.setAddTime(new Date());
	        			
	        			//处理播报内容
	        			Map<String, Object> context = VoiceBroadUtil.entityToMap(entity);
	        			String content = TemplateUtils.parseTemplate(config.getContent(), context, null);
	        			taskDo.setContent(content);
	        			
	        			//保存播报任务
	        			taskService.save(taskDo);
	        			
	        			//异步推送WebSocket消息
	        			VoiceBroadUtil.sendSocketMessage(taskDo);
	        		}
        		}
        	}
        }
        catch(Exception e) {
        	XxlJobHelper.log("定时播报任务运行异常：-----异常信息如下：-------");
        	XxlJobHelper.log(e.getMessage());
        }
        finally {
        	ConnectionFactory.destoryResource(conn);
        }
        
        XxlJobHelper.log("构建定时播报任务：-----结束-------");
	}
	
	/**
	 * 构建实时播报任务
	 */
	@XxlJob("buildCurrentVbTask")
	public void buildCurrentVbTask() {
		XxlJobHelper.log("构建实时播报任务：-----开始-------");
        
        try {
        	//清理播报任务
        	cleanVbTask();
        	
        	//获取待播报任务
        	List<VbConfigCurrentDO> configList = currentService.getWaittingCurrentConfig(null);
        	for(VbConfigCurrentDO config : configList) {
        		taskService.saveCurrentJob(config);
        	}
        }
        catch(Exception e) {
        	XxlJobHelper.log("实时播报任务运行异常：-----异常信息如下：-------");
        	XxlJobHelper.log(e.getMessage());
        }
        
        XxlJobHelper.log("构建实时播报任务：-----结束-------");
	}
	
	/**
	 * 清理播报任务
	 */
	public void cleanVbTask() {
		XxlJobHelper.log("清理播报任务：-----开始-------");
        
        try {
        	List<VbTaskDO> cleanTaskList = taskService.getNeedCleanTaskList();
        	for(VbTaskDO task : cleanTaskList) {
        		task.setStatus(VoiceBroadConstants.JOB_STATUS_CANCEL);
        		task.setCancelTime(new Date());
        		
        		//更新实时播报任务
        		if(VoiceBroadConstants.VB_TYPE_CURRENT == task.getVbType()) {
        			VbConfigCurrentDO currentDO = currentService.getById(task.getBusinessId());
        			if(currentDO != null) {
        				currentDO.setStatus(VoiceBroadConstants.JOB_STATUS_CANCEL);
        				currentService.updateById(currentDO);
        			}
        		}
        	}
        	taskService.updateBatchById(cleanTaskList);
        }
        catch(Exception e) {
        	XxlJobHelper.log("清理播报任务异常：-----异常信息如下：-------");
        	XxlJobHelper.log(e.getMessage());
        }
        
        XxlJobHelper.log("清理播报任务：-----结束-------");
	}
}
