package com.rs.module.acp.service.gj;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.EquipmentUseCheckRespVO;
import com.rs.module.acp.controller.admin.gj.vo.EquipmentUseCheckSaveReqVO;
import com.rs.module.acp.dao.gj.EquipmentUseCheckDao;
import com.rs.module.acp.entity.gj.EquipmentUseCheckDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;


/**
 * 实战平台-管教业务-戒具检查 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EquipmentUseCheckServiceImpl extends BaseServiceImpl<EquipmentUseCheckDao, EquipmentUseCheckDO> implements EquipmentUseCheckService {

    @Resource
    private EquipmentUseCheckDao equipmentUseCheckDao;

    @Override
    public String createEquipmentUseCheck(EquipmentUseCheckSaveReqVO createReqVO) {
        // 插入
        EquipmentUseCheckDO equipmentUseCheck = BeanUtils.toBean(createReqVO, EquipmentUseCheckDO.class);
        equipmentUseCheckDao.insert(equipmentUseCheck);
        // 返回
        return equipmentUseCheck.getId();
    }

    @Override
    public void updateEquipmentUseCheck(EquipmentUseCheckSaveReqVO updateReqVO) {
        // 更新
        EquipmentUseCheckDO updateObj = BeanUtils.toBean(updateReqVO, EquipmentUseCheckDO.class);
        updateObj.setStatus("1");
        updateObj.setCheckTime(new Date());
        equipmentUseCheckDao.updateById(updateObj);
    }

    @Override
    public void deleteEquipmentUseCheck(String id) {
        // 校验存在
        validateEquipmentUseCheckExists(id);
        // 删除
        equipmentUseCheckDao.deleteById(id);
    }

    private void validateEquipmentUseCheckExists(String id) {
        if (equipmentUseCheckDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-戒具检查数据不存在");
        }
    }

    @Override
    public EquipmentUseCheckDO getEquipmentUseCheck(String id) {
        return equipmentUseCheckDao.selectById(id);
    }

    @Override
    public List<EquipmentUseCheckRespVO> getCheckList(String useId) {
        return equipmentUseCheckDao.getCheckList(useId);
    }

    @Override
    public List<EquipmentUseCheckRespVO> getCheckByRoomCode(String roomCode) {
        return equipmentUseCheckDao.getCheckByRoomCode(roomCode);
    }

    @Override
    public List<EquipmentUseCheckRespVO> getCheckRecordList(String roomCode, Date startTime, Date endTimme) {
        return equipmentUseCheckDao.getCheckRecordList(roomCode, startTime, endTimme);
    }

    @Override
    public EquipmentUseCheckRespVO getByUseIdAndDate(String useId, Date checkDate) {
        return equipmentUseCheckDao.getByUseIdAndDate(useId, checkDate);
    }

}
