package com.rs.module.acp.dao.zh;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateAssessorListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateAssessorPageReqVO;
import com.rs.module.acp.entity.zh.IndicatorCateAssessorDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 综合管理-绩效考核指标分类与考评人关联 Dao
*
* <AUTHOR>
*/
@Mapper
public interface IndicatorCateAssessorDao extends IBaseDao<IndicatorCateAssessorDO> {


    default PageResult<IndicatorCateAssessorDO> selectPage(IndicatorCateAssessorPageReqVO reqVO) {
        Page<IndicatorCateAssessorDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<IndicatorCateAssessorDO> wrapper = new LambdaQueryWrapperX<IndicatorCateAssessorDO>()
            .eqIfPresent(IndicatorCateAssessorDO::getIndicatorCateId, reqVO.getIndicatorCateId())
            .eqIfPresent(IndicatorCateAssessorDO::getAssessorSfzh, reqVO.getAssessorSfzh())
            .likeIfPresent(IndicatorCateAssessorDO::getAssessorName, reqVO.getAssessorName())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(IndicatorCateAssessorDO::getAddTime);
        }
        Page<IndicatorCateAssessorDO> indicatorCateAssessorPage = selectPage(page, wrapper);
        return new PageResult<>(indicatorCateAssessorPage.getRecords(), indicatorCateAssessorPage.getTotal());
    }
    default List<IndicatorCateAssessorDO> selectList(IndicatorCateAssessorListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<IndicatorCateAssessorDO>()
            .eqIfPresent(IndicatorCateAssessorDO::getIndicatorCateId, reqVO.getIndicatorCateId())
            .eqIfPresent(IndicatorCateAssessorDO::getAssessorSfzh, reqVO.getAssessorSfzh())
            .likeIfPresent(IndicatorCateAssessorDO::getAssessorName, reqVO.getAssessorName())
        .orderByDesc(IndicatorCateAssessorDO::getAddTime));    }


    }
