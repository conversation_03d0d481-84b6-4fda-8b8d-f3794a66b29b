package com.rs.module.acp.controller.admin.db.vo.jxjsdj;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-减刑登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class JxjsdjJcyjdReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("检查院监督提请日期")
    private Date jcyjdtqrq;

    @ApiModelProperty("检查院监督审核日期")
    private Date jcyjdshrq;

    @ApiModelProperty("检查院监督意见")
    private String jcyjdyj;

    @ApiModelProperty("检查院监督材料")
    private String jcyjdcl;



}
