package com.rs.module.acp.service.wb;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.LeadershipReceptionDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.LeadershipReceptionDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-所领导接待登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LeadershipReceptionServiceImpl extends BaseServiceImpl<LeadershipReceptionDao, LeadershipReceptionDO> implements LeadershipReceptionService {

    @Resource
    private LeadershipReceptionDao leadershipReceptionDao;

    @Override
    public String createLeadershipReception(LeadershipReceptionSaveReqVO createReqVO) {
        // 插入
        LeadershipReceptionDO leadershipReception = BeanUtils.toBean(createReqVO, LeadershipReceptionDO.class);
        leadershipReceptionDao.insert(leadershipReception);
        // 返回
        return leadershipReception.getId();
    }

    @Override
    public void updateLeadershipReception(LeadershipReceptionSaveReqVO updateReqVO) {
        // 校验存在
        validateLeadershipReceptionExists(updateReqVO.getId());
        // 更新
        LeadershipReceptionDO updateObj = BeanUtils.toBean(updateReqVO, LeadershipReceptionDO.class);
        leadershipReceptionDao.updateById(updateObj);
    }

    @Override
    public void deleteLeadershipReception(String id) {
        // 校验存在
        validateLeadershipReceptionExists(id);
        // 删除
        leadershipReceptionDao.deleteById(id);
    }

    private void validateLeadershipReceptionExists(String id) {
        if (leadershipReceptionDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-所领导接待登记数据不存在");
        }
    }

    @Override
    public LeadershipReceptionDO getLeadershipReception(String id) {
        return leadershipReceptionDao.selectById(id);
    }

    @Override
    public PageResult<LeadershipReceptionDO> getLeadershipReceptionPage(LeadershipReceptionPageReqVO pageReqVO) {
        return leadershipReceptionDao.selectPage(pageReqVO);
    }

    @Override
    public List<LeadershipReceptionDO> getLeadershipReceptionList(LeadershipReceptionListReqVO listReqVO) {
        return leadershipReceptionDao.selectList(listReqVO);
    }


}
