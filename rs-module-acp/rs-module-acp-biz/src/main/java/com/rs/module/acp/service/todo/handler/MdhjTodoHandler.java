package com.rs.module.acp.service.todo.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.module.acp.dao.gj.conflict.ConflictPrisonerDao;
import com.rs.module.acp.entity.gj.conflict.ConflictPrisonerDO;
import com.rs.module.acp.service.todo.TodoBusinessType;
import com.rs.module.acp.service.todo.TodoContext;
import com.rs.module.acp.service.todo.TodoHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MdhjTodoHandler implements TodoHandler<List<ConflictPrisonerDO>> {

    @Autowired
    private ConflictPrisonerDao conflictPrisonerDao;


    @Override
    public boolean canHandle(TodoContext context) {
        return context.getJgrybm() != null && TodoBusinessType.CNP_MDHJQR.equals(context.getTodoType());
    }

    @Override
    public List<ConflictPrisonerDO> handle(TodoContext context) {
        return conflictPrisonerDao.selectList(getQueryWrapper(context.getJgrybm()));
    }

    @Override
    public TodoBusinessType getSupportedBusinessType() {
        return TodoBusinessType.CNP_MDHJQR;
    }

    // 新增方法：查询实际数量
    @Override
    public int handleCount(TodoContext context) {

        return conflictPrisonerDao.selectCount(getQueryWrapper(context.getJgrybm()));
    }

    public LambdaQueryWrapper<ConflictPrisonerDO> getQueryWrapper(String jgrybm) {
        LambdaQueryWrapper<ConflictPrisonerDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConflictPrisonerDO::getJgrybm, jgrybm);
        queryWrapper.eq(ConflictPrisonerDO::getConfirmStatus, 0);
        return queryWrapper;
    }
}
