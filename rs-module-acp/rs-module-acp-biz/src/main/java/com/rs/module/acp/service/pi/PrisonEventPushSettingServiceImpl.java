package com.rs.module.acp.service.pi;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventPushSettingListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventPushSettingPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventPushSettingRespVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventPushSettingSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.entity.pi.PrisonEventPushSettingDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.PrisonEventPushSettingDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-巡视管控-所情事件推送设置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrisonEventPushSettingServiceImpl extends BaseServiceImpl<PrisonEventPushSettingDao, PrisonEventPushSettingDO> implements PrisonEventPushSettingService {

    @Resource
    private PrisonEventPushSettingDao prisonEventPushSettingDao;

    @Override
    public String createPrisonEventPushSetting(PrisonEventPushSettingSaveReqVO createReqVO) {
        // 插入
        PrisonEventPushSettingDO prisonEventPushSetting = BeanUtils.toBean(createReqVO, PrisonEventPushSettingDO.class);
        prisonEventPushSettingDao.insert(prisonEventPushSetting);
        // 返回
        return prisonEventPushSetting.getId();
    }

    @Override
    public void updatePrisonEventPushSetting(PrisonEventPushSettingSaveReqVO updateReqVO) {
        // 校验存在
        validatePrisonEventPushSettingExists(updateReqVO.getId());
        // 更新
        PrisonEventPushSettingDO updateObj = BeanUtils.toBean(updateReqVO, PrisonEventPushSettingDO.class);
        prisonEventPushSettingDao.updateById(updateObj);
    }

    @Override
    public void deletePrisonEventPushSetting(String id) {
        // 校验存在
        validatePrisonEventPushSettingExists(id);
        // 删除
        prisonEventPushSettingDao.deleteById(id);
    }

    private void validatePrisonEventPushSettingExists(String id) {
        if (prisonEventPushSettingDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-所情事件推送设置数据不存在");
        }
    }

    @Override
    public PrisonEventPushSettingDO getPrisonEventPushSetting(String id) {
        return prisonEventPushSettingDao.selectById(id);
    }

    @Override
    public PageResult<PrisonEventPushSettingDO> getPrisonEventPushSettingPage(PrisonEventPushSettingPageReqVO pageReqVO) {
        return prisonEventPushSettingDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrisonEventPushSettingDO> getPrisonEventPushSettingList(PrisonEventPushSettingListReqVO listReqVO) {
        return prisonEventPushSettingDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean savePrisonEventPushSettingList(List<PrisonEventPushSettingSaveReqVO> pushSettingList, String typeId) {
        List<PrisonEventPushSettingDO> settingDOList = BeanUtils.toBean(pushSettingList,PrisonEventPushSettingDO.class);
        Map<String,String> idMap = new HashMap<>();
        for(PrisonEventPushSettingDO settingDO:settingDOList){
            if(ObjectUtil.isEmpty(settingDO.getId())){
                settingDO.setId(StringUtil.getGuid32());
            }
            settingDO.setTypeId(typeId);
            idMap.put(settingDO.getId(),settingDO.getId());
        }
        //删除用不到的
        LambdaQueryWrapper<PrisonEventPushSettingDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(PrisonEventPushSettingDO::getId);
        lambdaQueryWrapper.eq(PrisonEventPushSettingDO::getTypeId,typeId)
                .eq(PrisonEventPushSettingDO::getOrgCode, SessionUserUtil.getSessionUser().getOrgCode());
        List<PrisonEventPushSettingDO> dataBaseList = list();
        if(CollectionUtil.isNotEmpty(dataBaseList)){
            List<String> delIdList = new ArrayList<>();
            for(PrisonEventPushSettingDO settingDO:dataBaseList){
                if(!idMap.containsKey(settingDO.getId())){
                    delIdList.add(settingDO.getId());
                }
            }
            if(CollectionUtil.isNotEmpty(delIdList)){
                prisonEventPushSettingDao.deleteBatchIds(delIdList);
            }
        }
        return saveOrUpdateBatch(settingDOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePrisonEventPushSettingByTypeId(String typeId) {
        LambdaQueryWrapper<PrisonEventPushSettingDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PrisonEventPushSettingDO::getTypeId,typeId)
                .eq(PrisonEventPushSettingDO::getOrgCode,SessionUserUtil.getSessionUser().getOrgCode());
        prisonEventPushSettingDao.delete(lambdaQueryWrapper);
    }

    @Override
    public List<PrisonEventPushSettingRespVO> getPrisonEventPushSettingListByTypeId(String typeId) {
        LambdaQueryWrapper<PrisonEventPushSettingDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PrisonEventPushSettingDO::getTypeId,typeId)
                .eq(PrisonEventPushSettingDO::getOrgCode,SessionUserUtil.getSessionUser().getOrgCode())
                .orderByAsc(PrisonEventPushSettingDO::getOrderId);
        List<PrisonEventPushSettingDO> settingDOList = list(lambdaQueryWrapper);
        if(CollectionUtil.isEmpty(settingDOList)){
            return new ArrayList<>();
        }
        return BeanUtils.toBean(settingDOList,PrisonEventPushSettingRespVO.class);
    }
}
