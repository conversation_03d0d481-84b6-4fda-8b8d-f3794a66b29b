package com.rs.module.acp.controller.admin.gj.vo.riskassmt;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-风险评估待办 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RiskAssmtTodoRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("监区id")
    private String areaId;
    @ApiModelProperty("监区名称")
    private String areaName;
    @ApiModelProperty("监室号")
    private String roomId;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("来源业务ID")
    private String sourceBusinessId;
    @ApiModelProperty("评估时间")
    private Date assmtTime;
    @ApiModelProperty("评估类型")
    private String riskType;
    @ApiModelProperty("原风险等级")
    private String oldRiskLevel;
    @ApiModelProperty("评估风险等级")
    private String riskLevel;
    @ApiModelProperty("推送时间")
    private Date pushTime;
    @ApiModelProperty("报备状态")
    private String status;
}
