package com.rs.module.acp.entity.gj;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-协助破案 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_assistance_solve_case")
@KeySequence("acp_gj_assistance_solve_case_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_assistance_solve_case")
public class AssistanceSolveCaseDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 线索类型
     */
    private String clueType;
    /**
     * 线索获取日期
     */
    private Date clueGetTime;
    /**
     * 是否重大案件
     */
    private String clueSfzdaj;
    /**
     * 检举案件性质
     */
    private String clueJjajxz;
    /**
     * 涉案区域
     */
    private String clueSaqy;
    /**
     * 线索来源
     */
    private String clueXsly;
    /**
     * 涉案人
     */
    private String clueSar;
    /**
     * 涉案人数
     */
    private Integer clueSars;
    /**
     * 线索内容
     */
    private String clueContext;
    /**
     * 线索附件
     */
    private String clueAttachmentUrl;
    /**
     * 线索登记人
     */
    private String clueRegistUser;
    /**
     * 线索登记人身份证号
     */
    private String clueRegistUserSfzh;
    /**
     * 线索新增时间
     */
    private Date clueRegistTime;
    /**
     * 转递部门类型
     */
    private String forwardType;
    /**
     * 转递部门名称
     */
    private String forwardName;
    /**
     * 转递日期
     */
    private Date forwardTime;
    /**
     * 转递材料附件
     */
    private String forwardUrl;
    /**
     * 转递人
     */
    private String forwardUser;
    /**
     * 转递人身份证号
     */
    private String forwardUserSfzh;
    /**
     * 转递新增时间
     */
    private Date forwardUserTime;
    /**
     * 签收部门类型
     */
    private String feedbackReceiptType;
    /**
     * 签收部门名称
     */
    private String feedbackReceiptName;
    /**
     * 签收日期
     */
    private Date feedbackReceiptTime;
    /**
     * 反馈日期
     */
    private Date feedbackTime;
    /**
     * 查证情况
     */
    private String feedbackCzqk;
    /**
     * 破获刑事/行政案件数量
     */
    private Long feedbackPhajsl;
    /**
     * 抓获违法犯罪嫌疑人数量
     */
    private Long feedbackZhwffzxyrsl;
    /**
     * 发现在逃人员数量
     */
    private Long feedbackFxztrysl;
    /**
     * 缴获赃款赃物数量
     */
    private Long feedbackJhzkzwsl;
    /**
     * 反馈人
     */
    private String feedbackUser;
    /**
     * 反馈人身份证号
     */
    private String feedbackUserSfzh;
    /**
     * 反馈新增时间
     */
    private Date feedbackUserTime;
    /**
     * 状态
     */
    private String status;
    /**
     * 审批人身份证号
     */
    private String approverSfzh;
    /**
     * 审批人姓名
     */
    private String approverXm;
    /**
     * 审批时间
     */
    private Date approverTime;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 审核意见
     */
    private String approvalComments;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

}
