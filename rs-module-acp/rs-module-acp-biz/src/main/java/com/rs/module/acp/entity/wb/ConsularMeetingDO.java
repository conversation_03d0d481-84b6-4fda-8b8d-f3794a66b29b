package com.rs.module.acp.entity.wb;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-窗口业务-领事会见登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_wb_consular_meeting")
@KeySequence("acp_wb_consular_meeting_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_wb_consular_meeting")
public class ConsularMeetingDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 会见方式
     */
    private String meetingMethod;
    /**
     * 会见所在房间编号
     */
    private String roomId;
    /**
     * 介绍信编号
     */
    private String arraignmentLetterNumber;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 预约会见时间
     */
    private String appointmentTimeSlot;
    /**
     * 预约会见时间
     */
    private Date appointmentTime;
    /**
     * 会见资料上传URL
     */
    private String meetingDocumentsUrl;
    /**
     * 办理状态
     */
    private String status;
    /**
     * 分配房间时间
     */
    private Date assignmentRoomTime;
    /**
     * 分配民警身份证号
     */
    private String assignmentPoliceSfzh;
    /**
     * assignment_police
     */
    private String assignmentPolice;
    /**
     * 签到时间
     */
    private Date checkInTime;
    /**
     * 签到用户身份证号
     */
    private String checkInPoliceSfzh;
    /**
     * 签到用户
     */
    private String checkInPolice;
    /**
     * 带出民警身份证号
     */
    private String escortingPoliceSfzh;
    /**
     * 带出民警身份证号
     */
    private String escortingPolice;
    /**
     * 带出监室时间
     */
    private Date escortingTime;
    /**
     * 带出操作人身份证号
     */
    private String escortingOperatorSfzh;
    /**
     * 带出操作人
     */
    private String escortingOperator;
    /**
     * 带出操作时间
     */
    private Date escortingOperatorTime;
    /**
     * 检查结果
     */
    private String inspectionResult;
    /**
     * 违禁物品登记
     */
    private String prohibitedItems;
    /**
     * 体表检查登记
     */
    private String physicalExam;
    /**
     * 异常情况登记
     */
    private String abnormalSituations;
    /**
     * 违禁物品登记照片存储地址
     */
    private String prohibitedItemsImgUrl;
    /**
     * 体表检查登记照片存储地址
     */
    private String physicalExamImgUrl;
    /**
     * 异常情况登记照片存储地址
     */
    private String abnormalSituationsImgUrl;
    /**
     * 是否查出违禁物品
     */
    private Short isProhibitedItems;
    /**
     * 检查时间
     */
    private Date inspectionTime;
    /**
     * 检查民警身份证号
     */
    private String inspectorSfzh;
    /**
     * 检查民警身份证号
     */
    private String inspector;
    /**
     * 会见开始时间
     */
    private Date meetingStartTime;
    /**
     * 会见结束时间
     */
    private Date meetingEndTime;
    /**
     * 会毕检查人
     */
    private String returnInspectorSfzh;
    /**
     * 会毕检查人
     */
    private String returnInspector;
    /**
     * 会毕检查时间
     */
    private Date returnInspectionTime;
    /**
     * 会毕检查结果
     */
    private String returnInspectionResult;
    /**
     * 带回监室时间
     */
    private Date returnTime;
    /**
     * 带回民警身份证号
     */
    private String returnPoliceSfzh;
    /**
     * 带回民警姓名
     */
    private String returnPolice;
    /**
     * 带回操作人身份证号
     */
    private String returnOperatorSfzh;
    /**
     * 带回操作人
     */
    private String returnOperator;
    /**
     * 带回操作时间
     */
    private Date returnOperatorTime;
    /**
     * 预约会见开始时间
     */
    private Date applyMeetingStartTime;
    /**
     * 预约会见结束时间
     */
    private Date applyMeetingEndTime;
    /**
     * 带回违禁物品登记
     */
    private String returnProhibitedItems;
    /**
     * 带回体表检查登记
     */
    private String returnPhysicalExam;
    /**
     * 带回异常情况登记
     */
    private String returnAbnormalSituations;

}
