package com.rs.module.acp.service.gj.undercover;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverCancelListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverCancelPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverCancelSaveReqVO;
import com.rs.module.acp.entity.gj.UndercoverCancelDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-耳目撤销 Service 接口
 *
 * <AUTHOR>
 */
public interface UndercoverCancelService extends IBaseService<UndercoverCancelDO>{

    /**
     * 创建实战平台-管教业务-耳目撤销
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createUndercoverCancel(@Valid UndercoverCancelSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-耳目撤销
     *
     * @param updateReqVO 更新信息
     */
    void updateUndercoverCancel(@Valid UndercoverCancelSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-耳目撤销
     *
     * @param id 编号
     */
    void deleteUndercoverCancel(String id);

    /**
     * 获得实战平台-管教业务-耳目撤销
     *
     * @param id 编号
     * @return 实战平台-管教业务-耳目撤销
     */
    UndercoverCancelDO getUndercoverCancel(String id);

    /**
    * 获得实战平台-管教业务-耳目撤销分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-耳目撤销分页
    */
    PageResult<UndercoverCancelDO> getUndercoverCancelPage(UndercoverCancelPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-耳目撤销列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-耳目撤销列表
    */
    List<UndercoverCancelDO> getUndercoverCancelList(UndercoverCancelListReqVO listReqVO);

    /**
     * 审批
     * @param approveReqVO
     */
    void leaderApprove(GjApproveReqVO approveReqVO);
}
