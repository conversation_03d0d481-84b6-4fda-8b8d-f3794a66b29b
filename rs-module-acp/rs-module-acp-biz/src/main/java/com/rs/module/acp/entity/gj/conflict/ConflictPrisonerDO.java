package com.rs.module.acp.entity.gj.conflict;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import com.rs.framework.mybatis.type.BooleanToIntegerTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 实战平台-管教业务-社会矛盾化解登记-关联在押人员 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_conflict_prisoner")
@KeySequence("acp_gj_conflict_prisoner_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConflictPrisonerDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 事件ID
     */
    private String eventId;
    /**
     * 事件编号
     */
    private String eventCode;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 是否为当事人(0: 否，1:是)
     */
    @TableField(typeHandler = BooleanToIntegerTypeHandler.class)
    private Boolean partner;
    /**
     * 在押人员确认状态（0：待确认、1：已确认）
     */
    private Integer confirmStatus;
    /**
     * 确认时间
     */
    private Date confirmTime;
    /**
     * 在押人员签名地址
     */
    private String prisonerSignature;
    /**
     * 是否已评价（0：未评价，1：已评价）
     */
    private Integer isRated;
    /**
     * 满意度评分（1：满意：0：不满意）
     */
    private Integer satisfactionScore;
    /**
     * 回访状态（0：待回访，1：已回访）
     */
    private Integer followupStatus;


}
