package com.rs.module.acp.entity.gj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 实战平台-管教业务-过渡监室管理 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_transition_room")
@KeySequence("acp_gj_transition_room_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_transition_room")
public class TransitionRoomDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 过渡开始日期
     */
    private Date startDate;
    /**
     * 过渡结束日期
     */
    private Date endDate;
    /**
     * 实际开始日期
     */
    private Date actualStartDate;
    /**
     * 实际结束日期
     */
    private Date actualEndDate;
    /**
     * 过渡监室id
     */
    private String roomId;
    /**
     * 过渡监室名称
     */
    private String roomName;
    /**
     * 是否延长
     */
    private Integer isExtend;
    /**
     * 状态
     */
    private String status;

    //调整监室号ID
    private String adjustRoomId;

    //调整监室号名称
    private String adjustRoomName;
}
