package com.rs.module.acp.controller.admin.db;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.DetainReclaimDO;
import com.rs.module.acp.service.db.DetainReclaimService;

@Api(tags = "实战平台-羁押业务-收回登记")
@RestController
@RequestMapping("/acp/db/detainReclaim")
@Validated
public class DetainReclaimController {

    @Resource
    private DetainReclaimService detainReclaimService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-羁押业务-收回登记")
    public CommonResult<String> createDetainReclaim(@Valid @RequestBody DetainReclaimSaveReqVO createReqVO) {
        return success(detainReclaimService.createDetainReclaim(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-羁押业务-收回登记")
    public CommonResult<Boolean> updateDetainReclaim(@Valid @RequestBody DetainReclaimSaveReqVO updateReqVO) {
        detainReclaimService.updateDetainReclaim(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-羁押业务-收回登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteDetainReclaim(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           detainReclaimService.deleteDetainReclaim(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-羁押业务-收回登记")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<DetainReclaimRespVO> getDetainReclaim(@RequestParam("id") String id) {
        DetainReclaimDO detainReclaim = detainReclaimService.getDetainReclaim(id);
        return success(BeanUtils.toBean(detainReclaim, DetainReclaimRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-羁押业务-收回登记分页")
    public CommonResult<PageResult<DetainReclaimRespVO>> getDetainReclaimPage(@Valid @RequestBody DetainReclaimPageReqVO pageReqVO) {
        PageResult<DetainReclaimDO> pageResult = detainReclaimService.getDetainReclaimPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DetainReclaimRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-羁押业务-收回登记列表")
    public CommonResult<List<DetainReclaimRespVO>> getDetainReclaimList(@Valid @RequestBody DetainReclaimListReqVO listReqVO) {
        List<DetainReclaimDO> list = detainReclaimService.getDetainReclaimList(listReqVO);
        return success(BeanUtils.toBean(list, DetainReclaimRespVO.class));
    }

    /**
     * 通过人员编号查询收回登记信息
     */
    @GetMapping("/getByJgrybm")
    @ApiOperation(value = "通过人员编号查询收回登记信息")
    public CommonResult<DetainReclaimRespVO> getByJgrybm(@RequestParam("jgrybh") String jgrybh) {
        DetainReclaimDO detainReclaim = detainReclaimService.getByJgrybh(jgrybh);

        return success(BeanUtils.toBean(detainReclaim, DetainReclaimRespVO.class));
    }
}
