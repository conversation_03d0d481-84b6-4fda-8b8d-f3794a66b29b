package com.rs.module.acp.controller.admin.db.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-生物特征信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BiometricInfoPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("人员编号")
    private String rybh;

    @ApiModelProperty("采集项目类型")
    private String cjxmlx;

    @ApiModelProperty("生物特征,存储采集系统返回JSON结构")
    private String swtz;

    @ApiModelProperty("swtzfj")
    private String swtzfj;

    @ApiModelProperty("备注")
    private String bz;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
