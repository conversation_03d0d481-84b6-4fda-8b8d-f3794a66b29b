package com.rs.module.acp.service.pm;

import cn.hutool.core.collection.CollUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.db.vo.DetainRegKssRespVO;
import com.rs.module.acp.controller.admin.db.vo.InRecordJlsPreRespVO;
import com.rs.module.acp.controller.admin.db.vo.InRecordJlsRespVO;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomPageReqVO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.PrisonerPreInDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pm.PrisonerPreInDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 嫌疑人（待入所）信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrisonerPreInServiceImpl extends BaseServiceImpl<PrisonerPreInDao, PrisonerPreInDO> implements PrisonerPreInService {

    @Resource
    private PrisonerPreInDao prisonerPreInDao;

    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;

    @Override
    public String createPrisonerPreIn(PrisonerPreInSaveReqVO createReqVO) {
        // 插入
        PrisonerPreInDO prisonerPreIn = BeanUtils.toBean(createReqVO, PrisonerPreInDO.class);
        prisonerPreInDao.insert(prisonerPreIn);
        // 返回
        return prisonerPreIn.getId();
    }

    @Override
    public void updatePrisonerPreIn(PrisonerPreInSaveReqVO updateReqVO) {
        // 校验存在
        validatePrisonerPreInExists(updateReqVO.getId());
        // 更新
        PrisonerPreInDO updateObj = BeanUtils.toBean(updateReqVO, PrisonerPreInDO.class);
        prisonerPreInDao.updateById(updateObj);
    }

    @Override
    public void deletePrisonerPreIn(String id) {
        // 校验存在
        validatePrisonerPreInExists(id);
        // 删除
        prisonerPreInDao.deleteById(id);
    }

    private void validatePrisonerPreInExists(String id) {
        if (prisonerPreInDao.selectById(id) == null) {
            throw new ServerException("嫌疑人（待入所）信息数据不存在");
        }
    }

    @Override
    public PrisonerPreInDO getPrisonerPreIn(String id) {
        return prisonerPreInDao.selectById(id);
    }

    @Override
    public PageResult<PrisonerPreInDO> getPrisonerPreInPage(PrisonerPreInPageReqVO pageReqVO) {
        return prisonerPreInDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrisonerPreInDO> getPrisonerPreInList(PrisonerPreInListReqVO listReqVO) {
        return prisonerPreInDao.selectList(listReqVO);
    }

    @Override
    public DetainRegKssRespVO getPrisonerPreInByIdCardAndPrisonCode(PrisonerPreInListReqVO listReqVO) {
        List<PrisonerPreInDO> list = prisonerPreInDao.selectList(listReqVO);
        PrisonerPreInDO prisonerPreInDO = new PrisonerPreInDO();
        if (CollUtil.isNotEmpty(list)&& !list.isEmpty()) {
            prisonerPreInDO = list.get(0);
        }
        DetainRegKssRespVO detainRegKssRespVO = convertProperty(prisonerPreInDO);
//        DetainRegKssRespVO detainRegKssRespVO = BeanUtils.toBean(prisonerPreInDO, DetainRegKssRespVO.class);
        return detainRegKssRespVO;
    }

    @Override
    public InRecordJlsPreRespVO getPrisonerPreInByZjhmJls(PrisonerPreInListReqVO listReqVO) {
        List<PrisonerPreInDO> list = prisonerPreInDao.selectList(listReqVO);
        PrisonerPreInDO prisonerPreInDO = new PrisonerPreInDO();
        if (CollUtil.isNotEmpty(list)&& !list.isEmpty()) {
            prisonerPreInDO = list.get(0);
        }
        InRecordJlsPreRespVO inRecordJlsRespVO = convertPropertyJls(prisonerPreInDO);
        return inRecordJlsRespVO;
    }

    private InRecordJlsPreRespVO convertPropertyJls(PrisonerPreInDO prisonerPreInDO) {
        //模拟数据，后续需要删除

        AreaPrisonRoomDO areaPrisonRoomDO = getAreaPrisonRoom();
        String jsh = "";
        String roomName  = "";
        if(areaPrisonRoomDO!=null){
            jsh = areaPrisonRoomDO.getRoomCode();
            roomName = areaPrisonRoomDO.getRoomName();
        }

        return InRecordJlsPreRespVO.builder()
                .id(prisonerPreInDO.getId())
                .xm(prisonerPreInDO.getXm())
                .xmpy(prisonerPreInDO.getXmpy())
                .bm(prisonerPreInDO.getBm())
                .xb(prisonerPreInDO.getXb())
                .csrq(prisonerPreInDO.getCsrq())
                .zjlx(prisonerPreInDO.getZjlx())
                .zjhm(prisonerPreInDO.getZjh())
                .gj(prisonerPreInDO.getGj())
                .mz(prisonerPreInDO.getMz())
                .hyzk(prisonerPreInDO.getHyzk())
                .jg(prisonerPreInDO.getJg())
                .zjxy("40")
                .hjd(prisonerPreInDO.getHjd())
                .hjdxz(prisonerPreInDO.getHjdxz())
                .xzz(prisonerPreInDO.getXzd())
                .xzzxz(prisonerPreInDO.getXzdxz())
                .whcd(prisonerPreInDO.getWhcd())
                .zzmm(prisonerPreInDO.getZzmm())
                .zy(prisonerPreInDO.getZy())
                .gzdw(prisonerPreInDO.getGzdw())
                .sf(prisonerPreInDO.getSf())
                .tssf(prisonerPreInDO.getTssf())
                .xxhc("01")
                .sfwxzxs("1")
                .xxmc("1")
                .gllb(prisonerPreInDO.getGllb())
                .ajlbdm(prisonerPreInDO.getAy())
                .ajlb(prisonerPreInDO.getAy())
                .ajbh(prisonerPreInDO.getTabh())
                .rybh(prisonerPreInDO.getWbrybh())
                .bazxbh("A123213123")
                .sjpz(prisonerPreInDO.getSypz())
                .sjpzwsh(prisonerPreInDO.getSypzwsh())
                .sjpzwsdz("")
                .rsrq(prisonerPreInDO.getRsrq())
                .rsyy("12")
                .sjjglx("01")
                .jljdjgmc(prisonerPreInDO.getSydw())
                .sjrxm(prisonerPreInDO.getSyr())
                .sjrlxdh(prisonerPreInDO.getLxdh())
                .sjrxm2(prisonerPreInDO.getSyr())
                .sjrlxdh2(prisonerPreInDO.getLxdh())
                .badwlx("1")
                .badw(prisonerPreInDO.getBadw())
                .sjmjxm("周警官")
                .sjmjxmlxdh(prisonerPreInDO.getBardh())
                .jlqsrq(prisonerPreInDO.getJlrq())
                .jljzrq(prisonerPreInDO.getGyqx())
                .sjpzwsh("FL0000202506300001")
                .jyaq(prisonerPreInDO.getJyaq())
                .dabh("DA00002506300001")
                .jsh(jsh)
                .roomName(roomName)
                .sjmj("黄警官")
                .sfsm((short) 1)
                .rydh("DH0001")
                .smyy("01")
                .jjrq(new Date())
                .jjyy("01")
                .sdnjdw("毒品检测机构")
                .sdnjccsj(new Date())
                .sdnjccjg("异常")
                .sdnjjcr("张医生")
                .asjxgrybh("A0010210,B1221389,C123712937")
                .sjjgmc("1")
                .hzflwsh("FW12312313")
                .sjrq(new Date())
                .jlqx(10)
                .build();
    }

    private DetainRegKssRespVO convertProperty(PrisonerPreInDO prisonerPreInDO) {
        //模拟数据，后续需要删除

        AreaPrisonRoomDO areaPrisonRoomDO = getAreaPrisonRoom();
        String jsh = "";
        String roomName  = "";
        if(areaPrisonRoomDO!=null){
            jsh = areaPrisonRoomDO.getRoomCode();
            roomName = areaPrisonRoomDO.getRoomName();
        }

        return DetainRegKssRespVO.builder()
                .id(prisonerPreInDO.getId())
                .xm(prisonerPreInDO.getXm())
                .xmpy(prisonerPreInDO.getXmpy())
                .bm(prisonerPreInDO.getBm())
                .xb(prisonerPreInDO.getXb())
                .csrq(prisonerPreInDO.getCsrq())
                .zjlx(prisonerPreInDO.getZjlx())
                .zjhm(prisonerPreInDO.getZjh())
                .gj(prisonerPreInDO.getGj())
                .mz(prisonerPreInDO.getMz())
                .hyzk(prisonerPreInDO.getHyzk())
                .jg(prisonerPreInDO.getJg())
                .zjxy("40")
                .hjd(prisonerPreInDO.getHjd())
                .hjdxz(prisonerPreInDO.getHjdxz())
                .xzz(prisonerPreInDO.getXzd())
                .xzzxz(prisonerPreInDO.getXzdxz())
                .whcd(prisonerPreInDO.getWhcd())
                .zzmm(prisonerPreInDO.getZzmm())
                .zy(prisonerPreInDO.getZy())
                .gzdw(prisonerPreInDO.getGzdw())
                .zw(prisonerPreInDO.getZw())
                .zwjb("")
                .tc("31")
                .sf(prisonerPreInDO.getSf())
                .tssf(prisonerPreInDO.getTssf())
                .xxhc("01")
                .sfwxzxs("1")
                .xxmc("1")
                .gllb(prisonerPreInDO.getGllb())
                .ajlbdm(prisonerPreInDO.getAy())
                .ajlb(prisonerPreInDO.getAy())
                .ajbh(prisonerPreInDO.getTabh())
                .rybh(prisonerPreInDO.getWbrybh())
                .bazxbh("A123213123")
                .tabh(prisonerPreInDO.getTabh())
                .sypz(prisonerPreInDO.getSypz())
                .sypzwsh(prisonerPreInDO.getSypzwsh())
                .sypzwsdz("")
                .rssj(prisonerPreInDO.getRsrq())
                .rsyy("12")
                .sshj(prisonerPreInDO.getBahj())
                .syjglx("01")
                .sydw(prisonerPreInDO.getSydw())
                .syr1(prisonerPreInDO.getSyr())
                .syrgh1(prisonerPreInDO.getLxdh())
                .syrsj1(prisonerPreInDO.getLxdh())
                .badwlx("1")
                .badw(prisonerPreInDO.getBadw())
                .bar("周警官")
                .barlxff(prisonerPreInDO.getBardh())
                .barxb("1")
                .barzjlx("11")
                .barzjhm("110101199003072516")
                .bahj(prisonerPreInDO.getBahj())
                .jyrq(prisonerPreInDO.getJlrq())
                .jlrq(prisonerPreInDO.getJlrq())
                .dbrq(prisonerPreInDO.getDbrq())
                .gyqx(prisonerPreInDO.getGyqx())
                .flwsh("FL0000202506300001")
                .jyaq(prisonerPreInDO.getJyaq())
                .sxzm(prisonerPreInDO.getAy())
                .zxf("1")
                .dabh("DA00002506300001")
                .jsh(jsh)
                .roomName(roomName)
                .qzcslx("01")
                .zwbh("ZW00010002")
                .fwbh("FW00010002")
                .symjxm("黄警官")
                .ksslxdh("01087878767")
                .sfsm("1")
                .rydh("DH0001")
                .smyy("01")
                .jjrq(new Date())
                .jjyy("01")
                .sdnjdw("毒品检测机构")
                .sdnjccsj(new Date())
                .sdnjccjg("异常")
                .sdnjjcr("张医生")
                .syjgmc(prisonerPreInDO.getSydw())
                .xzhj((short) 1)
                .xzhjaj((short) 1)
                .build();


    }

    /**
     * 演示数据，监室选择
     */
    private AreaPrisonRoomDO getAreaPrisonRoom() {
        try {
            SessionUser sessionUser = SessionUserUtil.getSessionUser();
            String orgCode = sessionUser.getOrgCode();
            AreaPrisonRoomPageReqVO pageReqVO = new AreaPrisonRoomPageReqVO();
            pageReqVO.setPageNo(1);
            pageReqVO.setPageSize(100);
            pageReqVO.setOrgCode(orgCode);
            PageResult<AreaPrisonRoomDO> pageResult = areaPrisonRoomService.getAreaPrisonRoomPage(pageReqVO);
            List<AreaPrisonRoomDO> list = new ArrayList<>();
            pageResult.getList().forEach(item -> {
                list.add(item);
            });
            AreaPrisonRoomDO areaPrisonRoomDO = null;
            if (list != null && list.size() > 0) {
                areaPrisonRoomDO = list.get(0);
            }
            return areaPrisonRoomDO;
        } catch (Exception e) {
            log.error("获取监室信息异常", e);
            return null;
        }
    }


}
