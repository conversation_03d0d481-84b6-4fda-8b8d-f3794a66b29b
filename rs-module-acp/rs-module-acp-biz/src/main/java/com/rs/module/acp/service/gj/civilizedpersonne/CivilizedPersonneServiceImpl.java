package com.rs.module.acp.service.gj.civilizedpersonne;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne.*;
import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.CivilizedRoomApprovalReqVO;
import com.rs.module.acp.dao.gj.CivilizedPersonneDao;
import com.rs.module.acp.entity.gj.CivilizedPersonneDO;
import com.rs.module.acp.entity.gj.CivilizedPersonneDetailDO;
import com.rs.module.acp.enums.gj.CivilizedPersonneStatusEnum;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.util.ThreadPoolUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;


/**
 * 实战平台-管教业务-文明个人登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CivilizedPersonneServiceImpl extends BaseServiceImpl<CivilizedPersonneDao, CivilizedPersonneDO> implements CivilizedPersonneService {

    @Resource
    private CivilizedPersonneDao civilizedPersonneDao;

    @Resource
    private CivilizedPersonneDetailService civilizedPersonneDetailService;

    @Resource
    private PrisonerService prisonerService;

    private static final String defKey = "yonghudanweilindao";

    @Override
    public String createCivilizedPersonne(CivilizedPersonneSaveReqVO createReqVO) {
        // 插入
        CivilizedPersonneDO civilizedPersonne = BeanUtils.toBean(createReqVO, CivilizedPersonneDO.class);
        civilizedPersonneDao.insert(civilizedPersonne);
        // 返回
        return civilizedPersonne.getId();
    }

    @Override
    public void updateCivilizedPersonne(CivilizedPersonneSaveReqVO updateReqVO) {
        // 校验存在
        validateCivilizedPersonneExists(updateReqVO.getId());
        // 更新
        CivilizedPersonneDO updateObj = BeanUtils.toBean(updateReqVO, CivilizedPersonneDO.class);
        civilizedPersonneDao.updateById(updateObj);
    }

    @Override
    public void deleteCivilizedPersonne(String id) {
        // 校验存在
        validateCivilizedPersonneExists(id);
        // 删除
        civilizedPersonneDao.deleteById(id);
    }

    private void validateCivilizedPersonneExists(String id) {
        if (civilizedPersonneDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-文明个人登记数据不存在");
        }
    }

    @Override
    public CivilizedPersonneRespVO getCivilizedPersonne(String id) {
        CivilizedPersonneDO civilizedPersonneDO = civilizedPersonneDao.selectById(id);
        CivilizedPersonneRespVO civilizedPersonneRespVO = BeanUtils.toBean(civilizedPersonneDO, CivilizedPersonneRespVO.class);
        if (Objects.nonNull(civilizedPersonneRespVO)) {
            LambdaQueryWrapper<CivilizedPersonneDetailDO> wrapper = Wrappers.lambdaQuery(CivilizedPersonneDetailDO.class)
                    .eq(CivilizedPersonneDetailDO::getCivilizedPersonneId, id);
            wrapper.orderByAsc(CivilizedPersonneDetailDO::getAddTime);
            civilizedPersonneRespVO.setList(BeanUtils.toBean(civilizedPersonneDetailService.list(wrapper), CivilizedPersonneDetailRespVO.class));
        }
        return civilizedPersonneRespVO;
    }

    @Override
    public PageResult<CivilizedPersonneDO> getCivilizedPersonnePage(CivilizedPersonnePageReqVO pageReqVO) {
        return civilizedPersonneDao.selectPage(pageReqVO);
    }

    @Override
    public List<CivilizedPersonneRespVO> getCivilizedPersonneList(CivilizedPersonneListReqVO listReqVO) {
        List<CivilizedPersonneDO> civilizedPersonneDOS = civilizedPersonneDao.selectList(listReqVO);
        List<CivilizedPersonneRespVO> civilizedPersonneRespVOS = BeanUtils.toBean(civilizedPersonneDOS, CivilizedPersonneRespVO.class);
        if (CollectionUtil.isNotEmpty(civilizedPersonneRespVOS)) {
            List<String> ids = civilizedPersonneDOS.stream().map(CivilizedPersonneDO::getId).collect(Collectors.toList());
            LambdaQueryWrapper<CivilizedPersonneDetailDO> wrapper = Wrappers.lambdaQuery(CivilizedPersonneDetailDO.class)
                    .in(CivilizedPersonneDetailDO::getCivilizedPersonneId, ids);
            wrapper.orderByAsc(CivilizedPersonneDetailDO::getAddTime);
            Map<String, List<CivilizedPersonneDetailRespVO>> map = new HashMap<>();
            List<CivilizedPersonneDetailRespVO> list = BeanUtils.toBean(civilizedPersonneDetailService.list(wrapper), CivilizedPersonneDetailRespVO.class);
            if (CollectionUtil.isNotEmpty(list)) {
                Set<String> set = new HashSet<>();
                for (CivilizedPersonneDetailRespVO respVO : list) {
                    set.add(respVO.getJgrybm());
                    List<CivilizedPersonneDetailRespVO> tempList = map.get(respVO.getCivilizedPersonneId());
                    if (CollectionUtil.isEmpty(tempList)) {
                        tempList = new ArrayList<>();
                        map.put(respVO.getCivilizedPersonneId(), tempList);
                    }
                    tempList.add(respVO);
                }
                List<PrisonerInDO> prisonerInOneList = prisonerService.getPrisonerInOneList(set);
                List<PrisonerVwRespVO> userList = BeanUtils.toBean(prisonerInOneList, PrisonerVwRespVO.class);
                Map<String, PrisonerVwRespVO> userMap = new HashMap<>();
                if(CollectionUtil.isNotEmpty(userList)){
                    for (PrisonerVwRespVO prisonerVwRespVO : userList) {
                        userMap.put(prisonerVwRespVO.getJgrybm(), prisonerVwRespVO);
                    }
                }
                for (CivilizedPersonneDetailRespVO civilizedPersonneDetailRespVO : list) {
                    civilizedPersonneDetailRespVO.setPrisonerVwRespVO(userMap.get(civilizedPersonneDetailRespVO.getJgrybm()));
                }
            }
            for (CivilizedPersonneRespVO civilizedPersonneRespVO : civilizedPersonneRespVOS) {
                civilizedPersonneRespVO.setList(map.get(civilizedPersonneRespVO.getId()));
            }
        }
        return civilizedPersonneRespVOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void registEvaluation(CivilizedPersonneRegistReqVO registReqVO) {
        CivilizedPersonneDO civilizedPersonneDO = civilizedPersonneDao.selectById(registReqVO.getId());
        if (civilizedPersonneDO == null) {
            throw new ServerException("实战平台-管教业务-文明个人登记数据不存在");
        }

        if (CollectionUtil.isEmpty(registReqVO.getList())) {
            throw new ServerException("登记信息不能为空");
        }
        if (!CivilizedPersonneStatusEnum.DPB.getCode().equals(civilizedPersonneDO.getStatus())) {
            throw new ServerException("非待评比状态，不允许进行信息登记");
        }
        StringBuilder sb = new StringBuilder();
        for (CivilizedPersonneDetailDO detailDO : registReqVO.getList()) {
            detailDO.setCivilizedPersonneId(civilizedPersonneDO.getId());
            sb.append(detailDO.getJgryxm()).append("、");
        }
        String civilizedPersonne = sb.toString();
        if (civilizedPersonne.endsWith("、")) {
            civilizedPersonne = civilizedPersonne.substring(0, civilizedPersonne.length() - 1);
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        civilizedPersonneDO.setApplyTime(new Date());
        civilizedPersonneDO.setApplyUser(sessionUser.getName());
        civilizedPersonneDO.setApplyUserSfzh(sessionUser.getIdCard());
        civilizedPersonneDO.setCivilizedPersonne(civilizedPersonne);

        // 提交申请
        //启动流程审批
        String msgUrl = "/#/discipline/civilizedIndividuals";
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", registReqVO.getId());
        variables.put("civilizedPersonne", civilizedPersonne);
        JSONObject result = BspApprovalUtil.defaultStartProcess(HttpUtils.getAppCode() + "-" + defKey, registReqVO.getId(),
                "文明个人评比流程审批", msgUrl, variables);
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            civilizedPersonneDO.setActInstId(bpmTrail.getString("actInstId"));
            civilizedPersonneDO.setTaskId(bpmTrail.getString("taskId"));
            civilizedPersonneDO.setStatus(CivilizedPersonneStatusEnum.DSP.getCode());
            civilizedPersonneDetailService.saveBatch(registReqVO.getList());
            civilizedPersonneDao.updateById(civilizedPersonneDO);
        } else {
            throw new ServerException("流程启动失败");
        }

    }

    @Override
    public void approval(CivilizedRoomApprovalReqVO reqVO) {
        CivilizedPersonneDO civilizedPersonneDO = civilizedPersonneDao.selectById(reqVO.getId());
        if (civilizedPersonneDO == null) {
            throw new ServerException("实战平台-管教业务-文明个人登记数据不存在");
        }
        if (!CivilizedPersonneStatusEnum.DSP.getCode().equals(civilizedPersonneDO.getStatus())) {
            throw new ServerException("当前非待审批状态不能进行审批");
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(civilizedPersonneDO.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }
        BspApproceStatusEnum bspApproceStatusEnum;
        if (StringUtils.equals(CivilizedPersonneStatusEnum.PBWC.getCode(), reqVO.getApprovelResult())) {
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED_END;
        } else if (StringUtils.equals(CivilizedPersonneStatusEnum.DCXPB.getCode(), reqVO.getApprovelResult())) {
            bspApproceStatusEnum = BspApproceStatusEnum.REJECT;
        } else {
            throw new ServerException("非法审批状态：" + reqVO.getApprovelResult());
        }
        civilizedPersonneDO.setStatus(reqVO.getApprovelResult());

        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", civilizedPersonneDO.getId());
        variables.put("civilizedPersonne", civilizedPersonneDO.getCivilizedPersonne());
        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getId());
        nowApproveUser.put("name", sessionUser.getName());
        boolean terminateTask = BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode();
        JSONObject result = BspApprovalUtil.approvalProcess(HttpUtils.getAppCode() + "-" + defKey,
                civilizedPersonneDO.getActInstId(), civilizedPersonneDO.getTaskId(), civilizedPersonneDO.getId(),
                bspApproceStatusEnum, reqVO.getApprovelComment(), null, null, terminateTask,
                variables, nowApproveUser, HttpUtils.getAppCode());
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            civilizedPersonneDO.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程启动失败");
        }
        civilizedPersonneDao.updateById(civilizedPersonneDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void againEvaluation(CivilizedPersonneRegistReqVO registReqVO) {
        CivilizedPersonneDO civilizedPersonneDO = civilizedPersonneDao.selectById(registReqVO.getId());
        if (civilizedPersonneDO == null) {
            throw new ServerException("实战平台-管教业务-文明个人登记数据不存在");
        }
        if (!CivilizedPersonneStatusEnum.DCXPB.getCode().equals(civilizedPersonneDO.getStatus())) {
            throw new ServerException("当前非待重新评比状态，不能进行重新评比操作");
        }
        StringBuilder sb = new StringBuilder();
        for (CivilizedPersonneDetailDO detailDO : registReqVO.getList()) {
            detailDO.setCivilizedPersonneId(civilizedPersonneDO.getId());
            sb.append(detailDO.getJgryxm()).append("、");
        }
        String civilizedPersonne = sb.toString();
        if (civilizedPersonne.endsWith("、")) {
            civilizedPersonne = civilizedPersonne.substring(0, civilizedPersonne.length() - 1);
        }
        civilizedPersonneDO.setCivilizedPersonne(civilizedPersonne);
        civilizedPersonneDetailService.remove(Wrappers.lambdaQuery(CivilizedPersonneDetailDO.class)
                .eq(CivilizedPersonneDetailDO::getCivilizedPersonneId, civilizedPersonneDO.getId()));

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", civilizedPersonneDO.getId());
        variables.put("civilizedPersonne", civilizedPersonneDO.getCivilizedPersonne());
        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getId());
        nowApproveUser.put("name", sessionUser.getName());
        JSONObject result = BspApprovalUtil.approvalProcess(HttpUtils.getAppCode() + "-" + defKey,
                civilizedPersonneDO.getActInstId(), civilizedPersonneDO.getTaskId(), civilizedPersonneDO.getId(),
                BspApproceStatusEnum.PASSED, BspApproceStatusEnum.PASSED.getName(), null, null, false,
                variables, nowApproveUser, HttpUtils.getAppCode());
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            civilizedPersonneDO.setTaskId(bpmTrail.getString("taskId"));
            civilizedPersonneDO.setStatus(CivilizedPersonneStatusEnum.DSP.getCode());
        } else {
            throw new ServerException("流程启动失败");
        }
        civilizedPersonneDetailService.saveBatch(registReqVO.getList());
        civilizedPersonneDao.updateById(civilizedPersonneDO);
    }


}
