package com.rs.module.acp.controller.admin.db;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.OutHospitalDO;
import com.rs.module.acp.service.db.OutHospitalService;

@Api(tags = "实战平台-收押业务-出所就医")
@RestController
@RequestMapping("/acp/db/outHospital")
@Validated
public class OutHospitalController {

    @Resource
    private OutHospitalService outHospitalService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-收押业务-出所就医")
    public CommonResult<String> createOutHospital(@Valid @RequestBody OutHospitalSaveReqVO createReqVO) {
        return success(outHospitalService.createOutHospital(createReqVO));
    }


    /**
     * 出所就医登记-医生登记
     * @param updateReqVO
     * @return
     */
    @PostMapping("/doctorRegister")
    @ApiOperation(value = "出所就医登记-医生登记")
    public CommonResult<String> doctorRegister(@Valid @RequestBody OutHospitalSaveReqVO updateReqVO) {
        return success(outHospitalService.doctorRegister(updateReqVO));
    }


    /**
     * 出所就医登记-医务主任意见登记
     * @param updateReqVO
     * @return
     */
    @PostMapping("/jjsyRegister")
    @ApiOperation(value = "出所就医登记-医务主任意见登记")
    public CommonResult<String> jjsyRegister(@Valid @RequestBody OutHospitalSaveReqVO updateReqVO) {
        return success(outHospitalService.jjsyRegister(updateReqVO));
    }

    /**
     * 出所就医-勤务安排
     * @param updateReqVO
     * @return
     */
    @PostMapping("/qwap")
    @ApiOperation(value = "出所就医-勤务安排")
    public CommonResult<String> qwap(@Valid @RequestBody OutHospitalSaveReqVO updateReqVO) {
        return success(outHospitalService.qwap(updateReqVO));
    }

    /**
     * 出所就医--领导审批
     * @param updateReqVO
     * @return
     */
    @PostMapping("/leaderRegister")
    @ApiOperation(value = "出所就医--领导审批")
    public CommonResult<String> leaderRegister(@Valid @RequestBody OutHospitalSaveReqVO updateReqVO) {
        return success(outHospitalService.leaderRegister(updateReqVO));
    }

    /**
     * 出所就医-离所确认
     */
    @PostMapping("/confirmByLeave")
    @ApiOperation(value = "出所就医-离所确认")
    public CommonResult<String> confirmByLeave(@Valid @RequestBody OutHospitalSaveReqVO updateReqVO) {
        return success(outHospitalService.confirmByLeave(updateReqVO));
    }


    /**
     * 出所就医-回所勤务安排
     */
    @PostMapping("/qwapBack")
    @ApiOperation(value = "出所就医-回所勤务安排")
    public CommonResult<String> qwapBack(@Valid @RequestBody OutHospitalSaveReqVO updateReqVO) {
        return success(outHospitalService.qwapBack(updateReqVO));
    }

    /**
     * 出所就医-回所带领导审批
     */
    @PostMapping("/leaderRegisterBack")
    @ApiOperation(value = "出所就医-回所带领导审批")
    public CommonResult<String> leaderRegisterBack(@Valid @RequestBody OutHospitalSaveReqVO updateReqVO) {
        return success(outHospitalService.leaderRegisterBack(updateReqVO));
    }

    /**
     * 出所就医-回所确认
     * @param updateReqVO
     * @return
     */
    @PostMapping("/confirmByBack")
    @ApiOperation(value = "出所就医-回所确认")
    public CommonResult<String> confirmByBack(@Valid @RequestBody OutHospitalSaveReqVO updateReqVO) {
        return success(outHospitalService.confirmByBack(updateReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-收押业务-出所就医")
    public CommonResult<Boolean> updateOutHospital(@Valid @RequestBody OutHospitalSaveReqVO updateReqVO) {
        outHospitalService.updateOutHospital(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-收押业务-出所就医")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteOutHospital(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           outHospitalService.deleteOutHospital(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-收押业务-出所就医")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<OutHospitalRespVO> getOutHospital(@RequestParam("id") String id) {
        OutHospitalDO outHospital = outHospitalService.getOutHospital(id);
        return success(BeanUtils.toBean(outHospital, OutHospitalRespVO.class));
    }

    /**
     * 出所就医-根据jgrybm获取详细信息
     * @param jgrybm
     * @param current_step
     * @return
     */
    @GetMapping("/getByJgrybm")
    @ApiOperation(value = "出所就医-根据jgrybm获取详细信息")
    public CommonResult<OutHospitalRespVO> getByJgrybm(@RequestParam("jgrybm") String jgrybm,@RequestParam(value = "current_step",required = false)String current_step) {
        //
        if(jgrybm == null|| "".equalsIgnoreCase(jgrybm)){
            return CommonResult.error("jgrybm参数不能为空！");
        }
        OutHospitalDO db = outHospitalService.getPrisonerInfo(jgrybm, current_step);
        return success(BeanUtils.toBean(db, OutHospitalRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-收押业务-出所就医分页")
    public CommonResult<PageResult<OutHospitalRespVO>> getOutHospitalPage(@Valid @RequestBody OutHospitalPageReqVO pageReqVO) {
        PageResult<OutHospitalDO> pageResult = outHospitalService.getOutHospitalPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, OutHospitalRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-收押业务-出所就医列表")
    public CommonResult<List<OutHospitalRespVO>> getOutHospitalList(@Valid @RequestBody OutHospitalListReqVO listReqVO) {
        List<OutHospitalDO> list = outHospitalService.getOutHospitalList(listReqVO);
        return success(BeanUtils.toBean(list, OutHospitalRespVO.class));
    }

    /**
     * 更新流程信息
     *
     * @param updateReqVO
     * @return
     */
    @PostMapping("/updateWorkflowInfo")
    @ApiOperation(value = "更新流程信息-看守所收押登记")
    public CommonResult<Boolean> updateWorkflowInfo(@Valid @RequestBody OutHospitalSaveReqVO updateReqVO) {
        //更新操作，需要传ID，判断是否有传，如果未传则提示id不能为空
        if(updateReqVO.getJgrybm() == null|| updateReqVO.getJgrybm().isEmpty()){
            return CommonResult.error("rybh参数不能为空！");
        }
        OutHospitalDO db = outHospitalService.getPrisonerInfo(updateReqVO.getJgrybm(),updateReqVO.getCurrentStep());
        if(db != null){
            updateReqVO.setId(db.getId());
            outHospitalService.updateOutHospital(updateReqVO);
        }else {
            System.out.println("未找到RYBH为：" + updateReqVO.getJgrybm() + "的记录！");
        }

        return success(true);
    }

}
