package com.rs.module.acp.controller.admin.db;

import com.rs.module.acp.cons.DetainRegStatusEnum;
import com.rs.module.acp.cons.InProcessStageEnum;
import com.rs.module.acp.cons.InTypeEnums;
import com.rs.module.acp.cons.LeaderApprovalStatusEnum;
import com.rs.module.acp.entity.db.dbSocialRelationsDO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.InRecordJlsDO;
import com.rs.module.acp.service.db.InRecordJlsService;

@Api(tags = "实战平台-收押业务-入所登记（拘留所）")
@RestController
@RequestMapping("/acp/db/inRecordJls")
@Validated
public class InRecordJlsController {

    @Resource
    private InRecordJlsService inRecordJlsService;

    @Resource
    private com.rs.module.acp.service.db.dbSocialRelationsService dbSocialRelationsService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-收押业务-入所登记（拘留所）")
    public CommonResult<String> createInRecordJls(@Valid @RequestBody InRecordJlsSaveReqVO createReqVO) {
        return success(inRecordJlsService.createInRecordJls(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-收押业务-入所登记（拘留所）")
    public CommonResult<String> updateInRecordJls(@Valid @RequestBody InRecordJlsSaveReqVO updateReqVO) {
        //更新操作，需要传ID，判断是否有传，如果未传则提示id不能为空
        if(updateReqVO.getId() == null||  "".equals(updateReqVO.getId())){
            return CommonResult.error("更新操作，需要传ID字段！");
        }
        //判断是否插入过，没有则先插入
//        detainRegKssService.ifExists(updateReqVO);
        if(updateReqVO.getStatus()!=null&&updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.DRAFT.getCode())){
            updateReqVO.setCurrentStep(InProcessStageEnum.PENDING_REGISTRATION.getCode());
        }else if(updateReqVO.getStatus()!=null&&updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())
                &&updateReqVO.getRslx().equalsIgnoreCase(InTypeEnums.EMERGENCY.getCode())&&(updateReqVO.getActInstId()==null|| updateReqVO.getActInstId().isEmpty())){
            updateReqVO.setSpzt(LeaderApprovalStatusEnum.PENDING.getCode());
            updateReqVO.setCurrentStep(InProcessStageEnum.PENDING_LEADER_APPROVAL.getCode());
        }else if(updateReqVO.getStatus()!=null&&updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())
                &&updateReqVO.getRslx().equalsIgnoreCase(InTypeEnums.EMERGENCY.getCode())&&updateReqVO.getActInstId()!=null&&!updateReqVO.getActInstId().isEmpty()){
            updateReqVO.setCurrentStep(InProcessStageEnum.PENDING_HEALTH_CHECK.getCode());
        }else if(updateReqVO.getStatus()!=null&&updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())){
            updateReqVO.setCurrentStep(InProcessStageEnum.PENDING_HEALTH_CHECK.getCode());
        }
        inRecordJlsService.updateInRecordJls(updateReqVO);
        //循环更新社会关系
        dbSocialRelationsListReqVO listReqVO = new dbSocialRelationsListReqVO();
        listReqVO.setRybh(updateReqVO.getRybh());
        List<dbSocialRelationsDO> list = dbSocialRelationsService.getdbSocialRelationsList(listReqVO);
        //循环根据id删除
        for(dbSocialRelationsDO dbSocialRelationsDO : list){
            dbSocialRelationsService.deletedbSocialRelations(dbSocialRelationsDO.getId());
        }
        List<dbSocialRelationsSaveReqVO> socialRelations = updateReqVO.getSocialRelations();
        for (dbSocialRelationsSaveReqVO socialRelation : socialRelations) {
            socialRelation.setRybh(updateReqVO.getRybh());
            if(socialRelation.getIdType()==null|| socialRelation.getIdType().equals("")){
                socialRelation.setIdType("11");
            }
            dbSocialRelationsService.createdbSocialRelations(socialRelation);
        }

        return success(updateReqVO.getRybh());
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-收押业务-入所登记（拘留所）")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteInRecordJls(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            InRecordJlsDO detainRegKss = inRecordJlsService.getInRecordJls(id);
            // 创建并配置社会关系查询对象
            dbSocialRelationsListReqVO listReqVO = new dbSocialRelationsListReqVO();
            listReqVO.setRybh(detainRegKss.getRybh());

            // 获取与当前收押登记相关联的社会关系列表
            List<dbSocialRelationsDO> list = dbSocialRelationsService.getdbSocialRelationsList(listReqVO);
            //循环删除该list中的数据
            for (dbSocialRelationsDO socialRelations : list) {
                dbSocialRelationsService.deletedbSocialRelations(socialRelations.getId());
            }

            inRecordJlsService.deleteInRecordJls(id);
        }

        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-收押业务-入所登记（拘留所）")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<InRecordJlsRespVO> getInRecordJls(@RequestParam("id") String id) {
        // 根据ID获取看守所收押登记信息
        InRecordJlsDO detainRegKss = inRecordJlsService.getInRecordJls(id);

        // 创建并配置社会关系查询对象
        dbSocialRelationsListReqVO listReqVO = new dbSocialRelationsListReqVO();
        listReqVO.setRybh(detainRegKss.getRybh());

        // 获取与当前收押登记相关联的社会关系列表
        List<dbSocialRelationsDO> list = dbSocialRelationsService.getdbSocialRelationsList(listReqVO);

        // 将社会关系列表转换为响应所需的VO格式
        List<dbSocialRelationsRespVO> listVO = BeanUtils.toBean(list, dbSocialRelationsRespVO.class);

        // 将看守所收押登记信息转换为响应所需的VO格式
        InRecordJlsRespVO detainRegKssRespVO = BeanUtils.toBean(detainRegKss, InRecordJlsRespVO.class);

        // 将转换后的社会关系列表设置到收押登记响应对象中
        detainRegKssRespVO.setSocialRelations(listVO);

        return success(detainRegKssRespVO);
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-收押业务-入所登记（拘留所）分页")
    public CommonResult<PageResult<InRecordJlsRespVO>> getInRecordJlsPage(@Valid @RequestBody InRecordJlsPageReqVO pageReqVO) {
        PageResult<InRecordJlsDO> pageResult = inRecordJlsService.getInRecordJlsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InRecordJlsRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-收押业务-入所登记（拘留所）列表")
    public CommonResult<List<InRecordJlsRespVO>> getInRecordJlsList(@Valid @RequestBody InRecordJlsListReqVO listReqVO) {
        List<InRecordJlsDO> list = inRecordJlsService.getInRecordJlsList(listReqVO);
        return success(BeanUtils.toBean(list, InRecordJlsRespVO.class));
    }

    /**
     * 获取主表以及健康登记医生意见信息，根据ID参数
     */
    @GetMapping("/getCombineInfo")
    @ApiOperation(value = "获得基本信息以及医生意见信息")
    @ApiImplicitParam(name = "rybh", value = "RYBH")
    public CommonResult<CombineRespVO> getCombineInfo(@RequestParam("rybh") String rybh) {
        CombineRespVO db = inRecordJlsService.getCombineInfo(rybh);

        return success(db);
    }

    /**
     * 更新所领导审批状态
     */
    @PostMapping("/updateLeaderApprovalStatus")
    @ApiOperation(value = "更新所领导审批状态")
    public CommonResult<Boolean> updateLeaderApprovalStatus(@Valid @RequestBody LeaderApprovalStatusReqVO updateReqVO) {
        inRecordJlsService.updateLeaderApprovalStatus(updateReqVO);
        return success(true);
    }

    @PostMapping("/updateLeaderApprovalStatusByRybh")
    @ApiOperation(value = "更新所领导审批状态")
    public CommonResult<Boolean> updateLeaderApprovalStatusByRybh(@Valid @RequestBody LeaderApprovalStatusReqVO updateReqVO) {
        inRecordJlsService.updateLeaderApprovalStatus(updateReqVO);
        return success(true);
    }

    /**
     * 根据人员编号获取人员信息
     */
    @GetMapping("/getPrisonerInfo")
    @ApiOperation(value = "根据人员编号获取人员信息")
    @ApiImplicitParam(name = "rybh", value = "RYBH")
    public CommonResult<InRecordJlsRespVO> getPrisonerInfo(@RequestParam("rybh") String rybh) {
        InRecordJlsDO db = inRecordJlsService.getPrisonerInfo(rybh);

        return success(BeanUtils.toBean(db, InRecordJlsRespVO.class));
    }

    /**
     * 获取各个流程状态信息
     */
    @GetMapping("/getInRecordStatus")
    @ApiOperation(value = "获取各个流程状态信息")
    public CommonResult<InRecordStatusVO> getInRecordStatus(@RequestParam("rybh") String rybh) {
        InRecordStatusVO db = inRecordJlsService.getInRecordStatus(rybh);
        return success(db);
    }

    /**
     * 更新流程信息
     *
     * @param updateReqVO
     * @return
     */
    @PostMapping("/updateWorkflowInfo")
    @ApiOperation(value = "更新流程信息-看守所收押登记")
    public CommonResult<Boolean> updateWorkflowInfo(@Valid @RequestBody InRecordJlsSaveReqVO updateReqVO) {
        //更新操作，需要传ID，判断是否有传，如果未传则提示id不能为空
        if(updateReqVO.getRybh() == null|| updateReqVO.getRybh().isEmpty()){
            return CommonResult.error("rybh参数不能为空！");
        }
        InRecordJlsDO db = inRecordJlsService.getPrisonerInfo(updateReqVO.getRybh());
        if(db != null){
            updateReqVO.setId(db.getId());
            inRecordJlsService.updateInRecordJls(updateReqVO);
        }else {
            System.out.println("未找到RYBH为：" + updateReqVO.getRybh() + "的记录！");
        }

        return success(true);
    }
}
