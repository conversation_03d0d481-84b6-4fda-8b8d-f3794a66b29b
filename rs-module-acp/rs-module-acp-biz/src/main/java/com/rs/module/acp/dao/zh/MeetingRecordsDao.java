package com.rs.module.acp.dao.zh;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.zh.MeetingRecordsDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.zh.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
* 实战平台-综合管理-会议记录 Dao
*
* <AUTHOR>
*/
@Mapper
public interface MeetingRecordsDao extends IBaseDao<MeetingRecordsDO> {

    /**
     * 获取指定类型最大流水号
     *
     * @return
     */
    @Select("SELECT MAX(wsh::integer) FROM acp_zh_meeting_records where category_id=#{categoryId} and update_time between #{startTime} and #{endTime}")
    Integer getMaxWsh(@Param("startTime") LocalDateTime startTime,
                      @Param("endTime") LocalDateTime endTime,
                      @Param("categoryId") String categoryId);
    default Integer getNowYeaMaxWsh(String categoryId) {
        LocalDateTime startTime = LocalDateTime.of(
                LocalDateTime.now().with(TemporalAdjusters.firstDayOfYear()).toLocalDate(), LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(
                LocalDateTime.now().with(TemporalAdjusters.lastDayOfYear()).toLocalDate(), LocalTime.MAX);
        return Optional.ofNullable(this.getMaxWsh(startTime, endTime,categoryId)).orElse(0);
    }
}
