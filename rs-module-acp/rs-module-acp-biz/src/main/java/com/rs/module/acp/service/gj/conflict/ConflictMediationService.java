package com.rs.module.acp.service.gj.conflict;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictMediationRespVO;
import com.rs.module.acp.entity.gj.conflict.ConflictMediationDO;
import com.rs.module.acp.enums.gj.MediationResultEnum;

import java.util.List;

/**
 * 实战平台-管教业务-社会矛盾化解 Service 接口
 *
 * <AUTHOR>
 */
public interface ConflictMediationService extends IBaseService<ConflictMediationDO> {

    int getMediationCntByEventCode(String eventCode);

    List<ConflictMediationRespVO> getMediationListByEventCode(String eventCode);

    List<ConflictMediationDO> getMediationByEventCodeAndMediationResult(String eventCode, MediationResultEnum mediationResult);

}
