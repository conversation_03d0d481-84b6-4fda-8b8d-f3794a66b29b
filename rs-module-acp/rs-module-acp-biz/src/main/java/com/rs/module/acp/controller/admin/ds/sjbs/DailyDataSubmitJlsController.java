package com.rs.module.acp.controller.admin.ds.sjbs;

import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.DailyDataSubmitJlsRespVO;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.DailyDataSubmitJlsSaveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.ds.sjbs.DailyDataSubmitJlsDO;
import com.rs.module.acp.service.ds.sjbs.DailyDataSubmitJlsService;

@Api(tags = "实战平台-数据固化-每日数据报送(拘留所)")
@RestController
@RequestMapping("/acp/ds/dailyDataSubmitJls")
@Validated
public class DailyDataSubmitJlsController {

    @Resource
    private DailyDataSubmitJlsService dailyDataSubmitJlsService;
/*
    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-数据固化-每日数据报送(拘留所)")
    public CommonResult<String> createDailyDataSubmitJls(@Valid @RequestBody DailyDataSubmitJlsSaveReqVO createReqVO) {
        return success(dailyDataSubmitJlsService.createDailyDataSubmitJls(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-数据固化-每日数据报送(拘留所)")
    public CommonResult<Boolean> updateDailyDataSubmitJls(@Valid @RequestBody DailyDataSubmitJlsSaveReqVO updateReqVO) {
        dailyDataSubmitJlsService.updateDailyDataSubmitJls(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-数据固化-每日数据报送(拘留所)")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteDailyDataSubmitJls(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           dailyDataSubmitJlsService.deleteDailyDataSubmitJls(id);
        }
        return success(true);
    }*/

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-数据固化-每日数据报送(拘留所)")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<DailyDataSubmitJlsRespVO> getDailyDataSubmitJls(@RequestParam("id") String id) {
        DailyDataSubmitJlsDO dailyDataSubmitJls = dailyDataSubmitJlsService.getDailyDataSubmitJls(id);
        return success(BeanUtils.toBean(dailyDataSubmitJls, DailyDataSubmitJlsRespVO.class));
    }
    @GetMapping("/getBySolidificationDate")
    @ApiOperation(value = "获得实战平台-数据固化-每日数据报送(拘留所)")
    @ApiImplicitParam(name = "solidificationDate", value = "日期默认昨天")
    public CommonResult<DailyDataSubmitJlsRespVO> getBySolidificationDate(
            @RequestParam(value = "solidificationDate",required = false) String solidificationDate) {
        DailyDataSubmitJlsDO dailyDataSubmitJls = dailyDataSubmitJlsService.getDailyDataSubmitJlsBySolidificationDate(solidificationDate, SessionUserUtil.getSessionUser().getOrgCode());
        return success(BeanUtils.toBean(dailyDataSubmitJls, DailyDataSubmitJlsRespVO.class));
    }
}
