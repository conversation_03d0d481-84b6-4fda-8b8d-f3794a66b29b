package com.rs.module.acp.controller.admin.gj.vo.conflict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 实战平台-管教业务-社会矛盾化解调解参与单位新增/修改 Request VO")
@Data
public class ConflictMediationOrgSaveReqVO {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("事件编号")
    private String eventCode;

    @ApiModelProperty("调解单位名称")
    private String mediationOrgName;

    @ApiModelProperty("参与人数")
    private Integer partiCnt;

    @ApiModelProperty("人员名单")
    private String personnelList;

}
