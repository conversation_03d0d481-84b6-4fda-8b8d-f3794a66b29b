package com.rs.module.acp.entity.pi;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-巡视管控-所情处置 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_prison_event_handle")
@KeySequence("acp_pi_prison_event_handle_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_prison_event_handle")
public class PrisonEventHandleDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 所属警情
     */
    private String eventId;
    /**
     * 处置反馈
     */
    private String feedbackInfo;
    /**
     * 处置人ID
     */
    private String handleUserSfzh;
    /**
     * 处置人名称
     */
    private String handleUserName;
    /**
     * 处置时间
     */
    private Date handleTime;
    /**
     * 处置状态 0未办结 1已办结 2不通过 3通过
     */
    private String status;
    /**
     * 处置时所处岗位
     */
    private String handlePost;
    /**
     * 处置岗位编码
     */
    private String handlePostCode;
    /**
     * 历史数据：1是，0最新数据
     */
    private Integer history;
    /**
     * 处置类型 1.处置，2审批
     */
    private Integer handleType;
    /**
     * 审批内容
     */
    private String handleInfo;
    /**
     * 变动内容
     */
    private String changeContent;
    /**
     * 附件地址
     */
    private String attUrl;
    /**
     * 报告本级公安机关时间
     */
    private Date reportLevelPolice;
    /**
     * 报告上级监管业务指导部门时间
     */
    private Date reportSupervise;
    /**
     * 报告上级监管业务指导部门时间
     */
    private Date reportProcuratorialOrgans;

}
