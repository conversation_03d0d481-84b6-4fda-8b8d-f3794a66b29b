package com.rs.module.acp.controller.admin.db;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.PersonalEffectsDO;
import com.rs.module.acp.service.db.PersonalEffectsService;

@Api(tags = "实战平台-收押业务-随身物品登记")
@RestController
@RequestMapping("/acp/db/personalEffects")
@Validated
public class PersonalEffectsController {

    @Resource
    private PersonalEffectsService personalEffectsService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-收押业务-随身物品登记")
    public CommonResult<String> createPersonalEffects(@Valid @RequestBody PersonalEffectsSaveReqVO createReqVO) {
        return success(personalEffectsService.createPersonalEffects(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-收押业务-随身物品登记")
    public CommonResult<Boolean> updatePersonalEffects(@Valid @RequestBody PersonalEffectsSaveReqVO updateReqVO) {
        personalEffectsService.updatePersonalEffects(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-收押业务-随身物品登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deletePersonalEffects(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           personalEffectsService.deletePersonalEffects(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-收押业务-随身物品登记")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<PersonalEffectsRespVO> getPersonalEffects(@RequestParam("id") String id) {
        PersonalEffectsDO personalEffects = personalEffectsService.getPersonalEffects(id);
        return success(BeanUtils.toBean(personalEffects, PersonalEffectsRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-收押业务-随身物品登记分页")
    public CommonResult<PageResult<PersonalEffectsRespVO>> getPersonalEffectsPage(@Valid @RequestBody PersonalEffectsPageReqVO pageReqVO) {
        PageResult<PersonalEffectsDO> pageResult = personalEffectsService.getPersonalEffectsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PersonalEffectsRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-收押业务-随身物品登记列表")
    public CommonResult<List<PersonalEffectsRespVO>> getPersonalEffectsList(@Valid @RequestBody PersonalEffectsListReqVO listReqVO) {
        List<PersonalEffectsDO> list = personalEffectsService.getPersonalEffectsList(listReqVO);
        return success(BeanUtils.toBean(list, PersonalEffectsRespVO.class));
    }

    /**
     * 通过人员编号获取随身物品登记信息
     */
    @GetMapping("/getByRybh")
    @ApiOperation(value = "通过人员编号获取随身物品登记信息")
    public CommonResult<PersonalEffectsRespVO> getPersonalEffectsByRybh(@RequestParam("rybh") String rybh) {
        PersonalEffectsDO personalEffect = personalEffectsService.getPersonalEffectsByRybh(rybh);
        return success(BeanUtils.toBean(personalEffect, PersonalEffectsRespVO.class));
    }
}
