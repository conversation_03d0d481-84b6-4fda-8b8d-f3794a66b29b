package com.rs.module.acp.service.db.components;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rs.module.acp.cons.ThirdBiometricTypeEnum;
import com.rs.module.acp.controller.admin.db.vo.PersonalEffectsSubListReqVO;
import com.rs.module.acp.controller.admin.db.vo.third.haixin.PhotoVO;
import com.rs.module.acp.controller.admin.gj.vo.transitionroom.TransitionRoomSaveReqVO;
import com.rs.module.acp.controller.admin.wb.vo.DefaultCasePersonneSaveReqVO;
import com.rs.module.acp.controller.admin.wb.vo.SocialRelationsChildSaveReqVO;
import com.rs.module.acp.controller.admin.wb.vo.SocialRelationsListReqVO;
import com.rs.module.acp.controller.admin.wb.vo.SocialRelationsSaveReqVO;
import com.rs.module.acp.dao.db.BiometricInfoDao;
import com.rs.module.acp.dao.db.DetainRegKssDao;
import com.rs.module.acp.dao.db.PersonalEffectsSubDao;
import com.rs.module.acp.dao.wb.SocialRelationsDao;
import com.rs.module.acp.entity.db.BiometricInfoDO;
import com.rs.module.acp.entity.db.DetainRegKssDO;
import com.rs.module.acp.entity.db.PersonalEffectsSubDO;
import com.rs.module.acp.entity.wb.SocialRelationsDO;
import com.rs.module.acp.service.db.BiometricInfoService;
import com.rs.module.acp.service.gj.transitionroom.TransitionRoomService;
import com.rs.module.acp.service.wb.CasePersonnelService;
import com.rs.module.acp.service.wb.SocialRelationsService;
import com.rs.module.ptm.api.PtmGoodsApi;
import com.rs.module.ptm.vo.GoodsDetailSaveReqVO;
import com.rs.module.ptm.vo.GoodsSaveReqVO;
import com.rs.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service("kssPrisonerInInfoHandler")
public class KssPrisonerInInfoHandler implements PrisonerInInfoSyncHandler {

    //过渡监室人员模块对端接口调用
    @Resource
    private TransitionRoomService transitionRoomService;
    @Resource
    private DetainRegKssDao detainRegKssDao;
    //办案人员对端接口调用
    @Resource
    private CasePersonnelService casePersonnelService;
    //随身物品管理对端接口
    @Resource
    private PtmGoodsApi ptmGoodsApi;
    @Resource
    private PersonalEffectsSubDao personalEffectsSubDao;
    @Resource
    private SocialRelationsDao socialRelationsDao;
    @Resource
    private SocialRelationsService socialRelationsService;
    @Resource
    private BiometricInfoDao biometricInfoDao;

    @Override
    public boolean syncPrisonerInInfo(String rybh) {
        // TODO: 调用看守所模块相关接口，推送入所信息
        System.out.println("正在向看守所模块推送入所人员信息，人员编号：" + rybh);

        // 将人员三面照同步到在所表中
        BiometricInfoDO biometricInfoDO = biometricInfoDao.selectOne(new LambdaQueryWrapper<BiometricInfoDO>()
                .eq(BiometricInfoDO::getRybh, rybh)
                .eq(BiometricInfoDO::getCjxmlx, ThirdBiometricTypeEnum.PHOTO.getCode()));
        List<PhotoVO> photoList = JSONObject.parseArray(biometricInfoDO.getSwtz(), PhotoVO.class);
        String frontPhoto = "";
        String leftPhoto = "";
        String rightPhoto = "";
        for (PhotoVO photoVO : photoList) {
            switch (photoVO.getRyzplxdm()) {
                case "11":
                    frontPhoto = photoVO.getUrl();
                    break;
                case "12":
                    leftPhoto = photoVO.getUrl();
                    break;
                case "13":
                    rightPhoto = photoVO.getUrl();
                    break;
            }
        }
        detainRegKssDao.syncPhotoToPrisonerKssIn(rybh, frontPhoto, leftPhoto, rightPhoto);

        return true; // 返回推送结果
    }

    @Override
    public void syncDataToTransitionRoom(String rybh, String orgCode,String id) {
        log.info("正在向过渡监室模块推送入所人员信息，人员编号：{}", rybh);
        try {
            //detainRegKssDao 查询该rybh数据
            DetainRegKssDO detainRegKssDO = detainRegKssDao.selectOne("jgrybm", rybh);
            TransitionRoomSaveReqVO transitionRoomSaveReqVO = new TransitionRoomSaveReqVO();
            transitionRoomSaveReqVO.setJgrybm(detainRegKssDO.getJgrybm());
            transitionRoomSaveReqVO.setJgryxm(detainRegKssDO.getXm());
            transitionRoomSaveReqVO.setStartDate(detainRegKssDO.getJyrq());
            Date endDate = DateUtil.add(detainRegKssDO.getJyrq(), Calendar.DAY_OF_MONTH, 15);
            transitionRoomSaveReqVO.setEndDate(endDate);
            transitionRoomSaveReqVO.setRoomId(detainRegKssDO.getJsh());
            transitionRoomSaveReqVO.setRoomName(detainRegKssDO.getRoomName());
            log.info("同步到过渡监室模块信息为:{}",transitionRoomSaveReqVO.toJsonString());
            transitionRoomService.createTransitionRoom(transitionRoomSaveReqVO);
        } catch (Exception e) {
            log.info("同步过渡监室模块推送入所人员信息，人员编号：{} 异常，{}", rybh,e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void syncDataToCasePersonnel(String rybh, String orgCode, String id) {
        //detainRegKssDao 查询该rybh数据
        try {
            DetainRegKssDO detainRegKssDO = detainRegKssDao.selectOne("jgrybm", rybh);

            List<DefaultCasePersonneSaveReqVO> createReqVO = new ArrayList<>();
            DefaultCasePersonneSaveReqVO defaultCasePersonneSaveReqVO = new DefaultCasePersonneSaveReqVO();
//        defaultCasePersonneSaveReqVO.setJh(detainRegKssDO.getJh());
            defaultCasePersonneSaveReqVO.setXm(detainRegKssDO.getBar());
            defaultCasePersonneSaveReqVO.setZjlx(detainRegKssDO.getBarzjlx());
            defaultCasePersonneSaveReqVO.setZjhm(detainRegKssDO.getBarzjhm());
            defaultCasePersonneSaveReqVO.setBadwdm(detainRegKssDO.getBadw());
            defaultCasePersonneSaveReqVO.setBadwmc(detainRegKssDO.getBadw());
            defaultCasePersonneSaveReqVO.setLxfs(detainRegKssDO.getBarlxff());
            defaultCasePersonneSaveReqVO.setXb(detainRegKssDO.getBarxb());
//        defaultCasePersonneSaveReqVO.setZpUrl(detainRegKssDO.getZpUrl());
//        defaultCasePersonneSaveReqVO.setGzzjUrl(detainRegKssDO.getGzzjUrl());
//        defaultCasePersonneSaveReqVO.setTjjglx(detainRegKssDO.getTjjglx());
            defaultCasePersonneSaveReqVO.setJgrybm(detainRegKssDO.getJgrybm());
            createReqVO.add(defaultCasePersonneSaveReqVO);
            log.info("同步到实战平台-窗口业务-办案人员信息为:{}", defaultCasePersonneSaveReqVO.toJsonString());


            casePersonnelService.saveDefaultCasePersonnelList(createReqVO);
        } catch (Exception e) {
            log.info("同步到实战平台-窗口业务-办案人员信息为：{} 异常，{}", rybh,e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 将随身物品同步给附物管理系统那边
     * @param rybh
     * @param orgCode
     * @param id
     */
    @Override
    public void syncToPtmGoods(String rybh, String orgCode, String id)  {
        try {
            PersonalEffectsSubListReqVO listReqVO = new PersonalEffectsSubListReqVO();
            listReqVO.setRybh(rybh);
            List<PersonalEffectsSubDO> list = personalEffectsSubDao.selectList(listReqVO);

            //如果list==null 或者 list.size()==0，直接返回
            if (list == null || list.size() == 0) {
                return;
            }
            GoodsSaveReqVO reqVO = new GoodsSaveReqVO();
            List<GoodsDetailSaveReqVO> goodsDetailSaveReqVOList = new ArrayList<>();
            for (PersonalEffectsSubDO personalEffectsSubDO : list) {
                reqVO.setJgrybm(personalEffectsSubDO.getRybh());
                reqVO.setJgryxm(personalEffectsSubDO.getRyxm());

                //GoodsDetailSaveReqVO
                GoodsDetailSaveReqVO goodsDetailSaveReqVO = new GoodsDetailSaveReqVO();
                goodsDetailSaveReqVO.setPersonalGoodsId(personalEffectsSubDO.getPersonalEffectsId());
                goodsDetailSaveReqVO.setJgrybm(personalEffectsSubDO.getRybh());
                goodsDetailSaveReqVO.setJgryxm(personalEffectsSubDO.getRyxm());
                goodsDetailSaveReqVO.setWpbh(personalEffectsSubDO.getId());
                goodsDetailSaveReqVO.setName(personalEffectsSubDO.getWpmc());
                goodsDetailSaveReqVO.setGoodsType(personalEffectsSubDO.getZl());
                goodsDetailSaveReqVO.setQuantity(Integer.parseInt(personalEffectsSubDO.getSl()));
                goodsDetailSaveReqVO.setUnit(personalEffectsSubDO.getHbzl());
                goodsDetailSaveReqVO.setStorageLocation(personalEffectsSubDO.getWz());
                goodsDetailSaveReqVO.setStorageLocationName(personalEffectsSubDO.getWpczqk());//暫時用
                goodsDetailSaveReqVO.setFeatures(personalEffectsSubDO.getWptz());

                List<String> photoPathList = goodsDetailSaveReqVO.getPhotoPathList();

                ObjectMapper mapper = new ObjectMapper();
                if(personalEffectsSubDO.getWpzp()!=null&& !personalEffectsSubDO.getWpzp().equals("")) {
                    photoPathList = mapper.readValue(personalEffectsSubDO.getWpzp(), List.class);
                }

                goodsDetailSaveReqVO.setPhotoPathList(photoPathList);
                goodsDetailSaveReqVO.setPhotoCount(photoPathList==null?0:photoPathList.size());
                goodsDetailSaveReqVO.setRemark(personalEffectsSubDO.getBz());

                goodsDetailSaveReqVOList.add(goodsDetailSaveReqVO);
            }
            reqVO.setGoodsDetailSaveReqVOList(goodsDetailSaveReqVOList);
            ptmGoodsApi.createGoods(reqVO);
        } catch (Exception e) {
            log.error("syncToPtmGoods  err,json:{}", e);
        }
    }

    @Override
    public void syncSocialRelations(String rybh, String orgCode, String id) {
        try {
            SocialRelationsListReqVO reqVO = new SocialRelationsListReqVO();
            reqVO.setJgrybm(rybh);
            List<SocialRelationsDO> list = socialRelationsDao.selectList(reqVO);
            SocialRelationsSaveReqVO createReqVO = new SocialRelationsSaveReqVO();
            List<SocialRelationsChildSaveReqVO> socialRelationList = new ArrayList<>();
            for (SocialRelationsDO socialRelationsDO : list) {
                createReqVO.setJgrybm(socialRelationsDO.getJgrybm());

                SocialRelationsChildSaveReqVO socialRelationsChildSaveReqVO = new SocialRelationsChildSaveReqVO();
//            socialRelationsChildSaveReqVO.setId(socialRelationsDO.getId());
                socialRelationsChildSaveReqVO.setName(socialRelationsDO.getName());
                socialRelationsChildSaveReqVO.setGender(socialRelationsDO.getGender());
                socialRelationsChildSaveReqVO.setIdType(socialRelationsDO.getIdType());
                socialRelationsChildSaveReqVO.setIdNumber(socialRelationsDO.getIdNumber());
                socialRelationsChildSaveReqVO.setRelationship(socialRelationsDO.getRelationship());
                socialRelationsChildSaveReqVO.setContact(socialRelationsDO.getContact());
                socialRelationsChildSaveReqVO.setWorkUnit(socialRelationsDO.getWorkUnit());
                socialRelationsChildSaveReqVO.setAddress(socialRelationsDO.getAddress());
                socialRelationsChildSaveReqVO.setOccupation(socialRelationsDO.getOccupation());
                socialRelationsChildSaveReqVO.setImageUrl(socialRelationsDO.getImageUrl());
                socialRelationsChildSaveReqVO.setRelationsAttch(socialRelationsDO.getRelationsAttch());
                socialRelationList.add(socialRelationsChildSaveReqVO);

            }
            createReqVO.setSocialRelationList(socialRelationList);
            socialRelationsService.createSocialRelations(createReqVO);
        } catch (Exception e) {
            log.error("syncSocialRelations  err,json:{}", e);
        }
    }
}
