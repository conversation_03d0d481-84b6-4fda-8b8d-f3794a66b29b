package com.rs.module.acp.dao.sys;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.sys.VbConfigCurrentObjectDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.sys.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-语音播报-即时播报对象 Dao
*
* <AUTHOR>
*/
@Mapper
public interface VbConfigCurrentObjectDao extends IBaseDao<VbConfigCurrentObjectDO> {


    default PageResult<VbConfigCurrentObjectDO> selectPage(VbConfigCurrentObjectPageReqVO reqVO) {
        Page<VbConfigCurrentObjectDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<VbConfigCurrentObjectDO> wrapper = new LambdaQueryWrapperX<VbConfigCurrentObjectDO>()
            .eqIfPresent(VbConfigCurrentObjectDO::getConfigId, reqVO.getConfigId())
            .eqIfPresent(VbConfigCurrentObjectDO::getVbPrisonId, reqVO.getVbPrisonId())
            .eqIfPresent(VbConfigCurrentObjectDO::getVbAreaId, reqVO.getVbAreaId())
            .eqIfPresent(VbConfigCurrentObjectDO::getVbRoomId, reqVO.getVbRoomId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(VbConfigCurrentObjectDO::getAddTime);
        }
        Page<VbConfigCurrentObjectDO> vbConfigCurrentObjectPage = selectPage(page, wrapper);
        return new PageResult<>(vbConfigCurrentObjectPage.getRecords(), vbConfigCurrentObjectPage.getTotal());
    }
    default List<VbConfigCurrentObjectDO> selectList(VbConfigCurrentObjectListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<VbConfigCurrentObjectDO>()
            .eqIfPresent(VbConfigCurrentObjectDO::getConfigId, reqVO.getConfigId())
            .eqIfPresent(VbConfigCurrentObjectDO::getVbPrisonId, reqVO.getVbPrisonId())
            .eqIfPresent(VbConfigCurrentObjectDO::getVbAreaId, reqVO.getVbAreaId())
            .eqIfPresent(VbConfigCurrentObjectDO::getVbRoomId, reqVO.getVbRoomId())
        .orderByDesc(VbConfigCurrentObjectDO::getAddTime));    }


    }
