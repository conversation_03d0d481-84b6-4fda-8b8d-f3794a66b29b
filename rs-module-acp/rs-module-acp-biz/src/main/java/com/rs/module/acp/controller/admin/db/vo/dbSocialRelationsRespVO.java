package com.rs.module.acp.controller.admin.db.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-收押业务-社会关系 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class dbSocialRelationsRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("人员编号")
    private String rybh;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("性别")
    @Trans(type = TransType.DICTIONARY,key = "ZD_XB")
    private String gender;
    @ApiModelProperty("证件类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_ZJLX")
    private String idType;
    @ApiModelProperty("证件号码")
    private String idNumber;
    @ApiModelProperty("社会关系")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SHGX")
    private String relationship;
    @ApiModelProperty("联系方式")
    private String contact;
    @ApiModelProperty("工作单位")
    private String workUnit;
    @ApiModelProperty("居住地址")
    private String address;
    @ApiModelProperty("职业")
    private String occupation;
    @ApiModelProperty("照片")
    private String imageUrl;
}
