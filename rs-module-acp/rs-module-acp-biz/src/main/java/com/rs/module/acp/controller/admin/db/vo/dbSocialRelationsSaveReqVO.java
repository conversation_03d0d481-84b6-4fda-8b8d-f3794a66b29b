package com.rs.module.acp.controller.admin.db.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-收押业务-社会关系新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class dbSocialRelationsSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("人员编号")
//    @NotEmpty(message = "人员编号不能为空")
    private String rybh;

    @ApiModelProperty("姓名")
//    @NotEmpty(message = "姓名不能为空")
    private String name;

    @ApiModelProperty("性别")
//    @NotEmpty(message = "性别不能为空")
    private String gender;

    @ApiModelProperty("证件类型")
//    @NotEmpty(message = "证件类型不能为空")
    private String idType;

    @ApiModelProperty("证件号码")
//    @NotEmpty(message = "证件号码不能为空")
    private String idNumber;

    @ApiModelProperty("社会关系")
//    @NotEmpty(message = "社会关系不能为空")
    private String relationship;

    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("工作单位")
    private String workUnit;

    @ApiModelProperty("居住地址")
    private String address;

    @ApiModelProperty("职业")
    private String occupation;

    @ApiModelProperty("照片")
    private String imageUrl;

}
