package com.rs.module.acp.service.pi;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventHandleListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventHandlePageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventHandleRespVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventHandleSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.entity.pi.PrisonEventHandleDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.PrisonEventHandleDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-巡视管控-所情处置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrisonEventHandleServiceImpl extends BaseServiceImpl<PrisonEventHandleDao, PrisonEventHandleDO> implements PrisonEventHandleService {

    @Resource
    private PrisonEventHandleDao prisonEventHandleDao;

    @Override
    public String createPrisonEventHandle(PrisonEventHandleSaveReqVO createReqVO) {
        // 插入
        PrisonEventHandleDO prisonEventHandle = BeanUtils.toBean(createReqVO, PrisonEventHandleDO.class);
        prisonEventHandleDao.insert(prisonEventHandle);
        // 返回
        return prisonEventHandle.getId();
    }

    @Override
    public void updatePrisonEventHandle(PrisonEventHandleSaveReqVO updateReqVO) {
        // 校验存在
        validatePrisonEventHandleExists(updateReqVO.getId());
        // 更新
        PrisonEventHandleDO updateObj = BeanUtils.toBean(updateReqVO, PrisonEventHandleDO.class);
        prisonEventHandleDao.updateById(updateObj);
    }

    @Override
    public void deletePrisonEventHandle(String id) {
        // 校验存在
        validatePrisonEventHandleExists(id);
        // 删除
        prisonEventHandleDao.deleteById(id);
    }

    private void validatePrisonEventHandleExists(String id) {
        if (prisonEventHandleDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-所情处置数据不存在");
        }
    }

    @Override
    public PrisonEventHandleDO getPrisonEventHandle(String id) {
        return prisonEventHandleDao.selectById(id);
    }

    @Override
    public PageResult<PrisonEventHandleDO> getPrisonEventHandlePage(PrisonEventHandlePageReqVO pageReqVO) {
        return prisonEventHandleDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrisonEventHandleDO> getPrisonEventHandleList(PrisonEventHandleListReqVO listReqVO) {
        return prisonEventHandleDao.selectList(listReqVO);
    }

    @Override
    public List<PrisonEventHandleRespVO> getNewPrisonEventHandleListByEventId(String eventId) {
        LambdaQueryWrapper<PrisonEventHandleDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(PrisonEventHandleDO::getId,PrisonEventHandleDO::getEventId,
                PrisonEventHandleDO::getFeedbackInfo,PrisonEventHandleDO::getHandleUserSfzh,
                PrisonEventHandleDO::getHandleUserName,PrisonEventHandleDO::getHandleTime,
                PrisonEventHandleDO::getStatus,PrisonEventHandleDO::getHandlePost,
                PrisonEventHandleDO::getHandlePostCode,PrisonEventHandleDO::getHandleInfo,
                PrisonEventHandleDO::getAttUrl,PrisonEventHandleDO::getHandleType,PrisonEventHandleDO::getHistory);
        lambdaQueryWrapper.eq(PrisonEventHandleDO::getEventId,eventId)
                .orderByAsc(PrisonEventHandleDO::getHandleTime);
        List<PrisonEventHandleDO> handleDOList = list(lambdaQueryWrapper);
        if(CollectionUtil.isEmpty(handleDOList)){
            return new ArrayList<>();
        }
        return BeanUtils.toBean(handleDOList,PrisonEventHandleRespVO.class);
    }
}
