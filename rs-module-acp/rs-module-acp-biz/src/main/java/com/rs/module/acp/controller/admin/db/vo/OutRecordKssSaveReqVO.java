package com.rs.module.acp.controller.admin.db.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-羁押业务-出所登记（看守所）新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class OutRecordKssSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("批准/执行机关")
//    @NotEmpty(message = "批准/执行机关不能为空")
    private String pzzxjg;

    @ApiModelProperty("批准/执行人")
//    @NotEmpty(message = "批准/执行人不能为空")
    private String pzzxr;

    @ApiModelProperty("批准日期")
//    @NotNull(message = "批准日期不能为空")
    private Date pzrq;

    @ApiModelProperty("出所时间")
//    @NotNull(message = "出所时间不能为空")
    private Date cssj;

    @ApiModelProperty("出所原因")
//    @NotEmpty(message = "出所原因不能为空")
    private String csyy;

    @ApiModelProperty("出所去向")
//    @NotEmpty(message = "出所去向不能为空")
    private String csqx;

    @ApiModelProperty("出所凭证")
//    @NotEmpty(message = "出所凭证不能为空")
    private String cspz;

    @ApiModelProperty("出所凭证文书号")
//    @NotEmpty(message = "出所凭证文书号不能为空")
    private String cspzwsh;

    @ApiModelProperty("出所凭证文书号")
    private String cspzwsdz;

    @ApiModelProperty("财物交接情况")
    private String cwjjqk;

    @ApiModelProperty("财物交接实体")
    private String cwjjst;

    @ApiModelProperty("档案材料移交情况")
//    @NotEmpty(message = "档案材料移交情况不能为空")
    private String daclyjqk;

    @ApiModelProperty("档案材料移交实体")
    private String daclyjst;

    @ApiModelProperty("是否检查发现书信或者物品")
    private Short sfjcfxshhzwp;

    @ApiModelProperty("检查发现书信或者物品情况记录")
    private String jcfxsxhzwpqkjl;

    @ApiModelProperty("书信或者物品照片")
    private String sxhzwpzp;

    @ApiModelProperty("是否为他人捎带物品")
    private Short sfwtrsdwp;

    @ApiModelProperty("捎带物品类型")
    private String sdwplx;

    @ApiModelProperty("是否有帮其他人串供行为")
    private Short sfybzqtrcgxw;

    @ApiModelProperty("详细情况记录")
    private String xxqkjl;

    @ApiModelProperty("是否发现其他违法犯罪行为")
    private Short sffxqtwffzxs;

    @ApiModelProperty("其他违法犯罪行为")
    private String qtwffzxw;

    @ApiModelProperty("通报办案机关及办案机关回复情况")
    private String tbbajgjbajghfqk;

    @ApiModelProperty("转去公安监所编码")
    private String zqgajsbm;

    @ApiModelProperty("转去公安监所名称")
    private String zqgajsmc;

    @ApiModelProperty("经办人身份证号")
//    @NotEmpty(message = "经办人身份证号不能为空")
    private String jbrsfzh;

    @ApiModelProperty("经办人")
//    @NotEmpty(message = "经办人不能为空")
    private String jbr;

    @ApiModelProperty("经办时间")
//    @NotNull(message = "经办时间不能为空")
    private Date jbsj;

    @ApiModelProperty("审批意见")
    private String approvalResult;

    @ApiModelProperty("审批状态：01：未审批；02：同意；03：不同意；")
//    @NotEmpty(message = "办理状态不能为空")
    private String spzt;

    @ApiModelProperty("办理状态:01:待登记；02：草稿；03：已提交")
//    @NotEmpty(message = "办理状态不能为空")
    private String status;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("手环ID")
    private String shid;

    @ApiModelProperty("手环绑定状态")
    private String shbdzt;

    @ApiModelProperty("手环解除绑定时间")
    private Date sdjcbdsj;

    @ApiModelProperty("离所确认状态")
    private String lsqrzt;

    @ApiModelProperty("离所确认去向")
    private String lsqrqx;

    @ApiModelProperty("离所确认备注")
    private String lsqrbz;

    @ApiModelProperty("离所确认经办人身份证号")
    private String lsqrjbrsfzh;

    @ApiModelProperty("离所确认经办人")
    private String lsqrjbr;

    @ApiModelProperty("离所确认经办时间")
    private Date lsqrjbsj;

    @ApiModelProperty("当前阶段  01：出所登记；02：出所检查；03：财务交接/物品取出；04：领导审批；05：防误放验证；06：离所确认；")
    private String currentStep;

}
