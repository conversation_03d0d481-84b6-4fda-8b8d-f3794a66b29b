package com.rs.module.acp.entity.pm;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-监管管理-定位标签与人员绑定 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_localsense_tag_person")
@KeySequence("acp_pm_localsense_tag_person_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_localsense_tag_person")
public class LocalsenseTagPersonDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 绑定来源 ，01：入所，02：智能腕带
     */
    private String bindSource;
    /**
     * 标签id
     */
    private String tagId;
    /**
     * 人员标签类型，01：被监管人员，02：民警
     */
    private String personType;
    /**
     * 绑定人员ID
     */
    private String bindPersonId;
    /**
     * bind_person_name
     */
    private String bindPersonName;
    /**
     * 绑定时间
     */
    private Date bindTime;
    /**
     * 解绑时间
     */
    private Date unbindTime;
    /**
     * 解绑原因
     */
    private String unbindReason;
    /**
     * 状态
     */
    private String status;

}
