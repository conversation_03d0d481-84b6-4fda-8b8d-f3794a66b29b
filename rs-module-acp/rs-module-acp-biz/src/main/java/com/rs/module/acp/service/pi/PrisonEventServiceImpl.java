package com.rs.module.acp.service.pi;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.cache.DicUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.BspSdk;
import com.rs.adapter.bsp.api.UserApi;
import com.rs.adapter.bsp.api.dto.OrgRespDTO;
import com.rs.adapter.bsp.api.dto.OrgUserRespDTO;
import com.rs.adapter.bsp.api.dto.UserRespDTO;
import com.rs.module.acp.config.WebSocketServer;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.*;
import com.rs.module.acp.entity.pi.*;
import com.rs.module.acp.service.wb.WbCommonService;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.sys.MsgAddVO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.util.MsgUtil;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.PrisonEventDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-巡视管控-所情登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrisonEventServiceImpl extends BaseServiceImpl<PrisonEventDao, PrisonEventDO> implements PrisonEventService {

    @Resource
    private PrisonEventDao prisonEventDao;

    @Autowired
    private PrisonEventPrisonerService prisonEventPrisonerService;

    @Autowired
    private WbCommonService wbCommonService;

    @Autowired
    private PrisonEventTypeService prisonEventTypeService;

    @Value("${acp.business_no.prison_event_no:acp_prison_event_no}")
    private String prisonEventNo;

    @Autowired
    private BspApi bspApi;

    @Autowired
    private UserApi userApi;

    @Autowired
    private BspSdk bspSdk;

    @Autowired
    private PrisonEventHandleService prisonEventHandleService;

    @Autowired
    private PrisonEventPushSettingService prisonEventPushSettingService;

    @Autowired
    private PrisonerService prisonerService;

    @Autowired
    private PrisonEventBusinessService prisonEventBusinessService;

    @Autowired
    private PrisonEventSettingService prisonEventSettingService;

    @Autowired
    private PrisonEventTypeItemService prisonEventTypeItemService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createPrisonEvent(PrisonEventSaveReqVO createReqVO) {

        boolean isAdd = false;

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        // 插入
        PrisonEventDO prisonEvent = BeanUtils.toBean(createReqVO, PrisonEventDO.class);
        if(ObjectUtil.isEmpty(prisonEvent.getId())){
            prisonEvent.setId(StringUtil.getGuid32());
            isAdd = true;
        }

        //报警人
        if(CollectionUtil.isNotEmpty(createReqVO.getReportPrisonerList())){
            List<String> reportPrisonerSfzhList = new ArrayList<>();
            List<String> reportPrisonerNameList = new ArrayList<>();

            for(PrisonEventPersonSaveVO personSaveVO:createReqVO.getReportPrisonerList()){
                if(ObjectUtil.isNotEmpty(personSaveVO.getZjhm())){
                    reportPrisonerSfzhList.add(personSaveVO.getZjhm());
                }
                if(ObjectUtil.isNotEmpty(personSaveVO.getName())){
                    reportPrisonerNameList.add(personSaveVO.getName());
                }
            }
            if(CollectionUtil.isNotEmpty(reportPrisonerSfzhList)){
                prisonEvent.setReportPrisonerSfzhs(String.join(",",reportPrisonerSfzhList));
            }
            if(CollectionUtil.isNotEmpty(reportPrisonerNameList)){
                prisonEvent.setReportPrisonerName(String.join(",",reportPrisonerNameList));
            }
        }
        //在押人员
        if(CollectionUtil.isNotEmpty(createReqVO.getInPrisonerList())){
            prisonEventPrisonerService.savePrisonEventPrisonerList(createReqVO.getInPrisonerList(),prisonEvent.getId());
        }

        //工作人员
        if(CollectionUtil.isNotEmpty(createReqVO.getWorkPrisonerList())){
            List<String> policeSfzh =createReqVO.getWorkPrisonerList().stream().filter(x-> ObjectUtil.isNotEmpty(x.getZjhm()))
                    .map(PrisonEventPersonSaveVO::getZjhm).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(policeSfzh)){
                prisonEvent.setPoliceSfzh(String.join(",",policeSfzh));
            }
        }

        //外来人员
        if(CollectionUtil.isNotEmpty(createReqVO.getOutsiderPrisonerList())){
            List<String> outsider = new ArrayList<>();
            List<String> outsiderName = new ArrayList<>();

            for(PrisonEventPersonSaveVO personSaveVO:createReqVO.getOutsiderPrisonerList()){
                if(ObjectUtil.isNotEmpty(personSaveVO.getZjhm())){
                    outsider.add(personSaveVO.getZjhm());
                }
                if(ObjectUtil.isNotEmpty(personSaveVO.getName())){
                    outsiderName.add(personSaveVO.getName());
                }
            }
            if(CollectionUtil.isNotEmpty(outsider)){
                prisonEvent.setOutsider(String.join(",",outsider));
            }
            if(CollectionUtil.isNotEmpty(outsiderName)){
                prisonEvent.setOutsiderName(String.join(",",outsiderName));
            }
        }

        if(ObjectUtil.isNotEmpty(prisonEvent.getAttUrl())){
            prisonEvent.setAttUrl(wbCommonService.saveFile(null,prisonEvent.getAttUrl()));
        }

        //所情来源构建
        if(ObjectUtil.isEmpty(prisonEvent.getEventSrc())){
            //手动登记的---对应 所情联动配置的---所情登记
            prisonEvent.setEventSrc("SQDJ-"+prisonEvent.getEventLevel());
        }

        //推送信息
        prisonEvent.setDisposePostInfo(JSON.toJSONString(createReqVO.getPostInfoList()));

        //巡控登记人信息
        prisonEvent.setHandleUserName(sessionUser.getName());
        prisonEvent.setHandleUserSfzh(sessionUser.getIdCard());
        prisonEvent.setHandleTime(new Date());

        //所情编码
        prisonEvent.setEventCode(bspApi.executeByRuleCode(prisonEventNo,null));

        Map<String,List<UserRespDTO>> tsUserMap = new HashMap<>();

        if(isAdd){
            //创建巡控处置记录--因为是巡控直接新增，这里直接是已办结
            PrisonEventHandleDO handleDO = new PrisonEventHandleDO();
            handleDO.setId(StringUtil.getGuid32());
            handleDO.setEventId(prisonEvent.getId());
            handleDO.setStatus("1");
            handleDO.setHandlePostCode("02");
            handleDO.setHistory(0);
            handleDO.setHandleType(0);
            handleDO.setFeedbackInfo(prisonEvent.getHandleInfo());
            handleDO.setHandleTime(new Date());
            handleDO.setHandleUserName(sessionUser.getName());
            handleDO.setHandleUserSfzh(sessionUser.getIdCard());
            prisonEventHandleService.save(handleDO);

            prisonEvent.setStatus("1");

            //创建所情处置，推送信息，推送待办
            tsUserMap = pendingDisposal(prisonEvent,createReqVO.getPostInfoList(),sessionUser);

            prisonEventDao.insert(prisonEvent);
        }else {

            LambdaUpdateWrapper<PrisonEventHandleDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(PrisonEventHandleDO::getStatus,"1").set(PrisonEventHandleDO::getHandleUserSfzh,sessionUser.getIdCard())
                    .set(PrisonEventHandleDO::getHandleUserName,sessionUser.getName()).set(PrisonEventHandleDO::getHandleTime,new Date())
                    .set(PrisonEventHandleDO::getHandlePost,sessionUser.getPost())
                    .eq(PrisonEventHandleDO::getEventId,prisonEvent.getId()).eq(PrisonEventHandleDO::getHistory,0)
                    .eq(PrisonEventHandleDO::getHandleType,0);
            prisonEventHandleService.update(updateWrapper);
            if("0".equals(createReqVO.getSaveType())){
                //办结
                prisonEvent.setStatus("3");
            }else {
                prisonEvent.setStatus("1");
                tsUserMap = pendingDisposal(prisonEvent,createReqVO.getPostInfoList(),sessionUser);
            }
            prisonEventDao.updateById(prisonEvent);
        }

        //websocket 通知对应的用户
        sendMsgToWeb(prisonEvent,tsUserMap);

        // 返回
        return prisonEvent.getId();
    }

    /**
     * 创建待所情处置
     * @param eventDO
     * @param postInfoList
     * @param sessionUser
     */
    public Map<String,List<UserRespDTO>> pendingDisposal(PrisonEventDO eventDO,List<PrisonEventDisposePostSaveVO> postInfoList,SessionUser sessionUser){

        //根据推送配置，找人，一种：发待办的，2：发通知的
        Map<String,List<UserRespDTO>> toDoUserMap = new HashMap<>();
        List<UserRespDTO> noticeUserList = new ArrayList<>();

        for(PrisonEventDisposePostSaveVO disposePostSaveVO:postInfoList){
            List<UserRespDTO> tempList = new ArrayList<>();

            if("0".equals(disposePostSaveVO.getPostType())){
                //TODO 等待王龙的民警值班功能完成
            }else if("1".equals(disposePostSaveVO.getPostType())){
                tempList = userApi.getUserByOrgAndPost(sessionUser.getOrgCode(),disposePostSaveVO.getPostCode());
            }else if("2".equals(disposePostSaveVO.getPostType())){
                tempList = disposePostSaveVO.getOtherUserList();
            }

            if(!"2".equals(disposePostSaveVO.getPostType()) && CollectionUtil.isNotEmpty(disposePostSaveVO.getOtherUserList())){
                tempList.addAll(disposePostSaveVO.getOtherUserList());
            }

            if("1".equals(disposePostSaveVO.getIsBuiltIn())){

                List<UserRespDTO> userRespDTOList = new ArrayList<>();
                if(toDoUserMap.containsKey(disposePostSaveVO.getPostCode())){
                    userRespDTOList = toDoUserMap.get(disposePostSaveVO.getPostCode());
                }
                userRespDTOList.addAll(tempList);
                toDoUserMap.put(disposePostSaveVO.getPostCode(),userRespDTOList);
            }else {
                noticeUserList.addAll(tempList);
            }
        }

        MsgAddVO msgAddVO = new MsgAddVO();
        msgAddVO.setBusinessId(eventDO.getId());

        msgAddVO.setMsgType("108_001");

        Map<String, Object> contentData = new HashMap<>();
        contentData.put("evenCode",eventDO.getEventCode());
        contentData.put("roomName",eventDO.getAreaName());
        contentData.put("time", DateUtil.format(eventDO.getHandleTime(),"yyyy-MM-dd HH:mm:ss"));
        msgAddVO.setContentData(contentData);

        //发送待办
        if(CollectionUtil.isNotEmpty(toDoUserMap)){
            for(Map.Entry<String,List<UserRespDTO>> map:toDoUserMap.entrySet()){
                msgAddVO.setPcid(eventDO.getId()+":"+map.getKey());
                msgAddVO.setUrl("");
                msgAddVO.setModuleCode("ACP_JJKS_SQCZDB");
                msgAddVO.setSpecify(true);
                msgAddVO.setSpecifyReceiveUserList(BeanUtils.toBean(map.getValue(),ReceiveUser.class));
                MsgUtil.sendMsg(msgAddVO);
            }
        }

        //发送通知
        if(CollectionUtil.isNotEmpty(noticeUserList)){
            msgAddVO.setUrl("");
            msgAddVO.setModuleCode("ACP_JJKS_SQTX");
            msgAddVO.setSpecify(true);
            msgAddVO.setSpecifyReceiveUserList(BeanUtils.toBean(noticeUserList,ReceiveUser.class));
            MsgUtil.sendMsg(msgAddVO);
        }

        //根据配置的事件类型中的默认推送岗位信息，创建对应岗位的待办
        List<PrisonEventHandleDO> handleDOList = new ArrayList<>();
        for(PrisonEventDisposePostSaveVO disposePostSaveVO:postInfoList){
            if("0".equals(disposePostSaveVO.getIsBuiltIn())){
                continue;
            }
            PrisonEventHandleDO handleDO = new PrisonEventHandleDO();
            handleDO.setId(StringUtil.getGuid32());
            handleDO.setEventId(eventDO.getId());
            handleDO.setStatus("0");
            //这里是设置哪个岗位能处置的
            handleDO.setHandlePostCode(disposePostSaveVO.getPostCode());
            handleDO.setHistory(0);
            handleDO.setHandleType(1);
            handleDOList.add(handleDO);
        }

        //保存
        prisonEventHandleService.saveBatch(handleDOList);
        return toDoUserMap;
    }



    @Override
    public void updatePrisonEvent(PrisonEventSaveReqVO updateReqVO) {
        // 校验存在
        validatePrisonEventExists(updateReqVO.getId());
        // 更新
        PrisonEventDO updateObj = BeanUtils.toBean(updateReqVO, PrisonEventDO.class);
        prisonEventDao.updateById(updateObj);
    }

    @Override
    public void deletePrisonEvent(String id) {
        // 校验存在
        validatePrisonEventExists(id);
        // 删除
        prisonEventDao.deleteById(id);
    }

    private void validatePrisonEventExists(String id) {
        if (prisonEventDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-所情登记数据不存在");
        }
    }

    @Override
    public PrisonEventDO getPrisonEvent(String id) {
        return prisonEventDao.selectById(id);
    }

    @Override
    public PageResult<PrisonEventDO> getPrisonEventPage(PrisonEventPageReqVO pageReqVO) {
        return prisonEventDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrisonEventDO> getPrisonEventList(PrisonEventListReqVO listReqVO) {
        return prisonEventDao.selectList(listReqVO);
    }

    @Override
    public PageResult<PrisonEventPersonRespVO> getOutsiderPersonPage(PrisonEventPersonPageReqVO pageReqVO) {
        pageReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        Page<PrisonEventPersonRespVO> page = new Page<>(pageReqVO.getPageNo(),pageReqVO.getPageSize());
        IPage<PrisonEventPersonRespVO> iPage = prisonEventDao.getOutsiderPersonPage(page,pageReqVO);
        PageResult<PrisonEventPersonRespVO> res = new PageResult<>();
        return new PageResult<>(iPage.getRecords(),iPage.getTotal());
    }

    @Override
    public List<PrisonEventDisposePostSaveVO> getAllPostCode(String eventTypeId) {

        List<PrisonEventDisposePostSaveVO> resList = new ArrayList<>();

        Map<Object, Object> dicMap = bspSdk.getDic(null,"ZD_POST");

        if(CollectionUtil.isEmpty(dicMap)){
            return resList;
        }

        LambdaQueryWrapper<PrisonEventPushSettingDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(PrisonEventPushSettingDO::getPushPostId)
                .eq(PrisonEventPushSettingDO::getTypeId,eventTypeId)
                .eq(PrisonEventPushSettingDO::getOrgCode,SessionUserUtil.getSessionUser().getOrgCode());
        List<PrisonEventPushSettingDO> prisonEventPushSettingDOList = prisonEventPushSettingService.list(lambdaQueryWrapper);

        Map<String,String> postBuiltinMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(prisonEventPushSettingDOList)){
            prisonEventPushSettingDOList.forEach(x->{postBuiltinMap.put(x.getPushPostId(),x.getPushPostId());});
        }

        for(Map.Entry<Object,Object> map:dicMap.entrySet()){
            PrisonEventDisposePostSaveVO saveVO = new PrisonEventDisposePostSaveVO();
            saveVO.setPostCode(String.valueOf(map.getKey()));
            saveVO.setPostName(String.valueOf(map.getValue()));
            if(postBuiltinMap.containsKey(String.valueOf(map.getKey()))){
                saveVO.setIsBuiltIn("1");
            }else {
                saveVO.setIsBuiltIn("0");
            }
            resList.add(saveVO);
        }
        return resList;
    }

    @Override
    public PrisonEventRespVO getPrisonEventById(String eventId) {

        PrisonEventDO eventDO = prisonEventDao.selectById(eventId);
        PrisonEventRespVO respVO = BeanUtils.toBean(eventDO,PrisonEventRespVO.class);

        //工作人员
        if(ObjectUtil.isNotEmpty(eventDO.getPoliceSfzh())){
            Set<String> idCards = Arrays.asList(eventDO.getPoliceSfzh().split(",")).stream().collect(Collectors.toSet());
            List<UserRespDTO>  userRespDTOList = userApi.getUserPhotoByIdCard(idCards);
            List<PrisonEventPersonRespVO> workPrisonerList = new ArrayList<>();
            for(UserRespDTO userRespDTO:userRespDTOList){
                PrisonEventPersonRespVO personRespVO = new PrisonEventPersonRespVO();
                personRespVO.setId(userRespDTO.getId());
                personRespVO.setName(userRespDTO.getName());
                personRespVO.setZjhm(userRespDTO.getIdCard());
                personRespVO.setZpUrl(userRespDTO.getPhoto());
                workPrisonerList.add(personRespVO);
            }
            respVO.setWorkPrisonerList(workPrisonerList);
        }
        //外来人员
        if(ObjectUtil.isNotEmpty(eventDO.getOutsider())){
            List<String> idList = Arrays.asList(eventDO.getOutsider().split(","));
            List<PrisonEventPersonRespVO> outsiderPrisonerList = prisonEventDao.getOutsiderPersonByIds(idList,SessionUserUtil.getSessionUser().getOrgCode());
            respVO.setOutsiderPrisonerList(outsiderPrisonerList);
        }

        //在押人员
        List<PrisonEventPrisonerRespVO> prisonerRespVOList = prisonEventPrisonerService.getPrisonEventPrisonerListByEventId(eventId);
        if(CollectionUtil.isNotEmpty(prisonerRespVOList)){
            for(PrisonEventPrisonerRespVO prisonerRespVO:prisonerRespVOList){
                PrisonerVwRespVO prisonerVwRespVO = prisonerService.getPrisonerByJgrybm(prisonerRespVO.getJgrybm());
                if(ObjectUtil.isNotEmpty(prisonerVwRespVO)){
                    prisonerRespVO.setZpUrl(prisonerVwRespVO.getFrontPhoto());
                }
            }
        }
        respVO.setInPrisonerList(prisonerRespVOList);

        List<PrisonEventHandleRespVO> handleRespVOList = prisonEventHandleService.getNewPrisonEventHandleListByEventId(respVO.getId());
        //处理阶段
        handerStage(respVO,handleRespVOList);
        //轨迹
        handerlTrajectory(respVO,handleRespVOList);
        return respVO;

    }

    /**
     * 处理阶段
     * @param respVO
     * @param handleRespVOList
     */
    private void handerStage(PrisonEventRespVO respVO,List<PrisonEventHandleRespVO> handleRespVOList){
        List<JSONObject> handerStageList = new ArrayList<>();
        if("0".equals(respVO.getStatus())){
            handerStageList.add(defaultStartNode(false));
            handerStageList.add(defaultEndNode(false));
        }else if("1".equals(respVO.getStatus()) || "2".equals(respVO.getStatus())){
            handerStageList.add(defaultStartNode(true));
            handerStageList.addAll(middleNode(handleRespVOList));
            handerStageList.add(defaultEndNode(false));
        }else if("3".equals(respVO.getStatus())){
            handerStageList.add(defaultStartNode(true));
            handerStageList.addAll(middleNode(handleRespVOList));
            handerStageList.add(defaultEndNode(true));
        }else if("99".equals(respVO.getStatus())){
            handerStageList.add(defaultStartNode(true));
        }
        respVO.setHanderStageList(handerStageList);

    }
    private JSONObject defaultStartNode(boolean isFinish){
        JSONObject startStage = new JSONObject();
        startStage.put("postName","巡控岗");
        if(isFinish){
            startStage.put("disposalStatus","1");
            startStage.put("disposalStatusName","已办结");
        }else {
            startStage.put("disposalStatus","0");
            startStage.put("disposalStatusName","未办结");
        }
        return startStage;
    }

    private JSONObject defaultEndNode(boolean isFinish){
        JSONObject startStage = new JSONObject();
        startStage.put("postName","所领导岗");
        if(isFinish){
            startStage.put("disposalStatus","1");
            startStage.put("disposalStatusName","已办结");
        }else {
            startStage.put("disposalStatus","0");
            startStage.put("disposalStatusName","未办结");
        }
        return startStage;
    }

    private List<JSONObject> middleNode( List<PrisonEventHandleRespVO> handleRespVOList){

        List<JSONObject> nodeList = new ArrayList<>();
        for(PrisonEventHandleRespVO handleRespVO:handleRespVOList){
            if(handleRespVO.getHandleType() == 1 && handleRespVO.getHistory() == 0){
                JSONObject startStage = new JSONObject();
                startStage.put("postName", DicUtil.translate("ZD_POST",handleRespVO.getHandlePostCode()));
                if("1".equals(handleRespVO.getStatus()) || "3".equals(handleRespVO.getStatus())){
                    startStage.put("disposalStatus","1");
                    startStage.put("disposalStatusName","已办结");
                }else {
                    startStage.put("disposalStatus","0");
                    startStage.put("disposalStatusName","未办结");
                }
                nodeList.add(startStage);
            }
        }

        return nodeList;
    }

    /**
     * 轨迹
     * @param respVO
     * @param handleRespVOList
     */
    private void handerlTrajectory(PrisonEventRespVO respVO,List<PrisonEventHandleRespVO> handleRespVOList){
        List<JSONObject> resList = new ArrayList<>();
        for(PrisonEventHandleRespVO handleRespVO:handleRespVOList){
            if(handleRespVO.getHistory() == 0 && "0".equals(handleRespVO.getStatus())){
                continue;
            }
            JSONObject nodeJson = JSONObject.parseObject(JSON.toJSONString(handleRespVO));
            nodeJson.put("handlePostName",DicUtil.translate("ZD_POST",handleRespVO.getHandlePostCode()));

            if(handleRespVO.getHandleType() == 0){
                nodeJson.put("eventDetails",respVO.getEventDetails());
            }

            if(handleRespVO.getHandleType() == 1){
                //查找业务
                LambdaQueryWrapper<PrisonEventBusinessDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.select(PrisonEventBusinessDO::getBusinessType,PrisonEventBusinessDO::getBusinessObject,
                        PrisonEventBusinessDO::getBusinessObjectId,PrisonEventBusinessDO::getBusinessId,
                        PrisonEventBusinessDO::getEventId,PrisonEventBusinessDO::getStatus,PrisonEventBusinessDO::getOperatePerson,
                        PrisonEventBusinessDO::getOperateTime,PrisonEventBusinessDO::getHandleId,PrisonEventBusinessDO::getHandleMethod);
                lambdaQueryWrapper.eq(PrisonEventBusinessDO::getHandleId,handleRespVO.getId());
                List<PrisonEventBusinessDO> businessDOList = prisonEventBusinessService.list(lambdaQueryWrapper);
                //根据被监管人员，分组
                Map<String,List<PrisonEventBusinessDO>> personMap = new HashMap<>();
                Map<String,String> personNameMap = new HashMap<>();
                for(PrisonEventBusinessDO businessDO:businessDOList){
                    List<PrisonEventBusinessDO> tempList = new ArrayList<>();
                    if(personMap.containsKey(businessDO.getBusinessObjectId())){
                        tempList = personMap.get(businessDO.getBusinessObjectId());
                    }
                    tempList.add(businessDO);
                    personMap.put(businessDO.getBusinessObjectId(),tempList);
                    personNameMap.put(businessDO.getBusinessObjectId(),businessDO.getBusinessObject());
                }
                List<JSONObject> businessList = new ArrayList<>();
                for(Map.Entry<String,List<PrisonEventBusinessDO>> map:personMap.entrySet()){
                    JSONObject bJson = new JSONObject();
                    bJson.put("businessObject",personNameMap.get(map.getKey()));
                    bJson.put("businessObjectId",map.getKey());
                    List<JSONObject> tempList = new ArrayList<>();
                    for(PrisonEventBusinessDO businessDO:map.getValue()){
                        JSONObject temp = JSONObject.parseObject(JSON.toJSONString(businessDO));
                        temp.put("businessTypeName",DicUtil.translate("ZD_JJKS_GLYW",businessDO.getBusinessType()));
                        tempList.add(temp);
                    }
                    bJson.put("businessList",tempList);
                    businessList.add(bJson);
                }
                nodeJson.put("handleList",businessList);
            }

            resList.add(nodeJson);
        }
        respVO.setHanderTrajectory(resList);
    }

    @Override
    public PrisonEventHandleRespVO getCurrentNodeByPostCode(String eventId) {
        PrisonEventDO eventDO = prisonEventDao.selectById(eventId);
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        if(ObjectUtil.isEmpty(sessionUser.getPost())){
            return null;
        }

        List<String> postList = Arrays.asList(sessionUser.getPost().split(","));
        Map<String,String> postMap = new HashMap<>();
        postList.forEach(x->{postMap.put(x,x);});

        //判断下当前环节
        //如果用户有多个岗位，并且都符合待处置的岗位，则返回待处置的第一条
        if("0".equals(eventDO.getStatus())){
            return null;
        }
        PrisonEventHandleDO prisonEventHandleDO = new PrisonEventHandleDO();
        LambdaQueryWrapper<PrisonEventHandleDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PrisonEventHandleDO::getEventId,eventId).eq(PrisonEventHandleDO::getStatus,"0")
                .eq(PrisonEventHandleDO::getHistory,0);
        if("1".equals(eventDO.getStatus())){
            lambdaQueryWrapper.eq(PrisonEventHandleDO::getHandleType,1);
        }

        if("2".equals(eventDO.getStatus())){
            lambdaQueryWrapper.eq(PrisonEventHandleDO::getHandleType,2);
        }
        lambdaQueryWrapper.orderByAsc(PrisonEventHandleDO::getAddTime);
        List<PrisonEventHandleDO> handleDOList = prisonEventHandleService.list(lambdaQueryWrapper);
        if(CollectionUtil.isNotEmpty(handleDOList)){
            for(PrisonEventHandleDO eventHandleDO:handleDOList){
                if(postMap.containsKey(eventHandleDO.getHandlePostCode())){
                    prisonEventHandleDO = eventHandleDO;
                    break;
                }
            }
        }
        if(ObjectUtil.isEmpty(prisonEventHandleDO)){
            return null;
        }
        PrisonEventHandleRespVO respVO = BeanUtils.toBean(prisonEventHandleDO,PrisonEventHandleRespVO.class);
        if("1".equals(eventDO.getStatus())){
            LambdaQueryWrapper<PrisonEventPushSettingDO> settingDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            settingDOLambdaQueryWrapper.select(PrisonEventPushSettingDO::getDisposePlans,
                            PrisonEventPushSettingDO::getDisposeBusiness)
                    .eq(PrisonEventPushSettingDO::getTypeId,eventDO.getEventRootTypeId())
                    .eq(PrisonEventPushSettingDO::getPushPostId,prisonEventHandleDO.getHandlePostCode())
                    .eq(PrisonEventPushSettingDO::getOrgCode,SessionUserUtil.getSessionUser().getOrgCode());
            List<PrisonEventPushSettingDO> settingDOList = prisonEventPushSettingService.list(settingDOLambdaQueryWrapper);
            if(CollectionUtil.isNotEmpty(settingDOList)){
                respVO.setDisposePlans(settingDOList.get(0).getDisposePlans());
                respVO.setDisposeBusiness(settingDOList.get(0).getDisposeBusiness());
            }
        }

        return respVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean noNeeddisposal(String eventId) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        PrisonEventDO eventDO = prisonEventDao.selectById(eventId);
        eventDO.setStatus("99");
        eventDO.setHandleTime(new Date());
        eventDO.setHandleUserName(sessionUser.getName());
        eventDO.setHandleUserSfzh(sessionUser.getIdCard());

        LambdaUpdateWrapper<PrisonEventHandleDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(PrisonEventHandleDO::getStatus,"1").set(PrisonEventHandleDO::getHandleUserSfzh,sessionUser.getIdCard())
                .set(PrisonEventHandleDO::getHandleUserName,sessionUser.getName()).set(PrisonEventHandleDO::getHandleTime,new Date())
                .set(PrisonEventHandleDO::getHandlePost,sessionUser.getPost())
                .eq(PrisonEventHandleDO::getEventId,eventDO.getId()).eq(PrisonEventHandleDO::getHistory,0)
                .eq(PrisonEventHandleDO::getHandleType,0);
        prisonEventHandleService.update(updateWrapper);

        return updateById(eventDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean prisonEventDispose(PrisonEventHandleSaveReqVO createReqVO) {

        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        PrisonEventHandleDO prisonEventHandleDO = prisonEventHandleService.getById(createReqVO.getId());

        prisonEventHandleDO.setFeedbackInfo(createReqVO.getFeedbackInfo());
        prisonEventHandleDO.setHandlePost(sessionUser.getPost());

        //综合岗有几个特殊的字段需要保存
        if("1".equals(prisonEventHandleDO.getHandleType())){
            prisonEventHandleDO.setReportSupervise(createReqVO.getReportSupervise());
            prisonEventHandleDO.setReportLevelPolice(createReqVO.getReportLevelPolice());
            prisonEventHandleDO.setReportProcuratorialOrgans(createReqVO.getReportProcuratorialOrgans());
        }

        if(CollectionUtil.isNotEmpty(createReqVO.getBusinessList())){
           prisonEventBusinessService.savePrisonEventBusinessList(createReqVO.getBusinessList(),createReqVO.getEventId(),createReqVO.getId());
        }

        if("0".equals(createReqVO.getSaveType())){
            prisonEventHandleDO.setHistory(1);
            prisonEventHandleDO.setStatus("0");
            //更新旧的操作记录，保存一条新的
            PrisonEventHandleDO handleDO = new PrisonEventHandleDO();
            handleDO.setId(StringUtil.getGuid32());
            handleDO.setEventId(createReqVO.getEventId());
            handleDO.setStatus("0");
            handleDO.setHandlePostCode(prisonEventHandleDO.getHandlePostCode());
            handleDO.setHistory(0);
            handleDO.setHandleType(prisonEventHandleDO.getHandleType());
            prisonEventHandleService.save(handleDO);
        }else {
            prisonEventHandleDO.setHistory(0);
            prisonEventHandleDO.setStatus("1");

            //消除本岗位对应的待办
            SendMessageUtil.ProcessTodoMsg(createReqVO.getEventId()+":"+prisonEventHandleDO.getHandlePostCode(),sessionUser.getIdCard(),"pc");

            //如果是提交类型，这里查询，是否有其他平行节点没有完成
            LambdaQueryWrapper<PrisonEventHandleDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(PrisonEventHandleDO::getEventId,createReqVO.getEventId()).eq(PrisonEventHandleDO::getStatus,"0")
                    .eq(PrisonEventHandleDO::getHistory,0).eq(PrisonEventHandleDO::getHandleType,1)
                    .notIn(PrisonEventHandleDO::getId,Arrays.asList(createReqVO.getId()));

            if(prisonEventHandleService.count(lambdaQueryWrapper) == 0){
                //表示没有平行节点了，这里直接创建最终的审批节点
                PrisonEventHandleDO handleDO = new PrisonEventHandleDO();
                handleDO.setId(StringUtil.getGuid32());
                handleDO.setEventId(createReqVO.getEventId());
                handleDO.setStatus("0");
                handleDO.setHandlePostCode("06");
                handleDO.setHistory(0);
                handleDO.setHandleType(2);

                //构建待审批数据
                //如果是退回重新审批的，则只需审批对应的岗位就行
                PrisonEventHandleApproveSaveReqVO approveSaveReqVO = new PrisonEventHandleApproveSaveReqVO();
                approveSaveReqVO.setId(handleDO.getId());
                approveSaveReqVO.setEventId(handleDO.getEventId());

                LambdaQueryWrapper<PrisonEventHandleDO> prisonEventHandleDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                prisonEventHandleDOLambdaQueryWrapper.select(PrisonEventHandleDO::getId,PrisonEventHandleDO::getHandlePostCode,
                        PrisonEventHandleDO::getStatus,PrisonEventHandleDO::getHandleInfo);
                prisonEventHandleDOLambdaQueryWrapper.eq(PrisonEventHandleDO::getEventId,handleDO.getEventId())
                                .eq(PrisonEventHandleDO::getHandleType,1).eq(PrisonEventHandleDO::getHistory,0);
                List<PrisonEventHandleDO> newList = prisonEventHandleService.list(prisonEventHandleDOLambdaQueryWrapper);
                List<PrisonEventHandleApproveChildSaveReqVO> approveResultList = new ArrayList<>();

                for(PrisonEventHandleDO temp:newList){
                    PrisonEventHandleApproveChildSaveReqVO childSaveReqVO = new PrisonEventHandleApproveChildSaveReqVO();
                    childSaveReqVO.setHandleId(temp.getId());
                    childSaveReqVO.setHandlePostCode(temp.getHandlePostCode());
                    childSaveReqVO.setHandlePostName(DicUtil.translate("ZD_POST",temp.getHandlePostCode()));
//                    if("3".equals(temp.getStatus())){
//                        childSaveReqVO.setIsApprove("1");
//                        childSaveReqVO.setApprovalResult("1");
//                        childSaveReqVO.setApprovalComments(temp.getHandleInfo());
//                    }else {
//                        childSaveReqVO.setIsApprove("0");
//                    }
                    approveResultList.add(childSaveReqVO);
                }
                approveSaveReqVO.setApproveResultList(approveResultList);
                handleDO.setHandleInfo(JSON.toJSONString(approveSaveReqVO));
                prisonEventHandleService.save(handleDO);

                //这里也要更新所情事件的状态
                PrisonEventDO eventDO = prisonEventDao.selectById(createReqVO.getEventId());
                eventDO.setStatus("2");
                prisonEventDao.updateById(eventDO);

                MsgAddVO msgAddVO = new MsgAddVO();
                msgAddVO.setBusinessId(eventDO.getId());
                msgAddVO.setPcid(eventDO.getId()+":"+handleDO.getHandlePostCode());
                msgAddVO.setMsgType("108_001");
                msgAddVO.setUrl("");
                msgAddVO.setModuleCode("ACP_JJKS_SQSHDB");

                Map<String, Object> contentData = new HashMap<>();
                contentData.put("evenCode",eventDO.getEventCode());
                contentData.put("roomName",eventDO.getAreaName());
                contentData.put("time", DateUtil.format(eventDO.getHandleTime(),"yyyy-MM-dd HH:mm:ss"));
                msgAddVO.setContentData(contentData);
                List<UserRespDTO> userRespDTOList = userApi.getUserByOrgAndPost(sessionUser.getOrgCode(),handleDO.getHandlePostCode());
                //发送待办
                if(CollectionUtil.isNotEmpty(userRespDTOList)){
                    msgAddVO.setSpecify(true);
                    msgAddVO.setSpecifyReceiveUserList(BeanUtils.toBean(userRespDTOList,ReceiveUser.class));
                    MsgUtil.sendMsg(msgAddVO);
                }
            }
        }
        return prisonEventHandleService.updateById(prisonEventHandleDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean prisonEventApprove(PrisonEventHandleApproveSaveReqVO createReqVO) {

        boolean isAllPass = true;
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        PrisonEventDO eventDO = prisonEventDao.selectById(createReqVO.getEventId());
        PrisonEventHandleDO prisonEventHandleDO = prisonEventHandleService.getById(createReqVO.getId());
        prisonEventHandleDO.setHandleUserSfzh(sessionUser.getIdCard());
        prisonEventHandleDO.setHandleUserName(sessionUser.getName());
        prisonEventHandleDO.setHandleTime(new Date());
        prisonEventHandleDO.setHandleInfo(JSON.toJSONString(createReqVO.getApproveResultList()));
        List<PrisonEventHandleApproveChildSaveReqVO> approveResultList = createReqVO.getApproveResultList();
        //如果有审批不通过的，整体就审批不通过，只是上一环节点节点，只需要处理不通过的就行
        List<String> idList = new ArrayList<>();
        for(PrisonEventHandleApproveChildSaveReqVO childSaveReqVO:approveResultList){
            PrisonEventHandleDO dbEventHandle = prisonEventHandleService.getById(childSaveReqVO.getHandleId());
            if("0".equals(childSaveReqVO.getApprovalResult())){
                isAllPass = false;
                //不同意
                dbEventHandle.setStatus("2");
                dbEventHandle.setHistory(1);

                //重新给该岗位创建一个新的待所情处置
                PrisonEventHandleDO handleDO = new PrisonEventHandleDO();
                handleDO.setId(StringUtil.getGuid32());
                handleDO.setEventId(createReqVO.getEventId());
                handleDO.setStatus("0");
                //这里是设置哪个岗位能处置的
                handleDO.setHandlePostCode(dbEventHandle.getHandlePostCode());
                handleDO.setHistory(0);
                handleDO.setHandleType(1);
                prisonEventHandleService.save(handleDO);

                //发送待办
                OrgUserRespDTO orgUserRespDTO =  bspApi.getUserByIdCard(dbEventHandle.getHandleUserSfzh());

                MsgAddVO msgAddVO = new MsgAddVO();
                msgAddVO.setBusinessId(createReqVO.getEventId());
                msgAddVO.setPcid(createReqVO.getEventId()+":"+handleDO.getHandlePostCode());
                msgAddVO.setMsgType("108_001");
                msgAddVO.setUrl("");
                msgAddVO.setModuleCode("ACP_JJKS_SQSHDB");

                Map<String, Object> contentData = new HashMap<>();
                contentData.put("evenCode",eventDO.getEventCode());
                contentData.put("roomName",eventDO.getAreaName());
                contentData.put("time", DateUtil.format(eventDO.getHandleTime(),"yyyy-MM-dd HH:mm:ss"));
                msgAddVO.setContentData(contentData);
                //发送待办
                if(ObjectUtil.isNotEmpty(orgUserRespDTO)){
                    msgAddVO.setSpecify(true);
                    msgAddVO.setSpecifyReceiveUserList(BeanUtils.toBean(Arrays.asList(orgUserRespDTO),ReceiveUser.class));
                    MsgUtil.sendMsg(msgAddVO);
                }
                dbEventHandle.setHandleInfo(childSaveReqVO.getApprovalComments());
                prisonEventHandleService.updateById(dbEventHandle);
            }
            idList.add(childSaveReqVO.getHandleId());
        }

        prisonEventHandleDO.setStatus("2");
        if(!isAllPass){
            prisonEventHandleDO.setHistory(1);
            eventDO.setStatus("1");
        }else {
            LambdaUpdateWrapper<PrisonEventHandleDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(PrisonEventHandleDO::getStatus,"3").in(PrisonEventHandleDO::getId,idList);
            prisonEventHandleService.update(updateWrapper);
            eventDO.setStatus("3");
        }
        prisonEventDao.updateById(eventDO);
        return prisonEventHandleService.updateById(prisonEventHandleDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean savePrisonEventApi(PrisonEventTerminalWarningSaveReqVO saveReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        PrisonEventDO prisonEvent = BeanUtils.toBean(saveReqVO, PrisonEventDO.class);
        prisonEvent.setId(StringUtil.getGuid32());
        prisonEvent.setStatus("0");
        prisonEvent.setEventCode(bspApi.executeByRuleCode(prisonEventNo,null));

        //如果是客户端模式，可能获取不到用户信息
        if(ObjectUtil.isEmpty(sessionUser)){
            prisonEvent.setAddUser("无");
            prisonEvent.setAddUserName("系统自动生成");
            OrgRespDTO orgRespDTO = bspApi.getOrgByCode(prisonEvent.getOrgCode());
            prisonEvent.setOrgName(orgRespDTO.getName());
        }

        //创建巡控待办
        PrisonEventHandleDO handleDO = new PrisonEventHandleDO();
        handleDO.setId(StringUtil.getGuid32());
        handleDO.setEventId(prisonEvent.getId());
        handleDO.setStatus("0");
        handleDO.setHandlePostCode("02");
        handleDO.setHistory(0);
        handleDO.setHandleType(0);
        prisonEventHandleService.save(handleDO);

        save(prisonEvent);

        //wbscoke 通知前端
        sendMsgToWeb(prisonEvent,new HashMap<>());
        return true;
    }

    /**
     * websocke消息发送
     * @param eventDO
     * @param tsUserMap
     */
    private void sendMsgToWeb(PrisonEventDO eventDO,Map<String,List<UserRespDTO>> tsUserMap){
        //查询对应的所情联动配置，根据所情阶段，通过wbsocket 发送消息到对应的用户

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        PrisonEventSettingDO settingDO = prisonEventSettingService.getById(eventDO.getEventSrc());

        //判断是否需要发送
        if(!eventDO.getEventLevel().equals(settingDO.getEventLevel())){
            return;
        }

        JSONObject msg = new JSONObject();
        msg.put("eventId",eventDO.getId());
        msg.put("eventLevel",eventDO.getEventLevel());
        msg.put("happenTime",DateUtil.format(eventDO.getHappenTime(),"yyyy-MM-dd HH:mm:ss"));
        msg.put("areaName",eventDO.getAreaName());
        msg.put("eventDetails",eventDO.getEventDetails());

        if("0".equals(eventDO.getStatus())){
            List<UserRespDTO> userList = new ArrayList<>();
            if(tsUserMap.containsKey("02")){
                userList = tsUserMap.get("02");
            }else {
                //推送到巡控岗
                userList = userApi.getUserByOrgAndPost(ObjectUtil.isNotEmpty(sessionUser)?sessionUser.getOrgCode():eventDO.getOrgCode(),"02");
            }
            msg.put("postCode","02");
            send(msg,userList);
        }

        if("1".equals(eventDO.getStatus())){
            for(Map.Entry<String,List<UserRespDTO>> map:tsUserMap.entrySet()){
                msg.put("postCode",map.getKey());
                send(msg,map.getValue());
            }
        }
        if("2".equals(eventDO.getStatus())){
            List<UserRespDTO> userList = new ArrayList<>();
            if(tsUserMap.containsKey("06")){
                userList = tsUserMap.get("02");
            }else {
                //推送到巡控岗
                userList = userApi.getUserByOrgAndPost(ObjectUtil.isNotEmpty(sessionUser)?sessionUser.getOrgCode():eventDO.getOrgCode(),"06");
            }
            send(msg,userList);
        }
    }

    private void send(JSONObject msg,List<UserRespDTO> userList){
        for(UserRespDTO userRespDTO:userList){
            try {
                WebSocketServer.sendInfo(msg.toJSONString(),userRespDTO.getId());
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }
}
