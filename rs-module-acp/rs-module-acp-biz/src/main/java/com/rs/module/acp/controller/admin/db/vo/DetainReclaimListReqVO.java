package com.rs.module.acp.controller.admin.db.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-羁押业务-收回登记列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DetainReclaimListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("入所时间")
    private Date rssj;

    @ApiModelProperty("入所原因")
    private String rsyy;

    @ApiModelProperty("法律文书号")
    private String flwsh;

    @ApiModelProperty("收回原因")
    private String shyy;

    @ApiModelProperty("收回日期")
    private Date shrq;

    @ApiModelProperty("经办人身份证号")
    private String jbrsfzh;

    @ApiModelProperty("经办人")
    private String jbr;

    @ApiModelProperty("经办时间")
    private String jbsj;

    @ApiModelProperty("登记状态")
    private String status;

}
