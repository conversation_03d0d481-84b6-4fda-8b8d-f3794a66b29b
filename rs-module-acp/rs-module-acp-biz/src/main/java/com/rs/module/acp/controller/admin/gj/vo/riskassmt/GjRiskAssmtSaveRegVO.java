package com.rs.module.acp.controller.admin.gj.vo.riskassmt;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@Data
@EqualsAndHashCode(callSuper = true)
public class GjRiskAssmtSaveRegVO extends BaseVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("评估类型")
    @NotEmpty(message = "评估类型不能为空")
    private String riskType;

    @ApiModelProperty("原风险等级")
    private String oldRiskLevel;

    @ApiModelProperty("看守所-评估风险等级")
    //@NotEmpty(message = "评估风险等级不能为空")
    private String riskLevel;

    @ApiModelProperty("拘留所- 严管类别 字典 ZD_GJ_FXPG_STRICTLY ")
    //@NotEmpty(message = "严管类别 等级不能为空")
    private String strictlyRegulatedCategory;

    /**
     * 严管类别  字典 ZD_GJ_FXPG_STRICTLY
     */
    @ApiModelProperty("评估理由 字典: 看守所 GJ_FXPG_KSS_REASON 拘留所 GJ_FXPG_JLS_REASON , ")
    @NotEmpty(message = "评估理由不能为空")
    private String assmtReason;

    @ApiModelProperty("具体评估理由")
    private String specificAssmtReason;

    @ApiModelProperty("待评估风险ID")
    private String riskAssmtTodoId;


}
