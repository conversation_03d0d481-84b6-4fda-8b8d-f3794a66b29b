package com.rs.module.acp.entity.gj.conflict;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 实战平台-管教业务-社会矛盾化解登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_conflict_reg")
@KeySequence("acp_gj_conflict_reg_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConflictRegDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 数据来源：1:实战平台，2：仓外屏, 3：仓内屏
     */
    private String dataSources;
    /**
     * 发起人（通过数据来源区分，民警发起记录民警身份证号，被拘留人员记录：）
     */
    private String initiator;
    /**
     * 发起人姓名（记录发起人，通过数据来源区分是民警发起还是被拘留人员发起）
     */
    private String initiatorXm;
    /**
     * 流程实例ID
     */
    private String actInstId;
    /**
     * 事件编号
     */
    private String eventCode;
    /**
     * 事件名称
     */
    private String eventName;
    /**
     * 矛盾等级（01：普通矛盾，02：重大、疑难矛盾）
     */
    private String conflictLv;
    /**
     * 调解方式（01：单场调解，02：个别协商，03：庭式调解）
     */
    private String mediationMode;
    /**
     * 涉案金额
     */
    private BigDecimal involvedAmt;
    /**
     * 矛盾类别（01：非法上访、02：经济纠纷（补偿赔偿债务）、03：家庭矛盾、04：青少年违法、05：民间打架斗殴、06：民事纠纷、07：非法维权、08：对执法机关不满案件、09：迷信活动、10：“六失一偏”人员、11：其他类
     */
    private String conflictType;
    /**
     * 当事人或单位基本情况
     */
    private String partiesBasicInfo;
    /**
     * 矛盾基本情况
     */
    private String conflictBasicInfo;
    /**
     * 调解时间
     */
    private Date mediationTime;
    /**
     * 调解民警身份证号
     */
    private String mediationPoliceSfzh;
    /**
     * 调解民警姓名
     */
    private String mediationPoliceXm;
    /**
     * 状态（01:已登记、02：待审批（所领导审批）、03: 审批不通过、04：调解中、05：调解完成、06：待确认、07：被退回）
     */
    private String regStatus;
    /**
     * 确认状态（0：待确认、1：已确认）
     */
    private String confirmStatus;
    /**
     * 终端确认经办人
     */
    private String confirmOperatorSfzh;
    /**
     * 终端确认经办人姓名
     */
    private String confirmOperatorXm;
    /**
     * 终端确认经办时间
     */
    private Date confirmOperatorTime;
    /**
     * 登记经办人
     */
    private String regOperatorSfzh;
    /**
     * 登记经办人姓名
     */
    private String regOperatorXm;
    /**
     * 审批人身份证号
     */
    private String approverSfzh;
    /**
     * 审批人姓名
     */
    private String approverXm;
    /**
     * 审批时间
     */
    private Date approverTime;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 审核意见
     */
    private String approvalComments;
    /**
     * 是否已评价（0：未评价，1：已评价）
     */
    private Integer isRated;
    /**
     * 满意度评分（1：满意：0：不满意）
     */
    private Integer satisfactionScore;

}
