package com.rs.module.acp.service.pm;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.base.dao.pm.CusAppDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.CusAppUserDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pm.CusAppUserDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-监管管理-我的应用配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CusAppUserServiceImpl extends BaseServiceImpl<CusAppUserDao, CusAppUserDO> implements CusAppUserService {

    @Resource
    private CusAppUserDao cusAppUserDao;

    @Resource
    private CusAppDao cusAppDao;

    @Override
    public void createCusAppUser(String ids) {

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        //删除应用
        //cusAppUserDao.delete(Wrappers.lambdaQuery(CusAppUserDO.class).eq(CusAppUserDO::getUserIdCard, sessionUser.getIdCard()));
        // 物理删除
        cusAppDao.deleteCusAppUserByUserIdCard(sessionUser.getIdCard());

        if (StringUtils.isNotEmpty(ids)) {
            List<String> list = Arrays.asList(ids.split(","));
            List<CusAppUserDO> collect = list.stream().map(id -> {
                CusAppUserDO cusAppUserDO = new CusAppUserDO();
                cusAppUserDO.setSystemId(id);
                cusAppUserDO.setUserIdCard(sessionUser.getIdCard());
                return cusAppUserDO;
            }).collect(Collectors.toList());

            cusAppUserDao.insertBatch(collect);
        }

    }

    @Override
    public void updateCusAppUser(CusAppUserSaveReqVO updateReqVO) {
        // 校验存在
        validateCusAppUserExists(updateReqVO.getId());
        // 更新
        CusAppUserDO updateObj = BeanUtils.toBean(updateReqVO, CusAppUserDO.class);
        cusAppUserDao.updateById(updateObj);
    }

    @Override
    public void deleteCusAppUser(String id) {
        // 校验存在
        validateCusAppUserExists(id);
        // 删除
        cusAppUserDao.deleteById(id);
    }

    private void validateCusAppUserExists(String id) {
        if (cusAppUserDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-我的应用配置数据不存在");
        }
    }

    @Override
    public CusAppUserDO getCusAppUser(String id) {
        return cusAppUserDao.selectById(id);
    }

    @Override
    public PageResult<CusAppUserDO> getCusAppUserPage(CusAppUserPageReqVO pageReqVO) {
        return cusAppUserDao.selectPage(pageReqVO);
    }

    @Override
    public List<CusAppUserDO> getCusAppUserList(CusAppUserListReqVO listReqVO) {
        return cusAppUserDao.selectList(listReqVO);
    }

    @Override
    public List<Map<String, Object>> getWdyyList(List<String> yyidList, String yylx) {

        return CollectionUtil.isEmpty(yyidList) ? new ArrayList<>() : cusAppDao.getWdyyList(yyidList, yylx);
    }


}
