package com.rs.module.acp.service.gj.edurehabcourses;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.util.SnowflakeIdUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesSaveReqVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.EdurehabCoursesDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.EdurehabCoursesDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-教育康复课程 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EdurehabCoursesServiceImpl extends BaseServiceImpl<EdurehabCoursesDao, EdurehabCoursesDO> implements EdurehabCoursesService {

    @Resource
    private EdurehabCoursesDao edurehabCoursesDao;

    @Override
    public String createEdurehabCourses(EdurehabCoursesSaveReqVO createReqVO) {
        // 插入
        LambdaQueryWrapper<EdurehabCoursesDO> wrapper = Wrappers.lambdaQuery(EdurehabCoursesDO.class)
                .eq(EdurehabCoursesDO::getCoursesName, createReqVO.getCoursesName())
                .eq(EdurehabCoursesDO::getOrgCode, SessionUserUtil.getSessionUser().getOrgCode());
        Integer count = edurehabCoursesDao.selectCount(wrapper);
        if (Objects.nonNull(count) && count > 0) {
            throw new ServerException("课程不允许重复添加");
        }
        String guid = SnowflakeIdUtil.getGuid();
        createReqVO.setId(guid);
        createReqVO.setCoursesCode(guid);
        EdurehabCoursesDO edurehabCourses = BeanUtils.toBean(createReqVO, EdurehabCoursesDO.class);
        edurehabCoursesDao.insert(edurehabCourses);
        // 返回
        return edurehabCourses.getId();
    }

    @Override
    public void updateEdurehabCourses(EdurehabCoursesSaveReqVO updateReqVO) {
        // 校验存在
        validateEdurehabCoursesExists(updateReqVO.getId());
        // 更新
        EdurehabCoursesDO updateObj = BeanUtils.toBean(updateReqVO, EdurehabCoursesDO.class);
        edurehabCoursesDao.updateById(updateObj);
    }

    @Override
    public void deleteEdurehabCourses(String id) {
        // 校验存在
        validateEdurehabCoursesExists(id);
        // 删除
        edurehabCoursesDao.deleteById(id);
    }

    private void validateEdurehabCoursesExists(String id) {
        if (edurehabCoursesDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-教育康复课程数据不存在");
        }
    }

    @Override
    public EdurehabCoursesDO getEdurehabCourses(String id) {
        return edurehabCoursesDao.selectById(id);
    }

    @Override
    public PageResult<EdurehabCoursesDO> getEdurehabCoursesPage(EdurehabCoursesPageReqVO pageReqVO) {
        return edurehabCoursesDao.selectPage(pageReqVO);
    }

    @Override
    public List<EdurehabCoursesDO> getEdurehabCoursesList(EdurehabCoursesListReqVO listReqVO) {
        return edurehabCoursesDao.selectList(listReqVO);
    }


}
