package com.rs.module.acp.controller.admin.db.vo.jxjsdj;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-减刑登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class JxjsdjGajgscReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("报请公安机关日期")
    private String bqgajgrq;

    @ApiModelProperty("公安机关审查日期")
    private Date gajgscrq;

    @ApiModelProperty("公安机关审查意见（1、同意；2、不同意）")
    private String gajgscyj;

    @ApiModelProperty("公安机关审查备注")
    private String gajgscbz;

    @ApiModelProperty("公安机关审查材料")
    private String gajgsccl;

}
