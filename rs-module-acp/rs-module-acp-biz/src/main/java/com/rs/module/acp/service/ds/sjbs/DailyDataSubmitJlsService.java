package com.rs.module.acp.service.ds.sjbs;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.ds.vo.sjbs.DailyDataSubmitJlsSaveReqVO;
import com.rs.module.acp.entity.ds.sjbs.DailyDataSubmitJlsDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-数据固化-每日数据报送(拘留所) Service 接口
 *
 * <AUTHOR>
 */
public interface DailyDataSubmitJlsService extends IBaseService<DailyDataSubmitJlsDO>{

    /**
     * 创建实战平台-数据固化-每日数据报送(拘留所)
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createDailyDataSubmitJls(@Valid DailyDataSubmitJlsSaveReqVO createReqVO);

    /**
     * 更新实战平台-数据固化-每日数据报送(拘留所)
     *
     * @param updateReqVO 更新信息
     */
    void updateDailyDataSubmitJls(@Valid DailyDataSubmitJlsSaveReqVO updateReqVO);

    /**
     * 删除实战平台-数据固化-每日数据报送(拘留所)
     *
     * @param id 编号
     */
    void deleteDailyDataSubmitJls(String id);

    /**
     * 获得实战平台-数据固化-每日数据报送(拘留所)
     *
     * @param id 编号
     * @return 实战平台-数据固化-每日数据报送(拘留所)
     */
    DailyDataSubmitJlsDO getDailyDataSubmitJls(String id);

    void saveForStatistic(String orgCode,String startDate,String endDate);

    DailyDataSubmitJlsDO getDailyDataSubmitJlsBySolidificationDate(String solidificationDate,String orgCode);
}
