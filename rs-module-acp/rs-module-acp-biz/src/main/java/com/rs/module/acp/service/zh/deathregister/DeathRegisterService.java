package com.rs.module.acp.service.zh.deathregister;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.zh.vo.deathregister.*;
import com.rs.module.acp.entity.zh.DeathRegisterDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-综合管理-死亡登记 Service 接口
 *
 * <AUTHOR>
 */
public interface DeathRegisterService extends IBaseService<DeathRegisterDO>{

    /**
     * 创建实战平台-综合管理-死亡登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createDeathRegister(@Valid DeathRegisterSaveReqVO createReqVO);

    /**
     * 更新实战平台-综合管理-死亡登记
     *
     * @param updateReqVO 更新信息
     */
    void updateDeathRegister(@Valid DeathRegisterSaveReqVO updateReqVO);

    /**
     * 删除实战平台-综合管理-死亡登记
     *
     * @param id 编号
     */
    void deleteDeathRegister(String id);

    /**
     * 获得实战平台-综合管理-死亡登记
     *
     * @param id 编号
     * @return 实战平台-综合管理-死亡登记
     */
    DeathRegisterDO getDeathRegister(String id);

    String deathRegisterApprove(DeathApproveSaveVO approveReqVO) throws  Exception;

    String corpseHandleSave(DeathCorpseHandleSaveVO reqVO) throws  Exception;

    String deathAppraiseSave(DeathAppraiseSaveVO reqVO);

    String deathRegisterSave(DeathRegisterSaveReqVO reqVO);

    DeathRegisterRespVO getDeathRegisterDetailById(String id);
}
