package com.rs.module.acp.dao.ds.sjbs;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.acp.entity.ds.sjbs.WeeklyPrisonerTopDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* 实战平台-数据固化-每周会见被监管人员排名 Dao
*
* <AUTHOR>
*/
@Mapper
public interface WeeklyPrisonerTopDao extends IBaseDao<WeeklyPrisonerTopDO> {
    /**
     * 被拘留人及会见次数最多十人排名
     * @param orgCode 机构编码
     * @param startDate 开始日期(yyyy-MM-dd)
     * @param endDate 结束日期(yyyy-MM-dd)
     * @return 排名结果
     */
    List<WeeklyPrisonerTopDO> selectTop10Jgry(
            @Param("orgCode") String orgCode,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    int deleteByCondition(
            @Param("orgCode") String orgCode,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);
}
