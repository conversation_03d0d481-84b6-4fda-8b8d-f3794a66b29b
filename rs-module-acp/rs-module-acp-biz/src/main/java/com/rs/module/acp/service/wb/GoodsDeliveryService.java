package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.GoodsDeliveryDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-物品顾送登记 Service 接口
 *
 * <AUTHOR>
 */
public interface GoodsDeliveryService extends IBaseService<GoodsDeliveryDO>{

    /**
     * 创建实战平台-窗口业务-物品顾送登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createGoodsDelivery(@Valid GoodsDeliverySaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-物品顾送登记
     *
     * @param updateReqVO 更新信息
     */
    void updateGoodsDelivery(@Valid GoodsDeliverySaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-物品顾送登记
     *
     * @param id 编号
     */
    void deleteGoodsDelivery(String id);

    /**
     * 获得实战平台-窗口业务-物品顾送登记
     *
     * @param id 编号
     * @return 实战平台-窗口业务-物品顾送登记
     */
    GoodsDeliveryDO getGoodsDelivery(String id);

    /**
    * 获得实战平台-窗口业务-物品顾送登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-物品顾送登记分页
    */
    PageResult<GoodsDeliveryDO> getGoodsDeliveryPage(GoodsDeliveryPageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-物品顾送登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-物品顾送登记列表
    */
    List<GoodsDeliveryDO> getGoodsDeliveryList(GoodsDeliveryListReqVO listReqVO);

    /**
     * 实战平台-窗口业务-根据ID获取物品顾送登记
     * @param id
     * @return
     */
    GoodsDeliveryRespVO getGoodsDeliveryById(String id);

    /**
     * 实战平台-窗口业务-签收
     * @param createReqVO
     * @return
     */
    boolean signfor( GoodsDeliverySaveReqVO updateReqVO);
}
