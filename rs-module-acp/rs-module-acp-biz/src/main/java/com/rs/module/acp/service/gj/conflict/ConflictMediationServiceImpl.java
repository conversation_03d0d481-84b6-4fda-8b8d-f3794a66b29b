package com.rs.module.acp.service.gj.conflict;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictMediationOrgRespVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictMediationRespVO;
import com.rs.module.acp.dao.gj.conflict.ConflictMediationDao;
import com.rs.module.acp.entity.gj.conflict.ConflictMediationDO;
import com.rs.module.acp.entity.gj.conflict.ConflictMediationOrgDO;
import com.rs.module.acp.enums.gj.MediationResultEnum;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 实战平台-管教业务-社会矛盾化解 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ConflictMediationServiceImpl extends BaseServiceImpl<ConflictMediationDao, ConflictMediationDO> implements ConflictMediationService {

    @Resource
    private ConflictMediationDao conflictMediationDao;
    @Resource
    private ConflictMediationOrgService conflictMediationOrgService;


    @Override
    public int getMediationCntByEventCode(String eventCode) {
        LambdaQueryWrapper<ConflictMediationDO> query = new LambdaQueryWrapper<>();
        query.select(ConflictMediationDO::getMediationCnt);
        query.eq(ConflictMediationDO::getEventCode, eventCode);
        List<ConflictMediationDO> conflictMediationDOS = conflictMediationDao.selectList(query);
        if (CollUtil.isNotEmpty(conflictMediationDOS)) {
            return conflictMediationDOS.stream().mapToInt(ConflictMediationDO::getMediationCnt).max().orElse(0);
        }
        return 0;
    }

    @Override
    public List<ConflictMediationRespVO> getMediationListByEventCode(String eventCode) {
        List<ConflictMediationRespVO> resultList = new ArrayList<>();
        List<ConflictMediationDO> list = conflictMediationDao.selectList(Wrappers.lambdaQuery(ConflictMediationDO.class)
                .eq(ConflictMediationDO::getEventCode, eventCode)
                .orderByAsc(ConflictMediationDO::getMediationCnt));
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        Set<String> ids = list.stream().map(ConflictMediationDO::getId).collect(Collectors.toSet());
        LambdaQueryWrapper<ConflictMediationOrgDO> wrapper = Wrappers.lambdaQuery(ConflictMediationOrgDO.class);
        wrapper.in(ConflictMediationOrgDO::getMediationId, ids);
        List<ConflictMediationOrgDO> orgList = conflictMediationOrgService.list(wrapper);
        Map<String, List<ConflictMediationOrgRespVO>> listMap = new HashMap<>();
        if (CollUtil.isNotEmpty(orgList)) {
            listMap = orgList.stream()
                    .map(org -> BeanUtil.copyProperties(org, ConflictMediationOrgRespVO.class))
                    .collect(Collectors.groupingBy(ConflictMediationOrgRespVO::getMediationId));
        }
        for (ConflictMediationDO item : list) {
            ConflictMediationRespVO vo = BeanUtil.copyProperties(item, ConflictMediationRespVO.class);
            List<ConflictMediationOrgRespVO> orgRespVOS = listMap.get(vo.getId());
            if (CollUtil.isNotEmpty(orgRespVOS)) {
                vo.setOrgReqVOS(orgRespVOS);
            }
            resultList.add(vo);
        }
        return resultList;
    }

    @Override
    public List<ConflictMediationDO> getMediationByEventCodeAndMediationResult(String eventCode,
                                                                             MediationResultEnum mediationResult) {
        LambdaQueryWrapper<ConflictMediationDO> query = new LambdaQueryWrapper<>();
        query.eq(ConflictMediationDO::getEventCode, eventCode);
        query.eq(ConflictMediationDO::getMediationResult, mediationResult.getCode());
        List<ConflictMediationDO> conflictMediationDOS = conflictMediationDao.selectList(query);
        if (CollUtil.isEmpty(conflictMediationDOS)) {
            return Collections.emptyList();
        }
        return conflictMediationDOS;
    }
}
