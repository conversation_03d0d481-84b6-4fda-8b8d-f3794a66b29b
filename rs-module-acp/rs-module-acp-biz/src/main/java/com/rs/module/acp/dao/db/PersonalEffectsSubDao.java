package com.rs.module.acp.dao.db;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.db.PersonalEffectsSubDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-收押业务-随身物品登记子 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PersonalEffectsSubDao extends IBaseDao<PersonalEffectsSubDO> {


    default PageResult<PersonalEffectsSubDO> selectPage(PersonalEffectsSubPageReqVO reqVO) {
        Page<PersonalEffectsSubDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PersonalEffectsSubDO> wrapper = new LambdaQueryWrapperX<PersonalEffectsSubDO>()
            .eqIfPresent(PersonalEffectsSubDO::getPersonalEffectsId, reqVO.getPersonalEffectsId())
            .eqIfPresent(PersonalEffectsSubDO::getRybh, reqVO.getRybh())
            .eqIfPresent(PersonalEffectsSubDO::getRyxm, reqVO.getRyxm())
            .eqIfPresent(PersonalEffectsSubDO::getWpmc, reqVO.getWpmc())
            .eqIfPresent(PersonalEffectsSubDO::getZl, reqVO.getZl())
            .eqIfPresent(PersonalEffectsSubDO::getHbzl, reqVO.getHbzl())
            .eqIfPresent(PersonalEffectsSubDO::getSl, reqVO.getSl())
            .eqIfPresent(PersonalEffectsSubDO::getWptz, reqVO.getWptz())
            .eqIfPresent(PersonalEffectsSubDO::getWpzp, reqVO.getWpzp())
            .eqIfPresent(PersonalEffectsSubDO::getWz, reqVO.getWz())
            .eqIfPresent(PersonalEffectsSubDO::getWpczqk, reqVO.getWpczqk())
            .eqIfPresent(PersonalEffectsSubDO::getXjzrgrzkrq, reqVO.getXjzrgrzkrq())
            .eqIfPresent(PersonalEffectsSubDO::getBz, reqVO.getBz())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PersonalEffectsSubDO::getAddTime);
        }
        Page<PersonalEffectsSubDO> personalEffectsSubPage = selectPage(page, wrapper);
        return new PageResult<>(personalEffectsSubPage.getRecords(), personalEffectsSubPage.getTotal());
    }
    default List<PersonalEffectsSubDO> selectList(PersonalEffectsSubListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PersonalEffectsSubDO>()
            .eqIfPresent(PersonalEffectsSubDO::getPersonalEffectsId, reqVO.getPersonalEffectsId())
            .eqIfPresent(PersonalEffectsSubDO::getRybh, reqVO.getRybh())
            .eqIfPresent(PersonalEffectsSubDO::getRyxm, reqVO.getRyxm())
            .eqIfPresent(PersonalEffectsSubDO::getWpmc, reqVO.getWpmc())
            .eqIfPresent(PersonalEffectsSubDO::getZl, reqVO.getZl())
            .eqIfPresent(PersonalEffectsSubDO::getHbzl, reqVO.getHbzl())
            .eqIfPresent(PersonalEffectsSubDO::getSl, reqVO.getSl())
            .eqIfPresent(PersonalEffectsSubDO::getWptz, reqVO.getWptz())
            .eqIfPresent(PersonalEffectsSubDO::getWpzp, reqVO.getWpzp())
            .eqIfPresent(PersonalEffectsSubDO::getWz, reqVO.getWz())
            .eqIfPresent(PersonalEffectsSubDO::getWpczqk, reqVO.getWpczqk())
            .eqIfPresent(PersonalEffectsSubDO::getXjzrgrzkrq, reqVO.getXjzrgrzkrq())
            .eqIfPresent(PersonalEffectsSubDO::getBz, reqVO.getBz())
        .orderByDesc(PersonalEffectsSubDO::getAddTime));    }


    }
