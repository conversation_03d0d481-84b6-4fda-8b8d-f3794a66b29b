package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.transitionroom.TransitionRoomEvalListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.transitionroom.TransitionRoomEvalPageReqVO;
import com.rs.module.acp.entity.gj.TransitionRoomEvalDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-过渡考核登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface TransitionRoomEvalDao extends IBaseDao<TransitionRoomEvalDO> {


    default PageResult<TransitionRoomEvalDO> selectPage(TransitionRoomEvalPageReqVO reqVO) {
        Page<TransitionRoomEvalDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<TransitionRoomEvalDO> wrapper = new LambdaQueryWrapperX<TransitionRoomEvalDO>()
            .eqIfPresent(TransitionRoomEvalDO::getTransitionRoomId, reqVO.getTransitionRoomId())
            .eqIfPresent(TransitionRoomEvalDO::getEvalBasis, reqVO.getEvalBasis())
            .eqIfPresent(TransitionRoomEvalDO::getEvalContent, reqVO.getEvalContent())
            .eqIfPresent(TransitionRoomEvalDO::getEvalResult, reqVO.getEvalResult())
            .betweenIfPresent(TransitionRoomEvalDO::getEvalTime, reqVO.getEvalTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(TransitionRoomEvalDO::getAddTime);
        }
        Page<TransitionRoomEvalDO> transitionRoomEvalPage = selectPage(page, wrapper);
        return new PageResult<>(transitionRoomEvalPage.getRecords(), transitionRoomEvalPage.getTotal());
    }
    default List<TransitionRoomEvalDO> selectList(TransitionRoomEvalListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TransitionRoomEvalDO>()
            .eqIfPresent(TransitionRoomEvalDO::getTransitionRoomId, reqVO.getTransitionRoomId())
            .eqIfPresent(TransitionRoomEvalDO::getEvalBasis, reqVO.getEvalBasis())
            .eqIfPresent(TransitionRoomEvalDO::getEvalContent, reqVO.getEvalContent())
            .eqIfPresent(TransitionRoomEvalDO::getEvalResult, reqVO.getEvalResult())
            .betweenIfPresent(TransitionRoomEvalDO::getEvalTime, reqVO.getEvalTime())
        .orderByDesc(TransitionRoomEvalDO::getAddTime));    }


    }
