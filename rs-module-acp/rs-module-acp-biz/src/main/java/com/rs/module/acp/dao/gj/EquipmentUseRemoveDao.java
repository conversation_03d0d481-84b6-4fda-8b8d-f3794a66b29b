package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseRemoveListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseRemovePageReqVO;
import com.rs.module.acp.entity.gj.EquipmentUseRemoveDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-械具使用解除呈批 Dao
*
* <AUTHOR>
*/
@Mapper
public interface EquipmentUseRemoveDao extends IBaseDao<EquipmentUseRemoveDO> {


    default PageResult<EquipmentUseRemoveDO> selectPage(EquipmentUseRemovePageReqVO reqVO) {
        Page<EquipmentUseRemoveDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<EquipmentUseRemoveDO> wrapper = new LambdaQueryWrapperX<EquipmentUseRemoveDO>()
            .eqIfPresent(EquipmentUseRemoveDO::getEquipmentUseId, reqVO.getEquipmentUseId())
            .eqIfPresent(EquipmentUseRemoveDO::getRemoveReason, reqVO.getRemoveReason())
            .betweenIfPresent(EquipmentUseRemoveDO::getRemoveTime, reqVO.getRemoveTime())
            .eqIfPresent(EquipmentUseRemoveDO::getRemoveExecuteSituation, reqVO.getRemoveExecuteSituation())
            .eqIfPresent(EquipmentUseRemoveDO::getIsRemove, reqVO.getIsRemove())
            .eqIfPresent(EquipmentUseRemoveDO::getStatus, reqVO.getStatus())
            .eqIfPresent(EquipmentUseRemoveDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(EquipmentUseRemoveDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(EquipmentUseRemoveDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(EquipmentUseRemoveDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(EquipmentUseRemoveDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(EquipmentUseRemoveDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(EquipmentUseRemoveDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(EquipmentUseRemoveDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(EquipmentUseRemoveDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(EquipmentUseRemoveDO::getAddTime);
        }
        Page<EquipmentUseRemoveDO> equipmentUseRemovePage = selectPage(page, wrapper);
        return new PageResult<>(equipmentUseRemovePage.getRecords(), equipmentUseRemovePage.getTotal());
    }
    default List<EquipmentUseRemoveDO> selectList(EquipmentUseRemoveListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<EquipmentUseRemoveDO>()
            .eqIfPresent(EquipmentUseRemoveDO::getEquipmentUseId, reqVO.getEquipmentUseId())
            .eqIfPresent(EquipmentUseRemoveDO::getRemoveReason, reqVO.getRemoveReason())
            .betweenIfPresent(EquipmentUseRemoveDO::getRemoveTime, reqVO.getRemoveTime())
            .eqIfPresent(EquipmentUseRemoveDO::getRemoveExecuteSituation, reqVO.getRemoveExecuteSituation())
            .eqIfPresent(EquipmentUseRemoveDO::getIsRemove, reqVO.getIsRemove())
            .eqIfPresent(EquipmentUseRemoveDO::getStatus, reqVO.getStatus())
            .eqIfPresent(EquipmentUseRemoveDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(EquipmentUseRemoveDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(EquipmentUseRemoveDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(EquipmentUseRemoveDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(EquipmentUseRemoveDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(EquipmentUseRemoveDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(EquipmentUseRemoveDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(EquipmentUseRemoveDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(EquipmentUseRemoveDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(EquipmentUseRemoveDO::getAddTime));    }


    }
