package com.rs.module.acp.entity.wb;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-窗口业务-协同办案人员 DO
 *
 * <AUTHOR>
 */
@TableName("acp_wb_coop_case_personnel")
@KeySequence("acp_wb_coop_case_personnel_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_wb_coop_case_personnel")
public class CoopCasePersonnelDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 提讯ID
     */
    private String arraignmentId;
    /**
     * 警号
     */
    private String jh;
    /**
     * 姓名
     */
    private String xm;
    /**
     * 证件类型
     */
    private String zjlx;
    /**
     * 证件号码
     */
    private String zjhm;
    /**
     * 办案单位代码
     */
    private String badwdm;
    /**
     * 办案单位名称
     */
    private String badwmc;
    /**
     * 联系方式
     */
    private String lxfs;
    /**
     * 性别
     */
    private String xb;
    /**
     * 照片存储url
     */
    private String zpUrl;
    /**
     * 工作证件url
     */
    private String gzzjUrl;

    private String tjjglx;
}
