package com.rs.module.acp.entity.gj;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-教育康复课程计划 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_edurehab_courses_plan")
@KeySequence("acp_gj_edurehab_courses_plan_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_edurehab_courses_plan")
public class EdurehabCoursesPlanDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 课程计划名称
     */
    private String planName;
    /**
     * plan_code
     */
    private String planCode;
    /**
     * 课程时段-开始
     */
    private Date startDate;
    /**
     * 课程时段-结束
     */
    private Date endDate;
    /**
     * 状态
     */
    private String status;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

}
