package com.rs.module.acp.dao.wb;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.LegalAssistanceDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-窗口业务-法律帮助申请 Dao
*
* <AUTHOR>
*/
@Mapper
public interface LegalAssistanceDao extends IBaseDao<LegalAssistanceDO> {


    default PageResult<LegalAssistanceDO> selectPage(LegalAssistancePageReqVO reqVO) {
        Page<LegalAssistanceDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<LegalAssistanceDO> wrapper = new LambdaQueryWrapperX<LegalAssistanceDO>()
            .eqIfPresent(LegalAssistanceDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(LegalAssistanceDO::getJgryxm, reqVO.getJgryxm())
            .likeIfPresent(LegalAssistanceDO::getLawyerName, reqVO.getLawyerName())
            .eqIfPresent(LegalAssistanceDO::getLawyerIdNumber, reqVO.getLawyerIdNumber())
            .eqIfPresent(LegalAssistanceDO::getLawyerPracticeLicenseNumber, reqVO.getLawyerPracticeLicenseNumber())
            .eqIfPresent(LegalAssistanceDO::getLawyerFirm, reqVO.getLawyerFirm())
            .betweenIfPresent(LegalAssistanceDO::getApplyTime, reqVO.getApplyTime())
            .eqIfPresent(LegalAssistanceDO::getAssistanceMatter, reqVO.getAssistanceMatter())
            .eqIfPresent(LegalAssistanceDO::getIsNeedCaseUnitAllowed, reqVO.getIsNeedCaseUnitAllowed())
            .eqIfPresent(LegalAssistanceDO::getCaseUnitType, reqVO.getCaseUnitType())
            .likeIfPresent(LegalAssistanceDO::getCaseUnitName, reqVO.getCaseUnitName())
            .eqIfPresent(LegalAssistanceDO::getIsCaseUnitAllowed, reqVO.getIsCaseUnitAllowed())
            .eqIfPresent(LegalAssistanceDO::getCaseUnitFeedback, reqVO.getCaseUnitFeedback())
            .betweenIfPresent(LegalAssistanceDO::getDecisionTime, reqVO.getDecisionTime())
            .eqIfPresent(LegalAssistanceDO::getMeetingMethod, reqVO.getMeetingMethod())
            .eqIfPresent(LegalAssistanceDO::getMeetingRoomId, reqVO.getMeetingRoomId())
            .likeIfPresent(LegalAssistanceDO::getMeetingRoomName, reqVO.getMeetingRoomName())
            .eqIfPresent(LegalAssistanceDO::getIsSpecialMeeting, reqVO.getIsSpecialMeeting())
            .betweenIfPresent(LegalAssistanceDO::getEscortingTime, reqVO.getEscortingTime())
            .eqIfPresent(LegalAssistanceDO::getInspectionResult, reqVO.getInspectionResult())
            .betweenIfPresent(LegalAssistanceDO::getMeetingStartTime, reqVO.getMeetingStartTime())
            .betweenIfPresent(LegalAssistanceDO::getMeetingEndTime, reqVO.getMeetingEndTime())
            .eqIfPresent(LegalAssistanceDO::getEscortingPoliceSfzh, reqVO.getEscortingPoliceSfzh())
            .eqIfPresent(LegalAssistanceDO::getEscortingPolice, reqVO.getEscortingPolice())
            .eqIfPresent(LegalAssistanceDO::getReturnPoliceSfzh, reqVO.getReturnPoliceSfzh())
            .eqIfPresent(LegalAssistanceDO::getReturnPolice, reqVO.getReturnPolice())
            .betweenIfPresent(LegalAssistanceDO::getReturnTime, reqVO.getReturnTime())
            .eqIfPresent(LegalAssistanceDO::getReturnInspectionResult, reqVO.getReturnInspectionResult())
            .eqIfPresent(LegalAssistanceDO::getAbnormalSituations, reqVO.getAbnormalSituations())
            .eqIfPresent(LegalAssistanceDO::getSupervisingPoliceSfzh, reqVO.getSupervisingPoliceSfzh())
            .likeIfPresent(LegalAssistanceDO::getSupervisingPoliceName, reqVO.getSupervisingPoliceName())
            .eqIfPresent(LegalAssistanceDO::getIsLawyerViolation, reqVO.getIsLawyerViolation())
            .eqIfPresent(LegalAssistanceDO::getIsLawyerInformAbnormalBehav, reqVO.getIsLawyerInformAbnormalBehav())
            .eqIfPresent(LegalAssistanceDO::getLawyerViolationDetails, reqVO.getLawyerViolationDetails())
            .eqIfPresent(LegalAssistanceDO::getAbnormalBehaviorDetails, reqVO.getAbnormalBehaviorDetails())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(LegalAssistanceDO::getAddTime);
        }
        Page<LegalAssistanceDO> legalAssistancePage = selectPage(page, wrapper);
        return new PageResult<>(legalAssistancePage.getRecords(), legalAssistancePage.getTotal());
    }
    default List<LegalAssistanceDO> selectList(LegalAssistanceListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<LegalAssistanceDO>()
            .eqIfPresent(LegalAssistanceDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(LegalAssistanceDO::getJgryxm, reqVO.getJgryxm())
            .likeIfPresent(LegalAssistanceDO::getLawyerName, reqVO.getLawyerName())
            .eqIfPresent(LegalAssistanceDO::getLawyerIdNumber, reqVO.getLawyerIdNumber())
            .eqIfPresent(LegalAssistanceDO::getLawyerPracticeLicenseNumber, reqVO.getLawyerPracticeLicenseNumber())
            .eqIfPresent(LegalAssistanceDO::getLawyerFirm, reqVO.getLawyerFirm())
            .betweenIfPresent(LegalAssistanceDO::getApplyTime, reqVO.getApplyTime())
            .eqIfPresent(LegalAssistanceDO::getAssistanceMatter, reqVO.getAssistanceMatter())
            .eqIfPresent(LegalAssistanceDO::getIsNeedCaseUnitAllowed, reqVO.getIsNeedCaseUnitAllowed())
            .eqIfPresent(LegalAssistanceDO::getCaseUnitType, reqVO.getCaseUnitType())
            .likeIfPresent(LegalAssistanceDO::getCaseUnitName, reqVO.getCaseUnitName())
            .eqIfPresent(LegalAssistanceDO::getIsCaseUnitAllowed, reqVO.getIsCaseUnitAllowed())
            .eqIfPresent(LegalAssistanceDO::getCaseUnitFeedback, reqVO.getCaseUnitFeedback())
            .betweenIfPresent(LegalAssistanceDO::getDecisionTime, reqVO.getDecisionTime())
            .eqIfPresent(LegalAssistanceDO::getMeetingMethod, reqVO.getMeetingMethod())
            .eqIfPresent(LegalAssistanceDO::getMeetingRoomId, reqVO.getMeetingRoomId())
            .likeIfPresent(LegalAssistanceDO::getMeetingRoomName, reqVO.getMeetingRoomName())
            .eqIfPresent(LegalAssistanceDO::getIsSpecialMeeting, reqVO.getIsSpecialMeeting())
            .betweenIfPresent(LegalAssistanceDO::getEscortingTime, reqVO.getEscortingTime())
            .eqIfPresent(LegalAssistanceDO::getInspectionResult, reqVO.getInspectionResult())
            .betweenIfPresent(LegalAssistanceDO::getMeetingStartTime, reqVO.getMeetingStartTime())
            .betweenIfPresent(LegalAssistanceDO::getMeetingEndTime, reqVO.getMeetingEndTime())
            .eqIfPresent(LegalAssistanceDO::getEscortingPoliceSfzh, reqVO.getEscortingPoliceSfzh())
            .eqIfPresent(LegalAssistanceDO::getEscortingPolice, reqVO.getEscortingPolice())
            .eqIfPresent(LegalAssistanceDO::getReturnPoliceSfzh, reqVO.getReturnPoliceSfzh())
            .eqIfPresent(LegalAssistanceDO::getReturnPolice, reqVO.getReturnPolice())
            .betweenIfPresent(LegalAssistanceDO::getReturnTime, reqVO.getReturnTime())
            .eqIfPresent(LegalAssistanceDO::getReturnInspectionResult, reqVO.getReturnInspectionResult())
            .eqIfPresent(LegalAssistanceDO::getAbnormalSituations, reqVO.getAbnormalSituations())
            .eqIfPresent(LegalAssistanceDO::getSupervisingPoliceSfzh, reqVO.getSupervisingPoliceSfzh())
            .likeIfPresent(LegalAssistanceDO::getSupervisingPoliceName, reqVO.getSupervisingPoliceName())
            .eqIfPresent(LegalAssistanceDO::getIsLawyerViolation, reqVO.getIsLawyerViolation())
            .eqIfPresent(LegalAssistanceDO::getIsLawyerInformAbnormalBehav, reqVO.getIsLawyerInformAbnormalBehav())
            .eqIfPresent(LegalAssistanceDO::getLawyerViolationDetails, reqVO.getLawyerViolationDetails())
            .eqIfPresent(LegalAssistanceDO::getAbnormalBehaviorDetails, reqVO.getAbnormalBehaviorDetails())
        .orderByDesc(LegalAssistanceDO::getAddTime));    }


    }
