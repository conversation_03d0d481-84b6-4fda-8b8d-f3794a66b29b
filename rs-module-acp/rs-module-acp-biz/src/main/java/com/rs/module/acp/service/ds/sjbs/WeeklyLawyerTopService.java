package com.rs.module.acp.service.ds.sjbs;

import javax.validation.*;

import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyDataSubmitReqVO;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyLawyerTopSaveReqVO;
import com.rs.module.acp.entity.ds.sjbs.WeeklyDataSubmitJlsDO;
import com.rs.module.acp.entity.ds.sjbs.WeeklyLawyerTopDO;
import com.bsp.common.orm.mybatis.service.IBaseService;

import java.util.Date;
import java.util.List;

/**
 * 实战平台-数据固化-每周会见律师排名 Service 接口
 *
 * <AUTHOR>
 */
public interface WeeklyLawyerTopService extends IBaseService<WeeklyLawyerTopDO>{

    /**
     * 创建实战平台-数据固化-每周会见律师排名
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createWeeklyLawyerTop(@Valid WeeklyLawyerTopSaveReqVO createReqVO);

    /**
     * 更新实战平台-数据固化-每周会见律师排名
     *
     * @param updateReqVO 更新信息
     */
    void updateWeeklyLawyerTop(@Valid WeeklyLawyerTopSaveReqVO updateReqVO);

    /**
     * 删除实战平台-数据固化-每周会见律师排名
     *
     * @param id 编号
     */
    void deleteWeeklyLawyerTop(String id);

    /**
     * 获得实战平台-数据固化-每周会见律师排名
     *
     * @param id 编号
     * @return 实战平台-数据固化-每周会见律师排名
     */
    WeeklyLawyerTopDO getWeeklyLawyerTop(String id);

    void saveForStatistic(List<WeeklyDataSubmitReqVO> weeklyDataSubmitList);

    List<WeeklyLawyerTopDO> getWeeklyLawyerTopByDate(String startDate, String endDate, String orgCode);

    List<WeeklyLawyerTopDO> getByWeeklyDataSubmitId(String weeklyDataSubmitId);
}
