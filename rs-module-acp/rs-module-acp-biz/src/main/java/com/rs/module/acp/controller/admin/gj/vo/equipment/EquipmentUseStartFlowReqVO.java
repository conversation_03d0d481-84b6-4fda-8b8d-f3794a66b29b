package com.rs.module.acp.controller.admin.gj.vo.equipment;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 戒具使用-流程启动
 * <AUTHOR>
 * @date 2025/6/3 18:53
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EquipmentUseStartFlowReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("监管人员编码")
    @NotBlank(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员-姓名")
    private String jgryxm;

    @ApiModelProperty("使用情形")
    @NotBlank(message = "使用情形不能为空")
    private String useSituation;

    @ApiModelProperty("使用天数")
    @NotBlank(message = "使用天数不能为空")
    private String useDays;

    @ApiModelProperty("械具类型")
    @NotBlank(message = "械具类型不能为空")
    private String punishmentToolType;

    @ApiModelProperty("使用械具的理由")
    @NotBlank(message = "使用械具的理由不能为空")
    private String useReason;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否关联惩罚")
    private Integer isAssociatedPunishment;

    @ApiModelProperty("是否临时固定")
    private Integer isTempFixation;

    @ApiModelProperty("固定天数")
    private String fixationDays;

    @ApiModelProperty("惩罚措施 字典：ZD_GJCFNR 多个逗号分割")
    private String punishmentMeasures;

    //@ApiModelProperty("流程启动-流程标识")
    //private String defKey;
    //
    //@ApiModelProperty("流程启动-自定义消息标题。不设置此属性则使用流程中配置的消息标题")
    //private String msgTit;
    //
    //@ApiModelProperty("自定义消息地址(必须)")
    //private String msgUrl;
    //
    //@ApiModelProperty("流程自定义变量")
    //private Map<String, Object> variables;

}
