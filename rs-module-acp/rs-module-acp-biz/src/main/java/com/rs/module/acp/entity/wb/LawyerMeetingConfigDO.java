package com.rs.module.acp.entity.wb;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-窗口业务-律师会见-会见方式、预约时段配置 DO
 *
 * <AUTHOR>
 */
@TableName("acp_wb_lawyer_meeting_config")
@KeySequence("acp_wb_lawyer_meeting_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LawyerMeetingConfigDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 会见方式
     */
    private String meetingMethod;
    /**
     * 现场预约会见时间段配置
     */
    private String applyMeetingTimeSlot;

}
