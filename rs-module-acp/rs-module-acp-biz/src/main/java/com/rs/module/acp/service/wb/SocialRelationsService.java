package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.SocialRelationsDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-社会关系 Service 接口
 *
 * <AUTHOR>
 */
public interface SocialRelationsService extends IBaseService<SocialRelationsDO>{

    /**
     * 创建实战平台-窗口业务-社会关系
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    boolean createSocialRelations(@Valid SocialRelationsSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-社会关系
     *
     * @param updateReqVO 更新信息
     */
    void updateSocialRelations(@Valid SocialRelationsUpdateReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-社会关系
     *
     * @param id 编号
     */
    void deleteSocialRelations(String id);

    /**
     * 获得实战平台-窗口业务-社会关系
     *
     * @param id 编号
     * @return 实战平台-窗口业务-社会关系
     */
    SocialRelationsDO getSocialRelations(String id);

    /**
    * 获得实战平台-窗口业务-社会关系分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-社会关系分页
    */
    PageResult<SocialRelationsDO> getSocialRelationsPage(SocialRelationsPageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-社会关系列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-社会关系列表
    */
    List<SocialRelationsDO> getSocialRelationsList(SocialRelationsListReqVO listReqVO);

    /**
     * 获得实战平台-窗口业务-根据被监管人员编码获取社会关联列表
     *
     * @param jgrybm 被监管人员编码
     * @return 实战平台-窗口业务-社会关系列表
     */
    List<SocialRelationsRespVO> getSocialRelationsListByJgrybm(String jgrybm);

    /**
     * 获得实战平台-窗口业务-根据被监管人员编码获取社会关联列表
     *
     * @param familyList 家属创建信息
     * @param jgrybm 被监管人员编码
     * @return 实战平台-窗口业务-社会关系列表
     */
    boolean saveSocialRelationsByJgrybm(List<SocialRelationsChildSaveReqVO> familyList,String jgrybm);

    /**
     * 根据ID获取亲属信息
     * @param id
     * @return
     */
    SocialRelationsRespVO getSocialRelationsById(String id);
}
