package com.rs.module.acp.controller.admin.db;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.PersonalEffectsSubDO;
import com.rs.module.acp.service.db.PersonalEffectsSubService;

@Api(tags = "实战平台-收押业务-随身物品登记子")
@RestController
@RequestMapping("/acp/db/personalEffectsSub")
@Validated
public class PersonalEffectsSubController {

    @Resource
    private PersonalEffectsSubService personalEffectsSubService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-收押业务-随身物品登记子")
    public CommonResult<String> createPersonalEffectsSub(@Valid @RequestBody PersonalEffectsSubSaveReqVO createReqVO) {
        return success(personalEffectsSubService.createPersonalEffectsSub(createReqVO));
    }

    @PostMapping("/create/batch")
    @ApiOperation(value = "批量创建实战平台-收押业务-随身物品登记子,无需登记状态传status='04'")
    public CommonResult<ApprovalRespVO> createPersonalEffectsSubBatch(@Valid @RequestBody List<PersonalEffectsSubSaveReqVO> createReqVOList) {
        boolean isApproval = personalEffectsSubService.createPersonalEffectsSubBatch(createReqVOList);
        ApprovalRespVO approvalRespVO = new ApprovalRespVO();
        approvalRespVO.setIsApproval(isApproval);
        return success(approvalRespVO);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-收押业务-随身物品登记子")
    public CommonResult<Boolean> updatePersonalEffectsSub(@Valid @RequestBody PersonalEffectsSubSaveReqVO updateReqVO) {
        personalEffectsSubService.updatePersonalEffectsSub(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-收押业务-随身物品登记子")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deletePersonalEffectsSub(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           personalEffectsSubService.deletePersonalEffectsSub(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-收押业务-随身物品登记子")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<PersonalEffectsSubRespVO> getPersonalEffectsSub(@RequestParam("id") String id) {
        PersonalEffectsSubDO personalEffectsSub = personalEffectsSubService.getPersonalEffectsSub(id);
        return success(BeanUtils.toBean(personalEffectsSub, PersonalEffectsSubRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-收押业务-随身物品登记子分页")
    public CommonResult<PageResult<PersonalEffectsSubRespVO>> getPersonalEffectsSubPage(@Valid @RequestBody PersonalEffectsSubPageReqVO pageReqVO) {
        PageResult<PersonalEffectsSubDO> pageResult = personalEffectsSubService.getPersonalEffectsSubPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PersonalEffectsSubRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-收押业务-随身物品登记子列表")
    public CommonResult<List<PersonalEffectsSubRespVO>> getPersonalEffectsSubList(@Valid @RequestBody PersonalEffectsSubListReqVO listReqVO) {
        List<PersonalEffectsSubDO> list = personalEffectsSubService.getPersonalEffectsSubList(listReqVO);
        return success(BeanUtils.toBean(list, PersonalEffectsSubRespVO.class));
    }

    /**
     * 通过人员编号获取随身物品登记子信息
     */
    @GetMapping("/getByRybh")
    @ApiOperation(value = "通过人员编号获取随身物品登记子信息")
    public CommonResult<List<PersonalEffectsSubRespVO>> getPersonalEffectsSubByRybh(@RequestParam("rybh") String rybh) {
        List<PersonalEffectsSubDO> personalEffectsSubDOList = personalEffectsSubService.getPersonalEffectsSubByRybh(rybh);
        return success(BeanUtils.toBean(personalEffectsSubDOList, PersonalEffectsSubRespVO.class));
    }
}
