package com.rs.module.acp.controller.admin.db.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-入所登记（拘留所）列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class InRecordJlsListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("姓名")
    private String xm;

    @ApiModelProperty("姓名拼音")
    private String xmpy;

    @ApiModelProperty("别名")
    private String bm;

    @ApiModelProperty("性别")
    private String xb;

    @ApiModelProperty("出生日期")
    private Date csrq;

    @ApiModelProperty("证件类型")
    private String zjlx;

    @ApiModelProperty("证件号码")
    private String zjhm;

    @ApiModelProperty("国籍")
    private String gj;

    @ApiModelProperty("民族")
    private String mz;

    @ApiModelProperty("婚姻状况")
    private String hyzk;

    @ApiModelProperty("籍贯")
    private String jg;

    @ApiModelProperty("宗教信仰")
    private String zjxy;

    @ApiModelProperty("户籍地")
    private String hjd;

    @ApiModelProperty("户籍地详址")
    private String hjdxz;

    @ApiModelProperty("现住址")
    private String xzz;

    @ApiModelProperty("现住址详址")
    private String xzzxz;

    @ApiModelProperty("文化程度")
    private String whcd;

    @ApiModelProperty("政治面貌")
    private String zzmm;

    @ApiModelProperty("职业")
    private String zy;

    @ApiModelProperty("工作单位")
    private String gzdw;

    @ApiModelProperty("身份")
    private String sf;

    @ApiModelProperty("特殊身份")
    private String tssf;

    @ApiModelProperty("信息核查")
    private String xxhc;

    @ApiModelProperty("是否为在校学生")
    private String sfwxzxs;

    @ApiModelProperty("学校名称")
    private String xxmc;

    @ApiModelProperty("案由代码")
    private String ajlbdm;

    @ApiModelProperty("案件类别名称")
    private String ajlb;

    @ApiModelProperty("案件编号")
    private String ajbh;

    @ApiModelProperty("人员编号")
    private String rybh;

    @ApiModelProperty("案事件相关人员编号")
    private String asjxgrybh;

    @ApiModelProperty("办案中心编号")
    private String bazxbh;

    @ApiModelProperty("送拘日期")
    private Date sjrq;

    @ApiModelProperty("送拘机关类型")
    private String sjjglx;

    @ApiModelProperty("送拘机关名称")
    private String sjjgmc;

    @ApiModelProperty("送拘人姓名1")
    private String sjrxm;

    @ApiModelProperty("送拘人联系电话1")
    private String sjrlxdh;

    @ApiModelProperty("送拘人姓名2")
    private String sjrxm2;

    @ApiModelProperty("送拘人联系电话2")
    private String sjrlxdh2;

    @ApiModelProperty("送拘民警姓名")
    private String sjmjxm;

    @ApiModelProperty("送拘民警联系电话")
    private String sjmjxmlxdh;

    @ApiModelProperty("办案单位类型")
    private String badwlx;

    @ApiModelProperty("办案单位")
    private String badw;

    @ApiModelProperty("拘留决定机关类型")
    private String jljdjglx;

    @ApiModelProperty("拘留决定机关名称")
    private String jljdjgmc;

    @ApiModelProperty("收拘凭证")
    private String sjpz;

    @ApiModelProperty("收拘凭证文书号")
    private String sjpzwsh;

    @ApiModelProperty("收拘凭证文书号")
    private String sjpzwsdz;

    @ApiModelProperty("档案编号")
    private String dabh;

    @ApiModelProperty("回执法律文书号")
    private String hzflwsh;

    @ApiModelProperty("入所原因")
    private String rsyy;

    @ApiModelProperty("入所日期")
    private Date rsrq;

    @ApiModelProperty("收拘民警")
    private String sjmj;

    @ApiModelProperty("拘留期限")
    private Integer jlqx;

    @ApiModelProperty("拘留起始日期")
    private Date jlqsrq;

    @ApiModelProperty("拘留截止日期")
    private Date jljzrq;

    @ApiModelProperty("管理类别")
    private String gllb;

    @ApiModelProperty("监区id")
    private String areaId;

    @ApiModelProperty("监区名称")
    private String areaName;

    @ApiModelProperty("监室号")
    private String jsh;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("简要案情")
    private String jyaq;

    @ApiModelProperty("同案人，多个逗号分割")
    private String tar;

    @ApiModelProperty("备注")
    private String bz;

    @ApiModelProperty("涉毒尿检初查结果")
    private String sdnjccjg;

    @ApiModelProperty("涉毒尿检单位")
    private String sdnjdw;

    @ApiModelProperty("涉毒尿检初查结果")
    private Date sdnjccsj;

    @ApiModelProperty("涉毒尿检检查人")
    private String sdnjjcr;

    @ApiModelProperty("手环ID")
    private String shid;

    @ApiModelProperty("手环绑定状态")
    private String shbdzt;

    @ApiModelProperty("手环绑定时间")
    private Date sdbdsj;

    @ApiModelProperty("是否涉密人员")
    private Short sfsm;

    @ApiModelProperty("人员代号")
    private String rydh;

    @ApiModelProperty("涉密原因")
    private String smyy;

    @ApiModelProperty("涉密备注")
    private String smbz;

    @ApiModelProperty("救济日期")
    private Date jjrq;

    @ApiModelProperty("救济原因")
    private String jjyy;

    @ApiModelProperty("救济领取物品")
    private String jjlqwp;

    @ApiModelProperty("入所类型")
    private String rslx;

    @ApiModelProperty("经办人")
    private String jbr;

    @ApiModelProperty("经办时间")
    private Date jbsj;

    @ApiModelProperty("办理状态")
    private String status;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审批状态")
    private String spzt;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("当前步骤")
    private String currentStep;

}
