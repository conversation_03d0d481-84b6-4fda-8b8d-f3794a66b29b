package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bsp.security.util.SessionUserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.HallInfoDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.HallInfoDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-服务大厅信息发布 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HallInfoServiceImpl extends BaseServiceImpl<HallInfoDao, HallInfoDO> implements HallInfoService {

    @Resource
    private HallInfoDao hallInfoDao;

    @Autowired
    private WbCommonService wbCommonService;

    @Autowired
    private HallInfoConfigService hallInfoConfigService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createHallInfo(HallInfoSaveReqVO createReqVO) {
        // 插入
        HallInfoDO hallInfo = BeanUtils.toBean(createReqVO, HallInfoDO.class);
        getAttachmentUrl(hallInfo);
        hallInfoDao.insert(hallInfo);
        // 返回
        return hallInfo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateHallInfo(HallInfoSaveReqVO updateReqVO) {
        // 校验存在

        HallInfoDO sjkHallInfo =  hallInfoDao.selectById(updateReqVO.getId());
        if(ObjectUtil.isEmpty(sjkHallInfo)){
            throw new ServerException("实战平台-窗口业务-服务大厅信息发布数据不存在");
        }

        //先删除旧的
        if(ObjectUtil.isNotEmpty(sjkHallInfo.getAttachmentUrl()) && !"1".equals(sjkHallInfo.getAttachmentType())){
            wbCommonService.deleteFile(sjkHallInfo.getAttachmentUrl());
        }


        // 更新
        HallInfoDO updateObj = BeanUtils.toBean(updateReqVO, HallInfoDO.class);
        getAttachmentUrl(updateObj);

        hallInfoDao.updateById(updateObj);
    }

    private void getAttachmentUrl(HallInfoDO hallInfo){
        if ("0".equals(hallInfo.getAttachmentType())) {
            //文本类型，只能存在在在线编辑或附件上传一种
            if (ObjectUtil.isNotEmpty(hallInfo.getInfoContent()) && ObjectUtil.isNotEmpty(hallInfo.getAttachmentUrl())) {
                throw new ServerException("文本：只能存在在线编辑文本或附件上传一种");
            }
            if(ObjectUtil.isNotEmpty(hallInfo.getAttachmentUrl())){
                String attachmentUrl = wbCommonService.saveFile(null, hallInfo.getAttachmentUrl());
                hallInfo.setAttachmentUrl(attachmentUrl);
            }
            if(ObjectUtil.isNotEmpty(hallInfo.getInfoContent())){
                hallInfo.setAttachmentName("");
            }
        }else if("1".equals(hallInfo.getAttachmentType())){

        }else if("2".equals(hallInfo.getAttachmentType())){
            if(ObjectUtil.isNotEmpty(hallInfo.getAttachmentUrl())){
                String attachmentUrl = wbCommonService.saveFile(null, hallInfo.getAttachmentUrl());
                hallInfo.setAttachmentUrl(attachmentUrl);
            }
        }else {
            throw new ServerException("未找到对应类型！");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteHallInfo(String id) {
        // 校验存在
        validateHallInfoExists(id);
        // 删除
        hallInfoDao.deleteById(id);
    }

    private void validateHallInfoExists(String id) {
        if (hallInfoDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-服务大厅信息发布数据不存在");
        }
    }

    @Override
    public HallInfoRespVO getHallInfo(String id) {
        HallInfoDO hallInfoDO = hallInfoDao.selectById(id);
        HallInfoRespVO hallInfoRespVO = BeanUtils.toBean(hallInfoDO,HallInfoRespVO.class);
        if("0".equals(hallInfoRespVO.getAttachmentType()) || "2".equals(hallInfoRespVO.getAttachmentType())){
            if(ObjectUtil.isNotEmpty(hallInfoRespVO.getAttachmentUrl())){
                hallInfoRespVO.setAttachmentUrl(wbCommonService.getFile(hallInfoRespVO.getAttachmentUrl()));
            }
        }

        return hallInfoRespVO;
    }

    @Override
    public PageResult<HallInfoDO> getHallInfoPage(HallInfoPageReqVO pageReqVO) {
        return hallInfoDao.selectPage(pageReqVO);
    }

    @Override
    public List<HallInfoDO> getHallInfoList(HallInfoListReqVO listReqVO) {
        return hallInfoDao.selectList(listReqVO);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeStatus(String id, String status) {
        LambdaUpdateWrapper<HallInfoDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(HallInfoDO::getStatus, status).eq(HallInfoDO::getId, id);
        return update(lambdaUpdateWrapper);
    }

    @Override
    public HallInfoLargeScreenRespVO getHallInfoForLargeScreen() {

        HallInfoLargeScreenRespVO largeScreenRespVO = new HallInfoLargeScreenRespVO();
        HallInfoConfigRespVO hallInfoConfigRespVO = hallInfoConfigService.getHallInfoConfig();
        largeScreenRespVO.setIsCarousel(hallInfoConfigRespVO.getIsCarousel());

        LambdaQueryWrapper<HallInfoDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(HallInfoDO::getInfoContent,
                HallInfoDO::getAttachmentType,
                HallInfoDO::getAttachmentUrl,
                HallInfoDO::getTitle,HallInfoDO::getId,HallInfoDO::getSort);
        lambdaQueryWrapper.eq(HallInfoDO::getOrgCode, SessionUserUtil.getSessionUser().getOrgCode())
                .eq(HallInfoDO::getStatus,"0").orderByAsc(HallInfoDO::getSort);

        List<HallInfoDO> hallInfoDOList = list(lambdaQueryWrapper);

        if(CollectionUtil.isEmpty(hallInfoDOList)){
            return largeScreenRespVO;
        }

        List<HallInfoRespVO> hallInfoRespVOList = BeanUtils.toBean(hallInfoDOList,HallInfoRespVO.class);
        for(HallInfoRespVO hallInfoRespVO:hallInfoRespVOList){
            //图片类型不需要转
            if("0".equals(hallInfoRespVO.getAttachmentType()) || "2".equals(hallInfoRespVO.getAttachmentType())){
                if(ObjectUtil.isNotEmpty(hallInfoRespVO.getAttachmentUrl())){
                    hallInfoRespVO.setAttachmentUrl(wbCommonService.getFile(hallInfoRespVO.getAttachmentUrl()));
                }
            }
        }
        largeScreenRespVO.setInfoList(hallInfoRespVOList);
        return largeScreenRespVO;
    }

}
