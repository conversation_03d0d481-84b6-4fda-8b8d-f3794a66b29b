package com.rs.module.acp.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.rs.module.acp.entity.gj.*;
import com.rs.module.acp.entity.gj.confinement.ConfinementRegDO;
import com.rs.util.DicUtils;

public class GjBusTraceUtil {
    /**
     * 构建单独关押业务日志
     * @param entity
     * @param jgryxm
     * @return
     */
    public static String buildAloneImprisonBusTraceContent(AloneImprisonDO entity,  String jgryxm){
        JSONObject content = new JSONObject();
        content.put("在押人员", jgryxm);
        content.put("单独关押开始时间", entity.getStartTime());
        if(entity.getEndTime() != null) content.put("单独关押结束时间", entity.getEndTime());
        content.put("原监室号", entity.getOldRoomId());
        content.put("单独关押监室号", entity.getNewRoomId());
        content.put("单独关押原因", DicUtils.translate("ZD_DDGY_DJYY",entity.getRegisterReason()));
        return content.toJSONString();
    }
    /**
     * 构建禁闭业务日志
     * @param entity
     * @param jgryxm
     * @return
     */
    public static String buildConfinementBusTraceContent(ConfinementRegDO entity,String jgryxm) {
        JSONObject content = new JSONObject();
        content.put("在押人员", jgryxm);
        content.put("禁闭开始时间", entity.getConfinementStartDate());
        content.put("禁闭结束时间", entity.getConfinementEndDate());
        content.put("原监室号", entity.getOriginalRoomId());
        content.put("禁闭监室号", entity.getRoomId());
        content.put("禁闭原因", DicUtils.translate("ZD_GJYW_JBYY",entity.getConfinementReason()));
        return content.toJSONString();
    }


    /**
     * 信息布建
     * <AUTHOR>
     * @date 2025/6/14 21:02
     * @param [entity, jgryxm]
     * @return java.lang.String
     */
    public static String buildUndercoverBusTraceContent(UndercoverDO entity, String jgryxm) {
        JSONObject content = new JSONObject();
        content.put("在押人员", jgryxm);
        content.put("布建理由", DicUtils.translate("ZD_GJXXYBJLY",entity.getArrangeReason()) );
        content.put("承办民警", entity.getApproverXm());
        content.put("审批通过时间", DateUtil.formatDateTime(entity.getApproverTime()));
        content.put("布建时间", StrUtil.format("{}~{}", DateUtil.formatDateTime(entity.getStartTime()), DateUtil.formatDateTime(entity.getEndTime())));
        return content.toJSONString();
    }


    /**
     * 信息员撤销
     * <AUTHOR>
     * @date 2025/6/14 21:33
     * @param [entity, jgryxm]
     * @return java.lang.String
     */
    public static String buildUndercoverCanalBusTraceContent(UndercoverCancelDO entity, UndercoverDO entityMaster, String jgryxm) {
        JSONObject content = new JSONObject();
        content.put("在押人员", jgryxm);
        content.put("撤销理由", DicUtils.translate("ZD_GJXXYBJCX",entity.getCancelReason()) );
        content.put("承办民警", entity.getApproverXm());
        content.put("审批通过时间", DateUtil.formatDateTime(entity.getApproverTime()));
        content.put("布建时间", StrUtil.format("{}~{}", DateUtil.formatDateTime(entityMaster.getStartTime()), DateUtil.formatDateTime(entityMaster.getEndTime())));
        return content.toJSONString();
    }



    /**
     * 监室调整
     * <AUTHOR>
     * @date 2025/6/14 21:47
     * @param [entity, jgryxm]
     * @return java.lang.String
     */
    public static String buildRoomChangeBusTraceContent(PrisonRoomChangeDO entity, String jgryxm) {
        JSONObject content = new JSONObject();
        content.put("在押人员", jgryxm);
        content.put("原监室", entity.getOldRoomName() );
        content.put("新监室时间", DateUtil.formatDateTime(entity.getReturnTime()));
        content.put("调整时间", DateUtil.formatDateTime(entity.getRoomChangeTime()));
        content.put("调整事由", entity.getChangeReason());
        return content.toJSONString();
    }

    /**
     * 监室调整-调入监室
     * <AUTHOR>
     * @date 2025/6/15 16:17
     * @param [entity, jgryxm]
     * @return java.lang.String
     */
    public static String buildRoomChangeInBusTraceContent(PrisonRoomChangeDO entity, String jgryxm) {
        JSONObject content = new JSONObject();
        content.put("在押人员", jgryxm);
        content.put("原监室", entity.getOldRoomName() );
        content.put("新监室时间", DateUtil.formatDateTime(entity.getReturnTime()));
        return content.toJSONString();
    }

    /**
     * 监室调整-调出监室
     * <AUTHOR>
     * @date 2025/6/15 16:17
     * @param [entity, jgryxm]
     * @return java.lang.String
     */
    public static String buildRoomChangeOutBusTraceContent(PrisonRoomChangeDO entity, String jgryxm) {
        JSONObject content = new JSONObject();
        content.put("在押人员", jgryxm);
        content.put("新监室", entity.getNewRoomName() );
        content.put("新监室时间", DateUtil.formatDateTime(entity.getEscortingTime()));
        return content.toJSONString();
    }


    /**
     * 处罚和严管内容
     * <AUTHOR>
     * @date 2025/6/15 15:54
     * @param [entity]
     * @return java.lang.String
     */
    public static String buildPunishmentBusTraceContent(PunishmentDO entity) {
        JSONObject content = new JSONObject();
        content.put("在押人员", entity.getJgryxm());
        String name = entity.getSfyg() != null && entity.getSfyg() == 1 ? "惩罚" : "严管";
        if(entity.getSfyg() != null && entity.getSfyg() == 0){
            content.put("在押人员", entity.getJgryxm());
        }
        String reason = entity.getSfyg() != null && 1 == entity.getSfyg() ? DicUtils.translate("ZD_GJYGYY", entity.getReason()) : DicUtils.translate("ZD_GJCFYY", entity.getReason());

        content.put( StrUtil.format("{}周期", name) , StrUtil.format("{}~{}", DateUtil.formatDate(entity.getStartDate()) ,DateUtil.formatDate(entity.getEndDate()) ) );
        content.put(StrUtil.format("{}原因", name),  reason);
        content.put(StrUtil.format("{}内容", name), DicUtils.translate("ZD_GJCFNR",entity.getMeasures()));
        return content.toJSONString();
    }

    /**
     * 戒具使用通过后审批
     * <AUTHOR>
     * @date 2025/6/18 15:31
     * @param [entity, jgryxm]
     * @return java.lang.String
     */
    public static String buildEquipmentUseBusTraceContent(EquipmentUseDO entity,String jgryxm) {
        JSONObject content = new JSONObject();
        content.put("在押人员", jgryxm);
        content.put("戒具类型", "");
        if(StrUtil.isNotBlank(entity.getPunishmentToolType())){
            String name = DicUtils.translate(entity.getPunishmentToolType(), "ZD_GJ_EQUIPMENT_TYPE") ;
            content.put("戒具类型", name);
        }
        content.put("使用天数", entity.getUseDays());
        content.put("戒具使用时间", StrUtil.format("{}~{}", DateUtil.formatDate(entity.getStartTime()) ,DateUtil.formatDate(entity.getEndTime()) ) );
        content.put("戒具使用原因",  entity.getUseReason());
        return content.toJSONString();
    }

    /**
     * 戒具解除
     * @param entity
     * @param jgryxm
     * @return
     */
    public static String buildEquipmentUseCanalBusTraceContent(EquipmentUseDO entity,EquipmentUseRemoveDO removeDO,String jgryxm) {
        JSONObject content = new JSONObject();
        content.put("在押人员", jgryxm);
        content.put("解除原因", removeDO.getRemoveReason());
        content.put("使用天数", entity.getUseDays());
        content.put("戒具使用时间", StrUtil.format("{}~{}", DateUtil.formatDate(entity.getStartTime()) ,DateUtil.formatDate(entity.getEndTime()) ) );
        content.put("戒具解除时间",  removeDO.getRemoveTime());
        return content.toJSONString();
    }


    /**
     * 奖励管理
     * <AUTHOR>
     * @date 2025/7/7 9:37
     * @param [entity]
     * @return java.lang.String
     */
    public static String buildRewardBusTraceContent(RewardDO entity) {
        JSONObject content = new JSONObject();
        content.put("在押人员", entity.getJgryxm());
        content.put("奖励时间", DateUtil.formatDateTime(entity.getExecutionTime()));
        content.put("奖励类型", DicUtils.translate( "ZD_JLLX", entity.getRewardType()) );
        content.put("奖励原因", entity.getRewardReason() );
        return content.toJSONString();
    }

    /**
     * 风险评估信息
     * <AUTHOR>
     * @date 2025/6/25 21:05
     * @param [entity, jgryxm, iskss]
     * @return java.lang.String
     */
    public static String buildGjRiskAssmtBusTraceContent(GjRiskAssmtDO entity,String jgryxm,Boolean isjls) {
        JSONObject content = new JSONObject();
        content.put("在押人员", jgryxm);
        content.put("原风险等级", DicUtils.translate( "ZD_GJ_FXPG_LEVEL",entity.getOldRiskLevel() ));
        content.put("新风险等级",  DicUtils.translate( "ZD_GJ_FXPG_LEVEL",entity.getRiskLevel() ));
        content.put("风险等级调整时间", DateUtil.formatDate(entity.getApproverTime()) );
        if(isjls){
            content.put("评估理由",   DicUtils.translate( "GJ_FXPG_JLS_REASON",entity.getRiskLevel() ));
        } else {
            content.put("评估理由",   DicUtils.translate( "ZD_GJ_FXPG_LEVEL",entity.getRiskLevel() ));
        }
        return content.toJSONString();
    }


}
