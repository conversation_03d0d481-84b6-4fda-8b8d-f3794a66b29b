package com.rs.module.acp.controller.admin.gj.vo.face2face;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-管教业务-面对面配置列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FaceToFaceConfigListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("检查项目")
    private String checkItem;

    @ApiModelProperty("是否禁用")
    private Short isDisabled;

    @ApiModelProperty("机构编码")
    private String orgCode;

    @ApiModelProperty("分局编码")
    private String regCode;

    @ApiModelProperty("市局编码")
    private String cityCode;

}
