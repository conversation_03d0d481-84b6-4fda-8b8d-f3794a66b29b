package com.rs.module.acp.controller.admin.db.vo.jxjsdj;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-减刑登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class JxjsdjSngsReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("所内公示开始日期")
    private Date sngsksrq;

    @ApiModelProperty("所内公示截止日期")
    private Date sngsjsrq;

    @ApiModelProperty("是否收到异议（1、是；2、否）")
    private String sfsdyy;

    @ApiModelProperty("所内公示备注")
    private String sngsbz;

    @ApiModelProperty("所内公示材料")
    private String sngscl;



}
