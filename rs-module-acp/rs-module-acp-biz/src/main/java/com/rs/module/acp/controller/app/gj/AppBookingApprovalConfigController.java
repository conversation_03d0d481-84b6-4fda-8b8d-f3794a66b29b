package com.rs.module.acp.controller.app.gj;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalConfigListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalConfigPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalConfigRespVO;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalConfigSaveReqVO;
import com.rs.module.acp.entity.gj.BookingApprovalConfigDO;
import com.rs.module.acp.service.gj.bookingapproval.BookingApprovalConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管教业务-预约审核配置-App端")
@RestController
@RequestMapping("/app/acp/gj/bookingApprovalConfig")
@Validated
public class AppBookingApprovalConfigController {

    @Resource
    private BookingApprovalConfigService bookingApprovalConfigService;


    @PostMapping("/batchSaveOrUpdate")
    @ApiOperation(value = "管家业务-预约审核-启用开关控制")
    public CommonResult<Boolean> batchSaveOrUpdate(@Valid @RequestBody List<BookingApprovalConfigSaveReqVO> createReqVOList) {
        boolean flag = bookingApprovalConfigService.batchSaveOrUpdate(createReqVOList);
        return success(flag);
    }


    @GetMapping("/get")
    @ApiOperation(value = "管家业务-预约审核-详情获取")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<BookingApprovalConfigRespVO> getBookingApprovalConfig(@RequestParam("id") String id) {
        BookingApprovalConfigDO bookingApprovalConfig = bookingApprovalConfigService.getBookingApprovalConfig(id);
        return success(BeanUtils.toBean(bookingApprovalConfig, BookingApprovalConfigRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "管教业务-预约审核配置分页")
    public CommonResult<PageResult<BookingApprovalConfigRespVO>> getBookingApprovalConfigPage(@Valid @RequestBody BookingApprovalConfigPageReqVO pageReqVO) {
        PageResult<BookingApprovalConfigDO> pageResult = bookingApprovalConfigService.getBookingApprovalConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BookingApprovalConfigRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "管教业务-预约审核配置列表")
    public CommonResult<List<BookingApprovalConfigRespVO>> getBookingApprovalConfigList(@Valid @RequestBody BookingApprovalConfigListReqVO listReqVO) {
        List<BookingApprovalConfigRespVO> list = bookingApprovalConfigService.getBookingApprovalConfigList(listReqVO);
        return success(BeanUtils.toBean(list, BookingApprovalConfigRespVO.class));
    }


}
