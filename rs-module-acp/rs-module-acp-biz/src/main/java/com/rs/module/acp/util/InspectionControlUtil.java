package com.rs.module.acp.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.StringUtil;
import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.UserApi;
import com.rs.adapter.bsp.api.dto.UserRespDTO;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.acp.cons.CommonConstants;
import com.rs.module.acp.entity.pi.PatrolRecordDO;
import com.rs.module.acp.entity.pi.ViolationRecordDO;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 巡视管控工具
 */
@Slf4j
public class InspectionControlUtil {
    private final UserApi userApi;

    public InspectionControlUtil(UserApi userApi){
        this.userApi = userApi;
    }

    /**
     * 发送巡视登记消息
     * @param entity 巡视记录实体
     * @param listUser 接收用户列表
     */
    public void sendMessageByPatrolRecordDO(PatrolRecordDO entity, List<UserRespDTO> listUser) {
        if (entity == null || StringUtil.isEmpty(entity.getPushTargetIdCard()) || StringUtil.isEmpty(entity.getPushContent())) {
            return;
        }

        sendMessageCommon(
            entity.getId(),
            entity.getOperatorSfzh(),
            entity.getOperatorXm(),
            entity.getOrgCode(),
            entity.getOrgName(),
            "巡视登记岗位协同",
            entity.getPushContent(),
            entity.getId(),
            listUser
        );
    }

    /**
     * 发送违规登记消息
     * @param entity 违规记录实体
     * @param listUser 接收用户列表
     */
    public void sendMessageByViolationRecordDO(ViolationRecordDO entity, List<UserRespDTO> listUser) {
        if (entity == null || StringUtil.isEmpty(entity.getPushTargetIdCard()) || StringUtil.isEmpty(entity.getPushContent())) {
            return;
        }

        sendMessageCommon(
            entity.getId(),
            entity.getOperatorSfzh(),
            entity.getOperatorXm(),
            entity.getOrgCode(),
            entity.getOrgName(),
            "违规登记岗位协同",
            entity.getPushContent(),
            entity.getId(),
            listUser
        );
    }

    /**
     * 发送通用消息
     * @param pcid 消息ID
     * @param fUser 发送人身份证
     * @param fUserName 发送人姓名
     * @param fOrgCode 发送人机构代码
     * @param fOrgName 发送人机构名称
     * @param title 消息标题
     * @param pushContent 消息内容
     * @param id 业务ID
     * @param listUser 用户列表
     */
    private void sendMessageCommon(String pcid, String fUser, String fUserName, String fOrgCode, String fOrgName,
                                   String title, String pushContent, String id, List<UserRespDTO> listUser) {
        try {
            String content = pushContent;
            String url = "";
            String fXxpt = "pc";
            String ywbh = id;
            List<ReceiveUser> receiveUserList = Collections.emptyList();

            if (listUser != null && !listUser.isEmpty()) {
                receiveUserList = listUser.stream()
                    .filter(Objects::nonNull)
                    .map(e -> new ReceiveUser(e.getIdCard(), e.getOrgCode()))
                    .collect(Collectors.toList());
            }

            SendMessageUtil.sendTodoMsg(title, content, url, HttpUtils.getAppCode(), fUser, fUserName,
                fOrgCode, fOrgName, "", pcid, fXxpt, ywbh, receiveUserList, HttpUtils.getAppCode());
        } catch (Exception e) {
            log.error("发送消息失败", e);
        }
    }

    /**
     * 获取发送消息的用户列表
     * @param coordinationPosts 协同岗位
     * @param pushTargetIdCard 推送目标身份证
     * @return 用户列表
     * @throws Exception 异常
     */
    public List<UserRespDTO> getSendMsgUserList(String coordinationPosts, String pushTargetIdCard) throws Exception {
        if (coordinationPosts == null) {
            log.warn("协调岗位信息为空");
            return Collections.emptyList();
        }
        List<UserRespDTO> list = new ArrayList<>();
        if (coordinationPosts.contains("06")) {
            list.addAll(getSendMsgUserListByPushTargetIdCard(pushTargetIdCard));
        }
        list.addAll(getSendMsgUserList(coordinationPosts));
        return list;
    }

    /**
     * 根据协同岗位获取用户列表
     * @param coordinationPosts 协同岗位
     * @return 用户列表
     * @throws Exception 异常
     */
    public List<UserRespDTO> getSendMsgUserList(String coordinationPosts) throws Exception {
        List<UserRespDTO> listUser = new ArrayList<>();

        if (StringUtil.isEmpty(coordinationPosts)) {
            return listUser;
        }

        Set<String> processedIds = new HashSet<>();

        for (String coordinationPost : coordinationPosts.split(",")) {
            if(coordinationPost.equals("06")) continue;
            List<UserRespDTO> userList = getUserListByCoordinationPosts(coordinationPost);
            for (UserRespDTO user : userList) {
                if (user != null && !processedIds.contains(user.getIdCard())) {
                    listUser.add(user);
                    processedIds.add(user.getIdCard());
                }
            }
        }

        return listUser;
    }

    /**
     * 根据推送目标身份证获取用户列表
     * @param pushTargetIdCard 推送目标身份证
     * @return 用户列表
     * @throws Exception 异常
     */
    public List<UserRespDTO> getSendMsgUserListByPushTargetIdCard(String pushTargetIdCard) throws Exception {
        List<UserRespDTO> listUser = new ArrayList<>();

        if (StringUtil.isEmpty(pushTargetIdCard)) {
            return listUser;
        }

        try {
            JSONArray jsonArray = JSON.parseArray(pushTargetIdCard);

            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                UserRespDTO user = new UserRespDTO();
                user.setIdCard(jsonObject.getString("idCard"));
                user.setOrgCode(jsonObject.getString("orgCode"));
                listUser.add(user);
            }
        } catch (Exception e) {
            log.error("解析推送目标身份证JSON失败: {}", pushTargetIdCard, e);
            throw new IllegalArgumentException("推送目标身份证JSON格式错误");
        }

        return listUser;
    }

    /**
     * 处理岗位协同消息
     * @param entity 巡视记录实体
     */
    public void dealIsPostCoordination(PatrolRecordDO entity) {
        if (entity == null || "0".equals(entity.getIsPostCoordination())) {
            return;
        }

        try {
            List<UserRespDTO> listUser = getSendMsgUserList(entity.getCoordinationPosts(), entity.getPushTargetIdCard());
            sendMessageByPatrolRecordDO(entity, listUser);
        } catch (Exception e) {
            log.error("处理巡视记录岗位协同消息失败", e);
        }
    }

    /**
     * 处理岗位协同消息
     * @param entity 违规记录实体
     */
    public void dealIsPostCoordination(ViolationRecordDO entity) {
        if (entity == null || "0".equals(entity.getIsPostCoordination())) {
            return;
        }

        try {
            List<UserRespDTO> listUser = getSendMsgUserList(entity.getCoordinationPosts(), entity.getPushTargetIdCard());
            sendMessageByViolationRecordDO(entity, listUser);
        } catch (Exception e) {
            log.error("处理违规记录岗位协同消息失败", e);
        }
    }

    /**
     * 根据协同岗位获取用户列表
     * @param coordinationPostsCode 协同岗位编码
     * @return 用户列表
     * @throws Exception 异常
     */
    public List<UserRespDTO> getUserListByCoordinationPosts(String coordinationPostsCode) throws Exception {
        if (StringUtil.isEmpty(coordinationPostsCode) || "06".equals(coordinationPostsCode)) {
            throw new IllegalArgumentException("请选择正确的岗位编码");
        }

        List<UserRespDTO> list = new ArrayList<>();

        switch (coordinationPostsCode) {
            case "01": // 值班民警 - 警力分布 实时值班
                // TODO: 实现具体的业务逻辑
                break;
            case "02": // 值班所领导 - 所内交接班
                list = userApi.getUserByNowUserOrgAndPost("06");
                break;
            case "03": // 巡控民警
                list = userApi.getUserByNowUserOrgAndPost("02");
                break;
            case "04": // 医务人员
                list = userApi.getUserByNowUserOrgAndPost("04");
                break;
            case "05": // 所领导
                list = userApi.getUserByNowUserOrgAndPost("06");
                break;
            case "06": // 指定人员 - 使用传入参数作为 jgrybm 或 sfzh 查询
                // TODO: 实现具体的业务逻辑
                break;
            default:
                log.warn("未知的岗位编码: {}", coordinationPostsCode);
        }

        // 剔除当前用户
        String currentUserId = SessionUserUtil.getSessionUser().getIdCard();
        if (currentUserId != null) {
            list.removeIf(user -> currentUserId.equals(user.getIdCard()));
        }

        // todo 剔除超管

        return list;
    }
}
