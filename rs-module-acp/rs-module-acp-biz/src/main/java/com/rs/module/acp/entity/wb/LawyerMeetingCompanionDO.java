package com.rs.module.acp.entity.wb;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-窗口业务-律师会见同行登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_wb_lawyer_meeting_companion")
@KeySequence("acp_wb_lawyer_meeting_companion_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LawyerMeetingCompanionDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 律师会见ID
     */
    private String lawyerMeetingId;
    /**
     * 第一位律师姓名
     */
    private String name;
    /**
     * 第一位律师性别
     */
    private String gender;
    /**
     * 第一位律师证件号码
     */
    private String idCard;
    /**
     * 同行人类别
     */
    private String companionType;
    /**
     * 附件URL
     */
    private String attachmentUrl;

}
