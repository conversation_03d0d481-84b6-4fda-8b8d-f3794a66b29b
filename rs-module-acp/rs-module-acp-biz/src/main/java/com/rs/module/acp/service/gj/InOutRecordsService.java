package com.rs.module.acp.service.gj;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsSaveReqVO;
import com.rs.module.acp.controller.app.gj.vo.inoutrecords.InOutRecordsBatchSaveReqVO;
import com.rs.module.acp.controller.app.gj.vo.inoutrecords.InOutStatisticVO;
import com.rs.module.acp.controller.app.gj.vo.inoutrecords.PrisonerInOutBusinessTypeRespVO;
import com.rs.module.acp.entity.gj.InOutRecordsDO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerInVwRespVO;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 实战平台-管教业务-出入登记 Service 接口
 *
 * <AUTHOR>
 */
public interface InOutRecordsService extends IBaseService<InOutRecordsDO> {

    /**
     * 创建实战平台-管教业务-出入登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createInOutRecords(@Valid InOutRecordsSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-出入登记
     *
     * @param updateReqVO 更新信息
     */
    void updateInOutRecords(@Valid InOutRecordsSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-出入登记
     *
     * @param id 编号
     */
    void deleteInOutRecords(String id);

    /**
     * 获得实战平台-管教业务-出入登记
     *
     * @param id 编号
     * @return 实战平台-管教业务-出入登记
     */
    InOutRecordsDO getInOutRecords(String id);

    /**
     * 获得实战平台-管教业务-出入登记分页
     *
     * @param pageReqVO 分页查询
     * @return 实战平台-管教业务-出入登记分页
     */
    PageResult<InOutRecordsDO> getInOutRecordsPage(InOutRecordsPageReqVO pageReqVO);

    /**
     * 获得实战平台-管教业务-出入登记列表
     *
     * @param listReqVO 查询条件
     * @return 实战平台-管教业务-出入登记列表
     */
    List<InOutRecordsDO> getInOutRecordsList(InOutRecordsListReqVO listReqVO);


    String initRecordsCommon(String jgrybm, String roomId, String businessType, String businessId, String inoutType, String businessSubType);

    String initRecordsCommon(String jgrybm, String roomId, String businessType, String businessId, String inoutType);

    String initInRecords(String jgrybm, String roomId, String businessType, String businessId, String businessSubType);

    String initInRecords(String jgrybm, String roomId, String businessType, String businessId);

    String initOutRecords(String jgrybm, String roomId, String businessType, String businessId, String businessSubType);

    String initOutRecords(String jgrybm, String roomId, String businessType, String businessId);

    String initInRecords(InOutRecordsSaveReqVO outRecordsDO);

    //带出记录保存
    String saveOutRecordsAndInitInRecords(InOutRecordsSaveReqVO outRecordsDO);

    String acpSaveInOutRecords(InOutRecordsSaveReqVO vo, String inRoomId, Date inTime);

    List<InOutRecordsDO> getListInOutRecordsByJgrybm(String jgrybm, String businessId, String inoutType);

    //查询人员当天等于addtime 年月日的带出记录事项
    List<InOutRecordsDO> getListInOutRecordsByJgrybm(String jgrybm);

    List<InOutRecordsDO> getListInOutRecordsByRoomId(String roomId, String inoutType);

    List<PrisonerInOutBusinessTypeRespVO> getPaddingRoomInOutRecords(String orgCode, String roomId, String inoutType);

    InOutStatisticVO statisticNum(String orgCode, String roomId);

    String saveOutRecordsAndInitInRecords(InOutRecordsBatchSaveReqVO batchSaveReqVO) throws Exception;

    String saveInRecords(InOutRecordsBatchSaveReqVO batchSaveReqVO) throws Exception;

    /**
     * 获取监管人员,不包括外出人员
     *
     * @param orgCode
     * @param roomId
     * @return
     */
    List<PrisonerInVwRespVO> getOutRyJgrybm(String orgCode, String roomId);
}
