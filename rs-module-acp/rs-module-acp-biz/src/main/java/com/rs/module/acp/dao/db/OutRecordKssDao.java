package com.rs.module.acp.dao.db;

import java.util.*;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.db.OutRecordKssDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

import javax.validation.constraints.NotEmpty;

/**
* 实战平台-羁押业务-出所登记（看守所） Dao
*
* <AUTHOR>
*/
@Mapper
public interface OutRecordKssDao extends IBaseDao<OutRecordKssDO> {


    default PageResult<OutRecordKssDO> selectPage(OutRecordKssPageReqVO reqVO) {
        Page<OutRecordKssDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<OutRecordKssDO> wrapper = new LambdaQueryWrapperX<OutRecordKssDO>()
            .eqIfPresent(OutRecordKssDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(OutRecordKssDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(OutRecordKssDO::getPzzxjg, reqVO.getPzzxjg())
            .eqIfPresent(OutRecordKssDO::getPzzxr, reqVO.getPzzxr())
            .eqIfPresent(OutRecordKssDO::getPzrq, reqVO.getPzrq())
            .eqIfPresent(OutRecordKssDO::getCssj, reqVO.getCssj())
            .eqIfPresent(OutRecordKssDO::getCsyy, reqVO.getCsyy())
            .eqIfPresent(OutRecordKssDO::getCsqx, reqVO.getCsqx())
            .eqIfPresent(OutRecordKssDO::getCspz, reqVO.getCspz())
            .eqIfPresent(OutRecordKssDO::getCspzwsh, reqVO.getCspzwsh())
            .eqIfPresent(OutRecordKssDO::getCspzwsdz, reqVO.getCspzwsdz())
            .eqIfPresent(OutRecordKssDO::getCwjjqk, reqVO.getCwjjqk())
            .eqIfPresent(OutRecordKssDO::getCwjjst, reqVO.getCwjjst())
            .eqIfPresent(OutRecordKssDO::getDaclyjqk, reqVO.getDaclyjqk())
            .eqIfPresent(OutRecordKssDO::getDaclyjst, reqVO.getDaclyjst())
            .eqIfPresent(OutRecordKssDO::getSfjcfxshhzwp, reqVO.getSfjcfxshhzwp())
            .eqIfPresent(OutRecordKssDO::getJcfxsxhzwpqkjl, reqVO.getJcfxsxhzwpqkjl())
            .eqIfPresent(OutRecordKssDO::getSxhzwpzp, reqVO.getSxhzwpzp())
            .eqIfPresent(OutRecordKssDO::getSfwtrsdwp, reqVO.getSfwtrsdwp())
            .eqIfPresent(OutRecordKssDO::getSdwplx, reqVO.getSdwplx())
            .eqIfPresent(OutRecordKssDO::getSfybzqtrcgxw, reqVO.getSfybzqtrcgxw())
            .eqIfPresent(OutRecordKssDO::getXxqkjl, reqVO.getXxqkjl())
            .eqIfPresent(OutRecordKssDO::getSffxqtwffzxs, reqVO.getSffxqtwffzxs())
            .eqIfPresent(OutRecordKssDO::getQtwffzxw, reqVO.getQtwffzxw())
            .eqIfPresent(OutRecordKssDO::getTbbajgjbajghfqk, reqVO.getTbbajgjbajghfqk())
            .eqIfPresent(OutRecordKssDO::getZqgajsbm, reqVO.getZqgajsbm())
            .eqIfPresent(OutRecordKssDO::getZqgajsmc, reqVO.getZqgajsmc())
            .eqIfPresent(OutRecordKssDO::getJbrsfzh, reqVO.getJbrsfzh())
            .eqIfPresent(OutRecordKssDO::getJbr, reqVO.getJbr())
            .eqIfPresent(OutRecordKssDO::getJbsj, reqVO.getJbsj())
            .eqIfPresent(OutRecordKssDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(OutRecordKssDO::getSpzt, reqVO.getSpzt())
            .eqIfPresent(OutRecordKssDO::getStatus, reqVO.getStatus())
            .eqIfPresent(OutRecordKssDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(OutRecordKssDO::getTaskId, reqVO.getTaskId())
            .eqIfPresent(OutRecordKssDO::getShid, reqVO.getShid())
            .eqIfPresent(OutRecordKssDO::getShbdzt, reqVO.getShbdzt())
            .eqIfPresent(OutRecordKssDO::getSdjcbdsj, reqVO.getSdjcbdsj())
            .eqIfPresent(OutRecordKssDO::getLsqrzt, reqVO.getLsqrzt())
            .eqIfPresent(OutRecordKssDO::getLsqrqx, reqVO.getLsqrqx())
            .eqIfPresent(OutRecordKssDO::getLsqrbz, reqVO.getLsqrbz())
            .eqIfPresent(OutRecordKssDO::getLsqrjbrsfzh, reqVO.getLsqrjbrsfzh())
            .eqIfPresent(OutRecordKssDO::getLsqrjbr, reqVO.getLsqrjbr())
            .eqIfPresent(OutRecordKssDO::getLsqrjbsj, reqVO.getLsqrjbsj())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(OutRecordKssDO::getAddTime);
        }
        Page<OutRecordKssDO> outRecordKssPage = selectPage(page, wrapper);
        return new PageResult<>(outRecordKssPage.getRecords(), outRecordKssPage.getTotal());
    }
    default List<OutRecordKssDO> selectList(OutRecordKssListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<OutRecordKssDO>()
            .eqIfPresent(OutRecordKssDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(OutRecordKssDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(OutRecordKssDO::getPzzxjg, reqVO.getPzzxjg())
            .eqIfPresent(OutRecordKssDO::getPzzxr, reqVO.getPzzxr())
            .eqIfPresent(OutRecordKssDO::getPzrq, reqVO.getPzrq())
            .eqIfPresent(OutRecordKssDO::getCssj, reqVO.getCssj())
            .eqIfPresent(OutRecordKssDO::getCsyy, reqVO.getCsyy())
            .eqIfPresent(OutRecordKssDO::getCsqx, reqVO.getCsqx())
            .eqIfPresent(OutRecordKssDO::getCspz, reqVO.getCspz())
            .eqIfPresent(OutRecordKssDO::getCspzwsh, reqVO.getCspzwsh())
            .eqIfPresent(OutRecordKssDO::getCspzwsdz, reqVO.getCspzwsdz())
            .eqIfPresent(OutRecordKssDO::getCwjjqk, reqVO.getCwjjqk())
            .eqIfPresent(OutRecordKssDO::getCwjjst, reqVO.getCwjjst())
            .eqIfPresent(OutRecordKssDO::getDaclyjqk, reqVO.getDaclyjqk())
            .eqIfPresent(OutRecordKssDO::getDaclyjst, reqVO.getDaclyjst())
            .eqIfPresent(OutRecordKssDO::getSfjcfxshhzwp, reqVO.getSfjcfxshhzwp())
            .eqIfPresent(OutRecordKssDO::getJcfxsxhzwpqkjl, reqVO.getJcfxsxhzwpqkjl())
            .eqIfPresent(OutRecordKssDO::getSxhzwpzp, reqVO.getSxhzwpzp())
            .eqIfPresent(OutRecordKssDO::getSfwtrsdwp, reqVO.getSfwtrsdwp())
            .eqIfPresent(OutRecordKssDO::getSdwplx, reqVO.getSdwplx())
            .eqIfPresent(OutRecordKssDO::getSfybzqtrcgxw, reqVO.getSfybzqtrcgxw())
            .eqIfPresent(OutRecordKssDO::getXxqkjl, reqVO.getXxqkjl())
            .eqIfPresent(OutRecordKssDO::getSffxqtwffzxs, reqVO.getSffxqtwffzxs())
            .eqIfPresent(OutRecordKssDO::getQtwffzxw, reqVO.getQtwffzxw())
            .eqIfPresent(OutRecordKssDO::getTbbajgjbajghfqk, reqVO.getTbbajgjbajghfqk())
            .eqIfPresent(OutRecordKssDO::getZqgajsbm, reqVO.getZqgajsbm())
            .eqIfPresent(OutRecordKssDO::getZqgajsmc, reqVO.getZqgajsmc())
            .eqIfPresent(OutRecordKssDO::getJbrsfzh, reqVO.getJbrsfzh())
            .eqIfPresent(OutRecordKssDO::getJbr, reqVO.getJbr())
            .eqIfPresent(OutRecordKssDO::getJbsj, reqVO.getJbsj())
            .eqIfPresent(OutRecordKssDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(OutRecordKssDO::getSpzt, reqVO.getSpzt())
            .eqIfPresent(OutRecordKssDO::getStatus, reqVO.getStatus())
            .eqIfPresent(OutRecordKssDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(OutRecordKssDO::getTaskId, reqVO.getTaskId())
            .eqIfPresent(OutRecordKssDO::getShid, reqVO.getShid())
            .eqIfPresent(OutRecordKssDO::getShbdzt, reqVO.getShbdzt())
            .eqIfPresent(OutRecordKssDO::getSdjcbdsj, reqVO.getSdjcbdsj())
            .eqIfPresent(OutRecordKssDO::getLsqrzt, reqVO.getLsqrzt())
            .eqIfPresent(OutRecordKssDO::getLsqrqx, reqVO.getLsqrqx())
            .eqIfPresent(OutRecordKssDO::getLsqrbz, reqVO.getLsqrbz())
            .eqIfPresent(OutRecordKssDO::getLsqrjbrsfzh, reqVO.getLsqrjbrsfzh())
            .eqIfPresent(OutRecordKssDO::getLsqrjbr, reqVO.getLsqrjbr())
            .eqIfPresent(OutRecordKssDO::getLsqrjbsj, reqVO.getLsqrjbsj())
        .orderByDesc(OutRecordKssDO::getAddTime));    }


    void inserToOut(@NotEmpty(message = "监管人员编码不能为空") String jgrybm);

    void deleteFromIn(@NotEmpty(message = "监管人员编码不能为空") String jgrybm);

    @InterceptorIgnore(illegalSql = "true")
    InRecordStatusVO getInRecordStatus(String rybh);
}
