package com.rs.module.acp.entity.pi;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-巡视管控-所情事件在押人员 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_prison_event_prisoner")
@KeySequence("acp_pi_prison_event_prisoner_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_prison_event_prisoner")
public class PrisonEventPrisonerDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 所情事件ID
     */
    private String eventId;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 扣分值
     */
    private Integer deductPoint;
    /**
     * 应扣分值
     */
    private Integer shouldDeductPoint;
    /**
     * 监室号
     */
    private String roomId;
    /**
     * 监室名称
     */
    private String roomName;

}
