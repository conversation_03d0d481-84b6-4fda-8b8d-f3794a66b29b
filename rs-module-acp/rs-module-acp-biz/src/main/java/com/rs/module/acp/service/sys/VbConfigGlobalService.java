package com.rs.module.acp.service.sys;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.sys.vo.*;
import com.rs.module.acp.entity.sys.VbConfigGlobalDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-语音播报-全局配置 Service 接口
 *
 * <AUTHOR>
 */
public interface VbConfigGlobalService extends IBaseService<VbConfigGlobalDO>{

    /**
     * 创建实战平台-语音播报-全局配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createVbConfigGlobal(@Valid VbConfigGlobalSaveReqVO createReqVO);

    /**
     * 更新实战平台-语音播报-全局配置
     *
     * @param updateReqVO 更新信息
     */
    void updateVbConfigGlobal(@Valid VbConfigGlobalSaveReqVO updateReqVO);

    /**
     * 删除实战平台-语音播报-全局配置
     *
     * @param id 编号
     */
    void deleteVbConfigGlobal(String id);

    /**
     * 获得实战平台-语音播报-全局配置
     *
     * @param id 编号
     * @return 实战平台-语音播报-全局配置
     */
    VbConfigGlobalDO getVbConfigGlobal(String id);

    /**
    * 获得实战平台-语音播报-全局配置分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-语音播报-全局配置分页
    */
    PageResult<VbConfigGlobalDO> getVbConfigGlobalPage(VbConfigGlobalPageReqVO pageReqVO);

    /**
    * 获得实战平台-语音播报-全局配置列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-语音播报-全局配置列表
    */
    List<VbConfigGlobalDO> getVbConfigGlobalList(VbConfigGlobalListReqVO listReqVO);

    /**
     * 获得实战平台-语音播报-全局配置
     * @param orgCode 机构编号
     * @return VbConfigGlobalDO
     */
    VbConfigGlobalDO getConfig(String orgCode);
}
