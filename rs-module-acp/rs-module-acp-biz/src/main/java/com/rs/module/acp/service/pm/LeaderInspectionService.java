package com.rs.module.acp.service.pm;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.LeaderInspectionDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 领导巡视 Service 接口
 *
 * <AUTHOR>
 */
public interface LeaderInspectionService extends IBaseService<LeaderInspectionDO>{

    /**
     * 创建领导巡视
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createLeaderInspection(@Valid LeaderInspectionSaveReqVO createReqVO);

    /**
     * 更新领导巡视
     *
     * @param updateReqVO 更新信息
     */
    void updateLeaderInspection(@Valid LeaderInspectionSaveReqVO updateReqVO);

    /**
     * 删除领导巡视
     *
     * @param id 编号
     */
    void deleteLeaderInspection(String id);

    /**
     * 获得领导巡视
     *
     * @param id 编号
     * @return 领导巡视
     */
    LeaderInspectionDO getLeaderInspection(String id);

    /**
    * 获得领导巡视分页
    *
    * @param pageReqVO 分页查询
    * @return 领导巡视分页
    */
    PageResult<LeaderInspectionDO> getLeaderInspectionPage(LeaderInspectionPageReqVO pageReqVO);

    /**
    * 获得领导巡视列表
    *
    * @param listReqVO 查询条件
    * @return 领导巡视列表
    */
    List<LeaderInspectionDO> getLeaderInspectionList(LeaderInspectionListReqVO listReqVO);


}
