package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.LawyerPrisonerDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-律师关联被监管人员 Service 接口
 *
 * <AUTHOR>
 */
public interface LawyerPrisonerService extends IBaseService<LawyerPrisonerDO>{

    /**
     * 创建实战平台-窗口业务-律师关联被监管人员
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createLawyerPrisoner(@Valid LawyerPrisonerSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-律师关联被监管人员
     *
     * @param updateReqVO 更新信息
     */
    void updateLawyerPrisoner(@Valid LawyerPrisonerSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-律师关联被监管人员
     *
     * @param id 编号
     */
    void deleteLawyerPrisoner(String id);

    /**
     * 获得实战平台-窗口业务-律师关联被监管人员
     *
     * @param id 编号
     * @return 实战平台-窗口业务-律师关联被监管人员
     */
    LawyerPrisonerDO getLawyerPrisoner(String id);

    /**
    * 获得实战平台-窗口业务-律师关联被监管人员分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-律师关联被监管人员分页
    */
    PageResult<LawyerPrisonerDO> getLawyerPrisonerPage(LawyerPrisonerPageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-律师关联被监管人员列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-律师关联被监管人员列表
    */
    List<LawyerPrisonerDO> getLawyerPrisonerList(LawyerPrisonerListReqVO listReqVO);

    /**
     * 实战平台-窗口业务-根据律师ID获取律师关联的被监管人员
     *
     * @param lawyerId 律师ID
     * @return 实战平台-窗口业务-根据律师ID获取律师关联的被监管人员
     */
    List<LawyerPrisonerRespVO> getPrisonerListByLawyerId(String lawyerId);
//
//    /**
//     * 实战平台-窗口业务-校验同一个委托人，最多只能有两个委托中的律师
//     *
//     * @param lawyerId 律师ID
//     * @return 实战平台-窗口业务-根据律师ID获取律师关联的被监管人员
//     */
//    void checkLawyerCommission(List<LawyerPrisonerSaveReqVO> saveReqVOList);

    /**
     * 实战平台-窗口业务-律师关联的被监管人员
     *
     * @param lawyerId 律师ID
     * @param prisonerList 关联被监管人员
     * @return 实战平台-窗口业务-根据律师ID获取律师关联的被监管人员
     */
    boolean savePrisonerList(String lawyerId, List<LawyerPrisonerSaveReqVO> prisonerList);

    /**
     * 实战平台-窗口业务-校验同一个委托人，最多只能有两个委托中的律师
     *
     * @param jgrybh 被监管人员编码
     */
    JSONObject checkJgryAssociation(String jgrybh);

    List<JSONObject> getLawyerJsonList(String jgrybm,List<String> laywerIdList);

    /**
     *根据监管人员编码判断是否是同案人员
     * @param jgrybms
     * @return
     */
    JSONObject sameCaseJudgmentByJgrybm(String jgrybms);
}
