package com.rs.module.acp.service.db;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.OutBiometricInfoDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-收押业务-出所生物特征信息 Service 接口
 *
 * <AUTHOR>
 */
public interface OutBiometricInfoService extends IBaseService<OutBiometricInfoDO>{

    /**
     * 创建实战平台-收押业务-出所生物特征信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createOutBiometricInfo(@Valid OutBiometricInfoSaveReqVO createReqVO);

    /**
     * 更新实战平台-收押业务-出所生物特征信息
     *
     * @param updateReqVO 更新信息
     */
    void updateOutBiometricInfo(@Valid OutBiometricInfoSaveReqVO updateReqVO);

    /**
     * 删除实战平台-收押业务-出所生物特征信息
     *
     * @param id 编号
     */
    void deleteOutBiometricInfo(String id);

    /**
     * 获得实战平台-收押业务-出所生物特征信息
     *
     * @param id 编号
     * @return 实战平台-收押业务-出所生物特征信息
     */
    OutBiometricInfoDO getOutBiometricInfo(String id);

    /**
    * 获得实战平台-收押业务-出所生物特征信息分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-收押业务-出所生物特征信息分页
    */
    PageResult<OutBiometricInfoDO> getOutBiometricInfoPage(OutBiometricInfoPageReqVO pageReqVO);

    /**
    * 获得实战平台-收押业务-出所生物特征信息列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-收押业务-出所生物特征信息列表
    */
    List<OutBiometricInfoDO> getOutBiometricInfoList(OutBiometricInfoListReqVO listReqVO);


    List<OutBiometricInfoRespVO> getByJgrybm(String jgrybm);
}
