package com.rs.module.acp.entity.gj;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-耳目反映情况 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_undercover_report")
@KeySequence("acp_gj_undercover_report_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UndercoverReportDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 耳目ID
     */
    private String undercoverId;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 反映时间
     */
    private Date reportTime;
    /**
     * 反映情况
     */
    private String reportInfo;
    /**
     * 备注
     */
    private String remark;

}
