package com.rs.module.acp.entity.zh;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-综合管理-死亡登记文件 DO
 *
 * <AUTHOR>
 */
@TableName("acp_zh_death_register_files")
@KeySequence("acp_zh_death_register_files_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_death_register_files")
public class DeathRegisterFilesDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 对应id
     */
    private String deathRegisterId;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件地址
     */
    private String fileUrl;
    /**
     * 文件后缀
     */
    private String fileSuffix;
    /**
     * 死亡鉴定文件类型 字典 ZD_ZHGL_SWWJLX
     */
    private String deathAppraiseFileType;

}
