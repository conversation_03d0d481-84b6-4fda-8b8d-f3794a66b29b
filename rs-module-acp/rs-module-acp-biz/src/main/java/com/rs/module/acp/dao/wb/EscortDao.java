package com.rs.module.acp.dao.wb;

import java.util.*;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.EscortDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
 * 实战平台-窗口业务-提解登记 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface EscortDao extends IBaseDao<EscortDO> {


    default PageResult<EscortDO> selectPage(EscortPageReqVO reqVO) {
        Page<EscortDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<EscortDO> wrapper = new LambdaQueryWrapperX<EscortDO>()
                .eqIfPresent(EscortDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(EscortDO::getHandler1Id, reqVO.getHandler1Id())
                .eqIfPresent(EscortDO::getHandler1Xm, reqVO.getHandler1Xm())
                .eqIfPresent(EscortDO::getHandler2Id, reqVO.getHandler2Id())
                .eqIfPresent(EscortDO::getHandler2Xm, reqVO.getHandler2Xm())
                .eqIfPresent(EscortDO::getHandler3Id, reqVO.getHandler3Id())
                .eqIfPresent(EscortDO::getHandler3Xm, reqVO.getHandler3Xm())
                .eqIfPresent(EscortDO::getRoomId, reqVO.getRoomId())
                .eqIfPresent(EscortDO::getEscortReason, reqVO.getEscortReason())
                .betweenIfPresent(EscortDO::getApplyEscortDate, reqVO.getApplyEscortDate())
                .eqIfPresent(EscortDO::getDetailReason, reqVO.getDetailReason())
                .eqIfPresent(EscortDO::getDestination, reqVO.getDestination())
                .eqIfPresent(EscortDO::getWsh, reqVO.getWsh())
                .eqIfPresent(EscortDO::getEvidenceUrl, reqVO.getEvidenceUrl())
                .eqIfPresent(EscortDO::getRemark, reqVO.getRemark())
                .eqIfPresent(EscortDO::getTjjglx, reqVO.getTjjglx())
                .eqIfPresent(EscortDO::getTjjgmc, reqVO.getTjjgmc())
                .eqIfPresent(EscortDO::getStatus, reqVO.getStatus())
                .eqIfPresent(EscortDO::getEscortingPoliceSfzh, reqVO.getEscortingPoliceSfzh())
                .eqIfPresent(EscortDO::getEscortingPolice, reqVO.getEscortingPolice())
                .betweenIfPresent(EscortDO::getEscortingTime, reqVO.getEscortingTime())
                .eqIfPresent(EscortDO::getInspectionResult, reqVO.getInspectionResult())
                .eqIfPresent(EscortDO::getProhibitedItems, reqVO.getProhibitedItems())
                .eqIfPresent(EscortDO::getProhibitedItemsImgUrl, reqVO.getProhibitedItemsImgUrl())
                .eqIfPresent(EscortDO::getPhysicalExam, reqVO.getPhysicalExam())
                .eqIfPresent(EscortDO::getPhysicalExamImgUrl, reqVO.getPhysicalExamImgUrl())
                .eqIfPresent(EscortDO::getAbnormalSituations, reqVO.getAbnormalSituations())
                .eqIfPresent(EscortDO::getAbnormalSituationsImgUrl, reqVO.getAbnormalSituationsImgUrl())
                .eqIfPresent(EscortDO::getIsProhibitedItems, reqVO.getIsProhibitedItems())
                .betweenIfPresent(EscortDO::getInspectionTime, reqVO.getInspectionTime())
                .eqIfPresent(EscortDO::getInspectorSfzh, reqVO.getInspectorSfzh())
                .eqIfPresent(EscortDO::getInspector, reqVO.getInspector())
                .betweenIfPresent(EscortDO::getArraignmentStartTime, reqVO.getArraignmentStartTime())
                .betweenIfPresent(EscortDO::getArraignmentEndTime, reqVO.getArraignmentEndTime())
                .eqIfPresent(EscortDO::getReturnInspectorSfzh, reqVO.getReturnInspectorSfzh())
                .eqIfPresent(EscortDO::getReturnInspector, reqVO.getReturnInspector())
                .betweenIfPresent(EscortDO::getReturnInspectionTime, reqVO.getReturnInspectionTime())
                .eqIfPresent(EscortDO::getReturnInspectionResult, reqVO.getReturnInspectionResult())
                .eqIfPresent(EscortDO::getReturnProhibitedItems, reqVO.getReturnProhibitedItems())
                .eqIfPresent(EscortDO::getReturnPhysicalExam, reqVO.getReturnPhysicalExam())
                .eqIfPresent(EscortDO::getReturnAbnormalSituations, reqVO.getReturnAbnormalSituations())
                .betweenIfPresent(EscortDO::getReturnTime, reqVO.getReturnTime())
                .eqIfPresent(EscortDO::getReturnPolice, reqVO.getReturnPolice())
                .eqIfPresent(EscortDO::getApprovalResult, reqVO.getApprovalResult())
                .eqIfPresent(EscortDO::getActInstId, reqVO.getActInstId())
                .eqIfPresent(EscortDO::getTaskId, reqVO.getTaskId())
                .eqIfPresent(EscortDO::getReturnPoliceSfzh, reqVO.getReturnPoliceSfzh());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(EscortDO::getAddTime);
        }
        Page<EscortDO> escortPage = selectPage(page, wrapper);
        return new PageResult<>(escortPage.getRecords(), escortPage.getTotal());
    }

    default List<EscortDO> selectList(EscortListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<EscortDO>()
                .eqIfPresent(EscortDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(EscortDO::getHandler1Id, reqVO.getHandler1Id())
                .eqIfPresent(EscortDO::getHandler1Xm, reqVO.getHandler1Xm())
                .eqIfPresent(EscortDO::getHandler2Id, reqVO.getHandler2Id())
                .eqIfPresent(EscortDO::getHandler2Xm, reqVO.getHandler2Xm())
                .eqIfPresent(EscortDO::getHandler3Id, reqVO.getHandler3Id())
                .eqIfPresent(EscortDO::getHandler3Xm, reqVO.getHandler3Xm())
                .eqIfPresent(EscortDO::getRoomId, reqVO.getRoomId())
                .eqIfPresent(EscortDO::getEscortReason, reqVO.getEscortReason())
                .betweenIfPresent(EscortDO::getApplyEscortDate, reqVO.getApplyEscortDate())
                .eqIfPresent(EscortDO::getDetailReason, reqVO.getDetailReason())
                .eqIfPresent(EscortDO::getDestination, reqVO.getDestination())
                .eqIfPresent(EscortDO::getWsh, reqVO.getWsh())
                .eqIfPresent(EscortDO::getEvidenceUrl, reqVO.getEvidenceUrl())
                .eqIfPresent(EscortDO::getRemark, reqVO.getRemark())
                .eqIfPresent(EscortDO::getTjjglx, reqVO.getTjjglx())
                .eqIfPresent(EscortDO::getTjjgmc, reqVO.getTjjgmc())
                .eqIfPresent(EscortDO::getStatus, reqVO.getStatus())
                .eqIfPresent(EscortDO::getEscortingPoliceSfzh, reqVO.getEscortingPoliceSfzh())
                .eqIfPresent(EscortDO::getEscortingPolice, reqVO.getEscortingPolice())
                .betweenIfPresent(EscortDO::getEscortingTime, reqVO.getEscortingTime())
                .eqIfPresent(EscortDO::getInspectionResult, reqVO.getInspectionResult())
                .eqIfPresent(EscortDO::getProhibitedItems, reqVO.getProhibitedItems())
                .eqIfPresent(EscortDO::getProhibitedItemsImgUrl, reqVO.getProhibitedItemsImgUrl())
                .eqIfPresent(EscortDO::getPhysicalExam, reqVO.getPhysicalExam())
                .eqIfPresent(EscortDO::getPhysicalExamImgUrl, reqVO.getPhysicalExamImgUrl())
                .eqIfPresent(EscortDO::getAbnormalSituations, reqVO.getAbnormalSituations())
                .eqIfPresent(EscortDO::getAbnormalSituationsImgUrl, reqVO.getAbnormalSituationsImgUrl())
                .eqIfPresent(EscortDO::getIsProhibitedItems, reqVO.getIsProhibitedItems())
                .betweenIfPresent(EscortDO::getInspectionTime, reqVO.getInspectionTime())
                .eqIfPresent(EscortDO::getInspectorSfzh, reqVO.getInspectorSfzh())
                .eqIfPresent(EscortDO::getInspector, reqVO.getInspector())
                .betweenIfPresent(EscortDO::getArraignmentStartTime, reqVO.getArraignmentStartTime())
                .betweenIfPresent(EscortDO::getArraignmentEndTime, reqVO.getArraignmentEndTime())
                .eqIfPresent(EscortDO::getReturnInspectorSfzh, reqVO.getReturnInspectorSfzh())
                .eqIfPresent(EscortDO::getReturnInspector, reqVO.getReturnInspector())
                .betweenIfPresent(EscortDO::getReturnInspectionTime, reqVO.getReturnInspectionTime())
                .eqIfPresent(EscortDO::getReturnInspectionResult, reqVO.getReturnInspectionResult())
                .eqIfPresent(EscortDO::getReturnProhibitedItems, reqVO.getReturnProhibitedItems())
                .eqIfPresent(EscortDO::getReturnPhysicalExam, reqVO.getReturnPhysicalExam())
                .eqIfPresent(EscortDO::getReturnAbnormalSituations, reqVO.getReturnAbnormalSituations())
                .betweenIfPresent(EscortDO::getReturnTime, reqVO.getReturnTime())
                .eqIfPresent(EscortDO::getReturnPolice, reqVO.getReturnPolice())
                .eqIfPresent(EscortDO::getReturnPoliceSfzh, reqVO.getReturnPoliceSfzh())
                .eqIfPresent(EscortDO::getApprovalResult, reqVO.getApprovalResult())
                .eqIfPresent(EscortDO::getActInstId, reqVO.getActInstId())
                .eqIfPresent(EscortDO::getTaskId, reqVO.getTaskId())
                .orderByDesc(EscortDO::getAddTime));
    }


}
