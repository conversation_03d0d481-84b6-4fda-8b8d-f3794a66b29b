package com.rs.module.acp.controller.app.gj;

import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.emergencydeparture.EmergencyDepartureRespVO;
import com.rs.module.acp.controller.admin.gj.vo.emergencydeparture.EmergencyDepartureSaveReqVO;
import com.rs.module.acp.entity.gj.EmergencyDepartureDO;
import com.rs.module.acp.service.gj.emergencydeparture.EmergencyDepartureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.Objects;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-管教业务-紧急出所登记")
@RestController
@RequestMapping("/app/acp/gj/emergencyDeparture")
@Validated
public class AppEmergencyDepartureController {

    @Resource
    private EmergencyDepartureService emergencyDepartureService;

    @PostMapping("/create")
    @ApiOperation(value = "App-紧急出所登记")
    public CommonResult<String> createEmergencyDeparture(@Valid @RequestBody EmergencyDepartureSaveReqVO createReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        if (StringUtils.isEmpty(createReqVO.getOperatePolice())) {
            createReqVO.setOperatePolice(sessionUser.getName());
        }
        if (StringUtils.isEmpty(createReqVO.getOperatePoliceSfzh())) {
            createReqVO.setOperatePoliceSfzh(sessionUser.getIdCard());
        }
        if (Objects.isNull(createReqVO.getOperateTime())) {
            createReqVO.setOperateTime(new Date());
        }
        return success(emergencyDepartureService.createEmergencyDeparture(createReqVO));
    }

    @GetMapping("/page")
    @ApiOperation(value = "App-紧急出所登记分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小"),
            @ApiImplicitParam(name = "type", value = "寄信周期类型 1 全部，2 今天，3 昨天，4 近一周")
    })
    public CommonResult<PageResult<EmergencyDepartureRespVO>> getAppEmergencyDeparturePage(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                                                           @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                                                           @RequestParam("type") String type) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        PageResult<EmergencyDepartureDO> pageResult = emergencyDepartureService.getAppEmergencyDeparturePage(pageNo, pageSize, sessionUser.getIdCard(), type);
        return success(BeanUtils.toBean(pageResult, EmergencyDepartureRespVO.class));
    }

}
