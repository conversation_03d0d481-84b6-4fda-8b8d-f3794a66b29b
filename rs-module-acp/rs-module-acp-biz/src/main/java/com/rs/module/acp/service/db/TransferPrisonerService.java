package com.rs.module.acp.service.db;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.TransferPrisonerDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-羁押业务-转所人员 Service 接口
 *
 * <AUTHOR>
 */
public interface TransferPrisonerService extends IBaseService<TransferPrisonerDO>{

    /**
     * 创建实战平台-羁押业务-转所人员
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createTransferPrisoner(@Valid TransferPrisonerSaveReqVO createReqVO);

    /**
     * 更新实战平台-羁押业务-转所人员
     *
     * @param updateReqVO 更新信息
     */
    void updateTransferPrisoner(@Valid TransferPrisonerSaveReqVO updateReqVO);

    /**
     * 删除实战平台-羁押业务-转所人员
     *
     * @param id 编号
     */
    void deleteTransferPrisoner(String id);

    /**
     * 获得实战平台-羁押业务-转所人员
     *
     * @param id 编号
     * @return 实战平台-羁押业务-转所人员
     */
    TransferPrisonerDO getTransferPrisoner(String id);

    /**
    * 获得实战平台-羁押业务-转所人员分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-羁押业务-转所人员分页
    */
    PageResult<TransferPrisonerDO> getTransferPrisonerPage(TransferPrisonerPageReqVO pageReqVO);

    /**
    * 获得实战平台-羁押业务-转所人员列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-羁押业务-转所人员列表
    */
    List<TransferPrisonerDO> getTransferPrisonerList(TransferPrisonerListReqVO listReqVO);


}
