package com.rs.module.acp.entity.gj;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-人员表现鉴定表-看守所 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_performance_kss")
@KeySequence("acp_gj_performance_kss_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_performance_kss")
public class PerformanceKssDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 接收单位
     */
    private String jsdw;
    /**
     * 移送因由
     */
    private String ysyy;
    /**
     * 其他情况
     */
    private String qtqk;
    /**
     * 身体状况
     */
    private String healthStzk;
    /**
     * 外伤情况
     */
    private String healthWsqk;
    /**
     * 重大疾病
     */
    private String healthZdjb;
    /**
     * 精神状况
     */
    private String healthJszk;
    /**
     * 重大疾病及出所住院病因
     */
    private String healthZdjbjcszyyy;
    /**
     * 所规所纪制度
     */
    private String performanceSgsjzd;
    /**
     * 一日生活管理
     */
    private String performanceYrshgl;
    /**
     * 自伤自残行为或倾向
     */
    private String performanceZszcxwhqx;
    /**
     * 殴打他人行为
     */
    private String performanceOdtrxw;
    /**
     * 曾列为重大安全风险情况-一般
     */
    private String performanceZdaqfxqk1;
    /**
     * 曾列为重大安全风险情况-三级
     */
    private String performanceZdaqfxqk2;
    /**
     * 曾列为重大安全风险情况-二级
     */
    private String performanceZdaqfxqk3;
    /**
     * 曾列为重大安全风险情况-一级
     */
    private String performanceZdaqfxqk4;
    /**
     * 家属姓名及电话
     */
    private String familyXmjdh;
    /**
     * 羁押期间联系家属情况
     */
    private String familyJyqjlxjsqk;
    /**
     * 状态
     */
    private String status;
    /**
     * 审批人身份证号
     */
    private String approverSfzh;
    /**
     * 审批人姓名
     */
    private String approverXm;
    /**
     * 审批时间
     */
    private Date approverTime;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 审核意见
     */
    private String approvalComments;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

}
