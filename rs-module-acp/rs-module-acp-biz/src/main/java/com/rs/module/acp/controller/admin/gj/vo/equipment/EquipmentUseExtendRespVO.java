package com.rs.module.acp.controller.admin.gj.vo.equipment;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-械具使用延长呈批 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EquipmentUseExtendRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "申请人")
    private String addUserName;

    @ApiModelProperty(value = "申请时间")
    private Date addTime;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("械具使用ID")
    private String equipmentUseId;

    @ApiModelProperty("延长理由")
    private String extendReason;

    @ApiModelProperty("延长天数")
    private Short extendDay;

    @ApiModelProperty("延长时间")
    private Date extendTime;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审批人签名")
    private String approvalAutograph;

    @ApiModelProperty("审批人签名日期")
    private Date approvalAutographTime;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("是否临时固定")
    private Integer isTempFixation;

    @ApiModelProperty("固定天数")
    private String fixationDays;
}
