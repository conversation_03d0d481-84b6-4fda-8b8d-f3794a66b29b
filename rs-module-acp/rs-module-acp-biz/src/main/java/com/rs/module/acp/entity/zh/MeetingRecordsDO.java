package com.rs.module.acp.entity.zh;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-综合管理-会议记录 DO
 *
 * <AUTHOR>
 */
@TableName("acp_zh_meeting_records")
@KeySequence("acp_zh_meeting_records_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_meeting_records")
public class MeetingRecordsDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键ID，32位UUID
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 会议开始时间
     */
    private Date meetingStartTime;
    /**
     * 会议结束时间
     */
    private Date meetingEndTime;
    /**
     * 会议分类ID
     */
    private String categoryId;
    /**
     * 会议地点
     */
    private String location;
    /**
     * 会议主题
     */
    private String subject;
    /**
     * 会议内容
     */
    private String content;
    /**
     * 参会人员姓名
     */
    private String participants;
    /**
     * 参会人员身份证号
     */
    private String participantsSfzh;
    /**
     * 记录人
     */
    private String recorder;
    /**
     * 会议纪要
     */
    private String meetingSummary;
    /**
     * 会议结论
     */
    private String conclusion;
    /**
     * 上传附件路径
     */
    private String attachment;
    /**
     * data_sources
     */
    private String dataSources;

    /***
     * 文书号
     */
    private String wsh;
    /***
     * 文书字号
     */
    private String wszh;
    /***
     * 被监管人员编码,多个逗号分隔
     */
    private String jgrybms;
    /***
     * 被监管人员姓名,多个逗号分隔
     */
    private String jgryxms;

}
