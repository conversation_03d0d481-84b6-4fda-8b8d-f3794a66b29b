package com.rs.module.acp.controller.admin.db;

import com.rs.module.acp.entity.db.PersonalEffectsInfoDO;
import com.rs.module.acp.service.db.OutRecordKssService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.OutPersonalEffectsSubDO;
import com.rs.module.acp.service.db.OutPersonalEffectsSubService;

@Api(tags = "实战平台-收押业务-出所随身物品登记子")
@RestController
@RequestMapping("/acp/db/outPersonalEffectsSub")
@Validated
public class OutPersonalEffectsSubController {

    @Resource
    private OutPersonalEffectsSubService outPersonalEffectsSubService;


    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-收押业务-出所随身物品登记子")
    public CommonResult<String> createOutPersonalEffectsSub(@Valid @RequestBody OutPersonalEffectsSubSaveReqVO createReqVO) {
        return success(outPersonalEffectsSubService.createOutPersonalEffectsSub(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-收押业务-出所随身物品登记子")
    public CommonResult<Boolean> updateOutPersonalEffectsSub(@Valid @RequestBody OutPersonalEffectsSubSaveReqVO updateReqVO) {
        outPersonalEffectsSubService.updateOutPersonalEffectsSub(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-收押业务-出所随身物品登记子")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteOutPersonalEffectsSub(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           outPersonalEffectsSubService.deleteOutPersonalEffectsSub(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-收押业务-出所随身物品登记子")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<OutPersonalEffectsSubRespVO> getOutPersonalEffectsSub(@RequestParam("id") String id) {
        OutPersonalEffectsSubDO outPersonalEffectsSub = outPersonalEffectsSubService.getOutPersonalEffectsSub(id);
        return success(BeanUtils.toBean(outPersonalEffectsSub, OutPersonalEffectsSubRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-收押业务-出所随身物品登记子分页")
    public CommonResult<PageResult<OutPersonalEffectsSubRespVO>> getOutPersonalEffectsSubPage(@Valid @RequestBody OutPersonalEffectsSubPageReqVO pageReqVO) {
        PageResult<OutPersonalEffectsSubDO> pageResult = outPersonalEffectsSubService.getOutPersonalEffectsSubPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, OutPersonalEffectsSubRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-收押业务-出所随身物品登记子列表")
    public CommonResult<List<OutPersonalEffectsSubRespVO>> getOutPersonalEffectsSubList(@Valid @RequestBody OutPersonalEffectsSubListReqVO listReqVO) {
        List<OutPersonalEffectsSubDO> list = outPersonalEffectsSubService.getOutPersonalEffectsSubList(listReqVO);
        return success(BeanUtils.toBean(list, OutPersonalEffectsSubRespVO.class));
    }

    /**
     * 根据人员编号查询该人员的物品信息
     */
    @GetMapping("/getPersonalEffects")
    @ApiOperation(value = "根据人员编号查询该人员的物品信息")
    public CommonResult<PersonalEffectsInfoVO> getPersonalEffects(@RequestParam("rybh") String rybh) {
        PersonalEffectsInfoDO personalEffectsInfo = outPersonalEffectsSubService.getPersonalEffects(rybh);
        PersonalEffectsInfoVO personalEffectsInfoVO = BeanUtils.toBean(personalEffectsInfo, PersonalEffectsInfoVO.class);

        return success(personalEffectsInfoVO);
    }

    /**
     * 物品取出以及财务交接登记
     * @param createReqVO
     * @return
     */
    @PostMapping("/createOutPersonalEffectsSub")
    @ApiOperation(value = "创建实战平台-收押业务-出所随身物品登记子")
    public CommonResult<String> createOutPersonalEffectsSub(@Valid @RequestBody PersonalEffectsInfoVO createReqVO) {

        return success(outPersonalEffectsSubService.createEffectsSub(createReqVO));
    }
}
