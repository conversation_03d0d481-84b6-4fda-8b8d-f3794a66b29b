package com.rs.module.acp.service.db;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.OutPersonalEffectsSubDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.entity.db.PersonalEffectsInfoDO;

/**
 * 实战平台-收押业务-出所随身物品登记子 Service 接口
 *
 * <AUTHOR>
 */
public interface OutPersonalEffectsSubService extends IBaseService<OutPersonalEffectsSubDO>{

    /**
     * 创建实战平台-收押业务-出所随身物品登记子
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createOutPersonalEffectsSub(@Valid OutPersonalEffectsSubSaveReqVO createReqVO);

    /**
     * 更新实战平台-收押业务-出所随身物品登记子
     *
     * @param updateReqVO 更新信息
     */
    void updateOutPersonalEffectsSub(@Valid OutPersonalEffectsSubSaveReqVO updateReqVO);

    /**
     * 删除实战平台-收押业务-出所随身物品登记子
     *
     * @param id 编号
     */
    void deleteOutPersonalEffectsSub(String id);

    /**
     * 获得实战平台-收押业务-出所随身物品登记子
     *
     * @param id 编号
     * @return 实战平台-收押业务-出所随身物品登记子
     */
    OutPersonalEffectsSubDO getOutPersonalEffectsSub(String id);

    /**
    * 获得实战平台-收押业务-出所随身物品登记子分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-收押业务-出所随身物品登记子分页
    */
    PageResult<OutPersonalEffectsSubDO> getOutPersonalEffectsSubPage(OutPersonalEffectsSubPageReqVO pageReqVO);

    /**
    * 获得实战平台-收押业务-出所随身物品登记子列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-收押业务-出所随身物品登记子列表
    */
    List<OutPersonalEffectsSubDO> getOutPersonalEffectsSubList(OutPersonalEffectsSubListReqVO listReqVO);


    PersonalEffectsInfoDO getPersonalEffects(String rybh);

    String createEffectsSub(@Valid PersonalEffectsInfoVO createReqVO);
}
