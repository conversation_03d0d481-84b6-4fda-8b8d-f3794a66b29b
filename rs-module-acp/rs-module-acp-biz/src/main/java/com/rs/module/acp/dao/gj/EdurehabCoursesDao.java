package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesPageReqVO;
import com.rs.module.acp.entity.gj.EdurehabCoursesDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-教育康复课程 Dao
*
* <AUTHOR>
*/
@Mapper
public interface EdurehabCoursesDao extends IBaseDao<EdurehabCoursesDO> {


    default PageResult<EdurehabCoursesDO> selectPage(EdurehabCoursesPageReqVO reqVO) {
        Page<EdurehabCoursesDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<EdurehabCoursesDO> wrapper = new LambdaQueryWrapperX<EdurehabCoursesDO>()
            .eqIfPresent(EdurehabCoursesDO::getCoursesCode, reqVO.getCoursesCode())
            .likeIfPresent(EdurehabCoursesDO::getCoursesName, reqVO.getCoursesName())
            .eqIfPresent(EdurehabCoursesDO::getCoursesColor, reqVO.getCoursesColor())
            .eqIfPresent(EdurehabCoursesDO::getIsEnabled, reqVO.getIsEnabled())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(EdurehabCoursesDO::getAddTime);
        }
        Page<EdurehabCoursesDO> edurehabCoursesPage = selectPage(page, wrapper);
        return new PageResult<>(edurehabCoursesPage.getRecords(), edurehabCoursesPage.getTotal());
    }
    default List<EdurehabCoursesDO> selectList(EdurehabCoursesListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<EdurehabCoursesDO>()
            .eqIfPresent(EdurehabCoursesDO::getCoursesCode, reqVO.getCoursesCode())
            .likeIfPresent(EdurehabCoursesDO::getCoursesName, reqVO.getCoursesName())
            .eqIfPresent(EdurehabCoursesDO::getCoursesColor, reqVO.getCoursesColor())
            .eqIfPresent(EdurehabCoursesDO::getIsEnabled, reqVO.getIsEnabled())
            .eqIfPresent(EdurehabCoursesDO::getOrgCode, reqVO.getOrgCode())
        .orderByDesc(EdurehabCoursesDO::getAddTime));    }


    }
