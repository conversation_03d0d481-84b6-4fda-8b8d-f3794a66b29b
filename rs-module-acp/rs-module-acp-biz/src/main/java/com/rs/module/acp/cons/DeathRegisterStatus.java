package com.rs.module.acp.cons;

import lombok.Getter;

import java.util.stream.Stream;

@Getter
public enum DeathRegisterStatus {
    SAVEED("01", "登记中"),
    APPRAISE("02", "待死亡鉴定"),
    APPRAISE_SAVE("03", "待提交死亡鉴定"),
    CORPSEHANDLE_SAVE("04", "待保存尸体处理"),
    CORPSEHANDLE("05", "待提交尸体处理"),

    WAIT_APPROVE("06", "待所领导审批"),
    APPROVE_PASS("07", "所领导审批通过"),
    APPROVE_NOPASS("08", "所领导审批不通过");
    private final String code;
    private final String name;
    DeathRegisterStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }
    public DeathRegisterStatus getByCode(String code) {
        return Stream.of(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

}
