package com.rs.module.acp.service.gj.bookingapproval;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalConfigListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalConfigPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalConfigRespVO;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalConfigSaveReqVO;
import com.rs.module.acp.entity.gj.BookingApprovalConfigDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-管教业务-预约审核配置 Service 接口
 *
 * <AUTHOR>
 */
public interface BookingApprovalConfigService extends IBaseService<BookingApprovalConfigDO>{

    /**
     * 创建实战平台-管教业务-预约审核配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createBookingApprovalConfig(@Valid BookingApprovalConfigSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-预约审核配置
     *
     * @param updateReqVO 更新信息
     */
    void updateBookingApprovalConfig(@Valid BookingApprovalConfigSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-预约审核配置
     *
     * @param id 编号
     */
    void deleteBookingApprovalConfig(String id);

    /**
     * 获得实战平台-管教业务-预约审核配置
     *
     * @param id 编号
     * @return 实战平台-管教业务-预约审核配置
     */
    BookingApprovalConfigDO getBookingApprovalConfig(String id);

    /**
    * 获得实战平台-管教业务-预约审核配置分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-预约审核配置分页
    */
    PageResult<BookingApprovalConfigDO> getBookingApprovalConfigPage(BookingApprovalConfigPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-预约审核配置列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-预约审核配置列表
    */
    List<BookingApprovalConfigRespVO> getBookingApprovalConfigList(BookingApprovalConfigListReqVO listReqVO);

    /**
     * 批量更新或者修改
     * @param createReqVOList
     * @return
     */
    boolean batchSaveOrUpdate(List<BookingApprovalConfigSaveReqVO> createReqVOList);
}
