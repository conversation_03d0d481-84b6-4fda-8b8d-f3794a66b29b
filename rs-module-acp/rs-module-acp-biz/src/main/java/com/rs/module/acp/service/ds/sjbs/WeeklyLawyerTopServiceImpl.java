package com.rs.module.acp.service.ds.sjbs;

import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyDataSubmitReqVO;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyLawyerTopSaveReqVO;
import com.rs.module.acp.entity.ds.sjbs.WeeklyDataSubmitJlsDO;
import com.rs.module.acp.util.SjbsDateUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.rs.module.acp.entity.ds.sjbs.WeeklyLawyerTopDO;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.ds.sjbs.WeeklyLawyerTopDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 实战平台-数据固化-每周会见律师排名 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WeeklyLawyerTopServiceImpl extends BaseServiceImpl<WeeklyLawyerTopDao, WeeklyLawyerTopDO> implements WeeklyLawyerTopService {

    @Resource
    private WeeklyLawyerTopDao weeklyLawyerTopDao;

    @Override
    public String createWeeklyLawyerTop(WeeklyLawyerTopSaveReqVO createReqVO) {
        // 插入
        WeeklyLawyerTopDO weeklyLawyerTop = BeanUtils.toBean(createReqVO, WeeklyLawyerTopDO.class);
        weeklyLawyerTopDao.insert(weeklyLawyerTop);
        // 返回
        return weeklyLawyerTop.getId();
    }

    @Override
    public void updateWeeklyLawyerTop(WeeklyLawyerTopSaveReqVO updateReqVO) {
        // 校验存在
        validateWeeklyLawyerTopExists(updateReqVO.getId());
        // 更新
        WeeklyLawyerTopDO updateObj = BeanUtils.toBean(updateReqVO, WeeklyLawyerTopDO.class);
        weeklyLawyerTopDao.updateById(updateObj);
    }

    @Override
    public void deleteWeeklyLawyerTop(String id) {
        // 校验存在
        validateWeeklyLawyerTopExists(id);
        // 删除
        weeklyLawyerTopDao.deleteById(id);
    }

    private void validateWeeklyLawyerTopExists(String id) {
        if (weeklyLawyerTopDao.selectById(id) == null) {
            throw new ServerException("实战平台-数据固化-每周会见律师排名数据不存在");
        }
    }

    @Override
    public WeeklyLawyerTopDO getWeeklyLawyerTop(String id) {
        return weeklyLawyerTopDao.selectById(id);
    }

    @Override
    public void saveForStatistic(List<WeeklyDataSubmitReqVO> weeklyDataSubmitList){
        for (WeeklyDataSubmitReqVO weeklyDataSubmitReqVO: weeklyDataSubmitList) {
            List<WeeklyLawyerTopDO> list = weeklyLawyerTopDao.selectTop10Lawyers(weeklyDataSubmitReqVO.getOrgCode(),weeklyDataSubmitReqVO.getStartDate(),weeklyDataSubmitReqVO.getEndDate());
            /*Map<String,List<WeeklyLawyerTopDO>> orgListData = list.stream().collect(Collectors.groupingBy(WeeklyLawyerTopDO::getOrgCode));
            for (Map.Entry<String, List<WeeklyLawyerTopDO>> entry : orgListData.entrySet()) {
                List<WeeklyLawyerTopDO> orgList = entry.getValue();
                int pm=0;
                for (WeeklyLawyerTopDO entity : orgList) {
                    weeklyLawyerTopDao.deleteByCondition(entity.getOrgCode(),entity.getStartDate(),entity.getEndDate());
                    entity.setPm(pm++);
                    entity.setWeeklyDataSubmitId(weeklyDataSubmitReqVO.getId());
                }
                weeklyLawyerTopDao.insertBatch(orgList);
            }*/
            int pm=0;
            for (WeeklyLawyerTopDO entity : list) {
                weeklyLawyerTopDao.deleteByCondition(entity.getOrgCode(),entity.getStartDate(),entity.getEndDate());
                entity.setPm(pm++);
                entity.setWeeklyDataSubmitId(weeklyDataSubmitReqVO.getId());
                entity.setStartDate(weeklyDataSubmitReqVO.getStartDate());
                entity.setEndDate(weeklyDataSubmitReqVO.getEndDate());
                entity.setStartDate(entity.getSolidificationDate());
            }
            weeklyLawyerTopDao.insertBatch(list);
        }

    }

    @Override
    public List<WeeklyLawyerTopDO> getWeeklyLawyerTopByDate(String startDate, String endDate, String orgCode) {
        String[] range = SjbsDateUtil.getLastWeekRangeStr();
        if(null == startDate) startDate = range[0];
        if(null == endDate) endDate = range[1];
        if(StringUtil.isEmpty(orgCode)) orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        List<WeeklyLawyerTopDO> list = weeklyLawyerTopDao.selectList(new LambdaQueryWrapperX<WeeklyLawyerTopDO>().
                eq(WeeklyLawyerTopDO::getStartDate, startDate).eq(WeeklyLawyerTopDO::getEndDate, endDate).
                eq(WeeklyLawyerTopDO::getOrgCode, orgCode).eq(WeeklyLawyerTopDO::getIsDel, 0));
        return list;
    }

    @Override
    public List<WeeklyLawyerTopDO> getByWeeklyDataSubmitId(String weeklyDataSubmitId) {
        List<WeeklyLawyerTopDO> list = weeklyLawyerTopDao.selectList(new LambdaQueryWrapperX<WeeklyLawyerTopDO>().
                eq(WeeklyLawyerTopDO::getWeeklyDataSubmitId, weeklyDataSubmitId).eq(WeeklyLawyerTopDO::getIsDel, 0));
        return list;
    }
}
