package com.rs.module.acp.controller.admin.db;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.TransferPrisonerDO;
import com.rs.module.acp.service.db.TransferPrisonerService;

@Api(tags = "实战平台-羁押业务-转所人员")
@RestController
@RequestMapping("/acp/db/transferPrisoner")
@Validated
public class TransferPrisonerController {

    @Resource
    private TransferPrisonerService transferPrisonerService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-羁押业务-转所人员")
    public CommonResult<String> createTransferPrisoner(@Valid @RequestBody TransferPrisonerSaveReqVO createReqVO) {
        return success(transferPrisonerService.createTransferPrisoner(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-羁押业务-转所人员")
    public CommonResult<Boolean> updateTransferPrisoner(@Valid @RequestBody TransferPrisonerSaveReqVO updateReqVO) {
        transferPrisonerService.updateTransferPrisoner(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-羁押业务-转所人员")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteTransferPrisoner(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           transferPrisonerService.deleteTransferPrisoner(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-羁押业务-转所人员")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<TransferPrisonerRespVO> getTransferPrisoner(@RequestParam("id") String id) {
        TransferPrisonerDO transferPrisoner = transferPrisonerService.getTransferPrisoner(id);
        return success(BeanUtils.toBean(transferPrisoner, TransferPrisonerRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-羁押业务-转所人员分页")
    public CommonResult<PageResult<TransferPrisonerRespVO>> getTransferPrisonerPage(@Valid @RequestBody TransferPrisonerPageReqVO pageReqVO) {
        PageResult<TransferPrisonerDO> pageResult = transferPrisonerService.getTransferPrisonerPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TransferPrisonerRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-羁押业务-转所人员列表")
    public CommonResult<List<TransferPrisonerRespVO>> getTransferPrisonerList(@Valid @RequestBody TransferPrisonerListReqVO listReqVO) {
        List<TransferPrisonerDO> list = transferPrisonerService.getTransferPrisonerList(listReqVO);
        return success(BeanUtils.toBean(list, TransferPrisonerRespVO.class));
    }
}
