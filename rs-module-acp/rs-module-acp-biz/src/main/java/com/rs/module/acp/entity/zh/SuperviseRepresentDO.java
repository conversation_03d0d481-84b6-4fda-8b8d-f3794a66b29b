package com.rs.module.acp.entity.zh;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 综合管理-督导申诉 DO
 *
 * <AUTHOR>
 */
@TableName("acp_zh_supervise_represent")
@KeySequence("acp_zh_supervise_represent_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_supervise_represent")
public class SuperviseRepresentDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 标题
     */
    private String title;
    /**
     * 申诉人身份证号
     */
    private String userIdCard;
    /**
     * 申诉人姓名
     */
    private String userName;
    /**
     * 申诉时间
     */
    private Date time;
    /**
     * 申诉理由
     */
    private String reason;
    /**
     * 备注
     */
    private String remark;
    /**
     * 督导id
     */
    private String superviseId;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 附件信息
     */
    private String attachment;
    /**
     * 当前审批人身份证号
     */
    private String approvalUserSfzh;
    /**
     * 当前审批人姓名
     */
    private String approvalUserName;

}
