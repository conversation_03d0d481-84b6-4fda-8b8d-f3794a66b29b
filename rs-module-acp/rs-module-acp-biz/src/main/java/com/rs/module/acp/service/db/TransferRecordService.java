package com.rs.module.acp.service.db;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.TransferPrisonerInfoDO;
import com.rs.module.acp.entity.db.TransferRecordDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-羁押业务-转所登记 Service 接口
 *
 * <AUTHOR>
 */
public interface TransferRecordService extends IBaseService<TransferRecordDO>{

    /**
     * 创建实战平台-羁押业务-转所登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createTransferRecord(@Valid TransferRecordSaveReqVO createReqVO);

    /**
     * 更新实战平台-羁押业务-转所登记
     *
     * @param updateReqVO 更新信息
     */
    void updateTransferRecord(@Valid TransferRecordSaveReqVO updateReqVO);

    /**
     * 删除实战平台-羁押业务-转所登记
     *
     * @param id 编号
     */
    void deleteTransferRecord(String id);

    /**
     * 获得实战平台-羁押业务-转所登记
     *
     * @param id 编号
     * @return 实战平台-羁押业务-转所登记
     */
    TransferRecordDO getTransferRecord(String id);

    /**
    * 获得实战平台-羁押业务-转所登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-羁押业务-转所登记分页
    */
    PageResult<TransferRecordDO> getTransferRecordPage(TransferRecordPageReqVO pageReqVO);

    /**
    * 获得实战平台-羁押业务-转所登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-羁押业务-转所登记列表
    */
    List<TransferRecordDO> getTransferRecordList(TransferRecordListReqVO listReqVO);


    List<TransferPrisonerInfoDO> getPrisonersByTransferRecordId(String id);
}
