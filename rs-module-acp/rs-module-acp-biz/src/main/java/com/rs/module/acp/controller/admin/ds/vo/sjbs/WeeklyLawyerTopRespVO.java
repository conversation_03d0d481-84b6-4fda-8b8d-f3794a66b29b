package com.rs.module.acp.controller.admin.ds.vo.sjbs;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-数据固化-每周会见律师排名 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class WeeklyLawyerTopRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("固化日期")
    private String solidificationDate;
    @ApiModelProperty("固化开始日期")
    private String startDate;
    @ApiModelProperty("固化结束日期")
    private String endDate;
    @ApiModelProperty("每周数据报送ID")
    private String weeklyDataSubmitId;
    @ApiModelProperty("律师ID")
    private String lsId;
    @ApiModelProperty("律师姓名")
    private String xm;
    @ApiModelProperty("律师执业证号码")
    private String zyzhm;
    @ApiModelProperty("每周数据报送ID")
    private Integer hjcs;
    @ApiModelProperty("排名")
    private Integer pm;
}
