package com.rs.module.acp.service.pi;

import java.util.*;
import javax.validation.*;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.*;
import com.rs.module.acp.entity.pi.PrisonEventDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-巡视管控-所情登记 Service 接口
 *
 * <AUTHOR>
 */
public interface PrisonEventService extends IBaseService<PrisonEventDO>{

    /**
     * 创建实战平台-巡视管控-所情登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrisonEvent(@Valid PrisonEventSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-所情登记
     *
     * @param updateReqVO 更新信息
     */
    void updatePrisonEvent(@Valid PrisonEventSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-所情登记
     *
     * @param id 编号
     */
    void deletePrisonEvent(String id);

    /**
     * 获得实战平台-巡视管控-所情登记
     *
     * @param id 编号
     * @return 实战平台-巡视管控-所情登记
     */
    PrisonEventDO getPrisonEvent(String id);

    /**
    * 获得实战平台-巡视管控-所情登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-巡视管控-所情登记分页
    */
    PageResult<PrisonEventDO> getPrisonEventPage(PrisonEventPageReqVO pageReqVO);

    /**
    * 获得实战平台-巡视管控-所情登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-巡视管控-所情登记列表
    */
    List<PrisonEventDO> getPrisonEventList(PrisonEventListReqVO listReqVO);


    /**
     * 获取外来人员分页
     * @param pageReqVO
     * @return
     */
    PageResult<PrisonEventPersonRespVO> getOutsiderPersonPage(PrisonEventPersonPageReqVO pageReqVO);


    /**
     * 获取推送岗位
     * @param eventTypeId
     * @return
     */
    List<PrisonEventDisposePostSaveVO> getAllPostCode(String eventTypeId);


    /**
     * 获取所情详情
     * @param eventId
     * @return
     */
    PrisonEventRespVO getPrisonEventById(String eventId);

    /**
     * 根据当前岗位获取所情处置的节点信息
     * @param eventId
     * @return
     */
    PrisonEventHandleRespVO getCurrentNodeByPostCode(String eventId);

    /**
     * 无需处置
     * @param eventId
     * @return
     */
    boolean noNeeddisposal(String eventId);

    /**
     * 所情处置
     * @param createReqVO
     * @return
     */
    boolean prisonEventDispose(PrisonEventHandleSaveReqVO createReqVO);

    /**
     * 所领导审批
     * @param createReqVO
     * @return
     */
    boolean prisonEventApprove(PrisonEventHandleApproveSaveReqVO createReqVO);


    /**
     * 智能终端设备传入预警
     * @param saveReqVO
     * @return
     */
    boolean savePrisonEventApi(PrisonEventTerminalWarningSaveReqVO saveReqVO);
}
