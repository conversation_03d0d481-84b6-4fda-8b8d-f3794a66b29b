package com.rs.module.acp.controller.admin.db.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-羁押业务-转所登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TransferRecordSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("转所日期")
    private Date zsrq;

    @ApiModelProperty("转所人数")
    private Integer zsrs;

    @ApiModelProperty("'转所类型")
    private String zslx;

    @ApiModelProperty("转往单位")
    private String zwdw;

    @ApiModelProperty("押解方案")
    private String yjfa;

    @ApiModelProperty("押解民警身份证号")
    private String yjmjsfzh;

    @ApiModelProperty("押解民警")
    private String yjmj;

    @ApiModelProperty("经办人身份证号")
//    @NotEmpty(message = "经办人身份证号不能为空")
    private String jbrsfzh;

    @ApiModelProperty("经办人")
    @NotEmpty(message = "经办人不能为空")
    private String jbr;

    @ApiModelProperty("经办时间")
    @NotNull(message = "经办时间不能为空")
    private Date jbsj;

    @ApiModelProperty("转所人员列表")
    private List<TransferPrisonersVO> transferPrisoners;

}
