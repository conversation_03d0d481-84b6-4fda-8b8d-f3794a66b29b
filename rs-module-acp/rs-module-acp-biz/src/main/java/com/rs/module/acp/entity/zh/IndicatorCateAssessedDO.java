package com.rs.module.acp.entity.zh;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 综合管理-绩效考核指标分类与被考评人关联 DO
 *
 * <AUTHOR>
 */
@TableName("acp_zh_indicator_cate_assessed")
@KeySequence("acp_zh_indicator_cate_assessed_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_indicator_cate_assessed")
public class IndicatorCateAssessedDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 指标分类ID
     */
    private String indicatorCateId;
    /**
     * 被考核对象类型，01:岗位、02：角色、03：用户
     */
    private String assessedObjectType;
    /**
     * assessed_object_id
     */
    private String assessedObjectId;
    /**
     * assessed_object_name
     */
    private String assessedObjectName;

}
