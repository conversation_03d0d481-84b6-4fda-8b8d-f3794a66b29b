package com.rs.module.acp.service.gj.transitionroom;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.transitionroom.TransitionRoomEvalSaveReqVO;
import com.rs.module.acp.dao.gj.TransitionRoomEvalDao;
import com.rs.module.acp.entity.gj.TransitionRoomEvalDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


/**
 * 实战平台-管教业务-过渡考核登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TransitionRoomEvalServiceImpl extends BaseServiceImpl<TransitionRoomEvalDao, TransitionRoomEvalDO> implements TransitionRoomEvalService {

    @Resource
    private TransitionRoomEvalDao transitionRoomEvalDao;


    @Override
    public TransitionRoomEvalDO getTransitionRoomEval(String id) {
        return transitionRoomEvalDao.selectById(id);
    }

    @Override
    public boolean regAssessmentInfo(TransitionRoomEvalSaveReqVO createReqVO) {
        // 插入
        TransitionRoomEvalDO transitionRoomEval = BeanUtils.toBean(createReqVO, TransitionRoomEvalDO.class);
        transitionRoomEvalDao.insert(transitionRoomEval);
        return true;
    }

}
