package com.rs.module.acp.entity.zh;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 值班模板班组人员信息 DO
 *
 * <AUTHOR>
 */
@TableName("acp_zh_staff_duty_team_person")
@KeySequence("acp_zh_staff_duty_team_person_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_staff_duty_team_person")
public class StaffDutyTeamPersonDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * acp_zh_staff_duty_team.id
     */
    private String teamId;
    /**
     * acp_zh_staff_duty_role.id
     */
    private String roleId;
    /**
     * 人员id
     */
    private String personId;
    /**
     * 人员姓名
     */
    private String personName;
    /**
     * 人员sfzh
     */
    private String personSfzh;

}
