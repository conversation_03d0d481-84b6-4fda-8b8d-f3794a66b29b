package com.rs.module.acp.controller.app.pi;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.pi.vo.ProtRestraintPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.ProtRestraintRespVO;
import com.rs.module.acp.controller.app.pi.vo.ProtRestraintAppPageReqVO;
import com.rs.module.acp.controller.app.pi.vo.ProtRestraintAppRespVO;
import com.rs.module.acp.controller.app.pi.vo.ProtRestraintAppSaveReqVO;
import com.rs.module.acp.entity.pi.ProtRestraintDO;
import com.rs.module.acp.service.pi.ProtRestraintService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "仓内外屏-巡视管控-保护性约束")
@RestController
@RequestMapping("/app/acp/pi/protRestraint")
@Validated
public class ProtRestraintAppController {

    @Resource
    private ProtRestraintService protRestraintService;

    @PostMapping("/create")
    @ApiOperation(value = "仓内外屏-巡视管控-保护性约束登记")
    public CommonResult<String> appCreateProtRestraint(@Valid @RequestBody ProtRestraintAppSaveReqVO createReqVO) {
        try{
            return success(protRestraintService.appCreateProtRestraint(createReqVO));
        }catch (Exception e){
            return error(e.getMessage());
        }
    }

    @PostMapping("/page")
    @ApiOperation(value = "仓内外屏-巡视管控-当前监室人员保护性约束申请记录分页查询")
    public CommonResult<PageResult<ProtRestraintAppRespVO>> getAppProtRestraintAppPage(@Valid @RequestBody ProtRestraintAppPageReqVO pageReqVO) {
        PageResult<ProtRestraintAppRespVO> pageResult = protRestraintService.getProtRestraintAppPage(pageReqVO);
        return success(pageResult);
    }
}
