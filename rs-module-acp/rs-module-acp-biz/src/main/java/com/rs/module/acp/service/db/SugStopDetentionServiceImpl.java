package com.rs.module.acp.service.db;

import cn.hutool.core.bean.BeanUtil;
import com.bsp.common.util.SettingUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.acp.cons.CommonConstants;
import com.rs.module.acp.cons.SugDesusBusinessStatus;
import com.rs.module.acp.controller.admin.db.vo.sugstopdetention.*;
import com.rs.module.acp.util.SugDesusUtil;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.base.vo.ApproveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.entity.db.SugStopDetentionDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.db.SugStopDetentionDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-收押业务-建议停拘登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SugStopDetentionServiceImpl extends BaseServiceImpl<SugStopDetentionDao, SugStopDetentionDO> implements SugStopDetentionService {

    @Resource
    private SugStopDetentionDao sugStopDetentionDao;
    @Resource
    private BspApi bspApi;
    @Override
    public String createSugStopDetention(SugStopDetentionSaveReqVO createReqVO) {
        SugStopDetentionDO entity = BeanUtils.toBean(createReqVO, SugStopDetentionDO.class);
        if(StringUtil.isEmpty(entity.getId())){
            entity.setStatus(SugDesusBusinessStatus.getStatusByJtlxAndCode(createReqVO.getJtlx(),"00").getCode());
            sugStopDetentionDao.insert(entity);
        }else{
            sugStopDetentionDao.updateById( entity);
        }

        return entity.getId();
    }
    @Override
    public void updateSugStopDetention(SugStopDetentionSaveReqVO updateReqVO) {
        // 校验存在
        validateSugStopDetentionExists(updateReqVO.getId());
        // 更新
        SugStopDetentionDO entity = BeanUtils.toBean(updateReqVO, SugStopDetentionDO.class);
        sugStopDetentionDao.updateById(entity);
    }

    @Override
    public void deleteSugStopDetention(String id) {
        // 校验存在
        validateSugStopDetentionExists(id);
        // 删除
        sugStopDetentionDao.deleteById(id);
    }

    private void validateSugStopDetentionExists(String id) {
        if (sugStopDetentionDao.selectById(id) == null) {
            throw new ServerException("实战平台-收押业务-建议停拘登记数据不存在");
        }
    }

    @Override
    public SugStopDetentionDO getSugStopDetention(String id) {
        return sugStopDetentionDao.selectById(id);
    }

    @Override
    public String startProcess(SugStopDetentionSaveReqVO createReqVO) throws Exception {
        SugStopDetentionDO entity = BeanUtils.toBean(createReqVO, SugStopDetentionDO.class);
        entity.setStatus(SugDesusBusinessStatus.getStatusByJtlxAndCode(createReqVO.getJtlx(),"01").getCode());
        Map<String, Object> variables = new HashMap<>();
        String msgTit = "";
        String msgUrl = "";
        if(StringUtil.isEmpty(entity.getId())) entity.setId(StringUtil.getGuid());
        //启动流程
        Map<String,String> processResult = BspApprovalUtil.commonStartProcessMap(SugDesusUtil.getFlowDefKeyByJtlx(entity.getJtlx()), entity.getId(), msgTit, msgUrl, variables, HttpUtils.getAppCode());
        if(StringUtil.isEmpty(processResult.get("actInstId")) || StringUtil.isEmpty(processResult.get("taskId"))){
            throw new Exception("启动流程失败");
        }
        entity.setActInstId(processResult.get("actInstId"));
        entity.setTaskId(processResult.get("taskId"));
        if(StringUtil.isEmpty(entity.getActInstId())){
            sugStopDetentionDao.insert(entity);
        }else {
            sugStopDetentionDao.updateById(entity);
        }

        return entity.getId();
    }

    /**
     * 从当前登录用户信息初始化审批信息默认值
     * @param approveReqVO
     */
    private void initApproveInfo(ApproveReqVO approveReqVO){
        if(StringUtil.isEmpty(approveReqVO.getApproverXm())) approveReqVO.setApproverXm(SessionUserUtil.getSessionUser().getName());
        if(StringUtil.isEmpty(approveReqVO.getApproverSfzh())) approveReqVO.setApproverSfzh(SessionUserUtil.getSessionUser().getIdCard());
        if(approveReqVO.getApproverTime() == null) approveReqVO.setApproverTime(new Date());
    }
    public Boolean commonApprove(ApproveReqVO approveReqVO,String approvePassStatusCode,String approveRejectedStatusCode,String msgTit,String msgUrl) throws Exception  {
        initApproveInfo(approveReqVO);
        SugStopDetentionDO entity = sugStopDetentionDao.selectById(approveReqVO.getId());
        BspApproceStatusEnum isApprove = BspApproceStatusEnum.NOT_PASSED;
        String jtlx = entity.getJtlx();
        String status = SugDesusBusinessStatus.getStatusByJtlxAndCode(jtlx,approveRejectedStatusCode).getCode();
        if("1".equals(approveReqVO.getApprovalResult())){
            status = SugDesusBusinessStatus.getStatusByJtlxAndCode(jtlx,approvePassStatusCode).getCode();
            isApprove = BspApproceStatusEnum.PASSED;
        }
        approveReqVO.setActInstId(entity.getActInstId());
        approveReqVO.setTaskId(entity.getTaskId());
        approveReqVO.setDefKey(SugDesusUtil.getFlowDefKeyByJtlx(entity.getJtlx()));
        BeanUtil.copyProperties( approveReqVO,entity);
        entity.setStatus(status);
        boolean terminateTask = false;
        if(approvePassStatusCode.equals("07")){
            terminateTask = true;
            //设置文书号
            //bspApi.executeByRuleCode("acp_shmd_bm_system", null);
            entity.setSpr(approveReqVO.getApproverXm());
            entity.setSprsfzh(approveReqVO.getApproverSfzh());
            initWsInfo(approveReqVO,entity);
        }
        if(approvePassStatusCode.equals("03")){
            entity.setJbr(approveReqVO.getApproverXm());
            entity.setJbrsfzh(approveReqVO.getApproverSfzh());
        }
        try{
            Map<String,String> approvalResult = BspApprovalUtil.approvalProcessMap(approveReqVO,
                    isApprove,
                    msgTit,
                    msgUrl,
                    terminateTask,
                    null,
                    null,
                    HttpUtils.getAppCode()
            );
            String taskId = approvalResult.get("taskId"); // 更新任务ID
            entity.setTaskId(taskId);
        }catch (Exception e){
            log.error("审批失败",e);
            throw new Exception(e.getMessage());
        }
        sugStopDetentionDao.updateById(entity);
        return true;
    }
    @Override
    public Boolean policeApprove(ApproveReqVO approveReqVO) throws Exception  {
        commonApprove(approveReqVO,"03","02","","");
        return true;
    }
    @Override
    public Boolean doctorApprove(ApproveReqVO approveReqVO) throws Exception  {
        commonApprove(approveReqVO,"03","02","","");
        return true;
    }
    @Override
    public Boolean leaderApprove(ApproveReqVO approveReqVO) throws Exception  {
        commonApprove(approveReqVO,"05","04","","");
        return true;
    }

    @Override
    public Boolean directorApprove(ApproveReqVO approveReqVO) throws Exception  {
        commonApprove(approveReqVO,"07","06","","");
        return true;
    }

    @Override
    public Boolean receiveProcess(ApproveReqVO approveReqVO) throws Exception  {
        //commonApprove(approveReqVO,"09","08","","");
        return true;
    }
    @Override
    public String sjsPoliceCreateSugStopDetention(SugStopDetentionPoliceSaveReqVO createReqVO) throws Exception{
        SugStopDetentionDO entity = BeanUtils.toBean(createReqVO, SugStopDetentionDO.class);
        if("0".equals(createReqVO.getSaveType())){//0 保存,1 提交
            entity.setStatus(SugDesusBusinessStatus.WhenDetainedStatus.POLICE_SAVE.getCode());
        }else{
            entity.setStatus(SugDesusBusinessStatus.WhenDetainedStatus.WAITING_DOCTOR_SUBMIT.getCode());
        }
        entity.setJtlx(SugDesusUtil.SJS);
        entity.setJbr(SessionUserUtil.getSessionUser().getName());
        entity.setJbrsfzh(SessionUserUtil.getSessionUser().getIdCard());
        if(StringUtil.isEmpty(entity.getId())){
            sugStopDetentionDao.insert(entity);
        }else{
            sugStopDetentionDao.updateById( entity);
        }
        return entity.getId();
    }

    @Override
    public String sjsDoctorCreateSugStopDetention(SugStopDetentionDoctorSaveReqVO createReqVO)throws Exception {
        // 校验存在
        validateSugStopDetentionExists(createReqVO.getId());
        SugStopDetentionDO entity = BeanUtils.toBean(createReqVO, SugStopDetentionDO.class);
        entity.setJtlx(SugDesusUtil.SJS);
        if("0".equals(createReqVO.getSaveType())){//0 保存,1 提交
            entity.setStatus(SugDesusBusinessStatus.WhenDetainedStatus.DOCTOR_SAVE.getCode());
        }else{
            entity.setStatus(SugDesusBusinessStatus.WhenDetainedStatus.DOCTOR_SUBMIT.getCode());
            commonStartProcess(entity);
        }
        sugStopDetentionDao.updateById(entity);
        return entity.getId();
    }
    private void commonStartProcess(SugStopDetentionDO entity) throws Exception {
        if(StringUtil.isNotEmpty(entity.getActInstId())){//流程已经启动
            return ;
        }
        Map<String, Object> variables = new HashMap<>();
        String msgTit = "";
        String msgUrl = "";
        if(StringUtil.isEmpty(entity.getId())) entity.setId(StringUtil.getGuid());
        //启动流程
        Map<String,String> processResult = BspApprovalUtil.commonStartProcessMap(SugDesusUtil.getFlowDefKeyByJtlx(entity.getJtlx()), entity.getId(), msgTit, msgUrl, variables, HttpUtils.getAppCode());
        if(StringUtil.isEmpty(processResult.get("actInstId")) || StringUtil.isEmpty(processResult.get("taskId"))){
            throw new Exception("启动流程失败");
        }
        entity.setActInstId(processResult.get("actInstId"));
        entity.setTaskId(processResult.get("taskId"));
    }
    @Override
    public String sjhDoctorsave(SugStopDetentionSjhDoctorSaveReqVO createReqVO) throws Exception{
        SugStopDetentionDO entity = BeanUtils.toBean(createReqVO, SugStopDetentionDO.class);
        entity.setJtlx(SugDesusUtil.SJH);
        if(StringUtil.isEmpty(entity.getId())) entity.setId(StringUtil.getGuid());
        if("0".equals(createReqVO.getSaveType())){//0 保存,1 提交
            entity.setStatus(SugDesusBusinessStatus.AfterDetainedStatus.POLICE_SAVE.getCode());
        }else{
            entity.setStatus(SugDesusBusinessStatus.AfterDetainedStatus.WAITING_POLICE_APPROVAL.getCode());
            commonStartProcess(entity);
        }
        if(StringUtil.isEmpty(createReqVO.getId())){
            sugStopDetentionDao.insert(entity);
        }else{
            sugStopDetentionDao.updateById( entity);
        }
        return entity.getId();
    }
    private void initWsInfo(ApproveReqVO approveReqVO,SugStopDetentionDO entity){
        Integer maxWsh = sugStopDetentionDao.getNowYeaMaxWsh();
        String startWsh = SettingUtil.getParamKey("SUG_STOP_DETENTION_WSH", HttpUtils.getAppCode());
        if (StringUtil.isNotEmpty(startWsh) && maxWsh == null){
            maxWsh = Integer.parseInt(startWsh);
        }
        if (maxWsh == null) maxWsh = 0;
        maxWsh += 1;
        String wsh = String.format("%04d", maxWsh);
        //生成流水号
        entity.setWsh(wsh);
        entity.setWszh(SugDesusUtil.buildWszh(entity.getOrgCode(),approveReqVO.getApproverTime(),wsh));
    }

}
