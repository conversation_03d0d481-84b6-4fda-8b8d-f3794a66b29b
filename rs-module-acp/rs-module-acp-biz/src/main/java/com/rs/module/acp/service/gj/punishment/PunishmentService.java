package com.rs.module.acp.service.gj.punishment;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApprovalTraceVO;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.*;
import com.rs.module.acp.entity.gj.PunishmentDO;
import com.rs.module.acp.enums.gj.PunishmentStatusEnum;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 实战平台-管教业务-处罚呈批 Service 接口
 *
 * <AUTHOR>
 */
public interface PunishmentService extends IBaseService<PunishmentDO>{

    /**
     * 创建实战平台-管教业务-处罚呈批
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPunishment(@Valid PunishmentSaveReqVO createReqVO);

    /**
     * 获得实战平台-管教业务-处罚呈批
     *
     * @param id 编号
     * @return 实战平台-管教业务-处罚呈批
     */
    PunishmentRespVO getPunishment(String id);

    /**
    * 获得实战平台-管教业务-处罚呈批分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-处罚呈批分页
    */
    PageResult<PunishmentDO> getPunishmentPage(PunishmentPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-处罚呈批列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-处罚呈批列表
    */
    List<PunishmentDO> getPunishmentList(PunishmentListReqVO listReqVO);

    /**
     * 领导审批
     * <AUTHOR>
     * @date 2025/6/10 11:13
     * @param [approveReqVO]
     * @return void
     */
    void leaderApprove(GjApproveReqVO approveReqVO);

    /**
     * 登记信息
     * <AUTHOR>
     * @date 2025/6/10 15:01
     * @param [approveReqVO]
     * @return void
     */
    void regInfo(PunishmentSaveRegInfoVO approveReqVO);

    /**
     * 创建流程
     * @param createReqVO
     */
    String createExtend(PunishmentExtendSaveReqVO createReqVO);

    /**
     * 领导审批
     * <AUTHOR>
     * @date 2025/6/10 17:03
     * @param [createReqVO]
     * @return void
     */
    void approveExtend(GjApproveReqVO createReqVO);

    /**
     * 获取记录信息
     * <AUTHOR>
     * @date 2025/6/10 20:44
     * @param [jgrybm, pageNo, pageSize]
     * @return com.rs.framework.common.pojo.PageResult<com.rs.module.acp.entity.gj.PunishmentDO>
     */
    PageResult<PunishmentRespVO> getPunishmentHistoryByJgrybm(String jgrybm, Long pageNo, Long pageSize);

    /**
     * 提前解除呈批
     * <AUTHOR>
     * @date 2025/6/11 11:12
     * @param [createReqVO]
     * @return void
     */
    void createRemove(PunishmentRemoveSaveReqVO createReqVO);

    /**
     * 撤回领导审批
     * <AUTHOR>
     * @date 2025/6/11 12:49
     * @param [createReqVO]
     * @return void
     */
    void apprRemove(GjApproveReqVO createReqVO);

    /**
     * 获取流程轨迹信息
     * <AUTHOR>
     * @date 2025/6/11 15:12
     * @param [id]
     * @return java.util.List<com.rs.module.acp.controller.admin.gj.vo.common.GjApprovalTraceVO>
     */
    List<GjApprovalTraceVO> getApproveTrack(String id);

    /**
     * 解除登记
     * <AUTHOR>
     * @date 2025/6/11 16:06
     * @param [createReqVO]
     * @return void
     */
    void removeRegInfo(PunishmentSaveRemoveReqVO createReqVO);

    /**
     * 特殊场景- 保存外部扩展信息
     * <AUTHOR>
     * @date 2025/6/13 20:50
     * @param [saveInfo]
     * @return boolean
     */
    boolean saveExternal(PunishmentSaveExternalInfoVO saveInfo);

    /**
     * 特殊场景-更新状态信息  待解除、已解除
     * <AUTHOR>
     * @date 2025/6/14 8:37
     * @param [id, punEnum]
     * @return boolean
     */
    boolean updateExternalStatus(String id, PunishmentStatusEnum punEnum, Date endTime);

}
