package com.rs.module.acp.service.zh.indicatorcate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorConfigListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorConfigPageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorConfigSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.zh.IndicatorConfigDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.zh.IndicatorConfigDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 综合管理-绩效考核截止日期设置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class IndicatorConfigServiceImpl extends BaseServiceImpl<IndicatorConfigDao, IndicatorConfigDO> implements IndicatorConfigService {

    @Resource
    private IndicatorConfigDao indicatorConfigDao;

    @Override
    public String createIndicatorConfig(IndicatorConfigSaveReqVO createReqVO) {
        String orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        IndicatorConfigDO tempDO = getIndicatorConfigByOrgCode(orgCode);
        if(Objects.nonNull(tempDO)){
            throw new RuntimeException("该机构已存在绩效考核截止日期配置，无需重复添加");
        }
        // 插入
        IndicatorConfigDO indicatorConfig = BeanUtils.toBean(createReqVO, IndicatorConfigDO.class);
        indicatorConfigDao.insert(indicatorConfig);
        // 返回
        return indicatorConfig.getId();
    }

    @Override
    public void updateIndicatorConfig(IndicatorConfigSaveReqVO updateReqVO) {
        // 校验存在
        validateIndicatorConfigExists(updateReqVO.getId());
        // 更新
        IndicatorConfigDO updateObj = BeanUtils.toBean(updateReqVO, IndicatorConfigDO.class);
        indicatorConfigDao.updateById(updateObj);
    }

    @Override
    public void deleteIndicatorConfig(String id) {
        // 校验存在
        validateIndicatorConfigExists(id);
        // 删除
        indicatorConfigDao.deleteById(id);
    }

    private void validateIndicatorConfigExists(String id) {
        if (indicatorConfigDao.selectById(id) == null) {
            throw new ServerException("综合管理-绩效考核截止日期设置数据不存在");
        }
    }

    @Override
    public IndicatorConfigDO getIndicatorConfig(String id) {
        return indicatorConfigDao.selectById(id);
    }

    @Override
    public PageResult<IndicatorConfigDO> getIndicatorConfigPage(IndicatorConfigPageReqVO pageReqVO) {
        return indicatorConfigDao.selectPage(pageReqVO);
    }

    @Override
    public List<IndicatorConfigDO> getIndicatorConfigList(IndicatorConfigListReqVO listReqVO) {
        return indicatorConfigDao.selectList(listReqVO);
    }

    @Override
    public IndicatorConfigDO getIndicatorConfigByOrgCode(String orgCode) {
        LambdaQueryWrapper<IndicatorConfigDO> wrapper = Wrappers.lambdaQuery(IndicatorConfigDO.class)
                .eq(IndicatorConfigDO::getOrgCode, orgCode).orderByDesc(IndicatorConfigDO::getAddTime).last(" limit 1");
        return indicatorConfigDao.selectOne(wrapper);
    }


}
