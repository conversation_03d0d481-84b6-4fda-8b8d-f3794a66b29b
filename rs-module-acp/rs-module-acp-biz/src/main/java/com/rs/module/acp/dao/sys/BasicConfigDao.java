package com.rs.module.acp.dao.sys;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.sys.BasicConfigDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.sys.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-系统基础配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BasicConfigDao extends IBaseDao<BasicConfigDO> {


    default PageResult<BasicConfigDO> selectPage(BasicConfigPageReqVO reqVO) {
        Page<BasicConfigDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<BasicConfigDO> wrapper = new LambdaQueryWrapperX<BasicConfigDO>()
            .likeIfPresent(BasicConfigDO::getSystemName, reqVO.getSystemName())
            .eqIfPresent(BasicConfigDO::getSystemNameShort, reqVO.getSystemNameShort())
            .betweenIfPresent(BasicConfigDO::getOnlineDate, reqVO.getOnlineDate())
            .eqIfPresent(BasicConfigDO::getLogoLoginUrl, reqVO.getLogoLoginUrl())
            .eqIfPresent(BasicConfigDO::getSystemNameUrl, reqVO.getSystemNameUrl())
            .eqIfPresent(BasicConfigDO::getVersionInfo, reqVO.getVersionInfo())
            .eqIfPresent(BasicConfigDO::getSystemInfo, reqVO.getSystemInfo())
            .likeIfPresent(BasicConfigDO::getLogoLoginUrlName, reqVO.getLogoLoginUrlName())
            .likeIfPresent(BasicConfigDO::getSystemNameUrlName, reqVO.getSystemNameUrlName())
            .eqIfPresent(BasicConfigDO::getLoginType, reqVO.getLoginType())
            .eqIfPresent(BasicConfigDO::getPublicKey, reqVO.getPublicKey())
            .eqIfPresent(BasicConfigDO::getBackgroundImage, reqVO.getBackgroundImage())
            .eqIfPresent(BasicConfigDO::getLoginPageEffect, reqVO.getLoginPageEffect())
            .eqIfPresent(BasicConfigDO::getIsShowPlugin, reqVO.getIsShowPlugin())
            .eqIfPresent(BasicConfigDO::getIsShowBrowser, reqVO.getIsShowBrowser())
            .eqIfPresent(BasicConfigDO::getIsShowHyperlink, reqVO.getIsShowHyperlink())
            .eqIfPresent(BasicConfigDO::getBaseSystemInfoUrl, reqVO.getBaseSystemInfoUrl())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(BasicConfigDO::getAddTime);
        }
        Page<BasicConfigDO> basicConfigPage = selectPage(page, wrapper);
        return new PageResult<>(basicConfigPage.getRecords(), basicConfigPage.getTotal());
    }
    default List<BasicConfigDO> selectList(BasicConfigListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BasicConfigDO>()
            .likeIfPresent(BasicConfigDO::getSystemName, reqVO.getSystemName())
            .eqIfPresent(BasicConfigDO::getSystemNameShort, reqVO.getSystemNameShort())
            .betweenIfPresent(BasicConfigDO::getOnlineDate, reqVO.getOnlineDate())
            .eqIfPresent(BasicConfigDO::getLogoLoginUrl, reqVO.getLogoLoginUrl())
            .eqIfPresent(BasicConfigDO::getSystemNameUrl, reqVO.getSystemNameUrl())
            .eqIfPresent(BasicConfigDO::getVersionInfo, reqVO.getVersionInfo())
            .eqIfPresent(BasicConfigDO::getSystemInfo, reqVO.getSystemInfo())
            .likeIfPresent(BasicConfigDO::getLogoLoginUrlName, reqVO.getLogoLoginUrlName())
            .likeIfPresent(BasicConfigDO::getSystemNameUrlName, reqVO.getSystemNameUrlName())
            .eqIfPresent(BasicConfigDO::getLoginType, reqVO.getLoginType())
            .eqIfPresent(BasicConfigDO::getPublicKey, reqVO.getPublicKey())
            .eqIfPresent(BasicConfigDO::getBackgroundImage, reqVO.getBackgroundImage())
            .eqIfPresent(BasicConfigDO::getLoginPageEffect, reqVO.getLoginPageEffect())
            .eqIfPresent(BasicConfigDO::getIsShowPlugin, reqVO.getIsShowPlugin())
            .eqIfPresent(BasicConfigDO::getIsShowBrowser, reqVO.getIsShowBrowser())
            .eqIfPresent(BasicConfigDO::getIsShowHyperlink, reqVO.getIsShowHyperlink())
            .eqIfPresent(BasicConfigDO::getBaseSystemInfoUrl, reqVO.getBaseSystemInfoUrl())
        .orderByDesc(BasicConfigDO::getAddTime));    }


    }
