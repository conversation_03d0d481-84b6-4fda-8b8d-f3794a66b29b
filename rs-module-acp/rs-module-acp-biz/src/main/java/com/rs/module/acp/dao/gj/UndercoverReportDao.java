package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverReportListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverReportPageReqVO;
import com.rs.module.acp.entity.gj.UndercoverReportDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-耳目反映情况 Dao
*
* <AUTHOR>
*/
@Mapper
public interface UndercoverReportDao extends IBaseDao<UndercoverReportDO> {


    default PageResult<UndercoverReportDO> selectPage(UndercoverReportPageReqVO reqVO) {
        Page<UndercoverReportDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<UndercoverReportDO> wrapper = new LambdaQueryWrapperX<UndercoverReportDO>()
            .eqIfPresent(UndercoverReportDO::getUndercoverId, reqVO.getUndercoverId())
            .eqIfPresent(UndercoverReportDO::getJgrybm, reqVO.getJgrybm())
            .betweenIfPresent(UndercoverReportDO::getReportTime, reqVO.getReportTime())
            .eqIfPresent(UndercoverReportDO::getReportInfo, reqVO.getReportInfo())
            .eqIfPresent(UndercoverReportDO::getRemark, reqVO.getRemark())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(UndercoverReportDO::getAddTime);
        }
        Page<UndercoverReportDO> undercoverReportPage = selectPage(page, wrapper);
        return new PageResult<>(undercoverReportPage.getRecords(), undercoverReportPage.getTotal());
    }
    default List<UndercoverReportDO> selectList(UndercoverReportListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<UndercoverReportDO>()
            .eqIfPresent(UndercoverReportDO::getUndercoverId, reqVO.getUndercoverId())
            .eqIfPresent(UndercoverReportDO::getJgrybm, reqVO.getJgrybm())
            .betweenIfPresent(UndercoverReportDO::getReportTime, reqVO.getReportTime())
            .eqIfPresent(UndercoverReportDO::getReportInfo, reqVO.getReportInfo())
            .eqIfPresent(UndercoverReportDO::getRemark, reqVO.getRemark())
        .orderByDesc(UndercoverReportDO::getAddTime));    }


    }
