package com.rs.module.acp.service.gj.riskassmt;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.gj.vo.riskassmt.RiskAssmtTodoListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.riskassmt.RiskAssmtTodoPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.riskassmt.RiskAssmtTodoSaveReqVO;
import com.rs.module.acp.entity.gj.RiskAssmtTodoDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-管教业务-风险评估待办 Service 接口
 *
 * <AUTHOR>
 */
public interface RiskAssmtTodoService extends IBaseService<RiskAssmtTodoDO>{

    /**
     * 创建实战平台-管教业务-风险评估待办
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createRiskAssmtTodo(@Valid RiskAssmtTodoSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-风险评估待办
     *
     * @param updateReqVO 更新信息
     */
    //void updateRiskAssmtTodo(@Valid RiskAssmtTodoSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-风险评估待办
     *
     * @param id 编号
     */
    void deleteRiskAssmtTodo(String id);

    /**
     * 获得实战平台-管教业务-风险评估待办
     *
     * @param id 编号
     * @return 实战平台-管教业务-风险评估待办
     */
    RiskAssmtTodoDO getRiskAssmtTodo(String id);

    /**
    * 获得实战平台-管教业务-风险评估待办分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-风险评估待办分页
    */
    PageResult<RiskAssmtTodoDO> getRiskAssmtTodoPage(RiskAssmtTodoPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-风险评估待办列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-风险评估待办列表
    */
    List<RiskAssmtTodoDO> getRiskAssmtTodoList(RiskAssmtTodoListReqVO listReqVO);

    /**
     * 新入所风险信息
     * <AUTHOR>
     * @date 2025/6/15 19:02
     * @param []
     * @return void
     */
    void newEntrantJob();

    /**
     * 重点人员关注
     * <AUTHOR>
     * @date 2025/6/19 18:48
     * @param []
     * @return void
     */
    void attentionPersonnel();
}
