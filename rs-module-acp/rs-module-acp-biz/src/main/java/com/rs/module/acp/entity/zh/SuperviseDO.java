package com.rs.module.acp.entity.zh;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 综合管理-督导信息 DO
 *
 * <AUTHOR>
 */
@TableName("acp_zh_supervise")
@KeySequence("acp_zh_supervise_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_supervise")
public class SuperviseDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 督导编码
     */
    private String superviseNo;
    /**
     * 违规单位id
     */
    private String unitId;
    /**
     * 违规单位名称
     */
    private String unitName;
    /**
     * 违规时间
     */
    private Date outlineTime;
    /**
     * 违规地点id
     */
    private String addressId;
    /**
     * 违规地点名称
     */
    private String addressName;
    /**
     * 巡查来源
     */
    private String patrolSource;
    /**
     * 违规对象
     */
    private String outlineObject;
    /**
     * 违规人员编号
     */
    private String outlinePeopleNo;
    /**
     * 违规人员姓名
     */
    private String outlinePeopleName;
    /**
     * 违规问题数
     */
    private Integer questionNum;
    /**
     * 拟加扣分值
     */
    private BigDecimal deductValue;
    /**
     * 违规详情
     */
    private String detail;
    /**
     * 巡查时间
     */
    private Date aroundTime;
    /**
     * 整改期限
     */
    private Date correctPeriod;
    /**
     * 登记人id
     */
    private String operateUserSfzh;
    /**
     * 登记人名称
     */
    private String operateUserName;
    /**
     * 登记时间
     */
    private Date operateTime;
    /**
     * 是否整改反馈
     */
    private Short isCorrect;
    /**
     * 督导状态(01待审批，02不同意，03退回，04待整改，05已申诉，06已反馈，07已完成)
     */
    private String status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 违规类型
     */
    private String outLineViolation;
    /**
     * 附件信息
     */
    private String attachment;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 督导问题
     */
    private String superviseQuestion;
    /**
     * 接收人身份证号
     */
    private String receiveUserSfzh;
    /**
     * 接收人姓名
     */
    private String receiveUserName;
    /**
     * 当前审批人身份证号
     */
    private String approvalUserSfzh;
    /**
     * 当前审批人姓名
     */
    private String approvalUserName;
}
