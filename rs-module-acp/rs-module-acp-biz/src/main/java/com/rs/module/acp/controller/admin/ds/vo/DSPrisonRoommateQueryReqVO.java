package com.rs.module.acp.controller.admin.ds.vo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-数据固化-同监室人员查询 Request VO")
@Data
public class DSPrisonRoommateQueryReqVO {
    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监室id")
    /*@NotEmpty(message = "监室id不能为空")*/
    private String roomId;

    @ApiModelProperty("查询开始时间")
    private Date startTime;

    @ApiModelProperty("查询结束时间")
    private Date endTime;
}
