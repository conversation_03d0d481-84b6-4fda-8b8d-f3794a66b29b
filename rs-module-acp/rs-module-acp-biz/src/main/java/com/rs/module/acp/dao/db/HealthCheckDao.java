package com.rs.module.acp.dao.db;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.db.HealthCheckDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-收押业务-入所健康检查登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface HealthCheckDao extends IBaseDao<HealthCheckDO> {


    default PageResult<HealthCheckDO> selectPage(HealthCheckPageReqVO reqVO) {
        Page<HealthCheckDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<HealthCheckDO> wrapper = new LambdaQueryWrapperX<HealthCheckDO>()
            .eqIfPresent(HealthCheckDO::getRybh, reqVO.getRybh())
            .likeIfPresent(HealthCheckDO::getName, reqVO.getName())
            .eqIfPresent(HealthCheckDO::getJkzk, reqVO.getJkzk())
            .eqIfPresent(HealthCheckDO::getSg, reqVO.getSg())
            .eqIfPresent(HealthCheckDO::getTz, reqVO.getTz())
            .eqIfPresent(HealthCheckDO::getZc, reqVO.getZc())
            .eqIfPresent(HealthCheckDO::getTw, reqVO.getTw())
            .eqIfPresent(HealthCheckDO::getXx, reqVO.getXx())
            .eqIfPresent(HealthCheckDO::getXyssy, reqVO.getXyssy())
            .eqIfPresent(HealthCheckDO::getXyszy, reqVO.getXyszy())
            .eqIfPresent(HealthCheckDO::getXl, reqVO.getXl())
            .eqIfPresent(HealthCheckDO::getYybdnl, reqVO.getYybdnl())
            .eqIfPresent(HealthCheckDO::getYybdnljtqk, reqVO.getYybdnljtqk())
            .eqIfPresent(HealthCheckDO::getZthdsfzc, reqVO.getZthdsfzc())
            .eqIfPresent(HealthCheckDO::getZthdjtqk, reqVO.getZthdjtqk())
            .eqIfPresent(HealthCheckDO::getSlzksfzc, reqVO.getSlzksfzc())
            .eqIfPresent(HealthCheckDO::getSljtqk, reqVO.getSljtqk())
            .eqIfPresent(HealthCheckDO::getTlzksfzc, reqVO.getTlzksfzc())
            .eqIfPresent(HealthCheckDO::getTlzkjtqk, reqVO.getTlzkjtqk())
            .eqIfPresent(HealthCheckDO::getZlzksfzc, reqVO.getZlzksfzc())
            .eqIfPresent(HealthCheckDO::getZlzkjtqk, reqVO.getZlzkjtqk())
            .eqIfPresent(HealthCheckDO::getJszksfzc, reqVO.getJszksfzc())
            .eqIfPresent(HealthCheckDO::getJszkjtqk, reqVO.getJszkjtqk())
            .eqIfPresent(HealthCheckDO::getXcgscdz, reqVO.getXcgscdz())
            .eqIfPresent(HealthCheckDO::getXdtscdz, reqVO.getXdtscdz())
            .eqIfPresent(HealthCheckDO::getBcscdz, reqVO.getBcscdz())
            .eqIfPresent(HealthCheckDO::getXpscdz, reqVO.getXpscdz())
            .eqIfPresent(HealthCheckDO::getXbctscdz, reqVO.getXbctscdz())
            .eqIfPresent(HealthCheckDO::getNxrzjcg, reqVO.getNxrzjcg())
            .eqIfPresent(HealthCheckDO::getYwdxs, reqVO.getYwdxs())
            .eqIfPresent(HealthCheckDO::getYwyzcbrb, reqVO.getYwyzcbrb())
            .eqIfPresent(HealthCheckDO::getYzcbrbmc, reqVO.getYzcbrbmc())
            .eqIfPresent(HealthCheckDO::getJwbs, reqVO.getJwbs())
            .eqIfPresent(HealthCheckDO::getJwjblx, reqVO.getJwjblx())
            .eqIfPresent(HealthCheckDO::getTbsfywss, reqVO.getTbsfywss())
            .eqIfPresent(HealthCheckDO::getWsqkjlfs, reqVO.getWsqkjlfs())
            .eqIfPresent(HealthCheckDO::getWsqkscdz, reqVO.getWsqkscdz())
            .eqIfPresent(HealthCheckDO::getZswb, reqVO.getZswb())
            .eqIfPresent(HealthCheckDO::getZsrq, reqVO.getZsrq())
            .eqIfPresent(HealthCheckDO::getZsyy, reqVO.getZsyy())
            .eqIfPresent(HealthCheckDO::getYsyj, reqVO.getYsyj())
            .eqIfPresent(HealthCheckDO::getJcrsfzh, reqVO.getJcrsfzh())
            .eqIfPresent(HealthCheckDO::getJcr, reqVO.getJcr())
            .eqIfPresent(HealthCheckDO::getJcsj, reqVO.getJcsj())
            .eqIfPresent(HealthCheckDO::getBz, reqVO.getBz())
            .eqIfPresent(HealthCheckDO::getStatus, reqVO.getStatus())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(HealthCheckDO::getAddTime);
        }
        Page<HealthCheckDO> healthCheckPage = selectPage(page, wrapper);
        return new PageResult<>(healthCheckPage.getRecords(), healthCheckPage.getTotal());
    }
    default List<HealthCheckDO> selectList(HealthCheckListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<HealthCheckDO>()
            .eqIfPresent(HealthCheckDO::getRybh, reqVO.getRybh())
            .likeIfPresent(HealthCheckDO::getName, reqVO.getName())
            .eqIfPresent(HealthCheckDO::getJkzk, reqVO.getJkzk())
            .eqIfPresent(HealthCheckDO::getSg, reqVO.getSg())
            .eqIfPresent(HealthCheckDO::getTz, reqVO.getTz())
            .eqIfPresent(HealthCheckDO::getZc, reqVO.getZc())
            .eqIfPresent(HealthCheckDO::getTw, reqVO.getTw())
            .eqIfPresent(HealthCheckDO::getXx, reqVO.getXx())
            .eqIfPresent(HealthCheckDO::getXyssy, reqVO.getXyssy())
            .eqIfPresent(HealthCheckDO::getXyszy, reqVO.getXyszy())
            .eqIfPresent(HealthCheckDO::getXl, reqVO.getXl())
            .eqIfPresent(HealthCheckDO::getYybdnl, reqVO.getYybdnl())
            .eqIfPresent(HealthCheckDO::getYybdnljtqk, reqVO.getYybdnljtqk())
            .eqIfPresent(HealthCheckDO::getZthdsfzc, reqVO.getZthdsfzc())
            .eqIfPresent(HealthCheckDO::getZthdjtqk, reqVO.getZthdjtqk())
            .eqIfPresent(HealthCheckDO::getSlzksfzc, reqVO.getSlzksfzc())
            .eqIfPresent(HealthCheckDO::getSljtqk, reqVO.getSljtqk())
            .eqIfPresent(HealthCheckDO::getTlzksfzc, reqVO.getTlzksfzc())
            .eqIfPresent(HealthCheckDO::getTlzkjtqk, reqVO.getTlzkjtqk())
            .eqIfPresent(HealthCheckDO::getZlzksfzc, reqVO.getZlzksfzc())
            .eqIfPresent(HealthCheckDO::getZlzkjtqk, reqVO.getZlzkjtqk())
            .eqIfPresent(HealthCheckDO::getJszksfzc, reqVO.getJszksfzc())
            .eqIfPresent(HealthCheckDO::getJszkjtqk, reqVO.getJszkjtqk())
            .eqIfPresent(HealthCheckDO::getXcgscdz, reqVO.getXcgscdz())
            .eqIfPresent(HealthCheckDO::getXdtscdz, reqVO.getXdtscdz())
            .eqIfPresent(HealthCheckDO::getBcscdz, reqVO.getBcscdz())
            .eqIfPresent(HealthCheckDO::getXpscdz, reqVO.getXpscdz())
            .eqIfPresent(HealthCheckDO::getXbctscdz, reqVO.getXbctscdz())
            .eqIfPresent(HealthCheckDO::getNxrzjcg, reqVO.getNxrzjcg())
            .eqIfPresent(HealthCheckDO::getYwdxs, reqVO.getYwdxs())
            .eqIfPresent(HealthCheckDO::getYwyzcbrb, reqVO.getYwyzcbrb())
            .eqIfPresent(HealthCheckDO::getYzcbrbmc, reqVO.getYzcbrbmc())
            .eqIfPresent(HealthCheckDO::getJwbs, reqVO.getJwbs())
            .eqIfPresent(HealthCheckDO::getJwjblx, reqVO.getJwjblx())
            .eqIfPresent(HealthCheckDO::getTbsfywss, reqVO.getTbsfywss())
            .eqIfPresent(HealthCheckDO::getWsqkjlfs, reqVO.getWsqkjlfs())
            .eqIfPresent(HealthCheckDO::getWsqkscdz, reqVO.getWsqkscdz())
            .eqIfPresent(HealthCheckDO::getZswb, reqVO.getZswb())
            .eqIfPresent(HealthCheckDO::getZsrq, reqVO.getZsrq())
            .eqIfPresent(HealthCheckDO::getZsyy, reqVO.getZsyy())
            .eqIfPresent(HealthCheckDO::getYsyj, reqVO.getYsyj())
            .eqIfPresent(HealthCheckDO::getJcrsfzh, reqVO.getJcrsfzh())
            .eqIfPresent(HealthCheckDO::getJcr, reqVO.getJcr())
            .eqIfPresent(HealthCheckDO::getJcsj, reqVO.getJcsj())
            .eqIfPresent(HealthCheckDO::getBz, reqVO.getBz())
            .eqIfPresent(HealthCheckDO::getStatus, reqVO.getStatus())
        .orderByDesc(HealthCheckDO::getAddTime));    }


    }
