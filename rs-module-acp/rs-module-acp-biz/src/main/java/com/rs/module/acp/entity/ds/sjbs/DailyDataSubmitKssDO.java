package com.rs.module.acp.entity.ds.sjbs;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-数据固化-每日数据报送(看守所) DO
 *
 * <AUTHOR>
 */
@TableName("acp_ds_daily_data_submit_kss")
@KeySequence("acp_ds_daily_data_submit_kss_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_ds_daily_data_submit_kss")
public class DailyDataSubmitKssDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 固化日期
     */
    private String solidificationDate;
    /**
     * 固化开始日期
     */
    private String startDate;
    /**
     * 固化结束日期
     */
    private String endDate;
    /**
     * 被监管人员总数
     */
    private Integer bjgry;
    /**
     * 男性被监管人员总数
     */
    private Integer bjgryMale;
    /**
     * 女性被监管人员总数
     */
    private Integer bjgryFemale;
    /**
     * 未成年被监管人员总数
     */
    private Integer bjgryMinor;
    /**
     * 一级重大风险总数
     */
    private Integer yjzdfx;
    /**
     * 二级重大风险总数
     */
    private Integer ejzdfx;
    /**
     * 三级重大风险总数
     */
    private Integer sjzdfx;
    /**
     * 戒具使用总数
     */
    private Integer jgsy;
    /**
     * 精神异常总数
     */
    private Integer jsyc;
    /**
     * 等待精神鉴定总数
     */
    private Integer ddjsjd;
    /**
     * 谈话教育总数
     */
    private Integer thjy;
    /**
     * 监视调整总数
     */
    private Integer jstz;
    /**
     * 侦查阶段总数
     */
    private Integer zcjd;
    /**
     * 审查阶段总数
     */
    private Integer scjd;
    /**
     * 审判阶段总数
     */
    private Integer spjd;
    /**
     * 待交付执行总数
     */
    private Integer djfzx;
    /**
     * 留所服刑总数
     */
    private Integer lsfx;
    /**
     * 亲属会见总数
     */
    private Integer qshj;
    /**
     * 刑事拘留总数
     */
    private Integer xsjl;
    /**
     * 逮捕总数
     */
    private Integer db;
    /**
     * 取保候审总数
     */
    private Integer qbhs;
    /**
     * 监室居住总数
     */
    private Integer jsjz;

}
