package com.rs.module.acp.service.pm;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.cache.RedisClient;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.rs.module.acp.util.QingyanLocalSenseUtil;
import com.tsingoal.pojo.LsTagPosition;
import com.tsingoal.pojo.LsVitalSignInfo;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.LocalsenseTagPersonDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pm.LocalsenseTagPersonDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-监管管理-定位标签与人员绑定 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LocalsenseTagPersonServiceImpl extends BaseServiceImpl<LocalsenseTagPersonDao, LocalsenseTagPersonDO> implements LocalsenseTagPersonService {

    @Resource
    private LocalsenseTagPersonDao localsenseTagPersonDao;
    @Override
    public String createLocalsenseTagPerson(LocalsenseTagPersonSaveReqVO reqVO) {
        // 插入
        LocalsenseTagPersonDO entity = BeanUtils.toBean(reqVO, LocalsenseTagPersonDO.class);
        localsenseTagPersonDao.insert(entity);
        // 返回
        return entity.getId();
    }

    @Override
    public void updateLocalsenseTagPerson(LocalsenseTagPersonSaveReqVO reqVO) {
        // 校验存在
        validateLocalsenseTagPersonExists(reqVO.getId());
        // 更新
        LocalsenseTagPersonDO entity = BeanUtils.toBean(reqVO, LocalsenseTagPersonDO.class);
        localsenseTagPersonDao.updateById(entity);
    }

    @Override
    public void deleteLocalsenseTagPerson(String id) {
        // 校验存在
        validateLocalsenseTagPersonExists(id);
        // 删除
        localsenseTagPersonDao.deleteById(id);
    }

    private void validateLocalsenseTagPersonExists(String id) {
        if (localsenseTagPersonDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-定位标签与人员绑定数据不存在");
        }
    }

    @Override
    public LocalsenseTagPersonDO getLocalsenseTagPerson(String id) {
        return localsenseTagPersonDao.selectById(id);
    }

    @Override
    public PageResult<LocalsenseTagPersonDO> getLocalsenseTagPersonPage(LocalsenseTagPersonPageReqVO pageReqVO) {
        return localsenseTagPersonDao.selectPage(pageReqVO);
    }

    @Override
    public List<LocalsenseTagPersonDO> getLocalsenseTagPersonList(LocalsenseTagPersonListReqVO listReqVO) {
        return localsenseTagPersonDao.selectList(listReqVO);
    }

    /**
     * 入所被监管人员绑定标签(手环)
     * @param reqVO
     * @return
     */
    @Override
    public String rsCreateLocalsenseTagPerson(LocalsenseTagPersonRsSaveReqVO reqVO) {
        try{
            validateBindInfo(reqVO.getTagId(),reqVO.getBindPersonId());
        }catch (Exception e){
            throw new ServerException(e.getMessage());
        }
        LocalsenseTagPersonDO entity = BeanUtils.toBean(reqVO, LocalsenseTagPersonDO.class);
        entity.setBindSource("01");
        entity.setPersonType("01");
        entity.setStatus("01");//已绑定
        if(reqVO.getBindTime() == null )entity.setBindTime(new Date());
        localsenseTagPersonDao.insert(entity);
        // 返回
        return entity.getId();
    }

    /**
     * 智能腕带模块绑定 被监管人员绑定标签(手环)
     * @param reqVO
     * @return
     */
    @Override
    public String znwdCreateLocalsenseTagPerson(LocalsenseTagPersonZnwdSaveReqVO reqVO) {
        try{
            validateBindInfo(reqVO.getTagId(),reqVO.getBindPersonId());
        }catch (Exception e){
            throw new ServerException(e.getMessage());
        }
        // 插入
        LocalsenseTagPersonDO entity = BeanUtils.toBean(reqVO, LocalsenseTagPersonDO.class);
        entity.setBindSource("02");
        entity.setPersonType("01");
        entity.setStatus("01");//已绑定
        if(reqVO.getBindTime() == null )entity.setBindTime(new Date());
        localsenseTagPersonDao.insert(entity);
        // 返回
        return entity.getId();
    }

    /**
     * 民警绑定工牌
     * @param reqVO
     * @return
     */
    @Override
    public String mjCreateLocalsenseTagPerson(LocalsenseTagPersonMjSaveReqVO reqVO) {
        try{
            validateBindInfo(reqVO.getTagId(),reqVO.getBindPersonId());
        }catch (Exception e){
            throw new ServerException(e.getMessage());
        }
        // 插入
        LocalsenseTagPersonDO entity = BeanUtils.toBean(reqVO, LocalsenseTagPersonDO.class);
        entity.setBindSource("02");
        entity.setPersonType("02");
        entity.setStatus("01");//已绑定
        if(reqVO.getBindTime() == null )entity.setBindTime(new Date());
        localsenseTagPersonDao.insert(entity);
        // 返回
        return entity.getId();
    }

    /**
     * 查询bindPersonId 是否已经绑定过手环
     * @param bindPersonIds
     * @return
     */
    @Override
    public List<LocalsenseTagPersonDO> getLocalsenseTagPersonByBindPersonIds(String bindPersonIds) {
        if(StringUtil.isEmpty(bindPersonIds)) return null;
        return localsenseTagPersonDao.selectList(new LambdaQueryWrapper<LocalsenseTagPersonDO>()
                .in(LocalsenseTagPersonDO::getBindPersonId, bindPersonIds.split(",")).eq(LocalsenseTagPersonDO::getStatus, "01"));
    }
    /**
     * 根据tagId查询是否已绑定信息
     * @param tagIds
     * @return
     */
    @Override
    public List<LocalsenseTagPersonDO> getLocalsenseTagPersonByTagIds(String tagIds) {
        if(StringUtil.isEmpty(tagIds)) return null;
        return localsenseTagPersonDao.selectList(new LambdaQueryWrapper<LocalsenseTagPersonDO>()
                .in(LocalsenseTagPersonDO::getTagId, tagIds.split(",")).eq(LocalsenseTagPersonDO::getStatus, "01"));
    }
    /**
     * 解绑 id或tagId,bindPersonId 2选1 作为参数
     * @param reqVO
     * @return
     */
    @Override
    public String unbind(LocalsenseTagPersonUnbindSaveReqVO reqVO) {
        LocalsenseTagPersonDO entity = null;
        if(StringUtil.isNotEmpty(reqVO.getId())){
            entity = localsenseTagPersonDao.selectById(reqVO.getId());
        }else if(StringUtil.isNotEmpty(reqVO.getTagId()) && StringUtil.isNotEmpty(reqVO.getBindPersonId())){
            entity = localsenseTagPersonDao.selectOne(new LambdaQueryWrapper<LocalsenseTagPersonDO>()
                    .eq(LocalsenseTagPersonDO::getTagId, reqVO.getTagId())
                    .eq(LocalsenseTagPersonDO::getBindPersonId, reqVO.getBindPersonId())
                    .eq(LocalsenseTagPersonDO::getStatus, "01"));
        }
        if (entity == null) throw new ServerException("请确保解绑参数正确 id或tagId,bindPersonId 2选1");
        entity.setUnbindReason(reqVO.getUnbindReason());
        if(reqVO.getUnbindTime() != null) entity.setUnbindTime(reqVO.getUnbindTime());
        if(entity.getUnbindTime() == null) entity.setUnbindTime(new Date());
        entity.setStatus("02");//已解绑
        localsenseTagPersonDao.updateById(entity);
        // 返回
        return entity.getId();
    }

    /**
     * 查询人员所有历史绑定记录
     * @param bindPersonId
     * @return
     */
    @Override
    public List<LocalsenseTagPersonDO> getLocalsenseTagPersonHistoryList(String bindPersonId) {
        return localsenseTagPersonDao.selectList(new LambdaQueryWrapper<LocalsenseTagPersonDO>()
                .eq(LocalsenseTagPersonDO::getBindPersonId, bindPersonId).orderByDesc(LocalsenseTagPersonDO::getBindTime));
    }

    /**
     * 查询标签所有绑定记录
     * @param tagId
     * @return
     */
    @Override
    public List<LocalsenseTagPersonDO> getLocalsenseTagPersonHistoryListByTagId(String tagId) {
        return localsenseTagPersonDao.selectList(new LambdaQueryWrapper<LocalsenseTagPersonDO>()
                .eq(LocalsenseTagPersonDO::getTagId, tagId).orderByDesc(LocalsenseTagPersonDO::getBindTime));
    }
    public void validateBindInfo(String tagId,String bindPersonId) throws Exception {
        //查询tagId 当前是否已被绑定
        List<LocalsenseTagPersonDO> list = getLocalsenseTagPersonByTagIds(tagId);
        if(CollectionUtil.isNotNull(list)){
            throw new ServerException("该标签已绑定人员："+list.get(0).getBindPersonName());
        }
        list = getLocalsenseTagPersonByBindPersonIds(bindPersonId);
        if(CollectionUtil.isNotNull(list)){
            throw new ServerException("该人员已绑定标签ID："+list.get(0).getTagId());
        }
    }
    @Override
    public List<JSONObject> getLocalSenseInfo(String tagIds){
        String[] tagIdArray = tagIds.split(",");
        List<JSONObject> list = new ArrayList<>();
        for (String tagIdStr : tagIdArray) {
            if (StringUtil.isNotEmpty(tagIdStr)) {
                long tagId = Long.parseLong(tagIdStr);
                JSONObject result = new JSONObject();
                // 获取电量信息
                String batteryPercent = RedisClient.get(QingyanLocalSenseUtil.getCapKey(tagId));
                result.put("batteryPercent",batteryPercent);
                result.put("tagId",tagId);
                // 获取体征信息
                LsVitalSignInfo vitalSignInfo = RedisClient.get(QingyanLocalSenseUtil.getVitalsignKey(tagId),LsVitalSignInfo.class);
                result.put("vitalSignInfo",vitalSignInfo);

                // 获取位置信息
                LsTagPosition position = RedisClient.get(QingyanLocalSenseUtil.getPosKey(tagId),LsTagPosition.class);
                result.put("position",position);
                list.add(result);
            }
        }
        return list;
    }

}
