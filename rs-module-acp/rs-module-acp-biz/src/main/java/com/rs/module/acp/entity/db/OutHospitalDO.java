package com.rs.module.acp.entity.db;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-收押业务-出所就医 DO
 *
 * <AUTHOR>
 */
@TableName("acp_db_out_hospital")
@KeySequence("acp_db_out_hospital_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_db_out_hospital")
public class OutHospitalDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 预约时间
     */
    private Date appointmentTime;
    /**
     * 就诊医院
     */
    private String hospital;
    /**
     * 病情等级
     */
    private String symptomLevel;
    /**
     * 病情描述
     */
    private String symptomDesc;
    /**
     * 精神状况
     */
    private String jszk;
    /**
     * 是否存在拒绝服药或抗拒治疗情况
     */
    private Short sfczjjfyhkjzlqk;
    /**
     * 是否存在吞食异物情况
     */
    private Short sfcztsywqk;
    /**
     * 是否存在不服从管理或自残行为
     */
    private Short sfczbfcglhzcxw;
    /**
     * 是否存在其他微信行为或异常情况
     */
    private Short sfczqtwxxwhycqk;
    /**
     * 呈批医务主任
     */
    private String cpywzr;
    /**
     * 备注
     */
    private String remark;
    /**
     * 呈批医务主任姓名
     */
    private String cpywzrxm;
    /**
     * 经办人
     */
    private String jbr;
    /**
     * 经办时间
     */
    private Date jbsj;
    /**
     * 出所-是否需要用兵单
     */
    private Short sfxyybd;
    /**
     * 医生意见
     */
    private String ywzryj;
    /**
     * 出所-出发时间
     */
    private Date cfsj;
    /**
     * 出所-集合地点
     */
    private String jhdd;
    /**
     * 医务主任备注
     */
    private String ywzrbz;
    /**
     * 出所-在押人员数量
     */
    private Integer zyrysl;
    /**
     * 医务主任身份证号
     */
    private String ywzrsfzh;
    /**
     * 出所-民警人数
     */
    private Integer mjrs;
    /**
     * 医务主任姓名
     */
    private String ywzrxm;
    /**
     * 出所-拟用兵数
     */
    private Integer nybs;
    /**
     * 医务主任意见经办人
     */
    private String ywzryjJbr;
    /**
     * 出所-到达地点
     */
    private String dddd;
    /**
     * 医务主任意见经办时间
     */
    private Date ywzryjJbsj;
    /**
     * 出所-外押原因
     */
    private String wyyy;
    /**
     * 出所-押解单位
     */
    private String yjdw;
    /**
     * 出所-押解路线
     */
    private String yjlx;
    /**
     * 出所-押解民警
     */
    private String yjmj;
    /**
     * 出所-押解辅警
     */
    private String yjfj;
    /**
     * 出所-特别要求
     */
    private String tbyq;
    /**
     * 出所-医务人员
     */
    private String ywry;
    /**
     * 出所是否离所确认
     */
    private Short sflsqr;
    /**
     * 出所-电子脚铐编号
     */
    private String dzjkbh;
    /**
     * 出所-离所确认备注
     */
    private String lsqrbz;
    /**
     * 出所-押解车牌号
     */
    private String yjcph;
    /**
     * 出所-勤务安排经办人
     */
    private String qwapJbr;
    /**
     * 出所-勤务安排经办时间
     */
    private Date qwapJbsj;
    /**
     * 出所-执法记录仪
     */
    private String zfjly;
    /**
     * 出所-安全管控措施
     */
    private String aqgkcs;
    /**
     * 回所-押解单位
     */
    private String hsYjdw;
    /**
     * 回所-押解民警
     */
    private String hsYjmj;
    /**
     * 回所-押解辅警
     */
    private String hsYjfj;
    /**
     * 回所-医务人员
     */
    private String hsYwry;
    /**
     * 回所-电子脚铐编号
     */
    private String hsDzjkbh;
    /**
     * 回所-押解车牌号
     */
    private String hsYjcph;
    /**
     * 回所-执法记录仪
     */
    private String hsZfjly;
    /**
     * 回所-安全管控措施
     */
    private String hsAqgkcs;
    /**
     * 回所是否需要用兵单
     */
    private Short hsSfxyybd;
    /**
     * 回所出发时间
     */
    private Date hsCfsj;
    /**
     * 回所集合地点
     */
    private String hsJhdd;
    /**
     * 回所在押人员数量
     */
    private Integer hsZyrysl;
    /**
     * 出所-是否更换戒具
     */
    private Short sfghjj;
    /**
     * 回所民警人数
     */
    private Integer hsMjrs;
    /**
     * 出所-是否开启电子脚扣
     */
    private Short sfkqdzjk;
    /**
     * 回所拟用兵数
     */
    private Integer hsNybs;
    /**
     * 出所-是否开启执法记录义
     */
    private Short sfkqzfjly;
    /**
     * 回所到达地点
     */
    private String hsDddd;
    /**
     * 回所外押原因
     */
    private String hsWyyy;
    /**
     * 回所押解路线
     */
    private String hsYjlx;
    /**
     * 回所特别要求
     */
    private String hsTbyq;
    /**
     * 回所-是否更换戒具
     */
    private Short hsSfghjj;
    /**
     * 回所-是否开启电子脚扣
     */
    private Short hsSfkqdzjk;
    /**
     * 回所-是否开启执法记录义
     */
    private Short hsSfkqzfjly;
    /**
     * 是否回所确认
     */
    private Short sfhsqr;
    /**
     * 回所确认备注
     */
    private String hsqrbz;
    /**
     * 回所-勤务安排经办人
     */
    private String hsQwapJbr;
    /**
     * 回所-勤务安排经办时间
     */
    private Date hsQwapJbsj;
    /**
     * 办理状态
     */
    private String status;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 流程阶段
     */
    private String currentStep;
    /**
     * 回所ACT流程实例Id
     */
    private String hsActInstId;
    /**
     * 回所任务ID
     */
    private String hsTaskId;


    private String csjyyy;
}
