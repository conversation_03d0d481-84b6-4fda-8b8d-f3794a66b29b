package com.rs.module.acp.service.db;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.TransferPrisonerDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.db.TransferPrisonerDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-羁押业务-转所人员 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TransferPrisonerServiceImpl extends BaseServiceImpl<TransferPrisonerDao, TransferPrisonerDO> implements TransferPrisonerService {

    @Resource
    private TransferPrisonerDao transferPrisonerDao;

    @Override
    public String createTransferPrisoner(TransferPrisonerSaveReqVO createReqVO) {
        // 插入
        TransferPrisonerDO transferPrisoner = BeanUtils.toBean(createReqVO, TransferPrisonerDO.class);
        transferPrisonerDao.insert(transferPrisoner);
        // 返回
        return transferPrisoner.getId();
    }

    @Override
    public void updateTransferPrisoner(TransferPrisonerSaveReqVO updateReqVO) {
        // 校验存在
        validateTransferPrisonerExists(updateReqVO.getId());
        // 更新
        TransferPrisonerDO updateObj = BeanUtils.toBean(updateReqVO, TransferPrisonerDO.class);
        transferPrisonerDao.updateById(updateObj);
    }

    @Override
    public void deleteTransferPrisoner(String id) {
        // 校验存在
        validateTransferPrisonerExists(id);
        // 删除
        transferPrisonerDao.deleteById(id);
    }

    private void validateTransferPrisonerExists(String id) {
        if (transferPrisonerDao.selectById(id) == null) {
            throw new ServerException("实战平台-羁押业务-转所人员数据不存在");
        }
    }

    @Override
    public TransferPrisonerDO getTransferPrisoner(String id) {
        return transferPrisonerDao.selectById(id);
    }

    @Override
    public PageResult<TransferPrisonerDO> getTransferPrisonerPage(TransferPrisonerPageReqVO pageReqVO) {
        return transferPrisonerDao.selectPage(pageReqVO);
    }

    @Override
    public List<TransferPrisonerDO> getTransferPrisonerList(TransferPrisonerListReqVO listReqVO) {
        return transferPrisonerDao.selectList(listReqVO);
    }


}
