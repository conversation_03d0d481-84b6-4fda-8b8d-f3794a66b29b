package com.rs.module.acp.entity.gj.conflict;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 实战平台-管教业务-社会矛盾回访 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_conflict_followup")
@KeySequence("acp_gj_conflict_followup_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConflictFollowupDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 事件ID
     */
    private String eventId;
    /**
     * 事件编号
     */
    private String eventCode;
    /**
     * 回访方式
     */
    private String followUpWay;
    /**
     * 收到锦旗数
     */
    private Integer receivedBannersCnt;
    /**
     * 收到牌匾数
     */
    private Integer receivedPlaquesCnt;
    /**
     * 收到感谢信数
     */
    private Integer receivedThankYouLettersCnt;
    /**
     * 履行纠纷款项金额
     */
    private BigDecimal disputePaymentAmount;
    /**
     * 停访息诉起数
     */
    private Integer ceaseVisitsAndEndLitigationCnt;
    /**
     * 回访时间
     */
    private Date followUpTime;
    /**
     * 回访情况
     */
    private String followUpInfo;
    /**
     * 回访经办人
     */
    private String followupOperatorSfzh;
    /**
     * 回访经办人姓名
     */
    private String followupOperatorXm;
    /**
     * 回访经办时间
     */
    private Date followupOperatorTime;
    /**
     * 附件地址
     */
    private String attUrl;
    /**
     * 备注
     */
    private String remark;

}
