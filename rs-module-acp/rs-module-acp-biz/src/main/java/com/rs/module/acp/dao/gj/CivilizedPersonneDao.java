package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne.CivilizedPersonneListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne.CivilizedPersonnePageReqVO;
import com.rs.module.acp.entity.gj.CivilizedPersonneDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-文明个人登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface CivilizedPersonneDao extends IBaseDao<CivilizedPersonneDO> {


    default PageResult<CivilizedPersonneDO> selectPage(CivilizedPersonnePageReqVO reqVO) {
        Page<CivilizedPersonneDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<CivilizedPersonneDO> wrapper = new LambdaQueryWrapperX<CivilizedPersonneDO>()
            .eqIfPresent(CivilizedPersonneDO::getEvalMonth, reqVO.getEvalMonth())
            .eqIfPresent(CivilizedPersonneDO::getCivilizedPersonne, reqVO.getCivilizedPersonne())
            .betweenIfPresent(CivilizedPersonneDO::getApplyTime, reqVO.getApplyTime())
            .eqIfPresent(CivilizedPersonneDO::getApplyUserSfzh, reqVO.getApplyUserSfzh())
            .eqIfPresent(CivilizedPersonneDO::getApplyUser, reqVO.getApplyUser())
            .eqIfPresent(CivilizedPersonneDO::getStatus, reqVO.getStatus())
            .eqIfPresent(CivilizedPersonneDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(CivilizedPersonneDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(CivilizedPersonneDO::getAddTime);
        }
        Page<CivilizedPersonneDO> civilizedPersonnePage = selectPage(page, wrapper);
        return new PageResult<>(civilizedPersonnePage.getRecords(), civilizedPersonnePage.getTotal());
    }
    default List<CivilizedPersonneDO> selectList(CivilizedPersonneListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CivilizedPersonneDO>()
            .eqIfPresent(CivilizedPersonneDO::getEvalMonth, reqVO.getEvalMonth())
            .likeIfPresent(CivilizedPersonneDO::getEvalMonth, reqVO.getYear())
            .eqIfPresent(CivilizedPersonneDO::getOrgCode, reqVO.getOrgCode())
            .eqIfPresent(CivilizedPersonneDO::getCivilizedPersonne, reqVO.getCivilizedPersonne())
            .betweenIfPresent(CivilizedPersonneDO::getApplyTime, reqVO.getApplyTime())
            .eqIfPresent(CivilizedPersonneDO::getApplyUserSfzh, reqVO.getApplyUserSfzh())
            .eqIfPresent(CivilizedPersonneDO::getApplyUser, reqVO.getApplyUser())
            .eqIfPresent(CivilizedPersonneDO::getStatus, reqVO.getStatus())
        .orderByDesc(CivilizedPersonneDO::getEvalMonth));    }

    List<CivilizedPersonneDO> getNowPushInfo();

    }
