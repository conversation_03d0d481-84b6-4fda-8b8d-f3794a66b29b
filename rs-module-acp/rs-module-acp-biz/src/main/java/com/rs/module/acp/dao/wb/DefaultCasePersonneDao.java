package com.rs.module.acp.dao.wb;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.DefaultCasePersonneDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-窗口业务-办案人员 Dao
*
* <AUTHOR>
*/
@Mapper
public interface DefaultCasePersonneDao extends IBaseDao<DefaultCasePersonneDO> {


    default PageResult<DefaultCasePersonneDO> selectPage(DefaultCasePersonnePageReqVO reqVO) {
        Page<DefaultCasePersonneDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<DefaultCasePersonneDO> wrapper = new LambdaQueryWrapperX<DefaultCasePersonneDO>()
            .eqIfPresent(DefaultCasePersonneDO::getJh, reqVO.getJh())
            .eqIfPresent(DefaultCasePersonneDO::getXm, reqVO.getXm())
            .eqIfPresent(DefaultCasePersonneDO::getZjlx, reqVO.getZjlx())
            .eqIfPresent(DefaultCasePersonneDO::getZjhm, reqVO.getZjhm())
            .eqIfPresent(DefaultCasePersonneDO::getBadwdm, reqVO.getBadwdm())
            .eqIfPresent(DefaultCasePersonneDO::getBadwmc, reqVO.getBadwmc())
            .eqIfPresent(DefaultCasePersonneDO::getLxfs, reqVO.getLxfs())
            .eqIfPresent(DefaultCasePersonneDO::getXb, reqVO.getXb())
            .eqIfPresent(DefaultCasePersonneDO::getZpUrl, reqVO.getZpUrl())
            .eqIfPresent(DefaultCasePersonneDO::getGzzjUrl, reqVO.getGzzjUrl())
            .eqIfPresent(DefaultCasePersonneDO::getTjjglx, reqVO.getTjjglx())
            .eqIfPresent(DefaultCasePersonneDO::getJgrybm, reqVO.getJgrybm())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(DefaultCasePersonneDO::getAddTime);
        }
        Page<DefaultCasePersonneDO> defaultCasePersonnePage = selectPage(page, wrapper);
        return new PageResult<>(defaultCasePersonnePage.getRecords(), defaultCasePersonnePage.getTotal());
    }
    default List<DefaultCasePersonneDO> selectList(DefaultCasePersonneListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DefaultCasePersonneDO>()
            .eqIfPresent(DefaultCasePersonneDO::getJh, reqVO.getJh())
            .eqIfPresent(DefaultCasePersonneDO::getXm, reqVO.getXm())
            .eqIfPresent(DefaultCasePersonneDO::getZjlx, reqVO.getZjlx())
            .eqIfPresent(DefaultCasePersonneDO::getZjhm, reqVO.getZjhm())
            .eqIfPresent(DefaultCasePersonneDO::getBadwdm, reqVO.getBadwdm())
            .eqIfPresent(DefaultCasePersonneDO::getBadwmc, reqVO.getBadwmc())
            .eqIfPresent(DefaultCasePersonneDO::getLxfs, reqVO.getLxfs())
            .eqIfPresent(DefaultCasePersonneDO::getXb, reqVO.getXb())
            .eqIfPresent(DefaultCasePersonneDO::getZpUrl, reqVO.getZpUrl())
            .eqIfPresent(DefaultCasePersonneDO::getGzzjUrl, reqVO.getGzzjUrl())
            .eqIfPresent(DefaultCasePersonneDO::getTjjglx, reqVO.getTjjglx())
            .eqIfPresent(DefaultCasePersonneDO::getJgrybm, reqVO.getJgrybm())
        .orderByDesc(DefaultCasePersonneDO::getAddTime));    }


    }
