package com.rs.module.acp.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

/**
 * 实战平台-系统基础配置链接 DO
 *
 * <AUTHOR>
 */
@TableName("acp_sys_basic_config_url")
@KeySequence("acp_sys_basic_config_url_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BasicConfigUrlDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 系统信息表主键
     */
    private String systemId;
    /**
     * url类型 1：插件链接 2：浏览器链接 3：外链链接
     */
    private String urlType;
    /**
     * 链接名称
     */
    private String urlName;
    /**
     * 链接地址
     */
    private String url;
    /**
     * 是否展示 0：否 1：是
     */
    private String isShow;
    /**
     * 排序
     */
    private Integer sort;

}
