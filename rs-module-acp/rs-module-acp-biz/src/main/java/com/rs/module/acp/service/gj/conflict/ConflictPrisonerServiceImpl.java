package com.rs.module.acp.service.gj.conflict;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictPrisonerRespVO;
import com.rs.module.acp.dao.gj.conflict.ConflictPrisonerDao;
import com.rs.module.acp.entity.gj.conflict.ConflictPrisonerDO;
import com.rs.module.acp.entity.gj.conflict.ConflictRegDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

import static com.rs.module.acp.enums.gj.RegStatusEnum.*;


/**
 * 实战平台-管教业务-社会矛盾化解登记-关联在押人员 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ConflictPrisonerServiceImpl extends BaseServiceImpl<ConflictPrisonerDao, ConflictPrisonerDO> implements ConflictPrisonerService {

    @Resource
    private ConflictPrisonerDao conflictPrisonerDao;

    @Override
    public List<ConflictPrisonerRespVO> getPrisonerVOByEventCode(String eventCode) {
        List<ConflictPrisonerDO> prisonerDOS = conflictPrisonerDao.selectList(ConflictPrisonerDO::getEventCode, eventCode);
        if (CollUtil.isNotEmpty(prisonerDOS)) {
            return BeanUtils.toBean(prisonerDOS, ConflictPrisonerRespVO.class);
        }
        return Collections.emptyList();
    }

    @Override
    public void deleteByEventCode(String eventCode) {
        conflictPrisonerDao.deleteByEventCode(CollUtil.newArrayList(eventCode));
    }

    @Override
    public void deleteByEventCode(List<String> eventCodes) {
        if (CollUtil.isEmpty(eventCodes)) {
            return;
        }
        conflictPrisonerDao.deleteByEventCode(eventCodes);
    }

    @Override
    public void updateConfirmStatus(String eventCode, Integer confirmStatus) {
        LambdaUpdateWrapper<ConflictPrisonerDO> wrapper = new LambdaUpdateWrapper<ConflictPrisonerDO>()
                .eq(ConflictPrisonerDO::getEventCode, eventCode)
                .set(ConflictPrisonerDO::getConfirmStatus, confirmStatus);
        conflictPrisonerDao.update(null, wrapper);
    }

    @Override
    public boolean isRegistered(String jgrybm) {
        MPJLambdaWrapperX<ConflictPrisonerDO> inner = new MPJLambdaWrapperX<>();
        inner.innerJoin(ConflictRegDO.class, ConflictRegDO::getEventCode, ConflictPrisonerDO::getEventCode)
                .eq(ConflictPrisonerDO::getJgrybm, jgrybm)
                .in(ConflictRegDO::getRegStatus, CollUtil.newArrayList(YDJ.getCode(), DSP.getCode(), TJZ.getCode(), TJCG.getCode()));
        Long aLong = conflictPrisonerDao.selectJoinCount(inner);
        if (aLong > 0) {
            return true;
        }
        return false;
    }

    @Override
    public boolean batchUpdateByEventCodeAndJgrybm(String eventCode, List<String> jgrybms) {
        if (CollUtil.isEmpty(jgrybms)) {
            return true;
        }
        LambdaUpdateWrapper<ConflictPrisonerDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ConflictPrisonerDO::getEventCode, eventCode)
                .in(ConflictPrisonerDO::getJgrybm, jgrybms)
                .set(ConflictPrisonerDO::getFollowupStatus, 1);
        conflictPrisonerDao.update(null, wrapper);
        return true;
    }
}
