package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.SocialRelationsDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.SocialRelationsDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-社会关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SocialRelationsServiceImpl extends BaseServiceImpl<SocialRelationsDao, SocialRelationsDO> implements SocialRelationsService {

    @Resource
    private SocialRelationsDao socialRelationsDao;

    @Autowired
    private WbCommonService wbCommonService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createSocialRelations(SocialRelationsSaveReqVO createReqVO) {

        //通过证件号码判断，是否已关联该监管人员，若是，则提示【该证件号已关联当前被监管人员，请勿重复添加。】
        if(CollectionUtil.isEmpty(createReqVO.getSocialRelationList())){
            throw new ServerException("没有需要保存的家属信息！");
        }

        List<SocialRelationsChildSaveReqVO> socialRelationsList = createReqVO.getSocialRelationList();
        for(SocialRelationsChildSaveReqVO childSaveReqVO:socialRelationsList){
            LambdaQueryWrapper<SocialRelationsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(SocialRelationsDO::getJgrybm,createReqVO.getJgrybm())
                    .eq(SocialRelationsDO::getIdNumber,childSaveReqVO.getIdNumber());
            List<SocialRelationsDO> list = list(lambdaQueryWrapper);
            if(CollectionUtil.isNotEmpty(list) && list.size() > 0){
                throw new ServerException(String.format("该证件号(%s)已关联当前被监管人员，请勿重复添加。",childSaveReqVO.getIdNumber()));
            }
        }

        List<SocialRelationsDO> saveList = BeanUtils.toBean(socialRelationsList,SocialRelationsDO.class);
        saveList.forEach(x->{x.setJgrybm(createReqVO.getJgrybm());});
        // 返回
        return saveOrUpdateBatch(saveList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSocialRelations(SocialRelationsUpdateReqVO updateReqVO) {
        // 校验存在
        validateSocialRelationsExists(updateReqVO.getId());
        // 更新
        SocialRelationsDO updateObj = BeanUtils.toBean(updateReqVO, SocialRelationsDO.class);
        socialRelationsDao.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSocialRelations(String id) {
        // 校验存在
        validateSocialRelationsExists(id);
        // 删除
        socialRelationsDao.deleteById(id);
    }

    private void validateSocialRelationsExists(String id) {
        if (socialRelationsDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-社会关系数据不存在");
        }
    }

    @Override
    public SocialRelationsDO getSocialRelations(String id) {
        return socialRelationsDao.selectById(id);
    }

    @Override
    public PageResult<SocialRelationsDO> getSocialRelationsPage(SocialRelationsPageReqVO pageReqVO) {
        return socialRelationsDao.selectPage(pageReqVO);
    }

    @Override
    public List<SocialRelationsDO> getSocialRelationsList(SocialRelationsListReqVO listReqVO) {
        return socialRelationsDao.selectList(listReqVO);
    }

    @Override
    public List<SocialRelationsRespVO> getSocialRelationsListByJgrybm(String jgrybm) {
        LambdaQueryWrapper<SocialRelationsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(SocialRelationsDO::getId,SocialRelationsDO::getName,SocialRelationsDO::getRelationship,
                SocialRelationsDO::getIdType,SocialRelationsDO::getIdNumber,SocialRelationsDO::getWorkUnit,
                SocialRelationsDO::getAddress,SocialRelationsDO::getAddTime);
        lambdaQueryWrapper.eq(SocialRelationsDO::getJgrybm,jgrybm);
        List<SocialRelationsDO> list = list(lambdaQueryWrapper);

        return BeanUtils.toBean(list,SocialRelationsRespVO.class);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveSocialRelationsByJgrybm(List<SocialRelationsChildSaveReqVO> familyList, String jgrybm) {
        if(CollectionUtil.isEmpty(familyList)){
            return true;
        }
        List<String> familyIdCardList = new ArrayList<>();
        List<SocialRelationsDO> socialRelationsDOList = new ArrayList<>();
        for(SocialRelationsChildSaveReqVO childSaveReqVO:familyList){
            if(ObjectUtil.isEmpty(childSaveReqVO.getId())){
                childSaveReqVO.setId(StringUtil.getGuid32());
                if(ObjectUtil.isNotEmpty(childSaveReqVO.getRelationsAttch())){
                    childSaveReqVO.setRelationsAttch(wbCommonService.saveFile(null,childSaveReqVO.getRelationsAttch()));
                }
                SocialRelationsDO socialRelationsDO = BeanUtils.toBean(childSaveReqVO,SocialRelationsDO.class);
                socialRelationsDO.setJgrybm(jgrybm);
                socialRelationsDOList.add(socialRelationsDO);
                if(ObjectUtil.isNotEmpty(socialRelationsDO.getIdNumber())){
                    familyIdCardList.add(socialRelationsDO.getIdNumber());
                }
            }else {
                LambdaQueryWrapper<SocialRelationsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.select(SocialRelationsDO::getRelationsAttch).eq(SocialRelationsDO::getId,childSaveReqVO.getId());
                SocialRelationsDO socialRelationsDO = getOne(lambdaQueryWrapper);
                if(ObjectUtil.isNotEmpty(socialRelationsDO.getRelationsAttch())){
                    childSaveReqVO.setRelationsAttch(wbCommonService.getFile(socialRelationsDO.getRelationsAttch()));
                }
            }
        }

        //判断该家属是否已经关联了该被监管人员
        if(CollectionUtil.isNotEmpty(familyIdCardList)){
            LambdaQueryWrapper<SocialRelationsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.select(SocialRelationsDO::getIdNumber);
            lambdaQueryWrapper.eq(SocialRelationsDO::getJgrybm,jgrybm).in(SocialRelationsDO::getIdNumber,familyIdCardList);
            List<SocialRelationsDO> list = list(lambdaQueryWrapper);
            if(CollectionUtil.isNotEmpty(list)){
                List<String> idNumberList = list.stream().map(SocialRelationsDO::getName).collect(Collectors.toList());
                throw new ServerException(String.format("该证件号(%s)已关联当前被监管人员，请勿重复添加",String.join("、",idNumberList)));
            }
        }

        return saveBatch(socialRelationsDOList);
    }

    @Override
    public SocialRelationsRespVO getSocialRelationsById(String id) {
        SocialRelationsDO socialRelationsDO  = getById(id);

        if(ObjectUtil.isEmpty(socialRelationsDO)){
            throw new ServerException("无法查询到该数据");
        }
        SocialRelationsRespVO socialRelationsRespVO = BeanUtils.toBean(socialRelationsDO,SocialRelationsRespVO.class);
        if(ObjectUtil.isNotEmpty(socialRelationsRespVO.getRelationsAttch())){
            socialRelationsRespVO.setRelationsAttch(wbCommonService.getFile(socialRelationsRespVO.getRelationsAttch()));
        }
        return socialRelationsRespVO;
    }
}
