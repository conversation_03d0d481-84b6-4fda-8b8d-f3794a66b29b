package com.rs.module.acp.entity.gj.conflict;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 实战平台-管教业务-社会矛盾化解调解参与单位 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_conflict_mediation_org")
@KeySequence("acp_gj_conflict_mediation_org_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConflictMediationOrgDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 调解ID
     */
    private String mediationId;
    /**
     * 事件ID
     */
    private String eventId;
    /**
     * 事件编号
     */
    private String eventCode;
    /**
     * 调解单位名称
     */
    private String mediationOrgName;
    /**
     * 参与人数
     */
    private Integer partiCnt;
    /**
     * 人员名单
     */
    private String personnelList;

}
