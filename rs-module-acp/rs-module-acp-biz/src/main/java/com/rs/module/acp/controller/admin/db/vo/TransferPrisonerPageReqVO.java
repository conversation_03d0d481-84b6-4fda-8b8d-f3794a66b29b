package com.rs.module.acp.controller.admin.db.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-羁押业务-转所人员分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TransferPrisonerPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("转所登记ID")
    private String transferRecordId;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
