package com.rs.module.acp.service.ds.sjbs;

import javax.validation.*;

import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyDataSubmitKssSaveReqVO;
import com.rs.module.acp.entity.ds.sjbs.WeeklyDataSubmitKssDO;
import com.bsp.common.orm.mybatis.service.IBaseService;

import java.util.Date;
import java.util.List;

/**
 * 实战平台-数据固化-每周数据报送(看守所) Service 接口
 *
 * <AUTHOR>
 */
public interface WeeklyDataSubmitKssService extends IBaseService<WeeklyDataSubmitKssDO>{

    /**
     * 创建实战平台-数据固化-每周数据报送(看守所)
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createWeeklyDataSubmitKss(@Valid WeeklyDataSubmitKssSaveReqVO createReqVO);

    /**
     * 更新实战平台-数据固化-每周数据报送(看守所)
     *
     * @param updateReqVO 更新信息
     */
    void updateWeeklyDataSubmitKss(@Valid WeeklyDataSubmitKssSaveReqVO updateReqVO);

    /**
     * 删除实战平台-数据固化-每周数据报送(看守所)
     *
     * @param id 编号
     */
    void deleteWeeklyDataSubmitKss(String id);

    /**
     * 获得实战平台-数据固化-每周数据报送(看守所)
     *
     * @param id 编号
     * @return 实战平台-数据固化-每周数据报送(看守所)
     */
    WeeklyDataSubmitKssDO getWeeklyDataSubmitKss(String id);

    void saveForStatistic(String orgCode, String startDate, String endDate);

    List<WeeklyDataSubmitKssDO> getWeeklyDataSubmitKssByDate(String startDate, String endDate, String orgCode);
}
