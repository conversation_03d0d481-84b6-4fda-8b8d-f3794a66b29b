package com.rs.module.acp.entity.ds.sjbs;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-数据固化-每周数据报送(看守所) DO
 *
 * <AUTHOR>
 */
@TableName("acp_ds_weekly_data_submit_kss")
@KeySequence("acp_ds_weekly_data_submit_kss_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_ds_weekly_data_submit_kss")
public class WeeklyDataSubmitKssDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 固化日期
     */
    private String solidificationDate;
    /**
     * 固化开始日期
     */
    private String startDate;
    /**
     * 固化结束日期
     */
    private String endDate;
    /**
     * 律师会见
     */
    private Integer lshj;
    /**
     * 律师会见-快速会见
     */
    private Integer lshjKshj;
    /**
     * 律师会见-现场会见
     */
    private Integer lshjXchj;
    /**
     * 律师会见-远程会见
     */
    private Integer lshjYchj;
    /**
     * 提讯
     */
    private Integer tx;
    /**
     * 提讯-公安机关
     */
    private Integer txGajg;
    /**
     * 提讯-检察院
     */
    private Integer txJcy;
    /**
     * 提讯-法院
     */
    private Integer txFy;
    /**
     * 提讯-其他单位
     */
    private Integer txQtdw;
    /**
     * 提解
     */
    private Integer tj;
    /**
     * 提解-公安机关
     */
    private Integer tjGajg;
    /**
     * 提解-检察院
     */
    private Integer tjJcy;
    /**
     * 提解-法院
     */
    private Integer tjFy;
    /**
     * 提解-其他单位
     */
    private Integer tjQtdw;

}
