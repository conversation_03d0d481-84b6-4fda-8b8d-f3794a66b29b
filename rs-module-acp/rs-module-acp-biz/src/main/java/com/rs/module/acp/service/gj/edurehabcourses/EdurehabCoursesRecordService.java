package com.rs.module.acp.service.gj.edurehabcourses;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesRecordListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesRecordPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesRecordSaveReqVO;
import com.rs.module.acp.entity.gj.EdurehabCoursesRecordDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-教育康复课程记录 Service 接口
 *
 * <AUTHOR>
 */
public interface EdurehabCoursesRecordService extends IBaseService<EdurehabCoursesRecordDO>{

    /**
     * 创建实战平台-管教业务-教育康复课程记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createEdurehabCoursesRecord(@Valid EdurehabCoursesRecordSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-教育康复课程记录
     *
     * @param updateReqVO 更新信息
     */
    void updateEdurehabCoursesRecord(@Valid EdurehabCoursesRecordSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-教育康复课程记录
     *
     * @param id 编号
     */
    void deleteEdurehabCoursesRecord(String id);

    /**
     * 获得实战平台-管教业务-教育康复课程记录
     *
     * @param id 编号
     * @return 实战平台-管教业务-教育康复课程记录
     */
    EdurehabCoursesRecordDO getEdurehabCoursesRecord(String id);

    /**
    * 获得实战平台-管教业务-教育康复课程记录分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-教育康复课程记录分页
    */
    PageResult<EdurehabCoursesRecordDO> getEdurehabCoursesRecordPage(EdurehabCoursesRecordPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-教育康复课程记录列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-教育康复课程记录列表
    */
    List<EdurehabCoursesRecordDO> getEdurehabCoursesRecordList(EdurehabCoursesRecordListReqVO listReqVO);


}
