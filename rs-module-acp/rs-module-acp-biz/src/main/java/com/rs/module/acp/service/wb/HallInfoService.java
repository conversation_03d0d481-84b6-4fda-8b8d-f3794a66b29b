package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.HallInfoDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-服务大厅信息发布 Service 接口
 *
 * <AUTHOR>
 */
public interface HallInfoService extends IBaseService<HallInfoDO>{

    /**
     * 创建实战平台-管教业务-服务大厅信息发布
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createHallInfo(@Valid HallInfoSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-服务大厅信息发布
     *
     * @param updateReqVO 更新信息
     */
    void updateHallInfo(@Valid HallInfoSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-服务大厅信息发布
     *
     * @param id 编号
     */
    void deleteHallInfo(String id);

    /**
     * 获得实战平台-管教业务-服务大厅信息发布
     *
     * @param id 编号
     * @return 实战平台-管教业务-服务大厅信息发布
     */
    HallInfoRespVO getHallInfo(String id);

    /**
    * 获得实战平台-管教业务-服务大厅信息发布分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-服务大厅信息发布分页
    */
    PageResult<HallInfoDO> getHallInfoPage(HallInfoPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-服务大厅信息发布列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-服务大厅信息发布列表
    */
    List<HallInfoDO> getHallInfoList(HallInfoListReqVO listReqVO);

    /**
     *  实战平台-窗口业务-更改服务大厅信息发布状态
     * @param id
     * @param status
     * @return
     */
    boolean changeStatus(String id,String status);

    /**
     * 实战平台-窗口业务-获取大屏展示内容
     * @return
     */
    HallInfoLargeScreenRespVO getHallInfoForLargeScreen();
}
