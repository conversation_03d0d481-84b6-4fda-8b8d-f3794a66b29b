package com.rs.module.acp.dao.pi;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventSettingListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventSettingPageReqVO;
import com.rs.module.acp.entity.pi.PrisonEventSettingDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-巡视管控-所警情管理-报警联动设置(所情来源) Dao
*
* <AUTHOR>
*/
@Mapper
public interface PrisonEventSettingDao extends IBaseDao<PrisonEventSettingDO> {


    default PageResult<PrisonEventSettingDO> selectPage(PrisonEventSettingPageReqVO reqVO) {
        Page<PrisonEventSettingDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrisonEventSettingDO> wrapper = new LambdaQueryWrapperX<PrisonEventSettingDO>()
            .eqIfPresent(PrisonEventSettingDO::getEventSrc, reqVO.getEventSrc())
            .likeIfPresent(PrisonEventSettingDO::getName, reqVO.getName())
            .eqIfPresent(PrisonEventSettingDO::getEventLevel, reqVO.getEventLevel())
            .eqIfPresent(PrisonEventSettingDO::getEnabled, reqVO.getEnabled())
            .eqIfPresent(PrisonEventSettingDO::getProcessingDuration, reqVO.getProcessingDuration())
            .eqIfPresent(PrisonEventSettingDO::getTone, reqVO.getTone())
            .eqIfPresent(PrisonEventSettingDO::getOptionalSettings, reqVO.getOptionalSettings())
            .eqIfPresent(PrisonEventSettingDO::getSettings, reqVO.getSettings())
            .eqIfPresent(PrisonEventSettingDO::getExtendOne, reqVO.getExtendOne())
            .eqIfPresent(PrisonEventSettingDO::getTypeId, reqVO.getTypeId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PrisonEventSettingDO::getAddTime);
        }
        Page<PrisonEventSettingDO> prisonEventSettingPage = selectPage(page, wrapper);
        return new PageResult<>(prisonEventSettingPage.getRecords(), prisonEventSettingPage.getTotal());
    }
    default List<PrisonEventSettingDO> selectList(PrisonEventSettingListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrisonEventSettingDO>()
            .eqIfPresent(PrisonEventSettingDO::getEventSrc, reqVO.getEventSrc())
            .likeIfPresent(PrisonEventSettingDO::getName, reqVO.getName())
            .eqIfPresent(PrisonEventSettingDO::getEventLevel, reqVO.getEventLevel())
            .eqIfPresent(PrisonEventSettingDO::getEnabled, reqVO.getEnabled())
            .eqIfPresent(PrisonEventSettingDO::getProcessingDuration, reqVO.getProcessingDuration())
            .eqIfPresent(PrisonEventSettingDO::getTone, reqVO.getTone())
            .eqIfPresent(PrisonEventSettingDO::getOptionalSettings, reqVO.getOptionalSettings())
            .eqIfPresent(PrisonEventSettingDO::getSettings, reqVO.getSettings())
            .eqIfPresent(PrisonEventSettingDO::getExtendOne, reqVO.getExtendOne())
            .eqIfPresent(PrisonEventSettingDO::getTypeId, reqVO.getTypeId())
        .orderByDesc(PrisonEventSettingDO::getAddTime));    }


    }
