package com.rs.module.acp.entity.pm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 实战平台-监管管理-门禁点管理 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_device_door")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_device_door")
public class DeviceDoorDO extends BaseDO {
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 资源唯一编码
     */
    private String indexCode;
    /**
     * 资源类型
     */
    private String resourceType;
    /**
     * 资源名称
     */
    private String name;
    /**
     * 门禁点编号
     */
    private String doorNo;
    /**
     * 通道口
     */
    private String channelNo;
    /**
     * 门序号
     */
    private String doorSerial;
    /**
     * 描述
     */
    private String description;
    /**
     * 通道类型
     */
    private String channelType;
    /**
     * 原区域名称
     */
    private String originRegionName;
    /**
     * 原区域路径
     */
    private String originRegionPathName;
    /**
     * 监区ID
     */
    private String roomId;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 门禁点状态
     */
    private String doorStatus;
    /**
     * 绑定监控室状态(0-未绑定，1-已绑定)
     */
    private String bindRoomStatus;

}
