package com.rs.module.acp.service.gj.performance;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.fhs.trans.service.impl.TransService;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.acp.controller.admin.gj.vo.performancekss.PerformanceKssApprovalReqVO;
import com.rs.module.acp.controller.admin.gj.vo.performancekss.PerformanceKssListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.performancekss.PerformanceKssPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.performancekss.PerformanceKssSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.printdocument.PrintDocumentSaveReqVO;
import com.rs.module.acp.enums.gj.PerformanceStatusEnum;
import com.rs.module.acp.enums.gj.PrintDocumentTypeEnum;
import com.rs.module.acp.service.gj.printdocument.PrintDocumentService;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.enums.PrisonerQueryRyztEnum;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.util.BspApprovalUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.PerformanceKssDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.PerformanceKssDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-人员表现鉴定表-看守所 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PerformanceKssServiceImpl extends BaseServiceImpl<PerformanceKssDao, PerformanceKssDO> implements PerformanceKssService {

    @Resource
    private PerformanceKssDao performanceKssDao;

    @Resource
    private PrintDocumentService printDocumentService;

    @Resource
    private PrisonerService prisonerService;

    @Autowired
    private TransService transService;

    // 单位领导审批流程
    private static final String defKey = "yonghudanweilindao";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createPerformanceKss(PerformanceKssSaveReqVO createReqVO) {
        // 插入
        PerformanceKssDO performanceKss = BeanUtils.toBean(createReqVO, PerformanceKssDO.class);
        performanceKss.setStatus(PerformanceStatusEnum.DSH.getCode());
        performanceKssDao.insert(performanceKss);
        PrisonerVwRespVO prisonerInVwRespVO = prisonerService.getPrisonerSelectCompomenOne(createReqVO.getJgrybm(), PrisonerQueryRyztEnum.ZS);
        transService.transOne(prisonerInVwRespVO);
        PrintDocumentSaveReqVO printDocumentSaveReqVO = new PrintDocumentSaveReqVO();
        printDocumentSaveReqVO.setGlId(performanceKss.getId());
        printDocumentSaveReqVO.setGlType(PrintDocumentTypeEnum.kssyrbxjd.getCode());
        printDocumentSaveReqVO.setWsdata(JSON.toJSONStringWithDateFormat(prisonerInVwRespVO, "yyyy-MM-dd HH:mm:ss"));
        printDocumentService.createPrintDocument(printDocumentSaveReqVO);

        //启动流程审批
        String msgUrl = "/#/discipline/behaviorIdentification-kss/check?id="+performanceKss.getId()+"&jgrybm="+createReqVO.getJgrybm();
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", performanceKss.getId());
        JSONObject result = BspApprovalUtil.defaultStartProcess(HttpUtils.getAppCode() + "-" + defKey, performanceKss.getId(), "人员表现鉴定流程审批", msgUrl, variables);
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            performanceKss.setActInstId(bpmTrail.getString("actInstId"));
            performanceKss.setTaskId(bpmTrail.getString("taskId"));
            performanceKssDao.updateById(performanceKss);
        } else {
            throw new ServerException("流程启动失败");
        }
        // 返回
        return performanceKss.getId();
    }

    @Override
    public void updatePerformanceKss(PerformanceKssSaveReqVO updateReqVO) {
        // 校验存在
        validatePerformanceKssExists(updateReqVO.getId());
        // 更新
        PerformanceKssDO updateObj = BeanUtils.toBean(updateReqVO, PerformanceKssDO.class);
        performanceKssDao.updateById(updateObj);
    }

    @Override
    public void deletePerformanceKss(String id) {
        // 校验存在
        validatePerformanceKssExists(id);
        // 删除
        performanceKssDao.deleteById(id);
    }

    private void validatePerformanceKssExists(String id) {
        if (performanceKssDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-人员表现鉴定表-看守所数据不存在");
        }
    }

    @Override
    public PerformanceKssDO getPerformanceKss(String id) {
        return performanceKssDao.selectById(id);
    }

    @Override
    public PageResult<PerformanceKssDO> getPerformanceKssPage(PerformanceKssPageReqVO pageReqVO) {
        return performanceKssDao.selectPage(pageReqVO);
    }

    @Override
    public List<PerformanceKssDO> getPerformanceKssList(PerformanceKssListReqVO listReqVO) {
        return performanceKssDao.selectList(listReqVO);
    }

    @Override
    public void approval(PerformanceKssApprovalReqVO approvalReqVO) {
        PerformanceKssDO performanceKssDO = performanceKssDao.selectById(approvalReqVO.getId());
        if (Objects.isNull(performanceKssDO)) {
            throw new ServerException("实战平台-管教业务-人员表现鉴定表-看守所数据不存在");
        }
        if (!PerformanceStatusEnum.DSH.getCode().equals(performanceKssDO.getStatus())) {
            throw new ServerException("非待审批状态，不能进行审核操作");
        }
        performanceKssDO.setStatus(PerformanceStatusEnum.getByCode(approvalReqVO.getStatus()).getCode());
        performanceKssDO.setApproverTime(new Date());
        performanceKssDO.setApprovalComments(approvalReqVO.getApprovalComments());
        performanceKssDO.setApprovalResult(approvalReqVO.getStatus());
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        performanceKssDO.setApproverSfzh(sessionUser.getIdCard());
        performanceKssDO.setApproverXm(sessionUser.getName());
        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(performanceKssDO.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }

        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", performanceKssDO.getId());
        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getId());
        nowApproveUser.put("name", sessionUser.getName());
        BspApproceStatusEnum bspApproceStatusEnum = PerformanceStatusEnum.SHTG.getCode().equals(performanceKssDO.getStatus()) ?
                BspApproceStatusEnum.PASSED_END : BspApproceStatusEnum.NOT_PASSED_END;
        JSONObject result = BspApprovalUtil.approvalProcess(HttpUtils.getAppCode() + "-" + defKey,
                performanceKssDO.getActInstId(), performanceKssDO.getTaskId(), performanceKssDO.getId(),
                bspApproceStatusEnum, performanceKssDO.getApprovalComments(), null, null, true,
                variables, nowApproveUser, "acp");
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            performanceKssDO.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程启动失败");
        }
        performanceKssDao.updateById(performanceKssDO);
    }


}
