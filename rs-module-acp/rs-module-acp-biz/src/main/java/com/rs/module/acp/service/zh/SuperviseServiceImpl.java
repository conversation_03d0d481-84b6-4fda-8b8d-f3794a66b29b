package com.rs.module.acp.service.zh;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.cons.SuperviseStatusConstants;
import com.rs.module.acp.controller.admin.zh.vo.SuperviseSaveReqVO;
import com.rs.module.acp.dao.zh.SuperviseDao;
import com.rs.module.acp.entity.zh.SuperviseDO;
import com.rs.module.base.util.BspApprovalUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 综合管理-督导信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SuperviseServiceImpl extends BaseServiceImpl<SuperviseDao, SuperviseDO> implements SuperviseService {

    @Resource
    private SuperviseDao superviseDao;

    private String defKey = "dudaoshenpi";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createSupervise(SuperviseSaveReqVO createReqVO) throws Exception {
        // 插入
        SuperviseDO supervise = BeanUtils.toBean(createReqVO, SuperviseDO.class);
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        supervise.setStatus(SuperviseStatusConstants.SUPERVISE_STATUS_APPROVAL);
        supervise.setId(StringUtil.getGuid());
        Map<String, Object> variables = new HashMap<>();
        String msgTit = String.format("%s%s发起督导审批，请及时处理！", sessionUser.getOrgName(), sessionUser.getName());
        String msgUrl = "/#/comprehensive/prisonSupervisionLayout/prisonSupervisionHome?id=" + supervise.getId();
        //启动流程
        Map<String,String> processResult = BspApprovalUtil.commonStartProcessMap(defKey, supervise.getId(), msgTit,
                msgUrl, variables, HttpUtils.getAppCode());
        if(StringUtil.isEmpty(processResult.get("actInstId")) || StringUtil.isEmpty(processResult.get("taskId"))){
            throw new Exception("启动流程失败");
        }
        System.out.println("==================================");
        System.out.println(processResult);
        List<ReceiveUser> candidateUsers = JSONObject.parseArray(processResult.get("candidateUsers"), ReceiveUser.class);
        supervise.setApprovalUserSfzh(candidateUsers.stream().map(user -> user.getIdCard()).collect(Collectors.joining(",")));
        supervise.setApprovalUserName(candidateUsers.stream().map(user -> user.getName()).collect(Collectors.joining(",")));
        supervise.setActInstId(processResult.get("actInstId"));
        supervise.setTaskId(processResult.get("taskId"));
        superviseDao.insert(supervise);
        // 返回
        return supervise.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean approve(JSONObject approveReqVO) throws Exception {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        //从当前登录用户信息设置审批信息默认值
        SuperviseDO superviseDO = superviseDao.selectById(approveReqVO.getString("id"));
        BspApproceStatusEnum isApprove = BspApproceStatusEnum.PASSED;
        String msgTit = "";
        String msgUrl = "";
        if(SuperviseStatusConstants.SUPERVISE_STATUS_NOT_PASSED.equals(approveReqVO.getString("status"))) {
            isApprove = BspApproceStatusEnum.NOT_PASSED;
        } else if (SuperviseStatusConstants.SUPERVISE_STATUS_REJECT.equals(approveReqVO.getString("status"))) {
            isApprove = BspApproceStatusEnum.REJECT;
            msgTit = String.format("您发起的督导审批被退回了！");
        }
        superviseDO.setStatus(approveReqVO.getString("status"));
        //调用流程审批接口
        Map<String, Object> variables = new HashMap<>();
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getId());
        nowApproveUser.put("name", sessionUser.getName());
        boolean terminateTask = BspApproceStatusEnum.PASSED_END.getCode() == isApprove.getCode();
        JSONObject approvalResult = BspApprovalUtil.approvalProcess(defKey,
                superviseDO.getActInstId(), superviseDO.getTaskId(), approveReqVO.getString("id"),
                isApprove, approveReqVO.getString("comments"), msgTit, msgUrl,
                terminateTask, variables, nowApproveUser, HttpUtils.getAppCode());
        System.out.println("审批结果：" + approvalResult);
        if(!approvalResult.getBoolean("success")){
            throw new Exception("流程审批失败");
        }
        String taskId = approvalResult.getString("taskId"); // 更新任务ID
        List<ReceiveUser> candidateUsers = JSONObject.parseArray(approvalResult.getString("candidateUsers"), ReceiveUser.class);
        superviseDO.setApprovalUserSfzh(candidateUsers.stream().map(user -> user.getIdCard()).collect(Collectors.joining(",")));
        superviseDO.setApprovalUserName(candidateUsers.stream().map(user -> user.getName()).collect(Collectors.joining(",")));
        superviseDO.setTaskId(taskId);
        superviseDao.updateById(superviseDO);
        return true;
    }

//    @Override
//    public boolean affirm(JSONObject reqVO) throws Exception {
//        SuperviseDO superviseDO = superviseDao.selectById(reqVO.getString("id"));
//        if("04".equals(reqVO.getString("status"))){
//            superviseDO.setStatus(SuperviseStatusConstants.SUPERVISE_STATUS_FINISH);
//        } else {
//            superviseDO.setStatus(SuperviseStatusConstants.SUPERVISE_STATUS_PENDING);
//        }
//        superviseDao.updateById(superviseDO);
//        return false;
//    }

    @Override
    public void updateSupervise(SuperviseSaveReqVO updateReqVO) {
        // 校验存在
        validateSuperviseExists(updateReqVO.getId());
        // 更新
        SuperviseDO updateObj = BeanUtils.toBean(updateReqVO, SuperviseDO.class);
        superviseDao.updateById(updateObj);
    }

    @Override
    public void deleteSupervise(String id) {
        // 校验存在
        validateSuperviseExists(id);
        // 删除
        superviseDao.deleteById(id);
    }

    private void validateSuperviseExists(String id) {
        if (superviseDao.selectById(id) == null) {
            throw new ServerException("综合管理-督导信息数据不存在");
        }
    }

    @Override
    public SuperviseDO getSupervise(String id) {
        return superviseDao.selectById(id);
    }


}
