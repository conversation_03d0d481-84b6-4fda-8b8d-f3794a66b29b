package com.rs.module.acp.controller.admin.db.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "看守所收押登记收押信息VO")
@Data
public class DetainRegKssDetainVO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("重刑犯")
    @Trans(type = TransType.DICTIONARY,key = "ZD_TYSFDM")
    private String zxf;

    @ApiModelProperty("档案编号")
    private String dabh;

    @ApiModelProperty("监区id")
    private String areaId;

    @ApiModelProperty("监区名称")
    private String areaName;

    @ApiModelProperty("指纹编号")
    private String zwbh;

    @ApiModelProperty("附物编号")
    private String fwbh;

    @ApiModelProperty("收押民警姓名")
    private String symjxm;

    @ApiModelProperty("收分配标识服")
    private String sfpbsf;

    @ApiModelProperty("标识服颜色")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SBFBS")
    private String suitColor;

    @ApiModelProperty("看守所联系电话")
    private String ksslxdh;

    @ApiModelProperty("是否涉密人员")
    private String sfsm;

    @ApiModelProperty("人员代号")
    private String rydh;

    @ApiModelProperty("涉密原因")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYDJSMYY")
    private String smyy;

    @ApiModelProperty("涉密备注")
    private String smbz;

    @ApiModelProperty("救济日期")
    private Date jjrq;

    @ApiModelProperty("救济原因")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYDJJJYY")
    private String jjyy;

    @ApiModelProperty("救济领取物品")
    private String jjlqwp;
}
