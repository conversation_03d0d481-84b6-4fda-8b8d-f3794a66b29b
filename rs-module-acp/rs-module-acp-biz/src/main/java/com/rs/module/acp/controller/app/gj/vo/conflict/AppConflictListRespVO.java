package com.rs.module.acp.controller.app.gj.vo.conflict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @Date 2025/3/26 09:51
 */
@ApiModel(description = "管理后台-实战平台内屏-管教业务-社会矛盾list 响应 VO")
@Data
public class AppConflictListRespVO {

    @ApiModelProperty("主键ID")
    private String prisonerId;
    @ApiModelProperty("事件编号")
    private String eventCode;
    @ApiModelProperty("涉案金额")
    private BigDecimal involvedAmt;
    @ApiModelProperty("矛盾类别（01：非法上访、02：经济纠纷（补偿赔偿债务）、03：家庭矛盾、04：青少年违法、05：民间打架斗殴、06：民事纠纷、07：非法维权、08：对执法机关不满案件、09：迷信活动、10：“六失一偏”人员、11：其他类")
    private String conflictType;
    @ApiModelProperty("调解时间")
    private Date mediationTime;
    @ApiModelProperty("调解民警身份证号")
    private String mediationPoliceSfzh;
    @ApiModelProperty("调解民警姓名")
    private String mediationPoliceXm;
    @ApiModelProperty("登记状态")
    private String regStatus;
    @ApiModelProperty("确认状态（0：待确认、1：已确认）")
    private Integer confirmStatus;
    @ApiModelProperty("是否已评价（0：未评价，1：已评价）")
    private Integer isRated;
    @ApiModelProperty("满意度评分（1：满意：0：不满意）")
    private Integer satisfactionScore;
    @ApiModelProperty("调解结果（1：调解成功，2：调解暂缓，3：不再调解）")
    private String mediationResult;

    
}
