package com.rs.module.acp.entity.wb;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-窗口业务-物品顾送明细 DO
 *
 * <AUTHOR>
 */
@TableName("acp_wb_goods_delivery_details")
@KeySequence("acp_wb_goods_delivery_details_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_wb_goods_delivery_details")
public class GoodsDeliveryDetailsDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 物品顾送ID
     */
    private String goodsDeliveryId;
    /**
     * 物品名称
     */
    private String goodsName;
    /**
     * 物品数量
     */
    private Integer goodsQuantity;
    /**
     * 单位
     */
    private String unit;
    /**
     * 物品备注
     */
    private String remark;

}
