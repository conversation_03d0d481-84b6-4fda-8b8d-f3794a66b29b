package com.rs.module.acp.controller.admin.gj.vo.equipment;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-械具使用解除呈批 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EquipmentUseRespWearVO extends BaseVO implements TransPojo{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("械具使用ID")
    private String equipmentUseId;

    @ApiModelProperty("使用械具的理由")
    private String removeReason;

    @ApiModelProperty("解除日期")
    private Date removeTime;

    @ApiModelProperty("解除执行情况")
    private String removeExecuteSituation;

    @ApiModelProperty("是否提前解除")
    private Short isRemove;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审批人签名")
    private String approvalAutograph;

    @ApiModelProperty("审批人签名日期")
    private Date approvalAutographTime;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("监室号Id")
    private String roomId;

    @ApiModelProperty("监室号名称")
    private String roomName;

    @ApiModelProperty("机构代码")
    private String orgCode;

    @ApiModelProperty("机构代码")
    private String orgName;

    @ApiModelProperty("使用呈批人")
    private String addUser;

    @ApiModelProperty("使用呈批人姓名")
    private String addUserName;

    @ApiModelProperty("使用呈批时间")
    private Date addTime;

    @ApiModelProperty("分控室ID")
    private String divisionControlAreaId;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
}
