package com.rs.module.acp.service.db.components;

/**
 * 入所人员信息推送处理器接口
 * 各个模块需实现此接口以完成定制化的推送逻辑
 */
public interface PrisonerInInfoSyncHandler {
    /**
     * 同步入所人员信息到当前模块
     *
     * @param rybh 人员编号
     * @return 是否成功推送
     */
    boolean syncPrisonerInInfo(String rybh);

    void syncDataToTransitionRoom(String rybh, String orgCode,String id);

    void syncDataToCasePersonnel(String rybh, String orgCode, String id);

    void syncToPtmGoods(String rybh, String orgCode, String id);

    void syncSocialRelations(String rybh, String orgCode, String id);
}
