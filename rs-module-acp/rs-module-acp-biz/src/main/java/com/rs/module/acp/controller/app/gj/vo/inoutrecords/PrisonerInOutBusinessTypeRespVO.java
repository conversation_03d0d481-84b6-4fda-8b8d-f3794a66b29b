package com.rs.module.acp.controller.app.gj.vo.inoutrecords;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 所有在所人员出入事由列表 Response VO")
@Data
public class PrisonerInOutBusinessTypeRespVO implements TransPojo {

    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("入所时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date rssj;
    @ApiModelProperty("正面照片")
    private String frontPhoto;
    @ApiModelProperty("风险等级")
    private String fxdj;
    @ApiModelProperty("人员状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_RYZT")
    private String ryzt;
    @ApiModelProperty("监室号")
    private String jsh;
    @ApiModelProperty("姓名")
    private String xm;
    @ApiModelProperty("出入记录")
    private List<InOutRecordsRespVO> inOutRecordsRespVOList;

    @ApiModelProperty("出入事由多个逗号分隔拼接")
    private String businessTypeNames;


}
