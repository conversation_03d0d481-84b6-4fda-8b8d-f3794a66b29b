package com.rs.module.acp.controller.admin.db.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-收押业务-伤情鉴定新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class InjuryAssessmentSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("人员编号")
    @NotEmpty(message = "人员编号不能为空")
    private String rybh;

    @ApiModelProperty("序号")
    private String serialNumber;

    @ApiModelProperty("照片")
    private String zp;

    @ApiModelProperty("描述")
    private String ms;

    @ApiModelProperty("坐标")
    private String zb;

}
