package com.rs.module.acp.service.zh.indicatorcate;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateAssessedListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateAssessedPageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateAssessedSaveReqVO;
import com.rs.module.acp.entity.zh.IndicatorCateAssessedDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 综合管理-绩效考核指标分类与被考评人关联 Service 接口
 *
 * <AUTHOR>
 */
public interface IndicatorCateAssessedService extends IBaseService<IndicatorCateAssessedDO>{

    /**
     * 创建综合管理-绩效考核指标分类与被考评人关联
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createIndicatorCateAssessed(@Valid IndicatorCateAssessedSaveReqVO createReqVO);

    /**
     * 更新综合管理-绩效考核指标分类与被考评人关联
     *
     * @param updateReqVO 更新信息
     */
    void updateIndicatorCateAssessed(@Valid IndicatorCateAssessedSaveReqVO updateReqVO);

    /**
     * 删除综合管理-绩效考核指标分类与被考评人关联
     *
     * @param id 编号
     */
    void deleteIndicatorCateAssessed(String id);

    /**
     * 获得综合管理-绩效考核指标分类与被考评人关联
     *
     * @param id 编号
     * @return 综合管理-绩效考核指标分类与被考评人关联
     */
    IndicatorCateAssessedDO getIndicatorCateAssessed(String id);

    /**
    * 获得综合管理-绩效考核指标分类与被考评人关联分页
    *
    * @param pageReqVO 分页查询
    * @return 综合管理-绩效考核指标分类与被考评人关联分页
    */
    PageResult<IndicatorCateAssessedDO> getIndicatorCateAssessedPage(IndicatorCateAssessedPageReqVO pageReqVO);

    /**
    * 获得综合管理-绩效考核指标分类与被考评人关联列表
    *
    * @param listReqVO 查询条件
    * @return 综合管理-绩效考核指标分类与被考评人关联列表
    */
    List<IndicatorCateAssessedDO> getIndicatorCateAssessedList(IndicatorCateAssessedListReqVO listReqVO);


}
