package com.rs.module.acp.entity.gj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 实战平台-管教业务-处罚呈批 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_punishment")
@KeySequence("acp_gj_punishment_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_punishment")
public class PunishmentDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 数据来源（字典：ZD_DATA_SOURCES）
     */
    private String dataSources;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 处罚原因
     */
    private String reason;
    /**
     * 呈批处罚周期开始日期
     */
    private Date startDate;
    /**
     * 呈批处罚周期结束日期
     */
    private Date endDate;
    /**
     * 实际开始日期
     */
    private Date actualStartDate;
    /**
     * 实际结束日期
     */
    private Date actualEndDate;
    /**
     * 处罚时长，自动生成（天-时-分钟）
     */
    private Integer duration;
    /**
     * actual_duration
     */
    private BigDecimal actualDuration;
    /**
     * 处罚措施逗号分割
     */
    private String measures;
    /**
     * 具体原因
     */
    private String specificReason;
    /**
     * 备注
     */
    private String remark;
    /**
     * 执行人身份证号
     */
    private String executorSfzh;
    /**
     * 执行人身份证号
     */
    private String executor;
    /**
     * 执行登记时间
     */
    private Date executorRegTime;
    /**
     * 执行情况
     */
    private String executeSituation;
    /**
     * 是否提前解除
     */
    private Short isInAdvanceRemove;
    /**
     * 状态
     */
    private String status;
    /**
     * 审批人身份证号
     */
    private String approverSfzh;
    /**
     * 审批人姓名
     */
    private String approverXm;
    /**
     * 审批时间
     */
    private Date approverTime;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 审批人签名
     */
    private String approvalAutograph;
    /**
     * 审批人签名日期
     */
    private Date approvalAutographTime;
    /**
     * 审核意见
     */
    private String approvalComments;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

    //是否严管  1 是 0 否
    private Integer  sfyg;

    //是否自动生成 1是 0否
    private Integer isAutoGenerate;

    //处罚原因名称
    private String reasonName;

    //强制措施
    private String measuresName;

}
