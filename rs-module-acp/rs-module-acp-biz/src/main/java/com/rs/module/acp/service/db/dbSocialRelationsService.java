package com.rs.module.acp.service.db;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.dbSocialRelationsDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-收押业务-社会关系 Service 接口
 *
 * <AUTHOR>
 */
public interface dbSocialRelationsService extends IBaseService<dbSocialRelationsDO>{

    /**
     * 创建实战平台-收押业务-社会关系
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createdbSocialRelations(@Valid dbSocialRelationsSaveReqVO createReqVO);

    /**
     * 更新实战平台-收押业务-社会关系
     *
     * @param updateReqVO 更新信息
     */
    void updatedbSocialRelations(@Valid dbSocialRelationsSaveReqVO updateReqVO);

    /**
     * 删除实战平台-收押业务-社会关系
     *
     * @param id 编号
     */
    void deletedbSocialRelations(String id);

    /**
     * 获得实战平台-收押业务-社会关系
     *
     * @param id 编号
     * @return 实战平台-收押业务-社会关系
     */
    dbSocialRelationsDO getdbSocialRelations(String id);

    /**
    * 获得实战平台-收押业务-社会关系分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-收押业务-社会关系分页
    */
    PageResult<dbSocialRelationsDO> getdbSocialRelationsPage(dbSocialRelationsPageReqVO pageReqVO);

    /**
    * 获得实战平台-收押业务-社会关系列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-收押业务-社会关系列表
    */
    List<dbSocialRelationsDO> getdbSocialRelationsList(dbSocialRelationsListReqVO listReqVO);


}
