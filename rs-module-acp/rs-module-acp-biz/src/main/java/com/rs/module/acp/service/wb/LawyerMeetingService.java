package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.LawyerMeetingDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-律师会见登记 Service 接口
 *
 * <AUTHOR>
 */
public interface LawyerMeetingService extends IBaseService<LawyerMeetingDO>{

    /**
     * 创建实战平台-窗口业务-律师会见登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createLawyerMeeting(@Valid LawyerMeetingSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-律师会见登记
     *
     * @param updateReqVO 更新信息
     */
    void updateLawyerMeeting(@Valid LawyerMeetingSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-律师会见登记
     *
     * @param id 编号
     */
    void deleteLawyerMeeting(String id);

    /**
     * 获得实战平台-窗口业务-律师会见登记
     *
     * @param id 编号
     * @return 实战平台-窗口业务-律师会见登记
     */
    LawyerMeetingDO getLawyerMeeting(String id);

    /**
    * 获得实战平台-窗口业务-律师会见登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-律师会见登记分页
    */
    PageResult<LawyerMeetingDO> getLawyerMeetingPage(LawyerMeetingPageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-律师会见登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-律师会见登记列表
    */
    List<LawyerMeetingDO> getLawyerMeetingList(LawyerMeetingListReqVO listReqVO);

    /**
     * 获得实战平台-窗口业务-获取律师会见配置
     *
     * @return 实战平台-窗口业务-律师会见配置
     */

    JSONObject getMeetingConfig( String applData);

    /**
     * 获得实战平台-窗口业务-律师签到
     *
     * @param id 业务ID
     * @param checkInTime 签到时间
     */
    boolean signIn(String id,String checkInTime);

    /**
     * 获得实战平台-窗口业务-分配律师会见室
     *
     * @param id 编号
     * @param roomId 会见室ID
     * @return 实战平台-窗口业务-分配律师会见室
     */
    boolean allocationRoom(String id,String roomId);

    /**
     * 获得实战平台-窗口业务-带出安检
     *
     * @param updateReqVO
     */
    boolean escortingInspect(LawyerMeetingSaveReqVO updateReqVO);

    /**
     * 获得实战平台-窗口业务-会毕安检
     *
     * @param updateReqVO
     */
    boolean returnInspection(LawyerMeetingSaveReqVO updateReqVO);

    /**
     * 实战平台-窗口业务-创建律师会见登记前置接口1-同案判断
     * @param jgrybm 监管人员编码
     * @param lawyerIds 律师ID，多个律师时，使用英文逗号隔开
     * @param lawyerIds 案件编号
     * @return
     */
    JSONObject sameCaseJudgment(String jgrybm,String lawyerIds);

    /**
     *  实战平台-窗口业务-创建律师会见登记前置接口2-时间段重叠判断
     * @param jgrybm 监管人员编码
     * @param meetingMethod 会见方式
     * @param appointmentTime 预约会见日期（选择现场会见传入这个）
     * @param appointmentTimeSlot 预约会见时间段（选择现场会见传入这个）
     * @param applyMeetingStartTime 预约会见开始时间（选择快速会见、远程会见传入这个）
     * @param applyMeetingEndTime 预约会见结束时间（选择快速会见、远程会见传入这个）
     * @return
     */
    JSONObject timeOverlapJudgment(String jgrybm,String meetingMethod,String appointmentTime,String appointmentTimeSlot,String applyMeetingStartTime,String applyMeetingEndTime);


    /**
     * 获得实战平台-窗口业务-根据监管人员编码获取监管人员历史会见记录成功
     *
     * @param jgrybm 编号
     * @param pageNo 页码
     * @param pageSize 每页多少
     * @return 实战平台-窗口业务-获取历史律师会见记录
     */
    PageResult<LawyerMeetingRespVO> getHistoryMeetingByJgrybm(String jgrybm,int pageNo,int pageSize);

    /**
     * 获得实战平台-窗口业务-根据律师ID获取律师历史会见记录
     *
     * @param lawyerId 律师ID
     * @param pageNo 页码
     * @param pageSize 每页多少
     * @return 实战平台-窗口业务-获取历史律师会见记录
     */
    PageResult<LawyerMeetingRespVO> getHistoryMeetingByLwayerId(String lawyerId, int pageNo, int pageSize);
    /**
     * 实战平台-窗口业务-补录
     *
     * @param updateReqVO 更新信息
     * @return
     */
    boolean additionalRecording(LawyerMeetingSaveReqVO updateReqVO);

    /**
     * 实战平台-窗口业务-律师会见排号信息-现场会见
     * @return
     */
    List<JSONObject> getOnSiteNumbering();
    /**
     * 实战平台-窗口业务-律师会见排号信息-远程会见
     * @return
     */
    List<JSONObject> getremoteNumbering();
    /**
     * 实战平台-窗口业务-根据ID获得律师会见登记
     * @return
     */
    LawyerMeetingRespVO getLawyerMeetingById(String id);
}
