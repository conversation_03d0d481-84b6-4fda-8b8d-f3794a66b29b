package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne.CivilizedPersonneDetailListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne.CivilizedPersonneDetailPageReqVO;
import com.rs.module.acp.entity.gj.CivilizedPersonneDetailDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-文明个人登记明细 Dao
*
* <AUTHOR>
*/
@Mapper
public interface CivilizedPersonneDetailDao extends IBaseDao<CivilizedPersonneDetailDO> {


    default PageResult<CivilizedPersonneDetailDO> selectPage(CivilizedPersonneDetailPageReqVO reqVO) {
        Page<CivilizedPersonneDetailDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<CivilizedPersonneDetailDO> wrapper = new LambdaQueryWrapperX<CivilizedPersonneDetailDO>()
            .eqIfPresent(CivilizedPersonneDetailDO::getCivilizedPersonneId, reqVO.getCivilizedPersonneId())
            .eqIfPresent(CivilizedPersonneDetailDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(CivilizedPersonneDetailDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(CivilizedPersonneDetailDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(CivilizedPersonneDetailDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(CivilizedPersonneDetailDO::getNumberOfReward, reqVO.getNumberOfReward())
            .eqIfPresent(CivilizedPersonneDetailDO::getNumberOfViolations, reqVO.getNumberOfViolations())
            .eqIfPresent(CivilizedPersonneDetailDO::getNumberOfPunishment, reqVO.getNumberOfPunishment())
            .eqIfPresent(CivilizedPersonneDetailDO::getSelectionReason, reqVO.getSelectionReason())
            .eqIfPresent(CivilizedPersonneDetailDO::getAttUrl, reqVO.getAttUrl())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(CivilizedPersonneDetailDO::getAddTime);
        }
        Page<CivilizedPersonneDetailDO> civilizedPersonneDetailPage = selectPage(page, wrapper);
        return new PageResult<>(civilizedPersonneDetailPage.getRecords(), civilizedPersonneDetailPage.getTotal());
    }
    default List<CivilizedPersonneDetailDO> selectList(CivilizedPersonneDetailListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CivilizedPersonneDetailDO>()
            .eqIfPresent(CivilizedPersonneDetailDO::getCivilizedPersonneId, reqVO.getCivilizedPersonneId())
            .eqIfPresent(CivilizedPersonneDetailDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(CivilizedPersonneDetailDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(CivilizedPersonneDetailDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(CivilizedPersonneDetailDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(CivilizedPersonneDetailDO::getNumberOfReward, reqVO.getNumberOfReward())
            .eqIfPresent(CivilizedPersonneDetailDO::getNumberOfViolations, reqVO.getNumberOfViolations())
            .eqIfPresent(CivilizedPersonneDetailDO::getNumberOfPunishment, reqVO.getNumberOfPunishment())
            .eqIfPresent(CivilizedPersonneDetailDO::getSelectionReason, reqVO.getSelectionReason())
            .eqIfPresent(CivilizedPersonneDetailDO::getAttUrl, reqVO.getAttUrl())
        .orderByDesc(CivilizedPersonneDetailDO::getAddTime));    }


    }
