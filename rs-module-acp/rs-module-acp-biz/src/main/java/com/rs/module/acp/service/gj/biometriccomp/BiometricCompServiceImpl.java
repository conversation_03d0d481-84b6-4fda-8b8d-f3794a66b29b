package com.rs.module.acp.service.gj.biometriccomp;

import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.gj.vo.biometriccomp.*;
import com.rs.module.acp.enums.gj.BiometricCompStatusEnum;
import com.rs.module.acp.enums.gj.BiometricCompTypeEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.BiometricCompDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.BiometricCompDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-生物特征比对 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BiometricCompServiceImpl extends BaseServiceImpl<BiometricCompDao, BiometricCompDO> implements BiometricCompService {

    @Resource
    private BiometricCompDao biometricCompDao;

    @Override
    public String createBiometricComp(BiometricCompSaveReqVO createReqVO) {
        // 插入
        BiometricCompDO biometricComp = BeanUtils.toBean(createReqVO, BiometricCompDO.class);
        biometricCompDao.insert(biometricComp);
        // 返回
        return biometricComp.getId();
    }

    @Override
    public void updateBiometricComp(BiometricCompSaveReqVO updateReqVO) {
        // 校验存在
        validateBiometricCompExists(updateReqVO.getId());
        // 更新
        BiometricCompDO updateObj = BeanUtils.toBean(updateReqVO, BiometricCompDO.class);
        biometricCompDao.updateById(updateObj);
    }

    @Override
    public void deleteBiometricComp(String id) {
        // 校验存在
        validateBiometricCompExists(id);
        // 删除
        biometricCompDao.deleteById(id);
    }

    private void validateBiometricCompExists(String id) {
        if (biometricCompDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-生物特征比对数据不存在");
        }
    }

    @Override
    public BiometricCompDO getBiometricComp(String id) {
        return biometricCompDao.selectById(id);
    }

    @Override
    public PageResult<BiometricCompDO> getBiometricCompPage(BiometricCompPageReqVO pageReqVO) {
        return biometricCompDao.selectPage(pageReqVO);
    }

    @Override
    public List<BiometricCompDO> getBiometricCompList(BiometricCompListReqVO listReqVO) {
        return biometricCompDao.selectList(listReqVO);
    }

    @Override
    public String compareCreate(BiometricComPareSaveReqVO createReqVO) {
        // 插入
        BiometricCompDO biometricComp = BeanUtils.toBean(createReqVO, BiometricCompDO.class);
        biometricComp.setStatus(BiometricCompStatusEnum.DFK.getCode());
        biometricCompDao.insert(biometricComp);
        // 返回
        return biometricComp.getId();
    }

    @Override
    public void compareFeedback(BiometricCompFkReqVO fkReqVO) {
        BiometricCompDO biometricCompDO = biometricCompDao.selectById(fkReqVO.getId());
        if (biometricCompDO == null) {
            throw new ServerException("实战平台-管教业务-生物特征比对数据不存在");
        }
        if (!BiometricCompStatusEnum.DFK.getCode().equals(biometricCompDO.getStatus())) {
            throw new ServerException("非待反馈状态，不能进行反馈");
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        biometricCompDO.setStatus(BiometricCompStatusEnum.YFK.getCode());
        biometricCompDO.setIsComp(BiometricCompTypeEnum.getByCode(fkReqVO.getIsComp()).getCode());
        biometricCompDO.setCompCaseNo(fkReqVO.getCompCaseNo());
        biometricCompDO.setCompCaseType(fkReqVO.getCompCaseType());
        biometricCompDO.setDisposalSituation(fkReqVO.getDisposalSituation());
        biometricCompDO.setCompOperatorSfzh(sessionUser.getIdCard());
        biometricCompDO.setCompOperatorName(sessionUser.getName());
        biometricCompDO.setCompOperatorTime(new Date());
        biometricCompDao.updateById(biometricCompDO);
    }


}
