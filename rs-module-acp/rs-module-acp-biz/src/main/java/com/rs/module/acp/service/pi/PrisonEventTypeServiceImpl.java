package com.rs.module.acp.service.pi;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypeListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypePageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypeRespVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypeSaveReqVO;
import com.rs.module.acp.entity.pi.PrisonEventSettingDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.pi.PrisonEventTypeDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.PrisonEventTypeDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-巡视管控-所情事件类型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrisonEventTypeServiceImpl extends BaseServiceImpl<PrisonEventTypeDao, PrisonEventTypeDO> implements PrisonEventTypeService {

    @Resource
    private PrisonEventTypeDao prisonEventTypeDao;

    @Autowired
    private PrisonEventTypeItemService prisonEventTypeItemService;

    @Autowired
    private PrisonEventDisposeTemplateService prisonEventDisposeTemplateService;

    @Autowired
    private PrisonEventPushSettingService prisonEventPushSettingService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createPrisonEventType(PrisonEventTypeSaveReqVO createReqVO) {
        // 插入
        PrisonEventTypeDO prisonEventType = BeanUtils.toBean(createReqVO, PrisonEventTypeDO.class);
        prisonEventType.setId(StringUtil.getGuid32());

        //存储事件明细
        if (CollectionUtil.isNotEmpty(createReqVO.getEventItemList())) {
            prisonEventTypeItemService.savePrisonEventTypeTreeList(createReqVO.getEventItemList(), prisonEventType.getId());
        }

        //存储处置模板
        if (CollectionUtil.isNotEmpty(createReqVO.getDisposeTemplateList())) {
            prisonEventDisposeTemplateService.savePrisonEventDisposeTemplateList(createReqVO.getDisposeTemplateList(), prisonEventType.getId());
        }

        //保存推送岗位
        if (CollectionUtil.isNotEmpty(createReqVO.getPushSettingList())) {
            prisonEventPushSettingService.savePrisonEventPushSettingList(createReqVO.getPushSettingList(), prisonEventType.getId());
        }

        prisonEventTypeDao.insert(prisonEventType);
        // 返回
        return prisonEventType.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePrisonEventType(PrisonEventTypeSaveReqVO updateReqVO) {
        // 更新
        PrisonEventTypeDO updateObj = BeanUtils.toBean(updateReqVO, PrisonEventTypeDO.class);

        //存储事件明细
        if (CollectionUtil.isNotEmpty(updateReqVO.getEventItemList())) {
            prisonEventTypeItemService.savePrisonEventTypeTreeList(updateReqVO.getEventItemList(), updateObj.getId());
        }

        //存储处置模板
        if (CollectionUtil.isNotEmpty(updateReqVO.getDisposeTemplateList())) {
            prisonEventDisposeTemplateService.savePrisonEventDisposeTemplateList(updateReqVO.getDisposeTemplateList(), updateObj.getId());
        }

        //保存推送岗位
        if (CollectionUtil.isNotEmpty(updateReqVO.getPushSettingList())) {
            prisonEventPushSettingService.savePrisonEventPushSettingList(updateReqVO.getPushSettingList(), updateObj.getId());
        }

        prisonEventTypeDao.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePrisonEventType(String id) {
        // 校验存在
        validatePrisonEventTypeExists(id);

        LambdaQueryWrapper<PrisonEventTypeDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(PrisonEventTypeDO::getIsBuiltin).eq(PrisonEventTypeDO::getId,id);
        PrisonEventTypeDO typeDO = getOne(lambdaQueryWrapper);
        if("1".equals(typeDO.getIsBuiltin())){
            throw new ServerException("该所情类型不允许删除！");
        }

        //删除事件明细
        prisonEventTypeItemService.deletePrisonEventTypeItemByTypeId(id);

        //删除处置模板
        prisonEventDisposeTemplateService.deletePrisonEventDisposeTemplateByTypeId(id);

        //删除事件推送配置
        prisonEventPushSettingService.deletePrisonEventPushSettingByTypeId(id);

        // 删除
        prisonEventTypeDao.deleteById(id);
    }

    private void validatePrisonEventTypeExists(String id) {
        if (prisonEventTypeDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-所情事件类型数据不存在");
        }
    }

    @Override
    public PrisonEventTypeDO getPrisonEventType(String id) {
        return prisonEventTypeDao.selectById(id);
    }

    @Override
    public PageResult<PrisonEventTypeRespVO> getPrisonEventTypePage(PrisonEventTypePageReqVO pageReqVO) {
        pageReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        PageResult<PrisonEventTypeDO> pageResult = prisonEventTypeDao.selectPage(pageReqVO);
        if (CollectionUtil.isEmpty(pageResult.getList())) {
            return new PageResult<>(new ArrayList<>(), pageResult.getTotal());
        }
        List<PrisonEventTypeRespVO> respVOList = BeanUtils.toBean(pageResult.getList(), PrisonEventTypeRespVO.class);

        return new PageResult<>(respVOList, pageResult.getTotal());
    }

    @Override
    public List<PrisonEventTypeRespVO> getPrisonEventTypeList(PrisonEventTypeListReqVO listReqVO) {
        listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        List<PrisonEventTypeDO> typeDOList = prisonEventTypeDao.selectList(listReqVO);
        if (CollectionUtil.isEmpty(typeDOList)) {
            return new ArrayList<>();
        }

        List<PrisonEventTypeRespVO> respVOList = BeanUtils.toBean(typeDOList, PrisonEventTypeRespVO.class);
        if(!listReqVO.isList()){
            for (PrisonEventTypeRespVO eventTypeRespVO : respVOList) {
                //获取事件明细树形
                eventTypeRespVO.setEventItemList(prisonEventTypeItemService.getPrisonEventTypeItemTreeByTypeId(eventTypeRespVO.getId()));

                //获取处置模板
                eventTypeRespVO.setDisposeTemplateList(prisonEventDisposeTemplateService.getPrisonEventDisposeTemplateListByTypeId(eventTypeRespVO.getId()));

                //获取推送岗位
                eventTypeRespVO.setPushSettingList(prisonEventPushSettingService.getPrisonEventPushSettingListByTypeId(eventTypeRespVO.getId()));
            }
        }

        return respVOList;
    }

    @Override
    public PrisonEventTypeRespVO getPrisonEventTypeById(String id) {

        PrisonEventTypeDO prisonEventTypeDO = prisonEventTypeDao.selectById(id);
        if(ObjectUtil.isEmpty(prisonEventTypeDO)){
            throw new ServerException("所情事件类型不存在！");
        }

        PrisonEventTypeRespVO eventTypeRespVO = BeanUtils.toBean(prisonEventTypeDO, PrisonEventTypeRespVO.class);

        //获取事件明细树形
        eventTypeRespVO.setEventItemList(prisonEventTypeItemService.getPrisonEventTypeItemTreeByTypeId(id));

        //获取处置模板
        eventTypeRespVO.setDisposeTemplateList(prisonEventDisposeTemplateService.getPrisonEventDisposeTemplateListByTypeId(id));

        //获取推送岗位
        eventTypeRespVO.setPushSettingList(prisonEventPushSettingService.getPrisonEventPushSettingListByTypeId(id));

        return eventTypeRespVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeStatus(String id) {
        PrisonEventTypeDO prisonEventTypeDO = prisonEventTypeDao.selectById(id);

        if(ObjectUtil.isEmpty(prisonEventTypeDO)){
            throw new ServerException("所情类型不存在");
        }
        if(prisonEventTypeDO.getIsEnabled() == 1){
            prisonEventTypeDO.setIsEnabled((short) 0);
        }else {
            prisonEventTypeDO.setIsEnabled((short) 1);
        }
        return updateById(prisonEventTypeDO);
    }
}
