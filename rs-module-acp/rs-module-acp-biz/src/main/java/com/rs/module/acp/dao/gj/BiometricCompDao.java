package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.biometriccomp.BiometricCompListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.biometriccomp.BiometricCompPageReqVO;
import com.rs.module.acp.entity.gj.BiometricCompDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-生物特征比对 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BiometricCompDao extends IBaseDao<BiometricCompDO> {


    default PageResult<BiometricCompDO> selectPage(BiometricCompPageReqVO reqVO) {
        Page<BiometricCompDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<BiometricCompDO> wrapper = new LambdaQueryWrapperX<BiometricCompDO>()
            .eqIfPresent(BiometricCompDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(BiometricCompDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(BiometricCompDO::getBiometricType, reqVO.getBiometricType())
            .betweenIfPresent(BiometricCompDO::getSubmitTime, reqVO.getSubmitTime())
            .eqIfPresent(BiometricCompDO::getCaseUnitType, reqVO.getCaseUnitType())
            .likeIfPresent(BiometricCompDO::getCaseUnitName, reqVO.getCaseUnitName())
            .eqIfPresent(BiometricCompDO::getSubmitMethod, reqVO.getSubmitMethod())
            .eqIfPresent(BiometricCompDO::getRemark, reqVO.getRemark())
            .eqIfPresent(BiometricCompDO::getIsComp, reqVO.getIsComp())
            .eqIfPresent(BiometricCompDO::getCompCaseNo, reqVO.getCompCaseNo())
            .eqIfPresent(BiometricCompDO::getCompCaseType, reqVO.getCompCaseType())
            .eqIfPresent(BiometricCompDO::getDisposalSituation, reqVO.getDisposalSituation())
            .eqIfPresent(BiometricCompDO::getCompOperatorSfzh, reqVO.getCompOperatorSfzh())
            .likeIfPresent(BiometricCompDO::getCompOperatorName, reqVO.getCompOperatorName())
            .betweenIfPresent(BiometricCompDO::getCompOperatorTime, reqVO.getCompOperatorTime())
            .eqIfPresent(BiometricCompDO::getStatus, reqVO.getStatus())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(BiometricCompDO::getAddTime);
        }
        Page<BiometricCompDO> biometricCompPage = selectPage(page, wrapper);
        return new PageResult<>(biometricCompPage.getRecords(), biometricCompPage.getTotal());
    }
    default List<BiometricCompDO> selectList(BiometricCompListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BiometricCompDO>()
            .eqIfPresent(BiometricCompDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(BiometricCompDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(BiometricCompDO::getBiometricType, reqVO.getBiometricType())
            .betweenIfPresent(BiometricCompDO::getSubmitTime, reqVO.getSubmitTime())
            .eqIfPresent(BiometricCompDO::getCaseUnitType, reqVO.getCaseUnitType())
            .likeIfPresent(BiometricCompDO::getCaseUnitName, reqVO.getCaseUnitName())
            .eqIfPresent(BiometricCompDO::getSubmitMethod, reqVO.getSubmitMethod())
            .eqIfPresent(BiometricCompDO::getRemark, reqVO.getRemark())
            .eqIfPresent(BiometricCompDO::getIsComp, reqVO.getIsComp())
            .eqIfPresent(BiometricCompDO::getCompCaseNo, reqVO.getCompCaseNo())
            .eqIfPresent(BiometricCompDO::getCompCaseType, reqVO.getCompCaseType())
            .eqIfPresent(BiometricCompDO::getDisposalSituation, reqVO.getDisposalSituation())
            .eqIfPresent(BiometricCompDO::getCompOperatorSfzh, reqVO.getCompOperatorSfzh())
            .likeIfPresent(BiometricCompDO::getCompOperatorName, reqVO.getCompOperatorName())
            .betweenIfPresent(BiometricCompDO::getCompOperatorTime, reqVO.getCompOperatorTime())
            .eqIfPresent(BiometricCompDO::getStatus, reqVO.getStatus())
        .orderByDesc(BiometricCompDO::getAddTime));    }


    }
