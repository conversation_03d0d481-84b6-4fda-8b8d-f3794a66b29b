package com.rs.module.acp.service.zh.shiftteam;

import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.zh.vo.shiftteam.StaffDutyShiftSaveReqVO;
import com.rs.module.base.enums.StaffDutyTypeEnum;
import com.rs.module.base.service.zh.staffduty.StaffDutyRecordService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.rs.module.acp.entity.zh.StaffDutyShiftDO;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.zh.StaffDutyShiftDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;

import java.util.List;


/**
 * 值班模板班次 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class StaffDutyShiftServiceImpl extends BaseServiceImpl<StaffDutyShiftDao, StaffDutyShiftDO> implements StaffDutyShiftService {

    @Resource
    private StaffDutyShiftDao staffDutyShiftDao;
    @Resource
    private StaffDutyRecordService staffDutyRecordService;
    @Override
    public String createStaffDutyShift(StaffDutyShiftSaveReqVO createReqVO) {
        // 插入
        StaffDutyShiftDO entity = BeanUtils.toBean(createReqVO, StaffDutyShiftDO.class);
        if(StringUtil.isEmpty(createReqVO.getTempId())){
            String tempId = staffDutyRecordService.getTempId(null, StaffDutyTypeEnum.ON_DUTY_AT_INSTITUTE.getCode());
            entity.setTempId(tempId);
        }
        staffDutyShiftDao.insert(entity);
        // 返回
        return entity.getId();
    }

    @Override
    public void updateStaffDutyShift(StaffDutyShiftSaveReqVO updateReqVO) {
        // 校验存在
        validateStaffDutyShiftExists(updateReqVO.getId());
        // 更新
        StaffDutyShiftDO updateObj = BeanUtils.toBean(updateReqVO, StaffDutyShiftDO.class);
        staffDutyShiftDao.updateById(updateObj);
    }

    @Override
    public void deleteStaffDutyShift(String id) {
        // 校验存在
        validateStaffDutyShiftExists(id);
        // 删除
        staffDutyShiftDao.deleteById(id);
    }

    private void validateStaffDutyShiftExists(String id) {
        if (staffDutyShiftDao.selectById(id) == null) {
            throw new ServerException("值班模板班次数据不存在");
        }
    }

    @Override
    public StaffDutyShiftDO getStaffDutyShift(String id) {
        return staffDutyShiftDao.selectById(id);
    }

    @Override
    public List<StaffDutyShiftDO> getListByOrgCode(String orgCode) {
        if(StringUtil.isEmpty(orgCode)) orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        return staffDutyShiftDao.selectList(new LambdaQueryWrapperX<StaffDutyShiftDO>().
                eq(StaffDutyShiftDO::getOrgCode, orgCode).
                eq(StaffDutyShiftDO::getIsDel, 0).orderByAsc(StaffDutyShiftDO::getShiftOrder));
    }


}
