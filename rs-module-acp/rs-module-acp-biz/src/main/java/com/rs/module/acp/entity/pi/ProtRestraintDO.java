package com.rs.module.acp.entity.pi;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-巡视管控-保护性约束 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_prot_restraint")
@KeySequence("acp_pi_prot_restraint_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_prot_restraint")
public class ProtRestraintDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 监室ID
     */
    private String roomId;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 申请人身份证号
     */
    private String applicantSfzh;
    /**
     * 申请人
     */
    private String applicant;
    /**
     * 申请事由 字典：ZD_BHXYSSQSY
     */
    private String reason;
    /**
     * 约束带类别 字典：ZD_YSDLB
     */
    private String restraintType;
    /**
     * 执行人身份证号
     */
    private String executorSfzh;
    /**
     * 执行人身份证号
     */
    private String executor;
    /**
     * 约束开始时间
     */
    private Date startTime;
    /**
     * 束结束时间
     */
    private Date endTime;
    /**
     * 状态
     */
    private String status;

}
