package com.rs.module.acp.service.db;

import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.cons.DetainRegStatusEnum;
import com.rs.module.acp.cons.LeaderApprovalStatusEnum;
import com.rs.module.acp.dao.db.DetainRegKssDao;
import com.rs.module.acp.entity.db.DetainRegKssDO;
import com.rs.module.acp.util.GeneralUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.OutHospitalDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.db.OutHospitalDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-收押业务-出所就医 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OutHospitalServiceImpl extends BaseServiceImpl<OutHospitalDao, OutHospitalDO> implements OutHospitalService {

    @Resource
    private OutHospitalDao outHospitalDao;

    @Resource
    private DetainRegKssDao detainRegKssDao;

    @Override
    public String createOutHospital(OutHospitalSaveReqVO createReqVO) {
        // 插入
        OutHospitalDO outHospital = BeanUtils.toBean(createReqVO, OutHospitalDO.class);
        outHospitalDao.insert(outHospital);
        // 返回
        return outHospital.getId();
    }

    @Override
    public void updateOutHospital(OutHospitalSaveReqVO updateReqVO) {
        // 校验存在
        validateOutHospitalExists(updateReqVO.getId());
        // 更新
        OutHospitalDO updateObj = BeanUtils.toBean(updateReqVO, OutHospitalDO.class);
        outHospitalDao.updateById(updateObj);
    }

    @Override
    public void deleteOutHospital(String id) {
        // 校验存在
        validateOutHospitalExists(id);
        // 删除
        outHospitalDao.deleteById(id);
    }

    private void validateOutHospitalExists(String id) {
        if (outHospitalDao.selectById(id) == null) {
            throw new ServerException("实战平台-收押业务-出所就医数据不存在");
        }
    }

    @Override
    public OutHospitalDO getOutHospital(String id) {
        return outHospitalDao.selectById(id);
    }

    @Override
    public PageResult<OutHospitalDO> getOutHospitalPage(OutHospitalPageReqVO pageReqVO) {
        return outHospitalDao.selectPage(pageReqVO);
    }

    @Override
    public List<OutHospitalDO> getOutHospitalList(OutHospitalListReqVO listReqVO) {
        return outHospitalDao.selectList(listReqVO);
    }

    @Override
    public String doctorRegister(OutHospitalSaveReqVO updateReqVO) {
        String currentStep = "";
        String nextStep = "";
        updateReqVO.setJbsj(new Date());
        currentStep = "01";
        if(updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())){
            nextStep = "02";
            updateReqVO.setStatus(DetainRegStatusEnum.PENDING.getCode());
        }else if(updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.DRAFT.getCode())){
            nextStep = "01";
        }

        registerProcess(updateReqVO,currentStep,nextStep);
        return "true";
    }

    @Override
    public String jjsyRegister(OutHospitalSaveReqVO updateReqVO) {
        String currentStep = "";
        String nextStep = "";
        updateReqVO.setYwzryjJbsj(new Date());
        currentStep = "02";
        if(updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())){
            if(updateReqVO.getSpzt()!=null&&updateReqVO.getSpzt().equalsIgnoreCase(LeaderApprovalStatusEnum.APPROVED.getCode())){
                nextStep = "03";
                updateReqVO.setStatus(DetainRegStatusEnum.PENDING.getCode());
            }else{
                nextStep = "09";
            }
        }else if(updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.DRAFT.getCode())){
            nextStep = "02";
        }

        registerProcess(updateReqVO,currentStep,nextStep);
        return "true";
    }

    @Override
    public String qwap(OutHospitalSaveReqVO updateReqVO) {
        String currentStep = "";
        String nextStep = "";
        updateReqVO.setQwapJbsj(new Date());
        currentStep = "03";
        if(updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())){
            nextStep = "04";
            updateReqVO.setStatus(DetainRegStatusEnum.PENDING.getCode());
        }else if(updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.DRAFT.getCode())){
            nextStep = "03";
        }

        registerProcess(updateReqVO,currentStep,nextStep);
        return "true";
    }

    @Override
    public OutHospitalDO getPrisonerInfo(String jgrybm,String currentStep) {
        OutHospitalListReqVO listReqVO = new OutHospitalListReqVO();
        listReqVO.setJgrybm(jgrybm);
        if(currentStep!=null && !currentStep.isEmpty()){
            listReqVO.setCurrentStep(currentStep);
        }
        List<OutHospitalDO> list = outHospitalDao.selectList(listReqVO);
//        List<OutHospitalDO> list = outHospitalDao.selectList("jgrybm",jgrybm);
        if(list != null && list.size() > 0){
            return list.get(0);
        }
        return null;
    }

    @Override
    public String leaderRegister(OutHospitalSaveReqVO updateReqVO) {
        String currentStep = "";
        String nextStep = "";

        currentStep = "04";
        if(updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())){
            if(updateReqVO.getSpzt()!=null&&updateReqVO.getSpzt().equalsIgnoreCase(LeaderApprovalStatusEnum.APPROVED.getCode())){
                nextStep = "05";
                updateReqVO.setStatus(DetainRegStatusEnum.PENDING.getCode());
            }else{
                nextStep = "09";
            }
        }else if(updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.DRAFT.getCode())){
            nextStep = "04";
        }

        registerProcess(updateReqVO,currentStep,nextStep);
        return "true";
    }

    @Override
    public String confirmByBack(OutHospitalSaveReqVO updateReqVO) {
        String currentStep = "";
        String nextStep = "";
        updateReqVO.setSpzt(updateReqVO.getSfhsqr()==1?LeaderApprovalStatusEnum.APPROVED.getCode():LeaderApprovalStatusEnum.REJECTED.getCode());

        currentStep = "08";
        if(updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())){
            if(updateReqVO.getSpzt()!=null&&updateReqVO.getSpzt().equalsIgnoreCase(LeaderApprovalStatusEnum.APPROVED.getCode())){
                nextStep = "09";
            }else{
                //回所确认，若选择否，则跳转回回所勤务安排
                nextStep = "06";
                updateReqVO.setStatus(DetainRegStatusEnum.DRAFT.getCode());
            }
        }else if(updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.DRAFT.getCode())){
            nextStep = "08";
        }

        registerProcess(updateReqVO,currentStep,nextStep);
        return "true";
    }

    @Override
    public String confirmByLeave(OutHospitalSaveReqVO updateReqVO) {
        String currentStep = "";
        String nextStep = "";
        updateReqVO.setSpzt(updateReqVO.getSflsqr()==1?LeaderApprovalStatusEnum.APPROVED.getCode():LeaderApprovalStatusEnum.REJECTED.getCode());

        currentStep = "05";
        if(updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())){
            if(updateReqVO.getSpzt()!=null&&updateReqVO.getSpzt().equalsIgnoreCase(LeaderApprovalStatusEnum.APPROVED.getCode())){
                nextStep = "06";
                updateReqVO.setStatus(DetainRegStatusEnum.PENDING.getCode());
            }else{
                nextStep = "09";
            }
        }else if(updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.DRAFT.getCode())){
            nextStep = "05";
        }

        registerProcess(updateReqVO,currentStep,nextStep);
        //演示数据处理###############
        dealSyncData(updateReqVO);
        return "true";
    }

    private void dealSyncData(OutHospitalSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String orgCode = sessionUser.getOrgCode();

        DetainRegKssDO detainRegKssDO = detainRegKssDao.selectOne(DetainRegKssDO::getRybh,updateReqVO.getJgrybm(),DetainRegKssDO::getOrgCode,orgCode);
        if(detainRegKssDO != null){
            detainRegKssDO.setId(GeneralUtil.generateUUID());
            detainRegKssDO.setRybh(GeneralUtil.generateRybh(orgCode));
            detainRegKssDO.setJgrybm(detainRegKssDO.getRybh());
            detainRegKssDO.setStatus("02");
            detainRegKssDO.setCurrentStep("01");
            detainRegKssDO.setOrgCode("110000112");
            detainRegKssDO.setYysSydw("北京市第三看守所");
            detainRegKssDO.setYysSymj("张警官");
            detainRegKssDO.setYysSymjlxdh("13890987878");
            detainRegKssDO.setSfpdyj("1");
            detainRegKssDO.setDrjssj(new Date());
            detainRegKssDao.insert(detainRegKssDO);
        }

    }

    @Override
    public String qwapBack(OutHospitalSaveReqVO updateReqVO) {
        String currentStep = "";
        String nextStep = "";

        currentStep = "06";
        if(updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())){
            nextStep = "07";
            updateReqVO.setStatus(DetainRegStatusEnum.PENDING.getCode());
        }else if(updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.DRAFT.getCode())){
            nextStep = "06";
        }

        registerProcess(updateReqVO,currentStep,nextStep);
        return "true";
    }

    @Override
    public String leaderRegisterBack(OutHospitalSaveReqVO updateReqVO) {
        String currentStep = "";
        String nextStep = "";

        currentStep = "07";
        if(updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())){
            if(updateReqVO.getSpzt()!=null&&updateReqVO.getSpzt().equalsIgnoreCase(LeaderApprovalStatusEnum.APPROVED.getCode())){
                nextStep = "08";
                updateReqVO.setStatus(DetainRegStatusEnum.PENDING.getCode());
            }else{
                nextStep = "09";
            }
        }else if(updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.DRAFT.getCode())){
            nextStep = "07";
        }

        registerProcess(updateReqVO,currentStep,nextStep);
        return "true";
    }

    /**
     * 通用注册/更新流程方法
     *
     * @param updateReqVO 请求参数
     * @param currentStep 当前步骤编号
     * @param nextStep    下一步骤编号
     * @return 操作结果
     */
    private String registerProcess(OutHospitalSaveReqVO updateReqVO, String currentStep, String nextStep) {
        String jgrybm = updateReqVO.getJgrybm();
        OutHospitalListReqVO listReqVO = new OutHospitalListReqVO();
        listReqVO.setJgrybm(jgrybm);
        listReqVO.setCurrentStep(currentStep);
        List<OutHospitalDO> list = outHospitalDao.selectList(listReqVO);
//        List<OutHospitalDO> list = outHospitalDao.selectList("jgrybm", jgrybm);

        OutHospitalDO outHospital;
        boolean isUpdate = false;

        if (list != null && !list.isEmpty()) {
            // 更新已有记录
            outHospital = list.get(0);
            outHospital = BeanUtils.toBean(updateReqVO, OutHospitalDO.class); // 复用部分字段
            outHospital.setId(list.get(0).getId());
            outHospital.setJgrybm(jgrybm);
            outHospital.setUpdateTime(new Date());
            isUpdate = true;
        } else {
            // 插入新记录
            outHospital = BeanUtils.toBean(updateReqVO, OutHospitalDO.class);
            outHospital.setId(UUID.randomUUID().toString().replaceAll("-", ""));
            outHospital.setAddTime(new Date());
        }

        // 公共字段设置
        outHospital.setStatus(updateReqVO.getStatus() != null ? updateReqVO.getStatus() : DetainRegStatusEnum.DRAFT.getCode());
        outHospital.setCurrentStep(nextStep);
//        outHospital.setJbsj(new Date());

        // 特殊字段由子类实现（可选）
        customizeFields(outHospital, updateReqVO, currentStep, nextStep);

        if (isUpdate) {
            outHospitalDao.updateById(outHospital);
        } else {
            outHospitalDao.insert(outHospital);
        }

        return "success";
    }

    /**
     * 子类可重写此方法以设置特定字段
     *
     * @param outHospital 要填充的 DO
     * @param updateReqVO 请求 VO
     * @param currentStep 当前步骤
     * @param nextStep    下一步骤
     */
    protected void customizeFields(OutHospitalDO outHospital, OutHospitalSaveReqVO updateReqVO, String currentStep, String nextStep) {
        // 默认无实现，供子类扩展
    }

}
