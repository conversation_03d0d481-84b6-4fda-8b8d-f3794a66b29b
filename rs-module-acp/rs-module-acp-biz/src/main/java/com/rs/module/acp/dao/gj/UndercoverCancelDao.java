package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverCancelListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverCancelPageReqVO;
import com.rs.module.acp.entity.gj.UndercoverCancelDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-耳目撤销 Dao
*
* <AUTHOR>
*/
@Mapper
public interface UndercoverCancelDao extends IBaseDao<UndercoverCancelDO> {


    default PageResult<UndercoverCancelDO> selectPage(UndercoverCancelPageReqVO reqVO) {
        Page<UndercoverCancelDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<UndercoverCancelDO> wrapper = new LambdaQueryWrapperX<UndercoverCancelDO>()
            .eqIfPresent(UndercoverCancelDO::getCancelReason, reqVO.getCancelReason())
            .eqIfPresent(UndercoverCancelDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(UndercoverCancelDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(UndercoverCancelDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(UndercoverCancelDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(UndercoverCancelDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(UndercoverCancelDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(UndercoverCancelDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(UndercoverCancelDO::getAddTime);
        }
        Page<UndercoverCancelDO> undercoverCancelPage = selectPage(page, wrapper);
        return new PageResult<>(undercoverCancelPage.getRecords(), undercoverCancelPage.getTotal());
    }
    default List<UndercoverCancelDO> selectList(UndercoverCancelListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<UndercoverCancelDO>()
            .eqIfPresent(UndercoverCancelDO::getCancelReason, reqVO.getCancelReason())
            .eqIfPresent(UndercoverCancelDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(UndercoverCancelDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(UndercoverCancelDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(UndercoverCancelDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(UndercoverCancelDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(UndercoverCancelDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(UndercoverCancelDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(UndercoverCancelDO::getAddTime));    }


    }
