package com.rs.module.acp.util;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.PinyinUtil;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApprovalTraceVO;
import com.rs.module.base.util.BspApprovalUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class EquipmentUseUtil {


    /**
     * 流程轨迹处理
     * <AUTHOR>
     * @date 2025/6/7 15:34
     * @param [bspApprovalTrack]
     * @return java.util.List<com.rs.module.acp.controller.admin.gj.vo.common.GjApprovalTraceVO>
     */
    public static List<GjApprovalTraceVO> converBspApprovalTrack(String actInstid){
        List<GjApprovalTraceVO> list = new ArrayList<>();
        JSONObject bspApprovalTrack = BspApprovalUtil.getBpmApi().approveTrack(actInstid);


        if (bspApprovalTrack == null || !bspApprovalTrack.getBoolean("success")) {
            log.error("请求审批结果出错");
            return null;
          //  throw new RuntimeException("请求审批结果出错");
        }

        JSONArray data = bspApprovalTrack.getJSONArray("data");
        if (data == null || data.isEmpty()) {
            log.error("请求审批结果为空");
            return null;
            //throw new RuntimeException("请求审批结果为空");
        }

        for (Object obj: data) {
            JSONObject track = JSON.parseObject(JSON.toJSONString(obj));
            String taskName = track.getString("taskName");
            if(taskName.contains("审批")){
                GjApprovalTraceVO approvalTraceVO = new GjApprovalTraceVO();
                approvalTraceVO.setNodeKey(PinyinUtil.getSimplePinyin(taskName));
                approvalTraceVO.setNodeName(taskName);
                if(StrUtil.isBlank( track.getString("createTime"))){
                    continue;
                }
                approvalTraceVO.setNodeCreateTime(  track.getString("createTime").substring(0, 19));
                Integer nodeStatus = -1;
                List<GjApprovalTraceVO.TraceVO> nodeInfo = new ArrayList<>();
                if(track.getInteger("status") != null &&  (track.getInteger("status")  == 5 ||  track.getInteger("status")  == 6) ) {
                    nodeStatus= 1;
                    nodeInfo.add(GjApprovalTraceVO.TraceVO.builder().key("审批人").val(track.getString("executeUserName")).build());
                    String endTime = track.getString("endTime");
                    if(StrUtil.isNotBlank(endTime) ){
                        endTime = endTime.substring(0,19);
                    }
                    nodeInfo.add(GjApprovalTraceVO.TraceVO.builder().key("审批时间").val(endTime).build());
                    nodeInfo.add(GjApprovalTraceVO.TraceVO.builder().key("审批结果").val(  "5".equals(track.getString("isApprove")) ? "同意": "不同意").build());
                    nodeInfo.add(GjApprovalTraceVO.TraceVO.builder().key("审批意见").val(track.getString("approvalContent")).build());
                    approvalTraceVO.setNodeStatus(nodeStatus);
                    approvalTraceVO.setNodeInfo(nodeInfo );
                    list.add(approvalTraceVO);
                }
                //else{
                //    nodeInfo.add(GjApprovalTraceVO.TraceVO.builder().key("审批人").val("").build());
                //    nodeInfo.add(GjApprovalTraceVO.TraceVO.builder().key("审批时间").val("").build());
                //    nodeInfo.add(GjApprovalTraceVO.TraceVO.builder().key("审批结果").val( "").build());
                //    nodeInfo.add(GjApprovalTraceVO.TraceVO.builder().key("审批意见").val("").build());
                //}

            }
        }
        return list;
    }


    //    "taskKey": "sid-EA0F98F6-34F4-4E0F-A846-0EB4F68D3E2F",
    //            "executeUserName": "超级管理员",
    //            "businessId": "1931238671315636224",
    //            "mobile": "***********",
    //            "approvalContent": "通过",
    //            "isApprove": "5",
    //            "duration": 0,
    //            "actDefId": "xinxiyuanbujianshenqing:1:1927538417142206464",
    //            "executeCityId": "000000",
    //            "czpt": 0,
    //            "executeOrgId": "000000000000",
    //            "createTime": "2025-06-07 14:35:54:925",
    //            "executeUserId": "512501196512305186",
    //            "actInstId": "1931238672505376768",
    //            "executeRegId": "000000",
    //            "taskName": "单位审批",
    //            "id": "1931238692197634048",
    //            "executeOrgName": "示例机构",
    //            "endTime": "2025-06-07 14:35:59:000",
    //            "taskId": "1931238672757035008",
    //            "status": 5
}
