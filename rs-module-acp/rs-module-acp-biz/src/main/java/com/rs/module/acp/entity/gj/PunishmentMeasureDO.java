package com.rs.module.acp.entity.gj;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-处罚呈批关联措施 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_punishment_measure")
@KeySequence("acp_gj_punishment_measure_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_punishment_measure")
public class PunishmentMeasureDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 处罚呈批ID
     */
    private String punishmentId;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 处罚措施
     */
    private String measures;
    /**
     * 处罚周期开始日期
     */
    private Date startDate;
    /**
     * 处罚周期结束日期
     */
    private Date endDate;
    /**
     * 处罚时长，自动生成（天-时-分钟）
     */
    private Integer duration;
    /**
     * 处罚业务ID
     */
    private String businessId;

}
