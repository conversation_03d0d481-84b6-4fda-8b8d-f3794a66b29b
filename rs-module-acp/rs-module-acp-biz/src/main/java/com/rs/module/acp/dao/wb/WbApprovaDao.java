package com.rs.module.acp.dao.wb;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface WbApprovaDao {

    void updateTask(@Param("id") String id,@Param("taskId") String taskId,@Param("type") String type);

    void updateStatus(@Param("id") String id,@Param("status") String status,@Param("type") String type);

    JSONObject getApprovaInfo(@Param("id") String id,@Param("type") String type);
}
