package com.rs.module.acp.entity.gj;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-教育康复活动 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_edurehab_activity")
@KeySequence("acp_gj_edurehab_activity_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_edurehab_activity")
public class EdurehabActivityDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 活动时间
     */
    private Date activityTime;
    /**
     * 康复民警身份证号
     */
    private String rehabPoliceSfzh;
    /**
     * 康复民警姓名
     */
    private String rehabPolice;
    /**
     * 活动主题
     */
    private String activityTopic;
    /**
     * 参加人员身份证号
     */
    private String jgrybm;
    /**
     * 参加人员姓名
     */
    private String jgryxm;
    /**
     * 参加人数
     */
    private Integer participantCount;
    /**
     * 活动详情
     */
    private String activityDetails;
    /**
     * 附件地址
     */
    private String attUrl;

}
