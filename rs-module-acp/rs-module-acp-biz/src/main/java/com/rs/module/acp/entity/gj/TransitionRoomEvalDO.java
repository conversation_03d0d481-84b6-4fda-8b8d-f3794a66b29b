package com.rs.module.acp.entity.gj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 实战平台-管教业务-过渡考核登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_transition_room_eval")
@KeySequence("acp_gj_transition_room_eval_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_transition_room_eval")
public class TransitionRoomEvalDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 过渡监室ID
     */
    private String transitionRoomId;
    /**
     * 考核依据
     */
    private String evalBasis;
    /**
     * 考核内容
     */
    private String evalContent;
    /**
     * 考核情况
     */
    private String evalResult;
    /**
     * 考核时间
     */
    private Date evalTime;

    @ApiModelProperty("备注")
    private String remark;

}
