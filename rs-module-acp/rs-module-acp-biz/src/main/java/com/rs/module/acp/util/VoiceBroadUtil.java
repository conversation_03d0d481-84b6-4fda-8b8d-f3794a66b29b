package com.rs.module.acp.util;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.bsp.common.util.ServiceLocator;
import com.bsp.common.util.StringUtil;
import com.bsp.common.util.TemplateUtils;
import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.SocketService;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.PushMessageAckVO;
import com.rs.module.acp.cons.VoiceBroadConstants;
import com.rs.module.acp.entity.pm.BaseDeviceInscreenDO;
import com.rs.module.acp.entity.sys.VbConfigCurrentDO;
import com.rs.module.acp.entity.sys.VbTaskDO;
import com.rs.module.acp.service.sys.VbTaskService;
import com.rs.util.ThreadPoolUtil;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.db.Entity;

/**
 * 语音播报工具类
 * <AUTHOR>
 * @date 2025年6月26日
 */
public class VoiceBroadUtil {
	
	private static SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd");
	private static SimpleDateFormat sdfDateTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	

	/**
	 * 根据数据库实体更新即时播报任务
	 * @param taskDo VbTaskDO 播报任务对象
	 * @param entity Entity 数据库实体
	 */
	public static void updateTaskByEntity(VbTaskDO taskDo, Entity entity) {		
		if(entity != null) {
			taskDo.setBusinessId(entity.getStr("id"));
			taskDo.setVbRoomId(entity.getStr("room_id"));
			taskDo.setVbSerialNumber(entity.getStr("serial_number"));
			taskDo.setStartTime(entity.getTimestamp("start_time"));
			taskDo.setEndTime(entity.getTimestamp("end_time"));
		}
	}
	
	/**
	 * 构建实时播报任务
	 * @param currentDO VbConfigCurrentDO 实时播报配置
	 * @param inscreen BaseDeviceInscreenDO 仓内屏
	 * @return VbTaskDO
	 */
	public static VbTaskDO buildCurrentTask(VbConfigCurrentDO currentDO, BaseDeviceInscreenDO inscreen) {
		VbTaskDO taskDo = new VbTaskDO();
		
		//复制定时配置的属性
		BeanUtil.copyProperties(currentDO, taskDo, "id", "addTime");
		
		//设置属性
		taskDo.setId(StringUtil.getGuid32());
		taskDo.setStatus(VoiceBroadConstants.JOB_STATUS_WAIT);
		taskDo.setVbType(VoiceBroadConstants.VB_TYPE_CURRENT);
		taskDo.setAddTime(new Date());		
		taskDo.setBusinessId(currentDO.getId());
		taskDo.setVbRoomId(inscreen.getRoomId());
		taskDo.setVbSerialNumber(inscreen.getSerialNumber());
		
		//处理播报内容
		Map<String, Object> context = BeanUtil.beanToMap(taskDo);
		String content = TemplateUtils.parseTemplate(currentDO.getContent(), context, null);
		taskDo.setContent(content);
		
		return taskDo;
	}
	
	/**
	 * 将Hutool的Entity转换为Map
	 * @param entity Entity 数据库实体
	 * @return Map<String, Object>
	 */
	public static Map<String, Object> entityToMap(Entity entity){
		Map<String, Object> map = new HashMap<>();
		
		//根据类型绑定属性
		entity.forEach((k, v) -> {
			if(v instanceof Timestamp) {
				map.put(k, sdfDateTime.format(v));
			}
			else if(v instanceof Date) {
				map.put(k, sdfDate.format(v));
			}
			else {
				map.put(k, String.valueOf(v));
			}
		});
		
		return map;
	}
	
	/**
	 * 判断日期是否在某个静音时间段内
	 * @param date Date 要比较的日期
	 * @param slientTimeSlots String 静音时间段
	 * @return boolean
	 */
	public static boolean isSlient(Date date, String slientTimeSlots) {
		if(StringUtil.isNotEmpty(slientTimeSlots) && date != null) {
			
			//将Date转换成LocalTime
		    Instant instant = date.toInstant();
		    ZoneId zone = ZoneId.systemDefault();
		    LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
		    LocalTime bTime = localDateTime.toLocalTime();
		    
		    //获取静音时间段
		    String[] singleSlots = slientTimeSlots.split("\\,");
		    for(String singleSlot : singleSlots) {
		    	String[] slots = singleSlot.split("\\-");
		    	if(slots.length == 2) {		    		
		    		LocalTime start = LocalTime.parse(slots[0]);
		    		LocalTime end = LocalTime.parse(slots[0]);
		    		if(bTime.isAfter(start) && bTime.isBefore(end)) {
		    			return true;
		    		}
		    	}
		    }
		}
		
		return false;
	}
	
	/**
	 * 推送WebSocket消息
	 * @param taskDo VbTaskDO 播报任务
	 */
	public static void sendSocketMessage(VbTaskDO taskDo) {
		ThreadPoolUtil.getPool().execute(new Runnable() {
			
			@Override
			public void run() {
				SocketService socketService = ServiceLocator.getBean(SocketService.class);
    			List<String> clientIds = socketService.getSessionBySerialNumber(Arrays.asList(taskDo.getVbSerialNumber()));
    			if(!clientIds.isEmpty()) {
    				PushMessageForm pushForm = new PushMessageForm();
    				pushForm.setSessionIds(clientIds);
    				pushForm.setTarget(SocketActionConstants.PushMessageTargetEnum.web.name());
    		        pushForm.setAction(SocketActionConstants.WebVoiceBroadcast);
    		        PushMessageAckVO ack = socketService.pushMessageToClientWaitReply(pushForm);
    		        if(ack != null && ack.getOk()) {
    		        	VbTaskService taskService = ServiceLocator.getBean(VbTaskService.class);
    		        	taskDo.setStatus(VoiceBroadConstants.JOB_STATUS_PUSH);
    		        	taskService.updateById(taskDo);
    		        }
    			}
			}
		});
	}
}
