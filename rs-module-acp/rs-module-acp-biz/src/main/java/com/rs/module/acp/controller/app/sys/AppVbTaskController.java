package com.rs.module.acp.controller.app.sys;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.app.sys.vo.AppVbTaskRespVO;
import com.rs.module.acp.entity.pm.BaseDeviceInscreenDO;
import com.rs.module.acp.entity.sys.VbConfigGlobalDO;
import com.rs.module.acp.entity.sys.VbTaskDO;
import com.rs.module.acp.service.pm.BaseDeviceInscreenService;
import com.rs.module.acp.service.sys.VbConfigGlobalService;
import com.rs.module.acp.service.sys.VbTaskService;
import com.rs.module.acp.util.VoiceBroadUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@Api(tags = "实战平台-语音播报-app待播报任务")
@RestController
@RequestMapping("/app/acp/sys/vbTask")
@Validated
public class AppVbTaskController {

	@Resource
    private VbTaskService vbTaskService;
	
	@Resource
	private VbConfigGlobalService globalService;
	
	@Resource
	BaseDeviceInscreenService inscreeService;
	
	@PostMapping("/list")
    @ApiOperation(value = "语音播报-待播报任务列表")
	@ApiImplicitParam(name = "serialNumber", value = "播报设备序列号")
	public CommonResult<List<AppVbTaskRespVO>> getVbTaskList(@RequestParam("serialNumber") String serialNumber){        
		BaseDeviceInscreenDO inscreen = inscreeService.getInscreenBySeiralNumber(serialNumber);
		if(inscreen != null) {
			List<VbTaskDO> list = vbTaskService.getVbTaskListApp(serialNumber);
			List<AppVbTaskRespVO> listResp = BeanUtils.toBean(list, AppVbTaskRespVO.class);
			
			//添加静音时间段
			if(CollectionUtil.isNotNull(listResp)) {				
				VbConfigGlobalDO globalDo = globalService.getConfig(inscreen.getOrgCode());
				if(globalDo != null) {
					String slientTimeSlots = globalDo.getSilentTimeSlots();
					if(StringUtil.isNotEmpty(slientTimeSlots)) {
						listResp = listResp.stream().map(s -> {
							s.setSilentTimeSlots(slientTimeSlots);
							if(VoiceBroadUtil.isSlient(s.getStartTime(), slientTimeSlots)) {
								s.setIsSilent(true);
							}
							else {
								s.setIsSilent(false);
							}
							return s;
						}).collect(Collectors.toList());
					}
				}
			}			
			
			return success(listResp);
		}
		else {
			return CommonResult.error("获取待播报任务失败！找不到设备");
		}
	}
	
	@PostMapping("/updateStatus")
    @ApiOperation(value = "语音播报-更新播报任务状态")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "id", value = "播报任务Id"),
		@ApiImplicitParam(name = "serialNumber", value = "播报设备序列号"),
		@ApiImplicitParam(name = "status", value = "播报状态(2:已播报,3:超时自动取消,4:播报异常)")
	})
	public CommonResult<?> updateStatus(@RequestParam("id") String id,
			@RequestParam("serialNumber") String serialNumber, @RequestParam("status") Short status){
		BaseDeviceInscreenDO inscreen = inscreeService.getInscreenBySeiralNumber(serialNumber);
		if(inscreen != null) {
			VbTaskDO taskDO = vbTaskService.getById(id);
			if(taskDO != null) {
				taskDO.setVbSerialNumber(serialNumber);
				taskDO.setStatus(status);
				vbTaskService.updateById(taskDO);
				return success("播报任务更新状态成功");
			}
			else {
				return CommonResult.error("播报任务更新状态失败！播报任务不存在");
			}
		}
		else {
			return CommonResult.error("播报任务更新状态失败！找不到设备");
		}
	}
}
