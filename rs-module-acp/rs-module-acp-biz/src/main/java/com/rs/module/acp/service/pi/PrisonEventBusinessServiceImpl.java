package com.rs.module.acp.service.pi;

import cn.hutool.core.util.ObjectUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventBusinessListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventBusinessPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventBusinessSaveReqVO;
import com.rs.module.acp.service.wb.WbCommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.entity.pi.PrisonEventBusinessDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.PrisonEventBusinessDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-巡视管控-所情关联业务 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrisonEventBusinessServiceImpl extends BaseServiceImpl<PrisonEventBusinessDao, PrisonEventBusinessDO> implements PrisonEventBusinessService {

    @Resource
    private PrisonEventBusinessDao prisonEventBusinessDao;

    @Autowired
    private WbCommonService wbCommonService;

    @Override
    public String createPrisonEventBusiness(PrisonEventBusinessSaveReqVO createReqVO) {
        // 插入
        PrisonEventBusinessDO prisonEventBusiness = BeanUtils.toBean(createReqVO, PrisonEventBusinessDO.class);
        prisonEventBusinessDao.insert(prisonEventBusiness);
        // 返回
        return prisonEventBusiness.getId();
    }

    @Override
    public void updatePrisonEventBusiness(PrisonEventBusinessSaveReqVO updateReqVO) {
        // 校验存在
        validatePrisonEventBusinessExists(updateReqVO.getId());
        // 更新
        PrisonEventBusinessDO updateObj = BeanUtils.toBean(updateReqVO, PrisonEventBusinessDO.class);
        prisonEventBusinessDao.updateById(updateObj);
    }

    @Override
    public void deletePrisonEventBusiness(String id) {
        // 校验存在
        validatePrisonEventBusinessExists(id);
        // 删除
        prisonEventBusinessDao.deleteById(id);
    }

    private void validatePrisonEventBusinessExists(String id) {
        if (prisonEventBusinessDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-所情关联业务数据不存在");
        }
    }

    @Override
    public PrisonEventBusinessDO getPrisonEventBusiness(String id) {
        return prisonEventBusinessDao.selectById(id);
    }

    @Override
    public PageResult<PrisonEventBusinessDO> getPrisonEventBusinessPage(PrisonEventBusinessPageReqVO pageReqVO) {
        return prisonEventBusinessDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrisonEventBusinessDO> getPrisonEventBusinessList(PrisonEventBusinessListReqVO listReqVO) {
        return prisonEventBusinessDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean savePrisonEventBusinessList(List<PrisonEventBusinessSaveReqVO> saveReqVOList, String eventId, String handleId) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        List<PrisonEventBusinessDO> businessDOList = BeanUtils.toBean(saveReqVOList,PrisonEventBusinessDO.class);
        for(PrisonEventBusinessDO prisonEventBusinessDO:businessDOList){
            prisonEventBusinessDO.setEventId(eventId);
            prisonEventBusinessDO.setHandleId(handleId);
            if(ObjectUtil.isEmpty(prisonEventBusinessDO.getOperatePerson())){
                prisonEventBusinessDO.setOperatePerson(sessionUser.getName());
            }

            if(ObjectUtil.isNotEmpty(prisonEventBusinessDO.getAttUrl())){
                prisonEventBusinessDO.setAttUrl(wbCommonService.saveFile(null,prisonEventBusinessDO.getAttUrl()));
            }
        }
        return saveBatch(businessDOList);
    }
}
