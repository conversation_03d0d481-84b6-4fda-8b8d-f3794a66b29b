package com.rs.module.acp.entity.zh;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 综合管理-绩效考核指标 DO
 *
 * <AUTHOR>
 */
@TableName("acp_zh_indicator")
@KeySequence("acp_zh_indicator_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_indicator")
public class IndicatorDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 指标名称
     */
    private String indicatorName;
    /**
     * 指标类型ID
     */
    private String indicatorCateId;
    /**
     * 指标类型名称
     */
    private String indicatorCateName;
    /**
     * 指标类型，01：自观指标，02：加减分指标
     */
    private String indicatorType;
    /**
     * 排序序号
     */
    private Integer sortOrder;
    /**
     * 是否启用
     */
    private Short isEnabled;
    /**
     * 备注
     */
    private String remark;

}
