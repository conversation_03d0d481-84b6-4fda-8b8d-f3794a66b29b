package com.rs.module.acp.job.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.module.acp.entity.wb.ArraignmentDO;
import com.rs.module.acp.entity.wb.BringInterrogationDO;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.acp.service.wb.ArraignmentService;
import com.rs.module.acp.service.wb.BringInterrogationService;
import com.rs.module.acp.service.wb.WbCommonService;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 提询定时任务
 */
@Component
public class BringInterrogationJob {

    @Autowired
    private BringInterrogationService bringInterrogationService;

    @Autowired
    private WbCommonService wbCommonService;


    /**
     * 提询登记24小时内，状态未变更为【已完成】，则为异常
     */
    @XxlJob("abnormalBringInterrogation")
    public void abnormalBringInterrogation(){
        Map<String, PrisonerVwRespVO> prisonerMap = new HashMap<>();
        XxlJobHelper.log("提询任务：-----开始-------");
        XxlJobHelper.log("提询任务：提询登记24小时内，状态未变更为【已完成】，则为异常");
        try {
            Date queryTime = DateUtil.offsetHour(new Date(),-24);
            LambdaQueryWrapper<BringInterrogationDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.select(BringInterrogationDO::getId,BringInterrogationDO::getJgrybm,BringInterrogationDO::getStartApplyArraignmentTime);
            lambdaQueryWrapper.in(BringInterrogationDO::getStatus, Arrays.asList("0","1","2","3"))
                    .le(BringInterrogationDO::getAddTime,queryTime);
            Integer pageNo = 1;
            Integer pageSize = 500;
            while (true){
                Page<BringInterrogationDO> page = new Page<>(pageNo,pageSize);
                IPage<BringInterrogationDO> bringInterrogationDOPage = bringInterrogationService.page(page,lambdaQueryWrapper);
                if(CollectionUtil.isEmpty(bringInterrogationDOPage.getRecords())){
                    break;
                }
                List<String> idList = bringInterrogationDOPage.getRecords().stream().map(BringInterrogationDO::getId).collect(Collectors.toList());
                LambdaUpdateWrapper<BringInterrogationDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.in(BringInterrogationDO::getId,idList).set(BringInterrogationDO::getStatus,"99");
                bringInterrogationService.update(lambdaUpdateWrapper);

                //发送消息
                for(BringInterrogationDO bringInterrogationDO:bringInterrogationDOPage.getRecords()){
                    XxlJobHelper.log("提询任务：-----发送补录待办消息---start----");
                    wbCommonService.additionalRecordingReminder(JSONObject.parseObject(JSON.toJSONString(bringInterrogationDO)), WbConstants.BUSINESS_TYPE_BRING_INTERROGATION);
                    XxlJobHelper.log("提询任务：-----发送补录待办消息----end---");
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            XxlJobHelper.log("提询任务异常：{}",e.getMessage());
        }
        XxlJobHelper.log("提询任务:-----结束------");
    }
}
