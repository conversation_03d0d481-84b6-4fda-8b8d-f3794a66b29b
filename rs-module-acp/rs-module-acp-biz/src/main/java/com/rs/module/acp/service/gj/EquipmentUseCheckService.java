package com.rs.module.acp.service.gj;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.gj.vo.EquipmentUseCheckRespVO;
import com.rs.module.acp.controller.admin.gj.vo.EquipmentUseCheckSaveReqVO;
import com.rs.module.acp.entity.gj.EquipmentUseCheckDO;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 实战平台-管教业务-戒具检查 Service 接口
 *
 * <AUTHOR>
 */
public interface EquipmentUseCheckService extends IBaseService<EquipmentUseCheckDO> {

    /**
     * 创建实战平台-管教业务-戒具检查
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createEquipmentUseCheck(@Valid EquipmentUseCheckSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-戒具检查
     *
     * @param updateReqVO 更新信息
     */
    void updateEquipmentUseCheck(@Valid EquipmentUseCheckSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-戒具检查
     *
     * @param id 编号
     */
    void deleteEquipmentUseCheck(String id);

    /**
     * 获得实战平台-管教业务-戒具检查
     *
     * @param id 编号
     * @return 实战平台-管教业务-戒具检查
     */
    EquipmentUseCheckDO getEquipmentUseCheck(String id);

    /**
     * 获取待检查人员列表
     * @param useId
     * @return
     */
    List<EquipmentUseCheckRespVO> getCheckList(String useId);

    /**
     * 获取今日监室待检查人员列表
     * @param roomCode
     * @return
     */
    List<EquipmentUseCheckRespVO> getCheckByRoomCode(String roomCode);

    /**
     * 获取戒具检查记录列表
     * @param roomCode
     * @param startTime
     * @param endTimme
     * @return
     */
    List<EquipmentUseCheckRespVO> getCheckRecordList(String roomCode, Date startTime, Date endTimme);

    EquipmentUseCheckRespVO getByUseIdAndDate(String useId, Date checkDate);


}
