package com.rs.module.acp.dao.wb;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.GoodsDeliveryDetailsDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-窗口业务-物品顾送明细 Dao
*
* <AUTHOR>
*/
@Mapper
public interface GoodsDeliveryDetailsDao extends IBaseDao<GoodsDeliveryDetailsDO> {


    default PageResult<GoodsDeliveryDetailsDO> selectPage(GoodsDeliveryDetailsPageReqVO reqVO) {
        Page<GoodsDeliveryDetailsDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<GoodsDeliveryDetailsDO> wrapper = new LambdaQueryWrapperX<GoodsDeliveryDetailsDO>()
            .eqIfPresent(GoodsDeliveryDetailsDO::getGoodsDeliveryId, reqVO.getGoodsDeliveryId())
            .likeIfPresent(GoodsDeliveryDetailsDO::getGoodsName, reqVO.getGoodsName())
            .eqIfPresent(GoodsDeliveryDetailsDO::getGoodsQuantity, reqVO.getGoodsQuantity())
            .eqIfPresent(GoodsDeliveryDetailsDO::getUnit, reqVO.getUnit())
            .eqIfPresent(GoodsDeliveryDetailsDO::getRemark, reqVO.getRemark())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(GoodsDeliveryDetailsDO::getAddTime);
        }
        Page<GoodsDeliveryDetailsDO> goodsDeliveryDetailsPage = selectPage(page, wrapper);
        return new PageResult<>(goodsDeliveryDetailsPage.getRecords(), goodsDeliveryDetailsPage.getTotal());
    }
    default List<GoodsDeliveryDetailsDO> selectList(GoodsDeliveryDetailsListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<GoodsDeliveryDetailsDO>()
            .eqIfPresent(GoodsDeliveryDetailsDO::getGoodsDeliveryId, reqVO.getGoodsDeliveryId())
            .likeIfPresent(GoodsDeliveryDetailsDO::getGoodsName, reqVO.getGoodsName())
            .eqIfPresent(GoodsDeliveryDetailsDO::getGoodsQuantity, reqVO.getGoodsQuantity())
            .eqIfPresent(GoodsDeliveryDetailsDO::getUnit, reqVO.getUnit())
            .eqIfPresent(GoodsDeliveryDetailsDO::getRemark, reqVO.getRemark())
        .orderByDesc(GoodsDeliveryDetailsDO::getAddTime));    }


    }
