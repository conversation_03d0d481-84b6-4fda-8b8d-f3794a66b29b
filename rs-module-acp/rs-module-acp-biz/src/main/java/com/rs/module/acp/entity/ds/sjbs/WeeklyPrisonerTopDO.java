package com.rs.module.acp.entity.ds.sjbs;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-数据固化-每周会见被监管人员排名 DO
 *
 * <AUTHOR>
 */
@TableName("acp_ds_weekly_prisoner_top")
@KeySequence("acp_ds_weekly_prisoner_top_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_ds_weekly_prisoner_top")
public class WeeklyPrisonerTopDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 固化日期
     */
    private String solidificationDate;
    /**
     * 固化开始日期
     */
    private String startDate;
    /**
     * 固化结束日期
     */
    private String endDate;
    /**
     * 每周数据报送ID
     */
    private String weeklyDataSubmitId;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 每周数据报送ID
     */
    private Integer hjcs;
    /**
     * 排名
     */
    private Integer pm;

}
