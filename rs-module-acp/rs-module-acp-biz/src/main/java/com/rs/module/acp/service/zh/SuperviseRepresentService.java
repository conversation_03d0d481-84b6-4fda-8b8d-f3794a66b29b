package com.rs.module.acp.service.zh;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.zh.vo.SuperviseRepresentRespVO;
import com.rs.module.acp.controller.admin.zh.vo.SuperviseRepresentSaveReqVO;
import com.rs.module.acp.entity.zh.SuperviseRepresentDO;

import javax.validation.Valid;

/**
 * 综合管理-督导申诉 Service 接口
 *
 * <AUTHOR>
 */
public interface SuperviseRepresentService extends IBaseService<SuperviseRepresentDO>{

    /**
     * 创建综合管理-督导申诉
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createSuperviseRepresent(@Valid SuperviseRepresentSaveReqVO createReqVO) throws Exception;

    /**
     * 督导申诉审批
     * @param approveReqVO
     * @return
     * @throws Exception
     */
    boolean approve(JSONObject approveReqVO) throws Exception;

    /**
     * 更新综合管理-督导申诉
     *
     * @param updateReqVO 更新信息
     */
    void updateSuperviseRepresent(@Valid SuperviseRepresentSaveReqVO updateReqVO);

    /**
     * 删除综合管理-督导申诉
     *
     * @param id 编号
     */
    void deleteSuperviseRepresent(String id);

    /**
     * 获得综合管理-督导申诉
     *
     * @param id 编号
     * @return 综合管理-督导申诉
     */
    SuperviseRepresentRespVO getSuperviseRepresent(String superviseId);


}
