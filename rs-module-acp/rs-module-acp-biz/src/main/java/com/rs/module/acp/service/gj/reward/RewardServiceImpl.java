package com.rs.module.acp.service.gj.reward;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApprovalTraceVO;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.reward.*;
import com.rs.module.acp.dao.gj.RewardDao;
import com.rs.module.acp.entity.gj.RewardDO;
import com.rs.module.acp.enums.gj.RewardStatusEnum;
import com.rs.module.acp.util.GjBusTraceUtil;
import com.rs.module.acp.util.RewardUtil;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.base.service.sys.BusTraceService;
import com.rs.module.base.util.BspApprovalUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * 实战平台-管教业务-奖励管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class RewardServiceImpl extends BaseServiceImpl<RewardDao, RewardDO> implements RewardService {

    @Resource
    private RewardDao rewardDao;

    @Resource
    private BusTraceService busTraceService;

    private final String defKey = "guanjiaojinagliguanl";

    @Override
    @Transactional(rollbackFor = Exception.class )
    public String createReward(RewardSaveReqVO createReqVO) {
        // 插入
        RewardDO reward = BeanUtils.toBean(createReqVO, RewardDO.class);
        reward.setStatus(RewardStatusEnum.DSP.getCode());
        rewardDao.insert(reward);
        String busType = MsgBusTypeEnum.GJ_JLGL.getCode();
        //待前端提供
        String msgUrl = StrUtil.format("/#/discipline/rewardManagement?curId={}&saveType=approve", reward.getId());
        MapBuilder<String, Object> variables = MapUtil.builder();
        variables.put("ywbh", reward.getId()).put("busType", busType);
        JSONObject result = BspApprovalUtil.commonStartProcess(defKey, reward.getId(), "【审批】奖励管理", msgUrl, variables.build(), HttpUtils.getAppCode());
        log.info("==========result:{}", result);
        if (result.getIntValue("code") != HttpStatus.OK.value()) {
            throw new ServerException("流程启动失败");
        }
        JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
        reward.setActInstId(bpmTrail.getString("actInstId"));
        reward.setTaskId(bpmTrail.getString("taskId"));
        reward.setStatus(RewardStatusEnum.DSP.getCode());
        rewardDao.updateById(reward);
        return reward.getId();
    }


    private void validateRewardExists(String id) {
        if (rewardDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-奖励管理数据不存在");
        }
    }

    @Override
    public RewardRespVO getReward(String id) {
        RewardDO reward =  rewardDao.selectById(id);
        RewardRespVO respVO = BeanUtils.toBean(reward, RewardRespVO.class);
        if(respVO != null && StrUtil.isNotBlank(reward.getActInstId())){
            JSONObject bspApprovalTrack = BspApprovalUtil.getBpmApi().approveTrack(reward.getActInstId());
            if (bspApprovalTrack == null || !bspApprovalTrack.getBoolean("success")) {
                log.error("请求审批结果出错");
                return respVO;
            }
            JSONArray data = bspApprovalTrack.getJSONArray("data");
            if (data == null || data.isEmpty()) {
                log.error("请求审批结果为空");
                return respVO;
            }

            for (Object obj: data) {
                JSONObject track = JSON.parseObject(JSON.toJSONString(obj));
                String taskName = track.getString("taskName");
                if(taskName.contains("审批")){
                    List<GjApprovalTraceVO.TraceVO> nodeInfo = new ArrayList<>();
                    if(track.getInteger("status") != null &&  track.getInteger("status")  == 5) {
                        respVO.setApproverXm(track.getString("executeUserName"));
                        String endTime = track.getString("endTime");
                        if(StrUtil.isNotBlank(endTime) ){
                            endTime = endTime.substring(0,19);
                        }
                        nodeInfo.add(GjApprovalTraceVO.TraceVO.builder().key("审批时间").val(endTime).build());
                        nodeInfo.add(GjApprovalTraceVO.TraceVO.builder().key("审批结果").val(  "5".equals(track.getString("isApprove")) ? "通过": "不通过").build());
                        nodeInfo.add(GjApprovalTraceVO.TraceVO.builder().key("审批意见").val(track.getString("approvalContent")).build());
                        if(StrUtil.isNotBlank(endTime)){
                            respVO.setApproverTime(DateUtil.parseDateTime(endTime));
                        }
                        respVO.setApprovalComments(track.getString("approvalContent") );
                        respVO.setApprovalResult( track.getString("isApprove"));
                        respVO.setApprovalResultName(  "5".equals(track.getString("isApprove")) ? "通过": "不通过");
                    }

                }
            }

        }
        return respVO;
    }

    @Override
    public PageResult<RewardDO> getRewardPage(RewardPageReqVO pageReqVO) {
        return rewardDao.selectPage(pageReqVO);
    }

    @Override
    public List<RewardDO> getRewardList(RewardListReqVO listReqVO) {
        return rewardDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void regInfo(RewardRegInfoReqVO regInfoReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        RewardDO entity = rewardDao.selectById(regInfoReqVO.getId());
        Assert.notNull(entity, "记录不存在！");

        entity.setExecutor(sessionUser.getName());
        entity.setExecutorSfzh(sessionUser.getIdCard());
        entity.setExecutionTime(regInfoReqVO.getExecutionTime());
        entity.setExecuteSituation(regInfoReqVO.getExecuteSituation());
        entity.setStatus( RewardStatusEnum.YWJ.getCode());

        rewardDao.updateById( entity);

        busTraceService.saveBusTrace(BusTypeEnum.YEWU_JIANGLIGUANLI, GjBusTraceUtil.buildRewardBusTraceContent(entity),
                entity.getJgrybm(),
                SessionUserUtil.getSessionUser().getOrgCode(),
                entity.getId());

        //发送奖励
        RewardUtil.sendRewards(entity);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void leaderApprove(GjApproveReqVO approveReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        RewardDO entity = rewardDao.selectById(approveReqVO.getId());
        Assert.notNull(entity, "记录不存在！");

        RewardStatusEnum statusEnum = RewardStatusEnum.getByCode(entity.getStatus());
        if (!RewardStatusEnum.DSP.getCode().equals(statusEnum.getCode())) {
            throw new ServerException("该状态[" + statusEnum.getName() + "]不允许审批！");
        }

        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(entity.getTaskId(), sessionUser.getIdCard());
        Assert.isTrue(isApproval, "当前人无审批权限" );


        String approvalResult = approveReqVO.getApprovalResult();
        BspApproceStatusEnum bspApproceStatusEnum;
        String status = RewardStatusEnum.DDJJL.getCode();
        if (String.valueOf( BspApproceStatusEnum.PASSED_END.getCode()).equals(approvalResult)) {
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED_END;
        } else {
            bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
            status = RewardStatusEnum.SPBTG.getCode();
        }

        //entity.setApprovalComments(approveReqVO.getApprovalComments());
        //entity.setApproverXm(sessionUser.getName());
        //entity.setApproverSfzh(sessionUser.getIdCard());
        //entity.setApprovalResult(approveReqVO.getApprovalResult());
        //entity.setApproverTime(new Date());
        entity.setStatus(status);
        rewardDao.updateById( entity);

        JSONObject result = BspApprovalUtil.approvalProcessAcp(defKey,
                entity.getActInstId(),
                entity.getTaskId(),
                entity.getId(),
                bspApproceStatusEnum,
                approveReqVO.getApprovalComments()  );
        log.info("=======result:{}", result);
        if(result.getIntValue("code") != HttpStatus.OK.value()){
            throw new ServerException("流程审批失败");
        }

        JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
        entity.setTaskId(bpmTrail.getString("taskId"));
        rewardDao.updateById(entity);
    }

}
