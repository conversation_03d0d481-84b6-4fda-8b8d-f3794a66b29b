package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentRemoveListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentRemovePageReqVO;
import com.rs.module.acp.entity.gj.PunishmentRemoveDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务解除处罚呈批 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PunishmentRemoveDao extends IBaseDao<PunishmentRemoveDO> {


    default PageResult<PunishmentRemoveDO> selectPage(PunishmentRemovePageReqVO reqVO) {
        Page<PunishmentRemoveDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PunishmentRemoveDO> wrapper = new LambdaQueryWrapperX<PunishmentRemoveDO>()
            .eqIfPresent(PunishmentRemoveDO::getPunishmentId, reqVO.getPunishmentId())
            .eqIfPresent(PunishmentRemoveDO::getRemoveReason, reqVO.getRemoveReason())
            .eqIfPresent(PunishmentRemoveDO::getIsInAdvanceRemove, reqVO.getIsInAdvanceRemove())
            .eqIfPresent(PunishmentRemoveDO::getSpecificRemoveReason, reqVO.getSpecificRemoveReason())
            .eqIfPresent(PunishmentRemoveDO::getStatus, reqVO.getStatus())
            .eqIfPresent(PunishmentRemoveDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(PunishmentRemoveDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(PunishmentRemoveDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(PunishmentRemoveDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(PunishmentRemoveDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(PunishmentRemoveDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(PunishmentRemoveDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(PunishmentRemoveDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(PunishmentRemoveDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PunishmentRemoveDO::getAddTime);
        }
        Page<PunishmentRemoveDO> punishmentRemovePage = selectPage(page, wrapper);
        return new PageResult<>(punishmentRemovePage.getRecords(), punishmentRemovePage.getTotal());
    }
    default List<PunishmentRemoveDO> selectList(PunishmentRemoveListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PunishmentRemoveDO>()
            .eqIfPresent(PunishmentRemoveDO::getPunishmentId, reqVO.getPunishmentId())
            .eqIfPresent(PunishmentRemoveDO::getRemoveReason, reqVO.getRemoveReason())
            .eqIfPresent(PunishmentRemoveDO::getIsInAdvanceRemove, reqVO.getIsInAdvanceRemove())
            .eqIfPresent(PunishmentRemoveDO::getSpecificRemoveReason, reqVO.getSpecificRemoveReason())
            .eqIfPresent(PunishmentRemoveDO::getStatus, reqVO.getStatus())
            .eqIfPresent(PunishmentRemoveDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(PunishmentRemoveDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(PunishmentRemoveDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(PunishmentRemoveDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(PunishmentRemoveDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(PunishmentRemoveDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(PunishmentRemoveDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(PunishmentRemoveDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(PunishmentRemoveDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(PunishmentRemoveDO::getAddTime));    }


    }
