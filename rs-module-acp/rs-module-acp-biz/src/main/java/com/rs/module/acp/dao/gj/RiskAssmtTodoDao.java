package com.rs.module.acp.dao.gj;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.riskassmt.RiskAssmtTodoListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.riskassmt.RiskAssmtTodoPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.riskassmt.RiskAssmtTodoRespVO;
import com.rs.module.acp.entity.gj.RiskAssmtTodoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 实战平台-管教业务-风险评估待办 Dao
*
* <AUTHOR>
*/
@Mapper
public interface RiskAssmtTodoDao extends IBaseDao<RiskAssmtTodoDO> {

    default PageResult<RiskAssmtTodoDO> selectPage(RiskAssmtTodoPageReqVO reqVO) {
        Page<RiskAssmtTodoDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<RiskAssmtTodoDO> wrapper = new LambdaQueryWrapperX<RiskAssmtTodoDO>()
            .eqIfPresent(RiskAssmtTodoDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(RiskAssmtTodoDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(RiskAssmtTodoDO::getAreaId, reqVO.getAreaId())
            .likeIfPresent(RiskAssmtTodoDO::getAreaName, reqVO.getAreaName())
            .eqIfPresent(RiskAssmtTodoDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(RiskAssmtTodoDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(RiskAssmtTodoDO::getSourceBusinessId, reqVO.getSourceBusinessId())
            .betweenIfPresent(RiskAssmtTodoDO::getAssmtTime, reqVO.getAssmtTime())
            .eqIfPresent(RiskAssmtTodoDO::getRiskType, reqVO.getRiskType())
            .eqIfPresent(RiskAssmtTodoDO::getOldRiskLevel, reqVO.getOldRiskLevel())
            .eqIfPresent(RiskAssmtTodoDO::getRiskLevel, reqVO.getRiskLevel())
            .betweenIfPresent(RiskAssmtTodoDO::getPushTime, reqVO.getPushTime())
            .eqIfPresent(RiskAssmtTodoDO::getStatus, reqVO.getStatus())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(RiskAssmtTodoDO::getAddTime);
        }
        Page<RiskAssmtTodoDO> riskAssmtTodoPage = selectPage(page, wrapper);
        return new PageResult<>(riskAssmtTodoPage.getRecords(), riskAssmtTodoPage.getTotal());
    }

    default List<RiskAssmtTodoDO> selectList(RiskAssmtTodoListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<RiskAssmtTodoDO>()
            .eqIfPresent(RiskAssmtTodoDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(RiskAssmtTodoDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(RiskAssmtTodoDO::getAreaId, reqVO.getAreaId())
            .likeIfPresent(RiskAssmtTodoDO::getAreaName, reqVO.getAreaName())
            .eqIfPresent(RiskAssmtTodoDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(RiskAssmtTodoDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(RiskAssmtTodoDO::getSourceBusinessId, reqVO.getSourceBusinessId())
            .betweenIfPresent(RiskAssmtTodoDO::getAssmtTime, reqVO.getAssmtTime())
            .eqIfPresent(RiskAssmtTodoDO::getRiskType, reqVO.getRiskType())
            .eqIfPresent(RiskAssmtTodoDO::getOldRiskLevel, reqVO.getOldRiskLevel())
            .eqIfPresent(RiskAssmtTodoDO::getRiskLevel, reqVO.getRiskLevel())
            .betweenIfPresent(RiskAssmtTodoDO::getPushTime, reqVO.getPushTime())
            .eqIfPresent(RiskAssmtTodoDO::getStatus, reqVO.getStatus())
        .orderByDesc(RiskAssmtTodoDO::getAddTime));    }



    /**
     * 新入所评估
     * <AUTHOR>
     * @date 2025/6/15 20:26
     * @param [page]
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.rs.module.acp.controller.admin.gj.vo.riskassmt.RiskAssmtTodoRespVO>
     */
    IPage<RiskAssmtTodoRespVO> getNewEntrantJob(@Param("page") Page<RiskAssmtTodoRespVO> page);


    /**
     * 重点人员关注 状态推送
     * <AUTHOR>
     * @date 2025/6/19 18:57
     * @param [page]
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.rs.module.acp.controller.admin.gj.vo.riskassmt.RiskAssmtTodoRespVO>
     */
    IPage<RiskAssmtTodoRespVO> getAttentionPersonnel(@Param("page") Page<RiskAssmtTodoRespVO> page);




}
