package com.rs.module.acp.service.pi;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventDisposeTemplateListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventDisposeTemplatePageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventDisposeTemplateRespVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventDisposeTemplateSaveReqVO;
import com.rs.module.acp.entity.pi.PrisonEventDisposeTemplateDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-巡视管控-所情处置模板 Service 接口
 *
 * <AUTHOR>
 */
public interface PrisonEventDisposeTemplateService extends IBaseService<PrisonEventDisposeTemplateDO>{

    /**
     * 创建实战平台-巡视管控-所情处置模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrisonEventDisposeTemplate(@Valid PrisonEventDisposeTemplateSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-所情处置模板
     *
     * @param updateReqVO 更新信息
     */
    void updatePrisonEventDisposeTemplate(@Valid PrisonEventDisposeTemplateSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-所情处置模板
     *
     * @param id 编号
     */
    void deletePrisonEventDisposeTemplate(String id);

    /**
     * 获得实战平台-巡视管控-所情处置模板
     *
     * @param id 编号
     * @return 实战平台-巡视管控-所情处置模板
     */
    PrisonEventDisposeTemplateDO getPrisonEventDisposeTemplate(String id);

    /**
    * 获得实战平台-巡视管控-所情处置模板分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-巡视管控-所情处置模板分页
    */
    PageResult<PrisonEventDisposeTemplateDO> getPrisonEventDisposeTemplatePage(PrisonEventDisposeTemplatePageReqVO pageReqVO);

    /**
    * 获得实战平台-巡视管控-所情处置模板列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-巡视管控-所情处置模板列表
    */
    List<PrisonEventDisposeTemplateDO> getPrisonEventDisposeTemplateList(PrisonEventDisposeTemplateListReqVO listReqVO);

    /**
     * 保存所情处置模板列表
     * @param disposeTemplateList
     * @param typeId
     * @return
     */
    boolean savePrisonEventDisposeTemplateList(List<PrisonEventDisposeTemplateSaveReqVO> disposeTemplateList,String typeId);

    /**
     * 实战平台-巡视管控-根据所情配置ID删除所情处置模板
     *
     * @param typeId
     */
    void deletePrisonEventDisposeTemplateByTypeId(String typeId);

    /**
     * 根据所情类型ID 获取处置模板
     * @param typeId
     * @return
     */
    List<PrisonEventDisposeTemplateRespVO> getPrisonEventDisposeTemplateListByTypeId(String typeId);
}
