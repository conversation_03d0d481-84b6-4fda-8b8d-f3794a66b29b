package com.rs.module.acp.job;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.quartz.client.HttpXxlJobApi;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class XxlJobManage {

    // XXL-JOB 地址
    private static final String ADMIN_URL = "http://192.168.3.251:8080/xxl-job-admin";

    // 登录账号密码（根据实际情况修改）
    private static final String USERNAME = "admin";
    private static final String PASSWORD = "xxlbsp"; // 默认是加密的，实际可能需要明文

    // Cookie 缓存
    private String cookie;

    /**
     * 登录并获取 Cookie
     */
    public boolean login() {
        String url = ADMIN_URL + "/login";

        Map<String, Object> params = new HashMap<>();
        params.put("userName", USERNAME);
        params.put("password", PASSWORD);

        HttpResponse response = HttpRequest.post(url)
                .form(params)
                .execute();
        log.info("响应头: {}", response.headers());
        log.info("响应体: {}", response.body());
        // 获取 Set-Cookie
        String setCookie = response.header(Header.SET_COOKIE);
        log.info(setCookie);
        if (setCookie != null && setCookie.contains("XXL_JOB_LOGIN_IDENTITY")) {
            this.cookie = setCookie;
            log.info("登录成功，获取到 Cookie: {}", this.cookie);
            return true;
        } else {
            log.error("登录失败，无法获取 Cookie");
            return false;
        }
    }

    /**
     * 获取执行器 ID（根据 AppName）
     */
    public Integer getJobGroupId(String appName) {
        String url = ADMIN_URL + "/jobgroup/pageList";

        String result = HttpRequest.get(url)
                .header(Header.COOKIE, cookie)
                .execute().body();

        JSONObject json = JSON.parseObject(result);
        if (json.getIntValue("recordsTotal") <= 0) {
            log.error("查询执行器失败: {}", json.getString("msg"));
            return null;
        }

        JSONArray list = json.getJSONArray("data");
        for (Object obj : list) {
            JSONObject item = (JSONObject) obj;
            if (appName.equals(item.getString("appname"))) {
                Integer id = item.getInteger("id");
                log.info("找到执行器 [{}]，ID: {}", appName, id);
                return id;
            }
        }

        log.warn("未找到执行器: {}", appName);
        return null;
    }

    /**
     * 检查任务是否存在（根据 jobGroup + executorHandler）
     */
    public boolean isJobExists(int jobGroup, String executorHandler) {
        String url = ADMIN_URL + "/jobinfo/pageList";

        Map<String, Object> params = new HashMap<>();
        params.put("jobGroup", jobGroup);
        params.put("executorHandler", executorHandler);
        params.put("start", 0);
        params.put("length", 10);

        String result = HttpRequest.post(url)
                .header(Header.COOKIE, cookie)
                .header("X-Requested-With", "XMLHttpRequest")
                .form(params)
                .execute().body();

        JSONObject json = JSON.parseObject(result);
        if (json.getIntValue("recordsTotal") <= 0) {
            log.error("查询任务列表失败: {}", json.getString("msg"));
            return false;
        }

        JSONArray dataList = json.getJSONArray("data");
        if (dataList != null && dataList.size() > 0) {
            for (Object obj : dataList) {
                JSONObject job = (JSONObject) obj;
                if (executorHandler.equals(job.getString("executorHandler"))) {
                    log.info("任务已存在: jobGroup={}, handler={}", jobGroup, executorHandler);
                    return true;
                }
            }
        }

        log.info("任务不存在: jobGroup={}, handler={}", jobGroup, executorHandler);
        return false;
    }

    /**
     * 添加任务（调用你已有的方法）
     */
    public boolean addJob(String scheduleConf,String jobDesc, int jobGroup, String executorHandler) {
        log.info("开始添加任务: {}, cron: {}", executorHandler, scheduleConf);
        XxlJobManage xxlJobManage = new XxlJobManage();
        return xxlJobManage.addMsgAutoJob(scheduleConf, jobDesc,jobGroup, executorHandler);
    }

    /**
     * 主流程：自动登录 → 获取执行器 → 检查任务 → 创建任务（如果不存在）
     */
    public void ensureJobExists(String appName, String jobDesc, String scheduleConf, String executorHandler) {
        if (!login()) {
            log.error("登录失败，停止任务检查");
            return;
        }

        Integer jobGroup = getJobGroupId(appName);
        if (jobGroup == null) {
            log.error("未找到执行器: {}", appName);
            return;
        }

        if (!isJobExists(jobGroup, executorHandler)) {
            if (addJob(scheduleConf, jobDesc,jobGroup, executorHandler)) {
                log.info("任务添加成功");
            } else {
                log.error("任务添加失败");
            }
        } else {
            log.info("任务已存在，无需重复添加");
        }
    }

    public static void main(String[] args) {
        XxlJobManage client = new XxlJobManage();
        //client.ensureJobExists("acp-server", "单独关押每日安全检查登记","0 0 8 * * ?", "aloneImprisonSafetyInspection");
        //每半小时执行一次 scheduleConf
        client.ensureJobExists("acp-server", "定时巡视登记","0 0/30 * * * ?", "regularPatrolRecord");
    }
    /**
     * 添加任务（Bean 模式）
     *
     * @param scheduleConf  cron 表达式，如 "0 0 8 * * ?"
     * @param jobGroup      执行器 ID
     * @param executorHandler 对应 @XxlJob 注解的方法名
     * @return 是否添加成功
     */
    public boolean addMsgAutoJob(String scheduleConf, String jobDesc,int jobGroup, String executorHandler) {
        String url = ADMIN_URL + "/jobinfo/add";

        Map<String, Object> params = new HashMap<>();
        params.put("jobGroup", jobGroup);
        params.put("jobDesc", jobDesc);
        params.put("scheduleConf", scheduleConf); // Cron 表达式
        params.put("scheduleStatus", 1); // 调度状态：1-运行中
        params.put("glueType", "BEAN"); // 固定为 BEAN 模式
        params.put("executorHandler", executorHandler); // 对应方法名
        params.put("triggerType", 1); // 触发类型：1-固定频率
        params.put("executorRouteStrategy", "FIRST"); // 路由策略
        params.put("blockingStrategy", "SINGLE"); // 阻塞处理策略
        params.put("misfireStrategy", "DO_NOTHING"); // 错过调度策略
        params.put("alarmEmail", ""); // 报警邮箱
        params.put("childJobid", ""); // 子任务ID
        params.put("triggerStatus", 1); // 触发状态
        params.put("executorParam", ""); // 参数（可选）
        params.put("author","wl");
        params.put("scheduleType", "CRON");
        params.put("executorBlockStrategy", "SERIAL_EXECUTION");

        String response = HttpRequest.post(url)
                .header(Header.COOKIE, cookie)
                .form(params)
                .timeout(5000)
                .execute().body();

        log.info("添加任务响应结果: {}", response);

        return response != null && response.contains("\"code\":200");
    }

}
