package com.rs.module.acp.controller.app.gj.vo.conflict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Date 2025/3/26 09:42
 */
@ApiModel(description = "管理后台-实战平台内屏-管教业务-确认 Request VO")
@Data
public class AppConflictSaveReqVO {

    @ApiModelProperty("数据来源：1:实战平台，2：仓外屏, 3：仓内屏")
    @NotEmpty(message = "数据来源：1:实战平台，2：仓外屏, 3：仓内屏不能为空")
    private String dataSources;
    @ApiModelProperty("发起人（通过数据来源区分，民警发起记录民警身份证号，被拘留人员记录：）")
    @NotBlank(message = "发起人姓名（记录发起人，通过数据来源区分是民警发起还是被拘留人员发起）不能为空")
    private String initiator;
    @ApiModelProperty("发起人姓名（记录发起人，通过数据来源区分是民警发起还是被拘留人员发起）")
    @NotBlank(message = "发起人姓名（记录发起人，通过数据来源区分是民警发起还是被拘留人员发起）不能为空")
    private String initiatorXm;
    @ApiModelProperty("调解方式（01：单场调解，02：个别协商，03：庭式调解）")
    @NotEmpty(message = "调解方式（01：单场调解，02：个别协商，03：庭式调解）不能为空")
    private String mediationMode;
    @ApiModelProperty("矛盾类别（01：非法上访、02：经济纠纷（补偿赔偿债务）、03：家庭矛盾、04：青少年违法、05：民间打架斗殴、06：民事纠纷、07：非法维权、08：对执法机关不满案件、09：迷信活动、10：“六失一偏”人员、11：其他类")
    @NotEmpty(message = "矛盾类别（01：非法上访、02：经济纠纷（补偿赔偿债务）、03：家庭矛盾、04：青少年违法、05：民间打架斗殴、06：民事纠纷、07：非法维权、08：对执法机关不满案件、09：迷信活动、10：“六失一偏”人员、11：其他类不能为空")
    private String conflictType;


}
