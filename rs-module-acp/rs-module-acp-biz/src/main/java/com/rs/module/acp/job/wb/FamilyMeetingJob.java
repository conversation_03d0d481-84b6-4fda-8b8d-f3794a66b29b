package com.rs.module.acp.job.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.module.acp.entity.wb.FamilyMeetingDO;
import com.rs.module.acp.entity.wb.LawyerMeetingDO;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.acp.service.wb.FamilyMeetingService;
import com.rs.module.acp.service.wb.LawyerMeetingService;
import com.rs.module.acp.service.wb.WbCommonService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class FamilyMeetingJob {

    @Autowired
    private FamilyMeetingService familyMeetingService;

    @Autowired
    private WbCommonService wbCommonService;


    /**
     * 距离登记中的【预约会见结束时间】已超过12小时，【带出安检】/【带回安检】未登记，则状态为【异常】
     */
    @XxlJob("abnormalFamilyMeeting")
    public void abnormalFamilyMeeting(){

        XxlJobHelper.log("家属会见异常任务：-----开始-------");
        XxlJobHelper.log("家属会见异常任务：距离登记中的【预约会见结束时间】已超过12小时，【带出安检】/【带回安检】未登记，则状态为【异常】");
        try {
            Date queryTime = DateUtil.offsetHour(new Date(),-12);
            LambdaQueryWrapper<FamilyMeetingDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.select(FamilyMeetingDO::getId,FamilyMeetingDO::getJgrybm,
                    FamilyMeetingDO::getApplyMeetingStartTime);
            lambdaQueryWrapper.in(FamilyMeetingDO::getStatus, Arrays.asList("2","3"))
                    .le(FamilyMeetingDO::getApplyMeetingEndTime,queryTime);
            Integer pageNo = 1;
            Integer pageSize = 500;
            while (true){
                Page<FamilyMeetingDO> page = new Page<>(pageNo,pageSize);
                IPage<FamilyMeetingDO> familyMeetingDOIPage = familyMeetingService.page(page,lambdaQueryWrapper);
                if(CollectionUtil.isEmpty(familyMeetingDOIPage.getRecords())){
                    break;
                }
                List<String> idList = familyMeetingDOIPage.getRecords().stream().map(FamilyMeetingDO::getId).collect(Collectors.toList());
                LambdaUpdateWrapper<FamilyMeetingDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.in(FamilyMeetingDO::getId,idList).set(FamilyMeetingDO::getStatus,"99");
                familyMeetingService.update(lambdaUpdateWrapper);

                //发送消息
                for(FamilyMeetingDO familyMeetingDO:familyMeetingDOIPage.getRecords()){
                    XxlJobHelper.log("家属会见：-----发送补录待办消息---start----");
                    wbCommonService.additionalRecordingReminder(JSONObject.parseObject(JSON.toJSONString(familyMeetingDO)), WbConstants.BUSINESS_TYPE_FAMILY_MEETING);
                    XxlJobHelper.log("家属会见：-----发送补录待办消息----end---");
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            XxlJobHelper.log("家属会见异常任务异常：{}",e.getMessage());
        }
        XxlJobHelper.log("家属会见异常任务:-----结束------");
    }

}
