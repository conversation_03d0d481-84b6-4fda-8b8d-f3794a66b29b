package com.rs.module.acp.entity.gj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 实战平台-管教业务-风险评估待办 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_risk_assmt_todo")
@KeySequence("acp_gj_risk_assmt_todo_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_risk_assmt_todo")
public class RiskAssmtTodoDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 监区id
     */
    private String areaId;
    /**
     * 监区名称
     */
    private String areaName;
    /**
     * 监室号
     */
    private String roomId;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 来源业务ID
     */
    private String sourceBusinessId;
    /**
     * 评估时间
     */
    private Date assmtTime;
    /**
     * 评估类型
     */
    private String riskType;
    /**
     * 原风险等级
     */
    private String oldRiskLevel;
    /**
     * 评估风险等级
     */
    private String riskLevel;
    /**
     * 推送时间
     */
    private Date pushTime;
    /**
     * 评估状态 0待评估 1已评估
     */
    private String status;

}
