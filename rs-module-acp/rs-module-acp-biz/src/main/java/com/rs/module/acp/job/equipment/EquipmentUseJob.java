package com.rs.module.acp.job.equipment;

import com.rs.module.acp.dao.gj.EquipmentUseDao;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class EquipmentUseJob {

    @Autowired
    private EquipmentUseDao equipmentUseDao;

    /**
     * 戒具待解除状态处理
     */
    @XxlJob("restraintsExaminationRemainsLifted")
    public void restraintsExaminationRemainsLifted() {
        // 待解除
        XxlJobHelper.log("-----开始-------");
        XxlJobHelper.log("戒具使用-待解除状态更新");
        try {
            equipmentUseDao.updateRestraintsExaminationRemainsLifted(new Date());
        } catch (Exception e) {
            XxlJobHelper.log("戒具使用-待解除-状态任务异常：{}", e.getMessage());
        }
        XxlJobHelper.log("戒具使用-待解除-状态任务异常:-----结束------");
    }
}
