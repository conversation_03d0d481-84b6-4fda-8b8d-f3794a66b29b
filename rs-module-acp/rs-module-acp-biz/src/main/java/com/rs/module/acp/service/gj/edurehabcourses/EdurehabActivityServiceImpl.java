package com.rs.module.acp.service.gj.edurehabcourses;

import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabActivityListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabActivityPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabActivityRespVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabActivitySaveReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.util.ThreadPoolUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.concurrent.*;

import com.rs.module.acp.entity.gj.EdurehabActivityDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.EdurehabActivityDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-教育康复活动 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EdurehabActivityServiceImpl extends BaseServiceImpl<EdurehabActivityDao, EdurehabActivityDO> implements EdurehabActivityService {

    @Resource
    private EdurehabActivityDao edurehabActivityDao;

    @Resource
    private PrisonerService prisonerService;

    @Override
    public String createEdurehabActivity(EdurehabActivitySaveReqVO createReqVO) {
        // 插入
        EdurehabActivityDO edurehabActivity = BeanUtils.toBean(createReqVO, EdurehabActivityDO.class);
        edurehabActivityDao.insert(edurehabActivity);
        // 返回
        return edurehabActivity.getId();
    }

    @Override
    public void updateEdurehabActivity(EdurehabActivitySaveReqVO updateReqVO) {
        // 校验存在
        validateEdurehabActivityExists(updateReqVO.getId());
        // 更新
        EdurehabActivityDO updateObj = BeanUtils.toBean(updateReqVO, EdurehabActivityDO.class);
        edurehabActivityDao.updateById(updateObj);
    }

    @Override
    public void deleteEdurehabActivity(String id) {
        // 校验存在
        validateEdurehabActivityExists(id);
        // 删除
        edurehabActivityDao.deleteById(id);
    }

    private void validateEdurehabActivityExists(String id) {
        if (edurehabActivityDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-教育康复活动数据不存在");
        }
    }

    @Override
    public EdurehabActivityRespVO getEdurehabActivity(String id) {
        EdurehabActivityDO edurehabActivityDO = edurehabActivityDao.selectById(id);
        EdurehabActivityRespVO edurehabActivityRespVO = BeanUtils.toBean(edurehabActivityDO, EdurehabActivityRespVO.class);
        if(Objects.nonNull(edurehabActivityRespVO)){
            String[] jgrybmArray = edurehabActivityRespVO.getJgrybm().split(",");
            List<PrisonerInDO> prisonerInOneList = prisonerService.getPrisonerInOneList(new ArrayList<>(Arrays.asList(jgrybmArray)));
            edurehabActivityRespVO.setJgryxxList(BeanUtils.toBean(prisonerInOneList, PrisonerVwRespVO.class));
        }
        return edurehabActivityRespVO;
    }

    @Override
    public PageResult<EdurehabActivityDO> getEdurehabActivityPage(EdurehabActivityPageReqVO pageReqVO) {
        return edurehabActivityDao.selectPage(pageReqVO);
    }

    @Override
    public List<EdurehabActivityDO> getEdurehabActivityList(EdurehabActivityListReqVO listReqVO) {
        return edurehabActivityDao.selectList(listReqVO);
    }

    @Override
    public PageResult<EdurehabActivityDO> jykfcjqk(String jgrybm) {
        if(StringUtils.isEmpty(jgrybm)){
            return null;
        }
        return edurehabActivityDao.jykfcjqk(jgrybm);
    }


}
