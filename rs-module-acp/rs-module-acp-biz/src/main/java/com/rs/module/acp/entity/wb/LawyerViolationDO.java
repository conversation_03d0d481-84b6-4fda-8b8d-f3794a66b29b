package com.rs.module.acp.entity.wb;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-窗口业务-律师违规 DO
 *
 * <AUTHOR>
 */
@TableName("acp_wb_lawyer_violation")
@KeySequence("acp_wb_lawyer_violation_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LawyerViolationDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 律师表ID
     */
    private String lawyerId;
    /**
     * 违规时间
     */
    private Date violationTime;
    /**
     * 违规情况
     */
    private String violationType;
    /**
     * 详细说明
     */
    private String description;

}
