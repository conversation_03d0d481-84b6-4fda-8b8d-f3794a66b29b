package com.rs.module.acp.entity.gj;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-违禁品登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_contraband")
@KeySequence("acp_gj_contraband_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_contraband")
public class ContrabandDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 数据来源（字典：ZD_DATA_SOURCES）
     */
    private String dataSources;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 检查时间
     */
    private Date checkTime;
    /**
     * 违禁品类别
     */
    private String contrabandCategory;
    /**
     * 违禁品照片
     */
    private String contrabandImgPath;
    /**
     * 处理情况
     */
    private String handlingSituatio;

}
