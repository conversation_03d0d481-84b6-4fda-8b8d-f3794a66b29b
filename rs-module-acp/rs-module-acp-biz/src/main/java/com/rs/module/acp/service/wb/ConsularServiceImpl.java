package com.rs.module.acp.service.wb;

import cn.hutool.core.util.ObjectUtil;
import com.bsp.security.util.SessionUserUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.ConsularDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.ConsularDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-领事外事信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ConsularServiceImpl extends BaseServiceImpl<ConsularDao, ConsularDO> implements ConsularService {

    @Resource
    private ConsularDao consularDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createConsular(ConsularSaveReqVO createReqVO) {
        // 插入
        ConsularDO consular = BeanUtils.toBean(createReqVO, ConsularDO.class);
        consularDao.insert(consular);
        // 返回
        return consular.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateConsular(ConsularSaveReqVO updateReqVO) {
        // 校验存在
        validateConsularExists(updateReqVO.getId());
        // 更新
        ConsularDO updateObj = BeanUtils.toBean(updateReqVO, ConsularDO.class);
        consularDao.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteConsular(String id) {
        // 校验存在
        validateConsularExists(id);
        // 删除
        consularDao.deleteById(id);
    }

    private void validateConsularExists(String id) {
        if (consularDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-领事外事信息数据不存在");
        }
    }

    @Override
    public ConsularDO getConsular(String id) {
        return consularDao.selectById(id);
    }

    @Override
    public PageResult<ConsularDO> getConsularPage(ConsularPageReqVO pageReqVO) {
        if(ObjectUtil.isEmpty(pageReqVO.getOrgCode())){
            pageReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        return consularDao.selectPage(pageReqVO);
    }

    @Override
    public List<ConsularDO> getConsularList(ConsularListReqVO listReqVO) {
        return consularDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveConsularList(List<ConsularSaveReqVO> consularList) {
        List<ConsularDO> consularDOList = BeanUtils.toBean(consularList,ConsularDO.class);
        return saveOrUpdateBatch(consularDOList);
    }
}
