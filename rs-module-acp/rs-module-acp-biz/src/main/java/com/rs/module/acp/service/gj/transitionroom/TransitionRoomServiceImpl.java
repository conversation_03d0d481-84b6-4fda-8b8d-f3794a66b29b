package com.rs.module.acp.service.gj.transitionroom;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.riskassmt.RiskAssmtTodoSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.transitionroom.TransitionRoomEvalRespVO;
import com.rs.module.acp.controller.admin.gj.vo.transitionroom.TransitionRoomExtendRespVO;
import com.rs.module.acp.controller.admin.gj.vo.transitionroom.TransitionRoomRespVO;
import com.rs.module.acp.controller.admin.gj.vo.transitionroom.TransitionRoomSaveReqVO;
import com.rs.module.acp.dao.gj.TransitionRoomDao;
import com.rs.module.acp.entity.gj.TransitionRoomDO;
import com.rs.module.acp.entity.gj.TransitionRoomEvalDO;
import com.rs.module.acp.entity.gj.TransitionRoomExtendDO;
import com.rs.module.acp.enums.gj.GjRiskAssmtTypeEnum;
import com.rs.module.acp.enums.gj.TransitionRoomStatusEnum;
import com.rs.module.acp.service.gj.riskassmt.RiskAssmtTodoService;
import com.rs.module.base.entity.sys.MsgAddVO;
import com.rs.module.base.util.MsgUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 实战平台-管教业务-过渡监室管理 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class TransitionRoomServiceImpl extends BaseServiceImpl<TransitionRoomDao, TransitionRoomDO> implements TransitionRoomService {

    @Resource
    private TransitionRoomDao transitionRoomDao;

    @Resource
    private TransitionRoomEvalService transitionRoomEvalService;

    @Resource
    private TransitionRoomExtendService transitionRoomExtendService;


    @Resource
    private RiskAssmtTodoService riskAssmtTodoService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createTransitionRoom(TransitionRoomSaveReqVO createReqVO) {
        // 插入
        TransitionRoomDO transitionRoom = BeanUtils.toBean(createReqVO, TransitionRoomDO.class);
        transitionRoom.setIsExtend(0);
        transitionRoom.setActualStartDate(createReqVO.getStartDate());
        transitionRoom.setActualEndDate(createReqVO.getEndDate());
        transitionRoom.setStatus(TransitionRoomStatusEnum.GDZ.getCode());
        transitionRoomDao.insert(transitionRoom);
        return transitionRoom.getId();
    }


    @Override
    public TransitionRoomRespVO getTransitionRoom(String id) {
        TransitionRoomDO roomDO = transitionRoomDao.selectById(id);
        if(roomDO == null){
            return null;
        }
        TransitionRoomRespVO result = BeanUtils.toBean(roomDO, TransitionRoomRespVO.class);
        List<TransitionRoomExtendDO> roomExtendList = transitionRoomExtendService.list(new LambdaQueryWrapperX<TransitionRoomExtendDO>().eq(TransitionRoomExtendDO::getTransitionRoomId, result.getId()));
        if(CollUtil.isNotEmpty(roomExtendList)){
            result.setRoomExtendList(BeanUtils.toBean(roomExtendList, TransitionRoomExtendRespVO.class));
        }
        List<TransitionRoomEvalDO> roomEvalList = transitionRoomEvalService.list(new LambdaQueryWrapperX<TransitionRoomEvalDO>()
                .eq(TransitionRoomEvalDO::getTransitionRoomId, result.getId())
                .orderByDesc(TransitionRoomEvalDO::getAddTime));
        if(CollUtil.isNotEmpty(roomEvalList)){
            //只展示最新的一条
            result.setRoomEval(BeanUtils.toBean(roomEvalList.get(0), TransitionRoomEvalRespVO.class));
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(String jgrybm, TransitionRoomStatusEnum roomStatusEnum) {
        //当传入记录是 监室调整中的时候，将过渡 中改为  监室调整中。
        List<TransitionRoomDO> list = transitionRoomDao.selectList(new LambdaQueryWrapperX<TransitionRoomDO>()
                .eq(TransitionRoomDO::getJgrybm, jgrybm).orderByDesc(TransitionRoomDO::getUpdateTime));
        if(CollUtil.isEmpty(list)){
            //忽略不记
            return;
        }

        TransitionRoomDO roomDO = list.get(0);

        if(TransitionRoomStatusEnum.JSDZZ.equals(roomStatusEnum)){
            LambdaUpdateWrapper<TransitionRoomDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper
                    .eq(TransitionRoomDO::getStatus, TransitionRoomStatusEnum.GDZ.getCode())
                    .eq(TransitionRoomDO::getJgrybm, jgrybm)
                    .set(TransitionRoomDO::getStatus, roomStatusEnum.getCode());
            this.update(lambdaUpdateWrapper);
        }

        //审批通过、已解除
        if(TransitionRoomStatusEnum.YJC.equals(roomStatusEnum)){
            LambdaUpdateWrapper<TransitionRoomDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper
                    .eq(TransitionRoomDO::getStatus, TransitionRoomStatusEnum.JSDZZ.getCode())
                    .eq(TransitionRoomDO::getJgrybm, jgrybm)
                    .set(TransitionRoomDO::getStatus, roomStatusEnum.getCode());
            boolean row =  this.update(lambdaUpdateWrapper);


            if(row){
                //过渡期满，生成待办
                RiskAssmtTodoSaveReqVO riskAssmtTodoSaveReqVO = new RiskAssmtTodoSaveReqVO();
                riskAssmtTodoSaveReqVO.setJgrybm(jgrybm);
                riskAssmtTodoSaveReqVO.setSourceBusinessId(roomDO.getId());
                riskAssmtTodoSaveReqVO.setRiskType(GjRiskAssmtTypeEnum.GDQMFXPG.getCode());
                riskAssmtTodoService.createRiskAssmtTodo(riskAssmtTodoSaveReqVO);

                CompletableFuture.runAsync( ()->{
                    MsgAddVO vo = new MsgAddVO();
                    vo.setPcid(roomDO.getId());
                    //TODO 前端提供
                    vo.setUrl("#-------#");
                    vo.setMsgType("10");
                    vo.setModuleCode("ACP_GJ_RISK_ROOM_TODO");
                    vo.setOrgCode(roomDO.getOrgCode());
                    vo.setOrgName(roomDO.getOrgName());
                    vo.setJgrybm(roomDO.getJgrybm());
                    vo.setToOrgCode(roomDO.getOrgCode());
                    vo.setBusinessId(roomDO.getId());
                    MsgUtil.sendMsg(vo);
                } );

            }

        }

        //审批不通过，将审批过渡中调整为
        if(TransitionRoomStatusEnum.GDZ.equals(roomStatusEnum)){
            LambdaUpdateWrapper<TransitionRoomDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper
                    .eq(TransitionRoomDO::getStatus, TransitionRoomStatusEnum.JSDZZ.getCode())
                    .eq(TransitionRoomDO::getJgrybm, jgrybm)
                    .set(TransitionRoomDO::getStatus, roomStatusEnum.getCode());
            this.update(lambdaUpdateWrapper);
        }

    }



}
