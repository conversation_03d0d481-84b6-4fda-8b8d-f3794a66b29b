package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabTimeSlotListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabTimeSlotPageReqVO;
import com.rs.module.acp.entity.gj.EdurehabTimeSlotDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-教育康复课程时段 Dao
*
* <AUTHOR>
*/
@Mapper
public interface EdurehabTimeSlotDao extends IBaseDao<EdurehabTimeSlotDO> {


    default PageResult<EdurehabTimeSlotDO> selectPage(EdurehabTimeSlotPageReqVO reqVO) {
        Page<EdurehabTimeSlotDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<EdurehabTimeSlotDO> wrapper = new LambdaQueryWrapperX<EdurehabTimeSlotDO>()
            .eqIfPresent(EdurehabTimeSlotDO::getTimeSlotCode, reqVO.getTimeSlotCode())
            .eqIfPresent(EdurehabTimeSlotDO::getStartTime, reqVO.getStartTime())
            .eqIfPresent(EdurehabTimeSlotDO::getEndTime, reqVO.getEndTime())
            .eqIfPresent(EdurehabTimeSlotDO::getDurationMinutes, reqVO.getDurationMinutes())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(EdurehabTimeSlotDO::getAddTime);
        }
        Page<EdurehabTimeSlotDO> edurehabTimeSlotPage = selectPage(page, wrapper);
        return new PageResult<>(edurehabTimeSlotPage.getRecords(), edurehabTimeSlotPage.getTotal());
    }
    default List<EdurehabTimeSlotDO> selectList(EdurehabTimeSlotListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<EdurehabTimeSlotDO>()
            .eqIfPresent(EdurehabTimeSlotDO::getTimeSlotCode, reqVO.getTimeSlotCode())
            .eqIfPresent(EdurehabTimeSlotDO::getStartTime, reqVO.getStartTime())
            .eqIfPresent(EdurehabTimeSlotDO::getEndTime, reqVO.getEndTime())
            .eqIfPresent(EdurehabTimeSlotDO::getDurationMinutes, reqVO.getDurationMinutes())
            .eqIfPresent(EdurehabTimeSlotDO::getOrgCode, reqVO.getOrgCode())
        .orderByAsc(EdurehabTimeSlotDO::getStartTime));    }


    }
