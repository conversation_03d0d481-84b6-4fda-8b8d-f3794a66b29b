package com.rs.module.acp.dao.wb;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.wb.vo.HallInfoConfigListReqVO;
import com.rs.module.acp.controller.admin.wb.vo.HallInfoConfigPageReqVO;
import com.rs.module.acp.entity.wb.HallInfoConfigDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-服务大厅信息发布配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface HallInfoConfigDao extends IBaseDao<HallInfoConfigDO> {


    default PageResult<HallInfoConfigDO> selectPage(HallInfoConfigPageReqVO reqVO) {
        Page<HallInfoConfigDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<HallInfoConfigDO> wrapper = new LambdaQueryWrapperX<HallInfoConfigDO>()
            .eqIfPresent(HallInfoConfigDO::getIsCarousel, reqVO.getIsCarousel())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(HallInfoConfigDO::getAddTime);
        }
        Page<HallInfoConfigDO> hallInfoConfigPage = selectPage(page, wrapper);
        return new PageResult<>(hallInfoConfigPage.getRecords(), hallInfoConfigPage.getTotal());
    }
    default List<HallInfoConfigDO> selectList(HallInfoConfigListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<HallInfoConfigDO>()
            .eqIfPresent(HallInfoConfigDO::getIsCarousel, reqVO.getIsCarousel())
        .orderByDesc(HallInfoConfigDO::getAddTime));    }


    }
