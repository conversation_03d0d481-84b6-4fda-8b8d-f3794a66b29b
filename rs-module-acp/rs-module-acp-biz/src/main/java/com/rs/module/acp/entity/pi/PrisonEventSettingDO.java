package com.rs.module.acp.entity.pi;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-巡视管控-所警情管理-报警联动设置(所情来源) DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_prison_event_setting")
@KeySequence("acp_pi_prison_event_setting_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_prison_event_setting")
public class PrisonEventSettingDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 所请来源 字典：C_JQLY
     */
    private String eventSrc;
    /**
     * 所情来源-中文
     */
    private String name;
    /**
     * 所情等级 字典：C_JQDJ
     */
    private Short eventLevel;
    /**
     * 是否启用 1启用 0禁用
     */
    private Short enabled;
    /**
     * 处理时效（分钟）
     */
    private Short processingDuration;
    /**
     * 提示音 字典：PRISON_WARNING_TONE
     */
    private String tone;
    /**
     * 可选择的设置。逗号分隔	字典：PRISON_EVENT_SETTING_ITEM
     */
    private String optionalSettings;
    /**
     * 联动配置。逗号分隔。optional_settings
     */
    private String settings;
    /**
     * 扩展字段1
     */
    private String extendOne;
    /**
     * 所情事件类型ID
     */
    private String typeId;

}
