package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.module.acp.entity.wb.LawyerMeetingCompanionDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.FamilyMeetingCompanionDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.FamilyMeetingCompanionDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-家属会见同行登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FamilyMeetingCompanionServiceImpl extends BaseServiceImpl<FamilyMeetingCompanionDao, FamilyMeetingCompanionDO> implements FamilyMeetingCompanionService {

    @Resource
    private FamilyMeetingCompanionDao familyMeetingCompanionDao;

    @Autowired
    private WbCommonService wbCommonService;

    @Override
    public String createFamilyMeetingCompanion(FamilyMeetingCompanionSaveReqVO createReqVO) {
        // 插入
        FamilyMeetingCompanionDO familyMeetingCompanion = BeanUtils.toBean(createReqVO, FamilyMeetingCompanionDO.class);
        familyMeetingCompanionDao.insert(familyMeetingCompanion);
        // 返回
        return familyMeetingCompanion.getId();
    }

    @Override
    public void updateFamilyMeetingCompanion(FamilyMeetingCompanionSaveReqVO updateReqVO) {
        // 校验存在
        validateFamilyMeetingCompanionExists(updateReqVO.getId());
        // 更新
        FamilyMeetingCompanionDO updateObj = BeanUtils.toBean(updateReqVO, FamilyMeetingCompanionDO.class);
        familyMeetingCompanionDao.updateById(updateObj);
    }

    @Override
    public void deleteFamilyMeetingCompanion(String id) {
        // 校验存在
        validateFamilyMeetingCompanionExists(id);
        // 删除
        familyMeetingCompanionDao.deleteById(id);
    }

    private void validateFamilyMeetingCompanionExists(String id) {
        if (familyMeetingCompanionDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-家属会见同行登记数据不存在");
        }
    }

    @Override
    public FamilyMeetingCompanionDO getFamilyMeetingCompanion(String id) {
        return familyMeetingCompanionDao.selectById(id);
    }

    @Override
    public PageResult<FamilyMeetingCompanionDO> getFamilyMeetingCompanionPage(FamilyMeetingCompanionPageReqVO pageReqVO) {
        return familyMeetingCompanionDao.selectPage(pageReqVO);
    }

    @Override
    public List<FamilyMeetingCompanionDO> getFamilyMeetingCompanionList(FamilyMeetingCompanionListReqVO listReqVO) {
        return familyMeetingCompanionDao.selectList(listReqVO);
    }


    @Override
    public List<FamilyMeetingCompanionRespVO> getCompanionListByFamilyMeetingId(String familyMeetingId) {
        LambdaQueryWrapper<FamilyMeetingCompanionDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(FamilyMeetingCompanionDO::getName,FamilyMeetingCompanionDO::getCompanionType,
                FamilyMeetingCompanionDO::getGender,FamilyMeetingCompanionDO::getIdCard,FamilyMeetingCompanionDO::getAttachmentUrl);
        List<FamilyMeetingCompanionDO> companionDOList = list(lambdaQueryWrapper);
        if(CollectionUtil.isEmpty(companionDOList)){
            return new ArrayList<>();
        }
        for(FamilyMeetingCompanionDO companionDO:companionDOList){
            if(ObjectUtil.isNotEmpty(companionDO.getAttachmentUrl())){
                companionDO.setAttachmentUrl(wbCommonService.getFile(companionDO.getAttachmentUrl()));
            }
        }
        return BeanUtils.toBean(companionDOList,FamilyMeetingCompanionRespVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCompanionListByFamilyMeetingId(List<FamilyMeetingCompanionSaveReqVO> companionList, String familyMeetingId) {

        List<FamilyMeetingCompanionDO> familyMeetingCompanionDOList = BeanUtils.toBean(companionList,FamilyMeetingCompanionDO.class);
        for(FamilyMeetingCompanionDO companionDO:familyMeetingCompanionDOList){
            companionDO.setFamilyMeetingId(familyMeetingId);
            if(ObjectUtil.isNotEmpty(companionDO.getAttachmentUrl())){
                String attachmentUrl = wbCommonService.saveFile(null,companionDO.getAttachmentUrl());
                companionDO.setAttachmentUrl(attachmentUrl);
            }
        }
        return saveBatch(familyMeetingCompanionDOList);
    }
}
