package com.rs.module.acp.entity.gj;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-违规申诉 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_violation_appeal")
@KeySequence("acp_gj_violation_appeal_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_violation_appeal")
public class ViolationAppealDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 禁闭监室id
     */
    private String violationRecordId;
    /**
     * 申诉理由
     */
    private String appealReason;
    /**
     * 申诉要求
     */
    private String appealRequirement;
    /**
     * 状态
     */
    private String status;
    /**
     * 审批人身份证号
     */
    private String approverSfzh;
    /**
     * 审批人姓名
     */
    private String approverXm;
    /**
     * 审批时间
     */
    private Date approverTime;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 审核意见
     */
    private String approvalComments;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 违规类型
     */
    @TableField(exist = false)
    private String violationContent;
    /**
     * 违规情况
     */
    @TableField(exist = false)
    private String pushContent;

    @TableField(exist = false)
    private String jgrybm;

    @TableField(exist = false)
    private String jgryxm;

    @TableField(exist = false)
    private String roomId;

    @TableField(exist = false)
    private String roomName;

    @TableField(exist = false)
    private String sszt;

    @TableField(exist = false)
    private Date violationTime;

}
