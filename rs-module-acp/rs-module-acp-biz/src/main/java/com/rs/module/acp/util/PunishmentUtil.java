package com.rs.module.acp.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.spring.SpringUtils;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentSaveExternalInfoVO;
import com.rs.module.acp.entity.gj.AloneImprisonDO;
import com.rs.module.acp.entity.gj.PunishmentDO;
import com.rs.module.acp.entity.gj.confinement.ConfinementRegDO;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.concurrent.CompletableFuture;

@Slf4j
public class PunishmentUtil {
    public static PunishmentSaveExternalInfoVO convertPunishmentVO(AloneImprisonDO regDO,String jgryxm){
        PunishmentSaveExternalInfoVO punishmentSaveExternalInfoVO =new PunishmentSaveExternalInfoVO();
        punishmentSaveExternalInfoVO.setJgrybm(regDO.getJgrybm());
        punishmentSaveExternalInfoVO.setJgryxm(jgryxm);
        punishmentSaveExternalInfoVO.setReasonName("单独关押");
        punishmentSaveExternalInfoVO.setStartDate(regDO.getStartTime());
        punishmentSaveExternalInfoVO.setEndDate(regDO.getEndTime());
        //punishmentSaveExternalInfoVO.setMeasuresName(regDO.getPunishmentMeasures());
       /* if(regDO.getRegisterReason().equals("04")){
            punishmentSaveExternalInfoVO.setReasonName(regDO.getSpecificReason());
        }else{
            punishmentSaveExternalInfoVO.setReasonName(DicUtils.translate( "ZD_DDGY_DJYY", regDO.getRegisterReason()));
        }*/

        punishmentSaveExternalInfoVO.setMeasures(regDO.getPunishmentMeasures());
        punishmentSaveExternalInfoVO.setAddTime(regDO.getAddTime());
        punishmentSaveExternalInfoVO.setAddUser(regDO.getAddUser());
        punishmentSaveExternalInfoVO.setRemark(regDO.getRemarks());
        punishmentSaveExternalInfoVO.setAddUserName(regDO.getAddUserName());
        punishmentSaveExternalInfoVO.setId(regDO.getId());
        punishmentSaveExternalInfoVO.setDataSources("1");
        return punishmentSaveExternalInfoVO;
    }
    public static PunishmentSaveExternalInfoVO convertPunishmentVO(ConfinementRegDO regDO, String jgryxm){
        PunishmentSaveExternalInfoVO punishmentSaveExternalInfoVO =new PunishmentSaveExternalInfoVO();
        punishmentSaveExternalInfoVO.setJgrybm(regDO.getJgrybm());
        punishmentSaveExternalInfoVO.setJgryxm(jgryxm);
        punishmentSaveExternalInfoVO.setReasonName("禁闭");
        punishmentSaveExternalInfoVO.setStartDate(regDO.getConfinementStartDate());
        punishmentSaveExternalInfoVO.setEndDate(regDO.getConfinementEndDate());
        //punishmentSaveExternalInfoVO.setMeasuresName(regDO.getPunishmentMeasures());
        punishmentSaveExternalInfoVO.setMeasures(regDO.getPunishmentMeasures());
        punishmentSaveExternalInfoVO.setAddTime(regDO.getAddTime());
        punishmentSaveExternalInfoVO.setAddUser(regDO.getAddUser());
        punishmentSaveExternalInfoVO.setRemark(regDO.getRemarks());
        punishmentSaveExternalInfoVO.setAddUserName(regDO.getAddUserName());
        punishmentSaveExternalInfoVO.setId(regDO.getId());
        punishmentSaveExternalInfoVO.setDataSources("1");
        return punishmentSaveExternalInfoVO;
    }


    /**
     * 发送禁止购物给采买系统
     * <AUTHOR>
     * @date 2025/7/5 10:10
     * @param [punishmentDO]
     * @return java.lang.Boolean
     */
    public static void sendNoShoppingAllowed(PunishmentDO punishmentDO){
        //异步处理， 发送禁止购物异常，不影响主流程
        CompletableFuture.runAsync( ()->{
            //02  表示禁止购物
            if(StrUtil.isNotBlank(punishmentDO.getMeasures()) && punishmentDO.getMeasures().contains("02")){
                String url = SpringUtils.getProperty("shopping.forbid.url");
                if(StrUtil.isBlank(url)){
                    return;
                }
                String timeoutStr = SpringUtils.getProperty("shopping.timeout", "10000");
                Integer timeout = Integer.parseInt( timeoutStr);
                Date endDate =  punishmentDO.getActualEndDate() != null ? punishmentDO.getEndDate() : punishmentDO.getActualEndDate();
                if(endDate != null){
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("endTime", DateUtil.formatDateTime(endDate));
                    jsonObject.put("jgrybm", punishmentDO.getJgrybm());
                    jsonObject.put("orgCode", punishmentDO.getOrgCode());
                    jsonObject.put("orgName", punishmentDO.getOrgName());
                    String json = HttpUtil.post(url, jsonObject.toJSONString(), timeout);
                    CommonResult reuslt =  JSON.parseObject(json, CommonResult.class);
                    if(!reuslt.getSuccess()){
                        log.error("发送禁止购物异常！ {}" , JSON.toJSONString(reuslt));
                    }
                    //{
                    //    "code": 0,
                    //    "returnCode": 0,
                    //    "status": 200,
                    //    "data": true,
                    //    "msg": "",
                    //    "message": "",
                    //    "success": true
                    //}
                }
            }
        });


    }
}
