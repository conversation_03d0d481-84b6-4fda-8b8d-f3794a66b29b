package com.rs.module.acp.service.pi;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventBusinessListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventBusinessPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventBusinessSaveReqVO;
import com.rs.module.acp.entity.pi.PrisonEventBusinessDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-巡视管控-所情关联业务 Service 接口
 *
 * <AUTHOR>
 */
public interface PrisonEventBusinessService extends IBaseService<PrisonEventBusinessDO>{

    /**
     * 创建实战平台-巡视管控-所情关联业务
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrisonEventBusiness(@Valid PrisonEventBusinessSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-所情关联业务
     *
     * @param updateReqVO 更新信息
     */
    void updatePrisonEventBusiness(@Valid PrisonEventBusinessSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-所情关联业务
     *
     * @param id 编号
     */
    void deletePrisonEventBusiness(String id);

    /**
     * 获得实战平台-巡视管控-所情关联业务
     *
     * @param id 编号
     * @return 实战平台-巡视管控-所情关联业务
     */
    PrisonEventBusinessDO getPrisonEventBusiness(String id);

    /**
    * 获得实战平台-巡视管控-所情关联业务分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-巡视管控-所情关联业务分页
    */
    PageResult<PrisonEventBusinessDO> getPrisonEventBusinessPage(PrisonEventBusinessPageReqVO pageReqVO);

    /**
    * 获得实战平台-巡视管控-所情关联业务列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-巡视管控-所情关联业务列表
    */
    List<PrisonEventBusinessDO> getPrisonEventBusinessList(PrisonEventBusinessListReqVO listReqVO);

    /**
     * 保存所情关联业务列表
     * @param saveReqVOList
     * @param eventId
     * @param handleId
     * @return
     */
    boolean savePrisonEventBusinessList(List<PrisonEventBusinessSaveReqVO> saveReqVOList,String eventId,String handleId);

}
