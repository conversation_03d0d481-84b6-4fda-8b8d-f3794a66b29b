package com.rs.module.acp.controller.admin.gj.vo.conflict;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-社会矛盾化解登记 Response VO")
@Data
public class ConflictRegRespVO implements TransPojo {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("数据来源：1:实战平台，2：仓外屏, 3：仓内屏")
    private String dataSources;
    @ApiModelProperty("发起人（通过数据来源区分，民警发起记录民警身份证号，被拘留人员记录：）")
    private String initiator;
    @ApiModelProperty("发起人姓名（记录发起人，通过数据来源区分是民警发起还是被拘留人员发起）")
    private String initiatorXm;
    @ApiModelProperty("流程实例ID")
    private String actInstId;
    @ApiModelProperty("事件编号")
    private String eventCode;
    @ApiModelProperty("事件名称")
    private String eventName;
    @ApiModelProperty("矛盾等级（01：普通矛盾，02：重大、疑难矛盾）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_MDDJ")
    private String conflictLv;
    @ApiModelProperty("调解次数")
    private Integer mediationCnt;
    @ApiModelProperty("调解方式（01：单场调解，02：个别协商，03：庭式调解）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_TJFS")
    private String mediationMode;
    @ApiModelProperty("涉案金额")
    private BigDecimal involvedAmt;
    @ApiModelProperty("矛盾类别（01：非法上访、02：经济纠纷（补偿赔偿债务）、03：家庭矛盾、04：青少年违法、05：民间打架斗殴、06：民事纠纷、07：非法维权、08：对执法机关不满案件、09：迷信活动、10：“六失一偏”人员、11：其他类")
    @Trans(type = TransType.DICTIONARY, key = "ZD_MDLB")
    private String conflictType;
    @ApiModelProperty("当事人或单位基本情况")
    private String partiesBasicInfo;
    @ApiModelProperty("矛盾基本情况")
    private String conflictBasicInfo;
    @ApiModelProperty("调解时间")
    private Date mediationTime;
    @ApiModelProperty("调解民警身份证号")
    private String mediationPoliceSfzh;
    @ApiModelProperty("调解民警姓名")
    private String mediationPoliceXm;
    @ApiModelProperty("状态（01:已登记、02：待审批（所领导审批）、03: 审批不通过、04：调解中、05：调解完成、06：待确认、07：被退回）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_SHMDHJDJZT")
    private String regStatus;
    @ApiModelProperty("确认状态（0：待确认、1：已确认）")
    private String confirmStatus;
    @ApiModelProperty("终端确认经办人")
    private String confirmOperatorSfzh;
    @ApiModelProperty("终端确认经办人姓名")
    private String confirmOperatorXm;
    @ApiModelProperty("终端确认经办时间")
    private Date confirmOperatorTime;
    @ApiModelProperty("登记经办人")
    private String regOperatorSfzh;
    @ApiModelProperty("登记经办人姓名")
    private String regOperatorXm;
    @ApiModelProperty("经办时间")
    private Date addTime;
    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;
    @ApiModelProperty("审批人姓名")
    private String approverXm;
    @ApiModelProperty("审批时间")
    private Date approverTime;
    @ApiModelProperty("审批结果")
    private String approvalResult;
    @ApiModelProperty("审核意见")
    private String approvalComments;
    @ApiModelProperty("是否已评价（0：未评价，1：已评价）")
    private Integer isRated;
    @ApiModelProperty("满意度评分（1：满意：0：不满意）")
    private Integer satisfactionScore;
    @ApiModelProperty("登记人员")
    private List<ConflictPrisonerRespVO> prisonerList;
    @ApiModelProperty("调解信息")
    private List<ConflictMediationRespVO> mediationList;
    @ApiModelProperty("回访信息")
    private ConflictFollowupRespVO followup;
    @ApiModelProperty("轨迹记录")
    private List<ConflictRegTraceVO> traceVOS;

    public static class ConflictRegTraceVO {

        public ConflictRegTraceVO(String traceName, List<Date> traceTimes) {
            this.traceName = traceName;
            this.traceTimes = traceTimes;
        }

        private String traceName;
        private List<Date> traceTimes;

        public String getTraceName() {
            return traceName;
        }

        public void setTraceName(String traceName) {
            this.traceName = traceName;
        }

        public List<Date> getTraceTimes() {
            return traceTimes;
        }

        public void setTraceTimes(List<Date> traceTimes) {
            this.traceTimes = traceTimes;
        }
    }

}
