package com.rs.module.acp.service.sys;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.sys.vo.*;
import com.rs.module.acp.entity.sys.VbConfigTimedDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-语音播报-定时配置 Service 接口
 *
 * <AUTHOR>
 */
public interface VbConfigTimedService extends IBaseService<VbConfigTimedDO>{

    /**
     * 创建实战平台-语音播报-定时配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createVbConfigTimed(@Valid VbConfigTimedSaveReqVO createReqVO);

    /**
     * 更新实战平台-语音播报-定时配置
     *
     * @param updateReqVO 更新信息
     */
    void updateVbConfigTimed(@Valid VbConfigTimedSaveReqVO updateReqVO);

    /**
     * 删除实战平台-语音播报-定时配置
     *
     * @param id 编号
     */
    void deleteVbConfigTimed(String id);

    /**
     * 获得实战平台-语音播报-定时配置
     *
     * @param id 编号
     * @return 实战平台-语音播报-定时配置
     */
    VbConfigTimedDO getVbConfigTimed(String id);

    /**
    * 获得实战平台-语音播报-定时配置分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-语音播报-定时配置分页
    */
    PageResult<VbConfigTimedDO> getVbConfigTimedPage(VbConfigTimedPageReqVO pageReqVO);

    /**
    * 获得实战平台-语音播报-定时配置列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-语音播报-定时配置列表
    */
    List<VbConfigTimedDO> getVbConfigTimedList(VbConfigTimedListReqVO listReqVO);

    /**
     * 获取可用的即时播报配置
     * @param orgCode String 机构代码
     * @return List<VbConfigTimedDO>
     */
    public List<VbConfigTimedDO> getEnabledTimedConfig(String orgCode);
}
