package com.rs.module.acp.controller.admin.db;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.dbSocialRelationsDO;
import com.rs.module.acp.service.db.dbSocialRelationsService;

@Api(tags = "实战平台-收押业务-社会关系")
@RestController
@RequestMapping("/acp/db/dbSocialRelations")
@Validated
public class dbSocialRelationsController {

    @Resource
    private dbSocialRelationsService dbSocialRelationsService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-收押业务-社会关系")
    public CommonResult<String> createdbSocialRelations(@Valid @RequestBody dbSocialRelationsSaveReqVO createReqVO) {
        //新增操作，不用传ID，判断是否有传，如果有传了则提示不用传
        if(createReqVO.getId() != null){
            return CommonResult.error("新增操作，不用传ID字段！");
        }
        return success(dbSocialRelationsService.createdbSocialRelations(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-收押业务-社会关系")
    public CommonResult<Boolean> updatedbSocialRelations(@Valid @RequestBody dbSocialRelationsSaveReqVO updateReqVO) {
        dbSocialRelationsService.updatedbSocialRelations(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-收押业务-社会关系")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deletedbSocialRelations(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           dbSocialRelationsService.deletedbSocialRelations(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-收押业务-社会关系")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<dbSocialRelationsRespVO> getdbSocialRelations(@RequestParam("id") String id) {
        dbSocialRelationsDO dbSocialRelations = dbSocialRelationsService.getdbSocialRelations(id);
        return success(BeanUtils.toBean(dbSocialRelations, dbSocialRelationsRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-收押业务-社会关系分页")
    public CommonResult<PageResult<dbSocialRelationsRespVO>> getdbSocialRelationsPage(@Valid @RequestBody dbSocialRelationsPageReqVO pageReqVO) {
        PageResult<dbSocialRelationsDO> pageResult = dbSocialRelationsService.getdbSocialRelationsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, dbSocialRelationsRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-收押业务-社会关系列表")
    public CommonResult<List<dbSocialRelationsRespVO>> getdbSocialRelationsList(@Valid @RequestBody dbSocialRelationsListReqVO listReqVO) {
        List<dbSocialRelationsDO> list = dbSocialRelationsService.getdbSocialRelationsList(listReqVO);
        return success(BeanUtils.toBean(list, dbSocialRelationsRespVO.class));
    }
}
