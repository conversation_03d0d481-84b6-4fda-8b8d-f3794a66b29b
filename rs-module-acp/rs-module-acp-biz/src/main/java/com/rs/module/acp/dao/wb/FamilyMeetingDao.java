package com.rs.module.acp.dao.wb;

import java.util.*;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.FamilyMeetingDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
 * 实战平台-窗口业务-家属会见登记 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface FamilyMeetingDao extends IBaseDao<FamilyMeetingDO> {


    default PageResult<FamilyMeetingDO> selectPage(FamilyMeetingPageReqVO reqVO) {
        Page<FamilyMeetingDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<FamilyMeetingDO> wrapper = new LambdaQueryWrapperX<FamilyMeetingDO>()
                .eqIfPresent(FamilyMeetingDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember1Id, reqVO.getFamilyMember1Id())
                .likeIfPresent(FamilyMeetingDO::getFamilyMember1Name, reqVO.getFamilyMember1Name())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember1Gender, reqVO.getFamilyMember1Gender())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember1IdType, reqVO.getFamilyMember1IdType())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember1IdNumber, reqVO.getFamilyMember1IdNumber())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember1Relationship, reqVO.getFamilyMember1Relationship())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember1Contact, reqVO.getFamilyMember1Contact())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember1WorkUnit, reqVO.getFamilyMember1WorkUnit())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember1Address, reqVO.getFamilyMember1Address())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember2Id, reqVO.getFamilyMember2Id())
                .likeIfPresent(FamilyMeetingDO::getFamilyMember2Name, reqVO.getFamilyMember2Name())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember2Gender, reqVO.getFamilyMember2Gender())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember2IdType, reqVO.getFamilyMember2IdType())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember2IdNumber, reqVO.getFamilyMember2IdNumber())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember2Relationship, reqVO.getFamilyMember2Relationship())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember2Contact, reqVO.getFamilyMember2Contact())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember2WorkUnit, reqVO.getFamilyMember2WorkUnit())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember2Address, reqVO.getFamilyMember2Address())
                .eqIfPresent(FamilyMeetingDO::getApprovalDocumentNumber, reqVO.getApprovalDocumentNumber())
                .eqIfPresent(FamilyMeetingDO::getApprovalAttachmentPath, reqVO.getApprovalAttachmentPath())
                .eqIfPresent(FamilyMeetingDO::getRemarks, reqVO.getRemarks())
                .eqIfPresent(FamilyMeetingDO::getStatus, reqVO.getStatus())
                .eqIfPresent(FamilyMeetingDO::getRoomId, reqVO.getRoomId())
                .betweenIfPresent(FamilyMeetingDO::getAssignmentRoomTime, reqVO.getAssignmentRoomTime())
                .eqIfPresent(FamilyMeetingDO::getAssignmentPoliceSfzh, reqVO.getAssignmentPoliceSfzh())
                .eqIfPresent(FamilyMeetingDO::getAssignmentPolice, reqVO.getAssignmentPolice())
                .betweenIfPresent(FamilyMeetingDO::getCheckInTime, reqVO.getCheckInTime())
                .eqIfPresent(FamilyMeetingDO::getCheckInPoliceSfzh, reqVO.getCheckInPoliceSfzh())
                .eqIfPresent(FamilyMeetingDO::getCheckInPolice, reqVO.getCheckInPolice())
                .eqIfPresent(FamilyMeetingDO::getEscortingPoliceSfzh, reqVO.getEscortingPoliceSfzh())
                .eqIfPresent(FamilyMeetingDO::getEscortingPolice, reqVO.getEscortingPolice())
                .betweenIfPresent(FamilyMeetingDO::getEscortingTime, reqVO.getEscortingTime())
                .eqIfPresent(FamilyMeetingDO::getEscortingOperatorSfzh, reqVO.getEscortingOperatorSfzh())
                .eqIfPresent(FamilyMeetingDO::getEscortingOperator, reqVO.getEscortingOperator())
                .betweenIfPresent(FamilyMeetingDO::getEscortingOperatorTime, reqVO.getEscortingOperatorTime())
                .eqIfPresent(FamilyMeetingDO::getInspectionResult, reqVO.getInspectionResult())
                .betweenIfPresent(FamilyMeetingDO::getInspectionTime, reqVO.getInspectionTime())
                .eqIfPresent(FamilyMeetingDO::getInspectionSituation, reqVO.getInspectionSituation())
                .eqIfPresent(FamilyMeetingDO::getInspectorSfzh, reqVO.getInspectorSfzh())
                .eqIfPresent(FamilyMeetingDO::getInspector, reqVO.getInspector())
                .betweenIfPresent(FamilyMeetingDO::getMeetingStartTime, reqVO.getMeetingStartTime())
                .betweenIfPresent(FamilyMeetingDO::getMeetingEndTime, reqVO.getMeetingEndTime())
                .eqIfPresent(FamilyMeetingDO::getReturnInspectorSfzh, reqVO.getReturnInspectorSfzh())
                .eqIfPresent(FamilyMeetingDO::getReturnInspector, reqVO.getReturnInspector())
                .betweenIfPresent(FamilyMeetingDO::getReturnInspectionTime, reqVO.getReturnInspectionTime())
                .eqIfPresent(FamilyMeetingDO::getReturnInspectionResult, reqVO.getReturnInspectionResult())
                .betweenIfPresent(FamilyMeetingDO::getReturnTime, reqVO.getReturnTime())
                .eqIfPresent(FamilyMeetingDO::getReturnPoliceSfzh, reqVO.getReturnPoliceSfzh())
                .eqIfPresent(FamilyMeetingDO::getReturnPolice, reqVO.getReturnPolice())
                .eqIfPresent(FamilyMeetingDO::getReturnOperatorSfzh, reqVO.getReturnOperatorSfzh())
                .eqIfPresent(FamilyMeetingDO::getReturnOperator, reqVO.getReturnOperator())
                .betweenIfPresent(FamilyMeetingDO::getReturnOperatorTime, reqVO.getReturnOperatorTime())
                .betweenIfPresent(FamilyMeetingDO::getApplyMeetingStartTime, reqVO.getApplyMeetingStartTime())
                .betweenIfPresent(FamilyMeetingDO::getApplyMeetingEndTime, reqVO.getApplyMeetingEndTime())
                .eqIfPresent(FamilyMeetingDO::getReturnProhibitedItems, reqVO.getReturnProhibitedItems())
                .eqIfPresent(FamilyMeetingDO::getReturnPhysicalExam, reqVO.getReturnPhysicalExam())
                .eqIfPresent(FamilyMeetingDO::getReturnAbnormalSituations, reqVO.getReturnAbnormalSituations())
                .eqIfPresent(FamilyMeetingDO::getProhibitedItems, reqVO.getProhibitedItems())
                .eqIfPresent(FamilyMeetingDO::getPhysicalExam, reqVO.getPhysicalExam())
                .eqIfPresent(FamilyMeetingDO::getAbnormalSituations, reqVO.getAbnormalSituations())
                .eqIfPresent(FamilyMeetingDO::getProhibitedItemsImgUrl, reqVO.getProhibitedItemsImgUrl())
                .eqIfPresent(FamilyMeetingDO::getPhysicalExamImgUrl, reqVO.getPhysicalExamImgUrl())
                .eqIfPresent(FamilyMeetingDO::getAbnormalSituationsImgUrl, reqVO.getAbnormalSituationsImgUrl())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember3Id, reqVO.getFamilyMember3Id())
                .likeIfPresent(FamilyMeetingDO::getFamilyMember3Name, reqVO.getFamilyMember3Name())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember3Gender, reqVO.getFamilyMember3Gender())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember3IdType, reqVO.getFamilyMember3IdType())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember3IdNumber, reqVO.getFamilyMember3IdNumber())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember3Relationship, reqVO.getFamilyMember3Relationship())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember3Contact, reqVO.getFamilyMember3Contact())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember3WorkUnit, reqVO.getFamilyMember3WorkUnit())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember3Address, reqVO.getFamilyMember3Address())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember1Occupation, reqVO.getFamilyMember1Occupation())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember2Occupation, reqVO.getFamilyMember2Occupation())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember3Occupation, reqVO.getFamilyMember3Occupation())
                ;
        if(reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        }else {
            wrapper.orderByDesc(FamilyMeetingDO::getAddTime);
        }
        Page<FamilyMeetingDO> familyMeetingPage = selectPage(page, wrapper);
        return new PageResult<>(familyMeetingPage.getRecords(), familyMeetingPage.getTotal());
    }
    default List<FamilyMeetingDO> selectList(FamilyMeetingListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<FamilyMeetingDO>()
                .eqIfPresent(FamilyMeetingDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember1Id, reqVO.getFamilyMember1Id())
                .likeIfPresent(FamilyMeetingDO::getFamilyMember1Name, reqVO.getFamilyMember1Name())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember1Gender, reqVO.getFamilyMember1Gender())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember1IdType, reqVO.getFamilyMember1IdType())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember1IdNumber, reqVO.getFamilyMember1IdNumber())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember1Relationship, reqVO.getFamilyMember1Relationship())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember1Contact, reqVO.getFamilyMember1Contact())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember1WorkUnit, reqVO.getFamilyMember1WorkUnit())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember1Address, reqVO.getFamilyMember1Address())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember2Id, reqVO.getFamilyMember2Id())
                .likeIfPresent(FamilyMeetingDO::getFamilyMember2Name, reqVO.getFamilyMember2Name())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember2Gender, reqVO.getFamilyMember2Gender())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember2IdType, reqVO.getFamilyMember2IdType())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember2IdNumber, reqVO.getFamilyMember2IdNumber())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember2Relationship, reqVO.getFamilyMember2Relationship())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember2Contact, reqVO.getFamilyMember2Contact())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember2WorkUnit, reqVO.getFamilyMember2WorkUnit())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember2Address, reqVO.getFamilyMember2Address())
                .eqIfPresent(FamilyMeetingDO::getApprovalDocumentNumber, reqVO.getApprovalDocumentNumber())
                .eqIfPresent(FamilyMeetingDO::getApprovalAttachmentPath, reqVO.getApprovalAttachmentPath())
                .eqIfPresent(FamilyMeetingDO::getRemarks, reqVO.getRemarks())
                .eqIfPresent(FamilyMeetingDO::getStatus, reqVO.getStatus())
                .eqIfPresent(FamilyMeetingDO::getRoomId, reqVO.getRoomId())
                .betweenIfPresent(FamilyMeetingDO::getAssignmentRoomTime, reqVO.getAssignmentRoomTime())
                .eqIfPresent(FamilyMeetingDO::getAssignmentPoliceSfzh, reqVO.getAssignmentPoliceSfzh())
                .eqIfPresent(FamilyMeetingDO::getAssignmentPolice, reqVO.getAssignmentPolice())
                .betweenIfPresent(FamilyMeetingDO::getCheckInTime, reqVO.getCheckInTime())
                .eqIfPresent(FamilyMeetingDO::getCheckInPoliceSfzh, reqVO.getCheckInPoliceSfzh())
                .eqIfPresent(FamilyMeetingDO::getCheckInPolice, reqVO.getCheckInPolice())
                .eqIfPresent(FamilyMeetingDO::getEscortingPoliceSfzh, reqVO.getEscortingPoliceSfzh())
                .eqIfPresent(FamilyMeetingDO::getEscortingPolice, reqVO.getEscortingPolice())
                .betweenIfPresent(FamilyMeetingDO::getEscortingTime, reqVO.getEscortingTime())
                .eqIfPresent(FamilyMeetingDO::getEscortingOperatorSfzh, reqVO.getEscortingOperatorSfzh())
                .eqIfPresent(FamilyMeetingDO::getEscortingOperator, reqVO.getEscortingOperator())
                .betweenIfPresent(FamilyMeetingDO::getEscortingOperatorTime, reqVO.getEscortingOperatorTime())
                .eqIfPresent(FamilyMeetingDO::getInspectionResult, reqVO.getInspectionResult())
                .betweenIfPresent(FamilyMeetingDO::getInspectionTime, reqVO.getInspectionTime())
                .eqIfPresent(FamilyMeetingDO::getInspectionSituation, reqVO.getInspectionSituation())
                .eqIfPresent(FamilyMeetingDO::getInspectorSfzh, reqVO.getInspectorSfzh())
                .eqIfPresent(FamilyMeetingDO::getInspector, reqVO.getInspector())
                .betweenIfPresent(FamilyMeetingDO::getMeetingStartTime, reqVO.getMeetingStartTime())
                .betweenIfPresent(FamilyMeetingDO::getMeetingEndTime, reqVO.getMeetingEndTime())
                .eqIfPresent(FamilyMeetingDO::getReturnInspectorSfzh, reqVO.getReturnInspectorSfzh())
                .eqIfPresent(FamilyMeetingDO::getReturnInspector, reqVO.getReturnInspector())
                .betweenIfPresent(FamilyMeetingDO::getReturnInspectionTime, reqVO.getReturnInspectionTime())
                .eqIfPresent(FamilyMeetingDO::getReturnInspectionResult, reqVO.getReturnInspectionResult())
                .betweenIfPresent(FamilyMeetingDO::getReturnTime, reqVO.getReturnTime())
                .eqIfPresent(FamilyMeetingDO::getReturnPoliceSfzh, reqVO.getReturnPoliceSfzh())
                .eqIfPresent(FamilyMeetingDO::getReturnPolice, reqVO.getReturnPolice())
                .eqIfPresent(FamilyMeetingDO::getReturnOperatorSfzh, reqVO.getReturnOperatorSfzh())
                .eqIfPresent(FamilyMeetingDO::getReturnOperator, reqVO.getReturnOperator())
                .betweenIfPresent(FamilyMeetingDO::getReturnOperatorTime, reqVO.getReturnOperatorTime())
                .betweenIfPresent(FamilyMeetingDO::getApplyMeetingStartTime, reqVO.getApplyMeetingStartTime())
                .betweenIfPresent(FamilyMeetingDO::getApplyMeetingEndTime, reqVO.getApplyMeetingEndTime())
                .eqIfPresent(FamilyMeetingDO::getReturnProhibitedItems, reqVO.getReturnProhibitedItems())
                .eqIfPresent(FamilyMeetingDO::getReturnPhysicalExam, reqVO.getReturnPhysicalExam())
                .eqIfPresent(FamilyMeetingDO::getReturnAbnormalSituations, reqVO.getReturnAbnormalSituations())
                .eqIfPresent(FamilyMeetingDO::getProhibitedItems, reqVO.getProhibitedItems())
                .eqIfPresent(FamilyMeetingDO::getPhysicalExam, reqVO.getPhysicalExam())
                .eqIfPresent(FamilyMeetingDO::getAbnormalSituations, reqVO.getAbnormalSituations())
                .eqIfPresent(FamilyMeetingDO::getProhibitedItemsImgUrl, reqVO.getProhibitedItemsImgUrl())
                .eqIfPresent(FamilyMeetingDO::getPhysicalExamImgUrl, reqVO.getPhysicalExamImgUrl())
                .eqIfPresent(FamilyMeetingDO::getAbnormalSituationsImgUrl, reqVO.getAbnormalSituationsImgUrl())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember3Id, reqVO.getFamilyMember3Id())
                .likeIfPresent(FamilyMeetingDO::getFamilyMember3Name, reqVO.getFamilyMember3Name())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember3Gender, reqVO.getFamilyMember3Gender())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember3IdType, reqVO.getFamilyMember3IdType())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember3IdNumber, reqVO.getFamilyMember3IdNumber())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember3Relationship, reqVO.getFamilyMember3Relationship())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember3Contact, reqVO.getFamilyMember3Contact())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember3WorkUnit, reqVO.getFamilyMember3WorkUnit())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember3Address, reqVO.getFamilyMember3Address())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember1Occupation, reqVO.getFamilyMember1Occupation())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember2Occupation, reqVO.getFamilyMember2Occupation())
                .eqIfPresent(FamilyMeetingDO::getFamilyMember3Occupation, reqVO.getFamilyMember3Occupation())
                .orderByDesc(FamilyMeetingDO::getAddTime));    }


    JSONObject getOrgTypeByOrgCode(String orgCode);

    List<JSONObject> getOnSiteNumbering(@Param("type") String type, @Param("orgCode") String orgCode);

}
