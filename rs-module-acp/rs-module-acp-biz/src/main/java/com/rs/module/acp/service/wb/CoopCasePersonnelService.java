package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.CoopCasePersonnelDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-协同办案人员 Service 接口
 *
 * <AUTHOR>
 */
public interface CoopCasePersonnelService extends IBaseService<CoopCasePersonnelDO>{

    /**
     * 创建实战平台-窗口业务-协同办案人员
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCoopCasePersonnel(@Valid CoopCasePersonnelSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-协同办案人员
     *
     * @param updateReqVO 更新信息
     */
    void updateCoopCasePersonnel(@Valid CoopCasePersonnelSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-协同办案人员
     *
     * @param id 编号
     */
    void deleteCoopCasePersonnel(String id);

    /**
     * 获得实战平台-窗口业务-协同办案人员
     *
     * @param id 编号
     * @return 实战平台-窗口业务-协同办案人员
     */
    CoopCasePersonnelDO getCoopCasePersonnel(String id);

    /**
     * 获得实战平台-窗口业务-保存协同办案人员
     *
     * @param coopCasePersonnelList 创建信息
     * @param targetId 业务对象ID
     * @return 实战平台-窗口业务-协同办案人员列表
     */
    boolean saveCoopCasePersonnelList(List<CoopCasePersonnelSaveReqVO> coopCasePersonnelList,String targetId);

    /**
     * 获得实战平台-窗口业务-保存协同办案人员
     *
     * @param targetId 业务对象ID
     * @return 实战平台-窗口业务-协同办案人员列表
     */
    List<CoopCasePersonnelRespVO> getListByTargetId(String targetId);
}
