package com.rs.module.acp.dao.wb;

import java.util.*;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.LawyerPrisonerDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
 * 实战平台-窗口业务-律师关联被监管人员 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface LawyerPrisonerDao extends IBaseDao<LawyerPrisonerDO> {


    default PageResult<LawyerPrisonerDO> selectPage(LawyerPrisonerPageReqVO reqVO) {
        Page<LawyerPrisonerDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<LawyerPrisonerDO> wrapper = new LambdaQueryWrapperX<LawyerPrisonerDO>()
                .eqIfPresent(LawyerPrisonerDO::getLawyerId, reqVO.getLawyerId())
                .eqIfPresent(LawyerPrisonerDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(LawyerPrisonerDO::getEntrustType, reqVO.getEntrustType())
                .eqIfPresent(LawyerPrisonerDO::getEntrustStage, reqVO.getEntrustStage())
                .eqIfPresent(LawyerPrisonerDO::getPrincipal, reqVO.getPrincipal())
                .eqIfPresent(LawyerPrisonerDO::getPrincipalId, reqVO.getPrincipalId())
                .eqIfPresent(LawyerPrisonerDO::getPowerOfAttorneyType, reqVO.getPowerOfAttorneyType())
                .eqIfPresent(LawyerPrisonerDO::getPowerOfAttorneyUrl, reqVO.getPowerOfAttorneyUrl())
                .eqIfPresent(LawyerPrisonerDO::getLetterNumber, reqVO.getLetterNumber())
                .eqIfPresent(LawyerPrisonerDO::getMeetingApprovalAuthority, reqVO.getMeetingApprovalAuthority())
                .eqIfPresent(LawyerPrisonerDO::getStatus, reqVO.getStatus());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(LawyerPrisonerDO::getAddTime);
        }
        Page<LawyerPrisonerDO> lawyerPrisonerPage = selectPage(page, wrapper);
        return new PageResult<>(lawyerPrisonerPage.getRecords(), lawyerPrisonerPage.getTotal());
    }

    default List<LawyerPrisonerDO> selectList(LawyerPrisonerListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<LawyerPrisonerDO>()
                .eqIfPresent(LawyerPrisonerDO::getLawyerId, reqVO.getLawyerId())
                .eqIfPresent(LawyerPrisonerDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(LawyerPrisonerDO::getEntrustType, reqVO.getEntrustType())
                .eqIfPresent(LawyerPrisonerDO::getEntrustStage, reqVO.getEntrustStage())
                .eqIfPresent(LawyerPrisonerDO::getPrincipal, reqVO.getPrincipal())
                .eqIfPresent(LawyerPrisonerDO::getPrincipalId, reqVO.getPrincipalId())
                .eqIfPresent(LawyerPrisonerDO::getPowerOfAttorneyType, reqVO.getPowerOfAttorneyType())
                .eqIfPresent(LawyerPrisonerDO::getPowerOfAttorneyUrl, reqVO.getPowerOfAttorneyUrl())
                .eqIfPresent(LawyerPrisonerDO::getLetterNumber, reqVO.getLetterNumber())
                .eqIfPresent(LawyerPrisonerDO::getMeetingApprovalAuthority, reqVO.getMeetingApprovalAuthority())
                .eqIfPresent(LawyerPrisonerDO::getStatus, reqVO.getStatus())
                .orderByDesc(LawyerPrisonerDO::getAddTime));
    }

    List<LawyerPrisonerRespVO> getPrisonerListByLawyerId(String lawyerId);

    JSONObject checkJgryAssociation(String jgrybm);

    List<JSONObject> getLawyerJsonList(@Param("jgrybm") String jgrybm,@Param("laywerIdList") List<String> laywerIdList);
}
