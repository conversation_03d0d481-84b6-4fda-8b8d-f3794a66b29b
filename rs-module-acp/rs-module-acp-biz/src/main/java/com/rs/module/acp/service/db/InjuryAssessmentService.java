package com.rs.module.acp.service.db;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.InjuryAssessmentDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-收押业务-伤情鉴定 Service 接口
 *
 * <AUTHOR>
 */
public interface InjuryAssessmentService extends IBaseService<InjuryAssessmentDO>{

    /**
     * 创建实战平台-收押业务-伤情鉴定
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createInjuryAssessment(@Valid InjuryAssessmentSaveReqVO createReqVO);

    /**
     * 更新实战平台-收押业务-伤情鉴定
     *
     * @param updateReqVO 更新信息
     */
    void updateInjuryAssessment(@Valid InjuryAssessmentSaveReqVO updateReqVO);

    /**
     * 删除实战平台-收押业务-伤情鉴定
     *
     * @param id 编号
     */
    void deleteInjuryAssessment(String id);

    /**
     * 获得实战平台-收押业务-伤情鉴定
     *
     * @param id 编号
     * @return 实战平台-收押业务-伤情鉴定
     */
    InjuryAssessmentDO getInjuryAssessment(String id);

    /**
    * 获得实战平台-收押业务-伤情鉴定分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-收押业务-伤情鉴定分页
    */
    PageResult<InjuryAssessmentDO> getInjuryAssessmentPage(InjuryAssessmentPageReqVO pageReqVO);

    /**
    * 获得实战平台-收押业务-伤情鉴定列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-收押业务-伤情鉴定列表
    */
    List<InjuryAssessmentDO> getInjuryAssessmentList(InjuryAssessmentListReqVO listReqVO);


    void deleteByRybh(String rybh);

    InjuryAssessmentDO getPersonalEffectsByRybh(String rybh);
}
