package com.rs.module.acp.dao.pi;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventDisposeTemplateListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventDisposeTemplatePageReqVO;
import com.rs.module.acp.entity.pi.PrisonEventDisposeTemplateDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-巡视管控-所情处置模板 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PrisonEventDisposeTemplateDao extends IBaseDao<PrisonEventDisposeTemplateDO> {


    default PageResult<PrisonEventDisposeTemplateDO> selectPage(PrisonEventDisposeTemplatePageReqVO reqVO) {
        Page<PrisonEventDisposeTemplateDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrisonEventDisposeTemplateDO> wrapper = new LambdaQueryWrapperX<PrisonEventDisposeTemplateDO>()
            .likeIfPresent(PrisonEventDisposeTemplateDO::getTemplateName, reqVO.getTemplateName())
            .eqIfPresent(PrisonEventDisposeTemplateDO::getTemplateContent, reqVO.getTemplateContent())
            .eqIfPresent(PrisonEventDisposeTemplateDO::getOrderId, reqVO.getOrderId())
            .eqIfPresent(PrisonEventDisposeTemplateDO::getTypeId, reqVO.getTypeId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PrisonEventDisposeTemplateDO::getAddTime);
        }
        Page<PrisonEventDisposeTemplateDO> prisonEventDisposeTemplatePage = selectPage(page, wrapper);
        return new PageResult<>(prisonEventDisposeTemplatePage.getRecords(), prisonEventDisposeTemplatePage.getTotal());
    }
    default List<PrisonEventDisposeTemplateDO> selectList(PrisonEventDisposeTemplateListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrisonEventDisposeTemplateDO>()
            .likeIfPresent(PrisonEventDisposeTemplateDO::getTemplateName, reqVO.getTemplateName())
            .eqIfPresent(PrisonEventDisposeTemplateDO::getTemplateContent, reqVO.getTemplateContent())
            .eqIfPresent(PrisonEventDisposeTemplateDO::getOrderId, reqVO.getOrderId())
            .eqIfPresent(PrisonEventDisposeTemplateDO::getTypeId, reqVO.getTypeId())
        .orderByDesc(PrisonEventDisposeTemplateDO::getAddTime));    }


    }
