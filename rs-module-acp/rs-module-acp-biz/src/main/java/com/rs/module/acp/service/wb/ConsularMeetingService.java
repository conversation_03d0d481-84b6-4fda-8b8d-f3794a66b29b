package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.ConsularMeetingDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-领事会见登记 Service 接口
 *
 * <AUTHOR>
 */
public interface ConsularMeetingService extends IBaseService<ConsularMeetingDO>{

    /**
     * 创建实战平台-窗口业务-领事会见登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createConsularMeeting(@Valid ConsularMeetingSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-领事会见登记
     *
     * @param updateReqVO 更新信息
     */
    void updateConsularMeeting(@Valid ConsularMeetingSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-领事会见登记
     *
     * @param id 编号
     */
    void deleteConsularMeeting(String id);

    /**
     * 获得实战平台-窗口业务-领事会见登记
     *
     * @param id 编号
     * @return 实战平台-窗口业务-领事会见登记
     */
    ConsularMeetingDO getConsularMeeting(String id);

    /**
    * 获得实战平台-窗口业务-领事会见登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-领事会见登记分页
    */
    PageResult<ConsularMeetingDO> getConsularMeetingPage(ConsularMeetingPageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-领事会见登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-领事会见登记列表
    */
    List<ConsularMeetingDO> getConsularMeetingList(ConsularMeetingListReqVO listReqVO);

    /**
     * 获得实战平台-窗口业务-分配会见室
     *
     * @param id 编号
     * @param roomId 会见室ID
     * @return 实战平台-窗口业务-分配会见室
     */
    boolean allocationRoom(String id,String roomId);

    /**
     * 获得实战平台-窗口业务-带出安检
     *
     * @param updateReqVO
     */
    boolean escortingInspect(ConsularMeetingSaveReqVO updateReqVO);

    /**
     * 获得实战平台-窗口业务-会毕安检
     *
     * @param updateReqVO
     */
    boolean returnInspection(ConsularMeetingSaveReqVO updateReqVO);
    /**
     * 获得实战平台-窗口业务-补录
     *
     * @param updateReqVO 更新信息
     * @return
     */
    boolean additionalRecording(ConsularMeetingSaveReqVO updateReqVO);

    /**
     * 获得实战平台-窗口业务-根据监管人员编码获取监管人员历史会见记录成功
     *
     * @param jgrybm 编号
     * @param pageNo 页码
     * @param pageSize 每页多少
     * @return 实战平台-窗口业务-获取会见记录
     */
    PageResult<ConsularMeetingRespVO> getHistoryMeetingByJgrybm(String jgrybm,int pageNo,int pageSize);

    /**
     * 获得实战平台-窗口业务-领事会见签到
     *
     * @param id 业务ID
     * @param checkInTime 签到时间
     */
    boolean signIn(String id,String checkInTime);

    /**
     * 获得实战平台-窗口业务-根据领事会见登记ID获取详情
     *
     * @param id 编号
     * @return 实战平台-窗口业务-领事会见登记
     */
    ConsularMeetingRespVO getConsularMeetingById(String id);

}
