package com.rs.module.acp.controller.admin.db;

import com.rs.module.acp.controller.admin.wb.vo.WbApprovaReqVO;
import com.rs.module.base.vo.ApproveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.ServeSentenceDO;
import com.rs.module.acp.service.db.ServeSentenceService;

@Api(tags = "实战平台-收押业务-留所服刑")
@RestController
@RequestMapping("/acp/db/serveSentence")
@Validated
public class ServeSentenceController {

    @Resource
    private ServeSentenceService serveSentenceService;

    @PostMapping("/create")
    @ApiOperation(value = "实战平台-收押业务-创建留所服刑")
    @LogRecordAnnotation(bizModule = "acp:serveSentence:create", operateType = LogOperateType.CREATE,
            title = "实战平台-窗口业务-创建留所服刑",success = "实战平台-窗口业务-创建留所服刑成功",
            fail = "实战平台-窗口业务-创建留所服刑失败，错误信息：{{#_ret[msg]}}",extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createServeSentence(@Valid @RequestBody ServeSentenceSaveReqVO createReqVO) {
        return success(serveSentenceService.createServeSentence(createReqVO));
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-收押业务-留所服刑")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ServeSentenceRespVO> getServeSentence(@RequestParam("id") String id) {
        ServeSentenceDO serveSentence = serveSentenceService.getServeSentence(id);
        return success(BeanUtils.toBean(serveSentence, ServeSentenceRespVO.class));
    }

    @PostMapping("/approve")
    @ApiOperation(value = "实战平台-收押业务-审批流程")
    @LogRecordAnnotation(bizModule = "acp:serveSentence:approve", operateType = LogOperateType.UPDATE, title = "实战平台-收押业务-启动流程审批",
            success = "实战平台-收押业务-启动流程审批成功", fail = "实战平台-收押业务-启动流程审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Map<String,Object>> approvalProcess(@RequestBody ApproveReqVO approvaReqVO) {
        serveSentenceService.approve(approvaReqVO);
        return success();
    }
}
