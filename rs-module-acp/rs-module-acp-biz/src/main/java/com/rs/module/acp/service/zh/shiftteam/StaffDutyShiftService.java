package com.rs.module.acp.service.zh.shiftteam;

import javax.validation.*;

import com.rs.module.acp.controller.admin.zh.vo.shiftteam.StaffDutyShiftSaveReqVO;
import com.rs.module.acp.entity.zh.StaffDutyShiftDO;
import com.bsp.common.orm.mybatis.service.IBaseService;

import java.util.List;

/**
 * 值班模板班次 Service 接口
 *
 * <AUTHOR>
 */
public interface StaffDutyShiftService extends IBaseService<StaffDutyShiftDO>{

    /**
     * 创建值班模板班次
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createStaffDutyShift(@Valid StaffDutyShiftSaveReqVO createReqVO);

    /**
     * 更新值班模板班次
     *
     * @param updateReqVO 更新信息
     */
    void updateStaffDutyShift(@Valid StaffDutyShiftSaveReqVO updateReqVO);

    /**
     * 删除值班模板班次
     *
     * @param id 编号
     */
    void deleteStaffDutyShift(String id);

    /**
     * 获得值班模板班次
     *
     * @param id 编号
     * @return 值班模板班次
     */
    StaffDutyShiftDO getStaffDutyShift(String id);

    List<StaffDutyShiftDO> getListByOrgCode(String orgCode);
}
