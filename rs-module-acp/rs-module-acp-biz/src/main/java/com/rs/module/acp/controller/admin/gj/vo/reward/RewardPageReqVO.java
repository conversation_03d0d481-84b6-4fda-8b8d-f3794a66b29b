package com.rs.module.acp.controller.admin.gj.vo.reward;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-奖励管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RewardPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("奖励类型")
    private String rewardType;

    @ApiModelProperty("奖励原因")
    private String rewardReason;

    @ApiModelProperty("奖励内容")
    private String rewardContent;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("执行人")
    private String executor;

    @ApiModelProperty("执行人身份证号")
    private String executorSfzh;

    @ApiModelProperty("执行时间")
    private Date[] executionTime;

    @ApiModelProperty("执行情况")
    private String executeSituation;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
