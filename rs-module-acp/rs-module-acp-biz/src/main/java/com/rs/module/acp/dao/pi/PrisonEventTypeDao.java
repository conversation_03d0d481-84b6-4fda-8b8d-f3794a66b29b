package com.rs.module.acp.dao.pi;

import java.util.*;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypeListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypePageReqVO;
import com.rs.module.acp.entity.pi.PrisonEventTypeDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
 * 实战平台-巡视管控-所情事件类型 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface PrisonEventTypeDao extends IBaseDao<PrisonEventTypeDO> {


    default PageResult<PrisonEventTypeDO> selectPage(PrisonEventTypePageReqVO reqVO) {
        Page<PrisonEventTypeDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrisonEventTypeDO> wrapper = new LambdaQueryWrapperX<PrisonEventTypeDO>()
                .likeIfPresent(PrisonEventTypeDO::getTypeName, reqVO.getTypeName())
                .eqIfPresent(PrisonEventTypeDO::getIcon, reqVO.getIcon())
                .eqIfPresent(PrisonEventTypeDO::getIsEnabled, reqVO.getIsEnabled())
                .eqIfPresent(PrisonEventTypeDO::getOrderId, reqVO.getOrderId())
                .eqIfPresent(PrisonEventTypeDO::getOrgCode, reqVO.getOrgCode());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(PrisonEventTypeDO::getAddTime);
        }
        Page<PrisonEventTypeDO> prisonEventTypePage = selectPage(page, wrapper);
        return new PageResult<>(prisonEventTypePage.getRecords(), prisonEventTypePage.getTotal());
    }

    default List<PrisonEventTypeDO> selectList(PrisonEventTypeListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrisonEventTypeDO>()
                .likeIfPresent(PrisonEventTypeDO::getTypeName, reqVO.getTypeName())
                .eqIfPresent(PrisonEventTypeDO::getIcon, reqVO.getIcon())
                .eqIfPresent(PrisonEventTypeDO::getIsEnabled, reqVO.getIsEnabled())
                .eqIfPresent(PrisonEventTypeDO::getOrderId, reqVO.getOrderId())
                .eqIfPresent(PrisonEventTypeDO::getOrgCode, reqVO.getOrgCode())
                .orderByDesc(PrisonEventTypeDO::getAddTime));
    }


}
