package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.LawyerMeetingConfigDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-律师会见-会见方式、预约时段配置 Service 接口
 *
 * <AUTHOR>
 */
public interface LawyerMeetingConfigService extends IBaseService<LawyerMeetingConfigDO>{


}
