package com.rs.module.acp.service.gj.punishment;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentRemoveListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentRemovePageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentRemoveSaveReqVO;
import com.rs.module.acp.entity.gj.PunishmentRemoveDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务解除处罚呈批 Service 接口
 *
 * <AUTHOR>
 */
public interface PunishmentRemoveService extends IBaseService<PunishmentRemoveDO>{

    /**
     * 创建实战平台-管教业务解除处罚呈批
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPunishmentRemove(@Valid PunishmentRemoveSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务解除处罚呈批
     *
     * @param updateReqVO 更新信息
     */
    void updatePunishmentRemove(@Valid PunishmentRemoveSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务解除处罚呈批
     *
     * @param id 编号
     */
    void deletePunishmentRemove(String id);

    /**
     * 获得实战平台-管教业务解除处罚呈批
     *
     * @param id 编号
     * @return 实战平台-管教业务解除处罚呈批
     */
    PunishmentRemoveDO getPunishmentRemove(String id);

    /**
    * 获得实战平台-管教业务解除处罚呈批分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务解除处罚呈批分页
    */
    PageResult<PunishmentRemoveDO> getPunishmentRemovePage(PunishmentRemovePageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务解除处罚呈批列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务解除处罚呈批列表
    */
    List<PunishmentRemoveDO> getPunishmentRemoveList(PunishmentRemoveListReqVO listReqVO);


}
