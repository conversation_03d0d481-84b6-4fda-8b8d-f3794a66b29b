package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.FamilyMeetingCompanionDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-家属会见同行登记 Service 接口
 *
 * <AUTHOR>
 */
public interface FamilyMeetingCompanionService extends IBaseService<FamilyMeetingCompanionDO>{

    /**
     * 创建实战平台-窗口业务-家属会见同行登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createFamilyMeetingCompanion(@Valid FamilyMeetingCompanionSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-家属会见同行登记
     *
     * @param updateReqVO 更新信息
     */
    void updateFamilyMeetingCompanion(@Valid FamilyMeetingCompanionSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-家属会见同行登记
     *
     * @param id 编号
     */
    void deleteFamilyMeetingCompanion(String id);

    /**
     * 获得实战平台-窗口业务-家属会见同行登记
     *
     * @param id 编号
     * @return 实战平台-窗口业务-家属会见同行登记
     */
    FamilyMeetingCompanionDO getFamilyMeetingCompanion(String id);

    /**
    * 获得实战平台-窗口业务-家属会见同行登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-家属会见同行登记分页
    */
    PageResult<FamilyMeetingCompanionDO> getFamilyMeetingCompanionPage(FamilyMeetingCompanionPageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-家属会见同行登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-家属会见同行登记列表
    */
    List<FamilyMeetingCompanionDO> getFamilyMeetingCompanionList(FamilyMeetingCompanionListReqVO listReqVO);

    /**
     * 获得实战平台-窗口业务-根据家属会见ID获得家属会见同行登记列表
     *
     * @param familyMeetingId 家属会见ID
     * @return 实战平台-窗口业务-家属会见同行登记列表
     */
    List<FamilyMeetingCompanionRespVO> getCompanionListByFamilyMeetingId(String familyMeetingId);

    /**
     * 获得实战平台-窗口业务-根据家属会见ID保存家属会见同行登记列表
     *
     * @param companionList 创建信息
     * @param familyMeetingId 家属会见ID
     */
    boolean saveCompanionListByFamilyMeetingId(List<FamilyMeetingCompanionSaveReqVO> companionList,String familyMeetingId);
}
