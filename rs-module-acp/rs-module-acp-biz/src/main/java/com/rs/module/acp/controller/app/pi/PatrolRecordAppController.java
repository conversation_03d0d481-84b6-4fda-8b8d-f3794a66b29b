package com.rs.module.acp.controller.app.pi;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.app.pi.vo.PatrolRecordAppSaveReqVO;
import com.rs.module.acp.service.pi.PatrolRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "仓内外屏-巡视管控-巡视登记")
@RestController
@RequestMapping("/app/acp/pi/patrolRecord")
@Validated
public class PatrolRecordAppController {

    @Resource
    private PatrolRecordService patrolRecordService;

    @PostMapping("/manuallyCreatePatrolRecord")
    @ApiOperation(value = "仓内外屏-巡视管控-巡视登记")
    public CommonResult<String> appCreatePatrolRecord(@Valid @RequestBody PatrolRecordAppSaveReqVO regReqVO){
        return success(patrolRecordService.appCreatePatrolRecord(regReqVO));
    }

}
