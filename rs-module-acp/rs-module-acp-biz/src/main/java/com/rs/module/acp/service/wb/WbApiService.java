package com.rs.module.acp.service.wb;

import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.GoodsDeliveryDO;

import java.util.List;

public interface WbApiService {

    /**
     * 获取监管人员会见通知（内屏使用）
     * @param query 查询条件
     * @return
     */
    PageResult<MeetingNoticeVO> getMeetingNoticePage(MeetingNoticeQueryVO query);


    /**
     * 获得实战平台-窗口业务-物品顾送登记分页
     *
     * @param pageReqVO 分页查询
     * @return 实战平台-窗口业务-物品顾送登记分页
     */
    PageResult<GoodsDeliveryRespVO> getGoodsDeliveryPage(GoodsDeliveryPageReqVO pageReqVO);

    /**
     * 实战平台-窗口业务-物品顾送签收
     * @param createReqVO
     * @return
     */
    boolean GoodsDeliverySignfor( GoodsDeliverySaveReqVO updateReqVO);
}
