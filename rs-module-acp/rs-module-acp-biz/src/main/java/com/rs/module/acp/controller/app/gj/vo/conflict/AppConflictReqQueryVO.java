package com.rs.module.acp.controller.app.gj.vo.conflict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025/3/26 09:42
 */
@ApiModel(description = "管理后台-实战平台内屏-管教业务-社会矛盾查询 Request VO")
@Data
public class AppConflictReqQueryVO {

    @ApiModelProperty("确认状态（0：待确认、1：已确认）")
    @NotNull(message = "确认状态不能为空")
    private Integer confirmStatus;
    @ApiModelProperty("监管人员编号")
    @NotBlank(message = "监管人员编号不能为空")
    private String jgrybm;

}
