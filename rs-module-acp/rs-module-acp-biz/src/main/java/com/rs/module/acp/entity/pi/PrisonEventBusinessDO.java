package com.rs.module.acp.entity.pi;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-巡视管控-所情关联业务 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_prison_event_business")
@KeySequence("acp_pi_prison_event_business_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_prison_event_business")
public class PrisonEventBusinessDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 业务对象
     */
    private String businessObject;
    /**
     * 办理人
     */
    private String operatePerson;
    /**
     * 办理时间
     */
    private Date operateTime;
    /**
     * 业务主表id
     */
    private String businessId;
    /**
     * 警情主表id
     */
    private String eventId;
    /**
     * 0-登记 1-已完成可编辑
     */
    private String status;
    /**
     * 登记人岗位名称
     */
    private String createUserRoleName;
    /**
     * 业务对象ID
     */
    private String businessObjectId;
    /**
     * 处置id
     */
    private String handleId;
    /**
     * 处理方式
     */
    private String handleMethod;
    /**
     * 医疗变动内容
     */
    private String changeContent;
    /**
     * 附件地址
     */
    private String attUrl;

}
