package com.rs.module.acp.service.pi;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypeItemListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypeItemPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypeItemRespVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypeItemSaveReqVO;
import com.rs.module.base.util.TreeLcmUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.rs.module.acp.entity.pi.PrisonEventTypeItemDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.PrisonEventTypeItemDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-巡视管控-所情事件类型明细项 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrisonEventTypeItemServiceImpl extends BaseServiceImpl<PrisonEventTypeItemDao, PrisonEventTypeItemDO> implements PrisonEventTypeItemService {

    @Resource
    private PrisonEventTypeItemDao prisonEventTypeItemDao;

    @Override
    public String createPrisonEventTypeItem(PrisonEventTypeItemSaveReqVO createReqVO) {
        // 插入
        PrisonEventTypeItemDO prisonEventTypeItem = BeanUtils.toBean(createReqVO, PrisonEventTypeItemDO.class);
        prisonEventTypeItemDao.insert(prisonEventTypeItem);
        // 返回
        return prisonEventTypeItem.getId();
    }

    @Override
    public void updatePrisonEventTypeItem(PrisonEventTypeItemSaveReqVO updateReqVO) {
        // 校验存在
        validatePrisonEventTypeItemExists(updateReqVO.getId());
        // 更新
        PrisonEventTypeItemDO updateObj = BeanUtils.toBean(updateReqVO, PrisonEventTypeItemDO.class);
        prisonEventTypeItemDao.updateById(updateObj);
    }

    @Override
    public void deletePrisonEventTypeItem(String id) {
        // 校验存在
        validatePrisonEventTypeItemExists(id);
        // 删除
        prisonEventTypeItemDao.deleteById(id);
    }

    private void validatePrisonEventTypeItemExists(String id) {
        if (prisonEventTypeItemDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-所情事件类型明细项数据不存在");
        }
    }

    @Override
    public PrisonEventTypeItemDO getPrisonEventTypeItem(String id) {
        return prisonEventTypeItemDao.selectById(id);
    }

    @Override
    public PageResult<PrisonEventTypeItemDO> getPrisonEventTypeItemPage(PrisonEventTypeItemPageReqVO pageReqVO) {
        return prisonEventTypeItemDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrisonEventTypeItemDO> getPrisonEventTypeItemList(PrisonEventTypeItemListReqVO listReqVO) {
        return prisonEventTypeItemDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean savePrisonEventTypeTreeList(List<PrisonEventTypeItemSaveReqVO> eventItemList, String typeId) {

        if(CollectionUtil.isEmpty(eventItemList)){
            return true;
        }
        List<PrisonEventTypeItemSaveReqVO> saveReqVOList = new ArrayList<>();
        for(PrisonEventTypeItemSaveReqVO saveReqVO:eventItemList){
            List<PrisonEventTypeItemSaveReqVO> tempList = new ArrayList<>();
            traverse(saveReqVO,tempList,null);
            saveReqVOList.addAll(tempList);
        }

        List<PrisonEventTypeItemDO> itemDOList = BeanUtils.toBean(saveReqVOList,PrisonEventTypeItemDO.class);
        Map<String,String> idMap = new HashMap<>();
        for(PrisonEventTypeItemDO itemDO:itemDOList){
            itemDO.setTypeId(typeId);
            idMap.put(itemDO.getId(),itemDO.getId());
        }

        //删除不需要的
        LambdaQueryWrapper<PrisonEventTypeItemDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(PrisonEventTypeItemDO::getId);
        lambdaQueryWrapper.eq(PrisonEventTypeItemDO::getTypeId,typeId)
                .eq(PrisonEventTypeItemDO::getOrgCode, SessionUserUtil.getSessionUser().getOrgCode());

        List<PrisonEventTypeItemDO> dataBaseList = list(lambdaQueryWrapper);
        if(CollectionUtil.isNotEmpty(dataBaseList)){
            List<String> delIdList = new ArrayList<>();
            for(PrisonEventTypeItemDO itemDO:dataBaseList){
                if(!idMap.containsKey(itemDO.getId())){
                    delIdList.add(itemDO.getId());
                }
            }
            if(CollectionUtil.isNotEmpty(delIdList)){
                prisonEventTypeItemDao.deleteBatchIds(delIdList);

            }
        }

        return saveOrUpdateBatch(itemDOList);
    }

    private void traverse(PrisonEventTypeItemSaveReqVO item,List<PrisonEventTypeItemSaveReqVO> nodeList,String parentId){
        if(ObjectUtil.isEmpty(item.getId())){
            item.setId(StringUtil.getGuid32());
        }
        if(ObjectUtil.isNotEmpty(parentId)){
            item.setParentId(parentId);
        }
        nodeList.add(item);
        if(CollectionUtil.isNotEmpty(item.getChildList())){
            for(PrisonEventTypeItemSaveReqVO childVO:item.getChildList()){
                traverse(childVO,nodeList,item.getId());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePrisonEventTypeItemByTypeId(String typeId) {
        LambdaQueryWrapper<PrisonEventTypeItemDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PrisonEventTypeItemDO::getTypeId,typeId)
                        .eq(PrisonEventTypeItemDO::getOrgCode,SessionUserUtil.getSessionUser().getOrgCode());
       prisonEventTypeItemDao.delete(lambdaQueryWrapper);
    }

    @Override
    public List<PrisonEventTypeItemRespVO> getPrisonEventTypeItemTreeByTypeId(String typeId) {

        List<PrisonEventTypeItemDO> allList = list(new LambdaQueryWrapper<PrisonEventTypeItemDO>()
                .eq(PrisonEventTypeItemDO::getTypeId,typeId)
                .eq(PrisonEventTypeItemDO::getOrgCode,SessionUserUtil.getSessionUser().getOrgCode()));

        if(CollectionUtil.isEmpty(allList)){
            return new ArrayList<>();
        }

        List<PrisonEventTypeItemRespVO> respVOList = BeanUtils.toBean(allList,PrisonEventTypeItemRespVO.class);
        return buildTree(respVOList);
    }

    public List<PrisonEventTypeItemRespVO> buildTree(List<PrisonEventTypeItemRespVO> nodes) {
        // 存储所有节点的映射，便于快速查找和添加子节点

        Map<String,PrisonEventTypeItemRespVO> tempMap = new HashMap<>();
        for (PrisonEventTypeItemRespVO node : nodes) {
            tempMap.put(node.getId(),node);
        }

        Map<String, PrisonEventTypeItemRespVO> nodeMap = new HashMap<>();
        List<PrisonEventTypeItemRespVO> rootNodes = new ArrayList<>(); // 存储顶级节点（根节点）的列表
        // 初始化nodeMap，并找出所有顶级节点（即parentId为null或空字符串的节点）
        for (PrisonEventTypeItemRespVO node : nodes) {
            nodeMap.put(node.getId(), node); // 将节点添加到map中，便于后续查找和操作
            if (node.getParentId() == null || node.getParentId().isEmpty()) { // 顶级节点判断条件根据实际情况调整，比如使用特定的父ID值表示顶级节点等。
                rootNodes.add(node); // 添加到顶级节点列表中
            } else { // 非顶级节点暂时不做处理，稍后添加为子节点
                PrisonEventTypeItemRespVO temp = new PrisonEventTypeItemRespVO();
                if(nodeMap.containsKey(node.getParentId())){
                    // 直接在父节点的children列表中添加当前节点，但不作为顶级处理。这里只是为了初始化父子关系。
                    temp = nodeMap.get(node.getParentId());
                }else {
                    temp = tempMap.get(node.getParentId());
                }
                List<PrisonEventTypeItemRespVO> childList = CollectionUtil.isNotEmpty(temp.getChildList())?temp.getChildList():new ArrayList<>();
                childList.add(node);
                temp.setChildList(childList);
                nodeMap.put(temp.getId(),temp);
            }
        }
        return rootNodes;
    }
}
