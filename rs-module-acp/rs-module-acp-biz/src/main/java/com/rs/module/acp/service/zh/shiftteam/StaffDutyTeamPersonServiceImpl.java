package com.rs.module.acp.service.zh.shiftteam;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.zh.vo.shiftteam.StaffDutyTeamPersonSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.rs.module.acp.entity.zh.StaffDutyTeamPersonDO;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.zh.StaffDutyTeamPersonDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 值班模板班组人员信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class StaffDutyTeamPersonServiceImpl extends BaseServiceImpl<StaffDutyTeamPersonDao, StaffDutyTeamPersonDO> implements StaffDutyTeamPersonService {

    @Resource
    private StaffDutyTeamPersonDao staffDutyTeamPersonDao;

    @Override
    public String createStaffDutyTeamPerson(StaffDutyTeamPersonSaveReqVO createReqVO) {
        // 插入
        StaffDutyTeamPersonDO staffDutyTeamPerson = BeanUtils.toBean(createReqVO, StaffDutyTeamPersonDO.class);
        staffDutyTeamPersonDao.insert(staffDutyTeamPerson);
        // 返回
        return staffDutyTeamPerson.getId();
    }

    @Override
    public void updateStaffDutyTeamPerson(StaffDutyTeamPersonSaveReqVO updateReqVO) {
        // 校验存在
        validateStaffDutyTeamPersonExists(updateReqVO.getId());
        // 更新
        StaffDutyTeamPersonDO updateObj = BeanUtils.toBean(updateReqVO, StaffDutyTeamPersonDO.class);
        staffDutyTeamPersonDao.updateById(updateObj);
    }

    @Override
    public void deleteStaffDutyTeamPerson(String id) {
        // 校验存在
        validateStaffDutyTeamPersonExists(id);
        // 删除
        staffDutyTeamPersonDao.deleteById(id);
    }

    private void validateStaffDutyTeamPersonExists(String id) {
        if (staffDutyTeamPersonDao.selectById(id) == null) {
            throw new ServerException("值班模板班组人员信息数据不存在");
        }
    }

    @Override
    public StaffDutyTeamPersonDO getStaffDutyTeamPerson(String id) {
        return staffDutyTeamPersonDao.selectById(id);
    }

    @Override
    public void createOrUpdateBatch(String teamId, List<StaffDutyTeamPersonSaveReqVO> personInfos) {
        if (personInfos == null || personInfos.isEmpty()) {
            return;
        }
        createOrUpdateBatch( personInfos);
    }
    @Override
    public void createOrUpdateBatch(List<StaffDutyTeamPersonSaveReqVO> personInfos) {
        if (personInfos == null || personInfos.isEmpty()) {
            return;
        }
        Set<String> teamIds = personInfos.stream().map(StaffDutyTeamPersonSaveReqVO::getTeamId).collect(Collectors.toSet());
        for (String teamId : teamIds){
            deleteByTeamId(teamId);
        }
        // 先删除该teamId下的所有人员信息

        // 批量插入新的人员信息
        List<StaffDutyTeamPersonDO> personDOs = personInfos.stream()
                .map(personInfo -> {
                    StaffDutyTeamPersonDO personDO = BeanUtils.toBean(personInfo, StaffDutyTeamPersonDO.class);
                    return personDO;
                })
                .collect(Collectors.toList());

        staffDutyTeamPersonDao.insertBatch(personDOs);
    }
    @Override
    public List<StaffDutyTeamPersonDO> selectByTeamId(String teamId) {
        return staffDutyTeamPersonDao.selectList((new LambdaQueryWrapper<StaffDutyTeamPersonDO>().eq(StaffDutyTeamPersonDO::getTeamId, teamId)).eq(StaffDutyTeamPersonDO::getIsDel, 0));
    }

    @Override
    public List<StaffDutyTeamPersonDO> selectByOrgCodeTeamIdList(String orgCode, List<String> teamIdList) {
        if(StringUtil.isEmpty(orgCode)) orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        return staffDutyTeamPersonDao.selectList(new LambdaQueryWrapperX<StaffDutyTeamPersonDO>().eq(StaffDutyTeamPersonDO::getOrgCode, orgCode).
                eq(StaffDutyTeamPersonDO::getIsDel, 0).in(StaffDutyTeamPersonDO::getTeamId, teamIdList));
    }

    private void deleteByTeamId(String teamId) {
        staffDutyTeamPersonDao.deleteByTeamId(teamId);
    }

}
