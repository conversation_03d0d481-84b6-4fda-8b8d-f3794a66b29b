package com.rs.module.acp.service.gj.riskassmt;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.riskassmt.*;
import com.rs.module.acp.entity.gj.GjRiskAssmtDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-管教业务-风险评估 Service 接口
 *
 * <AUTHOR>
 */
public interface GjRiskAssmtService extends IBaseService<GjRiskAssmtDO>{

    /**
     * 创建实战平台-管教业务-风险评估
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createGjRiskAssmt(@Valid GjRiskAssmtSaveRegVO createReqVO);

    /**
     * 更新实战平台-管教业务-风险评估
     *
     * @param updateReqVO 更新信息
     */
    void updateGjRiskAssmt(@Valid GjRiskAssmtSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-风险评估
     *
     * @param id 编号
     */
    void deleteGjRiskAssmt(String id);

    /**
     * 获得实战平台-管教业务-风险评估
     *
     * @param id 编号
     * @return 实战平台-管教业务-风险评估
     */
    GjRiskAssmtRespVO getGjRiskAssmt(String id);

    /**
    * 获得实战平台-管教业务-风险评估分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-风险评估分页
    */
    PageResult<GjRiskAssmtDO> getGjRiskAssmtPage(GjRiskAssmtPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-风险评估列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-风险评估列表
    */
    List<GjRiskAssmtDO> getGjRiskAssmtList(GjRiskAssmtListReqVO listReqVO);

    /**
     * 管教业务获取风险评估记录
     * <AUTHOR>
     * @date 2025/6/4 14:32
     * @param [jgrybm]
     * @return java.util.List<com.rs.module.acp.controller.admin.gj.vo.riskassmt.GjRiskAssmtRespVO>
     */
    List<GjRiskAssmtRespVO> getGjRiskAssmtRespVOByJgrybm(String jgrybm);

    /**
     * 领导审批
     * <AUTHOR>
     * @date 2025/6/4 15:05
     * @param [approveReqVO]
     * @return void
     */
    void leaderApprove(GjApproveReqVO approveReqVO);

    /**
     * 更改流程状态
     * @param id
     * @param status
     * @param actInstId
     * @param taskId
     * @return
     */
    Boolean updateProcessStatus(String id, String status, String actInstId, String taskId);

    /**
     * 批量评估
     * <AUTHOR>
     * @date 2025/6/4 19:36
     * @param [createReqVOList]
     * @return java.lang.Boolean
     */
    Boolean createReqVOList(List<GjRiskAssmtSaveRegVO> createReqVOList);

    /**
     * 批量审批
     * <AUTHOR>
     * @date 2025/6/9 10:07
     * @param [approveReqVOList]
     * @return void
     */
    void batchApprove(List<GjApproveReqVO> approveReqVOList);

}
