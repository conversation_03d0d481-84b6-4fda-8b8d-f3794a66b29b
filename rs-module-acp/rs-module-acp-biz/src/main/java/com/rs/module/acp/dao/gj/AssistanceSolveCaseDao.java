package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.assistancesolvecase.AssistanceSolveCaseListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.assistancesolvecase.AssistanceSolveCasePageReqVO;
import com.rs.module.acp.entity.gj.AssistanceSolveCaseDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-协助破案 Dao
*
* <AUTHOR>
*/
@Mapper
public interface AssistanceSolveCaseDao extends IBaseDao<AssistanceSolveCaseDO> {


    default PageResult<AssistanceSolveCaseDO> selectPage(AssistanceSolveCasePageReqVO reqVO) {
        Page<AssistanceSolveCaseDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<AssistanceSolveCaseDO> wrapper = new LambdaQueryWrapperX<AssistanceSolveCaseDO>()
            .eqIfPresent(AssistanceSolveCaseDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(AssistanceSolveCaseDO::getClueType, reqVO.getClueType())
            .betweenIfPresent(AssistanceSolveCaseDO::getClueGetTime, reqVO.getClueGetTime())
            .eqIfPresent(AssistanceSolveCaseDO::getClueSfzdaj, reqVO.getClueSfzdaj())
            .eqIfPresent(AssistanceSolveCaseDO::getClueJjajxz, reqVO.getClueJjajxz())
            .eqIfPresent(AssistanceSolveCaseDO::getClueSaqy, reqVO.getClueSaqy())
            .eqIfPresent(AssistanceSolveCaseDO::getClueXsly, reqVO.getClueXsly())
            .eqIfPresent(AssistanceSolveCaseDO::getClueSar, reqVO.getClueSar())
            .eqIfPresent(AssistanceSolveCaseDO::getClueSars, reqVO.getClueSars())
            .eqIfPresent(AssistanceSolveCaseDO::getClueContext, reqVO.getClueContext())
            .eqIfPresent(AssistanceSolveCaseDO::getClueAttachmentUrl, reqVO.getClueAttachmentUrl())
            .eqIfPresent(AssistanceSolveCaseDO::getClueRegistUser, reqVO.getClueRegistUser())
            .eqIfPresent(AssistanceSolveCaseDO::getClueRegistUserSfzh, reqVO.getClueRegistUserSfzh())
            .betweenIfPresent(AssistanceSolveCaseDO::getClueRegistTime, reqVO.getClueRegistTime())
            .eqIfPresent(AssistanceSolveCaseDO::getForwardType, reqVO.getForwardType())
            .likeIfPresent(AssistanceSolveCaseDO::getForwardName, reqVO.getForwardName())
            .betweenIfPresent(AssistanceSolveCaseDO::getForwardTime, reqVO.getForwardTime())
            .eqIfPresent(AssistanceSolveCaseDO::getForwardUrl, reqVO.getForwardUrl())
            .eqIfPresent(AssistanceSolveCaseDO::getForwardUser, reqVO.getForwardUser())
            .eqIfPresent(AssistanceSolveCaseDO::getForwardUserSfzh, reqVO.getForwardUserSfzh())
            .betweenIfPresent(AssistanceSolveCaseDO::getForwardUserTime, reqVO.getForwardUserTime())
            .eqIfPresent(AssistanceSolveCaseDO::getFeedbackReceiptType, reqVO.getFeedbackReceiptType())
            .likeIfPresent(AssistanceSolveCaseDO::getFeedbackReceiptName, reqVO.getFeedbackReceiptName())
            .betweenIfPresent(AssistanceSolveCaseDO::getFeedbackReceiptTime, reqVO.getFeedbackReceiptTime())
            .betweenIfPresent(AssistanceSolveCaseDO::getFeedbackTime, reqVO.getFeedbackTime())
            .eqIfPresent(AssistanceSolveCaseDO::getFeedbackCzqk, reqVO.getFeedbackCzqk())
            .eqIfPresent(AssistanceSolveCaseDO::getFeedbackPhajsl, reqVO.getFeedbackPhajsl())
            .eqIfPresent(AssistanceSolveCaseDO::getFeedbackZhwffzxyrsl, reqVO.getFeedbackZhwffzxyrsl())
            .eqIfPresent(AssistanceSolveCaseDO::getFeedbackFxztrysl, reqVO.getFeedbackFxztrysl())
            .eqIfPresent(AssistanceSolveCaseDO::getFeedbackJhzkzwsl, reqVO.getFeedbackJhzkzwsl())
            .eqIfPresent(AssistanceSolveCaseDO::getFeedbackUser, reqVO.getFeedbackUser())
            .eqIfPresent(AssistanceSolveCaseDO::getFeedbackUserSfzh, reqVO.getFeedbackUserSfzh())
            .betweenIfPresent(AssistanceSolveCaseDO::getFeedbackUserTime, reqVO.getFeedbackUserTime())
            .eqIfPresent(AssistanceSolveCaseDO::getStatus, reqVO.getStatus())
            .eqIfPresent(AssistanceSolveCaseDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(AssistanceSolveCaseDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(AssistanceSolveCaseDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(AssistanceSolveCaseDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(AssistanceSolveCaseDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(AssistanceSolveCaseDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(AssistanceSolveCaseDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(AssistanceSolveCaseDO::getAddTime);
        }
        Page<AssistanceSolveCaseDO> assistanceSolveCasePage = selectPage(page, wrapper);
        return new PageResult<>(assistanceSolveCasePage.getRecords(), assistanceSolveCasePage.getTotal());
    }
    default List<AssistanceSolveCaseDO> selectList(AssistanceSolveCaseListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AssistanceSolveCaseDO>()
            .eqIfPresent(AssistanceSolveCaseDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(AssistanceSolveCaseDO::getClueType, reqVO.getClueType())
            .betweenIfPresent(AssistanceSolveCaseDO::getClueGetTime, reqVO.getClueGetTime())
            .eqIfPresent(AssistanceSolveCaseDO::getClueSfzdaj, reqVO.getClueSfzdaj())
            .eqIfPresent(AssistanceSolveCaseDO::getClueJjajxz, reqVO.getClueJjajxz())
            .eqIfPresent(AssistanceSolveCaseDO::getClueSaqy, reqVO.getClueSaqy())
            .eqIfPresent(AssistanceSolveCaseDO::getClueXsly, reqVO.getClueXsly())
            .eqIfPresent(AssistanceSolveCaseDO::getClueSar, reqVO.getClueSar())
            .eqIfPresent(AssistanceSolveCaseDO::getClueSars, reqVO.getClueSars())
            .eqIfPresent(AssistanceSolveCaseDO::getClueContext, reqVO.getClueContext())
            .eqIfPresent(AssistanceSolveCaseDO::getClueAttachmentUrl, reqVO.getClueAttachmentUrl())
            .eqIfPresent(AssistanceSolveCaseDO::getClueRegistUser, reqVO.getClueRegistUser())
            .eqIfPresent(AssistanceSolveCaseDO::getClueRegistUserSfzh, reqVO.getClueRegistUserSfzh())
            .betweenIfPresent(AssistanceSolveCaseDO::getClueRegistTime, reqVO.getClueRegistTime())
            .eqIfPresent(AssistanceSolveCaseDO::getForwardType, reqVO.getForwardType())
            .likeIfPresent(AssistanceSolveCaseDO::getForwardName, reqVO.getForwardName())
            .betweenIfPresent(AssistanceSolveCaseDO::getForwardTime, reqVO.getForwardTime())
            .eqIfPresent(AssistanceSolveCaseDO::getForwardUrl, reqVO.getForwardUrl())
            .eqIfPresent(AssistanceSolveCaseDO::getForwardUser, reqVO.getForwardUser())
            .eqIfPresent(AssistanceSolveCaseDO::getForwardUserSfzh, reqVO.getForwardUserSfzh())
            .betweenIfPresent(AssistanceSolveCaseDO::getForwardUserTime, reqVO.getForwardUserTime())
            .eqIfPresent(AssistanceSolveCaseDO::getFeedbackReceiptType, reqVO.getFeedbackReceiptType())
            .likeIfPresent(AssistanceSolveCaseDO::getFeedbackReceiptName, reqVO.getFeedbackReceiptName())
            .betweenIfPresent(AssistanceSolveCaseDO::getFeedbackReceiptTime, reqVO.getFeedbackReceiptTime())
            .betweenIfPresent(AssistanceSolveCaseDO::getFeedbackTime, reqVO.getFeedbackTime())
            .eqIfPresent(AssistanceSolveCaseDO::getFeedbackCzqk, reqVO.getFeedbackCzqk())
            .eqIfPresent(AssistanceSolveCaseDO::getFeedbackPhajsl, reqVO.getFeedbackPhajsl())
            .eqIfPresent(AssistanceSolveCaseDO::getFeedbackZhwffzxyrsl, reqVO.getFeedbackZhwffzxyrsl())
            .eqIfPresent(AssistanceSolveCaseDO::getFeedbackFxztrysl, reqVO.getFeedbackFxztrysl())
            .eqIfPresent(AssistanceSolveCaseDO::getFeedbackJhzkzwsl, reqVO.getFeedbackJhzkzwsl())
            .eqIfPresent(AssistanceSolveCaseDO::getFeedbackUser, reqVO.getFeedbackUser())
            .eqIfPresent(AssistanceSolveCaseDO::getFeedbackUserSfzh, reqVO.getFeedbackUserSfzh())
            .betweenIfPresent(AssistanceSolveCaseDO::getFeedbackUserTime, reqVO.getFeedbackUserTime())
            .eqIfPresent(AssistanceSolveCaseDO::getStatus, reqVO.getStatus())
            .eqIfPresent(AssistanceSolveCaseDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(AssistanceSolveCaseDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(AssistanceSolveCaseDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(AssistanceSolveCaseDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(AssistanceSolveCaseDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(AssistanceSolveCaseDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(AssistanceSolveCaseDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(AssistanceSolveCaseDO::getAddTime));    }


    }
