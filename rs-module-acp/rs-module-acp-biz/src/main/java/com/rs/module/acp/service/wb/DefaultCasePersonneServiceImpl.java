package com.rs.module.acp.service.wb;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.DefaultCasePersonneDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.DefaultCasePersonneDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-办案人员 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DefaultCasePersonneServiceImpl extends BaseServiceImpl<DefaultCasePersonneDao, DefaultCasePersonneDO> implements DefaultCasePersonneService {

    @Resource
    private DefaultCasePersonneDao defaultCasePersonneDao;

    @Override
    public String createDefaultCasePersonne(DefaultCasePersonneSaveReqVO createReqVO) {
        // 插入
        DefaultCasePersonneDO defaultCasePersonne = BeanUtils.toBean(createReqVO, DefaultCasePersonneDO.class);
        defaultCasePersonneDao.insert(defaultCasePersonne);
        // 返回
        return defaultCasePersonne.getId();
    }

    @Override
    public void updateDefaultCasePersonne(DefaultCasePersonneSaveReqVO updateReqVO) {
        // 校验存在
        validateDefaultCasePersonneExists(updateReqVO.getId());
        // 更新
        DefaultCasePersonneDO updateObj = BeanUtils.toBean(updateReqVO, DefaultCasePersonneDO.class);
        defaultCasePersonneDao.updateById(updateObj);
    }

    @Override
    public void deleteDefaultCasePersonne(String id) {
        // 校验存在
        validateDefaultCasePersonneExists(id);
        // 删除
        defaultCasePersonneDao.deleteById(id);
    }

    private void validateDefaultCasePersonneExists(String id) {
        if (defaultCasePersonneDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-办案人员数据不存在");
        }
    }

    @Override
    public DefaultCasePersonneDO getDefaultCasePersonne(String id) {
        return defaultCasePersonneDao.selectById(id);
    }

    @Override
    public PageResult<DefaultCasePersonneDO> getDefaultCasePersonnePage(DefaultCasePersonnePageReqVO pageReqVO) {
        return defaultCasePersonneDao.selectPage(pageReqVO);
    }

    @Override
    public List<DefaultCasePersonneDO> getDefaultCasePersonneList(DefaultCasePersonneListReqVO listReqVO) {
        return defaultCasePersonneDao.selectList(listReqVO);
    }


}
