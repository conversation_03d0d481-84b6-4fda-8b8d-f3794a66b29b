package com.rs.module.acp.dao.gj;

import java.util.*;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds.DiagnosisAssmtJdsListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds.DiagnosisAssmtJdsPageReqVO;
import com.rs.module.acp.entity.gj.DiagnosisAssmtJdsDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 实战平台-管教业务-诊断评估(戒毒所) Dao
*
* <AUTHOR>
*/
@Mapper
public interface DiagnosisAssmtJdsDao extends IBaseDao<DiagnosisAssmtJdsDO> {


    default PageResult<DiagnosisAssmtJdsDO> selectPage(DiagnosisAssmtJdsPageReqVO reqVO) {
        Page<DiagnosisAssmtJdsDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<DiagnosisAssmtJdsDO> wrapper = new LambdaQueryWrapperX<DiagnosisAssmtJdsDO>()
            .eqIfPresent(DiagnosisAssmtJdsDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(DiagnosisAssmtJdsDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(DiagnosisAssmtJdsDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(DiagnosisAssmtJdsDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtType, reqVO.getAssmtType())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtPeriod, reqVO.getAssmtPeriod())
            .betweenIfPresent(DiagnosisAssmtJdsDO::getStartTime, reqVO.getStartTime())
            .betweenIfPresent(DiagnosisAssmtJdsDO::getEndTime, reqVO.getEndTime())
            .betweenIfPresent(DiagnosisAssmtJdsDO::getPushTime, reqVO.getPushTime())
            .eqIfPresent(DiagnosisAssmtJdsDO::getPushJobPositions, reqVO.getPushJobPositions())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentValue1, reqVO.getAssmtContentValue1())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentValue2, reqVO.getAssmtContentValue2())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentValue3, reqVO.getAssmtContentValue3())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentValue4, reqVO.getAssmtContentValue4())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentValue5, reqVO.getAssmtContentValue5())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentValue6, reqVO.getAssmtContentValue6())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentValue7, reqVO.getAssmtContentValue7())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentValue8, reqVO.getAssmtContentValue8())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentValue9, reqVO.getAssmtContentValue9())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentValue10, reqVO.getAssmtContentValue10())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentTotalScore, reqVO.getAssmtContentTotalScore())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtResultValue1, reqVO.getAssmtResultValue1())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtResultValue2, reqVO.getAssmtResultValue2())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtResultValue3, reqVO.getAssmtResultValue3())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtResultValue4, reqVO.getAssmtResultValue4())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtResultValue5, reqVO.getAssmtResultValue5())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAsstmContentJson, reqVO.getAsstmContentJson())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtResultJson, reqVO.getAssmtResultJson())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtUserSfzh, reqVO.getAssmtUserSfzh())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtUser, reqVO.getAssmtUser())
            .betweenIfPresent(DiagnosisAssmtJdsDO::getAssmtTime, reqVO.getAssmtTime())
            .eqIfPresent(DiagnosisAssmtJdsDO::getStatus, reqVO.getStatus())
            .eqIfPresent(DiagnosisAssmtJdsDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(DiagnosisAssmtJdsDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(DiagnosisAssmtJdsDO::getAddTime);
        }
        Page<DiagnosisAssmtJdsDO> diagnosisAssmtJdsPage = selectPage(page, wrapper);
        return new PageResult<>(diagnosisAssmtJdsPage.getRecords(), diagnosisAssmtJdsPage.getTotal());
    }
    default List<DiagnosisAssmtJdsDO> selectList(DiagnosisAssmtJdsListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DiagnosisAssmtJdsDO>()
            .eqIfPresent(DiagnosisAssmtJdsDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(DiagnosisAssmtJdsDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(DiagnosisAssmtJdsDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(DiagnosisAssmtJdsDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtType, reqVO.getAssmtType())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtPeriod, reqVO.getAssmtPeriod())
            .betweenIfPresent(DiagnosisAssmtJdsDO::getStartTime, reqVO.getStartTime())
            .betweenIfPresent(DiagnosisAssmtJdsDO::getEndTime, reqVO.getEndTime())
            .betweenIfPresent(DiagnosisAssmtJdsDO::getPushTime, reqVO.getPushTime())
            .eqIfPresent(DiagnosisAssmtJdsDO::getPushJobPositions, reqVO.getPushJobPositions())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentValue1, reqVO.getAssmtContentValue1())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentValue2, reqVO.getAssmtContentValue2())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentValue3, reqVO.getAssmtContentValue3())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentValue4, reqVO.getAssmtContentValue4())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentValue5, reqVO.getAssmtContentValue5())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentValue6, reqVO.getAssmtContentValue6())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentValue7, reqVO.getAssmtContentValue7())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentValue8, reqVO.getAssmtContentValue8())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentValue9, reqVO.getAssmtContentValue9())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentValue10, reqVO.getAssmtContentValue10())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtContentTotalScore, reqVO.getAssmtContentTotalScore())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtResultValue1, reqVO.getAssmtResultValue1())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtResultValue2, reqVO.getAssmtResultValue2())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtResultValue3, reqVO.getAssmtResultValue3())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtResultValue4, reqVO.getAssmtResultValue4())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtResultValue5, reqVO.getAssmtResultValue5())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAsstmContentJson, reqVO.getAsstmContentJson())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtResultJson, reqVO.getAssmtResultJson())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtUserSfzh, reqVO.getAssmtUserSfzh())
            .eqIfPresent(DiagnosisAssmtJdsDO::getAssmtUser, reqVO.getAssmtUser())
            .betweenIfPresent(DiagnosisAssmtJdsDO::getAssmtTime, reqVO.getAssmtTime())
            .eqIfPresent(DiagnosisAssmtJdsDO::getStatus, reqVO.getStatus())
            .eqIfPresent(DiagnosisAssmtJdsDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(DiagnosisAssmtJdsDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(DiagnosisAssmtJdsDO::getAddTime));    }


    List<DiagnosisAssmtJdsDO> handleDiagnosisAssmtJds(@Param("periodType") String periodType,
                                                      @Param("assmtType") String assmtType,
                                                      @Param("sqlTime") String sqlTime);

    JSONObject getJgryxxByJgrybm(@Param("jgrybm") String jgrybm);
}
