package com.rs.module.acp.entity.gj;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-面对面管理 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_face_to_face")
@KeySequence("acp_gj_face_to_face_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_face_to_face")
public class FaceToFaceDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 禁闭监室id
     */
    private String roomId;
    /**
     * 数据来源（字典：ZD_DATA_SOURCES）
     */
    private String dataSources;
    /**
     * 检查民警身份证号
     */
    private String checkPoliceSfzh;
    /**
     * 检查民警身份证号
     */
    private String checkPolice;
    /**
     * 检查时间
     */
    private Date checkTime;
    /**
     * 情况记录
     */
    private String situationRecord;
    /**
     * 拍照url
     */
    private String snapPhoto;

}
