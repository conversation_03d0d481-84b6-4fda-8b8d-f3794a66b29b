package com.rs.module.acp.controller.app.gj.vo.groupinout;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "外屏-管教业务-集体出入 Response VO")
@Data
public class AppGroupInOutRespVO implements TransPojo {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("业务类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JTCR_YWLX")
    private String businessType;
    @ApiModelProperty("经办民警")
    private String updateUserName;
    @ApiModelProperty("办理时间")
    private Date updateTime;
    @ApiModelProperty("对象名称")
    private String objectName;
    @ApiModelProperty("对象数量")
    private Integer objectCount;

}
