package com.rs.module.acp.entity.db;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-收押业务-生物特征信息 DO
 *
 * <AUTHOR>
 */
@TableName("acp_db_biometric_info")
@KeySequence("acp_db_biometric_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_db_biometric_info")
public class BiometricInfoDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 人员编号
     */
    private String rybh;
    /**
     * 采集项目类型
     */
    private String cjxmlx;
    /**
     * 生物特征,存储采集系统返回JSON结构
     */
    private String swtz;
    /**
     * swtzfj
     */
    private String swtzfj;
    /**
     * 备注
     */
    private String bz;

    /**
     * 状态
     */
    private String status;

    private String jgrybm;

}
