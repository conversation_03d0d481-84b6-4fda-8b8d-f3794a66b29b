package com.rs.module.acp.service.pi;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.pi.vo.shifthandover.*;
import com.rs.module.acp.entity.pi.ShiftHandoverDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-巡视管控-巡控交接班登记 Service 接口
 *
 * <AUTHOR>
 */
public interface ShiftHandoverService extends IBaseService<ShiftHandoverDO>{

    /**
     * 创建实战平台-巡视管控-巡控交接班登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createShiftHandover(@Valid ShiftHandoverSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-巡控交接班登记
     *
     * @param updateReqVO 更新信息
     */
    void updateShiftHandover(@Valid ShiftHandoverSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-巡控交接班登记
     *
     * @param id 编号
     */
    void deleteShiftHandover(String id);

    /**
     * 获得实战平台-巡视管控-巡控交接班登记
     *
     * @param id 编号
     * @return 实战平台-巡视管控-巡控交接班登记
     */
    ShiftHandoverDO getShiftHandover(String id);


    String shiftReg(ShiftRegReqVO regReqVO);

    Boolean successionReg(SuccessionRegReqVO regReqVO);

    ShiftHandoverStatisticsNumVO getPersonDistribution(String patrolRoomId, String orgCode);
}
