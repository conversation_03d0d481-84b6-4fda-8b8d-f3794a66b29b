package com.rs.module.acp.service.gj.undercover;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.cons.CommonConstants;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverCancelListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverCancelPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverCancelSaveReqVO;
import com.rs.module.acp.dao.gj.UndercoverCancelDao;
import com.rs.module.acp.dao.gj.UndercoverDao;
import com.rs.module.acp.entity.gj.UndercoverCancelDO;
import com.rs.module.acp.entity.gj.UndercoverDO;
import com.rs.module.acp.enums.gj.UndercoverStatusEnum;
import com.rs.module.acp.util.GjBusTraceUtil;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.sys.MsgAddVO;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.service.sys.BusTraceService;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.base.util.MsgUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 实战平台-管教业务-耳目撤销 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class UndercoverCancelServiceImpl extends BaseServiceImpl<UndercoverCancelDao, UndercoverCancelDO> implements UndercoverCancelService {

    @Resource
    private UndercoverCancelDao undercoverCancelDao;

    @Resource
    private PrisonerService prisonerService;

    @Resource
    private UndercoverDao undercoverDao;

    @Resource
    private BusTraceService busTraceService;

    private String defKey = "xinxiyuanbujianshenqingchexiao";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createUndercoverCancel(UndercoverCancelSaveReqVO createReqVO) {
        // 插入
        UndercoverCancelDO undercover = BeanUtils.toBean(createReqVO, UndercoverCancelDO.class);
        undercoverCancelDao.insert(undercover);
        //启动流程审批跳转链接
        String msgUrl = StrUtil.format("/#/discipline/InformationOfficer?curId={}&saveType=revoke", undercover.getId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", undercover.getId());
        variables.put("busType", MsgBusTypeEnum.GJ_XXYDJ.getCode());
        JSONObject result = BspApprovalUtil.commonStartProcess(defKey, undercover.getId(), "【审批】信息员布建申请撤销", msgUrl, variables, HttpUtils.getAppCode());
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            undercover.setActInstId(bpmTrail.getString("actInstId"));
            undercover.setTaskId(bpmTrail.getString("taskId"));
            undercoverCancelDao.updateById(undercover);
        } else {
            throw new ServerException("流程启动失败");
        }
        UndercoverDO undercoverDO = undercoverDao.selectById(createReqVO.getId());
        undercoverDO.setStatus(UndercoverStatusEnum.CXDSH.getCode());
        undercoverDao.updateById(undercoverDO);
        return undercover.getId();
    }

    @Override
    public void updateUndercoverCancel(UndercoverCancelSaveReqVO updateReqVO) {
        // 校验存在
        validateUndercoverCancelExists(updateReqVO.getId());
        // 更新
        UndercoverCancelDO updateObj = BeanUtils.toBean(updateReqVO, UndercoverCancelDO.class);
        undercoverCancelDao.updateById(updateObj);
    }

    @Override
    public void deleteUndercoverCancel(String id) {
        // 校验存在
        validateUndercoverCancelExists(id);
        // 删除
        undercoverCancelDao.deleteById(id);
    }

    private void validateUndercoverCancelExists(String id) {
        if (undercoverCancelDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-耳目撤销数据不存在");
        }
    }

    @Override
    public UndercoverCancelDO getUndercoverCancel(String id) {
        return undercoverCancelDao.selectById(id);
    }

    @Override
    public PageResult<UndercoverCancelDO> getUndercoverCancelPage(UndercoverCancelPageReqVO pageReqVO) {
        return undercoverCancelDao.selectPage(pageReqVO);
    }

    @Override
    public List<UndercoverCancelDO> getUndercoverCancelList(UndercoverCancelListReqVO listReqVO) {
        return undercoverCancelDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void leaderApprove(GjApproveReqVO gjApproveReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        // 领导审批
        UndercoverCancelDO entityCancel =  undercoverCancelDao.selectById(gjApproveReqVO.getId());;
        if (entityCancel == null) {
            throw new ServerException("信息不存在！");
        }

        UndercoverDO entity =  undercoverDao.selectById(gjApproveReqVO.getId());;

        UndercoverStatusEnum statusEnum = UndercoverStatusEnum.getByCode(entity.getStatus());
        if (!UndercoverStatusEnum.CXDSH.getCode().equals(entity.getStatus())) {
            throw new ServerException("该状态[" + statusEnum.getName() + "]不允许审批！");
        }

        String approvalResult = gjApproveReqVO.getApprovalResult();
        String status = null;
        String statusName = "已通过审批", passStatus =  "审批通过";
        BspApproceStatusEnum bspApproceStatusEnum;
        if (String.valueOf( BspApproceStatusEnum.PASSED_END.getCode()).equals(approvalResult)) {
            status = UndercoverStatusEnum.TJCG.getCode();
            entity.setEndTime(new Date());
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED_END;
        } else {
            //审批不通过，还是布建中
            status = UndercoverStatusEnum.SPBTG.getCode();
            statusName = "审批不通过";
            passStatus =  statusName;
            bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
        }
        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(entityCancel.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }
        entityCancel.setApprovalComments(gjApproveReqVO.getApprovalComments());

        if(StrUtil.isBlank(entityCancel.getApprovalComments())){
            entityCancel.setApprovalComments(com.rs.framework.common.cons.CommonConstants.DEFAULT_APPROVAL_VALUE_PASS);
        }
        entityCancel.setApproverXm(sessionUser.getName());
        entityCancel.setApproverSfzh(sessionUser.getIdCard());
        entityCancel.setApprovalResult(gjApproveReqVO.getApprovalResult());
        entityCancel.setApproverTime(new Date());

        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", entity.getId());
        variables.put("busType", "08");

        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getIdCard());
        nowApproveUser.put("name", sessionUser.getName());
        boolean terminateTask = BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ||
                BspApproceStatusEnum.NOT_PASSED_END.getCode() == bspApproceStatusEnum.getCode();
        JSONObject result = BspApprovalUtil.approvalProcess(defKey, entityCancel.getActInstId(), entityCancel.getTaskId(), entity.getId(),
                bspApproceStatusEnum, entityCancel.getApprovalComments(), null, null, terminateTask,
                variables, nowApproveUser, HttpUtils.getAppCode());
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            entityCancel.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程审批失败");
        }

        // 处理消息通知
        //if (StrUtil.isNotBlank(gjApproveReqVO.getId())) {
        //    SendMessageUtil.ProcessTodoMsg(gjApproveReqVO.getId(), SessionUserUtil.getSessionUser().getIdCard(), "pc");
        //}

        //该审批，可能是申请，也可能是撤销
        String apprName = "信息员布建撤销";
        String moduleCode = "GJ_UNDERCOVER_UNDO";
        String msgType = "08";
        String title = apprName + passStatus ;	//消息标题
        entity.setStatus(status);
        MsgAddVO msg = new MsgAddVO();
        //TODO 等前端路由封装
        msg.setUrl("");
        msg.setModuleCode(moduleCode);
        //消息类型代码 字典编码对应：ZD_MSG_BUSTYP
        msg.setMsgType(msgType);
        msg.setTitle(title);
        msg.setOrgName(sessionUser.getOrgName());
        msg.setToOrgCode(sessionUser.getOrgCode());
        msg.setBusinessId(entity.getId());
        msg.setJgrybm(entity.getJgrybm());
        Map<String, Object> contentData = new HashMap<>();
        contentData.put("addTime", DateUtil.formatDateTime(entity.getAddTime()));
        contentData.put("addUserName", entity.getAddUserName());
        contentData.put("statusName", statusName);
        msg.setContentData(contentData);
        msg.setPcid(entity.getId());
        MsgUtil.sendMsg(msg);
        entity.setStatus(status);
        undercoverDao.updateById(entity);
        undercoverCancelDao.updateById(entityCancel);


        //链路
        if( BspApproceStatusEnum.PASSED_END.equals(bspApproceStatusEnum)){
            PrisonerVwRespVO prisoner = prisonerService.getPrisonerByJgrybm( entity.getJgrybm());
            busTraceService.saveBusTrace(BusTypeEnum.YEWU_XXYCX, GjBusTraceUtil.buildUndercoverCanalBusTraceContent(entityCancel,entity,prisoner.getXm()),
                    entity.getJgrybm(),
                    SessionUserUtil.getSessionUser().getOrgCode(),
                    entity.getId());
        }

    }

}
