package com.rs.module.acp.controller.admin.ds.sjbs;

import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.DailyDataSubmitJlsRespVO;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.DailyDataSubmitKssRespVO;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.DailyDataSubmitKssSaveReqVO;
import com.rs.module.acp.entity.ds.sjbs.DailyDataSubmitJlsDO;
import com.rs.module.acp.service.ds.sjbs.DailyDataSubmitJlsService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.ds.sjbs.DailyDataSubmitKssDO;
import com.rs.module.acp.service.ds.sjbs.DailyDataSubmitKssService;

@Api(tags = "实战平台-数据固化-每日数据报送(看守所)")
@RestController
@RequestMapping("/acp/ds/dailyDataSubmitKss")
@Validated
public class DailyDataSubmitKssController {

    @Resource
    private DailyDataSubmitKssService dailyDataSubmitKssService;
    @Resource
    private DailyDataSubmitJlsService dailyDataSubmitJlsService;
    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-数据固化-每日数据报送(看守所)")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<DailyDataSubmitKssRespVO> getDailyDataSubmitKss(@RequestParam("id") String id) {
        DailyDataSubmitKssDO dailyDataSubmitKss = dailyDataSubmitKssService.getDailyDataSubmitKss(id);
        return success(BeanUtils.toBean(dailyDataSubmitKss, DailyDataSubmitKssRespVO.class));
    }
    @GetMapping("/getBySolidificationDate")
    @ApiOperation(value = "获得实战平台-数据固化-每日数据报送(看守所)")
    @ApiImplicitParam(name = "solidificationDate", value = "日期默认昨天")
    public CommonResult<DailyDataSubmitKssRespVO> getBySolidificationDate(
            @RequestParam(value = "solidificationDate",required = false) String solidificationDate) {
        DailyDataSubmitKssDO dailyDataSubmitKss = dailyDataSubmitKssService.getDailyDataSubmitKssBySolidificationDate(solidificationDate, SessionUserUtil.getSessionUser().getOrgCode());
        return success(BeanUtils.toBean(dailyDataSubmitKss, DailyDataSubmitKssRespVO.class));
    }

    @GetMapping("/test")
    @ApiOperation(value = "测试实战平台-数据固化-每日数据报送(看守所)")
    public CommonResult<String> test() {
        dailyDataSubmitKssService.saveForStatistic(null, null,null);
        dailyDataSubmitJlsService.saveForStatistic(null, null,null);
        return success();
    }
}
