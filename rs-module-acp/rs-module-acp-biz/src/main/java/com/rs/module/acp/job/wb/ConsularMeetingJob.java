package com.rs.module.acp.job.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.module.acp.entity.wb.ConsularMeetingDO;
import com.rs.module.acp.entity.wb.LawyerMeetingDO;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.acp.service.wb.ConsularMeetingService;
import com.rs.module.acp.service.wb.LawyerMeetingService;
import com.rs.module.acp.service.wb.WbCommonService;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.sys.MsgAddVO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.util.MsgUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class ConsularMeetingJob {

    @Autowired
    private ConsularMeetingService consularMeetingService;

    @Autowired
    private WbCommonService wbCommonService;

    /**
     * 会见登记24小时内，状态未变更为【已完成】，则为异常
     */
    @XxlJob("abnormalConsularMeeting")
    public void abnormalConsularMeeting(){

        XxlJobHelper.log("领事会见-会见异常：-----开始-------");
        XxlJobHelper.log("领事会见-会见异常：会见登记24小时内，状态未变更为【已完成】，则为异常");
        try {
            Date queryTime = DateUtil.offsetHour(new Date(),-8);
            LambdaQueryWrapper<ConsularMeetingDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.select(ConsularMeetingDO::getId,ConsularMeetingDO::getJgrybm,
                    ConsularMeetingDO::getApplyMeetingStartTime);
            lambdaQueryWrapper.in(ConsularMeetingDO::getStatus, Arrays.asList("0","1","2","3"))
                    .le(ConsularMeetingDO::getApplyMeetingEndTime,queryTime);
            Integer pageNo = 1;
            Integer pageSize = 500;
            while (true){
                Page<ConsularMeetingDO> page = new Page<>(pageNo,pageSize);
                IPage<ConsularMeetingDO> consularMeetingDOIPage = consularMeetingService.page(page,lambdaQueryWrapper);
                if(CollectionUtil.isEmpty(consularMeetingDOIPage.getRecords())){
                    break;
                }
                List<String> idList = consularMeetingDOIPage.getRecords().stream().map(ConsularMeetingDO::getId).collect(Collectors.toList());
                LambdaUpdateWrapper<ConsularMeetingDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.in(ConsularMeetingDO::getId,idList).set(ConsularMeetingDO::getStatus,"99");
                consularMeetingService.update(lambdaUpdateWrapper);

                //发送消息
                for(ConsularMeetingDO consularMeetingDO:consularMeetingDOIPage.getRecords()){
                    XxlJobHelper.log("领事会见：-----发送补录待办消息---start----");
                    wbCommonService.additionalRecordingReminder(JSONObject.parseObject(JSON.toJSONString(consularMeetingDO)), WbConstants.BUSINESS_TYPE_CONSULAR_MEETING);
                    XxlJobHelper.log("领事会见：-----发送补录待办消息----end---");
                }

            }
        }catch (Exception e){
            e.printStackTrace();
            XxlJobHelper.log("领事会见-会见异常任务异常：{}",e.getMessage());
        }
        XxlJobHelper.log("领事会见-会见异常任务:-----结束------");
    }
}
