package com.rs.module.acp.dao.pi;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypeItemListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypeItemPageReqVO;
import com.rs.module.acp.entity.pi.PrisonEventTypeItemDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-巡视管控-所情事件类型明细项 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PrisonEventTypeItemDao extends IBaseDao<PrisonEventTypeItemDO> {


    default PageResult<PrisonEventTypeItemDO> selectPage(PrisonEventTypeItemPageReqVO reqVO) {
        Page<PrisonEventTypeItemDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrisonEventTypeItemDO> wrapper = new LambdaQueryWrapperX<PrisonEventTypeItemDO>()
            .likeIfPresent(PrisonEventTypeItemDO::getItemName, reqVO.getItemName())
            .eqIfPresent(PrisonEventTypeItemDO::getParentId, reqVO.getParentId())
            .eqIfPresent(PrisonEventTypeItemDO::getLevelCode, reqVO.getLevelCode())
            .eqIfPresent(PrisonEventTypeItemDO::getDeductPoint, reqVO.getDeductPoint())
            .eqIfPresent(PrisonEventTypeItemDO::getOrderId, reqVO.getOrderId())
            .eqIfPresent(PrisonEventTypeItemDO::getTypeId, reqVO.getTypeId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PrisonEventTypeItemDO::getAddTime);
        }
        Page<PrisonEventTypeItemDO> prisonEventTypeItemPage = selectPage(page, wrapper);
        return new PageResult<>(prisonEventTypeItemPage.getRecords(), prisonEventTypeItemPage.getTotal());
    }
    default List<PrisonEventTypeItemDO> selectList(PrisonEventTypeItemListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrisonEventTypeItemDO>()
            .likeIfPresent(PrisonEventTypeItemDO::getItemName, reqVO.getItemName())
            .eqIfPresent(PrisonEventTypeItemDO::getParentId, reqVO.getParentId())
            .eqIfPresent(PrisonEventTypeItemDO::getLevelCode, reqVO.getLevelCode())
            .eqIfPresent(PrisonEventTypeItemDO::getDeductPoint, reqVO.getDeductPoint())
            .eqIfPresent(PrisonEventTypeItemDO::getOrderId, reqVO.getOrderId())
            .eqIfPresent(PrisonEventTypeItemDO::getTypeId, reqVO.getTypeId())
        .orderByDesc(PrisonEventTypeItemDO::getAddTime));    }


    }
