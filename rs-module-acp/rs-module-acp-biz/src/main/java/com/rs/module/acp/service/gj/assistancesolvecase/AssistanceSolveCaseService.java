package com.rs.module.acp.service.gj.assistancesolvecase;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.assistancesolvecase.*;
import com.rs.module.acp.entity.gj.AssistanceSolveCaseDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-协助破案 Service 接口
 *
 * <AUTHOR>
 */
public interface AssistanceSolveCaseService extends IBaseService<AssistanceSolveCaseDO>{

    /**
     * 创建实战平台-管教业务-协助破案
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createAssistanceSolveCase(@Valid AssistanceSolveCaseSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-协助破案
     *
     * @param updateReqVO 更新信息
     */
    void updateAssistanceSolveCase(@Valid AssistanceSolveCaseSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-协助破案
     *
     * @param id 编号
     */
    void deleteAssistanceSolveCase(String id);

    /**
     * 获得实战平台-管教业务-协助破案
     *
     * @param id 编号
     * @return 实战平台-管教业务-协助破案
     */
    AssistanceSolveCaseDO getAssistanceSolveCase(String id);

    /**
    * 获得实战平台-管教业务-协助破案分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-协助破案分页
    */
    PageResult<AssistanceSolveCaseDO> getAssistanceSolveCasePage(AssistanceSolveCasePageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-协助破案列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-协助破案列表
    */
    List<AssistanceSolveCaseDO> getAssistanceSolveCaseList(AssistanceSolveCaseListReqVO listReqVO);


    String clueRegist(AssistanceSolveCaseClueRegistReqVO createReqVO);

    void clueApproval(AssistanceSolveCaseClueApprovalReqVO approvalReqVO);

    void clueForward(AssistanceSolveCaseForwardReqVO caseForwardReqVO);

    void clueFeedback(AssistanceSolveCaseFeedbackReqVO feedbackReqVO);
}
