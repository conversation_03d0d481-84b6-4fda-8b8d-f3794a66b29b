package com.rs.module.acp.service.gj.edurehabcourses;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabTimeSlotListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabTimeSlotPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabTimeSlotSaveReqVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.Duration;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import com.rs.module.acp.entity.gj.EdurehabTimeSlotDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.EdurehabTimeSlotDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-教育康复课程时段 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EdurehabTimeSlotServiceImpl extends BaseServiceImpl<EdurehabTimeSlotDao, EdurehabTimeSlotDO> implements EdurehabTimeSlotService {

    @Resource
    private EdurehabTimeSlotDao edurehabTimeSlotDao;

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");

    @Override
    public String createEdurehabTimeSlot(EdurehabTimeSlotSaveReqVO createReqVO) {
        // 插入
        EdurehabTimeSlotDO edurehabTimeSlotDO = reqVO2EdurehabTimeSlotDO(createReqVO);
        edurehabTimeSlotDao.insert(edurehabTimeSlotDO);
        // 返回
        return edurehabTimeSlotDO.getId();
    }

    @Override
    public void updateEdurehabTimeSlot(EdurehabTimeSlotSaveReqVO updateReqVO) {
        // 校验存在
        validateEdurehabTimeSlotExists(updateReqVO.getId());
        // 更新
        EdurehabTimeSlotDO updateObj = BeanUtils.toBean(updateReqVO, EdurehabTimeSlotDO.class);
        edurehabTimeSlotDao.updateById(updateObj);
    }

    @Override
    public void deleteEdurehabTimeSlot(String id) {
        // 校验存在
        validateEdurehabTimeSlotExists(id);
        // 删除
        edurehabTimeSlotDao.deleteById(id);
    }

    private void validateEdurehabTimeSlotExists(String id) {
        if (edurehabTimeSlotDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-教育康复课程时段数据不存在");
        }
    }

    @Override
    public EdurehabTimeSlotDO getEdurehabTimeSlot(String id) {
        return edurehabTimeSlotDao.selectById(id);
    }

    @Override
    public PageResult<EdurehabTimeSlotDO> getEdurehabTimeSlotPage(EdurehabTimeSlotPageReqVO pageReqVO) {
        return edurehabTimeSlotDao.selectPage(pageReqVO);
    }

    @Override
    public List<EdurehabTimeSlotDO> getEdurehabTimeSlotList(EdurehabTimeSlotListReqVO listReqVO) {
        return edurehabTimeSlotDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCreate(List<EdurehabTimeSlotSaveReqVO> list) {
        if (CollectionUtil.isNotEmpty(list)) {
            List<EdurehabTimeSlotDO> checkList = new ArrayList<>();
            for (EdurehabTimeSlotSaveReqVO reqVO : list) {
                EdurehabTimeSlotDO edurehabTimeSlotDO = reqVO2EdurehabTimeSlotDO(reqVO);
                checkList.add(edurehabTimeSlotDO);
            }
            checkList.sort(Comparator.comparing(EdurehabTimeSlotDO::getStartTime));
            if (checkList.size() > 1) {
                for (int i = 1; i < checkList.size(); i++) {
                    if (checkList.get(i - 1).getEndTime().compareTo(checkList.get(i).getStartTime()) > 0) {
                        throw new ServerException("时间段有交叉，请检查");
                    }
                }
            }
            SessionUser sessionUser = SessionUserUtil.getSessionUser();
            LambdaQueryWrapper<EdurehabTimeSlotDO> wrapper = Wrappers.lambdaQuery(EdurehabTimeSlotDO.class).eq(EdurehabTimeSlotDO::getOrgCode, sessionUser.getOrgCode());
            edurehabTimeSlotDao.delete(wrapper);
            edurehabTimeSlotDao.insertBatch(checkList);
        }
    }

    private EdurehabTimeSlotDO reqVO2EdurehabTimeSlotDO(EdurehabTimeSlotSaveReqVO createReqVO) {
        // 插入
        LocalTime startTime = LocalTime.parse(createReqVO.getStartTime());
        LocalTime endTime = LocalTime.parse(createReqVO.getEndTime());
        if (startTime.compareTo(endTime) >= 0) {
            throw new RuntimeException("开始时间不能大于结束时间");
        }

//        int sh = startTime.getHour();
//        int sm = startTime.getMinute();
//        int eh = endTime.getHour();
//        int em = endTime.getMinute();
        Duration between = Duration.between(startTime, endTime);
        EdurehabTimeSlotDO edurehabTimeSlot = new EdurehabTimeSlotDO();
        edurehabTimeSlot.setStartTime(createReqVO.getStartTime());
        edurehabTimeSlot.setEndTime(createReqVO.getEndTime());
        edurehabTimeSlot.setTimeSlotCode(createReqVO.getStartTime() + "-" + createReqVO.getEndTime());
//        edurehabTimeSlot.setTimeSlotCode(String.format("%02d", sh) + ":" + String.format("%02d", sm) + "-" +
//                String.format("%02d", eh) + ":" + String.format("%02d", em));
        edurehabTimeSlot.setDurationMinutes((int) between.toMinutes());
        return edurehabTimeSlot;
    }

}
