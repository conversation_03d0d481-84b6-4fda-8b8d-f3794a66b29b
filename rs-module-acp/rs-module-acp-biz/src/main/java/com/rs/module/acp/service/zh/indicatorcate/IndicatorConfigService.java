package com.rs.module.acp.service.zh.indicatorcate;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorConfigListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorConfigPageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorConfigSaveReqVO;
import com.rs.module.acp.entity.zh.IndicatorConfigDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 综合管理-绩效考核截止日期设置 Service 接口
 *
 * <AUTHOR>
 */
public interface IndicatorConfigService extends IBaseService<IndicatorConfigDO>{

    /**
     * 创建综合管理-绩效考核截止日期设置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createIndicatorConfig(@Valid IndicatorConfigSaveReqVO createReqVO);

    /**
     * 更新综合管理-绩效考核截止日期设置
     *
     * @param updateReqVO 更新信息
     */
    void updateIndicatorConfig(@Valid IndicatorConfigSaveReqVO updateReqVO);

    /**
     * 删除综合管理-绩效考核截止日期设置
     *
     * @param id 编号
     */
    void deleteIndicatorConfig(String id);

    /**
     * 获得综合管理-绩效考核截止日期设置
     *
     * @param id 编号
     * @return 综合管理-绩效考核截止日期设置
     */
    IndicatorConfigDO getIndicatorConfig(String id);

    /**
    * 获得综合管理-绩效考核截止日期设置分页
    *
    * @param pageReqVO 分页查询
    * @return 综合管理-绩效考核截止日期设置分页
    */
    PageResult<IndicatorConfigDO> getIndicatorConfigPage(IndicatorConfigPageReqVO pageReqVO);

    /**
    * 获得综合管理-绩效考核截止日期设置列表
    *
    * @param listReqVO 查询条件
    * @return 综合管理-绩效考核截止日期设置列表
    */
    List<IndicatorConfigDO> getIndicatorConfigList(IndicatorConfigListReqVO listReqVO);


    IndicatorConfigDO getIndicatorConfigByOrgCode(String orgCode);
}
