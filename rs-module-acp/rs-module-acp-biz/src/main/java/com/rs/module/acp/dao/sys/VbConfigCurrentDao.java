package com.rs.module.acp.dao.sys;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.sys.VbConfigCurrentDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.sys.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-语音播报-即时配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface VbConfigCurrentDao extends IBaseDao<VbConfigCurrentDO> {


    default PageResult<VbConfigCurrentDO> selectPage(VbConfigCurrentPageReqVO reqVO) {
        Page<VbConfigCurrentDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<VbConfigCurrentDO> wrapper = new LambdaQueryWrapperX<VbConfigCurrentDO>()
            .likeIfPresent(VbConfigCurrentDO::getVbName, reqVO.getVbName())
            .eqIfPresent(VbConfigCurrentDO::getContent, reqVO.getContent())
            .eqIfPresent(VbConfigCurrentDO::getVbNum, reqVO.getVbNum())
            .eqIfPresent(VbConfigCurrentDO::getPriority, reqVO.getPriority())
            .betweenIfPresent(VbConfigCurrentDO::getStartTime, reqVO.getStartTime())
            .betweenIfPresent(VbConfigCurrentDO::getEndTime, reqVO.getEndTime())
            .eqIfPresent(VbConfigCurrentDO::getIsEnabled, reqVO.getIsEnabled())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(VbConfigCurrentDO::getAddTime);
        }
        Page<VbConfigCurrentDO> vbConfigCurrentPage = selectPage(page, wrapper);
        return new PageResult<>(vbConfigCurrentPage.getRecords(), vbConfigCurrentPage.getTotal());
    }
    default List<VbConfigCurrentDO> selectList(VbConfigCurrentListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<VbConfigCurrentDO>()
            .likeIfPresent(VbConfigCurrentDO::getVbName, reqVO.getVbName())
            .eqIfPresent(VbConfigCurrentDO::getContent, reqVO.getContent())
            .eqIfPresent(VbConfigCurrentDO::getVbNum, reqVO.getVbNum())
            .eqIfPresent(VbConfigCurrentDO::getPriority, reqVO.getPriority())
            .betweenIfPresent(VbConfigCurrentDO::getStartTime, reqVO.getStartTime())
            .betweenIfPresent(VbConfigCurrentDO::getEndTime, reqVO.getEndTime())
            .eqIfPresent(VbConfigCurrentDO::getIsEnabled, reqVO.getIsEnabled())
        .orderByDesc(VbConfigCurrentDO::getAddTime));    }


    }
