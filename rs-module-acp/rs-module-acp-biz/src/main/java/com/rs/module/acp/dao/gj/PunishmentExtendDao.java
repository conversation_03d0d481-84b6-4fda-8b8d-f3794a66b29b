package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentExtendListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentExtendPageReqVO;
import com.rs.module.acp.entity.gj.PunishmentExtendDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务延长处罚呈批 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PunishmentExtendDao extends IBaseDao<PunishmentExtendDO> {


    default PageResult<PunishmentExtendDO> selectPage(PunishmentExtendPageReqVO reqVO) {
        Page<PunishmentExtendDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PunishmentExtendDO> wrapper = new LambdaQueryWrapperX<PunishmentExtendDO>()
            .eqIfPresent(PunishmentExtendDO::getPunishmentId, reqVO.getPunishmentId())
            .eqIfPresent(PunishmentExtendDO::getReason, reqVO.getReason())
            .eqIfPresent(PunishmentExtendDO::getExtendDay, reqVO.getExtendDay())
            .eqIfPresent(PunishmentExtendDO::getStatus, reqVO.getStatus())
            .eqIfPresent(PunishmentExtendDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(PunishmentExtendDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(PunishmentExtendDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(PunishmentExtendDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(PunishmentExtendDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(PunishmentExtendDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(PunishmentExtendDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(PunishmentExtendDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(PunishmentExtendDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PunishmentExtendDO::getAddTime);
        }
        Page<PunishmentExtendDO> punishmentExtendPage = selectPage(page, wrapper);
        return new PageResult<>(punishmentExtendPage.getRecords(), punishmentExtendPage.getTotal());
    }
    default List<PunishmentExtendDO> selectList(PunishmentExtendListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PunishmentExtendDO>()
            .eqIfPresent(PunishmentExtendDO::getPunishmentId, reqVO.getPunishmentId())
            .eqIfPresent(PunishmentExtendDO::getReason, reqVO.getReason())
            .eqIfPresent(PunishmentExtendDO::getExtendDay, reqVO.getExtendDay())
            .eqIfPresent(PunishmentExtendDO::getStatus, reqVO.getStatus())
            .eqIfPresent(PunishmentExtendDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(PunishmentExtendDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(PunishmentExtendDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(PunishmentExtendDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(PunishmentExtendDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(PunishmentExtendDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(PunishmentExtendDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(PunishmentExtendDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(PunishmentExtendDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(PunishmentExtendDO::getAddTime));    }


    }
