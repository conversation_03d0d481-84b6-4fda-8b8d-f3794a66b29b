package com.rs.module.acp.dao.gj;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalConfigListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalConfigPageReqVO;
import com.rs.module.acp.entity.gj.BookingApprovalConfigDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 实战平台-管教业务-预约审核配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BookingApprovalConfigDao extends IBaseDao<BookingApprovalConfigDO> {


    default PageResult<BookingApprovalConfigDO> selectPage(BookingApprovalConfigPageReqVO reqVO) {
        Page<BookingApprovalConfigDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<BookingApprovalConfigDO> wrapper = new LambdaQueryWrapperX<BookingApprovalConfigDO>()
            .eqIfPresent(BookingApprovalConfigDO::getBookingCategory, reqVO.getBookingCategory())
            .eqIfPresent(BookingApprovalConfigDO::getServiceCategory, reqVO.getServiceCategory())
            .eqIfPresent(BookingApprovalConfigDO::getIsEnabled, reqVO.getIsEnabled())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(BookingApprovalConfigDO::getAddTime);
        }
        Page<BookingApprovalConfigDO> bookingApprovalConfigPage = selectPage(page, wrapper);
        return new PageResult<>(bookingApprovalConfigPage.getRecords(), bookingApprovalConfigPage.getTotal());
    }
    default List<BookingApprovalConfigDO> selectList(BookingApprovalConfigListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BookingApprovalConfigDO>()
            .eqIfPresent(BookingApprovalConfigDO::getBookingCategory, reqVO.getBookingCategory())
            .eqIfPresent(BookingApprovalConfigDO::getServiceCategory, reqVO.getServiceCategory())
            .eqIfPresent(BookingApprovalConfigDO::getIsEnabled, reqVO.getIsEnabled())
                .eqIfPresent(BookingApprovalConfigDO::getOrgCode, reqVO.getOrgCode())
                .orderByDesc(BookingApprovalConfigDO::getAddTime));    }


    }
