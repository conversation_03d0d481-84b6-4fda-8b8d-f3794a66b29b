package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.module.acp.entity.wb.CasePersonnelDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.CoopCasePersonnelDO;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.CoopCasePersonnelDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-协同办案人员 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CoopCasePersonnelServiceImpl extends BaseServiceImpl<CoopCasePersonnelDao, CoopCasePersonnelDO> implements CoopCasePersonnelService {

    @Resource
    private CoopCasePersonnelDao coopCasePersonnelDao;

    @Autowired
    private WbCommonService wbCommonService;

    @Override
    public String createCoopCasePersonnel(CoopCasePersonnelSaveReqVO createReqVO) {
        // 插入
        CoopCasePersonnelDO coopCasePersonnel = BeanUtils.toBean(createReqVO, CoopCasePersonnelDO.class);
        coopCasePersonnelDao.insert(coopCasePersonnel);
        // 返回
        return coopCasePersonnel.getId();
    }

    @Override
    public void updateCoopCasePersonnel(CoopCasePersonnelSaveReqVO updateReqVO) {
        // 校验存在
        validateCoopCasePersonnelExists(updateReqVO.getId());
        // 更新
        CoopCasePersonnelDO updateObj = BeanUtils.toBean(updateReqVO, CoopCasePersonnelDO.class);
        coopCasePersonnelDao.updateById(updateObj);
    }

    @Override
    public void deleteCoopCasePersonnel(String id) {
        // 校验存在
        validateCoopCasePersonnelExists(id);
        // 删除
        coopCasePersonnelDao.deleteById(id);
    }

    private void validateCoopCasePersonnelExists(String id) {
        if (coopCasePersonnelDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-协同办案人员数据不存在");
        }
    }

    @Override
    public CoopCasePersonnelDO getCoopCasePersonnel(String id) {
        return coopCasePersonnelDao.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCoopCasePersonnelList(List<CoopCasePersonnelSaveReqVO> coopCasePersonnelList,String targetId) {
        if(CollectionUtil.isEmpty(coopCasePersonnelList)){
            return true;
        }
        List<CoopCasePersonnelDO> casePersonnelDOList = new ArrayList<>();
        for(CoopCasePersonnelSaveReqVO saveReqVO:coopCasePersonnelList){
            CoopCasePersonnelDO temp = BeanUtils.toBean(saveReqVO,CoopCasePersonnelDO.class);
            temp.setArraignmentId(targetId);
            if(ObjectUtil.isNotEmpty(temp.getGzzjUrl())){
                temp.setGzzjUrl(wbCommonService.saveFile(null,temp.getGzzjUrl()));
            }
            temp.setTjjglx(saveReqVO.getOrgType());
        }

        return saveBatch(casePersonnelDOList);
    }

    @Override
    public List<CoopCasePersonnelRespVO> getListByTargetId(String targetId) {
        List<CoopCasePersonnelDO> coopCasePersonnelDOList = list(new LambdaQueryWrapper<CoopCasePersonnelDO>()
                .select(CoopCasePersonnelDO::getZjhm, CoopCasePersonnelDO::getXm,CoopCasePersonnelDO::getBadwdm,CoopCasePersonnelDO::getBadwmc,
                        CoopCasePersonnelDO::getZjlx,CoopCasePersonnelDO::getLxfs,CoopCasePersonnelDO::getZpUrl,
                        CoopCasePersonnelDO::getGzzjUrl,CoopCasePersonnelDO::getId)
                .eq(CoopCasePersonnelDO::getArraignmentId, targetId));
        List<CoopCasePersonnelRespVO> casePersonnelRespVOList = BeanUtils.toBean(coopCasePersonnelDOList,CoopCasePersonnelRespVO.class);

        for(CoopCasePersonnelRespVO personnelRespVO:casePersonnelRespVOList){
            if(ObjectUtil.isNotEmpty(personnelRespVO.getGzzjUrl())){
                personnelRespVO.setGzzjUrl(wbCommonService.getFile(personnelRespVO.getGzzjUrl()));
            }
            for(CoopCasePersonnelDO casePersonnelDO:coopCasePersonnelDOList){
                if(casePersonnelDO.getId().equals(personnelRespVO.getId())){
                    personnelRespVO.setOrgType(casePersonnelDO.getTjjglx());
                    break;
                }
            }
            personnelRespVO.setDisdicName(getTjjglxDic(personnelRespVO.getOrgType()));
        }
        return casePersonnelRespVOList;
    }

    private String getTjjglxDic(String tjjglx){
        String dicName = null;
        if("1".equals(tjjglx)){
            dicName = "ZD_BADW_GAJG";
        }else if("2".equals(tjjglx)){
            dicName ="ZD_BADW_JCY";
        }else if("3".equals(tjjglx)){
            dicName = "ZD_BADW_FY";
        }else if("4".equals(tjjglx)){
            dicName = "ZD_BADW_AQJG";
        }
        return dicName;
    }
}
