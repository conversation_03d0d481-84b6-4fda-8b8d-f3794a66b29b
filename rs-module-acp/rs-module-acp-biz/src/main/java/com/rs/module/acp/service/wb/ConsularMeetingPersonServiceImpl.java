package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.ConsularMeetingPersonDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.ConsularMeetingPersonDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-领事会见人员 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ConsularMeetingPersonServiceImpl extends BaseServiceImpl<ConsularMeetingPersonDao, ConsularMeetingPersonDO> implements ConsularMeetingPersonService {

    @Resource
    private ConsularMeetingPersonDao consularMeetingPersonDao;

    @Autowired
    private WbCommonService wbCommonService;

    @Override
    public String createConsularMeetingPerson(ConsularMeetingPersonSaveReqVO createReqVO) {
        // 插入
        ConsularMeetingPersonDO consularMeetingPerson = BeanUtils.toBean(createReqVO, ConsularMeetingPersonDO.class);
        consularMeetingPersonDao.insert(consularMeetingPerson);
        // 返回
        return consularMeetingPerson.getId();
    }

    @Override
    public void updateConsularMeetingPerson(ConsularMeetingPersonSaveReqVO updateReqVO) {
        // 校验存在
        validateConsularMeetingPersonExists(updateReqVO.getId());
        // 更新
        ConsularMeetingPersonDO updateObj = BeanUtils.toBean(updateReqVO, ConsularMeetingPersonDO.class);
        consularMeetingPersonDao.updateById(updateObj);
    }

    @Override
    public void deleteConsularMeetingPerson(String id) {
        // 校验存在
        validateConsularMeetingPersonExists(id);
        // 删除
        consularMeetingPersonDao.deleteById(id);
    }

    private void validateConsularMeetingPersonExists(String id) {
        if (consularMeetingPersonDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-领事会见人员数据不存在");
        }
    }

    @Override
    public ConsularMeetingPersonDO getConsularMeetingPerson(String id) {
        return consularMeetingPersonDao.selectById(id);
    }

    @Override
    public PageResult<ConsularMeetingPersonDO> getConsularMeetingPersonPage(ConsularMeetingPersonPageReqVO pageReqVO) {
        return consularMeetingPersonDao.selectPage(pageReqVO);
    }

    @Override
    public List<ConsularMeetingPersonDO> getConsularMeetingPersonList(ConsularMeetingPersonListReqVO listReqVO) {
        return consularMeetingPersonDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveConsularMeetingPersonList(List<ConsularMeetingPersonSaveReqVO> meetingPersonList, String consularMeetingId) {
        List<ConsularMeetingPersonDO> personDOList = BeanUtils.toBean(meetingPersonList,ConsularMeetingPersonDO.class);
        for(ConsularMeetingPersonDO personDO:personDOList){
            if(ObjectUtil.isNotEmpty(personDO.getGzzjUrl())){
                personDO.setGzzjUrl(wbCommonService.saveFile(null,personDO.getGzzjUrl()));
            }
            personDO.setConsularMeetingId(consularMeetingId);
        }
        return saveBatch(personDOList);
    }

    @Override
    public List<ConsularMeetingPersonRespVO> getMeetingPersonListByConsularMeetingId(String consularMeetingId) {
        LambdaQueryWrapper<ConsularMeetingPersonDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(ConsularMeetingPersonDO::getId,ConsularMeetingPersonDO::getRylx,
                ConsularMeetingPersonDO::getJh,ConsularMeetingPersonDO::getXm,ConsularMeetingPersonDO::getXb,
                ConsularMeetingPersonDO::getZjlx,ConsularMeetingPersonDO::getZjhm,ConsularMeetingPersonDO::getBadwdm,
                ConsularMeetingPersonDO::getBadwmc,ConsularMeetingPersonDO::getLxfs,ConsularMeetingPersonDO::getZpUrl,
                ConsularMeetingPersonDO::getGzzjUrl,ConsularMeetingPersonDO::getGj,ConsularMeetingPersonDO::getWorkUnit);
        lambdaQueryWrapper.eq(ConsularMeetingPersonDO::getConsularMeetingId,consularMeetingId);
        List<ConsularMeetingPersonDO> personDOList = list(lambdaQueryWrapper);
        if(CollectionUtil.isEmpty(personDOList)){
            return new ArrayList<>();
        }
        List<ConsularMeetingPersonRespVO> personRespVOList = BeanUtils.toBean(personDOList,ConsularMeetingPersonRespVO.class);
        for(ConsularMeetingPersonRespVO personRespVO:personRespVOList){
            if(ObjectUtil.isNotEmpty(personRespVO.getGzzjUrl())){
                personRespVO.setGzzjUrl(wbCommonService.getFile(personRespVO.getGzzjUrl()));
            }
        }
        return personRespVOList;
    }
}
