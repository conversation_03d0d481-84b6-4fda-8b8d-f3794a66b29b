package com.rs.module.acp.service.gj.punishment;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentRemoveListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentRemovePageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentRemoveSaveReqVO;
import com.rs.module.acp.dao.gj.PunishmentRemoveDao;
import com.rs.module.acp.entity.gj.PunishmentRemoveDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 实战平台-管教业务解除处罚呈批 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PunishmentRemoveServiceImpl extends BaseServiceImpl<PunishmentRemoveDao, PunishmentRemoveDO> implements PunishmentRemoveService {

    @Resource
    private PunishmentRemoveDao punishmentRemoveDao;

    @Override
    public String createPunishmentRemove(PunishmentRemoveSaveReqVO createReqVO) {
        // 插入
        PunishmentRemoveDO punishmentRemove = BeanUtils.toBean(createReqVO, PunishmentRemoveDO.class);
        punishmentRemoveDao.insert(punishmentRemove);
        // 返回
        return punishmentRemove.getId();
    }

    @Override
    public void updatePunishmentRemove(PunishmentRemoveSaveReqVO updateReqVO) {
        // 校验存在
        validatePunishmentRemoveExists(updateReqVO.getId());
        // 更新
        PunishmentRemoveDO updateObj = BeanUtils.toBean(updateReqVO, PunishmentRemoveDO.class);
        punishmentRemoveDao.updateById(updateObj);
    }

    @Override
    public void deletePunishmentRemove(String id) {
        // 校验存在
        validatePunishmentRemoveExists(id);
        // 删除
        punishmentRemoveDao.deleteById(id);
    }

    private void validatePunishmentRemoveExists(String id) {
        if (punishmentRemoveDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务解除处罚呈批数据不存在");
        }
    }

    @Override
    public PunishmentRemoveDO getPunishmentRemove(String id) {
        return punishmentRemoveDao.selectById(id);
    }

    @Override
    public PageResult<PunishmentRemoveDO> getPunishmentRemovePage(PunishmentRemovePageReqVO pageReqVO) {
        return punishmentRemoveDao.selectPage(pageReqVO);
    }

    @Override
    public List<PunishmentRemoveDO> getPunishmentRemoveList(PunishmentRemoveListReqVO listReqVO) {
        return punishmentRemoveDao.selectList(listReqVO);
    }


}
