package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.LawyerMeetingCompanionDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.LawyerMeetingCompanionDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-律师会见同行登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LawyerMeetingCompanionServiceImpl extends BaseServiceImpl<LawyerMeetingCompanionDao, LawyerMeetingCompanionDO> implements LawyerMeetingCompanionService {

    @Resource
    private LawyerMeetingCompanionDao lawyerMeetingCompanionDao;
    @Autowired
    private WbCommonService wbCommonService;

    @Override
    public String createLawyerMeetingCompanion(LawyerMeetingCompanionSaveReqVO createReqVO) {
        // 插入
        LawyerMeetingCompanionDO lawyerMeetingCompanion = BeanUtils.toBean(createReqVO, LawyerMeetingCompanionDO.class);
        lawyerMeetingCompanionDao.insert(lawyerMeetingCompanion);
        // 返回
        return lawyerMeetingCompanion.getId();
    }

    @Override
    public void updateLawyerMeetingCompanion(LawyerMeetingCompanionSaveReqVO updateReqVO) {
        // 校验存在
        validateLawyerMeetingCompanionExists(updateReqVO.getId());
        // 更新
        LawyerMeetingCompanionDO updateObj = BeanUtils.toBean(updateReqVO, LawyerMeetingCompanionDO.class);
        lawyerMeetingCompanionDao.updateById(updateObj);
    }

    @Override
    public void deleteLawyerMeetingCompanion(String id) {
        // 校验存在
        validateLawyerMeetingCompanionExists(id);
        // 删除
        lawyerMeetingCompanionDao.deleteById(id);
    }

    private void validateLawyerMeetingCompanionExists(String id) {
        if (lawyerMeetingCompanionDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-律师会见同行登记数据不存在");
        }
    }

    @Override
    public LawyerMeetingCompanionDO getLawyerMeetingCompanion(String id) {
        return lawyerMeetingCompanionDao.selectById(id);
    }

    @Override
    public PageResult<LawyerMeetingCompanionDO> getLawyerMeetingCompanionPage(LawyerMeetingCompanionPageReqVO pageReqVO) {
        return lawyerMeetingCompanionDao.selectPage(pageReqVO);
    }

    @Override
    public List<LawyerMeetingCompanionDO> getLawyerMeetingCompanionList(LawyerMeetingCompanionListReqVO listReqVO) {
        return lawyerMeetingCompanionDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveLawyerMeetingCompanionList(String lawyerMeetingId, List<LawyerMeetingCompanionSaveReqVO> companionList) {
        List<LawyerMeetingCompanionDO> lawyerMeetingCompanionDOList = BeanUtils.toBean(companionList,LawyerMeetingCompanionDO.class);
        for(LawyerMeetingCompanionDO companionDO:lawyerMeetingCompanionDOList){
            companionDO.setLawyerMeetingId(lawyerMeetingId);
            if(ObjectUtil.isNotEmpty(companionDO.getAttachmentUrl())){
                String attachmentUrl = wbCommonService.saveFile(null,companionDO.getAttachmentUrl());
                companionDO.setAttachmentUrl(attachmentUrl);
            }
        }
        return saveBatch(lawyerMeetingCompanionDOList);
    }

    @Override
    public List<LawyerMeetingCompanionRespVO> getCompanionByLawyerMeetingId(String lawyerMeetingId) {

        LambdaQueryWrapper<LawyerMeetingCompanionDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(LawyerMeetingCompanionDO::getName,LawyerMeetingCompanionDO::getCompanionType,
                LawyerMeetingCompanionDO::getGender,LawyerMeetingCompanionDO::getIdCard,LawyerMeetingCompanionDO::getAttachmentUrl);
        lambdaQueryWrapper.eq(LawyerMeetingCompanionDO::getLawyerMeetingId,lawyerMeetingId);
        List<LawyerMeetingCompanionDO> list = list(lambdaQueryWrapper);
        if(CollectionUtil.isEmpty(list)){
            return new ArrayList<>();
        }

        for(LawyerMeetingCompanionDO lawyerMeetingCompanionDO:list){
            if(ObjectUtil.isNotEmpty(lawyerMeetingCompanionDO.getAttachmentUrl())){
                String attachmentUrl = wbCommonService.getFile(lawyerMeetingCompanionDO.getAttachmentUrl());
                lawyerMeetingCompanionDO.setAttachmentUrl(attachmentUrl);
            }
        }

        return BeanUtils.toBean(list,LawyerMeetingCompanionRespVO.class);
    }
}
