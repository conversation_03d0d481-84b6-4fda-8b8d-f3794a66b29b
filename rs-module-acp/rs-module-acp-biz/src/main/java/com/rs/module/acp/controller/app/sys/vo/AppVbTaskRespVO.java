package com.rs.module.acp.controller.app.sys.vo;

import java.util.Date;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-语音播报-待播报任务 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AppVbTaskRespVO extends BaseVO implements TransPojo{
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("播报名称")
    private String vbName;
    @ApiModelProperty("播报类型(0:定时,1:实时")
    private String vbType;
    @ApiModelProperty("播报内容")
    private String content;
    @ApiModelProperty("播报次数")
    private Short vbNum;
    @ApiModelProperty("优先级")
    private Short priority;
    @ApiModelProperty("播报开始时间")
    private Date startTime;
    @ApiModelProperty("播报结束时间")
    private Date endTime;
    @ApiModelProperty("播报静音时段")
    private String silentTimeSlots;
    @ApiModelProperty("是否静音")
    private Boolean isSilent;
}