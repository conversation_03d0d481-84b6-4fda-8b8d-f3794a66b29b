package com.rs.module.acp.controller.admin.db.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-随身物品登记分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PersonalEffectsPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("人员编号")
    private String rybh;

    @ApiModelProperty("人员姓名")
    private String ryxm;

    @ApiModelProperty("登记状态")
    private String status;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
