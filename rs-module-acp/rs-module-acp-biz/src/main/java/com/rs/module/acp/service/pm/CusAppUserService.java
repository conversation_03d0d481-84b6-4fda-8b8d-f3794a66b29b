package com.rs.module.acp.service.pm;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.CusAppUserDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-监管管理-我的应用配置 Service 接口
 *
 * <AUTHOR>
 */
public interface CusAppUserService extends IBaseService<CusAppUserDO>{

    /**
     * 创建实战平台-监管管理-我的应用配置
     *
     * @param ids 创建信息
     * @return 编号
     */
    void createCusAppUser(String ids);

    /**
     * 更新实战平台-监管管理-我的应用配置
     *
     * @param updateReqVO 更新信息
     */
    void updateCusAppUser(@Valid CusAppUserSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-我的应用配置
     *
     * @param id 编号
     */
    void deleteCusAppUser(String id);

    /**
     * 获得实战平台-监管管理-我的应用配置
     *
     * @param id 编号
     * @return 实战平台-监管管理-我的应用配置
     */
    CusAppUserDO getCusAppUser(String id);

    /**
    * 获得实战平台-监管管理-我的应用配置分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-监管管理-我的应用配置分页
    */
    PageResult<CusAppUserDO> getCusAppUserPage(CusAppUserPageReqVO pageReqVO);

    /**
    * 获得实战平台-监管管理-我的应用配置列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-监管管理-我的应用配置列表
    */
    List<CusAppUserDO> getCusAppUserList(CusAppUserListReqVO listReqVO);

    List<Map<String, Object>> getWdyyList(List<String> yyidList,String yylx);


}
