package com.rs.module.acp.controller.admin.db.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(description = "看守所收押登记其他信息VO")
@Data
public class DetainRegKssBiometricVO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("手环ID")
    private String shid;

    @ApiModelProperty("手环绑定状态")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYRSSHBDZT")
    private String shbdzt;

    @ApiModelProperty("生物采集信息")
    private List<BiometricInfoRespVO> biometricList;

}

