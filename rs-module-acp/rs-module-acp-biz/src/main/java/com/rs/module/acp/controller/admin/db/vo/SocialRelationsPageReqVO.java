package com.rs.module.acp.controller.admin.db.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-社会关系分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SocialRelationsPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("人员编号")
    private String rybh;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("性别")
    private String gender;

    @ApiModelProperty("证件类型")
    private String idType;

    @ApiModelProperty("证件号码")
    private String idNumber;

    @ApiModelProperty("社会关系")
    private String relationship;

    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("工作单位")
    private String workUnit;

    @ApiModelProperty("居住地址")
    private String address;

    @ApiModelProperty("职业")
    private String occupation;

    @ApiModelProperty("照片")
    private String imageUrl;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
