package com.rs.module.acp.service.db;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.PersonalEffectsDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-收押业务-随身物品登记 Service 接口
 *
 * <AUTHOR>
 */
public interface PersonalEffectsService extends IBaseService<PersonalEffectsDO>{

    /**
     * 创建实战平台-收押业务-随身物品登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPersonalEffects(@Valid PersonalEffectsSaveReqVO createReqVO);

    /**
     * 更新实战平台-收押业务-随身物品登记
     *
     * @param updateReqVO 更新信息
     */
    void updatePersonalEffects(@Valid PersonalEffectsSaveReqVO updateReqVO);

    /**
     * 删除实战平台-收押业务-随身物品登记
     *
     * @param id 编号
     */
    void deletePersonalEffects(String id);

    /**
     * 获得实战平台-收押业务-随身物品登记
     *
     * @param id 编号
     * @return 实战平台-收押业务-随身物品登记
     */
    PersonalEffectsDO getPersonalEffects(String id);

    /**
    * 获得实战平台-收押业务-随身物品登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-收押业务-随身物品登记分页
    */
    PageResult<PersonalEffectsDO> getPersonalEffectsPage(PersonalEffectsPageReqVO pageReqVO);

    /**
    * 获得实战平台-收押业务-随身物品登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-收押业务-随身物品登记列表
    */
    List<PersonalEffectsDO> getPersonalEffectsList(PersonalEffectsListReqVO listReqVO);


    PersonalEffectsDO getPersonalEffectsByRybh(String rybh);
}
