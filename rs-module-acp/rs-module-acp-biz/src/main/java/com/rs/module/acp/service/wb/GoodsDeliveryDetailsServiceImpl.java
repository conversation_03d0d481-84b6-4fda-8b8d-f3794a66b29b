package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.StringUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.GoodsDeliveryDetailsDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.GoodsDeliveryDetailsDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-物品顾送明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GoodsDeliveryDetailsServiceImpl extends BaseServiceImpl<GoodsDeliveryDetailsDao, GoodsDeliveryDetailsDO> implements GoodsDeliveryDetailsService {

    @Resource
    private GoodsDeliveryDetailsDao goodsDeliveryDetailsDao;

    @Override
    public String createGoodsDeliveryDetails(GoodsDeliveryDetailsSaveReqVO createReqVO) {
        // 插入
        GoodsDeliveryDetailsDO goodsDeliveryDetails = BeanUtils.toBean(createReqVO, GoodsDeliveryDetailsDO.class);
        goodsDeliveryDetailsDao.insert(goodsDeliveryDetails);
        // 返回
        return goodsDeliveryDetails.getId();
    }

    @Override
    public void updateGoodsDeliveryDetails(GoodsDeliveryDetailsSaveReqVO updateReqVO) {
        // 校验存在
        validateGoodsDeliveryDetailsExists(updateReqVO.getId());
        // 更新
        GoodsDeliveryDetailsDO updateObj = BeanUtils.toBean(updateReqVO, GoodsDeliveryDetailsDO.class);
        goodsDeliveryDetailsDao.updateById(updateObj);
    }

    @Override
    public void deleteGoodsDeliveryDetails(String id) {
        // 校验存在
        validateGoodsDeliveryDetailsExists(id);
        // 删除
        goodsDeliveryDetailsDao.deleteById(id);
    }

    private void validateGoodsDeliveryDetailsExists(String id) {
        if (goodsDeliveryDetailsDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-物品顾送明细数据不存在");
        }
    }

    @Override
    public GoodsDeliveryDetailsDO getGoodsDeliveryDetails(String id) {
        return goodsDeliveryDetailsDao.selectById(id);
    }

    @Override
    public PageResult<GoodsDeliveryDetailsDO> getGoodsDeliveryDetailsPage(GoodsDeliveryDetailsPageReqVO pageReqVO) {
        return goodsDeliveryDetailsDao.selectPage(pageReqVO);
    }

    @Override
    public List<GoodsDeliveryDetailsDO> getGoodsDeliveryDetailsList(GoodsDeliveryDetailsListReqVO listReqVO) {
        return goodsDeliveryDetailsDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveGoodsDeliveryDetailsListByGoodsDeliveryId(List<GoodsDeliveryDetailsSaveReqVO> goodsList, String goodsDeliveryId) {

        List<GoodsDeliveryDetailsDO> detailsDOList = BeanUtils.toBean(goodsList,GoodsDeliveryDetailsDO.class);

        detailsDOList.forEach(x->{
            x.setId(StringUtil.getGuid32());
            x.setGoodsDeliveryId(goodsDeliveryId);
        });

        return saveBatch(detailsDOList);
    }

    @Override
    public List<GoodsDeliveryDetailsRespVO> getGoodsDeliveryDetailsListByGoodsDeliveryId(String goodsDeliveryId) {
        LambdaQueryWrapper<GoodsDeliveryDetailsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(GoodsDeliveryDetailsDO::getId,GoodsDeliveryDetailsDO::getGoodsDeliveryId,
                GoodsDeliveryDetailsDO::getGoodsName,GoodsDeliveryDetailsDO::getGoodsQuantity,
                GoodsDeliveryDetailsDO::getUnit,GoodsDeliveryDetailsDO::getRemark);
        lambdaQueryWrapper.eq(GoodsDeliveryDetailsDO::getGoodsDeliveryId,goodsDeliveryId);
        List<GoodsDeliveryDetailsDO> goodList = list(lambdaQueryWrapper);
        return CollectionUtil.isNotEmpty(goodList)?BeanUtils.toBean(goodList,GoodsDeliveryDetailsRespVO.class):new ArrayList<>();
    }
}
