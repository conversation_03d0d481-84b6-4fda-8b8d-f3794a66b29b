package com.rs.module.acp.service.sys;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.sys.vo.*;
import com.rs.module.acp.entity.sys.VbConfigCurrentObjectDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.sys.VbConfigCurrentObjectDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-语音播报-即时播报对象 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class VbConfigCurrentObjectServiceImpl extends BaseServiceImpl<VbConfigCurrentObjectDao, VbConfigCurrentObjectDO> implements VbConfigCurrentObjectService {

    @Resource
    private VbConfigCurrentObjectDao vbConfigCurrentObjectDao;

    @Override
    public String createVbConfigCurrentObject(VbConfigCurrentObjectSaveReqVO createReqVO) {
        // 插入
        VbConfigCurrentObjectDO vbConfigCurrentObject = BeanUtils.toBean(createReqVO, VbConfigCurrentObjectDO.class);
        vbConfigCurrentObjectDao.insert(vbConfigCurrentObject);
        // 返回
        return vbConfigCurrentObject.getId();
    }

    @Override
    public void updateVbConfigCurrentObject(VbConfigCurrentObjectSaveReqVO updateReqVO) {
        // 校验存在
        validateVbConfigCurrentObjectExists(updateReqVO.getId());
        // 更新
        VbConfigCurrentObjectDO updateObj = BeanUtils.toBean(updateReqVO, VbConfigCurrentObjectDO.class);
        vbConfigCurrentObjectDao.updateById(updateObj);
    }

    @Override
    public void deleteVbConfigCurrentObject(String id) {
        // 校验存在
        validateVbConfigCurrentObjectExists(id);
        // 删除
        vbConfigCurrentObjectDao.deleteById(id);
    }

    private void validateVbConfigCurrentObjectExists(String id) {
        if (vbConfigCurrentObjectDao.selectById(id) == null) {
            throw new ServerException("实战平台-语音播报-即时播报对象数据不存在");
        }
    }

    @Override
    public VbConfigCurrentObjectDO getVbConfigCurrentObject(String id) {
        return vbConfigCurrentObjectDao.selectById(id);
    }

    @Override
    public PageResult<VbConfigCurrentObjectDO> getVbConfigCurrentObjectPage(VbConfigCurrentObjectPageReqVO pageReqVO) {
        return vbConfigCurrentObjectDao.selectPage(pageReqVO);
    }

    @Override
    public List<VbConfigCurrentObjectDO> getVbConfigCurrentObjectList(VbConfigCurrentObjectListReqVO listReqVO) {
        return vbConfigCurrentObjectDao.selectList(listReqVO);
    }


}
