package com.rs.module.acp.dao.wb;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.ConsularMeetingCompanionDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-窗口业务-领事外事会见同行登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ConsularMeetingCompanionDao extends IBaseDao<ConsularMeetingCompanionDO> {


    default PageResult<ConsularMeetingCompanionDO> selectPage(ConsularMeetingCompanionPageReqVO reqVO) {
        Page<ConsularMeetingCompanionDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ConsularMeetingCompanionDO> wrapper = new LambdaQueryWrapperX<ConsularMeetingCompanionDO>()
            .eqIfPresent(ConsularMeetingCompanionDO::getConsularMeetingId, reqVO.getConsularMeetingId())
            .likeIfPresent(ConsularMeetingCompanionDO::getName, reqVO.getName())
            .eqIfPresent(ConsularMeetingCompanionDO::getGender, reqVO.getGender())
            .eqIfPresent(ConsularMeetingCompanionDO::getIdCard, reqVO.getIdCard())
            .eqIfPresent(ConsularMeetingCompanionDO::getCompanionType, reqVO.getCompanionType())
            .eqIfPresent(ConsularMeetingCompanionDO::getAttachmentUrl, reqVO.getAttachmentUrl())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(ConsularMeetingCompanionDO::getAddTime);
        }
        Page<ConsularMeetingCompanionDO> consularMeetingCompanionPage = selectPage(page, wrapper);
        return new PageResult<>(consularMeetingCompanionPage.getRecords(), consularMeetingCompanionPage.getTotal());
    }
    default List<ConsularMeetingCompanionDO> selectList(ConsularMeetingCompanionListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ConsularMeetingCompanionDO>()
            .eqIfPresent(ConsularMeetingCompanionDO::getConsularMeetingId, reqVO.getConsularMeetingId())
            .likeIfPresent(ConsularMeetingCompanionDO::getName, reqVO.getName())
            .eqIfPresent(ConsularMeetingCompanionDO::getGender, reqVO.getGender())
            .eqIfPresent(ConsularMeetingCompanionDO::getIdCard, reqVO.getIdCard())
            .eqIfPresent(ConsularMeetingCompanionDO::getCompanionType, reqVO.getCompanionType())
            .eqIfPresent(ConsularMeetingCompanionDO::getAttachmentUrl, reqVO.getAttachmentUrl())
        .orderByDesc(ConsularMeetingCompanionDO::getAddTime));    }


    }
