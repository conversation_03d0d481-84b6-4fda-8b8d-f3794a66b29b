package com.rs.module.acp.controller.admin.db.vo.sugstopdetention;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-建议停拘登记列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SugStopDetentionListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("建停类型（01：收拘时，02：收拘后）")
    private String jtlx;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("姓名")
    private String xm;

    @ApiModelProperty("曾用名")
    private String cym;

    @ApiModelProperty("证件类型")
    private String zjlx;

    @ApiModelProperty("证件号码")
    private String zjhm;

    @ApiModelProperty("性别")
    private String xb;

    @ApiModelProperty("出生日期")
    private Date csrq;

    @ApiModelProperty("国籍")
    private String gj;

    @ApiModelProperty("特殊身份")
    private String tssf;

    @ApiModelProperty("民族")
    private String mz;

    @ApiModelProperty("政治面貌")
    private String zzmm;

    @ApiModelProperty("婚姻状况")
    private String hyzk;

    @ApiModelProperty("文化程度")
    private String whcd;

    @ApiModelProperty("籍贯")
    private String jg;

    @ApiModelProperty("身份")
    private String sf;

    @ApiModelProperty("工作单位")
    private String gzdw;

    @ApiModelProperty("职业")
    private String zy;

    @ApiModelProperty("户籍地")
    private String hjd;

    @ApiModelProperty("户籍地详址")
    private String hjdxz;

    @ApiModelProperty("现住址")
    private String xzz;

    @ApiModelProperty("现住址详址")
    private String xzzxz;

    @ApiModelProperty("决定拘留机关")
    private String jdjljg;

    @ApiModelProperty("办案单位类型")
    private String badwlx;

    @ApiModelProperty("办案单位")
    private String badw;

    @ApiModelProperty("送拘单位")
    private String sjdw;

    @ApiModelProperty("送拘人")
    private String sjr;

    @ApiModelProperty("送拘凭证")
    private String sjpz;

    @ApiModelProperty("送拘法律文书号")
    private String sjflwsh;

    @ApiModelProperty("联系电话")
    private String lxdh;

    @ApiModelProperty("案件类别代码")
    private String ajlbdm;

    @ApiModelProperty("案件类别")
    private String ajlb;

    @ApiModelProperty("送拘民警")
    private String sjmj;

    @ApiModelProperty("收拘类型")
    private String sjlx;

    @ApiModelProperty("拘留时长")
    private Integer jlsc;

    @ApiModelProperty("拘留起始日期")
    private Date jlqsrq;

    @ApiModelProperty("拘留截止日期")
    private Date jljzrq;

    @ApiModelProperty("简要案情")
    private String jyaq;

    @ApiModelProperty("备注")
    private String bz;

    @ApiModelProperty("身高")
    private Integer sg;

    @ApiModelProperty("体重")
    private Integer tz;

    @ApiModelProperty("足长")
    private Integer zc;

    @ApiModelProperty("体温")
    private Integer tw;

    @ApiModelProperty("血压")
    private Integer xy;

    @ApiModelProperty("脉搏")
    private Integer mb;

    @ApiModelProperty("心肺")
    private String xf;

    @ApiModelProperty("心率")
    private Integer xl;

    @ApiModelProperty("口音")
    private String ky;

    @ApiModelProperty("血型")
    private String xx;

    @ApiModelProperty("体型")
    private String tx;

    @ApiModelProperty("脸型")
    private String lx;

    @ApiModelProperty("语言表达能力")
    private String yybdnl;

    @ApiModelProperty("行走状况")
    private String xzzk;

    @ApiModelProperty("健康状况")
    private String jkzk;

    @ApiModelProperty("聋哑人")
    private String lyr;

    @ApiModelProperty("不能自理")
    private String bnzl;

    @ApiModelProperty("过敏史")
    private String gms;

    @ApiModelProperty("自述症状")
    private String zszzz;

    @ApiModelProperty("肺脏")
    private String fz;

    @ApiModelProperty("腹部")
    private String fb;

    @ApiModelProperty("体表特殊标志")
    private String tbtbsbz;

    @ApiModelProperty("既往病史")
    private String jwbs;

    @ApiModelProperty("有何传染病")
    private String yhcrb;

    @ApiModelProperty("本人病史")
    private String brbs;

    @ApiModelProperty("家庭病史")
    private String jtbs;

    @ApiModelProperty("血常规")
    private String xcg;

    @ApiModelProperty("心电图")
    private String xdt;

    @ApiModelProperty("B超")
    private String bc;

    @ApiModelProperty("胸片")
    private String xp;

    @ApiModelProperty("建议停止执行拘留原因")
    private String jytzzxjjlyy;

    @ApiModelProperty("医生意见")
    private String ysyj;

    @ApiModelProperty("诊断证明")
    private String zdzm;

    @ApiModelProperty("住院治疗证明文件")
    private String zyzlzmwj;

    @ApiModelProperty("鉴定结论")
    private String jdlj;

    @ApiModelProperty("其他")
    private String qt;

    @ApiModelProperty("经办医生身份证号")
    private String jbyssfzh;

    @ApiModelProperty("经办医生")
    private String jbys;

    @ApiModelProperty("经办时间")
    private Date jbsj;

    @ApiModelProperty("办理状态")
    private String status;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;
    @ApiModelProperty("文书号")
    private String wsh;

    @ApiModelProperty("文书字号")
    private String wszh;

    @ApiModelProperty("经办人身份证号")
    private String jbrsfzh;

    @ApiModelProperty("经办人")
    private String jbr;

    @ApiModelProperty("审批人身份证号")
    private String sprsfzh;

    @ApiModelProperty("审批人")
    private String spr;

    @ApiModelProperty("医嘱")
    private String yz;

    @ApiModelProperty("医院诊断结果")
    private String yyzdjg;
    @ApiModelProperty("入所时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date rssj;
}
