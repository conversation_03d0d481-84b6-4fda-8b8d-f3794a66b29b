package com.rs.module.acp.job.indicator;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.UserApi;
import com.rs.adapter.bsp.api.dto.UserRespDTO;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.entity.zh.AssmtRecordDO;
import com.rs.module.acp.entity.zh.IndicatorCateAssessedDO;
import com.rs.module.acp.entity.zh.IndicatorCateDO;
import com.rs.module.acp.entity.zh.IndicatorConfigDO;
import com.rs.module.acp.enums.zh.IndicatorObjectTypeEnum;
import com.rs.module.acp.service.zh.indicatorcate.AssmtRecordService;
import com.rs.module.acp.service.zh.indicatorcate.IndicatorCateAssessedService;
import com.rs.module.acp.service.zh.indicatorcate.IndicatorCateService;
import com.rs.module.acp.service.zh.indicatorcate.IndicatorConfigService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class IndicatorJob {

    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");

    @Autowired
    private IndicatorCateService indicatorCateService;

    @Autowired
    private IndicatorCateAssessedService indicatorCateAssessedService;

    @Autowired
    private IndicatorConfigService indicatorConfigService;

    @Autowired
    private UserApi userApi;

    @Autowired
    private AssmtRecordService assmtRecordService;

    /**
     * 按照设置的指标查询推送人员，每月1号进行人员绩效考核推送
     *     cron=0 0 0 1 * ?
     */
    @XxlJob("indicatorAssmtRecordJob")
    public void indicatorAssmtRecordJob() {
        String yearMonth = sdf.format(new Date());
        int count = assmtRecordService.count(Wrappers.lambdaQuery(AssmtRecordDO.class).eq(AssmtRecordDO::getAssmtMonth, yearMonth));
        if (count > 0) {
            return;
        }
        List<IndicatorCateDO> cateDOS = indicatorCateService.list();
        if (CollectionUtil.isEmpty(cateDOS)) {
            return;
        }
        //AssmtRecordDO
        List<IndicatorCateAssessedDO> list = indicatorCateAssessedService.list();
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<IndicatorConfigDO> configDOList = indicatorConfigService.list();
        Map<String, IndicatorConfigDO> jzrqMap = configDOList.stream().collect(Collectors.toMap(IndicatorConfigDO::getOrgCode, Function.identity(), (k1, k2) -> k2));

        Map<String, IndicatorCateDO> cateMame = cateDOS.stream().collect(Collectors.toMap(IndicatorCateDO::getId, Function.identity()));
        Map<String, AssmtRecordDO> saveMap = new HashMap<>();
        for (IndicatorCateAssessedDO indicatorCateAssessedDO : list) {

            if (IndicatorObjectTypeEnum.GW.getCode().equals(indicatorCateAssessedDO.getAssessedObjectType())) {
                List<UserRespDTO> users = userApi.getUserByOrgAndPost(indicatorCateAssessedDO.getOrgCode(), indicatorCateAssessedDO.getAssessedObjectId());
                makeAssmtRecordDOByUserRespDTO(indicatorCateAssessedDO, users, saveMap, jzrqMap, cateMame, yearMonth);
            } else if (IndicatorObjectTypeEnum.JS.getCode().equals(indicatorCateAssessedDO.getAssessedObjectType())) {
                List<UserRespDTO> users = userApi.getUserByOrgAndRole(indicatorCateAssessedDO.getOrgCode(), indicatorCateAssessedDO.getAssessedObjectId());
                makeAssmtRecordDOByUserRespDTO(indicatorCateAssessedDO, users, saveMap, jzrqMap, cateMame, yearMonth);

            } else if (IndicatorObjectTypeEnum.YH.getCode().equals(indicatorCateAssessedDO.getAssessedObjectType())) {
                AssmtRecordDO assmtRecordDO = BeanUtils.toBean(indicatorCateAssessedDO, AssmtRecordDO.class);
                assmtRecordDO.setId(null);
                assmtRecordDO.setUpdateTime(null);
                assmtRecordDO.setAddTime(new Date());
                IndicatorCateDO indicatorCateDO = cateMame.get(assmtRecordDO.getIndicatorCateId());
                if (Objects.nonNull(indicatorCateDO)) {
                    assmtRecordDO.setIndicatorCateName(indicatorCateDO.getTypeName());
                    assmtRecordDO.setTotalScore(indicatorCateDO.getInitScore());
                }

                assmtRecordDO.setAssmtMonth(yearMonth);
                // 待填写
                assmtRecordDO.setStatus("01");
                assmtRecordDO.setAssessedSfzh(indicatorCateAssessedDO.getAssessedObjectId());
                assmtRecordDO.setAssessedName(indicatorCateAssessedDO.getAssessedObjectName());
                IndicatorConfigDO indicatorConfigDO = jzrqMap.get(assmtRecordDO.getOrgCode());
                if (Objects.nonNull(indicatorConfigDO)) {
                    String expiryDateType = indicatorConfigDO.getExpiryDateType();
                    if ("02".equals(expiryDateType)) {
                        assmtRecordDO.setExpiryDate(getNextMonth());
                    } else {
                        assmtRecordDO.setExpiryDate(new Date(getNextMonth().getTime() - (indicatorConfigDO.getIntervalDays() * 24 * 60 * 60 * 1000L)));
                    }
                } else {
                    assmtRecordDO.setExpiryDate(getNextMonth());
                }
                saveMap.put(indicatorCateAssessedDO.getIndicatorCateId() + "_" + indicatorCateAssessedDO.getAssessedObjectId(), assmtRecordDO);
            }
        }
        if (saveMap.size() > 0) {
            assmtRecordService.saveBatch(saveMap.values());
            saveMap = null;
        }
    }

    private void makeAssmtRecordDOByUserRespDTO(IndicatorCateAssessedDO indicatorCateAssessedDO, List<UserRespDTO> userList,
                                                Map<String, AssmtRecordDO> saveMap, Map<String, IndicatorConfigDO> jzrqMap,
                                                Map<String, IndicatorCateDO> cateMame, String yearMonth) {
        if (Objects.isNull(indicatorCateAssessedDO) || CollectionUtil.isEmpty(userList)) {
            return;
        }
        for (UserRespDTO user : userList) {
            AssmtRecordDO assmtRecordDO = BeanUtils.toBean(indicatorCateAssessedDO, AssmtRecordDO.class);
            assmtRecordDO.setId(null);
            assmtRecordDO.setUpdateTime(null);
            assmtRecordDO.setAddTime(new Date());
            IndicatorCateDO indicatorCateDO = cateMame.get(assmtRecordDO.getIndicatorCateId());
            if (Objects.nonNull(indicatorCateDO)) {
                assmtRecordDO.setIndicatorCateName(indicatorCateDO.getTypeName());
                assmtRecordDO.setTotalScore(indicatorCateDO.getInitScore());
            }
            assmtRecordDO.setAssmtMonth(yearMonth);
            // 待填写
            assmtRecordDO.setStatus("01");
            assmtRecordDO.setAssessedSfzh(user.getIdCard());
            assmtRecordDO.setAssessedName(user.getName());
            IndicatorConfigDO indicatorConfigDO = jzrqMap.get(assmtRecordDO.getOrgCode());
            if (Objects.nonNull(indicatorConfigDO)) {
                String expiryDateType = indicatorConfigDO.getExpiryDateType();
                if ("02".equals(expiryDateType)) {
                    assmtRecordDO.setExpiryDate(getNextMonth());
                } else {
                    assmtRecordDO.setExpiryDate(new Date(getNextMonth().getTime() - (indicatorConfigDO.getIntervalDays() * 24 * 60 * 60 * 1000L)));
                }
            } else {
                assmtRecordDO.setExpiryDate(getNextMonth());
            }
            saveMap.put(indicatorCateAssessedDO.getIndicatorCateId() + "_" + user.getIdCard(), assmtRecordDO);
        }
    }

    // 获取次月1号
    private Date getNextMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MONTH, LocalDate.now().getMonthValue());
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

}
