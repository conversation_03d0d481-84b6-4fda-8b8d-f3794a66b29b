package com.rs.module.acp.service.zh;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BpmApi;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.cons.SuperviseStatusConstants;
import com.rs.module.acp.controller.admin.zh.vo.SuperviseRepresentRespVO;
import com.rs.module.acp.controller.admin.zh.vo.SuperviseRepresentSaveReqVO;
import com.rs.module.acp.dao.zh.SuperviseDao;
import com.rs.module.acp.dao.zh.SuperviseRepresentDao;
import com.rs.module.acp.entity.zh.SuperviseDO;
import com.rs.module.acp.entity.zh.SuperviseRepresentDO;
import com.rs.module.base.util.BspApprovalUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 综合管理-督导申诉 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SuperviseRepresentServiceImpl extends BaseServiceImpl<SuperviseRepresentDao, SuperviseRepresentDO> implements SuperviseRepresentService {

    @Resource
    private SuperviseRepresentDao superviseRepresentDao;
    @Resource
    private SuperviseDao superviseDao;
    @Resource
    private BpmApi bpmApi;

    private String defKey = "dudaoshensushenpi";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createSuperviseRepresent(SuperviseRepresentSaveReqVO createReqVO) throws Exception {
        // 插入
        SuperviseRepresentDO superviseRepresent = BeanUtils.toBean(createReqVO, SuperviseRepresentDO.class);
        superviseRepresent.setId(StringUtil.getGuid32());
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        //
        Map<String, Object> variables = new HashMap<>();
        String msgTit = String.format("%s%s发起督导申诉，请及时处理！", sessionUser.getOrgName(), sessionUser.getName());
        String msgUrl = "/#/comprehensive/prisonSupervisionLayout/prisonSupervisionHome?id=" + superviseRepresent.getSuperviseId();
        //启动流程
        Map<String,String> processResult = BspApprovalUtil.commonStartProcessMap(defKey, superviseRepresent.getId(), msgTit,
                msgUrl, variables, HttpUtils.getAppCode());
        System.out.println("==========================================");
        System.out.println(processResult);
        if(StringUtil.isEmpty(processResult.get("actInstId")) || StringUtil.isEmpty(processResult.get("taskId"))){
            throw new Exception("启动流程失败");
        }
        superviseRepresent.setUserIdCard(sessionUser.getIdCard());
        superviseRepresent.setUserName(sessionUser.getName());
        superviseRepresent.setTime(new Date());
        superviseRepresent.setActInstId(processResult.get("actInstId"));
        superviseRepresent.setTaskId(processResult.get("taskId"));
        List<ReceiveUser> candidateUsers = JSONObject.parseArray(processResult.get("candidateUsers"), ReceiveUser.class);
        superviseRepresent.setApprovalUserSfzh(candidateUsers.stream().map(user -> user.getIdCard()).collect(Collectors.joining(",")));
        superviseRepresent.setApprovalUserName(candidateUsers.stream().map(user -> user.getName()).collect(Collectors.joining(",")));
        superviseRepresentDao.insert(superviseRepresent);

        // 修改督导状态
        SuperviseDO superviseDO = new SuperviseDO();
        superviseDO.setId(createReqVO.getSuperviseId());
        superviseDO.setStatus(SuperviseStatusConstants.SUPERVISE_STATUS_REPRESENT);
        superviseDao.updateById(superviseDO);
        // 返回
        return superviseRepresent.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean approve(JSONObject approveReqVO) throws Exception {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        SuperviseDO superviseDO = superviseDao.selectById(approveReqVO.getString("id"));
        SuperviseRepresentDO superviseRepresent = superviseRepresentDao.selectOne(new LambdaQueryWrapper<SuperviseRepresentDO>()
                .eq(SuperviseRepresentDO::getSuperviseId, approveReqVO.getString("id")));
        BspApproceStatusEnum isApprove = BspApproceStatusEnum.PASSED;
        String msgTit = String.format("%s%s发起督导申诉，请及时处理！", superviseRepresent.getOrgName(), superviseRepresent.getAddUserName());
        String msgUrl = "/#/comprehensive/prisonSupervisionLayout/prisonSupervisionHome?id=" + superviseRepresent.getSuperviseId();
        String status = SuperviseStatusConstants.SUPERVISE_STATUS_REPRESENT;
        if(SuperviseStatusConstants.SUPERVISE_STATUS_NOT_PASSED.equals(approveReqVO.getString("status"))) {
            isApprove = BspApproceStatusEnum.NOT_PASSED_END;
            status = SuperviseStatusConstants.SUPERVISE_STATUS_REPRESENT_NOT_PASSED;
        }
        //调用流程审批接口
        Map<String, Object> variables = new HashMap<>();
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getId());
        nowApproveUser.put("name", sessionUser.getName());
        JSONObject approvalResult = BspApprovalUtil.approvalProcess(defKey,
                superviseRepresent.getActInstId(), superviseRepresent.getTaskId(), approveReqVO.getString("id"),
                isApprove, approveReqVO.getString("comments"), msgTit, msgUrl,
                null, variables, nowApproveUser, HttpUtils.getAppCode());
        System.out.println("审批结果：" + approvalResult);
        if(!approvalResult.getBoolean("success")){
            throw new Exception("流程审批失败");
        }

        Boolean finishProcinst = bpmApi.isFinishProcinst(superviseRepresent.getActInstId());
        if(finishProcinst && "04".equals(approveReqVO.getString("status")))
            status = SuperviseStatusConstants.SUPERVISE_STATUS_REPRESENT_PASSED;
        superviseDO.setStatus(status);
        superviseDao.updateById(superviseDO);
        JSONObject bpmTrail = BspApprovalUtil.validBPMResult(approvalResult, false);;
        String taskId = bpmTrail.getString("taskId"); // 更新任务ID
        superviseRepresent.setTaskId(taskId);
        List<ReceiveUser> candidateUsers = JSONObject.parseArray(JSONObject.toJSONString(approvalResult.get("candidateUsers")), ReceiveUser.class);
        superviseRepresent.setApprovalUserSfzh(candidateUsers.stream().map(user -> user.getIdCard()).collect(Collectors.joining(",")));
        superviseRepresent.setApprovalUserName(candidateUsers.stream().map(user -> user.getName()).collect(Collectors.joining(",")));
        superviseRepresentDao.updateById(superviseRepresent);
        return true;
    }

    @Override
    public void updateSuperviseRepresent(SuperviseRepresentSaveReqVO updateReqVO) {
        // 校验存在
        validateSuperviseRepresentExists(updateReqVO.getId());
        // 更新
        SuperviseRepresentDO updateObj = BeanUtils.toBean(updateReqVO, SuperviseRepresentDO.class);
        superviseRepresentDao.updateById(updateObj);
    }

    @Override
    public void deleteSuperviseRepresent(String id) {
        // 校验存在
        validateSuperviseRepresentExists(id);
        // 删除
        superviseRepresentDao.deleteById(id);
    }

    private void validateSuperviseRepresentExists(String id) {
        if (superviseRepresentDao.selectById(id) == null) {
            throw new ServerException("综合管理-督导申诉数据不存在");
        }
    }

    @Override
    public SuperviseRepresentRespVO getSuperviseRepresent(String superviseId) {
        SuperviseRepresentDO representDO = superviseRepresentDao.selectOne(new LambdaQueryWrapper<SuperviseRepresentDO>()
                .eq(SuperviseRepresentDO::getSuperviseId, superviseId));
        SuperviseRepresentRespVO respVO = BeanUtils.toBean(representDO, SuperviseRepresentRespVO.class);
        if (ObjectUtil.isNotEmpty(respVO)) {
            SessionUser sessionUser = SessionUserUtil.getSessionUser();
            respVO.setIsApprove(bpmApi.checkIsApproveAuthority(representDO.getTaskId(), sessionUser.getIdCard()));
        }
        return respVO;
    }

}
