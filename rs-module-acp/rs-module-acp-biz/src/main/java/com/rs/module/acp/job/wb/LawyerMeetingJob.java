package com.rs.module.acp.job.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.module.acp.entity.wb.EscortDO;
import com.rs.module.acp.entity.wb.LawyerMeetingDO;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.acp.service.wb.LawyerMeetingService;
import com.rs.module.acp.service.wb.WbCommonService;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.sys.MsgAddVO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.util.MsgUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class LawyerMeetingJob {

    @Autowired
    private LawyerMeetingService lawyerMeetingService;

    @Autowired
    private WbCommonService wbCommonService;


    /**
     * 仅针对【现场会见】，当在超过预约会见结束时间后，还未进行签到，则状态为【逾期取消】
     */
    @XxlJob("lateCancellation")
    public void lateCancellation(){

        XxlJobHelper.log("律师会见-逾期取消：-----开始-------");
        XxlJobHelper.log("律师会见-逾期取消：仅针对【现场会见】，当在超过预约会见结束时间后，还未进行签到，则状态为【逾期取消】");
        try {
            LambdaQueryWrapper<LawyerMeetingDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.select(LawyerMeetingDO::getId);
            lambdaQueryWrapper.eq(LawyerMeetingDO::getMeetingMethod,Arrays.asList("0","1")).eq(LawyerMeetingDO::getStatus, "0")
                    .lt(LawyerMeetingDO::getApplyMeetingEndTime,new Date());
            Integer pageNo = 1;
            Integer pageSize = 500;
            while (true){
                Page<LawyerMeetingDO> page = new Page<>(pageNo,pageSize);
                IPage<LawyerMeetingDO> lawyerMeetingDOIPage = lawyerMeetingService.page(page,lambdaQueryWrapper);
                if(CollectionUtil.isEmpty(lawyerMeetingDOIPage.getRecords())){
                    break;
                }
                List<String> idList = lawyerMeetingDOIPage.getRecords().stream().map(LawyerMeetingDO::getId).collect(Collectors.toList());
                LambdaUpdateWrapper<LawyerMeetingDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.in(LawyerMeetingDO::getId,idList).set(LawyerMeetingDO::getStatus,"98");
                lawyerMeetingService.update(lambdaUpdateWrapper);
            }
        }catch (Exception e){
            e.printStackTrace();
            XxlJobHelper.log("律师会见-逾期取消任务异常：{}",e.getMessage());
        }
        XxlJobHelper.log("律师会见-逾期取消任务:-----结束------");
    }

    /**
     * 若是现场会见、快速会见，当已进行了签到，距离【预约会见结束时间】已超过8小时，【带出安检】/【带回安检】未登记，则状态为【异常】
     */
    @XxlJob("abnormalLawyerMeeting")
    public void abnormalLawyerMeeting(){
        Map<String, PrisonerVwRespVO> prisonerMap = new HashMap<>();

        XxlJobHelper.log("律师会见-会见异常：-----开始-------");
        XxlJobHelper.log("律师会见-会见异常：若是现场会见、快速会见，当已进行了签到，距离【预约会见结束时间】已超过8小时，【带出安检】/【带回安检】未登记，则状态为【异常】");
        try {
            Date queryTime = DateUtil.offsetHour(new Date(),-8);
            LambdaQueryWrapper<LawyerMeetingDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.select(LawyerMeetingDO::getId,LawyerMeetingDO::getJgrybm,
                    LawyerMeetingDO::getApplyMeetingStartTime);
            lambdaQueryWrapper.in(LawyerMeetingDO::getMeetingMethod,Arrays.asList("0","1"))
                    .in(LawyerMeetingDO::getStatus, Arrays.asList("2","3"))
                    .le(LawyerMeetingDO::getApplyMeetingEndTime,queryTime);
            Integer pageNo = 1;
            Integer pageSize = 500;
            while (true){
                Page<LawyerMeetingDO> page = new Page<>(pageNo,pageSize);
                IPage<LawyerMeetingDO> lawyerMeetingDOIPage = lawyerMeetingService.page(page,lambdaQueryWrapper);
                if(CollectionUtil.isEmpty(lawyerMeetingDOIPage.getRecords())){
                    break;
                }
                List<String> idList = lawyerMeetingDOIPage.getRecords().stream().map(LawyerMeetingDO::getId).collect(Collectors.toList());
                LambdaUpdateWrapper<LawyerMeetingDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.in(LawyerMeetingDO::getId,idList).set(LawyerMeetingDO::getStatus,"99");
                lawyerMeetingService.update(lambdaUpdateWrapper);

                //发送消息
                for(LawyerMeetingDO lawyerMeeting:lawyerMeetingDOIPage.getRecords()){
                    XxlJobHelper.log("律师会见：-----发送补录待办消息---start----");
                    wbCommonService.additionalRecordingReminder(JSONObject.parseObject(JSON.toJSONString(lawyerMeeting)), WbConstants.BUSINESS_TYPE_LAWYER_MEETING);
                    XxlJobHelper.log("律师会见：-----发送补录待办消息----end---");
                }

            }
        }catch (Exception e){
            e.printStackTrace();
            XxlJobHelper.log("律师会见-会见异常任务异常：{}",e.getMessage());
        }
        XxlJobHelper.log("律师会见-会见异常任务:-----结束------");
    }
}
