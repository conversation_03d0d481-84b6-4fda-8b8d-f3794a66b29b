package com.rs.module.acp.controller.admin.db;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.InjuryAssessmentDO;
import com.rs.module.acp.service.db.InjuryAssessmentService;

@Api(tags = "实战平台-收押业务-伤情鉴定")
@RestController
@RequestMapping("/acp/db/injuryAssessment")
@Validated
public class InjuryAssessmentController {

    @Resource
    private InjuryAssessmentService injuryAssessmentService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-收押业务-伤情鉴定")
    public CommonResult<String> createInjuryAssessment(@Valid @RequestBody InjuryAssessmentSaveReqVO createReqVO) {
        return success(injuryAssessmentService.createInjuryAssessment(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-收押业务-伤情鉴定")
    public CommonResult<Boolean> updateInjuryAssessment(@Valid @RequestBody InjuryAssessmentSaveReqVO updateReqVO) {
        injuryAssessmentService.updateInjuryAssessment(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-收押业务-伤情鉴定")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteInjuryAssessment(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           injuryAssessmentService.deleteInjuryAssessment(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-收押业务-伤情鉴定")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<InjuryAssessmentRespVO> getInjuryAssessment(@RequestParam("id") String id) {
        InjuryAssessmentDO injuryAssessment = injuryAssessmentService.getInjuryAssessment(id);
        return success(BeanUtils.toBean(injuryAssessment, InjuryAssessmentRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-收押业务-伤情鉴定分页")
    public CommonResult<PageResult<InjuryAssessmentRespVO>> getInjuryAssessmentPage(@Valid @RequestBody InjuryAssessmentPageReqVO pageReqVO) {
        PageResult<InjuryAssessmentDO> pageResult = injuryAssessmentService.getInjuryAssessmentPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InjuryAssessmentRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-收押业务-伤情鉴定列表")
    public CommonResult<List<InjuryAssessmentRespVO>> getInjuryAssessmentList(@Valid @RequestBody InjuryAssessmentListReqVO listReqVO) {
        List<InjuryAssessmentDO> list = injuryAssessmentService.getInjuryAssessmentList(listReqVO);
        return success(BeanUtils.toBean(list, InjuryAssessmentRespVO.class));
    }
}
