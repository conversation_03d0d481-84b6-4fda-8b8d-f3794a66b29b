package com.rs.module.acp.service.ds.sjbs;

import javax.validation.*;

import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyDataSubmitJlsSaveReqVO;
import com.rs.module.acp.entity.ds.sjbs.WeeklyDataSubmitJlsDO;
import com.bsp.common.orm.mybatis.service.IBaseService;

import java.util.Date;
import java.util.List;

/**
 * 实战平台-数据固化-每周数据报送(拘留所) Service 接口
 *
 * <AUTHOR>
 */
public interface WeeklyDataSubmitJlsService extends IBaseService<WeeklyDataSubmitJlsDO>{

    /**
     * 创建实战平台-数据固化-每周数据报送(拘留所)
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createWeeklyDataSubmitJls(@Valid WeeklyDataSubmitJlsSaveReqVO createReqVO);

    /**
     * 更新实战平台-数据固化-每周数据报送(拘留所)
     *
     * @param updateReqVO 更新信息
     */
    void updateWeeklyDataSubmitJls(@Valid WeeklyDataSubmitJlsSaveReqVO updateReqVO);

    /**
     * 删除实战平台-数据固化-每周数据报送(拘留所)
     *
     * @param id 编号
     */
    void deleteWeeklyDataSubmitJls(String id);

    /**
     * 获得实战平台-数据固化-每周数据报送(拘留所)
     *
     * @param id 编号
     * @return 实战平台-数据固化-每周数据报送(拘留所)
     */
    WeeklyDataSubmitJlsDO getWeeklyDataSubmitJls(String id);

    void saveForStatistic(String orgCode, String startDate, String endDate);

    List<WeeklyDataSubmitJlsDO> getWeeklyDataSubmitJlsByDate(String startDate, String endDate, String orgCode);
}
