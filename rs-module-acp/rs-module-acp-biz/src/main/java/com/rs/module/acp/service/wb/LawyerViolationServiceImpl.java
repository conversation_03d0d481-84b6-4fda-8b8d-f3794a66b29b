package com.rs.module.acp.service.wb;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.LawyerViolationDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.LawyerViolationDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-律师违规 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LawyerViolationServiceImpl extends BaseServiceImpl<LawyerViolationDao, LawyerViolationDO> implements LawyerViolationService {

    @Resource
    private LawyerViolationDao lawyerViolationDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createLawyerViolation(LawyerViolationSaveReqVO createReqVO) {
        // 插入
        LawyerViolationDO lawyerViolation = BeanUtils.toBean(createReqVO, LawyerViolationDO.class);
        lawyerViolationDao.insert(lawyerViolation);
        // 返回
        return lawyerViolation.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLawyerViolation(LawyerViolationSaveReqVO updateReqVO) {
        // 校验存在
        validateLawyerViolationExists(updateReqVO.getId());
        // 更新
        LawyerViolationDO updateObj = BeanUtils.toBean(updateReqVO, LawyerViolationDO.class);
        lawyerViolationDao.updateById(updateObj);
    }

    @Override
    public void deleteLawyerViolation(String id) {
        // 校验存在
        validateLawyerViolationExists(id);
        // 删除
        lawyerViolationDao.deleteById(id);
    }

    private void validateLawyerViolationExists(String id) {
        if (lawyerViolationDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-律师违规数据不存在");
        }
    }

    @Override
    public LawyerViolationDO getLawyerViolation(String id) {
        return lawyerViolationDao.selectById(id);
    }

    @Override
    public PageResult<LawyerViolationDO> getLawyerViolationPage(LawyerViolationPageReqVO pageReqVO) {
        return lawyerViolationDao.selectPage(pageReqVO);
    }

    @Override
    public List<LawyerViolationDO> getLawyerViolationList(LawyerViolationListReqVO listReqVO) {
        return lawyerViolationDao.selectList(listReqVO);
    }

    @Override
    public PageResult<LawyerViolationRespVO> getLawyerViolationListByLawyerId(String lawyerId, int pageNo, int pageSize) {
        Page<LawyerViolationDO> page = new Page<>(pageNo,pageSize);
        LambdaQueryWrapper<LawyerViolationDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(LawyerViolationDO::getViolationTime,LawyerViolationDO::getViolationType,
                LawyerViolationDO::getAddUserName,LawyerViolationDO::getAddTime);
        lambdaQueryWrapper.eq(LawyerViolationDO::getLawyerId,lawyerId);
        IPage<LawyerViolationDO> iPage = page(page,lambdaQueryWrapper);
        PageResult<LawyerViolationDO> pageResult = new PageResult<>(iPage.getRecords(),iPage.getTotal());
        return BeanUtils.toBean(pageResult,LawyerViolationRespVO.class);
    }
}
