package com.rs.module.acp.controller.admin.db.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-入所登记（戒毒所）列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class InRecordJdsListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("姓名")
    private String xm;

    @ApiModelProperty("姓名拼音")
    private String xmpy;

    @ApiModelProperty("别名")
    private String bm;

    @ApiModelProperty("性别")
    private String xb;

    @ApiModelProperty("出生日期")
    private Date csrq;

    @ApiModelProperty("证件类型")
    private String zjlx;

    @ApiModelProperty("证件号码")
    private String zjhm;

    @ApiModelProperty("国籍")
    private String gj;

    @ApiModelProperty("民族")
    private String mz;

    @ApiModelProperty("婚姻状况")
    private String hyzk;

    @ApiModelProperty("籍贯")
    private String jg;

    @ApiModelProperty("宗教信仰")
    private String zjxy;

    @ApiModelProperty("户籍地")
    private String hjd;

    @ApiModelProperty("户籍地详址")
    private String hjdxz;

    @ApiModelProperty("现住址")
    private String xzz;

    @ApiModelProperty("现住址详址")
    private String xzzxz;

    @ApiModelProperty("文化程度")
    private String whcd;

    @ApiModelProperty("政治面貌")
    private String zzmm;

    @ApiModelProperty("职业")
    private String zy;

    @ApiModelProperty("工作单位")
    private String gzdw;

    @ApiModelProperty("身份")
    private String sf;

    @ApiModelProperty("特殊身份")
    private String tssf;

    @ApiModelProperty("信息核查")
    private String xxhc;

    @ApiModelProperty("是否为在校学生")
    private String sfwxzxs;

    @ApiModelProperty("学校名称")
    private String xxmc;

    @ApiModelProperty("案由代码")
    private String ajlbdm;

    @ApiModelProperty("案件类别名称")
    private String ajlb;

    @ApiModelProperty("案件编号")
    private String ajbh;

    @ApiModelProperty("人员编号")
    private String rybh;

    @ApiModelProperty("案事件相关人员编号")
    private String asjxgrybh;

    @ApiModelProperty("办案中心编号")
    private String bazxbh;

    @ApiModelProperty("送戒日期")
    private Date sjrq;

    @ApiModelProperty("送戒机关类型")
    private String sjjglx;

    @ApiModelProperty("送戒机关名称")
    private String sjjgmc;

    @ApiModelProperty("送戒人姓名1")
    private String sjrxm;

    @ApiModelProperty("送戒人联系电话1")
    private String sjrlxdh;

    @ApiModelProperty("送戒人姓名2")
    private String sjrxm2;

    @ApiModelProperty("送戒人联系电话2")
    private String sjrlxdh2;

    @ApiModelProperty("送戒民警姓名")
    private String sjmjxm;

    @ApiModelProperty("送戒民警联系电话")
    private String sjmjxmlxdh;

    @ApiModelProperty("办案单位类型")
    private String badwlx;

    @ApiModelProperty("办案单位")
    private String badw;

    @ApiModelProperty("收戒决定机关类型")
    private String sjjdjglx;

    @ApiModelProperty("收戒决定机关名称")
    private String sjjdjgmc;

    @ApiModelProperty("收戒凭证")
    private String sjpz;

    @ApiModelProperty("收戒凭证文书号")
    private String sjpzwsh;

    @ApiModelProperty("收戒凭证文书号")
    private String sjpzwsdz;

    @ApiModelProperty("档案编号")
    private String dabh;

    @ApiModelProperty("回执法律文书号")
    private String hzflwsh;

    @ApiModelProperty("入所原因")
    private String rsyy;

    @ApiModelProperty("入所日期")
    private Date rsrq;

    @ApiModelProperty("收戒民警")
    private String sjmj;

    @ApiModelProperty("收戒期限")
    private Date sjqx;

    @ApiModelProperty("收戒起始日期")
    private Date sjqsrq;

    @ApiModelProperty("收戒截止日期")
    private Date sjjzrq;

    @ApiModelProperty("管理类别")
    private String gllb;

    @ApiModelProperty("是否佩戴眼镜")
    private String sfpdyj;

    @ApiModelProperty("监区id")
    private String areaId;

    @ApiModelProperty("监区名称")
    private String areaName;

    @ApiModelProperty("监室号")
    private String jsh;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("分配床位")
    private String fpcw;

    @ApiModelProperty("收分配标识服")
    private String sfpbsf;

    @ApiModelProperty("带入戒室人")
    private String drjsr;

    @ApiModelProperty("带入戒室时间")
    private Date drjssj;

    @ApiModelProperty("简要案情")
    private String jyaq;

    @ApiModelProperty("同案人，多个逗号分割")
    private String tar;

    @ApiModelProperty("备注")
    private String bz;

    @ApiModelProperty("涉毒尿检初查结果")
    private String sdnjccjg;

    @ApiModelProperty("涉毒尿检单位")
    private String sdnjdw;

    @ApiModelProperty("涉毒尿检初查时间")
    private Date sdnjccsj;

    @ApiModelProperty("涉毒尿检检查人")
    private String sdnjjcr;

    @ApiModelProperty("手环ID")
    private String shid;

    @ApiModelProperty("手环绑定状态")
    private String shbdzt;

    @ApiModelProperty("手环绑定时间")
    private Date sdbdsj;

    @ApiModelProperty("是否涉密人员")
    private Short sfsm;

    @ApiModelProperty("人员代号")
    private String rydh;

    @ApiModelProperty("涉密原因")
    private String smyy;

    @ApiModelProperty("涉密备注")
    private String smbz;

    @ApiModelProperty("救济日期")
    private Date jjrq;

    @ApiModelProperty("救济原因")
    private String jjyy;

    @ApiModelProperty("救济领取物品")
    private String jjlqwp;

    @ApiModelProperty("入所类型")
    private String rslx;

    @ApiModelProperty("经办人")
    private String jbr;

    @ApiModelProperty("经办时间")
    private Date jbsj;

    @ApiModelProperty("办理状态")
    private String status;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("办理状态")
    private String spzt;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("当前步骤")
    private String currentStep;

    @ApiModelProperty("特长(专长)")
    private String tc;
    @ApiModelProperty("联系电话")
    private String lxdh;
    @ApiModelProperty("同案编号")
    private String tabh;
    @ApiModelProperty("办案单位电话")
    private String badwdh;
    @ApiModelProperty("办案人")
    private String bar;
    @ApiModelProperty("办案人联系电话")
    private String barlxdh;
    @ApiModelProperty("羁押日期")
    private Date jyrq;
    @ApiModelProperty("拘留日期")
    private Date jlrq;
    @ApiModelProperty("逮捕日期")
    private Date dbrq;
    @ApiModelProperty("限制会见案件")
    private Short xzhjaj;
    @ApiModelProperty("审批单位")
    private String spdw;
    @ApiModelProperty("审批人")
    private String spr;
    @ApiModelProperty("转入单位")
    private String zrdw;
    @ApiModelProperty("是否重刑犯")
    private String zxf;
    @ApiModelProperty("吸食毒品种类")
    private String xsdpzl;
    @ApiModelProperty("吸毒方式")
    private String xdfs;
    @ApiModelProperty("是否复吸")
    private String sffx;
    @ApiModelProperty("指纹编号")
    private String zwbh;
    @ApiModelProperty("附物编号")
    private String fwbh;
    @ApiModelProperty("性病")
    private String xingbing;
    @ApiModelProperty("本人电话")
    private String brdh;
    @ApiModelProperty("家属联系电话")
    private String jslxdh;
    @ApiModelProperty("其他行政处罚")
    private String qtxzcf;
    @ApiModelProperty("备注(收戒信息)")
    private String sjbz;
    @ApiModelProperty("所内编号")
    private String snbh;
    @ApiModelProperty("自愿戒毒")
    private String zrjd;

    @ApiModelProperty("艾滋病")
    private String azb;

    @ApiModelProperty("档案号")
    private String dah;

    @ApiModelProperty("收押期限")
    private Date syqx;


}
