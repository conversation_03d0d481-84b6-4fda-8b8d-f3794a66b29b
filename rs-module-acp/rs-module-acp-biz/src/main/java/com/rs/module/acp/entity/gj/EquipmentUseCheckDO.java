package com.rs.module.acp.entity.gj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 实战平台-管教业务-戒具检查 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_equipment_use_check")
@KeySequence("acp_gj_equipment_use_check_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_equipment_use_check")
public class EquipmentUseCheckDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 械具使用ID
     */
    private String equipmentUseId;
    /**
     * 检查时间
     */
    private Date checkTime;
    /**
     * 检查人身份证号
     */
    private String checkUserSfzh;
    /**
     * 检查人
     */
    private String checkUser;
    /**
     * 检查结果
     */
    private String checkResult;
    /**
     * 检查情况
     */
    private String checkSituation;
    /**
     * 状态
     */
    private String status;

}
