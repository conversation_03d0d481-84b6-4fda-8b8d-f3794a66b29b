package com.rs.module.acp.entity.gj;

import lombok.*;

import java.time.LocalTime;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-教育康复课程时段 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_edurehab_time_slot")
@KeySequence("acp_gj_edurehab_time_slot_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_edurehab_time_slot")
public class EdurehabTimeSlotDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 课程时段编码，如“9:00-10:00”，唯一标识时段
     */
    private String timeSlotCode;
    /**
     * 课程时段-开始
     */
    private String startTime;
    /**
     * 课程时段-结束
     */
    private String endTime;
    /**
     * 时段时长（分钟）
     */
    private Integer durationMinutes;

}
