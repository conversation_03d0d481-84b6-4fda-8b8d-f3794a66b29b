package com.rs.module.acp.service.gj.printdocument;

import com.rs.module.acp.controller.admin.gj.vo.printdocument.PrintDocumentListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.printdocument.PrintDocumentPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.printdocument.PrintDocumentSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.PrintDocumentDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.PrintDocumentDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-文书打印预览 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrintDocumentServiceImpl extends BaseServiceImpl<PrintDocumentDao, PrintDocumentDO> implements PrintDocumentService {

    @Resource
    private PrintDocumentDao printDocumentDao;

    @Override
    public String createPrintDocument(PrintDocumentSaveReqVO createReqVO) {
        // 插入
        PrintDocumentDO printDocument = BeanUtils.toBean(createReqVO, PrintDocumentDO.class);
        printDocumentDao.insert(printDocument);
        // 返回
        return printDocument.getId();
    }

    @Override
    public void updatePrintDocument(PrintDocumentSaveReqVO updateReqVO) {
        // 校验存在
        validatePrintDocumentExists(updateReqVO.getId());
        // 更新
        PrintDocumentDO updateObj = BeanUtils.toBean(updateReqVO, PrintDocumentDO.class);
        printDocumentDao.updateById(updateObj);
    }

    @Override
    public void deletePrintDocument(String id) {
        // 校验存在
        validatePrintDocumentExists(id);
        // 删除
        printDocumentDao.deleteById(id);
    }

    private void validatePrintDocumentExists(String id) {
        if (printDocumentDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-文书打印预览数据不存在");
        }
    }

    @Override
    public PrintDocumentDO getPrintDocument(String id) {
        return printDocumentDao.selectById(id);
    }

    @Override
    public PageResult<PrintDocumentDO> getPrintDocumentPage(PrintDocumentPageReqVO pageReqVO) {
        return printDocumentDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrintDocumentDO> getPrintDocumentList(PrintDocumentListReqVO listReqVO) {
        return printDocumentDao.selectList(listReqVO);
    }


}
