package com.rs.module.acp.service.zh;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.util.DateUtils;
import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.acp.cons.SignInStatusEnum;
import com.rs.module.acp.dao.zh.DutySuperviseRuleDao;
import com.rs.module.acp.entity.zh.DutySuperviseRuleDO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.zh.vo.*;
import com.rs.module.acp.entity.zh.DutySuperviseRecordDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.zh.DutySuperviseRecordDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 综合管理-值班管理-值班督导记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DutySuperviseRecordServiceImpl extends BaseServiceImpl<DutySuperviseRecordDao, DutySuperviseRecordDO> implements DutySuperviseRecordService {

    @Resource
    private DutySuperviseRecordDao dutySuperviseRecordDao;
    @Resource
    private DutySuperviseRuleDao dutySuperviseRuleDao;
    //是否允许签到
    private boolean isAllowSignIn = true;

    @Override
    public String createDutySuperviseRecord(DutySuperviseRecordSaveReqVO createReqVO) {
        // 插入
        DutySuperviseRecordDO dutySuperviseRecord = BeanUtils.toBean(createReqVO, DutySuperviseRecordDO.class);
        dutySuperviseRecordDao.insert(dutySuperviseRecord);
        // 返回
        return dutySuperviseRecord.getId();
    }

    @Override
    public void updateDutySuperviseRecord(DutySuperviseRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateDutySuperviseRecordExists(updateReqVO.getId());
        if(updateReqVO.getSigninTime()!=null){
            DutySuperviseRecordDO dutySuperviseRecordDO = dutySuperviseRecordDao.selectById(updateReqVO.getId());
            updateReqVO.setSigninStatus(SignInStatusEnum.SIGNED.getCode());
            updateReqVO.setStaffDutyTemplateId(dutySuperviseRecordDO.getStaffDutyTemplateId());
            //待根据时间判断签到状态
            DutySuperviseRuleDO dutySuperviseRuleDO = getDutySignInRule(updateReqVO);
            //根据签到时间以及签到规则，判断签到状态等
            updateReqVO = dealWithSignInStatus(updateReqVO,dutySuperviseRuleDO);

        }
        // 更新
        DutySuperviseRecordDO updateObj = BeanUtils.toBean(updateReqVO, DutySuperviseRecordDO.class);
        dutySuperviseRecordDao.updateById(updateObj);

        //已签到，要消除待办
        //消除待办
        String msgId = updateReqVO.getId();
        try {
            SendMessageUtil.ProcessTodoMsg(msgId, SessionUserUtil.getSessionUser().getIdCard(), "pc");
        } catch (Exception e) {
            e.printStackTrace();
        }
        //已经过了当次排班周期，不允许签到了
        if (!isAllowSignIn) {
            isAllowSignIn = true;
            throw new ServerException("已超过允许的最晚签到时间，不允许签到以及变更签到状态!");
        }
    }

    private DutySuperviseRecordSaveReqVO dealWithSignInStatus(DutySuperviseRecordSaveReqVO updateReqVO, DutySuperviseRuleDO dutySuperviseRuleDO) {
        //如果没有规则，则返回
        if (dutySuperviseRuleDO == null) {
            return updateReqVO;
        }
        // 获取签到时间，单位：分钟
        Date signInTime = updateReqVO.getSigninTime();
        // 获取超时阈值（单位：分钟），超过该时间视为超时签到
        Integer timeoutThreshold = dutySuperviseRuleDO.getTimeoutThreshold();
        // 获取当日允许的超时次数阈值，用于判断是否触发推送
        Integer todayTimeoutCount = dutySuperviseRuleDO.getTodayTimeoutCount();
        // 获取补签时间阈值（单位：分钟），在该时间内可以补签
        Integer lateSigninTime = dutySuperviseRuleDO.getLateSigninTime();
        // 获取签到间隔时间，用于判断连续签到的时间窗口
        Integer signinInterval = dutySuperviseRuleDO.getSigninInterval();
        //是否已超时签到
        boolean isTimeoutSignin = false;

        String timeoutPushTargetSfzh = dutySuperviseRuleDO.getTimeoutPushTargetSfzh();
        String todayTimeoutCountPushTargetSfzh = dutySuperviseRuleDO.getTodayTimeoutCountPushTargetSfzh();
        //获取到消息发送时间
        DutySuperviseRecordDO dutySuperviseRecordDO = dutySuperviseRecordDao.selectById(updateReqVO.getId());
        Date msgSendTime = dutySuperviseRecordDO.getAddTime();
        //签到时间减去msgSendTime小于等于timeoutThreshold，则为已签到
        long diffMinutes = (updateReqVO.getSigninTime().getTime() - msgSendTime.getTime()) / (60 * 1000);
        if (diffMinutes <= timeoutThreshold) {
            updateReqVO.setSigninStatus(SignInStatusEnum.SIGNED.getCode());
        }
        //签到时间减去msgSendTime大于timeoutThreshold，但小于补签时间，则为补签
        else if (diffMinutes > timeoutThreshold && diffMinutes <= lateSigninTime) {
            updateReqVO.setSigninStatus(SignInStatusEnum.RECOVERED.getCode());
        }
        //签到时间减去msgSendTime大于补签时间，但小于签到间隔，则为超时签到
        else if (diffMinutes > lateSigninTime && diffMinutes <= signinInterval) {
            updateReqVO.setSigninStatus(SignInStatusEnum.LATE.getCode());
            isTimeoutSignin = true;
        }else{
            isAllowSignIn = false;
            isTimeoutSignin = true;
        }

        if(isTimeoutSignin){
            sendTimeOutMsg(updateReqVO,dutySuperviseRecordDO,dutySuperviseRuleDO);
        }
        return updateReqVO;

    }

    /**
     * 发送超时消息
     * @param updateReqVO
     * @param dutySuperviseRecordDO
     * @param dutySuperviseRuleDO
     */
    private void sendTimeOutMsg(DutySuperviseRecordSaveReqVO updateReqVO, DutySuperviseRecordDO dutySuperviseRecordDO,DutySuperviseRuleDO dutySuperviseRuleDO) {
        // ========== 新增的消息发送逻辑 Start ==========
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        if (sessionUser == null) {
            throw new ServerException("用户未登录，无法发送消息！");
        }
        // 标题组装：值班岗位：XXX，值班时间：XXX，时间段：XXX-XXX,签到状态：XXX，已超时签到，请关注!
        String title = String.format("值班岗位：%s，值班时间：%s，时间段：%s-%s，签到状态：%s，已超时签到，请关注!",
                dutySuperviseRecordDO.getStaffDutyPostId(), 
                DateUtils.format(updateReqVO.getDutyDate(), "yyyy-MM-dd"),
                dutySuperviseRuleDO.getSigninStartTime(), 
                dutySuperviseRuleDO.getSigninEndTime(),
                SignInStatusEnum.LATE.getValue());
        
        String content = String.format("签到时间：%s\n排班模板ID：%s\n签到状态：%s\n值班岗位：%s\n时间段：%s-%s",
                DateUtils.format(updateReqVO.getSigninTime(), "yyyy-MM-dd HH:mm:ss"), 
                updateReqVO.getStaffDutyTemplateId(), 
                SignInStatusEnum.LATE.getValue(),
                dutySuperviseRecordDO.getStaffDutyPostId(),
                dutySuperviseRuleDO.getSigninStartTime(), 
                dutySuperviseRuleDO.getSigninEndTime());

        String simpleUUID = updateReqVO.getId();
        String url = "/duty/supervise/record/view?id=" + updateReqVO.getId(); // 消息点击后跳转的页面
        String appCode = HttpUtils.getAppCode(); // 获取当前应用编码
        String fUser = sessionUser.getIdCard();   // 来源用户身份证号
        String fUserName = sessionUser.getName(); // 来源用户名
        String fOrgCode = sessionUser.getOrgCode(); // 来源机构代码
        String fOrgName = sessionUser.getOrgName(); // 来源机构名称
        String fXxpt = "pc"; // 来源系统标识
        String ywbh = updateReqVO.getId(); // 业务编号，可以是主键ID
        String msgType = MsgBusTypeEnum.XK_ZBDD.getCode(); // 消息类型（建议定义枚举）

        // 接收人列表：从规则中获取超时推送对象
        List<ReceiveUser> receiveUserList = new ArrayList<>();
        String timeoutPushTarget = dutySuperviseRuleDO.getTimeoutPushTargetSfzh(); // 使用身份证号字段

        if (timeoutPushTarget != null && !timeoutPushTarget.trim().isEmpty()) {
            String[] userIds = timeoutPushTarget.split(",");
            for (String userId : userIds) {
                if (!userId.trim().isEmpty()) {
                    receiveUserList.add(new ReceiveUser(userId.trim(), fOrgCode)); // 假设统一组织机构码
                }
            }
        } else {
            throw new ServerException("超时推送对象不能为空");
        }

        SendMessageUtil.sendAlertMsg(title, content, url, fXxpt, fUser, fUserName, fOrgCode, fOrgName,
                null, simpleUUID, fXxpt, ywbh, receiveUserList, "acp", msgType, simpleUUID);
//        SendMessageUtil.sendTodoMsg(title, content, url, appCode, fUser, fUserName, fOrgCode, fOrgName,
//                null, simpleUUID, fXxpt, ywbh, receiveUserList, msgType, null);
        // ========== 新增的消息发送逻辑 End ==========
    }

    @Override
    public void deleteDutySuperviseRecord(String id) {
        // 校验存在
        validateDutySuperviseRecordExists(id);
        // 删除
        dutySuperviseRecordDao.deleteById(id);
    }

    private void validateDutySuperviseRecordExists(String id) {
        if (dutySuperviseRecordDao.selectById(id) == null) {
            throw new ServerException("综合管理-值班管理-值班督导记录数据不存在");
        }
    }

    @Override
    public DutySuperviseRecordDO getDutySuperviseRecord(String id) {
        return dutySuperviseRecordDao.selectById(id);
    }

    @Override
    public PageResult<DutySuperviseRecordDO> getDutySuperviseRecordPage(DutySuperviseRecordPageReqVO pageReqVO) {
        return dutySuperviseRecordDao.selectPage(pageReqVO);
    }

    @Override
    public List<DutySuperviseRecordDO> getDutySuperviseRecordList(DutySuperviseRecordListReqVO listReqVO) {
        return dutySuperviseRecordDao.selectList(listReqVO);
    }

    @Override
    public List<AcpZhStaffDutyTemplateCurrentInfo> getCurrentDutyInfo(String orgCode) {
        return dutySuperviseRecordDao.getCurrentDutyInfo(orgCode);
    }

    @Override
    public List<StaffDutyRecordPersonVO> getDutyRecordPersonsByRecordId(String recordId) {
        return dutySuperviseRecordDao.getDutyRecordPersonsByRecordId(recordId);
    }

    /**
     * 根据实体信息DutySuperviseRecordSaveReqVO updateReqVO的模版ID信息，获取签到规则数据
     * @param updateReqVO
     */
    private DutySuperviseRuleDO getDutySignInRule(DutySuperviseRecordSaveReqVO updateReqVO) {
        DutySuperviseRuleDO dutySignInRuleDO = dutySuperviseRuleDao.selectOne(DutySuperviseRuleDO::getStaffDutyTemplateId, updateReqVO.getStaffDutyTemplateId());
        return dutySignInRuleDO;
    }

}
