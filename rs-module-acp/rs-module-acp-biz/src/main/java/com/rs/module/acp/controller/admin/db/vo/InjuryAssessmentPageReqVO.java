package com.rs.module.acp.controller.admin.db.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-伤情鉴定分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InjuryAssessmentPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("人员编号")
    private String rybh;

    @ApiModelProperty("序号")
    private String serialNumber;

    @ApiModelProperty("照片")
    private String zp;

    @ApiModelProperty("描述")
    private String ms;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
