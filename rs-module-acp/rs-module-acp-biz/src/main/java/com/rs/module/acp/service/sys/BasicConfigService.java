package com.rs.module.acp.service.sys;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.sys.vo.BasicConfigListReqVO;
import com.rs.module.acp.controller.admin.sys.vo.BasicConfigPageReqVO;
import com.rs.module.acp.controller.admin.sys.vo.BasicConfigRespVO;
import com.rs.module.acp.controller.admin.sys.vo.BasicConfigSaveReqVO;
import com.rs.module.acp.entity.sys.BasicConfigDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-系统基础配置 Service 接口
 *
 * <AUTHOR>
 */
public interface BasicConfigService extends IBaseService<BasicConfigDO>{

    /**
     * 创建实战平台-系统基础配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createBasicConfig(@Valid BasicConfigSaveReqVO createReqVO);

    /**
     * 更新实战平台-系统基础配置
     *
     * @param updateReqVO 更新信息
     */
    void updateBasicConfig(@Valid BasicConfigSaveReqVO updateReqVO);

    /**
     * 删除实战平台-系统基础配置
     *
     * @param id 编号
     */
    void deleteBasicConfig(String id);

    /**
     * 获得实战平台-系统基础配置
     *
     * @param id 编号
     * @return 实战平台-系统基础配置
     */
    BasicConfigRespVO getBasicConfig();

    /**
    * 获得实战平台-系统基础配置分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-系统基础配置分页
    */
    PageResult<BasicConfigDO> getBasicConfigPage(BasicConfigPageReqVO pageReqVO);

    /**
    * 获得实战平台-系统基础配置列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-系统基础配置列表
    */
    List<BasicConfigDO> getBasicConfigList(BasicConfigListReqVO listReqVO);


}
