package com.rs.module.acp.service.db.components;

import com.rs.module.acp.controller.admin.db.vo.CollectedPersonDetailVO;
import com.rs.module.acp.controller.admin.db.vo.prisonerInfoReqVO;
import com.rs.module.acp.entity.db.DetainRegKssDO;

import java.util.Date;

public interface RegistrationInfoService {
    /**
     * 更新指定人员编号（rybh）的审批状态（spzt）和流程阶段（currentStep）
     * 
     * @param rybh        人员编号
     * @param spzt         审批状态
     * @param currentStep 流程阶段
     */
    boolean updateStateInfo(String rybh, String spzt, String currentStep);

    /**
     * 更新指定人员编号（rybh）的手环信息
     * 
     * @param rybh      人员编号
     * @param shid       手环ID
     * @param shbdzt     手环绑定状态
     * @param sdbdsj     手环绑定时间
     */
    boolean updateWristbandInfo(String rybh, String shid, String shbdzt, Date sdbdsj,String status);

    String getRslxInfo(String rybh);

    CollectedPersonDetailVO getCollectedPersonDetail(prisonerInfoReqVO prisonerInfoReqVO);
}