package com.rs.module.acp.entity.zh;

import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 综合管理-绩效考核指标分类 DO
 *
 * <AUTHOR>
 */
@TableName("acp_zh_indicator_cate")
@KeySequence("acp_zh_indicator_cate_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_indicator_cate")
public class IndicatorCateDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 指标类型名称
     */
    private String typeName;
    /**
     * 初始化分值
     */
    private BigDecimal initScore;
    /**
     * 考核人身份证号
     */
    private String assessorSfzh;
    /**
     * 考核人姓名
     */
    private String assessorName;
    /**
     * 被考核对象类型，01:岗位、02：角色、03：用户
     */
    private String assessedObjectType;
    /**
     * 被考核对象ID
     */
    private String assessedObjectId;
    /**
     * 被考核对象名称
     */
    private String assessedObjectName;
    /**
     * 排序序号
     */
    private Integer sortOrder;
    /**
     * 备注
     */
    private String remark;

}
