package com.rs.module.acp.controller.app.gj.vo.conflict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 确认VO
 * <AUTHOR>
 * @Date 2025/5/23 15:52
 */
@ApiModel(description = "管理后台-实战平台内屏-管教业务-社会矛盾确认VO")
@Data
public class AppConflictConfirmReqVO {

    @ApiModelProperty("冲突人员ID")
    @NotBlank(message = "冲突人员ID不能为空")
    private String prisonerId;
    @ApiModelProperty("在押人员签名")
    @NotBlank(message = "在押人员签名不能为空")
    private String prisonerSignature;
    @ApiModelProperty("满意度评分（1：满意：0：不满意）")
    @NotNull(message = "满意度评分（1：满意：0：不满意）不能为空")
    private Integer satisfactionScore;

}
