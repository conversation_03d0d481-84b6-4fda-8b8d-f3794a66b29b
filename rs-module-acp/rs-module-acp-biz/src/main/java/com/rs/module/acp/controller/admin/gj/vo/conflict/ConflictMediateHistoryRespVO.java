package com.rs.module.acp.controller.admin.gj.vo.conflict;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/3/24 11:34
 */
@ApiModel(description = "管理后台 - 实战平台-管教业务-社会矛盾化解历史记录响应VO")
@Data
public class ConflictMediateHistoryRespVO {
    @ApiModelProperty("事件编号")
    private String eventCode;
    @ApiModelProperty("调解经办时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date mediationOperatorTime;
}
