package com.rs.module.acp.controller.app.gj.vo.inoutrecords;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-出入登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class InOutRecordsBatchSaveReqVO extends BaseVO{
    @ApiModelProperty("主键")
    private String ids;

    @ApiModelProperty("数据来源（字典：ZD_DATA_SOURCES）")
    @NotEmpty(message = "数据来源（字典：ZD_DATA_SOURCES）不能为空")
    private String dataSources;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgryxm;

    @ApiModelProperty("进出监室类型（字典：ZD_GJJCJSLX）")
    @NotEmpty(message = "进出监室类型不能为空")
    private String inoutType;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("旧监室id")
    @NotEmpty(message = "旧监室id不能为空")
    private String roomId;

    @ApiModelProperty("出入监室时间")
    @NotNull(message = "出入监室时间不能为空")
    private Date inoutTime;

    @ApiModelProperty("带出带入民警身份证号")
    private String inoutPoliceSfzh;

    @ApiModelProperty("业务类型（字典：ZD_GJXZDCSY）")
    @NotEmpty(message = "业务类型不能为空")
    private String businessType;

    @ApiModelProperty("带出带入民警")
    private String inoutPolice;

    @ApiModelProperty("出入事由")
    private String inoutReason;

    @ApiModelProperty("检查结果")
    private String inspectionResult;

    @ApiModelProperty("业务ID")
    private String businessId;

    @ApiModelProperty("违禁物品登记")
    private String prohibitedItems;

    @ApiModelProperty("管教民警身份证号")
    @NotEmpty(message = "管教民警身份证号不能为空")
    private String supervisorPoliceSfzh;

    @ApiModelProperty("体表检查登记")
    private String physicalExam;

    @ApiModelProperty("管教民警姓名")
    @NotEmpty(message = "管教民警姓名不能为空")
    private String supervisorPolice;

    @ApiModelProperty("异常情况登记")
    private String abnormalSituations;

    @ApiModelProperty("管教民警人脸信息存储路径")
    private String supervisorFaceInfoPath;

    @ApiModelProperty("违禁物品登记照片存储地址")
    private String prohibitedItemsImgUrl;

    @ApiModelProperty("被监管人员人脸信息存储路径")
    private String prisonerFaceInfoPath;

    @ApiModelProperty("体表检查登记照片存储地址")
    private String physicalExamImgUrl;

    @ApiModelProperty("异常情况登记照片存储地址")
    private String abnormalSituationsImgUrl;

    @ApiModelProperty("是否查出违禁物品")
    private Short isProhibitedItems;

    @ApiModelProperty("检查时间")
    private Date inspectionTime;

    @ApiModelProperty("检查民警身份证号")
    private String inspectorSfzh;

    @ApiModelProperty("检查民警身份证号")
    private String inspector;

    @ApiModelProperty("业务类型子类型")
    private String businessSubType;

}
