package com.rs.module.acp.controller.app.gj;

import cn.hutool.core.date.DateUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.admin.gj.vo.EquipmentUseCheckRespVO;
import com.rs.module.acp.controller.admin.gj.vo.EquipmentUseCheckSaveReqVO;
import com.rs.module.acp.service.gj.EquipmentUseCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "仓外屏-戒具检查")
@RestController
@RequestMapping("/app/acp/gj/equipmentUse/check")
@Validated
public class AppEquipmentUseCheckController {

    @Resource
    private EquipmentUseCheckService equipmentUseCheckService;


    @PostMapping("/update")
    @ApiOperation(value = "更新戒具检查信息")
    public CommonResult<Boolean> updateEquipmentUseCheck(@Valid @RequestBody EquipmentUseCheckSaveReqVO updateReqVO) {
        equipmentUseCheckService.updateEquipmentUseCheck(updateReqVO);
        return success(true);
    }

    @GetMapping("/getCheckList")
    @ApiOperation(value = "获得待戒具检查列表")
    @ApiImplicitParam(name = "roomCode", value = "监视编号")
    public CommonResult<List<EquipmentUseCheckRespVO>> getCheckList(@RequestParam("roomCode") String roomCode) {
        return success(equipmentUseCheckService.getCheckByRoomCode(roomCode));
    }

    @GetMapping("/getCheckRecordList")
    @ApiOperation(value = "获得戒具检查记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roomCode", value = "监室号"),
            @ApiImplicitParam(name = "dateType", value = "时间类型(全部:all,今天:today,昨天:yesterday,本周:week)")
    })
    public CommonResult<List<EquipmentUseCheckRespVO>> getCheckRecordList(@RequestParam("roomCode") String roomCode,
                                                                          @RequestParam("dateType") String dateType) {
        Date startTime = null;
        Date endTime = null;
        switch (dateType) {
            case "today":
                startTime = DateUtil.beginOfDay(new Date());
                endTime = DateUtil.endOfDay(new Date());
                break;
            case "yesterday":
                startTime = DateUtil.beginOfDay(DateUtil.yesterday());
                endTime = DateUtil.endOfDay(DateUtil.yesterday());
                break;
            case "week":
                startTime = DateUtil.beginOfWeek(new Date());
                endTime = DateUtil.endOfWeek(new Date());
                break;
            default:
                break;
        }

        return success(equipmentUseCheckService.getCheckRecordList(roomCode, startTime, endTime));
    }
}
