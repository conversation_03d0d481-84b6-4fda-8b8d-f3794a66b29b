package com.rs.module.acp.entity.wb;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-窗口业务-单向视频家属会见 DO
 *
 * <AUTHOR>
 */
@TableName("acp_wb_family_meeting_video")
@KeySequence("acp_wb_family_meeting_video_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_wb_family_meeting_video")
public class FamilyMeetingVideoDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 第一位家属ID
     */
    private String familyMember1Id;
    /**
     * 第一位家属姓名
     */
    private String familyMember1Name;
    /**
     * 第一位家属性别
     */
    private String familyMember1Gender;
    /**
     * 第一位家属证件类型
     */
    private String familyMember1IdType;
    /**
     * 第一位家属证件号码
     */
    private String familyMember1IdNumber;
    /**
     * 第一位家属与被会见人社会关系
     */
    private String familyMember1Relationship;
    /**
     * 第一位家属联系方式
     */
    private String familyMember1Contact;
    /**
     * 第一位家属工作单位
     */
    private String familyMember1WorkUnit;
    /**
     * 第一位家属居住地址
     */
    private String familyMember1Address;
    /**
     * 第一位家属关系证明附件
     */
    private String familyMember1RelationsAttch;
    /**
     * 第一位家属照片
     */
    private String familyMember1ImageUrl;
    /**
     * 第二位家属ID
     */
    private String familyMember2Id;
    /**
     * 第二位家属姓名
     */
    private String familyMember2Name;
    /**
     * 第二位家属性别
     */
    private String familyMember2Gender;
    /**
     * 第二位家属证件类型
     */
    private String familyMember2IdType;
    /**
     * 第二位家属的证件号码
     */
    private String familyMember2IdNumber;
    /**
     * 第二位家属与被会见人社会关系
     */
    private String familyMember2Relationship;
    /**
     * 第二位家属联系方式
     */
    private String familyMember2Contact;
    /**
     * 第二位家属工作单位
     */
    private String familyMember2WorkUnit;
    /**
     * 第二位家属居住地址
     */
    private String familyMember2Address;
    /**
     * 第二位家属关系证明附件
     */
    private String familyMember2RelationsAttch;
    /**
     * 第二位家属照片
     */
    private String familyMember2ImageUrl;
    /**
     * 状态
     */
    private String status;
    /**
     * 通知家属时间
     */
    private Date notificationFamilyTime;
    /**
     * 通知会见日期
     */
    private Date notificationMeetingDate;
    /**
     * 会见开始时间
     */
    private Date meetingStartTime;
    /**
     * 会见结束时间
     */
    private Date meetingEndTime;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 审批人身份证号
     */
    private String approverSfzh;
    /**
     * 审批人姓名
     */
    private String approverXm;
    /**
     * 审批时间
     */
    private Date approverTime;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 审批人签名
     */
    private String approvalAutograph;
    /**
     * 审批人签名日期
     */
    private Date approvalAutographTime;
    /**
     * 审核意见
     */
    private String approvalComments;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 第三位家属ID
     */
    private String familyMember3Id;
    /**
     * 第三位家属姓名
     */
    private String familyMember3Name;
    /**
     * 第三位家属性别
     */
    private String familyMember3Gender;
    /**
     * 第三位家属证件类型
     */
    private String familyMember3IdType;
    /**
     * 第三位家属的证件号码
     */
    private String familyMember3IdNumber;
    /**
     * 第三位家属与被会见人社会关系
     */
    private String familyMember3Relationship;
    /**
     * 第三位家属联系方式
     */
    private String familyMember3Contact;
    /**
     * 第三位家属工作单位
     */
    private String familyMember3WorkUnit;
    /**
     * 第三位家属居住地址
     */
    private String familyMember3Address;
    /**
     * 第一位家属职业
     */
    private String familyMember1Occupation;
    /**
     * 第二位家属职业
     */
    private String familyMember2Occupation;
    /**
     * 第三位家属职业
     */
    private String familyMember3Occupation;
    /**
     * 第三位家属照片
     */
    private String familyMember3ImageUrl;
    /**
     * 通知操作人身份证号
     */
    private String notificationOperatorSfzh;
    /**
     * 通知操作人
     */
    private String notificationOperator;
    /**
     * 通知操作时间
     */
    private Date notificationOperatorTime;
    /**
     * 登记操作人身份证号
     */
    private String checkOperatorSfzh;
    /**
     * 登记操作人
     */
    private String checkOperator;
    /**
     * 登记操作时间
     */
    private Date checkOperatorTime;
    /**
     * 第三位家属关系证明附件
     */
    private String familyMember3RelationsAttch;

}
