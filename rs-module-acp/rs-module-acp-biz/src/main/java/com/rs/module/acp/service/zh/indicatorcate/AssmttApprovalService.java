package com.rs.module.acp.service.zh.indicatorcate;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.*;
import com.rs.module.acp.entity.zh.AssmttApprovalDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 综合管理-绩效考核-加减分考核审核 Service 接口
 *
 * <AUTHOR>
 */
public interface AssmttApprovalService extends IBaseService<AssmttApprovalDO>{

    /**
     * 创建综合管理-绩效考核-加减分考核审核
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createAssmttApproval(@Valid AssmttApprovalSaveReqVO createReqVO);

    /**
     * 更新综合管理-绩效考核-加减分考核审核
     *
     * @param updateReqVO 更新信息
     */
    void updateAssmttApproval(@Valid AssmttApprovalSaveReqVO updateReqVO);

    /**
     * 删除综合管理-绩效考核-加减分考核审核
     *
     * @param id 编号
     */
    void deleteAssmttApproval(String id);

    /**
     * 获得综合管理-绩效考核-加减分考核审核
     *
     * @param id 编号
     * @return 综合管理-绩效考核-加减分考核审核
     */
    AssmttApprovalDO getAssmttApproval(String id);

    /**
    * 获得综合管理-绩效考核-加减分考核审核分页
    *
    * @param pageReqVO 分页查询
    * @return 综合管理-绩效考核-加减分考核审核分页
    */
    PageResult<AssmttApprovalDO> getAssmttApprovalPage(AssmttApprovalPageReqVO pageReqVO);

    /**
    * 获得综合管理-绩效考核-加减分考核审核列表
    *
    * @param listReqVO 查询条件
    * @return 综合管理-绩效考核-加减分考核审核列表
    */
    List<AssmttApprovalDO> getAssmttApprovalList(AssmttApprovalListReqVO listReqVO);


    void checkAssessor(String assmtRecordId, String indicatorType);


    void zdApproval(AssmttZdldApprovalReqVO approvalReqVO);

    void zwApproval(AssmttZwApprovalReqVO approvalReqVO);

    /**
     * 获得综合管理-绩效考核-记录
     *
     * @param id 编号
     * @return 综合管理-绩效考核-记录
     */
    AssmtRecordRespVO getAssmtRecord(String id);

}
