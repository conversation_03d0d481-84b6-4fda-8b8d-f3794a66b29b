package com.rs.module.acp.service.gj.confinement;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.acp.cons.CommonConstants;
import com.rs.module.acp.controller.admin.gj.vo.confinement.*;
import com.rs.module.acp.entity.gj.confinement.ConfinementRegDO;
import com.rs.module.acp.util.ConfinementUtil;
import com.rs.module.base.util.BspApprovalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.confinement.ConfinementExtendDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.ConfinementExtendDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-延长禁闭呈批 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ConfinementExtendServiceImpl extends BaseServiceImpl<ConfinementExtendDao, ConfinementExtendDO> implements ConfinementExtendService {

    @Resource
    private ConfinementExtendDao confinementExtendDao;
    @Autowired
    private ConfinementRegService  confinementRegService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createConfinementExtend(ConfinementExtendSaveReqVO createReqVO) throws Exception{
        createReqVO.setId(StringUtil.getGuid());
        // 插入
        ConfinementExtendDO confinementExtend = BeanUtils.toBean(createReqVO, ConfinementExtendDO.class);
        //启动流程
        Map<String,String> processResult = BspApprovalUtil.commonStartProcessMap(ConfinementUtil.FLOW_DEF_KEY_CONFINEMENT_EXTEND,
                confinementExtend.getId(), "", "", null, HttpUtils.getAppCode());
        if(StringUtil.isEmpty(processResult.get("actInstId")) || StringUtil.isEmpty(processResult.get("taskId"))){
            throw new ServerException("启动流程失败");
        }
        confinementExtend.setActInstId(processResult.get("actInstId"));
        confinementExtend.setTaskId(processResult.get("taskId"));
        confinementExtend.setStatus(ConfinementUtil.ApprovalStatusEnum.EXTEND_PENDING.getCode());

        confinementExtendDao.insert(confinementExtend);
        confinementRegService.updateStatus(confinementExtend.getConfinementId(), confinementExtend.getStatus());
        // 返回
        return confinementExtend.getId();
    }

    @Override
    public void updateConfinementExtend(ConfinementExtendSaveReqVO updateReqVO) {
        // 校验存在
        validateConfinementExtendExists(updateReqVO.getId());
        // 更新
        ConfinementExtendDO updateObj = BeanUtils.toBean(updateReqVO, ConfinementExtendDO.class);
        confinementExtendDao.updateById(updateObj);
    }

    @Override
    public void deleteConfinementExtend(String id) {
        // 校验存在
        validateConfinementExtendExists(id);
        // 删除
        confinementExtendDao.deleteById(id);
    }

    private void validateConfinementExtendExists(String id) {
        if (confinementExtendDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-延长禁闭呈批数据不存在");
        }
    }

    @Override
    public ConfinementExtendDO getConfinementExtend(String id) {
        return confinementExtendDao.selectById(id);
    }

    @Override
    public PageResult<ConfinementExtendDO> getConfinementExtendPage(ConfinementExtendPageReqVO pageReqVO) {
        return confinementExtendDao.selectPage(pageReqVO);
    }

    @Override
    public List<ConfinementExtendDO> getConfinementExtendList(ConfinementExtendListReqVO listReqVO) {
        return confinementExtendDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean approve(ConfinementExtendApproveVO approveReqVO) throws Exception{
        //从当前登录用户信息设置审批信息默认值
        if(StringUtil.isEmpty(approveReqVO.getApproverXm())) approveReqVO.setApproverXm(SessionUserUtil.getSessionUser().getName());
        if(StringUtil.isEmpty(approveReqVO.getApproverSfzh())) approveReqVO.setApproverSfzh(SessionUserUtil.getSessionUser().getIdCard());
        if(approveReqVO.getApproverTime() == null) approveReqVO.setApproverTime(new Date());
        ConfinementExtendDO confinementExtendDO = confinementExtendDao.selectById(approveReqVO.getId());
        BspApproceStatusEnum isApprove = BspApproceStatusEnum.NOT_PASSED;
        String status = ConfinementUtil.ApprovalStatusEnum.APPROVAL_REJECTED.getCode();
        if(approveReqVO.getApprovalResult().equals("1")){//審批同意 ZD_DDGY_SPZT
            status = ConfinementUtil.ApprovalStatusEnum.CONFINING.getCode();
            isApprove = BspApproceStatusEnum.PASSED;
        }
        approveReqVO.setActInstId(confinementExtendDO.getActInstId());
        approveReqVO.setTaskId(confinementExtendDO.getTaskId());
        approveReqVO.setDefKey(ConfinementUtil.FLOW_DEF_KEY_CONFINEMENT_EXTEND);
        BeanUtil.copyProperties( approveReqVO,confinementExtendDO);
        confinementExtendDO.setStatus(status);
        //调用流程审批接口
        String msgTit = "";
        String msgUrl = "";
        Map<String,String> approvalResult = BspApprovalUtil.approvalProcessMap(approveReqVO,
                isApprove,
                msgTit,
                msgUrl,
                true,
                null,
                null,
                HttpUtils.getAppCode()
        );
        if(StringUtil.isEmpty(approvalResult.get("actInstId")) || StringUtil.isEmpty(approvalResult.get("taskId"))){
            throw new ServerException("审批流程失败");
        }
        ConfinementRegDO confinementRegDO = confinementRegService.getById(confinementExtendDO.getConfinementId());
        long remainDay = DateUtil.betweenDay(confinementRegDO.getActualEndTime(), new Date(), true);
        if(approveReqVO.getApprovalResult().equals("1")){
            //更新实际禁闭结束时间
            Date endDate = confinementRegDO.getConfinementEndDate();
            //endDate+daynum 赋值给actualEndTime
            Date actualEndTime = com.xxl.job.core.util.DateUtil.addDays(endDate, confinementExtendDO.getExtendDay());
            confinementRegDO.setActualEndTime(actualEndTime);
            confinementRegDO.setStatus(ConfinementUtil.ApprovalStatusEnum.CONFINING.getCode());
        }else{
            if(remainDay > 0){
                confinementRegDO.setStatus(ConfinementUtil.ApprovalStatusEnum.CONFINING.getCode());
            }else{
                confinementRegDO.setStatus(ConfinementUtil.ApprovalStatusEnum.RELEASE_PENDING.getCode());
            }
        }
        confinementRegService.updateById(confinementRegDO);
        confinementExtendDao.updateById(confinementExtendDO);
        String taskId = approvalResult.get("taskId");
        if(StringUtil.isNotEmpty(taskId)) {
            return true;
        }
        return false;
    }
    //单个延长禁闭轨迹查询
    @Override
    public List<ConfinementFlowApproveTrackVO> getApproveTrack(String id){
        List<ConfinementFlowApproveTrackVO> list = new ArrayList<>();
        //查询延长进行信息
        ConfinementExtendDO confinementExtendDO = getConfinementExtend(id);
        ConfinementRegDO confinementReg = confinementRegService.getConfinementReg(confinementExtendDO.getConfinementId());

        //查询历史轨迹 进行转换
        JSONObject regBspApprovalTrackVO = BspApprovalUtil.getBpmApi().approveTrack(confinementReg.getActInstId());
        //禁闭呈批
        list.add(ConfinementUtil.buildTrackNodeInfoFromReg(BeanUtils.toBean(confinementReg, ConfinementRegRespVO.class)));
        //审批轨迹构建
        list.addAll(ConfinementUtil.converBspApprovalTrack(regBspApprovalTrackVO,ConfinementUtil.ConfinementEnum.APPROVE));

        //延长禁闭呈批
        list.add(ConfinementUtil.buildTrackNodeInfoFromExtend(BeanUtils.toBean(confinementExtendDO, ConfinementExtendRespVO.class)));
        JSONObject regBspApprovalExtendTrackVO = BspApprovalUtil.getBpmApi().approveTrack(confinementExtendDO.getActInstId());
        list.addAll(ConfinementUtil.converBspApprovalTrack(regBspApprovalExtendTrackVO, ConfinementUtil.ConfinementEnum.EXTENDAPPROVE));
        //查询禁闭带入带出信息

        //查询解除禁闭带入带出信息
       return list;
    }

    @Override
    public List<ConfinementExtendDO> getConfinementExtendByConfinementId(String confinementId) {
        return confinementExtendDao.selectList(new LambdaQueryWrapper<ConfinementExtendDO>()
                .eq(ConfinementExtendDO::getConfinementId, confinementId).orderByAsc(ConfinementExtendDO::getAddTime));
    }
    //查询禁闭到期人员
    //禁闭到期人员列表
}
