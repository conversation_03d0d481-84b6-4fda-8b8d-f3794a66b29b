package com.rs.module.acp.dao.pi;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventBusinessListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventBusinessPageReqVO;
import com.rs.module.acp.entity.pi.PrisonEventBusinessDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-巡视管控-所情关联业务 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PrisonEventBusinessDao extends IBaseDao<PrisonEventBusinessDO> {


    default PageResult<PrisonEventBusinessDO> selectPage(PrisonEventBusinessPageReqVO reqVO) {
        Page<PrisonEventBusinessDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrisonEventBusinessDO> wrapper = new LambdaQueryWrapperX<PrisonEventBusinessDO>()
            .eqIfPresent(PrisonEventBusinessDO::getBusinessType, reqVO.getBusinessType())
            .eqIfPresent(PrisonEventBusinessDO::getBusinessObject, reqVO.getBusinessObject())
            .eqIfPresent(PrisonEventBusinessDO::getOperatePerson, reqVO.getOperatePerson())
            .betweenIfPresent(PrisonEventBusinessDO::getOperateTime, reqVO.getOperateTime())
            .eqIfPresent(PrisonEventBusinessDO::getBusinessId, reqVO.getBusinessId())
            .eqIfPresent(PrisonEventBusinessDO::getEventId, reqVO.getEventId())
            .eqIfPresent(PrisonEventBusinessDO::getStatus, reqVO.getStatus())
            .likeIfPresent(PrisonEventBusinessDO::getCreateUserRoleName, reqVO.getCreateUserRoleName())
            .eqIfPresent(PrisonEventBusinessDO::getBusinessObjectId, reqVO.getBusinessObjectId())
            .eqIfPresent(PrisonEventBusinessDO::getHandleId, reqVO.getHandleId())
            .eqIfPresent(PrisonEventBusinessDO::getHandleMethod, reqVO.getHandleMethod())
            .eqIfPresent(PrisonEventBusinessDO::getChangeContent, reqVO.getChangeContent())
            .eqIfPresent(PrisonEventBusinessDO::getAttUrl, reqVO.getAttUrl())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PrisonEventBusinessDO::getAddTime);
        }
        Page<PrisonEventBusinessDO> prisonEventBusinessPage = selectPage(page, wrapper);
        return new PageResult<>(prisonEventBusinessPage.getRecords(), prisonEventBusinessPage.getTotal());
    }
    default List<PrisonEventBusinessDO> selectList(PrisonEventBusinessListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrisonEventBusinessDO>()
            .eqIfPresent(PrisonEventBusinessDO::getBusinessType, reqVO.getBusinessType())
            .eqIfPresent(PrisonEventBusinessDO::getBusinessObject, reqVO.getBusinessObject())
            .eqIfPresent(PrisonEventBusinessDO::getOperatePerson, reqVO.getOperatePerson())
            .betweenIfPresent(PrisonEventBusinessDO::getOperateTime, reqVO.getOperateTime())
            .eqIfPresent(PrisonEventBusinessDO::getBusinessId, reqVO.getBusinessId())
            .eqIfPresent(PrisonEventBusinessDO::getEventId, reqVO.getEventId())
            .eqIfPresent(PrisonEventBusinessDO::getStatus, reqVO.getStatus())
            .likeIfPresent(PrisonEventBusinessDO::getCreateUserRoleName, reqVO.getCreateUserRoleName())
            .eqIfPresent(PrisonEventBusinessDO::getBusinessObjectId, reqVO.getBusinessObjectId())
            .eqIfPresent(PrisonEventBusinessDO::getHandleId, reqVO.getHandleId())
            .eqIfPresent(PrisonEventBusinessDO::getHandleMethod, reqVO.getHandleMethod())
            .eqIfPresent(PrisonEventBusinessDO::getChangeContent, reqVO.getChangeContent())
            .eqIfPresent(PrisonEventBusinessDO::getAttUrl, reqVO.getAttUrl())
        .orderByDesc(PrisonEventBusinessDO::getAddTime));    }


    }
