package com.rs.module.acp.service.zh.deathregister;

import cn.hutool.core.bean.BeanUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.acp.cons.CommonConstants;
import com.rs.module.acp.cons.DeathRegisterStatus;
import com.rs.module.acp.cons.SugDesusBusinessStatus;
import com.rs.module.acp.controller.admin.zh.vo.deathregister.*;
import com.rs.module.acp.entity.zh.DeathRegisterFilesDO;
import com.rs.module.base.util.BspApprovalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.zh.DeathRegisterDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.zh.DeathRegisterDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-综合管理-死亡登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DeathRegisterServiceImpl extends BaseServiceImpl<DeathRegisterDao, DeathRegisterDO> implements DeathRegisterService {

    @Resource
    private DeathRegisterDao deathRegisterDao;
    @Autowired
    private DeathRegisterFilesService sacpDeathRegisterFilesService;
    @Override
    public String createDeathRegister(DeathRegisterSaveReqVO createReqVO) {
        // 插入
        DeathRegisterDO deathRegister = BeanUtils.toBean(createReqVO, DeathRegisterDO.class);
        deathRegisterDao.insert(deathRegister);
        // 返回
        return deathRegister.getId();
    }

    @Override
    public void updateDeathRegister(DeathRegisterSaveReqVO updateReqVO) {
        // 校验存在
        validateDeathRegisterExists(updateReqVO.getId());
        // 更新
        DeathRegisterDO updateObj = BeanUtils.toBean(updateReqVO, DeathRegisterDO.class);
        deathRegisterDao.updateById(updateObj);
    }

    @Override
    public void deleteDeathRegister(String id) {
        // 校验存在
        validateDeathRegisterExists(id);
        // 删除
        deathRegisterDao.deleteById(id);
    }

    private void validateDeathRegisterExists(String id) {
        if (deathRegisterDao.selectById(id) == null) {
            throw new ServerException("实战平台-综合管理-死亡登记数据不存在");
        }
    }

    @Override
    public DeathRegisterDO getDeathRegister(String id) {
        return deathRegisterDao.selectById(id);
    }

    /**
     * 实战平台-综合管理-死亡登记 流程defKey
     */
    private final static String FLOW_NAME = "shizhanpingtaizongheguanlisiwangdengji";
    /**
     * 审批
     * @param approveReqVO
     * @return
     */
    @Override
    public String deathRegisterApprove(DeathApproveSaveVO approveReqVO) throws  Exception{
        if(StringUtil.isEmpty(approveReqVO.getApproverXm())) approveReqVO.setApproverXm(SessionUserUtil.getSessionUser().getName());
        if(StringUtil.isEmpty(approveReqVO.getApproverSfzh())) approveReqVO.setApproverSfzh(SessionUserUtil.getSessionUser().getIdCard());
        if(approveReqVO.getApproverTime() == null) approveReqVO.setApproverTime(new Date());
        DeathRegisterDO entity = BeanUtils.toBean(approveReqVO,DeathRegisterDO.class);
        DeathRegisterDO deathRegister = getById(approveReqVO.getId());
        BspApproceStatusEnum isApprove = BspApproceStatusEnum.NOT_PASSED;
        String status = DeathRegisterStatus.APPROVE_NOPASS.getCode();
        if(approveReqVO.getApprovalResult().equals("1")){//審批同意 ZD_DDGY_SPZT
            status = DeathRegisterStatus.APPROVE_PASS.getCode();
            isApprove = BspApproceStatusEnum.PASSED;
        }
        approveReqVO.setActInstId(deathRegister.getActInstId());
        approveReqVO.setTaskId(deathRegister.getTaskId());
        approveReqVO.setDefKey(FLOW_NAME);
        BeanUtil.copyProperties(approveReqVO, entity);
        String msgTit = "";
        String msgUrl = "";
        Map<String, Object> variables = new HashMap<>();
        Map<String, String> approvalResult = BspApprovalUtil.approvalProcessMap(approveReqVO,
                isApprove,
                msgTit,
                msgUrl,
                true,
                null,
                null,
                HttpUtils.getAppCode()
        );
        if(StringUtil.isEmpty(approvalResult.get("actInstId")) || StringUtil.isEmpty(approvalResult.get("taskId"))){
            throw new ServerException("审批流程失败");
        }
        String taskId = approvalResult.get("taskId"); // 更新任务ID
        entity.setTaskId(taskId);
        entity.setStatus(status);
        this.updateById(entity);
        return entity.getId();
    }

    /**
     * 尸体处理保存
     * @param reqVO
     * @return
     */
    @Override
    public String corpseHandleSave(DeathCorpseHandleSaveVO reqVO) throws  Exception{
        DeathRegisterDO entity = BeanUtils.toBean(reqVO,DeathRegisterDO.class);
        DeathRegisterDO deathRegister = getById(reqVO.getId());
        if(StringUtil.isEmpty(entity.getStatus())){
            entity.setStatus(deathRegister.getStatus());
        }
        SessionUser user = SessionUserUtil.getSessionUser();
        entity.setCorpseHandleHandleUser(user.getIdCard());
        entity.setCorpseHandleHandleUserName(user.getName());
        entity.setCorpseHandleHandleTime(new Date());
        if("0".equals(reqVO.getSaveType())){//0 保存,1 提交
            entity.setStatus(DeathRegisterStatus.CORPSEHANDLE.getCode());
        }else{
            entity.setStatus(DeathRegisterStatus.WAIT_APPROVE.getCode());
            Map<String, Object> variables = new HashMap<>();
            String msgTit = "";
            String msgUrl = "";
            //启动流程
            Map<String,String> processResult = BspApprovalUtil.commonStartProcessMap(FLOW_NAME, entity.getId(), msgTit, msgUrl, variables, HttpUtils.getAppCode());
            if(StringUtil.isEmpty(processResult.get("actInstId")) || StringUtil.isEmpty(processResult.get("taskId"))){
                throw new Exception("启动流程失败");
            }
            entity.setActInstId(processResult.get("actInstId"));
            entity.setTaskId(processResult.get("taskId"));
        }

        this.updateById(entity);
        return entity.getId();
    }

    /**
     * 死亡鉴定保存
     * @param reqVO
     * @return
     */
    @Override
    public String deathAppraiseSave(DeathAppraiseSaveVO reqVO) {
        DeathRegisterDO entity = BeanUtils.toBean(reqVO,DeathRegisterDO.class);
        //DeathRegisterDO deathRegister = getById(reqVO.getId());
        entity.setAppraiseHandleTime(new Date());
        SessionUser user = SessionUserUtil.getSessionUser();
        entity.setAppraiseHandleUser(user.getIdCard());
        entity.setAppraiseHandleUserName(user.getName());
        if("0".equals(reqVO.getSaveType())){//0 保存,1 提交
            entity.setStatus(DeathRegisterStatus.APPRAISE_SAVE.getCode());
        }else{
            entity.setStatus(DeathRegisterStatus.CORPSEHANDLE_SAVE.getCode());
        }

        this.updateById(entity);
        sacpDeathRegisterFilesService.deleteAndSaveFile(reqVO.getId(),reqVO.getFileList());
        return entity.getId();
    }

    /**
     * 死亡登记保存
     * @param reqVO
     * @return
     */
    @Override
    public String deathRegisterSave(DeathRegisterSaveReqVO reqVO) {
        DeathRegisterDO entity = BeanUtils.toBean(reqVO,DeathRegisterDO.class);
        if("0".equals(reqVO.getSaveType())){//0 保存,1 提交
            entity.setStatus(DeathRegisterStatus.SAVEED.getCode());
        }else{
            if(entity.getDeathAppraise() == 0){//不需要死亡鉴定
                entity.setStatus(DeathRegisterStatus.CORPSEHANDLE_SAVE.getCode());
            }else{
                entity.setStatus(DeathRegisterStatus.APPRAISE.getCode());
            }
        }
        if(StringUtil.isNotEmpty(reqVO.getId())){
            this.updateById(entity);
        }else{
            entity.setId(StringUtil.getGuid());
            this.save(entity);
        }
        return entity.getId();
    }
    @Override
    public DeathRegisterRespVO getDeathRegisterDetailById(String id) {
        DeathRegisterDO sacpDeathRegister = getById(id);
        DeathRegisterRespVO vo = BeanUtils.toBean(sacpDeathRegister, DeathRegisterRespVO.class);
        //vo.setApprovalStatusName(dictService.dictValue("SWDJ_SPZT",vo.getApprovalStatus()+""));
        List<DeathRegisterFilesDO> fileList = sacpDeathRegisterFilesService.getFileList(id);
        vo.setFileList(fileList);
        return vo;
    }
}
