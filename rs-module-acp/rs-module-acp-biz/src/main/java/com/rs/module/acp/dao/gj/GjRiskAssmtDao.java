package com.rs.module.acp.dao.gj;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.riskassmt.GjRiskAssmtListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.riskassmt.GjRiskAssmtPageReqVO;
import com.rs.module.acp.entity.gj.GjRiskAssmtDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 实战平台-管教业务-风险评估 Dao
*
* <AUTHOR>
*/
@Mapper
public interface GjRiskAssmtDao extends IBaseDao<GjRiskAssmtDO> {


    default PageResult<GjRiskAssmtDO> selectPage(GjRiskAssmtPageReqVO reqVO) {
        Page<GjRiskAssmtDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<GjRiskAssmtDO> wrapper = new LambdaQueryWrapperX<GjRiskAssmtDO>()
            .eqIfPresent(GjRiskAssmtDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(GjRiskAssmtDO::getRiskType, reqVO.getRiskType())
            .eqIfPresent(GjRiskAssmtDO::getOldRiskLevel, reqVO.getOldRiskLevel())
            .eqIfPresent(GjRiskAssmtDO::getRiskLevel, reqVO.getRiskLevel())
            .eqIfPresent(GjRiskAssmtDO::getAssmtReason, reqVO.getAssmtReason())
            .eqIfPresent(GjRiskAssmtDO::getSpecificAssmtReason, reqVO.getSpecificAssmtReason())
            .eqIfPresent(GjRiskAssmtDO::getStatus, reqVO.getStatus())
            .eqIfPresent(GjRiskAssmtDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(GjRiskAssmtDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(GjRiskAssmtDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(GjRiskAssmtDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(GjRiskAssmtDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(GjRiskAssmtDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(GjRiskAssmtDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(GjRiskAssmtDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(GjRiskAssmtDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(GjRiskAssmtDO::getAddTime);
        }
        Page<GjRiskAssmtDO> gjRiskAssmtPage = selectPage(page, wrapper);
        return new PageResult<>(gjRiskAssmtPage.getRecords(), gjRiskAssmtPage.getTotal());
    }
    default List<GjRiskAssmtDO> selectList(GjRiskAssmtListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<GjRiskAssmtDO>()
            .eqIfPresent(GjRiskAssmtDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(GjRiskAssmtDO::getRiskType, reqVO.getRiskType())
            .eqIfPresent(GjRiskAssmtDO::getOldRiskLevel, reqVO.getOldRiskLevel())
            .eqIfPresent(GjRiskAssmtDO::getRiskLevel, reqVO.getRiskLevel())
            .eqIfPresent(GjRiskAssmtDO::getAssmtReason, reqVO.getAssmtReason())
            .eqIfPresent(GjRiskAssmtDO::getSpecificAssmtReason, reqVO.getSpecificAssmtReason())
            .eqIfPresent(GjRiskAssmtDO::getStatus, reqVO.getStatus())
            .eqIfPresent(GjRiskAssmtDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(GjRiskAssmtDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(GjRiskAssmtDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(GjRiskAssmtDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(GjRiskAssmtDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(GjRiskAssmtDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(GjRiskAssmtDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(GjRiskAssmtDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(GjRiskAssmtDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(GjRiskAssmtDO::getAddTime));    }


    }
