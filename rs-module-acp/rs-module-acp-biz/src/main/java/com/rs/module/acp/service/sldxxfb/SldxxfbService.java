package com.rs.module.acp.service.sldxxfb;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.sldxxfb.vo.WgdjVO;
import com.rs.module.base.entity.pm.PrisonerInDO;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 实战平台-所领导信息发布 Service 接口
 *
 * <AUTHOR>
 */
public interface SldxxfbService extends IBaseService<PrisonerInDO> {

    /**
     * 查询当前羁押人数（含各所统计及总体）
     *
     * @param endDate 指定日期，用于统计“昨日”数据
     * @return List<Map<String, Object>> 包含 org_code、nowTotalNum、historyTotalNum
     */
    List<JSONObject> getAllPrisonerCount(Date endDate);

    /**
     * 查询近一年按月分组的羁押人数
     *
     * @return List<Map<String, Object>> 包含 month 和 count
     */
    List<JSONObject> getAllPrisonerInOneYear();
    List<JSONObject> getAllPrisonerOutOneYear();
    /**
     * 查询今日、昨日、今年、当前羁押总人数
     *
     * @param orgCode 机构编码（可选）
     * @return Map<String, Object> 包含 today_count、yesterday_count、year_count、now_total_count、yesterdayTotalNum
     */
    Map<String, Integer> getInCount(String orgCode);

    /**
     * 查询各监所设计关押量总数
     *
     * @return List<Map<String, Object>> 包含 org_code 和 plan_imprisonment_amount
     */
    List<JSONObject> getAllPrisonerDesignAmount();

    /**
     * 查询出所人数（今日、昨日、今年、当前）
     *
     * @param orgCode 机构编码（可选）
     * @return Map<String, Object> 包含 today_count、yesterday_count、year_count、now_total_count、yesterdayTotalNum
     */
    Map<String, Integer> getOutCount(String orgCode);

    JSONObject zdsj();

    JSONObject xxry(String orgCode);

    JSONObject csry(String orgCode);

    JSONObject ssjd(String orgCode);

    PageResult<WgdjVO> getWggjPage(int pageNo, int pageSize, String timeRange, String handleStatus, String code, String codeType);

    JSONObject getWggjAllCount(String timeRange, String code, String codeType);

    List<JSONObject> fxryqs(String orgCode, String timeRange);

    JSONObject fxryfb(String orgCode);

    List<JSONObject> wgqs(String orgCode, String timeRange);

    JSONObject gzry(String orgCode);

    JSONObject getRoomMonitorIndex(String roomId, String orgCode);
}
