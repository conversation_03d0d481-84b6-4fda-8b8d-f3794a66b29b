package com.rs.module.acp.controller.admin.ds.sjbs;

import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyDataSubmitKssRespVO;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyLawyerTopRespVO;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyLawyerTopSaveReqVO;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyPrisonerTopRespVO;
import com.rs.module.acp.entity.ds.sjbs.WeeklyDataSubmitKssDO;
import com.rs.module.acp.entity.ds.sjbs.WeeklyPrisonerTopDO;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.ds.sjbs.WeeklyLawyerTopDO;
import com.rs.module.acp.service.ds.sjbs.WeeklyLawyerTopService;

@Api(tags = "实战平台-数据固化-每周会见律师排名")
@RestController
@RequestMapping("/acp/ds/weeklyLawyerTop")
@Validated
public class WeeklyLawyerTopController {

    @Resource
    private WeeklyLawyerTopService weeklyLawyerTopService;

    /*@PostMapping("/create")
    @ApiOperation(value = "创建实战平台-数据固化-每周会见律师排名")
    public CommonResult<String> createWeeklyLawyerTop(@Valid @RequestBody WeeklyLawyerTopSaveReqVO createReqVO) {
        return success(weeklyLawyerTopService.createWeeklyLawyerTop(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-数据固化-每周会见律师排名")
    public CommonResult<Boolean> updateWeeklyLawyerTop(@Valid @RequestBody WeeklyLawyerTopSaveReqVO updateReqVO) {
        weeklyLawyerTopService.updateWeeklyLawyerTop(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-数据固化-每周会见律师排名")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteWeeklyLawyerTop(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           weeklyLawyerTopService.deleteWeeklyLawyerTop(id);
        }
        return success(true);
    }*/

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-数据固化-每周会见律师排名")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<WeeklyLawyerTopRespVO> getWeeklyLawyerTop(@RequestParam("id") String id) {
        WeeklyLawyerTopDO weeklyLawyerTop = weeklyLawyerTopService.getWeeklyLawyerTop(id);
        return success(BeanUtils.toBean(weeklyLawyerTop, WeeklyLawyerTopRespVO.class));
    }
    @GetMapping("/getByDate")
    @ApiOperation(value = "获得实战平台-数据固化-每周数据报送(看守所)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编号"),
            @ApiImplicitParam(name = "startDate", value = "开始时间"),
            @ApiImplicitParam(name = "endDate", value = "结束时间")
    })
    public CommonResult<List<WeeklyLawyerTopRespVO>> getByDate(
            @RequestParam(value = "orgCode",required = false) String orgCode,
            @RequestParam(value = "startDate",required = false) String startDate,
            @RequestParam(value = "endDate",required = false) String endDate) {
        List<WeeklyLawyerTopDO> data = weeklyLawyerTopService.getWeeklyLawyerTopByDate(startDate,endDate,orgCode);
        return success(BeanUtils.toBean(data, WeeklyLawyerTopRespVO.class));
    }
    @GetMapping("/getByWeeklyDataSubmitId")
    @ApiOperation(value = "获得实战平台-数据固化-每周数据报送(看守所)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "weeklyDataSubmitId", value = "每周报送ID")
    })
    public CommonResult<List<WeeklyLawyerTopRespVO>> getByWeeklyDataSubmitId(@RequestParam(value = "weeklyDataSubmitId") String weeklyDataSubmitId) {
        List<WeeklyLawyerTopDO> data = weeklyLawyerTopService.getByWeeklyDataSubmitId(weeklyDataSubmitId);
        return success(BeanUtils.toBean(data, WeeklyLawyerTopRespVO.class));
    }
}
