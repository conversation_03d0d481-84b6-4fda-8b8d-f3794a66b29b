package com.rs.module.acp.job.patrolRecord;

import com.bsp.common.util.StringUtil;
import com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseRespWearVO;
import com.rs.module.acp.entity.pi.PatrolRecordDO;
import com.rs.module.acp.service.gj.equipment.EquipmentUseService;
import com.rs.module.acp.service.pi.PatrolRecordService;
import com.rs.module.base.entity.pm.AreaDO;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class PatrolRecordJob {
    @Autowired
    private PatrolRecordService patrolRecordService;
    @Autowired
    private EquipmentUseService equipmentUseService;
    /**
     * 定时巡控
     * todo 不同所配置不同开始时间进行定时推送 目前暂时保持一致
     */
    @XxlJob("regularPatrolRecord")
    public void regularPatrolRecord() {
        List<AreaDO> listArea = getAllDivisionControlArea();
        Date nowDate = new Date();
        //查询分控室对应是那个监所
        for (AreaDO areaDO : listArea){
            PatrolRecordDO entity = initPatrolRecordDOByAreaDO(areaDO,nowDate);
            entity.setTodoSource("01");//系统定时推送
            entity.setTodoReason("定时巡视登记");
            entity.setTodoPerson("系统定时任务");
            patrolRecordService.save(entity);
        }
    }

    /**
     * 戒具加戴人员 每半小时每个分控室推送一条巡控任务
     */
    @XxlJob("equipmentUserPatrolRecord")
    public void equipmentUserPatrolRecord() {
        Date nowDate = new Date();
        //查询当前时间在 加戴戒具的人员 需要返回 监室id，监所编码，戒具加戴申请人信息
        List<EquipmentUseRespWearVO> equipmentUseRespVOList = equipmentUseService.getWearEquipmentUse(null,nowDate);
        List<AreaDO> listArea = getAllDivisionControlArea();
        //按照分控室ID进行分组
        Map<String, List<EquipmentUseRespWearVO>> equipmentUseRespWearMap = equipmentUseRespVOList.stream().collect(Collectors.groupingBy(EquipmentUseRespWearVO::getDivisionControlAreaId));
        //查询分控室对应是那个监所
        for (AreaDO areaDO : listArea){
            PatrolRecordDO entity = initPatrolRecordDOByAreaDO(areaDO,nowDate);
            entity.setTodoSource("03");//加戴戒具
            entity.setTodoReason("定时巡视登记");
            entity.setStatus("01");//未处理
            entity.setTodoPushTime(nowDate);
            List<EquipmentUseRespWearVO> list = equipmentUseRespWearMap.get(areaDO.getId());
            if (list == null) list = Collections.emptyList();

            Set<String> roomIdSet = new LinkedHashSet<>(); // 保持插入顺序并去重
            StringBuilder roomIdStrBuilder = new StringBuilder();
            StringBuilder jgrybmBuilder = new StringBuilder();
            StringBuilder jgryxmBuilder = new StringBuilder();
            for (EquipmentUseRespWearVO equipmentUseRespWearVO : list) {
                entity.setTodoPerson(equipmentUseRespWearVO.getAddUserName());
                entity.setTodoPersonSfzh(equipmentUseRespWearVO.getAddUser());
                String roomId = equipmentUseRespWearVO.getRoomId();

                if (roomId != null && !roomId.isEmpty()) {
                    roomIdSet.add(roomId);
                }
                // 拼接编码
                String jgrybm = equipmentUseRespWearVO.getJgrybm();
                if (!StringUtil.isNullBlank(jgrybm)) {
                    if (jgrybmBuilder.length() > 0) {
                        jgrybmBuilder.append(",");
                    }
                    jgrybmBuilder.append(jgrybm);
                }

                // 拼接姓名
                String jgryxm = equipmentUseRespWearVO.getJgryxm();
                if (!StringUtil.isNullBlank(jgryxm)) {
                    if (jgryxmBuilder.length() > 0) {
                        jgryxmBuilder.append(",");
                    }
                    jgryxmBuilder.append(jgryxm);
                }
            }
            // 拼接去重后的 roomId
            for (String roomId : roomIdSet) {
                if (roomIdStrBuilder.length() > 0) {
                    roomIdStrBuilder.append(",");
                }
                roomIdStrBuilder.append(roomId);
            }
            entity.setJgrybm(jgrybmBuilder.toString());
            entity.setJgryxm(jgryxmBuilder.toString());
            entity.setTodoPost("管教");
            entity.setRoomId(roomIdStrBuilder.toString());
            patrolRecordService.save(entity);
        }
    }

    /**
     * 查询所有分控室
     * @return
     */
    private List<AreaDO> getAllDivisionControlArea(){
        return patrolRecordService.getAreaByAreaType(null,false);
    }
    /**
     * 使用区域数据初始化巡视登记数据
     * @param areaDO
     * @param nowDate
     * @return
     */
    private PatrolRecordDO initPatrolRecordDOByAreaDO(AreaDO areaDO,Date nowDate){
        PatrolRecordDO entity = new PatrolRecordDO();
        entity.setPatrolRoomId(areaDO.getId());
        entity.setPatrolRoomName(areaDO.getAreaName());
        entity.setRecordType("01");//自动
        entity.setTodoPushTime(nowDate);
        entity.setOrgCode(areaDO.getOrgCode());
        entity.setOrgName(areaDO.getOrgName());
        entity.setRegCode(areaDO.getRegCode());
        entity.setRegName(areaDO.getRegName());
        entity.setCityCode(areaDO.getCityCode());
        entity.setCityName(areaDO.getCityName());
        entity.setStatus("01");//未处理
        entity.setIsPostCoordination(Short.valueOf("0"));
        entity.setIsDel(false);
        entity.setAddTime(nowDate);
        return entity;
    }
}
