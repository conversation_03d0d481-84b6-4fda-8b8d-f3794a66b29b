package com.rs.module.acp.service.db.components;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 动态获取对应模块的入所信息推送处理器
 *
 * 当前支持：
 * - kss（看守所）
 * - jls（拘留所）
 * - jds（戒毒所）
 */
@Component
public class PrisonerInInfoSyncContext {

    private final Map<String, PrisonerInInfoSyncHandler> handlerMap;

    public PrisonerInInfoSyncContext(
            @Qualifier("kssPrisonerInInfoHandler") PrisonerInInfoSyncHandler kssHandler,
            @Qualifier("jlsPrisonerInInfoHandler") PrisonerInInfoSyncHandler jlsHandler,
            @Qualifier("jdsPrisonerInInfoHandler") PrisonerInInfoSyncHandler jdsHandler) {
        this.handlerMap = new HashMap<>();
        handlerMap.put("kss", kssHandler);
        handlerMap.put("jls", jlsHandler);
        handlerMap.put("jds", jdsHandler);
    }

    /**
     * 获取指定模块类型的入所信息推送处理器
     *
     * @param moduleType 模块类型（"kss", "jls", "jds"）
     * @return 对应的推送处理器
     * @throws IllegalArgumentException 如果不支持该模块类型
     */
    public PrisonerInInfoSyncHandler getHandler(String moduleType) {
        if (moduleType == null || moduleType.isEmpty()) {
            moduleType = "kss";
        }
        PrisonerInInfoSyncHandler handler = handlerMap.get(moduleType);
        if (handler == null) {
            throw new IllegalArgumentException("Unsupported prisoner info sync module type: " + moduleType);
        }
        return handler;
    }
}
