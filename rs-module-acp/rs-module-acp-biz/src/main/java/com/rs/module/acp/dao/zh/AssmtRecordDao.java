package com.rs.module.acp.dao.zh;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmtRecordListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmtRecordPageReqVO;
import com.rs.module.acp.entity.zh.AssmtRecordDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 综合管理-绩效考核-记录 Dao
*
* <AUTHOR>
*/
@Mapper
public interface AssmtRecordDao extends IBaseDao<AssmtRecordDO> {


    default PageResult<AssmtRecordDO> selectPage(AssmtRecordPageReqVO reqVO) {
        Page<AssmtRecordDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<AssmtRecordDO> wrapper = new LambdaQueryWrapperX<AssmtRecordDO>()
            .eqIfPresent(AssmtRecordDO::getAssessedSfzh, reqVO.getAssessedSfzh())
            .likeIfPresent(AssmtRecordDO::getAssessedName, reqVO.getAssessedName())
            .likeIfPresent(AssmtRecordDO::getPostName, reqVO.getPostName())
            .eqIfPresent(AssmtRecordDO::getAssmtMonth, reqVO.getAssmtMonth())
            .eqIfPresent(AssmtRecordDO::getIndicatorCateId, reqVO.getIndicatorCateId())
            .likeIfPresent(AssmtRecordDO::getIndicatorCateName, reqVO.getIndicatorCateName())
            .betweenIfPresent(AssmtRecordDO::getExpiryDate, reqVO.getExpiryDate())
            .eqIfPresent(AssmtRecordDO::getAddsubtractScore, reqVO.getAddsubtractScore())
            .eqIfPresent(AssmtRecordDO::getSubjectiveScore, reqVO.getSubjectiveScore())
            .eqIfPresent(AssmtRecordDO::getTotalScore, reqVO.getTotalScore())
            .eqIfPresent(AssmtRecordDO::getStatus, reqVO.getStatus())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(AssmtRecordDO::getAddTime);
        }
        Page<AssmtRecordDO> assmtRecordPage = selectPage(page, wrapper);
        return new PageResult<>(assmtRecordPage.getRecords(), assmtRecordPage.getTotal());
    }
    default List<AssmtRecordDO> selectList(AssmtRecordListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AssmtRecordDO>()
            .eqIfPresent(AssmtRecordDO::getAssessedSfzh, reqVO.getAssessedSfzh())
            .likeIfPresent(AssmtRecordDO::getAssessedName, reqVO.getAssessedName())
            .likeIfPresent(AssmtRecordDO::getPostName, reqVO.getPostName())
            .eqIfPresent(AssmtRecordDO::getAssmtMonth, reqVO.getAssmtMonth())
            .eqIfPresent(AssmtRecordDO::getIndicatorCateId, reqVO.getIndicatorCateId())
            .likeIfPresent(AssmtRecordDO::getIndicatorCateName, reqVO.getIndicatorCateName())
            .betweenIfPresent(AssmtRecordDO::getExpiryDate, reqVO.getExpiryDate())
            .eqIfPresent(AssmtRecordDO::getAddsubtractScore, reqVO.getAddsubtractScore())
            .eqIfPresent(AssmtRecordDO::getSubjectiveScore, reqVO.getSubjectiveScore())
            .eqIfPresent(AssmtRecordDO::getTotalScore, reqVO.getTotalScore())
            .eqIfPresent(AssmtRecordDO::getStatus, reqVO.getStatus())
        .orderByDesc(AssmtRecordDO::getAddTime));    }


    }
