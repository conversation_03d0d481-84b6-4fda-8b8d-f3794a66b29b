package com.rs.module.acp.entity.wb;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-窗口业务-律师会见登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_wb_lawyer_meeting")
@KeySequence("acp_wb_lawyer_meeting_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LawyerMeetingDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 第一位律师ID
     */
    private String lawyer1Id;
    /**
     * 第一位律师姓名
     */
    private String lawyer1Name;
    /**
     * 第一位律师性别
     */
    private String lawyer1Gender;
    /**
     * 第一位律师证件号码
     */
    private String lawyer1IdNumber;
    /**
     * 第一位律师联系方式
     */
    private String lawyer1Contact;
    /**
     * 第一位律师类型
     */
    private String lawyer1Type;
    /**
     * 第一位律师执业证号码
     */
    private String lawyer1PracticeLicenseNumber;
    /**
     * 第一位律师委托截止阶段
     */
    private String lawyer1EntrustStage;
    /**
     * 第一位律师所属单位
     */
    private String lawyer1Firm;
    /**
     * 第一位律师委托类型
     */
    private String lawyer1EntrustType;
    /**
     * 第一位律师委托人姓名
     */
    private String lawyer1PrincipalName;
    /**
     * 第一位律师委托人证件号
     */
    private String lawyer1PrincipalIdNumber;
    /**
     * 第一位律师委托书类型
     */
    private String lawyer1PowerOfAttorneyType;
    /**
     * 第一位律师介绍信编号
     */
    private String lawyer1LetterNumber;
    /**
     * 第一位律师委托书上传后存储路径
     */
    private String lawyer1PowerOfAttorneyPath;
    /**
     * 第一位律师执业证书上传后存储路径
     */
    private String lawyer1PracticeCertificatePa;
    /**
     * 第二位律师ID
     */
    private String lawyer2Id;
    /**
     * 第二位律师姓名
     */
    private String lawyer2Name;
    /**
     * 第二位律师性别
     */
    private String lawyer2Gender;
    /**
     * 第二位律师证件号码
     */
    private String lawyer2IdNumber;
    /**
     * 第二位律师联系方式
     */
    private String lawyer2Contact;
    /**
     * 第二位律师类型
     */
    private String lawyer2Type;
    /**
     * 第二位律师执业证号码
     */
    private String lawyer2PracticeLicenseNumber;
    /**
     * 第二位律师委托截止阶段
     */
    private String lawyer2EntrustStage;
    /**
     * 第二位律师所属单位
     */
    private String lawyer2Firm;
    /**
     * 第二位律师委托类型
     */
    private String lawyer2EntrustType;
    /**
     * 第二位律师委托人姓名
     */
    private String lawyer2PrincipalName;
    /**
     * 第二位律律师委托人证件号
     */
    private String lawyer2PrincipalIdNumber;
    /**
     * 第二位律律师委托书类型
     */
    private String lawyer2PowerOfAttorneyType;
    /**
     * 第二位律师介绍信编号
     */
    private String lawyer2LetterNumber;
    /**
     * 第二位律律师委托书上传后的存储路径
     */
    private String lawyer2PowerOfAttorneyPath;
    /**
     * 第二位律律师执业证书上传后存储路径
     */
    private String lawyer2PracticeCertificatePa;
    /**
     * 第三位律师ID
     */
    private String lawyer3Id;
    /**
     * 第三位律师姓名
     */
    private String lawyer3Name;
    /**
     * 第三位律师性别
     */
    private String lawyer3Gender;
    /**
     * 第三位律师证件号码
     */
    private String lawyer3IdNumber;
    /**
     * 第三位律师联系方式
     */
    private String lawyer3Contact;
    /**
     * 第三位律师类型
     */
    private String lawyer3Type;
    /**
     * 第三位律师执业证号码
     */
    private String lawyer3PracticeLicenseNumber;
    /**
     * 第三位律师委托截止阶段
     */
    private String lawyer3EntrustStage;
    /**
     * 第三位律师所属单位
     */
    private String lawyer3Firm;
    /**
     * 第三位律师委托类型
     */
    private String lawyer3EntrustType;
    /**
     * 第三位律师委托人姓名
     */
    private String lawyer3PrincipalName;
    /**
     * 第三位律律师委托人证件号
     */
    private String lawyer3PrincipalIdNumber;
    /**
     * 第三位律律师委托书类型
     */
    private String lawyer3PowerOfAttorneyType;
    /**
     * 第三位律师介绍信编号
     */
    private String lawyer3LetterNumber;
    /**
     * 第三位律律师委托书上传后的存储路径
     */
    private String lawyer3PowerOfAttorneyPath;
    /**
     * 第三位律律师执业证书上传后存储路径
     */
    private String lawyer3PracticeCertificatePa;
    /**
     * 第四位律师ID
     */
    private String lawyer4Id;
    /**
     * 第四位律师姓名
     */
    private String lawyer4Name;
    /**
     * 第四位律师性别
     */
    private String lawyer4Gender;
    /**
     * 第四位律师证件号码
     */
    private String lawyer4IdNumber;
    /**
     * 第四位律师联系方式
     */
    private String lawyer4Contact;
    /**
     * 第四位律师类型
     */
    private String lawyer4Type;
    /**
     * 第四位律师执业证号码
     */
    private String lawyer4PracticeLicenseNumber;
    /**
     * 第四位律师委托截止阶段
     */
    private String lawyer4EntrustStage;
    /**
     * 第四位律师所属单位
     */
    private String lawyer4Firm;
    /**
     * 第四位律师委托类型
     */
    private String lawyer4EntrustType;
    /**
     * 第四位律师委托人姓名
     */
    private String lawyer4PrincipalName;
    /**
     * 第四位律律师委托人证件号
     */
    private String lawyer4PrincipalIdNumber;
    /**
     * 第四位律律师委托书类型
     */
    private String lawyer4PowerOfAttorneyType;
    /**
     * 第四位律律师委托书上传后的存储路径
     */
    private String lawyer4PowerOfAttorneyPath;
    /**
     * 第四位律律师执业证书上传后存储路径
     */
    private String lawyer4PracticeCertificatePa;
    /**
     * 第四位律师介绍信编号
     */
    private String lawyer4LetterNumber;
    /**
     * 会见方式
     */
    private String meetingMethod;
    /**
     * 预约会见时间
     */
    private Date appointmentTime;
    /**
     * 预约会见时间
     */
    private String appointmentTimeSlot;
    /**
     * 快速会见/远程会见-预约会见开始时间
     */
    private Date applyMeetingStartTime;
    /**
     * 快速会见/远程会见-预约会见结束时间
     */
    private Date applyMeetingEndTime;
    /**
     * 会见所在房间编号
     */
    private String roomId;
    /**
     * 会见批准机关
     */
    private String approvalAuthority;
    /**
     * 许可决定文书号
     */
    private String approvalDocumentNumber;
    /**
     * 上传会见批准材料附件
     */
    private String approvalAttachmentPath;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 办理状态
     */
    private String status;
    /**
     * 分配房间时间
     */
    private Date assignmentRoomTime;
    /**
     * 分配民警身份证号
     */
    private String assignmentPoliceSfzh;
    /**
     * assignment_police
     */
    private String assignmentPolice;
    /**
     * 签到时间
     */
    private Date checkInTime;
    /**
     * 签到用户身份证号
     */
    private String checkInPoliceSfzh;
    /**
     * 签到用户
     */
    private String checkInPolice;
    /**
     * 带出民警身份证号
     */
    private String escortingPoliceSfzh;
    /**
     * 带出民警身份证号
     */
    private String escortingPolice;
    /**
     * 带出监室时间
     */
    private Date escortingTime;
    /**
     * 检查结果
     */
    private String inspectionResult;
    /**
     * 违禁物品登记
     */
    private String prohibitedItems;
    /**
     * 体表检查登记
     */
    private String physicalExam;
    /**
     * 异常情况登记
     */
    private String abnormalSituations;
    /**
     * 违禁物品登记照片存储地址
     */
    private String prohibitedItemsImgUrl;
    /**
     * 体表检查登记照片存储地址
     */
    private String physicalExamImgUrl;
    /**
     * 异常情况登记照片存储地址
     */
    private String abnormalSituationsImgUrl;
    /**
     * 是否查出违禁物品
     */
    private Short isProhibitedItems;
    /**
     * 检查时间
     */
    private Date inspectionTime;
    /**
     * 检查民警身份证号
     */
    private String inspectorSfzh;
    /**
     * 检查民警身份证号
     */
    private String inspector;
    /**
     * 会见开始时间
     */
    private Date meetingStartTime;
    /**
     * 会见结束时间
     */
    private Date meetingEndTime;
    /**
     * 会毕检查人
     */
    private String returnInspectorSfzh;
    /**
     * 会毕检查人
     */
    private String returnInspector;
    /**
     * 会毕检查时间
     */
    private Date returnInspectionTime;
    /**
     * 会毕检查结果
     */
    private String returnInspectionResult;
    /**
     * 带回违禁物品登记
     */
    private String returnProhibitedItems;
    /**
     * 带回体表检查登记
     */
    private String returnPhysicalExam;
    /**
     * 带回异常情况登记
     */
    private String returnAbnormalSituations;
    /**
     * 带回监室时间
     */
    private Date returnTime;
    /**
     * return_police_sfzh
     */
    private String returnPoliceSfzh;
    /**
     * 带回民警姓名
     */
    private String returnPolice;
    /**
     * 带出操作人身份证号
     */
    private String escortingOperatorSfzh;
    /**
     * 带出操作人
     */
    private String escortingOperator;
    /**
     * 带出操作时间
     */
    private Date escortingOperatorTime;
    /**
     * 带回操作人身份证号
     */
    private String returnOperatorSfzh;
    /**
     * 带回操作人
     */
    private String returnOperator;
    /**
     * 带回操作时间
     */
    private Date returnOperatorTime;
    /**
     * 法律援助公函号
     */
    private String legalAidOfficialLetterNumber;
    /**
     * 第一位律师照片存储url
     */
    private String lawyer1ImageUrl;
    /**
     * 第二位律师照片存储url
     */
    private String lawyer2ImageUrl;
    /**
     * 第三位律师照片存储url
     */
    private String lawyer3ImageUrl;
    /**
     * 第四位律师照片存储url
     */
    private String lawyer4ImageUrl;


}
