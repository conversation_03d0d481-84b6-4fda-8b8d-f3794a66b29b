package com.rs.module.acp.dao.db;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.db.InjuryAssessmentDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-收押业务-伤情鉴定 Dao
*
* <AUTHOR>
*/
@Mapper
public interface InjuryAssessmentDao extends IBaseDao<InjuryAssessmentDO> {


    default PageResult<InjuryAssessmentDO> selectPage(InjuryAssessmentPageReqVO reqVO) {
        Page<InjuryAssessmentDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<InjuryAssessmentDO> wrapper = new LambdaQueryWrapperX<InjuryAssessmentDO>()
            .eqIfPresent(InjuryAssessmentDO::getRybh, reqVO.getRybh())
            .eqIfPresent(InjuryAssessmentDO::getSerialNumber, reqVO.getSerialNumber())
            .eqIfPresent(InjuryAssessmentDO::getZp, reqVO.getZp())
            .eqIfPresent(InjuryAssessmentDO::getMs, reqVO.getMs())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(InjuryAssessmentDO::getAddTime);
        }
        Page<InjuryAssessmentDO> injuryAssessmentPage = selectPage(page, wrapper);
        return new PageResult<>(injuryAssessmentPage.getRecords(), injuryAssessmentPage.getTotal());
    }
    default List<InjuryAssessmentDO> selectList(InjuryAssessmentListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<InjuryAssessmentDO>()
            .eqIfPresent(InjuryAssessmentDO::getRybh, reqVO.getRybh())
            .eqIfPresent(InjuryAssessmentDO::getSerialNumber, reqVO.getSerialNumber())
            .eqIfPresent(InjuryAssessmentDO::getZp, reqVO.getZp())
            .eqIfPresent(InjuryAssessmentDO::getMs, reqVO.getMs())
        .orderByDesc(InjuryAssessmentDO::getAddTime));    }


    }
