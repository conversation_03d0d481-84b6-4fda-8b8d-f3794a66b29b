package com.rs.module.acp.controller.app.wb;

import cn.hutool.core.util.ObjectUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.GoodsDeliveryDO;
import com.rs.module.acp.service.wb.WbApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-窗口业务-业务api")
@RestController
@RequestMapping("/app/acp/wbapi")
@Validated
public class WbApiController {

    @Autowired
    private WbApiService wbApiService;


    @PostMapping("/getMeetingNoticePage")
    @ApiOperation(value = "实战平台-窗口业务-获取会见通知（内屏使用）")
    @LogRecordAnnotation(bizModule = "acp:wbapi:getMeetingNoticePage", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-获取会见通知（内屏使用）", success = "实战平台-窗口业务-获取会见通知（内屏使用）成功",
            fail = "实战平台-窗口业务-获取会见通知（内屏使用）失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<PageResult<MeetingNoticeVO>> getMeetingNoticePage(@RequestBody MeetingNoticeQueryVO query) {
        return success(wbApiService.getMeetingNoticePage(query));
    }

    @PostMapping("/GoodsDeliveryPage")
    @ApiOperation(value = "获得实战平台-窗口业务-物品顾送登记分页（内屏使用）")
    @LogRecordAnnotation(bizModule = "acp:wbapi:GoodsDeliveryPage", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-物品顾送登记分页",
            success = "实战平台-窗口业务-物品顾送登记分页成功",
            fail = "实战平台-窗口业务-物品顾送登记分页失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<GoodsDeliveryRespVO>> GoodsDeliveryPage(@Valid @RequestBody GoodsDeliveryPageReqVO pageReqVO) {
        return success(wbApiService.getGoodsDeliveryPage(pageReqVO));
    }

    @PostMapping("/GoodsDeliverySignfor")
    @ApiOperation(value = "实战平台-窗口业务-签收物品")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "status", value = "签收（1：签收，2：拒签）", required = true, dataType = "String"),
            @ApiImplicitParam(name = "signature", value = "签名图片", required = false, dataType = "String"),
            @ApiImplicitParam(name = "rejectionReason", value = "拒签原因", required = false, dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:wbapi:GoodsDeliverySignfor", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-签收物品",
            success = "实战平台-窗口业务-签收物品成功", fail = "实战平台-窗口业务-签收物品失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#)}}")
    public CommonResult<Boolean> GoodsDeliverySignfor(@RequestBody GoodsDeliverySaveReqVO updateReqVO) {
        if(ObjectUtil.isEmpty(updateReqVO.getId())){
            return error("业务ID不可为空");
        }
        if (wbApiService.GoodsDeliverySignfor(updateReqVO)) {
            return success();
        }
        return error("签收物品成功失败");
    }
}
