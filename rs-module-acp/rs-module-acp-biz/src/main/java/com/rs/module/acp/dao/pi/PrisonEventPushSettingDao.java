package com.rs.module.acp.dao.pi;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventPushSettingListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventPushSettingPageReqVO;
import com.rs.module.acp.entity.pi.PrisonEventPushSettingDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-巡视管控-所情事件推送设置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PrisonEventPushSettingDao extends IBaseDao<PrisonEventPushSettingDO> {


    default PageResult<PrisonEventPushSettingDO> selectPage(PrisonEventPushSettingPageReqVO reqVO) {
        Page<PrisonEventPushSettingDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrisonEventPushSettingDO> wrapper = new LambdaQueryWrapperX<PrisonEventPushSettingDO>()
            .eqIfPresent(PrisonEventPushSettingDO::getDisposeBusiness, reqVO.getDisposeBusiness())
            .eqIfPresent(PrisonEventPushSettingDO::getDisposePlans, reqVO.getDisposePlans())
            .eqIfPresent(PrisonEventPushSettingDO::getTypeId, reqVO.getTypeId())
            .eqIfPresent(PrisonEventPushSettingDO::getOrderId, reqVO.getOrderId())
            .eqIfPresent(PrisonEventPushSettingDO::getPushPostId, reqVO.getPushPostId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PrisonEventPushSettingDO::getAddTime);
        }
        Page<PrisonEventPushSettingDO> prisonEventPushSettingPage = selectPage(page, wrapper);
        return new PageResult<>(prisonEventPushSettingPage.getRecords(), prisonEventPushSettingPage.getTotal());
    }
    default List<PrisonEventPushSettingDO> selectList(PrisonEventPushSettingListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrisonEventPushSettingDO>()
            .eqIfPresent(PrisonEventPushSettingDO::getDisposeBusiness, reqVO.getDisposeBusiness())
            .eqIfPresent(PrisonEventPushSettingDO::getDisposePlans, reqVO.getDisposePlans())
            .eqIfPresent(PrisonEventPushSettingDO::getTypeId, reqVO.getTypeId())
            .eqIfPresent(PrisonEventPushSettingDO::getOrderId, reqVO.getOrderId())
            .eqIfPresent(PrisonEventPushSettingDO::getPushPostId, reqVO.getPushPostId())
        .orderByDesc(PrisonEventPushSettingDO::getAddTime));    }


    }
