package com.rs.module.acp.cons;

/**
 * 入所类型枚举
 */
public enum InTypeEnums {

    NORMAL("01", "正常入所"),
    EMERGENCY("02", "快速入所");

    private final String code;
    private final String value;

    InTypeEnums(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 状态码
     * @return 对应的枚举值
     */
    public static InTypeEnums getByCode(String code) {
        for (InTypeEnums type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据value获取枚举值
     *
     * @param value 状态值
     * @return 对应的枚举值
     */
    public static InTypeEnums getByValue(String value) {
        for (InTypeEnums type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
}