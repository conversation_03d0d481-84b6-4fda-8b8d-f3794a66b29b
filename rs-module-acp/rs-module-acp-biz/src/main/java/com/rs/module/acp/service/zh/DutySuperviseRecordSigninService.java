package com.rs.module.acp.service.zh;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.zh.vo.*;
import com.rs.module.acp.entity.zh.DutySuperviseRecordSigninDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 综合管理-值班管理-值班督导记录签至 Service 接口
 *
 * <AUTHOR>
 */
public interface DutySuperviseRecordSigninService extends IBaseService<DutySuperviseRecordSigninDO>{

    /**
     * 创建综合管理-值班管理-值班督导记录签至
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createDutySuperviseRecordSignin(@Valid DutySuperviseRecordSigninSaveReqVO createReqVO);

    /**
     * 更新综合管理-值班管理-值班督导记录签至
     *
     * @param updateReqVO 更新信息
     */
    void updateDutySuperviseRecordSignin(@Valid DutySuperviseRecordSigninSaveReqVO updateReqVO);

    /**
     * 删除综合管理-值班管理-值班督导记录签至
     *
     * @param id 编号
     */
    void deleteDutySuperviseRecordSignin(String id);

    /**
     * 获得综合管理-值班管理-值班督导记录签至
     *
     * @param id 编号
     * @return 综合管理-值班管理-值班督导记录签至
     */
    DutySuperviseRecordSigninDO getDutySuperviseRecordSignin(String id);

    /**
    * 获得综合管理-值班管理-值班督导记录签至分页
    *
    * @param pageReqVO 分页查询
    * @return 综合管理-值班管理-值班督导记录签至分页
    */
    PageResult<DutySuperviseRecordSigninDO> getDutySuperviseRecordSigninPage(DutySuperviseRecordSigninPageReqVO pageReqVO);

    /**
    * 获得综合管理-值班管理-值班督导记录签至列表
    *
    * @param listReqVO 查询条件
    * @return 综合管理-值班管理-值班督导记录签至列表
    */
    List<DutySuperviseRecordSigninDO> getDutySuperviseRecordSigninList(DutySuperviseRecordSigninListReqVO listReqVO);


}
