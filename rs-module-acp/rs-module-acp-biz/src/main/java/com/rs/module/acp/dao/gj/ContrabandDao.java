package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.contraband.ContrabandListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.contraband.ContrabandPageReqVO;
import com.rs.module.acp.entity.gj.ContrabandDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-违禁品登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ContrabandDao extends IBaseDao<ContrabandDO> {


    default PageResult<ContrabandDO> selectPage(ContrabandPageReqVO reqVO) {
        Page<ContrabandDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ContrabandDO> wrapper = new LambdaQueryWrapperX<ContrabandDO>()
            .eqIfPresent(ContrabandDO::getDataSources, reqVO.getDataSources())
            .eqIfPresent(ContrabandDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(ContrabandDO::getJgryxm, reqVO.getJgryxm())
            .betweenIfPresent(ContrabandDO::getCheckTime, reqVO.getCheckTime())
            .eqIfPresent(ContrabandDO::getContrabandCategory, reqVO.getContrabandCategory())
            .eqIfPresent(ContrabandDO::getContrabandImgPath, reqVO.getContrabandImgPath())
            .eqIfPresent(ContrabandDO::getHandlingSituatio, reqVO.getHandlingSituatio())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(ContrabandDO::getAddTime);
        }
        Page<ContrabandDO> contrabandPage = selectPage(page, wrapper);
        return new PageResult<>(contrabandPage.getRecords(), contrabandPage.getTotal());
    }
    default List<ContrabandDO> selectList(ContrabandListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ContrabandDO>()
            .eqIfPresent(ContrabandDO::getDataSources, reqVO.getDataSources())
            .eqIfPresent(ContrabandDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(ContrabandDO::getJgryxm, reqVO.getJgryxm())
            .betweenIfPresent(ContrabandDO::getCheckTime, reqVO.getCheckTime())
            .eqIfPresent(ContrabandDO::getContrabandCategory, reqVO.getContrabandCategory())
            .eqIfPresent(ContrabandDO::getContrabandImgPath, reqVO.getContrabandImgPath())
            .eqIfPresent(ContrabandDO::getHandlingSituatio, reqVO.getHandlingSituatio())
        .orderByDesc(ContrabandDO::getAddTime));    }


    }
