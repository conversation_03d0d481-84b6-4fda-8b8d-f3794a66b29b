package com.rs.module.acp.config;
import com.rs.module.acp.component.QingyanLocalsenseSocketComponent;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;
/**
 * 延迟初始化 QingyanLocalsenseSocketComponent
 * 确保在 Spring Boot 完全启动后才初始化 WebSocket 连接
 */
@Component
@RequiredArgsConstructor
@Log4j2
public class QingyanSocketInitRunner implements ApplicationRunner {

    private final QingyanLocalsenseSocketComponent socketComponent;

    @Override
    public void run(ApplicationArguments args) {
        log.info("Spring Boot 应用启动完成，开始初始化 QingyanLocalsenseSocketComponent");
        socketComponent.ensureConnection();
    }
}