package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.performancekss.PerformanceKssListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.performancekss.PerformanceKssPageReqVO;
import com.rs.module.acp.entity.gj.PerformanceKssDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-人员表现鉴定表-看守所 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PerformanceKssDao extends IBaseDao<PerformanceKssDO> {


    default PageResult<PerformanceKssDO> selectPage(PerformanceKssPageReqVO reqVO) {
        Page<PerformanceKssDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PerformanceKssDO> wrapper = new LambdaQueryWrapperX<PerformanceKssDO>()
            .eqIfPresent(PerformanceKssDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PerformanceKssDO::getJsdw, reqVO.getJsdw())
            .eqIfPresent(PerformanceKssDO::getYsyy, reqVO.getYsyy())
            .eqIfPresent(PerformanceKssDO::getQtqk, reqVO.getQtqk())
            .eqIfPresent(PerformanceKssDO::getHealthStzk, reqVO.getHealthStzk())
            .eqIfPresent(PerformanceKssDO::getHealthWsqk, reqVO.getHealthWsqk())
            .eqIfPresent(PerformanceKssDO::getHealthZdjb, reqVO.getHealthZdjb())
            .eqIfPresent(PerformanceKssDO::getHealthJszk, reqVO.getHealthJszk())
            .eqIfPresent(PerformanceKssDO::getHealthZdjbjcszyyy, reqVO.getHealthZdjbjcszyyy())
            .eqIfPresent(PerformanceKssDO::getPerformanceSgsjzd, reqVO.getPerformanceSgsjzd())
            .eqIfPresent(PerformanceKssDO::getPerformanceYrshgl, reqVO.getPerformanceYrshgl())
            .eqIfPresent(PerformanceKssDO::getPerformanceZszcxwhqx, reqVO.getPerformanceZszcxwhqx())
            .eqIfPresent(PerformanceKssDO::getPerformanceOdtrxw, reqVO.getPerformanceOdtrxw())
            .eqIfPresent(PerformanceKssDO::getPerformanceZdaqfxqk1, reqVO.getPerformanceZdaqfxqk1())
            .eqIfPresent(PerformanceKssDO::getPerformanceZdaqfxqk2, reqVO.getPerformanceZdaqfxqk2())
            .eqIfPresent(PerformanceKssDO::getPerformanceZdaqfxqk3, reqVO.getPerformanceZdaqfxqk3())
            .eqIfPresent(PerformanceKssDO::getPerformanceZdaqfxqk4, reqVO.getPerformanceZdaqfxqk4())
            .eqIfPresent(PerformanceKssDO::getFamilyXmjdh, reqVO.getFamilyXmjdh())
            .eqIfPresent(PerformanceKssDO::getFamilyJyqjlxjsqk, reqVO.getFamilyJyqjlxjsqk())
            .eqIfPresent(PerformanceKssDO::getStatus, reqVO.getStatus())
            .eqIfPresent(PerformanceKssDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(PerformanceKssDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(PerformanceKssDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(PerformanceKssDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(PerformanceKssDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(PerformanceKssDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(PerformanceKssDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PerformanceKssDO::getAddTime);
        }
        Page<PerformanceKssDO> performanceKssPage = selectPage(page, wrapper);
        return new PageResult<>(performanceKssPage.getRecords(), performanceKssPage.getTotal());
    }
    default List<PerformanceKssDO> selectList(PerformanceKssListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PerformanceKssDO>()
            .eqIfPresent(PerformanceKssDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PerformanceKssDO::getJsdw, reqVO.getJsdw())
            .eqIfPresent(PerformanceKssDO::getYsyy, reqVO.getYsyy())
            .eqIfPresent(PerformanceKssDO::getQtqk, reqVO.getQtqk())
            .eqIfPresent(PerformanceKssDO::getHealthStzk, reqVO.getHealthStzk())
            .eqIfPresent(PerformanceKssDO::getHealthWsqk, reqVO.getHealthWsqk())
            .eqIfPresent(PerformanceKssDO::getHealthZdjb, reqVO.getHealthZdjb())
            .eqIfPresent(PerformanceKssDO::getHealthJszk, reqVO.getHealthJszk())
            .eqIfPresent(PerformanceKssDO::getHealthZdjbjcszyyy, reqVO.getHealthZdjbjcszyyy())
            .eqIfPresent(PerformanceKssDO::getPerformanceSgsjzd, reqVO.getPerformanceSgsjzd())
            .eqIfPresent(PerformanceKssDO::getPerformanceYrshgl, reqVO.getPerformanceYrshgl())
            .eqIfPresent(PerformanceKssDO::getPerformanceZszcxwhqx, reqVO.getPerformanceZszcxwhqx())
            .eqIfPresent(PerformanceKssDO::getPerformanceOdtrxw, reqVO.getPerformanceOdtrxw())
            .eqIfPresent(PerformanceKssDO::getPerformanceZdaqfxqk1, reqVO.getPerformanceZdaqfxqk1())
            .eqIfPresent(PerformanceKssDO::getPerformanceZdaqfxqk2, reqVO.getPerformanceZdaqfxqk2())
            .eqIfPresent(PerformanceKssDO::getPerformanceZdaqfxqk3, reqVO.getPerformanceZdaqfxqk3())
            .eqIfPresent(PerformanceKssDO::getPerformanceZdaqfxqk4, reqVO.getPerformanceZdaqfxqk4())
            .eqIfPresent(PerformanceKssDO::getFamilyXmjdh, reqVO.getFamilyXmjdh())
            .eqIfPresent(PerformanceKssDO::getFamilyJyqjlxjsqk, reqVO.getFamilyJyqjlxjsqk())
            .eqIfPresent(PerformanceKssDO::getStatus, reqVO.getStatus())
            .eqIfPresent(PerformanceKssDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(PerformanceKssDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(PerformanceKssDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(PerformanceKssDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(PerformanceKssDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(PerformanceKssDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(PerformanceKssDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(PerformanceKssDO::getAddTime));    }


    }
