package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.util.DicUtils;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.dao.wb.WbApiDao;
import com.rs.module.acp.entity.wb.GoodsDeliveryDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;

@Service
@Validated
public class WbApiServiceImpl implements WbApiService {

    @Resource
    private WbApiDao wbApiDao;

    @Autowired
    private GoodsDeliveryService goodsDeliveryService;

    @Override
    public PageResult<MeetingNoticeVO> getMeetingNoticePage(MeetingNoticeQueryVO query) {
        Page<MeetingNoticeVO> page = new Page<>(query.getPageNo(),query.getPageSize());
        query.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());

        //构建时间查询
        if(ObjectUtil.isNotEmpty(query.getQueryTimeType())){

            Map<String,Date> timeMap = getQueryTime(query.getQueryTimeType());
            query.setQueryStartTime(timeMap.get("startTime"));
            query.setQueryEndTime(timeMap.get("endTime"));
        }

        IPage<MeetingNoticeVO> iPage = wbApiDao.getMeetingNoticePage(page,query);


        if(CollectionUtil.isNotEmpty(iPage.getRecords())){
            for(MeetingNoticeVO noticeVO:iPage.getRecords()){
                //翻译会见事由
                if("0".equals(noticeVO.getBusinessType()) || "1".equals(noticeVO.getBusinessType()) ){
                    noticeVO.setReasonName(translate("ZD_WB_TXSY",noticeVO.getReason()));
                }else if("2".equals(noticeVO.getBusinessType())){
                    noticeVO.setReasonName(translate("ZD_WB_TJSY",noticeVO.getReason()));
                }

            }
        }

        return new PageResult<>(iPage.getRecords(),iPage.getTotal());
    }

    public String translate(String dicName,String code){
        if(ObjectUtil.isNotEmpty(code)){
            return DicUtils.translate(dicName,code);
        }
        return "/";
    }

    public Map<String,Date> getQueryTime(String queryTimeType){
        Map<String,Date> timeMap = new HashMap<>();
        Date startTime = null;
        Date endTime = null;
        String queryStartTime = null;
        String queryEndTime = null;

        if("0".equals(queryTimeType)){
            //全部
        }else if("1".equals(queryTimeType)){
            //今天
            queryStartTime = DateUtil.format(new Date(),"yyyy-MM-dd")+" 00:00:00";
            queryEndTime = DateUtil.format(new Date(),"yyyy-MM-dd")+" 23:59:59";
        }else if("2".equals(queryTimeType)){
            //昨天
            queryStartTime = DateUtil.format(DateUtil.offsetDay(new Date(),-1),"yyyy-MM-dd")+" 00:00:00";
            queryEndTime = DateUtil.format(DateUtil.offsetDay(new Date(),-1),"yyyy-MM-dd")+" 23:59:59";
        }else if("3".equals(queryTimeType)){
            //近一周
            queryStartTime = DateUtil.format(DateUtil.offsetWeek(new Date(),-1),"yyyy-MM-dd")+" 00:00:00";
            queryEndTime = DateUtil.format(new Date(),"yyyy-MM-dd")+" 23:59:59";
        }else if("4".equals(queryTimeType)){
            //本周
            queryStartTime = DateUtil.format(DateUtil.beginOfWeek(new Date()),"yyyy-MM-dd")+" 00:00:00";
            queryEndTime = DateUtil.format(new Date(),"yyyy-MM-dd")+" 23:59:59";
        }else if("5".equals(queryTimeType)){
            //近一个月
            queryStartTime = DateUtil.format(DateUtil.offsetMonth(new Date(),-1),"yyyy-MM-dd")+" 00:00:00";
            queryEndTime = DateUtil.format(new Date(),"yyyy-MM-dd")+" 23:59:59";
        }else if("6".equals(queryTimeType)){
            //本月
            queryStartTime = DateUtil.format(DateUtil.beginOfMonth(new Date()),"yyyy-MM-dd")+" 00:00:00";
            queryEndTime = DateUtil.format(new Date(),"yyyy-MM-dd")+" 23:59:59";
        }else if("7".equals(queryTimeType)){
            //近一年
            queryStartTime = DateUtil.format(DateUtil.offsetYear(new Date(),-1),"yyyy-MM-dd")+" 00:00:00";
            queryEndTime = DateUtil.format(new Date(),"yyyy-MM-dd")+" 23:59:59";
        }else if("8".equals(queryTimeType)){
            //本年
            queryStartTime = DateUtil.format(DateUtil.beginOfYear(new Date()),"yyyy-MM-dd")+" 00:00:00";
            queryEndTime = DateUtil.format(new Date(),"yyyy-MM-dd")+" 23:59:59";
        }

        if(ObjectUtil.isNotEmpty(queryStartTime)){
            startTime = DateUtil.parse(queryStartTime,"yyyy-MM-dd HH:mm:ss");
        }
        if(ObjectUtil.isNotEmpty(queryEndTime)){
            endTime = DateUtil.parse(queryEndTime,"yyyy-MM-dd HH:mm:ss");
        }
        timeMap.put("startTime",startTime);
        timeMap.put("endTime",endTime);
        return timeMap;
    }

    @Override
    public PageResult<GoodsDeliveryRespVO> getGoodsDeliveryPage(GoodsDeliveryPageReqVO pageReqVO) {
        PageResult<GoodsDeliveryDO> pageResult = goodsDeliveryService.getGoodsDeliveryPage(pageReqVO);
        if(CollectionUtil.isEmpty(pageResult.getList())){
            return new PageResult<>(new ArrayList<>(),pageResult.getTotal());
        }
        return BeanUtils.toBean(pageResult, GoodsDeliveryRespVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean GoodsDeliverySignfor(GoodsDeliverySaveReqVO updateReqVO) {
        return goodsDeliveryService.signfor(updateReqVO);
    }
}
