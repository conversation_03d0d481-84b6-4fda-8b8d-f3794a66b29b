package com.rs.module.acp.service.pi;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.controller.app.pi.vo.ProtRestraintAppPageReqVO;
import com.rs.module.acp.controller.app.pi.vo.ProtRestraintAppRespVO;
import com.rs.module.acp.controller.app.pi.vo.ProtRestraintAppSaveReqVO;
import com.rs.module.acp.entity.pi.ProtRestraintDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.base.vo.ApproveReqVO;

/**
 * 实战平台-巡视管控-保护性约束 Service 接口
 *
 * <AUTHOR>
 */
public interface ProtRestraintService extends IBaseService<ProtRestraintDO>{

    /**
     * 创建实战平台-巡视管控-保护性约束
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createProtRestraint(@Valid ProtRestraintSaveReqVO createReqVO)  throws Exception;

    /**
     * 更新实战平台-巡视管控-保护性约束
     *
     * @param updateReqVO 更新信息
     */
    void updateProtRestraint(@Valid ProtRestraintSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-保护性约束
     *
     * @param id 编号
     */
    void deleteProtRestraint(String id);

    /**
     * 获得实战平台-巡视管控-保护性约束
     *
     * @param id 编号
     * @return 实战平台-巡视管控-保护性约束
     */
    ProtRestraintDO getProtRestraint(String id);

    /**
    * 获得实战平台-巡视管控-保护性约束分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-巡视管控-保护性约束分页
    */
    PageResult<ProtRestraintDO> getProtRestraintPage(ProtRestraintPageReqVO pageReqVO);

    Boolean approve(ApproveReqVO approveReqVO) throws Exception;

    /**
    * 获得实战平台-巡视管控-保护性约束列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-巡视管控-保护性约束列表
    */
    List<ProtRestraintDO> getProtRestraintList(ProtRestraintListReqVO listReqVO);


    String appCreateProtRestraint(ProtRestraintAppSaveReqVO createReqVO) throws Exception;

    boolean isExist(String jgrybm);

    PageResult<ProtRestraintAppRespVO> getProtRestraintAppPage(ProtRestraintAppPageReqVO pageReqVO);
}
