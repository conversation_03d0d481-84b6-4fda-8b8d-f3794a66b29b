package com.rs.module.acp.dao.db;

import com.rs.module.acp.entity.db.SugStopDetentionDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.Optional;

/**
* 实战平台-收押业务-建议停拘登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface SugStopDetentionDao extends IBaseDao<SugStopDetentionDO> {

    /**
    * 获取最大流水号
    *
    * @return
    */
    @Select("SELECT MAX(wsh::integer) FROM acp_db_sug_stop_detention where update_time between #{startTime} and #{endTime}")
    Integer getMaxWsh(@Param("startTime") LocalDateTime startTime,
                      @Param("endTime") LocalDateTime endTime);
    default Integer getNowYeaMaxWsh() {
        LocalDateTime startTime = LocalDateTime.of(
                LocalDateTime.now().with(TemporalAdjusters.firstDayOfYear()).toLocalDate(), LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(
                LocalDateTime.now().with(TemporalAdjusters.lastDayOfYear()).toLocalDate(), LocalTime.MAX);
        return Optional.ofNullable(this.getMaxWsh(startTime, endTime)).orElse(0);
    }
}
