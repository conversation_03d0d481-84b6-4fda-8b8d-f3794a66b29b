package com.rs.module.acp.controller.admin.db.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-羁押业务-转所人员列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TransferPrisonerListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("转所登记ID")
    private String transferRecordId;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

}
