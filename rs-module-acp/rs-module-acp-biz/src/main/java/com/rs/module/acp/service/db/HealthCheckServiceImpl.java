package com.rs.module.acp.service.db;

import com.rs.module.acp.cons.DetainRegStatusEnum;
import com.rs.module.acp.cons.InProcessStageEnum;
import com.rs.module.acp.cons.LeaderApprovalStatusEnum;
import com.rs.module.acp.dao.db.DetainRegKssDao;
import com.rs.module.acp.entity.db.DetainRegKssDO;
import com.rs.module.acp.service.db.components.RegistrationInfoService;
import com.rs.module.acp.service.db.components.RegistrationInfoServiceContext;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.HealthCheckDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.db.HealthCheckDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-收押业务-入所健康检查登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HealthCheckServiceImpl extends BaseServiceImpl<HealthCheckDao, HealthCheckDO> implements HealthCheckService {

    @Resource
    private HealthCheckDao healthCheckDao;

    @Resource
    private RegistrationInfoServiceContext registrationInfoServiceContext;

    @Resource
    private InjuryAssessmentService injuryAssessmentService;

    @Override
    public String createHealthCheck(HealthCheckSaveReqVO createReqVO) {
        HealthCheckDO tmp = getHealthCheckByRybh(createReqVO.getRybh());
        // 插入
        HealthCheckDO healthCheck = BeanUtils.toBean(createReqVO, HealthCheckDO.class);
        healthCheck.setJgrybm(createReqVO.getRybh());
        if(tmp==null){
            healthCheckDao.insert(healthCheck);
        }else{
            healthCheck.setId(tmp.getId());
            healthCheck.updateById();
        }

        //先删除伤情鉴定数据
        injuryAssessmentService.deleteByRybh(healthCheck.getRybh());
        //伤情鉴定插入
        List<InjuryAssessmentSaveReqVO> injuryAssessmentList = createReqVO.getInjuryAssessmentList();
        if(injuryAssessmentList!=null&& !injuryAssessmentList.isEmpty()){
            for(InjuryAssessmentSaveReqVO injuryAssessmentSaveReqVO:injuryAssessmentList){
                injuryAssessmentService.createInjuryAssessment(injuryAssessmentSaveReqVO);
            }
        }
        //如果提交的状态为status=03，则判断物品登记以及生物特征采集是否完成，完成则更新收押登记表的审批状态为待审批
        if(DetainRegStatusEnum.SUBMITTED.getCode().equals(healthCheck.getStatus())) {
            if(createReqVO.getRslx()!=null&&createReqVO.getRslx().equals("02")){
                RegistrationInfoService registrationInfoService = registrationInfoServiceContext.getService(createReqVO.getBusinessType());
                registrationInfoService.updateStateInfo(healthCheck.getRybh(), null, InProcessStageEnum.PENDING_BIO_INFO_COLLECTION.getCode());

            }else {
                //更新收押/收拘登记表的审批状态为待审批,更新当前步骤为待物品管理
                RegistrationInfoService registrationInfoService = registrationInfoServiceContext.getService(createReqVO.getBusinessType());
                registrationInfoService.updateStateInfo(healthCheck.getRybh(), LeaderApprovalStatusEnum.PENDING.getCode(), InProcessStageEnum.PENDING_BIO_INFO_COLLECTION.getCode());
            }
        }
        // 返回
        return healthCheck.getId();
    }

    @Override
    public void updateHealthCheck(HealthCheckSaveReqVO updateReqVO) {
        // 校验存在
        validateHealthCheckExists(updateReqVO.getId());
        // 更新
        HealthCheckDO updateObj = BeanUtils.toBean(updateReqVO, HealthCheckDO.class);
        healthCheckDao.updateById(updateObj);

        //伤情鉴定插入
        //先删除
        injuryAssessmentService.deleteByRybh(updateReqVO.getRybh());
        //再插入
        List<InjuryAssessmentSaveReqVO> injuryAssessmentList = updateReqVO.getInjuryAssessmentList();
        if(injuryAssessmentList!=null&& !injuryAssessmentList.isEmpty()){
            for(InjuryAssessmentSaveReqVO injuryAssessmentSaveReqVO:injuryAssessmentList){
                injuryAssessmentService.createInjuryAssessment(injuryAssessmentSaveReqVO);
            }
        }
    }

    @Override
    public void deleteHealthCheck(String id) {
        // 校验存在
        validateHealthCheckExists(id);
        HealthCheckDO healthCheckDO = healthCheckDao.selectById(id);
        injuryAssessmentService.deleteByRybh(healthCheckDO.getRybh());
        // 删除
        healthCheckDao.deleteById(id);
    }

    private void validateHealthCheckExists(String id) {
        if (healthCheckDao.selectById(id) == null) {
            throw new ServerException("实战平台-收押业务-入所健康检查登记数据不存在");
        }
    }

    @Override
    public HealthCheckDO getHealthCheck(String id) {
        return healthCheckDao.selectById(id);
    }

    @Override
    public PageResult<HealthCheckDO> getHealthCheckPage(HealthCheckPageReqVO pageReqVO) {
        return healthCheckDao.selectPage(pageReqVO);
    }

    @Override
    public List<HealthCheckDO> getHealthCheckList(HealthCheckListReqVO listReqVO) {
        return healthCheckDao.selectList(listReqVO);
    }

    public HealthCheckDO getHealthCheckByRybh(String rybh){
        HealthCheckDO healthCheckDO = healthCheckDao.selectOne(HealthCheckDO::getRybh, rybh);

        return healthCheckDO;
    }


}
