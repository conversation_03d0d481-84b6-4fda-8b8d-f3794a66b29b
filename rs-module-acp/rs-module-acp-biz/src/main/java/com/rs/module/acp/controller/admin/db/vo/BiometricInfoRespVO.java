package com.rs.module.acp.controller.admin.db.vo;
import com.alibaba.fastjson.JSONObject;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-收押业务-生物特征信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BiometricInfoRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("人员编号")
    private String rybh;
    @ApiModelProperty("采集项目类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SWXXCJXM")
    private String cjxmlx;
    @ApiModelProperty("生物特征,存储采集系统返回JSON结构")
    private List<JSONObject> biometric;
    @ApiModelProperty("swtzfj")
    private String swtzfj;
    @ApiModelProperty("备注")
    private String bz;
}
