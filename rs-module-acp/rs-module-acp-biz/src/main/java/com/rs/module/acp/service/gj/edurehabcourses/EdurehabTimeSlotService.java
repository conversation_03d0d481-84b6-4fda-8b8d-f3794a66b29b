package com.rs.module.acp.service.gj.edurehabcourses;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabTimeSlotListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabTimeSlotPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabTimeSlotSaveReqVO;
import com.rs.module.acp.entity.gj.EdurehabTimeSlotDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-教育康复课程时段 Service 接口
 *
 * <AUTHOR>
 */
public interface EdurehabTimeSlotService extends IBaseService<EdurehabTimeSlotDO>{

    /**
     * 创建实战平台-管教业务-教育康复课程时段
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createEdurehabTimeSlot(@Valid EdurehabTimeSlotSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-教育康复课程时段
     *
     * @param updateReqVO 更新信息
     */
    void updateEdurehabTimeSlot(@Valid EdurehabTimeSlotSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-教育康复课程时段
     *
     * @param id 编号
     */
    void deleteEdurehabTimeSlot(String id);

    /**
     * 获得实战平台-管教业务-教育康复课程时段
     *
     * @param id 编号
     * @return 实战平台-管教业务-教育康复课程时段
     */
    EdurehabTimeSlotDO getEdurehabTimeSlot(String id);

    /**
    * 获得实战平台-管教业务-教育康复课程时段分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-教育康复课程时段分页
    */
    PageResult<EdurehabTimeSlotDO> getEdurehabTimeSlotPage(EdurehabTimeSlotPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-教育康复课程时段列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-教育康复课程时段列表
    */
    List<EdurehabTimeSlotDO> getEdurehabTimeSlotList(EdurehabTimeSlotListReqVO listReqVO);


    void batchCreate(List<EdurehabTimeSlotSaveReqVO> list);
}
