package com.rs.module.acp.service.sys;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.sys.vo.VbConfigTimedListReqVO;
import com.rs.module.acp.controller.admin.sys.vo.VbConfigTimedPageReqVO;
import com.rs.module.acp.controller.admin.sys.vo.VbConfigTimedSaveReqVO;
import com.rs.module.acp.dao.sys.VbConfigTimedDao;
import com.rs.module.acp.entity.sys.VbConfigTimedDO;


/**
 * 实战平台-语音播报-定时配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class VbConfigTimedServiceImpl extends BaseServiceImpl<VbConfigTimedDao, VbConfigTimedDO> implements VbConfigTimedService {

    @Resource
    private VbConfigTimedDao vbConfigTimedDao;

    @Override
    public String createVbConfigTimed(VbConfigTimedSaveReqVO createReqVO) {
        // 插入
        VbConfigTimedDO vbConfigTimed = BeanUtils.toBean(createReqVO, VbConfigTimedDO.class);
        vbConfigTimedDao.insert(vbConfigTimed);
        // 返回
        return vbConfigTimed.getId();
    }

    @Override
    public void updateVbConfigTimed(VbConfigTimedSaveReqVO updateReqVO) {
        // 校验存在
        validateVbConfigTimedExists(updateReqVO.getId());
        // 更新
        VbConfigTimedDO updateObj = BeanUtils.toBean(updateReqVO, VbConfigTimedDO.class);
        vbConfigTimedDao.updateById(updateObj);
    }

    @Override
    public void deleteVbConfigTimed(String id) {
        // 校验存在
        validateVbConfigTimedExists(id);
        // 删除
        vbConfigTimedDao.deleteById(id);
    }

    private void validateVbConfigTimedExists(String id) {
        if (vbConfigTimedDao.selectById(id) == null) {
            throw new ServerException("实战平台-语音播报-定时配置数据不存在");
        }
    }

    @Override
    public VbConfigTimedDO getVbConfigTimed(String id) {
        return vbConfigTimedDao.selectById(id);
    }

    @Override
    public PageResult<VbConfigTimedDO> getVbConfigTimedPage(VbConfigTimedPageReqVO pageReqVO) {
        return vbConfigTimedDao.selectPage(pageReqVO);
    }

    @Override
    public List<VbConfigTimedDO> getVbConfigTimedList(VbConfigTimedListReqVO listReqVO) {
        return vbConfigTimedDao.selectList(listReqVO);
    }

    @Override
    public List<VbConfigTimedDO> getEnabledTimedConfig(String orgCode) {
    	return vbConfigTimedDao.selectList(new LambdaQueryWrapperX<VbConfigTimedDO>()
    			.eqIfPresent(VbConfigTimedDO::getOrgCode, orgCode)
    			.eq(VbConfigTimedDO::getIsEnabled, 1));
    }
}
