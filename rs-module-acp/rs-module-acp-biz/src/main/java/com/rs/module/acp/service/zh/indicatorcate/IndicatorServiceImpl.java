package com.rs.module.acp.service.zh.indicatorcate;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.*;
import com.rs.module.acp.entity.zh.IndicatorConfigDO;
import com.rs.module.acp.entity.zh.IndicatorSubDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.sql.Wrapper;
import java.util.*;
import java.util.stream.Collectors;

import com.rs.module.acp.entity.zh.IndicatorDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.zh.IndicatorDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 综合管理-绩效考核指标 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class IndicatorServiceImpl extends BaseServiceImpl<IndicatorDao, IndicatorDO> implements IndicatorService {

    @Resource
    private IndicatorDao indicatorDao;

    @Resource
    private IndicatorSubService indicatorSubService;

    @Resource
    private IndicatorCateService indicatorCateService;

    @Resource
    private IndicatorConfigService indicatorConfigService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createIndicator(IndicatorSaveReqVO createReqVO) {

        IndicatorConfigDO indicatorConfigDO = indicatorConfigService.getIndicatorConfigByOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        if (Objects.isNull(indicatorConfigDO)) {
            throw new RuntimeException("本机构未设置绩效考核截至日期配置，请优先配置考核截至日期！");
        }
        checkRepeat(createReqVO);
        // 插入
        IndicatorDO indicator = BeanUtils.toBean(createReqVO, IndicatorDO.class);
        indicatorDao.insert(indicator);

        List<IndicatorSubSaveReqVO> subList = createReqVO.getSubList();
        if (CollectionUtil.isNotEmpty(subList)) {
            subList.forEach(a -> {
                a.setMainIndicatorId(indicator.getId());
                a.setId(null);
            });
            indicatorSubService.saveBatch(BeanUtils.toBean(subList, IndicatorSubDO.class));
        }

        // 返回
        return indicator.getId();
    }

    private void checkRepeat(IndicatorSaveReqVO createReqVO) {
        IndicatorCateRespVO indicatorCate = indicatorCateService.getIndicatorCate(createReqVO.getIndicatorCateId());
        if (Objects.isNull(indicatorCate)) {
            throw new RuntimeException("非法 indicatorCateId ");
        }
        LambdaQueryWrapper<IndicatorDO> wrapperCount = Wrappers.lambdaQuery(IndicatorDO.class)
                .eq(IndicatorDO::getIndicatorCateId, createReqVO.getIndicatorCateId())
                .eq(IndicatorDO::getIndicatorName, createReqVO.getIndicatorName());
        if(StringUtils.isNotEmpty(createReqVO.getId())){
            wrapperCount.ne(IndicatorDO::getId, createReqVO.getId());
        }
        Integer count = indicatorDao.selectCount(wrapperCount);
        if (Objects.nonNull(count) && count > 0) {
            throw new RuntimeException("主指标名称已存在");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateIndicator(IndicatorSaveReqVO updateReqVO) {
        // 校验存在
        validateIndicatorExists(updateReqVO.getId());
        checkRepeat(updateReqVO);
        // 更新
        IndicatorDO updateObj = BeanUtils.toBean(updateReqVO, IndicatorDO.class);
        indicatorDao.updateById(updateObj);
        indicatorSubService.remove(Wrappers.lambdaQuery(IndicatorSubDO.class).eq(IndicatorSubDO::getMainIndicatorId, updateReqVO.getId()));
        List<IndicatorSubSaveReqVO> subList = updateReqVO.getSubList();
        if (CollectionUtil.isNotEmpty(subList)) {
            subList.forEach(a -> {
                a.setMainIndicatorId(updateReqVO.getId());
                a.setId(null);
            });
            indicatorSubService.saveBatch(BeanUtils.toBean(subList, IndicatorSubDO.class));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteIndicator(String id) {
        // 校验存在
        validateIndicatorExists(id);
        // 删除
        indicatorDao.deleteById(id);
        indicatorSubService.remove(Wrappers.lambdaQuery(IndicatorSubDO.class).eq(IndicatorSubDO::getMainIndicatorId, id));
    }

    private void validateIndicatorExists(String id) {
        if (indicatorDao.selectById(id) == null) {
            throw new ServerException("综合管理-绩效考核指标数据不存在");
        }
    }

    @Override
    public IndicatorRespVO getIndicator(String id) {
        IndicatorDO indicatorDO = indicatorDao.selectById(id);
        IndicatorRespVO indicatorRespVO = BeanUtils.toBean(indicatorDO, IndicatorRespVO.class);
        if (Objects.nonNull(indicatorRespVO)) {
            List<IndicatorSubDO> list = indicatorSubService.list(Wrappers.lambdaQuery(IndicatorSubDO.class).eq(IndicatorSubDO::getMainIndicatorId, id));
            indicatorRespVO.setSubList(BeanUtils.toBean(list, IndicatorSubRespVO.class));
        }
        return indicatorRespVO;
    }

    @Override
    public PageResult<IndicatorDO> getIndicatorPage(IndicatorPageReqVO pageReqVO) {
        return indicatorDao.selectPage(pageReqVO);
    }

    @Override
    public List<IndicatorRespVO> getIndicatorList(IndicatorListReqVO listReqVO) {
        List<IndicatorDO> indicatorDOS = indicatorDao.selectList(listReqVO);
        List<IndicatorRespVO> indicatorRespVOS = BeanUtils.toBean(indicatorDOS, IndicatorRespVO.class);
        if (CollectionUtil.isNotEmpty(indicatorRespVOS)) {
            List<String> mainIdList = indicatorRespVOS.stream().map(IndicatorRespVO::getId).collect(Collectors.toList());
            List<IndicatorSubDO> list = indicatorSubService.list(Wrappers.lambdaQuery(IndicatorSubDO.class)
                    .in(IndicatorSubDO::getMainIndicatorId, mainIdList)
                    .orderByAsc(IndicatorSubDO::getSortOrder).orderByAsc(IndicatorSubDO::getAddTime)
            );
            List<IndicatorSubRespVO> indicatorSubRespVOS = BeanUtils.toBean(list, IndicatorSubRespVO.class);
            if (CollectionUtil.isNotEmpty(indicatorSubRespVOS)) {
                Map<String, List<IndicatorSubRespVO>> map = new HashMap<>();
                for (IndicatorSubRespVO indicatorSubRespVO : indicatorSubRespVOS) {
                    List<IndicatorSubRespVO> tempList = map.get(indicatorSubRespVO.getMainIndicatorId());
                    if (CollectionUtil.isEmpty(tempList)) {
                        tempList = new ArrayList<>();
                        map.put(indicatorSubRespVO.getMainIndicatorId(), tempList);
                    }
                    tempList.add(indicatorSubRespVO);
                }
                for (IndicatorRespVO indicatorRespVO : indicatorRespVOS) {
                    indicatorRespVO.setSubList(map.get(indicatorRespVO.getId()));
                }
            }
        }
        return indicatorRespVOS;
    }

    @Override
    public void startStopHandle(IndicatorStartStopHandleReqVO updateReqVO) {
        IndicatorDO indicatorDO = indicatorDao.selectById(updateReqVO.getId());
        if (indicatorDO == null) {
            throw new ServerException("综合管理-绩效考核指标数据不存在");
        }
        indicatorDO.setIsEnabled(updateReqVO.getIsEnabled());
        indicatorDao.updateById(indicatorDO);
    }


}
