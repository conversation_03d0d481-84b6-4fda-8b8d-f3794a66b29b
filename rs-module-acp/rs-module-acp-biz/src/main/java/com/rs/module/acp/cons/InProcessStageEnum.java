package com.rs.module.acp.cons;

/**
 * 入所收押流程阶段枚举
 */
public enum InProcessStageEnum {
    
    PENDING_REGISTRATION("01", "待登记"),
    
    PENDING_HEALTH_CHECK("02", "待健康检查"),
    
    PENDING_BIO_INFO_COLLECTION("03", "待生物信息采集"),
    
    PENDING_ITEM_REGISTRATION("04", "待物品登记"),
    
    PENDING_LEADER_APPROVAL("05", "待领导审批"),
    
    COMPLETED("06", "已完成");

    private final String code;
    private final String value;

    InProcessStageEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 状态码
     * @return 对应的枚举值
     */
    public static InProcessStageEnum getByCode(String code) {
        for (InProcessStageEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据value获取枚举值
     *
     * @param value 状态值
     * @return 对应的枚举值
     */
    public static InProcessStageEnum getByValue(String value) {
        for (InProcessStageEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
}