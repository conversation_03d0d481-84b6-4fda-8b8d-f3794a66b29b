package com.rs.module.acp.service.gj.face2face;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceConfigListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceConfigPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceConfigSaveReqVO;
import com.rs.module.acp.entity.gj.FaceToFaceConfigDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-面对面配置 Service 接口
 *
 * <AUTHOR>
 */
public interface FaceToFaceConfigService extends IBaseService<FaceToFaceConfigDO>{

    /**
     * 创建实战平台-管教业务-面对面配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createFaceToFaceConfig(@Valid FaceToFaceConfigSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-面对面配置
     *
     * @param updateReqVO 更新信息
     */
    void updateFaceToFaceConfig(@Valid FaceToFaceConfigSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-面对面配置
     *
     * @param id 编号
     */
    void deleteFaceToFaceConfig(String id);

    /**
     * 获得实战平台-管教业务-面对面配置
     *
     * @param id 编号
     * @return 实战平台-管教业务-面对面配置
     */
    FaceToFaceConfigDO getFaceToFaceConfig(String id);

    /**
    * 获得实战平台-管教业务-面对面配置分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-面对面配置分页
    */
    PageResult<FaceToFaceConfigDO> getFaceToFaceConfigPage(FaceToFaceConfigPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-面对面配置列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-面对面配置列表
    */
    List<FaceToFaceConfigDO> getFaceToFaceConfigList(FaceToFaceConfigListReqVO listReqVO);


    List<FaceToFaceConfigDO> selectListByOrgCode(String orgCode);


}
