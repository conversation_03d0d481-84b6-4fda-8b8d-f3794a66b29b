package com.rs.module.acp.controller.app.todo;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.service.todo.TodoBusinessType;
import com.rs.module.acp.service.todo.TodoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "待办提醒")
@RestController
@RequestMapping("/app/acp/msg")
@Validated
public class TodoController {

    @Autowired
    TodoService todoService;



    @GetMapping("/js/getJgryTodoCountByRoomId")
    @ApiOperation(value = "首页-查询监室内在押人员的消息（数量）-未登录")
    @Parameter(name = "roomId", description = "监室id", required = true)
    public CommonResult<?> getJgryTodoCountByRoomId(String roomId) {
        return success(todoService.processJgryTodoCountByRoomId(roomId));
    }

    @GetMapping("/js/getJgryBusinessTodoCountByJgrybm")
    @ApiOperation(value = "首页-查询监室内在押人员具体业务的消息（数量）-登录后")
    @Parameter(name = "jgrmbm", description = "监管人员编码", required = true)
    public CommonResult<?> getJgryBizTodoCountByJgrybm(String jgrmbm) {
        return success(todoService.processBizTodoCount(jgrmbm));
    }

    @GetMapping("/js/getJgryBusinessTodoByJgrybm")
    @ApiOperation(value = "首页-查询监室内在押人员具体业务的消息-登录后")
    @Parameter(name = "jgrmbm", description = "监管人员编码", required = true)
    public CommonResult<?> getJgryBusinessTodoByJgrybm(String jgrmbm, TodoBusinessType businessType) {
        return success(todoService.processTodo(jgrmbm,businessType));
    }

}
