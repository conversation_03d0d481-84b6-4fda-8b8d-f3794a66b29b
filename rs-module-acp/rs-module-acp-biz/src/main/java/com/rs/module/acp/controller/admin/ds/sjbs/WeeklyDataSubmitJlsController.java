package com.rs.module.acp.controller.admin.ds.sjbs;

import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.DailyDataSubmitKssRespVO;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyDataSubmitJlsRespVO;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyDataSubmitJlsSaveReqVO;
import com.rs.module.acp.entity.ds.sjbs.DailyDataSubmitKssDO;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.ds.sjbs.WeeklyDataSubmitJlsDO;
import com.rs.module.acp.service.ds.sjbs.WeeklyDataSubmitJlsService;

import java.util.Date;
import java.util.List;

@Api(tags = "实战平台-数据固化-每周数据报送(拘留所)")
@RestController
@RequestMapping("/acp/ds/weeklyDataSubmitJls")
@Validated
public class WeeklyDataSubmitJlsController {

    @Resource
    private WeeklyDataSubmitJlsService weeklyDataSubmitJlsService;
/*
    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-数据固化-每周数据报送(拘留所)")
    public CommonResult<String> createWeeklyDataSubmitJls(@Valid @RequestBody WeeklyDataSubmitJlsSaveReqVO createReqVO) {
        return success(weeklyDataSubmitJlsService.createWeeklyDataSubmitJls(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-数据固化-每周数据报送(拘留所)")
    public CommonResult<Boolean> updateWeeklyDataSubmitJls(@Valid @RequestBody WeeklyDataSubmitJlsSaveReqVO updateReqVO) {
        weeklyDataSubmitJlsService.updateWeeklyDataSubmitJls(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-数据固化-每周数据报送(拘留所)")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteWeeklyDataSubmitJls(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           weeklyDataSubmitJlsService.deleteWeeklyDataSubmitJls(id);
        }
        return success(true);
    }*/

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-数据固化-每周数据报送(拘留所)")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<WeeklyDataSubmitJlsRespVO> getWeeklyDataSubmitJls(@RequestParam("id") String id) {
        WeeklyDataSubmitJlsDO weeklyDataSubmitJls = weeklyDataSubmitJlsService.getWeeklyDataSubmitJls(id);
        return success(BeanUtils.toBean(weeklyDataSubmitJls, WeeklyDataSubmitJlsRespVO.class));
    }
    @GetMapping("/getByDate")
    @ApiOperation(value = "获得实战平台-数据固化-每日数据报送(拘留所)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编号"),
            @ApiImplicitParam(name = "startDate", value = "开始时间"),
            @ApiImplicitParam(name = "endDate", value = "结束时间")
    })
    public CommonResult<List<WeeklyDataSubmitJlsRespVO>> getByDate(
            @RequestParam(value = "orgCode",required = false) String orgCode,
            @RequestParam(value = "startDate",required = false) String startDate,
            @RequestParam(value = "endDate",required = false) String endDate) {
        List<WeeklyDataSubmitJlsDO> data = weeklyDataSubmitJlsService.getWeeklyDataSubmitJlsByDate(startDate,endDate,orgCode);
        return success(BeanUtils.toBean(data, WeeklyDataSubmitJlsRespVO.class));
    }
}
