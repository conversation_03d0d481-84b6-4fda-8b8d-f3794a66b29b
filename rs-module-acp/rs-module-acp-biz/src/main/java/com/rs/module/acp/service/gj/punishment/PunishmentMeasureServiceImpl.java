package com.rs.module.acp.service.gj.punishment;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentMeasureListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentMeasurePageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentMeasureSaveReqVO;
import com.rs.module.acp.dao.gj.PunishmentMeasureDao;
import com.rs.module.acp.entity.gj.PunishmentMeasureDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 实战平台-管教业务-处罚呈批关联措施 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PunishmentMeasureServiceImpl extends BaseServiceImpl<PunishmentMeasureDao, PunishmentMeasureDO> implements PunishmentMeasureService {

    @Resource
    private PunishmentMeasureDao punishmentMeasureDao;

    @Override
    public String createPunishmentMeasure(PunishmentMeasureSaveReqVO createReqVO) {
        // 插入
        PunishmentMeasureDO punishmentMeasure = BeanUtils.toBean(createReqVO, PunishmentMeasureDO.class);
        punishmentMeasureDao.insert(punishmentMeasure);
        // 返回
        return punishmentMeasure.getId();
    }

    @Override
    public void updatePunishmentMeasure(PunishmentMeasureSaveReqVO updateReqVO) {
        // 校验存在
        validatePunishmentMeasureExists(updateReqVO.getId());
        // 更新
        PunishmentMeasureDO updateObj = BeanUtils.toBean(updateReqVO, PunishmentMeasureDO.class);
        punishmentMeasureDao.updateById(updateObj);
    }

    @Override
    public void deletePunishmentMeasure(String id) {
        // 校验存在
        validatePunishmentMeasureExists(id);
        // 删除
        punishmentMeasureDao.deleteById(id);
    }

    private void validatePunishmentMeasureExists(String id) {
        if (punishmentMeasureDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-处罚呈批关联措施数据不存在");
        }
    }

    @Override
    public PunishmentMeasureDO getPunishmentMeasure(String id) {
        return punishmentMeasureDao.selectById(id);
    }

    @Override
    public PageResult<PunishmentMeasureDO> getPunishmentMeasurePage(PunishmentMeasurePageReqVO pageReqVO) {
        return punishmentMeasureDao.selectPage(pageReqVO);
    }

    @Override
    public List<PunishmentMeasureDO> getPunishmentMeasureList(PunishmentMeasureListReqVO listReqVO) {
        return punishmentMeasureDao.selectList(listReqVO);
    }


}
