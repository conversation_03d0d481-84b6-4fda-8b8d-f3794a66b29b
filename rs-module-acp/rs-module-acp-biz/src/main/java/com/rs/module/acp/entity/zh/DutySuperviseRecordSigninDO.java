package com.rs.module.acp.entity.zh;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 综合管理-值班管理-值班督导记录签至 DO
 *
 * <AUTHOR>
 */
@TableName("acp_zh_duty_supervise_record_signin")
@KeySequence("acp_zh_duty_supervise_record_signin_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_duty_supervise_record_signin")
public class DutySuperviseRecordSigninDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 值班记录ID
     */
    private String dutySuperviseRecordId;
    /**
     * 民警编号
     */
    private String policeId;
    /**
     * 民警姓名
     */
    private String policeName;

}
