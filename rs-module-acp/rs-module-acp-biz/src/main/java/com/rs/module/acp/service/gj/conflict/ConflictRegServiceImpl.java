package com.rs.module.acp.service.gj.conflict;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.cache.DicUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageParam;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictFollowupRespVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictFollowupSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictMediateHistoryRespVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictMediationOrgSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictMediationRespVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictMediationSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictPrisonerRespVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictPrisonerSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictRegApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictRegRespVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictRegSaveReqVO;
import com.rs.module.acp.controller.app.gj.vo.conflict.AppConflictConfirmReqVO;
import com.rs.module.acp.controller.app.gj.vo.conflict.AppConflictSaveReqVO;
import com.rs.module.acp.dao.gj.conflict.ConflictRegDao;
import com.rs.module.acp.entity.gj.conflict.ConflictFollowupDO;
import com.rs.module.acp.entity.gj.conflict.ConflictMediationDO;
import com.rs.module.acp.entity.gj.conflict.ConflictMediationOrgDO;
import com.rs.module.acp.entity.gj.conflict.ConflictPrisonerDO;
import com.rs.module.acp.entity.gj.conflict.ConflictRegDO;
import com.rs.module.acp.enums.gj.MediationResultEnum;
import com.rs.module.acp.enums.gj.RegStatusEnum;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderRespVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.enums.PrisonerQueryRyztEnum;
import com.rs.module.base.service.pm.PrisonerService;
import org.jetbrains.annotations.NotNull;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 实战平台-管教业务-社会矛盾化解登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ConflictRegServiceImpl extends BaseServiceImpl<ConflictRegDao, ConflictRegDO> implements ConflictRegService {

    @Resource
    private ConflictRegDao conflictRegDao;
    @Resource
    private ConflictPrisonerService conflictPrisonerService;
    @Resource
    private ConflictMediationService conflictMediationService;
    @Resource
    private ConflictMediationOrgService mediationOrgService;
    @Resource
    private PrisonerService prisonerService;
    @Resource
    private ConflictFollowupService conflictFollowupService;
    @Resource
    private BspApi bspApi;

    @Override
    public String getTJNumber() {
        return bspApi.executeByRuleCode("acp_shmd_bm_system", null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createConflict(ConflictRegSaveReqVO createReqVO) {
        ConflictRegDO conflictRegDO = new ConflictRegDO();
        BeanUtil.copyProperties(createReqVO, conflictRegDO, "id");
        conflictRegDO.setEventCode(getTJNumber());
        conflictRegDO.setRegOperatorXm(SessionUserUtil.getSessionUser().getName());
        conflictRegDO.setRegOperatorSfzh(SessionUserUtil.getSessionUser().getIdCard());
        conflictRegDO.setRegStatus(RegStatusEnum.YDJ.getCode());
        if (StrUtil.isBlank(createReqVO.getInitiator())) {
            conflictRegDO.setInitiator(SessionUserUtil.getSessionUser().getIdCard());
            conflictRegDO.setInitiatorXm(SessionUserUtil.getSessionUser().getName());
        }
        List<ConflictPrisonerSaveReqVO> prisonerList = createReqVO.getPrisonerList();
        if (CollUtil.isEmpty(prisonerList)) {
            throw new ServerException("监管人员不能为空！");
        }
        try {
            conflictRegDao.insert(conflictRegDO);
        } catch (DuplicateKeyException e) {
            throw new ServerException("该事件编号[" + conflictRegDO.getEventCode() + "]已存在，请勿重复登记！");
        }
        List<ConflictPrisonerDO> list = new ArrayList<>();
        Set<Integer> set = prisonerList.stream()
                .map(prisoner -> prisoner.getPartner() ? 1 : 0)
                .collect(Collectors.toSet());
        if (!set.contains(1)) {
            throw new ServerException("请选择当事人！");
        }
        for (ConflictPrisonerSaveReqVO prisoner : prisonerList) {
            if (conflictPrisonerService.isRegistered(prisoner.getJgrybm())) {
                throw new ServerException("监管人员[" + prisoner.getXm() + "]已登记或者调解中，请勿重复登记！");
            }
            ConflictPrisonerDO prisonerDO = new ConflictPrisonerDO();
            BeanUtil.copyProperties(prisoner, prisonerDO, "id");
            prisonerDO.setEventCode(conflictRegDO.getEventCode());
            prisonerDO.setEventId(conflictRegDO.getId());
            list.add(prisonerDO);
        }
        if (CollUtil.isNotEmpty(list)) {
            conflictPrisonerService.saveBatch(list);
        }
        return conflictRegDO.getEventCode();
    }

    @Override
    public ConflictRegRespVO getConflictRegByEventCodeVO(String eventCode) {
        // 登记信息
        ConflictRegRespVO respVO = new ConflictRegRespVO();
        ConflictRegDO conflictRegDO = getByEventCode(eventCode);
        BeanUtil.copyProperties(conflictRegDO, respVO);

        // 轨迹信息
        List<ConflictRegRespVO.ConflictRegTraceVO> traceVOS = new LinkedList<>();
        // 登记
        addTrace(traceVOS, "登记", CollUtil.newArrayList(conflictRegDO.getAddTime()));
        // 审批
        addTrace(traceVOS, "审批", CollUtil.newArrayList(conflictRegDO.getApproverTime()));

        // 处理监管人员信息
        List<ConflictPrisonerRespVO> prisonerRespVOS = conflictPrisonerService.getPrisonerVOByEventCode(eventCode);
        String jgrybms = prisonerRespVOS.stream().map(ConflictPrisonerRespVO::getJgrybm).collect(Collectors.joining(","));
        Map<String, String> nameMap = getNameMap(jgrybms);
        for (ConflictPrisonerRespVO prisoner : prisonerRespVOS) {
            String name = nameMap.get(prisoner.getJgrybm());
            prisoner.setXm(name);
        }
        respVO.setPrisonerList(prisonerRespVOS);
        // 调解信息
        respVO.setMediationCnt(0);
        List<ConflictMediationRespVO> mediationList = conflictMediationService.getMediationListByEventCode(eventCode);
        if (CollUtil.isNotEmpty(mediationList)) {
            respVO.setMediationList(mediationList);
            int mediationCnt = mediationList.stream().mapToInt(ConflictMediationRespVO::getMediationCnt).max().orElse(0);
            respVO.setMediationCnt(mediationCnt);
            List<Date> dateList = mediationList.stream().map(ConflictMediationRespVO::getMediationOperatorTime).collect(Collectors.toList());
            addTrace(traceVOS, "调解", dateList);
            ConflictMediationRespVO mediation = mediationList.stream()
                    .filter(m -> MediationResultEnum.BZTJ.getCode().equals(m.getMediationResult()))
                    .findAny().orElse(null);
            if (mediation != null) {
                addTrace(traceVOS, "结束", CollUtil.newArrayList(mediation.getMediationOperatorTime()));
            }
        }

        // 确认
        String confirmStatus = conflictRegDO.getConfirmStatus();
        if ("1".equals(confirmStatus)) {
            List<Date> dateList = prisonerRespVOS.stream().map(ConflictPrisonerRespVO::getConfirmTime).collect(Collectors.toList());
            addTrace(traceVOS, "内屏确认", dateList);
            ConflictMediationRespVO mediationRespVO = mediationList.stream()
                    .filter(m -> MediationResultEnum.TJCG.getCode().equals(m.getMediationResult()))
                    .findFirst().get();
            if (!"1".equals(mediationRespVO.getFollowUpVisit())) {
                addTrace(traceVOS, "结束", CollUtil.newArrayList(conflictRegDO.getUpdateTime()));
            }
        }

        // 回访信息
        ConflictFollowupRespVO conflictFollowup = conflictFollowupService.getConflictFollowup(eventCode);
        if (conflictFollowup != null) {
            respVO.setFollowup(conflictFollowup);
            traceVOS.add(new ConflictRegRespVO.ConflictRegTraceVO("回访", CollUtil.newArrayList(conflictFollowup.getFollowupOperatorTime())));
            addTrace(traceVOS, "结束", CollUtil.newArrayList(conflictFollowup.getFollowupOperatorTime()));
        }
        respVO.setTraceVOS(traceVOS);
        return respVO;
    }

    private void addTrace(List<ConflictRegRespVO.ConflictRegTraceVO> traceVOS, String name, List<Date> dates) {
        List<Date> dateList = dates.stream().filter(date -> date != null).collect(Collectors.toList());
        ConflictRegRespVO.ConflictRegTraceVO traceVO = new ConflictRegRespVO.ConflictRegTraceVO(name, dateList);
        traceVOS.add(traceVO);
    }

    private Map<String, String> getNameMap(String jgrybms) {
        Map<String, String> nameMap = new HashMap<>();
        List<PrisonerVwRespVO> listByJgrybm = prisonerService.getPrisonerListByJgrybm(jgrybms, PrisonerQueryRyztEnum.ZS);
        if (CollUtil.isNotEmpty(listByJgrybm)) {
            for (PrisonerVwRespVO prisoner : listByJgrybm) {
                if (!nameMap.containsKey(prisoner.getJgrybm())) {
                    nameMap.put(prisoner.getJgrybm(), prisoner.getXm());
                }
            }
        }
        return nameMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateConflict(ConflictRegSaveReqVO createReqVO) {
        ConflictRegDO conflictRegDO = getById(createReqVO.getId());
        if (conflictRegDO == null) {
            throw new ServerException("该事件ID[" + createReqVO.getId() + "]不存在！");
        }
        // 处理消息通知
        if (StrUtil.isNotBlank(createReqVO.getPcId())) {
            SendMessageUtil.ProcessTodoMsg(createReqVO.getPcId(), SessionUserUtil.getSessionUser().getIdCard(), "pc");
        }
        RegStatusEnum regStatus = RegStatusEnum.getByCode(conflictRegDO.getRegStatus());
        if (RegStatusEnum.YDJ == regStatus || RegStatusEnum.YSQ == regStatus) {
        } else {
            throw new ServerException("该事件状态为[" + regStatus.getName() + "]，不能修改！");
        }
        // 登记信息
        ConflictRegDO updateDO = new ConflictRegDO();
        BeanUtil.copyProperties(createReqVO, updateDO, "id", "eventCode", "prisonerList");
        updateDO.setId(conflictRegDO.getId());
        conflictRegDao.updateById(updateDO);
        // 处理监管人员信息
        List<ConflictPrisonerSaveReqVO> prisonerList = createReqVO.getPrisonerList();
        List<ConflictPrisonerDO> list = new ArrayList<>();
        for (ConflictPrisonerSaveReqVO prisoner : prisonerList) {
            ConflictPrisonerDO prisonerDO = new ConflictPrisonerDO();
            BeanUtil.copyProperties(prisoner, prisonerDO, "id");
            prisonerDO.setEventCode(conflictRegDO.getEventCode());
            prisonerDO.setEventId(conflictRegDO.getId());
            list.add(prisonerDO);
        }
        // 删除原有数据
        conflictPrisonerService.deleteByEventCode(conflictRegDO.getEventCode());
        if (CollUtil.isNotEmpty(list)) {
            conflictPrisonerService.saveOrUpdateBatch(list);
        }
        return conflictRegDO.getId();
    }

    @Override
    public Boolean updateProcessStatus(String eventCode, String regStatus, String actInstId) {
        LambdaUpdateWrapper<ConflictRegDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ConflictRegDO::getRegStatus, regStatus)
                .set(ConflictRegDO::getActInstId, actInstId)
                .eq(ConflictRegDO::getEventCode, eventCode);
        if (conflictRegDao.update(null, updateWrapper) > 0) {
            return true;
        }
        throw new ServerException("更新失败！");
    }

    @Override
    public void leaderApprove(ConflictRegApproveReqVO approveReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        if (sessionUser == null) {
            throw new ServerException("未登录！");
        }
        // 领导审批
        ConflictRegDO conflictDO = getByEventCode(approveReqVO.getEventCode());
        if (conflictDO == null) {
            throw new ServerException("调解事件不存在！");
        }
        RegStatusEnum regStatusEnum = RegStatusEnum.getByCode(conflictDO.getRegStatus());
        if (!RegStatusEnum.DSP.getCode().equals(regStatusEnum.getCode())) {
            throw new ServerException("该事件状态[" + regStatusEnum.getName() + "]不允许审批！");
        }
        ConflictRegDO updateDO = new ConflictRegDO();
        BeanUtil.copyProperties(approveReqVO, updateDO);
        updateDO.setId(conflictDO.getId());
        String approvalResult = approveReqVO.getApprovalResult();
        String regStatus = "";
        if ("1".equals(approvalResult) || "5".equals(approvalResult)) {
            regStatus = RegStatusEnum.TJZ.getCode();
        } else {
            regStatus = RegStatusEnum.SPBTG.getCode();
        }
        updateDO.setRegStatus(regStatus);
        conflictRegDao.updateById(updateDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMediateInfo(ConflictMediationSaveReqVO mediateInfoReqVO) {
        ConflictRegDO conflictDO = getByEventCode(mediateInfoReqVO.getEventCode());
        if (!conflictDO.getRegStatus().equals(RegStatusEnum.TJZ.getCode())) {
            throw new ServerException("登记状态错误，不能进行调解情况录入！");
        }
        updateRegDO(mediateInfoReqVO, conflictDO);
        // 调解信息
        ConflictMediationDO mediationDO = new ConflictMediationDO();
        BeanUtil.copyProperties(mediateInfoReqVO, mediationDO, "id");
        mediationDO.setEventCode(conflictDO.getEventCode());
        mediationDO.setEventId(conflictDO.getId());
        // 获取最新的调整次数
        int mediationCnt = conflictMediationService.getMediationCntByEventCode(conflictDO.getEventCode());
        mediationDO.setMediationCnt(mediationCnt + 1);
        conflictMediationService.save(mediationDO);
        // 保存单位信息
        List<ConflictMediationOrgSaveReqVO> orgReqVOS = mediateInfoReqVO.getOrgReqVOS();
        if (CollUtil.isNotEmpty(orgReqVOS)) {
            List<ConflictMediationOrgDO> orgDOList = new ArrayList<>();
            for (ConflictMediationOrgSaveReqVO orgReqVO : orgReqVOS) {
                ConflictMediationOrgDO orgDO = new ConflictMediationOrgDO();
                BeanUtil.copyProperties(orgReqVO, orgDO, "id");
                orgDO.setEventCode(conflictDO.getEventCode());
                orgDO.setEventId(conflictDO.getId());
                orgDO.setMediationId(mediationDO.getId());
                orgDOList.add(orgDO);
            }
            mediationOrgService.saveBatch(orgDOList);
        }
    }

    private void updateRegDO(ConflictMediationSaveReqVO mediateInfoReqVO, ConflictRegDO conflictDO) {
        // 状态处理
        String mediationResult = mediateInfoReqVO.getMediationResult();
        // 调解结果（1：调解成功，2：调解暂缓，3：不再调解）
        String regStatus = null;
        MediationResultEnum mediationResultEnum = MediationResultEnum.getByCode(mediationResult);
        ConflictRegDO update = new ConflictRegDO();
        List<ConflictPrisonerRespVO> prisonerRespVOS = conflictPrisonerService.getPrisonerVOByEventCode(conflictDO.getEventCode());
        switch (mediationResultEnum) {
            case TJCG:
                // 调解成功
                update.setConfirmStatus("0");
                updatePrisonerConfirmStatus(conflictDO.getEventCode());
                break;
            case BZTJ:
                // 不再调解
                regStatus = RegStatusEnum.TJCG.getCode();
                break;
            case TJZH:
                // 调解暂缓
                break;
            default:
                throw new ServerException("调解结果类型[" + mediationResult + "]错误！");
        }
        update.setId(conflictDO.getId());
        update.setRegStatus(regStatus);
        // 更新事件
        conflictRegDao.updateById(update);
    }

    private void updatePrisonerConfirmStatus(String eventCode) {
        conflictPrisonerService.updateConfirmStatus(eventCode, 0);
    }

    @Override
    public List<ConflictMediateHistoryRespVO> getMediateHistory(String eventCode, int pageSize) {
        MPJLambdaWrapper<ConflictRegDO> innerJoin = new MPJLambdaWrapperX<ConflictRegDO>();
        innerJoin.select(ConflictRegDO::getEventCode)
                .selectAs(ConflictMediationDO::getMediationOperatorTime, ConflictMediateHistoryRespVO::getMediationOperatorTime)
                .innerJoin(ConflictMediationDO.class, ConflictMediationDO::getEventCode, ConflictRegDO::getEventCode)
                .eq(ConflictRegDO::getEventCode, eventCode)
                .orderByDesc(ConflictMediationDO::getMediationOperatorTime);
        PageParam pageParam = new PageParam();
        pageParam.setPageNo(1);
        pageParam.setPageSize(pageSize);
        PageResult<ConflictMediateHistoryRespVO> joinPage = conflictRegDao.selectJoinPage(pageParam, ConflictMediateHistoryRespVO.class, innerJoin);
        return joinPage.getList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void applyConflict(AppConflictSaveReqVO reqVO) {
        PrisonerVwRespVO prisonerOne = prisonerService.getPrisonerOne(null, reqVO.getInitiator(), PrisonerQueryRyztEnum.ZS);
        List<PrisonRoomWarderRespVO> zgzjList = prisonerOne.getZgzjList();
        PrisonRoomWarderRespVO respVO = zgzjList.stream()
                .filter(zgzj -> "w".equals(zgzj.getUserType())).findFirst().orElse(null);
        if (respVO == null) {
            throw new ServerException("找不到管教民警！");
        }
        if (conflictPrisonerService.isRegistered(reqVO.getInitiator())) {
            throw new ServerException("您已登申请，请勿重复申请！");
        }

        String policeSfzh = StrUtil.isBlank(respVO.getPoliceSfzh()) ? respVO.getPoliceId() : respVO.getPoliceSfzh();

        ConflictRegDO conflictRegDO = new ConflictRegDO();
        BeanUtil.copyProperties(reqVO, conflictRegDO, "id");
        conflictRegDO.setEventCode(getTJNumber());
        conflictRegDO.setRegStatus(RegStatusEnum.YSQ.getCode());
        conflictRegDO.setAddUser(policeSfzh);
        conflictRegDO.setAddUserName(respVO.getPoliceName());
        conflictRegDO.setUpdateUser(policeSfzh);
        conflictRegDO.setUpdateUserName(respVO.getPoliceName());

        conflictRegDao.insert(conflictRegDO);

        ConflictPrisonerDO prisonerDO = getConflictPrisonerDO(reqVO, conflictRegDO);

        conflictPrisonerService.save(prisonerDO);

        String simpleUUID = IdUtil.fastSimpleUUID();
        String conflictTypeName = DicUtil.translate("ZD_MDLB", reqVO.getConflictType());
        String title = String.format("%s于%s提交了%s社会矛盾化解申请，请进行确认！", prisonerOne.getXm(),
                DateUtil.formatDateTime(new Date()), conflictTypeName);
        String content = "";                            //消息内容
        String url = "conflict/registration?evenCode=" + conflictRegDO.getEventCode() + "&jgrybm=" + reqVO.getInitiator() + "&pcId=" + simpleUUID;                                //消息处理页面地址
        //来源应用
        String fUser = prisonerOne.getZjhm();                        //来源用户身份证号
        String fUserName = prisonerOne.getXm();                                //来源用户姓名
        String fOrgCode = prisonerOne.getOrgCode();                        //来源机构代码
        String fOrgName = prisonerOne.getOrgName();                                //来源机构名称
        String fXxpt = "pc";
        //业务编号
        String ywbh = prisonerOne.getJgrybm();
        List<ReceiveUser> receiveUserList = ListUtil.of(        //消息接收用户
                new ReceiveUser(policeSfzh, fOrgCode));
        SendMessageUtil.sendTodoMsg(title, content, url, HttpUtils.getAppCode(), fUser, fUserName, fOrgCode, fOrgName,
                null, simpleUUID, fXxpt, ywbh, receiveUserList, MsgBusTypeEnum.GJ_SSHJ.getCode(), null);

    }

    @NotNull
    private static ConflictPrisonerDO getConflictPrisonerDO(AppConflictSaveReqVO reqVO, ConflictRegDO conflictRegDO) {
        ConflictPrisonerDO prisonerDO = new ConflictPrisonerDO();
        prisonerDO.setEventCode(conflictRegDO.getEventCode());
        prisonerDO.setEventId(conflictRegDO.getId());
        prisonerDO.setJgrybm(reqVO.getInitiator());
        prisonerDO.setAddUser(conflictRegDO.getAddUser());
        prisonerDO.setAddUserName(conflictRegDO.getAddUserName());
        prisonerDO.setUpdateUser(conflictRegDO.getUpdateUser());
        prisonerDO.setUpdateUserName(conflictRegDO.getUpdateUserName());
        prisonerDO.setPartner(true);
        return prisonerDO;
    }

    @Override
    public void confirm(AppConflictConfirmReqVO queryVO) {
        ConflictPrisonerDO prisonerDO = conflictPrisonerService.getById(queryVO.getPrisonerId());
        if (prisonerDO == null) {
            throw new ServerException("该人员调解记录不存在！");
        }
        if (prisonerDO.getConfirmStatus() != null && prisonerDO.getConfirmStatus().intValue() == 1) {
            return;
        }
        ConflictPrisonerDO updateDO = new ConflictPrisonerDO();
        BeanUtil.copyProperties(queryVO, updateDO, "id");
        updateDO.setId(prisonerDO.getId());
        // 确认时间
        updateDO.setConfirmTime(new Date());
        updateDO.setIsRated(1);
        updateDO.setConfirmStatus(1);
        conflictPrisonerService.updateById(updateDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void policeConfirm(String id) {
        ConflictRegDO regDO = conflictRegDao.selectById(id);
        if (regDO == null) {
            throw new ServerException("该事件[" + regDO.getEventName() + "]不存在！");
        }
        if ("1".equals(regDO.getRegStatus())) {
            throw new ServerException("该事件[" + regDO.getEventName() + "]已确认，请勿重复操作！");
        }
        LambdaQueryWrapper<ConflictPrisonerDO> query = new LambdaQueryWrapper<>();
        query.eq(ConflictPrisonerDO::getEventCode, regDO.getEventCode());
        List<ConflictPrisonerDO> list = conflictPrisonerService.list(query);
        for (ConflictPrisonerDO prisonerDO : list) {
            Integer confirmStatus = prisonerDO.getConfirmStatus();
            if (confirmStatus == null || confirmStatus.intValue() == 0) {
                throw new ServerException("存在未确认的在押人员调解记录，请联系确认后再进行操作！");
            }
        }
        ConflictRegDO update = new ConflictRegDO();
        update.setId(regDO.getId());
        update.setConfirmStatus("1");
        update.setConfirmOperatorXm(SessionUserUtil.getSessionUser().getName());
        update.setConfirmOperatorSfzh(SessionUserUtil.getSessionUser().getIdCard());
        update.setConfirmOperatorTime(new Date());

        Integer isRated = list.stream()
                .filter(p -> "0".equals(p.getIsRated())).findAny().isPresent() ? 0 : 1;
        update.setIsRated(isRated);
        Set<Integer> set = list.stream().filter(p -> p.getSatisfactionScore() != null)
                .map(ConflictPrisonerDO::getSatisfactionScore).collect(Collectors.toSet());
        if (set != null && set.contains(0)) {
            update.setSatisfactionScore(0);
        } else {
            update.setSatisfactionScore(1);
        }
        // 处理状态
        List<ConflictMediationDO> result =
                conflictMediationService.getMediationByEventCodeAndMediationResult(regDO.getEventCode(), MediationResultEnum.TJCG);
        String followUpVisit = result.get(0).getFollowUpVisit();
        if (followUpVisit == null || followUpVisit.equals("0")) {
            update.setRegStatus(RegStatusEnum.TJCG.getCode());
        }
        conflictRegDao.updateById(update);
    }

    @Override
    public ConflictRegDO getByEventCode(String eventCode) {
        ConflictRegDO conflictRegDO = conflictRegDao.selectOne(ConflictRegDO::getEventCode, eventCode);
        if (conflictRegDO == null) {
            throw new ServerException("该事件编号[" + eventCode + "]不存在！");
        }
        return conflictRegDO;
    }

    @Override
    public void returnAndResign(String id) {
        ConflictPrisonerDO prisonerDO = conflictPrisonerService.getById(id);
        if (prisonerDO == null) {
            throw new ServerException("社会矛盾化解登记-关联在押人员数据不存在");
        }
        LambdaUpdateWrapper<ConflictPrisonerDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ConflictPrisonerDO::getConfirmStatus, 0)
                .set(ConflictPrisonerDO::getIsRated, 0)
                .set(ConflictPrisonerDO::getConfirmTime, null)
                .set(ConflictPrisonerDO::getPrisonerSignature, null)
                .set(ConflictPrisonerDO::getSatisfactionScore, null)
                .eq(ConflictPrisonerDO::getId, id);
        conflictPrisonerService.update(null, updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createFollowUp(ConflictFollowupSaveReqVO createReqVO) {
        String eventCode = createReqVO.getEventCode();
        List<ConflictMediationDO> list = conflictMediationService
                .getMediationByEventCodeAndMediationResult(eventCode, MediationResultEnum.TJCG);
        ConflictMediationDO mediationDO = list.stream().filter(m -> m.getMediationResult()
                .equals(MediationResultEnum.TJCG.getCode())).findAny().orElseGet(null);
        if (mediationDO == null || !MediationResultEnum.TJCG.getCode().equals(mediationDO.getMediationResult())) {
            throw new ServerException("该事件编号[" + eventCode + "]不存在调解成功的调解记录, 无法进行回访！");
        }
        if (!"1".equals(mediationDO.getFollowUpVisit())) {
            throw new ServerException("该事件编号[" + eventCode + "]不需要进行回访，请勿操作！");
        }
        ConflictRegDO conflictRegDO = getByEventCode(eventCode);
        if (conflictRegDO.getRegStatus().equals(RegStatusEnum.TJCG.getCode())) {
            throw new ServerException("该事件编号[" + eventCode + "]已完成，无法进行回访！");
        }
        if ("0".equals(conflictRegDO.getConfirmStatus())) {
            throw new ServerException("该事件编号[" + eventCode + "]未确认，无法进行回访！");
        }
        ConflictFollowupDO followupDO = new ConflictFollowupDO();
        BeanUtil.copyProperties(createReqVO, followupDO, "id");
        followupDO.setEventId(conflictRegDO.getId());
        followupDO.setEventCode(conflictRegDO.getEventCode());
        followupDO.setFollowupOperatorTime(new Date());
        // 处理人员
        List<String> updateList = new ArrayList<>();
        List<ConflictPrisonerSaveReqVO> prisonerList = createReqVO.getPrisonerList();
        for (ConflictPrisonerSaveReqVO prisoner : prisonerList) {
            updateList.add(prisoner.getJgrybm());
        }
        if (CollUtil.isNotEmpty(updateList)) {
            conflictPrisonerService.batchUpdateByEventCodeAndJgrybm(eventCode, updateList);
        }
        conflictFollowupService.save(followupDO);
        // 处理状态
        ConflictRegDO updateRegDO = new ConflictRegDO();
        updateRegDO.setId(conflictRegDO.getId());
        updateRegDO.setRegStatus(RegStatusEnum.TJCG.getCode());
        conflictRegDao.updateById(updateRegDO);
        return followupDO.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteConflict(String ids) {
        if (StrUtil.isNotBlank(ids)) {
            List<String> idArr = Arrays.asList(ids.split(","));
            LambdaQueryWrapper<ConflictRegDO> query = Wrappers.lambdaQuery(ConflictRegDO.class);
            query.select(ConflictRegDO::getEventCode, ConflictRegDO::getRegStatus)
                    .in(ConflictRegDO::getId, idArr);
            List<ConflictRegDO> conflictRegDOS = conflictRegDao.selectList(query);
            List<String> eventCodeList = new ArrayList<>();
            for (ConflictRegDO conflictRegDO : conflictRegDOS) {
                if (!conflictRegDO.getRegStatus().equals(RegStatusEnum.YDJ.getCode())) {
                    throw new ServerException("存在事件状态不是已登记的事件，无法删除！");
                }
                eventCodeList.add(conflictRegDO.getEventCode());
            }
            // 删除关联的事件
            conflictRegDao.deleteConflictRegDOsByIds(idArr);
            conflictPrisonerService.deleteByEventCode(eventCodeList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void back(String pcId, String eventCode, String jgrybm) {
        ConflictRegDO conflictRegDO = getByEventCode(eventCode);
        if (conflictRegDO == null) {
            throw new ServerException("该事件不存在！");
        }
        if (!RegStatusEnum.YSQ.getCode().equals(conflictRegDO.getRegStatus())) {
            throw new ServerException("该事件状态不是待确认状态，无法进行退回操作！");
        }
        ConflictRegDO update = new ConflictRegDO();
        update.setId(conflictRegDO.getId());
        update.setRegStatus(RegStatusEnum.BTH.getCode());
        conflictRegDao.updateById(update);
        // 处理消息通知
        if (StrUtil.isNotBlank(pcId)) {
            SendMessageUtil.ProcessTodoMsg(pcId, SessionUserUtil.getSessionUser().getIdCard(), "pc");
        }
    }
}
