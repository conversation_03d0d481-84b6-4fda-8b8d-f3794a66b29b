package com.rs.module.acp.service.pi;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventPrisonerListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventPrisonerPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventPrisonerRespVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventPrisonerSaveReqVO;
import com.rs.module.acp.entity.pi.PrisonEventPrisonerDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-巡视管控-所情事件在押人员 Service 接口
 *
 * <AUTHOR>
 */
public interface PrisonEventPrisonerService extends IBaseService<PrisonEventPrisonerDO>{

    /**
     * 创建实战平台-巡视管控-所情事件在押人员
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrisonEventPrisoner(@Valid PrisonEventPrisonerSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-所情事件在押人员
     *
     * @param updateReqVO 更新信息
     */
    void updatePrisonEventPrisoner(@Valid PrisonEventPrisonerSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-所情事件在押人员
     *
     * @param id 编号
     */
    void deletePrisonEventPrisoner(String id);

    /**
     * 获得实战平台-巡视管控-所情事件在押人员
     *
     * @param id 编号
     * @return 实战平台-巡视管控-所情事件在押人员
     */
    PrisonEventPrisonerDO getPrisonEventPrisoner(String id);

    /**
    * 获得实战平台-巡视管控-所情事件在押人员分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-巡视管控-所情事件在押人员分页
    */
    PageResult<PrisonEventPrisonerDO> getPrisonEventPrisonerPage(PrisonEventPrisonerPageReqVO pageReqVO);

    /**
    * 获得实战平台-巡视管控-所情事件在押人员列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-巡视管控-所情事件在押人员列表
    */
    List<PrisonEventPrisonerDO> getPrisonEventPrisonerList(PrisonEventPrisonerListReqVO listReqVO);

    /**
     * 保存在押人员
     * @param saveReqVOList
     * @param evenId
     * @return
     */
    boolean savePrisonEventPrisonerList(List<PrisonEventPrisonerSaveReqVO> saveReqVOList,String evenId);

    /**
     * 根据所情ID获取在押人员
     * @param eventId
     * @return
     */
    List<PrisonEventPrisonerRespVO> getPrisonEventPrisonerListByEventId(String eventId);
}
