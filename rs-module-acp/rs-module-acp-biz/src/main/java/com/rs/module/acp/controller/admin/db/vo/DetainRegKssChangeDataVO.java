package com.rs.module.acp.controller.admin.db.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 实战平台-监管管理-业务数据变更 Response VO")
@Data
public class DetainRegKssChangeDataVO {
private static final long serialVersionUID = 1L;


    private String id;

    @ApiModelProperty("操作类型")
    private String operationType;

    @ApiModelProperty("数据字段")
    private String field;

    @ApiModelProperty("数据字段名称")
    private String fieldName;

    @ApiModelProperty("修改值")
    private String newValue;

    @ApiModelProperty("修改值翻译")
    private String newValueName;

    @ApiModelProperty("原值")
    private String oldValue;

    @ApiModelProperty("原值翻译")
    private String oldValueName;

    @ApiModelProperty("理由")
    private String reason;

    @ApiModelProperty("备注")
    private String remark;

}
