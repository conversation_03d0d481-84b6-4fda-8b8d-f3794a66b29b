package com.rs.module.acp.dao.gj;

import java.util.*;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.performancejls.PerformanceJlsListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.performancejls.PerformanceJlsPageReqVO;
import com.rs.module.acp.entity.gj.PerformanceJlsDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 实战平台-管教业务-人员表现鉴定表-拘留所 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PerformanceJlsDao extends IBaseDao<PerformanceJlsDO> {


    default PageResult<PerformanceJlsDO> selectPage(PerformanceJlsPageReqVO reqVO) {
        Page<PerformanceJlsDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PerformanceJlsDO> wrapper = new LambdaQueryWrapperX<PerformanceJlsDO>()
            .eqIfPresent(PerformanceJlsDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PerformanceJlsDO::getPerformanceSgsjzd, reqVO.getPerformanceSgsjzd())
            .eqIfPresent(PerformanceJlsDO::getPerformanceYrshgl, reqVO.getPerformanceYrshgl())
            .eqIfPresent(PerformanceJlsDO::getPerformanceZsxwhqx, reqVO.getPerformanceZsxwhqx())
            .eqIfPresent(PerformanceJlsDO::getPerformanceBlxwhqx, reqVO.getPerformanceBlxwhqx())
            .eqIfPresent(PerformanceJlsDO::getPerformanceLwygryqk, reqVO.getPerformanceLwygryqk())
            .eqIfPresent(PerformanceJlsDO::getPerformanceCjsnjyqk, reqVO.getPerformanceCjsnjyqk())
            .eqIfPresent(PerformanceJlsDO::getPerformanceRchgqk, reqVO.getPerformanceRchgqk())
            .eqIfPresent(PerformanceJlsDO::getPerformanceQtqk, reqVO.getPerformanceQtqk())
            .eqIfPresent(PerformanceJlsDO::getNeedSituations, reqVO.getNeedSituations())
            .eqIfPresent(PerformanceJlsDO::getStatus, reqVO.getStatus())
            .eqIfPresent(PerformanceJlsDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(PerformanceJlsDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(PerformanceJlsDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(PerformanceJlsDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(PerformanceJlsDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(PerformanceJlsDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(PerformanceJlsDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PerformanceJlsDO::getAddTime);
        }
        Page<PerformanceJlsDO> performanceJlsPage = selectPage(page, wrapper);
        return new PageResult<>(performanceJlsPage.getRecords(), performanceJlsPage.getTotal());
    }
    default List<PerformanceJlsDO> selectList(PerformanceJlsListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PerformanceJlsDO>()
            .eqIfPresent(PerformanceJlsDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PerformanceJlsDO::getPerformanceSgsjzd, reqVO.getPerformanceSgsjzd())
            .eqIfPresent(PerformanceJlsDO::getPerformanceYrshgl, reqVO.getPerformanceYrshgl())
            .eqIfPresent(PerformanceJlsDO::getPerformanceZsxwhqx, reqVO.getPerformanceZsxwhqx())
            .eqIfPresent(PerformanceJlsDO::getPerformanceBlxwhqx, reqVO.getPerformanceBlxwhqx())
            .eqIfPresent(PerformanceJlsDO::getPerformanceLwygryqk, reqVO.getPerformanceLwygryqk())
            .eqIfPresent(PerformanceJlsDO::getPerformanceCjsnjyqk, reqVO.getPerformanceCjsnjyqk())
            .eqIfPresent(PerformanceJlsDO::getPerformanceRchgqk, reqVO.getPerformanceRchgqk())
            .eqIfPresent(PerformanceJlsDO::getPerformanceQtqk, reqVO.getPerformanceQtqk())
            .eqIfPresent(PerformanceJlsDO::getNeedSituations, reqVO.getNeedSituations())
            .eqIfPresent(PerformanceJlsDO::getStatus, reqVO.getStatus())
            .eqIfPresent(PerformanceJlsDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(PerformanceJlsDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(PerformanceJlsDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(PerformanceJlsDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(PerformanceJlsDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(PerformanceJlsDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(PerformanceJlsDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(PerformanceJlsDO::getAddTime));    }


    JSONObject getJlsRyxxByJgrybm(@Param("jgrybm") String jgrybm);
}
