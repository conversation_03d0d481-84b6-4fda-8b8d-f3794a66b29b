package com.rs.module.acp.controller.admin.gj.vo.equipment;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 保存待登记的信息
 * <AUTHOR>
 * @date 2025/6/4 9:12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EquipmentUseSaveRegInfoVO extends BaseVO{

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("开始日期")
    private Date startTime;

    @ApiModelProperty("结束日期")
    private Date endTime;

    @ApiModelProperty("执行情况")
    private String executeSituation;

    @ApiModelProperty("使用登记人")
    private String useRegUser;




}
