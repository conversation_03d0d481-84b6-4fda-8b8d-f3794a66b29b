package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.entity.wb.ArraignmentDO;
import com.rs.module.acp.entity.wb.ConsularMeetingPersonDO;
import com.rs.module.acp.entity.wb.LawyerMeetingDO;
import com.rs.module.acp.enums.wb.WbConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.ConsularMeetingDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.ConsularMeetingDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-领事会见登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ConsularMeetingServiceImpl extends BaseServiceImpl<ConsularMeetingDao, ConsularMeetingDO> implements ConsularMeetingService {

    @Resource
    private ConsularMeetingDao consularMeetingDao;

    @Autowired
    private WbCommonService wbCommonService;

    @Autowired
    private ConsularMeetingPersonService consularMeetingPersonService;

    @Autowired
    private ConsularMeetingCompanionService consularMeetingCompanionService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createConsularMeeting(ConsularMeetingSaveReqVO createReqVO) {
        // 插入
        ConsularMeetingDO consularMeeting = BeanUtils.toBean(createReqVO, ConsularMeetingDO.class);
        consularMeeting.setId(StringUtil.getGuid32());

        List<ConsularMeetingPersonSaveReqVO> meetingPersonSaveReqVOList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(createReqVO.getCasePersonnelList())){
            meetingPersonSaveReqVOList = BeanUtils.toBean(createReqVO.getCasePersonnelList(),ConsularMeetingPersonSaveReqVO.class);
            for(ConsularMeetingPersonSaveReqVO personSaveReqVO:meetingPersonSaveReqVOList){
                personSaveReqVO.setId(null);
                personSaveReqVO.setRylx("1");
                if(ObjectUtil.isNotEmpty(personSaveReqVO.getGzzjUrl())){
                    personSaveReqVO.setGzzjUrl(wbCommonService.saveFile(null,personSaveReqVO.getGzzjUrl()));
                }
            }
        }
        if(CollectionUtil.isNotEmpty(createReqVO.getConsularList())){
            for(ConsularSaveReqVO consularSaveReqVO:createReqVO.getConsularList()){
                ConsularMeetingPersonSaveReqVO personSaveReqVO = new ConsularMeetingPersonSaveReqVO();
                personSaveReqVO.setRylx("0");
                personSaveReqVO.setXm(consularSaveReqVO.getName());
                personSaveReqVO.setZjlx(consularSaveReqVO.getIdType());
                personSaveReqVO.setZjhm(consularSaveReqVO.getIdNumber());
                personSaveReqVO.setXb(consularSaveReqVO.getGender());
                personSaveReqVO.setGj(consularSaveReqVO.getNationality());
                personSaveReqVO.setLxfs(consularSaveReqVO.getContact());
                personSaveReqVO.setWorkUnit(consularSaveReqVO.getWorkUnit());
                personSaveReqVO.setZpUrl(consularSaveReqVO.getImageUrl());
                meetingPersonSaveReqVOList.add(personSaveReqVO);
            }
        }


        if(CollectionUtil.isNotEmpty(meetingPersonSaveReqVOList)){
            consularMeetingPersonService.saveConsularMeetingPersonList(meetingPersonSaveReqVOList,consularMeeting.getId());
        }

        if(CollectionUtil.isNotEmpty(createReqVO.getCompanionList())){
            consularMeetingCompanionService.saveConsularMeetingCompanionList(createReqVO.getCompanionList(),consularMeeting.getId());
        }

        if(ObjectUtil.isNotEmpty(createReqVO.getMeetingDocumentsUrl())){
            createReqVO.setMeetingDocumentsUrl(wbCommonService.saveFile(null,createReqVO.getMeetingDocumentsUrl()));
        }

        consularMeeting.setStatus("0");

        consularMeetingDao.insert(consularMeeting);

        wbCommonService.registrationReminder(JSONObject.parseObject(JSON.toJSONString(consularMeeting)), WbConstants.BUSINESS_TYPE_CONSULAR_MEETING);

        ConsularMeetingDO consularMeetingDO = getById(consularMeeting.getId());
        WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(consularMeeting,WbInspectionResultsSaveReqVO.class);
        resultsSaveReqVO.setJgrybm(consularMeetingDO.getJgrybm());
        resultsSaveReqVO.setApplMeetingTime(consularMeetingDO.getApplyMeetingStartTime());
        wbCommonService.saveInOutRecord(resultsSaveReqVO,"07","out","0",false);

        // 返回
        return consularMeeting.getId();
    }

    @Override
    public void updateConsularMeeting(ConsularMeetingSaveReqVO updateReqVO) {
        // 校验存在
        validateConsularMeetingExists(updateReqVO.getId());
        // 更新
        ConsularMeetingDO updateObj = BeanUtils.toBean(updateReqVO, ConsularMeetingDO.class);
        consularMeetingDao.updateById(updateObj);
    }

    @Override
    public void deleteConsularMeeting(String id) {
        // 校验存在
        validateConsularMeetingExists(id);
        // 删除
        consularMeetingDao.deleteById(id);
    }

    private void validateConsularMeetingExists(String id) {
        if (consularMeetingDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-领事会见登记数据不存在");
        }
    }

    @Override
    public ConsularMeetingDO getConsularMeeting(String id) {
        return consularMeetingDao.selectById(id);
    }

    @Override
    public PageResult<ConsularMeetingDO> getConsularMeetingPage(ConsularMeetingPageReqVO pageReqVO) {
        return consularMeetingDao.selectPage(pageReqVO);
    }

    @Override
    public List<ConsularMeetingDO> getConsularMeetingList(ConsularMeetingListReqVO listReqVO) {
        return consularMeetingDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean allocationRoom(String id, String roomId) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        return update(new LambdaUpdateWrapper<ConsularMeetingDO>().eq(ConsularMeetingDO::getId, id)
                .set(ConsularMeetingDO::getRoomId, roomId)
                .set(ConsularMeetingDO::getStatus, "2")
                .set(ConsularMeetingDO::getAssignmentPolice, sessionUser.getName())
                .set(ConsularMeetingDO::getAssignmentPoliceSfzh, sessionUser.getIdCard())
                .set(ConsularMeetingDO::getAssignmentRoomTime, new Date()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean escortingInspect(ConsularMeetingSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        LambdaUpdateWrapper<ConsularMeetingDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ConsularMeetingDO::getId, updateReqVO.getId())
                .set(ConsularMeetingDO::getEscortingPolice, updateReqVO.getEscortingPolice())
                .set(ConsularMeetingDO::getEscortingPoliceSfzh, updateReqVO.getEscortingPoliceSfzh())
                .set(ConsularMeetingDO::getEscortingTime, updateReqVO.getEscortingTime())
                .set(ConsularMeetingDO::getInspectionResult, updateReqVO.getInspectionResult())
                .set(ConsularMeetingDO::getProhibitedItems, updateReqVO.getProhibitedItems())
                .set(ConsularMeetingDO::getPhysicalExam, updateReqVO.getPhysicalExam())
                .set(ConsularMeetingDO::getAbnormalSituations, updateReqVO.getAbnormalSituations())
                .set(ConsularMeetingDO::getInspectionTime, updateReqVO.getInspectionTime())
                .set(ConsularMeetingDO::getInspector, updateReqVO.getInspector())
                .set(ConsularMeetingDO::getInspectorSfzh, updateReqVO.getInspectorSfzh())
                .set(ConsularMeetingDO::getMeetingStartTime, updateReqVO.getMeetingStartTime())
                .set(ConsularMeetingDO::getStatus, "3");
        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorSfzh())){
            lambdaUpdateWrapper.set(ConsularMeetingDO::getEscortingOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(ConsularMeetingDO::getEscortingOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorTime())){
            lambdaUpdateWrapper.set(ConsularMeetingDO::getEscortingOperatorTime,new Date());
        }
        if("0".equals(updateReqVO.getDataSources())){
            ConsularMeetingDO consularMeetingDO = getById(updateReqVO.getId());
            WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(updateReqVO,WbInspectionResultsSaveReqVO.class);
            resultsSaveReqVO.setJgrybm(consularMeetingDO.getJgrybm());
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"07","out","3",false);
        }
        return update(lambdaUpdateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean returnInspection(ConsularMeetingSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        LambdaUpdateWrapper<ConsularMeetingDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ConsularMeetingDO::getId, updateReqVO.getId())
                .set(ConsularMeetingDO::getMeetingEndTime, updateReqVO.getMeetingEndTime())
                .set(ConsularMeetingDO::getReturnInspectorSfzh, updateReqVO.getReturnInspectorSfzh())
                .set(ConsularMeetingDO::getReturnInspector, updateReqVO.getReturnInspector())
                .set(ConsularMeetingDO::getReturnInspectionTime, updateReqVO.getReturnInspectionTime())
                .set(ConsularMeetingDO::getReturnInspectionResult, updateReqVO.getReturnInspectionResult())
                .set(ConsularMeetingDO::getReturnProhibitedItems, updateReqVO.getReturnProhibitedItems())
                .set(ConsularMeetingDO::getReturnPhysicalExam, updateReqVO.getReturnPhysicalExam())
                .set(ConsularMeetingDO::getReturnAbnormalSituations, updateReqVO.getReturnAbnormalSituations())
                .set(ConsularMeetingDO::getReturnTime, updateReqVO.getReturnTime())
                .set(ConsularMeetingDO::getReturnPolice, updateReqVO.getReturnPolice())
                .set(ConsularMeetingDO::getReturnPoliceSfzh, updateReqVO.getReturnPoliceSfzh())
                .set(ConsularMeetingDO::getStatus, "4");
        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorSfzh())){
            lambdaUpdateWrapper.set(ConsularMeetingDO::getReturnOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(ConsularMeetingDO::getReturnOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorTime())){
            lambdaUpdateWrapper.set(ConsularMeetingDO::getReturnOperatorTime,new Date());
        }
        if("0".equals(updateReqVO.getDataSources())){
            ConsularMeetingDO consularMeetingDO = getById(updateReqVO.getId());
            WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(updateReqVO,WbInspectionResultsSaveReqVO.class);
            resultsSaveReqVO.setJgrybm(consularMeetingDO.getJgrybm());
            resultsSaveReqVO.setApplMeetingTime(consularMeetingDO.getApplyMeetingEndTime());
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"07","in","3",false);
        }
        return update(lambdaUpdateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean additionalRecording(ConsularMeetingSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        LambdaUpdateWrapper<ConsularMeetingDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ConsularMeetingDO::getId, updateReqVO.getId())
                .set(ConsularMeetingDO::getCheckInTime, updateReqVO.getCheckInTime())
                .set(ConsularMeetingDO::getCheckInPoliceSfzh, updateReqVO.getCheckInPoliceSfzh())
                .set(ConsularMeetingDO::getCheckInPolice, updateReqVO.getCheckInPolice())
                .set(ConsularMeetingDO::getEscortingPolice, updateReqVO.getEscortingPolice())
                .set(ConsularMeetingDO::getEscortingPoliceSfzh, updateReqVO.getEscortingPoliceSfzh())
                .set(ConsularMeetingDO::getEscortingTime, updateReqVO.getEscortingTime())
                .set(ConsularMeetingDO::getInspectionResult, updateReqVO.getInspectionResult())
                .set(ConsularMeetingDO::getProhibitedItems, updateReqVO.getProhibitedItems())
                .set(ConsularMeetingDO::getPhysicalExam, updateReqVO.getPhysicalExam())
                .set(ConsularMeetingDO::getAbnormalSituations, updateReqVO.getAbnormalSituations())
                .set(ConsularMeetingDO::getInspectionTime, updateReqVO.getInspectionTime())
                .set(ConsularMeetingDO::getInspector, updateReqVO.getInspector())
                .set(ConsularMeetingDO::getInspectorSfzh, updateReqVO.getInspectorSfzh())
                .set(ConsularMeetingDO::getMeetingStartTime, updateReqVO.getMeetingStartTime())
                .set(ConsularMeetingDO::getMeetingEndTime, updateReqVO.getMeetingEndTime())
                .set(ConsularMeetingDO::getReturnInspectorSfzh, updateReqVO.getReturnInspectorSfzh())
                .set(ConsularMeetingDO::getReturnInspector, updateReqVO.getReturnInspector())
                .set(ConsularMeetingDO::getReturnInspectionTime, updateReqVO.getReturnInspectionTime())
                .set(ConsularMeetingDO::getReturnInspectionResult, updateReqVO.getReturnInspectionResult())
                .set(ConsularMeetingDO::getReturnProhibitedItems, updateReqVO.getReturnProhibitedItems())
                .set(ConsularMeetingDO::getReturnPhysicalExam, updateReqVO.getReturnPhysicalExam())
                .set(ConsularMeetingDO::getReturnAbnormalSituations, updateReqVO.getReturnAbnormalSituations())
                .set(ConsularMeetingDO::getReturnTime, updateReqVO.getReturnTime())
                .set(ConsularMeetingDO::getReturnPolice, updateReqVO.getReturnPolice())
                .set(ConsularMeetingDO::getReturnPoliceSfzh, updateReqVO.getReturnPoliceSfzh())
                .set(ConsularMeetingDO::getStatus,"4");
        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorSfzh())){
            lambdaUpdateWrapper.set(ConsularMeetingDO::getEscortingOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(ConsularMeetingDO::getEscortingOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorTime())){
            lambdaUpdateWrapper.set(ConsularMeetingDO::getEscortingOperatorTime,new Date());
        }

        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorSfzh())){
            lambdaUpdateWrapper.set(ConsularMeetingDO::getReturnOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(ConsularMeetingDO::getReturnOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorTime())){
            lambdaUpdateWrapper.set(ConsularMeetingDO::getReturnOperatorTime,new Date());
        }
        if("0".equals(updateReqVO.getDataSources())){
            ConsularMeetingDO consularMeetingDO = getById(updateReqVO.getId());
            WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(updateReqVO,WbInspectionResultsSaveReqVO.class);
            resultsSaveReqVO.setJgrybm(consularMeetingDO.getJgrybm());
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"07","out","3",true);
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"07","in","3",true);
        }
        return update(lambdaUpdateWrapper);
    }

    @Override
    public PageResult<ConsularMeetingRespVO> getHistoryMeetingByJgrybm(String jgrybm, int pageNo, int pageSize) {

        Page<ConsularMeetingDO> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<ConsularMeetingDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(ConsularMeetingDO::getId,ConsularMeetingDO::getMeetingStartTime,
                ConsularMeetingDO::getMeetingEndTime,ConsularMeetingDO::getMeetingMethod);

        Page<ConsularMeetingDO> consularMeetingDOPage = page(page,lambdaQueryWrapper);
        List<ConsularMeetingRespVO> consularMeetingRespVOList = BeanUtils.toBean(consularMeetingDOPage.getRecords(),ConsularMeetingRespVO.class);
        if(CollectionUtil.isEmpty(consularMeetingRespVOList)){
            return new PageResult<>(new ArrayList<>() ,0L);
        }

        List<String> meetingPersonIdList = consularMeetingRespVOList.stream().map(ConsularMeetingRespVO::getId).collect(Collectors.toList());
        //查询会见人员
        LambdaQueryWrapper<ConsularMeetingPersonDO> personDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        personDOLambdaQueryWrapper.select(ConsularMeetingPersonDO::getXm,ConsularMeetingPersonDO::getConsularMeetingId)
                .in(ConsularMeetingPersonDO::getConsularMeetingId,meetingPersonIdList);
        List<ConsularMeetingPersonDO> personDOList = consularMeetingPersonService.list(personDOLambdaQueryWrapper);

        if(CollectionUtil.isNotEmpty(personDOList)){
            Map<String,List<ConsularMeetingPersonDO>> personMap = new HashMap<>();
            for(ConsularMeetingPersonDO personDO:personDOList){
                List<ConsularMeetingPersonDO> tempList = new ArrayList<>();
                if(personMap.containsKey(personDO.getConsularMeetingId())){
                    tempList = personMap.get(personDO.getConsularMeetingId());
                }
                tempList.add(personDO);
                personMap.put(personDO.getConsularMeetingId(),tempList);
            }

            for(ConsularMeetingRespVO consularMeetingRespVO:consularMeetingRespVOList){
                if(personMap.containsKey(consularMeetingRespVO.getId())){
                    List<String> xmList = personMap.get(consularMeetingRespVO.getId()).stream().map(ConsularMeetingPersonDO::getXm).collect(Collectors.toList());
                    consularMeetingRespVO.setMeetingPersonName(String.join("、",xmList));
                }
            }
        }

        return new PageResult<>(consularMeetingRespVOList ,consularMeetingDOPage.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean signIn(String id, String checkInTime) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
//        ArraignmentDO arraignmentDO = signInInit(id);
        LambdaUpdateWrapper<ConsularMeetingDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ConsularMeetingDO::getId, id).set(ConsularMeetingDO::getCheckInTime, DateUtil.parse(checkInTime,"yyyy-MM-dd HH:mm:ss"))
                .set(ConsularMeetingDO::getCheckInPoliceSfzh,sessionUser.getIdCard())
                .set(ConsularMeetingDO::getCheckInPolice, sessionUser.getName()).set(ConsularMeetingDO::getStatus,"1");
        return update(lambdaUpdateWrapper);
    }

    @Override
    public ConsularMeetingRespVO getConsularMeetingById(String id) {
        ConsularMeetingDO consularMeetingDO = getById(id);
        if (ObjectUtil.isEmpty(consularMeetingDO)) {
            throw new ServerException("实战平台-窗口业务-领事会见数据不存在");
        }
        ConsularMeetingRespVO consularMeetingRespVO = BeanUtils.toBean(consularMeetingDO, ConsularMeetingRespVO.class);

        if(ObjectUtil.isNotEmpty(consularMeetingRespVO.getMeetingDocumentsUrl())){
            consularMeetingRespVO.setMeetingDocumentsUrl(wbCommonService.getFile(consularMeetingRespVO.getMeetingDocumentsUrl()));
        }

        //获取同行人信息
        consularMeetingRespVO.setCompanionList(consularMeetingCompanionService.getConsularMeetingCompanionListByconsularMeetingId(id));

        //获取领事人员\办案人员
        List<ConsularMeetingPersonRespVO> personRespVOList = consularMeetingPersonService.getMeetingPersonListByConsularMeetingId(id);

        List<CasePersonnelRespVO> casePersonnelList = new ArrayList<>();
        List<ConsularRespVO> consularList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(personRespVOList)){
            for(ConsularMeetingPersonRespVO personRespVO:personRespVOList){
                if("0".equals(personRespVO.getRylx())){
                    ConsularRespVO consularRespVO = new ConsularRespVO();
                    consularRespVO.setId(personRespVO.getId());
                    consularRespVO.setName(personRespVO.getXm());
                    consularRespVO.setIdType(personRespVO.getZjlx());
                    consularRespVO.setIdNumber(personRespVO.getZjhm());
                    consularRespVO.setGender(personRespVO.getXb());
                    consularRespVO.setNationality(personRespVO.getGj());
                    consularRespVO.setContact(personRespVO.getLxfs());
                    consularRespVO.setWorkUnit(personRespVO.getWorkUnit());
                    consularRespVO.setImageUrl(personRespVO.getZpUrl());

                    consularList.add(consularRespVO);

                }else {
                    CasePersonnelRespVO casePersonnelRespVO = BeanUtils.toBean(personRespVO,CasePersonnelRespVO.class);
                    casePersonnelList.add(casePersonnelRespVO);
                }
            }
        }

        consularMeetingRespVO.setConsularList(consularList);
        consularMeetingRespVO.setCasePersonnelList(casePersonnelList);
        return consularMeetingRespVO;
    }
}
