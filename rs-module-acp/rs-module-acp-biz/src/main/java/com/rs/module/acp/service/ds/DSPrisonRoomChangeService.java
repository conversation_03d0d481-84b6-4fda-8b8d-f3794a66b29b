package com.rs.module.acp.service.ds;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.ds.vo.*;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsSaveReqVO;
import com.rs.module.acp.entity.db.DetainRegKssDO;
import com.rs.module.acp.entity.db.InRecordJdsDO;
import com.rs.module.acp.entity.db.InRecordJlsDO;
import com.rs.module.acp.entity.db.OutRecordKssDO;
import com.rs.module.acp.entity.ds.DSPrisonRoomChangeDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-数据固化-监所人员变动记录 Service 接口
 *
 * <AUTHOR>
 */
public interface DSPrisonRoomChangeService extends IBaseService<DSPrisonRoomChangeDO>{

    /**
     * 创建实战平台-数据固化-监所人员变动记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrisonRoomChange(@Valid PrisonRoomChangeSaveReqVO createReqVO);

    /**
     * 更新实战平台-数据固化-监所人员变动记录
     *
     * @param updateReqVO 更新信息
     */
    void updatePrisonRoomChange(@Valid PrisonRoomChangeSaveReqVO updateReqVO);

    /**
     * 删除实战平台-数据固化-监所人员变动记录
     *
     * @param id 编号
     */
    void deletePrisonRoomChange(String id);

    /**
     * 获得实战平台-数据固化-监所人员变动记录
     *
     * @param id 编号
     * @return 实战平台-数据固化-监所人员变动记录
     */
    DSPrisonRoomChangeDO getPrisonRoomChange(String id);

    /**
    * 获得实战平台-数据固化-监所人员变动记录分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-数据固化-监所人员变动记录分页
    */
    PageResult<DSPrisonRoomChangeDO> getPrisonRoomChangePage(PrisonRoomChangePageReqVO pageReqVO);

    /**
    * 获得实战平台-数据固化-监所人员变动记录列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-数据固化-监所人员变动记录列表
    */
    List<DSPrisonRoomChangeDO> getPrisonRoomChangeList(PrisonRoomChangeListReqVO listReqVO);

    /**
     * 获取指定人员在指定监室和时间段内的同监室人员列表
     *
     * @param queryReqVO 查询条件
     * @return 同监室人员列表
     */
    List<DSPrisonRoomChangeRespVO> getPrisonRoommates(DSPrisonRoommateQueryReqVO queryReqVO);

    String kssSave(DetainRegKssDO rsInfoDO) throws  Exception;

    String jdsSave(InRecordJdsDO rsInfoDO) throws  Exception;

    String jlsSave(InRecordJlsDO rsInfoDO) throws  Exception;

    void updatePrisonRoomChangeForOutPerson(InOutRecordsSaveReqVO outRecordsSaveReqVO) throws Exception;

    DSPrisonRoomChangeDO getPrisonLatestRoomChangeInfo(String jgrybm, String roomId);

    void insertPrisonRoomChangeForOutPerson(InOutRecordsSaveReqVO inRecordsSaveReqVO, String roomName, String batchId) throws Exception;

    void updatePrisonRoomChangeForKssOutPrison(OutRecordKssDO outRecordKssDO) throws  Exception;

    List<DSPrisonRoomChangeRespVO> getPrisonRoommatesGrouped(DSPrisonRoommateQueryReqVO queryReqVO);
}
