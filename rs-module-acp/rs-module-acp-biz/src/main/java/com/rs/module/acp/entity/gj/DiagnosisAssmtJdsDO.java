package com.rs.module.acp.entity.gj;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-诊断评估(戒毒所) DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_diagnosis_assmt_jds")
@KeySequence("acp_gj_diagnosis_assmt_jds_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_diagnosis_assmt_jds")
public class DiagnosisAssmtJdsDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 待评估类型
     */
    private String assmtType;
    /**
     * 评估周期
     */
    private String assmtPeriod;
    /**
     * 评估周期开始时间
     */
    private Date startTime;
    /**
     * 评估周期结束时间
     */
    private Date endTime;
    /**
     * 推送时间
     */
    private Date pushTime;
    /**
     * 推送岗位，多个逗号分割
     */
    private String pushJobPositions;
    /**
     * 评估内容值1
     */
    private String assmtContentValue1;
    /**
     * 评估内容值2
     */
    private String assmtContentValue2;
    /**
     * 评估内容值3
     */
    private String assmtContentValue3;
    /**
     * 评估内容值4
     */
    private String assmtContentValue4;
    /**
     * 评估内容值5
     */
    private String assmtContentValue5;
    /**
     * 评估内容值6
     */
    private String assmtContentValue6;
    /**
     * 评估内容值7
     */
    private String assmtContentValue7;
    /**
     * 评估内容值8
     */
    private String assmtContentValue8;
    /**
     * 评估内容值9
     */
    private String assmtContentValue9;
    /**
     * 评估内容值10
     */
    private String assmtContentValue10;
    /**
     * 评估内容值10
     */
    private String assmtContentTotalScore;
    /**
     * 评估结果1
     */
    private String assmtResultValue1;
    /**
     * 评估结果2
     */
    private String assmtResultValue2;
    /**
     * 评估结果3
     */
    private String assmtResultValue3;
    /**
     * 评估结果4
     */
    private String assmtResultValue4;
    /**
     * 评估结果5
     */
    private String assmtResultValue5;
    /**
     * 评估内容JSON
     */
    private String asstmContentJson;
    /**
     * 评估结果JSON
     */
    private String assmtResultJson;
    /**
     * 评估人身份证号
     */
    private String assmtUserSfzh;
    /**
     * 评估人姓名
     */
    private String assmtUser;
    /**
     * 评估时间
     */
    private String assmtTime;
    /**
     * 办理状态
     */
    private String status;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

}
