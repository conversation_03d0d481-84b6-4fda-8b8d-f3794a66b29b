package com.rs.module.acp.service.gj.edurehabcourses;

import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesRecordListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesRecordPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesRecordSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.EdurehabCoursesRecordDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.EdurehabCoursesRecordDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-教育康复课程记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EdurehabCoursesRecordServiceImpl extends BaseServiceImpl<EdurehabCoursesRecordDao, EdurehabCoursesRecordDO> implements EdurehabCoursesRecordService {

    @Resource
    private EdurehabCoursesRecordDao edurehabCoursesRecordDao;

    @Override
    public String createEdurehabCoursesRecord(EdurehabCoursesRecordSaveReqVO createReqVO) {
        // 插入
        EdurehabCoursesRecordDO edurehabCoursesRecord = BeanUtils.toBean(createReqVO, EdurehabCoursesRecordDO.class);
        edurehabCoursesRecordDao.insert(edurehabCoursesRecord);
        // 返回
        return edurehabCoursesRecord.getId();
    }

    @Override
    public void updateEdurehabCoursesRecord(EdurehabCoursesRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateEdurehabCoursesRecordExists(updateReqVO.getId());
        // 更新
        EdurehabCoursesRecordDO updateObj = BeanUtils.toBean(updateReqVO, EdurehabCoursesRecordDO.class);
        edurehabCoursesRecordDao.updateById(updateObj);
    }

    @Override
    public void deleteEdurehabCoursesRecord(String id) {
        // 校验存在
        validateEdurehabCoursesRecordExists(id);
        // 删除
        edurehabCoursesRecordDao.deleteById(id);
    }

    private void validateEdurehabCoursesRecordExists(String id) {
        if (edurehabCoursesRecordDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-教育康复课程记录数据不存在");
        }
    }

    @Override
    public EdurehabCoursesRecordDO getEdurehabCoursesRecord(String id) {
        return edurehabCoursesRecordDao.selectById(id);
    }

    @Override
    public PageResult<EdurehabCoursesRecordDO> getEdurehabCoursesRecordPage(EdurehabCoursesRecordPageReqVO pageReqVO) {
        return edurehabCoursesRecordDao.selectPage(pageReqVO);
    }

    @Override
    public List<EdurehabCoursesRecordDO> getEdurehabCoursesRecordList(EdurehabCoursesRecordListReqVO listReqVO) {
        return edurehabCoursesRecordDao.selectList(listReqVO);
    }


}
