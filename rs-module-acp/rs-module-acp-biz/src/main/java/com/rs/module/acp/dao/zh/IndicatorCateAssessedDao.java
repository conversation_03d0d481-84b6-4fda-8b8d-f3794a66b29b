package com.rs.module.acp.dao.zh;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateAssessedListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateAssessedPageReqVO;
import com.rs.module.acp.entity.zh.IndicatorCateAssessedDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 综合管理-绩效考核指标分类与被考评人关联 Dao
*
* <AUTHOR>
*/
@Mapper
public interface IndicatorCateAssessedDao extends IBaseDao<IndicatorCateAssessedDO> {


    default PageResult<IndicatorCateAssessedDO> selectPage(IndicatorCateAssessedPageReqVO reqVO) {
        Page<IndicatorCateAssessedDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<IndicatorCateAssessedDO> wrapper = new LambdaQueryWrapperX<IndicatorCateAssessedDO>()
            .eqIfPresent(IndicatorCateAssessedDO::getIndicatorCateId, reqVO.getIndicatorCateId())
            .eqIfPresent(IndicatorCateAssessedDO::getAssessedObjectType, reqVO.getAssessedObjectType())
            .eqIfPresent(IndicatorCateAssessedDO::getAssessedObjectId, reqVO.getAssessedObjectId())
            .likeIfPresent(IndicatorCateAssessedDO::getAssessedObjectName, reqVO.getAssessedObjectName())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(IndicatorCateAssessedDO::getAddTime);
        }
        Page<IndicatorCateAssessedDO> indicatorCateAssessedPage = selectPage(page, wrapper);
        return new PageResult<>(indicatorCateAssessedPage.getRecords(), indicatorCateAssessedPage.getTotal());
    }
    default List<IndicatorCateAssessedDO> selectList(IndicatorCateAssessedListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<IndicatorCateAssessedDO>()
            .eqIfPresent(IndicatorCateAssessedDO::getIndicatorCateId, reqVO.getIndicatorCateId())
            .eqIfPresent(IndicatorCateAssessedDO::getAssessedObjectType, reqVO.getAssessedObjectType())
            .eqIfPresent(IndicatorCateAssessedDO::getAssessedObjectId, reqVO.getAssessedObjectId())
            .likeIfPresent(IndicatorCateAssessedDO::getAssessedObjectName, reqVO.getAssessedObjectName())
        .orderByDesc(IndicatorCateAssessedDO::getAddTime));    }


    }
