package com.rs.module.acp.controller.admin.ds.vo.sjbs;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-数据固化-每日数据报送(拘留所) Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DailyDataSubmitJlsRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("固化日期")
    private String solidificationDate;
    @ApiModelProperty("固化开始日期")
    private String startDate;
    @ApiModelProperty("固化结束日期")
    private String endDate;
    @ApiModelProperty("被监管人员总数")
    private Integer bjgry;
    @ApiModelProperty("男性被监管人员总数")
    private Integer bjgryMale;
    @ApiModelProperty("女性被监管人员总数")
    private Integer bjgryFemale;
    @ApiModelProperty("未成年被监管人员总数")
    private Integer bjgryMinor;
    @ApiModelProperty("行政拘留")
    private Integer xzjl;
    @ApiModelProperty("司法拘留")
    private Integer sfjl;
    @ApiModelProperty("戒具使用总数")
    private Integer jgsy;
    @ApiModelProperty("精神异常总数")
    private Integer jsyc;
    @ApiModelProperty("谈话教育总数")
    private Integer thjy;
}
