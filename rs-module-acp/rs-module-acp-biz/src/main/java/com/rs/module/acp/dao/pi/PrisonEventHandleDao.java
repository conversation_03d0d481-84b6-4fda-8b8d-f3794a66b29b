package com.rs.module.acp.dao.pi;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventHandleListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventHandlePageReqVO;
import com.rs.module.acp.entity.pi.PrisonEventHandleDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-巡视管控-所情处置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PrisonEventHandleDao extends IBaseDao<PrisonEventHandleDO> {


    default PageResult<PrisonEventHandleDO> selectPage(PrisonEventHandlePageReqVO reqVO) {
        Page<PrisonEventHandleDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrisonEventHandleDO> wrapper = new LambdaQueryWrapperX<PrisonEventHandleDO>()
            .eqIfPresent(PrisonEventHandleDO::getEventId, reqVO.getEventId())
            .eqIfPresent(PrisonEventHandleDO::getFeedbackInfo, reqVO.getFeedbackInfo())
            .eqIfPresent(PrisonEventHandleDO::getHandleUserSfzh, reqVO.getHandleUserSfzh())
            .likeIfPresent(PrisonEventHandleDO::getHandleUserName, reqVO.getHandleUserName())
            .betweenIfPresent(PrisonEventHandleDO::getHandleTime, reqVO.getHandleTime())
            .eqIfPresent(PrisonEventHandleDO::getStatus, reqVO.getStatus())
            .eqIfPresent(PrisonEventHandleDO::getHandlePost, reqVO.getHandlePost())
            .eqIfPresent(PrisonEventHandleDO::getHandlePostCode, reqVO.getHandlePostCode())
            .eqIfPresent(PrisonEventHandleDO::getHistory, reqVO.getHistory())
            .eqIfPresent(PrisonEventHandleDO::getHandleType, reqVO.getHandleType())
            .eqIfPresent(PrisonEventHandleDO::getHandleInfo, reqVO.getHandleInfo())
            .eqIfPresent(PrisonEventHandleDO::getChangeContent, reqVO.getChangeContent())
            .eqIfPresent(PrisonEventHandleDO::getAttUrl, reqVO.getAttUrl())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PrisonEventHandleDO::getAddTime);
        }
        Page<PrisonEventHandleDO> prisonEventHandlePage = selectPage(page, wrapper);
        return new PageResult<>(prisonEventHandlePage.getRecords(), prisonEventHandlePage.getTotal());
    }
    default List<PrisonEventHandleDO> selectList(PrisonEventHandleListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrisonEventHandleDO>()
            .eqIfPresent(PrisonEventHandleDO::getEventId, reqVO.getEventId())
            .eqIfPresent(PrisonEventHandleDO::getFeedbackInfo, reqVO.getFeedbackInfo())
            .eqIfPresent(PrisonEventHandleDO::getHandleUserSfzh, reqVO.getHandleUserSfzh())
            .likeIfPresent(PrisonEventHandleDO::getHandleUserName, reqVO.getHandleUserName())
            .betweenIfPresent(PrisonEventHandleDO::getHandleTime, reqVO.getHandleTime())
            .eqIfPresent(PrisonEventHandleDO::getStatus, reqVO.getStatus())
            .eqIfPresent(PrisonEventHandleDO::getHandlePost, reqVO.getHandlePost())
            .eqIfPresent(PrisonEventHandleDO::getHandlePostCode, reqVO.getHandlePostCode())
            .eqIfPresent(PrisonEventHandleDO::getHistory, reqVO.getHistory())
            .eqIfPresent(PrisonEventHandleDO::getHandleType, reqVO.getHandleType())
            .eqIfPresent(PrisonEventHandleDO::getHandleInfo, reqVO.getHandleInfo())
            .eqIfPresent(PrisonEventHandleDO::getChangeContent, reqVO.getChangeContent())
            .eqIfPresent(PrisonEventHandleDO::getAttUrl, reqVO.getAttUrl())
        .orderByDesc(PrisonEventHandleDO::getAddTime));    }


    }
