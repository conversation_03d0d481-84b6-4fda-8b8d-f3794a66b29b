package com.rs.module.acp.dao.wb;

import java.util.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.LawyerDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 实战平台-窗口业务-律师 Dao
*
* <AUTHOR>
*/
@Mapper
public interface LawyerDao extends IBaseDao<LawyerDO> {


    default PageResult<LawyerDO> selectPage(LawyerPageReqVO reqVO) {
        Page<LawyerDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<LawyerDO> wrapper = new LambdaQueryWrapperX<LawyerDO>()
            .eqIfPresent(LawyerDO::getXm, reqVO.getXm())
            .eqIfPresent(LawyerDO::getXb, reqVO.getXb())
            .eqIfPresent(LawyerDO::getZjlx, reqVO.getZjlx())
            .eqIfPresent(LawyerDO::getZjhm, reqVO.getZjhm())
            .eqIfPresent(LawyerDO::getLxfs, reqVO.getLxfs())
            .eqIfPresent(LawyerDO::getZyzhm, reqVO.getZyzhm())
            .eqIfPresent(LawyerDO::getKsZyzyxq, reqVO.getKsZyzyxq())
            .eqIfPresent(LawyerDO::getJsZyzyxq, reqVO.getJsZyzyxq())
            .eqIfPresent(LawyerDO::getLsdw, reqVO.getLsdw())
            .eqIfPresent(LawyerDO::getZpUrl, reqVO.getZpUrl())
            .eqIfPresent(LawyerDO::getZyzsUrl, reqVO.getZyzsUrl())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(LawyerDO::getAddTime);
        }
        Page<LawyerDO> lawyerPage = selectPage(page, wrapper);
        return new PageResult<>(lawyerPage.getRecords(), lawyerPage.getTotal());
    }
    default List<LawyerDO> selectList(LawyerListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<LawyerDO>()
            .eqIfPresent(LawyerDO::getXm, reqVO.getXm())
            .eqIfPresent(LawyerDO::getXb, reqVO.getXb())
            .eqIfPresent(LawyerDO::getZjlx, reqVO.getZjlx())
            .eqIfPresent(LawyerDO::getZjhm, reqVO.getZjhm())
            .eqIfPresent(LawyerDO::getLxfs, reqVO.getLxfs())
            .eqIfPresent(LawyerDO::getZyzhm, reqVO.getZyzhm())
            .eqIfPresent(LawyerDO::getKsZyzyxq, reqVO.getKsZyzyxq())
            .eqIfPresent(LawyerDO::getJsZyzyxq, reqVO.getJsZyzyxq())
            .eqIfPresent(LawyerDO::getLsdw, reqVO.getLsdw())
            .eqIfPresent(LawyerDO::getZpUrl, reqVO.getZpUrl())
            .eqIfPresent(LawyerDO::getZyzsUrl, reqVO.getZyzsUrl())
        .orderByDesc(LawyerDO::getAddTime));    }


    IPage<LawyerSelectVO> getLawyerPageByJgrybm(@Param("page") Page<LawyerSelectVO> page,@Param("pageReqVO") LawyerPageReqVO pageReqVO);


    }
