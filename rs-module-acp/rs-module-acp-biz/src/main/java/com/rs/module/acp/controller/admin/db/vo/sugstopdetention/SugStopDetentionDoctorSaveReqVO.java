package com.rs.module.acp.controller.admin.db.vo.sugstopdetention;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-建议停拘登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SugStopDetentionDoctorSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty(value = "保存类型 0 保存,1 提交",hidden = true)
    private String saveType;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("身高")
    @NotNull(message = "身高不能为空")
    private Integer sg;

    @ApiModelProperty("体重")
    @NotNull(message = "体重不能为空")
    private Integer tz;

    @ApiModelProperty("足长")
    @NotNull(message = "足长不能为空")
    private Integer zc;

    @ApiModelProperty("体温")
    @NotNull(message = "体温不能为空")
    private Integer tw;

    @ApiModelProperty("血压")
    @NotNull(message = "血压不能为空")
    private Integer xy;

    @ApiModelProperty("脉搏")
    private Integer mb;

    @ApiModelProperty("心肺")
    @NotEmpty(message = "心肺不能为空")
    private String xf;

    @ApiModelProperty("心率")
    @NotNull(message = "心率不能为空")
    private Integer xl;

    @ApiModelProperty("口音")
    private String ky;

    @ApiModelProperty("血型")
    private String xx;

    @ApiModelProperty("体型")
    private String tx;

    @ApiModelProperty("脸型")
    private String lx;

    @ApiModelProperty("语言表达能力")
    @NotEmpty(message = "语言表达能力不能为空")
    private String yybdnl;

    @ApiModelProperty("行走状况")
    @NotEmpty(message = "行走状况不能为空")
    private String xzzk;

    @ApiModelProperty("健康状况")
    @NotEmpty(message = "健康状况不能为空")
    private String jkzk;

    @ApiModelProperty("聋哑人")
    @NotEmpty(message = "聋哑人不能为空")
    private String lyr;

    @ApiModelProperty("不能自理")
    @NotEmpty(message = "不能自理不能为空")
    private String bnzl;

    @ApiModelProperty("过敏史")
    private String gms;

    @ApiModelProperty("自述症状")
    @NotEmpty(message = "自述症状不能为空")
    private String zszzz;

    @ApiModelProperty("肺脏")
    @NotEmpty(message = "肺脏不能为空")
    private String fz;

    @ApiModelProperty("腹部")
    @NotEmpty(message = "腹部不能为空")
    private String fb;

    @ApiModelProperty("体表特殊标志")
    @NotEmpty(message = "体表特殊标志不能为空")
    private String tbtbsbz;

    @ApiModelProperty("既往病史")
    @NotEmpty(message = "既往病史不能为空")
    private String jwbs;

    @ApiModelProperty("有何传染病")
    private String yhcrb;

    @ApiModelProperty("本人病史")
    private String brbs;

    @ApiModelProperty("家庭病史")
    @NotEmpty(message = "家庭病史不能为空")
    private String jtbs;

    @ApiModelProperty("血常规")
    @NotEmpty(message = "血常规不能为空")
    private String xcg;

    @ApiModelProperty("心电图")
    @NotEmpty(message = "心电图不能为空")
    private String xdt;

    @ApiModelProperty("B超")
    @NotEmpty(message = "B超不能为空")
    private String bc;

    @ApiModelProperty("胸片")
    @NotEmpty(message = "胸片不能为空")
    private String xp;

    @ApiModelProperty("建议停止执行拘留原因")
    @NotEmpty(message = "建议停止执行拘留原因不能为空")
    private String jytzzxjjlyy;

    @ApiModelProperty("医生意见")
    @NotEmpty(message = "医生意见不能为空")
    private String ysyj;

    @ApiModelProperty("诊断证明")
    private String zdzm;

    @ApiModelProperty("住院治疗证明文件")
    private String zyzlzmwj;

    @ApiModelProperty("鉴定结论")
    private String jdlj;

    @ApiModelProperty("其他")
    private String qt;

    @ApiModelProperty("经办医生身份证号")
    private String jbyssfzh;

    @ApiModelProperty("经办医生")
    private String jbys;

    @ApiModelProperty("经办时间")
    private Date jbsj;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

}
