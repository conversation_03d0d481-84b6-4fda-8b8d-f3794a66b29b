package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.rs.module.acp.entity.wb.LawyerPrisonerDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.LawyerDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.LawyerDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-律师 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LawyerServiceImpl extends BaseServiceImpl<LawyerDao, LawyerDO> implements LawyerService {

    @Resource
    private LawyerDao lawyerDao;

    @Autowired
    private LawyerPrisonerService lawyerPrisonerService;

    @Autowired
    private WbCommonService wbCommonService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createLawyer(LawyerSaveReqVO createReqVO) {

        //根据身份证号，判断律师是否已存在
        if(ObjectUtil.isNotEmpty(getOne(new LambdaQueryWrapper<LawyerDO>()
                .eq(LawyerDO::getZjhm,createReqVO.getZjhm())))){
            throw new ServerException("该律师已存在，请勿重复添加！");
        }

        /**同一个监管人员关联【委托中】状态下的正式律师最多只能有两位；当新增委托关系时；
        需判断所选被监管人员是否已经关联了两位委托中正式律师；若是，则新增委托失败；
        被监管人员支持关联多名律师助理，不做数量限制**/
//        if("0".equals(createReqVO.getLslx())){
//            lawyerPrisonerService.checkLawyerCommission(createReqVO.getPrisonerList());
//        }

        if(ObjectUtil.isNotEmpty(createReqVO.getZyzsUrl())){
            String zyzsUrl = wbCommonService.saveFile(null,createReqVO.getZyzsUrl());
            createReqVO.setZyzsUrl(zyzsUrl);
        }

        // 插入
        LawyerDO lawyer = BeanUtils.toBean(createReqVO, LawyerDO.class);
        lawyer.setId(StringUtil.getGuid32());
        lawyerPrisonerService.savePrisonerList(lawyer.getId(),createReqVO.getPrisonerList());
        lawyerDao.insert(lawyer);
        // 返回
        return lawyer.getId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLawyer(LawyerSaveReqVO updateReqVO) {

        // 校验存在
        LawyerDO lawyerDO = getById(updateReqVO.getId());
        if(ObjectUtil.isEmpty(lawyerDO)){
            throw new ServerException("实战平台-窗口业务-律师数据不存在");
        }

        /**同一个监管人员关联【委托中】状态下的正式律师最多只能有两位；当新增委托关系时；
         需判断所选被监管人员是否已经关联了两位委托中正式律师；若是，则新增委托失败；
         被监管人员支持关联多名律师助理，不做数量限制**/
//        lawyerPrisonerService.checkLawyerCommission(updateReqVO.getPrisonerList());

        if(ObjectUtil.isNotEmpty(updateReqVO.getZyzsUrl())){
            String zyzsUrl = wbCommonService.saveFile(ObjectUtil.isNotEmpty(lawyerDO.getZyzsUrl())?lawyerDO.getZyzsUrl():null,updateReqVO.getZyzsUrl());
            updateReqVO.setZyzsUrl(zyzsUrl);
        }

        // 更新
        LawyerDO updateObj = BeanUtils.toBean(updateReqVO, LawyerDO.class);
        lawyerPrisonerService.savePrisonerList(updateObj.getId(),updateReqVO.getPrisonerList());
        lawyerDao.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLawyer(String id) {
        // 校验存在
        LawyerDO lawyerDO = getById(id);
        if(ObjectUtil.isEmpty(lawyerDO)){
            throw new ServerException("实战平台-窗口业务-律师数据不存在");
        }
        // 删除
        lawyerDao.deleteById(id);
        lawyerPrisonerService.remove(new LambdaQueryWrapper<LawyerPrisonerDO>().eq(LawyerPrisonerDO::getLawyerId,id));
        if(ObjectUtil.isNotEmpty(lawyerDO.getZyzsUrl())){
            wbCommonService.deleteFile(lawyerDO.getZyzsUrl());
        }
    }

    private void validateLawyerExists(String id) {
        if (lawyerDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-律师数据不存在");
        }
    }

    @Override
    public LawyerDO getLawyer(String id) {
        LawyerDO lawyerDO = lawyerDao.selectById(id);
        if(ObjectUtil.isNotEmpty(lawyerDO.getZyzsUrl())){
            lawyerDO.setZyzsUrl(wbCommonService.getFile(lawyerDO.getZyzsUrl()));
        }
        return lawyerDO;
    }

    @Override
    public PageResult<LawyerDO> getLawyerPage(LawyerPageReqVO pageReqVO) {
        return lawyerDao.selectPage(pageReqVO);
    }

    @Override
    public List<LawyerDO> getLawyerList(LawyerListReqVO listReqVO) {
        return lawyerDao.selectList(listReqVO);
    }

    @Override
    public List<LawyerSelectVO> getLawyerListByJgrybm(String jgrybm) {
        MPJLambdaWrapper<LawyerDO> lambdaWrapper = new MPJLambdaWrapper<>();
        lambdaWrapper.select(LawyerDO::getId,LawyerDO::getXm,LawyerDO::getXb,LawyerDO::getZjhm,
                LawyerDO::getLxfs,LawyerDO::getLslx,LawyerDO::getZyzhm,LawyerDO::getLsdw,
                LawyerDO::getZyzsUrl,LawyerDO::getZpUrl)
                .select(LawyerPrisonerDO::getEntrustStage,LawyerPrisonerDO::getEntrustType,
                        LawyerPrisonerDO::getPrincipal,LawyerPrisonerDO::getPrincipalId,
                        LawyerPrisonerDO::getPowerOfAttorneyType,LawyerPrisonerDO::getPowerOfAttorneyUrl,
                        LawyerPrisonerDO::getLetterNumber,LawyerPrisonerDO::getAddTime)
                .leftJoin(LawyerPrisonerDO.class,on->on.eq(LawyerPrisonerDO::getLawyerId,LawyerDO::getId))
                .eq(LawyerPrisonerDO::getStatus,"1").eq(LawyerPrisonerDO::getJgrybm,jgrybm);

        List<LawyerSelectVO> lawyerSelectVOList = lawyerDao.selectJoinList(LawyerSelectVO.class,lambdaWrapper);
        if(CollectionUtil.isNotEmpty(lawyerSelectVOList)){
            for(LawyerSelectVO lawyerSelectVO:lawyerSelectVOList){
                if(ObjectUtil.isNotEmpty(lawyerSelectVO.getZyzsUrl())){
                    lawyerSelectVO.setZyzsUrl(wbCommonService.getFile(lawyerSelectVO.getZyzsUrl()));
                }

                if(ObjectUtil.isNotEmpty(lawyerSelectVO.getAddTime())){
                    lawyerSelectVO.setEntrustTime(DateUtil.format(lawyerSelectVO.getAddTime(),"yyyy-MM-dd HH:mm:ss")+"~案件结束");
                }

            }
        }

        return lawyerSelectVOList;
    }

    @Override
    public boolean checkJgryAssociation(String jgrybm) {
        JSONObject temp = lawyerPrisonerService.checkJgryAssociation(jgrybm);
        return temp.getInteger("num") > 1?false:true;
    }



    @Override
    public PageResult<LawyerSelectVO> getLawyerPageByJgrybm(LawyerPageReqVO pageReqVO) {
        Page<LawyerSelectVO> page = new Page<>(pageReqVO.getPageNo(),pageReqVO.getPageSize());
        pageReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        IPage<LawyerSelectVO> iPage = lawyerDao.getLawyerPageByJgrybm(page,pageReqVO);

        if(CollectionUtil.isNotEmpty(iPage.getRecords())){
            for(LawyerSelectVO lawyerSelectVO:iPage.getRecords()){
                if(ObjectUtil.isNotEmpty(lawyerSelectVO.getZyzsUrl())){
                    lawyerSelectVO.setZyzsUrl(wbCommonService.getFile(lawyerSelectVO.getZyzsUrl()));
                }
            }
        }

        return new PageResult<>(iPage.getRecords(),iPage.getTotal());
    }
}
