package com.rs.module.acp.controller.admin.gj.vo.equipment;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-械具使用 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EquipmentUseRespVO extends BaseVO implements TransPojo {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("使用情形")
    @Trans(type = TransType.DICTIONARY,key = "ZD_GJ_EQUIPMENT_SCENE")
    private String useSituation;

    @ApiModelProperty("使用天数")
    private String useDays;

    @ApiModelProperty("械具类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_GJ_EQUIPMENT_TYPE")
    private String punishmentToolType;

    @ApiModelProperty("使用械具的理由")
    private String useReason;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("登记审批人身份证号")
    private String regApproverSfzh;

    @ApiModelProperty("登记审批人姓名")
    private String regApproverXm;

    @ApiModelProperty("登记审批时间")
    private Date regApproverTime;

    @ApiModelProperty("登记审批结果")
    private String regApprovalResult;

    @ApiModelProperty("登记领导签名")
    private String regApprovalAutograph;

    @ApiModelProperty("登记领导签名日期")
    private Date regApprovalAutographTime;

    @ApiModelProperty("登记审核意见")
    private String regApprovalComments;

    @ApiModelProperty("开始日期")
    private Date startTime;

    @ApiModelProperty("结束日期")
    private Date endTime;

    @ApiModelProperty("执行情况")
    private String executeSituation;

    @ApiModelProperty("使用登记时间")
    private Date useRegTime;

    @ApiModelProperty("使用登记人")
    private String useRegUser;

    //@ApiModelProperty("延长理由")
    //private String extendReason;
    //
    //@ApiModelProperty("延长天数")
    //private Short extendDay;
    //
    //@ApiModelProperty("延长时间")
    //private Date extendTime;

    //@ApiModelProperty("延长登记人")
    //private String extendRegUser;
    //
    //@ApiModelProperty("延长执行情况")
    //private String extendExecuteSituation;
    //
    //@ApiModelProperty("延长审批人身份证号")
    //private String extendApproverSfzh;
    //
    //@ApiModelProperty("延长审批人姓名")
    //private String extendApproverXm;
    //
    //@ApiModelProperty("延长审批时间")
    //private Date extendApproverTime;
    //
    //@ApiModelProperty("延长审批结果")
    //private String extendApprovalResult;
    //
    //@ApiModelProperty("延长领导签名")
    //private String extendApprovalAutograph;
    //
    //@ApiModelProperty("延长领导签名日期")
    //private Date extendApprovalAutographTime;
    //
    //@ApiModelProperty("延长审核意见")
    //private String extendApprovalComments;

    private List<EquipmentUseExtendRespVO> extendRespList;

    @ApiModelProperty("解除登记理由")
    private String removeReason;

    @ApiModelProperty("解除日期")
    private Date removeTime;

    @ApiModelProperty("解除登记人")
    private String removeRegUser;

    @ApiModelProperty("解除执行情况")
    private String removeExecuteSituation;

    @ApiModelProperty("是否提前解除")
    private Short isRemove;

    @ApiModelProperty("提前解除理由")
    private String advanceRemoveReason;

    @ApiModelProperty("解除审批人身份证号")
    private String removeApproverSfzh;

    @ApiModelProperty("解除审批人姓名")
    private String removeApproverXm;

    @ApiModelProperty("解除审批时间")
    private Date removeApproverTime;

    @ApiModelProperty("解除审批结果")
    private String removeApprovalResult;

    @ApiModelProperty("解除领导签名")
    private String removeApprovalAutograph;

    @ApiModelProperty("解除领导签名日期")
    private Date removeApprovalAutographTime;

    @ApiModelProperty("解除审核意见")
    private String removeApprovalComments;

    @ApiModelProperty("是否关联惩罚 1是0否")
    private Integer isAssociatedPunishment;

    @ApiModelProperty("惩罚措施多个逗号分割")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GJCFNR")
    private String punishmentMeasures;

    @ApiModelProperty("是否临时固定")
    private Integer isTempFixation;

    @ApiModelProperty("固定天数")
    private String fixationDays;

    @ApiModelProperty("使用呈批人")
    private String addUser;

    @ApiModelProperty("使用呈批人姓名")
    private String addUserName;

    @ApiModelProperty("使用呈批时间")
    private Date addTime;

    @ApiModelProperty("实际开始时间")
    private Date actualStartTime;

    @ApiModelProperty("实际结束时间")
    private Date actualEndTime;


}
