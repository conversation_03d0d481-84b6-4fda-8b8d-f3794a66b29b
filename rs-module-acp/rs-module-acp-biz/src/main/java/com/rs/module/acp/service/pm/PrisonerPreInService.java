package com.rs.module.acp.service.pm;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.db.vo.DetainRegKssRespVO;
import com.rs.module.acp.controller.admin.db.vo.InRecordJlsPreRespVO;
import com.rs.module.acp.controller.admin.db.vo.InRecordJlsRespVO;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.PrisonerPreInDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 嫌疑人（待入所）信息 Service 接口
 *
 * <AUTHOR>
 */
public interface PrisonerPreInService extends IBaseService<PrisonerPreInDO>{

    /**
     * 创建嫌疑人（待入所）信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrisonerPreIn(@Valid PrisonerPreInSaveReqVO createReqVO);

    /**
     * 更新嫌疑人（待入所）信息
     *
     * @param updateReqVO 更新信息
     */
    void updatePrisonerPreIn(@Valid PrisonerPreInSaveReqVO updateReqVO);

    /**
     * 删除嫌疑人（待入所）信息
     *
     * @param id 编号
     */
    void deletePrisonerPreIn(String id);

    /**
     * 获得嫌疑人（待入所）信息
     *
     * @param id 编号
     * @return 嫌疑人（待入所）信息
     */
    PrisonerPreInDO getPrisonerPreIn(String id);

    /**
    * 获得嫌疑人（待入所）信息分页
    *
    * @param pageReqVO 分页查询
    * @return 嫌疑人（待入所）信息分页
    */
    PageResult<PrisonerPreInDO> getPrisonerPreInPage(PrisonerPreInPageReqVO pageReqVO);

    /**
    * 获得嫌疑人（待入所）信息列表
    *
    * @param listReqVO 查询条件
    * @return 嫌疑人（待入所）信息列表
    */
    List<PrisonerPreInDO> getPrisonerPreInList(PrisonerPreInListReqVO listReqVO);


    DetainRegKssRespVO getPrisonerPreInByIdCardAndPrisonCode(PrisonerPreInListReqVO listReqVO);

    InRecordJlsPreRespVO getPrisonerPreInByZjhmJls(PrisonerPreInListReqVO listReqVO);
}
