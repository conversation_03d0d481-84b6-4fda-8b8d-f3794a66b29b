package com.rs.module.acp.controller.admin.gj.vo.riskassmt;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-风险评估 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GjRiskAssmtRespVO extends BaseVO implements TransPojo{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("评估类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GJ_FXPG_TYPE")
    private String riskType;

    @ApiModelProperty("原风险等级")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GJ_FXPG_LEVEL")
    private String oldRiskLevel;

    @Trans(type = TransType.DICTIONARY, key = "ZD_GJ_FXPG_LEVEL")
    @ApiModelProperty("评估风险等级")
    private String riskLevel;

    @ApiModelProperty("评估理由")
    private String assmtReason;

    @ApiModelProperty("评估理由-中文")
    private String assmtReasonName;

    @ApiModelProperty("具体评估理由")
    private String specificAssmtReason;

    @ApiModelProperty("报备状态（字典：ZD_XXBB_BBZT）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_FXPG_STATUS")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审批结果-中文")
    private String approvalResultName;

    @ApiModelProperty("领导签名")
    private String approvalAutograph;

    @ApiModelProperty("领导签名日期")
    private Date approvalAutographTime;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("拘留所- 严管类别 字典 ZD_GJ_FXPG_STRICTLY ")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GJ_FXPG_STRICTLY")
    private String strictlyRegulatedCategory;

    @ApiModelProperty("登记人")
    private String addUserName;

    @ApiModelProperty("登记时间")
    private Date addTime;

}
