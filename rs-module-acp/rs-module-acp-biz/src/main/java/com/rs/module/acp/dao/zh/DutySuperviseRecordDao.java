package com.rs.module.acp.dao.zh;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.zh.DutySuperviseRecordDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.zh.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Select;

/**
* 综合管理-值班管理-值班督导记录 Dao
*
* <AUTHOR>
*/
@Mapper
public interface DutySuperviseRecordDao extends IBaseDao<DutySuperviseRecordDO> {


    default PageResult<DutySuperviseRecordDO> selectPage(DutySuperviseRecordPageReqVO reqVO) {
        Page<DutySuperviseRecordDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<DutySuperviseRecordDO> wrapper = new LambdaQueryWrapperX<DutySuperviseRecordDO>()
            .eqIfPresent(DutySuperviseRecordDO::getStaffDutyTemplateId, reqVO.getStaffDutyTemplateId())
            .eqIfPresent(DutySuperviseRecordDO::getStaffDutyPostId, reqVO.getStaffDutyPostId())
            .eqIfPresent(DutySuperviseRecordDO::getStaffDutyRoleId, reqVO.getStaffDutyRoleId())
            .eqIfPresent(DutySuperviseRecordDO::getDutyDate, reqVO.getDutyDate())
            .eqIfPresent(DutySuperviseRecordDO::getSigninValidMode, reqVO.getSigninValidMode())
            .eqIfPresent(DutySuperviseRecordDO::getSigninStatus, reqVO.getSigninStatus())
            .eqIfPresent(DutySuperviseRecordDO::getSigninTime, reqVO.getSigninTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(DutySuperviseRecordDO::getAddTime);
        }
        Page<DutySuperviseRecordDO> dutySuperviseRecordPage = selectPage(page, wrapper);
        return new PageResult<>(dutySuperviseRecordPage.getRecords(), dutySuperviseRecordPage.getTotal());
    }
    default List<DutySuperviseRecordDO> selectList(DutySuperviseRecordListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DutySuperviseRecordDO>()
            .eqIfPresent(DutySuperviseRecordDO::getStaffDutyTemplateId, reqVO.getStaffDutyTemplateId())
            .eqIfPresent(DutySuperviseRecordDO::getStaffDutyPostId, reqVO.getStaffDutyPostId())
            .eqIfPresent(DutySuperviseRecordDO::getStaffDutyRoleId, reqVO.getStaffDutyRoleId())
            .eqIfPresent(DutySuperviseRecordDO::getDutyDate, reqVO.getDutyDate())
            .eqIfPresent(DutySuperviseRecordDO::getSigninValidMode, reqVO.getSigninValidMode())
            .eqIfPresent(DutySuperviseRecordDO::getSigninStatus, reqVO.getSigninStatus())
            .eqIfPresent(DutySuperviseRecordDO::getSigninTime, reqVO.getSigninTime())
        .orderByDesc(DutySuperviseRecordDO::getAddTime));    }


    List<AcpZhStaffDutyTemplateCurrentInfo> getCurrentDutyInfo(String orgCode);

    @Select("SELECT police_id AS policeId, police_name AS policeName, police_type AS policeType, is_absent AS isAbsent, absent_reason AS absentReason FROM acp_zh_staff_duty_record_person WHERE record_id = #{recordId}")
    List<StaffDutyRecordPersonVO> getDutyRecordPersonsByRecordId(String recordId);
}
