package com.rs.module.acp.controller.admin.gj.vo.face2face;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-管教业务-面对面配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FaceToFaceConfigSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("检查项目")
    @NotEmpty(message = "检查项目不能为空")
    private String checkItem;

    @ApiModelProperty("是否禁用")
    @NotNull(message = "是否禁用不能为空")
    private Short isDisabled;

}
