package com.rs.module.acp.service.gj.groupinout;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.gj.vo.groupinout.GroupInOutRespVO;
import com.rs.module.acp.controller.admin.gj.vo.groupinout.GroupInOutSaveReqVO;
import com.rs.module.acp.controller.app.gj.vo.groupinout.AppGroupInOutStatisticVO;
import com.rs.module.acp.entity.gj.GroupInOutDO;

/**
 * 实战平台-管教业务-集体出入 Service 接口
 *
 * <AUTHOR>
 */
public interface GroupInOutService extends IBaseService<GroupInOutDO>{

    /**
     * 创建实战平台-管教业务-集体出入
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createGroupInOut(GroupInOutSaveReqVO createReqVO);

    /**
     * 删除实战平台-管教业务-集体出入
     *
     * @param id 编号
     */
    void deleteGroupInOut(String id);

    /**
     * 获得实战平台-管教业务-集体出入
     *
     * @param id 编号
     * @return 实战平台-管教业务-集体出入
     */
    GroupInOutDO getGroupInOut(String id);

    /**
     * 获得实战平台-管教业务-集体出入的响应对象
     * @param id
     * @return
     */
    GroupInOutRespVO getGroupInOutRespVO(String id);

    /**
     * 获得实战平台-管教业务-集体出入的统计对象
     * @param orgCode
     * @param roomId
     * @return
     */
    AppGroupInOutStatisticVO getGroupInOutStatisticVO(String orgCode, String roomId);
}
