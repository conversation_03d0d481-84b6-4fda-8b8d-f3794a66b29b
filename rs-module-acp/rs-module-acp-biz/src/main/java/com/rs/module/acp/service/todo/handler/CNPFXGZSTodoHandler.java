package com.rs.module.acp.service.todo.handler;

import com.rs.module.acp.service.todo.TodoBusinessType;
import com.rs.module.acp.service.todo.TodoContext;
import com.rs.module.acp.service.todo.TodoHandler;
import com.rs.module.ihc.controller.admin.ipm.vo.PrescribeExecuteFxgzTodoReqVO;
import com.rs.module.ihc.controller.admin.ipm.vo.PrescribeExecuteFytzRespVO;
import com.rs.module.ihc.dao.ipm.PrescribeExecuteDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CNPFXGZSTodoHandler implements TodoHandler<List<PrescribeExecuteFytzRespVO>> {

    @Autowired
    private PrescribeExecuteDao prescribeExecuteDao;


    @Override
    public boolean canHandle(TodoContext context) {
        return context.getJgrybm() != null && TodoBusinessType.CNP_MDHJQR.equals(context.getTodoType());
    }

    @Override
    public List<PrescribeExecuteFytzRespVO> handle(TodoContext context) {
        PrescribeExecuteFxgzTodoReqVO reqVO = new PrescribeExecuteFxgzTodoReqVO();
        reqVO.setJgrybm(context.getJgrybm());
        List<PrescribeExecuteFytzRespVO> prescribeExecuteFytzRespVOS = prescribeExecuteDao.selectFxgzsTodo(reqVO);
        return prescribeExecuteFytzRespVOS;
    }

    @Override
    public TodoBusinessType getSupportedBusinessType() {
        return TodoBusinessType.CNP_FXGZS;
    }

    // 新增方法：查询实际数量
    @Override
    public int handleCount(TodoContext context) {
        PrescribeExecuteFxgzTodoReqVO reqVO = new PrescribeExecuteFxgzTodoReqVO();
        reqVO.setJgrybm(context.getJgrybm());
        List<PrescribeExecuteFytzRespVO> prescribeExecuteFytzRespVOS = prescribeExecuteDao.selectFxgzsTodo(reqVO);
        return prescribeExecuteFytzRespVOS.size();
    }


}
