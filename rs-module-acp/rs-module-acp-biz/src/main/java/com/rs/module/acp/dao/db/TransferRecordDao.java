package com.rs.module.acp.dao.db;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.db.TransferPrisonerInfoDO;
import com.rs.module.acp.entity.db.TransferRecordDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-羁押业务-转所登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface TransferRecordDao extends IBaseDao<TransferRecordDO> {


    default PageResult<TransferRecordDO> selectPage(TransferRecordPageReqVO reqVO) {
        Page<TransferRecordDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<TransferRecordDO> wrapper = new LambdaQueryWrapperX<TransferRecordDO>()
            .eqIfPresent(TransferRecordDO::getZsrq, reqVO.getZsrq())
            .eqIfPresent(TransferRecordDO::getZsrs, reqVO.getZsrs())
            .eqIfPresent(TransferRecordDO::getZslx, reqVO.getZslx())
            .eqIfPresent(TransferRecordDO::getZwdw, reqVO.getZwdw())
            .eqIfPresent(TransferRecordDO::getYjfa, reqVO.getYjfa())
            .eqIfPresent(TransferRecordDO::getYjmjsfzh, reqVO.getYjmjsfzh())
            .eqIfPresent(TransferRecordDO::getYjmj, reqVO.getYjmj())
            .eqIfPresent(TransferRecordDO::getJbrsfzh, reqVO.getJbrsfzh())
            .eqIfPresent(TransferRecordDO::getJbr, reqVO.getJbr())
            .eqIfPresent(TransferRecordDO::getJbsj, reqVO.getJbsj())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(TransferRecordDO::getAddTime);
        }
        Page<TransferRecordDO> transferRecordPage = selectPage(page, wrapper);
        return new PageResult<>(transferRecordPage.getRecords(), transferRecordPage.getTotal());
    }
    default List<TransferRecordDO> selectList(TransferRecordListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TransferRecordDO>()
            .eqIfPresent(TransferRecordDO::getZsrq, reqVO.getZsrq())
            .eqIfPresent(TransferRecordDO::getZsrs, reqVO.getZsrs())
            .eqIfPresent(TransferRecordDO::getZslx, reqVO.getZslx())
            .eqIfPresent(TransferRecordDO::getZwdw, reqVO.getZwdw())
            .eqIfPresent(TransferRecordDO::getYjfa, reqVO.getYjfa())
            .eqIfPresent(TransferRecordDO::getYjmjsfzh, reqVO.getYjmjsfzh())
            .eqIfPresent(TransferRecordDO::getYjmj, reqVO.getYjmj())
            .eqIfPresent(TransferRecordDO::getJbrsfzh, reqVO.getJbrsfzh())
            .eqIfPresent(TransferRecordDO::getJbr, reqVO.getJbr())
            .eqIfPresent(TransferRecordDO::getJbsj, reqVO.getJbsj())
        .orderByDesc(TransferRecordDO::getAddTime));    }


    List<TransferPrisonerInfoDO> getPrisonersByTransferRecordId(String id);
}
