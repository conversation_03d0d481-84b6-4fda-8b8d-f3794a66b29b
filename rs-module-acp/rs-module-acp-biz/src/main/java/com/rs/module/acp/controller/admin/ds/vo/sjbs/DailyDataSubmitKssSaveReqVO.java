package com.rs.module.acp.controller.admin.ds.vo.sjbs;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-数据固化-每日数据报送(看守所)新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DailyDataSubmitKssSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("固化日期")
    @NotNull(message = "固化日期不能为空")
    private String solidificationDate;

    @ApiModelProperty("固化开始日期")
    @NotNull(message = "固化开始日期不能为空")
    private String startDate;

    @ApiModelProperty("固化结束日期")
    @NotNull(message = "固化结束日期不能为空")
    private String endDate;

    @ApiModelProperty("被监管人员总数")
    private Integer bjgry;

    @ApiModelProperty("男性被监管人员总数")
    private Integer bjgryMale;

    @ApiModelProperty("女性被监管人员总数")
    private Integer bjgryFemale;

    @ApiModelProperty("未成年被监管人员总数")
    private Integer bjgryMinor;

    @ApiModelProperty("一级重大风险总数")
    private Integer yjzdfx;

    @ApiModelProperty("二级重大风险总数")
    private Integer ejzdfx;

    @ApiModelProperty("三级重大风险总数")
    private Integer sjzdfx;

    @ApiModelProperty("戒具使用总数")
    private Integer jgsy;

    @ApiModelProperty("精神异常总数")
    private Integer jsyc;

    @ApiModelProperty("等待精神鉴定总数")
    private Integer ddjsjd;

    @ApiModelProperty("谈话教育总数")
    private Integer thjy;

    @ApiModelProperty("监视调整总数")
    private Integer jstz;

    @ApiModelProperty("侦查阶段总数")
    private Integer zcjd;

    @ApiModelProperty("审查阶段总数")
    private Integer scjd;

    @ApiModelProperty("审判阶段总数")
    private Integer spjd;

    @ApiModelProperty("待交付执行总数")
    private Integer djfzx;

    @ApiModelProperty("留所服刑总数")
    private Integer lsfx;

    @ApiModelProperty("亲属会见总数")
    private Integer qshj;

    @ApiModelProperty("刑事拘留总数")
    private Integer xsjl;

    @ApiModelProperty("逮捕总数")
    private Integer db;

    @ApiModelProperty("取保候审总数")
    private Integer qbhs;

    @ApiModelProperty("监室居住总数")
    private Integer jsjz;

}
