package com.rs.module.acp.service.ds.sjbs;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.DateUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyDataSubmitJlsSaveReqVO;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyDataSubmitReqVO;
import com.rs.module.acp.entity.ds.sjbs.WeeklyDataSubmitJlsDO;
import com.rs.module.acp.util.SjbsDateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.ds.sjbs.WeeklyDataSubmitJlsDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 实战平台-数据固化-每周数据报送(拘留所) Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WeeklyDataSubmitJlsServiceImpl extends BaseServiceImpl<WeeklyDataSubmitJlsDao, WeeklyDataSubmitJlsDO> implements WeeklyDataSubmitJlsService {

    @Resource
    private WeeklyDataSubmitJlsDao weeklyDataSubmitJlsDao;
    @Autowired
    private WeeklyLawyerTopService weeklyLawyerTopService;
    @Autowired
    private WeeklyPrisonerTopService weeklyPrisonerTopService;
    @Override
    public String createWeeklyDataSubmitJls(WeeklyDataSubmitJlsSaveReqVO createReqVO) {
        // 插入
        WeeklyDataSubmitJlsDO weeklyDataSubmitJls = BeanUtils.toBean(createReqVO, WeeklyDataSubmitJlsDO.class);
        weeklyDataSubmitJlsDao.insert(weeklyDataSubmitJls);
        // 返回
        return weeklyDataSubmitJls.getId();
    }

    @Override
    public void updateWeeklyDataSubmitJls(WeeklyDataSubmitJlsSaveReqVO updateReqVO) {
        // 校验存在
        validateWeeklyDataSubmitJlsExists(updateReqVO.getId());
        // 更新
        WeeklyDataSubmitJlsDO updateObj = BeanUtils.toBean(updateReqVO, WeeklyDataSubmitJlsDO.class);
        weeklyDataSubmitJlsDao.updateById(updateObj);
    }

    @Override
    public void deleteWeeklyDataSubmitJls(String id) {
        // 校验存在
        validateWeeklyDataSubmitJlsExists(id);
        // 删除
        weeklyDataSubmitJlsDao.deleteById(id);
    }

    private void validateWeeklyDataSubmitJlsExists(String id) {
        if (weeklyDataSubmitJlsDao.selectById(id) == null) {
            throw new ServerException("实战平台-数据固化-每周数据报送(拘留所)数据不存在");
        }
    }

    @Override
    public WeeklyDataSubmitJlsDO getWeeklyDataSubmitJls(String id) {
        return weeklyDataSubmitJlsDao.selectById(id);
    }

    @Override
    public void saveForStatistic(String orgCode, String startDate, String endDate) {
        String[] range = SjbsDateUtil.getLastWeekRangeStr();
        if(StringUtil.isEmpty(startDate)) startDate = range[0];
        if(StringUtil.isEmpty(endDate)) endDate = range[1];
        List<JSONObject> tjList = weeklyDataSubmitJlsDao.statisticNumTj(orgCode, startDate, endDate);
        List<JSONObject> txList = weeklyDataSubmitJlsDao.statisticNumTx(orgCode, startDate, endDate);
        List<WeeklyDataSubmitJlsDO> meetingInfo = weeklyDataSubmitJlsDao.statisticNumLawyerMeeting(orgCode, startDate, endDate);
        for (WeeklyDataSubmitJlsDO entity : meetingInfo){
            fixTxData(txList,entity);
            fixTjData(tjList,entity);
            entity.setSolidificationDate(SjbsDateUtil.getYearWeekStr(startDate, endDate));
            entity.setEndDate(endDate);
            entity.setStartDate(startDate);
            entity.setId(StringUtil.getGuid());
        }
        List<WeeklyDataSubmitReqVO> weeklyDataSubmitList = BeanUtils.toBean(meetingInfo, WeeklyDataSubmitReqVO.class);
        weeklyDataSubmitJlsDao.deleteByCondition(orgCode, startDate, endDate);
        weeklyDataSubmitJlsDao.insertBatch(meetingInfo);
        weeklyLawyerTopService.saveForStatistic(weeklyDataSubmitList);
        weeklyPrisonerTopService.saveForStatistic(weeklyDataSubmitList);
    }
    private void fixTxData(List<JSONObject> txList, WeeklyDataSubmitJlsDO entity){
        if(CollectionUtil.isNotNull(txList)){
            int qtCount = 0;
            int totalCount = 0;
            for (JSONObject jsonObject : txList){
                if(!entity.getOrgCode().equals(jsonObject.getString("orgcode"))) continue;
                String tjjglx = jsonObject.getString("tjjglx");
                Integer count = jsonObject.getInteger("count");
                switch (tjjglx){
                    case "1":
                        entity.setTxGajg(count);
                        break;
                    case "2":
                        entity.setTxJcy(count);
                        break;
                    case "3":
                        entity.setTxFy(count);
                        break;
                    case "4":
                    case "9":
                        qtCount += count;
                        break;
                }
                totalCount += count;
            }
            entity.setTxQtdw(qtCount);
            entity.setTx(totalCount);

        }
    }
    private void fixTjData(List<JSONObject> tjList, WeeklyDataSubmitJlsDO entity){
        if(CollectionUtil.isNotNull(tjList)){
            int qtCount = 0;
            int totalCount = 0;
            for (JSONObject jsonObject : tjList){
                if(!entity.getOrgCode().equals(jsonObject.getString("orgcode"))) continue;
                String tjjglx = jsonObject.getString("tjjglx");
                Integer count = jsonObject.getInteger("count");
                switch (tjjglx){
                    case "1":
                        entity.setTjGajg(count);
                        break;
                    case "2":
                        entity.setTjJcy(count);
                        break;
                    case "3":
                        entity.setTjFy(count);
                        break;
                    case "4":
                    case "9":
                        qtCount += count;
                        break;
                }
                totalCount += count;
            }
            entity.setTjQtdw(qtCount);
            entity.setTj(totalCount);
        }
    }
    @Override
    public List<WeeklyDataSubmitJlsDO> getWeeklyDataSubmitJlsByDate(String startDate, String endDate, String orgCode) {
        String[] range = SjbsDateUtil.getLastWeekRangeStr();
        if(null == startDate) startDate = range[0];
        if(null == endDate) endDate = range[1];
        if(StringUtil.isEmpty(orgCode)) orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        List<WeeklyDataSubmitJlsDO> list = weeklyDataSubmitJlsDao.selectList(new LambdaQueryWrapperX<WeeklyDataSubmitJlsDO>().
                eq(WeeklyDataSubmitJlsDO::getStartDate, startDate).eq(WeeklyDataSubmitJlsDO::getEndDate, endDate).
                eq(WeeklyDataSubmitJlsDO::getOrgCode, orgCode).eq(WeeklyDataSubmitJlsDO::getIsDel, 0));
        return list;
    }
}
