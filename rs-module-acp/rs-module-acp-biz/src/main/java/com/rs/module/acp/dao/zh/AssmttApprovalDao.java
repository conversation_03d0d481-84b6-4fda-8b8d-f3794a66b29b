package com.rs.module.acp.dao.zh;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmttApprovalListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmttApprovalPageReqVO;
import com.rs.module.acp.entity.zh.AssmttApprovalDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 综合管理-绩效考核-加减分考核审核 Dao
*
* <AUTHOR>
*/
@Mapper
public interface AssmttApprovalDao extends IBaseDao<AssmttApprovalDO> {


    default PageResult<AssmttApprovalDO> selectPage(AssmttApprovalPageReqVO reqVO) {
        Page<AssmttApprovalDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<AssmttApprovalDO> wrapper = new LambdaQueryWrapperX<AssmttApprovalDO>()
            .eqIfPresent(AssmttApprovalDO::getAssmtRecordId, reqVO.getAssmtRecordId())
            .eqIfPresent(AssmttApprovalDO::getAssessedSfzh, reqVO.getAssessedSfzh())
            .likeIfPresent(AssmttApprovalDO::getAssessedName, reqVO.getAssessedName())
            .eqIfPresent(AssmttApprovalDO::getAssmtMonth, reqVO.getAssmtMonth())
            .eqIfPresent(AssmttApprovalDO::getIndicatorType, reqVO.getIndicatorType())
            .eqIfPresent(AssmttApprovalDO::getAssessorSfzh, reqVO.getAssessorSfzh())
            .likeIfPresent(AssmttApprovalDO::getAssessorName, reqVO.getAssessorName())
            .betweenIfPresent(AssmttApprovalDO::getAssessorTime, reqVO.getAssessorTime())
            .eqIfPresent(AssmttApprovalDO::getStatus, reqVO.getStatus())
            .eqIfPresent(AssmttApprovalDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(AssmttApprovalDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(AssmttApprovalDO::getAddTime);
        }
        Page<AssmttApprovalDO> assmttApprovalPage = selectPage(page, wrapper);
        return new PageResult<>(assmttApprovalPage.getRecords(), assmttApprovalPage.getTotal());
    }
    default List<AssmttApprovalDO> selectList(AssmttApprovalListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AssmttApprovalDO>()
            .eqIfPresent(AssmttApprovalDO::getAssmtRecordId, reqVO.getAssmtRecordId())
            .eqIfPresent(AssmttApprovalDO::getAssessedSfzh, reqVO.getAssessedSfzh())
            .likeIfPresent(AssmttApprovalDO::getAssessedName, reqVO.getAssessedName())
            .eqIfPresent(AssmttApprovalDO::getAssmtMonth, reqVO.getAssmtMonth())
            .eqIfPresent(AssmttApprovalDO::getIndicatorType, reqVO.getIndicatorType())
            .eqIfPresent(AssmttApprovalDO::getAssessorSfzh, reqVO.getAssessorSfzh())
            .likeIfPresent(AssmttApprovalDO::getAssessorName, reqVO.getAssessorName())
            .betweenIfPresent(AssmttApprovalDO::getAssessorTime, reqVO.getAssessorTime())
            .eqIfPresent(AssmttApprovalDO::getStatus, reqVO.getStatus())
            .eqIfPresent(AssmttApprovalDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(AssmttApprovalDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(AssmttApprovalDO::getAddTime));    }


    }
