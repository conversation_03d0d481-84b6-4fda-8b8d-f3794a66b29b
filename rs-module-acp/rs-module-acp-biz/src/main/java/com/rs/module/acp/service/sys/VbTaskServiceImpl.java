package com.rs.module.acp.service.sys;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.CollectionUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.cons.VoiceBroadConstants;
import com.rs.module.acp.controller.admin.sys.vo.VbTaskListReqVO;
import com.rs.module.acp.controller.admin.sys.vo.VbTaskPageReqVO;
import com.rs.module.acp.controller.admin.sys.vo.VbTaskSaveReqVO;
import com.rs.module.acp.dao.sys.VbTaskDao;
import com.rs.module.acp.entity.pm.BaseDeviceInscreenDO;
import com.rs.module.acp.entity.sys.VbConfigCurrentDO;
import com.rs.module.acp.entity.sys.VbTaskDO;
import com.rs.module.acp.service.pm.BaseDeviceInscreenService;
import com.rs.module.acp.util.VoiceBroadUtil;
import com.rs.module.base.enums.InscreenDeviceTypeEnum;


/**
 * 实战平台-语音播报-待播报任务 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class VbTaskServiceImpl extends BaseServiceImpl<VbTaskDao, VbTaskDO> implements VbTaskService {

    @Resource
    private VbTaskDao vbTaskDao;
    
    @Resource
    BaseDeviceInscreenService inscreenService;

    @Override
    public String createVbTask(VbTaskSaveReqVO createReqVO) {
        // 插入
        VbTaskDO vbTask = BeanUtils.toBean(createReqVO, VbTaskDO.class);
        vbTaskDao.insert(vbTask);
        // 返回
        return vbTask.getId();
    }

    @Override
    public void updateVbTask(VbTaskSaveReqVO updateReqVO) {
        // 校验存在
        validateVbTaskExists(updateReqVO.getId());
        // 更新
        VbTaskDO updateObj = BeanUtils.toBean(updateReqVO, VbTaskDO.class);
        vbTaskDao.updateById(updateObj);
    }

    @Override
    public void deleteVbTask(String id) {
        // 校验存在
        validateVbTaskExists(id);
        // 删除
        vbTaskDao.deleteById(id);
    }

    private void validateVbTaskExists(String id) {
        if (vbTaskDao.selectById(id) == null) {
            throw new ServerException("实战平台-语音播报-待播报任务数据不存在");
        }
    }

    @Override
    public VbTaskDO getVbTask(String id) {
        return vbTaskDao.selectById(id);
    }

    @Override
    public PageResult<VbTaskDO> getVbTaskPage(VbTaskPageReqVO pageReqVO) {
        return vbTaskDao.selectPage(pageReqVO);
    }

    @Override
    public List<VbTaskDO> getVbTaskList(VbTaskListReqVO listReqVO) {
        return vbTaskDao.selectList(listReqVO);
    }

    @Override
    public List<VbTaskDO> getVbTaskListApp(String serialNumber) {
    	Date now = new Date();
    	QueryWrapper<VbTaskDO> wrapper = new QueryWrapper<>();
    	wrapper.eq("vb_serial_number", serialNumber);
    	wrapper.le("start_time", now);
    	wrapper.ge("end_time", now);
    	wrapper.in("status", Arrays.asList(VoiceBroadConstants.JOB_STATUS_WAIT, VoiceBroadConstants.JOB_STATUS_PUSH));
    	wrapper.orderByAsc("priority", "start_time");
    	
    	return list(wrapper);
    }
    
    public void saveCurrentJob(VbConfigCurrentDO currentDO) {
    	if(VoiceBroadConstants.JOB_STATUS_WAIT.equals(currentDO.getStatus())) {
    		Date now = new Date();
    		if(now.after(currentDO.getStartTime())) {
    			if(currentDO.getEndTime() == null || now.before(currentDO.getEndTime())) {
    				List<BaseDeviceInscreenDO> inscreenList = inscreenService.getInscreenByCondition(
    	    				currentDO.getVbArea(), InscreenDeviceTypeEnum.INSCREEN.getCode());
    	    		if(CollectionUtil.isNotNull(inscreenList)) {
    	    			
    	    			//删除历史播报任务
    	        		QueryWrapper<VbTaskDO> wrapper = new QueryWrapper<>();
    	        		wrapper.eq("business_id", currentDO.getId());
    	        		wrapper.eq("status", VoiceBroadConstants.JOB_STATUS_WAIT);
    	        		remove(wrapper);
    	        		
    	        		//循环插入播报任务
    	        		for(BaseDeviceInscreenDO inscreen : inscreenList) {
    	        			VbTaskDO taskDo = VoiceBroadUtil.buildCurrentTask(currentDO, inscreen);
    	        			
    	        			//保存播报任务
    	        			save(taskDo);
    	        			
    	        			//异步推送WebSocket消息
    	        			VoiceBroadUtil.sendSocketMessage(taskDo);
    	        		}
    	    		}
    			}
    		}
    	}    	
    }
    
    public List<VbTaskDO> getNeedCleanTaskList(){
    	return list(new LambdaQueryWrapperX<VbTaskDO>()
    			.lt(VbTaskDO::getEndTime, new Date())
    			.in(VbTaskDO::getStatus, Arrays.asList(new Short[] {0, 1}))
    			.isNotNull(VbTaskDO::getEndTime)
    	);
    }
}
