package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.doublecheck.DoubleCheckListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.doublecheck.DoubleCheckPageReqVO;
import com.rs.module.acp.entity.gj.DoubleCheckDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 实战平台-管教业务-双重检查登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface DoubleCheckDao extends IBaseDao<DoubleCheckDO> {


    default PageResult<DoubleCheckDO> selectPage(DoubleCheckPageReqVO reqVO) {
        Page<DoubleCheckDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<DoubleCheckDO> wrapper = new LambdaQueryWrapperX<DoubleCheckDO>()
            .eqIfPresent(DoubleCheckDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(DoubleCheckDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(DoubleCheckDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(DoubleCheckDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(DoubleCheckDO::getInspectionResult, reqVO.getInspectionResult())
            .eqIfPresent(DoubleCheckDO::getProhibitedItems, reqVO.getProhibitedItems())
            .eqIfPresent(DoubleCheckDO::getPhysicalExam, reqVO.getPhysicalExam())
            .eqIfPresent(DoubleCheckDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(DoubleCheckDO::getOperatePolice, reqVO.getOperatePolice())
            .betweenIfPresent(DoubleCheckDO::getOperateTime, reqVO.getOperateTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(DoubleCheckDO::getAddTime);
        }
        Page<DoubleCheckDO> doubleCheckPage = selectPage(page, wrapper);
        return new PageResult<>(doubleCheckPage.getRecords(), doubleCheckPage.getTotal());
    }
    default List<DoubleCheckDO> selectList(DoubleCheckListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DoubleCheckDO>()
            .eqIfPresent(DoubleCheckDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(DoubleCheckDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(DoubleCheckDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(DoubleCheckDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(DoubleCheckDO::getInspectionResult, reqVO.getInspectionResult())
            .eqIfPresent(DoubleCheckDO::getProhibitedItems, reqVO.getProhibitedItems())
            .eqIfPresent(DoubleCheckDO::getPhysicalExam, reqVO.getPhysicalExam())
            .eqIfPresent(DoubleCheckDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(DoubleCheckDO::getOperatePolice, reqVO.getOperatePolice())
            .betweenIfPresent(DoubleCheckDO::getOperateTime, reqVO.getOperateTime())
        .orderByDesc(DoubleCheckDO::getAddTime));    }


    Page<DoubleCheckDO> getAppDoubleCheckPage(Page<DoubleCheckDO> page, @Param("operatePoliceSfzh") String operatePoliceSfzh,
                                              @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
