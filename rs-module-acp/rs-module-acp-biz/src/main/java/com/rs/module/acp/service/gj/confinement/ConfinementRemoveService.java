package com.rs.module.acp.service.gj.confinement;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.confinement.*;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsACPSaveVO;
import com.rs.module.acp.entity.gj.confinement.ConfinementRemoveDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-解除禁闭呈批 Service 接口
 *
 * <AUTHOR>
 */
public interface ConfinementRemoveService extends IBaseService<ConfinementRemoveDO>{

    /**
     * 创建实战平台-管教业务-解除禁闭呈批
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createConfinementRemove(@Valid ConfinementRemoveSaveReqVO createReqVO) throws Exception;

    /**
     * 更新实战平台-管教业务-解除禁闭呈批
     *
     * @param updateReqVO 更新信息
     */
    void updateConfinementRemove(@Valid ConfinementRemoveSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-解除禁闭呈批
     *
     * @param id 编号
     */
    void deleteConfinementRemove(String id);

    /**
     * 获得实战平台-管教业务-解除禁闭呈批
     *
     * @param id 编号
     * @return 实战平台-管教业务-解除禁闭呈批
     */
    ConfinementRemoveDO getConfinementRemove(String id);

    /**
    * 获得实战平台-管教业务-解除禁闭呈批分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-解除禁闭呈批分页
    */
    PageResult<ConfinementRemoveDO> getConfinementRemovePage(ConfinementRemovePageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-解除禁闭呈批列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-解除禁闭呈批列表
    */
    List<ConfinementRemoveDO> getConfinementRemoveList(ConfinementRemoveListReqVO listReqVO);


    boolean approve(ConfinementRemoveApproveVO approveReqVO) throws Exception;

    List<ConfinementFlowApproveTrackVO> getApproveTrack(String id);

    ConfinementRemoveDO getConfinementRemoveByConfinementId(String id);

    ConfinementDetailRespVO getDetail(String id);

    ConfinementDetailRespVO getDetailTrack(String id);

    boolean saveInOutRecords(InOutRecordsConfinementRemoveSaveVO saveReqVO);

    ConfinementDetailRespVO initRemoveReason(String id);
}
