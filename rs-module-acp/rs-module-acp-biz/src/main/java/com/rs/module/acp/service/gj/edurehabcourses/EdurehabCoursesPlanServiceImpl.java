package com.rs.module.acp.service.gj.edurehabcourses;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.util.SnowflakeIdUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.*;
import com.rs.module.acp.dao.gj.EdurehabCoursesRecordDao;
import com.rs.module.acp.entity.gj.EdurehabCoursesRecordDO;
import com.rs.module.acp.enums.gj.EdurehabCoursesPlanStatusEnum;
import com.rs.module.base.util.BspApprovalUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

import com.rs.module.acp.entity.gj.EdurehabCoursesPlanDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.EdurehabCoursesPlanDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-教育康复课程计划 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class EdurehabCoursesPlanServiceImpl extends BaseServiceImpl<EdurehabCoursesPlanDao, EdurehabCoursesPlanDO> implements EdurehabCoursesPlanService {

    @Resource
    private EdurehabCoursesPlanDao edurehabCoursesPlanDao;

    @Resource
    private EdurehabCoursesRecordDao edurehabCoursesRecordDao;

    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

    private static final String defKey = "yonghudanweilindao";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createEdurehabCoursesPlan(EdurehabCoursesPlanSaveReqVO createReqVO) {
        if (new Date().compareTo(createReqVO.getEndDate()) > 0) {
            throw new RuntimeException("不允许创建过期时间课程！");
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        LambdaQueryWrapper<EdurehabCoursesPlanDO> checkWrapper = Wrappers.lambdaQuery(EdurehabCoursesPlanDO.class)
                .eq(EdurehabCoursesPlanDO::getOrgCode, sessionUser.getOrgCode())
                .eq(EdurehabCoursesPlanDO::getEndDate, createReqVO.getEndDate())
                .eq(EdurehabCoursesPlanDO::getStartDate, createReqVO.getStartDate())
                .ne(EdurehabCoursesPlanDO::getStatus, EdurehabCoursesPlanStatusEnum.SPBTG.getCode())
                .orderByDesc(EdurehabCoursesPlanDO::getAddTime).last("limit 1");
        EdurehabCoursesPlanDO planDO = edurehabCoursesPlanDao.selectOne(checkWrapper);
        if (Objects.nonNull(planDO)) {
            throw new RuntimeException("当前有" + EdurehabCoursesPlanStatusEnum.getByCode(planDO.getStatus()).getName() + "课程，不允许创建！");
        }
        // 插入
        EdurehabCoursesPlanDO edurehabCoursesPlan = BeanUtils.toBean(createReqVO, EdurehabCoursesPlanDO.class);
        edurehabCoursesPlan.setId(SnowflakeIdUtil.getGuid());
        List<EdurehabCoursesRecordSaveReqVO> recordReqList = createReqVO.getListRecord();
        if (CollectionUtil.isEmpty(recordReqList)) {
            throw new ServerException("课程记录信息不能为空");
        }
        Map<String, List<EdurehabCoursesRecordSaveReqVO>> map = new HashMap<>();
        for (EdurehabCoursesRecordSaveReqVO recordSaveReqVO : recordReqList) {
            recordSaveReqVO.setId(null);
            recordSaveReqVO.setEdurehabCoursesPlanId(edurehabCoursesPlan.getId());
            String key = sdf.format(recordSaveReqVO.getCoursesDate()) + "@" + recordSaveReqVO.getAreaId() + "@" + recordSaveReqVO.getTimeSlotCode();
            List<EdurehabCoursesRecordSaveReqVO> tmpList = map.get(key);
            if (CollectionUtil.isEmpty(tmpList)) {
                tmpList = new ArrayList<>();
                map.put(key, tmpList);
            }
            tmpList.add(recordSaveReqVO);
        }
        List<EdurehabCoursesRecordSaveReqVO> list = new ArrayList<>();
        List<Date> dayList = getDayListByStrartAndEndDate(createReqVO.getStartDate(), createReqVO.getEndDate());
        for (Date day : dayList) {
            for (JqAreaVO jqAreaVO : createReqVO.getJqAreaVOList()) {
                for (EdurehabTimeSlotSaveReqVO timeSlot : createReqVO.getTimeList()) {
                    String key = sdf.format(day) + "@" + jqAreaVO.getAreaId() + "@" + timeSlot.getTimeSlotCode();
                    List<EdurehabCoursesRecordSaveReqVO> needSaveList = map.get(key);
                    if (CollectionUtil.isEmpty(needSaveList)) {
                        // 创建空课程是为了方便获取详情时 统计时间天、时间段和监区
                        EdurehabCoursesRecordSaveReqVO recordSaveReqVO = new EdurehabCoursesRecordSaveReqVO();
                        recordSaveReqVO.setEdurehabCoursesPlanId(edurehabCoursesPlan.getId());
                        recordSaveReqVO.setAreaId(jqAreaVO.getAreaId());
                        recordSaveReqVO.setAreaName(jqAreaVO.getAreaName());
                        recordSaveReqVO.setCoursesDate(day);
                        recordSaveReqVO.setTimeSlotCode(timeSlot.getTimeSlotCode());
                        recordSaveReqVO.setTimeSlotStartTime(timeSlot.getStartTime());
                        recordSaveReqVO.setTimeSlotEndTime(timeSlot.getEndTime());
                        list.add(recordSaveReqVO);
                    } else {
                        list.addAll(needSaveList);
                    }
                }
            }
        }
        map = null;
        List<EdurehabCoursesRecordDO> listRecords = BeanUtils.toBean(list, EdurehabCoursesRecordDO.class);

        LambdaQueryWrapper<EdurehabCoursesPlanDO> wrapper = Wrappers.lambdaQuery(EdurehabCoursesPlanDO.class)
                .eq(EdurehabCoursesPlanDO::getOrgCode, sessionUser.getOrgCode())
                .eq(EdurehabCoursesPlanDO::getEndDate, createReqVO.getEndDate())
                .eq(EdurehabCoursesPlanDO::getStartDate, createReqVO.getStartDate())
                .orderByDesc(EdurehabCoursesPlanDO::getPlanCode).last("limit 1");
        EdurehabCoursesPlanDO temp = edurehabCoursesPlanDao.selectOne(wrapper);
        int index = 0;
        if (Objects.nonNull(temp)) {
            index = Integer.parseInt(temp.getPlanCode().split("-")[1]);
        }
        edurehabCoursesPlan.setPlanCode(sdf.format(createReqVO.getStartDate()) + "-" + String.format("%03d", index + 1));
        // 提交申请
        //启动流程审批
        String msgUrl = "/#/";
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", edurehabCoursesPlan.getId());
        JSONObject result = BspApprovalUtil.defaultStartProcess(HttpUtils.getAppCode() + "-" + defKey, edurehabCoursesPlan.getId(),
                edurehabCoursesPlan.getPlanName() + "流程审批", msgUrl, variables);
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            edurehabCoursesPlan.setActInstId(bpmTrail.getString("actInstId"));
            edurehabCoursesPlan.setTaskId(bpmTrail.getString("taskId"));
            edurehabCoursesPlan.setStatus(EdurehabCoursesPlanStatusEnum.DSP.getCode());
            edurehabCoursesRecordDao.insertBatch(listRecords);
            edurehabCoursesPlanDao.insert(edurehabCoursesPlan);
        } else {
            throw new ServerException("流程启动失败");
        }
        // 返回
        return edurehabCoursesPlan.getId();
    }

    private List<Date> getDayListByStrartAndEndDate(Date startDate, Date endDate) {
        List<Date> list = new ArrayList<>();
        long index = 1000 * 60 * 60 * 24L;
        long startTime = startDate.getTime();
        for (int i = 0; i < 7; i++) {
            list.add(new Date(startTime + i * index));
        }
        return list;
    }

    @Override
    public void updateEdurehabCoursesPlan(EdurehabCoursesPlanSaveReqVO updateReqVO) {
        // 校验存在
        validateEdurehabCoursesPlanExists(updateReqVO.getId());
        // 更新
        EdurehabCoursesPlanDO updateObj = BeanUtils.toBean(updateReqVO, EdurehabCoursesPlanDO.class);
        edurehabCoursesPlanDao.updateById(updateObj);
    }

    @Override
    public void deleteEdurehabCoursesPlan(String id) {
        // 校验存在
        validateEdurehabCoursesPlanExists(id);
        // 删除
        edurehabCoursesPlanDao.deleteById(id);
    }

    private void validateEdurehabCoursesPlanExists(String id) {
        if (edurehabCoursesPlanDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-教育康复课程计划数据不存在");
        }
    }

    @Override
    public EdurehabCoursesPlanRespVO getEdurehabCoursesPlan(String id) {
        EdurehabCoursesPlanDO planDO = edurehabCoursesPlanDao.selectById(id);
        EdurehabCoursesPlanRespVO respVO = BeanUtils.toBean(planDO, EdurehabCoursesPlanRespVO.class);
        if (Objects.nonNull(planDO)) {
            LambdaQueryWrapper<EdurehabCoursesRecordDO> wrapper = Wrappers.lambdaQuery(EdurehabCoursesRecordDO.class)
                    .eq(EdurehabCoursesRecordDO::getEdurehabCoursesPlanId, planDO.getId())
                    //.orderByAsc(EdurehabCoursesRecordDO::getTimeSlotCode)
                    //.orderByAsc(EdurehabCoursesRecordDO::getCoursesDate)
                    .orderByAsc(EdurehabCoursesRecordDO::getId);
            List<EdurehabCoursesRecordDO> list = edurehabCoursesRecordDao.selectList(wrapper);
            List<EdurehabCoursesRecordRespVO> recordRespVOS = BeanUtils.toBean(list, EdurehabCoursesRecordRespVO.class);
            if (CollectionUtil.isEmpty(recordRespVOS)) {
                return respVO;
            }
            List<JqAreaVO> jqAreaVOList = new ArrayList<>();
            Map<String, String> checkJqAreaMap = new HashMap<>();
            List<EdurehabTimeSlotRespVO> timeList = new ArrayList<>();
            Map<String, EdurehabTimeSlotRespVO> checkTimeMap = new HashMap<>();
            List<String> dateList = new ArrayList<>();
            Map<String, Date> checkDateMap = new HashMap<>();
            Map<String, List<EdurehabCoursesRecordRespVO>> allMap = new HashMap<>();
            for (EdurehabCoursesRecordRespVO recordRespVO : recordRespVOS) {
                recordRespVO.setCoursesDateStr(sdf.format(recordRespVO.getCoursesDate()));
                String areaName = checkJqAreaMap.get(recordRespVO.getAreaId());
                if (StringUtils.isEmpty(areaName)) {
                    checkJqAreaMap.put(recordRespVO.getAreaId(), recordRespVO.getAreaName());
                    JqAreaVO jqAreaVO = new JqAreaVO();
                    jqAreaVO.setAreaId(recordRespVO.getAreaId());
                    jqAreaVO.setAreaName(recordRespVO.getAreaName());
                    jqAreaVOList.add(jqAreaVO);
                }
                EdurehabTimeSlotRespVO edurehabTimeSlotRespVO = checkTimeMap.get(recordRespVO.getTimeSlotCode());
                if (Objects.isNull(edurehabTimeSlotRespVO)) {
                    edurehabTimeSlotRespVO = new EdurehabTimeSlotRespVO();
                    edurehabTimeSlotRespVO.setStartTime(recordRespVO.getTimeSlotStartTime());
                    edurehabTimeSlotRespVO.setEndTime(recordRespVO.getTimeSlotEndTime());
                    edurehabTimeSlotRespVO.setTimeSlotCode(recordRespVO.getTimeSlotCode());
                    checkTimeMap.put(recordRespVO.getTimeSlotCode(), edurehabTimeSlotRespVO);
                    timeList.add(edurehabTimeSlotRespVO);
                }
                Date dayInfo = checkDateMap.get(recordRespVO.getCoursesDateStr());
                if (Objects.isNull(dayInfo)) {
                    checkDateMap.put(recordRespVO.getCoursesDateStr(), recordRespVO.getCoursesDate());
                    dateList.add(recordRespVO.getCoursesDateStr());
                }
                String key = recordRespVO.getCoursesDateStr() + "@" + recordRespVO.getAreaId() + "@" + recordRespVO.getTimeSlotCode();
                List<EdurehabCoursesRecordRespVO> tempList = allMap.get(key);
                if (CollectionUtil.isEmpty(tempList)) {
                    tempList = new ArrayList<>();
                    allMap.put(key, tempList);
                }
                tempList.add(recordRespVO);

            }
            respVO.setJqAreaVOList(jqAreaVOList);
            respVO.setTimeList(timeList);
            //respVO.setListRecord(recordRespVOS);

            //信息整合
            List<EdurehabDayRespVO> edurehabList = new ArrayList<>();
            dateList.sort(String::compareTo);
            for (String day : dateList) {
                EdurehabDayRespVO dayRespVO = new EdurehabDayRespVO();
                dayRespVO.setCoursesDateStr(day);
                dayRespVO.setCoursesDate(checkDateMap.get(day));
                List<JqAreaVO> jqAreaVOS = new ArrayList<>();
                for (JqAreaVO jqAreaVO : jqAreaVOList) {
                    JqAreaVO jqAreaVOTmp = BeanUtils.toBean(jqAreaVO, JqAreaVO.class);
                    List<EdurehabCoursesTimeSlotRespVO> timeSlotList = new ArrayList<>();
                    for (EdurehabTimeSlotRespVO edurehabTimeSlotRespVO : timeList) {
                        String key = day + "@" + jqAreaVO.getAreaId() + "@" + edurehabTimeSlotRespVO.getTimeSlotCode();
                        EdurehabCoursesTimeSlotRespVO timeSlotRespVO = BeanUtils.toBean(edurehabTimeSlotRespVO, EdurehabCoursesTimeSlotRespVO.class);
                        timeSlotRespVO.setListRecord(allMap.get(key));
                        timeSlotList.add(timeSlotRespVO);
                    }
                    jqAreaVOTmp.setTimeSlotList(timeSlotList);
                    jqAreaVOS.add(jqAreaVOTmp);
                }
                dayRespVO.setJqAreaVOList(jqAreaVOS);
                edurehabList.add(dayRespVO);
            }
            respVO.setEdurehabList(edurehabList);
            checkTimeMap = null;
            checkJqAreaMap = null;
            checkDateMap = null;
            allMap = null;
        }
        return respVO;
    }

    @Override
    public PageResult<EdurehabCoursesPlanDO> getEdurehabCoursesPlanPage(EdurehabCoursesPlanPageReqVO pageReqVO) {
        return edurehabCoursesPlanDao.selectPage(pageReqVO);
    }

    @Override
    public List<EdurehabCoursesPlanDO> getEdurehabCoursesPlanList(EdurehabCoursesPlanListReqVO listReqVO) {
        return edurehabCoursesPlanDao.selectList(listReqVO);
    }

    @Override
    public EdurehabCoursesPlanRespVO getPlanTime(String orgCode) {
        for (int i = 0; i < 50; i++) {
            LocalDate localDate = LocalDate.now().plusDays(i * 7);
            Date date = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            LambdaQueryWrapper<EdurehabCoursesPlanDO> wrapper = Wrappers.lambdaQuery(EdurehabCoursesPlanDO.class)
                    .eq(EdurehabCoursesPlanDO::getOrgCode, orgCode)
                    .ne(EdurehabCoursesPlanDO::getStatus, EdurehabCoursesPlanStatusEnum.SPBTG.getCode())
                    .ge(EdurehabCoursesPlanDO::getEndDate, date)
                    .le(EdurehabCoursesPlanDO::getStartDate, date);
            Integer count = edurehabCoursesPlanDao.selectCount(wrapper);
            if (Objects.isNull(count) || count == 0) {
                EdurehabCoursesPlanRespVO respVO = new EdurehabCoursesPlanRespVO();
                DayOfWeek dayOfWeek = localDate.getDayOfWeek();
                LocalDate startLocalDate = localDate.plusDays(1 - dayOfWeek.getValue());
                LocalDate endLocalDate = localDate.plusDays(7 - dayOfWeek.getValue());
                respVO.setStartDate(Date.from(startLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
                respVO.setEndDate(Date.from(endLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
                respVO.setStartDateStr(startLocalDate.toString());
                respVO.setEndDateStr(endLocalDate.toString());
                return respVO;
            }
        }
        return null;
    }

    @Override
    public List<JqAreaVO> getPlanArea(String orgCode) {
        return edurehabCoursesPlanDao.getPlanAreaByOrgCode(orgCode);
    }

    @Override
    public void approval(EdurehabCoursesPlanApprovalReqVO approvalReqVO) {
        EdurehabCoursesPlanDO edurehabCoursesPlanDO = edurehabCoursesPlanDao.selectById(approvalReqVO.getId());
        if (edurehabCoursesPlanDO == null) {
            throw new ServerException("实战平台-管教业务-教育康复课程计划数据不存在");
        }
        if (!EdurehabCoursesPlanStatusEnum.DSP.getCode().equals(edurehabCoursesPlanDO.getStatus())) {
            throw new ServerException("非待审批状态，不能进行审批");
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(edurehabCoursesPlanDO.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }

        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", edurehabCoursesPlanDO.getId());
        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getId());
        nowApproveUser.put("name", sessionUser.getName());
        BspApproceStatusEnum bspApproceStatusEnum;
        if (EdurehabCoursesPlanStatusEnum.SXZ.getCode().equals(approvalReqVO.getStatus())) {
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED_END;
        } else if (EdurehabCoursesPlanStatusEnum.SPBTG.getCode().equals(approvalReqVO.getStatus())) {
            bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
        } else {
            throw new ServerException("非法status");
        }
        edurehabCoursesPlanDO.setStatus(approvalReqVO.getStatus());
        JSONObject result = BspApprovalUtil.approvalProcess(HttpUtils.getAppCode() + "-" + defKey,
                edurehabCoursesPlanDO.getActInstId(), edurehabCoursesPlanDO.getTaskId(), edurehabCoursesPlanDO.getId(),
                bspApproceStatusEnum, approvalReqVO.getApprovelComment(), null, null, true,
                variables, nowApproveUser, "acp");
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            edurehabCoursesPlanDO.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程启动失败");
        }
        edurehabCoursesPlanDao.updateById(edurehabCoursesPlanDO);
    }

    @Override
    public EdurehabCoursesPlanRespVO getByDate(String orgCode, Date startDate, Date endDate) {
        LambdaQueryWrapper<EdurehabCoursesPlanDO> wrapper = Wrappers.lambdaQuery(EdurehabCoursesPlanDO.class)
                .eq(EdurehabCoursesPlanDO::getOrgCode, orgCode)
                .eq(EdurehabCoursesPlanDO::getEndDate, endDate)
                .eq(EdurehabCoursesPlanDO::getStartDate, startDate)
                .orderByDesc(EdurehabCoursesPlanDO::getAddTime).last("limit 1");
        EdurehabCoursesPlanDO planDO = edurehabCoursesPlanDao.selectOne(wrapper);
        if (Objects.isNull(planDO)) {
            return null;
        }
        return getEdurehabCoursesPlan(planDO.getId());
    }


}
