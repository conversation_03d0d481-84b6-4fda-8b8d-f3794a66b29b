package com.rs.module.acp.controller.admin.gj.vo.conflict;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-社会矛盾回访新增/修改 Request VO")
@Data
public class ConflictFollowupSaveReqVO extends BaseVO {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("事件编号")
    @NotBlank(message = "事件编号不能为空")
    private String eventCode;
    @ApiModelProperty("事件状态（01:已登记、02：待审批（所领导审批）、03: 审批不通过、04：调解中、05：调解完成、06：待确认、07：被退回）")
    private String regStatus;
    @ApiModelProperty("回访方式")
    @NotBlank(message = "回访方式不能为空")
    private String followUpWay;
    @ApiModelProperty("收到锦旗数")
    private Integer receivedBannersCnt;
    @ApiModelProperty("收到牌匾数")
    private Integer receivedPlaquesCnt;
    @ApiModelProperty("收到感谢信数")
    private Integer receivedThankYouLettersCnt;
    @ApiModelProperty("履行纠纷款项金额")
    private BigDecimal disputePaymentAmount;
    @ApiModelProperty("回访时间")
    private Date followUpTime;
    @ApiModelProperty("回访情况")
    @NotBlank(message = "回访情况不能为空")
    private String followUpInfo;
    @ApiModelProperty("回访经办人")
    @NotBlank(message = "回访经办人不能为空")
    private String followupOperatorSfzh;
    @ApiModelProperty("回访经办人姓名")
    @NotBlank(message = "回访经办人姓名不能为空")
    private String followupOperatorXm;
    @ApiModelProperty("回访经办时间")
    private Date followupOperatorTime;
    @ApiModelProperty("附件地址")
    private String attUrl;
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("登记人员")
    private List<ConflictPrisonerSaveReqVO> prisonerList;

}
