package com.rs.module.acp.dao.wb;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.VisitorRegDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-对外开放登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface VisitorRegDao extends IBaseDao<VisitorRegDO> {


    default PageResult<VisitorRegDO> selectPage(VisitorRegPageReqVO reqVO) {
        Page<VisitorRegDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<VisitorRegDO> wrapper = new LambdaQueryWrapperX<VisitorRegDO>()
            .betweenIfPresent(VisitorRegDO::getVisitTime, reqVO.getVisitTime())
            .eqIfPresent(VisitorRegDO::getBusinessType, reqVO.getBusinessType())
            .eqIfPresent(VisitorRegDO::getVisitReason, reqVO.getVisitReason())
            .eqIfPresent(VisitorRegDO::getVisitorType, reqVO.getVisitorType())
            .eqIfPresent(VisitorRegDO::getVisitArea, reqVO.getVisitArea())
            .eqIfPresent(VisitorRegDO::getRemark, reqVO.getRemark())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(VisitorRegDO::getAddTime);
        }
        Page<VisitorRegDO> visitorRegPage = selectPage(page, wrapper);
        return new PageResult<>(visitorRegPage.getRecords(), visitorRegPage.getTotal());
    }
    default List<VisitorRegDO> selectList(VisitorRegListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<VisitorRegDO>()
            .betweenIfPresent(VisitorRegDO::getVisitTime, reqVO.getVisitTime())
            .eqIfPresent(VisitorRegDO::getBusinessType, reqVO.getBusinessType())
            .eqIfPresent(VisitorRegDO::getVisitReason, reqVO.getVisitReason())
            .eqIfPresent(VisitorRegDO::getVisitorType, reqVO.getVisitorType())
            .eqIfPresent(VisitorRegDO::getVisitArea, reqVO.getVisitArea())
            .eqIfPresent(VisitorRegDO::getRemark, reqVO.getRemark())
        .orderByDesc(VisitorRegDO::getAddTime));    }


    }
