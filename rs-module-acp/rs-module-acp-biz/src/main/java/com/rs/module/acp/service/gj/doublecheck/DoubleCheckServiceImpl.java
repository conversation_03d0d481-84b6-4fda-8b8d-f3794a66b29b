package com.rs.module.acp.service.gj.doublecheck;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.module.acp.controller.admin.gj.vo.doublecheck.DoubleCheckListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.doublecheck.DoubleCheckPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.doublecheck.DoubleCheckSaveReqVO;
import com.rs.module.acp.util.CommonUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.DoubleCheckDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.DoubleCheckDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-双重检查登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DoubleCheckServiceImpl extends BaseServiceImpl<DoubleCheckDao, DoubleCheckDO> implements DoubleCheckService {

    @Resource
    private DoubleCheckDao doubleCheckDao;

    @Override
    public String createDoubleCheck(DoubleCheckSaveReqVO createReqVO) {
        // 插入
        DoubleCheckDO doubleCheck = BeanUtils.toBean(createReqVO, DoubleCheckDO.class);
        doubleCheckDao.insert(doubleCheck);
        // 返回
        return doubleCheck.getId();
    }

    @Override
    public void updateDoubleCheck(DoubleCheckSaveReqVO updateReqVO) {
        // 校验存在
        validateDoubleCheckExists(updateReqVO.getId());
        // 更新
        DoubleCheckDO updateObj = BeanUtils.toBean(updateReqVO, DoubleCheckDO.class);
        doubleCheckDao.updateById(updateObj);
    }

    @Override
    public void deleteDoubleCheck(String id) {
        // 校验存在
        validateDoubleCheckExists(id);
        // 删除
        doubleCheckDao.deleteById(id);
    }

    private void validateDoubleCheckExists(String id) {
        if (doubleCheckDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-双重检查登记数据不存在");
        }
    }

    @Override
    public DoubleCheckDO getDoubleCheck(String id) {
        return doubleCheckDao.selectById(id);
    }

    @Override
    public PageResult<DoubleCheckDO> getDoubleCheckPage(DoubleCheckPageReqVO pageReqVO) {
        return doubleCheckDao.selectPage(pageReqVO);
    }

    @Override
    public List<DoubleCheckDO> getDoubleCheckList(DoubleCheckListReqVO listReqVO) {
        return doubleCheckDao.selectList(listReqVO);
    }

    @Override
    public PageResult<DoubleCheckDO> getAppDoubleCheckPage(int pageNo, int pageSize, String operatePoliceSfzh, String type) {
        Page<DoubleCheckDO> page = new Page<>(pageNo, pageSize);
        Map<String, Date> commonAppRecordPeriod = CommonUtils.getCommonAppRecordPeriod(type);
        Page<DoubleCheckDO> result = doubleCheckDao.getAppDoubleCheckPage(page, operatePoliceSfzh,
                commonAppRecordPeriod.get("startTime"), commonAppRecordPeriod.get("endTime"));
        return new PageResult<>(result.getRecords(), result.getTotal());
    }


}
