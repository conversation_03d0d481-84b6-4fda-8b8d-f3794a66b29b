package com.rs.module.acp.controller.admin.db.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-入所健康检查登记列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class HealthCheckListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("人员编号")
    private String rybh;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("健康状况")
    private String jkzk;

    @ApiModelProperty("身高")
    private String sg;

    @ApiModelProperty("体重")
    private String tz;

    @ApiModelProperty("足长")
    private String zc;

    @ApiModelProperty("体温")
    private String tw;

    @ApiModelProperty("血型")
    private String xx;

    @ApiModelProperty("血压收缩压")
    private String xyssy;

    @ApiModelProperty("血压舒张压")
    private String xyszy;

    @ApiModelProperty("心率")
    private String xl;

    @ApiModelProperty("语言表达能力")
    private String yybdnl;

    @ApiModelProperty("语言表达能力具体情况")
    private String yybdnljtqk;

    @ApiModelProperty("肢体活动是否正常")
    private String zthdsfzc;

    @ApiModelProperty("肢体活动具体情况")
    private String zthdjtqk;

    @ApiModelProperty("视力状况是否正常")
    private String slzksfzc;

    @ApiModelProperty("视力具体情况")
    private String sljtqk;

    @ApiModelProperty("听力状况是否正常")
    private String tlzksfzc;

    @ApiModelProperty("听力状况具体情况")
    private String tlzkjtqk;

    @ApiModelProperty("智力状况是否正常")
    private String zlzksfzc;

    @ApiModelProperty("智力状况具体情况")
    private String zlzkjtqk;

    @ApiModelProperty("精神状况是否正常")
    private String jszksfzc;

    @ApiModelProperty("精神状况具体情况")
    private String jszkjtqk;

    @ApiModelProperty("血常规")
    private String xcgscdz;

    @ApiModelProperty("心电图")
    private String xdtscdz;

    @ApiModelProperty("B超")
    private String bcscdz;

    @ApiModelProperty("胸片")
    private String xpscdz;

    @ApiModelProperty("胸部CT")
    private String xbctscdz;

    @ApiModelProperty("女性妊娠检查结果")
    private String nxrzjcg;

    @ApiModelProperty("有无吸毒史")
    private String ywdxs;

    @ApiModelProperty("有无严重传染病")
    private String ywyzcbrb;

    @ApiModelProperty("严重传染病名称")
    private String yzcbrbmc;

    @ApiModelProperty("既往病史")
    private String jwbs;

    @ApiModelProperty("既往疾病类型")
    private String jwjblx;

    @ApiModelProperty("体表是否有外伤")
    private String tbsfywss;

    @ApiModelProperty("外伤情况记录方式")
    private String wsqkjlfs;

    @ApiModelProperty("外伤情况")
    private String wsqkscdz;

    @ApiModelProperty("致伤部位")
    private String zswb;

    @ApiModelProperty("致伤日期")
    private Date zsrq;

    @ApiModelProperty("致伤原因")
    private String zsyy;

    @ApiModelProperty("医生意见")
    private String ysyj;

    @ApiModelProperty("检查人身份证号")
    private String jcrsfzh;

    @ApiModelProperty("检查人")
    private String jcr;

    @ApiModelProperty("检查时间")
    private Date jcsj;

    @ApiModelProperty("备注")
    private String bz;

    @ApiModelProperty("办理状态")
    private String status;

    @ApiModelProperty("精神状态")
    private String jszt;

}
