package com.rs.module.acp.service.ds;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.StringUtil;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsSaveReqVO;
import com.rs.module.acp.entity.db.DetainRegKssDO;
import com.rs.module.acp.entity.db.InRecordJdsDO;
import com.rs.module.acp.entity.db.InRecordJlsDO;
import com.rs.module.acp.entity.db.OutRecordKssDO;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.service.pm.PrisonerService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.ds.vo.*;
import com.rs.module.acp.entity.ds.DSPrisonRoomChangeDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.ds.DSPrisonRoomChangeDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-数据固化-监所人员变动记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DSPrisonRoomChangeServiceImpl extends BaseServiceImpl<DSPrisonRoomChangeDao, DSPrisonRoomChangeDO> implements DSPrisonRoomChangeService {

    @Resource
    private DSPrisonRoomChangeDao dSPrisonRoomChangeDao;
    @Resource
    private PrisonerService prisonerService;
    @Override
    public String createPrisonRoomChange(PrisonRoomChangeSaveReqVO createReqVO) {
        // 插入
        DSPrisonRoomChangeDO prisonRoomChange = BeanUtils.toBean(createReqVO, DSPrisonRoomChangeDO.class);
        dSPrisonRoomChangeDao.insert(prisonRoomChange);
        // 返回
        return prisonRoomChange.getId();
    }

    @Override
    public void updatePrisonRoomChange(PrisonRoomChangeSaveReqVO updateReqVO) {
        // 校验存在
        validatePrisonRoomChangeExists(updateReqVO.getId());
        // 更新
        DSPrisonRoomChangeDO updateObj = BeanUtils.toBean(updateReqVO, DSPrisonRoomChangeDO.class);
        dSPrisonRoomChangeDao.updateById(updateObj);
    }

    @Override
    public void deletePrisonRoomChange(String id) {
        // 校验存在
        validatePrisonRoomChangeExists(id);
        // 删除
        dSPrisonRoomChangeDao.deleteById(id);
    }

    private void validatePrisonRoomChangeExists(String id) {
        if (dSPrisonRoomChangeDao.selectById(id) == null) {
            throw new ServerException("实战平台-数据固化-监所人员变动记录数据不存在");
        }
    }

    @Override
    public DSPrisonRoomChangeDO getPrisonRoomChange(String id) {
        return dSPrisonRoomChangeDao.selectById(id);
    }

    @Override
    public PageResult<DSPrisonRoomChangeDO> getPrisonRoomChangePage(PrisonRoomChangePageReqVO pageReqVO) {
        return dSPrisonRoomChangeDao.selectPage(pageReqVO);
    }

    @Override
    public List<DSPrisonRoomChangeDO> getPrisonRoomChangeList(PrisonRoomChangeListReqVO listReqVO) {
        return dSPrisonRoomChangeDao.selectList(listReqVO);
    }

    @Override
    public List<DSPrisonRoomChangeRespVO> getPrisonRoommates(DSPrisonRoommateQueryReqVO queryReqVO) {
        return dSPrisonRoomChangeDao.getPrisonRoommates(queryReqVO);
        /*// 使用自定义SQL查询同监室人员
        List<DSPrisonRoomChangeDO> targetList = dSPrisonRoomChangeDao.selectList(
                new LambdaQueryWrapperX<DSPrisonRoomChangeDO>()
                        .eq(DSPrisonRoomChangeDO::getJgrybm, queryReqVO.getJgrybm())
                        .eq(DSPrisonRoomChangeDO::getRoomId, queryReqVO.getRoomId())
                        .le(DSPrisonRoomChangeDO::getInRoomTime, queryReqVO.getEndTime())
                        .ge(DSPrisonRoomChangeDO::getOutRoomTime, queryReqVO.getStartTime())
        );

        if (targetList.isEmpty()) {
            return new ArrayList<>();
        }

        DSPrisonRoomChangeDO target = targetList.get(0);

        // 查询同一监室的其他人员
        List<DSPrisonRoomChangeDO> roommates = dSPrisonRoomChangeDao.selectList(
                new LambdaQueryWrapperX<DSPrisonRoomChangeDO>()
                        .eq(DSPrisonRoomChangeDO::getRoomId, target.getRoomId())
                        .ne(DSPrisonRoomChangeDO::getJgrybm, target.getJgrybm())
                        .le(DSPrisonRoomChangeDO::getInRoomTime, target.getOutRoomTime())
                        .ge(DSPrisonRoomChangeDO::getOutRoomTime, target.getInRoomTime())
        );

        // 转换为返回对象
        List<DSPrisonRoomChangeRespVO> result = new ArrayList<>();
        for (DSPrisonRoomChangeDO roommate : roommates) {
            DSPrisonRoomChangeRespVO respVO = new DSPrisonRoomChangeRespVO();
            respVO.setJgrybm(roommate.getJgrybm());
            respVO.setJgryxm(roommate.getJgryxm());
            respVO.setRoomId(roommate.getRoomId());
            respVO.setRoomName(roommate.getRoomName());
            respVO.setInRoomTime(roommate.getInRoomTime());
            respVO.setOutRoomTime(roommate.getOutRoomTime());

            // 计算共同在监室的时间段
            Date cohabitationStart = roommate.getInRoomTime().after(target.getInRoomTime()) ?
                    roommate.getInRoomTime() : target.getInRoomTime();
            Date cohabitationEnd = (roommate.getOutRoomTime() == null ||
                    (target.getOutRoomTime() != null && roommate.getOutRoomTime().after(target.getOutRoomTime()))) ?
                    target.getOutRoomTime() : roommate.getOutRoomTime();

            respVO.setCohabitationStart(cohabitationStart);
            respVO.setCohabitationEnd(cohabitationEnd);

            result.add(respVO);
        }

        return result;*/
    }

    @Override
    public String kssSave(DetainRegKssDO rsInfoDO){
        DSPrisonRoomChangeDO prisonRoomChange = new DSPrisonRoomChangeDO();
        prisonRoomChange.setJgrybm(rsInfoDO.getJgrybm());
        prisonRoomChange.setJgryxm(rsInfoDO.getXm());
        prisonRoomChange.setRoomId(rsInfoDO.getJsh());
        prisonRoomChange.setRoomName(rsInfoDO.getRoomName());
        prisonRoomChange.setInRoomTime(rsInfoDO.getRssj());
        prisonRoomChange.setInRoomBusinessId(rsInfoDO.getId());
        prisonRoomChange.setInRoomReason(rsInfoDO.getRsyy());
        prisonRoomChange.setBatchId(StringUtil.getGuid());
        prisonRoomChange.setInRoomType("0");
        dSPrisonRoomChangeDao.insert(prisonRoomChange);
        // 返回
        return prisonRoomChange.getId();
    }

    /**
     * 戒毒所-入所保存记录
     * @param rsInfoDO
     * @return
     * @throws Exception
     */
    @Override
    public String jdsSave(InRecordJdsDO rsInfoDO) {
        DSPrisonRoomChangeDO prisonRoomChange = new DSPrisonRoomChangeDO();
        prisonRoomChange.setJgrybm(rsInfoDO.getJgrybm());
        prisonRoomChange.setJgryxm(rsInfoDO.getXm());
        prisonRoomChange.setRoomId(rsInfoDO.getJsh());
        prisonRoomChange.setRoomName(rsInfoDO.getRoomName());
        prisonRoomChange.setInRoomTime(rsInfoDO.getDrjssj());
        prisonRoomChange.setInRoomBusinessId(rsInfoDO.getId());
        prisonRoomChange.setInRoomReason(rsInfoDO.getRsyy());
        prisonRoomChange.setBatchId(StringUtil.getGuid());
        prisonRoomChange.setInRoomType("0");
        dSPrisonRoomChangeDao.insert(prisonRoomChange);
        // 返回
        return prisonRoomChange.getId();
    }
    @Override
    public String jlsSave(InRecordJlsDO rsInfoDO) {
        DSPrisonRoomChangeDO prisonRoomChange = new DSPrisonRoomChangeDO();
        prisonRoomChange.setJgrybm(rsInfoDO.getJgrybm());
        prisonRoomChange.setJgryxm(rsInfoDO.getXm());
        prisonRoomChange.setRoomId(rsInfoDO.getJsh());
        prisonRoomChange.setRoomName(rsInfoDO.getRoomName());
        prisonRoomChange.setInRoomTime(rsInfoDO.getJbsj());//todo 时间需要处理 目前取的经办时间
        prisonRoomChange.setInRoomBusinessId(rsInfoDO.getId());
        prisonRoomChange.setInRoomReason(rsInfoDO.getRsyy());
        prisonRoomChange.setBatchId(StringUtil.getGuid());
        prisonRoomChange.setInRoomType("0");
        dSPrisonRoomChangeDao.insert(prisonRoomChange);
        // 返回
        return prisonRoomChange.getId();
    }
    /**
     * 监室调整带出-修改数据
     * @param outRecordsSaveReqVO
     * @throws Exception
     */
    @Override
    public void updatePrisonRoomChangeForOutPerson(InOutRecordsSaveReqVO outRecordsSaveReqVO){
        //查询带出人员的数据当前存在的outRoomTime 为空的最新在监记录
        DSPrisonRoomChangeDO dsPrisonRoomChangeDO = getPrisonLatestRoomChangeInfo(outRecordsSaveReqVO.getJgrybm(),outRecordsSaveReqVO.getRoomId());
        if (dsPrisonRoomChangeDO == null || StringUtil.isEmpty(dsPrisonRoomChangeDO.getRoomId())){
            throw new ServerException("实战平台-数据固化-监所人员变动记录数据不存在");
        }
        dsPrisonRoomChangeDO.setOutRoomTime(outRecordsSaveReqVO.getInoutTime());
        dsPrisonRoomChangeDO.setOutRoomBusinessId(outRecordsSaveReqVO.getBusinessId());
        dsPrisonRoomChangeDO.setOutRoomType("1");
        dsPrisonRoomChangeDO.setOutRoomReason(outRecordsSaveReqVO.getInoutReason());
        dSPrisonRoomChangeDao.updateById(dsPrisonRoomChangeDO);
    }

    /**
     * 查询人员监室最新的在监变动记录
     * @param jgrybm
     * @param roomId
     * @return
     */
    @Override
    public DSPrisonRoomChangeDO getPrisonLatestRoomChangeInfo(String jgrybm, String roomId) {
        return dSPrisonRoomChangeDao.selectOne(new LambdaQueryWrapper<>(DSPrisonRoomChangeDO.class)
                .eq(DSPrisonRoomChangeDO::getJgrybm, jgrybm)
                .eq(DSPrisonRoomChangeDO::getRoomId, roomId)
                .eq(DSPrisonRoomChangeDO::getIsDel, 0)
                .orderByDesc(DSPrisonRoomChangeDO::getInRoomTime));
    }

    /**
     * 监室调整带入-新增数据
     * @param inRecordsSaveReqVO
     * @param roomName
     * @param batchId
     * @throws Exception
     */
    @Override
    public void insertPrisonRoomChangeForOutPerson(InOutRecordsSaveReqVO inRecordsSaveReqVO,String roomName,String batchId) {
        DSPrisonRoomChangeDO dsPrisonRoomChangeDO = new DSPrisonRoomChangeDO();
        dsPrisonRoomChangeDO.setInRoomTime(inRecordsSaveReqVO.getInoutTime());
        dsPrisonRoomChangeDO.setInRoomBusinessId(inRecordsSaveReqVO.getBusinessId());
        dsPrisonRoomChangeDO.setInRoomType("1");
        dsPrisonRoomChangeDO.setInRoomReason(inRecordsSaveReqVO.getInoutReason());
        dsPrisonRoomChangeDO.setJgrybm(inRecordsSaveReqVO.getJgrybm());
        dsPrisonRoomChangeDO.setJgryxm(inRecordsSaveReqVO.getJgryxm());
        dsPrisonRoomChangeDO.setRoomId(inRecordsSaveReqVO.getRoomId());
        dsPrisonRoomChangeDO.setRoomName(roomName);
        dsPrisonRoomChangeDO.setBatchId(batchId);
        dSPrisonRoomChangeDao.insert(dsPrisonRoomChangeDO);
    }

    /**
     * 看守所表人员出所更新人员监室记录
     * @param outRecordKssDO
     */
    @Override
    public void updatePrisonRoomChangeForKssOutPrison(OutRecordKssDO outRecordKssDO){
        PrisonerInDO prisonerInDO = prisonerService.getPrisonerInOne(outRecordKssDO.getJgrybm());
        String roomId = "";
        if(null != prisonerInDO && StringUtil.isNotEmpty(prisonerInDO.getJsh())){
            roomId = prisonerInDO.getJsh();
        }
        DSPrisonRoomChangeDO dsPrisonRoomChangeDO = getPrisonLatestRoomChangeInfo(outRecordKssDO.getJgrybm(),roomId);
        if (dsPrisonRoomChangeDO == null || StringUtil.isEmpty(dsPrisonRoomChangeDO.getRoomId())){
            throw new ServerException("实战平台-数据固化-监所人员变动记录数据不存在");
        }
        dsPrisonRoomChangeDO.setOutRoomTime(outRecordKssDO.getCssj());
        dsPrisonRoomChangeDO.setOutRoomBusinessId(outRecordKssDO.getId());
        dsPrisonRoomChangeDO.setOutRoomType("0");
        dsPrisonRoomChangeDO.setOutRoomReason("");
        dSPrisonRoomChangeDao.updateById(dsPrisonRoomChangeDO);
    }

    /**
     * 获取指定人员在指定监室和时间段内的同监室人员列表，并按共同时间段分组
     *
     * @param queryReqVO 查询条件
     * @return 同监室人员列表，按共同时间段分组
     */
    @Override
    public List<DSPrisonRoomChangeRespVO> getPrisonRoommatesGrouped(DSPrisonRoommateQueryReqVO queryReqVO) {
        return dSPrisonRoomChangeDao.getPrisonRoommatesGrouped(queryReqVO);
    }
}
