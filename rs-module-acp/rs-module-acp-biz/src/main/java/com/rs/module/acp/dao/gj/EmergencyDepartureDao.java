package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.emergencydeparture.EmergencyDepartureListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.emergencydeparture.EmergencyDeparturePageReqVO;
import com.rs.module.acp.entity.gj.EmergencyDepartureDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 实战平台-管教业务-紧急出所登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface EmergencyDepartureDao extends IBaseDao<EmergencyDepartureDO> {


    default PageResult<EmergencyDepartureDO> selectPage(EmergencyDeparturePageReqVO reqVO) {
        Page<EmergencyDepartureDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<EmergencyDepartureDO> wrapper = new LambdaQueryWrapperX<EmergencyDepartureDO>()
            .eqIfPresent(EmergencyDepartureDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(EmergencyDepartureDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(EmergencyDepartureDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(EmergencyDepartureDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(EmergencyDepartureDO::getHospital, reqVO.getHospital())
            .eqIfPresent(EmergencyDepartureDO::getSymptomDesc, reqVO.getSymptomDesc())
            .eqIfPresent(EmergencyDepartureDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(EmergencyDepartureDO::getOperatePolice, reqVO.getOperatePolice())
            .betweenIfPresent(EmergencyDepartureDO::getOperateTime, reqVO.getOperateTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(EmergencyDepartureDO::getAddTime);
        }
        Page<EmergencyDepartureDO> emergencyDeparturePage = selectPage(page, wrapper);
        return new PageResult<>(emergencyDeparturePage.getRecords(), emergencyDeparturePage.getTotal());
    }
    default List<EmergencyDepartureDO> selectList(EmergencyDepartureListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<EmergencyDepartureDO>()
            .eqIfPresent(EmergencyDepartureDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(EmergencyDepartureDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(EmergencyDepartureDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(EmergencyDepartureDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(EmergencyDepartureDO::getHospital, reqVO.getHospital())
            .eqIfPresent(EmergencyDepartureDO::getSymptomDesc, reqVO.getSymptomDesc())
            .eqIfPresent(EmergencyDepartureDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(EmergencyDepartureDO::getOperatePolice, reqVO.getOperatePolice())
            .betweenIfPresent(EmergencyDepartureDO::getOperateTime, reqVO.getOperateTime())
        .orderByDesc(EmergencyDepartureDO::getAddTime));    }


    Page<EmergencyDepartureDO> getAppEmergencyDeparturePage(Page<EmergencyDepartureDO> page,
                                                            @Param("operatePoliceSfzh") String operatePoliceSfzh,
                                                            @Param("startTime") Date startTime,
                                                            @Param("endTime") Date endTime);
}
