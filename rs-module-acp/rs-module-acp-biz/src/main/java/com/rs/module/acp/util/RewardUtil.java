package com.rs.module.acp.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.spring.SpringUtils;
import com.rs.module.acp.entity.gj.RewardDO;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.concurrent.CompletableFuture;

@Slf4j
public class RewardUtil {


    /**
     * 发送奖励给购物给采买系统
     * <AUTHOR>
     * @date 2025/7/5 10:10
     * @param [punishmentDO]
     * @return java.lang.Boolean
     */
    public static void sendRewards( RewardDO punishmentDO){
        //异步处理， 发送奖励异常，不影响主流程
        CompletableFuture.runAsync( ()->{
            //02  购物采买奖励
            if(StrUtil.isNotBlank(punishmentDO.getRewardType()) && punishmentDO.getRewardType().contains("02")){
                String url = SpringUtils.getProperty("shopping.forbid.url");
                if(StrUtil.isBlank(url)){
                    return;
                }
                String timeoutStr = SpringUtils.getProperty("shopping.timeout", "10000");
                Integer timeout = Integer.parseInt( timeoutStr);
                Date endDate =  punishmentDO.getExecutionTime() ;
                if(endDate != null){
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("rewardStartTime", DateUtil.formatDateTime(endDate));
                    jsonObject.put("jgrybm", punishmentDO.getJgrybm());
                    jsonObject.put("orgCode", punishmentDO.getOrgCode());
                    jsonObject.put("orgName", punishmentDO.getOrgName());
                    jsonObject.put("rewardRoomLevel", "01");
                    String json = HttpUtil.post(url, jsonObject.toJSONString(), timeout);
                    CommonResult reuslt =  JSON.parseObject(json, CommonResult.class);
                    if(!reuslt.getSuccess()){
                        log.error("发送奖励异常！ {}" , JSON.toJSONString(reuslt));
                    }
                }
            }
        });


    }
}
