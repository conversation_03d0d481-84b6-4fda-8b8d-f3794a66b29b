package com.rs.module.acp.entity.gj;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-单独关押登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_alone_imprison")
@KeySequence("acp_gj_alone_imprison_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AloneImprisonDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 旧监室id
     */
    private String oldRoomId;
    /**
     * 新监室ID
     */
    private String newRoomId;
    /**
     * 旧监室名称
     */
    private String oldRoomName;
    /**
     * 新监室名称
     */
    private String newRoomName;
    /**
     * 单独关押原因（字典：ZD_DDGY_DJYY）"
     */
    private String registerReason;
    /**
     * 具体原因
     */
    private String specificReason;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 关入单独监室时间
     */
    private Date inTime;
    /**
     * 移出单独监室时间
     */
    private Date outTime;
    /**
     * 是否关联惩罚 0 否,1 是
     */
    private Integer isAssociatedPunishment;
    /**
     * 惩罚措施
     */
    private String punishmentMeasures;
    /**
     * 备注
     */
    private String remarks;

    /**
     * 报备状态（字典：ZD_DDGY_SPZT）
     */
    private String status;
    /**
     * 审批人身份证号
     */
    private String approverSfzh;
    /**
     * 审批人姓名
     */
    private String approverXm;
    /**
     * 审批时间
     */
    private Date approverTime;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 领导签名
     */
    private String approvalAutograph;
    /**
     * 领导签名日期
     */
    private Date approvalAutographTime;
    /**
     * 审核意见
     */
    private String approvalComments;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

}
