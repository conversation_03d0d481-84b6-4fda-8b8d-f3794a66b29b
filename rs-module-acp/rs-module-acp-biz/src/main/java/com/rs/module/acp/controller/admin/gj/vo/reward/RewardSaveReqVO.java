package com.rs.module.acp.controller.admin.gj.vo.reward;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 实战平台-管教业务-奖励管理新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RewardSaveReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    //@ApiModelProperty("主键")
    //private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("奖励类型  字典ZD_JLLX")
    @NotEmpty(message = "奖励类型不能为空")
    private String rewardType;

    @ApiModelProperty("奖励原因")
    @NotEmpty(message = "奖励原因不能为空")
    private String rewardReason;

    @ApiModelProperty("奖励内容")
    private String rewardContent;

    @ApiModelProperty("备注")
    private String remark;
    //
    //@ApiModelProperty("执行人")
    //private String executor;
    //
    //@ApiModelProperty("执行人身份证号")
    //private String executorSfzh;
    //
    //@ApiModelProperty("执行时间")
    //private Date executionTime;
    //
    //@ApiModelProperty("执行情况")
    //private String executeSituation;

    //@ApiModelProperty("状态")
    //@NotEmpty(message = "状态不能为空")
    //private String status;

    //@ApiModelProperty("ACT流程实例Id")
    //private String actInstId;
    //
    //@ApiModelProperty("任务ID")
    //private String taskId;

}
