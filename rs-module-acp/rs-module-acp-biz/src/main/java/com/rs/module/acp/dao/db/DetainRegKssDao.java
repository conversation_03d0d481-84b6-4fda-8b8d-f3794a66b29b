package com.rs.module.acp.dao.db;

import java.util.*;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.db.DetainRegKssDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
* 实战平台-羁押业务-看守所收押登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface DetainRegKssDao extends IBaseDao<DetainRegKssDO> {


    default PageResult<DetainRegKssDO> selectPage(DetainRegKssPageReqVO reqVO) {
        Page<DetainRegKssDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<DetainRegKssDO> wrapper = new LambdaQueryWrapperX<DetainRegKssDO>()
            .eqIfPresent(DetainRegKssDO::getXm, reqVO.getXm())
            .eqIfPresent(DetainRegKssDO::getXmpy, reqVO.getXmpy())
            .eqIfPresent(DetainRegKssDO::getBm, reqVO.getBm())
            .eqIfPresent(DetainRegKssDO::getXb, reqVO.getXb())
            .eqIfPresent(DetainRegKssDO::getCsrq, reqVO.getCsrq())
            .eqIfPresent(DetainRegKssDO::getZjlx, reqVO.getZjlx())
            .eqIfPresent(DetainRegKssDO::getZjhm, reqVO.getZjhm())
            .eqIfPresent(DetainRegKssDO::getGj, reqVO.getGj())
            .eqIfPresent(DetainRegKssDO::getMz, reqVO.getMz())
            .eqIfPresent(DetainRegKssDO::getHyzk, reqVO.getHyzk())
            .eqIfPresent(DetainRegKssDO::getJg, reqVO.getJg())
            .eqIfPresent(DetainRegKssDO::getZjxy, reqVO.getZjxy())
            .eqIfPresent(DetainRegKssDO::getHjd, reqVO.getHjd())
            .eqIfPresent(DetainRegKssDO::getHjdxz, reqVO.getHjdxz())
            .eqIfPresent(DetainRegKssDO::getXzz, reqVO.getXzz())
            .eqIfPresent(DetainRegKssDO::getXzzxz, reqVO.getXzzxz())
            .eqIfPresent(DetainRegKssDO::getWhcd, reqVO.getWhcd())
            .eqIfPresent(DetainRegKssDO::getZzmm, reqVO.getZzmm())
            .eqIfPresent(DetainRegKssDO::getZy, reqVO.getZy())
            .eqIfPresent(DetainRegKssDO::getGzdw, reqVO.getGzdw())
            .eqIfPresent(DetainRegKssDO::getZw, reqVO.getZw())
            .eqIfPresent(DetainRegKssDO::getZwjb, reqVO.getZwjb())
            .eqIfPresent(DetainRegKssDO::getTc, reqVO.getTc())
            .eqIfPresent(DetainRegKssDO::getSf, reqVO.getSf())
            .eqIfPresent(DetainRegKssDO::getTssf, reqVO.getTssf())
            .eqIfPresent(DetainRegKssDO::getXxhc, reqVO.getXxhc())
            .eqIfPresent(DetainRegKssDO::getSfwxzxs, reqVO.getSfwxzxs())
            .eqIfPresent(DetainRegKssDO::getXxmc, reqVO.getXxmc())
            .eqIfPresent(DetainRegKssDO::getGllb, reqVO.getGllb())
            .eqIfPresent(DetainRegKssDO::getAjlbdm, reqVO.getAjlbdm())
            .eqIfPresent(DetainRegKssDO::getAjlb, reqVO.getAjlb())
            .eqIfPresent(DetainRegKssDO::getAjbh, reqVO.getAjbh())
            .eqIfPresent(DetainRegKssDO::getRybh, reqVO.getRybh())
            .eqIfPresent(DetainRegKssDO::getBazxbh, reqVO.getBazxbh())
            .eqIfPresent(DetainRegKssDO::getTabh, reqVO.getTabh())
            .eqIfPresent(DetainRegKssDO::getSypz, reqVO.getSypz())
            .eqIfPresent(DetainRegKssDO::getSypzwsh, reqVO.getSypzwsh())
            .eqIfPresent(DetainRegKssDO::getSypzwsdz, reqVO.getSypzwsdz())
            .eqIfPresent(DetainRegKssDO::getRssj, reqVO.getRssj())
            .eqIfPresent(DetainRegKssDO::getRsyy, reqVO.getRsyy())
            .eqIfPresent(DetainRegKssDO::getSshj, reqVO.getSshj())
            .eqIfPresent(DetainRegKssDO::getSyjglx, reqVO.getSyjglx())
            .eqIfPresent(DetainRegKssDO::getSyjgmc, reqVO.getSyjgmc())
            .eqIfPresent(DetainRegKssDO::getSydw, reqVO.getSydw())
            .eqIfPresent(DetainRegKssDO::getSyr1, reqVO.getSyr1())
            .eqIfPresent(DetainRegKssDO::getSyrgh1, reqVO.getSyrgh1())
            .eqIfPresent(DetainRegKssDO::getSyrsj1, reqVO.getSyrsj1())
            .eqIfPresent(DetainRegKssDO::getSyr2, reqVO.getSyr2())
            .eqIfPresent(DetainRegKssDO::getSyrgh2, reqVO.getSyrgh2())
            .eqIfPresent(DetainRegKssDO::getSyrsj2, reqVO.getSyrsj2())
            .eqIfPresent(DetainRegKssDO::getBadwlx, reqVO.getBadwlx())
            .eqIfPresent(DetainRegKssDO::getBadw, reqVO.getBadw())
            .eqIfPresent(DetainRegKssDO::getBar, reqVO.getBar())
            .eqIfPresent(DetainRegKssDO::getBarlxff, reqVO.getBarlxff())
            .eqIfPresent(DetainRegKssDO::getBarxb, reqVO.getBarxb())
            .eqIfPresent(DetainRegKssDO::getBarzjlx, reqVO.getBarzjlx())
            .eqIfPresent(DetainRegKssDO::getBarzjhm, reqVO.getBarzjhm())
            .eqIfPresent(DetainRegKssDO::getBahj, reqVO.getBahj())
            .eqIfPresent(DetainRegKssDO::getJyrq, reqVO.getJyrq())
            .eqIfPresent(DetainRegKssDO::getJlrq, reqVO.getJlrq())
            .eqIfPresent(DetainRegKssDO::getDbrq, reqVO.getDbrq())
            .eqIfPresent(DetainRegKssDO::getGyqx, reqVO.getGyqx())
            .eqIfPresent(DetainRegKssDO::getFlwsh, reqVO.getFlwsh())
            .eqIfPresent(DetainRegKssDO::getJyaq, reqVO.getJyaq())
            .eqIfPresent(DetainRegKssDO::getSxzm, reqVO.getSxzm())
            .eqIfPresent(DetainRegKssDO::getXzhjaj, reqVO.getXzhjaj())
            .eqIfPresent(DetainRegKssDO::getXzhj, reqVO.getXzhj())
            .eqIfPresent(DetainRegKssDO::getZxf, reqVO.getZxf())
            .eqIfPresent(DetainRegKssDO::getDabh, reqVO.getDabh())
            .eqIfPresent(DetainRegKssDO::getAreaId, reqVO.getAreaId())
            .likeIfPresent(DetainRegKssDO::getAreaName, reqVO.getAreaName())
            .eqIfPresent(DetainRegKssDO::getJsh, reqVO.getJsh())
            .likeIfPresent(DetainRegKssDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(DetainRegKssDO::getCwh, reqVO.getCwh())
            .eqIfPresent(DetainRegKssDO::getZwbh, reqVO.getZwbh())
            .eqIfPresent(DetainRegKssDO::getFwbh, reqVO.getFwbh())
            .eqIfPresent(DetainRegKssDO::getJbr, reqVO.getJbr())
            .eqIfPresent(DetainRegKssDO::getJbsj, reqVO.getJbsj())
            .eqIfPresent(DetainRegKssDO::getSdnjccjg, reqVO.getSdnjccjg())
            .eqIfPresent(DetainRegKssDO::getSdnjdw, reqVO.getSdnjdw())
            .eqIfPresent(DetainRegKssDO::getSdnjccsj, reqVO.getSdnjccsj())
            .eqIfPresent(DetainRegKssDO::getSdnjjcr, reqVO.getSdnjjcr())
            .eqIfPresent(DetainRegKssDO::getBz, reqVO.getBz())
            .eqIfPresent(DetainRegKssDO::getFrontPhoto, reqVO.getFrontPhoto())
            .eqIfPresent(DetainRegKssDO::getStatus, reqVO.getStatus())
                .eqIfPresent(DetainRegKssDO::getHzwsh, reqVO.getHzwsh())
                .eqIfPresent(DetainRegKssDO::getYysSydw, reqVO.getYysSydw())
                .eqIfPresent(DetainRegKssDO::getYysSymj, reqVO.getYysSymj())
                .eqIfPresent(DetainRegKssDO::getYysSymjlxdh, reqVO.getYysSymjlxdh())
                .eqIfPresent(DetainRegKssDO::getYysBx, reqVO.getYysBx())
                .eqIfPresent(DetainRegKssDO::getSymj, reqVO.getSymj())
                .eqIfPresent(DetainRegKssDO::getSfpdyj, reqVO.getSfpdyj())
                .eqIfPresent(DetainRegKssDO::getSfpbsf, reqVO.getSfpbsf())
                .eqIfPresent(DetainRegKssDO::getDrjsr, reqVO.getDrjsr())
                .eqIfPresent(DetainRegKssDO::getDrjssj, reqVO.getDrjssj())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(DetainRegKssDO::getAddTime);
        }
        Page<DetainRegKssDO> detainRegKssPage = selectPage(page, wrapper);
        return new PageResult<>(detainRegKssPage.getRecords(), detainRegKssPage.getTotal());
    }
    default List<DetainRegKssDO> selectList(DetainRegKssListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DetainRegKssDO>()
            .eqIfPresent(DetainRegKssDO::getXm, reqVO.getXm())
            .eqIfPresent(DetainRegKssDO::getXmpy, reqVO.getXmpy())
            .eqIfPresent(DetainRegKssDO::getBm, reqVO.getBm())
            .eqIfPresent(DetainRegKssDO::getXb, reqVO.getXb())
            .eqIfPresent(DetainRegKssDO::getCsrq, reqVO.getCsrq())
            .eqIfPresent(DetainRegKssDO::getZjlx, reqVO.getZjlx())
            .eqIfPresent(DetainRegKssDO::getZjhm, reqVO.getZjhm())
            .eqIfPresent(DetainRegKssDO::getGj, reqVO.getGj())
            .eqIfPresent(DetainRegKssDO::getMz, reqVO.getMz())
            .eqIfPresent(DetainRegKssDO::getHyzk, reqVO.getHyzk())
            .eqIfPresent(DetainRegKssDO::getJg, reqVO.getJg())
            .eqIfPresent(DetainRegKssDO::getZjxy, reqVO.getZjxy())
            .eqIfPresent(DetainRegKssDO::getHjd, reqVO.getHjd())
            .eqIfPresent(DetainRegKssDO::getHjdxz, reqVO.getHjdxz())
            .eqIfPresent(DetainRegKssDO::getXzz, reqVO.getXzz())
            .eqIfPresent(DetainRegKssDO::getXzzxz, reqVO.getXzzxz())
            .eqIfPresent(DetainRegKssDO::getWhcd, reqVO.getWhcd())
            .eqIfPresent(DetainRegKssDO::getZzmm, reqVO.getZzmm())
            .eqIfPresent(DetainRegKssDO::getZy, reqVO.getZy())
            .eqIfPresent(DetainRegKssDO::getGzdw, reqVO.getGzdw())
            .eqIfPresent(DetainRegKssDO::getZw, reqVO.getZw())
            .eqIfPresent(DetainRegKssDO::getZwjb, reqVO.getZwjb())
            .eqIfPresent(DetainRegKssDO::getTc, reqVO.getTc())
            .eqIfPresent(DetainRegKssDO::getSf, reqVO.getSf())
            .eqIfPresent(DetainRegKssDO::getTssf, reqVO.getTssf())
            .eqIfPresent(DetainRegKssDO::getXxhc, reqVO.getXxhc())
            .eqIfPresent(DetainRegKssDO::getSfwxzxs, reqVO.getSfwxzxs())
            .eqIfPresent(DetainRegKssDO::getXxmc, reqVO.getXxmc())
            .eqIfPresent(DetainRegKssDO::getGllb, reqVO.getGllb())
            .eqIfPresent(DetainRegKssDO::getAjlbdm, reqVO.getAjlbdm())
            .eqIfPresent(DetainRegKssDO::getAjlb, reqVO.getAjlb())
            .eqIfPresent(DetainRegKssDO::getAjbh, reqVO.getAjbh())
            .eqIfPresent(DetainRegKssDO::getRybh, reqVO.getRybh())
            .eqIfPresent(DetainRegKssDO::getBazxbh, reqVO.getBazxbh())
            .eqIfPresent(DetainRegKssDO::getTabh, reqVO.getTabh())
            .eqIfPresent(DetainRegKssDO::getSypz, reqVO.getSypz())
            .eqIfPresent(DetainRegKssDO::getSypzwsh, reqVO.getSypzwsh())
            .eqIfPresent(DetainRegKssDO::getSypzwsdz, reqVO.getSypzwsdz())
            .eqIfPresent(DetainRegKssDO::getRssj, reqVO.getRssj())
            .eqIfPresent(DetainRegKssDO::getRsyy, reqVO.getRsyy())
            .eqIfPresent(DetainRegKssDO::getSshj, reqVO.getSshj())
            .eqIfPresent(DetainRegKssDO::getSyjglx, reqVO.getSyjglx())
            .eqIfPresent(DetainRegKssDO::getSyjgmc, reqVO.getSyjgmc())
            .eqIfPresent(DetainRegKssDO::getSydw, reqVO.getSydw())
            .eqIfPresent(DetainRegKssDO::getSyr1, reqVO.getSyr1())
            .eqIfPresent(DetainRegKssDO::getSyrgh1, reqVO.getSyrgh1())
            .eqIfPresent(DetainRegKssDO::getSyrsj1, reqVO.getSyrsj1())
            .eqIfPresent(DetainRegKssDO::getSyr2, reqVO.getSyr2())
            .eqIfPresent(DetainRegKssDO::getSyrgh2, reqVO.getSyrgh2())
            .eqIfPresent(DetainRegKssDO::getSyrsj2, reqVO.getSyrsj2())
            .eqIfPresent(DetainRegKssDO::getBadwlx, reqVO.getBadwlx())
            .eqIfPresent(DetainRegKssDO::getBadw, reqVO.getBadw())
            .eqIfPresent(DetainRegKssDO::getBar, reqVO.getBar())
            .eqIfPresent(DetainRegKssDO::getBarlxff, reqVO.getBarlxff())
            .eqIfPresent(DetainRegKssDO::getBarxb, reqVO.getBarxb())
            .eqIfPresent(DetainRegKssDO::getBarzjlx, reqVO.getBarzjlx())
            .eqIfPresent(DetainRegKssDO::getBarzjhm, reqVO.getBarzjhm())
            .eqIfPresent(DetainRegKssDO::getBahj, reqVO.getBahj())
            .eqIfPresent(DetainRegKssDO::getJyrq, reqVO.getJyrq())
            .eqIfPresent(DetainRegKssDO::getJlrq, reqVO.getJlrq())
            .eqIfPresent(DetainRegKssDO::getDbrq, reqVO.getDbrq())
            .eqIfPresent(DetainRegKssDO::getGyqx, reqVO.getGyqx())
            .eqIfPresent(DetainRegKssDO::getFlwsh, reqVO.getFlwsh())
            .eqIfPresent(DetainRegKssDO::getJyaq, reqVO.getJyaq())
            .eqIfPresent(DetainRegKssDO::getSxzm, reqVO.getSxzm())
            .eqIfPresent(DetainRegKssDO::getXzhjaj, reqVO.getXzhjaj())
            .eqIfPresent(DetainRegKssDO::getXzhj, reqVO.getXzhj())
            .eqIfPresent(DetainRegKssDO::getZxf, reqVO.getZxf())
            .eqIfPresent(DetainRegKssDO::getDabh, reqVO.getDabh())
            .eqIfPresent(DetainRegKssDO::getAreaId, reqVO.getAreaId())
            .likeIfPresent(DetainRegKssDO::getAreaName, reqVO.getAreaName())
            .eqIfPresent(DetainRegKssDO::getJsh, reqVO.getJsh())
            .likeIfPresent(DetainRegKssDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(DetainRegKssDO::getCwh, reqVO.getCwh())
            .eqIfPresent(DetainRegKssDO::getZwbh, reqVO.getZwbh())
            .eqIfPresent(DetainRegKssDO::getFwbh, reqVO.getFwbh())
            .eqIfPresent(DetainRegKssDO::getJbr, reqVO.getJbr())
            .eqIfPresent(DetainRegKssDO::getJbsj, reqVO.getJbsj())
            .eqIfPresent(DetainRegKssDO::getSdnjccjg, reqVO.getSdnjccjg())
            .eqIfPresent(DetainRegKssDO::getSdnjdw, reqVO.getSdnjdw())
            .eqIfPresent(DetainRegKssDO::getSdnjccsj, reqVO.getSdnjccsj())
            .eqIfPresent(DetainRegKssDO::getSdnjjcr, reqVO.getSdnjjcr())
            .eqIfPresent(DetainRegKssDO::getBz, reqVO.getBz())
            .eqIfPresent(DetainRegKssDO::getFrontPhoto, reqVO.getFrontPhoto())
            .eqIfPresent(DetainRegKssDO::getStatus, reqVO.getStatus())
                .eqIfPresent(DetainRegKssDO::getHzwsh, reqVO.getHzwsh())
                .eqIfPresent(DetainRegKssDO::getYysSydw, reqVO.getYysSydw())
                .eqIfPresent(DetainRegKssDO::getYysSymj, reqVO.getYysSymj())
                .eqIfPresent(DetainRegKssDO::getYysSymjlxdh, reqVO.getYysSymjlxdh())
                .eqIfPresent(DetainRegKssDO::getYysBx, reqVO.getYysBx())
                .eqIfPresent(DetainRegKssDO::getSymj, reqVO.getSymj())
                .eqIfPresent(DetainRegKssDO::getSfpdyj, reqVO.getSfpdyj())
                .eqIfPresent(DetainRegKssDO::getSfpbsf, reqVO.getSfpbsf())
                .eqIfPresent(DetainRegKssDO::getDrjsr, reqVO.getDrjsr())
                .eqIfPresent(DetainRegKssDO::getDrjssj, reqVO.getDrjssj())
        .orderByDesc(DetainRegKssDO::getAddTime));    }


    CombineRespVO getCombineInfo(String id);

    void syncDataToPrisonerKssIn(String id);

    /**
     * 修改在所表三面照信息
     * @param rybh 监管人员编码
     * @param frontPhoto 正面照片
     * @param leftPhoto 左侧照片
     * @param rightPhoto 右侧照片
     */
    @Update("UPDATE acp_pm_prisoner_kss_in SET front_photo = #{frontPhoto}, " +
            "left_photo = #{leftPhoto}, " +
            "right_photo = #{rightPhoto} " +
            "WHERE jgrybm = #{rybh} AND is_del = 0")
    void syncPhotoToPrisonerKssIn(String rybh, String frontPhoto, String leftPhoto, String rightPhoto);

    @InterceptorIgnore(illegalSql = "true")
    InRecordStatusVO getInRecordStatus(String rybh);

    void updateStepInfo(@Param("rybh")String rybh, @Param("status")String status,
                        @Param("currentStep")String currentStep, @Param("spzt")String spzt,
                        @Param("actInstId")String actInstId);

    String ifExistsInPrisonerKssIn(String rybh);

    void deleteExitPrisoner(String rybh);
}
