package com.rs.module.acp.service.db;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.DetainReclaimDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-羁押业务-收回登记 Service 接口
 *
 * <AUTHOR>
 */
public interface DetainReclaimService extends IBaseService<DetainReclaimDO>{

    /**
     * 创建实战平台-羁押业务-收回登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createDetainReclaim(@Valid DetainReclaimSaveReqVO createReqVO);

    /**
     * 更新实战平台-羁押业务-收回登记
     *
     * @param updateReqVO 更新信息
     */
    void updateDetainReclaim(@Valid DetainReclaimSaveReqVO updateReqVO);

    /**
     * 删除实战平台-羁押业务-收回登记
     *
     * @param id 编号
     */
    void deleteDetainReclaim(String id);

    /**
     * 获得实战平台-羁押业务-收回登记
     *
     * @param id 编号
     * @return 实战平台-羁押业务-收回登记
     */
    DetainReclaimDO getDetainReclaim(String id);

    /**
    * 获得实战平台-羁押业务-收回登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-羁押业务-收回登记分页
    */
    PageResult<DetainReclaimDO> getDetainReclaimPage(DetainReclaimPageReqVO pageReqVO);

    /**
    * 获得实战平台-羁押业务-收回登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-羁押业务-收回登记列表
    */
    List<DetainReclaimDO> getDetainReclaimList(DetainReclaimListReqVO listReqVO);


    DetainReclaimDO getByJgrybh(String rybh);
}
