package com.rs.module.acp.controller.app.gj;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.service.pm.DeviceDoorService;
import com.rs.third.api.enums.DoorControlTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.rs.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @Date 2025/7/24 20:38
 */
@Api(tags = "外屏-管教业务-门禁控制")
@RestController
@RequestMapping("/app/acp/deviceDoor")
@Validated
public class AppDoorControlController {

    @Resource
    private DeviceDoorService deviceDoorService;

    @ApiOperation(value = "监室门禁控制")
    @PostMapping("/controlRoomDoor")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roomId", value = "监室号ID"),
            @ApiImplicitParam(name = "controlType", value = "控制类型（1-门闭，2-门开）")
    })
    public CommonResult controlRoomDoor(String roomId, String controlType) {
        DoorControlTypeEnum typeEnum = DoorControlTypeEnum.getEnum(Integer.parseInt(controlType));
        if (typeEnum == null) {
            return CommonResult.error("控制类型错误");
        }
        return success(deviceDoorService.controlRoomDoor(roomId, typeEnum));
    }
}
