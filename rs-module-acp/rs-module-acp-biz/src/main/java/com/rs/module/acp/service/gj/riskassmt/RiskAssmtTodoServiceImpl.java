package com.rs.module.acp.service.gj.riskassmt;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.riskassmt.RiskAssmtTodoListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.riskassmt.RiskAssmtTodoPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.riskassmt.RiskAssmtTodoRespVO;
import com.rs.module.acp.controller.admin.gj.vo.riskassmt.RiskAssmtTodoSaveReqVO;
import com.rs.module.acp.dao.gj.RiskAssmtTodoDao;
import com.rs.module.acp.entity.gj.RiskAssmtTodoDO;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.entity.sys.MsgAddVO;
import com.rs.module.base.service.pm.AreaService;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.util.MsgUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 实战平台-管教业务-风险评估待办 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RiskAssmtTodoServiceImpl extends BaseServiceImpl<RiskAssmtTodoDao, RiskAssmtTodoDO> implements RiskAssmtTodoService {

    @Resource
    private RiskAssmtTodoDao riskAssmtTodoDao;

    @Resource
    private PrisonerService prisonerService;

    @Resource
    private AreaService areaService;

    //最大分页编码
    private final Integer MAX_PAGE_SIZE = 1000;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createRiskAssmtTodo(RiskAssmtTodoSaveReqVO createReqVO) {
        // 插入
        RiskAssmtTodoDO riskAssmtTodo = BeanUtils.toBean(createReqVO, RiskAssmtTodoDO.class);

        //
        PrisonerInDO prisoner=  prisonerService.getPrisonerInOne(createReqVO.getJgrybm() );
        riskAssmtTodo.setJgryxm(prisoner.getXm());
        riskAssmtTodo.setAreaId(prisoner.getAreaId());

        AreaDO areaDO =  areaService.getArea(prisoner.getAreaId());
        if(areaDO != null){
            riskAssmtTodo.setAreaName(areaDO.getAreaName());
        }
        riskAssmtTodo.setRoomId(prisoner.getJsh());
        riskAssmtTodo.setRoomName(prisoner.getRoomName());
        riskAssmtTodo.setPushTime(new Date());
        riskAssmtTodo.setStatus("0");
        riskAssmtTodo.setOldRiskLevel(prisoner.getFxdj());
        riskAssmtTodoDao.insert(riskAssmtTodo);
        // 返回
        return riskAssmtTodo.getId();
    }

    //@Override
    //public void updateRiskAssmtTodo(RiskAssmtTodoSaveReqVO updateReqVO) {
    //    // 校验存在
    //    validateRiskAssmtTodoExists(updateReqVO.getId());
    //    // 更新
    //    RiskAssmtTodoDO updateObj = BeanUtils.toBean(updateReqVO, RiskAssmtTodoDO.class);
    //    riskAssmtTodoDao.updateById(updateObj);
    //}

    @Override
    public void deleteRiskAssmtTodo(String id) {
        // 校验存在
        validateRiskAssmtTodoExists(id);
        // 删除
        riskAssmtTodoDao.deleteById(id);
    }

    private void validateRiskAssmtTodoExists(String id) {
        if (riskAssmtTodoDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-风险评估待办数据不存在");
        }
    }

    @Override
    public RiskAssmtTodoDO getRiskAssmtTodo(String id) {
        return riskAssmtTodoDao.selectById(id);
    }

    @Override
    public PageResult<RiskAssmtTodoDO> getRiskAssmtTodoPage(RiskAssmtTodoPageReqVO pageReqVO) {
        return riskAssmtTodoDao.selectPage(pageReqVO);
    }

    @Override
    public List<RiskAssmtTodoDO> getRiskAssmtTodoList(RiskAssmtTodoListReqVO listReqVO) {
        return riskAssmtTodoDao.selectList(listReqVO);
    }

    @Override
    public void newEntrantJob() {
        long totalPages = 1;
        for (long currentPage = 1; currentPage <= totalPages; currentPage++) {
            // 创建分页请求
            Page<RiskAssmtTodoRespVO> pageRequest = new Page<>(currentPage, MAX_PAGE_SIZE);
            // 执行分页查询
            IPage<RiskAssmtTodoRespVO> pageResult = riskAssmtTodoDao.getNewEntrantJob(pageRequest);
            // 更新总页数（首次查询后会获取真实值）
            totalPages = pageResult.getPages();
            // 处理当前页数据
            execute(pageResult, "ACP_GJ_RISK_MENT_IN_TODO", "");
        }

    }

    @Override
    public void attentionPersonnel() {
        long totalPages = 1;
        for (long currentPage = 1; currentPage <= totalPages; currentPage++) {
            // 创建分页请求
            Page<RiskAssmtTodoRespVO> pageRequest = new Page<>(currentPage, MAX_PAGE_SIZE);
            // 执行分页查询
            IPage<RiskAssmtTodoRespVO> pageResult = riskAssmtTodoDao.getAttentionPersonnel(pageRequest);
            // 更新总页数（首次查询后会获取真实值）
            totalPages = pageResult.getPages();
            // 处理当前页数据
            execute(pageResult, "ACP_GJ_RISK_ATTENTION_TODO", "");
        }
    }



    /**
     * 风险评估待评估执行
     * <AUTHOR>
     * @date 2025/6/19 19:10
     * @param [pageResult, moduleCode, url]
     * @return void
     */
    private void execute(  IPage<RiskAssmtTodoRespVO> pageResult,String moduleCode,String url ){
        try {
            List<RiskAssmtTodoRespVO> currentPageData = pageResult.getRecords();

            if(CollUtil.isNotEmpty(currentPageData)){
                List<RiskAssmtTodoDO> riskAssmtTodoList = new ArrayList<>();
                currentPageData.forEach( e->{
                    RiskAssmtTodoDO riskAssmtTodoDO = new RiskAssmtTodoDO();
                    BeanUtils.copyProperties(e, riskAssmtTodoDO);
                    riskAssmtTodoDO.setPushTime(new Date());
                    riskAssmtTodoDO.setStatus("0");
                    riskAssmtTodoList.add(riskAssmtTodoDO);
                });
                super.saveBatch(riskAssmtTodoList);
                CompletableFuture.runAsync(()->{
                    //TODO 发送待办消息
                    for (RiskAssmtTodoDO risk: riskAssmtTodoList) {
                        MsgAddVO vo = new MsgAddVO();
                        vo.setPcid(risk.getId());
                        //TODO 前端提供
                        vo.setUrl("#-------#");
                        vo.setMsgType("10");
                        vo.setModuleCode(moduleCode);
                        vo.setOrgCode(risk.getOrgCode());
                        vo.setOrgName(risk.getOrgName());
                        vo.setJgrybm(risk.getJgrybm());
                        vo.setToOrgCode(risk.getOrgCode());
                        vo.setBusinessId(risk.getId());
                        MsgUtil.sendMsg(vo);
                    }
                });
            }
        } catch (Exception e) {
            log.error("插入待风险评估信息异常", e);
        }

    }

}
