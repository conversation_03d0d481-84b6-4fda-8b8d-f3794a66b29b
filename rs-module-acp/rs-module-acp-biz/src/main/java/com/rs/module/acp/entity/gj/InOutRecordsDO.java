package com.rs.module.acp.entity.gj;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import lombok.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-出入登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_in_out_records")
@KeySequence("acp_gj_in_out_records_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_in_out_records")
public class InOutRecordsDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 数据来源（字典：ZD_DATA_SOURCES）
     */
    private String dataSources;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员编码
     */
    private String jgryxm;
    /**
     * 进出监室类型（字典：ZD_GJJCJSLX）
     */
    private String inoutType;
    /**
     * 状态
     */
    private String status;
    /**
     * 旧监室id
     */
    private String roomId;
    /**
     * 出入监室时间
     */
    private Date inoutTime;
    /**
     * 带出带入民警身份证号
     */
    private String inoutPoliceSfzh;
    /**
     * 业务类型（字典：ZD_GJXZDCSY）
     */
    private String businessType;
    /**
     * 带出带入民警
     */
    private String inoutPolice;
    /**
     * 出入事由
     */
    private String inoutReason;
    /**
     * 检查结果
     */
    private String inspectionResult;
    /**
     * 业务ID
     */
    private String businessId;
    /**
     * 违禁物品登记
     */
    private String prohibitedItems;
    /**
     * 管教民警身份证号
     */
    private String supervisorPoliceSfzh;
    /**
     * 体表检查登记
     */
    private String physicalExam;
    /**
     * 管教民警姓名
     */
    private String supervisorPolice;
    /**
     * 异常情况登记
     */
    private String abnormalSituations;
    /**
     * 管教民警人脸信息存储路径
     */
    private String supervisorFaceInfoPath;
    /**
     * 违禁物品登记照片存储地址
     */
    private String prohibitedItemsImgUrl;
    /**
     * 被监管人员人脸信息存储路径
     */
    private String prisonerFaceInfoPath;
    /**
     * 体表检查登记照片存储地址
     */
    private String physicalExamImgUrl;
    /**
     * 异常情况登记照片存储地址
     */
    private String abnormalSituationsImgUrl;
    /**
     * 是否查出违禁物品
     */
    private Short isProhibitedItems;
    /**
     * 检查时间
     */
    private Date inspectionTime;
    /**
     * 检查民警身份证号
     */
    private String inspectorSfzh;
    /**
     * 检查民警身份证号
     */
    private String inspector;
    /**
     * 带出记录id
     */
    private String outBusinessId;
    /**
     * 业务类型子类型 ZD_GJXZDCSY_ZL
     */
    private String businessSubType;
}
