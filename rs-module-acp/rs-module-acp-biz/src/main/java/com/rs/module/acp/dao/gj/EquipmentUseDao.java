package com.rs.module.acp.dao.gj;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseRespWearVO;
import com.rs.module.acp.entity.gj.EquipmentUseDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* 实战平台-管教业务-械具使用 Dao
*
* <AUTHOR>
*/
@Mapper
public interface EquipmentUseDao extends IBaseDao<EquipmentUseDO> {

    
    /**
     * 戒具使用状态更新
     * <AUTHOR>
     * @date 2025/6/7 19:47
     * @param [nowDate]
     * @return int
     */
    int updateRestraintsExaminationRemainsLifted(@Param("nowDate")Date nowDate);


    /**
     * 获取正在佩戴戒具的人，给王龙提供，他说不用分页的，内存溢出，找他，狗头保命
     * <AUTHOR>
     * @date 2025/6/18 10:25
     * @param [startTime, endTime]
     * @return java.util.List<com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseRespWearVO>
     */
    List<EquipmentUseRespWearVO> getWearEquipmentUse(@Param("startTime") Date startTime,@Param("endTime") Date endTime);

}
