package com.rs.module.acp.dao.gj;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverRecordVO;
import com.rs.module.acp.controller.admin.wb.vo.LawyerMeetingRespVO;
import com.rs.module.acp.entity.gj.UndercoverDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 实战平台-管教业务-耳目管理 Dao
*
* <AUTHOR>
*/
@Mapper
public interface UndercoverDao extends IBaseDao<UndercoverDO> {


    default PageResult<UndercoverDO> selectPage(UndercoverPageReqVO reqVO) {
        Page<UndercoverDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<UndercoverDO> wrapper = new LambdaQueryWrapperX<UndercoverDO>()
            .eqIfPresent(UndercoverDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(UndercoverDO::getArrangeReason, reqVO.getArrangeReason())
            .eqIfPresent(UndercoverDO::getStatus, reqVO.getStatus())
            .eqIfPresent(UndercoverDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(UndercoverDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(UndercoverDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(UndercoverDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(UndercoverDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(UndercoverDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(UndercoverDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(UndercoverDO::getAddTime);
        }
        Page<UndercoverDO> undercoverPage = selectPage(page, wrapper);
        return new PageResult<>(undercoverPage.getRecords(), undercoverPage.getTotal());
    }
    default List<UndercoverDO> selectList(UndercoverListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<UndercoverDO>()
            .eqIfPresent(UndercoverDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(UndercoverDO::getArrangeReason, reqVO.getArrangeReason())
            .eqIfPresent(UndercoverDO::getStatus, reqVO.getStatus())
            .eqIfPresent(UndercoverDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(UndercoverDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(UndercoverDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(UndercoverDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(UndercoverDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(UndercoverDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(UndercoverDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(UndercoverDO::getAddTime));    }

    /**
     * 获取耳目管理-信息管理 人员信息
     * @param jgrybm
     * @return
     */
    List<UndercoverRecordVO> getUndercoverRespVOByJgrybm(@Param("page") Page<UndercoverRecordVO> page, @Param("jgrybm") String jgrybm);


}
