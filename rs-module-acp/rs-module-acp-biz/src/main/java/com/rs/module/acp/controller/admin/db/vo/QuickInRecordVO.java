package com.rs.module.acp.controller.admin.db.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 快速入所登记 Request VO
 */
@ApiModel(description = "管理后台 - 实战平台-羁押业务-快速入所登记 Request VO")
@Data
public class QuickInRecordVO extends BraceletBindingSaveReqVO {

    @ApiModelProperty(value = "入所类型", required = true,hidden = true)
//    @NotEmpty(message = "入所类型不能为空")
    private String rslx;
}

