package com.rs.module.acp.controller.admin.db.vo.third.haixin;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FingerVO implements TransPojo {

    private String id;

    @ApiModelProperty("图片url")
    private String url;

    @ApiModelProperty("是否强制通过")
    @Trans(type = TransType.DICTIONARY,key = "ZD_TYSFDM")
    private String forceFlag;

    @ApiModelProperty("缺指情况(1:缺失,3:受伤)")
    @Trans(type = TransType.DICTIONARY,key = "ZD_HX_ZWCJ_QZQK")
    private String qsqkdm;

    @ApiModelProperty("图象质量")
    private String txzl;

    @ApiModelProperty("指纹代码")
    @Trans(type = TransType.DICTIONARY,key = "ZD_HX_ZWCJ_ZWDM ")
    private String zwdm;

}
