package com.rs.module.acp.controller.admin.db.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-留所服刑 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ServeSentenceRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("留所原因")
    @Trans(type = TransType.DICTIONARY, key = "ZD_DB_LSYY")
    private String detainReason;
    @ApiModelProperty("留所原因详情")
    private String detainReasonDetails;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("申请时间")
    private Date operateTime;
    @ApiModelProperty("经办民警身份证号")
    private String operatePoliceSfzh;
    @ApiModelProperty("申请人")
    private String operatePolice;
    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_DB_LSFXZT")
    private String status;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("经办时间")
    private Date addTime;

    @ApiModelProperty("经办人")
    private String addUserName;
}
