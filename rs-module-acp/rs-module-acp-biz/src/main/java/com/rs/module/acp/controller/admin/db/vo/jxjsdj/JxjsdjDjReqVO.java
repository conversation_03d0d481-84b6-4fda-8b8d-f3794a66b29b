package com.rs.module.acp.controller.admin.db.vo.jxjsdj;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-减刑登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class JxjsdjDjReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("业务类型(1、减刑；2、假释)")
    @NotEmpty(message = "业务类型(1、减刑；2、假释)不能为空")
    private String ywlx;

    @ApiModelProperty("减刑原因")
    @NotEmpty(message = "减刑原因不能为空")
    private String jxyy;

    @ApiModelProperty("悔改表现")
    private String hgbx;

    @ApiModelProperty("立功表现")
    private String lgbx;

    @ApiModelProperty("重大立功表现")
    private String zdlgbx;

    @ApiModelProperty("其他原因")
    private String qtyy;

    @ApiModelProperty("备注")
    private String bz;

    @ApiModelProperty("附件地址")
    private String attUrl;

}
