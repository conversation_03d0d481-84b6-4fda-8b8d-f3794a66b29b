package com.rs.module.acp.entity.gj;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-奖励管理 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_reward")
@KeySequence("acp_gj_reward_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_reward")
public class RewardDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 奖励类型
     */
    private String rewardType;
    /**
     * 奖励原因
     */
    private String rewardReason;
    /**
     * 奖励内容
     */
    private String rewardContent;
    /**
     * 备注
     */
    private String remark;
    /**
     * 执行人
     */
    private String executor;
    /**
     * 执行人身份证号
     */
    private String executorSfzh;
    /**
     * 执行时间
     */
    private Date executionTime;
    /**
     * 执行情况
     */
    private String executeSituation;
    /**
     * 状态
     */
    private String status;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

}
