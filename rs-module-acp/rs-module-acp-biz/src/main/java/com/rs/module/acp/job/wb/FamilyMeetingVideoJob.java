package com.rs.module.acp.job.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.module.acp.entity.wb.FamilyMeetingDO;
import com.rs.module.acp.entity.wb.FamilyMeetingVideoDO;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.acp.service.wb.FamilyMeetingService;
import com.rs.module.acp.service.wb.FamilyMeetingVideoService;
import com.rs.module.acp.service.wb.WbCommonService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class FamilyMeetingVideoJob {

    @Autowired
    private FamilyMeetingVideoService familyMeetingVideoService;

    @Autowired
    private WbCommonService wbCommonService;


    /**
     * 当通知会见日期已结束已过凌晨12点，状态还未变更为【已会见】，则状态更新为【异常】
     */
    @XxlJob("abnormalFamilyMeetingVideo")
    public void abnormalFamilyMeetingVideo(){

        XxlJobHelper.log("家属单向视频会见异常任务：-----开始-------");
        XxlJobHelper.log("家属单向视频会见异常任务：当通知会见日期已结束已过凌晨12点，状态还未变更为【已会见】，则状态更新为【异常】");
        try {
            Date queryTime = DateUtil.parse(DateUtil.format(DateUtil.yesterday(),"yyyy-MM-dd")+" 23:59:59","yyyy-MM-dd HH:mm:ss");
            LambdaQueryWrapper<FamilyMeetingVideoDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.select(FamilyMeetingVideoDO::getId,FamilyMeetingVideoDO::getJgrybm,
                    FamilyMeetingVideoDO::getNotificationMeetingDate);
            lambdaQueryWrapper.in(FamilyMeetingVideoDO::getStatus, Arrays.asList("0","1"))
                    .le(FamilyMeetingVideoDO::getNotificationMeetingDate,queryTime);
            Integer pageNo = 1;
            Integer pageSize = 500;
            while (true){
                Page<FamilyMeetingVideoDO> page = new Page<>(pageNo,pageSize);
                IPage<FamilyMeetingVideoDO> familyMeetingVideoDOIPage = familyMeetingVideoService.page(page,lambdaQueryWrapper);
                if(CollectionUtil.isEmpty(familyMeetingVideoDOIPage.getRecords())){
                    break;
                }
                List<String> idList = familyMeetingVideoDOIPage.getRecords().stream().map(FamilyMeetingVideoDO::getId).collect(Collectors.toList());
                LambdaUpdateWrapper<FamilyMeetingVideoDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.in(FamilyMeetingVideoDO::getId,idList).set(FamilyMeetingVideoDO::getStatus,"99");
                familyMeetingVideoService.update(lambdaUpdateWrapper);

                //发送消息
                for(FamilyMeetingVideoDO familyMeetingVideoDO:familyMeetingVideoDOIPage.getRecords()){
                    XxlJobHelper.log("家属单向视频会见：-----发送补录待办消息---start----");
                    wbCommonService.additionalRecordingReminder(JSONObject.parseObject(JSON.toJSONString(familyMeetingVideoDO)), WbConstants.BUSINESS_TYPE_FAMILY_MEETING_VIDEO);
                    XxlJobHelper.log("家属单向视频会见：-----发送补录待办消息----end---");
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            XxlJobHelper.log("家属单向视频会见异常任务异常：{}",e.getMessage());
        }
        XxlJobHelper.log("家属单向视频会见异常任务:-----结束------");
    }

}
