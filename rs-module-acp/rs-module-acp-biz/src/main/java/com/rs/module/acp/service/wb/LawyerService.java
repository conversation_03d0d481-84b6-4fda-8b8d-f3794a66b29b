package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.LawyerDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-律师 Service 接口
 *
 * <AUTHOR>
 */
public interface LawyerService extends IBaseService<LawyerDO>{

    /**
     * 创建实战平台-窗口业务-律师
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createLawyer(@Valid LawyerSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-律师
     *
     * @param updateReqVO 更新信息
     */
    void updateLawyer(@Valid LawyerSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-律师
     *
     * @param id 编号
     */
    void deleteLawyer(String id);

    /**
     * 获得实战平台-窗口业务-律师
     *
     * @param id 编号
     * @return 实战平台-窗口业务-律师
     */
    LawyerDO getLawyer(String id);

    /**
    * 获得实战平台-窗口业务-律师分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-律师分页
    */
    PageResult<LawyerDO> getLawyerPage(LawyerPageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-律师列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-律师列表
    */
    List<LawyerDO> getLawyerList(LawyerListReqVO listReqVO);

    /**
     * 获得实战平台-窗口业务-获取监管人可选择的律师
     *
     * @param jgrybm 监管人员编码
     * @return 实战平台-窗口业务-律师列表
     */
    List<LawyerSelectVO> getLawyerListByJgrybm(String jgrybm);

    /**
     * 获得实战平台-窗口业务-校验被监管人员最多能关联2位正式律师
     *
     * @param jgrybm 被监管人员编码
     */
    boolean checkJgryAssociation(String jgrybm);
    /**
     * 获得实战平台-窗口业务-分页获取监管人可选择的律师
     *
     * @param pageReqVO 分页查询
     * @return 实战平台-窗口业务-律师列表
     */
    PageResult<LawyerSelectVO> getLawyerPageByJgrybm(LawyerPageReqVO pageReqVO);
}
