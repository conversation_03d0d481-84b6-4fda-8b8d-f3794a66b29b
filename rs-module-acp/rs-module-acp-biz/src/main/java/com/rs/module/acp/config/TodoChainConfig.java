package com.rs.module.acp.config;

import com.rs.module.acp.service.todo.TodoHandler;
import com.rs.module.acp.service.todo.TodoHandlerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class TodoChainConfig {

    @Bean
    public TodoHandlerFactory todoHandlerFactory(List<TodoHandler> handlers) {
        return new TodoHandlerFactory(handlers);
    }
}
