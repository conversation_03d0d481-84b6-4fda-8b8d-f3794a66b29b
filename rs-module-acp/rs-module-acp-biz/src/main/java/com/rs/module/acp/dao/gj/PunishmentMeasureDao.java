package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentMeasureListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentMeasurePageReqVO;
import com.rs.module.acp.entity.gj.PunishmentMeasureDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-处罚呈批关联措施 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PunishmentMeasureDao extends IBaseDao<PunishmentMeasureDO> {


    default PageResult<PunishmentMeasureDO> selectPage(PunishmentMeasurePageReqVO reqVO) {
        Page<PunishmentMeasureDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PunishmentMeasureDO> wrapper = new LambdaQueryWrapperX<PunishmentMeasureDO>()
            .eqIfPresent(PunishmentMeasureDO::getPunishmentId, reqVO.getPunishmentId())
            .eqIfPresent(PunishmentMeasureDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PunishmentMeasureDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(PunishmentMeasureDO::getMeasures, reqVO.getMeasures())
            .betweenIfPresent(PunishmentMeasureDO::getStartDate, reqVO.getStartDate())
            .betweenIfPresent(PunishmentMeasureDO::getEndDate, reqVO.getEndDate())
            .eqIfPresent(PunishmentMeasureDO::getDuration, reqVO.getDuration())
            .eqIfPresent(PunishmentMeasureDO::getBusinessId, reqVO.getBusinessId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PunishmentMeasureDO::getAddTime);
        }
        Page<PunishmentMeasureDO> punishmentMeasurePage = selectPage(page, wrapper);
        return new PageResult<>(punishmentMeasurePage.getRecords(), punishmentMeasurePage.getTotal());
    }
    default List<PunishmentMeasureDO> selectList(PunishmentMeasureListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PunishmentMeasureDO>()
            .eqIfPresent(PunishmentMeasureDO::getPunishmentId, reqVO.getPunishmentId())
            .eqIfPresent(PunishmentMeasureDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PunishmentMeasureDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(PunishmentMeasureDO::getMeasures, reqVO.getMeasures())
            .betweenIfPresent(PunishmentMeasureDO::getStartDate, reqVO.getStartDate())
            .betweenIfPresent(PunishmentMeasureDO::getEndDate, reqVO.getEndDate())
            .eqIfPresent(PunishmentMeasureDO::getDuration, reqVO.getDuration())
            .eqIfPresent(PunishmentMeasureDO::getBusinessId, reqVO.getBusinessId())
        .orderByDesc(PunishmentMeasureDO::getAddTime));    }


    }
