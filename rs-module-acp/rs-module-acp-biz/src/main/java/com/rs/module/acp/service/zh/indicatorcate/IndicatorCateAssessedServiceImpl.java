package com.rs.module.acp.service.zh.indicatorcate;

import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateAssessedListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateAssessedPageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateAssessedSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.zh.IndicatorCateAssessedDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.zh.IndicatorCateAssessedDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 综合管理-绩效考核指标分类与被考评人关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class IndicatorCateAssessedServiceImpl extends BaseServiceImpl<IndicatorCateAssessedDao, IndicatorCateAssessedDO> implements IndicatorCateAssessedService {

    @Resource
    private IndicatorCateAssessedDao indicatorCateAssessedDao;

    @Override
    public String createIndicatorCateAssessed(IndicatorCateAssessedSaveReqVO createReqVO) {
        // 插入
        IndicatorCateAssessedDO indicatorCateAssessed = BeanUtils.toBean(createReqVO, IndicatorCateAssessedDO.class);
        indicatorCateAssessedDao.insert(indicatorCateAssessed);
        // 返回
        return indicatorCateAssessed.getId();
    }

    @Override
    public void updateIndicatorCateAssessed(IndicatorCateAssessedSaveReqVO updateReqVO) {
        // 校验存在
        validateIndicatorCateAssessedExists(updateReqVO.getId());
        // 更新
        IndicatorCateAssessedDO updateObj = BeanUtils.toBean(updateReqVO, IndicatorCateAssessedDO.class);
        indicatorCateAssessedDao.updateById(updateObj);
    }

    @Override
    public void deleteIndicatorCateAssessed(String id) {
        // 校验存在
        validateIndicatorCateAssessedExists(id);
        // 删除
        indicatorCateAssessedDao.deleteById(id);
    }

    private void validateIndicatorCateAssessedExists(String id) {
        if (indicatorCateAssessedDao.selectById(id) == null) {
            throw new ServerException("综合管理-绩效考核指标分类与被考评人关联数据不存在");
        }
    }

    @Override
    public IndicatorCateAssessedDO getIndicatorCateAssessed(String id) {
        return indicatorCateAssessedDao.selectById(id);
    }

    @Override
    public PageResult<IndicatorCateAssessedDO> getIndicatorCateAssessedPage(IndicatorCateAssessedPageReqVO pageReqVO) {
        return indicatorCateAssessedDao.selectPage(pageReqVO);
    }

    @Override
    public List<IndicatorCateAssessedDO> getIndicatorCateAssessedList(IndicatorCateAssessedListReqVO listReqVO) {
        return indicatorCateAssessedDao.selectList(listReqVO);
    }


}
