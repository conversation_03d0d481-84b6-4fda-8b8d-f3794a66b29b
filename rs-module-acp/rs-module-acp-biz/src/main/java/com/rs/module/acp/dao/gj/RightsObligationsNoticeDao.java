package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.rightsobligationsnotice.RightsObligationsNoticeListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.rightsobligationsnotice.RightsObligationsNoticePageReqVO;
import com.rs.module.acp.entity.gj.RightsObligationsNoticeDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-权力义务告知书 Dao
*
* <AUTHOR>
*/
@Mapper
public interface RightsObligationsNoticeDao extends IBaseDao<RightsObligationsNoticeDO> {


    default PageResult<RightsObligationsNoticeDO> selectPage(RightsObligationsNoticePageReqVO reqVO) {
        Page<RightsObligationsNoticeDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<RightsObligationsNoticeDO> wrapper = new LambdaQueryWrapperX<RightsObligationsNoticeDO>()
            .eqIfPresent(RightsObligationsNoticeDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(RightsObligationsNoticeDO::getSignUrl, reqVO.getSignUrl())
            .betweenIfPresent(RightsObligationsNoticeDO::getSignTime, reqVO.getSignTime())
            .eqIfPresent(RightsObligationsNoticeDO::getFingerprintUrl, reqVO.getFingerprintUrl())
            .eqIfPresent(RightsObligationsNoticeDO::getJgryxm, reqVO.getJgryxm())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(RightsObligationsNoticeDO::getAddTime);
        }
        Page<RightsObligationsNoticeDO> rightsObligationsNoticePage = selectPage(page, wrapper);
        return new PageResult<>(rightsObligationsNoticePage.getRecords(), rightsObligationsNoticePage.getTotal());
    }
    default List<RightsObligationsNoticeDO> selectList(RightsObligationsNoticeListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<RightsObligationsNoticeDO>()
            .eqIfPresent(RightsObligationsNoticeDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(RightsObligationsNoticeDO::getSignUrl, reqVO.getSignUrl())
            .betweenIfPresent(RightsObligationsNoticeDO::getSignTime, reqVO.getSignTime())
            .eqIfPresent(RightsObligationsNoticeDO::getFingerprintUrl, reqVO.getFingerprintUrl())
            .eqIfPresent(RightsObligationsNoticeDO::getJgryxm, reqVO.getJgryxm())
        .orderByDesc(RightsObligationsNoticeDO::getAddTime));    }


    }
