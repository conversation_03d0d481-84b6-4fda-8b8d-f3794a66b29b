package com.rs.module.acp.entity.gj;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-文明个人登记明细 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_civilized_personne_detail")
@KeySequence("acp_gj_civilized_personne_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_civilized_personne_detail")
public class CivilizedPersonneDetailDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 文明个人登记ID
     */
    @ApiModelProperty("文明个人登记ID")
    private String civilizedPersonneId;
    /**
     * 监管人员编码
     */
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    /**
     * room_id
     */
    @ApiModelProperty("room_id")
    private String roomId;
    /**
     * 监室名称
     */
    @ApiModelProperty("监室名称")
    private String roomName;
    /**
     * 奖励次数
     */
    @ApiModelProperty("奖励次数")
    private Integer numberOfReward;
    /**
     * 违规次数
     */
    @ApiModelProperty("违规次数")
    private Integer numberOfViolations;
    /**
     * 惩罚次数
     */
    @ApiModelProperty("惩罚次数")
    private Integer numberOfPunishment;
    /**
     * 评选理由
     */
    @ApiModelProperty("评选理由")
    private String selectionReason;
    /**
     * 附件地址
     */
    @ApiModelProperty("附件地址")
    private String attUrl;

}
