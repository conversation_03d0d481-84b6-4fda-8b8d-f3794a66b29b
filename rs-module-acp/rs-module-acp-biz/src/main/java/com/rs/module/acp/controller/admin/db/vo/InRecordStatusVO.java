package com.rs.module.acp.controller.admin.db.vo;

import lombok.Data;

/**
 * 入所记录状态数据对象
 */
@Data
public class InRecordStatusVO {
    /**
     * 当前步骤
     */
    private String currentStep;

    /**
     * 入所登记状态
     */
    private String rsdj;

    /**
     * 健康状况状态
     */
    private String jkjc;

    /**
     * 个人物品状态
     */
    private String wpgl;

    /**
     * 生物特征状态
     */
    private String xxcj;
}

