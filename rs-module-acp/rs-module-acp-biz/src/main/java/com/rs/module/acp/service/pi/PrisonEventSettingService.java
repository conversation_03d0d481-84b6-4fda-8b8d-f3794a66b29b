package com.rs.module.acp.service.pi;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventSettingListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventSettingPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventSettingRespVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventSettingSaveReqVO;
import com.rs.module.acp.entity.pi.PrisonEventSettingDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-巡视管控-所警情管理-报警联动设置(所情来源) Service 接口
 *
 * <AUTHOR>
 */
public interface PrisonEventSettingService extends IBaseService<PrisonEventSettingDO>{

    /**
     * 创建实战平台-巡视管控-所警情管理-报警联动设置(所情来源)
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrisonEventSetting(@Valid PrisonEventSettingSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-所警情管理-报警联动设置(所情来源)
     *
     * @param updateReqVO 更新信息
     */
    void updatePrisonEventSetting(@Valid PrisonEventSettingSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-所警情管理-报警联动设置(所情来源)
     *
     * @param id 编号
     */
    void deletePrisonEventSetting(String id);

    /**
     * 获得实战平台-巡视管控-所警情管理-报警联动设置(所情来源)
     *
     * @param id 编号
     * @return 实战平台-巡视管控-所警情管理-报警联动设置(所情来源)
     */
    PrisonEventSettingDO getPrisonEventSetting(String id);

    /**
    * 获得实战平台-巡视管控-所警情管理-报警联动设置(所情来源)分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-巡视管控-所警情管理-报警联动设置(所情来源)分页
    */
    PageResult<PrisonEventSettingDO> getPrisonEventSettingPage(PrisonEventSettingPageReqVO pageReqVO);

    /**
    * 获得实战平台-巡视管控-所警情管理-报警联动设置(所情来源)列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-巡视管控-所警情管理-报警联动设置(所情来源)列表
    */
    List<PrisonEventSettingDO> getPrisonEventSettingList(PrisonEventSettingListReqVO listReqVO);

    /**
     * 启用或禁用报警联动设置
     * @param id
     * @return
     */
    boolean changeStatus(String id);
}
