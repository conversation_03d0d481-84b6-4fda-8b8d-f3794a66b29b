package com.rs.module.acp.service.pm;

import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.util.DateUtils;
import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.util.http.HttpUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.LeaderInspectionDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pm.LeaderInspectionDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 领导巡视 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LeaderInspectionServiceImpl extends BaseServiceImpl<LeaderInspectionDao, LeaderInspectionDO> implements LeaderInspectionService {

    @Resource
    private LeaderInspectionDao leaderInspectionDao;

    @Override
    public String createLeaderInspection(LeaderInspectionSaveReqVO createReqVO) {
        String xssffxwt = createReqVO.getXssffxwt();
        if(xssffxwt.equals("1")){
            createReqVO.setStatus("2");
        }else{
            createReqVO.setStatus("1");
        }
        // 插入
        LeaderInspectionDO leaderInspection = BeanUtils.toBean(createReqVO, LeaderInspectionDO.class);

        leaderInspectionDao.insert(leaderInspection);
        if(xssffxwt.equalsIgnoreCase("1")) {
            createReqVO.setId(leaderInspection.getId());
            this.sendTodoMsg(createReqVO);
        }
        // 返回
        return leaderInspection.getId();
    }

    private void sendTodoMsg(LeaderInspectionSaveReqVO createReqVO) {
        // ========== 新增的消息发送逻辑 Start ==========
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        if (sessionUser == null) {
            throw new ServerException("用户未登录，无法发送消息！");
        }

        String title = String.format("%s 于%s提交了新的领导巡视记录，请处理", sessionUser.getName(), DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        String content = String.format("巡视时间：%s\n巡视地点：%s\n发现问题：%s",
                createReqVO.getXskssj(), createReqVO.getXsddName(), createReqVO.getXsjgxx());

//        String simpleUUID = IdUtil.fastSimpleUUID();
        String simpleUUID = createReqVO.getId();
        String url = createReqVO.getUrl()+"?id="+createReqVO.getId(); // 消息点击后跳转的页面
        String appCode = HttpUtils.getAppCode(); // 获取当前应用编码
        String fUser = sessionUser.getIdCard();   // 来源用户身份证号
        String fUserName = sessionUser.getName(); // 来源用户名
        String fOrgCode = sessionUser.getOrgCode(); // 来源机构代码
        String fOrgName = sessionUser.getOrgName(); // 来源机构名称
        String fXxpt = "pc"; // 来源系统标识
        String ywbh = createReqVO.getId(); // 业务编号，可以是主键ID
        String msgType = MsgBusTypeEnum.LD_LDXS.getCode(); // 消息类型（建议定义枚举）

        // 接收人列表：假设固定发送给某个部门负责人（可从配置或数据库动态获取）
        List<ReceiveUser> receiveUserList = new ArrayList<>();
        String wttzr = createReqVO.getWttzr();

        if (wttzr != null && !wttzr.trim().isEmpty()) {
            String[] userIds = wttzr.split(",");
            for (String userId : userIds) {
                if (!userId.trim().isEmpty()) {
                    receiveUserList.add(new ReceiveUser(userId.trim(), fOrgCode)); // 假设统一组织机构码
                }
            }
        } else {
            throw new ServerException("问题通知责任人不能为空");
        }

        SendMessageUtil.sendTodoMsg(title, content, url, appCode, fUser, fUserName, fOrgCode, fOrgName,
                null, simpleUUID, fXxpt, ywbh, receiveUserList, msgType, null);
        // ========== 新增的消息发送逻辑 End ==========

    }

    @Override
    public void updateLeaderInspection(LeaderInspectionSaveReqVO updateReqVO) {
        String xssffxwt = updateReqVO.getXssffxwt();
        if(xssffxwt.equals("1")){
            updateReqVO.setStatus("3");
        }
        // 校验存在
        validateLeaderInspectionExists(updateReqVO.getId());
        // 更新
        LeaderInspectionDO updateObj = BeanUtils.toBean(updateReqVO, LeaderInspectionDO.class);
        leaderInspectionDao.updateById(updateObj);

        //消除待办
        String msgId = updateReqVO.getId();
        try {
            SendMessageUtil.ProcessTodoMsg(msgId, SessionUserUtil.getSessionUser().getIdCard(), "pc");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void deleteLeaderInspection(String id) {
        // 校验存在
        validateLeaderInspectionExists(id);
        // 删除
        leaderInspectionDao.deleteById(id);
    }

    private void validateLeaderInspectionExists(String id) {
        if (leaderInspectionDao.selectById(id) == null) {
            throw new ServerException("领导巡视数据不存在");
        }
    }

    @Override
    public LeaderInspectionDO getLeaderInspection(String id) {
        return leaderInspectionDao.selectById(id);
    }

    @Override
    public PageResult<LeaderInspectionDO> getLeaderInspectionPage(LeaderInspectionPageReqVO pageReqVO) {
        return leaderInspectionDao.selectPage(pageReqVO);
    }

    @Override
    public List<LeaderInspectionDO> getLeaderInspectionList(LeaderInspectionListReqVO listReqVO) {
        return leaderInspectionDao.selectList(listReqVO);
    }


}
