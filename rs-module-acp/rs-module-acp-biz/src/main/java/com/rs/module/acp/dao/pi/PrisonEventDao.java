package com.rs.module.acp.dao.pi;

import java.util.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventPersonPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventPersonRespVO;
import com.rs.module.acp.entity.pi.PrisonEventDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
 * 实战平台-巡视管控-所情登记 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface PrisonEventDao extends IBaseDao<PrisonEventDO> {


    default PageResult<PrisonEventDO> selectPage(PrisonEventPageReqVO reqVO) {
        Page<PrisonEventDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrisonEventDO> wrapper = new LambdaQueryWrapperX<PrisonEventDO>()
                .eqIfPresent(PrisonEventDO::getWarningId, reqVO.getWarningId())
                .eqIfPresent(PrisonEventDO::getAreaId, reqVO.getAreaId())
                .likeIfPresent(PrisonEventDO::getAreaName, reqVO.getAreaName())
                .betweenIfPresent(PrisonEventDO::getHappenTime, reqVO.getHappenTime())
                .eqIfPresent(PrisonEventDO::getEventType, reqVO.getEventType())
                .eqIfPresent(PrisonEventDO::getEventLevel, reqVO.getEventLevel())
                .eqIfPresent(PrisonEventDO::getDeviceTypeId, reqVO.getDeviceTypeId())
                .likeIfPresent(PrisonEventDO::getDeviceTypeName, reqVO.getDeviceTypeName())
                .eqIfPresent(PrisonEventDO::getDeviceId, reqVO.getDeviceId())
                .likeIfPresent(PrisonEventDO::getDeviceName, reqVO.getDeviceName())
                .eqIfPresent(PrisonEventDO::getStatus, reqVO.getStatus())
                .eqIfPresent(PrisonEventDO::getEventDetails, reqVO.getEventDetails())
                .eqIfPresent(PrisonEventDO::getOutsider, reqVO.getOutsider())
                .eqIfPresent(PrisonEventDO::getEventCode, reqVO.getEventCode())
                .likeIfPresent(PrisonEventDO::getOutsiderName, reqVO.getOutsiderName())
                .eqIfPresent(PrisonEventDO::getEventSrc, reqVO.getEventSrc())
                .eqIfPresent(PrisonEventDO::getHandleInfo, reqVO.getHandleInfo())
                .eqIfPresent(PrisonEventDO::getEventTypeId, reqVO.getEventTypeId())
                .betweenIfPresent(PrisonEventDO::getEventStartTime, reqVO.getEventStartTime())
                .betweenIfPresent(PrisonEventDO::getEventEndTime, reqVO.getEventEndTime())
                .eqIfPresent(PrisonEventDO::getHandleUserSfzh, reqVO.getHandleUserSfzh())
                .likeIfPresent(PrisonEventDO::getHandleUserName, reqVO.getHandleUserName())
                .betweenIfPresent(PrisonEventDO::getHandleTime, reqVO.getHandleTime())
                .eqIfPresent(PrisonEventDO::getAuditUserSfzh, reqVO.getAuditUserSfzh())
                .likeIfPresent(PrisonEventDO::getAuditUserName, reqVO.getAuditUserName())
                .betweenIfPresent(PrisonEventDO::getAuditTime, reqVO.getAuditTime())
                .eqIfPresent(PrisonEventDO::getAuditOpinion, reqVO.getAuditOpinion())
                .eqIfPresent(PrisonEventDO::getReportPrisonerSfzhs, reqVO.getReportPrisonerSfzhs())
                .eqIfPresent(PrisonEventDO::getEventVideo, reqVO.getEventVideo())
                .eqIfPresent(PrisonEventDO::getEventRootTypeId, reqVO.getEventRootTypeId())
                .likeIfPresent(PrisonEventDO::getEventRootTypeName, reqVO.getEventRootTypeName())
                .eqIfPresent(PrisonEventDO::getAttUrl, reqVO.getAttUrl())
                .eqIfPresent(PrisonEventDO::getActInstId, reqVO.getActInstId())
                .eqIfPresent(PrisonEventDO::getTaskId, reqVO.getTaskId());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(PrisonEventDO::getAddTime);
        }
        Page<PrisonEventDO> prisonEventPage = selectPage(page, wrapper);
        return new PageResult<>(prisonEventPage.getRecords(), prisonEventPage.getTotal());
    }

    default List<PrisonEventDO> selectList(PrisonEventListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrisonEventDO>()
                .eqIfPresent(PrisonEventDO::getWarningId, reqVO.getWarningId())
                .eqIfPresent(PrisonEventDO::getAreaId, reqVO.getAreaId())
                .likeIfPresent(PrisonEventDO::getAreaName, reqVO.getAreaName())
                .betweenIfPresent(PrisonEventDO::getHappenTime, reqVO.getHappenTime())
                .eqIfPresent(PrisonEventDO::getEventType, reqVO.getEventType())
                .eqIfPresent(PrisonEventDO::getEventLevel, reqVO.getEventLevel())
                .eqIfPresent(PrisonEventDO::getDeviceTypeId, reqVO.getDeviceTypeId())
                .likeIfPresent(PrisonEventDO::getDeviceTypeName, reqVO.getDeviceTypeName())
                .eqIfPresent(PrisonEventDO::getDeviceId, reqVO.getDeviceId())
                .likeIfPresent(PrisonEventDO::getDeviceName, reqVO.getDeviceName())
                .eqIfPresent(PrisonEventDO::getStatus, reqVO.getStatus())
                .eqIfPresent(PrisonEventDO::getEventDetails, reqVO.getEventDetails())
                .eqIfPresent(PrisonEventDO::getOutsider, reqVO.getOutsider())
                .eqIfPresent(PrisonEventDO::getEventCode, reqVO.getEventCode())
                .likeIfPresent(PrisonEventDO::getOutsiderName, reqVO.getOutsiderName())
                .eqIfPresent(PrisonEventDO::getEventSrc, reqVO.getEventSrc())
                .eqIfPresent(PrisonEventDO::getHandleInfo, reqVO.getHandleInfo())
                .eqIfPresent(PrisonEventDO::getEventTypeId, reqVO.getEventTypeId())
                .betweenIfPresent(PrisonEventDO::getEventStartTime, reqVO.getEventStartTime())
                .betweenIfPresent(PrisonEventDO::getEventEndTime, reqVO.getEventEndTime())
                .eqIfPresent(PrisonEventDO::getHandleUserSfzh, reqVO.getHandleUserSfzh())
                .likeIfPresent(PrisonEventDO::getHandleUserName, reqVO.getHandleUserName())
                .betweenIfPresent(PrisonEventDO::getHandleTime, reqVO.getHandleTime())
                .eqIfPresent(PrisonEventDO::getAuditUserSfzh, reqVO.getAuditUserSfzh())
                .likeIfPresent(PrisonEventDO::getAuditUserName, reqVO.getAuditUserName())
                .betweenIfPresent(PrisonEventDO::getAuditTime, reqVO.getAuditTime())
                .eqIfPresent(PrisonEventDO::getAuditOpinion, reqVO.getAuditOpinion())
                .eqIfPresent(PrisonEventDO::getReportPrisonerSfzhs, reqVO.getReportPrisonerSfzhs())
                .eqIfPresent(PrisonEventDO::getEventVideo, reqVO.getEventVideo())
                .eqIfPresent(PrisonEventDO::getEventRootTypeId, reqVO.getEventRootTypeId())
                .likeIfPresent(PrisonEventDO::getEventRootTypeName, reqVO.getEventRootTypeName())
                .eqIfPresent(PrisonEventDO::getAttUrl, reqVO.getAttUrl())
                .eqIfPresent(PrisonEventDO::getActInstId, reqVO.getActInstId())
                .eqIfPresent(PrisonEventDO::getTaskId, reqVO.getTaskId())
                .orderByDesc(PrisonEventDO::getAddTime));
    }

    IPage<PrisonEventPersonRespVO> getOutsiderPersonPage(@Param("page") Page<PrisonEventPersonRespVO> page, @Param("req") PrisonEventPersonPageReqVO req);

    List<PrisonEventPersonRespVO> getOutsiderPersonByIds(@Param("idList") List<String> idList, @Param("orgCode") String orgCode);
}
