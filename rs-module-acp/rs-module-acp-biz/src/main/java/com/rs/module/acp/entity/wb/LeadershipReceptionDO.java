package com.rs.module.acp.entity.wb;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-窗口业务-所领导接待登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_wb_leadership_reception")
@KeySequence("acp_wb_leadership_reception_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_wb_leadership_reception")
public class LeadershipReceptionDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 接待领导姓名
     */
    private String leaderName;
    /**
     * 同行人姓名
     */
    private String companionName;
    /**
     * 接待开始时间
     */
    private Date receptionStartTime;
    /**
     * 接待结束时间
     */
    private Date receptionEndTime;
    /**
     * 接待类型
     */
    private String type;
    /**
     * 接待事由
     */
    private String receptionReason;
    /**
     * 接待地点
     */
    private String receptionAddress;

}
