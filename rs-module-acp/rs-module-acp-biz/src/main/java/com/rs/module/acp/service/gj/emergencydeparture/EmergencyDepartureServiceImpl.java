package com.rs.module.acp.service.gj.emergencydeparture;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.module.acp.controller.admin.gj.vo.emergencydeparture.EmergencyDepartureListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.emergencydeparture.EmergencyDeparturePageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.emergencydeparture.EmergencyDepartureSaveReqVO;
import com.rs.module.acp.util.CommonUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.EmergencyDepartureDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.EmergencyDepartureDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-紧急出所登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EmergencyDepartureServiceImpl extends BaseServiceImpl<EmergencyDepartureDao, EmergencyDepartureDO> implements EmergencyDepartureService {

    @Resource
    private EmergencyDepartureDao emergencyDepartureDao;

    @Override
    public String createEmergencyDeparture(EmergencyDepartureSaveReqVO createReqVO) {
        // 插入
        EmergencyDepartureDO emergencyDeparture = BeanUtils.toBean(createReqVO, EmergencyDepartureDO.class);
        emergencyDepartureDao.insert(emergencyDeparture);
        // 返回
        return emergencyDeparture.getId();
    }

    @Override
    public void updateEmergencyDeparture(EmergencyDepartureSaveReqVO updateReqVO) {
        // 校验存在
        validateEmergencyDepartureExists(updateReqVO.getId());
        // 更新
        EmergencyDepartureDO updateObj = BeanUtils.toBean(updateReqVO, EmergencyDepartureDO.class);
        emergencyDepartureDao.updateById(updateObj);
    }

    @Override
    public void deleteEmergencyDeparture(String id) {
        // 校验存在
        validateEmergencyDepartureExists(id);
        // 删除
        emergencyDepartureDao.deleteById(id);
    }

    private void validateEmergencyDepartureExists(String id) {
        if (emergencyDepartureDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-紧急出所登记数据不存在");
        }
    }

    @Override
    public EmergencyDepartureDO getEmergencyDeparture(String id) {
        return emergencyDepartureDao.selectById(id);
    }

    @Override
    public PageResult<EmergencyDepartureDO> getEmergencyDeparturePage(EmergencyDeparturePageReqVO pageReqVO) {
        return emergencyDepartureDao.selectPage(pageReqVO);
    }

    @Override
    public List<EmergencyDepartureDO> getEmergencyDepartureList(EmergencyDepartureListReqVO listReqVO) {
        return emergencyDepartureDao.selectList(listReqVO);
    }

    @Override
    public PageResult<EmergencyDepartureDO> getAppEmergencyDeparturePage(int pageNo, int pageSize, String operatePoliceSfzh, String type) {
        Page<EmergencyDepartureDO> page = new Page<>(pageNo, pageSize);
        Map<String, Date> commonAppRecordPeriod = CommonUtils.getCommonAppRecordPeriod(type);
        Page<EmergencyDepartureDO> result = emergencyDepartureDao.getAppEmergencyDeparturePage(page, operatePoliceSfzh,
                commonAppRecordPeriod.get("startTime"), commonAppRecordPeriod.get("endTime"));
        return new PageResult<>(result.getRecords(), result.getTotal());
    }


}
