package com.rs.module.acp.service.gj.equipment;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseExtendListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseExtendPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseExtendSaveReqVO;
import com.rs.module.acp.entity.gj.EquipmentUseExtendDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-械具使用延长呈批 Service 接口
 *
 * <AUTHOR>
 */
public interface EquipmentUseExtendService extends IBaseService<EquipmentUseExtendDO>{

    /**
     * 创建实战平台-管教业务-械具使用延长呈批
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createEquipmentUseExtend(@Valid EquipmentUseExtendSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-械具使用延长呈批
     *
     * @param updateReqVO 更新信息
     */
    void updateEquipmentUseExtend(@Valid EquipmentUseExtendSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-械具使用延长呈批
     *
     * @param id 编号
     */
    void deleteEquipmentUseExtend(String id);

    /**
     * 获得实战平台-管教业务-械具使用延长呈批
     *
     * @param id 编号
     * @return 实战平台-管教业务-械具使用延长呈批
     */
    EquipmentUseExtendDO getEquipmentUseExtend(String id);

    /**
    * 获得实战平台-管教业务-械具使用延长呈批分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-械具使用延长呈批分页
    */
    PageResult<EquipmentUseExtendDO> getEquipmentUseExtendPage(EquipmentUseExtendPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-械具使用延长呈批列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-械具使用延长呈批列表
    */
    List<EquipmentUseExtendDO> getEquipmentUseExtendList(EquipmentUseExtendListReqVO listReqVO);


}
