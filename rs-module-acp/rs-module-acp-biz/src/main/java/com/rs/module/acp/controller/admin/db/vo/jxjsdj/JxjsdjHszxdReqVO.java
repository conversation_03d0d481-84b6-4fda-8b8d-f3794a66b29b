package com.rs.module.acp.controller.admin.db.vo.jxjsdj;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-减刑登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class JxjsdjHszxdReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("核实执行地调查评估结果（1、同意；2、不同意）")
    private String hszxddcpgjg;

    @ApiModelProperty("核实执行地调查备注")
    private String hszxddcbz;

    @ApiModelProperty("核实执行地调查材料")
    private String hszxddccl;

}
