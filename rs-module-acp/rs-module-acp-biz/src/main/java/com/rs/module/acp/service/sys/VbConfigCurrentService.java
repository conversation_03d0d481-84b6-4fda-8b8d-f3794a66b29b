package com.rs.module.acp.service.sys;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.sys.vo.*;
import com.rs.module.acp.entity.sys.VbConfigCurrentDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-语音播报-即时配置 Service 接口
 *
 * <AUTHOR>
 */
public interface VbConfigCurrentService extends IBaseService<VbConfigCurrentDO>{

    /**
     * 创建实战平台-语音播报-即时配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createVbConfigCurrent(@Valid VbConfigCurrentSaveReqVO createReqVO);

    /**
     * 更新实战平台-语音播报-即时配置
     *
     * @param updateReqVO 更新信息
     */
    void updateVbConfigCurrent(@Valid VbConfigCurrentSaveReqVO updateReqVO);

    /**
     * 删除实战平台-语音播报-即时配置
     *
     * @param id 编号
     */
    void deleteVbConfigCurrent(String id);

    /**
     * 获得实战平台-语音播报-即时配置
     *
     * @param id 编号
     * @return 实战平台-语音播报-即时配置
     */
    VbConfigCurrentDO getVbConfigCurrent(String id);

    /**
    * 获得实战平台-语音播报-即时配置分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-语音播报-即时配置分页
    */
    PageResult<VbConfigCurrentDO> getVbConfigCurrentPage(VbConfigCurrentPageReqVO pageReqVO);

    /**
    * 获得实战平台-语音播报-即时配置列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-语音播报-即时配置列表
    */
    List<VbConfigCurrentDO> getVbConfigCurrentList(VbConfigCurrentListReqVO listReqVO);

    /**
     * 获取等待播报的实时播报任务
     * @param orgCode
     * @return
     */
    public List<VbConfigCurrentDO> getWaittingCurrentConfig(String orgCode);
}
