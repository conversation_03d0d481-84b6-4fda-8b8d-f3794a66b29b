package com.rs.module.acp.service.gj.bookingapproval;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.cons.NormalApprovalStatusEnum;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.dao.gj.BookingApprovalDao;
import com.rs.module.acp.entity.gj.BookingApprovalDO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.enums.PrisonerQueryRyztEnum;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 实战平台-管教业务-预约审核管理 Service 实现类
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class BookingApprovalServiceImpl extends BaseServiceImpl<BookingApprovalDao, BookingApprovalDO> implements BookingApprovalService {

    @Resource
    private BookingApprovalDao bookingApprovalDao;

    @Resource
    private PrisonerService prisonerService;

    private final String defKey = "guanjiaoyuyueshenheguanli";

    private final String BUS_TYPE = "22";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createBookingApproval(BookingApprovalSaveReqVO createReqVO) {

        PrisonerVwRespVO respVO = prisonerService.getPrisonerSelectCompomenOne(createReqVO.getJgrybm(), PrisonerQueryRyztEnum.ALL);
        Assert.notNull(respVO, "传入人员编码异常");
        // 插入
        BookingApprovalDO bookingApproval = BeanUtils.toBean(createReqVO, BookingApprovalDO.class);
        bookingApproval.setJgryxm(respVO.getXm());
        bookingApproval.setStatus(NormalApprovalStatusEnum.PENDING.getCode());
        //@NotEmpty(message = "状态不能为空")
        //private String status;
        bookingApproval.setApplicationTime(new Date());
        bookingApproval.setRoomId(respVO.getJsh());
        bookingApproval.setRoomName(respVO.getRoomName());
        bookingApprovalDao.insert(bookingApproval);
        // 返回
        ////TODO 审批调整URL 前端提供
        //String msgUrl = StrUtil.format("/#/discipline/####?curId={}&saveType=approve", bookingApproval.getId());
        //MapBuilder<String, Object> variables = MapUtil.builder();
        //variables.put("ywbh", bookingApproval.getId()).put("busType", BUS_TYPE);
        //JSONObject result = BspApprovalUtil.commonStartProcess(defKey, bookingApproval.getId(), "【审批】预约审核", msgUrl, variables.build(), HttpUtils.getAppCode());
        //log.info("==========result:{}", result);
        //if (result.getIntValue("code") != HttpStatus.OK.value()) {
        //    throw new ServerException("流程启动失败");
        //}
        //JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
        //bookingApproval.setActInstId(bpmTrail.getString("actInstId"));
        //bookingApproval.setTaskId(bpmTrail.getString("taskId"));
        bookingApproval.setStatus(NormalApprovalStatusEnum.PENDING.getCode());
        bookingApprovalDao.updateById(bookingApproval);
        return bookingApproval.getId();
    }

    @Override
    public void deleteBookingApproval(String id) {
        // 校验存在
        validateBookingApprovalExists(id);
        // 删除
        bookingApprovalDao.deleteById(id);
    }

    private void validateBookingApprovalExists(String id) {
        if (bookingApprovalDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-预约审核管理数据不存在");
        }
    }

    @Override
    public BookingApprovalDO getBookingApproval(String id) {
        return bookingApprovalDao.selectById(id);
    }

    @Override
    public PageResult<BookingApprovalDO> getBookingApprovalPage(BookingApprovalPageReqVO pageReqVO) {
        if(pageReqVO.getType() != null){
            pageReqVO.setApplicationTime(DateUtil.getTimeRange(pageReqVO.getType()));
        }
        return bookingApprovalDao.selectPage(pageReqVO);
    }

    @Override
    public List<BookingApprovalDO> getBookingApprovalList(BookingApprovalListReqVO listReqVO) {
        if(listReqVO.getType() != null){
            listReqVO.setApplicationTime(DateUtil.getTimeRange(listReqVO.getType()));
        }
        return bookingApprovalDao.selectList(listReqVO);
    }

    @Override
    public Boolean approve(GjApproveReqVO approveReqVO) {

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        BookingApprovalDO entity =  bookingApprovalDao.selectById(approveReqVO.getId());
        Assert.notNull(entity, "记录不存在");

        NormalApprovalStatusEnum statusEnum = NormalApprovalStatusEnum.getByCode(entity.getStatus());
        if (!NormalApprovalStatusEnum.PENDING.getCode().equals(statusEnum.getCode())) {
            throw new ServerException("该状态[" + statusEnum.getValue() + "]不允许审批！");
        }

        String approvalResult = approveReqVO.getApprovalResult();
        BspApproceStatusEnum bspApproceStatusEnum;
        String status = NormalApprovalStatusEnum.APPROVED.getCode();
        if (String.valueOf( BspApproceStatusEnum.PASSED_END.getCode()).equals(approvalResult)) {
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED_END;
        } else {
            bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
            status = NormalApprovalStatusEnum.REJECTED.getCode();
        }
        entity.setStatus(status);

        //校验当前人有没有审批权限
        //Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(entity.getTaskId(), sessionUser.getIdCard());
        //if (!isApproval) {
        //    throw new ServerException("当前人无审批权限");
        //}

        entity.setApprovalComments(approveReqVO.getApprovalComments());
        entity.setApproverXm(sessionUser.getName());
        entity.setApproverSfzh(sessionUser.getIdCard());
        entity.setApprovalResult(approveReqVO.getApprovalResult());
        entity.setApproverTime(new Date());

        ////审批
        //JSONObject result = BspApprovalUtil.approvalProcessAcp(defKey,
        //        entity.getActInstId(),
        //        entity.getTaskId(),
        //        entity.getId(),
        //        bspApproceStatusEnum,
        //        approveReqVO.getApprovalComments()  );
        //
        //log.info("appr-esult:{}", result);
        //if(result.getIntValue("code") != HttpStatus.OK.value()){
        //    throw new ServerException("流程审批失败");
        //}
        //JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
        //entity.setTaskId(bpmTrail.getString("taskId"));
        bookingApprovalDao.updateById( entity);
        //TODO 消息发送，产品未提供
        return true;
    }


}
