package com.rs.module.acp.service.ds.sjbs;

import javax.validation.*;

import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyDataSubmitReqVO;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.WeeklyPrisonerTopSaveReqVO;
import com.rs.module.acp.entity.ds.sjbs.WeeklyDataSubmitJlsDO;
import com.rs.module.acp.entity.ds.sjbs.WeeklyPrisonerTopDO;
import com.bsp.common.orm.mybatis.service.IBaseService;

import java.util.Date;
import java.util.List;

/**
 * 实战平台-数据固化-每周会见被监管人员排名 Service 接口
 *
 * <AUTHOR>
 */
public interface WeeklyPrisonerTopService extends IBaseService<WeeklyPrisonerTopDO>{

    /**
     * 创建实战平台-数据固化-每周会见被监管人员排名
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createWeeklyPrisonerTop(@Valid WeeklyPrisonerTopSaveReqVO createReqVO);

    /**
     * 更新实战平台-数据固化-每周会见被监管人员排名
     *
     * @param updateReqVO 更新信息
     */
    void updateWeeklyPrisonerTop(@Valid WeeklyPrisonerTopSaveReqVO updateReqVO);

    /**
     * 删除实战平台-数据固化-每周会见被监管人员排名
     *
     * @param id 编号
     */
    void deleteWeeklyPrisonerTop(String id);

    /**
     * 获得实战平台-数据固化-每周会见被监管人员排名
     *
     * @param id 编号
     * @return 实战平台-数据固化-每周会见被监管人员排名
     */
    WeeklyPrisonerTopDO getWeeklyPrisonerTop(String id);

    void saveForStatistic(List<WeeklyDataSubmitReqVO> weeklyDataSubmitList);

    List<WeeklyPrisonerTopDO> getWeeklyPrisonerTopByDate(String startDate, String endDate, String orgCode);

    List<WeeklyPrisonerTopDO> getByWeeklyDataSubmitId(String weeklyDataSubmitId);
}
