package com.rs.module.acp.controller.app.gj;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.groupinout.GroupInOutSaveReqVO;
import com.rs.module.acp.controller.app.gj.vo.groupinout.AppGroupInOutRespVO;
import com.rs.module.acp.controller.app.gj.vo.groupinout.AppGroupInOutSaveReqVO;
import com.rs.module.acp.controller.app.gj.vo.groupinout.AppGroupInOutStatisticVO;
import com.rs.module.acp.entity.gj.GroupInOutDO;
import com.rs.module.acp.service.gj.InOutRecordsService;
import com.rs.module.acp.service.gj.groupinout.GroupInOutService;
import com.rs.module.acp.util.CommonUtils;
import com.rs.module.base.controller.admin.pm.vo.PrisonerInVwRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.rs.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @Date 2025/7/11 14:14
 */
@Api(tags = "外屏-管教业务-集体出入")
@RestController
@RequestMapping("/app/acp/gj/groupInOut")
@Validated
public class AppGroupInOutController {

    @Resource
    private GroupInOutService groupInOutService;
    @Resource
    private InOutRecordsService inOutRecordsService;

    @ApiOperation(value = "集体出入-统计")
    @GetMapping("/getGroupInOutStatisticVO")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "roomId", value = "监室ID", required = true, dataType = "String", paramType = "query")
    })
    public CommonResult<AppGroupInOutStatisticVO> getGroupInOutStatisticVO(@RequestParam("orgCode") String orgCode,
                                                                           @RequestParam("roomId") String roomId) {
        return success(groupInOutService.getGroupInOutStatisticVO(orgCode, roomId));
    }

    @ApiOperation(value = "集体出入-人员列表", response = PrisonerInVwRespVO.class)
    @GetMapping(value = "/getPrsionerList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "roomId", value = "监室ID", required = true, dataType = "String", paramType = "query")
    })
    public CommonResult<List<PrisonerInVwRespVO>> getPrsionerList(@RequestParam("orgCode") String orgCode,
                                                                  @RequestParam("roomId") String roomId) {
        return success(inOutRecordsService.getOutRyJgrybm(orgCode, roomId));
    }

    @PostMapping("/groupInOut")
    @ApiOperation(value = "集体出入")
    public CommonResult<String> groupInOut(@Valid @RequestBody AppGroupInOutSaveReqVO createReqVO) {
        GroupInOutSaveReqVO groupInOutSaveReqVO = new GroupInOutSaveReqVO();
        groupInOutSaveReqVO.setBusinessType(createReqVO.getBusinessType());
        groupInOutSaveReqVO.setIsRoom("0");
        groupInOutSaveReqVO.setRemark("");
        groupInOutSaveReqVO.setDetails(createReqVO.getDetails());
        return success(groupInOutService.createGroupInOut(groupInOutSaveReqVO));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得-外屏-集体出入记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "timeType", value = "时间段：1 全部，2 今天，3 昨天，4 近一周", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "roomId", value = "监室编号", dataType = "String", paramType = "query", required = true)
    })
    public CommonResult<List<AppGroupInOutRespVO>> createReportSow(@RequestParam(value = "timeType") String timeType,
                                                                   @RequestParam(value = "roomId") String roomId) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        Map<String, Date> commonAppRecordPeriod = CommonUtils.getCommonAppRecordPeriod(timeType);
        LambdaQueryWrapper<GroupInOutDO> wrapper = new LambdaQueryWrapper<GroupInOutDO>();
        wrapper.eq(GroupInOutDO::getAddUser, sessionUser.getIdCard())
                .like(GroupInOutDO::getRoomId, roomId)
                .between(commonAppRecordPeriod.get("startTime") != null,
                        GroupInOutDO::getAddTime, commonAppRecordPeriod.get("startTime"), commonAppRecordPeriod.get("endTime"));
        List<GroupInOutDO> list = groupInOutService.list(wrapper);
        if (CollUtil.isEmpty(list)) {
            return success(new LinkedList<>());
        }
        List<AppGroupInOutRespVO> resultList = new LinkedList<>();
        for (GroupInOutDO groupInOutDO : list) {
            AppGroupInOutRespVO appGroupInOutRespVO = new AppGroupInOutRespVO();
            BeanUtils.copyProperties(groupInOutDO, appGroupInOutRespVO);
            resultList.add(appGroupInOutRespVO);
        }
        return success(resultList);
    }

}
