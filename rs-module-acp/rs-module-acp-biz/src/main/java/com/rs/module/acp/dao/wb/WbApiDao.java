package com.rs.module.acp.dao.wb;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.module.acp.controller.admin.wb.vo.MeetingNoticeQueryVO;
import com.rs.module.acp.controller.admin.wb.vo.MeetingNoticeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 实战平台-窗口业务-提讯登记 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface WbApiDao {


    IPage<MeetingNoticeVO> getMeetingNoticePage(@Param("page") Page<MeetingNoticeVO> page, @Param("pageReqVO") MeetingNoticeQueryVO pageReqVO);
}
