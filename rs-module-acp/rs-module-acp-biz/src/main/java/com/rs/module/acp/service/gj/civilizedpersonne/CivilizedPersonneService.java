package com.rs.module.acp.service.gj.civilizedpersonne;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne.*;
import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.CivilizedRoomApprovalReqVO;
import com.rs.module.acp.entity.gj.CivilizedPersonneDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-文明个人登记 Service 接口
 *
 * <AUTHOR>
 */
public interface CivilizedPersonneService extends IBaseService<CivilizedPersonneDO>{

    /**
     * 创建实战平台-管教业务-文明个人登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCivilizedPersonne(@Valid CivilizedPersonneSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-文明个人登记
     *
     * @param updateReqVO 更新信息
     */
    void updateCivilizedPersonne(@Valid CivilizedPersonneSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-文明个人登记
     *
     * @param id 编号
     */
    void deleteCivilizedPersonne(String id);

    /**
     * 获得实战平台-管教业务-文明个人登记
     *
     * @param id 编号
     * @return 实战平台-管教业务-文明个人登记
     */
    CivilizedPersonneRespVO getCivilizedPersonne(String id);

    /**
    * 获得实战平台-管教业务-文明个人登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-文明个人登记分页
    */
    PageResult<CivilizedPersonneDO> getCivilizedPersonnePage(CivilizedPersonnePageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-文明个人登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-文明个人登记列表
    */
    List<CivilizedPersonneRespVO> getCivilizedPersonneList(CivilizedPersonneListReqVO listReqVO);


    void registEvaluation(CivilizedPersonneRegistReqVO registReqVO);

    void approval(CivilizedRoomApprovalReqVO reqVO);

    void againEvaluation(CivilizedPersonneRegistReqVO registReqVO);
}
