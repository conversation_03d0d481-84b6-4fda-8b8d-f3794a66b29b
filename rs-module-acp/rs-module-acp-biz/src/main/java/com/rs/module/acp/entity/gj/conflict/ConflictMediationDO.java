package com.rs.module.acp.entity.gj.conflict;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 实战平台-管教业务-社会矛盾化解 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_conflict_mediation")
@KeySequence("acp_gj_conflict_mediation_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConflictMediationDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 事件ID
     */
    private String eventId;
    /**
     * 事件编号
     */
    private String eventCode;
    /**
     * 主持人
     */
    private String hostName;
    /**
     * 主持单位
     */
    private String hostOrg;
    /**
     * 调解次数
     */
    private Integer mediationCnt;
    /**
     * 调解地点
     */
    private String mediationLocation;
    /**
     * 调解情况
     */
    private String mediationState;
    /**
     * 调解结果（1：调解成功，2：调解暂缓，3：不用调解）
     */
    private String mediationResult;
    /**
     * 矛盾化解结果
     */
    private String resolutionResult;
    /**
     * 经验总结
     */
    private String experienceSummary;
    /**
     * 调解经办人身份证号
     */
    private String mediationOperatorSfzh;
    /**
     * 调解经办人姓名
     */
    private String mediationOperatorXm;
    /**
     * 调解经办时间
     */
    private Date mediationOperatorTime;
    /**
     * 跟踪回访
     */
    private String followUpVisit;
    /**
     * 回访时间
     */
    private Date followUpTime;
    /**
     * 回访电话
     */
    private String followUpPhone;
    /**
     * 附件地址
     */
    private String attUrl;

}
