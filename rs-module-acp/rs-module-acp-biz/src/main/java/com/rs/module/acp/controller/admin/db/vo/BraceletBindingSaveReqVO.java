package com.rs.module.acp.controller.admin.db.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-羁押业务-手环绑定信息创建/更新 Request VO")
@Data
public class BraceletBindingSaveReqVO {

//    @ApiModelProperty("主键")
//    private String id;

    @ApiModelProperty(value = "人员编号", required = true)
    @NotEmpty(message = "人员编号不能为空")
    private String rybh;

    @ApiModelProperty(value = "手环ID", required = true)
//    @NotEmpty(message = "手环ID不能为空")
    private String shid;

    @ApiModelProperty(value = "手环绑定状态", required = true)
//    @NotEmpty(message = "手环绑定状态不能为空")
    private String shbdzt;

    @ApiModelProperty("手环绑定时间")
    private Date sdbdsj;

    @ApiModelProperty("状态")
    @NotEmpty(message = "状态不能为空")
    private String status;

    @ApiModelProperty("业务类型：看守所:kss;拘留所：jls;戒毒所:jds")
    @NotEmpty(message = "业务类型不能为空")
    private String businessType;

    @ApiModelProperty("入所类型")
    private String rslx;
} 