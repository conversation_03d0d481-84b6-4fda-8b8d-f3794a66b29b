package com.rs.module.acp.service.gj.equipment;

import com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseRemoveListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseRemovePageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseRemoveSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.EquipmentUseRemoveDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.EquipmentUseRemoveDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-械具使用解除呈批 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EquipmentUseRemoveServiceImpl extends BaseServiceImpl<EquipmentUseRemoveDao, EquipmentUseRemoveDO> implements EquipmentUseRemoveService {

    @Resource
    private EquipmentUseRemoveDao equipmentUseRemoveDao;

    @Override
    public String createEquipmentUseRemove(EquipmentUseRemoveSaveReqVO createReqVO) {
        // 插入
        EquipmentUseRemoveDO equipmentUseRemove = BeanUtils.toBean(createReqVO, EquipmentUseRemoveDO.class);
        equipmentUseRemoveDao.insert(equipmentUseRemove);
        // 返回
        return equipmentUseRemove.getId();
    }

    @Override
    public void updateEquipmentUseRemove(EquipmentUseRemoveSaveReqVO updateReqVO) {
        // 校验存在
        validateEquipmentUseRemoveExists(updateReqVO.getId());
        // 更新
        EquipmentUseRemoveDO updateObj = BeanUtils.toBean(updateReqVO, EquipmentUseRemoveDO.class);
        equipmentUseRemoveDao.updateById(updateObj);
    }

    @Override
    public void deleteEquipmentUseRemove(String id) {
        // 校验存在
        validateEquipmentUseRemoveExists(id);
        // 删除
        equipmentUseRemoveDao.deleteById(id);
    }

    private void validateEquipmentUseRemoveExists(String id) {
        if (equipmentUseRemoveDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-械具使用解除呈批数据不存在");
        }
    }

    @Override
    public EquipmentUseRemoveDO getEquipmentUseRemove(String id) {
        return equipmentUseRemoveDao.selectById(id);
    }

    @Override
    public PageResult<EquipmentUseRemoveDO> getEquipmentUseRemovePage(EquipmentUseRemovePageReqVO pageReqVO) {
        return equipmentUseRemoveDao.selectPage(pageReqVO);
    }

    @Override
    public List<EquipmentUseRemoveDO> getEquipmentUseRemoveList(EquipmentUseRemoveListReqVO listReqVO) {
        return equipmentUseRemoveDao.selectList(listReqVO);
    }


}
