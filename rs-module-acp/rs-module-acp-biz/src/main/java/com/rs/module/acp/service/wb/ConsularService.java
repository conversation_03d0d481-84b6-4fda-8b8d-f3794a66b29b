package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.ConsularDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-领事外事信息 Service 接口
 *
 * <AUTHOR>
 */
public interface ConsularService extends IBaseService<ConsularDO>{

    /**
     * 创建实战平台-窗口业务-领事外事信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createConsular(@Valid ConsularSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-领事外事信息
     *
     * @param updateReqVO 更新信息
     */
    void updateConsular(@Valid ConsularSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-领事外事信息
     *
     * @param id 编号
     */
    void deleteConsular(String id);

    /**
     * 获得实战平台-窗口业务-领事外事信息
     *
     * @param id 编号
     * @return 实战平台-窗口业务-领事外事信息
     */
    ConsularDO getConsular(String id);

    /**
    * 获得实战平台-窗口业务-领事外事信息分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-领事外事信息分页
    */
    PageResult<ConsularDO> getConsularPage(ConsularPageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-领事外事信息列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-领事外事信息列表
    */
    List<ConsularDO> getConsularList(ConsularListReqVO listReqVO);

    /**
     * 获得实战平台-窗口业务-领事外事信息列表
     *
     * @param  consularList 创建信息
     */
    boolean saveConsularList(List<ConsularSaveReqVO> consularList);

}
