package com.rs.module.acp.job.aloneImprison;

import com.rs.module.acp.controller.admin.gj.vo.aloneimprison.AloneImprisonApproveReqVO;
import com.rs.module.acp.entity.gj.AloneImprisonDO;
import com.rs.module.acp.service.gj.AloneImprisonService;
import com.rs.module.acp.util.AloneImprisonMsgSendUtil;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.pm.PrisonerService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class AloneImprisonJob {
    @Autowired
    private AloneImprisonService aloneImprisonService;
    @Autowired
    private PrisonerService prisonerService;
    /**
     * 关押期间，每日进行安全检查登记，每天8:00
     */
    @XxlJob("aloneImprisonSafetyInspection")
    public void safetyInspection() {
        //查询当前关押人员
        List<AloneImprisonDO> listDO = aloneImprisonService.getAloneImprisonListNow();
        for (AloneImprisonDO aloneImprisonDO : listDO){
            PrisonerVwRespVO vwRespVO = prisonerService.getPrisonerByJgrybm(aloneImprisonDO.getJgrybm());
            AloneImprisonMsgSendUtil util = new AloneImprisonMsgSendUtil(aloneImprisonDO,vwRespVO);
            //封装关押消息
            util.safetyInspection();
        }
    }
}
