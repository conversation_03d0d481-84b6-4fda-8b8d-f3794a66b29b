package com.rs.module.acp.controller.admin.gj.vo.conflict;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-社会矛盾化解登记新增/修改 Request VO")
@Data
public class ConflictRegSaveReqVO {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("登记经办时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date addTime;
    @ApiModelProperty("数据来源：1:实战平台，2：仓外屏, 3：仓内屏")
    @NotEmpty(message = "数据来源：1:实战平台，2：仓外屏, 3：仓内屏不能为空")
    private String dataSources;
    @ApiModelProperty("消息ID")
    private String pcId;
    @ApiModelProperty("发起人（通过数据来源区分，民警发起记录民警身份证号，被拘留人员记录：）")
    private String initiator;
    @ApiModelProperty("发起人姓名（记录发起人，通过数据来源区分是民警发起还是被拘留人员发起）")
    private String initiatorXm;
    @ApiModelProperty(value = "状态（01:已登记、02：待审批（所领导审批）、03: 审批不通过、04：调解中、05：调解完成、06：待确认、07：被退回）")
    private String regStatus;
    @ApiModelProperty("事件名称")
    @NotEmpty(message = "事件名称不能为空")
    private String eventName;
    @ApiModelProperty("矛盾等级（01：普通矛盾，02：重大、疑难矛盾）")
    @NotEmpty(message = "矛盾等级（01：普通矛盾，02：重大、疑难矛盾）不能为空")
    private String conflictLv;
    @ApiModelProperty("调解方式（01：单场调解，02：个别协商，03：庭式调解）")
    @NotEmpty(message = "调解方式（01：单场调解，02：个别协商，03：庭式调解）不能为空")
    private String mediationMode;
    @ApiModelProperty("涉案金额")
    @NotNull(message = "涉案金额不能为空")
    private BigDecimal involvedAmt;
    @ApiModelProperty("矛盾类别（01：非法上访、02：经济纠纷（补偿赔偿债务）、03：家庭矛盾、04：青少年违法、05：民间打架斗殴、06：民事纠纷、07：非法维权、08：对执法机关不满案件、09：迷信活动、10：“六失一偏”人员、11：其他类")
    @NotEmpty(message = "矛盾类别（01：非法上访、02：经济纠纷（补偿赔偿债务）、03：家庭矛盾、04：青少年违法、05：民间打架斗殴、06：民事纠纷、07：非法维权、08：对执法机关不满案件、09：迷信活动、10：“六失一偏”人员、11：其他类不能为空")
    private String conflictType;
    @ApiModelProperty("当事人或单位基本情况")
    @NotEmpty(message = "当事人或单位基本情况不能为空")
    private String partiesBasicInfo;
    @ApiModelProperty("矛盾基本情况")
    @NotEmpty(message = "矛盾基本情况不能为空")
    private String conflictBasicInfo;
    @ApiModelProperty("调解时间")
    @NotNull(message = "调解时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date mediationTime;
    @ApiModelProperty("调解民警身份证号")
    @NotEmpty(message = "调解民警身份证号不能为空")
    private String mediationPoliceSfzh;
    @ApiModelProperty("调解民警姓名")
    @NotEmpty(message = "调解民警姓名不能为空")
    private String mediationPoliceXm;
    @ApiModelProperty("登记人员")
    private List<ConflictPrisonerSaveReqVO> prisonerList;

}
