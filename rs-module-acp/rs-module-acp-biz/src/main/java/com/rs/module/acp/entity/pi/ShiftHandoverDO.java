package com.rs.module.acp.entity.pi;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-巡视管控-巡控交接班登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_shift_handover")
@KeySequence("acp_pi_shift_handover_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_shift_handover")
public class ShiftHandoverDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 人员情况
     */
    private String ryqk;
    /**
     * 重点关注人员
     */
    private String zdgzry;
    /**
     * 巡控登记
     */
    private String xkdj;
    /**
     * 交班总人数
     */
    private Integer totalPersons;
    /**
     * 交班点名总人数
     */
    private Integer rollCallTotal;
    /**
     * 待处理问题
     */
    private String pendingIssues;
    /**
     * 交班人身份证号
     */
    private String handoverPersonSfzh;
    /**
     * 交班人
     */
    private String handoverPerson;
    /**
     * 交班时间
     */
    private Date handoverTime;
    /**
     * 接班总人数
     */
    private Integer takeoverTotalPersons;
    /**
     * 接班人身份证号
     */
    private String takeoverPersonSfzh;
    /**
     * 接班人
     */
    private String takeoverPerson;
    /**
     * 接班时间
     */
    private Date takeoverTime;
    /**
     * 状态
     */
    private String status;
    /**
     * 交接班对象
     */
    private String handoverObject;
    /**
     * 交接班对象身份证号
     */
    private String handoverObjectSfzh;
}
