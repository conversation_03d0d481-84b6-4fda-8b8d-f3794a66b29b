package com.rs.module.acp.service.pi;

import com.bsp.common.util.StringUtil;
import com.rs.adapter.bsp.api.UserApi;
import com.rs.module.acp.controller.admin.pi.vo.violationrecord.ViolationRecordListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.violationrecord.ViolationRecordPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.violationrecord.ViolationRecordSaveReqVO;
import com.rs.module.acp.controller.app.pi.vo.ViolationRecordAppSaveReqVO;
import com.rs.module.acp.util.InspectionControlUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.pi.ViolationRecordDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.ViolationRecordDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-巡视管控-违规登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ViolationRecordServiceImpl extends BaseServiceImpl<ViolationRecordDao, ViolationRecordDO> implements ViolationRecordService {

    @Resource
    private ViolationRecordDao violationRecordDao;
    @Autowired
    private UserApi userApi;
    @Override
    public String createViolationRecord(ViolationRecordSaveReqVO createReqVO) {
        // 插入
        ViolationRecordDO entity = BeanUtils.toBean(createReqVO, ViolationRecordDO.class);
        entity.setStatus("03");
        if(StringUtil.isNotEmpty(entity.getDisposalSituation())){
            entity.setStatus("04");
        }

        violationRecordDao.insert(entity);
        new InspectionControlUtil(userApi).dealIsPostCoordination(entity);
        // 返回
        return entity.getId();
    }

    @Override
    public void updateViolationRecord(ViolationRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateViolationRecordExists(updateReqVO.getId());
        // 更新
        ViolationRecordDO entity = BeanUtils.toBean(updateReqVO, ViolationRecordDO.class);
        entity.setStatus("03");
        if(StringUtil.isNotEmpty(entity.getDisposalSituation())){
            entity.setStatus("04");
        }
        violationRecordDao.updateById(entity);
    }

    @Override
    public void deleteViolationRecord(String id) {
        // 校验存在
        validateViolationRecordExists(id);
        // 删除
        violationRecordDao.deleteById(id);
    }

    private void validateViolationRecordExists(String id) {
        if (violationRecordDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-违规登记数据不存在");
        }
    }

    @Override
    public ViolationRecordDO getViolationRecord(String id) {
        return violationRecordDao.selectById(id);
    }

    @Override
    public String appCreateViolationRecord(ViolationRecordAppSaveReqVO createReqVO) {
        // 插入
        ViolationRecordDO entity = BeanUtils.toBean(createReqVO, ViolationRecordDO.class);
        entity.setStatus("03");
        if(StringUtil.isNotEmpty(entity.getDisposalSituation())){
            entity.setStatus("04");
        }
        violationRecordDao.insert(entity);
        new InspectionControlUtil(userApi).dealIsPostCoordination(entity);
        // 返回
        return entity.getId();
    }


}
