package com.rs.module.acp.entity.gj;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-人员表现鉴定表-拘留所 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_performance_jls")
@KeySequence("acp_gj_performance_jls_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_performance_jls")
public class PerformanceJlsDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 所规所纪制度
     */
    private String performanceSgsjzd;
    /**
     * 一日生活管理
     */
    private String performanceYrshgl;
    /**
     * 自杀行为或倾向
     */
    private String performanceZsxwhqx;
    /**
     * 暴力行为或倾向
     */
    private String performanceBlxwhqx;
    /**
     * 列为严管人员情况
     */
    private String performanceLwygryqk;
    /**
     * 参加所内教育情况
     */
    private String performanceCjsnjyqk;
    /**
     * 认错悔过情况
     */
    private String performanceRchgqk;
    /**
     * 其他情况
     */
    private String performanceQtqk;
    /**
     * 需要说明的情况
     */
    private String needSituations;
    /**
     * 状态
     */
    private String status;
    /**
     * 审批人身份证号
     */
    private String approverSfzh;
    /**
     * 审批人姓名
     */
    private String approverXm;
    /**
     * 审批时间
     */
    private Date approverTime;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 审核意见
     */
    private String approvalComments;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

}
