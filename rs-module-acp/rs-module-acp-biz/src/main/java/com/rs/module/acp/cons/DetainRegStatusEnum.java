package com.rs.module.acp.cons;

/**
 * 羁押登记状态枚举
 */
public enum DetainRegStatusEnum {
    
    PENDING("01", "待登记"),
    DRAFT("02", "草稿"),
    SUBMITTED("03", "已提交"),
    NOT_NEED_REGISTER("04", "无需登记");

    private final String code;
    private final String value;

    DetainRegStatusEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 状态码
     * @return 对应的枚举值
     */
    public static DetainRegStatusEnum getByCode(String code) {
        for (DetainRegStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据value获取枚举值
     *
     * @param value 状态值
     * @return 对应的枚举值
     */
    public static DetainRegStatusEnum getByValue(String value) {
        for (DetainRegStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
}