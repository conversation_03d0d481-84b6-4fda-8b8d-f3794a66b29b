package com.rs.module.acp.service.pi;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.pi.vo.violationrecord.ViolationRecordListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.violationrecord.ViolationRecordPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.violationrecord.ViolationRecordSaveReqVO;
import com.rs.module.acp.controller.app.pi.vo.ViolationRecordAppSaveReqVO;
import com.rs.module.acp.entity.pi.ViolationRecordDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-巡视管控-违规登记 Service 接口
 *
 * <AUTHOR>
 */
public interface ViolationRecordService extends IBaseService<ViolationRecordDO>{

    /**
     * 创建实战平台-巡视管控-违规登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createViolationRecord(@Valid ViolationRecordSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-违规登记
     *
     * @param updateReqVO 更新信息
     */
    void updateViolationRecord(@Valid ViolationRecordSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-违规登记
     *
     * @param id 编号
     */
    void deleteViolationRecord(String id);

    /**
     * 获得实战平台-巡视管控-违规登记
     *
     * @param id 编号
     * @return 实战平台-巡视管控-违规登记
     */
    ViolationRecordDO getViolationRecord(String id);

    String appCreateViolationRecord(ViolationRecordAppSaveReqVO createReqVO);
}
