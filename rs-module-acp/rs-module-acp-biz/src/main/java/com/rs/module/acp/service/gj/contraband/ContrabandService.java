package com.rs.module.acp.service.gj.contraband;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.contraband.ContrabandListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.contraband.ContrabandPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.contraband.ContrabandSaveReqVO;
import com.rs.module.acp.entity.gj.ContrabandDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-违禁品登记 Service 接口
 *
 * <AUTHOR>
 */
public interface ContrabandService extends IBaseService<ContrabandDO>{

    /**
     * 创建实战平台-管教业务-违禁品登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createContraband(@Valid ContrabandSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-违禁品登记
     *
     * @param updateReqVO 更新信息
     */
    void updateContraband(@Valid ContrabandSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-违禁品登记
     *
     * @param id 编号
     */
    void deleteContraband(String id);

    /**
     * 获得实战平台-管教业务-违禁品登记
     *
     * @param id 编号
     * @return 实战平台-管教业务-违禁品登记
     */
    ContrabandDO getContraband(String id);

    /**
    * 获得实战平台-管教业务-违禁品登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-违禁品登记分页
    */
    PageResult<ContrabandDO> getContrabandPage(ContrabandPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-违禁品登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-违禁品登记列表
    */
    List<ContrabandDO> getContrabandList(ContrabandListReqVO listReqVO);


    List<ContrabandDO> getContrabandhistoryList(String jgrybm, String id);
}
