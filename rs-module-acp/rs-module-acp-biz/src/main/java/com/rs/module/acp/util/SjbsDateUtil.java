package com.rs.module.acp.util;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

public class SjbsDateUtil {

    public static Date[] getLastWeekRange() {
        Calendar cal = Calendar.getInstance();

        // 设置到本周周一
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);

        // 减去7天得到上周周一
        cal.add(Calendar.DATE, -7);
        Date lastWeekMonday = cal.getTime();

        // 加6天得到上周周日
        cal.add(Calendar.DATE, 6);
        Date lastWeekSunday = cal.getTime();

        return new Date[]{lastWeekMonday, lastWeekSunday};
    }
    private static final DateTimeFormatter DATE_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd");
    /**
     * 获取上周的日期范围（周一至周日）
     * @return 字符串数组，[0]为上周一，[1]为上周日，格式为 yyyy-MM-dd
     */
    public static String[] getLastWeekRangeStr() {
        // 获取当前日期
        LocalDate today = LocalDate.now();

        // 获取本周周一
        LocalDate thisWeekMonday = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));

        // 计算上周范围
        LocalDate lastWeekMonday = thisWeekMonday.minusWeeks(1);
        LocalDate lastWeekSunday = lastWeekMonday.plusDays(6);

        // 格式化为字符串
        return new String[] {
                lastWeekMonday.format(DATE_FORMATTER),
                lastWeekSunday.format(DATE_FORMATTER)
        };
    }
    /**
     * 根据日期范围获取年份和周数
     * @param startDate 开始日期(yyyy-MM-dd)
     * @param endDate 结束日期(yyyy-MM-dd)
     * @return 格式为"年份-第几周"，例如"2025-28"表示2025年第28周
     */
    public static String getYearWeekStr(String startDate, String endDate) {
        LocalDate date = LocalDate.parse(endDate, DATE_FORMATTER);
        WeekFields weekFields = WeekFields.of(Locale.getDefault());
        int weekNumber = date.get(weekFields.weekOfWeekBasedYear());
        int year = date.getYear();
        return String.format("%d-第%02d周", year, weekNumber);
    }

    public static void main(String[] args) {
        String startDate = "2025-07-01";
        String endDate = "2025-07-07";
        String yearWeek = SjbsDateUtil.getYearWeekStr(startDate, endDate);
        System.out.println(yearWeek);
    }
}
