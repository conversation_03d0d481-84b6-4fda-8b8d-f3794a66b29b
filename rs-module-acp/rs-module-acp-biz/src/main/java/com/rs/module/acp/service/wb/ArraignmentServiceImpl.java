package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.util.DicUtils;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.entity.wb.CasePersonnelDO;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.acp.service.gj.InOutRecordsService;
import com.rs.module.base.service.pm.AreaService;
import com.rs.module.base.service.pm.PrisonerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.ArraignmentDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.ArraignmentDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-提讯登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ArraignmentServiceImpl extends BaseServiceImpl<ArraignmentDao, ArraignmentDO> implements ArraignmentService {

    @Resource
    private ArraignmentDao arraignmentDao;
    @Autowired
    private CasePersonnelService casePersonnelService;
    @Autowired
    private AreaService areaService;
    @Autowired
    private WbCommonService wbCommonService;
    @Autowired
    private PrisonerService prisonerService;
    @Autowired
    private CoopCasePersonnelService coopCasePersonnelService;

    @Autowired
    private InOutRecordsService inOutRecordsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createArraignment(ArraignmentSaveReqVO createReqVO) {

        List<CasePersonnelSaveReqVO> casePersonnelList = createReqVO.getCasePersonnelList();

        if (CollectionUtil.isEmpty(casePersonnelList) || casePersonnelList.size() < 2) {
            throw new ServerException("实战平台-窗口业务-提讯登记办案人员数量不能小于2人");
        }

        //判断办案人员是否重复
        Map<String,String> casePersonNameMap = new HashMap<>();
        for(CasePersonnelSaveReqVO casePersonnelSaveReqVO:casePersonnelList){
            String judgeStr = casePersonnelSaveReqVO.getXm()+casePersonnelSaveReqVO.getZjhm();
            if(casePersonNameMap.containsKey(judgeStr)){
                throw new ServerException("实战平台-窗口业务-提讯登记办案人员不能重复");
            }
            casePersonNameMap.put(judgeStr,casePersonnelSaveReqVO.getXm());
        }

        ArraignmentDO arraignment = BeanUtils.toBean(createReqVO, ArraignmentDO.class);

        arraignment.setStartApplyArraignmentTime(createReqVO.getStartApplyArraignmentTime());
        arraignment.setEndApplyArraignmentTime(createReqVO.getEndApplyArraignmentTime());

        arraignment.setId(StringUtil.getGuid32());

        //判断casePersonnelList是否为空，则新增/更新数据插入到acp_wb_case_personnel 表
        for (CasePersonnelSaveReqVO casePersonnelSaveReqVO : casePersonnelList) {
            if(ObjectUtil.isEmpty(casePersonnelSaveReqVO.getId())){
                String casePersonnelId = casePersonnelService.createCasePersonnel(casePersonnelSaveReqVO);
                casePersonnelSaveReqVO.setId(casePersonnelId);
            }
        }

        arraignment.setHandler1Id(casePersonnelList.get(0).getId());
        arraignment.setHandler1Xm(casePersonnelList.get(0).getXm());
        arraignment.setHandler2Id(casePersonnelList.get(1).getId());
        arraignment.setHandler2Xm(casePersonnelList.get(1).getXm());
        if (casePersonnelList.size() > 2) {
            arraignment.setHandler3Id(casePersonnelList.get(2).getId());
            arraignment.setHandler3Xm(casePersonnelList.get(2).getXm());
        }

        Set<String> tjjgmcSet = new HashSet<>();
        casePersonnelList.forEach(x->{tjjgmcSet.add(x.getBadwmc());});
        arraignment.setTjjgmc(String.join("、",tjjgmcSet));

        if(CollectionUtil.isNotEmpty(createReqVO.getCoopCasePersonnelList())){
            coopCasePersonnelService.saveCoopCasePersonnelList(createReqVO.getCoopCasePersonnelList(),arraignment.getId());
        }

        //这里处理提讯凭证事情
        if(CollectionUtil.isNotEmpty(createReqVO.getVoucherList())){
            List<ArraignmentVoucherSaveReqVO> voucherList = createReqVO.getVoucherList();
            StringBuilder evidenceType = new StringBuilder();
            StringBuilder evidenceNumber = new StringBuilder();
            StringBuilder evidenceUrl = new StringBuilder();
            for(int i=0;i<voucherList.size();i++){
                evidenceType.append(voucherList.get(i).getEvidenceType());
                evidenceNumber.append(voucherList.get(i).getEvidenceNumber());
                if(ObjectUtil.isNotEmpty(voucherList.get(i).getEvidenceUrl())){
                    String objectId = wbCommonService.saveFile(null,voucherList.get(i).getEvidenceUrl());
                    if(ObjectUtil.isNotEmpty(objectId)){
                        evidenceUrl.append(objectId);
                    }
                }
                if(i<voucherList.size()-1){
                    evidenceType.append(",");
                    evidenceNumber.append(",");
                    evidenceUrl.append(",");
                }
            }
            arraignment.setEvidenceType(evidenceType.toString());
            arraignment.setEvidenceNumber(evidenceNumber.toString());
            arraignment.setEvidenceUrl(evidenceUrl.toString());
        }

        //这里处理其他专业人员书面证明存储路径
        if(ObjectUtil.isNotEmpty(arraignment.getProfessionalCertUrl())){
            String professionalCertUrl = wbCommonService.saveFile(null,arraignment.getProfessionalCertUrl());
            arraignment.setProfessionalCertUrl(professionalCertUrl);
        }

        arraignment.setStatus("0");
        arraignmentDao.insert(arraignment);

        wbCommonService.registrationReminder(JSONObject.parseObject(JSON.toJSONString(arraignment)), WbConstants.BUSINESS_TYPE_ARRAIGNMENT);

        //待带出登记--传给终端
        WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(arraignment,WbInspectionResultsSaveReqVO.class);
        resultsSaveReqVO.setJgrybm(arraignment.getJgrybm());
        resultsSaveReqVO.setApplMeetingTime(arraignment.getStartApplyArraignmentTime());
        resultsSaveReqVO.setBusinessSubType("0101");
        wbCommonService.saveInOutRecord(resultsSaveReqVO,"01","out","0",false);

        return arraignment.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateArraignment(ArraignmentSaveReqVO updateReqVO) {
        // 校验存在
        validateArraignmentExists(updateReqVO.getId());
        // 更新
        ArraignmentDO updateObj = BeanUtils.toBean(updateReqVO, ArraignmentDO.class);
        arraignmentDao.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteArraignment(String id) {
        // 校验存在
        validateArraignmentExists(id);
        // 删除
        arraignmentDao.deleteById(id);
    }

    private void validateArraignmentExists(String id) {
        if (arraignmentDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-提讯登记数据不存在");
        }
    }

    @Override
    public ArraignmentDO getArraignment(String id) {
        return arraignmentDao.selectById(id);
    }

    @Override
    public PageResult<ArraignmentDO> getArraignmentPage(ArraignmentPageReqVO pageReqVO) {
        return arraignmentDao.selectPage(pageReqVO);
    }

    @Override
    public List<ArraignmentDO> getArraignmentList(ArraignmentListReqVO listReqVO) {
        return arraignmentDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean signIn(String id, String checkInTime) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
//        ArraignmentDO arraignmentDO = signInInit(id);
        LambdaUpdateWrapper<ArraignmentDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ArraignmentDO::getId, id).set(ArraignmentDO::getCheckInTime, DateUtil.parse(checkInTime,"yyyy-MM-dd HH:mm:ss"))
                .set(ArraignmentDO::getCheckInPoliceSfzh,sessionUser.getIdCard())
                .set(ArraignmentDO::getCheckInPolice, sessionUser.getName()).set(ArraignmentDO::getStatus,"1");
        return update(lambdaUpdateWrapper);
    }

    private ArraignmentDO signInInit(String id) {
        ArraignmentDO arraignmentDO = getById(id);
        if (ObjectUtil.isEmpty(arraignmentDO)) {
            throw new ServerException("实战平台-窗口业务-提讯登记数据不存在");
        }
        List<String> casePersonnelIdList = new ArrayList<>();
        casePersonnelIdList.add(arraignmentDO.getHandler1Id());
        casePersonnelIdList.add(arraignmentDO.getHandler2Id());
        if (ObjectUtil.isNotEmpty(arraignmentDO.getHandler3Id())) {
            casePersonnelIdList.add(arraignmentDO.getHandler3Id());
        }
        List<CasePersonnelDO> casePersonnelDOList = casePersonnelService.list(new LambdaQueryWrapper<CasePersonnelDO>()
                .select(CasePersonnelDO::getZjhm, CasePersonnelDO::getXm).in(CasePersonnelDO::getId, casePersonnelIdList));

        String checkInPoliceSfzh = casePersonnelDOList.stream().map(x -> {
            return x.getZjhm();
        }).collect(Collectors.joining(","));
        String checkInPolice = casePersonnelDOList.stream().map(x -> {
            return x.getXm();
        }).collect(Collectors.joining(","));
        arraignmentDO.setCheckInPolice(checkInPolice);
        arraignmentDO.setCheckInPoliceSfzh(checkInPoliceSfzh);
        return arraignmentDO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean escortingInspect(ArraignmentSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        LambdaUpdateWrapper<ArraignmentDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ArraignmentDO::getId, updateReqVO.getId())
                .set(ArraignmentDO::getEscortingPolice, updateReqVO.getEscortingPolice())
                .set(ArraignmentDO::getEscortingPoliceSfzh, updateReqVO.getEscortingPoliceSfzh())
                .set(ArraignmentDO::getEscortingTime, updateReqVO.getEscortingTime())
                .set(ArraignmentDO::getInspectionResult, updateReqVO.getInspectionResult())
                .set(ArraignmentDO::getProhibitedItems, updateReqVO.getProhibitedItems())
                .set(ArraignmentDO::getPhysicalExam, updateReqVO.getPhysicalExam())
                .set(ArraignmentDO::getAbnormalSituations, updateReqVO.getAbnormalSituations())
                .set(ArraignmentDO::getInspectionTime, updateReqVO.getInspectionTime())
                .set(ArraignmentDO::getInspector, updateReqVO.getInspector())
                .set(ArraignmentDO::getInspectorSfzh, updateReqVO.getInspectorSfzh())
                .set(ArraignmentDO::getArraignmentStartTime, updateReqVO.getArraignmentStartTime())
                .set(ArraignmentDO::getStatus,"3");

        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorSfzh())){
            lambdaUpdateWrapper.set(ArraignmentDO::getEscortingOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(ArraignmentDO::getEscortingOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorTime())){
            lambdaUpdateWrapper.set(ArraignmentDO::getEscortingOperatorTime,new Date());
        }

        if("0".equals(updateReqVO.getDataSources())){
            ArraignmentDO arraignmentDO = getById(updateReqVO.getId());
            WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(updateReqVO,WbInspectionResultsSaveReqVO.class);
            resultsSaveReqVO.setJgrybm(arraignmentDO.getJgrybm());
            resultsSaveReqVO.setApplMeetingTime(arraignmentDO.getEndApplyArraignmentTime());
            resultsSaveReqVO.setBusinessSubType("0101");
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"01","out","3",false);
        }

        return update(lambdaUpdateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean returnInspection(ArraignmentSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        LambdaUpdateWrapper<ArraignmentDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ArraignmentDO::getId, updateReqVO.getId())
                .set(ArraignmentDO::getArraignmentEndTime, updateReqVO.getArraignmentEndTime())
                .set(ArraignmentDO::getReturnInspectorSfzh, updateReqVO.getReturnInspectorSfzh())
                .set(ArraignmentDO::getReturnInspector, updateReqVO.getReturnInspector())
                .set(ArraignmentDO::getReturnInspectionTime, updateReqVO.getReturnInspectionTime())
                .set(ArraignmentDO::getReturnInspectionResult, updateReqVO.getReturnInspectionResult())
                .set(ArraignmentDO::getReturnProhibitedItems, updateReqVO.getReturnProhibitedItems())
                .set(ArraignmentDO::getReturnPhysicalExam, updateReqVO.getReturnPhysicalExam())
                .set(ArraignmentDO::getReturnAbnormalSituations, updateReqVO.getReturnAbnormalSituations())
                .set(ArraignmentDO::getReturnTime, updateReqVO.getReturnTime())
                .set(ArraignmentDO::getReturnPolice, updateReqVO.getReturnPolice())
                .set(ArraignmentDO::getReturnPoliceSfzh, updateReqVO.getReturnPoliceSfzh())
                .set(ArraignmentDO::getStatus,"4");
        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorSfzh())){
            lambdaUpdateWrapper.set(ArraignmentDO::getReturnOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(ArraignmentDO::getReturnOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorTime())){
            lambdaUpdateWrapper.set(ArraignmentDO::getReturnOperatorTime,new Date());
        }

        if("0".equals(updateReqVO.getDataSources())){
            ArraignmentDO arraignmentDO = getById(updateReqVO.getId());
            WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(updateReqVO,WbInspectionResultsSaveReqVO.class);
            resultsSaveReqVO.setJgrybm(arraignmentDO.getJgrybm());
            resultsSaveReqVO.setBusinessSubType("0101");
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"01","in","3",false);
        }

        return update(lambdaUpdateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean additionalRecording(ArraignmentSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        ArraignmentDO arraignmentDO = signInInit(updateReqVO.getId());
        LambdaUpdateWrapper<ArraignmentDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ArraignmentDO::getId, updateReqVO.getId())
                .set(ArraignmentDO::getCheckInTime, updateReqVO.getCheckInTime())
                .set(ArraignmentDO::getCheckInPoliceSfzh, arraignmentDO.getCheckInPoliceSfzh())
                .set(ArraignmentDO::getCheckInPolice, arraignmentDO.getCheckInPolice())
                .set(ArraignmentDO::getEscortingPolice, updateReqVO.getEscortingPolice())
                .set(ArraignmentDO::getEscortingPoliceSfzh, updateReqVO.getEscortingPoliceSfzh())
                .set(ArraignmentDO::getEscortingTime, updateReqVO.getEscortingTime())
                .set(ArraignmentDO::getInspectionResult, updateReqVO.getInspectionResult())
                .set(ArraignmentDO::getProhibitedItems, updateReqVO.getProhibitedItems())
                .set(ArraignmentDO::getPhysicalExam, updateReqVO.getPhysicalExam())
                .set(ArraignmentDO::getAbnormalSituations, updateReqVO.getAbnormalSituations())
                .set(ArraignmentDO::getInspectionTime, updateReqVO.getInspectionTime())
                .set(ArraignmentDO::getInspector, updateReqVO.getInspector())
                .set(ArraignmentDO::getInspectorSfzh, updateReqVO.getInspectorSfzh())
                .set(ArraignmentDO::getArraignmentStartTime, updateReqVO.getArraignmentStartTime())
                .set(ArraignmentDO::getArraignmentEndTime, updateReqVO.getArraignmentEndTime())
                .set(ArraignmentDO::getReturnInspectorSfzh, updateReqVO.getReturnInspectorSfzh())
                .set(ArraignmentDO::getReturnInspector, updateReqVO.getReturnInspector())
                .set(ArraignmentDO::getReturnInspectionTime, updateReqVO.getReturnInspectionTime())
                .set(ArraignmentDO::getReturnInspectionResult, updateReqVO.getReturnInspectionResult())
                .set(ArraignmentDO::getReturnProhibitedItems, updateReqVO.getReturnProhibitedItems())
                .set(ArraignmentDO::getReturnPhysicalExam, updateReqVO.getReturnPhysicalExam())
                .set(ArraignmentDO::getReturnAbnormalSituations, updateReqVO.getReturnAbnormalSituations())
                .set(ArraignmentDO::getReturnTime, updateReqVO.getReturnTime())
                .set(ArraignmentDO::getReturnPolice, updateReqVO.getReturnPolice())
                .set(ArraignmentDO::getReturnPoliceSfzh, updateReqVO.getReturnPoliceSfzh())
                .set(ArraignmentDO::getStatus,"4");

        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorSfzh())){
            lambdaUpdateWrapper.set(ArraignmentDO::getEscortingOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(ArraignmentDO::getEscortingOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorTime())){
            lambdaUpdateWrapper.set(ArraignmentDO::getEscortingOperatorTime,new Date());
        }

        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorSfzh())){
            lambdaUpdateWrapper.set(ArraignmentDO::getReturnOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(ArraignmentDO::getReturnOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorTime())){
            lambdaUpdateWrapper.set(ArraignmentDO::getReturnOperatorTime,new Date());
        }

        if("0".equals(updateReqVO.getDataSources())){
            WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(updateReqVO,WbInspectionResultsSaveReqVO.class);
            resultsSaveReqVO.setJgrybm(arraignmentDO.getJgrybm());
            resultsSaveReqVO.setBusinessSubType("0101");
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"01","out","3",true);
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"01","in","3",true);
        }

        return update(lambdaUpdateWrapper);
    }

    @Override
    public ArraignmentRespVO getArraignmentById(String id) {
        ArraignmentDO arraignmentDO = getById(id);
        if (ObjectUtil.isEmpty(arraignmentDO)) {
            throw new ServerException("实战平台-窗口业务-提讯登记数据不存在");
        }
        ArraignmentRespVO arraignmentRespVO = BeanUtils.toBean(arraignmentDO, ArraignmentRespVO.class);
        //组装办案民警信息

        List<String> casePersonnelIdList = new ArrayList<>();
        casePersonnelIdList.add(arraignmentDO.getHandler1Id());
        casePersonnelIdList.add(arraignmentDO.getHandler2Id());
        if (ObjectUtil.isNotEmpty(arraignmentDO.getHandler3Id())) {
            casePersonnelIdList.add(arraignmentDO.getHandler3Id());
        }

        arraignmentRespVO.setCasePersonnelList(casePersonnelService.getByIds(casePersonnelIdList));

        arraignmentRespVO.setCoopCasePersonnelList(coopCasePersonnelService.getListByTargetId(id));

        //这里处理提讯凭证事情
        List<ArraignmentVoucherSaveReqVO> voucherList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(arraignmentDO.getEvidenceType())){
            List<String> evidenceTypeList = Arrays.asList(arraignmentDO.getEvidenceType().split(","));
            List<String> evidenceNumberList = ObjectUtil.isNotEmpty(arraignmentDO.getEvidenceNumber())?
                    Arrays.asList(arraignmentDO.getEvidenceNumber().split(",")):new ArrayList<>();
            List<String> evidenceUrlList = ObjectUtil.isNotEmpty(arraignmentDO.getEvidenceUrl())?
                    Arrays.asList(arraignmentDO.getEvidenceUrl().split(",")):new ArrayList<>();
            for(int i=0;i<evidenceTypeList.size();i++){
                ArraignmentVoucherSaveReqVO voucher = new ArraignmentVoucherSaveReqVO();
                voucher.setEvidenceType(evidenceTypeList.get(i));
                if(CollectionUtil.isNotEmpty(evidenceNumberList)){
                    voucher.setEvidenceNumber(evidenceNumberList.get(i));
                }
                if(CollectionUtil.isNotEmpty(evidenceUrlList)){
                    if(ObjectUtil.isNotEmpty(evidenceUrlList.get(i))){
                        voucher.setEvidenceUrl(wbCommonService.getFile(evidenceUrlList.get(i)));
                    }
                }
                voucher.setEvidenceTypeName(DicUtils.translate("ZD_WB_TXPZLX",voucher.getEvidenceType()));
                voucherList.add(voucher);
            }
        }
        arraignmentRespVO.setVoucherList(voucherList);

        //书面证明
        if(ObjectUtil.isNotEmpty(arraignmentRespVO.getProfessionalCertUrl())){
            arraignmentRespVO.setProfessionalCertUrl(wbCommonService.getFile(arraignmentRespVO.getProfessionalCertUrl()));
        }

        return arraignmentRespVO;
    }

    @Override
    public PageResult<ArraignmentRespVO> getHistoryArraignment(String jgrybm,int pageNo,int pageSize) {
        Page<ArraignmentDO> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<ArraignmentDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(ArraignmentDO::getAddTime,ArraignmentDO::getHandler1Id,
                ArraignmentDO::getHandler2Id,ArraignmentDO::getHandler3Id,ArraignmentDO::getArraignmentReason);
        lambdaQueryWrapper.eq(ArraignmentDO::getJgrybm,jgrybm)
                .orderByDesc(ArraignmentDO::getAddTime);

        Page<ArraignmentDO> arraignmentDOPage = page(page,lambdaQueryWrapper);
        List<ArraignmentRespVO> arraignmentRespVOList = BeanUtils.toBean(arraignmentDOPage.getRecords(),ArraignmentRespVO.class);
        if(CollectionUtil.isEmpty(arraignmentRespVOList)){
            return new PageResult<>(new ArrayList<>() ,0L);
        }

        Set<String> handlerIdSet =  new HashSet<>();

        for(ArraignmentRespVO arraignmentRespVO:arraignmentRespVOList){
            handlerIdSet.add(arraignmentRespVO.getHandler1Id());
            handlerIdSet.add(arraignmentRespVO.getHandler2Id());
            if(ObjectUtil.isNotEmpty(arraignmentRespVO.getHandler3Id())){
                handlerIdSet.add(arraignmentRespVO.getHandler3Id());
            }
        }

        List<String> handlerIdList = handlerIdSet.stream().collect(Collectors.toList());

        LambdaQueryWrapper<CasePersonnelDO> casePersonnelLambdaQueryWrapper = new LambdaQueryWrapper<>();
        casePersonnelLambdaQueryWrapper.select(CasePersonnelDO::getId,CasePersonnelDO::getXm,CasePersonnelDO::getBadwmc)
                .in(CasePersonnelDO::getId,handlerIdList);
        List<CasePersonnelDO> casePersonnelDOList = casePersonnelService.list(casePersonnelLambdaQueryWrapper);

        Map<String,CasePersonnelDO> casePersonnelMap = new HashMap<>();
        casePersonnelDOList.forEach(x->{casePersonnelMap.put(x.getId(),x);});

        for(ArraignmentRespVO arraignmentRespVO:arraignmentRespVOList){
            StringBuilder handlerXm = new StringBuilder();
            Set<String> badwmcSet = new HashSet<>();

            if(casePersonnelMap.containsKey(arraignmentRespVO.getHandler1Id())){
                handlerXm.append(casePersonnelMap.get(arraignmentRespVO.getHandler1Id()).getXm());
                badwmcSet.add(casePersonnelMap.get(arraignmentRespVO.getHandler1Id()).getBadwmc());
            }

            if(casePersonnelMap.containsKey(arraignmentRespVO.getHandler2Id())){
                handlerXm.append("、");
                handlerXm.append(casePersonnelMap.get(arraignmentRespVO.getHandler2Id()).getXm());
                badwmcSet.add(casePersonnelMap.get(arraignmentRespVO.getHandler2Id()).getBadwmc());
            }

            if(ObjectUtil.isNotEmpty(arraignmentRespVO.getHandler3Id())){
                if(casePersonnelMap.containsKey(arraignmentRespVO.getHandler2Id())){
                    handlerXm.append("、");
                    handlerXm.append(casePersonnelMap.get(arraignmentRespVO.getHandler3Id()).getXm());
                    badwmcSet.add(casePersonnelMap.get(arraignmentRespVO.getHandler3Id()).getBadwmc());
                }
            }
            arraignmentRespVO.setHandlerXm(handlerXm.toString());
            arraignmentRespVO.setBadwmc(String.join("、",badwmcSet));
        }

        return new PageResult<>(arraignmentRespVOList,arraignmentDOPage.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean allocationRoom(String id, String roomId) {
        return update(new LambdaUpdateWrapper<ArraignmentDO>().eq(ArraignmentDO::getId,id).set(ArraignmentDO::getRoomId,roomId).set(ArraignmentDO::getStatus,"2"));
    }

}
