package com.rs.module.acp.controller.admin.db.vo.jxjsdj;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-减刑登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class JxjsdjSnhyjfhReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("复核所务会编号")
    private String fhswhbh;

    @ApiModelProperty("重新召开所务会复核日期")
    private Date cxzkswhfhrq;

    @ApiModelProperty("复核意见（1、同意；2、不同意）")
    private String fhyj;

}
