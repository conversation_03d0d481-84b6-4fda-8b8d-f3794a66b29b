package com.rs.module.acp.dao.wb;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.GoodsDeliveryDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-窗口业务-物品顾送登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface GoodsDeliveryDao extends IBaseDao<GoodsDeliveryDO> {


    default PageResult<GoodsDeliveryDO> selectPage(GoodsDeliveryPageReqVO reqVO) {
        Page<GoodsDeliveryDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<GoodsDeliveryDO> wrapper = new LambdaQueryWrapperX<GoodsDeliveryDO>()
            .eqIfPresent(GoodsDeliveryDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(GoodsDeliveryDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(GoodsDeliveryDO::getDeliveryNo, reqVO.getDeliveryNo())
            .betweenIfPresent(GoodsDeliveryDO::getDeliveryDate, reqVO.getDeliveryDate())
            .likeIfPresent(GoodsDeliveryDO::getSenderName, reqVO.getSenderName())
            .eqIfPresent(GoodsDeliveryDO::getGender, reqVO.getGender())
            .eqIfPresent(GoodsDeliveryDO::getIdType, reqVO.getIdType())
            .eqIfPresent(GoodsDeliveryDO::getIdNumber, reqVO.getIdNumber())
            .eqIfPresent(GoodsDeliveryDO::getRelationship, reqVO.getRelationship())
            .eqIfPresent(GoodsDeliveryDO::getContact, reqVO.getContact())
            .eqIfPresent(GoodsDeliveryDO::getHouseholdAddress, reqVO.getHouseholdAddress())
            .eqIfPresent(GoodsDeliveryDO::getGoodsPhotoPath, reqVO.getGoodsPhotoPath())
            .eqIfPresent(GoodsDeliveryDO::getGoodsInfo, reqVO.getGoodsInfo())
            .eqIfPresent(GoodsDeliveryDO::getGoodsTotal, reqVO.getGoodsTotal())
            .eqIfPresent(GoodsDeliveryDO::getStatus, reqVO.getStatus())
            .betweenIfPresent(GoodsDeliveryDO::getReceiptTime, reqVO.getReceiptTime())
            .eqIfPresent(GoodsDeliveryDO::getSignature, reqVO.getSignature())
            .eqIfPresent(GoodsDeliveryDO::getRejectionReason, reqVO.getRejectionReason())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(GoodsDeliveryDO::getAddTime);
        }
        Page<GoodsDeliveryDO> goodsDeliveryPage = selectPage(page, wrapper);
        return new PageResult<>(goodsDeliveryPage.getRecords(), goodsDeliveryPage.getTotal());
    }
    default List<GoodsDeliveryDO> selectList(GoodsDeliveryListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<GoodsDeliveryDO>()
            .eqIfPresent(GoodsDeliveryDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(GoodsDeliveryDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(GoodsDeliveryDO::getDeliveryNo, reqVO.getDeliveryNo())
            .betweenIfPresent(GoodsDeliveryDO::getDeliveryDate, reqVO.getDeliveryDate())
            .likeIfPresent(GoodsDeliveryDO::getSenderName, reqVO.getSenderName())
            .eqIfPresent(GoodsDeliveryDO::getGender, reqVO.getGender())
            .eqIfPresent(GoodsDeliveryDO::getIdType, reqVO.getIdType())
            .eqIfPresent(GoodsDeliveryDO::getIdNumber, reqVO.getIdNumber())
            .eqIfPresent(GoodsDeliveryDO::getRelationship, reqVO.getRelationship())
            .eqIfPresent(GoodsDeliveryDO::getContact, reqVO.getContact())
            .eqIfPresent(GoodsDeliveryDO::getHouseholdAddress, reqVO.getHouseholdAddress())
            .eqIfPresent(GoodsDeliveryDO::getGoodsPhotoPath, reqVO.getGoodsPhotoPath())
            .eqIfPresent(GoodsDeliveryDO::getGoodsInfo, reqVO.getGoodsInfo())
            .eqIfPresent(GoodsDeliveryDO::getGoodsTotal, reqVO.getGoodsTotal())
            .eqIfPresent(GoodsDeliveryDO::getStatus, reqVO.getStatus())
            .betweenIfPresent(GoodsDeliveryDO::getReceiptTime, reqVO.getReceiptTime())
            .eqIfPresent(GoodsDeliveryDO::getSignature, reqVO.getSignature())
            .eqIfPresent(GoodsDeliveryDO::getRejectionReason, reqVO.getRejectionReason())
        .orderByDesc(GoodsDeliveryDO::getAddTime));    }


    }
