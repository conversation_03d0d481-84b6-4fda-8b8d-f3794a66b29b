package com.rs.module.acp.service.zh;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.zh.vo.*;
import com.rs.module.acp.entity.zh.DutySuperviseRecordDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 综合管理-值班管理-值班督导记录 Service 接口
 *
 * <AUTHOR>
 */
public interface DutySuperviseRecordService extends IBaseService<DutySuperviseRecordDO>{

    /**
     * 创建综合管理-值班管理-值班督导记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createDutySuperviseRecord(@Valid DutySuperviseRecordSaveReqVO createReqVO);

    /**
     * 更新综合管理-值班管理-值班督导记录
     *
     * @param updateReqVO 更新信息
     */
    void updateDutySuperviseRecord(@Valid DutySuperviseRecordSaveReqVO updateReqVO);

    /**
     * 删除综合管理-值班管理-值班督导记录
     *
     * @param id 编号
     */
    void deleteDutySuperviseRecord(String id);

    /**
     * 获得综合管理-值班管理-值班督导记录
     *
     * @param id 编号
     * @return 综合管理-值班管理-值班督导记录
     */
    DutySuperviseRecordDO getDutySuperviseRecord(String id);

    /**
    * 获得综合管理-值班管理-值班督导记录分页
    *
    * @param pageReqVO 分页查询
    * @return 综合管理-值班管理-值班督导记录分页
    */
    PageResult<DutySuperviseRecordDO> getDutySuperviseRecordPage(DutySuperviseRecordPageReqVO pageReqVO);

    /**
    * 获得综合管理-值班管理-值班督导记录列表
    *
    * @param listReqVO 查询条件
    * @return 综合管理-值班管理-值班督导记录列表
    */
    List<DutySuperviseRecordDO> getDutySuperviseRecordList(DutySuperviseRecordListReqVO listReqVO);


    List<AcpZhStaffDutyTemplateCurrentInfo> getCurrentDutyInfo(String orgCode);

    List<StaffDutyRecordPersonVO> getDutyRecordPersonsByRecordId(String recordId);
}
