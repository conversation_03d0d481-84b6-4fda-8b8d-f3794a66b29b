package com.rs.module.acp.service.gj.confinement;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.confinement.*;
import com.rs.module.acp.entity.gj.confinement.ConfinementExtendDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-延长禁闭呈批 Service 接口
 *
 * <AUTHOR>
 */
public interface ConfinementExtendService extends IBaseService<ConfinementExtendDO>{

    /**
     * 创建实战平台-管教业务-延长禁闭呈批
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createConfinementExtend(@Valid ConfinementExtendSaveReqVO createReqVO)throws Exception;

    /**
     * 更新实战平台-管教业务-延长禁闭呈批
     *
     * @param updateReqVO 更新信息
     */
    void updateConfinementExtend(@Valid ConfinementExtendSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-延长禁闭呈批
     *
     * @param id 编号
     */
    void deleteConfinementExtend(String id);

    /**
     * 获得实战平台-管教业务-延长禁闭呈批
     *
     * @param id 编号
     * @return 实战平台-管教业务-延长禁闭呈批
     */
    ConfinementExtendDO getConfinementExtend(String id);

    /**
    * 获得实战平台-管教业务-延长禁闭呈批分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-延长禁闭呈批分页
    */
    PageResult<ConfinementExtendDO> getConfinementExtendPage(ConfinementExtendPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-延长禁闭呈批列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-延长禁闭呈批列表
    */
    List<ConfinementExtendDO> getConfinementExtendList(ConfinementExtendListReqVO listReqVO);


    boolean approve(ConfinementExtendApproveVO approveReqVO) throws Exception;

    //单个延长禁闭轨迹查询
    List<ConfinementFlowApproveTrackVO> getApproveTrack(String id);

    List<ConfinementExtendDO> getConfinementExtendByConfinementId(String confinementId);

}
