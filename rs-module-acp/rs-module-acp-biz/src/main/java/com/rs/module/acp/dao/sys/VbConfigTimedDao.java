package com.rs.module.acp.dao.sys;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.sys.VbConfigTimedDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.sys.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-语音播报-定时配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface VbConfigTimedDao extends IBaseDao<VbConfigTimedDO> {


    default PageResult<VbConfigTimedDO> selectPage(VbConfigTimedPageReqVO reqVO) {
        Page<VbConfigTimedDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<VbConfigTimedDO> wrapper = new LambdaQueryWrapperX<VbConfigTimedDO>()
            .likeIfPresent(VbConfigTimedDO::getVbName, reqVO.getVbName())
            .eqIfPresent(VbConfigTimedDO::getQueryScript, reqVO.getQueryScript())
            .eqIfPresent(VbConfigTimedDO::getContent, reqVO.getContent())
            .eqIfPresent(VbConfigTimedDO::getVbNum, reqVO.getVbNum())
            .eqIfPresent(VbConfigTimedDO::getPriority, reqVO.getPriority())
            .eqIfPresent(VbConfigTimedDO::getIsEnabled, reqVO.getIsEnabled())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(VbConfigTimedDO::getAddTime);
        }
        Page<VbConfigTimedDO> vbConfigTimedPage = selectPage(page, wrapper);
        return new PageResult<>(vbConfigTimedPage.getRecords(), vbConfigTimedPage.getTotal());
    }
    default List<VbConfigTimedDO> selectList(VbConfigTimedListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<VbConfigTimedDO>()
            .likeIfPresent(VbConfigTimedDO::getVbName, reqVO.getVbName())
            .eqIfPresent(VbConfigTimedDO::getQueryScript, reqVO.getQueryScript())
            .eqIfPresent(VbConfigTimedDO::getContent, reqVO.getContent())
            .eqIfPresent(VbConfigTimedDO::getVbNum, reqVO.getVbNum())
            .eqIfPresent(VbConfigTimedDO::getPriority, reqVO.getPriority())
            .eqIfPresent(VbConfigTimedDO::getIsEnabled, reqVO.getIsEnabled())
        .orderByDesc(VbConfigTimedDO::getAddTime));    }


    }
