package com.rs.module.acp.service.gj.printdocument;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.printdocument.PrintDocumentListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.printdocument.PrintDocumentPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.printdocument.PrintDocumentSaveReqVO;
import com.rs.module.acp.entity.gj.PrintDocumentDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-文书打印预览 Service 接口
 *
 * <AUTHOR>
 */
public interface PrintDocumentService extends IBaseService<PrintDocumentDO>{

    /**
     * 创建实战平台-管教业务-文书打印预览
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrintDocument(@Valid PrintDocumentSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-文书打印预览
     *
     * @param updateReqVO 更新信息
     */
    void updatePrintDocument(@Valid PrintDocumentSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-文书打印预览
     *
     * @param id 编号
     */
    void deletePrintDocument(String id);

    /**
     * 获得实战平台-管教业务-文书打印预览
     *
     * @param id 编号
     * @return 实战平台-管教业务-文书打印预览
     */
    PrintDocumentDO getPrintDocument(String id);

    /**
    * 获得实战平台-管教业务-文书打印预览分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-文书打印预览分页
    */
    PageResult<PrintDocumentDO> getPrintDocumentPage(PrintDocumentPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-文书打印预览列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-文书打印预览列表
    */
    List<PrintDocumentDO> getPrintDocumentList(PrintDocumentListReqVO listReqVO);


}
