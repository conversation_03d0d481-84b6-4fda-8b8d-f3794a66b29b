package com.rs.module.acp.controller.app.pi.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@ApiModel(description = "仓内外屏 - 实战平台-巡视管控-保护性约束分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProtRestraintAppPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("机构编码")
    private String orgCode;
    @ApiModelProperty("监室ID")
    private String roomId;

    @ApiModelProperty("约束登记开始时间")
    private Date startTime;

    @ApiModelProperty("约束登记结束时间")
    private Date endTime;

    @ApiModelProperty("排序属性")
    List<OrderItem> orderFields;
    @ApiModelProperty("日期类型 0:全部,1:今天,2:昨天,3:近一周")
    private String dateType;
}
