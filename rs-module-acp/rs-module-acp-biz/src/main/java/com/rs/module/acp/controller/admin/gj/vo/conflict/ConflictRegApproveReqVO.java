package com.rs.module.acp.controller.admin.gj.vo.conflict;

/**
 *
 * <AUTHOR>
 * @Date 2025/3/20 13:54
 */

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@ApiModel(description = "管理后台 - 管教业务-社会矛盾化解登记审批 Request VO")
@Data
public class ConflictRegApproveReqVO {


    @ApiModelProperty("事件编号")
    @NotBlank(message = "事件编号不能为空")
    private String eventCode;
    @ApiModelProperty(value = "状态（01:已登记、02：待审批（所领导审批）、03: 审批不通过、04：调解中、05：调解完成、06：待确认、07：被退回）")
    private String regStatus;
    @ApiModelProperty("审批结果")
    private String approvalResult;
    @ApiModelProperty("审批意见")
    private String approvalComments;
    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;
    @ApiModelProperty("审批人姓名")
    private String approverXm;
    @ApiModelProperty("审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approverTime;


}
