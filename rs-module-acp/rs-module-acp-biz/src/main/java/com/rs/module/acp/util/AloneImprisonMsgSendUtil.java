package com.rs.module.acp.util;

import com.bsp.common.util.StringUtil;
import com.rs.module.acp.entity.gj.AloneImprisonDO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.sys.MsgAddVO;
import com.rs.module.base.util.MsgUtil;
import com.rs.util.DicUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
@Slf4j
public class AloneImprisonMsgSendUtil {
    private AloneImprisonDO aloneImprisonDO;

    private PrisonerVwRespVO vwRespVO;
    private String businessId;
    public AloneImprisonMsgSendUtil(AloneImprisonDO aloneImprisonDO,PrisonerVwRespVO vwRespVO){
        this.aloneImprisonDO=aloneImprisonDO;
        this.vwRespVO=vwRespVO;
    }
    /**
     * 主协管民警
     * 监室调整登记
     * 单独关押审批通过后
     */
    public void cellAdjustment(){
        String moduleCode = "ALONE_IMPRISON_CELL_ADJUSTMENT";
        sendCommon(moduleCode,businessId);
    }

    /**
     * 主协管民警
     * 安全检查登记
     * 单独关押期间每天8:00
     */
    public void safetyInspection(){
        String moduleCode = "ALONE_IMPRISON_SAFETY_INSPECTION";
        businessId = aloneImprisonDO.getOrgCode();
        sendCommon(moduleCode,businessId);
    }

    /**
     * 主协管民警
     * 谈话教育登记
     * 单独关押审批通过后
     */
    public void conversationEducation(){
        String moduleCode = "ALONE_IMPRISON_TALK_EDUCATION";
        businessId = aloneImprisonDO.getOrgCode();
        sendCommon(moduleCode,businessId);
    }

    /**
     * 医生岗
     * 巡诊登记
     * 单独关押期间每天8:00
     */
    public void visitRegistration(){
        String moduleCode= "ALONE_IMPRISON_VISIT_REGISTRATION";
        sendCommon(moduleCode,businessId);
    }
    public  void sendCommon(String moduleCode,String businessId){
        MsgAddVO msgAddVO = new MsgAddVO();
        Map<String, Object> contentData = new HashMap<>();
        msgAddVO.setModuleCode(moduleCode);//模块编码
        if(StringUtil.isEmpty(businessId)){
            businessId = aloneImprisonDO.getId();
        }
        msgAddVO.setJgrybm(aloneImprisonDO.getJgrybm());
        msgAddVO.setBusinessId(businessId);//业务主键id
        msgAddVO.setToOrgCode(aloneImprisonDO.getOrgCode());//发送给具体监所的id
        if("04".equals(aloneImprisonDO.getRegisterReason())){
            contentData.put("registerReason",aloneImprisonDO.getSpecificReason());
        }else{

            contentData.put("registerReason",DicUtils.translate( "ZD_DDGY_DJYY", aloneImprisonDO.getRegisterReason()));
        }
        contentData.put("roomName",aloneImprisonDO.getOldRoomName());
        contentData.put("prisonerName",vwRespVO.getXm());
        msgAddVO.setContentData(contentData);//消息内容数据
        MsgUtil.sendMsg(msgAddVO);
    }
    public static void sendMsg( AloneImprisonDO aloneImprisonDO,PrisonerVwRespVO vwRespVO) {
        AloneImprisonMsgSendUtil util = new AloneImprisonMsgSendUtil(aloneImprisonDO,vwRespVO);
        util.cellAdjustment();
        util.safetyInspection();
        util.conversationEducation();
        util.visitRegistration();
    }
}
