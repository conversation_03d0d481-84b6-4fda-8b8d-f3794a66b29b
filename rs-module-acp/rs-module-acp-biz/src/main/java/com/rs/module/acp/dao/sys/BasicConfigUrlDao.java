package com.rs.module.acp.dao.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.sys.vo.BasicConfigUrlListReqVO;
import com.rs.module.acp.controller.admin.sys.vo.BasicConfigUrlPageReqVO;
import com.rs.module.acp.entity.sys.BasicConfigUrlDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 实战平台-系统基础配置链接 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BasicConfigUrlDao extends IBaseDao<BasicConfigUrlDO> {


    default PageResult<BasicConfigUrlDO> selectPage(BasicConfigUrlPageReqVO reqVO) {
        Page<BasicConfigUrlDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<BasicConfigUrlDO> wrapper = new LambdaQueryWrapperX<BasicConfigUrlDO>()
            .eqIfPresent(BasicConfigUrlDO::getSystemId, reqVO.getSystemId())
            .eqIfPresent(BasicConfigUrlDO::getUrlType, reqVO.getUrlType())
            .likeIfPresent(BasicConfigUrlDO::getUrlName, reqVO.getUrlName())
            .eqIfPresent(BasicConfigUrlDO::getUrl, reqVO.getUrl())
            .eqIfPresent(BasicConfigUrlDO::getIsShow, reqVO.getIsShow())
            .eqIfPresent(BasicConfigUrlDO::getSort, reqVO.getSort())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(BasicConfigUrlDO::getAddTime);
        }
        Page<BasicConfigUrlDO> basicConfigUrlPage = selectPage(page, wrapper);
        return new PageResult<>(basicConfigUrlPage.getRecords(), basicConfigUrlPage.getTotal());
    }
    default List<BasicConfigUrlDO> selectList(BasicConfigUrlListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BasicConfigUrlDO>()
            .eqIfPresent(BasicConfigUrlDO::getSystemId, reqVO.getSystemId())
            .eqIfPresent(BasicConfigUrlDO::getUrlType, reqVO.getUrlType())
            .likeIfPresent(BasicConfigUrlDO::getUrlName, reqVO.getUrlName())
            .eqIfPresent(BasicConfigUrlDO::getUrl, reqVO.getUrl())
            .eqIfPresent(BasicConfigUrlDO::getIsShow, reqVO.getIsShow())
            .eqIfPresent(BasicConfigUrlDO::getSort, reqVO.getSort())
        .orderByDesc(BasicConfigUrlDO::getAddTime));    }


    }
