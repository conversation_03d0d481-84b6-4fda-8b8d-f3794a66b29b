package com.rs.module.acp.service.db.components;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 服务上下文类，用于根据业务类型动态获取对应的业务服务实现
 * 
 * 当前支持：
 * - kss（看守所）
 * - jls（拘留所）
 */
@Lazy
@Component
public class RegistrationInfoServiceContext {

    private final Map<String, RegistrationInfoService> serviceMap;

    /**
     * 构造函数，注入不同类型的业务服务实例
     * 
     * @param detainRegKssService 看守所业务服务
     * @param inRecordJlsService   拘留所业务服务
     * @param inRecordJdsService  戒毒所业务服务
     */
    public RegistrationInfoServiceContext(
        @Qualifier("detainRegKssService") RegistrationInfoService detainRegKssService,
        @Qualifier("inRecordJlsService") RegistrationInfoService inRecordJlsService,
        @Qualifier("inRecordJdsService") RegistrationInfoService inRecordJdsService) {
        this.serviceMap = new HashMap<>();
        serviceMap.put("kss", detainRegKssService);
        serviceMap.put("jls", inRecordJlsService);
        serviceMap.put("jds", inRecordJdsService);
    }

    /**
     * 获取指定业务类型的注册信息处理服务
     * 
     * @param type 业务类型（"kss" 或 "jls"）
     * @return 对应的服务实现
     * @throws IllegalArgumentException 如果类型不支持
     */
    public RegistrationInfoService getService(String type) {
        if (type == null || type.isEmpty()) {
            type = "kss";
        }
        RegistrationInfoService service = serviceMap.get(type);
        if (service == null) {
            throw new IllegalArgumentException("Unsupported registration info service type: " + type);
        }
        return service;
    }
}