package com.rs.module.acp.controller.app.pi;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.admin.pi.vo.violationrecord.ViolationRecordSaveReqVO;
import com.rs.module.acp.controller.app.pi.vo.ViolationRecordAppSaveReqVO;
import com.rs.module.acp.service.pi.ViolationRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "仓内外屏-巡视管控-违规登记")
@RestController
@RequestMapping("/app/acp/pi/violationRecord")
@Validated
public class ViolationRecordAppController {

    @Resource
    private ViolationRecordService violationRecordService;

    @PostMapping("/create")
    @ApiOperation(value = "仓内外屏-巡视管控-违规登记")
    public CommonResult<String> appCreateViolationRecord(@Valid @RequestBody ViolationRecordAppSaveReqVO createReqVO) {
        return success(violationRecordService.appCreateViolationRecord(createReqVO));
    }

   /* @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-巡视管控-违规登记")
    public CommonResult<Boolean> updateViolationRecord(@Valid @RequestBody ViolationRecordAppSaveReqVO updateReqVO) {
        violationRecordService.updateViolationRecord(updateReqVO);
        return success(true);
    }*/

}
