package com.rs.module.acp.controller.app.gj;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictRegRespVO;
import com.rs.module.acp.controller.app.gj.vo.conflict.AppConflictConfirmReqVO;
import com.rs.module.acp.controller.app.gj.vo.conflict.AppConflictSaveReqVO;
import com.rs.module.acp.service.gj.conflict.ConflictRegService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "内屏-管教业务-社会矛盾")
@RestController
@RequestMapping("/app/acp/conflict")
@Validated
public class AppConflictController {

    @Resource
    private ConflictRegService conflictRegService;

    @PostMapping("/applyConflict")
    @ApiOperation(value = "在押人员申请登记调解")
    public CommonResult applyConflict(@Valid @RequestBody AppConflictSaveReqVO reqVO) {
        conflictRegService.applyConflict(reqVO);
        return success();
    }

    @PostMapping("/confirm")
    @ApiOperation(value = "在押人员矛盾登记信息确认")
    public CommonResult confirm(@Valid @RequestBody AppConflictConfirmReqVO queryVO) {
        conflictRegService.confirm(queryVO);
        return success(true);
    }

    @GetMapping("/getByEventCode")
    @ApiOperation(value = "查询矛盾详情通过事件编号或者事件ID")
    @Parameter(name = "eventCode", description = "事件编号", required = true)
    public CommonResult<ConflictRegRespVO> getConflictRegByEventCodeVO(@RequestParam("eventCode") String eventCode) {
        ConflictRegRespVO respVO = conflictRegService.getConflictRegByEventCodeVO(eventCode);
        return success(respVO);
    }

}
