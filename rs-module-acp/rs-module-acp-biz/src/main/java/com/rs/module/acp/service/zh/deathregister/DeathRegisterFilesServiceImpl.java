package com.rs.module.acp.service.zh.deathregister;

import com.bsp.common.util.CollectionUtil;
import com.rs.module.acp.controller.admin.zh.vo.deathregister.DeathRegisterFilesSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.rs.module.acp.entity.zh.DeathRegisterFilesDO;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.zh.DeathRegisterFilesDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-综合管理-死亡登记文件 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DeathRegisterFilesServiceImpl extends BaseServiceImpl<DeathRegisterFilesDao, DeathRegisterFilesDO> implements DeathRegisterFilesService {

    @Resource
    private DeathRegisterFilesDao deathRegisterFilesDao;

    @Override
    public String createDeathRegisterFiles(DeathRegisterFilesSaveReqVO createReqVO) {
        // 插入
        DeathRegisterFilesDO deathRegisterFiles = BeanUtils.toBean(createReqVO, DeathRegisterFilesDO.class);
        deathRegisterFilesDao.insert(deathRegisterFiles);
        // 返回
        return deathRegisterFiles.getId();
    }

    @Override
    public void updateDeathRegisterFiles(DeathRegisterFilesSaveReqVO updateReqVO) {
        // 校验存在
        validateDeathRegisterFilesExists(updateReqVO.getId());
        // 更新
        DeathRegisterFilesDO updateObj = BeanUtils.toBean(updateReqVO, DeathRegisterFilesDO.class);
        deathRegisterFilesDao.updateById(updateObj);
    }

    @Override
    public void deleteDeathRegisterFiles(String id) {
        // 校验存在
        validateDeathRegisterFilesExists(id);
        // 删除
        deathRegisterFilesDao.deleteById(id);
    }

    private void validateDeathRegisterFilesExists(String id) {
        if (deathRegisterFilesDao.selectById(id) == null) {
            throw new ServerException("实战平台-综合管理-死亡登记文件数据不存在");
        }
    }

    @Override
    public DeathRegisterFilesDO getDeathRegisterFiles(String id) {
        return deathRegisterFilesDao.selectById(id);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAndSaveFile(String deathRegisterId, List<DeathRegisterFilesSaveReqVO> fileList) {
        this.lambdaUpdate()
                .eq(DeathRegisterFilesDO::getDeathRegisterId, deathRegisterId)
                .remove();
        if (CollectionUtil.isNull(fileList)) {
            return;
        }
        AtomicInteger sort = new AtomicInteger(0);
        List<DeathRegisterFilesDO> manageFiles = fileList.stream()
                .map(file -> DeathRegisterFilesDO.builder()
                        .deathRegisterId(deathRegisterId)
                        .fileUrl(file.getFileUrl())
                        .fileName(file.getFileName())
                        .deathAppraiseFileType(file.getDeathAppraiseFileType())
                        //.fileSort(sort.incrementAndGet())
                        .build())
                .collect(Collectors.toList());
        this.saveBatch(manageFiles);
    }
    @Override
    public List<DeathRegisterFilesDO> getFileList(String deathRegisterId) {
        return this.lambdaQuery()
                .eq(DeathRegisterFilesDO::getDeathRegisterId, deathRegisterId)
                //.orderByAsc(SacpDeathRegisterFiles::getFileSort)
                .list()
                .stream()
                .map(file -> DeathRegisterFilesDO.builder()
                        .fileName(file.getFileName())
                        .fileUrl(file.getFileUrl())
                        .deathAppraiseFileType(file.getDeathAppraiseFileType())
                        .build())
                .collect(Collectors.toList());
    }

}
