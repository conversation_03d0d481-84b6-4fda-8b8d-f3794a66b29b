package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.LeadershipReceptionDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-所领导接待登记 Service 接口
 *
 * <AUTHOR>
 */
public interface LeadershipReceptionService extends IBaseService<LeadershipReceptionDO>{

    /**
     * 创建实战平台-窗口业务-所领导接待登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createLeadershipReception(@Valid LeadershipReceptionSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-所领导接待登记
     *
     * @param updateReqVO 更新信息
     */
    void updateLeadershipReception(@Valid LeadershipReceptionSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-所领导接待登记
     *
     * @param id 编号
     */
    void deleteLeadershipReception(String id);

    /**
     * 获得实战平台-窗口业务-所领导接待登记
     *
     * @param id 编号
     * @return 实战平台-窗口业务-所领导接待登记
     */
    LeadershipReceptionDO getLeadershipReception(String id);

    /**
    * 获得实战平台-窗口业务-所领导接待登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-所领导接待登记分页
    */
    PageResult<LeadershipReceptionDO> getLeadershipReceptionPage(LeadershipReceptionPageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-所领导接待登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-所领导接待登记列表
    */
    List<LeadershipReceptionDO> getLeadershipReceptionList(LeadershipReceptionListReqVO listReqVO);


}
