package com.rs.module.acp.service.zh.indicatorcate;

import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorSubListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorSubPageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorSubSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.zh.IndicatorSubDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.zh.IndicatorSubDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 综合管理-绩效考核子指标 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class IndicatorSubServiceImpl extends BaseServiceImpl<IndicatorSubDao, IndicatorSubDO> implements IndicatorSubService {

    @Resource
    private IndicatorSubDao indicatorSubDao;

    @Override
    public String createIndicatorSub(IndicatorSubSaveReqVO createReqVO) {
        // 插入
        IndicatorSubDO indicatorSub = BeanUtils.toBean(createReqVO, IndicatorSubDO.class);
        indicatorSubDao.insert(indicatorSub);
        // 返回
        return indicatorSub.getId();
    }

    @Override
    public void updateIndicatorSub(IndicatorSubSaveReqVO updateReqVO) {
        // 校验存在
        validateIndicatorSubExists(updateReqVO.getId());
        // 更新
        IndicatorSubDO updateObj = BeanUtils.toBean(updateReqVO, IndicatorSubDO.class);
        indicatorSubDao.updateById(updateObj);
    }

    @Override
    public void deleteIndicatorSub(String id) {
        // 校验存在
        validateIndicatorSubExists(id);
        // 删除
        indicatorSubDao.deleteById(id);
    }

    private void validateIndicatorSubExists(String id) {
        if (indicatorSubDao.selectById(id) == null) {
            throw new ServerException("综合管理-绩效考核子指标数据不存在");
        }
    }

    @Override
    public IndicatorSubDO getIndicatorSub(String id) {
        return indicatorSubDao.selectById(id);
    }

    @Override
    public PageResult<IndicatorSubDO> getIndicatorSubPage(IndicatorSubPageReqVO pageReqVO) {
        return indicatorSubDao.selectPage(pageReqVO);
    }

    @Override
    public List<IndicatorSubDO> getIndicatorSubList(IndicatorSubListReqVO listReqVO) {
        return indicatorSubDao.selectList(listReqVO);
    }


}
