package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseExtendListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseExtendPageReqVO;
import com.rs.module.acp.entity.gj.EquipmentUseExtendDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-械具使用延长呈批 Dao
*
* <AUTHOR>
*/
@Mapper
public interface EquipmentUseExtendDao extends IBaseDao<EquipmentUseExtendDO> {


    default PageResult<EquipmentUseExtendDO> selectPage(EquipmentUseExtendPageReqVO reqVO) {
        Page<EquipmentUseExtendDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<EquipmentUseExtendDO> wrapper = new LambdaQueryWrapperX<EquipmentUseExtendDO>()
            .eqIfPresent(EquipmentUseExtendDO::getEquipmentUseId, reqVO.getEquipmentUseId())
            .eqIfPresent(EquipmentUseExtendDO::getExtendReason, reqVO.getExtendReason())
            .eqIfPresent(EquipmentUseExtendDO::getExtendDay, reqVO.getExtendDay())
            .betweenIfPresent(EquipmentUseExtendDO::getExtendTime, reqVO.getExtendTime())
            .eqIfPresent(EquipmentUseExtendDO::getStatus, reqVO.getStatus())
            .eqIfPresent(EquipmentUseExtendDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(EquipmentUseExtendDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(EquipmentUseExtendDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(EquipmentUseExtendDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(EquipmentUseExtendDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(EquipmentUseExtendDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(EquipmentUseExtendDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(EquipmentUseExtendDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(EquipmentUseExtendDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(EquipmentUseExtendDO::getAddTime);
        }
        Page<EquipmentUseExtendDO> equipmentUseExtendPage = selectPage(page, wrapper);
        return new PageResult<>(equipmentUseExtendPage.getRecords(), equipmentUseExtendPage.getTotal());
    }
    default List<EquipmentUseExtendDO> selectList(EquipmentUseExtendListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<EquipmentUseExtendDO>()
            .eqIfPresent(EquipmentUseExtendDO::getEquipmentUseId, reqVO.getEquipmentUseId())
            .eqIfPresent(EquipmentUseExtendDO::getExtendReason, reqVO.getExtendReason())
            .eqIfPresent(EquipmentUseExtendDO::getExtendDay, reqVO.getExtendDay())
            .betweenIfPresent(EquipmentUseExtendDO::getExtendTime, reqVO.getExtendTime())
            .eqIfPresent(EquipmentUseExtendDO::getStatus, reqVO.getStatus())
            .eqIfPresent(EquipmentUseExtendDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(EquipmentUseExtendDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(EquipmentUseExtendDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(EquipmentUseExtendDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(EquipmentUseExtendDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(EquipmentUseExtendDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(EquipmentUseExtendDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(EquipmentUseExtendDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(EquipmentUseExtendDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(EquipmentUseExtendDO::getAddTime));    }


    }
