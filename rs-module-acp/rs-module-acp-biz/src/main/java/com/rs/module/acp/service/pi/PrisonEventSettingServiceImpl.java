package com.rs.module.acp.service.pi;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventSettingListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventSettingPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventSettingRespVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventSettingSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.entity.pi.PrisonEventSettingDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.PrisonEventSettingDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-巡视管控-所警情管理-报警联动设置(所情来源) Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrisonEventSettingServiceImpl extends BaseServiceImpl<PrisonEventSettingDao, PrisonEventSettingDO> implements PrisonEventSettingService {

    @Resource
    private PrisonEventSettingDao prisonEventSettingDao;

    @Override
    public String createPrisonEventSetting(PrisonEventSettingSaveReqVO createReqVO) {
        // 插入
        PrisonEventSettingDO prisonEventSetting = BeanUtils.toBean(createReqVO, PrisonEventSettingDO.class);
        prisonEventSettingDao.insert(prisonEventSetting);
        // 返回
        return prisonEventSetting.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePrisonEventSetting(PrisonEventSettingSaveReqVO updateReqVO) {
        // 校验存在
        validatePrisonEventSettingExists(updateReqVO.getId());
        // 更新
        PrisonEventSettingDO updateObj = BeanUtils.toBean(updateReqVO, PrisonEventSettingDO.class);
        prisonEventSettingDao.updateById(updateObj);
    }

    @Override
    public void deletePrisonEventSetting(String id) {
        // 校验存在
        validatePrisonEventSettingExists(id);
        // 删除
        prisonEventSettingDao.deleteById(id);
    }

    private void validatePrisonEventSettingExists(String id) {
        if (prisonEventSettingDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-所警情管理-报警联动设置(所情来源)数据不存在");
        }
    }

    @Override
    public PrisonEventSettingDO getPrisonEventSetting(String id) {
        return prisonEventSettingDao.selectById(id);
    }

    @Override
    public PageResult<PrisonEventSettingDO> getPrisonEventSettingPage(PrisonEventSettingPageReqVO pageReqVO) {
        return prisonEventSettingDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrisonEventSettingDO> getPrisonEventSettingList(PrisonEventSettingListReqVO listReqVO) {
        return prisonEventSettingDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeStatus(String id) {
        PrisonEventSettingDO prisonEventSettingDO = prisonEventSettingDao.selectById(id);

        if(ObjectUtil.isEmpty(prisonEventSettingDO)){
            throw new ServerException("所情联动配置不存在");
        }
        if(prisonEventSettingDO.getEnabled() == 1){
            prisonEventSettingDO.setEnabled((short) 0);
        }else {
            prisonEventSettingDO.setEnabled((short) 1);
        }
        return updateById(prisonEventSettingDO);
    }


}
