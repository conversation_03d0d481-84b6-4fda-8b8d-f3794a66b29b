package com.rs.module.acp.dao.wb;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.VisitorInfoDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-来访人员信息 Dao
*
* <AUTHOR>
*/
@Mapper
public interface VisitorInfoDao extends IBaseDao<VisitorInfoDO> {


    default PageResult<VisitorInfoDO> selectPage(VisitorInfoPageReqVO reqVO) {
        Page<VisitorInfoDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<VisitorInfoDO> wrapper = new LambdaQueryWrapperX<VisitorInfoDO>()
            .eqIfPresent(VisitorInfoDO::getVisitorRegId, reqVO.getVisitorRegId())
            .likeIfPresent(VisitorInfoDO::getName, reqVO.getName())
            .eqIfPresent(VisitorInfoDO::getIdType, reqVO.getIdType())
            .eqIfPresent(VisitorInfoDO::getIdNumber, reqVO.getIdNumber())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(VisitorInfoDO::getAddTime);
        }
        Page<VisitorInfoDO> visitorInfoPage = selectPage(page, wrapper);
        return new PageResult<>(visitorInfoPage.getRecords(), visitorInfoPage.getTotal());
    }
    default List<VisitorInfoDO> selectList(VisitorInfoListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<VisitorInfoDO>()
            .eqIfPresent(VisitorInfoDO::getVisitorRegId, reqVO.getVisitorRegId())
            .likeIfPresent(VisitorInfoDO::getName, reqVO.getName())
            .eqIfPresent(VisitorInfoDO::getIdType, reqVO.getIdType())
            .eqIfPresent(VisitorInfoDO::getIdNumber, reqVO.getIdNumber())
        .orderByDesc(VisitorInfoDO::getAddTime));    }


    }
