package com.rs.module.acp.service.todo;

import com.rs.module.acp.model.Todo;

import java.util.List;

public class TodoContext {
    private String jgrybm;
    private TodoBusinessType todoType;
    private List<Todo> todos;

    // Getters and setters
    public String getJgrybm() {
        return jgrybm;
    }

    public void setJgrybm(String jgrybm) {
        this.jgrybm = jgrybm;
    }

    public TodoBusinessType getTodoType() {
        return todoType;
    }

    public void setTodoType(TodoBusinessType todoType) {
        this.todoType = todoType;
    }

    public List<Todo> getTodos() {
        return todos;
    }

    public void setTodos(List<Todo> todos) {
        this.todos = todos;
    }
}