package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.dao.wb.FamilyMeetingVideoDao;
import com.rs.module.acp.entity.wb.ArraignmentDO;
import com.rs.module.acp.entity.wb.FamilyMeetingVideoDO;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.util.DicUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 实战平台-窗口业务-单向视频家属会见 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FamilyMeetingVideoServiceImpl extends BaseServiceImpl<FamilyMeetingVideoDao, FamilyMeetingVideoDO> implements FamilyMeetingVideoService {

    @Resource
    private FamilyMeetingVideoDao familyMeetingVideoDao;

    @Autowired
    private SocialRelationsService socialRelationsService;

    @Autowired
    private WbCommonService wbCommonService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createFamilyMeetingVideo(FamilyMeetingVideoSaveReqVO createReqVO) {

        // 插入
        FamilyMeetingVideoDO familyMeetingVideo = BeanUtils.toBean(createReqVO, FamilyMeetingVideoDO.class);

        //保存家属
        socialRelationsService.saveSocialRelationsByJgrybm(createReqVO.getFamilyList(),createReqVO.getJgrybm());

        if(CollectionUtil.isNotEmpty(createReqVO.getFamilyList())){
            List<SocialRelationsChildSaveReqVO> familyList = createReqVO.getFamilyList();

            familyMeetingVideo.setFamilyMember1Id(familyList.get(0).getId());
            familyMeetingVideo.setFamilyMember1Name(familyList.get(0).getName());
            familyMeetingVideo.setFamilyMember1Gender(familyList.get(0).getGender());
            familyMeetingVideo.setFamilyMember1IdType(familyList.get(0).getIdType());
            familyMeetingVideo.setFamilyMember1IdNumber(familyList.get(0).getIdNumber());
            familyMeetingVideo.setFamilyMember1Relationship(familyList.get(0).getRelationship());
            familyMeetingVideo.setFamilyMember1Contact(familyList.get(0).getContact());
            familyMeetingVideo.setFamilyMember1WorkUnit(familyList.get(0).getWorkUnit());
            familyMeetingVideo.setFamilyMember1Address(familyList.get(0).getAddress());
            familyMeetingVideo.setFamilyMember1Occupation(familyList.get(0).getOccupation());
            familyMeetingVideo.setFamilyMember1ImageUrl(familyList.get(0).getImageUrl());
            familyMeetingVideo.setFamilyMember1RelationsAttch(familyList.get(0).getRelationsAttch());

            if(familyList.size()>1){
                familyMeetingVideo.setFamilyMember2Id(familyList.get(1).getId());
                familyMeetingVideo.setFamilyMember2Name(familyList.get(1).getName());
                familyMeetingVideo.setFamilyMember2Gender(familyList.get(1).getGender());
                familyMeetingVideo.setFamilyMember2IdType(familyList.get(1).getIdType());
                familyMeetingVideo.setFamilyMember2IdNumber(familyList.get(1).getIdNumber());
                familyMeetingVideo.setFamilyMember2Relationship(familyList.get(1).getRelationship());
                familyMeetingVideo.setFamilyMember2Contact(familyList.get(1).getContact());
                familyMeetingVideo.setFamilyMember2WorkUnit(familyList.get(1).getWorkUnit());
                familyMeetingVideo.setFamilyMember2Address(familyList.get(1).getAddress());
                familyMeetingVideo.setFamilyMember2Occupation(familyList.get(1).getOccupation());
                familyMeetingVideo.setFamilyMember2ImageUrl(familyList.get(1).getImageUrl());
                familyMeetingVideo.setFamilyMember1RelationsAttch(familyList.get(1).getRelationsAttch());
            }
            if(familyList.size()>2){
                familyMeetingVideo.setFamilyMember3Id(familyList.get(2).getId());
                familyMeetingVideo.setFamilyMember3Name(familyList.get(2).getName());
                familyMeetingVideo.setFamilyMember3Gender(familyList.get(2).getGender());
                familyMeetingVideo.setFamilyMember3IdType(familyList.get(2).getIdType());
                familyMeetingVideo.setFamilyMember3IdNumber(familyList.get(2).getIdNumber());
                familyMeetingVideo.setFamilyMember3Relationship(familyList.get(2).getRelationship());
                familyMeetingVideo.setFamilyMember3Contact(familyList.get(2).getContact());
                familyMeetingVideo.setFamilyMember3WorkUnit(familyList.get(2).getWorkUnit());
                familyMeetingVideo.setFamilyMember3Address(familyList.get(2).getAddress());
                familyMeetingVideo.setFamilyMember3Occupation(familyList.get(2).getOccupation());
                familyMeetingVideo.setFamilyMember3ImageUrl(familyList.get(2).getImageUrl());
                familyMeetingVideo.setFamilyMember1RelationsAttch(familyList.get(2).getRelationsAttch());
            }
        }


        familyMeetingVideo.setId(StringUtil.getGuid32());

        //发起审批
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", familyMeetingVideo.getId());
        variables.put("busType", MsgBusTypeEnum.CK_JSDXSPHJ.getCode());
        String msgUrl = "";
        JSONObject result = BspApprovalUtil.defaultStartProcess("chuangkouyewujiashuhuijianshenp",familyMeetingVideo.getId(),"家属会见审批",msgUrl,variables);
        JSONObject data = result.getJSONObject("data");
        JSONObject bpmTrail = data.getJSONObject("bpmTrail");
        familyMeetingVideo.setActInstId(bpmTrail.getString("actInstId"));
        familyMeetingVideo.setTaskId(bpmTrail.getString("taskId"));

        familyMeetingVideo.setStatus("10-1");

        familyMeetingVideoDao.insert(familyMeetingVideo);

        // 发送消息
        wbCommonService.registrationReminder(JSONObject.parseObject(JSON.toJSONString(familyMeetingVideo)), WbConstants.BUSINESS_TYPE_FAMILY_MEETING_VIDEO);

        // 返回
        return familyMeetingVideo.getId();
    }

    @Override
    public void updateFamilyMeetingVideo(FamilyMeetingVideoSaveReqVO updateReqVO) {
        // 校验存在
        validateFamilyMeetingVideoExists(updateReqVO.getId());
        // 更新
        FamilyMeetingVideoDO updateObj = BeanUtils.toBean(updateReqVO, FamilyMeetingVideoDO.class);
        familyMeetingVideoDao.updateById(updateObj);
    }

    @Override
    public void deleteFamilyMeetingVideo(String id) {
        // 校验存在
        validateFamilyMeetingVideoExists(id);
        // 删除
        familyMeetingVideoDao.deleteById(id);
    }

    private void validateFamilyMeetingVideoExists(String id) {
        if (familyMeetingVideoDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-单向视频家属会见数据不存在");
        }
    }

    @Override
    public FamilyMeetingVideoDO getFamilyMeetingVideo(String id) {
        return familyMeetingVideoDao.selectById(id);
    }

    @Override
    public PageResult<FamilyMeetingVideoDO> getFamilyMeetingVideoPage(FamilyMeetingVideoPageReqVO pageReqVO) {
        return familyMeetingVideoDao.selectPage(pageReqVO);
    }

    @Override
    public List<FamilyMeetingVideoDO> getFamilyMeetingVideoList(FamilyMeetingVideoListReqVO listReqVO) {
        return familyMeetingVideoDao.selectList(listReqVO);
    }

    @Override
    public List<JSONObject> getremoteNumbering() {
        return familyMeetingVideoDao.getremoteNumbering(LocalTime.now().getHour() < 12?"morning":"afternoon", SessionUserUtil.getSessionUser().getOrgCode());
    }

    @Override
    public JSONObject limitNumber(String jgrybm) {
        JSONObject resJson = new JSONObject();
        resJson.put("allow",false);
        //是否该监管人员本月内【单向视频会见】次数大于等于1，若是，则提示：【提示：单向视频会见每月仅限 1 次。您申请的对象本月已使用该权益。】
        JSONObject queryJson = familyMeetingVideoDao.limitNumber(jgrybm,SessionUserUtil.getSessionUser().getOrgCode());
        if(ObjectUtil.isEmpty(queryJson)){
            resJson.put("allow",true);
            return resJson;
        }

        String msg = "";
        if("0".equals(queryJson.getString("businesstype"))){
            msg = String.format("单向视频会见每月仅限 1 次。您申请的对象本月已使用该权益.");
        }else {
            msg = String.format("该人员存在未完结的单向视频会见，请完成后再进行申请");
        }
        resJson.put("msg",msg);
        return resJson;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean notifyFamilyMembers(FamilyMeetingVideoSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        LambdaUpdateWrapper<FamilyMeetingVideoDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(FamilyMeetingVideoDO::getId, updateReqVO.getId())
                .set(FamilyMeetingVideoDO::getNotificationFamilyTime, updateReqVO.getNotificationFamilyTime())
                .set(FamilyMeetingVideoDO::getNotificationMeetingDate, updateReqVO.getNotificationMeetingDate())
                .set(FamilyMeetingVideoDO::getStatus, "1");
        if(ObjectUtil.isEmpty(updateReqVO.getNotificationOperatorSfzh())){
            lambdaUpdateWrapper.set(FamilyMeetingVideoDO::getNotificationOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(FamilyMeetingVideoDO::getNotificationOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getNotificationOperatorTime())){
            lambdaUpdateWrapper.set(FamilyMeetingVideoDO::getNotificationOperatorTime,new Date());
        }


        return update(lambdaUpdateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean meetingRegister(FamilyMeetingVideoSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        LambdaUpdateWrapper<FamilyMeetingVideoDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(FamilyMeetingVideoDO::getId, updateReqVO.getId())
                .set(FamilyMeetingVideoDO::getMeetingStartTime, updateReqVO.getMeetingStartTime())
                .set(FamilyMeetingVideoDO::getMeetingEndTime, updateReqVO.getMeetingEndTime())
                .set(FamilyMeetingVideoDO::getRemarks, updateReqVO.getRemarks())
                .set(FamilyMeetingVideoDO::getStatus, "2");
        if(ObjectUtil.isEmpty(updateReqVO.getCheckOperatorSfzh())){
            lambdaUpdateWrapper.set(FamilyMeetingVideoDO::getCheckOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(FamilyMeetingVideoDO::getCheckOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getCheckOperatorTime())){
            lambdaUpdateWrapper.set(FamilyMeetingVideoDO::getCheckOperatorTime,new Date());
        }

        if("0".equals(updateReqVO.getDataSources())){
            FamilyMeetingVideoDO familyMeetingVideoDO = getById(updateReqVO.getId());
            WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(updateReqVO,WbInspectionResultsSaveReqVO.class);
            resultsSaveReqVO.setJgrybm(familyMeetingVideoDO.getJgrybm());
            resultsSaveReqVO.setApplMeetingTime(familyMeetingVideoDO.getNotificationMeetingDate());
            resultsSaveReqVO.setBusinessSubType("0302");
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"03","out","0",false);
        }

        return update(lambdaUpdateWrapper);
    }

    @Override
    public FamilyMeetingVideoRespVO getFamilyMeetingVideoById(String id) {
        FamilyMeetingVideoDO familyMeetingVideoDO = getById(id);
        FamilyMeetingVideoRespVO familyMeetingVideoRespVO = BeanUtils.toBean(familyMeetingVideoDO,FamilyMeetingVideoRespVO.class);

        //家属列表
        List<SocialRelationsRespVO> familyList = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(familyMeetingVideoDO.getFamilyMember1Id())){
            SocialRelationsRespVO socialRelationsRespVO = new SocialRelationsRespVO();
            socialRelationsRespVO.setId(familyMeetingVideoDO.getFamilyMember1Id());
            socialRelationsRespVO.setName(familyMeetingVideoDO.getFamilyMember1Name());
            socialRelationsRespVO.setGender(familyMeetingVideoDO.getFamilyMember1Gender());
            socialRelationsRespVO.setIdType(familyMeetingVideoDO.getFamilyMember1IdType());
            socialRelationsRespVO.setIdNumber(familyMeetingVideoDO.getFamilyMember1IdNumber());
            socialRelationsRespVO.setRelationship(familyMeetingVideoDO.getFamilyMember1Relationship());
            socialRelationsRespVO.setContact(familyMeetingVideoDO.getFamilyMember1Contact());
            socialRelationsRespVO.setWorkUnit(familyMeetingVideoDO.getFamilyMember1WorkUnit());
            socialRelationsRespVO.setAddress(familyMeetingVideoDO.getFamilyMember1Address());
            socialRelationsRespVO.setOccupation(familyMeetingVideoDO.getFamilyMember1Occupation());
            socialRelationsRespVO.setImageUrl(familyMeetingVideoDO.getFamilyMember1ImageUrl());
            if(ObjectUtil.isNotEmpty(familyMeetingVideoDO.getFamilyMember1RelationsAttch())){
                socialRelationsRespVO.setRelationsAttch(wbCommonService.getFile(familyMeetingVideoDO.getFamilyMember1RelationsAttch()));
            }
            familyList.add(socialRelationsRespVO);
        }

        if(ObjectUtil.isNotEmpty(familyMeetingVideoDO.getFamilyMember2Id())){
            SocialRelationsRespVO socialRelationsRespVO = new SocialRelationsRespVO();
            socialRelationsRespVO.setId(familyMeetingVideoDO.getFamilyMember2Id());
            socialRelationsRespVO.setName(familyMeetingVideoDO.getFamilyMember2Name());
            socialRelationsRespVO.setGender(familyMeetingVideoDO.getFamilyMember2Gender());
            socialRelationsRespVO.setIdType(familyMeetingVideoDO.getFamilyMember2IdType());
            socialRelationsRespVO.setIdNumber(familyMeetingVideoDO.getFamilyMember2IdNumber());
            socialRelationsRespVO.setRelationship(familyMeetingVideoDO.getFamilyMember2Relationship());
            socialRelationsRespVO.setContact(familyMeetingVideoDO.getFamilyMember2Contact());
            socialRelationsRespVO.setWorkUnit(familyMeetingVideoDO.getFamilyMember2WorkUnit());
            socialRelationsRespVO.setAddress(familyMeetingVideoDO.getFamilyMember2Address());
            socialRelationsRespVO.setOccupation(familyMeetingVideoDO.getFamilyMember2Occupation());
            socialRelationsRespVO.setImageUrl(familyMeetingVideoDO.getFamilyMember2ImageUrl());
            if(ObjectUtil.isNotEmpty(familyMeetingVideoDO.getFamilyMember2RelationsAttch())){
                socialRelationsRespVO.setRelationsAttch(wbCommonService.getFile(familyMeetingVideoDO.getFamilyMember2RelationsAttch()));
            }
            familyList.add(socialRelationsRespVO);
        }

        if(ObjectUtil.isNotEmpty(familyMeetingVideoDO.getFamilyMember3Id())){
            SocialRelationsRespVO socialRelationsRespVO = new SocialRelationsRespVO();
            socialRelationsRespVO.setId(familyMeetingVideoDO.getFamilyMember3Id());
            socialRelationsRespVO.setName(familyMeetingVideoDO.getFamilyMember3Name());
            socialRelationsRespVO.setGender(familyMeetingVideoDO.getFamilyMember3Gender());
            socialRelationsRespVO.setIdType(familyMeetingVideoDO.getFamilyMember3IdType());
            socialRelationsRespVO.setIdNumber(familyMeetingVideoDO.getFamilyMember3IdNumber());
            socialRelationsRespVO.setRelationship(familyMeetingVideoDO.getFamilyMember3Relationship());
            socialRelationsRespVO.setContact(familyMeetingVideoDO.getFamilyMember3Contact());
            socialRelationsRespVO.setWorkUnit(familyMeetingVideoDO.getFamilyMember3WorkUnit());
            socialRelationsRespVO.setAddress(familyMeetingVideoDO.getFamilyMember3Address());
            socialRelationsRespVO.setOccupation(familyMeetingVideoDO.getFamilyMember3Occupation());
            socialRelationsRespVO.setImageUrl(familyMeetingVideoDO.getFamilyMember3ImageUrl());
            if(ObjectUtil.isNotEmpty(familyMeetingVideoDO.getFamilyMember3RelationsAttch())){
                socialRelationsRespVO.setRelationsAttch(wbCommonService.getFile(familyMeetingVideoDO.getFamilyMember3RelationsAttch()));
            }
            familyList.add(socialRelationsRespVO);
        }
        familyMeetingVideoRespVO.setFamilyList(familyList);
        return familyMeetingVideoRespVO;
    }

    @Override
    public PageResult<FamilyMeetingVideoRespVO> getHistoryMeetingByJgrybm(String jgrybm, int pageNo, int pageSize) {
        Page<FamilyMeetingVideoDO> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<FamilyMeetingVideoDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(FamilyMeetingVideoDO::getId,FamilyMeetingVideoDO::getAddTime,FamilyMeetingVideoDO::getFamilyMember1Name,
                FamilyMeetingVideoDO::getFamilyMember2Name,FamilyMeetingVideoDO::getFamilyMember3Name,FamilyMeetingVideoDO::getMeetingStartTime,
                FamilyMeetingVideoDO::getMeetingEndTime);
        lambdaQueryWrapper.eq(FamilyMeetingVideoDO::getJgrybm,jgrybm)
                .orderByDesc(FamilyMeetingVideoDO::getAddTime);

        Page<FamilyMeetingVideoDO> familyMeetingVideoDOPage = page(page,lambdaQueryWrapper);
        List<FamilyMeetingVideoRespVO> familyMeetingVideoRespVOList = BeanUtils.toBean(familyMeetingVideoDOPage.getRecords(),FamilyMeetingVideoRespVO.class);

        if(CollectionUtil.isEmpty(familyMeetingVideoRespVOList)){
            return new PageResult<>(new ArrayList<>() ,0L);
        }

        for(FamilyMeetingVideoDO familyMeetingVideoDO:familyMeetingVideoDOPage.getRecords()){
            //姓名
            StringBuilder familyMemberName = new StringBuilder();
            familyMemberName.append(familyMeetingVideoDO.getFamilyMember1Name());
            familyMemberName.append("、");
            familyMemberName.append(familyMeetingVideoDO.getFamilyMember2Name());
            if(ObjectUtil.isNotEmpty(familyMeetingVideoDO.getFamilyMember3Name())){
                familyMemberName.append("、");
                familyMemberName.append(familyMeetingVideoDO.getFamilyMember3Name());
            }
            //证件号码
            StringBuilder IdNumber = new StringBuilder();
            IdNumber.append(familyMeetingVideoDO.getFamilyMember1IdNumber());
            IdNumber.append("、");
            IdNumber.append(familyMeetingVideoDO.getFamilyMember2IdNumber());
            if(ObjectUtil.isNotEmpty(familyMeetingVideoDO.getFamilyMember3IdNumber())){
                IdNumber.append("、");
                IdNumber.append(familyMeetingVideoDO.getFamilyMember3IdNumber());
            }

            //社会关系
            StringBuilder relationship = new StringBuilder();
            if(ObjectUtil.isNotEmpty(familyMeetingVideoDO.getFamilyMember1Relationship())){
                relationship.append(DicUtils.translate("ZD_GABBZ_SHGX",familyMeetingVideoDO.getFamilyMember1Relationship()));
            }
            relationship.append("、");
            if(ObjectUtil.isNotEmpty(familyMeetingVideoDO.getFamilyMember2Relationship())){
                relationship.append(DicUtils.translate("ZD_GABBZ_SHGX",familyMeetingVideoDO.getFamilyMember2Relationship()));
            }
            if(ObjectUtil.isNotEmpty(familyMeetingVideoDO.getFamilyMember3Relationship())){
                relationship.append("、");
                relationship.append(DicUtils.translate("ZD_GABBZ_SHGX",familyMeetingVideoDO.getFamilyMember3Relationship()));
            }
            //会见时间
            StringBuilder meetingTime = new StringBuilder();
            if(ObjectUtil.isNotEmpty(familyMeetingVideoDO.getMeetingStartTime())){
                meetingTime.append(DateUtil.format(familyMeetingVideoDO.getMeetingStartTime(),"yyyy-MM-dd HH:mm"));
            }
            meetingTime.append("~");
            if(ObjectUtil.isNotEmpty(familyMeetingVideoDO.getMeetingEndTime())){
                meetingTime.append(DateUtil.format(familyMeetingVideoDO.getMeetingEndTime(),"HH:mm"));
            }

            for(FamilyMeetingVideoRespVO familyMeetingVideoRespVO:familyMeetingVideoRespVOList){
                if(familyMeetingVideoRespVO.getId().equals(familyMeetingVideoDO.getId())){
                    familyMeetingVideoRespVO.setFamilyName(familyMemberName.toString());
                    familyMeetingVideoRespVO.setIdNumber(IdNumber.toString());
                    familyMeetingVideoRespVO.setRelationship(relationship.toString());
                    familyMeetingVideoRespVO.setMeetingTime(meetingTime.toString());
                    break;
                }
            }
        }

        return new PageResult<>(familyMeetingVideoRespVOList ,familyMeetingVideoDOPage.getTotal());
    }
}
