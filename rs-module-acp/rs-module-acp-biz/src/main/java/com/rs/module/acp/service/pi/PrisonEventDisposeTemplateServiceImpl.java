package com.rs.module.acp.service.pi;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventDisposeTemplateListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventDisposeTemplatePageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventDisposeTemplateRespVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventDisposeTemplateSaveReqVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.pi.PrisonEventDisposeTemplateDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.PrisonEventDisposeTemplateDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-巡视管控-所情处置模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrisonEventDisposeTemplateServiceImpl extends BaseServiceImpl<PrisonEventDisposeTemplateDao, PrisonEventDisposeTemplateDO> implements PrisonEventDisposeTemplateService {

    @Resource
    private PrisonEventDisposeTemplateDao prisonEventDisposeTemplateDao;

    @Override
    public String createPrisonEventDisposeTemplate(PrisonEventDisposeTemplateSaveReqVO createReqVO) {
        // 插入
        PrisonEventDisposeTemplateDO prisonEventDisposeTemplate = BeanUtils.toBean(createReqVO, PrisonEventDisposeTemplateDO.class);
        prisonEventDisposeTemplateDao.insert(prisonEventDisposeTemplate);
        // 返回
        return prisonEventDisposeTemplate.getId();
    }

    @Override
    public void updatePrisonEventDisposeTemplate(PrisonEventDisposeTemplateSaveReqVO updateReqVO) {
        // 校验存在
        validatePrisonEventDisposeTemplateExists(updateReqVO.getId());
        // 更新
        PrisonEventDisposeTemplateDO updateObj = BeanUtils.toBean(updateReqVO, PrisonEventDisposeTemplateDO.class);
        prisonEventDisposeTemplateDao.updateById(updateObj);
    }

    @Override
    public void deletePrisonEventDisposeTemplate(String id) {
        // 校验存在
        validatePrisonEventDisposeTemplateExists(id);
        // 删除
        prisonEventDisposeTemplateDao.deleteById(id);
    }

    private void validatePrisonEventDisposeTemplateExists(String id) {
        if (prisonEventDisposeTemplateDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-所情处置模板数据不存在");
        }
    }

    @Override
    public PrisonEventDisposeTemplateDO getPrisonEventDisposeTemplate(String id) {
        return prisonEventDisposeTemplateDao.selectById(id);
    }

    @Override
    public PageResult<PrisonEventDisposeTemplateDO> getPrisonEventDisposeTemplatePage(PrisonEventDisposeTemplatePageReqVO pageReqVO) {
        return prisonEventDisposeTemplateDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrisonEventDisposeTemplateDO> getPrisonEventDisposeTemplateList(PrisonEventDisposeTemplateListReqVO listReqVO) {
        return prisonEventDisposeTemplateDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean savePrisonEventDisposeTemplateList(List<PrisonEventDisposeTemplateSaveReqVO> disposeTemplateList, String typeId) {
        List<PrisonEventDisposeTemplateDO> templateDOList = BeanUtils.toBean(disposeTemplateList, PrisonEventDisposeTemplateDO.class);
        Map<String, String> idMap = new HashMap<>();
        for (PrisonEventDisposeTemplateDO templateDO : templateDOList) {
            if (ObjectUtil.isEmpty(templateDO.getId())) {
                templateDO.setId(StringUtil.getGuid32());
            }
            templateDO.setTypeId(typeId);
            idMap.put(templateDO.getId(), templateDO.getId());
        }

        //删除不需要的
        LambdaQueryWrapper<PrisonEventDisposeTemplateDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(PrisonEventDisposeTemplateDO::getId);
        lambdaQueryWrapper.eq(PrisonEventDisposeTemplateDO::getTypeId, typeId)
                .eq(PrisonEventDisposeTemplateDO::getOrgCode, SessionUserUtil.getSessionUser().getOrgCode());
        List<PrisonEventDisposeTemplateDO> dataBaseList = list(lambdaQueryWrapper);
        if (CollectionUtil.isNotEmpty(dataBaseList)) {
            List<String> delIdList = new ArrayList<>();
            for (PrisonEventDisposeTemplateDO templateDO : dataBaseList) {
                if (!idMap.containsKey(templateDO.getId())) {
                    delIdList.add(templateDO.getId());
                }
            }
            if (CollectionUtil.isNotEmpty(delIdList)) {
                prisonEventDisposeTemplateDao.deleteBatchIds(delIdList);
            }
        }

        return saveOrUpdateBatch(templateDOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePrisonEventDisposeTemplateByTypeId(String typeId) {
        LambdaQueryWrapper<PrisonEventDisposeTemplateDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PrisonEventDisposeTemplateDO::getTypeId, typeId)
                .eq(PrisonEventDisposeTemplateDO::getOrgCode, SessionUserUtil.getSessionUser().getOrgCode());
        prisonEventDisposeTemplateDao.delete(lambdaQueryWrapper);
    }

    @Override
    public List<PrisonEventDisposeTemplateRespVO> getPrisonEventDisposeTemplateListByTypeId(String typeId) {
        LambdaQueryWrapper<PrisonEventDisposeTemplateDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PrisonEventDisposeTemplateDO::getTypeId, typeId)
                .eq(PrisonEventDisposeTemplateDO::getOrgCode, SessionUserUtil.getSessionUser().getOrgCode())
                .orderByAsc(PrisonEventDisposeTemplateDO::getOrderId);
        List<PrisonEventDisposeTemplateDO> templateDOList = list(lambdaQueryWrapper);
        if(CollectionUtil.isEmpty(templateDOList)){
            return new ArrayList<>();
        }
        return BeanUtils.toBean(templateDOList,PrisonEventDisposeTemplateRespVO.class);
    }
}
