package com.rs.module.acp.service.wb;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.wb.vo.HallInfoConfigListReqVO;
import com.rs.module.acp.controller.admin.wb.vo.HallInfoConfigPageReqVO;
import com.rs.module.acp.controller.admin.wb.vo.HallInfoConfigRespVO;
import com.rs.module.acp.controller.admin.wb.vo.HallInfoConfigSaveReqVO;
import com.rs.module.acp.dao.wb.HallInfoConfigDao;
import com.rs.module.acp.entity.wb.HallInfoConfigDO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;


import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-服务大厅信息发布配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HallInfoConfigServiceImpl extends BaseServiceImpl<HallInfoConfigDao, HallInfoConfigDO> implements HallInfoConfigService {

    @Resource
    private HallInfoConfigDao hallInfoConfigDao;

    @Override
    public String createHallInfoConfig(HallInfoConfigSaveReqVO createReqVO) {
        // 插入
        HallInfoConfigDO hallInfoConfig = BeanUtils.toBean(createReqVO, HallInfoConfigDO.class);
        hallInfoConfigDao.insert(hallInfoConfig);
        // 返回
        return hallInfoConfig.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateHallInfoConfig(HallInfoConfigSaveReqVO updateReqVO) {

        HallInfoConfigDO updateObj = BeanUtils.toBean(updateReqVO, HallInfoConfigDO.class);


        //判断是否存在
        LambdaQueryWrapper<HallInfoConfigDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(HallInfoConfigDO::getId,HallInfoConfigDO::getIsCarousel)
                .eq(HallInfoConfigDO::getOrgCode, SessionUserUtil.getSessionUser().getOrgCode());
        HallInfoConfigDO hallInfoConfigDO = getOne(lambdaQueryWrapper);
        if(ObjectUtil.isNotEmpty(hallInfoConfigDO)){
            updateObj.setId(hallInfoConfigDO.getId());
        }
        saveOrUpdate(updateObj);
    }

    @Override
    public void deleteHallInfoConfig(String id) {
        // 校验存在
        validateHallInfoConfigExists(id);
        // 删除
        hallInfoConfigDao.deleteById(id);
    }

    private void validateHallInfoConfigExists(String id) {
        if (hallInfoConfigDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-服务大厅信息发布配置数据不存在");
        }
    }

    @Override
    public HallInfoConfigRespVO getHallInfoConfig() {

        LambdaQueryWrapper<HallInfoConfigDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(HallInfoConfigDO::getIsCarousel).eq(HallInfoConfigDO::getOrgCode, SessionUserUtil.getSessionUser().getOrgCode());

        HallInfoConfigDO hallInfoConfigDO = getOne(lambdaQueryWrapper);
        if(ObjectUtil.isEmpty(hallInfoConfigDO)){
            hallInfoConfigDO = new HallInfoConfigDO();
            //没有就默认轮播是打开的
            hallInfoConfigDO.setIsCarousel((short) 1);
        }

        return BeanUtils.toBean(hallInfoConfigDO,HallInfoConfigRespVO.class);
    }

    @Override
    public PageResult<HallInfoConfigDO> getHallInfoConfigPage(HallInfoConfigPageReqVO pageReqVO) {
        return hallInfoConfigDao.selectPage(pageReqVO);
    }

    @Override
    public List<HallInfoConfigDO> getHallInfoConfigList(HallInfoConfigListReqVO listReqVO) {
        return hallInfoConfigDao.selectList(listReqVO);
    }


}
