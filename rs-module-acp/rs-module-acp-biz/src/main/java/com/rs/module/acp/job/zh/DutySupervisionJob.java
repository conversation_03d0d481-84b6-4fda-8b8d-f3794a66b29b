package com.rs.module.acp.job.zh;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.util.DateUtils;
import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.acp.cons.SignInStatusEnum;
import com.rs.module.acp.controller.admin.pm.vo.LeaderInspectionSaveReqVO;
import com.rs.module.acp.controller.admin.zh.vo.*;
import com.rs.module.acp.entity.zh.DutySuperviseRuleDO;
import com.rs.module.acp.service.zh.DutySuperviseRecordService;
import com.rs.module.acp.service.zh.DutySuperviseRecordSigninService;
import com.rs.module.acp.service.zh.DutySuperviseRuleService;
import com.rs.module.acp.util.GeneralUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.DoubleStream;


@Component
public class DutySupervisionJob {

    @Resource
    private DutySuperviseRecordService dutySuperviseRecordService;
    @Resource
    private DutySuperviseRecordSigninService dutySuperviseRecordSigninService;
    @Resource
    private DutySuperviseRuleService dutySuperviseRuleService;

    /**
     * 定时推送待签到消息
     * @return
     */
    @XxlJob("dutySupervision")
    public void dutySupervision() {
        //
        XxlJobHelper.log("-----开始-------");
        try {
            //根据当前时间查询当前值班人员的信息，包括值班日期、值班岗位、值班人员等
            List<AcpZhStaffDutyTemplateCurrentInfo> dutyInfos = dutySuperviseRecordService.getCurrentDutyInfo("000000000000");
            //根据值班模板ID查询值班督导规则信息
            if (dutyInfos != null && !dutyInfos.isEmpty()) {
                //遍历值班人员信息，根据 dutyInfo.getDutyDate() 查询该时间段内所有待签到人员信息
                for (AcpZhStaffDutyTemplateCurrentInfo dutyInfo : dutyInfos) {
                    DutySuperviseRuleDO dutySuperviseRuleDO = getDutySuperviseRule(dutyInfo);
                    createDutySuperviseRecord(dutyInfo, dutySuperviseRuleDO);
                    //发送消息

                }
             }
        } catch (Exception e) {
            e.printStackTrace();
        }
        XxlJobHelper.log("-----结束------");
    }

    /**
     * 创建值班监督记录
     *
     * 此方法旨在根据当前的值班模板信息和值班监督规则，生成相应的值班监督记录
     * 它首先检查值班日期是否存在，如果不存在，则表明没有安排值班，直接返回
     * 如果值班日期存在，它将使用值班信息和规则构建一个新的值班监督记录，并将其保存
     *
     * @param dutyInfo 值班模板当前信息，包含值班相关细节
     * @param dutySuperviseRuleDO 值班监督规则数据对象，定义了监督规则
     */
    private void createDutySuperviseRecord(AcpZhStaffDutyTemplateCurrentInfo dutyInfo, DutySuperviseRuleDO dutySuperviseRuleDO) {
        //判断，如果值班日期为空，表示不存在排版，直接返回
        if (dutyInfo.getDutyDate() == null) {
            return;
        }
        if(dutySuperviseRuleDO==null){
            return;
        }
        String id = GeneralUtil.generateUUID();
        // 构建并创建值班监督记录，设置基本属性，如未签到状态和空的签到时间
        DutySuperviseRecordSaveReqVO dutySuperviseRecordSaveReqVO = new DutySuperviseRecordSaveReqVO();
        dutySuperviseRecordSaveReqVO.setId(id);
        dutySuperviseRecordSaveReqVO.setStaffDutyTemplateId(dutyInfo.getStaffDutyTemplateId());
        dutySuperviseRecordSaveReqVO.setStaffDutyPostId(dutyInfo.getStaffDutyPostId());
        dutySuperviseRecordSaveReqVO.setStaffDutyRoleId(dutyInfo.getStaffDutyRoleId());
        dutySuperviseRecordSaveReqVO.setDutyDate(dutyInfo.getDutyDate());
        dutySuperviseRecordSaveReqVO.setSigninValidMode(dutySuperviseRuleDO.getSigninValidMode());
        dutySuperviseRecordSaveReqVO.setSigninStatus(SignInStatusEnum.NOT_SIGNED.getCode());
        dutySuperviseRecordSaveReqVO.setSigninTime(null);

        dutySuperviseRecordService.createDutySuperviseRecord(dutySuperviseRecordSaveReqVO);
        List<StaffDutyRecordPersonVO> dutyRecordPersons = dutySuperviseRecordService.getDutyRecordPersonsByRecordId(dutyInfo.getRecordId());
        for (StaffDutyRecordPersonVO dutyRecordPerson : dutyRecordPersons) {
            DutySuperviseRecordSigninSaveReqVO dutySuperviseRecordSigninSaveReqVO = new DutySuperviseRecordSigninSaveReqVO();
            dutySuperviseRecordSigninSaveReqVO.setDutySuperviseRecordId(id);
            dutySuperviseRecordSigninSaveReqVO.setPoliceId(dutyRecordPerson.getPoliceId());
            dutySuperviseRecordSigninSaveReqVO.setPoliceName(dutyRecordPerson.getPoliceName());
            dutySuperviseRecordSigninService.createDutySuperviseRecordSignin(dutySuperviseRecordSigninSaveReqVO);

        }
        //发送消息
        sendTodoMsg(id,dutyInfo,dutyRecordPersons,dutySuperviseRuleDO);


    }

    /**
     * 发送待办消息
     * @param id 值班监督记录ID
     * @param dutyInfo 当前值班模板信息
     * @param dutyRecordPersons 值班记录人员列表
     * @param dutySuperviseRuleDO 值班督导规则对象
     */
    private void sendTodoMsg(String id, AcpZhStaffDutyTemplateCurrentInfo dutyInfo, List<StaffDutyRecordPersonVO> dutyRecordPersons, DutySuperviseRuleDO dutySuperviseRuleDO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        if (sessionUser == null) {
            throw new ServerException("用户未登录，无法发送消息！");
        }

        String title = String.format("%s 的值班签到任务已生成，请处理", sessionUser.getName());
        String content = String.format("值班日期：%s\n值班岗位：%s\n督导规则签到模式：%s",
                DateUtil.formatDate(dutyInfo.getDutyDate()),
                Optional.ofNullable(dutyInfo.getPostName()).orElse("未知岗位"),
                Optional.ofNullable(dutySuperviseRuleDO.getSigninValidMode()).orElse("未定义"));

        String simpleUUID = id;
        String url = "/comprehensive/superviseRule?id=" + id+"&mode="+dutySuperviseRuleDO.getSigninValidMode(); // 消息点击后跳转的页面
        String appCode = HttpUtils.getAppCode(); // 获取当前应用编码
        String fUser = sessionUser.getIdCard();   // 来源用户身份证号
        String fUserName = sessionUser.getName(); // 来源用户名
        String fOrgCode = sessionUser.getOrgCode(); // 来源机构代码
        String fOrgName = sessionUser.getOrgName(); // 来源机构名称
        String fXxpt = "pc"; // 来源系统标识
        String ywbh = id; // 业务编号，使用值班督导记录ID
        String msgType = MsgBusTypeEnum.XK_ZBDD.getCode(); // 消息类型，使用管教业务-戒具使用作为示例

        // 接收人列表：根据值班人员构建接收人列表
        List<ReceiveUser> receiveUserList = new ArrayList<>();
        if (dutyRecordPersons != null && !dutyRecordPersons.isEmpty()) {
            for (StaffDutyRecordPersonVO person : dutyRecordPersons) {
                if (person != null && person.getPoliceId() != null) {
                    receiveUserList.add(new ReceiveUser(person.getPoliceId(), fOrgCode));
                }
            }
        } else {
            throw new ServerException("值班记录中无相关人员");
        }

        SendMessageUtil.sendTodoMsg(title, content, url, appCode, fUser, fUserName, fOrgCode, fOrgName,
                null, simpleUUID, fXxpt, ywbh, receiveUserList, msgType, null);
    }


    /**
     * 获取值班监督规则
     * 根据值班模板信息获取对应的监督规则
     *
     * @param dutyInfo 值班模板当前信息，包含获取监督规则所需的信息
     * @return 返回找到的 DutySuperviseRuleDO 对象，如果没有找到则返回 null
     */
    private DutySuperviseRuleDO getDutySuperviseRule(AcpZhStaffDutyTemplateCurrentInfo dutyInfo) {
        // 创建请求对象以查询监督规则列表
        DutySuperviseRuleListReqVO listReqVO = new DutySuperviseRuleListReqVO();
        // 设置查询条件：值班模板 ID
        listReqVO.setStaffDutyTemplateId(dutyInfo.getStaffDutyTemplateId());
        // 调用服务层方法获取监督规则列表
        List<DutySuperviseRuleDO> dutySuperviseRuleDOList = dutySuperviseRuleService.getDutySuperviseRuleList(listReqVO);

        // 检查获取的监督规则列表是否非空
        if (dutySuperviseRuleDOList != null && !dutySuperviseRuleDOList.isEmpty()) {
            // 遍历监督规则列表，返回第一个监督规则对象
            for (DutySuperviseRuleDO dutySuperviseRuleDO : dutySuperviseRuleDOList) {
                return dutySuperviseRuleDO;
            }
        }

        // 如果列表为空，返回 null
        return null;
    }

}
