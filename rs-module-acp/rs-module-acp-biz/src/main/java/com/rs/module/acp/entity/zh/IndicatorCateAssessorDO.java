package com.rs.module.acp.entity.zh;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 综合管理-绩效考核指标分类与考评人关联 DO
 *
 * <AUTHOR>
 */
@TableName("acp_zh_indicator_cate_assessor")
@KeySequence("acp_zh_indicator_cate_assessor_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_indicator_cate_assessor")
public class IndicatorCateAssessorDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 指标分类ID
     */
    private String indicatorCateId;
    /**
     * 考核人身份证号
     */
    private String assessorSfzh;
    /**
     * 考核人姓名
     */
    private String assessorName;

}
