package com.rs.module.acp.service.ds.sjbs;

import javax.validation.*;

import com.rs.module.acp.controller.admin.ds.vo.sjbs.DailyDataSubmitKssSaveReqVO;
import com.rs.module.acp.entity.ds.sjbs.DailyDataSubmitKssDO;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-数据固化-每日数据报送(看守所) Service 接口
 *
 * <AUTHOR>
 */
public interface DailyDataSubmitKssService extends IBaseService<DailyDataSubmitKssDO>{

    /**
     * 创建实战平台-数据固化-每日数据报送(看守所)
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createDailyDataSubmitKss(@Valid DailyDataSubmitKssSaveReqVO createReqVO);

    /**
     * 更新实战平台-数据固化-每日数据报送(看守所)
     *
     * @param updateReqVO 更新信息
     */
    void updateDailyDataSubmitKss(@Valid DailyDataSubmitKssSaveReqVO updateReqVO);

    /**
     * 删除实战平台-数据固化-每日数据报送(看守所)
     *
     * @param id 编号
     */
    void deleteDailyDataSubmitKss(String id);

    /**
     * 获得实战平台-数据固化-每日数据报送(看守所)
     *
     * @param id 编号
     * @return 实战平台-数据固化-每日数据报送(看守所)
     */
    DailyDataSubmitKssDO getDailyDataSubmitKss(String id);

    void saveForStatistic(String orgCode, String startDate, String endDate);

    DailyDataSubmitKssDO getDailyDataSubmitKssBySolidificationDate(String solidificationDate, String orgCode);
}
