package com.rs.module.acp.entity.pi;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-巡视管控-所情登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_prison_event")
@KeySequence("acp_pi_prison_event_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_prison_event")
public class PrisonEventDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 所属预警
     */
    private String warningId;
    /**
     * 警情地点
     */
    private String areaId;
    /**
     * 警情地点名称
     */
    private String areaName;
    /**
     * 发生时间
     */
    private Date happenTime;
    /**
     * 警情事件
     */
    private String eventType;
    /**
     * 警情级别
     */
    private String eventLevel;
    /**
     * 设备类型ID
     */
    private String deviceTypeId;
    /**
     * 设备类型名称
     */
    private String deviceTypeName;
    /**
     * 设备ID
     */
    private String deviceId;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 状态（3待核实、0待处置、5待办结（所领导审批）、1已办结）
     */
    private String status;
    /**
     * 警情描述
     */
    private String eventDetails;
    /**
     * 工作人员
     */
    private String policeSfzh;
    /**
     * 外来人员
     */
    private String outsider;
    /**
     * 警情编码
     */
    private String eventCode;
    /**
     * 外来人员姓名
     */
    private String outsiderName;
    /**
     * 事件来源
     */
    private String eventSrc;
    /**
     * 处置情况
     */
    private String handleInfo;
    /**
     * 警情事件id
     */
    private String eventTypeId;
    /**
     * 事件开始时间（精确时间-开始）
     */
    private Date eventStartTime;
    /**
     * 事件结束时间（精确时间-结束）
     */
    private Date eventEndTime;
    /**
     * 巡控登记人身份证号
     */
    private String handleUserSfzh;
    /**
     * 巡控登记人
     */
    private String handleUserName;
    /**
     * 巡控登记时间
     */
    private Date handleTime;
    /**
     * 所领导审批人身份证号
     */
    private String auditUserSfzh;
    /**
     * 所领导审批人
     */
    private String auditUserName;
    /**
     * 所领导审批时间
     */
    private Date auditTime;
    /**
     * 所领导审批意见
     */
    private String auditOpinion;
    /**
     * 报警人员身份证号
     */
    private String reportPrisonerSfzhs;
    /**
     * 关联报警录像文件
     */
    private String eventVideo;
    /**
     * 所情事件类型大类ID
     */
    private String eventRootTypeId;
    /**
     * 所情事件类型大类名称
     */
    private String eventRootTypeName;
    /**
     * 推送信息
     */
    private String disposePostInfo;
    /**
     * 附件地址
     */
    private String attUrl;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 截图照片地址
     */
    private String screenshotUrl;
    /**
     * 报警人员姓名
     */
    private String reportPrisonerName;
}
