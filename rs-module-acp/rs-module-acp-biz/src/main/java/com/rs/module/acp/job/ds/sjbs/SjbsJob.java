package com.rs.module.acp.job.ds.sjbs;

import com.rs.module.acp.service.ds.sjbs.*;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
/**
 * @description：数据报送定时任务
 */
@Component
public class SjbsJob {

    @Autowired
    private DailyDataSubmitJlsService dailyDataSubmitJlsService;
    @Autowired
    private DailyDataSubmitKssService dailyDataSubmitKssService;
    @Autowired
    private WeeklyDataSubmitJlsService weeklyDataSubmitJlsService;
    @Autowired
    private WeeklyDataSubmitKssService weeklyDataSubmitKssService;

    /**
     * 每日数据报送
     */
    @XxlJob("sjbsDailyDataSubmitInit")
    public void sjbsDailyDataSubmitInit() {
        // 待解除
        XxlJobHelper.log("----每日数据报送(拘留所) 开始-------");
        try {
            dailyDataSubmitJlsService.saveForStatistic(null,null,null);
            dailyDataSubmitKssService.saveForStatistic(null,null,null);
        } catch (Exception e) {
            XxlJobHelper.log("每日数据报送(拘留所)-状态任务异常：{}", e.getMessage());
        }
        XxlJobHelper.log("----每日数据报送(拘留所) 结束-------");
    }
    /**
     * 每周数据报送
     */
    @XxlJob("sjbsWeeklyDataSubmitInit")
    public void sjbsWeeklyDataSubmitInit() {
        // 待解除
        XxlJobHelper.log("----每周数据报送 开始-------");
        try {
            weeklyDataSubmitJlsService.saveForStatistic(null,null,null);
            weeklyDataSubmitKssService.saveForStatistic(null,null,null);
        } catch (Exception e) {
            XxlJobHelper.log("每周数据报送-状态任务异常：{}", e.getMessage());
        }
        XxlJobHelper.log("----每周数据报送结束-------");
    }
}
