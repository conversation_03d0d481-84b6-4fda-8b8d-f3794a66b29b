package com.rs.module.acp.controller.admin.db.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "看守所收押登记基本信息VO")
@Data
public class DetainRegKssBasicVO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("姓名")
    private String xm;

    @ApiModelProperty("曾用名/别名/绰号")
    private String bm;

    @ApiModelProperty("性别")
    @Trans(type = TransType.DICTIONARY,key = "ZD_XB")
    private String xb;

    @ApiModelProperty("出生日期")
    private Date csrq;

    @ApiModelProperty("证件类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_ZJLX")
    private String zjlx;

    @ApiModelProperty("证件号码")
    private String zjhm;

    @ApiModelProperty("国籍")
    @Trans(type = TransType.DICTIONARY,key = "ZD_GABBZ_GJ")
    private String gj;

    @ApiModelProperty("民族")
    @Trans(type = TransType.DICTIONARY,key = "ZD_MZ")
    private String mz;

    @ApiModelProperty("婚姻状况")
    @Trans(type = TransType.DICTIONARY,key = "ZD_HYZK")
    private String hyzk;

    @ApiModelProperty("籍贯")
    @Trans(type = TransType.DICTIONARY,key = "ZD_GABBZ_JG")
    private String jg;

    @ApiModelProperty("宗教信仰")
    @Trans(type = TransType.DICTIONARY,key = "ZD_ZJXY")
    private String zjxy;

    @ApiModelProperty("户籍所在地")
    @Trans(type = TransType.DICTIONARY,key = "ZD_GABBZ_JG")
    private String hjd;

    @ApiModelProperty("户籍地所在地详址")
    private String hjdxz;

    @ApiModelProperty("现居住址")
    @Trans(type = TransType.DICTIONARY,key = "ZD_GABBZ_JG")
    private String xzz;

    @ApiModelProperty("现居住址详址")
    private String xzzxz;

    @ApiModelProperty("文化程度")
    @Trans(type = TransType.DICTIONARY,key = "ZD_WHCD")
    private String whcd;

    @ApiModelProperty("政治面貌")
    @Trans(type = TransType.DICTIONARY,key = "ZD_ZZMM")
    private String zzmm;

    @ApiModelProperty("职业")
    @Trans(type = TransType.DICTIONARY,key = "ZD_ZY")
    private String zy;

    @ApiModelProperty("工作单位")
    private String gzdw;

    @ApiModelProperty("职务")
    private String zw;

    @ApiModelProperty("特长(专长)")
    @Trans(type = TransType.DICTIONARY,key = "ZD_ZC")
    private String tc;

    @ApiModelProperty("身份")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SF")
    private String sf;

    @ApiModelProperty("特殊身份")
    @Trans(type = TransType.DICTIONARY,key = "ZD_TSSF")
    private String tssf;

    @ApiModelProperty("信息核查")
    @Trans(type = TransType.DICTIONARY,key = "ZD_XXHC")
    private String xxhc;

    @ApiModelProperty("是否为在校学生")
    @Trans(type = TransType.DICTIONARY,key = "ZD_TYSFDM")
    private String sfwxzxs;

    @ApiModelProperty("学校名称")
    @Trans(type = TransType.DICTIONARY,key = "ZD_XXMC")
    private String xxmc;

    @ApiModelProperty("人员管理类别")
    @Trans(type = TransType.DICTIONARY,key = "ZD_KSS_RYGLLB")
    private String gllb;
}
