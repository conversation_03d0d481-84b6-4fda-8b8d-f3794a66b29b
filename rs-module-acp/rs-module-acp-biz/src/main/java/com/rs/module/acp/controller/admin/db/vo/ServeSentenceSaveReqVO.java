package com.rs.module.acp.controller.admin.db.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-留所服刑新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ServeSentenceSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("留所原因")
    @NotEmpty(message = "留所原因不能为空")
    private String detainReason;

    @ApiModelProperty("留所原因详情")
    private String detainReasonDetails;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("申请时间")
    private Date operateTime;

    @ApiModelProperty("经办民警身份证号")
    private String operatePoliceSfzh;

    @ApiModelProperty("申请人")
    private String operatePolice;

    @ApiModelProperty("状态")
    private String status;

}
