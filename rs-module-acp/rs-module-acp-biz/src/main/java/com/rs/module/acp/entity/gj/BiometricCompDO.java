package com.rs.module.acp.entity.gj;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-生物特征比对 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_biometric_comp")
@KeySequence("acp_gj_biometric_comp_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_biometric_comp")
public class BiometricCompDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 提交生物特征类型
     */
    private String biometricType;
    /**
     * 提交时间
     */
    private Date submitTime;
    /**
     * 提交办案单位类型
     */
    private String caseUnitType;
    /**
     * 提交办案单位名称
     */
    private String caseUnitName;
    /**
     * 提交方式
     */
    private String submitMethod;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否比中
     */
    private String isComp;
    /**
     * 比中案件编号
     */
    private String compCaseNo;
    /**
     * 比中案件类型
     */
    private String compCaseType;
    /**
     * 比中处置情况
     */
    private String disposalSituation;
    /**
     * 比中登记人身份证号
     */
    private String compOperatorSfzh;
    /**
     * 比中登记人姓名
     */
    private String compOperatorName;
    /**
     * comp_operator_time
     */
    private Date compOperatorTime;
    /**
     * 状态
     */
    private String status;

}
