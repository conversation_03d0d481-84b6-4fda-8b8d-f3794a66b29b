package com.rs.module.acp.entity.wb;

import lombok.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-服务大厅信息发布 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_hall_info")
@KeySequence("acp_gj_hall_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_hall_info")
public class HallInfoDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 信息标题
     */
    private String title;
    /**
     * 信息内容
     */
    private String infoContent;
    /**
     * 附件文件类型
     */
    private String attachmentType;
    /**
     * 附件上传地址
     */
    private String attachmentUrl;
    /**
     * 状态
     */
    private String status;
    /**
     * 排序
     */
    private Short sort;
    /**
     * 附件名称
     */
    private String attachmentName;

}
