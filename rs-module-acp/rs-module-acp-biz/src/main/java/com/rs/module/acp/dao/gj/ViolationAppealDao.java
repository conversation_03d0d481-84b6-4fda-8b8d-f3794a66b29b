package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.violationappeal.ViolationAppealListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.violationappeal.ViolationAppealPageReqVO;
import com.rs.module.acp.entity.gj.ViolationAppealDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 实战平台-管教业务-违规申诉 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ViolationAppealDao extends IBaseDao<ViolationAppealDO> {


    default PageResult<ViolationAppealDO> selectPage(ViolationAppealPageReqVO reqVO) {
        Page<ViolationAppealDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ViolationAppealDO> wrapper = new LambdaQueryWrapperX<ViolationAppealDO>()
            .eqIfPresent(ViolationAppealDO::getViolationRecordId, reqVO.getViolationRecordId())
            .eqIfPresent(ViolationAppealDO::getAppealReason, reqVO.getAppealReason())
            .eqIfPresent(ViolationAppealDO::getAppealRequirement, reqVO.getAppealRequirement())
            .eqIfPresent(ViolationAppealDO::getStatus, reqVO.getStatus())
            .eqIfPresent(ViolationAppealDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(ViolationAppealDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(ViolationAppealDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(ViolationAppealDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(ViolationAppealDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(ViolationAppealDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(ViolationAppealDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(ViolationAppealDO::getAddTime);
        }
        Page<ViolationAppealDO> violationAppealPage = selectPage(page, wrapper);
        return new PageResult<>(violationAppealPage.getRecords(), violationAppealPage.getTotal());
    }
    default List<ViolationAppealDO> selectList(ViolationAppealListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ViolationAppealDO>()
            .eqIfPresent(ViolationAppealDO::getViolationRecordId, reqVO.getViolationRecordId())
            .eqIfPresent(ViolationAppealDO::getAppealReason, reqVO.getAppealReason())
            .eqIfPresent(ViolationAppealDO::getAppealRequirement, reqVO.getAppealRequirement())
            .eqIfPresent(ViolationAppealDO::getStatus, reqVO.getStatus())
            .eqIfPresent(ViolationAppealDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(ViolationAppealDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(ViolationAppealDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(ViolationAppealDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(ViolationAppealDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(ViolationAppealDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(ViolationAppealDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(ViolationAppealDO::getAddTime));    }


    ViolationAppealDO getOneById(@Param("id") String id);

    List<ViolationAppealDO> getListByIds(@Param("list") List<String> list);

    Page<ViolationAppealDO> applyList(Page<ViolationAppealDO> page, @Param("jgrybm") String jgrybm, @Param("type") String type);

    Page<ViolationAppealDO> applyRecord(Page<ViolationAppealDO> page, @Param("jgrybm") String jgrybm,
                                        @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
