package com.rs.module.acp.cons;

/**
 * 第三方生物信息采集类型
 */
public enum ThirdBiometricTypeEnum {

    // 海鑫
    FINGER("finger", "01", "指纹采集"),
    IRIS("iris", "02", "虹膜采集"),
    PHOTO("photo", "03", "人像采集");

    private final String thirdCode;
    private final String code;
    private final String value;

    ThirdBiometricTypeEnum(String thirdCode, String code, String value) {
        this.thirdCode = thirdCode;
        this.code = code;
        this.value = value;
    }

    public String getThirdCode() {
        return thirdCode;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    /**
     * 根据code获取枚举值
     *
     * @param thirdCode 第三方类型编码
     * @return 对应的枚举值
     */
    public static ThirdBiometricTypeEnum getByThirdCode(String thirdCode) {
        for (ThirdBiometricTypeEnum status : values()) {
            if (status.getThirdCode().equals(thirdCode)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 类型编码
     * @return 对应的枚举值
     */
    public static ThirdBiometricTypeEnum getByCode(String code) {
        for (ThirdBiometricTypeEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

}
