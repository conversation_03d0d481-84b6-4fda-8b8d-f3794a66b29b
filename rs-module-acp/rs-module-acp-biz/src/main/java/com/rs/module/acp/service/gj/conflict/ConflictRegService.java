package com.rs.module.acp.service.gj.conflict;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictFollowupSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictMediateHistoryRespVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictMediationSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictRegApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictRegRespVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictRegSaveReqVO;
import com.rs.module.acp.controller.app.gj.vo.conflict.AppConflictConfirmReqVO;
import com.rs.module.acp.controller.app.gj.vo.conflict.AppConflictSaveReqVO;
import com.rs.module.acp.entity.gj.conflict.ConflictRegDO;

import java.util.List;

/**
 * 实战平台-管教业务-社会矛盾化解登记 Service 接口
 *
 * <AUTHOR>
 */
public interface ConflictRegService extends IBaseService<ConflictRegDO> {

    /**
     * 事件编号
     *
     * @return
     */
    String getTJNumber();

    /**
     * 根据事件编号获取社会矛盾化解登记信息
     * @param eventCode
     * @return
     */
    ConflictRegDO getByEventCode(String eventCode);

    /**
     * 民警登记
     *
     * @param createReqVO
     * @return
     */
    String createConflict(ConflictRegSaveReqVO createReqVO);

    /**
     * 根据事件编号获取社会矛盾化解登记信息
     *
     * @param eventCode
     * @return
     */
    ConflictRegRespVO getConflictRegByEventCodeVO(String eventCode);

    /**
     * 编辑社会矛盾化解登记信息
     *
     * @param createReqVO
     * @return
     */
    String updateConflict(ConflictRegSaveReqVO createReqVO);

    /**
     * 更新流程状态
     *
     * @param eventCode
     * @param regStatus
     * @param actInstId
     * @return
     */
    Boolean updateProcessStatus(String eventCode, String regStatus, String actInstId);

    /**
     * 领导审批
     *
     * @param approveReqVO
     */
    void leaderApprove(ConflictRegApproveReqVO approveReqVO);

    /**
     * 调解信息保存
     * @param mediateInfoReqVO
     */
    void addMediateInfo(ConflictMediationSaveReqVO mediateInfoReqVO);

    /**
     * 获得调解历史
     *
     * @param eventCode
     * @param pageSize
     * @return
     */
    List<ConflictMediateHistoryRespVO> getMediateHistory(String eventCode, int pageSize);

    /**
     * 内屏申请
     *
     * @return
     */
    void applyConflict(AppConflictSaveReqVO reqVO);

    /**
     * 内屏确认
     * @param queryVO
     */
    void confirm(AppConflictConfirmReqVO queryVO);

    /**
     * 管教民警确认
     * @param id
     */
    void policeConfirm(String id);

    /**
     * 退回重签
     * @param id
     */
    void returnAndResign(String id);

    /**
     * 创建回访
     * @param createReqVO
     * @return
     */
    String createFollowUp(ConflictFollowupSaveReqVO createReqVO);

    /**
     * 删除社会矛盾化解登记信息
     *
     * @param ids
     * @return
     */
    void deleteConflict(String ids);


    void back(String pcId, String eventCode, String jgrybm);
}
