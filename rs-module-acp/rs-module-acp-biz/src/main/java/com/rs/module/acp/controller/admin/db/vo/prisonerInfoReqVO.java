package com.rs.module.acp.controller.admin.db.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class prisonerInfoReqVO {
    //jgrybm
    @ApiModelProperty("机构人员编号")
    @NotEmpty(message = "机构人员编号不能为空")
    private String jgrybm;
    //org_code
    @ApiModelProperty("机构人员编号")
//    @NotEmpty(message = "机构人员编号不能为空")
    private String orgCode;
    //businessType
    @ApiModelProperty("监所类型：看守所:kss;拘留所：jls;戒毒所:jds")
    @NotEmpty(message = "业务类型不能为空")
    private String businessType;
}
