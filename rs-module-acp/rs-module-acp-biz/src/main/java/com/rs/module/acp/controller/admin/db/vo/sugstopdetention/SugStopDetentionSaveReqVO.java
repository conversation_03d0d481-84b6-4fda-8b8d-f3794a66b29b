package com.rs.module.acp.controller.admin.db.vo.sugstopdetention;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-建议停拘登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SugStopDetentionSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("建停类型（01：收拘时，02：收拘后）")
    @NotEmpty(message = "建停类型（01：收拘时，02：收拘后）不能为空")
    private String jtlx;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("姓名")
    @NotEmpty(message = "姓名不能为空")
    private String xm;

    @ApiModelProperty("曾用名")
    private String cym;

    @ApiModelProperty("证件类型")
    @NotEmpty(message = "证件类型不能为空")
    private String zjlx;

    @ApiModelProperty("证件号码")
    @NotEmpty(message = "证件号码不能为空")
    private String zjhm;

    @ApiModelProperty("性别")
    @NotEmpty(message = "性别不能为空")
    private String xb;

    @ApiModelProperty("出生日期")
    @NotNull(message = "出生日期不能为空")
    private Date csrq;

    @ApiModelProperty("国籍")
    @NotEmpty(message = "国籍不能为空")
    private String gj;

    @ApiModelProperty("特殊身份")
    private String tssf;

    @ApiModelProperty("民族")
    @NotEmpty(message = "民族不能为空")
    private String mz;

    @ApiModelProperty("政治面貌")
    @NotEmpty(message = "政治面貌不能为空")
    private String zzmm;

    @ApiModelProperty("婚姻状况")
    @NotEmpty(message = "婚姻状况不能为空")
    private String hyzk;

    @ApiModelProperty("文化程度")
    @NotEmpty(message = "文化程度不能为空")
    private String whcd;

    @ApiModelProperty("籍贯")
    @NotEmpty(message = "籍贯不能为空")
    private String jg;

    @ApiModelProperty("身份")
    @NotEmpty(message = "身份不能为空")
    private String sf;

    @ApiModelProperty("工作单位")
    private String gzdw;

    @ApiModelProperty("职业")
    private String zy;

    @ApiModelProperty("户籍地")
    private String hjd;

    @ApiModelProperty("户籍地详址")
    @NotEmpty(message = "户籍地详址不能为空")
    private String hjdxz;

    @ApiModelProperty("现住址")
    @NotEmpty(message = "现住址不能为空")
    private String xzz;

    @ApiModelProperty("现住址详址")
    private String xzzxz;

    @ApiModelProperty("决定拘留机关")
    @NotEmpty(message = "决定拘留机关不能为空")
    private String jdjljg;

    @ApiModelProperty("办案单位类型")
    @NotEmpty(message = "办案单位类型不能为空")
    private String badwlx;

    @ApiModelProperty("办案单位")
    @NotEmpty(message = "办案单位不能为空")
    private String badw;

    @ApiModelProperty("送拘单位")
    @NotEmpty(message = "送拘单位不能为空")
    private String sjdw;

    @ApiModelProperty("送拘人")
    @NotEmpty(message = "送拘人不能为空")
    private String sjr;

    @ApiModelProperty("送拘凭证")
    @NotEmpty(message = "送拘凭证不能为空")
    private String sjpz;

    @ApiModelProperty("送拘法律文书号")
    @NotEmpty(message = "送拘法律文书号不能为空")
    private String sjflwsh;

    @ApiModelProperty("联系电话")
    @NotEmpty(message = "联系电话不能为空")
    private String lxdh;

    @ApiModelProperty("案件类别代码")
    @NotEmpty(message = "案件类别代码不能为空")
    private String ajlbdm;

    @ApiModelProperty("案件类别")
    @NotEmpty(message = "案件类别不能为空")
    private String ajlb;

    @ApiModelProperty("送拘民警")
    @NotEmpty(message = "送拘民警不能为空")
    private String sjmj;

    @ApiModelProperty("收拘类型")
    @NotEmpty(message = "收拘类型不能为空")
    private String sjlx;

    @ApiModelProperty("拘留时长")
    @NotNull(message = "拘留时长不能为空")
    private Integer jlsc;

    @ApiModelProperty("拘留起始日期")
    @NotNull(message = "拘留起始日期不能为空")
    private Date jlqsrq;

    @ApiModelProperty("拘留截止日期")
    @NotNull(message = "拘留截止日期不能为空")
    private Date jljzrq;

    @ApiModelProperty("简要案情")
    @NotEmpty(message = "简要案情不能为空")
    private String jyaq;

    @ApiModelProperty("备注")
    private String bz;

    @ApiModelProperty("身高")
    @NotNull(message = "身高不能为空")
    private Integer sg;

    @ApiModelProperty("体重")
    @NotNull(message = "体重不能为空")
    private Integer tz;

    @ApiModelProperty("足长")
    @NotNull(message = "足长不能为空")
    private Integer zc;

    @ApiModelProperty("体温")
    @NotNull(message = "体温不能为空")
    private Integer tw;

    @ApiModelProperty("血压")
    @NotNull(message = "血压不能为空")
    private Integer xy;

    @ApiModelProperty("脉搏")
    private Integer mb;

    @ApiModelProperty("心肺")
    @NotEmpty(message = "心肺不能为空")
    private String xf;

    @ApiModelProperty("心率")
    @NotNull(message = "心率不能为空")
    private Integer xl;

    @ApiModelProperty("口音")
    private String ky;

    @ApiModelProperty("血型")
    private String xx;

    @ApiModelProperty("体型")
    private String tx;

    @ApiModelProperty("脸型")
    private String lx;

    @ApiModelProperty("语言表达能力")
    @NotEmpty(message = "语言表达能力不能为空")
    private String yybdnl;

    @ApiModelProperty("行走状况")
    @NotEmpty(message = "行走状况不能为空")
    private String xzzk;

    @ApiModelProperty("健康状况")
    @NotEmpty(message = "健康状况不能为空")
    private String jkzk;

    @ApiModelProperty("聋哑人")
    @NotEmpty(message = "聋哑人不能为空")
    private String lyr;

    @ApiModelProperty("不能自理")
    @NotEmpty(message = "不能自理不能为空")
    private String bnzl;

    @ApiModelProperty("过敏史")
    private String gms;

    @ApiModelProperty("自述症状")
    @NotEmpty(message = "自述症状不能为空")
    private String zszzz;

    @ApiModelProperty("肺脏")
    @NotEmpty(message = "肺脏不能为空")
    private String fz;

    @ApiModelProperty("腹部")
    @NotEmpty(message = "腹部不能为空")
    private String fb;

    @ApiModelProperty("体表特殊标志")
    @NotEmpty(message = "体表特殊标志不能为空")
    private String tbtbsbz;

    @ApiModelProperty("既往病史")
    @NotEmpty(message = "既往病史不能为空")
    private String jwbs;

    @ApiModelProperty("有何传染病")
    private String yhcrb;

    @ApiModelProperty("本人病史")
    private String brbs;

    @ApiModelProperty("家庭病史")
    @NotEmpty(message = "家庭病史不能为空")
    private String jtbs;

    @ApiModelProperty("血常规")
    @NotEmpty(message = "血常规不能为空")
    private String xcg;

    @ApiModelProperty("心电图")
    @NotEmpty(message = "心电图不能为空")
    private String xdt;

    @ApiModelProperty("B超")
    @NotEmpty(message = "B超不能为空")
    private String bc;

    @ApiModelProperty("胸片")
    @NotEmpty(message = "胸片不能为空")
    private String xp;

    @ApiModelProperty("建议停止执行拘留原因")
    @NotEmpty(message = "建议停止执行拘留原因不能为空")
    private String jytzzxjjlyy;

    @ApiModelProperty("医生意见")
    @NotEmpty(message = "医生意见不能为空")
    private String ysyj;

    @ApiModelProperty("诊断证明")
    private String zdzm;

    @ApiModelProperty("住院治疗证明文件")
    private String zyzlzmwj;

    @ApiModelProperty("鉴定结论")
    private String jdlj;

    @ApiModelProperty("其他")
    private String qt;

    @ApiModelProperty("经办医生身份证号")
    private String jbyssfzh;

    @ApiModelProperty("经办医生")
    private String jbys;

    @ApiModelProperty("经办时间")
    private Date jbsj;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

}
