package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.VisitorInfoDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.VisitorInfoDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-来访人员信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class VisitorInfoServiceImpl extends BaseServiceImpl<VisitorInfoDao, VisitorInfoDO> implements VisitorInfoService {

    @Resource
    private VisitorInfoDao visitorInfoDao;

    @Override
    public String createVisitorInfo(VisitorInfoSaveReqVO createReqVO) {
        // 插入
        VisitorInfoDO visitorInfo = BeanUtils.toBean(createReqVO, VisitorInfoDO.class);
        visitorInfoDao.insert(visitorInfo);
        // 返回
        return visitorInfo.getId();
    }

    @Override
    public void updateVisitorInfo(VisitorInfoSaveReqVO updateReqVO) {
        // 校验存在
        validateVisitorInfoExists(updateReqVO.getId());
        // 更新
        VisitorInfoDO updateObj = BeanUtils.toBean(updateReqVO, VisitorInfoDO.class);
        visitorInfoDao.updateById(updateObj);
    }

    @Override
    public void deleteVisitorInfo(String id) {
        // 校验存在
        validateVisitorInfoExists(id);
        // 删除
        visitorInfoDao.deleteById(id);
    }

    private void validateVisitorInfoExists(String id) {
        if (visitorInfoDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-来访人员信息数据不存在");
        }
    }

    @Override
    public VisitorInfoDO getVisitorInfo(String id) {
        return visitorInfoDao.selectById(id);
    }

    @Override
    public PageResult<VisitorInfoDO> getVisitorInfoPage(VisitorInfoPageReqVO pageReqVO) {
        return visitorInfoDao.selectPage(pageReqVO);
    }

    @Override
    public List<VisitorInfoDO> getVisitorInfoList(VisitorInfoListReqVO listReqVO) {
        return visitorInfoDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean savetVisitorInfoList(List<VisitorInfoSaveReqVO> saveReqVOList, String visitorRegId) {

        if (CollectionUtil.isEmpty(saveReqVOList)) {
            return true;
        }

        List<VisitorInfoDO> dbList = new ArrayList<>();

        if (ObjectUtil.isNotEmpty(visitorRegId)) {
            //查询旧的数据
            LambdaQueryWrapper<VisitorInfoDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.select(VisitorInfoDO::getId).eq(VisitorInfoDO::getVisitorRegId, visitorRegId);
            dbList = list(lambdaQueryWrapper);
            if (CollectionUtil.isNotEmpty(dbList)) {

                Map<String, String> saveIdMap = new HashMap<>();
                for(VisitorInfoSaveReqVO saveReqVO:saveReqVOList){
                    saveIdMap.put(saveReqVO.getId(),saveReqVO.getId());
                }

                List<String> idDelList = new ArrayList<>();
                for (VisitorInfoDO infoDO : dbList) {
                    if(!saveIdMap.containsKey(infoDO.getId())){
                        idDelList.add(infoDO.getId());
                    }
                }
                if(CollectionUtil.isNotEmpty(idDelList)){
                    visitorInfoDao.deleteBatchIds(idDelList);
                }
            }
        }

        List<VisitorInfoDO> infoDOList = BeanUtils.toBean(saveReqVOList, VisitorInfoDO.class);
        infoDOList.forEach(x ->{
            if (ObjectUtil.isEmpty(x.getId())) {
                x.setId(StringUtil.getGuid32());
            }
            x.setVisitorRegId(visitorRegId);
        });
        return saveOrUpdateBatch(infoDOList);
    }

    @Override
    public List<VisitorInfoRespVO> getVisitorInfoListByVisitorRegId(String visitorRegId) {

        LambdaQueryWrapper<VisitorInfoDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(VisitorInfoDO::getId,VisitorInfoDO::getName,VisitorInfoDO::getIdType,
                VisitorInfoDO::getIdNumber,VisitorInfoDO::getVisitorRegId);
        lambdaQueryWrapper.eq(VisitorInfoDO::getVisitorRegId,visitorRegId);
        List<VisitorInfoDO> visitorInfoDOList = list(lambdaQueryWrapper);
        if(CollectionUtil.isEmpty(visitorInfoDOList)){
            return null;
        }

        return BeanUtils.toBean(visitorInfoDOList,VisitorInfoRespVO.class);
    }
}
