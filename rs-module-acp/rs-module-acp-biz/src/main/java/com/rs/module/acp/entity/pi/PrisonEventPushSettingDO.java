package com.rs.module.acp.entity.pi;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-巡视管控-所情事件推送设置 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_prison_event_push_setting")
@KeySequence("acp_pi_prison_event_push_setting_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_prison_event_push_setting")
public class PrisonEventPushSettingDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 处置业务（支持多个用逗号分隔）
     */
    private String disposeBusiness;
    /**
     * 处置预案
     */
    private String disposePlans;
    /**
     * 所情事件类型ID
     */
    private String typeId;
    /**
     * 排序号
     */
    private Integer orderId;
    /**
     * 推送岗位ID
     */
    private String pushPostId;

}
