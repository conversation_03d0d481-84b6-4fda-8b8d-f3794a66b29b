package com.rs.module.acp.dao.pi;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.pi.ProtRestraintDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-巡视管控-保护性约束 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ProtRestraintDao extends IBaseDao<ProtRestraintDO> {


    default PageResult<ProtRestraintDO> selectPage(ProtRestraintPageReqVO reqVO) {
        Page<ProtRestraintDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ProtRestraintDO> wrapper = new LambdaQueryWrapperX<ProtRestraintDO>()
            .eqIfPresent(ProtRestraintDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(ProtRestraintDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(ProtRestraintDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(ProtRestraintDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(ProtRestraintDO::getApplicantSfzh, reqVO.getApplicantSfzh())
            .eqIfPresent(ProtRestraintDO::getApplicant, reqVO.getApplicant())
            .eqIfPresent(ProtRestraintDO::getReason, reqVO.getReason())
            .eqIfPresent(ProtRestraintDO::getRestraintType, reqVO.getRestraintType())
            .eqIfPresent(ProtRestraintDO::getExecutorSfzh, reqVO.getExecutorSfzh())
            .eqIfPresent(ProtRestraintDO::getExecutor, reqVO.getExecutor())
            .betweenIfPresent(ProtRestraintDO::getStartTime, reqVO.getStartTime())
            .betweenIfPresent(ProtRestraintDO::getEndTime, reqVO.getEndTime())
            .eqIfPresent(ProtRestraintDO::getStatus, reqVO.getStatus())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(ProtRestraintDO::getAddTime);
        }
        Page<ProtRestraintDO> protRestraintPage = selectPage(page, wrapper);
        return new PageResult<>(protRestraintPage.getRecords(), protRestraintPage.getTotal());
    }
    default List<ProtRestraintDO> selectList(ProtRestraintListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ProtRestraintDO>()
            .eqIfPresent(ProtRestraintDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(ProtRestraintDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(ProtRestraintDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(ProtRestraintDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(ProtRestraintDO::getApplicantSfzh, reqVO.getApplicantSfzh())
            .eqIfPresent(ProtRestraintDO::getApplicant, reqVO.getApplicant())
            .eqIfPresent(ProtRestraintDO::getReason, reqVO.getReason())
            .eqIfPresent(ProtRestraintDO::getRestraintType, reqVO.getRestraintType())
            .eqIfPresent(ProtRestraintDO::getExecutorSfzh, reqVO.getExecutorSfzh())
            .eqIfPresent(ProtRestraintDO::getExecutor, reqVO.getExecutor())
            .betweenIfPresent(ProtRestraintDO::getStartTime, reqVO.getStartTime())
            .betweenIfPresent(ProtRestraintDO::getEndTime, reqVO.getEndTime())
            .eqIfPresent(ProtRestraintDO::getStatus, reqVO.getStatus())
        .orderByDesc(ProtRestraintDO::getAddTime));    }


    }
