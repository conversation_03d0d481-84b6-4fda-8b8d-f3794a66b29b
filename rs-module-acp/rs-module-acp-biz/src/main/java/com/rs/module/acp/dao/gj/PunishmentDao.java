package com.rs.module.acp.dao.gj;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentPageReqVO;
import com.rs.module.acp.entity.gj.PunishmentDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* 实战平台-管教业务-处罚呈批 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PunishmentDao extends IBaseDao<PunishmentDO> {


    default PageResult<PunishmentDO> selectPage(PunishmentPageReqVO reqVO) {
        Page<PunishmentDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PunishmentDO> wrapper = new LambdaQueryWrapperX<PunishmentDO>()
            .eqIfPresent(PunishmentDO::getDataSources, reqVO.getDataSources())
            .eqIfPresent(PunishmentDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PunishmentDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(PunishmentDO::getReason, reqVO.getReason())
            .betweenIfPresent(PunishmentDO::getStartDate, reqVO.getStartDate())
            .betweenIfPresent(PunishmentDO::getEndDate, reqVO.getEndDate())
            .betweenIfPresent(PunishmentDO::getActualStartDate, reqVO.getActualStartDate())
            .betweenIfPresent(PunishmentDO::getActualEndDate, reqVO.getActualEndDate())
            .eqIfPresent(PunishmentDO::getDuration, reqVO.getDuration())
            .eqIfPresent(PunishmentDO::getActualDuration, reqVO.getActualDuration())
            .eqIfPresent(PunishmentDO::getMeasures, reqVO.getMeasures())
            .eqIfPresent(PunishmentDO::getSpecificReason, reqVO.getSpecificReason())
            .eqIfPresent(PunishmentDO::getRemark, reqVO.getRemark())
            .eqIfPresent(PunishmentDO::getExecutorSfzh, reqVO.getExecutorSfzh())
            .eqIfPresent(PunishmentDO::getExecutor, reqVO.getExecutor())
            .betweenIfPresent(PunishmentDO::getExecutorRegTime, reqVO.getExecutorRegTime())
            .eqIfPresent(PunishmentDO::getExecuteSituation, reqVO.getExecuteSituation())
            .eqIfPresent(PunishmentDO::getIsInAdvanceRemove, reqVO.getIsInAdvanceRemove())
            .eqIfPresent(PunishmentDO::getStatus, reqVO.getStatus())
            .eqIfPresent(PunishmentDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(PunishmentDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(PunishmentDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(PunishmentDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(PunishmentDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(PunishmentDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(PunishmentDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(PunishmentDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(PunishmentDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PunishmentDO::getAddTime);
        }
        Page<PunishmentDO> punishmentPage = selectPage(page, wrapper);
        return new PageResult<>(punishmentPage.getRecords(), punishmentPage.getTotal());
    }
    default List<PunishmentDO> selectList(PunishmentListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PunishmentDO>()
            .eqIfPresent(PunishmentDO::getDataSources, reqVO.getDataSources())
            .eqIfPresent(PunishmentDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PunishmentDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(PunishmentDO::getReason, reqVO.getReason())
            .betweenIfPresent(PunishmentDO::getStartDate, reqVO.getStartDate())
            .betweenIfPresent(PunishmentDO::getEndDate, reqVO.getEndDate())
            .betweenIfPresent(PunishmentDO::getActualStartDate, reqVO.getActualStartDate())
            .betweenIfPresent(PunishmentDO::getActualEndDate, reqVO.getActualEndDate())
            .eqIfPresent(PunishmentDO::getDuration, reqVO.getDuration())
            .eqIfPresent(PunishmentDO::getActualDuration, reqVO.getActualDuration())
            .eqIfPresent(PunishmentDO::getMeasures, reqVO.getMeasures())
            .eqIfPresent(PunishmentDO::getSpecificReason, reqVO.getSpecificReason())
            .eqIfPresent(PunishmentDO::getRemark, reqVO.getRemark())
            .eqIfPresent(PunishmentDO::getExecutorSfzh, reqVO.getExecutorSfzh())
            .eqIfPresent(PunishmentDO::getExecutor, reqVO.getExecutor())
            .betweenIfPresent(PunishmentDO::getExecutorRegTime, reqVO.getExecutorRegTime())
            .eqIfPresent(PunishmentDO::getExecuteSituation, reqVO.getExecuteSituation())
            .eqIfPresent(PunishmentDO::getIsInAdvanceRemove, reqVO.getIsInAdvanceRemove())
            .eqIfPresent(PunishmentDO::getStatus, reqVO.getStatus())
            .eqIfPresent(PunishmentDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(PunishmentDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(PunishmentDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(PunishmentDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(PunishmentDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(PunishmentDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(PunishmentDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(PunishmentDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(PunishmentDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(PunishmentDO::getAddTime));    }


    /**
     * 状态更新
     * <AUTHOR>
     * @date 2025/6/14 11:08
     * @param [nowDate]
     * @return int
     */
    int updatePenaltyRemainsLifted(@Param("nowDate")Date nowDate);
}
