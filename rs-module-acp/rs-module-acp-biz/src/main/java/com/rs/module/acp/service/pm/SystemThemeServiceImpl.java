package com.rs.module.acp.service.pm;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.pm.vo.SystemThemeListReqVO;
import com.rs.module.acp.controller.admin.pm.vo.SystemThemePageReqVO;
import com.rs.module.acp.controller.admin.pm.vo.SystemThemeSaveReqVO;
import com.rs.module.acp.dao.pm.SystemThemeDao;
import com.rs.module.acp.entity.pm.SystemThemeDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 实战平台-主题-主题配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SystemThemeServiceImpl extends BaseServiceImpl<SystemThemeDao, SystemThemeDO> implements SystemThemeService {

    @Resource
    private SystemThemeDao systemThemeDao;

    @Override
    public String createSystemTheme(SystemThemeSaveReqVO createReqVO) {
        // 插入
        SystemThemeDO systemTheme = BeanUtils.toBean(createReqVO, SystemThemeDO.class);
        systemThemeDao.insert(systemTheme);
        // 返回
        return systemTheme.getId();
    }

    @Override
    public void updateSystemTheme(SystemThemeSaveReqVO updateReqVO) {
        // 校验存在
        validateSystemThemeExists(updateReqVO.getId());
        // 更新
        SystemThemeDO updateObj = BeanUtils.toBean(updateReqVO, SystemThemeDO.class);
        systemThemeDao.updateById(updateObj);
    }

    @Override
    public void deleteSystemTheme(String id) {
        // 校验存在
        validateSystemThemeExists(id);
        // 删除
        systemThemeDao.deleteById(id);
    }

    private void validateSystemThemeExists(String id) {
        if (systemThemeDao.selectById(id) == null) {
            throw new ServerException("实战平台-主题-主题配置数据不存在");
        }
    }

    @Override
    public SystemThemeDO getSystemTheme(String id) {
        return systemThemeDao.selectById(id);
    }

    @Override
    public PageResult<SystemThemeDO> getSystemThemePage(SystemThemePageReqVO pageReqVO) {
        return systemThemeDao.selectPage(pageReqVO);
    }

    @Override
    public List<SystemThemeDO> getSystemThemeList(SystemThemeListReqVO listReqVO) {
        return systemThemeDao.selectList(listReqVO);
    }


}
