package com.rs.module.acp.cons;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

public class SugDesusBusinessStatus {
    public interface BusinessStatus {
        String getCode();
        String getName();
    }

    @AllArgsConstructor
    @Getter
    public enum WhenDetainedStatus implements BusinessStatus {
        POLICE_SAVE("0100", "民警登记中"),
        WAITING_DOCTOR_SUBMIT("0101", "待医生登记"),
        DOCTOR_SAVE("0103", "医生登记中"),
        DOCTOR_SUBMIT("0102", "待领导审批"),
        LEADER_APPROVAL_REJECTED("0104", "领导审批不通过"),
        WAITING_DIRECTOR_APPROVAL("0105", "待所长审批"),
        DIRECTOR_APPROVAL_REJECTED("0106", "所长审批不通过"),
        WAITING_LEGAL_APPROVAL("0107", "待法综反馈"),
        LEGAL_FEEDBACK_REJECTED("0108", "法综反馈未通过"),
        LEGAL_FEEDBACK_APPROVED("0109", "法综反馈已通过"),
        DETENTION_STOPPED("0110", "已停止拘留"),
        CASE_CLOSED("0111", "已完结");
        private final String code;
        private final String name;
        public static WhenDetainedStatus getByCode(String code) {
            return Stream.of(values())
                    .filter(e -> e.getCode().equals(code))
                    .findFirst()
                    .orElse(null);
        }
    }

    @AllArgsConstructor
    @Getter
    public enum AfterDetainedStatus implements BusinessStatus {
        POLICE_SAVE("0200", "医生登记中"),
        WAITING_POLICE_APPROVAL("0201", "待民警审批"),
        POLICE_APPROVAL_REJECTED("0202", "民警审批不通过"),
        WAITING_LEADER_APPROVAL("0203", "待领导审批"),
        LEADER_APPROVAL_REJECTED("0204", "领导审批不通过"),
        WAITING_DIRECTOR_APPROVAL("0205", "待所长审批"),
        DIRECTOR_APPROVAL_REJECTED("0206", "所长审批不通过"),
        WAITING_LEGAL_APPROVAL("0207", "待法综反馈"),
        LEGAL_FEEDBACK_REJECTED("0208", "法综反馈未通过"),
        LEGAL_FEEDBACK_APPROVED("0209", "法综反馈已通过"),
        DETENTION_STOPPED("0210", "已停止拘留"),
        CASE_CLOSED("0211", "已完结");

        private final String code;
        private final String name;

        public static AfterDetainedStatus getByCode(String code) {
            return Stream.of(values())
                    .filter(e -> e.getCode().equals(code))
                    .findFirst()
                    .orElse(null);
        }
    }

    /**
     * 根据 jtlx 获取对应类型的业务状态枚举
     * @param jtlx 业务类型（"01": 收拘时；其他: 收拘后）
     * @param code 状态码
     * @return 通用的 BusinessStatus 接口对象
     */
    public static BusinessStatus getStatusByJtlxAndCode(String jtlx, String code) {
        code = jtlx+code;
        if ("01".equals(jtlx)) {
            return WhenDetainedStatus.getByCode(code);
        } else {
            return AfterDetainedStatus.getByCode(code);
        }
    }
}
