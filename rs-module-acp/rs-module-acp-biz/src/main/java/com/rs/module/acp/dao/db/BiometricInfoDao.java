package com.rs.module.acp.dao.db;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.db.BiometricInfoDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-收押业务-生物特征信息 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BiometricInfoDao extends IBaseDao<BiometricInfoDO> {


    default PageResult<BiometricInfoDO> selectPage(BiometricInfoPageReqVO reqVO) {
        Page<BiometricInfoDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<BiometricInfoDO> wrapper = new LambdaQueryWrapperX<BiometricInfoDO>()
            .eqIfPresent(BiometricInfoDO::getRybh, reqVO.getRybh())
            .eqIfPresent(BiometricInfoDO::getCjxmlx, reqVO.getCjxmlx())
            .eqIfPresent(BiometricInfoDO::getSwtz, reqVO.getSwtz())
            .eqIfPresent(BiometricInfoDO::getSwtzfj, reqVO.getSwtzfj())
            .eqIfPresent(BiometricInfoDO::getBz, reqVO.getBz())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(BiometricInfoDO::getAddTime);
        }
        Page<BiometricInfoDO> biometricInfoPage = selectPage(page, wrapper);
        return new PageResult<>(biometricInfoPage.getRecords(), biometricInfoPage.getTotal());
    }
    default List<BiometricInfoDO> selectList(BiometricInfoListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BiometricInfoDO>()
            .eqIfPresent(BiometricInfoDO::getRybh, reqVO.getRybh())
            .eqIfPresent(BiometricInfoDO::getCjxmlx, reqVO.getCjxmlx())
            .eqIfPresent(BiometricInfoDO::getSwtz, reqVO.getSwtz())
            .eqIfPresent(BiometricInfoDO::getSwtzfj, reqVO.getSwtzfj())
            .eqIfPresent(BiometricInfoDO::getBz, reqVO.getBz())
        .orderByDesc(BiometricInfoDO::getAddTime));    }


    }
