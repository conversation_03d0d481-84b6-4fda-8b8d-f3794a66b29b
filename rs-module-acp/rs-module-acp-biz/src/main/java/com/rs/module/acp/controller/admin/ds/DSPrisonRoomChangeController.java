package com.rs.module.acp.controller.admin.ds;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.ds.vo.*;
import com.rs.module.acp.entity.ds.DSPrisonRoomChangeDO;
import com.rs.module.acp.service.ds.DSPrisonRoomChangeService;

@Api(tags = "实战平台-数据固化-监所人员变动记录")
@RestController
@RequestMapping("/acp/ds/prisonRoomChange")
@Validated
public class DSPrisonRoomChangeController {

    @Resource
    private DSPrisonRoomChangeService prisonRoomChangeService;
/*
    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-数据固化-监所人员变动记录")
    public CommonResult<String> createPrisonRoomChange(@Valid @RequestBody PrisonRoomChangeSaveReqVO createReqVO) {
        return success(prisonRoomChangeService.createPrisonRoomChange(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-数据固化-监所人员变动记录")
    public CommonResult<Boolean> updatePrisonRoomChange(@Valid @RequestBody PrisonRoomChangeSaveReqVO updateReqVO) {
        prisonRoomChangeService.updatePrisonRoomChange(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-数据固化-监所人员变动记录")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deletePrisonRoomChange(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           prisonRoomChangeService.deletePrisonRoomChange(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-数据固化-监所人员变动记录")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<DSPrisonRoomChangeRespVO> getPrisonRoomChange(@RequestParam("id") String id) {
        DSPrisonRoomChangeDO prisonRoomChange = prisonRoomChangeService.getPrisonRoomChange(id);
        return success(BeanUtils.toBean(prisonRoomChange, DSPrisonRoomChangeRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-数据固化-监所人员变动记录分页")
    public CommonResult<PageResult<DSPrisonRoomChangeRespVO>> getPrisonRoomChangePage(@Valid @RequestBody PrisonRoomChangePageReqVO pageReqVO) {
        PageResult<DSPrisonRoomChangeDO> pageResult = prisonRoomChangeService.getPrisonRoomChangePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DSPrisonRoomChangeRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-数据固化-监所人员变动记录列表")
    public CommonResult<List<DSPrisonRoomChangeRespVO>> getPrisonRoomChangeList(@Valid @RequestBody PrisonRoomChangeListReqVO listReqVO) {
        List<DSPrisonRoomChangeDO> list = prisonRoomChangeService.getPrisonRoomChangeList(listReqVO);
        return success(BeanUtils.toBean(list, DSPrisonRoomChangeRespVO.class));
    }*/
    @PostMapping("/roommates")
    @ApiOperation(value = "获取同监室人员列表")
    public CommonResult<List<DSPrisonRoomChangeRespVO>> getPrisonRoommates(@Valid @RequestBody DSPrisonRoommateQueryReqVO queryReqVO) {
        List<DSPrisonRoomChangeRespVO> roommates = prisonRoomChangeService.getPrisonRoommatesGrouped(queryReqVO);
        return success(roommates);
    }
    @PostMapping("/roommatesGroups")
    @ApiOperation(value = "分组获取同监室人员列表")
    public CommonResult<List<DSPrisonRoomChangeRespVO>> getPrisonRoommatesGrouped(@Valid @RequestBody DSPrisonRoommateQueryReqVO queryReqVO) {
        List<DSPrisonRoomChangeRespVO> roommates = prisonRoomChangeService.getPrisonRoommatesGrouped(queryReqVO);
        return success(roommates);
    }
}
