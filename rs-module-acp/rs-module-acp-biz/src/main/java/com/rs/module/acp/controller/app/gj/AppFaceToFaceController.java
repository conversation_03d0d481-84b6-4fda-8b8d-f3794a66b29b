package com.rs.module.acp.controller.app.gj;

import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.enums.DataSourceAppEnum;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceConfigListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceConfigRespVO;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceSaveReqVO;
import com.rs.module.acp.entity.gj.FaceToFaceConfigDO;
import com.rs.module.acp.service.gj.face2face.FaceToFaceConfigService;
import com.rs.module.acp.service.gj.face2face.FaceToFaceService;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.enums.BusTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-管教业务-面对面管理")
@RestController
@RequestMapping("/app/acp/gj/faceToFace")
@Validated
public class AppFaceToFaceController {

    @Resource
    private FaceToFaceService faceToFaceService;

    @Resource
    private FaceToFaceConfigService faceToFaceConfigService;

    @PostMapping("/create")
    @ApiOperation(value = "App-创建实战平台-管教业务-面对面管理")
    @BusTrace(busType = BusTypeEnum.YEWU_MDMGL, roomId = "{{#createReqVO.roomId}}", content = "{\"检查民警\":\"{{#createReqVO.checkPolice}}\"," +
            "\"检查时间\":\"{{#fDateTime(#createReqVO.checkTime)}}\",\"情况记录\":\"{{#createReqVO.situationRecord}}\"}", businessId = "{{#result.data}}")
    public CommonResult<String> createFaceToFace(@Valid @RequestBody FaceToFaceSaveReqVO createReqVO) {
        createReqVO.setCheckPoliceSfzh(SessionUserUtil.getSessionUser().getIdCard());
        createReqVO.setDataSources(DataSourceAppEnum.CWP.getCode());
        return success(faceToFaceService.createFaceToFace(createReqVO));
    }

    @GetMapping("/config/list")
    @ApiOperation(value = "App-获得实战平台-管教业务-面对面配置列表")
    public CommonResult<List<FaceToFaceConfigRespVO>> getFaceToFaceConfigList() {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        List<FaceToFaceConfigDO> list = faceToFaceConfigService.selectListByOrgCode(sessionUser.getOrgCode());
        return success(BeanUtils.toBean(list, FaceToFaceConfigRespVO.class));
    }

}
