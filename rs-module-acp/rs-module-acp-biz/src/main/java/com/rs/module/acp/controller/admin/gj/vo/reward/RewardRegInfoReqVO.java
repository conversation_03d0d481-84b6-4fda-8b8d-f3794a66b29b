package com.rs.module.acp.controller.admin.gj.vo.reward;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-奖励管理-奖励登记VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RewardRegInfoReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    @NotEmpty(message = "主键ID，不能为空！")
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("执行时间")
    private Date executionTime;

    @ApiModelProperty("执行情况")
    private String executeSituation;


}
