package com.rs.module.acp.service.db;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.ServeSentenceDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.base.vo.ApproveReqVO;

/**
 * 实战平台-收押业务-留所服刑 Service 接口
 *
 * <AUTHOR>
 */
public interface ServeSentenceService extends IBaseService<ServeSentenceDO>{

    /**
     * 创建实战平台-收押业务-留所服刑
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createServeSentence(@Valid ServeSentenceSaveReqVO createReqVO);

    /**
     * 更新实战平台-收押业务-留所服刑
     *
     * @param updateReqVO 更新信息
     */
    void updateServeSentence(@Valid ServeSentenceSaveReqVO updateReqVO);

    /**
     * 删除实战平台-收押业务-留所服刑
     *
     * @param id 编号
     */
    void deleteServeSentence(String id);

    /**
     * 获得实战平台-收押业务-留所服刑
     *
     * @param id 编号
     * @return 实战平台-收押业务-留所服刑
     */
    ServeSentenceDO getServeSentence(String id);

    /**
    * 获得实战平台-收押业务-留所服刑分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-收押业务-留所服刑分页
    */
    PageResult<ServeSentenceDO> getServeSentencePage(ServeSentencePageReqVO pageReqVO);

    /**
    * 获得实战平台-收押业务-留所服刑列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-收押业务-留所服刑列表
    */
    List<ServeSentenceDO> getServeSentenceList(ServeSentenceListReqVO listReqVO);

    /**
     * 审批
     * @param approveReqVO
     */
    void approve(ApproveReqVO approveReqVO);

}
