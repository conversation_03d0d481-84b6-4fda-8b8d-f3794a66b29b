package com.rs.module.acp.service.sys;

import cn.hutool.core.collection.CollUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.sys.vo.*;
import com.rs.module.acp.dao.sys.BasicConfigDao;
import com.rs.module.acp.entity.sys.BasicConfigDO;
import com.rs.module.acp.entity.sys.BasicConfigUrlDO;
import com.rs.module.acp.enums.sys.UrlTypeEnum;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * 实战平台-系统基础配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BasicConfigServiceImpl extends BaseServiceImpl<BasicConfigDao, BasicConfigDO> implements BasicConfigService {

    @Resource
    private BasicConfigDao basicConfigDao;

    @Resource
    private BasicConfigUrlService basicConfigUrlService;

    //@Value("${file-url-prefix}")
    //private String pathPrefix;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createBasicConfig(BasicConfigSaveReqVO createReqVO) {
        // 插入
        BasicConfigDO basicConfig = BeanUtils.toBean(createReqVO, BasicConfigDO.class);
        basicConfigDao.insert(basicConfig);
        basicConfigUrlService.saveConfigBatchInfo(createReqVO, basicConfig.getId());
        return basicConfig.getId();
    }



    @Override
    public void updateBasicConfig(BasicConfigSaveReqVO updateReqVO) {
        //校验存在
        validateBasicConfigExists(updateReqVO.getId());
        //更新
        BasicConfigDO updateObj = BeanUtils.toBean(updateReqVO, BasicConfigDO.class);
        basicConfigDao.updateById(updateObj);

        basicConfigUrlService.saveConfigBatchInfo(updateReqVO, updateObj.getId());

    }

    @Override
    public void deleteBasicConfig(String id) {
        // 校验存在
        validateBasicConfigExists(id);
        // 删除
        basicConfigDao.deleteById(id);
        basicConfigUrlService.remove(new LambdaQueryWrapperX<BasicConfigUrlDO>().eq(BasicConfigUrlDO::getSystemId, id));
    }

    private void validateBasicConfigExists(String id) {
        if (basicConfigDao.selectById(id) == null) {
            throw new ServerException("实战平台-系统基础配置数据不存在");
        }
    }

    @Override
    public BasicConfigRespVO getBasicConfig() {
        List<BasicConfigDO> basicConfigDOList = basicConfigDao.selectList();
        BasicConfigDO basicConfigDO  = basicConfigDOList.get(0);
        BasicConfigRespVO vo = BeanUtils.toBean(basicConfigDO, BasicConfigRespVO.class);
        List<BasicConfigUrlDO> urlDOList = basicConfigUrlService.list(new LambdaQueryWrapperX<BasicConfigUrlDO>().eq(BasicConfigUrlDO::getSystemId, basicConfigDO.getId()));
        if(CollUtil.isNotEmpty(urlDOList)){
            List<BasicConfigUrlRespVO> browsers = new ArrayList<>();
            List<BasicConfigUrlRespVO> plugins = new ArrayList<>();
            List<BasicConfigUrlRespVO> hyperlink = new ArrayList<>();
            urlDOList.forEach( e->{
                BasicConfigUrlRespVO basicConfigUrlRespVO = new BasicConfigUrlRespVO();
                BeanUtils.copyProperties(e, basicConfigUrlRespVO);;
                if(UrlTypeEnum.PLUGIN.getCode().equals(e.getUrlType())){
                    plugins.add(basicConfigUrlRespVO );
                }
                if(UrlTypeEnum.HYPERLINK.getCode().equals(e.getUrlType())){
                    hyperlink.add(basicConfigUrlRespVO );
                }
                if(UrlTypeEnum.BROWSER.getCode().equals(e.getUrlType())){
                    browsers.add(basicConfigUrlRespVO );
                }
            });
            vo.setBrowsers(browsers);
            vo.setHyperlink(hyperlink);
            vo.setPlugins(plugins);
        }

        return vo;
    }

    @Override
    public PageResult<BasicConfigDO> getBasicConfigPage(BasicConfigPageReqVO pageReqVO) {
        return basicConfigDao.selectPage(pageReqVO);
    }

    @Override
    public List<BasicConfigDO> getBasicConfigList(BasicConfigListReqVO listReqVO) {
        return basicConfigDao.selectList(listReqVO);
    }


}
