package com.rs.module.acp.controller.admin.db.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "基本信息及医生审核信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CombineRespVO extends BaseVO implements TransPojo{

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("姓名")
    private String xm;

    @ApiModelProperty("别名")
    private String bm;
    @ApiModelProperty("性别")
    @Trans(type = TransType.DICTIONARY,key = "ZD_XB")
    private String xb;

    @ApiModelProperty("出生日期")
    private Date csrq;

    @ApiModelProperty("案由代码")
    @Trans(type = TransType.DICTIONARY,key = "ZD_AJLB")
    private String ajlbdm;
    @ApiModelProperty("案件类别名称")
    private String ajlb;
    @ApiModelProperty("案件编号")
    private String ajbh;
    @ApiModelProperty("人员编号")
    private String rybh;

    @ApiModelProperty("监室号")
    private String jsh;

    @ApiModelProperty("新增时间")
    private Date addTime;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("入所时间")
    private Date rssj;

    @ApiModelProperty("涉嫌罪名")
    @Trans(type = TransType.DICTIONARY,key = "ZD_AJLB")
    private String sszx;

    @ApiModelProperty("医生意见")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYRSYSYJ")
    private String ysyj;

    @ApiModelProperty("检查人")
    private String jcr;
    @ApiModelProperty("检查时间")
    private Date jcsj;
    @ApiModelProperty("备注")
    private String bz;

    @ApiModelProperty("监室名称")
    private String roomName;
}
