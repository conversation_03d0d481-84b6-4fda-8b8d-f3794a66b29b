package com.rs.module.acp.service.gj.civilizedpersonne;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne.CivilizedPersonneDetailListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne.CivilizedPersonneDetailPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne.CivilizedPersonneDetailSaveReqVO;
import com.rs.module.acp.entity.gj.CivilizedPersonneDetailDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-文明个人登记明细 Service 接口
 *
 * <AUTHOR>
 */
public interface CivilizedPersonneDetailService extends IBaseService<CivilizedPersonneDetailDO>{

    /**
     * 创建实战平台-管教业务-文明个人登记明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCivilizedPersonneDetail(@Valid CivilizedPersonneDetailSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-文明个人登记明细
     *
     * @param updateReqVO 更新信息
     */
    void updateCivilizedPersonneDetail(@Valid CivilizedPersonneDetailSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-文明个人登记明细
     *
     * @param id 编号
     */
    void deleteCivilizedPersonneDetail(String id);

    /**
     * 获得实战平台-管教业务-文明个人登记明细
     *
     * @param id 编号
     * @return 实战平台-管教业务-文明个人登记明细
     */
    CivilizedPersonneDetailDO getCivilizedPersonneDetail(String id);

    /**
    * 获得实战平台-管教业务-文明个人登记明细分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-文明个人登记明细分页
    */
    PageResult<CivilizedPersonneDetailDO> getCivilizedPersonneDetailPage(CivilizedPersonneDetailPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-文明个人登记明细列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-文明个人登记明细列表
    */
    List<CivilizedPersonneDetailDO> getCivilizedPersonneDetailList(CivilizedPersonneDetailListReqVO listReqVO);


}
