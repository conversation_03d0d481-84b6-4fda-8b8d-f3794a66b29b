package com.rs.module.acp.controller.admin.db.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "看守所收押登记案件信息VO")
@Data
public class DetainRegKssCaseVO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("案件类别代码")
    @Trans(type = TransType.DICTIONARY,key = "ZD_AJLB")
    private String ajlbdm;

    @ApiModelProperty("案件类别名称")
    private String ajlb;

    @ApiModelProperty("限制会见案件")
    private Short xzhjaj;

    @ApiModelProperty("是否限制会见")
    @Trans(type = TransType.DICTIONARY,key = "ZD_TYSFDM")
    private Short xzhj;

    @ApiModelProperty("同案编号")
    private String tabh;

    @ApiModelProperty("收押凭证")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYYWSYPZLX")
    private String sypz;

    @ApiModelProperty("收押凭证文书号")
    private String sypzwsh;

    @ApiModelProperty("案事件相关人员编号")
    private String asjxgrybh;

    @ApiModelProperty("入所时间")
    private Date rssj;

    @ApiModelProperty("诉讼环节")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SSJD")
    private String sshj;

    @ApiModelProperty("强制措施类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYYWQZCS")
    private String qzcslx;

    @ApiModelProperty("送押机关类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYYWSYDWLX")
    private String syjglx;

    @ApiModelProperty("送押机关名称")
    @Trans(type = TransType.DICTIONARY,key = "ZD_BADW_GAJG")
    private String syjgmc;

    @ApiModelProperty("送押单位")
    private String sydw;

    @ApiModelProperty("送押人")
    private String syr1;

    @ApiModelProperty("送押人固话")
    private String syrgh1;

    @ApiModelProperty("送押人手机")
    private String syrsj1;

    @ApiModelProperty("案件编号")
    private String ajbh;

    @ApiModelProperty("办案单位")
    @Trans(type = TransType.DICTIONARY,key = "ZD_BADW_GAJG")
    private String badw;

    @ApiModelProperty("办案单位类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_KSS_BADWLX")
    private String badwlx;

    @ApiModelProperty("办案环节")
    @Trans(type = TransType.DICTIONARY,key = "ZD_KSS_BAHJ")
    private String bahj;

    @ApiModelProperty("羁押日期")
    private Date jyrq;

    @ApiModelProperty("拘留日期")
    private Date jlrq;

    @ApiModelProperty("逮捕日期")
    private Date dbrq;

    @ApiModelProperty("关押期限")
    private Date gyqx;

    @ApiModelProperty("入所原因")
    @Trans(type = TransType.DICTIONARY,key = "ZD_KSS_RSYY")
    private String rsyy;

    @ApiModelProperty("法律文书号")
    private String flwsh;

    @ApiModelProperty("简要案情")
    private String jyaq;
}
