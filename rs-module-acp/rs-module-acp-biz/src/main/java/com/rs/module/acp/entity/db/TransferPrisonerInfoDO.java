package com.rs.module.acp.entity.db;

import com.baomidou.mybatisplus.annotation.*;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

@Data
@ApiModel("转所人员信息实体")
public class TransferPrisonerInfoDO {

    @ApiModelProperty("主键ID")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("监室号")
    private String jsh;

    @ApiModelProperty("监室名称")
    private String room_name;

    @ApiModelProperty("证件号码")
    private String zjhm;

    @ApiModelProperty("入所时间")
    private Date rssj;

    @ApiModelProperty("关押期限")
    private String gyqx;

    @ApiModelProperty("涉嫌罪名")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SXZM")
    private String sxzm;

    @ApiModelProperty("办案单位")
    private String badw;

    @ApiModelProperty("办案环节/诉讼环节")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SSJD")
    private String sshj;
}

