package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.CasePersonnelDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-办案人员 Service 接口
 *
 * <AUTHOR>
 */
public interface CasePersonnelService extends IBaseService<CasePersonnelDO>{

    /**
     * 创建实战平台-窗口业务-办案人员
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCasePersonnel(@Valid CasePersonnelSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-办案人员
     *
     * @param updateReqVO 更新信息
     */
    void updateCasePersonnel(@Valid CasePersonnelSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-办案人员
     *
     * @param id 编号
     */
    void deleteCasePersonnel(String id);

    /**
     * 获得实战平台-窗口业务-办案人员
     *
     * @param id 编号
     * @return 实战平台-窗口业务-办案人员
     */
    CasePersonnelDO getCasePersonnel(String id);

    /**
    * 获得实战平台-窗口业务-办案人员分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-办案人员分页
    */
    PageResult<CasePersonnelRespVO> getCasePersonnelPage(CasePersonnelPageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-办案人员列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-办案人员列表
    */
    List<CasePersonnelRespVO> getCasePersonnelList(CasePersonnelListReqVO listReqVO);

    /**
     * 获得实战平台-窗口业务-根据ids获取办案人员列表
     *
     * @param idList 查询条件
     * @return 实战平台-窗口业务-办案人员列表
     */
    List<CasePersonnelRespVO> getByIds(List<String> idList);

    /**
     * 实战平台-窗口业务-根据id获取办案人员
     *
     * @param id 查询条件
     * @return 实战平台-窗口业务-办案人员列表
     */
    CasePersonnelRespVO getCasePersonnelById(String id);

    /**
     * 实战平台-窗口业务-保存默认的被监管人员所关联的民警
     * @param createReqVO
     * @return
     */
    boolean saveDefaultCasePersonnelList(@Valid List<DefaultCasePersonneSaveReqVO> createReqVO);


    /**
     * 实战平台-窗口业务-根据被监管人员编码获取默认关联的民警
     * @param jgrybm
     * @return
     */
    List<CasePersonnelRespVO> getDefaultCasePersonnelList(String jgrybm);

}
