package com.rs.module.acp.controller.admin.db.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-出所就医新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class OutHospitalSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("预约时间")
//    @NotNull(message = "预约时间不能为空")
    private Date appointmentTime;

    @ApiModelProperty("就诊医院")
//    @NotEmpty(message = "就诊医院不能为空")
    private String hospital;

    @ApiModelProperty("病情描述")
//    @NotEmpty(message = "病情描述不能为空")
    private String symptomDesc;

    @ApiModelProperty("精神状况")
    private String jszk;

    @ApiModelProperty("是否存在拒绝服药或抗拒治疗情况")
//    @NotNull(message = "是否存在拒绝服药或抗拒治疗情况不能为空")
    private Short sfczjjfyhkjzlqk;

    @ApiModelProperty("是否存在吞食异物情况")
//    @NotNull(message = "是否存在吞食异物情况不能为空")
    private Short sfcztsywqk;

    @ApiModelProperty("是否存在不服从管理或自残行为")
//    @NotNull(message = "是否存在不服从管理或自残行为不能为空")
    private Short sfczbfcglhzcxw;

    @ApiModelProperty("是否存在其他微信行为或异常情况")
//    @NotNull(message = "是否存在其他微信行为或异常情况不能为空")
    private Short sfczqtwxxwhycqk;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("呈批医务主任")
//    @NotEmpty(message = "呈批医务主任不能为空")
    private String cpywzr;

    @ApiModelProperty("呈批医务主任姓名")
//    @NotEmpty(message = "呈批医务主任姓名不能为空")
    private String cpywzrxm;

    @ApiModelProperty("经办人")
//    @NotEmpty(message = "经办人不能为空")
    private String jbr;

    @ApiModelProperty("经办时间")
//    @NotNull(message = "经办时间不能为空")
    private Date jbsj;

    @ApiModelProperty("医生意见")
    private String ywzryj;

    @ApiModelProperty("医务主任备注")
    private String ywzrbz;

    @ApiModelProperty("医务主任身份证号")
    private String ywzrsfzh;

    @ApiModelProperty("医务主任姓名")
    private String ywzrxm;

    @ApiModelProperty("医务主任意见经办人")
    private String ywzryjJbr;

    @ApiModelProperty("医务主任意见经办时间")
    private Date ywzryjJbsj;

    @ApiModelProperty("出所-押解单位")
    private String yjdw;

    @ApiModelProperty("出所-押解民警")
    private String yjmj;

    @ApiModelProperty("出所-押解辅警")
    private String yjfj;

    @ApiModelProperty("出所-医务人员")
    private String ywry;

    @ApiModelProperty("出所-电子脚铐编号")
    private String dzjkbh;

    @ApiModelProperty("出所-押解车牌号")
    private String yjcph;

    @ApiModelProperty("出所-执法记录仪")
    private String zfjly;

    @ApiModelProperty("出所-安全管控措施")
    private String aqgkcs;

    @ApiModelProperty("是否需要用兵单")
    private Short sfxyybd;

    @ApiModelProperty("出发时间")
    private Date cfsj;

    @ApiModelProperty("集合地点")
    private String jhdd;

    @ApiModelProperty("在押人员数量")
    private Integer zyrysl;

    @ApiModelProperty("民警人数")
    private Integer mjrs;

    @ApiModelProperty("拟用兵数")
    private Integer nybs;

    @ApiModelProperty("到达地点")
    private String dddd;

    @ApiModelProperty("外押原因")
    private String wyyy;

    @ApiModelProperty("押解路线")
    private String yjlx;

    @ApiModelProperty("特别要求")
    private String tbyq;

    @ApiModelProperty("出所-是否更换戒具")
    private Short sfghjj;

    @ApiModelProperty("出所-是否开启电子脚扣")
    private Short sfkqdzjk;

    @ApiModelProperty("出所-是否开启执法记录义")
    private Short sfkqzfjly;

    @ApiModelProperty("出所是否离所确认")
    private Short sflsqr;

    @ApiModelProperty("出所-离所确认备注")
    private String lsqrbz;

    @ApiModelProperty("出所-勤务安排经办人")
    private String qwapJbr;

    @ApiModelProperty("出所-勤务安排经办时间")
    private Date qwapJbsj;

    @ApiModelProperty("回所-押解单位")
    private String hsYjdw;

    @ApiModelProperty("回所-押解民警")
    private String hsYjmj;

    @ApiModelProperty("回所-押解辅警")
    private String hsYjfj;

    @ApiModelProperty("回所-医务人员")
    private String hsYwry;

    @ApiModelProperty("回所-电子脚铐编号")
    private String hsDzjkbh;

    @ApiModelProperty("回所-押解车牌号")
    private String hsYjcph;

    @ApiModelProperty("回所-执法记录仪")
    private String hsZfjly;

    @ApiModelProperty("回所-安全管控措施")
    private String hsAqgkcs;

    @ApiModelProperty("回所是否需要用兵单")
    private Short hsSfxyybd;

    @ApiModelProperty("回所出发时间")
    private Date hsCfsj;

    @ApiModelProperty("回所集合地点")
    private String hsJhdd;

    @ApiModelProperty("回所在押人员数量")
    private Integer hsZyrysl;

    @ApiModelProperty("回所民警人数")
    private Integer hsMjrs;

    @ApiModelProperty("回所拟用兵数")
    private Integer hsNybs;

    @ApiModelProperty("回所到达地点")
    private String hsDddd;

    @ApiModelProperty("回所外押原因")
    private String hsWyyy;

    @ApiModelProperty("回所押解路线")
    private String hsYjlx;

    @ApiModelProperty("回所特别要求")
    private String hsTbyq;

    @ApiModelProperty("回所-是否更换戒具")
    private Short hsSfghjj;

    @ApiModelProperty("回所-是否开启电子脚扣")
    private Short hsSfkqdzjk;

    @ApiModelProperty("回所-是否开启执法记录义")
    private Short hsSfkqzfjly;

    @ApiModelProperty("是否回所确认")
    private Short sfhsqr;

    @ApiModelProperty("回所确认备注")
    private String hsqrbz;

    @ApiModelProperty("回所-勤务安排经办人")
    private String hsQwapJbr;

    @ApiModelProperty("回所-勤务安排经办时间")
    private Date hsQwapJbsj;

    @ApiModelProperty("办理状态")
    @NotEmpty(message = "办理状态不能为空")
    private String status;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("流程阶段")
    private String currentStep;

    @ApiModelProperty("病情等级")
    private String symptomLevel;

    @ApiModelProperty("回所ACT流程实例Id")
    private String hsActInstId;

    @ApiModelProperty("回所任务ID")
    private String hsTaskId;

    @ApiModelProperty("审批状态：1:待审批；2：同意；3：不同意")
    private String spzt;
    @ApiModelProperty("出所就医原因")
    @Trans(type = TransType.DICTIONARY,key = "ZD_CSJYYY")
    private String csjyyy;
}
