package com.rs.module.acp.service.zh;

import com.rs.module.acp.util.GeneralUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.time.LocalTime;
import java.util.*;
import com.rs.module.acp.controller.admin.zh.vo.*;
import com.rs.module.acp.entity.zh.DutySuperviseRuleDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.zh.DutySuperviseRuleDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 综合管理-值班管理-值班督导规则配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DutySuperviseRuleServiceImpl extends BaseServiceImpl<DutySuperviseRuleDao, DutySuperviseRuleDO> implements DutySuperviseRuleService {

    @Resource
    private DutySuperviseRuleDao dutySuperviseRuleDao;

    @Override
    public String createDutySuperviseRule(DutySuperviseRuleSaveReqVO createReqVO) {
        // 插入
        DutySuperviseRuleDO dutySuperviseRule = BeanUtils.toBean(createReqVO, DutySuperviseRuleDO.class);
        dutySuperviseRuleDao.insert(dutySuperviseRule);
        // 返回
        return dutySuperviseRule.getId();
    }

    @Override
    public void updateDutySuperviseRule(DutySuperviseRuleSaveReqVO updateReqVO) {
        // 校验存在
        validateDutySuperviseRuleExists(updateReqVO.getId());
        // 更新
        DutySuperviseRuleDO updateObj = BeanUtils.toBean(updateReqVO, DutySuperviseRuleDO.class);
        dutySuperviseRuleDao.updateById(updateObj);
    }

    @Override
    public void deleteDutySuperviseRule(String id) {
        // 校验存在
        validateDutySuperviseRuleExists(id);
        // 删除
        dutySuperviseRuleDao.deleteById(id);
    }

    private void validateDutySuperviseRuleExists(String id) {
        if (dutySuperviseRuleDao.selectById(id) == null) {
            throw new ServerException("综合管理-值班管理-值班督导规则配置数据不存在");
        }
    }

    @Override
    public DutySuperviseRuleDO getDutySuperviseRule(String id) {
        return dutySuperviseRuleDao.selectById(id);
    }

    @Override
    public PageResult<DutySuperviseRuleDO> getDutySuperviseRulePage(DutySuperviseRulePageReqVO pageReqVO) {
        return dutySuperviseRuleDao.selectPage(pageReqVO);
    }

    @Override
    public List<DutySuperviseRuleDO> getDutySuperviseRuleList(DutySuperviseRuleListReqVO listReqVO) {
        return dutySuperviseRuleDao.selectList(listReqVO);
    }

    @Override
    public String createOrUpdateDutySuperviseRule(DutySuperviseRuleSaveReqVO createReqVO) {
        String staffDutyTemplateId = createReqVO.getStaffDutyTemplateId();
        DutySuperviseRuleDO dutySuperviseRule = dutySuperviseRuleDao.selectOne(DutySuperviseRuleDO::getStaffDutyTemplateId,staffDutyTemplateId);
        //如果能查询到记录，则更新createVO的ID字段
        if(dutySuperviseRule!=null){
            createReqVO.setId(dutySuperviseRule.getId());
            DutySuperviseRuleDO dutySuperviseRuleDO = createReqVO.toBean(DutySuperviseRuleDO.class);
            dutySuperviseRuleDO.setSigninStartTime(LocalTime.parse(createReqVO.getSigninStartTime()));
            dutySuperviseRuleDO.setSigninEndTime(LocalTime.parse(createReqVO.getSigninEndTime()));

            dutySuperviseRuleDao.updateById(dutySuperviseRuleDO);
        }else{
            createReqVO.setId(GeneralUtil.generateUUID());
            DutySuperviseRuleDO dutySuperviseRuleDO = createReqVO.toBean(DutySuperviseRuleDO.class);
            dutySuperviseRuleDO.setSigninStartTime(LocalTime.parse(createReqVO.getSigninStartTime()));
            dutySuperviseRuleDO.setSigninEndTime(LocalTime.parse(createReqVO.getSigninEndTime()));
            dutySuperviseRuleDao.insert(dutySuperviseRuleDO);
        }
        return "true";
    }


}
