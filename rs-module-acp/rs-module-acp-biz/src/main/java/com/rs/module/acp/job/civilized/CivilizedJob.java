package com.rs.module.acp.job.civilized;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rs.module.acp.dao.gj.CivilizedPersonneDao;
import com.rs.module.acp.dao.gj.CivilizedRoomDao;
import com.rs.module.acp.entity.gj.CivilizedPersonneDO;
import com.rs.module.acp.entity.gj.CivilizedRoomDO;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class CivilizedJob {

    @Resource
    private CivilizedRoomDao civilizedRoomDao;

    @Resource
    private CivilizedPersonneDao civilizedPersonneDao;

    // 文明监室推送
    @XxlJob("pushCivilizedRoomJob")
    public void pushCivilizedRoomJob() {
        LambdaQueryWrapper<CivilizedRoomDO> wrapper = Wrappers.lambdaQuery(CivilizedRoomDO.class)
                .eq(CivilizedRoomDO::getEvalMonth, LocalDate.now().toString().substring(0, 7));
        Integer count = civilizedRoomDao.selectCount(wrapper);
        if (Objects.nonNull(count) && count > 0) {
            return;
        }
        // 查询所有机构
        List<CivilizedRoomDO> nowPushInfo = civilizedRoomDao.getNowPushInfo();
        civilizedRoomDao.insertBatch(nowPushInfo);
    }


    // 文明个人推送
    @XxlJob("pushCivilizedPersonneJob")
    public void pushCivilizedPersonneJob() {
        LambdaQueryWrapper<CivilizedPersonneDO> wrapper = Wrappers.lambdaQuery(CivilizedPersonneDO.class)
                .eq(CivilizedPersonneDO::getEvalMonth, LocalDate.now().toString().substring(0, 7));
        Integer count = civilizedPersonneDao.selectCount(wrapper);
        if (Objects.nonNull(count) && count > 0) {
            return;
        }
        // 查询所有机构
        List<CivilizedPersonneDO> nowPushInfo = civilizedPersonneDao.getNowPushInfo();
        civilizedPersonneDao.insertBatch(nowPushInfo);
    }

}
