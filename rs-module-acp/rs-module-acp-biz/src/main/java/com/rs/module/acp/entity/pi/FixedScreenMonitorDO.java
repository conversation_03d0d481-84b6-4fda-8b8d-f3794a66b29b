package com.rs.module.acp.entity.pi;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-巡视管控-定屏监控 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_fixed_screen_monitor")
@KeySequence("acp_pi_fixed_screen_monitor_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_fixed_screen_monitor")
public class FixedScreenMonitorDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 巡控室ID
     */
    private String areaId;
    /**
     * 巡控室名称
     */
    private String areaName;
    /**
     * 上墙人员信息
     */
    private String onScreenPersonInfo;
    /**
     * 上墙时间
     */
    private Date onScreenTime;
    /**
     * 下墙时间
     */
    private Date offScreenTime;
    /**
     * 经办人身份证号
     */
    private String operatorSfzh;
    /**
     * 经办人姓名
     */
    private String operatorXm;
    /**
     * 经办时间
     */
    private Date operateTime;
    /**
     * 状态
     */
    private String status;

}
