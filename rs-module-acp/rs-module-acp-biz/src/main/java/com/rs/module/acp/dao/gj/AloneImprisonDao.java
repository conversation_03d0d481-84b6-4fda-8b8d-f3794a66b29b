package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.gj.AloneImprisonDO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.gj.vo.aloneimprison.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 实战平台-管教业务-单独关押登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface AloneImprisonDao extends IBaseDao<AloneImprisonDO> {


    default PageResult<AloneImprisonDO> selectPage(AloneImprisonPageReqVO reqVO) {
        Page<AloneImprisonDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<AloneImprisonDO> wrapper = new LambdaQueryWrapperX<AloneImprisonDO>()
            .eqIfPresent(AloneImprisonDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(AloneImprisonDO::getOldRoomId, reqVO.getOldRoomId())
            .eqIfPresent(AloneImprisonDO::getNewRoomId, reqVO.getNewRoomId())
            .likeIfPresent(AloneImprisonDO::getOldRoomName, reqVO.getOldRoomName())
            .likeIfPresent(AloneImprisonDO::getNewRoomName, reqVO.getNewRoomName())
            .eqIfPresent(AloneImprisonDO::getRegisterReason, reqVO.getRegisterReason())
            .eqIfPresent(AloneImprisonDO::getSpecificReason, reqVO.getSpecificReason())
            .betweenIfPresent(AloneImprisonDO::getStartTime, reqVO.getStartTime())
            .betweenIfPresent(AloneImprisonDO::getEndTime, reqVO.getEndTime())
            .betweenIfPresent(AloneImprisonDO::getInTime, reqVO.getInTime())
            .betweenIfPresent(AloneImprisonDO::getOutTime, reqVO.getOutTime())
            .eqIfPresent(AloneImprisonDO::getStatus, reqVO.getStatus())
            .eqIfPresent(AloneImprisonDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(AloneImprisonDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(AloneImprisonDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(AloneImprisonDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(AloneImprisonDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(AloneImprisonDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(AloneImprisonDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(AloneImprisonDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(AloneImprisonDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(AloneImprisonDO::getAddTime);
        }
        Page<AloneImprisonDO> aloneImprisonPage = selectPage(page, wrapper);
        return new PageResult<>(aloneImprisonPage.getRecords(), aloneImprisonPage.getTotal());
    }
    default List<AloneImprisonDO> selectList(AloneImprisonListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AloneImprisonDO>()
            .eqIfPresent(AloneImprisonDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(AloneImprisonDO::getOldRoomId, reqVO.getOldRoomId())
            .eqIfPresent(AloneImprisonDO::getNewRoomId, reqVO.getNewRoomId())
            .likeIfPresent(AloneImprisonDO::getOldRoomName, reqVO.getOldRoomName())
            .likeIfPresent(AloneImprisonDO::getNewRoomName, reqVO.getNewRoomName())
            .eqIfPresent(AloneImprisonDO::getRegisterReason, reqVO.getRegisterReason())
            .eqIfPresent(AloneImprisonDO::getSpecificReason, reqVO.getSpecificReason())
            .betweenIfPresent(AloneImprisonDO::getStartTime, reqVO.getStartTime())
            .betweenIfPresent(AloneImprisonDO::getEndTime, reqVO.getEndTime())
            .betweenIfPresent(AloneImprisonDO::getInTime, reqVO.getInTime())
            .betweenIfPresent(AloneImprisonDO::getOutTime, reqVO.getOutTime())
            .eqIfPresent(AloneImprisonDO::getStatus, reqVO.getStatus())
            .eqIfPresent(AloneImprisonDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(AloneImprisonDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(AloneImprisonDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(AloneImprisonDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(AloneImprisonDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(AloneImprisonDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(AloneImprisonDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(AloneImprisonDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(AloneImprisonDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(AloneImprisonDO::getAddTime));    }


    List<AreaPrisonRoomDO> getAloneImprisonRoomList(@Param("nowRoomId") String nowRoomId, @Param("orgCode")String orgCode);

}
