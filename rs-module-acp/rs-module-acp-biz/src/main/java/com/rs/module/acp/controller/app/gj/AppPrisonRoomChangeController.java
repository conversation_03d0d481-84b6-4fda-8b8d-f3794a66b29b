package com.rs.module.acp.controller.app.gj;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.gj.vo.prisonroom.PrisonRoomChangeSaveReqVO;
import com.rs.module.acp.controller.app.gj.vo.prisonroom.PrisonRoomChangeAppListReqVO;
import com.rs.module.acp.controller.app.gj.vo.prisonroom.PrisonRoomChangeAppListVO;
import com.rs.module.acp.service.gj.prisonroom.PrisonRoomChangeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管教业务-监室调整-app端")
@RestController
@RequestMapping("/app/acp/gj/prisonRoomChange")
@Validated
public class AppPrisonRoomChangeController {

    @Resource
    private PrisonRoomChangeService prisonRoomChangeService;

    @PostMapping("/create")
    @ApiOperation(value = "管教业务-监室调整-创建")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomChange:create", operateType = LogOperateType.CREATE, title = "管教业务-监室调整-创建",
            success = "管教业务-监室调整-监室调整登记成功", fail = "管教业务-监室调整登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPrisonRoomChange(@Valid @RequestBody PrisonRoomChangeSaveReqVO createReqVO) {
        return success(prisonRoomChangeService.createPrisonRoomChange(createReqVO));
    }

    @PostMapping("/batchCreate")
    @ApiOperation(value = "批量创建-管教业务--监室调整")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomChange:batchCreate", operateType = LogOperateType.CREATE, title = "管教业务-监室调整-创建",
            success = "管教业务-监室调整-监室调整登记成功", fail = "管教业务-监室调整登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVOList}}")
    public CommonResult<List<String>> batchCreatePrisonRoomChange(@Valid @RequestBody List<PrisonRoomChangeSaveReqVO>  createReqVOList) {
        List<String> result = prisonRoomChangeService.batchCreatePrisonRoomChangeList(createReqVOList);
        return success(result);
    }


    @PostMapping("/page")
    @ApiOperation(value = "管教业务-获取监室调整记录")
    public CommonResult<PageResult<PrisonRoomChangeAppListVO>> getPrisonRoomChangeAppList(@Valid @RequestBody PrisonRoomChangeAppListReqVO pageReqVO) {
        PageResult<PrisonRoomChangeAppListVO> pageResult = prisonRoomChangeService.getPrisonRoomChangeAppList(pageReqVO);
        return success(pageResult);
    }



}
