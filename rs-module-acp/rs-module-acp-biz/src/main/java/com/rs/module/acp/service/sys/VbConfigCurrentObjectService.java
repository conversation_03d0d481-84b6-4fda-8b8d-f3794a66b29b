package com.rs.module.acp.service.sys;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.sys.vo.*;
import com.rs.module.acp.entity.sys.VbConfigCurrentObjectDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-语音播报-即时播报对象 Service 接口
 *
 * <AUTHOR>
 */
public interface VbConfigCurrentObjectService extends IBaseService<VbConfigCurrentObjectDO>{

    /**
     * 创建实战平台-语音播报-即时播报对象
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createVbConfigCurrentObject(@Valid VbConfigCurrentObjectSaveReqVO createReqVO);

    /**
     * 更新实战平台-语音播报-即时播报对象
     *
     * @param updateReqVO 更新信息
     */
    void updateVbConfigCurrentObject(@Valid VbConfigCurrentObjectSaveReqVO updateReqVO);

    /**
     * 删除实战平台-语音播报-即时播报对象
     *
     * @param id 编号
     */
    void deleteVbConfigCurrentObject(String id);

    /**
     * 获得实战平台-语音播报-即时播报对象
     *
     * @param id 编号
     * @return 实战平台-语音播报-即时播报对象
     */
    VbConfigCurrentObjectDO getVbConfigCurrentObject(String id);

    /**
    * 获得实战平台-语音播报-即时播报对象分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-语音播报-即时播报对象分页
    */
    PageResult<VbConfigCurrentObjectDO> getVbConfigCurrentObjectPage(VbConfigCurrentObjectPageReqVO pageReqVO);

    /**
    * 获得实战平台-语音播报-即时播报对象列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-语音播报-即时播报对象列表
    */
    List<VbConfigCurrentObjectDO> getVbConfigCurrentObjectList(VbConfigCurrentObjectListReqVO listReqVO);


}
