package com.rs.module.acp.controller.admin.gj.vo.reward;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-奖励管理 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RewardRespVO extends BaseVO implements TransPojo{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("奖励类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JLLX")
    private String rewardType;

    @ApiModelProperty("奖励原因")
    private String rewardReason;

    @ApiModelProperty("奖励内容")
    private String rewardContent;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("执行人")
    private String executor;

    @ApiModelProperty("执行人身份证号")
    private String executorSfzh;

    @ApiModelProperty("执行时间")
    private Date executionTime;

    @ApiModelProperty("执行情况")
    private String executeSituation;

    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JLZT")
    private String status;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审批结果")
    private String approvalResultName;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("呈批人")
    private String addUserName;

    @ApiModelProperty("呈批时间")
    private Date addTime;

}
