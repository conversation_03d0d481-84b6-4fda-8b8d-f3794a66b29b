package com.rs.module.acp.service.zh.shiftteam;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.zh.vo.shiftteam.*;
import com.rs.module.acp.entity.zh.StaffDutyShiftDO;
import com.rs.module.acp.entity.zh.StaffDutyTeamPersonDO;
import com.rs.module.base.controller.admin.zh.vo.staffduty.*;
import com.rs.module.base.enums.StaffDutyTypeEnum;
import com.rs.module.base.service.zh.staffduty.StaffDutyRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import com.rs.module.acp.entity.zh.StaffDutyTeamDO;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.zh.StaffDutyTeamDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 值班模板班组信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class StaffDutyTeamServiceImpl extends BaseServiceImpl<StaffDutyTeamDao, StaffDutyTeamDO> implements StaffDutyTeamService {

    @Resource
    private StaffDutyTeamDao staffDutyTeamDao;
    @Resource
    private StaffDutyTeamPersonService staffDutyTeamPersonService;

    @Autowired
    private StaffDutyRecordService staffDutyRecordService;

    @Autowired
    private StaffDutyShiftService staffDutyShiftService;
    @Override
    public String createStaffDutyTeam(StaffDutyTeamSaveReqVO createReqVO) {
        // 插入
        StaffDutyTeamDO staffDutyTeam = BeanUtils.toBean(createReqVO, StaffDutyTeamDO.class);
        staffDutyTeamDao.insert(staffDutyTeam);
        // 返回
        return staffDutyTeam.getId();
    }

    @Override
    public void updateStaffDutyTeam(StaffDutyTeamSaveReqVO updateReqVO) {
        // 校验存在
        validateStaffDutyTeamExists(updateReqVO.getId());
        // 更新
        StaffDutyTeamDO updateObj = BeanUtils.toBean(updateReqVO, StaffDutyTeamDO.class);
        staffDutyTeamDao.updateById(updateObj);
    }

    @Override
    public void deleteStaffDutyTeam(String id) {
        // 校验存在
        validateStaffDutyTeamExists(id);
        // 删除
        staffDutyTeamDao.deleteById(id);
    }

    private void validateStaffDutyTeamExists(String id) {
        if (staffDutyTeamDao.selectById(id) == null) {
            throw new ServerException("值班模板班组信息数据不存在");
        }
    }

    @Override
    public StaffDutyTeamDO getStaffDutyTeam(String id) {
        return staffDutyTeamDao.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveStaffDutyTeamAndPerson(StaffDutyTeamAndPersonSaveReqVO saveReqVO) {
        // 保存或更新值班模板班组信息
        String teamId = createOrUpdateStaffDutyTeam(saveReqVO.getTeamInfo());

        // 保存或更新值班模板班组人员信息
        if (CollectionUtil.isNotNull(saveReqVO.getPersonInfos())) {
            staffDutyTeamPersonService.createOrUpdateBatch(teamId,saveReqVO.getPersonInfos());
        }

        return teamId;
    }

    private String createOrUpdateStaffDutyTeam(StaffDutyTeamSaveReqVO teamInfo) {
        if (teamInfo.getId() == null) {
            return createStaffDutyTeam(teamInfo);
        } else {
            updateStaffDutyTeam(teamInfo);
            return teamInfo.getId();
        }
    }

    @Override
    public StaffDutyTeamAndPersonRespVO getStaffDutyTeamAndPerson(String teamId,Integer templateType) {
        // 查询值班模板班组信息
        StaffDutyTeamDO teamDO = staffDutyTeamDao.selectById(teamId);
        if (teamDO == null) {
            throw new ServerException("值班模板班组信息不存在");
        }
        StaffDutyTeamRespVO teamInfo = BeanUtils.toBean(teamDO, StaffDutyTeamRespVO.class);

        // 查询值班模板班组人员信息
        List<StaffDutyTeamPersonDO> personDOs = staffDutyTeamPersonService.selectByTeamId(teamId);
        List<StaffDutyTeamPersonRespVO> personInfos = personDOs.stream()
                .map(personDO -> BeanUtils.toBean(personDO, StaffDutyTeamPersonRespVO.class))
                .collect(Collectors.toList());

        // 分组人员信息
        Map<String, List<StaffDutyTeamPersonRespVO>> groupedPersons = personInfos.stream()
                .collect(Collectors.groupingBy(person -> person.getRoleId()));

        // 初始化响应对象
        StaffDutyTeamAndPersonRespVO respVO = new StaffDutyTeamAndPersonRespVO();
        respVO.setTeamInfo(teamInfo);
        respVO.setPersonInfos(personInfos);
        String tempId = staffDutyRecordService.getTempId(SessionUserUtil.getSessionUser().getOrgCode(),  templateType);
        List<DutyManageHeaderVO> headerList = staffDutyRecordService.getHeaderList(tempId);
        respVO.setHeaderList(headerList);
        return respVO;
    }

    @Override
    public List<StaffDutyTeamDO> getListByOrgCode(String orgCode,String shiftId) {
        if(StringUtil.isEmpty(orgCode)) orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        return staffDutyTeamDao.selectList(new LambdaQueryWrapperX<StaffDutyTeamDO>().eq(StaffDutyTeamDO::getOrgCode, orgCode).
                eq(StaffDutyTeamDO::getIsDel, 0).eq(StaffDutyTeamDO::getShiftId, shiftId));
    }
    public List<StaffDutyTeamDO> getListByOrgCodeShiftIdList(String orgCode,List<String> shiftIdList) {
        if(StringUtil.isEmpty(orgCode)) orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        return staffDutyTeamDao.selectList(new LambdaQueryWrapperX<StaffDutyTeamDO>().eq(StaffDutyTeamDO::getOrgCode, orgCode).
                eq(StaffDutyTeamDO::getIsDel, 0).in(StaffDutyTeamDO::getShiftId, shiftIdList).orderByAsc(StaffDutyTeamDO::getTeamOrder));
    }
    @Override
    public DutyManageVO shiftTeamIndex(String orgCode) {
        DutyManageVO dutyManageVO = new DutyManageVO();
        try {
            if (StringUtil.isEmpty(orgCode)) {
                orgCode = SessionUserUtil.getSessionUser().getOrgCode();
            }

            // 获取值班班次列表
            List<StaffDutyShiftDO> shiftList = staffDutyShiftService.getListByOrgCode(orgCode);
            if (CollectionUtil.isNull(shiftList)) {
                return dutyManageVO;
            }

            // 获取模板ID
            String tempId = shiftList.get(0).getTempId();
            if (StringUtil.isEmpty(tempId)) {
                tempId = staffDutyRecordService.getTempId(orgCode, StaffDutyTypeEnum.ON_DUTY_AT_INSTITUTE.getCode());
            }

            // 获取班次ID列表
            List<String> shiftIdList = shiftList.stream()
                    .map(StaffDutyShiftDO::getId)
                    .collect(Collectors.toList());

            // 获取班组列表并按班次分组
            List<StaffDutyTeamDO> teamList = getListByOrgCodeShiftIdList(orgCode, shiftIdList);
            Map<String, List<StaffDutyTeamDO>> teamMap = teamList.stream()
                    .collect(Collectors.groupingBy(StaffDutyTeamDO::getShiftId));

            // 获取班组人员列表
            List<String> teamIdList = teamList.stream()
                    .map(StaffDutyTeamDO::getId)
                    .collect(Collectors.toList());
            List<StaffDutyTeamPersonDO> teamPersonList = staffDutyTeamPersonService.selectByOrgCodeTeamIdList(orgCode, teamIdList);

            // 构建返回数据结构

            dutyManageVO.setTempId(tempId);
            List<DutyManageHeaderVO> headerList =staffDutyRecordService.getHeaderList(tempId);
            /*if (headerList != null) {
                headerList.forEach(headerVO -> {
                    Optional.ofNullable(headerVO.getSubPostList())
                            .ifPresent(subPostList -> subPostList.forEach(subPostVO -> {
                                subPostVO.setSubPostTimeVOS(null);
                            }));
                });
            }*/
            dutyManageVO.setHeaderList(headerList);

            // 构建人员列表数据结构
            JSONArray personList = new JSONArray();
            shiftList.forEach(shift -> {
                JSONObject headerVO = new JSONObject();
                headerVO.put("id", shift.getId());
                headerVO.put("names", shift.getShiftName());

                List<JSONObject> shiftTeamList = new ArrayList<>();
                teamMap.getOrDefault(shift.getId(), Collections.emptyList())
                        .forEach(team -> {
                            JSONObject teamResult = new JSONObject();
                            teamResult.put("id", team.getId());
                            teamResult.put("names", team.getTeamName());
                            teamResult.put("shiftId", team.getShiftId());

                            List<JSONObject> personResultList = teamPersonList.stream()
                                    .filter(person -> person.getTeamId().equals(team.getId()))
                                    .map(person -> {
                                        JSONObject personResult = new JSONObject();
                                        personResult.put(person.getRoleId(), person);
                                        return personResult;
                                    })
                                    .collect(Collectors.toList());

                            teamResult.put("teamPerson", personResultList);
                            shiftTeamList.add(teamResult);
                        });

                personList.add(headerVO);
            });

            dutyManageVO.setPersonList(personList);
            return dutyManageVO;

        } catch (Exception e) {
            log.error("获取值班管理信息失败", e);
           // throw new BusinessException("获取值班管理信息失败");
        }
        return dutyManageVO;
    }

    @Override
    public List<StaffDutyTeamAndPersonRespVO> getStaffDutyTeamAndPersonByShiftId(String shiftId, String orgCode, Integer templateType) {
        if(StringUtil.isEmpty(orgCode)) orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        List<StaffDutyTeamAndPersonRespVO> list = new ArrayList<>();
        // 查询值班模板班组信息
        List<StaffDutyTeamDO> teamDOList = getListByOrgCode(orgCode,shiftId);
        if (CollectionUtil.isNull(teamDOList)) {
            throw new ServerException("值班模板班组信息不存在");
        }
        String tempId = staffDutyRecordService.getTempId(orgCode,  templateType);
        List<DutyManageHeaderVO> headerList = staffDutyRecordService.getHeaderList(tempId);
        for (StaffDutyTeamDO teamDO: teamDOList) {
            StaffDutyTeamRespVO teamInfo = BeanUtils.toBean(teamDO, StaffDutyTeamRespVO.class);

            // 查询值班模板班组人员信息
            List<StaffDutyTeamPersonDO> personDOs = staffDutyTeamPersonService.selectByTeamId(teamDO.getId());
            List<StaffDutyTeamPersonRespVO> personInfos = personDOs.stream()
                    .map(personDO -> BeanUtils.toBean(personDO, StaffDutyTeamPersonRespVO.class))
                    .collect(Collectors.toList());

            // 分组人员信息
        /*    Map<String, List<StaffDutyTeamPersonRespVO>> groupedPersons = personInfos.stream()
                    .collect(Collectors.groupingBy(person -> person.getRoleId()));*/

            // 初始化响应对象
            StaffDutyTeamAndPersonRespVO respVO = new StaffDutyTeamAndPersonRespVO();
            respVO.setTeamInfo(teamInfo);
            respVO.setPersonInfos(personInfos);
            respVO.setHeaderList(headerList);
            list.add(respVO);
        }

        return list;
    }
}
