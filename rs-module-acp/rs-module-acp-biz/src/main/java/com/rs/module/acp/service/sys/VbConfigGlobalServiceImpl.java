package com.rs.module.acp.service.sys;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.sys.vo.*;
import com.rs.module.acp.entity.sys.VbConfigGlobalDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.sys.VbConfigGlobalDao;

import com.rs.framework.common.exception.ServerException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-语音播报-全局配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class VbConfigGlobalServiceImpl extends BaseServiceImpl<VbConfigGlobalDao, VbConfigGlobalDO> implements VbConfigGlobalService {

    @Resource
    private VbConfigGlobalDao vbConfigGlobalDao;

    @Override
    public String createVbConfigGlobal(VbConfigGlobalSaveReqVO createReqVO) {
        // 插入
        VbConfigGlobalDO vbConfigGlobal = BeanUtils.toBean(createReqVO, VbConfigGlobalDO.class);
        vbConfigGlobalDao.insert(vbConfigGlobal);
        // 返回
        return vbConfigGlobal.getId();
    }

    @Override
    public void updateVbConfigGlobal(VbConfigGlobalSaveReqVO updateReqVO) {
        // 校验存在
        validateVbConfigGlobalExists(updateReqVO.getId());
        // 更新
        VbConfigGlobalDO updateObj = BeanUtils.toBean(updateReqVO, VbConfigGlobalDO.class);
        vbConfigGlobalDao.updateById(updateObj);
    }

    @Override
    public void deleteVbConfigGlobal(String id) {
        // 校验存在
        validateVbConfigGlobalExists(id);
        // 删除
        vbConfigGlobalDao.deleteById(id);
    }

    private void validateVbConfigGlobalExists(String id) {
        if (vbConfigGlobalDao.selectById(id) == null) {
            throw new ServerException("实战平台-语音播报-全局配置数据不存在");
        }
    }

    @Override
    public VbConfigGlobalDO getVbConfigGlobal(String id) {
        return vbConfigGlobalDao.selectById(id);
    }

    @Override
    public PageResult<VbConfigGlobalDO> getVbConfigGlobalPage(VbConfigGlobalPageReqVO pageReqVO) {
        return vbConfigGlobalDao.selectPage(pageReqVO);
    }

    @Override
    public List<VbConfigGlobalDO> getVbConfigGlobalList(VbConfigGlobalListReqVO listReqVO) {
        return vbConfigGlobalDao.selectList(listReqVO);
    }

    @Override
    public VbConfigGlobalDO getConfig(String orgCode) {
    	VbConfigGlobalDO configDO = getOne(new LambdaQueryWrapper<VbConfigGlobalDO>().eq(VbConfigGlobalDO::getOrgCode, orgCode), false);
        if (configDO == null) {
            return new VbConfigGlobalDO();
        }
        return configDO;
    }
}
