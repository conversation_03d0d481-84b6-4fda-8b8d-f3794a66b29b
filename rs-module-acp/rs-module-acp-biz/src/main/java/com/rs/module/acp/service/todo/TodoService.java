package com.rs.module.acp.service.todo;

import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.pm.PrisonerService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Log4j2
@Service
public class TodoService {

    @Autowired
    private TodoHandlerFactory todoHandlerFactory;
    @Resource
    private PrisonerService prisonerService;

    public Object processTodo(String jgrybm, TodoBusinessType todoType) {
        // 创建上下文
        TodoContext context = new TodoContext();
        context.setJgrybm(jgrybm);
        context.setTodoType(todoType);

        // 获取处理器并处理
        TodoHandler handler = todoHandlerFactory.getHandler(todoType);
        if (handler != null && handler.canHandle(context)) {
            return handler.handle(context);
        } else {
            log.info("没有找到合适的处理器");
        }
        return null;
    }

    public int processTodoCount(String jgrybm, TodoBusinessType todoType) {
        // 创建上下文
        TodoContext context = new TodoContext();
        context.setJgrybm(jgrybm);
        context.setTodoType(todoType);

        // 获取处理器并处理
        TodoHandler handler = todoHandlerFactory.getHandler(todoType);
        if (handler != null && handler.canHandle(context)) {
            return handler.handleCount(context);
        } else {
            log.info("没有找到合适的处理器");
        }
        return 0;
    }

    public int processTodoCountByRoomId(String roomId) {

        AtomicInteger count = new AtomicInteger(0);
        List<PrisonerVwRespVO> prisonerVwRespVOList = prisonerService.getPrisonerListByJsh(roomId);
        prisonerVwRespVOList.stream().map(PrisonerVwRespVO::getJgrybm).forEach(jgrybm -> {
            for (TodoBusinessType type : TodoBusinessType.values()) {
                count.addAndGet(processTodoCount(jgrybm, type));
            }
        });
        return count.get();
    }

    public List processJgryTodoCountByRoomId(String roomId) {
        AtomicInteger count = new AtomicInteger(0);
        List<TodoCountVO> todoVOList = new ArrayList<>();
        List<PrisonerVwRespVO> prisonerVwRespVOList = prisonerService.getPrisonerListByJsh(roomId);

        prisonerVwRespVOList.stream().forEach(jgry -> {
            TodoCountVO todoVO = new TodoCountVO();
            todoVO.setPrisoner(jgry);
            for (TodoBusinessType type : TodoBusinessType.values()) {
                count.addAndGet(processTodoCount(jgry.getJgrybm(), type));
            }
            todoVO.setTodoCount(count.get());
            todoVOList.add(todoVO);
        });
        return todoVOList;
    }

    public List<TodoBizCountVO> processBizTodoCount(String jgrybm) {
        List<TodoBizCountVO> todoBizCountVOList = new ArrayList<>();
        for (TodoBusinessType type : TodoBusinessType.values()) {
            int i = processTodoCount(jgrybm, type);
            if (i > 0) {
                TodoBizCountVO todoBizCountVO = new TodoBizCountVO();
                todoBizCountVO.setCode(type.getCode());
                todoBizCountVO.setName(type.getDescription());
                todoBizCountVO.setTodoCount(i);
                todoBizCountVOList.add(todoBizCountVO);
            }
        }
        return todoBizCountVOList;

    }


}
