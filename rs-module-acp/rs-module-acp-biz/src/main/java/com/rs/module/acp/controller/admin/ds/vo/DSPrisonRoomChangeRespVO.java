package com.rs.module.acp.controller.admin.ds.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-数据固化-监所人员变动记录 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DSPrisonRoomChangeRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("监室id")
    private String roomId;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("入监室时间")
    private Date inRoomTime;
    @ApiModelProperty("入监室类型")
    private String inRoomType;
    @ApiModelProperty("入监室关联业务ID")
    private String inRoomBusinessId;
    @ApiModelProperty("in_room_reason")
    private String inRoomReason;
    @ApiModelProperty("出监室时间")
    private Date outRoomTime;
    @ApiModelProperty("出监室类型")
    private String outRoomType;
    @ApiModelProperty("出监室关联业务ID")
    private String outRoomBusinessId;
    @ApiModelProperty("出监室原因")
    private String outRoomReason;
    @ApiModelProperty("batch_id")
    private String batchId;
    @ApiModelProperty("共同在监室的开始时间")
    private Date cohabitationStart;
    @ApiModelProperty("共同在监室的结束时间")
    private Date cohabitationEnd;
}
