package com.rs.module.acp.entity.gj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

/**
 * 实战平台-管教业务-械具使用 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_equipment_use")
@KeySequence("acp_gj_equipment_use_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EquipmentUseDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 使用情形
     */
    private String useSituation;
    /**
     * 使用天数
     */
    private String useDays;
    /**
     * 械具种类
     */
    private String punishmentToolType;
    /**
     * 使用械具的理由
     */
    private String useReason;
    /**
     * 备注
     */
    private String remark;
    /**
     * 开始日期
     */
    private Date startTime;
    /**
     * 结束日期
     */
    private Date endTime;
    /**
     * 执行情况
     */
    private String executeSituation;
    /**
     * 使用登记时间
     */
    private Date useRegTime;
    /**
     * 使用登记人
     */
    private String useRegUser;
    /**
     * 是否提前解除
     */
    private Integer isRemove;
    /**
     * 延长时间
     */
    private Date extendTime;
    /**
     * 解除日期
     */
    private Date removeTime;
    /**
     * 审批人身份证号
     */
    private String approverSfzh;
    /**
     * 状态
     */
    private String status;
    /**
     * 审批人姓名
     */
    private String approverXm;
    /**
     * 审批时间
     */
    private Date approverTime;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 审批人签名
     */
    private String approvalAutograph;
    /**
     * 审批人签名日期
     */
    private Date approvalAutographTime;
    /**
     * 审核意见
     */
    private String approvalComments;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

    //是否关联惩罚
    private Integer isAssociatedPunishment;

    //惩罚措施
    private String punishmentMeasures;

    @ApiModelProperty("是否临时固定")
    private Integer isTempFixation;

    @ApiModelProperty("固定天数")
    private String fixationDays;

    @ApiModelProperty("实际开始时间")
    private Date actualStartTime;

    @ApiModelProperty("实际结束时间")
    private Date actualEndTime;

}
