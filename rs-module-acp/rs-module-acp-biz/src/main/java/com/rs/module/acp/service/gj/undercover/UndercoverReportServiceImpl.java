package com.rs.module.acp.service.gj.undercover;

import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverReportListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverReportPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverReportSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.UndercoverReportDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.UndercoverReportDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-耳目反映情况 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UndercoverReportServiceImpl extends BaseServiceImpl<UndercoverReportDao, UndercoverReportDO> implements UndercoverReportService {

    @Resource
    private UndercoverReportDao undercoverReportDao;

    @Override
    public String createUndercoverReport(UndercoverReportSaveReqVO createReqVO) {
        // 插入
        UndercoverReportDO undercoverReport = BeanUtils.toBean(createReqVO, UndercoverReportDO.class);
        undercoverReportDao.insert(undercoverReport);
        // 返回
        return undercoverReport.getId();
    }

    @Override
    public void updateUndercoverReport(UndercoverReportSaveReqVO updateReqVO) {
        // 校验存在
        validateUndercoverReportExists(updateReqVO.getId());
        // 更新
        UndercoverReportDO updateObj = BeanUtils.toBean(updateReqVO, UndercoverReportDO.class);
        undercoverReportDao.updateById(updateObj);
    }

    @Override
    public void deleteUndercoverReport(String id) {
        // 校验存在
        validateUndercoverReportExists(id);
        // 删除
        undercoverReportDao.deleteById(id);
    }

    private void validateUndercoverReportExists(String id) {
        if (undercoverReportDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-耳目反映情况数据不存在");
        }
    }

    @Override
    public UndercoverReportDO getUndercoverReport(String id) {
        return undercoverReportDao.selectById(id);
    }

    @Override
    public PageResult<UndercoverReportDO> getUndercoverReportPage(UndercoverReportPageReqVO pageReqVO) {
        return undercoverReportDao.selectPage(pageReqVO);
    }

    @Override
    public List<UndercoverReportDO> getUndercoverReportList(UndercoverReportListReqVO listReqVO) {
        return undercoverReportDao.selectList(listReqVO);
    }


}
