package com.rs.module.acp.service.gj.transitionroom;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.cons.CommonConstants;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.transitionroom.TransitionRoomExtendSaveReqVO;
import com.rs.module.acp.dao.gj.TransitionRoomDao;
import com.rs.module.acp.dao.gj.TransitionRoomExtendDao;
import com.rs.module.acp.entity.gj.TransitionRoomDO;
import com.rs.module.acp.entity.gj.TransitionRoomExtendDO;
import com.rs.module.acp.enums.gj.TransitionRoomStatusEnum;
import com.rs.module.base.util.BspApprovalUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 实战平台-管教业务-过渡监室延长呈批 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class TransitionRoomExtendServiceImpl extends BaseServiceImpl<TransitionRoomExtendDao, TransitionRoomExtendDO> implements TransitionRoomExtendService {

    @Resource
    private TransitionRoomExtendDao transitionRoomExtendDao;

    private final String defKey = "guanjiaoguodujianshiyanchangliucheng";

    @Resource
    private TransitionRoomDao transitionRoomDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createTransitionRoomExtend(TransitionRoomExtendSaveReqVO createReqVO) {

        TransitionRoomDO roomDO = transitionRoomDao.selectById(createReqVO.getTransitionRoomId());
        Assert.notNull(roomDO, "记录不存在！");

        TransitionRoomExtendDO entity = BeanUtils.toBean(createReqVO, TransitionRoomExtendDO.class);
        entity.setStatus(TransitionRoomStatusEnum.YCGDSPZ.getCode());
        roomDO.setStatus(TransitionRoomStatusEnum.YCGDSPZ.getCode());
        roomDO.setIsExtend(1);
        transitionRoomExtendDao.insert(entity);

        //待前端-提供
        String msgUrl = StrUtil.format("/#/discipline/transJailRoom/index?curId={}&saveType=approve", entity.getId());
        MapBuilder<String, Object> variables = MapUtil.builder();
        variables.put("ywbh", entity.getTransitionRoomId()).put("busType", MsgBusTypeEnum.GJ_GDJS.getCode());
        JSONObject result = BspApprovalUtil.commonStartProcess(defKey, entity.getId(), "【审批】延长过渡审批", msgUrl, variables.build(), HttpUtils.getAppCode());
        log.info("延长过渡审批result:{}", result);

        if(result.getIntValue("code") != HttpStatus.OK.value()){
            throw new ServerException("流程启动失败");
        }

        JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
        entity.setActInstId(bpmTrail.getString("actInstId"));
        entity.setTaskId(bpmTrail.getString("taskId"));
        transitionRoomExtendDao.updateById(entity);
        transitionRoomDao.updateById(roomDO);

        return entity.getId();
    }

    @Override
    public TransitionRoomExtendDO getTransitionRoomExtend(String id) {
        return transitionRoomExtendDao.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean apprTransitionRoomExtend(GjApproveReqVO approveReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        TransitionRoomDO entity = transitionRoomDao.selectById(approveReqVO.getId());
        Assert.notNull(entity, "记录不存在！");

        TransitionRoomStatusEnum statusEnum = TransitionRoomStatusEnum.getByCode(entity.getStatus());
        if (!TransitionRoomStatusEnum.YCGDSPZ.getCode().equals(statusEnum.getCode())) {
            throw new ServerException("该状态[" + statusEnum.getName() + "]不允许审批！");
        }

        List<TransitionRoomExtendDO> punishmentExtendDOList = transitionRoomExtendDao.selectList(new LambdaQueryWrapper<TransitionRoomExtendDO>()
                .eq(TransitionRoomExtendDO::getStatus,TransitionRoomStatusEnum.YCGDSPZ.getCode()).eq(TransitionRoomExtendDO::getTransitionRoomId, approveReqVO.getId()).orderByDesc(TransitionRoomExtendDO::getUpdateTime));
        Assert.notEmpty(punishmentExtendDOList, "未发起过延长申请" );
        TransitionRoomExtendDO extendDO = punishmentExtendDOList.get(0);

        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(extendDO.getTaskId(), sessionUser.getIdCard());
        Assert.isTrue(isApproval, "当前人无审批权限" );


        String approvalResult = approveReqVO.getApprovalResult();
        BspApproceStatusEnum bspApproceStatusEnum;
        String status = TransitionRoomStatusEnum.GDZ.getCode();
        if (String.valueOf( BspApproceStatusEnum.PASSED_END.getCode()).equals(approvalResult)) {
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED_END;
        } else {
            bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
            //判断时间，判断是否超期
            if(entity.getEndDate().getTime() > new Date().getTime() ){
                status = TransitionRoomStatusEnum.GDZ.getCode();
            } else {
                status = TransitionRoomStatusEnum.YCQDJC.getCode();
            }
        }

        //延长天数
        if(extendDO.getExtendDay() != null && BspApproceStatusEnum.PASSED_END.equals(bspApproceStatusEnum)){
            entity.setEndDate(DateUtil.offsetDay(entity.getEndDate(), extendDO.getExtendDay() ));
            entity.setActualEndDate(DateUtil.offsetDay(entity.getEndDate(), extendDO.getExtendDay() ));
        }

        entity.setStatus(status);
        transitionRoomDao.updateById( entity);

        extendDO.setApprovalComments(approveReqVO.getApprovalComments());
        extendDO.setApproverXm(sessionUser.getName());
        extendDO.setApproverSfzh(sessionUser.getIdCard());
        extendDO.setApprovalResult(approveReqVO.getApprovalResult());
        extendDO.setApproverTime(new Date());
        extendDO.setStatus(TransitionRoomStatusEnum.YSP.getCode());

        JSONObject result = BspApprovalUtil.approvalProcessAcp(defKey,
                extendDO.getActInstId(),
                extendDO.getTaskId(),
                extendDO.getId(),
                bspApproceStatusEnum,
                approveReqVO.getApprovalComments()  );
        log.info("=======result:{}", result);
        if(result.getIntValue("code") != HttpStatus.OK.value()){
            throw new ServerException("流程审批失败");
        }

        JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
        extendDO.setTaskId(bpmTrail.getString("taskId"));
        transitionRoomExtendDao.updateById(extendDO);
        return true;
    }

}
