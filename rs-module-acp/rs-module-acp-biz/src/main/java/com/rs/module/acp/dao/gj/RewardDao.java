package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.reward.RewardListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.reward.RewardPageReqVO;
import com.rs.module.acp.entity.gj.RewardDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-奖励管理 Dao
*
* <AUTHOR>
*/
@Mapper
public interface RewardDao extends IBaseDao<RewardDO> {


    default PageResult<RewardDO> selectPage(RewardPageReqVO reqVO) {
        Page<RewardDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<RewardDO> wrapper = new LambdaQueryWrapperX<RewardDO>()
            .eqIfPresent(RewardDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(RewardDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(RewardDO::getRewardType, reqVO.getRewardType())
            .eqIfPresent(RewardDO::getRewardReason, reqVO.getRewardReason())
            .eqIfPresent(RewardDO::getRewardContent, reqVO.getRewardContent())
            .eqIfPresent(RewardDO::getRemark, reqVO.getRemark())
            .eqIfPresent(RewardDO::getExecutor, reqVO.getExecutor())
            .eqIfPresent(RewardDO::getExecutorSfzh, reqVO.getExecutorSfzh())
            .betweenIfPresent(RewardDO::getExecutionTime, reqVO.getExecutionTime())
            .eqIfPresent(RewardDO::getExecuteSituation, reqVO.getExecuteSituation())
            .eqIfPresent(RewardDO::getStatus, reqVO.getStatus())
            .eqIfPresent(RewardDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(RewardDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(RewardDO::getAddTime);
        }
        Page<RewardDO> rewardPage = selectPage(page, wrapper);
        return new PageResult<>(rewardPage.getRecords(), rewardPage.getTotal());
    }
    default List<RewardDO> selectList(RewardListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<RewardDO>()
            .eqIfPresent(RewardDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(RewardDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(RewardDO::getRewardType, reqVO.getRewardType())
            .eqIfPresent(RewardDO::getRewardReason, reqVO.getRewardReason())
            .eqIfPresent(RewardDO::getRewardContent, reqVO.getRewardContent())
            .eqIfPresent(RewardDO::getRemark, reqVO.getRemark())
            .eqIfPresent(RewardDO::getExecutor, reqVO.getExecutor())
            .eqIfPresent(RewardDO::getExecutorSfzh, reqVO.getExecutorSfzh())
            .betweenIfPresent(RewardDO::getExecutionTime, reqVO.getExecutionTime())
            .eqIfPresent(RewardDO::getExecuteSituation, reqVO.getExecuteSituation())
            .eqIfPresent(RewardDO::getStatus, reqVO.getStatus())
            .eqIfPresent(RewardDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(RewardDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(RewardDO::getAddTime));    }


    }
