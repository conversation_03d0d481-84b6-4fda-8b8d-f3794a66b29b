package com.rs.module.acp.service.zh.shiftteam;

import javax.validation.*;

import com.rs.module.acp.controller.admin.zh.vo.shiftteam.StaffDutyTeamPersonSaveReqVO;
import com.rs.module.acp.entity.zh.StaffDutyTeamPersonDO;
import com.bsp.common.orm.mybatis.service.IBaseService;

import java.util.List;

/**
 * 值班模板班组人员信息 Service 接口
 *
 * <AUTHOR>
 */
public interface StaffDutyTeamPersonService extends IBaseService<StaffDutyTeamPersonDO>{

    /**
     * 创建值班模板班组人员信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createStaffDutyTeamPerson(@Valid StaffDutyTeamPersonSaveReqVO createReqVO);

    /**
     * 更新值班模板班组人员信息
     *
     * @param updateReqVO 更新信息
     */
    void updateStaffDutyTeamPerson(@Valid StaffDutyTeamPersonSaveReqVO updateReqVO);

    /**
     * 删除值班模板班组人员信息
     *
     * @param id 编号
     */
    void deleteStaffDutyTeamPerson(String id);

    /**
     * 获得值班模板班组人员信息
     *
     * @param id 编号
     * @return 值班模板班组人员信息
     */
    StaffDutyTeamPersonDO getStaffDutyTeamPerson(String id);


    void createOrUpdateBatch(String teamId, List<StaffDutyTeamPersonSaveReqVO> personInfos);

    void createOrUpdateBatch(List<StaffDutyTeamPersonSaveReqVO> personInfos);

    List<StaffDutyTeamPersonDO> selectByTeamId(String teamId);

    List<StaffDutyTeamPersonDO> selectByOrgCodeTeamIdList(String orgCode,List<String> teamIdList);
}
