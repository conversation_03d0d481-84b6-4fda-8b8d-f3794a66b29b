package com.rs.module.acp.service.zh;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.cache.DicUtil;
import com.bsp.common.util.SettingUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.acp.controller.admin.zh.vo.MeetingRecordsSaveReqVO;
import com.rs.util.DateUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.rs.module.acp.entity.zh.MeetingRecordsDO;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.zh.MeetingRecordsDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;

import java.util.Date;
import java.util.List;


/**
 * 实战平台-综合管理-会议记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MeetingRecordsServiceImpl extends BaseServiceImpl<MeetingRecordsDao, MeetingRecordsDO> implements MeetingRecordsService {

    @Resource
    private MeetingRecordsDao meetingRecordsDao;

    @Override
    public String createMeetingRecords(MeetingRecordsSaveReqVO reqVO) {
        // 插入
        MeetingRecordsDO entity = BeanUtils.toBean(reqVO, MeetingRecordsDO.class);
        initWsInfo(entity);
        meetingRecordsDao.insert(entity);
        // 返回
        return entity.getId();
    }

    @Override
    public void updateMeetingRecords(MeetingRecordsSaveReqVO reqVO) {
        // 校验存在
        validateMeetingRecordsExists(reqVO.getId());
        // 更新
        MeetingRecordsDO entity = BeanUtils.toBean(reqVO, MeetingRecordsDO.class);
        if(StringUtil.isEmpty(entity.getWszh())){
            initWsInfo(entity);
        }
        meetingRecordsDao.updateById(entity);
    }

    @Override
    public void deleteMeetingRecords(String id) {
        // 校验存在
        validateMeetingRecordsExists(id);
        // 删除
        meetingRecordsDao.deleteById(id);
    }

    private void validateMeetingRecordsExists(String id) {
        if (meetingRecordsDao.selectById(id) == null) {
            throw new ServerException("实战平台-综合管理-会议记录数据不存在");
        }
    }

    @Override
    public MeetingRecordsDO getMeetingRecords(String id) {
        return meetingRecordsDao.selectById(id);
    }
    private void initWsInfo(MeetingRecordsDO entity){
        Integer maxWsh = meetingRecordsDao.getNowYeaMaxWsh(entity.getCategoryId());
        String startWsh = SettingUtil.getParamKey("MEETING_RECORD_WSH", HttpUtils.getAppCode());
        if (StringUtil.isNotEmpty(startWsh) && maxWsh == null){
            maxWsh = Integer.parseInt(startWsh);
        }
        if (maxWsh == null) maxWsh = 0;
        maxWsh += 1;
        String wsh = String.format("%04d", maxWsh);
        //生成流水号
        entity.setWsh(wsh);
        String categoryName = DicUtil.translate("ZD_JLS_HYLX", entity.getCategoryId());
        if(StringUtil.isEmpty(entity.getOrgCode())) entity.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        Date date = null == entity.getAddTime() ? entity.getMeetingStartTime() : entity.getAddTime();
        entity.setWszh(buildWszh(entity.getOrgCode(),date,categoryName,wsh));
    }
    public static String buildWszh(String orgCode, Date approverTime, String categoryName,String wsh){
        String year = DateUtil.format(approverTime, "yyyy");
        String orgWsjc = DicUtil.translate("ZD-JGJC", orgCode);
        return String.format("%s[%s]%s第%s号", orgWsjc, year,categoryName, wsh);
    }

    /**
     * 查询指定会议类型及人员编码的会议记录
     * @param categoryId
     * @param jgrybm
     * @return
     */
    @Override
    public List<MeetingRecordsDO> getListMeetingRecordsByCategoryId(String categoryId,String jgrybm){
        LambdaQueryWrapper<MeetingRecordsDO> wrapper = new LambdaQueryWrapper<MeetingRecordsDO>()
                .eq(MeetingRecordsDO::getCategoryId, categoryId)
                .eq(MeetingRecordsDO::getIsDel, 0);
        if(StringUtil.isNotEmpty(jgrybm)) wrapper.in(MeetingRecordsDO::getJgrybms, jgrybm);
                return meetingRecordsDao.selectList(wrapper);
    }
}
