package com.rs.module.acp.dao.wb;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.ConsularMeetingDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-窗口业务-领事会见登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ConsularMeetingDao extends IBaseDao<ConsularMeetingDO> {



    default PageResult<ConsularMeetingDO> selectPage(ConsularMeetingPageReqVO reqVO) {
        Page<ConsularMeetingDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ConsularMeetingDO> wrapper = new LambdaQueryWrapperX<ConsularMeetingDO>()
                .eqIfPresent(ConsularMeetingDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(ConsularMeetingDO::getMeetingMethod, reqVO.getMeetingMethod())
                .eqIfPresent(ConsularMeetingDO::getRoomId, reqVO.getRoomId())
                .eqIfPresent(ConsularMeetingDO::getArraignmentLetterNumber, reqVO.getArraignmentLetterNumber())
                .eqIfPresent(ConsularMeetingDO::getRemarks, reqVO.getRemarks())
                .eqIfPresent(ConsularMeetingDO::getAppointmentTimeSlot, reqVO.getAppointmentTimeSlot())
                .betweenIfPresent(ConsularMeetingDO::getAppointmentTime, reqVO.getAppointmentTime())
                .eqIfPresent(ConsularMeetingDO::getMeetingDocumentsUrl, reqVO.getMeetingDocumentsUrl())
                .eqIfPresent(ConsularMeetingDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(ConsularMeetingDO::getAssignmentRoomTime, reqVO.getAssignmentRoomTime())
                .eqIfPresent(ConsularMeetingDO::getAssignmentPoliceSfzh, reqVO.getAssignmentPoliceSfzh())
                .eqIfPresent(ConsularMeetingDO::getAssignmentPolice, reqVO.getAssignmentPolice())
                .betweenIfPresent(ConsularMeetingDO::getCheckInTime, reqVO.getCheckInTime())
                .eqIfPresent(ConsularMeetingDO::getCheckInPoliceSfzh, reqVO.getCheckInPoliceSfzh())
                .eqIfPresent(ConsularMeetingDO::getCheckInPolice, reqVO.getCheckInPolice())
                .eqIfPresent(ConsularMeetingDO::getEscortingPoliceSfzh, reqVO.getEscortingPoliceSfzh())
                .eqIfPresent(ConsularMeetingDO::getEscortingPolice, reqVO.getEscortingPolice())
                .betweenIfPresent(ConsularMeetingDO::getEscortingTime, reqVO.getEscortingTime())
                .eqIfPresent(ConsularMeetingDO::getEscortingOperatorSfzh, reqVO.getEscortingOperatorSfzh())
                .eqIfPresent(ConsularMeetingDO::getEscortingOperator, reqVO.getEscortingOperator())
                .betweenIfPresent(ConsularMeetingDO::getEscortingOperatorTime, reqVO.getEscortingOperatorTime())
                .eqIfPresent(ConsularMeetingDO::getInspectionResult, reqVO.getInspectionResult())
                .eqIfPresent(ConsularMeetingDO::getProhibitedItems, reqVO.getProhibitedItems())
                .eqIfPresent(ConsularMeetingDO::getPhysicalExam, reqVO.getPhysicalExam())
                .eqIfPresent(ConsularMeetingDO::getAbnormalSituations, reqVO.getAbnormalSituations())
                .eqIfPresent(ConsularMeetingDO::getProhibitedItemsImgUrl, reqVO.getProhibitedItemsImgUrl())
                .eqIfPresent(ConsularMeetingDO::getPhysicalExamImgUrl, reqVO.getPhysicalExamImgUrl())
                .eqIfPresent(ConsularMeetingDO::getAbnormalSituationsImgUrl, reqVO.getAbnormalSituationsImgUrl())
                .eqIfPresent(ConsularMeetingDO::getIsProhibitedItems, reqVO.getIsProhibitedItems())
                .betweenIfPresent(ConsularMeetingDO::getInspectionTime, reqVO.getInspectionTime())
                .eqIfPresent(ConsularMeetingDO::getInspectorSfzh, reqVO.getInspectorSfzh())
                .eqIfPresent(ConsularMeetingDO::getInspector, reqVO.getInspector())
                .betweenIfPresent(ConsularMeetingDO::getMeetingStartTime, reqVO.getMeetingStartTime())
                .betweenIfPresent(ConsularMeetingDO::getMeetingEndTime, reqVO.getMeetingEndTime())
                .eqIfPresent(ConsularMeetingDO::getReturnInspectorSfzh, reqVO.getReturnInspectorSfzh())
                .eqIfPresent(ConsularMeetingDO::getReturnInspector, reqVO.getReturnInspector())
                .betweenIfPresent(ConsularMeetingDO::getReturnInspectionTime, reqVO.getReturnInspectionTime())
                .eqIfPresent(ConsularMeetingDO::getReturnInspectionResult, reqVO.getReturnInspectionResult())
                .betweenIfPresent(ConsularMeetingDO::getReturnTime, reqVO.getReturnTime())
                .eqIfPresent(ConsularMeetingDO::getReturnPoliceSfzh, reqVO.getReturnPoliceSfzh())
                .eqIfPresent(ConsularMeetingDO::getReturnPolice, reqVO.getReturnPolice())
                .eqIfPresent(ConsularMeetingDO::getReturnOperatorSfzh, reqVO.getReturnOperatorSfzh())
                .eqIfPresent(ConsularMeetingDO::getReturnOperator, reqVO.getReturnOperator())
                .betweenIfPresent(ConsularMeetingDO::getReturnOperatorTime, reqVO.getReturnOperatorTime())
                .betweenIfPresent(ConsularMeetingDO::getApplyMeetingStartTime, reqVO.getApplyMeetingStartTime())
                .betweenIfPresent(ConsularMeetingDO::getApplyMeetingEndTime, reqVO.getApplyMeetingEndTime())
                .eqIfPresent(ConsularMeetingDO::getReturnProhibitedItems, reqVO.getReturnProhibitedItems())
                .eqIfPresent(ConsularMeetingDO::getReturnPhysicalExam, reqVO.getReturnPhysicalExam())
                .eqIfPresent(ConsularMeetingDO::getReturnAbnormalSituations, reqVO.getReturnAbnormalSituations())
                ;
        if(reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        }else {
            wrapper.orderByDesc(ConsularMeetingDO::getAddTime);
        }
        Page<ConsularMeetingDO> consularMeetingPage = selectPage(page, wrapper);
        return new PageResult<>(consularMeetingPage.getRecords(), consularMeetingPage.getTotal());
    }
    default List<ConsularMeetingDO> selectList(ConsularMeetingListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ConsularMeetingDO>()
                .eqIfPresent(ConsularMeetingDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(ConsularMeetingDO::getMeetingMethod, reqVO.getMeetingMethod())
                .eqIfPresent(ConsularMeetingDO::getRoomId, reqVO.getRoomId())
                .eqIfPresent(ConsularMeetingDO::getArraignmentLetterNumber, reqVO.getArraignmentLetterNumber())
                .eqIfPresent(ConsularMeetingDO::getRemarks, reqVO.getRemarks())
                .eqIfPresent(ConsularMeetingDO::getAppointmentTimeSlot, reqVO.getAppointmentTimeSlot())
                .betweenIfPresent(ConsularMeetingDO::getAppointmentTime, reqVO.getAppointmentTime())
                .eqIfPresent(ConsularMeetingDO::getMeetingDocumentsUrl, reqVO.getMeetingDocumentsUrl())
                .eqIfPresent(ConsularMeetingDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(ConsularMeetingDO::getAssignmentRoomTime, reqVO.getAssignmentRoomTime())
                .eqIfPresent(ConsularMeetingDO::getAssignmentPoliceSfzh, reqVO.getAssignmentPoliceSfzh())
                .eqIfPresent(ConsularMeetingDO::getAssignmentPolice, reqVO.getAssignmentPolice())
                .betweenIfPresent(ConsularMeetingDO::getCheckInTime, reqVO.getCheckInTime())
                .eqIfPresent(ConsularMeetingDO::getCheckInPoliceSfzh, reqVO.getCheckInPoliceSfzh())
                .eqIfPresent(ConsularMeetingDO::getCheckInPolice, reqVO.getCheckInPolice())
                .eqIfPresent(ConsularMeetingDO::getEscortingPoliceSfzh, reqVO.getEscortingPoliceSfzh())
                .eqIfPresent(ConsularMeetingDO::getEscortingPolice, reqVO.getEscortingPolice())
                .betweenIfPresent(ConsularMeetingDO::getEscortingTime, reqVO.getEscortingTime())
                .eqIfPresent(ConsularMeetingDO::getEscortingOperatorSfzh, reqVO.getEscortingOperatorSfzh())
                .eqIfPresent(ConsularMeetingDO::getEscortingOperator, reqVO.getEscortingOperator())
                .betweenIfPresent(ConsularMeetingDO::getEscortingOperatorTime, reqVO.getEscortingOperatorTime())
                .eqIfPresent(ConsularMeetingDO::getInspectionResult, reqVO.getInspectionResult())
                .eqIfPresent(ConsularMeetingDO::getProhibitedItems, reqVO.getProhibitedItems())
                .eqIfPresent(ConsularMeetingDO::getPhysicalExam, reqVO.getPhysicalExam())
                .eqIfPresent(ConsularMeetingDO::getAbnormalSituations, reqVO.getAbnormalSituations())
                .eqIfPresent(ConsularMeetingDO::getProhibitedItemsImgUrl, reqVO.getProhibitedItemsImgUrl())
                .eqIfPresent(ConsularMeetingDO::getPhysicalExamImgUrl, reqVO.getPhysicalExamImgUrl())
                .eqIfPresent(ConsularMeetingDO::getAbnormalSituationsImgUrl, reqVO.getAbnormalSituationsImgUrl())
                .eqIfPresent(ConsularMeetingDO::getIsProhibitedItems, reqVO.getIsProhibitedItems())
                .betweenIfPresent(ConsularMeetingDO::getInspectionTime, reqVO.getInspectionTime())
                .eqIfPresent(ConsularMeetingDO::getInspectorSfzh, reqVO.getInspectorSfzh())
                .eqIfPresent(ConsularMeetingDO::getInspector, reqVO.getInspector())
                .betweenIfPresent(ConsularMeetingDO::getMeetingStartTime, reqVO.getMeetingStartTime())
                .betweenIfPresent(ConsularMeetingDO::getMeetingEndTime, reqVO.getMeetingEndTime())
                .eqIfPresent(ConsularMeetingDO::getReturnInspectorSfzh, reqVO.getReturnInspectorSfzh())
                .eqIfPresent(ConsularMeetingDO::getReturnInspector, reqVO.getReturnInspector())
                .betweenIfPresent(ConsularMeetingDO::getReturnInspectionTime, reqVO.getReturnInspectionTime())
                .eqIfPresent(ConsularMeetingDO::getReturnInspectionResult, reqVO.getReturnInspectionResult())
                .betweenIfPresent(ConsularMeetingDO::getReturnTime, reqVO.getReturnTime())
                .eqIfPresent(ConsularMeetingDO::getReturnPoliceSfzh, reqVO.getReturnPoliceSfzh())
                .eqIfPresent(ConsularMeetingDO::getReturnPolice, reqVO.getReturnPolice())
                .eqIfPresent(ConsularMeetingDO::getReturnOperatorSfzh, reqVO.getReturnOperatorSfzh())
                .eqIfPresent(ConsularMeetingDO::getReturnOperator, reqVO.getReturnOperator())
                .betweenIfPresent(ConsularMeetingDO::getReturnOperatorTime, reqVO.getReturnOperatorTime())
                .betweenIfPresent(ConsularMeetingDO::getApplyMeetingStartTime, reqVO.getApplyMeetingStartTime())
                .betweenIfPresent(ConsularMeetingDO::getApplyMeetingEndTime, reqVO.getApplyMeetingEndTime())
                .eqIfPresent(ConsularMeetingDO::getReturnProhibitedItems, reqVO.getReturnProhibitedItems())
                .eqIfPresent(ConsularMeetingDO::getReturnPhysicalExam, reqVO.getReturnPhysicalExam())
                .eqIfPresent(ConsularMeetingDO::getReturnAbnormalSituations, reqVO.getReturnAbnormalSituations())
                .orderByDesc(ConsularMeetingDO::getAddTime));    }


    }
