package com.rs.module.acp.service.gj.reward;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.reward.*;
import com.rs.module.acp.entity.gj.RewardDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-管教业务-奖励管理 Service 接口
 *
 * <AUTHOR>
 */
public interface RewardService extends IBaseService<RewardDO>{

    /**
     * 创建实战平台-管教业务-奖励管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createReward(@Valid RewardSaveReqVO createReqVO);


    /**
     * 获得实战平台-管教业务-奖励管理
     *
     * @param id 编号
     * @return 实战平台-管教业务-奖励管理
     */
    RewardRespVO getReward(String id);

    /**
    * 获得实战平台-管教业务-奖励管理分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-奖励管理分页
    */
    PageResult<RewardDO> getRewardPage(RewardPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-奖励管理列表
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-奖励管理列表
    */
    List<RewardDO> getRewardList(RewardListReqVO listReqVO);

    /**
     * 奖励登记
     * <AUTHOR>
     * @date 2025/7/5 16:59
     * @param [regInfoReqVO]
     * @return void
     */
    void regInfo(RewardRegInfoReqVO regInfoReqVO);

    /**
     * 领导审批
     * <AUTHOR>
     * @date 2025/7/5 17:00
     * @param [approveReqVO]
     * @return void
     */
    void leaderApprove(GjApproveReqVO approveReqVO);
}
