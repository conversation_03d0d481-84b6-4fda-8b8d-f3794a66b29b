package com.rs.module.acp.dao.gj;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.prisonroom.PrisonRoomChangeListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.prisonroom.PrisonRoomChangePageReqVO;
import com.rs.module.acp.controller.app.gj.vo.prisonroom.PrisonRoomChangeAppListVO;
import com.rs.module.acp.entity.gj.PrisonRoomChangeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* 实战平台-管教业务--监室调整 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PrisonRoomChangeDao extends IBaseDao<PrisonRoomChangeDO> {


    default PageResult<PrisonRoomChangeDO> selectPage(PrisonRoomChangePageReqVO reqVO) {
        Page<PrisonRoomChangeDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrisonRoomChangeDO> wrapper = new LambdaQueryWrapperX<PrisonRoomChangeDO>()
            .eqIfPresent(PrisonRoomChangeDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PrisonRoomChangeDO::getOldRoomId, reqVO.getOldRoomId())
            .eqIfPresent(PrisonRoomChangeDO::getNewRoomId, reqVO.getNewRoomId())
            .likeIfPresent(PrisonRoomChangeDO::getOldRoomName, reqVO.getOldRoomName())
            .likeIfPresent(PrisonRoomChangeDO::getNewRoomName, reqVO.getNewRoomName())
            .eqIfPresent(PrisonRoomChangeDO::getChangeReason, reqVO.getChangeReason())
            .betweenIfPresent(PrisonRoomChangeDO::getRoomChangeTime, reqVO.getRoomChangeTime())
            .eqIfPresent(PrisonRoomChangeDO::getIsChange, reqVO.getIsChange())
            .eqIfPresent(PrisonRoomChangeDO::getRemark, reqVO.getRemark())
            .eqIfPresent(PrisonRoomChangeDO::getStatus, reqVO.getStatus())
            .eqIfPresent(PrisonRoomChangeDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(PrisonRoomChangeDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(PrisonRoomChangeDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(PrisonRoomChangeDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(PrisonRoomChangeDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(PrisonRoomChangeDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(PrisonRoomChangeDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(PrisonRoomChangeDO::getEscortingPoliceSfzh, reqVO.getEscortingPoliceSfzh())
            .eqIfPresent(PrisonRoomChangeDO::getEscortingPolice, reqVO.getEscortingPolice())
            .betweenIfPresent(PrisonRoomChangeDO::getEscortingTime, reqVO.getEscortingTime())
            .eqIfPresent(PrisonRoomChangeDO::getInspectionResult, reqVO.getInspectionResult())
            .eqIfPresent(PrisonRoomChangeDO::getProhibitedItems, reqVO.getProhibitedItems())
            .eqIfPresent(PrisonRoomChangeDO::getPhysicalExam, reqVO.getPhysicalExam())
            .eqIfPresent(PrisonRoomChangeDO::getAbnormalSituations, reqVO.getAbnormalSituations())
            .eqIfPresent(PrisonRoomChangeDO::getInspectorSfzh, reqVO.getInspectorSfzh())
            .eqIfPresent(PrisonRoomChangeDO::getInspector, reqVO.getInspector())
            .betweenIfPresent(PrisonRoomChangeDO::getReturnTime, reqVO.getReturnTime())
            .eqIfPresent(PrisonRoomChangeDO::getReturnPoliceSfzh, reqVO.getReturnPoliceSfzh())
            .eqIfPresent(PrisonRoomChangeDO::getReturnPolice, reqVO.getReturnPolice())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PrisonRoomChangeDO::getAddTime);
        }
        Page<PrisonRoomChangeDO> prisonRoomChangePage = selectPage(page, wrapper);
        return new PageResult<>(prisonRoomChangePage.getRecords(), prisonRoomChangePage.getTotal());
    }
    default List<PrisonRoomChangeDO> selectList(PrisonRoomChangeListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrisonRoomChangeDO>()
            .eqIfPresent(PrisonRoomChangeDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PrisonRoomChangeDO::getOldRoomId, reqVO.getOldRoomId())
            .eqIfPresent(PrisonRoomChangeDO::getNewRoomId, reqVO.getNewRoomId())
            .likeIfPresent(PrisonRoomChangeDO::getOldRoomName, reqVO.getOldRoomName())
            .likeIfPresent(PrisonRoomChangeDO::getNewRoomName, reqVO.getNewRoomName())
            .eqIfPresent(PrisonRoomChangeDO::getChangeReason, reqVO.getChangeReason())
            .betweenIfPresent(PrisonRoomChangeDO::getRoomChangeTime, reqVO.getRoomChangeTime())
            .eqIfPresent(PrisonRoomChangeDO::getIsChange, reqVO.getIsChange())
            .eqIfPresent(PrisonRoomChangeDO::getRemark, reqVO.getRemark())
            .eqIfPresent(PrisonRoomChangeDO::getStatus, reqVO.getStatus())
            .eqIfPresent(PrisonRoomChangeDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(PrisonRoomChangeDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(PrisonRoomChangeDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(PrisonRoomChangeDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(PrisonRoomChangeDO::getApprovalAutograph, reqVO.getApprovalAutograph())
            .betweenIfPresent(PrisonRoomChangeDO::getApprovalAutographTime, reqVO.getApprovalAutographTime())
            .eqIfPresent(PrisonRoomChangeDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(PrisonRoomChangeDO::getEscortingPoliceSfzh, reqVO.getEscortingPoliceSfzh())
            .eqIfPresent(PrisonRoomChangeDO::getEscortingPolice, reqVO.getEscortingPolice())
            .betweenIfPresent(PrisonRoomChangeDO::getEscortingTime, reqVO.getEscortingTime())
            .eqIfPresent(PrisonRoomChangeDO::getInspectionResult, reqVO.getInspectionResult())
            .eqIfPresent(PrisonRoomChangeDO::getProhibitedItems, reqVO.getProhibitedItems())
            .eqIfPresent(PrisonRoomChangeDO::getPhysicalExam, reqVO.getPhysicalExam())
            .eqIfPresent(PrisonRoomChangeDO::getAbnormalSituations, reqVO.getAbnormalSituations())
            .eqIfPresent(PrisonRoomChangeDO::getInspectorSfzh, reqVO.getInspectorSfzh())
            .eqIfPresent(PrisonRoomChangeDO::getInspector, reqVO.getInspector())
            .betweenIfPresent(PrisonRoomChangeDO::getReturnTime, reqVO.getReturnTime())
            .eqIfPresent(PrisonRoomChangeDO::getReturnPoliceSfzh, reqVO.getReturnPoliceSfzh())
            .eqIfPresent(PrisonRoomChangeDO::getReturnPolice, reqVO.getReturnPolice())
        .orderByDesc(PrisonRoomChangeDO::getAddTime));    }



    /**
     * 看守所
     * <AUTHOR>
     * @date 2025/6/4 20:16
     * @param [jsh, roomName, jgrybm]
     * @return int
     */
    int updatePersonRoomInfoKss(@Param("jsh") String jsh,@Param("roomName") String roomName,@Param("jgrybm")String jgrybm ,@Param("areaId")String areaId );

    /**
     * 戒毒所
     * <AUTHOR>
     * @date 2025/6/4 20:16
     * @param [jsh, roomName, jgrybm]
     * @return int
     */
    int updatePersonRoomInfoJds(@Param("jsh") String jsh,@Param("roomName") String roomName,@Param("jgrybm")String jgrybm,@Param("areaId")String areaId );

    /**
     * 拘留所
     * <AUTHOR>
     * @date 2025/6/4 20:16
     * @param [jsh, roomName, jgrybm]
     * @return int
     */
    int updatePersonRoomInfoJls(@Param("jsh") String jsh,@Param("roomName") String roomName,@Param("jgrybm")String jgrybm,@Param("areaId")String areaId  );


    /**
     * 获取监室调整APP，list信息
     * <AUTHOR>
     * @date 2025/6/18 17:32
     * @param [page, orgCode, roomId]
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.rs.module.acp.controller.app.gj.vo.prisonroom.PrisonRoomChangeAppListVO>
     */
    IPage<PrisonRoomChangeAppListVO> getPrisonRoomChangeAppList(@Param("page") Page<PrisonRoomChangeAppListVO> page,
                                                                @Param("orgCode") String orgCode,
                                                                @Param("startTime") Date startTime,
                                                                @Param("endTime") Date endTime,
                                                                @Param("roomId") String roomId);

}
