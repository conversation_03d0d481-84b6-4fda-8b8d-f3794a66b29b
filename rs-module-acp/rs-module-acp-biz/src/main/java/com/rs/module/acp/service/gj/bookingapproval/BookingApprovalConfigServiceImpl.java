package com.rs.module.acp.service.gj.bookingapproval;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalConfigListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalConfigPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalConfigRespVO;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalConfigSaveReqVO;
import com.rs.module.acp.dao.gj.BookingApprovalConfigDao;
import com.rs.module.acp.entity.gj.BookingApprovalConfigDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 实战平台-管教业务-预约审核配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BookingApprovalConfigServiceImpl extends BaseServiceImpl<BookingApprovalConfigDao, BookingApprovalConfigDO> implements BookingApprovalConfigService {

    @Resource
    private BookingApprovalConfigDao bookingApprovalConfigDao;

    @Override
    public String createBookingApprovalConfig(BookingApprovalConfigSaveReqVO createReqVO) {
        // 插入
        BookingApprovalConfigDO bookingApprovalConfig = BeanUtils.toBean(createReqVO, BookingApprovalConfigDO.class);
        bookingApprovalConfigDao.insert(bookingApprovalConfig);
        // 返回
        return bookingApprovalConfig.getId();
    }

    @Override
    public void updateBookingApprovalConfig(BookingApprovalConfigSaveReqVO updateReqVO) {
        // 校验存在
        validateBookingApprovalConfigExists(updateReqVO.getId());
        // 更新
        BookingApprovalConfigDO updateObj = BeanUtils.toBean(updateReqVO, BookingApprovalConfigDO.class);
        bookingApprovalConfigDao.updateById(updateObj);
    }

    @Override
    public void deleteBookingApprovalConfig(String id) {
        // 校验存在
        validateBookingApprovalConfigExists(id);
        // 删除
        bookingApprovalConfigDao.deleteById(id);
    }

    private void validateBookingApprovalConfigExists(String id) {
        if (bookingApprovalConfigDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-预约审核配置数据不存在");
        }
    }

    @Override
    public BookingApprovalConfigDO getBookingApprovalConfig(String id) {
        return bookingApprovalConfigDao.selectById(id);
    }

    @Override
    public PageResult<BookingApprovalConfigDO> getBookingApprovalConfigPage(BookingApprovalConfigPageReqVO pageReqVO) {
        return bookingApprovalConfigDao.selectPage(pageReqVO);
    }

    @Override
    public List<BookingApprovalConfigRespVO> getBookingApprovalConfigList(BookingApprovalConfigListReqVO listReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        listReqVO.setOrgCode( sessionUser.getOrgCode());
        List<BookingApprovalConfigDO> list = bookingApprovalConfigDao.selectList(listReqVO);
        List<BookingApprovalConfigRespVO> respVOList  = null;
        List<String> arrayList = Arrays.asList("11","12","21","22","31");
        if(CollUtil.isNotEmpty(list)){
            respVOList = BeanUtils.toBean(list, BookingApprovalConfigRespVO.class);
            Map<String, BookingApprovalConfigRespVO> map =  respVOList.stream()
                    .collect(Collectors.toMap(BookingApprovalConfigRespVO::getServiceCategory, Function.identity()));
            List<BookingApprovalConfigRespVO> respVOListNew = new ArrayList<>();
            for (String s : arrayList) {
                BookingApprovalConfigRespVO vo =   map.get(s);
                if(vo != null){
                    respVOListNew.add(vo);
                } else {
                    respVOListNew.add(new BookingApprovalConfigRespVO(IdUtil.fastSimpleUUID(),s.substring(0,1) + "0",s, 1, null));
                }
            }
            return respVOListNew;
        }
        //为空的话，初始化基础配置数据
        respVOList =  new ArrayList<>();
        respVOList.add( new BookingApprovalConfigRespVO(IdUtil.fastSimpleUUID(),"10","11", 1, null));
        respVOList.add( new BookingApprovalConfigRespVO(IdUtil.fastSimpleUUID(),"10","12", 1, null));
        respVOList.add( new BookingApprovalConfigRespVO(IdUtil.fastSimpleUUID(),"20","21", 1, null));
        respVOList.add( new BookingApprovalConfigRespVO(IdUtil.fastSimpleUUID(),"20","22", 1, null));
        respVOList.add( new BookingApprovalConfigRespVO(IdUtil.fastSimpleUUID(),"30","31", 1, null));
        return respVOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveOrUpdate(List<BookingApprovalConfigSaveReqVO> createReqVOList) {
        createReqVOList.forEach( e->{
            BookingApprovalConfigDO bookingApprovalConfig = BeanUtils.toBean(e, BookingApprovalConfigDO.class);
            BookingApprovalConfigDO tempDO = bookingApprovalConfigDao.selectById(e.getId());
            if(tempDO == null){
                // 插入
                bookingApprovalConfigDao.insert(bookingApprovalConfig);
            } else {
                BeanUtils.copyProperties(e, tempDO);
                bookingApprovalConfigDao.updateById(tempDO);
            }
        });
        return true;
    }

}
