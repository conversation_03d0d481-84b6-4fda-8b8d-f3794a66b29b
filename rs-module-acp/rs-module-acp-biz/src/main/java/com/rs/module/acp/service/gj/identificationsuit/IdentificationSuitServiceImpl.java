package com.rs.module.acp.service.gj.identificationsuit;

import com.rs.framework.common.exception.ServerException;
import com.rs.framework.mybatis.util.BspDbUtil;
import com.rs.module.acp.controller.admin.gj.vo.identificationsuit.IdentificationSuitSaveReqVO;
import com.rs.module.acp.dao.gj.IdentificationSuitDao;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.enums.BjgrylxEnum;
import com.rs.module.base.enums.PrisonerQueryRyztEnum;
import com.rs.module.base.service.pm.PrisonerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * 实战平台-管教业务-识别服管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class IdentificationSuitServiceImpl implements IdentificationSuitService {

    @Resource
    private IdentificationSuitDao identificationSuitDao;

    @Resource
    private PrisonerService prisonerService;


    @Override
    public List<String> getSuitNumByRoomId(String roomId) {
        int maxSuitNum = 50;
        try {
            maxSuitNum = Integer.parseInt(BspDbUtil.getParam("SUIT_NUM"));
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        List<String> suitNumList = new ArrayList<>();
        for (int i = 1; i <= maxSuitNum; i++) {
            suitNumList.add(String.format("%02d", i));
        }
        List<String> usedSuitNumList = identificationSuitDao.getUsedSuitNumByRoomId(roomId);
        suitNumList.removeAll(usedSuitNumList);
        return suitNumList;
    }

    @Override
    public void editByJgrybm(IdentificationSuitSaveReqVO createReqVO) {
        PrisonerVwRespVO prisonerInVwRespVO = prisonerService.getPrisonerSelectCompomenOne(createReqVO.getJgrybm(), PrisonerQueryRyztEnum.ZS);
        if (Objects.isNull(prisonerInVwRespVO)) {
            throw new ServerException("该人员不存在");
        }
        String tableName;
        if (BjgrylxEnum.ZYRY.getCode().equals(prisonerInVwRespVO.getBjgrylx())) {
            tableName = "acp_pm_prisoner_kss_in";
        } else if (BjgrylxEnum.ZJRY.getCode().equals(prisonerInVwRespVO.getBjgrylx())) {
            tableName = "acp_pm_prisoner_jls_in";
        } else if (BjgrylxEnum.JDRY.getCode().equals(prisonerInVwRespVO.getBjgrylx())) {
            tableName = "acp_pm_prisoner_jds_in";
        } else {
            throw new ServerException("非法监管人员类型");
        }
        identificationSuitDao.updateSbfhByJgrybm(createReqVO.getJgrybm(), createReqVO.getSuitNum(), createReqVO.getSuitColor(), tableName);

    }


}
