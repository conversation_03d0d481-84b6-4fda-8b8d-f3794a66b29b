package com.rs.module.acp.controller.admin.db.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "看守所收押登记办案人信息VO")
@Data
public class DetainRegKssPoliceVO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("办案人")
    private String bar;

    @ApiModelProperty("办案人联系方式")
    private String barlxff;

    @ApiModelProperty("办案人性别")
    @Trans(type = TransType.DICTIONARY,key = "ZD_XB")
    private String barxb;

    @ApiModelProperty("办案人证件类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_ZJLX")
    private String barzjlx;

    @ApiModelProperty("办案人证件号码")
    private String barzjhm;
}
