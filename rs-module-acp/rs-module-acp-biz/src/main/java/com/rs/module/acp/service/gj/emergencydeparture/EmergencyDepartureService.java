package com.rs.module.acp.service.gj.emergencydeparture;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.emergencydeparture.EmergencyDepartureListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.emergencydeparture.EmergencyDeparturePageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.emergencydeparture.EmergencyDepartureSaveReqVO;
import com.rs.module.acp.entity.gj.EmergencyDepartureDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-紧急出所登记 Service 接口
 *
 * <AUTHOR>
 */
public interface EmergencyDepartureService extends IBaseService<EmergencyDepartureDO>{

    /**
     * 创建实战平台-管教业务-紧急出所登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createEmergencyDeparture(@Valid EmergencyDepartureSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-紧急出所登记
     *
     * @param updateReqVO 更新信息
     */
    void updateEmergencyDeparture(@Valid EmergencyDepartureSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-紧急出所登记
     *
     * @param id 编号
     */
    void deleteEmergencyDeparture(String id);

    /**
     * 获得实战平台-管教业务-紧急出所登记
     *
     * @param id 编号
     * @return 实战平台-管教业务-紧急出所登记
     */
    EmergencyDepartureDO getEmergencyDeparture(String id);

    /**
    * 获得实战平台-管教业务-紧急出所登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-紧急出所登记分页
    */
    PageResult<EmergencyDepartureDO> getEmergencyDeparturePage(EmergencyDeparturePageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-紧急出所登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-紧急出所登记列表
    */
    List<EmergencyDepartureDO> getEmergencyDepartureList(EmergencyDepartureListReqVO listReqVO);


    PageResult<EmergencyDepartureDO> getAppEmergencyDeparturePage(int pageNo, int pageSize, String operatePoliceSfzh, String type);
}
