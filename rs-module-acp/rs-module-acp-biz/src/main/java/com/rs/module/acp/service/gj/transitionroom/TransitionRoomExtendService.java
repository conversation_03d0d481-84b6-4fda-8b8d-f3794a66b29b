package com.rs.module.acp.service.gj.transitionroom;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.transitionroom.TransitionRoomExtendSaveReqVO;
import com.rs.module.acp.entity.gj.TransitionRoomExtendDO;

import javax.validation.Valid;

/**
 * 实战平台-管教业务-过渡监室延长呈批 Service 接口
 *
 * <AUTHOR>
 */
public interface TransitionRoomExtendService extends IBaseService<TransitionRoomExtendDO>{

    /**
     * 创建实战平台-管教业务-过渡监室延长呈批
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createTransitionRoomExtend(@Valid TransitionRoomExtendSaveReqVO createReqVO);

    /**
     * 获得实战平台-管教业务-过渡监室延长呈批
     *
     * @param id 编号
     * @return 实战平台-管教业务-过渡监室延长呈批
     */
    TransitionRoomExtendDO getTransitionRoomExtend(String id);

    /**
     * 延长审批
     * <AUTHOR>
     * @date 2025/6/16 16:31
     * @param [approveReqVO]
     * @return boolean
     */
    boolean apprTransitionRoomExtend(GjApproveReqVO approveReqVO);

}
