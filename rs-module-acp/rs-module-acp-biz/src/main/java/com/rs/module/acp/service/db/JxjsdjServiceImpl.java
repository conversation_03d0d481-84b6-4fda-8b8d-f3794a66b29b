package com.rs.module.acp.service.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.db.vo.jxjsdj.*;
import com.rs.module.acp.controller.admin.zh.vo.MeetingRecordsRespVO;
import com.rs.module.acp.entity.zh.MeetingRecordsDO;
import com.rs.module.acp.enums.db.JxjsdjStatusEnum;
import com.rs.module.acp.service.zh.MeetingRecordsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.db.JxjsdjDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.db.JxjsdjDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-收押业务-减刑登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class JxjsdjServiceImpl extends BaseServiceImpl<JxjsdjDao, JxjsdjDO> implements JxjsdjService {

    @Resource
    private JxjsdjDao jxjsdjDao;

    @Resource
    private MeetingRecordsService meetingRecordsService;

    @Override
    public String createJxjsdj(JxjsdjSaveReqVO createReqVO) {
        // 插入
        JxjsdjDO jxjsdj = BeanUtils.toBean(createReqVO, JxjsdjDO.class);
        jxjsdjDao.insert(jxjsdj);
        // 返回
        return jxjsdj.getId();
    }

    @Override
    public void updateJxjsdj(JxjsdjSaveReqVO updateReqVO) {
        // 校验存在
        validateJxjsdjExists(updateReqVO.getId());
        // 更新
        JxjsdjDO updateObj = BeanUtils.toBean(updateReqVO, JxjsdjDO.class);
        jxjsdjDao.updateById(updateObj);
    }

    @Override
    public void deleteJxjsdj(String id) {
        // 校验存在
        validateJxjsdjExists(id);
        // 删除
        jxjsdjDao.deleteById(id);
    }

    private JxjsdjDO validateJxjsdjExists(String id) {
        JxjsdjDO jxjsdjDO = jxjsdjDao.selectById(id);
        if (jxjsdjDO == null) {
            throw new ServerException("实战平台-收押业务-减刑登记数据不存在");
        }
        return jxjsdjDO;
    }

    @Override
    public JxjsdjRespVO getJxjsdj(String id) {
        JxjsdjDO jxjsdjDO = validateJxjsdjExists(id);
        JxjsdjRespVO jxjsdjRespVO = BeanUtils.toBean(jxjsdjDO, JxjsdjRespVO.class);
        if (Objects.isNull(jxjsdjRespVO)) {
            return null;
        }
        if (StringUtils.isNotEmpty(jxjsdjRespVO.getSwhbh())) {
            MeetingRecordsDO one = meetingRecordsService.getOne(Wrappers.lambdaQuery(MeetingRecordsDO.class)
                    .eq(MeetingRecordsDO::getId, jxjsdjRespVO.getSwhbh()));
            jxjsdjRespVO.setSwhxx(BeanUtils.toBean(one, MeetingRecordsRespVO.class));
        }
        if (StringUtils.isNotEmpty(jxjsdjRespVO.getFhswhbh())) {
            MeetingRecordsDO one = meetingRecordsService.getOne(Wrappers.lambdaQuery(MeetingRecordsDO.class)
                    .eq(MeetingRecordsDO::getId, jxjsdjRespVO.getFhswhbh()));
            jxjsdjRespVO.setSwhfhxx(BeanUtils.toBean(one, MeetingRecordsRespVO.class));
        }
        if(StringUtils.isNotEmpty(jxjsdjRespVO.getFyshcdjg())){
            // 法院审核裁定结果（1、予以减刑或者假释；2、不予减刑或者假释）
            String jgName = "1".equals(jxjsdjRespVO.getFyshcdjg()) ? "予以" : "不予以";
            // 业务类型(1、减刑；2、假释)
            String ywlxName = "1".equals(jxjsdjRespVO.getYwlx()) ? "减刑" : "假释";
            jxjsdjRespVO.setFyshcdjgName(jgName+ywlxName);
        }

        return jxjsdjRespVO;
    }

    @Override
    public PageResult<JxjsdjDO> getJxjsdjPage(JxjsdjPageReqVO pageReqVO) {
        return jxjsdjDao.selectPage(pageReqVO);
    }

    @Override
    public List<JxjsdjDO> getJxjsdjList(JxjsdjListReqVO listReqVO) {
        return jxjsdjDao.selectList(listReqVO);
    }

    @Override
    public String dj(JxjsdjDjReqVO djReqVO) {
        // 插入
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        JxjsdjDO jxjsdj = BeanUtils.toBean(djReqVO, JxjsdjDO.class);
        jxjsdj.setOperateTime(new Date());
        jxjsdj.setOperatePolice(sessionUser.getName());
        jxjsdj.setOperatePoliceSfzh(sessionUser.getIdCard());
        jxjsdj.setStatus(JxjsdjStatusEnum.DSWHYJ.getCode());
        jxjsdjDao.insert(jxjsdj);
        // 返回
        return jxjsdj.getId();
    }

    @Override
    public void swhyj(JxjsdjSnhyjReqVO snhyjReqVO) {
        JxjsdjDO jxjsdjDO = validateJxjsdjExists(snhyjReqVO.getId());
        if (!JxjsdjStatusEnum.DSWHYJ.getCode().equals(jxjsdjDO.getStatus())) {
            throw new ServerException("当前状态为【" + JxjsdjStatusEnum.getByCode(jxjsdjDO.getStatus()).getName() + "】，" +
                    "不能进行【" + JxjsdjStatusEnum.DSWHYJ.getName().substring(1) + "】操作");
        }
        JxjsdjDO updateDO = BeanUtils.toBean(snhyjReqVO, JxjsdjDO.class);

        MeetingRecordsDO one = meetingRecordsService.getOne(Wrappers.lambdaQuery(MeetingRecordsDO.class)
                .eq(MeetingRecordsDO::getId, snhyjReqVO.getSwhbh()));
        if (Objects.isNull(one)) {
            throw new ServerException("该所务会议不存在，会议编号：" + snhyjReqVO.getSwhbh());
        }
        updateDO.setSwhzkrq(one.getMeetingStartTime());
        // 1、同意；2、不同意
        if ("1".equals(updateDO.getSwhyjjg())) {
            updateDO.setStatus(JxjsdjStatusEnum.DLXNLRD.getCode());
        } else {
            updateDO.setStatus(JxjsdjStatusEnum.YBJ.getCode());
        }
        jxjsdjDao.updateById(updateDO);
    }

    @Override
    public void lxnlrd(JxjsdjLxnlrdReqVO lxnlrdReqVO) {
        JxjsdjDO jxjsdjDO = validateJxjsdjExists(lxnlrdReqVO.getId());
        if (!JxjsdjStatusEnum.DLXNLRD.getCode().equals(jxjsdjDO.getStatus())) {
            throw new ServerException("当前状态为【" + JxjsdjStatusEnum.getByCode(jxjsdjDO.getStatus()).getName() + "】，" +
                    "不能进行【" + JxjsdjStatusEnum.DLXNLRD.getName().substring(1) + "】操作");
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        JxjsdjDO updateDO = BeanUtils.toBean(lxnlrdReqVO, JxjsdjDO.class);
        updateDO.setCcxpxlxnlrdjbmjsfzh(sessionUser.getIdCard());
        updateDO.setCcxpxlxnlrdjbmjxm(sessionUser.getName());
        updateDO.setCcxpxlxnlrdjbsj(new Date());
        // 1、同意；2、不同意
        if ("1".equals(updateDO.getCcxpxlxnlrdjg())) {
            // 1、减刑；2、假释
            if ("1".equals(jxjsdjDO.getYwlx())) {
                updateDO.setStatus(JxjsdjStatusEnum.DSNGS.getCode());
            } else {
                updateDO.setStatus(JxjsdjStatusEnum.DHSZXD.getCode());
            }
        } else {
            updateDO.setStatus(JxjsdjStatusEnum.YBJ.getCode());
        }

        jxjsdjDao.updateById(updateDO);
    }

    @Override
    public void hszxd(JxjsdjHszxdReqVO hszxdReqVO) {
        JxjsdjDO jxjsdjDO = validateJxjsdjExists(hszxdReqVO.getId());
        // 1、减刑；2、假释
        if ("1".equals(jxjsdjDO.getYwlx())) {
            throw new ServerException("减刑业务不允许进行【" + JxjsdjStatusEnum.DHSZXD.getName().substring(1) + "】操作");
        }
        if (!JxjsdjStatusEnum.DHSZXD.getCode().equals(jxjsdjDO.getStatus())) {
            throw new ServerException("当前状态为【" + JxjsdjStatusEnum.getByCode(jxjsdjDO.getStatus()).getName() + "】，" +
                    "不能进行【" + JxjsdjStatusEnum.DHSZXD.getName().substring(1) + "】操作");
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        JxjsdjDO updateDO = BeanUtils.toBean(hszxdReqVO, JxjsdjDO.class);
        updateDO.setHszxddcjbmjsfzh(sessionUser.getIdCard());
        updateDO.setHszxddcjbmjxm(sessionUser.getName());
        updateDO.setHszxddcjbsj(new Date());
        // 1、同意；2、不同意
        if ("1".equals(updateDO.getHszxddcpgjg())) {
            // 1、减刑；2、假释
            updateDO.setStatus(JxjsdjStatusEnum.DSNGS.getCode());
        } else {
            updateDO.setStatus(JxjsdjStatusEnum.YBJ.getCode());
        }

        jxjsdjDao.updateById(updateDO);
    }

    @Override
    public void sngs(JxjsdjSngsReqVO sngsReqVO) {
        JxjsdjDO jxjsdjDO = validateJxjsdjExists(sngsReqVO.getId());
        if (!JxjsdjStatusEnum.DSNGS.getCode().equals(jxjsdjDO.getStatus())) {
            throw new ServerException("当前状态为【" + JxjsdjStatusEnum.getByCode(jxjsdjDO.getStatus()).getName() + "】，" +
                    "不能进行【" + JxjsdjStatusEnum.DSNGS.getName().substring(1) + "】操作");
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        JxjsdjDO updateDO = BeanUtils.toBean(sngsReqVO, JxjsdjDO.class);
        updateDO.setSngsjbsj(new Date());
        updateDO.setSngsjbmjxm(sessionUser.getName());
        updateDO.setSngsjbmjsfzh(sessionUser.getIdCard());

        // 是否收到异议（1、是；2、否）  是：下一节点为 待所务会研究复核
        if ("1".equals(updateDO.getSfsdyy())) {
            updateDO.setStatus(JxjsdjStatusEnum.DSWHYJFH.getCode());
        } else {
            updateDO.setStatus(JxjsdjStatusEnum.DGAJGSC.getCode());
        }
        jxjsdjDao.updateById(updateDO);
    }

    @Override
    public void swhyjfh(JxjsdjSnhyjfhReqVO snhyjfhReqVO) {
        JxjsdjDO jxjsdjDO = validateJxjsdjExists(snhyjfhReqVO.getId());
        if (!JxjsdjStatusEnum.DSWHYJFH.getCode().equals(jxjsdjDO.getStatus())) {
            throw new ServerException("当前状态为【" + JxjsdjStatusEnum.getByCode(jxjsdjDO.getStatus()).getName() + "】，" +
                    "不能进行【" + JxjsdjStatusEnum.DSWHYJFH.getName().substring(1) + "】操作");
        }
        // 是否收到异议（1、是；2、否）
        if ("2".equals(jxjsdjDO.getSfsdyy())) {
            throw new ServerException(JxjsdjStatusEnum.DSNGS.getName().substring(1) + "环节无异议，" +
                    "不能进行【" + JxjsdjStatusEnum.DSWHYJFH.getName().substring(1) + "】操作");
        }
        JxjsdjDO updateDO = BeanUtils.toBean(snhyjfhReqVO, JxjsdjDO.class);

        MeetingRecordsDO one = meetingRecordsService.getOne(Wrappers.lambdaQuery(MeetingRecordsDO.class)
                .eq(MeetingRecordsDO::getId, snhyjfhReqVO.getFhswhbh()));
        if (Objects.isNull(one)) {
            throw new ServerException("该所务会议不存在，会议编号：" + snhyjfhReqVO.getFhswhbh());
        }
        updateDO.setCxzkswhfhrq(one.getMeetingStartTime());

        // 核意见（1、同意--不在提请减刑；2、不同意--继续提请减刑） 同意（同意有异议，结束流程） 不同意（不同意有异议，流程继续）
        if ("1".equals(updateDO.getFhyj())) {
            updateDO.setStatus(JxjsdjStatusEnum.YBJ.getCode());
        } else {
            updateDO.setStatus(JxjsdjStatusEnum.DGAJGSC.getCode());
        }
        jxjsdjDao.updateById(updateDO);
    }

    @Override
    public void gajgsc(@Valid JxjsdjGajgscReqVO gajgscReqVO) {
        JxjsdjDO jxjsdjDO = validateJxjsdjExists(gajgscReqVO.getId());
        if (!JxjsdjStatusEnum.DGAJGSC.getCode().equals(jxjsdjDO.getStatus())) {
            throw new ServerException("当前状态为【" + JxjsdjStatusEnum.getByCode(jxjsdjDO.getStatus()).getName() + "】，" +
                    "不能进行【" + JxjsdjStatusEnum.DGAJGSC.getName().substring(1) + "】操作");
        }

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        JxjsdjDO updateDO = BeanUtils.toBean(gajgscReqVO, JxjsdjDO.class);
        updateDO.setGajgscjbsj(new Date());
        updateDO.setGajgscjbmjsfzh(sessionUser.getIdCard());
        updateDO.setGajgscjbmjxm(sessionUser.getName());

        // 公安机关审查意见（1、同意；2、不同意）
        if ("1".equals(updateDO.getGajgscyj())) {
            updateDO.setStatus(JxjsdjStatusEnum.DJCYJD.getCode());
        } else {
            updateDO.setStatus(JxjsdjStatusEnum.YBJ.getCode());
        }
        jxjsdjDao.updateById(updateDO);
    }

    @Override
    public void jcyjd(JxjsdjJcyjdReqVO jcyjdReqVO) {
        JxjsdjDO jxjsdjDO = validateJxjsdjExists(jcyjdReqVO.getId());
        if (!JxjsdjStatusEnum.DJCYJD.getCode().equals(jxjsdjDO.getStatus())) {
            throw new ServerException("当前状态为【" + JxjsdjStatusEnum.getByCode(jxjsdjDO.getStatus()).getName() + "】，" +
                    "不能进行【" + JxjsdjStatusEnum.DJCYJD.getName().substring(1) + "】操作");
        }

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        JxjsdjDO updateDO = BeanUtils.toBean(jcyjdReqVO, JxjsdjDO.class);
        updateDO.setJcyjdjbsj(new Date());
        updateDO.setJcyjdjbmjsfzh(sessionUser.getIdCard());
        updateDO.setJcyjdjbmjxm(sessionUser.getName());
        updateDO.setStatus(JxjsdjStatusEnum.DFYSHCD.getCode());
        jxjsdjDao.updateById(updateDO);
    }

    @Override
    public void fyshcd(JxjsdjFyshcdReqVO fyshcdReqVO) {
        JxjsdjDO jxjsdjDO = validateJxjsdjExists(fyshcdReqVO.getId());
        if (!JxjsdjStatusEnum.DFYSHCD.getCode().equals(jxjsdjDO.getStatus())) {
            throw new ServerException("当前状态为【" + JxjsdjStatusEnum.getByCode(jxjsdjDO.getStatus()).getName() + "】，" +
                    "不能进行【" + JxjsdjStatusEnum.DFYSHCD.getName().substring(1) + "】操作");
        }

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        JxjsdjDO updateDO = BeanUtils.toBean(fyshcdReqVO, JxjsdjDO.class);
        updateDO.setFyshcdjbsj(new Date());
        updateDO.setFyshcdjbmjsfzh(sessionUser.getIdCard());
        updateDO.setFyshcdjbmjxm(sessionUser.getName());
        updateDO.setStatus(JxjsdjStatusEnum.YBJ.getCode());
        jxjsdjDao.updateById(updateDO);
    }


}
