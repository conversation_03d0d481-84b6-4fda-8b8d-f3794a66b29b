package com.rs.module.acp.dao.gj;

import java.util.*;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabActivityListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabActivityPageReqVO;
import com.rs.module.acp.entity.gj.EdurehabActivityDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
 * 实战平台-管教业务-教育康复活动 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface EdurehabActivityDao extends IBaseDao<EdurehabActivityDO> {


    default PageResult<EdurehabActivityDO> selectPage(EdurehabActivityPageReqVO reqVO) {
        Page<EdurehabActivityDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<EdurehabActivityDO> wrapper = new LambdaQueryWrapperX<EdurehabActivityDO>()
                .betweenIfPresent(EdurehabActivityDO::getActivityTime, reqVO.getActivityTime())
                .eqIfPresent(EdurehabActivityDO::getRehabPoliceSfzh, reqVO.getRehabPoliceSfzh())
                .eqIfPresent(EdurehabActivityDO::getRehabPolice, reqVO.getRehabPolice())
                .eqIfPresent(EdurehabActivityDO::getActivityTopic, reqVO.getActivityTopic())
                .eqIfPresent(EdurehabActivityDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(EdurehabActivityDO::getJgryxm, reqVO.getJgryxm())
                .eqIfPresent(EdurehabActivityDO::getParticipantCount, reqVO.getParticipantCount())
                .eqIfPresent(EdurehabActivityDO::getActivityDetails, reqVO.getActivityDetails())
                .eqIfPresent(EdurehabActivityDO::getAttUrl, reqVO.getAttUrl());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(EdurehabActivityDO::getAddTime);
        }
        Page<EdurehabActivityDO> edurehabActivityPage = selectPage(page, wrapper);
        return new PageResult<>(edurehabActivityPage.getRecords(), edurehabActivityPage.getTotal());
    }

    default List<EdurehabActivityDO> selectList(EdurehabActivityListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<EdurehabActivityDO>()
                .betweenIfPresent(EdurehabActivityDO::getActivityTime, reqVO.getActivityTime())
                .eqIfPresent(EdurehabActivityDO::getRehabPoliceSfzh, reqVO.getRehabPoliceSfzh())
                .eqIfPresent(EdurehabActivityDO::getRehabPolice, reqVO.getRehabPolice())
                .eqIfPresent(EdurehabActivityDO::getActivityTopic, reqVO.getActivityTopic())
                .eqIfPresent(EdurehabActivityDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(EdurehabActivityDO::getJgryxm, reqVO.getJgryxm())
                .eqIfPresent(EdurehabActivityDO::getParticipantCount, reqVO.getParticipantCount())
                .eqIfPresent(EdurehabActivityDO::getActivityDetails, reqVO.getActivityDetails())
                .eqIfPresent(EdurehabActivityDO::getAttUrl, reqVO.getAttUrl())
                .orderByDesc(EdurehabActivityDO::getAddTime));
    }


    default PageResult<EdurehabActivityDO> jykfcjqk(String jgrybm) {
        Page<EdurehabActivityDO> page = new Page<>(1, 10);
        LambdaQueryWrapperX<EdurehabActivityDO> wrapper = new LambdaQueryWrapperX<EdurehabActivityDO>()
                .likeIfPresent(EdurehabActivityDO::getJgrybm, jgrybm);
        wrapper.orderByDesc(EdurehabActivityDO::getAddTime);
        Page<EdurehabActivityDO> edurehabActivityPage = selectPage(page, wrapper);
        return new PageResult<>(edurehabActivityPage.getRecords(), edurehabActivityPage.getTotal());
    }
}
