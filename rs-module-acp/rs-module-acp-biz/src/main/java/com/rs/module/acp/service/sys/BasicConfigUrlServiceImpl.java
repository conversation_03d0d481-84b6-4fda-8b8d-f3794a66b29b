package com.rs.module.acp.service.sys;

import cn.hutool.core.collection.CollUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.sys.vo.BasicConfigSaveReqVO;
import com.rs.module.acp.controller.admin.sys.vo.BasicConfigUrlSaveReqVO;
import com.rs.module.acp.dao.sys.BasicConfigUrlDao;
import com.rs.module.acp.entity.sys.BasicConfigUrlDO;
import com.rs.module.acp.enums.sys.UrlTypeEnum;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 实战平台-系统基础配置链接 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BasicConfigUrlServiceImpl extends BaseServiceImpl<BasicConfigUrlDao, BasicConfigUrlDO> implements BasicConfigUrlService {

    @Resource
    private BasicConfigUrlDao basicConfigUrlDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveConfigBatchInfo(BasicConfigSaveReqVO saveReqVO, String systemId) {

        //删除再新增
        basicConfigUrlDao.delete(new LambdaQueryWrapperX<BasicConfigUrlDO>().eq(BasicConfigUrlDO::getSystemId, systemId));

        //浏览器
        if(CollUtil.isNotEmpty( saveReqVO.getBrowsers())){
            List<BasicConfigUrlDO> list = buildBasicConfigUrlDO(saveReqVO.getBrowsers(), saveReqVO.getId(), UrlTypeEnum.BROWSER.getCode());
            saveBatch(list);
        }

        //超链接
        if(CollUtil.isNotEmpty( saveReqVO.getHyperlink())){
            List<BasicConfigUrlDO> list = buildBasicConfigUrlDO(saveReqVO.getBrowsers(), saveReqVO.getId(), UrlTypeEnum.HYPERLINK.getCode());
            saveBatch(list);
        }

        //插件链接
        if(CollUtil.isNotEmpty( saveReqVO.getPlugins())){
            List<BasicConfigUrlDO> list = buildBasicConfigUrlDO(saveReqVO.getBrowsers(), saveReqVO.getId(), UrlTypeEnum.PLUGIN.getCode());
            saveBatch(list);
        }
    }

    /**
     * 构建URL信息
     * <AUTHOR>
     * @date 2025/5/23 10:29
     * @param [list, systemId, urlType]
     * @return java.util.List<com.rs.module.acp.entity.sys.BasicConfigUrlDO>
     */
    private List<BasicConfigUrlDO> buildBasicConfigUrlDO(List<BasicConfigUrlSaveReqVO> list, String systemId, String urlType){
        List<BasicConfigUrlDO> result = new ArrayList<>();
        list.forEach( e->{
            BasicConfigUrlDO urlDO = new BasicConfigUrlDO();
            BeanUtils.copyProperties(e, urlDO);
            urlDO.setSystemId(systemId);
            urlDO.setUrlType(urlType);
            result.add(urlDO);
        });
        return result;
    }
}
