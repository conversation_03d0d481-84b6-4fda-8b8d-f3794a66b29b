package com.rs.module.acp.controller.admin.db.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-羁押业务-看守所收押登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DetainRegKssTempRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("姓名")
    private String xm;
    @ApiModelProperty("姓名拼音")
    private String xmpy;
    @ApiModelProperty("别名")
    private String bm;
    @ApiModelProperty("性别")
    @Trans(type = TransType.DICTIONARY,key = "ZD_XB")
    private String xb;
    @ApiModelProperty("出生日期")
    private Date csrq;
    @ApiModelProperty("证件类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_ZJLX")
    private String zjlx;
    @ApiModelProperty("证件号码")
    private String zjhm;
    @ApiModelProperty("国籍")
    @Trans(type = TransType.DICTIONARY,key = "ZD_GABBZ_GJ")
    private String gj;
    @ApiModelProperty("民族")
    @Trans(type = TransType.DICTIONARY,key = "ZD_MZ")
    private String mz;
    @ApiModelProperty("婚姻状况")
    @Trans(type = TransType.DICTIONARY,key = "ZD_HYZK")
    private String hyzk;
    @ApiModelProperty("籍贯")
    @Trans(type = TransType.DICTIONARY,key = "ZD_GABBZ_JG")
    private String jg;
    @ApiModelProperty("宗教信仰")
    @Trans(type = TransType.DICTIONARY,key = "ZD_ZJXY")
    private String zjxy;
    @ApiModelProperty("户籍地")
    @Trans(type = TransType.DICTIONARY,key = "ZD_GABBZ_JG")
    private String hjd;
    @ApiModelProperty("户籍地详址")
    private String hjdxz;
    @ApiModelProperty("现住址")
    @Trans(type = TransType.DICTIONARY,key = "ZD_GABBZ_JG")
    private String xzz;
    @ApiModelProperty("现住址详址")
    private String xzzxz;
    @ApiModelProperty("文化程度")
    @Trans(type = TransType.DICTIONARY,key = "ZD_WHCD")
    private String whcd;
    @ApiModelProperty("政治面貌")
    @Trans(type = TransType.DICTIONARY,key = "ZD_ZZMM")
    private String zzmm;
    @ApiModelProperty("职业")
    @Trans(type = TransType.DICTIONARY,key = "ZD_ZY")
    private String zy;
    @ApiModelProperty("工作单位")
    private String gzdw;
    @ApiModelProperty("职务")
//    @Trans(type = TransType.DICTIONARY,key = "ZD_ZWJB")
    private String zw;
    @ApiModelProperty("职务级别")
    private String zwjb;
    @ApiModelProperty("特长(专长)")
    @Trans(type = TransType.DICTIONARY,key = "ZD_ZC")
    private String tc;
    @ApiModelProperty("身份")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SF")
    private String sf;
    @ApiModelProperty("特殊身份")
    @Trans(type = TransType.DICTIONARY,key = "ZD_TSSF")
    private String tssf;
    @ApiModelProperty("信息核查")
    @Trans(type = TransType.DICTIONARY,key = "ZD_XXHC")
    private String xxhc;
    @ApiModelProperty("是否为在校学生")
    @Trans(type = TransType.DICTIONARY,key = "ZD_TYSFDM")
    private String sfwxzxs;
    @ApiModelProperty("学校名称")
    @Trans(type = TransType.DICTIONARY,key = "ZD_XXMC")
    private String xxmc;
    @ApiModelProperty("管理类别")
    @Trans(type = TransType.DICTIONARY,key = "ZD_KSS_RYGLLB")
    private String gllb;
    @ApiModelProperty("案由代码")
    @Trans(type = TransType.DICTIONARY,key = "ZD_AJLB")
    private String ajlbdm;
    @ApiModelProperty("案件类别名称")
    private String ajlb;
    @ApiModelProperty("案件编号")
    private String ajbh;
    @ApiModelProperty("人员编号")
    private String rybh;
    @ApiModelProperty("办案中心编号")
    private String bazxbh;
    @ApiModelProperty("同案编号")
    private String tabh;
    @ApiModelProperty("收押凭证")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYYWSYPZLX")
    private String sypz;
    @ApiModelProperty("收押凭证文书号")
    private String sypzwsh;
    @ApiModelProperty("收押凭证文书号")
    private String sypzwsdz;
    @ApiModelProperty("入所时间")
    private Date rssj;
    @ApiModelProperty("入所原因")
    @Trans(type = TransType.DICTIONARY,key = "ZD_KSS_RSYY")
    private String rsyy;
    @ApiModelProperty("诉讼环节")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SSJD")
    private String sshj;
    @ApiModelProperty("送押机关类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYYWSYDWLX")
    private String syjglx;
    @ApiModelProperty("送押机关名称")
    @Trans(type = TransType.DICTIONARY,key = "ZD_BADW_GAJG")
    private String syjgmc;
    @ApiModelProperty("送押单位")
    private String sydw;
    @ApiModelProperty("送押人")
    private String syr1;
    @ApiModelProperty("送押人固话")
    private String syrgh1;
    @ApiModelProperty("送押人手机")
    private String syrsj1;
    @ApiModelProperty("送押人")
    private String syr2;
    @ApiModelProperty("送押人固话")
    private String syrgh2;
    @ApiModelProperty("送押人手机")
    private String syrsj2;
    @ApiModelProperty("办案单位类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_KSS_BADWLX")
    private String badwlx;
    @ApiModelProperty("办案单位")
    @Trans(type = TransType.DICTIONARY,key = "ZD_BADW_GAJG")
    private String badw;
    @ApiModelProperty("办案人")
    private String bar;
    @ApiModelProperty("办案人联系方式")
    private String barlxff;
    @ApiModelProperty("办案人性别")
    @Trans(type = TransType.DICTIONARY,key = "ZD_XB")
    private String barxb;
    @ApiModelProperty("办案人证件类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_ZJLX")
    private String barzjlx;
    @ApiModelProperty("办案人证件号码")
    private String barzjhm;
    @ApiModelProperty("办案环节")
    @Trans(type = TransType.DICTIONARY,key = "ZD_KSS_BAHJ")
    private String bahj;
    @ApiModelProperty("羁押日期")
    private Date jyrq;
    @ApiModelProperty("拘留日期")
    private Date jlrq;
    @ApiModelProperty("逮捕日期")
    private Date dbrq;
    @ApiModelProperty("关押期限")
    private Date gyqx;
    @ApiModelProperty("法律文书号")
    private String flwsh;
    @ApiModelProperty("简要案情")
    private String jyaq;
    @ApiModelProperty("涉嫌罪名")
    private String sxzm;
    @ApiModelProperty("限制会见案件")
    private Short xzhjaj;
    @ApiModelProperty("是否限制会见")
    @Trans(type = TransType.DICTIONARY,key = "ZD_TYSFDM")
    private Short xzhj;
    @ApiModelProperty("重刑犯")
    @Trans(type = TransType.DICTIONARY,key = "ZD_TYSFDM")
    private String zxf;
    @ApiModelProperty("档案编号")
    private String dabh;
    @ApiModelProperty("监区id")
    private String areaId;
    @ApiModelProperty("监区名称")
    private String areaName;
    @ApiModelProperty("监室号")
    private String jsh;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("床位号")
    private String cwh;
    @ApiModelProperty("指纹编号")
    private String zwbh;
    @ApiModelProperty("附物编号")
    private String fwbh;
    @ApiModelProperty("经办人")
    private String jbr;
    @ApiModelProperty("经办时间")
    private Date jbsj;
    @ApiModelProperty("涉毒尿检初查结果")
    private String sdnjccjg;
    @ApiModelProperty("涉毒尿检单位")
    private String sdnjdw;
    @ApiModelProperty("涉毒尿检初查结果")
    private Date sdnjccsj;
    @ApiModelProperty("涉毒尿检检查人")
    private String sdnjjcr;
    @ApiModelProperty("备注")
    private String bz;
    @ApiModelProperty("正面照片")
    private String frontPhoto;
    @ApiModelProperty("办理状态")
    private String status;

    @ApiModelProperty("案事件相关人员编号")
    private String asjxgrybh;

    @ApiModelProperty("强制措施类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYYWQZCS")
    private String qzcslx;
    @ApiModelProperty("社会关系")
    private List<dbSocialRelationsRespVO> socialRelations;

    @ApiModelProperty("审批状态")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SLDSPZTLB")
    private String spzt;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("是否涉密人员")
    private String sfsm;

    @ApiModelProperty("人员代号")
    private String rydh;

    @ApiModelProperty("涉密原因")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYDJSMYY")
    private String smyy;

    @ApiModelProperty("涉密备注")
    private String smbz;

    @ApiModelProperty("救济日期")
    private Date jjrq;

    @ApiModelProperty("救济原因")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYDJJJYY")
    private String jjyy;

    @ApiModelProperty("救济领取物品")
    private String jjlqwp;

    @ApiModelProperty("入所类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYYWRSLX")
    private String rslx;


    @ApiModelProperty("收押民警姓名")
    private String symjxm;

    @ApiModelProperty("看守所联系电话")
    private String ksslxdh;

    @ApiModelProperty("手环ID")
    private String shid;

    @ApiModelProperty("手环绑定时间")
    private Date sdbdsj;

    @ApiModelProperty("手环绑定状态")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYRSSHBDZT")
    private String shbdzt;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;


    /**
     * 以下十个字段是针对二看收治增加的字段
     * @Date 2025/6/24 15:40
     */
    @ApiModelProperty("回执文书号")
    private String hzwsh;

    @ApiModelProperty("原押所-送押单位")
    private String yysSydw;

    @ApiModelProperty("原押所-送押民警")
    private String yysSymj;

    @ApiModelProperty("原押所-送押民警联系电话")
    private String yysSymjlxdh;

    @ApiModelProperty("原押所-表现")
    private String yysBx;

    @ApiModelProperty("送押民警")
    private String symj;

    @ApiModelProperty("是否佩戴眼镜")
    private String sfpdyj;

    @ApiModelProperty("收分配标识服")
    private String sfpbsf;

    @ApiModelProperty("带入监室人")
    private String drjsr;

    @ApiModelProperty("带入监室时间")
    private Date drjssj;

    @ApiModelProperty("当前阶段")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYYWJD")
    private String currentStep;

    @ApiModelProperty("标识服颜色")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SBFBS")
    private String suitColor;



}
