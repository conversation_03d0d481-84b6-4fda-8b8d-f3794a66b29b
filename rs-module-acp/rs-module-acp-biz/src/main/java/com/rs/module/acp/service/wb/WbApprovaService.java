package com.rs.module.acp.service.wb;


import com.alibaba.fastjson.JSONObject;
import com.rs.module.acp.controller.admin.wb.vo.WbApprovaReqVO;

import java.util.Map;

public interface WbApprovaService {

    /**
     *  窗口业务-流程审批
     * @param approvaReqVO
     */
    void approve(WbApprovaReqVO approvaReqVO);

    /**
     * 实战平台-窗口业务-根据流程实例ID获取流程轨迹
     * @param actInstId
     * @return
     */
    JSONObject getApproveTrack(String actInstId);
}
