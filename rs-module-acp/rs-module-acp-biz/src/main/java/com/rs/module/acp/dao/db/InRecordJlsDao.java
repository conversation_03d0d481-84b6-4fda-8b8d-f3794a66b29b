package com.rs.module.acp.dao.db;

import java.util.*;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.db.InRecordJlsDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-收押业务-入所登记（拘留所） Dao
*
* <AUTHOR>
*/
@Mapper
public interface InRecordJlsDao extends IBaseDao<InRecordJlsDO> {


    default PageResult<InRecordJlsDO> selectPage(InRecordJlsPageReqVO reqVO) {
        Page<InRecordJlsDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<InRecordJlsDO> wrapper = new LambdaQueryWrapperX<InRecordJlsDO>()
            .eqIfPresent(InRecordJlsDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(InRecordJlsDO::getXm, reqVO.getXm())
            .eqIfPresent(InRecordJlsDO::getXmpy, reqVO.getXmpy())
            .eqIfPresent(InRecordJlsDO::getBm, reqVO.getBm())
            .eqIfPresent(InRecordJlsDO::getXb, reqVO.getXb())
            .eqIfPresent(InRecordJlsDO::getCsrq, reqVO.getCsrq())
            .eqIfPresent(InRecordJlsDO::getZjlx, reqVO.getZjlx())
            .eqIfPresent(InRecordJlsDO::getZjhm, reqVO.getZjhm())
            .eqIfPresent(InRecordJlsDO::getGj, reqVO.getGj())
            .eqIfPresent(InRecordJlsDO::getMz, reqVO.getMz())
            .eqIfPresent(InRecordJlsDO::getHyzk, reqVO.getHyzk())
            .eqIfPresent(InRecordJlsDO::getJg, reqVO.getJg())
            .eqIfPresent(InRecordJlsDO::getZjxy, reqVO.getZjxy())
            .eqIfPresent(InRecordJlsDO::getHjd, reqVO.getHjd())
            .eqIfPresent(InRecordJlsDO::getHjdxz, reqVO.getHjdxz())
            .eqIfPresent(InRecordJlsDO::getXzz, reqVO.getXzz())
            .eqIfPresent(InRecordJlsDO::getXzzxz, reqVO.getXzzxz())
            .eqIfPresent(InRecordJlsDO::getWhcd, reqVO.getWhcd())
            .eqIfPresent(InRecordJlsDO::getZzmm, reqVO.getZzmm())
            .eqIfPresent(InRecordJlsDO::getZy, reqVO.getZy())
            .eqIfPresent(InRecordJlsDO::getGzdw, reqVO.getGzdw())
            .eqIfPresent(InRecordJlsDO::getSf, reqVO.getSf())
            .eqIfPresent(InRecordJlsDO::getTssf, reqVO.getTssf())
            .eqIfPresent(InRecordJlsDO::getXxhc, reqVO.getXxhc())
            .eqIfPresent(InRecordJlsDO::getSfwxzxs, reqVO.getSfwxzxs())
            .eqIfPresent(InRecordJlsDO::getXxmc, reqVO.getXxmc())
            .eqIfPresent(InRecordJlsDO::getAjlbdm, reqVO.getAjlbdm())
            .eqIfPresent(InRecordJlsDO::getAjlb, reqVO.getAjlb())
            .eqIfPresent(InRecordJlsDO::getAjbh, reqVO.getAjbh())
            .eqIfPresent(InRecordJlsDO::getRybh, reqVO.getRybh())
            .eqIfPresent(InRecordJlsDO::getAsjxgrybh, reqVO.getAsjxgrybh())
            .eqIfPresent(InRecordJlsDO::getBazxbh, reqVO.getBazxbh())
            .eqIfPresent(InRecordJlsDO::getSjrq, reqVO.getSjrq())
            .eqIfPresent(InRecordJlsDO::getSjjglx, reqVO.getSjjglx())
            .eqIfPresent(InRecordJlsDO::getSjjgmc, reqVO.getSjjgmc())
            .eqIfPresent(InRecordJlsDO::getSjrxm, reqVO.getSjrxm())
            .eqIfPresent(InRecordJlsDO::getSjrlxdh, reqVO.getSjrlxdh())
            .eqIfPresent(InRecordJlsDO::getSjrxm2, reqVO.getSjrxm2())
            .eqIfPresent(InRecordJlsDO::getSjrlxdh2, reqVO.getSjrlxdh2())
            .eqIfPresent(InRecordJlsDO::getSjmjxm, reqVO.getSjmjxm())
            .eqIfPresent(InRecordJlsDO::getSjmjxmlxdh, reqVO.getSjmjxmlxdh())
            .eqIfPresent(InRecordJlsDO::getBadwlx, reqVO.getBadwlx())
            .eqIfPresent(InRecordJlsDO::getBadw, reqVO.getBadw())
            .eqIfPresent(InRecordJlsDO::getJljdjglx, reqVO.getJljdjglx())
            .eqIfPresent(InRecordJlsDO::getJljdjgmc, reqVO.getJljdjgmc())
            .eqIfPresent(InRecordJlsDO::getSjpz, reqVO.getSjpz())
            .eqIfPresent(InRecordJlsDO::getSjpzwsh, reqVO.getSjpzwsh())
            .eqIfPresent(InRecordJlsDO::getSjpzwsdz, reqVO.getSjpzwsdz())
            .eqIfPresent(InRecordJlsDO::getDabh, reqVO.getDabh())
            .eqIfPresent(InRecordJlsDO::getHzflwsh, reqVO.getHzflwsh())
            .eqIfPresent(InRecordJlsDO::getRsyy, reqVO.getRsyy())
            .eqIfPresent(InRecordJlsDO::getRsrq, reqVO.getRsrq())
            .eqIfPresent(InRecordJlsDO::getSjmj, reqVO.getSjmj())
            .eqIfPresent(InRecordJlsDO::getJlqx, reqVO.getJlqx())
            .eqIfPresent(InRecordJlsDO::getJlqsrq, reqVO.getJlqsrq())
            .eqIfPresent(InRecordJlsDO::getJljzrq, reqVO.getJljzrq())
            .eqIfPresent(InRecordJlsDO::getGllb, reqVO.getGllb())
            .eqIfPresent(InRecordJlsDO::getAreaId, reqVO.getAreaId())
            .likeIfPresent(InRecordJlsDO::getAreaName, reqVO.getAreaName())
            .eqIfPresent(InRecordJlsDO::getJsh, reqVO.getJsh())
            .likeIfPresent(InRecordJlsDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(InRecordJlsDO::getJyaq, reqVO.getJyaq())
            .eqIfPresent(InRecordJlsDO::getTar, reqVO.getTar())
            .eqIfPresent(InRecordJlsDO::getBz, reqVO.getBz())
            .eqIfPresent(InRecordJlsDO::getSdnjccjg, reqVO.getSdnjccjg())
            .eqIfPresent(InRecordJlsDO::getSdnjdw, reqVO.getSdnjdw())
            .eqIfPresent(InRecordJlsDO::getSdnjccsj, reqVO.getSdnjccsj())
            .eqIfPresent(InRecordJlsDO::getSdnjjcr, reqVO.getSdnjjcr())
            .eqIfPresent(InRecordJlsDO::getShid, reqVO.getShid())
            .eqIfPresent(InRecordJlsDO::getShbdzt, reqVO.getShbdzt())
            .eqIfPresent(InRecordJlsDO::getSdbdsj, reqVO.getSdbdsj())
            .eqIfPresent(InRecordJlsDO::getSfsm, reqVO.getSfsm())
            .eqIfPresent(InRecordJlsDO::getRydh, reqVO.getRydh())
            .eqIfPresent(InRecordJlsDO::getSmyy, reqVO.getSmyy())
            .eqIfPresent(InRecordJlsDO::getSmbz, reqVO.getSmbz())
            .eqIfPresent(InRecordJlsDO::getJjrq, reqVO.getJjrq())
            .eqIfPresent(InRecordJlsDO::getJjyy, reqVO.getJjyy())
            .eqIfPresent(InRecordJlsDO::getJjlqwp, reqVO.getJjlqwp())
            .eqIfPresent(InRecordJlsDO::getRslx, reqVO.getRslx())
            .eqIfPresent(InRecordJlsDO::getJbr, reqVO.getJbr())
            .eqIfPresent(InRecordJlsDO::getJbsj, reqVO.getJbsj())
            .eqIfPresent(InRecordJlsDO::getStatus, reqVO.getStatus())
            .eqIfPresent(InRecordJlsDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(InRecordJlsDO::getSpzt, reqVO.getSpzt())
            .eqIfPresent(InRecordJlsDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(InRecordJlsDO::getTaskId, reqVO.getTaskId())
            .eqIfPresent(InRecordJlsDO::getCurrentStep, reqVO.getCurrentStep())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(InRecordJlsDO::getAddTime);
        }
        Page<InRecordJlsDO> inRecordJlsPage = selectPage(page, wrapper);
        return new PageResult<>(inRecordJlsPage.getRecords(), inRecordJlsPage.getTotal());
    }
    default List<InRecordJlsDO> selectList(InRecordJlsListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<InRecordJlsDO>()
            .eqIfPresent(InRecordJlsDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(InRecordJlsDO::getXm, reqVO.getXm())
            .eqIfPresent(InRecordJlsDO::getXmpy, reqVO.getXmpy())
            .eqIfPresent(InRecordJlsDO::getBm, reqVO.getBm())
            .eqIfPresent(InRecordJlsDO::getXb, reqVO.getXb())
            .eqIfPresent(InRecordJlsDO::getCsrq, reqVO.getCsrq())
            .eqIfPresent(InRecordJlsDO::getZjlx, reqVO.getZjlx())
            .eqIfPresent(InRecordJlsDO::getZjhm, reqVO.getZjhm())
            .eqIfPresent(InRecordJlsDO::getGj, reqVO.getGj())
            .eqIfPresent(InRecordJlsDO::getMz, reqVO.getMz())
            .eqIfPresent(InRecordJlsDO::getHyzk, reqVO.getHyzk())
            .eqIfPresent(InRecordJlsDO::getJg, reqVO.getJg())
            .eqIfPresent(InRecordJlsDO::getZjxy, reqVO.getZjxy())
            .eqIfPresent(InRecordJlsDO::getHjd, reqVO.getHjd())
            .eqIfPresent(InRecordJlsDO::getHjdxz, reqVO.getHjdxz())
            .eqIfPresent(InRecordJlsDO::getXzz, reqVO.getXzz())
            .eqIfPresent(InRecordJlsDO::getXzzxz, reqVO.getXzzxz())
            .eqIfPresent(InRecordJlsDO::getWhcd, reqVO.getWhcd())
            .eqIfPresent(InRecordJlsDO::getZzmm, reqVO.getZzmm())
            .eqIfPresent(InRecordJlsDO::getZy, reqVO.getZy())
            .eqIfPresent(InRecordJlsDO::getGzdw, reqVO.getGzdw())
            .eqIfPresent(InRecordJlsDO::getSf, reqVO.getSf())
            .eqIfPresent(InRecordJlsDO::getTssf, reqVO.getTssf())
            .eqIfPresent(InRecordJlsDO::getXxhc, reqVO.getXxhc())
            .eqIfPresent(InRecordJlsDO::getSfwxzxs, reqVO.getSfwxzxs())
            .eqIfPresent(InRecordJlsDO::getXxmc, reqVO.getXxmc())
            .eqIfPresent(InRecordJlsDO::getAjlbdm, reqVO.getAjlbdm())
            .eqIfPresent(InRecordJlsDO::getAjlb, reqVO.getAjlb())
            .eqIfPresent(InRecordJlsDO::getAjbh, reqVO.getAjbh())
            .eqIfPresent(InRecordJlsDO::getRybh, reqVO.getRybh())
            .eqIfPresent(InRecordJlsDO::getAsjxgrybh, reqVO.getAsjxgrybh())
            .eqIfPresent(InRecordJlsDO::getBazxbh, reqVO.getBazxbh())
            .eqIfPresent(InRecordJlsDO::getSjrq, reqVO.getSjrq())
            .eqIfPresent(InRecordJlsDO::getSjjglx, reqVO.getSjjglx())
            .eqIfPresent(InRecordJlsDO::getSjjgmc, reqVO.getSjjgmc())
            .eqIfPresent(InRecordJlsDO::getSjrxm, reqVO.getSjrxm())
            .eqIfPresent(InRecordJlsDO::getSjrlxdh, reqVO.getSjrlxdh())
            .eqIfPresent(InRecordJlsDO::getSjrxm2, reqVO.getSjrxm2())
            .eqIfPresent(InRecordJlsDO::getSjrlxdh2, reqVO.getSjrlxdh2())
            .eqIfPresent(InRecordJlsDO::getSjmjxm, reqVO.getSjmjxm())
            .eqIfPresent(InRecordJlsDO::getSjmjxmlxdh, reqVO.getSjmjxmlxdh())
            .eqIfPresent(InRecordJlsDO::getBadwlx, reqVO.getBadwlx())
            .eqIfPresent(InRecordJlsDO::getBadw, reqVO.getBadw())
            .eqIfPresent(InRecordJlsDO::getJljdjglx, reqVO.getJljdjglx())
            .eqIfPresent(InRecordJlsDO::getJljdjgmc, reqVO.getJljdjgmc())
            .eqIfPresent(InRecordJlsDO::getSjpz, reqVO.getSjpz())
            .eqIfPresent(InRecordJlsDO::getSjpzwsh, reqVO.getSjpzwsh())
            .eqIfPresent(InRecordJlsDO::getSjpzwsdz, reqVO.getSjpzwsdz())
            .eqIfPresent(InRecordJlsDO::getDabh, reqVO.getDabh())
            .eqIfPresent(InRecordJlsDO::getHzflwsh, reqVO.getHzflwsh())
            .eqIfPresent(InRecordJlsDO::getRsyy, reqVO.getRsyy())
            .eqIfPresent(InRecordJlsDO::getRsrq, reqVO.getRsrq())
            .eqIfPresent(InRecordJlsDO::getSjmj, reqVO.getSjmj())
            .eqIfPresent(InRecordJlsDO::getJlqx, reqVO.getJlqx())
            .eqIfPresent(InRecordJlsDO::getJlqsrq, reqVO.getJlqsrq())
            .eqIfPresent(InRecordJlsDO::getJljzrq, reqVO.getJljzrq())
            .eqIfPresent(InRecordJlsDO::getGllb, reqVO.getGllb())
            .eqIfPresent(InRecordJlsDO::getAreaId, reqVO.getAreaId())
            .likeIfPresent(InRecordJlsDO::getAreaName, reqVO.getAreaName())
            .eqIfPresent(InRecordJlsDO::getJsh, reqVO.getJsh())
            .likeIfPresent(InRecordJlsDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(InRecordJlsDO::getJyaq, reqVO.getJyaq())
            .eqIfPresent(InRecordJlsDO::getTar, reqVO.getTar())
            .eqIfPresent(InRecordJlsDO::getBz, reqVO.getBz())
            .eqIfPresent(InRecordJlsDO::getSdnjccjg, reqVO.getSdnjccjg())
            .eqIfPresent(InRecordJlsDO::getSdnjdw, reqVO.getSdnjdw())
            .eqIfPresent(InRecordJlsDO::getSdnjccsj, reqVO.getSdnjccsj())
            .eqIfPresent(InRecordJlsDO::getSdnjjcr, reqVO.getSdnjjcr())
            .eqIfPresent(InRecordJlsDO::getShid, reqVO.getShid())
            .eqIfPresent(InRecordJlsDO::getShbdzt, reqVO.getShbdzt())
            .eqIfPresent(InRecordJlsDO::getSdbdsj, reqVO.getSdbdsj())
            .eqIfPresent(InRecordJlsDO::getSfsm, reqVO.getSfsm())
            .eqIfPresent(InRecordJlsDO::getRydh, reqVO.getRydh())
            .eqIfPresent(InRecordJlsDO::getSmyy, reqVO.getSmyy())
            .eqIfPresent(InRecordJlsDO::getSmbz, reqVO.getSmbz())
            .eqIfPresent(InRecordJlsDO::getJjrq, reqVO.getJjrq())
            .eqIfPresent(InRecordJlsDO::getJjyy, reqVO.getJjyy())
            .eqIfPresent(InRecordJlsDO::getJjlqwp, reqVO.getJjlqwp())
            .eqIfPresent(InRecordJlsDO::getRslx, reqVO.getRslx())
            .eqIfPresent(InRecordJlsDO::getJbr, reqVO.getJbr())
            .eqIfPresent(InRecordJlsDO::getJbsj, reqVO.getJbsj())
            .eqIfPresent(InRecordJlsDO::getStatus, reqVO.getStatus())
            .eqIfPresent(InRecordJlsDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(InRecordJlsDO::getSpzt, reqVO.getSpzt())
            .eqIfPresent(InRecordJlsDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(InRecordJlsDO::getTaskId, reqVO.getTaskId())
            .eqIfPresent(InRecordJlsDO::getCurrentStep, reqVO.getCurrentStep())
        .orderByDesc(InRecordJlsDO::getAddTime));    }


    void syncDataToPrisonerJlsIn(String id);

    @InterceptorIgnore(illegalSql = "true")
    InRecordStatusVO getInRecordStatus(String rybh);
}
