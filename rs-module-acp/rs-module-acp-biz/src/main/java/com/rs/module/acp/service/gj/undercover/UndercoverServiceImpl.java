package com.rs.module.acp.service.gj.undercover;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.cons.CommonConstants;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverRecordVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverSaveReqVO;
import com.rs.module.acp.dao.gj.UndercoverDao;
import com.rs.module.acp.entity.gj.UndercoverDO;
import com.rs.module.acp.enums.gj.UndercoverStatusEnum;
import com.rs.module.acp.util.GjBusTraceUtil;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.sys.MsgAddVO;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.service.sys.BusTraceService;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.base.util.MsgUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 实战平台-管教业务-耳目管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class UndercoverServiceImpl extends BaseServiceImpl<UndercoverDao, UndercoverDO> implements UndercoverService {

    @Resource
    private UndercoverDao undercoverDao;

    @Resource
    private UndercoverCancelService undercoverCancelService;

    @Resource
    private PrisonerService prisonerService;

    private final String defKey = "xinxiyuanbujianshenqing";

    @Resource
    private BusTraceService busTraceService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createUndercover(UndercoverSaveReqVO createReqVO) {
        // 插入
        UndercoverDO undercover = BeanUtils.toBean(createReqVO, UndercoverDO.class);
        undercover.setStatus(UndercoverStatusEnum.YDJ.getCode());
        undercoverDao.insert(undercover);

        //启动流程审批跳转链接
        String msgUrl = StrUtil.format("/#/discipline/InformationOfficer?curId={}&saveType=approve", undercover.getId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", undercover.getId());
        variables.put("busType", MsgBusTypeEnum.GJ_XXYDJ.getCode());
        JSONObject result = BspApprovalUtil.commonStartProcess(defKey, undercover.getId(), "【审批】信息员布建申请", msgUrl, variables, HttpUtils.getAppCode());
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            undercover.setActInstId(bpmTrail.getString("actInstId"));
            undercover.setTaskId(bpmTrail.getString("taskId"));
            undercover.setStatus(UndercoverStatusEnum.DSP.getCode());
            undercoverDao.updateById(undercover);
        } else {
            throw new ServerException("流程启动失败");
        }

        // 返回
        return undercover.getId();
    }

    @Override
    public void updateUndercover(UndercoverSaveReqVO updateReqVO) {
        // 校验存在
        validateUndercoverExists(updateReqVO.getId());
        // 更新
        UndercoverDO updateObj = BeanUtils.toBean(updateReqVO, UndercoverDO.class);
        undercoverDao.updateById(updateObj);
    }

    @Override
    public void deleteUndercover(String id) {
        // 校验存在
        validateUndercoverExists(id);
        // 删除
        undercoverDao.deleteById(id);
    }

    private void validateUndercoverExists(String id) {
        if (undercoverDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-耳目管理数据不存在");
        }
    }

    @Override
    public UndercoverDO getUndercover(String id) {
        return undercoverDao.selectById(id);
    }

    @Override
    public PageResult<UndercoverDO> getUndercoverPage(UndercoverPageReqVO pageReqVO) {
        return undercoverDao.selectPage(pageReqVO);
    }

    @Override
    public List<UndercoverDO> getUndercoverList(UndercoverListReqVO listReqVO) {
        return undercoverDao.selectList(listReqVO);
    }

    @Override
    public Boolean updateProcessStatus(String id, String status, String actInstId, String taskId) {
        UndercoverDO undercoverDO = undercoverDao.selectById(id);
        if(undercoverDO == null){
            throw new ServerException("更新失败！");
        }
        undercoverDO.setActInstId(actInstId);
        undercoverDO.setStatus(status);
        undercoverDO.setTaskId(taskId);
        if (undercoverDao.updateById(undercoverDO)> 0) {
            return true;
        }
        throw new ServerException("更新失败！");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void leaderApprove(GjApproveReqVO gjApproveReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        // 领导审批
        UndercoverDO entity =  undercoverDao.selectById(gjApproveReqVO.getId());;
        if (entity == null) {
            throw new ServerException("信息不存在！");
        }
        UndercoverStatusEnum statusEnum = UndercoverStatusEnum.getByCode(entity.getStatus());
        if (!UndercoverStatusEnum.DSP.getCode().equals(entity.getStatus())) {
            throw new ServerException("该状态[" + statusEnum.getName() + "]不允许审批！");
        }

        BeanUtil.copyProperties(gjApproveReqVO, entity);
        String approvalResult = gjApproveReqVO.getApprovalResult();
        String status = null;
        String apprName = "信息员布建申请", statusName = "已通过审批", passStatus =  "审批通过";
        String moduleCode = "GJ_UNDERCOVER_APPR";
        BspApproceStatusEnum bspApproceStatusEnum;
        if (String.valueOf( BspApproceStatusEnum.PASSED_END.getCode()).equals(approvalResult)) {
            status = UndercoverStatusEnum.TJZ.getCode();
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED_END;
            entity.setStartTime(new Date());
        } else {
            status = UndercoverStatusEnum.SPBTG.getCode();
            statusName = "审批不通过";
            passStatus =  statusName;
            bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
        }
        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(entity.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }


        entity.setApproverXm(sessionUser.getName());
        entity.setApproverSfzh(sessionUser.getIdCard());
        entity.setApproverTime(new Date());

        if(StrUtil.isBlank(entity.getApprovalComments())){
            entity.setApprovalComments(com.rs.framework.common.cons.CommonConstants.DEFAULT_APPROVAL_VALUE_PASS);
        }

        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", entity.getId());
        variables.put("busType", MsgBusTypeEnum.GJ_XXYDJ.getCode());
        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getIdCard());
        nowApproveUser.put("name", sessionUser.getName());
        boolean terminateTask = BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ||
                BspApproceStatusEnum.NOT_PASSED_END.getCode() == bspApproceStatusEnum.getCode();
        JSONObject result = BspApprovalUtil.approvalProcess(defKey, entity.getActInstId(), entity.getTaskId(), entity.getId(),
                bspApproceStatusEnum, entity.getApprovalComments(), null, null, terminateTask,
                variables, nowApproveUser, HttpUtils.getAppCode());
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            entity.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程审批失败");
        }

        String msgType = MsgBusTypeEnum.GJ_XXYDJ.getCode();
        //该审批，可能是申请，也可能是撤销
        //UndercoverCancelDO undercoverCancel = undercoverCancelService.getUndercoverCancel(entity.getId());
        //if(undercoverCancel != null){
        //    apprName = "信息员布建撤销";
        //    moduleCode = "GJ_UNDERCOVER_UNDO";
        //    msgType = "08";
        //}

        String title = apprName + passStatus ;	//消息标题
        entity.setStatus(status);
        MsgAddVO msg = new MsgAddVO();
        // 等前端路由封装
        String msgUrl = StrUtil.format("/#/discipline/InformationOfficer?curId={}&saveType=approve", gjApproveReqVO.getId());
        msg.setUrl(msgUrl);
        msg.setModuleCode(moduleCode);
        //消息类型代码 字典编码对应：ZD_MSG_BUSTYP
        msg.setMsgType(msgType);
        msg.setTitle(title);
        msg.setOrgName(sessionUser.getOrgName());
        msg.setToOrgCode(sessionUser.getOrgCode());
        msg.setBusinessId(entity.getId());
        msg.setJgrybm(entity.getJgrybm());
        Map<String, Object> contentData = new HashMap<>();
        contentData.put("addTime", DateUtil.formatDateTime(entity.getAddTime()));
        contentData.put("addUserName", entity.getAddUserName());
        contentData.put("statusName", statusName);
        msg.setContentData(contentData);
        msg.setPcid(entity.getId());
        MsgUtil.sendMsg(msg);

        entity.setStatus(status);
        undercoverDao.updateById(entity);


        if( BspApproceStatusEnum.PASSED_END.equals(bspApproceStatusEnum)){
            PrisonerVwRespVO prisoner = prisonerService.getPrisonerByJgrybm( entity.getJgrybm());
            busTraceService.saveBusTrace(BusTypeEnum.YEWU_XXYGL, GjBusTraceUtil.buildUndercoverBusTraceContent(entity,prisoner.getXm()),
                    entity.getJgrybm(),
                    SessionUserUtil.getSessionUser().getOrgCode(),
                    entity.getId());
        }

    }

    @Override
    public PageResult<UndercoverRecordVO> getUndercoverRespVOByJgrybm(String jgrybm,Integer pageNo, Integer pageSize ) {

        Page<UndercoverRecordVO> page = new Page<>(pageNo,pageSize);

        List<UndercoverRecordVO> list = undercoverDao.getUndercoverRespVOByJgrybm(page, jgrybm);
        return new PageResult(list,page.getTotal());
    }

}
