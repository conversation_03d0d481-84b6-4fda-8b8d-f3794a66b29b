package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.module.acp.service.gj.samecasemanage.SameCaseManageService;
import com.rs.module.acp.service.gj.samecasemanage.bo.SameCaseManageBO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.pm.PrisonerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.LawyerPrisonerDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.LawyerPrisonerDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-律师关联被监管人员 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LawyerPrisonerServiceImpl extends BaseServiceImpl<LawyerPrisonerDao, LawyerPrisonerDO> implements LawyerPrisonerService {

    @Resource
    private LawyerPrisonerDao lawyerPrisonerDao;

    @Autowired
    private PrisonerService prisonerService;

    @Autowired
    private WbCommonService wbCommonService;

    @Autowired
    private SameCaseManageService sameCaseManageService;

    @Override
    public String createLawyerPrisoner(LawyerPrisonerSaveReqVO createReqVO) {
        // 插入
        LawyerPrisonerDO lawyerPrisoner = BeanUtils.toBean(createReqVO, LawyerPrisonerDO.class);
        lawyerPrisonerDao.insert(lawyerPrisoner);
        // 返回
        return lawyerPrisoner.getId();
    }

    @Override
    public void updateLawyerPrisoner(LawyerPrisonerSaveReqVO updateReqVO) {
        // 校验存在
        validateLawyerPrisonerExists(updateReqVO.getId());
        // 更新
        LawyerPrisonerDO updateObj = BeanUtils.toBean(updateReqVO, LawyerPrisonerDO.class);
        lawyerPrisonerDao.updateById(updateObj);
    }

    @Override
    public void deleteLawyerPrisoner(String id) {
        // 校验存在
        validateLawyerPrisonerExists(id);
        // 删除
        lawyerPrisonerDao.deleteById(id);
    }

    private void validateLawyerPrisonerExists(String id) {
        if (lawyerPrisonerDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-律师关联被监管人员数据不存在");
        }
    }

    @Override
    public LawyerPrisonerDO getLawyerPrisoner(String id) {
        return lawyerPrisonerDao.selectById(id);
    }

    @Override
    public PageResult<LawyerPrisonerDO> getLawyerPrisonerPage(LawyerPrisonerPageReqVO pageReqVO) {
        return lawyerPrisonerDao.selectPage(pageReqVO);
    }

    @Override
    public List<LawyerPrisonerDO> getLawyerPrisonerList(LawyerPrisonerListReqVO listReqVO) {
        return lawyerPrisonerDao.selectList(listReqVO);
    }

    @Override
    public List<LawyerPrisonerRespVO> getPrisonerListByLawyerId(String lawyerId) {
        List<LawyerPrisonerRespVO> lawyerPrisonerRespVOList = lawyerPrisonerDao.getPrisonerListByLawyerId(lawyerId);

        if (CollectionUtil.isNotEmpty(lawyerPrisonerRespVOList)) {
            for(LawyerPrisonerRespVO lawyerPrisonerRespVO:lawyerPrisonerRespVOList){
                if(ObjectUtil.isNotEmpty(lawyerPrisonerRespVO.getPowerOfAttorneyUrl())){
                    lawyerPrisonerRespVO.setPowerOfAttorneyUrl(wbCommonService.getFile(lawyerPrisonerRespVO.getPowerOfAttorneyUrl()));
                }
            }
            return lawyerPrisonerRespVOList;
        }
        return new ArrayList<>();
    }

//    @Override
//    public void checkLawyerCommission(List<LawyerPrisonerSaveReqVO> saveReqVOList) {
//        Map<String, List<LawyerPrisonerSaveReqVO>> repeatMap = new HashMap<>();
//
//        for (LawyerPrisonerSaveReqVO prisonerSaveReqVO : saveReqVOList) {
//            if ("1".equals(prisonerSaveReqVO.getStatus())) {
//                List<LawyerPrisonerSaveReqVO> tempList = new ArrayList<>();
//                if (repeatMap.containsKey(prisonerSaveReqVO.getJgrybm())) {
//                    tempList = repeatMap.get(prisonerSaveReqVO.getJgrybm());
//                }
//                tempList.add(prisonerSaveReqVO);
//                repeatMap.put(prisonerSaveReqVO.getJgrybm(), tempList);
//            }
//        }
//        if (CollectionUtil.isNotEmpty(repeatMap)) {
//            for (Map.Entry<String, List<LawyerPrisonerSaveReqVO>> temp : repeatMap.entrySet()) {
//                if (temp.getValue().size() > 2) {
//                    PrisonerVwRespVO prisonerVwRespVO = prisonerService.getPrisonerByJgrybm(temp.getKey());
//                    throw new ServerException(String.format("%s委托中律师已超过2人，请结束其他律师委托后，再进行操作！", prisonerVwRespVO.getXm()));
//                } else {
//                    //判断数据库是否存在
//                    List<JSONObject> lawyerPrisonerDOList = lawyerPrisonerDao.getLawyerPrisonerByJgrybm(temp.getKey());
//                    if(CollectionUtil.isNotEmpty(lawyerPrisonerDOList)){
//                        int size = 0;
//                        Set<String> idSet = new HashSet<>();
//                        lawyerPrisonerDOList.forEach(x->{
//                            idSet.add(x.getString("id"));
//                        });
//                        for(LawyerPrisonerSaveReqVO lawyerPrisonerSaveReqVO:temp.getValue()){
//                            if(ObjectUtil.isEmpty(lawyerPrisonerSaveReqVO.getId())){
//                                size += 1;
//                            }else {
//                                idSet.add(lawyerPrisonerSaveReqVO.getId());
//                            }
//                        }
//                        if((size+idSet.size()) > 2){
//                            PrisonerVwRespVO prisonerVwRespVO = prisonerService.getPrisonerByJgrybm(temp.getKey());
//                            throw new ServerException(String.format("%s委托中律师已超过2人，请结束其他律师委托后，再进行操作！", prisonerVwRespVO.getXm()));
//                        }
//                    }
//                }
//            }
//        }
//    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean savePrisonerList(String lawyerId, List<LawyerPrisonerSaveReqVO> prisonerList) {
        if(CollectionUtil.isEmpty(prisonerList)){
            return true;
        }
        for(LawyerPrisonerSaveReqVO prisonerSaveReqVO:prisonerList){
            prisonerSaveReqVO.setLawyerId(lawyerId);
            if(ObjectUtil.isEmpty(prisonerSaveReqVO.getStatus())){
                prisonerSaveReqVO.setStatus("1");
            }
            if(ObjectUtil.isNotEmpty(prisonerSaveReqVO.getPowerOfAttorneyUrl())){
                String powerOfAttorneyUrlId = null;
                if(ObjectUtil.isNotEmpty(prisonerSaveReqVO.getId())){
                    LambdaQueryWrapper<LawyerPrisonerDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                    lambdaQueryWrapper.select(LawyerPrisonerDO::getPowerOfAttorneyUrl).eq(LawyerPrisonerDO::getId,prisonerSaveReqVO.getId());
                    LawyerPrisonerDO prisonerDO = getOne(lambdaQueryWrapper);
                    if(ObjectUtil.isNotEmpty(prisonerDO) && ObjectUtil.isNotEmpty(prisonerDO.getPowerOfAttorneyUrl())){
                        powerOfAttorneyUrlId = prisonerDO.getPowerOfAttorneyUrl();
                    }
                }
                String powerOfAttorneyUrl = wbCommonService.saveFile(powerOfAttorneyUrlId,prisonerSaveReqVO.getPowerOfAttorneyUrl());
                prisonerSaveReqVO.setPowerOfAttorneyUrl(powerOfAttorneyUrl);
            }
        }

        List<LawyerPrisonerDO> lawyerPrisonerList = BeanUtils.toBean(prisonerList,LawyerPrisonerDO.class);
        return saveOrUpdateBatch(lawyerPrisonerList);
    }

    @Override
    public JSONObject checkJgryAssociation(String jgrybh) {
        return lawyerPrisonerDao.checkJgryAssociation(jgrybh);
    }

    @Override
    public List<JSONObject> getLawyerJsonList(String jgrybm,List<String> laywerIdList) {
        return lawyerPrisonerDao.getLawyerJsonList(jgrybm,laywerIdList);
    }

    @Override
    public JSONObject sameCaseJudgmentByJgrybm(String jgrybms) {
        if(ObjectUtil.isEmpty(jgrybms)){
            throw new ServerException("监管人员编码不可为空");
        }

        List<String> jgrybmList = Arrays.asList(jgrybms.split(","));

        Map<String,String> jgrybmMap = new HashMap<>();
        for(String jgrybm:jgrybmList){
            jgrybmMap.put(jgrybm,jgrybm);
        }

        JSONObject res = new JSONObject();
        res.put("prompt",false);
        StringBuilder msg = new StringBuilder();
        for(String jgrybm:jgrybmList){
            PageResult<SameCaseManageBO> result = sameCaseManageService.manageListPage(1,1000,jgrybm);
            if(CollectionUtil.isNotEmpty(result.getList())){
                Map<String,String> jgryMap = new HashMap<>();
                for(SameCaseManageBO sameCaseManageBO:result.getList()){
                    jgryMap.put(sameCaseManageBO.getJgrybm(),sameCaseManageBO.getXm());
                }
                StringBuilder tempBuilder = new StringBuilder();
                for(int i=0;i<result.getList().size();i++){
                    if(jgrybm.equals(result.getList().get(i).getJgrybm())){
                        continue;
                    }else {
                        if(jgrybmMap.containsKey(result.getList().get(i).getJgrybm())){
                            res.put("prompt",true);
                            if(ObjectUtil.isEmpty(tempBuilder.toString())){
                                tempBuilder.append("(").append(jgryMap.get(jgrybm)).append("和");
                            }
                            tempBuilder.append(result.getList().get(i).getXm());
                            if(i<result.getList().size()-1){
                                tempBuilder.append("、");
                            }
                        }
                    }
                }
                if(ObjectUtil.isNotEmpty(tempBuilder.toString())){
                    String tempMsg = tempBuilder.toString();
                    if(tempMsg.endsWith("、")){
                        tempMsg = tempMsg.substring(0,tempMsg.length()-1);
                    }
                    msg.append(tempMsg).append(")为同案人员；");
                }
            }
        }
        if(ObjectUtil.isNotEmpty(msg.toString())){
            res.put("msg",msg.toString());
        }
        return res;
    }
}
