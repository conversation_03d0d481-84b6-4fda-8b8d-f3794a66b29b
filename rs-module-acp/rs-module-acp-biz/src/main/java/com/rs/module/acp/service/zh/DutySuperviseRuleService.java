package com.rs.module.acp.service.zh;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.zh.vo.*;
import com.rs.module.acp.entity.zh.DutySuperviseRuleDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 综合管理-值班管理-值班督导规则配置 Service 接口
 *
 * <AUTHOR>
 */
public interface DutySuperviseRuleService extends IBaseService<DutySuperviseRuleDO>{

    /**
     * 创建综合管理-值班管理-值班督导规则配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createDutySuperviseRule(@Valid DutySuperviseRuleSaveReqVO createReqVO);

    /**
     * 更新综合管理-值班管理-值班督导规则配置
     *
     * @param updateReqVO 更新信息
     */
    void updateDutySuperviseRule(@Valid DutySuperviseRuleSaveReqVO updateReqVO);

    /**
     * 删除综合管理-值班管理-值班督导规则配置
     *
     * @param id 编号
     */
    void deleteDutySuperviseRule(String id);

    /**
     * 获得综合管理-值班管理-值班督导规则配置
     *
     * @param id 编号
     * @return 综合管理-值班管理-值班督导规则配置
     */
    DutySuperviseRuleDO getDutySuperviseRule(String id);

    /**
    * 获得综合管理-值班管理-值班督导规则配置分页
    *
    * @param pageReqVO 分页查询
    * @return 综合管理-值班管理-值班督导规则配置分页
    */
    PageResult<DutySuperviseRuleDO> getDutySuperviseRulePage(DutySuperviseRulePageReqVO pageReqVO);

    /**
    * 获得综合管理-值班管理-值班督导规则配置列表
    *
    * @param listReqVO 查询条件
    * @return 综合管理-值班管理-值班督导规则配置列表
    */
    List<DutySuperviseRuleDO> getDutySuperviseRuleList(DutySuperviseRuleListReqVO listReqVO);


    String createOrUpdateDutySuperviseRule(@Valid DutySuperviseRuleSaveReqVO createReqVO);
}
