package com.rs.module.acp.controller.admin.gj.vo.common;

/**
 *
 * <AUTHOR>
 * @Date 2025/3/20 13:54
 */

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@ApiModel(description = "管理后台 - 管教业务- 审批 Request VO")
@Data
public class GjApproveReqVO {

    @ApiModelProperty("id")
    @NotBlank(message = "ID不能为空")
    private String id;

    @ApiModelProperty("被监管人员编码")
    private String jgrybm;

    @ApiModelProperty("被监管人员编码")
    private String jgryxm;

    @ApiModelProperty("审批结果 字典编码： ZD_BPM_APPROVE_STATUS")
    private String approvalResult;

    @ApiModelProperty("审批意见")
    private String approvalComments;

    //@ApiModelProperty("审批人身份证号")
    //private String approverSfzh;
    //
    //@ApiModelProperty("审批人姓名")
    //private String approverXm;

    //@ApiModelProperty("审批时间")
    //@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    //@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    //private Date approverTime;


}
