package com.rs.module.acp.controller.admin.db.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-羁押业务-出所登记（看守所）分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OutRecordKssPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("批准/执行机关")
    private String pzzxjg;

    @ApiModelProperty("批准/执行人")
    private String pzzxr;

    @ApiModelProperty("批准日期")
    private Date pzrq;

    @ApiModelProperty("出所时间")
    private Date cssj;

    @ApiModelProperty("出所原因")
    private String csyy;

    @ApiModelProperty("出所去向")
    private String csqx;

    @ApiModelProperty("出所凭证")
    private String cspz;

    @ApiModelProperty("出所凭证文书号")
    private String cspzwsh;

    @ApiModelProperty("出所凭证文书号")
    private String cspzwsdz;

    @ApiModelProperty("财物交接情况")
    private String cwjjqk;

    @ApiModelProperty("财物交接实体")
    private String cwjjst;

    @ApiModelProperty("档案材料移交情况")
    private String daclyjqk;

    @ApiModelProperty("档案材料移交实体")
    private String daclyjst;

    @ApiModelProperty("是否检查发现书信或者物品")
    private Short sfjcfxshhzwp;

    @ApiModelProperty("检查发现书信或者物品情况记录")
    private String jcfxsxhzwpqkjl;

    @ApiModelProperty("书信或者物品照片")
    private String sxhzwpzp;

    @ApiModelProperty("是否为他人捎带物品")
    private Short sfwtrsdwp;

    @ApiModelProperty("捎带物品类型")
    private String sdwplx;

    @ApiModelProperty("是否有帮其他人串供行为")
    private Short sfybzqtrcgxw;

    @ApiModelProperty("详细情况记录")
    private String xxqkjl;

    @ApiModelProperty("是否发现其他违法犯罪行为")
    private Short sffxqtwffzxs;

    @ApiModelProperty("其他违法犯罪行为")
    private String qtwffzxw;

    @ApiModelProperty("通报办案机关及办案机关回复情况")
    private String tbbajgjbajghfqk;

    @ApiModelProperty("转去公安监所编码")
    private String zqgajsbm;

    @ApiModelProperty("转去公安监所名称")
    private String zqgajsmc;

    @ApiModelProperty("经办人身份证号")
    private String jbrsfzh;

    @ApiModelProperty("经办人")
    private String jbr;

    @ApiModelProperty("经办时间")
    private Date jbsj;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("办理状态")
    private String spzt;

    @ApiModelProperty("办理状态")
    private String status;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("手环ID")
    private String shid;

    @ApiModelProperty("手环绑定状态")
    private String shbdzt;

    @ApiModelProperty("手环解除绑定时间")
    private Date sdjcbdsj;

    @ApiModelProperty("离所确认状态")
    private String lsqrzt;

    @ApiModelProperty("离所确认去向")
    private String lsqrqx;

    @ApiModelProperty("离所确认备注")
    private String lsqrbz;

    @ApiModelProperty("离所确认经办人身份证号")
    private String lsqrjbrsfzh;

    @ApiModelProperty("离所确认经办人身份证号")
    private String lsqrjbr;

    @ApiModelProperty("离所确认经办人身份证号")
    private Date lsqrjbsj;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;

    @ApiModelProperty("当前阶段")
    private String currentStep;
}
