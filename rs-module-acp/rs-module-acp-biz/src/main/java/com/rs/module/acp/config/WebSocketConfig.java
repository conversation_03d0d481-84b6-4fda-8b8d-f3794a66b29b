package com.rs.module.acp.config;

import com.rs.module.acp.component.QingyanLocalsenseSocketComponent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;
import org.springframework.web.socket.server.standard.ServletServerContainerFactoryBean;

@Configuration
public class WebSocketConfig {

    /**
     * 注册ServerEndpointExporter Bean。
     * ServerEndpointExporter是Spring提供的一个工具类，
     * 它会扫描并注册所有使用@ServerEndpoint注解的类为WebSocket端点。
     *
     * @return ServerEndpointExporter实例
     */
    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }

    /**
     * 通信文本消息和二进制缓存区大小
     * 避免报文过大时，Websocket 1009 错误
     */
    @Bean
    public ServletServerContainerFactoryBean createWebSocketContainer() {
        ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();
        // 文本/二进制消息最大缓冲区（10MB）
        container.setMaxTextMessageBufferSize(1024 * 1024 * 10);
        container.setMaxBinaryMessageBufferSize(1024 * 1024 * 10);
        // 最大会话空闲超时时间（1小时）
        container.setMaxSessionIdleTimeout(60 * 60 * 1000L);
        return container;
    }
    @Bean
    public QingyanLocalsenseSocketComponent qingyanLocalsenseSocketComponent(QingyanDeviceProperties deviceProperties) {
        return new QingyanLocalsenseSocketComponent(deviceProperties);
    }
}
