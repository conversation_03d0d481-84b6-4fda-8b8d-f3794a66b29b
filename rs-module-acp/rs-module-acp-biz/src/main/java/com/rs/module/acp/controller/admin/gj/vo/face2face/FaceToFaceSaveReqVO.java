package com.rs.module.acp.controller.admin.gj.vo.face2face;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-面对面管理新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FaceToFaceSaveReqVO extends BaseVO {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("禁闭监室id")
    @NotEmpty(message = "禁闭监室id不能为空")
    private String roomId;

    @ApiModelProperty("数据来源（字典：ZD_DATA_SOURCES）")
    private String dataSources;

    @ApiModelProperty("检查民警身份证号")
    private String checkPoliceSfzh;

    @ApiModelProperty("检查民警姓名")
    @NotEmpty(message = "检查民警姓名不能为空")
    private String checkPolice;

    @ApiModelProperty("检查时间")
    private Date checkTime;

    @ApiModelProperty("情况记录")
    private String situationRecord;

    @ApiModelProperty("拍照")
    private String snapPhoto;

}
