package com.rs.module.acp.dao.wb;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.LeadershipReceptionDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-窗口业务-所领导接待登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface LeadershipReceptionDao extends IBaseDao<LeadershipReceptionDO> {


    default PageResult<LeadershipReceptionDO> selectPage(LeadershipReceptionPageReqVO reqVO) {
        Page<LeadershipReceptionDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<LeadershipReceptionDO> wrapper = new LambdaQueryWrapperX<LeadershipReceptionDO>()
            .likeIfPresent(LeadershipReceptionDO::getLeaderName, reqVO.getLeaderName())
            .likeIfPresent(LeadershipReceptionDO::getCompanionName, reqVO.getCompanionName())
            .betweenIfPresent(LeadershipReceptionDO::getReceptionStartTime, reqVO.getReceptionStartTime())
            .betweenIfPresent(LeadershipReceptionDO::getReceptionEndTime, reqVO.getReceptionEndTime())
            .eqIfPresent(LeadershipReceptionDO::getType, reqVO.getType())
            .eqIfPresent(LeadershipReceptionDO::getReceptionReason, reqVO.getReceptionReason())
            .eqIfPresent(LeadershipReceptionDO::getReceptionAddress, reqVO.getReceptionAddress())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(LeadershipReceptionDO::getAddTime);
        }
        Page<LeadershipReceptionDO> leadershipReceptionPage = selectPage(page, wrapper);
        return new PageResult<>(leadershipReceptionPage.getRecords(), leadershipReceptionPage.getTotal());
    }
    default List<LeadershipReceptionDO> selectList(LeadershipReceptionListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<LeadershipReceptionDO>()
            .likeIfPresent(LeadershipReceptionDO::getLeaderName, reqVO.getLeaderName())
            .likeIfPresent(LeadershipReceptionDO::getCompanionName, reqVO.getCompanionName())
            .betweenIfPresent(LeadershipReceptionDO::getReceptionStartTime, reqVO.getReceptionStartTime())
            .betweenIfPresent(LeadershipReceptionDO::getReceptionEndTime, reqVO.getReceptionEndTime())
            .eqIfPresent(LeadershipReceptionDO::getType, reqVO.getType())
            .eqIfPresent(LeadershipReceptionDO::getReceptionReason, reqVO.getReceptionReason())
            .eqIfPresent(LeadershipReceptionDO::getReceptionAddress, reqVO.getReceptionAddress())
        .orderByDesc(LeadershipReceptionDO::getAddTime));    }


    }
