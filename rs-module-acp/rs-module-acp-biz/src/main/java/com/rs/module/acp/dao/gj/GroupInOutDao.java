package com.rs.module.acp.dao.gj;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.groupinout.GroupInOutListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.groupinout.GroupInOutPageReqVO;
import com.rs.module.acp.entity.gj.GroupInOutDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 实战平台-管教业务-集体出入 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface GroupInOutDao extends IBaseDao<GroupInOutDO> {

    default PageResult<GroupInOutDO> selectPage(GroupInOutPageReqVO reqVO) {
        Page<GroupInOutDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<GroupInOutDO> wrapper = new LambdaQueryWrapperX<GroupInOutDO>()
                .eqIfPresent(GroupInOutDO::getBusinessType, reqVO.getBusinessType())
                .eqIfPresent(GroupInOutDO::getIsRoom, reqVO.getIsRoom())
                .eqIfPresent(GroupInOutDO::getObjectCount, reqVO.getObjectCount())
                .eqIfPresent(GroupInOutDO::getRemark, reqVO.getRemark());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(GroupInOutDO::getAddTime);
        }
        Page<GroupInOutDO> groupInOutPage = selectPage(page, wrapper);
        return new PageResult<>(groupInOutPage.getRecords(), groupInOutPage.getTotal());
    }

    default List<GroupInOutDO> selectList(GroupInOutListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<GroupInOutDO>()
                .eqIfPresent(GroupInOutDO::getBusinessType, reqVO.getBusinessType())
                .eqIfPresent(GroupInOutDO::getIsRoom, reqVO.getIsRoom())
                .eqIfPresent(GroupInOutDO::getObjectCount, reqVO.getObjectCount())
                .eqIfPresent(GroupInOutDO::getRemark, reqVO.getRemark())
                .orderByDesc(GroupInOutDO::getAddTime));
    }

    void deleteRoom(@Param("groupInOutId") String groupInOutId);

    
    void deleteJgry(@Param("groupInOutId") String groupInOutId);

    void delete(@Param("groupInOutId") String groupInOutId);



}
