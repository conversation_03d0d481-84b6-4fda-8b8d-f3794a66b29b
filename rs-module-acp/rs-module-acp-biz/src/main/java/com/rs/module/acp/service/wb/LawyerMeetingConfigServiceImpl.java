package com.rs.module.acp.service.wb;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.LawyerMeetingConfigDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.LawyerMeetingConfigDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-律师会见-会见方式、预约时段配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LawyerMeetingConfigServiceImpl extends BaseServiceImpl<LawyerMeetingConfigDao, LawyerMeetingConfigDO> implements LawyerMeetingConfigService {

}
