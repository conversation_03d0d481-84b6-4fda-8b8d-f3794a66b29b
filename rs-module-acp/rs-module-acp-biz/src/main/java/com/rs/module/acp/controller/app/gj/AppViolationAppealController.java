package com.rs.module.acp.controller.app.gj;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.violationappeal.ViolationAppealAppCreateReqVO;
import com.rs.module.acp.controller.admin.gj.vo.violationappeal.ViolationAppealRespVO;
import com.rs.module.acp.controller.admin.gj.vo.violationappeal.ViolationAppealSaveReqVO;
import com.rs.module.acp.entity.gj.ViolationAppealDO;
import com.rs.module.acp.service.gj.violationappeal.ViolationAppealService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-管教业务-违规申诉")
@RestController
@RequestMapping("/app/acp/gj/violationAppeal")
@Validated
public class AppViolationAppealController {

    @Resource
    private ViolationAppealService violationAppealService;

    @PostMapping("/appeal")
    @ApiOperation(value = "App-申诉")
    public CommonResult<String> appeal(@Valid @RequestBody ViolationAppealAppCreateReqVO createReqVO) {
        return success(violationAppealService.appeal(createReqVO));
    }

    @GetMapping("/applyList")
    @ApiOperation(value = "App-违规申诉申请列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true),
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码", required = true),
            @ApiImplicitParam(name = "type", value = "状态 01:全部，02:待审诉，03:已申诉", required = true)
    })
    public CommonResult<PageResult<ViolationAppealRespVO>> applyList(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                               @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                                               @RequestParam(value = "jgrybm") String jgrybm,
                                                               @RequestParam(value = "type", defaultValue = "01") String type) {
        PageResult<ViolationAppealDO> pageResult = violationAppealService.applyList(pageNo, pageSize, jgrybm, type);
        return success(BeanUtils.toBean(pageResult, ViolationAppealRespVO.class));
    }


    @GetMapping("/applyRecord")
    @ApiOperation(value = "App-违规申诉记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小"),
            @ApiImplicitParam(name = "jgrybm", value = "被监管人员编码"),
            @ApiImplicitParam(name = "type", value = "周期类型 1 全部，2 今天，3 昨天，4 近一周")
    })
    public CommonResult<PageResult<ViolationAppealRespVO>> applyRecord(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                                             @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                                             @RequestParam("jgrybm") String jgrybm,
                                                                             @RequestParam("type") String type) {
        PageResult<ViolationAppealDO> pageResult = violationAppealService.applyRecord(pageNo, pageSize, jgrybm, type);
        return success(BeanUtils.toBean(pageResult, ViolationAppealRespVO.class));
    }

}

