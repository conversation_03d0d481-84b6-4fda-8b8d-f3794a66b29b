package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.LawyerViolationDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-律师违规 Service 接口
 *
 * <AUTHOR>
 */
public interface LawyerViolationService extends IBaseService<LawyerViolationDO>{

    /**
     * 创建实战平台-窗口业务-律师违规
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createLawyerViolation(@Valid LawyerViolationSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-律师违规
     *
     * @param updateReqVO 更新信息
     */
    void updateLawyerViolation(@Valid LawyerViolationSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-律师违规
     *
     * @param id 编号
     */
    void deleteLawyerViolation(String id);

    /**
     * 获得实战平台-窗口业务-律师违规
     *
     * @param id 编号
     * @return 实战平台-窗口业务-律师违规
     */
    LawyerViolationDO getLawyerViolation(String id);

    /**
    * 获得实战平台-窗口业务-律师违规分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-窗口业务-律师违规分页
    */
    PageResult<LawyerViolationDO> getLawyerViolationPage(LawyerViolationPageReqVO pageReqVO);

    /**
    * 获得实战平台-窗口业务-律师违规列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-窗口业务-律师违规列表
    */
    List<LawyerViolationDO> getLawyerViolationList(LawyerViolationListReqVO listReqVO);

    /**
     * 获得实战平台-窗口业务-根据律师ID律师违规列表
     *
     * @param lawyerId 律师ID
     * @param pageNo 页码
     * @param pageSize 每页多少
     * @return 实战平台-窗口业务-律师违规列表
     */
    PageResult<LawyerViolationRespVO> getLawyerViolationListByLawyerId(String lawyerId, int pageNo, int pageSize);

}
