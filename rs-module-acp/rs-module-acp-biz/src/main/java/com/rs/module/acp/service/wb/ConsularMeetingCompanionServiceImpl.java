package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.ConsularMeetingCompanionDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.ConsularMeetingCompanionDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-领事外事会见同行登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ConsularMeetingCompanionServiceImpl extends BaseServiceImpl<ConsularMeetingCompanionDao, ConsularMeetingCompanionDO> implements ConsularMeetingCompanionService {

    @Resource
    private ConsularMeetingCompanionDao consularMeetingCompanionDao;

    @Autowired
    private WbCommonService wbCommonService;

    @Override
    public String createConsularMeetingCompanion(ConsularMeetingCompanionSaveReqVO createReqVO) {
        // 插入
        ConsularMeetingCompanionDO consularMeetingCompanion = BeanUtils.toBean(createReqVO, ConsularMeetingCompanionDO.class);
        consularMeetingCompanionDao.insert(consularMeetingCompanion);
        // 返回
        return consularMeetingCompanion.getId();
    }

    @Override
    public void updateConsularMeetingCompanion(ConsularMeetingCompanionSaveReqVO updateReqVO) {
        // 校验存在
        validateConsularMeetingCompanionExists(updateReqVO.getId());
        // 更新
        ConsularMeetingCompanionDO updateObj = BeanUtils.toBean(updateReqVO, ConsularMeetingCompanionDO.class);
        consularMeetingCompanionDao.updateById(updateObj);
    }

    @Override
    public void deleteConsularMeetingCompanion(String id) {
        // 校验存在
        validateConsularMeetingCompanionExists(id);
        // 删除
        consularMeetingCompanionDao.deleteById(id);
    }

    private void validateConsularMeetingCompanionExists(String id) {
        if (consularMeetingCompanionDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-领事外事会见同行登记数据不存在");
        }
    }

    @Override
    public ConsularMeetingCompanionDO getConsularMeetingCompanion(String id) {
        return consularMeetingCompanionDao.selectById(id);
    }

    @Override
    public PageResult<ConsularMeetingCompanionDO> getConsularMeetingCompanionPage(ConsularMeetingCompanionPageReqVO pageReqVO) {
        return consularMeetingCompanionDao.selectPage(pageReqVO);
    }

    @Override
    public List<ConsularMeetingCompanionDO> getConsularMeetingCompanionList(ConsularMeetingCompanionListReqVO listReqVO) {
        return consularMeetingCompanionDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveConsularMeetingCompanionList(List<ConsularMeetingCompanionSaveReqVO> compainList, String consularMeetingId) {

        List<ConsularMeetingCompanionDO> companionDOList = BeanUtils.toBean(compainList,ConsularMeetingCompanionDO.class);
        for(ConsularMeetingCompanionDO companionDO:companionDOList){
            if(ObjectUtil.isNotEmpty(companionDO.getAttachmentUrl())){
                companionDO.setAttachmentUrl(wbCommonService.saveFile(null,companionDO.getAttachmentUrl()));
            }
            companionDO.setConsularMeetingId(consularMeetingId);
        }

        return saveBatch(companionDOList);
    }

    @Override
    public List<ConsularMeetingCompanionRespVO> getConsularMeetingCompanionListByconsularMeetingId(String consularMeetingId) {

        LambdaQueryWrapper<ConsularMeetingCompanionDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(ConsularMeetingCompanionDO::getId,ConsularMeetingCompanionDO::getName,
                ConsularMeetingCompanionDO::getCompanionType,ConsularMeetingCompanionDO::getGender,
                ConsularMeetingCompanionDO::getIdCard,ConsularMeetingCompanionDO::getAttachmentUrl);
        lambdaQueryWrapper.eq(ConsularMeetingCompanionDO::getConsularMeetingId,consularMeetingId);
        List<ConsularMeetingCompanionDO> companionDOList = list(lambdaQueryWrapper);
        if(CollectionUtil.isEmpty(companionDOList)){
            return new ArrayList<>();
        }

        List<ConsularMeetingCompanionRespVO> companionRespVOList = BeanUtils.toBean(companionDOList,ConsularMeetingCompanionRespVO.class);
        for(ConsularMeetingCompanionRespVO companionRespVO:companionRespVOList){
            if(ObjectUtil.isNotEmpty(companionRespVO.getAttachmentUrl())){
                companionRespVO.setAttachmentUrl(wbCommonService.getFile(companionRespVO.getAttachmentUrl()));
            }
        }
        return companionRespVOList;
    }
}
