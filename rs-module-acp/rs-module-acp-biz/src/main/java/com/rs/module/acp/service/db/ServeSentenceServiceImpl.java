package com.rs.module.acp.service.db;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.StringUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.UserApi;
import com.rs.adapter.bsp.api.dto.OrgUserRespDTO;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.acp.cons.CommonConstants;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.sys.MsgAddVO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.base.util.MsgUtil;
import com.rs.module.base.vo.ApproveReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.ServeSentenceDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.db.ServeSentenceDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-收押业务-留所服刑 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ServeSentenceServiceImpl extends BaseServiceImpl<ServeSentenceDao, ServeSentenceDO> implements ServeSentenceService {

    @Resource
    private ServeSentenceDao serveSentenceDao;

    @Autowired
    private PrisonerService prisonerService;

    @Autowired
    private BspApi bspApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createServeSentence(ServeSentenceSaveReqVO createReqVO) {
        // 插入
        ServeSentenceDO serveSentence = BeanUtils.toBean(createReqVO, ServeSentenceDO.class);

        serveSentence.setId(StringUtil.getGuid32());
        serveSentence.setStatus("10-1");

        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", serveSentence.getId());
        variables.put("busType", MsgBusTypeEnum.JY_ZFGL_LSFX.getCode());
        String msgUrl = String.format("/detentionBusiness/lsfx?curId=%s&stepType=approve&jgrybm=%s",serveSentence.getId(),serveSentence.getJgrybm());
        JSONObject result = BspApprovalUtil.defaultStartProcess("jiyaguanlizuifanguanliliusuofuxing",serveSentence.getId(),"",msgUrl,variables);
//            JSONObject result = BspApprovalUtil.defaultStartProcess("chuangkouyewuceshiliuc1",familyMeeting.getId(),"",msgUrl,variables);
        JSONObject data = result.getJSONObject("data");
        JSONObject bpmTrail = data.getJSONObject("bpmTrail");
        serveSentence.setActInstId(bpmTrail.getString("actInstId"));
        serveSentence.setTaskId(bpmTrail.getString("taskId"));

        serveSentenceDao.insert(serveSentence);
        // 返回
        return serveSentence.getId();
    }

    @Override
    public void updateServeSentence(ServeSentenceSaveReqVO updateReqVO) {
        // 校验存在
        validateServeSentenceExists(updateReqVO.getId());
        // 更新
        ServeSentenceDO updateObj = BeanUtils.toBean(updateReqVO, ServeSentenceDO.class);
        serveSentenceDao.updateById(updateObj);
    }

    @Override
    public void deleteServeSentence(String id) {
        // 校验存在
        validateServeSentenceExists(id);
        // 删除
        serveSentenceDao.deleteById(id);
    }

    private void validateServeSentenceExists(String id) {
        if (serveSentenceDao.selectById(id) == null) {
            throw new ServerException("实战平台-收押业务-留所服刑数据不存在");
        }
    }

    @Override
    public ServeSentenceDO getServeSentence(String id) {
        return serveSentenceDao.selectById(id);
    }

    @Override
    public PageResult<ServeSentenceDO> getServeSentencePage(ServeSentencePageReqVO pageReqVO) {
        return serveSentenceDao.selectPage(pageReqVO);
    }

    @Override
    public List<ServeSentenceDO> getServeSentenceList(ServeSentenceListReqVO listReqVO) {
        return serveSentenceDao.selectList(listReqVO);
    }


    @Override
    public void approve(ApproveReqVO approveReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        ServeSentenceDO serveSentenceDO =  serveSentenceDao.selectById(approveReqVO.getId());
        PrisonerVwRespVO prisonerVwRespVO = prisonerService.getPrisonerByJgrybm(serveSentenceDO.getJgrybm());

        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(serveSentenceDO.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }

        //从当前登录用户信息设置审批信息默认值
        if(StringUtil.isEmpty(approveReqVO.getApproverXm())) approveReqVO.setApproverXm(sessionUser.getName());
        if(StringUtil.isEmpty(approveReqVO.getApproverSfzh())) approveReqVO.setApproverSfzh(sessionUser.getIdCard());
        if(approveReqVO.getApproverTime() == null) approveReqVO.setApproverTime(new Date());
        BspApproceStatusEnum isApprove = BspApproceStatusEnum.NOT_PASSED;
        String businessStatus = "";
        //1:审批通过，0：审批不通过
        if("1".equals(approveReqVO.getApprovalResult())){
            isApprove = BspApproceStatusEnum.PASSED;
        }
        approveReqVO.setActInstId(serveSentenceDO.getActInstId());
        approveReqVO.setTaskId(serveSentenceDO.getTaskId());
        approveReqVO.setDefKey("jiyaguanlizuifanguanliliusuofuxing");

        String msgTit = "";
        String msgUrl = "";
        Map<String, Object> variables = new HashMap<>();
        variables.put("busType", MsgBusTypeEnum.JY_ZFGL_LSFX.getCode());//ZD_MSG_BUSTYPE 字典对应code
        variables.put("addTime", DateUtil.format(serveSentenceDO.getAddTime(),"yyyy-MM-dd HH:mm:ss"));
        variables.put("roomName", prisonerVwRespVO.getRoomName());
        variables.put("prisonerName", serveSentenceDO.getJgryxm());

        Map<String,String> approvalResult = BspApprovalUtil.approvalProcessMap(approveReqVO,
                isApprove,
                msgTit,
                msgUrl,
                "1".equals(approveReqVO.getApprovalResult())?false:true,
                null,
                null,
                HttpUtils.getAppCode()
        );

        if("1".equals(approveReqVO.getApprovalResult())){
            serveSentenceDO.setStatus("1");
        }else {
            serveSentenceDO.setStatus("11");
        }

        //发送消息通知发起人
        MsgAddVO msgAddVO = new MsgAddVO();
        Map<String, Object> contentData = new HashMap<>();

        msgAddVO.setBusinessId(serveSentenceDO.getId());
        msgAddVO.setJgrybm(serveSentenceDO.getJgrybm());
        msgAddVO.setPcid(serveSentenceDO.getId());

        contentData.put("roomName",prisonerVwRespVO.getRoomName());
        contentData.put("jgryxm",prisonerVwRespVO.getXm());
        contentData.put("addUserName",serveSentenceDO.getAddUserName());
        contentData.put("addTime",DateUtil.format(serveSentenceDO.getAddTime(),"yyyy-MM-dd HH:mm:ss"));
        contentData.put("approvalResult","1".equals(approveReqVO.getApprovalResult())?"通过":"不通过");
        msgAddVO.setMsgType(MsgBusTypeEnum.JY_ZFGL_LSFX.getCode());
        msgAddVO.setModuleCode("ACP_JYGL_ZFGL_LSFX");
        msgAddVO.setUrl(String.format("/detentionBusiness/lsfx?curId=%s&stepType=info&jgrybm=%s",serveSentenceDO.getId(),serveSentenceDO.getJgrybm()));
        msgAddVO.setContentData(contentData);
        msgAddVO.setSpecify(true);

        OrgUserRespDTO userRespDTO = bspApi.getUserByIdCard(serveSentenceDO.getAddUser());
        ReceiveUser receiveUser = BeanUtils.toBean(userRespDTO,ReceiveUser.class);
        msgAddVO.setSpecifyReceiveUserList(Arrays.asList(receiveUser));
        MsgUtil.sendMsg(msgAddVO);

        serveSentenceDao.updateById(serveSentenceDO);
    }
}
