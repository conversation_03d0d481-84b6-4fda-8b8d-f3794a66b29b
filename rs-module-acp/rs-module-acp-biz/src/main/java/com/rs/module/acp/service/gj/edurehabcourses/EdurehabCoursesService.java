package com.rs.module.acp.service.gj.edurehabcourses;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesSaveReqVO;
import com.rs.module.acp.entity.gj.EdurehabCoursesDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-教育康复课程 Service 接口
 *
 * <AUTHOR>
 */
public interface EdurehabCoursesService extends IBaseService<EdurehabCoursesDO>{

    /**
     * 创建实战平台-管教业务-教育康复课程
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createEdurehabCourses(@Valid EdurehabCoursesSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-教育康复课程
     *
     * @param updateReqVO 更新信息
     */
    void updateEdurehabCourses(@Valid EdurehabCoursesSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-教育康复课程
     *
     * @param id 编号
     */
    void deleteEdurehabCourses(String id);

    /**
     * 获得实战平台-管教业务-教育康复课程
     *
     * @param id 编号
     * @return 实战平台-管教业务-教育康复课程
     */
    EdurehabCoursesDO getEdurehabCourses(String id);

    /**
    * 获得实战平台-管教业务-教育康复课程分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-教育康复课程分页
    */
    PageResult<EdurehabCoursesDO> getEdurehabCoursesPage(EdurehabCoursesPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-教育康复课程列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-教育康复课程列表
    */
    List<EdurehabCoursesDO> getEdurehabCoursesList(EdurehabCoursesListReqVO listReqVO);


}
