package com.rs.module.acp.job.punishment;

import com.rs.module.acp.dao.gj.PunishmentDao;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 处罚业务
 * <AUTHOR>
 * @date 2025/6/14 11:04
 */
@Component
public class PunishmentJob {

    @Resource
    private PunishmentDao punishmentDao;

    /**
     * 处罚待解除，状态更新
     * <AUTHOR>
     * @date 2025/6/14 11:05
     * @param []
     * @return void
     */
    @XxlJob("penaltyRemainsLifted")
    public void penaltyRemainsLifted() {
        // 待解除
        XxlJobHelper.log("-----开始-------");
        XxlJobHelper.log("处罚-待解除状态更新");
        try {
            punishmentDao.updatePenaltyRemainsLifted(new Date());
        } catch (Exception e) {
            XxlJobHelper.log("处罚使用-待解除-状态任务异常：{}", e.getMessage());
        }
        XxlJobHelper.log("处罚使用-待解除-状态任务异常:-----结束------");
    }
}
