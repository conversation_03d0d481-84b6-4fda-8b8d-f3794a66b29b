package com.rs.module.acp.util;

import com.bsp.common.util.StringUtil;
import com.rs.module.acp.controller.admin.gj.vo.aloneimprison.AloneImprisonApproveReqVO;
import com.rs.module.acp.entity.gj.AloneImprisonDO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.sys.MsgAddVO;
import com.rs.module.base.util.MsgUtil;
import com.rs.util.DicUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class ConfinementMsgSendUtil {

    private AloneImprisonApproveReqVO aloneImprisonApproveReqVO;
    private AloneImprisonDO aloneImprisonDO;

    private PrisonerVwRespVO vwRespVO;
    private String businessId;
    public ConfinementMsgSendUtil(AloneImprisonApproveReqVO aloneImprisonApproveReqVO, AloneImprisonDO aloneImprisonDO, PrisonerVwRespVO vwRespVO){
        this.aloneImprisonApproveReqVO=aloneImprisonApproveReqVO;
        this.aloneImprisonDO=aloneImprisonDO;
        this.vwRespVO=vwRespVO;
    }
    /*
    禁闭登记提醒	ACP_CONFINEMENT_REG
    禁闭提前解除登记	ACP_CONFINEMENT_REMOVE
    禁闭人员谈话教育登记	ACP_CONFINEMENT_CONVERSATION_EDUCATION
    禁闭到期解除登记	ACP_CONFINEMENT_EXPIRE_REMOVE
    禁闭人员巡控登记、定屏监控提醒	ACP_CONFINEMENT_PATROL_SCREEN
    * */
    /**
     * 主协管民警
     * 禁闭登记
     * 禁闭登记审批通过后
     */
    public void djtx(){
        String moduleCode = "CONFINEMENT_REG";
        sendCommon(moduleCode,businessId);
    }
    /**
     * 主协管民警
     * 禁闭人员谈话教育
     * 禁闭登记审批通过后
     */
    public void thjy(){
        String moduleCode = "ACP_CONFINEMENT_CONVERSATION_EDUCATION";
        sendCommon(moduleCode,businessId);
    }
    /**
     * 主协管民警
     * 禁闭到期解除
     * 禁闭到期
     */
    public void dqjc(){
        String moduleCode = "ACP_CONFINEMENT_EXPIRE_REMOVE";
        businessId = aloneImprisonDO.getOrgCode();
        sendCommon(moduleCode,businessId);
    }

    /**
     * 主协管民警
     * 禁闭提前解除
     * 解除审批通过后
     */
    public void tqjc(){
        String moduleCode = "ACP_CONFINEMENT_REMOVE";
        businessId = aloneImprisonDO.getOrgCode();
        sendCommon(moduleCode,businessId);
    }

    /**
     * 医生岗
     * 禁闭人员巡诊登记
     * 单独关押期间每天8:00
     */
    public void xzdj(){
        String moduleCode= "CONFINEMENT_XZDJ";
        sendCommon(moduleCode,businessId);
    }
    /**
     * 禁闭人员巡控登记、定屏监控提醒
     * 禁闭审批通过后，禁闭期间每天8点
     */
    public void xkdjdpjk(){
        String moduleCode= "ACP_CONFINEMENT_PATROL_SCREEN";
        sendCommon(moduleCode,businessId);
    }
    public  void sendCommon(String moduleCode,String businessId){
        MsgAddVO msgAddVO = new MsgAddVO();
        Map<String, Object> contentData = new HashMap<>();
        msgAddVO.setModuleCode(moduleCode);//模块编码
        if(StringUtil.isEmpty(businessId)){
            businessId = aloneImprisonApproveReqVO.getId();
        }
        msgAddVO.setBusinessId(businessId);//业务主键id
        msgAddVO.setToOrgCode(aloneImprisonDO.getOrgCode());//发送给具体监所的id
        if("04".equals(aloneImprisonDO.getRegisterReason())){
            contentData.put("registerReason",aloneImprisonDO.getSpecificReason());
        }else{

            contentData.put("registerReason",DicUtils.translate("ZD_DDGY_DJYY", aloneImprisonDO.getRegisterReason()));
        }
        contentData.put("roomName",aloneImprisonDO.getOldRoomName());
        contentData.put("prisonerName",vwRespVO.getXm());
        msgAddVO.setContentData(contentData);//消息内容数据
        MsgUtil.sendMsg(msgAddVO);
    }
    public static void sendMsg( AloneImprisonApproveReqVO aloneImprisonApproveReqVO, AloneImprisonDO aloneImprisonDO,PrisonerVwRespVO vwRespVO) {
        ConfinementMsgSendUtil util = new ConfinementMsgSendUtil( aloneImprisonApproveReqVO,aloneImprisonDO,vwRespVO);

    }


}
