package com.rs.module.acp.controller.admin.ds.vo.sjbs;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel(description = "管理后台 - 实战平台-数据固化-每周数据报送(看守所)新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class WeeklyDataSubmitReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("固化日期")
    private String solidificationDate;

    @ApiModelProperty("固化开始日期")
    private String startDate;

    @ApiModelProperty("固化结束日期")
    private String endDate;

    @ApiModelProperty("固化结束日期")
    private String orgCode;
}
