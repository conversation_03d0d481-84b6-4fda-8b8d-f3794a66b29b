package com.rs.module.acp.controller.app.gj.vo.prisonroom;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务--监室调整记录-app列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonRoomChangeAppListVO extends BaseVO implements TransPojo {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员编码")
    private String jgryxm;

    @ApiModelProperty("性别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XB")
    private String sex;

    private String sexName;

    @ApiModelProperty("旧监室id")
    private String oldRoomId;

    @ApiModelProperty("新监室ID")
    private String newRoomId;

    @ApiModelProperty("旧监室名称")
    private String oldRoomName;

    @ApiModelProperty("新监室名称")
    private String newRoomName;

    @ApiModelProperty("调整原因")

    @Trans(type = TransType.DICTIONARY,key = "ZD_JSTZYY")
    private String changeReason;

    @ApiModelProperty("是否调整（1：已调整，0：未调整）")
    private Short isChange;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("经办人姓名")
    private String addUserName;

    @ApiModelProperty("经办人呈批时间")
    private Date addTime;

    @ApiModelProperty("照片URL，http开头绝对路径")
    private String frontPhoto;

    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY,key = "ZD_GJ_JSTZZT")
    private String status;

    @ApiModelProperty("出生日期")
    private Date csrq;

    @ApiModelProperty("年龄计算")
    private Integer age;
}
