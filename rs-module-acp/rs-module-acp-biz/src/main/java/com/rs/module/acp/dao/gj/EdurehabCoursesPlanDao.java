package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesPlanListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesPlanPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.JqAreaVO;
import com.rs.module.acp.entity.gj.EdurehabCoursesPlanDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 实战平台-管教业务-教育康复课程计划 Dao
*
* <AUTHOR>
*/
@Mapper
public interface EdurehabCoursesPlanDao extends IBaseDao<EdurehabCoursesPlanDO> {


    default PageResult<EdurehabCoursesPlanDO> selectPage(EdurehabCoursesPlanPageReqVO reqVO) {
        Page<EdurehabCoursesPlanDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<EdurehabCoursesPlanDO> wrapper = new LambdaQueryWrapperX<EdurehabCoursesPlanDO>()
            .likeIfPresent(EdurehabCoursesPlanDO::getPlanName, reqVO.getPlanName())
            .eqIfPresent(EdurehabCoursesPlanDO::getPlanCode, reqVO.getPlanCode())
            .betweenIfPresent(EdurehabCoursesPlanDO::getStartDate, reqVO.getStartDate())
            .betweenIfPresent(EdurehabCoursesPlanDO::getEndDate, reqVO.getEndDate())
            .eqIfPresent(EdurehabCoursesPlanDO::getStatus, reqVO.getStatus())
            .eqIfPresent(EdurehabCoursesPlanDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(EdurehabCoursesPlanDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(EdurehabCoursesPlanDO::getAddTime);
        }
        Page<EdurehabCoursesPlanDO> edurehabCoursesPlanPage = selectPage(page, wrapper);
        return new PageResult<>(edurehabCoursesPlanPage.getRecords(), edurehabCoursesPlanPage.getTotal());
    }
    default List<EdurehabCoursesPlanDO> selectList(EdurehabCoursesPlanListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<EdurehabCoursesPlanDO>()
            .likeIfPresent(EdurehabCoursesPlanDO::getPlanName, reqVO.getPlanName())
            .eqIfPresent(EdurehabCoursesPlanDO::getPlanCode, reqVO.getPlanCode())
            .betweenIfPresent(EdurehabCoursesPlanDO::getStartDate, reqVO.getStartDate())
            .betweenIfPresent(EdurehabCoursesPlanDO::getEndDate, reqVO.getEndDate())
            .eqIfPresent(EdurehabCoursesPlanDO::getStatus, reqVO.getStatus())
            .eqIfPresent(EdurehabCoursesPlanDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(EdurehabCoursesPlanDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(EdurehabCoursesPlanDO::getAddTime));    }


    List<JqAreaVO> getPlanAreaByOrgCode(@Param("orgCode") String orgCode);
}
