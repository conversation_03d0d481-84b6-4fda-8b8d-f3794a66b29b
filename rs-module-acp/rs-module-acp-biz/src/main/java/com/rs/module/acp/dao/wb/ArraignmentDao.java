package com.rs.module.acp.dao.wb;

import java.util.*;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.wb.ArraignmentDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
 * 实战平台-窗口业务-提讯登记 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface ArraignmentDao extends IBaseDao<ArraignmentDO> {


    default PageResult<ArraignmentDO> selectPage(ArraignmentPageReqVO reqVO) {
        Page<ArraignmentDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ArraignmentDO> wrapper = new LambdaQueryWrapperX<ArraignmentDO>()
                .eqIfPresent(ArraignmentDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(ArraignmentDO::getHandler1Id, reqVO.getHandler1Id())
                .eqIfPresent(ArraignmentDO::getHandler1Xm, reqVO.getHandler1Xm())
                .eqIfPresent(ArraignmentDO::getHandler2Id, reqVO.getHandler2Id())
                .eqIfPresent(ArraignmentDO::getHandler2Xm, reqVO.getHandler2Xm())
                .eqIfPresent(ArraignmentDO::getHandler3Id, reqVO.getHandler3Id())
                .eqIfPresent(ArraignmentDO::getHandler3Xm, reqVO.getHandler3Xm())
                .eqIfPresent(ArraignmentDO::getRoomId, reqVO.getRoomId())
                .eqIfPresent(ArraignmentDO::getArraignmentReason, reqVO.getArraignmentReason())
                .betweenIfPresent(ArraignmentDO::getStartApplyArraignmentTime, reqVO.getStartApplyArraignmentTime())
                .betweenIfPresent(ArraignmentDO::getEndApplyArraignmentTime, reqVO.getEndApplyArraignmentTime())
                .eqIfPresent(ArraignmentDO::getArraignmentLetterNumber, reqVO.getArraignmentLetterNumber())
                .eqIfPresent(ArraignmentDO::getEvidenceType, reqVO.getEvidenceType())
                .eqIfPresent(ArraignmentDO::getEvidenceNumber, reqVO.getEvidenceNumber())
                .eqIfPresent(ArraignmentDO::getEvidenceUrl, reqVO.getEvidenceUrl())
                .eqIfPresent(ArraignmentDO::getMinorAgent, reqVO.getMinorAgent())
                .eqIfPresent(ArraignmentDO::getMinorGuardian, reqVO.getMinorGuardian())
                .eqIfPresent(ArraignmentDO::getTranslator, reqVO.getTranslator())
                .eqIfPresent(ArraignmentDO::getOtherProfessionals, reqVO.getOtherProfessionals())
                .eqIfPresent(ArraignmentDO::getProfessionalCertUrl, reqVO.getProfessionalCertUrl())
                .eqIfPresent(ArraignmentDO::getTjjglx, reqVO.getTjjglx())
                .eqIfPresent(ArraignmentDO::getTjjgmc, reqVO.getTjjgmc())
                .eqIfPresent(ArraignmentDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(ArraignmentDO::getCheckInTime, reqVO.getCheckInTime())
                .eqIfPresent(ArraignmentDO::getCheckInPoliceSfzh, reqVO.getCheckInPoliceSfzh())
                .eqIfPresent(ArraignmentDO::getCheckInPolice, reqVO.getCheckInPolice())
                .eqIfPresent(ArraignmentDO::getEscortingPoliceSfzh, reqVO.getEscortingPoliceSfzh())
                .eqIfPresent(ArraignmentDO::getEscortingPolice, reqVO.getEscortingPolice())
                .betweenIfPresent(ArraignmentDO::getEscortingTime, reqVO.getEscortingTime())
                .eqIfPresent(ArraignmentDO::getInspectionResult, reqVO.getInspectionResult())
                .eqIfPresent(ArraignmentDO::getProhibitedItems, reqVO.getProhibitedItems())
                .eqIfPresent(ArraignmentDO::getPhysicalExam, reqVO.getPhysicalExam())
                .eqIfPresent(ArraignmentDO::getAbnormalSituations, reqVO.getAbnormalSituations())
                .eqIfPresent(ArraignmentDO::getProhibitedItemsImgUrl, reqVO.getProhibitedItemsImgUrl())
                .eqIfPresent(ArraignmentDO::getPhysicalExamImgUrl, reqVO.getPhysicalExamImgUrl())
                .eqIfPresent(ArraignmentDO::getAbnormalSituationsImgUrl, reqVO.getAbnormalSituationsImgUrl())
                .eqIfPresent(ArraignmentDO::getIsProhibitedItems, reqVO.getIsProhibitedItems())
                .betweenIfPresent(ArraignmentDO::getInspectionTime, reqVO.getInspectionTime())
                .eqIfPresent(ArraignmentDO::getInspectorSfzh, reqVO.getInspectorSfzh())
                .eqIfPresent(ArraignmentDO::getInspector, reqVO.getInspector())
                .betweenIfPresent(ArraignmentDO::getArraignmentStartTime, reqVO.getArraignmentStartTime())
                .betweenIfPresent(ArraignmentDO::getArraignmentEndTime, reqVO.getArraignmentEndTime())
                .eqIfPresent(ArraignmentDO::getReturnInspectorSfzh, reqVO.getReturnInspectorSfzh())
                .eqIfPresent(ArraignmentDO::getReturnInspector, reqVO.getReturnInspector())
                .betweenIfPresent(ArraignmentDO::getReturnInspectionTime, reqVO.getReturnInspectionTime())
                .eqIfPresent(ArraignmentDO::getReturnInspectionResult, reqVO.getReturnInspectionResult())
                .betweenIfPresent(ArraignmentDO::getReturnTime, reqVO.getReturnTime())
                .eqIfPresent(ArraignmentDO::getReturnPolice, reqVO.getReturnPolice())
                .eqIfPresent(ArraignmentDO::getReturnPoliceSfzh, reqVO.getReturnPoliceSfzh());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(ArraignmentDO::getAddTime);
        }
        Page<ArraignmentDO> arraignmentPage = selectPage(page, wrapper);
        return new PageResult<>(arraignmentPage.getRecords(), arraignmentPage.getTotal());
    }

    default List<ArraignmentDO> selectList(ArraignmentListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ArraignmentDO>()
                .eqIfPresent(ArraignmentDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(ArraignmentDO::getHandler1Id, reqVO.getHandler1Id())
                .eqIfPresent(ArraignmentDO::getHandler1Xm, reqVO.getHandler1Xm())
                .eqIfPresent(ArraignmentDO::getHandler2Id, reqVO.getHandler2Id())
                .eqIfPresent(ArraignmentDO::getHandler2Xm, reqVO.getHandler2Xm())
                .eqIfPresent(ArraignmentDO::getHandler3Id, reqVO.getHandler3Id())
                .eqIfPresent(ArraignmentDO::getHandler3Xm, reqVO.getHandler3Xm())
                .eqIfPresent(ArraignmentDO::getRoomId, reqVO.getRoomId())
                .eqIfPresent(ArraignmentDO::getArraignmentReason, reqVO.getArraignmentReason())
                .betweenIfPresent(ArraignmentDO::getStartApplyArraignmentTime, reqVO.getStartApplyArraignmentTime())
                .betweenIfPresent(ArraignmentDO::getEndApplyArraignmentTime, reqVO.getEndApplyArraignmentTime())
                .eqIfPresent(ArraignmentDO::getArraignmentLetterNumber, reqVO.getArraignmentLetterNumber())
                .eqIfPresent(ArraignmentDO::getEvidenceType, reqVO.getEvidenceType())
                .eqIfPresent(ArraignmentDO::getEvidenceNumber, reqVO.getEvidenceNumber())
                .eqIfPresent(ArraignmentDO::getEvidenceUrl, reqVO.getEvidenceUrl())
                .eqIfPresent(ArraignmentDO::getMinorAgent, reqVO.getMinorAgent())
                .eqIfPresent(ArraignmentDO::getMinorGuardian, reqVO.getMinorGuardian())
                .eqIfPresent(ArraignmentDO::getTranslator, reqVO.getTranslator())
                .eqIfPresent(ArraignmentDO::getOtherProfessionals, reqVO.getOtherProfessionals())
                .eqIfPresent(ArraignmentDO::getProfessionalCertUrl, reqVO.getProfessionalCertUrl())
                .eqIfPresent(ArraignmentDO::getTjjglx, reqVO.getTjjglx())
                .eqIfPresent(ArraignmentDO::getTjjgmc, reqVO.getTjjgmc())
                .eqIfPresent(ArraignmentDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(ArraignmentDO::getCheckInTime, reqVO.getCheckInTime())
                .eqIfPresent(ArraignmentDO::getCheckInPoliceSfzh, reqVO.getCheckInPoliceSfzh())
                .eqIfPresent(ArraignmentDO::getCheckInPolice, reqVO.getCheckInPolice())
                .eqIfPresent(ArraignmentDO::getEscortingPoliceSfzh, reqVO.getEscortingPoliceSfzh())
                .eqIfPresent(ArraignmentDO::getEscortingPolice, reqVO.getEscortingPolice())
                .betweenIfPresent(ArraignmentDO::getEscortingTime, reqVO.getEscortingTime())
                .eqIfPresent(ArraignmentDO::getInspectionResult, reqVO.getInspectionResult())
                .eqIfPresent(ArraignmentDO::getProhibitedItems, reqVO.getProhibitedItems())
                .eqIfPresent(ArraignmentDO::getPhysicalExam, reqVO.getPhysicalExam())
                .eqIfPresent(ArraignmentDO::getAbnormalSituations, reqVO.getAbnormalSituations())
                .eqIfPresent(ArraignmentDO::getProhibitedItemsImgUrl, reqVO.getProhibitedItemsImgUrl())
                .eqIfPresent(ArraignmentDO::getPhysicalExamImgUrl, reqVO.getPhysicalExamImgUrl())
                .eqIfPresent(ArraignmentDO::getAbnormalSituationsImgUrl, reqVO.getAbnormalSituationsImgUrl())
                .eqIfPresent(ArraignmentDO::getIsProhibitedItems, reqVO.getIsProhibitedItems())
                .betweenIfPresent(ArraignmentDO::getInspectionTime, reqVO.getInspectionTime())
                .eqIfPresent(ArraignmentDO::getInspectorSfzh, reqVO.getInspectorSfzh())
                .eqIfPresent(ArraignmentDO::getInspector, reqVO.getInspector())
                .betweenIfPresent(ArraignmentDO::getArraignmentStartTime, reqVO.getArraignmentStartTime())
                .betweenIfPresent(ArraignmentDO::getArraignmentEndTime, reqVO.getArraignmentEndTime())
                .eqIfPresent(ArraignmentDO::getReturnInspectorSfzh, reqVO.getReturnInspectorSfzh())
                .eqIfPresent(ArraignmentDO::getReturnInspector, reqVO.getReturnInspector())
                .betweenIfPresent(ArraignmentDO::getReturnInspectionTime, reqVO.getReturnInspectionTime())
                .eqIfPresent(ArraignmentDO::getReturnInspectionResult, reqVO.getReturnInspectionResult())
                .betweenIfPresent(ArraignmentDO::getReturnTime, reqVO.getReturnTime())
                .eqIfPresent(ArraignmentDO::getReturnPolice, reqVO.getReturnPolice())
                .eqIfPresent(ArraignmentDO::getReturnPoliceSfzh, reqVO.getReturnPoliceSfzh())
                .orderByDesc(ArraignmentDO::getAddTime));
    }


}
