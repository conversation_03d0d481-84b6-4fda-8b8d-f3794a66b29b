package com.rs.module.acp.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.pm.CusAppUserDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-监管管理-我的应用配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface CusAppUserDao extends IBaseDao<CusAppUserDO> {


    default PageResult<CusAppUserDO> selectPage(CusAppUserPageReqVO reqVO) {
        Page<CusAppUserDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<CusAppUserDO> wrapper = new LambdaQueryWrapperX<CusAppUserDO>()
            .eqIfPresent(CusAppUserDO::getSystemId, reqVO.getSystemId())
            .eqIfPresent(CusAppUserDO::getUserIdCard, reqVO.getUserIdCard())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(CusAppUserDO::getAddTime);
        }
        Page<CusAppUserDO> cusAppUserPage = selectPage(page, wrapper);
        return new PageResult<>(cusAppUserPage.getRecords(), cusAppUserPage.getTotal());
    }
    default List<CusAppUserDO> selectList(CusAppUserListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CusAppUserDO>()
            .eqIfPresent(CusAppUserDO::getSystemId, reqVO.getSystemId())
            .eqIfPresent(CusAppUserDO::getUserIdCard, reqVO.getUserIdCard())
        .orderByDesc(CusAppUserDO::getAddTime));    }


    }
