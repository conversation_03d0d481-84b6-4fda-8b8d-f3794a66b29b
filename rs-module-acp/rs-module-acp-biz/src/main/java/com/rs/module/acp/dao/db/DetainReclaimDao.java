package com.rs.module.acp.dao.db;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.db.DetainReclaimDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-羁押业务-收回登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface DetainReclaimDao extends IBaseDao<DetainReclaimDO> {


    default PageResult<DetainReclaimDO> selectPage(DetainReclaimPageReqVO reqVO) {
        Page<DetainReclaimDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<DetainReclaimDO> wrapper = new LambdaQueryWrapperX<DetainReclaimDO>()
            .eqIfPresent(DetainReclaimDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(DetainReclaimDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(DetainReclaimDO::getRssj, reqVO.getRssj())
            .eqIfPresent(DetainReclaimDO::getRsyy, reqVO.getRsyy())
            .eqIfPresent(DetainReclaimDO::getFlwsh, reqVO.getFlwsh())
            .eqIfPresent(DetainReclaimDO::getShyy, reqVO.getShyy())
            .eqIfPresent(DetainReclaimDO::getShrq, reqVO.getShrq())
            .eqIfPresent(DetainReclaimDO::getJbrsfzh, reqVO.getJbrsfzh())
            .eqIfPresent(DetainReclaimDO::getJbr, reqVO.getJbr())
            .eqIfPresent(DetainReclaimDO::getJbsj, reqVO.getJbsj())
            .eqIfPresent(DetainReclaimDO::getStatus, reqVO.getStatus())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(DetainReclaimDO::getAddTime);
        }
        Page<DetainReclaimDO> detainReclaimPage = selectPage(page, wrapper);
        return new PageResult<>(detainReclaimPage.getRecords(), detainReclaimPage.getTotal());
    }
    default List<DetainReclaimDO> selectList(DetainReclaimListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DetainReclaimDO>()
            .eqIfPresent(DetainReclaimDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(DetainReclaimDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(DetainReclaimDO::getRssj, reqVO.getRssj())
            .eqIfPresent(DetainReclaimDO::getRsyy, reqVO.getRsyy())
            .eqIfPresent(DetainReclaimDO::getFlwsh, reqVO.getFlwsh())
            .eqIfPresent(DetainReclaimDO::getShyy, reqVO.getShyy())
            .eqIfPresent(DetainReclaimDO::getShrq, reqVO.getShrq())
            .eqIfPresent(DetainReclaimDO::getJbrsfzh, reqVO.getJbrsfzh())
            .eqIfPresent(DetainReclaimDO::getJbr, reqVO.getJbr())
            .eqIfPresent(DetainReclaimDO::getJbsj, reqVO.getJbsj())
            .eqIfPresent(DetainReclaimDO::getStatus, reqVO.getStatus())
        .orderByDesc(DetainReclaimDO::getAddTime));    }


    }
