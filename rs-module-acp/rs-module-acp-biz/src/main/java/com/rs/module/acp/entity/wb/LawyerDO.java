package com.rs.module.acp.entity.wb;

import lombok.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-窗口业务-律师 DO
 *
 * <AUTHOR>
 */
@TableName("acp_wb_lawyer")
@KeySequence("acp_wb_lawyer_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LawyerDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 姓名
     */
    private String xm;
    /**
     * 性别
     */
    private String xb;
    /**
     * 证件类型
     */
    private String zjlx;
    /**
     * 证件号码
     */
    private String zjhm;
    /**
     * 联系方式
     */
    private String lxfs;
    /**
     * 律师类型
     */
    private String lslx;
    /**
     * 执业证号码
     */
    private String zyzhm;
    /**
     * 执业证有效期
     */
    private Date ksZyzyxq;
    /**
     * 执业证有效期-结束
     */
    private Date jsZyzyxq;
    /**
     * 律师单位
     */
    private String lsdw;
    /**
     * 照片存储url
     */
    private String zpUrl;
    /**
     * 执业证书url
     */
    private String zyzsUrl;

}
