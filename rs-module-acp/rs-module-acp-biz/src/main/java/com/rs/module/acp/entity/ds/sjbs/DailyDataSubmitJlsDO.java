package com.rs.module.acp.entity.ds.sjbs;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-数据固化-每日数据报送(拘留所) DO
 *
 * <AUTHOR>
 */
@TableName("acp_ds_daily_data_submit_jls")
@KeySequence("acp_ds_daily_data_submit_jls_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_ds_daily_data_submit_jls")
public class DailyDataSubmitJlsDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 固化日期
     */
    private String solidificationDate;
    /**
     * 固化开始日期
     */
    private String startDate;
    /**
     * 固化结束日期
     */
    private String endDate;
    /**
     * 被监管人员总数
     */
    private Integer bjgry;
    /**
     * 男性被监管人员总数
     */
    private Integer bjgryMale;
    /**
     * 女性被监管人员总数
     */
    private Integer bjgryFemale;
    /**
     * 未成年被监管人员总数
     */
    private Integer bjgryMinor;
    /**
     * 行政拘留
     */
    private Integer xzjl;
    /**
     * 司法拘留
     */
    private Integer sfjl;
    /**
     * 戒具使用总数
     */
    private Integer jgsy;
    /**
     * 精神异常总数
     */
    private Integer jsyc;
    /**
     * 谈话教育总数
     */
    private Integer thjy;

}
