package com.rs.module.acp.entity.wb;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-对外开放登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_visitor_reg")
@KeySequence("acp_gj_visitor_reg_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_visitor_reg")
public class VisitorRegDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 来访时间
     */
    private Date visitTime;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 来访事由
     */
    private String visitReason;
    /**
     * 来访人员类型
     */
    private String visitorType;
    /**
     * 参观区域
     */
    private String visitArea;
    /**
     * 备注
     */
    private String remark;

}
