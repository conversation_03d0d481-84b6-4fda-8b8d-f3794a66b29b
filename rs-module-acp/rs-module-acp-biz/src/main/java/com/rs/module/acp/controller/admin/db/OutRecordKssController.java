package com.rs.module.acp.controller.admin.db;

import com.rs.module.acp.entity.db.DetainRegKssDO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.OutRecordKssDO;
import com.rs.module.acp.service.db.OutRecordKssService;

@Api(tags = "实战平台-羁押业务-出所登记（看守所）")
@RestController
@RequestMapping("/acp/db/outRecordKss")
@Validated
public class OutRecordKssController {

    @Resource
    private OutRecordKssService outRecordKssService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-羁押业务-出所登记（看守所）")
    public CommonResult<String> createOutRecordKss(@Valid @RequestBody OutRecordKssSaveReqVO createReqVO) {
        return success(outRecordKssService.createOutRecordKss(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-羁押业务-出所登记（看守所）")
    public CommonResult<Boolean> updateOutRecordKss(@Valid @RequestBody OutRecordKssSaveReqVO updateReqVO) {
        outRecordKssService.updateOutRecordKssByStatus(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-羁押业务-出所登记（看守所）")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteOutRecordKss(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           outRecordKssService.deleteOutRecordKss(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-羁押业务-出所登记（看守所）")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<OutRecordKssRespVO> getOutRecordKss(@RequestParam("id") String id) {
        OutRecordKssDO outRecordKss = outRecordKssService.getOutRecordKss(id);
        return success(BeanUtils.toBean(outRecordKss, OutRecordKssRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-羁押业务-出所登记（看守所）分页")
    public CommonResult<PageResult<OutRecordKssRespVO>> getOutRecordKssPage(@Valid @RequestBody OutRecordKssPageReqVO pageReqVO) {
        PageResult<OutRecordKssDO> pageResult = outRecordKssService.getOutRecordKssPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, OutRecordKssRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-羁押业务-出所登记（看守所）列表")
    public CommonResult<List<OutRecordKssRespVO>> getOutRecordKssList(@Valid @RequestBody OutRecordKssListReqVO listReqVO) {
        List<OutRecordKssDO> list = outRecordKssService.getOutRecordKssList(listReqVO);
        return success(BeanUtils.toBean(list, OutRecordKssRespVO.class));
    }

    /**
     * 根据人员编号查询出所人员信息
     */
    @GetMapping("/getByJgrybm")
    public CommonResult<OutRecordKssRespVO> getByJgrybm(@RequestParam("jgrybm") String jgrybm) {
        OutRecordKssDO outRecordKss = outRecordKssService.getByJgrybm(jgrybm);
        return success(BeanUtils.toBean(outRecordKss, OutRecordKssRespVO.class));
    }

    /**
     * 更新流程信息
     *
     * @param updateReqVO
     * @return
     */
    @PostMapping("/updateWorkflowInfo")
    @ApiOperation(value = "更新流程信息-看守所收押登记")
    public CommonResult<Boolean> updateWorkflowInfo(@Valid @RequestBody OutRecordKssSaveReqVO updateReqVO) {
        //更新操作，需要传ID，判断是否有传，如果未传则提示id不能为空
        if(updateReqVO.getJgrybm() == null|| updateReqVO.getJgrybm().isEmpty()){
            return CommonResult.error("rybh参数不能为空！");
        }

        OutRecordKssDO db = outRecordKssService.getPrisonerInfo(updateReqVO.getJgrybm());
        if(db != null){
            updateReqVO.setId(db.getId());
            outRecordKssService.updateOutRecordKss(updateReqVO);
        }else {
            System.out.println("未找到RYBH为：" + updateReqVO.getJgrybm() + "的记录！");
        }


        return success(true);
    }

    /**
     * 获取各个流程状态信息
     */
    @GetMapping("/getInRecordStatus")
    @ApiOperation(value = "获取各个流程状态信息")
    public CommonResult<InRecordStatusVO> getInRecordStatus(@RequestParam("rybh") String rybh) {
        InRecordStatusVO db = outRecordKssService.getInRecordStatus(rybh);
        return success(db);
    }
}
