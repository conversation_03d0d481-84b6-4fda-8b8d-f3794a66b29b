package com.rs.module.acp.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.pm.LocalsenseTagPersonDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-监管管理-定位标签与人员绑定 Dao
*
* <AUTHOR>
*/
@Mapper
public interface LocalsenseTagPersonDao extends IBaseDao<LocalsenseTagPersonDO> {


    default PageResult<LocalsenseTagPersonDO> selectPage(LocalsenseTagPersonPageReqVO reqVO) {
        Page<LocalsenseTagPersonDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<LocalsenseTagPersonDO> wrapper = new LambdaQueryWrapperX<LocalsenseTagPersonDO>()
            .eqIfPresent(LocalsenseTagPersonDO::getBindSource, reqVO.getBindSource())
            .eqIfPresent(LocalsenseTagPersonDO::getTagId, reqVO.getTagId())
            .eqIfPresent(LocalsenseTagPersonDO::getPersonType, reqVO.getPersonType())
            .eqIfPresent(LocalsenseTagPersonDO::getBindPersonId, reqVO.getBindPersonId())
            .likeIfPresent(LocalsenseTagPersonDO::getBindPersonName, reqVO.getBindPersonName())
            .betweenIfPresent(LocalsenseTagPersonDO::getBindTime, reqVO.getBindTime())
            .betweenIfPresent(LocalsenseTagPersonDO::getUnbindTime, reqVO.getUnbindTime())
            .eqIfPresent(LocalsenseTagPersonDO::getUnbindReason, reqVO.getUnbindReason())
            .eqIfPresent(LocalsenseTagPersonDO::getStatus, reqVO.getStatus())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(LocalsenseTagPersonDO::getAddTime);
        }
        Page<LocalsenseTagPersonDO> localsenseTagPersonPage = selectPage(page, wrapper);
        return new PageResult<>(localsenseTagPersonPage.getRecords(), localsenseTagPersonPage.getTotal());
    }
    default List<LocalsenseTagPersonDO> selectList(LocalsenseTagPersonListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<LocalsenseTagPersonDO>()
            .eqIfPresent(LocalsenseTagPersonDO::getBindSource, reqVO.getBindSource())
            .eqIfPresent(LocalsenseTagPersonDO::getTagId, reqVO.getTagId())
            .eqIfPresent(LocalsenseTagPersonDO::getPersonType, reqVO.getPersonType())
            .eqIfPresent(LocalsenseTagPersonDO::getBindPersonId, reqVO.getBindPersonId())
            .likeIfPresent(LocalsenseTagPersonDO::getBindPersonName, reqVO.getBindPersonName())
            .betweenIfPresent(LocalsenseTagPersonDO::getBindTime, reqVO.getBindTime())
            .betweenIfPresent(LocalsenseTagPersonDO::getUnbindTime, reqVO.getUnbindTime())
            .eqIfPresent(LocalsenseTagPersonDO::getUnbindReason, reqVO.getUnbindReason())
            .eqIfPresent(LocalsenseTagPersonDO::getStatus, reqVO.getStatus())
        .orderByDesc(LocalsenseTagPersonDO::getAddTime));    }


    }
