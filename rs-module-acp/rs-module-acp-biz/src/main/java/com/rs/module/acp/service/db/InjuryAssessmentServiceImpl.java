package com.rs.module.acp.service.db;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.InjuryAssessmentDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.db.InjuryAssessmentDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-收押业务-伤情鉴定 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InjuryAssessmentServiceImpl extends BaseServiceImpl<InjuryAssessmentDao, InjuryAssessmentDO> implements InjuryAssessmentService {

    @Resource
    private InjuryAssessmentDao injuryAssessmentDao;

    @Override
    public String createInjuryAssessment(InjuryAssessmentSaveReqVO createReqVO) {
        // 插入
        InjuryAssessmentDO injuryAssessment = BeanUtils.toBean(createReqVO, InjuryAssessmentDO.class);
        injuryAssessment.setId(UUID.randomUUID().toString().replace("-", ""));
        injuryAssessment.setJgrybm(injuryAssessment.getRybh());
        injuryAssessmentDao.insert(injuryAssessment);
        // 返回
        return injuryAssessment.getId();
    }

    @Override
    public void updateInjuryAssessment(InjuryAssessmentSaveReqVO updateReqVO) {
        // 校验存在
        validateInjuryAssessmentExists(updateReqVO.getId());
        // 更新
        InjuryAssessmentDO updateObj = BeanUtils.toBean(updateReqVO, InjuryAssessmentDO.class);
        injuryAssessmentDao.updateById(updateObj);
    }

    @Override
    public void deleteInjuryAssessment(String id) {
        // 校验存在
        validateInjuryAssessmentExists(id);
        // 删除
        injuryAssessmentDao.deleteById(id);
    }

    private void validateInjuryAssessmentExists(String id) {
        if (injuryAssessmentDao.selectById(id) == null) {
            throw new ServerException("实战平台-收押业务-伤情鉴定数据不存在");
        }
    }

    @Override
    public InjuryAssessmentDO getInjuryAssessment(String id) {
        return injuryAssessmentDao.selectById(id);
    }

    @Override
    public PageResult<InjuryAssessmentDO> getInjuryAssessmentPage(InjuryAssessmentPageReqVO pageReqVO) {
        return injuryAssessmentDao.selectPage(pageReqVO);
    }

    @Override
    public List<InjuryAssessmentDO> getInjuryAssessmentList(InjuryAssessmentListReqVO listReqVO) {
        return injuryAssessmentDao.selectList(listReqVO);
    }

    @Override
    public void deleteByRybh(String rybh) {
        InjuryAssessmentListReqVO listReqVO = new InjuryAssessmentListReqVO();
        listReqVO.setRybh(rybh);
        List<InjuryAssessmentDO> list = injuryAssessmentDao.selectList(listReqVO);
//        injuryAssessmentDao.deleteBatchIds(list);

        if (list != null && !list.isEmpty()) {
            List<String> idList = list.stream()
                                      .map(InjuryAssessmentDO::getId)
                                      .collect(Collectors.toList());
            injuryAssessmentDao.deleteBatchIds(idList);
        }

    }

    @Override
    public InjuryAssessmentDO getPersonalEffectsByRybh(String rybh) {
        InjuryAssessmentDO injuryAssessmentDO = injuryAssessmentDao.selectOne(InjuryAssessmentDO::getRybh, rybh);
        return injuryAssessmentDO;
    }


}
