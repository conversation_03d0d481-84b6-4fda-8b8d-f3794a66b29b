package com.rs.module.acp.entity.zh;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 综合管理-值班管理-值班督导记录 DO
 *
 * <AUTHOR>
 */
@TableName("acp_zh_duty_supervise_record")
@KeySequence("acp_zh_duty_supervise_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_duty_supervise_record")
public class DutySuperviseRecordDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 排班模板ID
     */
    private String staffDutyTemplateId;
    /**
     * 值班岗位ID
     */
    private String staffDutyPostId;
    /**
     * 值班模板关联角色ID
     */
    private String staffDutyRoleId;
    /**
     * 值班日期
     */
    private Date dutyDate;
    /**
     * 签到验证模式
     */
    private String signinValidMode;
    /**
     * 签到状态
     */
    private String signinStatus;
    /**
     * 签到时间
     */
    private Date signinTime;

}
