package com.rs.module.acp.service.gj.diagnosiassmtjds;

import java.util.*;
import javax.validation.*;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds.*;
import com.rs.module.acp.entity.gj.DiagnosisAssmtJdsDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.service.gj.diagnosiassmtjds.bo.AssmtCommonBO;

/**
 * 实战平台-管教业务-诊断评估(戒毒所) Service 接口
 *
 * <AUTHOR>
 */
public interface DiagnosisAssmtJdsService extends IBaseService<DiagnosisAssmtJdsDO>{

    /**
     * 创建实战平台-管教业务-诊断评估(戒毒所)
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createDiagnosisAssmtJds(@Valid DiagnosisAssmtJdsSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-诊断评估(戒毒所)
     *
     * @param updateReqVO 更新信息
     */
    void updateDiagnosisAssmtJds(@Valid DiagnosisAssmtJdsSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-诊断评估(戒毒所)
     *
     * @param id 编号
     */
    void deleteDiagnosisAssmtJds(String id);

    /**
     * 获得实战平台-管教业务-诊断评估(戒毒所)
     *
     * @param id 编号
     * @return 实战平台-管教业务-诊断评估(戒毒所)
     */
    DiagnosisAssmtJdsRespVO getDiagnosisAssmtJds(String id);

    /**
    * 获得实战平台-管教业务-诊断评估(戒毒所)分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-诊断评估(戒毒所)分页
    */
    PageResult<DiagnosisAssmtJdsDO> getDiagnosisAssmtJdsPage(DiagnosisAssmtJdsPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-诊断评估(戒毒所)列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-诊断评估(戒毒所)列表
    */
    List<DiagnosisAssmtJdsRespVO> getDiagnosisAssmtJdsList(DiagnosisAssmtJdsListReqVO listReqVO);


    List<DiagnosisAssmtJdsDO> handleDiagnosisAssmtJds(String periodType, String assmtType, String sqlTime);

    Map<String, List<AssmtCommonBO>> getModelByAssmtType(String assmtType, String jgrybm);

    String assmtCreat(DiagnosisAssmtJdsCommitReqVO createReqVO);

    void approval(DiagnosisAssmtJdsApprovalReqVO approvalReqVO);

    List<DiagnosisAssmtJdsRespVO> personRecordList(String jgrybm, String assmtType);

    Map<String, String> getMaxAssmtPeriod(String assmtType, String jgrybm);

    List<DiagnosisAssmtJdsBusinessIdRespVO> getFormIdBusinessId(String jgrybm);

}
