package com.rs.module.acp.controller.admin.ds.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-数据固化-监所人员变动记录新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonRoomChangeSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("监室id")
    @NotEmpty(message = "监室id不能为空")
    private String roomId;

    @ApiModelProperty("监室名称")
    @NotEmpty(message = "监室名称不能为空")
    private String roomName;

    @ApiModelProperty("入监室时间")
    @NotNull(message = "入监室时间不能为空")
    private Date inRoomTime;

    @ApiModelProperty("入监室类型")
    @NotEmpty(message = "入监室类型不能为空")
    private String inRoomType;

    @ApiModelProperty("入监室关联业务ID")
    @NotEmpty(message = "入监室关联业务ID不能为空")
    private String inRoomBusinessId;

    @ApiModelProperty("in_room_reason")
    @NotEmpty(message = "in_room_reason不能为空")
    private String inRoomReason;

    @ApiModelProperty("出监室时间")
    private Date outRoomTime;

    @ApiModelProperty("出监室类型")
    private String outRoomType;

    @ApiModelProperty("出监室关联业务ID")
    private String outRoomBusinessId;

    @ApiModelProperty("出监室原因")
    private String outRoomReason;

    @ApiModelProperty("batch_id")
    private String batchId;

}
