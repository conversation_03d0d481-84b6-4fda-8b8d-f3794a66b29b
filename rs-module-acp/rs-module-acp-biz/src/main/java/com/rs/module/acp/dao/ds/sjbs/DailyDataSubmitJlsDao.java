package com.rs.module.acp.dao.ds.sjbs;

import com.rs.module.acp.controller.admin.ds.vo.sjbs.DailyDataSubmitJlsRespVO;
import com.rs.module.acp.entity.ds.sjbs.DailyDataSubmitJlsDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 实战平台-数据固化-每日数据报送(拘留所) Dao
*
* <AUTHOR>
*/
@Mapper
public interface DailyDataSubmitJlsDao extends IBaseDao<DailyDataSubmitJlsDO> {
    List<DailyDataSubmitJlsDO> statisticNum(@Param("orgCode") String orgCode,
                                                @Param("startDate") String startDate,
                                                @Param("endDate") String endDate);
    int deleteByCondition(
            @Param("orgCode") String orgCode,
            @Param("solidificationDate") String solidificationDate);
}
