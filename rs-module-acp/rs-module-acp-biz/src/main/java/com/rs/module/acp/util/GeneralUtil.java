package com.rs.module.acp.util;

import cn.hutool.core.util.RandomUtil;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.UUID;

/**
 * 通用工具类 - 用于生成编号及其他通用功能
 */
public class GeneralUtil {
    // 编号前缀
    private static final String ID_PREFIX = "PERS";

    /**
     * 生成带时间戳和随机数的唯一编号
     * 格式: PERS-YYYYMMDD-RandomNumber(4位)
     * @return String 生成的编号
     */
    public static String generateRybh(String orgCode) {
        //根据当前日期时间生成8位数日期字符串yyyyMMdd
        String date = new SimpleDateFormat("yyyyMMdd").format(new Date());
        // 根据当前时间生成4位数，例如取当前时间的毫秒部分
        String timeSuffix = new SimpleDateFormat("SSS").format(new Date());
        int number = Integer.parseInt(timeSuffix) % 10000; // 防止超过四位数
        String time = String.format("%04d", number);

        String rybh = String.format("%s%s%s", orgCode, date, time);
        return rybh;
    }

    /**
     * 使用UUID生成不带连字符的唯一ID
     * @return String 生成的UUID
     */
    public static String generateUUID() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    /**
     * 检查编号是否符合规范格式
     * @param id 待检查的编号
     * @return boolean 是否有效
     */
    public static boolean isValidId(String id) {
        return id != null && id.matches("^PERS-\\d{8}-\\d{4}$");
    }

    /**
     * 获取当前年份作为字符串
     * @return String 当前年份
     */
    public static String getCurrentYear() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy"));
    }

    /**
     * 编号规则：A0000xxxxx，A0000后五位顺序自增
     * 生成五位顺序自增序列号
     * @return String
     */
    public static String generateId(int start) {
        String.format("%05d", start);
        return "A0000"+String.format("%05d", start);
    }
}