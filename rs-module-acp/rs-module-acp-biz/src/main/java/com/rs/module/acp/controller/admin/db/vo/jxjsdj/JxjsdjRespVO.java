package com.rs.module.acp.controller.admin.db.vo.jxjsdj;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.acp.controller.admin.zh.vo.MeetingRecordsRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-减刑登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class JxjsdjRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("业务类型(1、减刑；2、假释)")
    private String ywlx;
    @ApiModelProperty("减刑原因")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JYGL_ZFGL_JXJSYY")
    private String jxyy;
    @ApiModelProperty("悔改表现")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JYGL_FZGL_HGBX")
    private String hgbx;
    @ApiModelProperty("立功表现")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JYGL_ZFGL_LGBX")
    private String lgbx;
    @ApiModelProperty("重大立功表现")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JYGL_ZFGL_ZDLGBX")
    private String zdlgbx;
    @ApiModelProperty("其他原因")
    private String qtyy;
    @ApiModelProperty("备注")
    private String bz;
    @ApiModelProperty("申请时间")
    private Date operateTime;
    @ApiModelProperty("经办民警身份证号")
    private String operatePoliceSfzh;
    @ApiModelProperty("经办民警")
    private String operatePolice;
    @ApiModelProperty("附件地址")
    private String attUrl;
    @ApiModelProperty("所务会编号")
    private String swhbh;

    @ApiModelProperty("所务会--详细信息")
    private MeetingRecordsRespVO swhxx;

    @ApiModelProperty("复核所务会编号")
    private String fhswhbh;

    @ApiModelProperty("所务会复核--详细信息")
    private MeetingRecordsRespVO swhfhxx;

    @ApiModelProperty("所务会召开日期")
    private Date swhzkrq;
    @ApiModelProperty("所务会研究结果（1、同意；2、不同意）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_IFTY")
    private String swhyjjg;
    @ApiModelProperty("重新召开所务会复核日期")
    private Date cxzkswhfhrq;
    @ApiModelProperty("复核意见（1、不在提请减刑；2、继续提请减刑）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JYGL_ZFGL_SWHYFHJG")
    private String fhyj;
    @ApiModelProperty("财产性判项履行能力认定结果（1、同意；2、不同意）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_IFTY")
    private String ccxpxlxnlrdjg;
    @ApiModelProperty("财产性判项履行能力认定备注")
    private String ccxpxlxnlrdbz;
    @ApiModelProperty("财产性判项履行能力认定材料")
    private String ccxpxlxnlrdcl;
    @ApiModelProperty("财产性判项履行能力认定经办民警身份证号")
    private String ccxpxlxnlrdjbmjsfzh;
    @ApiModelProperty("财产性判项履行能力认定经办民警姓名")
    private String ccxpxlxnlrdjbmjxm;
    @ApiModelProperty("财产性判项履行能力认定经办时间")
    private Date ccxpxlxnlrdjbsj;
    @ApiModelProperty("核实执行地调查评估结果（1、同意；2、不同意）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_IFTY")
    private String hszxddcpgjg;
    @ApiModelProperty("核实执行地调查备注")
    private String hszxddcbz;
    @ApiModelProperty("核实执行地调查经办民警身份证号")
    private String hszxddcjbmjsfzh;
    @ApiModelProperty("核实执行地调查经办民警姓名")
    private String hszxddcjbmjxm;
    @ApiModelProperty("核实执行地调查经办时间")
    private Date hszxddcjbsj;
    @ApiModelProperty("核实执行地调查材料")
    private String hszxddccl;
    @ApiModelProperty("所内公示开始日期")
    private Date sngsksrq;
    @ApiModelProperty("所内公示截止日期")
    private Date sngsjsrq;
    @ApiModelProperty("是否收到异议（1、是；2、否）")
    private String sfsdyy;
    @ApiModelProperty("所内公示备注")
    private String sngsbz;
    @ApiModelProperty("所内公示材料")
    private String sngscl;
    @ApiModelProperty("所内公示经办民警身份证号")
    private String sngsjbmjsfzh;
    @ApiModelProperty("所内公示经办民警姓名")
    private String sngsjbmjxm;
    @ApiModelProperty("所内公示经办时间")
    private Date sngsjbsj;
    @ApiModelProperty("报请公安机关日期")
    private String bqgajgrq;
    @ApiModelProperty("公安机关审查日期")
    private Date gajgscrq;
    @ApiModelProperty("公安机关审查意见（1、同意；2、不同意）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_IFTY")
    private String gajgscyj;
    @ApiModelProperty("公安机关审查备注")
    private String gajgscbz;
    @ApiModelProperty("公安机关审查材料")
    private String gajgsccl;
    @ApiModelProperty("公安机关审查经办民警身份证号")
    private String gajgscjbmjsfzh;
    @ApiModelProperty("公安机关审查经办民警姓名")
    private String gajgscjbmjxm;
    @ApiModelProperty("公安机关审查经办时间")
    private Date gajgscjbsj;
    @ApiModelProperty("检查院监督提请日期")
    private Date jcyjdtqrq;
    @ApiModelProperty("检查院监督审核日期")
    private Date jcyjdshrq;
    @ApiModelProperty("检查院监督意见")
    private String jcyjdyj;
    @ApiModelProperty("检查院监督材料")
    private String jcyjdcl;
    @ApiModelProperty("检查院监督经办民警身份证号")
    private String jcyjdjbmjsfzh;
    @ApiModelProperty("检查院监督经办民警姓名")
    private String jcyjdjbmjxm;
    @ApiModelProperty("检查院监督经办时间")
    private Date jcyjdjbsj;
    @ApiModelProperty("法院审核裁定提请日期")
    private Date fyshcdtqrq;
    @ApiModelProperty("法院审核裁定日期")
    private Date fyshcdrq;
    @ApiModelProperty("法院审核裁定结果（1、予以减刑或者假释；2、不予减刑或者假释）")
    private String fyshcdjg;

    @ApiModelProperty("法院审核裁定结果名称（1、予以减刑或者假释；2、不予减刑或者假释）")
    private String fyshcdjgName;

    @ApiModelProperty("减刑情况")
    private String jxqk;
    @ApiModelProperty("刑期截止日期")
    private Date xqjzrq;
    @ApiModelProperty("法院审核裁定材料")
    private String fyshcdcl;
    @ApiModelProperty("法院审核裁定经办民警身份证号")
    private String fyshcdjbmjsfzh;
    @ApiModelProperty("法院审核裁定经办民警姓名")
    private String fyshcdjbmjxm;
    @ApiModelProperty("法院审核裁定经办时间")
    private Date fyshcdjbsj;
    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JYGL_ZFGL_JS")
    private String status;

    private String cityName;

    private String cityCode;

    private String regName;

    private String regCode;

    private String orgName;

    private String orgCode;

    private Boolean isDel;

    private String addUser;

    private String addUserName;

    private Date addTime;

    private String updateUser;

    private String updateUserName;

    private Date updateTime;
}
