package com.rs.module.acp.dao.zh;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorPageReqVO;
import com.rs.module.acp.entity.zh.IndicatorDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 综合管理-绩效考核指标 Dao
*
* <AUTHOR>
*/
@Mapper
public interface IndicatorDao extends IBaseDao<IndicatorDO> {


    default PageResult<IndicatorDO> selectPage(IndicatorPageReqVO reqVO) {
        Page<IndicatorDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<IndicatorDO> wrapper = new LambdaQueryWrapperX<IndicatorDO>()
            .likeIfPresent(IndicatorDO::getIndicatorName, reqVO.getIndicatorName())
            .eqIfPresent(IndicatorDO::getIndicatorCateId, reqVO.getIndicatorCateId())
            .likeIfPresent(IndicatorDO::getIndicatorCateName, reqVO.getIndicatorCateName())
            .eqIfPresent(IndicatorDO::getIndicatorType, reqVO.getIndicatorType())
            .eqIfPresent(IndicatorDO::getSortOrder, reqVO.getSortOrder())
            .eqIfPresent(IndicatorDO::getIsEnabled, reqVO.getIsEnabled())
            .eqIfPresent(IndicatorDO::getRemark, reqVO.getRemark())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(IndicatorDO::getAddTime);
        }
        Page<IndicatorDO> indicatorPage = selectPage(page, wrapper);
        return new PageResult<>(indicatorPage.getRecords(), indicatorPage.getTotal());
    }
    default List<IndicatorDO> selectList(IndicatorListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<IndicatorDO>()
            .likeIfPresent(IndicatorDO::getIndicatorName, reqVO.getIndicatorName())
            .eqIfPresent(IndicatorDO::getIndicatorCateId, reqVO.getIndicatorCateId())
            .likeIfPresent(IndicatorDO::getIndicatorCateName, reqVO.getIndicatorCateName())
            .eqIfPresent(IndicatorDO::getIndicatorType, reqVO.getIndicatorType())
            .eqIfPresent(IndicatorDO::getSortOrder, reqVO.getSortOrder())
            .eqIfPresent(IndicatorDO::getIsEnabled, reqVO.getIsEnabled())
            .eqIfPresent(IndicatorDO::getRemark, reqVO.getRemark())
            .eqIfPresent(IndicatorDO::getOrgCode, reqVO.getOrgCode())
        .orderByAsc(IndicatorDO::getSortOrder)
        .orderByAsc(IndicatorDO::getAddTime));    }


    }
