package com.rs.module.acp.service.gj.civilizedpersonne;

import com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne.CivilizedPersonneDetailListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne.CivilizedPersonneDetailPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne.CivilizedPersonneDetailSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.CivilizedPersonneDetailDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.CivilizedPersonneDetailDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-文明个人登记明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CivilizedPersonneDetailServiceImpl extends BaseServiceImpl<CivilizedPersonneDetailDao, CivilizedPersonneDetailDO> implements CivilizedPersonneDetailService {

    @Resource
    private CivilizedPersonneDetailDao civilizedPersonneDetailDao;

    @Override
    public String createCivilizedPersonneDetail(CivilizedPersonneDetailSaveReqVO createReqVO) {
        // 插入
        CivilizedPersonneDetailDO civilizedPersonneDetail = BeanUtils.toBean(createReqVO, CivilizedPersonneDetailDO.class);
        civilizedPersonneDetailDao.insert(civilizedPersonneDetail);
        // 返回
        return civilizedPersonneDetail.getId();
    }

    @Override
    public void updateCivilizedPersonneDetail(CivilizedPersonneDetailSaveReqVO updateReqVO) {
        // 校验存在
        validateCivilizedPersonneDetailExists(updateReqVO.getId());
        // 更新
        CivilizedPersonneDetailDO updateObj = BeanUtils.toBean(updateReqVO, CivilizedPersonneDetailDO.class);
        civilizedPersonneDetailDao.updateById(updateObj);
    }

    @Override
    public void deleteCivilizedPersonneDetail(String id) {
        // 校验存在
        validateCivilizedPersonneDetailExists(id);
        // 删除
        civilizedPersonneDetailDao.deleteById(id);
    }

    private void validateCivilizedPersonneDetailExists(String id) {
        if (civilizedPersonneDetailDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-文明个人登记明细数据不存在");
        }
    }

    @Override
    public CivilizedPersonneDetailDO getCivilizedPersonneDetail(String id) {
        return civilizedPersonneDetailDao.selectById(id);
    }

    @Override
    public PageResult<CivilizedPersonneDetailDO> getCivilizedPersonneDetailPage(CivilizedPersonneDetailPageReqVO pageReqVO) {
        return civilizedPersonneDetailDao.selectPage(pageReqVO);
    }

    @Override
    public List<CivilizedPersonneDetailDO> getCivilizedPersonneDetailList(CivilizedPersonneDetailListReqVO listReqVO) {
        return civilizedPersonneDetailDao.selectList(listReqVO);
    }


}
