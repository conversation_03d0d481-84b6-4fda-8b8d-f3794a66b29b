package com.rs.module.acp.entity.sys;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 实战平台-语音播报-即时配置 DO
 *
 * <AUTHOR>
 */
@TableName("acp_sys_vb_config_current")
@KeySequence("acp_sys_vb_config_current_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_sys_vb_config_current")
public class VbConfigCurrentDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 播报名称
     */
    private String vbName;
    /**
     * 播报内容
     */
    private String content;
    /**
     * 播报次数
     */
    private Short vbNum;
    /**
     * 优先级
     */
    private Short priority;
    /**
     * 播报开始时间
     */
    private Date startTime;
    /**
     * 播报结束时间
     */
    private Date endTime;
    /**
     * 是否启用(0否1是)
     */
    private Short isEnabled;
    /**
     * 状态(0:待播报,1:已播报,2:部分播报,3:播报异常)
     */
    private Short status;
    /**
     * 播报区域
     */
    private String vbArea;
}
