package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.wb.vo.HallInfoConfigListReqVO;
import com.rs.module.acp.controller.admin.wb.vo.HallInfoConfigPageReqVO;
import com.rs.module.acp.controller.admin.wb.vo.HallInfoConfigRespVO;
import com.rs.module.acp.controller.admin.wb.vo.HallInfoConfigSaveReqVO;
import com.rs.module.acp.entity.wb.HallInfoConfigDO;

/**
 * 实战平台-管教业务-服务大厅信息发布配置 Service 接口
 *
 * <AUTHOR>
 */
public interface HallInfoConfigService extends IBaseService<HallInfoConfigDO>{

    /**
     * 创建实战平台-管教业务-服务大厅信息发布配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createHallInfoConfig(@Valid HallInfoConfigSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-服务大厅信息发布配置
     *
     * @param updateReqVO 更新信息
     */
    void updateHallInfoConfig(@Valid HallInfoConfigSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-服务大厅信息发布配置
     *
     * @param id 编号
     */
    void deleteHallInfoConfig(String id);

    /**
     * 获得实战平台-管教业务-服务大厅信息发布配置
     *
     * @return 实战平台-管教业务-服务大厅信息发布配置
     */
    HallInfoConfigRespVO getHallInfoConfig();

    /**
    * 获得实战平台-管教业务-服务大厅信息发布配置分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-服务大厅信息发布配置分页
    */
    PageResult<HallInfoConfigDO> getHallInfoConfigPage(HallInfoConfigPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-服务大厅信息发布配置列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-服务大厅信息发布配置列表
    */
    List<HallInfoConfigDO> getHallInfoConfigList(HallInfoConfigListReqVO listReqVO);


}
