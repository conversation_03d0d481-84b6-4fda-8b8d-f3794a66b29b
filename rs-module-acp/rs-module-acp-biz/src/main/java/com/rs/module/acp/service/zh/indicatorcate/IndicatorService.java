package com.rs.module.acp.service.zh.indicatorcate;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.*;
import com.rs.module.acp.entity.zh.IndicatorDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 综合管理-绩效考核指标 Service 接口
 *
 * <AUTHOR>
 */
public interface IndicatorService extends IBaseService<IndicatorDO>{

    /**
     * 创建综合管理-绩效考核指标
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createIndicator(@Valid IndicatorSaveReqVO createReqVO);

    /**
     * 更新综合管理-绩效考核指标
     *
     * @param updateReqVO 更新信息
     */
    void updateIndicator(@Valid IndicatorSaveReqVO updateReqVO);

    /**
     * 删除综合管理-绩效考核指标
     *
     * @param id 编号
     */
    void deleteIndicator(String id);

    /**
     * 获得综合管理-绩效考核指标
     *
     * @param id 编号
     * @return 综合管理-绩效考核指标
     */
    IndicatorRespVO getIndicator(String id);

    /**
    * 获得综合管理-绩效考核指标分页
    *
    * @param pageReqVO 分页查询
    * @return 综合管理-绩效考核指标分页
    */
    PageResult<IndicatorDO> getIndicatorPage(IndicatorPageReqVO pageReqVO);

    /**
    * 获得综合管理-绩效考核指标列表
    *
    * @param listReqVO 查询条件
    * @return 综合管理-绩效考核指标列表
    */
    List<IndicatorRespVO> getIndicatorList(IndicatorListReqVO listReqVO);


    void startStopHandle(IndicatorStartStopHandleReqVO updateReqVO);
}
