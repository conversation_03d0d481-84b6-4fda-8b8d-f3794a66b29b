package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.samecasemanage.SameCaseManageListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.samecasemanage.SameCaseManagePageReqVO;
import com.rs.module.acp.entity.gj.SameCaseManageDO;
import com.rs.module.acp.service.gj.samecasemanage.bo.SameCaseManageBO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 实战平台-管教业务-同案人员管理 Dao
*
* <AUTHOR>
*/
@Mapper
public interface SameCaseManageDao extends IBaseDao<SameCaseManageDO> {


    default PageResult<SameCaseManageDO> selectPage(SameCaseManagePageReqVO reqVO) {
        Page<SameCaseManageDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<SameCaseManageDO> wrapper = new LambdaQueryWrapperX<SameCaseManageDO>()
            .eqIfPresent(SameCaseManageDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(SameCaseManageDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(SameCaseManageDO::getAjbh, reqVO.getAjbh())
            .eqIfPresent(SameCaseManageDO::getSameCaseJgrybm, reqVO.getSameCaseJgrybm())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(SameCaseManageDO::getAddTime);
        }
        Page<SameCaseManageDO> sameCaseManagePage = selectPage(page, wrapper);
        return new PageResult<>(sameCaseManagePage.getRecords(), sameCaseManagePage.getTotal());
    }
    default List<SameCaseManageDO> selectList(SameCaseManageListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SameCaseManageDO>()
            .eqIfPresent(SameCaseManageDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(SameCaseManageDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(SameCaseManageDO::getAjbh, reqVO.getAjbh())
            .eqIfPresent(SameCaseManageDO::getSameCaseJgrybm, reqVO.getSameCaseJgrybm())
        .orderByDesc(SameCaseManageDO::getAddTime));    }


    String getSystemTarAjbh(@Param("jgrybm") String jgrybm);

    Page<SameCaseManageBO> manageListPage(Page<SameCaseManageBO> page, @Param("jgrybm") String jgrybm, @Param("ajbh") String ajbh);
}
