package com.rs.module.acp.service.db.components;

import org.springframework.stereotype.Service;

/**
 * 默认的入所人员信息同步服务实现
 * 可作为基础类被继承，或直接使用时抛出异常提示未实现
 */
@Service
public class DefaultPrisonerInInfoSyncService implements PrisonerInInfoSyncService {

    @Override
    public boolean syncPrisonerInInfoToModule(String rybh, String moduleType) {
        throw new UnsupportedOperationException("尚未实现对模块 [" + moduleType + "] 的入所信息推送逻辑");
    }
}
