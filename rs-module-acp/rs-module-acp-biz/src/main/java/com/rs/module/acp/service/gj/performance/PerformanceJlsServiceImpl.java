package com.rs.module.acp.service.gj.performance;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.fhs.trans.service.impl.TransService;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.acp.controller.admin.gj.vo.performancejls.PerformanceJlsListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.performancejls.PerformanceJlsPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.performancejls.PerformanceJlsSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.performancejls.PerformanceJssApprovalReqVO;
import com.rs.module.acp.controller.admin.gj.vo.printdocument.PrintDocumentSaveReqVO;
import com.rs.module.acp.enums.gj.PerformanceStatusEnum;
import com.rs.module.acp.enums.gj.PrintDocumentTypeEnum;
import com.rs.module.acp.service.gj.printdocument.PrintDocumentService;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.enums.PrisonerQueryRyztEnum;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.util.BspApprovalUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.PerformanceJlsDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.PerformanceJlsDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-人员表现鉴定表-拘留所 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PerformanceJlsServiceImpl extends BaseServiceImpl<PerformanceJlsDao, PerformanceJlsDO> implements PerformanceJlsService {

    @Resource
    private PerformanceJlsDao performanceJlsDao;

    @Resource
    private PrintDocumentService printDocumentService;


    @Autowired
    private TransService transService;

    // 单位领导审批流程
    private static final String defKey = "yonghudanweilindao";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createPerformanceJls(PerformanceJlsSaveReqVO createReqVO) {
        // 插入
        PerformanceJlsDO performanceJls = BeanUtils.toBean(createReqVO, PerformanceJlsDO.class);
        performanceJls.setStatus(PerformanceStatusEnum.DSH.getCode());
        JSONObject jlsRyxx = performanceJlsDao.getJlsRyxxByJgrybm(createReqVO.getJgrybm());
        if(Objects.isNull(jlsRyxx)){
            throw new ServerException("该人员不存在");
        }
        performanceJlsDao.insert(performanceJls);
        PrintDocumentSaveReqVO printDocumentSaveReqVO = new PrintDocumentSaveReqVO();
        printDocumentSaveReqVO.setGlId(performanceJls.getId());
        printDocumentSaveReqVO.setGlType(PrintDocumentTypeEnum.jlsrybxjd.getCode());
        printDocumentSaveReqVO.setWsdata(jlsRyxx.toJSONString());
        printDocumentService.createPrintDocument(printDocumentSaveReqVO);
        //启动流程审批
        String msgUrl = "/#/discipline/behaviorIdentification-jls/check?id=" + performanceJls.getId() + "&jgrybm=" + createReqVO.getJgrybm();
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", performanceJls.getId());
        JSONObject result = BspApprovalUtil.defaultStartProcess(HttpUtils.getAppCode() + "-" + defKey, performanceJls.getId(), "人员表现鉴定流程审批", msgUrl, variables);
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            performanceJls.setActInstId(bpmTrail.getString("actInstId"));
            performanceJls.setTaskId(bpmTrail.getString("taskId"));
            performanceJlsDao.updateById(performanceJls);
        } else {
            throw new ServerException("流程启动失败");
        }
        // 返回
        return performanceJls.getId();
    }

    @Override
    public void updatePerformanceJls(PerformanceJlsSaveReqVO updateReqVO) {
        // 校验存在
        validatePerformanceJlsExists(updateReqVO.getId());
        // 更新
        PerformanceJlsDO updateObj = BeanUtils.toBean(updateReqVO, PerformanceJlsDO.class);
        performanceJlsDao.updateById(updateObj);
    }

    @Override
    public void deletePerformanceJls(String id) {
        // 校验存在
        validatePerformanceJlsExists(id);
        // 删除
        performanceJlsDao.deleteById(id);
    }

    private void validatePerformanceJlsExists(String id) {
        if (performanceJlsDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-人员表现鉴定表-拘留所数据不存在");
        }
    }

    @Override
    public PerformanceJlsDO getPerformanceJls(String id) {
        return performanceJlsDao.selectById(id);
    }

    @Override
    public PageResult<PerformanceJlsDO> getPerformanceJlsPage(PerformanceJlsPageReqVO pageReqVO) {
        return performanceJlsDao.selectPage(pageReqVO);
    }

    @Override
    public List<PerformanceJlsDO> getPerformanceJlsList(PerformanceJlsListReqVO listReqVO) {
        return performanceJlsDao.selectList(listReqVO);
    }

    @Override
    public void approval(PerformanceJssApprovalReqVO approvalReqVO) {
        PerformanceJlsDO performanceJlsDO = performanceJlsDao.selectById(approvalReqVO.getId());
        if (Objects.isNull(performanceJlsDO)) {
            throw new ServerException("实战平台-管教业务-人员表现鉴定表-拘留所数据不存在");
        }
        if (!PerformanceStatusEnum.DSH.getCode().equals(performanceJlsDO.getStatus())) {
            throw new ServerException("非待审批状态，不能进行审核操作");
        }
        performanceJlsDO.setStatus(PerformanceStatusEnum.getByCode(approvalReqVO.getStatus()).getCode());
        performanceJlsDO.setApproverTime(new Date());
        performanceJlsDO.setApprovalComments(approvalReqVO.getApprovalComments());
        performanceJlsDO.setApprovalResult(approvalReqVO.getStatus());
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        performanceJlsDO.setApproverSfzh(sessionUser.getIdCard());
        performanceJlsDO.setApproverXm(sessionUser.getName());
        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(performanceJlsDO.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }

        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", performanceJlsDO.getId());
        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getId());
        nowApproveUser.put("name", sessionUser.getName());
        BspApproceStatusEnum bspApproceStatusEnum = PerformanceStatusEnum.SHTG.getCode().equals(performanceJlsDO.getStatus()) ?
                BspApproceStatusEnum.PASSED_END : BspApproceStatusEnum.NOT_PASSED_END;
        JSONObject result = BspApprovalUtil.approvalProcess(HttpUtils.getAppCode() + "-" + defKey,
                performanceJlsDO.getActInstId(), performanceJlsDO.getTaskId(), performanceJlsDO.getId(),
                bspApproceStatusEnum, performanceJlsDO.getApprovalComments(), null, null, true,
                variables, nowApproveUser, "acp");
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            performanceJlsDO.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程启动失败");
        }
        performanceJlsDao.updateById(performanceJlsDO);
    }


}
