package com.rs.module.acp.controller.admin.db.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-收押业务-生物特征信息新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BiometricInfoSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("人员编号")
    @NotEmpty(message = "人员编号不能为空")
    private String rybh;

    @ApiModelProperty("采集项目类型")
    @NotEmpty(message = "采集项目类型不能为空")
    private String cjxmlx;

    @ApiModelProperty("生物特征,存储采集系统返回JSON结构")
    private String swtz;

    @ApiModelProperty("swtzfj")
    private String swtzfj;

    @ApiModelProperty("备注")
    private String bz;

}
