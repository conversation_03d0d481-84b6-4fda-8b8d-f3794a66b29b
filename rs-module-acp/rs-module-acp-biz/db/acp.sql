-- ----------------------------
-- Table structure for acp_pm_cus_app
-- ----------------------------
DROP TABLE IF EXISTS "public"."acp_pm_cus_app";
CREATE TABLE "public"."acp_pm_cus_app" (
  "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "is_del" int4 NOT NULL DEFAULT 0,
  "add_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "add_user" varchar(50) COLLATE "pg_catalog"."default",
  "add_user_name" varchar(30) COLLATE "pg_catalog"."default",
  "update_time" timestamp(6),
  "update_user" varchar(50) COLLATE "pg_catalog"."default",
  "update_user_name" varchar(30) COLLATE "pg_catalog"."default",
  "pro_code" varchar(50) COLLATE "pg_catalog"."default",
  "pro_name" varchar(100) COLLATE "pg_catalog"."default",
  "city_code" varchar(50) COLLATE "pg_catalog"."default",
  "city_name" varchar(100) COLLATE "pg_catalog"."default",
  "reg_code" varchar(50) COLLATE "pg_catalog"."default",
  "reg_name" varchar(100) COLLATE "pg_catalog"."default",
  "org_code" varchar(50) COLLATE "pg_catalog"."default",
  "org_name" varchar(100) COLLATE "pg_catalog"."default",
  "system_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "yymc" varchar(50) COLLATE "pg_catalog"."default",
  "ljdz" varchar(300) COLLATE "pg_catalog"."default",
  "fl_id" varchar(50) COLLATE "pg_catalog"."default",
  "sfnb" varchar(1) COLLATE "pg_catalog"."default",
  "sfgg" varchar(1) COLLATE "pg_catalog"."default" DEFAULT '0'::character varying,
  "yylx" varchar(1) COLLATE "pg_catalog"."default",
  "sfjy" varchar(1) COLLATE "pg_catalog"."default" DEFAULT '0'::character varying,
  "order_id" int4,
  "yyjs" varchar(300) COLLATE "pg_catalog"."default",
  "yytb" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."acp_pm_cus_app"."id" IS '主键';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."is_del" IS '是否删除';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."add_time" IS '添加时间';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."add_user" IS '添加人';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."add_user_name" IS '添加人姓名';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."update_user" IS '更新人';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."update_user_name" IS '更新人姓名';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."pro_code" IS '所属省级代码';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."pro_name" IS '所属省级名称';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."city_code" IS '所属市级代码';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."city_name" IS '所属市级名称';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."reg_code" IS '区域代码';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."reg_name" IS '区域名称';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."org_code" IS '机构编号';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."org_name" IS '机构名称';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."system_id" IS '系统ID';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."yymc" IS '应用名称';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."ljdz" IS '链接地址';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."fl_id" IS '应用分类ID';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."sfnb" IS '是否内部应用(0:否1:是)';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."sfgg" IS '是否公共应用(0:否1:是)';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."yylx" IS '应用类型(0:pc,1:移动端)';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."sfjy" IS '是否禁用(0:否1:是)';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."order_id" IS '排序';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."yyjs" IS '应用介绍';
COMMENT ON COLUMN "public"."acp_pm_cus_app"."yytb" IS '应用图标';
COMMENT ON TABLE "public"."acp_pm_cus_app" IS '自定义应用管理表';

-- ----------------------------
-- Table structure for acp_pm_cus_app_cat
-- ----------------------------
DROP TABLE IF EXISTS "public"."acp_pm_cus_app_cat";
CREATE TABLE "public"."acp_pm_cus_app_cat" (
  "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "is_del" int4 NOT NULL DEFAULT 0,
  "add_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "add_user" varchar(50) COLLATE "pg_catalog"."default",
  "add_user_name" varchar(30) COLLATE "pg_catalog"."default",
  "update_time" timestamp(6),
  "update_user" varchar(50) COLLATE "pg_catalog"."default",
  "update_user_name" varchar(30) COLLATE "pg_catalog"."default",
  "pro_code" varchar(50) COLLATE "pg_catalog"."default",
  "pro_name" varchar(100) COLLATE "pg_catalog"."default",
  "city_code" varchar(50) COLLATE "pg_catalog"."default",
  "city_name" varchar(100) COLLATE "pg_catalog"."default",
  "reg_code" varchar(50) COLLATE "pg_catalog"."default",
  "reg_name" varchar(100) COLLATE "pg_catalog"."default",
  "org_code" varchar(50) COLLATE "pg_catalog"."default",
  "org_name" varchar(100) COLLATE "pg_catalog"."default",
  "system_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "flmc" varchar(100) COLLATE "pg_catalog"."default",
  "order_id" int4,
  "mark" varchar(50) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."acp_pm_cus_app_cat"."id" IS '主键';
COMMENT ON COLUMN "public"."acp_pm_cus_app_cat"."is_del" IS '是否删除';
COMMENT ON COLUMN "public"."acp_pm_cus_app_cat"."add_time" IS '添加时间';
COMMENT ON COLUMN "public"."acp_pm_cus_app_cat"."add_user" IS '添加人';
COMMENT ON COLUMN "public"."acp_pm_cus_app_cat"."add_user_name" IS '添加人姓名';
COMMENT ON COLUMN "public"."acp_pm_cus_app_cat"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."acp_pm_cus_app_cat"."update_user" IS '更新人';
COMMENT ON COLUMN "public"."acp_pm_cus_app_cat"."update_user_name" IS '更新人姓名';
COMMENT ON COLUMN "public"."acp_pm_cus_app_cat"."pro_code" IS '所属省级代码';
COMMENT ON COLUMN "public"."acp_pm_cus_app_cat"."pro_name" IS '所属省级名称';
COMMENT ON COLUMN "public"."acp_pm_cus_app_cat"."city_code" IS '所属市级代码';
COMMENT ON COLUMN "public"."acp_pm_cus_app_cat"."city_name" IS '所属市级名称';
COMMENT ON COLUMN "public"."acp_pm_cus_app_cat"."reg_code" IS '区域代码';
COMMENT ON COLUMN "public"."acp_pm_cus_app_cat"."reg_name" IS '区域名称';
COMMENT ON COLUMN "public"."acp_pm_cus_app_cat"."org_code" IS '机构编号';
COMMENT ON COLUMN "public"."acp_pm_cus_app_cat"."org_name" IS '机构名称';
COMMENT ON COLUMN "public"."acp_pm_cus_app_cat"."system_id" IS '系统ID';
COMMENT ON COLUMN "public"."acp_pm_cus_app_cat"."flmc" IS '分类名称';
COMMENT ON COLUMN "public"."acp_pm_cus_app_cat"."order_id" IS '排序';
COMMENT ON COLUMN "public"."acp_pm_cus_app_cat"."mark" IS '分类标识';
COMMENT ON TABLE "public"."acp_pm_cus_app_cat" IS '自定义应用分类';

-- ----------------------------
-- Table structure for acp_pm_cus_app_uac
-- ----------------------------
DROP TABLE IF EXISTS "public"."acp_pm_cus_app_uac";
CREATE TABLE "public"."acp_pm_cus_app_uac" (
  "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "yyid" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "qxlx" varchar(2) COLLATE "pg_catalog"."default",
  "qxid" varchar(50) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."acp_pm_cus_app_uac"."id" IS '主键';
COMMENT ON COLUMN "public"."acp_pm_cus_app_uac"."yyid" IS '自定义应用ID';
COMMENT ON COLUMN "public"."acp_pm_cus_app_uac"."qxlx" IS '权限类型(01:角色ID 02:区域ID 03:机构ID)';
COMMENT ON COLUMN "public"."acp_pm_cus_app_uac"."qxid" IS '权限ID(01时存角色ID，02时存区域ID，03时存机构ID)';
COMMENT ON TABLE "public"."acp_pm_cus_app_uac" IS '自定义应用权限设置表';

-- ----------------------------
-- View structure for vw_acp_pm_prisoner_list
-- ----------------------------
DROP VIEW IF EXISTS "public"."vw_acp_pm_prisoner_list";
CREATE VIEW "public"."vw_acp_pm_prisoner_list" AS  SELECT t.id,
    t.is_del,
    t.add_time,
    t.org_code,
    t.org_name,
    t.jgrybm,
    t.rybh,
    t.xm,
    t.zjlx,
    t.zjhm,
    t.xb,
    t.mz,
    t.gj,
    t.csrq,
    t.ryzt,
    t.dabh,
    t.rssj,
    t.ajbh,
    NULL::text AS ajlb,
    t.zzczjg,
    t.sxzm,
    t.jsh,
    t.room_name,
    t.cwh,
    t.front_photo,
    t.bjgrylx,
    acp_pm_area_prison_room.area_id,
    t.cssj,
    t.csyy,
    t.jg,
    t.gyqx,
    t.sshj
   FROM ( SELECT acp_pm_prisoner_kss_in.id,
            acp_pm_prisoner_kss_in.is_del,
            acp_pm_prisoner_kss_in.add_time,
            acp_pm_prisoner_kss_in.org_code,
            acp_pm_prisoner_kss_in.org_name,
            acp_pm_prisoner_kss_in.jgrybm,
            acp_pm_prisoner_kss_in.rybh,
            acp_pm_prisoner_kss_in.xm,
            acp_pm_prisoner_kss_in.zjlx,
            acp_pm_prisoner_kss_in.zjhm,
            acp_pm_prisoner_kss_in.xb,
            acp_pm_prisoner_kss_in.mz,
            acp_pm_prisoner_kss_in.gj,
            acp_pm_prisoner_kss_in.csrq,
            acp_pm_prisoner_kss_in.ryzt,
            acp_pm_prisoner_kss_in.dabh,
            acp_pm_prisoner_kss_in.rssj,
            acp_pm_prisoner_kss_in.ajbh,
            NULL::text AS ajlb,
            acp_pm_prisoner_kss_in.zzczjg,
            acp_pm_prisoner_kss_in.sxzm,
            acp_pm_prisoner_kss_in.jsh,
            acp_pm_prisoner_kss_in.room_name,
            acp_pm_prisoner_kss_in.cwh,
            acp_pm_prisoner_kss_in.front_photo,
            '01'::text AS bjgrylx,
            acp_pm_prisoner_kss_in.cssj,
            acp_pm_prisoner_kss_in.csyy,
            acp_pm_prisoner_kss_in.jg,
            acp_pm_prisoner_kss_in.gyqx,
            acp_pm_prisoner_kss_in.sshj
           FROM acp_pm_prisoner_kss_in
        UNION ALL
         SELECT acp_pm_prisoner_kss_out.id,
            acp_pm_prisoner_kss_out.is_del,
            acp_pm_prisoner_kss_out.add_time,
            acp_pm_prisoner_kss_out.org_code,
            acp_pm_prisoner_kss_out.org_name,
            acp_pm_prisoner_kss_out.jgrybm,
            acp_pm_prisoner_kss_out.rybh,
            acp_pm_prisoner_kss_out.xm,
            acp_pm_prisoner_kss_out.zjlx,
            acp_pm_prisoner_kss_out.zjhm,
            acp_pm_prisoner_kss_out.xb,
            acp_pm_prisoner_kss_out.mz,
            acp_pm_prisoner_kss_out.gj,
            acp_pm_prisoner_kss_out.csrq,
            acp_pm_prisoner_kss_out.ryzt,
            acp_pm_prisoner_kss_out.dabh,
            acp_pm_prisoner_kss_out.rssj,
            acp_pm_prisoner_kss_out.ajbh,
            NULL::text AS ajlb,
            acp_pm_prisoner_kss_out.zzczjg,
            acp_pm_prisoner_kss_out.sxzm,
            acp_pm_prisoner_kss_out.jsh,
            acp_pm_prisoner_kss_out.room_name,
            acp_pm_prisoner_kss_out.cwh,
            acp_pm_prisoner_kss_out.front_photo,
            '01'::text AS bjgrylx,
            acp_pm_prisoner_kss_out.cssj,
            acp_pm_prisoner_kss_out.csyy,
            acp_pm_prisoner_kss_out.jg,
            acp_pm_prisoner_kss_out.gyqx,
            acp_pm_prisoner_kss_out.sshj
           FROM acp_pm_prisoner_kss_out
        UNION ALL
         SELECT acp_pm_prisoner_jds_in.id,
            acp_pm_prisoner_jds_in.is_del,
            acp_pm_prisoner_jds_in.add_time,
            acp_pm_prisoner_jds_in.org_code,
            acp_pm_prisoner_jds_in.org_name,
            acp_pm_prisoner_jds_in.jgrybm,
            acp_pm_prisoner_jds_in.rybh,
            acp_pm_prisoner_jds_in.xm,
            acp_pm_prisoner_jds_in.zjlx,
            acp_pm_prisoner_jds_in.zjhm,
            acp_pm_prisoner_jds_in.xb,
            acp_pm_prisoner_jds_in.mz,
            acp_pm_prisoner_jds_in.gj,
            acp_pm_prisoner_jds_in.csrq,
            acp_pm_prisoner_jds_in.ryzt,
            acp_pm_prisoner_jds_in.dabh,
            acp_pm_prisoner_jds_in.rssj,
            acp_pm_prisoner_jds_in.ajbh,
            NULL::text AS ajlb,
            NULL::character varying AS zzczjg,
            NULL::character varying AS sxzm,
            acp_pm_prisoner_jds_in.jsh,
            acp_pm_prisoner_jds_in.room_name,
            acp_pm_prisoner_jds_in.cwh,
            acp_pm_prisoner_jds_in.front_photo,
            '03'::text AS bjgrylx,
            acp_pm_prisoner_jds_in.cssj,
            acp_pm_prisoner_jds_in.csyy,
            acp_pm_prisoner_jds_in.jg,
            acp_pm_prisoner_jds_in.gyqx,
            NULL::character varying AS sshj
           FROM acp_pm_prisoner_jds_in
        UNION ALL
         SELECT acp_pm_prisoner_jds_out.id,
            acp_pm_prisoner_jds_out.is_del,
            acp_pm_prisoner_jds_out.add_time,
            acp_pm_prisoner_jds_out.org_code,
            acp_pm_prisoner_jds_out.org_name,
            acp_pm_prisoner_jds_out.jgrybm,
            acp_pm_prisoner_jds_out.rybh,
            acp_pm_prisoner_jds_out.xm,
            acp_pm_prisoner_jds_out.zjlx,
            acp_pm_prisoner_jds_out.zjhm,
            acp_pm_prisoner_jds_out.xb,
            acp_pm_prisoner_jds_out.mz,
            acp_pm_prisoner_jds_out.gj,
            acp_pm_prisoner_jds_out.csrq,
            acp_pm_prisoner_jds_out.ryzt,
            acp_pm_prisoner_jds_out.dabh,
            acp_pm_prisoner_jds_out.rssj,
            acp_pm_prisoner_jds_out.ajbh,
            NULL::text AS ajlb,
            NULL::character varying AS zzczjg,
            NULL::character varying AS sxzm,
            acp_pm_prisoner_jds_out.jsh,
            acp_pm_prisoner_jds_out.room_name,
            acp_pm_prisoner_jds_out.cwh,
            acp_pm_prisoner_jds_out.front_photo,
            '03'::text AS bjgrylx,
            acp_pm_prisoner_jds_out.cssj,
            acp_pm_prisoner_jds_out.csyy,
            acp_pm_prisoner_jds_out.jg,
            acp_pm_prisoner_jds_out.gyqx,
            NULL::character varying AS sshj
           FROM acp_pm_prisoner_jds_out
        UNION ALL
         SELECT acp_pm_prisoner_jls_in.id,
            acp_pm_prisoner_jls_in.is_del,
            acp_pm_prisoner_jls_in.add_time,
            acp_pm_prisoner_jls_in.org_code,
            acp_pm_prisoner_jls_in.org_name,
            acp_pm_prisoner_jls_in.jgrybm,
            acp_pm_prisoner_jls_in.rybh,
            acp_pm_prisoner_jls_in.xm,
            acp_pm_prisoner_jls_in.zjlx,
            acp_pm_prisoner_jls_in.zjhm,
            acp_pm_prisoner_jls_in.xb,
            acp_pm_prisoner_jls_in.mz,
            acp_pm_prisoner_jls_in.gj,
            acp_pm_prisoner_jls_in.csrq,
            acp_pm_prisoner_jls_in.ryzt,
            acp_pm_prisoner_jls_in.dabh,
            acp_pm_prisoner_jls_in.rssj,
            acp_pm_prisoner_jls_in.ajbh,
            NULL::text AS ajlb,
            acp_pm_prisoner_jls_in.zzczjg,
            NULL::character varying AS sxzm,
            acp_pm_prisoner_jls_in.jsh,
            acp_pm_prisoner_jls_in.room_name,
            acp_pm_prisoner_jls_in.cwh,
            acp_pm_prisoner_jls_in.front_photo,
            '02'::text AS bjgrylx,
            acp_pm_prisoner_jls_in.cssj,
            acp_pm_prisoner_jls_in.csyy,
            acp_pm_prisoner_jls_in.jg,
            acp_pm_prisoner_jls_in.gyqx,
            NULL::character varying AS sshj
           FROM acp_pm_prisoner_jls_in
        UNION ALL
         SELECT acp_pm_prisoner_jls_out.id,
            acp_pm_prisoner_jls_out.is_del,
            acp_pm_prisoner_jls_out.add_time,
            acp_pm_prisoner_jls_out.org_code,
            acp_pm_prisoner_jls_out.org_name,
            acp_pm_prisoner_jls_out.jgrybm,
            acp_pm_prisoner_jls_out.rybh,
            acp_pm_prisoner_jls_out.xm,
            acp_pm_prisoner_jls_out.zjlx,
            acp_pm_prisoner_jls_out.zjhm,
            acp_pm_prisoner_jls_out.xb,
            acp_pm_prisoner_jls_out.mz,
            acp_pm_prisoner_jls_out.gj,
            acp_pm_prisoner_jls_out.csrq,
            acp_pm_prisoner_jls_out.ryzt,
            acp_pm_prisoner_jls_out.dabh,
            acp_pm_prisoner_jls_out.rssj,
            acp_pm_prisoner_jls_out.ajbh,
            NULL::text AS ajlb,
            acp_pm_prisoner_jls_out.zzczjg,
            NULL::character varying AS sxzm,
            acp_pm_prisoner_jls_out.jsh,
            acp_pm_prisoner_jls_out.room_name,
            acp_pm_prisoner_jls_out.cwh,
            acp_pm_prisoner_jls_out.front_photo,
            '02'::text AS bjgrylx,
            acp_pm_prisoner_jls_out.cssj,
            acp_pm_prisoner_jls_out.csyy,
            acp_pm_prisoner_jls_out.jg,
            acp_pm_prisoner_jls_out.gyqx,
            NULL::character varying AS sshj
           FROM acp_pm_prisoner_jls_out) t
     JOIN acp_pm_area_prison_room ON t.jsh::text = acp_pm_area_prison_room.room_code::text AND t.org_code::text = acp_pm_area_prison_room.org_code::text;