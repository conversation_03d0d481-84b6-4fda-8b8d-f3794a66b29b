<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.rs</groupId>
	<artifactId>rs-master</artifactId>
	<version>${rs.version}</version>
	<packaging>pom</packaging>
	<name>${project.artifactId}</name>
	<description>监管实战平台-主项目</description>

	<properties>
		<rs.version>1.0.0</rs.version>
		<java.version>1.8</java.version>
		<maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <maven-surefire-plugin.version>3.0.0-M5</maven-surefire-plugin.version>
        <maven-compiler-plugin.version>3.13.0</maven-compiler-plugin.version>
		<flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>
		<lombok.version>1.18.4</lombok.version>
        <spring.boot.version>2.4.3</spring.boot.version>
        <mapstruct.version>1.6.3</mapstruct.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.report.outputEncoding>UTF-8</project.report.outputEncoding>
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.rs</groupId>
				<artifactId>rs-dependencies</artifactId>
				<version>${rs.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
        <pluginManagement>
            <plugins>
                <!-- maven-surefire-plugin 插件，用于运行单元测试。 -->
                <!-- 注意，需要使用 3.0.X+，因为要支持 Junit 5 版本 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                </plugin>
                <!-- maven-compiler-plugin 插件，解决 Lombok + MapStruct 组合 -->
                <!-- https://stackoverflow.com/questions/33483697/re-run-spring-boot-configuration-annotation-processor-to-update-generated-metada -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
						<fork>true</fork>
						<useIncrementalCompilation>true</useIncrementalCompilation>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.springframework.boot</groupId>
                                <artifactId>spring-boot-configuration-processor</artifactId>
                                <version>${spring.boot.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <!-- 统一 revision 版本 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <flattenMode>oss</flattenMode>
                    <updatePomFile>true</updatePomFile>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                    </execution>
                    <execution>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

	<!-- 使用私服Maven源，提升下载速度 -->
	<repositories>
		<repository>
			<id>gosuncn</id>
			<name>gosuncn Repository</name>
			<url>http://113.98.54.16:13081/content/groups/common/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
	</repositories>

	<!-- 发布到私服 -->
	<distributionManagement>
		<repository>
			<id>nexus-release</id>
			<name>nexus-release</name>
			<url>http://113.98.54.16:13081/content/repositories/custom/</url>
		</repository>
	</distributionManagement>

	<!-- Maven Profiles 用于可配置打包 -->
	<profiles>
		<!-- 默认Profile - 包含所有模块 -->
		<profile>
			<id>all</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<modules>
				<module>rs-dependencies</module>
				<module>rs-framework</module>
				<module>rs-adapter-zhjg</module>
				<module>rs-gateway</module>
				<module>rs-module-infra</module>
				<module>rs-module-base</module>
				<module>rs-module-ihc</module>
				<module>rs-module-acp</module>
				<module>rs-module-demo</module>
				<module>rs-module-rgf</module>
				<module>rs-module-integ</module>
				<module>rs-module-dam</module>
				<module>rs-module-tem</module>
				<module>rs-adapter-bsp</module>
				<module>rs-module-sps</module>
				<module>rs-module-pam</module>
				<module>rs-module-ptm</module>
				<module>rs-module-third-api</module>
				<module>rs-module-devops</module>
			</modules>
		</profile>
		<!-- ACP模块打包Profile -->
		<profile>
			<id>acp</id>
			<modules>
				<!-- 基础依赖模块 -->
				<module>rs-dependencies</module>
				<module>rs-framework</module>
				<module>rs-module-base</module>
				<module>rs-adapter-bsp</module>
				<module>rs-module-third-api</module>
				<!-- ACP相关依赖模块 -->
				<module>rs-adapter-zhjg</module>
				<module>rs-module-ptm</module>
				<!-- ACP业务模块 -->
				<module>rs-module-acp</module>
			</modules>
		</profile>

		<!-- INFRA模块打包Profile -->
		<profile>
			<id>infra</id>
			<modules>
				<!-- 基础依赖模块 -->
				<module>rs-dependencies</module>
				<module>rs-framework</module>
				<module>rs-module-base</module>
				<module>rs-adapter-bsp</module>
				<module>rs-module-third-api</module>
				<!-- INFRA业务模块 -->
				<module>rs-module-infra</module>
			</modules>
		</profile>

		<!-- TEM模块打包Profile -->
		<profile>
			<id>tem</id>
			<modules>
				<!-- 基础依赖模块 -->
				<module>rs-dependencies</module>
				<module>rs-framework</module>
				<module>rs-module-base</module>
				<module>rs-adapter-bsp</module>
				<module>rs-module-third-api</module>
				<!-- TEM业务模块 -->
				<module>rs-module-tem</module>
			</modules>
		</profile>

		<!-- IHC模块打包Profile -->
		<profile>
			<id>ihc</id>
			<modules>
				<!-- 基础依赖模块 -->
				<module>rs-dependencies</module>
				<module>rs-framework</module>
				<module>rs-module-base</module>
				<module>rs-adapter-bsp</module>
				<module>rs-module-third-api</module>
				<!-- IHC业务模块 -->
				<module>rs-module-ihc</module>
			</modules>
		</profile>

		<!-- DAM模块打包Profile -->
		<profile>
			<id>dam</id>
			<modules>
				<!-- 基础依赖模块 -->
				<module>rs-dependencies</module>
				<module>rs-framework</module>
				<module>rs-module-base</module>
				<module>rs-adapter-bsp</module>
				<module>rs-module-third-api</module>
				<!-- DAM业务模块 -->
				<module>rs-module-dam</module>
			</modules>
		</profile>

		<!-- RGF模块打包Profile -->
		<profile>
			<id>rgf</id>
			<modules>
				<!-- 基础依赖模块 -->
				<module>rs-dependencies</module>
				<module>rs-framework</module>
				<module>rs-module-base</module>
				<module>rs-adapter-bsp</module>
				<module>rs-module-third-api</module>
				<!-- RGF相关依赖模块 -->
				<module>rs-module-ihc</module>
				<!-- RGF业务模块 -->
				<module>rs-module-rgf</module>
			</modules>
		</profile>

		<!-- INTEG模块打包Profile -->
		<profile>
			<id>integ</id>
			<modules>
				<!-- 基础依赖模块 -->
				<module>rs-dependencies</module>
				<module>rs-framework</module>
				<module>rs-module-base</module>
				<module>rs-adapter-bsp</module>
				<module>rs-module-third-api</module>
				<!-- INTEG相关依赖模块 -->
				<module>rs-module-ihc</module>
				<!-- INTEG业务模块 -->
				<module>rs-module-integ</module>
			</modules>
		</profile>

		<!-- SPS模块打包Profile -->
		<profile>
			<id>sps</id>
			<modules>
				<!-- 基础依赖模块 -->
				<module>rs-dependencies</module>
				<module>rs-framework</module>
				<module>rs-module-base</module>
				<module>rs-adapter-bsp</module>
				<module>rs-module-third-api</module>
				<!-- SPS业务模块 -->
				<module>rs-module-sps</module>
			</modules>
		</profile>

		<!-- PAM模块打包Profile -->
		<profile>
			<id>pam</id>
			<modules>
				<!-- 基础依赖模块 -->
				<module>rs-dependencies</module>
				<module>rs-framework</module>
				<module>rs-module-base</module>
				<module>rs-adapter-bsp</module>
				<module>rs-module-third-api</module>
				<!-- PAM相关依赖模块 -->
				<module>rs-module-dam</module>
				<!-- PAM业务模块 -->
				<module>rs-module-pam</module>
			</modules>
		</profile>

		<!-- PTM模块打包Profile -->
		<profile>
			<id>ptm</id>
			<modules>
				<!-- 基础依赖模块 -->
				<module>rs-dependencies</module>
				<module>rs-framework</module>
				<module>rs-module-base</module>
				<module>rs-adapter-bsp</module>
				<module>rs-module-third-api</module>
				<!-- PTM业务模块 -->
				<module>rs-module-ptm</module>
			</modules>
		</profile>

		<!-- DEVOPS模块打包Profile -->
		<profile>
			<id>devops</id>
			<modules>
				<!-- 基础依赖模块 -->
				<module>rs-dependencies</module>
				<module>rs-framework</module>
				<module>rs-module-base</module>
				<module>rs-adapter-bsp</module>
				<module>rs-module-third-api</module>
				<!-- DEVOPS业务模块 -->
				<module>rs-module-devops</module>
			</modules>
		</profile>

		<!-- GATEWAY网关模块打包Profile -->
		<profile>
			<id>gateway</id>
			<modules>
				<!-- 基础依赖模块 -->
				<module>rs-dependencies</module>
				<module>rs-framework</module>
				<!-- 网关模块 -->
				<module>rs-gateway</module>
			</modules>
		</profile>
	</profiles>
</project>
