package com.rs.module.dam.ocr.gosuncn.callback;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.orm.mybatis.util.WrapUtil;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.rs.module.dam.config.WebSocketServer;
import com.rs.module.dam.constant.DamConstants;
import com.rs.module.dam.constant.OcrConstants;
import com.rs.module.dam.constant.RedisConstants;
import com.rs.module.dam.entity.archive.ArchiveJobCatalogDO;
import com.rs.module.dam.entity.archive.ArchiveJobDO;
import com.rs.module.dam.entity.log.IspInvokingDO;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.entity.prisoner.PrisonerCatalogDO;
import com.rs.module.dam.entity.sys.ConfigGroupDO;
import com.rs.module.dam.ocr.gosuncn.GoOcrCallbackParam;
import com.rs.module.dam.ocr.gosuncn.GoOcrCallbackParam.SerDatas;
import com.rs.module.dam.ocr.gosuncn.GoOcrStrategy;
import com.rs.module.dam.ocr.gosuncn.GoUtil;
import com.rs.module.dam.ocr.util.OcrUtil;
import com.rs.module.dam.ocr.vo.OcrRateProgress;
import com.rs.module.dam.service.archive.ArchiveJobCatalogService;
import com.rs.module.dam.service.archive.ArchiveJobService;
import com.rs.module.dam.service.log.IspInvokingService;
import com.rs.module.dam.service.material.MaterialInfoService;
import com.rs.module.dam.service.ocr.CatalogMarkService;
import com.rs.module.dam.service.prisoner.PrisonerCatalogService;
import com.rs.module.dam.service.sys.ConfigGroupService;
import com.rs.module.dam.util.IpUtil;
import com.rs.module.dam.util.RedisTemplateUtil;
import com.rs.module.dam.websocket.WebSocketCode;

import lombok.extern.slf4j.Slf4j;

/**
 * gosuncn-高新兴智能编目回调处理
 * <AUTHOR>
 * @date 2025年4月27日
 */
@Component
@Slf4j
public class ZnbmCallback implements IOcrCallback{
	
	//标题匹配相似度阈值
    @Value("${conf.catalog.familiarity:0.6}")
    private Double familiarity;
	
    //启用标题自动编目
    @Value("${isp.ocr2.enableCatalogBm:true}")
    private boolean enableCatalogBm;
    
    //是否合并同名标题
    @Value("${isp.ocr2.mergeSameTitle:true}")
    private boolean mergeSameTitle;
    
    @Autowired
    private ConfigGroupService configGroupService;
	
	@Autowired
	private IspInvokingService ispInvokingService;
	
	@Autowired
	private ArchiveJobService archiveJobService;
	
	@Autowired
    private PrisonerCatalogService dossierAjCatalogService;
	
	@Autowired
    private CatalogMarkService dossierCatalogOcrMarkService;
	
	@Autowired
	private MaterialInfoService dossierMaterialInfoService;
	
	@Autowired
	private ArchiveJobCatalogService dmsArchiveJobCatalogService;
	
	@Autowired
    RedisTemplateUtil redisTemplateUtil;
    
    @Autowired
    GoOcrStrategy goOcrStrategy;

	/**
	 * 回调处理
	 * @param params GoOcrCallbackParam 回调参数
	 */
	@SuppressWarnings("serial")
	@Override
	public void handle(GoOcrCallbackParam params) {
		
		//服务返回数据
		SerDatas serDatas = params.getSerDatas();
		
		//构建isp调用对象
		IspInvokingDO ispInvoking = new IspInvokingDO();
		ispInvoking.setId(StringUtil.getGuid32());
		ispInvoking.setJobId(serDatas.getBatchId());
		ispInvoking.setInput(JSON.toJSONString(params));
		ispInvoking.setSerCode(params.getSerCode());
		ispInvoking.setInvokeType(OcrConstants.OCR_INVOKE_TYPE_ZNBM_CALLBACK);
		ispInvoking.setRequestFrom(OcrConstants.OCR_REQUEST_FROM_ZNBM);
		ispInvoking.setServerIp(IpUtil.getHostIp());
		ispInvoking.setCheckType(OcrConstants.OCR_INVOKE_TYPE_ZNBM_CALLBACK);
		
		//开始调用时间
		long begin = System.currentTimeMillis();
		
		try {
			JSONArray resultArray = GoUtil.getOcrResultsArray(serDatas);
			if(!resultArray.isEmpty() || !serDatas.getFailed_ids().isEmpty()) {
				Set<String> materialIds = GoUtil.getOcrResultMaterialIds(serDatas);
				if(CollectionUtil.isNull(materialIds)) {
					String message = "参数中缺乏材料Id";
					log.error("ocr处理-智能编目回调异常: {}", message);
					throw new RuntimeException(message);
				}
				else {
					ispInvoking.setFileCount(materialIds.size());
				}
				
				//查询本次回调关联的历史调用任务
				QueryWrapper<ArchiveJobDO> jobWrapper = WrapUtil.eq(ArchiveJobDO.class, "batch_id", serDatas.getBatchId())
						.in("material_id", materialIds);
				List<ArchiveJobDO> jobList = archiveJobService.list(jobWrapper);
				if(CollectionUtil.isNull(jobList)) {
					String message = "找不到历史调用任务";
					log.error("ocr处理-智能编目回调异常: {}", message);
					throw new RuntimeException(message);
				}
				
				//第一个调用任务
				ArchiveJobDO firstJob = jobList.get(0);
				
				//构建材料与任务映射Map
				Map<String, ArchiveJobDO> materialJobMap = new HashMap<>();
				for(ArchiveJobDO job : jobList) {
					materialJobMap.put(job.getMaterialId(), job);
				}
				
				//查询卷宗配置信息
				ConfigGroupDO dmsConfigGroup = configGroupService.getUserConfigGroup(firstJob.getOrgCode());
				
				//构建isp调用对象
				ispInvoking.setAddress(dmsConfigGroup.getOcrServer());
				ispInvoking.setJgrybm(firstJob.getJgrybm());
				
				//查询本次回调关联的材料
				QueryWrapper<MaterialInfoDO> materialWrapper = WrapUtil.eq(MaterialInfoDO.class, "jgrybm", firstJob.getJgrybm())
						.in("id", materialIds);
				List<MaterialInfoDO> materialList = dossierMaterialInfoService.list(materialWrapper);
				if(CollectionUtil.isNull(materialList)) {
					String message = "找不到待处理的材料信息";
					log.error("ocr处理-智能编目回调异常: {}", message);
					throw new RuntimeException(message);
				}
				
				//第一个编目材料
				MaterialInfoDO firstMaterial = materialList.get(0);
				
				//是否快速组卷的材料
				boolean isKszj = firstMaterial.getType().equals("3");
				
				//构建材料id与材料映射Map
				Map<String, MaterialInfoDO> materialMap = new HashMap<>();
				for(MaterialInfoDO material : materialList) {
					materialMap.put(material.getId(), material);
				}
				
				//获取案件编目目录
				PrisonerCatalogDO ajCatalog = isKszj ? null :
					dossierAjCatalogService.getOne(WrapUtil.eq(PrisonerCatalogDO.class, "jgrybm", firstJob.getJgrybm()));
				
				//案件编目子目录
				List<JSONObject> ajChildCatalog = isKszj ? null :
						OcrUtil.getAllChildCatalog(ajCatalog.getBmCatalogData());
				
				//启用标题自动编目
				if(enableCatalogBm) {
					log.info("ocr处理-智能编目回调-开始标题自动编目");
					
					//需更新的归档任务
					Map<String, ArchiveJobDO> needUpdateJobs = new HashMap<>();
					
					//需更新的编目任务
					List<ArchiveJobCatalogDO> updateCatalogList = new ArrayList<>();
					
					//处理返回数据(返回code200)
					if(!resultArray.isEmpty()) {
						for(int i = 0; i < resultArray.size(); i ++) {
							JSONObject result = resultArray.getJSONObject(i);
							
							//文件Id和标题
							String ocrResultFileId = result.getString("fileId");
							String docTitle = result.getString("docTitle");
							String[] ocrResultFileIds = ocrResultFileId.split("_");
							if(ocrResultFileIds.length > 1) {
								
								//材料文件Id和材料Id
								String fileId = ocrResultFileIds[0];
								String oldMaterialId = ocrResultFileIds[1];
								
								//材料关联的任务信息
								ArchiveJobDO job = materialJobMap.get(oldMaterialId);
								
								//标题编目任务
								ArchiveJobCatalogDO jobCatalog = new ArchiveJobCatalogDO();
								jobCatalog.setId(fileId);
								jobCatalog.setState("1");
								jobCatalog.setUpdateTime(new Date());
								
								//标题不为空
								if(StringUtil.isNotEmpty(docTitle)) {
									jobCatalog.setDocTitle(docTitle);
									
									//编目组卷(非快速组卷)
									if(!isKszj) {
										
										//获取目录分类Id
										String catalogId = OcrUtil.getCatalogByName(ajChildCatalog, docTitle, familiarity, firstJob.getJgrybm(),
												oldMaterialId, dossierCatalogOcrMarkService);
										
										//能识别到目录分类
										if(StringUtil.isNotEmpty(catalogId)) {
											jobCatalog.setCatalogId(catalogId);
										}
									}
								}
								
								//添加到待更新的标题编目任务中
								updateCatalogList.add(jobCatalog);
								
								//更新任务(从任务材料信息中删除当前文件)
								if(job != null) {
									String newFileId = GoUtil.deleteJsonArrayStrByProp(job.getFileContent(), "id", fileId);
									job.setFileContent(newFileId);
									if(JSONArray.parseArray(newFileId).isEmpty()) {
										job.setState(DamConstants.JOB_STATE_YES);
										job.setEndTime(new Date());
										String ocrKey = String.format(RedisConstants.START_OCR_MATER_ID, job.getMaterialId());
										redisTemplateUtil.deleteKey(ocrKey);
									}
									needUpdateJobs.put(job.getId(), job);
								}
							}
						}
					}
					
					//处理失败数据(返回code-406)
					if(!serDatas.getFailed_ids().isEmpty()) {
						for(int i = 0; i < serDatas.getFailed_ids().size(); i ++) {
							String fileId = serDatas.getFailed_ids().getString(i);
							String[] fileIdArr = fileId.split("_");
							if(fileIdArr.length > 1) {
								
								//材料文件Id和材料Id
								String materialFileId = fileIdArr[0];
								String materialId = fileIdArr[1];
								
								//材料关联的任务信息
								ArchiveJobDO job = materialJobMap.get(materialId);
								
								ArchiveJobCatalogDO jobCatalog = new ArchiveJobCatalogDO();
								jobCatalog.setId(materialFileId);
								jobCatalog.setState("2");
								jobCatalog.setUpdateTime(new Date());
								
								//添加到待更新的标题编目任务中
								updateCatalogList.add(jobCatalog);
								
								//更新任务(从任务材料信息中删除当前文件)
								if(job != null) {
									String newFileId = GoUtil.deleteJsonArrayStrByProp(job.getFileContent(), "id", materialFileId);
									job.setFileContent(newFileId);
									needUpdateJobs.put(job.getId(), job);
								}
							}
						}
					}
					
					//更新标题编目任务
					if(CollectionUtil.isNotNull(updateCatalogList)) {
						dmsArchiveJobCatalogService.updateBatchById(updateCatalogList, 100);
					}					
					
					//人员未ocr识别的数量
					QueryWrapper<ArchiveJobCatalogDO> catalogJobCountWrapper = WrapUtil.eq(ArchiveJobCatalogDO.class, 
							new String[]{"jgrybm", "state"}, new String[]{firstJob.getJgrybm(), "0"});
		        	int noProcessedCount = dmsArchiveJobCatalogService.count(catalogJobCountWrapper);
		        	
		        	//ocr识别完成后全局编目(存在没有标题的材料)
		        	if(noProcessedCount == 0) {
			        	
			        	//案件全部标题编目任务
		        		QueryWrapper<ArchiveJobCatalogDO> allCatalogJobWrapper = WrapUtil.eq(ArchiveJobCatalogDO.class, "jgrybm", firstJob.getJgrybm())
		        				.orderByAsc("material_xh").orderByAsc("file_xh");
		        		List<ArchiveJobCatalogDO> allCatalogJobList = dmsArchiveJobCatalogService.list(allCatalogJobWrapper);
		        		
		        		//正常智能编目(非快速组卷)
		        		if(!isKszj) {
		        			
		        			//待更新的材料
			        		Map<String, MaterialInfoDO> needUpdateMaterials = new HashMap<>();
			        		
			        		//待添加的材料
			        		List<MaterialInfoDO> needAddMaterials = new ArrayList<>();
			        		
			        		//已处理的材料IdMap(key:原材料Id,value:名称|材料)
			        		Map<String, Map<String, MaterialInfoDO>> dealedMaterialIdMap = new HashMap<>();
			        		
			        		//最后材料的标题和编目Id
		        			String lastTitle = null;
			        		String lastCatalogId = null;
			        		
			        		//循环处理更新材料标题(含标题合并)
			        		for(ArchiveJobCatalogDO catalogJob : allCatalogJobList) {
			        			
			        			//初始化材料
			        			MaterialInfoDO materialInfo = new MaterialInfoDO();
			        			materialInfo.setId(catalogJob.getMaterialId());
			        			materialInfo.setCatalogId(catalogJob.getCatalogId());
			        			materialInfo.setXh(catalogJob.getMaterialXh());
			        			materialInfo.setPageCount(1);
			        			
			        			//识别成功
			        			if(catalogJob.getState().equals("1")) {
				        			
				        			//文件识别到标题
				        			if(StringUtil.isNotEmpty(catalogJob.getDocTitle())) {
				        				lastTitle = catalogJob.getDocTitle();
				        				lastCatalogId = catalogJob.getCatalogId();
				        				materialInfo.setName(lastTitle);
				        				materialInfo.setCatalogId(lastCatalogId);
				        			}
				        			else if(StringUtil.isNotEmpty(lastTitle)){
				        				materialInfo.setName(lastTitle);
				        				materialInfo.setCatalogId(lastCatalogId);
				        			}
				        			else {
				        				materialInfo.setName("材料无法识别");
				        			}
				        			
				        			//未编目
				        			if(StringUtil.isEmpty(materialInfo.getCatalogId())){
				        				materialInfo.setType(DamConstants.FILE_TYPE_WBM);
				        			}
				        			else {
				        				materialInfo.setType(DamConstants.FILE_TYPE_BM);
				        			}
			        			}
			        			
			        			//识别失败
			        			else {
			        				materialInfo.setName("材料无法识别");
			        				materialInfo.setType(DamConstants.FILE_TYPE_WBM);
			        			}
			        			
			        			//原始材料
			        			MaterialInfoDO originalMaterialInfo = materialMap.get(materialInfo.getId());
			        			if(originalMaterialInfo != null) {
			        				
			        				//材料包含多个文件时
			        				if(originalMaterialInfo.getPageCount() > 1) {
			        					JSONArray currentMaterialFile = OcrUtil.buildSingleFileJsonArray(originalMaterialInfo.getMaterialFile(), catalogJob.getId());
			        					materialInfo.setMaterialFile(currentMaterialFile.toJSONString());
			        					
			        					//已处理的材料(根据原材料Id查找)
			        					Map<String, MaterialInfoDO> dealedMaterialMap = dealedMaterialIdMap.get(materialInfo.getId());
			        					
			        					//材料未处理-添加到已处理材料Map和待更新材料中
					        			if(dealedMaterialMap == null){
					        				needUpdateMaterials.put(materialInfo.getId(), materialInfo);
					        				dealedMaterialIdMap.put(materialInfo.getId(), new HashMap<String, MaterialInfoDO>() {{
					        					put(materialInfo.getName(), materialInfo);
					        				}});					        				
					        			}
					        			
					        			//材料已处理-标题一致时合并
					        			else if(dealedMaterialMap.containsKey(materialInfo.getName())) {
					        				MaterialInfoDO dealedMaterialInfo = dealedMaterialMap.get(materialInfo.getName());					        				
					        				JSONArray existMaterialFiles = JSONArray.parseArray(dealedMaterialInfo.getMaterialFile());
					        				existMaterialFiles.addAll(currentMaterialFile);
					        				dealedMaterialInfo.setMaterialFile(existMaterialFiles.toJSONString());
					        				dealedMaterialInfo.setPageCount(existMaterialFiles.size());
					        			}
					        			
					        			//材料已处理-标题不一致时添加
					        			else {
					        				//保存到材料已编目名称Map中
					        				dealedMaterialMap.put(materialInfo.getName(), materialInfo);
					        				
					        				//重新设置材料属性
					        				materialInfo.setId(StringUtil.getGuid32());
					        				materialInfo.setJgrybm(originalMaterialInfo.getJgrybm());
					        				materialInfo.setMaterialId(originalMaterialInfo.getMaterialId());
					        				dossierMaterialInfoService.setMaterialInfoXh(materialInfo);
					        				needAddMaterials.add(materialInfo);
					        			}
			        				}
			        				
			        				//材料只包含一个文件
			        				else {
			        					needUpdateMaterials.put(materialInfo.getId(), materialInfo);
			        				}
			        			}			        			
			        		}
			        		
			        		//添加材料(基于标题编目任务)
							if(CollectionUtil.isNotNull(needAddMaterials)) {
								dossierMaterialInfoService.saveBatch(needAddMaterials, 100);
							}
			        		
							//更新材料(基于标题编目任务)
							if(CollectionUtil.isNotNull(needUpdateMaterials)) {
								List<MaterialInfoDO> needUpdateMaterialsList = needUpdateMaterials.values().stream().collect(Collectors.toList());
								dossierMaterialInfoService.updateBatchById(needUpdateMaterialsList, 100);
							}
		        		}
		        		
		        		//快速组卷智能编目
		        		else {
		        			
		        			//待添加的材料
			        		List<MaterialInfoDO> addMaterialList = new ArrayList<>();
			        		
			        		//历史材料Map
			        		Map<String, MaterialInfoDO> materialInfoMap = new HashMap<>();
			        		
			        		//循环处理添加材料
			        		for(int i = 0; i < allCatalogJobList.size(); i ++) {
			        			ArchiveJobCatalogDO catalogJob = allCatalogJobList.get(i);
			        			
			        			//获取历史材料
			        			MaterialInfoDO oldMaterialInfo = materialInfoMap.get(catalogJob.getMaterialId());
			        			if(oldMaterialInfo == null) {
			        				oldMaterialInfo = dossierMaterialInfoService.getById(catalogJob.getMaterialId());
			        				if(oldMaterialInfo == null) {
			        					continue;
			        				}
			        				else {
			        					materialInfoMap.put(catalogJob.getMaterialId(), oldMaterialInfo);
			        				}
			        			}
			        			
			        			//历史材料文件列表
								JSONArray materialFileArray = JSONArray.parseArray(oldMaterialInfo.getMaterialFile());
			        			
			        			//删除任务中的材料文件信息
								JSONObject fileJson = OcrUtil.popJobFileJson(materialFileArray, catalogJob.getId());
			        			
			        			//构建新的材料
			        			MaterialInfoDO materialInfo = new MaterialInfoDO();
			        			BeanUtils.copyProperties(oldMaterialInfo, materialInfo);
			        			materialInfo.setId(StringUtil.getGuid32());
			        			materialInfo.setXh(i + 1);
			        			materialInfo.setMaterialFile(fileJson.toJSONString());
			        			
			        			//设置标题
			        			if(StringUtil.isNotEmpty(catalogJob.getDocTitle())) {
			        				materialInfo.setName(catalogJob.getDocTitle());
			        			}
			        			else {
			        				materialInfo.setName("材料无法识别");
			        			}
			        			
			        			//暂存到待添加的材料文件中
			        			addMaterialList.add(materialInfo);
			        			
			        			//从历史材料中清除文件
			        			oldMaterialInfo.setMaterialFile(materialFileArray.toJSONString());
			        			materialInfoMap.put(catalogJob.getMaterialId(), oldMaterialInfo);
			        		}
			        		
			        		//快速组卷添加材料
							if(CollectionUtil.isNotNull(addMaterialList)) {
								dossierMaterialInfoService.saveBatch(addMaterialList, 100);
							}
							
							//快速组卷处理历史材料
							if(CollectionUtil.isNotNull(materialInfoMap)) {
								
								//待更新的材料
				        		List<MaterialInfoDO> updateMaterialList = new ArrayList<>();
								
								//待删除的材料Id
				        		List<String> deleteMaterialIdList = new ArrayList<>();				        		
				        		
				        		//循环处理材料
								for(Map.Entry<String, MaterialInfoDO> entry : materialInfoMap.entrySet()) {
									MaterialInfoDO materialInfo = entry.getValue();
									JSONArray materialInfoFiles = JSONArray.parseArray(materialInfo.getMaterialFile());
									if(materialInfoFiles.isEmpty()) {
										deleteMaterialIdList.add(entry.getKey());
									}
									else {
										materialInfo.setPageCount(materialInfoFiles.size());
										updateMaterialList.add(materialInfo);
									}
								}
								
								//快速组卷更新材料
								if(CollectionUtil.isNotNull(updateMaterialList)) {
									dossierMaterialInfoService.updateBatchById(updateMaterialList, 100);
								}
								
								//快速组卷删除材料
								if(CollectionUtil.isNotNull(deleteMaterialIdList)) {
									dossierMaterialInfoService.removeByIds(deleteMaterialIdList);
								}
							}
		        		}
		        	}
		        	
					//更新归档任务
					if(!needUpdateJobs.isEmpty()) {
						List<ArchiveJobDO> needUpdateJobsList = needUpdateJobs.values().stream().collect(Collectors.toList());
						archiveJobService.updateBatchById(needUpdateJobsList, 100);
					}
					
					//处理进度
					if(serDatas.getProgress() != 0) {
						handleProcess(firstJob.getJgrybm(), serDatas.getBatchId(), serDatas.getProgress());
					}
				}
				
				//普通编目
				else {
					//需添加的新材料(识别成功或失败)
					Map<String, MaterialInfoDO> needAddMaterials = new HashMap<>();
					
					//需更新的材料
					Map<String, MaterialInfoDO> needUpdateMaterials = new HashMap<>();
					
					//需删除的材料
					Set<String> needDeleteMaterials = new HashSet<>();
					
					//需更新的任务
					Map<String, ArchiveJobDO> needUpdateJobs = new HashMap<>();
					
					//处理返回数据(返回code200)
					if(!resultArray.isEmpty()) {
						for(int i = 0; i < resultArray.size(); i ++) {
							JSONObject result = resultArray.getJSONObject(i);
							
							//文件Id和标题
							String ocrResultFileId = result.getString("fileId");
							String docTitle = result.getString("docTitle");
							String[] ocrResultFileIds = ocrResultFileId.split("_");
							if(ocrResultFileIds.length > 1) {
								
								//材料文件Id和材料Id
								String fileId = ocrResultFileIds[0];
								String oldMaterialId = ocrResultFileIds[1];
								
								//材料关联的任务信息
								ArchiveJobDO job = materialJobMap.get(oldMaterialId);
								
								//材料信息
								MaterialInfoDO oldMaterialInfo = materialMap.get(oldMaterialId);
								
								//任务中的材料文件列表
								JSONArray jobFileArray = JSONArray.parseArray(job.getFileContent());
								
								//删除任务中的材料文件信息
								JSONObject fileJson = OcrUtil.popJobFileJson(jobFileArray, fileId);
								
								//识别到标题
								if(StringUtil.isNotEmpty(docTitle)) {
									
									//在待添加材料中获取材料信息
									MaterialInfoDO materialInfo = needAddMaterials.get(oldMaterialId);
									
									//待添加材料中不包含当前材料(第一次添加的材料)
									if(materialInfo == null) {
										materialInfo = new MaterialInfoDO();
										
										//新材料Id
										String newMaterialId = StringUtil.getGuid32();
										
										//获取分类Id
										String catalogId = OcrUtil.getCatalogByName(ajChildCatalog, docTitle, familiarity, firstJob.getJgrybm(),
												newMaterialId, dossierCatalogOcrMarkService);
										materialInfo.setId(newMaterialId);
										materialInfo.setJgrybm(firstJob.getJgrybm());
										materialInfo.setCatalogId(catalogId);
										materialInfo.setName(docTitle);
										materialInfo.setPageCount(1);
										materialInfo.setMaterialId(oldMaterialInfo.getMaterialId());
										
										//识别成功
										if(StringUtil.isNotEmpty(catalogId)) {
											materialInfo.setType(DamConstants.FILE_TYPE_BM);
										}
										else {
											materialInfo.setType(DamConstants.FILE_TYPE_WBM);
										}
										
										//设置材料文件
										JSONArray materialFile = new JSONArray();
										materialFile.add(fileJson);
										materialInfo.setMaterialFile(materialFile.toJSONString());
										
										//暂存到待添加的材料文件中
										needAddMaterials.put(newMaterialId, materialInfo);
									}
									
									//材料相关的文件
									else {
										//设置材料文件
										JSONArray materialFile = JSONArray.parseArray(materialInfo.getMaterialFile());
										materialFile.add(fileJson);
										materialInfo.setMaterialFile(materialFile.toJSONString());
										materialInfo.setPageCount(materialFile.size());
										
										//暂存到待添加的材料文件中
										needAddMaterials.put(materialInfo.getId(), materialInfo);
									}
								}
								
								//未识别到标题
								else {
									//获取案件关联的材料(未识别到的材料文件共用一个Key:监管人员编码）
									MaterialInfoDO noTileMaterialInfo = needAddMaterials.get(firstJob.getJgrybm());
									
									//第一次添加案件关联的材料(材料无法识别)
									if(noTileMaterialInfo == null) {
										noTileMaterialInfo = new MaterialInfoDO();																
										noTileMaterialInfo.setId(StringUtil.getGuid32());
										noTileMaterialInfo.setJgrybm(firstJob.getJgrybm());
										noTileMaterialInfo.setName("材料无法识别");
										noTileMaterialInfo.setPageCount(1);
										noTileMaterialInfo.setMaterialId(oldMaterialInfo.getMaterialId());
										noTileMaterialInfo.setType(DamConstants.FILE_TYPE_WBM);					                    
					                    
					                    //设置材料文件
										JSONArray materialFile = new JSONArray();
										materialFile.add(fileJson);
										noTileMaterialInfo.setMaterialFile(materialFile.toJSONString());
										
										//暂存到待添加的材料文件中
										needAddMaterials.put(firstJob.getJgrybm(), noTileMaterialInfo);
									}
									else {
										//设置材料文件
										JSONArray materialFile = JSONArray.parseArray(noTileMaterialInfo.getMaterialFile());
										materialFile.add(fileJson);
										noTileMaterialInfo.setMaterialFile(materialFile.toJSONString());
										noTileMaterialInfo.setPageCount(materialFile.size());
										
										//暂存到待添加的材料文件中
										needAddMaterials.put(firstJob.getJgrybm(), noTileMaterialInfo);
									}
								}
								
								//更新材料
								if(oldMaterialInfo != null) {								
									String newFileId = GoUtil.deleteJsonArrayStrByProp(oldMaterialInfo.getMaterialFile(), "id", fileId);
									oldMaterialInfo.setMaterialFile(newFileId);
									if(JSONArray.parseArray(newFileId).isEmpty()) {
										needDeleteMaterials.add(oldMaterialInfo.getId());
										needUpdateMaterials.remove(oldMaterialInfo.getId());
									}
									else {
										needUpdateMaterials.put(oldMaterialInfo.getId(), oldMaterialInfo);
									}
								}
								
								//更新任务(从任务材料信息中删除当前文件)
								if(job != null) {
									String newFileId = GoUtil.deleteJsonArrayStrByProp(job.getFileContent(), "id", fileId);
									job.setFileContent(newFileId);
									if(JSONArray.parseArray(newFileId).isEmpty()) {
										job.setState(DamConstants.JOB_STATE_YES);
										job.setEndTime(new Date());
										String ocrKey = String.format(RedisConstants.START_OCR_MATER_ID, job.getMaterialId());
										redisTemplateUtil.deleteKey(ocrKey);
									}
									needUpdateJobs.put(job.getId(), job);
								}
							}
						}
					}
					
					//处理失败数据(返回code-406)
					if(!serDatas.getFailed_ids().isEmpty()) {
						for(int i = 0; i < serDatas.getFailed_ids().size(); i ++) {
							String fileId = serDatas.getFailed_ids().getString(i);
							String[] fileIdArr = fileId.split("_");
							if(fileIdArr.length > 1) {
								
								//材料文件Id和材料Id
								String materialFileId = fileIdArr[0];
								String oldMaterialId = fileIdArr[1];
								
								//材料关联的任务信息
								ArchiveJobDO job = materialJobMap.get(oldMaterialId);
								
								//更新任务(从任务材料信息中删除当前文件)
								if(job != null) {
									String newFileId = GoUtil.deleteJsonArrayStrByProp(job.getFileContent(), "id", materialFileId);
									job.setFileContent(newFileId);
									needUpdateJobs.put(job.getId(), job);
								}
							}
						}
					}
					
					//添加材料
					if(!needAddMaterials.isEmpty()) {
						List<MaterialInfoDO> needAddMaterialsList = needAddMaterials.values().stream().collect(Collectors.toList());
						dossierMaterialInfoService.saveBatch(needAddMaterialsList, 100);
					}				
					
					//更新材料
					if(!needUpdateMaterials.isEmpty()) {
						List<MaterialInfoDO> needUpdateMaterialsList = needUpdateMaterials.values().stream().collect(Collectors.toList());
						dossierMaterialInfoService.updateBatchById(needUpdateMaterialsList);
					}
					
					//删除材料
					if(!needDeleteMaterials.isEmpty()) {		
						dossierMaterialInfoService.removeByIds(needDeleteMaterials);
					}
					
					//更新任务
					if(!needUpdateJobs.isEmpty()) {
						List<ArchiveJobDO> needUpdateJobsList = needUpdateJobs.values().stream().collect(Collectors.toList());
						archiveJobService.updateBatchById(needUpdateJobsList, 100);
					}
					
					//处理进度
					if(serDatas.getProgress() != 0) {
						handleProcess(firstJob.getJgrybm(), serDatas.getBatchId(), serDatas.getProgress());
					}
				}				
			}
			
			//更新isp调用对象
			ispInvoking.setOutput("智能编目回调成功");
		}
		catch (Exception e) {
			ispInvoking.setOutput(e.getMessage());
			ispInvoking.setRequestStatus("2");
			log.error("ocr处理-智能编目回调异常：{}", e);
			throw e;
		}
		finally {
			
			//插入isp调用对象
			ispInvoking.setRequestTime(System.currentTimeMillis() - begin);
			ispInvoking.setAddTime(new Date());
			ispInvokingService.save(ispInvoking);
		}
	}
	
	/**
	 * 处理编目进度
	 * @param jgrybm String 监管人员编码
	 * @param batchId String 批次Id
	 * @param progress float 当前批次返回的进度
	 * <AUTHOR>
	 * @date 2024年5月16日
	 */
	public void handleProcess(String jgrybm, String batchId, float progress) {
		
		//案件材料文件总数量
        String finishMark = String.format(RedisConstants.TASK_JOB_FINISH_NUM, jgrybm);
        int totalNum = Integer.valueOf(redisTemplateUtil.get(finishMark));
		
        //缓存ocr请求识别成功后的进度信息
        String requestBatchProgressKey = String.format(OcrConstants.REDIS_REQUEST_OCR_BATCH_PROGRESS, jgrybm);
        redisTemplateUtil.hSet(requestBatchProgressKey, batchId, String.valueOf(progress));
        
        //生成Websocket消息
        log.info("ocr处理-智能编目发送Websocket消息");
		JSONObject sendData = new JSONObject();
		sendData.put("code", WebSocketCode.RESULT_BM.getCode());
        sendData.put("msg", WebSocketCode.RESULT_BM.getMsg());
        sendData.put("jgrybm", jgrybm);
        sendData.put("showDetail", true);
        sendData.put("total", totalNum);
        
        //获取ocr识别进度
		OcrRateProgress ocrRateProgress = goOcrStrategy.getBmRateProgress(jgrybm);					
		if(ocrRateProgress != null) {
			sendData.put("remainder", ocrRateProgress.getRemainingNum());
            sendData.put("processedNum", ocrRateProgress.getProcessedNum());
		}
		else {
			sendData.put("remainder", 0);
            sendData.put("processedNum", 0);
		}
		
		//发送Websocket消息
		WebSocketServer.sendInfo(null, sendData.toJSONString());
        log.info("send webSocket...sendData:{}", sendData.toJSONString());
	}
}
