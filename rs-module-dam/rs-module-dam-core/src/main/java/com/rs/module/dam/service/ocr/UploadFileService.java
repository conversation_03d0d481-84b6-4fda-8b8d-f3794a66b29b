package com.rs.module.dam.service.ocr;

import com.bsp.common.util.R;
import org.springframework.web.multipart.MultipartFile;

/**
 * 上传文件服务接口类
 * <AUTHOR>
 * @date 2025年4月26日
 */
public interface UploadFileService {
	
	/**
	 * 导出excel
	 * @param file MultipartFile 要导出的文件
	 * @return boolean
	 */
	boolean importExcel(MultipartFile file);

	/**
	 * 旋转图片
	 * @param url String 图片地址
	 * @param angel Integer 旋转角度
	 * @return R
	 */
	R rotate(String url , Integer angel);

	/**
	 * 上传文件
	 * @param uploadType String 上传类型
	 * @param fileName String 文件名称
	 * @param bytes byte[] 数据内容
	 * @param xh String 序号
	 * @return R
	 */
	R uploadFile(String uploadType, String fileName, byte[] bytes, String xh);
}
