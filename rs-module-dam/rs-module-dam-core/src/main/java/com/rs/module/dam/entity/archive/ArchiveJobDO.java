package com.rs.module.dam.entity.archive;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 卷宗归档任务 DO
 *
 * <AUTHOR>
 */
@TableName("dam_archive_job")
@KeySequence("dam_archive_job_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ArchiveJobDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 封面内容
     */
    private String coverContent;
    /**
     * 材料ID
     */
    private String materialId;
    /**
     * 文件内容
     */
    private String fileContent;
    /**
     * 任务状态（0：未完成，1：已完成，2：异常）
     */
    private String state;
    /**
     * 分卷ID
     */
    private String partId;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 任务类型（0：编目组卷PDF，1：未编目组卷PDF，2：材料编目）
     */
    private String type;
    /**
     * 请求次数
     */
    private Integer requestCount;
    /**
     * 批次Id
     */
    private String batchId;

}
