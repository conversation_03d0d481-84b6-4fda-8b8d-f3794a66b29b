package com.rs.module.dam.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * 卷宗目录工具类
 * <AUTHOR>
 * @date 2025年4月25日
 */
public class CatalogUtil {

	/**
	 * 判断是否文档节点
	 * @param id String 节点Id
	 * @param zjCataArr JSONArray 组卷目录
	 * @return boolean
	 */
	public static boolean isDocment(String id, JSONArray zjCataArr) {    	
    	for(int i=0;i<zjCataArr.size();i++) {
    		JSONObject item = zjCataArr.getJSONObject(i);
    		if(id == item.getString("parentId")) {    			
    			return false;
    		}
    	}
    	
		return true;
	}
}
