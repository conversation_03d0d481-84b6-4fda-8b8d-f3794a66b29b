package com.rs.module.dam.entity.material;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * pdf转换图片 DO
 *
 * <AUTHOR>
 */
@TableName("dam_pdf_image")
@KeySequence("dam_pdf_image_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PdfImageDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 关联dam_upload_pdf_data的主键
     */
    private String pdfId;
    /**
     * 图片名称
     */
    private String imageName;
    /**
     * 图片地址
     */
    private String imageUrl;
    /**
     * 图片上传数据
     */
    private String imageData;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 页数序号
     */
    private Integer xh;
    /**
     * 状态(1:图片转换成功, 0:图片转换失败)
     */
    private Integer status;
    /**
     * 图片转换失败的原因
     */
    private String errorMsg;

}
