package com.rs.module.dam.util;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.imageio.ImageIO;

import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.core.io.InputStreamResource;
import org.springframework.util.ClassUtils;
import org.springframework.util.ResourceUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.ServiceLocator;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.io.image.ImageData;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.events.PdfDocumentEvent;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Div;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.property.AreaBreakType;
import com.itextpdf.layout.property.HorizontalAlignment;
import com.itextpdf.layout.property.TextAlignment;
import com.itextpdf.layout.property.UnitValue;
import com.itextpdf.layout.property.VerticalAlignment;
import com.rs.module.dam.constant.DamConstants;
import com.rs.module.dam.entity.archive.ArchiveCoverDO;
import com.rs.module.dam.entity.material.MaterialInfoDO;

import lombok.extern.slf4j.Slf4j;

/**
 * pdf生成工具类
 * <AUTHOR>
 * @date 2025年4月22日
 */
@Slf4j
public class PdfGenerateUtil {
	
	/**
	 * 异步生成分卷pdf
	 * @param index int 索引
	 * @param fjsl int 附卷数量
	 * @param archiveCover ArchiveCoverDO 卷宗封面
	 * @param materialList List<MaterialInfoDO> 分卷材料
	 * @param isAddWatermarking boolean 是否添加水印
	 * @param zjType String 组卷类型
	 * @param user SessionUser 当前用户
	 * @param callback CallBackFunction 回调函数
	 */
	public static void executeAsyncForPartPdf(int index, int fjsl, ArchiveCoverDO archiveCover,
			List<MaterialInfoDO> materialList, boolean isAddWatermarking, String zjType,
			SessionUser user, CallBackFunction callback) {
		ThreadPoolUtil.getPool().execute(new Runnable() {
			
			@Override
			public void run() {
				try {
					String fileName = generatePdf(index, fjsl, archiveCover, materialList, isAddWatermarking, zjType, user);
	       			String path = ObjectUtil.formatFilePath(PdfGenerateUtil.generateTempPath() + fileName + ".pdf");
	       			File file  = new File(path);
	       			
                    try {
                    	JSONObject json = upload(file, fileName);
                    	json.put("pdfImageUrl", pdfToImage(file, archiveCover.getPartCatalogId()));                    	
    	       			callback.execute(json);
                    }
                    catch(Exception e) {
                    	log.error("【pdf生成】发生异常，原因：", e);
                    	callback.execute(new JSONObject());
                    }
                    finally {
                    	file.delete();
                    }
				}
				catch(Exception e) {
					log.error("【pdf生成】发生异常，原因：", e);
				}
			}
		});
	}
	
	/**
	 * 生成pdf
	 * @param index int 索引
	 * @param fjsl int
	 * @param archiveCover ArchiveCoverDO 卷宗封面
	 * @param materialList List<MaterialInfoDO> 分卷材料
	 * @param isAddWatermarking boolean 是否添加水印
	 * @param zjType String 组卷类型
	 * @param user SessionUser 当前用户
	 * @param callback CallBackFunction 回调函数
	 * @return String
	 */
	public static String generatePdf(int index, int fjsl, ArchiveCoverDO archiveCover,
			List<MaterialInfoDO> materialList, boolean isAddWatermarking, String zjType, SessionUser user) {
		PdfWriter writer = null;
		Document document = null;
		String fileName = StringUtil.getGuid32();
		String path = ObjectUtil.formatFilePath(PdfGenerateUtil.generateTempPath() + fileName + ".pdf");
		File file = new File(path);
		FileOutputStream fos = null;
		
		try {
			fos = new FileOutputStream(file);
			writer = new PdfWriter(fos);
			PdfDocument pdf = new PdfDocument(writer);
			document = new Document(pdf, PageSize.A4);
			
			//添加水印
			if (isAddWatermarking) {
				pdf.addEventHandler(PdfDocumentEvent.END_PAGE, new WatermarkingEventHandler(user));
			}
			
			//设置页码
			TextFooterEventHandler eh = new TextFooterEventHandler(document);
			eh.setName(archiveCover.getJm());
			pdf.addEventHandler(PdfDocumentEvent.END_PAGE, eh);

			//编目组卷
			if (DamConstants.ZJ_TYPE_BMZJ.equals(zjType)) {
				
				//添加封面
				addCover(document, archiveCover);
				
				// 目录
				addCatalogDocument(pdf, document, materialList);
				
				//新页
				pdf.addNewPage(PageSize.A4);
				eh.setStartPage(pdf.getNumberOfPages());
				
				//保存封面和目录图片
				pdf.getPage(1);
			}
			
			for (int i = 0; i < materialList.size(); i ++) {
				MaterialInfoDO m = materialList.get(i);
				String fileStr = m.getMaterialFile();
				JSONArray filesArr = JSONArray.parseArray(fileStr);
				for (int a = 0; a < filesArr.size(); a++) {
					JSONObject obj = filesArr.getJSONObject(a);
					log.info("【pdf生成】pdf当前页数："+pdf.getNumberOfPages());
					if(pdf.getNumberOfPages() >= 1){
						document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					}
					
					try {
						byte[] urls2 = PicUtil.compressPic(obj.getString("url"));
						boolean useFile = useCompressFile(urls2);

						if (useFile){
							ImageData img = ImageDataFactory.create(urls2);
							Image url = new Image(img).setAutoScale(true);
							document.add(url);
						}
						else {
							//图片压缩失败或不需要进行压缩,使用原图
							document.add(
									new Image(ImageDataFactory.create(new String(obj.getString("url").getBytes("utf-8"))))
											.setAutoScale(true));
						}
					}
					catch (Exception e) {
						log.error("【pdf生成】发生异常：{}", e.getMessage());
						document.add(new Paragraph("该图片文件不能加载"));
					}
				}
			}
		}
		catch (Exception e) {
			log.error("发生异常，原因：", e);
			file.delete();
		}
		finally {
			if (document != null) {
				try {
					document.close();
				}
				catch (Exception e) {
					log.error("【pdf生成】发生异常，原因：", e);
				}
			}
			if(fos != null){
				try {
					fos.close();
				}
				catch (IOException e) {
					log.error("【pdf生成】发生异常，原因：", e);
				}
			}
		}
		
		return fileName;
	}
	
	/**
	 * 获取文件的真实路径
	 * @param dir String 文件目录
	 * @param filename String 文件名
	 * @return String
	 * @throws Exception
	 */
	public static String getRealPath(String dir, String filename) throws Exception {
		String realPath = "";
		String osName = System.getProperties().getProperty("os.name");
		if("Linux".equals(osName)) {
			realPath = System.getProperty("user.dir") + File.separator + "font" + File.separator + filename;
		}
		else {
			String classpath = ClassUtils.getDefaultClassLoader().getResource("").getPath();
			if (classpath.indexOf(".jar!") > -1) {
				realPath = System.getProperty("user.dir") + File.separator + dir + File.separator + filename;
			}
			else {
				realPath = ResourceUtils.getFile("classpath:" + dir + File.separator + filename).getPath();
			}
		}
		
		return realPath;
	}
	
	/**
	 * 添加封面
	 * @param document Document pdf文档
	 * @param archiveCover ArchiveCoverDO 封面对象
	 * @return String
	 * @throws Exception
	 */
	public static String addCover(Document document, ArchiveCoverDO archiveCover) throws Exception {
		
		//设置封面字体
		PdfFontFactory.register(getRealPath("font", "simfang.ttf"), "simfang");
		PdfFont font = PdfFontFactory.createRegisteredFont("simfang", PdfEncodings.IDENTITY_H, true);
		document.setFont(font).setFontSize(16);
		
		//卷宗名称
		String fileName = archiveCover.getJgrybm() + "-" + archiveCover.getJm() + "-" + archiveCover.getJzmc();

		//封面背景
		Image bg = new Image(ImageDataFactory.create(getRealPath("images", "jz_bg.png")))
				.scaleToFit(PageSize.A4.getWidth(), PageSize.A4.getHeight()).setFixedPosition(0, 0);
		document.add(bg);
		
		//卷宗名称
		if(StringUtil.isNotEmpty(archiveCover.getJzmc())) {
			document.add(new Paragraph(archiveCover.getJzmc()).setFont(font).setFontSize(36).setBold()
				.setTextAlignment(TextAlignment.CENTER));
		}
		
		//卷名
		if(StringUtil.isNotEmpty(archiveCover.getJm())) {
			document.add(new Paragraph(archiveCover.getJm()).setFont(font).setFontSize(26).setBold()
				.setTextAlignment(TextAlignment.CENTER));
		}
		
		//初始化表格
		UnitValue[] unitValues = new UnitValue[] { UnitValue.createPercentValue(50), UnitValue.createPercentValue(50) };
		Table table = new Table(unitValues).setBold();
		
		//添加表格内容
		addCoverCell(table, "人员姓名", archiveCover.getXm());
		addCoverCell(table, "监室号", archiveCover.getRoomName());
		addCoverCell(table, "涉嫌罪名", archiveCover.getSxzm());
		addCoverCell(table, "监管人员编码", archiveCover.getJgrybm());
		addCoverCell(table, "立卷人", archiveCover.getZjUserName());
		addCoverCell(table, "立卷单位", archiveCover.getZjOrgName());
		addCoverCell(table, "立卷时间", archiveCover.getZjsj());
		document.add(table);
		
		//二维码处理
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		String encoderContent = archiveCover.getJgrybm() + ":dam:gosuncn";
		QrCodeUtil.encoderQrCode(encoderContent, outputStream, "png", 67, 67, 3);
		
		document.add(new Div().setFixedPosition(50, 50, 67)
				.add(new Image(ImageDataFactory.create(outputStream.toByteArray()))));
		document.add(new AreaBreak());
		
		return fileName;
	}
	
	/**
	 * 添加封面单元格
	 * @param table Table 表格
	 * @param fieldName String 字段名称
	 * @param fieldValue String 字段值
	 */
	public static void addCoverCell(Table table, String fieldName, String fieldValue) {
		fieldValue = StringUtil.clearNull(fieldValue);
		table.addCell(new Cell().add(new Paragraph(fieldName + "：")).setBorder(Border.NO_BORDER))
			.addCell(new Cell().add(new Paragraph(fieldValue)).setBorder(Border.NO_BORDER));
	}
	
	/**
	 * 添加目录
	 * @param pdf PdfDocument pdf文档
	 * @param document Document 文档
	 * @param materialList List<MaterialInfoDO> 材料集合
	 * @throws Exception
	 */
	public static void addCatalogDocument(PdfDocument pdf, Document document, List<MaterialInfoDO> materialList) throws Exception {
		
		//设置封面字体
		PdfFontFactory.register(getRealPath("font", "simfang.ttf"), "simfang");
		PdfFont font = PdfFontFactory.createRegisteredFont("simfang", PdfEncodings.IDENTITY_H, true);

		//初始化表格
		UnitValue[] unitValues = new UnitValue[] { UnitValue.createPercentValue(8), UnitValue.createPercentValue(12),
				UnitValue.createPercentValue(12), UnitValue.createPercentValue(24), UnitValue.createPercentValue(18),
				UnitValue.createPercentValue(10), UnitValue.createPercentValue(16)};
		Table table = new Table(unitValues).useAllAvailableWidth();
		
		//处理表格内容
		processCatalogTable(pdf, table, materialList, font, Arrays.asList("序号","责任人","文号","文书名称","日期", "页码", "备注"));

		//添加标题
		document.add(new Paragraph("卷内文书目录").setFont(font).setFontSize(26).setBold().setTextAlignment(TextAlignment.CENTER));
		
		//添加表格
		document.add(table);
	}
	
	/**
	 * 添加卷内备考表
	 * @param document Document pdf文档
	 * @throws Exception
	 */
	public static void addJnbkbAndFd(Document document) throws Exception {
		document.getPdfDocument().addNewPage();
		document.add(new AreaBreak());
		
		// 第二页
		Div div = new Div().setFixedPosition(300, 100, 200);
		Table end = new Table(new UnitValue[] { UnitValue.createPercentValue(50), UnitValue.createPercentValue(50) })
				.addCell(new Cell().add(new Paragraph("立卷人：")).setBorder(Border.NO_BORDER))
				.addCell(new Cell().setBorder(Border.NO_BORDER).setBorderBottom(new SolidBorder(Border.SOLID)))
				.addCell(new Cell().add(new Paragraph("检查人：")).setBorder(Border.NO_BORDER))
				.addCell(new Cell().setBorder(Border.NO_BORDER).setBorderBottom(new SolidBorder(Border.SOLID)))
				.addCell(new Cell().add(new Paragraph("立卷时间：")).setBorder(Border.NO_BORDER))
				.addCell(new Cell().setBorder(Border.NO_BORDER).setBorderBottom(new SolidBorder(Border.SOLID)));
		document.add(new Div().setFillAvailableArea(true).setBorder(new SolidBorder(1)).add(new Paragraph("本案卷情况说明"))
				.add(new Paragraph("卷\t内\t备\t考\t表").setTextAlignment(TextAlignment.CENTER)).add(div.add(end)));

		document.add(new AreaBreak());
		Image bg = new Image(ImageDataFactory.create(getRealPath("images", "jz_bg.png")))
				.scaleToFit(PageSize.A4.getWidth(), PageSize.A4.getHeight()).setFixedPosition(0, 0);
		document.add(bg);

	}
	
	/**
	 * 生成目录表格
	 * @param pdf PdfDocument pdf文档 
	 * @param table Table 表格
	 * @param materialList List<MaterialInfoDO> 材料集合
	 * @param font PdfFont pdf字段
	 * @param titles List<String> 标题集合
	 */
	private static void processCatalogTable(PdfDocument pdf, Table table, List<MaterialInfoDO> materialList,
			PdfFont font, List<String> titles) {
		int fontSize = 16;
		int pageFontSize = 16;
		
		// 居中
		table.setTextAlignment(TextAlignment.CENTER).setFont(font).setFontSize(fontSize);
		
		// 列
		for (String title : titles) {
			table.addCell(title);
		}

		//起始页和高度
		int startPage = 0;
		int height = 0;
		
		//循环处理每个材料添加单元格
		for (int i = 0; i < materialList.size(); i++) {
			MaterialInfoDO material = materialList.get(i);
			int pageCount = material.getPageCount();
			int endCount = startPage + pageCount;

			String pageTxt = "";
			if (pageCount < 2) {
				pageTxt = endCount + "";
			} else {
				pageTxt = (startPage + 1) + "-" + endCount;
			}
			if(endCount >= 100){
				pageFontSize = 12;
			}
			startPage = endCount;

			// 16 * 25 = x * length 25字16号一行
			String mName = material.getName().replaceAll("\\r|\\n", "");

			table.addCell(getCatalogTableCell((i + 1) + ""));
			table.addCell(getCatalogTableCell(StringUtil.isNotEmpty(material.getResponsiblePerson())?material.getResponsiblePerson():""));
			table.addCell(getCatalogTableCell(StringUtil.isNotEmpty(material.getSymbol())?material.getSymbol():""));
			table.addCell(getCatalogTableCell(mName));
			String date = "";
			if(material.getDate() != null){
				date = new SimpleDateFormat("yyyy-MM-dd").format(material.getDate());
			}
			table.addCell(getCatalogTableCell(date));
			table.addCell(getCatalogTableCell(pageTxt).setFontSize(pageFontSize));
			table.addCell(getCatalogTableCell(StringUtil.isNotEmpty(material.getRemark()) ? material.getRemark() : ""));
			height += 1 + (mName.length()/12);
			if (height >= 24){
				pdf.addNewPage(PageSize.A4);
				height = 0;
			}
		}

		table.addCell(getCatalogTableCell((materialList.size() + 1) + ""));
		table.addCell(getCatalogTableCell(""));
		table.addCell(getCatalogTableCell(""));
		table.addCell(getCatalogTableCell("备考表"));
		table.addCell(getCatalogTableCell(""));
		table.addCell(getCatalogTableCell(startPage + 1 + "").setFontSize(pageFontSize));
		table.addCell(getCatalogTableCell(""));

		height += 1;
		if (height >= 24){
			height = 0;
		}

		int count = 6;

        for (int i = 0; i < count; i++) {
            table.addCell("\r").addCell("").addCell("").addCell("").addCell("").addCell("").addCell("");
		}
	}
	
	/**
	 * 生成目录表格单元格
	 * @param content String 内容
	 * @return Cell
	 */
	public static Cell getCatalogTableCell(String content){
		if (StringUtil.isEmpty(content)){
			content = "";
		}
		Cell cell = new Cell().add(new Paragraph(content));
		cell.setHorizontalAlignment(HorizontalAlignment.CENTER);
		cell.setVerticalAlignment(VerticalAlignment.MIDDLE);
		cell.setKeepTogether(true);
		
		return cell;
	}
	
	/**
	 * pdf生成图片
	 * @param f File pdf文件
	 * @param partCatalogId String 分卷Id
	 * @return List<String>
	 */
	public static List<String> pdfToImage(File f, String partCatalogId){
		List<String> list = new ArrayList<>();
		
		try {
			PDDocument doc = PDDocument.load(f);
			PDFRenderer renderer = new PDFRenderer(doc);
			int pageCount = doc.getNumberOfPages();
			for(int i = 0; i < pageCount; i++){
                BufferedImage image = renderer.renderImageWithDPI(i, 100);
				String path = ObjectUtil.formatFilePath(generateTempPath()  + partCatalogId + "_" 
						+ System.currentTimeMillis() + "_" + i + ".png");
				log.info("【pdf生成】pdf转图片(共{}张), 第:{}张图片的路径为:{}", pageCount, i + 1, path);
				File imagefile = new File(path);
				ImageIO.write(image, "PNG", imagefile);
				JSONObject upload = upload(imagefile, null);
				list.add(upload.getString("url"));
				imagefile.delete();
			}
		}
		catch (Exception e) {
			log.error("【pdf生成】发生异常，原因：", e);
		}
		
		return list;
	}
	
	/**
	 * pdf生成图片并转换成文件数据
	 * @param pdfUrl String pdf文件地址
	 * @return JSONArray
	 */
	public static JSONArray getFileDataFromPdf(String pdfUrl){
		JSONArray jsonArray = new JSONArray();
		InputStream is = null;
		PDDocument doc = null;
		
		try {
			pdfUrl = pdfUrl.replace(" ", "%20");
			URL url = new URL(pdfUrl);
			is = url.openStream();
			
			//临时存放磁盘，减少内存使用
			doc = PDDocument.load(is, MemoryUsageSetting.setupTempFileOnly());
			
			//软引用是在内存即将溢出才会回收，所以也会生命周期会一直占用内存
			doc.setResourceCache(new PdfCustomDefaultResourceCache());
			
			//主键
			String pdfId = StringUtil.getGuid32();

			PDFRenderer renderer = new PDFRenderer(doc);
			int pageCount = doc.getNumberOfPages();
			for(int i = 0; i < pageCount; i++){
                BufferedImage image = renderer.renderImageWithDPI(i, 100);               
                
                //写入字节输出流
				ByteArrayOutputStream baos = new ByteArrayOutputStream();
				ImageIO.write(image, "JPG", baos);
				
				 //文件Id
                String fileId = StringUtil.getGuid32();
				
				//上传图片
				String name = pdfId + "_" + fileId + "_" + i + ".jpg";
				JSONObject fileInfo = upload(baos.toByteArray(), name);
				
				//格式化数据
				JSONObject fileData = new JSONObject();
				fileData.put("fileId", fileId);
				fileData.put("fileUrl", fileInfo.getString("url"));
				
				jsonArray.add(fileData);
			}
		}
		catch (Exception e) {
			log.error("【pdf生成图片】发生异常，原因：", e);
		}
		finally {
			if(null != doc) {
				try {
					doc.close();
				}
				catch(Exception e) {}				
			}
			if(null != is) {
				try {
					is.close();
				}
				catch(Exception e) {}
			}
		}
		
		return jsonArray;
	}
	
	/**
	 * 生成临时文件路径
	 * @return String
	 */
	public static String generateTempPath(){
		String path = System.getProperty("user.dir") + File.separator + "temp" + File.separator;
		File dir = new File(path);
		if (dir.exists()) {
			log.info("创建目录" + path + "失败，目标目录已经存在");
		}
		else {
			if (dir.mkdirs()) {
				log.info("【pdf生成】创建目录" + path + "成功！");
			}
			else {
				log.info("【pdf生成】创建目录" + path + "失败！");
				return System.getProperty("user.dir") + File.separator;
			}
		}
		
		return path;
	}
	
	/**
	 * 上传文件
	 * @param fileUrl String 文件地址
	 * @return JSONObject
	 */
	public static JSONObject upload(String fileUrl){
		JSONObject result = new JSONObject();
		
		try {
			//文件名称
			String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
			
			URL url = new URL(fileUrl);
			URLConnection urlConnection = url.openConnection();
			InputStreamResource isr = new InputStreamResource(urlConnection.getInputStream(), fileName);
			isr.getInputStream();
			result = upload(urlConnection.getInputStream(), fileName);
		}
		catch(Exception e) {
			log.error("【文件上传】上传失败，文件路径{}，失败原因{}", fileUrl, e.getMessage());
		}
		
		return result;
	}
	
	/**
	 * 上传文件
	 * @param file File 待上传的文件
	 * @param fileName String 文件名称
	 * @return JSONObject
	 */
	public static JSONObject upload(File file, String fileName){
		FileStorageService fileStorageService = ServiceLocator.getBean(FileStorageService.class);
		if(StringUtil.isNotEmpty(fileName)) {
			return (JSONObject)JSON.toJSON(fileStorageService.of(file).setSaveFilename(fileName).upload());
		}
		else {
			return (JSONObject)JSON.toJSON(fileStorageService.of(file).upload());
		}
	}
	
	/**
	 * 上传文件
	 * @param is InputStream 待上传的流数据
	 * @param fileName String 文件名称
	 * @return JSONObject
	 */
	public static JSONObject upload(InputStream is, String fileName){
		FileStorageService fileStorageService = ServiceLocator.getBean(FileStorageService.class);
		if(StringUtil.isNotEmpty(fileName)) {
			return (JSONObject)JSON.toJSON(fileStorageService.of(is).setSaveFilename(fileName).upload());
		}
		else {
			return (JSONObject)JSON.toJSON(fileStorageService.of(is).upload());
		}
	}
	
	/**
	 * 上传文件
	 * @param data byte[] 待上传的数据
	 * @param fileName String 文件名称 
	 * @return JSONObject
	 */
	public static JSONObject upload(byte[] data, String fileName){
		FileStorageService fileStorageService = ServiceLocator.getBean(FileStorageService.class);
		if(StringUtil.isNotEmpty(fileName)) {
			return (JSONObject)JSON.toJSON(fileStorageService.of(data).setSaveFilename(fileName).upload());
		}
		else {
			return (JSONObject)JSON.toJSON(fileStorageService.of(data).upload());
		}
	}

	/**
	 * 移除卷宗数据中的目录信息
	 * @param array JSONArray 卷宗目录数组
	 * @param partCatalogIdList List<String> 分卷Id集合
	 */
	public static void removeCatalog(JSONArray array, List<String> partCatalogIdList){
        if (array == null) {
            return;
        }
        
        List<String> list = new ArrayList<>();
        while (true) {
            int size = array.size();
            for (int i = 0; i < array.size(); i++) {
                JSONObject object = array.getJSONObject(i);
                String id = object.getString("id");
                String parentId = object.getString("parentId");
                if (partCatalogIdList.contains(id) || list.contains(id)){
                    array.remove(i);
                    i--;
                }
                else if (partCatalogIdList.contains(parentId) || list.contains(parentId)){
                    array.remove(i);
                    list.add(id);
                    i--;
                }
            }
            if (size == array.size()){
                return;
            }
        }
    }
	
	/**
	 * 获取目录树的Id
	 * @param treeData Map<String, Object> 目录树
	 * @param list List<String> 接收集合对象
	 */
	@SuppressWarnings("unchecked")
	public static void getLeftTreeId(Map<String, Object> treeData, List<String> list) {
        list.add((String)treeData.get("id"));
        boolean isParent = (boolean)treeData.get("isParent");
        if (isParent){
            List<Map<String, Object>> children = (List<Map<String, Object>>)treeData.get("children");
            if (CollectionUtil.isNotNull(children)) {
                for (Map<String, Object> map : children) {
                    getLeftTreeId(map, list);
                }
            }
        }
    }
	
	/**
	 * 是否压缩文件
	 * @param bytes byte[] 文件内容
	 * @return boolean
	 */
	private static boolean useCompressFile(byte[] bytes){
		if (bytes.length > 0){
			BigDecimal fileSize = new BigDecimal(bytes.length);
			BigDecimal kilobyte = new BigDecimal(1024);
			float returnValue = fileSize.divide(kilobyte, 2, BigDecimal.ROUND_UP).floatValue();

			//压缩前图片大于1M左右,取压缩后的图片
			//1024 * 0.75 * 0.75 * 0.8 = 462.15
			return 460f < returnValue;
		}
		else {
			return false;
		}
	}
	
	public static void main(String[] args) {
		File f = new File("***.pdf");
		pdfToImage(f, "scu");
	}
}
