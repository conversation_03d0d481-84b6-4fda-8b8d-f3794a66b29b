package com.rs.module.dam.constant;

/**
 * Redis常量类
 * <AUTHOR>
 * @date 2025年4月17日
 */
public class RedisConstants {
	
	/** 扫描上传(未编目)保存的材料 */
    public static final String MATER_INFO_JGRYBM = "materInfo:%s";
    
    /** 扫描上传(在目录上进行扫描上传)保存的材料 */
    public static final String MATER_INFO_JGRYBM_CATALOG ="jgrybm:%s:catalogId:%s";
    
    /** 材料启动Id */
    public static final String START_MATER_ID = "start:mater:%s";
    
    /** 材料扫描自增 */
    public static final String SCAN_MATERIAL_INCREASE = "scan:material:increase:%s";

	/** 格式化审查,勾选任务的检测进度 */
    public static final String FORMAT_CHECK_MINCE_JOB_PROCESS = "format:check:mince:process";

    /** pdf转换key */
    public final static String PDF_CONVERT_KEY = "pdf_convert_key";
    
    /** pdf转换进度key */
    public final static String PDF_CONVERT_RATE_KEY = "convert:rate:%s:%s";
    
    /** pdf转换完成,前端刷新pdf转换进度状态 */
    public final static String REFRESH_CONVERT_PDF_STATUS = "convert:refresh:status:%s";
    
    /**pdf转换完成,前端刷新页面,加载转换完成的图片*/
    public final static String MATERIAL_REFRESH_FLAG = "convert:refresh:%s";
    
    /** 正在转换中的pdf取消转换 */
    public final static String CANCEL_CONVERT_PDF_KEY = "convert:cancel:pdfId";
    
    /** pdf重新转换的lock */
    public final static String RE_CONVERT_LOCK = "convert:re:lock:%s";
    
    /** 未编目材料排序 */
    public final static String Material_SORT_KEY = "material:sort:%s:%s";
    
            
    /*********************【ocr任务相关】*********************/
    
    /** 开启ocr请求的lock */
    public static final String START_OCR_MATER_ID = "ocr:start:materId:%s";
    
    /** ocr任务job执行完成后标识 */
    public static final String TASK_JOB_FINISH_MARK="ocr:job:finish:jgrybm:%s";

    /** ocr任务job执行完成后数量 */
    public static final String TASK_JOB_FINISH_NUM="ocr:job:finish:num:jgrybm:%s";
    
    
    /*********************【格式化审查相关】*********************/
    
    /**格式化审查,正在进行的任务*/
    public static final String FORMAT_CHECKING = "format:checking";
    
    /**格式化审查,图片方向合规性检查开启线程的lock*/
    public static final String FORMAT_CHECK_FLAW_LOCK = "format:check:flaw:lock:%s";

    /**格式化审查,空白页检测开启线程的lock*/
    public static final String FORMAT_CHECK_BLANK_LOCK = "format:check:blank:lock:%s";

    /**设置图片方向合规性检查完成的标记*/
    public static final String FORMAT_CHECK_FLAW_FINISH_MARK = "format:check:flaw:mark:%s";

    /**设置材料空白页检测完成的标记*/
    public static final String FORMAT_CHECK_BLANK_FINISH_MARK = "format:check:blank:mark:%s";

    /**未编目页面完成格式化审查后,完成自动编目的标记*/
    public static final String FORMAT_CHECK_AUTO_CATALOG_FINISH_MARK = "format:check:catalog:mark:%s";

    /**未编目页面,正在进行自动编目的标记*/
    public static final String FORMAT_CHECK_AUTO_CATALOGING_MARK = "format:check:cataloging:%s";

    /**格式化审查,重新发起检测任务的任务类型*/
    public static final String FORMAT_CHECK_RETRY_MARK = "format:check:retry:%s:%s";

    /**重新发起检测的主任务id*/
    public static final String FORMAT_CHECK_RETRY_JOB = "format:check:retry:jobId";

    /**重新发起检测时案件的主任务*/
    public static final String FORMAT_CHECK_RETRY_MASTER_JOB = "format:check:retry:masterJob:%s:%s";

    /**格式化审查,异步发起通用ocr基础识别请求的lock*/
    public static final String FORMAT_CHECK_REQUEST_OCR = "format:check:request:ocr:%s";

    /**取消正在进行中的检测任务,取消类型 1:取消任务,检测结果保留,2:取消任务,不保留检测结果*/
    public static final String FORMAT_CHECK_CANCEL_TYPE = "format:check:cancel:type:%s";

    /**记录空白页检测接口调用失败的次数*/
    public static final String FORMAT_CHECK_BLANK_FAIL_COUNT = "format:check:blank:fail:count:%s";

    /**记录瑕疵页检测接口调用失败的次数*/
    public static final String FORMAT_CHECK_FLAW_FAIL_COUNT = "format:check:flaw:fail:count:%s";

    /**重复页发起过检测的key*/
    public static final String FORMAT_CHECK_BATCH_REPEAT_REQUEST = "format:check:repeat:request:batch:%s";

    /**点击重新检测的子任务id*/
    public static final String FORMAT_CHECK_RECHECK_MINCE = "format:check:recheck:%s:%s";

    /**某个案件的检测进度*/
    public static final String FORMAT_CHECK_PROCESS_AJ = "format:check:search:process:%s:%s";
}
