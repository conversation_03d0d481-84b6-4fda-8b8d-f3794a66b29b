package com.rs.module.dam.vo.ocr;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 格式化审查参数
 * <AUTHOR>
 * @date 2025年4月26日
 */
@Data
public class CheckFormatParam {

    @NotEmpty(message = "jgrybm不能为空")
    private String jgrybm;

    @NotEmpty(message = "checkType不能为空")
    @ApiModelProperty(name = "检测类型: 0:未编目页面发起检测, 1:已编目页面发起检测, 2:智能阅卷页面发起检测")
    private String checkType;

    @NotEmpty(message = "seal不能为空")
    @ApiModelProperty(name = "是否发起签章检测 0:否, 1:是")
    private String seal;

    @NotEmpty(message = "signature不能为空")
    @ApiModelProperty(name = "是否发起签名检测 0:否, 1:是")
    private String signature;

    @NotEmpty(message = "fingerprint不能为空")
    @ApiModelProperty(name = "是否发起捺印检测 0:否, 1:是")
    private String fingerprint;

    @NotEmpty(message = "infoExtract不能为空")
    @ApiModelProperty(name = "是否发起文书规范性检测 0:否, 1:是")
    private String infoExtract;

    @NotEmpty(message = "blankSpace不能为空")
    @ApiModelProperty(name = "是否发起空白页检测 0:否, 1:是")
    private String blankSpace;

    @NotEmpty(message = "repeatPage不能为空")
    @ApiModelProperty(name = "是否发起重复页检测 0:否, 1:是")
    private String repeatPage;

    @NotEmpty(message = "flaw不能为空")
    @ApiModelProperty("是否开启瑕疵页检测 0:否, 1:是")
    private String flaw;

    /**任务id*/
    private String jobId;

    /**需要被检测的材料的id*/
    private String fileIds;

    /**全部的检测材料*/
    private String fileData;
}
