package com.rs.module.dam.strategy.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rs.module.dam.constant.DamConstants;
import com.rs.module.dam.entity.material.MaterialImageDO;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.entity.material.MaterialRecycleBinDO;
import com.rs.module.dam.service.material.MaterialImageService;
import com.rs.module.dam.service.material.MaterialRecycleBinService;
import com.rs.module.dam.strategy.OperateTypeService;

/**
 * 图片切割操作
 * <AUTHOR>
 * @date 2025年4月18日
 */
@Service(value = "damSplitImageOperate")
public class DamSplitImageOperate implements OperateTypeService {
	
    @Resource
    private MaterialImageService materialImageService;

    @Resource
    private MaterialRecycleBinService materialRecycleBinService;

    @Override
    public Set<String> operateByType(JSONObject materialObject) {
    	MaterialInfoDO materialInfo = JSONObject.toJavaObject(materialObject, MaterialInfoDO.class);
        String operate = materialObject.getString("operate");
        if (DamConstants.OPERATE_EDIT.equals(operate)) {
            // 图片所属目录移动，相当于新增目录材料
            List<String> imageIds = getImageIds(materialObject);
            materialImageService.removeByIds(imageIds);
        }
        else if (DamConstants.OPERATE_ADD.equals(operate)) {
            JSONArray materialFileArray = materialObject.getJSONArray("deleteMaterialFile");
            JSONObject jsonObject = materialFileArray.getJSONObject(materialFileArray.size() - 1);
            String originalMaterialId = jsonObject.getString("originalMaterialId");
            int lastXh = jsonObject.getIntValue("xh");
            QueryWrapper<MaterialImageDO> wrapper = new QueryWrapper<>();
            wrapper.eq("material_id", originalMaterialId);
            wrapper.gt("xh", lastXh);
            List<MaterialImageDO> originalMaterialImages = materialImageService.list(wrapper);
            String materialInfoId = materialObject.getString("materialInfoId");
            if (!CollectionUtils.isEmpty(originalMaterialImages)) {
                List<String> originalImageIds = originalMaterialImages.stream().map(MaterialImageDO::getId).collect(Collectors.toList());
                List<MaterialImageDO> dossierMaterialImages = materialFileArray.toJavaList(MaterialImageDO.class);
                List<MaterialImageDO> allMaterialImageList =  new ArrayList<>();
                allMaterialImageList.addAll(dossierMaterialImages);
                allMaterialImageList.addAll(originalMaterialImages);
                allMaterialImageList.sort(Comparator.comparing(MaterialImageDO::getXh));
                AtomicInteger xh = new AtomicInteger(1);
                allMaterialImageList.forEach(
                        materialImage -> {
                            materialImage.setMaterialId(materialInfoId);
                            materialImage.setXh(xh.get());
                            xh.getAndIncrement();
                        }
                );

                String catalogId = materialInfo.getCatalogId();
                String partCatalogId = materialInfo.getPartCatalogId();
                materialRecycleBinService.updateMaterialId(originalImageIds, materialInfoId, catalogId, partCatalogId);
                
                // 原材料已删除并且在属于拆分后的图片
                materialRecycleBinService.removeByIds(originalImageIds);
                materialImageService.saveBatch(allMaterialImageList);
            }

            QueryWrapper<MaterialRecycleBinDO> recycleBinWrapper = new QueryWrapper<>();
            recycleBinWrapper.eq("material_id", originalMaterialId);
            int num = materialRecycleBinService.count(recycleBinWrapper);
            if (Objects.equals(num, 0)) {
            	QueryWrapper<MaterialImageDO> imageWrapper = new QueryWrapper<>();
            	imageWrapper.eq("material_id", originalMaterialId);
            	materialImageService.remove(imageWrapper);
            }
        }

        return null;
    }
    
    private List<String> getImageIds(JSONObject materialObj) {
        JSONArray deleteMaterialFileArray = materialObj.getJSONArray("deleteMaterialFile");
        List<String> imageIds = new ArrayList<>();
        for (int j = 0; j < deleteMaterialFileArray.size(); j++) {
            JSONObject deleteMaterialFile = deleteMaterialFileArray.getJSONObject(j);
            imageIds.add(deleteMaterialFile.getString("id"));
        }
        return imageIds;
    }
}
