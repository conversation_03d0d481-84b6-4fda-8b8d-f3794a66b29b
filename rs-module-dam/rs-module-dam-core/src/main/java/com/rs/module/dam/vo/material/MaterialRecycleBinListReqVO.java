package com.rs.module.dam.vo.material;

import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 卷宗材料回收列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = false)
public class MaterialRecycleBinListReqVO extends BaseVO {
    @ApiModelProperty("图片关联的材料Id(dam_material_info表主键)")
    private String materialId;

    @ApiModelProperty("图片Id(dam_material_image表主键)")
    private String imageId;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("材料名称")
    private String name;

    @ApiModelProperty("材料数量")
    private Integer pageCount;

    @ApiModelProperty("模板Id")
    private String templateId;

    @ApiModelProperty("分卷目录Id")
    private String partCatalogId;

    @ApiModelProperty("目录Id")
    private String catalogId;

    @ApiModelProperty("材料序号")
    private Integer xh;

}
