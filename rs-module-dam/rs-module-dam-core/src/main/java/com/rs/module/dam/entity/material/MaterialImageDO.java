package com.rs.module.dam.entity.material;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO_;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 卷宗材料图片 DO
 *
 * <AUTHOR>
 */
@TableName("dam_material_image")
@KeySequence("dam_material_image_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialImageDO extends BaseDO_ {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 图片关联的材料Id(dam_material_info表主键)
     */
    private String materialId;
    /**
     * 图片序号
     */
    private Integer xh;
    /**
     * 图片名称
     */
    private String fileName;
    /**
     * 图片url
     */
    private String url;
    /**
     * 文书重复页检测-是否重复,1:重复
     */
    private String isRepeat;
    /**
     * 重复页分组标记
     */
    private String repeatMark;
    /**
     * 缺失签章个数
     */
    private String missSeal;
    /**
     * 图片方向合规性检测 (瑕疵页), 1—合规，0—不合规
     */
    private String isRegular;
    /**
     * 缺失签名个数
     */
    private String missSignature;
    /**
     * 文书空白页检测-是否空白页 1:是
     */
    private String blankSpace;
    /**
     * 文书规范性检测是否通过, 1:通过, 0:不通过
     */
    private String infoPass;
    /**
     * 缺失捺印个数
     */
    private String missFingerprint;
    /**
     * 材料无法识别
     */
    private String unidentify;
}
