package com.rs.module.dam.vo.ocr;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - ocr格式化审查任务 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FormatCheckJobRespVO extends BaseVO implements TransPojo{
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("任务状态(0：未完成，1：已完成，2：异常)")
    private String status;
    @ApiModelProperty("任务开始时间")
    private Date startTime;
    @ApiModelProperty("任务结束时间")
    private Date endTime;
    @ApiModelProperty("任务类型(0:未编目任务, 1:已编目任务, 2:智能阅卷任务)")
    private String jobType;
    @ApiModelProperty("上一次检测任务的Id")
    private String lastJobId;
    @ApiModelProperty("是否首次发起检测任务")
    private String isFirst;
    @ApiModelProperty("是否需要进行自动编目组卷(1:是,0:否)")
    private String autoCatalog;
    @ApiModelProperty("任务执行的最大次数")
    private Integer requestLimit;
    @ApiModelProperty("请求次数")
    private Integer requestCount;
}
