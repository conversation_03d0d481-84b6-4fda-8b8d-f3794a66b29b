package com.rs.module.dam.ocr.strategy;

/**
 * OCR策略枚举类
 * <AUTHOR>
 * @date 2025年4月26日
 */
public enum OcrStrategyEnum {
	
	//高新兴策略
	GOSUNCN("1", "com.rs.module.dam.ocr.gosuncn.GoOcrStrategy"),
	
	//享云策略
	XIANGYUN("2", "com.rs.module.dam.ocr.xiangyun.XyOcrStrategy");

	OcrStrategyEnum(String code, String className) {
		this.code = code;
		this.className = className;
	}
	
	//策略代码
	public String code;
	
	//策略处理类名称
	public String className;

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getClassName() {
		return className;
	}

	public void setClassName(String className) {
		this.className = className;
	}
	
	/**
	 * 根据策略代码获取OCR策略枚举
	 * @param code String 策略代码
	 * @return OcrStrategyEnum
	 * <AUTHOR>
	 * @date 2024年3月20日
	 */
	public static OcrStrategyEnum getOcrStrategyEnumByCode(String code) {
		for(OcrStrategyEnum strategy : OcrStrategyEnum.values()) {
			if(strategy.getCode().equals(code)) {
				return strategy;
			}
		}
		
		return null;
	}
}
