package com.rs.module.dam.dao.material;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.material.MaterialRecycleBinDO;
import com.rs.module.dam.vo.material.MaterialRecycleBinListReqVO;
import com.rs.module.dam.vo.material.MaterialRecycleBinPageReqVO;

/**
* 卷宗材料回收 Dao
*
* <AUTHOR>
*/
@Mapper
public interface MaterialRecycleBinDao extends IBaseDao<MaterialRecycleBinDO> {


    default PageResult<MaterialRecycleBinDO> selectPage(MaterialRecycleBinPageReqVO reqVO) {
        Page<MaterialRecycleBinDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<MaterialRecycleBinDO> wrapper = new LambdaQueryWrapperX<MaterialRecycleBinDO>()
            .eqIfPresent(MaterialRecycleBinDO::getMaterialId, reqVO.getMaterialId())
            .eqIfPresent(MaterialRecycleBinDO::getImageId, reqVO.getImageId())
            .eqIfPresent(MaterialRecycleBinDO::getJgrybm, reqVO.getJgrybm())
            .likeIfPresent(MaterialRecycleBinDO::getName, reqVO.getName())
            .eqIfPresent(MaterialRecycleBinDO::getPageCount, reqVO.getPageCount())
            .eqIfPresent(MaterialRecycleBinDO::getTemplateId, reqVO.getTemplateId())
            .eqIfPresent(MaterialRecycleBinDO::getPartCatalogId, reqVO.getPartCatalogId())
            .eqIfPresent(MaterialRecycleBinDO::getCatalogId, reqVO.getCatalogId())
            .eqIfPresent(MaterialRecycleBinDO::getXh, reqVO.getXh())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(MaterialRecycleBinDO::getAddTime);
        }
        Page<MaterialRecycleBinDO> materialRecycleBinPage = selectPage(page, wrapper);
        return new PageResult<>(materialRecycleBinPage.getRecords(), materialRecycleBinPage.getTotal());
    }
    default List<MaterialRecycleBinDO> selectList(MaterialRecycleBinListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MaterialRecycleBinDO>()
            .eqIfPresent(MaterialRecycleBinDO::getMaterialId, reqVO.getMaterialId())
            .eqIfPresent(MaterialRecycleBinDO::getImageId, reqVO.getImageId())
            .eqIfPresent(MaterialRecycleBinDO::getJgrybm, reqVO.getJgrybm())
            .likeIfPresent(MaterialRecycleBinDO::getName, reqVO.getName())
            .eqIfPresent(MaterialRecycleBinDO::getPageCount, reqVO.getPageCount())
            .eqIfPresent(MaterialRecycleBinDO::getTemplateId, reqVO.getTemplateId())
            .eqIfPresent(MaterialRecycleBinDO::getPartCatalogId, reqVO.getPartCatalogId())
            .eqIfPresent(MaterialRecycleBinDO::getCatalogId, reqVO.getCatalogId())
            .eqIfPresent(MaterialRecycleBinDO::getXh, reqVO.getXh())
        .orderByDesc(MaterialRecycleBinDO::getAddTime));
     }    

    /**
     * 更新材料Id
     * @param originalImageIds List<String> 图片Id集合
     * @param materialInfoId String 材料Id
     * @param catalogId String 目录Id
     * @param partCatalogId String 分卷目录Id
     */
    void updateMaterialId(@Param("imageIds") List<String> originalImageIds,
    		@Param("materialInfoId") String materialInfoId, 
    		@Param("catalogId") String catalogId, 
    		@Param("partCatalogId") String partCatalogId);
}
