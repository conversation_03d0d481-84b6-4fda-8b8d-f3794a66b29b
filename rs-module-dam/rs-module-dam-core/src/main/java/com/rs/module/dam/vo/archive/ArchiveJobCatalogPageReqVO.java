package com.rs.module.dam.vo.archive;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel(description = "管理后台 - 卷宗归档任务目录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ArchiveJobCatalogPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("批次Id")
    private String batchId;

    @ApiModelProperty("材料ID")
    private String materialId;

    @ApiModelProperty("材料序号")
    private Short materialXh;

    @ApiModelProperty("文件序号")
    private Short fileXh;

    @ApiModelProperty("标题")
    private String docTitle;

    @ApiModelProperty("分类Id")
    private String catalogId;

    @ApiModelProperty("任务状态（0：未完成，1：已完成，2：异常）")
    private String state;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
