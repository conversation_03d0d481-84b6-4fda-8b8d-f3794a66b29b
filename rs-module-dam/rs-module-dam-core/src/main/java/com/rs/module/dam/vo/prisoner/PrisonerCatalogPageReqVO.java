package com.rs.module.dam.vo.prisoner;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel(description = "管理后台 - 监管人员卷宗目录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrisonerCatalogPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("目录模板Id")
    private String catalogTemplateId;

    @ApiModelProperty("是否改变文书名称(1：是，0：否)")
    private String isChangeFileName;

    @ApiModelProperty("编目目录数据")
    private String bmCatalogData;

    @ApiModelProperty("组卷目录数据")
    private String zjCatalogData;

    @ApiModelProperty("快速组卷目录数据")
    private String kszjCatalogData;

    @ApiModelProperty("汇总目录数据")
    private String hzCatalogData;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
