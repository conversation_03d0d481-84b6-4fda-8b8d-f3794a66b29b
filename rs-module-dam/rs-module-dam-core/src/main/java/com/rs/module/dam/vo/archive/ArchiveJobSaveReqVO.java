package com.rs.module.dam.vo.archive;
import java.util.Date;

import javax.validation.constraints.NotEmpty;

import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 卷宗归档任务新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = false)
public class ArchiveJobSaveReqVO extends BaseVO{
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("封面内容")
    private String coverContent;
    
    @ApiModelProperty("材料ID")
    private String materialId;

    @ApiModelProperty("文件内容")
    private String fileContent;

    @ApiModelProperty("任务状态（0：未完成，1：已完成，2：异常）")
    private String state;

    @ApiModelProperty("分卷ID")
    private String partId;

    @ApiModelProperty("开始时间")
    private Date startTime;

    @ApiModelProperty("结束时间")
    private Date endTime;

    @ApiModelProperty("任务类型（0：编目组卷PDF，1：未编目组卷PDF，2：材料编目）")
    private String type;

    @ApiModelProperty("请求次数")
    private Integer requestCount;

    @ApiModelProperty("批次Id")
    private String batchId;

}
