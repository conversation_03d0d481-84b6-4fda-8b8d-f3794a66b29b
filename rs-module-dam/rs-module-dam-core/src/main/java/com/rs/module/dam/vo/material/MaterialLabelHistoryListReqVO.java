package com.rs.module.dam.vo.material;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 卷宗材料标签历史列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MaterialLabelHistoryListReqVO extends BaseVO {

	@ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("材料Id")
    private String materialId;

    @ApiModelProperty("材料目录Id")
    private String catalogId;

    @ApiModelProperty("材料文件图片Id")
    private String fileId;

}
