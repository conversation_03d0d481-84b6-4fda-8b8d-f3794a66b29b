package com.rs.module.dam.vo.archive;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 卷宗归档任务目录 Response VO")
@Data
@EqualsAndHashCode(callSuper = false)
public class ArchiveJobCatalogRespVO extends BaseVO implements TransPojo{
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("批次Id")
    private String batchId;
    @ApiModelProperty("材料ID")
    private String materialId;
    @ApiModelProperty("材料序号")
    private Short materialXh;
    @ApiModelProperty("文件序号")
    private Short fileXh;
    @ApiModelProperty("标题")
    private String docTitle;
    @ApiModelProperty("分类Id")
    private String catalogId;
    @ApiModelProperty("任务状态（0：未完成，1：已完成，2：异常）")
    private String state;
}
