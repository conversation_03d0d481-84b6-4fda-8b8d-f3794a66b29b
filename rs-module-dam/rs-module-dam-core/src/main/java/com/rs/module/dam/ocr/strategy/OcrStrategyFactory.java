package com.rs.module.dam.ocr.strategy;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.bsp.common.util.ServiceLocator;
import com.rs.module.dam.ocr.gosuncn.GoCallbackEnum;
import com.rs.module.dam.ocr.gosuncn.callback.IOcrCallback;

/**
 * OCR策略工厂类
 * <AUTHOR>
 * @date 2025年4月26日
 */
public class OcrStrategyFactory {
	
	public static final Logger log = LoggerFactory.getLogger(OcrStrategyFactory.class);

	/**
	 * 获取OCR处理策略
	 * @param strategyType String 策略类型
	 * @return OcrStrategy
	 */
	public static OcrStrategy getOcrStrategy(String strategyType) {
		String className = OcrStrategyEnum.getOcrStrategyEnumByCode(strategyType).getClassName();
		try {
			Class<?> strategy = Class.forName(className);
			return (OcrStrategy)ServiceLocator.getBean(strategy);
		}
		catch (Exception e) {
			log.info("无法获取处理策略，异常信息：{}", e.getMessage());
			return null;
		}
	}
	
	/**
	 * 获取OCR回调处理接口
	 * @param serviceCode String 服务代码
	 * @return IOcrCallback
	 */
	public static IOcrCallback getCallbackApi(String serviceCode) {
		String className = GoCallbackEnum.getOcrCallbackApiEnumByCode(serviceCode).getClassName();
		
		try {
			Class<?> strategy = Class.forName(className);
			return (IOcrCallback)ServiceLocator.getBean(strategy);
		}
		catch (Exception e) {
			log.info("无法获取回调接口策略，异常信息：{}", e.getMessage());
			return null;
		}
	}
}
