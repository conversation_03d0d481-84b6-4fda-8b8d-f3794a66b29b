package com.rs.module.dam.vo.material;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel(description = "管理后台 - 卷宗材料分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MaterialInfoPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("材料类型(0：未OCR,1：编目,2：组卷,3: 快速组卷)")
    private String type;

    @ApiModelProperty("材料名称")
    private String name;

    @ApiModelProperty("材料数量")
    private Integer pageCount;

    @ApiModelProperty("材料文书(json)")
    private String materialFile;

    @ApiModelProperty("模板ID")
    private String templateId;

    @ApiModelProperty("分卷目录Id")
    private String partCatalogId;

    @ApiModelProperty("目录Id")
    private String catalogId;

    @ApiModelProperty("材料序号")
    private Integer xh;

    @ApiModelProperty("关联dam_upload_pdf_data的主键")
    private String pdfId;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
