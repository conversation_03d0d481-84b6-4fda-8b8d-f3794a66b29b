package com.rs.module.dam.service.material;

import java.util.List;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.material.PdfBatchDO;
import com.rs.module.dam.vo.material.PdfBatchListReqVO;
import com.rs.module.dam.vo.material.PdfBatchPageReqVO;
import com.rs.module.dam.vo.material.PdfBatchSaveReqVO;

/**
 * pdf处理批次 Service 接口
 *
 * <AUTHOR>
 */
public interface PdfBatchService extends IBaseService<PdfBatchDO>{

    /**
     * 创建pdf处理批次
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPdfBatch(@Valid PdfBatchSaveReqVO createReqVO);

    /**
     * 更新pdf处理批次
     *
     * @param updateReqVO 更新信息
     */
    void updatePdfBatch(@Valid PdfBatchSaveReqVO updateReqVO);

    /**
     * 删除pdf处理批次
     *
     * @param id 编号
     */
    void deletePdfBatch(String id);

    /**
     * 获得pdf处理批次
     *
     * @param id 编号
     * @return pdf处理批次
     */
    PdfBatchDO getPdfBatch(String id);

    /**
    * 获得pdf处理批次分页
    *
    * @param pageReqVO 分页查询
    * @return pdf处理批次分页
    */
    PageResult<PdfBatchDO> getPdfBatchPage(PdfBatchPageReqVO pageReqVO);

    /**
    * 获得pdf处理批次列表
    *
    * @param listReqVO 查询条件
    * @return pdf处理批次列表
    */
    List<PdfBatchDO> getPdfBatchList(PdfBatchListReqVO listReqVO);


}
