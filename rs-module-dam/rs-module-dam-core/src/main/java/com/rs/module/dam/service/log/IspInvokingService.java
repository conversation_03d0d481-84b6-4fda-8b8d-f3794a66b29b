package com.rs.module.dam.service.log;

import java.util.List;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.log.IspInvokingDO;
import com.rs.module.dam.vo.log.IspInvokingListReqVO;
import com.rs.module.dam.vo.log.IspInvokingPageReqVO;
import com.rs.module.dam.vo.log.IspInvokingSaveReqVO;

/**
 * 智能服务调用日志 Service 接口
 *
 * <AUTHOR>
 */
public interface IspInvokingService extends IBaseService<IspInvokingDO>{

    /**
     * 创建智能服务调用日志
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createIspInvoking(@Valid IspInvokingSaveReqVO createReqVO);

    /**
     * 更新智能服务调用日志
     *
     * @param updateReqVO 更新信息
     */
    void updateIspInvoking(@Valid IspInvokingSaveReqVO updateReqVO);

    /**
     * 删除智能服务调用日志
     *
     * @param id 编号
     */
    void deleteIspInvoking(String id);

    /**
     * 获得智能服务调用日志
     *
     * @param id 编号
     * @return 智能服务调用日志
     */
    IspInvokingDO getIspInvoking(String id);

    /**
    * 获得智能服务调用日志分页
    *
    * @param pageReqVO 分页查询
    * @return 智能服务调用日志分页
    */
    PageResult<IspInvokingDO> getIspInvokingPage(IspInvokingPageReqVO pageReqVO);

    /**
    * 获得智能服务调用日志列表
    *
    * @param listReqVO 查询条件
    * @return 智能服务调用日志列表
    */
    List<IspInvokingDO> getIspInvokingList(IspInvokingListReqVO listReqVO);


}
