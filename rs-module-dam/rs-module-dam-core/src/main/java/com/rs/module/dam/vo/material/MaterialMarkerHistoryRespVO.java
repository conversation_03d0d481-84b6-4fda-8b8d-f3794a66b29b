package com.rs.module.dam.vo.material;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 卷宗材料标注历史 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MaterialMarkerHistoryRespVO extends BaseVO implements TransPojo{
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("卷宗Id")
    private String jzId;
    @ApiModelProperty("材料Id")
    private String materialId;
    @ApiModelProperty("材料文件图片Id")
    private String fileId;
    @ApiModelProperty("标注信息")
    private String marker;
    @ApiModelProperty("编号")
    private String number;
    @ApiModelProperty("标注内容")
    private String content;
}
