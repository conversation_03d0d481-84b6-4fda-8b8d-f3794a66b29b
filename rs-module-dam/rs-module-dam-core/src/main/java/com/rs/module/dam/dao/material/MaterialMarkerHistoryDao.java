package com.rs.module.dam.dao.material;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.material.MaterialMarkerHistoryDO;
import com.rs.module.dam.vo.material.MaterialMarkerHistoryListReqVO;
import com.rs.module.dam.vo.material.MaterialMarkerHistoryPageReqVO;

/**
* 卷宗材料标注历史 Dao
*
* <AUTHOR>
*/
@Mapper
public interface MaterialMarkerHistoryDao extends IBaseDao<MaterialMarkerHistoryDO> {


    default PageResult<MaterialMarkerHistoryDO> selectPage(MaterialMarkerHistoryPageReqVO reqVO) {
        Page<MaterialMarkerHistoryDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<MaterialMarkerHistoryDO> wrapper = new LambdaQueryWrapperX<MaterialMarkerHistoryDO>()
            .eqIfPresent(MaterialMarkerHistoryDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(MaterialMarkerHistoryDO::getJzId, reqVO.getJzId())
            .eqIfPresent(MaterialMarkerHistoryDO::getMaterialId, reqVO.getMaterialId())
            .eqIfPresent(MaterialMarkerHistoryDO::getFileId, reqVO.getFileId())
            .eqIfPresent(MaterialMarkerHistoryDO::getMarker, reqVO.getMarker())
            .eqIfPresent(MaterialMarkerHistoryDO::getNumber, reqVO.getNumber())
            .eqIfPresent(MaterialMarkerHistoryDO::getContent, reqVO.getContent())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(MaterialMarkerHistoryDO::getAddTime);
        }
        Page<MaterialMarkerHistoryDO> materialMarkerHistoryPage = selectPage(page, wrapper);
        return new PageResult<>(materialMarkerHistoryPage.getRecords(), materialMarkerHistoryPage.getTotal());
    }
    default List<MaterialMarkerHistoryDO> selectList(MaterialMarkerHistoryListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MaterialMarkerHistoryDO>()
            .eqIfPresent(MaterialMarkerHistoryDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(MaterialMarkerHistoryDO::getJzId, reqVO.getJzId())
            .eqIfPresent(MaterialMarkerHistoryDO::getMaterialId, reqVO.getMaterialId())
            .eqIfPresent(MaterialMarkerHistoryDO::getFileId, reqVO.getFileId())
            .eqIfPresent(MaterialMarkerHistoryDO::getMarker, reqVO.getMarker())
            .eqIfPresent(MaterialMarkerHistoryDO::getNumber, reqVO.getNumber())
            .eqIfPresent(MaterialMarkerHistoryDO::getContent, reqVO.getContent())
        .orderByDesc(MaterialMarkerHistoryDO::getAddTime));    }


    }
