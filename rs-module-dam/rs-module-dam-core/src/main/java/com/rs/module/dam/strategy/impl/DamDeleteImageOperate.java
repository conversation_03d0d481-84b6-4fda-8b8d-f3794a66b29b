package com.rs.module.dam.strategy.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.util.StringUtil;
import com.rs.module.dam.constant.CheckTypeEnum;
import com.rs.module.dam.constant.DamConstants;
import com.rs.module.dam.dao.material.MaterialInfoDao;
import com.rs.module.dam.entity.material.MaterialImageDO;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.entity.material.MaterialRecycleBinDO;
import com.rs.module.dam.entity.ocr.FormatCheckJobDO;
import com.rs.module.dam.service.material.MaterialImageService;
import com.rs.module.dam.service.material.MaterialRecycleBinService;
import com.rs.module.dam.service.ocr.FormatCheckJobService;
import com.rs.module.dam.service.ocr.FormatCheckResultService;
import com.rs.module.dam.strategy.OperateTypeService;

import cn.hutool.core.bean.BeanUtil;

/**
 * 图片删除操作
 * <AUTHOR>
 * @date 2025年4月18日
 */
@Service(value = "damDeleteImageOperate")
public class DamDeleteImageOperate implements OperateTypeService {

    @Resource
    MaterialInfoDao materialInfoDao;

    @Resource
    private MaterialRecycleBinService materialRecycleBinService;

    @Resource
    private MaterialImageService materialImageService;

    @Resource
    private FormatCheckJobService formatCheckJobService;

    @Resource
    private FormatCheckResultService formatCheckResultService;

    @Override
    public Set<String> operateByType(JSONObject materialObject) {
    	MaterialInfoDO deletedMaterialInfo = JSONObject.toJavaObject(materialObject, MaterialInfoDO.class);
        String operate = materialObject.getString("operate");
        String jgrybm = deletedMaterialInfo.getJgrybm();
        String type = deletedMaterialInfo.getType();
        if (DamConstants.OPERATE_EDIT.equals(operate)) {
            String materialId = deletedMaterialInfo.getId();
            QueryWrapper<MaterialImageDO> wrapper = new QueryWrapper<>();
            wrapper.eq("material_id", materialId);
            List<MaterialImageDO> materialImageList = materialImageService.list(wrapper);
            Optional<MaterialImageDO> first = materialImageList.stream().findFirst();
            JSONArray deleteMaterialFileArray = materialObject.getJSONArray("deleteMaterialFile");
            MaterialInfoDO materialInfo = materialInfoDao.selectById(materialId);
            if (first.isPresent()) {
                List<MaterialRecycleBinDO> materialRecycleBins = materialRecucleBinListInfo(deleteMaterialFileArray, materialInfo);
                if (!CollectionUtils.isEmpty(materialRecycleBins)) {
                	materialRecycleBinService.saveBatch(materialRecycleBins);
                }
                
                //处理将图片拖入到其它材料再删除后，回收站有材料无图片的情况
                MaterialInfoDO dbMaterial = materialInfoDao.selectById(deletedMaterialInfo.getId());
                List<MaterialImageDO> materialImageOrderList = materialImageListInfo(dbMaterial);
                if(materialImageOrderList != null){
                    materialImageOrderList.forEach(image->{
                    	MaterialImageDO materialImage = materialImageService.getById(image.getId());
                        if(materialImage == null){
                        	materialImageService.save(image);
                        }
                    });
                }
            }
            else {
                // 第一次删除
                List<MaterialImageDO> materialImageOrderList = materialImageListInfo(materialInfo);
                List<MaterialRecycleBinDO> materialRecycleBinList = materialRecucleBinListInfo(deleteMaterialFileArray, materialInfo);
                if (!CollectionUtils.isEmpty(materialImageOrderList)) {
                	materialImageService.saveBatch(materialImageList);
                }
                if (!CollectionUtils.isEmpty(materialRecycleBinList)) {
                	materialRecycleBinService.saveBatch(materialRecycleBinList);
                }
            }

        }
        else if (DamConstants.OPERATE_DELETE.equals(operate)) {
        	MaterialInfoDO dbMaterial = materialInfoDao.selectById(deletedMaterialInfo.getId());
            List<MaterialImageDO> materialImageOrderList = materialImageListInfo(dbMaterial);
            JSONArray materialFileArray = JSON.parseArray(dbMaterial.getMaterialFile());
            List<MaterialRecycleBinDO> materialRecycleBinList = materialRecucleBinListInfo(materialFileArray, dbMaterial);
            if (!CollectionUtils.isEmpty(materialRecycleBinList)) {
            	materialRecycleBinService.saveBatch(materialRecycleBinList);
            }
            QueryWrapper<MaterialImageDO> wrapper = new QueryWrapper<>();
            wrapper.eq("material_id", deletedMaterialInfo.getId());        
            List<MaterialImageDO> materialImageList = materialImageService.list(wrapper);
            Optional<MaterialImageDO> first = materialImageList.stream().findFirst();

            if (!first.isPresent() && !CollectionUtils.isEmpty(materialImageOrderList)) {
                materialImageOrderList.forEach(image -> {
                    MaterialImageDO imageOld = materialImageService.getById(image.getId());
                    if(imageOld == null){
                    	materialImageService.save(image);
                    }
                    else{
                        //如果存在，则说明image以前已经删除过，但是经过一系列业务操作后（如pdf重新转换、OCR等），image被添加到了其它材料中，此时需要将回收站中旧的材料和图片删除，不然会导致image的id冲突，无法加入回收站
                    	materialImageService.deleteMaterialImage(imageOld.getId());
                        QueryWrapper<MaterialRecycleBinDO> deleteBinWrapper = new QueryWrapper<>();
                        deleteBinWrapper.eq("material_id", imageOld.getMaterialId());
                        deleteBinWrapper.eq("imageId",imageOld.getId());
                        materialRecycleBinService.remove(deleteBinWrapper);
                        materialImageService.save(image);
                    }
                });
            }
        }

        // 遍历删除的图片, 判断是否有标记,。没有标记:不遍历; 有标记: 遍历所有材料
        JSONArray deleteMaterialArray = materialObject.getJSONArray("deleteMaterialFile");
        Set<String> repeatMarkSet = new HashSet<>();
        for (int i = 0; i < deleteMaterialArray.size(); i++) {
            JSONObject imageObj = deleteMaterialArray.getJSONObject(i);
            String imageId = imageObj.getString("id");

            List<String> checkTypeList = new ArrayList<>();
            // 重复页标记
            boolean repeatMark = imageObj.containsKey("repeatMark");
            if (repeatMark) {
                repeatMarkSet.add(imageObj.getString("repeatMark"));
                checkTypeList.add(CheckTypeEnum.REPEAT_PAGE.getCode());
            }
            boolean missSeal = imageObj.containsKey("missSeal");
            if (missSeal) {
                checkTypeList.add(CheckTypeEnum.SEAL.getCode());
            }
            boolean isRegular = imageObj.containsKey("isRegular");
            if (isRegular) {
                checkTypeList.add(CheckTypeEnum.FLAW.getCode());
            }
            boolean missSignature = imageObj.containsKey("missSignature");
            if (missSignature) {
                checkTypeList.add(CheckTypeEnum.SIGNATURE.getCode());
            }
            boolean blankSpace = imageObj.containsKey("blankSpace");
            if (blankSpace) {
                checkTypeList.add(CheckTypeEnum.BLANK_SPACE.getCode());
            }
            boolean infoPass = imageObj.containsKey("infoPass");
            if (infoPass) {
                checkTypeList.add(CheckTypeEnum.INFO_EXTRACT.getCode());
            }
            boolean missFingerprint =imageObj.containsKey("missFingerprint");
            if (missFingerprint) {
                checkTypeList.add(CheckTypeEnum.FINGERPRINT.getCode());
            }

            // 更新格式化检测结果数据
            if (!CollectionUtils.isEmpty(checkTypeList)) {
            	QueryWrapper<FormatCheckJobDO> queryJobWrapper = new QueryWrapper<>();
                queryJobWrapper.eq("jgrybm", jgrybm);
                queryJobWrapper.eq("status", "1");
                queryJobWrapper.eq("job_type", type);
                queryJobWrapper.orderByDesc("add_time");
                queryJobWrapper.last("limit 1");
                FormatCheckJobDO formatCheckJob = formatCheckJobService.getOne(queryJobWrapper);
                if (null != formatCheckJob){
                    String jobId = formatCheckJob.getId();
                    formatCheckResultService.updateShowData(jobId, imageId, checkTypeList, "0");
                }
            }
        }

        return repeatMarkSet;
    }


    public List<MaterialRecycleBinDO> materialRecucleBinListInfo(JSONArray deleteMaterialFileArray, MaterialInfoDO materialInfo) {
        List<MaterialRecycleBinDO> dossierMaterialRecycleBinList = new ArrayList<>();
        for (int j = 0; j < deleteMaterialFileArray.size(); j++) {
            JSONObject deleteMaterialFile = deleteMaterialFileArray.getJSONObject(j);
            String imageId = deleteMaterialFile.getString("id");
            MaterialRecycleBinDO materialRecycleBin = buildRecycleBinInfo(imageId, materialInfo);
            dossierMaterialRecycleBinList.add(materialRecycleBin);
        }

        return dossierMaterialRecycleBinList;
    }

    public MaterialRecycleBinDO buildRecycleBinInfo(String imageId, MaterialInfoDO materialInfo) {
        MaterialRecycleBinDO dossierMaterialRecycleBin = new MaterialRecycleBinDO();
        BeanUtil.copyProperties(materialInfo, dossierMaterialRecycleBin);
        dossierMaterialRecycleBin.setId(StringUtil.getGuid32());
        dossierMaterialRecycleBin.setMaterialId(materialInfo.getId());
        dossierMaterialRecycleBin.setImageId(imageId);
        dossierMaterialRecycleBin.setPageCount(1);
        return dossierMaterialRecycleBin;
    }

    public List<MaterialImageDO> materialImageListInfo(MaterialInfoDO materialInfo) {
        JSONArray materialFileArray = JSON.parseArray(materialInfo.getMaterialFile());
        List<MaterialImageDO> materialImageList = new ArrayList<>();
        for (int i = 0; i < materialFileArray.size(); i++) {
            JSONObject materialFile = materialFileArray.getJSONObject(i);
            MaterialImageDO dossierMaterialImage = JSONObject.toJavaObject(materialFile, MaterialImageDO.class);
            dossierMaterialImage.setMaterialId(materialInfo.getId());
            materialImageList.add(dossierMaterialImage);
        }
        return  materialImageList;
    }
}
