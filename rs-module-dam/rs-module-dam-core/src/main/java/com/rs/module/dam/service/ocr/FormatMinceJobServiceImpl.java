package com.rs.module.dam.service.ocr;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.dao.ocr.FormatMinceJobDao;
import com.rs.module.dam.entity.ocr.FormatMinceJobDO;
import com.rs.module.dam.vo.ocr.FormatMinceJobListReqVO;
import com.rs.module.dam.vo.ocr.FormatMinceJobPageReqVO;
import com.rs.module.dam.vo.ocr.FormatMinceJobSaveReqVO;


/**
 * ocr格式化审查任务细分 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FormatMinceJobServiceImpl extends BaseServiceImpl<FormatMinceJobDao, FormatMinceJobDO> implements FormatMinceJobService {

    @Resource
    private FormatMinceJobDao formatMinceJobDao;

    @Override
    public String createFormatMinceJob(FormatMinceJobSaveReqVO createReqVO) {
        // 插入
        FormatMinceJobDO formatMinceJob = BeanUtils.toBean(createReqVO, FormatMinceJobDO.class);
        formatMinceJobDao.insert(formatMinceJob);
        // 返回
        return formatMinceJob.getId();
    }

    @Override
    public void updateFormatMinceJob(FormatMinceJobSaveReqVO updateReqVO) {
        // 校验存在
        validateFormatMinceJobExists(updateReqVO.getId());
        // 更新
        FormatMinceJobDO updateObj = BeanUtils.toBean(updateReqVO, FormatMinceJobDO.class);
        formatMinceJobDao.updateById(updateObj);
    }

    @Override
    public void deleteFormatMinceJob(String id) {
        // 校验存在
        validateFormatMinceJobExists(id);
        // 删除
        formatMinceJobDao.deleteById(id);
    }

    private void validateFormatMinceJobExists(String id) {
        if (formatMinceJobDao.selectById(id) == null) {
            throw new ServerException("ocr格式化审查任务细分数据不存在");
        }
    }

    @Override
    public FormatMinceJobDO getFormatMinceJob(String id) {
        return formatMinceJobDao.selectById(id);
    }

    @Override
    public PageResult<FormatMinceJobDO> getFormatMinceJobPage(FormatMinceJobPageReqVO pageReqVO) {
        return formatMinceJobDao.selectPage(pageReqVO);
    }

    @Override
    public List<FormatMinceJobDO> getFormatMinceJobList(FormatMinceJobListReqVO listReqVO) {
        return formatMinceJobDao.selectList(listReqVO);
    }

    /**
     * 查询格式化审查检查任务列表
     * @param queryParam Map<String, Object> 查询参数
     * @param page Integer 当前第几页
     * @param pageSize Integer 每页大小
     * @return List<Map<String, Object>>
     */
    @Override
    public Page<Map<String, Object>> selectMinceJobList(Map<String, String> queryParam, Integer page, Integer pageSize){
    	Page<Map<String, Object>> pageInfo = new Page<>(page, pageSize);
    	List<Map<String, Object>> list = formatMinceJobDao.selectMinceJobList(queryParam, pageInfo);
    	return pageInfo.setRecords(list);
    }
    
    /**
     * 更新格式化审查子任务结束时间
     * @param jobId String 任务Id
     */
    @Override
    public void updateMinceJobEndTime(String jobId) {
    	formatMinceJobDao.updateMinceJobEndTime(jobId);
    }
}
