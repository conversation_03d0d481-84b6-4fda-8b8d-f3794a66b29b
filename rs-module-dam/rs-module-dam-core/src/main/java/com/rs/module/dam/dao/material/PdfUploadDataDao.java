package com.rs.module.dam.dao.material;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.material.PdfUploadDataDO;
import com.rs.module.dam.vo.material.PdfUploadDataListReqVO;
import com.rs.module.dam.vo.material.PdfUploadDataPageReqVO;

/**
* pdf上传数据 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PdfUploadDataDao extends IBaseDao<PdfUploadDataDO> {


    default PageResult<PdfUploadDataDO> selectPage(PdfUploadDataPageReqVO reqVO) {
        Page<PdfUploadDataDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PdfUploadDataDO> wrapper = new LambdaQueryWrapperX<PdfUploadDataDO>()
            .eqIfPresent(PdfUploadDataDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PdfUploadDataDO::getPdfUrl, reqVO.getPdfUrl())
            .eqIfPresent(PdfUploadDataDO::getUploadData, reqVO.getUploadData())
            .likeIfPresent(PdfUploadDataDO::getPdfName, reqVO.getPdfName())
            .eqIfPresent(PdfUploadDataDO::getStatus, reqVO.getStatus())
            .eqIfPresent(PdfUploadDataDO::getPageCount, reqVO.getPageCount())
            .eqIfPresent(PdfUploadDataDO::getBatchId, reqVO.getBatchId())
            .eqIfPresent(PdfUploadDataDO::getMd5, reqVO.getMd5())
            .eqIfPresent(PdfUploadDataDO::getDpi, reqVO.getDpi())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PdfUploadDataDO::getAddTime);
        }
        Page<PdfUploadDataDO> pdfUploadDataPage = selectPage(page, wrapper);
        return new PageResult<>(pdfUploadDataPage.getRecords(), pdfUploadDataPage.getTotal());
    }
    default List<PdfUploadDataDO> selectList(PdfUploadDataListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PdfUploadDataDO>()
            .eqIfPresent(PdfUploadDataDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PdfUploadDataDO::getPdfUrl, reqVO.getPdfUrl())
            .eqIfPresent(PdfUploadDataDO::getUploadData, reqVO.getUploadData())
            .likeIfPresent(PdfUploadDataDO::getPdfName, reqVO.getPdfName())
            .eqIfPresent(PdfUploadDataDO::getStatus, reqVO.getStatus())
            .eqIfPresent(PdfUploadDataDO::getPageCount, reqVO.getPageCount())
            .eqIfPresent(PdfUploadDataDO::getBatchId, reqVO.getBatchId())
            .eqIfPresent(PdfUploadDataDO::getMd5, reqVO.getMd5())
            .eqIfPresent(PdfUploadDataDO::getDpi, reqVO.getDpi())
        .orderByDesc(PdfUploadDataDO::getAddTime));
     }
    
	/**
	 * 根据类型获取转换的pdf记录
	 * @param params Map<String, Object> 查询参数
	 * @return List<PdfUploadDataDO>
	 */
	List<PdfUploadDataDO> selectConvertPdfWithType(Map<String, Object> params);
	
	/**
	 * 查询重复的pdf上传文件
	 * @param params Map<String, Object> 查询参数
	 * @return List<PdfUploadDataDO>
	 */
	List<PdfUploadDataDO> selectRepeatPdf(Map<String, Object> params);
}
