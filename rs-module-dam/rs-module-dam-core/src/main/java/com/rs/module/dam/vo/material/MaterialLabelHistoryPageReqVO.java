package com.rs.module.dam.vo.material;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel(description = "管理后台 - 卷宗材料标签历史分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MaterialLabelHistoryPageReqVO extends PageParam{
	private static final long serialVersionUID = 1L;
	
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("材料Id")
    private String materialId;

    @ApiModelProperty("材料目录Id")
    private String catalogId;

    @ApiModelProperty("材料文件图片Id")
    private String fileId;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
