package com.rs.module.dam.vo.sys;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel(description = "管理后台 - 卷宗配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ConfigGroupPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("文件服务器地址")
    private String fileServer;

    @ApiModelProperty("是否开启扫描仪 1:是，0:否")
    private String openScan;

    @ApiModelProperty("扫描仪类型")
    private String scanType;

    @ApiModelProperty("扫描服务地址")
    private String scanServer;

    @ApiModelProperty("扫描仪授权码")
    private String scanLicense;

    @ApiModelProperty("ocr服务地址")
    private String ocrServer;

    @ApiModelProperty("是否开启借阅审批 1:是, 0:否")
    private String enableBorrowApprove;

    @ApiModelProperty("是否开启未成年人特殊审批 1:是, 0:否")
    private String enableMinorApprove;

    @ApiModelProperty("配置方式 1:默认配置, 2:定制化配置")
    private String configType;

    @ApiModelProperty("是否开启ocr服务, 0:否, 1:是")
    private String openOcr;

    @ApiModelProperty("对接isp的appId")
    private String appId;

    @ApiModelProperty("对接isp的appKey")
    private String appKey;

    @ApiModelProperty("对接isp的secretKey")
    private String secretKey;

    @ApiModelProperty("组卷后是否生成双层pdf")
    private String makeDoublePdf;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
