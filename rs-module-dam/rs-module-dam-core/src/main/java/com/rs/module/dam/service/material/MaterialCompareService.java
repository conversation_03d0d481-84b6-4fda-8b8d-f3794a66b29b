package com.rs.module.dam.service.material;

import java.util.List;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.material.MaterialCompareDO;
import com.rs.module.dam.vo.material.MaterialCompareListReqVO;
import com.rs.module.dam.vo.material.MaterialComparePageReqVO;
import com.rs.module.dam.vo.material.MaterialCompareSaveReqVO;

/**
 * 卷宗材料比对 Service 接口
 *
 * <AUTHOR>
 */
public interface MaterialCompareService extends IBaseService<MaterialCompareDO>{

    /**
     * 创建卷宗材料比对
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMaterialCompare(@Valid MaterialCompareSaveReqVO createReqVO);

    /**
     * 更新卷宗材料比对
     *
     * @param updateReqVO 更新信息
     */
    void updateMaterialCompare(@Valid MaterialCompareSaveReqVO updateReqVO);

    /**
     * 删除卷宗材料比对
     *
     * @param id 编号
     */
    void deleteMaterialCompare(String id);

    /**
     * 获得卷宗材料比对
     *
     * @param id 编号
     * @return 卷宗材料比对
     */
    MaterialCompareDO getMaterialCompare(String id);

    /**
    * 获得卷宗材料比对分页
    *
    * @param pageReqVO 分页查询
    * @return 卷宗材料比对分页
    */
    PageResult<MaterialCompareDO> getMaterialComparePage(MaterialComparePageReqVO pageReqVO);

    /**
    * 获得卷宗材料比对列表
    *
    * @param listReqVO 查询条件
    * @return 卷宗材料比对列表
    */
    List<MaterialCompareDO> getMaterialCompareList(MaterialCompareListReqVO listReqVO);


}
