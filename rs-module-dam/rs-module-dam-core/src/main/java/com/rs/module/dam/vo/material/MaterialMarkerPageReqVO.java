package com.rs.module.dam.vo.material;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel(description = "管理后台 - 卷宗材料标注分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MaterialMarkerPageReqVO extends PageParam{
	private static final long serialVersionUID = 1L;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("卷宗Id")
    private String jzId;

    @ApiModelProperty("材料Id")
    private String materialId;

    @ApiModelProperty("材料文件图片Id")
    private String fileId;

    @ApiModelProperty("标注信息")
    private String marker;

    @ApiModelProperty("编号")
    private String number;

    @ApiModelProperty("标注内容")
    private String content;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
