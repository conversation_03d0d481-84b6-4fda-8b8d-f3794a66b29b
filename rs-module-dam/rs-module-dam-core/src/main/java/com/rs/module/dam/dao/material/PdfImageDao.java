package com.rs.module.dam.dao.material;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.material.PdfImageDO;
import com.rs.module.dam.vo.material.PdfImageListReqVO;
import com.rs.module.dam.vo.material.PdfImagePageReqVO;

/**
* pdf转换图片 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PdfImageDao extends IBaseDao<PdfImageDO> {


    default PageResult<PdfImageDO> selectPage(PdfImagePageReqVO reqVO) {
        Page<PdfImageDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PdfImageDO> wrapper = new LambdaQueryWrapperX<PdfImageDO>()
            .eqIfPresent(PdfImageDO::getPdfId, reqVO.getPdfId())
            .likeIfPresent(PdfImageDO::getImageName, reqVO.getImageName())
            .eqIfPresent(PdfImageDO::getImageUrl, reqVO.getImageUrl())
            .eqIfPresent(PdfImageDO::getImageData, reqVO.getImageData())
            .eqIfPresent(PdfImageDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PdfImageDO::getXh, reqVO.getXh())
            .eqIfPresent(PdfImageDO::getStatus, reqVO.getStatus())
            .eqIfPresent(PdfImageDO::getErrorMsg, reqVO.getErrorMsg())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PdfImageDO::getAddTime);
        }
        Page<PdfImageDO> pdfImagePage = selectPage(page, wrapper);
        return new PageResult<>(pdfImagePage.getRecords(), pdfImagePage.getTotal());
    }
    default List<PdfImageDO> selectList(PdfImageListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PdfImageDO>()
            .eqIfPresent(PdfImageDO::getPdfId, reqVO.getPdfId())
            .likeIfPresent(PdfImageDO::getImageName, reqVO.getImageName())
            .eqIfPresent(PdfImageDO::getImageUrl, reqVO.getImageUrl())
            .eqIfPresent(PdfImageDO::getImageData, reqVO.getImageData())
            .eqIfPresent(PdfImageDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PdfImageDO::getXh, reqVO.getXh())
            .eqIfPresent(PdfImageDO::getStatus, reqVO.getStatus())
            .eqIfPresent(PdfImageDO::getErrorMsg, reqVO.getErrorMsg())
        .orderByDesc(PdfImageDO::getAddTime));
    }
    
    /**
     * 查询pdf已转换成功的图片
     * @param pdfId String pdf主键
     * @return List<String>
     */
    List<String> selectImageIdWithPdfId(@Param("pdfId") String pdfId);
}
