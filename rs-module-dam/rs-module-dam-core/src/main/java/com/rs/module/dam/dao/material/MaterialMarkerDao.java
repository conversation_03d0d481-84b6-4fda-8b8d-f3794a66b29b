package com.rs.module.dam.dao.material;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.material.MaterialMarkerDO;
import com.rs.module.dam.vo.material.MaterialMarkerListReqVO;
import com.rs.module.dam.vo.material.MaterialMarkerPageReqVO;

/**
* 卷宗材料标注 Dao
*
* <AUTHOR>
*/
@Mapper
public interface MaterialMarkerDao extends IBaseDao<MaterialMarkerDO> {


    default PageResult<MaterialMarkerDO> selectPage(MaterialMarkerPageReqVO reqVO) {
        Page<MaterialMarkerDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<MaterialMarkerDO> wrapper = new LambdaQueryWrapperX<MaterialMarkerDO>()
            .eqIfPresent(MaterialMarkerDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(MaterialMarkerDO::getJzId, reqVO.getJzId())
            .eqIfPresent(MaterialMarkerDO::getMaterialId, reqVO.getMaterialId())
            .eqIfPresent(MaterialMarkerDO::getFileId, reqVO.getFileId())
            .eqIfPresent(MaterialMarkerDO::getMarker, reqVO.getMarker())
            .eqIfPresent(MaterialMarkerDO::getNumber, reqVO.getNumber())
            .eqIfPresent(MaterialMarkerDO::getContent, reqVO.getContent())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(MaterialMarkerDO::getAddTime);
        }
        Page<MaterialMarkerDO> materialMarkerPage = selectPage(page, wrapper);
        return new PageResult<>(materialMarkerPage.getRecords(), materialMarkerPage.getTotal());
    }
    default List<MaterialMarkerDO> selectList(MaterialMarkerListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MaterialMarkerDO>()
            .eqIfPresent(MaterialMarkerDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(MaterialMarkerDO::getJzId, reqVO.getJzId())
            .eqIfPresent(MaterialMarkerDO::getMaterialId, reqVO.getMaterialId())
            .eqIfPresent(MaterialMarkerDO::getFileId, reqVO.getFileId())
            .eqIfPresent(MaterialMarkerDO::getMarker, reqVO.getMarker())
            .eqIfPresent(MaterialMarkerDO::getNumber, reqVO.getNumber())
            .eqIfPresent(MaterialMarkerDO::getContent, reqVO.getContent())
        .orderByDesc(MaterialMarkerDO::getAddTime));
    }
    
    /**
     * 获取监管人员标注过的材料文件Id 
     * @param jgrybm String 监管人员编码
     * @return List<String>
     */
    List<String> selectMarkerFileIdByJgrybm(String jgrybm);
}
