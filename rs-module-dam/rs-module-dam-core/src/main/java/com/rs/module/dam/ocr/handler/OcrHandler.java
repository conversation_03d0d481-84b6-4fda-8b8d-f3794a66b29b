package com.rs.module.dam.ocr.handler;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.orm.mybatis.util.WrapUtil;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.google.common.collect.Lists;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.dam.config.IspV2ConfigUtil;
import com.rs.module.dam.config.WebSocketServer;
import com.rs.module.dam.constant.CheckFormatEnum;
import com.rs.module.dam.constant.CheckTypeEnum;
import com.rs.module.dam.constant.DamConstants;
import com.rs.module.dam.constant.OcrConstants;
import com.rs.module.dam.constant.RedisConstants;
import com.rs.module.dam.entity.archive.ArchiveCoverDO;
import com.rs.module.dam.entity.log.IspInvokingDO;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.entity.ocr.FormatCheckJobDO;
import com.rs.module.dam.entity.ocr.FormatMinceJobDO;
import com.rs.module.dam.entity.sys.ConfigGroupDO;
import com.rs.module.dam.ocr.gosuncn.GoUtil;
import com.rs.module.dam.ocr.strategy.OcrStrategy;
import com.rs.module.dam.ocr.strategy.OcrStrategyFactory;
import com.rs.module.dam.ocr.util.OcrUtil;
import com.rs.module.dam.service.log.IspInvokingService;
import com.rs.module.dam.service.material.MaterialInfoService;
import com.rs.module.dam.service.ocr.FormatCheckJobService;
import com.rs.module.dam.service.ocr.FormatCheckResultService;
import com.rs.module.dam.service.ocr.FormatMinceJobService;
import com.rs.module.dam.service.ocr.TFormatCheckService;
import com.rs.module.dam.service.sys.ConfigGroupService;
import com.rs.module.dam.util.DamUtil;
import com.rs.module.dam.util.RedisTemplateUtil;
import com.rs.module.dam.util.RestTemplateUtil;
import com.rs.module.dam.util.ThreadPoolUtil;
import com.rs.module.dam.vo.ocr.CheckFormatParam;
import com.rs.module.dam.websocket.WebSocketCode;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * OCR处理器
 * <AUTHOR>
 * @date 2025年4月28日
 */
@Component
@Slf4j
public class OcrHandler {
	
	@Autowired
	private MaterialInfoService dossierMaterialInfoService;
	
	@Autowired
    private ConfigGroupService dmsConfigGroupService;
	
    @Autowired
    private FormatCheckJobService dmsFormatCheckJobService;
    
    @Autowired
    private FormatMinceJobService dmsFormatMinceJobService;
    
    @Autowired
    private FormatCheckResultService dmsFormatCheckResultService;
    
    @Autowired
    private IspInvokingService dmsIspInvokingService;
    
    @Autowired
    private TFormatCheckService tFormatCheckService;
	
	@Autowired
	private RedisTemplateUtil redisTemplateUtil;
	
	//ocr版本号
	@Value("${isp.ocrVersion:1.3}")
	private String ocrVersion;
	
	//智能编目单次提交材料数
	@Value("${isp.ocr2.singleBMSize:0}")
	private Integer singleOcrSize;
	
	//是否启用快速组卷智能编目
	@Value("${isp.enableKszjZnbm:false}")
	private boolean enableKszjZnbm;
	
	/**
	 * 判断是否启用了V2版本
	 * @return boolean
	 */
	public boolean enableV2(){
		return ocrVersion.equals("2.0");
	}
	
	/**
	 * 判断是否启用快速组卷智能编目
	 * @return boolean
	 */
	public boolean enableKszjZnbm() {
		return enableKszjZnbm;
	}
	
	/**
	 * 启动智能编目
	 * @param jgrybm String 监管人员编码
	 * @param idCard String 身份证号码
	 * @param orgCode String 机构代码
	 * @param materialId String 材料Id
	 * @return CommonResult<?>
	 */
	public CommonResult<?> startZnbm(String jgrybm, String idCard, String orgCode, String materialId) {		
		try {
			//构建待编目材料查询Wrapper
			QueryWrapper<MaterialInfoDO> wrapper = DamUtil.getMaterialInfoWrapper(jgrybm, "0");
			
			//指定名称
			wrapper.eq("name", "未识别");
			
			//指定了材料
			if(StringUtil.isNotEmpty(materialId)) {
				wrapper.eq("id", materialId);
			}
			
			//获取数据库中待编目材料
			List<MaterialInfoDO> materialList = dossierMaterialInfoService.list(wrapper);
			if(CollectionUtil.isNull(materialList)) {
				return CommonResult.error("案件无需要识别的材料");
			}
			
			//待编目的材料
			List<MaterialInfoDO> needMaterialList = new ArrayList<>();
			
			//是否有新增的待编目的材料（依据缓存判断---key : "ocr:start:materId:{materialId}"）
			int count = 0;
			for(MaterialInfoDO material : materialList) {
				String materialKey = String.format(RedisConstants.START_OCR_MATER_ID, material.getId());
				if(!redisTemplateUtil.exists(materialKey)) {
					count ++;
					needMaterialList.add(material);
				}
			}
			
			//没有待编目材料时返回
			if(count == 0) {
				return CommonResult.error("材料正在识别中,没有新的材料需要发起识别");
			}
			
			//一次性处理案件未编目的材料
			if(singleOcrSize == 0) {
				executeZnbmByThreadPool(needMaterialList, jgrybm, idCard, orgCode);
			}
			
			//分页异步处理案件未编目的材料
			else {
				List<List<MaterialInfoDO>> pageMaterialLists = Lists.partition(needMaterialList, singleOcrSize);
				for(List<MaterialInfoDO> pageMaterialList : pageMaterialLists) {
					executeZnbmByThreadPool(pageMaterialList, jgrybm, idCard, orgCode);
				}
			}
			
			//获取ocr识别中的材料文件数量
			int ocringCount = 0;
			for(MaterialInfoDO material : needMaterialList) {
				JSONArray arr = JSON.parseArray(material.getMaterialFile());
				ocringCount = ocringCount + arr.size();
			}
			
			//缓存案件智能编目的材料文件数量（缓存key："ocr:job:finish:num:jgrybm:{jgrybm}"）
			String finishMark = String.format(RedisConstants.TASK_JOB_FINISH_NUM, jgrybm);
			redisTemplateUtil.set(finishMark, String.valueOf(ocringCount));
			
			return CommonResult.success(JSONUtil.createObj().set("total", ocringCount),
					"OCR处理-智能编目启动成功");
		}
		catch(Exception e) {
			log.error("ocr处理-智能编目异常：{}", e);
			return CommonResult.error(e.getMessage());
		}
	}
	
	/**
	 * 使用线程池执行智能编目任务
	 * @param materialInfoList List<MaterialInfoDO> 待处理的材料列表
	 * @param jgrybm String 监管人员编码
	 * @param idCard String 身份证号码
	 * @param orgCode String 机构代码
	 */
	private void executeZnbmByThreadPool(List<MaterialInfoDO> materialInfoList, String jgrybm,
			String idCard, String orgCode) {
		ThreadPoolUtil.getPool().execute(new Runnable() {
			
			@Override
			public void run() {
				OcrStrategy ocrStrategy = OcrStrategyFactory.getOcrStrategy(OcrUtil.getOcrType());
				ocrStrategy.znbm(materialInfoList, jgrybm, idCard, orgCode);
			}
		});
	}
	
	/**
	 * 启动双层pdf生成
	 * @param dmsArchiveCover ArchiveCoverDO 卷宗封面
	 * @param materialList List<MaterialInfoDO> 卷宗材料
	 * @param jgrybm String 监管人员编码
	 * @param zjType String 组卷类型(0:编目组卷完成组卷页面、1:卷宗导入确认组卷页面)
	 * @param idCard String 身份证号码
	 * @param orgCode String 机构代码
	 */
	public void startScPdf(ArchiveCoverDO dmsArchiveCover, List<MaterialInfoDO> materialList,
			String jgrybm, String zjType, String idCard, String orgCode) {		
		log.info("OCR处理-生成双层pdf-启动双层pdf转换 jgrybm:{}, zjType:{}, 材料数量:{}",
				jgrybm, zjType, materialList.size());
		
		//使用序号对材料列表排序
		materialList.sort(Comparator.comparing(MaterialInfoDO :: getXh));
		
		//构建两个文件列表(一个用于调用OCR服务调用，一个用于归档任务保存)
		JSONArray filesList = new JSONArray();
		JSONArray fileObjCoverList = new JSONArray();
        int xh = 1;
        for (MaterialInfoDO dossierMaterialInfo : materialList) {
            JSONArray materialInfoArray = JSONObject.parseArray(dossierMaterialInfo.getMaterialFile());
            for (int i = 0; i < materialInfoArray.size(); i++) {
                JSONObject material = materialInfoArray.getJSONObject(i);
                if(material != null && !material.isEmpty()) {
                	JSONObject file = new JSONObject();
                    file.put("fileId",material.getString("id"));
                    file.put("fileUrl",material.getString("url"));
                    filesList.add(file);

                    JSONObject fileObjCover = new JSONObject();
                    fileObjCover.put("fileId",material.getString("id"));
                    fileObjCover.put("fileXh",xh);
                    fileObjCoverList.add(fileObjCover);

                    xh ++;
                }
            }
        }
        
        //异步执行-启动双层pdf转换任务
		ThreadPoolUtil.getPool().execute(new Runnable() {
			
			@Override
			public void run() {
				OcrStrategy ocrStrategy = OcrStrategyFactory.getOcrStrategy(OcrUtil.getOcrType());
				ocrStrategy.scpdf(dmsArchiveCover, filesList, fileObjCoverList, jgrybm, zjType, idCard, orgCode);
			}
		});
	}
	
	/**
	 * 启动格式化审查
	 * @param formatParam CheckFormatParam 格式化审查参数
	 * @return CheckFormatEnum
	 */
	public CheckFormatEnum startFormatCheck(CheckFormatParam formatParam) {
		String jgrybm = formatParam.getJgrybm();
		String checkType = formatParam.getCheckType();
		
		//判断当前案件是否正在进行格式化审查
		String memberValue = jgrybm + "--" + checkType;
		boolean isMember = redisTemplateUtil.sIsMember(RedisConstants.FORMAT_CHECKING, memberValue);
		if(isMember) {
			return CheckFormatEnum.CHECK_FORMAT_PROCESSING;
		}
		
        //判断任务是否正在进行自动编目
        String catalogIngKey = String.format(RedisConstants.FORMAT_CHECK_AUTO_CATALOGING_MARK, jgrybm);
        String cataloging = redisTemplateUtil.get(catalogIngKey);
        if (StringUtil.isNotEmpty(cataloging)){
            return CheckFormatEnum.AUTO_CATALOGING;
        }
        
        //是否有正在进行重新检测的任务
        String retryKey = String.format(RedisConstants.FORMAT_CHECK_RETRY_MARK, jgrybm, checkType);
        Set<String> retryJob = redisTemplateUtil.sMembers(retryKey);
        if (!CollectionUtils.isEmpty(retryJob)){
            return CheckFormatEnum.RETRY_MINCE_JOB_PROCESSING;
        }
        
        //判断是否有待检测的材料
        List<MaterialInfoDO> dossierMaterialInfoList = tFormatCheckService.getFormatCheckMaterialInfoList(jgrybm, checkType, null);
        if(CollectionUtil.isNull(dossierMaterialInfoList)) {
        	return CheckFormatEnum.NO_MATERIAL_FILE;
        }
        
        //当前用户
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        
        //获取卷宗配置
        ConfigGroupDO dmsConfigGroup = dmsConfigGroupService.getUserConfigGroup(sessionUser.getOrgCode());
        
        //判断是否开启了OCR服务
        if(!StringUtil.getBoolean(dmsConfigGroup.getOpenOcr())) {
        	return CheckFormatEnum.OCR_SERVER_NOT_OPEN;
        }
        
        //获取待检测的文件数组和待ocr识别的文件数组
        JSONArray[] materialFileJsonArrays = OcrUtil.getMaterialFileJsonArrays(dossierMaterialInfoList, checkType);
        JSONArray checkFiles = materialFileJsonArrays[0];
        JSONArray needOcrFiles = materialFileJsonArrays[1];
        if(checkFiles.isEmpty()) {
        	return CheckFormatEnum.NO_MATERIAL_FILE;
        }
        formatParam.setFileData(JSONObject.toJSONString(checkFiles));
        
        //查询案件最后一次审查任务
        QueryWrapper<FormatCheckJobDO> checkJobWrapper = WrapUtil.eq(FormatCheckJobDO.class, new String[]{"jgrybm", "job_type"},
        		new String[]{jgrybm, checkType}).orderByDesc("add_time");
        FormatCheckJobDO lastCheckJob = dmsFormatCheckJobService.getOne(checkJobWrapper);
        
        //生成任务Id
        String jobId = StringUtil.getGuid32();
        formatParam.setJobId(jobId);
        
        //初始化新的审查任务
        FormatCheckJobDO formatCheckJob = new FormatCheckJobDO();
        formatCheckJob.setId(jobId);
        formatCheckJob.setJgrybm(formatParam.getJgrybm());
        formatCheckJob.setStatus(DamConstants.JOB_STATE_NO);
        formatCheckJob.setAddTime(new Date());
        formatCheckJob.setStartTime(new Date());
        formatCheckJob.setAddUser(sessionUser.getIdCard());
        formatCheckJob.setJobType(formatParam.getCheckType());
        formatCheckJob.setOrgCode(sessionUser.getOrgCode());
        
        //已经发起过审查任务
        if(null != lastCheckJob) {
        	formatCheckJob.setIsFirst("0");
        	formatCheckJob.setLastJobId(lastCheckJob.getId());        	
        	CheckFormatEnum checkFormatEnum = comparisonFormatJob(formatCheckJob, lastCheckJob, formatParam,
        			dmsConfigGroup, dossierMaterialInfoList, checkFiles, needOcrFiles);
        	if(!checkFormatEnum.equals(CheckFormatEnum.PROCESS_CONTINUE)) {
        		return checkFormatEnum;
        	}
        }
        
        //首次发起审查任务
        else {
        	
        	//构建检测子任务
        	List<FormatMinceJobDO> minceJobList = OcrUtil.buildMinceJob(formatParam, checkFiles);
        	
        	//插入检测任务和子任务
        	dmsFormatCheckJobService.save(formatCheckJob);
        	dmsFormatMinceJobService.saveBatch(minceJobList);
        	
        	//初始化格式化审查进度
        	initMinceJobProcess(minceJobList);
        	
        	//异步执行-启动格式化审查任务
    		ThreadPoolUtil.getPool().execute(new Runnable() {
    			
    			@Override
    			public void run() {
    				OcrStrategy ocrStrategy = OcrStrategyFactory.getOcrStrategy(OcrUtil.getOcrType());
    				ocrStrategy.formatCheck(dmsConfigGroup, checkFiles, formatCheckJob, minceJobList);
    			}
    		});
        }
        
        //添加案件格式化审查缓存
        redisTemplateUtil.sAdd(RedisConstants.FORMAT_CHECKING, memberValue);
        
        //全局标记存在格式化审查任务
        DamConstants.HAVING_FORMAT_CHECK = true;
        
		return CheckFormatEnum.START_SUCCESS;
	}
	
	/**
	 * 比较格式化任务(非首次格式化审查时)
	 * @param formatCheckJob FormatCheckJobDO 当前格式化任务
	 * @param lastJob FormatCheckJobDO 上一次格式化任务
	 * @param formatParam FormatCheckJobDO 格式化参数
	 * @param dmsConfigGroup ConfigGroupDO 卷宗配置
	 * @param dossierMaterialInfoList List<MaterialInfoDO> 卷宗材料
	 * @param checkFiles JSONArray 需要检测的材料文件
	 * @param needOcrFiles JSONArray 需要ocr识别的文件
	 * @return CheckFormatEnum
	 */
	private CheckFormatEnum comparisonFormatJob(FormatCheckJobDO formatCheckJob, FormatCheckJobDO lastJob,
			CheckFormatParam formatParam, ConfigGroupDO dmsConfigGroup, List<MaterialInfoDO> dossierMaterialInfoList,
			JSONArray checkFiles, JSONArray needOcrFiles){
		
		//待处理的检测子任务
		List<FormatMinceJobDO> newMinceJobs = new ArrayList<>();
		
		//上一次检测所有子任务
		List<FormatMinceJobDO> minceJobList = tFormatCheckService.getCheckedMinceJobList(lastJob.getId());
		
		//处理公章审查任务
		FormatMinceJobDO sealJob = OcrUtil.getMinceJobFinishedByCheckType(minceJobList, CheckTypeEnum.SEAL.getCode());
		checkMinceJob(formatParam.getSeal(), CheckTypeEnum.SEAL.getCode(), checkFiles, formatParam, newMinceJobs, sealJob);
		
		//处理签名审查任务
		FormatMinceJobDO signatureJob = OcrUtil.getMinceJobFinishedByCheckType(minceJobList, CheckTypeEnum.SIGNATURE.getCode());
		checkMinceJob(formatParam.getSignature(), CheckTypeEnum.SIGNATURE.getCode(), checkFiles, formatParam, newMinceJobs, signatureJob);
		
		//处理捺印审查任务
		FormatMinceJobDO fingerPrintJob = OcrUtil.getMinceJobFinishedByCheckType(minceJobList, CheckTypeEnum.FINGERPRINT.getCode());
		checkMinceJob(formatParam.getFingerprint(), CheckTypeEnum.FINGERPRINT.getCode(), checkFiles, formatParam, newMinceJobs, fingerPrintJob);
		
		//处理空白页检测任务
		FormatMinceJobDO blankJob = OcrUtil.getMinceJobFinishedByCheckType(minceJobList, CheckTypeEnum.BLANK_SPACE.getCode());
		checkMinceJob(formatParam.getBlankSpace(), CheckTypeEnum.BLANK_SPACE.getCode(), checkFiles, formatParam, newMinceJobs, blankJob);
		
		//处理重复页检测任务
		FormatMinceJobDO repeatJob = OcrUtil.getMinceJobFinishedByCheckType(minceJobList, CheckTypeEnum.REPEAT_PAGE.getCode());
		checkMinceJob(formatParam.getRepeatPage(), CheckTypeEnum.REPEAT_PAGE.getCode(), checkFiles, formatParam, newMinceJobs, repeatJob);
		
		//处理瑕疵页检测任务
		FormatMinceJobDO flawJob = OcrUtil.getMinceJobFinishedByCheckType(minceJobList, CheckTypeEnum.FLAW.getCode());
		checkMinceJob(formatParam.getFlaw(), CheckTypeEnum.FLAW.getCode(), checkFiles, formatParam, newMinceJobs, flawJob);
		
		//处理文书规范性检测任务
		FormatMinceJobDO wsxxtqJob = OcrUtil.getMinceJobFinishedByCheckType(minceJobList, CheckTypeEnum.INFO_EXTRACT.getCode());
		checkMinceJob(formatParam.getInfoExtract(), CheckTypeEnum.INFO_EXTRACT.getCode(), checkFiles, formatParam, newMinceJobs, wsxxtqJob);
		
		//没有新的检测子任务
		if(CollectionUtil.isNull(newMinceJobs)) {
			return CheckFormatEnum.NOT_NEW_FILE_CHECK;
		}
		else {
			//需要新增或检测入库的任务
			List<FormatMinceJobDO> needAddJobs = newMinceJobs.stream().filter(minceJob ->
				DamConstants.CHECK_FORMAT_MINCE_TYPE_ADD.equals(minceJob.getMinceType())).collect(Collectors.toList());
			if(CollectionUtil.isNull(needAddJobs)) {
				List<FormatMinceJobDO> needCheckJobs = newMinceJobs.stream().filter(minceJob ->
				DamConstants.CHECK_FORMAT_MINCE_TYPE_CHECK.equals(minceJob.getMinceType())).collect(Collectors.toList());
				if(CollectionUtil.isNull(needCheckJobs)) {
					return CheckFormatEnum.NOT_NEW_FILE_CHECK;
				}
			}
		}
		
		//插入检测任务和子任务
    	dmsFormatCheckJobService.save(formatCheckJob);
    	dmsFormatMinceJobService.saveBatch(newMinceJobs);
    	
    	//将上一次的检测结果归属到本次任务上
    	dmsFormatCheckResultService.updateCheckResultJobId(formatCheckJob.getId(), lastJob.getId());
    	
    	//初始化格式化审查进度
    	initMinceJobProcess(newMinceJobs);
    	
    	//需检测的任务
    	List<FormatMinceJobDO> needCheckJobs = newMinceJobs.stream().filter(minceJob -> DamConstants.CHECK_FORMAT_MINCE_TYPE_ADD
    			.equals(minceJob.getMinceType())).collect(Collectors.toList());
    	
    	//异步执行-启动格式化审查任务
		ThreadPoolUtil.getPool().execute(new Runnable() {
			
			@Override
			public void run() {
				OcrStrategy ocrStrategy = OcrStrategyFactory.getOcrStrategy(OcrUtil.getOcrType());
				ocrStrategy.formatCheck(dmsConfigGroup, checkFiles, formatCheckJob, needCheckJobs);
			}
		});
		
		return CheckFormatEnum.PROCESS_CONTINUE;
	}
	
	/**
	 * 验证检测子任务并添加到待处理的任务中
	 * @param jobIsOpen String 任务是否已经开启
	 * @param checkType String 检测类型
	 * @param checkFiles JSONArray 检测文件集合
	 * @param formatParam CheckFormatParam 格式化审查参数
	 * @param newMinceJobs List<FormatMinceJobDO> 待处理的任务集合
	 * @param lastMinceJob FormatMinceJobDO 上一个同类型任务
	 */
	private void checkMinceJob(String jobIsOpen, String checkType, JSONArray checkFiles, CheckFormatParam formatParam,
			List<FormatMinceJobDO> newMinceJobs, FormatMinceJobDO lastMinceJob) {
		
		//开启了任务
		if(DamConstants.FORMAT_CHECK_MINCE_OPEN.equals(jobIsOpen)) {
			
			//上一次开启了任务
			if(null != lastMinceJob) {
				JSONArray lastFiles = JSONObject.parseArray(lastMinceJob.getBaseFiles());
				JSONArray checkFileData = OcrUtil.compareFileData(lastFiles, JSON.parseArray(formatParam.getFileData()));
				
				//待新增的任务
				FormatMinceJobDO minceJob = null;
				
				//是否开发调试(强制进行格式化审查)
				boolean isDebug = false;
				
				//开发调试
				if(isDebug) {
					minceJob = OcrUtil.buildMinceJobSingle(formatParam, checkType, checkFileData);
				}
				
				//有新增的材料可发起检测任务
				else if(CollectionUtil.isNotNull(checkFileData)) {
					minceJob = OcrUtil.buildMinceJobSingle(formatParam, checkType, checkFileData);
				}
				
				//无新增的材料时更新检测任务状态
				else {
					minceJob = OcrUtil.buildMinceJobSingle(formatParam, checkType, checkFiles);
					minceJob.setJobStatus(DamConstants.JOB_STATE_YES);
					minceJob.setShowJob(DamConstants.CHECK_FORMAT_RESULT_HIDDEN);
					minceJob.setMinceType(DamConstants.CHECK_FORMAT_MINCE_TYPE_WAVE);
					setMinceJobProcess(minceJob.getId(), 1.0f);
				}
				
				newMinceJobs.add(minceJob);
			}
			
			//上一次未开启任务
			else {
				FormatMinceJobDO minceJob = OcrUtil.buildMinceJobSingle(formatParam, checkType, checkFiles);
				newMinceJobs.add(minceJob);
			}
		}
		
		//未开启任务
		else {
			if(null != lastMinceJob) {
				lastMinceJob.setId(StringUtil.getGuid32());
				lastMinceJob.setJobId(formatParam.getJobId());
				lastMinceJob.setShowJob(DamConstants.CHECK_FORMAT_RESULT_HIDDEN);
				lastMinceJob.setMinceType(DamConstants.CHECK_FORMAT_MINCE_TYPE_COPY);
				newMinceJobs.add(lastMinceJob);
			}
		}
	}
	
	/**
	 * 初始化格式化审查子任务进度
	 * @param minceJobs List<FormatMinceJobDO> 格式化审查子任务
	 */
	private void initMinceJobProcess(List<FormatMinceJobDO> minceJobs) {
		minceJobs.forEach(minceJob -> {
			if(DamConstants.JOB_STATE_NO.equals(minceJob.getJobStatus())
					|| DamConstants.JOB_STATE_PENDING.equals(minceJob.getJobStatus())) {
				setMinceJobProcess(minceJob.getId(), 0.0f);
			}
			else if(DamConstants.JOB_STATE_YES.equals(minceJob.getJobStatus())) {
				setMinceJobProcess(minceJob.getId(), 1.0f);
			}
			else {
				if(DamConstants.CHECK_FORMAT_MINCE_TYPE_ADD.equals(minceJob.getMinceType())) {
					setMinceJobProcess(minceJob.getId(), 0.0f);
				}
				else if(DamConstants.CHECK_FORMAT_MINCE_TYPE_CHECK.equals(minceJob.getMinceType())) {
					setMinceJobProcess(minceJob.getId(), 1.0f);
				}
				else if(DamConstants.CHECK_FORMAT_MINCE_TYPE_COPY.equals(minceJob.getMinceType())) {
					setMinceJobProcess(minceJob.getId(), 1.0f);
				}
			}
		});
	}
	
	/**
	 * 更新取消的格式化审查任务
	 * @param jobId String 任务Id
	 * @param jobs FormatMinceJobDO 子任务动态参数
	 */
	public void updateCancelFormatJob(String jobId, FormatMinceJobDO... jobs) {
		String cancelKey = String.format(RedisConstants.FORMAT_CHECK_CANCEL_TYPE, jobId);
		Set<String> cancelMinceIds = redisTemplateUtil.sMembers(cancelKey);
		
		//更新取消检测任务的状态
		if(CollectionUtil.isNotNull(cancelMinceIds)) {
			for(FormatMinceJobDO job : jobs) {
				if(job != null && cancelMinceIds.contains(job.getId())) {
					updateCancelMinceJob(job);
				}
			}
		}
	}
	
	/**
	 * 更新取消的格式化审查任务状态
	 * @param minceJob FormatMinceJobDO 格式化审查子任务
	 */
	public void updateCancelMinceJob(FormatMinceJobDO minceJob) {
		if(minceJob != null) {
			
			//更新任务状态
			minceJob.setJobStatus(DamConstants.JOB_STATE_CANCEL);
			
			//清除运行中子任务缓存
			redisTemplateUtil.hDel(RedisConstants.FORMAT_CHECK_MINCE_JOB_PROCESS, minceJob.getId());
			
			//清除放弃子任务缓存
			String cancelKey = String.format(RedisConstants.FORMAT_CHECK_CANCEL_TYPE, minceJob.getJobId());
	        redisTemplateUtil.sRem(cancelKey, minceJob.getId());
		}
	}
	
	/**
	 * 更新取消的格式化审查任务状态
	 * @param minceJobs List<FormatMinceJobDO> 格式化审查子任务
	 * @param jobId String 任务Id
	 */
	public void updateCancelMinceJobList(List<FormatMinceJobDO> minceJobList, String jobId) {
		
        //在进行开启ocr基础通用接口调用的过程中,点击了取消,设置任务状态为取消状态
        //获取在检测列表上点击取消检测的任务
        String cancelKey = String.format(RedisConstants.FORMAT_CHECK_CANCEL_TYPE, jobId);
        Set<String> cancelMinceIds = redisTemplateUtil.sMembers(cancelKey);
        if (!CollectionUtils.isEmpty(cancelMinceIds)){
            //此处的任务不包含瑕疵页和空白页检测的任务
            for (FormatMinceJobDO dmsFormatMinceJob : minceJobList) {
                String minceId = dmsFormatMinceJob.getId();
                if (cancelMinceIds.contains(minceId)) {
                    dmsFormatMinceJob.setJobStatus(DamConstants.JOB_STATE_CANCEL);
                    
                    //删除检测中任务列表的进度条
                    redisTemplateUtil.hDel(RedisConstants.FORMAT_CHECK_MINCE_JOB_PROCESS, minceId);
                    redisTemplateUtil.sRem(cancelKey, minceId);
                }
            }
        }
	}
	
	/**
	 * 设置格式化审查子任务分页进度
	 * @param minceId String 子任务Id
	 * @param fileCount int 总文件数
	 * @param checkedCount int 已检查数量
	 * @return float
	 */
    public float setMinceJobPageProgress(String minceId, int fileCount, int checkedCount){
        float process = (float) checkedCount / fileCount;
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        float formatProcess = Float.parseFloat(decimalFormat.format(process));
        setMinceJobProcess(minceId, formatProcess);
        return formatProcess;
    }
	
	/**
	 * 设置格式化审查子任务进度
	 * @param minceId String 子任务Id
	 * @param process float 进度
	 */
	public void setMinceJobProcess(String minceId, float process) {
		Map<String, Object> processMap = new HashMap<>();
		processMap.put(DamConstants.MaterialColumn.JOB_MINCE_ID, minceId);
		processMap.put(DamConstants.MaterialColumn.JOB_MINCE_PROCESS, process);
		redisTemplateUtil.hSet(RedisConstants.FORMAT_CHECK_MINCE_JOB_PROCESS, minceId, JSONObject.toJSONString(processMap));
	}
	
	/**
	 * 初始化格式化审查子任务进度
	 * @param minceId  String 子任务Id
	 */
	public void initMinceJobProcess(String minceId) {
		String minceProcess = redisTemplateUtil.hGet(RedisConstants.FORMAT_CHECK_MINCE_JOB_PROCESS, minceId);
        if (StringUtil.isEmpty(minceProcess)){
            setMinceJobProcess(minceId, 0.0f);
        }
	}
	
	/**
	 * 获取格式化审查完成的数量
	 * @param jobId String 检测任务Id
	 */
	public int getFormatCheckFinishNum(String jobId) {
		int finishNum = 0;
		
		//格式化审查批处理完成的数量缓存Key
		String batchNumKey = String.format(OcrConstants.REDIS_FORMAT_CHECK_BATCH_NUM, jobId);
		if(redisTemplateUtil.exists(batchNumKey)) {
			String cacheNum = redisTemplateUtil.get(batchNumKey);
			finishNum = Integer.parseInt(cacheNum);
		}
		
		return finishNum;
	}
	
	/**
	 * 获取格式化审查完成的数量
	 * @param jobId String 检测任务Id
	 * @param num int 当前已检测的数量
	 */
	public int getFormatCheckFinishNum(String jobId, int num) {
		int finishNum = num;
		
		//格式化审查批处理完成的数量缓存Key
		String batchNumKey = String.format(OcrConstants.REDIS_FORMAT_CHECK_BATCH_NUM, jobId);
		if(redisTemplateUtil.exists(batchNumKey)) {
			String cacheNum = redisTemplateUtil.get(batchNumKey);
			if(StringUtil.isEmpty(cacheNum)) {
				redisTemplateUtil.set(batchNumKey, String.valueOf(num));
			}
			else {
				finishNum = Integer.valueOf(cacheNum) + num;
				redisTemplateUtil.set(batchNumKey, String.valueOf(finishNum));
			}
		}
		else {
			redisTemplateUtil.set(batchNumKey, String.valueOf(num));
		}
		
		return finishNum;
	}
	
	/**
	 * 清除格式化审查任务处理完成时清除缓存
	 * @param checkJob FormatCheckJobDO 格式化审查任务
	 * @param minceJobList List<FormatMinceJobDO> 格式化审查子任务
	 */
	public void clearFormatCheckCacheWhenFinish(FormatCheckJobDO checkJob, List<FormatMinceJobDO> minceJobList) {
		if(checkJob != null) {
			
			//删除检测中的案件缓存
			redisTemplateUtil.sRem(RedisConstants.FORMAT_CHECKING, checkJob.getJgrybm() + "--" + checkJob.getJobType());
			
			//删除重新发起检测的缓存
            String retryKey = String.format(RedisConstants.FORMAT_CHECK_RETRY_MARK, checkJob.getJgrybm(), checkJob.getJobType());
            redisTemplateUtil.deleteKey(retryKey);
		}
		
		if(CollectionUtil.isNotNull(minceJobList)) {
			minceJobList.forEach(minceJob -> {
				
				//删除检测中任务列表的进度条缓存
                redisTemplateUtil.hDel(RedisConstants.FORMAT_CHECK_MINCE_JOB_PROCESS, minceJob.getId() );
				
				//删除格式化审查批处理完成的数量缓存
				String batchNumKey = String.format(OcrConstants.REDIS_FORMAT_CHECK_BATCH_NUM, minceJob.getId());
				if(redisTemplateUtil.exists(batchNumKey)) {
					redisTemplateUtil.deleteKey(batchNumKey);
				}
			});
		}
	}
	
	/**
	 * 更新格式化审查子任务进度
	 * @param minceJob FormatMinceJobDO 格式化审查子任务
	 * @param currentCheckedSize int 当前检测的数量
	 */
	public boolean getAndUpdateFormactCheckMinceJobProcess(FormatMinceJobDO minceJob, int currentCheckedSize) {
		
		//待检测的文件
//		JSONArray checkFiles = JSONArray.parseArray(minceJob.getCheckFiles());
		JSONArray checkFiles = JSONArray.parseArray(minceJob.getBaseFiles());
		
		//完成的数量
		int finishedNum = getFormatCheckFinishNum(minceJob.getId(), currentCheckedSize);
		
		//总数量(瑕疵页检测因包含图片方向合规性检测需 * 2)
		int totalSize = checkFiles.size();
		if(CheckTypeEnum.FLAW.getCode().equals(minceJob.getCheckType())) {
			totalSize *= 2;
		}
		
		//设置缓存进度状态
		setMinceJobPageProgress(minceJob.getId(), totalSize, finishedNum);
		
//		log.error("++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++");
//		log.error("设置进度id:{}, checkType:{}, totalSize:{}, finishedNum:{}", minceJob.getId(), minceJob.getCheckType(), totalSize, finishedNum);
//		log.error("++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++");
		
		//返回子任务是否完成
		return finishedNum >= totalSize;
	}
	
	/**
	 * 推送格式化审查进度结果
	 * @param checkJob FormatCheckJobDO
	 */
	public void sendFormatCheckProcessResult(FormatCheckJobDO checkJob) {
		Map<String, Object> processMap = buildMinceJobProcessMap(checkJob.getId(), checkJob.getJgrybm(), checkJob.getJobType());
		
		//构建待发送消息
		JSONObject sendMsg = new JSONObject();
        sendMsg.put("jgrybm", checkJob.getJgrybm());
        sendMsg.put("process", processMap);
        sendMsg.put("status", checkJob.getStatus());
        sendMsg.put("jobType", checkJob.getJobType());
        sendMsg.put("code", WebSocketCode.CHECK_FORMAT_DATA_PROCESS.getCode());
        sendMsg.put("msg", WebSocketCode.CHECK_FORMAT_DATA_PROCESS.getMsg());
        
        //设置进度的缓存数据,用于前端调用接口查询指定案件的检测进度
        String processKey = String.format(RedisConstants.FORMAT_CHECK_PROCESS_AJ, checkJob.getJgrybm(), checkJob.getJobType());
        redisTemplateUtil.set(processKey, sendMsg.toJSONString(), 1800);
        
        //发送消息
        log.info("智能格式化审查任务返回处理进度:{}", JSONObject.toJSONString(sendMsg));
        WebSocketServer.sendInfo(null, JSONObject.toJSONString(sendMsg));
	}
	
	/**
	 * 构建格式化审查子任务进度Map
	 * @param jobId String 任务Id
	 * @param jgrybm String 监管人员编码
	 * @param jobType String 任务类型
	 * @return Map<String, Object>
	 */
	@SuppressWarnings("unchecked")
	private Map<String, Object> buildMinceJobProcessMap(String jobId, String jgrybm, String jobType){
		QueryWrapper<FormatMinceJobDO> minceJobWrapper = WrapUtil.eq(FormatMinceJobDO.class, "job_id", jobId);
		List<FormatMinceJobDO> minceJobList = dmsFormatMinceJobService.list(minceJobWrapper);
		
		//子任务Id集合
		List<Object> minceIdList = minceJobList.stream().map(FormatMinceJobDO :: getId).collect(Collectors.toList());
		
		//缓存中的子任务进度
		List<Object> redisProcessList = redisTemplateUtil.hmGet(RedisConstants.FORMAT_CHECK_MINCE_JOB_PROCESS, minceIdList);
		Map<String, Object> redisProcessMap = new HashMap<>();
		redisProcessList.forEach(o -> {
			if(o != null) {
				Map<String, Object> pm = JSONObject.parseObject(o.toString(), Map.class);
				redisProcessMap.put(String.valueOf(pm.get(DamConstants.MaterialColumn.JOB_MINCE_ID)),
						pm.get(DamConstants.MaterialColumn.JOB_MINCE_PROCESS));
			}
		});
		
		//子任务进度Map
		Map<String, Object> processMap = new HashMap<>();
		
		//循环构建子任务进度Map
		minceJobList.forEach(minceJob -> {
			String checkType = minceJob.getCheckType();
			
			//缓存中的当前任务进度
			Object process = redisProcessMap.get(minceJob.getId());
			
			//签章检测
			if (CheckTypeEnum.SEAL.getCode().equals(checkType)){
                processMap.put(DamConstants.MaterialColumn.SEAL_PROCESS, process);
            }
			
			//签名检测
			else if (CheckTypeEnum.SIGNATURE.getCode().equals(checkType)){
                processMap.put(DamConstants.MaterialColumn.SIGNATURE_PROCESS, process);
            }
			
			//捺印检测
			else if (CheckTypeEnum.FINGERPRINT.getCode().equals(checkType)){
                processMap.put(DamConstants.MaterialColumn.FINGERPRINT_PROCESS, process);
            }
			
			//空白页检测
			else if (CheckTypeEnum.BLANK_SPACE.getCode().equals(checkType)){
                processMap.put(DamConstants.MaterialColumn.BLANK_PROCESS, process);
            }
			
			//重复页检测
			else if (CheckTypeEnum.REPEAT_PAGE.getCode().equals(checkType)){
                processMap.put(DamConstants.MaterialColumn.REPEAT_PROCESS, process);
            }
			
			//瑕疵页检测
			else if (CheckTypeEnum.FLAW.getCode().equals(checkType)){
                processMap.put(DamConstants.MaterialColumn.FLAW_PROCESS, process);
            }
			
			//文书填写规范检测
			else if (CheckTypeEnum.INFO_EXTRACT.getCode().equals(checkType)){
                processMap.put(DamConstants.MaterialColumn.EXTRACT_PROCESS, process);
            }
		});
		
		return processMap;
	}
	
	/**
	 * 更新任务状态并判断是否需要进行自动编目
	 * @param checkJob FormatCheckJobDO 检测任务
	 * @param minceJobList List<FormatMinceJobDO> 检测子任务 
	 */
	public void updateFormatCheckJobStatusAndCatalog(FormatCheckJobDO checkJob, List<FormatMinceJobDO> minceJobList) {
		String jgrybm = checkJob.getJgrybm();
		
		//任务是否全部完成
		boolean finished = false;
		
		//任务是否全部取消
		boolean allCanceled = false;
		
		//任务是否全部完成
		boolean allSuccessed = false;
		
		//处理中的任务
		List<FormatMinceJobDO> processingJobs = minceJobList.stream().filter(minceJob ->
				DamConstants.JOB_STATE_NO.equals(minceJob.getJobStatus()) || DamConstants.JOB_STATE_PENDING.equals(minceJob.getJobStatus()))
				.collect(Collectors.toList());
		
		//所有任务处理完成
		if(CollectionUtil.isNull(processingJobs)){
			finished = true;
			
			//取消的任务数量
			long cancelCount = minceJobList.stream().filter(minceJob -> DamConstants.JOB_STATE_CANCEL.equals(minceJob.getJobStatus())).count();
			
			//所有任务均被取消
			if(cancelCount == minceJobList.size()) {
				allCanceled = true;
				log.info("{}, {}的任务全部取消", jgrybm, checkJob.getId());
			}
			
			//成功的任务数量
			long successCount = minceJobList.stream().filter(minceJob -> DamConstants.JOB_STATE_YES.equals(minceJob.getJobStatus())).count();
			
			//所有任务均被取消
			if(successCount == minceJobList.size()) {
				allSuccessed = true;
			}
		}
		
		//所有任务完成
		if(finished) {
			
			//任务完成时清除所有缓存
			clearFormatCheckCacheWhenFinish(checkJob, minceJobList);
			
			//更新检测任务
			checkJob.setStatus(DamConstants.JOB_STATE_YES);
			checkJob.setEndTime(new Date());
			
			//更新检测子任务
			if(CollectionUtil.isNotNull(minceJobList)) {
				dmsFormatMinceJobService.updateMinceJobEndTime(checkJob.getId());
			}
			
			//检测任务完成, 如果是未编目页面发起的检测任务,且任务没有被全部取消,则自动进行编目组卷
            if (DamConstants.FORMAT_CHECK_UN_BM.equals(checkJob.getJobType()) && !allCanceled && allSuccessed){
            	startZnbm(jgrybm, checkJob.getAddUser(), checkJob.getOrgCode(), null);
            	log.info("{} 的检测任务完成,开始自动编目", jgrybm);
            }
		}
		
		//更新任务状态
		dmsFormatCheckJobService.updateById(checkJob);
	}
	
	/**
	 * 启动快速组卷智能编目
	 * @param jgrybm String 监管人员编码
	 * @param idCard String 身份证号码
	 * @param orgCode String 机构代码
	 * @return CommonResult
	 */
	public CommonResult<?> startKszjZnbm(String jgrybm, String idCard, String orgCode) {		
		try {
			//构建快速组卷待编目材料查询Wrapper
			QueryWrapper<MaterialInfoDO> wrapper = WrapUtil.eq(MaterialInfoDO.class, new String[]{"jgrybm", "type", "name"},
					new String[]{jgrybm, "3", "未识别"}).orderByAsc("xh");
			
			//获取数据库中待编目材料
			List<MaterialInfoDO> materialList = dossierMaterialInfoService.list(wrapper);
			if(CollectionUtil.isNull(materialList)) {
				return CommonResult.error("快速组卷案件无需要识别的材料");
			}
			
			//待编目的材料
			List<MaterialInfoDO> needMaterialList = new ArrayList<>();
			
			//是否有新增的待编目的材料（依据缓存判断---key : "ocr:start:materId:{materialId}"）
			int count = 0;
			for(MaterialInfoDO material : materialList) {
				String materialKey = String.format(RedisConstants.START_OCR_MATER_ID, material.getId());
				if(!redisTemplateUtil.exists(materialKey)) {
					count ++;
					needMaterialList.add(material);
				}
			}
			
			//没有待编目材料时返回
			if(count == 0) {
				return CommonResult.error("材料正在检测中,快速组卷没有新的材料需要发起识别");
			}
			
			//快速组卷智能编目
			OcrStrategy ocrStrategy = OcrStrategyFactory.getOcrStrategy(OcrUtil.getOcrType());
			ocrStrategy.znbm(needMaterialList, jgrybm, idCard, orgCode);
			
			//获取ocr识别中的材料文件数量
			int ocringCount = 0;
			for(MaterialInfoDO material : needMaterialList) {
				JSONArray arr = JSON.parseArray(material.getMaterialFile());
				ocringCount = ocringCount + arr.size();
			}
			
			//缓存案件智能编目的材料文件数量（缓存key："ocr:job:finish:num:jgrybm:{jgrybm}"）
			String finishMark = String.format(RedisConstants.TASK_JOB_FINISH_NUM, jgrybm);
			redisTemplateUtil.set(finishMark, String.valueOf(ocringCount));
			
			return CommonResult.success(JSONUtil.createObj().set("total", ocringCount),
					"OCR处理-快速组卷智能编目启动成功");
		}
		catch(Exception e) {
			log.error("ocr处理-快速组卷智能编目异常：{}", e);
			return CommonResult.error(e.getMessage());
		}
	}
	
	/**
	 * 执行ocr接口调用
	 * @param dmsConfigGroup ConfigGroupDO 卷宗配置
	 * @param serviceCode String 服务代码
	 * @param serviceRequestParams JSONObject 服务调用参数
	 * @param dmsIspInvoking IspInvokingDO isp调用对象
	 * @return JSONObject
	 */
	public JSONObject executeOcrInvoke(ConfigGroupDO dmsConfigGroup, String serviceCode,
			JSONObject serviceRequestParams, IspInvokingDO dmsIspInvoking) {
		
		//调用isp服务返回结果
        JSONObject result = null;
		
		try {
			//构建isp接口请求参数
	        JSONObject ispParams = GoUtil.buildIspParams(dmsConfigGroup, serviceCode, serviceRequestParams);
	        
	        //设置isp调用对象
	        dmsIspInvoking.setId(StringUtil.getGuid32());
	        dmsIspInvoking.setInput(ispParams.toJSONString());
        	dmsIspInvoking.setSerCode(serviceCode);
	        
        	//调用isp服务接口
        	long start = System.currentTimeMillis();
        	String invokeUrl = dmsConfigGroup.getOcrServer() + IspV2ConfigUtil.getCommonUri();
        	result = RestTemplateUtil.postWithJson(invokeUrl, ispParams);
        	
        	log.info("OCR处理-调用接口结束! jgrybm:{}, serviceCode: {}, 调用结果:{}", dmsIspInvoking.getJgrybm(),
        			dmsIspInvoking.getSerCode(), result);
        	
        	//更新isp调用对象
        	dmsIspInvoking.setOutput(result.toJSONString());
            dmsIspInvoking.setRequestTime(System.currentTimeMillis() - start);
        }
        catch(Exception e) {
        	log.error("OCR处理-调用接口失败! jgrybm:{}, serviceCode: {}, 异常信息：{}", dmsIspInvoking.getJgrybm(),
        			dmsIspInvoking.getSerCode(), e.getMessage());
        	
        	//更新isp调用对象
        	dmsIspInvoking.setOutput(e.getMessage());
            dmsIspInvoking.setRequestStatus("2");
        }
		
		try {
        	//插入isp调用记录
        	dmsIspInvoking.setAddTime(new Date());
            dmsIspInvokingService.save(dmsIspInvoking);
		}
        catch (Exception e) {
        	log.error("OCR处理-调用接口后插入isp调用记录失败! jgrybm:{}, serviceCode: {}, 异常信息：{}",
        			dmsIspInvoking.getJgrybm(), dmsIspInvoking.getSerCode(), e.getMessage());
		}
		
		return result;
	}
	
	/**
	 * 重新发起格式化审查
	 * @param id String 检测子任务id
	 * @return CheckFormatEnum
	 */
	public CheckFormatEnum reFormatCheck(String id) {
		FormatMinceJobDO minceJob = dmsFormatMinceJobService.getById(id);
		String jgrybm = minceJob.getJgrybm();
		String jobType = minceJob.getJobType();
		String checkType = minceJob.getCheckType();
		
		//判断任务是否正在进行自动编目
        String catalogIngKey = String.format(RedisConstants.FORMAT_CHECK_AUTO_CATALOGING_MARK, jgrybm);
        String cataloging = redisTemplateUtil.get(catalogIngKey);
        if (StringUtil.isNotEmpty(cataloging)){
            return CheckFormatEnum.AUTO_CATALOGING;
        }
        
        //判断是否有待检测的材料
        List<MaterialInfoDO> dossierMaterialInfoList = tFormatCheckService.getFormatCheckMaterialInfoList(jgrybm, jobType, null);
        if(CollectionUtil.isNull(dossierMaterialInfoList)) {
        	return CheckFormatEnum.NO_MATERIAL_FILE;
        }
        
        //当前用户
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        
        //获取卷宗配置
        ConfigGroupDO dmsConfigGroup = dmsConfigGroupService.getUserConfigGroup(sessionUser.getOrgCode());
        
        //判断是否开启了OCR服务
        if(!StringUtil.getBoolean(dmsConfigGroup.getOpenOcr())) {
        	return CheckFormatEnum.OCR_SERVER_NOT_OPEN;
        }
        
        //主任务Id
        String jobId = minceJob.getJobId();
        
        //有相同类型的检测任务正在执行中
        String retryKey = String.format(RedisConstants.FORMAT_CHECK_RETRY_MARK, jgrybm, jobType);
        boolean isMember = redisTemplateUtil.sIsMember(retryKey, checkType);
        if(isMember){
            return CheckFormatEnum.SAME_MINCE_JOB_PROCESSING;
        }
        
        //有正在重新检测的任务，并且新点击重新检测的任务不属于同一个主任务
        String retryCheckKey = String.format(RedisConstants.FORMAT_CHECK_RETRY_MASTER_JOB, jgrybm, jobType);
        String retryJobId = redisTemplateUtil.get(retryCheckKey);
        if (StringUtil.isNotEmpty(retryJobId) && !retryJobId.equals(jobId)){
        	return CheckFormatEnum.OTHER_MINCE_JOB_PROCESSING;
        }
        
        //主任务
        FormatCheckJobDO formatCheckJob = dmsFormatCheckJobService.getById(jobId);
        
        //更新检测任务状态
        minceJob.setJobStatus(DamConstants.JOB_STATE_NO);
        formatCheckJob.setStatus(DamConstants.JOB_STATE_NO);
        
        //更新检测任务
        dmsFormatCheckJobService.updateById(formatCheckJob);
        dmsFormatMinceJobService.updateById(minceJob);
        DamConstants.HAVING_FORMAT_CHECK = true;
        
        //设置重新检测的任务类型
        redisTemplateUtil.sAdd(retryKey, checkType);
        redisTemplateUtil.expires(retryKey, 1200);
        
        //重新发起检测的任务id
        redisTemplateUtil.sAdd(RedisConstants.FORMAT_CHECK_RETRY_JOB, jobId);
        
        //设置重新检测的主任务id
        String member = jgrybm + "--" + jobType;
        redisTemplateUtil.sAdd(RedisConstants.FORMAT_CHECKING, member);        
        redisTemplateUtil.lock(retryCheckKey, jobId, 3600);
        
        //添加点击重新检测的子任务id, 定时任务检测完成后,子任务的状态如果是已取消,则不更新该任务在数据库中的状态
        //用于在发起检测后,子任务点击了取消,又继续点击了重新检测的情况
        String recheckKey = String.format(RedisConstants.FORMAT_CHECK_RECHECK_MINCE, jgrybm, jobType);
        redisTemplateUtil.sAdd(recheckKey, id);
        
        //构建检测子任务
    	List<FormatMinceJobDO> minceJobList = Arrays.asList(minceJob);
    	
    	//初始化格式化审查进度
    	initMinceJobProcess(minceJobList);
    	
    	//待检测的文件
    	JSONArray checkFiles = JSONArray.parseArray(minceJob.getCheckFiles());
    	
    	//异步执行-启动格式化审查任务
		ThreadPoolUtil.getPool().execute(new Runnable() {
			
			@Override
			public void run() {
				OcrStrategy ocrStrategy = OcrStrategyFactory.getOcrStrategy(OcrUtil.getOcrType());
				ocrStrategy.formatCheck(dmsConfigGroup, checkFiles, formatCheckJob, minceJobList);
			}
		});
        
		return CheckFormatEnum.START_SUCCESS;
	}
	
	/**
	 * 调用ocr服务
	 * @param serviceCode String 服务代码
	 * @param fileData JSONArray 文件数据
	 * @param returnUrl String 回调地址
	 * @param jobId String 格式化审查任务 
	 * @param jgrybm String 监管人员编码
	 * @param extendParams String 扩展参数
	 * @return CommonResult<?>
	 */
	public CommonResult<?> call(String serviceCode, JSONArray fileData, String returnUrl,
			String jobId, String jgrybm, JSONObject extendParams) {
		
		//当前用户
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
		
		//获取卷宗配置
        ConfigGroupDO dmsConfigGroup = dmsConfigGroupService.getUserConfigGroup(sessionUser.getOrgCode());
        
        //判断是否开启了OCR服务
        if(!StringUtil.getBoolean(dmsConfigGroup.getOpenOcr())) {
        	return CommonResult.error(CheckFormatEnum.OCR_SERVER_NOT_OPEN.getDesc());
        }
        
        //调用服务
		OcrStrategy ocrStrategy = OcrStrategyFactory.getOcrStrategy(OcrUtil.getOcrType());
		JSONObject result = ocrStrategy.call(dmsConfigGroup, serviceCode, fileData, returnUrl, jobId, jgrybm, extendParams);
		
		return CommonResult.success(result);
	}
}
