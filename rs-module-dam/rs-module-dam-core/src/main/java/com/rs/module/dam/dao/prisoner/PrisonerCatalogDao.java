package com.rs.module.dam.dao.prisoner;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.prisoner.PrisonerCatalogDO;
import com.rs.module.dam.vo.prisoner.PrisonerCatalogListReqVO;
import com.rs.module.dam.vo.prisoner.PrisonerCatalogPageReqVO;

/**
* 监管人员卷宗目录 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PrisonerCatalogDao extends IBaseDao<PrisonerCatalogDO> {


    default PageResult<PrisonerCatalogDO> selectPage(PrisonerCatalogPageReqVO reqVO) {
        Page<PrisonerCatalogDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrisonerCatalogDO> wrapper = new LambdaQueryWrapperX<PrisonerCatalogDO>()
            .eqIfPresent(PrisonerCatalogDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PrisonerCatalogDO::getCatalogTemplateId, reqVO.getCatalogTemplateId())
            .likeIfPresent(PrisonerCatalogDO::getIsChangeFileName, reqVO.getIsChangeFileName())
            .eqIfPresent(PrisonerCatalogDO::getBmCatalogData, reqVO.getBmCatalogData())
            .eqIfPresent(PrisonerCatalogDO::getZjCatalogData, reqVO.getZjCatalogData())
            .eqIfPresent(PrisonerCatalogDO::getKszjCatalogData, reqVO.getKszjCatalogData())
            .eqIfPresent(PrisonerCatalogDO::getHzCatalogData, reqVO.getHzCatalogData())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PrisonerCatalogDO::getAddTime);
        }
        Page<PrisonerCatalogDO> prisonerCatalogPage = selectPage(page, wrapper);
        return new PageResult<>(prisonerCatalogPage.getRecords(), prisonerCatalogPage.getTotal());
    }
    default List<PrisonerCatalogDO> selectList(PrisonerCatalogListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrisonerCatalogDO>()
            .eqIfPresent(PrisonerCatalogDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PrisonerCatalogDO::getCatalogTemplateId, reqVO.getCatalogTemplateId())
            .likeIfPresent(PrisonerCatalogDO::getIsChangeFileName, reqVO.getIsChangeFileName())
            .eqIfPresent(PrisonerCatalogDO::getBmCatalogData, reqVO.getBmCatalogData())
            .eqIfPresent(PrisonerCatalogDO::getZjCatalogData, reqVO.getZjCatalogData())
            .eqIfPresent(PrisonerCatalogDO::getKszjCatalogData, reqVO.getKszjCatalogData())
            .eqIfPresent(PrisonerCatalogDO::getHzCatalogData, reqVO.getHzCatalogData())
        .orderByDesc(PrisonerCatalogDO::getAddTime));    }


    }
