package com.rs.module.dam.dao.prisoner;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.prisoner.PrisonerInfoDO;
import com.rs.module.dam.vo.prisoner.PrisonerInfoListReqVO;
import com.rs.module.dam.vo.prisoner.PrisonerInfoPageReqVO;

/**
* 卷宗监管人员 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PrisonerInfoDao extends IBaseDao<PrisonerInfoDO> {


    default PageResult<PrisonerInfoDO> selectPage(PrisonerInfoPageReqVO reqVO) {
        Page<PrisonerInfoDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrisonerInfoDO> wrapper = new LambdaQueryWrapperX<PrisonerInfoDO>()
            .eqIfPresent(PrisonerInfoDO::getJzzt, reqVO.getJzzt())
            .eqIfPresent(PrisonerInfoDO::getDabh, reqVO.getDabh())
            .eqIfPresent(PrisonerInfoDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PrisonerInfoDO::getSfwcnrda, reqVO.getSfwcnrda())
            .eqIfPresent(PrisonerInfoDO::getAjbh, reqVO.getAjbh())
            .eqIfPresent(PrisonerInfoDO::getAjmc, reqVO.getAjmc())
            .eqIfPresent(PrisonerInfoDO::getJycs, reqVO.getJycs())
            .eqIfPresent(PrisonerInfoDO::getGdsj, reqVO.getGdsj())
            .eqIfPresent(PrisonerInfoDO::getGdUser, reqVO.getGdUser())
            .likeIfPresent(PrisonerInfoDO::getGdUserName, reqVO.getGdUserName())
            .eqIfPresent(PrisonerInfoDO::getGdOrgCode, reqVO.getGdOrgCode())
            .likeIfPresent(PrisonerInfoDO::getGdOrgName, reqVO.getGdOrgName())
            .eqIfPresent(PrisonerInfoDO::getZjsj, reqVO.getZjsj())
            .eqIfPresent(PrisonerInfoDO::getZjUser, reqVO.getZjUser())
            .likeIfPresent(PrisonerInfoDO::getZjUserName, reqVO.getZjUserName())
            .eqIfPresent(PrisonerInfoDO::getZjOrgCode, reqVO.getZjOrgCode())
            .likeIfPresent(PrisonerInfoDO::getZjOrgName, reqVO.getZjOrgName())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PrisonerInfoDO::getAddTime);
        }
        Page<PrisonerInfoDO> prisonerInfoPage = selectPage(page, wrapper);
        return new PageResult<>(prisonerInfoPage.getRecords(), prisonerInfoPage.getTotal());
    }
    default List<PrisonerInfoDO> selectList(PrisonerInfoListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrisonerInfoDO>()
            .eqIfPresent(PrisonerInfoDO::getJzzt, reqVO.getJzzt())
            .eqIfPresent(PrisonerInfoDO::getDabh, reqVO.getDabh())
            .eqIfPresent(PrisonerInfoDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PrisonerInfoDO::getSfwcnrda, reqVO.getSfwcnrda())
            .eqIfPresent(PrisonerInfoDO::getAjbh, reqVO.getAjbh())
            .eqIfPresent(PrisonerInfoDO::getAjmc, reqVO.getAjmc())
            .eqIfPresent(PrisonerInfoDO::getJycs, reqVO.getJycs())
            .eqIfPresent(PrisonerInfoDO::getGdsj, reqVO.getGdsj())
            .eqIfPresent(PrisonerInfoDO::getGdUser, reqVO.getGdUser())
            .likeIfPresent(PrisonerInfoDO::getGdUserName, reqVO.getGdUserName())
            .eqIfPresent(PrisonerInfoDO::getGdOrgCode, reqVO.getGdOrgCode())
            .likeIfPresent(PrisonerInfoDO::getGdOrgName, reqVO.getGdOrgName())
            .eqIfPresent(PrisonerInfoDO::getZjsj, reqVO.getZjsj())
            .eqIfPresent(PrisonerInfoDO::getZjUser, reqVO.getZjUser())
            .likeIfPresent(PrisonerInfoDO::getZjUserName, reqVO.getZjUserName())
            .eqIfPresent(PrisonerInfoDO::getZjOrgCode, reqVO.getZjOrgCode())
            .likeIfPresent(PrisonerInfoDO::getZjOrgName, reqVO.getZjOrgName())
        .orderByDesc(PrisonerInfoDO::getAddTime));    }


    }
