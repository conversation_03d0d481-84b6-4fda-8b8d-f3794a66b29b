package com.rs.module.dam.dao.archive;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.archive.ArchiveJobDO;
import com.rs.module.dam.vo.archive.ArchiveJobListReqVO;
import com.rs.module.dam.vo.archive.ArchiveJobPageReqVO;

/**
* 卷宗归档任务 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ArchiveJobDao extends IBaseDao<ArchiveJobDO> {


    default PageResult<ArchiveJobDO> selectPage(ArchiveJobPageReqVO reqVO) {
        Page<ArchiveJobDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ArchiveJobDO> wrapper = new LambdaQueryWrapperX<ArchiveJobDO>()
            .eqIfPresent(ArchiveJobDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(ArchiveJobDO::getMaterialId, reqVO.getMaterialId())
            .eqIfPresent(ArchiveJobDO::getFileContent, reqVO.getFileContent())
            .eqIfPresent(ArchiveJobDO::getState, reqVO.getState())
            .eqIfPresent(ArchiveJobDO::getPartId, reqVO.getPartId())
            .betweenIfPresent(ArchiveJobDO::getStartTime, reqVO.getStartTime())
            .betweenIfPresent(ArchiveJobDO::getEndTime, reqVO.getEndTime())
            .eqIfPresent(ArchiveJobDO::getType, reqVO.getType())
            .eqIfPresent(ArchiveJobDO::getRequestCount, reqVO.getRequestCount())
            .eqIfPresent(ArchiveJobDO::getBatchId, reqVO.getBatchId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(ArchiveJobDO::getAddTime);
        }
        Page<ArchiveJobDO> archiveJobPage = selectPage(page, wrapper);
        return new PageResult<>(archiveJobPage.getRecords(), archiveJobPage.getTotal());
    }
    default List<ArchiveJobDO> selectList(ArchiveJobListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ArchiveJobDO>()
            .eqIfPresent(ArchiveJobDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(ArchiveJobDO::getMaterialId, reqVO.getMaterialId())
            .eqIfPresent(ArchiveJobDO::getFileContent, reqVO.getFileContent())
            .eqIfPresent(ArchiveJobDO::getState, reqVO.getState())
            .eqIfPresent(ArchiveJobDO::getPartId, reqVO.getPartId())
            .betweenIfPresent(ArchiveJobDO::getStartTime, reqVO.getStartTime())
            .betweenIfPresent(ArchiveJobDO::getEndTime, reqVO.getEndTime())
            .eqIfPresent(ArchiveJobDO::getType, reqVO.getType())
            .eqIfPresent(ArchiveJobDO::getRequestCount, reqVO.getRequestCount())
            .eqIfPresent(ArchiveJobDO::getBatchId, reqVO.getBatchId())
        .orderByDesc(ArchiveJobDO::getAddTime));    }


    }
