package com.rs.module.dam.service.prisoner;

import java.util.List;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.prisoner.PrisonerCatalogDO;
import com.rs.module.dam.vo.prisoner.PrisonerCatalogListReqVO;
import com.rs.module.dam.vo.prisoner.PrisonerCatalogPageReqVO;
import com.rs.module.dam.vo.prisoner.PrisonerCatalogSaveReqVO;

/**
 * 监管人员卷宗目录 Service 接口
 *
 * <AUTHOR>
 */
public interface PrisonerCatalogService extends IBaseService<PrisonerCatalogDO>{

    /**
     * 创建监管人员卷宗目录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrisonerCatalog(@Valid PrisonerCatalogSaveReqVO createReqVO);

    /**
     * 更新监管人员卷宗目录
     *
     * @param updateReqVO 更新信息
     */
    void updatePrisonerCatalog(@Valid PrisonerCatalogSaveReqVO updateReqVO);

    /**
     * 删除监管人员卷宗目录
     *
     * @param id 编号
     */
    void deletePrisonerCatalog(String id);

    /**
     * 获得监管人员卷宗目录
     *
     * @param id 编号
     * @return 监管人员卷宗目录
     */
    PrisonerCatalogDO getPrisonerCatalog(String id);

    /**
    * 获得监管人员卷宗目录分页
    *
    * @param pageReqVO 分页查询
    * @return 监管人员卷宗目录分页
    */
    PageResult<PrisonerCatalogDO> getPrisonerCatalogPage(PrisonerCatalogPageReqVO pageReqVO);

    /**
    * 获得监管人员卷宗目录列表
    *
    * @param listReqVO 查询条件
    * @return 监管人员卷宗目录列表
    */
    List<PrisonerCatalogDO> getPrisonerCatalogList(PrisonerCatalogListReqVO listReqVO);


}
