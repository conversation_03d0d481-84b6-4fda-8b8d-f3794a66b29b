package com.rs.module.dam.ocr.gosuncn.callback;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.rs.module.dam.constant.CheckTypeEnum;
import com.rs.module.dam.constant.DamConstants;
import com.rs.module.dam.constant.OcrConstants;
import com.rs.module.dam.entity.log.IspInvokingDO;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.entity.ocr.FormatCheckJobDO;
import com.rs.module.dam.entity.ocr.FormatCheckResultDO;
import com.rs.module.dam.entity.ocr.FormatMinceJobDO;
import com.rs.module.dam.entity.prisoner.PrisonerCatalogDO;
import com.rs.module.dam.entity.sys.ConfigGroupDO;
import com.rs.module.dam.ocr.gosuncn.GoOcrCallbackParam;
import com.rs.module.dam.ocr.gosuncn.GoOcrCallbackParam.SerDatas;
import com.rs.module.dam.ocr.gosuncn.GoOcrHttpStatus;
import com.rs.module.dam.ocr.gosuncn.GoUtil;
import com.rs.module.dam.ocr.handler.OcrHandler;
import com.rs.module.dam.ocr.util.OcrUtil;
import com.rs.module.dam.service.log.IspInvokingService;
import com.rs.module.dam.service.material.MaterialInfoService;
import com.rs.module.dam.service.ocr.FormatCheckJobService;
import com.rs.module.dam.service.ocr.FormatCheckResultService;
import com.rs.module.dam.service.ocr.FormatMinceJobService;
import com.rs.module.dam.service.ocr.TFormatCheckService;
import com.rs.module.dam.service.sys.ConfigGroupService;
import com.rs.module.dam.util.IpUtil;
import com.rs.module.dam.util.RedisTemplateUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 文书信息提取回调接口
 * <AUTHOR>
 * @date 2025年4月27日
 */
@Component
@Slf4j
public class WsxxtqCallback implements IOcrCallback {
	
	@Autowired
    private ConfigGroupService configGroupService;
	
	@Autowired
	private IspInvokingService ispInvokingService;
	
	@Autowired
	private MaterialInfoService dossierMaterialInfoService;
	
	@Autowired
	private FormatCheckJobService checkJobService;
	
	@Autowired
	private FormatMinceJobService minceJobService;
	
	@Autowired
	private FormatCheckResultService checkResultService;
	
	@Autowired
	private TFormatCheckService tFormatCheckService;
    
    @Autowired
    OcrHandler ocrHandler;
    
    @Autowired
    RedisTemplateUtil redisTemplateUtil;
    
	/**
	 * 回调处理
	 * @param params GoOcrCallbackParam 回调参数
	 */
	@Override
	public void handle(GoOcrCallbackParam params) {
		
		//服务返回数据
		SerDatas serDatas = params.getSerDatas();
		
		//构建isp调用对象
		IspInvokingDO ispInvoking = new IspInvokingDO();
		ispInvoking.setId(StringUtil.getGuid32());
		ispInvoking.setJobId(serDatas.getBatchId());
		ispInvoking.setInput(JSON.toJSONString(params));
		ispInvoking.setSerCode(params.getSerCode());
		ispInvoking.setInvokeType(OcrConstants.OCR_INVOKE_TYPE_WSXXTQ_CALLBACK);
		ispInvoking.setRequestFrom(OcrConstants.OCR_REQUEST_FROM_FORMAT_CHECK);
		ispInvoking.setServerIp(IpUtil.getHostIp());
		ispInvoking.setCheckType(OcrConstants.OCR_INVOKE_TYPE_WSXXTQ_CALLBACK);
		
		//格式化审查任务
		FormatCheckJobDO checkJob = null;
		
		//所有检测子任务
		List<FormatMinceJobDO> allMinceJobList = null;
		
		//文书规范检测子任务
		List<FormatMinceJobDO> wsxxtqMinceJobList = new ArrayList<>();
		
		//任务是否完成
		boolean isFinished = false;
		
		//开始调用时间
		long begin = System.currentTimeMillis();
		
		try {
			//回调返回结果
			JSONArray callbackResults = GoUtil.getOcrResultsArray(serDatas);
			
			//任务Id = 批次Id
			String jobId = serDatas.getBatchId();
			
			//格式化审查任务
			checkJob = checkJobService.getById(jobId);
			
			//设置isp调用对象
			ispInvoking.setJgrybm(checkJob.getJgrybm());
			
			//获取所有的检测子任务
			allMinceJobList = tFormatCheckService.getCheckedMinceJobList(checkJob.getId());
			
			//返回数据合法
			if((!callbackResults.isEmpty() || !serDatas.getFailed_ids().isEmpty())
					&& (GoOcrHttpStatus.OK.getCode() == serDatas.getCode() || GoOcrHttpStatus.INCOMPLETE_RETURN.getCode() == serDatas.getCode())) {
				
				//查询卷宗配置信息
				ConfigGroupDO dmsConfigGroup = configGroupService.getUserConfigGroup(checkJob.getOrgCode());
				
				//设置isp调用对象
				ispInvoking.setAddress(dmsConfigGroup.getOcrServer());
				
				//文书规范检测任务
				FormatMinceJobDO wsxxtqJob = OcrUtil.getMinceJobByCheckType(allMinceJobList, CheckTypeEnum.INFO_EXTRACT.getCode());
				
				//处理取消检测的子任务
				ocrHandler.updateCancelFormatJob(jobId, wsxxtqJob);
				
				//获取格式化审查关联的材料
				List<MaterialInfoDO> dossierMaterialInfoList = tFormatCheckService.getFormatCheckMaterialInfoList(checkJob.getJgrybm(), checkJob.getJobType());
								
				//没有可以审查的材料
				if(!tFormatCheckService.validateFormatCheckMaterials(checkJob, dossierMaterialInfoList)) {
					return;
				}
				
				//获取案件编目信息
				PrisonerCatalogDO ajCatalog = tFormatCheckService.getPrisonerCatalog(checkJob.getJgrybm());
				
				//检测结果
				List<FormatCheckResultDO> checkResultList = new ArrayList<>();
				
				//需更新的材料
				Map<String, MaterialInfoDO> needUpdateMaterialInfoMap = new HashMap<>();
				
				//检测结果插入时间
				Date addTime = new Date();
				
				//失败的文件数量
				int failedIdsNum = 0;
				if(GoOcrHttpStatus.INCOMPLETE_RETURN.getCode() == serDatas.getCode()){
					failedIdsNum = serDatas.getFailed_ids().size();
				}
				
				//文书规范检测
				if(null != wsxxtqJob) {
					if (DamConstants.JOB_STATE_NO.equals(wsxxtqJob.getJobStatus())){
						wsxxtqMinceJobList.add(wsxxtqJob);
						
						//已经检测过的文件Id集合
						List<String> checkedFileIdList = tFormatCheckService.getPrisonerCheckResultFileIdList(checkJob.getJgrybm(), checkJob.getJobType(), CheckTypeEnum.INFO_EXTRACT.getCode());
						
						//循环处理检测结果
						for(int i = 0; i < callbackResults.size(); i ++) {
							JSONObject file = callbackResults.getJSONObject(i);
							
							String fileId = file.getString("fileId");
							
							//是否找到不规范的材料
							boolean isExist = false;
							
							for(MaterialInfoDO dossierMaterialInfo : dossierMaterialInfoList) {
								JSONArray materialFile = JSONArray.parseArray(dossierMaterialInfo.getMaterialFile());
								for(int j = 0; j < materialFile.size(); j ++) {
									JSONObject material = materialFile.getJSONObject(j);
									String id = material.getString("id");
									if(id.equals(fileId)) {
										isExist = true;
										
										//材料已经检测
										if(checkedFileIdList.contains(id)) {
											break;
										}
										
										//检测提示信息
										List<String> loseData = new ArrayList<>();
										
										//文书号检测
										GoUtil.checkWsNodeInfo(file, "document", (t) -> {
											loseData.add("缺失文书号信息");
											material.put(DamConstants.MaterialColumn.LOSE_DOC_CODE, "1");
										});
										
										//机构检测
										GoUtil.checkWsNodeInfo(file, "institution", (t) -> {
											loseData.add("缺失案件单位信息");
											material.put(DamConstants.MaterialColumn.LOSE_AJDW, "1");
										});
										
										//检测不通过
										if(CollectionUtil.isNotNull(loseData)) {
											material.put(DamConstants.MaterialColumn.INFO_PASS, "1");
											
											//创建检测结果对象
											FormatCheckResultDO checkResult = OcrUtil.buildFormatCheckResult(checkJob, ajCatalog,
													dossierMaterialInfo, material, CheckTypeEnum.INFO_EXTRACT.getCode(), addTime);
											
											//补充结果信息
											checkResult.setFileInfo(file.toJSONString());
											checkResult.setLoseInfo(String.join("," , loseData));
											
											//添加检测结果
											checkResultList.add(checkResult);
											
											//更新材料文件
											dossierMaterialInfo.setMaterialFile(materialFile.toJSONString());
											
											//添加到待更新材料中
											if(!needUpdateMaterialInfoMap.containsKey(dossierMaterialInfo.getId())){
												needUpdateMaterialInfoMap.put(dossierMaterialInfo.getId(), dossierMaterialInfo);
											}
										}
										
										//找到材料直接退出当前循环
										break;
									}
								}
								
								//已经找到不规范材料退出循环
								if(isExist) {
									break;
								}
							}
						}
						
						//更新文书规范检测子任务进度
						isFinished = ocrHandler.getAndUpdateFormactCheckMinceJobProcess(wsxxtqJob, callbackResults.size() + failedIdsNum);
					}
				}
				
				//插入检测结果
				if(CollectionUtil.isNotNull(checkResultList)) {
					checkResultService.saveBatch(checkResultList);
				}
				
				//根据检测结果更新材料
				if(CollectionUtil.isNotNull(needUpdateMaterialInfoMap)) {
					List<MaterialInfoDO> needUpdateMaterialInfoList = new ArrayList<>(needUpdateMaterialInfoMap.values());
					dossierMaterialInfoService.updateBatchById(needUpdateMaterialInfoList);
				}
				
				//返回前端检测进度
				ocrHandler.sendFormatCheckProcessResult(checkJob);
				
				//更新任务状态
				if(isFinished) {
					OcrUtil.updateFormatCheckJobStatus(checkJob, wsxxtqMinceJobList, DamConstants.JOB_STATE_YES);
				}
				
				//更新isp调用对象
				ispInvoking.setOutput("文书规范检测回调成功");
			}
			else {
				ispInvoking.setOutput("文书规范检测回调异常：参数不正确");
				ispInvoking.setRequestStatus("2");
				log.error("ocr处理-格式化审查-文书规范检测回调异常：参数不正确");
				
				//任务是否完成
//				isFinished = serDatas.getProgress() >= 1;
				isFinished = true;
				
				//更新任务状态
				if(isFinished) {
					OcrUtil.updateFormatCheckJobStatus(checkJob, wsxxtqMinceJobList, DamConstants.JOB_STATE_ERROR);
				}
			}
		}
		catch (Exception e) {
			e.printStackTrace();
			ispInvoking.setOutput(e.toString());
			ispInvoking.setRequestStatus("2");
			log.error("ocr处理-格式化审查-文书规范检测回调异常：{}", e);
			
			//任务是否完成
//			isFinished = serDatas.getProgress() >= 1;
			isFinished = true;
			
			//更新任务状态
			if(isFinished) {
				OcrUtil.updateFormatCheckJobStatus(checkJob, wsxxtqMinceJobList, DamConstants.JOB_STATE_ERROR);
			}
		}
		finally {
			if(isFinished && CollectionUtil.isNotNull(wsxxtqMinceJobList)) {
				
				//更新子任务
				minceJobService.updateBatchById(wsxxtqMinceJobList);
				
				//更新主任务状态并判断是否需要进行自动编目
				ocrHandler.updateFormatCheckJobStatusAndCatalog(checkJob, allMinceJobList);
			}
			
			//插入isp调用对象
			ispInvoking.setRequestTime(System.currentTimeMillis() - begin);
			ispInvoking.setAddTime(new Date());
			ispInvokingService.save(ispInvoking);			
		}
	}
}
