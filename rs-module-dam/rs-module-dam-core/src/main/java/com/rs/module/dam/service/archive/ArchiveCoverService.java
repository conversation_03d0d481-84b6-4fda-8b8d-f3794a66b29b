package com.rs.module.dam.service.archive;

import java.util.List;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.archive.ArchiveCoverDO;
import com.rs.module.dam.vo.archive.ArchiveCoverListReqVO;
import com.rs.module.dam.vo.archive.ArchiveCoverPageReqVO;
import com.rs.module.dam.vo.archive.ArchiveCoverSaveReqVO;

/**
 * 卷宗封面 Service 接口
 *
 * <AUTHOR>
 */
public interface ArchiveCoverService extends IBaseService<ArchiveCoverDO>{

    /**
     * 创建卷宗封面
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createArchiveCover(@Valid ArchiveCoverSaveReqVO createReqVO);

    /**
     * 更新卷宗封面
     *
     * @param updateReqVO 更新信息
     */
    void updateArchiveCover(@Valid ArchiveCoverSaveReqVO updateReqVO);

    /**
     * 删除卷宗封面
     *
     * @param id 编号
     */
    void deleteArchiveCover(String id);

    /**
     * 获得卷宗封面
     *
     * @param id 编号
     * @return 卷宗封面
     */
    ArchiveCoverDO getArchiveCover(String id);

    /**
    * 获得卷宗封面分页
    *
    * @param pageReqVO 分页查询
    * @return 卷宗封面分页
    */
    PageResult<ArchiveCoverDO> getArchiveCoverPage(ArchiveCoverPageReqVO pageReqVO);

    /**
    * 获得卷宗封面列表
    *
    * @param listReqVO 查询条件
    * @return 卷宗封面列表
    */
    List<ArchiveCoverDO> getArchiveCoverList(ArchiveCoverListReqVO listReqVO);


}
