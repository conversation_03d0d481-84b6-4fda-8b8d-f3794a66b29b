package com.rs.module.dam.vo.material;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 卷宗材料图片 Response VO")
@Data
@EqualsAndHashCode(callSuper = false)
public class MaterialImageRespVO extends BaseVO implements TransPojo{
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("图片关联的材料Id(dam_material_info表主键)")
    private String materialId;
    @ApiModelProperty("图片序号")
    private Integer xh;
    @ApiModelProperty("图片名称")
    private String fileName;
    @ApiModelProperty("图片url")
    private String url;
    @ApiModelProperty("文书重复页检测-是否重复,1:重复")
    private String isRepeat;
    @ApiModelProperty("重复页分组标记")
    private String repeatMark;
    @ApiModelProperty("缺失签章个数")
    private String missSeal;
    @ApiModelProperty("图片方向合规性检测 (瑕疵页), 1—合规，0—不合规")
    private String isRegular;
    @ApiModelProperty("缺失签名个数")
    private String missSignature;
    @ApiModelProperty("文书空白页检测-是否空白页 1:是")
    private String blankSpace;
    @ApiModelProperty("文书规范性检测是否通过, 1:通过, 0:不通过")
    private String infoPass;
    @ApiModelProperty("缺失捺印个数")
    private String missFingerprint;
    @ApiModelProperty("材料无法识别")
    private String unidentify;
}
