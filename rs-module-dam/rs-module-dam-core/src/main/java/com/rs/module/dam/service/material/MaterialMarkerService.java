package com.rs.module.dam.service.material;

import java.util.List;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.material.MaterialMarkerDO;
import com.rs.module.dam.vo.material.MaterialMarkerListReqVO;
import com.rs.module.dam.vo.material.MaterialMarkerPageReqVO;
import com.rs.module.dam.vo.material.MaterialMarkerSaveReqVO;

/**
 * 卷宗材料标注 Service 接口
 *
 * <AUTHOR>
 */
public interface MaterialMarkerService extends IBaseService<MaterialMarkerDO>{

    /**
     * 创建卷宗材料标注
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMaterialMarker(@Valid MaterialMarkerSaveReqVO createReqVO);

    /**
     * 更新卷宗材料标注
     *
     * @param updateReqVO 更新信息
     */
    void updateMaterialMarker(@Valid MaterialMarkerSaveReqVO updateReqVO);

    /**
     * 删除卷宗材料标注
     *
     * @param id 编号
     */
    void deleteMaterialMarker(String id);

    /**
     * 获得卷宗材料标注
     *
     * @param id 编号
     * @return 卷宗材料标注
     */
    MaterialMarkerDO getMaterialMarker(String id);

    /**
    * 获得卷宗材料标注分页
    *
    * @param pageReqVO 分页查询
    * @return 卷宗材料标注分页
    */
    PageResult<MaterialMarkerDO> getMaterialMarkerPage(MaterialMarkerPageReqVO pageReqVO);

    /**
    * 获得卷宗材料标注列表
    *
    * @param listReqVO 查询条件
    * @return 卷宗材料标注列表
    */
    List<MaterialMarkerDO> getMaterialMarkerList(MaterialMarkerListReqVO listReqVO);

    /**
     * 获取监管人员标注过的材料文件Id 
     * @param jgrybm String 监管人员编码
     * @return List<String>
     */
    public List<String> selectMarkerFileIdByJgrybm(String jgrybm);
}
