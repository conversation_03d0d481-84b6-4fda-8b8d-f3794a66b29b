package com.rs.module.dam.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 格式化审查枚举类
 * <AUTHOR>
 * @date 2025年4月26日
 */
@Getter
@AllArgsConstructor
public enum CheckFormatEnum{

    START_SUCCESS("2000", "开启检测成功"),
    
    PROCESS_CONTINUE("2001", "线程继续进行"),
    
    NO_MATERIAL_FILE("2002", "没有需要检测的文书"),
    
    NO_OPTION_CHOOSE("2003", "没有选择检测项"),
    
    REQUEST_BASE_OCR_FAIL("2004", "启用通用ocr基础服务失败"),
    
    BASE_OCR_RESPONSE_ERROR("2005", "通用ocr基础服务返回错误"),
    
    CHECK_TYPE_NOT_SUPPORT("2006", "审查类型不支持"),
    
    CHECK_FORMAT_PROCESSING("2007", "格式化审查正在进行中,不能重复发起"),
    
    OCR_SERVER_NOT_OPEN("2008","未开启ocr服务检测"),
    
    OTHER_MINCE_JOB_PROCESSING("2009", "该案件的格式化审查任务有其他检测任务正在进行中,暂时不能发起新的检测任务"),
    
    NOT_NEW_FILE_CHECK("2010", "没有新的材料需要发起检测"),
    
    SAME_MINCE_JOB_PROCESSING("2011", "有相同类型的检测任务正在执行中"),
    
    RETRY_MINCE_JOB_PROCESSING("2012","有正在进行重新检测的任务,暂不能发起新的检测任务"),
    
    AUTO_CATALOGING("2013", "案件已完成智能格式化审查,正在进行自动编目中,暂不能发起新的检测任务");

	//代码
    private String code;
    
    //描述
    private String desc;
}
