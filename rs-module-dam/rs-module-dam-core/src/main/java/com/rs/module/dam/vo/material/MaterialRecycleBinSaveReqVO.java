package com.rs.module.dam.vo.material;
import javax.validation.constraints.NotEmpty;

import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 卷宗材料回收新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = false)
public class MaterialRecycleBinSaveReqVO extends BaseVO{
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("图片关联的材料Id(dam_material_info表主键)")
    @NotEmpty(message = "图片关联的材料Id(dam_material_info表主键)不能为空")
    private String materialId;

    @ApiModelProperty("图片Id(dam_material_image表主键)")
    @NotEmpty(message = "图片Id(dam_material_image表主键)不能为空")
    private String imageId;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("材料名称")
    private String name;

    @ApiModelProperty("材料数量")
    private Integer pageCount;

    @ApiModelProperty("模板Id")
    private String templateId;

    @ApiModelProperty("分卷目录Id")
    private String partCatalogId;

    @ApiModelProperty("目录Id")
    private String catalogId;

    @ApiModelProperty("材料序号")
    private Integer xh;

}
