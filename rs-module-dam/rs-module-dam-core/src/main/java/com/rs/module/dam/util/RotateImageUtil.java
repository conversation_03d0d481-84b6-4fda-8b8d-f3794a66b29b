package com.rs.module.dam.util;

/**
 * 图片旋转工具类
 */
import java.awt.Dimension;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.Rectangle;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

import javax.imageio.ImageIO;

/**
 * 
 * @ClassName : RotateImageUtil
 * @Description : 旋转图片工具类
 * <AUTHOR> liyou
 * @date : 2019年1月18日 上午11:49:51
 *
 */
public class RotateImageUtil {
	/**
	 * 
	 * @param bufferedImage
	 *            图片
	 * @param angel
	 *            旋转角度
	 * @return
	 */
	public static BufferedImage rotateImage(BufferedImage bufferedImage, int angel) {
		if (bufferedImage == null) {
			return null;
		}
		if (angel < 0) {
			// 将负数角度，纠正为正数角度
			angel = angel + 360;
		}
		int imageWidth = bufferedImage.getWidth(null);
		int imageHeight = bufferedImage.getHeight(null);
		// 计算重新绘制图片的尺寸
		Rectangle rectangle = calculatorRotatedSize(new Rectangle(new Dimension(imageWidth, imageHeight)), angel);
	    // 获取原始图片的透明度
		int type = bufferedImage.getColorModel().getTransparency();
		BufferedImage newImage = null;
		newImage = new BufferedImage(rectangle.width, rectangle.height, type);
		Graphics2D graphics = newImage.createGraphics();
		// 平移位置
		graphics.translate((rectangle.width - imageWidth) / 2, (rectangle.height - imageHeight) / 2);
		// 旋转角度
		graphics.rotate(Math.toRadians(angel), imageWidth / 2d, imageHeight / 2d);
		// 绘图
		graphics.drawImage(bufferedImage, null, null);
		return newImage;
	}

	/**
	 * 旋转图片
	 * 
	 * @param image
	 *            图片
	 * @param angel
	 *            旋转角度
	 * @return
	 */
	public static BufferedImage rotateImage(Image image, int angel) {
		if (image == null) {
			return null;
		}
		if (angel < 0) {
			// 将负数角度，纠正为正数角度
			angel = angel + 360;
		}
		int imageWidth = image.getWidth(null);
		int imageHeight = image.getHeight(null);
		Rectangle rectangle = calculatorRotatedSize(new Rectangle(new Dimension(imageWidth, imageHeight)), angel);
		BufferedImage newImage = null;
		newImage = new BufferedImage(rectangle.width, rectangle.height, BufferedImage.TYPE_INT_RGB);
		Graphics2D graphics = newImage.createGraphics();
		// transform
		graphics.translate((rectangle.width - imageWidth) / 2, (rectangle.height - imageHeight) / 2);
		graphics.rotate(Math.toRadians(angel), imageWidth / 2d, imageHeight / 2d);
		graphics.drawImage(image, null, null);
		return newImage;
	}

	/**
	 * 计算旋转后的尺寸
	 * 
	 * @param src
	 * @param angel
	 * @return
	 */
	private static Rectangle calculatorRotatedSize(Rectangle src, int angel) {
		if (angel >= 90) {
			if (angel / 90 % 2 == 1) {
				int temp = src.height;
				src.height = src.width;
				src.width = temp;
			}
			angel = angel % 90;
		}
		double r = Math.sqrt(src.height * src.height + src.width * src.width) / 2d;
		double len = 2 * Math.sin(Math.toRadians(angel) / 2) * r;
		double angelAlpha = (Math.PI - Math.toRadians(angel)) / 2;
		double angelDaltaWidth = Math.atan((double) src.height / src.width);
		double angelDaltaHeight = Math.atan((double) src.width / src.height);

		int lenDaltaWidth = (int) (len * Math.cos(Math.PI - angelAlpha - angelDaltaWidth));
		int lenDaltaHeight = (int) (len * Math.cos(Math.PI - angelAlpha - angelDaltaHeight));
		int desWidth = src.width + lenDaltaWidth * 2;
		int desHeight = src.height + lenDaltaHeight * 2;
		return new java.awt.Rectangle(new Dimension(desWidth, desHeight));
	}
	/**
	 * 获取网络图片流（附加）
	 * 
	 * @param url
	 * @return
	 */
	public static InputStream getImageStream(String url) {
		try {
			HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
			connection.setReadTimeout(5000);
			connection.setConnectTimeout(5000);
			connection.setRequestMethod("GET");
			if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
				InputStream inputStream = connection.getInputStream();
				return inputStream;
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}


public static void main(String[] args) {
		String url = "http://68.64.120.136:8086/archive/files/2019/1/16/17/8/1547629734732.jpg";
		// 将网络图片转为BufferedImage
	InputStream in = null;
		try {
			in=RotateImageUtil.getImageStream(url);
			BufferedImage bufferedImage = ImageIO.read(in);
			// 调用图片旋转工具类，旋转图片
			BufferedImage rotateImage = RotateImageUtil.rotateImage(bufferedImage, 90);
			// 截取URL中的图片名称和后缀
			String fileName = url.substring(url.lastIndexOf("/") + 1, url.length());
			// 截取图片后缀名（.png）,以保持图片格式不变
			String imgSuffix = url.substring(url.lastIndexOf(".") + 1, url.length());
			// 将旋转后的图片保存到D盘根目录下
			File file = new File("D:\\", fileName);
			ImageIO.write(rotateImage, imgSuffix, file);
		} catch (IOException e) {
			e.printStackTrace();
		}finally {
			try {
				if(in != null){
					in.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

}
