package com.rs.module.dam.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Redis操作工具类
 * <AUTHOR>
 * @date 2025年4月19日
 */
@Component
public class RedisTemplateUtil {

    @Autowired
    private StringRedisTemplate redisTemplate;

    public void expires(String key,int seconds){
        redisTemplate.expire(key,seconds, TimeUnit.SECONDS);
    }

    public void set(String key, String value){
        redisTemplate.opsForValue().set(key,value);
    }

    public boolean exists(String key){
        return redisTemplate.hasKey(key);
    }

    public void set(String key,String value,int seconds){
        redisTemplate.opsForValue().set(key,value,seconds,TimeUnit.SECONDS);
    }

    public String get(String key){
        return redisTemplate.opsForValue().get(key);
    }

    public long lPush(String key,String value){
        return redisTemplate.opsForList().leftPush(key,value);
    }


    public long lPush(String key,String value,int seconds){
        Long count = redisTemplate.opsForList().leftPush(key, value);
        if (seconds > 0){
            expires(key,seconds);
        }
        return count;
    }

    public String rPop(String key){
        String element = redisTemplate.opsForList().rightPop(key);
        return element;
    }

    public boolean lock (String key,String value,long seconds){
        return redisTemplate.opsForValue().setIfAbsent(key,value,seconds, TimeUnit.SECONDS);
    }

    public boolean deleteKey(String key){
        return redisTemplate.delete(key);
    }

    public void deleteKey(List<String> keys){
        redisTemplate.delete(keys);
    }

    public long sAdd(String key, List<String> value){
        String[] arr = new String[value.size()];
        return redisTemplate.opsForSet().add(key, value.toArray(arr));
    }

    public long sAdd(String key, String value){
        return redisTemplate.opsForSet().add(key, value);
    }

    public Set<String> sMembers(String key){
        return redisTemplate.opsForSet().members(key);
    }

    public long sRem(String key, String value){
        return redisTemplate.opsForSet().remove(key,value);
    }

    public long sRem(String key, Object... value){
        return redisTemplate.opsForSet().remove(key, value);
    }

    public boolean sIsMember(String key, String value){
        return redisTemplate.opsForSet().isMember(key, value);
    }

    public String sPop(String key){
        return redisTemplate.opsForSet().pop(key);
    }

    public long sCard(String key){
        return redisTemplate.opsForSet().size(key);
    }

    public long increase(String key){
        return redisTemplate.opsForValue().increment(key);
    }

    public long increaseBy(String key, int num){
        return redisTemplate.opsForValue().increment(key,num);
    }

    public long decrease(String key){
        return  redisTemplate.opsForValue().decrement(key);
    }

    public long decreaseBy(String key, int num){
        return redisTemplate.opsForValue().decrement(key, num);
    }


    public void hSet(String key, String field, String value){
        redisTemplate.opsForHash().put(key,field,value);
    }

    public void hSetAll(String key, Map<Object,Object> map){
        redisTemplate.opsForHash().putAll(key,map);
    }

    public String hGet(String key,String field){
        return  (String) redisTemplate.opsForHash().get(key,field);
    }

    public Map<Object, Object> hGetAll(String key){
        return   redisTemplate.opsForHash().entries(key);
    }

    public long hDel(String key,String field){
        return redisTemplate.opsForHash().delete(key, field);
    }

    public List<Object> hmGet(String key, List<Object> fieldList){
        return redisTemplate.opsForHash().multiGet(key, fieldList);
    }

    public long hLen(String key){
        return redisTemplate.opsForHash().size(key);
    }


    public long ttl(String key){
        return redisTemplate.getExpire(key);
    }

    public void zSet(String key,String userName,double value){
        redisTemplate.opsForZSet().add(key,userName,value);
    }

    public void rmZSet(String key,String userName){
        redisTemplate.opsForZSet().remove(key,userName);
    }

    public long rank(String key,String userName){
        return redisTemplate.opsForZSet().rank(key,userName);
    }

    public boolean  isExists(String key,String userName){
        Long rank = redisTemplate.opsForZSet().rank(key, userName);
        return rank != null;
    }
}
