package com.rs.module.dam.util;

import jp.sourceforge.qrcode.data.QRCodeImage;

import java.awt.image.BufferedImage;

/**
 * 二维码图像
 * <AUTHOR>
 * @date 2025年4月22日
 */
public class TwoDimensionCodeImage implements QRCodeImage {
    BufferedImage bufImg;

    public TwoDimensionCodeImage(BufferedImage bufImg) {
        this.bufImg = bufImg;
    }

    @Override
    public int getWidth() {
        return this.bufImg.getWidth();
    }

    @Override
    public int getHeight() {
        return this.bufImg.getHeight();
    }

    @Override
    public int getPixel(int x, int y) {
        return this.bufImg.getRGB(x, y);
    }
}
