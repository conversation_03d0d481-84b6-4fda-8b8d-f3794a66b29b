package com.rs.module.dam.ocr.xiangyun;

import java.util.List;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rs.module.dam.entity.archive.ArchiveCoverDO;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.entity.ocr.FormatCheckJobDO;
import com.rs.module.dam.entity.ocr.FormatMinceJobDO;
import com.rs.module.dam.entity.sys.ConfigGroupDO;
import com.rs.module.dam.ocr.strategy.OcrStrategy;
import com.rs.module.dam.ocr.vo.OcrRateProgress;

/**
 * xiangyun-享云OCR策略类
 * <AUTHOR>
 * @date 2025年4月26日
 */
@Component
public class XyOcrStrategy implements OcrStrategy {

	/**
	 * 智能编目
	 * @param materialList List<DossierMaterialInfo> 待编目材料列表
	 * @param jgrybm String 监管人员编码
	 * @param idCard String 身份证号码
	 * @param orgCode String 机构代码
	 * @return R
	 * <AUTHOR>
	 * @date 2024年3月20日
	 */
	@Override
	public void znbm(List<MaterialInfoDO> materialList, String jgrybm, String idCard, String orgCode) {
	}
	
	/**
	 * OCR回调
	 * @param callbackParam Object 回调参数
	 * <AUTHOR>
	 * @date 2024年4月30日
	 */
	@Override
	public void callback(Object callbackParam) {		
	}
	
	/**
	 * 获取案件OCR编目处理的进度
	 * @param jgrybm String 监管人员编码
	 * @return OcrProgress
	 * <AUTHOR>
	 * @date 2024年5月9日
	 */
	@Override
	public OcrRateProgress getBmRateProgress(String jgrybm) {
		return null;
	}
	
	/**
	 * 双层pdf生成
	 * @param archiveCover ArchiveCoverDO 卷宗封面
	 * @param filesList JSONArray 用于调用OCR服务的卷宗文件材料(fileId|fileUrl)
	 * @param fileObjCoverList JSONArray 用于保存任务的卷宗文件材料(fileId|fileXh)
	 * @param jgrybm String 监管人员编码
	 * @param zjType String 组卷类型(0:编目组卷完成组卷页面、1:卷宗导入确认组卷页面)
	 * @param idCard String 身份证号码
	 * @param orgCode String 机构代码
	 * <AUTHOR>
	 * @date 2024年5月10日
	 */
	@Override
	public void scpdf(ArchiveCoverDO archiveCover, JSONArray filesList, JSONArray fileObjCoverList,
			String jgrybm, String zjType, String idCard, String orgCode) {		
	}
	
	/**
	 * 格式化审查
	 * @param configGroup ConfigGroupDO 卷宗配置
	 * @param fileData JSONArray 待审查的文件数据
	 * @param formatCheckJob FormatCheckJobDO 格式化审查任务 
	 * @param minceJobList List<FormatMinceJobDO> 格式化审查子任务
	 * @return CheckFormatEnum
	 * <AUTHOR>
	 * @date 2024年5月29日
	 */
	@Override
	public void formatCheck(ConfigGroupDO configGroup, JSONArray fileData,
			FormatCheckJobDO formatCheckJob, List<FormatMinceJobDO> minceJobList) {		
	}
	
	/**
	 * 图片瑕疵页矫正
	 * @param materialId String 材料Id
	 * @param fileId String 文件Id
	 * @param url String 文件地址
	 * @param jgrybm String 监管人员编码
	 * @return boolean
	 * <AUTHOR>
	 * @date 2024年6月17日
	 */
	@Override
	public boolean reviseImage(String materialId, String fileId, String url, String jgrybm) {
		return true;
	}
	
	/**
	 * 调用ocr服务
	 * @param configGroup ConfigGroupDO 卷宗配置
	 * @param serviceCode String 服务代码
	 * @param fileData JSONArray 文件数据
	 * @param returnUrl String 回调地址
	 * @param jobId String 格式化审查任务 
	 * @param jgrybm String 监管人员编码
	 * @param extendParams String 扩展参数
	 * @return JSONObject
	 */
	@Override
	public JSONObject call(ConfigGroupDO configGroup, String serviceCode, JSONArray fileData,
			String returnUrl, String jobId, String jgrybm, JSONObject extendParams) {
		return new JSONObject();
	}
}
