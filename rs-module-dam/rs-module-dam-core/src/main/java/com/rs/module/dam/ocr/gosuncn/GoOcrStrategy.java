package com.rs.module.dam.ocr.gosuncn;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.orm.mybatis.util.WrapUtil;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.dam.config.IspV2ConfigUtil;
import com.rs.module.dam.config.WebSocketServer;
import com.rs.module.dam.constant.CheckTypeEnum;
import com.rs.module.dam.constant.DamConstants;
import com.rs.module.dam.constant.OcrConstants;
import com.rs.module.dam.constant.RedisConstants;
import com.rs.module.dam.entity.archive.ArchiveCoverDO;
import com.rs.module.dam.entity.archive.ArchiveJobCatalogDO;
import com.rs.module.dam.entity.archive.ArchiveJobDO;
import com.rs.module.dam.entity.log.IspInvokingDO;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.entity.ocr.FormatCheckJobDO;
import com.rs.module.dam.entity.ocr.FormatMinceJobDO;
import com.rs.module.dam.entity.sys.ConfigGroupDO;
import com.rs.module.dam.ocr.gosuncn.callback.IOcrCallback;
import com.rs.module.dam.ocr.handler.OcrHandler;
import com.rs.module.dam.ocr.strategy.OcrStrategy;
import com.rs.module.dam.ocr.strategy.OcrStrategyFactory;
import com.rs.module.dam.ocr.vo.OcrRateProgress;
import com.rs.module.dam.service.archive.ArchiveJobCatalogService;
import com.rs.module.dam.service.archive.ArchiveJobService;
import com.rs.module.dam.service.log.IspInvokingService;
import com.rs.module.dam.service.ocr.FormatMinceJobService;
import com.rs.module.dam.service.sys.ConfigGroupService;
import com.rs.module.dam.util.IpUtil;
import com.rs.module.dam.util.RedisTemplateUtil;
import com.rs.module.dam.util.RestTemplateUtil;
import com.rs.module.dam.websocket.WebSocketCode;

import lombok.extern.slf4j.Slf4j;

/**
 * gosuncn-高新兴OCR策略类
 * <AUTHOR>
 * @date 2025年4月26日
 */
@Component
@Slf4j
public class GoOcrStrategy implements OcrStrategy{
	
    @Value("${isp.ocr2.enableCatalogBm:true}")
    private boolean enableCatalogBm;
    
    //重复页检测-是否精细化（默认false）
    @Value("${isp.ocr2.repeat.refinement:false}")
    private boolean repeatRefinement;
    
    //重复页检测-相似度阈值（默认0.6）
    @Value("${isp.ocr2.repeat.simThreshold:0.6}")
    private float repeatSimThreshold;
    
    //瑕疵页检测-低于像素阈值超分
    @Value("${isp.ocr2.flaw.pixel:300}")
    private int flawPixel;
    
    //瑕疵页检测-黑边占图片比例下限阈值，高于阈值则存在黑边
    @Value("${isp.ocr2.flaw.black_ratio:0.1}")
    private float flawBlackRatio;
    
    //瑕疵页检测-阴影占图片比例下限阈值，高于阈值则存在阴影
    @Value("${isp.ocr2.flaw.shadow_ratio:0.25}")
    private float flawShadowRatio;
	
	@Autowired
	private ConfigGroupService dmsConfigGroupService;
	
	@Autowired
	private IspInvokingService dmsIspInvokingService;
	
	@Autowired
	private ArchiveJobService dmsArchiveJobService;
	
	@Autowired
	private ArchiveJobCatalogService dmsArchiveJobCatalogService;
	
	@Autowired
	private FormatMinceJobService dmsFormatMinceJobService;
	
    @Autowired
    private RedisTemplateUtil redisTemplateUtil;
    
    @Autowired
    OcrHandler ocrHandler;

	/**
	 * 智能编目
	 * @param materialList List<MaterialInfoDO> 待编目材料列表
	 * @param jgrybm String 监管人员编码
	 * @param idCard String 身份证号码
	 * @param orgCode String 机构代码
	 * <AUTHOR>
	 * @date 2024年3月20日
	 */
	@Override
	public void znbm(List<MaterialInfoDO> materialList, String jgrybm, String idCard, String orgCode) {
        
        //ocr请求识别成功后的批次id和数量, 用于获取识别进度
        String requestBatchNumKey = String.format(OcrConstants.REDIS_REQUEST_OCR_BATCH_NUM, jgrybm);
        
        //清理案件历史批次(信息)
        redisTemplateUtil.deleteKey(requestBatchNumKey);
        
        //查询卷宗配置信息
        ConfigGroupDO dmsConfigGroup = dmsConfigGroupService.getUserConfigGroup(orgCode);
        if(dmsConfigGroup == null) {
        	log.error("OCR处理-智能编目启动异常，找不到卷宗配置, jgrybm:{}, orgCode:{}", jgrybm, orgCode);
        	return;
        }
        
        //待编目ocr文件列表和材料文件列表
        JSONArray ocrFiles = new JSONArray();
        JSONArray materialFiles = new JSONArray();
        
        //智能编目缓存Key-材料Id列表
        List<String> startOcrKeys = new ArrayList<>();
        
        //批次Id
        String batchId = StringUtil.getGuid32();
        
        //启动标题自动编目时删除历史编目任务
        if(enableCatalogBm) {        	
        	QueryWrapper<ArchiveJobCatalogDO> deleteWrapper = WrapUtil.eq(ArchiveJobCatalogDO.class, "jgrybm", jgrybm);
        	dmsArchiveJobCatalogService.remove(deleteWrapper);
        }
        
        //标题编目任务
        List<ArchiveJobCatalogDO> catalogJobList = new ArrayList<>();
        
        //循环待编目材料列表，生成智能编目请求数据
        for(MaterialInfoDO dossierMaterialInfo : materialList) {
        	String materialInfoId = dossierMaterialInfo.getId();
        	
        	//添加材料缓存锁(ocr:start:materId:{materialId})，默认3小时有效期
        	String startOcrKey = String.format(RedisConstants.START_OCR_MATER_ID, materialInfoId);
            boolean lock = redisTemplateUtil.lock(startOcrKey, "1", 10800);
            if(!lock) {
            	log.info("OCR处理-智能编目 materialInfoId:{} 正在识别中", materialInfoId);
                continue;
            }
            else {
            	startOcrKeys.add(startOcrKey);
            }
            
            //材料文书
            JSONArray materialFile = JSONArray.parseArray(dossierMaterialInfo.getMaterialFile());
            
            //将材料文书放置到待编目文件列表中
            if(!materialFile.isEmpty()) {
            	JSONObject materialFileJson = new JSONObject();
            	materialFileJson.put("id", materialInfoId);
            	materialFileJson.put("materialFile", materialFile);
            	materialFiles.add(materialFileJson);
            	
            	//构建OCR编目文件列表数据
            	for(int i = 0; i < materialFile.size(); i ++) {
                	JSONObject materialFileObj = materialFile.getJSONObject(i);
                	
                	//文件Id
                	String fileId = materialFileObj.getString("id");
                	
                	//文件序号
                	Integer xh = materialFileObj.getInteger("xh");
                	
                	//文件地址
                	String url = materialFileObj.getString("url");
                	
                	JSONObject fileObj = new JSONObject();                	
                	fileObj.put("fileId", fileId + "_" + materialInfoId + "_" + xh);                	
                	if(StringUtil.isNotEmpty(url)) {
                		fileObj.put("fileUrl", url);
                		ocrFiles.add(fileObj);
                	}
                	else {
                		continue;
                	}
                    
                    //添加到标题编目任务中
                    if(enableCatalogBm) {
                    	ArchiveJobCatalogDO jobCatalog = new ArchiveJobCatalogDO();
                    	jobCatalog.setId(fileId);
                    	jobCatalog.setJgrybm(jgrybm);
                    	jobCatalog.setBatchId(batchId);
                    	jobCatalog.setMaterialId(materialInfoId);
                    	jobCatalog.setMaterialXh(dossierMaterialInfo.getXh());
                    	jobCatalog.setFileXh(xh);
                    	jobCatalog.setAddTime(new Date());
                    	catalogJobList.add(jobCatalog);
                    }
                }
            }
            else {
            	//删除材料缓存锁
            	redisTemplateUtil.deleteKey(startOcrKey);
            }
        }
        
        //构建服务请求参数
        JSONObject serviceRequestParams = GoUtil.buildBaseServiceRequestParmas(idCard, batchId,
        		ocrFiles, IspV2ConfigUtil.getReturnUrl());
        
        //智能编目服务代码
        String serviceCode = IspV2ConfigUtil.getZnbmCode();
        
        //构建isp接口请求参数
        JSONObject ispParams = GoUtil.buildIspParams(dmsConfigGroup, serviceCode, serviceRequestParams);
        
        //构建isp调用对象
        IspInvokingDO dmsIspInvoking = new IspInvokingDO();
        dmsIspInvoking.setId(StringUtil.getGuid32());
        dmsIspInvoking.setJobId(batchId);
        dmsIspInvoking.setInput(ispParams.toJSONString());
        dmsIspInvoking.setSerCode(serviceCode);
        dmsIspInvoking.setInvokeType(OcrConstants.OCR_INVOKE_TYPE_ZNBM);
        dmsIspInvoking.setAddress(dmsConfigGroup.getOcrServer());
        dmsIspInvoking.setJgrybm(jgrybm);
        dmsIspInvoking.setRequestFrom(OcrConstants.OCR_REQUEST_FROM_ZNBM);
        dmsIspInvoking.setServerIp(IpUtil.getHostIp());
        dmsIspInvoking.setCheckType(OcrConstants.OCR_INVOKE_TYPE_ZNBM);
        dmsIspInvoking.setFileCount(ocrFiles.size());
        
        //调用isp服务返回结果
        JSONObject result = null;
        
        try {
        	//调用isp服务接口
        	long start = System.currentTimeMillis();
        	String invokeUrl = dmsConfigGroup.getOcrServer() + IspV2ConfigUtil.getCommonUri();
        	result = RestTemplateUtil.postWithJson(invokeUrl, ispParams);
        	
        	//更新isp调用对象
        	dmsIspInvoking.setOutput(result.toJSONString());
            dmsIspInvoking.setRequestTime(System.currentTimeMillis() - start);
        }
        catch(Exception e) {
        	log.info("OCR处理-智能编目失败 jgrybm:{},异常信息：{}", jgrybm, e.getMessage());
        	
        	//更新isp调用对象
        	dmsIspInvoking.setOutput(e.getMessage());
            dmsIspInvoking.setRequestStatus("2");
            
            //删除材料缓存
            redisTemplateUtil.deleteKey(startOcrKeys);
            
            //通过webSocket发送接口调用失败的消息给前端
            //前端关闭进度，弹窗提示用户开启ocr识别失败
            JSONObject sendData = new JSONObject();
            sendData.put("code", WebSocketCode.OPEN_BASE_OCR_FAIL.getCode());
            sendData.put("msg", WebSocketCode.OPEN_BASE_OCR_FAIL.getMsg());
            sendData.put("jgrybm", jgrybm);
            WebSocketServer.sendInfo(null, sendData.toJSONString());
        }
        
        try {
        	//插入isp调用记录
        	dmsIspInvoking.setAddTime(new Date());
            dmsIspInvokingService.save(dmsIspInvoking);
		}
        catch (Exception e) {
        	log.error("OCR处理-智能编目插入接口调用记录失败, jgrybm:{},异常信息：{}", jgrybm, e.getMessage());
		}
        
        //处理isp返回结果
        if(result != null) {
        	GoOcrResult ocrResult = GoOcrResult.buildOcrResult(result);
        	
        	//返回代码为空时删除材料缓存并返回
        	if(null == ocrResult.getCode()) {
        		redisTemplateUtil.deleteKey(startOcrKeys);
                return;
        	}
        	
        	//处理成功或者资源不足时
        	else if(ocrResult.getCode() == GoOcrHttpStatus.OK.getCode()
        			|| ocrResult.getCode() == GoOcrHttpStatus.INSUFFICIENT_RESOURCE.getCode()) {
        		
            	//待插入的归档任务集合
            	List<ArchiveJobDO> archiveJobs = new ArrayList<>();
            	for(int i = 0; i < materialFiles.size(); i ++) {
            		JSONObject materialInfoJson = materialFiles.getJSONObject(i);            		
            		String materialId = materialInfoJson.getString("id");
            		JSONArray materialFile = materialInfoJson.getJSONArray("materialFile");
            		
                	//设置编目任务对象
                	ArchiveJobDO dmsArchiveJob = new ArchiveJobDO();
        			dmsArchiveJob.setId(StringUtil.getGuid32());
        			dmsArchiveJob.setJgrybm(jgrybm);
        			dmsArchiveJob.setMaterialId(materialId);
        			dmsArchiveJob.setStartTime(new Date());
                    dmsArchiveJob.setType(DamConstants.JOB_TYPE_BMJG);
                    dmsArchiveJob.setFileContent(materialFile.toJSONString());
                    dmsArchiveJob.setAddUser(idCard);
                    dmsArchiveJob.setOrgCode(orgCode);
                    dmsArchiveJob.setBatchId(batchId);
                    
                    //处理成功
                    if(ocrResult.getCode() == GoOcrHttpStatus.OK.getCode()) {
                    	
                    	//状态：job未执行
                    	dmsArchiveJob.setState(DamConstants.JOB_STATE_NO);
                    	
                    	//更新编目任务执行状态
                        DamConstants.BM_JOB_EXECUTE = true;
                    }
                    
                    //资源不足
                    else {
                    	
                    	//状态：job任务等待处理(资源不足)
                    	dmsArchiveJob.setState(DamConstants.JOB_STATE_PENDING);
                    }
                    
                    //添加到待插入的归档任务中
                    archiveJobs.add(dmsArchiveJob);
            	}
            	
            	//批量插入编目任务(100条插入一次)
            	dmsArchiveJobService.saveBatch(archiveJobs, 100);
            	
            	//添加到标题编目任务中
                if(enableCatalogBm) {
                	dmsArchiveJobCatalogService.saveBatch(catalogJobList, 100);
                }
            	
                //添加缓存-请求成功后的批次id和数量
                redisTemplateUtil.hSet(requestBatchNumKey, batchId, String.valueOf(ocrFiles.size()));
        	}
        	
        	//其他情况
        	else {
        		dmsIspInvoking.setRequestStatus("2");
        	}
        }
	}
	
	/**
	 * OCR回调处理
	 * @param callbackParam Object 回调参数
	 * <AUTHOR>
	 * @date 2024年4月30日
	 */
	@Override
	public void callback(Object callbackParam) {
		try {
			//生成回调参数对象
			GoOcrCallbackParam goCallbackParam = Optional.ofNullable(JSONObject.parseObject(
					JSON.toJSONString(callbackParam), GoOcrCallbackParam.class))
					.orElseThrow(() -> new RuntimeException("ocr处理-回调处理发生异常：回调数据不合法"));
			
			//服务代码
			String serCode = goCallbackParam.getSerCode();
			if(StringUtil.isNotEmpty(serCode)) {
				
				//获取处理Api
				IOcrCallback callback = Optional.ofNullable(OcrStrategyFactory.getCallbackApi(serCode))
						.orElseThrow(() -> new RuntimeException("ocr处理-回调处理发生异常：不支持的服务类型"));
				callback.handle(goCallbackParam);
			}
			else {
				log.error("ocr处理-回调处理发生异常：找不到服务代码");
				throw new RuntimeException("ocr处理-回调处理发生异常：找不到服务代码");
			}
		}
		catch(Exception e) {
			throw e;
		}
	}
	
	/**
	 * 获取案件OCR编目处理的进度
	 * @param jgrybm String 监管人员编码
	 * @return OcrProgress
	 * <AUTHOR>
	 * @date 2024年5月9日
	 */
	@Override
	public OcrRateProgress getBmRateProgress(String jgrybm) {
		
		 //ocr请求识别成功后的批次id和数量
        String requestBatchNumKey = String.format(OcrConstants.REDIS_REQUEST_OCR_BATCH_NUM, jgrybm);
        if(!redisTemplateUtil.exists(requestBatchNumKey)) {
        	return null;
        }
        
        //ocr请求识别成功后的批次Id和进度
        String requestBatchProgressKey = String.format(OcrConstants.REDIS_REQUEST_OCR_BATCH_PROGRESS, jgrybm);
       
        //案件材料文件总数量
        int ocrNum = 0, processedNum = 0;
        
		//案件材料文书已处理数量
        Map<Object, Object> batchNumMap = redisTemplateUtil.hGetAll(requestBatchNumKey);
        Map<Object, Object> batchProgressMap = redisTemplateUtil.hGetAll(requestBatchProgressKey);
        if(!batchNumMap.isEmpty() && !batchProgressMap.isEmpty()) {
        	for(Map.Entry<Object, Object> batchNumEntry : batchNumMap.entrySet()) {
            	int batchNum = Integer.valueOf(batchNumEntry.getValue().toString());
            	ocrNum += batchNum;
            	if(batchProgressMap.containsKey(batchNumEntry.getKey())) {
            		float batchProgress = Float.valueOf(batchProgressMap.get(batchNumEntry.getKey()).toString());
            		processedNum += Math.round(batchNum * batchProgress);
            	}
            }
        	
        	OcrRateProgress ocrRateProgress = new OcrRateProgress();
            
            //案件材料文书剩余数量
            int remainingNum = ocrNum - processedNum;
            
            //正常处理时
            if(ocrNum >= remainingNum) {
            	
                //所有材料文书处理完成后清除缓存
                if(remainingNum == 0) {
                	redisTemplateUtil.deleteKey(requestBatchNumKey);
                	redisTemplateUtil.deleteKey(requestBatchProgressKey);
                	
    		        //案件材料文件总数量
    		        String finishMark = String.format(RedisConstants.TASK_JOB_FINISH_NUM, jgrybm);    		        
                	redisTemplateUtil.deleteKey(finishMark);
                }
                
                ocrRateProgress.setOcrNum(ocrNum);
                ocrRateProgress.setProcessedNum(processedNum);
                ocrRateProgress.setRemainingNum(remainingNum);
            }
            
            //异常处理时(ocr不回调等)
            else {
            	redisTemplateUtil.deleteKey(requestBatchNumKey);
            	redisTemplateUtil.deleteKey(requestBatchProgressKey);
            	
		        //案件材料文件总数量
		        String finishMark = String.format(RedisConstants.TASK_JOB_FINISH_NUM, jgrybm);		        
            	redisTemplateUtil.deleteKey(finishMark);
            	
                ocrRateProgress.setOcrNum(ocrNum);
                ocrRateProgress.setProcessedNum(ocrNum);
                ocrRateProgress.setRemainingNum(0);
            }
            
            return ocrRateProgress;
        }
        
		return null;
	}
	
	/**
	 * 双层pdf生成
	 * @param dmsArchiveCover ArchiveCoverDO 卷宗封面
	 * @param filesList JSONArray 用于调用OCR服务的卷宗文件材料(fileId|fileUrl)
	 * @param fileObjCoverList JSONArray 用于保存任务的卷宗文件材料(fileId|fileXh)
	 * @param jgrybm String 监管人员编码
	 * @param zjType String 组卷类型(0:编目组卷完成组卷页面、1:卷宗导入确认组卷页面)
	 * @param idCard String 身份证号码
	 * @param orgCode String 机构代码
	 * <AUTHOR>
	 * @date 2024年5月10日
	 */
	@Override
	public void scpdf(ArchiveCoverDO dmsArchiveCover, JSONArray filesList, JSONArray fileObjCoverList,
			String jgrybm, String zjType, String idCard, String orgCode) {
		log.info("OCR处理-生成双层pdf-调用接口开始 jgrybm:{}, zjType:{}, 卷宗文件数量1:{}, 卷宗文件数量2:{}",
				jgrybm, zjType, filesList.size(), fileObjCoverList.size());
				
		//批次Id
		String batchId = dmsArchiveCover.getId();
		
        //查询卷宗配置信息
        ConfigGroupDO dmsConfigGroup = dmsConfigGroupService.getUserConfigGroup(orgCode);
		
        //构建服务请求参数
        JSONObject serviceRequestParams = GoUtil.buildBaseServiceRequestParmas(idCard,
        		batchId, filesList, IspV2ConfigUtil.getReturnUrl());
        
        //智能编目服务代码
        String serviceCode = IspV2ConfigUtil.getScpdfCode();
        
        //构建isp接口请求参数
        JSONObject ispParams = GoUtil.buildIspParams(dmsConfigGroup, serviceCode, serviceRequestParams);        
     
        //构建isp调用对象
        IspInvokingDO dmsIspInvoking = new IspInvokingDO();
        dmsIspInvoking.setId(StringUtil.getGuid32());
        dmsIspInvoking.setJobId(batchId);
        dmsIspInvoking.setInput(ispParams.toJSONString());
        dmsIspInvoking.setSerCode(serviceCode);
        dmsIspInvoking.setInvokeType(OcrConstants.OCR_INVOKE_TYPE_SCPDFZH);
        dmsIspInvoking.setAddress(dmsConfigGroup.getOcrServer());
        dmsIspInvoking.setJgrybm(jgrybm);
        dmsIspInvoking.setRequestFrom(OcrConstants.OCR_REQUEST_FROM_ZNBM);
        dmsIspInvoking.setServerIp(IpUtil.getHostIp());
        dmsIspInvoking.setInput(ispParams.toJSONString());
        dmsIspInvoking.setCheckType(OcrConstants.OCR_INVOKE_TYPE_SCPDFZH);
        dmsIspInvoking.setFileCount(filesList.size());
        
        //调用isp服务返回结果
        JSONObject result = null;
        String invokeUrl = null;
        
        try {
        	//调用isp服务接口
        	long start = System.currentTimeMillis();
        	invokeUrl = dmsConfigGroup.getOcrServer() + IspV2ConfigUtil.getCommonUri();
        	result = RestTemplateUtil.postWithJson(invokeUrl, ispParams);
        	
        	log.info("OCR处理-生成双层pdf-调用接口结束! 调用地址:{}, 调用参数:{}, 调用结果:{}", invokeUrl, ispParams, result);
        	
        	//更新isp调用对象
        	dmsIspInvoking.setOutput(result.toJSONString());
            dmsIspInvoking.setRequestTime(System.currentTimeMillis() - start);
        }
        catch(Exception e) {
        	log.error("OCR处理-生成双层pdf-调用接口失败! , 调用地址:{}, 调用参数:{}, jgrybm:{}, 异常信息：{}", invokeUrl,
        			ispParams, jgrybm, e.getMessage());
        	
        	//更新isp调用对象
        	dmsIspInvoking.setOutput(e.getMessage());
            dmsIspInvoking.setRequestStatus("2");
        }
        
        //处理isp返回结果
        if(result != null) {
        	GoOcrResult ocrResult = GoOcrResult.buildOcrResult(result);
        	
        	//返回代码为空时删除材料缓存并返回
        	if(null == ocrResult.getCode()) {
        		dmsIspInvoking.setRequestStatus("2");
        	}
        	
        	//处理成功或者资源不足时
        	else if(ocrResult.getCode() == GoOcrHttpStatus.OK.getCode()
        			|| ocrResult.getCode() == GoOcrHttpStatus.INSUFFICIENT_RESOURCE.getCode()) {
        		ArchiveJobDO dmsArchiveJob = new ArchiveJobDO();
                dmsArchiveJob.setId(batchId);
                dmsArchiveJob.setJgrybm(jgrybm);
                dmsArchiveJob.setCoverContent(fileObjCoverList.toJSONString());
                dmsArchiveJob.setMaterialId(dmsArchiveCover.getId());
                dmsArchiveJob.setStartTime(new Date());
                dmsArchiveJob.setType(zjType);
                dmsArchiveJob.setAddUser(idCard);
                dmsArchiveJob.setOrgCode(orgCode);
                
                //更新编目任务执行状态
                DamConstants.PDF_JOB_EXECUTE = true;
                
                //处理成功
                if(ocrResult.getCode() == GoOcrHttpStatus.OK.getCode()) {
                	
                	//状态：job未执行
                	dmsArchiveJob.setState(DamConstants.JOB_STATE_NO);
                }
                
                //资源不足
                else {
                	
                	//状态：job任务等待处理(资源不足)
                	dmsArchiveJob.setState(DamConstants.JOB_STATE_PENDING);
                }
                
                //插入双层pdf获取任务
                dmsArchiveJobService.save(dmsArchiveJob);
                
                dmsIspInvoking.setRequestStatus("1");
        	}
        	else {
        		dmsIspInvoking.setRequestStatus("2");
        	}
        }
        
        try {
        	//插入isp调用记录
        	dmsIspInvoking.setAddTime(new Date());
            dmsIspInvokingService.save(dmsIspInvoking);
		}
        catch (Exception e) {
        	log.error("OCR处理-生成双层pdf-插入调用记录失败,jgrybm:{},异常信息：{}", jgrybm, e.getMessage());
		}
	}
	
	/**
	 * 格式化审查
	 * @param configGroup ConfigGroupDO 卷宗配置
	 * @param fileData JSONArray 待审查的文件数据
	 * @param formatCheckJob FormatCheckJobDO 格式化审查任务 
	 * @param minceJobList List<FormatMinceJobDO> 格式化审查子任务
	 * @return CheckFormatEnum
	 * <AUTHOR>
	 */
	@Override
	public void formatCheck(ConfigGroupDO configGroup, JSONArray fileData,
			FormatCheckJobDO formatCheckJob, List<FormatMinceJobDO> minceJobList) {
        
        //构建isp调用对象
        IspInvokingDO dmsIspInvoking = new IspInvokingDO();	        
        dmsIspInvoking.setJobId(formatCheckJob.getId());
        dmsIspInvoking.setAddress(configGroup.getOcrServer());
        dmsIspInvoking.setJgrybm(formatCheckJob.getJgrybm());
        dmsIspInvoking.setRequestFrom(OcrConstants.OCR_REQUEST_FROM_FORMAT_CHECK);
        dmsIspInvoking.setServerIp(IpUtil.getHostIp());        
        dmsIspInvoking.setFileCount(fileData.size());
        
        //空白页检测、重复页检测和瑕疵页检测任务
        List<FormatMinceJobDO> kbcfxcMinceJobList = minceJobList.stream().filter(mce -> mce.getCheckType().equals(CheckTypeEnum.BLANK_SPACE.getCode())
    			|| mce.getCheckType().equals(CheckTypeEnum.REPEAT_PAGE.getCode()) || mce.getCheckType().equals(CheckTypeEnum.FLAW.getCode()))
    			.collect(Collectors.toList());
        
        //签名检测、签章检测和捺印检测任务
        List<FormatMinceJobDO> qmqznyMinceJobList = minceJobList.stream().filter(mce -> mce.getCheckType().equals(CheckTypeEnum.SEAL.getCode())
    			|| mce.getCheckType().equals(CheckTypeEnum.SIGNATURE.getCode()) || mce.getCheckType().equals(CheckTypeEnum.FINGERPRINT.getCode()))
    			.collect(Collectors.toList());
        
        //文书填写规范检测任务
        List<FormatMinceJobDO> wsxxtqMinceJobList = minceJobList.stream().filter(mce -> mce.getCheckType().equals(CheckTypeEnum.INFO_EXTRACT.getCode()))
    			.collect(Collectors.toList());
        
        //发起空白页、重复页和瑕疵页检测请求
        if(CollectionUtil.isNotNull(kbcfxcMinceJobList)) {  
        	
            //设置接口调用类型
            dmsIspInvoking.setInvokeType(OcrConstants.OCR_INVOKE_TYPE_CHECK_KBCFXC);
            dmsIspInvoking.setCheckType(OcrConstants.OCR_INVOKE_TYPE_CHECK_KBCFXC);
            
            //获取空白页、重复页和瑕疵页Job
        	Optional<FormatMinceJobDO> blankJob = kbcfxcMinceJobList.stream().filter(mce -> mce.getCheckType().equals(CheckTypeEnum.BLANK_SPACE.getCode())).findFirst();
        	Optional<FormatMinceJobDO> repeatJob = kbcfxcMinceJobList.stream().filter(mce -> mce.getCheckType().equals(CheckTypeEnum.REPEAT_PAGE.getCode())).findFirst();
        	Optional<FormatMinceJobDO> flawJob = kbcfxcMinceJobList.stream().filter(mce -> mce.getCheckType().equals(CheckTypeEnum.FLAW.getCode())).findFirst();
        	
        	//批次Id
        	String batchId = formatCheckJob.getId();
        	
        	//服务代码
        	String serviceCode = IspV2ConfigUtil.getKbcfxcCode();
        	
        	//构建服务请求参数
            JSONObject serviceRequestParams = GoUtil.buildBaseServiceRequestParmas(formatCheckJob.getAddUser(),
            		batchId, fileData, IspV2ConfigUtil.getReturnUrl());
            
            //补充空白页检测参数
            JSONObject blankJson = new JSONObject();
        	blankJson.put("open", blankJob.isPresent() ? 1 : 0);
        	serviceRequestParams.put("blank", blankJson);
            
            //补充重复页检测参数
        	JSONObject repeatJson = new JSONObject();
        	repeatJson.put("open", repeatJob.isPresent() ? 1 : 0);
        	repeatJson.put("refinement", repeatRefinement);
        	repeatJson.put("simThreshold", repeatSimThreshold);
        	serviceRequestParams.put("repeat", repeatJson);
            
            //补充瑕疵页检测参数
        	JSONObject flawJson = new JSONObject();
        	flawJson.put("open", flawJob.isPresent() ? 1 : 0);
        	flawJson.put("pixel", flawPixel);
        	flawJson.put("black_ratio", flawBlackRatio);
        	flawJson.put("shadow_ratio", flawShadowRatio);
        	serviceRequestParams.put("flaw", flawJson);
            
            //设置接口调用类型
            dmsIspInvoking.setInvokeType(OcrConstants.OCR_INVOKE_TYPE_CHECK_KBCFXC);
            dmsIspInvoking.setCheckType(OcrConstants.OCR_INVOKE_TYPE_CHECK_KBCFXC);
        	
        	//调用ocr接口(空白页、重复页、瑕疵页检测)
        	JSONObject result = ocrHandler.executeOcrInvoke(configGroup, serviceCode, serviceRequestParams, dmsIspInvoking);
        	
        	//根据返回结果获取任务状态
            String minceJobStatus = GoUtil.getMinceJobStatusByResult(result);
            
            //补充图片方向合规性检测
            if(flawJob.isPresent() && !DamConstants.JOB_STATE_ERROR.equals(minceJobStatus)) {
            	
            	//图片方向合规性检查代码
            	String tpfxjcServiceCode = IspV2ConfigUtil.getTpfxjzCode();
            	
            	//设置接口调用类型
            	IspInvokingDO tpfxjcDmsInvoking = new IspInvokingDO();
            	BeanUtils.copyProperties(dmsIspInvoking, tpfxjcDmsInvoking);
            	tpfxjcDmsInvoking.setInvokeType(OcrConstants.OCR_INVOKE_TYPE_CHECK_TPFX);
            	tpfxjcDmsInvoking.setCheckType(OcrConstants.OCR_INVOKE_TYPE_CHECK_TPFX);
            	
            	//图片方向合规性检查请求参数
            	JSONObject tpfxjcServiceRequestParams = GoUtil.buildBaseServiceRequestParmas(formatCheckJob.getAddUser(),
                		batchId, fileData, IspV2ConfigUtil.getReturnUrl());
            	
            	//调用ocr接口(图片方向合规性检测)
            	result = ocrHandler.executeOcrInvoke(configGroup, tpfxjcServiceCode, tpfxjcServiceRequestParams, tpfxjcDmsInvoking);
            	
            	//根据返回结果获取任务状态
            	minceJobStatus = GoUtil.getMinceJobStatusByResult(result);
            }
        	
        	//处理任务被取消时的任务状态
        	ocrHandler.updateCancelMinceJobList(kbcfxcMinceJobList, formatCheckJob.getId());
        	
        	//重新设置任务状态
        	for (FormatMinceJobDO minceJob : kbcfxcMinceJobList) {
        		
                //状态为取消的任务,不做修改
                if (!DamConstants.JOB_STATE_CANCEL.equals(minceJob.getJobStatus())){
                    minceJob.setJobStatus(minceJobStatus);
                }
                
                //异步时删除初始化进度条
                if (DamConstants.JOB_STATE_ERROR.equals(minceJobStatus)){
                    redisTemplateUtil.hDel(RedisConstants.FORMAT_CHECK_MINCE_JOB_PROCESS, minceJob.getId());
                }
            }
            dmsFormatMinceJobService.updateBatchById(kbcfxcMinceJobList);
        }
        
        //发起签名检测、签章检测和捺印检测请求
        if(CollectionUtil.isNotNull(qmqznyMinceJobList)) {
        	
            //设置接口调用类型
            dmsIspInvoking.setInvokeType(OcrConstants.OCR_INVOKE_TYPE_CHECK_QMQZNY);
            dmsIspInvoking.setCheckType(OcrConstants.OCR_INVOKE_TYPE_CHECK_QMQZNY);
        	
        	//批次Id
        	String batchId = formatCheckJob.getId();
        	
        	//服务代码
        	String serviceCode = IspV2ConfigUtil.getQmqznyCode();
        	
        	//构建服务请求参数
            JSONObject serviceRequestParams = GoUtil.buildBaseServiceRequestParmas(formatCheckJob.getAddUser(),
            		batchId, fileData, IspV2ConfigUtil.getReturnUrl());
            
        	//调用ocr接口(签名、签章和捺印审查)
            JSONObject result = ocrHandler.executeOcrInvoke(configGroup, serviceCode, serviceRequestParams, dmsIspInvoking);
            
            //根据返回结果获取任务状态
            String minceJobStatus = GoUtil.getMinceJobStatusByResult(result);
            
            //处理任务被取消时的任务状态
        	ocrHandler.updateCancelMinceJobList(qmqznyMinceJobList, formatCheckJob.getId());
        	
        	//重新设置任务状态
        	for (FormatMinceJobDO minceJob : qmqznyMinceJobList) {
        		
                //状态为取消的任务,不做修改
                if (!DamConstants.JOB_STATE_CANCEL.equals(minceJob.getJobStatus())){
                    minceJob.setJobStatus(minceJobStatus);
                }
                
                //异步时删除初始化进度条
                if (DamConstants.JOB_STATE_ERROR.equals(minceJobStatus)){
                    redisTemplateUtil.hDel(RedisConstants.FORMAT_CHECK_MINCE_JOB_PROCESS, minceJob.getId());
                }
            }
            dmsFormatMinceJobService.updateBatchById(qmqznyMinceJobList);
        }
        
        //发起文书填写规范检测请求
        if(CollectionUtil.isNotNull(wsxxtqMinceJobList)) {
        	
            //设置接口调用类型
            dmsIspInvoking.setInvokeType(OcrConstants.OCR_INVOKE_TYPE_WSXXTQ);
            dmsIspInvoking.setCheckType(OcrConstants.OCR_INVOKE_TYPE_WSXXTQ);
        	
        	//批次Id
        	String batchId = formatCheckJob.getId();
        	
        	//服务代码
        	String serviceCode = IspV2ConfigUtil.getWsxxtqCode();
        	
        	//构建服务请求参数
            JSONObject serviceRequestParams = GoUtil.buildBaseServiceRequestParmas(formatCheckJob.getAddUser(),
            		batchId, fileData, IspV2ConfigUtil.getReturnUrl());
            
        	//调用ocr接口(文书信息提取)
            JSONObject result = ocrHandler.executeOcrInvoke(configGroup, serviceCode, serviceRequestParams, dmsIspInvoking);
            
            //根据返回结果获取任务状态
            String minceJobStatus = GoUtil.getMinceJobStatusByResult(result);
            
            //处理任务被取消时的任务状态
        	ocrHandler.updateCancelMinceJobList(wsxxtqMinceJobList, formatCheckJob.getId());
        	
        	//重新设置任务状态
        	for (FormatMinceJobDO minceJob : wsxxtqMinceJobList) {
        		
                //状态为取消的任务,不做修改
                if (!DamConstants.JOB_STATE_CANCEL.equals(minceJob.getJobStatus())){
                    minceJob.setJobStatus(minceJobStatus);
                }
                
                //异步时删除初始化进度条
                if (DamConstants.JOB_STATE_ERROR.equals(minceJobStatus)){
                    redisTemplateUtil.hDel(RedisConstants.FORMAT_CHECK_MINCE_JOB_PROCESS, minceJob.getId());
                }
            }
            dmsFormatMinceJobService.updateBatchById(wsxxtqMinceJobList);
        }
	}
	
	/**
	 * 图片瑕疵页矫正
	 * @param materialId String 材料Id
	 * @param fileId String 文件Id
	 * @param url String 文件地址
	 * @param jgrybm String 监管人员编码
	 * @return boolean
	 * <AUTHOR>
	 */
	@Override
	public boolean reviseImage(String materialId, String fileId, String url, String jgrybm) {
		boolean isSucess = false;
		log.info("OCR处理-瑕疵页矫正-调用接口开始 监管人员编码:{}, 材料Id:{}, 文件Id:{}, 文件地址:{}",
				jgrybm, materialId, fileId, url);
		
		//当前用户
		SessionUser user = SessionUserUtil.getSessionUser();
		
        //查询卷宗配置信息
        ConfigGroupDO dmsConfigGroup = dmsConfigGroupService.getUserConfigGroup(user.getOrgCode());
        
        //待检测的文件
        JSONArray fileData = new JSONArray();
        JSONObject file = new JSONObject();
        file.put("fileId", fileId);
        file.put("fileUrl", url);
        fileData.add(file);
		
        //构建服务请求参数
        JSONObject serviceRequestParams = GoUtil.buildBaseServiceRequestParmas(user.getIdCard(),
        		materialId, fileData, IspV2ConfigUtil.getReturnUrl());
        
        //智能编目服务代码
        String serviceCode = IspV2ConfigUtil.getXcyjzCode();
        
        //构建isp接口请求参数
        JSONObject ispParams = GoUtil.buildIspParams(dmsConfigGroup, serviceCode, serviceRequestParams);        
     
        //构建isp调用对象
        IspInvokingDO dmsIspInvoking = new IspInvokingDO();
        dmsIspInvoking.setId(StringUtil.getGuid32());
        dmsIspInvoking.setJobId(materialId);
        dmsIspInvoking.setInput(ispParams.toJSONString());
        dmsIspInvoking.setSerCode(serviceCode);
        dmsIspInvoking.setInvokeType(OcrConstants.OCR_INVOKE_TYPE_XCJZ);
        dmsIspInvoking.setAddress(dmsConfigGroup.getOcrServer());
        dmsIspInvoking.setJgrybm(jgrybm);
        dmsIspInvoking.setRequestFrom(OcrConstants.OCR_REQUEST_FROM_ZNBM);
        dmsIspInvoking.setServerIp(IpUtil.getHostIp());
        dmsIspInvoking.setInput(ispParams.toJSONString());
        dmsIspInvoking.setCheckType(OcrConstants.OCR_INVOKE_TYPE_SCPDFZH);
        dmsIspInvoking.setFileCount(fileData.size());
        
        //调用isp服务返回结果
        JSONObject result = null;
        
        try {
        	//调用isp服务接口
        	long start = System.currentTimeMillis();
        	String invokeUrl = dmsConfigGroup.getOcrServer() + IspV2ConfigUtil.getCommonUri();
        	result = RestTemplateUtil.postWithJson(invokeUrl, ispParams);
        	
        	log.info("OCR处理-瑕疵页矫正-调用接口结束 调用结果:{}", result);
        	
        	//更新isp调用对象
        	dmsIspInvoking.setOutput(result.toJSONString());
            dmsIspInvoking.setRequestTime(System.currentTimeMillis() - start);
            
            //判断是否执行成功
            if(result != null) {
            	GoOcrResult ocrResult = GoOcrResult.buildOcrResult(result);
            	isSucess = (ocrResult.getCode() == GoOcrHttpStatus.OK.getCode());
            }
        }
        catch(Exception e) {
        	log.error("OCR处理-瑕疵页矫正-调用接口失败 jgrybm:{},异常信息：{}", jgrybm, e.getMessage());
        	
        	//更新isp调用对象
        	dmsIspInvoking.setOutput(e.getMessage());
            dmsIspInvoking.setRequestStatus("2");
        }
        
        try {
        	//插入isp调用记录
        	dmsIspInvoking.setAddTime(new Date());
            dmsIspInvokingService.save(dmsIspInvoking);
		}
        catch (Exception e) {
        	log.error("OCR处理-瑕疵页矫正-插入调用记录失败,jgrybm:{},异常信息：{}", jgrybm, e.getMessage());
		}
        
        return isSucess;
	}
	
	/**
	 * 调用ocr服务
	 * @param configGroup ConfigGroupDO 卷宗配置
	 * @param serviceCode String 服务代码
	 * @param fileData JSONArray 文件数据
	 * @param returnUrl String 回调地址
	 * @param jobId String 格式化审查任务 
	 * @param jgrybm String 监管人员编码
	 * @param extendParams String 扩展参数
	 * @return JSONObject
	 */
	@Override
	public JSONObject call(ConfigGroupDO configGroup, String serviceCode, JSONArray fileData,
			String returnUrl, String jobId, String jgrybm, JSONObject extendParams) {
      
        //构建isp调用对象
        IspInvokingDO dmsIspInvoking = new IspInvokingDO();	        
        dmsIspInvoking.setJobId(jobId);
        dmsIspInvoking.setAddress(configGroup.getOcrServer());
        dmsIspInvoking.setJgrybm(jgrybm);
        dmsIspInvoking.setRequestFrom(OcrConstants.OCR_REQUEST_FROM_OTHER);
        dmsIspInvoking.setServerIp(IpUtil.getHostIp());        
        dmsIspInvoking.setFileCount(fileData.size());
        
        //当前用户
        SessionUser user = SessionUserUtil.getSessionUser();
    	
    	//构建服务请求参数
        JSONObject serviceRequestParams = GoUtil.buildBaseServiceRequestParmas(user.getIdCard(),
        		jobId, fileData, returnUrl);
        
        //扩展参数
        if(extendParams != null && !extendParams.isEmpty()) {
        	serviceRequestParams.putAll(extendParams);
        }
        
        //设置接口调用类型
        GoInvokeTypeEnum invokeType = GoInvokeTypeEnum.getOcrInvokeTypeEnumByCode(serviceCode);
        if(invokeType != null) {
        	dmsIspInvoking.setInvokeType(invokeType.getInvokeType());
            dmsIspInvoking.setCheckType(invokeType.getInvokeType());
        }
    	
    	//调用ocr接口(空白页、重复页、瑕疵页检测)
        JSONObject result = ocrHandler.executeOcrInvoke(configGroup, serviceCode, serviceRequestParams, dmsIspInvoking);
        
        return result;
	}
}
