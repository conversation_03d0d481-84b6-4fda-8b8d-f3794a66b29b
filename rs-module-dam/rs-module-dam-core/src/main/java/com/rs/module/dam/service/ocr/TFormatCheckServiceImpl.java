package com.rs.module.dam.service.ocr;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.orm.mybatis.util.WrapUtil;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.R;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.dam.constant.DamConstants;
import com.rs.module.dam.constant.RedisConstants;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.entity.ocr.FormatCheckJobDO;
import com.rs.module.dam.entity.ocr.FormatMinceJobDO;
import com.rs.module.dam.entity.prisoner.PrisonerCatalogDO;
import com.rs.module.dam.ocr.strategy.OcrStrategy;
import com.rs.module.dam.ocr.strategy.OcrStrategyFactory;
import com.rs.module.dam.ocr.util.OcrUtil;
import com.rs.module.dam.service.material.MaterialInfoService;
import com.rs.module.dam.service.prisoner.PrisonerCatalogService;
import com.rs.module.dam.util.RedisTemplateUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 格式化审查服务实现类
 * <AUTHOR>
 * @date 2024年6月12日
 */
@Service
@Slf4j
public class TFormatCheckServiceImpl implements TFormatCheckService {

	@Autowired
	private FormatCheckJobService checkJobService;
	
	@Autowired
	private FormatMinceJobService minceJobService;
	
	@Autowired
	private MaterialInfoService dossierMaterialInfoService;
	
	@Autowired
	private PrisonerCatalogService dossierAjCatalogService;
	
	@Autowired
	private FormatCheckResultService dmsFormatCheckResultService;
	
	@Autowired
	private UploadFileService uploadFileService;
	
	@Autowired
    RedisTemplateUtil redisTemplateUtil;
	
	/**
	 * 获取格式化审查发起检测的所有子任务集合
	 * @param jobId String 主任务Id
	 * @return List<FormatMinceJobDO>
	 * <AUTHOR>
	 */
	@Override
	public List<FormatMinceJobDO> getCheckedMinceJobList(String jobId) {
		QueryWrapper<FormatMinceJobDO> allMinceJobWrapper = WrapUtil.eq(FormatMinceJobDO.class,
				new String[]{"job_id", "show_job"}, new String[] {jobId, DamConstants.CHECK_FORMAT_RESULT_SHOW});
		return minceJobService.list(allMinceJobWrapper);
	}

	/**
	 * 获取格式化审查指定检测类型的子任务集合
	 * @param jobId String 主任务Id
	 * @param checkTypes String... 检测类型动态参数
	 * @return List<FormatMinceJobDO>
	 * <AUTHOR>
	 */
	@Override
	public List<FormatMinceJobDO> getMinceJobListByCheckTypes(String jobId, String... checkTypes) {
		QueryWrapper<FormatMinceJobDO> minceJobWrapper = WrapUtil.eq(FormatMinceJobDO.class, "job_id", jobId)
				.in("check_type", Arrays.asList(checkTypes));
		return minceJobService.list(minceJobWrapper);
	}
	
	/**
	 * 获取格式化审查关联的材料
	 * @param jgrybm String 监管人员编码
	 * @param jobType String 任务类型(0:未编目任务, 1:已编目任务, 2:智能阅卷任务)
	 * @return List<MaterialInfoDO>
	 * <AUTHOR>
	 */
	@Override
	public List<MaterialInfoDO> getFormatCheckMaterialInfoList(String jgrybm, String jobType){
		String sqlSelect = "id, material_file, page_count, part_catalog_id, catalog_id, type";
		return getFormatCheckMaterialInfoList(jgrybm, jobType, sqlSelect);
	}
	
	/**
	 * 获取格式化审查关联的材料
	 * @param jgrybm String 监管人员编码
	 * @param jobType String 任务类型(0:未编目任务, 1:已编目任务, 2:智能阅卷任务)
	 * @param sqlSelect String 返回的字段
	 * @return List<DossierMaterialInfo>
	 * <AUTHOR>
	 */
	@Override
	public List<MaterialInfoDO> getFormatCheckMaterialInfoList(String jgrybm, String jobType, String sqlSelect) {
		QueryWrapper<MaterialInfoDO> materialWrapper = WrapUtil.eq(MaterialInfoDO.class, "jgrybm", jgrybm);
		if(DamConstants.FORMAT_CHECK_REVIEWS.equals(jobType)) {
			materialWrapper.in("type", "2", "3");
		}
		else {
			materialWrapper.eq("type", jobType);
		}
		
		//指定返回的字段
		if(StringUtil.isNotEmpty(sqlSelect)) {
			materialWrapper.select(sqlSelect);
		}
		
		return dossierMaterialInfoService.list(materialWrapper);
	}
	
	/**
	 * 获取监管人员编目信息
	 * @param jgrybm String 监管人员编码
	 * @return DossierAjCatalog
	 * <AUTHOR>
	 */
	@Override
	public PrisonerCatalogDO getPrisonerCatalog(String jgrybm) {
		QueryWrapper<PrisonerCatalogDO> catalogWrapper = WrapUtil.eq(PrisonerCatalogDO.class, "jgrybm", jgrybm);
		return dossierAjCatalogService.getOne(catalogWrapper);
	}
	
	/**
	 * 验证格式化审查材料是否合法
	 * @param checkJob DmsFormatCheckJob 格式化审查任务
	 * @param dossierMaterialInfoList List<DossierMaterialInfo> 格式化审查关联的材料
	 * @return boolean
	 * <AUTHOR>
	 */
	@Override
	public boolean validateFormatCheckMaterials(FormatCheckJobDO checkJob,
			List<MaterialInfoDO> dossierMaterialInfoList) {
		if(CollectionUtil.isNull(dossierMaterialInfoList)) {
			log.error("ocr处理-格式化审查-编目组卷页面没有可以审查的材料，{} 的格式化审查任务 {} 结束",
					checkJob.getJgrybm(), checkJob.getId());
			redisTemplateUtil.sRem(RedisConstants.FORMAT_CHECKING, checkJob.getJgrybm() + "--" + checkJob.getJobType());
			checkJob.setStatus(DamConstants.JOB_STATE_ERROR);
			checkJob.setEndTime(new Date());
			checkJobService.updateById(checkJob);
			return false;
		}
		
		return true;
	}
	
	/**
	 * 获取格式化审查任务指定类型已经检测完成的文件Id集合
	 * @param jobId String 主任务Id
	 * @param jgrybm String 监管人员编码
	 * @param checkType String 检测类型
	 * @return public List<String>
	 * <AUTHOR>
	 */
	@Override
	public List<String> getJobCheckResultFileIdList(String jobId, String jgrybm, String checkType){
		Map<String,Object> queryParams = new HashMap<>();
	    queryParams.put("jobId", jobId);
	    queryParams.put("jgrybm", jgrybm);
	    queryParams.put("type", checkType);
	    
	    return dmsFormatCheckResultService.selectFileIdByParams(queryParams);
	}
	
	/**
	 * 获取监管人员指定类型检测已经完成的文件Id集合
	 * @param jgrybm String 监管人员编码
	 * @param checkType String 检测类型
	 * @return public List<String>
	 * <AUTHOR>
	 */
	@Override
	public List<String> getPrisonerCheckResultFileIdList(String jgrybm, String jobType, String checkType){
		Map<String,Object> queryParams = new HashMap<>();
	    queryParams.put("jgrybm", jgrybm);
	    queryParams.put("jobType", jobType);
	    queryParams.put("type", checkType);
	    
	    return dmsFormatCheckResultService.selectFileIdByParams(queryParams);
	}
	
	/**
	 * 图片瑕疵页矫正处理
	 * @param materialId String 材料Id
	 * @param fileId String 文件Id
	 * @return CommonResult
	 */
	@Override
	public CommonResult<?> improveImageQuality(String materialId, String fileId) {
		boolean improveSuccess = true;
		
		//矫正后的地址
		String reviseUrl = null;
		
		//矫正提示信息
		List<String> msgList = new ArrayList<>();
		
		MaterialInfoDO dossierMaterialInfo = dossierMaterialInfoService.getById(materialId);
		if(dossierMaterialInfo != null) {
			JSONArray materialFile = JSONArray.parseArray(dossierMaterialInfo.getMaterialFile());
			for(int i = 0; i < materialFile.size(); i ++) {
				JSONObject file = materialFile.getJSONObject(i);
				String id = file.getString("id");
				if(id.equals(fileId)) {
					String url = file.getString("url");
					
					//图片歪斜矫正
					String askew = file.getString(DamConstants.MaterialColumn.ASKEW);
					if(StringUtil.getBoolean(askew)) {
						String angle = file.getString(DamConstants.MaterialColumn.ANGLE);
						if(StringUtil.isNotEmpty(angle)) {
							float angleFloat = Float.parseFloat(angle);
							int angleInt = (int)angleFloat;
							R rotate = uploadFileService.rotate(url, angleInt);
							log.info("图片瑕疵页矫正返回结果: {}", rotate.toJSONString());
							reviseUrl = OcrUtil.getUrlFromR(rotate);
							if(StringUtil.isNotEmpty(reviseUrl)){
								msgList.add("图片歪斜矫正成功");
							}
							else {
								improveSuccess = false;
								msgList.add("图片歪斜矫正失败！" + rotate.getString("msg"));
							}
						}
					}
					
					//图片阴影矫正
					String spot = file.getString(DamConstants.MaterialColumn.SPOT);
					if(StringUtil.getBoolean(spot)) {
						OcrStrategy ocrStrategy = OcrStrategyFactory.getOcrStrategy(OcrUtil.getOcrType());
						boolean isSuccess = ocrStrategy.reviseImage(materialId, fileId,
								(StringUtil.isEmpty(reviseUrl) ? url : reviseUrl), dossierMaterialInfo.getJgrybm());
						if(isSuccess) {
							msgList.add("图片阴影矫正OCR请求发起成功");
						}
						else {
							improveSuccess = false;
							msgList.add("图片阴影矫正OCR请求发起失败");
						}
					}
					
					//矫正成功
					if(improveSuccess) {
						OcrUtil.clearFileQuality(file);
						
						//更新文件矫正后的地址
						if(StringUtil.isNotEmpty(reviseUrl)){
							file.put("url", reviseUrl);
						}
						
						dossierMaterialInfo.setMaterialFile(materialFile.toJSONString());
						dossierMaterialInfoService.updateById(dossierMaterialInfo);
					}
					
					break;
				}
			}
		}
		
		//返回处理结果
		if(improveSuccess) {
			return CommonResult.success(String.join("|", msgList));
		}
		else {
			return CommonResult.error(String.join("|", msgList));
		}
	}
}
