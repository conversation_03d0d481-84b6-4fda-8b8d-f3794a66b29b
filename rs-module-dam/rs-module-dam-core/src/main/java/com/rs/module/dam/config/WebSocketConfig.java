package com.rs.module.dam.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

/**
 * WebSocket配置类
 * <AUTHOR>
 * @date 2025年4月22日
 */
@Configuration
public class WebSocketConfig {

	/**
	 * 自动注册使用@ServerEndpoint注解声明的websocket endpoint
	 * @return ServerEndpointExporter
	 */
	@Bean
	public ServerEndpointExporter serverEndpointExporter() {
		return new ServerEndpointExporter();
	}
}
