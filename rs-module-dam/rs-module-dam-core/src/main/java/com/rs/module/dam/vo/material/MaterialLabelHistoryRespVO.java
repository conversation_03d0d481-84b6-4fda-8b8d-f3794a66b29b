package com.rs.module.dam.vo.material;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 卷宗材料标签历史 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MaterialLabelHistoryRespVO extends BaseVO implements TransPojo{
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("材料Id")
    private String materialId;
    @ApiModelProperty("材料目录Id")
    private String catalogId;
    @ApiModelProperty("材料文件图片Id")
    private String fileId;
}
