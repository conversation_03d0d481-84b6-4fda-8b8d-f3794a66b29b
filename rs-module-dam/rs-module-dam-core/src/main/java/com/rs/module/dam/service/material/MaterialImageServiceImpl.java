package com.rs.module.dam.service.material;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.dao.material.MaterialImageDao;
import com.rs.module.dam.entity.material.MaterialImageDO;
import com.rs.module.dam.vo.material.MaterialImageListReqVO;
import com.rs.module.dam.vo.material.MaterialImagePageReqVO;
import com.rs.module.dam.vo.material.MaterialImageSaveReqVO;


/**
 * 卷宗材料图片 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MaterialImageServiceImpl extends BaseServiceImpl<MaterialImageDao, MaterialImageDO> implements MaterialImageService {

    @Resource
    private MaterialImageDao materialImageDao;

    @Override
    public String createMaterialImage(MaterialImageSaveReqVO createReqVO) {
        // 插入
        MaterialImageDO materialImage = BeanUtils.toBean(createReqVO, MaterialImageDO.class);
        materialImageDao.insert(materialImage);
        // 返回
        return materialImage.getId();
    }

    @Override
    public void updateMaterialImage(MaterialImageSaveReqVO updateReqVO) {
        // 校验存在
        validateMaterialImageExists(updateReqVO.getId());
        // 更新
        MaterialImageDO updateObj = BeanUtils.toBean(updateReqVO, MaterialImageDO.class);
        materialImageDao.updateById(updateObj);
    }

    @Override
    public void deleteMaterialImage(String id) {
        // 校验存在
        validateMaterialImageExists(id);
        // 删除
        materialImageDao.deleteById(id);
    }

    private void validateMaterialImageExists(String id) {
        if (materialImageDao.selectById(id) == null) {
            throw new ServerException("卷宗材料图片数据不存在");
        }
    }

    @Override
    public MaterialImageDO getMaterialImage(String id) {
        return materialImageDao.selectById(id);
    }

    @Override
    public PageResult<MaterialImageDO> getMaterialImagePage(MaterialImagePageReqVO pageReqVO) {
        return materialImageDao.selectPage(pageReqVO);
    }

    @Override
    public List<MaterialImageDO> getMaterialImageList(MaterialImageListReqVO listReqVO) {
        return materialImageDao.selectList(listReqVO);
    }

    /**
     * 更新材料图片地址
     * @param materialImage MaterialImageDO 材料
     */
    @Override
    public void updateUrlById(MaterialImageDO materialImage) {
    	materialImageDao.updateUrlById(materialImage);
    }
}
