package com.rs.module.dam.strategy.impl;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rs.module.dam.entity.material.MaterialImageDO;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.service.material.MaterialImageService;
import com.rs.module.dam.strategy.OperateTypeService;

/**
 * 图片粘贴操作
 * <AUTHOR>
 * @date 2025年4月18日
 */
@Service(value = "damCutImageOperate")
public class DamCutImageOperate implements OperateTypeService {

	@Resource
    private MaterialImageService materialImageService;

    @Override
    public Set<String> operateByType(JSONObject materialObject) {
    	MaterialInfoDO materialInfo = JSONObject.toJavaObject(materialObject, MaterialInfoDO.class);
        String materialInfoId = materialInfo.getId();
        
        // 改变图片顺序或改变图片所属材料
        JSONArray cutMaterialFileArray = materialObject.getJSONArray("deleteMaterialFile");

        int size = cutMaterialFileArray.size();
        for (int j = 0; j < size; j++) {
        	QueryWrapper<MaterialImageDO> wrapper = new QueryWrapper<>();
            wrapper.eq("material_id", materialInfoId);
            List<MaterialImageDO> materialImageOrderList = materialImageService.list(wrapper);
            JSONObject cutMaterialFile = cutMaterialFileArray.getJSONObject(j);
            Boolean currentMaterial = cutMaterialFile.getBoolean("currentMaterial");
            
            // 1、原材料粘贴
            if (currentMaterial) {
                String imageId = cutMaterialFile.getString("id");
                int cutBeforeXh = cutMaterialFile.getIntValue("cutBeforeXh");
                int cutAfterXh = cutMaterialFile.getIntValue("xh");
                if (Objects.equals(cutAfterXh, cutBeforeXh)) {
                    // 位置不变: 不处理
                } else if (cutBeforeXh < cutAfterXh) {
                    // 粘贴位置及到原位置中间图片依次向前移动
                    boolean after = false;
                    for (MaterialImageDO materialImageOrder : materialImageOrderList) {
                        String id = materialImageOrder.getId();
                        Integer xh = materialImageOrder.getXh();
                        if (Objects.equals(id, imageId)) {
                            materialImageOrder.setXh(cutAfterXh);
                            after = true;
                        } else if (after && xh <= cutAfterXh) {
                            materialImageOrder.setXh(xh - 1);
                        }
                    }
                    materialImageService.updateBatchById(materialImageOrderList);
                } else if (cutBeforeXh > cutAfterXh) {
                    //  粘贴位置到原位置中间的图片依次向后移动
                    for (MaterialImageDO materialImageOrder : materialImageOrderList) {
                        String id = materialImageOrder.getId();
                        Integer xh = materialImageOrder.getXh();
                        if (Objects.equals(id, imageId)) {
                            materialImageOrder.setXh(cutAfterXh);
                        } else if (xh >= cutAfterXh && xh <= cutBeforeXh) {
                            materialImageOrder.setXh(xh + 1);
                        }
                    }
                    materialImageService.updateBatchById(materialImageOrderList);
                }
            }
            
            // 2、粘贴到其他材料中
            else {                
                MaterialImageDO dossierMaterialImage1 = JSON.toJavaObject(cutMaterialFile, MaterialImageDO.class);
                dossierMaterialImage1.setMaterialId(materialInfoId);

                String originalImageId = cutMaterialFile.getString("id");
                String originalMaterialId = cutMaterialFile.getString("originalMaterialId");

                QueryWrapper<MaterialImageDO> imageWrapper = new QueryWrapper<>();
                imageWrapper.eq("id", originalImageId);
                List<MaterialImageDO> list = materialImageService.list(wrapper);
                Optional<MaterialImageDO> first = list.stream().findFirst();
                // 删除原图片完后, 将原材料图片排序
                if (first.isPresent()) {
                    materialImageService.removeById(originalImageId);
                    List<MaterialImageDO> materialImageList = getOriginalImageList(originalMaterialId, cutMaterialFile);
                    materialImageService.updateBatchById(materialImageList);
                }

                int cutAfterXh = cutMaterialFile.getIntValue("xh");
                // 2.2、新的材料中插入图片 粘贴位置及以后图片依次向后移动
                for (MaterialImageDO currentMaterialImage : materialImageOrderList) {
                    Integer xh = currentMaterialImage.getXh();
                    if (xh >= cutAfterXh) {
                        currentMaterialImage.setXh(xh + 1);
                    }
                }
                materialImageService.updateBatchById(materialImageOrderList);
                materialImageService.save(dossierMaterialImage1);
            }
        }

        return null;
    }

    private List<MaterialImageDO> getOriginalImageList(String originalMaterialId, JSONObject deleteMaterialFile) {
        QueryWrapper<MaterialImageDO> wrapper = new QueryWrapper<>();
        wrapper.eq("material_id", originalMaterialId);
        List<MaterialImageDO> materialImageList = materialImageService.list(wrapper);
        Integer deleteXh = deleteMaterialFile.getInteger("cutBeforeXh");
        for (MaterialImageDO dossierMaterialImage : materialImageList) {
            Integer xh = dossierMaterialImage.getXh();
            if (xh > deleteXh ) {
                dossierMaterialImage.setXh(xh - 1);
            }
        }
        return materialImageList;
    }
}
