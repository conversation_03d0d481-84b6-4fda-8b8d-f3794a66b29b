package com.rs.module.dam.ocr.gosuncn;

import java.util.HashSet;
import java.util.Set;
import java.util.function.Consumer;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.StringUtil;
import com.rs.module.dam.constant.DamConstants;
import com.rs.module.dam.entity.sys.ConfigGroupDO;

import com.rs.module.dam.ocr.gosuncn.GoOcrCallbackParam.SerDatas;

/**
 * gosuncn-高新兴工具类
 * <AUTHOR>
 * @date 2025年4月26日
 */
public class GoUtil {

	/**
	 * 构建isp请求参数
	 * @param configGroup ConfigGroupDO 系统配置
	 * @param serviceCode String 服务代码
	 * @param serviceRequestParams JSONObject 服务请求参数
	 * @return
	 * <AUTHOR>
	 * @date 2024年3月20日
	 */
	public static JSONObject buildIspParams(ConfigGroupDO configGroup, String serviceCode, JSONObject serviceRequestParams) {
		JSONObject ispParams = new JSONObject();
		
		ispParams.put("appId", configGroup.getAppId());
        ispParams.put("appKey", configGroup.getAppKey());
        ispParams.put("secretKey", configGroup.getSecretKey());
        ispParams.put("serCode", serviceCode);
        ispParams.put("serviceRequestParams", serviceRequestParams);
        
		return ispParams;
	}
	
	/**
	 * 构建基本的服务请求参数
	 * @param userId String 用户Id
	 * @param batchId String 批次Id
	 * @param datas JSONArray 数据数组
	 * @param returnUrl String 回调地址
	 * @return JSONObject
	 * <AUTHOR>
	 * @date 2024年3月20日
	 */
	public static JSONObject buildBaseServiceRequestParmas(String userId, String batchId,
			JSONArray datas, String returnUrl) {
		JSONObject serviceRequestParams = new JSONObject();
		
		serviceRequestParams.put("userId", userId);
		serviceRequestParams.put("batchId", batchId);
		serviceRequestParams.put("datas", datas);
		
		//回调地址
		if(StringUtil.isNotEmpty(returnUrl)) {
			serviceRequestParams.put("returnURL", returnUrl);
		}
        
		return serviceRequestParams;
	}
	
	/**
	 * 根据属性值删除字符串Json数组中的元素
	 * @param jsonArrStr String 字符串Json数组
	 * @param propName String 属性名称
	 * @param propValue String 属性值
	 * @return String
	 * <AUTHOR>
	 * @date 2024年4月30日
	 */
	public static String deleteJsonArrayStrByProp(String jsonArrStr, String propName, String propValue) {
		try {
			JSONArray jsonArray = JSONArray.parseArray(jsonArrStr);
			JSONArray resultJsonArr = new JSONArray();
			for(int i = 0; i < jsonArray.size(); i ++) {
				if(!jsonArray.getJSONObject(i).getString(propName).equals(propValue)) {
					resultJsonArr.add(jsonArray.getJSONObject(i));
				}
			}
			return resultJsonArr.toJSONString();
		}
		catch(Exception e) {
			return jsonArrStr;
		}
	}
	
	/**
	 * 将Ocr回调返回结果转换成数组
	 * @param serDatas SerDatas Ocr回调数据
	 * @return JSONArray
	 * <AUTHOR>
	 * @date 2024年5月10日
	 */
	public static JSONArray getOcrResultsArray(SerDatas serDatas) {
		JSONArray resultArr = new JSONArray();
		if(serDatas.getResults() != null) {
			if(serDatas.getResults() instanceof JSONArray) {
				return (JSONArray)serDatas.getResults();
			}
		}			
		return resultArr;
	}
	
	/**
	 * 将Ocr回调返回结果转换成字符串
	 * @param serDatas SerDatas Ocr回调数据
	 * @return JSONArray
	 * <AUTHOR>
	 * @date 2024年5月10日
	 */
	public static String getOcrResultsString(SerDatas serDatas) {
		if(serDatas.getResults() == null) {
			return null;
		}
		else if(serDatas.getResults() instanceof JSONArray) {
			return getOcrResultsArray(serDatas).toJSONString();
		}
		else if(serDatas.getResults() instanceof String) {
			return (String)serDatas.getResults();
		}
		else {
			return serDatas.getResults().toString();
		}
	}
	
	/**
	 * 获取Ocr回调返回数据中所有的材料Id信息(包括异常材料Id)，用于查询时过滤数据
	 * @param serDatas SerDatas Ocr回调数据
	 * @return Set<String>
	 * <AUTHOR>
	 * @date 2024年5月9日
	 */
	public static Set<String> getOcrResultMaterialIds(SerDatas serDatas){
		Set<String> materialInfoIds = new HashSet<>();
		try {
			JSONArray resultArr = getOcrResultsArray(serDatas);				
			if(!resultArr.isEmpty()) {
				for(int i = 0; i < resultArr.size(); i ++) {
					JSONObject result = resultArr.getJSONObject(i);
					String fileId = result.getString("fileId");
					String[] fileIdArr = fileId.split("_");
					if(fileIdArr.length > 1) {
						materialInfoIds.add(fileIdArr[1]);
					}
				}
			}
			if(!serDatas.getFailed_ids().isEmpty()) {
				for(int i = 0; i < serDatas.getFailed_ids().size(); i ++) {
					String fileId = serDatas.getFailed_ids().getString(i);
					String[] fileIdArr = fileId.split("_");
					if(fileIdArr.length > 1) {
						materialInfoIds.add(fileIdArr[1]);
					}
				}
			}
		}
		catch(Exception e) {}
		
		return materialInfoIds;
	}
	
	/**
	 * 根据格式化审查结果获取任务状态
	 * @param result JSONObject 格式化审查结果
	 * @return String
	 * <AUTHOR>
	 * @date 2024年6月6日
	 */
	public static String getMinceJobStatusByResult(JSONObject result) {	
		String minceJobStatus = DamConstants.JOB_STATE_NO;
		
		if(result != null) {
			GoOcrResult ocrResult = GoOcrResult.buildOcrResult(result);
    		
    		//返回代码为空时
    		if(null == ocrResult.getCode()) {
    			minceJobStatus = DamConstants.JOB_STATE_ERROR;
        	}
    		
    		//非处理成功或者资源不足时
        	else if(ocrResult.getCode() != GoOcrHttpStatus.OK.getCode()
        			&& ocrResult.getCode() != GoOcrHttpStatus.INSUFFICIENT_RESOURCE.getCode()) {
        		minceJobStatus = DamConstants.JOB_STATE_ERROR;
        	}
		}
		else {
			minceJobStatus = DamConstants.JOB_STATE_ERROR;
		}
		
		return minceJobStatus;
	}
	
	/**
	 * 检测文书节点信息
	 * @param file JSONObject 文件JSON
	 * @param firstNodeName String 第一个节点名称
	 * @param consumer Consumer<?>
	 * <AUTHOR>
	 * @date 2024年6月13日
	 */
	public static void checkWsNodeInfo(JSONObject file, String firstNodeName, Consumer<?> consumer) {
		checkWsNodeInfo(file, firstNodeName, "content", consumer);
	}
	
	/**
	 * 检测文书节点信息
	 * @param file JSONObject 文件JSON
	 * @param firstNodeName String 第一个节点名称
	 * @param nextNodeName String 第二个节点名称
	 * @param consumer Consumer<?>
	 * <AUTHOR>
	 * @date 2024年6月13日
	 */
	public static void checkWsNodeInfo(JSONObject file, String firstNodeName, String nextNodeName, Consumer<?> consumer) {
		JSONObject firstNode = file.getJSONObject(firstNodeName);
		if(firstNodeName != null) {
			String targetValue = firstNode.getString(nextNodeName);
			if(StringUtil.isEmpty(targetValue)) {
				consumer.accept(null);
			}
		}
	}
}
