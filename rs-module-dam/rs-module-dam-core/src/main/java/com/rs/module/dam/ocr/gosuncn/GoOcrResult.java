package com.rs.module.dam.ocr.gosuncn;

import com.alibaba.fastjson.JSONObject;

/**
 * gosuncn-高新兴OCR响应结果类
 * <AUTHOR>
 * @date 2025年4月26日
 */
public class GoOcrResult {

	//响应消息
	private String message;
	
	//响应代码
	private Integer code;

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}
	
	/**
	 * 将Json对象转换成标准的OCR结果对象
	 * @param result JSONObject Json对象
	 * @return GosuncnOcrResult
	 * <AUTHOR>
	 * @date 2024年3月21日
	 */
	public static GoOcrResult buildOcrResult(JSONObject result) {
		return result.toJavaObject(GoOcrResult.class);
	}
}
