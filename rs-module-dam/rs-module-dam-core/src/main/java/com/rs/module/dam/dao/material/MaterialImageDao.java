package com.rs.module.dam.dao.material;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.material.MaterialImageDO;
import com.rs.module.dam.vo.material.MaterialImageListReqVO;
import com.rs.module.dam.vo.material.MaterialImagePageReqVO;

/**
* 卷宗材料图片 Dao
*
* <AUTHOR>
*/
@Mapper
public interface MaterialImageDao extends IBaseDao<MaterialImageDO> {


    default PageResult<MaterialImageDO> selectPage(MaterialImagePageReqVO reqVO) {
        Page<MaterialImageDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<MaterialImageDO> wrapper = new LambdaQueryWrapperX<MaterialImageDO>()
            .eqIfPresent(MaterialImageDO::getMaterialId, reqVO.getMaterialId())
            .eqIfPresent(MaterialImageDO::getXh, reqVO.getXh())
            .likeIfPresent(MaterialImageDO::getFileName, reqVO.getFileName())
            .eqIfPresent(MaterialImageDO::getUrl, reqVO.getUrl())
            .eqIfPresent(MaterialImageDO::getIsRepeat, reqVO.getIsRepeat())
            .eqIfPresent(MaterialImageDO::getRepeatMark, reqVO.getRepeatMark())
            .eqIfPresent(MaterialImageDO::getMissSeal, reqVO.getMissSeal())
            .eqIfPresent(MaterialImageDO::getIsRegular, reqVO.getIsRegular())
            .eqIfPresent(MaterialImageDO::getMissSignature, reqVO.getMissSignature())
            .eqIfPresent(MaterialImageDO::getBlankSpace, reqVO.getBlankSpace())
            .eqIfPresent(MaterialImageDO::getInfoPass, reqVO.getInfoPass())
            .eqIfPresent(MaterialImageDO::getMissFingerprint, reqVO.getMissFingerprint())
            .eqIfPresent(MaterialImageDO::getUnidentify, reqVO.getUnidentify())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(MaterialImageDO::getAddTime);
        }
        Page<MaterialImageDO> materialImagePage = selectPage(page, wrapper);
        return new PageResult<>(materialImagePage.getRecords(), materialImagePage.getTotal());
    }
    default List<MaterialImageDO> selectList(MaterialImageListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MaterialImageDO>()
            .eqIfPresent(MaterialImageDO::getMaterialId, reqVO.getMaterialId())
            .eqIfPresent(MaterialImageDO::getXh, reqVO.getXh())
            .likeIfPresent(MaterialImageDO::getFileName, reqVO.getFileName())
            .eqIfPresent(MaterialImageDO::getUrl, reqVO.getUrl())
            .eqIfPresent(MaterialImageDO::getIsRepeat, reqVO.getIsRepeat())
            .eqIfPresent(MaterialImageDO::getRepeatMark, reqVO.getRepeatMark())
            .eqIfPresent(MaterialImageDO::getMissSeal, reqVO.getMissSeal())
            .eqIfPresent(MaterialImageDO::getIsRegular, reqVO.getIsRegular())
            .eqIfPresent(MaterialImageDO::getMissSignature, reqVO.getMissSignature())
            .eqIfPresent(MaterialImageDO::getBlankSpace, reqVO.getBlankSpace())
            .eqIfPresent(MaterialImageDO::getInfoPass, reqVO.getInfoPass())
            .eqIfPresent(MaterialImageDO::getMissFingerprint, reqVO.getMissFingerprint())
            .eqIfPresent(MaterialImageDO::getUnidentify, reqVO.getUnidentify())
        .orderByDesc(MaterialImageDO::getAddTime));
    }

    /**
     * 更新材料图片地址
     * @param materialImage MaterialImageDO 材料
     */
    void updateUrlById(MaterialImageDO materialImage);
}
