package com.rs.module.dam.service.ocr;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.dao.ocr.CatalogMarkDao;
import com.rs.module.dam.entity.ocr.CatalogMarkDO;
import com.rs.module.dam.vo.ocr.CatalogMarkListReqVO;
import com.rs.module.dam.vo.ocr.CatalogMarkPageReqVO;
import com.rs.module.dam.vo.ocr.CatalogMarkSaveReqVO;


/**
 * ocr相似度归目标识 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CatalogMarkServiceImpl extends BaseServiceImpl<CatalogMarkDao, CatalogMarkDO> implements CatalogMarkService {

    @Resource
    private CatalogMarkDao catalogMarkDao;

    @Override
    public String createCatalogMark(CatalogMarkSaveReqVO createReqVO) {
        // 插入
        CatalogMarkDO catalogMark = BeanUtils.toBean(createReqVO, CatalogMarkDO.class);
        catalogMarkDao.insert(catalogMark);
        // 返回
        return catalogMark.getId();
    }

    @Override
    public void updateCatalogMark(CatalogMarkSaveReqVO updateReqVO) {
        // 校验存在
        validateCatalogMarkExists(updateReqVO.getId());
        // 更新
        CatalogMarkDO updateObj = BeanUtils.toBean(updateReqVO, CatalogMarkDO.class);
        catalogMarkDao.updateById(updateObj);
    }

    @Override
    public void deleteCatalogMark(String id) {
        // 校验存在
        validateCatalogMarkExists(id);
        // 删除
        catalogMarkDao.deleteById(id);
    }

    private void validateCatalogMarkExists(String id) {
        if (catalogMarkDao.selectById(id) == null) {
            throw new ServerException("ocr相似度归目标识数据不存在");
        }
    }

    @Override
    public CatalogMarkDO getCatalogMark(String id) {
        return catalogMarkDao.selectById(id);
    }

    @Override
    public PageResult<CatalogMarkDO> getCatalogMarkPage(CatalogMarkPageReqVO pageReqVO) {
        return catalogMarkDao.selectPage(pageReqVO);
    }

    @Override
    public List<CatalogMarkDO> getCatalogMarkList(CatalogMarkListReqVO listReqVO) {
        return catalogMarkDao.selectList(listReqVO);
    }


}
