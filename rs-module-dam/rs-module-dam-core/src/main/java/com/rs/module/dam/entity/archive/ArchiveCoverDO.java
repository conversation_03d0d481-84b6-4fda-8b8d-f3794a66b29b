package com.rs.module.dam.entity.archive;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 卷宗封面 DO
 *
 * <AUTHOR>
 */
@TableName("dam_archive_cover")
@KeySequence("dam_archive_cover_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ArchiveCoverDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 卷宗名称
     */
    private String jzmc;
    /**
     * 卷名
     */
    private String jm;
    /**
     * 组卷分卷目录的ID（如诉讼文书卷的目录ID）
     */
    private String partCatalogId;
    /**
     * 分卷类型（1是快速组卷，0是编目组卷）
     */
    private String fjlx;
    /**
     * 分卷PDF地址
     */
    private String pdfUrl;
    /**
     * 双层PDF地址
     */
    private String doublePdfUrl;
    /**
     * pdf图片地址
     */
    private String imageUrl;
    /**
     * 预览图片地址
     */
    private String previewUrl;
    /**
     * 封面页数
     */
    private Integer coverPage;
    /**
     * 序号，用于排序
     */
    private Integer xh;
    /**
     * 姓名
     */
    private String xm;
    /**
     * 涉嫌罪名
     */
    private String sxzm;
    /**
     * 案件编号
     */
    private String ajbh;
    /**
     * 案件名称
     */
    private String ajmc;
    /**
     * 监室号
     */
    private String jsh;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 组卷时间
     */
    @TableField(exist = false)
    private String zjsj;
    /**
     * 组卷人
     */
    @TableField(exist = false)
    private String zjUser;
    /**
     * 组卷人姓名
     */
    @TableField(exist = false)
    private String zjUserName;
    /**
     * 组卷人机构编号
     */
    @TableField(exist = false)
    private String zjOrgCode;
    /**
     * 组卷人机构名称
     */
    @TableField(exist = false)
    private String zjOrgName;
}
