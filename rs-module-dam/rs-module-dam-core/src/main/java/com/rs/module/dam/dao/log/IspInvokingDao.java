package com.rs.module.dam.dao.log;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.log.IspInvokingDO;
import com.rs.module.dam.vo.log.IspInvokingListReqVO;
import com.rs.module.dam.vo.log.IspInvokingPageReqVO;

/**
* 智能服务调用日志 Dao
*
* <AUTHOR>
*/
@Mapper
public interface IspInvokingDao extends IBaseDao<IspInvokingDO> {


    default PageResult<IspInvokingDO> selectPage(IspInvokingPageReqVO reqVO) {
        Page<IspInvokingDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<IspInvokingDO> wrapper = new LambdaQueryWrapperX<IspInvokingDO>()
            .eqIfPresent(IspInvokingDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(IspInvokingDO::getJobId, reqVO.getJobId())
            .eqIfPresent(IspInvokingDO::getInput, reqVO.getInput())
            .eqIfPresent(IspInvokingDO::getOutput, reqVO.getOutput())
            .eqIfPresent(IspInvokingDO::getSerCode, reqVO.getSerCode())
            .eqIfPresent(IspInvokingDO::getInvokeType, reqVO.getInvokeType())
            .betweenIfPresent(IspInvokingDO::getRequestTime, reqVO.getRequestTime())
            .eqIfPresent(IspInvokingDO::getAddress, reqVO.getAddress())
            .eqIfPresent(IspInvokingDO::getRequestFrom, reqVO.getRequestFrom())
            .eqIfPresent(IspInvokingDO::getRequestStatus, reqVO.getRequestStatus())
            .eqIfPresent(IspInvokingDO::getServerIp, reqVO.getServerIp())
            .eqIfPresent(IspInvokingDO::getCheckType, reqVO.getCheckType())
            .eqIfPresent(IspInvokingDO::getFileCount, reqVO.getFileCount())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(IspInvokingDO::getAddTime);
        }
        Page<IspInvokingDO> ispInvokingPage = selectPage(page, wrapper);
        return new PageResult<>(ispInvokingPage.getRecords(), ispInvokingPage.getTotal());
    }
    default List<IspInvokingDO> selectList(IspInvokingListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<IspInvokingDO>()
            .eqIfPresent(IspInvokingDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(IspInvokingDO::getJobId, reqVO.getJobId())
            .eqIfPresent(IspInvokingDO::getInput, reqVO.getInput())
            .eqIfPresent(IspInvokingDO::getOutput, reqVO.getOutput())
            .eqIfPresent(IspInvokingDO::getSerCode, reqVO.getSerCode())
            .eqIfPresent(IspInvokingDO::getInvokeType, reqVO.getInvokeType())
            .betweenIfPresent(IspInvokingDO::getRequestTime, reqVO.getRequestTime())
            .eqIfPresent(IspInvokingDO::getAddress, reqVO.getAddress())
            .eqIfPresent(IspInvokingDO::getRequestFrom, reqVO.getRequestFrom())
            .eqIfPresent(IspInvokingDO::getRequestStatus, reqVO.getRequestStatus())
            .eqIfPresent(IspInvokingDO::getServerIp, reqVO.getServerIp())
            .eqIfPresent(IspInvokingDO::getCheckType, reqVO.getCheckType())
            .eqIfPresent(IspInvokingDO::getFileCount, reqVO.getFileCount())
        .orderByDesc(IspInvokingDO::getAddTime));    }


    }
