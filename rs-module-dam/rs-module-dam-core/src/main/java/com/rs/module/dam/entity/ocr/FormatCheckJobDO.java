package com.rs.module.dam.entity.ocr;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO_;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * ocr格式化审查任务 DO
 *
 * <AUTHOR>
 */
@TableName("dam_ocr_format_check_job")
@KeySequence("dam_ocr_format_check_job_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FormatCheckJobDO extends BaseDO_ {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 任务状态(0：未完成，1：已完成，2：异常)
     */
    private String status;
    /**
     * 任务开始时间
     */
    private Date startTime;
    /**
     * 任务结束时间
     */
    private Date endTime;
    /**
     * 任务类型(0:未编目任务, 1:已编目任务, 2:智能阅卷任务)
     */
    private String jobType;
    /**
     * 上一次检测任务的Id
     */
    private String lastJobId;
    /**
     * 是否首次发起检测任务
     */
    private String isFirst;
    /**
     * 是否需要进行自动编目组卷(1:是,0:否)
     */
    private String autoCatalog;
    /**
     * 任务执行的最大次数
     */
    private Integer requestLimit;
    /**
     * 请求次数
     */
    private Integer requestCount;

}
