package com.rs.module.dam.service.material;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.dao.material.PdfUploadDataDao;
import com.rs.module.dam.entity.material.PdfUploadDataDO;
import com.rs.module.dam.vo.material.PdfUploadDataListReqVO;
import com.rs.module.dam.vo.material.PdfUploadDataPageReqVO;
import com.rs.module.dam.vo.material.PdfUploadDataSaveReqVO;


/**
 * pdf上传数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PdfUploadDataServiceImpl extends BaseServiceImpl<PdfUploadDataDao, PdfUploadDataDO> implements PdfUploadDataService {

    @Resource
    private PdfUploadDataDao pdfUploadDataDao;

    @Override
    public String createPdfUploadData(PdfUploadDataSaveReqVO createReqVO) {
        // 插入
        PdfUploadDataDO pdfUploadData = BeanUtils.toBean(createReqVO, PdfUploadDataDO.class);
        pdfUploadDataDao.insert(pdfUploadData);
        // 返回
        return pdfUploadData.getId();
    }

    @Override
    public void updatePdfUploadData(PdfUploadDataSaveReqVO updateReqVO) {
        // 校验存在
        validatePdfUploadDataExists(updateReqVO.getId());
        // 更新
        PdfUploadDataDO updateObj = BeanUtils.toBean(updateReqVO, PdfUploadDataDO.class);
        pdfUploadDataDao.updateById(updateObj);
    }

    @Override
    public void deletePdfUploadData(String id) {
        // 校验存在
        validatePdfUploadDataExists(id);
        // 删除
        pdfUploadDataDao.deleteById(id);
    }

    private void validatePdfUploadDataExists(String id) {
        if (pdfUploadDataDao.selectById(id) == null) {
            throw new ServerException("pdf上传数据数据不存在");
        }
    }

    @Override
    public PdfUploadDataDO getPdfUploadData(String id) {
        return pdfUploadDataDao.selectById(id);
    }

    @Override
    public PageResult<PdfUploadDataDO> getPdfUploadDataPage(PdfUploadDataPageReqVO pageReqVO) {
        return pdfUploadDataDao.selectPage(pageReqVO);
    }

    @Override
    public List<PdfUploadDataDO> getPdfUploadDataList(PdfUploadDataListReqVO listReqVO) {
        return pdfUploadDataDao.selectList(listReqVO);
    }
    
    /**
	 * 根据类型获取转换的pdf记录
	 * @param params Map<String, Object> 查询参数
	 * @return List<PdfUploadDataDO>
	 */
    @Override
    public List<PdfUploadDataDO> selectConvertPdfWithType(Map<String, Object> params){
    	return pdfUploadDataDao.selectConvertPdfWithType(params);
    }
    
	/**
	 * 查询重复的pdf上传文件
	 * @param params Map<String, Object> 查询参数
	 * @return List<PdfUploadDataDO>
	 */
    @Override
	public List<PdfUploadDataDO> selectRepeatPdf(Map<String, Object> params){
    	return pdfUploadDataDao.selectRepeatPdf(params);
    }
}
