package com.rs.module.dam.entity.ocr;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO_;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * ocr格式化审查任务细分 DO
 *
 * <AUTHOR>
 */
@TableName("dam_ocr_format_mince_job")
@KeySequence("dam_ocr_format_mince_job_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FormatMinceJobDO extends BaseDO_ {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 关联dms_format_check_job的id
     */
    private String jobId;
    /**
     * 检测类型(1:签章检测,2:签名检测,3:捺印检测,4:空白页检测,5:重复页检测,6:瑕疵页检测,7:文书规范检测)
     */
    private String checkType;
    /**
     * 任务类型(0:未编目任务, 1:已编目任务, 2:智能阅卷任务)
     */
    private String jobType;
    /**
     * 任务状态(0:进行中, 1:成功, 2:失败)
     */
    private String jobStatus;
    /**
     * 任务是否显示(1:是, 0:否)
     */
    private String showJob;
    /**
     * 任务开始时间
     */
    private Date startTime;
    /**
     * 任务结束时间
     */
    private Date endTime;
    /**
     * 要检测的材料
     */
    private String checkFiles;
    /**
     * 全部的材料
     */
    private String baseFiles;

    /**任务的请求次数上限*/
    @TableField(exist = false)
    private int requestLimit;
    
    /**任务类型
     * 1:新增的任务,需要发起检测,入库
     * 2:新增的任务,不需要发起检测,入库
     * 3:新增的任务,不需要发起检测,是否可以入库根据1,2判断
     * 4:复制的上一次的任务,是否可以入库根据1,2判断
     */
    @TableField(exist = false)
    private String minceType;
}
