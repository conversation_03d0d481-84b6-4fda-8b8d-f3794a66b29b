package com.rs.module.dam.dao.material;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.vo.material.MaterialInfoListReqVO;
import com.rs.module.dam.vo.material.MaterialInfoPageReqVO;

/**
* 卷宗材料 Dao
*
* <AUTHOR>
*/
@Mapper
public interface MaterialInfoDao extends IBaseDao<MaterialInfoDO> {


    default PageResult<MaterialInfoDO> selectPage(MaterialInfoPageReqVO reqVO) {
        Page<MaterialInfoDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<MaterialInfoDO> wrapper = new LambdaQueryWrapperX<MaterialInfoDO>()
            .eqIfPresent(MaterialInfoDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(MaterialInfoDO::getType, reqVO.getType())
            .likeIfPresent(MaterialInfoDO::getName, reqVO.getName())
            .eqIfPresent(MaterialInfoDO::getPageCount, reqVO.getPageCount())
            .eqIfPresent(MaterialInfoDO::getMaterialFile, reqVO.getMaterialFile())
            .eqIfPresent(MaterialInfoDO::getTemplateId, reqVO.getTemplateId())
            .eqIfPresent(MaterialInfoDO::getPartCatalogId, reqVO.getPartCatalogId())
            .eqIfPresent(MaterialInfoDO::getCatalogId, reqVO.getCatalogId())
            .eqIfPresent(MaterialInfoDO::getXh, reqVO.getXh())
            .eqIfPresent(MaterialInfoDO::getPdfId, reqVO.getPdfId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(MaterialInfoDO::getAddTime);
        }
        Page<MaterialInfoDO> materialInfoPage = selectPage(page, wrapper);
        return new PageResult<>(materialInfoPage.getRecords(), materialInfoPage.getTotal());
    }
    default List<MaterialInfoDO> selectList(MaterialInfoListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MaterialInfoDO>()
            .eqIfPresent(MaterialInfoDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(MaterialInfoDO::getType, reqVO.getType())
            .likeIfPresent(MaterialInfoDO::getName, reqVO.getName())
            .eqIfPresent(MaterialInfoDO::getPageCount, reqVO.getPageCount())
            .eqIfPresent(MaterialInfoDO::getMaterialFile, reqVO.getMaterialFile())
            .eqIfPresent(MaterialInfoDO::getTemplateId, reqVO.getTemplateId())
            .eqIfPresent(MaterialInfoDO::getPartCatalogId, reqVO.getPartCatalogId())
            .eqIfPresent(MaterialInfoDO::getCatalogId, reqVO.getCatalogId())
            .eqIfPresent(MaterialInfoDO::getXh, reqVO.getXh())
            .eqIfPresent(MaterialInfoDO::getPdfId, reqVO.getPdfId())
        .orderByDesc(MaterialInfoDO::getAddTime));
    }
    
    /**
     * 根据监管人员代码和材料类型获取目录Id
     * @param jgrybm String 监管人员编码
     * @param type String 类型
     * @return List<String>
     */
    public List<String> selectPartCatalogIdByJgrybmAndType(@Param("jgrybm") String jgrybm, @Param("type") String type);

    /**
     * 获取材料上传的pdf数量
     * @param jgrybm String 监管人员编码
     * @param types List<String> types 类型集合
     * @return Integer
     */
    public Integer selectUploadPdfCountByType(@Param("jgrybm") String jgrybm, @Param("types") List<String> types);
    
    /**
     * 批量更新材料序号
     * @param orderList List<MaterialInfoDO> 排序集合({材料Id：序号})
     */
    public void updateMaterialInfoXh(@Param("orderList") List<MaterialInfoDO> orderList);
}
