package com.rs.module.dam.vo.material;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - pdf转换图片新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = false)
public class PdfImageSaveReqVO extends BaseVO{
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("关联dam_upload_pdf_data的主键")
    @NotEmpty(message = "关联dam_upload_pdf_data的主键不能为空")
    private String pdfId;

    @ApiModelProperty("图片名称")
    private String imageName;

    @ApiModelProperty("图片地址")
    private String imageUrl;

    @ApiModelProperty("图片上传数据")
    private String imageData;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("页数序号")
    private Integer xh;

    @ApiModelProperty("状态(1:图片转换成功, 0:图片转换失败)")
    @NotNull(message = "状态(1:图片转换成功, 0:图片转换失败)不能为空")
    private Integer status;

    @ApiModelProperty("图片转换失败的原因")
    private String errorMsg;

}
