package com.rs.module.dam.entity.material;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * pdf上传数据 DO
 *
 * <AUTHOR>
 */
@TableName("dam_pdf_upload_data")
@KeySequence("dam_pdf_upload_data_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PdfUploadDataDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * pdf上传地址
     */
    private String pdfUrl;
    /**
     * pdf上传数据信息
     */
    private String uploadData;
    /**
     * pdf文件名
     */
    private String pdfName;
    /**
     * pdf文件大小
     */
    private Long pdfSize;
    /**
     * pdf状态(0:已上传未处理,1:已下载处理中,2:已拆分完成,3:已删除,4:开始转换的pdf 取消转换 ，5：下载失败，6：pdf转换过程中进行取消  已暂停)
     */
    private Integer status;
    /**
     * pdf页数
     */
    private Integer pageCount;
    /**
     * 关联dam_pdf_batch主键id
     */
    private String batchId;
    /**
     * 文件的md5值
     */
    private String md5;
    /**
     * pdf转换图片的dpi值
     */
    private Integer dpi;
    /**
     * 材料来源
     */
    private String materialSource;
    /**
     * 材料Id
     */
    private String materialId;

    @TableField(exist = false)
    private String name;
    
    @TableField(exist = false)
    private String catalogId;
    
    @TableField(exist = false)
    private String partCatalogId; 
}
