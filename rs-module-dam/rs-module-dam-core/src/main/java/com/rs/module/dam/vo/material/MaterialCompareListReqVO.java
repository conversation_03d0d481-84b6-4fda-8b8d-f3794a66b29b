package com.rs.module.dam.vo.material;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 卷宗材料比对列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MaterialCompareListReqVO extends BaseVO {
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("左边材料文件图片ID")
    private String leftFileId;

    @ApiModelProperty("右边材料文件图片ID")
    private String rightFileId;

    @ApiModelProperty("左边材料ID")
    private String leftMaterialId;

    @ApiModelProperty("右边材料ID")
    private String rightMaterialId;

    @ApiModelProperty("比对图片url")
    private String url;

    @ApiModelProperty("备注")
    private String remark;

}
