package com.rs.module.dam.vo.material;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - pdf上传数据新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = false)
public class PdfUploadDataSaveReqVO extends BaseVO{
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("pdf上传地址")
    @NotEmpty(message = "pdf上传地址不能为空")
    private String pdfUrl;

    @ApiModelProperty("pdf上传数据信息")
    @NotEmpty(message = "pdf上传数据信息不能为空")
    private String uploadData;

    @ApiModelProperty("pdf文件名")
    private String pdfName;

    @ApiModelProperty("pdf文件大小")
    private Long pdfSize;
    
    @ApiModelProperty("pdf状态(0:已上传未处理,1:已下载处理中,2:已拆分完成,3:已删除,4:开始转换的pdf 取消转换 ，5：下载失败，6：pdf转换过程中进行取消  已暂停)")
    @NotNull(message = "pdf状态(0:已上传未处理,1:已下载处理中,2:已拆分完成,3:已删除,4:开始转换的pdf 取消转换 ，5：下载失败，6：pdf转换过程中进行取消  已暂停)不能为空")
    private Integer status;

    @ApiModelProperty("pdf页数")
    private Integer pageCount;

    @ApiModelProperty("关联dam_pdf_batch主键id")
    @NotEmpty(message = "关联dam_pdf_batch主键id不能为空")
    private String batchId;

    @ApiModelProperty("文件的md5值")
    private String md5;

    @ApiModelProperty("pdf转换图片的dpi值")
    private Integer dpi;

    @ApiModelProperty("材料来源")
    private String materialSource;
}
