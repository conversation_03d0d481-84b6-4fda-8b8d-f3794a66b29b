package com.rs.module.dam.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * isp-v2版本配置工具类
 * <AUTHOR>
 * @date 2025年4月26日
 */
@Configuration
public class IspV2ConfigUtil {

	/** 智能编目服务代码 */
	private static String znbmCode;
	
	/** 空白页、重复页、瑕疵页检测服务代码 */
	private static String kbcfxcCode;
	
	/** 瑕疵页矫正服务代码 */
	private static String xcyjzCode;
	
	/** 图片方向合规性检查服务代码 */
	private static String tpfxjzCode;
	
	/** 图片歪斜矫正服务代码 */
	private static String tpwxjzCode;
	
	/** 签名、签章、捺印审查服务代码 */
	private static String qmqznyCode;
	
	/** 双层PDF转换服务代码 */
	private static String scpdfCode;
	
	/** 文书信息提取服务代码 */
	private static String wsxxtqCode;
	
	/** 二维码、条形码识别服务代码 */
	private static String ewmtxmCode;
	
	/** 预编目服务代码 */
	private static String ybmCode;
	
    /** 通用服务地址 */
    private static String commonUri;
    
    /** 回调地址 */
    private static String returnUrl;

	public static String getZnbmCode() {
		return znbmCode;
	}

	@Value("${isp.ocr2.znbmCode}")
	public void setZnbmCode(String znbmCode) {
		IspV2ConfigUtil.znbmCode = znbmCode;
	}

	public static String getKbcfxcCode() {
		return kbcfxcCode;
	}

	@Value("${isp.ocr2.kbcfxcCode}")
	public void setKbcfxcCode(String kbcfxcCode) {
		IspV2ConfigUtil.kbcfxcCode = kbcfxcCode;
	}

	public static String getXcyjzCode() {
		return xcyjzCode;
	}

	@Value("${isp.ocr2.xcyjzCode}")
	public void setXcyjzCode(String xcyjzCode) {
		IspV2ConfigUtil.xcyjzCode = xcyjzCode;
	}

	public static String getTpfxjzCode() {
		return tpfxjzCode;
	}

	@Value("${isp.ocr2.tpfxjzCode}")
	public void setTpfxjzCode(String tpfxjzCode) {
		IspV2ConfigUtil.tpfxjzCode = tpfxjzCode;
	}

	public static String getTpwxjzCode() {
		return tpwxjzCode;
	}

	@Value("${isp.ocr2.tpwxjzCode}")
	public void setTpwxjzCode(String tpwxjzCode) {
		IspV2ConfigUtil.tpwxjzCode = tpwxjzCode;
	}

	public static String getQmqznyCode() {
		return qmqznyCode;
	}

	@Value("${isp.ocr2.qmqznyCode}")
	public void setQmqznyCode(String qmqznyCode) {
		IspV2ConfigUtil.qmqznyCode = qmqznyCode;
	}

	public static String getScpdfCode() {
		return scpdfCode;
	}

	@Value("${isp.ocr2.scpdfCode}")
	public void setScpdfCode(String scpdfCode) {
		IspV2ConfigUtil.scpdfCode = scpdfCode;
	}

	public static String getWsxxtqCode() {
		return wsxxtqCode;
	}

	@Value("${isp.ocr2.wsxxtqCode}")
	public void setWsxxtqCode(String wsxxtqCode) {
		IspV2ConfigUtil.wsxxtqCode = wsxxtqCode;
	}

	public static String getEwmtxmCode() {
		return ewmtxmCode;
	}

	@Value("${isp.ocr2.ewmtxmCode}")
	public void setEwmtxmCode(String ewmtxmCode) {
		IspV2ConfigUtil.ewmtxmCode = ewmtxmCode;
	}
	
	public static String getYbmCode() {
		return ybmCode;
	}

	@Value("${isp.ocr2.ybmCode}")
	public void setYbmCode(String ybmCode) {
		IspV2ConfigUtil.ybmCode = ybmCode;
	}
	
    public static String getCommonUri() {
        return commonUri;
    }
    
    @Value("${isp.ocr2.commonUri}")
    public void setCommonUri(String commonUri) {
    	IspV2ConfigUtil.commonUri = commonUri;
    }

	public static String getReturnUrl() {
		return returnUrl;
	}

	@Value("${isp.ocr2.returnUrl:}")
	public void setReturnUrl(String returnUrl) {
		IspV2ConfigUtil.returnUrl = returnUrl;
	}
}
