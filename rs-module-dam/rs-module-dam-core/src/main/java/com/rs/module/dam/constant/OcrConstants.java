package com.rs.module.dam.constant;

/**
 * OCR处理常量类
 * <AUTHOR>
 * @date 2025年4月26日
 */
public class OcrConstants {

	//OCR请求识别成功后的批次文件数量，用于获取识别进度
    public static final String REDIS_REQUEST_OCR_BATCH_NUM = "ocr:request:batch:num:%s";
    
	//OCR请求识别成功后的批次处理进度，用于获取识别进度
    public static final String REDIS_REQUEST_OCR_BATCH_PROGRESS = "ocr:request:batch:progress:%s";
    
    //OCR格式化审查请求处理材料数量
//    public static final String REDIS_FORMAT_CHECK_BATCH_DNUM = "format:check:batch:%s";
    
    //OCR格式化审查请求已回调材料数量
    public static final String REDIS_FORMAT_CHECK_BATCH_NUM = "format:check:batch:%s";
    
    /** OCR调用类型-智能编目及其回调 */
    public static final String OCR_INVOKE_TYPE_ZNBM = "21";
    public static final String OCR_INVOKE_TYPE_ZNBM_CALLBACK = "31";
    
    /** OCR调用类型-空白页、重复页和瑕疵页检测及其回调 */
    public static final String OCR_INVOKE_TYPE_CHECK_KBCFXC = "22";
    public static final String OCR_INVOKE_TYPE_CHECK_KBCFXC_CALLBACK = "32";
    
    /** OCR调用类型-瑕疵页矫正及其回调 */
    public static final String OCR_INVOKE_TYPE_XCJZ = "23";
    public static final String OCR_INVOKE_TYPE_XCJZ_CALLBACK = "33";
    
    /** OCR调用类型-图片方向合规性检查及其回调 */
    public static final String OCR_INVOKE_TYPE_CHECK_TPFX = "24";
    public static final String OCR_INVOKE_TYPE_CHECK_TPFX_CALLBACK = "34";
    
    /** OCR调用类型-图片歪斜矫正及其回调 */
    public static final String OCR_INVOKE_TYPE_TPWXJZ = "25";
    public static final String OCR_INVOKE_TYPE_TPWXJZ_CALLBACK = "35";
    
    /** OCR调用类型-签名、签章和捺印审查及其回调 */
    public static final String OCR_INVOKE_TYPE_CHECK_QMQZNY = "26";
    public static final String OCR_INVOKE_TYPE_CHECK_QMQZNY_CALLBACK = "36";
    
    /** OCR调用类型-双层PDF转换及其回调 */
    public static final String OCR_INVOKE_TYPE_SCPDFZH = "27";
    public static final String OCR_INVOKE_TYPE_SCPDFZH_CALLBACK = "37";
    
    /** OCR调用类型-文书信息提取及其回调 */
    public static final String OCR_INVOKE_TYPE_WSXXTQ = "28";
    public static final String OCR_INVOKE_TYPE_WSXXTQ_CALLBACK = "38";
    
    /** OCR调用类型-二维码、条形码识别及其回调 */
    public static final String OCR_INVOKE_TYPE_TXMEWMSB = "29";
    public static final String OCR_INVOKE_TYPE_TXMEWMSB_CALLBACK = "39";

    /** OCR请求来源-格式化审查 */
    public static final String OCR_REQUEST_FROM_FORMAT_CHECK = "1";
    
    /** OCR请求来源-智能编目 */
    public static final String OCR_REQUEST_FROM_ZNBM = "2";
    
    /** OCR请求来源-其它 */
    public static final String OCR_REQUEST_FROM_OTHER = "99";
}
