package com.rs.module.dam.service.material;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.module.dam.dao.material.PdfConvertLogDao;
import com.rs.module.dam.entity.material.PdfConvertLogDO;


/**
 * pdf转换日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PdfConvertLogServiceImpl extends BaseServiceImpl<PdfConvertLogDao, PdfConvertLogDO> implements PdfConvertLogService {

}
