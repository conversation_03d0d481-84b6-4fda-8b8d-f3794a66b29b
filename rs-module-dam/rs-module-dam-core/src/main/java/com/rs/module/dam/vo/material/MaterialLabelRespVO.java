package com.rs.module.dam.vo.material;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 卷宗材料标签 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MaterialLabelRespVO extends BaseVO implements TransPojo{
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("材料Id")
    private String materialId;
    @ApiModelProperty("材料目录Id")
    private String catalogId;
    @ApiModelProperty("材料文件图片Id")
    private String fileId;
}
