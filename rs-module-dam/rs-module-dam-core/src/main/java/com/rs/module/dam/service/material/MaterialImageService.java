package com.rs.module.dam.service.material;

import java.util.List;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.material.MaterialImageDO;
import com.rs.module.dam.vo.material.MaterialImageListReqVO;
import com.rs.module.dam.vo.material.MaterialImagePageReqVO;
import com.rs.module.dam.vo.material.MaterialImageSaveReqVO;

/**
 * 卷宗材料图片 Service 接口
 *
 * <AUTHOR>
 */
public interface MaterialImageService extends IBaseService<MaterialImageDO>{

    /**
     * 创建卷宗材料图片
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMaterialImage(@Valid MaterialImageSaveReqVO createReqVO);

    /**
     * 更新卷宗材料图片
     *
     * @param updateReqVO 更新信息
     */
    void updateMaterialImage(@Valid MaterialImageSaveReqVO updateReqVO);

    /**
     * 删除卷宗材料图片
     *
     * @param id 编号
     */
    void deleteMaterialImage(String id);

    /**
     * 获得卷宗材料图片
     *
     * @param id 编号
     * @return 卷宗材料图片
     */
    MaterialImageDO getMaterialImage(String id);

    /**
    * 获得卷宗材料图片分页
    *
    * @param pageReqVO 分页查询
    * @return 卷宗材料图片分页
    */
    PageResult<MaterialImageDO> getMaterialImagePage(MaterialImagePageReqVO pageReqVO);

    /**
    * 获得卷宗材料图片列表
    *
    * @param listReqVO 查询条件
    * @return 卷宗材料图片列表
    */
    List<MaterialImageDO> getMaterialImageList(MaterialImageListReqVO listReqVO);

    /**
     * 更新材料图片地址
     * @param materialImage MaterialImageDO 材料
     */
    void updateUrlById(MaterialImageDO materialImage);
}
