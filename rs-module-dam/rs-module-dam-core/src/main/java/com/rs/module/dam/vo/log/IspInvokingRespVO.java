package com.rs.module.dam.vo.log;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 智能服务调用日志 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class IspInvokingRespVO extends BaseVO implements TransPojo{
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("任务Id")
    private String jobId;
    @ApiModelProperty("请求参数")
    private String input;
    @ApiModelProperty("返回结果")
    private String output;
    @ApiModelProperty("isp接口serCode")
    private String serCode;
    @ApiModelProperty("请求接口类型(0:签名签章捺印检测结果查询,4:空白页检测,5:重复页检测, 6:瑕疵页检测,7:文书规范检测,8: 启用通用ocr基础识别服务,9:查询通用基础OCR识别结果, 10:双层PDF转换,11:图片瑕疵处理,12:图片方向合规性检测,13:查询ocr基础通用识别服务的进度)")
    private String invokeType;
    @ApiModelProperty("接口耗时")
    private Integer requestTime;
    @ApiModelProperty("请求地址")
    private String address;
    @ApiModelProperty("请求来源(1:智能格式化审查, 2:智能编目)")
    private String requestFrom;
    @ApiModelProperty("请求状态(1:成功, 2:失败)")
    private String requestStatus;
    @ApiModelProperty("服务器ip")
    private String serverIp;
    @ApiModelProperty("调用接口的任务类型")
    private String checkType;
    @ApiModelProperty("发起检测图片的数量")
    private Integer fileCount;
}
