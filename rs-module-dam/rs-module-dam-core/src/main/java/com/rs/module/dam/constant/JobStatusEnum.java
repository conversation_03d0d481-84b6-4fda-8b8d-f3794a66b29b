package com.rs.module.dam.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务状态枚举类
 * <AUTHOR>
 * @date 2025年4月17日
 */
@AllArgsConstructor
@Getter
public enum JobStatusEnum {
	
	NO("0", "未执行"),
	
	YES("1", "执行成功"),
	
	ERROR("2", "执行异常"),
	
	PENDING("3", "等待处理,OCR服务无资源"),
	
	STOP("4", "任务不执行,未开启OCR服务");

	//任务状态
	private String status;
	
	//状态描述
	private String remark;
	
	/**
	 * 根据代码获取检测类型
	 * @param status String 任务状态
	 * @return JobStatusEnum
	 */
	public static JobStatusEnum getByType(String status) {
		for(JobStatusEnum jobStatus : values()) {
			if(jobStatus.getStatus().equals(status)) {
				return jobStatus;
			}
		}
		return null;
	}
}
