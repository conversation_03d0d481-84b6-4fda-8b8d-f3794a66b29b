package com.rs.module.dam.service.archive;

import java.util.List;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.archive.ArchiveJobDO;
import com.rs.module.dam.vo.archive.ArchiveJobListReqVO;
import com.rs.module.dam.vo.archive.ArchiveJobPageReqVO;
import com.rs.module.dam.vo.archive.ArchiveJobSaveReqVO;

/**
 * 卷宗归档任务 Service 接口
 *
 * <AUTHOR>
 */
public interface ArchiveJobService extends IBaseService<ArchiveJobDO>{

    /**
     * 创建卷宗归档任务
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createArchiveJob(@Valid ArchiveJobSaveReqVO createReqVO);

    /**
     * 更新卷宗归档任务
     *
     * @param updateReqVO 更新信息
     */
    void updateArchiveJob(@Valid ArchiveJobSaveReqVO updateReqVO);

    /**
     * 删除卷宗归档任务
     *
     * @param id 编号
     */
    void deleteArchiveJob(String id);

    /**
     * 获得卷宗归档任务
     *
     * @param id 编号
     * @return 卷宗归档任务
     */
    ArchiveJobDO getArchiveJob(String id);

    /**
    * 获得卷宗归档任务分页
    *
    * @param pageReqVO 分页查询
    * @return 卷宗归档任务分页
    */
    PageResult<ArchiveJobDO> getArchiveJobPage(ArchiveJobPageReqVO pageReqVO);

    /**
    * 获得卷宗归档任务列表
    *
    * @param listReqVO 查询条件
    * @return 卷宗归档任务列表
    */
    List<ArchiveJobDO> getArchiveJobList(ArchiveJobListReqVO listReqVO);


}
