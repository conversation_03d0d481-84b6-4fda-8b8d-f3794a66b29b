package com.rs.module.dam.service.material;

import java.util.List;

import javax.validation.Valid;

import org.apache.ibatis.annotations.Param;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.material.PdfImageDO;
import com.rs.module.dam.vo.material.PdfImageListReqVO;
import com.rs.module.dam.vo.material.PdfImagePageReqVO;
import com.rs.module.dam.vo.material.PdfImageSaveReqVO;

/**
 * pdf转换图片 Service 接口
 *
 * <AUTHOR>
 */
public interface PdfImageService extends IBaseService<PdfImageDO>{

    /**
     * 创建pdf转换图片
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPdfImage(@Valid PdfImageSaveReqVO createReqVO);

    /**
     * 更新pdf转换图片
     *
     * @param updateReqVO 更新信息
     */
    void updatePdfImage(@Valid PdfImageSaveReqVO updateReqVO);

    /**
     * 删除pdf转换图片
     *
     * @param id 编号
     */
    void deletePdfImage(String id);

    /**
     * 获得pdf转换图片
     *
     * @param id 编号
     * @return pdf转换图片
     */
    PdfImageDO getPdfImage(String id);

    /**
    * 获得pdf转换图片分页
    *
    * @param pageReqVO 分页查询
    * @return pdf转换图片分页
    */
    PageResult<PdfImageDO> getPdfImagePage(PdfImagePageReqVO pageReqVO);

    /**
    * 获得pdf转换图片列表
    *
    * @param listReqVO 查询条件
    * @return pdf转换图片列表
    */
    List<PdfImageDO> getPdfImageList(PdfImageListReqVO listReqVO);

    /**
     * 查询pdf已转换成功的图片
     * @param pdfId String pdf主键
     * @return List<String>
     */
    List<String> selectImageIdWithPdfId(@Param("pdfId") String pdfId);
}
