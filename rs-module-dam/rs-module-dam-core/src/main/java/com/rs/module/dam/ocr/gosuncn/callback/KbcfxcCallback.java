package com.rs.module.dam.ocr.gosuncn.callback;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.rs.module.dam.constant.CheckTypeEnum;
import com.rs.module.dam.constant.DamConstants;
import com.rs.module.dam.constant.OcrConstants;
import com.rs.module.dam.entity.log.IspInvokingDO;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.entity.ocr.FormatCheckJobDO;
import com.rs.module.dam.entity.ocr.FormatCheckResultDO;
import com.rs.module.dam.entity.ocr.FormatMinceJobDO;
import com.rs.module.dam.entity.prisoner.PrisonerCatalogDO;
import com.rs.module.dam.entity.sys.ConfigGroupDO;
import com.rs.module.dam.ocr.gosuncn.GoOcrCallbackParam;
import com.rs.module.dam.ocr.gosuncn.GoOcrCallbackParam.SerDatas;
import com.rs.module.dam.ocr.gosuncn.GoOcrHttpStatus;
import com.rs.module.dam.ocr.handler.OcrHandler;
import com.rs.module.dam.ocr.util.OcrUtil;
import com.rs.module.dam.service.log.IspInvokingService;
import com.rs.module.dam.service.material.MaterialInfoService;
import com.rs.module.dam.service.ocr.FormatCheckJobService;
import com.rs.module.dam.service.ocr.FormatCheckResultService;
import com.rs.module.dam.service.ocr.FormatMinceJobService;
import com.rs.module.dam.service.ocr.TFormatCheckService;
import com.rs.module.dam.service.sys.ConfigGroupService;
import com.rs.module.dam.util.IpUtil;
import com.rs.module.dam.util.RedisTemplateUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 空白、重复、瑕疵页检测回调接口
 * <AUTHOR>
 * @date 2025年4月27日
 */
@Component
@Slf4j
public class KbcfxcCallback implements IOcrCallback {
	
	@Autowired
    private ConfigGroupService configGroupService;
	
	@Autowired
	private IspInvokingService ispInvokingService;
	
	@Autowired
	private MaterialInfoService dossierMaterialInfoService;
	
	@Autowired
	private FormatCheckJobService checkJobService;
	
	@Autowired
	private FormatMinceJobService minceJobService;
	
	@Autowired
	private FormatCheckResultService checkResultService;
	
	@Autowired
	private TFormatCheckService tFormatCheckService;
    
    @Autowired
    OcrHandler ocrHandler;
    
    @Autowired
    RedisTemplateUtil redisTemplateUtil;
    
	/**
	 * 回调处理
	 * @param params GoOcrCallbackParam 回调参数
	 */
	@Override
	public void handle(GoOcrCallbackParam params) {
		
		//服务返回数据
		SerDatas serDatas = params.getSerDatas();
		
		//构建isp调用对象
		IspInvokingDO ispInvoking = new IspInvokingDO();
		ispInvoking.setId(StringUtil.getGuid32());
		ispInvoking.setJobId(serDatas.getBatchId());
		ispInvoking.setInput(JSON.toJSONString(params));
		ispInvoking.setSerCode(params.getSerCode());
		ispInvoking.setInvokeType(OcrConstants.OCR_INVOKE_TYPE_CHECK_KBCFXC_CALLBACK);
		ispInvoking.setRequestFrom(OcrConstants.OCR_REQUEST_FROM_FORMAT_CHECK);
		ispInvoking.setServerIp(IpUtil.getHostIp());
		ispInvoking.setCheckType(OcrConstants.OCR_INVOKE_TYPE_CHECK_KBCFXC_CALLBACK);
		
		//格式化审查任务
		FormatCheckJobDO checkJob = null;
		
		//所有检测子任务
		List<FormatMinceJobDO> allMinceJobList = null;
		
		//空白页、重复页和瑕疵页检测子任务
		List<FormatMinceJobDO> kbcfxcMinceJobList = new ArrayList<>();
		
		//任务是否完成(主任务以及空白、重复和瑕疵检测子任务)
		boolean isFinished = false;
		boolean blankFinished = true;
		boolean repeatFinished = true;
		boolean flawFinished = true;
		
		//开始调用时间
		long begin = System.currentTimeMillis();
		
		try {
			//回调返回结果
			JSONObject callbackResults = (JSONObject)params.getSerDatas().getResults();
			
			//任务Id = 批次Id
			String jobId = serDatas.getBatchId();
			
			//格式化审查任务
			checkJob = checkJobService.getById(jobId);
			
			//设置isp调用对象
			ispInvoking.setJgrybm(checkJob.getJgrybm());
			
			//获取所有的检测子任务
			allMinceJobList = tFormatCheckService.getCheckedMinceJobList(checkJob.getId());
			
			//返回数据合法
			if((callbackResults != null || !serDatas.getFailed_ids().isEmpty())
					&& (GoOcrHttpStatus.OK.getCode() == serDatas.getCode() || GoOcrHttpStatus.INCOMPLETE_RETURN.getCode() == serDatas.getCode())) {
				
				//查询卷宗配置信息
				ConfigGroupDO dmsConfigGroup = configGroupService.getUserConfigGroup(checkJob.getOrgCode());
				
				//设置isp调用对象
				ispInvoking.setAddress(dmsConfigGroup.getOcrServer());
				
				//空白页检测任务及检测结果
				FormatMinceJobDO blankJob = OcrUtil.getMinceJobByCheckType(allMinceJobList, CheckTypeEnum.BLANK_SPACE.getCode());
				JSONArray blankResults = null;
				
				//重复页检测任务及检测结果
				FormatMinceJobDO repeatJob = OcrUtil.getMinceJobByCheckType(allMinceJobList, CheckTypeEnum.REPEAT_PAGE.getCode());
				JSONArray repeatResults = null;
				
				//瑕疵页检测任务及检测结果
				FormatMinceJobDO flawJob = OcrUtil.getMinceJobByCheckType(allMinceJobList, CheckTypeEnum.FLAW.getCode());
				JSONArray flawResults = null;
				
				//处理取消检测的子任务
				ocrHandler.updateCancelFormatJob(jobId, blankJob, repeatJob, flawJob);
				
				//获取格式化审查关联的材料
				List<MaterialInfoDO> dossierMaterialInfoList = tFormatCheckService.getFormatCheckMaterialInfoList(checkJob.getJgrybm(), checkJob.getJobType());
				
				//没有可以审查的材料
				if(!tFormatCheckService.validateFormatCheckMaterials(checkJob, dossierMaterialInfoList)) {
					return;
				}
				
				//获取案件编目信息
				PrisonerCatalogDO ajCatalog = tFormatCheckService.getPrisonerCatalog(checkJob.getJgrybm());
				
				//检测结果
				List<FormatCheckResultDO> checkResultList = new ArrayList<>();
				
				//需更新的材料
				Map<String, MaterialInfoDO> needUpdateMaterialInfoMap = new HashMap<>();
				
				//检测结果插入时间
				Date addTime = new Date();
				
				//失败的文件数量
				int failedIdsNum = 0;
				if(GoOcrHttpStatus.INCOMPLETE_RETURN.getCode() == serDatas.getCode()){
					failedIdsNum = serDatas.getFailed_ids().size();
				}

				//空白页检测
				if(null != blankJob) {
					kbcfxcMinceJobList.add(blankJob);
					if (DamConstants.JOB_STATE_NO.equals(blankJob.getJobStatus())){
						
						//已经检测过的文件Id集合
						List<String> checkedFileIdList = tFormatCheckService.getPrisonerCheckResultFileIdList(checkJob.getJgrybm(), checkJob.getJobType(), CheckTypeEnum.BLANK_SPACE.getCode());
						
						//循环处理检测结果
						blankResults = callbackResults.getJSONArray("blankResults");
						if(blankResults != null) {
							for(int i = 0; i < blankResults.size(); i ++) {
								JSONObject file = blankResults.getJSONObject(i);
								if(file.getBoolean("isBlank")) {
									String fileId = file.getString("fileId");
									
									//是否找到空白材料
									boolean hasBlank = false;
									
									for(MaterialInfoDO dossierMaterialInfo : dossierMaterialInfoList) {
										JSONArray materialFile = JSONArray.parseArray(dossierMaterialInfo.getMaterialFile());
										for(int j = 0; j < materialFile.size(); j ++) {
											JSONObject material = materialFile.getJSONObject(j);
											String id = material.getString("id");
											if(id.equals(fileId)) {											
												hasBlank = true;
												
												//材料已经检测
												if(checkedFileIdList.contains(id)) {
													break;
												}
												
												//设置材料的空白属性
												material.put(DamConstants.MaterialColumn.BLANK_SPACE, "1");
												
												//创建检测结果对象
												FormatCheckResultDO checkResult = OcrUtil.buildFormatCheckResult(checkJob, ajCatalog,
														dossierMaterialInfo, material, CheckTypeEnum.BLANK_SPACE.getCode(), addTime);
												
												//添加检测结果
												checkResultList.add(checkResult);
												
												//更新材料文件
												dossierMaterialInfo.setMaterialFile(materialFile.toJSONString());
												
												//添加到待更新材料中
												if(!needUpdateMaterialInfoMap.containsKey(dossierMaterialInfo.getId())){
													needUpdateMaterialInfoMap.put(dossierMaterialInfo.getId(), dossierMaterialInfo);
												}
												
												//找到空白材料直接退出当前循环
												break;
											}
										}
										
										//已经找到空白材料退出循环
										if(hasBlank) {
											break;
										}
									}
								}
							}
						}
					}
				}
				
				//重复页检测
				if(null != repeatJob) {
					kbcfxcMinceJobList.add(repeatJob);
					if (DamConstants.JOB_STATE_NO.equals(repeatJob.getJobStatus())){
						
						//已经检测过的文件Id集合
						List<String> checkedFileIdList = tFormatCheckService.getPrisonerCheckResultFileIdList(checkJob.getJgrybm(), checkJob.getJobType(), CheckTypeEnum.REPEAT_PAGE.getCode());
						
						//循环处理检测结果
						repeatResults = callbackResults.getJSONArray("repeatResults");
						if(repeatResults != null) {
							for(int i = 0; i < repeatResults.size(); i ++) {
								JSONObject file = repeatResults.getJSONObject(i);
								
								//重复的材料id组
								JSONArray repeatFieldIds = file.getJSONArray("fileIds");
								
								//重复相似度
								float simScore = file.getFloatValue("simScore");
								
								//接口返回的数据里面重复的id只有一个时，不处理数据
								if(repeatFieldIds.size() <= 1) {
									continue;
								}
								
								//重复分组标记
								String repeatMark = StringUtil.getGuid32();
								
								for(int m = 0; m < repeatFieldIds.size(); m ++) {
									String repeatId = repeatFieldIds.getString(m);
									
									//是否有重复材料
									boolean hasRepeat = false;
									
									for(MaterialInfoDO dossierMaterialInfo : dossierMaterialInfoList) {
										JSONArray materialFile = JSONArray.parseArray(dossierMaterialInfo.getMaterialFile());
										for(int j = 0; j < materialFile.size(); j ++) {
											JSONObject material = materialFile.getJSONObject(j);
											String id = material.getString("id");
											if(id.equals(repeatId)) {
												hasRepeat = true;
												
												//材料已经检测
												if(checkedFileIdList.contains(id)) {
													break;
												}
												
												//设置材料的重复属性
												material.put(DamConstants.MaterialColumn.REPEAT, "1");
												material.put(DamConstants.MaterialColumn.REPEAT_MARK, repeatMark);
												material.put(DamConstants.MaterialColumn.SIM_SCORE, simScore);											
												
												//创建检测结果对象
												FormatCheckResultDO checkResult = OcrUtil.buildFormatCheckResult(checkJob, ajCatalog,
														dossierMaterialInfo, material, CheckTypeEnum.REPEAT_PAGE.getCode(), addTime);
												
												//补充检测结果属性
												checkResult.setRepeatMark(repeatMark);
												
												//添加检测结果
												checkResultList.add(checkResult);
												
												//更新材料文件
												dossierMaterialInfo.setMaterialFile(materialFile.toJSONString());
												
												//添加到待更新材料中
												if(!needUpdateMaterialInfoMap.containsKey(dossierMaterialInfo.getId())){
													needUpdateMaterialInfoMap.put(dossierMaterialInfo.getId(), dossierMaterialInfo);
												}
												
												//找到重复材料直接退出当前循环
												break;
											}
										}
										
										//已经找到重复材料退出循环
										if(hasRepeat) {
											break;
										}
									}
								}
							}
						}
					}
				}
				
				//瑕疵页检测
				if(null != flawJob) {
					kbcfxcMinceJobList.add(flawJob);
					if (DamConstants.JOB_STATE_NO.equals(flawJob.getJobStatus())){
						
						//已经检测过的文件Id集合
						List<String> checkedFileIdList = tFormatCheckService.getPrisonerCheckResultFileIdList(checkJob.getJgrybm(), checkJob.getJobType(), CheckTypeEnum.FLAW.getCode());
						
						//循环处理检测结果
						flawResults = callbackResults.getJSONArray("flawResults");
						if(flawResults != null) {
							for(int i = 0; i < flawResults.size(); i ++) {
								JSONObject file = flawResults.getJSONObject(i);							
								
								//1表示存在阴影，0表示不存在阴影
								int shadow = file.getIntValue("shadow");
								
								//1表示分辨率低，0表示分辨率符合要求
								int resolution = file.getIntValue("resolution");
								
								//1表示存在黑边，0表示不存在黑边
								int blackborder = file.getIntValue("blackborder");
								
								//图片存在瑕疵问题
								if(shadow == 1 || resolution == 1 || blackborder == 1) {
									String fileId = file.getString("fileId");
									
									//是否有瑕疵材料
									boolean hasFlaw = false;
									
									for(MaterialInfoDO dossierMaterialInfo : dossierMaterialInfoList) {
										JSONArray materialFile = JSONArray.parseArray(dossierMaterialInfo.getMaterialFile());
										for(int j = 0; j < materialFile.size(); j ++) {
											JSONObject material = materialFile.getJSONObject(j);
											String id = material.getString("id");
											if(id.equals(fileId)) {											
												hasFlaw = true;
												
												//材料已经检测
												if(checkedFileIdList.contains(id)) {
													break;
												}
												
												//设置材料的瑕疵属性
												material.put(DamConstants.MaterialColumn.FLAW, "1");
												material.put(DamConstants.MaterialColumn.SPOT, "1");
												
												//创建检测结果对象
												FormatCheckResultDO checkResult = OcrUtil.buildFormatCheckResult(checkJob, ajCatalog,
														dossierMaterialInfo, material, CheckTypeEnum.FLAW.getCode(), addTime);
												
												//补充检测结果属性
												checkResult.setFlawData(file.toJSONString());
												
												//添加检测结果
												checkResultList.add(checkResult);
												
												//更新材料文件
												dossierMaterialInfo.setMaterialFile(materialFile.toJSONString());
												
												//添加到待更新材料中
												if(!needUpdateMaterialInfoMap.containsKey(dossierMaterialInfo.getId())){
													needUpdateMaterialInfoMap.put(dossierMaterialInfo.getId(), dossierMaterialInfo);
												}
												
												//找到瑕疵材料直接退出当前循环
												break;
											}
										}
										
										//已经找到瑕疵材料退出循环
										if(hasFlaw) {
											break;
										}
									}
								}
							}
						}
					}
				}
				
				//插入检测结果
				if(CollectionUtil.isNotNull(checkResultList)) {
					checkResultService.saveBatch(checkResultList);
				}
				
				//根据检测结果更新材料
				if(CollectionUtil.isNotNull(needUpdateMaterialInfoMap)) {
					List<MaterialInfoDO> needUpdateMaterialInfoList = new ArrayList<>(needUpdateMaterialInfoMap.values());
					dossierMaterialInfoService.updateBatchById(needUpdateMaterialInfoList);
				}
				
				//更新空白页检测子任务状态及进度
				if(blankJob != null && blankResults != null) {
					blankFinished = ocrHandler.getAndUpdateFormactCheckMinceJobProcess(blankJob, blankResults.size() + failedIdsNum);
					
					//更新子任务
					if(blankFinished) {
						OcrUtil.updateMinceJobStatus(blankJob, DamConstants.JOB_STATE_YES);
						minceJobService.updateById(blankJob);
					}
				}
				
				//更新瑕疵页检测子任务状态及进度
				if(flawJob != null && flawResults != null) {
					flawFinished = ocrHandler.getAndUpdateFormactCheckMinceJobProcess(flawJob, flawResults.size() + failedIdsNum);
					
					//更新子任务
					if(flawFinished) {
						OcrUtil.updateMinceJobStatus(flawJob, DamConstants.JOB_STATE_YES);
						minceJobService.updateById(flawJob);
					}
				}
				
				//更新重复页检测子任务状态及进度
				if(repeatJob != null) {
					repeatFinished = serDatas.getProgress() >= 1.0;
					
					//更新子任务
					if(repeatFinished) {
						OcrUtil.updateMinceJobStatus(repeatJob, DamConstants.JOB_STATE_YES);
						minceJobService.updateById(repeatJob);
						
						//设置重复页检测进度
						JSONArray repeatCheckFiles = JSONArray.parseArray(repeatJob.getCheckFiles());
						ocrHandler.getAndUpdateFormactCheckMinceJobProcess(repeatJob, repeatCheckFiles.size());
					}
				}
				
				//判断空白重复瑕疵检测任务是否完成
				isFinished = blankFinished && flawFinished && repeatFinished;
				
				//返回前端检测进度
				ocrHandler.sendFormatCheckProcessResult(checkJob);
				
				//更新isp调用对象
				ispInvoking.setOutput("空白、重复、瑕疵页检测回调成功");
			}
			else {
				ispInvoking.setOutput("空白、重复、瑕疵页检测回调异常：参数不正确");
				ispInvoking.setRequestStatus("2");
				log.error("ocr处理-格式化审查-空白、重复、瑕疵页检测回调异常：参数不正确");
				
				//任务是否完成
//				isFinished = serDatas.getProgress() >= 1;
				isFinished = true;
				
				//更新任务状态
				if(isFinished) {
					OcrUtil.updateFormatCheckJobStatus(checkJob, kbcfxcMinceJobList, DamConstants.JOB_STATE_ERROR);
				}
			}
		}
		catch (Exception e) {
			e.printStackTrace();
			ispInvoking.setOutput(e.toString());
			ispInvoking.setRequestStatus("2");
			log.error("ocr处理-格式化审查-空白、重复、瑕疵页检测回调异常：{}", e);
			
			//任务是否完成
//			isFinished = serDatas.getProgress() >= 1;
			isFinished = true;
			
			//更新任务状态
			if(isFinished) {
				OcrUtil.updateFormatCheckJobStatus(checkJob, kbcfxcMinceJobList, DamConstants.JOB_STATE_ERROR);
			}
		}
		finally {
			if(isFinished && CollectionUtil.isNotNull(kbcfxcMinceJobList)) {
				
				//更新子任务
				minceJobService.updateBatchById(kbcfxcMinceJobList);
				
				//更新主任务状态并判断是否需要进行自动编目
				ocrHandler.updateFormatCheckJobStatusAndCatalog(checkJob, allMinceJobList);
			}
			
			//插入isp调用对象
			ispInvoking.setRequestTime(System.currentTimeMillis() - begin);
			ispInvoking.setAddTime(new Date());
			ispInvokingService.save(ispInvoking);			
		}
	}
}
