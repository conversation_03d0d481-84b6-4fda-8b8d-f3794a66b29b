package com.rs.module.dam.vo.material;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel(description = "管理后台 - pdf转换图片分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PdfImagePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("关联dam_upload_pdf_data的主键")
    private String pdfId;

    @ApiModelProperty("图片名称")
    private String imageName;

    @ApiModelProperty("图片地址")
    private String imageUrl;

    @ApiModelProperty("图片上传数据")
    private String imageData;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("页数序号")
    private Integer xh;

    @ApiModelProperty("状态(1:图片转换成功, 0:图片转换失败)")
    private Integer status;

    @ApiModelProperty("图片转换失败的原因")
    private String errorMsg;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
