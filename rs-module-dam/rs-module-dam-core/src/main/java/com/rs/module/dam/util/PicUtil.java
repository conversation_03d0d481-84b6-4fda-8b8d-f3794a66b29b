package com.rs.module.dam.util;

import java.awt.image.BufferedImage;
import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.DataInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URL;

import javax.imageio.ImageIO;

import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;

/**
 * 图片处理工具类
 * <AUTHOR>
 * @date 2025年4月25日
 */
@Slf4j
public class PicUtil {

    /**
     * 根据指定大小压缩图片
     * @param desFileSize 指定图片大小，单位kb
     * @param imageId     影像编号
     * @param imageBytes  源图片字节数组
     * @return 压缩质量后的图片字节数组
     */
    public static byte[] compressPicForScale(String imageBytes) throws IOException {
        double accuracy = 0.1f;
        byte[] imageByte = new byte[0];

        try {
            URL url =  new URL(imageBytes);
            DataInputStream dataInputStream = new DataInputStream(url.openStream());
            byte[] buffer = new byte[2048];

            ByteArrayInputStream inputStream;
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            int length;
            while ((length = dataInputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, length);
            }

            byte[] context = outputStream.toByteArray();

            inputStream = new ByteArrayInputStream(context);

            Thumbnails.of(inputStream)
                    .scale(0.75d)
                    .outputQuality(accuracy)
                    .toOutputStream(outputStream);
            imageByte = outputStream.toByteArray();

        }
        catch (Exception e) {
            log.error("【图片压缩】msg=图片压缩失败!", e);
        }
        
        return imageByte;
    }

    /**
     * 根据指定大小压缩图片
     * @param desFileSize 指定图片大小，单位kb
     * @param imageId     影像编号
     * @param imageBytes  源图片字节数组
     * @return 压缩质量后的图片字节数组
     */
    public static byte[] compressPic(String imageBytes) throws IOException {
        byte[] imageByte = new byte[0];
        DataInputStream dataInputStream = null;
        ByteArrayOutputStream outputStream = null;
        try {
            URL url =  new URL(imageBytes);
            dataInputStream = new DataInputStream(url.openStream());

            outputStream = new ByteArrayOutputStream();

            Thumbnails.of(dataInputStream)
                    .scale(0.75f)
                    .outputQuality(0.8f)
                    .toOutputStream(outputStream);
            imageByte = outputStream.toByteArray();

        }
        catch (Exception e) {
            if (outputStream != null) {
                outputStream.close();
            }
            if (dataInputStream != null) {
                dataInputStream.close();
            }
            log.error("【图片压缩】msg=图片压缩失败!", e);
        }
        finally {
            if (outputStream != null) {
                outputStream.close();
            }
            if (dataInputStream != null) {
                dataInputStream.close();
            }
        }
        
        return imageByte;
    }



    /**
     * 自动调节精度(经验数值)
     * @param size 源图片大小
     * @return 图片压缩质量比
     */
    public static double getAccuracy(long size) {
        double accuracy;
        if (size < 900) {
            accuracy = 0.6;
        }
        else if (size < 2047) {
            accuracy = 0.5;
        }
        else if (size < 3275) {
            accuracy = 0.4;
        }
        else {
            accuracy = 0.2;
        }
        
        return accuracy;
    }

    /**
     * 获取图片宽度和高度
     * @param imageUrl String 图片地址
     * @return int[]
     */
    public static int[] getImageWidthAndHeight(String imageUrl){
    	BufferedInputStream bis = null;
    	OutputStream bos = null;
    	
		try {
			String[] split = imageUrl.split("/");
			String fileName = split[split.length -1];
			String tempPath = PdfGenerateUtil.generateTempPath();
			String path = tempPath + System.currentTimeMillis() +"_"+ fileName;
			log.info("写入图片地址:{}", path);
			
			//构建输入流
			URL url = new URL(imageUrl);
			bis = new BufferedInputStream(url.openStream());
			byte[] bytes = new byte[100];
			
			//设置写入路径以及图片名称
			bos = new FileOutputStream(new File(path));
			int len;
			while ((len = bis.read(bytes)) > 0) {
				bos.write(bytes, 0, len);
			}			

			int[] a = new int[2];

			File file = new File(path);
			BufferedImage bi = null;
			boolean imgWrong=false;
			try {
				//读取图片
				bi = ImageIO.read(file);
				imgWrong = true;
			} catch (IOException ex) {
				ex.printStackTrace();
			}
			if(imgWrong){
				a[0] = bi.getWidth(); //获得 宽度
				a[1] = bi.getHeight(); //获得 高度
			}else{
				a=null;
			}
			//删除文件
			file.delete();
			return a;
		}
		catch (Exception e) {
			log.error("获取图片:{} 宽高失败, 失败原因:{} ", imageUrl, e.getMessage());
		}
		finally {
			try {
				if(bis != null) {
					bis.close();
				}
				if(bos != null) {
					bos.flush();
					bos.close();
				}
			}
			catch(Exception e) {
				log.error("关闭流失败！异常原因：{}", e.getMessage());
			}
		}

		return null;
	}
}
