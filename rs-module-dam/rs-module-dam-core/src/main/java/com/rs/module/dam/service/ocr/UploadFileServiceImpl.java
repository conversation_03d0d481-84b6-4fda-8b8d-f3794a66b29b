package com.rs.module.dam.service.ocr;

import java.awt.Graphics;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.imageio.ImageIO;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.R;
import com.bsp.common.util.StringUtil;
import com.rs.module.dam.util.PdfGenerateUtil;
import com.rs.module.dam.util.RotateImageUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 上传文件服务服务类
 * <AUTHOR>
 * @date 2025年4月26日
 */
@Service("uploadFileService")
@Slf4j
public class UploadFileServiceImpl implements UploadFileService {
    private static Logger logger = LoggerFactory.getLogger(UploadFileServiceImpl.class);

    /**
	 * 导出excel
	 * @param file MultipartFile 要导出的文件
	 * @return boolean
	 */
    @SuppressWarnings({ "rawtypes", "unchecked", "unused" })
	@Override
    public boolean importExcel(MultipartFile file) {
        InputStream inputStream=null;
        try {
            inputStream = file.getInputStream();
            List list = readExcel(inputStream, file.getOriginalFilename());
            List data = new ArrayList();
            //主层级userId
            String uuid="";
            //其他层级userId
            String uuid2="";
            //其他层级userId
            String uuid3="";
            String parentId="";
            boolean isParent=false;
            int level=1;
            //第一层级orderId
            int orderId=0;
            //第二层级orderId
            int orderId2=0;
            //第三层级 orderId
            int orderId3=0;
            String nameResult="";
            for (int i = 0; i < list.size(); i++) {
                List object =  (List)list.get(i);
                //获取目录模板
                String xh= (String) object.get(0);
                String name= (String) object.get(1);
                if(!"".equals(name)){
                    nameResult=name;
                }
                //；一级
                String primaryDirectory= (String) object.get(2);
                //二级
                String secondaryDirectory= (String) object.get(3);
                //文书
                String documentaryMaterials= (String) object.get(4);
                //一级模板不为空
                if(!"".equals(primaryDirectory)){
                    uuid=StringUtil.getGuid32();
                    parentId="0";
                    isParent=true;
                    orderId=orderId+1;
                    orderId2=0;
                    level=0;
                    Object jsonObject=JSONObject.toJSON(getParam( uuid, parentId, isParent, orderId, level, primaryDirectory));
                    data.add(jsonObject.toString());
                }
                if(!"".equals(secondaryDirectory)){
                    uuid2=StringUtil.getGuid32();
                    parentId=uuid;
                    if("".equals(documentaryMaterials)){
                        isParent=false;
                    }else {
                        isParent=true;
                    }
                    orderId2=orderId2+1;
                    orderId3=0;
                    level=1;
                    Object jsonObject=JSONObject.toJSON(getParam(uuid2, parentId, isParent, orderId2, level, secondaryDirectory));
                    data.add(jsonObject.toString());
                }
                if(!"".equals(documentaryMaterials)){
                    uuid3=StringUtil.getGuid32();
                    parentId=uuid2;
                    isParent=false;
                    orderId3=orderId3+1;
                    level=2;
                    Object jsonObject=JSONObject.toJSON(getParam(uuid3, parentId, isParent, orderId3, level, documentaryMaterials));
                    data.add(jsonObject.toString());
                }
            }
            insert(data,nameResult);
            return true;

        }catch (Exception e) {
        	log.error("发生异常：{}", e.getMessage());
        }finally {
            if(inputStream != null){
                try {
                    inputStream.close();
                } catch (IOException e) {
                	log.error("发生异常：{}", e.getMessage());
                }
            }
        }
        return false;
    }

    /**
	 * 旋转图片
	 * @param url String 图片地址
	 * @param angel Integer 旋转角度
	 * @return R
	 */
    @Override
    public R rotate(String url, Integer angel) {
        InputStream in = null;
        
        try {
            in= RotateImageUtil.getImageStream(url);
            BufferedImage bufferedImage = ImageIO.read(in);
            // 调用图片旋转工具类，旋转图片
            BufferedImage rotateImage = RotateImageUtil.rotateImage(bufferedImage, angel);
            //去掉url地址携带的参数
            String[] split = url.split("\\?");
            url = split[0];
            // 截取图片后缀名,以保持图片格式不变
            String imgSuffix = url.substring(url.lastIndexOf(".") + 1, url.length());
            // 截取图片名称
            String filename = url.substring(url.lastIndexOf("/") + 1);
            logger.info("imgSuffix: " + imgSuffix);
            logger.info("filename: " + filename);

            ByteArrayOutputStream os = new ByteArrayOutputStream();
            //重画一下，要么会变色
            if ("jpg".equals(imgSuffix) || "jpeg".equals(imgSuffix)) {
                BufferedImage tag;
                tag = new BufferedImage(rotateImage.getWidth(), rotateImage.getHeight(), BufferedImage.TYPE_INT_BGR);
                Graphics g = tag.getGraphics();
                // 绘制缩小后的图
                g.drawImage(rotateImage, 0, 0, null);
                g.dispose();
                rotateImage = tag;
            }else if (imgSuffix.contains("/")){
                imgSuffix = "jpg";
                filename = StringUtil.getGuid32() + "." + imgSuffix;
            }
            ImageIO.write(rotateImage, imgSuffix, os);
            InputStream input = new ByteArrayInputStream(os.toByteArray());
            
            JSONObject json = PdfGenerateUtil.upload(input, null);
            JSONObject result = new JSONObject();
            result.put("url", json.getString("url"));
            
            return R.success().putData(result);
        }
        catch (Exception e) {
            logger.error("出现异常:{}",e);
        }
        finally {
            try {
                if(in != null){
                    in.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        
        return R.error("操作失败");
    }
    
    /**
	 * 上传文件
	 * @param uploadType String 上传类型
	 * @param fileName String 文件名称
	 * @param bytes byte[] 数据内容
	 * @param xh String 序号
	 * @return R
	 */
    @Override
    public R  uploadFile(String uploadType, String fileName, byte[] bytes, String xh){
        try {
        	JSONObject json = PdfGenerateUtil.upload(bytes, fileName);
            return R.success("上传成功！").putData(json)
            		.put("fileName", fileName)
                    .put("xh", xh);
        }
        catch (Exception e) {
            logger.error("上传文件出现异常:{}",e);
            return R.error("上传错误!");
        }
    }

    public Map<String, Object> getParam(String uuid, String parentId, Boolean isParent,
    		int orderId, int level, String name){
        Map<String, Object> param=new HashMap<String, Object>(16);
        param.put("id",uuid);
        param.put("parentId",parentId);
        param.put("isParent",isParent);
        param.put("orderId",orderId);
        param.put("level",level);
        
        //一级目录
        param.put("name",name);
        String []docment = {name};
        param.put("document",docment);

        return param;
    }
    
    @SuppressWarnings("rawtypes")
	public boolean insert(List list,String fileName){
    	return true;
//        DossierCatalogTemplate dossierCatalogTemplate=new DossierCatalogTemplate();
//        dossierCatalogTemplate.setName(fileName);
//        //0-私有   1-共享
//        dossierCatalogTemplate.setShare("0");
//        dossierCatalogTemplate.setType("0");
//        SessionUser user = SessionUserUtil.getSessionUser();
//        //新增
//        dossierCatalogTemplate.setId(StringUtil.getGuid32());
//        dossierCatalogTemplate.setUserId(user.getId());
//        dossierCatalogTemplate.setAddUser(user.getName());
//        dossierCatalogTemplate.setAddTime(new Date());
//        String data=list.toString();
//        dossierCatalogTemplate.setData(data);
//        return dossierCatalogTemplateService.insert(dossierCatalogTemplate);
    }

    /**
     * 读取excel内容
     * @param is InputStream 输入流
     * @param fileName String 文件名称
     * @return List
     * @throws Exception
     */
    @SuppressWarnings({ "unchecked", "rawtypes" })
	public static List readExcel(InputStream is, String fileName) throws Exception {
        List list = new ArrayList<>();
        //创建Excel工作薄
        Workbook work = getWorkbook(is, fileName);
        if (null == work) {
            throw new Exception("创建Excel工作薄为空！");
        }
        Row row = null;
        Cell cell = null;

        Sheet sheet = work.getSheetAt(0);
        for (int i = sheet.getFirstRowNum(); i <= sheet.getLastRowNum(); i++) {
            row = sheet.getRow(i);
            if (row == null || row.getFirstCellNum() == i) {
                continue;
            }
            List<Object> li = new ArrayList<>();
            for (int j = row.getFirstCellNum(); j < row.getLastCellNum(); j++) {
                cell = row.getCell(j);
                String str = getCellValue(cell);
                li.add(str);
            }
            list.add(li);
        }
        work.close();//这行报错的话  看下导入jar包的版本
        
        return list;
    }

    /**
     * 获取单元格的值
     * @param cell Cell 单元格
     * @return String
     */
    private static String getCellValue(Cell cell) {
        String cellValue = "";
        DataFormatter formatter = new DataFormatter();
        
        if (cell != null) {
            //判断单元格数据的类型，不同类型调用不同的方法
            switch (cell.getCellType()) {
                //数值类型
                case NUMERIC:
                    //进一步判断 ，单元格格式是日期格式
                    if (DateUtil.isCellDateFormatted(cell)) {
                        cellValue = formatter.formatCellValue(cell);
                    } else {
                        //数值
                        double value = cell.getNumericCellValue();
                        int intValue = (int) value;
                        cellValue = value - intValue == 0 ? String.valueOf(intValue) : String.valueOf(value);
                    }
                    break;
                case STRING:
                    cellValue = cell.getStringCellValue();
                    break;
                case BOOLEAN:
                    cellValue = String.valueOf(cell.getBooleanCellValue());
                    break;
                //判断单元格是公式格式，需要做一种特殊处理来得到相应的值
                case FORMULA:{
                    try{
                        cellValue = String.valueOf(cell.getNumericCellValue());
                    }catch(IllegalStateException e){
                        cellValue = String.valueOf(cell.getRichStringCellValue());
                    }
                }
                break;
                case BLANK:
                    cellValue = "";
                    break;
                case ERROR:
                    cellValue = "";
                    break;
                default:
                    cellValue = cell.toString().trim();
                    break;
            }
        }
        
        return cellValue.trim();
    }

    /**
     * 获取excel的Workbook
     * @param is InputStream 输入流
     * @param fileName String 文件名称
     * @return Workbook
     * @throws Exception
     */
    public static Workbook getWorkbook(InputStream is, String fileName) throws Exception {
        Workbook workbook = null;
        String fileType = fileName.substring(fileName.lastIndexOf("."));
        if (".xls".equals(fileType)) {
            workbook = new HSSFWorkbook(is);
        }
        else if (".xlsx".equals(fileType)) {
            workbook = new XSSFWorkbook(is);
        }
        else {
            throw new Exception("请上传excel文件！");
        }
        
        return workbook;
    }
}
