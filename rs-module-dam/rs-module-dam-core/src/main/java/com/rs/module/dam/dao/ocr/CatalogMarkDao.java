package com.rs.module.dam.dao.ocr;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.ocr.CatalogMarkDO;
import com.rs.module.dam.vo.ocr.CatalogMarkListReqVO;
import com.rs.module.dam.vo.ocr.CatalogMarkPageReqVO;

/**
* ocr相似度归目标识 Dao
*
* <AUTHOR>
*/
@Mapper
public interface CatalogMarkDao extends IBaseDao<CatalogMarkDO> {


    default PageResult<CatalogMarkDO> selectPage(CatalogMarkPageReqVO reqVO) {
        Page<CatalogMarkDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<CatalogMarkDO> wrapper = new LambdaQueryWrapperX<CatalogMarkDO>()
            .eqIfPresent(CatalogMarkDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(CatalogMarkDO::getMaterialId, reqVO.getMaterialId())
            .eqIfPresent(CatalogMarkDO::getCatalogId, reqVO.getCatalogId())
            .likeIfPresent(CatalogMarkDO::getCatalogName, reqVO.getCatalogName())
            .eqIfPresent(CatalogMarkDO::getPartCatalogId, reqVO.getPartCatalogId())
            .likeIfPresent(CatalogMarkDO::getName, reqVO.getName())
            .eqIfPresent(CatalogMarkDO::getFamiliarity, reqVO.getFamiliarity())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(CatalogMarkDO::getAddTime);
        }
        Page<CatalogMarkDO> catalogMarkPage = selectPage(page, wrapper);
        return new PageResult<>(catalogMarkPage.getRecords(), catalogMarkPage.getTotal());
    }
    default List<CatalogMarkDO> selectList(CatalogMarkListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CatalogMarkDO>()
            .eqIfPresent(CatalogMarkDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(CatalogMarkDO::getMaterialId, reqVO.getMaterialId())
            .eqIfPresent(CatalogMarkDO::getCatalogId, reqVO.getCatalogId())
            .likeIfPresent(CatalogMarkDO::getCatalogName, reqVO.getCatalogName())
            .eqIfPresent(CatalogMarkDO::getPartCatalogId, reqVO.getPartCatalogId())
            .likeIfPresent(CatalogMarkDO::getName, reqVO.getName())
            .eqIfPresent(CatalogMarkDO::getFamiliarity, reqVO.getFamiliarity())
        .orderByDesc(CatalogMarkDO::getAddTime));    }


    }
