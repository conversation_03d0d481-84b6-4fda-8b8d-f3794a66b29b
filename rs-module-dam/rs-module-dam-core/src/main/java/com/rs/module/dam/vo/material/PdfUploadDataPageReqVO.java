package com.rs.module.dam.vo.material;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel(description = "管理后台 - pdf上传数据分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PdfUploadDataPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("pdf上传地址")
    private String pdfUrl;

    @ApiModelProperty("pdf上传数据信息")
    private String uploadData;

    @ApiModelProperty("pdf文件名")
    private String pdfName;
    
    @ApiModelProperty("pdf文件大小")
    private Long pdfSize;

    @ApiModelProperty("pdf状态(0:已上传未处理,1:已下载处理中,2:已拆分完成,3:已删除,4:开始转换的pdf 取消转换 ，5：下载失败，6：pdf转换过程中进行取消  已暂停)")
    private Integer status;

    @ApiModelProperty("pdf页数")
    private Integer pageCount;

    @ApiModelProperty("关联dam_pdf_batch主键id")
    private String batchId;

    @ApiModelProperty("文件的md5值")
    private String md5;

    @ApiModelProperty("pdf转换图片的dpi值")
    private Integer dpi;
    
    @ApiModelProperty("材料来源")
    private String materialSource;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
