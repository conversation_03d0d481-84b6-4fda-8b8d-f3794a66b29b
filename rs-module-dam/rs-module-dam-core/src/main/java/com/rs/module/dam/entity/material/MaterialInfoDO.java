package com.rs.module.dam.entity.material;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 卷宗材料 DO
 *
 * <AUTHOR>
 */
@TableName("dam_material_info")
@KeySequence("dam_material_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialInfoDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 材料类型(0：未OCR,1：编目,2：组卷,3: 快速组卷)
     */
    private String type;
    /**
     * 材料名称
     */
    private String name;
    /**
     * 材料数量
     */
    private Integer pageCount;
    /**
     * 材料文书(json)
     */
    private String materialFile;
    /**
     * 模板ID
     */
    private String templateId;
    /**
     * 分卷目录Id
     */
    private String partCatalogId;
    /**
     * 目录Id
     */
    private String catalogId;
    /**
     * 材料序号
     */
    private Integer xh;
    /**
     * pdfId(关联dam_upload_pdf_data的主键)
     */
    private String pdfId;
    /**
     * 材料Id(关联dam_upload_pdf_data的material_id)
     */
    private String materialId;
    /**
     * 责任者
     */
    private String responsiblePerson;
    /**
     * 文号
     */
	private String symbol;
	/**
	 * 日期
	 */
	private Date date;
	/**
	 * 备注
	 */
	private String remark;
}
