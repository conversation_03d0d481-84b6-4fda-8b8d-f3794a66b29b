package com.rs.module.dam.vo.archive;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 卷宗封面 Response VO")
@Data
@EqualsAndHashCode(callSuper = false)
public class ArchiveCoverRespVO extends BaseVO implements TransPojo{
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("卷宗名称")
    private String jzmc;
    @ApiModelProperty("卷名")
    private String jm;
    @ApiModelProperty("组卷分卷目录的ID（如诉讼文书卷的目录ID）")
    private String partCatalogId;
    @ApiModelProperty("分卷类型（1是快速组卷，0是编目组卷）")
    private String fjlx;
    @ApiModelProperty("分卷PDF地址")
    private String pdfUrl;
    @ApiModelProperty("双层PDF地址")
    private String doublePdfUrl;
    @ApiModelProperty("pdf图片地址")
    private String imageUrl;
    @ApiModelProperty("预览图片地址")
    private String previewUrl;
    @ApiModelProperty("封面页数")
    private Integer coverPage;
    @ApiModelProperty("序号，用于排序")
    private Integer xh;
}
