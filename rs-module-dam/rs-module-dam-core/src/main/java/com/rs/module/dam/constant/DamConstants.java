package com.rs.module.dam.constant;

/**
 * 数字档案常量类
 * <AUTHOR>
 * @date 2025年4月17日
 */
public class DamConstants {
    
	/** 操作-新增 */
    public static final String OPERATE_ADD = "add";

    /** 操作-修改 */
    public static final String OPERATE_EDIT = "edit";

    /** 操作-删除 */
    public static final String OPERATE_DELETE = "delete";

    /** 操作-排序 */
    public static final String OPERATE_ORDER = "order";

    /** 操作-无改变 */
    public static final String OPERATE_NONE = "none";

    /** 操作-同步 (自定义模板) */
    public static final String OPERATE_UNIFY = "unify";

    /** 操作-从标准卷复制(自定义模板) */
    public static final String OPERATE_COPY = "copy";

    /** 操作-材料被修改（组卷） */
    public static final String OPERATE_FILEUPDATE = "fileupdate";
    
    /** windows 系统扫描上传 */
    public static final String SCAN_WINDOWS = "1";

    /** linux 系统 国产影源扫描仪上传 */
    public static final String SCAN_LINUX_WINMAGE = "2";
    
    /** 图片处理-旋转: 图片存储路径改变 */
    public static final String IMAGE_ROTARY = "01";

    /** 图片处理-剪切：图片顺序或图片所属目录变动 */
    public static final String IMAGE_CUT = "02";

    /** 图片处理-删除：删除单张、多张图片 */
    public static final String IMAGE_DELETE = "03";

    /** 图片处理-移动：图片所属目录移动 */
    public static final String IMAGE_MOVE = "04";

    /** 图片处理-拆分：将一份材料拆分为两份 */
    public static final String IMAGE_SPLIT = "05";

    /** 图片转换状态-转换失败 */
    public final static Integer IMAGE_CONVERT_FAIL = 0;

    /** 图片转换状态-转换成功 */
    public final static Integer IMAGE_CONVERT_SUCCESS = 1;

    /** 图片转换状态-pdf中途取消转换 */
    public final static Integer IMAGE_CONVERT_CANCEL = 2;
    
    /** 材料类型: 未OCR(未编目) */
    public static final String MATERIAL_TYPE_WBM = "0";
    
    /** 材料类型: 已编目页面 */
    public static final String MATERIAL_TYPE_YBM = "1";
    
    /** 材料类型: 完成组卷页面 */
    public static final String MATERIAL_TYPE_ZJ = "2";
    
    /** 材料类型: 快速组卷(卷宗导入页面) */
    public static final String MATERIAL_TYPE_KSZJ = "3";

    /** 组卷类型-编目组卷 */
    public static final String ZJ_TYPE_BMZJ = "0";

    /** 组卷类型-快速组卷 */
    public static final String ZJ_TYPE_KSZJ = "1";
    
    
    /*********************【任务相关】*********************/
    
    /** 是否有未执行的编目任务 */
    public static volatile boolean BM_JOB_EXECUTE = true;
    
    /** 是否有未执行的PDF任务 */
    public static boolean PDF_JOB_EXECUTE = true;
    
    /** job运行状态-未执行 */
    public static final String JOB_STATE_NO = "0";

    /** job运行状态-执行成功 */
    public static final String JOB_STATE_YES = "1";

    /** job运行状态-执行异常 */
    public static final String JOB_STATE_ERROR = "2";

    /** job运行状态-ocr服务无资源处理请求, job任务等待处理 */
    public static final String JOB_STATE_PENDING = "3";

    /** job运行状态-未开启ocr服务,任务不执行 */
    public static final String JOB_STATE_STOP = "4";

    /** job运行状态-任务检测过程中取消 */
    public static final String JOB_STATE_CANCEL = "5";
    
    /** job任务类型-获取编目组卷生成的PDF */
    public static final String JOB_TYPE_BMZJPDF = "0";

    /** job任务类型-获取快速组卷生成的PDF */
    public static final String JOB_TYPE_KSZJPDF = "1";

    /** job任务类型-获取编目结果 */
    public static final String JOB_TYPE_BMJG = "2";
    
    
    /*********************【文件类型】*********************/
    
    /** 文件类型-未编目材料 */
    public static final String FILE_TYPE_WBM = "0";

    /** 文件类型-编目材料 */
    public static final String FILE_TYPE_BM = "1";

    /** 文件类型-组卷材料 */
    public static final String FILE_TYPE_ZJ = "2";

    /** 文件类型-卷宗导入 */
    public static final String FILE_TYPE_JZDR = "3";
    
    
    /*********************【格式化审查】*********************/
    
    /**是否有格式化审查任务*/
    public static boolean HAVING_FORMAT_CHECK = true;
    
    /**格式化审查结果展示*/
    public static final String CHECK_FORMAT_RESULT_SHOW = "1";

    /**格式化审查结果不展示*/
    public static final String CHECK_FORMAT_RESULT_HIDDEN = "0";
    
    /**格式化审查-未编目**/
    public static final String FORMAT_CHECK_UN_BM = "0";

    /**格式化审查-已编目*/
    public static final String FORMAT_CHECK_BM = "1";

    /**格式化审查-智能阅卷*/
    public static final String FORMAT_CHECK_REVIEWS = "2";
    
    /**格式化审查任务未开启*/
    public static final String FORMAT_CHECK_MINCE_UN_OPEN = "0";

    /**格式化审查任务开启*/
    public static final String FORMAT_CHECK_MINCE_OPEN = "1";
    
    /**格式化审查，多次检测，勾选任务类型： 新增的任务,需要发起检测,入库*/
    public static final String CHECK_FORMAT_MINCE_TYPE_ADD = "1";

    /**格式化审查，多次检测，勾选任务类型： 新增的任务,不需要发起检测,入库*/
    public static final String CHECK_FORMAT_MINCE_TYPE_CHECK = "2";

    /**格式化审查，多次检测，勾选任务类型： 新增的任务,不需要发起检测,是否可以入库根据1,2判断*/
    public static final String CHECK_FORMAT_MINCE_TYPE_WAVE = "3";

    /**格式化审查，多次检测，勾选任务类型： 复制的上一次的任务,是否可以入库根据1,2判断*/
    public static final String CHECK_FORMAT_MINCE_TYPE_COPY = "4";
    
    /**图片方向合规性检测*/
    public static final String PIC_ANGLE = "12";
    
    /**
     * 材料属性(以下检测结果相关的参数,前端在进行图片操作后,需要在file中携带传入后端保存)
     * <AUTHOR>
     * @date 2025年4月26日
     */
    public static class MaterialColumn{

         /**文书填写规范检测-缺失文书号*/
         public static final String  LOSE_DOC_CODE = "loseDocCode";

         /**文书填写规范检测-缺失嫌疑人信息*/
         public static final String LOSE_RYXX = "loseRyxx";

         /**文书填写规范检测-缺失案件单位信息*/
         public static final String LOSE_AJDW = "loseAjdw";

         /**文书规范性检测是否通过, 1:通过, 0:不通过*/
         public static final String INFO_PASS = "infoPass";

         /**文书重复页检测-是否重复,1:重复*/
         public static final String REPEAT = "repeat";

         /**文书重复页检测-重复页标记*/
         public static final String REPEAT_MARK = "repeatMark";

         /**文书重复页检测-重复页相似度*/
         public static final String SIM_SCORE = "simScore";

         /**文书空白页检测-是否空白页 1:是*/
         public static final String BLANK_SPACE = "blankSpace";

         /**图片方向合规性检测 -图片倾斜角度*/
         public static final String ANGLE = "angle";

         /**材料瑕疵页标识*/
         public static final String FLAW = "isRegular";

         /**材料设置瑕疵页的原因是因为图片有阴影*/
         public static final String SPOT = "spot";

         /**材料设置瑕疵页的原因是因为图片歪斜*/
         public static final String ASKEW = "askew";

         /**签名签章捺印检测-缺失签章个数*/
         public static final String MISS_SEAL = "missSeal";

         /**签名签章捺印检测-缺失签名个数*/
         public static final String MISS_SIGNATURE = "missSignature";

         /**签名签章捺印检测-缺失捺印个数*/
         public static final String MISS_FINGERPRINT = "missFingerprint";

         /**材料无法识别*/
         public static final String FILE_CANNOT_IDENTIFY = "unIdentify";

         /**检测进度-签章识别进度*/
         public static final String SEAL_PROCESS = "sealProcess";

         /**检测进度-捺印识别进度 */
         public static final String FINGERPRINT_PROCESS = "fingerprintProcess";

         /**检测进度-签名识别进度 */
         public static final String SIGNATURE_PROCESS = "signatureProcess";

         /**检测进度-文书信息检测进度 */
         public static final String EXTRACT_PROCESS = "extractProcess";

         /**检测进度-重复页检测进度 */
         public static final String REPEAT_PROCESS = "repeatProcess";

         /**检测进度-图片方向合规性检测进度 */
         public static final String FLAW_PROCESS = "flawProcess";

         /**检测进度-空白页检测进度*/
         public static final String BLANK_PROCESS = "blankProcess";

         /**格式化审查任务id*/
         public static final String JOB_MINCE_ID = "minceId";

         /**格式化审查任务进度*/
         public static final String JOB_MINCE_PROCESS = "process";
     }
}
