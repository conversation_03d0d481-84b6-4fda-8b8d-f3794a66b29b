package com.rs.module.dam.service.prisoner;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import com.alibaba.fastjson.JSONArray;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.prisoner.PrisonerInfoDO;
import com.rs.module.dam.vo.prisoner.PrisonerInfoListReqVO;
import com.rs.module.dam.vo.prisoner.PrisonerInfoPageReqVO;
import com.rs.module.dam.vo.prisoner.PrisonerInfoSaveReqVO;

/**
 * 卷宗监管人员 Service 接口
 *
 * <AUTHOR>
 */
public interface PrisonerInfoService extends IBaseService<PrisonerInfoDO>{

    /**
     * 创建卷宗监管人员
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrisonerInfo(@Valid PrisonerInfoSaveReqVO createReqVO);

    /**
     * 更新卷宗监管人员
     *
     * @param updateReqVO 更新信息
     */
    void updatePrisonerInfo(@Valid PrisonerInfoSaveReqVO updateReqVO);

    /**
     * 删除卷宗监管人员
     *
     * @param id 编号
     */
    void deletePrisonerInfo(String id);

    /**
     * 获得卷宗监管人员
     *
     * @param id 编号
     * @return 卷宗监管人员
     */
    PrisonerInfoDO getPrisonerInfo(String id);

    /**
     * 获得卷宗监管人员
     *
     * @param dabh 档案编号
     * @return 卷宗监管人员
     */
    PrisonerInfoDO getByDabh(String dabh);

    /**
    * 获得卷宗监管人员分页
    *
    * @param pageReqVO 分页查询
    * @return 卷宗监管人员分页
    */
    PageResult<PrisonerInfoDO> getPrisonerInfoPage(PrisonerInfoPageReqVO pageReqVO);

    /**
    * 获得卷宗监管人员列表
    *
    * @param listReqVO 查询条件
    * @return 卷宗监管人员列表
    */
    List<PrisonerInfoDO> getPrisonerInfoList(PrisonerInfoListReqVO listReqVO);

    /**
     * 更新卷宗状态
     * @param jgrybm String 监管人员编码
     * @param jzzt String 卷宗状态
     * @param zjsj Date 组卷时间
     * @param gdsj Date 归档时间
     */
    void updateJzzt(String jgrybm, String jzzt, Date zjsj, Date gdsj);
    
    /**
     * 获取卷宗导出地址
     * @param jgrybm String 监管人员编码
     * @param zjCatalog String 卷宗目录
     * @return String
     */
    public String getExportUrl(String jgrybm, String zjCatalog);
    
    /**
     * 包含封面的pdf生成
     * @param jgrybm String 监管人员编码
     * @param zjCatalog String 卷宗目录
     * @param params Map<String, Object> 其它参数
     */
    public void coverPdfGenerate(String jgrybm, JSONArray zjCatalogArray, Map<String, Object> params);
}
