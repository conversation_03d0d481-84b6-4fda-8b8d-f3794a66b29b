package com.rs.module.dam.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.util.CollectionUtils;

import cn.hutool.core.convert.Convert;

/**
 * 对象工具类
 * <AUTHOR>
 * @date 2025年4月22日
 */
public class ObjectUtil {

	/**
	 * 格式化文件路径
	 * @param path String 路径
	 * @return String
	 */
	public static String formatFilePath(String path) {
		Map<String, Object> map = new HashMap<>();
		map.put("path", path);
		Map<String, Object> pathMap= formatObject(map);
		return String.valueOf(pathMap.get("path"));
	}



	/** 
	 * 格式化对象
	 * Map<String, Object>
	 * @param obj E 对象
	 * @return Map<String, Object>
	 */
	public static <E> Map<String, Object> formatObject(E obj) {
		List<Map<String,Object>> list = formatObjects(Arrays.asList(obj));
		if(list.size() > 0){
			return list.get(0);
		}
		else{
			return null;
		}
	}

	/**
	 * 格式化多个对象
	 * @param <T>
	 * @param list List<T> 多个对象
	 * @return List<Map<String,Object>>
	 */
	@SuppressWarnings("unchecked")
	public  static <T> List<Map<String,Object>> formatObjects(List<T> list){
		if(CollectionUtils.isEmpty(list)) {
			return null;
		} 
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>(0);
		for(Object obj : list) {
			Map<String,Object> objMap = null;
			if(obj instanceof Map) {
				objMap = (Map<String, Object>)obj;
			}
			else {
				try {
					objMap = Convert.toMap(String.class, Object.class, obj);
				}
				catch (Exception e) {
					e.printStackTrace();
				}
			}
			result.add(objMap);
		}
		
		return result;
	}
}
