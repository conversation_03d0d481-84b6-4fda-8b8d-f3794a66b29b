package com.rs.module.dam.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO_;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 卷宗配置机构关联 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 卷宗配置机构关联新增/修改 Request VO")
@TableName("dam_sys_config_group_org")
@KeySequence("dam_sys_config_group_org_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfigGroupOrgDO extends BaseDO_ {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;
    /**
     * 配置组Id
     */
    @ApiModelProperty("配置组Id")
    private String groupId;
    /**
     * 适用单位编号
     */
    @ApiModelProperty("适用单位编号")
    private String configOrgCode;
    /**
     * 适用单位名称
     */
    @ApiModelProperty("适用单位名称")
    private String configOrgName;
    /**
     * 适用分局编号
     */
    @ApiModelProperty("适用分局编号")
    private String configRegCode;
    /**
     * 适用区域名称
     */
    @ApiModelProperty("适用区域名称")
    private String configRegName;
    /**
     * 适用市局编号
     */
    @ApiModelProperty("适用市局编号")
    private String configCityCode;
    /**
     * 适用城市名称
     */
    @ApiModelProperty("适用城市名称")
    private String configCityName;

}
