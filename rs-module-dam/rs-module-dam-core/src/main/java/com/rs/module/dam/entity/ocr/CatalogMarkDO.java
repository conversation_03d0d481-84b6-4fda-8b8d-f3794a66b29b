package com.rs.module.dam.entity.ocr;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO_;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * ocr相似度归目标识 DO
 *
 * <AUTHOR>
 */
@TableName("dam_ocr_catalog_mark")
@KeySequence("dam_ocr_catalog_mark_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CatalogMarkDO extends BaseDO_ {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 材料Id
     */
    private String materialId;
    /**
     * 目录Id
     */
    private String catalogId;
    /**
     * 目录名称
     */
    private String catalogName;
    /**
     * 分卷目录Id
     */
    private String partCatalogId;
    /**
     * 识别材料名称
     */
    private String name;
    /**
     * 相似度
     */
    private String familiarity;

}
