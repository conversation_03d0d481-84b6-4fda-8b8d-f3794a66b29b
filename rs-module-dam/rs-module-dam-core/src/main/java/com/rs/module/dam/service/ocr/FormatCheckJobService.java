package com.rs.module.dam.service.ocr;

import java.util.List;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.ocr.FormatCheckJobDO;
import com.rs.module.dam.vo.ocr.FormatCheckJobListReqVO;
import com.rs.module.dam.vo.ocr.FormatCheckJobPageReqVO;
import com.rs.module.dam.vo.ocr.FormatCheckJobSaveReqVO;

/**
 * ocr格式化审查任务 Service 接口
 *
 * <AUTHOR>
 */
public interface FormatCheckJobService extends IBaseService<FormatCheckJobDO>{

    /**
     * 创建ocr格式化审查任务
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createFormatCheckJob(@Valid FormatCheckJobSaveReqVO createReqVO);

    /**
     * 更新ocr格式化审查任务
     *
     * @param updateReqVO 更新信息
     */
    void updateFormatCheckJob(@Valid FormatCheckJobSaveReqVO updateReqVO);

    /**
     * 删除ocr格式化审查任务
     *
     * @param id 编号
     */
    void deleteFormatCheckJob(String id);

    /**
     * 获得ocr格式化审查任务
     *
     * @param id 编号
     * @return ocr格式化审查任务
     */
    FormatCheckJobDO getFormatCheckJob(String id);

    /**
    * 获得ocr格式化审查任务分页
    *
    * @param pageReqVO 分页查询
    * @return ocr格式化审查任务分页
    */
    PageResult<FormatCheckJobDO> getFormatCheckJobPage(FormatCheckJobPageReqVO pageReqVO);

    /**
    * 获得ocr格式化审查任务列表
    *
    * @param listReqVO 查询条件
    * @return ocr格式化审查任务列表
    */
    List<FormatCheckJobDO> getFormatCheckJobList(FormatCheckJobListReqVO listReqVO);


}
