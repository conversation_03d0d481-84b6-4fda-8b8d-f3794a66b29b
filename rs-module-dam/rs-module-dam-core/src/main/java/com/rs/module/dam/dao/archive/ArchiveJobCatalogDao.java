package com.rs.module.dam.dao.archive;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.archive.ArchiveJobCatalogDO;
import com.rs.module.dam.vo.archive.ArchiveJobCatalogListReqVO;
import com.rs.module.dam.vo.archive.ArchiveJobCatalogPageReqVO;

/**
* 卷宗归档任务目录 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ArchiveJobCatalogDao extends IBaseDao<ArchiveJobCatalogDO> {


    default PageResult<ArchiveJobCatalogDO> selectPage(ArchiveJobCatalogPageReqVO reqVO) {
        Page<ArchiveJobCatalogDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ArchiveJobCatalogDO> wrapper = new LambdaQueryWrapperX<ArchiveJobCatalogDO>()
            .eqIfPresent(ArchiveJobCatalogDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(ArchiveJobCatalogDO::getBatchId, reqVO.getBatchId())
            .eqIfPresent(ArchiveJobCatalogDO::getMaterialId, reqVO.getMaterialId())
            .eqIfPresent(ArchiveJobCatalogDO::getMaterialXh, reqVO.getMaterialXh())
            .eqIfPresent(ArchiveJobCatalogDO::getFileXh, reqVO.getFileXh())
            .eqIfPresent(ArchiveJobCatalogDO::getDocTitle, reqVO.getDocTitle())
            .eqIfPresent(ArchiveJobCatalogDO::getCatalogId, reqVO.getCatalogId())
            .eqIfPresent(ArchiveJobCatalogDO::getState, reqVO.getState())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(ArchiveJobCatalogDO::getAddTime);
        }
        Page<ArchiveJobCatalogDO> archiveJobCatalogPage = selectPage(page, wrapper);
        return new PageResult<>(archiveJobCatalogPage.getRecords(), archiveJobCatalogPage.getTotal());
    }
    default List<ArchiveJobCatalogDO> selectList(ArchiveJobCatalogListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ArchiveJobCatalogDO>()
            .eqIfPresent(ArchiveJobCatalogDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(ArchiveJobCatalogDO::getBatchId, reqVO.getBatchId())
            .eqIfPresent(ArchiveJobCatalogDO::getMaterialId, reqVO.getMaterialId())
            .eqIfPresent(ArchiveJobCatalogDO::getMaterialXh, reqVO.getMaterialXh())
            .eqIfPresent(ArchiveJobCatalogDO::getFileXh, reqVO.getFileXh())
            .eqIfPresent(ArchiveJobCatalogDO::getDocTitle, reqVO.getDocTitle())
            .eqIfPresent(ArchiveJobCatalogDO::getCatalogId, reqVO.getCatalogId())
            .eqIfPresent(ArchiveJobCatalogDO::getState, reqVO.getState())
        .orderByDesc(ArchiveJobCatalogDO::getAddTime));    }


    }
