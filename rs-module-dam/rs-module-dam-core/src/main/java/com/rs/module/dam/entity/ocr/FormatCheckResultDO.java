package com.rs.module.dam.entity.ocr;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO_;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * ocr格式化审查结果 DO
 *
 * <AUTHOR>
 */
@TableName("dam_ocr_format_check_result")
@KeySequence("dam_ocr_format_check_result_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FormatCheckResultDO extends BaseDO_ {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 关联dam_format_check_job的Id
     */
    private String jobId;
    /**
     * 关联dam_format_check_job的任务类型(0:未编目任务, 1:已编目任务, 2:智能阅卷任务)
     */
    private String jobType;
    /**
     * 材料Id
     */
    private String materialId;
    /**
     * 图片id
     */
    private String fileId;
    /**
     * 文件地址
     */
    private String fileUrl;
    /**
     * 检测类型(1:签章检测,2:签名检测,3:捺印检测,4:空白页检测,5:重复页检测,6:瑕疵页检测,7:文书规范检测)
     */
    private String checkType;
    /**
     * 材料序号
     */
    private Integer xh;
    /**
     * 分卷目录Id
     */
    private String partCatalogId;
    /**
     * 材料目录Id
     */
    private String catalogId;
    /**
     * 材料目录对应名称
     */
    private String catalogName;
    /**
     * 重复页分组标记
     */
    private String repeatMark;
    /**
     * 签名签章捺印检测结果
     */
    private String ssfcsimResult;
    /**
     * 缺失签章数量
     */
    private Integer missSeal;
    /**
     * 缺失签名数量
     */
    private Integer missSignature;
    /**
     * 缺失捺印数量
     */
    private Integer missFingerprint;
    /**
     * 格式审查结果
     */
    private String formatResult;
    /**
     * 文书信息提取信息
     */
    private String fileInfo;
    /**
     * 检测结果是否展示(1:是, 0:否)
     */
    private String showData;
    /**
     * 图片宽
     */
    private Integer width;
    /**
     * 图片高
     */
    private Integer height;
    /**
     * 瑕疵页检测结果
     */
    private String flawData;
    /**
     * 文书填写规范检测缺失内容
     */
    private String loseInfo;

}
