package com.rs.module.dam.service.material;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.dao.material.MaterialLabelHistoryDao;
import com.rs.module.dam.entity.material.MaterialLabelHistoryDO;
import com.rs.module.dam.vo.material.MaterialLabelHistoryListReqVO;
import com.rs.module.dam.vo.material.MaterialLabelHistoryPageReqVO;
import com.rs.module.dam.vo.material.MaterialLabelHistorySaveReqVO;


/**
 * 卷宗材料标签历史 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MaterialLabelHistoryServiceImpl extends BaseServiceImpl<MaterialLabelHistoryDao, MaterialLabelHistoryDO> implements MaterialLabelHistoryService {

    @Resource
    private MaterialLabelHistoryDao materialLabelHistoryDao;

    @Override
    public String createMaterialLabelHistory(MaterialLabelHistorySaveReqVO createReqVO) {
        // 插入
        MaterialLabelHistoryDO materialLabelHistory = BeanUtils.toBean(createReqVO, MaterialLabelHistoryDO.class);
        materialLabelHistoryDao.insert(materialLabelHistory);
        // 返回
        return materialLabelHistory.getId();
    }

    @Override
    public void updateMaterialLabelHistory(MaterialLabelHistorySaveReqVO updateReqVO) {
        // 校验存在
        validateMaterialLabelHistoryExists(updateReqVO.getId());
        // 更新
        MaterialLabelHistoryDO updateObj = BeanUtils.toBean(updateReqVO, MaterialLabelHistoryDO.class);
        materialLabelHistoryDao.updateById(updateObj);
    }

    @Override
    public void deleteMaterialLabelHistory(String id) {
        // 校验存在
        validateMaterialLabelHistoryExists(id);
        // 删除
        materialLabelHistoryDao.deleteById(id);
    }

    private void validateMaterialLabelHistoryExists(String id) {
        if (materialLabelHistoryDao.selectById(id) == null) {
            throw new ServerException("卷宗材料标签历史数据不存在");
        }
    }

    @Override
    public MaterialLabelHistoryDO getMaterialLabelHistory(String id) {
        return materialLabelHistoryDao.selectById(id);
    }

    @Override
    public PageResult<MaterialLabelHistoryDO> getMaterialLabelHistoryPage(MaterialLabelHistoryPageReqVO pageReqVO) {
        return materialLabelHistoryDao.selectPage(pageReqVO);
    }

    @Override
    public List<MaterialLabelHistoryDO> getMaterialLabelHistoryList(MaterialLabelHistoryListReqVO listReqVO) {
        return materialLabelHistoryDao.selectList(listReqVO);
    }


}
