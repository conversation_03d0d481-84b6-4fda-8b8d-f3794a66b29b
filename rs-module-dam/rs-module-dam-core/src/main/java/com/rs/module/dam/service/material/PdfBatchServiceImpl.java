package com.rs.module.dam.service.material;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.dao.material.PdfBatchDao;
import com.rs.module.dam.entity.material.PdfBatchDO;
import com.rs.module.dam.vo.material.PdfBatchListReqVO;
import com.rs.module.dam.vo.material.PdfBatchPageReqVO;
import com.rs.module.dam.vo.material.PdfBatchSaveReqVO;


/**
 * pdf处理批次 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PdfBatchServiceImpl extends BaseServiceImpl<PdfBatchDao, PdfBatchDO> implements PdfBatchService {

    @Resource
    private PdfBatchDao pdfBatchDao;

    @Override
    public String createPdfBatch(PdfBatchSaveReqVO createReqVO) {
        // 插入
        PdfBatchDO pdfBatch = BeanUtils.toBean(createReqVO, PdfBatchDO.class);
        pdfBatchDao.insert(pdfBatch);
        // 返回
        return pdfBatch.getId();
    }

    @Override
    public void updatePdfBatch(PdfBatchSaveReqVO updateReqVO) {
        // 校验存在
        validatePdfBatchExists(updateReqVO.getId());
        // 更新
        PdfBatchDO updateObj = BeanUtils.toBean(updateReqVO, PdfBatchDO.class);
        pdfBatchDao.updateById(updateObj);
    }

    @Override
    public void deletePdfBatch(String id) {
        // 校验存在
        validatePdfBatchExists(id);
        // 删除
        pdfBatchDao.deleteById(id);
    }

    private void validatePdfBatchExists(String id) {
        if (pdfBatchDao.selectById(id) == null) {
            throw new ServerException("pdf处理批次数据不存在");
        }
    }

    @Override
    public PdfBatchDO getPdfBatch(String id) {
        return pdfBatchDao.selectById(id);
    }

    @Override
    public PageResult<PdfBatchDO> getPdfBatchPage(PdfBatchPageReqVO pageReqVO) {
        return pdfBatchDao.selectPage(pageReqVO);
    }

    @Override
    public List<PdfBatchDO> getPdfBatchList(PdfBatchListReqVO listReqVO) {
        return pdfBatchDao.selectList(listReqVO);
    }


}
