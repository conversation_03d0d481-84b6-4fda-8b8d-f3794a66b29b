package com.rs.module.dam.service.material;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.material.PdfUploadDataDO;
import com.rs.module.dam.vo.material.PdfUploadDataListReqVO;
import com.rs.module.dam.vo.material.PdfUploadDataPageReqVO;
import com.rs.module.dam.vo.material.PdfUploadDataSaveReqVO;

/**
 * pdf上传数据 Service 接口
 *
 * <AUTHOR>
 */
public interface PdfUploadDataService extends IBaseService<PdfUploadDataDO>{

    /**
     * 创建pdf上传数据
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPdfUploadData(@Valid PdfUploadDataSaveReqVO createReqVO);

    /**
     * 更新pdf上传数据
     *
     * @param updateReqVO 更新信息
     */
    void updatePdfUploadData(@Valid PdfUploadDataSaveReqVO updateReqVO);

    /**
     * 删除pdf上传数据
     *
     * @param id 编号
     */
    void deletePdfUploadData(String id);

    /**
     * 获得pdf上传数据
     *
     * @param id 编号
     * @return pdf上传数据
     */
    PdfUploadDataDO getPdfUploadData(String id);

    /**
    * 获得pdf上传数据分页
    *
    * @param pageReqVO 分页查询
    * @return pdf上传数据分页
    */
    PageResult<PdfUploadDataDO> getPdfUploadDataPage(PdfUploadDataPageReqVO pageReqVO);

    /**
    * 获得pdf上传数据列表
    *
    * @param listReqVO 查询条件
    * @return pdf上传数据列表
    */
    List<PdfUploadDataDO> getPdfUploadDataList(PdfUploadDataListReqVO listReqVO);

    /**
	 * 根据类型获取转换的pdf记录
	 * @param params Map<String, Object> 查询参数
	 * @return List<PdfUploadDataDO>
	 */
    List<PdfUploadDataDO> selectConvertPdfWithType(Map<String, Object> params);
    
	/**
	 * 查询重复的pdf上传文件
	 * @param params Map<String, Object> 查询参数
	 * @return List<PdfUploadDataDO>
	 */
	List<PdfUploadDataDO> selectRepeatPdf(Map<String, Object> params);
}
