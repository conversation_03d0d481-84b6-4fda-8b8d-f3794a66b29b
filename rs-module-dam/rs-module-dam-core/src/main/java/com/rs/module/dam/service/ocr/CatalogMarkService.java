package com.rs.module.dam.service.ocr;

import java.util.List;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.ocr.CatalogMarkDO;
import com.rs.module.dam.vo.ocr.CatalogMarkListReqVO;
import com.rs.module.dam.vo.ocr.CatalogMarkPageReqVO;
import com.rs.module.dam.vo.ocr.CatalogMarkSaveReqVO;

/**
 * ocr相似度归目标识 Service 接口
 *
 * <AUTHOR>
 */
public interface CatalogMarkService extends IBaseService<CatalogMarkDO>{

    /**
     * 创建ocr相似度归目标识
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCatalogMark(@Valid CatalogMarkSaveReqVO createReqVO);

    /**
     * 更新ocr相似度归目标识
     *
     * @param updateReqVO 更新信息
     */
    void updateCatalogMark(@Valid CatalogMarkSaveReqVO updateReqVO);

    /**
     * 删除ocr相似度归目标识
     *
     * @param id 编号
     */
    void deleteCatalogMark(String id);

    /**
     * 获得ocr相似度归目标识
     *
     * @param id 编号
     * @return ocr相似度归目标识
     */
    CatalogMarkDO getCatalogMark(String id);

    /**
    * 获得ocr相似度归目标识分页
    *
    * @param pageReqVO 分页查询
    * @return ocr相似度归目标识分页
    */
    PageResult<CatalogMarkDO> getCatalogMarkPage(CatalogMarkPageReqVO pageReqVO);

    /**
    * 获得ocr相似度归目标识列表
    *
    * @param listReqVO 查询条件
    * @return ocr相似度归目标识列表
    */
    List<CatalogMarkDO> getCatalogMarkList(CatalogMarkListReqVO listReqVO);


}
