package com.rs.module.dam.util;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * ip处理工具类
 * <AUTHOR>
 * @date 2025年4月26日
 */
public class IpUtil {

    public static String getHostIp(){
        try {
            InetAddress addr = InetAddress.getLocalHost();
            return addr.getHostAddress();
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        return "";
    }

}
