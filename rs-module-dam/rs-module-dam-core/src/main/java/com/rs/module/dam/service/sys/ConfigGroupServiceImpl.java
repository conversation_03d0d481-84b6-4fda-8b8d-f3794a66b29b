package com.rs.module.dam.service.sys;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.dao.sys.ConfigGroupDao;
import com.rs.module.dam.dao.sys.ConfigGroupOrgDao;
import com.rs.module.dam.entity.sys.ConfigGroupDO;
import com.rs.module.dam.entity.sys.ConfigGroupOrgDO;
import com.rs.module.dam.vo.sys.ConfigGroupListReqVO;
import com.rs.module.dam.vo.sys.ConfigGroupPageReqVO;
import com.rs.module.dam.vo.sys.ConfigGroupSaveReqVO;


/**
 * 卷宗配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ConfigGroupServiceImpl extends BaseServiceImpl<ConfigGroupDao, ConfigGroupDO> implements ConfigGroupService {

    @Resource
    private ConfigGroupDao configGroupDao;
    @Resource
    private ConfigGroupOrgDao configGroupOrgDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createConfigGroup(ConfigGroupSaveReqVO createReqVO) {
        // 插入
        ConfigGroupDO configGroup = BeanUtils.toBean(createReqVO, ConfigGroupDO.class);
        saveOrUpdate(configGroup);

        // 插入子表
//        createConfigGroupOrgList(configGroup.getId(), createReqVO.getConfigGroupOrgs());
        // 返回
        return configGroup.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateConfigGroup(ConfigGroupSaveReqVO updateReqVO) {
        // 校验存在
        validateConfigGroupExists(updateReqVO.getId());
        // 更新
        ConfigGroupDO updateObj = BeanUtils.toBean(updateReqVO, ConfigGroupDO.class);
        configGroupDao.updateById(updateObj);

        // 更新子表
//        updateConfigGroupOrgList(updateReqVO.getId(), updateReqVO.getConfigGroupOrgs());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteConfigGroup(String id) {
        // 校验存在
        validateConfigGroupExists(id);
        // 删除
        configGroupDao.deleteById(id);

        // 删除子表
//        deleteConfigGroupOrgByGroupId(id);
    }

    private void validateConfigGroupExists(String id) {
        if (configGroupDao.selectById(id) == null) {
            throw new ServerException("卷宗配置数据不存在");
        }
    }

    @Override
    public ConfigGroupDO getConfigGroup(String id) {
        return configGroupDao.selectById(id);
    }

    @Override
    public ConfigGroupDO getByOrgCode(String orgCode) {
        return getOne(new LambdaQueryWrapper<ConfigGroupDO>().eq(ConfigGroupDO::getOrgCode, orgCode));
    }

    @Override
    public PageResult<ConfigGroupDO> getConfigGroupPage(ConfigGroupPageReqVO pageReqVO) {
        return configGroupDao.selectPage(pageReqVO);
    }

    @Override
    public List<ConfigGroupDO> getConfigGroupList(ConfigGroupListReqVO listReqVO) {
        return configGroupDao.selectList(listReqVO);
    }

    /**
     * 获取用户可用的卷宗配置
     * @param orgCode String 机构代码
     * @return ConfigGroupDO
     */
    @Override
    public ConfigGroupDO getUserConfigGroup(String orgCode) {
    	QueryWrapper<ConfigGroupDO> wrapper = new QueryWrapper<>();
    	wrapper.eq("org_code", orgCode);
    	return getOne(wrapper);
    }

    // ==================== 子表（卷宗配置机构关联） ====================

    @Override
    public List<ConfigGroupOrgDO> getConfigGroupOrgListByGroupId(String groupId) {
        return configGroupOrgDao.selectListByGroupId(groupId);
    }

    private void createConfigGroupOrgList(String groupId, List<ConfigGroupOrgDO> list) {
        list.forEach(o -> o.setGroupId(groupId));
        list.forEach(o -> configGroupOrgDao.insert(o));
    }

    @SuppressWarnings("unused")
	private void updateConfigGroupOrgList(String groupId, List<ConfigGroupOrgDO> list) {
        deleteConfigGroupOrgByGroupId(groupId);
		list.forEach(o -> o.setId(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createConfigGroupOrgList(groupId, list);
    }

    private void deleteConfigGroupOrgByGroupId(String groupId) {
        configGroupOrgDao.deleteByGroupId(groupId);
    }

}
