package com.rs.module.dam.vo.material;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - pdf处理批次 Response VO")
@Data
@EqualsAndHashCode(callSuper = false)
public class PdfBatchRespVO extends BaseVO implements TransPojo{
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("类型(0：未OCR,1:编目,2：组卷 3: 快速组卷)")
    private String type;
    @ApiModelProperty("目录Id")
    private String catalogId;
    @ApiModelProperty("分卷目录Id")
    private String partCatalogId;
    @ApiModelProperty("编目目录名称")
    private String name;
    @ApiModelProperty("目录序号")
    private Integer xh;
    @ApiModelProperty("批次Id")
    private String batchId;
    @ApiModelProperty("业务Id")
    private String businessId;
    @ApiModelProperty("任务处理失败否日志")
    private String errorLog;
}
