package com.rs.module.dam.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 卷宗状态枚举类
 * <AUTHOR>
 * @date 2025年4月22日
 */
@AllArgsConstructor
@Getter
public enum JzztEnum {

	YDJ("01", "已登记"),
	
	YZJ("02", "已组卷"),
	
	YGD("03", "已归档");

	//状态类型
	private String type;
	
	//状态描述
	private String remark;
	
	/**
	 * 根据类型获取编目类型
	 * @param type String 编目类型
	 * @return BmTypeEnum
	 */
	public static JzztEnum getByType(String type) {
		for(JzztEnum bmTypeEnum : values()) {
			if(bmTypeEnum.getType().equals(type)) {
				return bmTypeEnum;
			}
		}
		return null;
	}
}
