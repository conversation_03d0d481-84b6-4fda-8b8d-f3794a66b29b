package com.rs.module.dam.config;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;

import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.bsp.common.util.ServiceLocator;
import com.rs.module.dam.websocket.WebSocketMsg;

import lombok.extern.slf4j.Slf4j;

/**
 * websocket服务端
 * <AUTHOR>
 * @date 2025年4月22日
 */
@ServerEndpoint("/webSocket/{sid}")
@Component
@Slf4j
public class WebSocketServer {
	
    /** 静态变量，用来记录当前在线连接数。应该把它设计成线程安全的。 */
    private static AtomicInteger onlineNum = new AtomicInteger();

    /** concurrent包的线程安全Set，用来存放每个客户端对应的WebSocketServer对象。 */
    private static ConcurrentHashMap<String, Session> sessionPools = new ConcurrentHashMap<>();
    
	/** 队列-websocket通道名称 */	
	private static String DAM_WEBSOCKET_EXCHANGE_NAME;
	
	/** 队列-websocket是否启用消息队列 */
	private static boolean DAM_WEBSOKCET_MQ_ENABLE;
	
	@Value("${conf.dam.websocket.exchange:}")
	public void setDamWebSocketExchangeName(String damWebSocketExchangeName) {
		DAM_WEBSOCKET_EXCHANGE_NAME = damWebSocketExchangeName;
	}
	
	public static String getDamWebSocketExchangeName() {
		return DAM_WEBSOCKET_EXCHANGE_NAME;
	}
	
	@Value("${conf.dam.websocket.enable-queue:false}")
	public void setDamWebSocketMQEnable(boolean damWebSocketMQEnable) {
		DAM_WEBSOKCET_MQ_ENABLE = damWebSocketMQEnable;
	}
	
	public static boolean getDamWebSocketMQEnable() {
		return DAM_WEBSOKCET_MQ_ENABLE;
	}

	/**
     * 发送消息
     * @param session Session 会话
     * @param message String 消息内容
     * @throws IOException
     */
    private static void sendMessage(Session session, String message) throws IOException {
        if(session != null && session.isOpen()){
            synchronized (session) {
                session.getBasicRemote().sendText(message);
            }
        }
    }
    
    /**
     * 发送消息(rabbitmq|单机)
     * @param userName String 用户名
     * @param message String 消息内容
     * @throws IOException
     */
    public static void sendInfo(String userName, String message) {
    	if(getDamWebSocketMQEnable()) {
	    	RabbitTemplate rabbitTemplate = (RabbitTemplate)ServiceLocator.getBean("rabbitTemplate");
	    	WebSocketMsg msg = new WebSocketMsg(userName, message);
	    	rabbitTemplate.convertAndSend(getDamWebSocketExchangeName(), null, JSON.toJSONString(msg));
	    	log.info("【websocket】send webSocket...queue:{} sendData:{}", getDamWebSocketExchangeName(), JSON.toJSONString(msg));
    	}
    	else {
    		sendInfoLocal(userName, message);
    	}
    }
    
    /**
     * 发送信息(本地)
     * @param userName String 接收用户
     * @param message String 消息内容
     * <AUTHOR>
     * @date 2024年4月12日
     */
    public static void sendInfoLocal(String userName, String message){    	
    	log.info("【websocket】send webSocket...username:{} sendData:{}", userName, message);
    	
    	//不传username 发给所有人
    	if(StringUtils.isEmpty(userName)) {    		
    		for(String key : sessionPools.keySet()) {
    			Session session = sessionPools.get(key);
    			try {
    				log.info("【websocket】send webSocket...username:{} ", key);
    	            sendMessage(session, message);
    	        }catch (Exception e){
    	        	log.error("【websocket】推送消息异常：{}", e);
    	        }
        	}
    	}
    	
    	//发给指定用户
    	else {
    		if(sessionPools.containsKey(userName)) {
        		try {
        			Session session = sessionPools.get(userName);
                    sendMessage(session, message);
                }
        		catch (Exception e){
        			log.error("【websocket】推送消息异常：{}", e);
                }
    		}
    	}        
    }

    /**
     * 连接建立时
     * @param session Session 会话
     * @param userName String 用户名
     */
    @OnOpen
    public void onOpen(Session session, @PathParam(value = "sid") String userName){
        sessionPools.put(userName, session);
        addOnlineCount();
        log.info("【websocket】" + userName + "加入webSocket！当前人数为" + onlineNum);
    }

    /**
     * 连接关闭时
     * @param userName String 用户名
     */
    @OnClose
    public void onClose(@PathParam(value = "sid") String userName){
        sessionPools.remove(userName);
        subOnlineCount();
        log.info("【websocket】" + userName + "断开webSocket连接！当前人数为" + onlineNum);
    }

    /**
     * 检查用户是否存在会话中
     * @param sid String 用户Id
     * @return boolean
     */
    public static boolean checkUser(String sid){
        Session session = sessionPools.get(sid);
        return session != null;
    }

    /**
     * 收到客户端信息
     * @param message String 消息
     * @throws IOException
     */
    @OnMessage
    public void onMessage(String message) throws IOException{
        message = "客户端：" + message + ",已收到";
        log.info("【websocket】" + message);
    }

    /**
     * 异常时调用
     * @param session Session 会话
     * @param throwable Throwable 异常
     */
    @OnError
    public void onError(Session session, Throwable throwable){
    	log.error("【websocket】发生异常：{}", (Exception) throwable);
    }

    /**
     * 增加在线人数
     */
    public static void addOnlineCount(){
        onlineNum.incrementAndGet();
    }

    /**
     * 减少在线人数
     */
    public static void subOnlineCount() {
        onlineNum.decrementAndGet();
    }
}