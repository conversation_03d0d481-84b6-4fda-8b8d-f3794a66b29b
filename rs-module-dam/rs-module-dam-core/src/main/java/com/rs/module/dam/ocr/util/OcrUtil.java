package com.rs.module.dam.ocr.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.text.similarity.JaroWinklerSimilarity;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.rs.module.dam.constant.CheckTypeEnum;
import com.rs.module.dam.constant.DamConstants;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.entity.ocr.CatalogMarkDO;
import com.rs.module.dam.entity.ocr.FormatCheckJobDO;
import com.rs.module.dam.entity.ocr.FormatCheckResultDO;
import com.rs.module.dam.entity.ocr.FormatMinceJobDO;
import com.rs.module.dam.entity.prisoner.PrisonerCatalogDO;
import com.rs.module.dam.service.ocr.CatalogMarkService;
import com.rs.module.dam.vo.ocr.CheckFormatParam;

/**
 * OCR处理工具类
 * <AUTHOR>
 * @date 2025年4月26日
 */
@Configuration
public class OcrUtil {
	
	/** ocr使用类型(1:集团ocr, 2:享云ocr，默认使用集团ocr) */
	private static String ocrType;
	
	public static String getOcrType() {
		return ocrType;
	}

	@Value("${ocr.type:1}")
	public void setOcrType(String ocrType) {
		OcrUtil.ocrType = ocrType;
	}

	/**
	 * 获取案件所有编目子目录
	 * @param bmCatalogData String 案件编目目录字符串
	 * @return List<JSONObject>
	 * <AUTHOR>
	 * @date 2024年4月2日
	 */
	public static List<JSONObject> getAllChildCatalog(String bmCatalogData){
		List<JSONObject> childCatalogList = new ArrayList<>();
		JSONArray catalog = JSONArray.parseArray(bmCatalogData);
		for(int i = 0; i < catalog.size(); i ++) {
			JSONObject catalogObj = catalog.getJSONObject(i);
			if(!catalogObj.getBooleanValue("isParent")) {
				childCatalogList.add(catalogObj);
			}
		}
		
		return childCatalogList;
	}
	
	/**
	 * 从归档任务中获取文件信息Json对象
	 * @param jobFileArray JSONArray 任务文件信息数组
	 * @param fileId String 文件Id
	 * @return JSONObject
	 * <AUTHOR>
	 * @date 2024年5月9日
	 */
	public static JSONObject popJobFileJson(JSONArray jobFileArray, String fileId) {
		JSONObject file = null;
		for (int j = 0; j < jobFileArray.size(); j++) {
            JSONObject materFile = jobFileArray.getJSONObject(j);
            if (materFile.get("id").equals(fileId)){
            	file = materFile;
            	jobFileArray.remove(materFile);
            	break;
            }
        }
		return file;
	}
	
	/**
	 * 从材料文件中获取单文件信息JSONArray对象
	 * @param metaFiles String 材料文件信息
	 * @param fileId String 文件Id
	 * @return JSONArray
	 * <AUTHOR>
	 * @date 2024年7月23日
	 */
	public static JSONArray buildSingleFileJsonArray(String metaFiles, String fileId) {
		if(StringUtil.isNotEmpty(metaFiles)) {
			JSONArray fileArray = JSONArray.parseArray(metaFiles);
			if(!fileArray.isEmpty()) {
				JSONObject fileJson = null;
				for (int j = 0; j < fileArray.size(); j++) {
		            JSONObject materFile = fileArray.getJSONObject(j);
		            if (materFile.get("id").equals(fileId)){
		            	fileJson = materFile;
		            	break;
		            }
		        }
				
				if(fileJson != null) {
					return new JSONArray().fluentAdd(fileJson);
				}
			}
		}
		
		return new JSONArray();
	}
	
	/**
	 * 从案件标准卷宗目录中匹配文件所属目录Id
	 * @param ajCatalogList List<JSONObject> 案件卷宗目录
	 * @param isContain boolean 是否包含关系(true:包含|false:等于)
	 * @param fileName String 材料文件名称
	 * @return String
	 * <AUTHOR>
	 * @date 2025年2月18日
	 */
	public static String getCatalogIdFromAjCatalog(List<JSONObject> ajCatalogList, boolean isContain, String fileName) {
		String catalogId = null;
		
		for(int i = 0; i < ajCatalogList.size(); i ++) {
			JSONObject catalogObj = ajCatalogList.get(i);
			Object documentObj = catalogObj.get("document");
			
			//案件标准卷宗目录中包含的文书名称数组
			String[] documentTag = null;
			if(documentObj instanceof JSONArray) {
				JSONArray documentArray = (JSONArray)documentObj;
				documentTag = documentArray.toArray(new String[documentArray.size()]);
			}
			else {
				documentTag = catalogObj.getString("document").split("\\|");
			}
			
			//匹配目录
			for(int j = 0 ; j < documentTag.length ; j ++ ) {
				String documentName = documentTag[j];
				if((!isContain && documentName.equalsIgnoreCase(fileName))
						|| (isContain && documentName.contains(fileName))) {
					catalogId = catalogObj.getString("id");
                	break;
				}
            }
		}
		
		return catalogId;
	}

	/**
	 * 根据名称匹配目录返回目录Id
	 * @param childCatalogList List<JSONObject> 案件所有子目录清单
	 * @param fileName String 文件名称
	 * @return String
	 * <AUTHOR>
	 * @date 2024年4月2日
	 */
	public static String getCatalogByName(List<JSONObject> childCatalogList, String fileName,
			Double familiarity, String jgrybm, String materialId,
			CatalogMarkService dossierCatalogOcrMarkService) {
		
		//使用等于关系匹配目录
		String catalogId = getCatalogIdFromAjCatalog(childCatalogList, false, fileName);
		
		//使用包含关系匹配目录
		if(StringUtil.isEmpty(catalogId)) {
			catalogId = getCatalogIdFromAjCatalog(childCatalogList, true, fileName);
		}
		
		//根据相似度匹配目录
		if(StringUtil.isEmpty(catalogId)) {
			for(int i = 0; i < childCatalogList.size(); i ++) {
				JSONObject catalogObj = childCatalogList.get(i);
				String[] documentTag = catalogObj.getString("document").split("\\|");
				
				//根据
				for(int j = 0 ; j < documentTag.length ; j ++ ) {
					JaroWinklerSimilarity js = new JaroWinklerSimilarity();
                    Double apply = js.apply(documentTag[j], fileName);
                    if(Double.compare(apply, familiarity) > -1 ){
                        String famil = String.format("%.2f%%", apply * 100);
                        CatalogMarkDO dossierCatalogOcrMark = new CatalogMarkDO();
                        dossierCatalogOcrMark.setId(StringUtil.getGuid32());
                        dossierCatalogOcrMark.setCatalogId(catalogObj.getString("id"));
                        dossierCatalogOcrMark.setCatalogName(catalogObj.getString("name"));
                        dossierCatalogOcrMark.setName(fileName);
                        dossierCatalogOcrMark.setFamiliarity(famil);
                        dossierCatalogOcrMark.setAddTime(new Date());
                        dossierCatalogOcrMark.setJgrybm(jgrybm);
                        dossierCatalogOcrMark.setMaterialId(materialId);
                        dossierCatalogOcrMarkService.save(dossierCatalogOcrMark);
                        catalogId = catalogObj.getString("id");
                        break;
                    }
	            }
			}
		}
		
		return catalogId;
	}
	
	/**
	 * 获取材料列表中的文件并返回数组(以fileId和fileUrl格式)
	 * @param dossierMaterialInfoList List<MaterialInfoDO> 待检测的材料列表
	 * @param checkType String 检测类型
	 * @return JSONArray
	 * <AUTHOR>
	 * @date 2024年5月10日
	 */
	public static JSONArray[] getMaterialFileJsonArrays(List<MaterialInfoDO> dossierMaterialInfoList, String checkType) {
		JSONArray fileData = new JSONArray();
        JSONArray noOcrfileData = new JSONArray();
        
        for (MaterialInfoDO dossierMaterialInfo : dossierMaterialInfoList) {
            String materialFile = dossierMaterialInfo.getMaterialFile();
            JSONArray jsonArray = JSONArray.parseArray(materialFile);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject fileJson = jsonArray.getJSONObject(i);
                JSONObject file = new JSONObject();
                file.put("fileId", fileJson.getString("id"));
                file.put("fileUrl", fileJson.getString("url"));
                fileData.add(file);
                
                if(DamConstants.FORMAT_CHECK_UN_BM.equals(checkType) && "未识别".equals(dossierMaterialInfo.getName())) {
                	noOcrfileData.add(file);
                }
            }
        }
        
        return new JSONArray[] {fileData, noOcrfileData};
	}
	
	/**
	 * 根据材料信息构建目录Json
	 * @param id String 节点Id
	 * @param name String 节点名称
	 * @param orderId int 排序Id
	 * @param isParent boolean 是否父级节点
	 * @param level int 当前层级
	 * @param parentId String 父Id
	 * @return JSONObject
	 * <AUTHOR>
	 * @date 2024年5月18日
	 */
	public static JSONObject buildCatalogJson(String id, String name, int orderId, 
			boolean isParent, int level, String parentId) {
		JSONObject json = new JSONObject();
		
		json.put("id", id);
		json.put("name", name);
		json.put("orderId", orderId);
		json.put("isParent", isParent);
		json.put("level", level);		
		json.put("parentId", parentId);
		json.put("isShow", true);
		
		return json;	
	}
	
	/**
	 * 根据目录Id查找目录名称
	 * @param bmCatalogData String 案件编目数据
	 * @param catalogId String 目录Id
	 * @return String
	 * <AUTHOR>
	 * @date 2024年5月30日
	 */
    public static String getCatalogNameById(String bmCatalogData, String catalogId) {
        if (StringUtil.isEmpty(bmCatalogData) || StringUtil.isEmpty(catalogId)){
            return null;
        }

        JSONArray catalog = JSONArray.parseArray(bmCatalogData);
        for( int i = 0 ; i < catalog.size() ; i ++ ) {
            JSONObject catalogObj = catalog.getJSONObject(i);
            if(!catalogObj.getBooleanValue("isParent")) {
                if (catalogObj.getString("id").equals(catalogId)){
                    return catalogObj.getString("name");
                }
            }
        }
        
        return null;
    }
    
    /**
	 * 根据目录Id查找目录名称(快速组卷)
	 * @param kszjCatalogData String 案件快速组卷编目数据
	 * @param catalogId String 目录Id
	 * @return String
	 * <AUTHOR>
	 * @date 2024年5月30日
	 */
    public static String getKszjCatalogNameById(String kszjCatalogData, String catalogId){
        if (StringUtil.isEmpty(kszjCatalogData) || StringUtil.isEmpty(catalogId)){
            return null;
        }

        JSONArray catalog = JSONArray.parseArray(kszjCatalogData);
        for( int i = 0 ; i < catalog.size() ; i ++ ) {
            JSONObject catalogObj = catalog.getJSONObject(i);
            if (catalogObj.getString("id").equals(catalogId)){
                return catalogObj.getString("name");
            }
        }
        
        return null;
    }
	
	/**
	 * 根据材料信息构建目录Json
	 * @param materialInfo MaterialInfoDO 材料信息
	 * @param isParent boolean 是否父级节点
	 * @param level int 当前层级
	 * @param parentId String 父Id
	 * @return JSONObject
	 * <AUTHOR>
	 * @date 2024年5月18日
	 */
	public static JSONObject buildCatalogJsonByMaterialInfo(MaterialInfoDO materialInfo, boolean isParent,
			int level, String parentId) {
		return buildCatalogJson(materialInfo.getId(), materialInfo.getName(), materialInfo.getXh(), isParent, level, parentId);
	}
	
	/**
	 * 构建格式化审查子任务
	 * @param formatParam CheckFormatParam 格式化审查参数
	 * @param checkFiles JSONArray 待审查的材料信息
	 * @return List<FormatMinceJobDO>
	 * <AUTHOR>
	 * @date 2024年5月29日
	 */
	public static List<FormatMinceJobDO> buildMinceJob(CheckFormatParam formatParam, JSONArray checkFiles){
		List<FormatMinceJobDO> minceJobList = new ArrayList<>();
		
		//签章检测
		if(DamConstants.FORMAT_CHECK_MINCE_OPEN.equals(formatParam.getSeal())) {
			FormatMinceJobDO minceJob = buildMinceJobSingle(formatParam, CheckTypeEnum.SEAL.getCode(), checkFiles);
			minceJobList.add(minceJob);
		}
		
		//签名检测
		if(DamConstants.FORMAT_CHECK_MINCE_OPEN.equals(formatParam.getSignature())) {
			FormatMinceJobDO minceJob = buildMinceJobSingle(formatParam, CheckTypeEnum.SIGNATURE.getCode(), checkFiles);
			minceJobList.add(minceJob);
		}
		
		//捺印检测
		if(DamConstants.FORMAT_CHECK_MINCE_OPEN.equals(formatParam.getFingerprint())) {
			FormatMinceJobDO minceJob = buildMinceJobSingle(formatParam, CheckTypeEnum.FINGERPRINT.getCode(), checkFiles);
			minceJobList.add(minceJob);
		}
		
		//文书填写规范性检测
		if(DamConstants.FORMAT_CHECK_MINCE_OPEN.equals(formatParam.getInfoExtract())) {
			FormatMinceJobDO minceJob = buildMinceJobSingle(formatParam, CheckTypeEnum.INFO_EXTRACT.getCode(), checkFiles);
			minceJobList.add(minceJob);
		}
		
		//空白页检测
		if(DamConstants.FORMAT_CHECK_MINCE_OPEN.equals(formatParam.getBlankSpace())) {
			FormatMinceJobDO minceJob = buildMinceJobSingle(formatParam, CheckTypeEnum.BLANK_SPACE.getCode(), checkFiles);
			minceJobList.add(minceJob);
		}
		
		//重复页检测
		if(DamConstants.FORMAT_CHECK_MINCE_OPEN.equals(formatParam.getRepeatPage())) {
			FormatMinceJobDO minceJob = buildMinceJobSingle(formatParam, CheckTypeEnum.REPEAT_PAGE.getCode(), checkFiles);
			minceJobList.add(minceJob);
		}
		
		//瑕疵页检测
		if(DamConstants.FORMAT_CHECK_MINCE_OPEN.equals(formatParam.getFlaw())) {
			FormatMinceJobDO minceJob = buildMinceJobSingle(formatParam, CheckTypeEnum.FLAW.getCode(), checkFiles);
			minceJobList.add(minceJob);
		}
		
		return minceJobList;		
	}
	
	/**
	 * 构建单个格式化审查子任务
	 * @param formatParam CheckFormatParam 格式化审查参数
	 * @param checkType String 检测类型（1:签章检测,2:签名检测,3:捺印检测,4:空白页检测,5:重复页检测,6:瑕疵页检测,7:文书规范检测）
	 * @param checkFiles JSONArray 待审查的材料信息
	 * @return FormatMinceJobDO
	 * <AUTHOR>
	 * @date 2024年5月29日
	 */
	public static FormatMinceJobDO buildMinceJobSingle(CheckFormatParam formatParam,
			String checkType, JSONArray checkFiles) {
		FormatMinceJobDO minceJob = new FormatMinceJobDO();
		
		minceJob.setId(StringUtil.getGuid32());
		minceJob.setJobId(formatParam.getJobId());
		minceJob.setJgrybm(formatParam.getJgrybm());
		minceJob.setCheckType(checkType);
		minceJob.setAddTime(new Date());
		minceJob.setCheckFiles(JSONObject.toJSONString(checkFiles));
		minceJob.setBaseFiles(formatParam.getFileData());
		minceJob.setMinceType(DamConstants.CHECK_FORMAT_MINCE_TYPE_ADD);
		minceJob.setJobType(formatParam.getCheckType());
		minceJob.setShowJob(DamConstants.CHECK_FORMAT_RESULT_SHOW);
		minceJob.setRequestLimit(200 + checkFiles.size());
		
		//任务状态
		minceJob.setJobStatus(DamConstants.JOB_STATE_NO);
		
		return minceJob;
	}
	
	/**
	 * 构建格式化检测结果对象
	 * @param checkJob FormatCheckJobDO 检测任务
	 * @param ajCatalog PrisonerCatalogDO 案件编目
	 * @param dossierMaterialInfo MaterialInfoDO 材料信息
	 * @param material JSONObject 文件JSON对象
	 * @param checkType String 检测类型
	 * @param addTime Date 添加时间
	 * @return FormatCheckResultDO
	 * <AUTHOR>
	 * @date 2024年6月3日
	 */
	public static FormatCheckResultDO buildFormatCheckResult(FormatCheckJobDO checkJob, PrisonerCatalogDO ajCatalog,
			MaterialInfoDO dossierMaterialInfo, JSONObject material, String checkType, Date addTime) {
		FormatCheckResultDO checkResult = new FormatCheckResultDO();
		
		checkResult.setId(StringUtil.getGuid32());
		checkResult.setJobId(checkJob.getId());
		checkResult.setJgrybm(checkJob.getJgrybm());
		checkResult.setFileId(material.getString("id"));
		checkResult.setCheckType(checkType);
		checkResult.setXh(material.getInteger("xh"));
		checkResult.setCatalogId(dossierMaterialInfo.getCatalogId());
		checkResult.setAddTime(addTime);
		checkResult.setPartCatalogId(dossierMaterialInfo.getPartCatalogId());
		checkResult.setMaterialId(dossierMaterialInfo.getId());
		checkResult.setFileUrl(material.getString("url"));
		checkResult.setJobType(checkJob.getJobType());
		
		//设置分类名称(快速组卷和编目组卷)
		if(DamConstants.FILE_TYPE_JZDR.equals(dossierMaterialInfo.getType())) {
			checkResult.setCatalogName(OcrUtil.getKszjCatalogNameById(ajCatalog.getKszjCatalogData(), dossierMaterialInfo.getCatalogId()));
		}
		else {
			checkResult.setCatalogName(OcrUtil.getCatalogNameById(ajCatalog.getBmCatalogData(), dossierMaterialInfo.getCatalogId()));
		}
		
		return checkResult;
	}
	
	/**
	 * 更新格式化审查子任务状态
	 * @param minceJob FormatMinceJobDO 格式化审查子任务
	 * @param jobStatus String 任务状态
	 * <AUTHOR>
	 * @date 2024年6月18日
	 */
	public static void updateMinceJobStatus(FormatMinceJobDO minceJob, String jobStatus) {
		if(minceJob != null) {
			minceJob.setJobStatus(jobStatus);
			minceJob.setEndTime(new Date());
		}
	}
	
	/**
	 * 更新格式化审查任务状态
	 * @param checkJob FormatCheckJobDO 格式化审查任务
	 * @param minceJobList List<FormatMinceJobDO> 格式化审查子任务
	 * @param jobStatus String 任务状态
	 * <AUTHOR>
	 * @date 2024年6月6日
	 */
	public static void updateFormatCheckJobStatus(FormatCheckJobDO checkJob, List<FormatMinceJobDO> minceJobList,
			String jobStatus) {
		if(checkJob != null) {
			checkJob.setStatus(jobStatus);
			checkJob.setEndTime(new Date());
		}
		if(CollectionUtil.isNotNull(minceJobList)) {
			minceJobList.forEach(minceJob -> {
				minceJob.setJobStatus(jobStatus);
				minceJob.setEndTime(new Date());
			});
		}
	}
	
	/**
	 * 更新格式化审查任务状态
	 * @param checkJob FormatCheckJobDO 格式化审查任务
	 * @param minceJob FormatMinceJobDO 格式化审查子任务
	 * @param jobStatus String 任务状态
	 * <AUTHOR>
	 * @date 2024年6月17日
	 */
	public static void updateFormatCheckJobStatus(FormatCheckJobDO checkJob, FormatMinceJobDO minceJob,
			String jobStatus) {
		updateFormatCheckJobStatus(checkJob, Arrays.asList(minceJob), jobStatus);
	}
	
	/**
	 * 替换Python的布尔类型值
	 * @param str String Python返回的字符串
	 * @return String
	 * <AUTHOR>
	 * @date 2024年6月7日
	 */
	public static String replacePythonBoolean(String str) {
		if(StringUtil.isNotEmpty(str)) {
			str = str.replace("True", "true");
			str = str.replace("False", "false");
		}
		
		return str;
	}
	
	/**
	 * 获取格式化审查指定检测类型的子任务
	 * @param minceJobList List<FormatMinceJobDO> 格式化审查子任务
	 * @param checkType String 检测类型
	 * @return FormatMinceJobDO
	 * <AUTHOR>
	 * @date 2024年6月12日
	 */
	public static FormatMinceJobDO getMinceJobByCheckType(List<FormatMinceJobDO> minceJobList, String checkType) {
		return minceJobList.stream().filter(mce -> mce.getCheckType().equals(checkType)).findFirst().orElse(null);
	}
	
	/**
	 * 获取格式化审查指定检测类型并且已完成的子任务
	 * @param minceJobList List<FormatMinceJobDO> 格式化审查子任务
	 * @param checkType String 检测类型
	 * @return FormatMinceJobDO
	 * <AUTHOR>
	 * @date 2024年6月12日
	 */
	public static FormatMinceJobDO getMinceJobFinishedByCheckType(List<FormatMinceJobDO> minceJobList, String checkType) {
		return minceJobList.stream().filter(mce -> 
			mce.getCheckType().equals(checkType) && DamConstants.JOB_STATE_YES.equals(mce.getJobStatus())
		).findFirst().orElse(null);
	}
	
	/**
	 * 比较JSON格式材料文件数组返回不同的部分
	 * @param sourceFileData 源文件数组
	 * @param targetFileData 目标文件数组
	 * @return JSONArray
	 * <AUTHOR>
	 * @date 2024年6月13日
	 */
	public static JSONArray compareFileData(JSONArray sourceFileData, JSONArray targetFileData) {
		for(int i = 0; i < sourceFileData.size(); i ++) {
			JSONObject lastFile = sourceFileData.getJSONObject(i);
			String lastFileId = lastFile.getString("fileId");
			for(int m = 0; m < targetFileData.size(); m ++) {
				JSONObject file = targetFileData.getJSONObject(m);
				String fileId = file.getString("fileId");
				if(lastFileId.equals(fileId)) {
					targetFileData.remove(m);
					break;
				}
			}
		}
		
		return targetFileData;
	}
	
	/**
	 * 从结果集中获取Url
	 * @param R JSONObject 结果集
	 * @return String
	 * <AUTHOR>
	 * @date 2024年6月17日
	 */
	public static String getUrlFromR(JSONObject R) {
		boolean success = R.getBooleanValue("success");
        if (success){
            JSONObject data = R.getJSONObject("data");
            if (null != data){
                String url = data.getString("url");
                return url;
            }
        }
        return null;
	}
	
	/**
	 * 清除文件质量标记
	 * @param file JSONObject JSON格式文件
	 * <AUTHOR>
	 * @date 2024年6月17日
	 */
	public static void clearFileQuality(JSONObject file) {
		file.remove(DamConstants.MaterialColumn.FLAW);
        file.remove(DamConstants.MaterialColumn.ANGLE);
        file.remove(DamConstants.MaterialColumn.ASKEW);
        file.remove(DamConstants.MaterialColumn.SPOT);
	}
	
	/**
	 * 更新材料信息中文件url地址
	 * @param materialInfo MaterialInfoDO 材料信息
	 * @param fileId String 文件Id
	 * @param fileUrl String 文件地址
	 * <AUTHOR>
	 * @date 2024年6月17日
	 */
	public static void updateMaterialInfoFileUrl(MaterialInfoDO materialInfo, String fileId, String fileUrl) {
		if(materialInfo != null) {
			JSONArray materialFiles = JSONArray.parseArray(materialInfo.getMaterialFile());
			if(materialFiles != null) {
				for(int i = 0; i < materialFiles.size(); i ++) {
					JSONObject matarialFile = materialFiles.getJSONObject(i);
					String id = matarialFile.getString("id");
					if(id.equals(fileId)) {
						matarialFile.put("url", fileUrl);
						materialInfo.setMaterialFile(materialFiles.toJSONString());
						break;
					}
				}
			}
		}
	}
}
