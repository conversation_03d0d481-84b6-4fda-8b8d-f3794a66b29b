package com.rs.module.dam.vo.ocr;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - ocr格式化审查任务细分新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FormatMinceJobSaveReqVO extends BaseVO{
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("关联dms_format_check_job的id")
    private String jobId;

    @ApiModelProperty("检测类型(1:签章检测,2:签名检测,3:捺印检测,4:空白页检测,5:重复页检测,6:瑕疵页检测,7:文书规范检测)")
    private String checkType;

    @ApiModelProperty("任务类型(0:未编目任务, 1:已编目任务, 2:智能阅卷任务)")
    private String jobType;

    @ApiModelProperty("任务状态(0:进行中, 1:成功, 2:失败)")
    @NotEmpty(message = "任务状态(0:进行中, 1:成功, 2:失败)不能为空")
    private String jobStatus;

    @ApiModelProperty("任务是否显示(1:是, 0:否)")
    private String showJob;

    @ApiModelProperty("任务开始时间")
    private Date startTime;

    @ApiModelProperty("任务结束时间")
    private Date endTime;

    @ApiModelProperty("要检测的材料")
    private String checkFiles;

    @ApiModelProperty("全部的材料")
    private String baseFiles;

}
