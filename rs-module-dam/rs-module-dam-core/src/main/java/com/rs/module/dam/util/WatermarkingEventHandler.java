package com.rs.module.dam.util;

import java.text.SimpleDateFormat;
import java.util.Date;

import com.bsp.security.model.SessionUser;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.kernel.colors.WebColors;
import com.itextpdf.kernel.events.Event;
import com.itextpdf.kernel.events.IEventHandler;
import com.itextpdf.kernel.events.PdfDocumentEvent;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.extgstate.PdfExtGState;
import com.itextpdf.layout.Canvas;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.property.TextAlignment;
import com.itextpdf.layout.property.VerticalAlignment;

import lombok.extern.slf4j.Slf4j;

/**
 * pdf水印事件处理器
 * <AUTHOR>
 * @date 2025年4月22日
 */
@Slf4j
public class WatermarkingEventHandler implements IEventHandler {

	private SessionUser user;
	
	public  WatermarkingEventHandler(SessionUser user){
		this.user = user;
	}
	@SuppressWarnings("resource")
	@Override
	public void handleEvent(Event event) {
		PdfDocumentEvent docEvent = (PdfDocumentEvent) event;
		PdfDocument pdfDoc = docEvent.getDocument();
		PdfPage page = docEvent.getPage();
		PdfFont font = null;
		
		try {
			PdfFontFactory.register(PdfGenerateUtil.getRealPath("font", "simfang.ttf"), "simfang");
			font = PdfFontFactory.createRegisteredFont("simfang", PdfEncodings.IDENTITY_H, true);
			PdfCanvas pdfCanvas = new PdfCanvas(page);
			PdfExtGState gs1 = new PdfExtGState();
			
			// 水印透明度
			gs1.setFillOpacity(0.5f);
			pdfCanvas.setExtGState(gs1);
			
			//水印内容
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH-mm-ss");
			String watermarking = user.getName() + "\r\t" + sdf.format(new Date());
			
			//添加水印
			if(font != null ){
                new Canvas(pdfCanvas, pdfDoc, page.getPageSize()).setFontColor(WebColors.getRGBColor("lightgray"))
                	.setFontSize(50)
                    .setFont(font)
                    .showTextAligned(new Paragraph(watermarking), 150, 200, pdfDoc.getPageNumber(page),
                    		TextAlignment.CENTER, VerticalAlignment.TOP, 45)
                    .showTextAligned(new Paragraph(watermarking), 400, 200, pdfDoc.getPageNumber(page),
                            TextAlignment.CENTER, VerticalAlignment.TOP, 45)
                    .showTextAligned(new Paragraph(watermarking), 150, 600, pdfDoc.getPageNumber(page),
                            TextAlignment.CENTER, VerticalAlignment.TOP, 45)
                    .showTextAligned(new Paragraph(watermarking), 400, 600, pdfDoc.getPageNumber(page),
                            TextAlignment.CENTER, VerticalAlignment.TOP, 45);
            }
		}
		catch (Exception e) {
			log.error("发生异常，原因：", e);
		}
	}
}
