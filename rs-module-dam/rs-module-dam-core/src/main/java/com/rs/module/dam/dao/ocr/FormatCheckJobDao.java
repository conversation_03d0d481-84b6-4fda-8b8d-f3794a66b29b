package com.rs.module.dam.dao.ocr;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.ocr.FormatCheckJobDO;
import com.rs.module.dam.vo.ocr.FormatCheckJobListReqVO;
import com.rs.module.dam.vo.ocr.FormatCheckJobPageReqVO;

/**
* ocr格式化审查任务 Dao
*
* <AUTHOR>
*/
@Mapper
public interface FormatCheckJobDao extends IBaseDao<FormatCheckJobDO> {


    default PageResult<FormatCheckJobDO> selectPage(FormatCheckJobPageReqVO reqVO) {
        Page<FormatCheckJobDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<FormatCheckJobDO> wrapper = new LambdaQueryWrapperX<FormatCheckJobDO>()
            .eqIfPresent(FormatCheckJobDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(FormatCheckJobDO::getStatus, reqVO.getStatus())
            .betweenIfPresent(FormatCheckJobDO::getStartTime, reqVO.getStartTime())
            .betweenIfPresent(FormatCheckJobDO::getEndTime, reqVO.getEndTime())
            .eqIfPresent(FormatCheckJobDO::getJobType, reqVO.getJobType())
            .eqIfPresent(FormatCheckJobDO::getLastJobId, reqVO.getLastJobId())
            .eqIfPresent(FormatCheckJobDO::getIsFirst, reqVO.getIsFirst())
            .eqIfPresent(FormatCheckJobDO::getAutoCatalog, reqVO.getAutoCatalog())
            .eqIfPresent(FormatCheckJobDO::getRequestLimit, reqVO.getRequestLimit())
            .eqIfPresent(FormatCheckJobDO::getRequestCount, reqVO.getRequestCount())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(FormatCheckJobDO::getAddTime);
        }
        Page<FormatCheckJobDO> formatCheckJobPage = selectPage(page, wrapper);
        return new PageResult<>(formatCheckJobPage.getRecords(), formatCheckJobPage.getTotal());
    }
    default List<FormatCheckJobDO> selectList(FormatCheckJobListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<FormatCheckJobDO>()
            .eqIfPresent(FormatCheckJobDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(FormatCheckJobDO::getStatus, reqVO.getStatus())
            .betweenIfPresent(FormatCheckJobDO::getStartTime, reqVO.getStartTime())
            .betweenIfPresent(FormatCheckJobDO::getEndTime, reqVO.getEndTime())
            .eqIfPresent(FormatCheckJobDO::getJobType, reqVO.getJobType())
            .eqIfPresent(FormatCheckJobDO::getLastJobId, reqVO.getLastJobId())
            .eqIfPresent(FormatCheckJobDO::getIsFirst, reqVO.getIsFirst())
            .eqIfPresent(FormatCheckJobDO::getAutoCatalog, reqVO.getAutoCatalog())
            .eqIfPresent(FormatCheckJobDO::getRequestLimit, reqVO.getRequestLimit())
            .eqIfPresent(FormatCheckJobDO::getRequestCount, reqVO.getRequestCount())
        .orderByDesc(FormatCheckJobDO::getAddTime));    }


    }
