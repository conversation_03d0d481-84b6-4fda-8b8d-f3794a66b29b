package com.rs.module.dam.util;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rs.module.dam.entity.material.MaterialInfoDO;

/**
 * 材料处理工具类
 * <AUTHOR>
 * @date 2025年4月22日
 */
public class MaterialUtil {
	
	/**
	 * 获取材料中的文件url属性集合
	 * @param materialList List<MaterialInfoDO> 材料集合
	 * @return List<String>
	 */
	public static List<String> getFileUrlList(List<MaterialInfoDO> materialList){
		return getFilePropList(materialList, "url");
	}
	
	/**
	 * 获取材料中的文件id属性集合
	 * @param materialList List<MaterialInfoDO> 材料集合
	 * @return List<String>
	 */
	public static List<String> getFileIdList(List<MaterialInfoDO> materialList){
		return getFilePropList(materialList, "id");
	}

	/**
	 * 获取材料中的文件属性集合
	 * @param materialList List<MaterialInfoDO> 材料集合
	 * @param propName String 属性名称
	 * @return List<String>
	 */
	public static List<String> getFilePropList(List<MaterialInfoDO> materialList, String propName){
		List<String> list = new ArrayList<>();
		for(MaterialInfoDO material : materialList) {
			String fileStr = material.getMaterialFile();
            JSONArray filesArr = JSONArray.parseArray(fileStr);
            if(!filesArr.isEmpty()) {
	            for (int a = 0; a < filesArr.size(); a++) {
	                JSONObject obj = filesArr.getJSONObject(a);
	                list.add(obj.getString(propName));
	            }
            }
		}
		
		return list;
	}
}
