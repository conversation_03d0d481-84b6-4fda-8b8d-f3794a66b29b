package com.rs.module.dam.entity.material;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO_;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 卷宗材料比对 DO
 *
 * <AUTHOR>
 */
@TableName("dam_material_compare")
@KeySequence("dam_material_compare_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialCompareDO extends BaseDO_ {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 左边材料文件图片ID
     */
    private String leftFileId;
    /**
     * 右边材料文件图片ID
     */
    private String rightFileId;
    /**
     * 左边材料ID
     */
    private String leftMaterialId;
    /**
     * 右边材料ID
     */
    private String rightMaterialId;
    /**
     * 比对图片url
     */
    private String url;
    /**
     * 备注
     */
    private String remark;

}
