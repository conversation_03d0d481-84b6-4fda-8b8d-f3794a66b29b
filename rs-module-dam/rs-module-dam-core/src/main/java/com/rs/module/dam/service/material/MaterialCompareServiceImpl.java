package com.rs.module.dam.service.material;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.dao.material.MaterialCompareDao;
import com.rs.module.dam.entity.material.MaterialCompareDO;
import com.rs.module.dam.vo.material.MaterialCompareListReqVO;
import com.rs.module.dam.vo.material.MaterialComparePageReqVO;
import com.rs.module.dam.vo.material.MaterialCompareSaveReqVO;


/**
 * 卷宗材料比对 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MaterialCompareServiceImpl extends BaseServiceImpl<MaterialCompareDao, MaterialCompareDO> implements MaterialCompareService {

    @Resource
    private MaterialCompareDao materialCompareDao;

    @Override
    public String createMaterialCompare(MaterialCompareSaveReqVO createReqVO) {
        // 插入
        MaterialCompareDO materialCompare = BeanUtils.toBean(createReqVO, MaterialCompareDO.class);
        materialCompareDao.insert(materialCompare);
        // 返回
        return materialCompare.getId();
    }

    @Override
    public void updateMaterialCompare(MaterialCompareSaveReqVO updateReqVO) {
        // 校验存在
        validateMaterialCompareExists(updateReqVO.getId());
        // 更新
        MaterialCompareDO updateObj = BeanUtils.toBean(updateReqVO, MaterialCompareDO.class);
        materialCompareDao.updateById(updateObj);
    }

    @Override
    public void deleteMaterialCompare(String id) {
        // 校验存在
        validateMaterialCompareExists(id);
        // 删除
        materialCompareDao.deleteById(id);
    }

    private void validateMaterialCompareExists(String id) {
        if (materialCompareDao.selectById(id) == null) {
            throw new ServerException("卷宗材料比对数据不存在");
        }
    }

    @Override
    public MaterialCompareDO getMaterialCompare(String id) {
        return materialCompareDao.selectById(id);
    }

    @Override
    public PageResult<MaterialCompareDO> getMaterialComparePage(MaterialComparePageReqVO pageReqVO) {
        return materialCompareDao.selectPage(pageReqVO);
    }

    @Override
    public List<MaterialCompareDO> getMaterialCompareList(MaterialCompareListReqVO listReqVO) {
        return materialCompareDao.selectList(listReqVO);
    }


}
