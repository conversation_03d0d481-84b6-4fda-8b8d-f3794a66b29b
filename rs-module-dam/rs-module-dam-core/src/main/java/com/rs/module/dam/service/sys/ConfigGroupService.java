package com.rs.module.dam.service.sys;

import java.util.List;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.sys.ConfigGroupDO;
import com.rs.module.dam.entity.sys.ConfigGroupOrgDO;
import com.rs.module.dam.vo.sys.ConfigGroupListReqVO;
import com.rs.module.dam.vo.sys.ConfigGroupPageReqVO;
import com.rs.module.dam.vo.sys.ConfigGroupSaveReqVO;

/**
 * 卷宗配置 Service 接口
 *
 * <AUTHOR>
 */
public interface ConfigGroupService extends IBaseService<ConfigGroupDO>{

    /**
     * 创建卷宗配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createConfigGroup(@Valid ConfigGroupSaveReqVO createReqVO);

    /**
     * 更新卷宗配置
     *
     * @param updateReqVO 更新信息
     */
    void updateConfigGroup(@Valid ConfigGroupSaveReqVO updateReqVO);

    /**
     * 删除卷宗配置
     *
     * @param id 编号
     */
    void deleteConfigGroup(String id);

    /**
     * 获得卷宗配置
     *
     * @param id 编号
     * @return 卷宗配置
     */
    ConfigGroupDO getConfigGroup(String id);

    /**
     * 获得卷宗配置
     *
     * @param orgCode 机构编号
     * @return 卷宗配置
     */
    ConfigGroupDO getByOrgCode(String orgCode);

    /**
    * 获得卷宗配置分页
    *
    * @param pageReqVO 分页查询
    * @return 卷宗配置分页
    */
    PageResult<ConfigGroupDO> getConfigGroupPage(ConfigGroupPageReqVO pageReqVO);

    /**
    * 获得卷宗配置列表
    *
    * @param listReqVO 查询条件
    * @return 卷宗配置列表
    */
    List<ConfigGroupDO> getConfigGroupList(ConfigGroupListReqVO listReqVO);
        
    /**
     * 获取用户可用的卷宗配置
     * @param orgCode String 机构代码
     * @return ConfigGroupDO
     */
    public ConfigGroupDO getUserConfigGroup(String orgCode);


    // ==================== 子表（卷宗配置机构关联） ====================

    /**
     * 获得卷宗配置机构关联列表
     *
     * @param groupId 配置组Id
     * @return 卷宗配置机构关联列表
     */
    List<ConfigGroupOrgDO> getConfigGroupOrgListByGroupId(String groupId);

}
