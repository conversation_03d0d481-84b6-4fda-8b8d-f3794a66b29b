package com.rs.module.dam.service.ocr;

import java.util.List;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.entity.ocr.FormatCheckJobDO;
import com.rs.module.dam.entity.ocr.FormatMinceJobDO;
import com.rs.module.dam.entity.prisoner.PrisonerCatalogDO;

/**
 * 格式化审查服务接口类
 * <AUTHOR>
 * @date 2025年4月26日
 */
public interface TFormatCheckService {

	/**
	 * 获取格式化审查发起检测的所有子任务集合
	 * @param jobId String 主任务Id
	 * @return List<FormatMinceJobDO>
	 * <AUTHOR>
	 */
	public List<FormatMinceJobDO> getCheckedMinceJobList(String jobId);
	
	/**
	 * 获取格式化审查指定检测类型的子任务集合
	 * @param jobId String 主任务Id
	 * @param checkTypes String... 检测类型动态参数
	 * @return List<FormatMinceJobDO>
	 * <AUTHOR>
	 */
	public List<FormatMinceJobDO> getMinceJobListByCheckTypes(String jobId, String... checkTypes);
	
	/**
	 * 获取格式化审查关联的材料
	 * @param jgrybm String 监管人员编码
	 * @param jobType String 任务类型(0:未编目任务, 1:已编目任务, 2:智能阅卷任务)
	 * @return List<MaterialInfoDO>
	 * <AUTHOR>
	 */
	public List<MaterialInfoDO> getFormatCheckMaterialInfoList(String jgrybm, String jobType);
	
	/**
	 * 获取格式化审查关联的材料
	 * @param jgrybm String 监管人员编码
	 * @param jobType String 任务类型(0:未编目任务, 1:已编目任务, 2:智能阅卷任务)
	 * @param sqlSelect String 返回的字段
	 * @return List<DossierMaterialInfo>
	 * <AUTHOR>
	 */
	public List<MaterialInfoDO> getFormatCheckMaterialInfoList(String jgrybm, String jobType, String sqlSelect);
	
	/**
	 * 获取监管人员编目信息
	 * @param jgrybm String 监管人员编码
	 * @return DossierAjCatalog
	 * <AUTHOR>
	 */
	public PrisonerCatalogDO getPrisonerCatalog(String jgrybm);
	
	/**
	 * 验证格式化审查材料是否合法
	 * @param checkJob DmsFormatCheckJob 格式化审查任务
	 * @param dossierMaterialInfoList List<DossierMaterialInfo> 格式化审查关联的材料
	 * @return boolean
	 * <AUTHOR>
	 */
	public boolean validateFormatCheckMaterials(FormatCheckJobDO checkJob,
			List<MaterialInfoDO> dossierMaterialInfoList);
	
	/**
	 * 获取格式化审查任务指定类型已经检测完成的文件Id集合
	 * @param jobId String 主任务Id
	 * @param jgrybm String 监管人员编码
	 * @param checkType String 检测类型
	 * @return public List<String>
	 * <AUTHOR>
	 */
	public List<String> getJobCheckResultFileIdList(String jobId, String jgrybm, String checkType);
	
	/**
	 * 获取监管人员指定类型检测已经完成的文件Id集合
	 * @param jgrybm String 监管人员编码
	 * @param checkType String 检测类型
	 * @return public List<String>
	 * <AUTHOR>
	 */
	public List<String> getPrisonerCheckResultFileIdList(String jgrybm, String jobType, String checkType);
	
	/**
	 * 图片瑕疵页矫正处理
	 * @param materialId String 材料Id
	 * @param fileId String 文件Id
	 * @return CommonResult
	 */
	public CommonResult<?> improveImageQuality(String materialId, String fileId);
}
