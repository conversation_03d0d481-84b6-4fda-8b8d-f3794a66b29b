package com.rs.module.dam.strategy;

/**
 * 图片操作类型枚举
 * <AUTHOR>
 * @date 2025年4月18日
 */
public enum OperateTypeEnum {
	
	//图片旋转
    IMAGE_ROTARY("01", "damRotaryImageOperate"),
    
    //图片粘贴
    IMAGE_CUT("02", "damCutImageOperate"),
    
    //图片删除
    IMAGE_DELETE("03", "damDeleteImageOperate"),
    
    //图片移动
    IMAGE_MOVE("04", "damMoveImageOperate"),
    
    //图片切割
    IMAGE_SPLIT("05", "damSplitImageOperate");

    OperateTypeEnum(String type, String beanName) {
        this.type = type;
        this.beanName = beanName;
    }

    private String type;
    private String beanName;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getBeanName() {
        return beanName;
    }

    public void setBeanName(String beanName) {
        this.beanName = beanName;
    }

    /**
     * 通过type获取beanName
     * @param type String 操作类型
     * @return String
     */
    public static String getBeanNameByType(String type){
        if(type == null){
            return "";
        }

        for(OperateTypeEnum typeEnum : OperateTypeEnum.values()){
            if(typeEnum.getType().equals(type)){
                return typeEnum.getBeanName();
            }
        }
        
        return "";
    }

    /**
     * 通过type获取实例
     * @param type
     * @return
     */
    public static OperateTypeEnum getTypeEnumBytypeId(String type){
        for(OperateTypeEnum operateTypeEnum : values()){
            if(operateTypeEnum.getType().equals(type)){
                return operateTypeEnum;
            }
        }
        return null;
    }
}
