package com.rs.module.dam.vo.ocr;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - ocr相似度归目标识 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CatalogMarkRespVO extends BaseVO implements TransPojo{
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("材料Id")
    private String materialId;
    @ApiModelProperty("目录Id")
    private String catalogId;
    @ApiModelProperty("目录名称")
    private String catalogName;
    @ApiModelProperty("分卷目录Id")
    private String partCatalogId;
    @ApiModelProperty("识别材料名称")
    private String name;
    @ApiModelProperty("相似度")
    private String familiarity;
}
