package com.rs.module.dam.util;

import org.apache.pdfbox.cos.COSObject;
import org.apache.pdfbox.pdmodel.DefaultResourceCache;
import org.apache.pdfbox.pdmodel.documentinterchange.markedcontent.PDPropertyList;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.graphics.PDXObject;
import org.apache.pdfbox.pdmodel.graphics.color.PDColorSpace;
import org.apache.pdfbox.pdmodel.graphics.pattern.PDAbstractPattern;
import org.apache.pdfbox.pdmodel.graphics.shading.PDShading;
import org.apache.pdfbox.pdmodel.graphics.state.PDExtendedGraphicsState;

import java.io.IOException;

/**
 * pdf资源缓存策略
 * <AUTHOR>
 * @date 2025年4月21日
 */
public class PdfCustomDefaultResourceCache extends DefaultResourceCache {

    @Override
    public void put(COSObject indirect, PDFont font) throws IOException {

    }

    @Override
    public void put(COSObject indirect, PDColorSpace colorSpace) throws IOException {

    }

    @Override
    public void put(COSObject indirect, PDExtendedGraphicsState extGState) {

    }

    @Override
    public void put(COSObject indirect, PDShading shading) throws IOException {

    }

    @Override
    public void put(COSObject indirect, PDAbstractPattern pattern) throws IOException {

    }

    @Override
    public void put(COSObject indirect, PDPropertyList propertyList) {

    }

    @Override
    public void put(COSObject indirect, PDXObject xobject) throws IOException {

    }
}
