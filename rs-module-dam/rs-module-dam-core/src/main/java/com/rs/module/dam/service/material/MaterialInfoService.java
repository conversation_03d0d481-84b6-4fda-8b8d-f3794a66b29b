package com.rs.module.dam.service.material;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import com.alibaba.fastjson.JSONArray;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.ocr.vo.OcrScpdfParams;
import com.rs.module.dam.vo.material.MaterialInfoListReqVO;
import com.rs.module.dam.vo.material.MaterialInfoPageReqVO;
import com.rs.module.dam.vo.material.MaterialInfoSaveReqVO;

/**
 * 卷宗材料 Service 接口
 *
 * <AUTHOR>
 */
public interface MaterialInfoService extends IBaseService<MaterialInfoDO>{

    /**
     * 创建卷宗材料
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMaterialInfo(@Valid MaterialInfoSaveReqVO createReqVO);

    /**
     * 更新卷宗材料
     *
     * @param updateReqVO 更新信息
     */
    void updateMaterialInfo(@Valid MaterialInfoSaveReqVO updateReqVO);

    /**
     * 删除卷宗材料
     *
     * @param id 编号
     */
    void deleteMaterialInfo(String id);

    /**
     * 获得卷宗材料
     *
     * @param id 编号
     * @return 卷宗材料
     */
    MaterialInfoDO getMaterialInfo(String id);

    /**
    * 获得卷宗材料分页
    *
    * @param pageReqVO 分页查询
    * @return 卷宗材料分页
    */
    PageResult<MaterialInfoDO> getMaterialInfoPage(MaterialInfoPageReqVO pageReqVO);

    /**
    * 获得卷宗材料列表
    *
    * @param listReqVO 查询条件
    * @return 卷宗材料列表
    */
    List<MaterialInfoDO> getMaterialInfoList(MaterialInfoListReqVO listReqVO);


    /**
     * 根据监管人员代码和材料类型获取分卷目录Id
     * @param jgrybm String 监管人员编码
     * @param type String 类型
     * @return List<String>
     */
    public List<String> selectPartCatalogIdByJgrybmAndType(String jgrybm, String type);
    
    /**
     * 获取材料上传的pdf数量
     * @param jgrybm String 监管人员编码
     * @param types List<String> types 类型集合
     * @return Integer
     */
    public Integer selectUploadPdfCountByType(String jgrybm, List<String> types);
    
    /**
     * 对材料进行操作
     * @param materialArray JSONArray 材料数据
     * @param request HttpServletRequest
     * @return boolean
     */
    public boolean materialOperate(JSONArray materialArray, HttpServletRequest request); 
    
    /**
     * 保存组卷材料(确认组卷)
     * @param data String 组卷数据
     * @param ocrV2ParamsList List<OcrV2ScpdfParams> 双层pdf参数列表
     * @return boolean
     */
    public boolean saveZjMaterial(String data, List<OcrScpdfParams> ocrV2ParamsList);
    
    /**
     * 获取卷宗材料集合
     * @param jgrybm String 监管人员编码
     * @param type String 编目类型
     * @param catalogId String 分类Id
     * @return List<MaterialInfoDO>
     */
    public List<MaterialInfoDO> getMaterialInfoList(String jgrybm, String type, String catalogId);
    
    /**
     * 设置材料序号(基于缓存)
     * @param materialInfo DossierMaterialInfo 材料
     */
    public void setMaterialInfoXh(MaterialInfoDO materialInfo);
}
