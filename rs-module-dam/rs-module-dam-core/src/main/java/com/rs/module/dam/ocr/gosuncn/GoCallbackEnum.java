package com.rs.module.dam.ocr.gosuncn;

import com.rs.module.dam.config.IspV2ConfigUtil;

/**
 * gosuncn-高新兴OCR回调接口枚举类
 * <AUTHOR>
 * @date 2024年3月22日
 */
public enum GoCallbackEnum {
	
	//智能编目回调接口
	ZNBM(IspV2ConfigUtil.getZnbmCode(), "com.rs.module.dam.ocr.gosuncn.callback.ZnbmCallback"),
	
	//空白页、重复页、瑕疵页检测回调接口
	KBCFXC(IspV2ConfigUtil.getKbcfxcCode(), "com.rs.module.dam.ocr.gosuncn.callback.KbcfxcCallback"),
	
	//瑕疵页矫正回调接口
	XCYJZ(IspV2ConfigUtil.getXcyjzCode(), "com.rs.module.dam.ocr.gosuncn.callback.XcyjzCallback"),
	
	//图片方向合规性检查回调接口
	TPFXJZ(IspV2ConfigUtil.getTpfxjzCode(), "com.rs.module.dam.ocr.gosuncn.callback.TpfxjzCallback"),
	
	//图片歪斜矫正回调接口
	TPWXJZ(IspV2ConfigUtil.getTpwxjzCode(), "com.rs.module.dam.ocr.gosuncn.callback.TpwxjzCallback"),
	
	//签名、签章、捺印审查回调接口
	QMQZNY(IspV2ConfigUtil.getQmqznyCode(), "com.rs.module.dam.ocr.gosuncn.callback.QmqznyCallback"),
	
	//双层PDF转换回调接口
	SCPDF(IspV2ConfigUtil.getScpdfCode(), "com.rs.module.dam.ocr.gosuncn.callback.ScpdfCallback"),
	
	//文书信息提取回调接口
	WSXXTQ(IspV2ConfigUtil.getWsxxtqCode(), "com.rs.module.dam.ocr.gosuncn.callback.WsxxtqCallback"),
	
	//二维码、条形码识别回调接口
	EWMTXM(IspV2ConfigUtil.getEwmtxmCode(), "com.rs.module.dam.ocr.gosuncn.callback.EwmtxmCallback"),
	
	//预编目回调接口
	YBM(IspV2ConfigUtil.getYbmCode(), "com.rs.module.dam.ocr.gosuncn.callback.YbmCallback");

	GoCallbackEnum(String code, String className) {
		this.code = code;
		this.className = className;
	}
	
	//策略代码
	public String code;
	
	//策略处理类名称
	public String className;

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getClassName() {
		return className;
	}

	public void setClassName(String className) {
		this.className = className;
	}
	
	/**
	 * 根据代码获取OCR回调Api枚举
	 * @param code String 服务代码
	 * @return OcrCallbackApiEnum
	 * <AUTHOR>
	 * @date 2024年3月22日
	 */
	public static GoCallbackEnum getOcrCallbackApiEnumByCode(String code) {
		for(GoCallbackEnum strategy : GoCallbackEnum.values()) {
			if(strategy.getCode().equals(code)) {
				return strategy;
			}
		}
		
		return null;
	}
}
