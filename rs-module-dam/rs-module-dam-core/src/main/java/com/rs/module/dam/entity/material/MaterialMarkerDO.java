package com.rs.module.dam.entity.material;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 卷宗材料标注 DO
 *
 * <AUTHOR>
 */
@TableName("dam_material_marker")
@KeySequence("dam_material_marker_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialMarkerDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 卷宗Id
     */
    private String jzId;
    /**
     * 材料Id
     */
    private String materialId;
    /**
     * 材料文件图片Id
     */
    private String fileId;
    /**
     * 标注信息
     */
    private String marker;
    /**
     * 编号
     */
    private String number;
    /**
     * 标注内容
     */
    private String content;

}
