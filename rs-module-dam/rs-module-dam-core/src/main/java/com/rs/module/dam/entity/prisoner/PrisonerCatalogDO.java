package com.rs.module.dam.entity.prisoner;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 监管人员卷宗目录 DO
 *
 * <AUTHOR>
 */
@TableName("dam_prisoner_catalog")
@KeySequence("dam_prisoner_catalog_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrisonerCatalogDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 目录模板Id
     */
    private String catalogTemplateId;
    /**
     * 是否改变文书名称(1：是，0：否)
     */
    private String isChangeFileName;
    /**
     * 编目目录数据
     */
    private String bmCatalogData;
    /**
     * 组卷目录数据
     */
    private String zjCatalogData;
    /**
     * 快速组卷目录数据
     */
    private String kszjCatalogData;
    /**
     * 汇总目录数据
     */
    private String hzCatalogData;

}
