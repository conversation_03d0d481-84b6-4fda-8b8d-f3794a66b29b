package com.rs.module.dam.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * pdf转换状态枚举
 * <AUTHOR>
 * @date 2025年4月19日
 */
@AllArgsConstructor
@Getter
public enum PdfConvertStatus {

	UN_START(0, "未开始"),
	
	CONVERTING(1, "转换中"),
	
	CONVERT_FINISH(2, "已完成"),
	
	DELETED(3, "已删除"),
	
	CONVERT_CANCEL(4, "已取消(未开始转换的pdf取消转换)"),
	
	PDF_DOWNLOAD_FAIL(5, "PDF下载失败"),
	
	PDF_CONVERTING_CANCEL(6, "转换过程中取消");
	
	//状态代码
	private int code;

	//状态描述
	private String remark;
}
