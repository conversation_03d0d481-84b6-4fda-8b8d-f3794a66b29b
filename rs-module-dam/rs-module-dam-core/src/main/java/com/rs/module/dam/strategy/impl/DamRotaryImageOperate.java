package com.rs.module.dam.strategy.impl;

import java.util.Set;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rs.module.dam.entity.material.MaterialImageDO;
import com.rs.module.dam.service.material.MaterialImageService;
import com.rs.module.dam.strategy.OperateTypeService;

/**
 * 图片旋转操作
 * <AUTHOR>
 * @date 2025年4月18日
 */
@Service(value = "damRotaryImageOperate")
public class DamRotaryImageOperate implements OperateTypeService {
    
    @Resource
    private MaterialImageService materialImageService;

    @Override
    public Set<String> operateByType(JSONObject materialObject) {
    	
        // 图片存储路径改变
        JSONArray deleteMaterialFileArray = materialObject.getJSONArray("deleteMaterialFile");
        
        for (int j = 0; j < deleteMaterialFileArray.size(); j++) {
            JSONObject deleteMaterialFile = deleteMaterialFileArray.getJSONObject(j);
            MaterialImageDO materialImageDO = JSONObject.toJavaObject(deleteMaterialFile, MaterialImageDO.class);
            materialImageService.updateUrlById(materialImageDO);
        }
        
        return null;
    }
}
