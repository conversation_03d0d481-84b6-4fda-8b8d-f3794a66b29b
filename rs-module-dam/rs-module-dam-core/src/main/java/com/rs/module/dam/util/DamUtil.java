package com.rs.module.dam.util;

import java.util.Arrays;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.cons.CommonConstants;
import com.bsp.common.orm.mybatis.util.WrapUtil;
import com.rs.module.dam.entity.material.MaterialInfoDO;

/**
 * 数字档案工具类
 * <AUTHOR>
 * @date 2025年4月17日
 */
public class DamUtil {

	/**
	 * 获取卷宗材料通用Wrapper
	 * @param jgrybm String 监管人员编码
	 * @param type String 材料类型
	 * @return Wrapper<MaterialInfoDO>
	 */
	public static QueryWrapper<MaterialInfoDO> getMaterialInfoWrapper(String jgrybm, String type){
		QueryWrapper<MaterialInfoDO> wrapper = WrapUtil.eq(MaterialInfoDO.class,
				new String[] {"jgrybm"}, new Object[] {jgrybm})
				.orderByAsc("xh").orderByAsc("add_time");
		
		//多种材料类型
		if(type.indexOf(",") > 0) {
			wrapper.in("type", Arrays.asList(type.split(CommonConstants.DEFAULT_SPLIT_STR)));
		}
		else {
			wrapper.eq("type", type);
		}
		
		return wrapper;
	}
}
