package com.rs.module.dam.service.material;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.dao.material.MaterialMarkerHistoryDao;
import com.rs.module.dam.entity.material.MaterialMarkerHistoryDO;
import com.rs.module.dam.vo.material.MaterialMarkerHistoryListReqVO;
import com.rs.module.dam.vo.material.MaterialMarkerHistoryPageReqVO;
import com.rs.module.dam.vo.material.MaterialMarkerHistorySaveReqVO;


/**
 * 卷宗材料标注历史 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MaterialMarkerHistoryServiceImpl extends BaseServiceImpl<MaterialMarkerHistoryDao, MaterialMarkerHistoryDO> implements MaterialMarkerHistoryService {

    @Resource
    private MaterialMarkerHistoryDao materialMarkerHistoryDao;

    @Override
    public String createMaterialMarkerHistory(MaterialMarkerHistorySaveReqVO createReqVO) {
        // 插入
        MaterialMarkerHistoryDO materialMarkerHistory = BeanUtils.toBean(createReqVO, MaterialMarkerHistoryDO.class);
        materialMarkerHistoryDao.insert(materialMarkerHistory);
        // 返回
        return materialMarkerHistory.getId();
    }

    @Override
    public void updateMaterialMarkerHistory(MaterialMarkerHistorySaveReqVO updateReqVO) {
        // 校验存在
        validateMaterialMarkerHistoryExists(updateReqVO.getId());
        // 更新
        MaterialMarkerHistoryDO updateObj = BeanUtils.toBean(updateReqVO, MaterialMarkerHistoryDO.class);
        materialMarkerHistoryDao.updateById(updateObj);
    }

    @Override
    public void deleteMaterialMarkerHistory(String id) {
        // 校验存在
        validateMaterialMarkerHistoryExists(id);
        // 删除
        materialMarkerHistoryDao.deleteById(id);
    }

    private void validateMaterialMarkerHistoryExists(String id) {
        if (materialMarkerHistoryDao.selectById(id) == null) {
            throw new ServerException("卷宗材料标注历史数据不存在");
        }
    }

    @Override
    public MaterialMarkerHistoryDO getMaterialMarkerHistory(String id) {
        return materialMarkerHistoryDao.selectById(id);
    }

    @Override
    public PageResult<MaterialMarkerHistoryDO> getMaterialMarkerHistoryPage(MaterialMarkerHistoryPageReqVO pageReqVO) {
        return materialMarkerHistoryDao.selectPage(pageReqVO);
    }

    @Override
    public List<MaterialMarkerHistoryDO> getMaterialMarkerHistoryList(MaterialMarkerHistoryListReqVO listReqVO) {
        return materialMarkerHistoryDao.selectList(listReqVO);
    }


}
