package com.rs.module.dam.service.material;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.dao.material.MaterialLabelDao;
import com.rs.module.dam.entity.material.MaterialLabelDO;
import com.rs.module.dam.vo.material.MaterialLabelListReqVO;
import com.rs.module.dam.vo.material.MaterialLabelPageReqVO;
import com.rs.module.dam.vo.material.MaterialLabelSaveReqVO;


/**
 * 卷宗材料标签 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MaterialLabelServiceImpl extends BaseServiceImpl<MaterialLabelDao, MaterialLabelDO> implements MaterialLabelService {

    @Resource
    private MaterialLabelDao materialLabelDao;

    @Override
    public String createMaterialLabel(MaterialLabelSaveReqVO createReqVO) {
        // 插入
        MaterialLabelDO materialLabel = BeanUtils.toBean(createReqVO, MaterialLabelDO.class);
        materialLabelDao.insert(materialLabel);
        // 返回
        return materialLabel.getId();
    }

    @Override
    public void updateMaterialLabel(MaterialLabelSaveReqVO updateReqVO) {
        // 校验存在
        validateMaterialLabelExists(updateReqVO.getId());
        // 更新
        MaterialLabelDO updateObj = BeanUtils.toBean(updateReqVO, MaterialLabelDO.class);
        materialLabelDao.updateById(updateObj);
    }

    @Override
    public void deleteMaterialLabel(String id) {
        // 校验存在
        validateMaterialLabelExists(id);
        // 删除
        materialLabelDao.deleteById(id);
    }

    private void validateMaterialLabelExists(String id) {
        if (materialLabelDao.selectById(id) == null) {
            throw new ServerException("卷宗材料标签数据不存在");
        }
    }

    @Override
    public MaterialLabelDO getMaterialLabel(String id) {
        return materialLabelDao.selectById(id);
    }

    @Override
    public PageResult<MaterialLabelDO> getMaterialLabelPage(MaterialLabelPageReqVO pageReqVO) {
        return materialLabelDao.selectPage(pageReqVO);
    }

    @Override
    public List<MaterialLabelDO> getMaterialLabelList(MaterialLabelListReqVO listReqVO) {
        return materialLabelDao.selectList(listReqVO);
    }
    
    /**
     * 获取用户材料标注列表
     * @param materialId String 材料Id
     * @param fileId String 文件Id
     * @return List<MaterialLabelDO>
     */
    @Override
    public List<MaterialLabelDO> getUserMaterialLabelList(String materialId, String fileId) {
    	QueryWrapper<MaterialLabelDO> wrapper = new QueryWrapper<>();
    	wrapper.eq("add_user", SessionUserUtil.getSessionUser().getIdCard());
    	
    	if(StringUtil.isNotEmpty(materialId)) {
    		wrapper.eq("material_id", materialId);
    	}
    	if(StringUtil.isNotEmpty(fileId)) {
    		wrapper.eq("file_id", fileId);
    	}
    	
    	return list(wrapper);
    }
}
