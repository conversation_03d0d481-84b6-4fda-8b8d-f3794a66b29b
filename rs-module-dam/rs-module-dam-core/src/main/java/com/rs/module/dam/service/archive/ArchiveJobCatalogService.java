package com.rs.module.dam.service.archive;

import java.util.List;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.archive.ArchiveJobCatalogDO;
import com.rs.module.dam.vo.archive.ArchiveJobCatalogListReqVO;
import com.rs.module.dam.vo.archive.ArchiveJobCatalogPageReqVO;
import com.rs.module.dam.vo.archive.ArchiveJobCatalogSaveReqVO;

/**
 * 卷宗归档任务目录 Service 接口
 *
 * <AUTHOR>
 */
public interface ArchiveJobCatalogService extends IBaseService<ArchiveJobCatalogDO>{

    /**
     * 创建卷宗归档任务目录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createArchiveJobCatalog(@Valid ArchiveJobCatalogSaveReqVO createReqVO);

    /**
     * 更新卷宗归档任务目录
     *
     * @param updateReqVO 更新信息
     */
    void updateArchiveJobCatalog(@Valid ArchiveJobCatalogSaveReqVO updateReqVO);

    /**
     * 删除卷宗归档任务目录
     *
     * @param id 编号
     */
    void deleteArchiveJobCatalog(String id);

    /**
     * 获得卷宗归档任务目录
     *
     * @param id 编号
     * @return 卷宗归档任务目录
     */
    ArchiveJobCatalogDO getArchiveJobCatalog(String id);

    /**
    * 获得卷宗归档任务目录分页
    *
    * @param pageReqVO 分页查询
    * @return 卷宗归档任务目录分页
    */
    PageResult<ArchiveJobCatalogDO> getArchiveJobCatalogPage(ArchiveJobCatalogPageReqVO pageReqVO);

    /**
    * 获得卷宗归档任务目录列表
    *
    * @param listReqVO 查询条件
    * @return 卷宗归档任务目录列表
    */
    List<ArchiveJobCatalogDO> getArchiveJobCatalogList(ArchiveJobCatalogListReqVO listReqVO);


}
