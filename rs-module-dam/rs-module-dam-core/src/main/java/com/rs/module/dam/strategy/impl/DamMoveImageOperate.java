package com.rs.module.dam.strategy.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.util.CollectionUtil;
import com.rs.module.dam.constant.DamConstants;
import com.rs.module.dam.entity.material.MaterialImageDO;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.service.material.MaterialImageService;
import com.rs.module.dam.strategy.OperateTypeService;

/**
 * 图片移动操作
 * <AUTHOR>
 * @date 2025年4月18日
 */
@Service(value = "damMoveImageOperate")
public class DamMoveImageOperate implements OperateTypeService {

	@Resource
    private MaterialImageService materialImageService;

    @Override
    public Set<String> operateByType(JSONObject materialObject) {
        String operate = materialObject.getString("operate");
        MaterialInfoDO materialInfo = JSONObject.toJavaObject(materialObject, MaterialInfoDO.class);
        String materialInfoId = materialInfo.getId();
        List<String> imageIds = getImageIds(materialObject);
        
        //删除历史图片
        materialImageService.removeByIds(imageIds);
        
        List<MaterialImageDO> materialImageList = getMaterialImageList(materialInfoId, materialObject);
        
        // 图片所属目录移动，相当于新增目录材料
        if (DamConstants.OPERATE_EDIT.equals(operate)) {
            if (CollectionUtil.isNotNull(materialImageList)) {
            	materialImageService.updateBatchById(materialImageList);
            }
        }
        else if (DamConstants.OPERATE_DELETE.equals(operate)) {
            if (!CollectionUtils.isEmpty(materialImageList)) {
            	materialImageService.updateBatchById(materialImageList);
            }
        }
        
        return null;
    }

    private List<MaterialImageDO> getMaterialImageList(String materialId, JSONObject materialObj) {
        JSONArray deleteMaterialFileArray = materialObj.getJSONArray("deleteMaterialFile");
        QueryWrapper<MaterialImageDO> wrapper = new QueryWrapper<>();
        wrapper.eq("material_id", materialId);        
        List<MaterialImageDO> materialImageList = materialImageService.list(wrapper);
        for (int j = 0; j < deleteMaterialFileArray.size(); j++) {
            JSONObject deleteMaterialFile = deleteMaterialFileArray.getJSONObject(j);
            Integer deleteXh = deleteMaterialFile.getInteger("xh");
            for (MaterialImageDO materialImage : materialImageList) {
                Integer xh = materialImage.getXh();
                if (xh > deleteXh ) {
                	materialImage.setXh(xh - 1);
                }
            }
        }
        
        return materialImageList;
    }

    /**
     * 获取数据中的图片Id
     * @param materialObj JSONObject
     * @return List<String>
     */
    private List<String> getImageIds(JSONObject materialObj) {
        JSONArray deleteMaterialFileArray = materialObj.getJSONArray("deleteMaterialFile");
        List<String> imageIds = new ArrayList<>();
        
        for (int j = 0; j < deleteMaterialFileArray.size(); j++) {
            JSONObject deleteMaterialFile = deleteMaterialFileArray.getJSONObject(j);
            imageIds.add(deleteMaterialFile.getString("id"));
        }
        
        return imageIds;
    }
}
