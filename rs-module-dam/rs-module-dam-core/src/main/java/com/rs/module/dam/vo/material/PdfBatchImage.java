package com.rs.module.dam.vo.material;

import java.util.List;

import com.rs.module.dam.entity.material.PdfBatchDO;
import com.rs.module.dam.entity.material.PdfImageDO;

import lombok.Data;

/**
 * pdf图片批量转换对象
 * <AUTHOR>
 * @date 2025年4月20日
 */
@Data
public class PdfBatchImage {

	//pdf主键
    private String pdfId;

    //图片集合
    private List<PdfImageDO> imageList;

    //图片主键集合
    private List<String> imageIdList;

    //序号
    private Integer xh;

    //pdf批量处理对象
    private PdfBatchDO dmsPdfBatch;
}
