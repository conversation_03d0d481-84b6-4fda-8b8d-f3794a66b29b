package com.rs.module.dam.ocr.gosuncn;

/**
 * gosuncn-高新兴OCR接口http响应码
 * <AUTHOR>
 * @date 2025年4月26日
 */
public enum GoOcrHttpStatus {

	OK(200, "成功"),
	
	INVALID_PARAMETER(401, "参数无效"),
	
	PARAMETER_MISSING(402, "必填参数缺失"),
	
	DOWNLOAD_FAILED(405, "图片下载失败"),
	
	INCOMPLETE_RETURN(406, "返回数据不完整"),
	
	OTHER_ERROR(410, "其他错误"),
	
	AUTHENTICATION_FAIL(415, "服务鉴权失败（服务未关联应用或审批未通过）"),
	
	SERVICE_NOT_ENABLED(425, "管理平台的服务未启用"),
	
	EXCEED_FREE_CALLS(440, "超过免费调用次数，请申请服务应用权限"),
	
	INSUFFICIENT_RESOURCE(501, "资源不足,机器资源已占满");
	
	private int code;
	
	private String desc;

	private GoOcrHttpStatus(int code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}
}
