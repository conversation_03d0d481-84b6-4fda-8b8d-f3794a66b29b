package com.rs.module.dam.util;

import java.time.Duration;
import java.util.Map;

import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSONObject;

/**
 * http访问工具类
 * <AUTHOR>
 * @date 2025年4月26日
 */
public class RestTemplateUtil {

    private static RestTemplate restTemplate;

    static {
        RestTemplateBuilder restTemplateBuilder = new RestTemplateBuilder();
        restTemplate = restTemplateBuilder
                .requestFactory(HttpComponentsClientHttpRequestFactory.class)
                .setConnectTimeout(Duration.ofMillis(1000000))
                .setReadTimeout(Duration.ofMillis(1000000))
                .build();
    }

    public static JSONObject postWithJson(String url, JSONObject params){
        return postWithJson( url,  params, null);
    }

    @SuppressWarnings({ "deprecation", "unchecked", "rawtypes" })
	public static JSONObject postWithJson(String url, JSONObject params, Map<String,String> headers){
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);

        if (headers != null){
            for (Map.Entry<String, String> header : headers.entrySet()) {
                httpHeaders.add(header.getKey(),header.getValue());
            }
        }
        HttpEntity requestEntity = new HttpEntity(params.toJSONString(),httpHeaders);
        ResponseEntity<JSONObject> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, JSONObject.class);
        return responseEntity.getBody();
    }

    public static JSONObject postWithForm(String url,Map<String,String> paramsMap){
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.MULTIPART_FORM_DATA);

        MultiValueMap<String,String> multiValueMap = new LinkedMultiValueMap<String,String>();

        for (Map.Entry<String, String> params : paramsMap.entrySet()) {
            multiValueMap.add(params.getKey(),params.getValue());
        }

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(multiValueMap, httpHeaders);
        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(url, request, JSONObject.class);

        return responseEntity.getBody();
    }
}
