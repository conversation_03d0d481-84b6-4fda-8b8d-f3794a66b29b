package com.rs.module.dam.vo.prisoner;
import java.util.Date;

import javax.validation.constraints.NotEmpty;

import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 卷宗监管人员新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = false)
public class PrisonerInfoSaveReqVO extends BaseVO{
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("卷宗状态（01：已登记；02：已组卷；03：已归档）")
    private String jzzt;

    @ApiModelProperty("档案编号")
    private String dabh;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;
    
    @ApiModelProperty("姓名")
    private String xm;

    @ApiModelProperty("涉嫌罪名")
    private String sxzm;

    @ApiModelProperty("是否未成年人档案")
    private String sfwcnrda;

    @ApiModelProperty("案件编号")
    private String ajbh;

    @ApiModelProperty("案件名称")
    private String ajmc;
    
    @ApiModelProperty("监室号")
    private String jsh;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("卷宗借阅次数")
    private Short jycs;

    @ApiModelProperty("归档时间")
    private Date gdsj;

    @ApiModelProperty("归档人")
    private String gdUser;

    @ApiModelProperty("归档人姓名")
    private String gdUserName;

    @ApiModelProperty("归档人机构编号")
    private String gdOrgCode;

    @ApiModelProperty("归档人机构名称")
    private String gdOrgName;

    @ApiModelProperty("组卷时间")
    private Date zjsj;

    @ApiModelProperty("组卷人")
    private String zjUser;

    @ApiModelProperty("组卷人姓名")
    private String zjUserName;

    @ApiModelProperty("组卷人机构编号")
    private String zjOrgCode;

    @ApiModelProperty("组卷人机构名称")
    private String zjOrgName;

}
