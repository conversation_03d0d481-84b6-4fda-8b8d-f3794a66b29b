package com.rs.module.dam.util;

import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

import javax.imageio.ImageIO;

import com.swetake.util.Qrcode;

import jp.sourceforge.qrcode.QRCodeDecoder;
import jp.sourceforge.qrcode.exception.DecodingFailedException;
import lombok.extern.slf4j.Slf4j;

/**
 * 二维码工具类
 * <AUTHOR>
 * @date 2025年4月22日
 */
@Slf4j
public class QrCodeUtil {
	
    /** 二维码内容默认编码格式-UTF-8 */
    private static String CONTENT_CODE_FORMAT = "UTF-8";


    /**
     * 生成二维码(QRCode)图片
     * @param content 存储内容
     * @param output 输出流
     * @param imgType 图片类型
     * @param size 二维码尺寸
     */
    public static void encoderQrCode(String content, OutputStream output, String imgType,int width,int height, int size) {
        try {
            BufferedImage bufImg = qrCodeCommon(content, width, height, size);
            // 生成二维码QRCode图片
            ImageIO.write(bufImg, imgType, output);
        }
        catch (Exception e) {
        	log.error("发生异常，原因：", e);
        }
    }
    public static void encoderQrCode(String content, String imgPath, String imgType,int width,int height, int size) {
        try {
            BufferedImage bufImg = qrCodeCommon(content, width,height, size);

            File imgFile = new File(imgPath);
            // 生成二维码QRCode图片
            ImageIO.write(bufImg, imgType, imgFile);
        }
        catch (Exception e) {
        	log.error("发生异常，原因：", e);
        }
    }


    /**
     * 生成二维码(QRCode)图片的公共方法
     * @param content 存储内容
     * @param width height 二维码尺寸  (67 * 67)
     * @return
     */
    private static BufferedImage qrCodeCommon(String content, int width, int height, int size) {
        BufferedImage bufImg = null;
        try {
            Qrcode qrcodeHandler = new Qrcode();
            // 设置二维码排错率，可选L(7%)、M(15%)、Q(25%)、H(30%)，排错率越高可存储的信息越少，但对二维码清晰度的要求越小
            qrcodeHandler.setQrcodeErrorCorrect('M');
            qrcodeHandler.setQrcodeEncodeMode('B');
            // 设置设置二维码尺寸，取值范围1-40，值越大尺寸越大，可存储的信息越大
            qrcodeHandler.setQrcodeVersion(size);
            // 获得内容的字节数组，设置编码格式
            byte[] contentBytes = content.getBytes(CONTENT_CODE_FORMAT);

            width = 67 + 12*(size-1);
            height = width;

            bufImg = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D gs = bufImg.createGraphics();
            // 设置背景颜色
            gs.setBackground(Color.WHITE);
            gs.clearRect(0, 0, width, height);

            // 设定图像颜色> BLACK
            gs.setColor(Color.BLACK);
            // 设置偏移量，不设置可能导致解析出错
            int pixoff = 2;
            // 输出内容> 二维码
            if (contentBytes.length > 0 && contentBytes.length < 800) {
                boolean[][] codeOut = qrcodeHandler.calQrcode(contentBytes);
                for (int i = 0; i < codeOut.length; i++) {
                    for (int j = 0; j < codeOut.length; j++) {
                        if (codeOut[j][i]) {
                            gs.fillRect(j * 3 + pixoff, i * 3 + pixoff, 3, 3);
                        }
                    }
                }
            } else {
                throw new Exception("QRCode content bytes length = " + contentBytes.length + " not in [0, 800].");
            }
            gs.dispose();
            bufImg.flush();
        } catch (Exception e) {
        	log.error("发生异常，原因：", e);
        }
        return bufImg;
    }

    /**
     * 解析二维码（QRCode）
     * @param input 输入流
     * @return
     */
    public static String decoderQrCode(InputStream input) {
        BufferedImage bufImg = null;
        String content = null;
        try {
            bufImg = ImageIO.read(input);
            QRCodeDecoder decoder = new QRCodeDecoder();
            content = new String(decoder.decode(new TwoDimensionCodeImage(bufImg)), CONTENT_CODE_FORMAT);
        } catch (IOException e) {
        	log.error("发生异常，原因：", e);
        } catch (DecodingFailedException e) {
        	log.error("发生异常，原因：", e);
        }
        return content;
    }


    public static void main(String[] args)  {
        String imgPath = "D:/QRCodeTest/Michael_QRCode.png";
        String encoderContent = "Hello 大大、小小,welcome to QRCode!" + "\nMyblog [ http://sjsky.iteye.com ]" + "\nEMail [ <EMAIL> ]";
        encoderQrCode(encoderContent, imgPath, "png",300,100,7);
        System.out.println("========encoder success");
        FileInputStream fis=null;
        try {
        fis=new FileInputStream(imgPath);
        String decoderContent = decoderQrCode(fis);
        System.out.println("解析结果如下：");
        System.out.println(decoderContent);
        System.out.println("========decoder success!!!");
        } catch (Exception e) {
        	log.error("发生异常，原因：", e);
        }finally {
            if(fis != null ){
                try {
                    fis.close();
                } catch (IOException e) {
                	log.error("发生异常，原因：", e);
                }
            }
        }
    }

}
