package com.rs.module.dam.vo.material;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 卷宗材料标签新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MaterialLabelSaveReqVO extends BaseVO{

	@ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("材料Id")
    private String materialId;

    @ApiModelProperty("材料目录Id")
    private String catalogId;

    @ApiModelProperty("材料文件图片Id")
    private String fileId;

}
