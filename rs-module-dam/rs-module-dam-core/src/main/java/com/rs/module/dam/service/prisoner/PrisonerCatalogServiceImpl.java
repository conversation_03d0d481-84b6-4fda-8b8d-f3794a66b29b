package com.rs.module.dam.service.prisoner;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.dao.prisoner.PrisonerCatalogDao;
import com.rs.module.dam.entity.prisoner.PrisonerCatalogDO;
import com.rs.module.dam.vo.prisoner.PrisonerCatalogListReqVO;
import com.rs.module.dam.vo.prisoner.PrisonerCatalogPageReqVO;
import com.rs.module.dam.vo.prisoner.PrisonerCatalogSaveReqVO;


/**
 * 监管人员卷宗目录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrisonerCatalogServiceImpl extends BaseServiceImpl<PrisonerCatalogDao, PrisonerCatalogDO> implements PrisonerCatalogService {

    @Resource
    private PrisonerCatalogDao prisonerCatalogDao;

    @Override
    public String createPrisonerCatalog(PrisonerCatalogSaveReqVO createReqVO) {
        // 插入
        PrisonerCatalogDO prisonerCatalog = BeanUtils.toBean(createReqVO, PrisonerCatalogDO.class);
        prisonerCatalogDao.insert(prisonerCatalog);
        // 返回
        return prisonerCatalog.getId();
    }

    @Override
    public void updatePrisonerCatalog(PrisonerCatalogSaveReqVO updateReqVO) {
        // 校验存在
        validatePrisonerCatalogExists(updateReqVO.getId());
        // 更新
        PrisonerCatalogDO updateObj = BeanUtils.toBean(updateReqVO, PrisonerCatalogDO.class);
        prisonerCatalogDao.updateById(updateObj);
    }

    @Override
    public void deletePrisonerCatalog(String id) {
        // 校验存在
        validatePrisonerCatalogExists(id);
        // 删除
        prisonerCatalogDao.deleteById(id);
    }

    private void validatePrisonerCatalogExists(String id) {
        if (prisonerCatalogDao.selectById(id) == null) {
            throw new ServerException("监管人员卷宗目录数据不存在");
        }
    }

    @Override
    public PrisonerCatalogDO getPrisonerCatalog(String id) {
        return prisonerCatalogDao.selectById(id);
    }

    @Override
    public PageResult<PrisonerCatalogDO> getPrisonerCatalogPage(PrisonerCatalogPageReqVO pageReqVO) {
        return prisonerCatalogDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrisonerCatalogDO> getPrisonerCatalogList(PrisonerCatalogListReqVO listReqVO) {
        return prisonerCatalogDao.selectList(listReqVO);
    }


}
