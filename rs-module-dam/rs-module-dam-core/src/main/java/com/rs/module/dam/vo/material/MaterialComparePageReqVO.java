package com.rs.module.dam.vo.material;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel(description = "管理后台 - 卷宗材料比对分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MaterialComparePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("左边材料文件图片ID")
    private String leftFileId;

    @ApiModelProperty("右边材料文件图片ID")
    private String rightFileId;

    @ApiModelProperty("左边材料ID")
    private String leftMaterialId;

    @ApiModelProperty("右边材料ID")
    private String rightMaterialId;

    @ApiModelProperty("比对图片url")
    private String url;

    @ApiModelProperty("备注")
    private String remark;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
