package com.rs.module.dam.ocr.vo;

import java.util.List;

import com.rs.module.dam.entity.archive.ArchiveCoverDO;
import com.rs.module.dam.entity.material.MaterialInfoDO;

/**
 * 双层pdf参数类
 * <AUTHOR>
 * @date 2025年4月21日
 */
public class OcrScpdfParams {
	
	//是否启用v2版本
	private boolean isV2;
	
	//是否处理双层pdf
	private boolean isDealScpdf;

	//卷宗封面
	private ArchiveCoverDO cover;
	
	//卷宗材料
	private List<MaterialInfoDO> materialList;
	
	//监管人员编码
	private String jgrybm;
	
	//组卷类型(0:编目组卷完成组卷页面、1:卷宗导入确认组卷页面)
	private String zjType;
	
	//身份证号码
	private String idCard;
	
	//机构代码
	private String orgCode;

	public OcrScpdfParams() {
	}

	public OcrScpdfParams(boolean isV2) {
		this.isV2 = isV2;
	}

	public boolean isV2() {
		return isV2;
	}

	public void setV2(boolean isV2) {
		this.isV2 = isV2;
	}

	public boolean isDealScpdf() {
		return isDealScpdf;
	}

	public OcrScpdfParams setDealScpdf(boolean isDealScpdf) {
		this.isDealScpdf = isDealScpdf;
		return this;
	}

	public ArchiveCoverDO getCover() {
		return cover;
	}

	public OcrScpdfParams setCover(ArchiveCoverDO cover) {
		this.cover = cover;
		return this;
	}

	public List<MaterialInfoDO> getMaterialList() {
		return materialList;
	}

	public OcrScpdfParams setMaterialList(List<MaterialInfoDO> materialList) {
		this.materialList = materialList;
		return this;
	}

	public String getJgrybm() {
		return jgrybm;
	}

	public OcrScpdfParams setJgrybm(String jgrybm) {
		this.jgrybm = jgrybm;
		return this;
	}

	public String getZjType() {
		return zjType;
	}

	public OcrScpdfParams setZjType(String zjType) {
		this.zjType = zjType;
		return this;
	}

	public String getIdCard() {
		return idCard;
	}

	public OcrScpdfParams setIdCard(String idCard) {
		this.idCard = idCard;
		return this;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public OcrScpdfParams setOrgCode(String orgCode) {
		this.orgCode = orgCode;
		return this;
	}
}
