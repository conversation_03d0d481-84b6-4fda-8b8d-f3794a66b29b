package com.rs.module.dam.service.ocr;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.ocr.FormatCheckResultDO;
import com.rs.module.dam.vo.ocr.FormatCheckResultListReqVO;
import com.rs.module.dam.vo.ocr.FormatCheckResultPageReqVO;
import com.rs.module.dam.vo.ocr.FormatCheckResultSaveReqVO;

/**
 * ocr格式化审查结果 Service 接口
 *
 * <AUTHOR>
 */
public interface FormatCheckResultService extends IBaseService<FormatCheckResultDO>{

    /**
     * 创建ocr格式化审查结果
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createFormatCheckResult(@Valid FormatCheckResultSaveReqVO createReqVO);

    /**
     * 更新ocr格式化审查结果
     *
     * @param updateReqVO 更新信息
     */
    void updateFormatCheckResult(@Valid FormatCheckResultSaveReqVO updateReqVO);

    /**
     * 删除ocr格式化审查结果
     *
     * @param id 编号
     */
    void deleteFormatCheckResult(String id);

    /**
     * 获得ocr格式化审查结果
     *
     * @param id 编号
     * @return ocr格式化审查结果
     */
    FormatCheckResultDO getFormatCheckResult(String id);

    /**
    * 获得ocr格式化审查结果分页
    *
    * @param pageReqVO 分页查询
    * @return ocr格式化审查结果分页
    */
    PageResult<FormatCheckResultDO> getFormatCheckResultPage(FormatCheckResultPageReqVO pageReqVO);

    /**
    * 获得ocr格式化审查结果列表
    *
    * @param listReqVO 查询条件
    * @return ocr格式化审查结果列表
    */
    List<FormatCheckResultDO> getFormatCheckResultList(FormatCheckResultListReqVO listReqVO);

    /**
     * 更新检测结果是否展示
     * @param jobId String 任务Id
     * @param imageId String 图片Id
     * @param checkTypeList List<String> 检测类型集合
     * @param showData String 是否展示
     */
    public void updateShowData(String jobId, String imageId, List<String> checkTypeList, String showData);
    
    /**
     * 更新检测结果是否展示
     * @param jobId String 任务Id
     * @param imageId String 图片Id
     * @param checkType String 检测类型
     * @param showData String 是否展示
     */
    public void updateShowData(String jobId, String imageId, String checkType, String showData);
    
    /**
     * 根据参数获取检测结果中的文件Id
     * @param queryParams Map<String, Object> 查询参数
     * @return List<String>
     */
    public List<String> selectFileIdByParams(Map<String, Object> queryParams);
    
    /**
     * 更新格式化审查结果的任务Id
     * @param newJobId String 新的任务Id
     * @param oldJobId String 旧的任务Id
     */
    public void updateCheckResultJobId(String newJobId, String oldJobId);
}
