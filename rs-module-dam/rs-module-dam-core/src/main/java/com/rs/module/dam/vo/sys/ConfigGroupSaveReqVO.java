package com.rs.module.dam.vo.sys;
import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 卷宗配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ConfigGroupSaveReqVO extends BaseVO{
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("文件服务器地址")
    private String fileServer;

    @ApiModelProperty("是否开启内部借阅审批 1:是, 0:否")
    private String enableBorrowApprove;

    @ApiModelProperty("是否开启文书自动同步卷宗服务 1:是, 0:否")
    private String enableWsAutoSyn;

    @ApiModelProperty("是否开启扫描仪 1:是，0:否")
    private String openScan;

    @ApiModelProperty("扫描仪类型")
    private String scanType;

    @ApiModelProperty("扫描服务地址")
    private String scanServer;

    @ApiModelProperty("扫描仪授权码")
    private String scanLicense;

    @ApiModelProperty("是否开启ocr服务, 0:否, 1:是")
    private String openOcr;

    @ApiModelProperty("ocr服务地址")
    private String ocrServer;

    @ApiModelProperty("配置方式 1:默认配置, 2:定制化配置")
    private String configType;

    @ApiModelProperty("对接isp的appId")
    private String appId;

    @ApiModelProperty("对接isp的appKey")
    private String appKey;

    @ApiModelProperty("对接isp的secretKey")
    private String secretKey;

    @ApiModelProperty("组卷后是否生成双层pdf")
    private String makeDoublePdf;

    @ApiModelProperty("租户ID")
    private String tenantId;

    @ApiModelProperty("是否开启未成年人特殊审批 1:是, 0:否")
    private String enableMinorApprove;

//    @ApiModelProperty("卷宗配置机构关联列表")
//    private List<ConfigGroupOrgDO> configGroupOrgs;

}
