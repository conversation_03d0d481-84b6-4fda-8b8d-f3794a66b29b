package com.rs.module.dam.entity.material;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO_;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 卷宗材料回收 DO
 *
 * <AUTHOR>
 */
@TableName("dam_material_recycle_bin")
@KeySequence("dam_material_recycle_bin_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialRecycleBinDO extends BaseDO_ {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 图片关联的材料Id(dam_material_info表主键)
     */
    private String materialId;
    /**
     * 图片Id(dam_material_image表主键)
     */
    private String imageId;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 材料名称
     */
    private String name;
    /**
     * 材料数量
     */
    private Integer pageCount;
    /**
     * 模板Id
     */
    private String templateId;
    /**
     * 分卷目录Id
     */
    private String partCatalogId;
    /**
     * 目录Id
     */
    private String catalogId;
    /**
     * 材料序号
     */
    private Integer xh;
    /**
     * 材料地址
     */
    @TableField(exist = false)
    private String url;
}
