package com.rs.module.dam.service.material;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.dao.material.MaterialRecycleBinDao;
import com.rs.module.dam.entity.material.MaterialImageDO;
import com.rs.module.dam.entity.material.MaterialRecycleBinDO;
import com.rs.module.dam.vo.material.MaterialRecycleBinListReqVO;
import com.rs.module.dam.vo.material.MaterialRecycleBinPageReqVO;
import com.rs.module.dam.vo.material.MaterialRecycleBinSaveReqVO;


/**
 * 卷宗材料回收 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MaterialRecycleBinServiceImpl extends BaseServiceImpl<MaterialRecycleBinDao, MaterialRecycleBinDO> implements MaterialRecycleBinService {

    @Resource
    private MaterialRecycleBinDao materialRecycleBinDao;
    
    @Resource
    private MaterialImageService materialImageService;

    @Override
    public String createMaterialRecycleBin(MaterialRecycleBinSaveReqVO createReqVO) {
        // 插入
        MaterialRecycleBinDO materialRecycleBin = BeanUtils.toBean(createReqVO, MaterialRecycleBinDO.class);
        materialRecycleBinDao.insert(materialRecycleBin);
        // 返回
        return materialRecycleBin.getId();
    }

    @Override
    public void updateMaterialRecycleBin(MaterialRecycleBinSaveReqVO updateReqVO) {
        // 校验存在
        validateMaterialRecycleBinExists(updateReqVO.getId());
        // 更新
        MaterialRecycleBinDO updateObj = BeanUtils.toBean(updateReqVO, MaterialRecycleBinDO.class);
        materialRecycleBinDao.updateById(updateObj);
    }

    @Override
    public void deleteMaterialRecycleBin(String id) {
        // 校验存在
        validateMaterialRecycleBinExists(id);
        // 删除
        materialRecycleBinDao.deleteById(id);
    }

    private void validateMaterialRecycleBinExists(String id) {
        if (materialRecycleBinDao.selectById(id) == null) {
            throw new ServerException("卷宗材料回收数据不存在");
        }
    }

    @Override
    public MaterialRecycleBinDO getMaterialRecycleBin(String id) {
        return materialRecycleBinDao.selectById(id);
    }

    @Override
    public PageResult<MaterialRecycleBinDO> getMaterialRecycleBinPage(MaterialRecycleBinPageReqVO pageReqVO) {
        return materialRecycleBinDao.selectPage(pageReqVO);
    }

    @Override
    public List<MaterialRecycleBinDO> getMaterialRecycleBinList(MaterialRecycleBinListReqVO listReqVO) {
        return materialRecycleBinDao.selectList(listReqVO);
    }

    /**
     * 更新材料Id
     * @param originalImageIds List<String> 图片Id集合
     * @param materialInfoId String 材料Id
     * @param catalogId String 目录Id
     * @param partCatalogId String 分卷目录Id
     */
    @Override
    public void updateMaterialId(List<String> originalImageIds, String materialInfoId,
    		String catalogId, String partCatalogId) {
    	materialRecycleBinDao.updateMaterialId(originalImageIds, materialInfoId, catalogId, partCatalogId);
    }
    
    /**
     * 根据监管人员编码获得卷宗材料回收列表
     * @param jgrybm String 监管人员编码
     * @return List<MaterialRecycleBinDO>
     */
    @Override
    public List<MaterialRecycleBinDO> getMaterialRecycleBinByJgrybm(String jgrybm){
    	QueryWrapper<MaterialRecycleBinDO> wrapper = new QueryWrapper<MaterialRecycleBinDO>();
    	wrapper.eq("jgrybm", jgrybm);
    	wrapper.orderByDesc("add_time");
    	List<MaterialRecycleBinDO> list = materialRecycleBinDao.selectList(wrapper);
    	
    	//循环绑定材料地址
    	for(MaterialRecycleBinDO naterialRecycleBinDO : list) {
    		String imageId = naterialRecycleBinDO.getImageId();
    		MaterialImageDO image = materialImageService.getOne(new QueryWrapper<MaterialImageDO>().eq("id", imageId));
    		if(image != null) {
    			naterialRecycleBinDO.setUrl(image.getUrl());
    		}
    	}
    	
    	return list;
    }
}
