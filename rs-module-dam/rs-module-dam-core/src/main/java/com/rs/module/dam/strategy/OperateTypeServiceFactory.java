package com.rs.module.dam.strategy;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 图片处理工厂类
 * <AUTHOR>
 * @date 2025年4月18日
 */
@Component
public class OperateTypeServiceFactory {
	
    /**
     * 	初始化时可以自动将OperateTypeService加入到map中，
     *  key为实现OperateTypeService类@Component("A")指定的值
     *  value为实现OperateTypeService类
     *  Map<"A", 实现类>
     */
    @Autowired
    private final Map<String, OperateTypeService> operateTypeInstanceMap = new ConcurrentHashMap<>();

    public OperateTypeService getOperateTypeInstance(String beanName) {
        OperateTypeService operateTypeInstance = operateTypeInstanceMap.get(beanName);

        if (operateTypeInstance == null) {
            throw new RuntimeException("未定义的operateTypeInstance");
        }
        return operateTypeInstance;
    }
}
