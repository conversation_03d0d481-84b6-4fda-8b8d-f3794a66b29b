package com.rs.module.dam.service.material;

import java.util.List;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.material.MaterialLabelHistoryDO;
import com.rs.module.dam.vo.material.MaterialLabelHistoryListReqVO;
import com.rs.module.dam.vo.material.MaterialLabelHistoryPageReqVO;
import com.rs.module.dam.vo.material.MaterialLabelHistorySaveReqVO;

/**
 * 卷宗材料标签历史 Service 接口
 *
 * <AUTHOR>
 */
public interface MaterialLabelHistoryService extends IBaseService<MaterialLabelHistoryDO>{

    /**
     * 创建卷宗材料标签历史
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMaterialLabelHistory(@Valid MaterialLabelHistorySaveReqVO createReqVO);

    /**
     * 更新卷宗材料标签历史
     *
     * @param updateReqVO 更新信息
     */
    void updateMaterialLabelHistory(@Valid MaterialLabelHistorySaveReqVO updateReqVO);

    /**
     * 删除卷宗材料标签历史
     *
     * @param id 编号
     */
    void deleteMaterialLabelHistory(String id);

    /**
     * 获得卷宗材料标签历史
     *
     * @param id 编号
     * @return 卷宗材料标签历史
     */
    MaterialLabelHistoryDO getMaterialLabelHistory(String id);

    /**
    * 获得卷宗材料标签历史分页
    *
    * @param pageReqVO 分页查询
    * @return 卷宗材料标签历史分页
    */
    PageResult<MaterialLabelHistoryDO> getMaterialLabelHistoryPage(MaterialLabelHistoryPageReqVO pageReqVO);

    /**
    * 获得卷宗材料标签历史列表
    *
    * @param listReqVO 查询条件
    * @return 卷宗材料标签历史列表
    */
    List<MaterialLabelHistoryDO> getMaterialLabelHistoryList(MaterialLabelHistoryListReqVO listReqVO);


}
