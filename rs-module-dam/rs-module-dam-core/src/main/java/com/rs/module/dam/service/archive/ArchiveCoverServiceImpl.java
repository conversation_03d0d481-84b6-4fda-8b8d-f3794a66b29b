package com.rs.module.dam.service.archive;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.dao.archive.ArchiveCoverDao;
import com.rs.module.dam.entity.archive.ArchiveCoverDO;
import com.rs.module.dam.vo.archive.ArchiveCoverListReqVO;
import com.rs.module.dam.vo.archive.ArchiveCoverPageReqVO;
import com.rs.module.dam.vo.archive.ArchiveCoverSaveReqVO;


/**
 * 卷宗封面 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ArchiveCoverServiceImpl extends BaseServiceImpl<ArchiveCoverDao, ArchiveCoverDO> implements ArchiveCoverService {

    @Resource
    private ArchiveCoverDao archiveCoverDao;

    @Override
    public String createArchiveCover(ArchiveCoverSaveReqVO createReqVO) {
        // 插入
        ArchiveCoverDO archiveCover = BeanUtils.toBean(createReqVO, ArchiveCoverDO.class);
        archiveCoverDao.insert(archiveCover);
        // 返回
        return archiveCover.getId();
    }

    @Override
    public void updateArchiveCover(ArchiveCoverSaveReqVO updateReqVO) {
        // 校验存在
        validateArchiveCoverExists(updateReqVO.getId());
        // 更新
        ArchiveCoverDO updateObj = BeanUtils.toBean(updateReqVO, ArchiveCoverDO.class);
        archiveCoverDao.updateById(updateObj);
    }

    @Override
    public void deleteArchiveCover(String id) {
        // 校验存在
        validateArchiveCoverExists(id);
        // 删除
        archiveCoverDao.deleteById(id);
    }

    private void validateArchiveCoverExists(String id) {
        if (archiveCoverDao.selectById(id) == null) {
            throw new ServerException("卷宗封面数据不存在");
        }
    }

    @Override
    public ArchiveCoverDO getArchiveCover(String id) {
        return archiveCoverDao.selectById(id);
    }

    @Override
    public PageResult<ArchiveCoverDO> getArchiveCoverPage(ArchiveCoverPageReqVO pageReqVO) {
        return archiveCoverDao.selectPage(pageReqVO);
    }

    @Override
    public List<ArchiveCoverDO> getArchiveCoverList(ArchiveCoverListReqVO listReqVO) {
        return archiveCoverDao.selectList(listReqVO);
    }


}
