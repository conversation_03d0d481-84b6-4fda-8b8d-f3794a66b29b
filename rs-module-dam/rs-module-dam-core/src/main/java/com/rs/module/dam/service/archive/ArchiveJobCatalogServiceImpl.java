package com.rs.module.dam.service.archive;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.dao.archive.ArchiveJobCatalogDao;
import com.rs.module.dam.entity.archive.ArchiveJobCatalogDO;
import com.rs.module.dam.vo.archive.ArchiveJobCatalogListReqVO;
import com.rs.module.dam.vo.archive.ArchiveJobCatalogPageReqVO;
import com.rs.module.dam.vo.archive.ArchiveJobCatalogSaveReqVO;


/**
 * 卷宗归档任务目录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ArchiveJobCatalogServiceImpl extends BaseServiceImpl<ArchiveJobCatalogDao, ArchiveJobCatalogDO> implements ArchiveJobCatalogService {

    @Resource
    private ArchiveJobCatalogDao archiveJobCatalogDao;

    @Override
    public String createArchiveJobCatalog(ArchiveJobCatalogSaveReqVO createReqVO) {
        // 插入
        ArchiveJobCatalogDO archiveJobCatalog = BeanUtils.toBean(createReqVO, ArchiveJobCatalogDO.class);
        archiveJobCatalogDao.insert(archiveJobCatalog);
        // 返回
        return archiveJobCatalog.getId();
    }

    @Override
    public void updateArchiveJobCatalog(ArchiveJobCatalogSaveReqVO updateReqVO) {
        // 校验存在
        validateArchiveJobCatalogExists(updateReqVO.getId());
        // 更新
        ArchiveJobCatalogDO updateObj = BeanUtils.toBean(updateReqVO, ArchiveJobCatalogDO.class);
        archiveJobCatalogDao.updateById(updateObj);
    }

    @Override
    public void deleteArchiveJobCatalog(String id) {
        // 校验存在
        validateArchiveJobCatalogExists(id);
        // 删除
        archiveJobCatalogDao.deleteById(id);
    }

    private void validateArchiveJobCatalogExists(String id) {
        if (archiveJobCatalogDao.selectById(id) == null) {
            throw new ServerException("卷宗归档任务目录数据不存在");
        }
    }

    @Override
    public ArchiveJobCatalogDO getArchiveJobCatalog(String id) {
        return archiveJobCatalogDao.selectById(id);
    }

    @Override
    public PageResult<ArchiveJobCatalogDO> getArchiveJobCatalogPage(ArchiveJobCatalogPageReqVO pageReqVO) {
        return archiveJobCatalogDao.selectPage(pageReqVO);
    }

    @Override
    public List<ArchiveJobCatalogDO> getArchiveJobCatalogList(ArchiveJobCatalogListReqVO listReqVO) {
        return archiveJobCatalogDao.selectList(listReqVO);
    }


}
