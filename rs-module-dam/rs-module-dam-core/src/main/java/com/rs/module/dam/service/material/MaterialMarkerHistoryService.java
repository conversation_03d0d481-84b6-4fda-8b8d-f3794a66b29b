package com.rs.module.dam.service.material;

import java.util.List;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.material.MaterialMarkerHistoryDO;
import com.rs.module.dam.vo.material.MaterialMarkerHistoryListReqVO;
import com.rs.module.dam.vo.material.MaterialMarkerHistoryPageReqVO;
import com.rs.module.dam.vo.material.MaterialMarkerHistorySaveReqVO;

/**
 * 卷宗材料标注历史 Service 接口
 *
 * <AUTHOR>
 */
public interface MaterialMarkerHistoryService extends IBaseService<MaterialMarkerHistoryDO>{

    /**
     * 创建卷宗材料标注历史
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMaterialMarkerHistory(@Valid MaterialMarkerHistorySaveReqVO createReqVO);

    /**
     * 更新卷宗材料标注历史
     *
     * @param updateReqVO 更新信息
     */
    void updateMaterialMarkerHistory(@Valid MaterialMarkerHistorySaveReqVO updateReqVO);

    /**
     * 删除卷宗材料标注历史
     *
     * @param id 编号
     */
    void deleteMaterialMarkerHistory(String id);

    /**
     * 获得卷宗材料标注历史
     *
     * @param id 编号
     * @return 卷宗材料标注历史
     */
    MaterialMarkerHistoryDO getMaterialMarkerHistory(String id);

    /**
    * 获得卷宗材料标注历史分页
    *
    * @param pageReqVO 分页查询
    * @return 卷宗材料标注历史分页
    */
    PageResult<MaterialMarkerHistoryDO> getMaterialMarkerHistoryPage(MaterialMarkerHistoryPageReqVO pageReqVO);

    /**
    * 获得卷宗材料标注历史列表
    *
    * @param listReqVO 查询条件
    * @return 卷宗材料标注历史列表
    */
    List<MaterialMarkerHistoryDO> getMaterialMarkerHistoryList(MaterialMarkerHistoryListReqVO listReqVO);


}
