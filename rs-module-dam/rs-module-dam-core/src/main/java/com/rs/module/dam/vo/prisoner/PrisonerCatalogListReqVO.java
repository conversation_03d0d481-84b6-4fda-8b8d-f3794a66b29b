package com.rs.module.dam.vo.prisoner;

import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 监管人员卷宗目录列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = false)
public class PrisonerCatalogListReqVO extends BaseVO {
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("目录模板Id")
    private String catalogTemplateId;

    @ApiModelProperty("是否改变文书名称(1：是，0：否)")
    private String isChangeFileName;

    @ApiModelProperty("编目目录数据")
    private String bmCatalogData;

    @ApiModelProperty("组卷目录数据")
    private String zjCatalogData;

    @ApiModelProperty("快速组卷目录数据")
    private String kszjCatalogData;

    @ApiModelProperty("汇总目录数据")
    private String hzCatalogData;

}
