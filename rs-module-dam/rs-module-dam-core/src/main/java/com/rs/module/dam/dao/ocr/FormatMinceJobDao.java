package com.rs.module.dam.dao.ocr;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.ocr.FormatMinceJobDO;
import com.rs.module.dam.vo.ocr.FormatMinceJobListReqVO;
import com.rs.module.dam.vo.ocr.FormatMinceJobPageReqVO;

/**
* ocr格式化审查任务细分 Dao
*
* <AUTHOR>
*/
@Mapper
public interface FormatMinceJobDao extends IBaseDao<FormatMinceJobDO> {


    default PageResult<FormatMinceJobDO> selectPage(FormatMinceJobPageReqVO reqVO) {
        Page<FormatMinceJobDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<FormatMinceJobDO> wrapper = new LambdaQueryWrapperX<FormatMinceJobDO>()
            .eqIfPresent(FormatMinceJobDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(FormatMinceJobDO::getJobId, reqVO.getJobId())
            .eqIfPresent(FormatMinceJobDO::getCheckType, reqVO.getCheckType())
            .eqIfPresent(FormatMinceJobDO::getJobType, reqVO.getJobType())
            .eqIfPresent(FormatMinceJobDO::getJobStatus, reqVO.getJobStatus())
            .eqIfPresent(FormatMinceJobDO::getShowJob, reqVO.getShowJob())
            .betweenIfPresent(FormatMinceJobDO::getStartTime, reqVO.getStartTime())
            .betweenIfPresent(FormatMinceJobDO::getEndTime, reqVO.getEndTime())
            .eqIfPresent(FormatMinceJobDO::getCheckFiles, reqVO.getCheckFiles())
            .eqIfPresent(FormatMinceJobDO::getBaseFiles, reqVO.getBaseFiles())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(FormatMinceJobDO::getAddTime);
        }
        Page<FormatMinceJobDO> formatMinceJobPage = selectPage(page, wrapper);
        return new PageResult<>(formatMinceJobPage.getRecords(), formatMinceJobPage.getTotal());
    }
    default List<FormatMinceJobDO> selectList(FormatMinceJobListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<FormatMinceJobDO>()
            .eqIfPresent(FormatMinceJobDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(FormatMinceJobDO::getJobId, reqVO.getJobId())
            .eqIfPresent(FormatMinceJobDO::getCheckType, reqVO.getCheckType())
            .eqIfPresent(FormatMinceJobDO::getJobType, reqVO.getJobType())
            .eqIfPresent(FormatMinceJobDO::getJobStatus, reqVO.getJobStatus())
            .eqIfPresent(FormatMinceJobDO::getShowJob, reqVO.getShowJob())
            .betweenIfPresent(FormatMinceJobDO::getStartTime, reqVO.getStartTime())
            .betweenIfPresent(FormatMinceJobDO::getEndTime, reqVO.getEndTime())
            .eqIfPresent(FormatMinceJobDO::getCheckFiles, reqVO.getCheckFiles())
            .eqIfPresent(FormatMinceJobDO::getBaseFiles, reqVO.getBaseFiles())
        .orderByDesc(FormatMinceJobDO::getAddTime));
    }

    /**
     * 查询格式化审查检查任务列表
     * @param queryParam Map<String, Object> 查询参数
     * @param pageInfo age<Map<String, Object>> 分页参数
     * @return List<Map<String, Object>>
     */
    List<Map<String, Object>> selectMinceJobList(@Param("cm") Map<String, String> queryParam, Page<Map<String, Object>> pageInfo);
    
    /**
     * 更新格式化审查子任务结束时间
     * @param jobId String 任务Id
     */
    void updateMinceJobEndTime(String jobId);
}
