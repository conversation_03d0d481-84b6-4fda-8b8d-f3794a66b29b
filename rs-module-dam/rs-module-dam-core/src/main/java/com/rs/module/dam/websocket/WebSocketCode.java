package com.rs.module.dam.websocket;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * WebSocket消息代码枚举
 * 前端页面根据消息code区分业务，消息内容用json数据传输。例:{'code':'3001',msg:'编目结果返回，刷新页面','jgrybm':'xxxxxx'}
 * <AUTHOR>
 * @date 2025年4月22日
 */
@Getter
@AllArgsConstructor
public enum WebSocketCode {

	/** 编目组卷步骤枚举 */
	RESULT_BM("3001", "编目结果返回，刷新页面"),
	RESULT_ZJ_PDF("3002", "组卷完成，PDF生成结束"),
	RESULT_YS_PDF("3003", "移送组卷完成，PDF生成结束"),
	SCAN_UPLOAD_FINISH("3004", "扫描上传完成"),
	OPEN_BASE_OCR_FAIL("3010", "开启智能编目失败"),


	/** 格式化审查枚举 */
	INVESTIGATE("3100", "签名签章捺印检测"),
	BLOCK_CHAIN_UPLOAD("3400", "归档上链进度"),
	BLOCK_CHAIN_FILE_CHANGE("3401", "返回被篡改文件"),
	CHECK_FORMAT_DATA_PROCESS("3500", "返回智能格式化审查进度"),

	/** pdf转换枚举 */
	PDF_CONVERTED("4000", "获取pdf转换进度");
	
	//代码
	private String code;
	
	//消息
	private String msg;
}
