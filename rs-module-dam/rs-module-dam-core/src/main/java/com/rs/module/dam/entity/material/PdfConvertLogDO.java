package com.rs.module.dam.entity.material;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * pdf转换日志 DO
 *
 * <AUTHOR>
 */
@TableName("dam_pdf_convert_log")
@KeySequence("dam_pdf_convert_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PdfConvertLogDO {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 推送数据
     */
    private String pushData;
    /**
     * 异常日志
     */
    private String errorLog;
    /**
     * 添加时间
     */
    private Date addTime;
}
