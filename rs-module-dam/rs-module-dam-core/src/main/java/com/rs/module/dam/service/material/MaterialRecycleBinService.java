package com.rs.module.dam.service.material;

import java.util.List;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.material.MaterialRecycleBinDO;
import com.rs.module.dam.vo.material.MaterialRecycleBinListReqVO;
import com.rs.module.dam.vo.material.MaterialRecycleBinPageReqVO;
import com.rs.module.dam.vo.material.MaterialRecycleBinSaveReqVO;

/**
 * 卷宗材料回收 Service 接口
 *
 * <AUTHOR>
 */
public interface MaterialRecycleBinService extends IBaseService<MaterialRecycleBinDO>{

    /**
     * 创建卷宗材料回收
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMaterialRecycleBin(@Valid MaterialRecycleBinSaveReqVO createReqVO);

    /**
     * 更新卷宗材料回收
     *
     * @param updateReqVO 更新信息
     */
    void updateMaterialRecycleBin(@Valid MaterialRecycleBinSaveReqVO updateReqVO);

    /**
     * 删除卷宗材料回收
     *
     * @param id 编号
     */
    void deleteMaterialRecycleBin(String id);

    /**
     * 获得卷宗材料回收
     *
     * @param id 编号
     * @return 卷宗材料回收
     */
    MaterialRecycleBinDO getMaterialRecycleBin(String id);

    /**
    * 获得卷宗材料回收分页
    *
    * @param pageReqVO 分页查询
    * @return 卷宗材料回收分页
    */
    PageResult<MaterialRecycleBinDO> getMaterialRecycleBinPage(MaterialRecycleBinPageReqVO pageReqVO);

    /**
    * 获得卷宗材料回收列表
    *
    * @param listReqVO 查询条件
    * @return 卷宗材料回收列表
    */
    List<MaterialRecycleBinDO> getMaterialRecycleBinList(MaterialRecycleBinListReqVO listReqVO);

    /**
     * 更新材料Id
     * @param originalImageIds List<String> 图片Id集合
     * @param materialInfoId String 材料Id
     * @param catalogId String 目录Id
     * @param partCatalogId String 分卷目录Id
     */
    void updateMaterialId(List<String> originalImageIds, String materialInfoId,
    		String catalogId, String partCatalogId);
    
    /**
     * 根据监管人员编码获得卷宗材料回收列表
     * @param jgrybm String 监管人员编码
     * @return List<MaterialRecycleBinDO>
     */
    List<MaterialRecycleBinDO> getMaterialRecycleBinByJgrybm(String jgrybm);
}
