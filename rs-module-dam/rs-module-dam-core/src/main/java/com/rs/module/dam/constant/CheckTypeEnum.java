package com.rs.module.dam.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 检测类型枚举类
 * <AUTHOR>
 * @date 2025年4月17日
 */
@AllArgsConstructor
@Getter
public enum CheckTypeEnum {
	
	SEAL("公章", "1", "sealProcess"),
	
	SIGNATURE("签名", "2", "signatureProcess"),
	
	FINGERPRINT("捺印", "3", "fingerprintProcess"),
	
	BLANK_SPACE("空白页", "4", "blankProcess"),
	
	REPEAT_PAGE("重复页", "5", "repeatProcess"),
	
	FLAW("瑕疵页", "6", "flawProcess"),
	
	INFO_EXTRACT("文书规范", "7", "extractProcess");
	
	//检测类型名称
	private String name;

	//检测类型代码
	private String code;
	
	//检测进度标识
	private String process;
	
	/**
	 * 根据代码获取检测类型
	 * @param code String 检测类型代码
	 * @return CheckTypeEnum
	 */
	public static CheckTypeEnum getByType(String code) {
		for(CheckTypeEnum checkType : values()) {
			if(checkType.getCode().equals(code)) {
				return checkType;
			}
		}
		return null;
	}
}
