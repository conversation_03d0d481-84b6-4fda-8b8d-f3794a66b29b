package com.rs.module.dam.vo.ocr;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel(description = "管理后台 - ocr相似度归目标识分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CatalogMarkPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("材料Id")
    private String materialId;

    @ApiModelProperty("目录Id")
    private String catalogId;

    @ApiModelProperty("目录名称")
    private String catalogName;

    @ApiModelProperty("分卷目录Id")
    private String partCatalogId;

    @ApiModelProperty("识别材料名称")
    private String name;

    @ApiModelProperty("相似度")
    private String familiarity;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
