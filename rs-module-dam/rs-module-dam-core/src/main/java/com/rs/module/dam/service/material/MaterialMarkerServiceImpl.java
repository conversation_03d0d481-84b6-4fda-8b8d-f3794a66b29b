package com.rs.module.dam.service.material;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.dao.material.MaterialMarkerDao;
import com.rs.module.dam.entity.material.MaterialMarkerDO;
import com.rs.module.dam.vo.material.MaterialMarkerListReqVO;
import com.rs.module.dam.vo.material.MaterialMarkerPageReqVO;
import com.rs.module.dam.vo.material.MaterialMarkerSaveReqVO;


/**
 * 卷宗材料标注 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MaterialMarkerServiceImpl extends BaseServiceImpl<MaterialMarkerDao, MaterialMarkerDO> implements MaterialMarkerService {

    @Resource
    private MaterialMarkerDao materialMarkerDao;

    @Override
    public String createMaterialMarker(MaterialMarkerSaveReqVO createReqVO) {
        // 插入
        MaterialMarkerDO materialMarker = BeanUtils.toBean(createReqVO, MaterialMarkerDO.class);
        materialMarkerDao.insert(materialMarker);
        // 返回
        return materialMarker.getId();
    }

    @Override
    public void updateMaterialMarker(MaterialMarkerSaveReqVO updateReqVO) {
        // 校验存在
        validateMaterialMarkerExists(updateReqVO.getId());
        // 更新
        MaterialMarkerDO updateObj = BeanUtils.toBean(updateReqVO, MaterialMarkerDO.class);
        materialMarkerDao.updateById(updateObj);
    }

    @Override
    public void deleteMaterialMarker(String id) {
        // 校验存在
        validateMaterialMarkerExists(id);
        // 删除
        materialMarkerDao.deleteById(id);
    }

    private void validateMaterialMarkerExists(String id) {
        if (materialMarkerDao.selectById(id) == null) {
            throw new ServerException("卷宗材料标注数据不存在");
        }
    }

    @Override
    public MaterialMarkerDO getMaterialMarker(String id) {
        return materialMarkerDao.selectById(id);
    }

    @Override
    public PageResult<MaterialMarkerDO> getMaterialMarkerPage(MaterialMarkerPageReqVO pageReqVO) {
        return materialMarkerDao.selectPage(pageReqVO);
    }

    @Override
    public List<MaterialMarkerDO> getMaterialMarkerList(MaterialMarkerListReqVO listReqVO) {
        return materialMarkerDao.selectList(listReqVO);
    }

    /**
     * 获取监管人员标注过的材料文件Id 
     * @param jgrybm String 监管人员编码
     * @return List<String>
     */
    @Override
    public List<String> selectMarkerFileIdByJgrybm(String jgrybm){
    	return materialMarkerDao.selectMarkerFileIdByJgrybm(jgrybm);
    }
}
