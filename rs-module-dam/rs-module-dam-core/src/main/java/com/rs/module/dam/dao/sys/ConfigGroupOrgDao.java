package com.rs.module.dam.dao.sys;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.sys.ConfigGroupOrgDO;

/**
 * 卷宗配置机构关联
 *
 * <AUTHOR>
 */
@Mapper
public interface ConfigGroupOrgDao extends IBaseDao<ConfigGroupOrgDO> {

    default List<ConfigGroupOrgDO> selectListByGroupId(String groupId) {
        return selectList(new LambdaQueryWrapperX<ConfigGroupOrgDO>().eq(ConfigGroupOrgDO::getGroupId, groupId));
    }

    default int deleteByGroupId(String groupId) {
        return delete(new LambdaQueryWrapperX<ConfigGroupOrgDO>().eq(ConfigGroupOrgDO::getGroupId, groupId));
    }

}
