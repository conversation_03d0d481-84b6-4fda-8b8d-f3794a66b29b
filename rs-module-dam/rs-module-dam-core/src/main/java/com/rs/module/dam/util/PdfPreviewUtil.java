package com.rs.module.dam.util;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.itextpdf.kernel.events.PdfDocumentEvent;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.rs.module.dam.constant.DamConstants;
import com.rs.module.dam.entity.archive.ArchiveCoverDO;
import com.rs.module.dam.entity.material.MaterialInfoDO;

import lombok.extern.slf4j.Slf4j;

/**
 * pdf预览工具类
 * <AUTHOR>
 * @date 2025年4月25日
 */
@Slf4j
public class PdfPreviewUtil {

	/**
	 * 异步生成分卷pdf(预览)
	 * @param index int 索引
	 * @param fjsl int 附卷数量
	 * @param archiveCover ArchiveCoverDO 卷宗封面
	 * @param materialList List<MaterialInfoDO> 分卷材料
	 * @param isAddWatermarking boolean 是否添加水印
	 * @param zjType String 组卷类型
	 * @param user SessionUser 当前用户
	 * @param callback CallBackFunction 回调函数
	 */
	public static void executeAsyncForPartPdf(int index, int fjsl, ArchiveCoverDO archiveCover,
			List<MaterialInfoDO> materialList, boolean isAddWatermarking, String zjType,
			SessionUser user, CallBackFunction callback) {
		ThreadPoolUtil.getPool().execute(new Runnable() {
			
			@Override
			public void run() {
				try {
					JSONObject json = new JSONObject();
					
					//获取所有材料的url地址
					List<String> urlList = MaterialUtil.getFileUrlList(materialList);
					
					//编目组卷
		       		if(DamConstants.ZJ_TYPE_BMZJ.equals(zjType)) {
		       			String fileName = generatePdf(index, fjsl, archiveCover, materialList, isAddWatermarking, zjType, user);
		       			String path = ObjectUtil.formatFilePath(PdfGenerateUtil.generateTempPath() + fileName + "_yl" + ".pdf");
	                    log.info("【pdf预览生成】生成案卷封面pdf的路径:{}", path);
	                    
	                    File file  = new File(path);
	                    try {
	                    	List<String> allList = PdfGenerateUtil.pdfToImage(file, archiveCover.getPartCatalogId());
	                    	allList.addAll(allList.size() - 2, urlList);
	                    	json.put("pdfImageUrl", allList);
	    	       			callback.execute(json);
	                    }
	                    catch(Exception e) {
	                    	log.error("【pdf预览生成】发生异常，原因：", e);
	                    	callback.execute(new JSONObject());
	                    }
	                    finally {
	                    	file.delete();
	                    }
		       		}
		       		 
		       		//快速组卷
		       		else {
		       			json.put("pdfImageUrl", urlList);
		       			callback.execute(json);
		       		}
				}
				catch(Exception e) {
					
				}
			}
		});
	}
	
	/**
	 * 生成pdf
	 * @param index int 索引
	 * @param fjsl int
	 * @param archiveCover ArchiveCoverDO 卷宗封面
	 * @param materialList List<MaterialInfoDO> 分卷材料
	 * @param isAddWatermarking boolean 是否添加水印
	 * @param zjType String 组卷类型
	 * @param user SessionUser 当前用户
	 * @param callback CallBackFunction 回调函数
	 * @return String
	 */
	public static String generatePdf(int index, int fjsl, ArchiveCoverDO archiveCover,
			List<MaterialInfoDO> materialList, boolean isAddWatermarking, String zjType, SessionUser user) {
		PdfWriter writer = null;
		Document document = null;
		String fileName = StringUtil.getGuid32();
		String path = ObjectUtil.formatFilePath(PdfGenerateUtil.generateTempPath() + fileName + "_yl" + ".pdf");
		File file = new File(path);
		FileOutputStream fos = null;
		
		try {
			fos = new FileOutputStream(file);
			writer = new PdfWriter(fos);
			PdfDocument pdf = new PdfDocument(writer);
			document = new Document(pdf, PageSize.A4);
			
			//添加水印
			if (isAddWatermarking) {
				pdf.addEventHandler(PdfDocumentEvent.END_PAGE, new WatermarkingEventHandler(user));
			}

			//编目组卷
			if (DamConstants.ZJ_TYPE_BMZJ.equals(zjType)) {
				
				//添加封面
				PdfGenerateUtil.addCover(document, archiveCover);
				
				// 目录
				pdf.addNewPage(PageSize.A4);
				PdfGenerateUtil.addCatalogDocument(pdf, document, materialList);
				
				//保存封面和目录图片
				pdf.getPage(1);
				
				//添加卷内备考表
				PdfGenerateUtil.addJnbkbAndFd(document);
			}
		}
		catch (Exception e) {
			log.error("【pdf预览生成】发生异常，原因：", e);
			file.delete();
		}
		finally {
			if (document != null) {
				try {
					document.close();
				}
				catch (Exception e) {
					log.error("【pdf预览生成】发生异常，原因：", e);
				}
			}
			if(fos != null){
				try {
					fos.close();
				}
				catch (IOException e) {
					log.error("【pdf预览生成】发生异常，原因：", e);
				}
			}
		}
		
		return fileName;
	}
}
