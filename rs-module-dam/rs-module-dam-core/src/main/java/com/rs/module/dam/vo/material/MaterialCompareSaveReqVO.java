package com.rs.module.dam.vo.material;

import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 卷宗材料比对新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MaterialCompareSaveReqVO extends BaseVO{
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("左边材料文件图片ID")
    private String leftFileId;

    @ApiModelProperty("右边材料文件图片ID")
    private String rightFileId;

    @ApiModelProperty("左边材料ID")
    private String leftMaterialId;

    @ApiModelProperty("右边材料ID")
    private String rightMaterialId;

    @ApiModelProperty("比对图片url")
    private String url;

    @ApiModelProperty("备注")
    private String remark;

}
