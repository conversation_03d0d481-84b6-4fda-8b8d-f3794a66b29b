package com.rs.module.dam.service.ocr;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.ocr.FormatMinceJobDO;
import com.rs.module.dam.vo.ocr.FormatMinceJobListReqVO;
import com.rs.module.dam.vo.ocr.FormatMinceJobPageReqVO;
import com.rs.module.dam.vo.ocr.FormatMinceJobSaveReqVO;

/**
 * ocr格式化审查任务细分 Service 接口
 *
 * <AUTHOR>
 */
public interface FormatMinceJobService extends IBaseService<FormatMinceJobDO>{

    /**
     * 创建ocr格式化审查任务细分
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createFormatMinceJob(@Valid FormatMinceJobSaveReqVO createReqVO);

    /**
     * 更新ocr格式化审查任务细分
     *
     * @param updateReqVO 更新信息
     */
    void updateFormatMinceJob(@Valid FormatMinceJobSaveReqVO updateReqVO);

    /**
     * 删除ocr格式化审查任务细分
     *
     * @param id 编号
     */
    void deleteFormatMinceJob(String id);

    /**
     * 获得ocr格式化审查任务细分
     *
     * @param id 编号
     * @return ocr格式化审查任务细分
     */
    FormatMinceJobDO getFormatMinceJob(String id);

    /**
    * 获得ocr格式化审查任务细分分页
    *
    * @param pageReqVO 分页查询
    * @return ocr格式化审查任务细分分页
    */
    PageResult<FormatMinceJobDO> getFormatMinceJobPage(FormatMinceJobPageReqVO pageReqVO);

    /**
    * 获得ocr格式化审查任务细分列表
    *
    * @param listReqVO 查询条件
    * @return ocr格式化审查任务细分列表
    */
    List<FormatMinceJobDO> getFormatMinceJobList(FormatMinceJobListReqVO listReqVO);

    /**
     * 查询格式化审查检查任务列表
     * @param queryParam Map<String, String> 查询参数
     * @param page Integer 当前第几页
     * @param pageSize Integer 每页大小
     * @return List<Map<String, Object>>
     */
    public Page<Map<String, Object>> selectMinceJobList(Map<String, String> queryParam, Integer page, Integer pageSize);
    
    /**
     * 更新格式化审查子任务结束时间
     * @param jobId String 任务Id
     */
    public void updateMinceJobEndTime(String jobId);
}
