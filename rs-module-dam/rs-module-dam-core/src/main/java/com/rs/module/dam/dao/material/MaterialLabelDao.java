package com.rs.module.dam.dao.material;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.material.MaterialLabelDO;
import com.rs.module.dam.vo.material.MaterialLabelListReqVO;
import com.rs.module.dam.vo.material.MaterialLabelPageReqVO;

/**
* 卷宗材料标签 Dao
*
* <AUTHOR>
*/
@Mapper
public interface MaterialLabelDao extends IBaseDao<MaterialLabelDO> {


    default PageResult<MaterialLabelDO> selectPage(MaterialLabelPageReqVO reqVO) {
        Page<MaterialLabelDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<MaterialLabelDO> wrapper = new LambdaQueryWrapperX<MaterialLabelDO>()
            .eqIfPresent(MaterialLabelDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(MaterialLabelDO::getMaterialId, reqVO.getMaterialId())
            .eqIfPresent(MaterialLabelDO::getCatalogId, reqVO.getCatalogId())
            .eqIfPresent(MaterialLabelDO::getFileId, reqVO.getFileId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(MaterialLabelDO::getAddTime);
        }
        Page<MaterialLabelDO> materialLabelPage = selectPage(page, wrapper);
        return new PageResult<>(materialLabelPage.getRecords(), materialLabelPage.getTotal());
    }
    default List<MaterialLabelDO> selectList(MaterialLabelListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MaterialLabelDO>()
            .eqIfPresent(MaterialLabelDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(MaterialLabelDO::getMaterialId, reqVO.getMaterialId())
            .eqIfPresent(MaterialLabelDO::getCatalogId, reqVO.getCatalogId())
            .eqIfPresent(MaterialLabelDO::getFileId, reqVO.getFileId())
        .orderByDesc(MaterialLabelDO::getAddTime));    }


    }
