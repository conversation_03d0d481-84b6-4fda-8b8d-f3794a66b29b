package com.rs.module.dam.service.material;

import java.util.List;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.entity.material.MaterialLabelDO;
import com.rs.module.dam.vo.material.MaterialLabelListReqVO;
import com.rs.module.dam.vo.material.MaterialLabelPageReqVO;
import com.rs.module.dam.vo.material.MaterialLabelSaveReqVO;

/**
 * 卷宗材料标签 Service 接口
 *
 * <AUTHOR>
 */
public interface MaterialLabelService extends IBaseService<MaterialLabelDO>{

    /**
     * 创建卷宗材料标签
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMaterialLabel(@Valid MaterialLabelSaveReqVO createReqVO);

    /**
     * 更新卷宗材料标签
     *
     * @param updateReqVO 更新信息
     */
    void updateMaterialLabel(@Valid MaterialLabelSaveReqVO updateReqVO);

    /**
     * 删除卷宗材料标签
     *
     * @param id 编号
     */
    void deleteMaterialLabel(String id);

    /**
     * 获得卷宗材料标签
     *
     * @param id 编号
     * @return 卷宗材料标签
     */
    MaterialLabelDO getMaterialLabel(String id);

    /**
    * 获得卷宗材料标签分页
    *
    * @param pageReqVO 分页查询
    * @return 卷宗材料标签分页
    */
    PageResult<MaterialLabelDO> getMaterialLabelPage(MaterialLabelPageReqVO pageReqVO);

    /**
    * 获得卷宗材料标签列表
    *
    * @param listReqVO 查询条件
    * @return 卷宗材料标签列表
    */
    List<MaterialLabelDO> getMaterialLabelList(MaterialLabelListReqVO listReqVO);

    /**
     * 获取用户材料标注列表
     * @param materialId String 材料Id
     * @param fileId String 文件Id
     * @return List<MaterialLabelDO>
     */
    public List<MaterialLabelDO> getUserMaterialLabelList(String materialId, String fileId);
}
