package com.rs.module.dam.util;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URL;
import java.util.List;

import javax.imageio.ImageIO;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.itextpdf.io.image.ImageData;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.events.PdfDocumentEvent;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.property.AreaBreakType;
import com.rs.module.dam.constant.DamConstants;
import com.rs.module.dam.entity.archive.ArchiveCoverDO;
import com.rs.module.dam.entity.material.MaterialInfoDO;

import lombok.extern.slf4j.Slf4j;

/**
 * pdf导出工具类
 * <AUTHOR>
 * @date 2025年4月25日
 */
@Slf4j
public class PdfExportUtil {

	/**
	 * 异步生成分卷pdf
	 * @param index int 索引
	 * @param fjsl int 附卷数量
	 * @param archiveCover ArchiveCoverDO 卷宗封面
	 * @param materialList List<MaterialInfoDO> 分卷材料
	 * @param isAddWatermarking boolean 是否添加水印
	 * @param labelType String 标签类型
	 * @param user SessionUser 当前用户
	 * @param callback CallBackFunction 回调函数
	 */
	public static void executeAsyncForPartPdf(int index, int fjsl, ArchiveCoverDO archiveCover,
			List<MaterialInfoDO> materialList, boolean isAddWatermarking, String labelType,
			SessionUser user, CallBackFunction callback) {
		ThreadPoolUtil.getPool().execute(new Runnable() {
			
			@Override
			public void run() {
				try {
					String fileName = generatePdf(index, fjsl, archiveCover, materialList, isAddWatermarking, labelType, user);
	       			String path = ObjectUtil.formatFilePath(PdfGenerateUtil.generateTempPath() + fileName + ".pdf");
	       			File file  = new File(path);
	       			
	       			try {
	       				JSONObject json = PdfGenerateUtil.upload(file, null);
	       				callback.execute(json);
	       			}
	       			catch(Exception e) {
	       				log.error("【pdf导出】发生异常，原因：", e);
	       				callback.execute(new JSONObject());
	       			}
	       			finally {
	       				file.delete();
	       			}
				}
				catch(Exception e) {
					log.error("【pdf导出】发生异常，原因：", e);
				}
			}
		});
	}
	
	/**
	 * 生成pdf
	 * @param index int 索引
	 * @param fjsl int 附卷数量
	 * @param archiveCover ArchiveCoverDO 卷宗封面
	 * @param materialList List<MaterialInfoDO> 分卷材料
	 * @param isAddWatermarking boolean 是否添加水印
	 * @param labelType String 标签类型
	 * @param user SessionUser 当前用户
	 * @return String
	 */
	public static String generatePdf(int index, int fjsl, ArchiveCoverDO archiveCover,
			List<MaterialInfoDO> materialList, boolean isAddWatermarking, String labelType,
			SessionUser user) {
		PdfWriter writer = null;
		Document document = null;
		String fileName = StringUtil.getGuid32();
		String path = ObjectUtil.formatFilePath(PdfGenerateUtil.generateTempPath() + fileName + ".pdf");
		File file = new File(path);
		FileOutputStream fos = null;
		
		try {
			fos = new FileOutputStream(file);
			writer = new PdfWriter(fos);
			PdfDocument pdf = new PdfDocument(writer);
			document = new Document(pdf, PageSize.A4);
			
			//添加水印
			if (isAddWatermarking) {
				pdf.addEventHandler(PdfDocumentEvent.END_PAGE, new WatermarkingEventHandler(user));
			}
			
			//组卷类型和页数
			String zjType = archiveCover.getFjlx();
			int pages = pdf.getNumberOfPages();
			
			//设置页码
			TextFooterEventHandler tfeh = new TextFooterEventHandler(document);
			tfeh.setName(archiveCover.getJm());
			pdf.addEventHandler(PdfDocumentEvent.END_PAGE, tfeh);

			//编目组卷
			if (DamConstants.ZJ_TYPE_BMZJ.equals(zjType)) {
				
				//添加封面
				PdfGenerateUtil.addCover(document, archiveCover);
				
				//目录
				pdf.addNewPage(PageSize.A4);
				PdfGenerateUtil.addCatalogDocument(pdf, document, materialList);
				
				//起始页号
				tfeh.setStartPage(pdf.getNumberOfPages());
			}
			
			//循环加载图片页
			for(MaterialInfoDO material : materialList) {
				String fileStr = material.getMaterialFile();
                JSONArray filesArr = JSONArray.parseArray(fileStr);
                for (int a = 0; a < filesArr.size(); a ++) {
                    JSONObject obj = filesArr.getJSONObject(a);
                    log.info("【pdf导出】pdf当前页数：" + pdf.getNumberOfPages());
                    if(pdf.getNumberOfPages() >= 1){
                        document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                    }
                    
                    try {
                    	//生成图片数据                        
                        ImageData imageData = null;
                        String url = obj.getString("url");
                        if(url.toLowerCase().contains(".webp")) {
                        	ByteArrayOutputStream baos = null;
                        	
                        	try {
                        		URL imageUrl = new URL(url);
                        		BufferedImage reader = ImageIO.read(imageUrl);
                            	baos = new ByteArrayOutputStream();
                            	ImageIO.write(reader, "jpg", baos);
                            	imageData = ImageDataFactory.create(baos.toByteArray());
                        	}
                        	catch(Exception e) {
                        		log.info("【pdf导出】发生异常：" + e.getMessage());
                        		throw e;
                        	}
                        	finally {
                        		if(baos != null) {
                        			baos.close();
                        		}
                        	}
                        }
                        else {
                        	imageData = ImageDataFactory.create(new String(url.getBytes("utf-8")));
                        }
                        
                        //添加图片内容
                        document.add(new Image(imageData).setAutoScaleWidth(true));
                    }
                    catch (Exception e) {
                    	log.info("【pdf导出】发生异常：" + e.getMessage());
                        document.add(new Paragraph("该图片文件不能加载"));
                    }
                }
			}
			
			tfeh.setEndPage(pdf.getNumberOfPages() - pages + 2);
			if (DamConstants.ZJ_TYPE_BMZJ.equals(zjType)) {
				PdfGenerateUtil.addJnbkbAndFd(document);
			}
		}
		catch (Exception e) {
			log.error("【pdf导出】发生异常，原因：", e);
			file.delete();
		}
		finally {
			if (document != null) {
				try {
					document.close();
				}
				catch (Exception e) {
					log.error("【pdf导出】发生异常，原因：", e);
				}
			}
			if(fos != null){
				try {
					fos.close();
				}
				catch (IOException e) {
					log.error("【pdf导出】发生异常，原因：", e);
				}
			}
		}
		
		return fileName;
	}
}
