package com.rs.module.dam.entity.prisoner;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 卷宗监管人员 DO
 *
 * <AUTHOR>
 */
@TableName("dam_prisoner_info")
@KeySequence("dam_prisoner_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrisonerInfoDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 卷宗状态（01：已登记；02：已组卷；03：已归档）
     */
    private String jzzt;
    /**
     * 档案编号
     */
    private String dabh;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 姓名
     */
    private String xm;
    /**
     * 涉嫌罪名
     */
    private String sxzm;
    /**
     * 是否未成年人档案
     */
    private String sfwcnrda;
    /**
     * 案件编号
     */
    private String ajbh;
    /**
     * 案件名称
     */
    private String ajmc;
    /**
     * 监室号
     */
    private String jsh;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 卷宗借阅次数
     */
    private Short jycs;
    /**
     * 归档时间
     */
    private Date gdsj;
    /**
     * 归档人
     */
    private String gdUser;
    /**
     * 归档人姓名
     */
    private String gdUserName;
    /**
     * 归档人机构编号
     */
    private String gdOrgCode;
    /**
     * 归档人机构名称
     */
    private String gdOrgName;
    /**
     * 组卷时间
     */
    private Date zjsj;
    /**
     * 组卷人
     */
    private String zjUser;
    /**
     * 组卷人姓名
     */
    private String zjUserName;
    /**
     * 组卷人机构编号
     */
    private String zjOrgCode;
    /**
     * 组卷人机构名称
     */
    private String zjOrgName;

}
