package com.rs.module.dam.ocr.gosuncn.callback;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.rs.module.dam.constant.CheckTypeEnum;
import com.rs.module.dam.constant.DamConstants;
import com.rs.module.dam.constant.OcrConstants;
import com.rs.module.dam.entity.log.IspInvokingDO;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.entity.ocr.FormatCheckJobDO;
import com.rs.module.dam.entity.ocr.FormatCheckResultDO;
import com.rs.module.dam.entity.ocr.FormatMinceJobDO;
import com.rs.module.dam.entity.prisoner.PrisonerCatalogDO;
import com.rs.module.dam.entity.sys.ConfigGroupDO;
import com.rs.module.dam.ocr.gosuncn.GoOcrCallbackParam;
import com.rs.module.dam.ocr.gosuncn.GoOcrCallbackParam.SerDatas;
import com.rs.module.dam.ocr.gosuncn.GoOcrHttpStatus;
import com.rs.module.dam.ocr.gosuncn.GoUtil;
import com.rs.module.dam.ocr.handler.OcrHandler;
import com.rs.module.dam.ocr.util.OcrUtil;
import com.rs.module.dam.service.log.IspInvokingService;
import com.rs.module.dam.service.material.MaterialInfoService;
import com.rs.module.dam.service.ocr.FormatCheckJobService;
import com.rs.module.dam.service.ocr.FormatCheckResultService;
import com.rs.module.dam.service.ocr.FormatMinceJobService;
import com.rs.module.dam.service.ocr.TFormatCheckService;
import com.rs.module.dam.service.sys.ConfigGroupService;
import com.rs.module.dam.util.IpUtil;
import com.rs.module.dam.util.PicUtil;
import com.rs.module.dam.util.RedisTemplateUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 签名、签章和捺印审查回调接口
 * <AUTHOR>
 * @date 2025年4月27日
 */
@Component
@Slf4j
public class QmqznyCallback implements IOcrCallback {
	
	@Autowired
    private ConfigGroupService configGroupService;
	
	@Autowired
	private IspInvokingService ispInvokingService;
	
	@Autowired
	private MaterialInfoService dossierMaterialInfoService;
	
	@Autowired
	private FormatCheckJobService checkJobService;
	
	@Autowired
	private FormatMinceJobService minceJobService;
	
	@Autowired
	private FormatCheckResultService checkResultService;
	
	@Autowired
	private TFormatCheckService tFormatCheckService;
    
    @Autowired
    OcrHandler ocrHandler;
    
    @Autowired
    RedisTemplateUtil redisTemplateUtil;
    
	/**
	 * 回调处理
	 * @param params GoOcrCallbackParam 回调参数
	 */
	@Override
	public void handle(GoOcrCallbackParam params) {
		
		//服务返回数据
		SerDatas serDatas = params.getSerDatas();
		
		//构建isp调用对象
		IspInvokingDO ispInvoking = new IspInvokingDO();
		ispInvoking.setId(StringUtil.getGuid32());
		ispInvoking.setJobId(serDatas.getBatchId());
		ispInvoking.setInput(JSON.toJSONString(params));
		ispInvoking.setSerCode(params.getSerCode());
		ispInvoking.setInvokeType(OcrConstants.OCR_INVOKE_TYPE_CHECK_QMQZNY_CALLBACK);
		ispInvoking.setRequestFrom(OcrConstants.OCR_REQUEST_FROM_FORMAT_CHECK);
		ispInvoking.setServerIp(IpUtil.getHostIp());
		ispInvoking.setCheckType(OcrConstants.OCR_INVOKE_TYPE_CHECK_QMQZNY_CALLBACK);
		
		//格式化审查任务
		FormatCheckJobDO checkJob = null;
		
		//所有子任务
		List<FormatMinceJobDO> allMinceJobList = null;
		
		//公章、签名和捺印审查子任务
		List<FormatMinceJobDO> qmqznyMinceJobList = new ArrayList<>();
		
		//任务是否完成(主任务以及公章、签名和捺印审查子任务)
		boolean isFinished = false;
		boolean sealFinished = true;
		boolean signatureFinished = true;
		boolean fingerPrintFinished = true;
		
		//开始调用时间
		long begin = System.currentTimeMillis();
		
		try {
			//回调返回结果
			JSONArray callbackResults = GoUtil.getOcrResultsArray(params.getSerDatas());
			
			//任务Id = 批次Id
			String jobId = serDatas.getBatchId();
			
			//格式化审查任务
			checkJob = checkJobService.getById(jobId);
			
			//设置isp调用对象
			ispInvoking.setJgrybm(checkJob.getJgrybm());
			
			//获取所有的检测子任务
			allMinceJobList = tFormatCheckService.getCheckedMinceJobList(checkJob.getId());
			
			//返回数据合法
			if((!callbackResults.isEmpty() || !serDatas.getFailed_ids().isEmpty())
					&& (GoOcrHttpStatus.OK.getCode() == serDatas.getCode() || GoOcrHttpStatus.INCOMPLETE_RETURN.getCode() == serDatas.getCode())) {
				
				//查询卷宗配置信息
				ConfigGroupDO dmsConfigGroup = configGroupService.getUserConfigGroup(checkJob.getOrgCode());
				
				//设置isp调用对象
				ispInvoking.setAddress(dmsConfigGroup.getOcrServer());
				
				//签章、签名和捺印已处理的文件列表
				List<String> sealFileIdList = new ArrayList<>();
				List<String> signatureFileIdList = new ArrayList<>();
				List<String> fingerPrintFileIdList = new ArrayList<>();
				
				//签章审查任务
				FormatMinceJobDO sealJob = OcrUtil.getMinceJobByCheckType(allMinceJobList, CheckTypeEnum.SEAL.getCode());
				if(sealJob != null) {
					qmqznyMinceJobList.add(sealJob);
					sealFileIdList = tFormatCheckService.getPrisonerCheckResultFileIdList(checkJob.getJgrybm(), checkJob.getJobType(), CheckTypeEnum.SEAL.getCode());
				}
				
				//签名审查任务
				FormatMinceJobDO signatureJob = OcrUtil.getMinceJobByCheckType(allMinceJobList, CheckTypeEnum.SIGNATURE.getCode());
				if(signatureJob != null) {
					qmqznyMinceJobList.add(signatureJob);
					signatureFileIdList = tFormatCheckService.getPrisonerCheckResultFileIdList(checkJob.getJgrybm(), checkJob.getJobType(), CheckTypeEnum.SIGNATURE.getCode());
				}
				
				//捺印审查任务
				FormatMinceJobDO fingerPrintJob = OcrUtil.getMinceJobByCheckType(allMinceJobList, CheckTypeEnum.FINGERPRINT.getCode());
				if(fingerPrintJob != null) {
					qmqznyMinceJobList.add(fingerPrintJob);
					fingerPrintFileIdList = tFormatCheckService.getPrisonerCheckResultFileIdList(checkJob.getJgrybm(), checkJob.getJobType(), CheckTypeEnum.FINGERPRINT.getCode());
				}
				
				//处理取消检测的子任务
				ocrHandler.updateCancelFormatJob(jobId, sealJob, signatureJob, fingerPrintJob);
				
				//获取格式化审查关联的材料
				List<MaterialInfoDO> dossierMaterialInfoList = tFormatCheckService.getFormatCheckMaterialInfoList(checkJob.getJgrybm(), checkJob.getJobType());
				
				//没有可以审查的材料
				if(!tFormatCheckService.validateFormatCheckMaterials(checkJob, dossierMaterialInfoList)) {
					return;
				}
				
				//获取案件编目信息
				PrisonerCatalogDO ajCatalog = tFormatCheckService.getPrisonerCatalog(checkJob.getJgrybm());
				
				//审查结果
				List<FormatCheckResultDO> checkResultList = new ArrayList<>();
				
				//需更新的材料
				Map<String, MaterialInfoDO> needUpdateMaterialInfoMap = new HashMap<>();
				
				//审查结果插入时间
				Date addTime = new Date();
				
				//失败的文件数量
				int failedIdsNum = 0;
				if(GoOcrHttpStatus.INCOMPLETE_RETURN.getCode() == serDatas.getCode()){
					failedIdsNum = serDatas.getFailed_ids().size();
				}
				
				//循环处理返回结果数据
				log.info("ocr处理-格式化审查-案件:{} ocr处理完成的图片数量:{}", checkJob.getJgrybm(), callbackResults.size());
				for(int i = 0; i < callbackResults.size(); i ++) {
					JSONObject file = callbackResults.getJSONObject(i);
					
					//文件Id
					String fileId = file.getString("fileId");
					
					//获取数据
					JSONObject datas = file.getJSONObject("datas");
					
					//从数据中获取原始结果
					String ssfcResult = datas.getString("ssfcResult");
					
					//从数据中获取简化结果
					JSONObject ssfcsimResult = JSONObject.parseObject(OcrUtil.replacePythonBoolean(datas.getString("ssfcsimResult")));
					
					//是否找到并处理当前文件对应的材料
					boolean checked = false;
					
					//循环处理材料
					for(MaterialInfoDO dossierMaterialInfo : dossierMaterialInfoList) {
						JSONArray materialFile = JSONArray.parseArray(dossierMaterialInfo.getMaterialFile());
						for(int j = 0; j < materialFile.size(); j ++) {
							JSONObject material = materialFile.getJSONObject(j);
							String id = material.getString("id");
							String url = material.getString("url");
							
							//获取图片的宽度和高度
							Integer width = null, height = null;
							int[] imageAttr = PicUtil.getImageWidthAndHeight(url);
							if(null != imageAttr) {
								width = imageAttr[0];
					            height = imageAttr[1];
							}
							
							//匹配到材料
							if(fileId.equals(id)) {
								checked = true;
								
								//根据返回结果处理材料
								if(null != ssfcsimResult) {
									
									//签章审查结果
									JSONObject seal = ssfcsimResult.getJSONObject("seal");
									if(null != seal && null != sealJob && !sealFileIdList.contains(id)) {
										boolean checkRes = seal.getBooleanValue("check_res");
										if(!checkRes) {
											JSONArray detailRes = seal.getJSONArray("detail_res");
											
											//创建审查结果对象
											FormatCheckResultDO checkResult = OcrUtil.buildFormatCheckResult(checkJob, ajCatalog,
													dossierMaterialInfo, material, CheckTypeEnum.SEAL.getCode(), addTime);
											
											//补充审查结果属性(图片宽度、高度等)
											checkResult.setWidth(width);
											checkResult.setHeight(height);
											checkResult.setMissSeal(detailRes.size());
											checkResult.setSsfcsimResult(ssfcsimResult.toJSONString());
											
											//设置原始结果
											if(ssfcResult != null) {
												checkResult.setFormatResult(ssfcResult);
											}
											
											//添加审查结果
											checkResultList.add(checkResult);
											
											//设置材料属性
											material.put(DamConstants.MaterialColumn.MISS_SEAL, detailRes.size());
//											if(null != sealJob) {
//												material.put(DamConstants.MaterialColumn.MISS_SEAL, detailRes.size());
//											}
//											else {
//												checkResult.setShowData(DamConstants.CHECK_FORMAT_RESULT_HIDDEN);
//											}
											
											//更新材料文件
											dossierMaterialInfo.setMaterialFile(materialFile.toJSONString());
											
											//添加到待更新材料中
											if(!needUpdateMaterialInfoMap.containsKey(dossierMaterialInfo.getId())){
												needUpdateMaterialInfoMap.put(dossierMaterialInfo.getId(), dossierMaterialInfo);
											}
										}
									}
									
									//签名审查结果
									JSONObject signature = ssfcsimResult.getJSONObject("signature");
									if(null != signature && null != signatureJob && !signatureFileIdList.contains(id)) {
										boolean checkRes = signature.getBooleanValue("check_res");
										if(!checkRes) {
											JSONArray detailRes = signature.getJSONArray("detail_res");
											
											//创建审查结果对象
											FormatCheckResultDO checkResult = OcrUtil.buildFormatCheckResult(checkJob, ajCatalog,
													dossierMaterialInfo, material, CheckTypeEnum.SIGNATURE.getCode(), addTime);
											
											//补充审查结果属性(图片宽度、高度等)
											checkResult.setWidth(width);
											checkResult.setHeight(height);
											checkResult.setMissSignature(detailRes.size());
											checkResult.setSsfcsimResult(ssfcsimResult.toJSONString());
											
											//设置原始结果
											if(ssfcResult != null) {
												checkResult.setFormatResult(ssfcResult);
											}
											
											//添加审查结果
											checkResultList.add(checkResult);
											
											//设置材料属性
											material.put(DamConstants.MaterialColumn.MISS_SIGNATURE, detailRes.size());
//											if(null != signatureJob) {
//												material.put(DamConstants.MaterialColumn.MISS_SIGNATURE, detailRes.size());
//											}
//											else {
//												checkResult.setShowData(DamConstants.CHECK_FORMAT_RESULT_HIDDEN);
//											}
											
											//更新材料文件
											dossierMaterialInfo.setMaterialFile(materialFile.toJSONString());
											
											//添加到待更新材料中
											if(!needUpdateMaterialInfoMap.containsKey(dossierMaterialInfo.getId())){
												needUpdateMaterialInfoMap.put(dossierMaterialInfo.getId(), dossierMaterialInfo);
											}
										}
									}
									
									//捺印审查结果
									JSONObject fingerprint = ssfcsimResult.getJSONObject("fingerprint");
									if(null != fingerprint && fingerPrintJob != null && !fingerPrintFileIdList.contains(id)) {
										boolean checkRes = seal.getBooleanValue("check_res");
										if(!checkRes) {
											JSONArray detailRes = seal.getJSONArray("detail_res");
											
											//创建审查结果对象
											FormatCheckResultDO checkResult = OcrUtil.buildFormatCheckResult(checkJob, ajCatalog,
													dossierMaterialInfo, material, CheckTypeEnum.FINGERPRINT.getCode(), addTime);
											
											//补充审查结果属性(图片宽度、高度等)
											checkResult.setWidth(width);
											checkResult.setHeight(height);
											checkResult.setMissFingerprint(detailRes.size());
											checkResult.setSsfcsimResult(ssfcsimResult.toJSONString());
											
											//设置原始结果
											if(ssfcResult != null) {
												checkResult.setFormatResult(ssfcResult);
											}
											
											//添加审查结果
											checkResultList.add(checkResult);
											
											//设置材料属性
											material.put(DamConstants.MaterialColumn.MISS_FINGERPRINT, detailRes.size());
//											if(null != fingerPrintJob) {
//												material.put(DamConstants.MaterialColumn.MISS_FINGERPRINT, detailRes.size());
//											}
//											else {
//												checkResult.setShowData(DamConstants.CHECK_FORMAT_RESULT_HIDDEN);
//											}
											
											//更新材料文件
											dossierMaterialInfo.setMaterialFile(materialFile.toJSONString());
											
											//添加到待更新材料中
											if(!needUpdateMaterialInfoMap.containsKey(dossierMaterialInfo.getId())){
												needUpdateMaterialInfoMap.put(dossierMaterialInfo.getId(), dossierMaterialInfo);
											}
										}
									}
								}
								
								//找到对应材料直接退出循环
								break;
							}
						}
						
						//已经找到并处理当前文件对应的材料
						if(checked) {
							break;
						}
					}
				}
				
				//更新公章审查子任务进度
				if(null != sealJob) {
					sealFinished = ocrHandler.getAndUpdateFormactCheckMinceJobProcess(sealJob, callbackResults.size() + failedIdsNum);
				}
				
				//更新签名审查子任务进度
				if(null != signatureJob) {
					signatureFinished = ocrHandler.getAndUpdateFormactCheckMinceJobProcess(signatureJob, callbackResults.size() + failedIdsNum);
				}
				
				//更新捺印审查子任务进度
				if(null != fingerPrintJob) {
					fingerPrintFinished = ocrHandler.getAndUpdateFormactCheckMinceJobProcess(fingerPrintJob, callbackResults.size() + failedIdsNum);
				}
				
				//插入审查结果
				if(CollectionUtil.isNotNull(checkResultList)) {
					checkResultService.saveBatch(checkResultList);
				}
				
				//根据检测结果更新材料
				if(CollectionUtil.isNotNull(needUpdateMaterialInfoMap)) {
					List<MaterialInfoDO> needUpdateMaterialInfoList = new ArrayList<>(needUpdateMaterialInfoMap.values());
					dossierMaterialInfoService.updateBatchById(needUpdateMaterialInfoList);
				}
				
				//返回前端审查进度
				ocrHandler.sendFormatCheckProcessResult(checkJob);
				
				//判断任务是否完成
				isFinished = sealFinished && signatureFinished && fingerPrintFinished;
				
				//更新任务状态
				if(isFinished) {
					OcrUtil.updateFormatCheckJobStatus(checkJob, qmqznyMinceJobList, DamConstants.JOB_STATE_YES);
				}
				
				//更新isp调用对象
				ispInvoking.setOutput("签章、签名和捺印审查回调成功");				
			}
			else {
				ispInvoking.setOutput("签章、签名和捺印审查回调异常：参数不正确");
				ispInvoking.setRequestStatus("2");
				log.error("ocr处理-格式化审查-签章、签名和捺印审查回调异常：参数不正确");
				
				//任务是否完成
//				isFinished = serDatas.getProgress() >= 1;
				isFinished = true;
				
				//更新任务状态
				if(isFinished) {
					OcrUtil.updateFormatCheckJobStatus(checkJob, qmqznyMinceJobList, DamConstants.JOB_STATE_ERROR);
				}
			}
		}
		catch (Exception e) {
			e.printStackTrace();
			ispInvoking.setOutput(e.toString());
			ispInvoking.setRequestStatus("2");
			log.error("ocr处理-格式化审查-签章、签名和捺印审查回调异常：{}", e);
			
			//任务是否完成
//			isFinished = serDatas.getProgress() >= 1;
			isFinished = true;
			
			//更新任务状态
			if(isFinished) {
				OcrUtil.updateFormatCheckJobStatus(checkJob, qmqznyMinceJobList, DamConstants.JOB_STATE_ERROR);
			}
		}
		finally {
			if(isFinished && CollectionUtil.isNotNull(qmqznyMinceJobList)) {
				
				//更新子任务状态
				minceJobService.updateBatchById(qmqznyMinceJobList);
				
				//更新主任务状态并判断是否需要进行自动编目
				ocrHandler.updateFormatCheckJobStatusAndCatalog(checkJob, allMinceJobList);
			}
			
			//插入isp调用对象
			ispInvoking.setRequestTime(System.currentTimeMillis() - begin);
			ispInvoking.setAddTime(new Date());
			ispInvokingService.save(ispInvoking);
		}
	}
}
