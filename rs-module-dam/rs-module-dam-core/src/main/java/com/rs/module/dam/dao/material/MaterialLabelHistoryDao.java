package com.rs.module.dam.dao.material;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.material.MaterialLabelHistoryDO;
import com.rs.module.dam.vo.material.MaterialLabelHistoryListReqVO;
import com.rs.module.dam.vo.material.MaterialLabelHistoryPageReqVO;

/**
* 卷宗材料标签历史 Dao
*
* <AUTHOR>
*/
@Mapper
public interface MaterialLabelHistoryDao extends IBaseDao<MaterialLabelHistoryDO> {


    default PageResult<MaterialLabelHistoryDO> selectPage(MaterialLabelHistoryPageReqVO reqVO) {
        Page<MaterialLabelHistoryDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<MaterialLabelHistoryDO> wrapper = new LambdaQueryWrapperX<MaterialLabelHistoryDO>()
            .eqIfPresent(MaterialLabelHistoryDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(MaterialLabelHistoryDO::getMaterialId, reqVO.getMaterialId())
            .eqIfPresent(MaterialLabelHistoryDO::getCatalogId, reqVO.getCatalogId())
            .eqIfPresent(MaterialLabelHistoryDO::getFileId, reqVO.getFileId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(MaterialLabelHistoryDO::getAddTime);
        }
        Page<MaterialLabelHistoryDO> materialLabelHistoryPage = selectPage(page, wrapper);
        return new PageResult<>(materialLabelHistoryPage.getRecords(), materialLabelHistoryPage.getTotal());
    }
    default List<MaterialLabelHistoryDO> selectList(MaterialLabelHistoryListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MaterialLabelHistoryDO>()
            .eqIfPresent(MaterialLabelHistoryDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(MaterialLabelHistoryDO::getMaterialId, reqVO.getMaterialId())
            .eqIfPresent(MaterialLabelHistoryDO::getCatalogId, reqVO.getCatalogId())
            .eqIfPresent(MaterialLabelHistoryDO::getFileId, reqVO.getFileId())
        .orderByDesc(MaterialLabelHistoryDO::getAddTime));    }


    }
