package com.rs.module.dam.vo.ocr;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - ocr格式化审查结果列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FormatCheckResultListReqVO extends BaseVO {
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("关联dam_format_check_job的Id")
    private String jobId;

    @ApiModelProperty("关联dam_format_check_job的任务类型(0:未编目任务, 1:已编目任务, 2:智能阅卷任务)")
    private String jobType;

    @ApiModelProperty("材料Id")
    private String materialId;

    @ApiModelProperty("图片id")
    private String fileId;

    @ApiModelProperty("文件地址")
    private String fileUrl;

    @ApiModelProperty("检测类型(1:签章检测,2:签名检测,3:捺印检测,4:空白页检测,5:重复页检测,6:瑕疵页检测,7:文书规范检测)")
    private String checkType;

    @ApiModelProperty("材料序号")
    private Integer xh;

    @ApiModelProperty("分卷目录Id")
    private String partCatalogId;

    @ApiModelProperty("材料目录Id")
    private String catalogId;

    @ApiModelProperty("材料目录对应名称")
    private String catalogName;

    @ApiModelProperty("重复页分组标记")
    private String repeatMark;

    @ApiModelProperty("签名签章捺印检测结果")
    private String ssfcsimResult;

    @ApiModelProperty("缺失签章数量")
    private Integer missSeal;

    @ApiModelProperty("缺失签名数量")
    private Integer missSignature;

    @ApiModelProperty("缺失捺印数量")
    private Integer missFingerprint;

    @ApiModelProperty("格式审查结果")
    private String formatResult;

    @ApiModelProperty("文书信息提取信息")
    private String fileInfo;

    @ApiModelProperty("检测结果是否展示(1:是, 0:否)")
    private String showData;

    @ApiModelProperty("图片宽")
    private Integer width;

    @ApiModelProperty("图片高")
    private Integer height;

    @ApiModelProperty("瑕疵页检测结果")
    private String flawData;

    @ApiModelProperty("文书填写规范检测缺失内容")
    private String loseInfo;

}
