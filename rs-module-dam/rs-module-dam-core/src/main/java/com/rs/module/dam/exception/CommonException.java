package com.rs.module.dam.exception;

import com.rs.module.dam.constant.PdfDealCode;

/**
 * 通用异常类
 * <AUTHOR>
 * @date 2025年4月21日
 */
public class CommonException extends RuntimeException{
	private static final long serialVersionUID = 7471208122055250355L;
	
	private String code;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public CommonException(String message){
        super(message);
    }

    public CommonException(String code, String message){
        super(message);
        this.code = code;
    }

    public CommonException(PdfDealCode codeEnum){
        super(codeEnum.getMsg());
        this.code = codeEnum.getCode();
    }
}
