package com.rs.module.dam.service.material;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.validation.annotation.Validated;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.cache.RedisClient;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.config.WebSocketServer;
import com.rs.module.dam.constant.BmTypeEnum;
import com.rs.module.dam.constant.CheckTypeEnum;
import com.rs.module.dam.constant.DamConstants;
import com.rs.module.dam.constant.JzztEnum;
import com.rs.module.dam.constant.RedisConstants;
import com.rs.module.dam.dao.material.MaterialInfoDao;
import com.rs.module.dam.entity.archive.ArchiveCoverDO;
import com.rs.module.dam.entity.material.MaterialImageDO;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.entity.material.MaterialMarkerDO;
import com.rs.module.dam.entity.ocr.FormatCheckJobDO;
import com.rs.module.dam.entity.prisoner.PrisonerCatalogDO;
import com.rs.module.dam.entity.sys.ConfigGroupDO;
import com.rs.module.dam.ocr.vo.OcrScpdfParams;
import com.rs.module.dam.service.archive.ArchiveCoverService;
import com.rs.module.dam.service.ocr.FormatCheckJobService;
import com.rs.module.dam.service.ocr.FormatCheckResultService;
import com.rs.module.dam.service.prisoner.PrisonerCatalogService;
import com.rs.module.dam.service.prisoner.PrisonerInfoService;
import com.rs.module.dam.service.sys.ConfigGroupService;
import com.rs.module.dam.strategy.OperateTypeEnum;
import com.rs.module.dam.strategy.OperateTypeService;
import com.rs.module.dam.strategy.OperateTypeServiceFactory;
import com.rs.module.dam.strategy.impl.DamDeleteImageOperate;
import com.rs.module.dam.util.CallBackFunction;
import com.rs.module.dam.util.MaterialUtil;
import com.rs.module.dam.util.PdfGenerateUtil;
import com.rs.module.dam.util.PdfPreviewUtil;
import com.rs.module.dam.util.RedisTemplateUtil;
import com.rs.module.dam.util.ThreadPoolUtil;
import com.rs.module.dam.vo.material.MaterialInfoListReqVO;
import com.rs.module.dam.vo.material.MaterialInfoPageReqVO;
import com.rs.module.dam.vo.material.MaterialInfoSaveReqVO;
import com.rs.module.dam.websocket.WebSocketCode;
import com.rs.module.dam.websocket.WebSocketUtil;

import lombok.extern.slf4j.Slf4j;


/**
 * 卷宗材料 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class MaterialInfoServiceImpl extends BaseServiceImpl<MaterialInfoDao, MaterialInfoDO> implements MaterialInfoService {

    @Resource
    private MaterialInfoDao materialInfoDao;    

    @Resource
    private MaterialImageService materialImageService;
    
    @Resource
    private OperateTypeServiceFactory operateTypeServiceFactory;
    
    @Resource
    private FormatCheckJobService formatCheckJobService;
    
    @Resource
    private FormatCheckResultService formatCheckResultService;
    
    @Resource
    private ArchiveCoverService archiveCoverService;
    
    @Resource
    private DamDeleteImageOperate deleteImageOperate;
    
    @Resource
    private PrisonerCatalogService prisonerCatalogService;
    
    @Resource
    private PrisonerInfoService prisonerInfoService;
    
    @Resource
    private MaterialMarkerService materialMarkerService;
    
    @Resource
    private ConfigGroupService configGroupService;
        
    @Resource
    private RedisTemplateUtil redisUtil;

    @Value("${ocr.scanPage}")
    private String scanPage;

    @Value("${ocr.waitTime}")
    private String waitingTime;
    
    @Override
    public String createMaterialInfo(MaterialInfoSaveReqVO createReqVO) {
        // 插入
        MaterialInfoDO materialInfo = BeanUtils.toBean(createReqVO, MaterialInfoDO.class);
        materialInfoDao.insert(materialInfo);
        // 返回
        return materialInfo.getId();
    }

    @Override
    public void updateMaterialInfo(MaterialInfoSaveReqVO updateReqVO) {
        // 校验存在
        validateMaterialInfoExists(updateReqVO.getId());
        // 更新
        MaterialInfoDO updateObj = BeanUtils.toBean(updateReqVO, MaterialInfoDO.class);
        materialInfoDao.updateById(updateObj);
    }

    @Override
    public void deleteMaterialInfo(String id) {
        // 校验存在
        validateMaterialInfoExists(id);
        // 删除
        materialInfoDao.deleteById(id);
    }

    private void validateMaterialInfoExists(String id) {
        if (materialInfoDao.selectById(id) == null) {
            throw new ServerException("卷宗材料数据不存在");
        }
    }

    @Override
    public MaterialInfoDO getMaterialInfo(String id) {
        return materialInfoDao.selectById(id);
    }

    @Override
    public PageResult<MaterialInfoDO> getMaterialInfoPage(MaterialInfoPageReqVO pageReqVO) {
        return materialInfoDao.selectPage(pageReqVO);
    }

    @Override
    public List<MaterialInfoDO> getMaterialInfoList(MaterialInfoListReqVO listReqVO) {
        return materialInfoDao.selectList(listReqVO);
    }

    /**
     * 根据监管人员代码和材料类型获取分卷目录Id
     * @param jgrybm String 监管人员编码
     * @param type String 类型
     * @return List<String>
     */
    @Override
    public List<String> selectPartCatalogIdByJgrybmAndType(String jgrybm, String type){
    	return materialInfoDao.selectPartCatalogIdByJgrybmAndType(jgrybm, type);
    }
    
    /**
     * 获取材料上传的pdf数量
     * @param jgrybm String 监管人员编码
     * @param types List<String> types 类型集合
     * @return Integer
     */
    @Override
    public Integer selectUploadPdfCountByType(String jgrybm, List<String> types) {
    	return materialInfoDao.selectUploadPdfCountByType(jgrybm, types);
    }
    
    /**
     * 对材料进行操作
     * @param materialArray JSONArray 材料数据
     * @param request HttpServletRequest
     * @return boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean materialOperate(JSONArray materialArray, HttpServletRequest request) {
    	boolean flag = false;
    	
    	try {
    		for(int i = 0; i < materialArray.size(); i ++) {
    			JSONObject materialObject = materialArray.getJSONObject(i);
    			MaterialInfoDO materialInfo = JSONObject.toJavaObject(materialObject, MaterialInfoDO.class);
    			
    			//操作类型
    			String operate = materialObject.containsKey("operate") ? materialObject.getString("operate") : "";
    			
    			//编辑材料
    			if(DamConstants.OPERATE_EDIT.equals(operate)) {
    				Set<String> repeatMarkSet = new HashSet<>();
    				String operateType = materialObject.getString("operateType");
    				
    				//垃圾回收功能
    				if(StringUtil.isNotEmpty(operateType)) {
    					List<MaterialImageDO> materialImages = materialImageService.list(new QueryWrapper<MaterialImageDO>().eq("material_id", materialInfo.getId()));
    					Optional<MaterialImageDO> first = materialImages.stream().findFirst();
    					boolean deleteMaterialFile = materialObject.containsKey("deleteMaterialFile");
    					String beanName = OperateTypeEnum.getBeanNameByType(operateType);
    					OperateTypeService operateTypeService = operateTypeServiceFactory.getOperateTypeInstance(beanName);
    					if(first.isPresent()) {
    						repeatMarkSet = operateTypeService.operateByType(materialObject);
    					}
    					
    					//删除图片
    					else if(DamConstants.IMAGE_DELETE.equals(operateType) && deleteMaterialFile) {
    						repeatMarkSet = operateTypeService.operateByType(materialObject);
    					}
    					
    					//未编目移动
    					else if(DamConstants.IMAGE_MOVE.equals(operateType) && DamConstants.MATERIAL_TYPE_WBM.equals(materialObject.getString("type"))) {
    						operateTypeService.operateByType(materialObject);
    					}
    				}
    				
    				//更新数据库材料
    				MaterialInfoDO dbMaterialInfo = getById(materialInfo.getId());
    				dbMaterialInfo.setName(materialInfo.getName());
    				dbMaterialInfo.setMaterialFile(materialInfo.getMaterialFile());
    				dbMaterialInfo.setPageCount(materialInfo.getPageCount());
    				updateById(dbMaterialInfo);
    				
    				//清除重复标记
    				removeRepeatMark(repeatMarkSet, materialInfo);
    			}
    			
    			//删除材料
    			else if(DamConstants.OPERATE_DELETE.equals(operate)) {
    				Set<String> repeatMarkSet = new HashSet<>();
    				String operateType = materialObject.getString("operateType");
    				if(StringUtil.isNotEmpty(operateType)) {
    					String beanName = OperateTypeEnum.getBeanNameByType(operateType);
    					OperateTypeService operateTypeService = operateTypeServiceFactory.getOperateTypeInstance(beanName);
    					repeatMarkSet = operateTypeService.operateByType(materialObject);
    				}
    				
    				this.baseMapper.deleteById(materialInfo.getId());
    				
    				//清除重复标记
    				removeRepeatMark(repeatMarkSet, materialInfo);
    			}
    			
    			//添加材料
    			else if(DamConstants.OPERATE_ADD.equals(operate)) {
    				String scanType = materialObject.getString("scanType");
    				
    				if(StringUtil.isNotEmpty(scanType)) {
    					
    					//windows 系统扫描上传
    					if(DamConstants.SCAN_WINDOWS.equals(scanType)) {
    						setMaterialFileCache(materialInfo);
    						getMaterialFileCache(materialInfo);
    					}
    					
    					//linux 系统 国产影源扫描仪上传
    					else if(DamConstants.SCAN_LINUX_WINMAGE.equals(scanType)) {
    						String id = StringUtil.getGuid32();
    						materialInfo.setId(id);
    						save(materialInfo);
    						
    						//通过左侧树结构进行的上传不用进行ocr识别, 通过批量上传处的扫描上传,开启ocr识别
    						
    						//插入材料数据, 发送webSocket, 前端刷新页面
    						String jgrybm = materialObject.getString("jgrybm");
    						WebSocketUtil.sendMessage(WebSocketCode.SCAN_UPLOAD_FINISH.getCode(),
    								WebSocketCode.SCAN_UPLOAD_FINISH.getMsg(), jgrybm);
    					}
    				}
    				else {
    					String id = StringUtil.getGuid32();
        				materialInfo.setId(id);
        				save(materialInfo);
        				
        				//其它操作
        				String operateType = materialObject.getString("operateType");
        				if(StringUtil.isNotEmpty(operateType)) {
        					materialObject.put("materialInfoId", id);
        					String beanName = OperateTypeEnum.getBeanNameByType(operateType);
        					OperateTypeService operateTypeService = operateTypeServiceFactory.getOperateTypeInstance(beanName);
                            operateTypeService.operateByType(materialObject);
        				}
    				}
    			}
    			
    			//材料排序
    			else if(DamConstants.OPERATE_ORDER.equals(operate)) {
    				JSONArray materialOrders = JSONArray.parseArray(materialObject.getString(DamConstants.OPERATE_ORDER));
    				if(materialOrders != null && !materialOrders.isEmpty()) {
    					List<MaterialInfoDO> orderList = new ArrayList<>();
    					materialOrders.forEach(o -> {
                			if(o == null) return;
                			JSONObject orderJson = (JSONObject)o;
                			orderJson.keySet().forEach(m -> {                    				
                				MaterialInfoDO mater = new MaterialInfoDO();
                				mater.setId(m);
                				mater.setXh(orderJson.getInteger(m));
                				orderList.add(mater);
                			});
                		});
    					
    					//更新序号
                		if(CollectionUtil.isNotNull(orderList)) {
                			materialInfoDao.updateMaterialInfoXh(orderList);
                		}
    				}
    			}
    		}
    		
    		flag = true;
    	}
    	catch(Exception e) {
    		TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
    	}
    	
    	return flag;
    }
    
    /**
     * 清除重复标记
     * @param repeatMarkSet Set<String> 去重的材料Id
     * @param materialInfo MaterialInfoDO 材料信息
     */
    public void removeRepeatMark(Set<String> repeatMarkSet, MaterialInfoDO materialInfo) {
        String type = materialInfo.getType();
        String jgrybm = materialInfo.getJgrybm();
        if (CollectionUtil.isNotNull(repeatMarkSet)) {
        	
            // 已有人员材料
            List<MaterialInfoDO> materialInfoList = materialInfoDao.selectList(
                    new QueryWrapper<MaterialInfoDO>().eq("jgrybm", jgrybm).eq("type", type));

            // map: key=重复标记, value=已有材料含有该标记的图片数量
            Map<String, Integer> repeatMarkMap = new HashMap<>();
            for (MaterialInfoDO materInfo : materialInfoList) {
                String materialFile = materInfo.getMaterialFile();
                JSONArray materialFileArray = JSONArray.parseArray(materialFile);
                for (int j = 0; j < materialFileArray.size(); j++) {
                    JSONObject materialFileObj = materialFileArray.getJSONObject(j);
                    boolean containsKey = materialFileObj.containsKey("repeatMark");
                    String repeatMark = materialFileObj.getString("repeatMark");
                    if (containsKey && repeatMarkSet.contains(repeatMark)) {
                        Integer markNum = repeatMarkMap.get(repeatMark);
                        
                        // 赋初值
                        if (markNum == null) {
                            repeatMarkMap.put(repeatMark, 1);
                        }
                        else {
                            repeatMarkMap.put(repeatMark, markNum + 1);
                        }
                    }
                }
            }

            // 材料中需要清除的标记集合
            List<String> clearRepeatMarks = new ArrayList<>();
            Set<Map.Entry<String, Integer>> entries = repeatMarkMap.entrySet();
            Iterator<Map.Entry<String, Integer>> iterator = entries.iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, Integer> next = iterator.next();
                String repeatMark = next.getKey();
                Integer repeatMarkNum = next.getValue();
                // 所有材料中只有一张图片含有该重复页标记, 需要清除
                if (repeatMarkNum == 1){
                    clearRepeatMarks.add(repeatMark);
                }
            }

            if (CollectionUtil.isNotNull(clearRepeatMarks)) {
            	for (MaterialInfoDO materInfo : materialInfoList) {
                    String materialFile = materInfo.getMaterialFile();
                    
                    // 材料中图片信息数组
                    JSONArray materialFileArray = JSONArray.parseArray(materialFile);
                    boolean flag = false;
                    
                    // 遍历图片信息
                    for (int j = 0; j < materialFileArray.size(); j++) {
                        JSONObject materialFileObj = materialFileArray.getJSONObject(j);
                        boolean containsKey = materialFileObj.containsKey("repeatMark");
                        String repeatMark = materialFileObj.getString("repeatMark");

                        // 修改具有相同重复页标记图片信息
                        if (containsKey && clearRepeatMarks.contains(repeatMark)) {
                            materialFileObj.remove("repeatMark");
                            materialFileObj.remove("repeat");

                            //图片Id
                            String imageId = materialFileObj.getString("id");
                            
                            // 更新格式化检测结果数据
                            QueryWrapper<FormatCheckJobDO> queryJobWrapper = new QueryWrapper<>();
                            queryJobWrapper.eq("jgrybm", jgrybm);
                            queryJobWrapper.eq("status", "1");
                            queryJobWrapper.eq("job_type", type);
                            queryJobWrapper.orderByDesc("add_time");
                            queryJobWrapper.last("limit 1");                            
                            FormatCheckJobDO formatCheckJob = formatCheckJobService.getOne(queryJobWrapper);
                            if (null != formatCheckJob){
                                String jobId = formatCheckJob.getId();
                                
                                // 删除格式化审查结果中记录
                                formatCheckResultService.updateShowData(jobId, imageId, CheckTypeEnum.REPEAT_PAGE.getCode(), "0");
                            }
                            
                            flag = true;
                        }
                    }

                    // 此材料是否修改过
                    if (flag) {
                    	materialInfo.setMaterialFile(materialFileArray.toJSONString());
                    	materialInfoDao.updateById(materialInfo);
                        
                        List<MaterialImageDO> materialImages = materialImageService.list(new QueryWrapper<MaterialImageDO>().eq("material_id", materialInfo.getId()));
                        Optional<MaterialImageDO> first = materialImages.stream().findFirst();
                        List<MaterialImageDO> materialImageOrderList = deleteImageOperate.materialImageListInfo(materialInfo);
                        
                        // 第一次删除
                        if (!first.isPresent() && CollectionUtil.isNotNull(materialImageOrderList)) {
                        	materialImageService.saveBatch(materialImageOrderList);
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 设置材料缓存
     * @param materialInfo MaterialInfoDO 材料信息
     */
    private void setMaterialFileCache(MaterialInfoDO materialInfo){
        try {
            //未编目的扫描上传
            if (StringUtil.isEmpty(materialInfo.getCatalogId())){
                String materialFile = materialInfo.getMaterialFile();
                String materKey = String.format(RedisConstants.MATER_INFO_JGRYBM, materialInfo.getJgrybm());
                redisUtil.lPush(materKey, materialFile, 120);
            }
            
            //在目录树的扫描上传
            else{
                
                String materialFile = materialInfo.getMaterialFile();
                String materKey = String.format(RedisConstants.MATER_INFO_JGRYBM_CATALOG, materialInfo.getJgrybm(),
                		materialInfo.getCatalogId());
                redisUtil.lPush(materKey, materialFile, 120);
            }
        }
        catch (Exception e){
            e.printStackTrace();
        }
    }
    
    /**
     * 获取材料缓存
     * @param materialInfo MaterialInfoDO 材料信息
     * @throws Exception
     */
    private void getMaterialFileCache(MaterialInfoDO materialInfo) throws Exception{
    	logger.info("into getMaterFileCache");
    	
        String jgrybmMater = materialInfo.getJgrybm();
        String materIdKey = String.format(RedisConstants.START_MATER_ID, jgrybmMater);
        boolean setOk = redisUtil.lock(materIdKey, "1", 120);
        logger.info("materIdKey:{},setOk:{}",materIdKey,setOk);
        if (!setOk){
            return;
        }
        
        String increaseKey = String.format(RedisConstants.SCAN_MATERIAL_INCREASE, jgrybmMater);
        boolean hasCatalogId = StringUtil.isNotEmpty(materialInfo.getCatalogId());

        //定义线程任务
        Runnable updateMaterFile = new Runnable(){
        	
            @Override
            public void run() {
                String materKey = String.format(RedisConstants.MATER_INFO_JGRYBM, jgrybmMater);

                if (hasCatalogId){
                    materKey = String.format(RedisConstants.MATER_INFO_JGRYBM_CATALOG, jgrybmMater, 
                    		materialInfo.getCatalogId());
                }

                JSONArray newMaterial = new JSONArray();
                long beginTime = System.currentTimeMillis();

                while (true){
                    RedisClient.expire(materIdKey,120);
                    String cacheMaterialInfo = redisUtil.rPop(materKey);
                    if (StringUtil.isEmpty(cacheMaterialInfo)){
                        //队列中暂时取不到数据,自旋等待,开始计时
                        long waitTime = System.currentTimeMillis() - beginTime;
                        int time = Integer.parseInt(waitingTime);

                        if(hasCatalogId){
                            if (waitTime/1000 >= 5 ){
                                logger.info("break-----");
                                break;
                            }
                        }
                        else{
                            //一定时间拿不到数据,结束线程
                            if (waitTime/1000 >= time ){
                                logger.info("break-----");
                                break;
                            }
                        }

                        continue;
                    }

                    JSONArray materArray = JSONObject.parseArray(cacheMaterialInfo);
                    for (int i = 0; i < materArray.size(); i++) {
                        JSONObject mater = materArray.getJSONObject(i);
                        long increase = redisUtil.increase(increaseKey);
                        redisUtil.expires(increaseKey,300);
                        mater.put("xh", increase);
                        newMaterial.add(mater);
                    }

                    if (newMaterial.size() >= Integer.parseInt(scanPage) && !hasCatalogId){
                        String id = StringUtil.getGuid32();
                        materialInfo.setId(id);
                        materialInfo.setAddTime(new Date());
                        materialInfo.setPageCount(newMaterial.size());
                        materialInfo.setMaterialFile(newMaterial.toJSONString());
                        baseMapper.insert(materialInfo);
                        logger.info("insert a dossierMaterialInfo:{}", materialInfo);                        

                        //清空 newMaterial
                        newMaterial.clear();
                        
                        //插入材料数据, 发送webSocket, 前端刷新页面
                        WebSocketUtil.sendMessage(WebSocketCode.SCAN_UPLOAD_FINISH.getCode(),
								WebSocketCode.SCAN_UPLOAD_FINISH.getMsg(), jgrybmMater);
                    }
                    //更新开始计时的时间
                    beginTime = System.currentTimeMillis();
                }

                if (CollectionUtil.isNotNull(newMaterial)){
                    String id = StringUtil.getGuid32();
                    materialInfo.setId(id);
                    materialInfo.setAddTime(new Date());
                    materialInfo.setPageCount(newMaterial.size());
                    materialInfo.setMaterialFile(newMaterial.toJSONString());
                    baseMapper.insert(materialInfo);
                    logger.info("insert b dossierMaterialInfo:{}", materialInfo);
                    
                    //插入材料数据, 发送webSocket, 前端刷新页面
                    WebSocketUtil.sendMessage(WebSocketCode.SCAN_UPLOAD_FINISH.getCode(),
							WebSocketCode.SCAN_UPLOAD_FINISH.getMsg(), jgrybmMater);
                    
                    //清除缓存
                    redisUtil.deleteKey(increaseKey);
                    
                    //开启ocr
                    if(!hasCatalogId) {
                    	
                    }

                    //清空 newMaterial
                    newMaterial.clear();
                }
                
                redisUtil.deleteKey(materIdKey);
            }
        };
        
        ThreadPoolUtil.getPool().execute(updateMaterFile);
    }
    
    /**
     * 保存组卷材料(确认组卷)
     * @param data String 组卷数据
     * @param ocrV2ParamsList List<OcrV2ScpdfParams> 双层pdf参数列表
     * @return boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveZjMaterial(String data, List<OcrScpdfParams> ocrV2ParamsList) {
    	 boolean flag = false;
    	 
    	 try {
    		 SessionUser user = SessionUserUtil.getSessionUser();
             JSONObject dataObj = JSONObject.parseObject(data);
             
             //监管人员编码
             String jgrybm = dataObj.getString("jgrybm");
             
             //组卷目录
             String zjCatalogData = dataObj.getString("zjCatalogData");
             
             //封面
             String coverData = dataObj.getString("coverData");
             
             //组卷材料
             String materialData = dataObj.getString("materialData");
             
             //组卷类型
             String zjType = dataObj.getString("zjType");
             
             //设置卷宗封面信息
             List<ArchiveCoverDO> coverList = JSONArray.parseArray(coverData, ArchiveCoverDO.class);
             List<String> partCatalogIdList = new ArrayList<>();
             for (ArchiveCoverDO archiveCover : coverList) {
            	 archiveCover.setId(StringUtil.getGuid32());
            	 archiveCover.setAddUser(user.getIdCard());
            	 archiveCover.setFjlx(zjType);
                 partCatalogIdList.add(archiveCover.getPartCatalogId());
             }
             
             //删除组卷材料-相同组卷方式
             QueryWrapper<MaterialInfoDO> materialDeleteWrapper = new QueryWrapper<>();
             materialDeleteWrapper.eq("jgrybm", jgrybm);
             if("0".equals(zjType)) {		//编目组卷-完成组卷
            	 materialDeleteWrapper.eq("type", BmTypeEnum.ZJ.getType());
             }
             else if("1".equals(zjType)) {	//卷宗导入-确认组卷
            	 materialDeleteWrapper.eq("type", BmTypeEnum.KSZJ.getType());
             }
             else {
            	 String[] types = new String[] {BmTypeEnum.ZJ.getType(), BmTypeEnum.KSZJ.getType()};
            	 materialDeleteWrapper.in("type", Arrays.asList(types));
             }
             remove(materialDeleteWrapper);
             
             //删除组卷材料--同一分卷
             materialDeleteWrapper = new QueryWrapper<>();
             materialDeleteWrapper.eq("jgrybm", jgrybm);
             String[] types = new String[] {BmTypeEnum.ZJ.getType(), BmTypeEnum.KSZJ.getType()};
        	 materialDeleteWrapper.in("type", Arrays.asList(types));
        	 materialDeleteWrapper.in("part_catalog_id", partCatalogIdList);
             remove(materialDeleteWrapper);
             
             //删除封面-相同组卷方式
             QueryWrapper<ArchiveCoverDO> coverDeleteWrapper = new QueryWrapper<ArchiveCoverDO>();
             coverDeleteWrapper.eq("jgrybm", jgrybm);
             coverDeleteWrapper.eq("fjlx", zjType);
             archiveCoverService.remove(coverDeleteWrapper);
             
             //删除封面-同一分卷
             coverDeleteWrapper = new QueryWrapper<ArchiveCoverDO>();
             coverDeleteWrapper.eq("jgrybm", jgrybm);
             coverDeleteWrapper.in("part_catalog_id", partCatalogIdList);
             archiveCoverService.remove(coverDeleteWrapper);
             
             //保存组卷材料
             List<MaterialInfoDO> materialList = JSONArray.parseArray(materialData, MaterialInfoDO.class);
             for(MaterialInfoDO materialInfo : materialList) {
            	 materialInfo.setId(StringUtil.getGuid32());
            	 materialInfo.setAddUser(user.getIdCard());
            	 
            	 JSONArray array = JSON.parseArray(materialInfo.getMaterialFile());
                 if(!array.isEmpty()) {
                	 JSONObject object = array.getJSONObject(0);
                	 MaterialInfoDO para = new MaterialInfoDO();
                     BeanUtils.copyProperties(materialInfo, para);
                     para.setMaterialFile(object.getString("id"));
//                     materialInfoDao.updateBmmlByZjml(para);

                     //插入材料
                     materialInfoDao.insert(materialInfo);
                 }
             }
             
             //材料文件Id
             List<String> fileIdList = MaterialUtil.getFileIdList(materialList);
             
             //全部材料页数
             int fileSize = fileIdList.size();
             
             //对比格式化审查结果，删除不在fileIdList中的检测结果
             
             //保存封面
             archiveCoverService.saveBatch(coverList);
             
             //保存目录
             if(dataObj.containsKey("saveCatalog")) {
            	 if (dataObj.getBooleanValue("saveCatalog")) {
                     PrisonerCatalogDO catalog = new PrisonerCatalogDO();
                     catalog.setId(StringUtil.getGuid32());
                     catalog.setJgrybm(jgrybm);
                     catalog.setBmCatalogData(dataObj.getString("bmCatalogData"));
                     catalog.setKszjCatalogData(dataObj.getString("kszjCatalogData"));
                     prisonerCatalogService.save(catalog);
                 }
            	 
            	 //移除卷宗数据中的目录信息
            	 QueryWrapper<PrisonerCatalogDO> prisonerCatalogQueryWrapper = new QueryWrapper<PrisonerCatalogDO>().eq("jgrybm", jgrybm);
            	 List<PrisonerCatalogDO> prisonerCatalogList = prisonerCatalogService.list(prisonerCatalogQueryWrapper);
            	 if(CollectionUtil.isNotNull(prisonerCatalogList)) {
            		 PrisonerCatalogDO prisonerCatalog = prisonerCatalogList.get(0);
            		 if (StringUtil.isNotEmpty(prisonerCatalog.getZjCatalogData())){
                         JSONArray array = JSONObject.parseArray(prisonerCatalog.getZjCatalogData());
                         PdfGenerateUtil.removeCatalog(array, partCatalogIdList);
                         prisonerCatalog.setZjCatalogData(array.toJSONString());
                         if(StringUtil.isNotEmpty(dataObj.getString("kszjCatalogData"))){
                        	 prisonerCatalog.setKszjCatalogData(dataObj.getString("kszjCatalogData"));
                         }
                         prisonerCatalogService.updateById(prisonerCatalog);
                     }
            	 }
             }
             
             //更新目录
             else {
            	 QueryWrapper<PrisonerCatalogDO> prisonerCatalogQueryWrapper = new QueryWrapper<PrisonerCatalogDO>().eq("jgrybm", jgrybm);
            	 PrisonerCatalogDO prisonerCatalog = prisonerCatalogService.getOne(prisonerCatalogQueryWrapper);
            	 prisonerCatalog.setZjCatalogData(zjCatalogData);
            	 if(StringUtil.isNotEmpty(dataObj.getString("kszjCatalogData"))) {
            		 prisonerCatalog.setKszjCatalogData(dataObj.getString("kszjCatalogData"));
            	 }
            	 prisonerCatalogService.updateById(prisonerCatalog);
             }
             
             //删除异常错误数据
             deleteWrongData(jgrybm);
             
             try {
            	 ConfigGroupDO configGroup = configGroupService.getUserConfigGroup(user.getOrgCode());
            	 
            	 //是否生成双层pdf
            	 boolean makeDoublePdf = StringUtil.getBoolean(configGroup.getMakeDoublePdf());
            	 
            	 for(int i = 0; i < coverList.size(); i ++) {
            		 ArchiveCoverDO archiveCover = coverList.get(i);
            		 
            		 //构建分卷材料
            		 String partCatalogId = archiveCover.getPartCatalogId();
            		 List<MaterialInfoDO> partMaterialList = new CopyOnWriteArrayList<>();
            		 for(MaterialInfoDO materialInfo : materialList) {
            			 if(partCatalogId.equals(materialInfo.getPartCatalogId())) {
            				 partMaterialList.add(materialInfo);
            			 }
            		 }
            		 
            		 //编目组卷
            		 if(DamConstants.ZJ_TYPE_BMZJ.equals(zjType)) {
            			 
            			 //生成预览pdf
            			 PdfPreviewUtil.executeAsyncForPartPdf(i, coverList.size(), archiveCover,
            					 partMaterialList, false, zjType, user, new CallBackFunction() {
									
							@Override
							public void execute(JSONObject jsonObject) {
								JSONArray array = jsonObject.getJSONArray("pdfImageUrl");
								
								//更新卷宗封面(封面页数=pdf总页数-材料总页数-尾部封面2页)
								archiveCover.setPdfUrl(jsonObject.getString("url"));
                                int coverSize = array.size() - fileSize - 2;
                                coverSize = Math.max(coverSize, 0);
                                archiveCover.setCoverPage(coverSize);
                                archiveCover.setPreviewUrl(array.toJSONString());                                
                                archiveCoverService.updateById(archiveCover);

                                //推送Websocket消息
                                JSONObject sendData = WebSocketUtil.buildBaseMessage(WebSocketCode.SCAN_UPLOAD_FINISH.getCode(),
        								WebSocketCode.SCAN_UPLOAD_FINISH.getMsg(), jgrybm);
                                sendData.put("id", archiveCover.getId());
                                sendData.put("url", array);
                                WebSocketServer.sendInfo(null, sendData.toJSONString());
							}
            			 });
            		 }
            		 
            		 //快速组卷
            		 else {
         				List<String> urlList = MaterialUtil.getFileUrlList(materialList);
         				archiveCover.setPreviewUrl(JSON.toJSONString(urlList));                                
                        archiveCoverService.updateById(archiveCover);
            		 }
            		 
            		 //生成普通pdf
            		 PdfGenerateUtil.executeAsyncForPartPdf(i, coverList.size(), archiveCover,
        					 partMaterialList, false, zjType, user, new CallBackFunction() {
								
            			 @Override
            			 public void execute(JSONObject jsonObject) {
            				 JSONArray array = jsonObject.getJSONArray("pdfImageUrl");
            				 JSONArray imageUrl = new JSONArray();
            				 for (int j = 0; j < array.size(); j++) {
            					 JSONObject obj = new JSONObject();
            					 obj.put("xh", j+1);
            					 obj.put("id", StringUtil.getGuid32());
            					 obj.put("url", array.getString(j));
            					 imageUrl.add(obj);
            				 }
							
            				 //更新卷宗封面(封面页数=pdf总页数-材料总页数-尾部封面2页)
            				 archiveCover.setPdfUrl(jsonObject.getString("url"));
            				 int coverSize = array.size() - fileSize - 2;
            				 coverSize = Math.max(coverSize, 0);
            				 archiveCover.setCoverPage(coverSize);
            				 archiveCover.setImageUrl(imageUrl.toJSONString());                                
            				 archiveCoverService.updateById(archiveCover);
            			 }
            		 });
            		 
            		 
            		 //生成双层pdf
            		 if(makeDoublePdf) {
            			 if(ocrV2ParamsList != null) {
            				 OcrScpdfParams ocrV2Params = new OcrScpdfParams()            						 
            						 .setDealScpdf(true)
            						 .setCover(archiveCover)
            						 .setMaterialList(partMaterialList)
            						 .setJgrybm(jgrybm)
                     				 .setZjType(zjType)
                     				 .setIdCard(user.getIdCard())
                     				 .setOrgCode(user.getOrgCode());
            				 ocrV2ParamsList.add(ocrV2Params);
            			 }
            		 }
            		 
            		 //更新卷宗状态
            		 prisonerInfoService.updateJzzt(jgrybm, JzztEnum.YZJ.getType(), new Date(), null);
            	 }
             }
             catch(Exception e) {
            	 log.error("保存组卷材料(确认组卷)-生成pdf出现异常：{}", e.getMessage(), e);
             }
             
             flag = true;
    	 }
    	 catch(Exception e) {
    		 log.error("保存组卷材料(确认组卷)出现异常：{}", e.getMessage(), e);
    		 TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
    	 }
    	 
    	 return flag;
    }
    
    /**
     * 删除异常错误数据
     * @param jgrybm String 监管人员编码
     */
    public void deleteWrongData(String jgrybm) {
    	QueryWrapper<MaterialInfoDO> materialWrapper = new QueryWrapper<>();
    	materialWrapper.eq("jgrybm", jgrybm);
    	materialWrapper.in("type", Arrays.asList(new String[] {BmTypeEnum.ZJ.getType(), BmTypeEnum.KSZJ.getType()}));
    	List<MaterialInfoDO> materialList = list(materialWrapper);
    	List<String> fileIdList = new ArrayList<>();
    	for(MaterialInfoDO material : materialList) {
    		JSONArray array = JSONArray.parseArray(material.getMaterialFile());
    		for (int i = 0; i < array.size() ; i++) {
    			JSONObject object = array.getJSONObject(i);
    			fileIdList.add(object.getString("id"));
    		}
    	}
    	
    	//删除材料标记
    	QueryWrapper<MaterialMarkerDO> markerWrapper = new QueryWrapper<>();
    	materialWrapper.eq("jgrybm", jgrybm);
    	materialWrapper.notIn("jgrybm", fileIdList);
    	materialMarkerService.remove(markerWrapper);
    }
    
    /**
     * 获取卷宗材料集合
     * @param jgrybm String 监管人员编码
     * @param type String 编目类型
     * @param catalogId String 分类Id
     * @return List<MaterialInfoDO>
     */
    @Override
    public List<MaterialInfoDO> getMaterialInfoList(String jgrybm, String type, String catalogId){
    	QueryWrapper<MaterialInfoDO> materialWrapper = new QueryWrapper<>();
    	materialWrapper.orderByAsc("xh");
    	materialWrapper.eq("jgrybm", jgrybm);
    	materialWrapper.eq("type", "2");
    	if(StringUtil.isNotEmpty(catalogId)){
    		materialWrapper.eq("catalog_id", catalogId);
    	}
		
		return list(materialWrapper);
    }
    
    /**
     * 设置材料序号(基于缓存)
     * @param materialInfo DossierMaterialInfo 材料
     */
    @Override
    public void setMaterialInfoXh(MaterialInfoDO materialInfo) {
    	String materialSortKey = String.format(RedisConstants.Material_SORT_KEY, materialInfo.getJgrybm(), materialInfo.getType());
    	String currentXh = redisUtil.get(materialSortKey);
    	if(StringUtil.isNotEmpty(currentXh)) {
    		Integer currentXhInt = Integer.valueOf(currentXh);
    		currentXhInt ++;
    		materialInfo.setXh(currentXhInt);
    		redisUtil.set(materialSortKey, String.valueOf(currentXhInt));
    	}
    	else {
    		Integer currentXhInt = 1;
    		materialInfo.setXh(currentXhInt);
    		redisUtil.set(materialSortKey, String.valueOf(currentXhInt));
    	}
    }
}
