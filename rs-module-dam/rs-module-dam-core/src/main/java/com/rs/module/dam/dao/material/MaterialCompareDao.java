package com.rs.module.dam.dao.material;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.material.MaterialCompareDO;
import com.rs.module.dam.vo.material.MaterialCompareListReqVO;
import com.rs.module.dam.vo.material.MaterialComparePageReqVO;

/**
* 卷宗材料比对 Dao
*
* <AUTHOR>
*/
@Mapper
public interface MaterialCompareDao extends IBaseDao<MaterialCompareDO> {


    default PageResult<MaterialCompareDO> selectPage(MaterialComparePageReqVO reqVO) {
        Page<MaterialCompareDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<MaterialCompareDO> wrapper = new LambdaQueryWrapperX<MaterialCompareDO>()
            .eqIfPresent(MaterialCompareDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(MaterialCompareDO::getLeftFileId, reqVO.getLeftFileId())
            .eqIfPresent(MaterialCompareDO::getRightFileId, reqVO.getRightFileId())
            .eqIfPresent(MaterialCompareDO::getLeftMaterialId, reqVO.getLeftMaterialId())
            .eqIfPresent(MaterialCompareDO::getRightMaterialId, reqVO.getRightMaterialId())
            .eqIfPresent(MaterialCompareDO::getUrl, reqVO.getUrl())
            .eqIfPresent(MaterialCompareDO::getRemark, reqVO.getRemark())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(MaterialCompareDO::getAddTime);
        }
        Page<MaterialCompareDO> materialComparePage = selectPage(page, wrapper);
        return new PageResult<>(materialComparePage.getRecords(), materialComparePage.getTotal());
    }
    default List<MaterialCompareDO> selectList(MaterialCompareListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MaterialCompareDO>()
            .eqIfPresent(MaterialCompareDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(MaterialCompareDO::getLeftFileId, reqVO.getLeftFileId())
            .eqIfPresent(MaterialCompareDO::getRightFileId, reqVO.getRightFileId())
            .eqIfPresent(MaterialCompareDO::getLeftMaterialId, reqVO.getLeftMaterialId())
            .eqIfPresent(MaterialCompareDO::getRightMaterialId, reqVO.getRightMaterialId())
            .eqIfPresent(MaterialCompareDO::getUrl, reqVO.getUrl())
            .eqIfPresent(MaterialCompareDO::getRemark, reqVO.getRemark())
        .orderByDesc(MaterialCompareDO::getAddTime));    }


    }
