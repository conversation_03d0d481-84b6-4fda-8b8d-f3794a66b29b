package com.rs.module.dam.ocr.gosuncn;

import com.rs.module.dam.config.IspV2ConfigUtil;
import com.rs.module.dam.constant.OcrConstants;

/**
 * gosuncn-高新兴OCR调用类型枚举类
 * <AUTHOR>
 * @date 2025年6月5日
 */
public enum GoInvokeTypeEnum {
	
	//智能编目
	ZNBM(IspV2ConfigUtil.getZnbmCode(), OcrConstants.OCR_INVOKE_TYPE_ZNBM),
	
	//空白页、重复页、瑕疵页检测回调接口
	KBCFXC(IspV2ConfigUtil.getKbcfxcCode(), OcrConstants.OCR_INVOKE_TYPE_CHECK_KBCFXC),
	
	//瑕疵页矫正
	XCYJZ(IspV2ConfigUtil.getXcyjzCode(), OcrConstants.OCR_INVOKE_TYPE_XCJZ),
	
	//图片方向合规性检查
	TPFXJZ(IspV2ConfigUtil.getTpfxjzCode(), OcrConstants.OCR_INVOKE_TYPE_CHECK_TPFX),
	
	//图片歪斜矫正
	TPWXJZ(IspV2ConfigUtil.getTpwxjzCode(), OcrConstants.OCR_INVOKE_TYPE_TPWXJZ),
	
	//签名、签章、捺印审查
	QMQZNY(IspV2ConfigUtil.getQmqznyCode(), OcrConstants.OCR_INVOKE_TYPE_CHECK_QMQZNY),
	
	//双层PDF转换
	SCPDF(IspV2ConfigUtil.getScpdfCode(), OcrConstants.OCR_INVOKE_TYPE_SCPDFZH),
	
	//文书信息提取
	WSXXTQ(IspV2ConfigUtil.getWsxxtqCode(), OcrConstants.OCR_INVOKE_TYPE_WSXXTQ),
	
	//二维码、条形码识别
	EWMTXM(IspV2ConfigUtil.getEwmtxmCode(), OcrConstants.OCR_INVOKE_TYPE_TXMEWMSB);

	GoInvokeTypeEnum(String serviceCode, String invokeType) {
		this.serviceCode = serviceCode;
		this.invokeType = invokeType;
	}
	
	//服务代码
	public String serviceCode;
	
	//调用类型
	public String invokeType;
	
	public String getServiceCode() {
		return serviceCode;
	}

	public void setServiceCode(String serviceCode) {
		this.serviceCode = serviceCode;
	}

	public String getInvokeType() {
		return invokeType;
	}

	public void setInvokeType(String invokeType) {
		this.invokeType = invokeType;
	}

	/**
	 * 根据服务代码获取OCR调用类型枚举
	 * @param serviceCode String 服务代码
	 * @return GoInvokeTypeEnum
	 * <AUTHOR>
	 * @date 2025年6月5日
	 */
	public static GoInvokeTypeEnum getOcrInvokeTypeEnumByCode(String serviceCode) {
		for(GoInvokeTypeEnum invokeType : GoInvokeTypeEnum.values()) {
			if(invokeType.getServiceCode().equals(serviceCode)) {
				return invokeType;
			}
		}
		
		return null;
	}
}
