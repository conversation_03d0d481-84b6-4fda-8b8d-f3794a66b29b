package com.rs.module.dam.dao.sys;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.sys.ConfigGroupDO;
import com.rs.module.dam.vo.sys.ConfigGroupListReqVO;
import com.rs.module.dam.vo.sys.ConfigGroupPageReqVO;

/**
* 卷宗配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ConfigGroupDao extends IBaseDao<ConfigGroupDO> {


    default PageResult<ConfigGroupDO> selectPage(ConfigGroupPageReqVO reqVO) {
        Page<ConfigGroupDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ConfigGroupDO> wrapper = new LambdaQueryWrapperX<ConfigGroupDO>()
            .eqIfPresent(ConfigGroupDO::getFileServer, reqVO.getFileServer())
            .eqIfPresent(ConfigGroupDO::getOpenScan, reqVO.getOpenScan())
            .eqIfPresent(ConfigGroupDO::getScanType, reqVO.getScanType())
            .eqIfPresent(ConfigGroupDO::getScanServer, reqVO.getScanServer())
            .eqIfPresent(ConfigGroupDO::getScanLicense, reqVO.getScanLicense())
            .eqIfPresent(ConfigGroupDO::getOcrServer, reqVO.getOcrServer())
            .eqIfPresent(ConfigGroupDO::getEnableBorrowApprove, reqVO.getEnableBorrowApprove())
            .eqIfPresent(ConfigGroupDO::getEnableMinorApprove, reqVO.getEnableMinorApprove())
            .eqIfPresent(ConfigGroupDO::getConfigType, reqVO.getConfigType())
            .eqIfPresent(ConfigGroupDO::getOpenOcr, reqVO.getOpenOcr())
            .eqIfPresent(ConfigGroupDO::getAppId, reqVO.getAppId())
            .eqIfPresent(ConfigGroupDO::getAppKey, reqVO.getAppKey())
            .eqIfPresent(ConfigGroupDO::getSecretKey, reqVO.getSecretKey())
            .eqIfPresent(ConfigGroupDO::getMakeDoublePdf, reqVO.getMakeDoublePdf())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(ConfigGroupDO::getAddTime);
        }
        Page<ConfigGroupDO> configGroupPage = selectPage(page, wrapper);
        return new PageResult<>(configGroupPage.getRecords(), configGroupPage.getTotal());
    }
    default List<ConfigGroupDO> selectList(ConfigGroupListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ConfigGroupDO>()
            .eqIfPresent(ConfigGroupDO::getFileServer, reqVO.getFileServer())
            .eqIfPresent(ConfigGroupDO::getOpenScan, reqVO.getOpenScan())
            .eqIfPresent(ConfigGroupDO::getScanType, reqVO.getScanType())
            .eqIfPresent(ConfigGroupDO::getScanServer, reqVO.getScanServer())
            .eqIfPresent(ConfigGroupDO::getScanLicense, reqVO.getScanLicense())
            .eqIfPresent(ConfigGroupDO::getOcrServer, reqVO.getOcrServer())
            .eqIfPresent(ConfigGroupDO::getEnableBorrowApprove, reqVO.getEnableBorrowApprove())
            .eqIfPresent(ConfigGroupDO::getEnableMinorApprove, reqVO.getEnableMinorApprove())
            .eqIfPresent(ConfigGroupDO::getConfigType, reqVO.getConfigType())
            .eqIfPresent(ConfigGroupDO::getOpenOcr, reqVO.getOpenOcr())
            .eqIfPresent(ConfigGroupDO::getAppId, reqVO.getAppId())
            .eqIfPresent(ConfigGroupDO::getAppKey, reqVO.getAppKey())
            .eqIfPresent(ConfigGroupDO::getSecretKey, reqVO.getSecretKey())
            .eqIfPresent(ConfigGroupDO::getMakeDoublePdf, reqVO.getMakeDoublePdf())
        .orderByDesc(ConfigGroupDO::getAddTime));    }


    }
