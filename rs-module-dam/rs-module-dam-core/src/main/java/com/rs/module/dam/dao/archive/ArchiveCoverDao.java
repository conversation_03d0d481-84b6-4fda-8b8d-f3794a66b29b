package com.rs.module.dam.dao.archive;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.archive.ArchiveCoverDO;
import com.rs.module.dam.vo.archive.ArchiveCoverListReqVO;
import com.rs.module.dam.vo.archive.ArchiveCoverPageReqVO;

/**
* 卷宗封面 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ArchiveCoverDao extends IBaseDao<ArchiveCoverDO> {


    default PageResult<ArchiveCoverDO> selectPage(ArchiveCoverPageReqVO reqVO) {
        Page<ArchiveCoverDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ArchiveCoverDO> wrapper = new LambdaQueryWrapperX<ArchiveCoverDO>()
            .eqIfPresent(ArchiveCoverDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(ArchiveCoverDO::getJzmc, reqVO.getJzmc())
            .eqIfPresent(ArchiveCoverDO::getJm, reqVO.getJm())
            .eqIfPresent(ArchiveCoverDO::getPartCatalogId, reqVO.getPartCatalogId())
            .eqIfPresent(ArchiveCoverDO::getFjlx, reqVO.getFjlx())
            .eqIfPresent(ArchiveCoverDO::getPdfUrl, reqVO.getPdfUrl())
            .eqIfPresent(ArchiveCoverDO::getDoublePdfUrl, reqVO.getDoublePdfUrl())
            .eqIfPresent(ArchiveCoverDO::getImageUrl, reqVO.getImageUrl())
            .eqIfPresent(ArchiveCoverDO::getPreviewUrl, reqVO.getPreviewUrl())
            .eqIfPresent(ArchiveCoverDO::getCoverPage, reqVO.getCoverPage())
            .eqIfPresent(ArchiveCoverDO::getXh, reqVO.getXh())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(ArchiveCoverDO::getAddTime);
        }
        Page<ArchiveCoverDO> archiveCoverPage = selectPage(page, wrapper);
        return new PageResult<>(archiveCoverPage.getRecords(), archiveCoverPage.getTotal());
    }
    default List<ArchiveCoverDO> selectList(ArchiveCoverListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ArchiveCoverDO>()
            .eqIfPresent(ArchiveCoverDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(ArchiveCoverDO::getJzmc, reqVO.getJzmc())
            .eqIfPresent(ArchiveCoverDO::getJm, reqVO.getJm())
            .eqIfPresent(ArchiveCoverDO::getPartCatalogId, reqVO.getPartCatalogId())
            .eqIfPresent(ArchiveCoverDO::getFjlx, reqVO.getFjlx())
            .eqIfPresent(ArchiveCoverDO::getPdfUrl, reqVO.getPdfUrl())
            .eqIfPresent(ArchiveCoverDO::getDoublePdfUrl, reqVO.getDoublePdfUrl())
            .eqIfPresent(ArchiveCoverDO::getImageUrl, reqVO.getImageUrl())
            .eqIfPresent(ArchiveCoverDO::getPreviewUrl, reqVO.getPreviewUrl())
            .eqIfPresent(ArchiveCoverDO::getCoverPage, reqVO.getCoverPage())
            .eqIfPresent(ArchiveCoverDO::getXh, reqVO.getXh())
        .orderByDesc(ArchiveCoverDO::getAddTime));    }


    }
