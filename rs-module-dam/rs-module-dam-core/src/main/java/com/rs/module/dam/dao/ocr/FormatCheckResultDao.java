package com.rs.module.dam.dao.ocr;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.ocr.FormatCheckResultDO;
import com.rs.module.dam.vo.ocr.FormatCheckResultListReqVO;
import com.rs.module.dam.vo.ocr.FormatCheckResultPageReqVO;

/**
* ocr格式化审查结果 Dao
*
* <AUTHOR>
*/
@Mapper
public interface FormatCheckResultDao extends IBaseDao<FormatCheckResultDO> {


    default PageResult<FormatCheckResultDO> selectPage(FormatCheckResultPageReqVO reqVO) {
        Page<FormatCheckResultDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<FormatCheckResultDO> wrapper = new LambdaQueryWrapperX<FormatCheckResultDO>()
            .eqIfPresent(FormatCheckResultDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(FormatCheckResultDO::getJobId, reqVO.getJobId())
            .eqIfPresent(FormatCheckResultDO::getJobType, reqVO.getJobType())
            .eqIfPresent(FormatCheckResultDO::getMaterialId, reqVO.getMaterialId())
            .eqIfPresent(FormatCheckResultDO::getFileId, reqVO.getFileId())
            .eqIfPresent(FormatCheckResultDO::getFileUrl, reqVO.getFileUrl())
            .eqIfPresent(FormatCheckResultDO::getCheckType, reqVO.getCheckType())
            .eqIfPresent(FormatCheckResultDO::getXh, reqVO.getXh())
            .eqIfPresent(FormatCheckResultDO::getPartCatalogId, reqVO.getPartCatalogId())
            .eqIfPresent(FormatCheckResultDO::getCatalogId, reqVO.getCatalogId())
            .likeIfPresent(FormatCheckResultDO::getCatalogName, reqVO.getCatalogName())
            .eqIfPresent(FormatCheckResultDO::getRepeatMark, reqVO.getRepeatMark())
            .eqIfPresent(FormatCheckResultDO::getSsfcsimResult, reqVO.getSsfcsimResult())
            .eqIfPresent(FormatCheckResultDO::getMissSeal, reqVO.getMissSeal())
            .eqIfPresent(FormatCheckResultDO::getMissSignature, reqVO.getMissSignature())
            .eqIfPresent(FormatCheckResultDO::getMissFingerprint, reqVO.getMissFingerprint())
            .eqIfPresent(FormatCheckResultDO::getFormatResult, reqVO.getFormatResult())
            .eqIfPresent(FormatCheckResultDO::getFileInfo, reqVO.getFileInfo())
            .eqIfPresent(FormatCheckResultDO::getShowData, reqVO.getShowData())
            .eqIfPresent(FormatCheckResultDO::getWidth, reqVO.getWidth())
            .eqIfPresent(FormatCheckResultDO::getHeight, reqVO.getHeight())
            .eqIfPresent(FormatCheckResultDO::getFlawData, reqVO.getFlawData())
            .eqIfPresent(FormatCheckResultDO::getLoseInfo, reqVO.getLoseInfo())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(FormatCheckResultDO::getAddTime);
        }
        Page<FormatCheckResultDO> formatCheckResultPage = selectPage(page, wrapper);
        return new PageResult<>(formatCheckResultPage.getRecords(), formatCheckResultPage.getTotal());
    }
    default List<FormatCheckResultDO> selectList(FormatCheckResultListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<FormatCheckResultDO>()
            .eqIfPresent(FormatCheckResultDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(FormatCheckResultDO::getJobId, reqVO.getJobId())
            .eqIfPresent(FormatCheckResultDO::getJobType, reqVO.getJobType())
            .eqIfPresent(FormatCheckResultDO::getMaterialId, reqVO.getMaterialId())
            .eqIfPresent(FormatCheckResultDO::getFileId, reqVO.getFileId())
            .eqIfPresent(FormatCheckResultDO::getFileUrl, reqVO.getFileUrl())
            .eqIfPresent(FormatCheckResultDO::getCheckType, reqVO.getCheckType())
            .eqIfPresent(FormatCheckResultDO::getXh, reqVO.getXh())
            .eqIfPresent(FormatCheckResultDO::getPartCatalogId, reqVO.getPartCatalogId())
            .eqIfPresent(FormatCheckResultDO::getCatalogId, reqVO.getCatalogId())
            .likeIfPresent(FormatCheckResultDO::getCatalogName, reqVO.getCatalogName())
            .eqIfPresent(FormatCheckResultDO::getRepeatMark, reqVO.getRepeatMark())
            .eqIfPresent(FormatCheckResultDO::getSsfcsimResult, reqVO.getSsfcsimResult())
            .eqIfPresent(FormatCheckResultDO::getMissSeal, reqVO.getMissSeal())
            .eqIfPresent(FormatCheckResultDO::getMissSignature, reqVO.getMissSignature())
            .eqIfPresent(FormatCheckResultDO::getMissFingerprint, reqVO.getMissFingerprint())
            .eqIfPresent(FormatCheckResultDO::getFormatResult, reqVO.getFormatResult())
            .eqIfPresent(FormatCheckResultDO::getFileInfo, reqVO.getFileInfo())
            .eqIfPresent(FormatCheckResultDO::getShowData, reqVO.getShowData())
            .eqIfPresent(FormatCheckResultDO::getWidth, reqVO.getWidth())
            .eqIfPresent(FormatCheckResultDO::getHeight, reqVO.getHeight())
            .eqIfPresent(FormatCheckResultDO::getFlawData, reqVO.getFlawData())
            .eqIfPresent(FormatCheckResultDO::getLoseInfo, reqVO.getLoseInfo())
        .orderByDesc(FormatCheckResultDO::getAddTime));
    }

    /**
     * 根据参数获取检测结果中的文件Id
     * @param queryParams Map<String, Object> 查询参数
     * @return List<String>
     */
    List<String> selectFileIdByParams(Map<String, Object> queryParams);
    
    /**
     * 更新格式化审查结果的任务Id
     * @param newJobId String 新的任务Id
     * @param oldJobId String 旧的任务Id
     */
    void updateCheckResultJobId(@Param("newJobId") String newJobId, @Param("oldJobId") String oldJobId);
}
