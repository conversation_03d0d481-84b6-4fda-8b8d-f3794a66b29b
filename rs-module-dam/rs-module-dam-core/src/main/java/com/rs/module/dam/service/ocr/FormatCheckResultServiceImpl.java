package com.rs.module.dam.service.ocr;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.dao.ocr.FormatCheckResultDao;
import com.rs.module.dam.entity.ocr.FormatCheckResultDO;
import com.rs.module.dam.vo.ocr.FormatCheckResultListReqVO;
import com.rs.module.dam.vo.ocr.FormatCheckResultPageReqVO;
import com.rs.module.dam.vo.ocr.FormatCheckResultSaveReqVO;


/**
 * ocr格式化审查结果 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FormatCheckResultServiceImpl extends BaseServiceImpl<FormatCheckResultDao, FormatCheckResultDO> implements FormatCheckResultService {

    @Resource
    private FormatCheckResultDao formatCheckResultDao;

    @Override
    public String createFormatCheckResult(FormatCheckResultSaveReqVO createReqVO) {
        // 插入
        FormatCheckResultDO formatCheckResult = BeanUtils.toBean(createReqVO, FormatCheckResultDO.class);
        formatCheckResultDao.insert(formatCheckResult);
        // 返回
        return formatCheckResult.getId();
    }

    @Override
    public void updateFormatCheckResult(FormatCheckResultSaveReqVO updateReqVO) {
        // 校验存在
        validateFormatCheckResultExists(updateReqVO.getId());
        // 更新
        FormatCheckResultDO updateObj = BeanUtils.toBean(updateReqVO, FormatCheckResultDO.class);
        formatCheckResultDao.updateById(updateObj);
    }

    @Override
    public void deleteFormatCheckResult(String id) {
        // 校验存在
        validateFormatCheckResultExists(id);

        // 删除
        formatCheckResultDao.deleteById(id);
    }

    private void validateFormatCheckResultExists(String id) {
        if (formatCheckResultDao.selectById(id) == null) {
            throw new ServerException("ocr格式化审查结果数据不存在");
        }
    }

    @Override
    public FormatCheckResultDO getFormatCheckResult(String id) {
        return formatCheckResultDao.selectById(id);
    }

    @Override
    public PageResult<FormatCheckResultDO> getFormatCheckResultPage(FormatCheckResultPageReqVO pageReqVO) {
        return formatCheckResultDao.selectPage(pageReqVO);
    }

    @Override
    public List<FormatCheckResultDO> getFormatCheckResultList(FormatCheckResultListReqVO listReqVO) {
        return formatCheckResultDao.selectList(listReqVO);
    }

    /**
     * 更新检测结果是否展示
     * @param jobId String 任务Id
     * @param imageId String 图片Id
     * @param checkTypeList List<String> 检测类型集合
     * @param showData String 是否展示
     */
    @Override
    public void updateShowData(String jobId, String imageId, List<String> checkTypeList, String showData) {
    	this.lambdaUpdate().eq(FormatCheckResultDO::getJobId, jobId)
    		.eq(FormatCheckResultDO::getFileId, imageId)
    		.in(FormatCheckResultDO::getCheckType, checkTypeList)
    		.set(FormatCheckResultDO::getShowData, showData)
    		.update();
    }
    
    /**
     * 更新检测结果是否展示
     * @param jobId String 任务Id
     * @param imageId String 图片Id
     * @param checkType String 检测类型
     * @param showData String 是否展示
     */
    @Override
    public void updateShowData(String jobId, String imageId, String checkType, String showData) {
    	this.lambdaUpdate().eq(FormatCheckResultDO::getJobId, jobId)
    		.eq(FormatCheckResultDO::getFileId, imageId)
    		.eq(FormatCheckResultDO::getCheckType, checkType)
    		.set(FormatCheckResultDO::getShowData, showData)
    		.update();
    }
    
    /**
     * 根据参数获取检测结果中的文件Id
     * @param queryParams Map<String, Object> 查询参数
     * @return List<String>
     */
    @Override
    public List<String> selectFileIdByParams(Map<String, Object> queryParams){
    	return formatCheckResultDao.selectFileIdByParams(queryParams);
    }
    
    /**
     * 更新格式化审查结果的任务Id
     * @param newJobId String 新的任务Id
     * @param oldJobId String 旧的任务Id
     */
    @Override
    public void updateCheckResultJobId(String newJobId, String oldJobId) {
    	formatCheckResultDao.updateCheckResultJobId(newJobId, oldJobId);
    }
}
