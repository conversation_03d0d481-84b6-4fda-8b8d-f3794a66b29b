package com.rs.module.dam.service.log;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.dao.log.IspInvokingDao;
import com.rs.module.dam.entity.log.IspInvokingDO;
import com.rs.module.dam.vo.log.IspInvokingListReqVO;
import com.rs.module.dam.vo.log.IspInvokingPageReqVO;
import com.rs.module.dam.vo.log.IspInvokingSaveReqVO;


/**
 * 智能服务调用日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class IspInvokingServiceImpl extends BaseServiceImpl<IspInvokingDao, IspInvokingDO> implements IspInvokingService {

    @Resource
    private IspInvokingDao ispInvokingDao;

    @Override
    public String createIspInvoking(IspInvokingSaveReqVO createReqVO) {
        // 插入
        IspInvokingDO ispInvoking = BeanUtils.toBean(createReqVO, IspInvokingDO.class);
        ispInvokingDao.insert(ispInvoking);
        // 返回
        return ispInvoking.getId();
    }

    @Override
    public void updateIspInvoking(IspInvokingSaveReqVO updateReqVO) {
        // 校验存在
        validateIspInvokingExists(updateReqVO.getId());
        // 更新
        IspInvokingDO updateObj = BeanUtils.toBean(updateReqVO, IspInvokingDO.class);
        ispInvokingDao.updateById(updateObj);
    }

    @Override
    public void deleteIspInvoking(String id) {
        // 校验存在
        validateIspInvokingExists(id);
        // 删除
        ispInvokingDao.deleteById(id);
    }

    private void validateIspInvokingExists(String id) {
        if (ispInvokingDao.selectById(id) == null) {
            throw new ServerException("智能服务调用日志数据不存在");
        }
    }

    @Override
    public IspInvokingDO getIspInvoking(String id) {
        return ispInvokingDao.selectById(id);
    }

    @Override
    public PageResult<IspInvokingDO> getIspInvokingPage(IspInvokingPageReqVO pageReqVO) {
        return ispInvokingDao.selectPage(pageReqVO);
    }

    @Override
    public List<IspInvokingDO> getIspInvokingList(IspInvokingListReqVO listReqVO) {
        return ispInvokingDao.selectList(listReqVO);
    }


}
