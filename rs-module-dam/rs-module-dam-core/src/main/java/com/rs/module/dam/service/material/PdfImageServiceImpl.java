package com.rs.module.dam.service.material;

import java.util.List;

import javax.annotation.Resource;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.dao.material.PdfImageDao;
import com.rs.module.dam.entity.material.PdfImageDO;
import com.rs.module.dam.vo.material.PdfImageListReqVO;
import com.rs.module.dam.vo.material.PdfImagePageReqVO;
import com.rs.module.dam.vo.material.PdfImageSaveReqVO;


/**
 * pdf转换图片 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PdfImageServiceImpl extends BaseServiceImpl<PdfImageDao, PdfImageDO> implements PdfImageService {

    @Resource
    private PdfImageDao pdfImageDao;

    @Override
    public String createPdfImage(PdfImageSaveReqVO createReqVO) {
        // 插入
        PdfImageDO pdfImage = BeanUtils.toBean(createReqVO, PdfImageDO.class);
        pdfImageDao.insert(pdfImage);
        // 返回
        return pdfImage.getId();
    }

    @Override
    public void updatePdfImage(PdfImageSaveReqVO updateReqVO) {
        // 校验存在
        validatePdfImageExists(updateReqVO.getId());
        // 更新
        PdfImageDO updateObj = BeanUtils.toBean(updateReqVO, PdfImageDO.class);
        pdfImageDao.updateById(updateObj);
    }

    @Override
    public void deletePdfImage(String id) {
        // 校验存在
        validatePdfImageExists(id);
        // 删除
        pdfImageDao.deleteById(id);
    }

    private void validatePdfImageExists(String id) {
        if (pdfImageDao.selectById(id) == null) {
            throw new ServerException("pdf转换图片数据不存在");
        }
    }

    @Override
    public PdfImageDO getPdfImage(String id) {
        return pdfImageDao.selectById(id);
    }

    @Override
    public PageResult<PdfImageDO> getPdfImagePage(PdfImagePageReqVO pageReqVO) {
        return pdfImageDao.selectPage(pageReqVO);
    }

    @Override
    public List<PdfImageDO> getPdfImageList(PdfImageListReqVO listReqVO) {
        return pdfImageDao.selectList(listReqVO);
    }

    /**
     * 查询pdf已转换成功的图片
     * @param pdfId String pdf主键
     * @return List<String>
     */
    @Override
    public List<String> selectImageIdWithPdfId(@Param("pdfId") String pdfId) {
    	return pdfImageDao.selectImageIdWithPdfId(pdfId);
    }
}
