package com.rs.module.dam.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 卷宗配置 DO
 *
 * <AUTHOR>
 */
@TableName("dam_sys_config_group")
@KeySequence("dam_sys_config_group_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfigGroupDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 文件服务器地址
     */
    private String fileServer;
    /**
     * 是否开启扫描仪 1:是，0:否
     */
    private String openScan;
    /**
     * 扫描仪类型
     */
    private String scanType;
    /**
     * 扫描服务地址
     */
    private String scanServer;
    /**
     * 扫描仪授权码
     */
    private String scanLicense;
    /**
     * ocr服务地址
     */
    private String ocrServer;
    /**
     * 是否开启借阅审批 1:是, 0:否
     */
    private String enableBorrowApprove;
    /**
     * 是否开启未成年人特殊审批 1:是, 0:否
     */
    private String enableMinorApprove;
    /**
     * 配置方式 1:默认配置, 2:定制化配置
     */
    private String configType;
    /**
     * 是否开启ocr服务, 0:否, 1:是
     */
    private String openOcr;
    /**
     * 对接isp的appId
     */
    private String appId;
    /**
     * 对接isp的appKey
     */
    private String appKey;
    /**
     * 对接isp的secretKey
     */
    private String secretKey;
    /**
     * 组卷后是否生成双层pdf
     */
    private String makeDoublePdf;

}
