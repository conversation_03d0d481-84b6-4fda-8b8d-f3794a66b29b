package com.rs.module.dam.websocket;

import java.io.IOException;

import javax.websocket.Session;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.dam.config.WebSocketServer;

/**
 * WebSocket工具类
 * <AUTHOR>
 * @date 2025年4月22日
 */
public class WebSocketUtil {

	/**
     * 发送消息
     * @param session Session 会话
     * @param message String 消息内容
     * @throws IOException
     */
    public static void sendMessage(Session session, String message) throws IOException {
        if(session != null && session.isOpen()){
            synchronized (session) {
                session.getBasicRemote().sendText(message);
            }
        }
    }
    
    /**
     * 构建基础消息
     * @param code String 代码
     * @param msg String 消息内容
     * @param jgrybm String 监管人员编码
     * @return JSONObject
     */
    public static JSONObject buildBaseMessage(String code, String msg, String jgrybm) {
    	JSONObject sendData = new JSONObject();
        sendData.put("code", code);
        sendData.put("msg", msg);
        sendData.put("jgrybm", jgrybm);
        return sendData;
    }
    
    /**
     * 发送消息
     * @param code String 代码
     * @param msg String 消息内容
     * @param jgrybm String 监管人员编码
     */
    public static void sendMessage(String code, String msg, String jgrybm) {
    	JSONObject sendData = buildBaseMessage(code, msg, jgrybm);
        WebSocketServer.sendInfo(null, sendData.toJSONString());
    }
}
