package com.rs.module.dam.util;

import java.io.IOException;

import com.itextpdf.io.font.FontConstants;
import com.itextpdf.kernel.events.Event;
import com.itextpdf.kernel.events.IEventHandler;
import com.itextpdf.kernel.events.PdfDocumentEvent;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 文本Footer事件
 * <AUTHOR>
 * @date 2025年4月25日
 */
@SuppressWarnings("deprecation")
@Data
@Slf4j
public class TextFooterEventHandler implements IEventHandler {

	//当前文档
    protected Document doc;
    
    //当前页
    protected int page;
    
    //起始页
    private int startPage = 9999;
    
    //结束页
    private int endPage = 9999;
    
    //名称
    private String name;

    public TextFooterEventHandler(Document doc) {
        this.doc = doc;
    }

	@Override
    public void handleEvent(Event event) {
        PdfDocumentEvent docEvent = (PdfDocumentEvent) event;
        PdfCanvas canvas = new PdfCanvas(docEvent.getPage());
        Rectangle pageSize = docEvent.getPage().getPageSize();
        page++;
        if (page < startPage || page >= endPage){
            return;
        }

        canvas.beginText();
        try {
            canvas.setFontAndSize(PdfFontFactory.createFont(FontConstants.HELVETICA_OBLIQUE), 15);
        } catch (IOException e) {
        	log.error("【pdf页号】发生异常：{}", e.getMessage());
        }
        
        canvas.moveText(pageSize.getRight() - doc.getRightMargin(),pageSize.getTop() - doc.getTopMargin())
                .showText(""+(page - startPage + 1))
                .endText()
                .release();
    }
}
