package com.rs.module.dam.ocr.gosuncn.callback;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.StringUtil;
import com.rs.module.dam.constant.OcrConstants;
import com.rs.module.dam.entity.log.IspInvokingDO;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.ocr.gosuncn.GoOcrCallbackParam;
import com.rs.module.dam.ocr.gosuncn.GoOcrCallbackParam.SerDatas;
import com.rs.module.dam.ocr.gosuncn.GoOcrHttpStatus;
import com.rs.module.dam.ocr.gosuncn.GoUtil;
import com.rs.module.dam.ocr.handler.OcrHandler;
import com.rs.module.dam.ocr.util.OcrUtil;
import com.rs.module.dam.service.log.IspInvokingService;
import com.rs.module.dam.service.material.MaterialInfoService;
import com.rs.module.dam.util.IpUtil;
import com.rs.module.dam.util.PdfGenerateUtil;
import com.rs.module.dam.util.RedisTemplateUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 瑕疵页矫正回调接口
 * <AUTHOR>
 * @date 2025年4月27日
 */
@Component
@Slf4j
public class XcyjzCallback implements IOcrCallback {
	
	@Autowired
	private IspInvokingService ispInvokingService;
	
	@Autowired
	private MaterialInfoService dossierMaterialInfoService;
    
	@Autowired
    OcrHandler ocrHandler;
    
    @Autowired
    RedisTemplateUtil redisTemplateUtil;
    
    /**
	 * 回调处理
	 * @param params GoOcrCallbackParam 回调参数
	 */
	@Override
	public void handle(GoOcrCallbackParam params) {
		
		//服务返回数据
		SerDatas serDatas = params.getSerDatas();
		
		//构建isp调用对象
		IspInvokingDO ispInvoking = new IspInvokingDO();
		ispInvoking.setId(StringUtil.getGuid32());
		ispInvoking.setJobId(serDatas.getBatchId());
		ispInvoking.setInput(JSON.toJSONString(params));
		ispInvoking.setSerCode(params.getSerCode());
		ispInvoking.setInvokeType(OcrConstants.OCR_INVOKE_TYPE_XCJZ_CALLBACK);
		ispInvoking.setRequestFrom(OcrConstants.OCR_REQUEST_FROM_FORMAT_CHECK);
		ispInvoking.setServerIp(IpUtil.getHostIp());
		ispInvoking.setCheckType(OcrConstants.OCR_INVOKE_TYPE_XCJZ_CALLBACK);
		
		//开始调用时间
		long begin = System.currentTimeMillis();
		
		try {
			//回调返回结果
			JSONArray callbackResults = GoUtil.getOcrResultsArray(serDatas);
			
			//材料Id = 批次Id
			String materialId = serDatas.getBatchId();
			
			//材料
			MaterialInfoDO materialInfo = dossierMaterialInfoService.getById(materialId);
			
			//设置isp调用对象
			ispInvoking.setJgrybm(materialInfo.getJgrybm());
			
			//返回数据合法
			if(callbackResults != null && materialInfo != null 
					&& (GoOcrHttpStatus.OK.getCode() == serDatas.getCode() || GoOcrHttpStatus.INCOMPLETE_RETURN.getCode() == serDatas.getCode())) {
				for(int i = 0; i < callbackResults.size(); i ++) {
					JSONObject file = callbackResults.getJSONObject(i);
					String fileId = file.getString("fileId");
					String downloadUrl = file.getString("download_url");
					if(StringUtil.isNotEmpty(fileId) && StringUtil.isNotEmpty(downloadUrl)) {
						JSONObject uploadJson = PdfGenerateUtil.upload(downloadUrl);
						if(uploadJson != null) {
							String reviseUrl = uploadJson.getString("url");
							OcrUtil.updateMaterialInfoFileUrl(materialInfo, fileId, reviseUrl);
							dossierMaterialInfoService.updateById(materialInfo);
						}
					}
				}				
				
				//更新isp调用对象
				ispInvoking.setOutput("瑕疵页矫正回调成功");
			}
			else {
				ispInvoking.setOutput("瑕疵页矫正回调异常：参数不正确");
				ispInvoking.setRequestStatus("2");
				log.error("ocr处理-格式化审查-瑕疵页矫正回调异常：参数不正确");
			}
		}
		catch (Exception e) {
			e.printStackTrace();
			ispInvoking.setOutput(e.toString());
			ispInvoking.setRequestStatus("2");
			log.error("ocr处理-格式化审查-瑕疵页矫正回调异常：{}", e);
		}
		finally {
			
			//插入isp调用对象
			ispInvoking.setRequestTime(System.currentTimeMillis() - begin);
			ispInvoking.setAddTime(new Date());
			ispInvokingService.save(ispInvoking);			
		}
	}
}
