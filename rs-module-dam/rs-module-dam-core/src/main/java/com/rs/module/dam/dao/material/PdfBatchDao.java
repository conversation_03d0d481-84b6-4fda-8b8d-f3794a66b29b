package com.rs.module.dam.dao.material;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.material.PdfBatchDO;
import com.rs.module.dam.vo.material.PdfBatchListReqVO;
import com.rs.module.dam.vo.material.PdfBatchPageReqVO;

/**
* pdf处理批次 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PdfBatchDao extends IBaseDao<PdfBatchDO> {


    default PageResult<PdfBatchDO> selectPage(PdfBatchPageReqVO reqVO) {
        Page<PdfBatchDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PdfBatchDO> wrapper = new LambdaQueryWrapperX<PdfBatchDO>()
            .eqIfPresent(PdfBatchDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PdfBatchDO::getType, reqVO.getType())
            .eqIfPresent(PdfBatchDO::getCatalogId, reqVO.getCatalogId())
            .eqIfPresent(PdfBatchDO::getPartCatalogId, reqVO.getPartCatalogId())
            .likeIfPresent(PdfBatchDO::getName, reqVO.getName())
            .eqIfPresent(PdfBatchDO::getXh, reqVO.getXh())
            .eqIfPresent(PdfBatchDO::getErrorLog, reqVO.getErrorLog())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PdfBatchDO::getAddTime);
        }
        Page<PdfBatchDO> pdfBatchPage = selectPage(page, wrapper);
        return new PageResult<>(pdfBatchPage.getRecords(), pdfBatchPage.getTotal());
    }
    default List<PdfBatchDO> selectList(PdfBatchListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PdfBatchDO>()
            .eqIfPresent(PdfBatchDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PdfBatchDO::getType, reqVO.getType())
            .eqIfPresent(PdfBatchDO::getCatalogId, reqVO.getCatalogId())
            .eqIfPresent(PdfBatchDO::getPartCatalogId, reqVO.getPartCatalogId())
            .likeIfPresent(PdfBatchDO::getName, reqVO.getName())
            .eqIfPresent(PdfBatchDO::getXh, reqVO.getXh())
            .eqIfPresent(PdfBatchDO::getErrorLog, reqVO.getErrorLog())
        .orderByDesc(PdfBatchDO::getAddTime));    }


    }
