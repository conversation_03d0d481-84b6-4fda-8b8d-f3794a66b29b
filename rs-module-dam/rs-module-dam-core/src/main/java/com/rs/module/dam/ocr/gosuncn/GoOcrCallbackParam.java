package com.rs.module.dam.ocr.gosuncn;

import com.alibaba.fastjson.JSONArray;

/**
 * gosuncn-高新兴OCR回调参数
 * <AUTHOR>
 * @date 2025年4月26日
 */
public class GoOcrCallbackParam {

	//服务代码
	private String serCode;
	
	//服务数据
	private SerDatas serDatas;
	
	public String getSerCode() {
		return serCode;
	}

	public void setSerCode(String serCode) {
		this.serCode = serCode;
	}

	public SerDatas getSerDatas() {
		return serDatas;
	}

	public void setSerDatas(SerDatas serDatas) {
		this.serDatas = serDatas;
	}

	/**
	 * 服务数据对象
	 * <AUTHOR>
	 * @date 2024年3月22日
	 */
	public class SerDatas{
		
		//用户Id
		private String userId;
		
		//批次Id
		private String batchId;
		
		//状态码
		private Integer code;
		
		//处理进度
		private float progress;
		
		//失败的fileId的列表
		private JSONArray failed_ids;
		
		//处理结果
		private Object results;

		public String getUserId() {
			return userId;
		}

		public void setUserId(String userId) {
			this.userId = userId;
		}

		public String getBatchId() {
			return batchId;
		}

		public void setBatchId(String batchId) {
			this.batchId = batchId;
		}

		public Integer getCode() {
			return code;
		}

		public void setCode(Integer code) {
			this.code = code;
		}

		public float getProgress() {
			return progress;
		}

		public void setProgress(float progress) {
			this.progress = progress;
		}

		public JSONArray getFailed_ids() {
			return failed_ids;
		}

		public void setFailed_ids(JSONArray failed_ids) {
			this.failed_ids = failed_ids;
		}

		public Object getResults() {
			return results;
		}

		public void setResults(Object results) {
			this.results = results;
		}
	}
}
