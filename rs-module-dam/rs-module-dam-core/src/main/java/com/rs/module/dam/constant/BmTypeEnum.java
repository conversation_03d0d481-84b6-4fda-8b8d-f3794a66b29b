package com.rs.module.dam.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 编目类型枚举类
 * <AUTHOR>
 * @date 2025年4月20日
 */
@AllArgsConstructor
@Getter
public enum BmTypeEnum {

	UN_BM("0", "未编目"),
	
	B<PERSON>("1", "已编目"),
	
	Z<PERSON>("2", "已组卷"),
	
	KSZJ("3", "快速组卷");

	//编目类型
	private String type;
	
	//状态描述
	private String remark;
	
	/**
	 * 根据类型获取编目类型
	 * @param type String 编目类型
	 * @return BmTypeEnum
	 */
	public static BmTypeEnum getByType(String type) {
		for(BmTypeEnum bmTypeEnum : values()) {
			if(bmTypeEnum.getType().equals(type)) {
				return bmTypeEnum;
			}
		}
		return null;
	}
}
