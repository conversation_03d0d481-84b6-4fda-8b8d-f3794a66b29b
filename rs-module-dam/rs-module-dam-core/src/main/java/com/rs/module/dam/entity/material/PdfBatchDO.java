package com.rs.module.dam.entity.material;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * pdf处理批次 DO
 *
 * <AUTHOR>
 */
@TableName("dam_pdf_batch")
@KeySequence("dam_pdf_batch_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PdfBatchDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 类型(0：未OCR,1:编目,2：组卷 3: 快速组卷)
     */
    private String type;
    /**
     * 目录Id
     */
    private String catalogId;
    /**
     * 分卷目录Id
     */
    private String partCatalogId;
    /**
     * 编目目录名称
     */
    private String name;
    /**
     * 目录序号
     */
    private Integer xh;
    /**
     * 任务处理失败否日志
     */
    private String errorLog;
    /**
     * 批次Id
     */
    private String batchId;
    /**
     * 业务Id
     */
    private String businessId;

    @TableField(exist = false)
    private String templateId;
}
