package com.rs.module.dam.service.ocr;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.dao.ocr.FormatCheckJobDao;
import com.rs.module.dam.entity.ocr.FormatCheckJobDO;
import com.rs.module.dam.vo.ocr.FormatCheckJobListReqVO;
import com.rs.module.dam.vo.ocr.FormatCheckJobPageReqVO;
import com.rs.module.dam.vo.ocr.FormatCheckJobSaveReqVO;


/**
 * ocr格式化审查任务 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FormatCheckJobServiceImpl extends BaseServiceImpl<FormatCheckJobDao, FormatCheckJobDO> implements FormatCheckJobService {

    @Resource
    private FormatCheckJobDao formatCheckJobDao;

    @Override
    public String createFormatCheckJob(FormatCheckJobSaveReqVO createReqVO) {
        // 插入
        FormatCheckJobDO formatCheckJob = BeanUtils.toBean(createReqVO, FormatCheckJobDO.class);
        formatCheckJobDao.insert(formatCheckJob);
        // 返回
        return formatCheckJob.getId();
    }

    @Override
    public void updateFormatCheckJob(FormatCheckJobSaveReqVO updateReqVO) {
        // 校验存在
        validateFormatCheckJobExists(updateReqVO.getId());
        // 更新
        FormatCheckJobDO updateObj = BeanUtils.toBean(updateReqVO, FormatCheckJobDO.class);
        formatCheckJobDao.updateById(updateObj);
    }

    @Override
    public void deleteFormatCheckJob(String id) {
        // 校验存在
        validateFormatCheckJobExists(id);
        // 删除
        formatCheckJobDao.deleteById(id);
    }

    private void validateFormatCheckJobExists(String id) {
        if (formatCheckJobDao.selectById(id) == null) {
            throw new ServerException("ocr格式化审查任务数据不存在");
        }
    }

    @Override
    public FormatCheckJobDO getFormatCheckJob(String id) {
        return formatCheckJobDao.selectById(id);
    }

    @Override
    public PageResult<FormatCheckJobDO> getFormatCheckJobPage(FormatCheckJobPageReqVO pageReqVO) {
        return formatCheckJobDao.selectPage(pageReqVO);
    }

    @Override
    public List<FormatCheckJobDO> getFormatCheckJobList(FormatCheckJobListReqVO listReqVO) {
        return formatCheckJobDao.selectList(listReqVO);
    }


}
