package com.rs.module.dam.vo.material;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 卷宗材料标注列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MaterialMarkerListReqVO extends BaseVO {

	@ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("卷宗Id")
    private String jzId;

    @ApiModelProperty("材料Id")
    private String materialId;

    @ApiModelProperty("材料文件图片Id")
    private String fileId;

    @ApiModelProperty("标注信息")
    private String marker;

    @ApiModelProperty("编号")
    private String number;

    @ApiModelProperty("标注内容")
    private String content;

}
