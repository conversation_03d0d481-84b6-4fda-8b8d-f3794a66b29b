package com.rs.module.dam.service.archive;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.dao.archive.ArchiveJobDao;
import com.rs.module.dam.entity.archive.ArchiveJobDO;
import com.rs.module.dam.vo.archive.ArchiveJobListReqVO;
import com.rs.module.dam.vo.archive.ArchiveJobPageReqVO;
import com.rs.module.dam.vo.archive.ArchiveJobSaveReqVO;


/**
 * 卷宗归档任务 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ArchiveJobServiceImpl extends BaseServiceImpl<ArchiveJobDao, ArchiveJobDO> implements ArchiveJobService {

    @Resource
    private ArchiveJobDao archiveJobDao;

    @Override
    public String createArchiveJob(ArchiveJobSaveReqVO createReqVO) {
        // 插入
        ArchiveJobDO archiveJob = BeanUtils.toBean(createReqVO, ArchiveJobDO.class);
        archiveJobDao.insert(archiveJob);
        // 返回
        return archiveJob.getId();
    }

    @Override
    public void updateArchiveJob(ArchiveJobSaveReqVO updateReqVO) {
        // 校验存在
        validateArchiveJobExists(updateReqVO.getId());
        // 更新
        ArchiveJobDO updateObj = BeanUtils.toBean(updateReqVO, ArchiveJobDO.class);
        archiveJobDao.updateById(updateObj);
    }

    @Override
    public void deleteArchiveJob(String id) {
        // 校验存在
        validateArchiveJobExists(id);
        // 删除
        archiveJobDao.deleteById(id);
    }

    private void validateArchiveJobExists(String id) {
        if (archiveJobDao.selectById(id) == null) {
            throw new ServerException("卷宗归档任务数据不存在");
        }
    }

    @Override
    public ArchiveJobDO getArchiveJob(String id) {
        return archiveJobDao.selectById(id);
    }

    @Override
    public PageResult<ArchiveJobDO> getArchiveJobPage(ArchiveJobPageReqVO pageReqVO) {
        return archiveJobDao.selectPage(pageReqVO);
    }

    @Override
    public List<ArchiveJobDO> getArchiveJobList(ArchiveJobListReqVO listReqVO) {
        return archiveJobDao.selectList(listReqVO);
    }


}
