package com.rs.module.dam.entity.archive;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO_;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 卷宗归档任务目录 DO
 *
 * <AUTHOR>
 */
@TableName("dam_archive_job_catalog")
@KeySequence("dam_archive_job_catalog_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ArchiveJobCatalogDO extends BaseDO_ {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 批次Id
     */
    private String batchId;
    /**
     * 材料ID
     */
    private String materialId;
    /**
     * 材料序号
     */
    private Integer materialXh;
    /**
     * 文件序号
     */
    private Integer fileXh;
    /**
     * 标题
     */
    private String docTitle;
    /**
     * 分类Id
     */
    private String catalogId;
    /**
     * 任务状态（0：未完成，1：已完成，2：异常）
     */
    private String state;

    /**
     * 更新时间
     */
    private Date updateTime;
}
