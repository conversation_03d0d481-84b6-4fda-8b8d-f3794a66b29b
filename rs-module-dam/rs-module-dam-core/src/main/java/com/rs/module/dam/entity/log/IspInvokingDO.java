package com.rs.module.dam.entity.log;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO_;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 智能服务调用日志 DO
 *
 * <AUTHOR>
 */
@TableName("dam_log_isp_invoking")
@KeySequence("dam_log_isp_invoking_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IspInvokingDO extends BaseDO_ {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 任务Id
     */
    private String jobId;
    /**
     * 请求参数
     */
    private String input;
    /**
     * 返回结果
     */
    private String output;
    /**
     * isp接口serCode
     */
    private String serCode;
    /**
     * 请求接口类型(0:签名签章捺印检测结果查询,4:空白页检测,5:重复页检测, 6:瑕疵页检测,7:文书规范检测,8: 启用通用ocr基础识别服务,9:查询通用基础OCR识别结果, 10:双层PDF转换,11:图片瑕疵处理,12:图片方向合规性检测,13:查询ocr基础通用识别服务的进度)
     */
    private String invokeType;
    /**
     * 接口耗时
     */
    private Long requestTime;
    /**
     * 请求地址
     */
    private String address;
    /**
     * 请求来源(1:智能格式化审查, 2:智能编目)
     */
    private String requestFrom;
    /**
     * 请求状态(1:成功, 2:失败)
     */
    private String requestStatus;
    /**
     * 服务器ip
     */
    private String serverIp;
    /**
     * 调用接口的任务类型
     */
    private String checkType;
    /**
     * 发起检测图片的数量
     */
    private Integer fileCount;

}
