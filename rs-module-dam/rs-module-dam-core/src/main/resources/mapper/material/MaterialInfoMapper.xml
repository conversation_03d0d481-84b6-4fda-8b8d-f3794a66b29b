<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.dam.dao.material.MaterialInfoDao">

	<!-- 根据监管人员代码和材料类型获取分卷目录Id -->
    <select id="selectPartCatalogIdByJgrybmAndType" resultType="java.lang.String">
		select distinct t.part_catalog_id from dam_material_info t 
			where t.jgrybm = #{jgrybm} and t.type = #{type}
	</select>

	<!-- 获取材料上传的pdf数量 -->
	<select id="selectUploadPdfCountByType" resultType="java.lang.Integer">
		select count(t2.id) as count from dam_pdf_batch t1 
				inner join dam_pdf_upload_data t2 on t1.id = t2.batch_id
			<where>
				t1.jgrybm = #{jgrybm} and t2.material_source = '1' and t1.type in 
				<foreach item="item" collection="types" open="(" separator="," close=")">
					#{item, jdbcType=VARCHAR }
				</foreach>
			</where>
	</select>
	
	<!-- 批量更新材料序号 -->
	<update id="updateMaterialInfoXh" parameterType="java.util.List">
		<foreach collection="orderList" item="item" index="index" separator=";">
			update dossier_material_info set xh = #{item.xh} where id = #{item.id}
		</foreach>
	</update>
</mapper>
