<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.dam.dao.material.PdfUploadDataDao">

    <!-- 根据类型获取转换的pdf记录 -->
    <select id="selectConvertPdfWithType" resultType="com.rs.module.dam.entity.material.PdfUploadDataDO">
        select t1.*, t2.catalog_id, t2.part_catalog_id from dam_pdf_upload_data t1
            inner join dam_pdf_batch t2 on t1.batch_id = t2.id and t1.jgrybm = t2.jgrybm
            where t1.jgrybm = #{jgrybm} and t1.material_source = #{materialSource}
                and t2.type in <foreach item="item" collection="types" open="(" separator="," close=")">#{item}</foreach>
                and t1.status in (0, 1 ,2, 4, 5, 6)
            <if test='sortField != null and sortField != "" and sortMethod != null and sortMethod != ""'>
                order by ${sortField} ${sortMethod}
            </if>
    </select>

	<!-- 查询重复的pdf上传文件 -->
	<select id="selectRepeatPdf" resultType="com.rs.module.dam.entity.material.PdfUploadDataDO">
        select t1.* from dam_pdf_upload_data t1 
        	inner join dam_pdf_batch t2 on t1.jgrybm = t2.jgrybm and t1.batch_id = t2.id
        <where>
            t1.jgrybm = #{jgrybm}
            and t2.type = #{type}
            <!--and (t1.pdf_url = #{pdfUrl} or t1.md5 = #{md5})-->
            and (t1.pdf_name = #{pdfName} and t1.pdf_size = #{pdfSize})
            and t1.status in (0, 1 ,2, 4, 6)
            <if test='catalogId != null and catalogId != ""'>
                and t2.catalog_id = #{catalogId}
            </if>
            <if test='partCatalogId != null and partCatalogId != ""'>
                and t2.part_catalog_id = #{partCatalogId}
            </if>
            <if test='businessId != null and businessId != ""'>
                and t2.batch_id = #{batchId}
                and t2.business_id = #{businessId}
            </if>
            <if test='businessId == null or businessId == ""'>
                and t2.business_id is null
            </if>
        </where>
    </select>
</mapper>
