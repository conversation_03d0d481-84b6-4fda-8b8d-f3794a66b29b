<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.dam.dao.material.MaterialRecycleBinDao">

	<!-- 更新材料Id -->
    <update id="updateMaterialId">
		update dam_material_recycle_bin
		set material_id = #{materialInfoId}
		<if test='catalogId != null and catalogId != ""'>
			,catalog_id = #{catalogId}
		</if>
		<if test='partCatalogId != null and partCatalogId != ""'>
			,part_catalog_id = #{partCatalogId}
		</if>
		where image_id in
		<foreach item="item" collection="imageIds" open="(" separator="," close=")">#{item}</foreach>
	</update>
</mapper>
