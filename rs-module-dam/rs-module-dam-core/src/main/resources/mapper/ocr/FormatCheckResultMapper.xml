<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.dam.dao.ocr.FormatCheckResultDao">

    <select id="selectFileIdByParams" resultType="java.lang.String">
        select
            file_id
        from
            dam_ocr_format_check_result
        <where>
            <if test=' jobId != null and jobId != "" '>
                job_id = #{jobId}
            </if>
            <if test=' jgrybm != null and jgrybm != "" '>
                and jgrybm = #{jgrybm}
            </if>
            <if test=' types != null and types.size > 0 '>
                and check_type in
                <foreach item="item" collection="types" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test=' type != null and type != "" '>
                and check_type = #{type}
            </if>
            <if test=' jobType != null and jobType != "" '>
                and job_type = #{jobType}
            </if>
        </where>
    </select>
    
    <update id="updateCheckResultJobId">
        update dam_ocr_format_check_result set job_id = #{newJobId} where job_id = #{oldJobId}
    </update>
</mapper>
