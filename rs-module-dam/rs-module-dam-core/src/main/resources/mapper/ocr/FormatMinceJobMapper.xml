<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.dam.dao.ocr.FormatMinceJobDao">

	<!-- 查询格式化审查检查任务列表 -->
    <select id="selectMinceJobList" parameterType="java.util.Map" resultType="java.util.Map">
		select t1.id, t1.jgrybm, t1.job_status, t1.add_time, t1.job_id, t1.check_type, t2.ajbh, t2.ajmc from dam_ocr_format_mince_job t1
			left join dam_prisoner_info t2 on t1.jgrybm = t2.jgrybm
			<where>
	            t1.show_job = '1'
	            <if test='cm.jobStatus != ""  and cm.jobStatus != null and cm.jobStatus == 0 '>
	                and t1.job_status in ('0', '3')
	            </if>
	            <if test='cm.jobStatus != ""  and cm.jobStatus != null and cm.jobStatus == 1 '>
	                and t1.job_status in ('1', '2', '5')
	            </if>
	            <if test='cm.jobType != "" and cm.jobType != null '>
	                and t1.job_type = #{cm.jobType}
	            </if>
	            <if test='cm.jgrybm != "" and cm.jgrybm != null '>
	                and t2.jgrybm = #{cm.jgrybm}
	            </if>
	        </where>
        	order by t1.add_time desc
    </select>

	<!-- 更新格式化审查子任务结束时间 -->
	<update id="updateMinceJobEndTime">
        update
            dam_ocr_format_mince_job
        set
            end_time = now()
        <where>
            job_id = #{jobId}
            and end_time is null
        </where>
    </update>
</mapper>
