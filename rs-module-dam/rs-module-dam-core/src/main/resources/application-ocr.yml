isp:
  ocr2:
    znbmCode: 4511 			#智能编目服务代码
    kbcfxcCode: 4512		#空白页、重复页、瑕疵页检测服务代码
    xcyjzCode: 4513			#瑕疵页矫正服务代码
    tpfxjzCode: 4514		#图片方向合规性检查服务代码
    tpwxjzCode: 4515		#图片歪斜矫正服务代码
    qmqznyCode: 4516		#签名、签章、捺印审查服务代码
    scpdfCode: 4517			#双层PDF转换服务代码
    wsxxtqCode: 4518		#文书信息提取服务代码
    ewmtxmCode: 4519		#二维码、条形码识别服务代码
    ybmCode: 4510			#预编目服务代码
    commonUri: /gonlp/service_identity_authentication #OCR服务通用地址
    singleBMSize: 100   	#单次OCR识别提交的材料数(0不限制)
    enableCatalogBm: true 	#是否开启标题编目(true|false)
    mergeSameTitle: true  	#是否合并相同标题的材料    
    #重复页检测
    repeat:                 
      refinement: false		#是否精细化（默认false）
      simThreshold: 0.6		#相似度阈值
    #瑕疵页检测
    flaw:
      pixel: 300			#低于像素阈值超分
      black_ratio: 0.1		#黑边占图片比例下限阈值，高于阈值则存在黑边
      shadow_ratio: 0.25	#阴影占图片比例下限阈值，高于阈值则存在阴影
