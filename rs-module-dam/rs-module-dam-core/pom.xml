<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<parent>
		<groupId>com.rs</groupId>
		<artifactId>rs-module-dam</artifactId>
		<version>${rs.version}</version>
	</parent>
	
	<artifactId>rs-module-dam-core</artifactId>
	<packaging>jar</packaging>
	<name>${project.artifactId}</name>
	<description>监所管理-数字档案管理-核心模块</description>
	
	<dependencies>		
		<!-- 注册中心相关 begin -->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
		</dependency>
		<!-- 注册中心相关 begin -->
		
		<!-- spring相关 begin -->
		<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <!-- spring相关 end -->

		<!-- web 相关 begin -->
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-starter-web</artifactId>
		</dependency>
		<!-- web 相关 end -->

		<!-- bsp 相关 begin -->
		<dependency>
			<groupId>com.bsp</groupId>
			<artifactId>bsp-common</artifactId>
			<exclusions>
				<exclusion>
					<groupId>cn.hutool</groupId>
		  			<artifactId>hutool-all-u</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.bsp</groupId>
			<artifactId>bsp-security</artifactId>
		</dependency>
		<dependency>
			<groupId>com.bsp</groupId>
			<artifactId>bsp-plus-sdk</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>xmlbeans</artifactId>
					<groupId>org.apache.xmlbeans</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- bsp 相关 end -->

		<!-- DB 相关 begin -->
		<dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
		</dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId> <!-- 多数据源 -->
            <exclusions>
		        <exclusion>
		             <groupId>org.springframework.boot</groupId>
      				 <artifactId>spring-boot-starter</artifactId>
		        </exclusion>
		    </exclusions>
        </dependency>
        <dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
        <!-- DB 相关 end -->

		<!-- api组件 begin -->
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-module-base</artifactId>
		</dependency>
		<!-- api组件 end -->

		<!-- 技术组件 begin -->
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-starter-oss</artifactId>
		</dependency>
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-starter-job</artifactId>
		</dependency>
		<!-- 技术组件 end -->

		<!-- 工具类相关 begin -->
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-common</artifactId>
		</dependency>
		<dependency>
		  	<groupId>org.bouncycastle</groupId>
 			<artifactId>bcprov-jdk15on</artifactId>
		  	<version>1.69</version>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.zhxu</groupId>
			<artifactId>bean-searcher-boot-starter</artifactId>
		</dependency>
		<dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>fontbox</artifactId>
        </dependency>
        <dependency>
            <groupId>com.levigo.jbig2</groupId>
            <artifactId>levigo-jbig2-imageio</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>jbig2-imageio</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.jai-imageio</groupId>
            <artifactId>jai-imageio-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jai-imageio</groupId>
            <artifactId>jai-imageio-jpeg2000</artifactId>
        </dependency>
        <dependency>
		    <groupId>org.sejda.imageio</groupId>
		    <artifactId>webp-imageio</artifactId>
		</dependency>
		<dependency>
        	<groupId>org.ofdrw</groupId>
            <artifactId>ofdrw-full</artifactId>
            <exclusions>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
    				<artifactId>log4j-slf4j-impl</artifactId>
				</exclusion>
			</exclusions>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>kernel</artifactId>
        </dependency>
        <dependency>
			<groupId>com.swetake</groupId>
			<artifactId>QRCode</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
		</dependency>
	</dependencies>
</project>