package com.rs.module.dam.dao.prison;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.controller.admin.prison.vo.PublicityConfigListReqVO;
import com.rs.module.dam.controller.admin.prison.vo.PublicityConfigPageReqVO;
import com.rs.module.dam.entity.prison.PublicityConfigDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 一所一档-监所档案宣传报道配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PublicityConfigDao extends IBaseDao<PublicityConfigDO> {


    default PageResult<PublicityConfigDO> selectPage(PublicityConfigPageReqVO reqVO) {
        Page<PublicityConfigDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PublicityConfigDO> wrapper = new LambdaQueryWrapperX<PublicityConfigDO>()
            .likeIfPresent(PublicityConfigDO::getPublicityTitle, reqVO.getPublicityTitle())
            .betweenIfPresent(PublicityConfigDO::getPublicityTime, reqVO.getPublicityTime())
            .likeIfPresent(PublicityConfigDO::getPublicityContent, reqVO.getPublicityContent());
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PublicityConfigDO::getAddTime);
        }
        Page<PublicityConfigDO> publicityConfigPage = selectPage(page, wrapper);
        return new PageResult<>(publicityConfigPage.getRecords(), publicityConfigPage.getTotal());
    }
    default List<PublicityConfigDO> selectList(PublicityConfigListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PublicityConfigDO>()
            .likeIfPresent(PublicityConfigDO::getPublicityTitle, reqVO.getPublicityTitle())
            .betweenIfPresent(PublicityConfigDO::getPublicityTime, reqVO.getPublicityTime())
            .likeIfPresent(PublicityConfigDO::getPublicityContent, reqVO.getPublicityContent())
        .orderByDesc(PublicityConfigDO::getAddTime));
    }

}
