package com.rs.module.dam.job.review;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.module.dam.cons.CommonConstants;
import com.rs.module.dam.entity.borrow.BorrowApplyDO;
import com.rs.module.dam.entity.borrow.BorrowPrisonerDO;
import com.rs.module.dam.service.borrow.BorrowApplyService;
import com.rs.module.dam.service.borrow.BorrowPrisonerService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数字档案Job
 * <AUTHOR>
 * @date 2025年3月19日
 */
@Component
@Slf4j
public class BorrowJob {

	@Resource
	private BorrowApplyService borrowApplyService;
	@Resource
	private BorrowPrisonerService borrowPrisonerService;

	/**
	 * 借阅期限判断任务
	 */
	@XxlJob("borrowEndDateJudge")
	public void borrowEndDateJudge() {
		try {
			List<BorrowPrisonerDO> prisonerList = borrowPrisonerService.list(new LambdaQueryWrapper<BorrowPrisonerDO>()
					.eq(BorrowPrisonerDO::getBorrowStatus, CommonConstants.BORROW_STATUS_BORROWED));
			List<String> borrowIds = prisonerList.stream().map(BorrowPrisonerDO::getBorrowId).distinct().collect(Collectors.toList());
			List<BorrowApplyDO> applyList = borrowApplyService.listByIds(borrowIds);
			Map<String, BorrowApplyDO> applyMap = applyList.stream().collect(Collectors.toMap(
					BorrowApplyDO::getId, obj -> obj, (existing, replacement) -> replacement));
			for (BorrowPrisonerDO borrowPrisonerDO : prisonerList) {
				// 根据借阅截止时间 判断借阅是否超期
				if (ObjectUtil.isNotEmpty(borrowPrisonerDO.getBorrowEndDate()) &&
						DateUtil.endOfDay(borrowPrisonerDO.getBorrowEndDate()).getTime() <= System.currentTimeMillis()) {
					borrowPrisonerDO.setBorrowStatus(CommonConstants.BORROW_STATUS_EXPIRE);
					borrowApplyService.updateBorrowPrisonerById(borrowPrisonerDO);
					continue;
				}
				// 根据借阅时间与借阅审批通过时间 判断借阅是否超期
				if(ObjectUtil.isEmpty(borrowPrisonerDO.getBorrowEndDate())) {
					BorrowApplyDO borrowApplyDO = applyMap.get(borrowPrisonerDO.getBorrowId());
					DateTime borrowEndDate = DateUtil.offset(borrowPrisonerDO.getApproveTime(),
							DateField.DAY_OF_MONTH, borrowApplyDO.getBorrowDays());
					if (DateUtil.endOfDay(borrowEndDate).getTime() <= System.currentTimeMillis()) {
						borrowPrisonerDO.setBorrowStatus(CommonConstants.BORROW_STATUS_EXPIRE);
						borrowApplyService.updateBorrowPrisonerById(borrowPrisonerDO);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			XxlJobHelper.log("任务执行失败:" + e.getMessage());
		}
	}

}
