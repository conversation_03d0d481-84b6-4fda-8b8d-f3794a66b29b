package com.rs.module.dam.controller.admin.sys;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.dam.controller.admin.sys.vo.*;
import com.rs.module.dam.entity.sys.CatalogTemplateDO;
import com.rs.module.dam.service.sys.CatalogTemplateService;

@Api(tags = "系统配置 - 卷宗目录模板")
@RestController
@RequestMapping("/dam/sys/catalogTemplate")
@Validated
public class CatalogTemplateController {

    @Resource
    private CatalogTemplateService catalogTemplateService;

    @PostMapping("/create")
    @ApiOperation(value = "创建卷宗目录模板")
    @LogRecordAnnotation(bizModule = "dam:catalogTemplate:create", operateType = LogOperateType.CREATE, title = "创建卷宗目录模板",
    success = "创建卷宗目录模板成功", fail = "创建卷宗目录模板失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createCatalogTemplate(@Valid @RequestBody CatalogTemplateSaveReqVO createReqVO) {
        return success(catalogTemplateService.createCatalogTemplate(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新卷宗目录模板", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:catalogTemplate:update", operateType = LogOperateType.UPDATE, title = "更新卷宗目录模板",
    success = "更新卷宗目录模板成功", fail = "更新卷宗目录模板失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateCatalogTemplate(@Valid @RequestBody CatalogTemplateSaveReqVO updateReqVO) {
        catalogTemplateService.updateCatalogTemplate(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除卷宗目录模板", hidden = true)
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:catalogTemplate:delete", operateType = LogOperateType.DELETE, title = "删除卷宗目录模板",
    success = "删除卷宗目录模板成功", fail = "删除卷宗目录模板失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteCatalogTemplate(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           catalogTemplateService.deleteCatalogTemplate(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得卷宗目录模板")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:catalogTemplate:get", operateType = LogOperateType.QUERY, title = "获取卷宗目录模板",
            bizNo = "{{#id}}", success = "获取卷宗目录模板成功", fail = "获取卷宗目录模板失败", extraInfo = "{{#id}}")
    public CommonResult<CatalogTemplateRespVO> getCatalogTemplate(@RequestParam("id") String id) {
        CatalogTemplateDO catalogTemplate = catalogTemplateService.getCatalogTemplate(id);
        return success(BeanUtils.toBean(catalogTemplate, CatalogTemplateRespVO.class));
    }

    @GetMapping("/getByType")
    @ApiOperation(value = "根据模板类型获得卷宗目录模板")
    @ApiImplicitParam(name = "type", value = "模板类型")
    @LogRecordAnnotation(bizModule = "dam:catalogTemplate:get", operateType = LogOperateType.QUERY, title = "根据模板类型获取卷宗目录模板",
            bizNo = "{{#id}}", success = "根据模板类型获取卷宗目录模板成功", fail = "根据模板类型获取卷宗目录模板失败", extraInfo = "{{#id}}")
    public CommonResult<List<CatalogTemplateRespVO>> getByType(@RequestParam("type") String type) {
        List<CatalogTemplateDO> list = catalogTemplateService.list(new LambdaQueryWrapper<CatalogTemplateDO>()
                .eq(CatalogTemplateDO::getType, type));
        return success(BeanUtils.toBean(list, CatalogTemplateRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得卷宗目录模板分页", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:catalogTemplate:page", operateType = LogOperateType.QUERY, title = "获得卷宗目录模板分页",
    success = "获得卷宗目录模板分页成功", fail = "获得卷宗目录模板分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<CatalogTemplateRespVO>> getCatalogTemplatePage(@Valid @RequestBody CatalogTemplatePageReqVO pageReqVO) {
        PageResult<CatalogTemplateDO> pageResult = catalogTemplateService.getCatalogTemplatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CatalogTemplateRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得卷宗目录模板列表")
    @LogRecordAnnotation(bizModule = "dam:catalogTemplate:list", operateType = LogOperateType.QUERY, title = "获得卷宗目录模板列表",
    success = "获得卷宗目录模板列表成功", fail = "获得卷宗目录模板列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<CatalogTemplateRespVO>> getCatalogTemplateList(@Valid @RequestBody CatalogTemplateListReqVO listReqVO) {
    	List<CatalogTemplateDO> list = catalogTemplateService.getCatalogTemplateList(listReqVO);
        return success(BeanUtils.toBean(list, CatalogTemplateRespVO.class));
    }
}
