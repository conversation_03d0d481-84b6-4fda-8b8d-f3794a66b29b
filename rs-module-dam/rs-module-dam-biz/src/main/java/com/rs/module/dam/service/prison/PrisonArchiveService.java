package com.rs.module.dam.service.prison;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.controller.admin.prison.vo.PrisonArchiveListReqVO;
import com.rs.module.dam.controller.admin.prison.vo.PrisonArchivePageReqVO;
import com.rs.module.dam.controller.admin.prison.vo.PrisonArchiveSaveReqVO;
import com.rs.module.dam.entity.prison.FeatureConfigDO;
import com.rs.module.dam.entity.prison.PrisonArchiveDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 一所一档-监所档案 Service 接口
 *
 * <AUTHOR>
 */
public interface PrisonArchiveService extends IBaseService<PrisonArchiveDO>{

    /**
     * 创建一所一档-监所档案
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createArchive(@Valid PrisonArchiveSaveReqVO createReqVO);

    /**
     * 创建基础信息一所一档-监所档案
     *
     * @param prisonCode 监所编号
     * @return 编号
     */
    PrisonArchiveDO createDefaultArchive(String prisonCode);

    /**
     * 更新一所一档-监所档案
     *
     * @param updateReqVO 更新信息
     */
    String updateArchive(@Valid PrisonArchiveSaveReqVO updateReqVO);

    /**
     * 删除一所一档-监所档案
     *
     * @param id 编号
     */
    void deleteArchive(String id);

    /**
     * 获得一所一档-监所档案
     *
     * @param id 编号
     * @return 一所一档-监所档案
     */
    PrisonArchiveDO getArchive(String id);

    /**
     * 获得一所一档-监所档案
     *
     * @param prisonCode 监所编号
     * @return 一所一档-监所档案
     */
    PrisonArchiveDO getByPrisonCode(String prisonCode);

    /**
    * 获得一所一档-监所档案分页
    *
    * @param pageReqVO 分页查询
    * @return 一所一档-监所档案分页
    */
    PageResult<PrisonArchiveDO> getArchivePage(PrisonArchivePageReqVO pageReqVO);

    /**
    * 获得一所一档-监所档案列表
    *
    * @param listReqVO 查询条件
    * @return 一所一档-监所档案列表
    */
    List<PrisonArchiveDO> getArchiveList(PrisonArchiveListReqVO listReqVO);


}
