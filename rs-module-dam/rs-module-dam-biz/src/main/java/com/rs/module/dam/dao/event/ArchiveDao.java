package com.rs.module.dam.dao.event;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.event.ArchiveDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.dam.controller.admin.event.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 一事一档-事件档案 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ArchiveDao extends IBaseDao<ArchiveDO> {


    default PageResult<ArchiveDO> selectPage(ArchivePageReqVO reqVO) {
        Page<ArchiveDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ArchiveDO> wrapper = new LambdaQueryWrapperX<ArchiveDO>()
            .eqIfPresent(ArchiveDO::getEventCode, reqVO.getEventCode())
            .eqIfPresent(ArchiveDO::getEventType, reqVO.getEventType())
            .betweenIfPresent(ArchiveDO::getOccurrenceTime, reqVO.getOccurrenceTime())
            .eqIfPresent(ArchiveDO::getEventLocation, reqVO.getEventLocation())
            .eqIfPresent(ArchiveDO::getEventDescription, reqVO.getEventDescription())
            .eqIfPresent(ArchiveDO::getEventStatus, reqVO.getEventStatus())
            .eqIfPresent(ArchiveDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(ArchiveDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(ArchiveDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(ArchiveDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(ArchiveDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(ArchiveDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(ArchiveDO::getTaskId, reqVO.getTaskId());

        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(ArchiveDO::getAddTime);
        }
        Page<ArchiveDO> archivePage = selectPage(page, wrapper);
        return new PageResult<>(archivePage.getRecords(), archivePage.getTotal());
    }
    default List<ArchiveDO> selectList(ArchiveListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ArchiveDO>()
            .eqIfPresent(ArchiveDO::getEventCode, reqVO.getEventCode())
            .eqIfPresent(ArchiveDO::getEventType, reqVO.getEventType())
            .betweenIfPresent(ArchiveDO::getOccurrenceTime, reqVO.getOccurrenceTime())
            .eqIfPresent(ArchiveDO::getEventLocation, reqVO.getEventLocation())
            .eqIfPresent(ArchiveDO::getEventDescription, reqVO.getEventDescription())
            .eqIfPresent(ArchiveDO::getEventStatus, reqVO.getEventStatus())
            .eqIfPresent(ArchiveDO::getApproverSfzh, reqVO.getApproverSfzh())
            .eqIfPresent(ArchiveDO::getApproverXm, reqVO.getApproverXm())
            .betweenIfPresent(ArchiveDO::getApproverTime, reqVO.getApproverTime())
            .eqIfPresent(ArchiveDO::getApprovalResult, reqVO.getApprovalResult())
            .eqIfPresent(ArchiveDO::getApprovalComments, reqVO.getApprovalComments())
            .eqIfPresent(ArchiveDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(ArchiveDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(ArchiveDO::getAddTime));
    }

}
