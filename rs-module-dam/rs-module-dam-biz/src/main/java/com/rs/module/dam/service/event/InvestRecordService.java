package com.rs.module.dam.service.event;

import java.util.*;
import javax.validation.*;
import com.rs.module.dam.controller.admin.event.vo.*;
import com.rs.module.dam.entity.event.InvestRecordDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 一事一档-事件档案调查记录 Service 接口
 *
 * <AUTHOR>
 */
public interface InvestRecordService extends IBaseService<InvestRecordDO>{

    /**
     * 创建一事一档-事件档案调查记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createInvestRecord(@Valid InvestRecordSaveReqVO createReqVO);

    /**
     * 更新一事一档-事件档案调查记录
     *
     * @param updateReqVO 更新信息
     */
    void updateInvestRecord(@Valid InvestRecordSaveReqVO updateReqVO);

    /**
     * 删除一事一档-事件档案调查记录
     *
     * @param id 编号
     */
    void deleteInvestRecord(String id);

    /**
     * 获得一事一档-事件档案调查记录
     *
     * @param id 编号
     * @return 一事一档-事件档案调查记录
     */
    InvestRecordDO getInvestRecord(String id);



}
