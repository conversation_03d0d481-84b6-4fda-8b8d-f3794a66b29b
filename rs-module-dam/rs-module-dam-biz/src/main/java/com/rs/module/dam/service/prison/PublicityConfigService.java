package com.rs.module.dam.service.prison;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.controller.admin.prison.vo.PublicityConfigListReqVO;
import com.rs.module.dam.controller.admin.prison.vo.PublicityConfigPageReqVO;
import com.rs.module.dam.controller.admin.prison.vo.PublicityConfigSaveReqVO;
import com.rs.module.dam.entity.prison.FeatureConfigDO;
import com.rs.module.dam.entity.prison.PublicityConfigDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 一所一档-监所档案宣传报道配置 Service 接口
 *
 * <AUTHOR>
 */
public interface PublicityConfigService extends IBaseService<PublicityConfigDO>{

    /**
     * 创建一所一档-监所档案宣传报道配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPublicityConfig(@Valid PublicityConfigSaveReqVO createReqVO);

    /**
     * 更新一所一档-监所档案宣传报道配置
     *
     * @param updateReqVO 更新信息
     */
    String updatePublicityConfig(@Valid PublicityConfigSaveReqVO updateReqVO);

    /**
     * 删除一所一档-监所档案宣传报道配置
     *
     * @param id 编号
     */
    void deletePublicityConfig(String id);

    /**
     * 获得一所一档-监所档案宣传报道配置
     *
     * @param id 编号
     * @return 一所一档-监所档案宣传报道配置
     */
    PublicityConfigDO getPublicityConfig(String id);

    /**
     * 获得一所一档-监所档案宣传报道配置
     *
     * @param prisonCode 监所编号
     * @return 一所一档-监所档案宣传报道配置
     */
    List<PublicityConfigDO> getByPrisonCode(String prisonCode);

    /**
    * 获得一所一档-监所档案宣传报道配置分页
    *
    * @param pageReqVO 分页查询
    * @return 一所一档-监所档案宣传报道配置分页
    */
    PageResult<PublicityConfigDO> getPublicityConfigPage(PublicityConfigPageReqVO pageReqVO);

    /**
    * 获得一所一档-监所档案宣传报道配置列表
    *
    * @param listReqVO 查询条件
    * @return 一所一档-监所档案宣传报道配置列表
    */
    List<PublicityConfigDO> getPublicityConfigList(PublicityConfigListReqVO listReqVO);


}
