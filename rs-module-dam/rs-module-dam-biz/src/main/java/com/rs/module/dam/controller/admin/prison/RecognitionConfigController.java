package com.rs.module.dam.controller.admin.prison;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.controller.admin.prison.vo.RecognitionConfigListReqVO;
import com.rs.module.dam.controller.admin.prison.vo.RecognitionConfigPageReqVO;
import com.rs.module.dam.controller.admin.prison.vo.RecognitionConfigRespVO;
import com.rs.module.dam.controller.admin.prison.vo.RecognitionConfigSaveReqVO;
import com.rs.module.dam.entity.prison.RecognitionConfigDO;
import com.rs.module.dam.service.prison.RecognitionConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "一所一档-监所档案荣誉表彰配置")
@RestController
@RequestMapping("/dam/prison/recognitionConfig")
@Validated
public class RecognitionConfigController {

    @Resource
    private RecognitionConfigService recognitionConfigService;

    @PostMapping("/create")
    @ApiOperation(value = "创建一所一档-监所档案荣誉表彰配置")
    @LogRecordAnnotation(bizModule = "dam:recognitionConfig:create", operateType = LogOperateType.CREATE, title = "创建一所一档-监所档案荣誉表彰配置",
    success = "创建一所一档-监所档案荣誉表彰配置成功", fail = "创建一所一档-监所档案荣誉表彰配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createRecognitionConfig(@Valid @RequestBody RecognitionConfigSaveReqVO createReqVO) {
        return success(recognitionConfigService.createRecognitionConfig(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新一所一档-监所档案荣誉表彰配置")
    @LogRecordAnnotation(bizModule = "dam:recognitionConfig:update", operateType = LogOperateType.UPDATE, title = "更新一所一档-监所档案荣誉表彰配置",
    success = "更新一所一档-监所档案荣誉表彰配置成功", fail = "更新一所一档-监所档案荣誉表彰配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<String> updateRecognitionConfig(@Valid @RequestBody RecognitionConfigSaveReqVO updateReqVO) {
        return success(recognitionConfigService.updateRecognitionConfig(updateReqVO));
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除一所一档-监所档案荣誉表彰配置")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:recognitionConfig:delete", operateType = LogOperateType.DELETE, title = "删除一所一档-监所档案荣誉表彰配置",
    success = "删除一所一档-监所档案荣誉表彰配置成功", fail = "删除一所一档-监所档案荣誉表彰配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteRecognitionConfig(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           recognitionConfigService.deleteRecognitionConfig(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得一所一档-监所档案荣誉表彰配置")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<RecognitionConfigRespVO> getRecognitionConfig(@RequestParam("id") String id) {
        RecognitionConfigDO recognitionConfig = recognitionConfigService.getRecognitionConfig(id);
        return success(BeanUtils.toBean(recognitionConfig, RecognitionConfigRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得一所一档-监所档案荣誉表彰配置分页")
    public CommonResult<PageResult<RecognitionConfigRespVO>> getRecognitionConfigPage(@Valid @RequestBody RecognitionConfigPageReqVO pageReqVO) {
        PageResult<RecognitionConfigDO> pageResult = recognitionConfigService.getRecognitionConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RecognitionConfigRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得一所一档-监所档案荣誉表彰配置列表")
    public CommonResult<List<RecognitionConfigRespVO>> getRecognitionConfigList(@Valid @RequestBody RecognitionConfigListReqVO listReqVO) {
        List<RecognitionConfigDO> list = recognitionConfigService.getRecognitionConfigList(listReqVO);
        return success(BeanUtils.toBean(list, RecognitionConfigRespVO.class));
    }
}
