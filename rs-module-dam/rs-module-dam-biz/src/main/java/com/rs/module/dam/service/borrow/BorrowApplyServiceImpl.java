package com.rs.module.dam.service.borrow;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.config.ApproveConfig;
import com.rs.module.dam.cons.ApproveFlowNode;
import com.rs.module.dam.cons.CommonConstants;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApplyListReqVO;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApplyPageReqVO;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApplySaveReqVO;
import com.rs.module.dam.dao.borrow.BorrowApplyDao;
import com.rs.module.dam.dao.borrow.BorrowPrisonerDao;
import com.rs.module.dam.dao.prisoner.PrisonerInfoDao;
import com.rs.module.dam.entity.borrow.BorrowApplyDO;
import com.rs.module.dam.entity.borrow.BorrowApproveDO;
import com.rs.module.dam.entity.borrow.BorrowPrisonerDO;
import com.rs.module.dam.entity.borrow.PrisonerApproveDO;
import com.rs.module.dam.entity.prisoner.PrisonerInfoDO;
import com.rs.module.dam.service.prisoner.PrisonerInfoService;
import com.rs.module.dam.util.ApproveUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 借阅申请 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BorrowApplyServiceImpl extends BaseServiceImpl<BorrowApplyDao, BorrowApplyDO> implements BorrowApplyService {

    @Resource
    private BorrowApplyDao borrowApplyDao;
    @Resource
    private BorrowPrisonerDao borrowPrisonerDao;
    @Resource
    private PrisonerInfoService prisonerInfoService;
    @Resource
    private PrisonerApproveService prisonerApproveService;
    @Resource
    private BorrowApproveService borrowApproveService;
    @Resource
    private ApproveConfig damConfig;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BorrowApplyDO createBorrowApply(BorrowApplySaveReqVO createReqVO) {
        // 插入
        BorrowApplyDO borrowApply = BeanUtils.toBean(createReqVO, BorrowApplyDO.class);
        borrowApply.setBorrowNum(createReqVO.getBorrowPrisoners().size());
        borrowApplyDao.insert(borrowApply);
        // 插入子表
        createBorrowPrisonerList(borrowApply.getId(), createReqVO.getBorrowPrisoners());
        //
        createApprove(borrowApply.getId(), borrowApply.getType(), createReqVO.getBorrowPrisoners());
        // 返回
        return borrowApply;
    }

    @Override
    public Map<String, Object> getReapplyDetail(String prisonerId) {
        return borrowPrisonerDao.getReapplyDetail(prisonerId);
    }

    /**
     * 插入审批节点信息
     * @param applyId 借阅申请ID
     */
    private void createApprove(String applyId, String type, List<BorrowPrisonerDO> prisonerList) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        List<ApproveFlowNode> approveFlow = damConfig.getInteriorApproveFlow();
        if (CommonConstants.APPROVE_TYPE_EXTERNAL.equals(type)) {
            approveFlow = damConfig.getExternalApproveFlow();
        }
        // 创建发起人节点信息
        BorrowApproveDO initiator = new BorrowApproveDO();
        initiator.setAddTime(new Date());
        initiator.setAddUser(sessionUser.getIdCard());
        initiator.setAddUserName(sessionUser.getName());
        initiator.setRegCode(sessionUser.getRegCode());
        initiator.setRegName(sessionUser.getRegName());
        initiator.setOrgCode(sessionUser.getOrgCode());
        initiator.setOrgName(sessionUser.getOrgName());
        initiator.setBorrowId(applyId);
        initiator.setApproveStatus(CommonConstants.APPROVE_STATUS_INITIATOR);
        initiator.setOrderId(0);
        borrowApproveService.save(initiator);
        ApproveFlowNode firstNode = ApproveUtil.getFirstNode(type);
        // 插入全部审批节点信息
        for (ApproveFlowNode flowNode : approveFlow) {
            BorrowApproveDO approveDO = new BorrowApproveDO();
            approveDO.setAddUser("");
            approveDO.setAddUserName("");
            approveDO.setRegCode(sessionUser.getRegCode());
            approveDO.setRegName(sessionUser.getRegName());
            approveDO.setOrgCode(sessionUser.getOrgCode());
            approveDO.setOrgName(sessionUser.getOrgName());
            approveDO.setBorrowId(applyId);
            approveDO.setApproveStatus(CommonConstants.APPROVE_STATUS_WAIT);
            approveDO.setOrderId(flowNode.getIndex());
            approveDO.setApproveRoleCode(flowNode.getRoleCode());
            approveDO.setApproveRoleName(flowNode.getRoleName());
            borrowApproveService.save(approveDO);
            // 插入第一级审批节点审批记录
            if (firstNode.getIndex() == flowNode.getIndex()) {
                createPrisonerApprove(approveDO.getId(), prisonerList, flowNode);
            }
        }
    }

    /**
     * 插入借阅审批记录
     * @param approveId 审批记录ID
     * @param prisonerList 借阅人员档案关联信息
     * @param firstNode 当前节点
     */
    private void createPrisonerApprove(String approveId, List<BorrowPrisonerDO> prisonerList, ApproveFlowNode firstNode) {
        List<PrisonerApproveDO> list = new ArrayList<>();
        for (BorrowPrisonerDO borrowPrisonerDO : prisonerList) {
            PrisonerApproveDO prisonerApproveDO = new PrisonerApproveDO();
            prisonerApproveDO.setBorrowId(borrowPrisonerDO.getBorrowId());
            prisonerApproveDO.setBorrowPrisonerId(borrowPrisonerDO.getId());
            prisonerApproveDO.setApproveId(approveId);
            prisonerApproveDO.setApproveRoleCode(firstNode.getRoleCode());
            prisonerApproveDO.setApproveRoleName(firstNode.getRoleName());
            prisonerApproveDO.setApproveStatus(CommonConstants.PRISONER_APPROVE_STATUS_WAIT);
            list.add(prisonerApproveDO);
        }
        prisonerApproveService.saveBatch(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBorrowApply(BorrowApplySaveReqVO updateReqVO) {
        // 校验存在
        validateBorrowApplyExists(updateReqVO.getId());
        // 更新
        BorrowApplyDO updateObj = BeanUtils.toBean(updateReqVO, BorrowApplyDO.class);
        borrowApplyDao.updateById(updateObj);

        // 更新子表
        updateBorrowPrisonerList(updateReqVO.getId(), updateReqVO.getBorrowPrisoners());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBorrowApply(String id) {
        // 校验存在
        validateBorrowApplyExists(id);
        // 删除
        borrowApplyDao.deleteById(id);

        // 删除子表
        deleteBorrowPrisonerByBorrowId(id);
    }

    private void validateBorrowApplyExists(String id) {
        if (borrowApplyDao.selectById(id) == null) {
            throw new ServerException("借阅申请数据不存在");
        }
    }

    @Override
    public BorrowApplyDO getBorrowApply(String id) {
        return borrowApplyDao.selectById(id);
    }

    @Override
    public PageResult<BorrowApplyDO> getBorrowApplyPage(BorrowApplyPageReqVO pageReqVO) {
        return borrowApplyDao.selectPage(pageReqVO);
    }

    @Override
    public List<BorrowApplyDO> getBorrowApplyList(BorrowApplyListReqVO listReqVO) {
        return borrowApplyDao.selectList(listReqVO);
    }


    // ==================== 子表（借阅人员档案关联） ====================

    @Override
    public List<BorrowPrisonerDO> getBorrowPrisonerListByBorrowId(String borrowId) {
        return borrowPrisonerDao.selectListByBorrowId(borrowId);
    }

    @Override
    public void updateBorrowPrisonerById(BorrowPrisonerDO borrowPrisoner) {
        borrowPrisonerDao.updateById(borrowPrisoner);
    }

    @Override
    public void approve(BorrowApplyDO borrowApply, List<BorrowPrisonerDO> list) {
        Integer approve = list.size();
        Date date = new Date();
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        DateTime borrowEndDate = DateUtil.offset(date, DateField.DAY_OF_MONTH, borrowApply.getBorrowDays());
        // 获取下一级审确认角色信息
        ApproveFlowNode approveNextNode = ApproveUtil.getApproveNextNode(borrowApply.getNextApproveRole(), borrowApply.getType());

        if (approveNextNode != null) {
            BorrowApproveDO approveDO = borrowApproveService.getOne(new LambdaQueryWrapper<BorrowApproveDO>()
                    .eq(BorrowApproveDO::getBorrowId, borrowApply.getId())
                    .eq(BorrowApproveDO::getApproveRoleCode, approveNextNode.getRoleCode()), false);

            // 修改借阅人员档案关联审批状态
            for (BorrowPrisonerDO borrowPrisonerDO : list) {
                // 更新当前审批节点轨迹
                prisonerApproveService.update(new LambdaUpdateWrapper<PrisonerApproveDO>()
                        .eq(PrisonerApproveDO::getBorrowId, borrowApply.getId())
                        .eq(PrisonerApproveDO::getBorrowPrisonerId, borrowPrisonerDO.getId())
                        .eq(PrisonerApproveDO::getApproveRoleCode, borrowApply.getNextApproveRole())
                        .set(PrisonerApproveDO::getApproveStatus, borrowPrisonerDO.getApproveStatus())
                        .set(PrisonerApproveDO::getApproveTime, date)
                        .set(PrisonerApproveDO::getApproveContent, borrowPrisonerDO.getApproveContent()));

                // 处理不通过的借阅
                String approveStatus = CommonConstants.PRISONER_APPROVE_STATUS_WAIT;
                if (CommonConstants.PRISONER_APPROVE_STATUS_FAILED.equals(borrowPrisonerDO.getApproveStatus())) {
                    approve--;
                    approveStatus = CommonConstants.PRISONER_APPROVE_STATUS_FAILED;
                    borrowPrisonerDO.setBorrowStatus(CommonConstants.BORROW_STATUS_FAILED);
                    borrowPrisonerDao.updateById(borrowPrisonerDO);
                }

                // 插入下一级审批节点轨迹
                PrisonerApproveDO prisonerApproveDO = new PrisonerApproveDO();
                prisonerApproveDO.setBorrowId(borrowApply.getId());
                prisonerApproveDO.setBorrowPrisonerId(borrowPrisonerDO.getId());
                prisonerApproveDO.setApproveId(approveDO.getId());
                prisonerApproveDO.setApproveRoleCode(approveNextNode.getRoleCode());
                prisonerApproveDO.setApproveRoleName(approveNextNode.getRoleName());
                prisonerApproveDO.setApproveStatus(approveStatus);
                prisonerApproveService.save(prisonerApproveDO);

            }
        } else {
            // 修改借阅人员档案关联审批状态
            for (BorrowPrisonerDO borrowPrisonerDO : list) {
                // 更新当前审批节点轨迹
                prisonerApproveService.update(new LambdaUpdateWrapper<PrisonerApproveDO>()
                        .eq(PrisonerApproveDO::getBorrowId, borrowApply.getId())
                        .eq(PrisonerApproveDO::getBorrowPrisonerId, borrowPrisonerDO.getId())
                        .eq(PrisonerApproveDO::getApproveRoleCode, borrowApply.getNextApproveRole())
                        .set(PrisonerApproveDO::getApproveStatus, borrowPrisonerDO.getApproveStatus())
                        .set(PrisonerApproveDO::getApproveTime, date)
                        .set(PrisonerApproveDO::getApproveContent, borrowPrisonerDO.getApproveContent()));

                if (CommonConstants.PRISONER_APPROVE_STATUS_FAILED.equals(borrowPrisonerDO.getApproveStatus())) {
                    approve--;
                    borrowPrisonerDO.setBorrowStatus(CommonConstants.BORROW_STATUS_FAILED);
                } else if (CommonConstants.PRISONER_APPROVE_STATUS_APPROVED.equals(borrowPrisonerDO.getApproveStatus())) {
                    borrowPrisonerDO.setBorrowStatus(CommonConstants.BORROW_STATUS_BORROWED);
                    borrowPrisonerDO.setBorrowEndDate(borrowEndDate);
                    // 更新借阅次数
                    BorrowPrisonerDO borrowPrisoner = borrowPrisonerDao.selectById(borrowPrisonerDO.getId());
                    PrisonerInfoDO prisonerInfo = prisonerInfoService.getByDabh(borrowPrisoner.getDabh());
                    if (ObjectUtil.isNotEmpty(prisonerInfo)) {
                        Short current = prisonerInfo.getJycs();
                        prisonerInfo.setJycs((short) (current != null ? current + 1 : 1));
                        prisonerInfoService.updateById(prisonerInfo);
                    }
                }

                borrowPrisonerDao.updateById(borrowPrisonerDO);
            }
        }

        LambdaUpdateWrapper<BorrowApplyDO> updateWrapper = new LambdaUpdateWrapper<BorrowApplyDO>()
                .eq(BorrowApplyDO::getId, borrowApply.getId());
        // 获取借阅审批状态
        String status = CommonConstants.APPROVE_STATUS_APPROVED;
        if (approve == 0) {
            status = CommonConstants.APPROVE_STATUS_FAILED;
            updateWrapper.set(BorrowApplyDO::getApproveStatus, status);
        } else if (approve < list.size()) {
            status = CommonConstants.APPROVE_STATUS_PARTIALLY_PASSED;
        }

        // 修改借阅审批信息
        borrowApproveService.update(new LambdaUpdateWrapper<BorrowApproveDO>()
                .eq(BorrowApproveDO::getBorrowId, borrowApply.getId())
                .eq(BorrowApproveDO::getApproveRoleCode, borrowApply.getNextApproveRole())
                .set(BorrowApproveDO::getApproveStatus, status)
                .set(BorrowApproveDO::getAddTime, date)
                .set(BorrowApproveDO::getAddUser, sessionUser.getIdCard())
                .set(BorrowApproveDO::getAddUserName, sessionUser.getName())
                .set(BorrowApproveDO::getRegCode, sessionUser.getRegCode())
                .set(BorrowApproveDO::getRegName, sessionUser.getRegName())
                .set(BorrowApproveDO::getOrgCode, sessionUser.getOrgCode())
                .set(BorrowApproveDO::getOrgName, sessionUser.getOrgName()));

        // 修改借阅申请信息
        if (approveNextNode == null) {
            updateWrapper.set(BorrowApplyDO::getApproveStatus, status);
            updateWrapper.set(BorrowApplyDO::getBorrowEndDate, borrowEndDate);
        } else {
            updateWrapper.set(BorrowApplyDO::getNextApproveRole, approveNextNode.getRoleCode());
        }
        update(updateWrapper);
    }



    private void createBorrowPrisonerList(String borrowId, List<BorrowPrisonerDO> list) {
        list.forEach(o -> o.setBorrowId(borrowId));
        list.forEach(o -> borrowPrisonerDao.insert(o));
    }

    private void updateBorrowPrisonerList(String borrowId, List<BorrowPrisonerDO> list) {
        deleteBorrowPrisonerByBorrowId(borrowId);
		list.forEach(o -> o.setId(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createBorrowPrisonerList(borrowId, list);
    }

    private void deleteBorrowPrisonerByBorrowId(String borrowId) {
        borrowPrisonerDao.deleteByBorrowId(borrowId);
    }

}
