package com.rs.module.dam.controller.admin.material;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.entity.material.MaterialImageDO;
import com.rs.module.dam.service.material.MaterialImageService;
import com.rs.module.dam.vo.material.MaterialImageListReqVO;
import com.rs.module.dam.vo.material.MaterialImagePageReqVO;
import com.rs.module.dam.vo.material.MaterialImageRespVO;
import com.rs.module.dam.vo.material.MaterialImageSaveReqVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

@Api(tags = "材料处理 - 卷宗材料图片")
@ApiIgnore
@RestController
@RequestMapping("/dam/material/materialImage")
@Validated
public class MaterialImageController {

    @Resource
    private MaterialImageService materialImageService;

    @PostMapping("/create")
    @ApiOperation(value = "创建卷宗材料图片")
    @LogRecordAnnotation(bizModule = "dam:materialImage:create", operateType = LogOperateType.CREATE, title = "创建卷宗材料图片",
    success = "创建卷宗材料图片成功", fail = "创建卷宗材料图片失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createMaterialImage(@Valid @RequestBody MaterialImageSaveReqVO createReqVO) {
        return success(materialImageService.createMaterialImage(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新卷宗材料图片")
    @LogRecordAnnotation(bizModule = "dam:materialImage:update", operateType = LogOperateType.UPDATE, title = "更新卷宗材料图片",
    success = "更新卷宗材料图片成功", fail = "更新卷宗材料图片失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateMaterialImage(@Valid @RequestBody MaterialImageSaveReqVO updateReqVO) {
        materialImageService.updateMaterialImage(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除卷宗材料图片")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:materialImage:delete", operateType = LogOperateType.DELETE, title = "删除卷宗材料图片",
    success = "删除卷宗材料图片成功", fail = "删除卷宗材料图片失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteMaterialImage(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           materialImageService.deleteMaterialImage(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得卷宗材料图片")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:materialImage:get", operateType = LogOperateType.QUERY, title = "获取卷宗材料图片", bizNo = "{{#id}}", success = "获取卷宗材料图片成功", fail = "获取卷宗材料图片失败", extraInfo = "{{#id}}")
    public CommonResult<MaterialImageRespVO> getMaterialImage(@RequestParam("id") String id) {
        MaterialImageDO materialImage = materialImageService.getMaterialImage(id);
        return success(BeanUtils.toBean(materialImage, MaterialImageRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得卷宗材料图片分页")
    @LogRecordAnnotation(bizModule = "dam:materialImage:page", operateType = LogOperateType.QUERY, title = "获得卷宗材料图片分页",
    success = "获得卷宗材料图片分页成功", fail = "获得卷宗材料图片分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<MaterialImageRespVO>> getMaterialImagePage(@Valid @RequestBody MaterialImagePageReqVO pageReqVO) {
        PageResult<MaterialImageDO> pageResult = materialImageService.getMaterialImagePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MaterialImageRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得卷宗材料图片列表")
    @LogRecordAnnotation(bizModule = "dam:materialImage:list", operateType = LogOperateType.QUERY, title = "获得卷宗材料图片列表",
    success = "获得卷宗材料图片列表成功", fail = "获得卷宗材料图片列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<MaterialImageRespVO>> getMaterialImageList(@Valid @RequestBody MaterialImageListReqVO listReqVO) {
    List<MaterialImageDO> list = materialImageService.getMaterialImageList(listReqVO);
        return success(BeanUtils.toBean(list, MaterialImageRespVO.class));
    }

}
