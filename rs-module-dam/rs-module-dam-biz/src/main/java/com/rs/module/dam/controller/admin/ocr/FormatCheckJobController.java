package com.rs.module.dam.controller.admin.ocr;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.entity.ocr.FormatCheckJobDO;
import com.rs.module.dam.service.ocr.FormatCheckJobService;
import com.rs.module.dam.vo.ocr.FormatCheckJobListReqVO;
import com.rs.module.dam.vo.ocr.FormatCheckJobPageReqVO;
import com.rs.module.dam.vo.ocr.FormatCheckJobRespVO;
import com.rs.module.dam.vo.ocr.FormatCheckJobSaveReqVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

@Api(tags = "ocr处理 - ocr格式化审查任务")
@ApiIgnore
@RestController
@RequestMapping("/dam/ocr/formatCheckJob")
@Validated
public class FormatCheckJobController {

    @Resource
    private FormatCheckJobService formatCheckJobService;

    @PostMapping("/create")
    @ApiOperation(value = "创建ocr格式化审查任务")
    @LogRecordAnnotation(bizModule = "dam:formatCheckJob:create", operateType = LogOperateType.CREATE, title = "创建ocr格式化审查任务",
    success = "创建ocr格式化审查任务成功", fail = "创建ocr格式化审查任务失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createFormatCheckJob(@Valid @RequestBody FormatCheckJobSaveReqVO createReqVO) {
        return success(formatCheckJobService.createFormatCheckJob(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新ocr格式化审查任务")
    @LogRecordAnnotation(bizModule = "dam:formatCheckJob:update", operateType = LogOperateType.UPDATE, title = "更新ocr格式化审查任务",
    success = "更新ocr格式化审查任务成功", fail = "更新ocr格式化审查任务失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateFormatCheckJob(@Valid @RequestBody FormatCheckJobSaveReqVO updateReqVO) {
        formatCheckJobService.updateFormatCheckJob(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除ocr格式化审查任务")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:formatCheckJob:delete", operateType = LogOperateType.DELETE, title = "删除ocr格式化审查任务",
    success = "删除ocr格式化审查任务成功", fail = "删除ocr格式化审查任务失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteFormatCheckJob(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           formatCheckJobService.deleteFormatCheckJob(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得ocr格式化审查任务")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:formatCheckJob:get", operateType = LogOperateType.QUERY, title = "获取ocr格式化审查任务", bizNo = "{{#id}}", success = "获取ocr格式化审查任务成功", fail = "获取ocr格式化审查任务失败", extraInfo = "{{#id}}")
    public CommonResult<FormatCheckJobRespVO> getFormatCheckJob(@RequestParam("id") String id) {
        FormatCheckJobDO formatCheckJob = formatCheckJobService.getFormatCheckJob(id);
        return success(BeanUtils.toBean(formatCheckJob, FormatCheckJobRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得ocr格式化审查任务分页")
    @LogRecordAnnotation(bizModule = "dam:formatCheckJob:page", operateType = LogOperateType.QUERY, title = "获得ocr格式化审查任务分页",
    success = "获得ocr格式化审查任务分页成功", fail = "获得ocr格式化审查任务分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<FormatCheckJobRespVO>> getFormatCheckJobPage(@Valid @RequestBody FormatCheckJobPageReqVO pageReqVO) {
        PageResult<FormatCheckJobDO> pageResult = formatCheckJobService.getFormatCheckJobPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, FormatCheckJobRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得ocr格式化审查任务列表")
    @LogRecordAnnotation(bizModule = "dam:formatCheckJob:list", operateType = LogOperateType.QUERY, title = "获得ocr格式化审查任务列表",
    success = "获得ocr格式化审查任务列表成功", fail = "获得ocr格式化审查任务列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<FormatCheckJobRespVO>> getFormatCheckJobList(@Valid @RequestBody FormatCheckJobListReqVO listReqVO) {
    List<FormatCheckJobDO> list = formatCheckJobService.getFormatCheckJobList(listReqVO);
        return success(BeanUtils.toBean(list, FormatCheckJobRespVO.class));
    }

}
