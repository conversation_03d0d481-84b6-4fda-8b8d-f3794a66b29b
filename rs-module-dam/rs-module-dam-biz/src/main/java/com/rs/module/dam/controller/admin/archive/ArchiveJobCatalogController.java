package com.rs.module.dam.controller.admin.archive;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.entity.archive.ArchiveJobCatalogDO;
import com.rs.module.dam.service.archive.ArchiveJobCatalogService;
import com.rs.module.dam.vo.archive.ArchiveJobCatalogListReqVO;
import com.rs.module.dam.vo.archive.ArchiveJobCatalogPageReqVO;
import com.rs.module.dam.vo.archive.ArchiveJobCatalogRespVO;
import com.rs.module.dam.vo.archive.ArchiveJobCatalogSaveReqVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

@Api(tags = "卷宗归档 - 卷宗归档任务目录")
@ApiIgnore
@RestController
@RequestMapping("/dam/archive/archiveJobCatalog")
@Validated
public class ArchiveJobCatalogController {

    @Resource
    private ArchiveJobCatalogService archiveJobCatalogService;

    @PostMapping("/create")
    @ApiOperation(value = "创建卷宗归档任务目录")
    @LogRecordAnnotation(bizModule = "dam:archiveJobCatalog:create", operateType = LogOperateType.CREATE, title = "创建卷宗归档任务目录",
    success = "创建卷宗归档任务目录成功", fail = "创建卷宗归档任务目录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createArchiveJobCatalog(@Valid @RequestBody ArchiveJobCatalogSaveReqVO createReqVO) {
        return success(archiveJobCatalogService.createArchiveJobCatalog(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新卷宗归档任务目录")
    @LogRecordAnnotation(bizModule = "dam:archiveJobCatalog:update", operateType = LogOperateType.UPDATE, title = "更新卷宗归档任务目录",
    success = "更新卷宗归档任务目录成功", fail = "更新卷宗归档任务目录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateArchiveJobCatalog(@Valid @RequestBody ArchiveJobCatalogSaveReqVO updateReqVO) {
        archiveJobCatalogService.updateArchiveJobCatalog(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除卷宗归档任务目录")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:archiveJobCatalog:delete", operateType = LogOperateType.DELETE, title = "删除卷宗归档任务目录",
    success = "删除卷宗归档任务目录成功", fail = "删除卷宗归档任务目录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteArchiveJobCatalog(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           archiveJobCatalogService.deleteArchiveJobCatalog(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得卷宗归档任务目录")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:archiveJobCatalog:get", operateType = LogOperateType.QUERY, title = "获取卷宗归档任务目录", bizNo = "{{#id}}", success = "获取卷宗归档任务目录成功", fail = "获取卷宗归档任务目录失败", extraInfo = "{{#id}}")
    public CommonResult<ArchiveJobCatalogRespVO> getArchiveJobCatalog(@RequestParam("id") String id) {
        ArchiveJobCatalogDO archiveJobCatalog = archiveJobCatalogService.getArchiveJobCatalog(id);
        return success(BeanUtils.toBean(archiveJobCatalog, ArchiveJobCatalogRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得卷宗归档任务目录分页")
    @LogRecordAnnotation(bizModule = "dam:archiveJobCatalog:page", operateType = LogOperateType.QUERY, title = "获得卷宗归档任务目录分页",
    success = "获得卷宗归档任务目录分页成功", fail = "获得卷宗归档任务目录分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<ArchiveJobCatalogRespVO>> getArchiveJobCatalogPage(@Valid @RequestBody ArchiveJobCatalogPageReqVO pageReqVO) {
        PageResult<ArchiveJobCatalogDO> pageResult = archiveJobCatalogService.getArchiveJobCatalogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ArchiveJobCatalogRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得卷宗归档任务目录列表")
    @LogRecordAnnotation(bizModule = "dam:archiveJobCatalog:list", operateType = LogOperateType.QUERY, title = "获得卷宗归档任务目录列表",
    success = "获得卷宗归档任务目录列表成功", fail = "获得卷宗归档任务目录列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<ArchiveJobCatalogRespVO>> getArchiveJobCatalogList(@Valid @RequestBody ArchiveJobCatalogListReqVO listReqVO) {
    List<ArchiveJobCatalogDO> list = archiveJobCatalogService.getArchiveJobCatalogList(listReqVO);
        return success(BeanUtils.toBean(list, ArchiveJobCatalogRespVO.class));
    }

}
