package com.rs.module.dam.entity.event;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import java.util.Date;

/**
 * 一事一档-事件档案 DO
 *
 * <AUTHOR>
 */
@TableName("dam_event_archive")
@KeySequence("dam_event_archive_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ArchiveDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 事件编号(规则：S+9位单位编号+8位日期+6位序号)
     */
    private String eventCode;
    /**
     * 事件类型（字典：ZD_YSYD_SJLX）
     */
    private String eventType;
    /**
     * 事件发生的具体时间
     */
    private Date occurrenceTime;
    /**
     * 事件发生的地点
     */
    private String eventLocation;
    /**
     * 对事件的详细描述
     */
    private String eventDescription;
    /**
     * 状态（字典：ZD_YSYD_SJZT）
     */
    private String eventStatus;
    /**
     * 审批人身份证号
     */
    private String approverSfzh;
    /**
     * 审批人姓名
     */
    private String approverXm;
    /**
     * 审批时间
     */
    private Date approverTime;
    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 审核意见
     */
    private String approvalComments;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 流程任务ID
     */
    private String taskId;

}
