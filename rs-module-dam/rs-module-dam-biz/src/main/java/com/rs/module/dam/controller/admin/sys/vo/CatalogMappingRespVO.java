package com.rs.module.dam.controller.admin.sys.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 目录文书映射 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CatalogMappingRespVO extends BaseVO implements TransPojo{
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("目录模板Id")
    private String templateId;
    @ApiModelProperty("模板目录Id")
    private String catalogId;
    @ApiModelProperty("应用Id")
    private String appId;
    @ApiModelProperty("表单Id")
    private String formId;
    @ApiModelProperty("表单名称")
    private String formName;
}
