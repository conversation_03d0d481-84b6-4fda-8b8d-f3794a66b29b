package com.rs.module.dam.entity.borrow;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO_;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

/**
 * 借阅人员档案关联 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 借阅人员档案关联新增/修改 Request VO")
@TableName("dam_borrow_prisoner")
@KeySequence("dam_borrow_prisoner_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BorrowPrisonerDO extends BaseDO_ {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;
    /**
     * 借阅Id
     */
    @ApiModelProperty("借阅Id")
    private String borrowId;
    /**
     * 案件编号
     */
    @ApiModelProperty("案件编号")
    private String ajbh;
    /**
     * 案件名称
     */
    @ApiModelProperty("案件名称")
    private String ajmc;
    /**
     * 档案编号
     */
    @ApiModelProperty("档案编号")
    private String dabh;
    /**
     * 监室号
     */
    @ApiModelProperty("监室号")
    private String jsh;
    /**
     * 监室名称
     */
    @ApiModelProperty("监室名称")
    private String roomName;
    /**
     * 监管人员编码
     */
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    /**
     * 借阅截止时间
     */
    @ApiModelProperty("借阅截止时间")
    private Date borrowEndDate;
    /**
     * 审批状态(0:待审批,1:审批中,2:审批通过,3:审批不通过)
     */
    @ApiModelProperty("审批状态(0:待审批,1:审批中,2:审批通过,3:审批不通过)")
    private String approveStatus;
    /**
     * 审批时间
     */
    @ApiModelProperty("审批时间")
    private Date approveTime;
    /**
     * 审批意见
     */
    @ApiModelProperty("审批意见")
    private String approveContent;
    /**
     * 借阅状态(0:待审批,1:借阅中,2:已到期,3:权限回收,4:审核不通过)
     */
    @ApiModelProperty("借阅状态(0:待审批,1:借阅中,2:已到期,3:权限回收,4:审核不通过)")
    private String borrowStatus;
    /**
     * 权限回收理由
     */
    @ApiModelProperty("权限回收理由")
    private String recycleReason;
    /**
     * 回收时间
     */
    @ApiModelProperty("回收时间")
    private Date recycleTime;
    /**
     * 回收用户
     */
    @ApiModelProperty("回收用户")
    private String recycleUser;
    /**
     * 回收用户姓名
     */
    @ApiModelProperty("回收用户姓名")
    private String recycleUserName;
    /**
     * 回收机构编号
     */
    @ApiModelProperty("回收机构编号")
    private String recycleOrgCode;
    /**
     * 回收机构名称
     */
    @ApiModelProperty("回收机构名称")
    private String recycleOrgName;

}
