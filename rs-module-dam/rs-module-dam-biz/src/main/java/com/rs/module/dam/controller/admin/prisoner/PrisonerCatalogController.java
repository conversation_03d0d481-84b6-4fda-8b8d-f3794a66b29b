package com.rs.module.dam.controller.admin.prisoner;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.constant.BmTypeEnum;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.entity.material.MaterialLabelDO;
import com.rs.module.dam.entity.prisoner.PrisonerCatalogDO;
import com.rs.module.dam.service.material.MaterialInfoService;
import com.rs.module.dam.service.material.MaterialLabelService;
import com.rs.module.dam.service.prisoner.PrisonerCatalogService;
import com.rs.module.dam.util.CatalogUtil;
import com.rs.module.dam.vo.prisoner.PrisonerCatalogListReqVO;
import com.rs.module.dam.vo.prisoner.PrisonerCatalogPageReqVO;
import com.rs.module.dam.vo.prisoner.PrisonerCatalogRespVO;
import com.rs.module.dam.vo.prisoner.PrisonerCatalogSaveReqVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

@Api(tags = "监管人员 - 监管人员卷宗目录")
@RestController
@RequestMapping("/dam/prisoner/prisonerCatalog")
@Validated
public class PrisonerCatalogController {

    @Resource
    private PrisonerCatalogService prisonerCatalogService;
    
    @Resource
    private MaterialInfoService materialInfoService;
    
    @Resource
    private MaterialLabelService materialLabelService;

    @PostMapping("/create")
    @ApiOperation(value = "创建监管人员卷宗目录", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:prisonerCatalog:create", operateType = LogOperateType.CREATE, title = "创建监管人员卷宗目录",
    success = "创建监管人员卷宗目录成功", fail = "创建监管人员卷宗目录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPrisonerCatalog(@Valid @RequestBody PrisonerCatalogSaveReqVO createReqVO) {
        return success(prisonerCatalogService.createPrisonerCatalog(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新监管人员卷宗目录", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:prisonerCatalog:update", operateType = LogOperateType.UPDATE, title = "更新监管人员卷宗目录",
    success = "更新监管人员卷宗目录成功", fail = "更新监管人员卷宗目录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePrisonerCatalog(@Valid @RequestBody PrisonerCatalogSaveReqVO updateReqVO) {
        prisonerCatalogService.updatePrisonerCatalog(updateReqVO);
        return success(true);
    }
    
    @PostMapping("/save")
    @ApiOperation(value = "保存监管人员卷宗目录")
    @LogRecordAnnotation(bizModule = "dam:prisonerInfo:save", operateType = LogOperateType.CREATE, title = "保存监管人员卷宗目录",
    success = "保存监管人员卷宗目录成功", fail = "保存监管人员卷宗目录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> savePrisonerInfo(@Valid @RequestBody PrisonerCatalogSaveReqVO createReqVO) {
    	if(StringUtil.isEmpty(createReqVO.getJgrybm())){
    		return CommonResult.error("请输入监管人员编码");
    	}
    	
    	QueryWrapper<PrisonerCatalogDO> wrapper = new QueryWrapper<PrisonerCatalogDO>().eq("jgrybm", createReqVO.getJgrybm());
    	PrisonerCatalogDO prisonerCatalogDO = prisonerCatalogService.getOne(wrapper);
    	
    	//创建监管人员卷宗目录
    	if(prisonerCatalogDO == null) {
    		return success(prisonerCatalogService.createPrisonerCatalog(createReqVO));
    	}
    	
    	//更新监管人员卷宗目录
    	else {
    		createReqVO.setId(prisonerCatalogDO.getId());
    		prisonerCatalogService.updatePrisonerCatalog(createReqVO);
            return success("监管人员卷宗目录更新成功");
    	}
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除监管人员卷宗目录", hidden = true)
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:prisonerCatalog:delete", operateType = LogOperateType.DELETE, title = "删除监管人员卷宗目录",
    success = "删除监管人员卷宗目录成功", fail = "删除监管人员卷宗目录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePrisonerCatalog(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           prisonerCatalogService.deletePrisonerCatalog(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得监管人员卷宗目录", hidden = true)
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:prisonerCatalog:get", operateType = LogOperateType.QUERY, title = "获取监管人员卷宗目录",
    	bizNo = "{{#id}}", success = "获取监管人员卷宗目录成功", fail = "获取监管人员卷宗目录失败", extraInfo = "{{#id}}")
    public CommonResult<PrisonerCatalogRespVO> getPrisonerCatalog(@RequestParam("id") String id) {
        PrisonerCatalogDO prisonerCatalog = prisonerCatalogService.getPrisonerCatalog(id);
        return success(BeanUtils.toBean(prisonerCatalog, PrisonerCatalogRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得监管人员卷宗目录分页", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:prisonerCatalog:page", operateType = LogOperateType.QUERY, title = "获得监管人员卷宗目录分页",
    success = "获得监管人员卷宗目录分页成功", fail = "获得监管人员卷宗目录分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<PrisonerCatalogRespVO>> getPrisonerCatalogPage(@Valid @RequestBody PrisonerCatalogPageReqVO pageReqVO) {
        PageResult<PrisonerCatalogDO> pageResult = prisonerCatalogService.getPrisonerCatalogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PrisonerCatalogRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得监管人员卷宗目录列表", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:prisonerCatalog:list", operateType = LogOperateType.QUERY, title = "获得监管人员卷宗目录列表",
    success = "获得监管人员卷宗目录列表成功", fail = "获得监管人员卷宗目录列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<PrisonerCatalogRespVO>> getPrisonerCatalogList(@Valid @RequestBody PrisonerCatalogListReqVO listReqVO) {
    	List<PrisonerCatalogDO> list = prisonerCatalogService.getPrisonerCatalogList(listReqVO);
        return success(BeanUtils.toBean(list, PrisonerCatalogRespVO.class));
    }

    @GetMapping("/getByJgrybm")
    @ApiOperation(value = "根据监管人员编码获得卷宗目录")
    @ApiImplicitParam(name = "jgrybm", value = "监管人员编码")
    @LogRecordAnnotation(bizModule = "dam:prisonerCatalog:get", operateType = LogOperateType.QUERY, title = "获取监管人员卷宗目录",
    	bizNo = "{{#id}}", success = "获取监管人员卷宗目录成功", fail = "获取监管人员卷宗目录失败", extraInfo = "{{#jgrybm}}")
    public CommonResult<PrisonerCatalogRespVO> getByJgrybm(@RequestParam("jgrybm") String jgrybm) {
    	QueryWrapper<PrisonerCatalogDO> wrapper = new QueryWrapper<PrisonerCatalogDO>().eq("jgrybm", jgrybm);
    	PrisonerCatalogDO catalogDO = prisonerCatalogService.getOne(wrapper);
    	PrisonerCatalogRespVO catalog = BeanUtils.toBean(catalogDO, PrisonerCatalogRespVO.class);
    	
    	//处理快速组卷
    	if(catalog != null && StringUtil.isNotEmpty(catalog.getKszjCatalogData())) {
    		JSONArray array = JSON.parseArray(catalog.getKszjCatalogData());
    		
    		//获取标准目录
    		JSONArray bzml = new JSONArray();
    		array.stream().forEach(x -> {
    			if(!((JSONObject)x).containsKey("isAdd")) {
    				bzml.add(x);
    			}
    		});
    		catalog.setKszjCatalogData(bzml.toJSONString());
    		
    		//获取已有材料的目录
    		List<String> wsList = materialInfoService.selectPartCatalogIdByJgrybmAndType(jgrybm, "3");
    		if(CollectionUtil.isNotNull(wsList)) {
    			JSONArray wsml = new JSONArray();
    			array.stream().forEach(x -> {
    				if(wsList.contains(((JSONObject)x).getString("id"))) {
    					wsml.add(x);
    				}
        		});
    			catalog.setKszjWsCatalogData(wsml.toJSONString());
    		}
    	}
    	
        return success(catalog);
    }
    
    @GetMapping("/getZjCatalog")
    @ApiOperation(value = "根据监管人员编码获得组卷目录")
    @ApiImplicitParam(name = "jgrybm", value = "监管人员编码")
    @LogRecordAnnotation(bizModule = "dam:prisonerCatalog:get", operateType = LogOperateType.QUERY, title = "获取监管人员组卷目录",
    	bizNo = "{{#jgrybm}}", success = "获取监管人员组卷目录成功", fail = "获取监管人员组卷目录失败", extraInfo = "{{#jgrybm}}")
    public CommonResult<?> getZjCatalog(@RequestParam("jgrybm") String jgrybm) {
    	QueryWrapper<PrisonerCatalogDO> wrapper = new QueryWrapper<PrisonerCatalogDO>().eq("jgrybm", jgrybm);
    	PrisonerCatalogDO catalogDO = prisonerCatalogService.getOne(wrapper);
    	PrisonerCatalogRespVO catalog = BeanUtils.toBean(catalogDO, PrisonerCatalogRespVO.class);
    	if(catalog == null) {
    		return CommonResult.error("监管人员卷宗目录为空");
    	}
    	
    	//编目组卷-确认组卷目录
    	JSONArray zjCataArray = JSON.parseArray(catalog.getZjCatalogData());
    	
    	//结果数组
    	JSONArray resultArr = new JSONArray();
    	
    	//起始索引
		int docStartIndex = 1;
    	
    	//编目组卷-确认组卷目录
    	List<String> zjPartCatalogIds = new ArrayList<>();
		if(zjCataArray != null ) {
			zjCataArray.sort(Comparator.comparing(obj -> ((JSONObject) obj).getString("orderId").length()));
			String partCatalogId = "";
			for (int i = 0; i < zjCataArray.size(); i++) {
    			JSONObject zjCatalogObject = zjCataArray.getJSONObject(i);
    			
    			//获取文书
    			String level = zjCatalogObject.getString("level");
    			
    			//根目录
    			if("0".equals(level)) {
    				zjCatalogObject.put("docStartIndex", i+docStartIndex);
					partCatalogId = zjCatalogObject.getString("id");
					zjPartCatalogIds.add(partCatalogId);
    			}
    			
    			//文档目录
    			else if(CatalogUtil.isDocment(zjCatalogObject.getString("id"), zjCataArray)) {
    				String catalogId = zjCatalogObject.getString("id");
    				zjCatalogObject.put("docStartIndex", docStartIndex);
    				
    				//获取当前分类的卷宗材料
    				List<MaterialInfoDO> materialList = materialInfoService.getMaterialInfoList(
    						jgrybm, BmTypeEnum.ZJ.getType(), catalogId);
    				
    				int num = 0;
    				for (MaterialInfoDO materialInfo : materialList) {
    					String materialInfoId = materialInfo.getId();
    					
    					//是否有标签
    					boolean hasLabel = false;

    					//获取用户的材料标签
    					List<MaterialLabelDO> materialLabelList = materialLabelService.getUserMaterialLabelList(materialInfoId, null);
						if(materialLabelList.size() > 0) {
							hasLabel = true;
						}
						else {
							hasLabel = false;
						}

						//材料包含的文件
						JSONArray fileArr = JSON.parseArray(materialInfo.getMaterialFile());
						docStartIndex =+ fileArr.size();
						JSONObject material = new JSONObject();
						material.put("isParent", false);
						material.put("parentId", catalogId);
						material.put("length", fileArr.size());
						material.put("orderId", num++);
						material.put("level", 2);
						material.put("docStartIndex", docStartIndex);
						material.put("isDoc", true);
						material.put("name", materialInfo.getName());
						material.put("id", materialInfo.getId());
						zjCatalogObject.put("hasLabel", hasLabel);
						material.put("hasLabel", hasLabel);
						
						//判断文件是否有标签
						for(int  x= 0 ; x < fileArr.size() ; x ++ ) {
							String fileId = fileArr.getJSONObject(x).getString("id");
							
							//获取用户的文件标签
	    					List<MaterialLabelDO> fileLabelList = materialLabelService.getUserMaterialLabelList(null, fileId);
	    					if(fileLabelList.size() > 0) {
	    						zjCatalogObject.put("hasLabel", true);
	    						material.put("hasLabel", true);
	    					}
						}

						material.put("partCatalogId",partCatalogId);
						resultArr.add(material);
					}
    			}
    			
				zjCatalogObject.put("partCatalogId",partCatalogId);
    			resultArr.add(zjCatalogObject);    			
			}
		}
    	
        return success(resultArr);
    }
}
