package com.rs.module.dam.controller.admin.ocr;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.entity.ocr.FormatCheckResultDO;
import com.rs.module.dam.service.ocr.FormatCheckResultService;
import com.rs.module.dam.vo.ocr.FormatCheckResultListReqVO;
import com.rs.module.dam.vo.ocr.FormatCheckResultPageReqVO;
import com.rs.module.dam.vo.ocr.FormatCheckResultRespVO;
import com.rs.module.dam.vo.ocr.FormatCheckResultSaveReqVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

@Api(tags = "ocr处理 - ocr格式化审查结果")
@ApiIgnore
@RestController
@RequestMapping("/dam/ocr/formatCheckResult")
@Validated
public class FormatCheckResultController {

    @Resource
    private FormatCheckResultService formatCheckResultService;

    @PostMapping("/create")
    @ApiOperation(value = "创建ocr格式化审查结果")
    @LogRecordAnnotation(bizModule = "dam:formatCheckResult:create", operateType = LogOperateType.CREATE, title = "创建ocr格式化审查结果",
    success = "创建ocr格式化审查结果成功", fail = "创建ocr格式化审查结果失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createFormatCheckResult(@Valid @RequestBody FormatCheckResultSaveReqVO createReqVO) {
        return success(formatCheckResultService.createFormatCheckResult(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新ocr格式化审查结果")
    @LogRecordAnnotation(bizModule = "dam:formatCheckResult:update", operateType = LogOperateType.UPDATE, title = "更新ocr格式化审查结果",
    success = "更新ocr格式化审查结果成功", fail = "更新ocr格式化审查结果失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateFormatCheckResult(@Valid @RequestBody FormatCheckResultSaveReqVO updateReqVO) {
        formatCheckResultService.updateFormatCheckResult(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除ocr格式化审查结果")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:formatCheckResult:delete", operateType = LogOperateType.DELETE, title = "删除ocr格式化审查结果",
    success = "删除ocr格式化审查结果成功", fail = "删除ocr格式化审查结果失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteFormatCheckResult(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           formatCheckResultService.deleteFormatCheckResult(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得ocr格式化审查结果")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:formatCheckResult:get", operateType = LogOperateType.QUERY, title = "获取ocr格式化审查结果", bizNo = "{{#id}}", success = "获取ocr格式化审查结果成功", fail = "获取ocr格式化审查结果失败", extraInfo = "{{#id}}")
    public CommonResult<FormatCheckResultRespVO> getFormatCheckResult(@RequestParam("id") String id) {
        FormatCheckResultDO formatCheckResult = formatCheckResultService.getFormatCheckResult(id);
        return success(BeanUtils.toBean(formatCheckResult, FormatCheckResultRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得ocr格式化审查结果分页")
    @LogRecordAnnotation(bizModule = "dam:formatCheckResult:page", operateType = LogOperateType.QUERY, title = "获得ocr格式化审查结果分页",
    success = "获得ocr格式化审查结果分页成功", fail = "获得ocr格式化审查结果分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<FormatCheckResultRespVO>> getFormatCheckResultPage(@Valid @RequestBody FormatCheckResultPageReqVO pageReqVO) {
        PageResult<FormatCheckResultDO> pageResult = formatCheckResultService.getFormatCheckResultPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, FormatCheckResultRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得ocr格式化审查结果列表")
    @LogRecordAnnotation(bizModule = "dam:formatCheckResult:list", operateType = LogOperateType.QUERY, title = "获得ocr格式化审查结果列表",
    success = "获得ocr格式化审查结果列表成功", fail = "获得ocr格式化审查结果列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<FormatCheckResultRespVO>> getFormatCheckResultList(@Valid @RequestBody FormatCheckResultListReqVO listReqVO) {
    List<FormatCheckResultDO> list = formatCheckResultService.getFormatCheckResultList(listReqVO);
        return success(BeanUtils.toBean(list, FormatCheckResultRespVO.class));
    }

}
