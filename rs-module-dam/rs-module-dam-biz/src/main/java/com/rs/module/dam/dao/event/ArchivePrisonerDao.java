package com.rs.module.dam.dao.event;

import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.PageParam;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.dam.entity.event.ArchivePrisonerDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
 * 一事一档-事件档案监管人员关联
 *
 * <AUTHOR>
 */
@Mapper
public interface ArchivePrisonerDao extends IBaseDao<ArchivePrisonerDO> {

    default List<ArchivePrisonerDO> selectListByEventId(String eventId) {
        return selectList(new LambdaQueryWrapperX<ArchivePrisonerDO>().eq(ArchivePrisonerDO::getEventId, eventId));
    }

    default int deleteByEventId(String eventId) {
        return delete(new LambdaQueryWrapperX<ArchivePrisonerDO>().eq(ArchivePrisonerDO::getEventId, eventId));
    }

}
