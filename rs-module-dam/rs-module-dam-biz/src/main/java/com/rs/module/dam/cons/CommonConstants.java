package com.rs.module.dam.cons;

public class CommonConstants {

	/** 字典-子系统，值为@Value */
	public static final String DIC_SUB_SYSTEM = "ZD_RS_SUB_SYSTEM";

	/** 字典-bsp流程审批状态，值为@Value */
	public static final String DIC_ZD_BPM_APPROVE_STATUS = "ZD_BPM_APPROVE_STATUS";

	/** 字典-借阅审批结果，值为@Value */
	public static final String DIC_ZD_SPJG = "ZD_SPJG";

	/** 字典-一事一档事件类型，值为@Value */
	public static final String DIC_ZD_EVENT_SJLX = "ZD_EVENT_SJLX";

	/** 借阅申请单号生成规则code，值为@Value */
	public static final String BORROW_APPLY_NO_RULE = "dam_borrow_no";

	/** 一事一档-事件编号生成规则code，值为@Value */
	public static final String EVENT_CODE_RULE = "dam_event_code";


	/** 字典-审批类型-内部借阅，值为@Value */
	public static final String APPROVE_TYPE_INTERIOR = "0";

	/** 字典-审批类型-外部借阅，值为@Value */
	public static final String APPROVE_TYPE_EXTERNAL = "1";



	/** 字典-审批节点状态-发起申请，值为@Value */
	public static final String APPROVE_STATUS_INITIATOR = "0";

	/** 字典-审批节点状态-待审批，值为@Value */
	public static final String APPROVE_STATUS_WAIT = "1";

	/** 字典-审批节点状态-审批通过，值为@Value */
	public static final String APPROVE_STATUS_APPROVED = "2";

	/** 字典-审批节点状态-部分审批通过，值为@Value */
	public static final String APPROVE_STATUS_PARTIALLY_PASSED = "3";

	/** 字典-审批节点状态-审批不通过，值为@Value */
	public static final String APPROVE_STATUS_FAILED = "4";



	/** 字典-卷宗审批节点状态-待审批，值为@Value */
	public static final String PRISONER_APPROVE_STATUS_WAIT = "0";

	/** 字典-卷宗审批节点状态-审批中，值为@Value */
	public static final String PRISONER_APPROVE_STATUS_IN_APPROVAL = "1";

	/** 字典-卷宗审批节点状态-审批通过，值为@Value */
	public static final String PRISONER_APPROVE_STATUS_APPROVED = "2";

	/** 字典-卷宗审批节点状态-审批不通过，值为@Value */
	public static final String PRISONER_APPROVE_STATUS_FAILED = "3";



	/** 字典-借阅状态-借阅中，值为@Value */
	public static final String BORROW_STATUS_BORROWED = "1";

	/** 字典-借阅状态-已到期，值为@Value */
	public static final String BORROW_STATUS_EXPIRE = "2";

	/** 字典-借阅状态-权限回收，值为@Value */
	public static final String BORROW_STATUS_RECYCLE = "3";

	/** 字典-借阅状态-审批不通过，值为@Value */
	public static final String BORROW_STATUS_FAILED = "4";


	/** 字典-一事一档审批状态-待审批，值为@Value */
	public static final String EVENT_ARCHIVE_STATUS_WAIT = "1";

	/** 字典-一事一档审批状态-审批通过，值为@Value */
	public static final String EVENT_ARCHIVE_STATUS_APPROVED = "2";

	/** 字典-一事一档审批状态-驳回，值为@Value */
	public static final String EVENT_ARCHIVE_STATUS_REJECT = "3";

}

