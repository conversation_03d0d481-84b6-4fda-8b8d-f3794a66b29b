package com.rs.module.dam.controller.admin.event.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 一事一档-事件档案 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ArchiveRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("上报人")
    private String addUser;
    @ApiModelProperty("上报人姓名")
    private String addUserName;
    @ApiModelProperty("上报时间")
    private Date addTime;
    @ApiModelProperty("事件编号(规则：S+9位单位编号+8位日期+6位序号)")
    private String eventCode;
    @ApiModelProperty("事件类型（字典：ZD_EVENT_SJLX）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_EVENT_SJLX")
    private String eventType;
    @ApiModelProperty("事件发生的具体时间")
    private Date occurrenceTime;
    @ApiModelProperty("事件发生的地点")
    private String eventLocation;
    @ApiModelProperty("对事件的详细描述")
    private String eventDescription;
    @ApiModelProperty("状态（字典：ZD_EVENT_SJZT）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_EVENT_SJZT")
    private String eventStatus;
    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;
    @ApiModelProperty("审批人姓名")
    private String approverXm;
    @ApiModelProperty("审批时间")
    private Date approverTime;
    @ApiModelProperty("审批结果")
    private String approvalResult;
    @ApiModelProperty("审核意见")
    private String approvalComments;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("流程任务ID")
    private String taskId;
}
