package com.rs.module.dam.controller.admin.home;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.cons.CommonConstants;
import com.bsp.common.util.StringUtil;
import com.bsp.sdk.cons.MsgType;
import com.bsp.sdk.msg.MessageClientUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.mongodb.BasicDBObject;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.pojo.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "首页")
@RestController
@RequestMapping("/dam/home")
@Validated
public class HomeController {

    @Value("${system-mark}")
    private String systemMark;

    /**
     * 根据应用标识获取待办、通知、预警信息数量
     *
     * @param isRead
     * @param isProc
     * @return com.bsp.common.util.R
     * <AUTHOR>
     * @date 2021/2/25
     */
    @ApiOperation(value = "根据应用标识获取待办、通知、预警信息数量", notes = "获取待办、通知、预警信息数量", produces = "application/json")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "access_token", value = "令牌", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "isRead", value = "是否已读，0未读，1已读", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "isProc", value = "是否已处理，0未处理，1已处理", required = false, paramType = "query", dataType = "String"),
    })
    @RequestMapping(value = "/getMsgCount", method = {RequestMethod.GET, RequestMethod.POST})
    public CommonResult<JSONObject> getMsgCount(@RequestParam(value = "isRead", required = false, defaultValue = "0") String isRead,
                                              @RequestParam(value = "isProc", required = false, defaultValue = "0") String isProc) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        BasicDBObject filter = new BasicDBObject();
        filter.put("rUser", sessionUser.getIdCard());

        if(!"-1".equals(isProc)) {
            filter.put("isProc", isProc);
        }
        if(!"-1".equals(isRead)) {
            filter.put("isRead", isRead);
        }

        // 只展示设置为提醒的数据（1是提醒，0是不提醒）
        filter.put("isPrompting", "1");
        filter.put("isdel", CommonConstants.DATA_IS_DEL_FALSE);
        // 应用标识
        filter.put("systemMark", systemMark);

        try {
            filter.put("busType", new BasicDBObject("$in", MsgBusTypeEnum.getDamAllBusTypeList()));
            long allCount = MessageClientUtil.getCount(filter, MsgType.TODO.getCode());
            filter.put("busType", new BasicDBObject("$in", MsgBusTypeEnum.getDamApproveBusTypeList()));
            long approvalCount = MessageClientUtil.getCount(filter, MsgType.TODO.getCode());
            filter.put("busType", new BasicDBObject("$in", MsgBusTypeEnum.getDamCatalogBusTypeList()));
            long catalogCount = MessageClientUtil.getCount(filter, MsgType.TODO.getCode());

            JSONObject data = new JSONObject();
            data.put("allCount", allCount);
            data.put("approvalCount", approvalCount);
            data.put("catalogCount", catalogCount);
            return success(data);
        } catch (Exception e) {
            e.printStackTrace();
            return error("获取待办信息数量失败");
        }
    }



    @ApiOperation(value = "按照应用标识分页查询消息", notes = "按照应用标识查询消息", produces = "application/json")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "access_token", value = "令牌", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "busType", value = "业务类型", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "isProc", value = "是否已处理，0未处理，1已处理 ,默认未处理", required = false, paramType = "query", dataType = "String"),
    })
    @RequestMapping(value = "/getMsg", method = {RequestMethod.GET, RequestMethod.POST})
    public CommonResult<JSONObject> getMsg(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                                @RequestParam(value = "type", required = false) String type,
                                                @RequestParam(value = "isProc", required = false, defaultValue = "0") String isProc) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        List<JSONObject> result = new ArrayList<JSONObject>();
        BasicDBObject filter = new BasicDBObject().append("isdel", CommonConstants.DATA_IS_DEL_FALSE);
        filter.put("isPrompting", "1");
        filter.put("rUser", sessionUser.getIdCard());

        if(!StringUtil.isNullBlank(isProc) && !"-1".equals(isProc)) {
            filter.put("isProc", isProc);
        }

        switch (type) {
            case "all":
                filter.put("busType", new BasicDBObject("$in", MsgBusTypeEnum.getDamAllBusTypeList()));
                break;
            case "approval":
                filter.put("busType", new BasicDBObject("$in", MsgBusTypeEnum.getDamApproveBusTypeList()));
                break;
            case "catalog":
                filter.put("busType", new BasicDBObject("$in", MsgBusTypeEnum.getDamCatalogBusTypeList()));
                break;
        }

        try {
            Map<String, Object> map = MessageClientUtil.getPageByMark(systemMark ,filter, pageSize,
                    (pageNo - 1) * pageSize, MsgType.TODO.getCode(), "sTime", false);
            Object obj = map.get("page");
            result = (List<JSONObject>) obj;

            result.forEach(item -> {
                item.put("busTypeName", MsgBusTypeEnum.getNameByCode(item.getString("busType")));
            });

            JSONObject data = new JSONObject();
            data.put("rows", result);
            data.put("total", map.get("total"));
            return success(data);
        } catch (Exception e) {
            e.printStackTrace();
            return error("查询消息失败");
        }
    }

}
