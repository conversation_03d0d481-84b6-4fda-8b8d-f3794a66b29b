package com.rs.module.dam.controller.admin.prison.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 一所一档-监所档案宣传报道配置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PublicityConfigRespVO extends BaseVO implements TransPojo{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty(" 报道标题")
    private String publicityTitle;
    @ApiModelProperty("报道时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date publicityTime;
    @ApiModelProperty("报道内容")
    private String publicityContent;
}
