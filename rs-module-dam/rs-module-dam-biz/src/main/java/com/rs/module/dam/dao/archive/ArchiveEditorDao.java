package com.rs.module.dam.dao.archive;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.archive.ArchiveEditorDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.dam.controller.admin.archive.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 卷宗编辑 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ArchiveEditorDao extends IBaseDao<ArchiveEditorDO> {


    default PageResult<ArchiveEditorDO> selectPage(ArchiveEditorPageReqVO reqVO) {
        Page<ArchiveEditorDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ArchiveEditorDO> wrapper = new LambdaQueryWrapperX<ArchiveEditorDO>()
            .eqIfPresent(ArchiveEditorDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(ArchiveEditorDO::getTitle, reqVO.getTitle())
            .eqIfPresent(ArchiveEditorDO::getContent, reqVO.getContent())
            .eqIfPresent(ArchiveEditorDO::getJzid, reqVO.getJzid())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(ArchiveEditorDO::getAddTime);
        }
        Page<ArchiveEditorDO> archiveEditorPage = selectPage(page, wrapper);
        return new PageResult<>(archiveEditorPage.getRecords(), archiveEditorPage.getTotal());
    }
    default List<ArchiveEditorDO> selectList(ArchiveEditorListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ArchiveEditorDO>()
            .eqIfPresent(ArchiveEditorDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(ArchiveEditorDO::getTitle, reqVO.getTitle())
            .eqIfPresent(ArchiveEditorDO::getContent, reqVO.getContent())
            .eqIfPresent(ArchiveEditorDO::getJzid, reqVO.getJzid())
        .orderByDesc(ArchiveEditorDO::getAddTime));    }


    }
