package com.rs.module.dam.dao.borrow;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.borrow.BorrowPrisonerDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 借阅人员档案关联
 *
 * <AUTHOR>
 */
@Mapper
public interface BorrowPrisonerDao extends IBaseDao<BorrowPrisonerDO> {

    default List<BorrowPrisonerDO> selectListByBorrowId(String borrowId) {
        return selectList(new LambdaQueryWrapperX<BorrowPrisonerDO>().eq(BorrowPrisonerDO::getBorrowId, borrowId));
    }

    default int deleteByBorrowId(String borrowId) {
        return delete(new LambdaQueryWrapperX<BorrowPrisonerDO>().eq(BorrowPrisonerDO::getBorrowId, borrowId));
    }

    Map<String, Object> getReapplyDetail(String prisonerId);

}
