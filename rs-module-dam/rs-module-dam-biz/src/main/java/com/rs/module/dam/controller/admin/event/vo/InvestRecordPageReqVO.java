package com.rs.module.dam.controller.admin.event.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel(description = "管理后台 - 一事一档-事件档案调查记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InvestRecordPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("事件ID")
    private String eventId;

    @ApiModelProperty("事件编号(规则：S+9位单位编号+8位日期+6位序号)")
    private String eventCode;

    @ApiModelProperty("调查详情")
    private String investDetails;

    @ApiModelProperty("上传附件存储路径")
    private String attachmentPath;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
