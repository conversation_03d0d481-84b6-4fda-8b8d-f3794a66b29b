package com.rs.module.dam.dao.event;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.event.ArchivePersonnelDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 一事一档-事件档案工作人员关联 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ArchivePersonnelDao extends IBaseDao<ArchivePersonnelDO> {


    default List<ArchivePersonnelDO> selectListByEventId(String eventId) {
        return selectList(new LambdaQueryWrapperX<ArchivePersonnelDO>().eq(ArchivePersonnelDO::getEventId, eventId));
    }

    default int deleteByEventId(String eventId) {
        return delete(new LambdaQueryWrapperX<ArchivePersonnelDO>().eq(ArchivePersonnelDO::getEventId, eventId));
    }


}
