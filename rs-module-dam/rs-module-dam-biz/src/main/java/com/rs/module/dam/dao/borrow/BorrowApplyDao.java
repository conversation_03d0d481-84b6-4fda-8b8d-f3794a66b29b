package com.rs.module.dam.dao.borrow;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApplyListReqVO;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApplyPageReqVO;
import com.rs.module.dam.entity.borrow.BorrowApplyDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 借阅申请 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BorrowApplyDao extends IBaseDao<BorrowApplyDO> {


    default PageResult<BorrowApplyDO> selectPage(BorrowApplyPageReqVO reqVO) {
        Page<BorrowApplyDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<BorrowApplyDO> wrapper = new LambdaQueryWrapperX<BorrowApplyDO>()
            .eqIfPresent(BorrowApplyDO::getApplyNo, reqVO.getApplyNo())
            .eqIfPresent(BorrowApplyDO::getType, reqVO.getType())
            .eqIfPresent(BorrowApplyDO::getBorrowOrgCode, reqVO.getBorrowOrgCode())
            .likeIfPresent(BorrowApplyDO::getBorrowOrgName, reqVO.getBorrowOrgName())
            .eqIfPresent(BorrowApplyDO::getBorrowUser, reqVO.getBorrowUser())
            .likeIfPresent(BorrowApplyDO::getBorrowUserName, reqVO.getBorrowUserName())
            .eqIfPresent(BorrowApplyDO::getBorrowReason, reqVO.getBorrowReason())
            .eqIfPresent(BorrowApplyDO::getBorrowDays, reqVO.getBorrowDays())
            .betweenIfPresent(BorrowApplyDO::getBorrowEndDate, reqVO.getBorrowEndDate())
            .eqIfPresent(BorrowApplyDO::getBorrowNum, reqVO.getBorrowNum())
            .eqIfPresent(BorrowApplyDO::getRemark, reqVO.getRemark())
            .eqIfPresent(BorrowApplyDO::getAttachment, reqVO.getAttachment())
            .eqIfPresent(BorrowApplyDO::getApproveStatus, reqVO.getApproveStatus())
            .eqIfPresent(BorrowApplyDO::getNextApproveRole, reqVO.getNextApproveRole())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(BorrowApplyDO::getAddTime);
        }
        Page<BorrowApplyDO> borrowApplyPage = selectPage(page, wrapper);
        return new PageResult<>(borrowApplyPage.getRecords(), borrowApplyPage.getTotal());
    }

    default List<BorrowApplyDO> selectList(BorrowApplyListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BorrowApplyDO>()
            .eqIfPresent(BorrowApplyDO::getApplyNo, reqVO.getApplyNo())
            .eqIfPresent(BorrowApplyDO::getType, reqVO.getType())
            .eqIfPresent(BorrowApplyDO::getBorrowOrgCode, reqVO.getBorrowOrgCode())
            .likeIfPresent(BorrowApplyDO::getBorrowOrgName, reqVO.getBorrowOrgName())
            .eqIfPresent(BorrowApplyDO::getBorrowUser, reqVO.getBorrowUser())
            .likeIfPresent(BorrowApplyDO::getBorrowUserName, reqVO.getBorrowUserName())
            .eqIfPresent(BorrowApplyDO::getBorrowReason, reqVO.getBorrowReason())
            .eqIfPresent(BorrowApplyDO::getBorrowDays, reqVO.getBorrowDays())
            .betweenIfPresent(BorrowApplyDO::getBorrowEndDate, reqVO.getBorrowEndDate())
            .eqIfPresent(BorrowApplyDO::getBorrowNum, reqVO.getBorrowNum())
            .eqIfPresent(BorrowApplyDO::getRemark, reqVO.getRemark())
            .eqIfPresent(BorrowApplyDO::getAttachment, reqVO.getAttachment())
            .eqIfPresent(BorrowApplyDO::getApproveStatus, reqVO.getApproveStatus())
            .eqIfPresent(BorrowApplyDO::getNextApproveRole, reqVO.getNextApproveRole())
        .orderByDesc(BorrowApplyDO::getAddTime));
    }


}
