package com.rs.module.dam.service.sys;

import java.util.*;
import javax.validation.*;
import com.rs.module.dam.controller.admin.sys.vo.*;
import com.rs.module.dam.entity.sys.CatalogTemplateDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 卷宗目录模板 Service 接口
 *
 * <AUTHOR>
 */
public interface CatalogTemplateService extends IBaseService<CatalogTemplateDO>{

    /**
     * 创建卷宗目录模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCatalogTemplate(@Valid CatalogTemplateSaveReqVO createReqVO);

    /**
     * 更新卷宗目录模板
     *
     * @param updateReqVO 更新信息
     */
    void updateCatalogTemplate(@Valid CatalogTemplateSaveReqVO updateReqVO);

    /**
     * 删除卷宗目录模板
     *
     * @param id 编号
     */
    void deleteCatalogTemplate(String id);

    /**
     * 获得卷宗目录模板
     *
     * @param id 编号
     * @return 卷宗目录模板
     */
    CatalogTemplateDO getCatalogTemplate(String id);

    /**
    * 获得卷宗目录模板分页
    *
    * @param pageReqVO 分页查询
    * @return 卷宗目录模板分页
    */
    PageResult<CatalogTemplateDO> getCatalogTemplatePage(CatalogTemplatePageReqVO pageReqVO);

    /**
    * 获得卷宗目录模板列表
    *
    * @param listReqVO 查询条件
    * @return 卷宗目录模板列表
    */
    List<CatalogTemplateDO> getCatalogTemplateList(CatalogTemplateListReqVO listReqVO);


}
