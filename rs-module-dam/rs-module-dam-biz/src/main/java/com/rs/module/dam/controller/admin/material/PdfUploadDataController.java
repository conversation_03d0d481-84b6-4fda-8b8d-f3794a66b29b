package com.rs.module.dam.controller.admin.material;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.entity.material.PdfUploadDataDO;
import com.rs.module.dam.service.material.PdfUploadDataService;
import com.rs.module.dam.vo.material.PdfUploadDataListReqVO;
import com.rs.module.dam.vo.material.PdfUploadDataPageReqVO;
import com.rs.module.dam.vo.material.PdfUploadDataRespVO;
import com.rs.module.dam.vo.material.PdfUploadDataSaveReqVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

@Api(tags = "材料处理 - pdf上传数据")
@ApiIgnore
@RestController
@RequestMapping("/dam/material/pdfUploadData")
@Validated
public class PdfUploadDataController {

    @Resource
    private PdfUploadDataService pdfUploadDataService;

    @PostMapping("/create")
    @ApiOperation(value = "创建pdf上传数据")
    @LogRecordAnnotation(bizModule = "dam:pdfUploadData:create", operateType = LogOperateType.CREATE, title = "创建pdf上传数据",
    success = "创建pdf上传数据成功", fail = "创建pdf上传数据失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPdfUploadData(@Valid @RequestBody PdfUploadDataSaveReqVO createReqVO) {
        return success(pdfUploadDataService.createPdfUploadData(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新pdf上传数据")
    @LogRecordAnnotation(bizModule = "dam:pdfUploadData:update", operateType = LogOperateType.UPDATE, title = "更新pdf上传数据",
    success = "更新pdf上传数据成功", fail = "更新pdf上传数据失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePdfUploadData(@Valid @RequestBody PdfUploadDataSaveReqVO updateReqVO) {
        pdfUploadDataService.updatePdfUploadData(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除pdf上传数据")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:pdfUploadData:delete", operateType = LogOperateType.DELETE, title = "删除pdf上传数据",
    success = "删除pdf上传数据成功", fail = "删除pdf上传数据失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePdfUploadData(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           pdfUploadDataService.deletePdfUploadData(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得pdf上传数据")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:pdfUploadData:get", operateType = LogOperateType.QUERY, title = "获取pdf上传数据", bizNo = "{{#id}}", success = "获取pdf上传数据成功", fail = "获取pdf上传数据失败", extraInfo = "{{#id}}")
    public CommonResult<PdfUploadDataRespVO> getPdfUploadData(@RequestParam("id") String id) {
        PdfUploadDataDO pdfUploadData = pdfUploadDataService.getPdfUploadData(id);
        return success(BeanUtils.toBean(pdfUploadData, PdfUploadDataRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得pdf上传数据分页")
    @LogRecordAnnotation(bizModule = "dam:pdfUploadData:page", operateType = LogOperateType.QUERY, title = "获得pdf上传数据分页",
    success = "获得pdf上传数据分页成功", fail = "获得pdf上传数据分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<PdfUploadDataRespVO>> getPdfUploadDataPage(@Valid @RequestBody PdfUploadDataPageReqVO pageReqVO) {
        PageResult<PdfUploadDataDO> pageResult = pdfUploadDataService.getPdfUploadDataPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PdfUploadDataRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得pdf上传数据列表")
    @LogRecordAnnotation(bizModule = "dam:pdfUploadData:list", operateType = LogOperateType.QUERY, title = "获得pdf上传数据列表",
    success = "获得pdf上传数据列表成功", fail = "获得pdf上传数据列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<PdfUploadDataRespVO>> getPdfUploadDataList(@Valid @RequestBody PdfUploadDataListReqVO listReqVO) {
    List<PdfUploadDataDO> list = pdfUploadDataService.getPdfUploadDataList(listReqVO);
        return success(BeanUtils.toBean(list, PdfUploadDataRespVO.class));
    }

}
