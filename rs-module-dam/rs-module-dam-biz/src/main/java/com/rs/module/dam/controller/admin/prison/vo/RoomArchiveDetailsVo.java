package com.rs.module.dam.controller.admin.prison.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("一室一档-事务全览")
public class RoomArchiveDetailsVo {

    @ApiModelProperty("业务主键id")
    private String businessId;

    @ApiModelProperty("业务类型")
    private Integer businessType;

    @ApiModelProperty("业务名称")
    private String businessName;

    @ApiModelProperty("业务发生时间")
    private Date businessTime;

    @ApiModelProperty("人员编号")
    private String prisonerId;

    @ApiModelProperty("字段一")
    private String fieldOne;

    @ApiModelProperty("字段二")
    private String fieldTwo;

    @ApiModelProperty("字段三")
    private String fieldThree;
}
