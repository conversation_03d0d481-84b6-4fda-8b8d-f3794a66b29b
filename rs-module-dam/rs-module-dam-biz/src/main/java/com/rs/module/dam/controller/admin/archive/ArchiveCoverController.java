package com.rs.module.dam.controller.admin.archive;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.entity.archive.ArchiveCoverDO;
import com.rs.module.dam.entity.sys.ConfigGroupDO;
import com.rs.module.dam.service.archive.ArchiveCoverService;
import com.rs.module.dam.service.sys.ConfigGroupService;
import com.rs.module.dam.vo.archive.ArchiveCoverListReqVO;
import com.rs.module.dam.vo.archive.ArchiveCoverPageReqVO;
import com.rs.module.dam.vo.archive.ArchiveCoverRespVO;
import com.rs.module.dam.vo.archive.ArchiveCoverSaveReqVO;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@Api(tags = "卷宗归档 - 卷宗封面")
@RestController
@RequestMapping("/dam/archive/archiveCover")
@Validated
public class ArchiveCoverController {

    @Resource
    private ArchiveCoverService archiveCoverService;
    
    @Resource
    private ConfigGroupService configGroupService;

    @PostMapping("/create")
    @ApiOperation(value = "创建卷宗封面")
    @LogRecordAnnotation(bizModule = "dam:archiveCover:create", operateType = LogOperateType.CREATE, title = "创建卷宗封面",
    success = "创建卷宗封面成功", fail = "创建卷宗封面失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createArchiveCover(@Valid @RequestBody ArchiveCoverSaveReqVO createReqVO) {
        return success(archiveCoverService.createArchiveCover(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新卷宗封面")
    @LogRecordAnnotation(bizModule = "dam:archiveCover:update", operateType = LogOperateType.UPDATE, title = "更新卷宗封面",
    success = "更新卷宗封面成功", fail = "更新卷宗封面失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateArchiveCover(@Valid @RequestBody ArchiveCoverSaveReqVO updateReqVO) {
        archiveCoverService.updateArchiveCover(updateReqVO);
        return success(true);
    }
    
    @PostMapping("/save")
    @ApiOperation(value = "保存卷宗封面")
    @LogRecordAnnotation(bizModule = "dam:archiveCover:save", operateType = LogOperateType.CREATE, title = "保存卷宗封面",
    success = "保存卷宗封面成功", fail = "保存卷宗封面失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<?> saveArchiveCover(@Valid @RequestBody ArchiveCoverSaveReqVO createReqVO) {
    	if(StringUtil.isNotEmpty(createReqVO.getId())) {
    		archiveCoverService.updateArchiveCover(createReqVO);
            return success(true);
    	}
    	else {
    		return success(archiveCoverService.createArchiveCover(createReqVO));
    	}
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除卷宗封面")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:archiveCover:delete", operateType = LogOperateType.DELETE, title = "删除卷宗封面",
    success = "删除卷宗封面成功", fail = "删除卷宗封面失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteArchiveCover(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           archiveCoverService.deleteArchiveCover(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得卷宗封面")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:archiveCover:get", operateType = LogOperateType.QUERY, title = "获取卷宗封面",
    	bizNo = "{{#id}}", success = "获取卷宗封面成功", fail = "获取卷宗封面失败", extraInfo = "{{#id}}")
    public CommonResult<ArchiveCoverRespVO> getArchiveCover(@RequestParam("id") String id) {
        ArchiveCoverDO archiveCover = archiveCoverService.getArchiveCover(id);
        return success(BeanUtils.toBean(archiveCover, ArchiveCoverRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得卷宗封面分页")
    @LogRecordAnnotation(bizModule = "dam:archiveCover:page", operateType = LogOperateType.QUERY, title = "获得卷宗封面分页",
    success = "获得卷宗封面分页成功", fail = "获得卷宗封面分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<ArchiveCoverRespVO>> getArchiveCoverPage(@Valid @RequestBody ArchiveCoverPageReqVO pageReqVO) {
        PageResult<ArchiveCoverDO> pageResult = archiveCoverService.getArchiveCoverPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ArchiveCoverRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得卷宗封面列表")
    @LogRecordAnnotation(bizModule = "dam:archiveCover:list", operateType = LogOperateType.QUERY, title = "获得卷宗封面列表",
    success = "获得卷宗封面列表成功", fail = "获得卷宗封面列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<ArchiveCoverRespVO>> getArchiveCoverList(@Valid @RequestBody ArchiveCoverListReqVO listReqVO) {
    List<ArchiveCoverDO> list = archiveCoverService.getArchiveCoverList(listReqVO);
        return success(BeanUtils.toBean(list, ArchiveCoverRespVO.class));
    }

    @PostMapping("/getScPdf")
    @ApiOperation(value = "获取双层pdf地址")
    @ApiImplicitParams({
    	@ApiImplicitParam(name = "jgrybm", value = "监管人员编码"),
		@ApiImplicitParam(name = "partCatalogId", value = "分卷目录Id")
    })
    @LogRecordAnnotation(bizModule = "dam:archiveEditor:getUserEditor", operateType = LogOperateType.QUERY, title = "双层pdf地址",
    success = "双层pdf地址成功", fail = "双层pdf地址失败", extraInfo = "{TO_JSON{#jgrybm}}")
    public CommonResult<?> getScPdf(
    		@RequestParam(value = "jgrybm", required = true) String jgrybm,
    		@RequestParam(value = "partCatalogId", required = false) String partCatalogId) {
    	QueryWrapper<ArchiveCoverDO> wrapper = new QueryWrapper<>();
    	wrapper.eq("is_del", 0);
    	wrapper.eq("jgrybm", jgrybm);
    	wrapper.eq("part_catalog_id", partCatalogId);
    	
    	//卷宗封面
    	ArchiveCoverDO archiveCover = archiveCoverService.getOne(wrapper);
    	if(archiveCover == null) {
    		return CommonResult.error("找不到卷宗封面");
    	}
    	
    	//卷宗配置
    	ConfigGroupDO configGroup = configGroupService.getByOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
    	if(configGroup == null) {
    		return CommonResult.error("找不到卷宗配置");
    	}
    	
    	//生成双层pdf
    	if(StringUtil.getBoolean(configGroup.getMakeDoublePdf())) {
    		if(StringUtil.isEmpty(archiveCover.getDoublePdfUrl())) {
    			return CommonResult.error("PDF正在生成");
    		}
    		else {
    			return CommonResult.success(JSONUtil.createObj()
    					.set("scpdf", archiveCover.getDoublePdfUrl())
    					.set("isScpdf", true));
    		}
    	}
    	else {
    		if(StringUtil.isEmpty(archiveCover.getPdfUrl())) {
    			return CommonResult.error("PDF正在生成");
    		}
    		else {
    			Integer coverPage = archiveCover.getCoverPage();
    			coverPage = coverPage == null ? 2 : coverPage;
    			return CommonResult.success(JSONUtil.createObj()
    					.set("scpdf", archiveCover.getPdfUrl())
    					.set("isScpdf", false)
    					.set("coverPage", coverPage));
    		}
    	}
    }
}
