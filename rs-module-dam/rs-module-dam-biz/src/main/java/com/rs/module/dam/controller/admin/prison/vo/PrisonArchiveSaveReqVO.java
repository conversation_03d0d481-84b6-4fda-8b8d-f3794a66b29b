package com.rs.module.dam.controller.admin.prison.vo;

import com.rs.framework.annotation.SaveFile;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.dam.entity.prison.FeatureConfigDO;
import com.rs.module.dam.entity.prison.PublicityConfigDO;
import com.rs.module.dam.entity.prison.RecognitionConfigDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.x.file.storage.core.FileInfo;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@ApiModel(description = "管理后台 - 一所一档-监所档案新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonArchiveSaveReqVO extends BaseVO{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监所的名称")
    @NotEmpty(message = "监所的名称不能为空")
    private String prisonName;

    @ApiModelProperty("监所单位机构代码")
    @NotEmpty(message = "监所单位机构代码不能为空")
    private String prisonCode;

    @ApiModelProperty("监所联系电话")
    private String contactPhone;

    @ApiModelProperty("电子邮件地址")
    private String email;

    @ApiModelProperty("邮政编码")
    private String postCode;

    @ApiModelProperty("监所地址")
    @NotEmpty(message = "监所地址不能为空")
    private String address;

    @ApiModelProperty("对监所简介")
    @NotEmpty(message = "对监所简介不能为空")
    private String remarks;

    @ApiModelProperty("监所的建筑总面积")
    @NotNull(message = "监所的建筑总面积不能为空")
    private BigDecimal buildingArea;

    @ApiModelProperty("监室数量")
    @NotNull(message = "监室数量不能为空")
    private Integer monitoringRoomCount;

    @ApiModelProperty("监室总可容纳人数")
    @NotNull(message = "监室总可容纳人数不能为空")
    private Integer monitoringRoomCapacity;

    @ApiModelProperty("监所内功能室数量")
    @NotNull(message = "监所内功能室数量不能为空")
    private Integer functionRoomCount;

    @ApiModelProperty("监所背景图片存储地址")
    private String backgroundImageUrl;

    @ApiModelProperty("功能室配置情况")
    private List<PrisonArchiveConfigVO> functionRoomConfiguration;

    @ApiModelProperty("装备配备情况")
    private List<PrisonArchiveConfigVO> equipmentConfiguration;

    @ApiModelProperty("装备数量")
    @NotNull(message = "装备数量不能为空")
    private Integer equipmentCount;

    @ApiModelProperty("警力总人数")
    @NotNull(message = "警力总人数不能为空")
    private Integer totalPoliceForce;

    @ApiModelProperty("民警总数量")
    @NotNull(message = "民警总数量不能为空")
    private Integer policeOfficerCount;

    @ApiModelProperty("辅警总数量")
    @NotNull(message = "辅警总数量不能为空")
    private Integer auxiliaryPoliceCount;

    @ApiModelProperty("累计羁押人数")
    @NotNull(message = "累计羁押人数不能为空")
    private Integer totalDetainedCount;

    @ApiModelProperty("累计办理案件数量")
    @NotNull(message = "累计办理案件数量不能为空")
    private Integer totalCaseCount;

    @ApiModelProperty("累计讯 / 询问嫌疑人数")
    @NotNull(message = "累计讯 / 询问嫌疑人数不能为空")
    private Integer totalInterrogatedSuspectCount;

    @ApiModelProperty("累计管理涉案财物件数")
    @NotNull(message = "累计管理涉案财物件数不能为空")
    private Integer totalSeizedPropertyCount;

    @ApiModelProperty("累计管理涉案财物总价值")
    @NotNull(message = "累计管理涉案财物总价值不能为空")
    private BigDecimal totalSeizedPropertyValue;

    @ApiModelProperty("评定等级")
    private String ratingLevel;

    @SaveFile
    List<FileInfo> fileList;

}
