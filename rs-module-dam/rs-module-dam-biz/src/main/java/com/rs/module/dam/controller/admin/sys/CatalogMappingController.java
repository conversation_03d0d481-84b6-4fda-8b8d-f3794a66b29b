package com.rs.module.dam.controller.admin.sys;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.dam.controller.admin.sys.vo.*;
import com.rs.module.dam.entity.sys.CatalogMappingDO;
import com.rs.module.dam.service.sys.CatalogMappingService;

@Api(tags = "系统配置 - 目录文书映射")
@ApiIgnore
@RestController
@RequestMapping("/dam/sys/catalogMapping")
@Validated
public class CatalogMappingController {

    @Resource
    private CatalogMappingService catalogMappingService;

    @PostMapping("/create")
    @ApiOperation(value = "创建目录文书映射")
    @LogRecordAnnotation(bizModule = "dam:catalogMapping:create", operateType = LogOperateType.CREATE, title = "创建目录文书映射",
    success = "创建目录文书映射成功", fail = "创建目录文书映射失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createCatalogMapping(@Valid @RequestBody CatalogMappingSaveReqVO createReqVO) {
        return success(catalogMappingService.createCatalogMapping(createReqVO));
    }

    @SuppressWarnings("unchecked")
	@PostMapping("/createBatch")
    @ApiOperation(value = "创建目录文书映射")
    @LogRecordAnnotation(bizModule = "dam:catalogMapping:create", operateType = LogOperateType.CREATE, title = "创建目录文书映射",
            success = "创建目录文书映射成功", fail = "创建目录文书映射失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createBatch(@Valid @RequestBody JSONObject params) {
        JSONArray jsonArray = params.getJSONArray("list");
        List<CatalogMappingDO> list = JSONObject.parseArray(JSONObject.toJSONString(jsonArray), CatalogMappingDO.class);
        catalogMappingService.createBatch(list);
        return success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新目录文书映射")
    @LogRecordAnnotation(bizModule = "dam:catalogMapping:update", operateType = LogOperateType.UPDATE, title = "更新目录文书映射",
    success = "更新目录文书映射成功", fail = "更新目录文书映射失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateCatalogMapping(@Valid @RequestBody CatalogMappingSaveReqVO updateReqVO) {
        catalogMappingService.updateCatalogMapping(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除目录文书映射")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:catalogMapping:delete", operateType = LogOperateType.DELETE, title = "删除目录文书映射",
    success = "删除目录文书映射成功", fail = "删除目录文书映射失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteCatalogMapping(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           catalogMappingService.deleteCatalogMapping(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得目录文书映射")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:catalogMapping:get", operateType = LogOperateType.QUERY, title = "获取目录文书映射",
            bizNo = "{{#id}}", success = "获取目录文书映射成功", fail = "获取目录文书映射失败", extraInfo = "{{#id}}")
    public CommonResult<CatalogMappingRespVO> getCatalogMapping(@RequestParam("id") String id) {
        CatalogMappingDO catalogMapping = catalogMappingService.getCatalogMapping(id);
        return success(BeanUtils.toBean(catalogMapping, CatalogMappingRespVO.class));
    }

    @GetMapping("/getByCatalogId")
    @ApiOperation(value = "获得目录文书映射")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:catalogMapping:get", operateType = LogOperateType.QUERY, title = "获取目录文书映射",
            bizNo = "{{#id}}", success = "获取目录文书映射成功", fail = "获取目录文书映射失败", extraInfo = "{{#id}}")
    public CommonResult<List<CatalogMappingRespVO>> getByCatalogId(@RequestParam("catalogId") String catalogId) {
        List<CatalogMappingDO> catalogMapping = catalogMappingService.list(
                new LambdaQueryWrapper<CatalogMappingDO>()
                        .eq(CatalogMappingDO::getCatalogId, catalogId));
        return success(BeanUtils.toBean(catalogMapping, CatalogMappingRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得目录文书映射分页")
    @LogRecordAnnotation(bizModule = "dam:catalogMapping:page", operateType = LogOperateType.QUERY, title = "获得目录文书映射分页",
    success = "获得目录文书映射分页成功", fail = "获得目录文书映射分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<CatalogMappingRespVO>> getCatalogMappingPage(@Valid @RequestBody CatalogMappingPageReqVO pageReqVO) {
        PageResult<CatalogMappingDO> pageResult = catalogMappingService.getCatalogMappingPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CatalogMappingRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得目录文书映射列表")
    @LogRecordAnnotation(bizModule = "dam:catalogMapping:list", operateType = LogOperateType.QUERY, title = "获得目录文书映射列表",
    success = "获得目录文书映射列表成功", fail = "获得目录文书映射列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<CatalogMappingRespVO>> getCatalogMappingList(@Valid @RequestBody CatalogMappingListReqVO listReqVO) {
    List<CatalogMappingDO> list = catalogMappingService.getCatalogMappingList(listReqVO);
        return success(BeanUtils.toBean(list, CatalogMappingRespVO.class));
    }

}
