package com.rs.module.dam.controller.admin.prison.vo;

import com.rs.framework.annotation.GetFile;
import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.dromara.x.file.storage.core.FileInfo;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 一所一档-监所档案荣誉表彰配置列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RecognitionConfigListReqVO extends BaseVO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("表彰名称")
    private String recognitionName;

    @ApiModelProperty("表彰时间")
    private Date[] recognitionTime;

    @GetFile(value = "id")
    public List<FileInfo> fileList;

}
