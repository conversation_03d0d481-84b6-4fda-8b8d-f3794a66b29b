package com.rs.module.dam.controller.admin.material;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.cons.CommonConstants;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.entity.archive.ArchiveCoverDO;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.entity.material.MaterialLabelDO;
import com.rs.module.dam.entity.prisoner.PrisonerCatalogDO;
import com.rs.module.dam.ocr.handler.OcrHandler;
import com.rs.module.dam.ocr.vo.OcrScpdfParams;
import com.rs.module.dam.service.archive.ArchiveCoverService;
import com.rs.module.dam.service.material.MaterialInfoService;
import com.rs.module.dam.service.material.MaterialLabelService;
import com.rs.module.dam.service.material.MaterialMarkerService;
import com.rs.module.dam.service.prisoner.PrisonerCatalogService;
import com.rs.module.dam.util.DamUtil;
import com.rs.module.dam.vo.material.MaterialInfoListReqVO;
import com.rs.module.dam.vo.material.MaterialInfoPageReqVO;
import com.rs.module.dam.vo.material.MaterialInfoRespVO;
import com.rs.module.dam.vo.material.MaterialInfoSaveReqVO;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@Api(tags = "材料处理 - 卷宗材料")
@RestController
@RequestMapping("/dam/material/materialInfo")
@Validated
public class MaterialInfoController {

    @Resource
    private MaterialInfoService materialInfoService;
    
    @Resource
    private PrisonerCatalogService prisonerCatalogService;
    
    @Resource
    private MaterialLabelService materialLabelService;
    
    @Resource
    private MaterialMarkerService materialMarkerService;
    
    @Resource
    private ArchiveCoverService archiveCoverService;
    
    @Resource
	private OcrHandler ocrHandler;

    @PostMapping("/create")
    @ApiOperation(value = "创建卷宗材料", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:materialInfo:create", operateType = LogOperateType.CREATE, title = "创建卷宗材料",
    success = "创建卷宗材料成功", fail = "创建卷宗材料失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createMaterialInfo(@Valid @RequestBody MaterialInfoSaveReqVO createReqVO) {
        return success(materialInfoService.createMaterialInfo(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新卷宗材料", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:materialInfo:update", operateType = LogOperateType.UPDATE, title = "更新卷宗材料",
    success = "更新卷宗材料成功", fail = "更新卷宗材料失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateMaterialInfo(@Valid @RequestBody MaterialInfoSaveReqVO updateReqVO) {
        materialInfoService.updateMaterialInfo(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除卷宗材料")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:materialInfo:delete", operateType = LogOperateType.DELETE, title = "删除卷宗材料",
    success = "删除卷宗材料成功", fail = "删除卷宗材料失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteMaterialInfo(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           materialInfoService.deleteMaterialInfo(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得卷宗材料")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:materialInfo:get", operateType = LogOperateType.QUERY, title = "获取卷宗材料",
    	bizNo = "{{#id}}", success = "获取卷宗材料成功", fail = "获取卷宗材料失败", extraInfo = "{{#id}}")
    public CommonResult<MaterialInfoRespVO> getMaterialInfo(@RequestParam("id") String id) {
        MaterialInfoDO materialInfo = materialInfoService.getMaterialInfo(id);
        return success(BeanUtils.toBean(materialInfo, MaterialInfoRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得卷宗材料分页", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:materialInfo:page", operateType = LogOperateType.QUERY, title = "获得卷宗材料分页",
    success = "获得卷宗材料分页成功", fail = "获得卷宗材料分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<MaterialInfoRespVO>> getMaterialInfoPage(@Valid @RequestBody MaterialInfoPageReqVO pageReqVO) {
        PageResult<MaterialInfoDO> pageResult = materialInfoService.getMaterialInfoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MaterialInfoRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得卷宗材料列表", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:materialInfo:list", operateType = LogOperateType.QUERY, title = "获得卷宗材料列表",
    success = "获得卷宗材料列表成功", fail = "获得卷宗材料列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<MaterialInfoRespVO>> getMaterialInfoList(@Valid @RequestBody MaterialInfoListReqVO listReqVO) {
    	List<MaterialInfoDO> list = materialInfoService.getMaterialInfoList(listReqVO);
        return success(BeanUtils.toBean(list, MaterialInfoRespVO.class));
    }

    @PostMapping("/getMaterialByJgrybm")
    @ApiOperation(value = "获取监管人员的卷宗材料")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "jgrybm", value = "监管人员编码"),
		@ApiImplicitParam(name = "type", value = "材料类型(0：未OCR,1：编目,2：组卷,3: 快速组卷)"),
		@ApiImplicitParam(name = "catalogId", value = "目录Id"),
		@ApiImplicitParam(name = "partCatalogId", value = "分卷目录Id"),
		@ApiImplicitParam(name = "id", value = "材料Id"),
		@ApiImplicitParam(name = "isZnyj", value = "是否智能阅卷"),
		@ApiImplicitParam(name = "isView", value = "是否预览"),
		@ApiImplicitParam(name = "isYj", value = "是否阅卷")
	})
    public CommonResult<?> getMaterialByJgrybm(@RequestParam("jgrybm") String jgrybm,
    		@RequestParam("type") String type,
    		@RequestParam(value = "catalogId", required = false) String catalogId,
    		@RequestParam(value = "partCatalogId", required = false) String partCatalogId,
    		@RequestParam(value = "id", required = false) String id,
    		@RequestParam(value = "isZnyj", required = false) String isZnyj,
    		@RequestParam(value = "isView", required = false) String isView,
    		@RequestParam(value = "isYj", required = false) String isYj) {
    	QueryWrapper<MaterialInfoDO> wrapper = DamUtil.getMaterialInfoWrapper(jgrybm, type);
    	if(StringUtil.isNotEmpty(catalogId)) {
    		wrapper.eq("catalog_id", catalogId);
    	}
    	if(StringUtil.isNotEmpty(partCatalogId)) {
    		wrapper.in("part_catalog_id", Arrays.asList(partCatalogId.split(CommonConstants.DEFAULT_SPLIT_STR)));
    	}
    	if(StringUtil.isNotEmpty(id)) {
    		wrapper.eq("id", id);
    	}
    	
    	//卷宗材料
    	List<MaterialInfoDO> materialInfoList = new ArrayList<>();
    	
    	//智能阅卷
    	if(StringUtil.isNotEmpty(isZnyj)) {
    		
    		//卷宗借阅模式
    		if(StringUtil.isNotEmpty(partCatalogId)) {
    			String[] partCatalogIdArray = partCatalogId.split(",");
				StringBuilder partCatalogIdString = new StringBuilder();
				for (int i = 0; i < partCatalogIdArray.length; i++) {
					partCatalogIdString.append("WHEN '").append(partCatalogIdArray[i])
						.append("' THEN ").append(i).append("\n");
				}
				partCatalogIdString.append("END");
				String orderString = "CASE partCatalogId " + partCatalogIdString.toString() + ", addTime ASC";

				QueryWrapper<MaterialInfoDO> jyWrapper = new QueryWrapper<>();
				jyWrapper.eq("jgrybm", jgrybm);
				jyWrapper.in("type", Arrays.asList(type.split(",")));
				jyWrapper.in("part_catalog_id", Arrays.asList(partCatalogIdArray));
				jyWrapper.orderByAsc(true, orderString);
				materialInfoList = materialInfoService.list(jyWrapper);
    		}
    		else {
    			QueryWrapper<PrisonerCatalogDO> queryWrapper = new QueryWrapper<>();
    			queryWrapper.eq("jgrybm", jgrybm);
    			PrisonerCatalogDO prisonerCatalog = prisonerCatalogService.getOne(queryWrapper);
    			
    			//编目组卷-确认组卷目录
    			JSONArray zjCatalogArray = JSON.parseArray(prisonerCatalog.getZjCatalogData());
    			
    			//快速组卷目录
    			JSONArray kszjCatalogArray = JSON.parseArray(prisonerCatalog.getKszjCatalogData());
    			
    			List<MaterialInfoDO> zjMaterialInfo = new ArrayList<>();
				List<String> zjPartCatalogIds = new ArrayList<>();
				if (zjCatalogArray != null && zjCatalogArray.size() > 0) {
					for (int i = 0; i < zjCatalogArray.size(); i++) {
						JSONObject zjCatalogObject = zjCatalogArray.getJSONObject(i);
						if("0".equals(zjCatalogObject.getString("level"))) {
							zjPartCatalogIds.add(zjCatalogObject.getString("id"));
						}
					}

					QueryWrapper<MaterialInfoDO> zjWrapper = new QueryWrapper<>();
					zjWrapper.eq("jgrybm", jgrybm);
					zjWrapper.eq("type", "2");
					zjWrapper.orderByAsc("xh");
					zjWrapper.orderByAsc("add_time");
					zjMaterialInfo = materialInfoService.list(zjWrapper);
				}

				List<String> kszjPartCatalogIds = new ArrayList<>();
				List<MaterialInfoDO> kszjMaterialInfo = new ArrayList<>();
				if(kszjCatalogArray != null) {
					// 去除快速组卷文书目录中和确认组卷相同的分卷数据
					Iterator<Object> iterator = kszjCatalogArray.iterator();
					while (iterator.hasNext()) {
						JSONObject jsonObject = (JSONObject) iterator.next();
						String kszjPartCatalogId = jsonObject.getString("id");

						if (CollectionUtil.isNotNull(zjPartCatalogIds)) {
							if (zjPartCatalogIds.contains(kszjPartCatalogId)) {
								iterator.remove();
							} else {
								kszjPartCatalogIds.add(kszjPartCatalogId);
							}
						} else {
							kszjPartCatalogIds.add(kszjPartCatalogId);
						}
					}

					if (CollectionUtil.isNotNull(kszjPartCatalogIds)) {
						QueryWrapper<MaterialInfoDO> kszjWrapper = new QueryWrapper<>();
						kszjWrapper.eq("jgrybm", jgrybm);
						kszjWrapper.eq("type", "3");
						kszjWrapper.in("part_catalog_id", kszjPartCatalogIds);
						kszjWrapper.orderByAsc("xh");
						kszjWrapper.orderByAsc("add_time");
						kszjMaterialInfo = materialInfoService.list(kszjWrapper);
					}
				}

				materialInfoList.addAll(zjMaterialInfo);
				materialInfoList.addAll(kszjMaterialInfo);
    		}
    	}
    	else {
    		materialInfoList = materialInfoService.list(wrapper);
    	}
    	
    	//已编目、未编目、快速组卷数量
    	int ybmTotal = 0, wbmTotal = 0, kszjTotal = 0;
    	for(MaterialInfoDO materialInfo : materialInfoList) {
    		
    		//材料已编目或组卷
    		if("2".equals(materialInfo.getType()) || 
    				("1".equals(materialInfo.getType()) && StringUtil.isNotEmpty(materialInfo.getCatalogId()))){
    			ybmTotal += materialInfo.getPageCount();
    		}
    		else {
    			wbmTotal += materialInfo.getPageCount();
    		}

			//快速组卷
			if("3".equals(materialInfo.getType()) ){
				kszjTotal += materialInfo.getPageCount();
			}
    	}
    	
    	//监管人员上传的pdf数量
    	List<String> types = Arrays.asList(type.split(CommonConstants.DEFAULT_SPLIT_STR));
    	int pdfCount = materialInfoService.selectUploadPdfCountByType(jgrybm, types);
    	
    	//监管人员材料的标注Id
    	List<String> fileIdList = materialMarkerService.selectMarkerFileIdByJgrybm(jgrybm);
    	
    	//非预览情况
    	if(StringUtil.isEmpty(isView)) {			
    		return CommonResult.success(JSONUtil.createObj()
    				.set("ybmTotal", ybmTotal)
    				.set("wbmTotal", wbmTotal)
    				.set("kszjTotal", kszjTotal)
    				.set("materialList", materialInfoList)
    				.set("pdfCount", pdfCount)
    				.set("fileIdList", fileIdList));
    	}
    	
    	//智能阅卷-有标签的材料
    	int photoNum = 0;
    	List<MaterialInfoDO> hasLabelPhotos = new ArrayList<>();
    	String userIdCard = SessionUserUtil.getSessionUser().getIdCard();
    	for(MaterialInfoDO materialInfo : materialInfoList) {
    		JSONArray fileArr = JSON.parseArray(materialInfo.getMaterialFile());
			JSONArray itemArr = new JSONArray();
			JSONArray labelArr = new JSONArray();
			
			//判断当前用户是否有标签并累加数量
			for(int i = 0; i < fileArr.size(); i ++) {
				JSONObject obj = fileArr.getJSONObject(i);
				String fileId = obj.getString("id");				
				QueryWrapper<MaterialLabelDO> wrapperLabel = new QueryWrapper<>();
				wrapperLabel.eq("file_id", fileId);
				wrapperLabel.eq("add_user", userIdCard);
				MaterialLabelDO materialLabelDO = materialLabelService.getOne(wrapperLabel);
				if(materialLabelDO != null) {
					obj.put("hasLabel", true);
					labelArr.add(obj);
					photoNum ++;
				}
				itemArr.add(obj);
			}
			
			//标签处理
			if(labelArr.size() > 0) {
				MaterialInfoDO materialInfoLabel = new MaterialInfoDO();
				BeanUtils.copyProperties(materialInfo, materialInfoLabel);
				materialInfoLabel.setMaterialFile(labelArr.toJSONString());
				materialInfoLabel.setPageCount(labelArr.size());
				hasLabelPhotos.add(materialInfoLabel);
			}
			
			materialInfo.setMaterialFile(itemArr.toJSONString());
    	}
    	
    	//智能阅卷-我的标签
    	if(StringUtil.isNotEmpty(isYj)) {
    		return CommonResult.success(JSONUtil.createObj()
    				.set("ybmTotal", ybmTotal)
    				.set("wbmTotal", wbmTotal)
    				.set("kszjTotal", kszjTotal)
    				.set("materialList", materialInfoList)
    				.set("pdfCount", pdfCount)
    				.set("fileIdList", fileIdList)
    				.set("photoNum", photoNum)
    				.set("hasLabelPhotos", hasLabelPhotos));
    	}
    	
    	return CommonResult.success(JSONUtil.createObj()
				.set("ybmTotal", ybmTotal)
				.set("wbmTotal", wbmTotal)
				.set("kszjTotal", kszjTotal)
				.set("materialList", materialInfoList)
				.set("pdfCount", pdfCount)
				.set("fileIdList", fileIdList));
    }
    
    @PostMapping("/operate")
    @ApiOperation(value = "对材料进行操作")
    @ApiImplicitParam(name = "materialJson", value = "材料数据(json数组)")
    @LogRecordAnnotation(bizModule = "dam:materialInfo:operate", operateType = LogOperateType.QUERY, title = "对材料进行操作",
    success = "对材料进行操作成功", fail = "对材料进行操作失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<?> operate(@Valid @RequestBody JSONArray materialArray, HttpServletRequest request) {
    	try {
    		if(materialArray == null || materialArray.isEmpty()) {
    			return CommonResult.error("卷宗材料数据不能为空");
    		}
    		
    		if(materialInfoService.materialOperate(materialArray, request)) {
    			return CommonResult.success("操作成功");
    		}
    		else {
    			return CommonResult.error("操作失败");
    		}
    	}
    	catch(Exception e) {
    		return CommonResult.error("材料操作出现异常：" + e.getMessage());
    	}
    }
    
    @PostMapping("/saveZjMaterial")
    @ApiOperation(value = "保存组卷材料(确认组卷)")
    @ApiImplicitParam(name = "data", value = "组卷数据")
    @LogRecordAnnotation(bizModule = "dam:materialInfo:saveZjMaterial", operateType = LogOperateType.QUERY, title = "保存组卷材料(确认组卷)",
    success = "保存组卷材料(确认组卷)操作成功", fail = "保存组卷材料(确认组卷)失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<?> saveZjMaterial(@Valid @RequestBody String data, HttpServletRequest request) {
    	try {
    		if (StringUtil.isEmpty(data)) {
				return CommonResult.error("未接收到组卷材料数据");
			}
    		
    		//使用V2生成双层pdf
			List<OcrScpdfParams> ocrV2ParamsList = ocrHandler.enableV2() ? new ArrayList<>() : null;
			if(materialInfoService.saveZjMaterial(data, ocrV2ParamsList)) {
				
				//生成双层pdf
				if(!ocrV2ParamsList.isEmpty()) {
					for(OcrScpdfParams params : ocrV2ParamsList) {
						ocrHandler.startScPdf(params.getCover(), params.getMaterialList(),
								params.getJgrybm(), params.getZjType(), params.getIdCard(),
								params.getOrgCode());
					}
				}
				
				return CommonResult.success("操作成功");
			}
			else {
				return CommonResult.error("操作失败");
			}
    	}
    	catch(Exception e) {
    		return CommonResult.error("材料操作出现异常：" + e.getMessage());
    	}
    }
    
    @GetMapping("/getPartByJgrybm")
    @ApiOperation(value = "根据监管人员编码获取分卷材料")
    @ApiImplicitParam(name = "jgrybm", value = "监管人员编码")
    @LogRecordAnnotation(bizModule = "dam:materialInfo:getPartByJgrybm", operateType = LogOperateType.QUERY, title = "获取分卷材料",
    	bizNo = "{{#id}}", success = "获取分卷材料成功", fail = "获取分卷材料失败", extraInfo = "{{#jgrybm}}")
    public CommonResult<?> getPartByJgrybm(@RequestParam("jgrybm") String jgrybm) {
    	try {
    		QueryWrapper<ArchiveCoverDO> wrapper = new QueryWrapper<>();
    		wrapper.eq("jgrybm", jgrybm);
    		List<ArchiveCoverDO> archiveCoverList = archiveCoverService.list(wrapper);
    		
    		return CommonResult.success(JSONUtil.createObj()
    				.set("partList", archiveCoverList));
    	}
    	catch(Exception e) {
    		return CommonResult.error("获取分卷材料出现异常：" + e.getMessage());
    	}
    }
    
    @PostMapping("/getCatalogAndMaterialByJgrybm")
    @ApiOperation(value = "获取监管人员的卷宗目录和材料")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "jgrybm", value = "监管人员编码"),
		@ApiImplicitParam(name = "type", value = "材料类型(0：未OCR,1：编目,2：组卷,3: 快速组卷)")
	})
    public CommonResult<?> getCatalogAndMaterialByJgrybm(@RequestParam("jgrybm") String jgrybm,
    		@RequestParam("type") String type) {
    	try {
    		QueryWrapper<PrisonerCatalogDO> catalogWrapper = new QueryWrapper<>();
    		catalogWrapper.eq("jgrybm", jgrybm);
    		JSONArray zjCatalog = new JSONArray();
    		PrisonerCatalogDO catalog = prisonerCatalogService.getOne(catalogWrapper);
    		if(catalog != null) {
    			if(StringUtil.isNotEmpty(catalog.getZjCatalogData())) {
    				
    				//组卷目录
    				zjCatalog = JSONObject.parseArray(catalog.getZjCatalogData());
    				
    				//获取卷宗材料
    				QueryWrapper<MaterialInfoDO> materWrapper = new QueryWrapper<>();
    				materWrapper.eq("jgrybm", jgrybm);
    				materWrapper.in("type", Arrays.asList(type.split(CommonConstants.DEFAULT_SPLIT_STR)));
    				List<MaterialInfoDO> materialList = materialInfoService.list(materWrapper);
    				
    				//将材料绑定到目录中
    				for(MaterialInfoDO material : materialList) {
    					JSONObject object = new JSONObject();
    					
                        object.put("id", material.getId());
                        object.put("catalogId", material.getCatalogId());
                        object.put("parentId", material.getCatalogId());
                        object.put("isDoc", true);
                        object.put("materialFile", material.getMaterialFile());
                        object.put("name", material.getName());
                        object.put("pageCount", material.getPageCount());
                        object.put("responsiblePerson", material.getResponsiblePerson());
                        object.put("symbol", material.getSymbol());
						object.put("templateId", material.getTemplateId());
						String date = (material.getDate() != null ?
								new SimpleDateFormat("yyyy-MM-dd").format(material.getDate()) : "");
                        object.put("date", date);
                        object.put("remark", material.getRemark());
                        
                        zjCatalog.add(object);
    				}
    			}
    		}
    		
    		return CommonResult.success(JSONUtil.createObj().set("zjCatalog", zjCatalog));
    	}
    	catch(Exception e) {
    		return CommonResult.error("获取监管人员的卷宗目录和材料出现异常：" + e.getMessage());
    	}
    }
    
    @PostMapping("/getImageUrlByJgrybm")
    @ApiOperation(value = "获取监管人员的材料图片地址")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "jgrybm", value = "监管人员编码")
	})
    public CommonResult<?> getImageUrlByJgrybm(@RequestParam("jgrybm") String jgrybm) {
    	try {
    		Map<String, Object> resultMap = new HashMap<>();
    		Map<String, Object> fjlxMap = new HashMap<>();
    		
    		//获取卷宗封面
    		QueryWrapper<ArchiveCoverDO> wrapper = new QueryWrapper<>();
    		wrapper.eq("jgrybm", jgrybm);
    		List<ArchiveCoverDO> coverList = archiveCoverService.list(wrapper);
    		for(ArchiveCoverDO archiveCover : coverList) {
    			resultMap.put(archiveCover.getPartCatalogId(), JSONObject.parseArray(archiveCover.getImageUrl()));
    			fjlxMap.put(archiveCover.getPartCatalogId(), archiveCover.getFjlx());
    		}
    		
    		resultMap.put("fjlx", fjlxMap);
    		return CommonResult.success(resultMap);
    	}
    	catch(Exception e) {
    		return CommonResult.error("获取监管人员的材料图片地址出现异常：" + e.getMessage());
    	}
    }
}
