package com.rs.module.dam.controller.admin.archive;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.io.ByteArrayInputStream;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.apache.poi.poifs.filesystem.DirectoryEntry;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.controller.admin.archive.vo.ArchiveEditorListReqVO;
import com.rs.module.dam.controller.admin.archive.vo.ArchiveEditorPageReqVO;
import com.rs.module.dam.controller.admin.archive.vo.ArchiveEditorRespVO;
import com.rs.module.dam.controller.admin.archive.vo.ArchiveEditorSaveReqVO;
import com.rs.module.dam.entity.archive.ArchiveEditorDO;
import com.rs.module.dam.service.archive.ArchiveEditorService;
import com.rs.module.dam.util.ObjectUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Api(tags = "卷宗归档 - 卷宗编辑")
@RestController
@RequestMapping("/dam/archive/archiveEditor")
@Validated
@Slf4j
public class ArchiveEditorController {

    @Resource
    private ArchiveEditorService archiveEditorService;

    @PostMapping("/create")
    @ApiOperation(value = "创建卷宗编辑")
    @LogRecordAnnotation(bizModule = "dam:archiveEditor:create", operateType = LogOperateType.CREATE, title = "创建卷宗编辑",
    success = "创建卷宗编辑成功", fail = "创建卷宗编辑失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createArchiveEditor(@Valid @RequestBody ArchiveEditorSaveReqVO createReqVO) {
        return success(archiveEditorService.createArchiveEditor(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新卷宗编辑")
    @LogRecordAnnotation(bizModule = "dam:archiveEditor:update", operateType = LogOperateType.UPDATE, title = "更新卷宗编辑",
    success = "更新卷宗编辑成功", fail = "更新卷宗编辑失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateArchiveEditor(@Valid @RequestBody ArchiveEditorSaveReqVO updateReqVO) {
        archiveEditorService.updateArchiveEditor(updateReqVO);
        return success(true);
    }
    
    @PostMapping("/save")
    @ApiOperation(value = "保存卷宗编辑")
    @LogRecordAnnotation(bizModule = "dam:archiveEditor:create", operateType = LogOperateType.CREATE, title = "保存卷宗编辑",
    success = "保存卷宗编辑成功", fail = "保存卷宗编辑失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<?> saveArchiveEditor(@Valid @RequestBody ArchiveEditorSaveReqVO createReqVO) {
    	if(StringUtil.isNotEmpty(createReqVO.getId())) {
    		archiveEditorService.updateArchiveEditor(createReqVO);
            return success(true);
    	}
    	else {
    		return success(archiveEditorService.createArchiveEditor(createReqVO));
    	}
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除卷宗编辑")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:archiveEditor:delete", operateType = LogOperateType.DELETE, title = "删除卷宗编辑",
    success = "删除卷宗编辑成功", fail = "删除卷宗编辑失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteArchiveEditor(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           archiveEditorService.deleteArchiveEditor(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得卷宗编辑")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:archiveEditor:get", operateType = LogOperateType.QUERY, title = "获取卷宗编辑", bizNo = "{{#id}}", success = "获取卷宗编辑成功", fail = "获取卷宗编辑失败", extraInfo = "{{#id}}")
    public CommonResult<ArchiveEditorRespVO> getArchiveEditor(@RequestParam("id") String id) {
        ArchiveEditorDO archiveEditor = archiveEditorService.getArchiveEditor(id);
        return success(BeanUtils.toBean(archiveEditor, ArchiveEditorRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得卷宗编辑分页")
    @LogRecordAnnotation(bizModule = "dam:archiveEditor:page", operateType = LogOperateType.QUERY, title = "获得卷宗编辑分页",
    success = "获得卷宗编辑分页成功", fail = "获得卷宗编辑分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<ArchiveEditorRespVO>> getArchiveEditorPage(@Valid @RequestBody ArchiveEditorPageReqVO pageReqVO) {
        PageResult<ArchiveEditorDO> pageResult = archiveEditorService.getArchiveEditorPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ArchiveEditorRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得卷宗编辑列表")
    @LogRecordAnnotation(bizModule = "dam:archiveEditor:list", operateType = LogOperateType.QUERY, title = "获得卷宗编辑列表",
    success = "获得卷宗编辑列表成功", fail = "获得卷宗编辑列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<ArchiveEditorRespVO>> getArchiveEditorList(@Valid @RequestBody ArchiveEditorListReqVO listReqVO) {
    List<ArchiveEditorDO> list = archiveEditorService.getArchiveEditorList(listReqVO);
        return success(BeanUtils.toBean(list, ArchiveEditorRespVO.class));
    }

    @PostMapping("/getUserEditor")
    @ApiOperation(value = "获得用户在线编辑文档")
    @ApiImplicitParams({
    	@ApiImplicitParam(name = "jgrybm", value = "监管人员编码"),
		@ApiImplicitParam(name = "jzid", value = "卷宗Id")
    })
    @LogRecordAnnotation(bizModule = "dam:archiveEditor:getUserEditor", operateType = LogOperateType.QUERY, title = "获得用户在线编辑文档",
    success = "获得用户在线编辑文档成功", fail = "获得用户在线编辑文档失败", extraInfo = "{TO_JSON{#jgrybm}}")
    public CommonResult<List<ArchiveEditorDO>> getUserEditor(
    		@RequestParam(value = "jgrybm", required = true) String jgrybm,
    		@RequestParam(value = "jzid", required = false) String jzid) {
    	QueryWrapper<ArchiveEditorDO> wrapper = new QueryWrapper<>();
    	wrapper.eq("jgrybm", jgrybm);
    	wrapper.eq("add_user", SessionUserUtil.getSessionUser().getIdCard());
    	if(StringUtil.isNotEmpty(jzid)) {
    		wrapper.eq("jzid", jzid);
    	}
    	wrapper.orderByDesc("add_time");
    	
    	List<ArchiveEditorDO> list = archiveEditorService.list(wrapper);
    	
    	return CommonResult.success(list);
    }
    
    @GetMapping("/exportToWord")
    @ApiOperation(value = "导出Word文档")
    @ApiImplicitParams({
    	@ApiImplicitParam(name = "title", value = "标题"),
		@ApiImplicitParam(name = "content", value = "内容")
    })
    @LogRecordAnnotation(bizModule = "dam:archiveEditor:exportToWord", operateType = LogOperateType.QUERY, title = "导出Word文档",
    success = "导出Word文档成功", fail = "导出Word文档失败", extraInfo = "{TO_JSON{#title}}")
    public void exportToWord(HttpServletRequest request, HttpServletResponse response,
			@RequestParam(value = "title", required = true) String title, 
			@RequestParam(value = "content", required = true) String content){
    	ByteArrayInputStream bais = null;
    	POIFSFileSystem poifs = new POIFSFileSystem();
    	
    	try {
    		//去掉富文本内容中的ASCII码为160的nbsp空格
			content = content.replaceAll("\\u00A0+", "&nbsp;");
			
			//去掉富文本内容中的 zwsp零宽空格 \u200b   &#8203;
			content = content.replaceAll("\\u200b+", "&#8203;");
			
			//添加HTML头尾标签后，文档用office打开就不会包含html标签了
			content = "<html><body>" + content + "</body></html>";
			
			//创建Word文档
			byte[] bytes = content.getBytes("GBK");
			bais = new ByteArrayInputStream(bytes);			
			DirectoryEntry directory = poifs.getRoot();
			directory.createDocument("WordDocument", bais);
			
			//输出流
			response.setContentType("application/msword");
			String titleStr = ObjectUtil.formatFilePath( new String(title.getBytes(), "iso8859-1"));
			response.addHeader("Content-Disposition", "attachment;filename=" + titleStr + ".doc");
			poifs.writeFilesystem(response.getOutputStream());
    	}
    	catch(Exception e) {
    		log.error("导出Word文档出现异常：{}", e);
    	}
    	finally {
    		try {
    			if(bais != null) {
    				bais.close();
    			}
    			if(poifs != null) {
    				poifs.close();
    			}
    		}
    		catch(Exception e) {
    			log.error("导出Word文档出现异常：{}", e);
    		}
    	}
    }
}
