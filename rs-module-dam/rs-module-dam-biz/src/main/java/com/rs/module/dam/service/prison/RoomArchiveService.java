package com.rs.module.dam.service.prison;

import com.rs.module.dam.controller.admin.prison.vo.RoomArchiveCountVo;
import com.rs.module.dam.controller.admin.prison.vo.RoomArchiveDTO;
import com.rs.module.dam.controller.admin.prison.vo.RoomArchiveDetailsDTO;
import com.rs.module.dam.controller.admin.prison.vo.RoomArchiveDetailsVo;

import java.util.List;

/**
 * 一室一档- Service 接口
 *
 * <AUTHOR>
 */
public interface RoomArchiveService{

    List<RoomArchiveCountVo> getCount(RoomArchiveDTO dto);

    List<RoomArchiveDetailsVo> getList(RoomArchiveDetailsDTO dto, String prisonType);

}
