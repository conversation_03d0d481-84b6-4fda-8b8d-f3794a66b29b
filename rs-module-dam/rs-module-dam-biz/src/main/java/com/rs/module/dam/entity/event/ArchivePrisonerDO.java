package com.rs.module.dam.entity.event;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 一事一档-事件档案监管人员关联 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 一事一档-事件档案监管人员关联新增/修改 Request VO")
@TableName("dam_event_archive_prisoner")
@KeySequence("dam_event_archive_prisoner_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ArchivePrisonerDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;
    /**
     * 事件ID
     */
    @ApiModelProperty("事件ID")
    private String eventId;
    /**
     * 事件编号(规则：S+9位单位编号+8位日期+6位序号)
     */
    @ApiModelProperty("事件编号(规则：S+9位单位编号+8位日期+6位序号)")
    private String eventCode;
    /**
     * 监管人员编码
     */
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

}
