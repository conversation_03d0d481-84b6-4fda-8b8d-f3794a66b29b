package com.rs.module.dam.dao.prison;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.controller.admin.prison.vo.FeatureConfigListReqVO;
import com.rs.module.dam.controller.admin.prison.vo.FeatureConfigPageReqVO;
import com.rs.module.dam.entity.prison.FeatureConfigDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 一所一档-监所档案特色亮点配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface FeatureConfigDao extends IBaseDao<FeatureConfigDO> {


    default PageResult<FeatureConfigDO> selectPage(FeatureConfigPageReqVO reqVO) {
        Page<FeatureConfigDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<FeatureConfigDO> wrapper = new LambdaQueryWrapperX<FeatureConfigDO>()
            .likeIfPresent(FeatureConfigDO::getFeatureTitle, reqVO.getFeatureTitle())
            .betweenIfPresent(FeatureConfigDO::getConstructionTime, reqVO.getConstructionTime())
            .likeIfPresent(FeatureConfigDO::getFeatureContent, reqVO.getFeatureContent());
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(FeatureConfigDO::getAddTime);
        }
        Page<FeatureConfigDO> featureConfigPage = selectPage(page, wrapper);
        return new PageResult<>(featureConfigPage.getRecords(), featureConfigPage.getTotal());
    }
    default List<FeatureConfigDO> selectList(FeatureConfigListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<FeatureConfigDO>()
            .likeIfPresent(FeatureConfigDO::getFeatureTitle, reqVO.getFeatureTitle())
            .betweenIfPresent(FeatureConfigDO::getConstructionTime, reqVO.getConstructionTime())
            .likeIfPresent(FeatureConfigDO::getFeatureContent, reqVO.getFeatureContent())
        .orderByDesc(FeatureConfigDO::getAddTime));
    }

}
