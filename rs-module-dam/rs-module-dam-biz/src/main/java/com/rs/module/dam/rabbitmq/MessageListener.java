package com.rs.module.dam.rabbitmq;

import java.io.IOException;

import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import com.rs.module.dam.config.WebSocketServer;
import com.rs.module.dam.websocket.WebSocketMsg;

import lombok.extern.slf4j.Slf4j;

/**
 * 消息监听器
 * <AUTHOR>
 * @date 2025年4月23日
 */
@Component
@Slf4j
public class MessageListener {

	@RabbitHandler
	@RabbitListener(queues = "${conf.dam.websocket.queue:}")
	public void wsConsumer(String msg, Channel channel, Message message) throws IOException {
		try {
			log.info("【websocket】消息接收:{}", msg);
			WebSocketMsg wsMsg = JSON.parseObject(msg, WebSocketMsg.class);
			WebSocketServer.sendInfoLocal(wsMsg.getUserName(), wsMsg.getMessage());
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
		}
		catch (Exception e) {
			log.error("【websocket】消息推送异常:{}", e.getMessage());
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
		}
	}
}
