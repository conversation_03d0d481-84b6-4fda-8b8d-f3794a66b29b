package com.rs.module.dam.service.prison;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.controller.admin.prison.vo.RecognitionConfigListReqVO;
import com.rs.module.dam.controller.admin.prison.vo.RecognitionConfigPageReqVO;
import com.rs.module.dam.controller.admin.prison.vo.RecognitionConfigSaveReqVO;
import com.rs.module.dam.entity.prison.FeatureConfigDO;
import com.rs.module.dam.entity.prison.RecognitionConfigDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 一所一档-监所档案荣誉表彰配置 Service 接口
 *
 * <AUTHOR>
 */
public interface RecognitionConfigService extends IBaseService<RecognitionConfigDO>{

    /**
     * 创建一所一档-监所档案荣誉表彰配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createRecognitionConfig(@Valid RecognitionConfigSaveReqVO createReqVO);

    /**
     * 更新一所一档-监所档案荣誉表彰配置
     *
     * @param updateReqVO 更新信息
     */
    String updateRecognitionConfig(@Valid RecognitionConfigSaveReqVO updateReqVO);

    /**
     * 删除一所一档-监所档案荣誉表彰配置
     *
     * @param id 编号
     */
    void deleteRecognitionConfig(String id);

    /**
     * 获得一所一档-监所档案荣誉表彰配置
     *
     * @param id 编号
     * @return 一所一档-监所档案荣誉表彰配置
     */
    RecognitionConfigDO getRecognitionConfig(String id);

    /**
     * 获得一所一档-监所档案荣誉表彰配置
     *
     * @param prisonCode 监所编号
     * @return 一所一档-监所档案荣誉表彰配置
     */
    List<RecognitionConfigDO> getByPrisonCode(String prisonCode);

    /**
    * 获得一所一档-监所档案荣誉表彰配置分页
    *
    * @param pageReqVO 分页查询
    * @return 一所一档-监所档案荣誉表彰配置分页
    */
    PageResult<RecognitionConfigDO> getRecognitionConfigPage(RecognitionConfigPageReqVO pageReqVO);

    /**
    * 获得一所一档-监所档案荣誉表彰配置列表
    *
    * @param listReqVO 查询条件
    * @return 一所一档-监所档案荣誉表彰配置列表
    */
    List<RecognitionConfigDO> getRecognitionConfigList(RecognitionConfigListReqVO listReqVO);


}
