package com.rs.module.dam.controller.admin.archive;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.entity.archive.ArchiveJobDO;
import com.rs.module.dam.service.archive.ArchiveJobService;
import com.rs.module.dam.vo.archive.ArchiveJobListReqVO;
import com.rs.module.dam.vo.archive.ArchiveJobPageReqVO;
import com.rs.module.dam.vo.archive.ArchiveJobRespVO;
import com.rs.module.dam.vo.archive.ArchiveJobSaveReqVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

@Api(tags = "卷宗归档 - 卷宗归档任务")
@ApiIgnore
@RestController
@RequestMapping("/dam/archive/archiveJob")
@Validated
public class ArchiveJobController {

    @Resource
    private ArchiveJobService archiveJobService;

    @PostMapping("/create")
    @ApiOperation(value = "创建卷宗归档任务")
    @LogRecordAnnotation(bizModule = "dam:archiveJob:create", operateType = LogOperateType.CREATE, title = "创建卷宗归档任务",
    success = "创建卷宗归档任务成功", fail = "创建卷宗归档任务失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createArchiveJob(@Valid @RequestBody ArchiveJobSaveReqVO createReqVO) {
        return success(archiveJobService.createArchiveJob(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新卷宗归档任务")
    @LogRecordAnnotation(bizModule = "dam:archiveJob:update", operateType = LogOperateType.UPDATE, title = "更新卷宗归档任务",
    success = "更新卷宗归档任务成功", fail = "更新卷宗归档任务失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateArchiveJob(@Valid @RequestBody ArchiveJobSaveReqVO updateReqVO) {
        archiveJobService.updateArchiveJob(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除卷宗归档任务")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:archiveJob:delete", operateType = LogOperateType.DELETE, title = "删除卷宗归档任务",
    success = "删除卷宗归档任务成功", fail = "删除卷宗归档任务失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteArchiveJob(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           archiveJobService.deleteArchiveJob(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得卷宗归档任务")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:archiveJob:get", operateType = LogOperateType.QUERY, title = "获取卷宗归档任务", bizNo = "{{#id}}", success = "获取卷宗归档任务成功", fail = "获取卷宗归档任务失败", extraInfo = "{{#id}}")
    public CommonResult<ArchiveJobRespVO> getArchiveJob(@RequestParam("id") String id) {
        ArchiveJobDO archiveJob = archiveJobService.getArchiveJob(id);
        return success(BeanUtils.toBean(archiveJob, ArchiveJobRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得卷宗归档任务分页")
    @LogRecordAnnotation(bizModule = "dam:archiveJob:page", operateType = LogOperateType.QUERY, title = "获得卷宗归档任务分页",
    success = "获得卷宗归档任务分页成功", fail = "获得卷宗归档任务分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<ArchiveJobRespVO>> getArchiveJobPage(@Valid @RequestBody ArchiveJobPageReqVO pageReqVO) {
        PageResult<ArchiveJobDO> pageResult = archiveJobService.getArchiveJobPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ArchiveJobRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得卷宗归档任务列表")
    @LogRecordAnnotation(bizModule = "dam:archiveJob:list", operateType = LogOperateType.QUERY, title = "获得卷宗归档任务列表",
    success = "获得卷宗归档任务列表成功", fail = "获得卷宗归档任务列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<ArchiveJobRespVO>> getArchiveJobList(@Valid @RequestBody ArchiveJobListReqVO listReqVO) {
    List<ArchiveJobDO> list = archiveJobService.getArchiveJobList(listReqVO);
        return success(BeanUtils.toBean(list, ArchiveJobRespVO.class));
    }

}
