package com.rs.module.dam.controller.admin.borrow.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel(description = "管理后台 - 借阅审批分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BorrowApprovePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("借阅Id")
    private String borrowId;

    @ApiModelProperty("审批状态(0:发起提交,1:待审批,2:审批通过,3:部分审批通过,4:审批不通过)")
    private String approveStatus;

    @ApiModelProperty("审批顺序")
    private String orderId;

    @ApiModelProperty("审批角色代码")
    private String approveRoleCode;

    @ApiModelProperty("审批角色名称")
    private String approveRoleName;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
