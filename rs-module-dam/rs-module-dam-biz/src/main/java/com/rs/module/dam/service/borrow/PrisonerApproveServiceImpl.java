package com.rs.module.dam.service.borrow;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.dam.controller.admin.borrow.vo.*;
import com.rs.module.dam.entity.borrow.PrisonerApproveDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.dam.dao.borrow.PrisonerApproveDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 借阅人员档案审批 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrisonerApproveServiceImpl extends BaseServiceImpl<PrisonerApproveDao, PrisonerApproveDO> implements PrisonerApproveService {

    @Resource
    private PrisonerApproveDao prisonerApproveDao;

    @Override
    public String createPrisonerApprove(PrisonerApproveSaveReqVO createReqVO) {
        // 插入
        PrisonerApproveDO prisonerApprove = BeanUtils.toBean(createReqVO, PrisonerApproveDO.class);
        prisonerApproveDao.insert(prisonerApprove);
        // 返回
        return prisonerApprove.getId();
    }

    @Override
    public void updatePrisonerApprove(PrisonerApproveSaveReqVO updateReqVO) {
        // 校验存在
        validatePrisonerApproveExists(updateReqVO.getId());
        // 更新
        PrisonerApproveDO updateObj = BeanUtils.toBean(updateReqVO, PrisonerApproveDO.class);
        prisonerApproveDao.updateById(updateObj);
    }

    @Override
    public void deletePrisonerApprove(String id) {
        // 校验存在
        validatePrisonerApproveExists(id);
        // 删除
        prisonerApproveDao.deleteById(id);
    }

    private void validatePrisonerApproveExists(String id) {
        if (prisonerApproveDao.selectById(id) == null) {
            throw new ServerException("借阅人员档案审批数据不存在");
        }
    }

    @Override
    public PrisonerApproveDO getPrisonerApprove(String id) {
        return prisonerApproveDao.selectById(id);
    }

    @Override
    public PageResult<PrisonerApproveDO> getPrisonerApprovePage(PrisonerApprovePageReqVO pageReqVO) {
        return prisonerApproveDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrisonerApproveDO> getPrisonerApproveList(PrisonerApproveListReqVO listReqVO) {
        return prisonerApproveDao.selectList(listReqVO);
    }


}
