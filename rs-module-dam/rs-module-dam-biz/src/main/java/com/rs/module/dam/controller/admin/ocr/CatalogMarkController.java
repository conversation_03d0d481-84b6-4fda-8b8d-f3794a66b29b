package com.rs.module.dam.controller.admin.ocr;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.entity.ocr.CatalogMarkDO;
import com.rs.module.dam.service.ocr.CatalogMarkService;
import com.rs.module.dam.vo.ocr.CatalogMarkListReqVO;
import com.rs.module.dam.vo.ocr.CatalogMarkPageReqVO;
import com.rs.module.dam.vo.ocr.CatalogMarkRespVO;
import com.rs.module.dam.vo.ocr.CatalogMarkSaveReqVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

@Api(tags = "ocr处理 - ocr相似度归目标识")
@ApiIgnore
@RestController
@RequestMapping("/dam/ocr/catalogMark")
@Validated
public class CatalogMarkController {

    @Resource
    private CatalogMarkService catalogMarkService;

    @PostMapping("/create")
    @ApiOperation(value = "创建ocr相似度归目标识")
    @LogRecordAnnotation(bizModule = "dam:catalogMark:create", operateType = LogOperateType.CREATE, title = "创建ocr相似度归目标识",
    success = "创建ocr相似度归目标识成功", fail = "创建ocr相似度归目标识失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createCatalogMark(@Valid @RequestBody CatalogMarkSaveReqVO createReqVO) {
        return success(catalogMarkService.createCatalogMark(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新ocr相似度归目标识")
    @LogRecordAnnotation(bizModule = "dam:catalogMark:update", operateType = LogOperateType.UPDATE, title = "更新ocr相似度归目标识",
    success = "更新ocr相似度归目标识成功", fail = "更新ocr相似度归目标识失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateCatalogMark(@Valid @RequestBody CatalogMarkSaveReqVO updateReqVO) {
        catalogMarkService.updateCatalogMark(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除ocr相似度归目标识")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:catalogMark:delete", operateType = LogOperateType.DELETE, title = "删除ocr相似度归目标识",
    success = "删除ocr相似度归目标识成功", fail = "删除ocr相似度归目标识失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteCatalogMark(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           catalogMarkService.deleteCatalogMark(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得ocr相似度归目标识")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:catalogMark:get", operateType = LogOperateType.QUERY, title = "获取ocr相似度归目标识", bizNo = "{{#id}}", success = "获取ocr相似度归目标识成功", fail = "获取ocr相似度归目标识失败", extraInfo = "{{#id}}")
    public CommonResult<CatalogMarkRespVO> getCatalogMark(@RequestParam("id") String id) {
        CatalogMarkDO catalogMark = catalogMarkService.getCatalogMark(id);
        return success(BeanUtils.toBean(catalogMark, CatalogMarkRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得ocr相似度归目标识分页")
    @LogRecordAnnotation(bizModule = "dam:catalogMark:page", operateType = LogOperateType.QUERY, title = "获得ocr相似度归目标识分页",
    success = "获得ocr相似度归目标识分页成功", fail = "获得ocr相似度归目标识分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<CatalogMarkRespVO>> getCatalogMarkPage(@Valid @RequestBody CatalogMarkPageReqVO pageReqVO) {
        PageResult<CatalogMarkDO> pageResult = catalogMarkService.getCatalogMarkPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CatalogMarkRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得ocr相似度归目标识列表")
    @LogRecordAnnotation(bizModule = "dam:catalogMark:list", operateType = LogOperateType.QUERY, title = "获得ocr相似度归目标识列表",
    success = "获得ocr相似度归目标识列表成功", fail = "获得ocr相似度归目标识列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<CatalogMarkRespVO>> getCatalogMarkList(@Valid @RequestBody CatalogMarkListReqVO listReqVO) {
    List<CatalogMarkDO> list = catalogMarkService.getCatalogMarkList(listReqVO);
        return success(BeanUtils.toBean(list, CatalogMarkRespVO.class));
    }

}
