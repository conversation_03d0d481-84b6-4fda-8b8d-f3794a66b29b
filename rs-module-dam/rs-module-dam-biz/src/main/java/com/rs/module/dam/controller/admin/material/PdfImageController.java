package com.rs.module.dam.controller.admin.material;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.entity.material.PdfImageDO;
import com.rs.module.dam.service.material.PdfImageService;
import com.rs.module.dam.vo.material.PdfImageListReqVO;
import com.rs.module.dam.vo.material.PdfImagePageReqVO;
import com.rs.module.dam.vo.material.PdfImageRespVO;
import com.rs.module.dam.vo.material.PdfImageSaveReqVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

@Api(tags = "材料处理 - pdf转换图片")
@ApiIgnore
@RestController
@RequestMapping("/dam/material/pdfImage")
@Validated
public class PdfImageController {

    @Resource
    private PdfImageService pdfImageService;

    @PostMapping("/create")
    @ApiOperation(value = "创建pdf转换图片")
    @LogRecordAnnotation(bizModule = "dam:pdfImage:create", operateType = LogOperateType.CREATE, title = "创建pdf转换图片",
    success = "创建pdf转换图片成功", fail = "创建pdf转换图片失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPdfImage(@Valid @RequestBody PdfImageSaveReqVO createReqVO) {
        return success(pdfImageService.createPdfImage(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新pdf转换图片")
    @LogRecordAnnotation(bizModule = "dam:pdfImage:update", operateType = LogOperateType.UPDATE, title = "更新pdf转换图片",
    success = "更新pdf转换图片成功", fail = "更新pdf转换图片失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePdfImage(@Valid @RequestBody PdfImageSaveReqVO updateReqVO) {
        pdfImageService.updatePdfImage(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除pdf转换图片")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:pdfImage:delete", operateType = LogOperateType.DELETE, title = "删除pdf转换图片",
    success = "删除pdf转换图片成功", fail = "删除pdf转换图片失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePdfImage(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           pdfImageService.deletePdfImage(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得pdf转换图片")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:pdfImage:get", operateType = LogOperateType.QUERY, title = "获取pdf转换图片", bizNo = "{{#id}}", success = "获取pdf转换图片成功", fail = "获取pdf转换图片失败", extraInfo = "{{#id}}")
    public CommonResult<PdfImageRespVO> getPdfImage(@RequestParam("id") String id) {
        PdfImageDO pdfImage = pdfImageService.getPdfImage(id);
        return success(BeanUtils.toBean(pdfImage, PdfImageRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得pdf转换图片分页")
    @LogRecordAnnotation(bizModule = "dam:pdfImage:page", operateType = LogOperateType.QUERY, title = "获得pdf转换图片分页",
    success = "获得pdf转换图片分页成功", fail = "获得pdf转换图片分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<PdfImageRespVO>> getPdfImagePage(@Valid @RequestBody PdfImagePageReqVO pageReqVO) {
        PageResult<PdfImageDO> pageResult = pdfImageService.getPdfImagePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PdfImageRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得pdf转换图片列表")
    @LogRecordAnnotation(bizModule = "dam:pdfImage:list", operateType = LogOperateType.QUERY, title = "获得pdf转换图片列表",
    success = "获得pdf转换图片列表成功", fail = "获得pdf转换图片列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<PdfImageRespVO>> getPdfImageList(@Valid @RequestBody PdfImageListReqVO listReqVO) {
    List<PdfImageDO> list = pdfImageService.getPdfImageList(listReqVO);
        return success(BeanUtils.toBean(list, PdfImageRespVO.class));
    }

}
