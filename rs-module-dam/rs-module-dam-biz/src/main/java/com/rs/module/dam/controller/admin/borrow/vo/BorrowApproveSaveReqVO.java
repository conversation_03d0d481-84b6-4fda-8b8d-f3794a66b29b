package com.rs.module.dam.controller.admin.borrow.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 借阅审批新增/修改 Request VO")
@Data
public class BorrowApproveSaveReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("借阅Id")
    private String borrowId;

    @ApiModelProperty("审批状态(0:发起提交,1:待审批,2:审批通过,3:部分审批通过,4:审批不通过)")
    private String approveStatus;

    @ApiModelProperty("审批顺序")
    private String orderId;

    @ApiModelProperty("审批角色代码")
    private String approveRoleCode;

    @ApiModelProperty("审批角色名称")
    private String approveRoleName;

}
