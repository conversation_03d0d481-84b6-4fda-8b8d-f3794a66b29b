package com.rs.module.dam.service.event;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.controller.admin.event.vo.InvestRecordSaveReqVO;
import com.rs.module.dam.dao.event.InvestRecordDao;
import com.rs.module.dam.entity.event.InvestRecordDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


/**
 * 一事一档-事件档案调查记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InvestRecordServiceImpl extends BaseServiceImpl<InvestRecordDao, InvestRecordDO> implements InvestRecordService {

    @Resource
    private InvestRecordDao investRecordDao;

    @Override
    public String createInvestRecord(InvestRecordSaveReqVO createReqVO) {
        // 插入
        InvestRecordDO investRecord = BeanUtils.toBean(createReqVO, InvestRecordDO.class);
        saveOrUpdate(investRecord);
        // 返回
        return investRecord.getId();
    }

    @Override
    public void updateInvestRecord(InvestRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateInvestRecordExists(updateReqVO.getId());
        // 更新
        InvestRecordDO updateObj = BeanUtils.toBean(updateReqVO, InvestRecordDO.class);
        investRecordDao.updateById(updateObj);
    }

    @Override
    public void deleteInvestRecord(String id) {
        // 校验存在
        validateInvestRecordExists(id);
        // 删除
        investRecordDao.deleteById(id);
    }

    private void validateInvestRecordExists(String id) {
        if (investRecordDao.selectById(id) == null) {
            throw new ServerException("一事一档-事件档案调查记录数据不存在");
        }
    }

    @Override
    public InvestRecordDO getInvestRecord(String id) {
        return investRecordDao.selectById(id);
    }

}
