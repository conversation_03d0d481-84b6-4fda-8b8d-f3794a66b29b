package com.rs.module.dam.controller.admin.event.vo;
import com.rs.framework.annotation.SaveFile;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.dromara.x.file.storage.core.FileInfo;

import javax.validation.constraints.*;
import java.util.List;

@ApiModel(description = "管理后台 - 一事一档-事件档案调查记录新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class InvestRecordSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("事件ID")
    private String eventId;

    @ApiModelProperty("事件编号(规则：S+9位单位编号+8位日期+6位序号)")
    private String eventCode;

    @ApiModelProperty("调查详情")
    @NotEmpty(message = "调查详情不能为空")
    private String investDetails;

    @ApiModelProperty("上传附件存储路径")
    private String attachmentPath;

    @SaveFile
    @ApiModelProperty("附件")
    private List<FileInfo> fileList;

}
