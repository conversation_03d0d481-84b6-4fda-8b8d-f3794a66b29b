package com.rs.module.dam.controller.admin.event.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.dam.entity.event.ArchivePersonnelDO;
import com.rs.module.dam.entity.event.ArchivePrisonerDO;
import com.rs.module.dam.entity.event.InvestRecordDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 一事一档-事件档案新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ArchiveSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("事件编号(规则：S+9位单位编号+8位日期+6位序号)")
    private String eventCode;

    @ApiModelProperty("事件类型（字典：ZD_YSYD_SJLX）")
    @NotEmpty(message = "事件类型（字典：ZD_YSYD_SJLX）不能为空")
    private String eventType;

    @ApiModelProperty("事件发生的具体时间")
    @NotNull(message = "事件发生的具体时间不能为空")
    private Date occurrenceTime;

    @ApiModelProperty("事件发生的地点")
    @NotEmpty(message = "事件发生的地点不能为空")
    private String eventLocation;

    @ApiModelProperty("对事件的详细描述")
    private String eventDescription;

    @ApiModelProperty("状态（字典：ZD_YSYD_SJZT）")
    private String eventStatus;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("流程任务ID")
    private String taskId;

    @ApiModelProperty("一事一档-事件档案监管人员关联列表")
    private List<ArchivePrisonerDO> archivePrisoners;

    @ApiModelProperty("一事一档-事件档案工作人员关联列表")
    private List<ArchivePersonnelDO> archivePersonnels;

    @ApiModelProperty("一事一档-事件档案调查记录表")
    private InvestRecordDO investRecord;;

}
