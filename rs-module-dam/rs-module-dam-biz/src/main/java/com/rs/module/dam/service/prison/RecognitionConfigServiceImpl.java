package com.rs.module.dam.service.prison;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.controller.admin.prison.vo.RecognitionConfigListReqVO;
import com.rs.module.dam.controller.admin.prison.vo.RecognitionConfigPageReqVO;
import com.rs.module.dam.controller.admin.prison.vo.RecognitionConfigSaveReqVO;
import com.rs.module.dam.dao.prison.RecognitionConfigDao;
import com.rs.module.dam.entity.prison.FeatureConfigDO;
import com.rs.module.dam.entity.prison.RecognitionConfigDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;


/**
 * 一所一档-监所档案荣誉表彰配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RecognitionConfigServiceImpl extends BaseServiceImpl<RecognitionConfigDao, RecognitionConfigDO> implements RecognitionConfigService {

    @Resource
    private RecognitionConfigDao recognitionConfigDao;

    @Override
    public String createRecognitionConfig(RecognitionConfigSaveReqVO createReqVO) {
        // 插入
        RecognitionConfigDO recognitionConfig = BeanUtils.toBean(createReqVO, RecognitionConfigDO.class);
        recognitionConfigDao.insert(recognitionConfig);
        // 返回
        return recognitionConfig.getId();
    }

    @Override
    public String updateRecognitionConfig(RecognitionConfigSaveReqVO updateReqVO) {
        // 校验存在
        validateRecognitionConfigExists(updateReqVO.getId());
        // 更新
        RecognitionConfigDO updateObj = BeanUtils.toBean(updateReqVO, RecognitionConfigDO.class);
        recognitionConfigDao.updateById(updateObj);
        return updateObj.getId();
    }

    @Override
    public void deleteRecognitionConfig(String id) {
        // 校验存在
        validateRecognitionConfigExists(id);
        // 删除
        recognitionConfigDao.deleteById(id);
    }

    private void validateRecognitionConfigExists(String id) {
        if (recognitionConfigDao.selectById(id) == null) {
            throw new ServerException("一所一档-监所档案荣誉表彰配置数据不存在");
        }
    }

    @Override
    public RecognitionConfigDO getRecognitionConfig(String id) {
        return recognitionConfigDao.selectById(id);
    }

    @Override
    public List<RecognitionConfigDO> getByPrisonCode(String prisonCode) {
        if (StringUtil.isNullBlank(prisonCode)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<RecognitionConfigDO>().eq(RecognitionConfigDO::getOrgCode, prisonCode));
    }

    @Override
    public PageResult<RecognitionConfigDO> getRecognitionConfigPage(RecognitionConfigPageReqVO pageReqVO) {
        return recognitionConfigDao.selectPage(pageReqVO);
    }

    @Override
    public List<RecognitionConfigDO> getRecognitionConfigList(RecognitionConfigListReqVO listReqVO) {
        return recognitionConfigDao.selectList(listReqVO);
    }


}
