package com.rs.module.dam.dao.event;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.event.InvestRecordDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 一事一档-事件档案调查记录 Dao
*
* <AUTHOR>
*/
@Mapper
public interface InvestRecordDao extends IBaseDao<InvestRecordDO> {


    default List<InvestRecordDO> selectListByEventId(String eventId) {
        return selectList(new LambdaQueryWrapperX<InvestRecordDO>().eq(InvestRecordDO::getEventId, eventId));
    }

    default int deleteByEventId(String eventId) {
        return delete(new LambdaQueryWrapperX<InvestRecordDO>().eq(InvestRecordDO::getEventId, eventId));
    }


}
