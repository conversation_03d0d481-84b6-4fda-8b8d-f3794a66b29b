package com.rs.module.dam.controller.admin.sys.vo;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel(description = "管理后台 - 卷宗目录模板分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CatalogTemplatePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("目录数据")
    private String data;

    @ApiModelProperty("类型（1：标准  0：自定义模板）")
    private String type;

    @ApiModelProperty("标准模板ID")
    private String templateId;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
