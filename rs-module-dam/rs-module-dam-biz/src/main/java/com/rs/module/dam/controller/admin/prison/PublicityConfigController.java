package com.rs.module.dam.controller.admin.prison;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.controller.admin.prison.vo.PublicityConfigListReqVO;
import com.rs.module.dam.controller.admin.prison.vo.PublicityConfigPageReqVO;
import com.rs.module.dam.controller.admin.prison.vo.PublicityConfigRespVO;
import com.rs.module.dam.controller.admin.prison.vo.PublicityConfigSaveReqVO;
import com.rs.module.dam.entity.prison.PublicityConfigDO;
import com.rs.module.dam.service.prison.PublicityConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "一所一档-监所档案宣传报道配置")
@RestController
@RequestMapping("/dam/prison/publicityConfig")
@Validated
public class PublicityConfigController {

    @Resource
    private PublicityConfigService publicityConfigService;

    @PostMapping("/create")
    @ApiOperation(value = "创建一所一档-监所档案宣传报道配置")
    @LogRecordAnnotation(bizModule = "dam:publicityConfig:create", operateType = LogOperateType.CREATE, title = "创建一所一档-监所档案宣传报道配置",
    success = "创建一所一档-监所档案宣传报道配置成功", fail = "创建一所一档-监所档案宣传报道配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPublicityConfig(@Valid @RequestBody PublicityConfigSaveReqVO createReqVO) {
        return success(publicityConfigService.createPublicityConfig(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新一所一档-监所档案宣传报道配置")
    @LogRecordAnnotation(bizModule = "dam:publicityConfig:update", operateType = LogOperateType.UPDATE, title = "更新一所一档-监所档案宣传报道配置",
    success = "更新一所一档-监所档案宣传报道配置成功", fail = "更新一所一档-监所档案宣传报道配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<String> updatePublicityConfig(@Valid @RequestBody PublicityConfigSaveReqVO updateReqVO) {
        return success(publicityConfigService.updatePublicityConfig(updateReqVO));
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除一所一档-监所档案宣传报道配置")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:publicityConfig:delete", operateType = LogOperateType.DELETE, title = "删除一所一档-监所档案宣传报道配置",
    success = "删除一所一档-监所档案宣传报道配置成功", fail = "删除一所一档-监所档案宣传报道配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePublicityConfig(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           publicityConfigService.deletePublicityConfig(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得一所一档-监所档案宣传报道配置")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<PublicityConfigRespVO> getPublicityConfig(@RequestParam("id") String id) {
        PublicityConfigDO publicityConfig = publicityConfigService.getPublicityConfig(id);
        return success(BeanUtils.toBean(publicityConfig, PublicityConfigRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得一所一档-监所档案宣传报道配置分页")
    public CommonResult<PageResult<PublicityConfigRespVO>> getPublicityConfigPage(@Valid @RequestBody PublicityConfigPageReqVO pageReqVO) {
        PageResult<PublicityConfigDO> pageResult = publicityConfigService.getPublicityConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PublicityConfigRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得一所一档-监所档案宣传报道配置列表")
    public CommonResult<List<PublicityConfigRespVO>> getPublicityConfigList(@Valid @RequestBody PublicityConfigListReqVO listReqVO) {
        List<PublicityConfigDO> list = publicityConfigService.getPublicityConfigList(listReqVO);
        return success(BeanUtils.toBean(list, PublicityConfigRespVO.class));
    }
}
