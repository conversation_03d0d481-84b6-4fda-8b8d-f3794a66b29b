package com.rs.module.dam.controller.admin.borrow;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.controller.admin.borrow.vo.PrisonerApproveListReqVO;
import com.rs.module.dam.controller.admin.borrow.vo.PrisonerApprovePageReqVO;
import com.rs.module.dam.controller.admin.borrow.vo.PrisonerApproveRespVO;
import com.rs.module.dam.controller.admin.borrow.vo.PrisonerApproveSaveReqVO;
import com.rs.module.dam.entity.borrow.PrisonerApproveDO;
import com.rs.module.dam.service.borrow.PrisonerApproveService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

@Api(tags = "卷宗借阅 - 借阅人员档案审批")
@RestController
@RequestMapping("/dam/borrow/prisonerApprove")
@Validated
public class PrisonerApproveController {

    @Resource
    private PrisonerApproveService prisonerApproveService;

    @PostMapping("/create")
    @ApiOperation(value = "创建借阅人员档案审批", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:prisonerApprove:create", operateType = LogOperateType.CREATE, title = "创建借阅人员档案审批",
    success = "创建借阅人员档案审批成功", fail = "创建借阅人员档案审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPrisonerApprove(@Valid @RequestBody PrisonerApproveSaveReqVO createReqVO) {
        return success(prisonerApproveService.createPrisonerApprove(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新借阅人员档案审批", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:prisonerApprove:update", operateType = LogOperateType.UPDATE, title = "更新借阅人员档案审批",
    success = "更新借阅人员档案审批成功", fail = "更新借阅人员档案审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePrisonerApprove(@Valid @RequestBody PrisonerApproveSaveReqVO updateReqVO) {
        prisonerApproveService.updatePrisonerApprove(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除借阅人员档案审批", hidden = true)
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:prisonerApprove:delete", operateType = LogOperateType.DELETE, title = "删除借阅人员档案审批",
    success = "删除借阅人员档案审批成功", fail = "删除借阅人员档案审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePrisonerApprove(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           prisonerApproveService.deletePrisonerApprove(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得借阅人员档案审批")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:prisonerApprove:get", operateType = LogOperateType.QUERY, title = "获取借阅人员档案审批",
            bizNo = "{{#id}}", success = "获取借阅人员档案审批成功", fail = "获取借阅人员档案审批失败", extraInfo = "{{#id}}")
    public CommonResult<PrisonerApproveRespVO> getPrisonerApprove(@RequestParam("id") String id) {
        PrisonerApproveDO prisonerApprove = prisonerApproveService.getPrisonerApprove(id);
        return success(BeanUtils.toBean(prisonerApprove, PrisonerApproveRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得借阅人员档案审批分页", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:prisonerApprove:page", operateType = LogOperateType.QUERY, title = "获得借阅人员档案审批分页",
    success = "获得借阅人员档案审批分页成功", fail = "获得借阅人员档案审批分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<PrisonerApproveRespVO>> getPrisonerApprovePage(@Valid @RequestBody PrisonerApprovePageReqVO pageReqVO) {
        PageResult<PrisonerApproveDO> pageResult = prisonerApproveService.getPrisonerApprovePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PrisonerApproveRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得借阅人员档案审批列表", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:prisonerApprove:list", operateType = LogOperateType.QUERY, title = "获得借阅人员档案审批列表",
    success = "获得借阅人员档案审批列表成功", fail = "获得借阅人员档案审批列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<PrisonerApproveRespVO>> getPrisonerApproveList(@Valid @RequestBody PrisonerApproveListReqVO listReqVO) {
    List<PrisonerApproveDO> list = prisonerApproveService.getPrisonerApproveList(listReqVO);
        return success(BeanUtils.toBean(list, PrisonerApproveRespVO.class));
    }

}
