package com.rs.module.dam.controller.admin.sys.vo;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel(description = "管理后台 - 目录文书映射分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CatalogMappingPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("目录模板Id")
    private String templateId;

    @ApiModelProperty("模板目录Id")
    private String catalogId;

    @ApiModelProperty("应用Id")
    private String appId;

    @ApiModelProperty("表单Id")
    private String formId;

    @ApiModelProperty("表单名称")
    private String formName;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
