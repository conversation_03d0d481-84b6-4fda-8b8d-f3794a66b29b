package com.rs.module.dam.service.sys;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.dam.controller.admin.sys.vo.*;
import com.rs.module.dam.entity.sys.CatalogMappingDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.dam.dao.sys.CatalogMappingDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 目录文书映射 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CatalogMappingServiceImpl extends BaseServiceImpl<CatalogMappingDao, CatalogMappingDO> implements CatalogMappingService {

    @Resource
    private CatalogMappingDao catalogMappingDao;

    @Override
    public String createCatalogMapping(CatalogMappingSaveReqVO createReqVO) {
        // 插入
        CatalogMappingDO catalogMapping = BeanUtils.toBean(createReqVO, CatalogMappingDO.class);
        saveOrUpdate(catalogMapping);
        // 返回
        return catalogMapping.getId();
    }

    @Override
    public void createBatch(List<CatalogMappingDO> list) {
        if (CollUtil.isEmpty(list)){
            return;
        }
        String catalogId = list.get(0).getCatalogId();
        remove(new LambdaQueryWrapper<CatalogMappingDO>().eq(CatalogMappingDO::getCatalogId, catalogId));
        // 插入
        saveOrUpdateBatch(list);
    }

    @Override
    public void updateCatalogMapping(CatalogMappingSaveReqVO updateReqVO) {
        // 校验存在
        validateCatalogMappingExists(updateReqVO.getId());
        // 更新
        CatalogMappingDO updateObj = BeanUtils.toBean(updateReqVO, CatalogMappingDO.class);
        catalogMappingDao.updateById(updateObj);
    }

    @Override
    public void deleteCatalogMapping(String id) {
        // 校验存在
        validateCatalogMappingExists(id);
        // 删除
        catalogMappingDao.deleteById(id);
    }

    private void validateCatalogMappingExists(String id) {
        if (catalogMappingDao.selectById(id) == null) {
            throw new ServerException("目录文书映射数据不存在");
        }
    }

    @Override
    public CatalogMappingDO getCatalogMapping(String id) {
        return catalogMappingDao.selectById(id);
    }

    @Override
    public PageResult<CatalogMappingDO> getCatalogMappingPage(CatalogMappingPageReqVO pageReqVO) {
        return catalogMappingDao.selectPage(pageReqVO);
    }

    @Override
    public List<CatalogMappingDO> getCatalogMappingList(CatalogMappingListReqVO listReqVO) {
        return catalogMappingDao.selectList(listReqVO);
    }


}
