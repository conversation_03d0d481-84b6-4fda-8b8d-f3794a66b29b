package com.rs.module.dam.entity.event;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

/**
 * 一事一档-事件档案工作人员关联 DO
 *
 * <AUTHOR>
 */
@TableName("dam_event_archive_personnel")
@KeySequence("dam_event_archive_personnel_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ArchivePersonnelDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 事件ID
     */
    private String eventId;
    /**
     * 事件编号(规则：S+9位单位编号+8位日期+6位序号)
     */
    private String eventCode;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 用户身份证号
     */
    private String userIdCard;
    /**
     * 用户名称
     */
    private String userName;

}
