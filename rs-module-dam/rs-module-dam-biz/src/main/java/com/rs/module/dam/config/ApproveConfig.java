package com.rs.module.dam.config;

import com.rs.module.dam.cons.ApproveFlowNode;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 需求收集反馈系统配置类
 * <AUTHOR>
 * @date 2025年4月8日
 */
@Data
@Component
@ConfigurationProperties(prefix = "conf.dam")
public class ApproveConfig {
	
	//启用多级确认
	private boolean multiConfirm;

	//内部借阅审批流程
	private List<ApproveFlowNode> interiorApproveFlow;

	//外部借阅审批流程
	private List<ApproveFlowNode> externalApproveFlow;
	
	//直接确认角色
	private String[] confirmAdminRoles;
}
