package com.rs.module.dam.controller.admin.material;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.entity.material.MaterialMarkerHistoryDO;
import com.rs.module.dam.service.material.MaterialMarkerHistoryService;
import com.rs.module.dam.vo.material.MaterialMarkerHistoryListReqVO;
import com.rs.module.dam.vo.material.MaterialMarkerHistoryPageReqVO;
import com.rs.module.dam.vo.material.MaterialMarkerHistoryRespVO;
import com.rs.module.dam.vo.material.MaterialMarkerHistorySaveReqVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

@Api(tags = "材料处理 - 卷宗材料标注历史")
@ApiIgnore
@RestController
@RequestMapping("/dam/material/materialMarkerHistory")
@Validated
public class MaterialMarkerHistoryController {

    @Resource
    private MaterialMarkerHistoryService materialMarkerHistoryService;

    @PostMapping("/create")
    @ApiOperation(value = "创建卷宗材料标注历史")
    @LogRecordAnnotation(bizModule = "dam:materialMarkerHistory:create", operateType = LogOperateType.CREATE, title = "创建卷宗材料标注历史",
    success = "创建卷宗材料标注历史成功", fail = "创建卷宗材料标注历史失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createMaterialMarkerHistory(@Valid @RequestBody MaterialMarkerHistorySaveReqVO createReqVO) {
        return success(materialMarkerHistoryService.createMaterialMarkerHistory(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新卷宗材料标注历史")
    @LogRecordAnnotation(bizModule = "dam:materialMarkerHistory:update", operateType = LogOperateType.UPDATE, title = "更新卷宗材料标注历史",
    success = "更新卷宗材料标注历史成功", fail = "更新卷宗材料标注历史失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateMaterialMarkerHistory(@Valid @RequestBody MaterialMarkerHistorySaveReqVO updateReqVO) {
        materialMarkerHistoryService.updateMaterialMarkerHistory(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除卷宗材料标注历史")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:materialMarkerHistory:delete", operateType = LogOperateType.DELETE, title = "删除卷宗材料标注历史",
    success = "删除卷宗材料标注历史成功", fail = "删除卷宗材料标注历史失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteMaterialMarkerHistory(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           materialMarkerHistoryService.deleteMaterialMarkerHistory(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得卷宗材料标注历史")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<MaterialMarkerHistoryRespVO> getMaterialMarkerHistory(@RequestParam("id") String id) {
        MaterialMarkerHistoryDO materialMarkerHistory = materialMarkerHistoryService.getMaterialMarkerHistory(id);
        return success(BeanUtils.toBean(materialMarkerHistory, MaterialMarkerHistoryRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得卷宗材料标注历史分页")
    public CommonResult<PageResult<MaterialMarkerHistoryRespVO>> getMaterialMarkerHistoryPage(@Valid @RequestBody MaterialMarkerHistoryPageReqVO pageReqVO) {
        PageResult<MaterialMarkerHistoryDO> pageResult = materialMarkerHistoryService.getMaterialMarkerHistoryPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MaterialMarkerHistoryRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得卷宗材料标注历史列表")
    public CommonResult<List<MaterialMarkerHistoryRespVO>> getMaterialMarkerHistoryList(@Valid @RequestBody MaterialMarkerHistoryListReqVO listReqVO) {
        List<MaterialMarkerHistoryDO> list = materialMarkerHistoryService.getMaterialMarkerHistoryList(listReqVO);
        return success(BeanUtils.toBean(list, MaterialMarkerHistoryRespVO.class));
    }
}
