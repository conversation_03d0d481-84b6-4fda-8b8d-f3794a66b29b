package com.rs.module.dam.controller.admin.prison.vo;

import com.rs.framework.annotation.GetFile;
import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.dromara.x.file.storage.core.FileInfo;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 一所一档-监所档案特色亮点配置列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FeatureConfigListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;

    @ApiModelProperty("特色亮点标题")
    private String featureTitle;

    @ApiModelProperty("建设时间")
    private Date[] constructionTime;

    @ApiModelProperty("特色亮点内容")
    private String featureContent;

    @GetFile(value = "id")
    public List<FileInfo> fileList;

}
