package com.rs.module.dam.controller.admin.sys.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 卷宗目录模板列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CatalogTemplateListReqVO extends BaseVO {
    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("目录数据")
    private String data;

    @ApiModelProperty("类型（1：标准  0：自定义模板）")
    private String type;

    @ApiModelProperty("标准模板ID")
    private String templateId;

}
