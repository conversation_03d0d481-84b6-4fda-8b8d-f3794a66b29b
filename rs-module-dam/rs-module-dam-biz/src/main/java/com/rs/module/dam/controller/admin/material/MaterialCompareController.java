package com.rs.module.dam.controller.admin.material;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.rs.framework.common.pojo.CommonResult.success;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.entity.material.MaterialCompareDO;
import com.rs.module.dam.service.material.MaterialCompareService;
import com.rs.module.dam.vo.material.MaterialCompareListReqVO;
import com.rs.module.dam.vo.material.MaterialComparePageReqVO;
import com.rs.module.dam.vo.material.MaterialCompareRespVO;
import com.rs.module.dam.vo.material.MaterialCompareSaveReqVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

@Api(tags = "材料处理 - 卷宗材料比对")
@RestController
@RequestMapping("/dam/material/compare")
@Validated
public class MaterialCompareController {

	@Resource
    private MaterialCompareService materialCompareService;

    @PostMapping("/create")
    @ApiOperation(value = "创建卷宗材料比对")
    public CommonResult<String> createMaterialCompare(@Valid @RequestBody MaterialCompareSaveReqVO createReqVO) {
        return success(materialCompareService.createMaterialCompare(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新卷宗材料比对")
    public CommonResult<Boolean> updateMaterialCompare(@Valid @RequestBody MaterialCompareSaveReqVO updateReqVO) {
        materialCompareService.updateMaterialCompare(updateReqVO);
        return success(true);
    }
    
    @PostMapping("/save")
    @ApiOperation(value = "保存卷宗材料比对")
    public CommonResult<?> saveMaterialCompare(@Valid @RequestBody MaterialCompareSaveReqVO createReqVO) {
    	if(StringUtil.isEmpty(createReqVO.getId())) {
    		return success(materialCompareService.createMaterialCompare(createReqVO));
    	}
    	else {
    		materialCompareService.updateMaterialCompare(createReqVO);
    		return success(true);
    	}
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除卷宗材料比对")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteMaterialCompare(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           materialCompareService.deleteMaterialCompare(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得卷宗材料比对")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<MaterialCompareRespVO> getMaterialCompare(@RequestParam("id") String id) {
        MaterialCompareDO materialCompare = materialCompareService.getMaterialCompare(id);
        return success(BeanUtils.toBean(materialCompare, MaterialCompareRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得卷宗材料比对分页")
    public CommonResult<PageResult<MaterialCompareRespVO>> getMaterialComparePage(@Valid @RequestBody MaterialComparePageReqVO pageReqVO) {
        PageResult<MaterialCompareDO> pageResult = materialCompareService.getMaterialComparePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MaterialCompareRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得卷宗材料比对列表")
    public CommonResult<List<MaterialCompareRespVO>> getMaterialCompareList(@Valid @RequestBody MaterialCompareListReqVO listReqVO) {
        List<MaterialCompareDO> list = materialCompareService.getMaterialCompareList(listReqVO);
        return success(BeanUtils.toBean(list, MaterialCompareRespVO.class));
    }
    
    @PostMapping("findCompareListByJgrybm")
    @ApiOperation(value = "通过监管人员编码获取卷宗材料比对")
    @ApiImplicitParam(name = "jgrybm", value = "监管人员编码", required = true)
    public CommonResult<?> findCompareListByJgrybm(@RequestParam(value = "jgrybm", required = true) String jgrybm){
    	QueryWrapper<MaterialCompareDO> wrapper = new QueryWrapper<>();
    	wrapper.eq("jgrybm", jgrybm);
    	wrapper.orderByDesc("add_time");
    	List<MaterialCompareDO> list = materialCompareService.list(wrapper);
    	return CommonResult.success(list);
    }
}
