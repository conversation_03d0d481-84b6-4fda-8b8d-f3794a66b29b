package com.rs.module.dam.entity.prison;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import java.util.Date;

/**
 * 一所一档-监所档案特色亮点配置 DO
 *
 * <AUTHOR>
 */
@TableName("dam_prison_feature_config")
@KeySequence("dam_prison_feature_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FeatureConfigDO extends BaseDO {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 特色亮点标题
     */
    private String featureTitle;
    /**
     * 建设时间
     */
    private Date constructionTime;
    /**
     * 表彰照片
     */
    private String recognitionPhotoPath;
    /**
     * 特色亮点内容
     */
    private String featureContent;

}
