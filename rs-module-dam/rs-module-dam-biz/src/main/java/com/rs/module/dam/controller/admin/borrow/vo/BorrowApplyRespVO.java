package com.rs.module.dam.controller.admin.borrow.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.annotation.GetFile;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.dromara.x.file.storage.core.FileInfo;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 借阅申请 Response VO")
@Data
public class BorrowApplyRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("申请时间")
    private Date addTime;
    @ApiModelProperty("申请人")
    private String addUser;
    @ApiModelProperty("申请人姓名")
    private String addUserName;
    @ApiModelProperty("申请单号")
    private String applyNo;
    @ApiModelProperty("借阅类型(0:内部借阅,1:外部借阅)")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JYLX")
    private String type;
    @ApiModelProperty("借阅单位编号")
    private String borrowOrgCode;
    @ApiModelProperty("借阅单位名称")
    private String borrowOrgName;
    @ApiModelProperty("借阅人")
    private String borrowUser;
    @ApiModelProperty("借阅人姓名")
    private String borrowUserName;
    @ApiModelProperty("申请理由")
    private String borrowReason;
    @ApiModelProperty("借阅天数")
    private Integer borrowDays;
    @ApiModelProperty("借阅截止时间")
    private Date borrowEndDate;
    @ApiModelProperty("借阅卷宗数量")
    private Integer borrowNum;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("附件")
    private String attachment;
    @ApiModelProperty("审批状态(0:待审批,1:审批中,2:审批通过,3:部分审批通过,4:审批不通过)")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JYSPZT")
    private String approveStatus;
    @ApiModelProperty("下级审批角色")
    private String nextApproveRole;
    @GetFile(value = "id")
    public List<FileInfo> fileList;
}
