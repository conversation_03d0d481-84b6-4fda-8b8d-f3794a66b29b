package com.rs.module.dam.service.archive;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.dam.controller.admin.archive.vo.*;
import com.rs.module.dam.entity.archive.ArchiveEditorDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.dam.dao.archive.ArchiveEditorDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 卷宗编辑 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ArchiveEditorServiceImpl extends BaseServiceImpl<ArchiveEditorDao, ArchiveEditorDO> implements ArchiveEditorService {

    @Resource
    private ArchiveEditorDao archiveEditorDao;

    @Override
    public String createArchiveEditor(ArchiveEditorSaveReqVO createReqVO) {
        // 插入
        ArchiveEditorDO archiveEditor = BeanUtils.toBean(createReqVO, ArchiveEditorDO.class);
        archiveEditorDao.insert(archiveEditor);
        // 返回
        return archiveEditor.getId();
    }

    @Override
    public void updateArchiveEditor(ArchiveEditorSaveReqVO updateReqVO) {
        // 校验存在
        validateArchiveEditorExists(updateReqVO.getId());
        // 更新
        ArchiveEditorDO updateObj = BeanUtils.toBean(updateReqVO, ArchiveEditorDO.class);
        archiveEditorDao.updateById(updateObj);
    }

    @Override
    public void deleteArchiveEditor(String id) {
        // 校验存在
        validateArchiveEditorExists(id);
        // 删除
        archiveEditorDao.deleteById(id);
    }

    private void validateArchiveEditorExists(String id) {
        if (archiveEditorDao.selectById(id) == null) {
            throw new ServerException("卷宗编辑数据不存在");
        }
    }

    @Override
    public ArchiveEditorDO getArchiveEditor(String id) {
        return archiveEditorDao.selectById(id);
    }

    @Override
    public PageResult<ArchiveEditorDO> getArchiveEditorPage(ArchiveEditorPageReqVO pageReqVO) {
        return archiveEditorDao.selectPage(pageReqVO);
    }

    @Override
    public List<ArchiveEditorDO> getArchiveEditorList(ArchiveEditorListReqVO listReqVO) {
        return archiveEditorDao.selectList(listReqVO);
    }


}
