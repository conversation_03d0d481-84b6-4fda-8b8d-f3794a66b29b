package com.rs.module.dam.controller.admin.ocr;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.cache.RedisClient;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.constant.CheckTypeEnum;
import com.rs.module.dam.constant.DamConstants;
import com.rs.module.dam.constant.JobStatusEnum;
import com.rs.module.dam.constant.RedisConstants;
import com.rs.module.dam.entity.ocr.FormatMinceJobDO;
import com.rs.module.dam.service.ocr.FormatCheckJobService;
import com.rs.module.dam.service.ocr.FormatMinceJobService;
import com.rs.module.dam.util.RedisTemplateUtil;
import com.rs.util.DicUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Api(tags = "ocr处理 - ocr格式化审查")
@RestController
@RequestMapping("/dam/ocr/formatCheck")
@Validated
@Slf4j
public class FormatCheckController {

	@Resource
    private FormatCheckJobService formatCheckJobService;

	@Resource
	private FormatMinceJobService formatMinceJobService;

	@Resource
	private RedisTemplateUtil redisTemplateUtil;

	@PostMapping("/jobCheckList")
    @ApiOperation(value = "查询格式化审查检测任务列表")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "jgrybm", value = "监管人员编码"),
		@ApiImplicitParam(name = "jobType", value = "任务类型"),
		@ApiImplicitParam(name = "jobStatus", value = "任务状态"),
		@ApiImplicitParam(name = "page", value = "当前页号"),
		@ApiImplicitParam(name = "pageSize", value = "每页大小")
	})
	@SuppressWarnings("unchecked")
	public CommonResult<?> jobCheckList(@RequestParam(value = "jgrybm") String jgrybm,
			@RequestParam(value = "jobType", required = false) String jobType,
			@RequestParam(value = "jobStatus") String jobStatus,
			@RequestParam(value = "page", defaultValue = "1") Integer page,
			@RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize){
		Map<String, String> params = MapUtil.of("jgrybm", jgrybm);
		params.put("jobStatus", jobStatus);
		params.put("jobType", jobType);

		//获取检测任务分页数据
		Page<Map<String, Object>> minceJobPage = formatMinceJobService.selectMinceJobList(params, page, pageSize);
		List<Map<String, Object>> minceJobList = minceJobPage.getRecords();

		//绑定进度标识
		if(CollectionUtil.isNotNull(minceJobList)) {
			minceJobList.forEach(map -> {
				String checkType = String.valueOf(map.get("check_type"));
				if(StringUtil.isNotEmpty(checkType)) {
					CheckTypeEnum checkTypeEnum = CheckTypeEnum.getByType(checkType);
					map.put("processKey" , checkTypeEnum.getProcess());
				}
			});
		}

		//绑定检测进度
		if(JobStatusEnum.NO.getStatus().equals(jobStatus)) {
			List<String> minceIds = minceJobList.stream().map(map -> (String)map.get("id")).collect(Collectors.toList());
			List<String> processList = RedisClient.hmultiGet(RedisConstants.FORMAT_CHECK_MINCE_JOB_PROCESS, minceIds);
			minceJobList.forEach(minceJob -> {
				processList.forEach(process -> {
                    if (process instanceof String){
                        Map<String, Object> map = JSONObject.parseObject((String) process, Map.class);
                        if (minceJob.get("id").equals(map.get("minceId"))){
                            minceJob.put("process",  map.get("process"));
                        }
                    }
                });
			});
		}

		//字典翻译
		List<Map<String, Object>> translateList = DicUtils.translate(minceJobList, "ZD_FORMAT_CHECK_TYPE", "check_type");
		minceJobList = translateList;

		//返回格式转换
		PageResult<Map<String, Object>> pageResult = new PageResult<>(minceJobList, minceJobPage.getTotal());

		return CommonResult.success(pageResult);
	}

	@PostMapping("/cancelMinceJob")
    @ApiOperation(value = "取消正在检测中的任务")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "minceId", value = "取消检测任务的id")
	})
	public CommonResult<?> cancelMinceJob(@RequestParam(value = "minceId") String minceId){
		FormatMinceJobDO minceJob = formatMinceJobService.getById(minceId);
		if(minceJob != null) {
			String jobStatus = minceJob.getJobStatus();

			//未执行或待处理的任务
			if(DamConstants.JOB_STATE_NO.equals(jobStatus) || DamConstants.JOB_STATE_PENDING.equals(jobStatus)) {
				String cancelKey = String.format(RedisConstants.FORMAT_CHECK_CANCEL_TYPE, minceJob.getJobId());
				redisTemplateUtil.sAdd(cancelKey, minceId);

				//更新任务状态
				minceJob.setJobStatus(DamConstants.JOB_STATE_CANCEL);
				formatMinceJobService.updateById(minceJob);

				//查询是否还有待执行的任务
				QueryWrapper<FormatMinceJobDO> wrapper = new QueryWrapper<>();
				wrapper.eq("jgrybm", minceJob.getJgrybm());
				wrapper.eq("job_id", minceJob.getJobId());
				wrapper.in("job_status", DamConstants.JOB_STATE_NO, DamConstants.JOB_STATE_PENDING);
				int checkCount = formatMinceJobService.count(wrapper);

				//所以任务执行完成，移除检测中的人员
				if(checkCount == 0) {
					String member = minceJob.getJgrybm() + "--" + minceJob.getJobType();
					redisTemplateUtil.sRem(RedisConstants.FORMAT_CHECKING, member);
					log.info("{}取消任务{}后，删除任务执行的key：{}", minceJob.getJgrybm(), minceId, member);
				}

				//移除点击重新检测时设置的任务类型
				String retryKey = String.format(RedisConstants.FORMAT_CHECK_RETRY_MARK, minceJob.getJgrybm(), minceJob.getJobType());
				redisTemplateUtil.sRem(retryKey, minceJob.getJobType());

				return CommonResult.success();
			}

			//检测完成的任务
			else {
				return CommonResult.error("该任务已检测完成，不能取消检测");
			}
		}
		else {
			return CommonResult.error("取消异常！任务不存在");
		}
	}
}
