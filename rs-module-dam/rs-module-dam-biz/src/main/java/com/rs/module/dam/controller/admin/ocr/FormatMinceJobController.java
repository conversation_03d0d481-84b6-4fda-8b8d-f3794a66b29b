package com.rs.module.dam.controller.admin.ocr;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.entity.ocr.FormatMinceJobDO;
import com.rs.module.dam.service.ocr.FormatMinceJobService;
import com.rs.module.dam.vo.ocr.FormatMinceJobListReqVO;
import com.rs.module.dam.vo.ocr.FormatMinceJobPageReqVO;
import com.rs.module.dam.vo.ocr.FormatMinceJobRespVO;
import com.rs.module.dam.vo.ocr.FormatMinceJobSaveReqVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

@Api(tags = "ocr处理 - ocr格式化审查任务细分")
@ApiIgnore
@RestController
@RequestMapping("/dam/ocr/formatMinceJob")
@Validated
public class FormatMinceJobController {

    @Resource
    private FormatMinceJobService formatMinceJobService;

    @PostMapping("/create")
    @ApiOperation(value = "创建ocr格式化审查任务细分")
    @LogRecordAnnotation(bizModule = "dam:formatMinceJob:create", operateType = LogOperateType.CREATE, title = "创建ocr格式化审查任务细分",
    success = "创建ocr格式化审查任务细分成功", fail = "创建ocr格式化审查任务细分失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createFormatMinceJob(@Valid @RequestBody FormatMinceJobSaveReqVO createReqVO) {
        return success(formatMinceJobService.createFormatMinceJob(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新ocr格式化审查任务细分")
    @LogRecordAnnotation(bizModule = "dam:formatMinceJob:update", operateType = LogOperateType.UPDATE, title = "更新ocr格式化审查任务细分",
    success = "更新ocr格式化审查任务细分成功", fail = "更新ocr格式化审查任务细分失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateFormatMinceJob(@Valid @RequestBody FormatMinceJobSaveReqVO updateReqVO) {
        formatMinceJobService.updateFormatMinceJob(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除ocr格式化审查任务细分")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:formatMinceJob:delete", operateType = LogOperateType.DELETE, title = "删除ocr格式化审查任务细分",
    success = "删除ocr格式化审查任务细分成功", fail = "删除ocr格式化审查任务细分失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteFormatMinceJob(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           formatMinceJobService.deleteFormatMinceJob(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得ocr格式化审查任务细分")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:formatMinceJob:get", operateType = LogOperateType.QUERY, title = "获取ocr格式化审查任务细分", bizNo = "{{#id}}", success = "获取ocr格式化审查任务细分成功", fail = "获取ocr格式化审查任务细分失败", extraInfo = "{{#id}}")
    public CommonResult<FormatMinceJobRespVO> getFormatMinceJob(@RequestParam("id") String id) {
        FormatMinceJobDO formatMinceJob = formatMinceJobService.getFormatMinceJob(id);
        return success(BeanUtils.toBean(formatMinceJob, FormatMinceJobRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得ocr格式化审查任务细分分页")
    @LogRecordAnnotation(bizModule = "dam:formatMinceJob:page", operateType = LogOperateType.QUERY, title = "获得ocr格式化审查任务细分分页",
    success = "获得ocr格式化审查任务细分分页成功", fail = "获得ocr格式化审查任务细分分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<FormatMinceJobRespVO>> getFormatMinceJobPage(@Valid @RequestBody FormatMinceJobPageReqVO pageReqVO) {
        PageResult<FormatMinceJobDO> pageResult = formatMinceJobService.getFormatMinceJobPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, FormatMinceJobRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得ocr格式化审查任务细分列表")
    @LogRecordAnnotation(bizModule = "dam:formatMinceJob:list", operateType = LogOperateType.QUERY, title = "获得ocr格式化审查任务细分列表",
    success = "获得ocr格式化审查任务细分列表成功", fail = "获得ocr格式化审查任务细分列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<FormatMinceJobRespVO>> getFormatMinceJobList(@Valid @RequestBody FormatMinceJobListReqVO listReqVO) {
    List<FormatMinceJobDO> list = formatMinceJobService.getFormatMinceJobList(listReqVO);
        return success(BeanUtils.toBean(list, FormatMinceJobRespVO.class));
    }

}
