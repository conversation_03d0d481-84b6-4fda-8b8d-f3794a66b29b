package com.rs.module.dam.config;

import java.net.SocketException;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.FanoutExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * rabbitmq配置类
 * <AUTHOR>
 * @date 2025年4月23日
 */
@Configuration
public class RabbitMqConfig {

	/** websocket通道名称 */
	@Value("${conf.dam.websocket.exchange}")
	private String DAM_WEBSOCKET_EXCHANGE_NAME;
	
	/** websocket队列名称 */
	@Value("${conf.dam.websocket.queue}")
	private String DAM_WEBSOCKET_QUEUE_NAME;
	
    @Autowired
    RabbitAdmin rabbitAdmin;
	
	/**
	 * 使用扇形交换机
	 * @return FanoutExchange
	 * <AUTHOR>
	 */
	@Bean
	public FanoutExchange fanoutExchange() {
		return new FanoutExchange(this.DAM_WEBSOCKET_EXCHANGE_NAME, true, false);
	}
	
	/**
	 * 获取Websocket消息队列
	 * @return Queue
	 * @throws SocketException
	 * <AUTHOR>
	 */
	@Bean
	public Queue wsQueue() {
		return new Queue(this.DAM_WEBSOCKET_QUEUE_NAME);
	}
	
	/**
	 * 绑定交换机和Websocket消息队列
	 * @return Binding
	 * @throws SocketException
	 * <AUTHOR>
	 */
	@Bean
	public Binding routingFirstBinding() throws SocketException {
		return BindingBuilder.bind(wsQueue()).to(fanoutExchange());
	}
	
    /**
     * 创建初始化RabbitAdmin对象
     */
    @Bean
    public RabbitAdmin rabbitAdmin(ConnectionFactory connectionFactory) {
        RabbitAdmin rabbitAdmin = new RabbitAdmin(connectionFactory);
        // 只有设置为 true，spring 才会加载 RabbitAdmin 这个类
        rabbitAdmin.setAutoStartup(true);
        return rabbitAdmin;
    }

    /**
     * 主动创建交换机和对列
     */
    @Bean
    public void createExchangeQueue() {
        rabbitAdmin.declareExchange(fanoutExchange());
        rabbitAdmin.declareQueue(wsQueue());
    }

    @Bean
    public RabbitTemplate rabbitTemplate(CachingConnectionFactory factory) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(factory);
        //设置开启Mandatory,才能触发回调函数,无论消息推送结果怎么样都强制调用回调函数
        // 需要配置 spring.rabbitmq.publisher-confirms: true
        rabbitTemplate.setMandatory(true);
        
        return rabbitTemplate;
    }
}
