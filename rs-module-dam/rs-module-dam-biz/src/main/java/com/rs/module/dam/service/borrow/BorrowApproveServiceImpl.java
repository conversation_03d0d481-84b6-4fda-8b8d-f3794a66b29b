package com.rs.module.dam.service.borrow;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApproveListReqVO;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApprovePageReqVO;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApproveSaveReqVO;
import com.rs.module.dam.dao.borrow.BorrowApproveDao;
import com.rs.module.dam.dao.borrow.BorrowPrisonerDao;
import com.rs.module.dam.entity.borrow.BorrowApproveDO;
import com.rs.module.dam.entity.borrow.BorrowPrisonerDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * 借阅审批 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BorrowApproveServiceImpl extends BaseServiceImpl<BorrowApproveDao, BorrowApproveDO> implements BorrowApproveService {

    @Resource
    private BorrowApproveDao borrowApproveDao;

    @Override
    public String createBorrowApprove(BorrowApproveSaveReqVO createReqVO) {
        // 插入
        BorrowApproveDO borrowApprove = BeanUtils.toBean(createReqVO, BorrowApproveDO.class);
        borrowApproveDao.insert(borrowApprove);
        // 返回
        return borrowApprove.getId();
    }

    @Override
    public void updateBorrowApprove(BorrowApproveSaveReqVO updateReqVO) {
        // 校验存在
        validateBorrowApproveExists(updateReqVO.getId());
        // 更新
        BorrowApproveDO updateObj = BeanUtils.toBean(updateReqVO, BorrowApproveDO.class);
        borrowApproveDao.updateById(updateObj);
    }

    @Override
    public void deleteBorrowApprove(String id) {
        // 校验存在
        validateBorrowApproveExists(id);
        // 删除
        borrowApproveDao.deleteById(id);
    }

    private void validateBorrowApproveExists(String id) {
        if (borrowApproveDao.selectById(id) == null) {
            throw new ServerException("借阅审批数据不存在");
        }
    }

    @Override
    public BorrowApproveDO getBorrowApprove(String id) {
        return borrowApproveDao.selectById(id);
    }

    @Override
    public List<BorrowApproveDO> getByBorrowId(String id) {
        List<BorrowApproveDO> list = borrowApproveDao.selectList(new LambdaQueryWrapper<BorrowApproveDO>()
                .eq(BorrowApproveDO::getBorrowId, id)
                .orderByAsc(BorrowApproveDO::getOrderId));
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public PageResult<BorrowApproveDO> getBorrowApprovePage(BorrowApprovePageReqVO pageReqVO) {
        return borrowApproveDao.selectPage(pageReqVO);
    }

    @Override
    public List<BorrowApproveDO> getBorrowApproveList(BorrowApproveListReqVO listReqVO) {
        return borrowApproveDao.selectList(listReqVO);
    }


}
