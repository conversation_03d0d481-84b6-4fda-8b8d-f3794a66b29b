package com.rs.module.dam.service.prison;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.controller.admin.prison.vo.FeatureConfigListReqVO;
import com.rs.module.dam.controller.admin.prison.vo.FeatureConfigPageReqVO;
import com.rs.module.dam.controller.admin.prison.vo.FeatureConfigSaveReqVO;
import com.rs.module.dam.dao.prison.FeatureConfigDao;
import com.rs.module.dam.entity.prison.FeatureConfigDO;
import com.rs.module.dam.entity.prison.PrisonArchiveDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;


/**
 * 一所一档-监所档案特色亮点配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FeatureConfigServiceImpl extends BaseServiceImpl<FeatureConfigDao, FeatureConfigDO> implements FeatureConfigService {

    @Resource
    private FeatureConfigDao featureConfigDao;

    @Override
    public String createFeatureConfig(FeatureConfigSaveReqVO createReqVO) {
        // 插入
        FeatureConfigDO featureConfig = BeanUtils.toBean(createReqVO, FeatureConfigDO.class);
        featureConfigDao.insert(featureConfig);
        // 返回
        return featureConfig.getId();
    }

    @Override
    public String updateFeatureConfig(FeatureConfigSaveReqVO updateReqVO) {
        // 校验存在
        validateFeatureConfigExists(updateReqVO.getId());
        // 更新
        FeatureConfigDO updateObj = BeanUtils.toBean(updateReqVO, FeatureConfigDO.class);
        featureConfigDao.updateById(updateObj);
        return updateObj.getId();
    }

    @Override
    public void deleteFeatureConfig(String id) {
        // 校验存在
        validateFeatureConfigExists(id);
        // 删除
        featureConfigDao.deleteById(id);
    }

    private void validateFeatureConfigExists(String id) {
        if (featureConfigDao.selectById(id) == null) {
            throw new ServerException("一所一档-监所档案特色亮点配置数据不存在");
        }
    }

    @Override
    public FeatureConfigDO getFeatureConfig(String id) {
        return featureConfigDao.selectById(id);
    }

    @Override
    public List<FeatureConfigDO> getByPrisonCode(String prisonCode) {
        if (StringUtil.isNullBlank(prisonCode)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<FeatureConfigDO>().eq(FeatureConfigDO::getOrgCode, prisonCode));
    }

    @Override
    public PageResult<FeatureConfigDO> getFeatureConfigPage(FeatureConfigPageReqVO pageReqVO) {
        return featureConfigDao.selectPage(pageReqVO);
    }

    @Override
    public List<FeatureConfigDO> getFeatureConfigList(FeatureConfigListReqVO listReqVO) {
        return featureConfigDao.selectList(listReqVO);
    }


}
