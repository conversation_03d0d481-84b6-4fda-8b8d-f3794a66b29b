package com.rs.module.dam.dao.prison;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.controller.admin.prison.vo.PrisonArchiveListReqVO;
import com.rs.module.dam.controller.admin.prison.vo.PrisonArchivePageReqVO;
import com.rs.module.dam.entity.prison.PrisonArchiveDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 一所一档-监所档案 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PrisonArchiveDao extends IBaseDao<PrisonArchiveDO> {


    default PageResult<PrisonArchiveDO> selectPage(PrisonArchivePageReqVO reqVO) {
        Page<PrisonArchiveDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrisonArchiveDO> wrapper = new LambdaQueryWrapperX<PrisonArchiveDO>()
            .likeIfPresent(PrisonArchiveDO::getPrisonName, reqVO.getPrisonName())
            .eqIfPresent(PrisonArchiveDO::getPrisonCode, reqVO.getPrisonCode())
            .eqIfPresent(PrisonArchiveDO::getContactPhone, reqVO.getContactPhone())
            .eqIfPresent(PrisonArchiveDO::getEmail, reqVO.getEmail())
            .eqIfPresent(PrisonArchiveDO::getAddress, reqVO.getAddress())
            .eqIfPresent(PrisonArchiveDO::getRemarks, reqVO.getRemarks())
            .eqIfPresent(PrisonArchiveDO::getBackgroundImageUrl, reqVO.getBackgroundImageUrl())
            .eqIfPresent(PrisonArchiveDO::getBuildingArea, reqVO.getBuildingArea())
            .eqIfPresent(PrisonArchiveDO::getMonitoringRoomCount, reqVO.getMonitoringRoomCount())
            .eqIfPresent(PrisonArchiveDO::getMonitoringRoomCapacity, reqVO.getMonitoringRoomCapacity())
            .eqIfPresent(PrisonArchiveDO::getFunctionRoomCount, reqVO.getFunctionRoomCount())
            .eqIfPresent(PrisonArchiveDO::getEquipmentCount, reqVO.getEquipmentCount())
            .eqIfPresent(PrisonArchiveDO::getTotalPoliceForce, reqVO.getTotalPoliceForce())
            .eqIfPresent(PrisonArchiveDO::getPoliceOfficerCount, reqVO.getPoliceOfficerCount())
            .eqIfPresent(PrisonArchiveDO::getAuxiliaryPoliceCount, reqVO.getAuxiliaryPoliceCount())
            .eqIfPresent(PrisonArchiveDO::getTotalDetainedCount, reqVO.getTotalDetainedCount())
            .eqIfPresent(PrisonArchiveDO::getTotalCaseCount, reqVO.getTotalCaseCount())
            .eqIfPresent(PrisonArchiveDO::getTotalInterrogatedSuspectCount, reqVO.getTotalInterrogatedSuspectCount())
            .eqIfPresent(PrisonArchiveDO::getTotalSeizedPropertyCount, reqVO.getTotalSeizedPropertyCount())
            .eqIfPresent(PrisonArchiveDO::getTotalSeizedPropertyValue, reqVO.getTotalSeizedPropertyValue());
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PrisonArchiveDO::getAddTime);
        }
        Page<PrisonArchiveDO> archivePage = selectPage(page, wrapper);
        return new PageResult<>(archivePage.getRecords(), archivePage.getTotal());
    }
    default List<PrisonArchiveDO> selectList(PrisonArchiveListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrisonArchiveDO>()
            .likeIfPresent(PrisonArchiveDO::getPrisonName, reqVO.getPrisonName())
            .eqIfPresent(PrisonArchiveDO::getPrisonCode, reqVO.getPrisonCode())
            .eqIfPresent(PrisonArchiveDO::getContactPhone, reqVO.getContactPhone())
            .eqIfPresent(PrisonArchiveDO::getEmail, reqVO.getEmail())
            .eqIfPresent(PrisonArchiveDO::getAddress, reqVO.getAddress())
            .eqIfPresent(PrisonArchiveDO::getRemarks, reqVO.getRemarks())
            .eqIfPresent(PrisonArchiveDO::getBackgroundImageUrl, reqVO.getBackgroundImageUrl())
            .eqIfPresent(PrisonArchiveDO::getBuildingArea, reqVO.getBuildingArea())
            .eqIfPresent(PrisonArchiveDO::getMonitoringRoomCount, reqVO.getMonitoringRoomCount())
            .eqIfPresent(PrisonArchiveDO::getMonitoringRoomCapacity, reqVO.getMonitoringRoomCapacity())
            .eqIfPresent(PrisonArchiveDO::getFunctionRoomCount, reqVO.getFunctionRoomCount())
            .eqIfPresent(PrisonArchiveDO::getEquipmentCount, reqVO.getEquipmentCount())
            .eqIfPresent(PrisonArchiveDO::getTotalPoliceForce, reqVO.getTotalPoliceForce())
            .eqIfPresent(PrisonArchiveDO::getPoliceOfficerCount, reqVO.getPoliceOfficerCount())
            .eqIfPresent(PrisonArchiveDO::getAuxiliaryPoliceCount, reqVO.getAuxiliaryPoliceCount())
            .eqIfPresent(PrisonArchiveDO::getTotalDetainedCount, reqVO.getTotalDetainedCount())
            .eqIfPresent(PrisonArchiveDO::getTotalCaseCount, reqVO.getTotalCaseCount())
            .eqIfPresent(PrisonArchiveDO::getTotalInterrogatedSuspectCount, reqVO.getTotalInterrogatedSuspectCount())
            .eqIfPresent(PrisonArchiveDO::getTotalSeizedPropertyCount, reqVO.getTotalSeizedPropertyCount())
            .eqIfPresent(PrisonArchiveDO::getTotalSeizedPropertyValue, reqVO.getTotalSeizedPropertyValue())
        .orderByDesc(PrisonArchiveDO::getAddTime));
    }

}
