package com.rs.module.dam.controller.admin.borrow;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.rs.util.DicUtils;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.cons.CommonConstants;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApproveListReqVO;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApprovePageReqVO;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApproveRespVO;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApproveSaveReqVO;
import com.rs.module.dam.entity.borrow.BorrowApproveDO;
import com.rs.module.dam.service.borrow.BorrowApproveService;

import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

@Api(tags = "卷宗借阅 - 借阅审批")
@RestController
@RequestMapping("/dam/borrow/borrowApprove")
@Validated
public class BorrowApproveController {

    @Resource
    private BorrowApproveService borrowApproveService;

    @PostMapping("/create")
    @ApiOperation(value = "创建借阅审批", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:borrowApprove:create", operateType = LogOperateType.CREATE, title = "创建借阅审批",
    success = "创建借阅审批成功", fail = "创建借阅审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createBorrowApprove(@Valid @RequestBody BorrowApproveSaveReqVO createReqVO) {
        return success(borrowApproveService.createBorrowApprove(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新借阅审批", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:borrowApprove:update", operateType = LogOperateType.UPDATE, title = "更新借阅审批",
    success = "更新借阅审批成功", fail = "更新借阅审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateBorrowApprove(@Valid @RequestBody BorrowApproveSaveReqVO updateReqVO) {
        borrowApproveService.updateBorrowApprove(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除借阅审批", hidden = true)
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:borrowApprove:delete", operateType = LogOperateType.DELETE, title = "删除借阅审批",
    success = "删除借阅审批成功", fail = "删除借阅审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteBorrowApprove(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           borrowApproveService.deleteBorrowApprove(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得借阅审批")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:borrowApprove:get", operateType = LogOperateType.QUERY, title = "获取借阅审批",
            bizNo = "{{#id}}", success = "获取借阅审批成功", fail = "获取借阅审批失败", extraInfo = "{{#id}}")
    public CommonResult<BorrowApproveRespVO> getBorrowApprove(@RequestParam("id") String id) {
        BorrowApproveDO borrowApprove = borrowApproveService.getBorrowApprove(id);
        return success(BeanUtils.toBean(borrowApprove, BorrowApproveRespVO.class));
    }

    @GetMapping("/getByBorrowId")
    @ApiOperation(value = "获得借阅审批")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:borrowApprove:get", operateType = LogOperateType.QUERY, title = "获取借阅审批",
            bizNo = "{{#id}}", success = "获取借阅审批成功", fail = "获取借阅审批失败", extraInfo = "{{#id}}")
    public CommonResult<JSONObject> getByBorrowId(@RequestParam("borrowId") String borrowId) {
        JSONObject result = new JSONObject();
        List<BorrowApproveDO> list = borrowApproveService.getByBorrowId(borrowId);
        long count = list.stream().filter(item -> ObjectUtil.isNotEmpty(item.getAddUser())).count();
        list.forEach(item -> {
            item.setApproveStatusName(DicUtils.translate(CommonConstants.DIC_ZD_SPJG, item.getApproveStatus()));
        });
        result.put("data", list);
        result.put("index", count);
        return success(result);
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得借阅审批分页", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:borrowApprove:page", operateType = LogOperateType.QUERY, title = "获得借阅审批分页",
    success = "获得借阅审批分页成功", fail = "获得借阅审批分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<BorrowApproveRespVO>> getBorrowApprovePage(@Valid @RequestBody BorrowApprovePageReqVO pageReqVO) {
        PageResult<BorrowApproveDO> pageResult = borrowApproveService.getBorrowApprovePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BorrowApproveRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得借阅审批列表", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:borrowApprove:list", operateType = LogOperateType.QUERY, title = "获得借阅审批列表",
    success = "获得借阅审批列表成功", fail = "获得借阅审批列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<BorrowApproveRespVO>> getBorrowApproveList(@Valid @RequestBody BorrowApproveListReqVO listReqVO) {
    List<BorrowApproveDO> list = borrowApproveService.getBorrowApproveList(listReqVO);
        return success(BeanUtils.toBean(list, BorrowApproveRespVO.class));
    }

}
