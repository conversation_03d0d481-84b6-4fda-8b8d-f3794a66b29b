package com.rs.module.dam.controller.admin.event.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.annotation.GetFile;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.x.file.storage.core.FileInfo;

import java.util.List;

@ApiModel(description = "管理后台 - 一事一档-事件档案调查记录 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class InvestRecordRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("事件ID")
    private String eventId;
    @ApiModelProperty("事件编号(规则：S+9位单位编号+8位日期+6位序号)")
    private String eventCode;
    @ApiModelProperty("调查详情")
    private String investDetails;
    @ApiModelProperty("上传附件存储路径")
    private String attachmentPath;
    @GetFile(value = "id")
    public List<FileInfo> fileList;
}
