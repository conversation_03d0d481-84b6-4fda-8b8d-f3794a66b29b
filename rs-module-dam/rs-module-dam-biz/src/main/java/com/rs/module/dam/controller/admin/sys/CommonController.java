package com.rs.module.dam.controller.admin.sys;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bsp.common.util.RequestUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "系统配置 - 通用")
@RestController
@RequestMapping("/dam/sys/common")
@Validated
public class CommonController {

	@Value("${conf.server.ip}")
	private String ip;
	
	@Value("${conf.server.port.dam}")
	private String port;
	
	@GetMapping("/getConfIpAndPort")
	@ApiOperation(value = "获取IP和端口配置")
	public CommonResult<String> getConfIpAndPort(){
		return CommonResult.success(ip + ":" + port);
	}
	
	@GetMapping("/getCurClientInfo")
	@ApiOperation(value = "获取当前客户端信息")
	public CommonResult<?> getCurClientInfo(HttpServletRequest request){
		try {
			String ip = RequestUtil.getClientIp(request);
			SessionUser user = SessionUserUtil.getSessionUser();
			
			return CommonResult.success(JSONUtil.createObj()
					.set("ip", ip).set("userName", user.getName()));
		}
		catch(Exception e) {
			return CommonResult.error("获取信息异常：" + e.getMessage());
		}
	}
}
