package com.rs.module.dam.controller.admin.prison.vo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rs.framework.annotation.GetFile;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
import org.dromara.x.file.storage.core.FileInfo;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 一所一档-监所档案荣誉表彰配置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RecognitionConfigRespVO extends BaseVO implements TransPojo{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("表彰名称")
    private String recognitionName;
    @ApiModelProperty("表彰时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date recognitionTime;
    @GetFile(value = "id")
    public List<FileInfo> fileList;
}
