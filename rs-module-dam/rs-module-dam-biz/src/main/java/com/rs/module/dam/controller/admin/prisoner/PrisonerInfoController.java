package com.rs.module.dam.controller.admin.prisoner;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.util.StringUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.entity.prisoner.PrisonerInfoDO;
import com.rs.module.dam.service.prisoner.PrisonerInfoService;
import com.rs.module.dam.vo.prisoner.PrisonerInfoListReqVO;
import com.rs.module.dam.vo.prisoner.PrisonerInfoPageReqVO;
import com.rs.module.dam.vo.prisoner.PrisonerInfoRespVO;
import com.rs.module.dam.vo.prisoner.PrisonerInfoSaveReqVO;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@Api(tags = "监管人员 - 卷宗监管人员")
@RestController
@RequestMapping("/dam/prisoner/prisonerInfo")
@Validated
public class PrisonerInfoController {

    @Resource
    private PrisonerInfoService prisonerInfoService;

    @PostMapping("/create")
    @ApiOperation(value = "创建卷宗监管人员", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:prisonerInfo:create", operateType = LogOperateType.CREATE, title = "创建卷宗监管人员",
    success = "创建卷宗监管人员成功", fail = "创建卷宗监管人员失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPrisonerInfo(@Valid @RequestBody PrisonerInfoSaveReqVO createReqVO) {
        return success(prisonerInfoService.createPrisonerInfo(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新卷宗监管人员", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:prisonerInfo:update", operateType = LogOperateType.UPDATE, title = "更新卷宗监管人员",
    success = "更新卷宗监管人员成功", fail = "更新卷宗监管人员失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePrisonerInfo(@Valid @RequestBody PrisonerInfoSaveReqVO updateReqVO) {
        prisonerInfoService.updatePrisonerInfo(updateReqVO);
        return success(true);
    }
    
    @PostMapping("/save")
    @ApiOperation(value = "保存卷宗监管人员")
    @LogRecordAnnotation(bizModule = "dam:prisonerInfo:save", operateType = LogOperateType.CREATE, title = "保存卷宗监管人员",
    success = "保存卷宗监管人员成功", fail = "保存卷宗监管人员失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> savePrisonerInfo(@Valid @RequestBody PrisonerInfoSaveReqVO createReqVO) {
    	if(StringUtil.isEmpty(createReqVO.getJgrybm())){
    		return CommonResult.error("请输入监管人员编码");
    	}
    	
    	QueryWrapper<PrisonerInfoDO> wrapper = new QueryWrapper<PrisonerInfoDO>().eq("jgrybm", createReqVO.getJgrybm());
    	PrisonerInfoDO prisonerInfoDO = prisonerInfoService.getOne(wrapper);
    	
    	//创建卷宗监管人员
    	if(prisonerInfoDO == null) {
    		return success(prisonerInfoService.createPrisonerInfo(createReqVO));
    	}
    	
    	//更新卷宗监管人员
    	else {
    		createReqVO.setId(prisonerInfoDO.getId());
    		prisonerInfoService.updatePrisonerInfo(createReqVO);
            return success("卷宗更新成功");
    	}
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除卷宗监管人员")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:prisonerInfo:delete", operateType = LogOperateType.DELETE, title = "删除卷宗监管人员",
    success = "删除卷宗监管人员成功", fail = "删除卷宗监管人员失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePrisonerInfo(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           prisonerInfoService.deletePrisonerInfo(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得卷宗监管人员")
    @ApiImplicitParams({
    		@ApiImplicitParam(name = "id", value = "监管人员Id"),
			@ApiImplicitParam(name = "jgrybm", value = "监管人员编码")
	})
    @LogRecordAnnotation(bizModule = "dam:prisonerInfo:get", operateType = LogOperateType.QUERY, title = "获取卷宗监管人员", bizNo = "{{#id}}", success = "获取卷宗监管人员成功", fail = "获取卷宗监管人员失败", extraInfo = "{{#id}}")
    public CommonResult<?> getPrisonerInfo(
    		@RequestParam(value = "id", required = false) String id,
    		@RequestParam(value = "jgrybm", required = false) String jgrybm) {
    	if(StringUtil.isNotEmpty(id)) {
    		PrisonerInfoDO prisonerInfo = prisonerInfoService.getPrisonerInfo(id);
            return success(BeanUtils.toBean(prisonerInfo, PrisonerInfoRespVO.class));
    	}
    	else if(StringUtil.isNotEmpty(jgrybm)) {
    		QueryWrapper<PrisonerInfoDO> wrapper = new QueryWrapper<PrisonerInfoDO>();
    		wrapper.eq("jgrybm", jgrybm);
    		PrisonerInfoDO prisonerInfo = prisonerInfoService.getOne(wrapper);
    		return success(prisonerInfo);
    	}
    	else {
    		return CommonResult.error("参数不正确");
    	}
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得卷宗监管人员分页", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:prisonerInfo:page", operateType = LogOperateType.QUERY, title = "获得卷宗监管人员分页",
    success = "获得卷宗监管人员分页成功", fail = "获得卷宗监管人员分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<PrisonerInfoRespVO>> getPrisonerInfoPage(@Valid @RequestBody PrisonerInfoPageReqVO pageReqVO) {
        PageResult<PrisonerInfoDO> pageResult = prisonerInfoService.getPrisonerInfoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PrisonerInfoRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得卷宗监管人员列表", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:prisonerInfo:list", operateType = LogOperateType.QUERY, title = "获得卷宗监管人员列表",
    success = "获得卷宗监管人员列表成功", fail = "获得卷宗监管人员列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<PrisonerInfoRespVO>> getPrisonerInfoList(@Valid @RequestBody PrisonerInfoListReqVO listReqVO) {
    List<PrisonerInfoDO> list = prisonerInfoService.getPrisonerInfoList(listReqVO);
        return success(BeanUtils.toBean(list, PrisonerInfoRespVO.class));
    }

    @PostMapping("/archivedPrisoner")
    @ApiOperation(value = "监管人员档案归档")
    @ApiImplicitParams({
    		@ApiImplicitParam(name = "jzzt", value = "卷宗状态(01：已登记；02：已组卷；03：已归档)"),
    		@ApiImplicitParam(name = "jgrybm", value = "监管人员编码")
    })
    @LogRecordAnnotation(bizModule = "dam:prisonerInfo:archivedPrisoner", operateType = LogOperateType.UPDATE, title = "监管人员档案归档", 
    	bizNo = "{{#id}}", success = "监管人员档案归档成功", fail = "监管人员档案归档失败", extraInfo = "{{#jgrybm}}")
    public CommonResult<?> archivedPrisoner(
    		@RequestParam(value = "jzzt") String jzzt,
    		@RequestParam(value = "jgrybm") String jgrybm) {
    	if(StringUtil.isNotEmpty(jgrybm)) {
    		prisonerInfoService.updateJzzt(jgrybm, jzzt, null, new Date());
    		return success("归档成功");
    	}
    	else {
    		return CommonResult.error("监管人员编码不能为空");
    	}
    }
    
	@PostMapping("/getExportUrl")
    @ApiOperation(value = "监管人员卷宗导出")
    @ApiImplicitParams({
    		@ApiImplicitParam(name = "jgrybm", value = "监管人员编码"),
    		@ApiImplicitParam(name = "zjCatalog", value = "组卷目录")
    })
    @LogRecordAnnotation(bizModule = "dam:prisonerInfo:archivedPrisoner", operateType = LogOperateType.UPDATE, title = "监管人员档案归档", 
    	bizNo = "{{#jgrybm}}", success = "监管人员卷宗导出成功", fail = "监管人员卷宗导出失败", extraInfo = "{{#zjCatalog}}")
    public CommonResult<?> getExportUrl(
    		@RequestParam(value = "jgrybm") String jgrybm,
    		@RequestParam(value = "zjCatalog") String zjCatalog) {
		try {
			String exportUrl = prisonerInfoService.getExportUrl(jgrybm, zjCatalog);
			return CommonResult.success(JSONUtil.createObj()
					.set("exportUrl", exportUrl));
		}
		catch(Exception e) {
			return CommonResult.error("监管人员卷宗导出失败，异常信息：" + e.getMessage());
		}
    }
}
