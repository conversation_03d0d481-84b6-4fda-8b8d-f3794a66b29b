package com.rs.module.dam.service.borrow;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApproveListReqVO;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApprovePageReqVO;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApproveSaveReqVO;
import com.rs.module.dam.entity.borrow.BorrowApproveDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 借阅审批 Service 接口
 *
 * <AUTHOR>
 */
public interface BorrowApproveService extends IBaseService<BorrowApproveDO>{

    /**
     * 创建借阅审批
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createBorrowApprove(@Valid BorrowApproveSaveReqVO createReqVO);

    /**
     * 更新借阅审批
     *
     * @param updateReqVO 更新信息
     */
    void updateBorrowApprove(@Valid BorrowApproveSaveReqVO updateReqVO);

    /**
     * 删除借阅审批
     *
     * @param id 编号
     */
    void deleteBorrowApprove(String id);

    /**
     * 获得借阅审批
     *
     * @param id 编号
     * @return 借阅审批
     */
    BorrowApproveDO getBorrowApprove(String id);

    /**
     * 获得借阅审批流程
     *
     * @param id 借阅ID
     * @return 借阅审批
     */
    List<BorrowApproveDO> getByBorrowId(String id);

    /**
    * 获得借阅审批分页
    *
    * @param pageReqVO 分页查询
    * @return 借阅审批分页
    */
    PageResult<BorrowApproveDO> getBorrowApprovePage(BorrowApprovePageReqVO pageReqVO);

    /**
    * 获得借阅审批列表
    *
    * @param listReqVO 查询条件
    * @return 借阅审批列表
    */
    List<BorrowApproveDO> getBorrowApproveList(BorrowApproveListReqVO listReqVO);


}
