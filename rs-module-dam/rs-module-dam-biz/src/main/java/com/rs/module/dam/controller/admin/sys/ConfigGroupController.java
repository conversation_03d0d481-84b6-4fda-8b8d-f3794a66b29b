package com.rs.module.dam.controller.admin.sys;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.entity.sys.ConfigGroupDO;
import com.rs.module.dam.entity.sys.ConfigGroupOrgDO;
import com.rs.module.dam.service.sys.ConfigGroupService;
import com.rs.module.dam.vo.sys.ConfigGroupListReqVO;
import com.rs.module.dam.vo.sys.ConfigGroupPageReqVO;
import com.rs.module.dam.vo.sys.ConfigGroupRespVO;
import com.rs.module.dam.vo.sys.ConfigGroupSaveReqVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

@Api(tags = "系统配置 - 卷宗配置")
@RestController
@RequestMapping("/dam/sys/configGroup")
@Validated
public class ConfigGroupController {

    @Resource
    private ConfigGroupService configGroupService;

    @PostMapping("/create")
    @ApiOperation(value = "创建卷宗配置")
    @LogRecordAnnotation(bizModule = "dam:configGroup:create", operateType = LogOperateType.CREATE, title = "创建卷宗配置",
    success = "创建卷宗配置成功", fail = "创建卷宗配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createConfigGroup(@Valid @RequestBody ConfigGroupSaveReqVO createReqVO) {
        return success(configGroupService.createConfigGroup(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新卷宗配置")
    @LogRecordAnnotation(bizModule = "dam:configGroup:update", operateType = LogOperateType.UPDATE, title = "更新卷宗配置",
    success = "更新卷宗配置成功", fail = "更新卷宗配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateConfigGroup(@Valid @RequestBody ConfigGroupSaveReqVO updateReqVO) {
        configGroupService.updateConfigGroup(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除卷宗配置")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:configGroup:delete", operateType = LogOperateType.DELETE, title = "删除卷宗配置",
    success = "删除卷宗配置成功", fail = "删除卷宗配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteConfigGroup(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           configGroupService.deleteConfigGroup(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得卷宗配置")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:configGroup:get", operateType = LogOperateType.QUERY, title = "获取卷宗配置",
            bizNo = "{{#id}}", success = "获取卷宗配置成功", fail = "获取卷宗配置失败", extraInfo = "{{#id}}")
    public CommonResult<ConfigGroupRespVO> getConfigGroup(@RequestParam("id") String id) {
        ConfigGroupDO configGroup = configGroupService.getConfigGroup(id);
        return success(BeanUtils.toBean(configGroup, ConfigGroupRespVO.class));
    }

    @GetMapping("/getByOrgCode")
    @ApiOperation(value = "获得卷宗配置")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:configGroup:get", operateType = LogOperateType.QUERY, title = "获取卷宗配置",
            bizNo = "{{#id}}", success = "获取卷宗配置成功", fail = "获取卷宗配置失败", extraInfo = "{{#id}}")
    public CommonResult<ConfigGroupRespVO> getByOrgCode(@RequestParam("orgCode") String orgCode) {
        ConfigGroupDO configGroup = configGroupService.getByOrgCode(orgCode);
        return success(BeanUtils.toBean(configGroup, ConfigGroupRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得卷宗配置分页", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:configGroup:page", operateType = LogOperateType.QUERY, title = "获得卷宗配置分页",
    success = "获得卷宗配置分页成功", fail = "获得卷宗配置分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<ConfigGroupRespVO>> getConfigGroupPage(@Valid @RequestBody ConfigGroupPageReqVO pageReqVO) {
        PageResult<ConfigGroupDO> pageResult = configGroupService.getConfigGroupPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ConfigGroupRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得卷宗配置列表", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:configGroup:list", operateType = LogOperateType.QUERY, title = "获得卷宗配置列表",
    success = "获得卷宗配置列表成功", fail = "获得卷宗配置列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<ConfigGroupRespVO>> getConfigGroupList(@Valid @RequestBody ConfigGroupListReqVO listReqVO) {
    List<ConfigGroupDO> list = configGroupService.getConfigGroupList(listReqVO);
        return success(BeanUtils.toBean(list, ConfigGroupRespVO.class));
    }

    // ==================== 子表（卷宗配置机构关联） ====================

    @GetMapping("/config-group-org/list-by-group-id")
    @ApiOperation(value = "获得卷宗配置机构关联列表", hidden = true)
    @ApiImplicitParam(name = "groupId", value = "配置组Id")
    public CommonResult<List<ConfigGroupOrgDO>> getConfigGroupOrgListByGroupId(@RequestParam("groupId") String groupId) {
        return success(configGroupService.getConfigGroupOrgListByGroupId(groupId));
    }

}
