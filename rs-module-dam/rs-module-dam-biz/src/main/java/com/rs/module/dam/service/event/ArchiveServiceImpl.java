package com.rs.module.dam.service.event;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.util.DicUtils;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.dam.cons.CommonConstants;
import com.rs.module.dam.controller.admin.event.vo.ArchiveListReqVO;
import com.rs.module.dam.controller.admin.event.vo.ArchivePageReqVO;
import com.rs.module.dam.controller.admin.event.vo.ArchiveSaveReqVO;
import com.rs.module.dam.dao.event.ArchiveDao;
import com.rs.module.dam.dao.event.ArchivePersonnelDao;
import com.rs.module.dam.dao.event.ArchivePrisonerDao;
import com.rs.module.dam.dao.event.InvestRecordDao;
import com.rs.module.dam.entity.event.ArchiveDO;
import com.rs.module.dam.entity.event.ArchivePersonnelDO;
import com.rs.module.dam.entity.event.ArchivePrisonerDO;
import com.rs.module.dam.entity.event.InvestRecordDO;
import com.rs.module.oss.entity.FileDetail;
import com.rs.module.oss.service.FileDetailService;
import org.dromara.x.file.storage.core.FileInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;


/**
 * 一事一档-事件档案 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ArchiveServiceImpl extends BaseServiceImpl<ArchiveDao, ArchiveDO> implements ArchiveService {

    @Resource
    private ArchiveDao archiveDao;
    @Resource
    private ArchivePrisonerDao archivePrisonerDao;
    @Resource
    private ArchivePersonnelDao archivePersonnelDao;
    @Resource
    private InvestRecordDao investRecordDao;
    @Resource
    private FileDetailService fileDetailService;
    @Resource
    private BspApi bspApi;
    private static final String defKey = "event11010020250424000001";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createArchive(ArchiveSaveReqVO createReqVO) {
        // 插入
        ArchiveDO archive = BeanUtils.toBean(createReqVO, ArchiveDO.class);
        archive.setEventStatus(CommonConstants.EVENT_ARCHIVE_STATUS_WAIT);
        archive.setEventCode(bspApi.executeByRuleCode(CommonConstants.EVENT_CODE_RULE, null));

        archiveDao.insert(archive);

        // 插入子表-事件档案监管人员关联表
        createArchivePrisonerList(archive.getId(), archive.getEventCode(), createReqVO.getArchivePrisoners());

        // 插入子表-事件档案工作人员关联表
        createArchivePersonnelList(archive.getId(), archive.getEventCode(), createReqVO.getArchivePersonnels());

        // 插入子表-事件档案调查记录表
        InvestRecordDO investRecord = createReqVO.getInvestRecord();
        createInvestRecord(archive.getId(), archive.getEventCode(), investRecord);

        // 插入子表-事件档案调查记录表-附件表
        createFileList(investRecord.getId(), investRecord.getFileList());

        // 发起流程审批
        String msgUrl = String.format("/#/record/detail?id=%s&type=2", archive.getId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", archive.getId());
        variables.put("busType", MsgBusTypeEnum.JZ_SJSB.getCode());
        JSONObject result = BspApprovalUtil.defaultStartProcess(HttpUtils.getAppCode() + "-" + defKey, archive.getId(), null, msgUrl, variables);
        JSONObject data = result.getJSONObject("data");
        JSONObject bpmTrail = data.getJSONObject("bpmTrail");
        archive.setActInstId(bpmTrail.getString("actInstId"));
        archive.setTaskId(bpmTrail.getString("taskId"));
        archiveDao.updateById(archive);
        // 返回
        return archive.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateArchive(ArchiveSaveReqVO updateReqVO) {
        // 校验存在
        validateArchiveExists(updateReqVO.getId());
        // 更新
        ArchiveDO updateObj = BeanUtils.toBean(updateReqVO, ArchiveDO.class);

        // 更新子表-事件档案监管人员关联表
        updateArchivePrisonerList(updateReqVO.getId(), updateReqVO.getEventCode(), updateReqVO.getArchivePrisoners());

        // 更新子表-事件档案工作人员关联表
        updateArchivePersonnelList(updateReqVO.getId(), updateReqVO.getEventCode(), updateReqVO.getArchivePersonnels());

        // 更新子表-事件档案调查记录表
        updateInvestRecord(updateReqVO.getId(), updateReqVO.getEventCode(), updateReqVO.getInvestRecord());
        // 插入子表-事件档案调查记录表-附件表
        InvestRecordDO investRecord = updateReqVO.getInvestRecord();
        createFileList(investRecord.getId(), investRecord.getFileList());

        // 流程审批再次上报
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
//        String msgTit = String.format("%s%s发起事件上报审批，请及时处理！", sessionUser.getOrgName(), sessionUser.getName());
        String msgUrl = String.format("/#/record/detail?id=%s&type=2", updateReqVO.getId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", updateReqVO.getId());
        variables.put("busType", MsgBusTypeEnum.JZ_SJSB.getCode());
        // 获取流程审批返回的流程实例ID和任务ID，每次审批后，任务ID会改变，所以每次审批后，需要重新获取
        JSONObject processResult = BspApprovalUtil.defaultApprovalProcess(HttpUtils.getAppCode() + "-" + defKey, updateReqVO.getActInstId(), updateReqVO.getTaskId(),
                updateReqVO.getId(), BspApproceStatusEnum.PASSED, "再次上报", null, msgUrl, variables);
        JSONObject data = processResult.getJSONObject("data");
        JSONObject bpmTrail = data.getJSONObject("bpmTrail");
        updateObj.setActInstId(bpmTrail.getString("actInstId"));
        updateObj.setTaskId(bpmTrail.getString("taskId"));
        updateObj.setId(updateReqVO.getId());
        updateObj.setEventStatus(CommonConstants.EVENT_ARCHIVE_STATUS_WAIT);
        archiveDao.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteArchive(String id) {
        // 校验存在
        validateArchiveExists(id);
        // 删除
        archiveDao.deleteById(id);

        // 删除子表-事件档案监管人员关联表
        deleteArchivePrisonerByEventId(id);

        // 删除子表-事件档案工作人员关联表
        deleteArchivePersonnelByEventId(id);
    }

    private void validateArchiveExists(String id) {
        if (archiveDao.selectById(id) == null) {
            throw new ServerException("一事一档-事件档案数据不存在");
        }
    }

    @Override
    public ArchiveDO getArchive(String id) {
        return archiveDao.selectById(id);
    }

    @Override
    public PageResult<ArchiveDO> getArchivePage(ArchivePageReqVO pageReqVO) {
        return archiveDao.selectPage(pageReqVO);
    }

    @Override
    public List<ArchiveDO> getArchiveList(ArchiveListReqVO listReqVO) {
        return archiveDao.selectList(listReqVO);
    }

    @Override
    public void approvalProcess(String eventId, String approvalResult, String approvalComments) {
        ArchiveDO archive = archiveDao.selectById(eventId);
        String msgTit = "";
        String msgUrl = "";

        // 判断流程审批状态
        BspApproceStatusEnum statusEnum = BspApproceStatusEnum.PASSED;
        String result = CommonConstants.EVENT_ARCHIVE_STATUS_APPROVED;
        Map<String, Object> variables = new HashMap<>();
        if (approvalResult.equals("1")) {
            statusEnum = BspApproceStatusEnum.REJECT;
            result = CommonConstants.EVENT_ARCHIVE_STATUS_REJECT;
//            msgTit = String.format("你上报的事件【%s】流程被退回了，请尽快处理", DicUtils.translate(
//                    CommonConstants.DIC_ZD_EVENT_SJLX, archive.getEventType()));
            msgUrl = String.format("/#/record/apply?id=%s", archive.getId());
            variables.put("eventTypeName", DicUtils.translate(CommonConstants.DIC_ZD_EVENT_SJLX, archive.getEventType()));
            variables.put("ywbh", eventId);
            variables.put("busType", MsgBusTypeEnum.JZ_SJHT.getCode());
        }

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        // 获取流程审批返回的流程实例ID和任务ID，并填充审批人信息
        JSONObject processResult = BspApprovalUtil.defaultApprovalProcess(HttpUtils.getAppCode() + "-" + defKey, archive.getActInstId(),
                archive.getTaskId(), eventId, statusEnum, approvalComments, msgTit, msgUrl, variables);
        JSONObject data = processResult.getJSONObject("data");
        JSONObject bpmTrail = data.getJSONObject("bpmTrail");
        archive.setId(eventId);
        archive.setActInstId(bpmTrail.getString("actInstId"));
        archive.setTaskId(bpmTrail.getString("taskId"));
        archive.setApprovalResult(result);
        archive.setEventStatus(result);
        archive.setApprovalComments(approvalComments);
        archive.setApproverXm(sessionUser.getName());
        archive.setApproverSfzh(sessionUser.getIdCard());
        archive.setApproverTime(new Date());
        archiveDao.updateById(archive);
    }


    // ==================== 子表（一事一档-事件档案监管人员关联） ====================

    @Override
    public List<ArchivePrisonerDO> getArchivePrisonerListByEventId(String eventId) {
        return archivePrisonerDao.selectListByEventId(eventId);
    }

    private void createArchivePrisonerList(String eventId, String eventCode, List<ArchivePrisonerDO> list) {
        list.forEach(o -> {
            o.setEventId(eventId);
            o.setEventCode(eventCode);
            o.setId(null);
        });
        list.forEach(o -> archivePrisonerDao.insert(o));
    }

    private void updateArchivePrisonerList(String eventId, String eventCode, List<ArchivePrisonerDO> list) {
        deleteArchivePrisonerByEventId(eventId);
        list.forEach(o -> {
            o.setEventId(eventId);
            o.setEventCode(eventCode);
            o.setId(null);
        });
        list.forEach(o -> archivePrisonerDao.insert(o));
    }

    private void deleteArchivePrisonerByEventId(String eventId) {
        archivePrisonerDao.deleteByEventId(eventId);
    }


    // ==================== 子表（一事一档-事件档案工作人员关联表） ====================

    @Override
    public List<ArchivePersonnelDO> getArchivePersonnelListByEventId(String eventId) {
        return archivePersonnelDao.selectListByEventId(eventId);
    }

    private void createArchivePersonnelList(String eventId, String eventCode, List<ArchivePersonnelDO> list) {
        list.forEach(o -> {
            o.setEventId(eventId);
            o.setEventCode(eventCode);
            o.setId(null);
        });
        list.forEach(o -> archivePersonnelDao.insert(o));
    }

    private void updateArchivePersonnelList(String eventId, String eventCode, List<ArchivePersonnelDO> list) {
        deleteArchivePersonnelByEventId(eventId);
        list.forEach(o -> {
            o.setEventId(eventId);
            o.setEventCode(eventCode);
            o.setId(null);
        });
        list.forEach(o -> archivePersonnelDao.insert(o));
    }

    private void deleteArchivePersonnelByEventId(String eventId) {
        archivePersonnelDao.deleteByEventId(eventId);
    }


    // ==================== 子表（一事一档-事件档案调查记录表） ====================

    @Override
    public List<InvestRecordDO> getInvestRecordListByEventId(String eventId) {
        return investRecordDao.selectListByEventId(eventId);
    }

    private void createInvestRecord(String eventId, String eventCode, InvestRecordDO investRecord) {
        investRecord.setId(null);
        investRecord.setEventId(eventId);
        investRecord.setEventCode(eventCode);
        investRecordDao.insert(investRecord);
    }

    private void updateInvestRecord(String eventId, String eventCode, InvestRecordDO investRecord) {
        investRecord.setEventId(eventId);
        investRecord.setEventCode(eventCode);
        investRecordDao.updateById(investRecord);
    }


    // ==================== 子表（一事一档-事件档案调查记录表） ====================

    private void createFileList(String objectId, List<FileInfo> fileList) {
        fileDetailService.remove(new LambdaQueryWrapper<FileDetail>().eq(FileDetail::getObjectId, objectId));
        List<FileDetail> fileDetailList = new ArrayList<>();
        fileList.forEach(o -> {
            try {
                o.setObjectId(objectId);
                FileDetail fileDetail = fileDetailService.toFileDetail(o);
                fileDetailList.add(fileDetail);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        });
        fileDetailService.saveOrUpdateBatch(fileDetailList);
    }

}
