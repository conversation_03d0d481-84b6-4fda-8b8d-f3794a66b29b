package com.rs.module.dam.entity.prison;

import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.annotation.GetFile;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;
import org.dromara.x.file.storage.core.FileInfo;

import java.util.Date;
import java.util.List;

/**
 * 一所一档-监所档案荣誉表彰配置 DO
 *
 * <AUTHOR>
 */
@TableName("dam_prison_recognition_config")
@KeySequence("dam_prison_recognition_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecognitionConfigDO extends BaseDO {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 表彰名称
     */
    private String recognitionName;
    /**
     * 表彰时间
     */
    private Date recognitionTime;
    /**
     * 表彰照片
     */
    private String recognitionPhotoPath;

}
