package com.rs.module.dam.service.archive;

import java.util.*;
import javax.validation.*;
import com.rs.module.dam.controller.admin.archive.vo.*;
import com.rs.module.dam.entity.archive.ArchiveEditorDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 卷宗编辑 Service 接口
 *
 * <AUTHOR>
 */
public interface ArchiveEditorService extends IBaseService<ArchiveEditorDO>{

    /**
     * 创建卷宗编辑
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createArchiveEditor(@Valid ArchiveEditorSaveReqVO createReqVO);

    /**
     * 更新卷宗编辑
     *
     * @param updateReqVO 更新信息
     */
    void updateArchiveEditor(@Valid ArchiveEditorSaveReqVO updateReqVO);

    /**
     * 删除卷宗编辑
     *
     * @param id 编号
     */
    void deleteArchiveEditor(String id);

    /**
     * 获得卷宗编辑
     *
     * @param id 编号
     * @return 卷宗编辑
     */
    ArchiveEditorDO getArchiveEditor(String id);

    /**
    * 获得卷宗编辑分页
    *
    * @param pageReqVO 分页查询
    * @return 卷宗编辑分页
    */
    PageResult<ArchiveEditorDO> getArchiveEditorPage(ArchiveEditorPageReqVO pageReqVO);

    /**
    * 获得卷宗编辑列表
    *
    * @param listReqVO 查询条件
    * @return 卷宗编辑列表
    */
    List<ArchiveEditorDO> getArchiveEditorList(ArchiveEditorListReqVO listReqVO);


}
