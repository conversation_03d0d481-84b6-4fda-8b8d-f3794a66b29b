package com.rs.module.dam.controller.admin.ocr;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.dam.constant.CheckFormatEnum;
import com.rs.module.dam.ocr.handler.OcrHandler;
import com.rs.module.dam.ocr.strategy.OcrStrategy;
import com.rs.module.dam.ocr.strategy.OcrStrategyFactory;
import com.rs.module.dam.ocr.util.OcrUtil;
import com.rs.module.dam.ocr.vo.OcrRateProgress;
import com.rs.module.dam.service.ocr.TFormatCheckService;
import com.rs.module.dam.util.PdfGenerateUtil;
import com.rs.module.dam.vo.ocr.CheckFormatParam;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Example;
import io.swagger.annotations.ExampleProperty;
import lombok.extern.slf4j.Slf4j;

/**
 * 卷宗OCR处理Controller
 * <AUTHOR>
 * @date 2025年4月28日
 */
@RestController
@Slf4j
@RequestMapping(value = "/dam/ocr/handler")
@Api(tags = "ocr处理 - ocr处理接口")
public class DamOcrController {
	
	@Autowired
	TFormatCheckService tFormatCheckService;

	@Autowired
	OcrHandler ocrHandler;
	
	@GetMapping(value = "/startZnbm")
	@ApiOperation(value = "启动OCR智能编目")
	@ApiImplicitParam(name = "jgrybm", value = "监管人员编码")
	public CommonResult<?> startZnbm(@RequestParam("jgrybm") String jgrybm) {
		try {
			if(StringUtil.isEmpty(jgrybm)) {
				return CommonResult.error("监管人员编码为空");
			}
			
			SessionUser user = SessionUserUtil.getSessionUser();
			return ocrHandler.startZnbm(jgrybm, user.getIdCard(), user.getOrgCode(), null);
		}
		catch(Exception e) {
			log.error("ocr处理-智能编目启动异常：{}", e);
			return CommonResult.error(e.getMessage());
		}
	}
	
	@GetMapping(value = "/startKszjZnbm")
	@ApiOperation(value = "启动OCR快速组卷智能编目")
	@ApiImplicitParam(name = "jgrybm", value = "监管人员编码")
	public CommonResult<?> startKszjZnbm(@RequestParam("jgrybm") String jgrybm) {
		try {
			if(StringUtil.isEmpty(jgrybm)) {
				return CommonResult.error("监管人员编码为空");
			}
			
			SessionUser user = SessionUserUtil.getSessionUser();
			return ocrHandler.startKszjZnbm(jgrybm, user.getIdCard(), user.getOrgCode());
		}
		catch(Exception e) {
			log.error("ocr处理-智能编目启动异常：{}", e);
			return CommonResult.error(e.getMessage());
		}
	}
	
	@PostMapping(value = "/callback")
	@ApiOperation(value = "OCR回调(供isp调用)")
	@ApiImplicitParam(name = "jsonParams", value = "Json格式参数")
	public CommonResult<?> ocrCallback(@RequestBody JSONObject jsonParams) {
		try {
			if(jsonParams != null) {
				OcrStrategy ocrStrategy = OcrStrategyFactory.getOcrStrategy(OcrUtil.getOcrType());
				ocrStrategy.callback(jsonParams);
				return CommonResult.success("ocr处理-回调处理成功");
			}
			else {
				String message = "ocr处理-回调处理发生异常：无法解析回调数据";
				log.error(message);
				return CommonResult.error(message);
			}
		}
		catch(Exception e) {
			log.error("ocr处理-回调处理发生异常：{}", e.getMessage());
			return CommonResult.error(e.getMessage());
		}
	}
	
	@GetMapping(value = "/getBmRateProgress")
	@ApiOperation(value = "获取OCR编目进度详情")
	@ApiImplicitParam(name = "jgrybm", value = "监管人员编码")
	public CommonResult<?> getBmRateProgress(@RequestParam("jgrybm") String jgrybm) {
		try {
			OcrStrategy ocrStrategy = OcrStrategyFactory.getOcrStrategy(OcrUtil.getOcrType());
			OcrRateProgress ocrRateProgress = ocrStrategy.getBmRateProgress(jgrybm);
			if(ocrRateProgress != null) {
				return CommonResult.success(JSONUtil.createObj().set("data", ocrRateProgress));
			}
			else {
				return CommonResult.error("没有正在识别中的任务");
			}
		}
		catch(Exception e) {
			log.error("ocr处理-智能编目获取编目进度详情：{}", e);
			return CommonResult.error(e.getMessage());
		}
	}
	
	/**
	 * 启动格式化审查
	 * @param formatParam CheckFormatParam 格式化审查参数
	 *   {jgrybm} 监管人员编码
	 *   {checkType} 检测类型, 0:未编目任务, 1:已编目任务, 2:智能阅卷任务
	 *   {seal} 是否签章识别, 0:否, 1:是
	 *   {signature} 是否签名识别, 0:否, 1:是
	 *   {fingerprint} 是否捺印识别, 0:否, 1:是
	 *   {infoExtract} 是否开启文件信息提取, 0:否, 1:是
	 *   {blankSpace} 是否开启空白页检测, 0:否, 1:是
	 *   {repeatPage} 是否开启重复页检测, 0:否, 1:是
	 *   {flaw} 是否开启瑕疵页检测, 0:否, 1:是
	 * @param bindingResult BindingResult 参数验证结果
	 */
	@PostMapping(value = "/startFormatCheck")
	@ApiOperation(value = "启动格式化审查")
	@ApiImplicitParam(name = "formatParam", value = "格式化审查参数", examples = @Example(
			@ExampleProperty(
					value="{jgrybm:监管人员编码, checkType:检测类型(0:未编目任务, 1:已编目任务, 2:智能阅卷任务),"
							+ "seal:0|1, signature:0|1, fingerprint:0|1,infoExtract:0|1,"
							+ "blankSpace:0|1,repeatPage:0|1,flaw:0|1}",
					mediaType = "application/json"
			)
		)
	)
	public CommonResult<?> startFormatCheck(@RequestBody @Validated CheckFormatParam formatParam, BindingResult bindingResult) {
		try {
			//参数基础验证
			if(bindingResult.hasErrors()) {
				List<ObjectError> allErrors = bindingResult.getAllErrors();
				StringBuilder sb = new StringBuilder();
				for(ObjectError error : allErrors) {
					sb.append(error.getDefaultMessage());
					sb.append(",");
				}
				return CommonResult.error(sb.toString());
			}
			
			//检查是否选择了格式化审查的内容
			if(!checkFormatCheckChoose(formatParam)) {
				return CommonResult.error("请选择要检测的项目");
			}
			
			//开始启动格式化审查
			CheckFormatEnum checkFormatResult = ocrHandler.startFormatCheck(formatParam);
			if(CheckFormatEnum.START_SUCCESS.equals(checkFormatResult)) {
				return CommonResult.success(JSONUtil.createObj().set("msg", checkFormatResult.getDesc()));
			}
			else {
				return CommonResult.error(checkFormatResult.getDesc());
			}
		}
		catch(Exception e) {
			log.error("ocr处理-格式化审查请求失败：{}", e);
			return CommonResult.error(e.getMessage());
		}
	}
	
	/**
	 * 检查是否选择了格式化审查的内容
	 * @param formatParam CheckFormatParam 格式化审查参数
	 * @return boolean
	 */
    private boolean checkFormatCheckChoose(CheckFormatParam formatParam){
        if (("0").equals(formatParam.getSeal()) && ("0").equals(formatParam.getSignature())
            && ("0").equals(formatParam.getFingerprint()) && ("0").equals(formatParam.getInfoExtract())
            && ("0").equals(formatParam.getBlankSpace()) && ("0").equals(formatParam.getRepeatPage())
            && ("0").equals(formatParam.getFlaw())){
            return false;
        }
        return true;
    }
    
    @ApiOperation(value = "图片瑕疵页处理")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "materialId", value = "材料Id"),
		@ApiImplicitParam(name = "fileId", value = "文件Id")
	})
    @PostMapping(value = "/improveImageQuality")
    public CommonResult<?> improveImageQuality(@RequestParam("materialId") String materialId,
                                 @RequestParam("fileId") String fileId) {
    	try {
    		return tFormatCheckService.improveImageQuality(materialId, fileId);
    	}
    	catch(Exception e) {
    		log.error("图片瑕疵页矫正失败：{}", e.getMessage());
    		return CommonResult.error("图片瑕疵页矫正失败，原因：" + e.getMessage());
    	}
    }
    
	@ApiOperation(value = "重新发起格式化审查")
    @GetMapping(value = "/reFormatCheck")
    @ApiImplicitParam(value = "id", name = "格式化审查子任务Id")
	public CommonResult<?> reFormatCheck(@RequestParam("id") String id) {
		try {
			CheckFormatEnum checkFormatEnum = ocrHandler.reFormatCheck(id);
			
			//发起成功
			if(CheckFormatEnum.START_SUCCESS.equals(checkFormatEnum)) {
				return CommonResult.success(JSONUtil.createObj().set("msg", checkFormatEnum.getDesc()));
			}
			
			return CommonResult.error(checkFormatEnum.getDesc());
		}
		catch(Exception e) {
			return CommonResult.error("重新发起格式化审查失败：" + e.getMessage());
		}
	}
	
	@ApiOperation(value = "使用pdf调用ocr服务")
    @PostMapping(value = "/invokeByPdf")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "serviceCode", value = "ocr服务代码，可参考【isp服务接口说明】", required = true),
		@ApiImplicitParam(name = "pdfUrl", value = "pdf文件地址", required = true),
		@ApiImplicitParam(name = "returnUrl", value = "回调地址，因ocr服务均为异步调用需回调处理，回调接口需放开权限拦截", required = true),
		@ApiImplicitParam(name = "jobId", value = "任务Id，可使用业务主键，可为空"),
		@ApiImplicitParam(name = "jgrybm", value = "监管人员编码，可为空"),
		@ApiImplicitParam(name = "extendParams", value = "扩展参数，JSON对象格式字符串，可参考【isp服务接口说明】")
	})    
	public CommonResult<?> invokeByPdf(@RequestParam("serviceCode") String serviceCode,
			@RequestParam("pdfUrl") String pdfUrl,
			@RequestParam("returnUrl") String returnUrl,
			@RequestParam(value = "jobId", required = false) String jobId,
			@RequestParam(value = "jgrybm", required = false) String jgrybm,
			@RequestParam(value = "extendParams", required = false) String extendParams) {
		try {
			//文件数据转换
			JSONArray fileDataArr = PdfGenerateUtil.getFileDataFromPdf(pdfUrl);
			
			//扩展参数转换
			JSONObject extendParamsJson = new JSONObject();
			if(StringUtil.isNotEmpty(extendParams)) {
				extendParamsJson = JSONObject.parseObject(extendParams);
			}
			
			//任务Id处理
			if(StringUtil.isEmpty(jobId)) {
				jobId = StringUtil.getGuid32();
			}
			
			//调用ocr服务
			CommonResult<?> result = ocrHandler.call(serviceCode, fileDataArr, returnUrl, jobId, jgrybm, extendParamsJson);
			JSONObject data = (JSONObject)result.getData();
			data.put("fileData", fileDataArr);
			
			return result;
		}
		catch(Exception e) {
			return CommonResult.error("调用ocr服务！异常信息：" + e.getMessage());
		}
	}
	
	@ApiOperation(value = "使用图片调用ocr服务")
    @PostMapping(value = "/invokeByImage")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "serviceCode", value = "ocr服务代码，可参考【isp服务接口说明】", required = true),
		@ApiImplicitParam(name = "fileData", value = "图片文件数据，JSON数组格式字符串，包括fileId和fileUrl属性，可参考【isp服务接口说明】", 
			required = true, example = "[{\"fileId\":\"1929853661643804672\",\"fileUrl\":\"http://***:9010/dam/1929853493074726912_1929853638713544704_0.jpg\"}]"),
		@ApiImplicitParam(name = "returnUrl", value = "回调地址，因ocr服务均为异步调用需回调处理，回调接口需放开权限拦截", required = true),
		@ApiImplicitParam(name = "jobId", value = "任务Id，可使用业务主键，可为空"),
		@ApiImplicitParam(name = "jgrybm", value = "监管人员编码，可为空"),
		@ApiImplicitParam(name = "extendParams", value = "扩展参数，JSON对象格式字符串，可参考【isp服务接口说明】")
	})    
	public CommonResult<?> invokeByImage(@RequestParam("serviceCode") String serviceCode,
			@RequestParam("fileData") String fileData,
			@RequestParam("returnUrl") String returnUrl,
			@RequestParam(value = "jobId", required = false) String jobId,
			@RequestParam(value = "jgrybm", required = false) String jgrybm,
			@RequestParam(value = "extendParams", required = false) String extendParams) {
		try {
			//文件数据转换
			JSONArray fileDataArr = JSONArray.parseArray(fileData);
			
			//扩展参数转换
			JSONObject extendParamsJson = new JSONObject();
			if(StringUtil.isNotEmpty(extendParams)) {
				extendParamsJson = JSONObject.parseObject(extendParams);
			}
			
			//任务Id处理
			if(StringUtil.isEmpty(jobId)) {
				jobId = StringUtil.getGuid32();
			}
			
			//调用ocr服务
			CommonResult<?> result = ocrHandler.call(serviceCode, fileDataArr, returnUrl, jobId, jgrybm, extendParamsJson);
			
			return result;
		}
		catch(Exception e) {
			return CommonResult.error("调用ocr服务！异常信息：" + e.getMessage());
		}
	}
}
