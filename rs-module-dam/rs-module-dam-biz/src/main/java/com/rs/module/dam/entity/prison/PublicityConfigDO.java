package com.rs.module.dam.entity.prison;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 一所一档-监所档案宣传报道配置 DO
 *
 * <AUTHOR>
 */
@TableName("dam_prison_publicity_config")
@KeySequence("dam_prison_publicity_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PublicityConfigDO extends BaseDO {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     *  报道标题
     */
    private String publicityTitle;
    /**
     * 报道时间
     */
    private Date publicityTime;
    /**
     * 报道内容
     */
    private String publicityContent;

}
