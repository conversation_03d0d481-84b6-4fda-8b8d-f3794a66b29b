package com.rs.module.dam.entity.event;

import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.annotation.GetFile;
import com.rs.framework.annotation.SaveFile;
import com.rs.framework.mybatis.entity.BaseDO;
import com.rs.module.oss.entity.FileDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.dromara.x.file.storage.core.FileInfo;

import java.util.List;

/**
 * 一事一档-事件档案调查记录 DO
 *
 * <AUTHOR>
 */
@TableName("dam_event_invest_record")
@KeySequence("dam_event_invest_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvestRecordDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 事件ID
     */
    private String eventId;
    /**
     * 事件编号(规则：S+9位单位编号+8位日期+6位序号)
     */
    private String eventCode;
    /**
     * 调查详情
     */
    private String investDetails;
    /**
     * 上传附件存储路径
     */
    private String attachmentPath;

    @TableField(exist = false)
    @GetFile("id")
    private List<FileInfo> fileList;

}
