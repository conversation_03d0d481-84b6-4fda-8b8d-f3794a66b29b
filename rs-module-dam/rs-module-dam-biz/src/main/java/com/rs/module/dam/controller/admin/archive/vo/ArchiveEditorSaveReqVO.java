package com.rs.module.dam.controller.admin.archive.vo;
import javax.validation.constraints.NotEmpty;

import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 卷宗编辑新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = false)
public class ArchiveEditorSaveReqVO extends BaseVO{
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("卷宗ID(主卷/分卷ID)")
    private String jzid;

}
