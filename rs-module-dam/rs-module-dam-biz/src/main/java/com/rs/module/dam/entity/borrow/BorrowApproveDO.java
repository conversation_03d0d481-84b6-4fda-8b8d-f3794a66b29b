package com.rs.module.dam.entity.borrow;

import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO_;
import lombok.*;

/**
 * 借阅审批 DO
 *
 * <AUTHOR>
 */
@TableName("dam_borrow_approve")
@KeySequence("dam_borrow_approve_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BorrowApproveDO extends BaseDO_ {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 借阅Id
     */
    private String borrowId;
    /**
     * 审批状态(0:发起提交,1:待审批,2:审批通过,3:部分审批通过,4:审批不通过)
     */
    private String approveStatus;
    /**
     * 审批顺序
     */
    private Integer orderId;
    /**
     * 审批角色代码
     */
    private String approveRoleCode;
    /**
     * 审批角色名称
     */
    private String approveRoleName;


    /**
     * 审批状态(0:发起提交,1:待审批,2:审批通过,3:部分审批通过,4:审批不通过)
     */
    @TableField(exist = false)
    private String approveStatusName;
}
