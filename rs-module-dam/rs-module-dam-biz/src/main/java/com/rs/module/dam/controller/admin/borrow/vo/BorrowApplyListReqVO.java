package com.rs.module.dam.controller.admin.borrow.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "管理后台 - 借阅申请列表 Request VO")
@Data
public class BorrowApplyListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("申请单号")
    private String applyNo;

    @ApiModelProperty("借阅类型(0:内部借阅,1:外部借阅)")
    private String type;

    @ApiModelProperty("借阅单位编号")
    private String borrowOrgCode;

    @ApiModelProperty("借阅单位名称")
    private String borrowOrgName;

    @ApiModelProperty("借阅人")
    private String borrowUser;

    @ApiModelProperty("借阅人姓名")
    private String borrowUserName;

    @ApiModelProperty("申请理由")
    private String borrowReason;

    @ApiModelProperty("借阅天数")
    private Integer borrowDays;

    @ApiModelProperty("借阅截止时间")
    private Date[] borrowEndDate;

    @ApiModelProperty("借阅卷宗数量")
    private Integer borrowNum;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("附件")
    private String attachment;

    @ApiModelProperty("审批状态(0:待审批,1:审批中,2:审批通过,3:部分审批通过,4:审批不通过)")
    private String approveStatus;

    @ApiModelProperty("下级审批角色")
    private String nextApproveRole;

}
