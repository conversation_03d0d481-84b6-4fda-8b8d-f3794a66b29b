package com.rs.module.dam.controller.admin.material;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.entity.material.MaterialLabelHistoryDO;
import com.rs.module.dam.service.material.MaterialLabelHistoryService;
import com.rs.module.dam.vo.material.MaterialLabelHistoryListReqVO;
import com.rs.module.dam.vo.material.MaterialLabelHistoryPageReqVO;
import com.rs.module.dam.vo.material.MaterialLabelHistoryRespVO;
import com.rs.module.dam.vo.material.MaterialLabelHistorySaveReqVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

@Api(tags = "材料处理 - 卷宗材料标签历史")
@ApiIgnore
@RestController
@RequestMapping("/dam/material/materialLabelHistory")
@Validated
public class MaterialLabelHistoryController {

    @Resource
    private MaterialLabelHistoryService materialLabelHistoryService;

    @PostMapping("/create")
    @ApiOperation(value = "创建卷宗材料标签历史")
    @LogRecordAnnotation(bizModule = "dam:materialLabelHistory:create", operateType = LogOperateType.CREATE, title = "创建卷宗材料标签历史",
    success = "创建卷宗材料标签历史成功", fail = "创建卷宗材料标签历史失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createMaterialLabelHistory(@Valid @RequestBody MaterialLabelHistorySaveReqVO createReqVO) {
        return success(materialLabelHistoryService.createMaterialLabelHistory(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新卷宗材料标签历史")
    @LogRecordAnnotation(bizModule = "dam:materialLabelHistory:update", operateType = LogOperateType.UPDATE, title = "更新卷宗材料标签历史",
    success = "更新卷宗材料标签历史成功", fail = "更新卷宗材料标签历史失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateMaterialLabelHistory(@Valid @RequestBody MaterialLabelHistorySaveReqVO updateReqVO) {
        materialLabelHistoryService.updateMaterialLabelHistory(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除卷宗材料标签历史")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:materialLabelHistory:delete", operateType = LogOperateType.DELETE, title = "删除卷宗材料标签历史",
    success = "删除卷宗材料标签历史成功", fail = "删除卷宗材料标签历史失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteMaterialLabelHistory(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           materialLabelHistoryService.deleteMaterialLabelHistory(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得卷宗材料标签历史")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<MaterialLabelHistoryRespVO> getMaterialLabelHistory(@RequestParam("id") String id) {
        MaterialLabelHistoryDO materialLabelHistory = materialLabelHistoryService.getMaterialLabelHistory(id);
        return success(BeanUtils.toBean(materialLabelHistory, MaterialLabelHistoryRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得卷宗材料标签历史分页")
    public CommonResult<PageResult<MaterialLabelHistoryRespVO>> getMaterialLabelHistoryPage(@Valid @RequestBody MaterialLabelHistoryPageReqVO pageReqVO) {
        PageResult<MaterialLabelHistoryDO> pageResult = materialLabelHistoryService.getMaterialLabelHistoryPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MaterialLabelHistoryRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得卷宗材料标签历史列表")
    public CommonResult<List<MaterialLabelHistoryRespVO>> getMaterialLabelHistoryList(@Valid @RequestBody MaterialLabelHistoryListReqVO listReqVO) {
        List<MaterialLabelHistoryDO> list = materialLabelHistoryService.getMaterialLabelHistoryList(listReqVO);
        return success(BeanUtils.toBean(list, MaterialLabelHistoryRespVO.class));
    }
}
