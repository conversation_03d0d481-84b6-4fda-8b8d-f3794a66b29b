package com.rs.module.dam.controller.admin.event.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 一事一档-事件档案 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)

public class ArchivePrisonerRespVO extends BaseVO implements TransPojo {
private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("事件ID")
    private String eventId;
    @ApiModelProperty("事件编号(规则：S+9位单位编号+8位日期+6位序号)")
    private String eventCode;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("监管人员编码")
    private String rybh;
    @ApiModelProperty("监管人员姓名")
    private String xm;
    @ApiModelProperty("性别")
    private String xb;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("涉嫌罪名")
    private String sxzm;
    @ApiModelProperty("诉讼环节")
    private String sshj;
    @ApiModelProperty("风险等级")
    private String fxdj;

}
