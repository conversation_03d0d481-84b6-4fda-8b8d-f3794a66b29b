package com.rs.module.dam.controller.admin.borrow;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.dto.OrgUserRespDTO;
import com.rs.adapter.bsp.api.dto.RoleRespDTO;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.cons.ApproveFlowNode;
import com.rs.module.dam.cons.CommonConstants;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApplyListReqVO;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApplyPageReqVO;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApplyRespVO;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApplySaveReqVO;
import com.rs.module.dam.entity.borrow.BorrowApplyDO;
import com.rs.module.dam.entity.borrow.BorrowPrisonerDO;
import com.rs.module.dam.service.borrow.BorrowApplyService;
import com.rs.module.dam.util.ApproveUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "卷宗借阅 - 借阅申请")
@RestController
@RequestMapping("/dam/borrow/borrowApply")
@Validated
public class BorrowApplyController {

    @Resource
    private BorrowApplyService borrowApplyService;

    @Resource
    private BspApi bspApi;

    @Value("${system-mark}")
    private String systemMark;

    @PostMapping("/create")
    @ApiOperation(value = "创建借阅申请")
    @LogRecordAnnotation(bizModule = "dam:borrowApply:create", operateType = LogOperateType.CREATE, title = "创建借阅申请",
    success = "创建借阅申请成功", fail = "创建借阅申请失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createBorrowApply(@Valid @RequestBody BorrowApplySaveReqVO createReqVO) {
        ApproveFlowNode approveNextNode = ApproveUtil.getFirstNode(createReqVO.getType());
        createReqVO.setApplyNo(bspApi.executeByRuleCode(CommonConstants.BORROW_APPLY_NO_RULE, null));
        createReqVO.setNextApproveRole(approveNextNode.getRoleCode());
        BorrowApplyDO borrowApplyDO = borrowApplyService.createBorrowApply(createReqVO);
        sendTodo(approveNextNode.getRoleCode(), borrowApplyDO, createReqVO.getBorrowPrisoners());
        return success(borrowApplyDO.getId());
    }

    @GetMapping("/reapply")
    @ApiOperation(value = "获取再次申请档案详情")
    @LogRecordAnnotation(bizModule = "dam:borrowApply:create", operateType = LogOperateType.QUERY, title = "获取再次申请档案详情",
            success = "获取再次申请档案详情成功", fail = "获取再次申请档案详情失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Map<String, Object>> getReapplyDetail(@RequestParam("prisonerId") String prisonerId) {
        return success(borrowApplyService.getReapplyDetail(prisonerId));
    }

    /**
     * 给相同机构指定角色发送借阅申请待办消息
     * @param borrowApplyDO 借阅信息
     * @param roleCode 当前节点角色编号
     */
    private void sendTodo(String roleCode, BorrowApplyDO borrowApplyDO, List<BorrowPrisonerDO> prisonerList) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        // 给下级确认人发送待办消息
        RoleRespDTO roleByCode = bspApi.getRoleByCode(roleCode);
        List<OrgUserRespDTO> userList = bspApi.getUserByOrgAndRole(sessionUser.getOrgCode(), roleByCode.getId());
        List<ReceiveUser> receiveUserList = new ArrayList<>();
        for (OrgUserRespDTO userDO : userList) {
            receiveUserList.add(new ReceiveUser(userDO.getIdCard(), userDO.getOrgCode()));
        }
        if (CollUtil.isNotEmpty(receiveUserList)) {
            String title = "借阅审核";
            String content = "%s%s申请借阅%s的档案，待您审批，请及时处理！";
            String person = "";
            for (BorrowPrisonerDO prisoner : prisonerList) {
                person += String.format("【%s%s】、", prisoner.getRoomName(), prisoner.getJgryxm());
            }
            if (person != null && person.endsWith("、")) {
                person = person.substring(0, person.length() - 1);
            }
            content = String.format(content, borrowApplyDO.getOrgName(), borrowApplyDO.getAddUserName(), person);
            String url = "/#/jzgl/jysp/sp?id=" + borrowApplyDO.getId();
            SendMessageUtil.sendTodoMsg(title, content, url, systemMark, sessionUser.getIdCard(), sessionUser.getName(),
                    sessionUser.getOrgCode(), sessionUser.getOrgName(), null, borrowApplyDO.getId(),
                    "pc", borrowApplyDO.getId(), receiveUserList, systemMark,
                    MsgBusTypeEnum.JZ_JYSH.getCode(), null);
        }
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新借阅申请", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:borrowApply:update", operateType = LogOperateType.UPDATE, title = "更新借阅申请",
    success = "更新借阅申请成功", fail = "更新借阅申请失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateBorrowApply(@Valid @RequestBody BorrowApplySaveReqVO updateReqVO) {
        borrowApplyService.updateBorrowApply(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除借阅申请", hidden = true)
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:borrowApply:delete", operateType = LogOperateType.DELETE, title = "删除借阅申请",
    success = "删除借阅申请成功", fail = "删除借阅申请失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteBorrowApply(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           borrowApplyService.deleteBorrowApply(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得借阅申请")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:borrowApply:get", operateType = LogOperateType.QUERY, title = "获取借阅申请",
            bizNo = "{{#id}}", success = "获取借阅申请成功", fail = "获取借阅申请失败", extraInfo = "{{#id}}")
    public CommonResult<BorrowApplyRespVO> getBorrowApply(@RequestParam("id") String id) {
        BorrowApplyDO borrowApply = borrowApplyService.getBorrowApply(id);
        return success(BeanUtils.toBean(borrowApply, BorrowApplyRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得借阅申请分页", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:borrowApply:page", operateType = LogOperateType.QUERY, title = "获得借阅申请分页",
    success = "获得借阅申请分页成功", fail = "获得借阅申请分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<BorrowApplyRespVO>> getBorrowApplyPage(@Valid @RequestBody BorrowApplyPageReqVO pageReqVO) {
        PageResult<BorrowApplyDO> pageResult = borrowApplyService.getBorrowApplyPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BorrowApplyRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得借阅申请列表", hidden = true)
    @LogRecordAnnotation(bizModule = "dam:borrowApply:list", operateType = LogOperateType.QUERY, title = "获得借阅申请列表",
    success = "获得借阅申请列表成功", fail = "获得借阅申请列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<BorrowApplyRespVO>> getBorrowApplyList(@Valid @RequestBody BorrowApplyListReqVO listReqVO) {
    List<BorrowApplyDO> list = borrowApplyService.getBorrowApplyList(listReqVO);
        return success(BeanUtils.toBean(list, BorrowApplyRespVO.class));
    }

    // ==================== 子表（借阅人员档案关联） ====================

    @GetMapping("/borrow-prisoner/list-by-borrow-id")
    @ApiOperation(value = "获得借阅人员档案关联列表")
    @ApiImplicitParam(name = "borrowId", value = "借阅Id")
    public CommonResult<List<BorrowPrisonerDO>> getBorrowPrisonerListByBorrowId(@RequestParam("borrowId") String borrowId) {
        return success(borrowApplyService.getBorrowPrisonerListByBorrowId(borrowId));
    }

    @PostMapping("/approve")
    @ApiOperation(value = "审批借阅")
    @LogRecordAnnotation(bizModule = "dam:borrowApply:list", operateType = LogOperateType.UPDATE, title = "审批借阅",
            success = "审批借阅成功", fail = "审批借阅失败", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<Boolean> approve(@Valid @RequestBody JSONObject params) {
        JSONArray jsonArray = params.getJSONArray("approveList");
        List<BorrowPrisonerDO> list = JSONObject.parseArray(JSONObject.toJSONString(jsonArray), BorrowPrisonerDO.class);
        BorrowApplyDO borrowApply = borrowApplyService.getById(params.getString("borrowId"));
        borrowApplyService.approve(borrowApply, list);
        // 发送待办消息
        ApproveFlowNode approveNextNode = ApproveUtil.getApproveNextNode(borrowApply.getNextApproveRole(), borrowApply.getType());
        long count = list.stream().filter(borrowPrisonerDO -> CommonConstants.PRISONER_APPROVE_STATUS_FAILED
                .equals(borrowPrisonerDO.getApproveStatus())).count();
        if (approveNextNode != null && count != list.size())
            sendTodo(approveNextNode.getRoleCode(), borrowApply, list);

        return success(true);
    }

    @GetMapping("/reason")
    @ApiOperation(value = "借阅卷宗权限回收")
    @LogRecordAnnotation(bizModule = "dam:borrowApply:list", operateType = LogOperateType.UPDATE, title = "借阅卷宗权限回收",
            success = "借阅卷宗权限回收成功", fail = "借阅卷宗权限回收失败", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<Boolean> reason(@RequestParam(value = "id") String id,
                                        @RequestParam(value = "reason") String reason) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        BorrowPrisonerDO borrowPrisonerDO = new BorrowPrisonerDO();
        borrowPrisonerDO.setId(id);
        borrowPrisonerDO.setBorrowStatus(CommonConstants.BORROW_STATUS_RECYCLE);
        borrowPrisonerDO.setRecycleReason(reason);
        borrowPrisonerDO.setRecycleTime(new Date());
        borrowPrisonerDO.setRecycleOrgCode(sessionUser.getOrgCode());
        borrowPrisonerDO.setRecycleOrgName(sessionUser.getOrgName());
        borrowPrisonerDO.setRecycleUser(sessionUser.getIdCard());
        borrowPrisonerDO.setRecycleUserName(sessionUser.getName());
        borrowApplyService.updateBorrowPrisonerById(borrowPrisonerDO);
        return success(true);
    }

}
