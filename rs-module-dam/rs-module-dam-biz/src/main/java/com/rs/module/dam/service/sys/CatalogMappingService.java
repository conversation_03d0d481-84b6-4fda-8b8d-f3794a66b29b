package com.rs.module.dam.service.sys;

import java.util.*;
import javax.validation.*;
import com.rs.module.dam.controller.admin.sys.vo.*;
import com.rs.module.dam.entity.sys.CatalogMappingDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 目录文书映射 Service 接口
 *
 * <AUTHOR>
 */
public interface CatalogMappingService extends IBaseService<CatalogMappingDO>{

    /**
     * 创建目录文书映射
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCatalogMapping(@Valid CatalogMappingSaveReqVO createReqVO);

    /**
     * 创建目录文书映射
     *
     * @param list 创建信息
     * @return
     */
    void createBatch(List<CatalogMappingDO> list);

    /**
     * 更新目录文书映射
     *
     * @param updateReqVO 更新信息
     */
    void updateCatalogMapping(@Valid CatalogMappingSaveReqVO updateReqVO);

    /**
     * 删除目录文书映射
     *
     * @param id 编号
     */
    void deleteCatalogMapping(String id);

    /**
     * 获得目录文书映射
     *
     * @param id 编号
     * @return 目录文书映射
     */
    CatalogMappingDO getCatalogMapping(String id);

    /**
    * 获得目录文书映射分页
    *
    * @param pageReqVO 分页查询
    * @return 目录文书映射分页
    */
    PageResult<CatalogMappingDO> getCatalogMappingPage(CatalogMappingPageReqVO pageReqVO);

    /**
    * 获得目录文书映射列表
    *
    * @param listReqVO 查询条件
    * @return 目录文书映射列表
    */
    List<CatalogMappingDO> getCatalogMappingList(CatalogMappingListReqVO listReqVO);


}
