package com.rs.module.dam.controller.admin.sys.vo;
import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 卷宗目录模板新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CatalogTemplateSaveReqVO extends BaseVO{
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("目录数据")
    private String data;

    @ApiModelProperty("类型（1：标准  0：自定义模板）")
    private String type;

    @ApiModelProperty("标准模板ID")
    private String templateId;

}
