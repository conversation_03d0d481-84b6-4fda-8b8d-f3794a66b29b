package com.rs.module.dam.controller.admin.archive.vo;

import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 卷宗编辑列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = false)
public class ArchiveEditorListReqVO extends BaseVO {
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("卷宗ID(主卷/分卷ID)")
    private String jzid;

}
