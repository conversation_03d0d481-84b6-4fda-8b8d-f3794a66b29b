package com.rs.module.dam.dao.prison;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.controller.admin.prison.vo.RecognitionConfigListReqVO;
import com.rs.module.dam.controller.admin.prison.vo.RecognitionConfigPageReqVO;
import com.rs.module.dam.entity.prison.RecognitionConfigDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 一所一档-监所档案荣誉表彰配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface RecognitionConfigDao extends IBaseDao<RecognitionConfigDO> {


    default PageResult<RecognitionConfigDO> selectPage(RecognitionConfigPageReqVO reqVO) {
        Page<RecognitionConfigDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<RecognitionConfigDO> wrapper = new LambdaQueryWrapperX<RecognitionConfigDO>()
            .likeIfPresent(RecognitionConfigDO::getRecognitionName, reqVO.getRecognitionName())
            .betweenIfPresent(RecognitionConfigDO::getRecognitionTime, reqVO.getRecognitionTime());
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(RecognitionConfigDO::getAddTime);
        }
        Page<RecognitionConfigDO> recognitionConfigPage = selectPage(page, wrapper);
        return new PageResult<>(recognitionConfigPage.getRecords(), recognitionConfigPage.getTotal());
    }
    default List<RecognitionConfigDO> selectList(RecognitionConfigListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<RecognitionConfigDO>()
            .likeIfPresent(RecognitionConfigDO::getRecognitionName, reqVO.getRecognitionName())
            .betweenIfPresent(RecognitionConfigDO::getRecognitionTime, reqVO.getRecognitionTime())
        .orderByDesc(RecognitionConfigDO::getAddTime));
    }

}
