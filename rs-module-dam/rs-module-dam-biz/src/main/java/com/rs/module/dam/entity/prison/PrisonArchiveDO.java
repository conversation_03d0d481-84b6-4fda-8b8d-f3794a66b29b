package com.rs.module.dam.entity.prison;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;

/**
 * 一所一档-监所档案 DO
 *
 * <AUTHOR>
 */
@TableName("dam_prison_archive")
@KeySequence("dam_prison_archive_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrisonArchiveDO extends BaseDO {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监所的名称
     */
    private String prisonName;
    /**
     * 监所单位机构代码
     */
    private String prisonCode;
    /**
     * 监所联系电话
     */
    private String contactPhone;
    /**
     * 电子邮件地址
     */
    private String email;
    /**
     * 邮政编码
     */
    private String postCode;
    /**
     * 监所地址
     */
    private String address;
    /**
     * 对监所简介
     */
    private String remarks;
    /**
     * 监所背景图片存储地址
     */
    private String backgroundImageUrl;
    /**
     * 监所的建筑总面积
     */
    private BigDecimal buildingArea;
    /**
     * 监室数量
     */
    private Integer monitoringRoomCount;
    /**
     * 监室总可容纳人数
     */
    private Integer monitoringRoomCapacity;
    /**
     * 监所内功能室数量
     */
    private Integer functionRoomCount;
    /**
     * 功能室配置情况
     */
    private String functionRoomConfiguration;
    /**
     * 装备配备情况
     */
    private String equipmentConfiguration;
    /**
     * 装备数量
     */
    private Integer equipmentCount;
    /**
     * 警力总人数
     */
    private Integer totalPoliceForce;
    /**
     * 民警总数量
     */
    private Integer policeOfficerCount;
    /**
     * 辅警总数量
     */
    private Integer auxiliaryPoliceCount;
    /**
     * 累计羁押人数
     */
    private Integer totalDetainedCount;
    /**
     * 累计办理案件数量
     */
    private Integer totalCaseCount;
    /**
     * 累计讯 / 询问嫌疑人数
     */
    private Integer totalInterrogatedSuspectCount;
    /**
     * 累计管理涉案财物件数
     */
    private Integer totalSeizedPropertyCount;
    /**
     * 累计管理涉案财物总价值
     */
    private BigDecimal totalSeizedPropertyValue;

    /**
     * 评定等级
     */
    private String ratingLevel;
}
