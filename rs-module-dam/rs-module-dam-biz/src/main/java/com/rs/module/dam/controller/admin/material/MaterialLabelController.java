package com.rs.module.dam.controller.admin.material;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.util.StringUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.entity.material.MaterialLabelDO;
import com.rs.module.dam.service.material.MaterialLabelService;
import com.rs.module.dam.vo.material.MaterialLabelListReqVO;
import com.rs.module.dam.vo.material.MaterialLabelPageReqVO;
import com.rs.module.dam.vo.material.MaterialLabelRespVO;
import com.rs.module.dam.vo.material.MaterialLabelSaveReqVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@Api(tags = "材料处理 - 卷宗材料标签")
@RestController
@RequestMapping("/dam/material/materialLabel")
@Validated
public class MaterialLabelController {

    @Resource
    private MaterialLabelService materialLabelService;

    @PostMapping("/create")
    @ApiOperation(value = "创建卷宗材料标签")
    @LogRecordAnnotation(bizModule = "dam:materialLabel:create", operateType = LogOperateType.CREATE, title = "创建卷宗材料标签",
    success = "创建卷宗材料标签成功", fail = "创建卷宗材料标签失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createMaterialLabel(@Valid @RequestBody MaterialLabelSaveReqVO createReqVO) {
        return success(materialLabelService.createMaterialLabel(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新卷宗材料标签")
    @LogRecordAnnotation(bizModule = "dam:materialLabel:update", operateType = LogOperateType.UPDATE, title = "更新卷宗材料标签",
    success = "更新卷宗材料标签成功", fail = "更新卷宗材料标签失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateMaterialLabel(@Valid @RequestBody MaterialLabelSaveReqVO updateReqVO) {
        materialLabelService.updateMaterialLabel(updateReqVO);
        return success(true);
    }
    
    @PostMapping("/save")
    @ApiOperation(value = "保存卷宗材料标签")
    @LogRecordAnnotation(bizModule = "dam:materialLabel:save", operateType = LogOperateType.CREATE, title = "保存卷宗材料标签",
    success = "保存卷宗材料标签成功", fail = "保存卷宗材料标签失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<?> saveMaterialLabel(@Valid @RequestBody MaterialLabelSaveReqVO createReqVO) {
    	if(StringUtil.isNotEmpty(createReqVO.getId())) {
    		materialLabelService.updateMaterialLabel(createReqVO);
            return success(true);
    	}
    	else {
    		return success(materialLabelService.createMaterialLabel(createReqVO));
    	}
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除卷宗材料标签")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:materialLabel:delete", operateType = LogOperateType.DELETE, title = "删除卷宗材料标签",
    success = "删除卷宗材料标签成功", fail = "删除卷宗材料标签失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteMaterialLabel(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           materialLabelService.deleteMaterialLabel(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得卷宗材料标签")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<MaterialLabelRespVO> getMaterialLabel(@RequestParam("id") String id) {
        MaterialLabelDO materialLabel = materialLabelService.getMaterialLabel(id);
        return success(BeanUtils.toBean(materialLabel, MaterialLabelRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得卷宗材料标签分页")
    public CommonResult<PageResult<MaterialLabelRespVO>> getMaterialLabelPage(@Valid @RequestBody MaterialLabelPageReqVO pageReqVO) {
        PageResult<MaterialLabelDO> pageResult = materialLabelService.getMaterialLabelPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MaterialLabelRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得卷宗材料标签列表")
    public CommonResult<List<MaterialLabelRespVO>> getMaterialLabelList(@Valid @RequestBody MaterialLabelListReqVO listReqVO) {
        List<MaterialLabelDO> list = materialLabelService.getMaterialLabelList(listReqVO);
        return success(BeanUtils.toBean(list, MaterialLabelRespVO.class));
    }
    
    @GetMapping("/removeMaterialLabel")
    @ApiOperation(value = "根据材料Id和文件Id删除标签")
    @ApiImplicitParams({
    		@ApiImplicitParam(name = "materialId", value = "材料Id"),
    		@ApiImplicitParam(name = "fileId", value = "文件Id")
    })    
    @LogRecordAnnotation(bizModule = "dam:materialLabel:removeMaterialLabel", operateType = LogOperateType.DELETE, title = "根据材料Id和文件Id删除标签",
    success = "根据材料Id和文件Id删除标签成功", fail = "根据材料Id和文件Id删除标签失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#materialId}}")
    public CommonResult<Boolean> removeMaterialLabel(@RequestParam(value = "materialId", required = true) String materialId,
    		@RequestParam(value = "fileId", required = true) String fileId) {
    	QueryWrapper<MaterialLabelDO> wrapper = new QueryWrapper<>();
    	wrapper.eq("material_id", materialId);
    	wrapper.eq("file_id", fileId);
    	materialLabelService.remove(wrapper);
        return success(true);
    }
}
