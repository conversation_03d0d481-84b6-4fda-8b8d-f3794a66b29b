package com.rs.module.dam.controller.admin.prison.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


@Data
@ApiModel("监室事务全览")
public class RoomArchiveDTO {

    @ApiModelProperty(value = "机构编号",required = true)
    private String orgCode;

    @ApiModelProperty(value = "监室编号",required = true)
    private String roomId;

    @ApiModelProperty(value = "日期",required = true)
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date date;


}
