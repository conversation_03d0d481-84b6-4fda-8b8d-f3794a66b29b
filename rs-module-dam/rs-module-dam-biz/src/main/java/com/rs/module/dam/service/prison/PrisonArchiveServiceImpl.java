package com.rs.module.dam.service.prison;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.dto.OrgRespDTO;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.controller.admin.prison.vo.PrisonArchiveListReqVO;
import com.rs.module.dam.controller.admin.prison.vo.PrisonArchivePageReqVO;
import com.rs.module.dam.controller.admin.prison.vo.PrisonArchiveSaveReqVO;
import com.rs.module.dam.dao.prison.PrisonArchiveDao;
import com.rs.module.dam.entity.prison.PrisonArchiveDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;


/**
 * 一所一档-监所档案 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrisonArchiveServiceImpl extends BaseServiceImpl<PrisonArchiveDao, PrisonArchiveDO> implements PrisonArchiveService {

    @Resource
    private PrisonArchiveDao prisonArchiveDao;
    @Resource
    private BspApi bspApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createArchive(PrisonArchiveSaveReqVO createReqVO) {
        // 插入
        PrisonArchiveDO archive = BeanUtils.toBean(createReqVO, PrisonArchiveDO.class);
        archive.setFunctionRoomConfiguration(JSONObject.toJSONString(createReqVO.getFunctionRoomConfiguration()));
        archive.setEquipmentConfiguration(JSONObject.toJSONString(createReqVO.getEquipmentConfiguration()));
        prisonArchiveDao.insert(archive);
        // 返回
        return archive.getId();
    }

    @Override
    public PrisonArchiveDO createDefaultArchive(String prisonCode) {
        PrisonArchiveDO archive = new PrisonArchiveDO();
        OrgRespDTO org = bspApi.getOrgByCode(prisonCode);
        archive.setPrisonCode(prisonCode);
        archive.setPrisonName(org.getName());
        archive.setContactPhone(org.getOfficeTel());
        archive.setEmail(org.getEmail());
        archive.setAddress(StringUtil.isNullBlank(org.getAddress()) ? "" : org.getAddress());
        archive.setRemarks(StringUtil.isNullBlank(org.getIntro()) ? "" : org.getIntro());
        archive.setBackgroundImageUrl("");
        prisonArchiveDao.insert(archive);
        return archive;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateArchive(PrisonArchiveSaveReqVO updateReqVO) {
        // 校验存在
        validateArchiveExists(updateReqVO.getId());
        // 更新
        PrisonArchiveDO updateObj = BeanUtils.toBean(updateReqVO, PrisonArchiveDO.class);
        updateObj.setFunctionRoomConfiguration(JSONObject.toJSONString(updateReqVO.getFunctionRoomConfiguration()));
        updateObj.setEquipmentConfiguration(JSONObject.toJSONString(updateReqVO.getEquipmentConfiguration()));
        prisonArchiveDao.updateById(updateObj);
        return updateObj.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteArchive(String id) {
        // 校验存在
        validateArchiveExists(id);
        // 删除
        prisonArchiveDao.deleteById(id);

    }

    private void validateArchiveExists(String id) {
        if (prisonArchiveDao.selectById(id) == null) {
            throw new ServerException("一所一档-监所档案数据不存在");
        }
    }

    @Override
    public PrisonArchiveDO getArchive(String id) {
        return prisonArchiveDao.selectById(id);
    }

    @Override
    public PrisonArchiveDO getByPrisonCode(String prisonCode) {
        if (StringUtil.isNullBlank(prisonCode)) {
            return new PrisonArchiveDO();
        }
        return getOne(new LambdaQueryWrapper<PrisonArchiveDO>().eq(PrisonArchiveDO::getPrisonCode, prisonCode), false);
    }

    @Override
    public PageResult<PrisonArchiveDO> getArchivePage(PrisonArchivePageReqVO pageReqVO) {
        return prisonArchiveDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrisonArchiveDO> getArchiveList(PrisonArchiveListReqVO listReqVO) {
        return prisonArchiveDao.selectList(listReqVO);
    }


}
