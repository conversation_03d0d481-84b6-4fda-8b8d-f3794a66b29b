package com.rs.module.dam.service.borrow;

import java.util.*;
import javax.validation.*;
import com.rs.module.dam.controller.admin.borrow.vo.*;
import com.rs.module.dam.entity.borrow.PrisonerApproveDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 借阅人员档案审批 Service 接口
 *
 * <AUTHOR>
 */
public interface PrisonerApproveService extends IBaseService<PrisonerApproveDO>{

    /**
     * 创建借阅人员档案审批
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrisonerApprove(@Valid PrisonerApproveSaveReqVO createReqVO);

    /**
     * 更新借阅人员档案审批
     *
     * @param updateReqVO 更新信息
     */
    void updatePrisonerApprove(@Valid PrisonerApproveSaveReqVO updateReqVO);

    /**
     * 删除借阅人员档案审批
     *
     * @param id 编号
     */
    void deletePrisonerApprove(String id);

    /**
     * 获得借阅人员档案审批
     *
     * @param id 编号
     * @return 借阅人员档案审批
     */
    PrisonerApproveDO getPrisonerApprove(String id);

    /**
    * 获得借阅人员档案审批分页
    *
    * @param pageReqVO 分页查询
    * @return 借阅人员档案审批分页
    */
    PageResult<PrisonerApproveDO> getPrisonerApprovePage(PrisonerApprovePageReqVO pageReqVO);

    /**
    * 获得借阅人员档案审批列表
    *
    * @param listReqVO 查询条件
    * @return 借阅人员档案审批列表
    */
    List<PrisonerApproveDO> getPrisonerApproveList(PrisonerApproveListReqVO listReqVO);


}
