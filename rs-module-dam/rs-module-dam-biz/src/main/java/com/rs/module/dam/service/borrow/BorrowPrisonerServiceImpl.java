package com.rs.module.dam.service.borrow;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.module.dam.dao.borrow.BorrowPrisonerDao;
import com.rs.module.dam.entity.borrow.BorrowPrisonerDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


/**
 * 借阅人员档案关联表 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BorrowPrisonerServiceImpl extends BaseServiceImpl<BorrowPrisonerDao, BorrowPrisonerDO> implements BorrowPrisonerService {

    @Resource
    private BorrowPrisonerDao borrowPrisonerDao;


}
