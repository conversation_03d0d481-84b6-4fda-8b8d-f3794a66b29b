package com.rs.module.dam.controller.admin.event;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.rs.util.DicUtils;
import com.bsp.common.util.StringUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.adapter.bsp.api.BpmApi;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.dto.OrgUserRespDTO;
import com.rs.adapter.bsp.api.dto.RoleRespDTO;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.enums.PrisonerQueryRyztEnum;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.dam.cons.CommonConstants;
import com.rs.module.dam.controller.admin.event.vo.*;
import com.rs.module.dam.entity.event.ArchiveDO;
import com.rs.module.dam.entity.event.ArchivePersonnelDO;
import com.rs.module.dam.entity.event.ArchivePrisonerDO;
import com.rs.module.dam.entity.event.InvestRecordDO;
import com.rs.module.dam.service.event.ArchiveService;
import com.rs.module.dam.service.event.InvestRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "一事一档-事件档案")
@RestController
@RequestMapping("/dam/event/archive")
@Validated
public class ArchiveController {

    @Resource
    private ArchiveService archiveService;
    @Resource
    private InvestRecordService investRecordService;
    @Resource
    private PrisonerService prisonerService;
    @Resource
    private BspApi bspApi;
    @Resource
    private BpmApi bpmApi;
    @Value("${system-mark}")
    private String systemMark;


    @PostMapping("/create")
    @ApiOperation(value = "创建一事一档-事件档案")
    @LogRecordAnnotation(bizModule = "dam:archive:create", operateType = LogOperateType.CREATE, title = "创建一事一档-事件档案",
    success = "创建一事一档-事件档案成功", fail = "创建一事一档-事件档案失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createArchive(@Valid @RequestBody ArchiveSaveReqVO createReqVO) {
        return success(archiveService.createArchive(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新一事一档-事件档案")
    @LogRecordAnnotation(bizModule = "dam:archive:update", operateType = LogOperateType.UPDATE, title = "更新一事一档-事件档案",
    success = "更新一事一档-事件档案成功", fail = "更新一事一档-事件档案失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateArchive(@Valid @RequestBody ArchiveSaveReqVO updateReqVO) {
        archiveService.updateArchive(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除一事一档-事件档案")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:archive:delete", operateType = LogOperateType.DELETE, title = "删除一事一档-事件档案",
    success = "删除一事一档-事件档案成功", fail = "删除一事一档-事件档案失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteArchive(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           archiveService.deleteArchive(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得一事一档-事件档案")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:archive:get", operateType = LogOperateType.UPDATE, title = "获得一事一档-事件档案",
            success = "获得一事一档-事件档案成功", fail = "获得一事一档-事件档案失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<ArchiveRespVO> getArchive(@RequestParam("id") String id) {
        ArchiveDO archive = archiveService.getArchive(id);
        return success(BeanUtils.toBean(archive, ArchiveRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得一事一档-事件档案分页", hidden = true)
    public CommonResult<PageResult<ArchiveRespVO>> getArchivePage(@Valid @RequestBody ArchivePageReqVO pageReqVO) {
        PageResult<ArchiveDO> pageResult = archiveService.getArchivePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ArchiveRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得一事一档-事件档案列表", hidden = true)
    public CommonResult<List<ArchiveRespVO>> getArchiveList(@Valid @RequestBody ArchiveListReqVO listReqVO) {
        List<ArchiveDO> list = archiveService.getArchiveList(listReqVO);
        return success(BeanUtils.toBean(list, ArchiveRespVO.class));
    }

    @PostMapping("/approvalProcess")
    @ApiOperation(value = "审批一事一档-事件档案")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "eventId", value = "事件编号"),
            @ApiImplicitParam(name = "approvalResult", value = "审批结果"),
            @ApiImplicitParam(name = "approvalComments", value = "审批意见")
    })
    @LogRecordAnnotation(bizModule = "dam:archive:update", operateType = LogOperateType.UPDATE, title = "审批一事一档-事件档案",
            success = "审批一事一档-事件档案成功", fail = "审批一事一档-事件档案失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#archiveId}}")
    public CommonResult<Boolean> approvalProcess(@Valid @RequestBody JSONObject params) {
        String eventId = params.getString("eventId");
        String approvalResult = params.getString("approvalResult");
        String approvalComments = params.getString("approvalComments");
        archiveService.approvalProcess(eventId, approvalResult, approvalComments);
        return success(true);
    }

    @GetMapping("/approveTrack")
    @ApiOperation(value = "获取一事一档-事件档案审批轨迹")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:archive:update", operateType = LogOperateType.UPDATE, title = "获取一事一档-事件档案审批轨迹",
            success = "获取一事一档-事件档案审批轨迹成功", fail = "获取一事一档-事件档案审批轨迹失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#archiveId}}")
    public CommonResult<JSONObject> approveTrack(@RequestParam("id") String id) {
        ArchiveDO archive = archiveService.getById(id);
        JSONObject result = bpmApi.approveTrack(archive.getActInstId());
        // 获取审批轨迹
        List<JSONObject> approveTrack = JSONObject.parseArray(JSONObject.toJSONString(result.get("data")), JSONObject.class);
        approveTrack.forEach(track -> track.put("statusName",
                DicUtils.translate(CommonConstants.DIC_ZD_BPM_APPROVE_STATUS, track.getString("status"))));
        // 反转数组顺序
        Collections.reverse(approveTrack);
        // 格式化时间格式
        approveTrack.forEach(track -> {
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss:SSS");
            String trackEndTime = track.getString("endTime");
            if (StringUtil.isNullBlank(trackEndTime)) {
                trackEndTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss:SSS");
            }
            LocalDateTime dateTime = LocalDateTime.parse(trackEndTime, inputFormatter);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            track.put("endTime", dateTime.format(outputFormatter));
        });
        JSONObject data = new JSONObject();
        data.put("list", approveTrack);
        data.put("index", approveTrack.size());
        return success(data);
    }

    // ==================== 子表（一事一档-事件档案监管人员关联） ====================

    @GetMapping("/archive-prisoner/list-by-even-id")
    @ApiOperation(value = "获得一事一档-事件档案监管人员关联列表")
    @ApiImplicitParam(name = "eventId", value = "事件ID")
    public CommonResult<List<ArchivePrisonerRespVO>> getArchivePrisonerListByEventId(@RequestParam("eventId") String eventId) {
        List<ArchivePrisonerDO> prisonerList = archiveService.getArchivePrisonerListByEventId(eventId);
        if (CollUtil.isEmpty(prisonerList)) {
            return success(new ArrayList<>(0));
        }
        List<ArchivePrisonerRespVO> list = BeanUtils.toBean(prisonerList, ArchivePrisonerRespVO.class);
        String jgrybm = prisonerList.stream().map(prisoner -> prisoner.getJgrybm()).collect(Collectors.joining(","));
        // 获取被监管人员信息
        List<PrisonerVwRespVO> jgryList = prisonerService.getPrisonerListByJgrybm(jgrybm, PrisonerQueryRyztEnum.ALL);
        for (ArchivePrisonerRespVO prisoner : list) {
            for (PrisonerVwRespVO prisonerVwRespVO : jgryList) {
                if (prisoner.getJgrybm().equals(prisonerVwRespVO.getJgrybm())) {
                    prisoner.setXm(prisonerVwRespVO.getXm());
                    prisoner.setXb(prisonerVwRespVO.getXb());
                    prisoner.setFxdj(prisonerVwRespVO.getFxdj());
                    prisoner.setSshj(prisonerVwRespVO.getSshj());
                    prisoner.setSxzm(prisonerVwRespVO.getSxzm());
                    prisoner.setRybh(prisonerVwRespVO.getJgrybm());
                    prisoner.setRoomName(prisonerVwRespVO.getRoomName());
                    break;
                }
             }
        }
        return success(list);
    }

    // ==================== 子表（一事一档-事件档案工作人员关联表） ====================

    @GetMapping("/archive-personnel/list-by-even-id")
    @ApiOperation(value = "获得一事一档-事件档案工作人员关联表")
    @ApiImplicitParam(name = "eventId", value = "事件ID")
    public CommonResult<List<ArchivePersonnelRespVO>> getArchivePersonnelListByEventId(@RequestParam("eventId") String eventId) {
        List<ArchivePersonnelDO> personnelList = archiveService.getArchivePersonnelListByEventId(eventId);
        if (CollUtil.isEmpty(personnelList)) {
            return success(new ArrayList<>(0));
        }
        List<ArchivePersonnelRespVO> list = BeanUtils.toBean(personnelList, ArchivePersonnelRespVO.class);
        // 获取工作人员信息
        String userIdCards = list.stream().map(personnel -> personnel.getUserIdCard()).collect(Collectors.joining(","));
        List<OrgUserRespDTO> userList = bspApi.getUserByIdCards(userIdCards);
        for (ArchivePersonnelRespVO personnel : list) {
            for (OrgUserRespDTO user : userList) {
                if (personnel.getUserId().equals(user.getId())) {
                    personnel.setIdCard(user.getIdCard());
                    personnel.setXb(user.getSex());
                    personnel.setName(user.getName());
                    personnel.setLoginId(user.getLoginId());
                    break;
                }
            }
            // 获取岗位(角色)信息
            List<RoleRespDTO> roleList = bspApi.getRolesByUserId(personnel.getUserId());
            String station = "";
            for (RoleRespDTO role : roleList) {
                if (systemMark.equals(role.getSystemMark())) {
                    station += role.getName() + ",";
                }
            }
            if (station.endsWith(",")){
                station = station.substring(0, station.length() - 1);
            }
            personnel.setStation(station);
        }
        return success(list);
    }

    // ==================== 子表（一事一档-事件档案调查记录表） ====================

    @GetMapping("/invest-record/list-by-even-id")
    @ApiOperation(value = "获得一事一档-事件档案调查记录表")
    @ApiImplicitParam(name = "eventId", value = "事件ID")
    public CommonResult<List<InvestRecordDO>> getInvestRecordListByEventId(@RequestParam("eventId") String eventId) {
        return success(archiveService.getInvestRecordListByEventId(eventId));
    }

    @PostMapping("/invest-record/create")
    @ApiOperation(value = "创建一事一档-事件档案调查记录")
    @LogRecordAnnotation(bizModule = "dam:investRecord:create", operateType = LogOperateType.CREATE, title = "创建一事一档-事件档案调查记录",
            success = "创建一事一档-事件档案调查记录成功", fail = "创建一事一档-事件档案调查记录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createInvestRecord(@Valid @RequestBody InvestRecordSaveReqVO createReqVO) {
        return success(investRecordService.createInvestRecord(createReqVO));
    }

    @GetMapping("/invest-record/delete")
    @ApiOperation(value = "删除一事一档-事件档案调查记录")
    @LogRecordAnnotation(bizModule = "dam:investRecord:create", operateType = LogOperateType.CREATE, title = "删除一事一档-事件档案调查记录",
            success = "删除一事一档-事件档案调查记录成功", fail = "删除一事一档-事件档案调查记录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Boolean> deleteInvestRecord(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            investRecordService.deleteInvestRecord(id);
        }
        return success(true);
    }


}
