package com.rs.module.dam.controller.admin.material;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.entity.material.MaterialRecycleBinDO;
import com.rs.module.dam.service.material.MaterialRecycleBinService;
import com.rs.module.dam.vo.material.MaterialRecycleBinListReqVO;
import com.rs.module.dam.vo.material.MaterialRecycleBinPageReqVO;
import com.rs.module.dam.vo.material.MaterialRecycleBinRespVO;
import com.rs.module.dam.vo.material.MaterialRecycleBinSaveReqVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

@Api(tags = "材料处理 - 卷宗材料回收")
@RestController
@RequestMapping("/dam/material/materialRecycleBin")
@Validated
public class MaterialRecycleBinController {

    @Resource
    private MaterialRecycleBinService materialRecycleBinService;

    @PostMapping("/create")
    @ApiOperation(value = "创建卷宗材料回收")
    @LogRecordAnnotation(bizModule = "dam:materialRecycleBin:create", operateType = LogOperateType.CREATE, title = "创建卷宗材料回收",
    success = "创建卷宗材料回收成功", fail = "创建卷宗材料回收失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createMaterialRecycleBin(@Valid @RequestBody MaterialRecycleBinSaveReqVO createReqVO) {
        return success(materialRecycleBinService.createMaterialRecycleBin(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新卷宗材料回收")
    @LogRecordAnnotation(bizModule = "dam:materialRecycleBin:update", operateType = LogOperateType.UPDATE, title = "更新卷宗材料回收",
    success = "更新卷宗材料回收成功", fail = "更新卷宗材料回收失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateMaterialRecycleBin(@Valid @RequestBody MaterialRecycleBinSaveReqVO updateReqVO) {
        materialRecycleBinService.updateMaterialRecycleBin(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除卷宗材料回收")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:materialRecycleBin:delete", operateType = LogOperateType.DELETE, title = "删除卷宗材料回收",
    success = "删除卷宗材料回收成功", fail = "删除卷宗材料回收失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteMaterialRecycleBin(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           materialRecycleBinService.deleteMaterialRecycleBin(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得卷宗材料回收")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:materialRecycleBin:get", operateType = LogOperateType.QUERY, title = "获取卷宗材料回收", bizNo = "{{#id}}", success = "获取卷宗材料回收成功", fail = "获取卷宗材料回收失败", extraInfo = "{{#id}}")
    public CommonResult<MaterialRecycleBinRespVO> getMaterialRecycleBin(@RequestParam("id") String id) {
        MaterialRecycleBinDO materialRecycleBin = materialRecycleBinService.getMaterialRecycleBin(id);
        return success(BeanUtils.toBean(materialRecycleBin, MaterialRecycleBinRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得卷宗材料回收分页")
    @LogRecordAnnotation(bizModule = "dam:materialRecycleBin:page", operateType = LogOperateType.QUERY, title = "获得卷宗材料回收分页",
    success = "获得卷宗材料回收分页成功", fail = "获得卷宗材料回收分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<MaterialRecycleBinRespVO>> getMaterialRecycleBinPage(@Valid @RequestBody MaterialRecycleBinPageReqVO pageReqVO) {
        PageResult<MaterialRecycleBinDO> pageResult = materialRecycleBinService.getMaterialRecycleBinPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MaterialRecycleBinRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得卷宗材料回收列表")
    @LogRecordAnnotation(bizModule = "dam:materialRecycleBin:list", operateType = LogOperateType.QUERY, title = "获得卷宗材料回收列表",
    success = "获得卷宗材料回收列表成功", fail = "获得卷宗材料回收列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<MaterialRecycleBinRespVO>> getMaterialRecycleBinList(@Valid @RequestBody MaterialRecycleBinListReqVO listReqVO) {
    	List<MaterialRecycleBinDO> list = materialRecycleBinService.getMaterialRecycleBinList(listReqVO);
        return success(BeanUtils.toBean(list, MaterialRecycleBinRespVO.class));
    }

    @GetMapping("/getMaterialRecycleBinByJgrybm")
    @ApiOperation(value = "根据监管人员编码获得卷宗材料回收列表")
    @ApiImplicitParam(name = "jgrybm", value = "监管人员编码")
    @LogRecordAnnotation(bizModule = "dam:materialRecycleBin:list", operateType = LogOperateType.QUERY, title = "根据监管人员编码获得卷宗材料回收列表获得卷宗材料回收列表",
    success = "根据监管人员编码获得卷宗材料回收列表成功", fail = "根据监管人员编码获得卷宗材料回收列表获得卷宗材料回收列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<MaterialRecycleBinRespVO>> getMaterialRecycleBinByJgrybm(@RequestParam("jgrybm") String jgrybm) {
    	List<MaterialRecycleBinDO> list = materialRecycleBinService.getMaterialRecycleBinByJgrybm(jgrybm);
        return success(BeanUtils.toBean(list, MaterialRecycleBinRespVO.class));
    }
}
