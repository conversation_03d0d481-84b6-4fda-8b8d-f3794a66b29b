package com.rs.module.dam.controller.admin.prison.vo;

import com.rs.framework.annotation.GetFile;
import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import org.dromara.x.file.storage.core.FileInfo;

import java.util.Date;

@ApiModel(description = "管理后台 - 一所一档-监所档案荣誉表彰配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RecognitionConfigPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("表彰名称")
    private String recognitionName;

    @ApiModelProperty("表彰时间")
    private Date[] recognitionTime;

    @ApiModelProperty("排序属性")
    List<OrderItem> orderFields;

    @GetFile(value = "id")
    public List<FileInfo> fileList;
}
