package com.rs.module.dam.service.prison;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.controller.admin.prison.vo.FeatureConfigListReqVO;
import com.rs.module.dam.controller.admin.prison.vo.FeatureConfigPageReqVO;
import com.rs.module.dam.controller.admin.prison.vo.FeatureConfigSaveReqVO;
import com.rs.module.dam.entity.prison.FeatureConfigDO;
import com.rs.module.dam.entity.prison.PrisonArchiveDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 一所一档-监所档案特色亮点配置 Service 接口
 *
 * <AUTHOR>
 */
public interface FeatureConfigService extends IBaseService<FeatureConfigDO>{

    /**
     * 创建一所一档-监所档案特色亮点配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createFeatureConfig(@Valid FeatureConfigSaveReqVO createReqVO);

    /**
     * 更新一所一档-监所档案特色亮点配置
     *
     * @param updateReqVO 更新信息
     */
    String updateFeatureConfig(@Valid FeatureConfigSaveReqVO updateReqVO);

    /**
     * 删除一所一档-监所档案特色亮点配置
     *
     * @param id 编号
     */
    void deleteFeatureConfig(String id);

    /**
     * 获得一所一档-监所档案特色亮点配置
     *
     * @param id 编号
     * @return 一所一档-监所档案特色亮点配置
     */
    FeatureConfigDO getFeatureConfig(String id);

    /**
     * 获得一所一档-监所档案特色亮点配置
     *
     * @param prisonCode 监所编号
     * @return 一所一档-监所档案特色亮点配置
     */
    List<FeatureConfigDO> getByPrisonCode(String prisonCode);

    /**
    * 获得一所一档-监所档案特色亮点配置分页
    *
    * @param pageReqVO 分页查询
    * @return 一所一档-监所档案特色亮点配置分页
    */
    PageResult<FeatureConfigDO> getFeatureConfigPage(FeatureConfigPageReqVO pageReqVO);

    /**
    * 获得一所一档-监所档案特色亮点配置列表
    *
    * @param listReqVO 查询条件
    * @return 一所一档-监所档案特色亮点配置列表
    */
    List<FeatureConfigDO> getFeatureConfigList(FeatureConfigListReqVO listReqVO);


}
