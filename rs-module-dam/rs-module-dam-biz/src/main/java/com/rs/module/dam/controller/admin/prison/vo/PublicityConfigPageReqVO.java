package com.rs.module.dam.controller.admin.prison.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 一所一档-监所档案宣传报道配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PublicityConfigPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(" 报道标题")
    private String publicityTitle;

    @ApiModelProperty("报道时间")
    private Date[] publicityTime;

    @ApiModelProperty("报道内容")
    private String publicityContent;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
