package com.rs.module.dam.controller.admin.prison;

import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.dto.OrgRespDTO;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.dam.controller.admin.prison.vo.RoomArchiveDTO;
import com.rs.module.dam.controller.admin.prison.vo.RoomArchiveDetailsDTO;
import com.rs.module.dam.service.prison.RoomArchiveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "一室一档")
@RestController
@RequestMapping("/dam/archive/room")
@Validated
public class RoomArchiveController {

    @Resource
    private RoomArchiveService roomArchiveService;
    @Resource
    private BspApi bspApi;

    @GetMapping("/getCount")
    @ApiOperation(value = "获得监室事务总数")
    public CommonResult getCount(RoomArchiveDTO dto) {
        try {
            return success(roomArchiveService.getCount(dto));
        }catch (Exception e){
            e.printStackTrace();
            return error("查询失败！");
        }
    }

    @GetMapping("/getList")
    @ApiOperation(value = "获得监室事务列表")
    public CommonResult getList(RoomArchiveDetailsDTO dto) {
        try {
            String orgCode = dto.getOrgCode();
            if (StringUtil.isNullBlank(orgCode)) {
                orgCode = SessionUserUtil.getSessionUser().getOrgCode();
            }
            OrgRespDTO org = bspApi.getOrgByCode(orgCode);
            return success(roomArchiveService.getList(dto, org.getOrgType()));
        }catch (Exception e){
            e.printStackTrace();
            return error("查询失败！");
        }
    }

}
