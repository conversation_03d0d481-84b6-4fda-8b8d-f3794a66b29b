package com.rs.module.dam.controller.admin.prison.vo;
import com.rs.framework.annotation.SaveFile;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.dromara.x.file.storage.core.FileInfo;

import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 一所一档-监所档案荣誉表彰配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RecognitionConfigSaveReqVO extends BaseVO{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("表彰名称")
    @NotEmpty(message = "表彰名称不能为空")
    private String recognitionName;

    @ApiModelProperty("表彰时间")
    @NotNull(message = "表彰时间不能为空")
    private Date recognitionTime;

    @SaveFile
    @ApiModelProperty("附件")
    private List<FileInfo> fileList;

}
