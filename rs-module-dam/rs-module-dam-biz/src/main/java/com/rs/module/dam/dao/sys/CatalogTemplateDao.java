package com.rs.module.dam.dao.sys;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.sys.CatalogTemplateDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.dam.controller.admin.sys.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 卷宗目录模板 Dao
*
* <AUTHOR>
*/
@Mapper
public interface CatalogTemplateDao extends IBaseDao<CatalogTemplateDO> {


    default PageResult<CatalogTemplateDO> selectPage(CatalogTemplatePageReqVO reqVO) {
        Page<CatalogTemplateDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<CatalogTemplateDO> wrapper = new LambdaQueryWrapperX<CatalogTemplateDO>()
            .likeIfPresent(CatalogTemplateDO::getName, reqVO.getName())
            .eqIfPresent(CatalogTemplateDO::getData, reqVO.getData())
            .eqIfPresent(CatalogTemplateDO::getType, reqVO.getType())
            .eqIfPresent(CatalogTemplateDO::getTemplateId, reqVO.getTemplateId());

        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(CatalogTemplateDO::getAddTime);
        }
        Page<CatalogTemplateDO> catalogTemplatePage = selectPage(page, wrapper);
        return new PageResult<>(catalogTemplatePage.getRecords(), catalogTemplatePage.getTotal());
    }

    default List<CatalogTemplateDO> selectList(CatalogTemplateListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CatalogTemplateDO>()
            .likeIfPresent(CatalogTemplateDO::getName, reqVO.getName())
            .eqIfPresent(CatalogTemplateDO::getData, reqVO.getData())
            .eqIfPresent(CatalogTemplateDO::getType, reqVO.getType())
            .eqIfPresent(CatalogTemplateDO::getTemplateId, reqVO.getTemplateId())
        .orderByDesc(CatalogTemplateDO::getAddTime));
    }


}
