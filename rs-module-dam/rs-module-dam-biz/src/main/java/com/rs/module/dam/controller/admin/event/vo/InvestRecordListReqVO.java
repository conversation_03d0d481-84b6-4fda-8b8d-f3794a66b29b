package com.rs.module.dam.controller.admin.event.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 一事一档-事件档案调查记录列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class InvestRecordListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("事件ID")
    private String eventId;

    @ApiModelProperty("事件编号(规则：S+9位单位编号+8位日期+6位序号)")
    private String eventCode;

    @ApiModelProperty("调查详情")
    private String investDetails;

    @ApiModelProperty("上传附件存储路径")
    private String attachmentPath;

}
