package com.rs.module.dam.service.event;

import java.util.*;
import javax.validation.*;
import com.rs.module.dam.controller.admin.event.vo.*;
import com.rs.module.dam.entity.event.ArchiveDO;
import com.rs.module.dam.entity.event.ArchivePersonnelDO;
import com.rs.module.dam.entity.event.ArchivePrisonerDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.dam.entity.event.InvestRecordDO;

/**
 * 一事一档-事件档案 Service 接口
 *
 * <AUTHOR>
 */
public interface ArchiveService extends IBaseService<ArchiveDO>{

    /**
     * 创建一事一档-事件档案
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createArchive(@Valid ArchiveSaveReqVO createReqVO);

    /**
     * 更新一事一档-事件档案
     *
     * @param updateReqVO 更新信息
     */
    void updateArchive(@Valid ArchiveSaveReqVO updateReqVO);

    /**
     * 删除一事一档-事件档案
     *
     * @param id 编号
     */
    void deleteArchive(String id);

    /**
     * 获得一事一档-事件档案
     *
     * @param id 编号
     * @return 一事一档-事件档案
     */
    ArchiveDO getArchive(String id);

    /**
    * 获得一事一档-事件档案分页
    *
    * @param pageReqVO 分页查询
    * @return 一事一档-事件档案分页
    */
    PageResult<ArchiveDO> getArchivePage(ArchivePageReqVO pageReqVO);

    /**
    * 获得一事一档-事件档案列表
    *
    * @param listReqVO 查询条件
    * @return 一事一档-事件档案列表
    */
    List<ArchiveDO> getArchiveList(ArchiveListReqVO listReqVO);

    /**
     * 一事一档审批流程
     */
    void approvalProcess(String eventId, String approvalResult, String approvalComments);


    // ==================== 子表（一事一档-事件档案监管人员关联） ====================

    /**
     * 获得一事一档-事件档案监管人员关联列表
     *
     * @param eventId 事件ID
     * @return 一事一档-事件档案监管人员关联列表
     */
    List<ArchivePrisonerDO> getArchivePrisonerListByEventId(String eventId);


    // ==================== 子表（一事一档-事件档案工作人员关联表） ====================

    /**
     * 获得一事一档-事件档案工作人员关联表
     *
     * @param eventId 事件ID
     * @return 一事一档-事件档案工作人员关联表
     */
    List<ArchivePersonnelDO> getArchivePersonnelListByEventId(String eventId);


    // ==================== 子表（一事一档-事件档案调查记录表） ====================

    /**
     * 获得一事一档-事件档案调查记录表
     *
     * @param eventId 事件ID
     * @return 一事一档-事件档案调查记录表
     */
    List<InvestRecordDO> getInvestRecordListByEventId(String eventId);

}
