package com.rs.module.dam.controller.admin.sys.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 目录文书映射列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CatalogMappingListReqVO extends BaseVO {
    @ApiModelProperty("目录模板Id")
    private String templateId;

    @ApiModelProperty("模板目录Id")
    private String catalogId;

    @ApiModelProperty("应用Id")
    private String appId;

    @ApiModelProperty("表单Id")
    private String formId;

    @ApiModelProperty("表单名称")
    private String formName;

}
