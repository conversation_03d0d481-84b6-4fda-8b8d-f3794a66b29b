package com.rs.module.dam.controller.admin.event.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

@ApiModel(description = "管理后台 - 一事一档-事件档案 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ArchivePersonnelRespVO extends BaseVO implements TransPojo {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("事件ID")
    private String eventId;
    @ApiModelProperty("事件编号")
    private String eventCode;
    @ApiModelProperty("用户ID")
    private String userId;
    @ApiModelProperty("用户身份证号")
    private String userIdCard;
    @ApiModelProperty("用户名称")
    private String userName;
    @ApiModelProperty("岗位")
    private String station;
    @ApiModelProperty("身份证号")
    private String idCard;
    @ApiModelProperty("警号")
    private String loginId;
    @ApiModelProperty("用户名称")
    private String name;
    @ApiModelProperty("性别")
    private String xb;

}
