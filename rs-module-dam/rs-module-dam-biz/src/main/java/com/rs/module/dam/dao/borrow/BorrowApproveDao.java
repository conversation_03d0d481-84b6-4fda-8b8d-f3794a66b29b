package com.rs.module.dam.dao.borrow;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApproveListReqVO;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApprovePageReqVO;
import com.rs.module.dam.entity.borrow.BorrowApproveDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 借阅审批 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BorrowApproveDao extends IBaseDao<BorrowApproveDO> {


    default PageResult<BorrowApproveDO> selectPage(BorrowApprovePageReqVO reqVO) {
        Page<BorrowApproveDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<BorrowApproveDO> wrapper = new LambdaQueryWrapperX<BorrowApproveDO>()
            .eqIfPresent(BorrowApproveDO::getBorrowId, reqVO.getBorrowId())
            .eqIfPresent(BorrowApproveDO::getApproveStatus, reqVO.getApproveStatus())
            .eqIfPresent(BorrowApproveDO::getApproveRoleCode, reqVO.getApproveRoleCode())
            .likeIfPresent(BorrowApproveDO::getApproveRoleName, reqVO.getApproveRoleName())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(BorrowApproveDO::getAddTime);
        }
        Page<BorrowApproveDO> borrowApprovePage = selectPage(page, wrapper);
        return new PageResult<>(borrowApprovePage.getRecords(), borrowApprovePage.getTotal());
    }
    default List<BorrowApproveDO> selectList(BorrowApproveListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BorrowApproveDO>()
            .eqIfPresent(BorrowApproveDO::getBorrowId, reqVO.getBorrowId())
            .eqIfPresent(BorrowApproveDO::getApproveStatus, reqVO.getApproveStatus())
            .eqIfPresent(BorrowApproveDO::getApproveRoleCode, reqVO.getApproveRoleCode())
            .likeIfPresent(BorrowApproveDO::getApproveRoleName, reqVO.getApproveRoleName())
        .orderByDesc(BorrowApproveDO::getAddTime));    }


    }
