package com.rs.module.dam.entity.borrow;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import java.util.Date;

/**
 * 借阅申请 DO
 *
 * <AUTHOR>
 */
@TableName("dam_borrow_apply")
@KeySequence("dam_borrow_apply_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BorrowApplyDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 申请单号
     */
    private String applyNo;
    /**
     * 借阅类型(0:内部借阅,1:外部借阅)
     */
    private String type;
    /**
     * 借阅单位编号
     */
    private String borrowOrgCode;
    /**
     * 借阅单位名称
     */
    private String borrowOrgName;
    /**
     * 借阅人
     */
    private String borrowUser;
    /**
     * 借阅人姓名
     */
    private String borrowUserName;
    /**
     * 申请理由
     */
    private String borrowReason;
    /**
     * 借阅天数
     */
    private Integer borrowDays;
    /**
     * 借阅截止时间
     */
    private Date borrowEndDate;
    /**
     * 借阅卷宗数量
     */
    private Integer borrowNum;
    /**
     * 备注
     */
    private String remark;
    /**
     * 附件
     */
    private String attachment;
    /**
     * 审批状态(0:待审批,1:审批中,2:审批通过,3:部分审批通过,4:审批不通过)
     */
    private String approveStatus;
    /**
     * 下级审批角色
     */
    private String nextApproveRole;

}
