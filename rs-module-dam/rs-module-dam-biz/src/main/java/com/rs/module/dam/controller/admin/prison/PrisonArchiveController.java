package com.rs.module.dam.controller.admin.prison;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.controller.admin.prison.vo.*;
import com.rs.module.dam.entity.prison.PrisonArchiveDO;
import com.rs.module.dam.service.prison.FeatureConfigService;
import com.rs.module.dam.service.prison.PrisonArchiveService;
import com.rs.module.dam.service.prison.PublicityConfigService;
import com.rs.module.dam.service.prison.RecognitionConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "一所一档-监所档案")
@RestController
@RequestMapping("/dam/prison/archive")
@Validated
public class PrisonArchiveController {

    @Resource
    private PrisonArchiveService archiveService;
    @Resource
    private FeatureConfigService  featureConfigService;
    @Resource
    private PublicityConfigService  publicityConfigService;
    @Resource
    private RecognitionConfigService recognitionConfigService;


    @PostMapping("/create")
    @ApiOperation(value = "创建一所一档-监所档案")
    @LogRecordAnnotation(bizModule = "dam:archive:create", operateType = LogOperateType.CREATE, title = "创建一所一档-监所档案",
    success = "创建一所一档-监所档案成功", fail = "创建一所一档-监所档案失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createArchive(@Valid @RequestBody PrisonArchiveSaveReqVO createReqVO) {
        return success(archiveService.createArchive(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新一所一档-监所档案")
    @LogRecordAnnotation(bizModule = "dam:archive:update", operateType = LogOperateType.UPDATE, title = "更新一所一档-监所档案",
    success = "更新一所一档-监所档案成功", fail = "更新一所一档-监所档案失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<String> updateArchive(@Valid @RequestBody PrisonArchiveSaveReqVO updateReqVO) {
        return success(archiveService.updateArchive(updateReqVO));
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除一所一档-监所档案")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:archive:delete", operateType = LogOperateType.DELETE, title = "删除一所一档-监所档案",
    success = "删除一所一档-监所档案成功", fail = "删除一所一档-监所档案失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteArchive(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           archiveService.deleteArchive(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得一所一档-监所档案")
    @ApiImplicitParam(name = "prisonCode", value = "监所编号")
    public CommonResult<PrisonArchiveRespVO> getArchive(@RequestParam("prisonCode") String prisonCode) {
        PrisonArchiveDO archive = archiveService.getByPrisonCode(prisonCode);
        if (archive == null) {
            PrisonArchiveDO defaultArchive = archiveService.createDefaultArchive(prisonCode);
            PrisonArchiveRespVO respVO = BeanUtils.toBean(defaultArchive, PrisonArchiveRespVO.class);
            return success(respVO);
        }
        PrisonArchiveRespVO respVO = JSONUtil.toBean(JSONObject.toJSONString(archive), PrisonArchiveRespVO.class);
        respVO.setFunctionRoomConfigs(JSONObject.parseArray(archive.getFunctionRoomConfiguration(), PrisonArchiveConfigVO.class));
        respVO.setEquipmentConfigs(JSONObject.parseArray(archive.getEquipmentConfiguration(), PrisonArchiveConfigVO.class));
        respVO.setFeatureConfigs(featureConfigService.getByPrisonCode(archive.getPrisonCode()));
        respVO.setPublicityConfigs(publicityConfigService.getByPrisonCode(archive.getPrisonCode()));
        respVO.setRecognitionConfigs(recognitionConfigService.getByPrisonCode(archive.getPrisonCode()));
        return success(respVO);
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得一所一档-监所档案分页")
    public CommonResult<PageResult<PrisonArchiveRespVO>> getArchivePage(@Valid @RequestBody PrisonArchivePageReqVO pageReqVO) {
        PageResult<PrisonArchiveDO> pageResult = archiveService.getArchivePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PrisonArchiveRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得一所一档-监所档案列表")
    public CommonResult<List<PrisonArchiveRespVO>> getArchiveList(@Valid @RequestBody PrisonArchiveListReqVO listReqVO) {
        List<PrisonArchiveDO> list = archiveService.getArchiveList(listReqVO);
        return success(BeanUtils.toBean(list, PrisonArchiveRespVO.class));
    }

}
