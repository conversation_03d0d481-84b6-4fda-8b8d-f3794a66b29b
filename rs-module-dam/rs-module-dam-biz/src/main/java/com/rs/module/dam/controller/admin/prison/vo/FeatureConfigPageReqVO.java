package com.rs.module.dam.controller.admin.prison.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.annotation.GetFile;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.dromara.x.file.storage.core.FileInfo;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 一所一档-监所档案特色亮点配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FeatureConfigPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("特色亮点标题")
    private String featureTitle;

    @ApiModelProperty("建设时间")
    private Date[] constructionTime;

    @ApiModelProperty("特色亮点内容")
    private String featureContent;

    @ApiModelProperty("排序属性")
    List<OrderItem> orderFields;

    @GetFile(value = "id")
    public List<FileInfo> fileList;
}
