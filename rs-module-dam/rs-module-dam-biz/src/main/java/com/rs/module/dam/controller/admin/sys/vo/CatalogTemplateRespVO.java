package com.rs.module.dam.controller.admin.sys.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 卷宗目录模板 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CatalogTemplateRespVO extends BaseVO implements TransPojo{
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("模板名称")
    private String name;
    @ApiModelProperty("目录数据")
    private String data;
    @ApiModelProperty("类型（1：标准  0：自定义模板）")
    private String type;
    @ApiModelProperty("标准模板ID")
    private String templateId;
}
