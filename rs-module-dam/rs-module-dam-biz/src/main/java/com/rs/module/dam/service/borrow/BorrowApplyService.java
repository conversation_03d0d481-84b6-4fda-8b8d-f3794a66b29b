package com.rs.module.dam.service.borrow;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApplyListReqVO;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApplyPageReqVO;
import com.rs.module.dam.controller.admin.borrow.vo.BorrowApplySaveReqVO;
import com.rs.module.dam.entity.borrow.BorrowApplyDO;
import com.rs.module.dam.entity.borrow.BorrowPrisonerDO;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 借阅申请 Service 接口
 *
 * <AUTHOR>
 */
public interface BorrowApplyService extends IBaseService<BorrowApplyDO>{

    /**
     * 创建借阅申请
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    BorrowApplyDO createBorrowApply(@Valid BorrowApplySaveReqVO createReqVO);

    /**
     * 获取再次申请档案详情
     * @param prisonerId
     * @return
     */
    Map<String, Object> getReapplyDetail(String prisonerId);

    /**
     * 更新借阅申请
     *
     * @param updateReqVO 更新信息
     */
    void updateBorrowApply(@Valid BorrowApplySaveReqVO updateReqVO);

    /**
     * 删除借阅申请
     *
     * @param id 编号
     */
    void deleteBorrowApply(String id);

    /**
     * 获得借阅申请
     *
     * @param id 编号
     * @return 借阅申请
     */
    BorrowApplyDO getBorrowApply(String id);

    /**
    * 获得借阅申请分页
    *
    * @param pageReqVO 分页查询
    * @return 借阅申请分页
    */
    PageResult<BorrowApplyDO> getBorrowApplyPage(BorrowApplyPageReqVO pageReqVO);

    /**
    * 获得借阅申请列表
    *
    * @param listReqVO 查询条件
    * @return 借阅申请列表
    */
    List<BorrowApplyDO> getBorrowApplyList(BorrowApplyListReqVO listReqVO);


    // ==================== 子表（借阅人员档案关联） ====================

    /**
     * 获得借阅人员档案关联列表
     *
     * @param borrowId 借阅Id
     * @return 借阅人员档案关联列表
     */
    List<BorrowPrisonerDO> getBorrowPrisonerListByBorrowId(String borrowId);

    /**
     * 更新借阅人员档案关联
     * @param borrowPrisoner
     */
    void updateBorrowPrisonerById(BorrowPrisonerDO borrowPrisoner);

    /**
     * 借阅审批
     * @param borrowApply 借阅信息
     * @param list 借阅人员档案关联列表
     */
    void approve(BorrowApplyDO borrowApply, List<BorrowPrisonerDO> list);


}
