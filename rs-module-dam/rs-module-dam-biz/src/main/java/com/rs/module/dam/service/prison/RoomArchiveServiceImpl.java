package com.rs.module.dam.service.prison;

import cn.hutool.core.date.DateUtil;
import com.bsp.common.cache.DicUtil;
import com.rs.enums.PrisonTypeEnum;
import com.rs.module.dam.controller.admin.prison.vo.RoomArchiveCountVo;
import com.rs.module.dam.controller.admin.prison.vo.RoomArchiveDTO;
import com.rs.module.dam.controller.admin.prison.vo.RoomArchiveDetailsDTO;
import com.rs.module.dam.controller.admin.prison.vo.RoomArchiveDetailsVo;
import com.rs.module.dam.dao.prison.RoomArchiveDao;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 一室一档- Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RoomArchiveServiceImpl implements RoomArchiveService {

    @Resource
    private RoomArchiveDao roomArchiveDao;

    @Override
    public List<RoomArchiveCountVo> getCount(RoomArchiveDTO dto) {
        //时间传空则默认当天
        if (dto.getDate()==null){
            dto.setDate(new Date());
        }
        return roomArchiveDao.getCount(dto.getRoomId(), DateUtil.formatDate(dto.getDate()));
    }

    @Override
    public List<RoomArchiveDetailsVo> getList(RoomArchiveDetailsDTO dto, String prisonType) {
        if (dto.getDate()==null){
            dto.setDate(new Date());
        }
        if (dto.getBusinessIds()==null){
            List<RoomArchiveCountVo> countVoList = roomArchiveDao.getCount(dto.getRoomId(), DateUtil.formatDate(dto.getDate()));
            List<Integer> collect = countVoList.stream().map(o -> o.getBusinessType()).collect(Collectors.toList());
            if (collect!=null && collect.size()>0){
                dto.setBusinessIds(collect);
            }else {
                return null;
            }

        }
        List<RoomArchiveDetailsVo> list = roomArchiveDao.getList(dto.getRoomId(),DateUtil.formatDate(dto.getDate()), dto.getBusinessIds());
        list.forEach(o -> {
            switch (o.getBusinessType()) {
                case 1:
                    //新入所
                    o.setFieldTwo(DicUtil.translate("ZD_SXZM", o.getFieldTwo()));
                    o.setFieldThree(DicUtil.translate("ZD_SSJD", o.getFieldThree()));
                    break;
                case 2:
                    //出所
                    if (PrisonTypeEnum.KSS.getValue().equals(prisonType)) {
                        o.setFieldTwo(DicUtil.translate("ZD_KSS_CSYY", o.getFieldTwo()));
                    } else if (PrisonTypeEnum.JDS.getValue().equals(prisonType)) {
                        o.setFieldTwo(DicUtil.translate("ZD_JDS_CSYY", o.getFieldTwo()));
                    } else if (PrisonTypeEnum.JLS.getValue().equals(prisonType)) {
                        o.setFieldTwo(DicUtil.translate("ZD_JLS_CSYY", o.getFieldTwo()));
                    }
                    break;
                case 7:
                    //提解
                    o.setFieldThree(DicUtil.translate("ZD_TXTJ_YY", o.getFieldThree()));
                    break;
                case 9:
                    //家属会见
                    o.setFieldThree(DicUtil.translate("ZD_GABBZ_SHGX", o.getFieldThree()));
                    break;
//                case 11:
//                    //出所就医
//                    o.setFieldTwo(DicUtil.translate("ZD_SXZM", o.getFieldTwo()));
//                    o.setFieldThree(DicUtil.translate("ZD_SSJD", o.getFieldThree()));
//                    break;
                case 15:
                    //所情管理
                    o.setFieldOne(DicUtil.translate("ZD_PRISON_EVENT_LEVEL", o.getFieldTwo()));
                    o.setFieldTwo(DicUtil.translate("ZD_PRISON_EVENT_BUSINESS_TYPE", o.getFieldThree()));
                    break;
                case 16:
                    //械具使用
                    String s = "";
                    String[] split = o.getFieldTwo().split(",");
                    for (String s1 : split) {
                        s += DicUtil.translate("ZD_GJ_EQUIPMENT_TYPE", s1);
                    }
                    o.setFieldTwo(s.substring(0, s.length() - 1));
                    break;
                case 22:
                    //安全检查
                    o.setFieldTwo(DicUtil.translate("ZD_AQJC", o.getFieldTwo()));
                    break;
            }
        });
        return list;
    }

}
