package com.rs.module.dam.dao.borrow;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.borrow.PrisonerApproveDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.dam.controller.admin.borrow.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 借阅人员档案审批 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PrisonerApproveDao extends IBaseDao<PrisonerApproveDO> {


    default PageResult<PrisonerApproveDO> selectPage(PrisonerApprovePageReqVO reqVO) {
        Page<PrisonerApproveDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrisonerApproveDO> wrapper = new LambdaQueryWrapperX<PrisonerApproveDO>()
            .eqIfPresent(PrisonerApproveDO::getBorrowId, reqVO.getBorrowId())
            .eqIfPresent(PrisonerApproveDO::getBorrowPrisonerId, reqVO.getBorrowPrisonerId())
            .eqIfPresent(PrisonerApproveDO::getApproveId, reqVO.getApproveId())
            .eqIfPresent(PrisonerApproveDO::getApproveRoleCode, reqVO.getApproveRoleCode())
            .likeIfPresent(PrisonerApproveDO::getApproveRoleName, reqVO.getApproveRoleName())
            .eqIfPresent(PrisonerApproveDO::getApproveStatus, reqVO.getApproveStatus())
            .betweenIfPresent(PrisonerApproveDO::getApproveTime, reqVO.getApproveTime())
            .eqIfPresent(PrisonerApproveDO::getApproveContent, reqVO.getApproveContent())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PrisonerApproveDO::getAddTime);
        }
        Page<PrisonerApproveDO> prisonerApprovePage = selectPage(page, wrapper);
        return new PageResult<>(prisonerApprovePage.getRecords(), prisonerApprovePage.getTotal());
    }
    default List<PrisonerApproveDO> selectList(PrisonerApproveListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrisonerApproveDO>()
            .eqIfPresent(PrisonerApproveDO::getBorrowId, reqVO.getBorrowId())
            .eqIfPresent(PrisonerApproveDO::getBorrowPrisonerId, reqVO.getBorrowPrisonerId())
            .eqIfPresent(PrisonerApproveDO::getApproveId, reqVO.getApproveId())
            .eqIfPresent(PrisonerApproveDO::getApproveRoleCode, reqVO.getApproveRoleCode())
            .likeIfPresent(PrisonerApproveDO::getApproveRoleName, reqVO.getApproveRoleName())
            .eqIfPresent(PrisonerApproveDO::getApproveStatus, reqVO.getApproveStatus())
            .betweenIfPresent(PrisonerApproveDO::getApproveTime, reqVO.getApproveTime())
            .eqIfPresent(PrisonerApproveDO::getApproveContent, reqVO.getApproveContent())
        .orderByDesc(PrisonerApproveDO::getAddTime));    }


    }
