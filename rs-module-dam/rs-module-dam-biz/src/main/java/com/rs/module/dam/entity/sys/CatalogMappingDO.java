package com.rs.module.dam.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 目录文书映射 DO
 *
 * <AUTHOR>
 */
@TableName("dam_sys_catalog_mapping")
@KeySequence("dam_sys_catalog_mapping_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CatalogMappingDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 目录模板Id
     */
    private String templateId;
    /**
     * 模板目录Id
     */
    private String catalogId;
    /**
     * 应用Id
     */
    private String appId;
    /**
     * 表单Id
     */
    private String formId;
    /**
     * 表单名称
     */
    private String formName;

}
