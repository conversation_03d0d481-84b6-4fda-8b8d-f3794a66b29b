package com.rs.module.dam.controller.admin.prison.vo;

import com.rs.framework.annotation.SaveFile;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.x.file.storage.core.FileInfo;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 一所一档-监所档案特色亮点配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FeatureConfigSaveReqVO extends BaseVO{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("特色亮点标题")
    @NotEmpty(message = "特色亮点标题不能为空")
    private String featureTitle;

    @ApiModelProperty("建设时间")
    @NotNull(message = "建设时间不能为空")
    private Date constructionTime;

    @ApiModelProperty("特色亮点内容")
    @NotEmpty(message = "特色亮点内容不能为空")
    private String featureContent;

    @SaveFile
    @ApiModelProperty("附件")
    private List<FileInfo> fileList;

}
