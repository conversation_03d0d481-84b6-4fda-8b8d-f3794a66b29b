package com.rs.module.dam.service.sys;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.dam.controller.admin.sys.vo.*;
import com.rs.module.dam.entity.sys.CatalogTemplateDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.dam.dao.sys.CatalogTemplateDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 卷宗目录模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CatalogTemplateServiceImpl extends BaseServiceImpl<CatalogTemplateDao, CatalogTemplateDO> implements CatalogTemplateService {

    @Resource
    private CatalogTemplateDao catalogTemplateDao;

    @Override
    public String createCatalogTemplate(CatalogTemplateSaveReqVO createReqVO) {
        // 插入
        CatalogTemplateDO catalogTemplate = BeanUtils.toBean(createReqVO, CatalogTemplateDO.class);
        saveOrUpdate(catalogTemplate);
        // 返回
        return catalogTemplate.getId();
    }

    @Override
    public void updateCatalogTemplate(CatalogTemplateSaveReqVO updateReqVO) {
        // 校验存在
        validateCatalogTemplateExists(updateReqVO.getId());
        // 更新
        CatalogTemplateDO updateObj = BeanUtils.toBean(updateReqVO, CatalogTemplateDO.class);
        catalogTemplateDao.updateById(updateObj);
    }

    @Override
    public void deleteCatalogTemplate(String id) {
        // 校验存在
        validateCatalogTemplateExists(id);
        // 删除
        catalogTemplateDao.deleteById(id);
    }

    private void validateCatalogTemplateExists(String id) {
        if (catalogTemplateDao.selectById(id) == null) {
            throw new ServerException("卷宗目录模板数据不存在");
        }
    }

    @Override
    public CatalogTemplateDO getCatalogTemplate(String id) {
        return catalogTemplateDao.selectById(id);
    }

    @Override
    public PageResult<CatalogTemplateDO> getCatalogTemplatePage(CatalogTemplatePageReqVO pageReqVO) {
        return catalogTemplateDao.selectPage(pageReqVO);
    }

    @Override
    public List<CatalogTemplateDO> getCatalogTemplateList(CatalogTemplateListReqVO listReqVO) {
        return catalogTemplateDao.selectList(listReqVO);
    }


}
