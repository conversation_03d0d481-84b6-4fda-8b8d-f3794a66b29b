package com.rs.module.dam.controller.admin.prison;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.controller.admin.prison.vo.FeatureConfigListReqVO;
import com.rs.module.dam.controller.admin.prison.vo.FeatureConfigPageReqVO;
import com.rs.module.dam.controller.admin.prison.vo.FeatureConfigRespVO;
import com.rs.module.dam.controller.admin.prison.vo.FeatureConfigSaveReqVO;
import com.rs.module.dam.entity.prison.FeatureConfigDO;
import com.rs.module.dam.service.prison.FeatureConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "一所一档-监所档案特色亮点配置")
@RestController
@RequestMapping("/dam/prison/featureConfig")
@Validated
public class FeatureConfigController {

    @Resource
    private FeatureConfigService featureConfigService;

    @PostMapping("/create")
    @ApiOperation(value = "创建一所一档-监所档案特色亮点配置")
    @LogRecordAnnotation(bizModule = "dam:featureConfig:create", operateType = LogOperateType.CREATE, title = "创建一所一档-监所档案特色亮点配置",
    success = "创建一所一档-监所档案特色亮点配置成功", fail = "创建一所一档-监所档案特色亮点配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createFeatureConfig(@Valid @RequestBody FeatureConfigSaveReqVO createReqVO) {
        return success(featureConfigService.createFeatureConfig(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新一所一档-监所档案特色亮点配置")
    @LogRecordAnnotation(bizModule = "dam:featureConfig:update", operateType = LogOperateType.UPDATE, title = "更新一所一档-监所档案特色亮点配置",
    success = "更新一所一档-监所档案特色亮点配置成功", fail = "更新一所一档-监所档案特色亮点配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<String> updateFeatureConfig(@Valid @RequestBody FeatureConfigSaveReqVO updateReqVO) {
        return success(featureConfigService.updateFeatureConfig(updateReqVO));
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除一所一档-监所档案特色亮点配置")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:featureConfig:delete", operateType = LogOperateType.DELETE, title = "删除一所一档-监所档案特色亮点配置",
    success = "删除一所一档-监所档案特色亮点配置成功", fail = "删除一所一档-监所档案特色亮点配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteFeatureConfig(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           featureConfigService.deleteFeatureConfig(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得一所一档-监所档案特色亮点配置")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<FeatureConfigRespVO> getFeatureConfig(@RequestParam("id") String id) {
        FeatureConfigDO featureConfig = featureConfigService.getFeatureConfig(id);
        return success(BeanUtils.toBean(featureConfig, FeatureConfigRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得一所一档-监所档案特色亮点配置分页")
    public CommonResult<PageResult<FeatureConfigRespVO>> getFeatureConfigPage(@Valid @RequestBody FeatureConfigPageReqVO pageReqVO) {
        PageResult<FeatureConfigDO> pageResult = featureConfigService.getFeatureConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, FeatureConfigRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得一所一档-监所档案特色亮点配置列表")
    public CommonResult<List<FeatureConfigRespVO>> getFeatureConfigList(@Valid @RequestBody FeatureConfigListReqVO listReqVO) {
        List<FeatureConfigDO> list = featureConfigService.getFeatureConfigList(listReqVO);
        return success(BeanUtils.toBean(list, FeatureConfigRespVO.class));
    }
}
