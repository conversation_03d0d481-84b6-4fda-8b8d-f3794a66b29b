package com.rs.module.dam.entity.borrow;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO_;
import lombok.*;

import java.util.Date;

/**
 * 借阅人员档案审批 DO
 *
 * <AUTHOR>
 */
@TableName("dam_borrow_prisoner_approve")
@KeySequence("dam_borrow_prisoner_approve_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrisonerApproveDO extends BaseDO_ {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 借阅Id
     */
    private String borrowId;
    /**
     * 借阅人员Id
     */
    private String borrowPrisonerId;
    /**
     * 借阅审批Id
     */
    private String approveId;
    /**
     * 审批角色代码
     */
    private String approveRoleCode;
    /**
     * 审批角色名称
     */
    private String approveRoleName;
    /**
     * 审批状态(0:待审批,1:审批中,2:审批通过,3:审批不通过)
     */
    private String approveStatus;
    /**
     * 审批时间
     */
    private Date approveTime;
    /**
     * 审批意见
     */
    private String approveContent;

}
