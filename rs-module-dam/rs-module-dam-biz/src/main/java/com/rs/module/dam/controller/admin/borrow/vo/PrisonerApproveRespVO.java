package com.rs.module.dam.controller.admin.borrow.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 借阅人员档案审批 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonerApproveRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("借阅Id")
    private String borrowId;
    @ApiModelProperty("借阅人员Id")
    private String borrowPrisonerId;
    @ApiModelProperty("借阅审批Id")
    private String approveId;
    @ApiModelProperty("审批角色代码")
    private String approveRoleCode;
    @ApiModelProperty("审批角色名称")
    private String approveRoleName;
    @ApiModelProperty("审批状态(0:待审批,1:审批中,2:审批通过,3:审批不通过)")
    private String approveStatus;
    @ApiModelProperty("审批时间")
    private Date approveTime;
    @ApiModelProperty("审批意见")
    private String approveContent;
}
