package com.rs.module.dam.controller.admin.prison.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.annotation.GetFile;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.x.file.storage.core.FileInfo;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 一所一档-监所档案特色亮点配置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FeatureConfigRespVO extends BaseVO implements TransPojo{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("特色亮点标题")
    private String featureTitle;
    @ApiModelProperty("建设时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date constructionTime;
    @ApiModelProperty("特色亮点内容")
    private String featureContent;
    @GetFile(value = "id")
    public List<FileInfo> fileList;
}
