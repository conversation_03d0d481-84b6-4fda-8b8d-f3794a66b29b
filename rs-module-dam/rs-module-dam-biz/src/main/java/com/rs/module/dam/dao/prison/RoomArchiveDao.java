package com.rs.module.dam.dao.prison;

import com.rs.module.dam.controller.admin.prison.vo.RoomArchiveCountVo;
import com.rs.module.dam.controller.admin.prison.vo.RoomArchiveDetailsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 一室一档- Dao
*
* <AUTHOR>
*/
@Mapper
public interface RoomArchiveDao {

    List<RoomArchiveCountVo> getCount(@Param("roomId")String roomId, @Param("date") String date);

    //一室一档-监室事务业务列表
    List<RoomArchiveDetailsVo> getList(@Param("roomId")String roomId, @Param("date") String date, @Param("businessIds") List<Integer> businessIds);

}
