package com.rs.module.dam.controller.admin.event.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 一事一档-事件档案列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ArchiveListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("事件编号(规则：S+9位单位编号+8位日期+6位序号)")
    private String eventCode;

    @ApiModelProperty("事件类型（字典：ZD_YSYD_SJLX）")
    private String eventType;

    @ApiModelProperty("事件发生的具体时间")
    private Date[] occurrenceTime;

    @ApiModelProperty("事件发生的地点")
    private String eventLocation;

    @ApiModelProperty("对事件的详细描述")
    private String eventDescription;

    @ApiModelProperty("状态（字典：ZD_YSYD_SJZT）")
    private String eventStatus;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date[] approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("流程任务ID")
    private String taskId;


}
