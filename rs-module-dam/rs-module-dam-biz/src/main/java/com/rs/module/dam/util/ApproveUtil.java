package com.rs.module.dam.util;

import com.bsp.common.util.CollectionUtil;
import com.rs.module.dam.config.ApproveConfig;
import com.rs.module.dam.cons.ApproveFlowNode;
import com.rs.module.dam.cons.CommonConstants;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@Component
public class ApproveUtil implements ApplicationContextAware {


    private static ApproveConfig damConfig;

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        damConfig = context.getBean(ApproveConfig.class);
    }

    /**
     * 获取当前问题确认流程节点
     * @param roleCode 当前角色代码
     * @param type 审批类型(0:内部借阅,1:外部借阅)
     * @return ApproveFlowNode
     */
    public static ApproveFlowNode getCurrentConfirmFlowNode(String roleCode, String type) {
        List<ApproveFlowNode> nodeList = getFlowNode(type);
        ApproveFlowNode currentNode = null;
        if(CollectionUtil.isNotNull(nodeList)) {
            for(ApproveFlowNode node: nodeList) {
                if(node.getRoleCode().equals(roleCode)) {
                    currentNode = node;
                    break;
                }
            }
        }
        return currentNode;
    }

    /**
     * 获取下一级审批节点
     * @param nodeIndex 当前节点索引
     * @param type 审批类型(0:内部借阅,1:外部借阅)
     * @return ApproveFlowNode
     */
    public static ApproveFlowNode getApproveNextNode(Integer nodeIndex, String type) {
        List<ApproveFlowNode> nodeList = getFlowNode(type);
        Optional<ApproveFlowNode> min = nodeList.stream()
                .filter(p -> p.getIndex() > nodeIndex)
                .min(Comparator.comparingInt(p -> p.getIndex() - nodeIndex));
        return min.get();
    }

    /**
     * 获取下一级审批节点
     * @param roleCode 节点角色编号
     * @param type 审批类型(0:内部借阅,1:外部借阅)
     * @return ApproveFlowNode
     */
    public static ApproveFlowNode getApproveNextNode(String roleCode, String type) {
        ApproveFlowNode currentConfirmFlowNode = getCurrentConfirmFlowNode(roleCode, type);
        int nodeIndex = currentConfirmFlowNode.getIndex();
        List<ApproveFlowNode> nodeList = getFlowNode(type);
        Optional<ApproveFlowNode> min = nodeList.stream()
                .filter(p -> p.getIndex() > nodeIndex)
                .min(Comparator.comparingInt(p -> p.getIndex() - nodeIndex));
        if(!min.isPresent()) {
            return null;
        }
        return min.get();
    }

    /**
     * 获取第一级审批节点
     * @param type 审批类型(0:内部借阅,1:外部借阅)
     * @return ApproveFlowNode
     */
    public static ApproveFlowNode getFirstNode(String type) {
        List<ApproveFlowNode> nodeList = getFlowNode(type);
        Optional<ApproveFlowNode> min = nodeList.stream().min(Comparator.comparingInt(ApproveFlowNode::getIndex));
        return min.get();
    }

    private static List<ApproveFlowNode> getFlowNode(String type) {
        List<ApproveFlowNode> nodeList = damConfig.getInteriorApproveFlow();
        if (CommonConstants.APPROVE_TYPE_EXTERNAL.equals(type)) {
            nodeList = damConfig.getExternalApproveFlow();
        }
        return nodeList;
    }

}
