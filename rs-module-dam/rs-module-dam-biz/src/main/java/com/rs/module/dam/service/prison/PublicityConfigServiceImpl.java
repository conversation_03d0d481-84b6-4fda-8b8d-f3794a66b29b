package com.rs.module.dam.service.prison;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.controller.admin.prison.vo.PublicityConfigListReqVO;
import com.rs.module.dam.controller.admin.prison.vo.PublicityConfigPageReqVO;
import com.rs.module.dam.controller.admin.prison.vo.PublicityConfigSaveReqVO;
import com.rs.module.dam.dao.prison.PublicityConfigDao;
import com.rs.module.dam.entity.prison.FeatureConfigDO;
import com.rs.module.dam.entity.prison.PublicityConfigDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;


/**
 * 一所一档-监所档案宣传报道配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PublicityConfigServiceImpl extends BaseServiceImpl<PublicityConfigDao, PublicityConfigDO> implements PublicityConfigService {

    @Resource
    private PublicityConfigDao publicityConfigDao;

    @Override
    public String createPublicityConfig(PublicityConfigSaveReqVO createReqVO) {
        // 插入
        PublicityConfigDO publicityConfig = BeanUtils.toBean(createReqVO, PublicityConfigDO.class);
        publicityConfigDao.insert(publicityConfig);
        // 返回
        return publicityConfig.getId();
    }

    @Override
    public String updatePublicityConfig(PublicityConfigSaveReqVO updateReqVO) {
        // 校验存在
        validatePublicityConfigExists(updateReqVO.getId());
        // 更新
        PublicityConfigDO updateObj = BeanUtils.toBean(updateReqVO, PublicityConfigDO.class);
        publicityConfigDao.updateById(updateObj);
        return updateObj.getId();
    }

    @Override
    public void deletePublicityConfig(String id) {
        // 校验存在
        validatePublicityConfigExists(id);
        // 删除
        publicityConfigDao.deleteById(id);
    }

    private void validatePublicityConfigExists(String id) {
        if (publicityConfigDao.selectById(id) == null) {
            throw new ServerException("一所一档-监所档案宣传报道配置数据不存在");
        }
    }

    @Override
    public PublicityConfigDO getPublicityConfig(String id) {
        return publicityConfigDao.selectById(id);
    }

    @Override
    public List<PublicityConfigDO> getByPrisonCode(String prisonCode) {
        if (StringUtil.isNullBlank(prisonCode)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<PublicityConfigDO>().eq(PublicityConfigDO::getOrgCode, prisonCode));
    }

    @Override
    public PageResult<PublicityConfigDO> getPublicityConfigPage(PublicityConfigPageReqVO pageReqVO) {
        return publicityConfigDao.selectPage(pageReqVO);
    }

    @Override
    public List<PublicityConfigDO> getPublicityConfigList(PublicityConfigListReqVO listReqVO) {
        return publicityConfigDao.selectList(listReqVO);
    }


}
