package com.rs.module.dam.controller.admin.material;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.entity.material.PdfBatchDO;
import com.rs.module.dam.service.material.PdfBatchService;
import com.rs.module.dam.vo.material.PdfBatchListReqVO;
import com.rs.module.dam.vo.material.PdfBatchPageReqVO;
import com.rs.module.dam.vo.material.PdfBatchRespVO;
import com.rs.module.dam.vo.material.PdfBatchSaveReqVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

@Api(tags = "材料处理 - pdf处理批次")
@ApiIgnore
@RestController
@RequestMapping("/dam/material/pdfBatch")
@Validated
public class PdfBatchController {

    @Resource
    private PdfBatchService pdfBatchService;

    @PostMapping("/create")
    @ApiOperation(value = "创建pdf处理批次")
    @LogRecordAnnotation(bizModule = "dam:pdfBatch:create", operateType = LogOperateType.CREATE, title = "创建pdf处理批次",
    success = "创建pdf处理批次成功", fail = "创建pdf处理批次失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPdfBatch(@Valid @RequestBody PdfBatchSaveReqVO createReqVO) {
        return success(pdfBatchService.createPdfBatch(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新pdf处理批次")
    @LogRecordAnnotation(bizModule = "dam:pdfBatch:update", operateType = LogOperateType.UPDATE, title = "更新pdf处理批次",
    success = "更新pdf处理批次成功", fail = "更新pdf处理批次失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePdfBatch(@Valid @RequestBody PdfBatchSaveReqVO updateReqVO) {
        pdfBatchService.updatePdfBatch(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除pdf处理批次")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:pdfBatch:delete", operateType = LogOperateType.DELETE, title = "删除pdf处理批次",
    success = "删除pdf处理批次成功", fail = "删除pdf处理批次失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePdfBatch(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           pdfBatchService.deletePdfBatch(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得pdf处理批次")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:pdfBatch:get", operateType = LogOperateType.QUERY, title = "获取pdf处理批次", bizNo = "{{#id}}", success = "获取pdf处理批次成功", fail = "获取pdf处理批次失败", extraInfo = "{{#id}}")
    public CommonResult<PdfBatchRespVO> getPdfBatch(@RequestParam("id") String id) {
        PdfBatchDO pdfBatch = pdfBatchService.getPdfBatch(id);
        return success(BeanUtils.toBean(pdfBatch, PdfBatchRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得pdf处理批次分页")
    @LogRecordAnnotation(bizModule = "dam:pdfBatch:page", operateType = LogOperateType.QUERY, title = "获得pdf处理批次分页",
    success = "获得pdf处理批次分页成功", fail = "获得pdf处理批次分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<PdfBatchRespVO>> getPdfBatchPage(@Valid @RequestBody PdfBatchPageReqVO pageReqVO) {
        PageResult<PdfBatchDO> pageResult = pdfBatchService.getPdfBatchPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PdfBatchRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得pdf处理批次列表")
    @LogRecordAnnotation(bizModule = "dam:pdfBatch:list", operateType = LogOperateType.QUERY, title = "获得pdf处理批次列表",
    success = "获得pdf处理批次列表成功", fail = "获得pdf处理批次列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<PdfBatchRespVO>> getPdfBatchList(@Valid @RequestBody PdfBatchListReqVO listReqVO) {
    List<PdfBatchDO> list = pdfBatchService.getPdfBatchList(listReqVO);
        return success(BeanUtils.toBean(list, PdfBatchRespVO.class));
    }

}
