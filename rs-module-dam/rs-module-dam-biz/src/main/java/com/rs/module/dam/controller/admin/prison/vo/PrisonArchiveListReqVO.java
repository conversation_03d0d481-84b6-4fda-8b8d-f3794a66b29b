package com.rs.module.dam.controller.admin.prison.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@ApiModel(description = "管理后台 - 一所一档-监所档案列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonArchiveListReqVO extends BaseVO {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("监所的名称")
    private String prisonName;

    @ApiModelProperty("监所单位机构代码")
    private String prisonCode;

    @ApiModelProperty("监所联系电话")
    private String contactPhone;

    @ApiModelProperty("电子邮件地址")
    private String email;

    @ApiModelProperty("邮政编码")
    private String postCode;

    @ApiModelProperty("监所地址")
    private String address;

    @ApiModelProperty("对监所简介")
    private String remarks;

    @ApiModelProperty("监所背景图片存储地址")
    private String backgroundImageUrl;

    @ApiModelProperty("监所的建筑总面积")
    private BigDecimal buildingArea;

    @ApiModelProperty("监室数量")
    private Integer monitoringRoomCount;

    @ApiModelProperty("监室总可容纳人数")
    private Integer monitoringRoomCapacity;

    @ApiModelProperty("监所内功能室数量")
    private Integer functionRoomCount;

    @ApiModelProperty("功能室配置情况")
    private List<PrisonArchiveConfigVO> functionRoomConfigs;

    @ApiModelProperty("装备配备情况")
    private List<PrisonArchiveConfigVO> equipmentConfigs;

    @ApiModelProperty("装备数量")
    private Integer equipmentCount;

    @ApiModelProperty("警力总人数")
    private Integer totalPoliceForce;

    @ApiModelProperty("民警总数量")
    private Integer policeOfficerCount;

    @ApiModelProperty("辅警总数量")
    private Integer auxiliaryPoliceCount;

    @ApiModelProperty("累计羁押人数")
    private Integer totalDetainedCount;

    @ApiModelProperty("累计办理案件数量")
    private Integer totalCaseCount;

    @ApiModelProperty("累计讯 / 询问嫌疑人数")
    private Integer totalInterrogatedSuspectCount;

    @ApiModelProperty("累计管理涉案财物件数")
    private Integer totalSeizedPropertyCount;

    @ApiModelProperty("累计管理涉案财物总价值")
    private BigDecimal totalSeizedPropertyValue;

}
