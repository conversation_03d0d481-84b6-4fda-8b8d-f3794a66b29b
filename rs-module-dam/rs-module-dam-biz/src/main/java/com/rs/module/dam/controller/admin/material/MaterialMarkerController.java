package com.rs.module.dam.controller.admin.material;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.util.StringUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.entity.material.MaterialMarkerDO;
import com.rs.module.dam.service.material.MaterialMarkerService;
import com.rs.module.dam.vo.material.MaterialMarkerListReqVO;
import com.rs.module.dam.vo.material.MaterialMarkerPageReqVO;
import com.rs.module.dam.vo.material.MaterialMarkerRespVO;
import com.rs.module.dam.vo.material.MaterialMarkerSaveReqVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@Api(tags = "材料处理 - 卷宗材料标注")
@RestController
@RequestMapping("/dam/material/materialMarker")
@Validated
public class MaterialMarkerController {

    @Resource
    private MaterialMarkerService materialMarkerService;

    @PostMapping("/create")
    @ApiOperation(value = "创建卷宗材料标注")
    @LogRecordAnnotation(bizModule = "dam:materialMarker:create", operateType = LogOperateType.CREATE, title = "创建卷宗材料标注",
    success = "创建卷宗材料标注成功", fail = "创建卷宗材料标注失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createMaterialMarker(@Valid @RequestBody MaterialMarkerSaveReqVO createReqVO) {
        return success(materialMarkerService.createMaterialMarker(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新卷宗材料标注")
    @LogRecordAnnotation(bizModule = "dam:materialMarker:update", operateType = LogOperateType.UPDATE, title = "更新卷宗材料标注",
    success = "更新卷宗材料标注成功", fail = "更新卷宗材料标注失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateMaterialMarker(@Valid @RequestBody MaterialMarkerSaveReqVO updateReqVO) {
        materialMarkerService.updateMaterialMarker(updateReqVO);
        return success(true);
    }
    
    @PostMapping("/save")
    @ApiOperation(value = "保存卷宗材料标注")
    @LogRecordAnnotation(bizModule = "dam:materialMarker:save", operateType = LogOperateType.CREATE, title = "保存卷宗材料标注",
    success = "保存卷宗材料标注成功", fail = "保存卷宗材料标注失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<?> saveMaterialMarker(@Valid @RequestBody MaterialMarkerSaveReqVO createReqVO) {
    	if(StringUtil.isNotEmpty(createReqVO.getId())) {    		
    		materialMarkerService.updateMaterialMarker(createReqVO);
            return success(true);
    	}
    	else {
    		return success(materialMarkerService.createMaterialMarker(createReqVO));
    	}
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除卷宗材料标注")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "dam:materialMarker:delete", operateType = LogOperateType.DELETE, title = "删除卷宗材料标注",
    success = "删除卷宗材料标注成功", fail = "删除卷宗材料标注失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteMaterialMarker(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           materialMarkerService.deleteMaterialMarker(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得卷宗材料标注")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<MaterialMarkerRespVO> getMaterialMarker(@RequestParam("id") String id) {
        MaterialMarkerDO materialMarker = materialMarkerService.getMaterialMarker(id);
        return success(BeanUtils.toBean(materialMarker, MaterialMarkerRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得卷宗材料标注分页")
    public CommonResult<PageResult<MaterialMarkerRespVO>> getMaterialMarkerPage(@Valid @RequestBody MaterialMarkerPageReqVO pageReqVO) {
        PageResult<MaterialMarkerDO> pageResult = materialMarkerService.getMaterialMarkerPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MaterialMarkerRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得卷宗材料标注列表")
    public CommonResult<List<MaterialMarkerRespVO>> getMaterialMarkerList(@Valid @RequestBody MaterialMarkerListReqVO listReqVO) {
        List<MaterialMarkerDO> list = materialMarkerService.getMaterialMarkerList(listReqVO);
        return success(BeanUtils.toBean(list, MaterialMarkerRespVO.class));
    }
    
    @PostMapping("/findMarkersByJgrybm")
    @ApiOperation(value = "获取监管人员的材料标注")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "jgrybm", value = "监管人员编码"),
		@ApiImplicitParam(name = "content", value = "标注内容")
	})
    public CommonResult<?> findMarkersByJgrybm(@RequestParam("jgrybm") String jgrybm,
    		@RequestParam(value = "content", required = false) String content) {
    	try {
    		QueryWrapper<MaterialMarkerDO> wrapper = new QueryWrapper<>();
    		wrapper.eq("jgrybm", jgrybm);
    		wrapper.orderByDesc("add_time");
    		
    		//标注内容
    		if(StringUtil.isNotEmpty(content)){
    			wrapper.like("content", content);
    		}
    		
    		List<MaterialMarkerDO> markerList = materialMarkerService.list(wrapper);
    		return CommonResult.success(markerList);
    	}
    	catch(Exception e) {
    		return CommonResult.error("获取监管人员的材料标注出现异常：" + e.getMessage());
    	}
    }
}
