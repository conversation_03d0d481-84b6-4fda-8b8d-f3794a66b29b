package com.rs.module.dam.controller.admin.prison.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 一所一档-监所档案宣传报道配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PublicityConfigSaveReqVO extends BaseVO{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty(" 报道标题")
    @NotEmpty(message = " 报道标题不能为空")
    private String publicityTitle;

    @ApiModelProperty("报道时间")
    @NotNull(message = "报道时间不能为空")
    private Date publicityTime;

    @ApiModelProperty("报道内容")
    @NotEmpty(message = "报道内容不能为空")
    private String publicityContent;

}
