package com.rs.module.dam.dao.sys;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.dam.entity.sys.CatalogMappingDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.dam.controller.admin.sys.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 目录文书映射 Dao
*
* <AUTHOR>
*/
@Mapper
public interface CatalogMappingDao extends IBaseDao<CatalogMappingDO> {


    default PageResult<CatalogMappingDO> selectPage(CatalogMappingPageReqVO reqVO) {
        Page<CatalogMappingDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<CatalogMappingDO> wrapper = new LambdaQueryWrapperX<CatalogMappingDO>()
            .eqIfPresent(CatalogMappingDO::getTemplateId, reqVO.getTemplateId())
            .eqIfPresent(CatalogMappingDO::getCatalogId, reqVO.getCatalogId())
            .eqIfPresent(CatalogMappingDO::getAppId, reqVO.getAppId())
            .eqIfPresent(CatalogMappingDO::getFormId, reqVO.getFormId())
            .eqIfPresent(CatalogMappingDO::getFormName, reqVO.getFormName());

        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(CatalogMappingDO::getAddTime);
        }
        Page<CatalogMappingDO> catalogMappingPage = selectPage(page, wrapper);
        return new PageResult<>(catalogMappingPage.getRecords(), catalogMappingPage.getTotal());
    }

    default List<CatalogMappingDO> selectList(CatalogMappingListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CatalogMappingDO>()
            .eqIfPresent(CatalogMappingDO::getTemplateId, reqVO.getTemplateId())
            .eqIfPresent(CatalogMappingDO::getCatalogId, reqVO.getCatalogId())
            .eqIfPresent(CatalogMappingDO::getAppId, reqVO.getAppId())
            .eqIfPresent(CatalogMappingDO::getFormId, reqVO.getFormId())
            .eqIfPresent(CatalogMappingDO::getFormName, reqVO.getFormName())
        .orderByDesc(CatalogMappingDO::getAddTime));
    }


}
