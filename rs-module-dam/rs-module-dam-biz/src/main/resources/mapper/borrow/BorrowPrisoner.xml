<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.dam.dao.borrow.BorrowPrisonerDao">

    <select id="getReapplyDetail" resultType="java.util.HashMap">
        select t1.jgryxm xm,t1.dabh,t1.jgrybm,t1.ajbh,t1.ajmc,t2.gdsj,t3.jsh,t3.room_name
        from dam_borrow_prisoner t1 left join dam_prisoner_info t2 on t1.dabh = t2.dabh and t2.is_del = '0'
        left join vw_acp_pm_prisoner t3 on t1.jgrybm = t3.jgrybm and t3.is_del = '0'
        where t1.id = #{prisonerId}
    </select>

</mapper>
