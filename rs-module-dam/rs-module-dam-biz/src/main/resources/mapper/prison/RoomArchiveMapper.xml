<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.dam.dao.prison.RoomArchiveDao">

    <select id="getCount" resultType="com.rs.module.dam.controller.admin.prison.vo.RoomArchiveCountVo">
        select *
        from (
                     select COALESCE(count(1), 0) as count,1 as businessType,'新入所' as businessName
                     from vw_acp_pm_prisoner_in where is_del = 0 and jsh =#{roomId} and to_char(rssj,'yyyy-mm-dd') = #{date}
                 UNION ALL
                     select COALESCE(count(1), 0) as count,2 as businessType,'出所' as businessName
                     from vw_acp_pm_prisoner_out where is_del = 0 and jsh =#{roomId} and  to_char(cssj,'yyyy-mm-dd') = #{date}
                 UNION ALL
                     select COALESCE(count(1), 0) as count,3 as businessType,'调入监室' as businessName
                     from acp_gj_prison_room_change where is_del = 0 and new_room_id = #{roomId} and to_char(room_change_time,'yyyy-mm-dd') =#{date}
                 UNION ALL
                     select COALESCE(count(1), 0) as count,4 as businessType,'调出监室' as businessName
                     from acp_gj_prison_room_change where is_del = 0 and old_room_id=#{roomId} and to_char(room_change_time,'yyyy-mm-dd') =#{date}
                 UNION ALL
                     select COALESCE(count(1), 0) as count,6 as businessType,'提讯' as businessName
                     from acp_wb_arraignment a INNER JOIN vw_acp_pm_prisoner b ON a.jgrybm = b.jgrybm
                     where b.jsh = #{roomId} and to_char(a.escorting_time,'yyyy-mm-dd') =#{date}
                 UNION ALL
                     select COALESCE(count(1), 0) as count,7 as businessType,'提解' as businessName
                     from acp_wb_escort a INNER JOIN vw_acp_pm_prisoner b ON a.jgrybm = b.jgrybm
                     where b.jsh = #{roomId} and to_char(a.arraignment_start_time,'yyyy-mm-dd') =#{date}
                 UNION ALL
                     select COALESCE(count(1), 0) as count,8 as businessType,'律师会见' as businessName
                     from acp_wb_lawyer_meeting a INNER JOIN vw_acp_pm_prisoner b ON a.jgrybm = b.jgrybm
                     where b.jsh = #{roomId} and to_char(a.appointment_time,'yyyy-mm-dd') =#{date}
                 UNION ALL
                     select COALESCE(count(1), 0) as count,9 as businessType,'家属会见' as businessName
                     from acp_wb_family_meeting a INNER JOIN vw_acp_pm_prisoner b ON a.jgrybm = b.jgrybm
                     where b.jsh = #{roomId} and to_char(a.apply_meeting_start_time,'yyyy-mm-dd') =#{date}
                 UNION ALL
                     select COALESCE(count(1), 0) as count,10 as businessType,'所内就医' as businessName
                     from ihc_ipm_outpatient a INNER JOIN vw_acp_pm_prisoner b ON a.jgrybm = b.jgrybm
                     where  b.jsh = #{roomId} and to_char(a.disease_time,'yyyy-mm-dd') =#{date}
                 UNION ALL
                     select COALESCE(count(1), 0) as count,11 as businessType,'出所就医' as businessName
                     from acp_pm_out_prison_treatment a INNER JOIN vw_acp_pm_prisoner b ON a.jgrybm = b.jgrybm
                     where b.jsh = #{roomId} and to_char(a.leave_time,'yyyy-mm-dd')=#{date}
                 UNION ALL
                     select COALESCE(count(1), 0) as count,12 as businessType,'管教报备' as businessName
                     from pam_info_report where status in ('3','4') and  object_id is not null
                     and room_id = #{roomId} and to_char(approver_time,'yyyy-mm-dd')=#{date}
                 UNION ALL
                     select COALESCE(count(1), 0) as count,13 as businessType,'仓内屏报备' as businessName
                     from pam_info_report a where (object_id is null or object_id='')
                     and a.room_id= #{roomId} and to_char(a.report_time,'yyyy-mm-dd')=#{date}
                 UNION ALL
                     select COALESCE(count(1), 0) as count,14 as businessType,'管教变更' as businessName from (
                         select to_char(a.add_time, 'yyyy-MM-dd hh24:mi:ss')
                         from acp_pm_prison_room_warder a where
                         a.police_name != a.history_police_name
                         and a.room_id= #{roomId} and to_char(a.add_time,'yyyy-mm-dd')=#{date}
                         group by to_char(a.add_time, 'yyyy-MM-dd hh24:mi:ss')
                     ) t
                 UNION ALL
                     SELECT COALESCE(count(1), 0) as count,15 as businessType,'所情登记' as businessName
                     FROM acp_pi_prison_event  where status in ('0','3','5') and area_id=#{roomId}
                     and to_char(happen_time,'yyyy-mm-dd')=#{date}
                 UNION ALL
                     SELECT COALESCE(count(1), 0) as count,16 as businessType,'戒具使用' as businessName
                     FROM acp_gj_equipment_use a inner JOIN vw_acp_pm_prisoner b ON A.jgrybm = b.jgrybm where a.is_del = 0
                     and A.status in ('03', '04', '05', '06', '07') and b.jsh = #{roomId} and to_char(a.actual_start_time,'yyyy-mm-dd')=#{date}
                 UNION ALL
                     SELECT COALESCE(count(1), 0) as count,17 as businessType,'戒具解除' as businessName
                     FROM acp_gj_equipment_use a inner JOIN vw_acp_pm_prisoner b ON A.jgrybm = b.jgrybm where a.is_del = 0
                     and A.status = '08' and b.jsh = #{roomId} and to_char(a.actual_end_time,'yyyy-mm-dd')=#{date}
                 UNION ALL
                     SELECT COALESCE(count(1), 0) as count,18 as businessType,'耳目布建' as businessName
                     FROM acp_gj_undercover a inner JOIN vw_acp_pm_prisoner b on a.jgrybm = b.jgrybm
                     where a.status = '3' and b.jsh = #{roomId} and to_char(a.start_time,'yyyy-mm-dd')=#{date}
                 UNION ALL
                     SELECT COALESCE(count(1), 0) as count,19 as businessType,'耳目撤销' as businessName
                     FROM acp_gj_undercover a inner JOIN vw_acp_pm_prisoner b on a.jgrybm = b.jgrybm
                     where a.status = '4' and b.jsh = #{roomId} and to_char(a.end_time,'yyyy-mm-dd')=#{date}
                 UNION ALL
                     SELECT COALESCE(count(1), 0) as count,21 as businessType,'面对面管理' as businessName
                     FROM acp_gj_face_to_face a INNER JOIN acp_pm_area_prison_room AS t2 ON a.room_id = t2.id
                     where a.is_del = 0 and a.room_id = #{roomId} and to_char(a.check_time,'yyyy-mm-dd') =#{date}
                 UNION ALL
                     select COALESCE(count(1), 0) as count,22 as businessType,'安全检查' as businessName
                     from pam_daily_clean a where is_del = 0 and check_type = '2'
                     and a.check_room_id like concat('%', #{roomId}, '%') and to_char(a.check_time,'yyyy-mm-dd') =#{date}
                 UNION ALL
                     SELECT COALESCE(count(1), 0) as count,23 as businessType,'监室点名' as businessName
                     FROM pam_represent a where a.present_no is not NULL AND a.room_id =#{roomId}
                     and to_char(a.start_time,'yyyy-mm-dd') =#{date}
        ) t where t.count >0 order by t.count desc
    </select>

    <select id="getList" resultType="com.rs.module.dam.controller.admin.prison.vo.RoomArchiveDetailsVo">
        select t.*
        from (
        <trim suffixOverrides="UNION ALL">
            <if test="businessIds.size()==0 or businessIds.contains(1)">
                select 1 as businessType,'新入所' as businessName, jgrybm as businessId,rssj as businessTime,
                xm as fieldOne,sxzm as fieldTwo,sshj as fieldThree,jgrybm as prisonerId
                from vw_acp_pm_prisoner_in
                where jsh = #{roomId}
                <if test="date!=null and date!=''">
                    and to_char(rssj,'yyyy-mm-dd') =#{date}
                </if>
                UNION ALL
            </if>
            <if test="businessIds.size()==0 or businessIds.contains(2)">
                select 2 as businessType,'出所' as businessName,jgrybm as businessId,cssj as businessTime,
                xm as fieldOne,csyy as fieldTwo,csqx as fieldThree, jgrybm as prisonerId
                from vw_acp_pm_prisoner_out
                where jsh = #{roomId}
                <if test="date!=null and date!=''">
                    and to_char(cssj,'yyyy-mm-dd')=#{date}
                </if>
                UNION ALL
            </if>
            <if test="businessIds.size()==0 or businessIds.contains(3)">
                select 3 as businessType,'调入监室' as businessName,id as businessId,room_change_time as businessTime,
                new_room_name as fieldOne,old_room_name as fieldTwo,null as fieldThree,new_room_id as prisonerId
                from acp_gj_prison_room_change
                where is_del = 0 and new_room_id = #{roomId}
                <if test="date!=null and date!=''">
                    and to_char(room_change_time,'yyyy-mm-dd') =#{date}
                </if>
                UNION ALL
            </if>
            <if test="businessIds.size()==0 or businessIds.contains(4)">
                select 4 as businessType,'调出监室' as businessName,id as businessId,room_change_time as businessTime,
                new_room_name as fieldOne,old_room_name as fieldTwo,null as fieldThree,new_room_id as prisonerId
                from acp_gj_prison_room_change
                where is_del = 0 and old_room_id = #{roomId}
                <if test="date!=null and date!=''">
                    and to_char(room_change_time,'yyyy-mm-dd') =#{date}
                </if>
                UNION ALL
            </if>
            <if test="businessIds.size()==0 or businessIds.contains(6)">
                select 6 as businessType,'提讯' as businessName,a.id as businessId,a.escorting_time as businessTime,
                b.xm as fieldOne,a.tjjgmc as fieldTwo,a.arraignment_reason as fieldThree,a.jgrybm as prisonerId
                from acp_wb_arraignment a INNER JOIN vw_acp_pm_prisoner b ON a.jgrybm = b.jgrybm
                where b.jsh = #{roomId}
                <if test="date!=null and date!=''">
                    and to_char(a.escorting_time,'yyyy-mm-dd') =#{date}
                </if>
                UNION ALL
            </if>
            <if test="businessIds.size()==0 or businessIds.contains(7)">
                select 7 as businessType,'提解' as businessName,a.id as businessId,a.arraignment_start_time as businessTime,
                b.xm as fieldOne,a.tjjgmc as fieldTwo,a.escort_reason as fieldThree,a.jgrybm as prisonerId
                from acp_wb_escort a INNER JOIN vw_acp_pm_prisoner b ON a.jgrybm = b.jgrybm
                where b.jsh = #{roomId}
                <if test="date!=null and date!=''">
                    and to_char(a.arraignment_start_time,'yyyy-mm-dd') =#{date}
                </if>
                UNION ALL
            </if>
            <if test="businessIds.size()==0 or businessIds.contains(8)">
                select 8 as businessType,'律师会见' as businessName,a.id as businessId,a.appointment_time as businessTime,
                b.xm as fieldOne,a.lawyer1_name as fieldTwo,a.lawyer1_firm as fieldThree,a.jgrybm as prisonerId
                from acp_wb_lawyer_meeting a INNER JOIN vw_acp_pm_prisoner b ON a.jgrybm = b.jgrybm
                where b.jsh = #{roomId}
                <if test="date!=null and date!=''">
                    and to_char(a.appointment_time,'yyyy-mm-dd') =#{date}
                </if>
                UNION ALL
            </if>
            <if test="businessIds.size()==0 or businessIds.contains(9)">
                select 9 as businessType,'家属会见' as businessName, a.id as businessId,a.apply_meeting_start_time as businessTime,
                b.xm as fieldOne,a.family_member1_name as fieldTwo,a.family_member1_relationship as fieldThree,a.jgrybm as prisonerId
                from acp_wb_family_meeting a INNER JOIN vw_acp_pm_prisoner b ON a.jgrybm = b.jgrybm
                where b.jsh = #{roomId}
                <if test="date!=null and date!=''">
                    and to_char(a.apply_meeting_start_time,'yyyy-mm-dd') =#{date}
                </if>
                UNION ALL
            </if>
            <if test="businessIds.size()==0 or businessIds.contains(10)">
                select 10 as businessType,'所内就医' as businessName,a.id as businessId,a.disease_time as businessTime,
                b.xm as fieldOne,a.main_complaint as fieldTwo,a.illness_resume as fieldThree,a.jgrybm as prisonerId
                from ihc_ipm_outpatient a INNER JOIN vw_acp_pm_prisoner b ON a.jgrybm = b.jgrybm
                where b.jsh = #{roomId}
                <if test="date!=null and date!=''">
                    and to_char(a.disease_time,'yyyy-mm-dd') =#{date}
                </if>
                UNION ALL
            </if>
            <if test="businessIds.size()==0 or businessIds.contains(11)">
                select 11 as businessType,'出所就医' as businessName,a.id as businessId,a.cfsj as businessTime,
                b.xm as fieldOne,a.symptom_desc as fieldTwo,a.ywzryj as fieldThree ,a.jgrybm as prisonerId
                from acp_pm_out_prison_treatment a INNER JOIN vw_acp_pm_prisoner b ON a.jgrybm = b.jgrybm
                where b.jsh = #{roomId}
                <if test="date!=null and date!=''">
                    and to_char(a.cfsj,'yyyy-mm-dd')=#{date}
                </if>
                UNION ALL
            </if>
            <if test="businessIds.size()==0 or businessIds.contains(12)">
                select 12 as businessType,'管教报备' as businessName,id as businessId,approver_time as businessTime,
                reporter_name as fieldOne,report_content as fieldTwo,null as fieldThree,null as prisonerId
                from pam_info_report
                where status not in('3','4') and  object_id is not null and room_id= #{roomId}
                <if test="date!=null and date!=''">
                    and to_char(approver_time,'yyyy-mm-dd')=#{date}
                </if>
                UNION ALL
            </if>
            <if test="businessIds.size()==0 or businessIds.contains(13)">
                select 13 as businessType,'仓内屏报备' as businessName,id as businessId,report_time as businessTime,
                reporter_name as fieldOne,report_type as fieldTwo,report_content as fieldThree,null as prisonerId
                from pam_info_report a
                where (object_id is null or object_id='') and a.room_id= #{roomId}
                <if test="date!=null and date!=''">
                    and to_char(a.report_time,'yyyy-mm-dd')=#{date}
                </if>
                UNION ALL
            </if>
            <if test="businessIds.size()==0 or businessIds.contains(14)">
                <include refid="businessIds_14"/>
                UNION ALL
            </if>
            <if test="businessIds.size()==0 or businessIds.contains(15)">
                SELECT 15 as businessType,'所情登记' as businessName,id as businessId,happen_time as businessTime,
                event_src as fieldOne,event_level as fieldTwo,event_type as fieldThree ,null as prisonerId
                FROM acp_pi_prison_event
                where status in ('0','3','5') and area_id=#{roomId}
                <if test="date!=null and date!=''">
                    and to_char(happen_time,'yyyy-mm-dd')=#{date}
                </if>
                UNION ALL
            </if>
            <if test="businessIds.size()==0 or businessIds.contains(16)">
                SELECT 16 as businessType,'戒具使用' as businessName,a.id as businessId,actual_start_time as businessTime,
                b.xm as fieldOne,punishment_tool_type as fieldTwo,use_days as fieldThree,A.jgrybm as prisonerId
                FROM acp_gj_equipment_use a inner JOIN vw_acp_pm_prisoner b ON A.jgrybm = b.jgrybm
                where a.is_del = 0 and A.status in ('03', '04', '05', '06', '07') and b.jsh = #{roomId}
                <if test="date!=null and date!=''">
                    and to_char(a.actual_start_time,'yyyy-mm-dd')=#{date}
                </if>
                UNION ALL
            </if>
            <if test="businessIds.size()==0 or businessIds.contains(17)">
                select 17 as businessType,'戒具解除' as businessName,a.id as businessId,actual_end_time as businessTime,
                b.xm as fieldOne,execute_situation as fieldTwo,null as fieldThree,A.jgrybm as prisonerId
                FROM acp_gj_equipment_use a inner JOIN vw_acp_pm_prisoner b ON A.jgrybm = b.jgrybm
                where a.is_del = 0 and A.status = '08' and b.jsh = #{roomId}
                <if test="date!=null and date!=''">
                    and to_char(a.actual_end_time,'yyyy-mm-dd')=#{date}
                </if>
                UNION ALL
            </if>
            <if test="businessIds.size()==0 or businessIds.contains(18)">
                SELECT 18 as businessType,'耳目布建' as businessName,a.id as businessId,start_time as businessTime,
                b.xm as fieldOne,null as fieldTwo,a.add_user_name as fieldThree,a.jgrybm as prisonerId
                FROM acp_gj_undercover a inner JOIN vw_acp_pm_prisoner b on a.jgrybm = b.jgrybm
                where a.status = '3' and b.jsh = #{roomId}
                <if test="date!=null and date!=''">
                    and to_char(a.start_time,'yyyy-mm-dd')=#{date}
                </if>
                UNION ALL
            </if>
            <if test="businessIds.size()==0 or businessIds.contains(19)">
                SELECT 19 as businessType,'耳目撤销' as businessName,a.id as businessId,end_time as businessTime,
                b.xm as fieldOne,null as fieldTwo,a.add_user_name as fieldThree,a.jgrybm as prisonerId
                FROM acp_gj_undercover a inner JOIN vw_acp_pm_prisoner b on a.jgrybm = b.jgrybm
                where a.status = '4' and b.jsh = #{roomId}
                <if test="date!=null and date!=''">
                    and to_char(a.end_time,'yyyy-mm-dd')=#{date}
                </if>
                UNION ALL
            </if>
            <if test="businessIds.size()==0 or businessIds.contains(21)">
                SELECT 21 as businessType,'面对面管理' as businessName,a.id as businessId,check_time as businessTime,
                check_police as fieldOne,situation_record as fieldTwo,null as fieldThree,null as prisonerId
                FROM acp_gj_face_to_face a INNER JOIN acp_pm_area_prison_room AS t2 ON a.room_id = t2.id
                where a.is_del = 0 and a.room_id = #{roomId}
                <if test="date!=null and date!=''">
                    and to_char(a.check_time,'yyyy-mm-dd') =#{date}
                </if>
                UNION ALL
            </if>
            <if test="businessIds.size()==0 or businessIds.contains(22)">
                select 22 as businessType,'安全检查' as businessName ,a.id as businessId,check_time as businessTime,
                leader_user_name as fieldOne,check_type as fieldTwo,check_content as fieldThree ,null as prisonerId
                from pam_daily_clean a
                where is_del = 0 and check_type = '2' and a.check_room_id like concat('%', #{roomId}, '%')
                <if test="date!=null and date!=''">
                    and to_char(a.check_time,'yyyy-mm-dd') =#{date}
                </if>
                UNION ALL
            </if>
            <if test="businessIds.size()==0 or businessIds.contains(23)">
                SELECT 23 as businessType,'监室点名' as businessName,a.id as businessId,start_time as businessTime,
                cast(error_num as varchar) as fieldOne,cast(out_num+report_num as varchar) as fieldTwo,
                cast(present_num as varchar) as fieldThree,null as prisonerId
                FROM pam_represent a
                where a.present_no is not NULL AND a.room_id = #{roomId}
                <if test="date!=null and date!=''">
                    and to_char(a.start_time,'yyyy-mm-dd') =#{date}
                </if>
                UNION ALL
            </if>
        </trim>
        ) t order by t.businessTime
    </select>

    <sql id="businessIds_14">
        select 14 as businessType,'管教变更' as businessName,null as businessId,to_timestamp(t.businessTime, 'yyyy-MM-dd hh24:mi:ss') as businessTime,
        (
            select string_agg(t1.history_police_name, ',') from (
                select
                DISTINCT c.history_police_name,
                c.user_type
                from
                acp_pm_prison_room_warder c
                where c.room_id = #{roomId}
                and to_char(c.add_time, 'yyyy-MM-dd hh24:mi:ss')=t.businessTime
                order by c.user_type  desc
            ) t1
        ) as fieldOne,
        (
            select
            string_agg(b.police_name, ',')
            from
            acp_pm_prison_room_warder b
            where
            b.room_id = #{roomId}
            and to_char(b.add_time, 'yyyy-MM-dd hh24:mi:ss')=t.businessTime
        ) as fieldTwo
        ,null as fieldThree,null as prisonerId
        from (
            select to_char(a.add_time, 'yyyy-MM-dd hh24:mi:ss') as businessTime
            from acp_pm_prison_room_warder a where
            a.police_name != a.history_police_name
            and a.room_id = #{roomId}
            <if test="date!=null and date!=''">
                and to_char(a.add_time,'yyyy-mm-dd')=#{date}
            </if>
            group by to_char(a.add_time, 'yyyy-MM-dd hh24:mi:ss')
        ) t
    </sql>

</mapper>
