conf:
  server:
    ip: *************
    port:
      dam-convert: 9510
  middleware:
    ip: *************
  snowflake:
    worker-id: 1 		#全局唯一guid生成器终端ID,最大值为31，最小值为1
    datacenter-id: 2 	#全局唯一guid生成器数据中心ID,最大值为31，最小值为1
  system-mark: dam
  matchers:
    ignores: /doc.html/**,/swagger/**,/rpc-api/**,/webSocketConvert/**
  debug: false
  datasource:
    druid:
      log-slow-sql: true
      slow-sql-millis: 100
    dynamic:
      druid:
        initial-size: 1
        min-idle: 1 		# 最小连接池数量
        max-active: 20 		# 最大连接池数量
        max-wait: 600000 	# 配置获取连接等待超时的时间，单位：毫秒
      master:
        url: ********************************************************************************************************************************
        driver-class-name: org.postgresql.Driver
        username: postgres
        password: Go@123456
      bsp:
        url: jdbc:mysql://${conf.middleware.ip}:3336/bsp_v1.1?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&nullCatalogMeansCurrent=true&rewriteBatchedStatements=true # MySQL Connector/J 8.X 连接的示例
        username: root
        password: sundun_bsp
  mongodb:
    uri: mongodb://${conf.middleware.ip}:27111/bsp
    hosts:
  redis:
    host: ${conf.middleware.ip}
    port: 6399
    database: 3
    password: redisbsp
    timeout: 6000  # 连接超时时长（毫秒）
    max-redirects: 3
    lettuce:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
  rabbitmq:
    addresses: ${conf.middleware.ip}:5682
    username: root
    password: sundun_bsp
    maxConcurrency: 20
    concurrency: 10
    prefetch: 1

---
conf:
  nacos:
    enabled: true
    ip: ${conf.middleware.ip}
    port: 8848
    username: nacos
    password: nacos@gxx
    namespace: rs
    group: DEFAULT_GROUP
---
conf:
  xxl:
    enabled: false
    admin:
      addresses: http://${conf.middleware.ip}:8080/xxl-job-admin
      username: admin
      password: xxlbsp
    executor:
      ip: ***********
      port: 9502
      logPath: logs/xxl-job/${spring.application.name}
---
conf:
  dromara:
    x-file-storage:
      enable-storage: true
      access-key: admin
      secret-key: admin123456
      end-point: http://${conf.middleware.ip}:9010
      bucket-name: dam
      domain: http://${conf.middleware.ip}:9010/dam/
      base-path:
---
conf:
  convert:
    websocket: ${conf.server.ip}:${conf.server.port.dam-convert}
    #交换机
    exchange:
      # 处理pdf转换交换机
      convert: dam-pdf-convert
      # 重新转化pdf交换机
      reconvert: dam-pdf-reconvert
      # 查询进度的websocket的交换机
      rate: dam-pdf-convert-rate
    #路由
    routing:
      convert: dam-pdf-convert
      reconvert: dam-pdf-reconvert
    #队列
    queue:
      convert: dam-pdf-convert
      reconvert: dam-pdf-reconvert
      rate: dam-pdf-convert-rate
    #pdf转换线程数
    threadCount: 1
    #pdf分割数量
    pdfSplitCount: 1
