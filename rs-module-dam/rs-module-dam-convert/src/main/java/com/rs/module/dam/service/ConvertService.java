package com.rs.module.dam.service;

import java.util.List;

import com.rs.module.dam.entity.material.PdfBatchDO;
import com.rs.module.dam.entity.material.PdfImageDO;
import com.rs.module.dam.vo.material.PdfBatchImage;
import com.rs.module.dam.vo.material.PdfConvertRate;

/**
 * 转换服务接口类
 * <AUTHOR>
 * @date 2025年4月20日
 */
public interface ConvertService {

	/**
	 * 获取pdf转换进度
	 * @param jgrybm String 监管人员编码
	 * @param type String 类型(0:未OCR,1:编目,2:组卷,3:快速组卷)
	 * @return List<PdfConvertRate>
	 */
	public List<PdfConvertRate> getConvertRate(String jgrybm, String type);
	
	/**
	 * 刷新pdf状态标记
	 * @param jgrybm String 监管人员编码
	 */
    public void setRefreshPdfStatusFlag(String jgrybm);
    
	/**
	 * 异步转换
	 * @param jgrybm String 监管人员编码
	 * @param pdfId String pdf主键
	 * @param batchConvert boolean 是否批量转换
	 * @param imageData PdfBatchImage pdf图片批量转换对象
	 * @param materialSource String 材料来源
	 */
    public void asynchronousConvert(String jgrybm, String pdfId, boolean batchConvert,
    		PdfBatchImage imageData, String materialSource);
    
    /**
     * 卷宗重新转换和编目
     * @param pdfBatch PdfBatchDO pdf上传的批次
     * @param pdfId String pdf上传主键
     * @param imageIdList List<String> 已转换成功的图片Id
     * @param imageList List<PdfImageDO> 当前图片集合
     */
    public void convertedAndToCataloging(PdfBatchDO pdfBatch, String pdfId,
    		List<PdfImageDO> imageList, List<String> imageIdList);
}
