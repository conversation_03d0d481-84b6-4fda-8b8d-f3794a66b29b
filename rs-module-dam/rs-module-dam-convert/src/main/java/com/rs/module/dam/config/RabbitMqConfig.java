package com.rs.module.dam.config;

import java.util.Date;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.alibaba.fastjson.JSON;
import com.bsp.common.util.StringUtil;
import com.rs.module.dam.constant.RedisConstants;
import com.rs.module.dam.controller.vo.PushPdfData;
import com.rs.module.dam.entity.material.PdfConvertLogDO;
import com.rs.module.dam.rabbitmq.vo.PdfCorrelationData;
import com.rs.module.dam.service.material.PdfConvertLogService;
import com.rs.module.dam.util.RedisTemplateUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * rabbitmq配置类
 * <AUTHOR>
 * @date 2025年4月20日
 */
@Configuration
@Slf4j
public class RabbitMqConfig {

	@Autowired
	RabbitAdmin rabbitAdmin;
	
	@Autowired
	RedisTemplateUtil redisUtil;
	
	@Autowired
	private PdfConvertLogService convertLogService;
	
    /** pdf转换交换机 */
    public static String CONVERT_PDF_EXCHANGE;
    
    /** 重新转换pdf交换机 */
    public static String RECONVERT_PDF_EXCHANGE;
    
    /** 查询进度的websocket的交换机 */
    public static String CONVERT_RATE_EXCHANGE;
    
    /** pdf转换队列路由键 */
    public static String CONVERT_ROUTING_KEY;
    
    /** pdf重新转换队列路由键 */
    public static String RECONVERT_ROUTING_KEY;
	
	/** pdf转换队列 */
	public static String CONVERT_PDF_QUEUE;
	
	/** pdf重新转换队列 */
	public static String RECONVERT_PDF_QUEUE;
	
	/** 查询进度的websocket的队列 */
	public static String CONVERT_RATE_QUEUE;
	

	/************************************* 属性绑定 ************************************/
	
	public static String getConvertPdfExchange() {
		return CONVERT_PDF_EXCHANGE;
	}

	@Value("${conf.convert.exchange.convert}")
	public void setConvertPdfExchange(String convertPdfQueue) {
		CONVERT_PDF_EXCHANGE = convertPdfQueue;
	}

	public static String getReconvertPdfExchange() {
		return RECONVERT_PDF_EXCHANGE;
	}

	@Value("${conf.convert.exchange.reconvert}")
	public void setReconvertPdfExchange(String reconvertPdfExchange) {
		RECONVERT_PDF_EXCHANGE = reconvertPdfExchange;
	}

	public static String getConvertRateExchange() {
		return CONVERT_RATE_EXCHANGE;
	}

	@Value("${conf.convert.exchange.rate}")
	public void setConvertRateExchange(String convertRateExchange) {
		CONVERT_RATE_EXCHANGE = convertRateExchange;
	}

	public static String getConvertRoutingKey() {
		return CONVERT_ROUTING_KEY;
	}

	@Value("${conf.convert.routing.convert}")
	public void setConvertRoutingKey(String convertRoutingKey) {
		CONVERT_ROUTING_KEY = convertRoutingKey;
	}

	public static String getReconvertRoutingKey() {
		return RECONVERT_ROUTING_KEY;
	}

	@Value("${conf.convert.routing.reconvert}")
	public void setReconvertRoutingKey(String reconvertRoutingKey) {
		RECONVERT_ROUTING_KEY = reconvertRoutingKey;
	}

	public static String getConvertPdfQueue() {
		return CONVERT_PDF_QUEUE;
	}

	@Value("${conf.convert.queue.convert}")
	public void setConvertPdfQueue(String convertPdfQueue) {
		CONVERT_PDF_QUEUE = convertPdfQueue;
	}

	public static String getReonvertPdfQueue() {
		return RECONVERT_PDF_QUEUE;
	}

	@Value("${conf.convert.queue.reconvert}")
	public void setReonvertPdfQueue(String reonvertPdfQueue) {
		RECONVERT_PDF_QUEUE = reonvertPdfQueue;
	}

	public static String getConvertRateQueue() {
		return CONVERT_RATE_QUEUE;
	}

	@Value("${conf.convert.queue.rate}")
	public void setConvertRateQueue(String convertRateQueue) {
		CONVERT_RATE_QUEUE = convertRateQueue;
	}
	
	/************************************* Bean定义 ************************************/
	
	/**
	 * pdf转换队列
	 * @return Queue
	 */
	@Bean
	public Queue convertPdfQueue() {
		return new Queue(CONVERT_PDF_QUEUE, true);
	}
	
	/**
	 * pdf转换交换机
	 * @return DirectExchange
	 */
	@Bean
	public DirectExchange convertPdfExchange() {
		return new DirectExchange(CONVERT_PDF_EXCHANGE, true, false);
	}
	
	/**
	 * pdf转换队列与交换机绑定
	 * @return Binding
	 */
	@Bean
	public Binding convertPdfBinding() {
		return BindingBuilder.bind(convertPdfQueue()).to(convertPdfExchange()).with(CONVERT_ROUTING_KEY);
	}
	
	/**
	 * pdf重新转换队列
	 * @return Queue
	 */
	@Bean
	public Queue reconvertPdfQueue() {
		return new Queue(RECONVERT_PDF_QUEUE, true);
	}
	
	/**
	 * pdf重新转换交换机
	 * @return DirectExchange
	 */
	@Bean
	public DirectExchange reconvertPdfExchange() {
		return new DirectExchange(RECONVERT_PDF_EXCHANGE, true, false);
	}
	
	/**
	 * pdf重新转换队列与交换机绑定
	 * @return Binding
	 */
	@Bean
	public Binding reconvertPdfBinding() {
		return BindingBuilder.bind(convertPdfQueue()).to(convertPdfExchange()).with(RECONVERT_ROUTING_KEY);
	}
	
	/************************************* 初始化 ************************************/
	
	/**
	 * 初始化 RabbitAdmin
	 * @param connectionFactory ConnectionFactory 连接工厂
	 * @return RabbitAdmin
	 */
	@Bean
	public RabbitAdmin rabbitAdmin(ConnectionFactory connectionFactory) {
		RabbitAdmin rabbitAdmin = new RabbitAdmin(connectionFactory);
		rabbitAdmin.setAutoStartup(true);	//只有设置为 true，spring 才会加载 RabbitAdmin 这个类
		return rabbitAdmin;
	}
	
	/**
	 * 创建交换机和队列
	 */
	@Bean
	public void createExchangeQueue() {
		rabbitAdmin.declareExchange(convertPdfExchange());
		rabbitAdmin.declareQueue(convertPdfQueue());
		rabbitAdmin.declareExchange(reconvertPdfExchange());
		rabbitAdmin.declareQueue(reconvertPdfQueue());
	}
	
	/**
	 * 初始化 RabbitTemplate
	 * @param connectionFactory ConnectionFactory 连接工厂
	 * @return RabbitTemplate
	 */
	@Bean
	public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
		RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
		
		//设置开启Mandatory,才能触发回调函数,无论消息推送结果怎么样都强制调用回调函数
        // 需要配置 spring.rabbitmq.publisher-confirms: true
        rabbitTemplate.setMandatory(true);
        
        // 发送端消息回调
        rabbitTemplate.setConfirmCallback((correlationData, ack, s) -> {
        	if (!ack && correlationData instanceof PdfCorrelationData) {
                log.error("发送消息失败: {}", s);
                
                PdfCorrelationData pdfCorrelationData = (PdfCorrelationData)correlationData;
                PushPdfData pushPdfData = pdfCorrelationData.getData();
                
                //清除缓存数据
                redisUtil.rmZSet(RedisConstants.PDF_CONVERT_KEY, pushPdfData.getUuid());
                
                //保存转换异常日志
                PdfConvertLogDO convertLog = new PdfConvertLogDO();
                convertLog.setId(StringUtil.getGuid32());
                convertLog.setPushData(JSON.toJSONString(pushPdfData));
                convertLog.setErrorLog("mq推送失败：" + s);
                convertLog.setAddTime(new Date());
                convertLogService.save(convertLog);
        	}
        });
        
		return rabbitTemplate;
	}
}
