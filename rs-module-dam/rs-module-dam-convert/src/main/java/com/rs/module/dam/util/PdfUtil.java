package com.rs.module.dam.util;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.imageio.ImageIO;

import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.ofdrw.converter.ImageMaker;
import org.ofdrw.reader.OFDReader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.ServiceLocator;
import com.bsp.common.util.StringUtil;
import com.google.common.collect.Lists;
import com.rs.module.dam.constant.DamConstants;
import com.rs.module.dam.constant.PdfConvertStatus;
import com.rs.module.dam.constant.PdfDealCode;
import com.rs.module.dam.constant.RedisConstants;
import com.rs.module.dam.entity.material.PdfImageDO;
import com.rs.module.dam.entity.material.PdfUploadDataDO;
import com.rs.module.dam.exception.CommonException;
import com.rs.module.dam.service.ConvertService;
import com.rs.module.dam.service.material.PdfImageService;
import com.rs.module.dam.service.material.PdfUploadDataService;
import com.rs.module.dam.vo.material.PdfConvertRate;

import lombok.extern.slf4j.Slf4j;

/**
 * pdf处理工具类
 * <AUTHOR>
 * @date 2025年4月21日
 */
@Component
@Slf4j
public class PdfUtil {
	
	@Resource
	private PdfUploadDataService uploadDataService;
	
	@Resource
	private PdfImageService pdfImageService;
	
	@Resource
	private ConvertService convertService;
	
	@Resource
    private RedisTemplateUtil redisUtil;
	
	@Value("${conf.convert.threadCount:1}")
    private int threadCount;

	/**
	 * 加载pdf文档
	 * @param pdfUrl String pdf文档地址
	 * @return PDDocument
	 */
	public PDDocument loadPdfDocument(String pdfUrl) {
		InputStream is = null;
		
		try {
			pdfUrl = pdfUrl.replace(" ", "%20");
			URL url = new URL(pdfUrl);
			is = url.openStream();
			
			//临时存放磁盘，减少内存使用
			PDDocument pdDocument = PDDocument.load(is, MemoryUsageSetting.setupTempFileOnly());
			
			//软引用是在内存即将溢出才会回收，所以也会生命周期会一直占用内存
			pdDocument.setResourceCache(new PdfCustomDefaultResourceCache());
			
			return pdDocument;
		}
		catch(Exception e) {
			log.error("加载pdf文档失败，pdf地址：{}，失败原因：{}", pdfUrl, e);
			throw new CommonException(PdfDealCode.PDF_DOWNLOAD_FAIL.getCode(), e.getMessage());
		}
		finally {
			if(null != is) {
				try {
					is.close();
				}
				catch(Exception e) {
					log.error("加载pdf文档时关闭输入流失败:{}", e.getMessage());
				}
			}
		}
	}
	
	/**
	 * 构建ofd文档读取器
	 * @param pdfUrl String ofd文档地址
	 * @return OFDReader
	 */
	public OFDReader buildOfdReader(String ofdUrl) {
		InputStream is = null;
		
		try {
			ofdUrl = ofdUrl.replace(" ", "%20");
			URL url = new URL(ofdUrl);
			is = url.openStream();
			
			return new OFDReader(is);
		}
		catch(Exception e) {
			log.error("加载ofd文档失败，ofd地址：{}，失败原因：{}", ofdUrl, e);
			throw new CommonException(PdfDealCode.PDF_DOWNLOAD_FAIL.getCode(), e.getMessage());
		}
		finally {
			if(null != is) {
				try {
					is.close();
				}
				catch(Exception e) {
					log.error("加载ofd文档时关闭输入流失败:{}", e.getMessage());
				}
			}
		}
	}
	
	/**
	 * pdf转换为图片
	 * @param pdDocument PDDocument pdf文档
	 * @param ofdReader OFDReader ofd读取器
	 * @param uploadPdfData PdfUploadDataDO pdf上传对象
	 * @param convertRateKey String 转换进度key
	 * @return List<PdfImageDO>
	 */
	public List<PdfImageDO> pdfConvertImage(PDDocument pdDocument, OFDReader ofdReader,
			PdfUploadDataDO uploadPdfData, String convertRateKey){
		log.info("【PdfUtil-pdfConvertImage-pdf-转换图片】，【pdfId-{}】，转换开始", uploadPdfData.getId());
		
		ImageMaker imageMarker = null;
		int pdfPages = 0;
		boolean isPdf = true;
		if(null != pdDocument) {
			pdfPages = pdDocument.getNumberOfPages();
		}
		else {
			imageMarker = new ImageMaker(ofdReader, 16);
			pdfPages = imageMarker.pageSize();
			isPdf = false;
		}
		
		//更新pdf状态为正在转换中
		PdfUploadDataDO updateUploadPdfData = new PdfUploadDataDO();
		updateUploadPdfData.setId(uploadPdfData.getId());
		updateUploadPdfData.setPageCount(pdfPages);
		updateUploadPdfData.setStatus(PdfConvertStatus.CONVERTING.getCode());
		updateUploadPdfData.setUpdateTime(new Date());
		uploadDataService.updateById(updateUploadPdfData);
		
		//pdf状态发生变化,前端页面刷新转换进度pdf状态
        convertService.setRefreshPdfStatusFlag(uploadPdfData.getJgrybm());
        
        List<PdfImageDO> imageList = new ArrayList<>();
        uploadPdfData.setPageCount(pdfPages);
        String pdfId = uploadPdfData.getId();
        List<PdfImageDO> cancelImageList = new ArrayList<>();
        List<Integer> cancelXhList = new ArrayList<>();
        
        //pdf上次转换时被中途取消,暂停, 转换接着上次后面继续
        if(PdfConvertStatus.CONVERT_CANCEL.getCode() == uploadPdfData.getStatus()) {
        	cancelImageList = pdfImageService.list(new QueryWrapper<PdfImageDO>()
        			.eq("pdf_id", pdfId)
        			.eq("status", DamConstants.IMAGE_CONVERT_CANCEL)
        			.orderByAsc("xh"));
        	if(CollectionUtil.isNotNull(cancelImageList)) {
        		cancelXhList = cancelImageList.stream().map(PdfImageDO::getXh).collect(Collectors.toList());
        	}
        }
        
        try {
        	List<Integer> allPageList = new ArrayList<>();
            for (int i = 0; i < pdfPages; i++) {
            	allPageList.add(i);
            }
            
            //构建线程池
            ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
            
            //为使用多线程根据可用线程数分割页号
            int groupNum = ((allPageList.size() + threadCount - 1) / threadCount);
            List<List<Integer>> pageList = Lists.partition(allPageList, groupNum);
            
            //使用多线程循环转换
            for(List<Integer> pages : pageList) {
            	boolean finalIsPdf = isPdf;
            	ImageMaker finalImageMaker = imageMarker;
                List<Integer> finalCancelXhList = cancelXhList;
                
                //执行转换任务
            	executorService.submit(() -> {
            		pages.stream().filter(i -> !finalCancelXhList.contains(i)).forEach(i -> {
            			long start = System.currentTimeMillis();
            			
            			//读取redis取消转换值
            			boolean hasMember = redisUtil.sIsMember(RedisConstants.CANCEL_CONVERT_PDF_KEY, pdfId);
            			
            			//转换已取消
            			if(hasMember) {
            				printConvertLog(pdfId, i, "中途取消");
            				
            				//点击重新转换后的取消
            				String reConvertKey = String.format(RedisConstants.RE_CONVERT_LOCK, pdfId);
            				redisUtil.deleteKey(reConvertKey);
            				
            				//更新pdf状态为取消
            				updateUploadPdfData.setStatus(PdfConvertStatus.PDF_CONVERTING_CANCEL.getCode());
            				updateUploadPdfData.setUpdateTime(new Date());
            				uploadDataService.updateById(updateUploadPdfData);
            				
            				//pdf状态发生变化,前端页面刷新转换进度pdf状态
                            convertService.setRefreshPdfStatusFlag(uploadPdfData.getJgrybm());
                            
                            //更新pdf转换进度为暂停
                            String rate = redisUtil.hGet(convertRateKey, pdfId);
                            if(StringUtil.isNotEmpty(rate)) {
                            	PdfConvertRate pdfConvertRate = JSONObject.parseObject(rate, PdfConvertRate.class);
                            	pdfConvertRate.setPause("1");
                            	redisUtil.hSet(reConvertKey, pdfId, JSONObject.toJSONString(pdfConvertRate));
                            }
                            
                            printConvertLog(pdfId, i, "暂停转换");
                            throw new CommonException(PdfDealCode.PDF_CONVERTING_CANCEL);
            			}
            			
            			//正常转换
            			else {
            				BufferedImage image = null;
            				String name = pdfId + "_" + StringUtil.getGuid32() + "_" + i + ".jpg";
            				
            				try {
								if(finalIsPdf) {
									PDFRenderer pdfRender = new PDFRenderer(pdDocument);
									image = pdfRender.renderImageWithDPI(i, uploadPdfData.getDpi());
								}
								else {
									image = finalImageMaker.makePage(i);
								}
								
								//写入字节输出流
								ByteArrayOutputStream baos = new ByteArrayOutputStream();
								ImageIO.write(image, "JPG", baos);
								printConvertDurationLog(pdfId, i, "结束", start);
								
								//上传图片
								FileInfo uploadImage = upload(baos.toByteArray(), name, pdfId);
								image = null;
								printConvertLog(pdfId, i, "转换成功");
								
								//添加到pdf图片集合中
								if(null != uploadImage) {
									
									//添加到pdf图片集合中
									PdfImageDO pdfImage = new PdfImageDO();
	            					pdfImage.setId(StringUtil.getGuid32());
	            					pdfImage.setJgrybm(uploadPdfData.getJgrybm());
	            					pdfImage.setPdfId(pdfId);
	            					pdfImage.setImageUrl(uploadImage.getUrl());
	            					pdfImage.setImageName(uploadImage.getFilename());
	            					pdfImage.setImageData(JSONObject.toJSONString(uploadImage));
	            					pdfImage.setAddTime(new Date());
	            					pdfImage.setXh(i + 1);
	            					pdfImage.setStatus(DamConstants.IMAGE_CONVERT_SUCCESS);
	            					imageList.add(pdfImage);
								}
							}
            				catch (Exception e) {
            					printConvertErrorLog(pdfId, i, e);
            					
            					//添加到pdf图片集合中
            					PdfImageDO pdfImage = new PdfImageDO();
            					pdfImage.setId(StringUtil.getGuid32());
            					pdfImage.setJgrybm(uploadPdfData.getJgrybm());
            					pdfImage.setPdfId(pdfId);
            					pdfImage.setAddTime(new Date());
            					pdfImage.setXh(i + 1);
            					pdfImage.setStatus(DamConstants.IMAGE_CONVERT_FAIL);
            					pdfImage.setErrorMsg(e.getMessage());
            					imageList.add(pdfImage);
							}
            				
            				String rate = redisUtil.hGet(convertRateKey, pdfId);
            				if(StringUtil.isNotEmpty(rate)) {
            					PdfConvertRate pdfConvertRate = JSONObject.parseObject(rate, PdfConvertRate.class);
            					
            					//转换任务完成删除进度条
            					if(uploadPdfData.getPageCount() - imageList.size() == 0) {
            						redisUtil.hDel(convertRateKey, pdfId);
            					}
            					else {
            						pdfConvertRate.setPageCount(uploadPdfData.getPageCount());
            						pdfConvertRate.setConvertCount(imageList.size());
            						redisUtil.hSet(convertRateKey, pdfId, JSONObject.toJSONString(pdfConvertRate));
            					}
            				}
            				else {
            					PdfConvertRate pdfConvertRate = new PdfConvertRate();
            					pdfConvertRate.setPdfId(pdfId);
                                pdfConvertRate.setPageCount(uploadPdfData.getPageCount());
                                pdfConvertRate.setPdfName(uploadPdfData.getPdfName());
                                pdfConvertRate.setConvertCount(imageList.size());
                                pdfConvertRate.setPause("0");
                                redisUtil.hSet(convertRateKey, pdfId, JSONObject.toJSONString(pdfConvertRate));
            				}
            			}
            		});
            	});
            }
            
            //等待所有线程执行完毕
        	executorService.shutdown();
        	while(!executorService.isTerminated()) {}
        }
        catch(Exception e) {
        	log.error("pdf转换错误，异常信息：{}", e.getMessage(), e);
        }
        
        //转换任务完成, 删除进度条
        String rate = redisUtil.hGet(convertRateKey, pdfId);
        if(StringUtil.isNotEmpty(rate)) {
        	if(uploadPdfData.getPageCount() - imageList.size() == 0) {
        		redisUtil.hDel(convertRateKey, pdfId);
        	}
        }
        
        //返回转换后的图片
        if(CollectionUtil.isNotNull(imageList)) {
    		boolean hasMember = redisUtil.sIsMember(RedisConstants.CANCEL_CONVERT_PDF_KEY, pdfId);
    		
    		//暂停或取消
    		if(hasMember){
    			
    			//修改图片状态
    			imageList.forEach(image -> image.setStatus(DamConstants.IMAGE_CONVERT_CANCEL));
    			
    			//重新排序
    			List<PdfImageDO> sortedImageList = imageList.stream().sorted(Comparator.comparing(PdfImageDO::getXh)).collect(Collectors.toList());
    			
    			//插入图片
    			pdfImageService.saveBatch(sortedImageList);
    			
    			//清除缓存
    			redisUtil.sRem(RedisConstants.CANCEL_CONVERT_PDF_KEY, pdfId);
    			
    			throw new CommonException(PdfDealCode.PDF_CONVERTING_CANCEL);
    		}
    		else {
    			
    			//重新排序
    			List<PdfImageDO> sortedImageList = imageList.stream().sorted(Comparator.comparing(PdfImageDO::getXh)).collect(Collectors.toList());
    			
    			//插入图片
    			pdfImageService.saveBatch(sortedImageList);
    			
    			//放弃转换的照片集合
    			if(CollectionUtil.isNotNull(cancelImageList)) {
    				
    				//修改图片状态
    				cancelImageList.forEach(cancelImage -> cancelImage.setStatus(DamConstants.IMAGE_CONVERT_SUCCESS));
    				
    				//批量更新图片
    				pdfImageService.updateBatchById(cancelImageList);
    				
    				//返回全部排好序的图片
                    cancelImageList.addAll(sortedImageList);
                    sortedImageList = cancelImageList;
    			}
    			
    			return sortedImageList;
    		}
        }
        else {
        	return imageList;
        }
	}
	
	/**
	 * 上传文件
	 * @param uploadData Object 上传的数据
	 * @param fileName String 文件名称
	 * @param objId String 对象Id
	 * @return FileInfo
	 */
	private static FileInfo upload(Object uploadData, String fileName, String objId) {
		FileStorageService fileStorageService = ServiceLocator.getBean(FileStorageService.class);
		FileInfo fileInfo = fileStorageService.of(uploadData)
                .setSaveFilename(fileName) 	//设置保存的文件名，不需要可以不写，会随机生成
                .setObjectId(objId)   		//关联对象id，为了方便管理，不需要可以不写
//                .setObjectType(objType) 	//关联对象类型，为了方便管理，不需要可以不写
                .upload();  				//将文件上传到对应地方
		
		return fileInfo;
	}
	
	/**
	 * 打印消息监听器日志
	 * @param pdfId String pdf主键
	 * @param msg String 日志消息
	 */
	public static void printListenerLog(String pdfId, String msg) {
		log.info("【MessageListener】，【pdfId-{}】，{}", pdfId, msg);
	}
	
	/**
	 * 打印转换耗时日志
	 * @param pdfId String pdf主键
	 * @param msg String 日志消息
	 * @param start long 开始时间(毫秒)
	 */
	public static void printListenerDurationLog(String pdfId, String msg, long start) {
		log.info("【MessageListener】，【pdfId-{}】，{}，耗时【{}毫秒】",
				pdfId, msg, System.currentTimeMillis() - start);
	}
	
	/**
	 * 打印转换异常日志
	 * @param pdfId String pdf主键
	 * @param e Exception 异常
	 */
	public static void printListenerErrorLog(String pdfId, Exception e) {
		log.info("【MessageListener】，【pdfId-{}】，{}，失败原因【{}】",
				pdfId, e.getMessage(), e);
	}
	
	/**
	 * 打印转换日志
	 * @param pdfId String pdf主键
	 * @param i int 图片索引
	 * @param msg String 日志消息
	 */
	public static void printConvertLog(String pdfId, int i, String msg) {
		log.info("【PdfUtil-pdfConvertImage-pdf-转换图片】，【pdfId-{}】，转换第【{}】张图片-{}", pdfId, i, msg);
	}
	
	/**
	 * 打印转换耗时日志
	 * @param pdfId String pdf主键
	 * @param i int 图片索引
	 * @param msg String 日志消息
	 * @param start long 开始时间(毫秒)
	 */
	public static void printConvertDurationLog(String pdfId, int i, String msg, long start) {
		log.info("【PdfUtil-pdfConvertImage-pdf-转换图片】，【pdfId-{}】，转换第【{}】张图片-{}，耗时【{}毫秒】",
				pdfId, i, msg, System.currentTimeMillis() - start);
	}
	
	/**
	 * 打印转换异常日志
	 * @param pdfId String pdf主键
	 * @param i int 图片索引
	 * @param e Exception 异常
	 */
	public static void printConvertErrorLog(String pdfId, int i, Exception e) {
		log.info("【PdfUtil-pdfConvertImage-pdf-转换图片】，【pdfId-{}】，转换第【{}】张图片失败，失败原因【{}】",
				pdfId, i + 1, e.getMessage(), e);
	}
	
	/**
	 * 生成临时文件目录
	 * @param source String 源
	 * @return String
	 */
	@SuppressWarnings("unused")
	private String generateTempPath(String source) {
		String userDir = System.getProperty("user.dir") + File.separator;
		String path = userDir + "temp" + File.separator + source + File.separator;
		File dir = new File(path);
		if(dir.exists()) {}
		else {
			if(dir.mkdirs()) {
				log.info("创建目录【" + path + "】成功！");
			}
			else {
				log.info("创建目录【" + path + "】失败！");
				return userDir;
			}
		}
		return path;
	}
}
