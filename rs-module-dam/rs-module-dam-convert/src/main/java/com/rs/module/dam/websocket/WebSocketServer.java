package com.rs.module.dam.websocket;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.dam.constant.BmTypeEnum;
import com.rs.module.dam.constant.RedisConstants;
import com.rs.module.dam.service.ConvertService;
import com.rs.module.dam.util.RedisTemplateUtil;
import com.rs.module.dam.vo.material.PdfConvertRate;

import lombok.extern.slf4j.Slf4j;

/**
 * websocket服务端
 * <AUTHOR>
 * @date 2025年4月22日
 */
@ServerEndpoint("/webSocketConvert/{sid}")
@Component
@Slf4j
public class WebSocketServer {
	
    /** 静态变量，用来记录当前在线连接数。应该把它设计成线程安全的。 */
    private static AtomicInteger onlineNum = new AtomicInteger();

    /** concurrent包的线程安全Set，用来存放每个客户端对应的WebSocketServer对象。*/
    private static ConcurrentHashMap<String, Session> sessionPools = new ConcurrentHashMap<>();

    /** pdf转换服务类 */
    private static ConvertService convertService;

    @Autowired
    public void setConvertService(ConvertService convertService){
        WebSocketServer.convertService = convertService;
    }

    /** redis工具类 */
    private static RedisTemplateUtil redisTemplateUtil;
    
    @Autowired
    public void setRedisTemplateUtil(RedisTemplateUtil redisTemplateUtil){
        WebSocketServer.redisTemplateUtil = redisTemplateUtil;
    }

    /**
     * 发送消息
     * @param session Session 会话
     * @param message String 消息内容
     * @throws IOException
     */
    private static void sendMessage(Session session, String message) throws IOException {
        if(session != null && session.isOpen()){
            synchronized (session) {
                session.getBasicRemote().sendText(message);
            }
        }
    }
    
    /**
     * 发送消息
     * @param userName String 用户名
     * @param message String 消息内容
     * @throws IOException
     */
    public static void sendInfo(String userName, String message){
    	
    	//不传username 发给所有人
    	if(StringUtils.isEmpty(userName)) {
    		for(String key : sessionPools.keySet()) {
    			Session session = sessionPools.get(key);
    			try {
    	            sendMessage(session, message);
    	        }
    			catch (Exception e){
    	        	log.error("【websocket-convert】推送消息异常：{}", e);
    	        }
        	}
    	}
    	
    	//发给指定用户
    	else {
    		if(sessionPools.containsKey(userName)) {
    			try {
        			Session session = sessionPools.get(userName);
                    sendMessage(session, message);
                }
        		catch (Exception e){
                    log.error("【websocket-convert】推送消息异常：{}", e);
                }
    		}
    	}
    }

    /**
     * 连接建立时
     * @param session Session 会话
     * @param userName String 用户名
     */
    @OnOpen
    public void onOpen(Session session, @PathParam(value = "sid") String userName){
        sessionPools.put(userName, session);
        addOnlineCount();
        log.info("【websocket-convert】" + userName + "加入webSocket！当前人数为" + onlineNum);
    }

    /**
     * 连接关闭时
     * @param userName String 用户名
     */
    @OnClose
    public void onClose(@PathParam(value = "sid") String userName){
        sessionPools.remove(userName);
        subOnlineCount();
        log.info("【websocket-convert】" + userName + "断开webSocket连接！当前人数为" + onlineNum);
    }

    /**
     * 收到客户端信息
     * @param message String 消息
     * @throws IOException
     */
    @OnMessage
    public void onMessage(String message) throws IOException{
        log.info("【websocket-convert】" + "接收客户端消息:{}", message);
        JSONObject socketJson = JSONObject.parseObject(message);
        String source = socketJson.getString("source");
        
        //转换进度
        if (("convertRate").equals(source)){
            String jgrybm = socketJson.getString("jgrybm");
            String type = socketJson.getString("type");

            List<PdfConvertRate> convertRate = new ArrayList<>();
            
            //已编目和未编目
            if (BmTypeEnum.BM.getType().equals(type) || BmTypeEnum.UN_BM.getType().equals(type)){
            	
                //已编目进度
                List<PdfConvertRate> bmConvertRate = convertService.getConvertRate(jgrybm, "1");
                convertRate.addAll(bmConvertRate);
                
                //未编目进度
                List<PdfConvertRate> unBmConvertRate = convertService.getConvertRate(jgrybm, "0");
                convertRate.addAll(unBmConvertRate);
            }
            
            //已组卷和快速组卷
            else {
                convertRate = convertService.getConvertRate(jgrybm, type);
            }

            //非暂停的转换进度
            convertRate = convertRate.stream().filter(convert -> !("1").equals(convert.getPause())).collect(Collectors.toList());

            //构建推送的数据
            Map<String, Object> sendData = new HashMap<>();
            sendData.put("code", WebSocketCode.PDF_CONVERTED.getCode());
            sendData.put("msg", WebSocketCode.PDF_CONVERTED.getMsg());
            sendData.put("jgrybm", jgrybm);
            sendData.put("type", type);
            sendData.put("data", convertRate);

            //刷新材料
            String refreshKey = String.format(RedisConstants.MATERIAL_REFRESH_FLAG, jgrybm);
            String isRefresh = redisTemplateUtil.get(refreshKey);
            if (StringUtils.isNotBlank(isRefresh) && CollectionUtils.isEmpty(convertRate)){
                sendData.put("refresh", "1");
                redisTemplateUtil.deleteKey(refreshKey);
            }
            else {
                sendData.put("refresh", "0");
            }
            
            //刷新pdf转换状态
            String refreshStatusKey = String.format(RedisConstants.REFRESH_CONVERT_PDF_STATUS, jgrybm);
            String refreshStatus = redisTemplateUtil.get(refreshStatusKey);
            if (StringUtils.isNotBlank(refreshStatus) && CollectionUtils.isEmpty(convertRate)){
                sendData.put("refreshStatus", "1");
                redisTemplateUtil.deleteKey(refreshStatusKey);
            }
            else {
                sendData.put("refreshStatus", "0");
            }

            log.info("【websocket-convert】发送pdf转换进度webSocket消息:{}", JSONObject.toJSONString(sendData));
            sendInfo(jgrybm + "-" + type, JSONObject.toJSONString(sendData));
        }
    }

    /**
     * 异常时调用
     * @param session Session 会话
     * @param throwable Throwable 异常
     */
    @OnError
    public void onError(Session session, Throwable throwable){
    	log.error("【websocket-convert】发生异常：{}", (Exception) throwable);
    }
    
    /**
     * 增加在线人数
     */
    public static void addOnlineCount(){
        onlineNum.incrementAndGet();
    }

    /**
     * 减少在线人数
     */
    public static void subOnlineCount() {
        onlineNum.decrementAndGet();
    }
}