package com.rs.module.dam.service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.pdfbox.cos.COSDocument;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.ofdrw.reader.OFDReader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.google.common.collect.Lists;
import com.rs.module.dam.constant.BmTypeEnum;
import com.rs.module.dam.constant.DamConstants;
import com.rs.module.dam.constant.PdfConvertStatus;
import com.rs.module.dam.constant.PdfDealCode;
import com.rs.module.dam.constant.RedisConstants;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.entity.material.PdfBatchDO;
import com.rs.module.dam.entity.material.PdfImageDO;
import com.rs.module.dam.entity.material.PdfUploadDataDO;
import com.rs.module.dam.exception.CommonException;
import com.rs.module.dam.service.material.MaterialInfoService;
import com.rs.module.dam.service.material.PdfBatchService;
import com.rs.module.dam.service.material.PdfImageService;
import com.rs.module.dam.service.material.PdfUploadDataService;
import com.rs.module.dam.util.PdfConvertUtil;
import com.rs.module.dam.util.PdfUtil;
import com.rs.module.dam.util.RedisTemplateUtil;
import com.rs.module.dam.vo.material.PdfBatchImage;
import com.rs.module.dam.vo.material.PdfConvertRate;

import lombok.extern.slf4j.Slf4j;

/**
 * 转换服务实现类
 * <AUTHOR>
 * @date 2025年4月20日
 */
@Service
@Slf4j
public class ConvertServiceImpl implements ConvertService{
	
	@Resource
	private RedisTemplateUtil redisUtil;
	
	@Resource
	private PdfConvertUtil pdfConvertUtil;
	
	@Resource
	private PdfUtil pdfUtil;
	
	@Resource
	private PdfUploadDataService uploadDataService;
	
	@Resource
	private PdfBatchService pdfBatchService;
	
	@Resource
	private PdfImageService pdfImageService;
	
	@Resource
	private MaterialInfoService materialInfoService;
	
	@Value("${conf.convert.pdfSplitCount:1}")
    private int pdfSplit;

	/**
	 * 获取pdf转换进度
	 * @param jgrybm String 监管人员编码
	 * @param type String 类型(0:未OCR,1:编目,2:组卷,3:快速组卷)
	 * @return List<PdfConvertRate>
	 */
	@Override
	public List<PdfConvertRate> getConvertRate(String jgrybm, String type){
		String convertRateKey = String.format(RedisConstants.PDF_CONVERT_RATE_KEY, jgrybm, type);
        Map<Object, Object> rateMap = redisUtil.hGetAll(convertRateKey);
        List<PdfConvertRate> convertRateData = new ArrayList<>();
        Collection<Object> values = rateMap.values();
        for (Object value : values) {
            convertRateData.add(JSONObject.parseObject(value.toString(), PdfConvertRate.class) );
        }
        
        return convertRateData;
	}
	
	/**
	 * 刷新pdf状态标记
	 * @param jgrybm String 监管人员编码
	 */
	@Override
    public void setRefreshPdfStatusFlag(String jgrybm){
        String refreshStatusKey = String.format(RedisConstants.REFRESH_CONVERT_PDF_STATUS, jgrybm);
        redisUtil.set(refreshStatusKey, "1", 120);
    }
	
	/**
	 * 异步转换
	 * @param jgrybm String 监管人员编码
	 * @param pdfId String pdf主键
	 * @param batchConvert boolean 是否批量转换
	 * @param imageData PdfBatchImage pdf图片批量转换对象
	 * @param materialSource String 材料来源
	 */
	@Override
    public void asynchronousConvert(String jgrybm, String pdfId, boolean batchConvert,
    		PdfBatchImage imageData, String materialSource) {
		String reConvertKey = String.format(RedisConstants.RE_CONVERT_LOCK, pdfId);
		boolean lock = redisUtil.lock(reConvertKey, "1", 1800);
		if(!lock) {
			log.info("【pdf重新转换】重新转换pdf失败，pdf正在转换中...");
		}
		
		//获取pdf上传的数据
		PdfUploadDataDO uploadPdfData = uploadDataService.getById(pdfId);
		if(uploadPdfData == null) {
			log.error("【pdf重新转换】重新转换pdf失败，pdfId: {}，没有找到上传的数据", pdfId);
			return;
		}
		
		//获取pdf上传的批次
		QueryWrapper<PdfBatchDO> batchWrapper = new QueryWrapper<>();
		batchWrapper.eq("id", uploadPdfData.getBatchId());
		PdfBatchDO pdfBatch = pdfBatchService.getOne(batchWrapper);
		
		//pdf文档和ofd读写器
		PDDocument pdDocument = null;
		OFDReader ofdReader = null;
		
		try {
			String bmType = pdfBatch.getType();
			
			//pdf转换进度条Key
			String convertRateKey = String.format(RedisConstants.PDF_CONVERT_RATE_KEY,
					uploadPdfData.getJgrybm(), bmType);
			
			//初始化转换进度条,避免pdf下载时间过长,一时进度条不能显示
			pdfConvertUtil.initConvertRate(uploadPdfData.getJgrybm(), pdfBatch.getType(),
					uploadPdfData.getId(), uploadPdfData.getPdfName());
			
			try {
				//pdf文件地址
        		String pdfUrl = uploadPdfData.getPdfUrl();
        		
        		//pdf数据加载
				if(pdfUrl.toLowerCase().endsWith(".pdf")) {
					pdDocument = pdfUtil.loadPdfDocument(pdfUrl);
				}
				else if(pdfUrl.toLowerCase().endsWith(".ofd")) {
					ofdReader = pdfUtil.buildOfdReader(pdfUrl);
				}
			}
			catch(CommonException e) {
				if(PdfDealCode.PDF_DOWNLOAD_FAIL.getCode().equals(e.getCode())) {
					PdfUploadDataDO pdfUploadData = new PdfUploadDataDO();
					pdfUploadData.setId(uploadPdfData.getId());
					pdfUploadData.setStatus(PdfConvertStatus.PDF_DOWNLOAD_FAIL.getCode());
					pdfUploadData.setUpdateTime(new Date());
					uploadDataService.updateById(pdfUploadData);
					
					//删除进度条
					redisUtil.hDel(convertRateKey, uploadPdfData.getId());
					
					//pdf状态发生变化，前端页面刷新转换进度条pdf状态
					setRefreshPdfStatusFlag(uploadPdfData.getJgrybm());
				}
			}
			
			//文档加载成功|ofd读取器构建成功
			if(pdDocument == null && ofdReader == null) {
				redisUtil.hDel(convertRateKey, uploadPdfData.getId());
				log.error("【pdf重新转换】文档加载和ofd读取器构建均失败，无法进行转换");
				return;
			}
			
			//查询上次已经转换成功的图片
			List<String> imageIdList = pdfImageService.selectImageIdWithPdfId(pdfId);
			
			//将pdf转换为图片
			Long start = System.currentTimeMillis();
			List<PdfImageDO> imageList = pdfUtil.pdfConvertImage(pdDocument, ofdReader, uploadPdfData, convertRateKey);
			log.error("【pdf重新转换】转换图片结束，pdfid:【{}】，耗时=【{}】ms", uploadPdfData.getId(),
					System.currentTimeMillis() - start);
			
			//更新pdf上传数据对象
			uploadPdfData.setStatus(PdfConvertStatus.CONVERT_FINISH.getCode());
			uploadPdfData.setUpdateTime(new Date());
			uploadDataService.updateById(uploadPdfData);
			
			//pdf状态发生变化，前端页面刷新转换进度条pdf状态
			setRefreshPdfStatusFlag(uploadPdfData.getJgrybm());
			
			//批量勾选多个pdf进行转换，转换后的结果按照勾选的顺序排列
			if(batchConvert) {
				imageData.setImageList(imageList);
                imageData.setImageIdList(imageIdList);
                imageData.setDmsPdfBatch(pdfBatch);
                return;
			}
			
			//卷宗重新转换和编目
			convertedAndToCataloging(pdfBatch, uploadPdfData.getId(), imageList, imageIdList);
		}
		catch(Exception e) {
			log.error("【pdf重新转换】pdf转换异常：{}", e.getMessage());
		}
		finally {
			redisUtil.deleteKey(reConvertKey);
            if (null != pdDocument){
                try {
                    COSDocument document = pdDocument.getDocument();
                    document.close();
                    pdDocument.close();
                }
                catch (IOException e) {
                	log.error("【pdf重新转换】pdf:{}，关闭文档失败:{}", uploadPdfData.getId(), e.getMessage());
                }
            }
            if (null != ofdReader){
                try {
                    ofdReader.close();
                }
                catch (IOException e) {
                	log.error("【pdf重新转换】ofd:{}，关闭文档失败:{}", uploadPdfData.getId(), e.getMessage());
                }
            }
		}
	}
	
    /**
     * 卷宗重新转换和编目
     * @param pdfBatch PdfBatchDO pdf上传的批次
     * @param pdfId String pdf上传主键
     * @param imageIdList List<String> 已转换成功的图片Id
     * @param imageList List<PdfImageDO> 当前图片集合
     */
	@Override
    public void convertedAndToCataloging(PdfBatchDO pdfBatch, String pdfId,
    		List<PdfImageDO> imageList, List<String> imageIdList) {
		
		//获取pdf上传的数据
		PdfUploadDataDO uploadPdfData = uploadDataService.getById(pdfId);
		if(uploadPdfData == null) {
			log.error("【pdf重新转换】重新转换pdf失败，pdfId: {}，没有找到上传的数据", pdfId);
			return;
		}
		
		//材料关联Id
		String materialId = uploadPdfData.getMaterialId();
		
		//MQ未消费|手动批量转换|手动重新转换
		if(StringUtil.isEmpty(materialId)){
			//快速组卷
			if(BmTypeEnum.KSZJ.getType().equals(pdfBatch.getType())) {
				///////////////////////////////////////
			}
			else {
				MaterialInfoDO materialInfo = buildMaterialInfo(pdfBatch, pdfId, imageList);
				materialInfoService.save(materialInfo);
			}
		}
		
		//其它情况
		else {
			String materialSortKey = String.format(RedisConstants.Material_SORT_KEY,
					uploadPdfData.getJgrybm(), pdfBatch.getType());
			
			QueryWrapper<MaterialInfoDO> materialWrapper = new QueryWrapper<>();
			materialWrapper.eq("material_id", materialId);
			List<MaterialInfoDO> materialList = materialInfoService.list(materialWrapper);
			if(CollectionUtil.isNotNull(materialList)) {
				List<String> ids = materialList.stream().map(MaterialInfoDO::getId).collect(Collectors.toList());
				materialInfoService.removeByIds(ids);
				addBmMaterialInfo(pdfBatch, imageList, uploadPdfData, materialSortKey);
			}
			else {
				//快速组卷
				if(BmTypeEnum.KSZJ.getType().equals(pdfBatch.getType())) {
					///////////////////////////////////////
				}
				else {
					addBmMaterialInfo(pdfBatch, imageList, uploadPdfData, materialSortKey);
				}
			}
		}
		
		//删除历史上传成功的图片
		if(CollectionUtil.isNotNull(imageIdList)) {
			pdfImageService.removeByIds(imageIdList);
		}
		
		//pdf转换完成,设置标识位,前端页面刷新
        String refreshKey = String.format(RedisConstants.MATERIAL_REFRESH_FLAG, pdfBatch.getJgrybm());
        redisUtil.set(refreshKey, "1", 120);
    }
	
	/**
	 * 基于图片构建材料
	 * @param pdfBatch PdfBatchDO pdf上传批次
	 * @param pdfId String pdf主键
	 * @param imageList List<PdfImageDO> 图片集合
	 * @return MaterialInfoDO
	 */
	private MaterialInfoDO buildMaterialInfo(PdfBatchDO pdfBatch, String pdfId, List<PdfImageDO> imageList) {
        int xh = 1;
        JSONArray materialFile = new JSONArray();
        
        //构建材料文件
        for (PdfImageDO pdfImage : imageList) {
            JSONObject material = new JSONObject();
            material.put("pdfId", pdfId);
            material.put("addTime", pdfImage.getAddTime().getTime());
            material.put("fileName", pdfImage.getImageName());
            material.put("id", pdfImage.getId());
            material.put("url", pdfImage.getImageUrl());
            material.put("xh", xh);
            xh = xh + 1;
            
            //起始索引
            int imageXh = pdfImage.getXh();
            if (imageXh == 1){
                material.put("index","start");
            }
            else if (imageXh == imageList.size() ){
                material.put("index","end");
            }

            materialFile.add(material);
        }
        
        MaterialInfoDO materialInfo = new MaterialInfoDO();
        materialInfo.setId(StringUtil.getGuid32());
        materialInfo.setJgrybm(pdfBatch.getJgrybm());
        materialInfo.setPageCount(materialFile.size());
        materialInfo.setType(pdfBatch.getType());
        materialInfo.setAddTime(new Date());
        materialInfo.setMaterialFile(materialFile.toJSONString());
        materialInfo.setAddUser(pdfBatch.getAddUser());
        materialInfo.setCatalogId(pdfBatch.getCatalogId());
        materialInfo.setPartCatalogId(pdfBatch.getPartCatalogId());
        materialInfo.setName(pdfBatch.getName());
        materialInfo.setXh(pdfBatch.getXh());

        return materialInfo;
    }
	
	/**
	 * 添加编辑材料并更新上传状态
	 * @param pdfBatch PdfBatchDO pdf上传批次
	 * @param imageList List<PdfImageDO> 图片集合
	 * @param updatePdf PdfUploadDataDO pdf上传对象
	 * @param materialSortKey String 材料排序Key
	 */
	private void addBmMaterialInfo(PdfBatchDO pdfBatch, List<PdfImageDO> imageList, PdfUploadDataDO updatePdf,
			String materialSortKey){
        String guid32 = StringUtil.getGuid32();
        imageList = imageList.stream().filter(s -> !s.getStatus().equals(DamConstants.IMAGE_CONVERT_FAIL))
        		.collect(Collectors.toList());
        int groupNum = ((imageList.size() + pdfSplit - 1) / pdfSplit);
        List<List<PdfImageDO>> lists = Lists.partition(imageList, groupNum);
        AtomicInteger i = new AtomicInteger(1);
        String num = redisUtil.get(materialSortKey);
        Optional.ofNullable(num)
                .filter(s ->!s.isEmpty())
                .map(s -> {
                    Integer integer = Integer.valueOf(s);
                    i.set(integer + 1);
                    int i1 = integer + lists.size();
                    redisUtil.set(materialSortKey,String.valueOf(i1));
                    return s;
                }).orElseGet(() -> {
                	redisUtil.set(materialSortKey,String.valueOf(lists.size()));
                    return String.valueOf(lists.size());
                });
        
        //将图片转换为材料并保存
        List<MaterialInfoDO> mterialInfoList = new ArrayList<>();
        if (CollectionUtil.isNotNull(lists)){
            List<PdfImageDO> finalImageList = imageList;
            lists.stream().map(s ->{
                JSONArray materialFile = new JSONArray();
                s.stream().map( image ->{
                    int xh = image.getXh();
                    JSONObject material = new JSONObject();
                    material.put("addTime",image.getAddTime().getTime());
                    material.put("fileName",image.getImageName());
                    material.put("id",image.getId());
                    material.put("url",image.getImageUrl());
                    material.put("xh",xh);
                    material.put("pdfId", updatePdf.getId());
                    if (xh == 1){
                        material.put("index","start");
                    }else if (xh == finalImageList.size() ){
                        material.put("index","end");
                    }
                    materialFile.add(material);
                    return image;
                }).collect(Collectors.toList());
                
                MaterialInfoDO materialInfo = new MaterialInfoDO();
                materialInfo.setId(StringUtil.getGuid32());
                materialInfo.setJgrybm(updatePdf.getJgrybm());
                materialInfo.setPageCount(materialFile.size());
                materialInfo.setType(pdfBatch.getType());
                materialInfo.setAddTime(new Date());
                materialInfo.setMaterialFile(materialFile.toJSONString());
                materialInfo.setCatalogId(pdfBatch.getCatalogId());
                materialInfo.setPartCatalogId(pdfBatch.getPartCatalogId());
                materialInfo.setName(pdfBatch.getName());
                materialInfo.setMaterialId(guid32);
                materialInfo.setXh(i.getAndIncrement());
                mterialInfoList.add(materialInfo);
                
                return s;
            }).collect(Collectors.toList());

            //批量保存材料
            if (CollectionUtil.isNotNull(mterialInfoList)){
                materialInfoService.saveBatch(mterialInfoList);
            }
            
            //更新pdf上传状态
            updatePdf.setStatus(PdfConvertStatus.CONVERT_FINISH.getCode());
            updatePdf.setUpdateTime(new Date());
            updatePdf.setMaterialId(guid32);
            uploadDataService.updateById(updatePdf);
        }
    }
}
