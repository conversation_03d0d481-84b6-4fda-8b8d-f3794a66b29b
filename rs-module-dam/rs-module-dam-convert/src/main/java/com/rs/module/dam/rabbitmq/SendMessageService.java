package com.rs.module.dam.rabbitmq;

import javax.annotation.Resource;

import org.springframework.amqp.rabbit.core.ChannelCallback;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rs.module.dam.config.RabbitMqConfig;
import com.rs.module.dam.controller.vo.PushPdfData;
import com.rs.module.dam.rabbitmq.vo.PdfCorrelationData;

/**
 * RabbitMQ推送消息服务
 * <AUTHOR>
 * @date 2025年4月19日
 */
@Component
public class SendMessageService {

	@Resource
	private RabbitTemplate rabbitTemplate;
	
	@Resource
	private RabbitAdmin rabbitAdmin;
	
	/**
	 * 推送pdf转换消息
	 * @param pdfData PushPdfData 消息对象
	 */
	public void pushPdfConvertMessage(PushPdfData pdfData) {
		PdfCorrelationData pdfCorrelationData = new PdfCorrelationData();
		pdfCorrelationData.setData(pdfData);		
		rabbitTemplate.convertAndSend(RabbitMqConfig.CONVERT_PDF_EXCHANGE, RabbitMqConfig.CONVERT_ROUTING_KEY,
				JSONObject.toJSONString(pdfData), pdfCorrelationData);
	}
	
	/**
	 * 推送pdf转换消息
	 * @param data JSONObject 消息对象
	 */
	public void pushPdfReConvertMessage(JSONObject data) {
		rabbitTemplate.convertAndSend(RabbitMqConfig.RECONVERT_PDF_EXCHANGE, RabbitMqConfig.RECONVERT_ROUTING_KEY,
				data.toJSONString());
	}
	
	/**
	 * 发送进度消息
	 * @param data String 消息数据
	 */
	public void sendRateFanoutMessage(String data) {
		rabbitTemplate.convertAndSend(RabbitMqConfig.CONVERT_RATE_EXCHANGE, "", data);
	}
	
	/**
	 * 获取消息数量
	 * @param queueName String 队列名称
	 * @return int
	 * @throws Exception
	 */
	public int getMessageCount(String queueName) throws Exception {
		AMQP.Queue.DeclareOk declareOk = getDeclareOk(queueName);		
		return declareOk.getMessageCount();
	}
	
	/**
	 * 获取消费者数量
	 * @param queueName String 队列名称
	 * @return int
	 * @throws Exception
	 */
	public int getConsumerCount(String queueName) throws Exception {
		AMQP.Queue.DeclareOk declareOk = getDeclareOk(queueName);		
		return declareOk.getConsumerCount();
	}
	
	/**
	 * 获取队列声明
	 * @param queueName String 队列名称
	 * @return AMQP.Queue.DeclareOk
	 */
	private AMQP.Queue.DeclareOk getDeclareOk(String queueName){
		return rabbitAdmin.getRabbitTemplate().execute(
			new ChannelCallback<AMQP.Queue.DeclareOk>() {
				
				@Override
				public AMQP.Queue.DeclareOk doInRabbit(Channel channel) throws Exception {
					return channel.queueDeclarePassive(queueName);
				}
			}
		);
	}
}
