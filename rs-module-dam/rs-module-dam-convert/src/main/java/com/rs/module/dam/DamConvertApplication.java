package com.rs.module.dam;

import java.net.UnknownHostException;

import org.dromara.x.file.storage.spring.EnableFileStorage;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.mzt.bsp.logapi.starter.annotation.EnableLogRecord;
import com.rs.framework.common.util.spring.SpringUtils;
import com.rs.module.dam.config.WebSocketServer;

/**
 * 数字档案转换系统启动类
 * <AUTHOR>
 * @date 2025年4月16日
 */
@SpringBootApplication(
		exclude = {
				DruidDataSourceAutoConfigure.class,
				MongoAutoConfiguration.class,
				MongoDataAutoConfiguration.class
		}
)
@ComponentScan(
		value = {"com.rs.*", "com.bsp.*"},
		excludeFilters = @ComponentScan.Filter(
				type = FilterType.ASSIGNABLE_TYPE,
				classes = {
						WebSocketServer.class
				}
		)
)
@EnableLogRecord(systemMark = "dam-convert")
@EnableFileStorage
public class DamConvertApplication {

	public static void main(String[] args) throws UnknownHostException {
		ConfigurableApplicationContext application = SpringApplication.run(DamConvertApplication.class, args);
		SpringUtils.printStartLog(application);
	}
}
