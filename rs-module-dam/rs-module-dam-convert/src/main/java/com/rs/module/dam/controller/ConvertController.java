package com.rs.module.dam.controller;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.cons.CommonConstants;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.rs.adapter.bsp.api.BpmApi;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.dam.config.RabbitMqConfig;
import com.rs.module.dam.constant.PdfConvertStatus;
import com.rs.module.dam.constant.RedisConstants;
import com.rs.module.dam.controller.vo.PushPdfData;
import com.rs.module.dam.entity.material.PdfBatchDO;
import com.rs.module.dam.entity.material.PdfUploadDataDO;
import com.rs.module.dam.rabbitmq.SendMessageService;
import com.rs.module.dam.service.ConvertService;
import com.rs.module.dam.service.material.PdfBatchService;
import com.rs.module.dam.service.material.PdfUploadDataService;
import com.rs.module.dam.util.RedisTemplateUtil;
import com.rs.module.dam.util.ThreadPoolUtil;
import com.rs.module.dam.vo.material.PdfConvertRate;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "卷宗转换接口")
@RestController
@RequestMapping("/dam-convert/pdf/convert")
@Validated
public class ConvertController {

	@Value("${conf.convert.websocket}")
	private String websocket;

	@Resource
	private ConvertService convertService;

	@Resource
	private PdfUploadDataService uploadDataService;

	@Resource
	private PdfBatchService pdfBatchService;

	@Resource
	private SendMessageService sendMessageService;

	@Resource
	private RedisTemplateUtil redisUtil;
	@Resource
	private BpmApi bpmApi;

	@GetMapping("/getConfIpAndPort")
    @ApiOperation(value = "获取卷宗配置的IP和端口")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<String> getConfIpAndPort() {
		return success(websocket);
    }

	@PostMapping("/getConvertPdfList")
    @ApiOperation(value = "获取转换的pdf记录")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "jgrybm", value = "监管人员编码"),
		@ApiImplicitParam(name = "type", value = "类型(0:未OCR,1:编目,2:组卷,3:快速组卷)"),
		@ApiImplicitParam(name = "materialSource", value = "材料来源"),
		@ApiImplicitParam(name = "sortField", value = "排序字段"),
		@ApiImplicitParam(name = "sortMethod", value = "排序方式{asc|desc)")
	})
    public CommonResult<List<Map<String, Object>>> getConvertPdfList(
    		@RequestParam("jgrybm") String jgrybm,
    		@RequestParam("type") String type,
    		@RequestParam("materialSource") String materialSource,
    		@RequestParam(value = "sortField", required = false) String sortField,
    		@RequestParam(value = "sortMethod", required = false) String sortMethod) {
		Map<String, Object> params = new HashMap<>();
		params.put("jgrybm", jgrybm);
		params.put("types", Arrays.asList(type.split(CommonConstants.DEFAULT_SPLIT_STR)));
		params.put("materialSource", materialSource);

		//处理排序参数
		if(StringUtil.isNotEmpty(sortField)) {
			params.put("sortField", sortField);
		}
		if(StringUtil.isNotEmpty(sortField)) {
			params.put("sortMethod", sortMethod);
		}

		//获取转换的pdf记录
		List<PdfUploadDataDO> uploadDataList = uploadDataService.selectConvertPdfWithType(params);

		//根据目录汇总数据
		MultiValueMap<String, PdfUploadDataDO> multiValueMap = new LinkedMultiValueMap<>();
		for(PdfUploadDataDO uploadData : uploadDataList) {
			String catalogName = uploadData.getName();
			if(StringUtil.isNotEmpty(catalogName)) {
				multiValueMap.add(catalogName, uploadData);
			}
			else {
				multiValueMap.add("暂无目录", uploadData);
			}
		}

		//转换成集合(catalogName、pdfList)
		List<Map<String, Object>> pdfList = new ArrayList<>();
		for (Map.Entry<String, List<PdfUploadDataDO>> listEntry : multiValueMap.entrySet()) {
            Map<String, Object> data = new HashMap<>();
            data.put("catalogName", listEntry.getKey());
            data.put("pdfList", listEntry.getValue());
            pdfList.add(data);
        }

		return success(pdfList);
    }

	@PostMapping("/addPdfMsg")
    @ApiOperation(value = "上传pdf完成后添加消息")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "jgrybm", value = "监管人员编码"),
		@ApiImplicitParam(name = "type", value = "类型(0:未OCR,1:编目,2:组卷,3:快速组卷)"),
		@ApiImplicitParam(name = "materialSource", value = "材料来源"),
		@ApiImplicitParam(name = "templateId", value = "模板Id"),
		@ApiImplicitParam(name = "catalogId", value = "目录Id"),
		@ApiImplicitParam(name = "partCatalogId", value = "分卷目录Id"),
		@ApiImplicitParam(name = "name", value = "名称"),
		@ApiImplicitParam(name = "pdfMsg", value = "pdf消息"),
		@ApiImplicitParam(name = "xh", value = "序号"),
		@ApiImplicitParam(name = "batchId", value = "批次Id"),
		@ApiImplicitParam(name = "businessId", value = "业务Id"),
		@ApiImplicitParam(name = "reConvert", value = "是否重新转换"),
	})
	public CommonResult<?> addPdfMsg(
			@RequestParam("jgrybm") String jgrybm,
			@RequestParam("type") String type,
			@RequestParam("materialSource") String materialSource,
			@RequestParam(value = "templateId", required = false) String templateId,
			@RequestParam(value = "catalogId", required = false) String catalogId,
			@RequestParam(value = "partCatalogId", required = false) String partCatalogId,
			@RequestParam(value = "name", required = false) String name,
			@RequestParam(value = "pdfMsg", required = false) String pdfMsg,
			@RequestParam(value = "xh", required = false) Integer xh,
			@RequestParam(value = "batchId", required = false) String batchId,
			@RequestParam(value = "businessId", required = false) String businessId,
			@RequestParam(value = "reConvert", required = false) boolean reConvert) {
		try {
			//上传Id
			String id = StringUtil.getGuid32();

			//批次Id
			String pdfBatchId = StringUtil.getGuid32();

			//重复的pdf名称
			List<String> padNameList = new ArrayList<>();

			//上传的pdf数据
			JSONArray pdfList = JSONObject.parseArray(pdfMsg);

			//构建推送的pdf数据对象
			PushPdfData pushPdfData = new PushPdfData();
			pushPdfData.setUuid(id);
			pushPdfData.setJgrybm(jgrybm);
			if(StringUtil.isNotEmpty(batchId)) {
				pushPdfData.setBatchId(batchId);
				pushPdfData.setBusinessId(businessId);
			}

			//构建上传批次对象和数据传输对象
			PdfBatchDO pdfBatchDO = new PdfBatchDO();
			pdfBatchDO.setId(pdfBatchId);
			pdfBatchDO.setJgrybm(jgrybm);
			pdfBatchDO.setType(type);
			pdfBatchDO.setCatalogId(catalogId);
			pdfBatchDO.setPartCatalogId(partCatalogId);
			pdfBatchDO.setName(name);
			pdfBatchDO.setXh(xh);
			pdfBatchDO.setTemplateId(templateId);
			if(StringUtil.isNotEmpty(batchId)) {
				pdfBatchDO.setBatchId(batchId);
				pdfBatchDO.setBusinessId(businessId);
				pushPdfData.setBatchId(batchId);
				pushPdfData.setBusinessId(businessId);
			}
			pushPdfData.setPdfBatch(pdfBatchDO);

			//循环处理上传的pdf数据
			for (int i = 0; i < pdfList.size(); i ++) {
				JSONObject jsonObject = pdfList.getJSONObject(i);
				String url = jsonObject.getString("url");
				String pdfName = jsonObject.getString("fileName");
				Long pdfSize = jsonObject.getLong("fileSize");
				String md5 = jsonObject.getString("md5");

                //非重新转换时判断是否重复上传
                if(!reConvert) {
                	Map<String,Object> params = new HashMap<>();
                    params.put("jgrybm", jgrybm);
                    params.put("catalogId", catalogId);
                    params.put("partCatalogId", partCatalogId);
                    params.put("type", type);
                    params.put("pdfName", pdfName);
                    params.put("pdfSize", pdfSize);
                    //params.put("pdfUrl", url);
                    //params.put("md5", md5);

                    //查找已有的pdf上传文件
                	List<PdfUploadDataDO> pdfDataList = uploadDataService.selectRepeatPdf(params);
                	if (CollectionUtil.isNotNull(pdfDataList)){
                        padNameList.add(pdfName);
                        continue;
                    }
                }

                //构建pdf上传对象
                PdfUploadDataDO pdfUploadDataDO = new PdfUploadDataDO();
                pdfUploadDataDO.setId(StringUtil.getGuid32());
                pdfUploadDataDO.setJgrybm(jgrybm);
                pdfUploadDataDO.setPdfUrl(url);
                pdfUploadDataDO.setMaterialSource(materialSource);
                pdfUploadDataDO.setPdfName(pdfName);
                pdfUploadDataDO.setPdfSize(pdfSize);
                pdfUploadDataDO.setAddTime(new Date());
                pdfUploadDataDO.setUpdateTime(new Date());
                pdfUploadDataDO.setUploadData(jsonObject.toJSONString());
                pdfUploadDataDO.setStatus(PdfConvertStatus.UN_START.getCode());
                pdfUploadDataDO.setBatchId(pdfBatchId);
                pdfUploadDataDO.setMd5(md5);

                //保存pdf上传对象
                uploadDataService.save(pdfUploadDataDO);
			}

			//存在上传的文件
			if (padNameList.size() != pdfList.size()) {
				pdfBatchService.save(pdfBatchDO);

				//推送pdf上传消息到队列
				sendMessageService.pushPdfConvertMessage(pushPdfData);
			}

			return success(JSONUtil.createObj()
					.set("batchId", pdfBatchId)
					.set("repeatPdf", padNameList)
					.set("taskId", id));
		}
		catch(Exception e) {
			return CommonResult.error("开始转换pdf出现异常：" + e.getMessage());
		}
	}

	@GetMapping("/getPdfConvertRate")
    @ApiOperation(value = "获取pdf转换进度")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "jgrybm", value = "监管人员编码"),
		@ApiImplicitParam(name = "type", value = "类型(0:未OCR,1:编目,2:组卷,3:快速组卷)")
	})
    public CommonResult<?> getPdfConvertRate(
    		@RequestParam("jgrybm") String jgrybm,
    		@RequestParam("type") String type) {
		try {
			List<PdfConvertRate> convertRateData = convertService.getConvertRate(jgrybm, type);
			return success(convertRateData);
		}
		catch(Exception e) {
			return CommonResult.error("查询转换任务数量失败：" + e.getMessage());
		}
    }

	@GetMapping("/cancelConvert")
    @ApiOperation(value = "取消pdf转换")
	@ApiImplicitParam(name = "pdfId", value = "pdf主键")
    public CommonResult<?> cancelConvert(@RequestParam("pdfId") String pdfId) {
		try {
			PdfUploadDataDO uploadDataDO = uploadDataService.getById(pdfId);
			int status = uploadDataDO.getStatus();

			//未开始转换
			if(PdfConvertStatus.UN_START.equals(status)) {
				uploadDataDO.setStatus(PdfConvertStatus.CONVERT_CANCEL.getCode());
				uploadDataDO.setUpdateTime(new Date());
				uploadDataService.updateById(uploadDataDO);

				//pdf状态发生变化，前端页面刷新转换进度pdf状态
				convertService.setRefreshPdfStatusFlag(uploadDataDO.getJgrybm());

				return success("任务正在取消中，请稍等");
			}

			//转换中
			else if(PdfConvertStatus.CONVERTING.equals(status)) {
				uploadDataDO.setStatus(PdfConvertStatus.CONVERT_CANCEL.getCode());
				uploadDataDO.setUpdateTime(new Date());
				uploadDataService.updateById(uploadDataDO);

				//pdf状态发生变化，前端页面刷新转换进度pdf状态
				convertService.setRefreshPdfStatusFlag(uploadDataDO.getJgrybm());

				//更新缓存
				redisUtil.sAdd(RedisConstants.CANCEL_CONVERT_PDF_KEY, pdfId);

				return success("任务正在取消中，请稍等");
			}

			//转换完成
			else if(PdfConvertStatus.CONVERT_FINISH.equals(status)) {
				return CommonResult.error("任务已经转换完成，不能取消");
			}

			//其它
			else {
				return CommonResult.error("任务暂不能取消");
			}
		}
		catch(Exception e) {
			return CommonResult.error("查询转换任务数量失败：" + e.getMessage());
		}
    }

	@PostMapping("/ownReConvert")
    @ApiOperation(value = "手动调用接口进行转换(前端不调用)")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "status", value = "状态"),
		@ApiImplicitParam(name = "pageNo", value = "页号"),
		@ApiImplicitParam(name = "pageSize", value = "每页大小"),
		@ApiImplicitParam(name = "pdfId", value = "pdf主键")
	})
    public CommonResult<?> ownReConvert(
    		@RequestParam("status") Integer status,
    		@RequestParam("pageNo") Integer pageNo,
    		@RequestParam("pageSize") Integer pageSize,
    		@RequestParam(value = "pdfId", required = false) String pdfId) {
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("status", status);
		if(StringUtil.isNotEmpty(pdfId)) {
			paramMap.put("id", pdfId);
		}

		//获取pdf上传数据
		Page<PdfUploadDataDO> pdfDataListPage = uploadDataService.getPageBy(pageNo, pageSize, paramMap);
		List<PdfUploadDataDO> pdfDataList = pdfDataListPage.getRecords();

		//循环重新转换
		for (PdfUploadDataDO uploadPdfData : pdfDataList) {
			JSONObject msgData = new JSONObject();
            msgData.put("uuid", StringUtil.getGuid32());
            msgData.put("jgrybm", uploadPdfData.getJgrybm());
            msgData.put("pdfId", uploadPdfData.getId());

            sendMessageService.pushPdfReConvertMessage(msgData);
		}

		return success("手动调用接口成功");
	}

	@PostMapping("/reConvertPdf")
    @ApiOperation(value = "pdf重新转换")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "jgrybm", value = "监管人员编码"),
		@ApiImplicitParam(name = "pdfId", value = "pdf主键"),
		@ApiImplicitParam(name = "materialSource", value = "材料来源")
	})
    public CommonResult<?> reConvertPdf(
    		@RequestParam("jgrybm") String jgrybm,
            @RequestParam("pdfId") String pdfId,
            @RequestParam(value = "materialSource", required = false) String materialSource) {

		//取消状态-清除缓存取消状态的pdf主键值
		boolean hasMember = redisUtil.sIsMember(RedisConstants.CANCEL_CONVERT_PDF_KEY, pdfId);
		if(hasMember) {
			redisUtil.sRem(RedisConstants.CANCEL_CONVERT_PDF_KEY, pdfId);
		}

		//异步转换
		ThreadPoolUtil.getPool().execute(new Runnable() {
			@Override
			public void run() {
				convertService.asynchronousConvert(jgrybm, pdfId, false, null, materialSource);
			}
		});

		return success("调用pdf重新转换接口成功");
	}

	@GetMapping("/getMessageCount")
    @ApiOperation(value = "获取消息数量")
    @ApiImplicitParam(name = "taskId", value = "任务Id")
    public CommonResult<?> getMessageCount(String taskId) {
		try {
			String queueName = RabbitMqConfig.getConvertPdfQueue();

			//消息数量
			int messageCount = sendMessageService.getMessageCount(queueName);

			//消费者数量
			int consumerCount = sendMessageService.getConsumerCount(queueName);

			//转换数量
			int num = (int) redisUtil.rank(RedisConstants.PDF_CONVERT_KEY, taskId);

			return success(JSONUtil.createObj()
					.set("messageCount", messageCount)
					.set("consumerCount", consumerCount)
					.set("num", num));
		}
		catch(Exception e) {
			return CommonResult.error("查询转换任务数量失败：" + e.getMessage());
		}
    }

	@GetMapping("/getStartApproveUser")
	public CommonResult<Map<String, Object>> getArchivePrisonerListByEventId(@RequestParam("defKey") String defKey,
																			 @RequestParam("startUserId") String startUserId) {
		Map<String, Object> startApproveUser = bpmApi.getStartApproveUser(defKey, startUserId,null);
		System.out.println("=============================================");
		System.out.println(JSONObject.parseObject(JSONObject.toJSONString(startApproveUser)));
		return success(startApproveUser);
	}
}
