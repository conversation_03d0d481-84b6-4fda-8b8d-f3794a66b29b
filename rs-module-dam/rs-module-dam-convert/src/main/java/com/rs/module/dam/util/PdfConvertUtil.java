package com.rs.module.dam.util;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.dam.constant.RedisConstants;
import com.rs.module.dam.vo.material.PdfConvertRate;

/**
 * pdf转换工具类
 * <AUTHOR>
 * @date 2025年4月24日
 */
@Component
public class PdfConvertUtil {
	
	@Resource
    private RedisTemplateUtil redisUtil;

	/**
	 * 初始化转换进度条(避免pdf下载转换时间过长,一时进度条不能显示)
	 * @param jgrybm String 监管人员编码
	 * @param bmType String 编目类型(0：未OCR,1:编目,2：组卷 3: 快速组卷)
	 * @param pdfId String pdf主键
	 * @param pdfName String pdf名称
	 */
	public void initConvertRate(String jgrybm, String bmType, String pdfId, String pdfName) {
		String convertRateKey = String.format(RedisConstants.PDF_CONVERT_RATE_KEY, jgrybm, bmType);
		PdfConvertRate pdfConvertRate = new PdfConvertRate();
		pdfConvertRate.setPdfId(pdfId);
		pdfConvertRate.setPageCount(1);
		pdfConvertRate.setPdfName(pdfName);
		pdfConvertRate.setConvertCount(0);
		pdfConvertRate.setPause("0");
		redisUtil.hSet(convertRateKey, pdfId, JSONObject.toJSONString(pdfConvertRate));
		redisUtil.expires(convertRateKey, 600);
	}
}
