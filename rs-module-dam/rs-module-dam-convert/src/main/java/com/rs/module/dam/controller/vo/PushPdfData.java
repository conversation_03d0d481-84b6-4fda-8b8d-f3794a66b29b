package com.rs.module.dam.controller.vo;

import java.util.List;

import com.rs.module.dam.entity.material.PdfBatchDO;

import lombok.Data;

/**
 * 推送的pdf数据对象
 * <AUTHOR>
 * @date 2025年4月19日
 */
@Data
public class PushPdfData {

	//唯一标识
	private String uuid;
	
	//监管人员编码
	private String jgrybm;
	
	//pdf批次信息
	private PdfBatchDO pdfBatch;
	
	//是否重新转换
	private boolean reConvert;

	//pdf文件Id集合
    private List<String> pdfIdList;

    //目录Id
    private String catalogId;

    //模板Id
    private String templateId;

    //业务Id
    private String businessId;

    //批次Id
    private String batchId;
}
