package com.rs.module.dam.rabbitmq.vo;

import org.springframework.amqp.rabbit.connection.CorrelationData;

import com.rs.module.dam.controller.vo.PushPdfData;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * pdf消息封装体
 * <AUTHOR>
 * @date 2025年4月20日
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PdfCorrelationData extends CorrelationData{

	//推送的pdf数据对象
	private PushPdfData data;
	
	//重试次数
	private int retryCount = 0;
}
