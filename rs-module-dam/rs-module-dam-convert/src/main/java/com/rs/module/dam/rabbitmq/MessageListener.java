package com.rs.module.dam.rabbitmq;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.google.common.collect.Lists;
import com.rabbitmq.client.Channel;
import com.rs.module.dam.constant.*;
import com.rs.module.dam.controller.vo.PushPdfData;
import com.rs.module.dam.entity.material.*;
import com.rs.module.dam.exception.CommonException;
import com.rs.module.dam.service.ConvertService;
import com.rs.module.dam.service.material.MaterialInfoService;
import com.rs.module.dam.service.material.PdfConvertLogService;
import com.rs.module.dam.service.material.PdfUploadDataService;
import com.rs.module.dam.util.PdfConvertUtil;
import com.rs.module.dam.util.PdfUtil;
import com.rs.module.dam.util.RedisTemplateUtil;
import com.rs.module.dam.vo.material.PdfBatchImage;
import com.rs.module.dam.vo.material.PdfConvertRate;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.cos.COSDocument;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.ofdrw.reader.OFDReader;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 消息监听器
 * <AUTHOR>
 * @date 2025年4月20日
 */
@Component
@Slf4j
public class MessageListener {

	@Resource
    private RedisTemplateUtil redisUtil;

	@Resource
	private PdfUploadDataService uploadDataService;

	@Resource
	private MaterialInfoService materialInfoService;

	@Resource
	private PdfConvertLogService convertLogService;

	@Resource
	private ConvertService convertService;

	@Resource
	private PdfConvertUtil pdfConvertUtil;

	@Resource
	private PdfUtil pdfUtil;

    @Value("${conf.pdf.split:1}")
    private int pdfSplit;

	/**
	 * pdf转换消费者(L397)
	 * @param msg String 转换消息
	 * @param channel Channel 通道
	 * @param message Message 队列消息
	 */
	@RabbitHandler
	@RabbitListener(queues = "${conf.convert.queue.convert}")
	public void msgConvertConsumer(String msg, Channel channel, Message message) throws IOException{
		log.info("【MessageListener】接收转换的消息:{}", msg);

		//获取消息json格式
		PushPdfData pushPdfData = JSONObject.parseObject(msg, PushPdfData.class);
        String uuid = pushPdfData.getUuid();

		try {
			//转换加锁
			boolean lock = redisUtil.lock(uuid, "1", 600);
	        if(lock) {
	        	PdfBatchDO pdfBatch = pushPdfData.getPdfBatch();

	        	//上传的pdf数据
	        	List<PdfUploadDataDO> pdfDataList = uploadDataService.list(new QueryWrapper<PdfUploadDataDO>()
	        			.eq("batch_id", pdfBatch.getId()));

	        	//编目类型(0：未OCR,1:编目,2：组卷,3: 快速组卷)
	        	String bmType = pdfBatch.getType();

	        	//pdf上传的图片列表
	        	List<PdfBatchImage> pdfBatchImageList = new ArrayList<>();

	        	//循环处理上传的pdf
	        	for(PdfUploadDataDO uploadPdfData : pdfDataList) {

	        		//再查一次数据，状态可能会变更
	        		PdfUploadDataDO pud = uploadDataService.getById(uploadPdfData.getId());
	        		if(PdfConvertStatus.CONVERT_CANCEL.getCode() == pud.getStatus()) {
	        			uploadPdfData.setStatus(pud.getStatus());
	        			uploadPdfData.setUpdateTime(new Date());
	        			continue;
	        		}

	        		//pdf文档和ofd读写器
	        		PDDocument pdDocument = null;
	        		OFDReader ofdReader = null;

	        		try {
	        			//pdf转换进度条Key
	        			String convertRateKey = String.format(RedisConstants.PDF_CONVERT_RATE_KEY,
	        					uploadPdfData.getJgrybm(), bmType);

	        			//初始化转换进度条,避免pdf下载时间过长,一时进度条不能显示
	        			pdfConvertUtil.initConvertRate(uploadPdfData.getJgrybm(), pdfBatch.getType(),
	        					uploadPdfData.getId(), uploadPdfData.getPdfName());

	        			try {
	        				//pdf文件地址
	    	        		String pdfUrl = uploadPdfData.getPdfUrl();

	    	        		//pdf数据加载
	        				if(pdfUrl.toLowerCase().endsWith(".pdf")) {
	        					pdDocument = pdfUtil.loadPdfDocument(pdfUrl);
	        				}
	        				else if(pdfUrl.toLowerCase().endsWith(".ofd")) {
	        					ofdReader = pdfUtil.buildOfdReader(pdfUrl);
	        				}
	        			}
	        			catch(CommonException e) {
	        				if(PdfDealCode.PDF_DOWNLOAD_FAIL.getCode().equals(e.getCode())) {
	        					PdfUploadDataDO pdfUploadData = new PdfUploadDataDO();
	        					pdfUploadData.setId(uploadPdfData.getId());
	        					pdfUploadData.setStatus(PdfConvertStatus.PDF_DOWNLOAD_FAIL.getCode());
	        					pdfUploadData.setUpdateTime(new Date());
	        					uploadDataService.updateById(pdfUploadData);

	        					//删除进度条
	        					redisUtil.hDel(convertRateKey, uploadPdfData.getId());

	        					//pdf状态发生变化，前端页面刷新转换进度条pdf状态
	        					convertService.setRefreshPdfStatusFlag(uploadPdfData.getJgrybm());
	        				}
	        			}

	        			//文档加载成功|ofd读取器构建成功
	        			if(pdDocument == null && ofdReader == null) {
	        				redisUtil.hDel(convertRateKey, uploadPdfData.getId());
	        				log.error("【MessageListener】文档加载和ofd读取器构建均失败，无法进行转换");
	        				throw new RuntimeException("文档加载和ofd读取器构建均失败，无法进行转换");
	        			}

	        			//将pdf转换为图片
	        			Long start = System.currentTimeMillis();
	        			List<PdfImageDO> imageList = pdfUtil.pdfConvertImage(pdDocument, ofdReader, uploadPdfData, convertRateKey);
	        			PdfUtil.printListenerDurationLog(uploadPdfData.getId(), "转换图片结束", start);

	        			//未编目转换处理
	        			if(BmTypeEnum.UN_BM.getType().equals(bmType)) {
	        				String materialSortKey = String.format(RedisConstants.Material_SORT_KEY, uploadPdfData.getJgrybm(), bmType);

	        				//添加未编目上传材料
	        				addUnBmUploadMaterial(imageList, uploadPdfData, materialSortKey);

	        				//pdf转换完成,设置标识位,前端页面刷新
	        				List<PdfConvertRate> convertRateList = convertService.getConvertRate(uploadPdfData.getJgrybm(), bmType);
	        				if(CollectionUtil.isNull(convertRateList)) {
	        					convertService.setRefreshPdfStatusFlag(uploadPdfData.getJgrybm());
	        				}
	        			}

	        			//其它处理
	        			else {
	        				//添加到pdf上传的图片列表
	        				PdfBatchImage batchPdfImage = new PdfBatchImage();
                            batchPdfImage.setPdfId(uploadPdfData.getId());
                            batchPdfImage.setImageList(imageList);
                            pdfBatchImageList.add(batchPdfImage);

                            //更新pdf上传数据对象
                			uploadPdfData.setStatus(PdfConvertStatus.CONVERT_FINISH.getCode());
                			uploadPdfData.setUpdateTime(new Date());
                			uploadDataService.updateById(uploadPdfData);
	        			}
	        		}
	        		catch(Exception e) {
	        			log.error("pdf转换过程中失败：{}", e.getMessage());
	        		}
	        		finally {
	        			if(null != pdDocument) {
	        				try {
	        					COSDocument cosd = pdDocument.getDocument();
	        					cosd.close();
	        					pdDocument.close();
	        				}
	        				catch(Exception e) {
	        					log.error("pdf:{} 关闭文档失败:{}", uploadPdfData.getId(), e.getMessage());
	        				}
	        			}
	        			if(null != ofdReader) {
	        				try {
	        					ofdReader.close();
	        				}
	        				catch(Exception e) {
	        					log.error("ofd:{} 关闭文档失败:{}", uploadPdfData.getId(), e.getMessage());
	        				}
	        			}
	        		}
	        	}

	        	//已编目或快速组卷转换处理
	        	if(BmTypeEnum.BM.getType().equals(bmType) || BmTypeEnum.KSZJ.getType().equals(bmType)) {

	        		//没有需要插入的图片
	        		if(CollectionUtil.isNull(pdfBatchImageList)) {
	        			return;
	        		}

	        		//获取卷宗材料
	        		QueryWrapper<MaterialInfoDO> materialWrapper = new QueryWrapper<MaterialInfoDO>();
	        		materialWrapper.eq("jgrybm", pdfBatch.getJgrybm());
	        		materialWrapper.eq("type", bmType);
	        		materialWrapper.eq("catalog_id", pdfBatch.getCatalogId());
	        		if(StringUtil.isNotEmpty(pdfBatch.getPartCatalogId())){
	        			materialWrapper.eq("part_catalog_id", pdfBatch.getPartCatalogId());
	        		}
	        		MaterialInfoDO materialInfo = materialInfoService.getOne(materialWrapper);

	        		//存在卷宗材料
	        		if(null != materialInfo) {
	        			//构建材料文件数组
                        JSONArray materialArr = JSONObject.parseArray(materialInfo.getMaterialFile());
                        buildMaterialFilesByBatchImage(pdfBatchImageList, materialArr);

                        //更新卷宗材料
                        materialInfo.setMaterialFile(materialArr.toJSONString());
                        materialInfo.setPageCount(materialArr.size());
                        if(StringUtil.isNotEmpty(pdfBatch.getTemplateId())){
                        	materialInfo.setTemplateId(pdfBatch.getTemplateId());
                        }
                        materialInfoService.updateById(materialInfo);
	        		}
	        		else {
	        			//构建材料文件数组
	        			JSONArray materialArr = new JSONArray();
	        			buildMaterialFilesByBatchImage(pdfBatchImageList, materialArr);

	        			//保存卷宗材料
	        			materialInfo = buildMaterialInfo(pdfBatch, bmType, materialArr);
	        			materialInfoService.save(materialInfo);
	        		}

	        		//更新pdf上传数据
        			String materialInfoId = materialInfo.getId();
        			pdfDataList.forEach(pdfUploadDataDO -> pdfUploadDataDO.setMaterialId(materialInfoId));
        			uploadDataService.updateBatchById(pdfDataList);

	        		//pdf转换完成,设置标识位,前端页面刷新
    				List<PdfConvertRate> convertRateList = convertService.getConvertRate(pdfBatch.getJgrybm(), bmType);
    				if(CollectionUtil.isNull(convertRateList)) {
    					convertService.setRefreshPdfStatusFlag(pdfBatch.getJgrybm());
    				}
	        	}

	        	//pdf转换完成,设置标识位,前端页面刷新
                String refreshKey = String.format(RedisConstants.MATERIAL_REFRESH_FLAG, pdfBatch.getJgrybm());
                redisUtil.set(refreshKey, "1", 120);
	        }

	        //清除缓存数据
            redisUtil.rmZSet(RedisConstants.PDF_CONVERT_KEY, uuid);
		}
		catch(Exception e) {
			log.error("pdf转换消息异常：{}", e.getMessage());

			//插入异常日志
			PdfConvertLogDO convertLog = new PdfConvertLogDO();
			convertLog.setId(StringUtil.getGuid32());
			convertLog.setErrorLog(e.getMessage());
			convertLog.setPushData(msg);
			convertLog.setAddTime(new Date());
			convertLogService.save(convertLog);

			//清除缓存数据
            redisUtil.rmZSet(RedisConstants.PDF_CONVERT_KEY, uuid);
		}
		finally {
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
		}
	}

	/**
	 * 添加未编目上传材料
	 * @param imageList List<PdfImageDO> pdf图片集合
	 * @param uploadPdfData PdfUploadDataDO pdf上传数据对象
	 * @param materialSortKey String 材料排序Key
	 */
	private void addUnBmUploadMaterial(List<PdfImageDO> imageList, PdfUploadDataDO uploadPdfData, String materialSortKey) {
		String materialId = StringUtil.getGuid32();

		//非转换失败的图片集合(转换成功|pdf中途取消转换)
		imageList = imageList.stream().filter(s -> !s.getStatus().equals(DamConstants.IMAGE_CONVERT_FAIL))
				.collect(Collectors.toList());
		List<List<PdfImageDO>> lists = Lists.partition(imageList, pdfSplit);
		AtomicInteger integer = new AtomicInteger(1);
		String num = redisUtil.get(materialSortKey);
		Optional.ofNullable(num)
			.filter(s -> !s.isEmpty())
			.map( s -> {
				Integer i = Integer.valueOf(s);
				integer.set(i + 1);
				int i1 = i + lists.size();
				redisUtil.set(materialSortKey, String.valueOf(i1));
				return s;
			})
			.orElseGet(() -> {
				String size = String.valueOf(lists.size());
				redisUtil.set(materialSortKey, size);
				return size;
			});

		//构建卷宗材料数据并入库
		List<MaterialInfoDO> materialList = new ArrayList<>();
		if(CollectionUtil.isNotNull(lists)) {
			List<PdfImageDO> finalImageList = imageList;
			lists.stream().map(s -> {
				JSONArray materialFile = new JSONArray();

				//构建材料文件数据
                s.stream().map( image ->{
                    int xh = image.getXh();
                    JSONObject material = new JSONObject();
                    material.put("addTime", image.getAddTime().getTime());
                    material.put("fileName", image.getImageName());
                    material.put("id", image.getId());
                    material.put("url", image.getImageUrl());
                    material.put("xh", xh);
                    material.put("pdfId", uploadPdfData.getId());
                    if (xh == 1){
                        material.put("index","start");
                    }
                    else if (xh == finalImageList.size() ){
                        material.put("index","end");
                    }
                    materialFile.add(material);
                    return image;
                }).collect(Collectors.toList());

                //构建材料
                MaterialInfoDO materialInfo = new MaterialInfoDO();
                materialInfo.setCatalogId(uploadPdfData.getCatalogId());
                materialInfo.setId(StringUtil.getGuid32());
                materialInfo.setJgrybm(uploadPdfData.getJgrybm());
                materialInfo.setName("未识别");
                materialInfo.setPageCount(materialFile.size());
                materialInfo.setType(BmTypeEnum.UN_BM.getType());
                materialInfo.setAddTime(new Date());
                materialInfo.setMaterialFile(materialFile.toJSONString());
                materialInfo.setMaterialId(materialId);
                materialInfo.setXh(integer.getAndIncrement());
                materialInfo.setIsDel(false);
                materialList.add(materialInfo);

				return s;
			}).collect(Collectors.toList());

			//保存材料
			if(CollectionUtil.isNotNull(materialList)) {
				materialInfoService.saveBatch(materialList);
			}

			//更新pdf上传数据对象
			uploadPdfData.setStatus(PdfConvertStatus.CONVERT_FINISH.getCode());
			uploadPdfData.setUpdateTime(new Date());
			uploadPdfData.setMaterialId(materialId);
			uploadDataService.updateById(uploadPdfData);
		}
	}

	/**
	 * 构建卷宗材料
	 * @param pdfBatch PdfBatchDO pdf批次对象
	 * @param bmType String 编目类型
	 * @param materialFile JSONArray 文件数据
	 * @return MaterialInfoDO
	 */
	private MaterialInfoDO buildMaterialInfo(PdfBatchDO pdfBatch, String bmType, JSONArray materialFile) {
        MaterialInfoDO materialInfo = new MaterialInfoDO();

        String id = StringUtil.getGuid32();
        materialInfo.setId(id);
        materialInfo.setJgrybm(pdfBatch.getJgrybm());
        materialInfo.setPageCount(materialFile.size());
        materialInfo.setType(bmType);
        materialInfo.setAddTime(new Date());
        materialInfo.setMaterialFile(materialFile.toJSONString());
        materialInfo.setAddUser(pdfBatch.getAddUser());
        materialInfo.setCatalogId(pdfBatch.getCatalogId());
        materialInfo.setPartCatalogId(pdfBatch.getPartCatalogId());
        materialInfo.setName(pdfBatch.getName());
        materialInfo.setXh(pdfBatch.getXh());
        materialInfo.setTemplateId(pdfBatch.getTemplateId());
        materialInfo.setMaterialId(id);

        return materialInfo;
    }

	/**
	 * 根据批量上传的图片构建材料文件Json数组
	 * @param pdfBatchImageList List<PdfBatchImage> 批量上传的图片
	 * @param materialFiles JSONArray 材料文件数组
	 * @return JSONArray
	 */
	private JSONArray buildMaterialFilesByBatchImage(List<PdfBatchImage> pdfBatchImageList, JSONArray materialFiles) {
		int startIndex = materialFiles.size() + 1;
		for(PdfBatchImage pdfBatchImage : pdfBatchImageList) {
			List<PdfImageDO> imageList = pdfBatchImage.getImageList();
			for(PdfImageDO image : imageList) {
				JSONObject material = new JSONObject();
                material.put("addTime", image.getAddTime().getTime());
                material.put("fileName", image.getImageName());
                material.put("id", image.getId());
                material.put("url", image.getImageUrl());
                material.put("xh", startIndex);
                material.put("pdfId", image.getPdfId());
                int imageXh = image.getXh();
                if (imageXh == 1){
                    material.put("index", "start");
                }
                else if (imageXh == imageList.size() ){
                    material.put("index", "end");
                }

                startIndex = startIndex + 1;
                materialFiles.add(material);
			}
		}

		return materialFiles;
	}

	/**
	 * pdf重新转换消费者(L1200)
	 * @param msg String 转换消息
	 * @param channel Channel 通道
	 * @param message Message 队列消息
	 */
	@RabbitHandler
	@RabbitListener(queues = "${conf.convert.queue.reconvert}")
	public void msgReConvertConsumer(String msg, Channel channel, Message message) throws IOException {
		log.info("接收重新转换的消息:{}", msg);

		//获取消息json格式
		JSONObject jsonObject = JSONObject.parseObject(msg);
        String uuid = jsonObject.getString("uuid");

        try {
			//转换加锁
			boolean lock = redisUtil.lock(uuid, "1", 600);
	        if(lock) {
	        	String jgrybm = jsonObject.getString("jgrybm");
	        	String pdfId = jsonObject.getString("pdfId");

	        	//异步转换
	        	convertService.asynchronousConvert(jgrybm, pdfId, false, null, null);
	        }

	        //清除缓存数据
            redisUtil.rmZSet(RedisConstants.PDF_CONVERT_KEY, uuid);
		}
		catch(Exception e) {
			log.error("pdf重新转换消息异常：{}", e.getMessage());

			//插入异常日志
			PdfConvertLogDO convertLog = new PdfConvertLogDO();
			convertLog.setId(StringUtil.getGuid32());
			convertLog.setErrorLog(e.getMessage());
			convertLog.setPushData(msg);
			convertLog.setAddTime(new Date());
			convertLogService.save(convertLog);

			//清除缓存数据
            redisUtil.rmZSet(RedisConstants.PDF_CONVERT_KEY, uuid);
		}
		finally {
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
		}
	}
}
