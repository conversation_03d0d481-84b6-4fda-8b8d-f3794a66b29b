pipeline {
  agent any
	options{
		timestamps() // 日志时间
	}
	tools{
		maven 'maven-3.6.3'
		jdk 'jdk1.8'
	}
	environment {
		SOURCE_DIR = '/home/<USER>/jenkins/jenkins_home/workspace' // jenkins工作目录
		def sshServer = ''
		def git_version = ''
		def master_host = '*************'
  }
  parameters {
        extendedChoice(description: '请选择要发布的服务', multiSelectDelimiter: ',', name: 'projectName', quoteValue: false, saveJSONParameterToFile: false, type: 'PT_CHECKBOX', value: 'rs-module-acp,rs-module-ihc,rs-module-tem,rs-module-pam,rs-module-dam,rs-adapter-bsp,rs-adapter-zhjg,rs-module-ptm', visibleItemCount: 15)
        gitParameter description: '请选择构建分支', branch: '', branchFilter: 'origin/(dev.*)', defaultValue: '', name: 'git_fetch', quickFilterEnabled: false, selectedValue: 'NONE', sortMode: 'NONE', tagFilter: '*', type: 'GitParameterDefinition', useRepository: 'https://***************/pd-rs/code/rs-master.git'
  }
  stages {
	stage('连接服务器') {
      steps {
			timeout(time:5, unit:"MINUTES"){
				script{
					sshServer = getServer("*************")
					// 初始化发布分支
					if(params.git_fetch == ''){
						git_version = "dev"
					} else {
						git_version = params.git_fetch
					}
					if(params.projectName == ''){
                        error '请选择要发布的服务'
                    }
				}
			}
      }
    }
    stage('拉取代码') {
      steps {
        timeout(time:5, unit:"MINUTES"){
          script{
            checkout([$class: 'GitSCM', branches: [[name: git_version]], extensions: [], userRemoteConfigs: [[credentialsId: 'gitLab', url: 'https://***************/pd-rs/code/rs-master.git']]])
          }
        }
      }
    }
	stage('打包构建') {
      steps {
	      	script{
	      	    sh "mvn clean package -Dmaven.test.skip=true"
      		}
      }
    }
	stage('部署镜像') {
      steps {
		script{
		    def strName = "${projectName}"
            def list = strName.split(",")
            for (pName in list) {
                sshCommand remote: sshServer, command: "sh -x ${SOURCE_DIR}/${JOB_NAME}/${pName}/${pName}-biz/deploy/deploy.sh ${SOURCE_DIR}/${JOB_NAME}/build"
                def name = "${pName}"
                if(name.contains('rs-module-acp')){
                   sshCommand remote: sshServer, command: "docker restart rs-acp"
                }
                if(name.contains('rs-module-ihc')){
                   sshCommand remote: sshServer, command: "docker restart rs-ihc"
                }
                if(name.contains('rs-module-tem')){
                   sshCommand remote: sshServer, command: "docker restart rs-tem"
                }
                if(name.contains('rs-module-pam')){
                   sshCommand remote: sshServer, command: "docker restart rs-pam"
                }
                if(name.contains('rs-module-dam')){
                   sshCommand remote: sshServer, command: "docker restart rs-dam"
                }
                if(name.contains('rs-adapter-bsp')){
                   sshCommand remote: sshServer, command: "docker restart rs-bsp"
                }
                if(name.contains('rs-module-ptm')){
                   sshCommand remote: sshServer, command: "docker restart rs-ptm"
                }
                if(name.contains('rs-adapter-zhjg')){
                   sshCommand remote: sshServer, command: "docker restart rs-zhjg"
                }
            }
		}
      }
    }
  }

	post{
		success{
			script{
				currentBuild.description = "\n 构建成功"
			}
		}
		failure{
			script{
				currentBuild.description = "\n 构建失败"
			}
		}
		aborted{
			script{
				currentBuild.description = "\n 构建取消"
			}
		}
	}
}

// 声明一个获取远程服务的方法
def getServer(ip){
	def remote = [:]
	remote.name = "${ip}"
	remote.host = ip
	remote.port = 22
	remote.allowAnyHosts = true
	withCredentials([usernamePassword(credentialsId: '*************', passwordVariable: 'password', usernameVariable: 'user_name')]) {
		remote.user = "${user_name}"
		remote.password = "${password}"
	}
	return remote
}
