package com.rs.module.demo;

import java.net.UnknownHostException;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.mzt.bsp.logapi.starter.annotation.EnableLogRecord;
import com.rs.framework.common.util.spring.SpringUtils;

@SpringBootApplication(exclude = {DruidDataSourceAutoConfigure.class, MongoAutoConfiguration.class, MongoDataAutoConfiguration.class})
@ComponentScan(value = {"com.rs.*, com.bsp.*"})
@EnableLogRecord(systemMark = "demo")
public class DemoServerApplication {

	public static void main(String[] args) throws UnknownHostException {
		ConfigurableApplicationContext application = SpringApplication.run(DemoServerApplication.class, args);
		SpringUtils.printStartLog(application);
	}
}
