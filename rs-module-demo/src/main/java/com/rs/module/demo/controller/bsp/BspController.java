package com.rs.module.demo.controller.bsp;

import cn.hutool.core.collection.ListUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.sdk.cons.MsgType;
import com.bsp.sdk.log.LogClient;
import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageParam;
import org.apache.skywalking.apm.toolkit.trace.ActiveSpan;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * bsp控制器demo
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/rs/demo/bsp")
public class BspController {

	/**
	 * 获取会话用户
	 * @return CommonResult<SessionUser>
	 */
	@GetMapping("getSessionUser")
	public CommonResult<SessionUser> getSessionUser(){
		ActiveSpan.info("用户:1111");
		SessionUser user = SessionUserUtil.getSessionUser();
		return CommonResult.success(user);
	}

	/**
	 * 使用注解推送日志
	 * @return CommonResult<String>
	 */
	@GetMapping("sendLogByAnnotation")
	@LogRecordAnnotation(bizModule = "bsp:com:fmdialog", operateType = LogOperateType.UPSERT, title = "保存自定义对话框",
            bizNo = "{{#page.pageSize}}", success = "保存自定义对话框成功", fail = "错误信息：{{#_ret[msg]}}",
            extraInfo = "{TO_JSON{#page}}")
	public CommonResult<String> sendLogByAnnotation(PageParam page){
		return CommonResult.success("使用注解推送日志成功");
	}

	/**
	 * 推送登录日志
	 * @return CommonResult<String>
	 */
	@GetMapping("sendLoginLog")
	public CommonResult<String> sendLoginLog(){
		SessionUser user = SessionUserUtil.getSessionUser();
		LogClient.build().loginLogBuilder()
			.setContent("操作信息内容")
			.setId(StringUtil.getGuid32())
			.setIdCard(user.getIdCard())
			.setLoginId("登录帐号")
			.setOperateDuration(300l)      	//操作耗时
			.setOperateResult(0) 			//操作结果
			.setOperateType("操作类型")
			.setOperateUrl("操作地址")
			.setOrgCode(user.getOrgCode())
			.setOrgName(user.getOrgName())
			.setSystemMark("ihc")
			.setTerminalId("终端信息")
			.setTerminalType(0)
			.setUserAgent("用户浏览器代理信息")
			.setUserId(user.getId())
			.setUserName(user.getName())
			.send();
		return CommonResult.success("登录日志推送成功");
	}

	/**
	 * 推送操作日志
	 * @return CommonResult<String>
	 */
	@GetMapping("sendOperLog")
	public CommonResult<String> sendOperLog(){
		SessionUser user = SessionUserUtil.getSessionUser();
		LogClient.build().operLogBuilder()
			.setBizNo("业务编号")
			.setContent("操作信息内容")
			.setId(StringUtil.getGuid32())
			.setIdCard(user.getIdCard())
			.setKeyValue("主键值")
			.setLoginId("登录帐号")
			.setOperateCondition("操作条件")
			.setOperateDuration(300l)      	//操作耗时
			.setOperateResult(0) 			//操作结果
			.setOperateType("操作类型")
			.setOperateUrl("操作地址")
			.setOrgCode(user.getOrgCode())
			.setOrgName(user.getOrgName())
			.setReqParams("请求参数")
			.setStartTime(System.currentTimeMillis())  //操作开始时间
			.setSystemMark("ihc")
			.setTableName("业务表名称")
			.setTerminalId("终端信息")
			.setTerminalType(0)
			.setTitle("标题")
			.setUserAgent("用户浏览器代理信息")
			.setUserId(user.getId())
			.setUserName(user.getName())
			.setXxbrXm("现协办人姓名")
			.setXzbrXm("现主办人姓名")
			.setYwlx("业务类型")
			.send();
		return CommonResult.success("操作日志推送成功");
	}

	/**
	 * 推送异常日志
	 * @return CommonResult<String>
	 */
	@GetMapping("sendErrorLog")
	public CommonResult<String> sendErrorLog(){
		SessionUser user = SessionUserUtil.getSessionUser();
		LogClient.build().errorLogBuilder()
			.setBizNo("业务编号")
			.setContent("异常信息内容")
			.setErrorReason("异常原因")
			.setId(StringUtil.getGuid32())
			.setIdCard(user.getIdCard())
			.setKeyValue("主键值")
			.setLoginId("登录帐号")
			.setOperateCondition("操作条件")
			.setOperateDuration(300l)      	//操作耗时
			.setOperateResult(0) 			//操作结果
			.setOperateType("操作类型")
			.setOperateUrl("操作地址")
			.setOrgCode(user.getOrgCode())
			.setOrgName(user.getOrgName())
			.setReqParams("请求参数")
			.setStartTime(System.currentTimeMillis())  //操作开始时间
			.setSystemMark("ihc")
			.setTableName("业务表名称")
			.setTerminalId("终端信息")
			.setTerminalType(0)
			.setTitle("标题")
			.setUserAgent("用户浏览器代理信息")
			.setUserId(user.getId())
			.setUserName(user.getName())
			.setXxbrXm("现协办人姓名")
			.setXzbrXm("现主办人姓名")
			.setYwlx("业务类型")
			.send();
		return CommonResult.success("异常日志推送成功");
	}

	/**
	 * 推送待办消息
	 * @return CommonResult<String>
	 */
	@GetMapping("sendTodoMsg")
	public CommonResult<String> sendTodoMsg(){
		String title = "待办消息" + System.currentTimeMillis();	//消息标题
		String content = "待办消息内容";							//消息内容
		String url = "待办消息地址";								//消息处理页面地址
		String fApp = "大数据平台";								//来源应用
		String fUser = "300120001231001X";						//来源用户身份证号								//
		String fUserName = "王五";								//来源用户姓名
		String fOrgCode = "110110110110";						//来源机构代码
		String fOrgName = "测试机构";								//来源机构名称
		String fXxpt = "pc";									//来源消息平台(pc/app)
		String ywbh = "A39003425001900";						//业务编号
		List<ReceiveUser> receiveUserList = ListUtil.of(		//消息接收用户
				new ReceiveUser("100120001231001X", "110110110110"),
				new ReceiveUser("200120001231001X", "210110110110"));
		SendMessageUtil.sendTodoMsg(title, content, url, fApp, fUser, fUserName, fOrgCode, fOrgName,
				fXxpt, ywbh, receiveUserList);
		return CommonResult.success("待办消息推送成功");
	}

	/**
	 * 推送工作提醒
	 * @return CommonResult<String>
	 */
	@GetMapping("sendAlertMsg")
	public CommonResult<String> sendAlertMsg(){
		String title = "工作提醒" + System.currentTimeMillis();	//消息标题
		String content = "工作提醒内容";							//消息内容
		String url = "工作提醒地址";								//消息处理页面地址
		String fApp = "大数据平台";								//来源应用
		String fUser = "300120001231001X";						//来源用户身份证号								//
		String fUserName = "王五";								//来源用户姓名
		String fOrgCode = "110110110110";						//来源机构代码
		String fOrgName = "测试机构";								//来源机构名称
		String actInstId = "780096018080";						//流程实例Id
		String pcid = "" + System.currentTimeMillis();			//批次Id
		String fXxpt = "pc";									//来源消息平台(pc/app)
		String ywbh = "A39003425001900";						//业务编号
		String systemMark = "ihc";								//应用标识
		String busType = "001";									//业务类型
		String extendData = "8987080ABE8EFF";					//扩展数据
		List<ReceiveUser> receiveUserList = ListUtil.of(		//消息接收用户
				new ReceiveUser("100120001231001X", "110110110110"),
				new ReceiveUser("200120001231001X", "210110110110"));
		SendMessageUtil.sendAlertMsg(title, content, url, fApp, fUser, fUserName, fOrgCode, fOrgName,
				actInstId, pcid, fXxpt, ywbh, receiveUserList, systemMark, busType, extendData);
		return CommonResult.success("工作提醒推送成功");
	}

	/**
	 * 推送预警消息
	 * @return CommonResult<String>
	 */
	@GetMapping("sendAlarmMsg")
	public CommonResult<String> sendAlarmMsg(){
		String title = "预警消息" + System.currentTimeMillis();	//消息标题
		String content = "预警消息内容";							//消息内容
		String url = "预警消息地址";								//消息处理页面地址
		String fOrgCode = "110110110110";						//来源机构代码
		String fOrgName = "测试机构";								//来源机构名称
		String pcid = "" + System.currentTimeMillis();			//批次Id
		String ywbh = "A39003425001900";						//业务编号
		String ajmc = "某某某诈骗案";								//案件名称
		String yjlxdm = "134000";								//预警类型代码
		String yjlxmc = "诈骗类";								//预警类型名称
		String jdlx = "01";										//监督类型【一级预警、二级预警、三级预警、四级预警】
		String cbrSfzh = "100120001231001X";					//承办人身份证号
		String cbrXm = "张三";									//承办人姓名
		String ryxm = "李四";									//人员姓名
		String systemMark = "ihc";								//应用标识
		List<ReceiveUser> receiveUserList = ListUtil.of(		//消息接收用户
				new ReceiveUser("100120001231001X", "110110110110"),
				new ReceiveUser("200120001231001X", "210110110110"));
		SendMessageUtil.sendAlarmMsg(title, content, url, fOrgCode, fOrgName, pcid, ywbh,
				receiveUserList, ajmc, yjlxdm, yjlxmc, jdlx, cbrSfzh, cbrXm, ryxm, systemMark);
		return CommonResult.success("预警消息推送成功");
	}

	/**
	 * 推送其它消息
	 * @return CommonResult<String>
	 */
	@GetMapping("sendOtherMsg")
	public CommonResult<String> sendOtherMsg(){
		String title = "其它消息" + System.currentTimeMillis();	//消息标题
		String content = "其它消息内容";							//消息内容
		String url = "其它消息地址";								//消息处理页面地址
		String fApp = "大数据平台";								//来源应用
		String fUser = "300120001231001X";						//来源用户身份证号								//
		String fUserName = "王五";								//来源用户姓名
		String fOrgCode = "110110110110";						//来源机构代码
		String fOrgName = "测试机构";								//来源机构名称
		String actInstId = "780096018080";						//流程实例Id
		String pcid = "" + System.currentTimeMillis();			//批次Id
		String fXxpt = "pc";									//来源消息平台(pc/app)
		String ywbh = "A39003425001900";						//业务编号
		String systemMark = "ihc";								//应用标识
		String busType = "001";									//业务类型
		String extendData = "8987080ABE8EFF";					//扩展数据
		List<ReceiveUser> receiveUserList = ListUtil.of(		//消息接收用户
				new ReceiveUser("100120001231001X", "110110110110"),
				new ReceiveUser("200120001231001X", "210110110110"));
		SendMessageUtil.sendMsg(MsgType.OTHER.getCode(), title, content, url, fApp, fUser,
				fUserName, fOrgCode, fOrgName, actInstId, pcid, fXxpt, ywbh, receiveUserList,
                systemMark, busType, extendData);
		return CommonResult.success("其它消息推送成功");
	}

	/**
	 * 获取会话用户
	 * @return CommonResult<SessionUser>
	 */
	@PostMapping("/testDate")
	public Date testDate(@RequestBody DateTestVO user){

		return user.getDate();
	}
}
