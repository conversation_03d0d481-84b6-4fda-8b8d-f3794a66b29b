server:
  port: ${conf.server.port.demo}
  max-http-header-size: 102400

# 日志
logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径

# spring    
spring:
  application:
    name: demo-server
  profiles:
    active: conf,dev,sdk,as
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Feign 等会存在重复定义的服务
  
  # Spring Cloud Nacos 配置
  cloud:
    nacos:
      discovery: # 【配置中心】配置项
        enabled: ${conf.nacos.enabled}
        server-addr: ${conf.nacos.ip}:${conf.nacos.port} # Nacos 服务器地址
        username: ${conf.nacos.username}
        password: ${conf.nacos.password}
        namespace: ${conf.nacos.namespace}
        group: ${conf.nacos.group}
        metadata:
          version: 1.0.0 # 服务实例的版本号，可用于灰度发布
  
  # Spring Data Redis 配置
  data:
    redis:
      repositories:
        enabled: false # 项目未使用到 Spring Data Redis 的 Repository，所以直接禁用，保证启动速度
        
  # Servlet 配置
  servlet:
    # 文件上传相关配置项
    multipart:
      max-file-size: 16MB # 单个文件大小
      max-request-size: 32MB # 设置总上传的文件大小

  # Jackson 配置项
  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 LocalDateTime 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 1611460870.401，而是直接 1611460870401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean

  # 放行的路由
  matchers:
    ignores: ${conf.matchers.ignores}

# 接口文档配置
springdoc:
  api-docs:
    enabled: true # 1. 是否开启 Swagger 接文档的元数据
    path: /v3/api-docs
  swagger-ui:
    enabled: true # 2.1 是否开启 Swagger 文档的官方 UI 界面
    path: /swagger-ui
  default-flat-param-object: true # 参见 https://doc.xiaominfo.com/docs/faq/v4/knife4j-parameterobject-flat-param 文档

# MyBatis Plus 的配置项
mybatis-plus:
  mapper-locations:
  - classpath*:mapper/**/*.xml
  configuration:
    map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
  global-config:
    db-config:
      id-type: NONE # “智能”模式，基于 IdTypeEnvironmentPostProcessor + 数据源的类型，自动适配成 AUTO、INPUT 模式。
      #      id-type: AUTO # 自增 ID，适合 MySQL 等直接自增的数据库
      #      id-type: INPUT # 用户输入 ID，适合 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库
      #      id-type: ASSIGN_ID # 分配 ID，默认使用雪花算法。注意，Oracle、PostgreSQL、Kingbase、DB2、H2 数据库时，需要去除实体类上的 @KeySequence 注解
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
    banner: false # 关闭控制台的 Banner 打印
  type-aliases-package: ${rs.info.base-package}.dao
  encryptor:
    password: XDV71a+xqStEA3WH # 加解密的秘钥，可使用 https://www.imaegoo.com/2020/aes-key-generator/ 网站生成


##########################   bsp相关配置  ##########################
bsp:
  snowflake:
    worker-id: ${conf.snowflake.worker-id}
    datacenter-id: ${conf.snowflake.datacenter-id}
system-mark: ${conf.system-mark}


########################## 演示系统相关配置 ##########################
rs:
  info:
    version: 1.0.0
    base-package: com.rs.module.demo
  web:
    admin-ui:
      url: http://dashboard.rs # Admin 管理后台
  swagger:
    title: 演示系统
    description: 提供演示系统的所有功能
    version: ${rs.info.version}
    base-package: com.rs.module.demo
debug: ${conf.debug}
