package com.rs.module.integ.service;

import com.rs.module.integ.constant.LegalPlatformDataShareTypeConstants;
import com.rs.module.integ.constant.LegalPlatformDataStateConstant;
import com.rs.module.integ.constant.LegalPlatformFieldAndSortConstant;
import com.rs.module.integ.constant.OrderConstant;
import com.rs.module.integ.dao.AcpPmPrisonerJdsInRepository;
import com.rs.module.integ.domain.entity.AcpPmPrisonerJdsIn;
import com.rs.module.integ.domain.request.DataShareRequest;
import com.rs.module.integ.domain.response.LegalPlatformBaseResponse;
import com.rs.module.integ.domain.response.LegalPlatformJlsDataShareResponse;
import com.rs.module.integ.domain.response.PrisonerJlsResponse;
import com.rs.module.integ.util.DateUtil;
import com.rs.module.integ.util.NameKeywordGenerator;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import lombok.extern.slf4j.XSlf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Collections;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AcpPmPrisonerJdsInService {

    private final AcpPmPrisonerJdsInRepository repository;

    @Autowired
    private HttpClientImpl httpClient;

    /**
     * 子类名称
     */
    private final String classSimpleName = this.getClass().getSimpleName();
    /**
     * 增量同步时的最后一次的创建时间
     */
    @Getter
    private volatile LocalDateTime lastCreateTime;
    /**
     * 增量同步时的最后一次的更新时间
     */
    @Getter
    private volatile LocalDateTime lastUpdateTime;
    /**
     * 分页查询数量
     */
    private long pageSize = 1000;

    @Autowired
    public AcpPmPrisonerJdsInService(AcpPmPrisonerJdsInRepository repository) {
        this.repository = repository;
    }


    /**
     * 拘留所在所人员数据同步流程
     * @param
     * @return
     */
    public void incrementSync() {
        // 查询数据库最大创建时间
        LocalDateTime startCreateTime = this.lastCreateTime;
        if (Objects.isNull(startCreateTime)) {
            startCreateTime = getTableLastCreateTime();
            // 因为同步第三方数据会有出现创建时间一样并且超过1000条甚至更多的情况，
            // 且接口按时间过滤是没有等于，这样会导致创建时间相同但是还未同步的被过滤掉，所以这里减一秒冗余下时间
            startCreateTime = startCreateTime == null ? null : startCreateTime.minusSeconds(1);
            log.info("{} --从数据库查询最大的创建时间：startAddTime：{}", this.classSimpleName, startCreateTime);
        }
        LocalDateTime startUpdateTime = this.lastUpdateTime;
        if (Objects.isNull(startUpdateTime)) {
            startUpdateTime = getTableLastUpdateTime();
            // 同创建时间一样的问题
            startUpdateTime = startUpdateTime == null ? null : startUpdateTime.minusSeconds(1);
            log.info("{} --从数据库查询最大的更新时间：startUpdateTime：{}", this.classSimpleName, startUpdateTime);
        }
        // 新增数据同步
//        this.doPageIncrementSyncCreateData(startCreateTime);
        // 更新数据同步
//        this.doPageIncrementSyncUpdateData(startUpdateTime);

    }


    /**
     * 查询数据库中最大的创建时间
     * @return
     */
    private LocalDateTime getTableLastCreateTime() {
        //从数据库查询该表所有数据的最大add_time
        Date maxAddTime = repository.findMaxAddTime();
        // 如果 maxAddTime 为 null，返回默认值 2010年1月1日0点0分0秒
        if (maxAddTime == null) {
            return LocalDateTime.of(2010, 1, 1, 0, 0, 0);
        }
        // 将 Date 转换为 LocalDateTime
        return maxAddTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

    }

    /**
     * 查询数据库中最大的更新时间
     * @return
     */
    private LocalDateTime getTableLastUpdateTime() {
        //从数据库查询该表所有数据的最大update_time
        Date maxUpdateTime = repository.findMaxUpdateTime();
        // 如果 maxAddTime 为 null，返回默认值 2010年1月1日0点0分0秒
        if (maxUpdateTime == null) {
            return LocalDateTime.of(2010, 1, 1, 0, 0, 0);
        }
        // 将 Date 转换为 LocalDateTime
        return maxUpdateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }


    /**
     * 实行按创建时间查询分批次处理同步的执行方法
     *
     * @param startCreateTime 查询的开始时间
     */
//    private void doPageIncrementSyncCreateData(final LocalDateTime startCreateTime) {
//        long page = 1;
//        long pageSize = this.pageSize;
//        LocalDateTime lastMaxCreateTimeTemp = startCreateTime;
//        while (true) {
//            try {
//                log.info("{} --第{}页数据新增开始同步，查询开始时间：startCreateTime：{}",
//                        this.classSimpleName, page, startCreateTime);
//                List<T> result = this.getCreateDataListPage(startCreateTime, page, pageSize);
//                if (CollectionUtils.isEmpty(result)) {
//                    log.warn("{} --新增数据查询为空，查询开始时间：startCreateTime：{}，当前系统时间：{}",
//                            this.classSimpleName, startCreateTime, LocalDateTime.now());
//                    break;
//                }
//                int size = result.size();
//                lastMaxCreateTimeTemp = DateUtil.max(this.handleDataAndReturnMaxCreateTime(result), lastMaxCreateTimeTemp);
//                log.info("{} --第{}页数据新增同步，该批次比较的最大的创建时间为：{}",
//                        this.classSimpleName, page, lastMaxCreateTimeTemp);
//                if (size < pageSize) {
//                    break;
//                }
//                page++;
//            } catch (Exception e) {
//                log.error("{} --第{}页数据新增同步异常，重置最后创建时间为空", this.classSimpleName, page, e);
//                this.lastCreateTime = null;
//                throw new RuntimeException(e);
//            }
//        }
//        this.lastCreateTime = lastMaxCreateTimeTemp;
//        log.info("{} --数据更新完成，最后比较的最大创建时间为：{}", this.classSimpleName, lastMaxCreateTimeTemp);
//    }


    private List<AcpPmPrisonerJdsIn> getCreateDataListPage(LocalDateTime startCreateTime, long page, long pageSize) {
        LegalPlatformBaseResponse<LegalPlatformJlsDataShareResponse> legalPlatformBaseResponse =
                httpClient.getJlsData(DataShareRequest.builder()
                        .createStartTime(DateUtil.toDate(startCreateTime))
                        .types(LegalPlatformDataShareTypeConstants.jbxx)
                        .state(LegalPlatformDataStateConstant.PRISONER_IN)
                        .pageNum((int) page)
                        .pageSize((int) pageSize)
                        .orderField(LegalPlatformFieldAndSortConstant.CREATE_TIME)
                        .seType(OrderConstant.ASC)
                        .build());
        List<PrisonerJlsResponse> prisonerJlsResponses = legalPlatformBaseResponse.getData().getJbxx();
        return this.convertBatchToAcpPmPrisonerJdsIn(prisonerJlsResponses);
    }

    private List<AcpPmPrisonerJdsIn> convertBatchToAcpPmPrisonerJdsIn(List<PrisonerJlsResponse> prisonerJlsResponses) {
        return Optional.ofNullable(prisonerJlsResponses)
                .orElse(Collections.emptyList())
                .stream()
                .map(this::convertToAcpPmPrisonerJdsIn)
                .collect(Collectors.toList());
    }

    private AcpPmPrisonerJdsIn convertToAcpPmPrisonerJdsIn(PrisonerJlsResponse prisonerJlsResponse) {
        return AcpPmPrisonerJdsIn.builder()
                .id(prisonerJlsResponse.getId())
                .rybh(prisonerJlsResponse.getRybh())
                .jsh(prisonerJlsResponse.getJsh())
                .xm(prisonerJlsResponse.getXm())
                .xmpy(NameKeywordGenerator.generateKeywords(prisonerJlsResponse.getXm()))
                .build();
    }


    // 增加
    public AcpPmPrisonerJdsIn save(AcpPmPrisonerJdsIn prisoner) {
        return repository.save(prisoner);
    }

    // 查询所有
    public List<AcpPmPrisonerJdsIn> findAll() {
        return repository.findAll();
    }

    // 根据 ID 查询
    public Optional<AcpPmPrisonerJdsIn> findById(String id) {
        return repository.findById(id);
    }

    // 更新
    public AcpPmPrisonerJdsIn update(AcpPmPrisonerJdsIn prisoner) {
        return repository.save(prisoner); // save 方法会根据 ID 判断是更新还是插入
    }

    // 删除
    public void deleteById(String id) {
        repository.deleteById(id);
    }

    // 根据人员编号查询
    public List<AcpPmPrisonerJdsIn> findByRybh(String rybh) {
        return repository.findByRybh(rybh);
    }
}
