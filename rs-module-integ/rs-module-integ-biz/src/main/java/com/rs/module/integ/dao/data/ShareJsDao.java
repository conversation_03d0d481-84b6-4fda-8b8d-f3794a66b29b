package com.rs.module.integ.dao.data;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.integ.entity.data.ShareJsDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.integ.controller.admin.data.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 数据共享-监室 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ShareJsDao extends IBaseDao<ShareJsDO> {


    default PageResult<ShareJsDO> selectPage(ShareJsPageReqVO reqVO) {
        Page<ShareJsDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ShareJsDO> wrapper = new LambdaQueryWrapperX<ShareJsDO>()
            .eqIfPresent(ShareJsDO::getSysId, reqVO.getSysId())
            .eqIfPresent(ShareJsDO::getJsh, reqVO.getJsh())
            .eqIfPresent(ShareJsDO::getJslb, reqVO.getJslb())
            .eqIfPresent(ShareJsDO::getJsmc, reqVO.getJsmc())
            .eqIfPresent(ShareJsDO::getJqh, reqVO.getJqh())
            .eqIfPresent(ShareJsDO::getState, reqVO.getState())
            .eqIfPresent(ShareJsDO::getXgmjjh, reqVO.getXgmjjh())
            .eqIfPresent(ShareJsDO::getZgmjjh, reqVO.getZgmjjh())
            .eqIfPresent(ShareJsDO::getJsbh, reqVO.getJsbh())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(ShareJsDO::getAddTime);
        }
        Page<ShareJsDO> shareJsPage = selectPage(page, wrapper);
        return new PageResult<>(shareJsPage.getRecords(), shareJsPage.getTotal());
    }
    default List<ShareJsDO> selectList(ShareJsListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ShareJsDO>()
            .eqIfPresent(ShareJsDO::getSysId, reqVO.getSysId())
            .eqIfPresent(ShareJsDO::getJsh, reqVO.getJsh())
            .eqIfPresent(ShareJsDO::getJslb, reqVO.getJslb())
            .eqIfPresent(ShareJsDO::getJsmc, reqVO.getJsmc())
            .eqIfPresent(ShareJsDO::getJqh, reqVO.getJqh())
            .eqIfPresent(ShareJsDO::getState, reqVO.getState())
            .eqIfPresent(ShareJsDO::getXgmjjh, reqVO.getXgmjjh())
            .eqIfPresent(ShareJsDO::getZgmjjh, reqVO.getZgmjjh())
            .eqIfPresent(ShareJsDO::getJsbh, reqVO.getJsbh())
        .orderByDesc(ShareJsDO::getAddTime));    }


    }
