package com.rs.module.integ.domain.request;

import com.rs.module.integ.constant.LegalPlatformDataShareTypeConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataShareRequest {

    /**
     * 监所编号
     */
    private String jsbh;

    /**
     * 证件号码
     * <p>
     * ##########################
     * hm,zhiwxx,rxxx三类数据使用该属性
     * ##########################
     */
    private String zjhm;

    /**
     * 状态
     * <p>
     * 人员  R8-在押  R7-历史
     * 业务  R2-有效  R3-无效
     */
    private String state;

    /**
     * 查询数据类型
     * <p>
     * (逗号隔开的表名，不传查询所有表)，例如：JBXX,JQ,JS
     *
     * @see LegalPlatformDataShareTypeConstants
     */
    private String types;

    /**
     * 创建开始时间
     */
    private Date createStartTime;

    /**
     * 创建结束时间
     */
    private Date createEndTime;

    /**
     * 修改开始时间
     */
    private Date updateStartTime;

    /**
     * 修改结束时间
     */
    private Date updateEndTime;

    /**
     * 当前页
     */
    @NotNull
    private Integer pageNum;

    /**
     * 每页条数
     */
    @NotNull
    private Integer pageSize;

    /**
     * 进行排序的字段
     */
    private String orderField;

    /**
     * 排序类型
     */
    private String seType;

    /**
     * 是否分页
     * <p>
     * 默认分页
     */
    private Boolean doPage;

}
