package com.rs.module.integ.domain.vo;

import com.rs.module.integ.domain.po.PdmsPrisoner;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetPrisonerInfoVO extends PdmsPrisoner {

    // -----------------------------------------------------------------------------------------------------------------
    /**
     * 照片URL
     */
    @ApiModelProperty("照片URL")
    private String photourl;

    // -----------------------------------------------------------------------------------------------------------------

    /**
     * 监室名称
     */
    @ApiModelProperty("监室名称")
    private String jsmc;

    /**
     * 监所id
     */
    @ApiModelProperty("监所id")
    private String prisonId;

    /**
     * 监所名称
     */
    @ApiModelProperty("监所名称")
    private String prisonName;

    /**
     * 涉嫌罪名
     */
    @ApiModelProperty("涉嫌罪名")
    private String sxzm;

}
