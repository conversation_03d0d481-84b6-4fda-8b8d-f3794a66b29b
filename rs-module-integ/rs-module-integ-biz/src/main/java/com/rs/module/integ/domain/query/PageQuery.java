package com.rs.module.integ.domain.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create_datetime 2019/10/25 8:54
 */
@Getter
@Setter
public class PageQuery {

    @NotNull(message = "curPage不能为空")
    @Min(value = 1L, message = "curPage最小为1")
    @ApiModelProperty(value = "PAGEQUERY_CURPAGE", required = true)
    private Integer curPage;

    @NotNull(message = "pageSize不能为空")
    @Min(value = 1L, message = "pageSize最小为1")
    @ApiModelProperty(value = "PAGEQUERY_PAGESIZE", required = true)
    private Integer pageSize;

    @ApiModelProperty(value = "排序字段")
    private String sortBy;

    @ApiModelProperty(value = "排序方式(默认倒叙) asc=顺序 desc=倒序")
    private String order;
}
