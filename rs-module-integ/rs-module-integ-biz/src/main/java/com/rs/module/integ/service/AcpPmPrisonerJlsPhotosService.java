package com.rs.module.integ.service;

import com.rs.module.integ.constant.*;
import com.rs.module.integ.dao.AcpPmPrisonerPhotosRepository;
import com.rs.module.integ.domain.entity.AcpPmPrisonerPhotos;
import com.rs.module.integ.domain.po.PdmsPrisonerPhotos;
import com.rs.module.integ.domain.request.DataShareRequest;
import com.rs.module.integ.domain.response.LegalPlatformBaseResponse;
import com.rs.module.integ.domain.response.LegalPlatformJlsDataShareResponse;
import com.rs.module.integ.service.sync.AbstractSyncLegalPlatform;
import com.rs.module.integ.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AcpPmPrisonerJlsPhotosService extends AbstractSyncLegalPlatform<AcpPmPrisonerPhotos> {

    private final AcpPmPrisonerPhotosRepository repository;

    @Autowired
    private AcpPmPrisonerJlsInService acpPmPrisonerJlsInService;

    @Autowired
    private HttpClientImpl httpClient;

    /**
     * 查询数据库中最大的创建时间
     *
     * @return
     */
    protected LocalDateTime getTableLastCreateTime() {
        //从数据库查询该表所有数据的最大add_time
        Date maxAddTime = repository.findMaxAddTime(PrisonDataSourceConstant.JLS);
        // 如果 maxAddTime 为 null，返回默认值 2010年1月1日0点0分0秒
        if (maxAddTime == null) {
            return LocalDateTime.of(2010, 1, 1, 0, 0, 0);
        }
        // 将 Date 转换为 LocalDateTime
        return maxAddTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

    }

    @Override
    protected boolean hasData() {
        return repository.count() > 0 ? true : false;
    }

    /**
     * 查询数据库中最大的更新时间
     *
     * @return
     */
    protected LocalDateTime getTableLastUpdateTime() {
        //从数据库查询该表所有数据的最大update_time
        Date maxUpdateTime = repository.findMaxUpdateTime(PrisonDataSourceConstant.JLS);
        // 如果 maxAddTime 为 null，返回默认值 2010年1月1日0点0分0秒
        if (maxUpdateTime == null) {
            return LocalDateTime.of(2010, 1, 1, 0, 0, 0);
        }
        // 将 Date 转换为 LocalDateTime
        return maxUpdateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }


    protected LocalDateTime handleDataAndReturnMaxCreateTime(List<AcpPmPrisonerPhotos> result) {
        LocalDateTime maxCreateTime = result.stream()
                .map(AcpPmPrisonerPhotos::getAddTime) // Assuming getAddTime returns Date
                .filter(Objects::nonNull)
                .map(date -> date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()) // Convert Date to LocalDateTime
                .max(LocalDateTime::compareTo) // Now comparing LocalDateTime
                .orElse(null);
        //将result保存到数据库中
        result.forEach(this::save);

        //更新人员表照片信息
        acpPmPrisonerJlsInService.updatePhoto(result);

        return maxCreateTime;
    }

    protected LocalDateTime handleDataAndReturnMaxUpdateTime(List<AcpPmPrisonerPhotos> result) {
        LocalDateTime maxUpdateTime = result.stream()
                .map(AcpPmPrisonerPhotos::getUpdateTime) // Assuming getAddTime returns Date
                .filter(Objects::nonNull)
                .map(date -> date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()) // Convert Date to LocalDateTime
                .max(LocalDateTime::compareTo) // Now comparing LocalDateTime
                .orElse(null);

        List<String> deleteIds = new ArrayList<>();
        List<AcpPmPrisonerPhotos> updatePdmsPrisonerPhotos = new ArrayList<>();
        for (AcpPmPrisonerPhotos pdmsPrisonerPhotos : result) {
            if (Objects.equals(pdmsPrisonerPhotos.getState(), LegalPlatformDataStateConstant.BUSINESS_INVALID)) {
                deleteIds.add(pdmsPrisonerPhotos.getId());
            } else {
                updatePdmsPrisonerPhotos.add(pdmsPrisonerPhotos);
            }
        }
        if (!CollectionUtils.isEmpty(deleteIds)) {
            this.removeBatchByIds(deleteIds);
        }

        //将result更新到数据库中
        updatePdmsPrisonerPhotos.forEach(this::update);

        //更新人员表照片信息
        acpPmPrisonerJlsInService.updatePhoto(updatePdmsPrisonerPhotos);

        return maxUpdateTime;
    }

    protected List<AcpPmPrisonerPhotos> getCreateDataListPage(LocalDateTime startCreateTime, long page, long pageSize) {
        LegalPlatformBaseResponse<LegalPlatformJlsDataShareResponse> legalPlatformBaseResponse =
                httpClient.getJlsData(DataShareRequest.builder()
                        .createStartTime(DateUtil.toDate(startCreateTime))
                        .types(LegalPlatformDataShareTypeConstants.photos)
                        .state(LegalPlatformDataStateConstant.BUSINESS_VALID)
                        .pageNum((int) page)
                        .pageSize((int) pageSize)
                        .orderField(LegalPlatformFieldAndSortConstant.CREATE_TIME)
                        .seType(OrderConstant.ASC)
                        .build());
        List<PdmsPrisonerPhotos> prisonerPhotos = legalPlatformBaseResponse.getData().getPhotos();
        return this.convertBatchToAcpPmPrisonerPhotos(prisonerPhotos);
    }


    protected List<AcpPmPrisonerPhotos> getUpdateDataListPage(LocalDateTime startUpdateTime, long page, long pageSize) {
        LegalPlatformBaseResponse<LegalPlatformJlsDataShareResponse> legalPlatformBaseResponse =
                httpClient.getJlsData(DataShareRequest.builder()
                        .updateStartTime(DateUtil.toDate(startUpdateTime))
                        .types(LegalPlatformDataShareTypeConstants.photos)
                        .pageNum((int) page)
                        .pageSize((int) pageSize)
                        .orderField(LegalPlatformFieldAndSortConstant.UPDATE_TIME)
                        .seType(OrderConstant.ASC)
                        .build());
        List<PdmsPrisonerPhotos> prisonerPhotos = legalPlatformBaseResponse.getData().getPhotos();
        return this.convertBatchToAcpPmPrisonerPhotos(prisonerPhotos);
    }

    private List<AcpPmPrisonerPhotos> convertBatchToAcpPmPrisonerPhotos(List<PdmsPrisonerPhotos> prisonerPhotos) {
        return Optional.ofNullable(prisonerPhotos)
                .orElse(Collections.emptyList())
                .stream()
                .map(this::convertToAcpPmPrisonerPhotos)
                .collect(Collectors.toList());
    }


    private AcpPmPrisonerPhotos convertToAcpPmPrisonerPhotos(PdmsPrisonerPhotos prisonerPhotos) {
        return AcpPmPrisonerPhotos.builder()
                .id(prisonerPhotos.getId()) // 主键
                .isDel(0) // 是否删除(0:否,1:是)
                .addTime(DateUtil.convertToDate(prisonerPhotos.getCreatetime())) // 添加时间
                .addUser("") // 添加人
                .addUserName("") // 添加人姓名
                .updateTime(DateUtil.convertToDate(prisonerPhotos.getUpdatetime())) // 更新时间
                .updateUser("") // 更新人
                .updateUserName("") // 更新人姓名
                .proCode("") // 所属省级代码
                .proName("") // 所属省级名称
                .cityCode("") // 所属市级代码
                .cityName("") // 所属市级名称
                .regCode("") // 区域代码
                .regName("") // 区域名称
                .orgCode(prisonerPhotos.getJsbh()) // 机构编号
                .orgName("") // 机构名称
                .jsbh(prisonerPhotos.getJsbh()) // 监所编号
                .rybh(prisonerPhotos.getRybh()) // 人员编号
                .photo(prisonerPhotos.getPhoto()) // 照片（base64）
                .type(prisonerPhotos.getType()) // 位置(1正面，2左，3右)
                .ywzp(prisonerPhotos != null && prisonerPhotos.getYwzp() != null ? prisonerPhotos.getYwzp() : "") // 有无照片(YWYC)
                .typetemp(prisonerPhotos != null && prisonerPhotos.getTypetemp() != null ? prisonerPhotos.getTypetemp() : "") // 临时照片
                .state(prisonerPhotos != null && prisonerPhotos.getState() != null ? prisonerPhotos.getState() : "") // 删除状态(STATE) R2：有效 R3：无效
                .creator(prisonerPhotos != null && prisonerPhotos.getCreator() != null ? prisonerPhotos.getCreator() : "") // 创建人
                .updator(prisonerPhotos != null && prisonerPhotos.getUpdator() != null ? prisonerPhotos.getUpdator() : "") // 更新人
                .photourl(prisonerPhotos != null && prisonerPhotos.getPhotourl() != null ? prisonerPhotos.getPhotourl() : "") // 照片URL
                .dataSource(PrisonDataSourceConstant.JLS) // 数据来源 1：看守所 2：拘留所
                .build();
    }

    @Autowired
    public AcpPmPrisonerJlsPhotosService(AcpPmPrisonerPhotosRepository repository) {
        this.repository = repository;
    }

    public AcpPmPrisonerPhotos save(AcpPmPrisonerPhotos prisonerPhotos) {
        return repository.save(prisonerPhotos);
    }

    public List<AcpPmPrisonerPhotos> findAll() {
        return repository.findAll();
    }

    public Optional<AcpPmPrisonerPhotos> findById(String id) {
        return repository.findById(id);
    }

    public AcpPmPrisonerPhotos update(AcpPmPrisonerPhotos prisonerPhotos) {
        return repository.save(prisonerPhotos); // save 方法会根据 ID 判断是更新还是插入
    }

    private void removeBatchByIds(List<String> deleteIds) {
        deleteIds.forEach(this::deleteById);
    }

    public void deleteById(String id) {
        if (repository.findById(id).isPresent()) {
            repository.deleteById(id);
        } else {
            // 处理记录不存在的情况，例如抛出异常或记录日志
            throw new IllegalArgumentException("Entity with id " + id + " does not exist.");
        }
    }

    public List<AcpPmPrisonerPhotos> findByRybh(String rybh) {
        return repository.findByRybh(rybh);
    }
}
