package com.rs.module.integ.service;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.integ.constant.*;
import com.rs.module.integ.dao.AcpPmPrisonerJlsInRepository;
import com.rs.module.integ.dao.AcpPmPrisonerPhotosRepository;
import com.rs.module.integ.domain.entity.AcpPmPrisonerJlsIn;
import com.rs.module.integ.domain.entity.AcpPmPrisonerJlsOut;
import com.rs.module.integ.domain.entity.AcpPmPrisonerPhotos;
import com.rs.module.integ.domain.entity.VwAcpPmPrisonerJls;
import com.rs.module.integ.domain.request.DataShareRequest;
import com.rs.module.integ.domain.response.LegalPlatformBaseResponse;
import com.rs.module.integ.domain.response.LegalPlatformJlsDataShareResponse;
import com.rs.module.integ.domain.response.PrisonerJlsResponse;
import com.rs.module.integ.service.sync.AbstractSyncLegalPlatform;
import com.rs.module.integ.util.DateUtil;
import com.rs.module.integ.util.NameKeywordGenerator;
import com.rs.module.integ.util.OrgNameCache;
import com.rs.module.integ.util.UrlHandleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.rs.module.integ.util.DateUtil.DATE_PATTERN_OF_BAR;

@Slf4j
@Service
public class AcpPmPrisonerJlsInService extends AbstractSyncLegalPlatform<VwAcpPmPrisonerJls>  {

    private final AcpPmPrisonerJlsInRepository repository;

    private final AcpPmPrisonerPhotosRepository acpPmPrisonerPhotosRepository;

    private final  AcpPmPrisonerJlsOutService acpPmPrisonerJlsOutService;

    @Autowired
    private HttpClientImpl httpClient;

    @Resource
    private BspApi bspApi;

    private final OrgNameCache orgNameCache = new OrgNameCache();

    @Value("${prison-prisoner-id-generator}")
    private String id_generator ;

    /**
     * 图片跨域问题使用nginx代理处理，如果有配置配替换，没有则不替换
     */
    @Value("${legal-platform-service.sync.prisoner-jls-photos.nginx-proxy:}")
    private String nginxProxy;

    /**
     * 查询数据库中最大的创建时间
     * @return
     */
    protected LocalDateTime getTableLastCreateTime() {
        //从数据库查询该表所有数据的最大add_time
        Date maxAddTime = repository.findMaxAddTime();
        // 如果 maxAddTime 为 null，返回默认值 2010年1月1日0点0分0秒
        if (maxAddTime == null) {
            return LocalDateTime.of(2010, 1, 1, 0, 0, 0);
        }
        // 将 Date 转换为 LocalDateTime
        return maxAddTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

    }

    @Override
    protected boolean hasData() {
        //查询acp_pm_prisoner_jls_in表中是否有数据
        return repository.count()>0?true:false;
    }

    /**
     * 查询数据库中最大的更新时间
     * @return
     */
    protected LocalDateTime getTableLastUpdateTime() {
        //从数据库查询该表所有数据的最大update_time
        Date maxUpdateTime = repository.findMaxUpdateTime();
        // 如果 maxAddTime 为 null，返回默认值 2010年1月1日0点0分0秒
        if (maxUpdateTime == null) {
            return LocalDateTime.of(2010, 1, 1, 0, 0, 0);
        }
        // 将 Date 转换为 LocalDateTime
        return maxUpdateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }


    protected LocalDateTime handleDataAndReturnMaxCreateTime(List<VwAcpPmPrisonerJls> result) {
        LocalDateTime maxCreateTime= result.stream()
                .map(VwAcpPmPrisonerJls::getAddTime) // Assuming getAddTime returns Date
                .filter(Objects::nonNull)
                .map(date -> date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()) // Convert Date to LocalDateTime
                .max(LocalDateTime::compareTo) // Now comparing LocalDateTime
                .orElse(null);
        this.setPhotoField(result);
        //将result保存到数据库中
        this.handleNewData(result);

        return maxCreateTime;
    }

    private void handleNewData(List<VwAcpPmPrisonerJls> result) {
        // 将 List<VwAcpPmPrisonerJls> 转换为 List<AcpPmPrisonerJlsIn>
        List<AcpPmPrisonerJlsIn> prisonerJlsInList = Optional.ofNullable(result)
                .orElse(Collections.emptyList())
                .stream()
                .map(VwAcpPmPrisonerJls -> {
                    AcpPmPrisonerJlsIn pdmsPrisonerJlsIn = new AcpPmPrisonerJlsIn();
                    BeanUtils.copyProperties(VwAcpPmPrisonerJls, pdmsPrisonerJlsIn);
                    return pdmsPrisonerJlsIn;
                })
                .collect(Collectors.toList());

        // 保存 List<AcpPmPrisonerJlsIn> 到数据库
        prisonerJlsInList.forEach(this::save); // 假设 save 方法用于保存到数据库
    }

    private void setPhotoField(List<VwAcpPmPrisonerJls> result) {
        //循环result，根据rybh字段查询AcpPmPrisonerPhotos表，将查询到的数据设置到result的photo字段中
        result.forEach(item -> {
            List<AcpPmPrisonerPhotos> photos = acpPmPrisonerPhotosRepository.findByRybh(item.getRybh());
            for (AcpPmPrisonerPhotos photo : photos) {
                if(photo.getType()!=null&&photo.getType().equals(LegalPlatformPrisonerPhotosConstants.FACE_PHOTO)){
                    item.setFrontPhoto(photo.getPhotourl());
                } else if (photo.getType() != null && photo.getType().equals(LegalPlatformPrisonerPhotosConstants.SIDE_PHOTO_LEFT)) {
                    item.setLeftPhoto(photo.getPhotourl());
                }else if (photo.getType() != null && photo.getType().equals(LegalPlatformPrisonerPhotosConstants.SIDE_PHOTO_RIGHT)) {
                    item.setRightPhoto(photo.getPhotourl());
                }

            }
        });
    }

    protected LocalDateTime handleDataAndReturnMaxUpdateTime(List<VwAcpPmPrisonerJls> result) {
        LocalDateTime maxUpdateTime= result.stream()
                .map(VwAcpPmPrisonerJls::getUpdateTime) // Assuming getAddTime returns Date
                .filter(Objects::nonNull)
                .map(date -> date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()) // Convert Date to LocalDateTime
                .max(LocalDateTime::compareTo) // Now comparing LocalDateTime
                .orElse(null);
        this.setPhotoField(result);
        //将result更新到数据库中
        this.handleUpdateData(result);

        return maxUpdateTime;
    }

    private void handleUpdateData(List<VwAcpPmPrisonerJls> result) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        List<String> deleteIds = new ArrayList<>();
        List<AcpPmPrisonerJlsIn> updatePdmsPrisonerJlsIns = new ArrayList<>();
        List<AcpPmPrisonerJlsOut> acpmsPrisonerJlsOuts = new ArrayList<>();
        for (VwAcpPmPrisonerJls VwAcpPmPrisonerJls : result) {
            String state = VwAcpPmPrisonerJls.getRyzt();
            String id = VwAcpPmPrisonerJls.getId();
            // 在押
            if (Objects.equals(state, LegalPlatformDataStateConstant.PRISONER_IN)) {
                AcpPmPrisonerJlsIn acpPmPrisonerJlsIn = new AcpPmPrisonerJlsIn();
                BeanUtils.copyProperties(VwAcpPmPrisonerJls, acpPmPrisonerJlsIn);
                updatePdmsPrisonerJlsIns.add(acpPmPrisonerJlsIn);
            }
            // 出所
            if (Objects.equals(state, LegalPlatformDataStateConstant.PRISONER_HISTORY)) {
                AcpPmPrisonerJlsOut pdmsPrisonerJlsOut = new AcpPmPrisonerJlsOut();
                BeanUtils.copyProperties(VwAcpPmPrisonerJls, pdmsPrisonerJlsOut);
                acpmsPrisonerJlsOuts.add(pdmsPrisonerJlsOut);
                deleteIds.add(id);
            }
            // 删除
            if (Objects.equals(state, LegalPlatformDataStateConstant.PRISONER_DELETE)) {
                deleteIds.add(id);
            }
        }
        if (!CollectionUtils.isEmpty(deleteIds)) {
            this.removeBatchByIds(deleteIds);
        }
        updatePdmsPrisonerJlsIns.forEach(this::update);
        acpPmPrisonerJlsOutService.compareSaveOrUpdateBatch(acpmsPrisonerJlsOuts);
    }

    protected List<VwAcpPmPrisonerJls> getCreateDataListPage(LocalDateTime startCreateTime, long page, long pageSize) {
        LegalPlatformBaseResponse<LegalPlatformJlsDataShareResponse> legalPlatformBaseResponse =
                httpClient.getJlsData(DataShareRequest.builder()
                        .createStartTime(DateUtil.toDate(startCreateTime))
                        .types(LegalPlatformDataShareTypeConstants.jbxx)
                        .state(LegalPlatformDataStateConstant.PRISONER_IN)
                        .pageNum((int) page)
                        .pageSize((int) pageSize)
                        .orderField(LegalPlatformFieldAndSortConstant.CREATE_TIME)
                        .seType(OrderConstant.ASC)
                        .build());
        List<PrisonerJlsResponse> prisonerJlsResponses = legalPlatformBaseResponse.getData().getJbxx();
        return this.convertBatchToAcpPmPrisonerJlsIn(prisonerJlsResponses);
    }


    protected List<VwAcpPmPrisonerJls> getUpdateDataListPage(LocalDateTime startUpdateTime, long page, long pageSize) {
        LegalPlatformBaseResponse<LegalPlatformJlsDataShareResponse> legalPlatformBaseResponse =
                httpClient.getJlsData(DataShareRequest.builder()
                        .updateStartTime(DateUtil.toDate(startUpdateTime))
                        .types(LegalPlatformDataShareTypeConstants.jbxx)
                        .pageNum((int) page)
                        .pageSize((int) pageSize)
                        .orderField(LegalPlatformFieldAndSortConstant.UPDATE_TIME)
                        .seType(OrderConstant.ASC)
                        .build());
        List<PrisonerJlsResponse> prisonerJlsResponses = legalPlatformBaseResponse.getData().getJbxx();
        return this.convertBatchToAcpPmPrisonerJlsIn(prisonerJlsResponses);
    }

    private List<VwAcpPmPrisonerJls> convertBatchToAcpPmPrisonerJlsIn(List<PrisonerJlsResponse> prisonerJlsResponses) {
        return Optional.ofNullable(prisonerJlsResponses)
                .orElse(Collections.emptyList())
                .stream()
                .map(this::convertToAcpPmPrisonerJlsIn)
                .collect(Collectors.toList());
    }


    private VwAcpPmPrisonerJls convertToAcpPmPrisonerJlsIn(PrisonerJlsResponse prisonerJlsResponse) {
        String orgName = orgNameCache.getOrgName(bspApi,prisonerJlsResponse.getJsbh());
        AreaDO areaDO = getJsh(prisonerJlsResponse.getJsbh(), prisonerJlsResponse.getJsh());
        return VwAcpPmPrisonerJls.builder()
                .id(prisonerJlsResponse.getId())
                .isDel(0) // 默认值
                .addTime(DateUtil.convertToDate(prisonerJlsResponse.getCreatetime()))
                .addUser("") // 默认值
                .addUserName("") // 默认值
                .updateTime(DateUtil.convertToDate(prisonerJlsResponse.getUpdatetime()))
                .updateUser("") // 默认值
                .updateUserName("") // 默认值
                .proCode("") // 默认值
                .proName("") // 默认值
                .cityCode("") // 默认值
                .cityName("") // 默认值
                .regCode("") // 默认值
                .regName("") // 默认值
                .orgCode(prisonerJlsResponse.getJsbh())
                .orgName(orgName) // 如果 orgName 为空，则取空字符串
                .jgrybm("")
                .rybh(prisonerJlsResponse.getRybh())
                .ajbh("") // 默认值
                .ryzt(PrisonerRyzt.convertToPrisonerRyzt(prisonerJlsResponse.getState()))
                .xm(prisonerJlsResponse.getXm())
//                .xmpy(prisonerJlsResponse.getXmpy()) // 默认值
                .xmpy(NameKeywordGenerator.generateKeywords(prisonerJlsResponse.getXm()))
                .bm(prisonerJlsResponse.getBm())
                .xb(prisonerJlsResponse.getXb())
                .csrq(DateUtil.convertToDate(prisonerJlsResponse.getCsrq()))
                .zjlx(prisonerJlsResponse.getZjlx())
                .zjhm(prisonerJlsResponse.getZjh())
                .mz(prisonerJlsResponse.getMz())
                .gj(prisonerJlsResponse.getGj())
                .hyzk(prisonerJlsResponse.getHyzk())
                .jg(prisonerJlsResponse.getJg())
                .hjd(prisonerJlsResponse.getHjd())
                .hjdxz(prisonerJlsResponse.getHjdxz())
                .xzz(prisonerJlsResponse.getXzd()) // 默认值
                .xzzxz(prisonerJlsResponse.getXzdxz()) // 默认值
                .whcd(prisonerJlsResponse.getWhcd())
                .zzmm(prisonerJlsResponse.getZzmm()) // 默认值
                .zy(prisonerJlsResponse.getZy())
                .gzdw(prisonerJlsResponse.getGzdw())
                .zw("") // 默认值
                .zwjb("") // 默认值
                .jl(prisonerJlsResponse.getBjlrjl()) // 默认值
                .sf(prisonerJlsResponse.getSf()) // 默认值
                .tssf(prisonerJlsResponse.getTssf()) // 默认值
                .jkzk(prisonerJlsResponse.getJkzk()) // 默认值
                .sg("") // 默认值
                .tz("") // 默认值
                .zc("") // 默认值
                .tbtsbj(prisonerJlsResponse.getTmtbtz()) // 默认值
                .wffzjl("") // 默认值
                .wfsj(DateUtil.convertToDate(prisonerJlsResponse.getWfsj())) // 默认值
                .wfdd(prisonerJlsResponse.getWfdd()) // 默认值
                .ajlb(prisonerJlsResponse.getAy()) // 默认值
                .jyaq(prisonerJlsResponse.getJyaq())
                .jdjg(prisonerJlsResponse.getJljdjg()) // 默认值
                .jlqx(DateUtil.format(DateUtil.convertToDate(prisonerJlsResponse.getGyqx()), DATE_PATTERN_OF_BAR)) // 默认值
                .jlqsrq(DateUtil.convertToDate(prisonerJlsResponse.getJlrq())) // 默认值
                .jljzrq(DateUtil.convertToDate(prisonerJlsResponse.getGyqx())) // 默认值
                .rssj(DateUtil.convertToDate(prisonerJlsResponse.getRsrq())) // 默认值
                .rsyy("") // 默认值
                .sypz(prisonerJlsResponse.getSypz()) // 默认值
                .sypzwsh(prisonerJlsResponse.getSypzwsh()) // 默认值
                .sydw(prisonerJlsResponse.getBadw()) // 默认值
                .syr(prisonerJlsResponse.getSyr()) // 默认值
                .lxfs(prisonerJlsResponse.getLxdh()) // 默认值
                .yglb(prisonerJlsResponse.getRygllb()) // 默认值
                .fxdj(prisonerJlsResponse.getFxdj()) // 默认值
                .sbfh("") // 默认值
                .cwh("") // 默认值
                .zzczjg("") // 默认值
                .sfhs("") // 默认值
                .dabh(prisonerJlsResponse.getDah()) // 默认值
                .fwbh("") // 默认值
                .cssj(DateUtil.convertToDate(prisonerJlsResponse.getCssj())) // 默认值
                .csyy(prisonerJlsResponse.getCsyy()) // 默认值
                .csqx("") // 默认值
                .jbr("") // 默认值
                .jbsj(null) // 默认值
                .dwdm(prisonerJlsResponse.getJsbh()) // 默认值
                .bz(prisonerJlsResponse.getBz()) // 默认值
                .bgr("") // 默认值
                .bgsj(null) // 默认值
                .gyqx(DateUtil.convertToDate(prisonerJlsResponse.getGyqx())) // 默认值
                .frontPhoto("") // 默认值
                .leftPhoto("") // 默认值
                .rightPhoto("") // 默认值
                .roomName(areaDO.getAreaName())
                .jsh(areaDO.getAreaCode())
                .areaId(areaDO.getParentId())
                .build();
    }


    /**
     * 根据传入的列表，根据每个AcpPmPrisonerPhotos对象的rybh属性，查询AcpPmPrisonerKssIn是否存在，
     * 如果存在，则更新AcpPmPrisonerKssIn的frontPhoto、leftPhoto、rightPhoto属性，对应AcpPmPrisonerPhotos的type属性不同值的照片
     * @param  result
     */
    public void updatePhoto(List<AcpPmPrisonerPhotos> result) {
        for (AcpPmPrisonerPhotos photo : result) {
            // 使用 findByRybh 方法根据 rybh 查询
            List<AcpPmPrisonerJlsIn> jlsList = repository.findByRybh(photo.getRybh());
            String photoUrl = photo.getPhotourl();
            if (StringUtils.isNotBlank(nginxProxy) && StringUtils.isNotBlank(photoUrl)) {
                photo.setPhotourl(UrlHandleUtil.urlOriginReplaceToParamAddFileType(photoUrl, nginxProxy));
            }

            if (!jlsList.isEmpty()) {
                for (AcpPmPrisonerJlsIn jlsin : jlsList) {
                    switch (photo.getType()) {
                        case "1":
                            jlsin.setFrontPhoto(photo.getPhotourl());
                            break;
                        case "2":
                            jlsin.setLeftPhoto(photo.getPhotourl());
                            break;
                        case "3":
                            jlsin.setRightPhoto(photo.getPhotourl());
                            break;
                        default:
                            log.error("未知的type值：{}", photo.getType());
                            break;
                    }
                    repository.save(jlsin);
                }
            }
        }
    }

    @Autowired
    public AcpPmPrisonerJlsInService(AcpPmPrisonerJlsInRepository repository, AcpPmPrisonerPhotosRepository acpPmPrisonerPhotosRepository, AcpPmPrisonerJlsOutService acpPmPrisonerJlsOutService) {
        this.repository = repository;
        this.acpPmPrisonerPhotosRepository = acpPmPrisonerPhotosRepository;
        this.acpPmPrisonerJlsOutService = acpPmPrisonerJlsOutService;
    }

    // 增加
    public AcpPmPrisonerJlsIn save(AcpPmPrisonerJlsIn prisoner) {
        // 检查是否存在
        if (prisoner.getId() != null) {
            Optional<AcpPmPrisonerJlsIn> existingPrisonerOptional = repository.findById(prisoner.getId());
            if (existingPrisonerOptional.isPresent()) {
                AcpPmPrisonerJlsIn existingPrisoner = existingPrisonerOptional.get();
                prisoner.setJgrybm(existingPrisoner.getJgrybm()); // 保持原有的 jgrybm
            } else {
                try {
                    JSONObject formData = JSONUtil.createObj().set("orgCode", prisoner.getOrgCode());
                    String code = bspApi.executeByRuleCode(id_generator, formData.toJSONString(4));
                    prisoner.setJgrybm(code);
                } catch (Exception e) {
                    prisoner.setJgrybm(prisoner.getRybh());
                    log.warn("生成 jgrybm 失败，使用 rybh 作为降级值", e);
                }
            }
        }
        AcpPmPrisonerJlsIn acpPmPrisonerJlsIn = null;
        try{
            acpPmPrisonerJlsIn = repository.save(prisoner);
        }catch (Exception e){
            log.error("拘留所入所信息保存失败，要保存的数据为id:{},acpPmPrisonerJlsIn:{}", acpPmPrisonerJlsIn.getId(),acpPmPrisonerJlsIn);
            throw new RuntimeException("保存拘留所入所信息失败", e);
        }
        return acpPmPrisonerJlsIn;
    }

    // 查询所有
    public List<AcpPmPrisonerJlsIn> findAll() {
        return repository.findAll();
    }

    // 根据 ID 查询
    public Optional<AcpPmPrisonerJlsIn> findById(String id) {
        return repository.findById(id);
    }

    // 更新
    public AcpPmPrisonerJlsIn update(AcpPmPrisonerJlsIn prisoner) {
        return repository.save(prisoner); // save 方法会根据 ID 判断是更新还是插入
    }

    private void removeBatchByIds(List<String> deleteIds) {
        deleteIds.forEach(this::deleteById);
    }

    // 删除
    public void deleteById(String id) {
        if(repository.findById(id).isPresent()){
            repository.deleteById(id);
        }else {
            // 处理记录不存在的情况，例如抛出异常或记录日志
            throw new IllegalArgumentException("Entity with id " + id + " does not exist.");
        }
    }

    // 根据人员编号查询
    public List<AcpPmPrisonerJlsIn> findByRybh(String rybh) {
        return repository.findByRybh(rybh);
    }

    // 查询最大 add_time
    public Date getMaxAddTime() {
        return repository.findMaxAddTime();
    }

    // 查询最大 update_time
    public Date getMaxUpdateTime() {
        return repository.findMaxUpdateTime();
    }
}
