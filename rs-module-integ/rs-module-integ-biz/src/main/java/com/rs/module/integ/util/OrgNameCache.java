package com.rs.module.integ.util;

import com.rs.adapter.bsp.api.BspApi;

import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

public class OrgNameCache {
    // 使用 ConcurrentHashMap 作为全局静态内存缓存
    private static final ConcurrentHashMap<String, String> orgNameCache = new ConcurrentHashMap<>();

    // API 调用的方法
    private String fetchOrgNameFromApi(BspApi bspApi, String orgCode) {
        return Optional.ofNullable(bspApi)
                .map(api -> api.getOrgByCode(orgCode))
                .map(org -> org.getName())
                .orElse(null);
    }

    // 获取 orgName 的方法
    public String getOrgName(BspApi bspApi, String orgCode) {
        // 先从缓存中获取 orgName
        String orgName = orgNameCache.get(orgCode);
        if (orgName != null) {
            return orgName; // 如果缓存中存在，直接返回
        }

        // 如果缓存中不存在，调用 API 获取
        try {
            orgName = fetchOrgNameFromApi(bspApi, orgCode);
            // 将获取到的值放入缓存
            if (orgName != null && !orgName.isEmpty()) {
                orgNameCache.put(orgCode, orgName);
            }
        } catch (Exception e) {
            // 处理 API 调用失败的情况
            e.printStackTrace(); // 打印异常信息
        }

        // 如果 API 调用失败或获取不到值，返回空字符串
        return orgName != null ? orgName : "";
    }
}