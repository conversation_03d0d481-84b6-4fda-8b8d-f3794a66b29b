package com.rs.module.integ.controller.admin.data.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 数据共享-监区分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ShareJqPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("第三方id")
    private String sysId;

    @ApiModelProperty("监区号")
    private String jqh;

    @ApiModelProperty("监区名称")
    private String jqmc;

    @ApiModelProperty("jqicons")
    private String jqicons;

    @ApiModelProperty("状态")
    private String state;

    @ApiModelProperty("监所编号")
    private String jsbh;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
