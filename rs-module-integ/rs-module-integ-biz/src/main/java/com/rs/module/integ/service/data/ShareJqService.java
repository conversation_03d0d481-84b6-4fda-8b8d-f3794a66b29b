package com.rs.module.integ.service.data;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.integ.controller.admin.data.vo.ShareJqListReqVO;
import com.rs.module.integ.controller.admin.data.vo.ShareJqPageReqVO;
import com.rs.module.integ.controller.admin.data.vo.ShareJqSaveReqVO;
import com.rs.module.integ.entity.data.ShareJqDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 数据共享-监区 Service 接口
 *
 * <AUTHOR>
 */
public interface ShareJqService extends IBaseService<ShareJqDO>{

    /**
     * 创建数据共享-监区
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createShareJq(@Valid ShareJqSaveReqVO createReqVO);

    /**
     * 更新数据共享-监区
     *
     * @param updateReqVO 更新信息
     */
    void updateShareJq(@Valid ShareJqSaveReqVO updateReqVO);

    /**
     * 删除数据共享-监区
     *
     * @param id 编号
     */
    void deleteShareJq(String id);

    /**
     * 获得数据共享-监区
     *
     * @param id 编号
     * @return 数据共享-监区
     */
    ShareJqDO getShareJq(String id);

    /**
    * 获得数据共享-监区分页
    *
    * @param pageReqVO 分页查询
    * @return 数据共享-监区分页
    */
    PageResult<ShareJqDO> getShareJqPage(ShareJqPageReqVO pageReqVO);

    /**
    * 获得数据共享-监区列表
    *
    * @param listReqVO 查询条件
    * @return 数据共享-监区列表
    */
    List<ShareJqDO> getShareJqList(ShareJqListReqVO listReqVO);


}
