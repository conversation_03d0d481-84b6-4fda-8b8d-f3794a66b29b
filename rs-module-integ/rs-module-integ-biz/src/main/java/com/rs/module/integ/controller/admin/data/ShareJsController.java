package com.rs.module.integ.controller.admin.data;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.integ.controller.admin.data.vo.*;
import com.rs.module.integ.entity.data.ShareJsDO;
import com.rs.module.integ.service.data.ShareJsService;

@Api(tags = "数据共享-监室")
@RestController
@RequestMapping("/integ/data/shareJs")
@Validated
public class ShareJsController {

    @Resource
    private ShareJsService shareJsService;

    @PostMapping("/create")
    @ApiOperation(value = "创建数据共享-监室")
    public CommonResult<String> createShareJs(@Valid @RequestBody ShareJsSaveReqVO createReqVO) {
        return success(shareJsService.createShareJs(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新数据共享-监室")
    public CommonResult<Boolean> updateShareJs(@Valid @RequestBody ShareJsSaveReqVO updateReqVO) {
        shareJsService.updateShareJs(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除数据共享-监室")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteShareJs(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           shareJsService.deleteShareJs(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得数据共享-监室")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ShareJsRespVO> getShareJs(@RequestParam("id") String id) {
        ShareJsDO shareJs = shareJsService.getShareJs(id);
        return success(BeanUtils.toBean(shareJs, ShareJsRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得数据共享-监室分页")
    public CommonResult<PageResult<ShareJsRespVO>> getShareJsPage(@Valid @RequestBody ShareJsPageReqVO pageReqVO) {
        PageResult<ShareJsDO> pageResult = shareJsService.getShareJsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ShareJsRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得数据共享-监室列表")
    public CommonResult<List<ShareJsRespVO>> getShareJsList(@Valid @RequestBody ShareJsListReqVO listReqVO) {
        List<ShareJsDO> list = shareJsService.getShareJsList(listReqVO);
        return success(BeanUtils.toBean(list, ShareJsRespVO.class));
    }
}
