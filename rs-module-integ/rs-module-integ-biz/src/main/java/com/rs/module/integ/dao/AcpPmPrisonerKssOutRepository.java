package com.rs.module.integ.dao;

import com.rs.module.integ.domain.entity.AcpPmPrisonerKssOut;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface AcpPmPrisonerKssOutRepository extends JpaRepository<AcpPmPrisonerKssOut, String> {
    // 根据人员编号查询
    List<AcpPmPrisonerKssOut> findByRybh(String rybh);

    // 查询最大 add_time
    @Query("SELECT MAX(a.addTime) FROM AcpPmPrisonerKssOut a")
    Date findMaxAddTime();

    // 查询最大 update_time
    @Query("SELECT MAX(a.updateTime) FROM AcpPmPrisonerKssOut a")
    Date findMaxUpdateTime();
}
