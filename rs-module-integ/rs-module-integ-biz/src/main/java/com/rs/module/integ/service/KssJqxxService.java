package com.rs.module.integ.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.db.Entity;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.util.BspDbUtil;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomSaveReqDTO;
import com.rs.module.base.controller.admin.pm.vo.AreaRelatedWarderReqVO;
import com.rs.module.base.controller.admin.pm.vo.AreaSaveReqDTO;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderSaveReqVO;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.base.entity.pm.PrisonRoomWarderDO;
import com.rs.module.base.enums.AreaTypeEnum;
import com.rs.module.base.service.pm.AreaService;
import com.rs.module.base.service.pm.PrisonRoomWarderService;
import com.rs.module.integ.domain.dto.KssJqDTO;
import com.rs.module.integ.domain.dto.KssJsDTO;
import com.rs.module.integ.entity.data.ShareJqDO;
import com.rs.module.integ.entity.data.ShareJsDO;
import com.rs.module.integ.service.data.ShareJqService;
import com.rs.module.integ.service.data.ShareJsService;
import com.rs.module.integ.service.sync.AbstractSyncLegalPlatform;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class KssJqxxService extends AbstractSyncLegalPlatform<AreaDO> {

    private final ShareJqService shareJqService;
    private final ShareJsService shareJsService;
    private final AreaService areaService;
    private final PrisonRoomWarderService prisonRoomWarderService;


    @Override
    public void incrementSync() {
        List<KssJqDTO.DataDTO.JqDTO> jqList = new ArrayList<>();
        try {
            //看守所
            String url = "http://14.66.127.82:3254/dataShare/data";
            Map<String, Object> params = new HashMap<>();
            params.put("types", "jq");
            params.put("pageNum", "1");
            params.put("pageSize", "100000");
            params.put("orderField", "createtime");
            params.put("createStartTime", "2000-01-01 00:18:38");
            HttpRequest request = HttpRequest.post(url);
            request.body(JSONObject.toJSONString(params));
            String body = request.execute().body();
            log.info("请求结果：{}", body);
            KssJqDTO kssJqDTO = JSON.parseObject(body, KssJqDTO.class);
            if (kssJqDTO.getData() != null) {
                jqList.addAll(kssJqDTO.getData().getJq());
            }
        } catch (HttpException e) {
            log.error("看守所同步监区请求异常：{}", e.getMessage());
        }
        try {
            //拘留所
            String url = "http://14.66.127.82:3254/dataShare/jlsData";
            Map<String, Object> params = new HashMap<>();
            params.put("types", "jq");
            params.put("pageNum", "1");
            params.put("pageSize", "100000");
            params.put("orderField", "createtime");
            params.put("createStartTime", "2000-01-01 00:18:38");
            HttpRequest request = HttpRequest.post(url);
            request.body(JSONObject.toJSONString(params));
            String body = request.execute().body();
            log.info("请求结果：{}", body);
            KssJqDTO kssJqDTO = JSON.parseObject(body, KssJqDTO.class);
            if (kssJqDTO.getData() != null) {
                jqList.addAll(kssJqDTO.getData().getJq());
            }
        } catch (HttpException e) {
            log.error("拘留所同步监区请求异常：{}", e.getMessage());
        }

        for (KssJqDTO.DataDTO.JqDTO jq : jqList) {
            try {
                log.info("监区信息：{}", jq);
                ShareJqDO shareKssJqDO = BeanUtil.toBean(jq, ShareJqDO.class);
                shareKssJqDO.setId(null);
                shareKssJqDO.setSysId(jq.getId());
                ShareJqDO dataShareKssJqDO = shareJqService.lambdaQuery().eq(ShareJqDO::getSysId, jq.getId()).one();
                if (dataShareKssJqDO != null) {
                    CopyOptions copyOptions = CopyOptions.create();
                    copyOptions.setIgnoreNullValue(true);
                    BeanUtil.copyProperties(shareKssJqDO, dataShareKssJqDO, copyOptions);
                    shareJqService.updateById(dataShareKssJqDO);
                    log.info("Updated dataShareKssJqDO: {}", dataShareKssJqDO);
                } else {
                    shareJqService.save(shareKssJqDO);
                    log.info("Saved shareKssJqDO: {}", shareKssJqDO);
                }

                if (StringUtils.isBlank(jq.getJqh())) {
                    log.info("监区号为空：{}", jq);
                    continue;
                }
                if (StringUtils.isBlank(jq.getJsbh())) {
                    log.info("监所编号为空：{}", jq);
                    continue;
                }

                AreaDO areaDO = areaService.lambdaQuery().eq(AreaDO::getSelfAreaId, jq.getJqh()).eq(AreaDO::getOrgCode, jq.getJsbh()).eq(AreaDO::getAreaType, AreaTypeEnum.DETENTION_AREA.getCode()).one();
                AreaDO jsAreaDO = areaService.lambdaQuery().eq(AreaDO::getOrgCode, jq.getJsbh()).eq(AreaDO::getAreaType, AreaTypeEnum.DETENTION_FACILITY.getCode()).one();
                if (jsAreaDO == null) {
                    log.info("监所信息不存在：{}", jq);
                    continue;
                }
                if (areaDO == null) {
                    log.info("监区信息不存在：{}", jq);
                    AreaSaveReqDTO areaSaveReqDTO = new AreaSaveReqDTO();
                    areaSaveReqDTO.setAreaName(jq.getJqmc());
                    areaSaveReqDTO.setParentId(jsAreaDO.getId());
                    areaSaveReqDTO.setSelfAreaId(jq.getJqh());
                    areaSaveReqDTO.setAreaType(AreaTypeEnum.DETENTION_AREA.getCode());
                    areaSaveReqDTO.setOrgCode(jsAreaDO.getOrgCode());
                    areaService.createArea(areaSaveReqDTO);
                    log.info("创建监区：{}", areaSaveReqDTO);
                }

            } catch (Exception e) {
                log.error("同步监区异常：{}", e);
            }

        }

        //监室同步
        List<KssJsDTO.DataDTO.JsDTO> jsList = new ArrayList<>();
        try {
            //看守所监室数据
            String url = "http://14.66.127.82:3254/dataShare/data";
            Map<String, Object> params = new HashMap<>();
            params.put("types", "js");
            params.put("pageNum", "1");
            params.put("pageSize", "100000");
            params.put("orderField", "createtime");
            params.put("createStartTime", "2000-01-01 00:18:38");
            HttpRequest request = HttpRequest.post(url);
            request.body(JSONObject.toJSONString(params));
            String body = request.execute().body();
            log.info("请求结果：{}", body);
            KssJsDTO kssJsDTO = JSON.parseObject(body, KssJsDTO.class);
            if (kssJsDTO.getData() != null) {
                jsList.addAll(kssJsDTO.getData().getJs());
            }
        } catch (HttpException e) {
            log.error("看守所同步监区请求异常：{}", e.getMessage());
        }

        try {
            //拘留所监室数据
            String url = "http://14.66.127.82:3254/dataShare/jlsData";
            Map<String, Object> params = new HashMap<>();
            params.put("types", "js");
            params.put("pageNum", "1");
            params.put("pageSize", "100000");
            params.put("orderField", "createtime");
            params.put("createStartTime", "2000-01-01 00:18:38");
            HttpRequest request = HttpRequest.post(url);
            request.body(JSONObject.toJSONString(params));
            String body = request.execute().body();
            log.info("请求结果：{}", body);
            KssJsDTO kssJsDTO = JSON.parseObject(body, KssJsDTO.class);
            if (kssJsDTO.getData() != null) {
                jsList.addAll(kssJsDTO.getData().getJs());
            }
        } catch (HttpException e) {
            log.error("看守所同步监室请求异常：{}", e.getMessage());
        }
        for (KssJsDTO.DataDTO.JsDTO js : jsList) {

            try {
                log.info("监区信息：{}", js);
                ShareJsDO shareKssJsDO = BeanUtil.toBean(js, ShareJsDO.class);
                shareKssJsDO.setId(null);
                shareKssJsDO.setSysId(js.getId());
                ShareJsDO dataShareJsDO = shareJsService.lambdaQuery().eq(ShareJsDO::getSysId, js.getId()).one();
                if (dataShareJsDO != null) {
                    CopyOptions copyOptions = CopyOptions.create();
                    copyOptions.setIgnoreNullValue(true);
                    BeanUtil.copyProperties(shareKssJsDO, dataShareJsDO, copyOptions);
                    shareJsService.updateById(dataShareJsDO);
                    log.info("Updated dataShareJsDO: {}", dataShareJsDO);
                } else {
                    shareJsService.save(shareKssJsDO);
                    log.info("Saved shareKssJsDO: {}", shareKssJsDO);
                }
                if (StringUtils.isBlank(js.getJsbh())) {
                    log.info("监所编号为空：{}", js);
                    continue;
                }
                AreaDO areaJqDO = null;
                if (StringUtils.isNotEmpty(js.getJqh())) {
                    //获取监区
                    areaJqDO = areaService.lambdaQuery().eq(AreaDO::getSelfAreaId, js.getJqh()).eq(AreaDO::getAreaType, AreaTypeEnum.DETENTION_AREA.getCode()).eq(AreaDO::getOrgCode, js.getJsbh()).one();
                }


                //监室
                AreaDO areaDO = areaService.lambdaQuery().eq(AreaDO::getSelfAreaId, js.getJsh()).eq(AreaDO::getOrgCode, js.getJsbh()).eq(AreaDO::getAreaType, AreaTypeEnum.DETENTION_ROOM.getCode()).one();
                //监所
                AreaDO jsAreaDO = areaService.lambdaQuery().eq(AreaDO::getOrgCode, js.getJsbh()).eq(AreaDO::getAreaType, AreaTypeEnum.DETENTION_FACILITY.getCode()).one();
                if (jsAreaDO == null) {
                    log.info("监所信息不存在：{}", js);
                    continue;
                }
                if (areaDO == null) {
                    AreaSaveReqDTO areaSaveReqDTO = new AreaSaveReqDTO();
                    areaSaveReqDTO.setAreaName(js.getJsmc());
                    if (areaJqDO != null) {
                        areaSaveReqDTO.setParentId(areaJqDO.getId());
                    } else {
                        areaSaveReqDTO.setParentId(jsAreaDO.getId());
                    }
                    areaSaveReqDTO.setSelfAreaId(js.getJsh());
                    areaSaveReqDTO.setAreaType(AreaTypeEnum.DETENTION_ROOM.getCode());
                    areaSaveReqDTO.setOrgCode(jsAreaDO.getOrgCode());
                    AreaPrisonRoomSaveReqDTO areaPrisonRoomSaveReqDTO = new AreaPrisonRoomSaveReqDTO();

                    areaPrisonRoomSaveReqDTO.setRoomName(js.getJsmc());
                    areaPrisonRoomSaveReqDTO.setOrgCode(jsAreaDO.getOrgCode());
                    areaPrisonRoomSaveReqDTO.setOrgName(jsAreaDO.getOrgName());
                    areaPrisonRoomSaveReqDTO.setSelfAreaId(js.getJsh());
                    if (areaJqDO != null) {
                        areaPrisonRoomSaveReqDTO.setAreaId(areaJqDO.getId());
                        areaPrisonRoomSaveReqDTO.setAreaName(areaJqDO.getAreaName());
                    } else {
                        areaPrisonRoomSaveReqDTO.setAreaId(jsAreaDO.getId());
                        areaPrisonRoomSaveReqDTO.setAreaName(jsAreaDO.getAreaName());
                    }


                    areaSaveReqDTO.setAreaPrisonRoomSaveDto(areaPrisonRoomSaveReqDTO);
                    areaService.createArea(areaSaveReqDTO);
                    log.info("创建监区：{}", areaSaveReqDTO);
                }

            } catch (Exception e) {
                log.error("同步监区异常：{}", e);
            }
        }
        //设置主协管
        for (KssJsDTO.DataDTO.JsDTO jsxx : jsList) {
            //监室
            AreaDO areaDO = areaService.lambdaQuery().eq(AreaDO::getSelfAreaId, jsxx.getJsh()).eq(AreaDO::getOrgCode, jsxx.getJsbh()).eq(AreaDO::getAreaType, AreaTypeEnum.DETENTION_ROOM.getCode()).one();
            if (areaDO != null) {
                List<PrisonRoomWarderDO> list = prisonRoomWarderService.lambdaQuery().eq(PrisonRoomWarderDO::getRoomId, areaDO.getId()).list();

                if (list.isEmpty()) {
                    //保存主协管信息
                    List<AreaRelatedWarderReqVO> warderReqVOList = new ArrayList<>();
                    if (StringUtils.isNotEmpty(jsxx.getZgmjjh())) {
                        Entity where = Entity.create();
                        try {
                            where.setTableName("uac_user");
                            where.set("LOGIN_ID", jsxx.getZgmjjh());
                            List<Entity> entityList = BspDbUtil.getDb().find(where);
                            if (entityList != null && !entityList.isEmpty()) {
                                Entity entity = entityList.get(0);
                                String id = entity.getStr("ID");
                                String name = entity.getStr("NAME");
                                String idCard = entity.getStr("ID_CARD");
                                AreaRelatedWarderReqVO reqVO = new AreaRelatedWarderReqVO();
                                reqVO.setPoliceId(id);
                                reqVO.setPoliceName(name);
                                reqVO.setPoliceSfzh(idCard);
                                reqVO.setUserType("w");
                                reqVO.setRoomId(areaDO.getId());
                                warderReqVOList.add(reqVO);
                            } else {
                                log.error("用户不存在 {}", jsxx.getZgmjjh());
                            }
                        } catch (SQLException e) {

                        }
                    }
                    if (StringUtils.isNotEmpty(jsxx.getXgmjjh())) {
                        Entity where = Entity.create();
                        try {
                            where.setTableName("uac_user");
                            where.set("LOGIN_ID", jsxx.getXgmjjh());
                            List<Entity> entityList = BspDbUtil.getDb().find(where);
                            if (entityList != null && !entityList.isEmpty()) {
                                Entity entity = entityList.get(0);
                                String id = entity.getStr("ID");
                                String name = entity.getStr("NAME");
                                String idCard = entity.getStr("ID_CARD");
                                AreaRelatedWarderReqVO reqVO = new AreaRelatedWarderReqVO();
                                reqVO.setPoliceId(id);
                                reqVO.setPoliceName(name);
                                reqVO.setPoliceSfzh(idCard);
                                reqVO.setUserType("a");
                                reqVO.setRoomId(areaDO.getId());
                                warderReqVOList.add(reqVO);
                            } else {
                                log.error("用户不存在 {}", jsxx.getXgmjjh());
                            }
                        } catch (SQLException e) {

                        }
                    }
                    try {
                        if (CollUtil.isNotEmpty(warderReqVOList)) {
                            for (AreaRelatedWarderReqVO warderReqVO : warderReqVOList) {
                                PrisonRoomWarderSaveReqVO prisonRoomWarderSaveReqVO = BeanUtils.toBean(warderReqVO, PrisonRoomWarderSaveReqVO.class);
                                prisonRoomWarderService.createPrisonRoomWarder(prisonRoomWarderSaveReqVO);
                            }
                        }
                    } catch (Exception e) {
                        log.error("保存监室主协管人员异常", e);
                    }
                }
            }
        }

    }

    @Override
    protected List<AreaDO> getCreateDataListPage(LocalDateTime startCreateTime, long page, long pageSize) {
        return Collections.emptyList();
    }

    @Override
    protected List<AreaDO> getUpdateDataListPage(LocalDateTime startUpdateTime, long page, long pageSize) {
        return Collections.emptyList();
    }

    @Override
    protected LocalDateTime handleDataAndReturnMaxCreateTime(List<AreaDO> result) {
        return null;
    }

    @Override
    protected LocalDateTime handleDataAndReturnMaxUpdateTime(List<AreaDO> result) {
        return null;
    }

    @Override
    protected LocalDateTime getTableLastCreateTime() {
        return null;
    }

    @Override
    protected boolean hasData() {
        return true;
    }

    @Override
    protected LocalDateTime getTableLastUpdateTime() {
        return null;
    }


}
