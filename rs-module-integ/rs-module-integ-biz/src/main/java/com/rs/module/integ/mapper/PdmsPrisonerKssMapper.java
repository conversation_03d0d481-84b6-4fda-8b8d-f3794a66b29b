package com.rs.module.integ.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rs.module.integ.domain.po.PdmsPrisonerKss;
import com.rs.module.integ.domain.vo.GetPrisonerInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface PdmsPrisonerKssMapper extends BaseMapper<PdmsPrisonerKss> {

    @Select("select * from pdms_prisoner_kss where jyrq is not null limit 1")
    GetPrisonerInfoVO test();
}
