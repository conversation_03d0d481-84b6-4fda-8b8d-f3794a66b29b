package com.rs.module.integ.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rs.module.integ.constant.PdmsDictionaryConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("法综数据同步-拘留所人员接口返回数据")
public class PrisonerJlsResponse {

    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private String id;

    /**
     * 监所编号
     */
    @ApiModelProperty("监所编号")
    private String jsbh;

    /**
     * 人员编号（业务平台唯一标识）
     */
    @ApiModelProperty("人员编号（业务平台唯一标识）")
    private String rybh;

    /**
     * 填表人
     */
    @ApiModelProperty("填表人")
    private String tbr;

    /**
     * 填表日期
     */
    @ApiModelProperty("填表日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tbrq;

    /**
     * 网办人员编号 (法制系统唯一编号)
     */
    @ApiModelProperty("网办人员编号 (法制系统唯一编号)")
    private String wbrybh;

    /**
     * 过程编号
     */
    @ApiModelProperty("过程编号")
    private String gcbh;

    /**
     * 业务流程ID
     */
    @ApiModelProperty("业务流程ID")
    private String ywlcid;

    /**
     * 业务进程ID
     */
    @ApiModelProperty("业务进程ID")
    private String taskid;

    /**
     * 所内编号
     */
    @ApiModelProperty("所内编号")
    private String snbh;

    /**
     * 拘室号
     */
    @ApiModelProperty("拘室号")
    private String jsh;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String xm;

    /**
     * 姓名拼音
     */
    @ApiModelProperty("姓名拼音")
    private String xmpy;

    /**
     * 姓名拼音首字母
     */
    @ApiModelProperty("姓名拼音首字母")
    private String xmpyszm;

    /**
     * 别名
     */
    @ApiModelProperty("别名")
    private String bm;

    /**
     * 别名同音
     */
    @ApiModelProperty("别名同音")
    private String bmty;

    /**
     * 性别 字典值
     *
     * @see PdmsDictionaryConstant#XB
     */
    @ApiModelProperty("性别 字典值：" + PdmsDictionaryConstant.XB)
    private String xb;

    /**
     * 入所日期
     */
    @ApiModelProperty("入所日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime rsrq;

    /**
     * 证件类型 字典值
     *
     * @see PdmsDictionaryConstant#ZJLX
     */
    @ApiModelProperty("证件类型 字典值：" + PdmsDictionaryConstant.ZJLX)
    private String zjlx;

    /**
     * 证件号
     */
    @ApiModelProperty("证件号")
    private String zjh;

    /**
     * 国籍 字典值
     */
    @ApiModelProperty("国籍 字典值：" + PdmsDictionaryConstant.GJ)
    private String gj;

    /**
     * 文化程度 字典值
     */
    @ApiModelProperty("文化程度 字典值：" + PdmsDictionaryConstant.WHCD)
    private String whcd;

    /**
     * 身份 字典值
     */
    @ApiModelProperty("身份 字典值：" + PdmsDictionaryConstant.SF)
    private String sf;

    /**
     * 特殊身份
     */
    @ApiModelProperty("特殊身份 字典值：" + PdmsDictionaryConstant.TSSF)
    private String tssf;

    /**
     * 民族 字典值
     * <p>
     *
     * @see PdmsDictionaryConstant#MZ
     */
    @ApiModelProperty("民族 字典值：" + PdmsDictionaryConstant.MZ)
    private String mz;

    /**
     * 政治面貌 字典值
     */
    @ApiModelProperty("政治面貌 字典值：" + PdmsDictionaryConstant.ZZMM)
    private String zzmm;

    /**
     * 出生日期
     */
    @ApiModelProperty("出生日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime csrq;

    /**
     * 婚姻状况
     *
     * @see PdmsDictionaryConstant#HYZK
     */
    @ApiModelProperty("婚姻状况 字典值：" + PdmsDictionaryConstant.HYZK)
    private String hyzk;

    /**
     * 户籍地 字典
     */
    @ApiModelProperty("户籍地 字典值：" + PdmsDictionaryConstant.XZQH)
    private String hjd;

    /**
     * 户籍地详址
     */
    @ApiModelProperty("户籍地详址")
    private String hjdxz;

    /**
     * 籍贯 字典值
     */
    @ApiModelProperty("籍贯 字典值：" + PdmsDictionaryConstant.XZQH)
    private String jg;

    /**
     * 现住地 字典
     */
    @ApiModelProperty("现住地 字典值：" + PdmsDictionaryConstant.XZQH)
    private String xzd;

    /**
     * 现住地详址
     */
    @ApiModelProperty("现住地详址")
    private String xzdxz;

    /**
     * 工作单位
     */
    @ApiModelProperty("工作单位")
    private String gzdw;

    /**
     * 职业 字典值
     */
    @ApiModelProperty("职业 字典值：" + PdmsDictionaryConstant.ZY)
    private String zy;

    /**
     * 拘留决定机关
     */
    @ApiModelProperty("拘留决定机关")
    private String jljdjg;

    /**
     * 办案单位（送案单位）
     */
    @ApiModelProperty("办案单位（送案单位）")
    private String badw;

    /**
     * 办案人
     */
    @ApiModelProperty("办案人")
    private String bar;

    /**
     * 入所性质(JLSRJYY)（收拘类别）字典值
     */
    @ApiModelProperty("入所性质(JLSRJYY)（收拘类别） 字典值：" + PdmsDictionaryConstant.JLSRJYY)
    private String rsxz;

    /**
     * 收押人（收拘民警）
     */
    @ApiModelProperty("收押人（收拘民警）")
    private String syr;

    /**
     * 收押凭证(SYPZ) 字典值
     */
    @ApiModelProperty("收押凭证(SYPZ) 字典值：" + PdmsDictionaryConstant.SYPZ)
    private String sypz;

    /**
     * 收押凭证文书号（收拘法律文书号）
     */
    @ApiModelProperty("收押凭证文书号（收拘法律文书号）")
    private String sypzwsh;

    /**
     * 拘留所案由(JLSAJLB)
     */
    @ApiModelProperty("拘留所案由(JLSAJLB) 字典值：" + PdmsDictionaryConstant.JLSAJLB)
    private String ay;

    /**
     * 办案人警号
     */
    @ApiModelProperty("办案人警号")
    private String barjh;

    /**
     * 办案民警电话
     */
    @ApiModelProperty("办案民警电话")
    private String bardh;

    /**
     * 办案传真电话
     */
    @ApiModelProperty("办案传真电话")
    private String czdh;

    /**
     * 关押天数（日）
     */
    @ApiModelProperty("关押天数（日）")
    private String gyts;

    /**
     * 拘留日期（拘留开始日期）
     * <p>
     * 同步接口返回值： 2024-12-01
     */
    @ApiModelProperty("拘留日期（拘留开始日期）")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate jlrq;

    /**
     * 关押期限（拘留结束日期）
     * <p>
     * 同步接口返回值： 2024-12-01
     */
    @ApiModelProperty("关押期限（拘留结束日期）")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate gyqx;

    /**
     * 涉毒尿检初查结果
     */
    @ApiModelProperty("涉毒尿检初查结果")
    private String sdnjccjg;

    /**
     * 涉毒尿检单位
     */
    @ApiModelProperty("涉毒尿检单位")
    private String sdnjdw;

    /**
     * 涉毒尿检初检时间
     */
    @ApiModelProperty("涉毒尿检初检时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss.S")
    private LocalDateTime sdnjcjsj;

    /**
     * 涉毒尿检检查人
     */
    @ApiModelProperty("涉毒尿检检查人")
    private String sdnjjcr;

    /**
     * 简要案情
     */
    @ApiModelProperty("简要案情")
    private String jyaq;

    /**
     * 档案号
     */
    @ApiModelProperty("档案号")
    private String dah;

    /**
     * 回执文书号
     */
    @ApiModelProperty("回执文书号")
    private String hzflwsh;

    /**
     * 派综人员编号
     */
    @ApiModelProperty("派综人员编号")
    private String pzrybh;

    /**
     * 派综案件编号
     */
    @ApiModelProperty("派综案件编号")
    private String pzajbh;

    /**
     * 派综法律文书号
     */
    @ApiModelProperty("派综法律文书号")
    private String pzflwsh;

    /**
     * 操作状态(CZZT) 字典值
     */
    @ApiModelProperty("操作状态(CZZT) 字典值：" + PdmsDictionaryConstant.CZZT)
    private String czzt;

    /**
     * 拒收理由（健康检查结果）
     */
    @ApiModelProperty("拒收理由（健康检查结果）")
    private String jsly;

    /**
     * 不宜拘留文书号
     */
    @ApiModelProperty("不宜拘留文书号")
    private String byjswsh;

    /**
     * 拒收日期
     */
    @ApiModelProperty("拒收日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date jsrq;

    /**
     * 重刑犯(ZXF) 字典值
     */
    @ApiModelProperty("重刑犯(ZXF) 字典值：" + PdmsDictionaryConstant.ZXF)
    private String zxf;

    /**
     * 安全等级(AQDJ) 字典值
     */
    @ApiModelProperty("安全等级(AQDJ) 字典值：" + PdmsDictionaryConstant.AQDJ)
    private String aqdj;

    /**
     * 人员管理类别(RYGLLB) 字典值
     */
    @ApiModelProperty("人员管理类别(RYGLLB) 字典值：" + PdmsDictionaryConstant.RYGLLB)
    private String rygllb;

    /**
     * 临时出所原因(LSCS) 字典值
     */
    @ApiModelProperty("临时出所原因(LSCS) 字典值：" + PdmsDictionaryConstant.LSCS)
    private String lscsyy;

    /**
     * 上一次临时出所时间
     */
    @ApiModelProperty("上一次临时出所时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lscssj;

    /**
     * 上一次临时出所回所时间
     */
    @ApiModelProperty("上一次临时出所回所时间")
    private String lscshssj;

    /**
     * 出入监标记(CJBJ) 字典值
     */
    @ApiModelProperty("出入监标记(CJBJ) 字典值：" + PdmsDictionaryConstant.CJBJ)
    private String crjbj;

    /**
     * 入库合格不合格标记(SHFO) 字典值
     */
    @ApiModelProperty("出入库合格不合格标记(SHFO) 字典值：" + PdmsDictionaryConstant.SHFO)
    private String rkbhgbj;

    /**
     * 不合格入库原因
     */
    @ApiModelProperty("出入库合格不合格原因")
    private String rkbhgyy;

    /**
     * 是否及时录入标记(SHFO) 字典值
     */
    @ApiModelProperty("是否及时录入标记(SHFO) 字典值：" + PdmsDictionaryConstant.SHFO)
    private String lrsfjs;

    /**
     * 出所时间
     */
    @ApiModelProperty("出所时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cssj;

    /**
     * 出所原因(JLSCSYY) 字典值
     */
    @ApiModelProperty("出所原因(JLSCSYY) 字典值：" + PdmsDictionaryConstant.JLSCSYY)
    private String csyy;

    /**
     * 人员状态(STATE) 字典值
     */
    @ApiModelProperty("人员状态(STATE) 字典值：" + PdmsDictionaryConstant.STATE)
    private String state;

    /**
     * 上传标志(SHFO) 字典值
     */
    @ApiModelProperty("上传标志(SHFO) 字典值：" + PdmsDictionaryConstant.SHFO)
    private String scbz;

    /**
     * 单位类型(DWLX) 字典值
     */
    @ApiModelProperty("单位类型(DWLX) 字典值：" + PdmsDictionaryConstant.DWLX)
    private String dwlx;

    /**
     * 违法地点
     */
    @ApiModelProperty("违法地点")
    private String wfdd;

    /**
     * 收押单位
     */
    @ApiModelProperty("收押单位")
    private String sydw;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updator;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    private String lxdh;

    /**
     * 户籍地所在派出所
     */
    @ApiModelProperty("户籍地所在派出所")
    private String hjdszpcs;

    /**
     * 体貌体表特征
     */
    @ApiModelProperty("体貌体表特征")
    private String tmtbtz;

    /**
     * 单位地址
     */
    @ApiModelProperty("单位地址")
    private String dwdz;

    /**
     * 被拘留人简历
     */
    @ApiModelProperty("被拘留人简历")
    private String bjlrjl;

    /**
     * 被拘留人接受处罚情况
     */
    @ApiModelProperty("被拘留人接受处罚情况")
    private String bjlrjscfqk;

    /**
     * 违法时间
     */
    @ApiModelProperty("违法时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime wfsj;

    /**
     * 风险等级（FXDJ） 字典值
     */
    @ApiModelProperty("风险等级（FXDJ） 字典值：" + PdmsDictionaryConstant.FXDJ)
    private String fxdj;

    /**
     * 金额
     */
    @ApiModelProperty("金额")
    private BigDecimal je;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String bz;

    /**
     * 手环编号
     */
    @ApiModelProperty("手环编号")
    private String shid;

    /**
     * 耳目
     */
    @ApiModelProperty("耳目")
    private String em;

    /**
     * 耳目类型 字典值
     */
    @ApiModelProperty("耳目类型 字典值：" + PdmsDictionaryConstant.EMLX)
    private String emlx;

    /**
     * 是否需要发药(SHFO) 字典值
     */
    @ApiModelProperty("是否需要发药(SHFO) 字典值：" + PdmsDictionaryConstant.SHFO)
    private String sffy;

    /**
     * 发药开始日期(yyyy-MM-dd)
     */
    @ApiModelProperty("发药开始日期(yyyy-MM-dd)")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime fyksrq;

    /**
     * 发药结束日期(yyyy-MM-dd)
     */
    @ApiModelProperty("发药结束日期(yyyy-MM-dd)")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime fyjsrq;

    /**
     * 是否有传染病(SHFO) 字典值
     */
    @ApiModelProperty("是否有传染病(SHFO) 字典值：" + PdmsDictionaryConstant.SHFO)
    private String sfcrb;

    /**
     * 精神异常标志(SHFO) 字典值
     */
    @ApiModelProperty("精神异常标志(SHFO) 字典值：" + PdmsDictionaryConstant.SHFO)
    private String jsycbz;

    /**
     * 列控等级（LKDJ） 字典值
     */
    @ApiModelProperty("列控等级（LKDJ） 字典值：" + PdmsDictionaryConstant.LKDJ)
    private String lkdj;

    /**
     * 是否抽血采样(SHFO) 字典值
     */
    @ApiModelProperty("是否抽血采样(SHFO) 字典值：" + PdmsDictionaryConstant.SHFO)
    private String sfcxcy;

    /**
     * 健康情况(JKZK) 字典值
     */
    @ApiModelProperty("健康情况(JKZK) 字典值：" + PdmsDictionaryConstant.JKZK)
    private String jkzk;

    /**
     * 同案编号
     */
    @ApiModelProperty("同案编号")
    private String tabh;

    /**
     * 宽管人员(KGRY) 字典值
     */
    @ApiModelProperty("宽管人员(KGRY) 字典值：" + PdmsDictionaryConstant.KGRY)
    private String kgry;

    /**
     * 接种次数
     */
    @ApiModelProperty("接种次数")
    private String jzcs;

    /**
     * 是否加强针
     */
    @ApiModelProperty("是否加强针")
    private String sfjqz;

    /**
     * 核酸检测次数
     */
    @ApiModelProperty("核酸检测次数")
    private String hsjccs;

    /**
     * 核酸检测结果(HSJCJG) 字典值
     */
    @ApiModelProperty("核酸检测结果(HSJCJG) 字典值：" + PdmsDictionaryConstant.HSJCJG)
    private String hsjcjg;
}
