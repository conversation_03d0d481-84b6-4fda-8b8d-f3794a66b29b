package com.rs.module.integ.dao;

import com.rs.module.integ.domain.entity.AcpPmPrisonerPhotos;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface AcpPmPrisonerPhotosRepository extends JpaRepository<AcpPmPrisonerPhotos, String> {
    List<AcpPmPrisonerPhotos> findByRybh(String rybh); // 根据人员编号查询

    // 查询最大 add_time
    @Query("SELECT MAX(a.addTime) FROM AcpPmPrisonerPhotos a WHERE a.dataSource = :dataSource")
    Date findMaxAddTime(@Param("dataSource") Integer dataSource);

    // 查询最大 update_time
    @Query("SELECT MAX(a.updateTime) FROM AcpPmPrisonerPhotos a WHERE a.dataSource = :dataSource")
    Date findMaxUpdateTime(@Param("dataSource") Integer dataSource);
}