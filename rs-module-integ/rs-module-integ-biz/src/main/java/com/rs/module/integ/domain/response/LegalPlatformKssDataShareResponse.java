package com.rs.module.integ.domain.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.rs.module.integ.domain.po.PdmsLegalPlatformDictionary;
import com.rs.module.integ.domain.po.PdmsPrisonerPhotos;
import lombok.Data;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LegalPlatformKssDataShareResponse {

    /**
     * 看守所被监管人基本信息
     */
    private List<PrisonerKssResponse> jbxx;

    /**
     * 监室信息
     */
    private List<RoomKssResponse> js;

    /**
     * 监管人照片信息
     */
    private List<PdmsPrisonerPhotos> photos;

    /**
     * 字典信息
     */
    private List<PdmsLegalPlatformDictionary> dictionary;
}
