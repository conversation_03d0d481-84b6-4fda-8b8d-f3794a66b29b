package com.rs.module.integ.exception;

import org.springframework.http.HttpStatus;

/**
 * 业务基础异常
 */
public class BusinessException extends RuntimeException {

	public BusinessException() {
    }

    public BusinessException(String message) {
        super(message);
    }


    public BusinessException(Integer errorCode, String errorMsg) {
        super(errorMsg);
        this.errorMsg = errorMsg;
        this.errorCode=errorCode;
    }

    public BusinessException(String message, Throwable cause) {
        super(message, cause);
    }


    /**
     * 状态码
     */
    private HttpStatus status;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 业务异常自定义错误码
     */
    private Integer errorCode;

}
