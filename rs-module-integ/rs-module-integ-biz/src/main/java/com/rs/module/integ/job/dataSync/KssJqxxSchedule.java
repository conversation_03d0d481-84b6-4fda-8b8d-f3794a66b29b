package com.rs.module.integ.job.dataSync;

import com.rs.module.integ.config.ThreadPoolConfig;
import com.rs.module.integ.service.KssJqxxService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;

/**
 * 看守所监区信息同步
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class KssJqxxSchedule {

    private final KssJqxxService kssJqxxService;

    @Qualifier(ThreadPoolConfig.COMMON_THREAD_POOL_BEAN_NAME)
    private final ExecutorService executorService;

    @XxlJob("KssJqxxSchedule")
    public void execute() {
        log.info("开始异步同步看守所监区信息");
        executorService.execute(kssJqxxService::incrementSync);
    }
}
