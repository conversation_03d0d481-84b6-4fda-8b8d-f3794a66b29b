package com.rs.module.integ.dao;

import com.rs.module.integ.domain.entity.AcpPmPrisonerJlsIn;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface AcpPmPrisonerJlsInRepository extends JpaRepository<AcpPmPrisonerJlsIn, String> {
    // 根据人员编号查询
    List<AcpPmPrisonerJlsIn> findByRybh(String rybh);

    // 查询最大 add_time
    @Query("SELECT MAX(a.addTime) FROM AcpPmPrisonerJlsIn a")
    Date findMaxAddTime();

    // 查询最大 update_time
    @Query("SELECT MAX(a.updateTime) FROM AcpPmPrisonerJlsIn a")
    Date findMaxUpdateTime();
}
