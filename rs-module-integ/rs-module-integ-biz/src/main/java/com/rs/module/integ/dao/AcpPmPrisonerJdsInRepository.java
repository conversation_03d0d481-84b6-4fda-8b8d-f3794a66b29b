package com.rs.module.integ.dao;

import com.rs.module.integ.domain.entity.AcpPmPrisonerJdsIn;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface AcpPmPrisonerJdsInRepository extends JpaRepository<AcpPmPrisonerJdsIn, String> {
    // 自定义查询方法可以在这里添加
    List<AcpPmPrisonerJdsIn> findByRybh(String rybh); // 根据人员编号查询

    // 查询最大 add_time
    @Query("SELECT MAX(a.addTime) FROM AcpPmPrisonerJdsIn a")
    Date findMaxAddTime();

    // 查询最大 update_time
    @Query("SELECT MAX(a.updateTime) FROM AcpPmPrisonerJdsIn a")
    Date findMaxUpdateTime();
}
