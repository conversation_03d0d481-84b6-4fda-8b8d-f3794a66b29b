package com.rs.module.integ.controller;

import com.rs.module.integ.domain.entity.AcpPmPrisonerJlsIn;
import com.rs.module.integ.service.AcpPmPrisonerJlsInService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/prisoners/jls")
public class AcpPmPrisonerJlsInController {

    private final AcpPmPrisonerJlsInService service;

    @Autowired
    public AcpPmPrisonerJlsInController(AcpPmPrisonerJlsInService service) {
        this.service = service;
    }

    @PostMapping
    public AcpPmPrisonerJlsIn create(@RequestBody AcpPmPrisonerJlsIn prisoner) {
        return service.save(prisoner);
    }

    @GetMapping
    public List<AcpPmPrisonerJlsIn> getAll() {
        return service.findAll();
    }

    @GetMapping("/{id}")
    public Optional<AcpPmPrisonerJlsIn> getById(@PathVariable String id) {
        return service.findById(id);
    }

    @GetMapping("syncData")
    public void syncData() {
        service.incrementSync();
    }

    @PutMapping("/{id}")
    public AcpPmPrisonerJlsIn update(@PathVariable String id, @RequestBody AcpPmPrisonerJlsIn prisoner) {
        prisoner.setId(id); // 确保 ID 被设置
        return service.update(prisoner);
    }

    @DeleteMapping("/{id}")
    public void delete(@PathVariable String id) {
        service.deleteById(id);
    }

    @GetMapping("/rybh/{rybh}")
    public List<AcpPmPrisonerJlsIn> getByRybh(@PathVariable String rybh) {
        return service.findByRybh(rybh);
    }
}