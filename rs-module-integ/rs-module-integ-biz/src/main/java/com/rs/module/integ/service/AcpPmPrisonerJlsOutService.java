package com.rs.module.integ.service;

import com.rs.module.integ.dao.AcpPmPrisonerJlsOutRepository;
import com.rs.module.integ.domain.entity.AcpPmPrisonerJlsOut;
import com.rs.module.integ.domain.entity.AcpPmPrisonerKssOut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
public class AcpPmPrisonerJlsOutService {

    private final AcpPmPrisonerJlsOutRepository repository;

    @Autowired
    public AcpPmPrisonerJlsOutService(AcpPmPrisonerJlsOutRepository repository) {
        this.repository = repository;
    }

    // 保存
    public AcpPmPrisonerJlsOut save(AcpPmPrisonerJlsOut prisonerJlsOut) {
        return repository.save(prisonerJlsOut);
    }

    // 查询所有
    public List<AcpPmPrisonerJlsOut> findAll() {
        return repository.findAll();
    }

    // 根据 ID 查询
    public Optional<AcpPmPrisonerJlsOut> findById(String id) {
        return repository.findById(id);
    }

    // 更新
    public AcpPmPrisonerJlsOut update(AcpPmPrisonerJlsOut prisonerJlsOut) {
        return repository.save(prisonerJlsOut); // save 方法会根据 ID 判断是更新还是插入
    }

    // 删除
    public void deleteById(String id) {
        repository.deleteById(id);
    }

    // 根据人员编号查询
    public List<AcpPmPrisonerJlsOut> findByRybh(String rybh) {
        return repository.findByRybh(rybh);
    }

    // 查询最大添加时间
    public Date getMaxAddTime() {
        return repository.findMaxAddTime();
    }

    // 查询最大更新时间
    public Date getMaxUpdateTime() {
        return repository.findMaxUpdateTime();
    }

    public void compareSaveOrUpdateBatch(List<AcpPmPrisonerJlsOut> acpmsPrisonerJlsOuts) {
        acpmsPrisonerJlsOuts.forEach(this::save);
    }
}