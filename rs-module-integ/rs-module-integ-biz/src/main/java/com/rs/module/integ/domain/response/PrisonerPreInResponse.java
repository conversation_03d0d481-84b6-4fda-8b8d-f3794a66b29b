package com.rs.module.integ.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class PrisonerPreInResponse {

    @ApiModelProperty("ID")
    private String id;
    @ApiModelProperty("姓名")
    private String xm;
    @ApiModelProperty("过程编号")
    private String gcbh;
    @ApiModelProperty("网办人员编号")
    private String wbrybh;
    @ApiModelProperty("档案编号")
    private String dah;
    @ApiModelProperty("监所编号")
    private String jsbh;
    @ApiModelProperty("姓名拼音")
    private String xmpy;
    @ApiModelProperty("别名")
    private String bm;
    @ApiModelProperty("别名同音")
    private String bmty;
    @ApiModelProperty("民族")
    private String mz;
    @ApiModelProperty("国籍")
    private String gj;
    @ApiModelProperty("性别")
    private String xb;
    @ApiModelProperty("出生日期")
    private LocalDateTime csrq;
    @ApiModelProperty("证件类型")
    private String zjlx;
    @ApiModelProperty("证件号")
    private String zjh;
    @ApiModelProperty("政治面貌")
    private String zzmm;
    @ApiModelProperty("婚姻状况")
    private String hyzk;
    @ApiModelProperty("足长")
    private String zuc;
    @ApiModelProperty("身高")
    private String sg;
    @ApiModelProperty("籍贯")
    private String jg;
    @ApiModelProperty("户籍地")
    private String hjd;
    @ApiModelProperty("户籍地详址")
    private String hjdxz;
    @ApiModelProperty("现住地")
    private String xzd;
    @ApiModelProperty("现住地详址")
    private String xzdxz;
    @ApiModelProperty("文化程度")
    private String whcd;
    @ApiModelProperty("专长")
    private String zc;
    @ApiModelProperty("身份")
    private String sf;
    @ApiModelProperty("特殊身份")
    private String tssf;
    @ApiModelProperty("职业")
    private String zy;
    @ApiModelProperty("(原)工作单位")
    private String gzdw;
    @ApiModelProperty("健康情况")
    private String jkzk;
    @ApiModelProperty("病号类型")
    private String bhlx;
    @ApiModelProperty("艾滋病")
    private String azb;
    @ApiModelProperty("入所日期")
    private LocalDateTime rsrq;
    @ApiModelProperty("入所性质")
    private String rsxz;
    @ApiModelProperty("转入单位")
    private String zrdw;
    @ApiModelProperty("手环ID")
    private String shid;
    @ApiModelProperty("送押单位")
    private String sydw;
    @ApiModelProperty("送押人")
    private String syr;
    @ApiModelProperty("收押人")
    private String sy;
    @ApiModelProperty("收押非拘捕人员")
    private String byzd;
    @ApiModelProperty("收押凭证文书号")
    private String sypzwsh;
    @ApiModelProperty("收押凭证")
    private String sypz;
    @ApiModelProperty("羁押日期")
    private LocalDateTime jyrq;
    @ApiModelProperty("关押期限")
    private LocalDateTime gyqx;
    @ApiModelProperty("主要案由")
    private String ay;
    @ApiModelProperty("细化案由")
    private String xhay;
    @ApiModelProperty("犯罪经历")
    private String fzjl;
    @ApiModelProperty("简要案情")
    private String jyaq;
    @ApiModelProperty("重刑犯")
    private String zxf;
    @ApiModelProperty("从案类型")
    private String caaj;
    @ApiModelProperty("成员类型")
    private String cylx;
    @ApiModelProperty("办案单位")
    private String badw;
    @ApiModelProperty("办案单位类型")
    private String dwlx;
    @ApiModelProperty("办案人")
    private String bar;
    @ApiModelProperty("办案人警号")
    private String barjh;
    @ApiModelProperty("办案环节")
    private String bahj;
    @ApiModelProperty("办案民警电话")
    private String bardh;
    @ApiModelProperty("办案人固定电话")
    private String bargddh;
    @ApiModelProperty("暂住起始日期")
    private LocalDateTime zzqsrq;
    @ApiModelProperty("拘留日期")
    private LocalDateTime jlrq;
    @ApiModelProperty("逮捕日期")
    private LocalDateTime dbrq;
    @ApiModelProperty("实有人口ID")
    private String syrkid;
    @ApiModelProperty("安全等级")
    private String aqdj;
    @ApiModelProperty("是否允许家属联系")
    private String sfyxjslx;
    @ApiModelProperty("联系电话")
    private String lxdh;
    @ApiModelProperty("涉毒尿检初查结果")
    private String sdnjccjg;
    @ApiModelProperty("涉毒尿检单位")
    private String sdnjdw;
    @ApiModelProperty("涉毒尿检初检时间")
    private LocalDateTime sdnjcjsj;
    @ApiModelProperty("涉毒尿检检查人")
    private String sdnjjcr;
    @ApiModelProperty("是否戴眼镜")
    private String sfyj;
    @ApiModelProperty("指纹编号")
    private String zwbh;
    @ApiModelProperty("业务流程ID")
    private String ywlcid;
    @ApiModelProperty("送押人电话")
    private String syrdh;
    @ApiModelProperty("送押人手机")
    private String syrsj;
    @ApiModelProperty("同案编号")
    private String tabh;
    @ApiModelProperty("收回原因")
    private String shyy;
    @ApiModelProperty("收回日期")
    private LocalDateTime shrq;
    @ApiModelProperty("收押法律文书号")
    private String flwsh;
    @ApiModelProperty("监室号")
    private String jsh;
    @ApiModelProperty("检查医生")
    private String jcys;
    @ApiModelProperty("限制会见案件")
    private String xzhjaj;
    @ApiModelProperty("类别")
    private String lb;
    @ApiModelProperty("审批单位")
    private String spdw;
    @ApiModelProperty("审批人")
    private String spr;
    @ApiModelProperty("管理类别")
    private String gllb;
    @ApiModelProperty("职务")
    private String zw;
    @ApiModelProperty("附物编号")
    private String fwbh;
    @ApiModelProperty("暂不收押操作人")
    private String czr;
    @ApiModelProperty("暂不收押操作时间")
    private LocalDateTime czsj;
    @ApiModelProperty("状态")
    private String state;
    @ApiModelProperty("备注")
    private String bz;
    @ApiModelProperty("拒收理由")
    private String jsly;
    @ApiModelProperty("创建人")
    private String creator;
    @ApiModelProperty("创建人警号")
    private String creatorjh;
    @ApiModelProperty("更新人")
    private String updator;
    @ApiModelProperty("限制会见审批单位")
    private String xzhjspdw;
    @ApiModelProperty("限制会见审批人")
    private String xzhjspr;
    @ApiModelProperty("案件性质审批单位")
    private String ajxzspdw;
    @ApiModelProperty("案件性质审批人")
    private String ajxzspr;
    @ApiModelProperty("案件性质")
    private String ajxz;
    @ApiModelProperty("刑期起始日期")
    private LocalDateTime xqqsrq;
    @ApiModelProperty("缓刑起始日期")
    private LocalDateTime hxqsrq;
    @ApiModelProperty("刑期结束日期")
    private LocalDateTime xqjsrq;
    @ApiModelProperty("异地羁押")
    private String ydjy;
    @ApiModelProperty("操作状态")
    private String czzt;
    @ApiModelProperty("创建日期")
    private LocalDateTime createtime;
    @ApiModelProperty("更新日期")
    private LocalDateTime updatetime;


}
