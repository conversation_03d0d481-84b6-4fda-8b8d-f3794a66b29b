package com.rs.module.integ.dao.data;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.integ.entity.data.ShareJqDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.integ.controller.admin.data.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 数据共享-监区 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ShareJqDao extends IBaseDao<ShareJqDO> {


    default PageResult<ShareJqDO> selectPage(ShareJqPageReqVO reqVO) {
        Page<ShareJqDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ShareJqDO> wrapper = new LambdaQueryWrapperX<ShareJqDO>()
            .eqIfPresent(ShareJqDO::getSysId, reqVO.getSysId())
            .eqIfPresent(ShareJqDO::getJqh, reqVO.getJqh())
            .eqIfPresent(ShareJqDO::getJqmc, reqVO.getJqmc())
            .eqIfPresent(ShareJqDO::getJqicons, reqVO.getJqicons())
            .eqIfPresent(ShareJqDO::getState, reqVO.getState())
            .eqIfPresent(ShareJqDO::getJsbh, reqVO.getJsbh())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(ShareJqDO::getAddTime);
        }
        Page<ShareJqDO> shareJqPage = selectPage(page, wrapper);
        return new PageResult<>(shareJqPage.getRecords(), shareJqPage.getTotal());
    }
    default List<ShareJqDO> selectList(ShareJqListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ShareJqDO>()
            .eqIfPresent(ShareJqDO::getSysId, reqVO.getSysId())
            .eqIfPresent(ShareJqDO::getJqh, reqVO.getJqh())
            .eqIfPresent(ShareJqDO::getJqmc, reqVO.getJqmc())
            .eqIfPresent(ShareJqDO::getJqicons, reqVO.getJqicons())
            .eqIfPresent(ShareJqDO::getState, reqVO.getState())
            .eqIfPresent(ShareJqDO::getJsbh, reqVO.getJsbh())
        .orderByDesc(ShareJqDO::getAddTime));    }


    }
