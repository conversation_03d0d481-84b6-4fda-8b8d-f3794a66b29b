package com.rs.module.integ.controller;

import com.rs.module.integ.domain.entity.AcpPmPrisonerJdsIn;
import com.rs.module.integ.service.AcpPmPrisonerJdsInService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

        import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/prisoners")
public class AcpPmPrisonerJdsInController {

    private final AcpPmPrisonerJdsInService service;

    @Autowired
    public AcpPmPrisonerJdsInController(AcpPmPrisonerJdsInService service) {
        this.service = service;
    }

    @PostMapping
    public AcpPmPrisonerJdsIn create(@RequestBody AcpPmPrisonerJdsIn prisoner) {
        return service.save(prisoner);
    }

    @GetMapping
    public List<AcpPmPrisonerJdsIn> getAll() {
        return service.findAll();
    }

    @GetMapping("/{id}")
    public Optional<AcpPmPrisonerJdsIn> getById(@PathVariable String id) {
        return service.findById(id);
    }

    @PutMapping("/{id}")
    public AcpPmPrisonerJdsIn update(@PathVariable String id, @RequestBody AcpPmPrisonerJdsIn prisoner) {
        prisoner.setId(id); // 确保 ID 被设置
        return service.update(prisoner);
    }

    @DeleteMapping("/{id}")
    public void delete(@PathVariable String id) {
        service.deleteById(id);
    }

    @GetMapping("/rybh/{rybh}")
    public List<AcpPmPrisonerJdsIn> getByRybh(@PathVariable String rybh) {
        return service.findByRybh(rybh);
    }
}
