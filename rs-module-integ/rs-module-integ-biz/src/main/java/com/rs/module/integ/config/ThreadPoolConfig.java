package com.rs.module.integ.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class ThreadPoolConfig {

    /**
     * 公用线程池bean名称
     */
    public static final String COMMON_THREAD_POOL_BEAN_NAME = "commonThreadPool";

    private static final Integer CORE_POOL_SIZE = Runtime.getRuntime().availableProcessors() * 2;


    @Bean(COMMON_THREAD_POOL_BEAN_NAME)
    public ExecutorService executorService() {
        return new ThreadPoolExecutor(CORE_POOL_SIZE, CORE_POOL_SIZE, 0L,
                TimeUnit.SECONDS, new LinkedBlockingQueue<>(1000));
    }

}