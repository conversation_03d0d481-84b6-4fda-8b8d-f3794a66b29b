package com.rs.module.integ.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.rs.module.integ.entity.data.PrisonerKssRawInDO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName KssInRawDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/7/30 15:17
 * @Version 1.0
 */
@NoArgsConstructor
@Data
public class KssInRawDTO extends BaseDataShareRespDTO {

    @JsonProperty("data")
    private KssInRawDTO.DataDTO data;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JsonProperty("jbxx")
        private List<PrisonerKssRawInDO> jbxx;

    }

}
