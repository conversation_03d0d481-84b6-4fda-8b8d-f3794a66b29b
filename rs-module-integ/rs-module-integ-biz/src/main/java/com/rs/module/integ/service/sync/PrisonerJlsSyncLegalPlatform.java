package com.rs.module.integ.service.sync;

import com.rs.module.integ.constant.LegalPlatformDataShareTypeConstants;
import com.rs.module.integ.constant.LegalPlatformDataStateConstant;
import com.rs.module.integ.constant.LegalPlatformFieldAndSortConstant;
import com.rs.module.integ.constant.OrderConstant;
import com.rs.module.integ.domain.po.PdmsPrisonerJls;
import com.rs.module.integ.domain.request.DataShareRequest;
import com.rs.module.integ.domain.response.LegalPlatformBaseResponse;
import com.rs.module.integ.domain.response.LegalPlatformJlsDataShareResponse;
import com.rs.module.integ.domain.response.PrisonerJlsResponse;
import com.rs.module.integ.service.DataShareService;
import com.rs.module.integ.service.PdmsPrisonerJlsService;
import com.rs.module.integ.util.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PrisonerJlsSyncLegalPlatform extends AbstractSyncLegalPlatform<PdmsPrisonerJls> {

    private PdmsPrisonerJlsService pdmsPrisonerJlsService;

//    private final PdmsPrisonerPhotosService pdmsPrisonerPhotosService;

    @Autowired
    private DataShareService dataShareService;

    @Override
    protected List<PdmsPrisonerJls> getCreateDataListPage(LocalDateTime startCreateTime, long page, long pageSize) {
        LegalPlatformBaseResponse<LegalPlatformJlsDataShareResponse> legalPlatformBaseResponse =
                dataShareService.getJlsData(DataShareRequest.builder()
                        .createStartTime(DateUtil.toDate(startCreateTime))
                        .types(LegalPlatformDataShareTypeConstants.jbxx)
                        .state(LegalPlatformDataStateConstant.PRISONER_IN)
                        .pageNum((int) page)
                        .pageSize((int) pageSize)
                        .orderField(LegalPlatformFieldAndSortConstant.CREATE_TIME)
                        .seType(OrderConstant.ASC)
                        .build());
        List<PrisonerJlsResponse> prisonerJlsResponses = legalPlatformBaseResponse.getData().getJbxx();
        return this.convertBatchToPdmsData(prisonerJlsResponses);
    }

    private List<PdmsPrisonerJls> convertBatchToPdmsData(List<PrisonerJlsResponse> prisonerJlsResponses) {
        return Optional.ofNullable(prisonerJlsResponses)
                .orElse(Collections.emptyList())
                .stream()
                .map(this::convertToPdmsData)
                .collect(Collectors.toList());
    }


    @Override
    protected List<PdmsPrisonerJls> getUpdateDataListPage(LocalDateTime startUpdateTime, long page, long pageSize) {
        LegalPlatformBaseResponse<LegalPlatformJlsDataShareResponse> legalPlatformBaseResponse =
                dataShareService.getJlsData(DataShareRequest.builder()
                        .updateStartTime(DateUtil.toDate(startUpdateTime))
                        .types(LegalPlatformDataShareTypeConstants.jbxx)
                        .pageNum((int) page)
                        .pageSize((int) pageSize)
                        .orderField(LegalPlatformFieldAndSortConstant.UPDATE_TIME)
                        .seType(OrderConstant.ASC)
                        .build());
        List<PrisonerJlsResponse> prisonerJlsResponses = legalPlatformBaseResponse.getData().getJbxx();
        return this.convertBatchToPdmsData(prisonerJlsResponses);
    }

    @Override
    protected LocalDateTime handleDataAndReturnMaxCreateTime(List<PdmsPrisonerJls> result) {
        LocalDateTime newLastCreateTime = this.compareMaxTime(result, this.getLastCreateTime(), PdmsPrisonerJls::getCreatetime);
        this.setPhotoField(result);
        pdmsPrisonerJlsService.handleNewData(result);
        return newLastCreateTime;
    }

    /**
     * 设置照片字段
     */
    private void setPhotoField(List<PdmsPrisonerJls> result) {
//        List<String> rybhList = result.stream()
//                .map(PdmsPrisonerJls::getRybh)
//                .collect(Collectors.toList());
//        List<PdmsPrisonerPhotos> photos = pdmsPrisonerPhotosService.getByUserPhotos(rybhList,
//                PrisonDataSourceConstant.JLS);
//        Map<String, Map<String, PdmsPrisonerPhotos>> prisonerMap = PdmsPrisonerPhotosService.getPrisonerMap(photos);
//        for (PdmsPrisonerJls pdmsPrisonerJls : result) {
//            pdmsPrisonerJls.setLeftPhoto(PdmsPrisonerPhotosService.fromMapGetPhotoUrl(prisonerMap,
//                    pdmsPrisonerJls.getRybh(), LegalPlatformPrisonerPhotosConstants.SIDE_PHOTO_LEFT));
//            pdmsPrisonerJls.setRightPhoto(PdmsPrisonerPhotosService.fromMapGetPhotoUrl(prisonerMap,
//                    pdmsPrisonerJls.getRybh(), LegalPlatformPrisonerPhotosConstants.SIDE_PHOTO_RIGHT));
//            pdmsPrisonerJls.setFrontPhoto(PdmsPrisonerPhotosService.fromMapGetPhotoUrl(prisonerMap,
//                    pdmsPrisonerJls.getRybh(), LegalPlatformPrisonerPhotosConstants.FACE_PHOTO));
//        }
    }


    @Override
    protected LocalDateTime handleDataAndReturnMaxUpdateTime(List<PdmsPrisonerJls> result) {
        LocalDateTime newLastUpdateTime = this.compareMaxTime(result, this.getLastUpdateTime(),
                PdmsPrisonerJls::getUpdatetime);
        this.setPhotoField(result);
        pdmsPrisonerJlsService.handleUpdateData(result);
        return newLastUpdateTime;
    }

    @Override
    protected LocalDateTime getTableLastCreateTime() {
        return pdmsPrisonerJlsService.getTableLastCreateTime();
    }

    @Override
    protected boolean hasData() {
        return false;
    }

    @Override
    protected LocalDateTime getTableLastUpdateTime() {
        return pdmsPrisonerJlsService.getTableLastUpdateTime();
    }

    // --------------------------------------------------------------------------------------------------------------
    private PdmsPrisonerJls convertToPdmsData(PrisonerJlsResponse prisonerJlsResponse) {
        return PdmsPrisonerJls.builder()
                .id(prisonerJlsResponse.getId())
                .jsbh(prisonerJlsResponse.getJsbh())
                .rybh(prisonerJlsResponse.getRybh())
                .tbr(prisonerJlsResponse.getTbr())
                .tbrq(prisonerJlsResponse.getTbrq())
                .wbrybh(prisonerJlsResponse.getWbrybh())
                .gcbh(prisonerJlsResponse.getGcbh())
                .ywlcid(prisonerJlsResponse.getYwlcid())
                .taskid(prisonerJlsResponse.getTaskid())
                .snbh(prisonerJlsResponse.getSnbh())
                .jsh(prisonerJlsResponse.getJsh())
                .xm(prisonerJlsResponse.getXm())
                .xmpy(prisonerJlsResponse.getXmpy())
                .xmpyszm(prisonerJlsResponse.getXmpyszm())
                .bm(prisonerJlsResponse.getBm())
                .bmty(prisonerJlsResponse.getBmty())
                .xb(prisonerJlsResponse.getXb())
                .rsrq(prisonerJlsResponse.getRsrq())
                .zjlx(prisonerJlsResponse.getZjlx())
                .zjh(prisonerJlsResponse.getZjh())
                .gj(prisonerJlsResponse.getGj())
                .whcd(prisonerJlsResponse.getWhcd())
                .sf(prisonerJlsResponse.getSf())
                .tssf(prisonerJlsResponse.getTssf())
                .mz(prisonerJlsResponse.getMz())
                .zzmm(prisonerJlsResponse.getZzmm())
                .csrq(prisonerJlsResponse.getCsrq())
                .hyzk(prisonerJlsResponse.getHyzk())
                .hjd(prisonerJlsResponse.getHjd())
                .hjdxz(prisonerJlsResponse.getHjdxz())
                .jg(prisonerJlsResponse.getJg())
                .xzd(prisonerJlsResponse.getXzd())
                .xzdxz(prisonerJlsResponse.getXzdxz())
                .gzdw(prisonerJlsResponse.getGzdw())
                .zy(prisonerJlsResponse.getZy())
                .jljdjg(prisonerJlsResponse.getJljdjg())
                .badw(prisonerJlsResponse.getBadw())
                .bar(prisonerJlsResponse.getBar())
                .rsxz(prisonerJlsResponse.getRsxz())
                .syr(prisonerJlsResponse.getSyr())
                .sypz(prisonerJlsResponse.getSypz())
                .sypzwsh(prisonerJlsResponse.getSypzwsh())
                .ay(prisonerJlsResponse.getAy())
                .barjh(prisonerJlsResponse.getBarjh())
                .bardh(prisonerJlsResponse.getBardh())
                .czdh(prisonerJlsResponse.getCzdh())
                .gyts(prisonerJlsResponse.getGyts())
                // 和看守所统一时间类型
                .jlrq(DateUtil.toLocalDateTime(prisonerJlsResponse.getJlrq()))
                .gyqx(DateUtil.toLocalDateTime(prisonerJlsResponse.getGyqx()))
                .sdnjccjg(prisonerJlsResponse.getSdnjccjg())
                .sdnjdw(prisonerJlsResponse.getSdnjdw())
                .sdnjcjsj(prisonerJlsResponse.getSdnjcjsj())
                .sdnjjcr(prisonerJlsResponse.getSdnjjcr())
                .jyaq(prisonerJlsResponse.getJyaq())
                .dah(prisonerJlsResponse.getDah())
                .hzflwsh(prisonerJlsResponse.getHzflwsh())
                .pzrybh(prisonerJlsResponse.getPzrybh())
                .pzajbh(prisonerJlsResponse.getPzajbh())
                .pzflwsh(prisonerJlsResponse.getPzflwsh())
                .czzt(prisonerJlsResponse.getCzzt())
                .jsly(prisonerJlsResponse.getJsly())
                .byjswsh(prisonerJlsResponse.getByjswsh())
                .jsrq(prisonerJlsResponse.getJsrq())
                .zxf(prisonerJlsResponse.getZxf())
                .aqdj(prisonerJlsResponse.getAqdj())
                .rygllb(prisonerJlsResponse.getRygllb())
                .lscsyy(prisonerJlsResponse.getLscsyy())
                .lscssj(prisonerJlsResponse.getLscssj())
                .lscshssj(prisonerJlsResponse.getLscshssj())
                .crjbj(prisonerJlsResponse.getCrjbj())
                .rkbhgbj(prisonerJlsResponse.getRkbhgbj())
                .rkbhgyy(prisonerJlsResponse.getRkbhgyy())
                .lrsfjs(prisonerJlsResponse.getLrsfjs())
                .cssj(prisonerJlsResponse.getCssj())
                .csyy(prisonerJlsResponse.getCsyy())
                .state(prisonerJlsResponse.getState())
                .scbz(prisonerJlsResponse.getScbz())
                .dwlx(prisonerJlsResponse.getDwlx())
                .wfdd(prisonerJlsResponse.getWfdd())
                .sydw(prisonerJlsResponse.getSydw())
                .creator(prisonerJlsResponse.getCreator())
                .createtime(prisonerJlsResponse.getCreatetime())
                .updator(prisonerJlsResponse.getUpdator())
                .updatetime(prisonerJlsResponse.getUpdatetime())
                .lxdh(prisonerJlsResponse.getLxdh())
                .hjdszpcs(prisonerJlsResponse.getHjdszpcs())
                .tmtbtz(prisonerJlsResponse.getTmtbtz())
                .dwdz(prisonerJlsResponse.getDwdz())
                .bjlrjl(prisonerJlsResponse.getBjlrjl())
                .bjlrjscfqk(prisonerJlsResponse.getBjlrjscfqk())
                .wfsj(prisonerJlsResponse.getWfsj())
                .fxdj(prisonerJlsResponse.getFxdj())
                .je(prisonerJlsResponse.getJe())
                .bz(prisonerJlsResponse.getBz())
                .shid(prisonerJlsResponse.getShid())
                .em(prisonerJlsResponse.getEm())
                .emlx(prisonerJlsResponse.getEmlx())
                .sffy(prisonerJlsResponse.getSffy())
                .fyksrq(prisonerJlsResponse.getFyksrq())
                .fyjsrq(prisonerJlsResponse.getFyjsrq())
                .sfcrb(prisonerJlsResponse.getSfcrb())
                .jsycbz(prisonerJlsResponse.getJsycbz())
                .lkdj(prisonerJlsResponse.getLkdj())
                .sfcxcy(prisonerJlsResponse.getSfcxcy())
                .jkzk(prisonerJlsResponse.getJkzk())
                .tabh(prisonerJlsResponse.getTabh())
                .kgry(prisonerJlsResponse.getKgry())
                .jzcs(prisonerJlsResponse.getJzcs())
                .sfjqz(prisonerJlsResponse.getSfjqz())
                .hsjccs(prisonerJlsResponse.getHsjccs())
                .hsjcjg(prisonerJlsResponse.getHsjcjg())
                .build();
    }
}
