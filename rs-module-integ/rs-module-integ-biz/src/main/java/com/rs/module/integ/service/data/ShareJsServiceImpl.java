package com.rs.module.integ.service.data;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.integ.controller.admin.data.vo.ShareJsListReqVO;
import com.rs.module.integ.controller.admin.data.vo.ShareJsPageReqVO;
import com.rs.module.integ.controller.admin.data.vo.ShareJsSaveReqVO;
import com.rs.module.integ.dao.data.ShareJsDao;
import com.rs.module.integ.entity.data.ShareJsDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 数据共享-监室 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ShareJsServiceImpl extends BaseServiceImpl<ShareJsDao, ShareJsDO> implements ShareJsService {

    @Resource
    private ShareJsDao shareJsDao;

    @Override
    public String createShareJs(ShareJsSaveReqVO createReqVO) {
        // 插入
        ShareJsDO shareJs = BeanUtils.toBean(createReqVO, ShareJsDO.class);
        shareJsDao.insert(shareJs);
        // 返回
        return shareJs.getId();
    }

    @Override
    public void updateShareJs(ShareJsSaveReqVO updateReqVO) {
        // 校验存在
        validateShareJsExists(updateReqVO.getId());
        // 更新
        ShareJsDO updateObj = BeanUtils.toBean(updateReqVO, ShareJsDO.class);
        shareJsDao.updateById(updateObj);
    }

    @Override
    public void deleteShareJs(String id) {
        // 校验存在
        validateShareJsExists(id);
        // 删除
        shareJsDao.deleteById(id);
    }

    private void validateShareJsExists(String id) {
        if (shareJsDao.selectById(id) == null) {
            throw new ServerException("数据共享-监室数据不存在");
        }
    }

    @Override
    public ShareJsDO getShareJs(String id) {
        return shareJsDao.selectById(id);
    }

    @Override
    public PageResult<ShareJsDO> getShareJsPage(ShareJsPageReqVO pageReqVO) {
        return shareJsDao.selectPage(pageReqVO);
    }

    @Override
    public List<ShareJsDO> getShareJsList(ShareJsListReqVO listReqVO) {
        return shareJsDao.selectList(listReqVO);
    }


}
