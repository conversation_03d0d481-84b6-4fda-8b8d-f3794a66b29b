package com.rs.module.integ;

import java.net.UnknownHostException;

import org.dromara.x.file.storage.spring.EnableFileStorage;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.mzt.bsp.logapi.starter.annotation.EnableLogRecord;
import com.rs.framework.common.util.spring.SpringUtils;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 系统集成启动类
 * <AUTHOR>
 * @date 2025年3月17日
 */
@SpringBootApplication(exclude = {DruidDataSourceAutoConfigure.class, MongoAutoConfiguration.class, MongoDataAutoConfiguration.class})
@ComponentScan(value = {"com.rs.*", "com.bsp.*"})
@EnableLogRecord(systemMark = "integ")
@EnableScheduling
@EnableFileStorage
public class IntegServerApplication {

	public static void main(String[] args) throws UnknownHostException {
		ConfigurableApplicationContext application = SpringApplication.run(IntegServerApplication.class, args);
		SpringUtils.printStartLog(application);
	}
}
