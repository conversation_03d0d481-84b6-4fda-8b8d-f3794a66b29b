package com.rs.module.integ.service;

import com.rs.module.integ.domain.request.DataShareRequest;
import com.rs.module.integ.domain.request.PreInRequest;
import com.rs.module.integ.domain.response.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
public class HttpClientImpl {

    @Value("${legal-platform-service.data-share.url:http://14.66.127.82:3254}")
    private String baseUrl;

    private final RestTemplate restTemplate;
    private AtomicInteger logCounter = new AtomicInteger(0); // 使用 AtomicInteger 保证线程安全

    public HttpClientImpl(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public LegalPlatformBaseResponse<LegalPlatformKssDataShareResponse> getKssData(DataShareRequest request) {
        try {
            String url = baseUrl + "/dataShare/data";
            log.info("开始调用数据共享接口，URL: {}, 请求参数: {}", url, request);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<DataShareRequest> entity = new HttpEntity<>(request, headers);

            // 使用 ResponseEntity 来获取完整的响应
            ResponseEntity<LegalPlatformBaseResponse<LegalPlatformKssDataShareResponse>> responseEntity =
                    restTemplate.exchange(url, HttpMethod.POST, entity,
                            new ParameterizedTypeReference<LegalPlatformBaseResponse<LegalPlatformKssDataShareResponse>>() {});

            LegalPlatformBaseResponse<LegalPlatformKssDataShareResponse> response = responseEntity.getBody();
            try {
                log.info("数据共享接口调用成功，响应结果: {}",response.getData().getJbxx().size() );
            } catch (Exception e) {

            }
            return response;
        } catch (Exception e) {
            log.error("数据共享接口调用失败", e);
            throw new RuntimeException("数据共享接口调用失败: " + e.getMessage());
        }
    }

    public LegalPlatformBaseResponse<LegalPlatformJlsDataShareResponse> getJlsData(DataShareRequest request) {
        try {
            String url = baseUrl + "/dataShare/jlsData";
            log.info("开始调用数据共享接口，URL: {}, 请求参数: {}", url, request);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<DataShareRequest> entity = new HttpEntity<>(request, headers);

            // 使用 ResponseEntity 来获取完整的响应
            ResponseEntity<LegalPlatformBaseResponse<LegalPlatformJlsDataShareResponse>> responseEntity =
                    restTemplate.exchange(url, HttpMethod.POST, entity,
                            new ParameterizedTypeReference<LegalPlatformBaseResponse<LegalPlatformJlsDataShareResponse>>() {});

            LegalPlatformBaseResponse<LegalPlatformJlsDataShareResponse> response = responseEntity.getBody();
            // 增加计数器
            int currentCount = logCounter.incrementAndGet();
            if (currentCount % 200 == 0) {
                log.info("数据共享接口调用成功，响应结果: {}", response);
            }
            return response;
        } catch (Exception e) {
            log.error("数据共享接口调用失败", e);
            throw new RuntimeException("数据共享接口调用失败: " + e.getMessage());
        }
    }

    public LegalPlatformPreInDataShareResponse<PrisonerPreInResponse> getPreInKssData(PreInRequest request) {
        try {
            String url = "http://14.66.127.82:8099/business/api/xyr/list";
            log.info("开始调用数据共享接口，URL: {}, 请求参数: {}", url, request);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("X-API-Version", "V1");
            headers.set("APP-Client", "shizhanpingtai");

            HttpEntity<PreInRequest> entity = new HttpEntity<>(request, headers);

            // 使用 ResponseEntity 来获取完整的响应
            ResponseEntity<LegalPlatformPreInDataShareResponse<PrisonerPreInResponse>> responseEntity =
                    restTemplate.exchange(url, HttpMethod.POST, entity,
                            new ParameterizedTypeReference<LegalPlatformPreInDataShareResponse<PrisonerPreInResponse>>() {});

            LegalPlatformPreInDataShareResponse<PrisonerPreInResponse> response = responseEntity.getBody();

            // 增加计数器
            int currentCount = logCounter.incrementAndGet();
            if (currentCount % 200 == 0) {
                log.info("数据共享接口调用成功，响应结果: {}", response);
            }
            return response;
        } catch (Exception e) {
            log.error("数据共享接口调用失败", e);
            throw new RuntimeException("数据共享接口调用失败: " + e.getMessage());
        }
    }

    public LegalPlatformPreInDataShareResponse<PrisonerJlsPreInResponse> getPreInJlsData(PreInRequest request) {
        try {
            String url = "http://14.66.127.82:8099/business/api/xyrJls/list";
            log.info("开始调用数据共享接口，URL: {}, 请求参数: {}", url, request);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("X-API-Version", "V1");
            headers.set("APP-Client", "shizhanpingtai");

            HttpEntity<PreInRequest> entity = new HttpEntity<>(request, headers);

            // 使用 ResponseEntity 来获取完整的响应
            ResponseEntity<LegalPlatformPreInDataShareResponse<PrisonerJlsPreInResponse>> responseEntity =
                    restTemplate.exchange(url, HttpMethod.POST, entity,
                            new ParameterizedTypeReference<LegalPlatformPreInDataShareResponse<PrisonerJlsPreInResponse>>() {});

            LegalPlatformPreInDataShareResponse<PrisonerJlsPreInResponse> response = responseEntity.getBody();

            // 增加计数器
            int currentCount = logCounter.incrementAndGet();
            if (currentCount % 200 == 0) {
                log.info("数据共享接口调用成功，响应结果: {}", response);
            }
            return response;
        } catch (Exception e) {
            log.error("数据共享接口调用失败", e);
            throw new RuntimeException("数据共享接口调用失败: " + e.getMessage());
        }
    }

    public LegalPlatformPreInDataShareResponse<PrisonerJdsPreInResponse> getPreInJdsData(PreInRequest request) {
        try {
            String url = "http://14.66.127.82:8099/business/api/xyrJds/list";
            log.info("开始调用数据共享接口，URL: {}, 请求参数: {}", url, request);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("X-API-Version", "V1");
            headers.set("APP-Client", "shizhanpingtai");

            HttpEntity<PreInRequest> entity = new HttpEntity<>(request, headers);

            // 使用 ResponseEntity 来获取完整的响应
            ResponseEntity<LegalPlatformPreInDataShareResponse<PrisonerJdsPreInResponse>> responseEntity =
                    restTemplate.exchange(url, HttpMethod.POST, entity,
                            new ParameterizedTypeReference<LegalPlatformPreInDataShareResponse<PrisonerJdsPreInResponse>>() {});

            LegalPlatformPreInDataShareResponse<PrisonerJdsPreInResponse> response = responseEntity.getBody();

            // 增加计数器
            int currentCount = logCounter.incrementAndGet();
            if (currentCount % 200 == 0) {
                log.info("数据共享接口调用成功，响应结果: {}", response);
            }
            return response;
        } catch (Exception e) {
            log.error("数据共享接口调用失败", e);
            throw new RuntimeException("数据共享接口调用失败: " + e.getMessage());
        }
    }
}
