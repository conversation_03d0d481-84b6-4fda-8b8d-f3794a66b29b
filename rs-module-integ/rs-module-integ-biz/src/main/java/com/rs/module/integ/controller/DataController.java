package com.rs.module.integ.controller;


import com.rs.module.integ.service.AcpPmPrisonerKssOutService;
import com.rs.module.integ.service.KssJqxxService;
import com.rs.module.integ.service.KssRawInService;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/data")
public class DataController {


    @Autowired
    private AcpPmPrisonerKssOutService acpPmPrisonerKssOutService;

    @Autowired
    private KssJqxxService kssJqxxService;

    @Autowired
    private KssRawInService kssRawInService;

    /**
     * 同步出所人员数据
     */
    @GetMapping("syncData")
    @ApiModelProperty("同步出所人员数据，state:R3-删除，R6-缓收，R7-历史，R9-拒收，R8-在押")
    public void syncData(String state) {
        if (null == state || state.equalsIgnoreCase("") || state.equalsIgnoreCase("R8")) {
            return;
        }
        log.info("###开始手动同步出所人员数据");
        acpPmPrisonerKssOutService.setState(state);
        acpPmPrisonerKssOutService.incrementSync();
        log.info("###手动同步出所人员数据结束");
    }

    /**
     * 同步监区信息人员数据
     */
    @GetMapping("syncJqData")
    public void syncJqData() {
        kssJqxxService.incrementSync();
    }
    @GetMapping("syncKssRawData")
    public void syncKssRawData() {
        kssRawInService.incrementSync();
    }


}
