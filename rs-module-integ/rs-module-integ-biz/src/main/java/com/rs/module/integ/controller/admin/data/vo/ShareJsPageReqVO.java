package com.rs.module.integ.controller.admin.data.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 数据共享-监室分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ShareJsPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("第三方id")
    private String sysId;

    @ApiModelProperty("监室号")
    private String jsh;

    @ApiModelProperty("监室列别")
    private String jslb;

    @ApiModelProperty("监室名称")
    private String jsmc;

    @ApiModelProperty("监区号")
    private String jqh;

    @ApiModelProperty("状态")
    private String state;

    @ApiModelProperty("协管民警")
    private String xgmjjh;

    @ApiModelProperty("主管民警")
    private String zgmjjh;

    @ApiModelProperty("监所编号")
    private String jsbh;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
