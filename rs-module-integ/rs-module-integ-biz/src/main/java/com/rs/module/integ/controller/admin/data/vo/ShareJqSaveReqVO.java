package com.rs.module.integ.controller.admin.data.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 数据共享-监区新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ShareJqSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("第三方id")
    private String sysId;

    @ApiModelProperty("监区号")
    private String jqh;

    @ApiModelProperty("监区名称")
    private String jqmc;

    @ApiModelProperty("jqicons")
    private String jqicons;

    @ApiModelProperty("状态")
    private String state;

    @ApiModelProperty("监所编号")
    private String jsbh;

}
