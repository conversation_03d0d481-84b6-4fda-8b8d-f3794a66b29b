package com.rs.module.integ.service;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.integ.constant.*;
import com.rs.module.integ.dao.AcpPmPrisonerKssInRepository;
import com.rs.module.integ.dao.AcpPmPrisonerPhotosRepository;
import com.rs.module.integ.domain.entity.AcpPmPrisonerKssIn;
import com.rs.module.integ.domain.entity.AcpPmPrisonerKssOut;
import com.rs.module.integ.domain.entity.AcpPmPrisonerPhotos;
import com.rs.module.integ.domain.entity.VwAcpPmPrisonerKss;
import com.rs.module.integ.domain.request.DataShareRequest;
import com.rs.module.integ.domain.response.LegalPlatformBaseResponse;
import com.rs.module.integ.domain.response.LegalPlatformKssDataShareResponse;
import com.rs.module.integ.domain.response.PrisonerKssResponse;
import com.rs.module.integ.service.sync.AbstractSyncLegalPlatform;
import com.rs.module.integ.util.DateUtil;
import com.rs.module.integ.util.NameKeywordGenerator;
import com.rs.module.integ.util.OrgNameCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AcpPmPrisonerKssInService extends AbstractSyncLegalPlatform<VwAcpPmPrisonerKss> {

    private final AcpPmPrisonerKssInRepository repository;

    private final AcpPmPrisonerPhotosRepository acpPmPrisonerPhotosRepository;

    private final AcpPmPrisonerKssOutService acpPmPrisonerKssOutService;

    @Autowired
    private HttpClientImpl httpClient;

    @Resource
    private BspApi bspApi;

    private final OrgNameCache orgNameCache = new OrgNameCache();

    @Value("${prison-prisoner-id-generator}")
    private String id_generator;

    /**
     * 查询数据库中最大的创建时间
     *
     * @return
     */
    protected LocalDateTime getTableLastCreateTime() {
        //从数据库查询该表所有数据的最大add_time
        Date maxAddTime = repository.findMaxAddTime();
        // 如果 maxAddTime 为 null，返回默认值 2010年1月1日0点0分0秒
        if (maxAddTime == null) {
            return LocalDateTime.of(2010, 1, 1, 0, 0, 0);
        }
        // 将 Date 转换为 LocalDateTime
        return maxAddTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

    }

    @Override
    protected boolean hasData() {
        return repository.count() > 0 ? true : false;
    }

    /**
     * 查询数据库中最大的更新时间
     *
     * @return
     */
    protected LocalDateTime getTableLastUpdateTime() {
        //从数据库查询该表所有数据的最大update_time
        Date maxUpdateTime = repository.findMaxUpdateTime();
        // 如果 maxAddTime 为 null，返回默认值 2010年1月1日0点0分0秒
        if (maxUpdateTime == null) {
            return LocalDateTime.of(2010, 1, 1, 0, 0, 0);
        }
        // 将 Date 转换为 LocalDateTime
        return maxUpdateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }


    protected LocalDateTime handleDataAndReturnMaxCreateTime(List<VwAcpPmPrisonerKss> result) {
        LocalDateTime maxCreateTime = result.stream()
                .map(VwAcpPmPrisonerKss::getAddTime) // Assuming getAddTime returns Date
                .filter(Objects::nonNull)
                .map(date -> date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()) // Convert Date to LocalDateTime
                .max(LocalDateTime::compareTo) // Now comparing LocalDateTime
                .orElse(null);
        this.setPhotoField(result);
        //将result保存到数据库中
        this.handleNewData(result);

        return maxCreateTime;
    }

    private void handleNewData(List<VwAcpPmPrisonerKss> result) {
        // 将 List<VwAcpPmPrisonerKss> 转换为 List<AcpPmPrisonerKssIn>
        List<AcpPmPrisonerKssIn> prisonerKssInList = Optional.ofNullable(result)
                .orElse(Collections.emptyList())
                .stream()
                .map(vwAcpPmPrisonerKss -> {
                    AcpPmPrisonerKssIn pdmsPrisonerKssIn = new AcpPmPrisonerKssIn();
                    BeanUtils.copyProperties(vwAcpPmPrisonerKss, pdmsPrisonerKssIn);
                    return pdmsPrisonerKssIn;
                })
                .collect(Collectors.toList());

        // 保存 List<AcpPmPrisonerKssIn> 到数据库
        prisonerKssInList.forEach(this::save); // 假设 save 方法用于保存到数据库
    }

    private void setPhotoField(List<VwAcpPmPrisonerKss> result) {
        //循环result，根据rybh字段查询AcpPmPrisonerPhotos表，将查询到的数据设置到result的photo字段中
        result.forEach(item -> {
            List<AcpPmPrisonerPhotos> photos = acpPmPrisonerPhotosRepository.findByRybh(item.getRybh());
            for (AcpPmPrisonerPhotos photo : photos) {
                if (photo.getType() != null && photo.getType().equals(LegalPlatformPrisonerPhotosConstants.FACE_PHOTO)) {
                    item.setFrontPhoto(photo.getPhotourl());
                } else if (photo.getType() != null && photo.getType().equals(LegalPlatformPrisonerPhotosConstants.SIDE_PHOTO_LEFT)) {
                    item.setLeftPhoto(photo.getPhotourl());
                } else if (photo.getType() != null && photo.getType().equals(LegalPlatformPrisonerPhotosConstants.SIDE_PHOTO_RIGHT)) {
                    item.setRightPhoto(photo.getPhotourl());
                }

            }
        });
    }

    protected LocalDateTime handleDataAndReturnMaxUpdateTime(List<VwAcpPmPrisonerKss> result) {
        LocalDateTime maxUpdateTime = result.stream()
                .map(VwAcpPmPrisonerKss::getUpdateTime) // Assuming getAddTime returns Date
                .filter(Objects::nonNull)
                .map(date -> date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()) // Convert Date to LocalDateTime
                .max(LocalDateTime::compareTo) // Now comparing LocalDateTime
                .orElse(null);
        this.setPhotoField(result);
        //将result更新到数据库中
        this.handleUpdateData(result);

        return maxUpdateTime;
    }

    private void handleUpdateData(List<VwAcpPmPrisonerKss> result) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        List<String> deleteIds = new ArrayList<>();
        List<AcpPmPrisonerKssIn> updatePdmsPrisonerKssIns = new ArrayList<>();
        List<AcpPmPrisonerKssOut> acpmsPrisonerKssOuts = new ArrayList<>();
        for (VwAcpPmPrisonerKss vwAcpPmPrisonerKss : result) {
            String state = vwAcpPmPrisonerKss.getRyzt();
            String id = vwAcpPmPrisonerKss.getId();
            // 在押
            if (Objects.equals(state, LegalPlatformDataStateConstant.PRISONER_IN)) {
                AcpPmPrisonerKssIn acpPmPrisonerKssIn = new AcpPmPrisonerKssIn();
                BeanUtils.copyProperties(vwAcpPmPrisonerKss, acpPmPrisonerKssIn);
                updatePdmsPrisonerKssIns.add(acpPmPrisonerKssIn);
            }
            // 出所
            if (Objects.equals(state, LegalPlatformDataStateConstant.PRISONER_HISTORY)) {
                AcpPmPrisonerKssOut pdmsPrisonerKssOut = new AcpPmPrisonerKssOut();
                BeanUtils.copyProperties(vwAcpPmPrisonerKss, pdmsPrisonerKssOut);
                acpmsPrisonerKssOuts.add(pdmsPrisonerKssOut);
                deleteIds.add(id);
            }
            // 删除
            if (Objects.equals(state, LegalPlatformDataStateConstant.PRISONER_DELETE)) {
                deleteIds.add(id);
            }
        }
        if (!CollectionUtils.isEmpty(deleteIds)) {
            this.removeBatchByIds(deleteIds);
        }
        updatePdmsPrisonerKssIns.forEach(this::update);
        acpPmPrisonerKssOutService.compareSaveOrUpdateBatch(acpmsPrisonerKssOuts);
    }

    protected List<VwAcpPmPrisonerKss> getCreateDataListPage(LocalDateTime startCreateTime, long page, long pageSize) {
        long start = System.currentTimeMillis();
        LegalPlatformBaseResponse<LegalPlatformKssDataShareResponse> legalPlatformBaseResponse =
                httpClient.getKssData(DataShareRequest.builder()
                        .createStartTime(DateUtil.toDate(startCreateTime))
                        .types(LegalPlatformDataShareTypeConstants.jbxx)
                        .state(LegalPlatformDataStateConstant.PRISONER_IN)
                        .pageNum((int) page)
                        .pageSize((int) pageSize)
                        .orderField(LegalPlatformFieldAndSortConstant.CREATE_TIME)
                        .seType(OrderConstant.ASC)
                        .build());
        long end = System.currentTimeMillis();
        log.info("getCreateDataListPage耗时：{}", end - start);
        List<PrisonerKssResponse> prisonerKssResponses = legalPlatformBaseResponse.getData().getJbxx();
        return this.convertBatchToAcpPmPrisonerKssIn(prisonerKssResponses);
    }


    protected List<VwAcpPmPrisonerKss> getUpdateDataListPage(LocalDateTime startUpdateTime, long page, long pageSize) {
        LegalPlatformBaseResponse<LegalPlatformKssDataShareResponse> legalPlatformBaseResponse =
                httpClient.getKssData(DataShareRequest.builder()
                        .updateStartTime(DateUtil.toDate(startUpdateTime))
                        .types(LegalPlatformDataShareTypeConstants.jbxx)
                        .pageNum((int) page)
                        .pageSize((int) pageSize)
                        .orderField(LegalPlatformFieldAndSortConstant.UPDATE_TIME)
                        .seType(OrderConstant.ASC)
                        .build());
        List<PrisonerKssResponse> prisonerKssResponses = legalPlatformBaseResponse.getData().getJbxx();
        return this.convertBatchToAcpPmPrisonerKssIn(prisonerKssResponses);
    }

    private List<VwAcpPmPrisonerKss> convertBatchToAcpPmPrisonerKssIn(List<PrisonerKssResponse> prisonerKssResponses) {
        return Optional.ofNullable(prisonerKssResponses)
                .orElse(Collections.emptyList())
                .stream()
                .map(this::convertToAcpPmPrisonerKssIn)
                .collect(Collectors.toList());
    }


    private VwAcpPmPrisonerKss convertToAcpPmPrisonerKssIn(PrisonerKssResponse prisonerKssResponse) {
       long startTime = System.currentTimeMillis();
        String orgName = orgNameCache.getOrgName(bspApi, prisonerKssResponse.getJsbh());
        long endTime = System.currentTimeMillis();
        log.info("getOrgName time: {}", endTime - startTime);
        AreaDO areaDO = getJsh(prisonerKssResponse.getJsbh(), prisonerKssResponse.getJsh());
        return VwAcpPmPrisonerKss.builder()
                .id(prisonerKssResponse.getId() != null ? prisonerKssResponse.getId() : "") // 主键
                .isDel(0) // 是否删除(0:否,1:是)
                .addTime(DateUtil.convertToDate(prisonerKssResponse.getCreatetime())) // 添加时间
                .addUser("") // 添加人
                .addUserName("") // 添加人姓名
                .updateTime(DateUtil.convertToDate(prisonerKssResponse.getUpdatetime())) // 更新时间
                .updateUser("") // 更新人
                .updateUserName("") // 更新人姓名
                .proCode("") // 所属省级代码
                .proName("") // 所属省级名称
                .cityCode("") // 所属市级代码
                .cityName("") // 所属市级名称
                .regCode("") // 区域代码
                .regName("") // 区域名称
                .orgCode(prisonerKssResponse.getJsbh()) // 机构编号
                .orgName(orgName) // 机构名称
                .jgrybm("") // 监管人员编码
                .rybh(prisonerKssResponse.getRybh() != null ? prisonerKssResponse.getRybh() : "") // 人员编号
                .ajbh(prisonerKssResponse.getAjbh() != null ? prisonerKssResponse.getAjbh() : "") // 案件编号
                .ryzt(PrisonerRyzt.convertToPrisonerRyzt(prisonerKssResponse.getState())) // 人员状态
                .xm(prisonerKssResponse.getXm()) // 姓名
                .xmpy(NameKeywordGenerator.generateKeywords(prisonerKssResponse.getXm())) // 姓名拼音
                .bm(prisonerKssResponse.getBm()) // 别名
                .xb(prisonerKssResponse.getXb()) // 性别
                .csrq(DateUtil.convertToDate(prisonerKssResponse.getCsrq())) // 出生日期
                .zjlx(prisonerKssResponse.getZjlx() != null ? prisonerKssResponse.getZjlx() : "") // 证件类型
                .zjhm(prisonerKssResponse.getZjh() != null ? prisonerKssResponse.getZjh() : "") // 证件号码
                .gj(prisonerKssResponse.getGj() != null ? prisonerKssResponse.getGj() : "") // 国籍
                .mz(prisonerKssResponse.getMz() != null ? prisonerKssResponse.getMz() : "") // 民族
                .hyzk(prisonerKssResponse.getHyzk() != null ? prisonerKssResponse.getHyzk() : "") // 婚姻状况
                .jg(prisonerKssResponse.getJg() != null ? prisonerKssResponse.getJg() : "") // 籍贯
                .hjd(prisonerKssResponse.getHjd() != null ? prisonerKssResponse.getHjd() : "") // 户籍地
                .hjdxz(prisonerKssResponse.getHjdxz() != null ? prisonerKssResponse.getHjdxz() : "") // 户籍地详址
                .xzz(prisonerKssResponse.getXzd() != null ? prisonerKssResponse.getXzd() : "") // 现住址
                .xzzxz(prisonerKssResponse.getXzdxz() != null ? prisonerKssResponse.getXzdxz() : "") // 现住址详址
                .whcd(prisonerKssResponse.getWhcd() != null ? prisonerKssResponse.getWhcd() : "") // 文化程度
                .zzmm(prisonerKssResponse.getZzmm() != null ? prisonerKssResponse.getZzmm() : "") // 政治面貌
                .zy(prisonerKssResponse.getZy() != null ? prisonerKssResponse.getZy() : "") // 职业
                .gzdw(prisonerKssResponse.getGzdw() != null ? prisonerKssResponse.getGzdw() : "") // 工作单位
                .zw(prisonerKssResponse.getZw() != null ? prisonerKssResponse.getZw() : "") // 职务
                .zwjb("") // 职务级别
                .jl("") // 简历
                .sf(prisonerKssResponse.getSf() != null ? prisonerKssResponse.getSf() : "") // 身份
                .tssf(prisonerKssResponse.getTssf() != null ? prisonerKssResponse.getTssf() : "") // 特殊身份
                .jkzk(prisonerKssResponse.getJkzk() != null ? prisonerKssResponse.getJkzk() : "") // 健康状况
                .sg(prisonerKssResponse.getSg() != null ? prisonerKssResponse.getSg() : "") // 身高
                .tz("") // 体重
                .zc(prisonerKssResponse.getZuc() != null ? prisonerKssResponse.getZuc() : "") // 足长
                .tbtsbj(prisonerKssResponse.getTbtsbj() != null ? prisonerKssResponse.getTbtsbj() : "") // 体表特殊标记
                .wffzjl(prisonerKssResponse.getFzjl() != null ? prisonerKssResponse.getFzjl() : "") // 违法犯罪经历
                .cylx(prisonerKssResponse.getCylx() != null ? prisonerKssResponse.getCylx() : "") // 成员类型
                .sxzm(prisonerKssResponse.getPjzm()) // 涉嫌罪名
                .jyaq(prisonerKssResponse.getJyaq() != null ? prisonerKssResponse.getJyaq() : "") // 简要案情
                .za("") // 专案
                .zxf(prisonerKssResponse.getZxf() != null ? prisonerKssResponse.getZxf() : "") // 重刑犯
                .tabh(prisonerKssResponse.getTabh() != null ? prisonerKssResponse.getTabh() : "") // 同案编号
                .badwlx("") // 办案单位类型
                .badw(prisonerKssResponse.getBadw() != null ? prisonerKssResponse.getBadw() : "") // 办案单位
                .bar("") // 办案人
                .barlxff(prisonerKssResponse.getBardh()) // 办案人联系方式
                .sshj(prisonerKssResponse.getBahj()) // 诉讼环节
                .gyqx(DateUtil.convertToDate(prisonerKssResponse.getGyqx())) // 关押期限
                .jyrq(DateUtil.convertToDate(prisonerKssResponse.getJyrq())) // 羁押日期
                .jlrq(DateUtil.convertToDate(prisonerKssResponse.getJlrq())) // 拘留日期
                .dbrq(DateUtil.convertToDate(prisonerKssResponse.getDbrq())) // 逮捕日期
                .scqsrq(DateUtil.convertToDate(prisonerKssResponse.getScqsrq())) // 审查起诉日期
                .ysfyrq(DateUtil.convertToDate(prisonerKssResponse.getYsfyrq())) // 移送法院日期
                .zzczrq(null) // 最终处置日期
                .zzczjg("") // 最终处置结果
                .zxtzssdrq(DateUtil.convertToDate(prisonerKssResponse.getZxtzssdrq())) // 执行通知书送达日期
                .xq(prisonerKssResponse.getXq() != null ? prisonerKssResponse.getXq() : "") // 刑期
                .xqjzrq(null) // 刑期截止日期
                .xqqsrq(null) // 刑期起始日期
                .fjcl("") // 附加处理
                .rssj(DateUtil.convertToDate(prisonerKssResponse.getRsrq())) // 入所时间
                .rsyy("") // 入所原因
                .sydw(prisonerKssResponse.getSydw() != null ? prisonerKssResponse.getSydw() : "") // 送押单位
                .syr(prisonerKssResponse.getSyr() != null ? prisonerKssResponse.getSyr() : "") // 送押人
                .sypz(prisonerKssResponse.getSypz() != null ? prisonerKssResponse.getSypz() : "") // 收押凭证
                .sypzwsh(prisonerKssResponse.getSypzwsh() != null ? prisonerKssResponse.getSypzwsh() : "") // 收押凭证文书号
                .fxdj("") // 风险等级
                .gllb("") // 管理类别
                .sbfh("") // 识别服号
                .cwh(prisonerKssResponse.getCwh() != null ? prisonerKssResponse.getCwh() : "") // 床位号
                .snbh(prisonerKssResponse.getSnbh() != null ? prisonerKssResponse.getSnbh() : "") // 所内编号
                .dabh(prisonerKssResponse.getDah()) // 档案编号
                .lsyy(prisonerKssResponse.getLsyy() != null ? prisonerKssResponse.getLsyy() : "") // 留所原因
                .sfhs("") // 身份核实
                .fwbh("") // 附物编号
                .cssj(DateUtil.convertToDate(prisonerKssResponse.getCssj())) // 出所时间
                .csyy(prisonerKssResponse.getCsyy() != null ? prisonerKssResponse.getCsyy() : "") // 出所原因
                .csqx(prisonerKssResponse.getCsqx() != null ? prisonerKssResponse.getCsqx() : "") // 出所去向
                .jbr(prisonerKssResponse.getJbr() != null ? prisonerKssResponse.getJbr() : "") // 经办人
                .jbsj(DateUtil.convertToDate(prisonerKssResponse.getJbrq())) // 经办时间
                .dwdm(prisonerKssResponse.getJsbh()) // 单位代码
                .bz("") // 备注
                .bgr("") // 变更人
                .bgsj(null) // 变更时间
                .tc("") // 特长(专长)
                .frontPhoto("") // 正面照片
                .leftPhoto("") // 左侧照片
                .rightPhoto("") // 右侧照片
                .roomName(areaDO.getAreaName())
                .jsh(areaDO.getAreaCode())
                .areaId(areaDO.getParentId())
                .build();
    }

    /**
     * 根据传入的列表，根据每个AcpPmPrisonerPhotos对象的rybh属性，查询AcpPmPrisonerKssIn是否存在，
     * 如果存在，则更新AcpPmPrisonerKssIn的frontPhoto、leftPhoto、rightPhoto属性，对应AcpPmPrisonerPhotos的type属性不同值的照片
     *
     * @param result
     */
    public void updatePhoto(List<AcpPmPrisonerPhotos> result) {
        for (AcpPmPrisonerPhotos photo : result) {
            // 使用 findByRybh 方法根据 rybh 查询
            List<AcpPmPrisonerKssIn> kssInList = repository.findByRybh(photo.getRybh());
            if (!kssInList.isEmpty()) {
                for (AcpPmPrisonerKssIn kssIn : kssInList) {
                    switch (photo.getType()) {
                        case "1":
                            kssIn.setFrontPhoto(photo.getPhotourl());
                            break;
                        case "2":
                            kssIn.setLeftPhoto(photo.getPhotourl());
                            break;
                        case "3":
                            kssIn.setRightPhoto(photo.getPhotourl());
                            break;
                        default:
                            log.error("未知的type值：{}", photo.getType());
                            break;
                    }
                    repository.save(kssIn);
                }
            }
        }
    }


    @Autowired
    public AcpPmPrisonerKssInService(AcpPmPrisonerKssInRepository repository, AcpPmPrisonerPhotosRepository acpPmPrisonerPhotosRepository, AcpPmPrisonerKssOutService acpPmPrisonerKssOutService) {
        this.repository = repository;
        this.acpPmPrisonerPhotosRepository = acpPmPrisonerPhotosRepository;
        this.acpPmPrisonerKssOutService = acpPmPrisonerKssOutService;
    }

    // 增加
    public AcpPmPrisonerKssIn save(AcpPmPrisonerKssIn prisoner) {
        // 检查是否存在
        if (prisoner.getId() != null) {
            Optional<AcpPmPrisonerKssIn> existingPrisonerOptional = repository.findById(prisoner.getId());
            if (existingPrisonerOptional.isPresent()) {
                AcpPmPrisonerKssIn existingPrisoner = existingPrisonerOptional.get();
                prisoner.setJgrybm(existingPrisoner.getJgrybm()); // 保持原有的 jgrybm
            } else {
                try {
                    JSONObject formData = JSONUtil.createObj().set("orgCode", prisoner.getOrgCode());
                    String code = bspApi.executeByRuleCode(id_generator, formData.toJSONString(4));
                    log.info("生成 jgrybm 成功，jgrybm:{}", code);
                    prisoner.setJgrybm(code);
                } catch (Exception e) {
                    prisoner.setJgrybm(prisoner.getRybh());
                    log.error("生成 jgrybm 失败，使用 rybh 作为降级值", e);
                }
            }
        }
        AcpPmPrisonerKssIn acpPmPrisonerKssIn = null;
        try {

            acpPmPrisonerKssIn = repository.save(prisoner);
        } catch (Exception e) {
            log.error("看守所入所信息保存失败，要保存的数据为id:{},acpPmPrisonerKssIn:{}", acpPmPrisonerKssIn.getId(), acpPmPrisonerKssIn);
            throw new RuntimeException("保存看守所入所信息失败", e);
        }
        return acpPmPrisonerKssIn;
    }

    // 查询所有
    public List<AcpPmPrisonerKssIn> findAll() {
        return repository.findAll();
    }

    // 根据 ID 查询
    public Optional<AcpPmPrisonerKssIn> findById(String id) {
        return repository.findById(id);
    }

    // 更新
    public AcpPmPrisonerKssIn update(AcpPmPrisonerKssIn prisoner) {
        return repository.save(prisoner); // save 方法会根据 ID 判断是更新还是插入
    }

    private void removeBatchByIds(List<String> deleteIds) {
        deleteIds.forEach(this::deleteById);
    }

    // 删除
    public void deleteById(String id) {
        if (repository.findById(id).isPresent()) {
            repository.deleteById(id);
        } else {
            // 处理记录不存在的情况，例如抛出异常或记录日志
            throw new IllegalArgumentException("Entity with id " + id + " does not exist.");
        }
    }

    // 根据人员编号查询
    public List<AcpPmPrisonerKssIn> findByRybh(String rybh) {
        return repository.findByRybh(rybh);
    }
}
