package com.rs.module.integ.job.dataSync;

import com.rs.module.integ.config.ThreadPoolConfig;
import com.rs.module.integ.service.AcpPmPrisonerJlsInService;
import com.rs.module.integ.service.AcpPmPrisonerKssInService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;

@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "legal-platform-service.sync.prisoner-kss.enable", matchIfMissing = true)
public class PrisonerKssSyncSchedule {

    private final AcpPmPrisonerKssInService acpPmPrisonerKssInService;

    @Qualifier(ThreadPoolConfig.COMMON_THREAD_POOL_BEAN_NAME)
    private final ExecutorService executorService;

    @XxlJob("PrisonerKssSyncSchedule")
    public void execute() {
        //查询同步到的最新的创建时间或者更新时间
        log.info("开始异步同步看守所人员信息");
        executorService.execute(acpPmPrisonerKssInService::incrementSync);

    }
}
