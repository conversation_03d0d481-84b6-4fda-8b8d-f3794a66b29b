package com.rs.module.integ.controller;

import com.rs.module.integ.domain.entity.AcpPmPrisonerPhotos;
import com.rs.module.integ.service.AcpPmPrisonerJlsPhotosService;
import com.rs.module.integ.service.AcpPmPrisonerKssPhotosService;
import com.rs.module.integ.service.AcpPmPrisonerPhotosService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/prisonerPhotos")
public class AcpPmPrisonerPhotosController {
    private final AcpPmPrisonerPhotosService service;

    private final AcpPmPrisonerKssPhotosService acpPmPrisonerKssPhotosService;

    private final AcpPmPrisonerJlsPhotosService acpPmPrisonerJlsPh;

    @Autowired
    public AcpPmPrisonerPhotosController(AcpPmPrisonerPhotosService service, AcpPmPrisonerKssPhotosService acpPmPrisonerKssPhotosService, AcpPmPrisonerJlsPhotosService acpPmPrisonerJlsPh) {
        this.service = service;
        this.acpPmPrisonerKssPhotosService = acpPmPrisonerKssPhotosService;
        this.acpPmPrisonerJlsPh = acpPmPrisonerJlsPh;
    }

    @GetMapping("syncDataKssPhoto")
    public void syncData() {
        acpPmPrisonerKssPhotosService.incrementSync();
    }

    @GetMapping("syncDataJlsPhoto")
    public void syncDataJls() {
        acpPmPrisonerJlsPh.incrementSync();
    }

    @PostMapping
    public AcpPmPrisonerPhotos create(@RequestBody AcpPmPrisonerPhotos prisonerPhotos) {
        return service.save(prisonerPhotos);
    }

    @GetMapping
    public List<AcpPmPrisonerPhotos> getAll() {
        return service.findAll();
    }

    @GetMapping("/{id}")
    public Optional<AcpPmPrisonerPhotos> getById(@PathVariable String id) {
        return service.findById(id);
    }

    @DeleteMapping("/{id}")
    public void delete(@PathVariable String id) {
        service.deleteById(id);
    }

    @GetMapping("/rybh/{rybh}")
    public List<AcpPmPrisonerPhotos> getByRybh(@PathVariable String rybh) {
        return service.findByRybh(rybh);
    }
}