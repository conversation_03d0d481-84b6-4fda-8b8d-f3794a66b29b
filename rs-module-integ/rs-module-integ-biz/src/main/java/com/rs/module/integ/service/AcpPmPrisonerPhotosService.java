package com.rs.module.integ.service;

import com.rs.module.integ.domain.entity.AcpPmPrisonerPhotos;
import com.rs.module.integ.dao.AcpPmPrisonerPhotosRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class AcpPmPrisonerPhotosService {
    private final AcpPmPrisonerPhotosRepository repository;

    @Autowired
    public AcpPmPrisonerPhotosService(AcpPmPrisonerPhotosRepository repository) {
        this.repository = repository;
    }

    public AcpPmPrisonerPhotos save(AcpPmPrisonerPhotos prisonerPhotos) {
        return repository.save(prisonerPhotos);
    }

    public List<AcpPmPrisonerPhotos> findAll() {
        return repository.findAll();
    }

    public Optional<AcpPmPrisonerPhotos> findById(String id) {
        return repository.findById(id);
    }

    public void deleteById(String id) {
        repository.deleteById(id);
    }

    public List<AcpPmPrisonerPhotos> findByRybh(String rybh) {
        return repository.findByRybh(rybh);
    }
}