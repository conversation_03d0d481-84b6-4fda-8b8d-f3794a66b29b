package com.rs.module.integ.service;

import com.rs.module.integ.domain.dto.PaginationResult;
import com.rs.module.integ.domain.dto.RoomListPageDTO;
import com.rs.module.integ.domain.po.PdmsRoom;
import com.rs.module.integ.domain.vo.RoomListPageVO;
import com.rs.module.integ.mapper.PdmsRoomMapper;

import javax.validation.Valid;


public interface PdmsRoomService extends IBaseBatchService<PdmsRoomMapper, PdmsRoom> {

    /**
     * 分页查询监室列表
     *
     * @param roomListPageDTO 查询条件
     */
    PaginationResult<RoomListPageVO> roomListPage(@Valid RoomListPageDTO roomListPageDTO);

    /**
     * 获取监室信息
     *
     * @param jsbh 监所编号
     * @param jsh  监室编号
     */
    PdmsRoom getRoomInfo(String jsbh, String jsh);
}
