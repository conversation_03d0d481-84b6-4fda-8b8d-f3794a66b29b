package com.rs.module.integ.service.data;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.integ.controller.admin.data.vo.ShareJqListReqVO;
import com.rs.module.integ.controller.admin.data.vo.ShareJqPageReqVO;
import com.rs.module.integ.controller.admin.data.vo.ShareJqSaveReqVO;
import com.rs.module.integ.dao.data.ShareJqDao;
import com.rs.module.integ.entity.data.ShareJqDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 数据共享-监区 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ShareJqServiceImpl extends BaseServiceImpl<ShareJqDao, ShareJqDO> implements ShareJqService {

    @Resource
    private ShareJqDao shareJqDao;

    @Override
    public String createShareJq(ShareJqSaveReqVO createReqVO) {
        // 插入
        ShareJqDO shareJq = BeanUtils.toBean(createReqVO, ShareJqDO.class);
        shareJqDao.insert(shareJq);
        // 返回
        return shareJq.getId();
    }

    @Override
    public void updateShareJq(ShareJqSaveReqVO updateReqVO) {
        // 校验存在
        validateShareJqExists(updateReqVO.getId());
        // 更新
        ShareJqDO updateObj = BeanUtils.toBean(updateReqVO, ShareJqDO.class);
        shareJqDao.updateById(updateObj);
    }

    @Override
    public void deleteShareJq(String id) {
        // 校验存在
        validateShareJqExists(id);
        // 删除
        shareJqDao.deleteById(id);
    }

    private void validateShareJqExists(String id) {
        if (shareJqDao.selectById(id) == null) {
            throw new ServerException("数据共享-监区数据不存在");
        }
    }

    @Override
    public ShareJqDO getShareJq(String id) {
        return shareJqDao.selectById(id);
    }

    @Override
    public PageResult<ShareJqDO> getShareJqPage(ShareJqPageReqVO pageReqVO) {
        return shareJqDao.selectPage(pageReqVO);
    }

    @Override
    public List<ShareJqDO> getShareJqList(ShareJqListReqVO listReqVO) {
        return shareJqDao.selectList(listReqVO);
    }


}
