package com.rs.module.integ.domain.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class ExportPageQuery extends PageQuery {

    /**
     * 导出列信息（字段集合），不传导出全部
     */
    @ApiModelProperty("导出列信息集合，如果不传将按照后台默认的规则进行导出，该字段只有使用导出的时候生效")
    private List<HeadInfo> exportHeadInfoList;


    // ------------------------------------------------不对外暴露字段------------------------------------------------------
    /**
     * 监所id
     */
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String prisonId;

    // -----------------------------------------------------------------------------------------------------------------

    @Data
    public static class HeadInfo {

        /**
         * 导出字段名称
         */
        @ApiModelProperty("导出字段名称")
        private String fieldName;

        /**
         * 导出标题
         */
        @ApiModelProperty("导出标题，支持多个标题合并，合并规则查看easyexcel")
        private List<String> headNameList;
    }
}
