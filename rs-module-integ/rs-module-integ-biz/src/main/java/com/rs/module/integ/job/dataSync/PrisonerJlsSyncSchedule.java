package com.rs.module.integ.job.dataSync;

import com.rs.module.integ.config.ThreadPoolConfig;
import com.rs.module.integ.service.AcpPmPrisonerJlsInService;
import com.rs.module.integ.service.sync.PrisonerJlsSyncLegalPlatform;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;

@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "legal-platform-service.sync.prisoner-jls.enable", matchIfMissing = true)
public class PrisonerJlsSyncSchedule {

    private final AcpPmPrisonerJlsInService acpPmPrisonerJlsInService;

    @Qualifier(ThreadPoolConfig.COMMON_THREAD_POOL_BEAN_NAME)
    private final ExecutorService executorService;
    @XxlJob("PrisonerJlsSyncSchedule")
    public void execute() {
        //查询同步到的最新的创建时间或者更新时间
        log.info("开始异步同步拘留所人员信息");
        executorService.execute(acpPmPrisonerJlsInService::incrementSync);

    }
}
