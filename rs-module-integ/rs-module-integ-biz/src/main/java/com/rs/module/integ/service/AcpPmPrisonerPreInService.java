package com.rs.module.integ.service;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.rs.module.integ.dao.AcpPmPrisonerPreInRepository;
import com.rs.module.integ.domain.entity.AcpPmPrisonerPreIn;
import com.rs.module.integ.domain.request.PreInRequest;
import com.rs.module.integ.domain.response.LegalPlatformPreInDataShareResponse;
import com.rs.module.integ.domain.response.PrisonerJdsPreInResponse;
import com.rs.module.integ.domain.response.PrisonerJlsPreInResponse;
import com.rs.module.integ.domain.response.PrisonerPreInResponse;
import com.rs.module.integ.exception.BusinessException;
import com.rs.module.integ.service.sync.AbstractSyncLegalPlatform;
import com.rs.module.integ.util.DateUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Service
@RequiredArgsConstructor
public class AcpPmPrisonerPreInService extends AbstractSyncLegalPlatform<AcpPmPrisonerPreIn> {

    private final AcpPmPrisonerPreInRepository acpPmPrisonerPreInRepository;

    private final HttpClientImpl httpClient;

    /**
     * 对接库中存在时间为3000年后的数据，且月份超过12月，或者小于12月
     */
    private static final LocalDate endValidDate = LocalDate.of(2099, 1, 1);

    private static final LocalDate startValidDate = LocalDate.of(1900, 1, 1);

    private long pageSize = 500;
    /**
     * 增量同步时的最后一次的创建时间
     */
    @Getter
    private volatile LocalDateTime lastCreateTimeKss;
    @Getter
    private volatile LocalDateTime lastCreateTimeJls;
    @Getter
    private volatile LocalDateTime lastCreateTimeJds;

    /**
     * 增量同步时的最后一次的更新时间
     */
    @Getter
    private volatile LocalDateTime lastUpdateTimeKss;
    @Getter
    private volatile LocalDateTime lastUpdateTimeJls;
    @Getter
    private volatile LocalDateTime lastUpdateTimeJds;
    /**
     * 增量同步锁
     */
    private final ReentrantLock SYNC_LOCK = new ReentrantLock();

    @Value("${legal-platform-service.sync.prisoner-pre-in.minusDays}")
    private int minusDays;

    @Override
    public void incrementSync() {
        if (SYNC_LOCK.tryLock()) {
            try {
                // 开始时间
                LocalDateTime startCreateTimeKss = this.lastCreateTimeKss;
                if (Objects.isNull(startCreateTimeKss)) {
                    startCreateTimeKss = this.getTableLastCreateTimeByJslx("kss");
                    // 因为同步第三方数据会有出现创建时间一样并且超过1000条甚至更多的情况，
                    // 且接口按时间过滤是没有等于，这样会导致创建时间相同但是还未同步的被过滤掉，所以这里减一秒冗余下时间
                    startCreateTimeKss = startCreateTimeKss == null ? null : startCreateTimeKss.minusSeconds(1);
                    log.info("{} --从数据库查询看守所待入所人员最大的创建时间：startCreateTime：{}", this, startCreateTimeKss);
                }
                LocalDateTime startUpdateTimeKss = this.lastUpdateTimeKss;
                if (Objects.isNull(startUpdateTimeKss)) {
                    startUpdateTimeKss = this.getTableLastUpdateTimeByJslx("kss");
                    // 同创建时间一样的问题
                    startUpdateTimeKss = startUpdateTimeKss == null ? null : startUpdateTimeKss.minusSeconds(1);
                    log.info("{} --从数据库查询看守所待入所人员最大的更新时间：startUpdateTime：{}", this, startUpdateTimeKss);
                }
                // 拘留所开始时间
                LocalDateTime startCreateTimeJls = this.lastCreateTimeJls;
                if (Objects.isNull(startCreateTimeJls)) {
                    startCreateTimeJls = this.getTableLastCreateTimeByJslx("jls");
                    // 因为同步第三方数据会有出现创建时间一样并且超过1000条甚至更多的情况，
                    // 且接口按时间过滤是没有等于，这样会导致创建时间相同但是还未同步的被过滤掉，所以这里减一秒冗余下时间
                    startCreateTimeJls = startCreateTimeJls == null ? null : startCreateTimeJls.minusSeconds(1);
                    log.info("{} --从数据库查询拘留所待入所人员最大的创建时间：startCreateTime：{}", this, startCreateTimeJls);
                }
                LocalDateTime startUpdateTimeJls = this.lastUpdateTimeJls;
                if (Objects.isNull(startUpdateTimeJls)) {
                    startUpdateTimeJls = this.getTableLastUpdateTimeByJslx("jls");
                    // 同创建时间一样的问题
                    startUpdateTimeJls = startUpdateTimeJls == null ? null : startUpdateTimeJls.minusSeconds(1);
                    log.info("{} --从数据库查询拘留所待入所人员最大的更新时间：startUpdateTime：{}", this, startUpdateTimeJls);
                }
                // 戒毒所开始时间
                LocalDateTime startCreateTimeJds = this.lastCreateTimeJds;
                if (Objects.isNull(startCreateTimeJds)) {
                    startCreateTimeJds = this.getTableLastCreateTimeByJslx("jds");
                    // 因为同步第三方数据会有出现创建时间一样并且超过1000条甚至更多的情况，
                    // 且接口按时间过滤是没有等于，这样会导致创建时间相同但是还未同步的被过滤掉，所以这里减一秒冗余下时间
                    startCreateTimeJds = startCreateTimeJds == null ? null : startCreateTimeJds.minusSeconds(1);
                    log.info("{} --从数据库查询戒毒所待入所人员最大的创建时间：startCreateTime：{}", this, startCreateTimeJds);
                }
                LocalDateTime startUpdateTimeJds = this.lastUpdateTimeJds;
                if (Objects.isNull(startUpdateTimeJds)) {
                    startUpdateTimeJds = this.getTableLastUpdateTimeByJslx("jds");
                    // 同创建时间一样的问题
                    startUpdateTimeJds = startUpdateTimeJds == null ? null : startUpdateTimeJds.minusSeconds(1);
                    log.info("{} --从数据库查询戒毒所待入所人员最大的更新时间：startUpdateTime：{}", this, startUpdateTimeJds);
                }
                // 新增数据同步
                this.doPageIncrementSyncCreateDataKss(startCreateTimeKss);
                this.doPageIncrementSyncCreateDataJls(startCreateTimeJls);
                this.doPageIncrementSyncCreateDataJds(startCreateTimeJds);
                // 更新数据同步
                this.doPageIncrementSyncUpdateDataKss(startUpdateTimeKss);
                this.doPageIncrementSyncUpdateDataJls(startUpdateTimeJls);
                this.doPageIncrementSyncUpdateDataJds(startUpdateTimeJds);
            } finally {
                SYNC_LOCK.unlock();
            }
        } else {
            log.warn("{} --已有线程正在增量同步中.......", this);
        }
    }

    private void doPageIncrementSyncCreateDataKss(final LocalDateTime startCreateTimeKss) {
        long page = 1;
        long pageSize = this.pageSize;
        LocalDateTime lastMaxCreateTimeTempKss = startCreateTimeKss;
        while (true) {
            try {
                log.info("{} --第{}页看守所待入所人员数据新增开始同步，查询开始时间：startCreateTime：{}",
                        this, page, startCreateTimeKss);
                List<AcpPmPrisonerPreIn> result = this.getCreateDataListPage(startCreateTimeKss, page, pageSize, "kss");
                if (CollectionUtils.isEmpty(result)) {
                    log.warn("{} --看守所待入所人员新增数据查询为空，查询开始时间：startCreateTime：{}，当前系统时间：{}",
                            this, startCreateTimeKss, LocalDateTime.now());
                    break;
                }
                int size = result.size();
                lastMaxCreateTimeTempKss = DateUtil.max(this.handleDataAndReturnMaxCreateTime(result), lastMaxCreateTimeTempKss);
                log.info("{} --第{}页看守所待入所人员数据新增同步，该批次比较的最大的创建时间为：{}",
                        this, page, lastMaxCreateTimeTempKss);
                if (size < pageSize) {
                    break;
                }
                page++;
            } catch (Exception e) {
                log.error("{} --第{}页看守所待入所人员数据新增同步异常，重置最后创建时间为空", this, page, e);
                this.lastCreateTimeKss = null;
                throw new RuntimeException(e);
            }
        }
        this.lastCreateTimeKss = lastMaxCreateTimeTempKss;
        log.info("{} --看守所待入所人员数据更新完成，最后比较的最大创建时间为：{}", this, lastMaxCreateTimeTempKss);
    }

    private void doPageIncrementSyncCreateDataJls(final LocalDateTime startCreateTimeJls) {
        long page = 1;
        long pageSize = this.pageSize;
        LocalDateTime lastMaxCreateTimeTempJls = startCreateTimeJls;
        while (true) {
            try {
                log.info("{} --第{}页拘留所待入所人员数据新增开始同步，查询开始时间：startCreateTime：{}",
                        this, page, startCreateTimeJls);
                List<AcpPmPrisonerPreIn> result = this.getCreateDataListPage(startCreateTimeJls, page, pageSize, "jls");
                if (CollectionUtils.isEmpty(result)) {
                    log.warn("{} --拘留所待入所人员新增数据查询为空，查询开始时间：startCreateTime：{}，当前系统时间：{}",
                            this, startCreateTimeJls, LocalDateTime.now());
                    break;
                }
                int size = result.size();
                lastMaxCreateTimeTempJls = DateUtil.max(this.handleDataAndReturnMaxCreateTime(result), lastMaxCreateTimeTempJls);
                log.info("{} --第{}页拘留所待入所人员数据新增同步，该批次比较的最大的创建时间为：{}",
                        this, page, lastMaxCreateTimeTempJls);
                if (size < pageSize) {
                    break;
                }
                page++;
            } catch (Exception e) {
                log.error("{} --第{}页拘留所待入所人员数据新增同步异常，重置最后创建时间为空", this, page, e);
                this.lastCreateTimeJls = null;
                throw new RuntimeException(e);
            }
        }
        this.lastCreateTimeJls = lastMaxCreateTimeTempJls;
        log.info("{} --拘留所待入所人员数据更新完成，最后比较的最大创建时间为：{}", this, lastMaxCreateTimeTempJls);
    }

    private void doPageIncrementSyncCreateDataJds(final LocalDateTime startCreateTimeJds) {
        long page = 1;
        long pageSize = this.pageSize;
        LocalDateTime lastMaxCreateTimeTempJds = startCreateTimeJds;
        while (true) {
            try {
                log.info("{} --第{}页戒毒所待入所人员数据新增开始同步，查询开始时间：startCreateTime：{}",
                        this, page, startCreateTimeJds);
                List<AcpPmPrisonerPreIn> result = this.getCreateDataListPage(startCreateTimeJds, page, pageSize, "jds");
                if (CollectionUtils.isEmpty(result)) {
                    log.warn("{} --戒毒所待入所人员新增数据查询为空，查询开始时间：startCreateTime：{}，当前系统时间：{}",
                            this, startCreateTimeJds, LocalDateTime.now());
                    break;
                }
                int size = result.size();
                lastMaxCreateTimeTempJds = DateUtil.max(this.handleDataAndReturnMaxCreateTime(result), lastMaxCreateTimeTempJds);
                log.info("{} --第{}页戒毒所待入所人员数据新增同步，该批次比较的最大的创建时间为：{}",
                        this, page, lastMaxCreateTimeTempJds);
                if (size < pageSize) {
                    break;
                }
                page++;
            } catch (Exception e) {
                log.error("{} --第{}页戒毒所待入所人员数据新增同步异常，重置最后创建时间为空", this, page, e);
                this.lastCreateTimeJds = null;
                throw new RuntimeException(e);
            }
        }
        this.lastCreateTimeJds = lastMaxCreateTimeTempJds;
        log.info("{} --戒毒所待入所人员数据更新完成，最后比较的最大创建时间为：{}", this, lastMaxCreateTimeTempJds);
    }

    private void doPageIncrementSyncUpdateDataKss(final LocalDateTime startUpdateTimeKss) {
        long page = 1;
        long pageSize = this.pageSize;
        LocalDateTime lastMaxUpdateTimeTempKss = startUpdateTimeKss;
        while (true) {
            try {
                log.info("{} --第{}页看守所待入所人员数据更新同步，查询开始时间：startUpdateTime：{}",
                        this, page, startUpdateTimeKss);
                List<AcpPmPrisonerPreIn> result = this.getUpdateDataListPage(startUpdateTimeKss, page, pageSize, "kss");
                if (CollectionUtils.isEmpty(result)) {
                    log.warn("{} --看守所待入所人员更新数据查询为空，查询开始时间：startUpdateTime：{}，当前系统时间：{}",
                            this, startUpdateTimeKss, LocalDateTime.now());
                    break;
                }
                int size = result.size();
                lastMaxUpdateTimeTempKss = DateUtil.max(this.handleDataAndReturnMaxUpdateTime(result), lastMaxUpdateTimeTempKss);
                log.info("{} --第{}页看守所待入所人员数据更新同步，该批次比较的最大更新时间为：{}",
                        this, page, lastMaxUpdateTimeTempKss);
                if (size < pageSize) {
                    break;
                }
                page++;
            } catch (Exception e) {
                log.error("{} --第{}页看守所待入所人员数据更新同步异常，重置最后更新时间为空", this, page, e);
                this.lastUpdateTimeKss = null;
                throw new RuntimeException(e);
            }
        }
        this.lastUpdateTimeKss = lastMaxUpdateTimeTempKss;
        log.info("{} --看守所待入所人员数据更新完成，最后比较的最大更新时间为：{}", this, lastMaxUpdateTimeTempKss);
    }

    private void doPageIncrementSyncUpdateDataJls(final LocalDateTime startUpdateTimeJls) {
        long page = 1;
        long pageSize = this.pageSize;
        LocalDateTime lastMaxUpdateTimeTempJls = startUpdateTimeJls;
        while (true) {
            try {
                log.info("{} --第{}页拘留所待入所人员数据更新同步，查询开始时间：startUpdateTime：{}",
                        this, page, startUpdateTimeJls);
                List<AcpPmPrisonerPreIn> result = this.getUpdateDataListPage(startUpdateTimeJls, page, pageSize, "jls");
                if (CollectionUtils.isEmpty(result)) {
                    log.warn("{} --拘留所待入所人员更新数据查询为空，查询开始时间：startUpdateTime：{}，当前系统时间：{}",
                            this, startUpdateTimeJls, LocalDateTime.now());
                    break;
                }
                int size = result.size();
                lastMaxUpdateTimeTempJls = DateUtil.max(this.handleDataAndReturnMaxUpdateTime(result), lastMaxUpdateTimeTempJls);
                log.info("{} --第{}页拘留所待入所人员数据更新同步，该批次比较的最大更新时间为：{}",
                        this, page, lastMaxUpdateTimeTempJls);
                if (size < pageSize) {
                    break;
                }
                page++;
            } catch (Exception e) {
                log.error("{} --第{}页拘留所待入所人员数据更新同步异常，重置最后更新时间为空", this, page, e);
                this.lastUpdateTimeJls = null;
                throw new RuntimeException(e);
            }
        }
        this.lastUpdateTimeJls = lastMaxUpdateTimeTempJls;
        log.info("{} --拘留所待入所人员数据更新完成，最后比较的最大更新时间为：{}", this, lastMaxUpdateTimeTempJls);
    }

    private void doPageIncrementSyncUpdateDataJds(final LocalDateTime startUpdateTimeJds) {
        long page = 1;
        long pageSize = this.pageSize;
        LocalDateTime lastMaxUpdateTimeTempJds = startUpdateTimeJds;
        while (true) {
            try {
                log.info("{} --第{}页戒毒所待入所人员数据更新同步，查询开始时间：startUpdateTime：{}",
                        this, page, startUpdateTimeJds);
                List<AcpPmPrisonerPreIn> result = this.getUpdateDataListPage(startUpdateTimeJds, page, pageSize, "jds");
                if (CollectionUtils.isEmpty(result)) {
                    log.warn("{} --戒毒所待入所人员更新数据查询为空，查询开始时间：startUpdateTime：{}，当前系统时间：{}",
                            this, startUpdateTimeJds, LocalDateTime.now());
                    break;
                }
                int size = result.size();
                lastMaxUpdateTimeTempJds = DateUtil.max(this.handleDataAndReturnMaxUpdateTime(result), lastMaxUpdateTimeTempJds);
                log.info("{} --第{}页戒毒所待入所人员数据更新同步，该批次比较的最大更新时间为：{}",
                        this, page, lastMaxUpdateTimeTempJds);
                if (size < pageSize) {
                    break;
                }
                page++;
            } catch (Exception e) {
                log.error("{} --第{}页戒毒所待入所人员数据更新同步异常，重置最后更新时间为空", this, page, e);
                this.lastUpdateTimeJds = null;
                throw new RuntimeException(e);
            }
        }
        this.lastUpdateTimeJds = lastMaxUpdateTimeTempJds;
        log.info("{} --戒毒所待入所人员数据更新完成，最后比较的最大更新时间为：{}", this, lastMaxUpdateTimeTempJds);
    }

    @Override
    protected List<AcpPmPrisonerPreIn> getCreateDataListPage(LocalDateTime startCreateTime, long page, long pageSize) {
        return null;
    }

    @Override
    protected List<AcpPmPrisonerPreIn> getUpdateDataListPage(LocalDateTime startUpdateTime, long page, long pageSize) {
        return null;
    }

    protected List<AcpPmPrisonerPreIn> getCreateDataListPage(LocalDateTime startCreateTime, long page, long pageSize, String jslx) {
        // 不能确保源数据是否线性递增，所以开始时间后退几天，确保同步到所有数据
        startCreateTime = startCreateTime == null ? null : startCreateTime.minusDays(minusDays);
        // 时间格式化
        String formattedTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(startCreateTime);
        // pageSize最大值为500
        pageSize = Math.min(pageSize, 500);
        List<AcpPmPrisonerPreIn> result = new ArrayList<>();
        // 看守所数据
        if (jslx.equals("kss")) {
            LegalPlatformPreInDataShareResponse<PrisonerPreInResponse> legalPlatformBaseResponse =
                    httpClient.getPreInKssData(PreInRequest.builder()
                            .createStartTime(formattedTime)
                            .pageNum(page + "")
                            .pageSize(pageSize + "")
                            .build());

            List<PrisonerPreInResponse> preInResponses = legalPlatformBaseResponse.getRows();

            preInResponses.forEach(prisonerPreInResponse -> {
                AcpPmPrisonerPreIn acpPmPrisonerPreIn = new AcpPmPrisonerPreIn();
                BeanUtils.copyProperties(prisonerPreInResponse, acpPmPrisonerPreIn);
                acpPmPrisonerPreIn.setJslx("kss");

                result.add(acpPmPrisonerPreIn);
            });

        } else if (jslx.equals("jls")) {
            // 拘留所数据
            LegalPlatformPreInDataShareResponse<PrisonerJlsPreInResponse> legalPlatformJlsBaseResponse =
                    httpClient.getPreInJlsData(PreInRequest.builder()
                            .createStartTime(formattedTime)
                            .pageNum(page + "")
                            .pageSize(pageSize + "")
                            .build());

            List<PrisonerJlsPreInResponse> preInResponses = legalPlatformJlsBaseResponse.getRows();

            preInResponses.forEach(prisonerPreInResponse -> {
                AcpPmPrisonerPreIn acpPmPrisonerPreIn = new AcpPmPrisonerPreIn();
                BeanUtils.copyProperties(prisonerPreInResponse, acpPmPrisonerPreIn);
                acpPmPrisonerPreIn.setJslx("jls");

                result.add(acpPmPrisonerPreIn);
            });
        } else if (jslx.equals("jds")) {
            // 戒毒所数据
            LegalPlatformPreInDataShareResponse<PrisonerJdsPreInResponse> legalPlatformJlsBaseResponse =
                    httpClient.getPreInJdsData(PreInRequest.builder()
                            .createStartTime(formattedTime)
                            .pageNum(page + "")
                            .pageSize(pageSize + "")
                            .build());

            List<PrisonerJdsPreInResponse> preInResponses = legalPlatformJlsBaseResponse.getRows();

            preInResponses.forEach(prisonerPreInResponse -> {
                AcpPmPrisonerPreIn acpPmPrisonerPreIn = new AcpPmPrisonerPreIn();
                BeanUtils.copyProperties(prisonerPreInResponse, acpPmPrisonerPreIn);
                acpPmPrisonerPreIn.setJslx("jds");

                result.add(acpPmPrisonerPreIn);
            });
        } else {
            throw new BusinessException("监所类型异常");
        }

        return result;
    }

    /**
     * 对接时间的存在不符合规范的数据，加条件过滤掉
     *
     * @param chainWrapper 查询条件
     */
    private void filterDate(LambdaQueryChainWrapper<AcpPmPrisonerPreIn> chainWrapper) {
        chainWrapper.gt(AcpPmPrisonerPreIn::getCreatetime, startValidDate)
                .lt(AcpPmPrisonerPreIn::getCreatetime, endValidDate)
                .and(wrapper ->
                        wrapper.isNull(AcpPmPrisonerPreIn::getCsrq)
                                .or(andWrapper ->
                                        andWrapper.lt(AcpPmPrisonerPreIn::getCsrq, endValidDate)
                                                .gt(AcpPmPrisonerPreIn::getCsrq, startValidDate))
                )
                .and(wrapper ->
                        wrapper.isNull(AcpPmPrisonerPreIn::getUpdatetime)
                                .or(andWrapper ->
                                        andWrapper.lt(AcpPmPrisonerPreIn::getUpdatetime, endValidDate)
                                                .gt(AcpPmPrisonerPreIn::getUpdatetime, startValidDate))
                )
                .and(wrapper ->
                        wrapper.isNull(AcpPmPrisonerPreIn::getJlrq)
                                .or(andWrapper ->
                                        andWrapper.lt(AcpPmPrisonerPreIn::getJlrq, endValidDate)
                                                .gt(AcpPmPrisonerPreIn::getJlrq, startValidDate))
                )
                .and(wrapper ->
                        wrapper.isNull(AcpPmPrisonerPreIn::getGyqx)
                                .or(andWrapper ->
                                        andWrapper.lt(AcpPmPrisonerPreIn::getGyqx, endValidDate)
                                                .gt(AcpPmPrisonerPreIn::getGyqx, startValidDate))
                )
                .and(wrapper ->
                        wrapper.isNull(AcpPmPrisonerPreIn::getRsrq)
                                .or(andWrapper ->
                                        andWrapper.lt(AcpPmPrisonerPreIn::getRsrq, endValidDate)
                                                .gt(AcpPmPrisonerPreIn::getRsrq, startValidDate))
                );
    }

    private AcpPmPrisonerPreIn convertToPdmsEntity(AcpPmPrisonerPreIn legalPlatformPrisonerPreIn,
                                                  Map<String, String> dictValueToNameMapByType) {
        AcpPmPrisonerPreIn acpPmPrisonerPreIn = new AcpPmPrisonerPreIn();
        BeanUtils.copyProperties(legalPlatformPrisonerPreIn, acpPmPrisonerPreIn);
        acpPmPrisonerPreIn.setBadw(dictValueToNameMapByType.getOrDefault(acpPmPrisonerPreIn.getBadw(), acpPmPrisonerPreIn.getBadw()));
        acpPmPrisonerPreIn.setSydw(dictValueToNameMapByType.getOrDefault(acpPmPrisonerPreIn.getSydw(), acpPmPrisonerPreIn.getSydw()));
        acpPmPrisonerPreIn.setSpdw(dictValueToNameMapByType.getOrDefault(acpPmPrisonerPreIn.getSpdw(), acpPmPrisonerPreIn.getSpdw()));
        acpPmPrisonerPreIn.setJljdjg(dictValueToNameMapByType.getOrDefault(acpPmPrisonerPreIn.getJljdjg(), acpPmPrisonerPreIn.getJljdjg()));
        return acpPmPrisonerPreIn;
    }

    protected List<AcpPmPrisonerPreIn> getUpdateDataListPage(LocalDateTime startUpdateTime, long page, long pageSize, String jslx) {
        // 不能确保源数据是否线性递增，所以开始时间后退几天，确保同步到所有数据
        startUpdateTime = startUpdateTime == null ? null : startUpdateTime.minusDays(minusDays);
        // 时间格式化
        String formattedTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(startUpdateTime);
        // pageSize最大值为500
        pageSize = Math.min(pageSize, 500);
        List<AcpPmPrisonerPreIn> result = new ArrayList<>();
        // 看守所数据
        if (jslx.equals("kss")) {
            LegalPlatformPreInDataShareResponse<PrisonerPreInResponse> legalPlatformBaseResponse =
                    httpClient.getPreInKssData(PreInRequest.builder()
                            .updateStartTime(formattedTime)
                            .pageNum(page + "")
                            .pageSize(pageSize + "")
                            .build());

            List<PrisonerPreInResponse> preInResponses = legalPlatformBaseResponse.getRows();

            preInResponses.forEach(prisonerPreInResponse -> {
                AcpPmPrisonerPreIn acpPmPrisonerPreIn = new AcpPmPrisonerPreIn();
                BeanUtils.copyProperties(prisonerPreInResponse, acpPmPrisonerPreIn);
                acpPmPrisonerPreIn.setJslx("kss");

                result.add(acpPmPrisonerPreIn);
            });

        } else if (jslx.equals("jls")) {
            // 拘留所数据
            LegalPlatformPreInDataShareResponse<PrisonerJlsPreInResponse> legalPlatformJlsBaseResponse =
                    httpClient.getPreInJlsData(PreInRequest.builder()
                            .updateStartTime(formattedTime)
                            .pageNum(page + "")
                            .pageSize(pageSize + "")
                            .build());

            List<PrisonerJlsPreInResponse> preInResponses = legalPlatformJlsBaseResponse.getRows();

            preInResponses.forEach(prisonerPreInResponse -> {
                AcpPmPrisonerPreIn acpPmPrisonerPreIn = new AcpPmPrisonerPreIn();
                BeanUtils.copyProperties(prisonerPreInResponse, acpPmPrisonerPreIn);
                acpPmPrisonerPreIn.setJslx("jls");

                result.add(acpPmPrisonerPreIn);
            });
        } else if (jslx.equals("jds")) {
            // 戒毒所数据
            LegalPlatformPreInDataShareResponse<PrisonerJdsPreInResponse> legalPlatformJlsBaseResponse =
                    httpClient.getPreInJdsData(PreInRequest.builder()
                            .updateStartTime(formattedTime)
                            .pageNum(page + "")
                            .pageSize(pageSize + "")
                            .build());

            List<PrisonerJdsPreInResponse> preInResponses = legalPlatformJlsBaseResponse.getRows();

            preInResponses.forEach(prisonerPreInResponse -> {
                AcpPmPrisonerPreIn acpPmPrisonerPreIn = new AcpPmPrisonerPreIn();
                BeanUtils.copyProperties(prisonerPreInResponse, acpPmPrisonerPreIn);
                acpPmPrisonerPreIn.setJslx("jds");

                result.add(acpPmPrisonerPreIn);
            });
        } else {
            throw new BusinessException("监所类型异常");
        }

        return result;
    }

    @Override
    protected LocalDateTime handleDataAndReturnMaxCreateTime(List<AcpPmPrisonerPreIn> result) {
        LocalDateTime newLastCreateTime = this.compareMaxTime(result, this.getLastCreateTime(),
                AcpPmPrisonerPreIn::getCreatetime);
        acpPmPrisonerPreInRepository.saveAll(result);
        return newLastCreateTime;
    }

    @Override
    protected LocalDateTime handleDataAndReturnMaxUpdateTime(List<AcpPmPrisonerPreIn> result) {
        LocalDateTime newLastUpdateTime = this.compareMaxTime(result, this.getLastUpdateTime(),
                AcpPmPrisonerPreIn::getUpdatetime);
        acpPmPrisonerPreInRepository.saveAll(result);
        return newLastUpdateTime;
    }

    @Override
    protected LocalDateTime getTableLastCreateTime() {
        return null;
    }

    @Override
    protected boolean hasData() {
        return acpPmPrisonerPreInRepository.count() > 0 ? true : false;
    }

    @Override
    protected LocalDateTime getTableLastUpdateTime() {
        return null;
    }

    protected LocalDateTime getTableLastCreateTimeByJslx(String jslx) {
        return acpPmPrisonerPreInRepository.getTableLastCreateTime(jslx);
    }

    protected LocalDateTime getTableLastUpdateTimeByJslx(String jslx) {
        return acpPmPrisonerPreInRepository.getTableLastUpdateTime(jslx);
    }


}
