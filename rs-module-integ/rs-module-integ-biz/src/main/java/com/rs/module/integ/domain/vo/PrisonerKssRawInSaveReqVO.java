package com.rs.module.integ.domain.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 看守所原始在押人员信息新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonerKssRawInSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("ID")
    private String id;

    @ApiModelProperty("人员编号")
    private String rybh;

    @ApiModelProperty("姓名")
    private String xm;

    @ApiModelProperty("民族")
    private String mz;

    @ApiModelProperty("性别")
    private String xb;

    @ApiModelProperty("证件类型")
    private String zjlx;

    @ApiModelProperty("证件号")
    private String zjh;

    @ApiModelProperty("别名")
    private String bm;

    @ApiModelProperty("出生日期")
    private Date csrq;

    @ApiModelProperty("婚姻状况")
    private String hyzk;

    @ApiModelProperty("国籍")
    private String gj;

    @ApiModelProperty("户籍地")
    private String hjd;

    @ApiModelProperty("入所日期")
    private Date rsrq;

    @ApiModelProperty("出所时间")
    private String cssj;

    @ApiModelProperty("简要案情")
    private String jyaq;

    @ApiModelProperty("办案单位")
    private String badw;

    @ApiModelProperty("关押期限")
    private String gyqx;

    @ApiModelProperty("健康状况")
    private String jkzk;

    @ApiModelProperty("人员管理类别")
    private String rygllb;

    @ApiModelProperty("危险等级")
    private String wxdj;

    @ApiModelProperty("办案环节")
    private String bahj;

    @ApiModelProperty("案由")
    private String ay;

    @ApiModelProperty("入所性质")
    private String rsxz;

    @ApiModelProperty("单位类型")
    private String dwlx;

    @ApiModelProperty("指纹编号")
    private String zwbh;

    @ApiModelProperty("监室号")
    private String jsh;

    @ApiModelProperty("创建时间")
    private Date createtime;

    @ApiModelProperty("更新时间")
    private Date updatetime;

    @ApiModelProperty("状态")
    private String state;

    @ApiModelProperty("拘（看守）所编号")
    private String jsbh;

    @ApiModelProperty("SHID")
    private String shid;

    @ApiModelProperty("执行通知书收到日期")
    private String zxtzssdrq;

    @ApiModelProperty("处理结果日期")
    private String cljgrq;

    @ApiModelProperty("经办人")
    private String jbr;

    @ApiModelProperty("经办日期")
    private String jbrq;

    @ApiModelProperty("出所原因")
    private String csyy;

    @ApiModelProperty("文化程度")
    private String whcd;

    @ApiModelProperty("职业")
    private String zy;

    @ApiModelProperty("职务")
    private String zw;

    @ApiModelProperty("特殊身份")
    private String tssf;

    @ApiModelProperty("专长")
    private String zc;

    @ApiModelProperty("身份")
    private String sf;

    @ApiModelProperty("工作单位")
    private String gzdw;

    @ApiModelProperty("政治面貌")
    private String zzmm;

    @ApiModelProperty("送押单位")
    private String sydw;

    @ApiModelProperty("外籍人员编号")
    private String wbrybh;

    @ApiModelProperty("档案号")
    private String dah;

    @ApiModelProperty("所内编号")
    private String snbh;

    @ApiModelProperty("ZUC")
    private String zuc;

    @ApiModelProperty("身高")
    private String sg;

    @ApiModelProperty("籍贯")
    private String jg;

    @ApiModelProperty("户籍地详址")
    private String hjdxz;

    @ApiModelProperty("现住地")
    private String xzd;

    @ApiModelProperty("现住地详址")
    private String xzdxz;

    @ApiModelProperty("BHLX")
    private String bhlx;

    @ApiModelProperty("SYR")
    private String syr;

    @ApiModelProperty("SY")
    private String sy;

    @ApiModelProperty("收押凭证文书号")
    private String sypzwsh;

    @ApiModelProperty("收押凭证")
    private String sypz;

    @ApiModelProperty("羁押日期")
    private Date jyrq;

    @ApiModelProperty("XHYA")
    private String xhay;

    @ApiModelProperty("FZJL")
    private String fzjl;

    @ApiModelProperty("主刑犯")
    private String zxf;

    @ApiModelProperty("CAAJ")
    private String caaj;

    @ApiModelProperty("CYLX")
    private String cylx;

    @ApiModelProperty("JLRQ")
    private String jlrq;

    @ApiModelProperty("逮捕日期")
    private Date dbrq;

    @ApiModelProperty("SCQSRQ")
    private String scqsrq;

    @ApiModelProperty("YSFYRQ")
    private String ysfyrq;

    @ApiModelProperty("CSQX")
    private String csqx;

    @ApiModelProperty("刑期")
    private String xq;

    @ApiModelProperty("处理结果")
    private String cljg;

    @ApiModelProperty("FJX")
    private String fjx;

    @ApiModelProperty("奖惩情况")
    private String jcqk;

    @ApiModelProperty("YKSS")
    private String ykss;

    @ApiModelProperty("LSYY")
    private String lsyy;

    @ApiModelProperty("ZSZT")
    private String zszt;

    @ApiModelProperty("LSCSYY")
    private String lscsyy;

    @ApiModelProperty("ZYRYXGQK")
    private String zyryxgqk;

    @ApiModelProperty("EMLX")
    private String emlx;

    @ApiModelProperty("SYKZRQ")
    private String sykzrq;

    @ApiModelProperty("JSLY")
    private String jsly;

    @ApiModelProperty("TABH")
    private String tabh;

    @ApiModelProperty("YFH")
    private String yfh;

    @ApiModelProperty("CWH")
    private String cwh;

    @ApiModelProperty("TBTSBJ")
    private String tbtsbj;

    @ApiModelProperty("PJZM")
    private String pjzm;

    @ApiModelProperty("金额")
    private String je;

    @ApiModelProperty("LKDJ")
    private String lkdj;

    @ApiModelProperty("XZJE")
    private String xzje;

    @ApiModelProperty("案件编号")
    private String ajbh;

    @ApiModelProperty("YFHBS")
    private String yfhbs;

    @ApiModelProperty("THCS")
    private String thcs;

    @ApiModelProperty("案件性质")
    private String ajxz;

}
