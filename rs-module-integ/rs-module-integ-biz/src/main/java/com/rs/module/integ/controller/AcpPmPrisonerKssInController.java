package com.rs.module.integ.controller;

import com.rs.module.integ.domain.entity.AcpPmPrisonerKssIn;
import com.rs.module.integ.service.AcpPmPrisonerKssInService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/prisoners/kss")
public class AcpPmPrisonerKssInController {

    private final AcpPmPrisonerKssInService service;

    @Autowired
    public AcpPmPrisonerKssInController(AcpPmPrisonerKssInService service) {
        this.service = service;
    }

    @GetMapping("syncData")
    public void syncData() {
        service.incrementSync();
    }

    @PostMapping
    public AcpPmPrisonerKssIn create(@RequestBody AcpPmPrisonerKssIn prisoner) {
        return service.save(prisoner);
    }

    @GetMapping
    public List<AcpPmPrisonerKssIn> getAll() {
        return service.findAll();
    }

    @GetMapping("/{id}")
    public Optional<AcpPmPrisonerKssIn> getById(@PathVariable String id) {
        return service.findById(id);
    }

    @PutMapping("/{id}")
    public AcpPmPrisonerKssIn update(@PathVariable String id, @RequestBody AcpPmPrisonerKssIn prisoner) {
        prisoner.setId(id); // 确保 ID 被设置
        return service.update(prisoner);
    }

    @DeleteMapping("/{id}")
    public void delete(@PathVariable String id) {
        service.deleteById(id);
    }

    @GetMapping("/rybh/{rybh}")
    public List<AcpPmPrisonerKssIn> getByRybh(@PathVariable String rybh) {
        return service.findByRybh(rybh);
    }
}