package com.rs.module.integ.entity.data;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 看守所原始在押人员信息 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_prisoner_kss_raw_in")
@KeySequence("acp_pm_prisoner_kss_raw_in_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_prisoner_kss_raw_in")
public class PrisonerKssRawInDO {
private static final long serialVersionUID = 1L;

    @TableField(value = "CITY_NAME", fill = FieldFill.INSERT)
    private String cityName;

    @TableField(value = "CITY_CODE", fill = FieldFill.INSERT)
    private String cityCode;

    @TableField(value = "REG_NAME", fill = FieldFill.INSERT)
    private String regName;

    @TableField(value = "REG_CODE", fill = FieldFill.INSERT)
    private String regCode;

    @TableField(value = "ORG_NAME", fill = FieldFill.INSERT)
    private String orgName;

    @TableField(value = "ORG_CODE", fill = FieldFill.INSERT)
    private String orgCode;

    @TableField(value = "ADD_USER", fill = FieldFill.INSERT)
    private String addUser;

    @TableField(value = "ADD_USER_NAME", fill = FieldFill.INSERT)
    private String addUserName;

    @TableField(value = "ADD_TIME", fill = FieldFill.INSERT)
    private Date addTime;

    @TableField(value = "UPDATE_USER", fill = FieldFill.INSERT_UPDATE)
    private String updateUser;


    @TableField(value = "UPDATE_USER_NAME", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * ID
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 人员编号
     */
    private String rybh;
    /**
     * 姓名
     */
    private String xm;
    /**
     * 民族
     */
    private String mz;
    /**
     * 性别
     */
    private String xb;
    /**
     * 证件类型
     */
    private String zjlx;
    /**
     * 证件号
     */
    private String zjh;
    /**
     * 别名
     */
    private String bm;
    /**
     * 出生日期
     */
    private Date csrq;
    /**
     * 婚姻状况
     */
    private String hyzk;
    /**
     * 国籍
     */
    private String gj;
    /**
     * 户籍地
     */
    private String hjd;
    /**
     * 入所日期
     */
    private Date rsrq;
    /**
     * 出所时间
     */
    private String cssj;
    /**
     * 简要案情
     */
    private String jyaq;
    /**
     * 办案单位
     */
    private String badw;
    /**
     * 关押期限
     */
    private String gyqx;
    /**
     * 健康状况
     */
    private String jkzk;
    /**
     * 人员管理类别
     */
    private String rygllb;
    /**
     * 危险等级
     */
    private String wxdj;
    /**
     * 办案环节
     */
    private String bahj;
    /**
     * 案由
     */
    private String ay;
    /**
     * 入所性质
     */
    private String rsxz;
    /**
     * 单位类型
     */
    private String dwlx;
    /**
     * 指纹编号
     */
    private String zwbh;
    /**
     * 监室号
     */
    private String jsh;

    /**
     * 状态
     */
    private String state;
    /**
     * 拘（看守）所编号
     */
    private String jsbh;
    /**
     * SHID
     */
    private String shid;
    /**
     * 执行通知书收到日期
     */
    private String zxtzssdrq;
    /**
     * 处理结果日期
     */
    private String cljgrq;
    /**
     * 经办人
     */
    private String jbr;
    /**
     * 经办日期
     */
    private String jbrq;
    /**
     * 出所原因
     */
    private String csyy;
    /**
     * 文化程度
     */
    private String whcd;
    /**
     * 职业
     */
    private String zy;
    /**
     * 职务
     */
    private String zw;
    /**
     * 特殊身份
     */
    private String tssf;
    /**
     * 专长
     */
    private String zc;
    /**
     * 身份
     */
    private String sf;
    /**
     * 工作单位
     */
    private String gzdw;
    /**
     * 政治面貌
     */
    private String zzmm;
    /**
     * 送押单位
     */
    private String sydw;
    /**
     * 外籍人员编号
     */
    private String wbrybh;
    /**
     * 档案号
     */
    private String dah;
    /**
     * 所内编号
     */
    private String snbh;
    /**
     * ZUC
     */
    private String zuc;
    /**
     * 身高
     */
    private String sg;
    /**
     * 籍贯
     */
    private String jg;
    /**
     * 户籍地详址
     */
    private String hjdxz;
    /**
     * 现住地
     */
    private String xzd;
    /**
     * 现住地详址
     */
    private String xzdxz;
    /**
     * BHLX
     */
    private String bhlx;
    /**
     * SYR
     */
    private String syr;
    /**
     * SY
     */
    private String sy;
    /**
     * 收押凭证文书号
     */
    private String sypzwsh;
    /**
     * 收押凭证
     */
    private String sypz;
    /**
     * 羁押日期
     */
    private Date jyrq;
    /**
     * XHYA
     */
    private String xhay;
    /**
     * FZJL
     */
    private String fzjl;
    /**
     * 主刑犯
     */
    private String zxf;
    /**
     * CAAJ
     */
    private String caaj;
    /**
     * CYLX
     */
    private String cylx;
    /**
     * JLRQ
     */
    private String jlrq;
    /**
     * 逮捕日期
     */
    private Date dbrq;
    /**
     * SCQSRQ
     */
    private String scqsrq;
    /**
     * YSFYRQ
     */
    private String ysfyrq;
    /**
     * CSQX
     */
    private String csqx;
    /**
     * 刑期
     */
    private String xq;
    /**
     * 处理结果
     */
    private String cljg;
    /**
     * FJX
     */
    private String fjx;
    /**
     * 奖惩情况
     */
    private String jcqk;
    /**
     * YKSS
     */
    private String ykss;
    /**
     * LSYY
     */
    private String lsyy;
    /**
     * ZSZT
     */
    private String zszt;
    /**
     * LSCSYY
     */
    private String lscsyy;
    /**
     * ZYRYXGQK
     */
    private String zyryxgqk;
    /**
     * EMLX
     */
    private String emlx;
    /**
     * SYKZRQ
     */
    private String sykzrq;
    /**
     * JSLY
     */
    private String jsly;
    /**
     * TABH
     */
    private String tabh;
    /**
     * YFH
     */
    private String yfh;
    /**
     * CWH
     */
    private String cwh;
    /**
     * TBTSBJ
     */
    private String tbtsbj;
    /**
     * PJZM
     */
    private String pjzm;
    /**
     * 金额
     */
    private String je;
    /**
     * LKDJ
     */
    private String lkdj;
    /**
     * XZJE
     */
    private String xzje;
    /**
     * 案件编号
     */
    private String ajbh;
    /**
     * YFHBS
     */
    private String yfhbs;
    /**
     * THCS
     */
    private String thcs;
    /**
     * 案件性质
     */
    private String ajxz;

}
