package com.rs.module.integ.domain.dto;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @Author: fuwanghui
 * @Date: 2020/8/6 14:25:45
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaginationResult<T> {

    /**
     * 总条数
     */
    @ApiModelProperty("总条数")
    private long total;

    /**
     * 当前查询到的数据
     */
    @ApiModelProperty("当前查询到的数据")
    private List<T> rows;



    public static <T> PaginationResult<T> convert(Page<?> page, List<T> data) {
        PaginationResult<T> pageInfo = new PaginationResult<>();
        pageInfo.setTotal(page.getTotal());
        pageInfo.setRows(data);
        return pageInfo;
    }

    public static <T> PaginationResult<T> empty() {
        return new PaginationResult<>(0L, Collections.emptyList());
    }

    public static <T> PaginationResult<T> of(Long total, List<T> rows) {
        return new PaginationResult<>(total, rows);
    }

    public static <T> PaginationResult<T> convert(IPage<T> page) {
        if (Objects.isNull(page)) {
            throw new IllegalArgumentException("page required non null");
        }
        return new PaginationResult<>(page.getTotal(), page.getRecords());
    }

    public static <T> PaginationResult<T> convert(IPage<?> page, List<T> data) {
        return PaginationResult.<T>builder()
                .total(page.getTotal())
                .rows(data)
                .build();
    }
}
