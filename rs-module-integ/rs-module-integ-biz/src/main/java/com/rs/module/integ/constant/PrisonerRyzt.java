package com.rs.module.integ.constant;

public interface PrisonerRyzt {

    /**
     * 在所
     */
    public static final String RYZT_ZS = "10";

    /**
     * 出所
     */
    public static final String RYZT_CS = "11";

    /**
     * 待收
     */
    public static final String RYZT_DS = "19";

    /**
     * 根据 LegalPlatformDataStateConstant 的状态转换为 PrisonerRyzt 的状态
     *
     * @param legalState LegalPlatformDataStateConstant 的状态
     * @return 对应的 PrisonerRyzt 状态
     */
    static String convertToPrisonerRyzt(String legalState) {
        switch (legalState) {
            case LegalPlatformDataStateConstant.PRISONER_IN:
                return RYZT_ZS; // R8 转为 10
            case LegalPlatformDataStateConstant.PRISONER_DELETE:
                return RYZT_CS; // R3 转为 11
            case LegalPlatformDataStateConstant.PRISONER_REFUSE:
                return RYZT_DS; // R9 转为 19
            case LegalPlatformDataStateConstant.PRISONER_HISTORY:
                return RYZT_CS;
            default:
                return legalState; // 或者抛出异常
        }
    }

}
