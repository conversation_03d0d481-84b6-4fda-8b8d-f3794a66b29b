package com.rs.module.integ.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_prisoner_kss_in")
public class AcpPmPrisonerKssIn {

    @Id
    @Column(length = 32, nullable = false)
    private String id; // 主键

    @Column(nullable = false, columnDefinition = "int default 0")
    private int isDel; // 是否删除(0:否,1:是)

    @Column(name = "add_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date addTime; // 添加时间

    @Column(name = "add_user", length = 50)
    private String addUser; // 添加人

    @Column(name = "add_user_name", length = 30)
    private String addUserName; // 添加人姓名

    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime; // 更新时间

    @Column(name = "update_user", length = 50)
    private String updateUser; // 更新人

    @Column(name = "update_user_name", length = 30)
    private String updateUserName; // 更新人姓名

    @Column(name = "pro_code", length = 50)
    private String proCode; // 所属省级代码

    @Column(name = "pro_name", length = 100)
    private String proName; // 所属省级名称

    @Column(name = "city_code", length = 50)
    private String cityCode; // 所属市级代码

    @Column(name = "city_name", length = 100)
    private String cityName; // 所属市级名称

    @Column(name = "reg_code", length = 50)
    private String regCode; // 区域代码

    @Column(name = "reg_name", length = 100)
    private String regName; // 区域名称

    @Column(name = "org_code", length = 50)
    private String orgCode; // 机构编号

    @Column(name = "org_name", length = 100)
    private String orgName; // 机构名称

    @Column(name = "jgrybm", length = 64, nullable = false)
    private String jgrybm; // 监管人员编码

    @Column(name = "rybh", length = 32, nullable = false)
    private String rybh; // 人员编号

    @Column(name = "ajbh", length = 50)
    private String ajbh; // 案件编号

    @Column(name = "ryzt", length = 4)
    private String ryzt; // 人员状态

    @Column(name = "xm", length = 30)
    private String xm; // 姓名

    @Column(name = "xmpy", length = 60)
    private String xmpy; // 姓名拼音

    @Column(name = "bm", length = 30)
    private String bm; // 别名

    @Column(name = "xb", length = 2)
    private String xb; // 性别

    @Column(name = "csrq")
    @Temporal(TemporalType.TIMESTAMP)
    private Date csrq; // 出生日期

    @Column(name = "zjlx", length = 4)
    private String zjlx; // 证件类型

    @Column(name = "zjhm", length = 50)
    private String zjhm; // 证件号码

    @Column(name = "gj", length = 6)
    private String gj; // 国籍

    @Column(name = "mz", length = 4)
    private String mz; // 民族

    @Column(name = "hyzk", length = 4)
    private String hyzk; // 婚姻状况

    @Column(name = "jg", length = 12)
    private String jg; // 籍贯

    @Column(name = "hjd", length = 6)
    private String hjd; // 户籍地

    @Column(name = "hjdxz", length = 100)
    private String hjdxz; // 户籍地详址

    @Column(name = "xzz", length = 12)
    private String xzz; // 现住址

    @Column(name = "xzzxz", length = 100)
    private String xzzxz; // 现住址详址

    @Column(name = "whcd", length = 4)
    private String whcd; // 文化程度

    @Column(name = "zzmm", length = 4)
    private String zzmm; // 政治面貌

    @Column(name = "zy", length = 100)
    private String zy; // 职业

    @Column(name = "gzdw", length = 100)
    private String gzdw; // 工作单位

    @Column(name = "zw", length = 100)
    private String zw; // 职务

    @Column(name = "zwjb", length = 6)
    private String zwjb; // 职务级别

    @Column(name = "jl", columnDefinition = "text")
    private String jl; // 简历

    @Column(name = "sf", length = 4)
    private String sf; // 身份

    @Column(name = "tssf", length = 12)
    private String tssf; // 特殊身份

    @Column(name = "jkzk", length = 4)
    private String jkzk; // 健康状况

    @Column(name = "sg", length = 6)
    private String sg; // 身高

    @Column(name = "tz", length = 6)
    private String tz; // 体重

    @Column(name = "zc", length = 6)
    private String zc; // 足长

    @Column(name = "tbtsbj", columnDefinition = "text")
    private String tbtsbj; // 体表特殊标记

    @Column(name = "wffzjl", length = 100)
    private String wffzjl; // 违法犯罪经历

    @Column(name = "cylx", length = 20)
    private String cylx; // 成员类型

    @Column(name = "sxzm", length = 255)
    private String sxzm; // 涉嫌罪名

    @Column(name = "jyaq", columnDefinition = "text")
    private String jyaq; // 简要案情

    @Column(name = "za", length = 30)
    private String za; // 专案

    @Column(name = "zxf", length = 100)
    private String zxf; // 重刑犯

    @Column(name = "tabh", columnDefinition = "text")
    private String tabh; // 同案编号

    @Column(name = "badwlx", length = 100)
    private String badwlx; // 办案单位类型

    @Column(name = "badw", length = 200)
    private String badw; // 办案单位

    @Column(name = "bar", length = 60)
    private String bar; // 办案人

    @Column(name = "barlxff", columnDefinition = "text")
    private String barlxff; // 办案人联系方式

    @Column(name = "sshj", length = 16)
    private String sshj; // 诉讼环节

    @Column(name = "gyqx")
    @Temporal(TemporalType.TIMESTAMP)
    private Date gyqx; // 关押期限

    @Column(name = "jyrq")
    @Temporal(TemporalType.TIMESTAMP)
    private Date jyrq; // 羁押日期

    @Column(name = "jlrq")
    @Temporal(TemporalType.TIMESTAMP)
    private Date jlrq; // 拘留日期

    @Column(name = "dbrq")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dbrq; // 逮捕日期

    @Column(name = "scqsrq")
    @Temporal(TemporalType.TIMESTAMP)
    private Date scqsrq; // 审查起诉日期

    @Column(name = "ysfyrq")
    @Temporal(TemporalType.TIMESTAMP)
    private Date ysfyrq; // 移送法院日期

    @Column(name = "zzczrq")
    @Temporal(TemporalType.TIMESTAMP)
    private Date zzczrq; // 最终处置日期

    @Column(name = "zzczjg", length = 32)
    private String zzczjg; // 最终处置结果

    @Column(name = "zxtzssdrq")
    @Temporal(TemporalType.TIMESTAMP)
    private Date zxtzssdrq; // 执行通知书送达日期

    @Column(name = "xq", length = 32)
    private String xq; // 刑期

    @Column(name = "xqjzrq")
    @Temporal(TemporalType.TIMESTAMP)
    private Date xqjzrq; // 刑期截止日期

    @Column(name = "xqqsrq")
    @Temporal(TemporalType.TIMESTAMP)
    private Date xqqsrq; // 刑期起始日期

    @Column(name = "fjcl", length = 200)
    private String fjcl; // 附加处理

    @Column(name = "rssj")
    @Temporal(TemporalType.TIMESTAMP)
    private Date rssj; // 入所时间

    @Column(name = "rsyy", length = 4)
    private String rsyy; // 入所原因

    @Column(name = "sydw", length = 100)
    private String sydw; // 送押单位

    @Column(name = "syr", length = 100)
    private String syr; // 送押人

    @Column(name = "sypz", length = 4)
    private String sypz; // 收押凭证

    @Column(name = "sypzwsh", length = 60)
    private String sypzwsh; // 收押凭证文书号

    @Column(name = "fxdj", length = 4)
    private String fxdj; // 风险等级

    @Column(name = "gllb", length = 12)
    private String gllb; // 管理类别

    @Column(name = "jsh", length = 32)
    private String jsh; // 监室号

    @Column(name = "sbfh", length = 30)
    private String sbfh; // 识别服号

    @Column(name = "cwh", length = 10)
    private String cwh; // 床位号

    @Column(name = "snbh", length = 30)
    private String snbh; // 所内编号

    @Column(name = "dabh", length = 30)
    private String dabh; // 档案编号

    @Column(name = "lsyy", length = 32)
    private String lsyy; // 留所原因

    @Column(name = "sfhs", length = 30)
    private String sfhs; // 身份核实

    @Column(name = "fwbh", length = 255)
    private String fwbh; // 附物编号

    @Column(name = "cssj")
    @Temporal(TemporalType.TIMESTAMP)
    private Date cssj; // 出所时间

    @Column(name = "csyy", length = 4)
    private String csyy; // 出所原因

    @Column(name = "csqx", length = 100)
    private String csqx; // 出所去向

    @Column(name = "jbr", length = 30)
    private String jbr; // 经办人

    @Column(name = "jbsj")
    @Temporal(TemporalType.TIMESTAMP)
    private Date jbsj; // 经办时间

    @Column(name = "dwdm", length = 18)
    private String dwdm; // 单位代码

    @Column(name = "bz", columnDefinition = "text")
    private String bz; // 备注

    @Column(name = "bgr", length = 30)
    private String bgr; // 变更人

    @Column(name = "bgsj")
    @Temporal(TemporalType.TIMESTAMP)
    private Date bgsj; // 变更时间

    @Column(name = "tc", length = 40)
    private String tc; // 特长(专长)

    @Column(name = "front_photo", length = 100)
    private String frontPhoto; // 正面照片

    @Column(name = "left_photo", length = 100)
    private String leftPhoto; // 左侧照片

    @Column(name = "right_photo", length = 100)
    private String rightPhoto; // 右侧照片

    @Column(name = "room_name", length = 32)
    private String roomName; // 监室名称

    @Column(name = "area_id", length = 32)
    private String areaId;
}
