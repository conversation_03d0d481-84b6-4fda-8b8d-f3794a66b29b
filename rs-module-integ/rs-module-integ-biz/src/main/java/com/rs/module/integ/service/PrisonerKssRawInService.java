package com.rs.module.integ.service;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.integ.domain.vo.PrisonerKssRawInListReqVO;
import com.rs.module.integ.domain.vo.PrisonerKssRawInPageReqVO;
import com.rs.module.integ.domain.vo.PrisonerKssRawInSaveReqVO;
import com.rs.module.integ.entity.data.PrisonerKssRawInDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 看守所原始在押人员信息 Service 接口
 *
 * <AUTHOR>
 */
public interface PrisonerKssRawInService extends IBaseService<PrisonerKssRawInDO>{

    /**
     * 创建看守所原始在押人员信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrisonerKssRawIn(@Valid PrisonerKssRawInSaveReqVO createReqVO);

    /**
     * 更新看守所原始在押人员信息
     *
     * @param updateReqVO 更新信息
     */
    void updatePrisonerKssRawIn(@Valid PrisonerKssRawInSaveReqVO updateReqVO);

    /**
     * 删除看守所原始在押人员信息
     *
     * @param id 编号
     */
    void deletePrisonerKssRawIn(String id);

    /**
     * 获得看守所原始在押人员信息
     *
     * @param id 编号
     * @return 看守所原始在押人员信息
     */
    PrisonerKssRawInDO getPrisonerKssRawIn(String id);

    /**
    * 获得看守所原始在押人员信息分页
    *
    * @param pageReqVO 分页查询
    * @return 看守所原始在押人员信息分页
    */
    PageResult<PrisonerKssRawInDO> getPrisonerKssRawInPage(PrisonerKssRawInPageReqVO pageReqVO);

    /**
    * 获得看守所原始在押人员信息列表
    *
    * @param listReqVO 查询条件
    * @return 看守所原始在押人员信息列表
    */
    List<PrisonerKssRawInDO> getPrisonerKssRawInList(PrisonerKssRawInListReqVO listReqVO);


}
