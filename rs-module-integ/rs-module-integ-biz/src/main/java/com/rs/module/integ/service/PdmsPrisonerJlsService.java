package com.rs.module.integ.service;


import com.rs.module.integ.domain.po.PdmsPrisonerJls;
import com.rs.module.integ.domain.po.PdmsPrisonerPhotos;
import com.rs.module.integ.domain.vo.GetPrisonerJlsInfoVO;
import com.rs.module.integ.mapper.PdmsPrisonerJlsMapper;
import com.rs.module.integ.util.BusinessAssert;

import java.time.LocalDateTime;
import java.util.List;

public interface PdmsPrisonerJlsService extends IBaseService<PdmsPrisonerJlsMapper, PdmsPrisonerJls> {

    /**
     * 获取当前表最新创建时间
     */
    LocalDateTime getTableLastCreateTime();

    /**
     * 获取当前表最新修改时间
     */
    LocalDateTime getTableLastUpdateTime();

    /**
     * 处理新数据
     */
    void handleNewData(List<PdmsPrisonerJls> result);

    /**
     * 处理更新数据
     */
    void handleUpdateData(List<PdmsPrisonerJls> result);

    /**
     * 处理更新人员图片数据
     */
    void updatePrisonerPhotos(List<PdmsPrisonerPhotos> result);

    /**
     * 根据人员id获取人员信息
     */
    GetPrisonerJlsInfoVO getByRybh(String rybh);

    /**
     * 根据人员id获取人员信息，不允许为空
     */
    default GetPrisonerJlsInfoVO getByRybhRequireNonNull(String rybh) {
        GetPrisonerJlsInfoVO getPrisonerJlsInfoVO = getByRybh(rybh);
        BusinessAssert.notNull(getPrisonerJlsInfoVO, "该人员不在拘留所人员列表内, rybh: " + rybh);
        return getPrisonerJlsInfoVO;
    }
}
