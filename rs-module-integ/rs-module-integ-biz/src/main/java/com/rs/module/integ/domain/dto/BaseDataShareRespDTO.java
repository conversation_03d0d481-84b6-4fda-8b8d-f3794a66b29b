package com.rs.module.integ.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName BaseDataShareRespDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/7/30 15:19
 * @Version 1.0
 */
@Data
public class BaseDataShareRespDTO {
    @JsonProperty("code")
    private Integer code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("serverTime")
    private String serverTime;
    @JsonProperty("pages")
    private Integer pages;
    @JsonProperty("pageNum")
    private Integer pageNum;
    @JsonProperty("pageSize")
    private Integer pageSize;
    @JsonProperty("total")
    private Integer total;

    public boolean isSuccess() {
        if (code != null && code == 200) {
            return true;
        }
        return false;
    }
}
