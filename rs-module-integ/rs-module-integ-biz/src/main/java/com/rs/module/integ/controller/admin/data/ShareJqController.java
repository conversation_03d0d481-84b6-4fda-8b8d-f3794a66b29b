package com.rs.module.integ.controller.admin.data;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.integ.controller.admin.data.vo.*;
import com.rs.module.integ.entity.data.ShareJqDO;
import com.rs.module.integ.service.data.ShareJqService;

@Api(tags = "数据共享-监区")
@RestController
@RequestMapping("/integ/data/shareJq")
@Validated
public class ShareJqController {

    @Resource
    private ShareJqService shareJqService;

    @PostMapping("/create")
    @ApiOperation(value = "创建数据共享-监区")
    public CommonResult<String> createShareJq(@Valid @RequestBody ShareJqSaveReqVO createReqVO) {
        return success(shareJqService.createShareJq(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新数据共享-监区")
    public CommonResult<Boolean> updateShareJq(@Valid @RequestBody ShareJqSaveReqVO updateReqVO) {
        shareJqService.updateShareJq(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除数据共享-监区")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteShareJq(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           shareJqService.deleteShareJq(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得数据共享-监区")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ShareJqRespVO> getShareJq(@RequestParam("id") String id) {
        ShareJqDO shareJq = shareJqService.getShareJq(id);
        return success(BeanUtils.toBean(shareJq, ShareJqRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得数据共享-监区分页")
    public CommonResult<PageResult<ShareJqRespVO>> getShareJqPage(@Valid @RequestBody ShareJqPageReqVO pageReqVO) {
        PageResult<ShareJqDO> pageResult = shareJqService.getShareJqPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ShareJqRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得数据共享-监区列表")
    public CommonResult<List<ShareJqRespVO>> getShareJqList(@Valid @RequestBody ShareJqListReqVO listReqVO) {
        List<ShareJqDO> list = shareJqService.getShareJqList(listReqVO);
        return success(BeanUtils.toBean(list, ShareJqRespVO.class));
    }
}
