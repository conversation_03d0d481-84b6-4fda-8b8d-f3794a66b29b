package com.rs.module.integ.domain.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PreInRequest {

    /**
     * 网办人员编号
     */
    private String wbrybh;
    /**
     * 监所编号
     */
    private String jsbh;
    /**
     * 监室号
     */
    private String jsh;
    /**
     * 证件号
     */
    private String zjh;
    /**
     * 状态
     */
    private String state;
    /**
     * 创建时间开始（yyyy-MM-dd HH:mm:ss）
     */
    private String createStartTime;
    /**
     * 创建时间结束（yyyy-MM-dd HH:mm:ss）
     */
    private String createEndTime;
    /**
     * 更新时间开始（yyyy-MM-dd HH:mm:ss）
     */
    private String updateStartTime;
    /**
     * 更新时间结束（yyyy-MM-dd HH:mm:ss）
     */
    private String updateEndTime;
    /**
     * 每页显示条数（最大值500）
     */
    @NotNull
    private String pageSize;
    /**
     * 当前页数
     */
    @NotNull
    private String pageNum;
}
