package com.rs.module.integ.service.impl;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rs.module.integ.service.IBaseService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;


@Slf4j
public abstract class AbstractBaseServiceImpl<M extends BaseMapper<T>, T>
        extends ServiceImpl<M, T> implements IBaseService<M, T> {


    @Transactional(rollbackFor = Exception.class)
    public <R> void compareSaveOrUpdateBatch(List<T> result, SFunction<T, R> compareFunction) {
        BatchResult<T> batchResult = compareBatchResult(result, compareFunction);
        if (!CollectionUtils.isEmpty(batchResult.saveList)) {
            this.saveBatch(batchResult.saveList);
        }
        if (!CollectionUtils.isEmpty(batchResult.updateList)) {
            this.updateBatchById(batchResult.updateList);
        }
    }


    public <R> void compareSaveOrUpdateBatchIgnoreException(List<T> result,
                                                            SFunction<T, R> compareFunction,
                                                            BiConsumer<T, Exception> saveExceptionConsumer,
                                                            BiConsumer<T, Exception> updateExceptionConsumer) {
        BatchResult<T> batchResult = this.compareBatchResult(result, compareFunction);
        if (!CollectionUtils.isEmpty(batchResult.saveList)) {
            for (T t : batchResult.saveList) {
                try {
                    this.save(t);
                } catch (Exception e) {
                    if (Objects.nonNull(saveExceptionConsumer)) {
                        saveExceptionConsumer.accept(t, e);
                    } else {
                        log.error("{} 数据保存失败", this.getClass().getSimpleName(), e);
                    }
                }
            }
        }
        for (T t : batchResult.updateList) {
            try {
                this.updateById(t);
            } catch (Exception e) {
                if (Objects.nonNull(updateExceptionConsumer)) {
                    updateExceptionConsumer.accept(t, e);
                } else {
                    log.error("{} 数据更新失败", this.getClass().getSimpleName(), e);
                }
            }
        }
    }


    /**
     * 比较批量结果
     *
     * @param result          批量数据
     * @param compareFunction 比较字段
     */
    public <R> BatchResult<T> compareBatchResult(List<T> result, SFunction<T, R> compareFunction) {
        if (CollectionUtils.isEmpty(result)) {
            return new BatchResult<>();
        }
        Set<R> idSet = result.stream()
                .map(compareFunction)
                .collect(Collectors.toSet());
        Set<R> existIdSet = this.lambdaQuery()
                .select(compareFunction)
                .in(compareFunction, idSet)
                .list()
                .stream()
                .map(compareFunction)
                .collect(Collectors.toSet());
        List<T> saveList = new ArrayList<>(1000);
        List<T> updateList = new ArrayList<>(1000);
        for (T t : result) {
            R id = compareFunction.apply(t);
            if (existIdSet.contains(id)) {
                updateList.add(t);
            } else {
                saveList.add(t);
            }
        }
        BatchResult<T> batchResult = new BatchResult<>();
        batchResult.saveList = saveList;
        batchResult.updateList = updateList;
        return batchResult;
    }


    // ------------------------------------------------------------------------------------------------------------
    @Data
    public static class BatchResult<T> {
        private List<T> saveList;
        private List<T> updateList;
    }
}
