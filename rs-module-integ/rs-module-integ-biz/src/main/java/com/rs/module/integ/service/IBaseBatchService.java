package com.rs.module.integ.service;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.rs.module.integ.service.impl.AbstractBaseServiceImpl;

import java.util.List;
import java.util.function.BiConsumer;

public interface IBaseBatchService<M extends BaseMapper<T>, T> extends IBaseService<M, T> {

    /**
     * 批量查询比较的方式，批量保存或更新
     * ############################################
     * 使用该方法需要注意数量，不宜超过1000条
     * ############################################
     *
     * @param result          批量保存或更新的数据
     * @param compareFunction 比较的属性
     */
    <R> void compareSaveOrUpdateBatch(List<T> result, SFunction<T, R> compareFunction);


    /**
     * 批量查询比较的方式，批量保存或更新，如果保存或者更新失败，则不抛出异常
     * ############################################
     * 使用该方法需要注意数量，不宜超过1000条
     * ############################################
     * <p>
     * ###################################################################################################
     * 该方法执行效率很低，且没有事务，加事务的话还是会整批失败，需谨慎使用
     * ###################################################################################################
     *
     * @param result                  批量保存或更新的数据
     * @param compareFunction         比较的属性
     * @param saveExceptionConsumer   保存异常处理
     * @param updateExceptionConsumer 更新异常处理
     */
    <R> void compareSaveOrUpdateBatchIgnoreException(List<T> result, SFunction<T, R> compareFunction,
                                                     BiConsumer<T, Exception> saveExceptionConsumer,
                                                     BiConsumer<T, Exception> updateExceptionConsumer);

    /**
     * 批量查询比较的方式，批量保存或更新，如果保存或者更新失败，则不抛出异常
     * ############################################
     * 使用该方法需要注意数量，不宜超过1000条
     * ############################################
     * <p>
     * ###################################################################################################
     * 该方法执行效率很低，且没有事务，加事务的话还是会整批失败，需谨慎使用
     * ###################################################################################################
     *
     * @param result          批量保存或更新的数据
     * @param compareFunction 比较的属性
     */
    default <R> void compareSaveOrUpdateBatchIgnoreException(List<T> result, SFunction<T, R> compareFunction) {
        compareSaveOrUpdateBatchIgnoreException(result, compareFunction, null, null);
    }


    /**
     * 批量查询比较的方式，返回批量结果
     * ############################################
     * 使用该方法需要注意数量，不宜超过1000条
     * ############################################
     *
     * @param result          批量保存或更新的数据
     * @param compareFunction 比较的属性
     */
    <R> AbstractBaseServiceImpl.BatchResult<T> compareBatchResult(List<T> result, SFunction<T, R> compareFunction);

}
