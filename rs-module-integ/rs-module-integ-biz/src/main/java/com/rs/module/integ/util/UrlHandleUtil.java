package com.rs.module.integ.util;

import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

public class UrlHandleUtil {

    public static final String HTTP_PREFIX = "http://";

    public static final String HTTPS_PREFIX = "https://";

    /**
     * 替换原有的域名为目标域名，然后把原来的域名拼接到参数上面
     * <p>
     * {@code http://192.168.175.232:8787/xxxx/xxxx.jpg?name=xxx.jpg}
     * ->
     * {@code http://45.164.12.36/xxxx/xxxx.jpg?name=xxx.jpg&sourceOrigin=http://192.168.175.232:8787}
     *
     * @param url           待处理的url
     * @param replaceOrigin 替换的域名
     */
    public static String urlOriginReplaceAndToParam(final String url, final String replaceOrigin) {
        if (StringUtils.isBlank(url)) {
            return url;
        }
        String targetOrigin = Optional.ofNullable(replaceOrigin).orElse("");
        if (StringUtils.isNotBlank(targetOrigin) && targetOrigin.endsWith("/")) {
            targetOrigin = targetOrigin.substring(0, targetOrigin.length() - 1);
        }
        int index = -1;
        if (url.startsWith(HTTP_PREFIX)) {
            int indexOf = url.indexOf("/", HTTP_PREFIX.length());
            if (indexOf != -1) {
                index = indexOf;
            }
        }
        if (url.startsWith(HTTPS_PREFIX)) {
            int indexOf = url.indexOf("/", HTTP_PREFIX.length());
            if (indexOf != -1) {
                index = indexOf;
            }
        }
        if (index == -1) {
            return url;
        }
        String sourceOrigin = url.substring(0, index);
        String urlPath = url.substring(index);
        String newUrl;
        if (url.contains("?")) {
            newUrl = targetOrigin + urlPath + "&sourceOrigin=" + sourceOrigin;
        } else {
            newUrl = targetOrigin + urlPath + "?sourceOrigin=" + sourceOrigin;
        }
        return newUrl;
    }

    /**
     * 判断url的文件类型并且添加文件参数到url后面
     * <p>
     * {@code http://192.168.175.232:8787/xxxx/xxxx.jpg}
     * ->
     * {@code http://192.168.175.232:8787/xxxx/xxxx.jpg?fileType=.jpg}
     *
     * @param url url
     */
    public static String urlAddFileTypeParam(final String url) {
        if (StringUtils.isBlank(url)) {
            return url;
        }
        String newUrl = url;
        int index = newUrl.indexOf("?");
        String urlPath;
        if (index == -1) {
            urlPath = newUrl;
        } else {
            urlPath = newUrl.substring(0, index);
        }

        int lastIndexOf = urlPath.lastIndexOf(".");
        if (lastIndexOf == -1) {
            return newUrl;
        }
        String fileType = urlPath.substring(lastIndexOf);
        if (newUrl.contains("?")) {
            newUrl = newUrl + "&fileType=" + fileType;
        } else {
            newUrl = newUrl + "?fileType=" + fileType;
        }
        return newUrl;
    }

    /**
     * 替换原有的域名为目标域名，然后把原来的域名拼接到参数上面，并且添加文件类型参数
     * <p>
     * {@code http://192.168.175.232:8787/xxxx/xxxx.jpg}
     * ->
     * {@code http://45.164.12.36/xxxx/xxxx.jpg?sourceOrigin=http://192.168.175.232:8787&fileType=.jpg}
     *
     * @param url           待处理的url
     * @param replaceOrigin 替换的域名
     */
    public static String urlOriginReplaceToParamAddFileType(final String url, final String replaceOrigin) {
        String newUrl = urlOriginReplaceAndToParam(url, replaceOrigin);
        return urlAddFileTypeParam(newUrl);
    }
}
