package com.rs.module.integ.service;

import com.rs.module.integ.domain.po.PdmsPrisonerKss;
import com.rs.module.integ.domain.po.PdmsPrisonerPhotos;
import com.rs.module.integ.mapper.PdmsPrisonerKssMapper;
import java.time.LocalDateTime;
import java.util.List;

public interface PdmsPrisonerKssService extends IBaseService<PdmsPrisonerKssMapper, PdmsPrisonerKss> {

    /**
     * 获取当前表最新创建时间
     */
    LocalDateTime getTableLastCreateTime();

    /**
     * 获取当前表最新修改时间
     */
    LocalDateTime getTableLastUpdateTime();

    /**
     * 处理新数据
     */
    void handleNewData(List<PdmsPrisonerKss> result);

    /**
     * 处理更新数据
     */
    void handleUpdateData(List<PdmsPrisonerKss> result);

    /**
     * 更新图片数据
     */
    void updatePrisonerPhotos(List<PdmsPrisonerPhotos> result);
}
