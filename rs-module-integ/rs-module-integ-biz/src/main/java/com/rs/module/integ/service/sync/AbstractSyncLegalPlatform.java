package com.rs.module.integ.service.sync;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.base.service.pm.AreaService;
import com.rs.module.integ.domain.dto.DataShareReqVO;
import com.rs.module.integ.exception.BusinessException;
import com.rs.module.integ.util.DateUtil;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Consumer;
import java.util.function.Function;

@Slf4j
@NoArgsConstructor
public abstract class AbstractSyncLegalPlatform<T> implements ISyncLegalPlatformDataShare {

    /**
     * 增量同步锁
     */
    private final ReentrantLock SYNC_LOCK = new ReentrantLock();

    /**
     * 子类名称
     */
    private final String classSimpleName = this.getClass().getSimpleName();

    /**
     * 增量同步时的最后一次的创建时间
     */
    @Getter
    private volatile LocalDateTime lastCreateTime;

    /**
     * 增量同步时的最后一次的更新时间
     */
    @Getter
    private volatile LocalDateTime lastUpdateTime;

    /**
     * 分页查询数量
     */
    private long pageSize = 1000;


    public AbstractSyncLegalPlatform(long pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 线程安全的增量同步
     */
    @Override
    public void incrementSync() {
        if (SYNC_LOCK.tryLock()) {
            try {
                LocalDateTime startCreateTime = this.lastCreateTime;
                log.debug("{} --当前内存中的最后创建时间：{}", this.classSimpleName, startCreateTime);

                boolean needQueryFromDb = Objects.isNull(startCreateTime);
                boolean hasDataFlag = false;

                if (!needQueryFromDb) {
                    try {
                        hasDataFlag = hasData();
                        log.debug("{} --检查是否有数据：{}", this.classSimpleName, hasDataFlag);
                        needQueryFromDb = !hasDataFlag;
                    } catch (Exception e) {
                        log.warn("{} --检查数据存在性异常，将从数据库重新获取时间", this.classSimpleName, e);
                        needQueryFromDb = true;
                    }
                }

                if (needQueryFromDb) {
                    log.info("{} --需要从数据库获取最后创建时间，原因：{}", this.classSimpleName,
                            Objects.isNull(this.lastCreateTime) ? "内存中无时间" : "数据库无数据");
                    try {
                        LocalDateTime dbCreateTime = getTableLastCreateTime();
                        log.debug("{} --从数据库获取的原始创建时间：{}", this.classSimpleName, dbCreateTime);
                        // 因为同步第三方数据会有出现创建时间一样并且超过1000条甚至更多的情况，
                        // 且接口按时间过滤是没有等于，这样会导致创建时间相同但是还未同步的被过滤掉，所以这里减一秒冗余下时间
                        startCreateTime = dbCreateTime == null ? null : dbCreateTime.minusSeconds(1);
                        log.info("{} --从数据库查询最大的创建时间（已减1秒）：{}", this.classSimpleName, startCreateTime);
                    } catch (Exception e) {
                        log.error("{} --从数据库获取最后创建时间异常", this.classSimpleName, e);
                        throw new RuntimeException("获取数据库最后创建时间失败", e);
                    }
                }
                LocalDateTime startUpdateTime = this.lastUpdateTime;
                log.debug("{} --当前内存中的最后更新时间：{}", this.classSimpleName, startUpdateTime);

                if (Objects.isNull(startUpdateTime)) {
                    log.info("{} --内存中无更新时间，从数据库获取最后更新时间", this.classSimpleName);
                    try {
                        LocalDateTime dbUpdateTime = getTableLastUpdateTime();
                        log.debug("{} --从数据库获取的原始更新时间：{}", this.classSimpleName, dbUpdateTime);
                        // 同创建时间一样的问题
                        startUpdateTime = dbUpdateTime == null ? null : dbUpdateTime.minusSeconds(1);
                        log.info("{} --从数据库查询最大的更新时间（已减1秒）：{}", this.classSimpleName, startUpdateTime);
                    } catch (Exception e) {
                        log.error("{} --从数据库获取最后更新时间异常", this.classSimpleName, e);
                        throw new RuntimeException("获取数据库最后更新时间失败", e);
                    }
                }
                // 新增数据同步
                this.doPageIncrementSyncCreateData(startCreateTime);
                // 更新数据同步
                this.doPageIncrementSyncUpdateData(startUpdateTime);
            } finally {
                SYNC_LOCK.unlock();
            }
        } else {
            log.warn("{} --已有线程正在增量同步中.......", this.classSimpleName);
        }
    }

    /**
     * 实行按创建时间查询分批次处理同步的执行方法
     *
     * @param startCreateTime 查询的开始时间
     */
    private void doPageIncrementSyncCreateData(final LocalDateTime startCreateTime) {
        long page = 1;
        long pageSize = this.pageSize;
        LocalDateTime lastMaxCreateTimeTemp = startCreateTime;
        while (true) {
            try {
                log.info("{} --第{}页数据新增开始同步，查询开始时间：{}，页大小：{}",
                        this.classSimpleName, page, startCreateTime, pageSize);

                List<T> result = null;
                try {
                    result = this.getCreateDataListPage(startCreateTime, page, pageSize);
                    log.debug("{} --第{}页数据查询完成，返回{}条数据", this.classSimpleName, page,
                            result != null ? result.size() : 0);
                } catch (Exception e) {
                    log.error("{} --第{}页数据查询异常，查询开始时间：{}，页大小：{}",
                            this.classSimpleName, page, startCreateTime, pageSize, e);
                    throw e;
                }
                if (CollectionUtils.isEmpty(result)) {
                    log.warn("{} --新增数据查询为空，查询开始时间：startCreateTime：{}，当前系统时间：{}",
                            this.classSimpleName, startCreateTime, LocalDateTime.now());
                    break;
                }
                int size = result.size();
                log.debug("{} --第{}页数据新增同步，获取到{}条数据，开始处理", this.classSimpleName, page, size);

                LocalDateTime batchMaxCreateTime = null;
                try {
                    batchMaxCreateTime = this.handleDataAndReturnMaxCreateTime(result);
                    log.debug("{} --第{}页数据处理完成，该批次最大创建时间：{}", this.classSimpleName, page, batchMaxCreateTime);
                } catch (Exception e) {
                    log.error("{} --第{}页数据处理异常，数据条数：{}", this.classSimpleName, page, size, e);
                    throw e;
                }

                lastMaxCreateTimeTemp = DateUtil.max(batchMaxCreateTime, lastMaxCreateTimeTemp);
                log.info("{} --第{}页数据新增同步，该批次比较的最大的创建时间为：{}",
                        this.classSimpleName, page, lastMaxCreateTimeTemp);
                if (size < pageSize) {
                    break;
                }
                page++;

                try {
                    Thread.sleep(1000);
                    log.debug("{} --第{}页数据新增同步完成，等待1秒后继续下一页", this.classSimpleName, page);
                } catch (InterruptedException ie) {
                    log.warn("{} --第{}页数据新增同步时线程被中断", this.classSimpleName, page);
                    Thread.currentThread().interrupt();
                    break;
                }
            } catch (Exception e) {
                log.error("{} --第{}页数据新增同步异常，查询开始时间：{}，页大小：{}，重置最后创建时间为空",
                        this.classSimpleName, page, startCreateTime, pageSize, e);
                this.lastCreateTime = null;
                throw new RuntimeException(e);
            }
        }
        this.lastCreateTime = lastMaxCreateTimeTemp;
        log.info("{} --数据更新完成，最后比较的最大创建时间为：{}", this.classSimpleName, lastMaxCreateTimeTemp);
    }

    /**
     * 实行按更新时间查询分批次处理同步的执行方法
     *
     * @param startUpdateTime 查询的开始时间
     */
    private void doPageIncrementSyncUpdateData(final LocalDateTime startUpdateTime) {
        long page = 1;
        long pageSize = this.pageSize;
        LocalDateTime lastMaxUpdateTimeTemp = startUpdateTime;
        while (true) {
            try {
                log.info("{} --第{}页数据更新同步，查询开始时间：startUpdateTime：{}",
                        this.classSimpleName, page, startUpdateTime);
                List<T> result = this.getUpdateDataListPage(startUpdateTime, page, pageSize);
                if (CollectionUtils.isEmpty(result)) {
                    log.warn("{} --更新数据查询为空，查询开始时间：startUpdateTime：{}，当前系统时间：{}",
                            this.classSimpleName, startUpdateTime, LocalDateTime.now());
                    break;
                }
                int size = result.size();
                log.debug("{} --第{}页数据更新同步，获取到{}条数据，开始处理", this.classSimpleName, page, size);

                LocalDateTime batchMaxUpdateTime = null;
                try {
                    batchMaxUpdateTime = this.handleDataAndReturnMaxUpdateTime(result);
                    log.debug("{} --第{}页数据更新处理完成，该批次最大更新时间：{}", this.classSimpleName, page, batchMaxUpdateTime);
                } catch (Exception e) {
                    log.error("{} --第{}页数据更新处理异常，数据条数：{}", this.classSimpleName, page, size, e);
                    throw e;
                }

                lastMaxUpdateTimeTemp = DateUtil.max(batchMaxUpdateTime, lastMaxUpdateTimeTemp);
                log.info("{} --第{}页数据更新同步，该批次比较的最大更新时间为：{}",
                        this.classSimpleName, page, lastMaxUpdateTimeTemp);
                if (size < pageSize) {
                    break;
                }
                page++;
            } catch (Exception e) {
                log.error("{} --第{}页数据更新同步异常，查询开始时间：{}，页大小：{}，重置最后更新时间为空",
                        this.classSimpleName, page, startUpdateTime, pageSize, e);
                this.lastUpdateTime = null;
                throw new RuntimeException(e);
            }
        }
        this.lastUpdateTime = lastMaxUpdateTimeTemp;
        log.info("{} --数据更新完成，最后比较的最大更新时间为：{}", this.classSimpleName, lastMaxUpdateTimeTemp);
    }

    /**
     * 比较结果集中的时间，返回最大的时间
     *
     * @param result   结果集
     * @param lastTime 需要比较的时间
     * @param function 获取时间字段的方法
     * @param consumer 前置处理数据的方法
     */
    protected LocalDateTime compareMaxTime(List<T> result, final LocalDateTime lastTime,
                                           Function<T, LocalDateTime> function, Consumer<T> consumer) {
        LocalDateTime lastMaxTime = lastTime;
        if (CollectionUtils.isEmpty(result)) {
            log.debug("{} --compareMaxTime: 结果集为空，返回原时间：{}", this.classSimpleName, lastTime);
            return lastMaxTime;
        }

        log.debug("{} --compareMaxTime: 开始比较{}条数据的时间，当前最大时间：{}", this.classSimpleName, result.size(), lastTime);
        int nullTimeCount = 0;
        int processedCount = 0;

        for (T t : result) {
            try {
                consumer.accept(t);
                processedCount++;
            } catch (Exception e) {
                log.warn("{} --compareMaxTime: 数据预处理异常，跳过该条数据", this.classSimpleName, e);
                continue;
            }

            LocalDateTime resultTime = null;
            try {
                resultTime = function.apply(t);
            } catch (Exception e) {
                log.warn("{} --compareMaxTime: 获取时间字段异常，跳过该条数据", this.classSimpleName, e);
                continue;
            }

            if (Objects.isNull(resultTime)) {
                nullTimeCount++;
                continue;
            }
            if (lastMaxTime == null || resultTime.isAfter(lastMaxTime)) {
                lastMaxTime = resultTime;
                log.debug("{} --compareMaxTime: 发现更大时间：{}", this.classSimpleName, resultTime);
            }
        }

        if (nullTimeCount > 0) {
            log.warn("{} --compareMaxTime: 发现{}条数据的时间字段为空", this.classSimpleName, nullTimeCount);
        }

        log.debug("{} --compareMaxTime: 完成时间比较，处理{}条数据，最终最大时间：{}",
                this.classSimpleName, processedCount, lastMaxTime);
        return lastMaxTime;
    }

    /**
     * 比较结果集中的时间，返回最大的时间
     *
     * @param result   结果集
     * @param lastTime 需要比较的时间
     * @param function 获取时间字段的方法
     */
    protected LocalDateTime compareMaxTime(List<T> result, final LocalDateTime lastTime,
                                           Function<T, LocalDateTime> function) {
        return this.compareMaxTime(result, lastTime, function, t -> {
        });
    }

    /**
     * 设置增量同步的最后一次同步创建时间
     *
     * @param lastCreateTime 最后一次同步创建时间时间
     */
    public void resetLastCreateTime(LocalDateTime lastCreateTime) {
        log.info("{} --尝试重置最后创建时间为：{}", this.classSimpleName, lastCreateTime);
        if (SYNC_LOCK.tryLock()) {
            try {
                LocalDateTime oldTime = this.lastCreateTime;
                this.lastCreateTime = lastCreateTime;
                log.info("{} --成功重置最后创建时间，从：{} 改为：{}", this.classSimpleName, oldTime, lastCreateTime);
            } finally {
                SYNC_LOCK.unlock();
            }
        } else {
            log.error("{} --重置最后创建时间失败，已有线程正在同步中", this.classSimpleName);
            throw new BusinessException("已有线程正在同步中，设置失败");
        }
    }

    /**
     * 设置增量同步的最后一次同步更新时间
     *
     * @param lastUpdateTime 最后一次同步更新时间
     */
    public void resetLastUpdateTime(LocalDateTime lastUpdateTime) {
        log.info("{} --尝试重置最后更新时间为：{}", this.classSimpleName, lastUpdateTime);
        if (SYNC_LOCK.tryLock()) {
            try {
                LocalDateTime oldTime = this.lastUpdateTime;
                this.lastUpdateTime = lastUpdateTime;
                log.info("{} --成功重置最后更新时间，从：{} 改为：{}", this.classSimpleName, oldTime, lastUpdateTime);
            } finally {
                SYNC_LOCK.unlock();
            }
        } else {
            log.error("{} --重置最后更新时间失败，已有线程正在同步中", this.classSimpleName);
            throw new BusinessException("已有线程正在同步中，设置失败");
        }
    }


    // --------------------------------------------------------------------------------------------------------------------------

    /**
     * 分页获取增量同步新增的方法
     *
     * @param startCreateTime 同步开始查询创建时间
     */
    protected abstract List<T> getCreateDataListPage(LocalDateTime startCreateTime, long page, long pageSize);

    /**
     * 分页获取增量同步更新数据的方法
     *
     * @param startUpdateTime 同步开始查询更新时间
     */
    protected abstract List<T> getUpdateDataListPage(LocalDateTime startUpdateTime, long page, long pageSize);

    /**
     * 处理查询的结果数据并且返回最大的创建时间
     *
     * @param result 查询的结果数据
     * @return 最大的创建时间
     */
    protected abstract LocalDateTime handleDataAndReturnMaxCreateTime(List<T> result);

    /**
     * 处理查询的结果数据并且返回最大的更新时间
     *
     * @param result 查询的结果数据
     * @return 最大的更新时间
     */
    protected abstract LocalDateTime handleDataAndReturnMaxUpdateTime(List<T> result);

    /**
     * 从数据库获取最后的创建时间
     */
    protected abstract LocalDateTime getTableLastCreateTime();
    protected abstract boolean hasData();

    /**
     * 从数据库获取最后的更新时间
     */
    protected abstract LocalDateTime getTableLastUpdateTime();

    public String executeHttp(DataShareReqVO dataShareReqVO) {
        if (dataShareReqVO == null) {
            log.error("{} --executeHttp: 请求参数为空", this.classSimpleName);
            throw new IllegalArgumentException("请求参数不能为空");
        }

        if (StringUtils.isBlank(dataShareReqVO.getUrl())) {
            log.error("{} --executeHttp: 请求URL为空", this.classSimpleName);
            throw new IllegalArgumentException("请求URL不能为空");
        }

        long start = System.currentTimeMillis();
        String url = dataShareReqVO.getUrl();
        String requestBody = JSONObject.toJSONString(dataShareReqVO);

        log.info("{} --executeHttp: 开始HTTP请求，URL：{}，请求体大小：{}字节",
                this.classSimpleName, url, requestBody.length());
        log.debug("{} --executeHttp: 请求参数详情：{}", this.classSimpleName, requestBody);

        try {
            HttpRequest request = HttpRequest.post(url);
            request.body(requestBody);

            String responseBody = request.execute().body();
            long end = System.currentTimeMillis();
            long duration = end - start;

            if (StringUtils.isBlank(responseBody)) {
                log.warn("{} --executeHttp: HTTP请求成功但响应体为空，耗时：{}ms", this.classSimpleName, duration);
            } else {
                log.info("{} --executeHttp: HTTP请求成功，耗时：{}ms，响应体大小：{}字节",
                        this.classSimpleName, duration, responseBody.length());
                log.debug("{} --executeHttp: 响应内容：{}", this.classSimpleName, responseBody);
            }

            return responseBody;
        } catch (Exception e) {
            long end = System.currentTimeMillis();
            long duration = end - start;
            log.error("{} --executeHttp: HTTP请求异常，URL：{}，耗时：{}ms",
                    this.classSimpleName, url, duration, e);
            throw new RuntimeException("HTTP请求执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取监室号
     *
     * @return
     */
    public AreaDO getJsh(String jsbh, String jsh) {
        log.debug("{} --getJsh: 开始获取监室信息，监所编号：{}，监室号：{}", this.classSimpleName, jsbh, jsh);

        AreaDO areaDO = null;
        try {
            AreaService areaService = SpringUtil.getBean(AreaService.class);
            if (areaService == null) {
                log.error("{} --getJsh: 无法获取AreaService实例", this.classSimpleName);
                throw new RuntimeException("AreaService服务不可用");
            }

            if (StringUtils.isNotBlank(jsbh) && StringUtils.isNotBlank(jsh)) {
                log.debug("{} --getJsh: 执行数据库查询，监所编号：{}，监室号：{}", this.classSimpleName, jsbh, jsh);
                areaDO = areaService.lambdaQuery()
                        .eq(AreaDO::getOrgCode, jsbh)
                        .eq(AreaDO::getSelfAreaId, jsh)
                        .one();
                log.debug("{} --getJsh: 数据库查询完成，结果：{}", this.classSimpleName, areaDO != null ? "找到数据" : "未找到数据");
            } else {
                log.warn("{} --getJsh: 参数不完整，监所编号：{}，监室号：{}", this.classSimpleName, jsbh, jsh);
            }
        } catch (Exception e) {
            log.error("{} --getJsh: 查询监室信息异常，监所编号：{}，监室号：{}", this.classSimpleName, jsbh, jsh, e);
            // 继续执行，使用默认值
        }

        if (areaDO == null) {
            log.info("{} --getJsh: 监室信息不存在，创建默认监室信息，监所编号：{}，监室号：{}", this.classSimpleName, jsbh, jsh);
            areaDO = new AreaDO();
            areaDO.setAreaName(jsh);
            areaDO.setAreaCode(jsh);
        } else {
            log.info("{} --getJsh: 成功获取到监室信息，ID：{}，名称：{}",
                    this.classSimpleName, areaDO.getId(), areaDO.getAreaName());
        }

        return areaDO;
    }
}
