package com.rs.module.integ.service;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.integ.dao.PrisonerKssRawInDao;
import com.rs.module.integ.domain.vo.PrisonerKssRawInListReqVO;
import com.rs.module.integ.domain.vo.PrisonerKssRawInPageReqVO;
import com.rs.module.integ.domain.vo.PrisonerKssRawInSaveReqVO;
import com.rs.module.integ.entity.data.PrisonerKssRawInDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 看守所原始在押人员信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrisonerKssRawInServiceImpl extends BaseServiceImpl<PrisonerKssRawInDao, PrisonerKssRawInDO> implements PrisonerKssRawInService {

    @Resource
    private PrisonerKssRawInDao prisonerKssRawInDao;

    @Override
    public String createPrisonerKssRawIn(PrisonerKssRawInSaveReqVO createReqVO) {
        // 插入
        PrisonerKssRawInDO prisonerKssRawIn = BeanUtils.toBean(createReqVO, PrisonerKssRawInDO.class);
        prisonerKssRawInDao.insert(prisonerKssRawIn);
        // 返回
        return prisonerKssRawIn.getId();
    }

    @Override
    public void updatePrisonerKssRawIn(PrisonerKssRawInSaveReqVO updateReqVO) {
        // 校验存在
        validatePrisonerKssRawInExists(updateReqVO.getId());
        // 更新
        PrisonerKssRawInDO updateObj = BeanUtils.toBean(updateReqVO, PrisonerKssRawInDO.class);
        prisonerKssRawInDao.updateById(updateObj);
    }

    @Override
    public void deletePrisonerKssRawIn(String id) {
        // 校验存在
        validatePrisonerKssRawInExists(id);
        // 删除
        prisonerKssRawInDao.deleteById(id);
    }

    private void validatePrisonerKssRawInExists(String id) {
        if (prisonerKssRawInDao.selectById(id) == null) {
            throw new ServerException("看守所原始在押人员信息数据不存在");
        }
    }

    @Override
    public PrisonerKssRawInDO getPrisonerKssRawIn(String id) {
        return prisonerKssRawInDao.selectById(id);
    }

    @Override
    public PageResult<PrisonerKssRawInDO> getPrisonerKssRawInPage(PrisonerKssRawInPageReqVO pageReqVO) {
        return prisonerKssRawInDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrisonerKssRawInDO> getPrisonerKssRawInList(PrisonerKssRawInListReqVO listReqVO) {
        return prisonerKssRawInDao.selectList(listReqVO);
    }


}
