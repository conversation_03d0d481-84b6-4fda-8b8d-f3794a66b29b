package com.rs.module.integ.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rs.module.integ.domain.po.PdmsRoom;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

@Mapper
public interface PdmsRoomMapper extends BaseMapper<PdmsRoom> {

    /**
     * 获取当前表最新创建时间
     */
    LocalDateTime getTableLastCreateTime(@Param("dataSource") Integer dataSource);

    /**
     * 获取当前表最新更新时间
     */
    LocalDateTime getTableLastUpdateTime(@Param("dataSource") Integer dataSource);

}
