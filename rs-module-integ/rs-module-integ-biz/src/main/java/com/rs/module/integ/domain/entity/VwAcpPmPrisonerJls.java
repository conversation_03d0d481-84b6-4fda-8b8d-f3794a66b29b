package com.rs.module.integ.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "vw_acp_pm_prisoner_jls")
public class VwAcpPmPrisonerJls {

    @Id
    @Column(length = 32, nullable = false)
    private String id; // 主键

    @Column(nullable = false, columnDefinition = "int default 0")
    private int isDel; // 是否删除(0:否,1:是)

    @Column(name = "add_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date addTime; // 添加时间

    @Column(name = "add_user", length = 50)
    private String addUser; // 添加人

    @Column(name = "add_user_name", length = 30)
    private String addUserName; // 添加人姓名

    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime; // 更新时间

    @Column(name = "update_user", length = 50)
    private String updateUser; // 更新人

    @Column(name = "update_user_name", length = 30)
    private String updateUserName; // 更新人姓名

    @Column(name = "pro_code", length = 50)
    private String proCode; // 所属省级代码

    @Column(name = "pro_name", length = 100)
    private String proName; // 所属省级名称

    @Column(name = "city_code", length = 50)
    private String cityCode; // 所属市级代码

    @Column(name = "city_name", length = 100)
    private String cityName; // 所属市级名称

    @Column(name = "reg_code", length = 50)
    private String regCode; // 区域代码

    @Column(name = "reg_name", length = 100)
    private String regName; // 区域名称

    @Column(name = "org_code", length = 50)
    private String orgCode; // 机构编号

    @Column(name = "org_name", length = 100)
    private String orgName; // 机构名称

    @Column(name = "jgrybm", length = 64, nullable = false)
    private String jgrybm; // 监管人员编码

    @Column(name = "rybh", length = 32, nullable = false)
    private String rybh; // 人员编号

    @Column(name = "ajbh", length = 50)
    private String ajbh; // 案件编号

    @Column(name = "ryzt", length = 32)
    private String ryzt; // 人员状态

    @Column(name = "xm", length = 32)
    private String xm; // 姓名

    @Column(name = "xmpy", length = 255)
    private String xmpy; // 姓名拼音

    @Column(name = "bm", length = 255)
    private String bm; // 别名

    @Column(name = "xb", length = 32)
    private String xb; // 性别

    @Column(name = "csrq")
    @Temporal(TemporalType.TIMESTAMP)
    private Date csrq; // 出生日期

    @Column(name = "zjlx", length = 32)
    private String zjlx; // 证件类型

    @Column(name = "zjhm", length = 32)
    private String zjhm; // 证件号码

    @Column(name = "mz", length = 32)
    private String mz; // 民族

    @Column(name = "gj", length = 32)
    private String gj; // 国籍

    @Column(name = "hyzk", length = 32)
    private String hyzk; // 婚姻状况

    @Column(name = "jg", length = 32)
    private String jg; // 籍贯

    @Column(name = "hjd", length = 32)
    private String hjd; // 户籍地

    @Column(name = "hjdxz", columnDefinition = "text")
    private String hjdxz; // 户籍地详址

    @Column(name = "xzz", length = 32)
    private String xzz; // 现住址

    @Column(name = "xzzxz", columnDefinition = "text")
    private String xzzxz; // 现住址详址

    @Column(name = "whcd", length = 32)
    private String whcd; // 文化程度

    @Column(name = "zzmm", length = 32)
    private String zzmm; // 政治面貌

    @Column(name = "zy", length = 32)
    private String zy; // 职业

    @Column(name = "gzdw", length = 64)
    private String gzdw; // 工作单位

    @Column(name = "zw", length = 32)
    private String zw; // 职务

    @Column(name = "zwjb", length = 32)
    private String zwjb; // 职务级别

    @Column(name = "jl", columnDefinition = "longtext")
    private String jl; // 简历

    @Column(name = "sf", length = 32)
    private String sf; // 身份

    @Column(name = "tssf", length = 32)
    private String tssf; // 特殊身份

    @Column(name = "jkzk", length = 32)
    private String jkzk; // 健康状况

    @Column(name = "sg", length = 255)
    private String sg; // 身高

    @Column(name = "tz", length = 255)
    private String tz; // 体重

    @Column(name = "zc", length = 255)
    private String zc; // 足长

    @Column(name = "tbtsbj", columnDefinition = "text")
    private String tbtsbj; // 体表特殊标记

    @Column(name = "wffzjl", length = 32)
    private String wffzjl; // 违法犯罪经历

    @Column(name = "wfsj")
    @Temporal(TemporalType.TIMESTAMP)
    private Date wfsj; // 违法时间

    @Column(name = "wfdd", length = 64)
    private String wfdd; // 违法地点

    @Column(name = "ajlb", length = 32)
    private String ajlb; // 案件类别

    @Column(name = "jyaq", columnDefinition = "longtext")
    private String jyaq; // 简要案情

    @Column(name = "jdjg", length = 255)
    private String jdjg; // 决定机关

    @Column(name = "jlqx", length = 255)
    private String jlqx; // 拘留期限

    @Column(name = "jlqsrq")
    @Temporal(TemporalType.TIMESTAMP)
    private Date jlqsrq; // 拘留起始日期

    @Column(name = "jljzrq")
    @Temporal(TemporalType.TIMESTAMP)
    private Date jljzrq; // 拘留截止日期

    @Column(name = "rssj")
    @Temporal(TemporalType.TIMESTAMP)
    private Date rssj; // 入所时间

    @Column(name = "rsyy", length = 32)
    private String rsyy; // 入所原因

    @Column(name = "sypz", length = 32)
    private String sypz; // 收押凭证

    @Column(name = "sypzwsh", length = 64)
    private String sypzwsh; // 收押凭证文书号

    @Column(name = "sydw", length = 255)
    private String sydw; // 送押单位

    @Column(name = "syr", length = 32)
    private String syr; // 送押人

    @Column(name = "lxfs", length = 64)
    private String lxfs; // 联系方式

    @Column(name = "yglb", length = 32)
    private String yglb; // 严管类别

    @Column(name = "fxdj", length = 32)
    private String fxdj; // 风险等级

    @Column(name = "jsh", length = 64)
    private String jsh; // 监室号

    @Column(name = "sbfh", length = 32)
    private String sbfh; // 识别服号

    @Column(name = "cwh", length = 32)
    private String cwh; // 床位号

    @Column(name = "zzczjg", length = 32)
    private String zzczjg; // 最终处置结果

    @Column(name = "sfhs", length = 64)
    private String sfhs; // 身份核实

    @Column(name = "dabh", length = 255)
    private String dabh; // 档案编号

    @Column(name = "fwbh", length = 255)
    private String fwbh; // 附物编号

    @Column(name = "cssj")
    @Temporal(TemporalType.TIMESTAMP)
    private Date cssj; // 出所时间

    @Column(name = "csyy", length = 32)
    private String csyy; // 出所原因

    @Column(name = "csqx", length = 255)
    private String csqx; // 出所去向

    @Column(name = "jbr", length = 32)
    private String jbr; // 经办人

    @Column(name = "jbsj")
    @Temporal(TemporalType.TIMESTAMP)
    private Date jbsj; // 经办时间

    @Column(name = "dwdm", length = 64)
    private String dwdm; // 单位代码

    @Column(name = "bz", columnDefinition = "text")
    private String bz; // 备注

    @Column(name = "bgr", length = 64)
    private String bgr; // 变更人

    @Column(name = "bgsj")
    @Temporal(TemporalType.TIMESTAMP)
    private Date bgsj; // 变更时间

    @Column(name = "gyqx")
    @Temporal(TemporalType.TIMESTAMP)
    private Date gyqx; // 关押期限

    @Column(name = "front_photo", length = 100)
    private String frontPhoto; // 正面照片

    @Column(name = "left_photo", length = 100)
    private String leftPhoto; // 左侧照片

    @Column(name = "right_photo", length = 100)
    private String rightPhoto; // 右侧照片

    @Column(name = "room_name", length = 32)
    private String roomName; // 监室名称


    @Column(name = "area_id", length = 32)
    private String areaId;

}
