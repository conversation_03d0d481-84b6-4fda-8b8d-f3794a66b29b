package com.rs.module.integ.service;

import com.rs.module.integ.domain.po.PdmsPrisonerKss;
import com.rs.module.integ.domain.request.DataShareRequest;
import com.rs.module.integ.domain.response.LegalPlatformBaseResponse;
import com.rs.module.integ.domain.response.LegalPlatformJlsDataShareResponse;
import com.rs.module.integ.domain.response.LegalPlatformKssDataShareResponse;
import com.rs.module.integ.domain.response.PrisonerKssResponse;
import com.rs.module.integ.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DataShareService {

    @Value("${legal-platform-service.data-share.url:http://14.66.127.82:3254}")
    private String baseUrl;

    private final RestTemplate restTemplate;

    private final ObjectMapper objectMapper; // 声明 ObjectMapper

    public DataShareService() {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper(); // 初始化 ObjectMapper
    }

    public List<PdmsPrisonerKss> getData(DataShareRequest request) {
        try {
            String url = baseUrl + "/dataShare/data";
            log.info("开始调用数据共享接口，URL: {}, 请求参数: {}", url, request);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<DataShareRequest> entity = new HttpEntity<>(request, headers);
//            LegalPlatformBaseResponse<LegalPlatformKssDataShareResponse> response = restTemplate.postForObject(url, entity, LegalPlatformBaseResponse.class);

            // 使用 ResponseEntity 来获取完整的响应
            ResponseEntity<LegalPlatformBaseResponse<LegalPlatformKssDataShareResponse>> responseEntity =
                    restTemplate.exchange(url, HttpMethod.POST, entity,
                            new ParameterizedTypeReference<LegalPlatformBaseResponse<LegalPlatformKssDataShareResponse>>() {});

            LegalPlatformBaseResponse<LegalPlatformKssDataShareResponse> response = responseEntity.getBody();
            List<PrisonerKssResponse> prisonerKssResponses = response.getData().getJbxx();

// 反序列化
//            LegalPlatformKssDataShareResponse response = objectMapper.readValue(responseBody, LegalPlatformKssDataShareResponse.class);
            log.info("数据共享接口调用成功，响应结果: {}", response);
            return this.convertBatchToPdmsData(prisonerKssResponses);
        } catch (Exception e) {
            log.error("数据共享接口调用失败", e);
            throw new RuntimeException("数据共享接口调用失败: " + e.getMessage());
        }
    }

    public LegalPlatformBaseResponse<LegalPlatformJlsDataShareResponse> getJlsData(DataShareRequest request) {
        try {
            String url = baseUrl + "/dataShare/jlsData";
            log.info("开始调用拘留所数据接口，URL: {}, 请求参数: {}", url, request);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<DataShareRequest> entity = new HttpEntity<>(request, headers);
            LegalPlatformJlsDataShareResponse response = restTemplate.postForObject(url, entity, LegalPlatformJlsDataShareResponse.class);

            log.info("拘留所数据接口调用成功，响应结果: {}", response);
            return LegalPlatformBaseResponse.success(response);
        } catch (Exception e) {
            log.error("拘留所数据接口调用失败", e);
            throw new RuntimeException("拘留所数据接口调用失败: " + e.getMessage());
        }
    }

    private List<PdmsPrisonerKss> convertBatchToPdmsData(List<PrisonerKssResponse> prisonerKssResponses) {
        return Optional.ofNullable(prisonerKssResponses)
                .orElse(Collections.emptyList())
                .stream()
                .map(this::convertToPdmsData)
                .collect(Collectors.toList());
    }

    private PdmsPrisonerKss convertToPdmsData(PrisonerKssResponse prisonerKssResponse) {
        return PdmsPrisonerKss.builder()
                .id(prisonerKssResponse.getId())
                .rybh(prisonerKssResponse.getRybh())
                .wbrybh(prisonerKssResponse.getWbrybh())
                .xm(prisonerKssResponse.getXm())
                .mz(prisonerKssResponse.getMz())
                .xb(prisonerKssResponse.getXb())
                .zjlx(prisonerKssResponse.getZjlx())
                .zjh(prisonerKssResponse.getZjh())
                .bm(prisonerKssResponse.getBm())
                .csrq(prisonerKssResponse.getCsrq())
                .hyzk(prisonerKssResponse.getHyzk())
                .gj(prisonerKssResponse.getGj())
                .jg(prisonerKssResponse.getJg())
                .hjd(prisonerKssResponse.getHjd())
                .hjdxz(prisonerKssResponse.getHjdxz())
                .xzd(prisonerKssResponse.getXzd())
                .xzdxz(prisonerKssResponse.getXzdxz())
                .rsrq(prisonerKssResponse.getRsrq())
                .cssj(prisonerKssResponse.getCssj())
                .jyaq(prisonerKssResponse.getJyaq())
                .badw(prisonerKssResponse.getBadw())
                // 时间类型统一
                .gyqx(DateUtil.toLocalDateTime(prisonerKssResponse.getGyqx()))
                .jkzk(prisonerKssResponse.getJkzk())
                .rygllb(prisonerKssResponse.getRygllb())
                .wxdj(prisonerKssResponse.getWxdj())
                .bahj(prisonerKssResponse.getBahj())
                .ay(prisonerKssResponse.getAy())
                .rsxz(prisonerKssResponse.getRsxz())
                .dwlx(prisonerKssResponse.getDwlx())
                .zwbh(prisonerKssResponse.getZwbh())
                .jsh(prisonerKssResponse.getJsh())
                .createtime(prisonerKssResponse.getCreatetime())
                .updatetime(prisonerKssResponse.getUpdatetime())
                .state(prisonerKssResponse.getState())
                .jsbh(prisonerKssResponse.getJsbh())
                .shid(prisonerKssResponse.getShid())
                .zxtzssdrq(prisonerKssResponse.getZxtzssdrq())
                .cljgrq(prisonerKssResponse.getCljgrq())
                .jbr(prisonerKssResponse.getJbr())
                .jbrq(prisonerKssResponse.getJbrq())
                .csyy(prisonerKssResponse.getCsyy())
                .whcd(prisonerKssResponse.getWhcd())
                .zy(prisonerKssResponse.getZy())
                .zw(prisonerKssResponse.getZw())
                .tssf(prisonerKssResponse.getTssf())
                .zc(prisonerKssResponse.getZc())
                .sf(prisonerKssResponse.getSf())
                .gzdw(prisonerKssResponse.getGzdw())
                .zzmm(prisonerKssResponse.getZzmm())
                .sydw(prisonerKssResponse.getSydw())
                .dah(prisonerKssResponse.getDah())
                .snbh(prisonerKssResponse.getSnbh())
                .zuc(prisonerKssResponse.getZuc())
                .sg(prisonerKssResponse.getSg())
                .bhlx(prisonerKssResponse.getBhlx())
                .syr(prisonerKssResponse.getSyr())
                .sy(prisonerKssResponse.getSy())
                .sypzwsh(prisonerKssResponse.getSypzwsh())
                .sypz(prisonerKssResponse.getSypz())
                .jyrq(prisonerKssResponse.getJyrq())
                .xhay(prisonerKssResponse.getXhay())
                .fzjl(prisonerKssResponse.getFzjl())
                .zxf(prisonerKssResponse.getZxf())
                .caaj(prisonerKssResponse.getCaaj())
                .cylx(prisonerKssResponse.getCylx())
                .jlrq(prisonerKssResponse.getJlrq())
                .dbrq(prisonerKssResponse.getDbrq())
                .scqsrq(prisonerKssResponse.getScqsrq())
                .ysfyrq(prisonerKssResponse.getYsfyrq())
                .csqx(prisonerKssResponse.getCsqx())
                .xq(prisonerKssResponse.getXq())
                .cljg(prisonerKssResponse.getCljg())
                .fjx(prisonerKssResponse.getFjx())
                .jcqk(prisonerKssResponse.getJcqk())
                .ykss(prisonerKssResponse.getYkss())
                .lsyy(prisonerKssResponse.getLsyy())
                .zszt(prisonerKssResponse.getZszt())
                .lscsyy(prisonerKssResponse.getLscsyy())
                .zyryxgqk(prisonerKssResponse.getZyryxgqk())
                .emlx(prisonerKssResponse.getEmlx())
                .sykzrq(prisonerKssResponse.getSykzrq())
                .jsly(prisonerKssResponse.getJsly())
                .tabh(prisonerKssResponse.getTabh())
                .yfh(prisonerKssResponse.getYfh())
                .cwh(prisonerKssResponse.getCwh())
                .tbtsbj(prisonerKssResponse.getTbtsbj())
                .pjzm(prisonerKssResponse.getPjzm())
                .je(prisonerKssResponse.getJe())
                .lkdj(prisonerKssResponse.getLkdj())
                .xzje(prisonerKssResponse.getXzje())
                .ajbh(prisonerKssResponse.getAjbh())
                .yfhbs(prisonerKssResponse.getYfhbs())
                .thcs(prisonerKssResponse.getThcs())
                .ajxz(prisonerKssResponse.getAjxz())
                .bardh(prisonerKssResponse.getBardh())
                .build();
    }
}