package com.rs.module.integ.constant;

public interface LegalPlatformDataShareTypeConstants {

    /**
     * 基本信息
     */
    String jbxx = "jbxx";

    /**
     * 临时出所
     */
    String lscs = "lscs";

    /**
     * 耳目登记
     */
    String emdj = "emdj";

    /**
     * 监区信息
     */
    String jq = "jq";

    /**
     * 监室信息
     */
    String js = "js";

    /**
     * 家属会见
     */
    String jshj = "jshj";

    /**
     * 监室调整
     */
    String jstz = "jstz";

    /**
     * 律师会见
     */
    String lshj = "lshj";

    /**
     * 民警基本信息
     */
    String mjjbxx = "mjjbxx";

    /**
     * 铺位管理
     */
    String pwgl = "pwgl";

    /**
     * 商品信息
     */
    String spxx = "spxx";

    /**
     * 所外就医
     */
    String swjy = "swjy";

    /**
     * 谈话教育
     */
    String thjy = "thjy";

    /**
     * 提解登记
     */
    String tjdj = "tjdj";

    /**
     * 提讯登记
     */
    String txdj = "txdj";

    /**
     * 物品接收
     */
    String wpjs = "wpjs";

    /**
     * 械具使用
     */
    String xjsy = "xjsy";

    /**
     * 现金接收
     */
    String xjjs = "xjjs";

    /**
     * 延期
     */
    String yq = "yq";

    /**
     * 值日安排
     */
    String zrap = "zrap";

    /**
     * 人员值日安排
     */
    String ryzrap = "ryzrap";

    /**
     * 同案犯
     */
    String taf = "taf";

    /**
     * 人员照片
     */
    String photos = "photos";

    /**
     * 集体教育
     */
    String jtjy = "jtjy";

    /**
     * 风险评估
     */
    String fxpg = "fxpg";

    /**
     * 判决登记
     */
    String pjdj = "pjdj";

    /**
     * 字典表
     */
    String dictionary = "dictionary";

    /**
     * 民警照片
     */
    String mjzp = "mjzp";

    /**
     * 民警人脸
     */
    String m_face = "m_face";

    /**
     * 民警虹膜
     */
    String m_iris = "m_iris";

    /**
     * 虹膜
     */
    String hm = "hm";

    /**
     * 指纹
     */
    String zhiwxx = "zhiwxx";

    /**
     * 三面照
     */
    String rxxx = "rxxx";

    /**
     * 环节变动
     */
    String hjbd = "hjbd";

    /**
     * 律师预约
     */
    String lsyy = "lsyy";

    /**
     * 健康情况
     */
    String jkqk = "jkqk";



}
