package com.rs.module.integ.domain.response;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class PrisonerJlsPreInResponse {

    /** ID */
    private String id;

    /** 监所编号 */

    private String jsbh;

    /** 人员编号 */

    private String rybh;

    /** 填表人 */

    private String tbr;

    /** 填表日期 */

    private LocalDateTime tbrq;

    /** 网办人员编号 */

    private String wbrybh;

    /** 过程编号 */

    private String gcbh;

    /** 业务流程ID */

    private String ywlcid;

    /** 业务进程ID */

    private String taskid;

    /** 所内编号 */

    private String snbh;

    /** 拘室号 */

    private String jsh;

    /** 姓名 */

    private String xm;

    /** 姓名拼音 */

    private String xmpy;

    /** 姓名拼音首字母 */

    private String xmpyszm;

    /** 别名 */

    private String bm;

    /** 别名同音 */

    private String bmty;

    /** 性别(XB) */
    private String xb;

    /** 入所日期 */

    private LocalDateTime rsrq;

    /** 证件类型(ZJLX) */

    private String zjlx;

    /** 证件号 */

    private String zjh;

    /** 国籍(GJ) */

    private String gj;

    /** 文化程度(WHCD) */

    private String whcd;

    /** 身份(SF) */

    private String sf;

    /** 特殊身份(TSSF) */

    private String tssf;

    /** 民族(MZ) */

    private String mz;

    /** 政治面貌(ZZMM) */

    private String zzmm;

    /** 出生日期 */

    private LocalDateTime csrq;

    /** 婚姻状况(HYZK) */

    private String hyzk;

    /** 户籍地(XZQH) */

    private String hjd;

    /** 户籍地详址 */

    private String hjdxz;

    /** 籍贯(XZQH) */

    private String jg;

    /** 现住地(XZQH) */

    private String xzd;

    /** 现住地详址 */

    private String xzdxz;

    /** 工作单位 */

    private String gzdw;

    /** 职业(ZY) */

    private String zy;

    /** 拘留决定机关 */

    private String jljdjg;

    /** 办案单位（送案单位） */

    private String badw;

    /** 办案人 */

    private String bar;

    /** 入所性质(JLSRJYY)（收拘类别） */

    private String rsxz;

    /** 收押人（收拘民警） */

    private String syr;

    /** 收押凭证(SYPZ) */

    private String sypz;

    /** 收押凭证文书号（收拘法律文书号） */

    private String sypzwsh;

    /** 案由(JLSAJLB) */

    private String ay;

    /** 办案人警号 */

    private String barjh;

    /** 办案民警电话 */

    private String bardh;

    /** 办案传真电话 */

    private String czdh;

    /** 关押天数（日） */

    private String gyts;

    /** 拘留日期（拘留开始日期） */

    private LocalDateTime jlrq;

    /** 关押期限（拘留结束日期） */

    private LocalDateTime gyqx;

    /** 涉毒尿检初查结果 */

    private String sdnjccjg;

    /** 涉毒尿检单位 */

    private String sdnjdw;

    /** 涉毒尿检初检时间 */

    private LocalDateTime sdnjcjsj;

    /** 涉毒尿检检查人 */

    private String sdnjjcr;

    /** 简要案情 */

    private String jyaq;

    /** 档案号 */

    private String dah;

    /** 派综人员编号 */

    private String pzrybh;

    /** 派综案件编号 */

    private String pzajbh;

    /** 派综法律文书号 */

    private String pzflwsh;

    /** 删除状态(STATE) */

    private String state;

    /** 操作状态(JLSCZZT) */

    private String czzt;

    /** 拒收理由 */

    private String jsly;

    /** 不宜拘留文书号 */

    private String byjswsh;

    /** 拒收日期 */

    private LocalDateTime jsrq;

    /** 创建人 */

    private String creator;

    /** 更新人 */

    private String updator;

    /** 重点在押(ZDRY) */

    private String zdzy;

    /** 同案编号 */

    private String tabh;

    /** 备注 */

    private String bz;

    /** 送押单位(DW) */

    private String sydw;

    /** 单位类型 */

    private String dwlx;

    /** 手环id */

    private String shid;

    /** 回执法律文书号 */

    private String hzflwsh;

    private LocalDateTime createtime;

    private LocalDateTime updatetime;
}
