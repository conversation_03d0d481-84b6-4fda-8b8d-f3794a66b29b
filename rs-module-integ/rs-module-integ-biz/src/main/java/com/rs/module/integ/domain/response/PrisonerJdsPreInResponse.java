package com.rs.module.integ.domain.response;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class PrisonerJdsPreInResponse {
    /** ID */
    private String id;

    /** 监所编号 */

    private String jsbh;

    /** 行政档案编号 */

    private String xzdabh;

    /** 人员编号 */

    private String rybh;

    /** 所内编号 */

    private String snbh;

    /** 戒室号(JSH) */

    private String jsh;

    /** 决定机关 */

    private String jdjg;

    /** 戒毒决字 */

    private String jdjz;

    /** 戒毒字号 */

    private String jdzh;

    /** 法律文书号 */

    private String flwsh;

    /** 姓名 */

    private String xm;

    /** 别名 */

    private String bm;

    /** 姓名拼音 */

    private String xmpy;

    /** 证件类型(ZJLX) */

    private String zjlx;

    /** 证件号码 */

    private String zjhm;

    /** 性别(XB) */

    private String xb;

    /** 出生日期(yyyy-mm-dd) */

    private LocalDateTime csrq;

    /** 民族(MZ) */

    private String mz;

    /** 政治面貌(ZZMM) */

    private String zzmm;

    /** 文化程度(WHCD) */

    private String whcd;

    /** 婚姻状况(HYZK) */

    private String hyzk;

    /** 身份(SF) */

    private String sf;

    /** 特殊身份(TSSF) */

    private String tssf;

    /** 国籍(GJ) */

    private String gj;

    /** 籍贯(XZQH) */

    private String jg;

    /** 职业(ZY) */

    private String zy;

    /** 户籍所在地(XZQH) */

    private String hjszd;

    /** 户籍地详址 */

    private String hjdxz;

    /** 现住址区划(XZQH) */

    private String xzzqh;

    /** 现住址详址 */

    private String xzzxz;

    /** 职务(ZW) */

    private String zw;

    /** 工作单位 */

    private String gzdw;

    /** 入所日期(yyyy-mm-dd hh:ii:ss) */

    private LocalDateTime rsrq;

    /** 入所原因(JDSRSYY) */

    private String rsyy;

    /** 办案单位(BADW) */

    private String badw;

    /** 办案人 */

    private String bar;

    /** 办案人联系电话 */

    private String barlxdh;

    /** 吸毒史 */

    private String xds;

    /** 毒品种类1 */

    private String dpzl1;

    /** 毒品种类2 */

    private String dpzl2;

    /** 毒品种类3 */

    private String dpzl3;

    /** 吸毒方式1 */

    private String xdfs1;

    /** 吸毒方式2 */

    private String xdfs2;

    /** 吸毒方式3 */

    private String xdfs3;

    /** 吸食剂量1 */

    private String xsjl1;

    /** 吸食剂量2 */

    private String xsjl2;

    /** 吸食剂量3 */

    private String xsjl3;

    /** 月收入 */

    private String ysr;

    /** 首次吸毒时间(yyyy-mm-dd) */

    private LocalDateTime scxdsj;

    /** 毒品来源 */

    private String dply;

    /** 戒毒方法(JDFF) */

    private String jdff;

    /** 复吸标志(FXBZ) */

    private String fxbz;

    /** 戒毒期限(yyyy-mm-dd) */

    private LocalDateTime jdqx;

    /** 戒毒开始日期(yyyy-mm-dd) */

    private LocalDateTime jdksrq;

    /** 戒毒结束日期(yyyy-mm-dd) */

    private LocalDateTime jdjsrq;

    /** 拘留天数 */

    private String jlts;

    /** 拘留开始日期(yyyy-mm-dd) */

    private LocalDateTime jlksrq;

    /** 拘留截至日期(yyyy-mm-dd) */

    private LocalDateTime jljzrq;

    /** 犯罪经历 */

    private String fzjl;

    /** 其他并行处罚(ZAAJ) */

    private String qtbxcf;

    /** 简要案情 */

    private String jyaq;

    /** 预计出所时间(yyyy-mm-dd) */

    private LocalDateTime yjcssj;

    /** 出所时间 */

    private LocalDateTime cssj;

    /** 出所原因(CSYY) */

    private String csyy;

    /** 出所去向 */

    private String csqx;

    /** 备注 */

    private String bz;

    /** 业务流程ID */

    private String ywlcid;

    /** 任务ID */

    private String taskid;

    /** 人员状态(STATE) */

    private String state;

    /** 上传标志(SHFO) */

    private String scbz;

    /** 操作人 */

    private String operator;

    /** 创建人 */

    private String creator;

    /** 更新人 */

    private String updator;

    /** 限制会见案件(0：不限制，1：限制) */

    private String xzhjaj;

    /** 类别 */

    private String lb;

    /** 审批单位 */

    private String spdw;

    /** 转入单位(BADW) */

    private String zrdw;

    /** 审批人 */

    private String spr;

    /** 档案编号 */

    private String dah;

    /** 重刑犯(ZXF) */

    private String zxf;

    /** 同案编号 */

    private String tafh;

    /** 指纹编号 */

    private String zwbh;

    /** 专长(ZC) */

    private String zc;

    /** 收治凭证(JDSSZPZ) */

    private String szpz;

    /** 羁押日期(yyyy-mm-dd) */

    private LocalDateTime jyrq;

    /** 拘留日期(yyyy-mm-dd  hh:ii:ss) */

    private LocalDateTime jlrq;

    /** 逮捕日期(yyyy-mm-dd  hh:ii:ss) */

    private LocalDateTime dbrq;

    /** 送戒单位(BADW) */

    private String sjdw;

    /** 送戒人 */

    private String sjr;

    /** 办案单位电话 */

    private String badwdh;

    /** 涉嫌案由(JDSAY) */

    private String sxay;

    /** 关押期限(yyyy-mm-dd) */

    private LocalDateTime gyqx;

    /** 附物编号 */

    private String fwbh;

    /** 涉毒尿检（或毛发检测）初查结果 */
    private String sdnjccjg;

    /** 涉毒尿检（或毛发检测）单位 */
    private String sdnjdw;

    /** 涉毒尿检初检时间(yyyy-mm-dd) */

    private LocalDateTime sdnjcjsj;

    /** 涉毒尿检检查人 */

    private String sdnjjcr;

    /** 本人电话 */

    private String brdh;

    /** 联系电话 */

    private String lxdh;

    /** 其他行政处罚 */

    private String qtxzcf;

    /** 健康情况(JKZK) */

    private String jkzk;

    /** 吸毒种类(XDZL) */

    private String xdzl;

    /** 办案单位类型(BADWLX) */

    private String badwlx;

    /** 当前办案单位(DQBADW) */

    private String dqbadw;

    /** 当前办案环节(DQBAHJ) */

    private String dqbahj;

    /** 成员类型(CYLX) */

    private String cylx;

    /** 戒室分配(JSFP) */

    private String jsfp;

    /** 分配床位 */

    private String fpcw;

    /** 分配识别服 */

    private String fpsbf;

    /** 带入戒室人 */

    private String drjsr;

    /** 带入戒室时间((yyyy-mm-dd  hh:ii:ss)) */

    private LocalDateTime drjssj;

    /** 家属姓名 */

    private String jsxm;

    /** 关系(GX) */

    private String gx;

    /** 家属联系电话 */

    private String jslxdh;

    /** 吸毒年限 */

    private String xdnx;

    /** 吸毒方式(XDFS) */

    private String xdfs;

    /** 第几次强戒 */

    private String qjcs;

    /** 办案人证件号 */

    private String barzjh;

    /** 入所文书 */

    private String rsws;

    /** 班级编号 */

    private String bjbh;

    /** 吸食场所 */

    private String xscs;

    /** 操作状态(CZZT) */

    private String czzt;

    /** 过程编号 */

    private String gcbh;

    /** 暂不收押原因 */

    private String zbsyyy;

    /** 病号类型(BHLX) */

    private String bhlx;

    private LocalDateTime createtime;

    private LocalDateTime updatetime;
}
