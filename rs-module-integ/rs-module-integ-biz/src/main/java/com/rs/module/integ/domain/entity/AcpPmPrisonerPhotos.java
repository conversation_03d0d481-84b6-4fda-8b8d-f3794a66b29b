package com.rs.module.integ.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_prisoner_photos")
public class AcpPmPrisonerPhotos {
    @Id
    @Column(length = 32, nullable = false)
    private String id; // 主键

    @Column(nullable = false, columnDefinition = "int default 0")
    private int isDel; // 是否删除(0:否,1:是)

    @Column(name = "add_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date addTime; // 添加时间

    @Column(name = "add_user", length = 50)
    private String addUser; // 添加人

    @Column(name = "add_user_name", length = 30)
    private String addUserName; // 添加人姓名

    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime; // 更新时间

    @Column(name = "update_user", length = 50)
    private String updateUser; // 更新人

    @Column(name = "update_user_name", length = 30)
    private String updateUserName; // 更新人姓名

    @Column(name = "pro_code", length = 50)
    private String proCode; // 所属省级代码

    @Column(name = "pro_name", length = 100)
    private String proName; // 所属省级名称

    @Column(name = "city_code", length = 50)
    private String cityCode; // 所属市级代码

    @Column(name = "city_name", length = 100)
    private String cityName; // 所属市级名称

    @Column(name = "reg_code", length = 50)
    private String regCode; // 区域代码

    @Column(name = "reg_name", length = 100)
    private String regName; // 区域名称

    @Column(name = "org_code", length = 50)
    private String orgCode; // 机构编号

    @Column(name = "org_name", length = 100)
    private String orgName; // 机构名称

    @Column(name = "jsbh", length = 64)
    private String jsbh; // 监所编号

    @Column(name = "rybh", length = 64)
    private String rybh; // 人员编号

    @Column(columnDefinition = "text")
    private String photo; // 照片（base64）

    @Column(length = 64)
    private String type; // 位置(1正面，2左，3右)

    @Column(length = 64)
    private String ywzp; // 有无照片(YWYC)

    @Column(length = 64)
    private String typetemp; // 临时照片

    @Column(length = 64)
    private String state; // 删除状态(STATE) R2：有效 R3：无效

    @Column(length = 128)
    private String creator; // 创建人

    @Column(length = 128)
    private String updator; // 更新人

    @Column(name = "photourl", length = 500)
    private String photourl; // 照片URL

    @Column(name = "data_source", nullable = false)
    private int dataSource; // 数据来源 1：看守所 2：拘留所
}