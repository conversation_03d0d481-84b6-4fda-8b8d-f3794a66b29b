package com.rs.module.integ.service;

import com.alibaba.fastjson.JSON;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.base.enums.AreaTypeEnum;
import com.rs.module.base.service.pm.AreaService;
import com.rs.module.integ.domain.dto.DataShareReqVO;
import com.rs.module.integ.domain.dto.KssInRawDTO;
import com.rs.module.integ.entity.data.PrisonerKssRawInDO;
import com.rs.module.integ.service.sync.AbstractSyncLegalPlatform;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class KssRawInService extends AbstractSyncLegalPlatform<PrisonerKssRawInDO> {

    private final PrisonerKssRawInService prisonerKssRawInService;
    private final AreaService areaService;

    @Override
    public void incrementSync() {
        List<AreaDO> list = areaService.lambdaQuery()
                .eq(AreaDO::getAreaType, AreaTypeEnum.DETENTION_FACILITY.getCode())
                .in(AreaDO::getOrgCode,"110000113","110000121","110000112","110000114","110000131","110000151","110000116")
                .list();
        log.info("开始同步监所人员信息：{}", list);
        for (AreaDO areaDO : list) {
            try {
                DataShareReqVO dataShareReqVO = new DataShareReqVO();
                dataShareReqVO.setUrl("http://14.66.127.82:3254/dataShare/data");
                dataShareReqVO.setTypes("jbxx");
                dataShareReqVO.setState("R8");
                dataShareReqVO.setPageNum("1");
                dataShareReqVO.setPageSize("10000000");
                dataShareReqVO.setOrderField("createtime");
                dataShareReqVO.setCreateStartTime("2010-09-17 15:40:46");
                dataShareReqVO.setJsbh(areaDO.getOrgCode());
                String resp = executeHttp(dataShareReqVO);
                KssInRawDTO kssInRawDTO = JSON.parseObject(resp, KssInRawDTO.class);
                if (kssInRawDTO.isSuccess()) {
                    if (kssInRawDTO.getData() != null && kssInRawDTO.getData().getJbxx() != null) {
                        try {
                            List<PrisonerKssRawInDO> prisonerKssRawInDOList = kssInRawDTO.getData().getJbxx();
                            prisonerKssRawInService.lambdaUpdate()
                                    .eq(PrisonerKssRawInDO::getJsbh, (areaDO.getOrgCode()))
                                    .remove();
                            for (PrisonerKssRawInDO prisonerKssRawInDO : prisonerKssRawInDOList) {
                                prisonerKssRawInDO.setOrgCode(prisonerKssRawInDO.getJsbh());
                                prisonerKssRawInDO.setAddTime(new Date());
                                prisonerKssRawInDO.setUpdateTime(new Date());
                                prisonerKssRawInService.save(prisonerKssRawInDO);
                            }
                        } catch (Exception e) {
                            log.error("保存数据失败{}", e);
                        }
                    }
                }else {
                    log.error("请求数据失败");
                }
            } catch (Exception e) {
                log.error("同步原始看守所人员信息异常：{}", e);
            }
        }
    }

    @Override
    protected List<PrisonerKssRawInDO> getCreateDataListPage(LocalDateTime startCreateTime, long page, long pageSize) {
        return Collections.emptyList();
    }

    @Override
    protected List<PrisonerKssRawInDO> getUpdateDataListPage(LocalDateTime startUpdateTime, long page, long pageSize) {
        return Collections.emptyList();
    }

    @Override
    protected LocalDateTime handleDataAndReturnMaxCreateTime(List<PrisonerKssRawInDO> result) {
        return null;
    }

    @Override
    protected LocalDateTime handleDataAndReturnMaxUpdateTime(List<PrisonerKssRawInDO> result) {
        return null;
    }

    @Override
    protected LocalDateTime getTableLastCreateTime() {
        return null;
    }

    @Override
    protected boolean hasData() {
        return true;
    }

    @Override
    protected LocalDateTime getTableLastUpdateTime() {
        return null;
    }
}
