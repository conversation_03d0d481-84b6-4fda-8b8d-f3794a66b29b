package com.rs.module.integ.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.rs.module.integ.constant.PdmsDictionaryConstant;
import com.rs.module.integ.constant.PrisonDataSourceConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 拘留所/看守所同样的数据报文
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("法综-人员照片表同步")
public class PdmsPrisonerPhotos {

    /**
     * ID
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty("ID")
    private String id;

    /**
     * 监所编号
     */
    @ApiModelProperty("监所编号")
    private String jsbh;

    /**
     * 人员编号
     */
    @ApiModelProperty("人员编号")
    private String rybh;

    /**
     * 照片（base64） 目前不存
     */
    @JsonIgnore
    @ApiModelProperty("照片（base64） 目前不存")
    private String photo;

    /**
     * 位置(1正面，2左，3右)
     */
    @ApiModelProperty("位置(1正面，2左，3右)")
    private String type;

    /**
     * 有无照片(YWYC)
     */
    @ApiModelProperty("有无照片(YWYC)")
    private String ywzp;

    /**
     * 临时照片
     */
    @ApiModelProperty("临时照片")
    private String typetemp;

    /**
     * 删除状态(STATE) R2：有效 R3：无效
     */
    @ApiModelProperty("状态（STATE） 字典值：" + PdmsDictionaryConstant.STATE)
    private String state;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updator;

    /**
     * 照片URL
     */
    @ApiModelProperty("照片URL")
    private String photourl;

    /**
     * 创建时间(yyyy-MM-dd HH:mm:ss)
     */
    @ApiModelProperty("创建时间(yyyy-MM-dd HH:mm:ss)")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;

    /**
     * 数据来源
     *
     * @see PrisonDataSourceConstant
     */
    @ApiModelProperty("数据来源 1：看守所 2：拘留所")
    private Integer dataSource;
}
