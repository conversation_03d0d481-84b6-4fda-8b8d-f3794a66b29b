package com.rs.module.integ.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName KssJqDTO
 * @Description 看守所监区同步
 * <AUTHOR>
 * @Date 2025/7/29 23:52
 * @Version 1.0
 */
@NoArgsConstructor
@Data
public class KssJsDTO {


    @JsonProperty("code")
    private Integer code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("serverTime")
    private String serverTime;
    @JsonProperty("data")
    private DataDTO data;
    @JsonProperty("pages")
    private Integer pages;
    @JsonProperty("pageNum")
    private Integer pageNum;
    @JsonProperty("pageSize")
    private Integer pageSize;
    @JsonProperty("total")
    private Integer total;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JsonProperty("js")
        private List<JsDTO> js;

        @NoArgsConstructor
        @Data
        public static class JsDTO {
            @JsonProperty("id")
            private String id;
            @JsonProperty("jsh")
            private String jsh;
            @JsonProperty("jslb")
            private String jslb;
            @JsonProperty("jsmc")
            private String jsmc;
            @JsonProperty("jqh")
            private String jqh;
            @JsonProperty("state")
            private String state;
            @JsonProperty("xgmjjh")
            private String xgmjjh;
            @JsonProperty("zgmjjh")
            private String zgmjjh;
            @JsonProperty("createtime")
            private String createtime;
            @JsonProperty("updatetime")
            private String updatetime;
            @JsonProperty("jsbh")
            private String jsbh;
        }
    }
}
