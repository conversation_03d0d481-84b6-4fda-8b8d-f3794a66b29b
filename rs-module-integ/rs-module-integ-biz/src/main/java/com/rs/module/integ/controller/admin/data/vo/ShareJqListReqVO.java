package com.rs.module.integ.controller.admin.data.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 数据共享-监区列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ShareJqListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("第三方id")
    private String sysId;

    @ApiModelProperty("监区号")
    private String jqh;

    @ApiModelProperty("监区名称")
    private String jqmc;

    @ApiModelProperty("jqicons")
    private String jqicons;

    @ApiModelProperty("状态")
    private String state;

    @ApiModelProperty("监所编号")
    private String jsbh;

}
