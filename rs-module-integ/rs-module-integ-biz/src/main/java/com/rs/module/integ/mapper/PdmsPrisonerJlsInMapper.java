package com.rs.module.integ.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rs.module.integ.domain.po.PdmsPrisonerJlsIn;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;

@Mapper
public interface PdmsPrisonerJlsInMapper extends BaseMapper<PdmsPrisonerJlsIn> {

    /**
     * 获取当前表最新创建时间
     */
    LocalDateTime getTableLastCreateTime();

    /**
     * 获取当前表最新更新时间
     */
    LocalDateTime getTableLastUpdateTime();

}
