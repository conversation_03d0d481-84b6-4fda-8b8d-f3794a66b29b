package com.rs.module.integ.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 接口响应数据结构，用于接收执法平台的请求进行响应封装
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LegalPlatformBaseResponse<T> implements Serializable {

    private static final long serialVersionUID = 8518955172797076258L;

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    public final static int SUCCESS_CODE = 200;

    public final static String DEFAULT_SUCCESS_MESSAGE = "操作成功";

    public final static int FAIL_CODE = 500;

    public final static String DEFAULT_FAIL_MESSAGE = "操作失败";



    /**
     * 返回响应成功结果
     */
    public static <T> LegalPlatformBaseResponse<T> success(Integer code, String message, T data) {
        return LegalPlatformBaseResponse.<T>builder()
                .code(code)
                .message(message)
                .data(data)
                .build();
    }

    /**
     * 返回响应成功结果
     */
    public static <T> LegalPlatformBaseResponse<T> success(T data) {
        return success(SUCCESS_CODE, DEFAULT_SUCCESS_MESSAGE, data);
    }

    /**
     * 返回响应成功结果
     */
    public static <T> LegalPlatformBaseResponse<T> success(String message, T data) {
        return success(SUCCESS_CODE, message, data);
    }

    /**
     * 返回响应成功结果
     */
    public static <T> LegalPlatformBaseResponse<T> success() {
        return success(null);
    }

    // ---------------------------------------------------------------------------------------------------------------------------

    /**
     * 返回响应失败结果
     */
    public static <T> LegalPlatformBaseResponse<T> fail() {
        return fail(FAIL_CODE, DEFAULT_FAIL_MESSAGE, null);
    }

    /**
     * 返回响应失败结果
     */
    public static <T> LegalPlatformBaseResponse<T> fail(String message) {
        return fail(FAIL_CODE, message, null);
    }

    /**
     * 返回响应失败结果
     */
    public static <T> LegalPlatformBaseResponse<T> fail(int code, String message) {
        return fail(code, message, null);
    }

    /**
     * 返回响应失败结果
     */
    public static <T> LegalPlatformBaseResponse<T> fail(int code, String message, T data) {
        return LegalPlatformBaseResponse.<T>builder()
                .code(code)
                .message(message)
                .data(data)
                .build();
    }

}
