package com.rs.module.integ.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("执法平台字典表同步")
public class PdmsLegalPlatformDictionary {


    /**
     * ID
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty("ID")
    private String id;

    /**
     * 监所类型
     */
    @ApiModelProperty("监所类型")
    private String jslx;

    /**
     * 分类名
     */
    @ApiModelProperty("分类名")
    private String fieldname;

    /**
     * 代码
     */
    @ApiModelProperty("代码")
    private String code;

    /**
     * 内容
     */
    @ApiModelProperty("内容")
    private String content;

    /**
     * 拼音
     */
    @ApiModelProperty("拼音")
    private String py;

    /**
     * 是否是国标
     */
    @ApiModelProperty("是否是国标")
    private String isgb;

    /**
     * 使用频率
     */
    @ApiModelProperty("使用频率")
    private Integer sypl;

    /**
     * 是否可编辑
     */
    @ApiModelProperty("是否可编辑")
    private String editable;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updator;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createtime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updatetime;
}
