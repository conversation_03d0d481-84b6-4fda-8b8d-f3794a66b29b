package com.rs.module.integ.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.rs.module.integ.constant.PdmsDictionaryConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("法综-全量人员视图表")
public class PdmsPrisoner {

    /**
     * 同步法综数据表的主键id
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty("同步法综数据表的主键id")
    private String id;

    /**
     * 人员编号（业务平台唯一标识）
     */
    @ApiModelProperty("人员编号（业务平台唯一标识）")
    private String rybh;

    /**
     * 网办人员编号 (法制系统唯一编号)
     */
    @ApiModelProperty("网办人员编号 (法制系统唯一编号)")
    private String wbrybh;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String xm;

    /**
     * 民族 字典值
     * <p>
     *
     * @see PdmsDictionaryConstant#MZ
     */
    @ApiModelProperty("民族 字典值：" + PdmsDictionaryConstant.MZ)
    private String mz;

    /**
     * 性别 字典值
     *
     * @see PdmsDictionaryConstant#XB
     */
    @ApiModelProperty("性别 字典值：" + PdmsDictionaryConstant.XB)
    private String xb;

    /**
     * 证件类型 字典值
     *
     * @see PdmsDictionaryConstant#ZJLX
     */
    @ApiModelProperty("证件类型 字典值：" + PdmsDictionaryConstant.ZJLX)
    private String zjlx;

    /**
     * 证件号
     */
    @ApiModelProperty("证件号")
    private String zjh;

    /**
     * 别名
     */
    @ApiModelProperty("别名")
    private String bm;

    /**
     * 出生日期
     */
    @ApiModelProperty("出生日期")
    private LocalDateTime csrq;

    /**
     * 婚姻状况
     *
     * @see PdmsDictionaryConstant#HYZK
     */
    @ApiModelProperty("婚姻状况 字典值：" + PdmsDictionaryConstant.HYZK)
    private String hyzk;

    /**
     * 国籍 字典值
     */
    @ApiModelProperty("国籍 字典值：" + PdmsDictionaryConstant.GJ)
    private String gj;

    /**
     * 籍贯 字典值
     */
    @ApiModelProperty("籍贯 字典值：" + PdmsDictionaryConstant.XZQH)
    private String jg;

    /**
     * 户籍地 字典
     */
    @ApiModelProperty("户籍地 字典值：" + PdmsDictionaryConstant.XZQH)
    private String hjd;

    /**
     * 户籍地详址
     */
    @ApiModelProperty("户籍地详址")
    private String hjdxz;

    /**
     * 现住地 字典
     */
    @ApiModelProperty("现住地 字典值：" + PdmsDictionaryConstant.XZQH)
    private String xzd;

    /**
     * 现住地详址
     */
    @ApiModelProperty("现住地详址")
    private String xzdxz;

    /**
     * 入所日期
     */
    @ApiModelProperty("入所日期")
    private LocalDateTime rsrq;

    /**
     * 出所时间
     */
    @ApiModelProperty("出所时间")
    private LocalDateTime cssj;

    /**
     * 简要案情
     */
    @ApiModelProperty("简要案情")
    private String jyaq;

    /**
     * 办案单位
     */
    @ApiModelProperty("办案单位")
    private String badw;

    /**
     * 关押期限
     */
    @ApiModelProperty("关押期限")
    private LocalDateTime gyqx;

    /**
     * 健康状况 字典值
     */
    @ApiModelProperty("健康状况 字典值：" + PdmsDictionaryConstant.JKZK)
    private String jkzk;

    /**
     * 人员管理类别 字典值
     */
    @ApiModelProperty("人员管理类别 字典值：" + PdmsDictionaryConstant.RYGLLB)
    private String rygllb;

    /**
     * 危险等级 字典值
     */
    @ApiModelProperty("危险等级 字典值：" + PdmsDictionaryConstant.FXDJ)
    private String wxdj;

    /**
     * 办案环节 字典值
     */
    @ApiModelProperty("办案环节 字典值：" + PdmsDictionaryConstant.BAJD)
    private String bahj;

    /**
     * 案由 字典值
     */
    @ApiModelProperty("案由 字典值：" + PdmsDictionaryConstant.AJLB)
    private String ay;

    /**
     * 入所性质（RSXZ） 字典值
     */
    @ApiModelProperty("入所性质（RSXZ） 字典值：" + PdmsDictionaryConstant.RSXZ)
    private String rsxz;

    /**
     * 办案单位类型 字典值
     */
    @ApiModelProperty("办案单位类型 字典值：" + PdmsDictionaryConstant.DWLX)
    private String dwlx;

    /**
     * 指纹编号
     */
    @ApiModelProperty("指纹编号")
    private String zwbh;

    /**
     * 监室号
     */
    @ApiModelProperty("监室号")
    private String jsh;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createtime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updatetime;

    /**
     * 状态（STATE） 字典值
     * <p>
     * 人员  R8-在押  R7-历史
     * 业务  R2-有效  R3-无效
     */
    @ApiModelProperty("状态（STATE） 字典值：" + PdmsDictionaryConstant.STATE)
    private String state;

    /**
     * 监所编号
     */
    @ApiModelProperty("监所编号")
    private String jsbh;

    /**
     * 手环id
     */
    @ApiModelProperty("手环id")
    private String shid;

    /**
     * 执行书送达日期
     */
    @ApiModelProperty("执行书送达日期")
    private LocalDateTime zxtzssdrq;

    /**
     * 处理结果日期
     */
    @ApiModelProperty("处理结果日期")
    private LocalDateTime cljgrq;

    /**
     * 经办人
     */
    @ApiModelProperty("经办人")
    private String jbr;

    /**
     * 经办日期
     */
    @ApiModelProperty("经办日期")
    private LocalDateTime jbrq;

    /**
     * 出所原因 字典值
     */
    @ApiModelProperty("出所原因 字典值：" + PdmsDictionaryConstant.CSYY)
    private String csyy;

    /**
     * 文化程度 字典值
     */
    @ApiModelProperty("文化程度 字典值：" + PdmsDictionaryConstant.WHCD)
    private String whcd;

    /**
     * 职业 字典值
     */
    @ApiModelProperty("职业 字典值：" + PdmsDictionaryConstant.ZY)
    private String zy;

    /**
     * 职务 字典值
     */
    @ApiModelProperty("职务 字典值：" + PdmsDictionaryConstant.ZW)
    private String zw;

    /**
     * 特殊身份
     */
    @ApiModelProperty("特殊身份 字典值：" + PdmsDictionaryConstant.TSSF)
    private String tssf;

    /**
     * 专长
     */
    @ApiModelProperty("专长 字典值：" + PdmsDictionaryConstant.ZC)
    private String zc;

    /**
     * 身份 字典值
     */
    @ApiModelProperty("身份 字典值：" + PdmsDictionaryConstant.SF)
    private String sf;

    /**
     * 工作单位
     */
    @ApiModelProperty("工作单位")
    private String gzdw;

    /**
     * 政治面貌 字典值
     */
    @ApiModelProperty("政治面貌 字典值：" + PdmsDictionaryConstant.ZZMM)
    private String zzmm;

    /**
     * 送押单位
     */
    @ApiModelProperty("送押单位")
    private String sydw;

    /**
     * 档案编号
     */
    @ApiModelProperty("档案编号")
    private String dah;

    /**
     * 人员所内编号
     */
    @ApiModelProperty("人员所内编号")
    private String snbh;

    /**
     * 足长
     */
    @ApiModelProperty("足长")
    private String zuc;

    /**
     * 身高
     */
    @ApiModelProperty("身高")
    private String sg;

    /**
     * 病号类型（BHLX） 字典值
     */
    @ApiModelProperty("病号类型（BHLX） 字典值：" + PdmsDictionaryConstant.BHLX)
    private String bhlx;

    /**
     * 送押人
     */
    @ApiModelProperty("送押人")
    private String syr;

    /**
     * 收押人
     */
    @ApiModelProperty("收押人")
    private String sy;

    /**
     * 收押凭证文书号
     */
    @ApiModelProperty("收押凭证文书号")
    private String sypzwsh;

    /**
     * 收押凭证（SYPZ） 字典值
     */
    @ApiModelProperty("收押凭证（SYPZ） 字典值：" + PdmsDictionaryConstant.SYPZ)
    private String sypz;

    /**
     * 羁押日期
     */
    @ApiModelProperty("羁押日期")
    private LocalDateTime jyrq;

    /**
     * 细化案由(AJLB) 字典值
     */
    @ApiModelProperty("细化案由(AJLB) 字典值：" + PdmsDictionaryConstant.AJLB)
    private String xhay;

    /**
     * 犯罪经历(WFFZ) 字典值
     */
    @ApiModelProperty("犯罪经历(WFFZ) 字典值：" + PdmsDictionaryConstant.WFFZ)
    private String fzjl;

    /**
     * 是否是重刑犯(ZXF) 0：否 1：是 字典值
     */
    @ApiModelProperty("是否是重刑犯(ZXF) 0：否 1：是 字典值：" + PdmsDictionaryConstant.BOOLEAN_TYPE)
    private String zxf;

    /**
     * 从案类型(AJLB) 字典值
     */
    @ApiModelProperty("从案类型(AJLB) 字典值：" + PdmsDictionaryConstant.AJLB)
    private String caaj;

    /**
     * 成员类型(CYLX) 字典值
     */
    @ApiModelProperty("成员类型(CYLX) 字典值：" + PdmsDictionaryConstant.CYLX)
    private String cylx;

    /**
     * 拘留日期
     */
    @ApiModelProperty("拘留日期")
    private LocalDateTime jlrq;

    /**
     * 逮捕日期
     */
    @ApiModelProperty("逮捕日期")
    private LocalDateTime dbrq;

    /**
     * 审查起诉日期
     */
    @ApiModelProperty("审查起诉日期")
    private LocalDateTime scqsrq;

    /**
     * 移送法院日期
     */
    @ApiModelProperty("移送法院日期")
    private LocalDateTime ysfyrq;

    /**
     * 出所去向
     */
    @ApiModelProperty("出所去向")
    private String csqx;

    /**
     * 刑期
     */
    @ApiModelProperty("刑期")
    private String xq;

    /**
     * 处理结果(CLJG) 字典值
     */
    @ApiModelProperty("处理结果(CLJG) 字典值：" + PdmsDictionaryConstant.CLJG)
    private String cljg;

    /**
     * 附加刑(FJX) 字典值
     */
    @ApiModelProperty("附加刑(FJX) 字典值：" + PdmsDictionaryConstant.FJX)
    private String fjx;

    /**
     * 奖惩情况
     */
    @ApiModelProperty("奖惩情况")
    private String jcqk;

    /**
     * 原看守所
     */
    @ApiModelProperty("原看守所")
    private String ykss;

    /**
     * 留所原因(LSYY) 字典值
     */
    @ApiModelProperty("原看守所 字典值：" + PdmsDictionaryConstant.LSYY)
    private String lsyy;

    /**
     * 在所状态(ZSZT) 12,13都属于已决 字典值
     */
    @ApiModelProperty("在所状态(ZSZT) 12,13都属于已决 字典值：" + PdmsDictionaryConstant.ZSZT)
    private String zszt;

    /**
     * 临时出所原因(LSCS) 字典值
     */
    @ApiModelProperty("临时出所原因(LSCS) 字典值：" + PdmsDictionaryConstant.LSCS)
    private String lscsyy;

    /**
     * 在押人员相关情况 (ZYRYXGQK) 字典值
     */
    @ApiModelProperty("在押人员相关情况 (ZYRYXGQK) 字典值：" + PdmsDictionaryConstant.ZYRYXGQK)
    private String zyryxgqk;

    /**
     * 耳目类型(EMLX) 字典值
     */
    @ApiModelProperty("耳目类型(EMLX) 字典值：" + PdmsDictionaryConstant.EMLX)
    private String emlx;

    /**
     * 收押开证日期(经办日期)(yyyy-MM-dd HH:mm:ss)
     */
    @ApiModelProperty("收押开证日期(经办日期)(yyyy-MM-dd HH:mm:ss)")
    private LocalDateTime sykzrq;

    /**
     * 拒收理由(入所健康检查结果)
     */
    @ApiModelProperty("拒收理由(入所健康检查结果)")
    private String jsly;

    /**
     * 同案编号
     */
    @ApiModelProperty("同案编号")
    private String tabh;

    /**
     * 衣服号
     */
    @ApiModelProperty("衣服号")
    private String yfh;

    /**
     * 床位号
     */
    @ApiModelProperty("床位号")
    private String cwh;

    /**
     * 体表特殊标记
     */
    @ApiModelProperty("体表特殊标记")
    private String tbtsbj;

    /**
     * 判决罪名
     */
    @ApiModelProperty("判决罪名")
    private String pjzm;

    /**
     * 金额
     */
    @ApiModelProperty("金额")
    private BigDecimal je;

    /**
     * 列控等级（LKDJ）字典值
     */
    @ApiModelProperty("列控等级（LKDJ）字典值：" + PdmsDictionaryConstant.LKDJ)
    private String lkdj;

    /**
     * 限制消费金额
     */
    @ApiModelProperty("限制消费金额")
    private String xzje;

    /**
     * 案件编号
     */
    @ApiModelProperty("案件编号")
    private String ajbh;

    /**
     * 衣服号标识
     */
    @ApiModelProperty("衣服号标识")
    private String yfhbs;

    /**
     * 谈话次数
     */
    @ApiModelProperty("谈话次数")
    private String thcs;

    /**
     * 案件性质
     */
    @ApiModelProperty("案件性质")
    private String ajxz;
    /**
     * 办案人员联系方式
     */
    @ApiModelProperty("办案人员联系方式")
    private String bardh;

    // ----------------------------------------------------拘留所字段(部分，按需添加)-----------------------------------------------------------

    /**
     * 拘留决定机关
     */
    @ApiModelProperty("拘留决定机关")
    private String jljdjg;

    /**
     * 办案人
     */
    @ApiModelProperty("办案人")
    private String bar;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String bz;

    /**
     * 关押天数
     */
    @ApiModelProperty("关押天数")
    private String gyts;

    // ----------------------------------------------------自定义数据字段------------------------------------------------------------

    /**
     * 左侧照片
     */
    @ApiModelProperty("左侧照片")
    private String leftPhoto;

    /**
     * 正面照片
     */
    @ApiModelProperty("正面照片")
    private String frontPhoto;

    /**
     * 右侧照片
     */
    @ApiModelProperty("右侧照片")
    private String rightPhoto;
}
