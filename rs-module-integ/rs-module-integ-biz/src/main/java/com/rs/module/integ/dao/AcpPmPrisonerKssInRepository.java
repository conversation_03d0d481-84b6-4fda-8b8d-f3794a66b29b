package com.rs.module.integ.dao;

import com.rs.module.integ.domain.entity.AcpPmPrisonerKssIn;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface AcpPmPrisonerKssInRepository extends JpaRepository<AcpPmPrisonerKssIn, String> {
    // 根据人员编号查询
    List<AcpPmPrisonerKssIn> findByRybh(String rybh);

    // 查询最大 add_time
    @Query("SELECT MAX(a.addTime) FROM AcpPmPrisonerKssIn a")
    Date findMaxAddTime();

    // 查询最大 update_time
    @Query("SELECT MAX(a.updateTime) FROM AcpPmPrisonerKssIn a")
    Date findMaxUpdateTime();
}