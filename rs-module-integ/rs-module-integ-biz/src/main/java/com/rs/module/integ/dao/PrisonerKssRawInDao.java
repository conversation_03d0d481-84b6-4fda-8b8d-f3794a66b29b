package com.rs.module.integ.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.integ.domain.vo.PrisonerKssRawInListReqVO;
import com.rs.module.integ.domain.vo.PrisonerKssRawInPageReqVO;
import com.rs.module.integ.entity.data.PrisonerKssRawInDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 看守所原始在押人员信息 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PrisonerKssRawInDao extends IBaseDao<PrisonerKssRawInDO> {


    default PageResult<PrisonerKssRawInDO> selectPage(PrisonerKssRawInPageReqVO reqVO) {
        Page<PrisonerKssRawInDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrisonerKssRawInDO> wrapper = new LambdaQueryWrapperX<PrisonerKssRawInDO>()
            .eqIfPresent(PrisonerKssRawInDO::getRybh, reqVO.getRybh())
            .eqIfPresent(PrisonerKssRawInDO::getXm, reqVO.getXm())
            .eqIfPresent(PrisonerKssRawInDO::getMz, reqVO.getMz())
            .eqIfPresent(PrisonerKssRawInDO::getXb, reqVO.getXb())
            .eqIfPresent(PrisonerKssRawInDO::getZjlx, reqVO.getZjlx())
            .eqIfPresent(PrisonerKssRawInDO::getZjh, reqVO.getZjh())
            .eqIfPresent(PrisonerKssRawInDO::getBm, reqVO.getBm())
            .eqIfPresent(PrisonerKssRawInDO::getCsrq, reqVO.getCsrq())
            .eqIfPresent(PrisonerKssRawInDO::getHyzk, reqVO.getHyzk())
            .eqIfPresent(PrisonerKssRawInDO::getGj, reqVO.getGj())
            .eqIfPresent(PrisonerKssRawInDO::getHjd, reqVO.getHjd())
            .eqIfPresent(PrisonerKssRawInDO::getRsrq, reqVO.getRsrq())
            .eqIfPresent(PrisonerKssRawInDO::getCssj, reqVO.getCssj())
            .eqIfPresent(PrisonerKssRawInDO::getJyaq, reqVO.getJyaq())
            .eqIfPresent(PrisonerKssRawInDO::getBadw, reqVO.getBadw())
            .eqIfPresent(PrisonerKssRawInDO::getGyqx, reqVO.getGyqx())
            .eqIfPresent(PrisonerKssRawInDO::getJkzk, reqVO.getJkzk())
            .eqIfPresent(PrisonerKssRawInDO::getRygllb, reqVO.getRygllb())
            .eqIfPresent(PrisonerKssRawInDO::getWxdj, reqVO.getWxdj())
            .eqIfPresent(PrisonerKssRawInDO::getBahj, reqVO.getBahj())
            .eqIfPresent(PrisonerKssRawInDO::getAy, reqVO.getAy())
            .eqIfPresent(PrisonerKssRawInDO::getRsxz, reqVO.getRsxz())
            .eqIfPresent(PrisonerKssRawInDO::getDwlx, reqVO.getDwlx())
            .eqIfPresent(PrisonerKssRawInDO::getZwbh, reqVO.getZwbh())
            .eqIfPresent(PrisonerKssRawInDO::getJsh, reqVO.getJsh())
            .eqIfPresent(PrisonerKssRawInDO::getState, reqVO.getState())
            .eqIfPresent(PrisonerKssRawInDO::getJsbh, reqVO.getJsbh())
            .eqIfPresent(PrisonerKssRawInDO::getShid, reqVO.getShid())
            .eqIfPresent(PrisonerKssRawInDO::getZxtzssdrq, reqVO.getZxtzssdrq())
            .eqIfPresent(PrisonerKssRawInDO::getCljgrq, reqVO.getCljgrq())
            .eqIfPresent(PrisonerKssRawInDO::getJbr, reqVO.getJbr())
            .eqIfPresent(PrisonerKssRawInDO::getJbrq, reqVO.getJbrq())
            .eqIfPresent(PrisonerKssRawInDO::getCsyy, reqVO.getCsyy())
            .eqIfPresent(PrisonerKssRawInDO::getWhcd, reqVO.getWhcd())
            .eqIfPresent(PrisonerKssRawInDO::getZy, reqVO.getZy())
            .eqIfPresent(PrisonerKssRawInDO::getZw, reqVO.getZw())
            .eqIfPresent(PrisonerKssRawInDO::getTssf, reqVO.getTssf())
            .eqIfPresent(PrisonerKssRawInDO::getZc, reqVO.getZc())
            .eqIfPresent(PrisonerKssRawInDO::getSf, reqVO.getSf())
            .eqIfPresent(PrisonerKssRawInDO::getGzdw, reqVO.getGzdw())
            .eqIfPresent(PrisonerKssRawInDO::getZzmm, reqVO.getZzmm())
            .eqIfPresent(PrisonerKssRawInDO::getSydw, reqVO.getSydw())
            .eqIfPresent(PrisonerKssRawInDO::getWbrybh, reqVO.getWbrybh())
            .eqIfPresent(PrisonerKssRawInDO::getDah, reqVO.getDah())
            .eqIfPresent(PrisonerKssRawInDO::getSnbh, reqVO.getSnbh())
            .eqIfPresent(PrisonerKssRawInDO::getZuc, reqVO.getZuc())
            .eqIfPresent(PrisonerKssRawInDO::getSg, reqVO.getSg())
            .eqIfPresent(PrisonerKssRawInDO::getJg, reqVO.getJg())
            .eqIfPresent(PrisonerKssRawInDO::getHjdxz, reqVO.getHjdxz())
            .eqIfPresent(PrisonerKssRawInDO::getXzd, reqVO.getXzd())
            .eqIfPresent(PrisonerKssRawInDO::getXzdxz, reqVO.getXzdxz())
            .eqIfPresent(PrisonerKssRawInDO::getBhlx, reqVO.getBhlx())
            .eqIfPresent(PrisonerKssRawInDO::getSyr, reqVO.getSyr())
            .eqIfPresent(PrisonerKssRawInDO::getSy, reqVO.getSy())
            .eqIfPresent(PrisonerKssRawInDO::getSypzwsh, reqVO.getSypzwsh())
            .eqIfPresent(PrisonerKssRawInDO::getSypz, reqVO.getSypz())
            .eqIfPresent(PrisonerKssRawInDO::getJyrq, reqVO.getJyrq())
            .eqIfPresent(PrisonerKssRawInDO::getXhay, reqVO.getXhay())
            .eqIfPresent(PrisonerKssRawInDO::getFzjl, reqVO.getFzjl())
            .eqIfPresent(PrisonerKssRawInDO::getZxf, reqVO.getZxf())
            .eqIfPresent(PrisonerKssRawInDO::getCaaj, reqVO.getCaaj())
            .eqIfPresent(PrisonerKssRawInDO::getCylx, reqVO.getCylx())
            .eqIfPresent(PrisonerKssRawInDO::getJlrq, reqVO.getJlrq())
            .eqIfPresent(PrisonerKssRawInDO::getDbrq, reqVO.getDbrq())
            .eqIfPresent(PrisonerKssRawInDO::getScqsrq, reqVO.getScqsrq())
            .eqIfPresent(PrisonerKssRawInDO::getYsfyrq, reqVO.getYsfyrq())
            .eqIfPresent(PrisonerKssRawInDO::getCsqx, reqVO.getCsqx())
            .eqIfPresent(PrisonerKssRawInDO::getXq, reqVO.getXq())
            .eqIfPresent(PrisonerKssRawInDO::getCljg, reqVO.getCljg())
            .eqIfPresent(PrisonerKssRawInDO::getFjx, reqVO.getFjx())
            .eqIfPresent(PrisonerKssRawInDO::getJcqk, reqVO.getJcqk())
            .eqIfPresent(PrisonerKssRawInDO::getYkss, reqVO.getYkss())
            .eqIfPresent(PrisonerKssRawInDO::getLsyy, reqVO.getLsyy())
            .eqIfPresent(PrisonerKssRawInDO::getZszt, reqVO.getZszt())
            .eqIfPresent(PrisonerKssRawInDO::getLscsyy, reqVO.getLscsyy())
            .eqIfPresent(PrisonerKssRawInDO::getZyryxgqk, reqVO.getZyryxgqk())
            .eqIfPresent(PrisonerKssRawInDO::getEmlx, reqVO.getEmlx())
            .eqIfPresent(PrisonerKssRawInDO::getSykzrq, reqVO.getSykzrq())
            .eqIfPresent(PrisonerKssRawInDO::getJsly, reqVO.getJsly())
            .eqIfPresent(PrisonerKssRawInDO::getTabh, reqVO.getTabh())
            .eqIfPresent(PrisonerKssRawInDO::getYfh, reqVO.getYfh())
            .eqIfPresent(PrisonerKssRawInDO::getCwh, reqVO.getCwh())
            .eqIfPresent(PrisonerKssRawInDO::getTbtsbj, reqVO.getTbtsbj())
            .eqIfPresent(PrisonerKssRawInDO::getPjzm, reqVO.getPjzm())
            .eqIfPresent(PrisonerKssRawInDO::getJe, reqVO.getJe())
            .eqIfPresent(PrisonerKssRawInDO::getLkdj, reqVO.getLkdj())
            .eqIfPresent(PrisonerKssRawInDO::getXzje, reqVO.getXzje())
            .eqIfPresent(PrisonerKssRawInDO::getAjbh, reqVO.getAjbh())
            .eqIfPresent(PrisonerKssRawInDO::getYfhbs, reqVO.getYfhbs())
            .eqIfPresent(PrisonerKssRawInDO::getThcs, reqVO.getThcs())
            .eqIfPresent(PrisonerKssRawInDO::getAjxz, reqVO.getAjxz())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PrisonerKssRawInDO::getAddTime);
        }
        Page<PrisonerKssRawInDO> prisonerKssRawInPage = selectPage(page, wrapper);
        return new PageResult<>(prisonerKssRawInPage.getRecords(), prisonerKssRawInPage.getTotal());
    }
    default List<PrisonerKssRawInDO> selectList(PrisonerKssRawInListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrisonerKssRawInDO>()
            .eqIfPresent(PrisonerKssRawInDO::getRybh, reqVO.getRybh())
            .eqIfPresent(PrisonerKssRawInDO::getXm, reqVO.getXm())
            .eqIfPresent(PrisonerKssRawInDO::getMz, reqVO.getMz())
            .eqIfPresent(PrisonerKssRawInDO::getXb, reqVO.getXb())
            .eqIfPresent(PrisonerKssRawInDO::getZjlx, reqVO.getZjlx())
            .eqIfPresent(PrisonerKssRawInDO::getZjh, reqVO.getZjh())
            .eqIfPresent(PrisonerKssRawInDO::getBm, reqVO.getBm())
            .eqIfPresent(PrisonerKssRawInDO::getCsrq, reqVO.getCsrq())
            .eqIfPresent(PrisonerKssRawInDO::getHyzk, reqVO.getHyzk())
            .eqIfPresent(PrisonerKssRawInDO::getGj, reqVO.getGj())
            .eqIfPresent(PrisonerKssRawInDO::getHjd, reqVO.getHjd())
            .eqIfPresent(PrisonerKssRawInDO::getRsrq, reqVO.getRsrq())
            .eqIfPresent(PrisonerKssRawInDO::getCssj, reqVO.getCssj())
            .eqIfPresent(PrisonerKssRawInDO::getJyaq, reqVO.getJyaq())
            .eqIfPresent(PrisonerKssRawInDO::getBadw, reqVO.getBadw())
            .eqIfPresent(PrisonerKssRawInDO::getGyqx, reqVO.getGyqx())
            .eqIfPresent(PrisonerKssRawInDO::getJkzk, reqVO.getJkzk())
            .eqIfPresent(PrisonerKssRawInDO::getRygllb, reqVO.getRygllb())
            .eqIfPresent(PrisonerKssRawInDO::getWxdj, reqVO.getWxdj())
            .eqIfPresent(PrisonerKssRawInDO::getBahj, reqVO.getBahj())
            .eqIfPresent(PrisonerKssRawInDO::getAy, reqVO.getAy())
            .eqIfPresent(PrisonerKssRawInDO::getRsxz, reqVO.getRsxz())
            .eqIfPresent(PrisonerKssRawInDO::getDwlx, reqVO.getDwlx())
            .eqIfPresent(PrisonerKssRawInDO::getZwbh, reqVO.getZwbh())
            .eqIfPresent(PrisonerKssRawInDO::getJsh, reqVO.getJsh())
            .eqIfPresent(PrisonerKssRawInDO::getState, reqVO.getState())
            .eqIfPresent(PrisonerKssRawInDO::getJsbh, reqVO.getJsbh())
            .eqIfPresent(PrisonerKssRawInDO::getShid, reqVO.getShid())
            .eqIfPresent(PrisonerKssRawInDO::getZxtzssdrq, reqVO.getZxtzssdrq())
            .eqIfPresent(PrisonerKssRawInDO::getCljgrq, reqVO.getCljgrq())
            .eqIfPresent(PrisonerKssRawInDO::getJbr, reqVO.getJbr())
            .eqIfPresent(PrisonerKssRawInDO::getJbrq, reqVO.getJbrq())
            .eqIfPresent(PrisonerKssRawInDO::getCsyy, reqVO.getCsyy())
            .eqIfPresent(PrisonerKssRawInDO::getWhcd, reqVO.getWhcd())
            .eqIfPresent(PrisonerKssRawInDO::getZy, reqVO.getZy())
            .eqIfPresent(PrisonerKssRawInDO::getZw, reqVO.getZw())
            .eqIfPresent(PrisonerKssRawInDO::getTssf, reqVO.getTssf())
            .eqIfPresent(PrisonerKssRawInDO::getZc, reqVO.getZc())
            .eqIfPresent(PrisonerKssRawInDO::getSf, reqVO.getSf())
            .eqIfPresent(PrisonerKssRawInDO::getGzdw, reqVO.getGzdw())
            .eqIfPresent(PrisonerKssRawInDO::getZzmm, reqVO.getZzmm())
            .eqIfPresent(PrisonerKssRawInDO::getSydw, reqVO.getSydw())
            .eqIfPresent(PrisonerKssRawInDO::getWbrybh, reqVO.getWbrybh())
            .eqIfPresent(PrisonerKssRawInDO::getDah, reqVO.getDah())
            .eqIfPresent(PrisonerKssRawInDO::getSnbh, reqVO.getSnbh())
            .eqIfPresent(PrisonerKssRawInDO::getZuc, reqVO.getZuc())
            .eqIfPresent(PrisonerKssRawInDO::getSg, reqVO.getSg())
            .eqIfPresent(PrisonerKssRawInDO::getJg, reqVO.getJg())
            .eqIfPresent(PrisonerKssRawInDO::getHjdxz, reqVO.getHjdxz())
            .eqIfPresent(PrisonerKssRawInDO::getXzd, reqVO.getXzd())
            .eqIfPresent(PrisonerKssRawInDO::getXzdxz, reqVO.getXzdxz())
            .eqIfPresent(PrisonerKssRawInDO::getBhlx, reqVO.getBhlx())
            .eqIfPresent(PrisonerKssRawInDO::getSyr, reqVO.getSyr())
            .eqIfPresent(PrisonerKssRawInDO::getSy, reqVO.getSy())
            .eqIfPresent(PrisonerKssRawInDO::getSypzwsh, reqVO.getSypzwsh())
            .eqIfPresent(PrisonerKssRawInDO::getSypz, reqVO.getSypz())
            .eqIfPresent(PrisonerKssRawInDO::getJyrq, reqVO.getJyrq())
            .eqIfPresent(PrisonerKssRawInDO::getXhay, reqVO.getXhay())
            .eqIfPresent(PrisonerKssRawInDO::getFzjl, reqVO.getFzjl())
            .eqIfPresent(PrisonerKssRawInDO::getZxf, reqVO.getZxf())
            .eqIfPresent(PrisonerKssRawInDO::getCaaj, reqVO.getCaaj())
            .eqIfPresent(PrisonerKssRawInDO::getCylx, reqVO.getCylx())
            .eqIfPresent(PrisonerKssRawInDO::getJlrq, reqVO.getJlrq())
            .eqIfPresent(PrisonerKssRawInDO::getDbrq, reqVO.getDbrq())
            .eqIfPresent(PrisonerKssRawInDO::getScqsrq, reqVO.getScqsrq())
            .eqIfPresent(PrisonerKssRawInDO::getYsfyrq, reqVO.getYsfyrq())
            .eqIfPresent(PrisonerKssRawInDO::getCsqx, reqVO.getCsqx())
            .eqIfPresent(PrisonerKssRawInDO::getXq, reqVO.getXq())
            .eqIfPresent(PrisonerKssRawInDO::getCljg, reqVO.getCljg())
            .eqIfPresent(PrisonerKssRawInDO::getFjx, reqVO.getFjx())
            .eqIfPresent(PrisonerKssRawInDO::getJcqk, reqVO.getJcqk())
            .eqIfPresent(PrisonerKssRawInDO::getYkss, reqVO.getYkss())
            .eqIfPresent(PrisonerKssRawInDO::getLsyy, reqVO.getLsyy())
            .eqIfPresent(PrisonerKssRawInDO::getZszt, reqVO.getZszt())
            .eqIfPresent(PrisonerKssRawInDO::getLscsyy, reqVO.getLscsyy())
            .eqIfPresent(PrisonerKssRawInDO::getZyryxgqk, reqVO.getZyryxgqk())
            .eqIfPresent(PrisonerKssRawInDO::getEmlx, reqVO.getEmlx())
            .eqIfPresent(PrisonerKssRawInDO::getSykzrq, reqVO.getSykzrq())
            .eqIfPresent(PrisonerKssRawInDO::getJsly, reqVO.getJsly())
            .eqIfPresent(PrisonerKssRawInDO::getTabh, reqVO.getTabh())
            .eqIfPresent(PrisonerKssRawInDO::getYfh, reqVO.getYfh())
            .eqIfPresent(PrisonerKssRawInDO::getCwh, reqVO.getCwh())
            .eqIfPresent(PrisonerKssRawInDO::getTbtsbj, reqVO.getTbtsbj())
            .eqIfPresent(PrisonerKssRawInDO::getPjzm, reqVO.getPjzm())
            .eqIfPresent(PrisonerKssRawInDO::getJe, reqVO.getJe())
            .eqIfPresent(PrisonerKssRawInDO::getLkdj, reqVO.getLkdj())
            .eqIfPresent(PrisonerKssRawInDO::getXzje, reqVO.getXzje())
            .eqIfPresent(PrisonerKssRawInDO::getAjbh, reqVO.getAjbh())
            .eqIfPresent(PrisonerKssRawInDO::getYfhbs, reqVO.getYfhbs())
            .eqIfPresent(PrisonerKssRawInDO::getThcs, reqVO.getThcs())
            .eqIfPresent(PrisonerKssRawInDO::getAjxz, reqVO.getAjxz())
        .orderByDesc(PrisonerKssRawInDO::getAddTime));    }


    }
