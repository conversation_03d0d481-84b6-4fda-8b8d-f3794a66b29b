package com.rs.module.integ.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("acp_pm_prisoner_pre_in")
@Entity
public class AcpPmPrisonerPreIn {

    /**
     * 主键id
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value = "主键id")
    @Id
    private String id;

    /**
     * 监所编号
     */
    @ApiModelProperty(value = "监所编号")
    private String jsbh;

    /**
     * 网办人员编号 (法制系统唯一编号)
     */
    @ApiModelProperty(value = "网办人员编号 (法制系统唯一编号)")
    private String wbrybh;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String xm;

    /**
     * 姓名拼音
     */
    @ApiModelProperty(value = "姓名拼音")
    private String xmpy;

    /**
     * 入所日期
     */
    @ApiModelProperty(value = "入所日期")
    private LocalDateTime rsrq;

    /**
     * 职务 字典值：ZW
     */
    @ApiModelProperty(value = "职务 字典值：ZW")
    private String zw;

    /**
     * 户籍地详址
     */
    @ApiModelProperty(value = "户籍地详址")
    private String hjdxz;

    /**
     * 现住地详址
     */
    @ApiModelProperty(value = "现住地详址")
    private String xzdxz;

    /**
     * 工作单位
     */
    @ApiModelProperty(value = "工作单位")
    private String gzdw;

    /**
     * 状态（STATE） 字典值：STATE
     */
    @ApiModelProperty(value = "状态（STATE） 字典值：STATE")
    private String state;

    /**
     * 别名
     */
    @ApiModelProperty(value = "别名")
    private String bm;

    /**
     * 同案编号
     */
    @ApiModelProperty(value = "同案编号")
    private String tabh;

    /**
     * 审批人
     */
    @ApiModelProperty(value = "审批人")
    private String spr;

    /**
     * 审批单位
     */
    @ApiModelProperty(value = "审批单位")
    private String spdw;

    /**
     * 特殊身份 字典值：TSSF
     */
    @ApiModelProperty(value = "特殊身份 字典值：TSSF")
    private String tssf;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String sdnjccjg;

    /**
     * 专长 字典值：ZC
     */
    @ApiModelProperty(value = "专长 字典值：ZC")
    private String zc;

    /**
     * 身高
     */
    @ApiModelProperty(value = "身高")
    private String sg;

    /**
     * 足长
     */
    @ApiModelProperty(value = "足长")
    private String zuc;

    /**
     * 证件号
     */
    @ApiModelProperty(value = "证件号")
    private String zjh;

    /**
     * 性别 字典值：XB
     */
    @ApiModelProperty(value = "性别 字典值：XB")
    private String xb;

    /**
     * 民族 字典值：MZ
     */
    @ApiModelProperty(value = "民族 字典值：MZ")
    private String mz;

    /**
     * 国籍 字典值：GJ
     */
    @ApiModelProperty(value = "国籍 字典值：GJ")
    private String gj;

    /**
     * 文化程度 字典值：WHCD
     */
    @ApiModelProperty(value = "文化程度 字典值：WHCD")
    private String whcd;

    /**
     * 政治面貌 字典值：ZZMM
     */
    @ApiModelProperty(value = "政治面貌 字典值：ZZMM")
    private String zzmm;

    /**
     * 户籍地 字典值：XZQH
     */
    @ApiModelProperty(value = "户籍地 字典值：XZQH")
    private String hjd;

    /**
     * 现住地 字典值：XZQH
     */
    @ApiModelProperty(value = "现住地 字典值：XZQH")
    private String xzd;

    /**
     * 职业 字典值：ZY
     */
    @ApiModelProperty(value = "职业 字典值：ZY")
    private String zy;

    /**
     * 籍贯 字典值：XZQH
     */
    @ApiModelProperty(value = "籍贯 字典值：XZQH")
    private String jg;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String xzhjaj;

    /**
     * 人员管理类别 字典值：RYGLLB
     */
    @ApiModelProperty(value = "人员管理类别 字典值：RYGLLB")
    private String gllb;

    /**
     * 身份 字典值：SF
     */
    @ApiModelProperty(value = "身份 字典值：SF")
    private String sf;

    /**
     * 证件类型 字典值：ZJLX
     */
    @ApiModelProperty(value = "证件类型 字典值：ZJLX")
    private String zjlx;

    /**
     * 办案单位
     */
    @ApiModelProperty(value = "办案单位")
    private String badw;

    /**
     * 送押单位
     */
    @ApiModelProperty(value = "送押单位")
    private String sydw;

    /**
     * 送押人
     */
    @ApiModelProperty(value = "送押人")
    private String syr;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createtime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatetime;

    /**
     * 简要案情
     */
    @ApiModelProperty(value = "简要案情")
    private String jyaq;

    /**
     * 案由 字典值
     */
    @ApiModelProperty(value = "案由 字典值：看守所：AJLB，拘留所：JLSAJLB")
    private String ay;

    /**
     * 婚姻状况 字典值：HYZK
     */
    @ApiModelProperty(value = "婚姻状况 字典值：HYZK")
    private String hyzk;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String czzt;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    private LocalDateTime csrq;

    /**
     * 拘留决定机关
     */
    @ApiModelProperty(value = "拘留决定机关")
    private String jljdjg;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String gyts;

    /**
     * 拘留日期
     */
    @ApiModelProperty(value = "拘留日期")
    private LocalDateTime jlrq;

    /**
     * 关押期限
     */
    @ApiModelProperty(value = "关押期限")
    private LocalDateTime gyqx;

    /**
     * 办案人电话
     */
    @ApiModelProperty(value = "办案人电话")
    private String bardh;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String syrsj;

    /**
     * 收押凭证（SYPZ） 字典值：SYPZ
     */
    @ApiModelProperty(value = "收押凭证（SYPZ） 字典值：SYPZ")
    private String sypz;

    /**
     * 收押凭证文书号
     */
    @ApiModelProperty(value = "收押凭证文书号")
    private String sypzwsh;

    /**
     * 逮捕日期
     */
    @ApiModelProperty(value = "逮捕日期")
    private LocalDateTime dbrq;

    /**
     * 办案环节 字典值：BAJD
     */
    @ApiModelProperty(value = "办案环节 字典值：BAJD")
    private String bahj;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String lxdh;

    @ApiModelProperty("监所类型")
    private String jslx;

}
