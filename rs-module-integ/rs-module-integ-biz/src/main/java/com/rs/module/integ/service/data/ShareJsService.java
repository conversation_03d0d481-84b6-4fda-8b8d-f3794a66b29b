package com.rs.module.integ.service.data;

import java.util.*;
import javax.validation.*;
import com.rs.module.integ.controller.admin.data.vo.*;
import com.rs.module.integ.entity.data.ShareJsDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 数据共享-监室 Service 接口
 *
 * <AUTHOR>
 */
public interface ShareJsService extends IBaseService<ShareJsDO>{

    /**
     * 创建数据共享-监室
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createShareJs(@Valid ShareJsSaveReqVO createReqVO);

    /**
     * 更新数据共享-监室
     *
     * @param updateReqVO 更新信息
     */
    void updateShareJs(@Valid ShareJsSaveReqVO updateReqVO);

    /**
     * 删除数据共享-监室
     *
     * @param id 编号
     */
    void deleteShareJs(String id);

    /**
     * 获得数据共享-监室
     *
     * @param id 编号
     * @return 数据共享-监室
     */
    ShareJsDO getShareJs(String id);

    /**
    * 获得数据共享-监室分页
    *
    * @param pageReqVO 分页查询
    * @return 数据共享-监室分页
    */
    PageResult<ShareJsDO> getShareJsPage(ShareJsPageReqVO pageReqVO);

    /**
    * 获得数据共享-监室列表
    *
    * @param listReqVO 查询条件
    * @return 数据共享-监室列表
    */
    List<ShareJsDO> getShareJsList(ShareJsListReqVO listReqVO);


}
