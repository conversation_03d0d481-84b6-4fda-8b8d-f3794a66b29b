package com.rs.module.integ.entity.data;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 数据共享-监室 DO
 *
 * <AUTHOR>
 */
@TableName("integ_data_share_js")
@KeySequence("integ_data_share_js_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "integ_data_share_js")
public class ShareJsDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 第三方id
     */
    private String sysId;
    /**
     * 监室号
     */
    private String jsh;
    /**
     * 监室列别
     */
    private String jslb;
    /**
     * 监室名称
     */
    private String jsmc;
    /**
     * 监区号
     */
    private String jqh;
    /**
     * 状态
     */
    private String state;
    /**
     * 协管民警
     */
    private String xgmjjh;
    /**
     * 主管民警
     */
    private String zgmjjh;
    /**
     * 监所编号
     */
    private String jsbh;

}
