package com.rs.module.integ.dao;

import com.rs.module.integ.domain.entity.AcpPmPrisonerPhotos;
import com.rs.module.integ.domain.entity.AcpPmPrisonerPreIn;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Date;

@Repository
public interface AcpPmPrisonerPreInRepository extends JpaRepository<AcpPmPrisonerPreIn, String> {


    @Query("select max(a.createtime) from AcpPmPrisonerPreIn a where a.jslx = :jslx")
    LocalDateTime getTableLastCreateTime(@Param("jslx")String jslx);

    @Query("select max(a.updatetime) from AcpPmPrisonerPreIn a where a.jslx = :jslx")
    LocalDateTime getTableLastUpdateTime(@Param("jslx")String jslx);
}
