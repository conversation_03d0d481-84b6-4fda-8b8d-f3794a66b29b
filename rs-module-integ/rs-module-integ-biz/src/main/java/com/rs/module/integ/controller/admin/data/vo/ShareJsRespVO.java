package com.rs.module.integ.controller.admin.data.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 数据共享-监室 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ShareJsRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("第三方id")
    private String sysId;
    @ApiModelProperty("监室号")
    private String jsh;
    @ApiModelProperty("监室列别")
    private String jslb;
    @ApiModelProperty("监室名称")
    private String jsmc;
    @ApiModelProperty("监区号")
    private String jqh;
    @ApiModelProperty("状态")
    private String state;
    @ApiModelProperty("协管民警")
    private String xgmjjh;
    @ApiModelProperty("主管民警")
    private String zgmjjh;
    @ApiModelProperty("监所编号")
    private String jsbh;
}
