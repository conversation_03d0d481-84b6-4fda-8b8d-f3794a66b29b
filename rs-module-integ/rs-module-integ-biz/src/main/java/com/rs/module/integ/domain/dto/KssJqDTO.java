package com.rs.module.integ.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName KssJqDTO
 * @Description 看守所监区同步
 * <AUTHOR>
 * @Date 2025/7/29 23:52
 * @Version 1.0
 */
@NoArgsConstructor
@Data
public class KssJqDTO {

    @JsonProperty("code")
    private Integer code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("serverTime")
    private String serverTime;
    @JsonProperty("data")
    private DataDTO data;
    @JsonProperty("pages")
    private Integer pages;
    @JsonProperty("pageNum")
    private Integer pageNum;
    @JsonProperty("pageSize")
    private Integer pageSize;
    @JsonProperty("total")
    private Integer total;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JsonProperty("jq")
        private List<JqDTO> jq;

        @NoArgsConstructor
        @Data
        public static class JqDTO {
            @JsonProperty("id")
            private String id;
            @JsonProperty("jqh")
            private String jqh;
            @JsonProperty("jqmc")
            private String jqmc;
            @JsonProperty("jqicons")
            private Object jqicons;
            @JsonProperty("state")
            private String state;
            @JsonProperty("createtime")
            private String createtime;
            @JsonProperty("updatetime")
            private String updatetime;
            @JsonProperty("jsbh")
            private String jsbh;
        }
    }
}
