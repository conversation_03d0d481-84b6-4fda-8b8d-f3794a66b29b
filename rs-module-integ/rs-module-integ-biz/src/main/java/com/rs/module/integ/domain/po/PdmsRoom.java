package com.rs.module.integ.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.rs.module.integ.constant.PdmsDictionaryConstant;
import com.rs.module.integ.constant.PrisonDataSourceConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("法综-监室信息表同步")
public class PdmsRoom {

    /**
     * ID
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty("ID")
    private String id;

    /**
     * 监所编号
     */
    @ApiModelProperty("监所编号")
    private String jsbh;

    /**
     * 监区号
     */
    @ApiModelProperty("监区号")
    private String jqh;

    /**
     * 监室名称
     */
    @ApiModelProperty("监室名称")
    private String jsmc;

    /**
     * 监室号
     */
    @ApiModelProperty("监室号")
    private String jsh;

    /**
     * 监室类别(JSLX)
     */
    @ApiModelProperty("监室类别(JSLX)")
    private String jslb;

    /**
     * 男女类别(XB)
     */
    @ApiModelProperty("男女类别(XB)")
    private String type;

    /**
     * 关押量
     */
    @ApiModelProperty("关押量")
    private BigDecimal innum;

    /**
     * 额定押量
     */
    @ApiModelProperty("额定押量")
    private BigDecimal bznum;

    /**
     * 主管民警
     */
    @ApiModelProperty("主管民警")
    private String zgmj;

    /**
     * 主管民警警号
     */
    @ApiModelProperty("主管民警警号")
    private String zgmjjh;

    /**
     * 协管民警
     */
    @ApiModelProperty("协管民警")
    private String xgmj;

    /**
     * 协管民警警号
     */
    @ApiModelProperty("协管民警警号")
    private String xgmjjh;

    /**
     * 文明监室(1：文明监室，2：严管监室)
     */
    @ApiModelProperty("文明监室(1：文明监室，2：严管监室)")
    private String wmjs;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String bz;

    /**
     * 状态(STATE) R2：有效 R3：无效
     */
    @ApiModelProperty("状态（STATE） 字典值：" + PdmsDictionaryConstant.STATE)
    private String state;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updator;

    /**
     * 监室方向（1 代表从上面开始   2 代表从下面开始）
     */
    @ApiModelProperty("监室方向（1 代表从上面开始   2 代表从下面开始）")
    private String jsfx;

    /**
     * 监室布局（1 大监室 2小监室）
     */
    @ApiModelProperty("监室布局（1 大监室 2小监室）")
    private String jsbj;

    /**
     * 是否设置（默认为0未设置  1为设置）
     */
    @ApiModelProperty("是否设置（默认为0未设置  1为设置）")
    private String sfsz;

    /**
     * 创建时间(yyyy-MM-dd HH:mm:ss)
     */
    @ApiModelProperty("创建时间(yyyy-MM-dd HH:mm:ss)")
    private LocalDateTime createtime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updatetime;

    /**
     * 数据来源
     *
     * @see PrisonDataSourceConstant
     */
    @ApiModelProperty("数据来源 1：看守所 2：拘留所")
    private Integer dataSource;
}
