//package com.rs.module.integ.service;
//
//import com.rs.module.integ.constant.PrisonDataSourceConstant;
//import com.rs.module.integ.domain.po.PdmsPrisonerPhotos;
//import com.rs.module.integ.mapper.PdmsPrisonerPhotosMapper;
//import org.apache.commons.lang3.StringUtils;
//
//import java.util.*;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
//public interface PdmsPrisonerPhotosService extends IBaseBatchService<PdmsPrisonerPhotosMapper, PdmsPrisonerPhotos> {
//
//    /**
//     * 根据人员编号和监所号查询人员正面照片信息
//     */
//    PdmsPrisonerPhotos getByUserFacePhotos(String rybh, String prisonId);
//
//    /**
//     * 根据人员编号列表和数据来源查询人员照片信息
//     *
//     * @param rybhList   人员编号
//     * @param dataSource 数据来源 {@link PrisonDataSourceConstant}
//     */
//    List<PdmsPrisonerPhotos> getByUserPhotos(List<String> rybhList, Integer dataSource);
//
//
//    /**
//     * 根据人员编号列表查询人员照片信息
//     */
//    default List<PdmsPrisonerPhotos> getByUserPhotos(List<String> rybhList) {
//        return getByUserPhotos(rybhList, null);
//    }
//
//    /**
//     * 根据人员照片信息按人员编号分组然后再按照片类型分组
//     * <p>
//     * key：人员编号
//     * value：map -> key：照片类型 value：照片信息
//     */
//    static Map<String, Map<String, PdmsPrisonerPhotos>> getPrisonerMap(List<PdmsPrisonerPhotos> pdmsPrisonerPhotos) {
//        return Optional.ofNullable(pdmsPrisonerPhotos)
//                .orElse(Collections.emptyList())
//                .stream()
//                .filter(o -> StringUtils.isNotBlank(o.getType()))
//                .filter(o -> StringUtils.isNotBlank(o.getRybh()))
//                .filter(o -> StringUtils.isNotBlank(o.getPhotourl()))
//                .collect(Collectors.groupingBy(PdmsPrisonerPhotos::getRybh,
//                        Collectors.toMap(PdmsPrisonerPhotos::getType, Function.identity(),
//                                (o1, o2) -> o2)));
//    }
//
//    /**
//     * 根据人员照片信息按人员编号分组，再根据照片类型获取照片url
//     */
//    static String fromMapGetPhotoUrl(Map<String, Map<String, PdmsPrisonerPhotos>> prisonerPhotosMap,
//                                     String rybh,
//                                     String type) {
//        PdmsPrisonerPhotos pdmsPrisonerPhotos = Optional.ofNullable(prisonerPhotosMap.get(rybh))
//                .orElse(Collections.emptyMap())
//                .get(type);
//        if (Objects.isNull(pdmsPrisonerPhotos)) {
//            return null;
//        }
//        return pdmsPrisonerPhotos.getPhotourl();
//    }
//}
