package com.rs.module.acp.dao.pm;

import com.baomidou.mybatisplus.core.MybatisSqlSessionFactoryBuilder;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

/**
 * @ClassName TerminalVirtualAccountDaoTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/6/14 16:12
 * @Version 1.0
 */
public class TerminalVirtualAccountDaoTest {
    private static TerminalVirtualAccountDao mapper;

    @BeforeAll
    public static void setUpMybatisDatabase() {
        SqlSessionFactory builder = new MybatisSqlSessionFactoryBuilder().build(TerminalVirtualAccountDaoTest.class.getClassLoader().getResourceAsStream("mybatisTestConfiguration/TerminalVirtualAccountDaoTestConfiguration.xml"));
        final MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        builder.getConfiguration().addInterceptor(interceptor);
        //you can use builder.openSession(false) to not commit to database
        mapper = builder.getConfiguration().getMapper(TerminalVirtualAccountDao.class, builder.openSession(true));
    }

    @Test
    public void testGetAvailableAccount() {
        mapper.getAvailableAccount();
    }
}
