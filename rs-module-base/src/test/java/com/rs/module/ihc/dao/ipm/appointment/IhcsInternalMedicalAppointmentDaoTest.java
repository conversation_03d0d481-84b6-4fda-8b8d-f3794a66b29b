package com.rs.module.ihc.dao.ipm.appointment;

import com.baomidou.mybatisplus.core.MybatisSqlSessionFactoryBuilder;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.rs.module.ihc.controller.admin.ipm.appointment.vo.IhcsInternalMedicalAppointmentListReqVO;
import com.rs.module.ihc.entity.ipm.appointment.IhcsInternalMedicalAppointmentDO;
import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName IhcsInternalMedicalAppointmentDaoTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/31 22:15
 * @Version 1.0
 */
@Log4j2
public class IhcsInternalMedicalAppointmentDaoTest {
    private static IhcsInternalMedicalAppointmentDao mapper;

    @BeforeAll
    public static void setUpMybatisDatabase() {
        SqlSessionFactory builder = new MybatisSqlSessionFactoryBuilder().build(IhcsInternalMedicalAppointmentDaoTest.class.getClassLoader().getResourceAsStream("mybatisTestConfiguration/IhcsInternalMedicalAppointmentDaoTestConfiguration.xml"));
        final MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        builder.getConfiguration().addInterceptor(interceptor);
        //you can use builder.openSession(false) to not commit to database
        mapper = builder.getConfiguration().getMapper(IhcsInternalMedicalAppointmentDao.class, builder.openSession(true));
    }

    @Test
    public void testGetMaxAppointmentNumber() {
        String maxAppointmentNumber = mapper.getMaxAppointmentNumber(LocalDateTime.now(), LocalDateTime.now(), null);
        log.info("maxAppointmentNumber:{}", maxAppointmentNumber);
    }


    @Test
    public void testGetMedicalAppointmentById() {
        mapper.getMedicalAppointmentById("1111");
    }

    @Test
    public void testSelectList() {
        IhcsInternalMedicalAppointmentListReqVO reqVO = new IhcsInternalMedicalAppointmentListReqVO();
        reqVO.setOperationType("2");
        List<IhcsInternalMedicalAppointmentDO> ihcsInternalMedicalAppointmentDOS = mapper.selectList(reqVO);
        log.info("ihcsInternalMedicalAppointmentDOS:{}", ihcsInternalMedicalAppointmentDOS);
    }


}
