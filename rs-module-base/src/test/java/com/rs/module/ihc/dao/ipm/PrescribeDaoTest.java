package com.rs.module.ihc.dao.ipm;

import com.baomidou.mybatisplus.core.MybatisSqlSessionFactoryBuilder;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

/**
 * @ClassName PrescribeDaoTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/6/7 2:50
 * @Version 1.0
 */
public class PrescribeDaoTest {
    private static PrescribeDao mapper;

    @BeforeAll
    public static void setUpMybatisDatabase() {
        SqlSessionFactory builder = new MybatisSqlSessionFactoryBuilder().build(PrescribeDaoTest.class.getClassLoader().getResourceAsStream("mybatisTestConfiguration/PrescribeDaoTestConfiguration.xml"));
        final MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        builder.getConfiguration().addInterceptor(interceptor);
        //you can use builder.openSession(false) to not commit to database
        mapper = builder.getConfiguration().getMapper(PrescribeDao.class, builder.openSession(true));
    }

    @Test
    public void testGetPrescribeListByJgrybm() {
        mapper.getPrescribeListByJgrybm("111", "1");
    }

}
