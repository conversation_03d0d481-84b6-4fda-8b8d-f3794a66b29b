package com.rs.module.ihc.dao.ipm;

import com.baomidou.mybatisplus.core.MybatisSqlSessionFactoryBuilder;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.rs.module.ihc.controller.admin.ipm.vo.PrescribeExecuteFxgzTodoReqVO;
import com.rs.module.ihc.controller.admin.ipm.vo.PrescribeExecuteFytzReqVO;
import com.rs.module.ihc.controller.admin.ipm.vo.PrescribeExecuteFytzRespVO;
import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.util.List;

/**
 * @ClassName PrescribeExecuteDaoTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/6/5 15:54
 * @Version 1.0
 */
@Log4j2
public class PrescribeExecuteDaoTest {
    private static PrescribeExecuteDao mapper;

    @BeforeAll
    public static void setUpMybatisDatabase() {
        SqlSessionFactory builder = new MybatisSqlSessionFactoryBuilder().build(PrescribeExecuteDaoTest.class.getClassLoader().getResourceAsStream("mybatisTestConfiguration/PrescribeExecuteDaoTestConfiguration.xml"));
        final MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        builder.getConfiguration().addInterceptor(interceptor);
        //you can use builder.openSession(false) to not commit to database
        mapper = builder.getConfiguration().getMapper(PrescribeExecuteDao.class, builder.openSession(true));
    }

    @Test
    public void testSelectFytz() {
        PrescribeExecuteFytzReqVO prescribeExecuteFytzReqVO = new PrescribeExecuteFytzReqVO();
        List<PrescribeExecuteFytzRespVO> prescribeExecuteFytzRespVOS = mapper.selectFytz(prescribeExecuteFytzReqVO);
        log.info("prescribeExecuteFytzRespVOS={}", prescribeExecuteFytzRespVOS);
    }

    @Test
    public void testSelectFxgzsTodo() {
        PrescribeExecuteFxgzTodoReqVO prescribeExecuteFxgzTodoReqVO = new PrescribeExecuteFxgzTodoReqVO();
        prescribeExecuteFxgzTodoReqVO.setRangType("2");
        prescribeExecuteFxgzTodoReqVO.setJgrybm("111");
        mapper.selectFxgzsTodo(prescribeExecuteFxgzTodoReqVO);
    }
}
