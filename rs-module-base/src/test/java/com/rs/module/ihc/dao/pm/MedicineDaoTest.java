package com.rs.module.ihc.dao.pm;

import com.baomidou.mybatisplus.core.MybatisSqlSessionFactoryBuilder;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.rs.module.ihc.controller.admin.pm.vo.MedicinePageReqVO;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

/**
 * @ClassName MedicineDaoTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/6/3 12:08
 * @Version 1.0
 */
public class MedicineDaoTest {
    private static MedicineDao mapper;

    @BeforeAll
    public static void setUpMybatisDatabase() {
        SqlSessionFactory builder = new MybatisSqlSessionFactoryBuilder().build(MedicineDaoTest.class.getClassLoader().getResourceAsStream("mybatisTestConfiguration/MedicineDaoTestConfiguration.xml"));
        final MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        builder.getConfiguration().addInterceptor(interceptor);
        //you can use builder.openSession(false) to not commit to database
        mapper = builder.getConfiguration().getMapper(MedicineDao.class, builder.openSession(true));
    }

    @Test
    public void testSelectPage() {
        MedicinePageReqVO medicinePageReqVO = new MedicinePageReqVO();
        medicinePageReqVO.setMedicineName("藿香");
        mapper.selectPage(medicinePageReqVO);
    }
}
