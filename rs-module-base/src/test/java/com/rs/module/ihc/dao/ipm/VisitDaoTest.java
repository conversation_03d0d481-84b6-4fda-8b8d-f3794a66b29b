package com.rs.module.ihc.dao.ipm;

import com.baomidou.mybatisplus.core.MybatisSqlSessionFactoryBuilder;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.rs.module.base.entity.RyReqVO;
import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

/**
 * @ClassName VisitDaoTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/6/7 18:15
 * @Version 1.0
 */
@Log4j2
public class VisitDaoTest {
    private static VisitDao mapper;

    @BeforeAll
    public static void setUpMybatisDatabase() {
        SqlSessionFactory builder = new MybatisSqlSessionFactoryBuilder().build(VisitDaoTest.class.getClassLoader().getResourceAsStream("mybatisTestConfiguration/VisitDaoTestConfiguration.xml"));
        final MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        builder.getConfiguration().addInterceptor(interceptor);
        //you can use builder.openSession(false) to not commit to database
        mapper = builder.getConfiguration().getMapper(VisitDao.class, builder.openSession(true));
    }


    @Test
    public void testGetVisitTodo() {
        RyReqVO ryReqVO = new RyReqVO();
        ryReqVO.setJgrybm("JDS-440400121220217085200");
        mapper.getVisitTodo(ryReqVO);
        log.info("{}", mapper.getVisitTodo(ryReqVO).size());
    }


    @Test
    public void testCountEquipmentUseByJgrybm() {
        mapper.countEquipmentUseByJgrybm("JDS-440400121220217085200");
    }



    @Test
    public void testFindDiseaseTimeDescInId() {

        mapper.findDiseaseTimeDescInId("1930246401896484864,1930941279701831680".split(","));
    }
}
