package com.rs.module.uac.service.user;

import java.util.*;
import javax.validation.*;

import com.rs.module.uac.controller.admin.user.vo.UacUserPhotoListReqVO;
import com.rs.module.uac.controller.admin.user.vo.UacUserPhotoPageReqVO;
import com.rs.module.uac.controller.admin.user.vo.UacUserPhotoSaveReqVO;
import com.rs.module.uac.entity.user.UacUserPhotoDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 用户头像 Service 接口
 *
 * <AUTHOR>
 */
public interface UacUserPhotoService extends IBaseService<UacUserPhotoDO>{

    /**
     * 创建用户头像
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createUserPhoto(@Valid UacUserPhotoSaveReqVO createReqVO);

    /**
     * 更新用户头像
     *
     * @param updateReqVO 更新信息
     */
    void updateUserPhoto(@Valid UacUserPhotoSaveReqVO updateReqVO);

    /**
     * 删除用户头像
     *
     * @param id 编号
     */
    void deleteUserPhoto(String id);

    /**
     * 获得用户头像
     *
     * @param id 编号
     * @return 用户头像
     */
    UacUserPhotoDO getUserPhoto(String id);

    /**
    * 获得用户头像分页
    *
    * @param pageReqVO 分页查询
    * @return 用户头像分页
    */
    PageResult<UacUserPhotoDO> getUserPhotoPage(UacUserPhotoPageReqVO pageReqVO);

    /**
    * 获得用户头像列表
    *
    * @param listReqVO 查询条件
    * @return 用户头像列表
    */
    List<UacUserPhotoDO> getUserPhotoList(UacUserPhotoListReqVO listReqVO);


}
