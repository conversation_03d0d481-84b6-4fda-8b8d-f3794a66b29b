package com.rs.module.uac.service.user;

import java.util.*;
import javax.validation.*;
import com.rs.module.uac.controller.admin.user.vo.*;
import com.rs.module.uac.entity.user.BspUacUserDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 用户 Service 接口
 *
 * <AUTHOR>
 */
public interface BspUacUserService extends IBaseService<BspUacUserDO>{

    /**
     * 创建用户
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createBspUacUser(@Valid BspUacUserSaveReqVO createReqVO);

    /**
     * 更新用户
     *
     * @param updateReqVO 更新信息
     */
    void updateBspUacUser(@Valid BspUacUserSaveReqVO updateReqVO);

    /**
     * 删除用户
     *
     * @param id 编号
     */
    void deleteBspUacUser(String id);

    /**
     * 获得用户
     *
     * @param id 编号
     * @return 用户
     */
    BspUacUserDO getBspUacUser(String id);

    /**
    * 获得用户分页
    *
    * @param pageReqVO 分页查询
    * @return 用户分页
    */
    PageResult<BspUacUserDO> getBspUacUserPage(BspUacUserPageReqVO pageReqVO);

    /**
    * 获得用户列表
    *
    * @param listReqVO 查询条件
    * @return 用户列表
    */
    List<BspUacUserDO> getBspUacUserList(BspUacUserListReqVO listReqVO);


    //根据id 获取用户
    BspUacUserRespVO getUserById(String id);
}
