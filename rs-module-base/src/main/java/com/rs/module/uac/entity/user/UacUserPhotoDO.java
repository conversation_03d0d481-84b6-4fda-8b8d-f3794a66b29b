package com.rs.module.uac.entity.user;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 用户头像 DO
 *
 * <AUTHOR>
 */
@TableName("uac_user_photo")
@KeySequence("uac_user_photo_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UacUserPhotoDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 用户id
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 头像
     */
    private String photo;

}
