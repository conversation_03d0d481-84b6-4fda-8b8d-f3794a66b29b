package com.rs.module.uac.dao.user;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.uac.entity.user.BspUacUserDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.uac.controller.admin.user.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 用户 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BspUacUserDao extends IBaseDao<BspUacUserDO> {


    default PageResult<BspUacUserDO> selectPage(BspUacUserPageReqVO reqVO) {
        Page<BspUacUserDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<BspUacUserDO> wrapper = new LambdaQueryWrapperX<BspUacUserDO>()
            .eqIfPresent(BspUacUserDO::getCityId, reqVO.getCityId())
            .eqIfPresent(BspUacUserDO::getRegionId, reqVO.getRegionId())
            .likeIfPresent(BspUacUserDO::getRegionName, reqVO.getRegionName())
            .eqIfPresent(BspUacUserDO::getRegionCode, reqVO.getRegionCode())
            .eqIfPresent(BspUacUserDO::getOrgId, reqVO.getOrgId())
            .eqIfPresent(BspUacUserDO::getLoginId, reqVO.getLoginId())
            .eqIfPresent(BspUacUserDO::getPassword, reqVO.getPassword())
            .likeIfPresent(BspUacUserDO::getName, reqVO.getName())
            .eqIfPresent(BspUacUserDO::getSex, reqVO.getSex())
            .eqIfPresent(BspUacUserDO::getIdCard, reqVO.getIdCard())
            .eqIfPresent(BspUacUserDO::getEmail, reqVO.getEmail())
            .eqIfPresent(BspUacUserDO::getMobile, reqVO.getMobile())
            .eqIfPresent(BspUacUserDO::getOfficeTel, reqVO.getOfficeTel())
            .eqIfPresent(BspUacUserDO::getIp, reqVO.getIp())
            .eqIfPresent(BspUacUserDO::getScode, reqVO.getScode())
            .eqIfPresent(BspUacUserDO::getLastIp, reqVO.getLastIp())
            .eqIfPresent(BspUacUserDO::getLastVisit, reqVO.getLastVisit())
            .eqIfPresent(BspUacUserDO::getIsDisabled, reqVO.getIsDisabled())
            .eqIfPresent(BspUacUserDO::getOrderId, reqVO.getOrderId())
            .eqIfPresent(BspUacUserDO::getLoginMethod, reqVO.getLoginMethod())
            .eqIfPresent(BspUacUserDO::getPosition, reqVO.getPosition())
            .eqIfPresent(BspUacUserDO::getPoliceRank, reqVO.getPoliceRank())
            .eqIfPresent(BspUacUserDO::getIsFj, reqVO.getIsFj())
            .eqIfPresent(BspUacUserDO::getFjLx, reqVO.getFjLx())
            .eqIfPresent(BspUacUserDO::getRemark, reqVO.getRemark())
            .eqIfPresent(BspUacUserDO::getIsdel, reqVO.getIsdel())
            .eqIfPresent(BspUacUserDO::getIsSync, reqVO.getIsSync())
            .eqIfPresent(BspUacUserDO::getConfirm, reqVO.getConfirm())
            .eqIfPresent(BspUacUserDO::getValidType, reqVO.getValidType())
            .betweenIfPresent(BspUacUserDO::getEndTime, reqVO.getEndTime())
            .eqIfPresent(BspUacUserDO::getTempPass, reqVO.getTempPass())
            .eqIfPresent(BspUacUserDO::getUserGroup, reqVO.getUserGroup())
            .betweenIfPresent(BspUacUserDO::getPassExpireDate, reqVO.getPassExpireDate())
            .eqIfPresent(BspUacUserDO::getGmPass, reqVO.getGmPass())
            .eqIfPresent(BspUacUserDO::getGmIdCard, reqVO.getGmIdCard())
            .eqIfPresent(BspUacUserDO::getGmMobile, reqVO.getGmMobile())
            .eqIfPresent(BspUacUserDO::getGmPassSign, reqVO.getGmPassSign())
            .eqIfPresent(BspUacUserDO::getGmIdCardSign, reqVO.getGmIdCardSign())
            .eqIfPresent(BspUacUserDO::getGmMobileSign, reqVO.getGmMobileSign())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(BspUacUserDO::getAddTime);
        }
        Page<BspUacUserDO> bspUacUserPage = selectPage(page, wrapper);
        return new PageResult<>(bspUacUserPage.getRecords(), bspUacUserPage.getTotal());
    }
    default List<BspUacUserDO> selectList(BspUacUserListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BspUacUserDO>()
            .eqIfPresent(BspUacUserDO::getCityId, reqVO.getCityId())
            .eqIfPresent(BspUacUserDO::getRegionId, reqVO.getRegionId())
            .likeIfPresent(BspUacUserDO::getRegionName, reqVO.getRegionName())
            .eqIfPresent(BspUacUserDO::getRegionCode, reqVO.getRegionCode())
            .eqIfPresent(BspUacUserDO::getOrgId, reqVO.getOrgId())
            .eqIfPresent(BspUacUserDO::getLoginId, reqVO.getLoginId())
            .eqIfPresent(BspUacUserDO::getPassword, reqVO.getPassword())
            .likeIfPresent(BspUacUserDO::getName, reqVO.getName())
            .eqIfPresent(BspUacUserDO::getSex, reqVO.getSex())
            .eqIfPresent(BspUacUserDO::getIdCard, reqVO.getIdCard())
            .eqIfPresent(BspUacUserDO::getEmail, reqVO.getEmail())
            .eqIfPresent(BspUacUserDO::getMobile, reqVO.getMobile())
            .eqIfPresent(BspUacUserDO::getOfficeTel, reqVO.getOfficeTel())
            .eqIfPresent(BspUacUserDO::getIp, reqVO.getIp())
            .eqIfPresent(BspUacUserDO::getScode, reqVO.getScode())
            .eqIfPresent(BspUacUserDO::getLastIp, reqVO.getLastIp())
            .eqIfPresent(BspUacUserDO::getLastVisit, reqVO.getLastVisit())
            .eqIfPresent(BspUacUserDO::getIsDisabled, reqVO.getIsDisabled())
            .eqIfPresent(BspUacUserDO::getOrderId, reqVO.getOrderId())
            .eqIfPresent(BspUacUserDO::getLoginMethod, reqVO.getLoginMethod())
            .eqIfPresent(BspUacUserDO::getPosition, reqVO.getPosition())
            .eqIfPresent(BspUacUserDO::getPoliceRank, reqVO.getPoliceRank())
            .eqIfPresent(BspUacUserDO::getIsFj, reqVO.getIsFj())
            .eqIfPresent(BspUacUserDO::getFjLx, reqVO.getFjLx())
            .eqIfPresent(BspUacUserDO::getRemark, reqVO.getRemark())
            .eqIfPresent(BspUacUserDO::getIsdel, reqVO.getIsdel())
            .eqIfPresent(BspUacUserDO::getIsSync, reqVO.getIsSync())
            .eqIfPresent(BspUacUserDO::getConfirm, reqVO.getConfirm())
            .eqIfPresent(BspUacUserDO::getValidType, reqVO.getValidType())
            .betweenIfPresent(BspUacUserDO::getEndTime, reqVO.getEndTime())
            .eqIfPresent(BspUacUserDO::getTempPass, reqVO.getTempPass())
            .eqIfPresent(BspUacUserDO::getUserGroup, reqVO.getUserGroup())
            .betweenIfPresent(BspUacUserDO::getPassExpireDate, reqVO.getPassExpireDate())
            .eqIfPresent(BspUacUserDO::getGmPass, reqVO.getGmPass())
            .eqIfPresent(BspUacUserDO::getGmIdCard, reqVO.getGmIdCard())
            .eqIfPresent(BspUacUserDO::getGmMobile, reqVO.getGmMobile())
            .eqIfPresent(BspUacUserDO::getGmPassSign, reqVO.getGmPassSign())
            .eqIfPresent(BspUacUserDO::getGmIdCardSign, reqVO.getGmIdCardSign())
            .eqIfPresent(BspUacUserDO::getGmMobileSign, reqVO.getGmMobileSign())
        .orderByDesc(BspUacUserDO::getAddTime));    }


    }
