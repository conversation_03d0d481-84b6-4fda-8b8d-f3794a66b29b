package com.rs.module.uac.controller.admin.user.vo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

@ApiModel(description = "管理后台 - 用户头像新增/修改 Request VO")
@Data
public class UacUserPhotoSaveReqVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("用户id")
    private String id;

    @ApiModelProperty("头像")
    private String photo;

}
