package com.rs.module.uac.entity.user;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 用户 DO
 *
 * <AUTHOR>
 */
@TableName("uac_user")
@KeySequence("uac_user_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BspUacUserDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 地市Id
     */
    private String cityId;
    /**
     * 区域Id
     */
    private String regionId;
    /**
     * 区域名称
     */
    private String regionName;
    /**
     * 区域代码
     */
    private String regionCode;
    /**
     * 机构Id
     */
    private String orgId;
    /**
     * 登录名
     */
    private String loginId;
    /**
     * 登录密码
     */
    private String password;
    /**
     * 姓名
     */
    private String name;
    /**
     * 性别
     */
    private String sex;
    /**
     * 身份证号码
     */
    private String idCard;
    /**
     * 邮箱地址
     */
    private String email;
    /**
     * 移动电话
     */
    private String mobile;
    /**
     * 办公电话
     */
    private String officeTel;
    /**
     * IP地址
     */
    private String ip;
    /**
     * 拼音码
     */
    private String scode;
    /**
     * 上次访问IP
     */
    private String lastIp;
    /**
     * 上次访问时间
     */
    private Date lastVisit;
    /**
     * 是否禁用(0否1是)
     */
    private String isDisabled;
    /**
     * 排序Id
     */
    private Integer orderId;
    /**
     * 登录方式
     */
    private String loginMethod;
    /**
     * 职务
     */
    private String position;
    /**
     * 警衔
     */
    private String policeRank;
    /**
     * 是否辅警(0否1是)
     */
    private String isFj;
    /**
     * 辅警类型
     */
    private String fjLx;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否删除(0否1是)
     */
    private String isdel;
    /**
     * 是否同步数据(0否1是)
     */
    private String isSync;
    /**
     * 用户身份证号确认状态(0:否1:是)
     */
    private String confirm;
    /**
     * 帐号有效期：1:永久，2：临时
     */
    private String validType;
    /**
     * 帐号到期日期
     */
    private Date endTime;
    /**
     * 临时密码
     */
    private String tempPass;
    /**
     * 用户组
     */
    private String userGroup;
    /**
     * 密码过期时间
     */
    private Date passExpireDate;
    /**
     * 密码国密加密
     */
    private String gmPass;
    /**
     * 身份证国密加密
     */
    private String gmIdCard;
    /**
     * 移动电话国密加密
     */
    private String gmMobile;
    /**
     * 密码国密加密签名
     */
    private String gmPassSign;
    /**
     * 身份证国密加密签名
     */
    private String gmIdCardSign;
    /**
     * 移动电话国密加密签名
     */
    private String gmMobileSign;

}
