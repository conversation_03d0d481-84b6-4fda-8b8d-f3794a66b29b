package com.rs.module.uac.service.user;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.rs.module.uac.controller.admin.user.vo.UacUserPhotoListReqVO;
import com.rs.module.uac.controller.admin.user.vo.UacUserPhotoPageReqVO;
import com.rs.module.uac.controller.admin.user.vo.UacUserPhotoSaveReqVO;
import com.rs.framework.mybatis.config.GlobalConstant;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.uac.entity.user.UacUserPhotoDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.uac.dao.user.UacUserPhotoDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 用户头像 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@DS(GlobalConstant.BSP_DATASOURCE_KEY)
public class UacUserPhotoServiceImpl extends BaseServiceImpl<UacUserPhotoDao, UacUserPhotoDO> implements UacUserPhotoService {

    @Resource
    private UacUserPhotoDao userPhotoDao;

    @Override
    public String createUserPhoto(UacUserPhotoSaveReqVO createReqVO) {
        // 插入
        UacUserPhotoDO userPhoto = BeanUtils.toBean(createReqVO, UacUserPhotoDO.class);
        userPhotoDao.insert(userPhoto);
        // 返回
        return userPhoto.getId();
    }

    @Override
    public void updateUserPhoto(UacUserPhotoSaveReqVO updateReqVO) {
        // 校验存在
        validateUserPhotoExists(updateReqVO.getId());
        // 更新
        UacUserPhotoDO updateObj = BeanUtils.toBean(updateReqVO, UacUserPhotoDO.class);
        userPhotoDao.updateById(updateObj);
    }

    @Override
    public void deleteUserPhoto(String id) {
        // 校验存在
        validateUserPhotoExists(id);
        // 删除
        userPhotoDao.deleteById(id);
    }

    private void validateUserPhotoExists(String id) {
        if (userPhotoDao.selectById(id) == null) {
            throw new ServerException("用户头像数据不存在");
        }
    }

    @Override
    public UacUserPhotoDO getUserPhoto(String id) {
        return userPhotoDao.selectById(id);
    }

    @Override
    public PageResult<UacUserPhotoDO> getUserPhotoPage(UacUserPhotoPageReqVO pageReqVO) {
        return userPhotoDao.selectPage(pageReqVO);
    }

    @Override
    public List<UacUserPhotoDO> getUserPhotoList(UacUserPhotoListReqVO listReqVO) {
        return userPhotoDao.selectList(listReqVO);
    }


}
