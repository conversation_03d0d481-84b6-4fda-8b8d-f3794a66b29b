package com.rs.module.uac.service.user;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.entity.uac.UacUserPhotoRespVO;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.config.GlobalConstant;
import com.rs.module.uac.controller.admin.user.vo.*;
import com.rs.module.uac.dao.user.BspUacUserDao;
import com.rs.module.uac.entity.user.BspUacUserDO;
import com.rs.module.uac.entity.user.UacUserPhotoDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 用户 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@DS(GlobalConstant.BSP_DATASOURCE_KEY)
public class BspUacUserServiceImpl extends BaseServiceImpl<BspUacUserDao, BspUacUserDO> implements BspUacUserService {

    @Resource
    private BspUacUserDao bspUacUserDao;

    @Resource
    private UacUserPhotoService uacUserPhotoService;

    @Override
    public String createBspUacUser(BspUacUserSaveReqVO createReqVO) {
        // 插入
        BspUacUserDO bspUacUser = BeanUtils.toBean(createReqVO, BspUacUserDO.class);
        bspUacUserDao.insert(bspUacUser);
        // 返回
        return bspUacUser.getId();
    }

    @Override
    public void updateBspUacUser(BspUacUserSaveReqVO updateReqVO) {
        // 校验存在
        validateBspUacUserExists(updateReqVO.getId());
        // 更新
        BspUacUserDO updateObj = BeanUtils.toBean(updateReqVO, BspUacUserDO.class);
        bspUacUserDao.updateById(updateObj);
    }

    @Override
    public void deleteBspUacUser(String id) {
        // 校验存在
        validateBspUacUserExists(id);
        // 删除
        bspUacUserDao.deleteById(id);
    }

    private void validateBspUacUserExists(String id) {
        if (bspUacUserDao.selectById(id) == null) {
            throw new ServerException("用户数据不存在");
        }
    }

    @Override
    public BspUacUserDO getBspUacUser(String id) {
        return bspUacUserDao.selectById(id);
    }

    @Override
    public PageResult<BspUacUserDO> getBspUacUserPage(BspUacUserPageReqVO pageReqVO) {
        return bspUacUserDao.selectPage(pageReqVO);
    }

    @Override
    public List<BspUacUserDO> getBspUacUserList(BspUacUserListReqVO listReqVO) {
        return bspUacUserDao.selectList(listReqVO);
    }

    @Override
    public BspUacUserRespVO getUserById(String id) {
        BspUacUserDO bspUacUserDO = bspUacUserDao.selectById(id);
        BspUacUserRespVO bspUacUserRespVO = BeanUtils.toBean(bspUacUserDO, BspUacUserRespVO.class);
        UacUserPhotoDO uacUserPhotoDO = uacUserPhotoService.getById(bspUacUserRespVO.getId());
        if (uacUserPhotoDO != null) {
            bspUacUserRespVO.setPhotoVo(BeanUtils.toBean(uacUserPhotoDO, UacUserPhotoRespVO.class));
        }
        return bspUacUserRespVO;
    }


}
