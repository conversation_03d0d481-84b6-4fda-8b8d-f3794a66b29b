package com.rs.module.uac.controller.admin.user.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 用户分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BspUacUserPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("地市Id")
    private String cityId;

    @ApiModelProperty("区域Id")
    private String regionId;

    @ApiModelProperty("区域名称")
    private String regionName;

    @ApiModelProperty("区域代码")
    private String regionCode;

    @ApiModelProperty("机构Id")
    private String orgId;

    @ApiModelProperty("登录名")
    private String loginId;

    @ApiModelProperty("登录密码")
    private String password;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("性别")
    private String sex;

    @ApiModelProperty("身份证号码")
    private String idCard;

    @ApiModelProperty("邮箱地址")
    private String email;

    @ApiModelProperty("移动电话")
    private String mobile;

    @ApiModelProperty("办公电话")
    private String officeTel;

    @ApiModelProperty("IP地址")
    private String ip;

    @ApiModelProperty("拼音码")
    private String scode;

    @ApiModelProperty("上次访问IP")
    private String lastIp;

    @ApiModelProperty("上次访问时间")
    private Date lastVisit;

    @ApiModelProperty("是否禁用(0否1是)")
    private String isDisabled;

    @ApiModelProperty("排序Id")
    private Integer orderId;

    @ApiModelProperty("登录方式")
    private String loginMethod;

    @ApiModelProperty("职务")
    private String position;

    @ApiModelProperty("警衔")
    private String policeRank;

    @ApiModelProperty("是否辅警(0否1是)")
    private String isFj;

    @ApiModelProperty("辅警类型")
    private String fjLx;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否删除(0否1是)")
    private String isdel;

    @ApiModelProperty("是否同步数据(0否1是)")
    private String isSync;

    @ApiModelProperty("用户身份证号确认状态(0:否1:是)")
    private String confirm;

    @ApiModelProperty("帐号有效期：1:永久，2：临时")
    private String validType;

    @ApiModelProperty("帐号到期日期")
    private Date[] endTime;

    @ApiModelProperty("临时密码")
    private String tempPass;

    @ApiModelProperty("用户组")
    private String userGroup;

    @ApiModelProperty("密码过期时间")
    private Date[] passExpireDate;

    @ApiModelProperty("密码国密加密")
    private String gmPass;

    @ApiModelProperty("身份证国密加密")
    private String gmIdCard;

    @ApiModelProperty("移动电话国密加密")
    private String gmMobile;

    @ApiModelProperty("密码国密加密签名")
    private String gmPassSign;

    @ApiModelProperty("身份证国密加密签名")
    private String gmIdCardSign;

    @ApiModelProperty("移动电话国密加密签名")
    private String gmMobileSign;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
