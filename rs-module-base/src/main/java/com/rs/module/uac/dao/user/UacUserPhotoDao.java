package com.rs.module.uac.dao.user;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.module.uac.controller.admin.user.vo.UacUserPhotoListReqVO;
import com.rs.module.uac.controller.admin.user.vo.UacUserPhotoPageReqVO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.uac.entity.user.UacUserPhotoDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 用户头像 Dao
*
* <AUTHOR>
*/
@Mapper
public interface UacUserPhotoDao extends IBaseDao<UacUserPhotoDO> {


    default PageResult<UacUserPhotoDO> selectPage(UacUserPhotoPageReqVO reqVO) {
        Page<UacUserPhotoDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<UacUserPhotoDO> wrapper = new LambdaQueryWrapperX<UacUserPhotoDO>()
            .eqIfPresent(UacUserPhotoDO::getPhoto, reqVO.getPhoto())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(UacUserPhotoDO::getAddTime);
        }
        Page<UacUserPhotoDO> userPhotoPage = selectPage(page, wrapper);
        return new PageResult<>(userPhotoPage.getRecords(), userPhotoPage.getTotal());
    }
    default List<UacUserPhotoDO> selectList(UacUserPhotoListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<UacUserPhotoDO>()
            .eqIfPresent(UacUserPhotoDO::getPhoto, reqVO.getPhoto())
        .orderByDesc(UacUserPhotoDO::getAddTime));    }


    }
