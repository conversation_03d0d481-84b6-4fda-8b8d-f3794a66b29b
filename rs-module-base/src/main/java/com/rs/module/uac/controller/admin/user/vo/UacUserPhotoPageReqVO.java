package com.rs.module.uac.controller.admin.user.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;

@ApiModel(description = "管理后台 - 用户头像分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UacUserPhotoPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("头像")
    private String photo;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
