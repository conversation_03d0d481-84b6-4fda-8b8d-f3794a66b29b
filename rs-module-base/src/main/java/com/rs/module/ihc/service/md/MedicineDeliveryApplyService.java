package com.rs.module.ihc.service.md;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.base.vo.SimpleApproveReqVO;
import com.rs.module.ihc.controller.admin.md.vo.*;
import com.rs.module.ihc.entity.md.MedicineDeliveryApplyDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 药品顾送管理-药品顾送申请 Service 接口
 *
 * <AUTHOR>
 */
public interface MedicineDeliveryApplyService extends IBaseService<MedicineDeliveryApplyDO>{

    /**
     * 创建药品顾送管理-药品顾送申请
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMedicineDeliveryApply(@Valid MedicineDeliveryApplySaveReqVO createReqVO);

    /**
     * 删除药品顾送管理-药品顾送申请
     *
     * @param id 编号
     */
    void deleteMedicineDeliveryApply(String id);

    /**
     * 获得药品顾送管理-药品顾送申请
     *
     * @param id 编号
     * @return 药品顾送管理-药品顾送申请
     */
    MedicineDeliveryApplyRespVO getMedicineDeliveryApply(String id);

    /**
    * 获得药品顾送管理-药品顾送申请分页
    *
    * @param pageReqVO 分页查询
    * @return 药品顾送管理-药品顾送申请分页
    */
    PageResult<MedicineDeliveryApplyAppRespVO> getMedicineDeliveryApplyPage(MedicineDeliveryApplyAppPageReqVO pageReqVO);

    /**
    * 获得药品顾送管理-药品顾送申请列表
    *
    * @param listReqVO 查询条件
    * @return 药品顾送管理-药品顾送申请列表
    */
    List<MedicineDeliveryApplyDO> getMedicineDeliveryApplyList(MedicineDeliveryApplyListReqVO listReqVO);

    /**
     * 审批信息
     * <AUTHOR>
     * @date 2025/7/7 15:29
     * @param [approveReqVO]
     * @return void
     */
    void approve(SimpleApproveReqVO approveReqVO);

    /**
     * 药品顾送登记
     * <AUTHOR>
     * @date 2025/7/7 15:36
     * @param [regInfoReqVO]
     * @return void
     */
    void regInfo(MedicineDeliveryApplyRegInfoReqVO regInfoReqVO);

    /**
     * 异常登记
     * <AUTHOR>
     * @date 2025/7/7 15:37
     * @param [regInfoReqVO]
     * @return void
     */
    void abnormalRegInfo(MedicineDeliveryApplyAbnormalReqVO regInfoReqVO);

    /**
     * 接口默认分页信息
     * <AUTHOR>
     * @date 2025/7/8 9:05
     * @param [pageReqVO]
     * @return com.rs.framework.common.pojo.PageResult<com.rs.module.ihc.entity.md.MedicineDeliveryApplyDO>
     */
    PageResult<MedicineDeliveryApplyDO> getMedicineDeliveryApplyDefaultPage(MedicineDeliveryApplyPageReqVO pageReqVO);
}
