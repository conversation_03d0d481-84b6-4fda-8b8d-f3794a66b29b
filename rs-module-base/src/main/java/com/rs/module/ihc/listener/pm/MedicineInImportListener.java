package com.rs.module.ihc.listener.pm;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineCodeImportVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineInImportListVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineInImportVO;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * @ClassName MedicineInImportListener
 * 
 * <AUTHOR>
 * @Date 2025/3/20 20:46
 * @Version 1.0
 */
public class MedicineInImportListener implements ReadListener<MedicineInImportVO> {
    List<MedicineInImportVO> cachedDataList;
    MedicineInImportListVO medicineInImportListVO;
    public MedicineInImportListener(List<MedicineInImportVO> cachedDataList, MedicineInImportListVO medicineInImportListVO) {
        this.cachedDataList = cachedDataList;
        this.medicineInImportListVO = medicineInImportListVO;
    }
    @Override
    public void invoke(MedicineInImportVO medicineInImportVO, AnalysisContext analysisContext) {
        this.cachedDataList.add(medicineInImportVO);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        headMap.forEach((key, value) -> {
            if (StringUtils.isNotBlank(value.getStringValue())) {
                String stringValue = value.getStringValue();
                if (stringValue.contains("随货单号：")) {
                    medicineInImportListVO.setShdh(stringValue.replace("随货单号：", ""));
                }
            }
        });
        ReadListener.super.invokeHead(headMap, context);
    }
}
