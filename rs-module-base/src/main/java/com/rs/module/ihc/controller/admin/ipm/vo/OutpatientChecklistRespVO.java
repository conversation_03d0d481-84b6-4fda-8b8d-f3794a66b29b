package com.rs.module.ihc.controller.admin.ipm.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.annotation.Query;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 所内就医-所内门诊-检查单 Response VO")
@Data
public class OutpatientChecklistRespVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("所内门诊id，对应ihc_ipm_outpatient.id")
    private String outpatientId;
    @ApiModelProperty("主诉")
    private String mainComplaint;
    @ApiModelProperty("病情简述")
    private String illnessResume;
    @ApiModelProperty("检查结论")
    private String checkConclusion;
    @ApiModelProperty("检查单编号")
    private String checklistNum;
    @ApiModelProperty("检查单种类 多选  ,分割")
    private String checklistCategory;
    @ApiModelProperty("检查单状态")
    private String checklistStatus;
    @ApiModelProperty("登记人，对应permission_user.userid")
    private String registerUserid;
    @ApiModelProperty("登记人名称")
    private String registerUserName;
    @ApiModelProperty("登记时间")
    private Date registerTime;
    @ApiModelProperty("监所id")
    private String prisonId;
    private String jgrybm;

    @ApiModelProperty("所内就医-所内门诊-检查单-检查种类列表")
    @Query(sql = "select * from ihc_ipm_outpatient_checklist_category where checklist_id = '${id}' and is_del =0",beanClass = OutpatientChecklistCategoryRespVO.class)
    private List<OutpatientChecklistCategoryRespVO> outpatientChecklistCategorys;

}
