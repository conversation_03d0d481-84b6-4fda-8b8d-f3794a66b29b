package com.rs.module.ihc.controller.admin.pm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 大药房管理-药品信息新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PharmacyMedicineSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("药房ID")
    private String pharmacyId;

    @ApiModelProperty("药品名称")
    private String medicineName;

    @ApiModelProperty("药品名称全拼")
    private String medicineSpellName;

    @ApiModelProperty("药品名称拼音首字母或者英文拼接")
    private String medicineNameFirstLetter;

    @ApiModelProperty("剂型（字典:剂型）")
    private String dosageForm;

    @ApiModelProperty("计量单位（字典：计量单位）")
    private String measurementUnit;

    @ApiModelProperty("规格")
    private String specs;

    @ApiModelProperty("生产单位")
    private String productUnit;

    @ApiModelProperty("批准文号")
    private String approvalNum;

    @ApiModelProperty("原批准文号")
    private String originalApprovalNum;

    @ApiModelProperty("药品本位码")
    private String medicineStandardCode;

    @ApiModelProperty("是否毒麻品 (0：否 1：是)")
    private String drug;

    @ApiModelProperty("每日用量")
    private String dayDosage;

    @ApiModelProperty("每次用量")
    private String oneDosage;

    @ApiModelProperty("库存预警值 按最小单位")
    private BigDecimal inventoryWarnValue;

    @ApiModelProperty("是否可报损（0：否 1：是）")
    private String reportableLoss;

    @ApiModelProperty("英文名称")
    private String medicineEnglishName;

    @ApiModelProperty("商品名称")
    private String productName;

    @ApiModelProperty("上市许可持有人")
    private String marketApprovalHolder;

    @ApiModelProperty("上市许可持有人地址")
    private String marketApprovalHolderAddress;

    @ApiModelProperty("生产地址")
    private String productAddress;

    @ApiModelProperty("药品类别")
    private String medicineCategory;

    @ApiModelProperty("药品本位编码备注")
    private String medicineStandardCodeRemark;

    @ApiModelProperty("药品别名")
    private String medicineAlias;

    @ApiModelProperty("包装单位")
    private String packageUnit;

    @ApiModelProperty("批准日期")
    private Date approvalDate;

    @ApiModelProperty("总库存数量")
    private BigDecimal totalInventoryNum;

    @ApiModelProperty("是否有过期批次药 (0：否 1：是)")
    private Integer hasExpireDateBatch;

    @ApiModelProperty("最小计量单位（字典值：计量单位)")
    private String minMeasurementUnit;

    @ApiModelProperty("计量单位转最小计量单位转化比")
    private Integer unitConversionRatio;

    @ApiModelProperty("是否属于敏感药 (0：否 1：是)")
    private Integer belongSensitive;

    @ApiModelProperty("是否属于精神药品 (0：否 1：是)")
    private Short paychoactieDrug;

    @ApiModelProperty("监所id")
    private String prisonId;

    @ApiModelProperty("药品名字拼音")
    private String pinyin;

    @ApiModelProperty("药品名字简拼")
    private String jp;

    @ApiModelProperty("排序")
    private String px;

}
