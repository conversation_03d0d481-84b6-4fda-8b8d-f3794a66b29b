package com.rs.module.ihc.entity.pm;

import lombok.*;
import java.util.*;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 药房管理-国家药品编码本位码信息 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_pm_medicine_code")
@KeySequence("ihc_pm_medicine_code_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MedicineCodeDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 药品来源类型（1： 代表国产，2 ：代表进口）
     */
    private Short originType;
    /**
     * 药品批准文号
     */
    private String approvalNum;
    /**
     * 产品名称
     */
    private String medicineName;
    /**
     * 剂型（字典:剂型）
     */
    private String dosageForm;
    /**
     * 规格
     */
    private String specs;
    /**
     * 上市许可持有人
     */
    private String marketApprovalHolder;
    /**
     * 上市许可持有人英文
     */
    private String marketApprovalHolderEn;
    /**
     * 生产单位
     */
    private String productUnit;
    /**
     * 生产单位英文
     */
    private String productUnitEn;
    /**
     * 药品本位码
     */
    private String medicineStandardCode;
    /**
     * 药品本位编码备注
     */
    private String medicineStandardCodeRemark;
    /**
     * 监所id
     */
    private String prisonId;
    /**
     * 注册证号
     */
    private String zczh;

}
