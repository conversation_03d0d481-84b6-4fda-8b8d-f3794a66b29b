package com.rs.module.ihc.constant;



import com.rs.framework.mybatis.util.BizAssert;

import java.util.HashMap;
import java.util.Map;

/**
 * 是否同意常量
 * 字典值 {@link IhcsDictionaryConstant#IS_AGREE}
 */
public interface IsAgreeConstant {

    /**
     * 不同意
     */
    String DISAGREE = "0";

    /**
     * 同意
     */
    String AGREE = "1";


    /**
     * 工作流审批结果转换
     */
    class WorkFlowConverter {
        private static final Map<String, Integer> convertMap = new HashMap<>();

        static {
            convertMap.put(DISAGREE, 0);
            convertMap.put(AGREE, 1);
        }

        public static Integer convert(String value) {
            Integer workFlowStatus = convertMap.get(value);
            BizAssert.notNull(workFlowStatus, "审批结果转参数异常");
            return workFlowStatus;
        }

    }
}
