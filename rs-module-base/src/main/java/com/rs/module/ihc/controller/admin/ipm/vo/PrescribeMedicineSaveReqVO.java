package com.rs.module.ihc.controller.admin.ipm.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@ApiModel(description = "管理后台 - 所内就医-处方-药方信息新增/修改 Request VO")
@Data
public class PrescribeMedicineSaveReqVO implements TransPojo {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("处方id，对应ihc_ipm_prescribe.id")
    private String mlh;

    @ApiModelProperty("药品id，对应ihc_pm_drug.id")
    @NotNull(message = "药品id，对应ihc_pm_drug.id不能为空")
    private String medicineId;

    @ApiModelProperty("总库存数量")
    private BigDecimal totalInventoryNum;

    @ApiModelProperty("每次用量（数字）")
    @NotNull(message = "每次用量（数字）不能为空")
    private BigDecimal oneDosageNum;

    @ApiModelProperty("单位")
    @NotNull(message = "单位不能为空")
    private String unit;

    @ApiModelProperty("使用频率")
    @NotNull(message = "使用频率不能为空")
    private String useFrequency;

    @ApiModelProperty("用药天数")
    @NotNull(message = "用药天数不能为空")
    private Integer useDay;

    @ApiModelProperty("总药量")
    private BigDecimal num;

    @ApiModelProperty("给药方式")
    @Trans(type = TransType.DICTIONARY,key = "ZD_USE_MEDICINE_METHOD")
    @NotNull(message = "给药方式不能为空")
    private String useMedicineMethod;

    @ApiModelProperty("嘱托")
    private String entrust;

}
