package com.rs.module.ihc.service.ipm;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.PrescribeMedicineDO;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.ipm.PrescribeMedicineDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 所内就医-处方-药方信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrescribeMedicineServiceImpl extends BaseServiceImpl<PrescribeMedicineDao, PrescribeMedicineDO> implements PrescribeMedicineService {

    @Resource
    private PrescribeMedicineDao prescribeMedicineDao;

    @Override
    public String createPrescribeMedicine(PrescribeMedicineSaveReqVO createReqVO) {
        // 插入
        PrescribeMedicineDO prescribeMedicine = BeanUtils.toBean(createReqVO, PrescribeMedicineDO.class);
        prescribeMedicineDao.insert(prescribeMedicine);
        // 返回
        return prescribeMedicine.getId();
    }

    @Override
    public void updatePrescribeMedicine(PrescribeMedicineSaveReqVO updateReqVO) {
        // 校验存在
        validatePrescribeMedicineExists(updateReqVO.getId());
        // 更新
        PrescribeMedicineDO updateObj = BeanUtils.toBean(updateReqVO, PrescribeMedicineDO.class);
        prescribeMedicineDao.updateById(updateObj);
    }

    @Override
    public void deletePrescribeMedicine(String id) {
        // 校验存在
        validatePrescribeMedicineExists(id);
        // 删除
        prescribeMedicineDao.deleteById(id);
    }

    private void validatePrescribeMedicineExists(String id) {
        if (prescribeMedicineDao.selectById(id) == null) {
            throw new ServerException("所内就医-处方-药方信息数据不存在");
        }
    }

    @Override
    public PrescribeMedicineDO getPrescribeMedicine(String id) {
        return prescribeMedicineDao.selectById(id);
    }

}
