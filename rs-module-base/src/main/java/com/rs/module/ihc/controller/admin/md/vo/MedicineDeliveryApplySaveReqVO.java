package com.rs.module.ihc.controller.admin.md.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 药品顾送管理-药品顾送申请新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MedicineDeliveryApplySaveReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    //@ApiModelProperty("主键")
    //private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("申请-药品名称")
    @NotEmpty(message = "申请-药品名称不能为空")
    private String applyMedicineName;

    @ApiModelProperty("申请-剂型（字典:剂型）")
    @NotEmpty(message = "申请-剂型（字典:剂型  ZD_DOSAGE_FORM）不能为空")
    private String applyDosageForm;

    @ApiModelProperty("申请规格")
    @NotEmpty(message = "申请规格不能为空")
    private String applySpecs;

    @ApiModelProperty("申请-生产单位")
    @NotEmpty(message = "申请-生产单位不能为空")
    private String applyProductUnit;

    @ApiModelProperty("申请-批准文号")
    @NotEmpty(message = "申请-批准文号不能为空")
    private String applyApprovalNum;

    @ApiModelProperty("申请-原批准文号")
    @NotEmpty(message = "申请-原批准文号不能为空")
    private String applyOriginalApprovalNum;

    @ApiModelProperty("申请-数量")
    @NotNull(message = "申请-数量不能为空")
    private BigDecimal applyTotalNum;

    @ApiModelProperty("期望送药开始日期")
    @NotNull(message = "期望送药开始日期不能为空")
    private Date expectedStartDate;

    @ApiModelProperty("期望送药结束日期")
    @NotNull(message = "期望送药结束日期不能为空")
    private Date expectedEndDate;

    @ApiModelProperty("顾送药来源 字典：ZD_YPGS_GSYLY")
    @NotEmpty(message = "顾送药来源 字典：ZD_YPGS_GSYLY不能为空")
    private String drugSource;

    @ApiModelProperty("顾送药原因 字典： ZD_YPGS_GSYY")
    @NotEmpty(message = "顾送药原因 字典： ZD_YPGS_GSYY不能为空")
    private String deliveryReason;

    @ApiModelProperty("备注")
    private String remark;

}
