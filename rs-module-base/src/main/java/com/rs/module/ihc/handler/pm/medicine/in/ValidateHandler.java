package com.rs.module.ihc.handler.pm.medicine.in;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.framework.common.handler.AbstractHandler;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineInImportListVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineInImportVO;
import com.rs.module.ihc.entity.pm.MedicineDO;
import com.rs.module.ihc.entity.pm.MedicineInDO;
import com.rs.module.ihc.service.pm.MedicineInService;
import com.rs.module.ihc.service.pm.MedicineService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * @ClassName ValidateHandler
 *
 * <AUTHOR>
 * @Date 2025/3/20 19:31
 * @Version 1.0
 */
@Component
public class ValidateHandler extends AbstractHandler<MedicineInImportListVO> {
    @Autowired
    MedicineInService medicineInService;
    @Autowired
    MedicineService medicineService;
    @Override
    protected MedicineInImportListVO doHandle(MedicineInImportListVO input) {
        if (StringUtils.isEmpty(input.getShdh())) {
            throw new RuntimeException("随货单号不能为空");
        }
        List<MedicineInImportVO> medicineInImportVOList = input.getMedicineInImportVOList();
        Iterator<MedicineInImportVO> iterator = medicineInImportVOList.iterator();
        while (iterator.hasNext()) {
            List<String> msgList = new ArrayList<>();
            MedicineInImportVO medicineInImportVO = iterator.next();
            String approvalNum = medicineInImportVO.getApprovalNum();
            if (StringUtils.isEmpty(approvalNum)) {
                msgList.add("批准文号不能为空");
            }
            if (StringUtils.isEmpty(medicineInImportVO.getUnit())) {
                msgList.add("计量单位不能为空");
            }
            if (medicineInImportVO.getSl() == null) {
                msgList.add("入库数量不能为空");
            }
            if (StringUtils.isEmpty(medicineInImportVO.getYxq())) {
                msgList.add("有效期不能为空");
            }
            if (StringUtils.isEmpty(medicineInImportVO.getScrq())) {
                msgList.add("生产日期不能为空");
            }

            MedicineDO medicineDO = medicineService.getOne(new LambdaQueryWrapper<MedicineDO>()
                    .eq(MedicineDO::getApprovalNum, medicineInImportVO.getApprovalNum()));
            if (medicineDO == null) {
                msgList.add("药品不存在，请导入已有的药品");
            }else {
                MedicineInDO medicineInDO = medicineInService.getOne(new LambdaQueryWrapper<MedicineInDO>()
                        .eq(MedicineInDO::getMedicineId, medicineDO.getId())
                        .eq(MedicineInDO::getBatchCode, input.getShdh()));
                if (medicineInDO != null) {
                    msgList.add("该药品已入库，请勿重复入库");
                }
            }
            if (medicineDO != null) {
                if (medicineDO.getUnitConversionRatio() == null) {
                    msgList.add("该药品的计量单位转最小计量单位转化比为空，无法计算库存");
                }
            }

            // 定义日期格式
            DateTimeFormatter[] formatters = {
                    DateTimeFormatter.ofPattern("yyyyMMdd"),
                    DateTimeFormatter.ofPattern("yyyy/MM/dd"),
                    DateTimeFormatter.ofPattern("yyyy-MM-dd"),
                    DateTimeFormatter.ofPattern("yyyy年MM月dd")
            };

            LocalDate yxqDate = null;
            if (StringUtils.isNotEmpty(medicineInImportVO.getYxq())) {
                for (DateTimeFormatter formatter : formatters) {
                    try {
                        yxqDate = LocalDate.parse(medicineInImportVO.getYxq(), formatter);
                        break;
                    } catch (Exception e) {
                        // 继续尝试下一个格式
                    }
                }
            }

            LocalDate scrqDate = null;
            if (StringUtils.isNotEmpty(medicineInImportVO.getScrq())) {
                for (DateTimeFormatter formatter : formatters) {
                    try {
                        scrqDate = LocalDate.parse(medicineInImportVO.getScrq(), formatter);
                        break;
                    } catch (Exception e) {
                        // 继续尝试下一个格式
                    }
                }
            }

            if (yxqDate == null) {
                msgList.add("有效期至格式不正确，支持格式，如：20190101、2019/01/01、2019-01-01");
            }else {
                if (scrqDate.isAfter(yxqDate)) {
                    msgList.add("有效期至不能早于生产日期");
                }
                //yxqDate不能早于当期日期
                if (yxqDate.isBefore(LocalDate.now())) {
                    msgList.add("有效期至不能早于当前日期");
                }

                medicineInImportVO.setYxqDate(Date.from(yxqDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            }
            if (scrqDate == null) {
                msgList.add("有生产日期至格式不正确，支持格式，如：20190101、2019/01/01、2019-01-01");
            }
            medicineInImportVO.setShdh(input.getShdh());
            if (!msgList.isEmpty()) {
                String msg = String.join(";", msgList);
                medicineInImportVO.setErrMsg(msg);
                input.getMedicineInImportVOErrorList().add(medicineInImportVO);
                iterator.remove();
            }
        }

        return input;
    }
}
