package com.rs.module.ihc.entity.ephe;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 卫生防疫与健康教育-卫生消毒登记 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_ephe_disinfect")
@KeySequence("ihc_ephe_disinfect_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ihc_ephe_disinfect")
public class DisinfectDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 夏季每周洗热水澡次数
     */
    private String xjmzxrszcs;
    /**
     * 每15天热水澡次数
     */
    private Integer mswtrszcs;
    /**
     * 每周晾晒被褥次数	
     */
    private Integer mzlsbrcs;
    /**
     * 每季度拆洗被褥次数
     */
    private Integer mjdcxbrcs;
    /**
     * 每周消毒次数
     */
    private Integer mzxdcs;
    /**
     * 登记民警身份证号
     */
    private String operatePoliceSfzh;
    /**
     * 登记民警
     */
    private String operatePolice;
    /**
     * 登记时间
     */
    private Date operateTime;

}
