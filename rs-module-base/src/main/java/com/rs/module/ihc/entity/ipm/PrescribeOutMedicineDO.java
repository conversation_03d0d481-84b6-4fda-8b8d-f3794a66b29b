package com.rs.module.ihc.entity.ipm;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import lombok.*;
import java.util.*;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 所内就医-处方药品出库记录关联药品 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 所内就医-处方药品出库记录关联药品新增/修改 Request VO")
@TableName("ihc_ipm_prescribe_out_medicine")
@KeySequence("ihc_ipm_prescribe_out_medicine_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrescribeOutMedicineDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;
    /**
     * 添加人姓名
     */
    @ApiModelProperty("添加人姓名")
    private String addUserName;
    /**
     * 更新人姓名
     */
    @ApiModelProperty("更新人姓名")
    private String updateUserName;
    /**
     * 处方药品出库id，对应iihc_ipm_prescribe_out.id
     */
    @ApiModelProperty("处方药品出库id，对应iihc_ipm_prescribe_out.id")
    private String outId;
    /**
     * 药品id，对应ihc_pm_medicine.id
     */
    @ApiModelProperty("药品id，对应ihc_pm_medicine.id")
    private String medicineId;
    /**
     * 每次用量（数字）
     */
    @ApiModelProperty("每次用量（数字）")
    private BigDecimal oneDosageNum;
    /**
     * 使用频率
     */
    @ApiModelProperty("使用频率")
    @Trans(type = TransType.DICTIONARY,key = "ZD_USE_MEDICINE_FREQUENCY")
    private String useFrequency;
    /**
     * 用药天数
     */
    @ApiModelProperty("用药天数")
    private Integer useDay;
    /**
     * 总药量
     */
    @ApiModelProperty("总药量")
    private BigDecimal num;
    /**
     * 给药方式
     */
    @ApiModelProperty("给药方式")
    @Trans(type = TransType.DICTIONARY,key = "ZD_INTERNAL_MEDICAL_PRESCRIBE_EXECUTE_SOURCE")
    private String useMedicineMethod;
    /**
     * 出库数量
     */
    @ApiModelProperty("出库数量")
    private BigDecimal outNum;
    /**
     * 嘱托
     */
    @ApiModelProperty("嘱托")
    private String entrust;

    @ApiModelProperty("药品名称")
    @TableField(exist = false)
    private String medicineName;

    @ApiModelProperty("规格")
    @TableField(exist = false)
    private String specs;

    @ApiModelProperty("计量单位")
    @TableField(exist = false)
    @Trans(type = TransType.DICTIONARY,key = "ZD_MEASUREMENT_UNIT")
    private String measurementUnit;

    @ApiModelProperty("最小计量单位")
    @TableField(exist = false)
    @Trans(type = TransType.DICTIONARY,key = "ZD_MEASUREMENT_UNIT")
    private String minMeasurementUnit;

    @ApiModelProperty("计量单位转最小计量单位转化比")
    @TableField(exist = false)
    private String unitConversionRatio;

}
