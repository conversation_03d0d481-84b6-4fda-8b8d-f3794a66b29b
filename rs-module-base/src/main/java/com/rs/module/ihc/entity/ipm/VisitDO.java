package com.rs.module.ihc.entity.ipm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import java.util.Date;

/**
 * 所内就医-现场巡检 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_ipm_visit")
@KeySequence("ihc_ipm_visit_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VisitDO extends BaseDO {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 来源（1:所内就医预约，2:手动新增）
     */
    private String source;
    /**
     * 预约id，对应ihc_ipm_internal_medical_appointment.id
     */
    private String appointmentId;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 在押人员编号
     */
    private String rybh;
    /**
     * 在押人员名称
     */
    private String ryxm;
    /**
     * 是否重病号（ 0：否 ，1：是）
     */
    private Integer sickType;
    /**
     * 报病时间
     */
    private Date diseaseTime;
    /**
     * 报病原因
     */
    private String diseaseReason;
    /**
     * 主诉
     */
    private String mainComplaint;
    /**
     * 巡诊情况
     */
    private String visitState;
    /**
     * 巡诊处理方式
     */
    private String visitProcessMethod;
    /**
     * 巡诊时间
     */
    private Date visitTime;
    /**
     * 巡诊人证件号码
     */
    private String visitUserid;
    /**
     * 巡诊人名称
     */
    private String visitUserName;
    /**
     * 巡诊结论
     */
    private String visitConclusion;
    /**
     * 监所id
     */
    private String prisonId;
    /**
     * 械具使用建议
     */
    private String toolUseAdvise;
    /**
     * 械具解除原因
     */
    private String toolUseCancelReason;
    /**
     * 第三方数据id
     */
    private String extId;



}
