package com.rs.module.ihc.controller.admin.ipm.vo;

import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@ApiModel(description = "管理后台 - 所内就医-现场巡检分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VisitPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("来源（1:所内就医预约，2:手动新增）")
    private String source;

    @ApiModelProperty("预约id，对应ihc_ipm_internal_medical_appointment.id")
    private String appointmentId;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("在押人员编号")
    private String rybh;

    @ApiModelProperty("在押人员名称")
    private String ryxm;

    @ApiModelProperty("是否重病号（ 0：否 ，1：是）")
    private Integer sickType;

    @ApiModelProperty("报病时间")
    private Date[] diseaseTime;

    @ApiModelProperty("报病原因")
    private String diseaseReason;

    @ApiModelProperty("主诉")
    private String mainComplaint;

    @ApiModelProperty("巡诊情况")
    private String visitState;

    @ApiModelProperty("巡诊处理方式")
    private String visitProcessMethod;

    @ApiModelProperty("巡诊时间")
    private Date[] visitTime;

    @ApiModelProperty("巡诊人证件号码")
    private String visitUserid;

    @ApiModelProperty("巡诊人名称")
    private String visitUserName;

    @ApiModelProperty("巡诊结论")
    private String visitConclusion;

    @ApiModelProperty("监所id")
    private String prisonId;

    @ApiModelProperty("械具使用建议")
    private String toolUseAdvise;

    @ApiModelProperty("械具解除原因")
    private String toolUseCancelReason;

    @ApiModelProperty("第三方数据id")
    private String extId;

}
