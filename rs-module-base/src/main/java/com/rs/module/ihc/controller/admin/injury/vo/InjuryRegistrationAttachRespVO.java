package com.rs.module.ihc.controller.admin.injury.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 医疗子系统-伤亡登记-附件信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class InjuryRegistrationAttachRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("关联主表ID")
    private String registrationId;
    @ApiModelProperty("附件URL")
    private String attUrl;
    @ApiModelProperty("描述")
    private String remark;

    @ApiModelProperty("x轴")
    private String xAxis;

    @ApiModelProperty("y轴")
    private String yAxis;

    @ApiModelProperty("序号")
    private Integer serialNumber;
}

