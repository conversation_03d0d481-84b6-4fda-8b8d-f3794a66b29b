package com.rs.module.ihc.handler.pm;

import com.rs.module.ihc.controller.admin.pm.vo.MedicineCodeImportVO;
import com.rs.module.ihc.controller.admin.pm.vo.ValidationResult;
import com.rs.module.ihc.service.pm.IhcPmMedicineCodeBuilder;
import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName IhcPmMedicineCodeValidate
 * @Description 校验输入是否合法
 * <AUTHOR>
 * @Date 2025/3/18 15:30
 * @Version 1.0
 */
public class IhcPmMedicineCodeValidate extends IhcPmMedicineCodeBuilder<MedicineCodeImportVO> {
    @Override
    public ValidationResult importExcel(MedicineCodeImportVO toValidate) {
        if (StringUtils.isEmpty(toValidate.getMedicineStandardCode())) {
            throw new RuntimeException("药品编码不能为空");
        }
        if (StringUtils.isEmpty(toValidate.getApprovalNum())) {
            throw new RuntimeException("批准文号不能为空");
        }
        return checkNext(toValidate);
    }
}
