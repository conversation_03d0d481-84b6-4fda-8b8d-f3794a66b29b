package com.rs.module.ihc.enums;

import com.baomidou.mybatisplus.annotation.DbType;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> LiuWenxing
 * @create 2024/8/13 9:48
 */
@Getter
@AllArgsConstructor
public enum DataBaseEnum {

    pg(1, "PostgreSQL", "pg", DbType.POSTGRE_SQL),
    kingbase(2, "KingbaseES", "kb", DbType.KINGBASE_ES),
    dm(3, "DM DBMS", "dm", DbType.DM),
    ;

    public final int databaseType;
    public final String databaseProductName;
    public final String databaseId;
    public final DbType dbType;


    public static DataBaseEnum getByDatabaseType(int type) {
        for (DataBaseEnum anEnum : DataBaseEnum.values()) {
            if (Objects.equals(type, anEnum.databaseType)) {
                return anEnum;
            }
        }
        return null;
    }
}
