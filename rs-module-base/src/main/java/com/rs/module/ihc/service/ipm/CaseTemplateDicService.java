package com.rs.module.ihc.service.ipm;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.CaseTemplateDicDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 所内就医-病例模板表-字典 Service 接口
 *
 * <AUTHOR>
 */
public interface CaseTemplateDicService extends IBaseService<CaseTemplateDicDO>{

    /**
     * 创建所内就医-病例模板表-字典
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCaseTemplateDic(@Valid CaseTemplateDicSaveReqVO createReqVO);

    /**
     * 更新所内就医-病例模板表-字典
     *
     * @param updateReqVO 更新信息
     */
    void updateCaseTemplateDic(@Valid CaseTemplateDicSaveReqVO updateReqVO);

    /**
     * 删除所内就医-病例模板表-字典
     *
     * @param id 编号
     */
    void deleteCaseTemplateDic(String id);

    /**
     * 获得所内就医-病例模板表-字典
     *
     * @param id 编号
     * @return 所内就医-病例模板表-字典
     */
    CaseTemplateDicDO getCaseTemplateDic(String id);

    /**
    * 获得所内就医-病例模板表-字典分页
    *
    * @param pageReqVO 分页查询
    * @return 所内就医-病例模板表-字典分页
    */
    PageResult<CaseTemplateDicDO> getCaseTemplateDicPage(CaseTemplateDicPageReqVO pageReqVO);

    /**
    * 获得所内就医-病例模板表-字典列表
    *
    * @param listReqVO 查询条件
    * @return 所内就医-病例模板表-字典列表
    */
    List<CaseTemplateDicDO> getCaseTemplateDicList(CaseTemplateDicListReqVO listReqVO);


}
