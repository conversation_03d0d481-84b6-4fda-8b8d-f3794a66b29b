package com.rs.module.ihc.enums;

/**
 * @ClassName AdviceType
 * <AUTHOR>
 * @Date 2025/3/26 11:02
 * @Version 1.0
 */
public enum SourceType {
    //1所内就医预约2手动新增
    IHC_IPM_SOURCE_TYPE_1("所内就医预约", "1"),
    IHC_IPM_SOURCE_TYPE_2("手动新增", "2");
    private String name;
    private String value;
    SourceType(String name, String value) {
        this.name = name;
        this.value = value;
    }
    public String getName() {
        return name;
    }
    public String getValue() {
        return value;
    }
}
