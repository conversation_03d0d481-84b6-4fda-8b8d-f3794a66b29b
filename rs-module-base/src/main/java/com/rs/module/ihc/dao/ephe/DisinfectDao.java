package com.rs.module.ihc.dao.ephe;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.ephe.DisinfectDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.ephe.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 卫生防疫与健康教育-卫生消毒登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface DisinfectDao extends IBaseDao<DisinfectDO> {


    default PageResult<DisinfectDO> selectPage(DisinfectPageReqVO reqVO) {
        Page<DisinfectDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<DisinfectDO> wrapper = new LambdaQueryWrapperX<DisinfectDO>()
            .eqIfPresent(DisinfectDO::getXjmzxrszcs, reqVO.getXjmzxrszcs())
            .eqIfPresent(DisinfectDO::getMswtrszcs, reqVO.getMswtrszcs())
            .eqIfPresent(DisinfectDO::getMzlsbrcs, reqVO.getMzlsbrcs())
            .eqIfPresent(DisinfectDO::getMjdcxbrcs, reqVO.getMjdcxbrcs())
            .eqIfPresent(DisinfectDO::getMzxdcs, reqVO.getMzxdcs())
            .eqIfPresent(DisinfectDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(DisinfectDO::getOperatePolice, reqVO.getOperatePolice())
            .betweenIfPresent(DisinfectDO::getOperateTime, reqVO.getOperateTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(DisinfectDO::getAddTime);
        }
        Page<DisinfectDO> disinfectPage = selectPage(page, wrapper);
        return new PageResult<>(disinfectPage.getRecords(), disinfectPage.getTotal());
    }
    default List<DisinfectDO> selectList(DisinfectListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DisinfectDO>()
            .eqIfPresent(DisinfectDO::getXjmzxrszcs, reqVO.getXjmzxrszcs())
            .eqIfPresent(DisinfectDO::getMswtrszcs, reqVO.getMswtrszcs())
            .eqIfPresent(DisinfectDO::getMzlsbrcs, reqVO.getMzlsbrcs())
            .eqIfPresent(DisinfectDO::getMjdcxbrcs, reqVO.getMjdcxbrcs())
            .eqIfPresent(DisinfectDO::getMzxdcs, reqVO.getMzxdcs())
            .eqIfPresent(DisinfectDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(DisinfectDO::getOperatePolice, reqVO.getOperatePolice())
            .betweenIfPresent(DisinfectDO::getOperateTime, reqVO.getOperateTime())
        .orderByDesc(DisinfectDO::getAddTime));    }


    }
