package com.rs.module.ihc.dao.ipm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.ipm.PrescribeOutDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 所内就医-处方药品出库记录 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PrescribeOutDao extends IBaseDao<PrescribeOutDO> {


    default PageResult<PrescribeOutDO> selectPage(PrescribeOutPageReqVO reqVO) {
        Page<PrescribeOutDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrescribeOutDO> wrapper = new LambdaQueryWrapperX<PrescribeOutDO>()
            .likeIfPresent(PrescribeOutDO::getAddUserName, reqVO.getAddUserName())
            .likeIfPresent(PrescribeOutDO::getUpdateUserName, reqVO.getUpdateUserName())
            .eqIfPresent(PrescribeOutDO::getPrescribeId, reqVO.getPrescribeId())
            .eqIfPresent(PrescribeOutDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PrescribeOutDO::getDoctorAdvice, reqVO.getDoctorAdvice())
            .eqIfPresent(PrescribeOutDO::getPrescribeDescribe, reqVO.getPrescribeDescribe())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PrescribeOutDO::getAddTime);
        }
        Page<PrescribeOutDO> prescribeOutPage = selectPage(page, wrapper);
        return new PageResult<>(prescribeOutPage.getRecords(), prescribeOutPage.getTotal());
    }
    default List<PrescribeOutDO> selectList(PrescribeOutListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrescribeOutDO>()
            .likeIfPresent(PrescribeOutDO::getAddUserName, reqVO.getAddUserName())
            .likeIfPresent(PrescribeOutDO::getUpdateUserName, reqVO.getUpdateUserName())
            .eqIfPresent(PrescribeOutDO::getPrescribeId, reqVO.getPrescribeId())
            .eqIfPresent(PrescribeOutDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PrescribeOutDO::getDoctorAdvice, reqVO.getDoctorAdvice())
            .eqIfPresent(PrescribeOutDO::getPrescribeDescribe, reqVO.getPrescribeDescribe())
        .orderByDesc(PrescribeOutDO::getAddTime));
    }

    PrescribeOutRespVO getPrescribeOutById(String id);

}
