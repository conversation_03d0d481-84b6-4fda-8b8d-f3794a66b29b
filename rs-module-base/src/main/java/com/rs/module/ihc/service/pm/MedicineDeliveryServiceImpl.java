package com.rs.module.ihc.service.pm;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.MedicineDeliveryDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.pm.MedicineDeliveryDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 药房管理-顾送药品信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MedicineDeliveryServiceImpl extends BaseServiceImpl<MedicineDeliveryDao, MedicineDeliveryDO> implements MedicineDeliveryService {

    @Resource
    private MedicineDeliveryDao medicineDeliveryDao;

    @Override
    public String createMedicineDelivery(MedicineDeliverySaveReqVO createReqVO) {
        // 插入
        MedicineDeliveryDO medicineDelivery = BeanUtils.toBean(createReqVO, MedicineDeliveryDO.class);
        medicineDeliveryDao.insert(medicineDelivery);
        // 返回
        return medicineDelivery.getId();
    }

    @Override
    public void updateMedicineDelivery(MedicineDeliverySaveReqVO updateReqVO) {
        // 校验存在
        validateMedicineDeliveryExists(updateReqVO.getId());
        // 更新
        MedicineDeliveryDO updateObj = BeanUtils.toBean(updateReqVO, MedicineDeliveryDO.class);
        medicineDeliveryDao.updateById(updateObj);
    }

    @Override
    public void deleteMedicineDelivery(String id) {
        // 校验存在
        validateMedicineDeliveryExists(id);
        // 删除
        medicineDeliveryDao.deleteById(id);
    }

    private void validateMedicineDeliveryExists(String id) {
        if (medicineDeliveryDao.selectById(id) == null) {
            throw new ServerException("药房管理-顾送药品信息数据不存在");
        }
    }

    @Override
    public MedicineDeliveryDO getMedicineDelivery(String id) {
        return medicineDeliveryDao.selectById(id);
    }

    @Override
    public PageResult<MedicineDeliveryDO> getMedicineDeliveryPage(MedicineDeliveryPageReqVO pageReqVO) {
        return medicineDeliveryDao.selectPage(pageReqVO);
    }

    @Override
    public List<MedicineDeliveryDO> getMedicineDeliveryList(MedicineDeliveryListReqVO listReqVO) {
        return medicineDeliveryDao.selectList(listReqVO);
    }


}
