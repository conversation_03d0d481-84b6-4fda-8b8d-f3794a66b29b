package com.rs.module.ihc.controller.admin.pm.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 药品管理-药品采购申请-药品信息关联 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 药品管理-药品采购申请-药品信息关联新增/修改 Request VO")
@TableName("ihc_pm_purchase_apply_rel")
@KeySequence("ihc_pm_purchase_apply_rel_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseApplyRelExportVO {

    @ExcelProperty("批准文号")
    @ColumnWidth(30)
    @ApiModelProperty("批准文号")
    private String approvalNum;
    @ApiModelProperty("药品名称")
    @ExcelProperty("药品名称")
    @ColumnWidth(30)
    private String medicineName;
    @ApiModelProperty("剂型（字典:剂型）")
    @ExcelProperty("剂型")
    @ColumnWidth(10)
    private String dosageForm;
    @ApiModelProperty("规格")
    @ExcelProperty("规格")
    @ColumnWidth(30)
    private String specs;
    @ApiModelProperty("上市许可持有人")
    @ExcelProperty("上市许可持有人")
    @ColumnWidth(20)
    private String marketApprovalHolder;
    @ApiModelProperty("生产单位")
    @ExcelProperty("生产单位")
    @ColumnWidth(40)
    private String productUnit;
    @ApiModelProperty("药品本位码")
    @ExcelProperty("药品本位码")
    @ColumnWidth(20)
    private String medicineStandardCode;
    @ApiModelProperty("采购数量")
    @ExcelProperty("采购数量")
    @ColumnWidth(15)
    private Integer num;
    @ApiModelProperty("单价")
    @ExcelProperty("单价")
    @ColumnWidth(10)
    private Float unitPrice;
    @ApiModelProperty("计量单位")
    @ExcelProperty("计量单位")
    @ColumnWidth(15)
    private String minMeasurementUnit;

}
