package com.rs.module.ihc.entity.bd;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 精神病异常管理 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_bd_psychiatric_mgr")
@KeySequence("ihc_bd_psychiatric_mgr_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ihc_bd_psychiatric_mgr")
public class PsychiatricMgrDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 会诊初步诊断 字典ZD_JSB_HZCBZD
     */
    private String cnsltPreDiag;
    /**
     * 治疗后是否好转 1是0否
     */
    private String treatmentOutcome;
    /**
     * 列管等级  字典：ZD_JSB_LGDJ
     */
    private String managementLevel;
    /**
     * 列控时间
     */
    private Date controlTime;
    /**
     * 列控原因
     */
    private String controlReason;
    /**
     * 登记民警身份证号
     */
    private String operatePoliceSfzh;
    /**
     * 登记民警
     */
    private String operatePolice;
    /**
     * 登记时间
     */
    private Date operateTime;

    /**
     * 列管状态 1已列管 0 未列管
     */
    private String status;


    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;

}
