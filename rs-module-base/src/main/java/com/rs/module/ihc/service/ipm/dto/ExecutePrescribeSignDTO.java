package com.rs.module.ihc.service.ipm.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class ExecutePrescribeSignDTO {

    /**
     * 处方/医嘱执行id
     */
    @NotNull(message = "处方/医嘱执行id不能为空")
    @ApiModelProperty(value = "处方/医嘱执行id", required = true)
    private String id;

    /**
     * 签名图片地址
     */
    @NotBlank(message = "签名图片地址不能为空")
    @ApiModelProperty(value = "签名图片地址", required = true)
    private String signPicUrl;

    /**
     * 签名图片地址
     */
    @ApiModelProperty(value = "服药视频url", required = false)
    private String doseVideoUrl;

}
