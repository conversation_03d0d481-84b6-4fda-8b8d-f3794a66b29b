package com.rs.module.ihc.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.pm.MedicineDeliveryInDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 药房管理-顾送药品入库批次 Dao
*
* <AUTHOR>
*/
@Mapper
public interface MedicineDeliveryInDao extends IBaseDao<MedicineDeliveryInDO> {


    default PageResult<MedicineDeliveryInDO> selectPage(MedicineDeliveryInPageReqVO reqVO) {
        Page<MedicineDeliveryInDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<MedicineDeliveryInDO> medicineDeliveryInPage = selectPage(page, new LambdaQueryWrapperX<MedicineDeliveryInDO>()
            .eqIfPresent(MedicineDeliveryInDO::getDrugId, reqVO.getDrugId())
            .eqIfPresent(MedicineDeliveryInDO::getInNum, reqVO.getInNum())
            .eqIfPresent(MedicineDeliveryInDO::getBatchCode, reqVO.getBatchCode())
            .eqIfPresent(MedicineDeliveryInDO::getSupplierId, reqVO.getSupplierId())
            .likeIfPresent(MedicineDeliveryInDO::getSupplierName, reqVO.getSupplierName())
            .eqIfPresent(MedicineDeliveryInDO::getVoucherCode, reqVO.getVoucherCode())
            .eqIfPresent(MedicineDeliveryInDO::getPurchasePrice, reqVO.getPurchasePrice())
            .eqIfPresent(MedicineDeliveryInDO::getWholesalePrice, reqVO.getWholesalePrice())
            .eqIfPresent(MedicineDeliveryInDO::getTransferPrice, reqVO.getTransferPrice())
            .eqIfPresent(MedicineDeliveryInDO::getRetailPrice, reqVO.getRetailPrice())
            .betweenIfPresent(MedicineDeliveryInDO::getInDate, reqVO.getInDate())
            .betweenIfPresent(MedicineDeliveryInDO::getExpireDate, reqVO.getExpireDate())
            .eqIfPresent(MedicineDeliveryInDO::getInStorageMethod, reqVO.getInStorageMethod())
            .eqIfPresent(MedicineDeliveryInDO::getTransferMethod, reqVO.getTransferMethod())
            .eqIfPresent(MedicineDeliveryInDO::getMedicinePlace, reqVO.getMedicinePlace())
            .eqIfPresent(MedicineDeliveryInDO::getInventoryNum, reqVO.getInventoryNum())
            .eqIfPresent(MedicineDeliveryInDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
            .eqIfPresent(MedicineDeliveryInDO::getInStatus, reqVO.getInStatus())
            .eqIfPresent(MedicineDeliveryInDO::getWorkflowEnd, reqVO.getWorkflowEnd())
            .orderByDesc(MedicineDeliveryInDO::getId));
            return new PageResult<>(medicineDeliveryInPage.getRecords(), medicineDeliveryInPage.getTotal());
    }
    default List<MedicineDeliveryInDO> selectList(MedicineDeliveryInListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MedicineDeliveryInDO>()
            .eqIfPresent(MedicineDeliveryInDO::getDrugId, reqVO.getDrugId())
            .eqIfPresent(MedicineDeliveryInDO::getInNum, reqVO.getInNum())
            .eqIfPresent(MedicineDeliveryInDO::getBatchCode, reqVO.getBatchCode())
            .eqIfPresent(MedicineDeliveryInDO::getSupplierId, reqVO.getSupplierId())
            .likeIfPresent(MedicineDeliveryInDO::getSupplierName, reqVO.getSupplierName())
            .eqIfPresent(MedicineDeliveryInDO::getVoucherCode, reqVO.getVoucherCode())
            .eqIfPresent(MedicineDeliveryInDO::getPurchasePrice, reqVO.getPurchasePrice())
            .eqIfPresent(MedicineDeliveryInDO::getWholesalePrice, reqVO.getWholesalePrice())
            .eqIfPresent(MedicineDeliveryInDO::getTransferPrice, reqVO.getTransferPrice())
            .eqIfPresent(MedicineDeliveryInDO::getRetailPrice, reqVO.getRetailPrice())
            .betweenIfPresent(MedicineDeliveryInDO::getInDate, reqVO.getInDate())
            .betweenIfPresent(MedicineDeliveryInDO::getExpireDate, reqVO.getExpireDate())
            .eqIfPresent(MedicineDeliveryInDO::getInStorageMethod, reqVO.getInStorageMethod())
            .eqIfPresent(MedicineDeliveryInDO::getTransferMethod, reqVO.getTransferMethod())
            .eqIfPresent(MedicineDeliveryInDO::getMedicinePlace, reqVO.getMedicinePlace())
            .eqIfPresent(MedicineDeliveryInDO::getInventoryNum, reqVO.getInventoryNum())
            .eqIfPresent(MedicineDeliveryInDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
            .eqIfPresent(MedicineDeliveryInDO::getInStatus, reqVO.getInStatus())
            .eqIfPresent(MedicineDeliveryInDO::getWorkflowEnd, reqVO.getWorkflowEnd())
        .orderByDesc(MedicineDeliveryInDO::getId));    }


    }
