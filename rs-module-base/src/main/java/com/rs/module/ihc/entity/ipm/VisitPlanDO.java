package com.rs.module.ihc.entity.ipm;

import lombok.*;
import java.util.*;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 所内就医-现场巡诊计划 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_ipm_visit_plan")
@KeySequence("ihc_ipm_visit_plan_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VisitPlanDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 来源（2:手动新增，3:加戴械具）
     */
    private String source;
    /**
     * 来源id，存第三方来源的主键
     */
    private String sourceId;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 在押人员编号
     */
    private String rybh;
    /**
     * 在押人员名称
     */
    private String ryxm;
    /**
     * 原因
     */
    private String reason;
    /**
     * 开始日期
     */
    private Date startDate;
    /**
     * 结束日期
     */
    private Date endDate;
    /**
     * 巡诊频率
     */
    private String frequency;
    /**
     * 监所id
     */
    private String prisonId;
    /**
     * 状态（ 0：停用，1：启用）
     */
    private String status;
    /**
     * 最后一次创建巡诊时间
     */
    private Date lastVisitCreateTime;

}
