package com.rs.module.ihc.controller.admin.injury.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 医疗子系统-伤亡登记分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InjuryRegistrationPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("外伤情况 字典ZD_WSBQK")
    private String injuryDetails;

    @ApiModelProperty("外伤情况记录方式 字典ZD_WSQKJLFS")
    private String recordingMethod;

    @ApiModelProperty("致伤日期")
    private Date[] injuryDate;

    @ApiModelProperty("致伤原因")
    private String injuryCause;

    @ApiModelProperty("登记民警身份证号")
    private String operatePoliceSfzh;

    @ApiModelProperty("登记民警")
    private String operatePolice;

    @ApiModelProperty("登记时间")
    private Date[] operateTime;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
