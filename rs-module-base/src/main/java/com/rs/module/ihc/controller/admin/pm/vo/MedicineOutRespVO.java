package com.rs.module.ihc.controller.admin.pm.vo;

import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 药房管理-药品出库 Response VO")
@Data
public class MedicineOutRespVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("药品id，对应ichs_medicine.id")
    private String medicineId;
    @ApiModelProperty("入库批次id，对应ichs_medicine_in.id")
    private String medicineInId;
    @ApiModelProperty("出库原因")
    private String outStorageReason;
    @ApiModelProperty("出库数量")
    private BigDecimal outNum;
    @ApiModelProperty("取药单位")
    private String dispensaryUnit;
    @ApiModelProperty("取药时间")
    private Date dispensaryTime;
    @ApiModelProperty("取药人，对应permission_user.userid")
    private String userid;
    @ApiModelProperty("取药人名称")
    private String userName;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("批次库存数量 最小单位")
    private BigDecimal inventoryNum;
    @ApiModelProperty("当前药品总库存数量 最小单位")
    private BigDecimal totalInventoryNum;
    @ApiModelProperty("出库状态")
    private String outStatus;
    @ApiModelProperty("流程是否结束 0=否 1=是")
    private Integer workflowEnd;
    @ApiModelProperty("药品入库信息")
    private MedicineInRespVO medicineIn;
    @ApiModelProperty("药品信息")
    private MedicineRespVO medicine;
}
