package com.rs.module.ihc.controller.admin.hc.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 五项体检-数据补录
 * <AUTHOR>
 * @date 2025/7/14 15:45
 */
@Data
public class HealthCheckupAdditionalRecordVO implements Serializable {

    private static final long serialVersionUID = 6885423911901452774L;

    @ApiModelProperty("被监管人员编号")
    private String supervisedUserCode;

    @ApiModelProperty("体检时间")
    private Date checkupTime;
    /**
     * 既往病史
     **/
    @ApiModelProperty("既往病史")
    private String medicalHistory;
    /**
     * 药物过敏史
     **/
    @ApiModelProperty("药物过敏史")
    private String drugAllergy;
    /**
     * 家族病史
     **/
    @ApiModelProperty("家族病史")
    private String familyMedicalHistory;
    /**
     * 目前身体状况
     **/
    @ApiModelProperty("目前身体状况")
    private String healthStatus;
    /**
     * 血压(高压)
     **/
    @ApiModelProperty("血压(高压)")
    private Float bloodPressureHeight;
    /**
     * 血压(低压)
     **/
    @ApiModelProperty("血压(低压)")
    private Float bloodPressureLow;
    /**
     * 脉搏
     **/
    @ApiModelProperty("脉搏")
    private Float pulse;
    /**
     * 常规检查
     **/
    @ApiModelProperty("常规检查")
    private String routineExamination;
    /**
     * 血常规
     **/
    @ApiModelProperty("血常规")
    private String bloodRoutineExamination;
    /**
     * B超
     **/
    @ApiModelProperty("B超")
    private String bUltrasonics;
    /**
     * 肝、胆、脾、胰
     **/
    @ApiModelProperty("肝、胆、脾、胰")
    private String fiveInternalOrgans;
    /**
     * 双肾、输尿管、膀胱
     **/
    @ApiModelProperty("双肾、输尿管、膀胱")
    private String urinarySystem;
    /**
     * 心电图
     **/
    @ApiModelProperty("心电图")
    private String electrocardiogram;
    /**
     * 常规心电图
     **/
    @ApiModelProperty("常规心电图")
    private String routineElectrocardiogram;
    /**
     * X光
     **/
    @ApiModelProperty("X光")
    private String xRay;
    /**
     * 胸部
     **/
    @ApiModelProperty("胸部")
    private String chest;
    /**
     * 检查结论
     **/
    @ApiModelProperty("检查结论")
    private String checkConclusion;

    @ApiModelProperty("附件信息")
    private List<HealthCheckupFileSaveReqVO> fileList;

}
