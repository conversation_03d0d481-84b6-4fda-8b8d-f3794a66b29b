package com.rs.module.ihc.controller.admin.ipm.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.List;

@Data
public class PrescribeMedicineOutReqVO {

    /**
     * 处方/医嘱id
     */
    @NotNull(message = "处方/医嘱id不能为空")
    @ApiModelProperty(value = "处方/医嘱id", required = true)
    private String prescribeId;

    /**
     * 药品出库列表
     */
    @NotEmpty(message = "药品出库列表不能为空")
    @ApiModelProperty(value = "药品出库列表", required = true)
    private List<PrescribeMedicineOutInfo> prescribeMedicineOutInfoList;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PrescribeMedicineOutInfo {

        /**
         * 药品id
         */
        @NotNull(message = "药品id不能为空")
        @ApiModelProperty(value = "药品id", required = true)
        private String medicineId;

        /**
         * 处方药品出库数量 最小单位
         */
        @Positive(message = "药品出库数量不能小于0")
        @NotNull(message = "药品出库数量不能为空")
        @ApiModelProperty(value = "处方药品出库数量 最小单位", required = true)
        private BigDecimal outNum;
    }
}
