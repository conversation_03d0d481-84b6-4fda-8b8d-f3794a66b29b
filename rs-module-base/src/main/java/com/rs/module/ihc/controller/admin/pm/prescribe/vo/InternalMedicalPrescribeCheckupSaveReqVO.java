package com.rs.module.ihc.controller.admin.pm.prescribe.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 所内就医-处方-体检登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class InternalMedicalPrescribeCheckupSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键，自增")
    private String id;

    @ApiModelProperty("处方id，对应ihcs_internal_medical_prescribe.id")
    private String prescribeId;

    @ApiModelProperty("被监管人员编号")
    private String supervisedUserCode;

    @ApiModelProperty("血压(高压)")
    private BigDecimal bloodPressureHeight;

    @ApiModelProperty("血压(低压)")
    private BigDecimal bloodPressureLow;

    @ApiModelProperty("体温")
    private BigDecimal temperature;

    @ApiModelProperty("心率")
    private BigDecimal heartRate;

    @ApiModelProperty("呼吸频次")
    private BigDecimal breatheFrequency;

    @ApiModelProperty("脉搏")
    private BigDecimal pulse;

    @ApiModelProperty("血氧")
    private BigDecimal bloodOxygen;

    @ApiModelProperty("血糖")
    private BigDecimal bloodSugar;

    @ApiModelProperty("胰岛素")
    private BigDecimal insulin;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("注射时间")
    private Date injectionTime;

}
