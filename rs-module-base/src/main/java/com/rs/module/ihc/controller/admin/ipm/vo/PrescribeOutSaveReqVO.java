package com.rs.module.ihc.controller.admin.ipm.vo;

import com.rs.module.ihc.entity.ipm.PrescribeOutMedicineDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@ApiModel(description = "管理后台 - 所内就医-处方药品出库记录新增/修改 Request VO")
@Data
public class PrescribeOutSaveReqVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("添加人姓名")
    private String addUserName;

    @ApiModelProperty("更新人姓名")
    private String updateUserName;

    @ApiModelProperty("处方id，对应ihc_ipm_prescribe.id")
    @NotEmpty(message = "处方id，对应ihc_ipm_prescribe.id不能为空")
    private String prescribeId;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("医嘱")
    private String doctorAdvice;

    @ApiModelProperty("处方说明")
    private String prescribeDescribe;

    @ApiModelProperty("所内就医-处方药品出库记录关联药品列表")
    private List<PrescribeOutMedicineDO> prescribeOutMedicines;

}
