package com.rs.module.ihc.dao.pm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.rs.module.ihc.controller.admin.pm.vo.PurchaseApplyListReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.PurchaseApplyPageReqVO;
import com.rs.module.ihc.entity.pm.PurchaseApplyDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 药品管理-药品采购申请 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface PurchaseApplyDao extends IBaseDao<PurchaseApplyDO> {


    default PageResult<PurchaseApplyDO> selectPage(PurchaseApplyPageReqVO reqVO) {
        Page<PurchaseApplyDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<PurchaseApplyDO> purchaseApplyPage = selectPage(page, new LambdaQueryWrapperX<PurchaseApplyDO>()
                .eqIfPresent(PurchaseApplyDO::getIsDel, reqVO.getIsDel())
                .betweenIfPresent(PurchaseApplyDO::getAddTime, reqVO.getAddTime())
                .eqIfPresent(PurchaseApplyDO::getAddUser, reqVO.getAddUser())
                .eqIfPresent(PurchaseApplyDO::getUpdateUser, reqVO.getUpdateUser())
                .eqIfPresent(PurchaseApplyDO::getProCode, reqVO.getProCode())
                .likeIfPresent(PurchaseApplyDO::getProName, reqVO.getProName())
                .eqIfPresent(PurchaseApplyDO::getCityCode, reqVO.getCityCode())
                .likeIfPresent(PurchaseApplyDO::getCityName, reqVO.getCityName())
                .eqIfPresent(PurchaseApplyDO::getRegCode, reqVO.getRegCode())
                .likeIfPresent(PurchaseApplyDO::getRegName, reqVO.getRegName())
                .eqIfPresent(PurchaseApplyDO::getOrgCode, reqVO.getOrgCode())
                .likeIfPresent(PurchaseApplyDO::getOrgName, reqVO.getOrgName())
                .eqIfPresent(PurchaseApplyDO::getApplyNum, reqVO.getApplyNum())
                .eqIfPresent(PurchaseApplyDO::getMedicineSource, reqVO.getMedicineSource())
                .eqIfPresent(PurchaseApplyDO::getDoctorOpinion, reqVO.getDoctorOpinion())
                .eqIfPresent(PurchaseApplyDO::getChargeOpinion, reqVO.getChargeOpinion())
                .eqIfPresent(PurchaseApplyDO::getLeaderOpinion, reqVO.getLeaderOpinion())
                .eqIfPresent(PurchaseApplyDO::getApproveOpinion, reqVO.getApproveOpinion())
                .eqIfPresent(PurchaseApplyDO::getPrisonId, reqVO.getPrisonId())
                .orderByDesc(PurchaseApplyDO::getId));
        return new PageResult<>(purchaseApplyPage.getRecords(), purchaseApplyPage.getTotal());
    }

    default List<PurchaseApplyDO> selectList(PurchaseApplyListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PurchaseApplyDO>()
                .eqIfPresent(PurchaseApplyDO::getIsDel, reqVO.getIsDel())
                .betweenIfPresent(PurchaseApplyDO::getAddTime, reqVO.getAddTime())
                .eqIfPresent(PurchaseApplyDO::getAddUser, reqVO.getAddUser())
                .eqIfPresent(PurchaseApplyDO::getUpdateUser, reqVO.getUpdateUser())
                .eqIfPresent(PurchaseApplyDO::getProCode, reqVO.getProCode())
                .likeIfPresent(PurchaseApplyDO::getProName, reqVO.getProName())
                .eqIfPresent(PurchaseApplyDO::getCityCode, reqVO.getCityCode())
                .likeIfPresent(PurchaseApplyDO::getCityName, reqVO.getCityName())
                .eqIfPresent(PurchaseApplyDO::getRegCode, reqVO.getRegCode())
                .likeIfPresent(PurchaseApplyDO::getRegName, reqVO.getRegName())
                .eqIfPresent(PurchaseApplyDO::getOrgCode, reqVO.getOrgCode())
                .likeIfPresent(PurchaseApplyDO::getOrgName, reqVO.getOrgName())
                .eqIfPresent(PurchaseApplyDO::getApplyNum, reqVO.getApplyNum())
                .eqIfPresent(PurchaseApplyDO::getMedicineSource, reqVO.getMedicineSource())
                .eqIfPresent(PurchaseApplyDO::getDoctorOpinion, reqVO.getDoctorOpinion())
                .eqIfPresent(PurchaseApplyDO::getChargeOpinion, reqVO.getChargeOpinion())
                .eqIfPresent(PurchaseApplyDO::getLeaderOpinion, reqVO.getLeaderOpinion())
                .eqIfPresent(PurchaseApplyDO::getApproveOpinion, reqVO.getApproveOpinion())
                .eqIfPresent(PurchaseApplyDO::getPrisonId, reqVO.getPrisonId())
                .orderByDesc(PurchaseApplyDO::getId));
    }

    default PageResult<PurchaseApplyDO> selectByProCode(PurchaseApplyPageReqVO reqVO) {

        MPJLambdaWrapperX<PurchaseApplyDO> query = new MPJLambdaWrapperX<>();
        return selectJoinPage(reqVO, PurchaseApplyDO.class, query);
    }
}

