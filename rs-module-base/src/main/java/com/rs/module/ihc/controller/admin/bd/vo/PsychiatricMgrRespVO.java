package com.rs.module.ihc.controller.admin.bd.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 精神病异常管理 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PsychiatricMgrRespVO extends BaseVO implements TransPojo{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("会诊初步诊断 字典ZD_JSB_HZCBZD")
    @Trans(type = TransType.DICTIONARY,key = "ZD_JSB_HZCBZD")
    private String cnsltPreDiag;

    @ApiModelProperty("治疗后是否好转 1是0否")
    private String treatmentOutcome;

    @ApiModelProperty("列管等级  字典：ZD_JSB_LGDJ")
    @Trans(type = TransType.DICTIONARY,key = "ZD_JSB_LGDJ")
    private String managementLevel;

    @ApiModelProperty("列控时间")
    private Date controlTime;

    @ApiModelProperty("列控原因")
    private String controlReason;

    @ApiModelProperty("登记民警身份证号")
    private String operatePoliceSfzh;

    @ApiModelProperty("登记民警")
    private String operatePolice;

    @ApiModelProperty("登记时间")
    private Date operateTime;

    @ApiModelProperty("列管状态 1 已列管 0已解除")
    @Trans(type = TransType.DICTIONARY,key = "ZD_JSB_LGZT")
    private String status;

    @ApiModelProperty("被监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

}
