package com.rs.module.ihc.entity.pm.prescribe;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 所内就医-处方-体检登记 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_pm_internal_medical_prescribe_checkup")
@KeySequence("ihc_pm_internal_medical_prescribe_checkup_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ihc_pm_internal_medical_prescribe_checkup")
public class InternalMedicalPrescribeCheckupDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键，自增
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 处方id，对应ihcs_internal_medical_prescribe.id
     */
    private String prescribeId;
    /**
     * 被监管人员编号
     */
    private String supervisedUserCode;
    /**
     * 血压(高压)
     */
    private BigDecimal bloodPressureHeight;
    /**
     * 血压(低压)
     */
    private BigDecimal bloodPressureLow;
    /**
     * 体温
     */
    private BigDecimal temperature;
    /**
     * 心率
     */
    private BigDecimal heartRate;
    /**
     * 呼吸频次
     */
    private BigDecimal breatheFrequency;
    /**
     * 脉搏
     */
    private BigDecimal pulse;
    /**
     * 血氧
     */
    private BigDecimal bloodOxygen;
    /**
     * 血糖
     */
    private BigDecimal bloodSugar;
    /**
     * 胰岛素
     */
    private BigDecimal insulin;
    /**
     * 备注
     */
    private String remark;
    /**
     * 注射时间
     */
    private Date injectionTime;


}
