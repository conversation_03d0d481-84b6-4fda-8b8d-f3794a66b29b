package com.rs.module.ihc.controller.admin.ipm.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateMedicineInInventoryNumBO {
    /**
     * 批次id
     */
    private String id;

    /**
     * 目标库存数量
     */
    private BigDecimal targetInventoryNum;

    /**
     * 源库存数量
     */
    private BigDecimal sourceInventoryNum;
}
