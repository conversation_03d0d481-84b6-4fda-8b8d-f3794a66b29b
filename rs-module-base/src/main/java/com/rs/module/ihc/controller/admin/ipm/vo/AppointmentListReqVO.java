package com.rs.module.ihc.controller.admin.ipm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "管理后台 - 所内就医-预约登记列表 Request VO")
@Data
public class AppointmentListReqVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("预约编号，编号规则：8位日期+4位序列号，如：************")
    private String appointmentNum;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("在押人员编号")
    private String rybh;

    @ApiModelProperty("在押人员名称")
    private String ryxm;

    @ApiModelProperty("是否重病（ 0：否 1：是）")
    private Short sickType;

    @ApiModelProperty("报病时间")
    private Date[] diseaseTime;

    @ApiModelProperty("报病原因")
    private String diseaseReason;

    @ApiModelProperty("初步诊断")
    private String primaryDiagnosis;

    @ApiModelProperty("病情等级（字典值：病情等级 ）")
    private String diseaseLevel;

    @ApiModelProperty("处理方式/预约审核结果 字典值")
    private String processMethod;

    @ApiModelProperty("处理人证件号码")
    private String processUserid;

    @ApiModelProperty("处理人名称")
    private String processUserName;

    @ApiModelProperty("处理时间")
    private Date[] processTime;

    @ApiModelProperty("远程问诊状态（ 0：待处理 1：已处理）")
    private Short remoteDiagnoseStatus;

    @ApiModelProperty("监所id")
    private String prisonId;

}
