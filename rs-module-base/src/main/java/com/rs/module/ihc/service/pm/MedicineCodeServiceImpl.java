package com.rs.module.ihc.service.pm;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.MedicineCodeDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.pm.MedicineCodeDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 药房管理-国家药品编码本位码信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MedicineCodeServiceImpl extends BaseServiceImpl<MedicineCodeDao, MedicineCodeDO> implements MedicineCodeService {

    @Resource
    private MedicineCodeDao medicineCodeDao;

    @Override
    public String createMedicineCode(MedicineCodeSaveReqVO createReqVO) {
        // 插入
        MedicineCodeDO medicineCode = BeanUtils.toBean(createReqVO, MedicineCodeDO.class);
        medicineCodeDao.insert(medicineCode);
        // 返回
        return medicineCode.getId();
    }

    @Override
    public void updateMedicineCode(MedicineCodeSaveReqVO updateReqVO) {
        // 校验存在
        validateMedicineCodeExists(updateReqVO.getId());
        // 更新
        MedicineCodeDO updateObj = BeanUtils.toBean(updateReqVO, MedicineCodeDO.class);
        medicineCodeDao.updateById(updateObj);
    }

    @Override
    public void deleteMedicineCode(String id) {
        // 校验存在
        validateMedicineCodeExists(id);
        // 删除
        medicineCodeDao.deleteById(id);
    }

    private void validateMedicineCodeExists(String id) {
        if (medicineCodeDao.selectById(id) == null) {
            throw new ServerException("药房管理-国家药品编码本位码信息数据不存在");
        }
    }

    @Override
    public MedicineCodeDO getMedicineCode(String id) {
        return medicineCodeDao.selectById(id);
    }

    @Override
    public PageResult<MedicineCodeDO> getMedicineCodePage(MedicineCodePageReqVO pageReqVO) {
        return medicineCodeDao.selectPage(pageReqVO);
    }

    @Override
    public List<MedicineCodeDO> getMedicineCodeList(MedicineCodeListReqVO listReqVO) {
        return medicineCodeDao.selectList(listReqVO);
    }


}
