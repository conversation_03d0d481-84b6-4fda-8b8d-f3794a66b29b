package com.rs.module.ihc.controller.admin.ipm.vo;

import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "签署风险告知书O")
@Data
public class SignRiskDisclosurePdfVO  implements TransPojo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("执行id")
    private String executionId;

    @ApiModelProperty("指纹URL")
    private String fingerprintUrl;

    @ApiModelProperty("患者意见")
    private String patientOpinion;

    @ApiModelProperty("签名图片url")
    private String signPicUrl;

}
