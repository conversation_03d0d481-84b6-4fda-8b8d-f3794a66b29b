package com.rs.module.ihc.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.pm.MedicineDeliveryLossDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 药房管理-顾送药品报损 Dao
*
* <AUTHOR>
*/
@Mapper
public interface MedicineDeliveryLossDao extends IBaseDao<MedicineDeliveryLossDO> {


    default PageResult<MedicineDeliveryLossDO> selectPage(MedicineDeliveryLossPageReqVO reqVO) {
        Page<MedicineDeliveryLossDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<MedicineDeliveryLossDO> medicineDeliveryLossPage = selectPage(page, new LambdaQueryWrapperX<MedicineDeliveryLossDO>()
            .eqIfPresent(MedicineDeliveryLossDO::getMedicineId, reqVO.getMedicineId())
            .eqIfPresent(MedicineDeliveryLossDO::getMedicineInId, reqVO.getMedicineInId())
            .eqIfPresent(MedicineDeliveryLossDO::getLossReason, reqVO.getLossReason())
            .eqIfPresent(MedicineDeliveryLossDO::getLossNum, reqVO.getLossNum())
            .betweenIfPresent(MedicineDeliveryLossDO::getLossTime, reqVO.getLossTime())
            .eqIfPresent(MedicineDeliveryLossDO::getRemark, reqVO.getRemark())
            .eqIfPresent(MedicineDeliveryLossDO::getInventoryNum, reqVO.getInventoryNum())
            .eqIfPresent(MedicineDeliveryLossDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
            .orderByDesc(MedicineDeliveryLossDO::getId));
            return new PageResult<>(medicineDeliveryLossPage.getRecords(), medicineDeliveryLossPage.getTotal());
    }
    default List<MedicineDeliveryLossDO> selectList(MedicineDeliveryLossListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MedicineDeliveryLossDO>()
            .eqIfPresent(MedicineDeliveryLossDO::getMedicineId, reqVO.getMedicineId())
            .eqIfPresent(MedicineDeliveryLossDO::getMedicineInId, reqVO.getMedicineInId())
            .eqIfPresent(MedicineDeliveryLossDO::getLossReason, reqVO.getLossReason())
            .eqIfPresent(MedicineDeliveryLossDO::getLossNum, reqVO.getLossNum())
            .betweenIfPresent(MedicineDeliveryLossDO::getLossTime, reqVO.getLossTime())
            .eqIfPresent(MedicineDeliveryLossDO::getRemark, reqVO.getRemark())
            .eqIfPresent(MedicineDeliveryLossDO::getInventoryNum, reqVO.getInventoryNum())
            .eqIfPresent(MedicineDeliveryLossDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
        .orderByDesc(MedicineDeliveryLossDO::getId));    }


    }
