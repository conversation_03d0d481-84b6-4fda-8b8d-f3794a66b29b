package com.rs.module.ihc.service.ipm;

import javax.validation.*;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.VisitMedicineDO;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 所内就医-现场巡诊-现场司药信息 Service 接口
 *
 * <AUTHOR>
 */
public interface VisitMedicineService extends IBaseService<VisitMedicineDO>{

    /**
     * 创建所内就医-现场巡诊-现场司药信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createVisitMedicine(@Valid VisitMedicineSaveReqVO createReqVO);

    /**
     * 更新所内就医-现场巡诊-现场司药信息
     *
     * @param updateReqVO 更新信息
     */
    void updateVisitMedicine(@Valid VisitMedicineSaveReqVO updateReqVO);

    /**
     * 删除所内就医-现场巡诊-现场司药信息
     *
     * @param id 编号
     */
    void deleteVisitMedicine(String id);

    /**
     * 获得所内就医-现场巡诊-现场司药信息
     *
     * @param id 编号
     * @return 所内就医-现场巡诊-现场司药信息
     */
    VisitMedicineDO getVisitMedicine(String id);



}
