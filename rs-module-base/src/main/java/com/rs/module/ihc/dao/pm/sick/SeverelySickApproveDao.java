package com.rs.module.ihc.dao.pm.sick;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.pm.sick.SeverelySickApproveDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.pm.sick.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 重特病号审批 Dao
*
* <AUTHOR>
*/
@Mapper
public interface SeverelySickApproveDao extends IBaseDao<SeverelySickApproveDO> {


    default PageResult<SeverelySickApproveDO> selectPage(SeverelySickApprovePageReqVO reqVO) {
        Page<SeverelySickApproveDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<SeverelySickApproveDO> wrapper = new LambdaQueryWrapperX<SeverelySickApproveDO>()
            .eqIfPresent(SeverelySickApproveDO::getManageId, reqVO.getManageId())
            .eqIfPresent(SeverelySickApproveDO::getBusinessStatus, reqVO.getBusinessStatus())
            .eqIfPresent(SeverelySickApproveDO::getApproveResult, reqVO.getApproveResult())
            .eqIfPresent(SeverelySickApproveDO::getApproveUserId, reqVO.getApproveUserId())
            .likeIfPresent(SeverelySickApproveDO::getApproveUserName, reqVO.getApproveUserName())
            .eqIfPresent(SeverelySickApproveDO::getApproveOpinion, reqVO.getApproveOpinion())
            .betweenIfPresent(SeverelySickApproveDO::getApproveTime, reqVO.getApproveTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(SeverelySickApproveDO::getAddTime);
        }
        Page<SeverelySickApproveDO> severelySickApprovePage = selectPage(page, wrapper);
        return new PageResult<>(severelySickApprovePage.getRecords(), severelySickApprovePage.getTotal());
    }
    default List<SeverelySickApproveDO> selectList(SeverelySickApproveListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SeverelySickApproveDO>()
            .eqIfPresent(SeverelySickApproveDO::getManageId, reqVO.getManageId())
            .eqIfPresent(SeverelySickApproveDO::getBusinessStatus, reqVO.getBusinessStatus())
            .eqIfPresent(SeverelySickApproveDO::getApproveResult, reqVO.getApproveResult())
            .eqIfPresent(SeverelySickApproveDO::getApproveUserId, reqVO.getApproveUserId())
            .likeIfPresent(SeverelySickApproveDO::getApproveUserName, reqVO.getApproveUserName())
            .eqIfPresent(SeverelySickApproveDO::getApproveOpinion, reqVO.getApproveOpinion())
            .betweenIfPresent(SeverelySickApproveDO::getApproveTime, reqVO.getApproveTime())
        .orderByDesc(SeverelySickApproveDO::getAddTime));    }


    }
