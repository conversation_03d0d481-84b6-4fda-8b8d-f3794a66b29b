package com.rs.module.ihc.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.pm.MedicineDeliveryOutDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 药房管理-顾送药品出库 Dao
*
* <AUTHOR>
*/
@Mapper
public interface MedicineDeliveryOutDao extends IBaseDao<MedicineDeliveryOutDO> {


    default PageResult<MedicineDeliveryOutDO> selectPage(MedicineDeliveryOutPageReqVO reqVO) {
        Page<MedicineDeliveryOutDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<MedicineDeliveryOutDO> wrapper = new LambdaQueryWrapperX<MedicineDeliveryOutDO>()
            .eqIfPresent(MedicineDeliveryOutDO::getMedicineId, reqVO.getMedicineId())
            .eqIfPresent(MedicineDeliveryOutDO::getMedicineInId, reqVO.getMedicineInId())
            .eqIfPresent(MedicineDeliveryOutDO::getOutStorageReason, reqVO.getOutStorageReason())
            .eqIfPresent(MedicineDeliveryOutDO::getOutNum, reqVO.getOutNum())
            .eqIfPresent(MedicineDeliveryOutDO::getDispensaryUnit, reqVO.getDispensaryUnit())
            .betweenIfPresent(MedicineDeliveryOutDO::getDispensaryTime, reqVO.getDispensaryTime())
            .eqIfPresent(MedicineDeliveryOutDO::getUserid, reqVO.getUserid())
            .likeIfPresent(MedicineDeliveryOutDO::getUserName, reqVO.getUserName())
            .eqIfPresent(MedicineDeliveryOutDO::getRemark, reqVO.getRemark())
            .eqIfPresent(MedicineDeliveryOutDO::getInventoryNum, reqVO.getInventoryNum())
            .eqIfPresent(MedicineDeliveryOutDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
            .eqIfPresent(MedicineDeliveryOutDO::getOutStatus, reqVO.getOutStatus())
            .eqIfPresent(MedicineDeliveryOutDO::getWorkflowEnd, reqVO.getWorkflowEnd())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(MedicineDeliveryOutDO::getAddTime);
        }
        Page<MedicineDeliveryOutDO> medicineDeliveryOutPage = selectPage(page, wrapper);
        return new PageResult<>(medicineDeliveryOutPage.getRecords(), medicineDeliveryOutPage.getTotal());
    }
    default List<MedicineDeliveryOutDO> selectList(MedicineDeliveryOutListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MedicineDeliveryOutDO>()
            .eqIfPresent(MedicineDeliveryOutDO::getMedicineId, reqVO.getMedicineId())
            .eqIfPresent(MedicineDeliveryOutDO::getMedicineInId, reqVO.getMedicineInId())
            .eqIfPresent(MedicineDeliveryOutDO::getOutStorageReason, reqVO.getOutStorageReason())
            .eqIfPresent(MedicineDeliveryOutDO::getOutNum, reqVO.getOutNum())
            .eqIfPresent(MedicineDeliveryOutDO::getDispensaryUnit, reqVO.getDispensaryUnit())
            .betweenIfPresent(MedicineDeliveryOutDO::getDispensaryTime, reqVO.getDispensaryTime())
            .eqIfPresent(MedicineDeliveryOutDO::getUserid, reqVO.getUserid())
            .likeIfPresent(MedicineDeliveryOutDO::getUserName, reqVO.getUserName())
            .eqIfPresent(MedicineDeliveryOutDO::getRemark, reqVO.getRemark())
            .eqIfPresent(MedicineDeliveryOutDO::getInventoryNum, reqVO.getInventoryNum())
            .eqIfPresent(MedicineDeliveryOutDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
            .eqIfPresent(MedicineDeliveryOutDO::getOutStatus, reqVO.getOutStatus())
            .eqIfPresent(MedicineDeliveryOutDO::getWorkflowEnd, reqVO.getWorkflowEnd())
        .orderByDesc(MedicineDeliveryOutDO::getAddTime));    }


    }
