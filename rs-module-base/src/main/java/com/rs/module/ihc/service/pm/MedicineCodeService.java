package com.rs.module.ihc.service.pm;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.MedicineCodeDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 药房管理-国家药品编码本位码信息 Service 接口
 *
 * <AUTHOR>
 */
public interface MedicineCodeService extends IBaseService<MedicineCodeDO>{

    /**
     * 创建药房管理-国家药品编码本位码信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMedicineCode(@Valid MedicineCodeSaveReqVO createReqVO);

    /**
     * 更新药房管理-国家药品编码本位码信息
     *
     * @param updateReqVO 更新信息
     */
    void updateMedicineCode(@Valid MedicineCodeSaveReqVO updateReqVO);

    /**
     * 删除药房管理-国家药品编码本位码信息
     *
     * @param id 编号
     */
    void deleteMedicineCode(String id);

    /**
     * 获得药房管理-国家药品编码本位码信息
     *
     * @param id 编号
     * @return 药房管理-国家药品编码本位码信息
     */
    MedicineCodeDO getMedicineCode(String id);

    /**
    * 获得药房管理-国家药品编码本位码信息分页
    *
    * @param pageReqVO 分页查询
    * @return 药房管理-国家药品编码本位码信息分页
    */
    PageResult<MedicineCodeDO> getMedicineCodePage(MedicineCodePageReqVO pageReqVO);

    /**
    * 获得药房管理-国家药品编码本位码信息列表
    *
    * @param listReqVO 查询条件
    * @return 药房管理-国家药品编码本位码信息列表
    */
    List<MedicineCodeDO> getMedicineCodeList(MedicineCodeListReqVO listReqVO);


}
