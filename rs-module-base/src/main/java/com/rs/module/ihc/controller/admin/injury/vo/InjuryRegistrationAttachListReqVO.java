package com.rs.module.ihc.controller.admin.injury.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 医疗子系统-伤亡登记-附件信息列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class InjuryRegistrationAttachListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("关联主表ID")
    private String registrationId;

    @ApiModelProperty("附件URL")
    private String attUrl;

    @ApiModelProperty("描述")
    private String remark;

}
