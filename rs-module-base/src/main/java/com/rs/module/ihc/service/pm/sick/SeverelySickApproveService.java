package com.rs.module.ihc.service.pm.sick;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.pm.sick.vo.*;
import com.rs.module.ihc.entity.pm.sick.SeverelySickApproveDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 重特病号审批 Service 接口
 *
 * <AUTHOR>
 */
public interface SeverelySickApproveService extends IBaseService<SeverelySickApproveDO>{

    /**
     * 创建重特病号审批
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createSeverelySickApprove(@Valid SeverelySickApproveSaveReqVO createReqVO);

    /**
     * 更新重特病号审批
     *
     * @param updateReqVO 更新信息
     */
    void updateSeverelySickApprove(@Valid SeverelySickApproveSaveReqVO updateReqVO);

    /**
     * 删除重特病号审批
     *
     * @param id 编号
     */
    void deleteSeverelySickApprove(String id);

    /**
     * 获得重特病号审批
     *
     * @param id 编号
     * @return 重特病号审批
     */
    SeverelySickApproveDO getSeverelySickApprove(String id);

    /**
    * 获得重特病号审批分页
    *
    * @param pageReqVO 分页查询
    * @return 重特病号审批分页
    */
    PageResult<SeverelySickApproveDO> getSeverelySickApprovePage(SeverelySickApprovePageReqVO pageReqVO);

    /**
    * 获得重特病号审批列表
    *
    * @param listReqVO 查询条件
    * @return 重特病号审批列表
    */
    List<SeverelySickApproveDO> getSeverelySickApproveList(SeverelySickApproveListReqVO listReqVO);


}
