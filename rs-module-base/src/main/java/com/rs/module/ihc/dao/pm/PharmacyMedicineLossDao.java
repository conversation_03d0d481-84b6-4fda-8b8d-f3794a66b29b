package com.rs.module.ihc.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.pm.PharmacyMedicineLossDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 大药房管理-药品报损 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PharmacyMedicineLossDao extends IBaseDao<PharmacyMedicineLossDO> {


    default PageResult<PharmacyMedicineLossDO> selectPage(PharmacyMedicineLossPageReqVO reqVO) {
        Page<PharmacyMedicineLossDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PharmacyMedicineLossDO> wrapper = new LambdaQueryWrapperX<PharmacyMedicineLossDO>()
            .eqIfPresent(PharmacyMedicineLossDO::getMedicineId, reqVO.getMedicineId())
            .eqIfPresent(PharmacyMedicineLossDO::getMedicineInId, reqVO.getMedicineInId())
            .eqIfPresent(PharmacyMedicineLossDO::getLossReason, reqVO.getLossReason())
            .eqIfPresent(PharmacyMedicineLossDO::getLossNum, reqVO.getLossNum())
            .betweenIfPresent(PharmacyMedicineLossDO::getLossTime, reqVO.getLossTime())
            .eqIfPresent(PharmacyMedicineLossDO::getRemark, reqVO.getRemark())
            .eqIfPresent(PharmacyMedicineLossDO::getInventoryNum, reqVO.getInventoryNum())
            .eqIfPresent(PharmacyMedicineLossDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PharmacyMedicineLossDO::getAddTime);
        }
        Page<PharmacyMedicineLossDO> pharmacyMedicineLossPage = selectPage(page, wrapper);
        return new PageResult<>(pharmacyMedicineLossPage.getRecords(), pharmacyMedicineLossPage.getTotal());
    }
    default List<PharmacyMedicineLossDO> selectList(PharmacyMedicineLossListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PharmacyMedicineLossDO>()
            .eqIfPresent(PharmacyMedicineLossDO::getMedicineId, reqVO.getMedicineId())
            .eqIfPresent(PharmacyMedicineLossDO::getMedicineInId, reqVO.getMedicineInId())
            .eqIfPresent(PharmacyMedicineLossDO::getLossReason, reqVO.getLossReason())
            .eqIfPresent(PharmacyMedicineLossDO::getLossNum, reqVO.getLossNum())
            .betweenIfPresent(PharmacyMedicineLossDO::getLossTime, reqVO.getLossTime())
            .eqIfPresent(PharmacyMedicineLossDO::getRemark, reqVO.getRemark())
            .eqIfPresent(PharmacyMedicineLossDO::getInventoryNum, reqVO.getInventoryNum())
            .eqIfPresent(PharmacyMedicineLossDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
        .orderByDesc(PharmacyMedicineLossDO::getAddTime));    }


    }
