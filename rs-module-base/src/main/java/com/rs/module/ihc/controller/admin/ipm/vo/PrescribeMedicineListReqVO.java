package com.rs.module.ihc.controller.admin.ipm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(description = "管理后台 - 所内就医-处方-药方信息列表 Request VO")
@Data
public class PrescribeMedicineListReqVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("处方id，对应ihc_ipm_prescribe.id")
    private String mlh;

    @ApiModelProperty("药品id，对应ihc_pm_drug.id")
    private String medicineId;

    @ApiModelProperty("总库存数量")
    private BigDecimal totalInventoryNum;

    @ApiModelProperty("每次用量（数字）")
    private BigDecimal oneDosageNum;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("使用频率")
    private String useFrequency;

    @ApiModelProperty("用药天数")
    private Integer useDay;

    @ApiModelProperty("总药量")
    private BigDecimal num;

    @ApiModelProperty("给药方式")
    private String useMedicineMethod;

    @ApiModelProperty("嘱托")
    private String entrust;

}
