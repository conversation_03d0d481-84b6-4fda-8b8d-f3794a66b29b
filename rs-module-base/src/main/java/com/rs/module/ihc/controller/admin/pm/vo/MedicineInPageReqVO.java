package com.rs.module.ihc.controller.admin.pm.vo;

import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 药房管理-药品入库批次分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MedicineInPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("药品id，对应ihc_ppm_drug.id")
    private String medicineId;

    @ApiModelProperty("入库数量")
    private BigDecimal inNum;

    @ApiModelProperty("批次编码，编号规则：8位日期+3位序列号，如：20250228001")
    private String batchCode;

    @ApiModelProperty("供药单位，对应ihc_pm_supplier.id")
    private Long supplierId;

    @ApiModelProperty("供药单位名称")
    private String supplierName;

    @ApiModelProperty("凭证号")
    private String voucherCode;

    @ApiModelProperty("进价")
    private BigDecimal purchasePrice;

    @ApiModelProperty("批发价")
    private BigDecimal wholesalePrice;

    @ApiModelProperty("调拨价")
    private BigDecimal transferPrice;

    @ApiModelProperty("零售价")
    private BigDecimal retailPrice;

    @ApiModelProperty("入库日期")
    private Date[] inDate;

    @ApiModelProperty("有效期至")
    private Date[] expireDate;

    @ApiModelProperty("入库方式")
    private String inStorageMethod;

    @ApiModelProperty("调拨方式")
    private String transferMethod;

    @ApiModelProperty("药品货位")
    private String medicinePlace;

    @ApiModelProperty("批次库存数量 最小单位")
    private BigDecimal inventoryNum;

    @ApiModelProperty("当前药品总库存数量 最小单位")
    private BigDecimal totalInventoryNum;

    @ApiModelProperty("入库状态")
    private String inStatus;

    @ApiModelProperty("流程是否结束 0=否 1=是")
    private Integer workflowEnd;

}
