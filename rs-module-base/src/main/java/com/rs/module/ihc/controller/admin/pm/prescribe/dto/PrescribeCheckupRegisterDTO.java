package com.rs.module.ihc.controller.admin.pm.prescribe.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class PrescribeCheckupRegisterDTO {

    /**
     * 处方id，对应ihcs_internal_medical_prescribe.id
     */
    @NotNull(message = "处方id不能为空")
    @ApiModelProperty(value = "处方id，对应ihcs_internal_medical_prescribe.id")
    private String prescribeId;

    /**
     * 血压(高压)
     */
    @ApiModelProperty(value = "血压(高压)")
    private BigDecimal bloodPressureHeight;

    /**
     * 血压(低压)
     */
    @ApiModelProperty(value = "血压(低压)")
    private BigDecimal bloodPressureLow;

    /**
     * 体温
     */
    @ApiModelProperty(value = "体温")
    private BigDecimal temperature;

    /**
     * 心率
     */
    @ApiModelProperty(value = "心率")
    private BigDecimal heartRate;

    /**
     * 呼吸频次
     */
    @ApiModelProperty(value = "呼吸频次")
    private BigDecimal breatheFrequency;

    /**
     * 脉搏
     */
    @ApiModelProperty(value = "脉搏")
    private BigDecimal pulse;

    /**
     * 血氧
     */
    @ApiModelProperty(value = "血氧")
    private BigDecimal bloodOxygen;

    /**
     * 血糖
     */
    @ApiModelProperty(value = "血糖")
    private BigDecimal bloodSugar;

    /**
     * 胰岛素
     */
    @ApiModelProperty(value = "胰岛素")
    private BigDecimal insulin;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "注射时间")
    private Date injectionTime;

}
