package com.rs.module.ihc.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.pm.MedicineLossDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 药房管理-药品报损 Dao
*
* <AUTHOR>
*/
@Mapper
public interface MedicineLossDao extends IBaseDao<MedicineLossDO> {


    default PageResult<MedicineLossDO> selectPage(MedicineLossPageReqVO reqVO) {
        Page<MedicineLossDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<MedicineLossDO> medicineLossPage = selectPage(page, new LambdaQueryWrapperX<MedicineLossDO>()
            .eqIfPresent(MedicineLossDO::getMedicineId, reqVO.getMedicineId())
            .eqIfPresent(MedicineLossDO::getMedicineInId, reqVO.getMedicineInId())
            .eqIfPresent(MedicineLossDO::getLossReason, reqVO.getLossReason())
            .eqIfPresent(MedicineLossDO::getLossNum, reqVO.getLossNum())
            .betweenIfPresent(MedicineLossDO::getLossTime, reqVO.getLossTime())
            .eqIfPresent(MedicineLossDO::getRemark, reqVO.getRemark())
            .eqIfPresent(MedicineLossDO::getInventoryNum, reqVO.getInventoryNum())
            .eqIfPresent(MedicineLossDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
            .orderByDesc(MedicineLossDO::getId));
            return new PageResult<>(medicineLossPage.getRecords(), medicineLossPage.getTotal());
    }
    default List<MedicineLossDO> selectList(MedicineLossListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MedicineLossDO>()
            .eqIfPresent(MedicineLossDO::getMedicineId, reqVO.getMedicineId())
            .eqIfPresent(MedicineLossDO::getMedicineInId, reqVO.getMedicineInId())
            .eqIfPresent(MedicineLossDO::getLossReason, reqVO.getLossReason())
            .eqIfPresent(MedicineLossDO::getLossNum, reqVO.getLossNum())
            .betweenIfPresent(MedicineLossDO::getLossTime, reqVO.getLossTime())
            .eqIfPresent(MedicineLossDO::getRemark, reqVO.getRemark())
            .eqIfPresent(MedicineLossDO::getInventoryNum, reqVO.getInventoryNum())
            .eqIfPresent(MedicineLossDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
        .orderByDesc(MedicineLossDO::getId));    }


    }
