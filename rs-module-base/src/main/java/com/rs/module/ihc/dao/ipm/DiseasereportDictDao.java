package com.rs.module.ihc.dao.ipm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.ipm.DiseasereportDictDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 所内就医-报病字典管理 Dao
*
* <AUTHOR>
*/
@Mapper
public interface DiseasereportDictDao extends IBaseDao<DiseasereportDictDO> {


    default PageResult<DiseasereportDictDO> selectPage(DiseasereportDictPageReqVO reqVO) {
        Page<DiseasereportDictDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<DiseasereportDictDO> wrapper = new LambdaQueryWrapperX<DiseasereportDictDO>()
            .eqIfPresent(DiseasereportDictDO::getDiseaseType, reqVO.getDiseaseType())
            .eqIfPresent(DiseasereportDictDO::getDiseaseSymptom, reqVO.getDiseaseSymptom())
            .eqIfPresent(DiseasereportDictDO::getIsBuiltIn, reqVO.getIsBuiltIn())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
            wrapper.orderByDesc(DiseasereportDictDO::getAddTime);
        }
        Page<DiseasereportDictDO> diseasereportDictPage = selectPage(page, wrapper);
        return new PageResult<>(diseasereportDictPage.getRecords(), diseasereportDictPage.getTotal());
    }
    default List<DiseasereportDictDO> selectList(DiseasereportDictListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DiseasereportDictDO>()
            .eqIfPresent(DiseasereportDictDO::getDiseaseType, reqVO.getDiseaseType())
            .eqIfPresent(DiseasereportDictDO::getDiseaseSymptom, reqVO.getDiseaseSymptom())
            .eqIfPresent(DiseasereportDictDO::getIsBuiltIn, reqVO.getIsBuiltIn())
        .orderByDesc(DiseasereportDictDO::getAddTime));    }


    }
