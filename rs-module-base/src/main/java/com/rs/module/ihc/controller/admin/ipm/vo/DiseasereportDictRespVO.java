package com.rs.module.ihc.controller.admin.ipm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 所内就医-报病字典管理 Response VO")
@Data
public class DiseasereportDictRespVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("疾病类型（字典：报病字典 ZD_BBZD）")
    private String diseaseType;
    @ApiModelProperty("疾病症状")
    private String diseaseSymptom;
    @ApiModelProperty("是否内置")
    private String isBuiltIn;
}
