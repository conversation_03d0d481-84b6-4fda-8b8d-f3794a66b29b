package com.rs.module.ihc.constant;

/**
 * 药品入库方式 目前大小药房共用一个字典，有区别时请分开
 *
 * @see IhcsDictionaryConstant#IN_STORAGE_METHOD
 */
public interface InStorageMethodConstant {

    /**
     * 药物顾送
     */
    String MEDICINE_GU_SONG = "1";

    /**
     * 采购入库
     */
    String PURCHASE_STORE = "2";

    /**
     * 调拨入库
     */
    String TRANSFER_STORE = "3";

    /**
     * 临时领药归还
     */
    String TEMPORARY_RETURN = "4";

    /**
     * 拒服药入库
     */
    String REFUSE_DOSE_RETURN = "6";
}
