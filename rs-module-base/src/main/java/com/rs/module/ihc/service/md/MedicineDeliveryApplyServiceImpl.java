package com.rs.module.ihc.service.md;

import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BpmApi;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.base.vo.ApproveTraceVO;
import com.rs.module.base.vo.SimpleApproveReqVO;
import com.rs.module.ihc.controller.admin.md.vo.*;
import com.rs.module.ihc.dao.md.MedicineDeliveryApplyDao;
import com.rs.module.ihc.entity.md.MedicineDeliveryApplyDO;
import com.rs.module.ihc.enums.MedicineDeliveryApplyStatusEnum;
import com.rs.util.ApprovalTraceUtil;
import com.rs.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


/**
 * 药品顾送管理-药品顾送申请 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class MedicineDeliveryApplyServiceImpl extends BaseServiceImpl<MedicineDeliveryApplyDao, MedicineDeliveryApplyDO> implements MedicineDeliveryApplyService {

    @Resource
    private MedicineDeliveryApplyDao medicineDeliveryApplyDao;

    @Resource
    private BpmApi bpmApi;

    private final String defKey = "yaopingusongliucheng";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createMedicineDeliveryApply(MedicineDeliveryApplySaveReqVO createReqVO) {
        // 插入
        MedicineDeliveryApplyDO medicineDeliveryApply = BeanUtils.toBean(createReqVO, MedicineDeliveryApplyDO.class);
        medicineDeliveryApply.setStatus(MedicineDeliveryApplyStatusEnum.DSP.getCode());
        medicineDeliveryApplyDao.insert(medicineDeliveryApply);
        // 插入
        String busType = MsgBusTypeEnum.YL_LDXS.getCode();
        //TODO 待前端提供
        String msgUrl = StrUtil.format("/#/discipline/ypgs?curId={}&saveType=approve", medicineDeliveryApply.getId());
        MapBuilder<String, Object> variables = MapUtil.builder();
        variables.put("ywbh", medicineDeliveryApply.getId()).put("busType", busType);
        JSONObject result = BspApprovalUtil.commonStartProcess(defKey, medicineDeliveryApply.getId(), "【审批】药品顾送", msgUrl, variables.build(), HttpUtils.getAppCode());
        log.info("==========result:{}", result);
        if (result.getIntValue("code") != HttpStatus.OK.value()) {
            throw new ServerException("流程启动失败");
        }
        JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
        medicineDeliveryApply.setActInstId(bpmTrail.getString("actInstId"));
        medicineDeliveryApply.setTaskId(bpmTrail.getString("taskId"));
        medicineDeliveryApply.setStatus(MedicineDeliveryApplyStatusEnum.DSP.getCode());
        medicineDeliveryApplyDao.updateById(medicineDeliveryApply);

        return medicineDeliveryApply.getId();
    }



    @Override
    public void deleteMedicineDeliveryApply(String id) {
        // 校验存在
        validateMedicineDeliveryApplyExists(id);
        // 删除
        medicineDeliveryApplyDao.deleteById(id);
    }

    private void validateMedicineDeliveryApplyExists(String id) {
        if (medicineDeliveryApplyDao.selectById(id) == null) {
            throw new ServerException("药品顾送管理-药品顾送申请数据不存在");
        }
    }

    @Override
    public MedicineDeliveryApplyRespVO getMedicineDeliveryApply(String id) {
        MedicineDeliveryApplyDO medicineDeliveryApplyDO = medicineDeliveryApplyDao.selectById(id);
        MedicineDeliveryApplyRespVO medicineDeliveryApplyRespVO = BeanUtils.toBean(medicineDeliveryApplyDO, MedicineDeliveryApplyRespVO.class);
        if(medicineDeliveryApplyRespVO != null){
            List<ApproveTraceVO> approveList = ApprovalTraceUtil.converBspApprovalResult(medicineDeliveryApplyRespVO.getActInstId());
            medicineDeliveryApplyRespVO.setApproveList(approveList );
        }
        return medicineDeliveryApplyRespVO;
    }

    @Override
    public PageResult<MedicineDeliveryApplyAppRespVO> getMedicineDeliveryApplyPage(MedicineDeliveryApplyAppPageReqVO pageReqVO) {
        Page<MedicineDeliveryApplyAppRespVO> page = new Page<MedicineDeliveryApplyAppRespVO>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        pageReqVO.setOrgCode(sessionUser.getOrgCode());
        if(pageReqVO.getType() != null){
            Date[] dates = DateUtil.getTimeRange(pageReqVO.getType());
            pageReqVO.setStartTime( dates[0]);
            pageReqVO.setEndTime( dates[1]);
        }
        IPage<MedicineDeliveryApplyAppRespVO> respVOIPage = medicineDeliveryApplyDao.getMedicineDeliveryApplyAppRespVO(page, pageReqVO);
        return new PageResult<>(respVOIPage.getRecords(),respVOIPage.getTotal());
    }

    @Override
    public List<MedicineDeliveryApplyDO> getMedicineDeliveryApplyList(MedicineDeliveryApplyListReqVO listReqVO) {
        return medicineDeliveryApplyDao.selectList(listReqVO);
    }

    @Override
    public void approve(SimpleApproveReqVO approveReqVO) {

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        MedicineDeliveryApplyDO entity = medicineDeliveryApplyDao.selectById(approveReqVO.getId());
        Assert.notNull(entity, "记录不存在！");

        MedicineDeliveryApplyStatusEnum statusEnum = MedicineDeliveryApplyStatusEnum.getByCode(entity.getStatus());
        if (!MedicineDeliveryApplyStatusEnum.DSP.getCode().equals(statusEnum.getCode())) {
            throw new ServerException("该状态[" + statusEnum.getName() + "]不允许审批！");
        }

        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(entity.getTaskId(), sessionUser.getIdCard());
        Assert.isTrue(isApproval, "当前人无审批权限" );

        String approvalResult = approveReqVO.getApprovalResult();
        BspApproceStatusEnum bspApproceStatusEnum;
        String status = MedicineDeliveryApplyStatusEnum.YWC.getCode();
        if (String.valueOf( BspApproceStatusEnum.PASSED.getCode()).equals(approvalResult)) {
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED;
        } else {
            bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
            status = MedicineDeliveryApplyStatusEnum.YZZ.getCode();
        }
        entity.setStatus(status);

        JSONObject result = BspApprovalUtil.approvalProcessAcp(defKey,
                entity.getActInstId(),
                entity.getTaskId(),
                entity.getId(),
                bspApproceStatusEnum,
                approveReqVO.getApprovalComments());

        log.info("=======result:{}", result);
        if(result.getIntValue("code") != HttpStatus.OK.value()){
            throw new ServerException("流程审批失败");
        }

        Boolean finishProcinst = bpmApi.isFinishProcinst(entity.getActInstId());
        if(finishProcinst != null && !finishProcinst){
            entity.setStatus(MedicineDeliveryApplyStatusEnum.DSP.getCode());
        }

        JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
        entity.setTaskId(bpmTrail.getString("taskId"));
        medicineDeliveryApplyDao.updateById(entity);


    }

    @Override
    public void regInfo(MedicineDeliveryApplyRegInfoReqVO regInfoReqVO) {
        MedicineDeliveryApplyDO medicineDeliveryApplyDO = medicineDeliveryApplyDao.selectById(regInfoReqVO.getId());
        Assert.notNull( medicineDeliveryApplyDO, "药品顾送申请数据不存在");
        BeanUtils.copyProperties(regInfoReqVO, medicineDeliveryApplyDO);
        medicineDeliveryApplyDO.setStatus(MedicineDeliveryApplyStatusEnum.YWC.getCode() );
        medicineDeliveryApplyDao.updateById(medicineDeliveryApplyDO);
    }

    @Override
    public void abnormalRegInfo(MedicineDeliveryApplyAbnormalReqVO regInfoReqVO) {
        MedicineDeliveryApplyDO medicineDeliveryApplyDO = medicineDeliveryApplyDao.selectById(regInfoReqVO.getId());
        Assert.notNull( medicineDeliveryApplyDO, "药品顾送申请数据不存在");
        BeanUtils.copyProperties(regInfoReqVO, medicineDeliveryApplyDO);
        medicineDeliveryApplyDO.setStatus(MedicineDeliveryApplyStatusEnum.YZZ.getCode() );
        medicineDeliveryApplyDao.updateById(medicineDeliveryApplyDO);
    }

    @Override
    public PageResult<MedicineDeliveryApplyDO> getMedicineDeliveryApplyDefaultPage(MedicineDeliveryApplyPageReqVO pageReqVO) {
        return medicineDeliveryApplyDao.selectPage(pageReqVO);
    }

}
