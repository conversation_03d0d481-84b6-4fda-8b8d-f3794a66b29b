package com.rs.module.ihc.controller.admin.hr.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 健康档案-打印文书配置列表 Request VO")
@Data
public class PrintdocConfigListReqVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("表单ID")
    private String formId;

    @ApiModelProperty("文书名称")
    private String wsName;

    @ApiModelProperty("文书地址")
    private String wsUrl;

    @ApiModelProperty("文书排序ID")
    private Integer orderId;

    @ApiModelProperty("是否禁用")
    private Integer isDisabled;

}
