package com.rs.module.ihc.constant;


import com.rs.framework.mybatis.util.BizAssert;

/**
 * 医疗系统-所情医生处置状态
 * <p>
 * 对应字典值：{@link IhcsDictionaryConstant#PRISON_EVENT_DOCTOR_HANDLE_STATUS}
 */
public interface PrisonEventDoctorHandleStatusConstant {

    /**
     * 未处置
     */
    String NO_HANDLE = "0";

    /**
     * 已处置
     */
    String HANDLE = "1";

    /**
     * 保存
     */
    String SUBMIT = "submit";

    /**
     * 提交
     */
    String CLOSE = "close";

    /**
     * 所内就医类型
     */
    String BUSINESS_TYPE_MEDICAL = "5";
    /**
     * 附件上传
     */
    String BUSINESS_TYPE_FILE_UPLOAD = "11";
    /**
     * 附件删除
     */
    String BUSINESS_TYPE_FILE_DELETE = "12";
    /**
     * 文本
     */
    String BUSINESS_TYPE_TEXT = "13";

    /**
     * 重点文本，加红加粗
     */
    String BUSINESS_TYPE_KEY_TEXT = "14";


    /**
     * 无需处理选项
     */
    String NO_NEED_HANDLE = "0";


    /**
     * 校验所情是否可以保存和处置
     *
     * @param doctorHandleStatus 医生处置状态
     */
    static void checkPrisonEventIsHandle(String doctorHandleStatus) {
        BizAssert.isTrue(NO_HANDLE.equals(doctorHandleStatus), "未处置状态的所情才能保存和处置");
    }
}
