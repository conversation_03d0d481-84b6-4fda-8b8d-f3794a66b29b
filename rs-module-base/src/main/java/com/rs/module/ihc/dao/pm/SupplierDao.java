package com.rs.module.ihc.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.pm.SupplierDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 药房管理-供应商 Dao
*
* <AUTHOR>
*/
@Mapper
public interface SupplierDao extends IBaseDao<SupplierDO> {


    default PageResult<SupplierDO> selectPage(SupplierPageReqVO reqVO) {
        Page<SupplierDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<SupplierDO> supplierPage = selectPage(page, new LambdaQueryWrapperX<SupplierDO>()
            .likeIfPresent(SupplierDO::getCompanyName, reqVO.getCompanyName())
            .eqIfPresent(SupplierDO::getAddress, reqVO.getAddress())
            .eqIfPresent(SupplierDO::getPostalCode, reqVO.getPostalCode())
            .eqIfPresent(SupplierDO::getTelephone, reqVO.getTelephone())
            .eqIfPresent(SupplierDO::getContacts, reqVO.getContacts())
            .eqIfPresent(SupplierDO::getOpeningBank, reqVO.getOpeningBank())
            .eqIfPresent(SupplierDO::getAccount, reqVO.getAccount())
            .eqIfPresent(SupplierDO::getDutyParagraph, reqVO.getDutyParagraph())
            .eqIfPresent(SupplierDO::getPrisonId, reqVO.getPrisonId())
            .orderByDesc(SupplierDO::getId));
            return new PageResult<>(supplierPage.getRecords(), supplierPage.getTotal());
    }
    default List<SupplierDO> selectList(SupplierListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SupplierDO>()
            .likeIfPresent(SupplierDO::getCompanyName, reqVO.getCompanyName())
            .eqIfPresent(SupplierDO::getAddress, reqVO.getAddress())
            .eqIfPresent(SupplierDO::getPostalCode, reqVO.getPostalCode())
            .eqIfPresent(SupplierDO::getTelephone, reqVO.getTelephone())
            .eqIfPresent(SupplierDO::getContacts, reqVO.getContacts())
            .eqIfPresent(SupplierDO::getOpeningBank, reqVO.getOpeningBank())
            .eqIfPresent(SupplierDO::getAccount, reqVO.getAccount())
            .eqIfPresent(SupplierDO::getDutyParagraph, reqVO.getDutyParagraph())
            .eqIfPresent(SupplierDO::getPrisonId, reqVO.getPrisonId())
        .orderByDesc(SupplierDO::getId));    }


    }
