package com.rs.module.ihc.dao.idm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.idm.RegularInspectionDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.idm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 传染病管理-定期检查登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface RegularInspectionDao extends IBaseDao<RegularInspectionDO> {


    default PageResult<RegularInspectionDO> selectPage(RegularInspectionPageReqVO reqVO) {
        Page<RegularInspectionDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<RegularInspectionDO> wrapper = new LambdaQueryWrapperX<RegularInspectionDO>()
            .eqIfPresent(RegularInspectionDO::getInfectionId, reqVO.getInfectionId())
            .eqIfPresent(RegularInspectionDO::getJgrybm, reqVO.getJgrybm())
            .betweenIfPresent(RegularInspectionDO::getCheckDate, reqVO.getCheckDate())
            .eqIfPresent(RegularInspectionDO::getCd4tlbz, reqVO.getCd4tlbz())
            .eqIfPresent(RegularInspectionDO::getViralLoadValue, reqVO.getViralLoadValue())
            .eqIfPresent(RegularInspectionDO::getManagementSituation, reqVO.getManagementSituation())
            .eqIfPresent(RegularInspectionDO::getControlPersonnel, reqVO.getControlPersonnel())
            .eqIfPresent(RegularInspectionDO::getAttUrl, reqVO.getAttUrl())
            .eqIfPresent(RegularInspectionDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(RegularInspectionDO::getOperatePolice, reqVO.getOperatePolice())
            .betweenIfPresent(RegularInspectionDO::getOperateTime, reqVO.getOperateTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(RegularInspectionDO::getAddTime);
        }
        Page<RegularInspectionDO> regularInspectionPage = selectPage(page, wrapper);
        return new PageResult<>(regularInspectionPage.getRecords(), regularInspectionPage.getTotal());
    }
    default List<RegularInspectionDO> selectList(RegularInspectionListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<RegularInspectionDO>()
            .eqIfPresent(RegularInspectionDO::getInfectionId, reqVO.getInfectionId())
            .eqIfPresent(RegularInspectionDO::getJgrybm, reqVO.getJgrybm())
            .betweenIfPresent(RegularInspectionDO::getCheckDate, reqVO.getCheckDate())
            .eqIfPresent(RegularInspectionDO::getCd4tlbz, reqVO.getCd4tlbz())
            .eqIfPresent(RegularInspectionDO::getViralLoadValue, reqVO.getViralLoadValue())
            .eqIfPresent(RegularInspectionDO::getManagementSituation, reqVO.getManagementSituation())
            .eqIfPresent(RegularInspectionDO::getControlPersonnel, reqVO.getControlPersonnel())
            .eqIfPresent(RegularInspectionDO::getAttUrl, reqVO.getAttUrl())
            .eqIfPresent(RegularInspectionDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(RegularInspectionDO::getOperatePolice, reqVO.getOperatePolice())
            .betweenIfPresent(RegularInspectionDO::getOperateTime, reqVO.getOperateTime())
        .orderByDesc(RegularInspectionDO::getAddTime));    }


    }
