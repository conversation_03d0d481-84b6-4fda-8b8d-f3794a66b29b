package com.rs.module.ihc.controller.admin.pm.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 大药房管理-药品报损列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PharmacyMedicineLossListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("药品id，对应ichs_medicine.id")
    private String medicineId;

    @ApiModelProperty("入库批次id，对应ichs_medicine_in.id")
    private String medicineInId;

    @ApiModelProperty("报损原因")
    private String lossReason;

    @ApiModelProperty("报损数量")
    private BigDecimal lossNum;

    @ApiModelProperty("报损时间")
    private Date[] lossTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("批次库存数量 最小单位")
    private BigDecimal inventoryNum;

    @ApiModelProperty("当前药品总库存数量 最小单位")
    private BigDecimal totalInventoryNum;

}
