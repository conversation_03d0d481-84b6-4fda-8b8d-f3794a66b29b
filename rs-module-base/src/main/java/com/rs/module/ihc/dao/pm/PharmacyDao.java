package com.rs.module.ihc.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.pm.PharmacyDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 药房管理-药房信息 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PharmacyDao extends IBaseDao<PharmacyDO> {


    default PageResult<PharmacyDO> selectPage(PharmacyPageReqVO reqVO) {
        Page<PharmacyDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<PharmacyDO> pharmacyPage = selectPage(page, new LambdaQueryWrapperX<PharmacyDO>()
            .likeIfPresent(PharmacyDO::getPharmacyName, reqVO.getPharmacyName())
            .eqIfPresent(PharmacyDO::getPrisonId, reqVO.getPrisonId())
            .orderByDesc(PharmacyDO::getId));
            return new PageResult<>(pharmacyPage.getRecords(), pharmacyPage.getTotal());
    }
    default List<PharmacyDO> selectList(PharmacyListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PharmacyDO>()
            .likeIfPresent(PharmacyDO::getPharmacyName, reqVO.getPharmacyName())
            .eqIfPresent(PharmacyDO::getPrisonId, reqVO.getPrisonId())
        .orderByDesc(PharmacyDO::getId));    }


    }
