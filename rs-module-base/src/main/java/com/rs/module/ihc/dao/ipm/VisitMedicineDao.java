package com.rs.module.ihc.dao.ipm;

import java.util.*;

import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.ihc.entity.ipm.VisitMedicineDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 所内就医-现场巡诊-现场司药信息
 *
 * <AUTHOR>
 */
@Mapper
public interface VisitMedicineDao extends IBaseDao<VisitMedicineDO> {

    default List<VisitMedicineDO> selectListByVisitId(String visitId) {
        return selectList(new LambdaQueryWrapperX<VisitMedicineDO>().eq(VisitMedicineDO::getVisitId, visitId));
    }

    default int deleteByVisitId(String visitId) {
        return delete(new LambdaQueryWrapperX<VisitMedicineDO>().eq(VisitMedicineDO::getVisitId, visitId));
    }

}
