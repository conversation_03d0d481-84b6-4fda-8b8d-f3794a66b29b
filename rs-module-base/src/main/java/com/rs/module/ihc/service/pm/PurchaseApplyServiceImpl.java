package com.rs.module.ihc.service.pm;

import cn.hutool.core.bean.copier.CopyOptions;
import com.rs.module.ihc.entity.pm.MedicineDO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.PurchaseApplyDO;
import com.rs.module.ihc.entity.pm.PurchaseApplyRelDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.pm.PurchaseApplyDao;
import com.rs.module.ihc.dao.pm.PurchaseApplyRelDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 药品管理-药品采购申请 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PurchaseApplyServiceImpl extends BaseServiceImpl<PurchaseApplyDao, PurchaseApplyDO> implements PurchaseApplyService {

    @Resource
    private PurchaseApplyDao purchaseApplyDao;
    @Resource
    private PurchaseApplyRelDao purchaseApplyRelDao;
    @Resource
    private MedicineService medicineService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createPurchaseApply(PurchaseApplySaveReqVO createReqVO) {
        // 插入
        PurchaseApplyDO purchaseApply = BeanUtils.toBean(createReqVO, PurchaseApplyDO.class);
        purchaseApplyDao.insert(purchaseApply);

        // 插入子表
        createPurchaseApplyRelList(purchaseApply.getId(), createReqVO.getPurchaseApplyRels());
        // 返回
        return purchaseApply.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePurchaseApply(PurchaseApplySaveReqVO updateReqVO) {
        // 校验存在
        validatePurchaseApplyExists(updateReqVO.getId());
        // 更新
        PurchaseApplyDO updateObj = BeanUtils.toBean(updateReqVO, PurchaseApplyDO.class);
        purchaseApplyDao.updateById(updateObj);

        // 更新子表
        updatePurchaseApplyRelList(updateReqVO.getId(), updateReqVO.getPurchaseApplyRels());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePurchaseApply(String id) {
        // 校验存在
        validatePurchaseApplyExists(id);
        // 删除
        purchaseApplyDao.deleteById(id);

        // 删除子表
        deletePurchaseApplyRelByMlh(id);
    }

    private void validatePurchaseApplyExists(String id) {
        if (purchaseApplyDao.selectById(id) == null) {
            throw new ServerException("药品管理-药品采购申请数据不存在");
        }
    }

    @Override
    public PurchaseApplyRespVO getPurchaseApply(String id) {
        PurchaseApplyRespVO purchaseApplyRespVO = BeanUtils.toBean(purchaseApplyDao.selectById(id), PurchaseApplyRespVO.class);
        purchaseApplyRespVO.setPurchaseApplyRelList(getPurchaseApplyRelListByMlh(purchaseApplyRespVO.getId()));
        return purchaseApplyRespVO;
    }

    @Override
    public PageResult<PurchaseApplyDO> getPurchaseApplyPage(PurchaseApplyPageReqVO pageReqVO) {
        return purchaseApplyDao.selectPage(pageReqVO);
    }

    @Override
    public List<PurchaseApplyDO> getPurchaseApplyList(PurchaseApplyListReqVO listReqVO) {
        return purchaseApplyDao.selectList(listReqVO);
    }


    // ==================== 子表（药品管理-药品采购申请-药品信息关联） ====================

    @Override
    public List<PurchaseApplyRelRespVO> getPurchaseApplyRelListByMlh(String mlh) {
        List<PurchaseApplyRelDO> purchaseApplyRelDOList = purchaseApplyRelDao.selectListByMlh(mlh);
        List<PurchaseApplyRelRespVO> purchaseApplyRelRespVOList = BeanUtils.toBean(purchaseApplyRelDOList, PurchaseApplyRelRespVO.class);
        purchaseApplyRelRespVOList.forEach(purchaseApplyRelRespVO -> {
            MedicineDO medicine = medicineService.getMedicine(purchaseApplyRelRespVO.getMedicineId());
            CopyOptions copyOptions = CopyOptions.create();
            copyOptions.setIgnoreNullValue(true);
            copyOptions.setIgnoreProperties("id");
            BeanUtils.copyProperties(medicine, purchaseApplyRelRespVO);
        });
        return purchaseApplyRelRespVOList;
    }

    @Override
    public PageResult<PurchaseApplyDO> selectByProCode(PurchaseApplyPageReqVO reqVO) {
        return purchaseApplyDao.selectByProCode(reqVO);
    }


    private void createPurchaseApplyRelList(String mlh, List<PurchaseApplyRelDO> list) {
        list.forEach(o -> o.setMlh(mlh));
        list.forEach(o -> purchaseApplyRelDao.insert(o));
    }

    private void updatePurchaseApplyRelList(String mlh, List<PurchaseApplyRelDO> list) {
        deletePurchaseApplyRelByMlh(mlh);
		list.forEach(o -> o.setId(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createPurchaseApplyRelList(mlh, list);
    }

    private void deletePurchaseApplyRelByMlh(String mlh) {
        purchaseApplyRelDao.deleteByMlh(mlh);
    }

}
