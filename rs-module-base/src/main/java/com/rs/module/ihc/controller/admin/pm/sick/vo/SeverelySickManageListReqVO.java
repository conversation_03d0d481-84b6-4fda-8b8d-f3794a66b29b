package com.rs.module.ihc.controller.admin.pm.sick.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 重特病号管理列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SeverelySickManageListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("所属监所id")
    private String prisonId;

    @ApiModelProperty("被监管人id")
    private String jgrybm;

    @ApiModelProperty("业务状态 字典值：severely_sick_business_status")
    private String businessStatus;

    @ApiModelProperty("病号类别 字典值：severely_sick_type")
    private String severelySickType;

    @ApiModelProperty("病情情况")
    private String patientSituation;

    @ApiModelProperty("治疗情况")
    private String treatmentSituation;

    @ApiModelProperty("申请人id")
    private String applyUserId;

    @ApiModelProperty("申请人姓名")
    private String applyUserName;

    @ApiModelProperty("申请时间")
    private Date[] applyTime;

    @ApiModelProperty("当前是否是重病号 字典值：boolean_type")
    private String isSeverelySick;

    @ApiModelProperty("对应的最新的一条解除申请id")
    private String relieveApplyId;

    @ApiModelProperty("解除申请审核结果 字典值：is_agree")
    private String relieveApproveResult;

    @ApiModelProperty("解除时间")
    private Date[] relieveTime;

}
