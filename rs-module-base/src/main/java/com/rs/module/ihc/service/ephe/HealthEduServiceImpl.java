package com.rs.module.ihc.service.ephe;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.ephe.vo.HealthEduSaveReqVO;
import com.rs.module.ihc.dao.ephe.HealthEduDao;
import com.rs.module.ihc.entity.ephe.HealthEduDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;


/**
 * 卫生防疫与健康教育-健康教育 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HealthEduServiceImpl extends BaseServiceImpl<HealthEduDao, HealthEduDO> implements HealthEduService {

    @Resource
    private HealthEduDao healthEduDao;

    @Override
    public String createHealthEdu(HealthEduSaveReqVO createReqVO) {
        // 插入
        HealthEduDO healthEdu = BeanUtils.toBean(createReqVO, HealthEduDO.class);
        SessionUser user = SessionUserUtil.getSessionUser();
        healthEdu.setOperatePoliceSfzh(user.getIdCard());
        healthEdu.setOperatePolice(user.getName());
        healthEdu.setOperateTime(new Date());
        healthEduDao.insert(healthEdu);
        return healthEdu.getId();
    }

    @Override
    public HealthEduDO getHealthEdu(String id) {
        return healthEduDao.selectById(id);
    }


}
