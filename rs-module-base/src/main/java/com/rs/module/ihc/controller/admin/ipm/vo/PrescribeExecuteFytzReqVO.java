package com.rs.module.ihc.controller.admin.ipm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 所内就医-医嘱/处方执行记录列表 Request VO")
@Data
public class PrescribeExecuteFytzReqVO extends DateRangVO{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("服药状态 0=未服药 1=已服药 2=拒绝服药 字典：dose_status")
    private String doseStatus;

    @ApiModelProperty("被监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监所id")
    private String prisonerId;

    @ApiModelProperty("1查询未签的风险告知书，")
    private String fygzs;


}
