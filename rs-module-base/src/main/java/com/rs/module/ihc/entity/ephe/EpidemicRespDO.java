package com.rs.module.ihc.entity.ephe;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 卫生防疫与健康教育-疫情处置 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_ephe_epidemic_resp")
@KeySequence("ihc_ephe_epidemic_resp_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ihc_ephe_epidemic_resp")
public class EpidemicRespDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 是否出现聚集性疫情
     */
    private String sfcxzjxyq;
    /**
     * 出现聚集性疫情日期
     */
    private Date cxzjxyqrq;
    /**
     * 聚集性疫情处置情况
     */
    private String zjxyqczqk;
    /**
     * 报告方式 字典：ZD_CYB_JHBSCBGFS 
     */
    private String bgfs;
    /**
     * 传染病报告卡填报日期
     */
    private Date crbbgktbrq;
    /**
     * 传染病报告卡
     */
    private String crbbgk;
    /**
     * 登记民警身份证号
     */
    private String operatePoliceSfzh;
    /**
     * 登记民警
     */
    private String operatePolice;
    /**
     * 登记时间
     */
    private Date operateTime;

}
