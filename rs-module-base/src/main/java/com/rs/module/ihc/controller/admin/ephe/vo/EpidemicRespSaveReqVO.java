package com.rs.module.ihc.controller.admin.ephe.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 卫生防疫与健康教育-疫情处置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EpidemicRespSaveReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;


    @ApiModelProperty("是否出现聚集性疫情")
    private String sfcxzjxyq;

    @ApiModelProperty("出现聚集性疫情日期")
    private Date cxzjxyqrq;

    @ApiModelProperty("聚集性疫情处置情况")
    private String zjxyqczqk;

    @ApiModelProperty("报告方式 字典：ZD_CYB_JHBSCBGFS ")
    private String bgfs;

    @ApiModelProperty("传染病报告卡填报日期")
    private Date crbbgktbrq;

    @ApiModelProperty("传染病报告卡")
    private String crbbgk;

    //@ApiModelProperty("登记民警身份证号")
    //@NotEmpty(message = "登记民警身份证号不能为空")
    //private String operatePoliceSfzh;
    //
    //@ApiModelProperty("登记民警")
    //@NotEmpty(message = "登记民警不能为空")
    //private String operatePolice;
    //
    //@ApiModelProperty("登记时间")
    //@NotNull(message = "登记时间不能为空")
    //private Date operateTime;

}
