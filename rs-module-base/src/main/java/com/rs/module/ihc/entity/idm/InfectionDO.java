package com.rs.module.ihc.entity.idm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 传染病管理-传染病管理登记 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_idm_infection")
@KeySequence("ihc_idm_infection_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ihc_idm_infection")
public class InfectionDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 传染病类型
     */
    private String infectiousDiseaseType;
    /**
     * 艾滋病-初筛时间
     */
    private Date aidsFirstScreeningTime;
    /**
     * 艾滋病-初筛操作方式 字典：ZD_CYB_CSCZFS
     */
    private String aidsFirstScreeningMethod;
    /**
     * 艾滋病-初筛结果 字典：ZD_CYB_CSJG_YXYX
     */
    private String aidsFirstScreeningResult;
    /**
     * 艾滋病-确诊日期
     */
    private Date aidsDiagnosisDate;
    /**
     * 艾滋病-实验室反馈时间
     */
    private Date aidsLabFeedbackTime;
    /**
     * 艾滋病-实验室反馈结果 字典：ZD_CYB_CSJG_YXYX
     */
    private String aidsLabFeedbackResult;
    /**
     * 艾滋病-告知在押人员时间
     */
    private Date aidsInformInmateTime;
    /**
     * 艾滋病-是否告知家属
     */
    private String aidsIsInformFamily;
    /**
     * 艾滋病-告知家属时间
     */
    private Date aidsInformFamilyTime;
    /**
     * 告知疾控机构日期
     */
    private Date gzjkjgrq;
    /**
     * 疾控机构名称
     */
    private String jkjgrqmc;
    /**
     * 是否接收转入疾控机构名称反馈信息
     */
    private String sfjszrjkjgfkxx;
    /**
     * 接收转入疾控机构反馈信息日期
     */
    private Date jszrjkjgfkxxrq;
    /**
     * 是否需要再次联系转入疾控机构
     */
    private String sfxyzclxzrjkjg;
    /**
     * 再次联系转入疾控机构日期
     */
    private Date zclxzrjkjgrq;
    /**
     * 送交人
     */
    private String submitter;
    /**
     * 附件地址
     */
    private String attUrl;
    /**
     * 登记民警身份证号
     */
    private String operatePoliceSfzh;
    /**
     * 登记民警
     */
    private String operatePolice;
    /**
     * 登记时间
     */
    private Date operateTime;

    /**
     * 健康体检时间
     */
    private Date checkupTime;

    /**
     * 健康体检方式：ZD_CYB_JKTJFS
     */
    private String checkupMethod;

    /**
     * 健康体检检查方式：ZD_CYB_CSJG_YXYX
     */
    private String checkupResult;

    /**
     * 健康体检、检查人
     */
    private String checkupExaminer;


}
