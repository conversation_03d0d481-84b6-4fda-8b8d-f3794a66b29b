package com.rs.module.ihc.service.remote;


import com.rs.module.ihc.controller.admin.ipm.vo.InternalMedicalRemotePrescribeVO;
import com.rs.module.ihc.entity.ipm.appointment.IhcsInternalMedicalAppointmentDO;

public interface IhcsInternalMedicalRemoteService {


    /**
     * 远程问诊开具处方
     *
     * @param internalMedicalRemotePrescribeDTO 处方信息
     */
    void internalMedicalRemotePrescribe(InternalMedicalRemotePrescribeVO internalMedicalRemotePrescribeDTO);


    public IhcsInternalMedicalAppointmentDO getIhcsInternalMedicalAppointmentById(String id);

}
