package com.rs.module.ihc.dao.ephe;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.ephe.HealthEduDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.ephe.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 卫生防疫与健康教育-健康教育 Dao
*
* <AUTHOR>
*/
@Mapper
public interface HealthEduDao extends IBaseDao<HealthEduDO> {


    default PageResult<HealthEduDO> selectPage(HealthEduPageReqVO reqVO) {
        Page<HealthEduDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<HealthEduDO> wrapper = new LambdaQueryWrapperX<HealthEduDO>()
            .eqIfPresent(HealthEduDO::getXjkc, reqVO.getXjkc())
            .eqIfPresent(HealthEduDO::getJkjyqk, reqVO.getJkjyqk())
            .eqIfPresent(HealthEduDO::getCjfw, reqVO.getCjfw())
            .eqIfPresent(HealthEduDO::getXjsdKssj, reqVO.getXjsdKssj())
            .eqIfPresent(HealthEduDO::getXjsdJssj, reqVO.getXjsdJssj())
            .eqIfPresent(HealthEduDO::getXjrxm, reqVO.getXjrxm())
            .eqIfPresent(HealthEduDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(HealthEduDO::getOperatePolice, reqVO.getOperatePolice())
            .betweenIfPresent(HealthEduDO::getOperateTime, reqVO.getOperateTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(HealthEduDO::getAddTime);
        }
        Page<HealthEduDO> healthEduPage = selectPage(page, wrapper);
        return new PageResult<>(healthEduPage.getRecords(), healthEduPage.getTotal());
    }
    default List<HealthEduDO> selectList(HealthEduListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<HealthEduDO>()
            .eqIfPresent(HealthEduDO::getXjkc, reqVO.getXjkc())
            .eqIfPresent(HealthEduDO::getJkjyqk, reqVO.getJkjyqk())
            .eqIfPresent(HealthEduDO::getCjfw, reqVO.getCjfw())
            .eqIfPresent(HealthEduDO::getXjsdKssj, reqVO.getXjsdKssj())
            .eqIfPresent(HealthEduDO::getXjsdJssj, reqVO.getXjsdJssj())
            .eqIfPresent(HealthEduDO::getXjrxm, reqVO.getXjrxm())
            .eqIfPresent(HealthEduDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(HealthEduDO::getOperatePolice, reqVO.getOperatePolice())
            .betweenIfPresent(HealthEduDO::getOperateTime, reqVO.getOperateTime())
        .orderByDesc(HealthEduDO::getAddTime));    }


    }
