package com.rs.module.ihc.dao.injury;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.injury.InjuryRegistrationDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.injury.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 医疗子系统-伤亡登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface InjuryRegistrationDao extends IBaseDao<InjuryRegistrationDO> {


    default PageResult<InjuryRegistrationDO> selectPage(InjuryRegistrationPageReqVO reqVO) {
        Page<InjuryRegistrationDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<InjuryRegistrationDO> wrapper = new LambdaQueryWrapperX<InjuryRegistrationDO>()
            .eqIfPresent(InjuryRegistrationDO::getInjuryDetails, reqVO.getInjuryDetails())
            .eqIfPresent(InjuryRegistrationDO::getRecordingMethod, reqVO.getRecordingMethod())
            .betweenIfPresent(InjuryRegistrationDO::getInjuryDate, reqVO.getInjuryDate())
            .eqIfPresent(InjuryRegistrationDO::getInjuryCause, reqVO.getInjuryCause())
            .eqIfPresent(InjuryRegistrationDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(InjuryRegistrationDO::getOperatePolice, reqVO.getOperatePolice())
            .betweenIfPresent(InjuryRegistrationDO::getOperateTime, reqVO.getOperateTime())
            .eqIfPresent(InjuryRegistrationDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(InjuryRegistrationDO::getJgryxm, reqVO.getJgryxm())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(InjuryRegistrationDO::getAddTime);
        }
        Page<InjuryRegistrationDO> injuryRegistrationPage = selectPage(page, wrapper);
        return new PageResult<>(injuryRegistrationPage.getRecords(), injuryRegistrationPage.getTotal());
    }
    default List<InjuryRegistrationDO> selectList(InjuryRegistrationListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<InjuryRegistrationDO>()
            .eqIfPresent(InjuryRegistrationDO::getInjuryDetails, reqVO.getInjuryDetails())
            .eqIfPresent(InjuryRegistrationDO::getRecordingMethod, reqVO.getRecordingMethod())
            .betweenIfPresent(InjuryRegistrationDO::getInjuryDate, reqVO.getInjuryDate())
            .eqIfPresent(InjuryRegistrationDO::getInjuryCause, reqVO.getInjuryCause())
            .eqIfPresent(InjuryRegistrationDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(InjuryRegistrationDO::getOperatePolice, reqVO.getOperatePolice())
            .betweenIfPresent(InjuryRegistrationDO::getOperateTime, reqVO.getOperateTime())
            .eqIfPresent(InjuryRegistrationDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(InjuryRegistrationDO::getJgryxm, reqVO.getJgryxm())
        .orderByDesc(InjuryRegistrationDO::getAddTime));    }


    }
