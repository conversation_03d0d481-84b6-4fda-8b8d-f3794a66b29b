package com.rs.module.ihc.service.ipm.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class PrescribeClinicalRegisterDTO {

    /**
     * 处方id
     */
    @NotNull(message = "处方id不能为空")
    @ApiModelProperty(value = "处方id", required = true)
    private String prescribeId;

    /**
     * 医务民警id
     */
    @NotNull(message = "医务民警id不能为空")
    @ApiModelProperty(value = "医务民警id", required = true)
    private String medicalPoliceId;

    /**
     * 医务民警名称
     */
    @NotBlank(message = "医务民警名称不能为空")
    @ApiModelProperty(value = "医务民警名称", required = true)
    private String medicalPoliceName;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

}
