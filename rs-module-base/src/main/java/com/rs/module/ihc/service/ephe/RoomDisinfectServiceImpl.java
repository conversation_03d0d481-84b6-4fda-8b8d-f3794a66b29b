package com.rs.module.ihc.service.ephe;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.ephe.vo.RoomDisinfectRespVO;
import com.rs.module.ihc.controller.admin.ephe.vo.RoomDisinfectSaveReqVO;
import com.rs.module.ihc.dao.ephe.RoomDisinfectDao;
import com.rs.module.ihc.entity.ephe.RoomDisinfectDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 卫生防疫与健康教育--监室消毒 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RoomDisinfectServiceImpl extends BaseServiceImpl<RoomDisinfectDao, RoomDisinfectDO> implements RoomDisinfectService {

    @Resource
    private RoomDisinfectDao roomDisinfectDao;

    @Override
    public String createRoomDisinfect(RoomDisinfectSaveReqVO createReqVO) {
        // 插入
        RoomDisinfectDO roomDisinfect = BeanUtils.toBean(createReqVO, RoomDisinfectDO.class);
        roomDisinfect.setDisinfectPolice(SessionUserUtil.getSessionUser().getName());
        roomDisinfect.setDisinfectPoliceSfzh(SessionUserUtil.getSessionUser().getIdCard());
        roomDisinfectDao.insert(roomDisinfect);
        // 返回
        return roomDisinfect.getId();
    }

    @Override
    public void updateRoomDisinfect(RoomDisinfectSaveReqVO updateReqVO) {
        // 校验存在
        validateRoomDisinfectExists(updateReqVO.getId());
        // 更新
        RoomDisinfectDO updateObj = BeanUtils.toBean(updateReqVO, RoomDisinfectDO.class);
        updateObj.setDisinfectPolice(SessionUserUtil.getSessionUser().getName());
        updateObj.setDisinfectPoliceSfzh(SessionUserUtil.getSessionUser().getIdCard());
        roomDisinfectDao.updateById(updateObj);
    }

    @Override
    public void deleteRoomDisinfect(String id) {
        // 校验存在
        validateRoomDisinfectExists(id);
        // 删除
        roomDisinfectDao.deleteById(id);
    }

    private void validateRoomDisinfectExists(String id) {
        if (roomDisinfectDao.selectById(id) == null) {
            throw new ServerException("卫生防疫与健康教育--监室消毒数据不存在");
        }
    }

    @Override
    public RoomDisinfectDO getRoomDisinfect(String id) {
        return roomDisinfectDao.selectById(id);
    }

    @Override
    public List<RoomDisinfectRespVO> listRoomDisinfect(String timeType, String roomId, String orgCode) {
        Map<String, Date> commonAppRecordPeriod = getCommonAppRecordPeriod(timeType);
        LambdaQueryWrapper<RoomDisinfectDO> query = Wrappers.lambdaQuery(RoomDisinfectDO.class);
        query.eq(RoomDisinfectDO::getDataSources, "2")
                .eq(RoomDisinfectDO::getRoomId, roomId)
                .eq(RoomDisinfectDO::getOrgCode, orgCode)
                .between(commonAppRecordPeriod.get("startTime") != null, RoomDisinfectDO::getDisinfectTime,
                        commonAppRecordPeriod.get("startTime"), commonAppRecordPeriod.get("endTime"));
        List<RoomDisinfectDO> roomDisinfectDOS = roomDisinfectDao.selectList(query);
        if (CollUtil.isNotEmpty(roomDisinfectDOS)) {
            return BeanUtils.toBean(roomDisinfectDOS, RoomDisinfectRespVO.class);
        }
        return Collections.emptyList();
    }

    /**
     * 根据CommonAppRecordPeriodTypeEnum枚举类型获取 1 全部，2 今天，3 昨天，4 近一周 的开始时间和结束时间
     *
     * @param type
     * @return
     */
    public static Map<String, Date> getCommonAppRecordPeriod(String type) {
        //类型 1 全部，2 今天，3 昨天，4 近一周
        Date startTime = null;
        Date endTime = null;
        if ("2".equals(type)) {
            LocalDate startLocal = LocalDate.now(); // 获取当前日期 yyyy-MM-dd
            LocalDate endLocal = LocalDate.now().plusDays(1L);
            startTime = Date.from(startLocal.atStartOfDay(ZoneId.systemDefault()).toInstant());
            endTime = Date.from(endLocal.atStartOfDay(ZoneId.systemDefault()).toInstant());
        } else if ("3".equals(type)) {
            LocalDate startLocal = LocalDate.now().plusDays(-1L);
            LocalDate endLocal = LocalDate.now();
            startTime = Date.from(startLocal.atStartOfDay(ZoneId.systemDefault()).toInstant());
            endTime = Date.from(endLocal.atStartOfDay(ZoneId.systemDefault()).toInstant());
        } else if ("4".equals(type)) {
            LocalDate startLocal = LocalDate.now().plusDays(-6L);
            LocalDate endLocal = LocalDate.now().plusDays(1L);
            startTime = Date.from(startLocal.atStartOfDay(ZoneId.systemDefault()).toInstant());
            endTime = Date.from(endLocal.atStartOfDay(ZoneId.systemDefault()).toInstant());
        }
        Map<String, Date> result = new HashMap<>(2);
        result.put("startTime", startTime);
        result.put("endTime", endTime);
        return result;
    }

}
