package com.rs.module.ihc.service.pm;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.common.util.spring.SpringUtils;
import com.rs.framework.mybatis.util.BizAssert;
import com.rs.module.ihc.constant.InStorageMethodConstant;
import com.rs.module.ihc.constant.OutStorageReasonConstant;
import com.rs.module.ihc.constant.TransferMethodConstant;
import com.rs.module.ihc.controller.admin.ipm.bo.UpdateMedicineInInventoryNumBO;
import com.rs.module.ihc.controller.admin.ipm.dto.SaveMedicineOutDTO;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.dao.pm.PharmacyMedicineDao;
import com.rs.module.ihc.dao.pm.PharmacyMedicineOutDao;
import com.rs.module.ihc.entity.pm.MedicineDO;
import com.rs.module.ihc.entity.pm.PharmacyMedicineDO;
import com.rs.module.ihc.entity.pm.PharmacyMedicineInDO;
import com.rs.module.ihc.entity.pm.PharmacyMedicineOutDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 大药房管理-药品出库 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PharmacyMedicineOutServiceImpl extends BaseServiceImpl<PharmacyMedicineOutDao, PharmacyMedicineOutDO> implements PharmacyMedicineOutService {

    @Resource
    private PharmacyMedicineOutDao pharmacyMedicineOutDao;

    @Resource
    private PharmacyMedicineService pharmacyMedicineService;

    @Resource
    private PharmacyMedicineDao pharmacyMedicineDao;

    @Resource
    private PharmacyMedicineInService pharmacyMedicineInService;

    @Resource
    private MedicineService medicineService;

    @Resource
    private MedicineInService medicineInService;

    @Override
    public String createPharmacyMedicineOut(PharmacyMedicineOutSaveReqVO createReqVO) {
        // 插入
        PharmacyMedicineOutDO pharmacyMedicineOut = BeanUtils.toBean(createReqVO, PharmacyMedicineOutDO.class);
        pharmacyMedicineOutDao.insert(pharmacyMedicineOut);
        // 返回
        return pharmacyMedicineOut.getId();
    }

    @Override
    public void updatePharmacyMedicineOut(PharmacyMedicineOutSaveReqVO updateReqVO) {
        // 校验存在
        validatePharmacyMedicineOutExists(updateReqVO.getId());
        // 更新
        PharmacyMedicineOutDO updateObj = BeanUtils.toBean(updateReqVO, PharmacyMedicineOutDO.class);
        pharmacyMedicineOutDao.updateById(updateObj);
    }

    @Override
    public void deletePharmacyMedicineOut(String id) {
        // 校验存在
        validatePharmacyMedicineOutExists(id);
        // 删除
        pharmacyMedicineOutDao.deleteById(id);
    }

    private void validatePharmacyMedicineOutExists(String id) {
        if (pharmacyMedicineOutDao.selectById(id) == null) {
            throw new ServerException("大药房管理-药品出库数据不存在");
        }
    }

    @Override
    public PharmacyMedicineOutDO getPharmacyMedicineOut(String id) {
        return pharmacyMedicineOutDao.selectById(id);
    }

    @Override
    public PageResult<PharmacyMedicineOutDO> getPharmacyMedicineOutPage(PharmacyMedicineOutPageReqVO pageReqVO) {
        return pharmacyMedicineOutDao.selectPage(pageReqVO);
    }

    @Override
    public List<PharmacyMedicineOutDO> getPharmacyMedicineOutList(PharmacyMedicineOutListReqVO listReqVO) {
        return pharmacyMedicineOutDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveMedicineOut(List<SaveMedicineOutDTO> medicineOutList, String medicineId) {
        List<PharmacyMedicineInDO> ihcsMedicineIns = pharmacyMedicineInService.lambdaQuery()
                .in(PharmacyMedicineInDO::getId, medicineOutList.stream()
                        .map(SaveMedicineOutDTO::getMedicineInId)
                        .collect(Collectors.toList()))
                .list();
        PharmacyMedicineOutService pharmacyMedicineOutService =  SpringUtils.getBean(PharmacyMedicineOutService.class);
        pharmacyMedicineOutService.batchSaveMedicineOutBySourceData(medicineOutList, medicineId, ihcsMedicineIns);
        //调拨小药房
        handleTransfer(medicineOutList, medicineId);
    }




    /**
     * 处理调拨出库数据
     */
    private void handleTransfer(List<SaveMedicineOutDTO> medicineOutList, String pharmacyMedicineId) {
        // 获取调拨出库的入库批次id
        List<String> medicineInIds = new ArrayList<>();
        List<SaveMedicineOutDTO> transferMedicineOuts = medicineOutList.stream()
                .filter(dto ->
                        Objects.equals(dto.getOutStorageReason(), OutStorageReasonConstant.TRANSFER))
                .peek(dto -> medicineInIds.add(dto.getMedicineInId()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(transferMedicineOuts)) {
            return;
        }
        // 先去找当前药的信息
        PharmacyMedicineDO pharmacyMedicine = pharmacyMedicineDao.selectById(pharmacyMedicineId);
        BizAssert.notNull(pharmacyMedicine, "当前药品信息不存在");
        MedicineDO onlyMedicine = medicineService.getOnlyMedicine(pharmacyMedicine.getMedicineName(), pharmacyMedicine.getSpecs(), pharmacyMedicine.getPrisonId());
        String medicineId;
        // 药品不存在先去添加药品
        if (Objects.isNull(onlyMedicine)) {
            MedicineSaveReqVO saveMedicineDTO = new MedicineSaveReqVO();
            BeanUtils.copyProperties(pharmacyMedicine, saveMedicineDTO);
            medicineId = medicineService.createMedicine(saveMedicineDTO);
        } else {
            medicineId = onlyMedicine.getId();
        }
        // 查询入库批次信息
        Map<String, PharmacyMedicineInDO> pharmacyMedicineInMap = pharmacyMedicineInService.lambdaQuery()
                .in(PharmacyMedicineInDO::getId, medicineInIds)
                .list()
                .stream()
                .collect(Collectors.toMap(PharmacyMedicineInDO::getId, Function.identity()));
        List<SaveMedicineInReqVO> saveMedicineInDTOList = transferMedicineOuts.stream()
                .map(outDto -> {
                    PharmacyMedicineInDO pharmacyMedicineIn = pharmacyMedicineInMap.get(outDto.getMedicineInId());
                    BizAssert.notNull(pharmacyMedicineIn, "入库批次id[" + outDto.getMedicineInId() + "]不存在");
                    return SaveMedicineInReqVO.builder()
                            .inNum(outDto.getOutNum())
                            .supplierId(pharmacyMedicineIn.getSupplierId())
                            .supplierName(pharmacyMedicineIn.getSupplierName())
                            .voucherCode(pharmacyMedicineIn.getVoucherCode())
                            .purchasePrice(pharmacyMedicineIn.getPurchasePrice())
                            .wholesalePrice(pharmacyMedicineIn.getWholesalePrice())
                            .transferPrice(pharmacyMedicineIn.getTransferPrice())
                            .retailPrice(pharmacyMedicineIn.getRetailPrice())
                            .inDate(new Date())
                            .expireDate(pharmacyMedicineIn.getExpireDate())
                            .inStorageMethod(InStorageMethodConstant.TRANSFER_STORE)
                            .transferMethod(TransferMethodConstant.PHARMACY)
                            .medicinePlace(pharmacyMedicineIn.getMedicinePlace())
                            .build();
                }).collect(Collectors.toList());
        // 添加批次，忽略审批
        medicineInService.batchSaveMedicineIn(saveMedicineInDTOList, medicineId, true);
    }









    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveMedicineOutBySourceData(List<SaveMedicineOutDTO> medicineOutList, String medicineId, List<PharmacyMedicineInDO> dataSourceIhcsMedicineIn) {
        SessionUser user = SessionUserUtil.getSessionUser();
        PharmacyMedicineDO ihcsMedicine = pharmacyMedicineService.getBaseMapper().selectById(medicineId);
        BizAssert.notNull(ihcsMedicine, String.format("药品不存在，id：%s", medicineId));
        Map<String, PharmacyMedicineInDO> ihcsMedicineInMap = dataSourceIhcsMedicineIn.stream()
                .collect(Collectors.toMap(PharmacyMedicineInDO::getId, Function.identity()));

        // 药品库存数量
        final BigDecimal sourceInventoryNum = ihcsMedicine.getTotalInventoryNum();
        BigDecimal targetInventoryNum = sourceInventoryNum;
        // 记录每个批次的扣减记录
        Map<String, UpdateMedicineInInventoryNumBO> batchInventoryNumMap = new LinkedHashMap<>();
        List<PharmacyMedicineOutDO> ihcsMedicineOutList = new ArrayList<>();
        Date nowDate = new Date();
        for (SaveMedicineOutDTO saveMedicineOutDTO : medicineOutList) {
            String medicineInId = saveMedicineOutDTO.getMedicineInId();
            PharmacyMedicineInDO ihcsMedicineIn = ihcsMedicineInMap.get(medicineInId);
            BizAssert.notNull(ihcsMedicineIn, "入库批次id[" + medicineInId + "]不存在");
            String batchCode = ihcsMedicineIn.getBatchCode();
            BizAssert.isTrue(Objects.equals(ihcsMedicineIn.getMedicineId(), medicineId),
                    "入库批次[" + batchCode + "]不属于该药品");
            long l = DateUtil.betweenDay(nowDate, ihcsMedicineIn.getExpireDate(), true);
            BizAssert.isTrue(l >= 0, "入库批次[" + batchCode + "]已过期，无法出库");
            // 出库数量
            BigDecimal outNum = saveMedicineOutDTO.getOutNum();
            // 批次库存数量
            BigDecimal batchSourceInventoryNum = ihcsMedicineIn.getInventoryNum();
            // 药品总库存扣减
            targetInventoryNum = targetInventoryNum.subtract(outNum);
            // 这里是由于可能出现批量出库时，一个批次在一次请求中分两个记录来出
            UpdateMedicineInInventoryNumBO updateMedicineInInventoryNumBO =
                    batchInventoryNumMap.computeIfAbsent(medicineInId, key -> UpdateMedicineInInventoryNumBO.builder()
                            .id(medicineInId)
                            .sourceInventoryNum(batchSourceInventoryNum)
                            .targetInventoryNum(batchSourceInventoryNum)
                            .build());
            // 批次库存数量扣减
            BigDecimal batchTargetInventoryNum = updateMedicineInInventoryNumBO.getTargetInventoryNum().subtract(outNum);
            BizAssert.isTrue(batchTargetInventoryNum.signum() >= 0, "批次[" + batchCode + "]出库数量不能大于库存数量");
            updateMedicineInInventoryNumBO.setTargetInventoryNum(batchTargetInventoryNum);
            // 记录存库
            PharmacyMedicineOutDO ihcsMedicineOut = this.convertToIhcsMedicineOut(saveMedicineOutDTO);
            ihcsMedicineOut.setMedicineId(medicineId);
            ihcsMedicineOut.setInventoryNum(batchTargetInventoryNum);
            ihcsMedicineOut.setTotalInventoryNum(targetInventoryNum);
            ihcsMedicineOut.setUserid(user.getIdCard());
            ihcsMedicineOut.setUserName(user.getName());
            ihcsMedicineOutList.add(ihcsMedicineOut);
        }
        // 批次库存更新
        medicineInService.batchCompareAndSetInventoryNum(batchInventoryNumMap.values());
        // 药品库存更新
        pharmacyMedicineDao.compareAndSetMedicineInventoryNum(medicineId, sourceInventoryNum, targetInventoryNum);

        // 出库记录保存
        this.saveBatch(ihcsMedicineOutList);
    }


    private PharmacyMedicineOutDO convertToIhcsMedicineOut(SaveMedicineOutDTO saveMedicineOutDTO) {
        return PharmacyMedicineOutDO.builder()
                .medicineInId(saveMedicineOutDTO.getMedicineInId())
                .outStorageReason(saveMedicineOutDTO.getOutStorageReason())
                .outNum(saveMedicineOutDTO.getOutNum())
                .dispensaryUnit(saveMedicineOutDTO.getDispensaryUnit())
                .dispensaryTime(saveMedicineOutDTO.getDispensaryTime())
                .remark(saveMedicineOutDTO.getRemark())
                .build();
    }

}
