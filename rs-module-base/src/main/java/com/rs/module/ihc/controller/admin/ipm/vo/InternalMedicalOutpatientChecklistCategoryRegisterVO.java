package com.rs.module.ihc.controller.admin.ipm.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.dromara.x.file.storage.core.FileInfo;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR> LiuWenxing
 * @create 2024/7/30 17:50
 */
@Data
public class InternalMedicalOutpatientChecklistCategoryRegisterVO {

    @ApiModelProperty(value="检查类型 字典值 1=常规检查 2=心电图 3=B超 4=DR")
    @NotEmpty
    private String checklistCategory;

    @ApiModelProperty(value="检查结果")
    private String checkResult;

    @ApiModelProperty("检查文件")
    private List<FileInfo> fileList;


}
