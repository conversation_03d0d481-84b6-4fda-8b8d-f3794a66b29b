package com.rs.module.ihc.controller.admin.ipm.appointment.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 所内就医-预约登记列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class IhcsInternalMedicalAppointmentListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("预约编号")
    private String appointmentNum;

    @ApiModelProperty("被监管人员编号")
    private String supervisedUserCode;

    @ApiModelProperty("是否重病号 0：否 1：是")
    private Short sickType;

    @ApiModelProperty("报病时间")
    private Date[] diseaseTime;

    @ApiModelProperty("报病原因")
    private String diseaseReason;

    @ApiModelProperty("初步诊断")
    private String primaryDiagnosis;

    @ApiModelProperty("病情等级 字典值")
    private String diseaseLevel;

    @ApiModelProperty("处理方式/预约审核结果 字典值")
    private String processMethod;

    @ApiModelProperty("处理人 对应permission_user.userid")
    private String processUserid;

    @ApiModelProperty("处理人名称")
    private String processUserName;

    @ApiModelProperty("处理时间")
    private Date[] processTime;

    @ApiModelProperty("远程问诊状态 0：待处理 1：已处理")
    private Short remoteDiagnoseStatus;

    @ApiModelProperty("监所id")
    private String prisonId;

    @ApiModelProperty("有无过敏史 字典值：is_have")
    private String hasAllergyHistory;


    @ApiModelProperty("操作类型0全部1今天2昨天3近一周")
    private String operationType;

}
