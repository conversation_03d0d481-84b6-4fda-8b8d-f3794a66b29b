package com.rs.module.ihc.dao.pm;

import java.util.*;

import com.github.yulichang.base.MPJBaseMapper;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.pm.PurchaseApplyRelDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 药品管理-药品采购申请-药品信息关联
 *
 * <AUTHOR>
 */
@Mapper
public interface PurchaseApplyRelDao extends MPJBaseMapper<PurchaseApplyRelDO> {

    default List<PurchaseApplyRelDO> selectListByMlh(String mlh) {
        return selectList(new LambdaQueryWrapperX<PurchaseApplyRelDO>().eq(PurchaseApplyRelDO::getMlh, mlh));
    }

    default int deleteByMlh(String mlh) {
        return delete(new LambdaQueryWrapperX<PurchaseApplyRelDO>().eq(PurchaseApplyRelDO::getMlh, mlh));
    }

}
