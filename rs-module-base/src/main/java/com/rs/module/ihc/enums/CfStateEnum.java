package com.rs.module.ihc.enums;

/**
 * @ClassName CfStatusEnum
 * @Description 处方状态
 * <AUTHOR>
 * @Date 2025/3/25 18:20
 * @Version 1.0
 */
public enum CfStateEnum {
    CSH("初始化", "-1"),
    YSY("未使用", "0"),
    SYZ("使用中","1"),
    YZF("已作废","2"),
    YZT("已暂停","3"),
    YJS("已结束","4"),
    ;

    private String desc;
    private String val;

    CfStateEnum(String desc, String val) {
        this.desc = desc;
        this.val = val;
    }

    public String getDesc() {
        return desc;
    }
    public String getVal() {
        return val;
    }
}
