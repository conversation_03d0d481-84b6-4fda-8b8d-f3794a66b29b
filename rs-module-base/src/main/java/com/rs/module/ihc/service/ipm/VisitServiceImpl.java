package com.rs.module.ihc.service.ipm;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.entity.RyReqVO;
import com.rs.module.ihc.constant.InternalMedicalPrescribeSourceConstant;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.dao.ipm.VisitDao;
import com.rs.module.ihc.dao.ipm.VisitMedicineDao;
import com.rs.module.ihc.entity.ipm.VisitDO;
import com.rs.module.ihc.entity.ipm.VisitMedicineDO;
import lombok.extern.log4j.Log4j2;
import org.apache.tika.utils.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 所内就医-现场巡检 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Log4j2
public class VisitServiceImpl extends BaseServiceImpl<VisitDao, VisitDO> implements VisitService {

    @Resource
    private VisitDao visitDao;
    @Resource
    private VisitMedicineDao visitMedicineDao;

    @Resource
    private PrescribeService prescribeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createVisit(VisitSaveReqVO createReqVO) {
        VisitDO visit = null;
        if (StringUtils.isBlank(createReqVO.getId())) {
            // 插入
            visit = BeanUtils.toBean(createReqVO, VisitDO.class);
            visitDao.insert(visit);
            // 插入子表
            createVisitMedicineList(visit.getId(), createReqVO.getVisitMedicines());
        } else {
            String[] ids = createReqVO.getId().split(",");
            //获取所有选中的id
            List<VisitDO> visitList = visitDao.findDiseaseTimeDescInId(ids);
            for (int i = 0; i < visitList.size(); i++) {
                visit = visitList.get(i);
                //只处理第一条
                if (i == 0) {
                    //获取最新一条
                    CopyOptions copyOptions = CopyOptions.create();
                    copyOptions.setIgnoreNullValue(true);
                    BeanUtil.copyProperties(createReqVO, visit, copyOptions);
                    visitDao.updateById(visit);
                    //更新子表
                    updateVisitMedicineList(visit.getId(), createReqVO.getVisitMedicines());
                } else {
                    //其他数据变更为无需用药
                    VisitSaveWxclReqVO createVisitWxyy = new VisitSaveWxclReqVO();
                    createVisitWxyy.setVisitProcessMethod("3");
                    createVisitWxyy.setId(visit.getId());
                    createVisitWxyy.setMainComplaint(createReqVO.getMainComplaint());
                    createVisitWxyy(createVisitWxyy);
                }
            }

        }
        return visit.getId();
    }

    @Override
    public String createVisitCf(VisitSaveCfReqVO createReqVO) {
        VisitDO visit = null;
        if (StringUtils.isBlank(createReqVO.getId())) {
            visit = BeanUtils.toBean(createReqVO, VisitDO.class);
            visitDao.insert(visit);
        } else {
            visit = visitDao.selectById(createReqVO.getId());
        }
        prescribeService.saveOrUpdatePrescribeFromType(InternalMedicalPrescribeSourceConstant.SITE_DIAGNOSE,
                visit.getId(),
                visit.getJgrybm(),
                createReqVO.getPrescribe());
        return visit.getId();
    }

    @Override
    public String createVisitWxyy(VisitSaveWxclReqVO createReqVO) {
        VisitDO visit;
        if (StringUtils.isBlank(createReqVO.getId())) {
            // 插入
            visit = BeanUtils.toBean(createReqVO, VisitDO.class);
            visitDao.insert(visit);
        } else {
            CopyOptions copyOptions = CopyOptions.create();
            copyOptions.setIgnoreNullValue(true);
            visit = visitDao.selectById(createReqVO.getId());
            BeanUtil.copyProperties(createReqVO, visit, copyOptions);
            visitDao.updateById(visit);
        }

        return visit.getId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateVisit(VisitSaveReqVO updateReqVO) {
        // 校验存在
        validateVisitExists(updateReqVO.getId());
        // 更新
        VisitDO updateObj = BeanUtils.toBean(updateReqVO, VisitDO.class);
        visitDao.updateById(updateObj);

        // 更新子表
        updateVisitMedicineList(updateReqVO.getId(), updateReqVO.getVisitMedicines());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateVisitCf(VisitSaveCfReqVO updateReqVO) {
        // 校验存在
        validateVisitExists(updateReqVO.getId());
        // 更新
        VisitDO updateObj = BeanUtils.toBean(updateReqVO, VisitDO.class);
        visitDao.updateById(updateObj);
        prescribeService.saveOrUpdatePrescribeFromType(InternalMedicalPrescribeSourceConstant.SITE_DIAGNOSE,
                updateObj.getId(),
                updateObj.getJgrybm(),
                updateReqVO.getPrescribe());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteVisit(String id) {
        // 校验存在
        validateVisitExists(id);
        // 删除
        visitDao.deleteById(id);

        // 删除子表
        deleteVisitMedicineByVisitId(id);
    }

    private void validateVisitExists(String id) {
        if (visitDao.selectById(id) == null) {
            throw new ServerException("所内就医-现场巡检数据不存在");
        }
    }

    @Override
    public VisitDO getVisit(String id) {
        return visitDao.selectById(id);
    }

    @Override
    public PageResult<VisitDO> getVisitPage(VisitPageReqVO pageReqVO) {
        return visitDao.selectPage(pageReqVO);
    }

    @Override
    public List<VisitDO> getVisitList(VisitListReqVO listReqVO) {
        return visitDao.selectList(listReqVO);
    }


    // ==================== 子表（所内就医-现场巡诊-现场司药信息） ====================

    @Override
    public List<VisitMedicineDO> getVisitMedicineListByVisitId(String visitId) {
        return visitMedicineDao.selectListByVisitId(visitId);
    }

    private void createVisitMedicineList(String visitId, List<VisitMedicineDO> list) {
        list.forEach(o -> o.setVisitId(visitId));
        list.forEach(o -> visitMedicineDao.insert(o));
    }

    private void updateVisitMedicineList(String visitId, List<VisitMedicineDO> list) {
        deleteVisitMedicineByVisitId(visitId);
        list.forEach(o -> o.setId(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createVisitMedicineList(visitId, list);
    }

    private void deleteVisitMedicineByVisitId(String visitId) {
        visitMedicineDao.deleteByVisitId(visitId);
    }


    @Override
    public List<VisitRespVO> getVisitTodo(RyReqVO ry) {
        return visitDao.getVisitTodo(ry);
    }


    @Override
    public List<VisitXzjgRespVO> listXzjgByRoomId(String type, String roomId) {
        return visitDao.listXzjgByRoomId(type, roomId);

    }

    @Override
    public Boolean getHaveRestraints(String jgrybm) {
        if (visitDao.countEquipmentUseByJgrybm(jgrybm) > 0) {
            return true;
        }
        return false;
    }
}
