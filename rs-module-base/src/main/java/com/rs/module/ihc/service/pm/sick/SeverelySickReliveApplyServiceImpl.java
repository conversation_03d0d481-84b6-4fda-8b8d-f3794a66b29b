package com.rs.module.ihc.service.pm.sick;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.ihc.controller.admin.pm.sick.vo.*;
import com.rs.module.ihc.entity.pm.sick.SeverelySickReliveApplyDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.pm.sick.SeverelySickReliveApplyDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 重特病号解除申请 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SeverelySickReliveApplyServiceImpl extends BaseServiceImpl<SeverelySickReliveApplyDao, SeverelySickReliveApplyDO> implements SeverelySickReliveApplyService {

    @Resource
    private SeverelySickReliveApplyDao severelySickReliveApplyDao;

    @Override
    public String createSeverelySickReliveApply(SeverelySickReliveApplySaveReqVO createReqVO) {
        // 插入
        SeverelySickReliveApplyDO severelySickReliveApply = BeanUtils.toBean(createReqVO, SeverelySickReliveApplyDO.class);
        severelySickReliveApplyDao.insert(severelySickReliveApply);
        // 返回
        return severelySickReliveApply.getId();
    }

    @Override
    public void updateSeverelySickReliveApply(SeverelySickReliveApplySaveReqVO updateReqVO) {
        // 校验存在
        validateSeverelySickReliveApplyExists(updateReqVO.getId());
        // 更新
        SeverelySickReliveApplyDO updateObj = BeanUtils.toBean(updateReqVO, SeverelySickReliveApplyDO.class);
        severelySickReliveApplyDao.updateById(updateObj);
    }

    @Override
    public void deleteSeverelySickReliveApply(String id) {
        // 校验存在
        validateSeverelySickReliveApplyExists(id);
        // 删除
        severelySickReliveApplyDao.deleteById(id);
    }

    private void validateSeverelySickReliveApplyExists(String id) {
        if (severelySickReliveApplyDao.selectById(id) == null) {
            throw new ServerException("重特病号解除申请数据不存在");
        }
    }

    @Override
    public SeverelySickReliveApplyDO getSeverelySickReliveApply(String id) {
        return severelySickReliveApplyDao.selectById(id);
    }

    @Override
    public PageResult<SeverelySickReliveApplyDO> getSeverelySickReliveApplyPage(SeverelySickReliveApplyPageReqVO pageReqVO) {
        return severelySickReliveApplyDao.selectPage(pageReqVO);
    }

    @Override
    public List<SeverelySickReliveApplyDO> getSeverelySickReliveApplyList(SeverelySickReliveApplyListReqVO listReqVO) {
        return severelySickReliveApplyDao.selectList(listReqVO);
    }


}
