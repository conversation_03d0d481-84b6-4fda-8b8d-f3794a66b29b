package com.rs.module.ihc.service.pm;

import java.util.*;
import javax.validation.*;

import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.PurchaseApplyDO;
import com.rs.framework.common.pojo.PageResult;

/**
 * 药品管理-药品采购申请 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseApplyService {

    /**
     * 创建药品管理-药品采购申请
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPurchaseApply(@Valid PurchaseApplySaveReqVO createReqVO);

    /**
     * 更新药品管理-药品采购申请
     *
     * @param updateReqVO 更新信息
     */
    void updatePurchaseApply(@Valid PurchaseApplySaveReqVO updateReqVO);

    /**
     * 删除药品管理-药品采购申请
     *
     * @param id 编号
     */
    void deletePurchaseApply(String id);

    /**
     * 获得药品管理-药品采购申请
     *
     * @param id 编号
     * @return 药品管理-药品采购申请
     */
    PurchaseApplyRespVO getPurchaseApply(String id);

    /**
    * 获得药品管理-药品采购申请分页
    *
    * @param pageReqVO 分页查询
    * @return 药品管理-药品采购申请分页
    */
    PageResult<PurchaseApplyDO> getPurchaseApplyPage(PurchaseApplyPageReqVO pageReqVO);

    /**
    * 获得药品管理-药品采购申请列表
    *
    * @param listReqVO 查询条件
    * @return 药品管理-药品采购申请列表
    */
    List<PurchaseApplyDO> getPurchaseApplyList(PurchaseApplyListReqVO listReqVO);


    // ==================== 子表（药品管理-药品采购申请-药品信息关联） ====================

    /**
     * 获得药品管理-药品采购申请-药品信息关联列表
     *
     * @param mlh 采购申请id，对应ihc_pm_purchase_apply.id
     * @return 药品管理-药品采购申请-药品信息关联列表
     */
    List<PurchaseApplyRelRespVO> getPurchaseApplyRelListByMlh(String mlh);

    PageResult<PurchaseApplyDO> selectByProCode(PurchaseApplyPageReqVO reqVO) ;
}
