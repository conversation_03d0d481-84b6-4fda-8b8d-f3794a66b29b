package com.rs.module.ihc.controller.admin.pm.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.*;

/**
 * 药品管理-药品采购申请-药品信息关联 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 药品管理-药品采购申请-药品信息关联新增/修改 Request VO")
@TableName("ihc_pm_purchase_apply_rel")
@KeySequence("ihc_pm_purchase_apply_rel_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MedicineCodeImportVO {

    @ExcelProperty("序号")
    private String xh;

    @ExcelProperty("批准文号")
    private String approvalNum;

    @ExcelProperty("产品名称")
    private String medicineName;

    @ExcelProperty("剂型")
    private String dosageForm;

    @ExcelProperty("规格")
    private String specs;

    @ExcelProperty("上市许可持有人")
    private String marketApprovalHolder;

    @ExcelProperty("生产单位")
    private String productUnit;

    @ExcelProperty("药品编码")
    private String medicineStandardCode;

    @ExcelProperty("药品编码备注")
    private String medicineStandardCodeRemark;


    @ExcelProperty("公司名称中文")
    private String productUnitZw;

    @ExcelProperty("公司名称英文")
    private String productUnitEn;

    @ExcelProperty("上市许可持有人中文")
    private String marketApprovalHolderZw;

    @ExcelProperty("上市许可持有人英文")
    private String marketApprovalHolderEn;

    @ExcelProperty("注册证号")
    private String zczh;


    private String originType;


}
