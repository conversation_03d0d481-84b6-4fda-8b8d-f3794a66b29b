package com.rs.module.ihc.service.pm;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.util.BizAssert;
import com.rs.module.ihc.controller.admin.ipm.bo.UpdateMedicineInInventoryNumBO;
import com.rs.module.ihc.controller.admin.pm.dto.SaveMedicineLossDTO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineLossListReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineLossPageReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineLossSaveReqVO;
import com.rs.module.ihc.dao.pm.PharmacyMedicineDao;
import com.rs.module.ihc.dao.pm.PharmacyMedicineLossDao;
import com.rs.module.ihc.entity.pm.PharmacyMedicineInDO;
import com.rs.module.ihc.entity.pm.PharmacyMedicineLossDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 大药房管理-药品报损 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PharmacyMedicineLossServiceImpl extends BaseServiceImpl<PharmacyMedicineLossDao, PharmacyMedicineLossDO> implements PharmacyMedicineLossService {

    @Resource
    private PharmacyMedicineLossDao pharmacyMedicineLossDao;

    @Resource
    private PharmacyMedicineInService pharmacyMedicineInService;

    @Resource
    private PharmacyMedicineDao pharmacyMedicineDao;

    @Override
    public String createPharmacyMedicineLoss(PharmacyMedicineLossSaveReqVO createReqVO) {
        // 插入
        PharmacyMedicineLossDO pharmacyMedicineLoss = BeanUtils.toBean(createReqVO, PharmacyMedicineLossDO.class);
        pharmacyMedicineLossDao.insert(pharmacyMedicineLoss);
        // 返回
        return pharmacyMedicineLoss.getId();
    }

    @Override
    public void updatePharmacyMedicineLoss(PharmacyMedicineLossSaveReqVO updateReqVO) {
        // 校验存在
        validatePharmacyMedicineLossExists(updateReqVO.getId());
        // 更新
        PharmacyMedicineLossDO updateObj = BeanUtils.toBean(updateReqVO, PharmacyMedicineLossDO.class);
        pharmacyMedicineLossDao.updateById(updateObj);
    }

    @Override
    public void deletePharmacyMedicineLoss(String id) {
        // 校验存在
        validatePharmacyMedicineLossExists(id);
        // 删除
        pharmacyMedicineLossDao.deleteById(id);
    }

    private void validatePharmacyMedicineLossExists(String id) {
        if (pharmacyMedicineLossDao.selectById(id) == null) {
            throw new ServerException("大药房管理-药品报损数据不存在");
        }
    }

    @Override
    public PharmacyMedicineLossDO getPharmacyMedicineLoss(String id) {
        return pharmacyMedicineLossDao.selectById(id);
    }

    @Override
    public PageResult<PharmacyMedicineLossDO> getPharmacyMedicineLossPage(PharmacyMedicineLossPageReqVO pageReqVO) {
        return pharmacyMedicineLossDao.selectPage(pageReqVO);
    }

    @Override
    public List<PharmacyMedicineLossDO> getPharmacyMedicineLossList(PharmacyMedicineLossListReqVO listReqVO) {
        return pharmacyMedicineLossDao.selectList(listReqVO);
    }

    @Override
    public void batchSaveMedicineLoss(List<SaveMedicineLossDTO> medicineLossList, String medicineId) {
        PharmacyMedicineLossDO ihcsMedicine = pharmacyMedicineLossDao.selectById(medicineId);
        BizAssert.notNull(ihcsMedicine, "药品不存在");
        Map<String, PharmacyMedicineInDO> ihcsMedicineInMap = pharmacyMedicineInService.lambdaQuery()
                .in(PharmacyMedicineInDO::getId, medicineLossList.stream()
                        .map(SaveMedicineLossDTO::getMedicineInId)
                        .collect(Collectors.toList()))
                .list()
                .stream()
                .collect(Collectors.toMap(PharmacyMedicineInDO::getId, Function.identity()));
        // 药品库存数量
        final BigDecimal sourceInventoryNum = ihcsMedicine.getTotalInventoryNum();
        BigDecimal targetInventoryNum = sourceInventoryNum;
        // 记录每个批次的扣减记录
        Map<String, UpdateMedicineInInventoryNumBO> batchInventoryNumMap = new LinkedHashMap<>();
        List<PharmacyMedicineLossDO> ihcsMedicineOutList = new ArrayList<>();
        for (SaveMedicineLossDTO saveMedicineOutDTO : medicineLossList) {
            String medicineInId = saveMedicineOutDTO.getMedicineInId();
            PharmacyMedicineInDO ihcsMedicineIn = ihcsMedicineInMap.get(medicineInId);
            BizAssert.notNull(ihcsMedicineIn, "入库批次id[" + medicineInId + "]不存在");
            String batchCode = ihcsMedicineIn.getBatchCode();
            BizAssert.isTrue(Objects.equals(ihcsMedicineIn.getMedicineId(), medicineId),
                    "入库批次[" + batchCode + "]不属于该药品");
            // 报损数量
            BigDecimal lossNum = saveMedicineOutDTO.getLossNum();
            // 批次库存数量
            BigDecimal batchSourceInventoryNum = ihcsMedicineIn.getInventoryNum();
            BizAssert.isTrue(batchSourceInventoryNum.compareTo(lossNum) >= 0,
                    "批次[" + batchCode + "]报损数量不能大于库存数量");
            // 药品总库存扣减
            targetInventoryNum = targetInventoryNum.subtract(lossNum);
            // 这里是由于可能出现批量报损时，一个批次在一次请求中分两个记录来出
            UpdateMedicineInInventoryNumBO updateMedicineInInventoryNumBO =
                    batchInventoryNumMap.computeIfAbsent(medicineId, key -> UpdateMedicineInInventoryNumBO.builder()
                            .id(medicineInId)
                            .sourceInventoryNum(batchSourceInventoryNum)
                            .targetInventoryNum(batchSourceInventoryNum)
                            .build());
            // 批次库存数量扣减
            BigDecimal batchTargetInventoryNum = updateMedicineInInventoryNumBO.getTargetInventoryNum().subtract(lossNum);
            BizAssert.isTrue(batchTargetInventoryNum.signum() >= 0, "批次[" + batchCode + "]报损数量不能大于库存数量");
            updateMedicineInInventoryNumBO.setTargetInventoryNum(batchTargetInventoryNum);
            // 报损记录入库
            PharmacyMedicineLossDO ihcsMedicineLoss = this.convertToIhcsMedicineLoss(saveMedicineOutDTO);
            ihcsMedicineLoss.setMedicineId(medicineId);
            ihcsMedicineLoss.setInventoryNum(batchTargetInventoryNum);
            ihcsMedicineLoss.setTotalInventoryNum(targetInventoryNum);
            ihcsMedicineOutList.add(ihcsMedicineLoss);
        }
        // 报损记录保存
        this.saveBatch(ihcsMedicineOutList);
        // 批次库存更新
        pharmacyMedicineInService.batchCompareAndSetInventoryNum(batchInventoryNumMap.values());
        // 药品库存更新
        pharmacyMedicineDao.compareAndSetMedicineInventoryNum(medicineId, sourceInventoryNum, targetInventoryNum);
        // 药品是否存在过期批次标识更新，必须再批次库存更新后执行，否则查询时会有问题
        pharmacyMedicineInService.selectExpireBatchAndUpdateMedicineFlag(Collections.singletonList(medicineId));
    }


    private PharmacyMedicineLossDO convertToIhcsMedicineLoss(SaveMedicineLossDTO saveMedicineOutDTO) {
        return PharmacyMedicineLossDO.builder()
                .medicineInId(saveMedicineOutDTO.getMedicineInId())
                .lossReason(saveMedicineOutDTO.getLossReason())
                .lossNum(saveMedicineOutDTO.getLossNum())
                .lossTime(saveMedicineOutDTO.getLossTime())
                .remark(saveMedicineOutDTO.getRemark())
                .build();
    }

}
