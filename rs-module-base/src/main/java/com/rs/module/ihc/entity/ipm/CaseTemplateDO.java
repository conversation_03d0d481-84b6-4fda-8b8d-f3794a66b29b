package com.rs.module.ihc.entity.ipm;

import lombok.*;
import java.util.*;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 所内就医-病例模板 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_ipm_case_template")
@KeySequence("ihc_ipm_case_template_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CaseTemplateDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 病例名称
     */
    private String templateName;
    /**
     * 模板类型（1：医嘱模板， 2：门诊登记模板，3：巡诊登记模板）
     */
    private Integer templateType;
    /**
     * 主诉
     */
    private String mainComplaint;
    /**
     * 病史
     */
    private String medicalHistory;
    /**
     * 体格检查
     */
    private String physicalCheck;
    /**
     * 辅助检查
     */
    private String auxiliaryCheck;
    /**
     * 初步诊断
     */
    private String primaryDiagnosis;
    /**
     * 处理意见
     */
    private String suggestion;
    /**
     * 病情简述
     */
    private String illnessResume;
    /**
     * 巡诊情况
     */
    private String visitState;
    /**
     * 巡诊结论
     */
    private String visitConclusion;

}
