package com.rs.module.ihc.controller.admin.ipm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 所内就医-病例模板列表 Request VO")
@Data
public class CaseTemplateListReqVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("病例名称")
    private String templateName;

    @ApiModelProperty("模板类型（1：医嘱模板， 2：门诊登记模板，3：巡诊登记模板）")
    private Integer templateType;

    @ApiModelProperty("主诉")
    private String mainComplaint;

    @ApiModelProperty("病史")
    private String medicalHistory;

    @ApiModelProperty("体格检查")
    private String physicalCheck;

    @ApiModelProperty("辅助检查")
    private String auxiliaryCheck;

    @ApiModelProperty("初步诊断")
    private String primaryDiagnosis;

    @ApiModelProperty("处理意见")
    private String suggestion;

    @ApiModelProperty("病情简述")
    private String illnessResume;

    @ApiModelProperty("巡诊情况")
    private String visitState;

    @ApiModelProperty("巡诊结论")
    private String visitConclusion;

}
