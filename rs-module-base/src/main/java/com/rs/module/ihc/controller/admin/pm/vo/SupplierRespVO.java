package com.rs.module.ihc.controller.admin.pm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 药房管理-供应商 Response VO")
@Data
public class SupplierRespVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("公司名称")
    private String companyName;
    @ApiModelProperty("地址")
    private String address;
    @ApiModelProperty("邮编")
    private String postalCode;
    @ApiModelProperty("电话")
    private String telephone;
    @ApiModelProperty("联系人")
    private String contacts;
    @ApiModelProperty("开户行")
    private String openingBank;
    @ApiModelProperty("账号")
    private String account;
    @ApiModelProperty("税号")
    private String dutyParagraph;
    @ApiModelProperty("监所id")
    private String prisonId;
}
