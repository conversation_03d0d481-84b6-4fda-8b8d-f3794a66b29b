package com.rs.module.ihc.controller.admin.ipm.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.common.annotation.Format;
import com.rs.framework.mybatis.annotation.Query;
import com.rs.module.base.entity.RyVO;
import com.rs.module.ihc.service.ipm.VisitService;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "管理后台 - 所内就医-现场巡检 Response VO")
@Data
public class VisitPrescribeRespVO extends RyVO {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("来源（1:所内就医预约，2:手动新增）")
    private String source;
    @ApiModelProperty("预约id，对应ihc_ipm_internal_medical_appointment.id")
    private String appointmentId;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("在押人员编号")
    private String rybh;
    @ApiModelProperty("在押人员名称")
    private String ryxm;
    @ApiModelProperty("是否重病号（ 0：否 ，1：是）")
    private Integer sickType;
    @ApiModelProperty("报病时间")
    private Date diseaseTime;
    @ApiModelProperty("报病原因")
    private String diseaseReason;
    @ApiModelProperty("主诉")
    private String mainComplaint;
    @ApiModelProperty("巡诊情况")
    private String visitState;
    @ApiModelProperty("巡诊处理方式")
    @Trans(type = TransType.DICTIONARY, key = "ZD_VISIT_PROCESS_METHOD")
    private String visitProcessMethod;
    @ApiModelProperty("巡诊时间")
    private Date visitTime;
    @ApiModelProperty("巡诊人证件号码")
    private String visitUserid;
    @ApiModelProperty("巡诊人名称")
    private String visitUserName;
    @ApiModelProperty("巡诊结论")
    private String visitConclusion;
    @ApiModelProperty("监所id")
    private String prisonId;
    @ApiModelProperty("械具使用建议")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JJSYJY ")

    private String toolUseAdvise;
    @Trans(type = TransType.DICTIONARY, key = "ZD_JCYY")
    @ApiModelProperty("械具解除原因")
    private String toolUseCancelReason;
    @ApiModelProperty("第三方数据id")
    private String extId;

    @ApiModelProperty("更新时间")
    private Date updateTime;


    @ApiModelProperty("是否有戒具")
    @Format(service = VisitService.class, method = "getHaveRestraints", value = "jgrybm")
    private Boolean haveRestraints;

    @Query(sql = "select * from ihc_ipm_prescribe where business_id = '${id}' and is_del =0",beanClass = PrescribeRespVO.class)
    public PrescribeRespVO prescribe;

}
