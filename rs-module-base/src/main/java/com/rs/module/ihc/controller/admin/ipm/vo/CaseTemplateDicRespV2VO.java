package com.rs.module.ihc.controller.admin.ipm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

@ApiModel(description = "管理后台 - 所内就医-病例模板表-字典 Response VO")
public class CaseTemplateDicRespV2VO {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主诉")
    public List<CaseTemplateDicRespVO> zs = new ArrayList<>();
    @ApiModelProperty("主诉周期")
    public List<CaseTemplateDicRespVO> zszq = new ArrayList<>();
    @ApiModelProperty("既往史")
    public List<CaseTemplateDicRespVO> jws = new ArrayList<>();
    @ApiModelProperty("既往有")
    public List<CaseTemplateDicRespVO> jwy = new ArrayList<>();
    @ApiModelProperty("个人史")
    public List<CaseTemplateDicRespVO> grs = new ArrayList<>();
    @ApiModelProperty("体征")
    public List<CaseTemplateDicRespVO> tz = new ArrayList<>();
    @ApiModelProperty("体温")
    public List<CaseTemplateDicRespVO> tw = new ArrayList<>();
    @ApiModelProperty("脉搏")
    public List<CaseTemplateDicRespVO> mb = new ArrayList<>();
    @ApiModelProperty("呼吸")
    public List<CaseTemplateDicRespVO> hx = new ArrayList<>();
    @ApiModelProperty("血压-收缩压")
    public List<CaseTemplateDicRespVO> ssy = new ArrayList<>();
    @ApiModelProperty("血压-舒张压")
    public List<CaseTemplateDicRespVO> szy = new ArrayList<>();
    @ApiModelProperty("体重-婴儿")
    public List<CaseTemplateDicRespVO> yrtz = new ArrayList<>();
    @ApiModelProperty("体重-青少年")
    public List<CaseTemplateDicRespVO> qsntz = new ArrayList<>();
    @ApiModelProperty("体重-成年人")
    public List<CaseTemplateDicRespVO> cnrtz = new ArrayList<>();
    @ApiModelProperty("身高-儿童")
    public List<CaseTemplateDicRespVO> etsg = new ArrayList<>();
    @ApiModelProperty("身高-青少年")
    public List<CaseTemplateDicRespVO> qsnsg = new ArrayList<>();
    @ApiModelProperty("身高-成年人")
    public List<CaseTemplateDicRespVO> cnrsg = new ArrayList<>();
    @ApiModelProperty("血氧")
    public List<CaseTemplateDicRespVO> xy = new ArrayList<>();
    @ApiModelProperty("血糖")
    public List<CaseTemplateDicRespVO> xt = new ArrayList<>();

    public List<CaseTemplateDicRespVO> getZs() {
        return zs;
    }

    public void setZs(List<CaseTemplateDicRespVO> zs) {
        this.zs = zs;
    }

    public List<CaseTemplateDicRespVO> getZszq() {
        return zszq;
    }

    public void setZszq(List<CaseTemplateDicRespVO> zszq) {
        this.zszq = zszq;
    }

    public List<CaseTemplateDicRespVO> getJws() {
        return jws;
    }

    public void setJws(List<CaseTemplateDicRespVO> jws) {
        this.jws = jws;
    }

    public List<CaseTemplateDicRespVO> getJwy() {
        return jwy;
    }

    public void setJwy(List<CaseTemplateDicRespVO> jwy) {
        this.jwy = jwy;
    }

    public List<CaseTemplateDicRespVO> getGrs() {
        return grs;
    }

    public void setGrs(List<CaseTemplateDicRespVO> grs) {
        this.grs = grs;
    }

    public List<CaseTemplateDicRespVO> getTz() {
        return tz;
    }

    public void setTz(List<CaseTemplateDicRespVO> tz) {
        this.tz = tz;
    }

    public List<CaseTemplateDicRespVO> getTw() {
        return tw;
    }

    public void setTw(List<CaseTemplateDicRespVO> tw) {
        this.tw = tw;
    }

    public List<CaseTemplateDicRespVO> getMb() {
        return mb;
    }

    public void setMb(List<CaseTemplateDicRespVO> mb) {
        this.mb = mb;
    }

    public List<CaseTemplateDicRespVO> getHx() {
        return hx;
    }

    public void setHx(List<CaseTemplateDicRespVO> hx) {
        this.hx = hx;
    }

    public List<CaseTemplateDicRespVO> getSsy() {
        return ssy;
    }

    public void setSsy(List<CaseTemplateDicRespVO> ssy) {
        this.ssy = ssy;
    }

    public List<CaseTemplateDicRespVO> getSzy() {
        return szy;
    }

    public void setSzy(List<CaseTemplateDicRespVO> szy) {
        this.szy = szy;
    }

    public List<CaseTemplateDicRespVO> getYrtz() {
        return yrtz;
    }

    public void setYrtz(List<CaseTemplateDicRespVO> yrtz) {
        this.yrtz = yrtz;
    }

    public List<CaseTemplateDicRespVO> getQsntz() {
        return qsntz;
    }

    public void setQsntz(List<CaseTemplateDicRespVO> qsntz) {
        this.qsntz = qsntz;
    }

    public List<CaseTemplateDicRespVO> getCnrtz() {
        return cnrtz;
    }

    public void setCnrtz(List<CaseTemplateDicRespVO> cnrtz) {
        this.cnrtz = cnrtz;
    }

    public List<CaseTemplateDicRespVO> getEtsg() {
        return etsg;
    }

    public void setEtsg(List<CaseTemplateDicRespVO> etsg) {
        this.etsg = etsg;
    }

    public List<CaseTemplateDicRespVO> getQsnsg() {
        return qsnsg;
    }

    public void setQsnsg(List<CaseTemplateDicRespVO> qsnsg) {
        this.qsnsg = qsnsg;
    }

    public List<CaseTemplateDicRespVO> getCnrsg() {
        return cnrsg;
    }

    public void setCnrsg(List<CaseTemplateDicRespVO> cnrsg) {
        this.cnrsg = cnrsg;
    }

    public List<CaseTemplateDicRespVO> getXy() {
        return xy;
    }

    public void setXy(List<CaseTemplateDicRespVO> xy) {
        this.xy = xy;
    }

    public List<CaseTemplateDicRespVO> getXt() {
        return xt;
    }

    public void setXt(List<CaseTemplateDicRespVO> xt) {
        this.xt = xt;
    }
}
