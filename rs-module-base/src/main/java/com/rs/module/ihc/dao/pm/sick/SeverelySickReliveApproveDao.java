package com.rs.module.ihc.dao.pm.sick;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.pm.sick.SeverelySickReliveApproveDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.pm.sick.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 重特病号解除申请审批 Dao
*
* <AUTHOR>
*/
@Mapper
public interface SeverelySickReliveApproveDao extends IBaseDao<SeverelySickReliveApproveDO> {


    default PageResult<SeverelySickReliveApproveDO> selectPage(SeverelySickReliveApprovePageReqVO reqVO) {
        Page<SeverelySickReliveApproveDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<SeverelySickReliveApproveDO> wrapper = new LambdaQueryWrapperX<SeverelySickReliveApproveDO>()
            .eqIfPresent(SeverelySickReliveApproveDO::getReliveApplyId, reqVO.getReliveApplyId())
            .eqIfPresent(SeverelySickReliveApproveDO::getBusinessStatus, reqVO.getBusinessStatus())
            .eqIfPresent(SeverelySickReliveApproveDO::getApproveResult, reqVO.getApproveResult())
            .eqIfPresent(SeverelySickReliveApproveDO::getApproveUserId, reqVO.getApproveUserId())
            .likeIfPresent(SeverelySickReliveApproveDO::getApproveUserName, reqVO.getApproveUserName())
            .eqIfPresent(SeverelySickReliveApproveDO::getApproveOpinion, reqVO.getApproveOpinion())
            .betweenIfPresent(SeverelySickReliveApproveDO::getApproveTime, reqVO.getApproveTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(SeverelySickReliveApproveDO::getAddTime);
        }
        Page<SeverelySickReliveApproveDO> severelySickReliveApprovePage = selectPage(page, wrapper);
        return new PageResult<>(severelySickReliveApprovePage.getRecords(), severelySickReliveApprovePage.getTotal());
    }
    default List<SeverelySickReliveApproveDO> selectList(SeverelySickReliveApproveListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SeverelySickReliveApproveDO>()
            .eqIfPresent(SeverelySickReliveApproveDO::getReliveApplyId, reqVO.getReliveApplyId())
            .eqIfPresent(SeverelySickReliveApproveDO::getBusinessStatus, reqVO.getBusinessStatus())
            .eqIfPresent(SeverelySickReliveApproveDO::getApproveResult, reqVO.getApproveResult())
            .eqIfPresent(SeverelySickReliveApproveDO::getApproveUserId, reqVO.getApproveUserId())
            .likeIfPresent(SeverelySickReliveApproveDO::getApproveUserName, reqVO.getApproveUserName())
            .eqIfPresent(SeverelySickReliveApproveDO::getApproveOpinion, reqVO.getApproveOpinion())
            .betweenIfPresent(SeverelySickReliveApproveDO::getApproveTime, reqVO.getApproveTime())
        .orderByDesc(SeverelySickReliveApproveDO::getAddTime));    }


    }
