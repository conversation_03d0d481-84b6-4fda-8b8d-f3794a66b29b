package com.rs.module.ihc.service.pm.sick;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.pm.sick.vo.*;
import com.rs.module.ihc.entity.pm.sick.SeverelySickReliveApproveDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 重特病号解除申请审批 Service 接口
 *
 * <AUTHOR>
 */
public interface SeverelySickReliveApproveService extends IBaseService<SeverelySickReliveApproveDO>{

    /**
     * 创建重特病号解除申请审批
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createSeverelySickReliveApprove(@Valid SeverelySickReliveApproveSaveReqVO createReqVO);

    /**
     * 更新重特病号解除申请审批
     *
     * @param updateReqVO 更新信息
     */
    void updateSeverelySickReliveApprove(@Valid SeverelySickReliveApproveSaveReqVO updateReqVO);

    /**
     * 删除重特病号解除申请审批
     *
     * @param id 编号
     */
    void deleteSeverelySickReliveApprove(String id);

    /**
     * 获得重特病号解除申请审批
     *
     * @param id 编号
     * @return 重特病号解除申请审批
     */
    SeverelySickReliveApproveDO getSeverelySickReliveApprove(String id);

    /**
    * 获得重特病号解除申请审批分页
    *
    * @param pageReqVO 分页查询
    * @return 重特病号解除申请审批分页
    */
    PageResult<SeverelySickReliveApproveDO> getSeverelySickReliveApprovePage(SeverelySickReliveApprovePageReqVO pageReqVO);

    /**
    * 获得重特病号解除申请审批列表
    *
    * @param listReqVO 查询条件
    * @return 重特病号解除申请审批列表
    */
    List<SeverelySickReliveApproveDO> getSeverelySickReliveApproveList(SeverelySickReliveApproveListReqVO listReqVO);


}
