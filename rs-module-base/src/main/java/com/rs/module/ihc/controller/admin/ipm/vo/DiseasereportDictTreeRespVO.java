package com.rs.module.ihc.controller.admin.ipm.vo;

import com.rs.framework.mybatis.annotation.Query;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(description = "管理后台 - 所内就医-报病字典管理 Response VO")
@Data
public class DiseasereportDictTreeRespVO {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    private String diseaseType;
    @ApiModelProperty("疾病症状")
    private String diseaseSymptom;
    @ApiModelProperty("是否内置")
    private String isBuiltIn;
    @Query(lbMark = "ihc:bbzdgllb",beanClass = DiseasereportDictRespVO.class)
    public List<DiseasereportDictRespVO> children;
}
