package com.rs.module.ihc.controller.admin.hc.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 五项体检-附件 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class HealthCheckupFileRespVO extends BaseVO implements TransPojo{

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("体检单id，对应ihc_hc_health_checkup.id")
    private String checkupId;

    @ApiModelProperty("检查类型 1=常规检查 2=血常规 3=B超 4=肝、胆、脾、胰 5=双肾、输尿管、膀胱 6=心电图 7=常规心电图 8=X光 9=胸部 10=检查结论")
    private String checkupCategory;

    @ApiModelProperty("文件地址")
    private String fileUrl;
}
