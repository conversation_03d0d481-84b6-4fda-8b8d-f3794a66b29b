package com.rs.module.ihc.service.pm;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.MedicineDeliveryInDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.pm.MedicineDeliveryInDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 药房管理-顾送药品入库批次 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MedicineDeliveryInServiceImpl extends BaseServiceImpl<MedicineDeliveryInDao, MedicineDeliveryInDO> implements MedicineDeliveryInService {

    @Resource
    private MedicineDeliveryInDao medicineDeliveryInDao;

    @Override
    public String createMedicineDeliveryIn(MedicineDeliveryInSaveReqVO createReqVO) {
        // 插入
        MedicineDeliveryInDO medicineDeliveryIn = BeanUtils.toBean(createReqVO, MedicineDeliveryInDO.class);
        medicineDeliveryInDao.insert(medicineDeliveryIn);
        // 返回
        return medicineDeliveryIn.getId();
    }

    @Override
    public void updateMedicineDeliveryIn(MedicineDeliveryInSaveReqVO updateReqVO) {
        // 校验存在
        validateMedicineDeliveryInExists(updateReqVO.getId());
        // 更新
        MedicineDeliveryInDO updateObj = BeanUtils.toBean(updateReqVO, MedicineDeliveryInDO.class);
        medicineDeliveryInDao.updateById(updateObj);
    }

    @Override
    public void deleteMedicineDeliveryIn(String id) {
        // 校验存在
        validateMedicineDeliveryInExists(id);
        // 删除
        medicineDeliveryInDao.deleteById(id);
    }

    private void validateMedicineDeliveryInExists(String id) {
        if (medicineDeliveryInDao.selectById(id) == null) {
            throw new ServerException("药房管理-顾送药品入库批次数据不存在");
        }
    }

    @Override
    public MedicineDeliveryInDO getMedicineDeliveryIn(String id) {
        return medicineDeliveryInDao.selectById(id);
    }

    @Override
    public PageResult<MedicineDeliveryInDO> getMedicineDeliveryInPage(MedicineDeliveryInPageReqVO pageReqVO) {
        return medicineDeliveryInDao.selectPage(pageReqVO);
    }

    @Override
    public List<MedicineDeliveryInDO> getMedicineDeliveryInList(MedicineDeliveryInListReqVO listReqVO) {
        return medicineDeliveryInDao.selectList(listReqVO);
    }


}
