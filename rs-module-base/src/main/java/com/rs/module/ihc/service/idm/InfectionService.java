package com.rs.module.ihc.service.idm;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.idm.vo.*;
import com.rs.module.ihc.entity.idm.InfectionDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 传染病管理-传染病管理登记 Service 接口
 *
 * <AUTHOR>
 */
public interface InfectionService extends IBaseService<InfectionDO>{

    /**
     * 创建传染病管理-传染病管理登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createInfection(@Valid InfectionSaveReqVO createReqVO);


    /**
     * 获得传染病管理-传染病管理登记
     *
     * @param id 编号
     * @return 传染病管理-传染病管理登记
     */
    InfectionDO getInfection(String id);

}
