package com.rs.module.ihc.handler.pm;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.cons.CommonConstants;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineCodeImportVO;
import com.rs.module.ihc.controller.admin.pm.vo.ValidationResult;
import com.rs.module.ihc.entity.pm.IhcPmMedicineCode;
import com.rs.module.ihc.service.pm.IhcPmMedicineCodeBuilder;
import com.rs.module.ihc.service.pm.IhcPmMedicineCodeService;

/**
 * @ClassName IhcPmMedicineCodeValidate
 * @Description 插入或更新数据库
 * <AUTHOR>
 * @Date 2025/3/18 15:30
 * @Version 1.0
 */
public class IhcPmMedicineCodeToDb extends IhcPmMedicineCodeBuilder<MedicineCodeImportVO> {
    IhcPmMedicineCodeService ihcPmMedicineCodeService;

    public IhcPmMedicineCodeToDb(IhcPmMedicineCodeService ihcPmMedicineCodeService) {
        this.ihcPmMedicineCodeService = ihcPmMedicineCodeService;
    }

    @Override
    public ValidationResult importExcel(MedicineCodeImportVO toValidate) {
        IhcPmMedicineCode ihcPmMedicineCodeDb = ihcPmMedicineCodeService.getOne(
                new LambdaQueryWrapper<IhcPmMedicineCode>()
                        .eq(IhcPmMedicineCode::getApprovalNum, toValidate.getApprovalNum()));
        if (ihcPmMedicineCodeDb == null) {
            ihcPmMedicineCodeDb = new IhcPmMedicineCode();
        }
        CopyOptions copyOptions = CopyOptions.create();
        copyOptions.setIgnoreNullValue(true);
        BeanUtil.copyProperties(toValidate, ihcPmMedicineCodeDb, copyOptions);
        if (ihcPmMedicineCodeDb.getId() != null) {
            this.ihcPmMedicineCodeService.updateById(ihcPmMedicineCodeDb);
        } else {
            ihcPmMedicineCodeDb.setId(StringUtil.getGuid());
            ihcPmMedicineCodeDb.setIsDel(CommonConstants.CONSTANTS_FALSE);
            this.ihcPmMedicineCodeService.save(ihcPmMedicineCodeDb);
        }
        return checkNext(toValidate);
    }
}
