package com.rs.module.ihc.controller.admin.ipm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 所内就医-处方药品出库记录列表 Request VO")
@Data
public class PrescribeOutListReqVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("添加人姓名")
    private String addUserName;

    @ApiModelProperty("更新人姓名")
    private String updateUserName;

    @ApiModelProperty("处方id，对应ihc_ipm_prescribe.id")
    private String prescribeId;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("医嘱")
    private String doctorAdvice;

    @ApiModelProperty("处方说明")
    private String prescribeDescribe;

}
