package com.rs.module.ihc.controller.admin.pm.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rs.framework.mybatis.annotation.DbValid;
import com.rs.framework.mybatis.annotation.IdValid;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 药房管理-药品信息列表 Request VO")
@Data
public class MedicineListReqVO extends BaseVO {

    @IdValid()
    private String id;


    @ApiModelProperty("是否删除(0:否,1:是)")
    private Integer isDel;

    @ApiModelProperty("添加时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date[] addTime;

    @ApiModelProperty("添加人")
    private String addUser;

    @ApiModelProperty("更新人")
    private String updateUser;

    @ApiModelProperty("所属省级代码")
    private String proCode;

    @ApiModelProperty("所属省级名称")
    private String proName;

    @ApiModelProperty("所属市级代码")
    private String cityCode;

    @ApiModelProperty("所属市级名称")
    private String cityName;

    @ApiModelProperty("区域代码")
    private String regCode;

    @ApiModelProperty("区域名称")
    private String regName;

    @ApiModelProperty("机构名称")
    private String orgCode;

    @ApiModelProperty("机构名称")
    private String orgName;

    @ApiModelProperty("药房ID")
    private String pharmacyId;

    @ApiModelProperty("药品名称")
    private String medicineName;

    @ApiModelProperty("药品名称全拼")
    private String medicineSpellName;

    @ApiModelProperty("药品名称拼音首字母或者英文拼接")
    private String medicineNameFirstLetter;

    @ApiModelProperty("剂型（字典:剂型）")
    private String dosageForm;

    @ApiModelProperty("计量单位（字典：计量单位）")
    private String measurementUnit;

    @ApiModelProperty("规格")
    private String specs;

    @ApiModelProperty("生产单位")
    private String productUnit;

    @ApiModelProperty("批准文号")
    private String approvalNum;

    @ApiModelProperty("原批准文号")
    private String originalApprovalNum;

    @ApiModelProperty("药品本位码")
    private String medicineStandardCode;

    @ApiModelProperty("是否毒麻品 (0：否 1：是)")
    private Short drug;

    @ApiModelProperty("每日用量")
    private String dayDosage;

    @ApiModelProperty("每次用量")
    private String oneDosage;

    @ApiModelProperty("库存预警值 按最小单位")
    private BigDecimal inventoryWarnValue;

    @ApiModelProperty("是否可报损（0：否 1：是）")
    private Short reportableLoss;

    @ApiModelProperty("英文名称")
    private String medicineEnglishName;

    @ApiModelProperty("商品名称")
    private String productName;

    @ApiModelProperty("上市许可持有人")
    private String marketApprovalHolder;

    @ApiModelProperty("上市许可持有人地址")
    private String marketApprovalHolderAddress;

    @ApiModelProperty("生产地址")
    private String productAddress;

    @ApiModelProperty("药品类别")
    private String medicineCategory;

    @ApiModelProperty("药品本位编码备注")
    private String medicineStandardCodeRemark;

    @ApiModelProperty("药品别名")
    private String medicineAlias;

    @ApiModelProperty("包装单位")
    private String packageUnit;

    @ApiModelProperty("批准日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date[] approvalDate;

    @ApiModelProperty("总库存数量")
    private BigDecimal totalInventoryNum;

    @ApiModelProperty("是否有过期批次药 (0：否 1：是)")
    private Integer hasExpireDateBatch;

    @ApiModelProperty("最小计量单位（字典值：计量单位)")
    private String minMeasurementUnit;

    @ApiModelProperty("计量单位转最小计量单位转化比")
    private Float unitConversionRatio;

    @ApiModelProperty("是否属于敏感药 (0：否 1：是)")
    private Integer belongSensitive;

    @ApiModelProperty("是否属于精神药品 (0：否 1：是)")
    private Short paychoactieDrug;

    @DbValid(sql = "select id from ihc_ipm_visit where id = ${prisonId}", message = " prisonId 不存在")
    private String prisonId;

}
