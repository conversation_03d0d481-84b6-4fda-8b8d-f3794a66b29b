package com.rs.module.ihc.enums;


import com.rs.framework.common.exception.ServiceException;
import com.rs.framework.mybatis.util.BizAssert;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 所内就医处方状态
 * <p>
 * 字典type：  prescribe_status
 */
@Getter
@AllArgsConstructor
public enum InternalMedicalPrescribeStatusEnum {

    /**
     * 处方状态-未使用
     */
    NO_USE("0", "未使用"),

    /**
     * 处方状态-使用中
     */
    USE("1", "使用中"),

    /**
     * 处方状态-已作废
     */
    NULLIFY("2", "已作废"),

    /**
     * 处方状态-已暂停
     */
    PAUSE("3", "已暂停"),

    /**
     * 处方状态-已结束
     */
    FINISH("4", "已结束"),
    ;


    /**
     * 字典编码
     */
    private final String code;

    /**
     * 字典描述
     */
    private final String desc;

    // ---------------------------------------------------------------------------------------------------------------------------
    /**
     * 状态变更关系维护
     */
    private static final Map<String, List<String>> statusChanageMap;

    static {
        statusChanageMap = new HashedMap<>();
        // 未使用可以变更为全部状态
        statusChanageMap.put(NO_USE.code, Arrays.asList(NO_USE.code, USE.code, NULLIFY.code, PAUSE.code, FINISH.code));
        // 已使用不能变更为未使用
        statusChanageMap.put(USE.code, Arrays.asList(USE.code, NULLIFY.code, PAUSE.code, FINISH.code));
        // 作废后不能变为其他状态
        statusChanageMap.put(NULLIFY.code, Collections.singletonList(NULLIFY.code));
        // 暂停后可以变更为未使用和已使用
        statusChanageMap.put(PAUSE.code, Arrays.asList(NO_USE.code, USE.code, PAUSE.code));
        // 已结束不能变更为其他状态
        statusChanageMap.put(FINISH.code, Collections.singletonList(FINISH.code));
    }
    // ---------------------------------------------------------------------------------------------------------------------------

    /**
     * 校验处方状态是否合法
     */
    public static void checkIsExist(String prescribeStatus) {
        for (InternalMedicalPrescribeStatusEnum prescribeStatusEnum : values()) {
            if (Objects.equals(prescribeStatusEnum.code, prescribeStatus)) {
                return;
            }
        }
        throw new ServiceException(500,"未知的处方状态" + prescribeStatus);
    }


    /**
     * 获取状态描述，不存在的状态返回空
     */
    public static String getStatusDesc(String prescribeStatus) {
        for (InternalMedicalPrescribeStatusEnum prescribeStatusEnum : values()) {
            if (Objects.equals(prescribeStatusEnum.code, prescribeStatus)) {
                return prescribeStatusEnum.desc;
            }
        }
        return null;
    }

    /**
     * 校验当前状态是否允许出库和执行
     * 提示消息显示处方编号，适用于批量判断时判断哪个处方无法执行
     *
     * @param prescribeStatus 处方状态
     * @param prescribeNum    处方编号
     */
    public static void checkIsCanExecute(String prescribeStatus, String prescribeNum) {
        checkIsExist(prescribeStatus);
        String message = "";
        if (StringUtils.isNotBlank(prescribeNum)) {
            message = " 处方编号：" + prescribeNum;
        }
        BizAssert.isFalse(NULLIFY.code.equals(prescribeStatus), NULLIFY.desc + "处方无法执行" + message);
        BizAssert.isFalse(PAUSE.code.equals(prescribeStatus), PAUSE.desc + "处方无法执行" + message);
        BizAssert.isFalse(FINISH.code.equals(prescribeStatus), FINISH.desc + "处方无法执行" + message);
    }

    /**
     * 校验当前状态是否允许执行
     *
     * @param prescribeStatus 处方状态
     */
    public static void checkIsCanExecute(String prescribeStatus) {
        checkIsCanExecute(prescribeStatus, null);
    }


    /**
     * 检查当前状态是否可以修改
     *
     * @param prescribeStatus 处方状态
     */
    public static void checkIsCanUpdate(String prescribeStatus) {
        BizAssert.isFalse(NULLIFY.code.equals(prescribeStatus), NULLIFY.desc + "处方无法编辑");
        BizAssert.isFalse(FINISH.code.equals(prescribeStatus), FINISH.desc + "处方无法编辑");
    }

    /**
     * 判断两个状态之间是否可以转换
     *
     * @param sourceStatus 源处方状态
     * @param updateStatus 目标处方状态
     */
    public static void checkIsCanStatusChange(String sourceStatus, String updateStatus) {
        if (Objects.equals(sourceStatus, updateStatus)) {
            return;
        }
        checkIsExist(sourceStatus);
        List<String> canChangeStatusList = statusChanageMap.get(sourceStatus);
        if (!CollectionUtils.isEmpty(canChangeStatusList) && canChangeStatusList.contains(updateStatus)) {
            return;
        }
        throw new ServiceException(500,String.format("无法将处方状态[%s]变更为[%s]",
                getStatusDesc(sourceStatus), getStatusDesc(updateStatus)));
    }

    /**
     * 获取正常的处方状态
     */
    public static List<String> getValidStatusList() {
        return Arrays.asList(NO_USE.code, USE.code);
    }

    /**
     * 获取已经完结的处方状态
     */
    public static List<String> getTerminateStatusList() {
        return Arrays.asList(NULLIFY.code, FINISH.code);
    }

}
