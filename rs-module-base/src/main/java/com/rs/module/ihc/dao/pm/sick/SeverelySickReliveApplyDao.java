package com.rs.module.ihc.dao.pm.sick;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.pm.sick.SeverelySickReliveApplyDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.pm.sick.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 重特病号解除申请 Dao
*
* <AUTHOR>
*/
@Mapper
public interface SeverelySickReliveApplyDao extends IBaseDao<SeverelySickReliveApplyDO> {


    default PageResult<SeverelySickReliveApplyDO> selectPage(SeverelySickReliveApplyPageReqVO reqVO) {
        Page<SeverelySickReliveApplyDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<SeverelySickReliveApplyDO> wrapper = new LambdaQueryWrapperX<SeverelySickReliveApplyDO>()
            .eqIfPresent(SeverelySickReliveApplyDO::getManageId, reqVO.getManageId())
            .eqIfPresent(SeverelySickReliveApplyDO::getPrisonerId, reqVO.getPrisonerId())
            .betweenIfPresent(SeverelySickReliveApplyDO::getRelieveApplyTime, reqVO.getRelieveApplyTime())
            .eqIfPresent(SeverelySickReliveApplyDO::getRelieveReason, reqVO.getRelieveReason())
            .eqIfPresent(SeverelySickReliveApplyDO::getRelieveUserId, reqVO.getRelieveUserId())
            .likeIfPresent(SeverelySickReliveApplyDO::getRelieveUserName, reqVO.getRelieveUserName())
            .eqIfPresent(SeverelySickReliveApplyDO::getFinalApproveResult, reqVO.getFinalApproveResult())
            .betweenIfPresent(SeverelySickReliveApplyDO::getRelieveTime, reqVO.getRelieveTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(SeverelySickReliveApplyDO::getAddTime);
        }
        Page<SeverelySickReliveApplyDO> severelySickReliveApplyPage = selectPage(page, wrapper);
        return new PageResult<>(severelySickReliveApplyPage.getRecords(), severelySickReliveApplyPage.getTotal());
    }
    default List<SeverelySickReliveApplyDO> selectList(SeverelySickReliveApplyListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SeverelySickReliveApplyDO>()
            .eqIfPresent(SeverelySickReliveApplyDO::getManageId, reqVO.getManageId())
            .eqIfPresent(SeverelySickReliveApplyDO::getPrisonerId, reqVO.getPrisonerId())
            .betweenIfPresent(SeverelySickReliveApplyDO::getRelieveApplyTime, reqVO.getRelieveApplyTime())
            .eqIfPresent(SeverelySickReliveApplyDO::getRelieveReason, reqVO.getRelieveReason())
            .eqIfPresent(SeverelySickReliveApplyDO::getRelieveUserId, reqVO.getRelieveUserId())
            .likeIfPresent(SeverelySickReliveApplyDO::getRelieveUserName, reqVO.getRelieveUserName())
            .eqIfPresent(SeverelySickReliveApplyDO::getFinalApproveResult, reqVO.getFinalApproveResult())
            .betweenIfPresent(SeverelySickReliveApplyDO::getRelieveTime, reqVO.getRelieveTime())
        .orderByDesc(SeverelySickReliveApplyDO::getAddTime));    }


    }
