package com.rs.module.ihc.controller.admin.ephe.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 卫生防疫与健康教育-卫生消毒登记分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DisinfectPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("夏季每周洗热水澡次数")
    private String xjmzxrszcs;

    @ApiModelProperty("每15天热水澡次数")
    private Integer mswtrszcs;

    @ApiModelProperty("每周晾晒被褥次数	")
    private Integer mzlsbrcs;

    @ApiModelProperty("每季度拆洗被褥次数")
    private Integer mjdcxbrcs;

    @ApiModelProperty("每周消毒次数")
    private Integer mzxdcs;

    @ApiModelProperty("登记民警身份证号")
    private String operatePoliceSfzh;

    @ApiModelProperty("登记民警")
    private String operatePolice;

    @ApiModelProperty("登记时间")
    private Date[] operateTime;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
