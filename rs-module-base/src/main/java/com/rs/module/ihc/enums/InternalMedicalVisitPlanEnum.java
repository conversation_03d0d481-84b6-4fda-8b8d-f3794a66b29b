package com.rs.module.ihc.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> LiuWenxing
 * @create 2024/11/5 11:06
 */
@Getter
@AllArgsConstructor
public enum InternalMedicalVisitPlanEnum {
    /**
     * 频率-1天2次
     */
    FREQUENCY_TWICE_ONE_DAY("1", 12L),

    /**
     * 频率-1天1次
     */
    FREQUENCY_ONCE_ONE_DAY("2", 24L),

    /**
     * 频率-2天1次
     */
    FREQUENCY_ONCE_TWO_DAY("3", 48L),

    /**
     * 频率-3天1次
     */
    FREQUENCY_ONCE_THREE_DAY("4", 72L),

    /**
     * 频率-3天1次
     */
    FREQUENCY_THREE_TIME_ONE_DAY("5", 8L),
    ;
    /**
     * 频率
     */
    private String frequency;
    /**
     * 时间差（小时）
     */
    private Long timeDifference;

    public static Long getTimeDifferenceByFrequency(String frequency) {
        for (InternalMedicalVisitPlanEnum anEnum : InternalMedicalVisitPlanEnum.values()) {
            if (Objects.equals(anEnum.getFrequency(), frequency)) {
                return anEnum.timeDifference;
            }
        }
        return null;
    }
}
