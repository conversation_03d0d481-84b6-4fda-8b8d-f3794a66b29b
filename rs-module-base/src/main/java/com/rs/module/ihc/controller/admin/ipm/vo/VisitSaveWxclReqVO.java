package com.rs.module.ihc.controller.admin.ipm.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.common.annotation.DefaultValueDate;
import com.rs.framework.common.annotation.DefaultValueString;
import com.rs.framework.common.annotation.SessionUserIdCard;
import com.rs.framework.common.annotation.SessionUserName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "管理后台 - 所内就医-现场巡检新增/修改 Request VO")
@Data
public class VisitSaveWxclReqVO implements TransPojo {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("来源（1:所内就医预约，2:手动新增）")
    @DefaultValueString("2")
    private String source;

    @ApiModelProperty("预约id，对应ihc_ipm_internal_medical_appointment.id")
    private String appointmentId;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("在押人员编号")
    private String rybh;

    @ApiModelProperty("在押人员名称")
    private String ryxm;

    @ApiModelProperty("是否重病号（ 0：否 ，1：是）")
    private Integer sickType;

    @ApiModelProperty("报病时间")
    private Date diseaseTime;

    @ApiModelProperty("报病原因")
    private String diseaseReason;

    @ApiModelProperty("主诉")
    private String mainComplaint;

    @ApiModelProperty("巡诊情况")
    private String visitState;

    @ApiModelProperty("巡诊处理方式")
    private String visitProcessMethod;

    @ApiModelProperty("巡诊时间")
    @DefaultValueDate
    private Date visitTime;

    @SessionUserIdCard
    @ApiModelProperty("巡诊人证件号码")
    private String visitUserid;

    @SessionUserName
    @ApiModelProperty("巡诊人名称")
    private String visitUserName;

    @ApiModelProperty("巡诊结论")
    private String visitConclusion;

    @ApiModelProperty("监所id")
    private String prisonId;

    @ApiModelProperty("械具使用建议")
    private String toolUseAdvise;

    @ApiModelProperty("械具解除原因")
    private String toolUseCancelReason;

    @ApiModelProperty("第三方数据id")
    private String extId;


}
