package com.rs.module.ihc.service.pm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.BizException;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.common.util.spring.SpringUtils;
import com.rs.framework.mybatis.util.BizAssert;
import com.rs.module.ihc.constant.BooleanIntConstant;
import com.rs.module.ihc.constant.IhcsMedicineConstant;
import com.rs.module.ihc.controller.admin.ipm.bo.UpdateMedicineInInventoryNumBO;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.dao.pm.MedicineDao;
import com.rs.module.ihc.dao.pm.MedicineInDao;
import com.rs.module.ihc.entity.pm.MedicineDO;
import com.rs.module.ihc.entity.pm.MedicineInDO;
import com.rs.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * 药房管理-药品入库批次 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MedicineInServiceImpl extends BaseServiceImpl<MedicineInDao, MedicineInDO> implements MedicineInService {

    @Resource
    private MedicineInDao medicineInDao;

    @Resource
    private MedicineDao medicineDao;

    private final ReentrantLock lock = new ReentrantLock();


    @Override
    public String createMedicineIn(MedicineInSaveReqVO createReqVO) {

        MedicineInDO medicineInDO = getOne(new LambdaQueryWrapper<MedicineInDO>()
                .eq(MedicineInDO::getMedicineId, createReqVO.getId())
                .eq(MedicineInDO::getBatchCode, createReqVO.getBatchCode()));
        if (medicineInDO != null) {
            throw new ServerException("该药品已入库，请勿重复入库");
        }

        MedicineDO ihcsMedicine = medicineDao.selectById(createReqVO.getMedicineId());
        BizAssert.notNull(ihcsMedicine, "药品不存在");
        // 药品库存总量
        BigDecimal sourceTotalInventoryNum = ihcsMedicine.getTotalInventoryNum();

        // 插入
        MedicineInDO medicineIn = BeanUtils.toBean(createReqVO, MedicineInDO.class);
        // 进行总量计算以及入库操作
        BigDecimal targetTotalInventoryNum = sourceTotalInventoryNum;

        // 该批次添加后的药品的总数
        targetTotalInventoryNum = targetTotalInventoryNum.add(createReqVO.getInNum());
        this.checkMedicineIn(createReqVO);
        medicineIn.setInventoryNum(createReqVO.getInNum());
        medicineIn.setTotalInventoryNum(targetTotalInventoryNum);
        medicineInDao.insert(medicineIn);

        //增加库存
        MedicineDO medicine = medicineDao.selectById(createReqVO.getMedicineId());
        medicine.setTotalInventoryNum(medicine.getTotalInventoryNum().add(createReqVO.getInNum()));
        medicineDao.updateById(medicine);
        // 返回
        return medicineIn.getId();
    }

    @Override
    public void updateMedicineIn(MedicineInSaveReqVO updateReqVO) {
        // 校验存在
        validateMedicineInExists(updateReqVO.getId());
        // 更新
        MedicineInDO updateObj = BeanUtils.toBean(updateReqVO, MedicineInDO.class);
        medicineInDao.updateById(updateObj);
    }

    @Override
    public void deleteMedicineIn(String id) {
        // 校验存在
        validateMedicineInExists(id);
        // 删除
        medicineInDao.deleteById(id);
    }

    private void validateMedicineInExists(String id) {
        if (medicineInDao.selectById(id) == null) {
            throw new ServerException("药房管理-药品入库批次数据不存在");
        }
    }

    @Override
    public MedicineInDO getMedicineIn(String id) {
        return medicineInDao.selectById(id);
    }

    @Override
    public MedicineInRespVO getMedicineInDetail(String id) {
        MedicineInDO medicineIn = medicineInDao.selectById(id);
        return BeanUtils.toBean(medicineIn, MedicineInRespVO.class);
    }

    @Override
    public PageResult<MedicineInDO> getMedicineInPage(MedicineInPageReqVO pageReqVO) {
        return medicineInDao.selectPage(pageReqVO);
    }

    @Override
    public List<MedicineInDO> getMedicineInList(MedicineInListReqVO listReqVO) {
        return medicineInDao.selectList(listReqVO);
    }

    @Override
    public List<MedicineInDO> ypbsList(MedicineInYpbsReqVO listReqVO) {
        LambdaQueryWrapper<MedicineInDO> queryWrapper = Wrappers.lambdaQuery(MedicineInDO.class)
                .eq(MedicineInDO::getMedicineId, listReqVO.getMedicineId())
                .gt(MedicineInDO::getInventoryNum, 0);
        if (StringUtils.isNotEmpty(listReqVO.getBatchCode())) {
            queryWrapper.like(MedicineInDO::getBatchCode, listReqVO.getBatchCode());
        }
        queryWrapper.orderByDesc(MedicineInDO::getInDate);
        return this.list(queryWrapper);
    }

    @Override
    public List<String> getExistExpireBatchMedicineId(Date expireDate, List<String> medicineIdList) {
        return medicineInDao.getExistExpireBatchMedicineId(expireDate, medicineIdList);
    }

    @Override
    public List<MedicineInDO> selectFirstNoExpireMedicineBatch(Collection<String> medicineIdList, Date expireDate) {
        return medicineInDao.selectFirstNoExpireMedicineBatch(medicineIdList, expireDate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCompareAndSetInventoryNum(Collection<UpdateMedicineInInventoryNumBO> updateMedicineInInventoryNumBOList) {
        for (UpdateMedicineInInventoryNumBO updateMedicineInInventoryNumBO : updateMedicineInInventoryNumBOList) {
            String id = updateMedicineInInventoryNumBO.getId();
            boolean update = this.lambdaUpdate()
                    .set(MedicineInDO::getInventoryNum, updateMedicineInInventoryNumBO.getTargetInventoryNum())
                    .set(MedicineInDO::getUpdateTime, LocalDateTime.now())
                    .eq(MedicineInDO::getId, id)
                    .eq(MedicineInDO::getInventoryNum, updateMedicineInInventoryNumBO.getSourceInventoryNum())
                    .update();
            BizAssert.isTrue(update, "药品入库批次id[" + id + "]库存同时有其他操作修改，操作失败请重试");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void selectExpireBatchAndUpdateMedicineFlag(List<String> medicineIdList) {
        if (!CollectionUtils.isEmpty(medicineIdList)) {
            medicineDao.update(null, Wrappers.lambdaUpdate(MedicineDO.class)
                    .set(MedicineDO::getUpdateTime, LocalDateTime.now())
                    .set(MedicineDO::getHasExpireDateBatch, BooleanIntConstant.FALSE)
                    .in(MedicineDO::getId, medicineIdList));
        }
        Date nowDate = new Date();
        List<String> existExpireBatchMedicineIdList = this.getBaseMapper().getExistExpireBatchMedicineId(nowDate, medicineIdList);
        if (CollectionUtils.isEmpty(existExpireBatchMedicineIdList)) {
            return;
        }
        medicineDao.update(null, Wrappers.lambdaUpdate(MedicineDO.class)
                .set(MedicineDO::getHasExpireDateBatch, BooleanIntConstant.TRUE)
                .set(MedicineDO::getUpdateTime, LocalDateTime.now())
                .in(MedicineDO::getId, existExpireBatchMedicineIdList));
    }

    @Override
    public List<String> batchSaveMedicineIn(List<SaveMedicineInReqVO> saveMedicineInReqVOList, String medicineId, boolean ignoreApprove) {
        BizAssert.notEmpty(saveMedicineInReqVOList, "入库登记数据不能为空");
        BizAssert.notNull(medicineId, "药品id不能为空");
        MedicineDO ihcsMedicine = medicineDao.selectById(medicineId);
        BizAssert.notNull(ihcsMedicine, "药品不存在");
        List<MedicineInDO> ihcsMedicineIns = new ArrayList<>();

        try {
            if (!lock.tryLock(9, TimeUnit.SECONDS)) {
                throw new BizException("与其他入库同时操作冲突，请重试");
            }
            try {
                MedicineInService bean = SpringUtils.getBean(MedicineInService.class);
                // 执行带事务和生成唯一批次的入库操作
                ihcsMedicineIns = bean.batchSaveMedicineIn0(saveMedicineInReqVOList, ihcsMedicine, ignoreApprove);
            } finally {
                lock.unlock();
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        return ihcsMedicineIns.stream().map(MedicineInDO::getId).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<MedicineInDO> batchSaveMedicineIn0(List<SaveMedicineInReqVO> saveMedicineInReqVOList, MedicineDO ihcsMedicine, boolean ignoreApprove) {
        List<MedicineInDO> ihcsMedicineInList = new ArrayList<>();
        String medicineId = ihcsMedicine.getId();
        // 药品库存总量
        BigDecimal sourceTotalInventoryNum = ihcsMedicine.getTotalInventoryNum();
        BigDecimal targetTotalInventoryNum;
        // 序列号前面的日期长度
        final int length = DateUtil.DATE_PATTERN.length();
        // 查询各个入库日期数据库中的最大的入库批次代码
        Map<LocalDate, Long> selectBatchCodeSequenceMap = this.getBaseMapper()
                .getMaxBatchCode(saveMedicineInReqVOList.stream()
                        .map( e->  e != null ? e.getInDate().toInstant().atZone(ZoneId.systemDefault()) // 关联到系统默认时区
                                .toLocalDate() : null)
                        .collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(GetMaxBatchCodeVO::getInDate,
                        vo -> Long.parseLong(vo.getMaxBatchCode().substring(length))));
        // 进行总量计算以及入库操作
        targetTotalInventoryNum = sourceTotalInventoryNum;
        String inStatus = !ignoreApprove ? IhcsMedicineConstant.MEDICINE_IN_STATUS_START : IhcsMedicineConstant.MEDICINE_IN_STATUS_END;
        for (SaveMedicineInReqVO saveMedicineInDTO : saveMedicineInReqVOList) {
            // 该批次添加后的药品的总数
            targetTotalInventoryNum = targetTotalInventoryNum.add(saveMedicineInDTO.getInNum());
            MedicineInDO ihcsMedicineIn = convertToIhcsMedicineIn(saveMedicineInDTO);
            ihcsMedicineIn.setInventoryNum(saveMedicineInDTO.getInNum());
            ihcsMedicineIn.setBatchCode(this.getNextMaxBatchCodeByDate(saveMedicineInDTO.getInDate(), selectBatchCodeSequenceMap));
            ihcsMedicineIn.setMedicineId(medicineId);
            ihcsMedicineIn.setTotalInventoryNum(targetTotalInventoryNum);
            ihcsMedicineIn.setInStatus(inStatus);
            ihcsMedicineInList.add(ihcsMedicineIn);
        }
        this.saveBatch(ihcsMedicineInList);

        // 需要更新药品总量
        medicineDao.compareAndSetMedicineInventoryNum(medicineId, sourceTotalInventoryNum, targetTotalInventoryNum);

        return ihcsMedicineInList;
    }

    private String getNextMaxBatchCodeByDate( Date date, Map<LocalDate, Long> localDateStringMap) {
        LocalDate inDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Long lastSequence = Optional.ofNullable(localDateStringMap.get(inDate))
                .orElse(0L) + 1;
        String dateFormat = DateUtil.DATE_FORMATTER.format(inDate);
        String lastBatchCode = String.format("%s%03d", dateFormat, lastSequence);
        localDateStringMap.put(inDate, lastSequence);
        return lastBatchCode;
    }

    /**
     * 将SaveMedicineInDTO对象转换为IhcsMedicineIn对象。
     */
    private MedicineInDO convertToIhcsMedicineIn(SaveMedicineInReqVO saveMedicineInDTO) {
        return MedicineInDO.builder()
                .inNum(saveMedicineInDTO.getInNum())
                .supplierId(Long.parseLong( saveMedicineInDTO.getSupplierId()))
                .supplierName(saveMedicineInDTO.getSupplierName())
                .voucherCode(saveMedicineInDTO.getVoucherCode())
                .purchasePrice(saveMedicineInDTO.getPurchasePrice())
                .wholesalePrice(saveMedicineInDTO.getWholesalePrice())
                .transferPrice(saveMedicineInDTO.getTransferPrice())
                .retailPrice(saveMedicineInDTO.getRetailPrice())
                .inDate(saveMedicineInDTO.getInDate())
                .expireDate(saveMedicineInDTO.getExpireDate())
                .inStorageMethod(saveMedicineInDTO.getInStorageMethod())
                .transferMethod(saveMedicineInDTO.getTransferMethod())
                .medicinePlace(saveMedicineInDTO.getMedicinePlace())
                .build();
    }


    /**
     * 校验入库登记数据
     *
     * @param saveMedicineInDTO 入库登记数据
     */
    private void checkMedicineIn(MedicineInSaveReqVO saveMedicineInDTO) {
        Date expireDate = saveMedicineInDTO.getExpireDate();
        BizAssert.isTrue(expireDate.after(new Date()) || expireDate.equals(new Date()), "批次有效期不能低于当天");
    }

}
