package com.rs.module.ihc.entity.pm.sick;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 重特病号解除申请 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_pm_severely_sick_relive_apply")
@KeySequence("ihc_pm_severely_sick_relive_apply_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SeverelySickReliveApplyDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 关联重特病号管理表id
     */
    private String manageId;
    /**
     * 被监管人id
     */
    private String prisonerId;
    /**
     * 解除申请时间
     */
    private Date relieveApplyTime;
    /**
     * 解除原因
     */
    private String relieveReason;
    /**
     * 解除申请人id
     */
    private Long relieveUserId;
    /**
     * 解除申请人姓名
     */
    private String relieveUserName;
    /**
     * 最终审批结果 字典值：is_agree
     */
    private String finalApproveResult;
    /**
     * 解除时间
     */
    private Date relieveTime;

}
