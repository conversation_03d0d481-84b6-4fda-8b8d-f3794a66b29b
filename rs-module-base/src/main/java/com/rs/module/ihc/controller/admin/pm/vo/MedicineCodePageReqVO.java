package com.rs.module.ihc.controller.admin.pm.vo;

import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel(description = "管理后台 - 药房管理-国家药品编码本位码信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MedicineCodePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("药品来源类型（1： 代表国产，2 ：代表进口）")
    private Short originType;

    @ApiModelProperty("药品批准文号")
    private String approvalNum;

    @ApiModelProperty("产品名称")
    private String medicineName;

    @ApiModelProperty("剂型（字典:剂型）")
    private String dosageForm;

    @ApiModelProperty("规格")
    private String specs;

    @ApiModelProperty("上市许可持有人")
    private String marketApprovalHolder;

    @ApiModelProperty("上市许可持有人英文")
    private String marketApprovalHolderEn;

    @ApiModelProperty("生产单位")
    private String productUnit;

    @ApiModelProperty("生产单位英文")
    private String productUnitEn;

    @ApiModelProperty("药品本位码")
    private String medicineStandardCode;

    @ApiModelProperty("药品本位编码备注")
    private String medicineStandardCodeRemark;

    @ApiModelProperty("监所id")
    private String prisonId;

    @ApiModelProperty("注册证号")
    private String zczh;

}
