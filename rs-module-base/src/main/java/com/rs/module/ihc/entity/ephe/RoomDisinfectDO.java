package com.rs.module.ihc.entity.ephe;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 卫生防疫与健康教育--监室消毒 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_ephe_room_disinfect")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ihc_ephe_room_disinfect")
public class RoomDisinfectDO extends BaseDO {
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 数据来源(实战平台:1, 仓外屏:2, 仓内屏:3)
     */
    private String dataSources;
    /**
     * 监室ID
     */
    private String roomId;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 消毒方式
     */
    private String disinfectMethod;
    /**
     * 消毒民警身份证号
     */
    private String disinfectPoliceSfzh;
    /**
     * 消毒民警姓名
     */
    private String disinfectPolice;
    /**
     * disinfect_time
     */
    private Date disinfectTime;

}
