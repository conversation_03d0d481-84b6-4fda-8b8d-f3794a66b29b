package com.rs.module.ihc.service.pm.sick;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.ihc.controller.admin.pm.sick.vo.*;
import com.rs.module.ihc.entity.pm.sick.SeverelySickManageDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.pm.sick.SeverelySickManageDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 重特病号管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SeverelySickManageServiceImpl extends BaseServiceImpl<SeverelySickManageDao, SeverelySickManageDO> implements SeverelySickManageService {

    @Resource
    private SeverelySickManageDao severelySickManageDao;

    @Override
    public String createSeverelySickManage(SeverelySickManageSaveReqVO createReqVO) {
        // 插入
        SeverelySickManageDO severelySickManage = BeanUtils.toBean(createReqVO, SeverelySickManageDO.class);
        severelySickManageDao.insert(severelySickManage);
        // 返回
        return severelySickManage.getId();
    }

    @Override
    public void updateSeverelySickManage(SeverelySickManageSaveReqVO updateReqVO) {
        // 校验存在
        validateSeverelySickManageExists(updateReqVO.getId());
        // 更新
        SeverelySickManageDO updateObj = BeanUtils.toBean(updateReqVO, SeverelySickManageDO.class);
        severelySickManageDao.updateById(updateObj);
    }

    @Override
    public void deleteSeverelySickManage(String id) {
        // 校验存在
        validateSeverelySickManageExists(id);
        // 删除
        severelySickManageDao.deleteById(id);
    }

    private void validateSeverelySickManageExists(String id) {
        if (severelySickManageDao.selectById(id) == null) {
            throw new ServerException("重特病号管理数据不存在");
        }
    }

    @Override
    public SeverelySickManageDO getSeverelySickManage(String id) {
        return severelySickManageDao.selectById(id);
    }

    @Override
    public PageResult<SeverelySickManageDO> getSeverelySickManagePage(SeverelySickManagePageReqVO pageReqVO) {
        return severelySickManageDao.selectPage(pageReqVO);
    }

    @Override
    public List<SeverelySickManageDO> getSeverelySickManageList(SeverelySickManageListReqVO listReqVO) {
        return severelySickManageDao.selectList(listReqVO);
    }


}
