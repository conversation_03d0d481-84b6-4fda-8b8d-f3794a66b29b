package com.rs.module.ihc.entity.pm;

import lombok.*;
import java.util.*;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 药房管理-顾送药品报损 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_pm_medicine_delivery_loss")
@KeySequence("ihc_pm_medicine_delivery_loss_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MedicineDeliveryLossDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 药品id，对应ichs_medicine.id
     */
    private String medicineId;
    /**
     * 入库批次id，对应ichs_medicine_in.id
     */
    private String medicineInId;
    /**
     * 报损原因
     */
    private String lossReason;
    /**
     * 报损数量
     */
    private BigDecimal lossNum;
    /**
     * 报损时间
     */
    private Date lossTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 批次库存数量 最小单位
     */
    private BigDecimal inventoryNum;
    /**
     * 当前药品总库存数量 最小单位
     */
    private BigDecimal totalInventoryNum;

}
