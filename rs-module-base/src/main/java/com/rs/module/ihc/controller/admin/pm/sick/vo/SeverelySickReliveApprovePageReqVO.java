package com.rs.module.ihc.controller.admin.pm.sick.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 重特病号解除申请审批分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SeverelySickReliveApprovePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("关联重特病号解除申请表id")
    private String reliveApplyId;

    @ApiModelProperty("业务状态 字典值：severely_sick_business_status")
    private String businessStatus;

    @ApiModelProperty("审批结果 字典值：is_agree")
    private String approveResult;

    @ApiModelProperty("审批人id")
    private Long approveUserId;

    @ApiModelProperty("审批人姓名")
    private String approveUserName;

    @ApiModelProperty("审批意见")
    private String approveOpinion;

    @ApiModelProperty("审批时间")
    private Date[] approveTime;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
