package com.rs.module.ihc.service.ipm;


import com.rs.module.ihc.service.ipm.dto.*;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

public interface IhcsMedicalDeliveryRemindService {


    /**
     * 执行医嘱/处方
     *
     * @param executePrescribeDTO 医嘱/处方信息
     */
    void executePrescribe(ExecutePrescribeDTO executePrescribeDTO);


    /**
     * 服药签名
     *
     * @param executePrescribeSignDTO 医嘱/处方信息
     */
    void executePrescribeSign(ExecutePrescribeSignDTO executePrescribeSignDTO);

    /**
     * 保存服药视频
     *
     * @param executePrescribeVideoDTO 医嘱/处方信息
     */
    void executePrescribeVideo(ExecutePrescribeVideoDTO executePrescribeVideoDTO);

    /**
     * 批量执行医嘱/处方
     *
     * @param batchExecutePrescribeDTO 医嘱/处方信息
     */
    void batchExecutePrescribe(BatchExecutePrescribeDTO batchExecutePrescribeDTO);


    /**
     * 临床送药登记
     *
     * @param prescribeClinicalRegisterDTO 登记信息
     */
    void prescribeClinicalRegister(PrescribeClinicalRegisterDTO prescribeClinicalRegisterDTO);

    /**
     * 拒绝领药/服药
     *
     * @param refuseExecutePrescribeDTO 拒绝信息
     */
    void refuseExecutePrescribe(RefuseExecutePrescribeDTO refuseExecutePrescribeDTO);

    /**
     * 上传拒绝领药/服药告知书
     *
     * @param signRefuseExecutePrescribeDTO 拒绝信息
     */
    void signRefuseExecutePrescribe(SignRefuseExecutePrescribeDTO signRefuseExecutePrescribeDTO);

    /**
     * 拒绝服药
     *
     * @param executePrescribeSignDTO
     */
    void refusalToReceiveMedication(@RequestBody @Valid RefusalToReceiveMedicationDTO executePrescribeSignDTO);


}
