package com.rs.module.ihc.service.ipm;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.CaseTemplateDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 所内就医-病例模板 Service 接口
 *
 * <AUTHOR>
 */
public interface CaseTemplateService extends IBaseService<CaseTemplateDO>{

    /**
     * 创建所内就医-病例模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCaseTemplate(@Valid CaseTemplateSaveReqVO createReqVO);

    /**
     * 更新所内就医-病例模板
     *
     * @param updateReqVO 更新信息
     */
    void updateCaseTemplate(@Valid CaseTemplateSaveReqVO updateReqVO);

    /**
     * 删除所内就医-病例模板
     *
     * @param id 编号
     */
    void deleteCaseTemplate(String id);

    /**
     * 获得所内就医-病例模板
     *
     * @param id 编号
     * @return 所内就医-病例模板
     */
    CaseTemplateDO getCaseTemplate(String id);

    /**
    * 获得所内就医-病例模板分页
    *
    * @param pageReqVO 分页查询
    * @return 所内就医-病例模板分页
    */
    PageResult<CaseTemplateDO> getCaseTemplatePage(CaseTemplatePageReqVO pageReqVO);

    /**
    * 获得所内就医-病例模板列表
    *
    * @param listReqVO 查询条件
    * @return 所内就医-病例模板列表
    */
    List<CaseTemplateDO> getCaseTemplateList(CaseTemplateListReqVO listReqVO);


}
