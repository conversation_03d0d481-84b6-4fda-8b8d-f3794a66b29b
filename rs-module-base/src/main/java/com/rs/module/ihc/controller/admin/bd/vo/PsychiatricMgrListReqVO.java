package com.rs.module.ihc.controller.admin.bd.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 精神病异常管理列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PsychiatricMgrListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("会诊初步诊断 字典ZD_JSB_HZCBZD")
    private String cnsltPreDiag;

    @ApiModelProperty("治疗后是否好转 1是0否")
    private String treatmentOutcome;

    @ApiModelProperty("列管等级  字典：ZD_JSB_LGDJ")
    private String managementLevel;

    @ApiModelProperty("列控时间")
    private Date[] controlTime;

    @ApiModelProperty("列控原因")
    private String controlReason;

    @ApiModelProperty("登记民警身份证号")
    private String operatePoliceSfzh;

    @ApiModelProperty("登记民警")
    private String operatePolice;

    @ApiModelProperty("登记时间")
    private Date[] operateTime;

}
