package com.rs.module.ihc.entity.pm;

import lombok.*;
import java.util.*;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 药房管理-顾送药品信息 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_pm_medicine_delivery")
@KeySequence("ihc_pm_medicine_delivery_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MedicineDeliveryDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 药房ID
     */
    private String pharmacyId;
    /**
     * 药品名称
     */
    private String medicineName;
    /**
     * 药品名称全拼
     */
    private String medicineSpellName;
    /**
     * 药品名称拼音首字母或者英文拼接
     */
    private String medicineNameFirstLetter;
    /**
     * 剂型（字典:剂型）
     */
    private String dosageForm;
    /**
     * 计量单位（字典：计量单位）
     */
    private String measurementUnit;
    /**
     * 规格
     */
    private String specs;
    /**
     * 生产单位
     */
    private String productUnit;
    /**
     * 批准文号
     */
    private String approvalNum;
    /**
     * 原批准文号
     */
    private String originalApprovalNum;
    /**
     * 药品本位码
     */
    private String medicineStandardCode;
    /**
     * 是否毒麻品 (0：否 1：是)
     */
    private Short drug;
    /**
     * 每日用量
     */
    private String dayDosage;
    /**
     * 每次用量
     */
    private String oneDosage;
    /**
     * 库存预警值 按最小单位
     */
    private BigDecimal inventoryWarnValue;
    /**
     * 是否可报损（0：否 1：是）
     */
    private Short reportableLoss;
    /**
     * 英文名称
     */
    private String medicineEnglishName;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 上市许可持有人
     */
    private String marketApprovalHolder;
    /**
     * 上市许可持有人地址
     */
    private String marketApprovalHolderAddress;
    /**
     * 生产地址
     */
    private String productAddress;
    /**
     * 药品类别
     */
    private String medicineCategory;
    /**
     * 药品本位编码备注
     */
    private String medicineStandardCodeRemark;
    /**
     * 药品别名
     */
    private String medicineAlias;
    /**
     * 包装单位
     */
    private String packageUnit;
    /**
     * 批准日期
     */
    private Date approvalDate;
    /**
     * 总库存数量
     */
    private BigDecimal totalInventoryNum;
    /**
     * 是否有过期批次药 (0：否 1：是)
     */
    private Integer hasExpireDateBatch;
    /**
     * 最小计量单位（字典值：计量单位)
     */
    private String minMeasurementUnit;
    /**
     * 计量单位转最小计量单位转化比
     */
    private Float unitConversionRatio;
    /**
     * 在押人员编码，顾送药专药专用的人
     */
    private String rybh;
    /**
     * 在押人员名称，顾送药专药专用的人
     */
    private String ryxm;
    /**
     * 是否属于敏感药 (0：否 1：是)
     */
    private Integer belongSensitive;
    /**
     * 监所id
     */
    private String prisonId;

}
