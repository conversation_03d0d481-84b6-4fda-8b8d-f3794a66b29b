package com.rs.module.ihc.service.pm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.util.BizAssert;
import com.rs.module.ihc.constant.BooleanIntConstant;
import com.rs.module.ihc.controller.admin.ipm.bo.UpdateMedicineInInventoryNumBO;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.dao.pm.PharmacyMedicineDao;
import com.rs.module.ihc.dao.pm.PharmacyMedicineInDao;
import com.rs.module.ihc.entity.pm.PharmacyMedicineDO;
import com.rs.module.ihc.entity.pm.PharmacyMedicineInDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.List;


/**
 * 大药房管理-药品入库批次 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PharmacyMedicineInServiceImpl extends BaseServiceImpl<PharmacyMedicineInDao, PharmacyMedicineInDO> implements PharmacyMedicineInService {

    @Resource
    private PharmacyMedicineInDao pharmacyMedicineInDao;

    @Resource
    private PharmacyMedicineDao pharmacyMedicineDao;

    @Override
    public String createPharmacyMedicineIn(PharmacyMedicineInSaveReqVO createReqVO) {
        PharmacyMedicineInDO medicineInDO = getOne(new LambdaQueryWrapper<PharmacyMedicineInDO>()
                .eq(PharmacyMedicineInDO::getMedicineId, createReqVO.getId())
                .eq(PharmacyMedicineInDO::getBatchCode, createReqVO.getBatchCode()));
        if (medicineInDO != null) {
            throw new ServerException("该药品已入库，请勿重复入库");
        }

        PharmacyMedicineInDO ihcsMedicine = pharmacyMedicineInDao.selectById(createReqVO.getMedicineId());
        BizAssert.notNull(ihcsMedicine, "药品不存在");
        // 药品库存总量
        BigDecimal sourceTotalInventoryNum = ihcsMedicine.getTotalInventoryNum();

        // 插入
        PharmacyMedicineInDO medicineIn = BeanUtils.toBean(createReqVO, PharmacyMedicineInDO.class);
        // 进行总量计算以及入库操作
        BigDecimal targetTotalInventoryNum = sourceTotalInventoryNum;

        // 该批次添加后的药品的总数
        targetTotalInventoryNum = targetTotalInventoryNum.add(createReqVO.getInNum());
        this.checkMedicineIn(createReqVO);
        medicineIn.setInventoryNum(createReqVO.getInNum());
        medicineIn.setTotalInventoryNum(targetTotalInventoryNum);
        pharmacyMedicineInDao.insert(medicineIn);

        //增加库存
        PharmacyMedicineInDO medicine = pharmacyMedicineInDao.selectById(createReqVO.getMedicineId());
        medicine.setTotalInventoryNum(medicine.getTotalInventoryNum().add(createReqVO.getInNum()));
        pharmacyMedicineInDao.updateById(medicine);
        // 返回
        return medicineIn.getId();
        // 返回
    }

    @Override
    public void updatePharmacyMedicineIn(PharmacyMedicineInSaveReqVO updateReqVO) {
        // 校验存在
        validatePharmacyMedicineInExists(updateReqVO.getId());
        // 更新
        PharmacyMedicineInDO updateObj = BeanUtils.toBean(updateReqVO, PharmacyMedicineInDO.class);
        pharmacyMedicineInDao.updateById(updateObj);
    }

    @Override
    public void deletePharmacyMedicineIn(String id) {
        // 校验存在
        validatePharmacyMedicineInExists(id);
        // 删除
        pharmacyMedicineInDao.deleteById(id);
    }

    private void validatePharmacyMedicineInExists(String id) {
        if (pharmacyMedicineInDao.selectById(id) == null) {
            throw new ServerException("大药房管理-药品入库批次数据不存在");
        }
    }

    @Override
    public PharmacyMedicineInDO getPharmacyMedicineIn(String id) {
        return pharmacyMedicineInDao.selectById(id);
    }

    @Override
    public PageResult<PharmacyMedicineInDO> getPharmacyMedicineInPage(PharmacyMedicineInPageReqVO pageReqVO) {
        return pharmacyMedicineInDao.selectPage(pageReqVO);
    }

    @Override
    public List<PharmacyMedicineInDO> getPharmacyMedicineInList(PharmacyMedicineInListReqVO listReqVO) {
        return pharmacyMedicineInDao.selectList(listReqVO);
    }

    @Override
    public PharmacyMedicineInRespVO getMedicineInDetail(String medicineInId) {
        PharmacyMedicineInDO medicineIn = pharmacyMedicineInDao.selectById(medicineInId);
        return BeanUtils.toBean(medicineIn, PharmacyMedicineInRespVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCompareAndSetInventoryNum(Collection<UpdateMedicineInInventoryNumBO> updateMedicineInInventoryNumBOList) {
        for (UpdateMedicineInInventoryNumBO updateMedicineInInventoryNumBO : updateMedicineInInventoryNumBOList) {
            String id = updateMedicineInInventoryNumBO.getId();
            boolean update = this.lambdaUpdate()
                    .set(PharmacyMedicineInDO::getInventoryNum, updateMedicineInInventoryNumBO.getTargetInventoryNum())
                    .set(PharmacyMedicineInDO::getUpdateTime, LocalDateTime.now())
                    .eq(PharmacyMedicineInDO::getId, id)
                    .eq(PharmacyMedicineInDO::getInventoryNum, updateMedicineInInventoryNumBO.getSourceInventoryNum())
                    .update();
            BizAssert.isTrue(update, "药品入库批次id[" + id + "]库存同时有其他操作修改，操作失败请重试");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void selectExpireBatchAndUpdateMedicineFlag(List<String> medicineIdList) {
        if (!CollectionUtils.isEmpty(medicineIdList)) {
            pharmacyMedicineDao.update(null, Wrappers.lambdaUpdate(PharmacyMedicineDO.class)
                    .set(PharmacyMedicineDO::getUpdateTime, LocalDateTime.now())
                    .set(PharmacyMedicineDO::getHasExpireDateBatch, BooleanIntConstant.FALSE)
                    .in(PharmacyMedicineDO::getId, medicineIdList));
        }
        Date nowDate = new Date();
        List<String> existExpireBatchMedicineIdList = this.getBaseMapper().getExistExpireBatchMedicineId(nowDate, medicineIdList);
        if (CollectionUtils.isEmpty(existExpireBatchMedicineIdList)) {
            return;
        }
        pharmacyMedicineDao.update(null, Wrappers.lambdaUpdate(PharmacyMedicineDO.class)
                .set(PharmacyMedicineDO::getHasExpireDateBatch, BooleanIntConstant.TRUE)
                .set(PharmacyMedicineDO::getUpdateTime, LocalDateTime.now())
                .in(PharmacyMedicineDO::getId, existExpireBatchMedicineIdList));
    }

    @Override
    public List<PharmacyMedicineInDO> ypbsList(MedicineInYpbsReqVO listReqVO) {
        LambdaQueryWrapper<PharmacyMedicineInDO> queryWrapper = Wrappers.lambdaQuery(PharmacyMedicineInDO.class)
                .eq(PharmacyMedicineInDO::getMedicineId, listReqVO.getMedicineId())
                .gt(PharmacyMedicineInDO::getInventoryNum, 0);
        if (StringUtils.isNotEmpty(listReqVO.getBatchCode())) {
            queryWrapper.like(PharmacyMedicineInDO::getBatchCode, listReqVO.getBatchCode());
        }
        queryWrapper.orderByDesc(PharmacyMedicineInDO::getInDate);
        return this.list(queryWrapper);
    }

    /**
     * 校验入库登记数据
     * @param saveMedicineInDTO 入库登记数据
     */
    private void checkMedicineIn(PharmacyMedicineInSaveReqVO saveMedicineInDTO) {
        Date expireDate = saveMedicineInDTO.getExpireDate();
        BizAssert.isTrue(expireDate.after(new Date()) || expireDate.equals(new Date()), "批次有效期不能低于当天");
    }

}
