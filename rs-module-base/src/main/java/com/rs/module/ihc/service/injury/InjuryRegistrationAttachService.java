package com.rs.module.ihc.service.injury;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.injury.vo.*;
import com.rs.module.ihc.entity.injury.InjuryRegistrationAttachDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 医疗子系统-伤亡登记-附件信息 Service 接口
 *
 * <AUTHOR>
 */
public interface InjuryRegistrationAttachService extends IBaseService<InjuryRegistrationAttachDO>{

    /**
     * 创建医疗子系统-伤亡登记-附件信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createInjuryRegistrationAttach(@Valid InjuryRegistrationAttachSaveReqVO createReqVO);

    /**
     * 删除医疗子系统-伤亡登记-附件信息
     *
     * @param id 编号
     */
    void deleteInjuryRegistrationAttach(String id);

    /**
     * 获得医疗子系统-伤亡登记-附件信息
     *
     * @param id 编号
     * @return 医疗子系统-伤亡登记-附件信息
     */
    InjuryRegistrationAttachDO getInjuryRegistrationAttach(String id);

    /**
    * 获得医疗子系统-伤亡登记-附件信息分页
    *
    * @param pageReqVO 分页查询
    * @return 医疗子系统-伤亡登记-附件信息分页
    */
    PageResult<InjuryRegistrationAttachDO> getInjuryRegistrationAttachPage(InjuryRegistrationAttachPageReqVO pageReqVO);

    /**
    * 获得医疗子系统-伤亡登记-附件信息列表
    *
    * @param listReqVO 查询条件
    * @return 医疗子系统-伤亡登记-附件信息列表
    */
    List<InjuryRegistrationAttachDO> getInjuryRegistrationAttachList(InjuryRegistrationAttachListReqVO listReqVO);


}
