package com.rs.module.ihc.service.pm;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.MedicineDeliveryDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 药房管理-顾送药品信息 Service 接口
 *
 * <AUTHOR>
 */
public interface MedicineDeliveryService extends IBaseService<MedicineDeliveryDO>{

    /**
     * 创建药房管理-顾送药品信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMedicineDelivery(@Valid MedicineDeliverySaveReqVO createReqVO);

    /**
     * 更新药房管理-顾送药品信息
     *
     * @param updateReqVO 更新信息
     */
    void updateMedicineDelivery(@Valid MedicineDeliverySaveReqVO updateReqVO);

    /**
     * 删除药房管理-顾送药品信息
     *
     * @param id 编号
     */
    void deleteMedicineDelivery(String id);

    /**
     * 获得药房管理-顾送药品信息
     *
     * @param id 编号
     * @return 药房管理-顾送药品信息
     */
    MedicineDeliveryDO getMedicineDelivery(String id);

    /**
    * 获得药房管理-顾送药品信息分页
    *
    * @param pageReqVO 分页查询
    * @return 药房管理-顾送药品信息分页
    */
    PageResult<MedicineDeliveryDO> getMedicineDeliveryPage(MedicineDeliveryPageReqVO pageReqVO);

    /**
    * 获得药房管理-顾送药品信息列表
    *
    * @param listReqVO 查询条件
    * @return 药房管理-顾送药品信息列表
    */
    List<MedicineDeliveryDO> getMedicineDeliveryList(MedicineDeliveryListReqVO listReqVO);


}
