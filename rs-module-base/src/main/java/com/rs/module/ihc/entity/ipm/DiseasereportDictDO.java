package com.rs.module.ihc.entity.ipm;

import lombok.*;
import java.util.*;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 所内就医-报病字典管理 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_ipm_diseasereport_dict")
@KeySequence("ihc_ipm_diseasereport_dict_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DiseasereportDictDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 疾病类型（字典：报病字典 ZD_BBZD）
     */
    private String diseaseType;
    /**
     * 疾病症状
     */
    private String diseaseSymptom;
    /**
     * 是否内置
     */
    private String isBuiltIn;

}
