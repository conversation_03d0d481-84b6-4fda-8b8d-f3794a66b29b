package com.rs.module.ihc.controller.admin.ipm.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.common.annotation.Format;
import com.rs.framework.mybatis.annotation.Query;
import com.rs.module.base.entity.RyVO;
import com.rs.module.ihc.service.ipm.VisitService;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel(description = "管理后台 - 所内就医-所内门诊 Response VO")
@Data
public class OutpatientCfRespVO extends RyVO implements Serializable, TransPojo {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("来源（1:所内就医预约，2:手动新增）")
    private String source;
    @ApiModelProperty("预约id，对应ihc_ipm_internal_medical_appointment.id")
    private String appointmentId;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("在押人员编号")
    private String rybh;
    @ApiModelProperty("在押人员名称")
    private String ryxm;
    @ApiModelProperty("是否重病号（ 0：否 ，1：是）")
    private Integer sickType;
    @ApiModelProperty("报病时间")
    private Date diseaseTime;
    @ApiModelProperty("报病原因")
    private String diseaseReason;
    @ApiModelProperty("主诉")
    private String mainComplaint;
    @ApiModelProperty("情况登记")
    private String illnessResume;
    @ApiModelProperty("状态（ 0：待看诊，1：已看诊）")
    private Integer status;
    @ApiModelProperty("监所id")
    private String prisonId;
    @ApiModelProperty("处方id")
    private String cfid;

    @Query(sql = "select * from ihc_ipm_prescribe where id = '${cfid}' and is_del =0",beanClass = PrescribeRespVO.class)
    public PrescribeRespVO prescribe;

    @ApiModelProperty("是否有戒具")
    @Format(service = VisitService.class, method = "getHaveRestraints", value = "jgrybm")
    private Boolean haveRestraints;
}
