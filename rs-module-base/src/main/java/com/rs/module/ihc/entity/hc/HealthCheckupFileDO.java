package com.rs.module.ihc.entity.hc;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 五项体检-附件 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_hc_health_checkup_file")
@KeySequence("ihc_hc_health_checkup_file_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ihc_hc_health_checkup_file")
public class HealthCheckupFileDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 体检单id，对应ihc_hc_health_checkup.id
     */
    private String checkupId;
    /**
     * 检查类型 1=常规检查 2=血常规 3=B超 4=肝、胆、脾、胰 5=双肾、输尿管、膀胱 6=心电图 7=常规心电图 8=X光 9=胸部 10=检查结论
     */
    private String checkupCategory;
    /**
     * 文件地址
     */
    private String fileUrl;

}
