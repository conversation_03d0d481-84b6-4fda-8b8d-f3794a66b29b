package com.rs.module.ihc.handler.ipm.casetl.dic;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.framework.common.handler.AbstractHandler;
import com.rs.module.ihc.controller.admin.ipm.vo.CaseTemplateDicRespV2VO;
import com.rs.module.ihc.controller.admin.ipm.vo.CaseTemplateDicRespVO;
import com.rs.module.ihc.entity.ipm.CaseTemplateDicDO;
import com.rs.module.ihc.service.ipm.CaseTemplateDicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName TzHandler
 * @Description 体征
 * <AUTHOR>
 * @Date 2025/3/21 17:13
 * @Version 1.0
 */
@Component
public class TzHandler extends AbstractHandler<CaseTemplateDicRespV2VO> {
    @Autowired
    private CaseTemplateDicService caseTemplateDicService;
    @Override
    protected CaseTemplateDicRespV2VO doHandle(CaseTemplateDicRespV2VO input) {
        List<CaseTemplateDicDO> list = caseTemplateDicService.list(new LambdaQueryWrapper<CaseTemplateDicDO>()
                .eq(CaseTemplateDicDO::getDl, "3")
                .eq(CaseTemplateDicDO::getFl,"3")
                .orderByAsc(CaseTemplateDicDO::getSort));
        list.forEach(item -> {
            CaseTemplateDicRespVO caseTemplateDicRespVO = new CaseTemplateDicRespVO();
            caseTemplateDicRespVO.setLable(item.getLable());
            input.getTz().add(caseTemplateDicRespVO);
        });
        return input;
    }
}
