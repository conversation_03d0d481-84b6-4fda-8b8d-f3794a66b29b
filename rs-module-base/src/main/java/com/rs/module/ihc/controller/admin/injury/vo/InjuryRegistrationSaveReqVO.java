package com.rs.module.ihc.controller.admin.injury.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 医疗子系统-伤亡登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class InjuryRegistrationSaveReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("外伤情况 字典ZD_WSBQK")
    @NotEmpty(message = "外伤情况 字典ZD_WSBQK不能为空")
    private String injuryDetails;

    @ApiModelProperty("外伤情况记录方式 字典ZD_WSQKJLFS")
    @NotEmpty(message = "外伤情况记录方式 字典ZD_WSQKJLFS不能为空")
    private String recordingMethod;

    @ApiModelProperty("致伤日期")
    private Date injuryDate;

    @ApiModelProperty("致伤原因")
    private String injuryCause;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("附件信息")
    private String attUrl;

    @ApiModelProperty("伤病信息-JSON存储")
    private String medicalConditionInformation;

    @ApiModelProperty("附件信息")
    private List<InjuryRegistrationAttachSaveReqVO> attachList;

}
