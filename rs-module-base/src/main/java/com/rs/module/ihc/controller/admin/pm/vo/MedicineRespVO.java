package com.rs.module.ihc.controller.admin.pm.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 药房管理-药品信息 Response VO")
@Data
public class MedicineRespVO implements TransPojo {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("是否删除(0:否,1:是)")
    private Integer isDel;
    @ApiModelProperty("添加时间")
    private Date addTime;
    @ApiModelProperty("添加人")
    private String addUser;
    @ApiModelProperty("更新人")
    private String updateUser;
    @ApiModelProperty("所属省级代码")
    private String proCode;
    @ApiModelProperty("所属省级名称")
    private String proName;
    @ApiModelProperty("所属市级代码")
    private String cityCode;
    @ApiModelProperty("所属市级名称")
    private String cityName;
    @ApiModelProperty("区域代码")
    private String regCode;
    @ApiModelProperty("区域名称")
    private String regName;
    @ApiModelProperty("机构名称")
    private String orgCode;
    @ApiModelProperty("机构名称")
    private String orgName;
    @ApiModelProperty("药房ID")
    private String pharmacyId;
    @ApiModelProperty("药品名称")
    private String medicineName;
    @ApiModelProperty("药品名称全拼")
    private String medicineSpellName;
    @ApiModelProperty("药品名称拼音首字母或者英文拼接")
    private String medicineNameFirstLetter;
    @ApiModelProperty("计量单位（字典：计量单位）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_MEASUREMENT_UNIT")
    private String measurementUnit;
    @ApiModelProperty("规格")
    private String specs;
    @ApiModelProperty("生产单位")
    private String productUnit;
    @ApiModelProperty("剂型（字典:剂型）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_DOSAGE_FORM")
    private String dosageForm;
    @ApiModelProperty("批准文号")
    private String approvalNum;
    @ApiModelProperty("原批准文号")
    private String originalApprovalNum;
    @ApiModelProperty("药品本位码")
    private String medicineStandardCode;
    @ApiModelProperty("是否毒麻品 (0：否 1：是)")
    @Trans(type = TransType.DICTIONARY, key = "ZD_IS_DRUG")
    private Short drug;
    @ApiModelProperty("每日用量")
    private String dayDosage;
    @ApiModelProperty("每次用量")
    private String oneDosage;
    @ApiModelProperty("库存预警值 按最小单位")
    private BigDecimal inventoryWarnValue;
    @ApiModelProperty("是否可报损（0：否 1：是）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_IS_REPORTABLE_LOSS")
    private Short reportableLoss;
    @ApiModelProperty("英文名称")
    private String medicineEnglishName;
    @ApiModelProperty("商品名称")
    private String productName;
    @ApiModelProperty("上市许可持有人")
    private String marketApprovalHolder;
    @ApiModelProperty("上市许可持有人地址")
    private String marketApprovalHolderAddress;
    @ApiModelProperty("生产地址")
    private String productAddress;
    @ApiModelProperty("药品类别")
    private String medicineCategory;
    @ApiModelProperty("药品本位编码备注")
    private String medicineStandardCodeRemark;
    @ApiModelProperty("药品别名")
    private String medicineAlias;
    @ApiModelProperty("包装单位")
    private String packageUnit;
    @ApiModelProperty("批准日期")
    private Date approvalDate;
    @ApiModelProperty("总库存数量")
    private BigDecimal totalInventoryNum;
    @ApiModelProperty("是否有过期批次药 (0：否 1：是)")
    @Trans(type = TransType.DICTIONARY, key = "ZD_BOOLEAN_TYPE")
    private Integer hasExpireDateBatch;
    @ApiModelProperty("最小计量单位（字典值：计量单位)")
    @Trans(type = TransType.DICTIONARY, key = "ZD_MEASUREMENT_UNIT")
    private String minMeasurementUnit;
    @ApiModelProperty("计量单位转最小计量单位转化比")
    private Float unitConversionRatio;
    @ApiModelProperty("是否属于敏感药 (0：否 1：是)")
    @Trans(type = TransType.DICTIONARY, key = "ZD_BOOLEAN_TYPE")
    private Integer belongSensitive;
    @ApiModelProperty("是否属于精神药品 (0：否 1：是)")
    @Trans(type = TransType.DICTIONARY, key = "ZD_BOOLEAN_TYPE")
    private Short paychoactieDrug;
    @ApiModelProperty("监所id")
    private String prisonId;
}
