package com.rs.module.ihc.entity.hr;

import lombok.*;
import java.util.*;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 健康档案-打印文书配置 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_hr_printdoc_config")
@KeySequence("ihc_hr_printdoc_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrintdocConfigDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 表单ID
     */
    private String formId;
    /**
     * 文书名称
     */
    private String wsName;
    /**
     * 文书地址
     */
    private String wsUrl;
    /**
     * 文书排序ID
     */
    private Integer orderId;
    /**
     * 是否禁用
     */
    private Integer isDisabled;

}
