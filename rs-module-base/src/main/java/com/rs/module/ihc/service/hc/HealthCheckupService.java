package com.rs.module.ihc.service.hc;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.ihc.controller.admin.hc.vo.HealthCheckupAdditionalRecordVO;
import com.rs.module.ihc.controller.admin.hc.vo.HealthCheckupRespVO;
import com.rs.module.ihc.controller.admin.hc.vo.HealthCheckupSaveReqVO;
import com.rs.module.ihc.entity.hc.HealthCheckupDO;

import javax.validation.Valid;

/**
 * 五项体检 Service 接口
 *
 * <AUTHOR>
 */
public interface HealthCheckupService extends IBaseService<HealthCheckupDO>{

    /**
     * 创建五项体检
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createHealthCheckup(@Valid HealthCheckupAdditionalRecordVO createReqVO);

    /**
     * 更新五项体检
     *
     * @param updateReqVO 更新信息
     */
    void updateHealthCheckup(@Valid HealthCheckupSaveReqVO updateReqVO);


    /**
     * 获得五项体检
     *
     * @param id 编号
     * @return 五项体检
     */
    HealthCheckupRespVO getHealthCheckup(String id);


    /**
     * 提交申请
     * <AUTHOR>
     * @date 2025/7/14 15:29
     * @param [jgrybms]
     * @return void
     */
    void apply(String jgrybms);
}
