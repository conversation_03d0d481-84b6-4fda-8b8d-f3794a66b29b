package com.rs.module.ihc.service.hc;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.hc.vo.*;
import com.rs.module.ihc.entity.hc.HealthCheckupFileDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 五项体检-附件 Service 接口
 *
 * <AUTHOR>
 */
public interface HealthCheckupFileService extends IBaseService<HealthCheckupFileDO>{

    /**
     * 创建五项体检-附件
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createHealthCheckupFile(@Valid HealthCheckupFileSaveReqVO createReqVO);

    /**
     * 更新五项体检-附件
     *
     * @param updateReqVO 更新信息
     */
    void updateHealthCheckupFile(@Valid HealthCheckupFileSaveReqVO updateReqVO);

    /**
     * 删除五项体检-附件
     *
     * @param id 编号
     */
    void deleteHealthCheckupFile(String id);

    /**
     * 获得五项体检-附件
     *
     * @param id 编号
     * @return 五项体检-附件
     */
    HealthCheckupFileDO getHealthCheckupFile(String id);

    /**
    * 获得五项体检-附件分页
    *
    * @param pageReqVO 分页查询
    * @return 五项体检-附件分页
    */
    PageResult<HealthCheckupFileDO> getHealthCheckupFilePage(HealthCheckupFilePageReqVO pageReqVO);

    /**
    * 获得五项体检-附件列表
    *
    * @param listReqVO 查询条件
    * @return 五项体检-附件列表
    */
    List<HealthCheckupFileDO> getHealthCheckupFileList(HealthCheckupFileListReqVO listReqVO);


}
