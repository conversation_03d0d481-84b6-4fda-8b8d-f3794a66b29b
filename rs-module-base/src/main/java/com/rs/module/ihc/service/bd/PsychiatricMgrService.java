package com.rs.module.ihc.service.bd;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.ihc.controller.admin.bd.vo.PsychiatricMgrListReqVO;
import com.rs.module.ihc.controller.admin.bd.vo.PsychiatricMgrSaveReqVO;
import com.rs.module.ihc.entity.bd.PsychiatricMgrDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 精神病异常管理 Service 接口
 *
 * <AUTHOR>
 */
public interface PsychiatricMgrService extends IBaseService<PsychiatricMgrDO>{

    /**
     * 创建精神病异常管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPsychiatricMgr(@Valid PsychiatricMgrSaveReqVO createReqVO);


    /**
     * 获得精神病异常管理
     *
     * @param id 编号
     * @return 精神病异常管理
     */
    PsychiatricMgrDO getPsychiatricMgr(String id);

    /**
     * 解除人员列管状态
     * <AUTHOR>
     * @date 2025/6/28 11:45
     * @param [id]
     * @return void
     */
    void releasePerson(String id);
}
