package com.rs.module.ihc.controller.admin.ephe.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 卫生防疫与健康教育-健康教育新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class HealthEduSaveReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("宣教课程")
    @NotEmpty(message = "宣教课程不能为空")
    private String xjkc;

    @ApiModelProperty("健康教育情况")
    private String jkjyqk;

    @ApiModelProperty("宣教范围")
    @NotEmpty(message = "宣教范围不能为空")
    private String cjfw;

    @ApiModelProperty("宣教时段开始时间")
    private Date xjsdKssj;

    @ApiModelProperty("宣教时段结束时间")
    private Date xjsdJssj;

    @ApiModelProperty("宣教人姓名")
    private String xjrxm;


}
