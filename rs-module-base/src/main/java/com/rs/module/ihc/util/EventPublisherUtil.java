package com.rs.module.ihc.util;


import com.fhs.common.spring.SpringContextUtil;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEvent;


/**
 * @ClassName EventPublisherUtil
 *
 * <AUTHOR>
 * @Date 2025/3/25 19:06
 * @Version 1.0
 */
public class EventPublisherUtil {
    private static ApplicationContext publisher;

    public static void publishEvent(ApplicationEvent event) {
        if (publisher == null) {
            publisher = SpringContextUtil.getApplicationContext();
        }
        // 发布事件
        publisher.publishEvent(event);
    }
}
