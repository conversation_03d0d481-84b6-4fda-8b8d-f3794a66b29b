package com.rs.module.ihc.entity.ipm;

import lombok.*;
import java.util.*;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 所内就医-病例模板表-字典 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_ipm_case_template_dic")
@KeySequence("ihc_ipm_case_template_dic_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CaseTemplateDicDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 名称
     */
    private String lable;
    /**
     * 分类1既往史2个人史3体征4体温5脉搏6呼吸7血压8体重9身高10血氧11血糖
     */
    private String fl;
    /**
     * 分组1收缩压2舒张压3婴儿童4青少年5成年人
     */
    private String fz;
    /**
     * 排列顺序
     */
    private Integer sort;
    /**
     * 分组排列顺序
     */
    private Integer fzOrder;
    /**
     * 大类1主诉2既往史3体格检查
     */
    private String dl;

}
