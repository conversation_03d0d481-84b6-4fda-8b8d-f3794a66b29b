package com.rs.module.ihc.entity.ipm;

import lombok.*;
import java.util.*;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;


/**
 * 所内就医-所内门诊-检查单 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_ipm_outpatient_checklist")
@KeySequence("ihc_ipm_outpatient_checklist_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "ihc_ipm_outpatient_checklist")
@Entity
public class OutpatientChecklistDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @Id
    private String id;
    /**
     * 所内门诊id，对应ihc_ipm_outpatient.id
     */
    private String outpatientId;
    /**
     * 主诉
     */
    private String mainComplaint;
    /**
     * 病情简述
     */
    private String illnessResume;
    /**
     * 检查结论
     */
    private String checkConclusion;
    /**
     * 检查单编号
     */
    private String checklistNum;
    /**
     * 检查单种类 多选  ,分割
     */
    private String checklistCategory;
    /**
     * 检查单状态
     */
    private String checklistStatus;
    /**
     * 登记人，对应permission_user.userid
     */
    private String registerUserid;
    /**
     * 登记人名称
     */
    private String registerUserName;
    /**
     * 登记时间
     */
    private Date registerTime;
    /**
     * 监所id
     */
    private String prisonId;
    private String jgrybm;



}
