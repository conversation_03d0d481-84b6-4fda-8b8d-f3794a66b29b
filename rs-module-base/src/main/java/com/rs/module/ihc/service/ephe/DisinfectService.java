package com.rs.module.ihc.service.ephe;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.ephe.vo.*;
import com.rs.module.ihc.entity.ephe.DisinfectDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 卫生防疫与健康教育-卫生消毒登记 Service 接口
 *
 * <AUTHOR>
 */
public interface DisinfectService extends IBaseService<DisinfectDO>{

    /**
     * 创建卫生防疫与健康教育-卫生消毒登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createDisinfect(@Valid DisinfectSaveReqVO createReqVO);


    /**
     * 获得卫生防疫与健康教育-卫生消毒登记
     *
     * @param id 编号
     * @return 卫生防疫与健康教育-卫生消毒登记
     */
    DisinfectDO getDisinfect(String id);


}
