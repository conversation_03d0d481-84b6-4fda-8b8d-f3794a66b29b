package com.rs.module.ihc.service.pm;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.PharmacyDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 药房管理-药房信息 Service 接口
 *
 * <AUTHOR>
 */
public interface PharmacyService extends IBaseService<PharmacyDO>{

    /**
     * 创建药房管理-药房信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPharmacy(@Valid PharmacySaveReqVO createReqVO);

    /**
     * 更新药房管理-药房信息
     *
     * @param updateReqVO 更新信息
     */
    void updatePharmacy(@Valid PharmacySaveReqVO updateReqVO);

    /**
     * 删除药房管理-药房信息
     *
     * @param id 编号
     */
    void deletePharmacy(String id);

    /**
     * 获得药房管理-药房信息
     *
     * @param id 编号
     * @return 药房管理-药房信息
     */
    PharmacyDO getPharmacy(String id);

    /**
    * 获得药房管理-药房信息分页
    *
    * @param pageReqVO 分页查询
    * @return 药房管理-药房信息分页
    */
    PageResult<PharmacyDO> getPharmacyPage(PharmacyPageReqVO pageReqVO);

    /**
    * 获得药房管理-药房信息列表
    *
    * @param listReqVO 查询条件
    * @return 药房管理-药房信息列表
    */
    List<PharmacyDO> getPharmacyList(PharmacyListReqVO listReqVO);


}
