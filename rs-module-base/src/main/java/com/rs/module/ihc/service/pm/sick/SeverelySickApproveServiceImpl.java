package com.rs.module.ihc.service.pm.sick;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.ihc.controller.admin.pm.sick.vo.*;
import com.rs.module.ihc.entity.pm.sick.SeverelySickApproveDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.pm.sick.SeverelySickApproveDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 重特病号审批 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SeverelySickApproveServiceImpl extends BaseServiceImpl<SeverelySickApproveDao, SeverelySickApproveDO> implements SeverelySickApproveService {

    @Resource
    private SeverelySickApproveDao severelySickApproveDao;

    @Override
    public String createSeverelySickApprove(SeverelySickApproveSaveReqVO createReqVO) {
        // 插入
        SeverelySickApproveDO severelySickApprove = BeanUtils.toBean(createReqVO, SeverelySickApproveDO.class);
        severelySickApproveDao.insert(severelySickApprove);
        // 返回
        return severelySickApprove.getId();
    }

    @Override
    public void updateSeverelySickApprove(SeverelySickApproveSaveReqVO updateReqVO) {
        // 校验存在
        validateSeverelySickApproveExists(updateReqVO.getId());
        // 更新
        SeverelySickApproveDO updateObj = BeanUtils.toBean(updateReqVO, SeverelySickApproveDO.class);
        severelySickApproveDao.updateById(updateObj);
    }

    @Override
    public void deleteSeverelySickApprove(String id) {
        // 校验存在
        validateSeverelySickApproveExists(id);
        // 删除
        severelySickApproveDao.deleteById(id);
    }

    private void validateSeverelySickApproveExists(String id) {
        if (severelySickApproveDao.selectById(id) == null) {
            throw new ServerException("重特病号审批数据不存在");
        }
    }

    @Override
    public SeverelySickApproveDO getSeverelySickApprove(String id) {
        return severelySickApproveDao.selectById(id);
    }

    @Override
    public PageResult<SeverelySickApproveDO> getSeverelySickApprovePage(SeverelySickApprovePageReqVO pageReqVO) {
        return severelySickApproveDao.selectPage(pageReqVO);
    }

    @Override
    public List<SeverelySickApproveDO> getSeverelySickApproveList(SeverelySickApproveListReqVO listReqVO) {
        return severelySickApproveDao.selectList(listReqVO);
    }


}
