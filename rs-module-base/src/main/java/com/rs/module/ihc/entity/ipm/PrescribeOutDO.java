package com.rs.module.ihc.entity.ipm;

import lombok.*;
import java.util.*;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 所内就医-处方药品出库记录 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_ipm_prescribe_out")
@KeySequence("ihc_ipm_prescribe_out_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrescribeOutDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 添加人姓名
     */
    private String addUserName;
    /**
     * 更新人姓名
     */
    private String updateUserName;
    /**
     * 处方id，对应ihc_ipm_prescribe.id
     */
    private String prescribeId;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 医嘱
     */
    private String doctorAdvice;
    /**
     * 处方说明
     */
    private String prescribeDescribe;

}
