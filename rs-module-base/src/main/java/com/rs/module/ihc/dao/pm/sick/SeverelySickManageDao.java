package com.rs.module.ihc.dao.pm.sick;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.controller.admin.pm.sick.vo.SeverelySickManageListReqVO;
import com.rs.module.ihc.controller.admin.pm.sick.vo.SeverelySickManagePageReqVO;
import com.rs.module.ihc.entity.pm.sick.SeverelySickManageDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 重特病号管理 Dao
*
* <AUTHOR>
*/
@Mapper
public interface SeverelySickManageDao extends IBaseDao<SeverelySickManageDO> {


    default PageResult<SeverelySickManageDO> selectPage(SeverelySickManagePageReqVO reqVO) {
        Page<SeverelySickManageDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<SeverelySickManageDO> wrapper = new LambdaQueryWrapperX<SeverelySickManageDO>()
            .eqIfPresent(SeverelySickManageDO::getPrisonId, reqVO.getPrisonId())
            .eqIfPresent(SeverelySickManageDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(SeverelySickManageDO::getBusinessStatus, reqVO.getBusinessStatus())
            .eqIfPresent(SeverelySickManageDO::getSeverelySickType, reqVO.getSeverelySickType())
            .eqIfPresent(SeverelySickManageDO::getPatientSituation, reqVO.getPatientSituation())
            .eqIfPresent(SeverelySickManageDO::getTreatmentSituation, reqVO.getTreatmentSituation())
            .eqIfPresent(SeverelySickManageDO::getApplyUserId, reqVO.getApplyUserId())
            .likeIfPresent(SeverelySickManageDO::getApplyUserName, reqVO.getApplyUserName())
            .betweenIfPresent(SeverelySickManageDO::getApplyTime, reqVO.getApplyTime())
            .eqIfPresent(SeverelySickManageDO::getIsSeverelySick, reqVO.getIsSeverelySick())
            .eqIfPresent(SeverelySickManageDO::getRelieveApplyId, reqVO.getRelieveApplyId())
            .eqIfPresent(SeverelySickManageDO::getRelieveApproveResult, reqVO.getRelieveApproveResult())
            .betweenIfPresent(SeverelySickManageDO::getRelieveTime, reqVO.getRelieveTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(SeverelySickManageDO::getAddTime);
        }
        Page<SeverelySickManageDO> severelySickManagePage = selectPage(page, wrapper);
        return new PageResult<>(severelySickManagePage.getRecords(), severelySickManagePage.getTotal());
    }
    default List<SeverelySickManageDO> selectList(SeverelySickManageListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SeverelySickManageDO>()
            .eqIfPresent(SeverelySickManageDO::getPrisonId, reqVO.getPrisonId())
            .eqIfPresent(SeverelySickManageDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(SeverelySickManageDO::getBusinessStatus, reqVO.getBusinessStatus())
            .eqIfPresent(SeverelySickManageDO::getSeverelySickType, reqVO.getSeverelySickType())
            .eqIfPresent(SeverelySickManageDO::getPatientSituation, reqVO.getPatientSituation())
            .eqIfPresent(SeverelySickManageDO::getTreatmentSituation, reqVO.getTreatmentSituation())
            .eqIfPresent(SeverelySickManageDO::getApplyUserId, reqVO.getApplyUserId())
            .likeIfPresent(SeverelySickManageDO::getApplyUserName, reqVO.getApplyUserName())
            .betweenIfPresent(SeverelySickManageDO::getApplyTime, reqVO.getApplyTime())
            .eqIfPresent(SeverelySickManageDO::getIsSeverelySick, reqVO.getIsSeverelySick())
            .eqIfPresent(SeverelySickManageDO::getRelieveApplyId, reqVO.getRelieveApplyId())
            .eqIfPresent(SeverelySickManageDO::getRelieveApproveResult, reqVO.getRelieveApproveResult())
            .betweenIfPresent(SeverelySickManageDO::getRelieveTime, reqVO.getRelieveTime())
        .orderByDesc(SeverelySickManageDO::getAddTime));    }


    }
