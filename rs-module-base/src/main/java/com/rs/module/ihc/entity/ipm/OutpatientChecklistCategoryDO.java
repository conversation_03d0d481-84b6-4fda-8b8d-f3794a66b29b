package com.rs.module.ihc.entity.ipm;

import lombok.*;
import java.util.*;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 所内就医-所内门诊-检查单-检查种类 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 所内就医-所内门诊-检查单-检查种类新增/修改 Request VO")
@TableName("ihc_ipm_outpatient_checklist_category")
@KeySequence("ihc_ipm_outpatient_checklist_category_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutpatientChecklistCategoryDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;
    /**
     * 检查单id，对应ihc_ipm_outpatient_checklist.id
     */
    @ApiModelProperty("检查单id，对应ihc_ipm_outpatient_checklist.id")
    private String checklistId;
    /**
     * 检查类型 字典值 1=常规检查 2=心电图 3=B超 4=DR
     */
    @ApiModelProperty("检查类型 字典值 1=常规检查 2=心电图 3=B超 4=DR")
    private String checklistCategory;
    /**
     * 检查子类型 字典值 多选 ,分割
     */
    @ApiModelProperty("检查子类型 字典值 多选 ,分割")
    private String checklistSubCategory;
    /**
     * 检查子类型其他
     */
    @ApiModelProperty("检查子类型其他")
    private String checklistSubCategoryOther;
    /**
     * 检查结果
     */
    @ApiModelProperty("检查结果")
    private String checkResult;

}
