package com.rs.module.ihc.service.injury;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.injury.vo.InjuryRegistrationAttachListReqVO;
import com.rs.module.ihc.controller.admin.injury.vo.InjuryRegistrationAttachPageReqVO;
import com.rs.module.ihc.controller.admin.injury.vo.InjuryRegistrationAttachSaveReqVO;
import com.rs.module.ihc.dao.injury.InjuryRegistrationAttachDao;
import com.rs.module.ihc.entity.injury.InjuryRegistrationAttachDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 医疗子系统-伤亡登记-附件信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InjuryRegistrationAttachServiceImpl extends BaseServiceImpl<InjuryRegistrationAttachDao, InjuryRegistrationAttachDO> implements InjuryRegistrationAttachService {

    @Resource
    private InjuryRegistrationAttachDao injuryRegistrationAttachDao;

    @Override
    public String createInjuryRegistrationAttach(InjuryRegistrationAttachSaveReqVO createReqVO) {
        // 插入
        InjuryRegistrationAttachDO injuryRegistrationAttach = BeanUtils.toBean(createReqVO, InjuryRegistrationAttachDO.class);
        injuryRegistrationAttachDao.insert(injuryRegistrationAttach);
        // 返回
        return injuryRegistrationAttach.getId();
    }


    @Override
    public void deleteInjuryRegistrationAttach(String id) {
        // 校验存在
        validateInjuryRegistrationAttachExists(id);
        // 删除
        injuryRegistrationAttachDao.deleteById(id);
    }

    private void validateInjuryRegistrationAttachExists(String id) {
        if (injuryRegistrationAttachDao.selectById(id) == null) {
            throw new ServerException("医疗子系统-伤亡登记-附件信息数据不存在");
        }
    }

    @Override
    public InjuryRegistrationAttachDO getInjuryRegistrationAttach(String id) {
        return injuryRegistrationAttachDao.selectById(id);
    }

    @Override
    public PageResult<InjuryRegistrationAttachDO> getInjuryRegistrationAttachPage(InjuryRegistrationAttachPageReqVO pageReqVO) {
        return injuryRegistrationAttachDao.selectPage(pageReqVO);
    }

    @Override
    public List<InjuryRegistrationAttachDO> getInjuryRegistrationAttachList(InjuryRegistrationAttachListReqVO listReqVO) {
        return injuryRegistrationAttachDao.selectList(listReqVO);
    }


}
