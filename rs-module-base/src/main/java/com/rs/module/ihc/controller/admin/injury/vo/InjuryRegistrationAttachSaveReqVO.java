package com.rs.module.ihc.controller.admin.injury.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 医疗子系统-伤亡登记-附件信息新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class InjuryRegistrationAttachSaveReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "关联主表ID", hidden = true)
    //@NotEmpty(message = "关联主表ID不能为空")
    private String registrationId;

    @ApiModelProperty("附件URL")
    private String attUrl;

    @ApiModelProperty("描述")
    private String remark;

    @ApiModelProperty("x轴")
    private String xAxis;

    @ApiModelProperty("y轴")
    private String yAxis;

    @ApiModelProperty("序号")
    private Integer serialNumber;

}
