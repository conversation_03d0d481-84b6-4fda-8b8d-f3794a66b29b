package com.rs.module.ihc.service.pm.prescribe;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.util.BizAssert;
import com.rs.module.ihc.controller.admin.pm.prescribe.dto.PrescribeCheckupRegisterDTO;
import com.rs.module.ihc.controller.admin.pm.prescribe.vo.InternalMedicalPrescribeCheckupListReqVO;
import com.rs.module.ihc.controller.admin.pm.prescribe.vo.InternalMedicalPrescribeCheckupPageReqVO;
import com.rs.module.ihc.controller.admin.pm.prescribe.vo.InternalMedicalPrescribeCheckupSaveReqVO;
import com.rs.module.ihc.dao.pm.prescribe.InternalMedicalPrescribeCheckupDao;
import com.rs.module.ihc.entity.ipm.PrescribeDO;
import com.rs.module.ihc.entity.pm.prescribe.InternalMedicalPrescribeCheckupDO;
import com.rs.module.ihc.service.ipm.PrescribeService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 所内就医-处方-体检登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InternalMedicalPrescribeCheckupServiceImpl extends BaseServiceImpl<InternalMedicalPrescribeCheckupDao, InternalMedicalPrescribeCheckupDO> implements InternalMedicalPrescribeCheckupService {

    @Resource
    private InternalMedicalPrescribeCheckupDao internalMedicalPrescribeCheckupDao;
    @Resource
    private PrescribeService prescribeService;

    @Override
    public String createInternalMedicalPrescribeCheckup(InternalMedicalPrescribeCheckupSaveReqVO createReqVO) {
        // 插入
        InternalMedicalPrescribeCheckupDO internalMedicalPrescribeCheckup = BeanUtils.toBean(createReqVO, InternalMedicalPrescribeCheckupDO.class);
        internalMedicalPrescribeCheckupDao.insert(internalMedicalPrescribeCheckup);
        // 返回
        return internalMedicalPrescribeCheckup.getId();
    }

    @Override
    public void updateInternalMedicalPrescribeCheckup(InternalMedicalPrescribeCheckupSaveReqVO updateReqVO) {
        // 校验存在
        validateInternalMedicalPrescribeCheckupExists(updateReqVO.getId());
        // 更新
        InternalMedicalPrescribeCheckupDO updateObj = BeanUtils.toBean(updateReqVO, InternalMedicalPrescribeCheckupDO.class);
        internalMedicalPrescribeCheckupDao.updateById(updateObj);
    }

    @Override
    public void deleteInternalMedicalPrescribeCheckup(String id) {
        // 校验存在
        validateInternalMedicalPrescribeCheckupExists(id);
        // 删除
        internalMedicalPrescribeCheckupDao.deleteById(id);
    }

    private void validateInternalMedicalPrescribeCheckupExists(String id) {
        if (internalMedicalPrescribeCheckupDao.selectById(id) == null) {
            throw new ServerException("所内就医-处方-体检登记数据不存在");
        }
    }

    @Override
    public InternalMedicalPrescribeCheckupDO getInternalMedicalPrescribeCheckup(String id) {
        return internalMedicalPrescribeCheckupDao.selectById(id);
    }

    @Override
    public PageResult<InternalMedicalPrescribeCheckupDO> getInternalMedicalPrescribeCheckupPage(InternalMedicalPrescribeCheckupPageReqVO pageReqVO) {
        return internalMedicalPrescribeCheckupDao.selectPage(pageReqVO);
    }

    @Override
    public List<InternalMedicalPrescribeCheckupDO> getInternalMedicalPrescribeCheckupList(InternalMedicalPrescribeCheckupListReqVO listReqVO) {
        return internalMedicalPrescribeCheckupDao.selectList(listReqVO);
    }

    @Override
    public void prescribeCheckupRegister(PrescribeCheckupRegisterDTO prescribeCheckupRegisterDTO) {
        String prescribeId = prescribeCheckupRegisterDTO.getPrescribeId();
        PrescribeDO ihcsInternalMedicalPrescribe = prescribeService.getById(prescribeId);
        BizAssert.notNull(ihcsInternalMedicalPrescribe, "处方不存在");
        Integer checkupNum = ihcsInternalMedicalPrescribe.getCheckupNum();
        boolean update = prescribeService.lambdaUpdate()
                .set(PrescribeDO::getCheckupNum, checkupNum + 1)
                .eq(PrescribeDO::getId, prescribeId)
                .eq(PrescribeDO::getCheckupNum, checkupNum)
                .update();
        BizAssert.isTrue(update, "该处方多人同时检查登记冲突，请重试");
        InternalMedicalPrescribeCheckupDO ihcsInternalMedicalPrescribeCheckup = InternalMedicalPrescribeCheckupDO.builder()
                .prescribeId(prescribeId)
                .supervisedUserCode(ihcsInternalMedicalPrescribe.getJgrybm())
                .bloodPressureHeight(prescribeCheckupRegisterDTO.getBloodPressureHeight())
                .bloodPressureLow(prescribeCheckupRegisterDTO.getBloodPressureLow())
                .temperature(prescribeCheckupRegisterDTO.getTemperature())
                .heartRate(prescribeCheckupRegisterDTO.getHeartRate())
                .breatheFrequency(prescribeCheckupRegisterDTO.getBreatheFrequency())
                .pulse(prescribeCheckupRegisterDTO.getPulse())
                .bloodOxygen(prescribeCheckupRegisterDTO.getBloodOxygen())
                .bloodSugar(prescribeCheckupRegisterDTO.getBloodSugar())
                .insulin(prescribeCheckupRegisterDTO.getInsulin())
                .remark(prescribeCheckupRegisterDTO.getRemark())
                .injectionTime(prescribeCheckupRegisterDTO.getInjectionTime())
                .build();
        this.save(ihcsInternalMedicalPrescribeCheckup);
    }


}
