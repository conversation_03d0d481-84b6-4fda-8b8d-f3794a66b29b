package com.rs.module.ihc.entity.idm;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 传染病管理-定期检查登记 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_idm_regular_inspection")
@KeySequence("ihc_idm_regular_inspection_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ihc_idm_regular_inspection")
public class RegularInspectionDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 传染病登记ID
     */
    private String infectionId;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 检查日期/分类管控记录日期
     */
    private Date checkDate;
    /**
     * cd4t淋巴值
     */
    private String cd4tlbz;
    /**
     * 病毒载量值
     */
    private String viralLoadValue;
    /**
     * 管理情况/分类管控情况
     */
    private String managementSituation;
    /**
     * 管控人
     */
    private String controlPersonnel;
    /**
     * 附件地址
     */
    private String attUrl;
    /**
     * 登记民警身份证号
     */
    private String operatePoliceSfzh;
    /**
     * 登记民警
     */
    private String operatePolice;
    /**
     * 登记时间
     */
    private Date operateTime;

}
