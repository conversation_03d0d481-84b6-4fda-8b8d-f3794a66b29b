package com.rs.module.ihc.dao.hc;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.hc.HealthCheckupFileDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.hc.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 五项体检-附件 Dao
*
* <AUTHOR>
*/
@Mapper
public interface HealthCheckupFileDao extends IBaseDao<HealthCheckupFileDO> {


    default PageResult<HealthCheckupFileDO> selectPage(HealthCheckupFilePageReqVO reqVO) {
        Page<HealthCheckupFileDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<HealthCheckupFileDO> wrapper = new LambdaQueryWrapperX<HealthCheckupFileDO>()
            .eqIfPresent(HealthCheckupFileDO::getCheckupId, reqVO.getCheckupId())
            .eqIfPresent(HealthCheckupFileDO::getCheckupCategory, reqVO.getCheckupCategory())
            .eqIfPresent(HealthCheckupFileDO::getFileUrl, reqVO.getFileUrl())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(HealthCheckupFileDO::getAddTime);
        }
        Page<HealthCheckupFileDO> healthCheckupFilePage = selectPage(page, wrapper);
        return new PageResult<>(healthCheckupFilePage.getRecords(), healthCheckupFilePage.getTotal());
    }
    default List<HealthCheckupFileDO> selectList(HealthCheckupFileListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<HealthCheckupFileDO>()
            .eqIfPresent(HealthCheckupFileDO::getCheckupId, reqVO.getCheckupId())
            .eqIfPresent(HealthCheckupFileDO::getCheckupCategory, reqVO.getCheckupCategory())
            .eqIfPresent(HealthCheckupFileDO::getFileUrl, reqVO.getFileUrl())
        .orderByDesc(HealthCheckupFileDO::getAddTime));    }


    }
