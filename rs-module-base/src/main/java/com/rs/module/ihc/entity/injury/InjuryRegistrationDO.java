package com.rs.module.ihc.entity.injury;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 医疗子系统-伤亡登记 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_injury_registration")
@KeySequence("ihc_injury_registration_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ihc_injury_registration")
public class InjuryRegistrationDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 外伤情况 字典ZD_WSBQK
     */
    private String injuryDetails;
    /**
     * 外伤情况记录方式 字典ZD_WSQKJLFS
     */
    private String recordingMethod;
    /**
     * 致伤日期
     */
    private Date injuryDate;
    /**
     * 致伤原因
     */
    private String injuryCause;
    /**
     * 登记民警身份证号
     */
    private String operatePoliceSfzh;
    /**
     * 登记民警
     */
    private String operatePolice;
    /**
     * 登记时间
     */
    private Date operateTime;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;

     /**
      *附件信息
      **/
    private String attUrl;

    /**
     * 伤情登记
     */
    private String medicalConditionInformation;

}
