package com.rs.module.ihc.enums;

import com.rs.framework.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 医疗系统-所情医生处理方式
 * <p>
 * 对应字典值：prison_event_prisoner_handle_method
 */
@Getter
@AllArgsConstructor
public enum PrisonEventPrisonerHandleMethodEnum {

    /**
     * 无需处理
     */
    NO_HANDLE("0", null),

    /**
     * 现场巡诊
     */
    SITE_DIAGNOSE("2", "5"),

    /**
     * 所内门诊
     */
    OUTPATIENT("3", "5"),

    /**
     * 所外就诊
     */
    OUTSIDE_DIAGNOSE("4", "6");

    /**
     * 所情医生处理方式字典编码
     */
    private final String code;

    /**
     * 实战平台处理方式字典
     * <p>
     * 5  ---- 所内就医
     * 6  ---- 所外就医
     */
    private final String actualCombatType;


    /**
     * 根据所情医生处理方式字典编码获取实战平台处理方式字典
     *
     * @param code 所情医生处理方式字典编码
     * @return 实战平台处理方式字典
     */
    public static String getActualCombatTypeFromCode(String code) {
        return Arrays.stream(values())
                .filter(item -> Objects.equals(item.code, code))
                .map(PrisonEventPrisonerHandleMethodEnum::getActualCombatType)
                .findFirst()
                .orElseThrow(() -> new ServiceException(500,"医生处理方式字典[" + code + "]不存在"));
    }



    // -------------------------------------------------------------------------------------------------------------------

    /**
     * 是否现场巡诊
     *
     * @param handleMethod 处理方式
     * @return true：是，false：否
     */
    public static boolean isSiteDiagnose(String handleMethod) {
        return Objects.equals(handleMethod, SITE_DIAGNOSE.code);
    }

    /**
     * 是否所内门诊
     *
     * @param handleMethod 处理方式
     * @return true：是，false：否
     */
    public static boolean isOutpatient(String handleMethod) {
        return Objects.equals(handleMethod, OUTPATIENT.code);
    }

    /**
     * 是否无需处理
     *
     * @param handleMethod 处理方式
     * @return true：是，false：否
     */
    public static boolean isNoHandle(String handleMethod) {
        return Objects.equals(handleMethod, NO_HANDLE.code);
    }

    /**
     * 是否所在外就诊
     *
     * @param handleMethod 处理方式
     * @return true：是，false：否
     */
    public static boolean isOutsideDiagnose(String handleMethod) {
        return Objects.equals(handleMethod, OUTSIDE_DIAGNOSE.code);
    }

}
