package com.rs.module.ihc.service.remote;

import com.rs.framework.mybatis.util.BizAssert;
import com.rs.module.ihc.constant.InternalMedicalPrescribeSourceConstant;
import com.rs.module.ihc.constant.RemoteDiagnoseStatusConstant;
import com.rs.module.ihc.controller.admin.ipm.vo.InternalMedicalRemotePrescribeVO;
import com.rs.module.ihc.entity.ipm.appointment.IhcsInternalMedicalAppointmentDO;
import com.rs.module.ihc.service.ipm.PrescribeService;
import com.rs.module.ihc.service.ipm.appointment.IhcsInternalMedicalAppointmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class IhcsInternalMedicalRemoteServiceImpl implements IhcsInternalMedicalRemoteService {


    @Autowired
    public PrescribeService prescribeService;

    @Autowired
    public IhcsInternalMedicalAppointmentService ihcsInternalMedicalAppointmentService;


    @Override
    public void internalMedicalRemotePrescribe(InternalMedicalRemotePrescribeVO internalMedicalRemotePrescribeDTO) {

        String id = internalMedicalRemotePrescribeDTO.getId();
        IhcsInternalMedicalAppointmentDO appointment = ihcsInternalMedicalAppointmentService.getById(id);
        BizAssert.notNull(appointment, "远程问诊预约信息不存在");
        prescribeService.addPrescribe(internalMedicalRemotePrescribeDTO.getPrescribe(),
                appointment.getSupervisedUserCode(),
                id,
                InternalMedicalPrescribeSourceConstant.REMOTE_DIAGNOSE);

        // 需要修改远程问诊预约信息的状态
        ihcsInternalMedicalAppointmentService.lambdaUpdate()
                .set(IhcsInternalMedicalAppointmentDO::getRemoteDiagnoseStatus, RemoteDiagnoseStatusConstant.COMPLETE_HANDLE)
                .eq(IhcsInternalMedicalAppointmentDO::getId, id)
                .update();

    }

    @Override
    public IhcsInternalMedicalAppointmentDO getIhcsInternalMedicalAppointmentById(String id) {
        return ihcsInternalMedicalAppointmentService.getById(id);
    }

}
