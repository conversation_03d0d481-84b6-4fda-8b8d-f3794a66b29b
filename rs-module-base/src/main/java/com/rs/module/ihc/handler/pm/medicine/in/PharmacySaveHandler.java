package com.rs.module.ihc.handler.pm.medicine.in;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.framework.common.handler.AbstractHandler;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineInImportListVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineInImportVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineInSaveReqVO;
import com.rs.module.ihc.entity.pm.PharmacyMedicineDO;
import com.rs.module.ihc.enums.RkfsEnum;
import com.rs.module.ihc.service.pm.PharmacyMedicineInService;
import com.rs.module.ihc.service.pm.PharmacyMedicineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 大药房导入保存
 */
@Component
public class PharmacySaveHandler extends AbstractHandler<MedicineInImportListVO> {

    @Autowired
    PharmacyMedicineInService pharmacyMedicineInService;

    @Autowired
    PharmacyMedicineService pharmacyMedicineService;

    @Override
    protected MedicineInImportListVO doHandle(MedicineInImportListVO input) {
        List<MedicineInImportVO> medicineInImportVOList = input.getMedicineInImportVOList();
        for (MedicineInImportVO medicineInImportVO : medicineInImportVOList) {
            PharmacyMedicineInSaveReqVO medicineInSaveReqVO = new PharmacyMedicineInSaveReqVO();
            PharmacyMedicineDO medicineDO = pharmacyMedicineService.getOne(new LambdaQueryWrapper<PharmacyMedicineDO>()
                    .eq(PharmacyMedicineDO::getApprovalNum, medicineInImportVO.getApprovalNum()));
            medicineInSaveReqVO.setMedicineId(medicineDO.getId());
            medicineInSaveReqVO.setInNum(medicineInImportVO.getSl().multiply(BigDecimal.valueOf(medicineDO.getUnitConversionRatio())));
            medicineInSaveReqVO.setBatchCode(input.getShdh());
            medicineInSaveReqVO.setInDate(new Date());
            medicineInSaveReqVO.setExpireDate(medicineInImportVO.getYxqDate());
            medicineInSaveReqVO.setInStorageMethod(RkfsEnum.CGRK.getCode());
            medicineInSaveReqVO.setBatchCode(input.getShdh());
            //保存入库单
            pharmacyMedicineInService.createPharmacyMedicineIn(medicineInSaveReqVO);
        }
        return input;
    }
}
