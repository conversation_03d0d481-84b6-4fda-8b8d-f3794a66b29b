package com.rs.module.ihc.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.ihc.controller.admin.pm.dto.SaveMedicineLossDTO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineLossListReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineLossPageReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineLossSaveReqVO;
import com.rs.module.ihc.entity.pm.PharmacyMedicineLossDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 大药房管理-药品报损 Service 接口
 *
 * <AUTHOR>
 */
public interface PharmacyMedicineLossService extends IBaseService<PharmacyMedicineLossDO>{

    /**
     * 创建大药房管理-药品报损
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPharmacyMedicineLoss(@Valid PharmacyMedicineLossSaveReqVO createReqVO);

    /**
     * 更新大药房管理-药品报损
     *
     * @param updateReqVO 更新信息
     */
    void updatePharmacyMedicineLoss(@Valid PharmacyMedicineLossSaveReqVO updateReqVO);

    /**
     * 删除大药房管理-药品报损
     *
     * @param id 编号
     */
    void deletePharmacyMedicineLoss(String id);

    /**
     * 获得大药房管理-药品报损
     *
     * @param id 编号
     * @return 大药房管理-药品报损
     */
    PharmacyMedicineLossDO getPharmacyMedicineLoss(String id);

    /**
    * 获得大药房管理-药品报损分页
    *
    * @param pageReqVO 分页查询
    * @return 大药房管理-药品报损分页
    */
    PageResult<PharmacyMedicineLossDO> getPharmacyMedicineLossPage(PharmacyMedicineLossPageReqVO pageReqVO);

    /**
    * 获得大药房管理-药品报损列表
    *
    * @param listReqVO 查询条件
    * @return 大药房管理-药品报损列表
    */
    List<PharmacyMedicineLossDO> getPharmacyMedicineLossList(PharmacyMedicineLossListReqVO listReqVO);

    /**
     * 批量更新报损
     * @param medicineLossList
     * @param id
     */
    void batchSaveMedicineLoss(List<SaveMedicineLossDTO> medicineLossList, String id);
}
