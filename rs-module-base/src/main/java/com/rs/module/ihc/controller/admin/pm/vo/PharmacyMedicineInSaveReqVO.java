package com.rs.module.ihc.controller.admin.pm.vo;

import com.rs.framework.common.annotation.DefaultValueDate;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 大药房管理-药品入库批次新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PharmacyMedicineInSaveReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("药品id，对应ihc_ppm_drug.id")
    @NotNull
    private String medicineId;

    @ApiModelProperty("入库数量")
    @NotNull
    private BigDecimal inNum;

    @ApiModelProperty("批次编码，编号规则：8位日期+3位序列号，如：20250228001")
    @NotNull
    private String batchCode;

    @ApiModelProperty("凭证号")
    private String voucherCode;

    @ApiModelProperty("进价")
    private BigDecimal purchasePrice;

    @ApiModelProperty("批发价")
    private BigDecimal wholesalePrice;

    @ApiModelProperty("调拨价")
    private BigDecimal transferPrice;

    @ApiModelProperty("零售价")
    private BigDecimal retailPrice;

    @ApiModelProperty("入库日期")
    @DefaultValueDate
    private Date inDate;

    @ApiModelProperty("有效期至")
    private Date expireDate;

    @ApiModelProperty("入库方式")
    private String inStorageMethod;

    @ApiModelProperty("调拨方式")
    private String transferMethod;

    @ApiModelProperty("药品货位")
    private String medicinePlace;

    @ApiModelProperty("批次库存数量 最小单位")
    private BigDecimal inventoryNum;


}
