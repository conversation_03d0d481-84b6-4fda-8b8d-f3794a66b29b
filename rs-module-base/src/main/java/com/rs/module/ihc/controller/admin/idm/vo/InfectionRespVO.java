package com.rs.module.ihc.controller.admin.idm.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 传染病管理-传染病管理登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class InfectionRespVO extends BaseVO implements TransPojo{

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("传染病类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_CYB_CRBLX")
    private String infectiousDiseaseType;

    @ApiModelProperty("艾滋病-初筛时间")
    private Date aidsFirstScreeningTime;

    @ApiModelProperty("艾滋病-初筛操作方式 字典：ZD_CYB_CSCZFS")
    @Trans(type = TransType.DICTIONARY,key = "ZD_CYB_CSCZFS")
    private String aidsFirstScreeningMethod;

    @ApiModelProperty("艾滋病-初筛结果 字典：ZD_CYB_CSJG_YXYX")
    private String aidsFirstScreeningResult;

    @ApiModelProperty("艾滋病-确诊日期")
    private Date aidsDiagnosisDate;

    @ApiModelProperty("艾滋病-实验室反馈时间")
    private Date aidsLabFeedbackTime;

    @ApiModelProperty("艾滋病-实验室反馈结果 字典：ZD_CYB_CSJG_YXYX")
    @Trans(type = TransType.DICTIONARY,key = "ZD_CYB_CSJG_YXYX")
    private String aidsLabFeedbackResult;

    @ApiModelProperty("艾滋病-告知在押人员时间")
    private Date aidsInformInmateTime;

    @ApiModelProperty("艾滋病-是否告知家属")
    private String aidsIsInformFamily;

    @ApiModelProperty("艾滋病-告知家属时间")
    private Date aidsInformFamilyTime;

    @ApiModelProperty("告知疾控机构日期")
    private Date gzjkjgrq;

    @ApiModelProperty("疾控机构名称")
    private String jkjgrqmc;

    @ApiModelProperty("是否接收转入疾控机构名称反馈信息")
    private String sfjszrjkjgfkxx;

    @ApiModelProperty("接收转入疾控机构反馈信息日期")
    private Date jszrjkjgfkxxrq;

    @ApiModelProperty("是否需要再次联系转入疾控机构")
    private String sfxyzclxzrjkjg;

    @ApiModelProperty("再次联系转入疾控机构日期")
    private Date zclxzrjkjgrq;

    @ApiModelProperty("送交人")
    private String submitter;

    @ApiModelProperty("附件地址")
    private String attUrl;

    @ApiModelProperty("登记民警身份证号")
    private String operatePoliceSfzh;

    @ApiModelProperty("登记民警")
    private String operatePolice;

    @ApiModelProperty("登记时间")
    private Date operateTime;

    @ApiModelProperty("健康体检时间")
    private Date checkupTime;

    @ApiModelProperty("健康体检方式：ZD_CYB_JKTJFS")
    private String checkupMethod;

    @ApiModelProperty("健康体检检查方式：ZD_CYB_CSJG_YXYX")
    private String checkupResult;

    @ApiModelProperty("健康体检检查人")
    private String checkupExaminer;
}
