package com.rs.module.ihc.service.pm;

import java.util.*;
import javax.validation.*;

import com.rs.module.ihc.controller.admin.pm.dto.SaveMedicineLossDTO;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.MedicineLossDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 药房管理-药品报损 Service 接口
 *
 * <AUTHOR>
 */
public interface MedicineLossService extends IBaseService<MedicineLossDO>{

    /**
     * 创建药房管理-药品报损
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMedicineLoss(@Valid MedicineLossSaveReqVO createReqVO);

    /**
     * 更新药房管理-药品报损
     *
     * @param updateReqVO 更新信息
     */
    void updateMedicineLoss(@Valid MedicineLossSaveReqVO updateReqVO);

    /**
     * 删除药房管理-药品报损
     *
     * @param id 编号
     */
    void deleteMedicineLoss(String id);

    /**
     * 获得药房管理-药品报损
     *
     * @param id 编号
     * @return 药房管理-药品报损
     */
    MedicineLossDO getMedicineLoss(String id);

    /**
    * 获得药房管理-药品报损分页
    *
    * @param pageReqVO 分页查询
    * @return 药房管理-药品报损分页
    */
    PageResult<MedicineLossDO> getMedicineLossPage(MedicineLossPageReqVO pageReqVO);

    /**
    * 获得药房管理-药品报损列表
    *
    * @param listReqVO 查询条件
    * @return 药房管理-药品报损列表
    */
    List<MedicineLossDO> getMedicineLossList(MedicineLossListReqVO listReqVO);


    /**
     * 批量新增报损
     * @param medicineLossList 报损列表
     * @param medicineId 药品id
     */
    void batchSaveMedicineLoss(List<SaveMedicineLossDTO> medicineLossList, String medicineId);
}
