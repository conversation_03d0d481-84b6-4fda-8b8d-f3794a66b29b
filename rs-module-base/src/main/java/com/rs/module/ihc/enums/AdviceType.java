package com.rs.module.ihc.enums;

/**
 * @ClassName AdviceType
 * <AUTHOR>
 * @Date 2025/3/26 11:02
 * @Version 1.0
 */
public enum AdviceType {
    //1长期医嘱2临时医嘱
    LONG_TERM_ADVICE("长期医嘱", "1"),
    TEMPORARY_ADVICE("临时医嘱", "2");

    private String desc;
    private String val;

    AdviceType(String desc, String val) {
        this.desc = desc;
        this.val = val;
    }

    public String getDesc() {
        return desc;
    }

    public String getVal() {
        return val;
    }
}
