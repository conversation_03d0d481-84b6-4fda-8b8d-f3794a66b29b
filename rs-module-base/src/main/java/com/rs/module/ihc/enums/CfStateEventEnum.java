package com.rs.module.ihc.enums;

/**
 * @ClassName CfStatusEventEnum
 * @Description 处方事件
 * <AUTHOR>
 * @Date 2025/3/25 18:20
 * @Version 1.0
 */
public enum CfStateEventEnum {
    CKSL("处方药品出库次数为0", "0"),
    SYZ("该处方的药库-处方药品出库次数>= 1 and 该处方未作废 and 该处方未结束 and 该处方未暂停","1"),
    YZF("医生将该处方作废","2"),
    YZT("医生将该处方暂停","3"),
    YJS("医生结束处方 or 在押人员出所 or 在押人员 所内死亡。","4"),
    ;

    private String desc;
    private String val;

    CfStateEventEnum(String desc, String val) {
        this.desc = desc;
        this.val = val;
    }

    public String getDesc() {
        return desc;
    }
    public String getVal() {
        return val;
    }
}
