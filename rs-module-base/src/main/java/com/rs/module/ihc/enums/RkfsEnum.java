package com.rs.module.ihc.enums;

/**
 * @ClassName RKFSenum
 * @Description 入库方式
 * <AUTHOR>
 * @Date 2025/3/21 11:39
 * @Version 1.0
 */
public enum RkfsEnum {
//1药物顾送、2采购入库、3调拨入库
    YWGS("1", "药物顾送"),
    CGRK("2", "采购入库"),
    DBRK("3", "调拨入库");

    private String code;
    private String desc;

    RkfsEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
