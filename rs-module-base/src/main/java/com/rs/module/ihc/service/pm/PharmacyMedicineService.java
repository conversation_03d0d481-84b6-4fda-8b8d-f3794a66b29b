package com.rs.module.ihc.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineListReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicinePageReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineRespVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineSaveReqVO;
import com.rs.module.ihc.entity.pm.PharmacyMedicineDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 大药房管理-药品信息 Service 接口
 *
 * <AUTHOR>
 */
public interface PharmacyMedicineService extends IBaseService<PharmacyMedicineDO>{

    /**
     * 创建大药房管理-药品信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPharmacyMedicine(@Valid PharmacyMedicineSaveReqVO createReqVO);

    /**
     * 更新大药房管理-药品信息
     *
     * @param updateReqVO 更新信息
     */
    void updatePharmacyMedicine(@Valid PharmacyMedicineSaveReqVO updateReqVO);

    /**
     * 删除大药房管理-药品信息
     *
     * @param id 编号
     */
    void deletePharmacyMedicine(String id);

    /**
     * 获得大药房管理-药品信息
     *
     * @param id 编号
     * @return 大药房管理-药品信息
     */
    PharmacyMedicineDO getPharmacyMedicine(String id);

    /**
     *
     * @param id
     * @return
     */
    PharmacyMedicineRespVO getMedicineDetail(String id);


    /**
    * 获得大药房管理-药品信息分页
    *
    * @param pageReqVO 分页查询
    * @return 大药房管理-药品信息分页
    */
    PageResult<PharmacyMedicineDO> getPharmacyMedicinePage(PharmacyMedicinePageReqVO pageReqVO);

    /**
    * 获得大药房管理-药品信息列表
    *
    * @param listReqVO 查询条件
    * @return 大药房管理-药品信息列表
    */
    List<PharmacyMedicineDO> getPharmacyMedicineList(PharmacyMedicineListReqVO listReqVO);


}
