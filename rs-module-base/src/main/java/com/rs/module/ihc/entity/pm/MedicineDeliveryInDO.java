package com.rs.module.ihc.entity.pm;

import lombok.*;
import java.util.*;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 药房管理-顾送药品入库批次 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_pm_medicine_delivery_in")
@KeySequence("ihc_pm_medicine_delivery_in_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MedicineDeliveryInDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 药品id，对应ihc_ppm_drug.id
     */
    private String drugId;
    /**
     * 入库数量
     */
    private BigDecimal inNum;
    /**
     * 批次编码，编号规则：8位日期+3位序列号，如：20250228001
     */
    private String batchCode;
    /**
     * 供药单位，对应ihc_pm_supplier.id
     */
    private Long supplierId;
    /**
     * 供药单位名称
     */
    private String supplierName;
    /**
     * 凭证号
     */
    private String voucherCode;
    /**
     * 进价
     */
    private BigDecimal purchasePrice;
    /**
     * 批发价
     */
    private BigDecimal wholesalePrice;
    /**
     * 调拨价
     */
    private BigDecimal transferPrice;
    /**
     * 零售价
     */
    private BigDecimal retailPrice;
    /**
     * 入库日期
     */
    private Date inDate;
    /**
     * 有效期至
     */
    private Date expireDate;
    /**
     * 入库方式
     */
    private String inStorageMethod;
    /**
     * 调拨方式
     */
    private String transferMethod;
    /**
     * 药品货位
     */
    private String medicinePlace;
    /**
     * 批次库存数量 最小单位
     */
    private BigDecimal inventoryNum;
    /**
     * 当前药品总库存数量 最小单位
     */
    private BigDecimal totalInventoryNum;
    /**
     * 入库状态
     */
    private String inStatus;
    /**
     * 流程是否结束 0=否 1=是
     */
    private Integer workflowEnd;

}
