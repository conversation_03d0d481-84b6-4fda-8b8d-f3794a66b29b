package com.rs.module.ihc.service.pm;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.PharmacyDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.pm.PharmacyDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 药房管理-药房信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PharmacyServiceImpl extends BaseServiceImpl<PharmacyDao, PharmacyDO> implements PharmacyService {

    @Resource
    private PharmacyDao pharmacyDao;

    @Override
    public String createPharmacy(PharmacySaveReqVO createReqVO) {
        // 插入
        PharmacyDO pharmacy = BeanUtils.toBean(createReqVO, PharmacyDO.class);
        pharmacyDao.insert(pharmacy);
        // 返回
        return pharmacy.getId();
    }

    @Override
    public void updatePharmacy(PharmacySaveReqVO updateReqVO) {
        // 校验存在
        validatePharmacyExists(updateReqVO.getId());
        // 更新
        PharmacyDO updateObj = BeanUtils.toBean(updateReqVO, PharmacyDO.class);
        pharmacyDao.updateById(updateObj);
    }

    @Override
    public void deletePharmacy(String id) {
        // 校验存在
        validatePharmacyExists(id);
        // 删除
        pharmacyDao.deleteById(id);
    }

    private void validatePharmacyExists(String id) {
        if (pharmacyDao.selectById(id) == null) {
            throw new ServerException("药房管理-药房信息数据不存在");
        }
    }

    @Override
    public PharmacyDO getPharmacy(String id) {
        return pharmacyDao.selectById(id);
    }

    @Override
    public PageResult<PharmacyDO> getPharmacyPage(PharmacyPageReqVO pageReqVO) {
        return pharmacyDao.selectPage(pageReqVO);
    }

    @Override
    public List<PharmacyDO> getPharmacyList(PharmacyListReqVO listReqVO) {
        return pharmacyDao.selectList(listReqVO);
    }


}
