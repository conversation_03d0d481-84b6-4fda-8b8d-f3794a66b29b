package com.rs.module.ihc.controller.admin.ephe.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 卫生防疫与健康教育-疫情处置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EpidemicRespPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("是否出现聚集性疫情")
    private String sfcxzjxyq;

    @ApiModelProperty("出现聚集性疫情日期")
    private Date cxzjxyqrq;

    @ApiModelProperty("聚集性疫情处置情况")
    private String zjxyqczqk;

    @ApiModelProperty("报告方式 字典：ZD_CYB_JHBSCBGFS ")
    private String bgfs;

    @ApiModelProperty("传染病报告卡填报日期")
    private Date crbbgktbrq;

    @ApiModelProperty("传染病报告卡")
    private String crbbgk;

    @ApiModelProperty("登记民警身份证号")
    private String operatePoliceSfzh;

    @ApiModelProperty("登记民警")
    private String operatePolice;

    @ApiModelProperty("登记时间")
    private Date[] operateTime;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
