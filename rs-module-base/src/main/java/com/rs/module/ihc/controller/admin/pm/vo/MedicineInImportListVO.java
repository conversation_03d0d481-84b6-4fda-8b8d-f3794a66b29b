package com.rs.module.ihc.controller.admin.pm.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

@ApiModel(description = "管理后台 - 药房管理-药品入库批次 Response VO")
@Data
public class MedicineInImportListVO {
private static final long serialVersionUID = 1L;
    List<MedicineInImportVO> medicineInImportVOList;
    List<MedicineInImportVO> medicineInImportVOErrorList;
    /**
     * 随货单号
     */
    public String shdh;
    /**
     * 采购日期
     */
    public String cgrq;
    /**
     * 负责人
     */
    public String fzr;
}
