package com.rs.module.ihc.service.pm.sick;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.ihc.controller.admin.pm.sick.vo.*;
import com.rs.module.ihc.entity.pm.sick.SeverelySickReliveApproveDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.pm.sick.SeverelySickReliveApproveDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 重特病号解除申请审批 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SeverelySickReliveApproveServiceImpl extends BaseServiceImpl<SeverelySickReliveApproveDao, SeverelySickReliveApproveDO> implements SeverelySickReliveApproveService {

    @Resource
    private SeverelySickReliveApproveDao severelySickReliveApproveDao;

    @Override
    public String createSeverelySickReliveApprove(SeverelySickReliveApproveSaveReqVO createReqVO) {
        // 插入
        SeverelySickReliveApproveDO severelySickReliveApprove = BeanUtils.toBean(createReqVO, SeverelySickReliveApproveDO.class);
        severelySickReliveApproveDao.insert(severelySickReliveApprove);
        // 返回
        return severelySickReliveApprove.getId();
    }

    @Override
    public void updateSeverelySickReliveApprove(SeverelySickReliveApproveSaveReqVO updateReqVO) {
        // 校验存在
        validateSeverelySickReliveApproveExists(updateReqVO.getId());
        // 更新
        SeverelySickReliveApproveDO updateObj = BeanUtils.toBean(updateReqVO, SeverelySickReliveApproveDO.class);
        severelySickReliveApproveDao.updateById(updateObj);
    }

    @Override
    public void deleteSeverelySickReliveApprove(String id) {
        // 校验存在
        validateSeverelySickReliveApproveExists(id);
        // 删除
        severelySickReliveApproveDao.deleteById(id);
    }

    private void validateSeverelySickReliveApproveExists(String id) {
        if (severelySickReliveApproveDao.selectById(id) == null) {
            throw new ServerException("重特病号解除申请审批数据不存在");
        }
    }

    @Override
    public SeverelySickReliveApproveDO getSeverelySickReliveApprove(String id) {
        return severelySickReliveApproveDao.selectById(id);
    }

    @Override
    public PageResult<SeverelySickReliveApproveDO> getSeverelySickReliveApprovePage(SeverelySickReliveApprovePageReqVO pageReqVO) {
        return severelySickReliveApproveDao.selectPage(pageReqVO);
    }

    @Override
    public List<SeverelySickReliveApproveDO> getSeverelySickReliveApproveList(SeverelySickReliveApproveListReqVO listReqVO) {
        return severelySickReliveApproveDao.selectList(listReqVO);
    }


}
