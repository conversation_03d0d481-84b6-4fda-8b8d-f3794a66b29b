package com.rs.module.ihc.entity.ephe;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 卫生防疫与健康教育-健康教育 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_ephe_health_edu")
@KeySequence("ihc_ephe_health_edu_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ihc_ephe_health_edu")
public class HealthEduDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 宣教课程
     */
    private String xjkc;
    /**
     * 健康教育情况
     */
    private String jkjyqk;
    /**
     * 宣教范围
     */
    private String cjfw;
    /**
     * 宣教时段开始时间
     */
    private Date xjsdKssj;
    /**
     * 宣教时段结束时间
     */
    private Date xjsdJssj;
    /**
     * 宣教人姓名
     */
    private String xjrxm;
    /**
     * 登记民警身份证号
     */
    private String operatePoliceSfzh;
    /**
     * 登记民警
     */
    private String operatePolice;
    /**
     * 登记时间
     */
    private Date operateTime;

}
