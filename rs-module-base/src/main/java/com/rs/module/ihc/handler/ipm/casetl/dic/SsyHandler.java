package com.rs.module.ihc.handler.ipm.casetl.dic;

import com.rs.framework.common.handler.AbstractHandler;
import com.rs.module.ihc.controller.admin.ipm.vo.CaseTemplateDicRespV2VO;
import com.rs.module.ihc.controller.admin.ipm.vo.CaseTemplateDicRespVO;
import org.springframework.stereotype.Component;

/**
 * @ClassName SsyHandler
 * @Description 血压-收缩压
 * <AUTHOR>
 * @Date 2025/3/21 17:13
 * @Version 1.0
 */
@Component
public class SsyHandler extends AbstractHandler<CaseTemplateDicRespV2VO> {
    @Override
    protected CaseTemplateDicRespV2VO doHandle(CaseTemplateDicRespV2VO input) {
        //血压-收缩压 间隔是2
        for (int i = 40; i <= 200; i = i + 2) {
            CaseTemplateDicRespVO caseTemplateDicRespVO = new CaseTemplateDicRespVO();
            caseTemplateDicRespVO.setLable(String.valueOf(i));
            input.getSsy().add(caseTemplateDicRespVO);
        }

        return input;
    }
}
