package com.rs.module.ihc.service.pm;

import com.rs.framework.mybatis.util.BizAssert;
import com.rs.module.ihc.controller.admin.ipm.bo.UpdateMedicineInInventoryNumBO;
import com.rs.module.ihc.controller.admin.pm.dto.SaveMedicineLossDTO;
import com.rs.module.ihc.dao.pm.MedicineDao;
import com.rs.module.ihc.entity.pm.MedicineDO;
import com.rs.module.ihc.entity.pm.MedicineInDO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.MedicineLossDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.pm.MedicineLossDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 药房管理-药品报损 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MedicineLossServiceImpl extends BaseServiceImpl<MedicineLossDao, MedicineLossDO> implements MedicineLossService {

    @Resource
    private MedicineLossDao medicineLossDao;

    @Resource
    private MedicineDao medicineDao;
    @Resource
    private MedicineInService medicineInService;

    @Override
    public String createMedicineLoss(MedicineLossSaveReqVO createReqVO) {
        // 插入
        MedicineLossDO medicineLoss = BeanUtils.toBean(createReqVO, MedicineLossDO.class);
        medicineLossDao.insert(medicineLoss);
        // 返回
        return medicineLoss.getId();
    }

    @Override
    public void updateMedicineLoss(MedicineLossSaveReqVO updateReqVO) {
        // 校验存在
        validateMedicineLossExists(updateReqVO.getId());
        // 更新
        MedicineLossDO updateObj = BeanUtils.toBean(updateReqVO, MedicineLossDO.class);
        medicineLossDao.updateById(updateObj);
    }

    @Override
    public void deleteMedicineLoss(String id) {
        // 校验存在
        validateMedicineLossExists(id);
        // 删除
        medicineLossDao.deleteById(id);
    }

    private void validateMedicineLossExists(String id) {
        if (medicineLossDao.selectById(id) == null) {
            throw new ServerException("药房管理-药品报损数据不存在");
        }
    }

    @Override
    public MedicineLossDO getMedicineLoss(String id) {
        return medicineLossDao.selectById(id);
    }

    @Override
    public PageResult<MedicineLossDO> getMedicineLossPage(MedicineLossPageReqVO pageReqVO) {
        return medicineLossDao.selectPage(pageReqVO);
    }

    @Override
    public List<MedicineLossDO> getMedicineLossList(MedicineLossListReqVO listReqVO) {
        return medicineLossDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveMedicineLoss(List<SaveMedicineLossDTO> medicineLossList, String  medicineId) {
        MedicineDO ihcsMedicine = medicineDao.selectById(medicineId);
        BizAssert.notNull(ihcsMedicine, "药品不存在");
        Map<String, MedicineInDO> ihcsMedicineInMap = medicineInService.lambdaQuery()
                .in(MedicineInDO::getId, medicineLossList.stream()
                        .map(SaveMedicineLossDTO::getMedicineInId)
                        .collect(Collectors.toList()))
                .list()
                .stream()
                .collect(Collectors.toMap(MedicineInDO::getId, Function.identity()));
        // 药品库存数量
        final BigDecimal sourceInventoryNum = ihcsMedicine.getTotalInventoryNum();
        BigDecimal targetInventoryNum = sourceInventoryNum;
        // 记录每个批次的扣减记录
        Map<String, UpdateMedicineInInventoryNumBO> batchInventoryNumMap = new LinkedHashMap<>();
        List<MedicineLossDO> ihcsMedicineOutList = new ArrayList<>();
        for (SaveMedicineLossDTO saveMedicineOutDTO : medicineLossList) {
            String medicineInId = saveMedicineOutDTO.getMedicineInId();
            MedicineInDO ihcsMedicineIn = ihcsMedicineInMap.get(medicineInId);
            BizAssert.notNull(ihcsMedicineIn, "入库批次id[" + medicineInId + "]不存在");
            String batchCode = ihcsMedicineIn.getBatchCode();
            BizAssert.isTrue(Objects.equals(ihcsMedicineIn.getMedicineId(), medicineId),
                    "入库批次[" + batchCode + "]不属于该药品");
            // 报损数量
            BigDecimal lossNum = saveMedicineOutDTO.getLossNum();
            // 批次库存数量
            BigDecimal batchSourceInventoryNum = ihcsMedicineIn.getInventoryNum();
            BizAssert.isTrue(batchSourceInventoryNum.compareTo(lossNum) >= 0,
                    "批次[" + batchCode + "]报损数量不能大于库存数量");
            // 药品总库存扣减
            targetInventoryNum = targetInventoryNum.subtract(lossNum);
            // 这里是由于可能出现批量报损时，一个批次在一次请求中分两个记录来出
            UpdateMedicineInInventoryNumBO updateMedicineInInventoryNumBO =
                    batchInventoryNumMap.computeIfAbsent(medicineId, key -> UpdateMedicineInInventoryNumBO.builder()
                            .id(medicineInId)
                            .sourceInventoryNum(batchSourceInventoryNum)
                            .targetInventoryNum(batchSourceInventoryNum)
                            .build());
            // 批次库存数量扣减
            BigDecimal batchTargetInventoryNum = updateMedicineInInventoryNumBO.getTargetInventoryNum().subtract(lossNum);
            BizAssert.isTrue(batchTargetInventoryNum.signum() >= 0, "批次[" + batchCode + "]报损数量不能大于库存数量");
            updateMedicineInInventoryNumBO.setTargetInventoryNum(batchTargetInventoryNum);
            // 报损记录入库
            MedicineLossDO ihcsMedicineLoss = this.convertToIhcsMedicineLoss(saveMedicineOutDTO);
            ihcsMedicineLoss.setMedicineId(medicineId);
            ihcsMedicineLoss.setInventoryNum(batchTargetInventoryNum);
            ihcsMedicineLoss.setTotalInventoryNum(targetInventoryNum);
            ihcsMedicineOutList.add(ihcsMedicineLoss);
        }
        // 报损记录保存
        this.saveBatch(ihcsMedicineOutList);
        // 批次库存更新
        medicineInService.batchCompareAndSetInventoryNum(batchInventoryNumMap.values());
        // 药品库存更新
        medicineDao.compareAndSetMedicineInventoryNum(medicineId, sourceInventoryNum, targetInventoryNum);
        // 药品是否存在过期批次标识更新，必须再批次库存更新后执行，否则查询时会有问题
        medicineInService.selectExpireBatchAndUpdateMedicineFlag(Collections.singletonList(medicineId));
    }

    private MedicineLossDO convertToIhcsMedicineLoss(SaveMedicineLossDTO saveMedicineOutDTO) {
        return MedicineLossDO.builder()
                .medicineInId(saveMedicineOutDTO.getMedicineInId())
                .lossReason(saveMedicineOutDTO.getLossReason())
                .lossNum(saveMedicineOutDTO.getLossNum())
                .lossTime(saveMedicineOutDTO.getLossTime())
                .remark(saveMedicineOutDTO.getRemark())
                .build();
    }


}
