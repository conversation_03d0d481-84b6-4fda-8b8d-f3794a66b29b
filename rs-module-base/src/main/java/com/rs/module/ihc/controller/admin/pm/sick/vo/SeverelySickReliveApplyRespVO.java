package com.rs.module.ihc.controller.admin.pm.sick.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 重特病号解除申请 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SeverelySickReliveApplyRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键id")
    private String id;
    @ApiModelProperty("关联重特病号管理表id")
    private String manageId;
    @ApiModelProperty("被监管人id")
    private String prisonerId;
    @ApiModelProperty("解除申请时间")
    private Date relieveApplyTime;
    @ApiModelProperty("解除原因")
    private String relieveReason;
    @ApiModelProperty("解除申请人id")
    private Long relieveUserId;
    @ApiModelProperty("解除申请人姓名")
    private String relieveUserName;
    @ApiModelProperty("最终审批结果 字典值：is_agree")
    private String finalApproveResult;
    @ApiModelProperty("解除时间")
    private Date relieveTime;
}
