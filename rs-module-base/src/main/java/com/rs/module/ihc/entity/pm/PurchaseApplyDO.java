package com.rs.module.ihc.entity.pm;

import lombok.*;
import java.util.*;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 药品管理-药品采购申请 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_pm_purchase_apply")
@KeySequence("ihc_pm_purchase_apply_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseApplyDO extends BaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 所属省级代码
     */
    private String proCode;
    /**
     * 所属省级名称
     */
    private String proName;
    /**
     * 采购申请编号，编号规则：YPCG+8位日期+4位序列号，如：YPCG202503040002
     */
    private String applyNum;
    /**
     * 药品来源
     */
    private String medicineSource;
    /**
     * 医生意见
     */
    private String doctorOpinion;
    /**
     * 卫生所负责人意见
     */
    private String chargeOpinion;
    /**
     * 分管领导意见
     */
    private String leaderOpinion;
    /**
     * 审批意见
     */
    private String approveOpinion;
    /**
     * 监所id
     */
    private String prisonId;

}
