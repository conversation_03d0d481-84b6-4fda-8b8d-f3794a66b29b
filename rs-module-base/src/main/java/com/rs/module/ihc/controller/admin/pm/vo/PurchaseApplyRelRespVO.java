package com.rs.module.ihc.controller.admin.pm.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 药品管理-药品采购申请-药品信息关联 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 药品管理-药品采购申请-药品信息关联新增/修改 Request VO")
@TableName("ihc_pm_purchase_apply_rel")
@KeySequence("ihc_pm_purchase_apply_rel_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseApplyRelRespVO extends MedicineRespVO {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;
    /**
     * 所属省级代码
     */
    @ApiModelProperty("所属省级代码")
    private String proCode;
    /**
     * 所属省级名称
     */
    @ApiModelProperty("所属省级名称")
    private String proName;
    /**
     * 药品所属类型（ 1：药品 2：卫材）
     */
    @ApiModelProperty("药品所属类型（ 1：药品 2：卫材）")
    private Integer medicineType;
    /**
     * 采购申请id，对应ihc_pm_purchase_apply.id
     */
    @ApiModelProperty("采购申请id，对应ihc_pm_purchase_apply.id")
    private String mlh;
    /**
     * 药品id，对应ihc_pm_medicine.id
     */
    @ApiModelProperty("药品id，对应ihc_pm_medicine.id")
    private String medicineId;
    /**
     * 采购数量
     */
    @ApiModelProperty("采购数量")
    private Integer num;

    /**
     * 单价
     */
    @ApiModelProperty("单价")
    private Float unitPrice;


}
