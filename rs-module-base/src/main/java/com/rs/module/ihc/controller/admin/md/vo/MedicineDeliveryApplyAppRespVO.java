package com.rs.module.ihc.controller.admin.md.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 药品顾送管理-药品顾送申请-app端 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MedicineDeliveryApplyAppRespVO extends BaseVO implements TransPojo{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("年龄")
    private Integer age;

    @ApiModelProperty("性别")
    @Trans(type = TransType.DICTIONARY,key = "ZD_XB")
    private String sex;

    @ApiModelProperty("申请-药品名称")
    private String applyMedicineName;

    @ApiModelProperty("申请-剂型（字典:剂型  ZD_DOSAGE_FORM）")
    @Trans(type = TransType.DICTIONARY,key = "ZD_DOSAGE_FORM")
    private String applyDosageForm;

    @ApiModelProperty("申请规格")
    private String applySpecs;

    @ApiModelProperty("申请-数量")
    private BigDecimal applyTotalNum;

    @ApiModelProperty("顾送药来源 字典：ZD_YPGS_GSYLY")
    @Trans(type = TransType.DICTIONARY,key = "ZD_YPGS_GSYLY")
    private String drugSource;

    @ApiModelProperty("顾送药原因 字典： ZD_YPGS_GSYY")
    @Trans(type = TransType.DICTIONARY,key = "ZD_YPGS_GSYY")
    private String deliveryReason;

    @ApiModelProperty("实际送药-药品名称")
    private String deliveryMedicineName;

    @ApiModelProperty("实际送药--剂型（字典:ZD_DOSAGE_FORM ）")
    @Trans(type = TransType.DICTIONARY,key = "ZD_DOSAGE_FORM")
    private String deliveryDosageForm;

    @ApiModelProperty("送药日期")
    //@DefaultValueDate
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryDate;

    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY,key = "ZD_YPGS_ZT")
    private String status;

    @ApiModelProperty("登记人")
    private String addUserName;

    @ApiModelProperty("登记时间")
    private Date addTime;

}
