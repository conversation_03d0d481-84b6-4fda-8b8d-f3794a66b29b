package com.rs.module.ihc.controller.admin.ipm.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class PrescribeStatusChangeReqVO {

    /**
     * 处方id
     */
    @NotNull(message = "处方id不能为空")
    @ApiModelProperty(value = "处方id", required = true)
    private String prescribeId;

    /**
     * 处方状态
     *
     */
    @NotBlank(message = "处方状态不能为空")
    @ApiModelProperty(value = "处方状态", required = true)
    private String prescribeStatus;
}
