package com.rs.module.ihc.controller.admin.ephe.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 卫生防疫与健康教育--监室消毒新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RoomDisinfectSaveReqVO extends BaseVO{

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("数据来源(实战平台:1, 仓外屏:2, 仓内屏:3)")
    @NotEmpty(message = "数据来源(实战平台:1, 仓外屏:2, 仓内屏:3)不能为空")
    private String dataSources;

    @ApiModelProperty("监室ID")
    @NotEmpty(message = "监室ID不能为空")
    private String roomId;

    @ApiModelProperty("监室名称")
    @NotEmpty(message = "监室名称不能为空")
    private String roomName;

    @ApiModelProperty("消毒方式")
    @NotEmpty(message = "消毒方式不能为空")
    private String disinfectMethod;

    @ApiModelProperty("消毒时间")
    @NotNull(message = "消毒时间不能为空")
    private Date disinfectTime;

}
