package com.rs.module.ihc.service.ipm;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.BizException;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.util.BizAssert;
import com.rs.framework.mybatis.util.PrisonerUtils;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.dao.ipm.PrescribeMedicineDao;
import com.rs.module.ihc.dao.ipm.PrescribeOutDao;
import com.rs.module.ihc.dao.ipm.PrescribeOutMedicineDao;
import com.rs.module.ihc.entity.ipm.PrescribeDO;
import com.rs.module.ihc.entity.ipm.PrescribeMedicineDO;
import com.rs.module.ihc.entity.ipm.PrescribeOutDO;
import com.rs.module.ihc.entity.ipm.PrescribeOutMedicineDO;
import com.rs.module.ihc.enums.InternalMedicalPrescribeStatusEnum;
import com.rs.module.ihc.service.pm.MedicineOutService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 所内就医-处方药品出库记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrescribeOutServiceImpl extends BaseServiceImpl<PrescribeOutDao, PrescribeOutDO> implements PrescribeOutService {

    @Resource
    private PrescribeOutDao prescribeOutDao;
    @Resource
    private PrescribeOutMedicineDao prescribeOutMedicineDao;
    @Resource
    private PrescribeService prescribeService;

    @Resource
    private PrescribeMedicineDao prescribeMedicineDao;
    @Resource
    private MedicineOutService medicineOutService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createPrescribeOut(PrescribeOutSaveReqVO createReqVO) {
        // 插入
        PrescribeOutDO prescribeOut = BeanUtils.toBean(createReqVO, PrescribeOutDO.class);
        prescribeOutDao.insert(prescribeOut);

        // 插入子表
        createPrescribeOutMedicineList(prescribeOut.getId(), createReqVO.getPrescribeOutMedicines());
        // 返回
        return prescribeOut.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePrescribeOut(PrescribeOutSaveReqVO updateReqVO) {
        // 校验存在
        validatePrescribeOutExists(updateReqVO.getId());
        // 更新
        PrescribeOutDO updateObj = BeanUtils.toBean(updateReqVO, PrescribeOutDO.class);
        prescribeOutDao.updateById(updateObj);

        // 更新子表
        updatePrescribeOutMedicineList(updateReqVO.getId(), updateReqVO.getPrescribeOutMedicines());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePrescribeOut(String id) {
        // 校验存在
        validatePrescribeOutExists(id);
        // 删除
        prescribeOutDao.deleteById(id);

        // 删除子表
        deletePrescribeOutMedicineByOutId(id);
    }

    private void validatePrescribeOutExists(String id) {
        if (prescribeOutDao.selectById(id) == null) {
            throw new ServerException("所内就医-处方药品出库记录数据不存在");
        }
    }

    @Override
    public PrescribeOutDO getPrescribeOut(String id) {
        return prescribeOutDao.selectById(id);
    }

    @Override
    public PrescribeOutRespVO getPrescribeOutById(String id) {
        return prescribeOutDao.getPrescribeOutById(id);
    }

    @Override
    public PageResult<PrescribeOutDO> getPrescribeOutPage(PrescribeOutPageReqVO pageReqVO) {
        return prescribeOutDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrescribeOutDO> getPrescribeOutList(PrescribeOutListReqVO listReqVO) {
        return prescribeOutDao.selectList(listReqVO);
    }


    // ==================== 子表（所内就医-处方药品出库记录关联药品） ====================

    @Override
    public List<PrescribeOutMedicineDO> getPrescribeOutMedicineListByOutId(String outId) {
        return prescribeOutMedicineDao.selectListByOutId(outId);
    }


    private void createPrescribeOutMedicineList(String outId, List<PrescribeOutMedicineDO> list) {
        list.forEach(o -> o.setOutId(outId));
        list.forEach(o -> prescribeOutMedicineDao.insert(o));
    }

    private void updatePrescribeOutMedicineList(String outId, List<PrescribeOutMedicineDO> list) {
        deletePrescribeOutMedicineByOutId(outId);
        list.forEach(o -> o.setId(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createPrescribeOutMedicineList(outId, list);
    }

    private void deletePrescribeOutMedicineByOutId(String outId) {
        prescribeOutMedicineDao.deleteByOutId(outId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void prescribeMedicineOut(PrescribeMedicineOutReqVO prescribeMedicineOutDTO) {
        final String prescribeId = prescribeMedicineOutDTO.getPrescribeId();
        PrescribeDO prisonerPrescribeById = prescribeService.getById(prescribeId);
        BizAssert.notNull(prisonerPrescribeById, "处方不存在");
        // 判断处方是否能出库
        InternalMedicalPrescribeStatusEnum.checkIsCanExecute(prisonerPrescribeById.getPrescribeStatus());
        List<PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo> prescribeMedicineOutInfoList =
                prescribeMedicineOutDTO.getPrescribeMedicineOutInfoList();
        Integer outNum = prisonerPrescribeById.getOutNum();
        if (outNum == null) {
            outNum = 0;
        }
        boolean update = prescribeService.lambdaUpdate()
                .set(PrescribeDO::getLastOutTime, new Date())
                .set(PrescribeDO::getPrescribeStatus, InternalMedicalPrescribeStatusEnum.USE.getCode())
                .set(PrescribeDO::getOutNum, outNum + 1)
                .eq(PrescribeDO::getId, prescribeId)
                .eq(PrescribeDO::getOutNum, outNum)
                .update();
        BizAssert.isTrue(update, "该处方出库重复点击或者与他人同时出库冲突，请重试");
        // 检查药品是否属于处方
        Set<String> medicineIds = prescribeMedicineOutInfoList.stream()
                .map(PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo::getMedicineId)
                .collect(Collectors.toSet());

        List<PrescribeMedicineDO> prescribeMedicines = prescribeMedicineDao.selectList(
                        Wrappers.lambdaQuery(PrescribeMedicineDO.class)
                                .eq(PrescribeMedicineDO::getMlh, prescribeId)
                                .in(PrescribeMedicineDO::getMedicineId, medicineIds));
        this.checkMedicineIsFromPrescribe(medicineIds, prescribeMedicines);
        // 获取处方药品信息的扣减批次信息，按照入库时间顺序，依次扣减库存
        this.medicineOutService.batchAutoMedicalOut(prescribeMedicineOutInfoList, String.format("处方编号：%s，被监管人：%s", prisonerPrescribeById.getPrescribeNum(), PrisonerUtils.getPrisonerByJgrybm(prisonerPrescribeById.getJgrybm()).getXm()));
        // 添加处方出库记录
        SessionUser user = SessionUserUtil.getSessionUser();
        PrescribeOutDO ihcsInternalMedicalPrescribeOut = PrescribeOutDO.builder()
                .prescribeId(prescribeId)
                .jgrybm(prisonerPrescribeById.getJgrybm())
                .prescribeDescribe(prisonerPrescribeById.getPrescribeDescribe())
                .doctorAdvice(prisonerPrescribeById.getDoctorAdvice())
                .build();
        this.save(ihcsInternalMedicalPrescribeOut);
        String outId = ihcsInternalMedicalPrescribeOut.getId();
        // 添加处方出库药品记录
        this.savePrescribeOutMedicine(prescribeMedicineOutInfoList, outId, prescribeMedicines);
    }

    /**
     * 检查药品是否属于处方
     *
     * @param medicineIds        药品id
     * @param prescribeMedicines 处方药品信息
     */
    private void checkMedicineIsFromPrescribe(Collection<String> medicineIds, List<PrescribeMedicineDO> prescribeMedicines) {
        prescribeMedicines.stream()
                .filter(o -> !medicineIds.contains(o.getMedicineId()))
                .findFirst()
                .ifPresent(o -> {
                    throw new BizException(String.format("药品id[%s]不属于该处方", o.getMedicineId()));
                });
    }

    private void savePrescribeOutMedicine(List<PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo> prescribeMedicineOutInfoList,
                                          String outId,
                                          List<PrescribeMedicineDO> prescribeMedicines) {
        Map<String, PrescribeMedicineDO> prescribeMedicineMap = prescribeMedicines.stream()
                .collect(Collectors.toMap(PrescribeMedicineDO::getMedicineId,
                        Function.identity()));
        List<PrescribeOutMedicineDO> outMedicines = prescribeMedicineOutInfoList.stream()
                .map(o -> {
                    String medicineId = o.getMedicineId();
                    PrescribeMedicineDO medicineInfo = prescribeMedicineMap.get(medicineId);
                    BizAssert.notNull(medicineInfo, String.format("处方药品不存在，药品id：%s", medicineId));
                    return PrescribeOutMedicineDO.builder()
                            .outId(outId)
                            .outNum(o.getOutNum())
                            .medicineId(medicineInfo.getMedicineId())
                            .oneDosageNum(medicineInfo.getOneDosageNum())
                            .useFrequency(medicineInfo.getUseFrequency())
                            .useDay(medicineInfo.getUseDay())
                            .num(medicineInfo.getNum())
                            .useMedicineMethod(medicineInfo.getUseMedicineMethod())
                            .entrust(medicineInfo.getEntrust())
                            .build();
                })
                .collect(Collectors.toList());
        outMedicines.forEach(o -> {
            prescribeOutMedicineDao.insert(o);
        });
    }

}
