package com.rs.module.ihc.controller.admin.pm.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@ApiModel(description = "管理后台 - 药品管理-药品采购申请列表 Request VO")
@Data
public class PurchaseApplyListReqVO {

    @ApiModelProperty("是否删除(0:否,1:是)")
    private Integer isDel;

    @ApiModelProperty("添加时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date[] addTime;

    @ApiModelProperty("添加人")
    private String addUser;

    @ApiModelProperty("更新人")
    private String updateUser;

    @ApiModelProperty("所属省级代码")
    private String proCode;

    @ApiModelProperty("所属省级名称")
    private String proName;

    @ApiModelProperty("所属市级代码")
    private String cityCode;

    @ApiModelProperty("所属市级名称")
    private String cityName;

    @ApiModelProperty("区域代码")
    private String regCode;

    @ApiModelProperty("区域名称")
    private String regName;

    @ApiModelProperty("机构编号")
    private String orgCode;

    @ApiModelProperty("机构名称")
    private String orgName;

    @ApiModelProperty("采购申请编号，编号规则：YPCG+8位日期+4位序列号，如：YPCG202503040002")
    private String applyNum;

    @ApiModelProperty("药品来源")
    private String medicineSource;

    @ApiModelProperty("医生意见")
    private String doctorOpinion;

    @ApiModelProperty("卫生所负责人意见")
    private String chargeOpinion;

    @ApiModelProperty("分管领导意见")
    private String leaderOpinion;

    @ApiModelProperty("审批意见")
    private String approveOpinion;

    @ApiModelProperty("监所id")
    private String prisonId;

}
