package com.rs.module.ihc.dao.idm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.idm.InfectionDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.idm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 传染病管理-传染病管理登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface InfectionDao extends IBaseDao<InfectionDO> {


    default PageResult<InfectionDO> selectPage(InfectionPageReqVO reqVO) {
        Page<InfectionDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<InfectionDO> wrapper = new LambdaQueryWrapperX<InfectionDO>()
            .eqIfPresent(InfectionDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(InfectionDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(InfectionDO::getInfectiousDiseaseType, reqVO.getInfectiousDiseaseType())
            .betweenIfPresent(InfectionDO::getAidsFirstScreeningTime, reqVO.getAidsFirstScreeningTime())
            .eqIfPresent(InfectionDO::getAidsFirstScreeningMethod, reqVO.getAidsFirstScreeningMethod())
            .eqIfPresent(InfectionDO::getAidsFirstScreeningResult, reqVO.getAidsFirstScreeningResult())
            .betweenIfPresent(InfectionDO::getAidsDiagnosisDate, reqVO.getAidsDiagnosisDate())
            .betweenIfPresent(InfectionDO::getAidsLabFeedbackTime, reqVO.getAidsLabFeedbackTime())
            .eqIfPresent(InfectionDO::getAidsLabFeedbackResult, reqVO.getAidsLabFeedbackResult())
            .betweenIfPresent(InfectionDO::getAidsInformInmateTime, reqVO.getAidsInformInmateTime())
            .eqIfPresent(InfectionDO::getAidsIsInformFamily, reqVO.getAidsIsInformFamily())
            .betweenIfPresent(InfectionDO::getAidsInformFamilyTime, reqVO.getAidsInformFamilyTime())
            .eqIfPresent(InfectionDO::getGzjkjgrq, reqVO.getGzjkjgrq())
            .eqIfPresent(InfectionDO::getJkjgrqmc, reqVO.getJkjgrqmc())
            .eqIfPresent(InfectionDO::getSfjszrjkjgfkxx, reqVO.getSfjszrjkjgfkxx())
            .eqIfPresent(InfectionDO::getJszrjkjgfkxxrq, reqVO.getJszrjkjgfkxxrq())
            .eqIfPresent(InfectionDO::getSfxyzclxzrjkjg, reqVO.getSfxyzclxzrjkjg())
            .eqIfPresent(InfectionDO::getZclxzrjkjgrq, reqVO.getZclxzrjkjgrq())
            .eqIfPresent(InfectionDO::getSubmitter, reqVO.getSubmitter())
            .eqIfPresent(InfectionDO::getAttUrl, reqVO.getAttUrl())
            .eqIfPresent(InfectionDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(InfectionDO::getOperatePolice, reqVO.getOperatePolice())
            .betweenIfPresent(InfectionDO::getOperateTime, reqVO.getOperateTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(InfectionDO::getAddTime);
        }
        Page<InfectionDO> infectionPage = selectPage(page, wrapper);
        return new PageResult<>(infectionPage.getRecords(), infectionPage.getTotal());
    }
    default List<InfectionDO> selectList(InfectionListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<InfectionDO>()
            .eqIfPresent(InfectionDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(InfectionDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(InfectionDO::getInfectiousDiseaseType, reqVO.getInfectiousDiseaseType())
            .betweenIfPresent(InfectionDO::getAidsFirstScreeningTime, reqVO.getAidsFirstScreeningTime())
            .eqIfPresent(InfectionDO::getAidsFirstScreeningMethod, reqVO.getAidsFirstScreeningMethod())
            .eqIfPresent(InfectionDO::getAidsFirstScreeningResult, reqVO.getAidsFirstScreeningResult())
            .betweenIfPresent(InfectionDO::getAidsDiagnosisDate, reqVO.getAidsDiagnosisDate())
            .betweenIfPresent(InfectionDO::getAidsLabFeedbackTime, reqVO.getAidsLabFeedbackTime())
            .eqIfPresent(InfectionDO::getAidsLabFeedbackResult, reqVO.getAidsLabFeedbackResult())
            .betweenIfPresent(InfectionDO::getAidsInformInmateTime, reqVO.getAidsInformInmateTime())
            .eqIfPresent(InfectionDO::getAidsIsInformFamily, reqVO.getAidsIsInformFamily())
            .betweenIfPresent(InfectionDO::getAidsInformFamilyTime, reqVO.getAidsInformFamilyTime())
            .eqIfPresent(InfectionDO::getGzjkjgrq, reqVO.getGzjkjgrq())
            .eqIfPresent(InfectionDO::getJkjgrqmc, reqVO.getJkjgrqmc())
            .eqIfPresent(InfectionDO::getSfjszrjkjgfkxx, reqVO.getSfjszrjkjgfkxx())
            .eqIfPresent(InfectionDO::getJszrjkjgfkxxrq, reqVO.getJszrjkjgfkxxrq())
            .eqIfPresent(InfectionDO::getSfxyzclxzrjkjg, reqVO.getSfxyzclxzrjkjg())
            .eqIfPresent(InfectionDO::getZclxzrjkjgrq, reqVO.getZclxzrjkjgrq())
            .eqIfPresent(InfectionDO::getSubmitter, reqVO.getSubmitter())
            .eqIfPresent(InfectionDO::getAttUrl, reqVO.getAttUrl())
            .eqIfPresent(InfectionDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(InfectionDO::getOperatePolice, reqVO.getOperatePolice())
            .betweenIfPresent(InfectionDO::getOperateTime, reqVO.getOperateTime())
        .orderByDesc(InfectionDO::getAddTime));    }


    }
