package com.rs.module.ihc.dao.ipm.appointment;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.controller.admin.ipm.appointment.vo.GetMedicalAppointmentByIdVO;
import com.rs.module.ihc.controller.admin.ipm.appointment.vo.IhcsInternalMedicalAppointmentListReqVO;
import com.rs.module.ihc.controller.admin.ipm.appointment.vo.IhcsInternalMedicalAppointmentPageReqVO;
import com.rs.module.ihc.entity.ipm.appointment.IhcsInternalMedicalAppointmentDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 所内就医-预约登记 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface IhcsInternalMedicalAppointmentDao extends IBaseDao<IhcsInternalMedicalAppointmentDO> {


    default PageResult<IhcsInternalMedicalAppointmentDO> selectPage(IhcsInternalMedicalAppointmentPageReqVO reqVO) {
        Page<IhcsInternalMedicalAppointmentDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<IhcsInternalMedicalAppointmentDO> wrapper = new LambdaQueryWrapperX<IhcsInternalMedicalAppointmentDO>()
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getAppointmentNum, reqVO.getAppointmentNum())
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getSupervisedUserCode, reqVO.getSupervisedUserCode())
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getSickType, reqVO.getSickType())
                .betweenIfPresent(IhcsInternalMedicalAppointmentDO::getDiseaseTime, reqVO.getDiseaseTime())
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getDiseaseReason, reqVO.getDiseaseReason())
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getPrimaryDiagnosis, reqVO.getPrimaryDiagnosis())
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getDiseaseLevel, reqVO.getDiseaseLevel())
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getProcessMethod, reqVO.getProcessMethod())
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getProcessUserid, reqVO.getProcessUserid())
                .likeIfPresent(IhcsInternalMedicalAppointmentDO::getProcessUserName, reqVO.getProcessUserName())
                .betweenIfPresent(IhcsInternalMedicalAppointmentDO::getProcessTime, reqVO.getProcessTime())
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getRemoteDiagnoseStatus, reqVO.getRemoteDiagnoseStatus())
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getPrisonId, reqVO.getPrisonId())
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getHasAllergyHistory, reqVO.getHasAllergyHistory());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(IhcsInternalMedicalAppointmentDO::getAddTime);
        }
        Page<IhcsInternalMedicalAppointmentDO> sInternalMedicalAppointmentPage = selectPage(page, wrapper);
        return new PageResult<>(sInternalMedicalAppointmentPage.getRecords(), sInternalMedicalAppointmentPage.getTotal());
    }

    default List<IhcsInternalMedicalAppointmentDO> selectList(IhcsInternalMedicalAppointmentListReqVO reqVO) {

        LambdaQueryWrapperX<IhcsInternalMedicalAppointmentDO> ihcsInternalMedicalAppointmentDOLambdaQueryWrapperX = new LambdaQueryWrapperX<IhcsInternalMedicalAppointmentDO>()
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getAppointmentNum, reqVO.getAppointmentNum())
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getSupervisedUserCode, reqVO.getSupervisedUserCode())
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getSickType, reqVO.getSickType())
                .betweenIfPresent(IhcsInternalMedicalAppointmentDO::getDiseaseTime, reqVO.getDiseaseTime())
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getDiseaseReason, reqVO.getDiseaseReason())
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getPrimaryDiagnosis, reqVO.getPrimaryDiagnosis())
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getDiseaseLevel, reqVO.getDiseaseLevel())
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getProcessMethod, reqVO.getProcessMethod())
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getProcessUserid, reqVO.getProcessUserid())
                .likeIfPresent(IhcsInternalMedicalAppointmentDO::getProcessUserName, reqVO.getProcessUserName())
                .betweenIfPresent(IhcsInternalMedicalAppointmentDO::getProcessTime, reqVO.getProcessTime())
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getRemoteDiagnoseStatus, reqVO.getRemoteDiagnoseStatus())
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getPrisonId, reqVO.getPrisonId())
                .eqIfPresent(IhcsInternalMedicalAppointmentDO::getHasAllergyHistory, reqVO.getHasAllergyHistory())
                .orderByDesc(IhcsInternalMedicalAppointmentDO::getAddTime);

        if (StringUtils.isNotEmpty(reqVO.getOperationType())) {
            // 根据操作类型设置时间范围过滤
            LocalDateTime now = LocalDateTime.now();
            switch (reqVO.getOperationType()) {
                // 今天
                case "1":
                    ihcsInternalMedicalAppointmentDOLambdaQueryWrapperX.between(
                            IhcsInternalMedicalAppointmentDO::getAddTime,
                            now.toLocalDate().atStartOfDay(),
                            now
                    );
                    break;
                // 昨天
                case "2":
                    LocalDateTime yesterdayStart = now.minusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
                    LocalDateTime yesterdayEnd = now.withHour(0).withMinute(0).withSecond(0).withNano(0);
                    ihcsInternalMedicalAppointmentDOLambdaQueryWrapperX.between(
                            IhcsInternalMedicalAppointmentDO::getAddTime,
                            yesterdayStart,
                            yesterdayEnd
                    );
                    break;
                // 近一周
                case "3":
                    ihcsInternalMedicalAppointmentDOLambdaQueryWrapperX.ge(
                            IhcsInternalMedicalAppointmentDO::getAddTime,
                            now.minusWeeks(1)
                    );
                    break;
                // case "0" 全部，不需要额外处理
                default:
                    break;
            }
        }

        return selectList(ihcsInternalMedicalAppointmentDOLambdaQueryWrapperX);
    }


    /**
     * 获取时间范围内最大预约单号
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param prefix    预约单号前缀
     * @return 最大预约单号
     */
    String getMaxAppointmentNumber(@Param("startTime") LocalDateTime startTime,
                                   @Param("endTime") LocalDateTime endTime,
                                   @Param("prefix") String prefix);

    GetMedicalAppointmentByIdVO getMedicalAppointmentById(String id);

    IhcsInternalMedicalAppointmentDO getMedicalAppointmentByJgrybm(@Param("jgrybm") String jgrybm);

}



