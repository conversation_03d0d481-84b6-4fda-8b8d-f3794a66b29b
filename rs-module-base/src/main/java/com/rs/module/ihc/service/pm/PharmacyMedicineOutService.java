package com.rs.module.ihc.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.ihc.controller.admin.ipm.dto.SaveMedicineOutDTO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineOutListReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineOutPageReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineOutSaveReqVO;
import com.rs.module.ihc.entity.pm.PharmacyMedicineInDO;
import com.rs.module.ihc.entity.pm.PharmacyMedicineOutDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 大药房管理-药品出库 Service 接口
 *
 * <AUTHOR>
 */
public interface PharmacyMedicineOutService extends IBaseService<PharmacyMedicineOutDO>{

    /**
     * 创建大药房管理-药品出库
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPharmacyMedicineOut(@Valid PharmacyMedicineOutSaveReqVO createReqVO);

    /**
     * 更新大药房管理-药品出库
     *
     * @param updateReqVO 更新信息
     */
    void updatePharmacyMedicineOut(@Valid PharmacyMedicineOutSaveReqVO updateReqVO);

    /**
     * 删除大药房管理-药品出库
     *
     * @param id 编号
     */
    void deletePharmacyMedicineOut(String id);

    /**
     * 获得大药房管理-药品出库
     *
     * @param id 编号
     * @return 大药房管理-药品出库
     */
    PharmacyMedicineOutDO getPharmacyMedicineOut(String id);

    /**
    * 获得大药房管理-药品出库分页
    *
    * @param pageReqVO 分页查询
    * @return 大药房管理-药品出库分页
    */
    PageResult<PharmacyMedicineOutDO> getPharmacyMedicineOutPage(PharmacyMedicineOutPageReqVO pageReqVO);

    /**
    * 获得大药房管理-药品出库列表
    *
    * @param listReqVO 查询条件
    * @return 大药房管理-药品出库列表
    */
    List<PharmacyMedicineOutDO> getPharmacyMedicineOutList(PharmacyMedicineOutListReqVO listReqVO);

    /**
     * 批量新增
     * <AUTHOR>
     * @date 2025/7/15 14:29
     * @param [medicineOutList, id]
     * @return void
     */
    void batchSaveMedicineOut(List<SaveMedicineOutDTO> medicineOutList, String id);


    /**
     * 批量新增出库批次
     * @param medicineOutList          出库批次列表
     * @param medicineId               药品id
     * @param dataSourceIhcsMedicineIn 出库批次对应的数据库源数据
     */
    void batchSaveMedicineOutBySourceData(List<SaveMedicineOutDTO> medicineOutList,
                                          String medicineId,
                                          List<PharmacyMedicineInDO> dataSourceIhcsMedicineIn);
}
