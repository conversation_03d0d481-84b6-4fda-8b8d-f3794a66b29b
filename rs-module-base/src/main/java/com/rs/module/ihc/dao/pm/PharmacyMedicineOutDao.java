package com.rs.module.ihc.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.pm.PharmacyMedicineOutDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 大药房管理-药品出库 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PharmacyMedicineOutDao extends IBaseDao<PharmacyMedicineOutDO> {


    default PageResult<PharmacyMedicineOutDO> selectPage(PharmacyMedicineOutPageReqVO reqVO) {
        Page<PharmacyMedicineOutDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PharmacyMedicineOutDO> wrapper = new LambdaQueryWrapperX<PharmacyMedicineOutDO>()
            .eqIfPresent(PharmacyMedicineOutDO::getMedicineId, reqVO.getMedicineId())
            .eqIfPresent(PharmacyMedicineOutDO::getMedicineInId, reqVO.getMedicineInId())
            .eqIfPresent(PharmacyMedicineOutDO::getOutStorageReason, reqVO.getOutStorageReason())
            .eqIfPresent(PharmacyMedicineOutDO::getOutNum, reqVO.getOutNum())
            .eqIfPresent(PharmacyMedicineOutDO::getDispensaryUnit, reqVO.getDispensaryUnit())
            .betweenIfPresent(PharmacyMedicineOutDO::getDispensaryTime, reqVO.getDispensaryTime())
            .eqIfPresent(PharmacyMedicineOutDO::getUserid, reqVO.getUserid())
            .likeIfPresent(PharmacyMedicineOutDO::getUserName, reqVO.getUserName())
            .eqIfPresent(PharmacyMedicineOutDO::getRemark, reqVO.getRemark())
            .eqIfPresent(PharmacyMedicineOutDO::getInventoryNum, reqVO.getInventoryNum())
            .eqIfPresent(PharmacyMedicineOutDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
            .eqIfPresent(PharmacyMedicineOutDO::getOutStatus, reqVO.getOutStatus())
            .eqIfPresent(PharmacyMedicineOutDO::getWorkflowEnd, reqVO.getWorkflowEnd())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PharmacyMedicineOutDO::getAddTime);
        }
        Page<PharmacyMedicineOutDO> pharmacyMedicineOutPage = selectPage(page, wrapper);
        return new PageResult<>(pharmacyMedicineOutPage.getRecords(), pharmacyMedicineOutPage.getTotal());
    }
    default List<PharmacyMedicineOutDO> selectList(PharmacyMedicineOutListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PharmacyMedicineOutDO>()
            .eqIfPresent(PharmacyMedicineOutDO::getMedicineId, reqVO.getMedicineId())
            .eqIfPresent(PharmacyMedicineOutDO::getMedicineInId, reqVO.getMedicineInId())
            .eqIfPresent(PharmacyMedicineOutDO::getOutStorageReason, reqVO.getOutStorageReason())
            .eqIfPresent(PharmacyMedicineOutDO::getOutNum, reqVO.getOutNum())
            .eqIfPresent(PharmacyMedicineOutDO::getDispensaryUnit, reqVO.getDispensaryUnit())
            .betweenIfPresent(PharmacyMedicineOutDO::getDispensaryTime, reqVO.getDispensaryTime())
            .eqIfPresent(PharmacyMedicineOutDO::getUserid, reqVO.getUserid())
            .likeIfPresent(PharmacyMedicineOutDO::getUserName, reqVO.getUserName())
            .eqIfPresent(PharmacyMedicineOutDO::getRemark, reqVO.getRemark())
            .eqIfPresent(PharmacyMedicineOutDO::getInventoryNum, reqVO.getInventoryNum())
            .eqIfPresent(PharmacyMedicineOutDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
            .eqIfPresent(PharmacyMedicineOutDO::getOutStatus, reqVO.getOutStatus())
            .eqIfPresent(PharmacyMedicineOutDO::getWorkflowEnd, reqVO.getWorkflowEnd())
        .orderByDesc(PharmacyMedicineOutDO::getAddTime));    }


    }
