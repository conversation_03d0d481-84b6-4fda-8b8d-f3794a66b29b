package com.rs.module.ihc.handler.pm.medicine.in;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.framework.common.handler.AbstractHandler;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineInImportListVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineInImportVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineInSaveReqVO;
import com.rs.module.ihc.entity.pm.MedicineDO;
import com.rs.module.ihc.enums.RkfsEnum;
import com.rs.module.ihc.service.pm.MedicineInService;
import com.rs.module.ihc.service.pm.MedicineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ValidateHandler
 *
 * <AUTHOR>
 * @Date 2025/3/20 19:31
 * @Version 1.0
 */
@Component
public class SaveHandler extends AbstractHandler<MedicineInImportListVO> {
    @Autowired
    MedicineInService medicineInService;
    @Autowired
    MedicineService medicineService;
    @Override
    protected MedicineInImportListVO doHandle(MedicineInImportListVO input) {
        List<MedicineInImportVO> medicineInImportVOList = input.getMedicineInImportVOList();
        for (MedicineInImportVO medicineInImportVO : medicineInImportVOList) {
            MedicineInSaveReqVO medicineInSaveReqVO = new MedicineInSaveReqVO();
            MedicineDO medicineDO = medicineService.getOne(new LambdaQueryWrapper<MedicineDO>()
                    .eq(MedicineDO::getApprovalNum, medicineInImportVO.getApprovalNum()));
            medicineInSaveReqVO.setMedicineId(medicineDO.getId());
            medicineInSaveReqVO.setInNum(medicineInImportVO.getSl().multiply(BigDecimal.valueOf(medicineDO.getUnitConversionRatio())));
            medicineInSaveReqVO.setBatchCode(input.getShdh());
            medicineInSaveReqVO.setInDate(new Date());
            medicineInSaveReqVO.setExpireDate(medicineInImportVO.getYxqDate());
            medicineInSaveReqVO.setInStorageMethod(RkfsEnum.CGRK.getCode());
            medicineInSaveReqVO.setBatchCode(input.getShdh());
            //保存入库单
            medicineInService.createMedicineIn(medicineInSaveReqVO);
        }
        return input;
    }
}
