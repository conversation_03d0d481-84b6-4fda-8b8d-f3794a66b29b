package com.rs.module.ihc.service.ipm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.PrescribeDO;
import com.rs.module.ihc.entity.ipm.PrescribeMedicineDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 所内就医-处方 Service 接口
 *
 * <AUTHOR>
 */
public interface PrescribeService extends IBaseService<PrescribeDO>{

    /**
     * 创建所内就医-处方
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrescribe(@Valid PrescribeSaveReqVO createReqVO);

    /**
     * 更新所内就医-处方
     *
     * @param updateReqVO 更新信息
     */
    void updatePrescribe(@Valid PrescribeSaveReqVO updateReqVO);

    /**
     * 删除所内就医-处方
     *
     * @param id 编号
     */
    void deletePrescribe(String id);

    /**
     * 获得所内就医-处方
     *
     * @param id 编号
     * @return 所内就医-处方
     */
    PrescribeDO getPrescribe(String id);

    /**
    * 获得所内就医-处方分页
    *
    * @param pageReqVO 分页查询
    * @return 所内就医-处方分页
    */
    PageResult<PrescribeDO> getPrescribePage(PrescribePageReqVO pageReqVO);

    /**
    * 获得所内就医-处方列表
    *
    * @param listReqVO 查询条件
    * @return 所内就医-处方列表
    */
    List<PrescribeDO> getPrescribeList(PrescribeListReqVO listReqVO);


    // ==================== 子表（所内就医-处方-药方信息） ====================

    /**
     * 获得所内就医-处方-药方信息列表
     *
     * @param mlh 处方id，对应ihc_ipm_prescribe.id
     * @return 所内就医-处方-药方信息列表
     */
    List<PrescribeMedicineDO> getPrescribeMedicineListByMlh(String mlh);


    /**
     * 根据处方数据来源去判断处方是否存在，存在则更新，不存在则新增
     * @param type
     * @param businessId
     * @param jgrybm
     * @param prescribeSaveReqVO
     */
    public void saveOrUpdatePrescribeFromType(Integer type, String businessId, String jgrybm, PrescribeSaveReqVO prescribeSaveReqVO);


    /**
     * 处方状态变更
     */
    void prescribeStatusChange(PrescribeStatusChangeReqVO prescribeStatusChangeDTO);


    /**
     * 自动关闭被监管人员出所的处方
     */
    void autoClosePrisonerOutPrescribe();

    /**
     * 新增处方
     *
     * @param saveInternalMedicalPrescribeDTO 数据
     * @param supervisedUserCode              被监管人员编号
     * @param businessId                      业务id
     * @param type                            处方类型 1=所内就医远程问诊处方 2=所内就医现场巡诊处方 3=所内就医所内门诊
     */
    void addPrescribe(PrescribeSaveReqVO saveInternalMedicalPrescribeDTO, String supervisedUserCode, String businessId, Integer type);


    public List<PrescribeRespVO> getPrescribeList(String roomId);

    List<PrescribeRespVO> getPrescribeListByJgrybm(String jgrybm, String doctorAdviceType);


}
