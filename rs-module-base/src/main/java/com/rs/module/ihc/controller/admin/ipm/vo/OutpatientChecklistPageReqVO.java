package com.rs.module.ihc.controller.admin.ipm.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 所内就医-所内门诊-检查单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OutpatientChecklistPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("所内门诊id，对应ihc_ipm_outpatient.id")
    private String outpatientId;

    @ApiModelProperty("主诉")
    private String mainComplaint;

    @ApiModelProperty("病情简述")
    private String illnessResume;

    @ApiModelProperty("检查结论")
    private String checkConclusion;

    @ApiModelProperty("检查单编号")
    private String checklistNum;

    @ApiModelProperty("检查单种类 多选  ,分割")
    private String checklistCategory;

    @ApiModelProperty("检查单状态")
    private String checklistStatus;

    @ApiModelProperty("登记人，对应permission_user.userid")
    private String registerUserid;

    @ApiModelProperty("登记人名称")
    private String registerUserName;

    @ApiModelProperty("登记时间")
    private Date[] registerTime;

    @ApiModelProperty("监所id")
    private String prisonId;

    private String jgrybm;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
