package com.rs.module.ihc.controller.admin.ipm.appointment.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.common.annotation.Format;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.pm.PrisonerService;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class GetMedicalAppointmentByIdVO implements TransPojo {

    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 预约编号
     */
    @ApiModelProperty("预约编号")
    private String appointmentNum;

    /**
     * 被监管人员编号
     */
    @ApiModelProperty("被监管人员编号")
    private String supervisedUserCode;

    /**
     * 被监管人员名称/姓名
     */
    @ApiModelProperty("被监管人员名称/姓名")
    private String supervisedUserName;

    /**
     * 是否重病号 0：否 1：是
     */
    @ApiModelProperty("是否重病号 0：否 1：是")
    private Integer sickType;

    /**
     * 性别 字典值，0: 未知，1: 男，2: 女 5：女变男 6：男变女 9: 未说明的性别
     */
    @ApiModelProperty("性别 字典值，0: 未知，1: 男，2: 女 5：女变男 6：男变女 9: 未说明的性别")
    private String sex;

    /**
     * 年龄
     */
    @ApiModelProperty("年龄")
    private Long age;

    /**
     * 出生日期
     */
    @ApiModelProperty("出生日期")
    private LocalDateTime birthday;

    /**
     * 入所时间
     */
    @ApiModelProperty("入所时间")
    private LocalDateTime entryTime;

    /**
     * 正面照片
     */
    @ApiModelProperty("正面照片")
    private String frontPhoto;

    /**
     * 监室id
     */
    @ApiModelProperty("监室id")
    private String roomId;

    /**
     * 监室名称
     */
    @ApiModelProperty("监室名称")
    private String roomName;

    /**
     * 报病时间
     */
    @ApiModelProperty("报病时间")
    private LocalDateTime diseaseTime;

    /**
     * 报病原因
     */
    @ApiModelProperty("报病原因")
    private String diseaseReason;

    /**
     * 初步诊断
     */
    @ApiModelProperty("初步诊断")
    private String primaryDiagnosis;

    /**
     * 病情等级 字典值
     */
    @ApiModelProperty("病情等级 字典值")
    private String diseaseLevel;

    /**
     * 处理方式/预约审核结果
     */
    @ApiModelProperty("处理方式/预约审核结果")
    private String processMethod;

    /**
     * 处理人 对应permission_user.userid
     */
    @ApiModelProperty("处理人id")
    private Long processUserid;

    /**
     * 处理人名称
     */
    @ApiModelProperty("处理人名称")
    private String processUserName;

    /**
     * 处理时间
     */
    @ApiModelProperty("处理时间")
    private LocalDateTime processTime;

    @Format(service = PrisonerService.class, method = "getPrisonerByJgrybm", value = "supervisedUserCode", toBean = PrisonerVwRespVO.class)
    public PrisonerVwRespVO jgry;



}
