package com.rs.module.ihc.service.ipm;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.db.Db;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.rs.framework.common.enums.FileObjectTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.annotation.DbHalder;
import com.rs.framework.mybatis.config.GlobalConstant;
import com.rs.framework.mybatis.util.BizAssert;
import com.rs.framework.mybatis.util.DatasourceUtil;
import com.rs.module.ihc.constant.IhcsInternalMedicalOutpatientChecklistConstant;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.dao.ipm.OutpatientChecklistCategoryDao;
import com.rs.module.ihc.dao.ipm.OutpatientChecklistDao;
import com.rs.module.ihc.entity.ipm.OutpatientChecklistCategoryDO;
import com.rs.module.ihc.entity.ipm.OutpatientChecklistDO;
import com.rs.module.ihc.entity.ipm.OutpatientDO;
import com.rs.module.oss.entity.FileDetail;
import com.rs.module.oss.service.FileDetailService;
import org.apache.commons.lang3.StringUtils;
import org.dromara.x.file.storage.core.FileInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 所内就医-所内门诊-检查单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OutpatientChecklistServiceImpl extends BaseServiceImpl<OutpatientChecklistDao, OutpatientChecklistDO> implements OutpatientChecklistService {

    @Resource
    private OutpatientChecklistDao outpatientChecklistDao;
    @Resource
    private OutpatientChecklistCategoryDao outpatientChecklistCategoryDao;
    @Resource
    private FileDetailService fileDetailService;

    @Resource
    private OutpatientService outpatientService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DbHalder(sql = "update ihc_ipm_outpatient set illness_resume = '${req.illnessResume}', main_complaint='${req.mainComplaint}' where id = '${req.outpatientId}'")
    public String createOutpatientChecklist(OutpatientChecklistSaveReqVO createReqVO) {
        String outpatientId = createReqVO.getOutpatientId();
        OutpatientDO outpatient = outpatientService.getOutpatient(outpatientId);
        createReqVO.setJgrybm(outpatient.getJgrybm());
        // 插入
        OutpatientChecklistDO outpatientChecklist = BeanUtils.toBean(createReqVO, OutpatientChecklistDO.class);
        outpatientChecklistDao.insert(outpatientChecklist);

        // 插入子表
        createOutpatientChecklistCategoryList(outpatientChecklist.getId(), createReqVO.getOutpatientChecklistCategorys());
        // 返回
        return outpatientChecklist.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DbHalder(sql = "update ihc_ipm_outpatient set illness_resume = '${req.illnessResume}', main_complaint='${req.mainComplaint}' where id = '${req.outpatientId}'")
    public void updateOutpatientChecklist(OutpatientChecklistSaveReqVO updateReqVO) {
        // 校验存在
        validateOutpatientChecklistExists(updateReqVO.getId());
        // 更新
        OutpatientChecklistDO updateObj = BeanUtils.toBean(updateReqVO, OutpatientChecklistDO.class);
        outpatientChecklistDao.updateById(updateObj);

        // 更新子表
        updateOutpatientChecklistCategoryList(updateReqVO.getId(), updateReqVO.getOutpatientChecklistCategorys());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOutpatientChecklist(String id) {
        // 校验存在
        validateOutpatientChecklistExists(id);
        // 删除
        outpatientChecklistDao.deleteById(id);

        // 删除子表
        deleteOutpatientChecklistCategoryByChecklistId(id);
    }

    private void validateOutpatientChecklistExists(String id) {
        if (outpatientChecklistDao.selectById(id) == null) {
            throw new ServerException("所内就医-所内门诊-检查单数据不存在");
        }
    }

    @Override
    public OutpatientChecklistDO getOutpatientChecklist(String id) {
        return outpatientChecklistDao.selectById(id);
    }

    @Override
    public PageResult<OutpatientChecklistDO> getOutpatientChecklistPage(OutpatientChecklistPageReqVO pageReqVO) {
        return outpatientChecklistDao.selectPage(pageReqVO);
    }

    @Override
    public List<OutpatientChecklistDO> getOutpatientChecklistList(OutpatientChecklistListReqVO listReqVO) {
        return outpatientChecklistDao.selectList(listReqVO);
    }


    // ==================== 子表（所内就医-所内门诊-检查单-检查种类） ====================

    @Override
    public List<OutpatientChecklistCategoryDO> getOutpatientChecklistCategoryListByChecklistId(String checklistId) {
        return outpatientChecklistCategoryDao.selectListByChecklistId(checklistId);
    }

    @Override
    public void registerCheckList(InternalMedicalOutpatientChecklistRegisterVO vo) {


        OutpatientChecklistDO checklist = outpatientChecklistDao.selectById(vo.getId());
        BizAssert.isFalse(Objects.equals(IhcsInternalMedicalOutpatientChecklistConstant.CHECKLIST_STATUS_NULLIFY, checklist.getChecklistStatus()), "作废的检查单不能再进行登记");
        CopyOptions copyOptions = CopyOptions.create()
                .setIgnoreNullValue(true)
                .setIgnoreError(true)
                .setIgnoreCase(true);
        BeanUtil.copyProperties(vo, checklist, copyOptions);

        checklist.setChecklistStatus(IhcsInternalMedicalOutpatientChecklistConstant.CHECKLIST_STATUS_REGISTERED);
        outpatientChecklistDao.updateById(checklist);

        // 存储检查结果
        List<OutpatientChecklistCategoryDO> categoryList = outpatientChecklistCategoryDao.selectList(
                Wrappers.lambdaQuery(OutpatientChecklistCategoryDO.class)
                        .eq(OutpatientChecklistCategoryDO::getChecklistId, vo.getId()));

        Map<String, OutpatientChecklistCategoryDO> categoryMap = categoryList.stream()
                .collect(Collectors.toMap(OutpatientChecklistCategoryDO::getChecklistCategory, a -> a, (a, b) -> a));

        for (InternalMedicalOutpatientChecklistCategoryRegisterVO internalMedicalOutpatientChecklistCategoryRegisterVO : vo.getCategoryList()) {
            OutpatientChecklistCategoryDO category = categoryMap.get(internalMedicalOutpatientChecklistCategoryRegisterVO.getChecklistCategory());
            if (Objects.isNull(category)) {
                category = new OutpatientChecklistCategoryDO();
                category.setChecklistId(vo.getId());
            }
            BeanUtil.copyProperties(internalMedicalOutpatientChecklistCategoryRegisterVO, category, copyOptions);
            if (Objects.isNull(category.getId())) {
                outpatientChecklistCategoryDao.insert(category);
            } else {
                outpatientChecklistCategoryDao.updateById(category);
            }
            QueryWrapper<FileDetail> queryWrapper = new QueryWrapper<>();
            //1=常规检查 2=心电图 3=B超 4=DR
//            if ("1".equals(internalMedicalOutpatientChecklistCategoryRegisterVO.getChecklistCategory())) {
//                queryWrapper.eq("object_type", FileObjectTypeEnum.cgjc.getCode());
//            }
//            if ("2".equals(internalMedicalOutpatientChecklistCategoryRegisterVO.getChecklistCategory())) {
//                queryWrapper.eq("object_type", FileObjectTypeEnum.xdt.getCode());
//            }
//            if ("3".equals(internalMedicalOutpatientChecklistCategoryRegisterVO.getChecklistCategory())) {
//                queryWrapper.eq("object_type", FileObjectTypeEnum.bc.getCode());
//            }
//            if ("4".equals(internalMedicalOutpatientChecklistCategoryRegisterVO.getChecklistCategory())) {
//                queryWrapper.eq("object_type", FileObjectTypeEnum.dr.getCode());
//            }
            queryWrapper.eq("object_id", category.getId());
            fileDetailService.remove(queryWrapper);

            List<FileInfo> fileList = internalMedicalOutpatientChecklistCategoryRegisterVO.getFileList();
            if (fileList != null) {
                for (FileInfo fileInfo : fileList) {
                    FileDetail fileDetail = null;
                    try {
                        fileDetail = fileDetailService.toFileDetail(fileInfo);
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                    fileDetail.setObjectId(category.getId());

                    //1=常规检查 2=心电图 3=B超 4=DR
                    if ("1".equals(internalMedicalOutpatientChecklistCategoryRegisterVO.getChecklistCategory())) {
                        fileDetail.setObjectType(FileObjectTypeEnum.cgjc.getCode());
                    }
                    if ("2".equals(internalMedicalOutpatientChecklistCategoryRegisterVO.getChecklistCategory())) {
                        fileDetail.setObjectType(FileObjectTypeEnum.xdt.getCode());
                    }
                    if ("3".equals(internalMedicalOutpatientChecklistCategoryRegisterVO.getChecklistCategory())) {
                        fileDetail.setObjectType(FileObjectTypeEnum.bc.getCode());
                    }
                    if ("4".equals(internalMedicalOutpatientChecklistCategoryRegisterVO.getChecklistCategory())) {
                        fileDetail.setObjectType(FileObjectTypeEnum.dr.getCode());

                    }
                    try {
                        fileDetailService.save(fileDetail);
                    } catch (Exception e) {
                        fileDetailService.updateById(fileDetail);

                    }
                }
            }
        }
    }

    @Override
    public void nullifyCheckListById(String id) {
        OutpatientChecklistDO outpatientChecklistDO = outpatientChecklistDao.selectById(id);
        BizAssert.isTrue(Objects.equals(IhcsInternalMedicalOutpatientChecklistConstant.CHECKLIST_STATUS_UNREGISTERED, outpatientChecklistDO.getChecklistStatus()), "未登记状态才能进行作废");
        outpatientChecklistDO.setChecklistStatus(IhcsInternalMedicalOutpatientChecklistConstant.CHECKLIST_STATUS_NULLIFY);
        outpatientChecklistDao.updateById(outpatientChecklistDO);
    }



    private void createOutpatientChecklistCategoryList(String checklistId, List<OutpatientChecklistCategoryDO> list) {
        list.forEach(o -> o.setChecklistId(checklistId));
        list.forEach(o -> outpatientChecklistCategoryDao.insert(o));
    }

    private void updateOutpatientChecklistCategoryList(String checklistId, List<OutpatientChecklistCategoryDO> list) {
        deleteOutpatientChecklistCategoryByChecklistId(checklistId);
        list.forEach(o -> o.setId(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createOutpatientChecklistCategoryList(checklistId, list);
    }

    private void deleteOutpatientChecklistCategoryByChecklistId(String checklistId) {
        outpatientChecklistCategoryDao.deleteByChecklistId(checklistId);
    }

    @Override
    public String getChecklistSubCategoryName(String checklistSubCategory) {
        List<String> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(checklistSubCategory)) {
            String[] strings = checklistSubCategory.split(",");
            DataSource ds = DatasourceUtil.getDataSource(GlobalConstant.BSP_DATASOURCE_KEY);
            Db use = Db.use(ds);
            for (String s : strings) {
                String sql = "select t.id, t.NAME as checklistCategoryName,t.CODE as checklistCategory from ops_dic_code t where code = ?  and dic_name = 'ZD_OUTPATIENT_CHECKLIST_CATEGORY'";
                List<OutpatientChecklistCategoryDicRespVO> query = null;
                try {
                    query = use.query(sql, OutpatientChecklistCategoryDicRespVO.class,s);
                    if (query != null && query.size() > 0) {
                        list.add(query.get(0).getChecklistCategoryName());
                    }
                } catch (Exception e) {
                    log.error("获得所内就医-所内门诊-检查单-检查种类列表-字典", e);
                }
            }
        }
        return String.join(",", list);
    }

}
