package com.rs.module.ihc.service.ipm;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.VisitMedicineDO;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.ipm.VisitMedicineDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 所内就医-现场巡诊-现场司药信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class VisitMedicineServiceImpl extends BaseServiceImpl<VisitMedicineDao, VisitMedicineDO> implements VisitMedicineService {

    @Resource
    private VisitMedicineDao visitMedicineDao;

    @Override
    public String createVisitMedicine(VisitMedicineSaveReqVO createReqVO) {
        // 插入
        VisitMedicineDO visitMedicine = BeanUtils.toBean(createReqVO, VisitMedicineDO.class);
        visitMedicineDao.insert(visitMedicine);
        // 返回
        return visitMedicine.getId();
    }

    @Override
    public void updateVisitMedicine(VisitMedicineSaveReqVO updateReqVO) {
        // 校验存在
        validateVisitMedicineExists(updateReqVO.getId());
        // 更新
        VisitMedicineDO updateObj = BeanUtils.toBean(updateReqVO, VisitMedicineDO.class);
        visitMedicineDao.updateById(updateObj);
    }

    @Override
    public void deleteVisitMedicine(String id) {
        // 校验存在
        validateVisitMedicineExists(id);
        // 删除
        visitMedicineDao.deleteById(id);
    }

    private void validateVisitMedicineExists(String id) {
        if (visitMedicineDao.selectById(id) == null) {
            throw new ServerException("所内就医-现场巡诊-现场司药信息数据不存在");
        }
    }

    @Override
    public VisitMedicineDO getVisitMedicine(String id) {
        return visitMedicineDao.selectById(id);
    }




}
