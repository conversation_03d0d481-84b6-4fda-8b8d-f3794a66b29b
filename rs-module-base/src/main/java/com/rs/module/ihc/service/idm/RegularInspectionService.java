package com.rs.module.ihc.service.idm;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.idm.vo.*;
import com.rs.module.ihc.entity.idm.RegularInspectionDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 传染病管理-定期检查登记 Service 接口
 *
 * <AUTHOR>
 */
public interface RegularInspectionService extends IBaseService<RegularInspectionDO>{

    /**
     * 创建传染病管理-定期检查登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createRegularInspection(@Valid RegularInspectionSaveReqVO createReqVO);

    /**
     * 更新传染病管理-定期检查登记
     *
     * @param updateReqVO 更新信息
     */
    void updateRegularInspection(@Valid RegularInspectionSaveReqVO updateReqVO);

    /**
     * 删除传染病管理-定期检查登记
     *
     * @param id 编号
     */
    void deleteRegularInspection(String id);

    /**
     * 获得传染病管理-定期检查登记
     *
     * @param id 编号
     * @return 传染病管理-定期检查登记
     */
    RegularInspectionDO getRegularInspection(String id);

    /**
    * 获得传染病管理-定期检查登记分页
    *
    * @param pageReqVO 分页查询
    * @return 传染病管理-定期检查登记分页
    */
    PageResult<RegularInspectionDO> getRegularInspectionPage(RegularInspectionPageReqVO pageReqVO);

    /**
    * 获得传染病管理-定期检查登记列表
    *
    * @param listReqVO 查询条件
    * @return 传染病管理-定期检查登记列表
    */
    List<RegularInspectionDO> getRegularInspectionList(RegularInspectionListReqVO listReqVO);


}
