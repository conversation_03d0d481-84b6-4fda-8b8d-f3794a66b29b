package com.rs.module.ihc.service.pm;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.MedicineDeliveryLossDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.pm.MedicineDeliveryLossDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 药房管理-顾送药品报损 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MedicineDeliveryLossServiceImpl extends BaseServiceImpl<MedicineDeliveryLossDao, MedicineDeliveryLossDO> implements MedicineDeliveryLossService {

    @Resource
    private MedicineDeliveryLossDao medicineDeliveryLossDao;

    @Override
    public String createMedicineDeliveryLoss(MedicineDeliveryLossSaveReqVO createReqVO) {
        // 插入
        MedicineDeliveryLossDO medicineDeliveryLoss = BeanUtils.toBean(createReqVO, MedicineDeliveryLossDO.class);
        medicineDeliveryLossDao.insert(medicineDeliveryLoss);
        // 返回
        return medicineDeliveryLoss.getId();
    }

    @Override
    public void updateMedicineDeliveryLoss(MedicineDeliveryLossSaveReqVO updateReqVO) {
        // 校验存在
        validateMedicineDeliveryLossExists(updateReqVO.getId());
        // 更新
        MedicineDeliveryLossDO updateObj = BeanUtils.toBean(updateReqVO, MedicineDeliveryLossDO.class);
        medicineDeliveryLossDao.updateById(updateObj);
    }

    @Override
    public void deleteMedicineDeliveryLoss(String id) {
        // 校验存在
        validateMedicineDeliveryLossExists(id);
        // 删除
        medicineDeliveryLossDao.deleteById(id);
    }

    private void validateMedicineDeliveryLossExists(String id) {
        if (medicineDeliveryLossDao.selectById(id) == null) {
            throw new ServerException("药房管理-顾送药品报损数据不存在");
        }
    }

    @Override
    public MedicineDeliveryLossDO getMedicineDeliveryLoss(String id) {
        return medicineDeliveryLossDao.selectById(id);
    }

    @Override
    public PageResult<MedicineDeliveryLossDO> getMedicineDeliveryLossPage(MedicineDeliveryLossPageReqVO pageReqVO) {
        return medicineDeliveryLossDao.selectPage(pageReqVO);
    }

    @Override
    public List<MedicineDeliveryLossDO> getMedicineDeliveryLossList(MedicineDeliveryLossListReqVO listReqVO) {
        return medicineDeliveryLossDao.selectList(listReqVO);
    }


}
