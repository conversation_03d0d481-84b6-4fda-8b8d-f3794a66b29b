package com.rs.module.ihc.dao.ipm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.controller.admin.ipm.vo.InternalMedicalOutpatientTodayStatVO;
import com.rs.module.ihc.controller.admin.ipm.vo.OutpatientListReqVO;
import com.rs.module.ihc.controller.admin.ipm.vo.OutpatientPageReqVO;
import com.rs.module.ihc.entity.ipm.OutpatientDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 所内就医-所内门诊 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface OutpatientDao extends IBaseDao<OutpatientDO> {


    default PageResult<OutpatientDO> selectPage(OutpatientPageReqVO reqVO) {
        Page<OutpatientDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<OutpatientDO> outpatientPage = selectPage(page, new LambdaQueryWrapperX<OutpatientDO>()
                .eqIfPresent(OutpatientDO::getSource, reqVO.getSource())
                .eqIfPresent(OutpatientDO::getAppointmentId, reqVO.getAppointmentId())
                .eqIfPresent(OutpatientDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(OutpatientDO::getRybh, reqVO.getRybh())
                .eqIfPresent(OutpatientDO::getRyxm, reqVO.getRyxm())
                .eqIfPresent(OutpatientDO::getSickType, reqVO.getSickType())
                .betweenIfPresent(OutpatientDO::getDiseaseTime, reqVO.getDiseaseTime())
                .eqIfPresent(OutpatientDO::getDiseaseReason, reqVO.getDiseaseReason())
                .eqIfPresent(OutpatientDO::getMainComplaint, reqVO.getMainComplaint())
                .eqIfPresent(OutpatientDO::getIllnessResume, reqVO.getIllnessResume())
                .eqIfPresent(OutpatientDO::getStatus, reqVO.getStatus())
                .eqIfPresent(OutpatientDO::getPrisonId, reqVO.getPrisonId())
                .orderByDesc(OutpatientDO::getId));
        return new PageResult<>(outpatientPage.getRecords(), outpatientPage.getTotal());
    }

    default List<OutpatientDO> selectList(OutpatientListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<OutpatientDO>()
                .eqIfPresent(OutpatientDO::getSource, reqVO.getSource())
                .eqIfPresent(OutpatientDO::getAppointmentId, reqVO.getAppointmentId())
                .eqIfPresent(OutpatientDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(OutpatientDO::getRybh, reqVO.getRybh())
                .eqIfPresent(OutpatientDO::getRyxm, reqVO.getRyxm())
                .eqIfPresent(OutpatientDO::getSickType, reqVO.getSickType())
                .betweenIfPresent(OutpatientDO::getDiseaseTime, reqVO.getDiseaseTime())
                .eqIfPresent(OutpatientDO::getDiseaseReason, reqVO.getDiseaseReason())
                .eqIfPresent(OutpatientDO::getMainComplaint, reqVO.getMainComplaint())
                .eqIfPresent(OutpatientDO::getIllnessResume, reqVO.getIllnessResume())
                .eqIfPresent(OutpatientDO::getStatus, reqVO.getStatus())
                .eqIfPresent(OutpatientDO::getPrisonId, reqVO.getPrisonId())
                .orderByDesc(OutpatientDO::getId));
    }

    InternalMedicalOutpatientTodayStatVO getTodayStat(@Param("start") Date start, @Param("end") Date end, @Param("prisonId") String prisonId);

}
