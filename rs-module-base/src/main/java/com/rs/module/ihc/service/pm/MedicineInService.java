package com.rs.module.ihc.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.ihc.controller.admin.ipm.bo.UpdateMedicineInInventoryNumBO;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.MedicineDO;
import com.rs.module.ihc.entity.pm.MedicineInDO;
import org.apache.ibatis.annotations.Param;

import javax.validation.Valid;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 药房管理-药品入库批次 Service 接口
 *
 * <AUTHOR>
 */
public interface MedicineInService extends IBaseService<MedicineInDO>{

    /**
     * 创建药房管理-药品入库批次
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMedicineIn(@Valid MedicineInSaveReqVO createReqVO);

    /**
     * 更新药房管理-药品入库批次
     *
     * @param updateReqVO 更新信息
     */
    void updateMedicineIn(@Valid MedicineInSaveReqVO updateReqVO);

    /**
     * 删除药房管理-药品入库批次
     *
     * @param id 编号
     */
    void deleteMedicineIn(String id);

    /**
     * 获得药房管理-药品入库批次
     *
     * @param id 编号
     * @return 药房管理-药品入库批次
     */
    MedicineInDO getMedicineIn(String id);

    /**
     * 获得药房管理-药品入库批次
     *
     * @param id 编号
     * @return 药房管理-药品入库批次
     */
    MedicineInRespVO getMedicineInDetail(String id);

    /**
    * 获得药房管理-药品入库批次分页
    *
    * @param pageReqVO 分页查询
    * @return 药房管理-药品入库批次分页
    */
    PageResult<MedicineInDO> getMedicineInPage(MedicineInPageReqVO pageReqVO);

    /**
    * 获得药房管理-药品入库批次列表
    *
    * @param listReqVO 查询条件
    * @return 药房管理-药品入库批次列表
    */
    List<MedicineInDO> getMedicineInList(MedicineInListReqVO listReqVO);

    /**
     * 获得药房管理-药品入库批次列表
     *
     * @param listReqVO 查询条件
     * @return 药房管理-药品入库批次列表
     */
    List<MedicineInDO> ypbsList(MedicineInYpbsReqVO listReqVO);


    /**
     * 获取存在批次过期的药品id
     * <p>
     * 当过期时间到了并且库存已经为0的批次就不算存在过期药品的批次
     *
     * @param expireDate 过期日期
     * @param medicineIdList 药品id集合 可以为空
     * @return 批次过期的药品id集合
     */
    List<String> getExistExpireBatchMedicineId(@Param("expireDate") Date expireDate,
                                               @Param("medicineIdList") List<String> medicineIdList);

    /**
     * 根据多个药品集合获取每个药品第一个没有过期的药品批次
     *
     * @param medicineIdList 药品id集合
     * @param expireDate 过期日期
     * @return 药品批次
     */
    List<MedicineInDO> selectFirstNoExpireMedicineBatch(@Param("medicineIdList") Collection<String> medicineIdList, @Param("expireDate") Date expireDate);

    /**
     * 批量比较替换入库批次的库存数量
     * <p>
     * 将批次的源库存更新为目标库存，只有当源库存没有被其他操作修改时才能更新成功，否则更新失败
     *
     * @param updateMedicineInInventoryNumBOList 入库批次库存数量比较替换集合
     */
    void batchCompareAndSetInventoryNum(Collection<UpdateMedicineInInventoryNumBO> updateMedicineInInventoryNumBOList);


    /**
     * 查询过期批次药品并且更新药品的标识
     * <p>
     * 当传入的medicineIdList不为空时，将先去更新药品的是否存在过期批次为否，然后再根据查询重新设置是否存在过期批次
     *
     * @param medicineIdList 药品id集合 指定查询某些药品批次，为空时查询全部的存在过期批次的药品进行标识
     */
    void selectExpireBatchAndUpdateMedicineFlag(List<String> medicineIdList);

    /**
     * 批量入库
     * @param saveMedicineInReqVOList
     * @param medicineId
     * @param b
     */
    List<String> batchSaveMedicineIn(List<SaveMedicineInReqVO> saveMedicineInReqVOList, String medicineId, boolean b);

    /**
     * 保存入库信息
     * @param saveMedicineInReqVOList
     * @param ihcsMedicine
     * @param ignoreApprove
     * @return
     */
    List<MedicineInDO> batchSaveMedicineIn0(List<SaveMedicineInReqVO> saveMedicineInReqVOList, MedicineDO ihcsMedicine, boolean ignoreApprove);
}
