package com.rs.module.ihc.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 内部就医预约审核状态枚举
 */
@Getter
@AllArgsConstructor
public enum InternalMedicalAppointmentProcessMethodEnum {

    /**
     * 未审核
     */
    NO_PROCESS("0"),

    /**
     * 远程问诊
     */
    REMOTE_DIAGNOSE("1"),

    /**
     * 现场巡诊
     */
    ON_SITE_DIAGNOSE("2"),

    /**
     * 所内门诊就医
     */
    INSIDE_THE_PRISON_DIAGNOSE("3"),

    /**
     * 无需处理
     */
    NO_REQUIRE_PROCESS("4");


    private final String code;


}
