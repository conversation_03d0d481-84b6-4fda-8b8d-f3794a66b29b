package com.rs.module.ihc.service.ephe;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.ihc.controller.admin.ephe.vo.RoomDisinfectRespVO;
import com.rs.module.ihc.controller.admin.ephe.vo.RoomDisinfectSaveReqVO;
import com.rs.module.ihc.entity.ephe.RoomDisinfectDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 卫生防疫与健康教育--监室消毒 Service 接口
 *
 * <AUTHOR>
 */
public interface RoomDisinfectService extends IBaseService<RoomDisinfectDO>{

    /**
     * 创建卫生防疫与健康教育--监室消毒
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createRoomDisinfect(@Valid RoomDisinfectSaveReqVO createReqVO);

    /**
     * 更新卫生防疫与健康教育--监室消毒
     *
     * @param updateReqVO 更新信息
     */
    void updateRoomDisinfect(@Valid RoomDisinfectSaveReqVO updateReqVO);

    /**
     * 删除卫生防疫与健康教育--监室消毒
     *
     * @param id 编号
     */
    void deleteRoomDisinfect(String id);

    /**
     * 获得卫生防疫与健康教育--监室消毒
     *
     * @param id 编号
     * @return 卫生防疫与健康教育--监室消毒
     */
    RoomDisinfectDO getRoomDisinfect(String id);


    /**
     * 获取监室消毒列表
     * @param timeType
     * @param roomId
     * @param orgCode
     * @return
     */
    List<RoomDisinfectRespVO> listRoomDisinfect(String timeType, String roomId, String orgCode);
}
