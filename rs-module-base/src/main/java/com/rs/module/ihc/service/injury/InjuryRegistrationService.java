package com.rs.module.ihc.service.injury;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.ihc.controller.admin.injury.vo.InjuryRegistrationListReqVO;
import com.rs.module.ihc.controller.admin.injury.vo.InjuryRegistrationPageReqVO;
import com.rs.module.ihc.controller.admin.injury.vo.InjuryRegistrationSaveReqVO;
import com.rs.module.ihc.entity.injury.InjuryRegistrationDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 医疗子系统-伤亡登记 Service 接口
 *
 * <AUTHOR>
 */
public interface InjuryRegistrationService extends IBaseService<InjuryRegistrationDO>{

    /**
     * 创建医疗子系统-伤亡登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createInjuryRegistration(@Valid InjuryRegistrationSaveReqVO createReqVO);

    ///**
    // * 更新医疗子系统-伤亡登记
    // *
    // * @param updateReqVO 更新信息
    // */
    //void updateInjuryRegistration(@Valid InjuryRegistrationSaveReqVO updateReqVO);
    //
    ///**
    // * 删除医疗子系统-伤亡登记
    // *
    // * @param id 编号
    // */
    //void deleteInjuryRegistration(String id);

    /**
     * 获得医疗子系统-伤亡登记
     *
     * @param id 编号
     * @return 医疗子系统-伤亡登记
     */
    InjuryRegistrationDO getInjuryRegistration(String id);

    /**
    * 获得医疗子系统-伤亡登记分页
    *
    * @param pageReqVO 分页查询
    * @return 医疗子系统-伤亡登记分页
    */
    PageResult<InjuryRegistrationDO> getInjuryRegistrationPage(InjuryRegistrationPageReqVO pageReqVO);

    /**
    * 获得医疗子系统-伤亡登记列表
    *
    * @param listReqVO 查询条件
    * @return 医疗子系统-伤亡登记列表
    */
    List<InjuryRegistrationDO> getInjuryRegistrationList(InjuryRegistrationListReqVO listReqVO);


}
