package com.rs.module.ihc.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;

import java.util.List;
import java.util.Map;

/**
 * @ClassName ImportListener
 * 
 * <AUTHOR>
 * @Date 2025/3/20 20:49
 * @Version 1.0
 */
public class ImportListener <T> implements ReadListener<T> {
    private List<T> cachedDataList;

    public ImportListener(List<T> cachedDataList) {
        this.cachedDataList = cachedDataList;
    }

    @Override
    public void invoke(T data, AnalysisContext analysisContext) {
        this.cachedDataList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 在所有数据解析完成后的操作
    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        headMap.forEach((key, value) -> {
            System.out.println(key + ":" + value.getStringValue());
        });
        ReadListener.super.invokeHead(headMap, context);
    }
}
