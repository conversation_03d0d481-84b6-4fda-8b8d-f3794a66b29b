package com.rs.module.ihc.service.ipm;

import com.rs.framework.mybatis.util.BizAssert;
import com.rs.module.base.controller.admin.pm.vo.GetPoliceWarderInfoVO;
import com.rs.module.base.dao.pm.PrisonerInDao;
import com.rs.module.ihc.constant.InternalMedicalPrescribeDoseStatusConstant;
import com.rs.module.ihc.constant.InternalMedicalPrescribeExecuteSourceConstant;
import com.rs.module.ihc.constant.InternalMedicalPrescribeSignStatusConstant;
import com.rs.module.ihc.constant.PoliceWarderUserTypeConstant;
import com.rs.module.ihc.entity.ipm.PrescribeDO;
import com.rs.module.ihc.entity.ipm.PrescribeExecuteDO;
import com.rs.module.ihc.enums.InternalMedicalPrescribeStatusEnum;
import com.rs.module.ihc.service.ipm.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class IhcsMedicalDeliveryRemindServiceImpl implements IhcsMedicalDeliveryRemindService {

    @Resource
    private PrescribeService ihcsInternalMedicalPrescribeService;

    @Resource
    private PrescribeExecuteService ihcsInternalMedicalPrescribeExecuteService;

    @Resource
    private
    PrisonerInDao basePrisonerInMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executePrescribe(ExecutePrescribeDTO executePrescribeDTO) {
        String id = executePrescribeDTO.getId();
        PrescribeDO ihcsInternalMedicalPrescribe = ihcsInternalMedicalPrescribeService.getById(id);
        BizAssert.notNull(ihcsInternalMedicalPrescribe, "该处方/医嘱不存在");
        this.checkPrescribeIsExecute(ihcsInternalMedicalPrescribe);
        this.singleExecutePrescribe(ihcsInternalMedicalPrescribe);
        // 加入执行记录
        this.batchSavePrescribeExecuteRecord(Collections.singletonList(ihcsInternalMedicalPrescribe));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executePrescribeSign(ExecutePrescribeSignDTO executePrescribeSignDTO) {
        String id = executePrescribeSignDTO.getId();
        PrescribeExecuteDO ihcsInternalMedicalPrescribeExecute =
                ihcsInternalMedicalPrescribeExecuteService.getById(id);
        BizAssert.notNull(ihcsInternalMedicalPrescribeExecute, "处方/医嘱执行记录不存在");
        String prescribeId = ihcsInternalMedicalPrescribeExecute.getPrescribeId();
        PrescribeDO ihcsInternalMedicalPrescribe = ihcsInternalMedicalPrescribeService.getById(prescribeId);
        BizAssert.notNull(ihcsInternalMedicalPrescribe, "处方/医嘱不存在");
        Integer doseNum = ihcsInternalMedicalPrescribe.getDoseNum();
        String signStatus = ihcsInternalMedicalPrescribeExecute.getSignStatus();
        LocalDateTime nowDateTime = LocalDateTime.now();
        ihcsInternalMedicalPrescribeExecuteService.lambdaUpdate()
                .set(PrescribeExecuteDO::getSignStatus, InternalMedicalPrescribeSignStatusConstant.SIGN)
                .set(PrescribeExecuteDO::getSignPicUrl, executePrescribeSignDTO.getSignPicUrl())
                .set(PrescribeExecuteDO::getDoseVideoUrl, executePrescribeSignDTO.getDoseVideoUrl())
                .set(PrescribeExecuteDO::getSignTime, nowDateTime)
                .set(PrescribeExecuteDO::getDoseTime, nowDateTime)
                .set(PrescribeExecuteDO::getDoseStatus, InternalMedicalPrescribeDoseStatusConstant.DOSE)
                .eq(PrescribeExecuteDO::getId, id)
                .update();
        // 采用 doseNum + 1 直接更新处方表就需要考虑接口多次调用和多线程问题，直接统计一下再更新
        if (Objects.equals(signStatus, InternalMedicalPrescribeSignStatusConstant.NO_SIGN)) {
            Integer count = ihcsInternalMedicalPrescribeExecuteService.lambdaQuery()
                    .eq(PrescribeExecuteDO::getDoseStatus, InternalMedicalPrescribeDoseStatusConstant.DOSE)
                    .eq(PrescribeExecuteDO::getPrescribeId, prescribeId)
                    .count();
            boolean update = ihcsInternalMedicalPrescribeService.lambdaUpdate()
                    .set(PrescribeDO::getDoseNum, count)
                    .eq(PrescribeDO::getId, prescribeId)
                    .eq(PrescribeDO::getDoseNum, doseNum)
                    .update();
            BizAssert.isTrue(update, "签名失败，请重试");
        }
    }

    @Override
    public void executePrescribeVideo(ExecutePrescribeVideoDTO executePrescribeVideoDTO) {
        ihcsInternalMedicalPrescribeExecuteService.lambdaUpdate()
                .set(PrescribeExecuteDO::getDoseVideoUrl, executePrescribeVideoDTO.getDoseVideoUrl())
                .eq(PrescribeExecuteDO::getId, executePrescribeVideoDTO.getId())
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchExecutePrescribe(BatchExecutePrescribeDTO batchExecutePrescribeDTO) {
        List<PrescribeDO> ihcsInternalMedicalPrescribes =
                ihcsInternalMedicalPrescribeService.getBaseMapper().selectBatchIds(batchExecutePrescribeDTO.getIds());
        BizAssert.notEmpty(ihcsInternalMedicalPrescribes, "执行的处方/医嘱为空");
        ihcsInternalMedicalPrescribes.forEach(this::checkPrescribeIsExecute);
        ihcsInternalMedicalPrescribes.forEach(this::singleExecutePrescribe);
        // 加入执行记录
        this.batchSavePrescribeExecuteRecord(ihcsInternalMedicalPrescribes);
    }

    /**
     * 批量添加处方/医嘱执行记录
     *
     * @param ihcsInternalMedicalPrescribes 处方/医嘱
     */
    private void batchSavePrescribeExecuteRecord(List<PrescribeDO> ihcsInternalMedicalPrescribes) {
        // 获取用户和监室以及监室对应的管教民警信息
        Map<String, List<GetPoliceWarderInfoVO>> getPoliceWarderInfoMap = this.getPoliceWarderInfoMap(ihcsInternalMedicalPrescribes);
        List<PrescribeExecuteDO> prescribeExecutes = new ArrayList<>();
        for (PrescribeDO ihcsInternalMedicalPrescribe : ihcsInternalMedicalPrescribes) {
            String supervisedUserCode = ihcsInternalMedicalPrescribe.getJgrybm();
            PrescribeExecuteDO internalMedicalPrescribeExecute =
                    PrescribeExecuteDO.builder()
                            .prescribeId(ihcsInternalMedicalPrescribe.getId())
                            .doctorAdvice(ihcsInternalMedicalPrescribe.getDoctorAdvice())
                            .prescribeDescribe(ihcsInternalMedicalPrescribe.getPrescribeDescribe())
                            .doseStatus(InternalMedicalPrescribeDoseStatusConstant.NO_DOSE)
                            .signStatus(InternalMedicalPrescribeSignStatusConstant.NO_SIGN)
                            .executeSource(InternalMedicalPrescribeExecuteSourceConstant.PRISON)
                            .build();
            this.setWarderInfo(supervisedUserCode, getPoliceWarderInfoMap, internalMedicalPrescribeExecute);
            prescribeExecutes.add(internalMedicalPrescribeExecute);
        }
        ihcsInternalMedicalPrescribeExecuteService.saveBatch(prescribeExecutes);
    }

    /**
     * 获取人员对应的管教民警信息
     *
     * @param ihcsInternalMedicalPrescribes 处方/医嘱
     * @return 管教民警信息
     */
    private Map<String, List<GetPoliceWarderInfoVO>> getPoliceWarderInfoMap(
            List<PrescribeDO> ihcsInternalMedicalPrescribes) {
        List<String> supervisedUserCodeList = ihcsInternalMedicalPrescribes.stream()
                .map(PrescribeDO::getJgrybm)
                .collect(Collectors.toList());
        return basePrisonerInMapper.getPoliceWarderInfo(supervisedUserCodeList)
                .stream()
                .collect(Collectors.groupingBy(GetPoliceWarderInfoVO::getSuperviseUserCode));
    }


    /**
     * 单次执行处方/医嘱
     *
     * @param ihcsInternalMedicalPrescribe 处方/医嘱
     */
    private void singleExecutePrescribe(PrescribeDO ihcsInternalMedicalPrescribe) {
        String id = ihcsInternalMedicalPrescribe.getId();
        Integer dispenseNum = ihcsInternalMedicalPrescribe.getDispenseNum();
        boolean update = ihcsInternalMedicalPrescribeService.lambdaUpdate()
                .set(PrescribeDO::getDispenseNum, dispenseNum + 1)
                .set(PrescribeDO::getLastDispenseTime, LocalDateTime.now())
                .set(PrescribeDO::getPrescribeStatus,
                        InternalMedicalPrescribeStatusEnum.USE.getCode())
                .eq(PrescribeDO::getId, id)
                .eq(PrescribeDO::getDispenseNum, dispenseNum)
                .update();
        BizAssert.isTrue(update, String.format("处方id[%s]存在与他人同时执行或者单次重复点击执行，请重试", id));
    }


    /**
     * 设置送药记录的监室和管教民警信息
     */
    private void setWarderInfo(String supervisedUserCode,
                               Map<String, List<GetPoliceWarderInfoVO>> policeWarderInfoMap,
                               PrescribeExecuteDO ihcsInternalMedicalPrescribeExecute) {
        List<GetPoliceWarderInfoVO> getPoliceWarderInfos =
                policeWarderInfoMap.getOrDefault(supervisedUserCode, Collections.emptyList());
        List<String> assistPoliceIdList = new ArrayList<>();
        List<String> assistPoliceNameList = new ArrayList<>();
        String roomId = null;
        String roomName = null;
        for (GetPoliceWarderInfoVO getPoliceWarderInfo : getPoliceWarderInfos) {
            roomId = getPoliceWarderInfo.getRoomId();
            roomName = getPoliceWarderInfo.getRoomName();
            String userType = getPoliceWarderInfo.getUserType();
            if (PoliceWarderUserTypeConstant.MANAGER.equalsIgnoreCase(userType)) {
                ihcsInternalMedicalPrescribeExecute.setManagePoliceSfzh(getPoliceWarderInfo.getPoliceId());
                ihcsInternalMedicalPrescribeExecute.setManagePoliceName(getPoliceWarderInfo.getPoliceName());
            }
            if (PoliceWarderUserTypeConstant.ASSIST.equalsIgnoreCase(userType)) {
                assistPoliceIdList.add(getPoliceWarderInfo.getPoliceId());
                assistPoliceNameList.add(getPoliceWarderInfo.getPoliceName());
            }
        }
        ihcsInternalMedicalPrescribeExecute.setRoomId(roomId);
        ihcsInternalMedicalPrescribeExecute.setRoomName(roomName);
        ihcsInternalMedicalPrescribeExecute.setAssistPoliceSfzhs(String.join(",", assistPoliceIdList));
        ihcsInternalMedicalPrescribeExecute.setAssistPoliceNames(String.join(",", assistPoliceNameList));
    }


    /**
     * 检查处方是否能执行，不能执行则抛出异常
     *
     * @param ihcsInternalMedicalPrescribe 处方
     */
    private void checkPrescribeIsExecute(PrescribeDO ihcsInternalMedicalPrescribe) {
        String prescribeNum = ihcsInternalMedicalPrescribe.getPrescribeNum();
        String prescribeStatus = ihcsInternalMedicalPrescribe.getPrescribeStatus();
        InternalMedicalPrescribeStatusEnum.checkIsCanExecute(prescribeStatus, prescribeNum);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void prescribeClinicalRegister(PrescribeClinicalRegisterDTO prescribeClinicalRegisterDTO) {
        String prescribeId = prescribeClinicalRegisterDTO.getPrescribeId();
        PrescribeDO ihcsInternalMedicalPrescribe = ihcsInternalMedicalPrescribeService.getById(prescribeId);
        BizAssert.notNull(ihcsInternalMedicalPrescribe, "处方不存在");
        Integer dispenseNum = ihcsInternalMedicalPrescribe.getDispenseNum();
        Integer doseNum = ihcsInternalMedicalPrescribe.getDoseNum();
        boolean update = ihcsInternalMedicalPrescribeService.lambdaUpdate()
                .set(PrescribeDO::getDispenseNum, dispenseNum + 1)
                .set(PrescribeDO::getDoseNum, doseNum + 1)
                .set(PrescribeDO::getLastDispenseTime, new Date())
                .eq(PrescribeDO::getId, prescribeId)
                .eq(PrescribeDO::getDispenseNum, dispenseNum)
                .eq(PrescribeDO::getDoseNum, doseNum)
                .update();
        BizAssert.isTrue(update, "该处方与他人同时操作冲突，请重试");

        // 获取对应的管教信息
        Map<String, List<GetPoliceWarderInfoVO>> policeWarderInfoMap =
                this.getPoliceWarderInfoMap(Collections.singletonList(ihcsInternalMedicalPrescribe));
        PrescribeExecuteDO internalMedicalPrescribeExecute = PrescribeExecuteDO.builder()
                .prescribeId(prescribeId)
                .managePoliceSfzh(prescribeClinicalRegisterDTO.getMedicalPoliceId())
                .medicalPoliceName(prescribeClinicalRegisterDTO.getMedicalPoliceName())
                .remark(prescribeClinicalRegisterDTO.getRemark())
                .doctorAdvice(ihcsInternalMedicalPrescribe.getDoctorAdvice())
                .prescribeDescribe(ihcsInternalMedicalPrescribe.getPrescribeDescribe())
                .doseTime(new Date())
                .executeSource(InternalMedicalPrescribeExecuteSourceConstant.OUTPATIENT_SITE)
                .doseStatus(InternalMedicalPrescribeDoseStatusConstant.DOSE)
                .build();
        this.setWarderInfo(ihcsInternalMedicalPrescribe.getJgrybm(),
                policeWarderInfoMap, internalMedicalPrescribeExecute);
        ihcsInternalMedicalPrescribeExecuteService.save(internalMedicalPrescribeExecute);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refuseExecutePrescribe(RefuseExecutePrescribeDTO refuseExecutePrescribeDTO) {
        String prescribeId = refuseExecutePrescribeDTO.getId();
        PrescribeDO ihcsInternalMedicalPrescribe =
                ihcsInternalMedicalPrescribeService.getById(prescribeId);
        BizAssert.notNull(ihcsInternalMedicalPrescribe, "处方不存在");
        String prescribeStatus = ihcsInternalMedicalPrescribe.getPrescribeStatus();
        InternalMedicalPrescribeStatusEnum.checkIsCanExecute(prescribeStatus);
        this.singleExecutePrescribe(ihcsInternalMedicalPrescribe);
        // 获取对应的管教信息
        Map<String, List<GetPoliceWarderInfoVO>> policeWarderInfoMap =
                this.getPoliceWarderInfoMap(Collections.singletonList(ihcsInternalMedicalPrescribe));
        // 生成一条拒绝执行记录
        PrescribeExecuteDO internalMedicalPrescribeExecute = PrescribeExecuteDO.builder()
                .prescribeId(prescribeId)
                .doctorAdvice(ihcsInternalMedicalPrescribe.getDoctorAdvice())
                .prescribeDescribe(ihcsInternalMedicalPrescribe.getPrescribeDescribe())
                .executeSource(InternalMedicalPrescribeExecuteSourceConstant.PRISON)
                .doseStatus(InternalMedicalPrescribeDoseStatusConstant.REFUSE_DOSE)
                .doseTime(new Date())
                .build();
        this.setWarderInfo(ihcsInternalMedicalPrescribe.getJgrybm(),
                policeWarderInfoMap, internalMedicalPrescribeExecute);
        ihcsInternalMedicalPrescribeExecuteService.save(internalMedicalPrescribeExecute);
    }

    @Override
    public void signRefuseExecutePrescribe(SignRefuseExecutePrescribeDTO signRefuseExecutePrescribeDTO) {
        ihcsInternalMedicalPrescribeExecuteService.updateById(
                PrescribeExecuteDO.builder()
                        .id(signRefuseExecutePrescribeDTO.getId())
                        .signPicUrl(signRefuseExecutePrescribeDTO.getRefuseDoseDescUrl())
                        .doseStatus(InternalMedicalPrescribeDoseStatusConstant.REFUSE_DOSE)
                        .signStatus(InternalMedicalPrescribeSignStatusConstant.SIGN)
                        .signTime(new Date())
                        .build());
    }

    @Override
    public void refusalToReceiveMedication(RefusalToReceiveMedicationDTO executePrescribeSignDTO) {
        //生成监室送药
        ExecutePrescribeDTO batchExecutePrescribeDTO = new ExecutePrescribeDTO();
        batchExecutePrescribeDTO.setId(executePrescribeSignDTO.getPrescribeId());
        executePrescribe(batchExecutePrescribeDTO);


    }




}
