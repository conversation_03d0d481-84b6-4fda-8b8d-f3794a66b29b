package com.rs.module.ihc.service.idm;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.idm.vo.RegularInspectionListReqVO;
import com.rs.module.ihc.controller.admin.idm.vo.RegularInspectionPageReqVO;
import com.rs.module.ihc.controller.admin.idm.vo.RegularInspectionSaveReqVO;
import com.rs.module.ihc.dao.idm.RegularInspectionDao;
import com.rs.module.ihc.entity.idm.RegularInspectionDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


/**
 * 传染病管理-定期检查登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RegularInspectionServiceImpl extends BaseServiceImpl<RegularInspectionDao, RegularInspectionDO> implements RegularInspectionService {

    @Resource
    private RegularInspectionDao regularInspectionDao;

    @Override
    public String createRegularInspection(RegularInspectionSaveReqVO createReqVO) {
        // 插入
        RegularInspectionDO regularInspection = BeanUtils.toBean(createReqVO, RegularInspectionDO.class);
        SessionUser user = SessionUserUtil.getSessionUser();
        regularInspection.setOperatePoliceSfzh(user.getIdCard());
        regularInspection.setOperatePolice(user.getName());
        regularInspection.setOperateTime(new Date());
        regularInspectionDao.insert(regularInspection);
        // 返回
        return regularInspection.getId();
    }

    @Override
    public void updateRegularInspection(RegularInspectionSaveReqVO updateReqVO) {
        // 校验存在
        validateRegularInspectionExists(updateReqVO.getId());
        // 更新
        RegularInspectionDO updateObj = BeanUtils.toBean(updateReqVO, RegularInspectionDO.class);
        regularInspectionDao.updateById(updateObj);
    }

    @Override
    public void deleteRegularInspection(String id) {
        // 校验存在
        validateRegularInspectionExists(id);
        // 删除
        regularInspectionDao.deleteById(id);
    }

    private void validateRegularInspectionExists(String id) {
        if (regularInspectionDao.selectById(id) == null) {
            throw new ServerException("传染病管理-定期检查登记数据不存在");
        }
    }

    @Override
    public RegularInspectionDO getRegularInspection(String id) {
        return regularInspectionDao.selectById(id);
    }

    @Override
    public PageResult<RegularInspectionDO> getRegularInspectionPage(RegularInspectionPageReqVO pageReqVO) {
        return regularInspectionDao.selectPage(pageReqVO);
    }

    @Override
    public List<RegularInspectionDO> getRegularInspectionList(RegularInspectionListReqVO listReqVO) {
        return regularInspectionDao.selectList(listReqVO);
    }


}
