package com.rs.module.ihc.controller.admin.pm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 大药房管理-药品报损新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PharmacyMedicineLossSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("药品id，对应ichs_medicine.id")
    @NotEmpty(message = "药品id，对应ichs_medicine.id不能为空")
    private String medicineId;

    @ApiModelProperty("入库批次id，对应ichs_medicine_in.id")
    @NotEmpty(message = "入库批次id，对应ichs_medicine_in.id不能为空")
    private String medicineInId;

    @ApiModelProperty("报损原因")
    @NotEmpty(message = "报损原因不能为空")
    private String lossReason;

    @ApiModelProperty("报损数量")
    @NotNull(message = "报损数量不能为空")
    private BigDecimal lossNum;

    @ApiModelProperty("报损时间")
    private Date lossTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("批次库存数量 最小单位")
    @NotNull(message = "批次库存数量 最小单位不能为空")
    private BigDecimal inventoryNum;

    @ApiModelProperty("当前药品总库存数量 最小单位")
    @NotNull(message = "当前药品总库存数量 最小单位不能为空")
    private BigDecimal totalInventoryNum;

}
