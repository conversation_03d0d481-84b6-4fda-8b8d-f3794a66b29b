package com.rs.module.ihc.dao.ipm;

import java.util.*;

import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.ihc.entity.ipm.PrescribeOutMedicineDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 所内就医-处方药品出库记录关联药品
 *
 * <AUTHOR>
 */
@Mapper
public interface PrescribeOutMedicineDao extends IBaseDao<PrescribeOutMedicineDO> {

    List<PrescribeOutMedicineDO> selectListByOutId(String outId);

    default int deleteByOutId(String outId) {
        return delete(new LambdaQueryWrapperX<PrescribeOutMedicineDO>().eq(PrescribeOutMedicineDO::getOutId, outId));
    }

}
