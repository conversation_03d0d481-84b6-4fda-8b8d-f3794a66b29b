package com.rs.module.ihc.controller.admin.ipm.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.annotation.Query;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(description = "管理后台 - 所内就医-处方-药方信息 Response VO")
@Data
public class PrescribeMedicineRespVO extends MedicineRespVO implements TransPojo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("处方id，对应ihc_ipm_prescribe.id")
    private String mlh;
    @ApiModelProperty("药品id，对应ihc_pm_drug.id")
    private String medicineId;
    @ApiModelProperty("总库存数量")
    private BigDecimal totalInventoryNum;
    @ApiModelProperty("每次用量（数字）")
    private BigDecimal oneDosageNum;
    @ApiModelProperty("单位")
    private String unit;
    @ApiModelProperty("使用频率")
    @Trans(type = TransType.DICTIONARY, key = "ZD_USE_MEDICINE_FREQUENCY")
    private String useFrequency;
    @ApiModelProperty("用药天数")
    private Integer useDay;
    @ApiModelProperty("总药量")
    private BigDecimal num;
    @ApiModelProperty("给药方式")
    @Trans(type = TransType.DICTIONARY, key = "ZD_USE_MEDICINE_METHOD")
    private String useMedicineMethod;
    @ApiModelProperty("嘱托")
    private String entrust;

    @Query(sql = "select * from ihc_pm_medicine where id = '${medicineId}' and is_del =0", beanClass = MedicineRespVO.class, isTile = true)
    public MedicineRespVO medicine;

    //管理后台 - 所内就医-处方-药方信息
    @Query(lbMark = "ihc:glhtsnjycfyfxx",beanClass = PrisonerRespVO.class)
    public PrisonerRespVO ryxx;
}
