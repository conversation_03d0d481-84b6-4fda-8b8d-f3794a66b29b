package com.rs.module.ihc.dao.hr;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.hr.PrintdocConfigDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.hr.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 健康档案-打印文书配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PrintdocConfigDao extends IBaseDao<PrintdocConfigDO> {


    default PageResult<PrintdocConfigDO> selectPage(PrintdocConfigPageReqVO reqVO) {
        Page<PrintdocConfigDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<PrintdocConfigDO> printdocConfigPage = selectPage(page, new LambdaQueryWrapperX<PrintdocConfigDO>()
            .eqIfPresent(PrintdocConfigDO::getFormId, reqVO.getFormId())
            .likeIfPresent(PrintdocConfigDO::getWsName, reqVO.getWsName())
            .eqIfPresent(PrintdocConfigDO::getWsUrl, reqVO.getWsUrl())
            .eqIfPresent(PrintdocConfigDO::getOrderId, reqVO.getOrderId())
            .eqIfPresent(PrintdocConfigDO::getIsDisabled, reqVO.getIsDisabled())
            .orderByDesc(PrintdocConfigDO::getId));
            return new PageResult<>(printdocConfigPage.getRecords(), printdocConfigPage.getTotal());
    }
    default List<PrintdocConfigDO> selectList(PrintdocConfigListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrintdocConfigDO>()
            .eqIfPresent(PrintdocConfigDO::getFormId, reqVO.getFormId())
            .likeIfPresent(PrintdocConfigDO::getWsName, reqVO.getWsName())
            .eqIfPresent(PrintdocConfigDO::getWsUrl, reqVO.getWsUrl())
            .eqIfPresent(PrintdocConfigDO::getOrderId, reqVO.getOrderId())
            .eqIfPresent(PrintdocConfigDO::getIsDisabled, reqVO.getIsDisabled())
        .orderByDesc(PrintdocConfigDO::getId));    }


    }
