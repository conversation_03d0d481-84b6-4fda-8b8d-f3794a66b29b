package com.rs.module.ihc.handler.ipm.prescribe;

import com.rs.framework.common.handler.AbstractHandler;
import com.rs.module.ihc.controller.admin.ipm.vo.PrescribeSaveReqVO;
import com.rs.module.ihc.enums.AdviceType;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @ClassName CnrsgHandler
 * @Description 身高-成年人
 * <AUTHOR>
 * @Date 2025/3/21 17:13
 * @Version 1.0
 */
@Component
public class PrescribeSaveValidateHandler extends AbstractHandler<PrescribeSaveReqVO> {
    @Override
    protected PrescribeSaveReqVO doHandle(PrescribeSaveReqVO input) {
        if (input.getMedicineList()== null || input.getMedicineList().size() == 0) {
            throw new RuntimeException("处方药品信息不能为空");
        }
        Date startPrescribeDate = input.getStartPrescribeDate();
        Date endPrescribeDate = input.getEndPrescribeDate();
        if (startPrescribeDate != null && endPrescribeDate != null && startPrescribeDate.after(endPrescribeDate)) {
            throw new RuntimeException("开始日期不能大于结束日期");
        }
        //短期医嘱相隔不能超5天
        if (AdviceType.TEMPORARY_ADVICE.getVal().equals(input.getDoctorAdviceType()) && startPrescribeDate != null && endPrescribeDate != null && (endPrescribeDate.getTime() - startPrescribeDate.getTime()) / (1000 * 60 * 60 * 24) > 5) {
            throw new RuntimeException("短期医嘱相隔不能超5天");
        }
        return input;
    }
}
