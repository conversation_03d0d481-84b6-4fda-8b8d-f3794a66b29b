package com.rs.module.ihc.service.pm.prescribe;

import java.util.*;
import javax.validation.*;

import com.rs.module.ihc.controller.admin.pm.prescribe.dto.PrescribeCheckupRegisterDTO;
import com.rs.module.ihc.controller.admin.pm.prescribe.vo.*;
import com.rs.module.ihc.entity.pm.prescribe.InternalMedicalPrescribeCheckupDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 所内就医-处方-体检登记 Service 接口
 *
 * <AUTHOR>
 */
public interface InternalMedicalPrescribeCheckupService extends IBaseService<InternalMedicalPrescribeCheckupDO>{

    /**
     * 创建所内就医-处方-体检登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createInternalMedicalPrescribeCheckup(@Valid InternalMedicalPrescribeCheckupSaveReqVO createReqVO);

    /**
     * 更新所内就医-处方-体检登记
     *
     * @param updateReqVO 更新信息
     */
    void updateInternalMedicalPrescribeCheckup(@Valid InternalMedicalPrescribeCheckupSaveReqVO updateReqVO);

    /**
     * 删除所内就医-处方-体检登记
     *
     * @param id 编号
     */
    void deleteInternalMedicalPrescribeCheckup(String id);

    /**
     * 获得所内就医-处方-体检登记
     *
     * @param id 编号
     * @return 所内就医-处方-体检登记
     */
    InternalMedicalPrescribeCheckupDO getInternalMedicalPrescribeCheckup(String id);

    /**
    * 获得所内就医-处方-体检登记分页
    *
    * @param pageReqVO 分页查询
    * @return 所内就医-处方-体检登记分页
    */
    PageResult<InternalMedicalPrescribeCheckupDO> getInternalMedicalPrescribeCheckupPage(InternalMedicalPrescribeCheckupPageReqVO pageReqVO);

    /**
    * 获得所内就医-处方-体检登记列表
    *
    * @param listReqVO 查询条件
    * @return 所内就医-处方-体检登记列表
    */
    List<InternalMedicalPrescribeCheckupDO> getInternalMedicalPrescribeCheckupList(InternalMedicalPrescribeCheckupListReqVO listReqVO);


    /**
     * 检查登记
     *
     * @param prescribeCheckupRegisterDTO 检查登记信息
     */
    void prescribeCheckupRegister(PrescribeCheckupRegisterDTO prescribeCheckupRegisterDTO);
}
