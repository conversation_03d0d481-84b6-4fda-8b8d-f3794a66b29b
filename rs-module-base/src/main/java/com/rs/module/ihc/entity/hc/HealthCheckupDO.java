package com.rs.module.ihc.entity.hc;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 五项体检 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_hc_health_checkup")
@KeySequence("ihc_hc_health_checkup_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ihc_hc_health_checkup")
public class HealthCheckupDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 被监管人员编号
     */
    private String jgrybm;
    /**
     * 体检状态
     */
    private String checkupStatus;
    /**
     * 体检时间
     */
    private Date checkupTime;
    /**
     * 病史
     */
    private String medicalHistory;
    /**
     * 药物过敏
     */
    private String drugAllergy;
    /**
     * 家族病史
     */
    private String familyMedicalHistory;
    /**
     * 目前身体状况
     */
    private String healthStatus;
    /**
     * 血压(高压)
     */
    private Double bloodPressureHeight;
    /**
     * 血压(低压)
     */
    private Double bloodPressureLow;
    /**
     * 脉搏
     */
    private Double pulse;
    /**
     * 常规检查
     */
    private String routineExamination;
    /**
     * 血常规
     */
    private String bloodRoutineExamination;
    /**
     * B超
     */
    private String bUltrasonics;
    /**
     * 肝、胆、脾、胰
     */
    private String fiveInternalOrgans;
    /**
     * 双肾、输尿管、膀胱
     */
    private String urinarySystem;
    /**
     * 心电图
     */
    private String electrocardiogram;
    /**
     * 常规心电图
     */
    private String routineElectrocardiogram;
    /**
     * X光
     */
    private String xRay;
    /**
     * 胸部
     */
    private String chest;
    /**
     * 检查结论
     */
    private String checkConclusion;
    /**
     * 医生
     */
    private String checkupDoctor;
    /**
     * 医生名称
     */
    private String checkupDoctorName;
    /**
     * 录入时间
     */
    private Date enterTime;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     *  被监管人员姓名
     */
    private String jgryxm;

}
