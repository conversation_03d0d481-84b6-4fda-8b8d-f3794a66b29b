package com.rs.module.ihc.service.ipm;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.OutpatientDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 所内就医-所内门诊 Service 接口
 *
 * <AUTHOR>
 */
public interface OutpatientService extends IBaseService<OutpatientDO>{

    /**
     * 创建所内就医-所内门诊
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createOutpatient(@Valid OutpatientSaveReqVO createReqVO);

    /**
     * 更新所内就医-所内门诊
     *
     * @param updateReqVO 更新信息
     */
    void updateOutpatient(@Valid OutpatientSaveReqVO updateReqVO);
    void createOutpatientCf(@Valid OutpatientSaveReqVO updateReqVO);
    void updateOutpatientCf(@Valid OutpatientSaveReqVO updateReqVO);

    /**
     * 删除所内就医-所内门诊
     *
     * @param id 编号
     */
    void deleteOutpatient(String id);

    /**
     * 获得所内就医-所内门诊
     *
     * @param id 编号
     * @return 所内就医-所内门诊
     */
    OutpatientDO getOutpatient(String id);

    /**
    * 获得所内就医-所内门诊分页
    *
    * @param pageReqVO 分页查询
    * @return 所内就医-所内门诊分页
    */
    PageResult<OutpatientDO> getOutpatientPage(OutpatientPageReqVO pageReqVO);

    /**
    * 获得所内就医-所内门诊列表
    *
    * @param listReqVO 查询条件
    * @return 所内就医-所内门诊列表
    */
    List<OutpatientDO> getOutpatientList(OutpatientListReqVO listReqVO);


}
