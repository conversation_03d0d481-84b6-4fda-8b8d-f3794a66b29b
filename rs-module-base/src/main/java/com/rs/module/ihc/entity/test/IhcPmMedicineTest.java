package com.rs.module.ihc.entity.test;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 药品实体类(测试)
 * <AUTHOR>
 *
 */
@TableName("ihc_pm_medicine")
@Data
public class IhcPmMedicineTest implements Serializable{
	
	private static final long serialVersionUID = 1L;

	@TableId("id")
    private String id;

    @TableField("medicine_name")
    private String medicineName;
    
    @TableField("medicine_spell_name")
    private String medicineSpellName;
    
    @TableField("specs")
    private String specs;
    
    @TableField("product_unit")
    private String productUnit;
    
    @TableField("approval_num")
    private String approvalNum;
}
