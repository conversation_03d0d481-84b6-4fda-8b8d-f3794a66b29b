package com.rs.module.ihc.controller.admin.ipm.vo;

import com.rs.framework.common.annotation.DefaultValueInteger;
import com.rs.framework.common.annotation.DefaultValueString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "管理后台 - 所内就医-所内门诊新增/修改 Request VO")
@Data
public class OutpatientSaveReqVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("来源（1:所内就医预约，2:手动新增）")
    @DefaultValueString("2")
    private String source;

    @ApiModelProperty("预约id，对应ihc_ipm_internal_medical_appointment.id")
    private String appointmentId;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("在押人员编号")
    private String rybh;

    @ApiModelProperty("在押人员名称")
    private String ryxm;

    @ApiModelProperty("是否重病号（ 0：否 ，1：是）")
    private Integer sickType;

    @ApiModelProperty("报病时间")
    private Date diseaseTime;

    @ApiModelProperty("报病原因")
    private String diseaseReason;

    @ApiModelProperty("主诉")
    private String mainComplaint;

    @ApiModelProperty("情况登记")
    private String illnessResume;

    @ApiModelProperty("状态（ 0：待看诊，1：已看诊）")
    @DefaultValueInteger(1)
    private Integer status;

    @ApiModelProperty("监所id")
    private String prisonId;

    private PrescribeSaveReqVO prescribe;

}
