package com.rs.module.ihc.entity.pm.sick;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 重特病号解除申请审批 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_pm_severely_sick_relive_approve")
@KeySequence("ihc_pm_severely_sick_relive_approve_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SeverelySickReliveApproveDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 关联重特病号解除申请表id
     */
    private String reliveApplyId;
    /**
     * 业务状态 字典值：severely_sick_business_status
     */
    private String businessStatus;
    /**
     * 审批结果 字典值：is_agree
     */
    private String approveResult;
    /**
     * 审批人id
     */
    private Long approveUserId;
    /**
     * 审批人姓名
     */
    private String approveUserName;
    /**
     * 审批意见
     */
    private String approveOpinion;
    /**
     * 审批时间
     */
    private Date approveTime;

}
