package com.rs.module.ihc.controller.admin.pm.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 药房管理-药品入库批次 Response VO")
@Data
public class MedicineInImportVO {
private static final long serialVersionUID = 1L;
    @ExcelProperty("批准文号")
    private String approvalNum;

    @ExcelProperty("药品名称")
    private String medicineName;

    @ExcelProperty("剂型")
    private String dosageForm;

    @ExcelProperty("规格")
    private String specs;

    @ExcelProperty("类型")
    private String medicineCategory;

    @ExcelProperty("生产单位")
    private String productUnit;

    @ExcelProperty("单位")
    private String unit;

    @ExcelProperty("数量")
    private BigDecimal sl;

    @ExcelProperty("有效期至")
    private String yxq;

    @ExcelProperty("生产日期")
    private String scrq;

    @ExcelIgnore
    private String errMsg;

    @ExcelIgnore
    private Date yxqDate;

    @ExcelIgnore
    private Date scrqDate;
    @ExcelIgnore
    private String shdh;

}
