package com.rs.module.ihc.entity.pm.sick;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import java.util.Date;

/**
 * 重特病号管理 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_pm_severely_sick_manage")
@KeySequence("ihc_pm_severely_sick_manage_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SeverelySickManageDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 所属监所id
     */
    private String prisonId;
    /**
     * 被监管人id
     */
    private String jgrybm;
    /**
     * 业务状态 字典值：severely_sick_business_status
     */
    private String businessStatus;
    /**
     * 病号类别 字典值：severely_sick_type
     */
    private String severelySickType;
    /**
     * 病情情况
     */
    private String patientSituation;
    /**
     * 治疗情况
     */
    private String treatmentSituation;
    /**
     * 申请人id
     */
    private String applyUserId;
    /**
     * 申请人姓名
     */
    private String applyUserName;
    /**
     * 申请时间
     */
    private Date applyTime;
    /**
     * 当前是否是重病号 字典值：boolean_type
     */
    private String isSeverelySick;
    /**
     * 对应的最新的一条解除申请id
     */
    private String relieveApplyId;
    /**
     * 解除申请审核结果 字典值：is_agree
     */
    private String relieveApproveResult;
    /**
     * 解除时间
     */
    private Date relieveTime;

}
