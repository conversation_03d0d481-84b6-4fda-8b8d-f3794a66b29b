package com.rs.module.ihc.controller.admin.pm.vo;

import lombok.AllArgsConstructor;
import lombok.Data; /**
 * @ClassName ValidationResult
 * 
 * <AUTHOR>
 * @Date 2025/3/18 15:27
 * @Version 1.0
 */
@Data
@AllArgsConstructor
public class ValidationResult {
    private boolean isSuccess;
    private String errorMsg;

    public static ValidationResult success() {
        return new ValidationResult(true, null);
    }

    public static ValidationResult fail(String errorMsg) {
        return new ValidationResult(false, errorMsg);
    }

    public boolean existFail() {
        return !isSuccess;
    }

}
