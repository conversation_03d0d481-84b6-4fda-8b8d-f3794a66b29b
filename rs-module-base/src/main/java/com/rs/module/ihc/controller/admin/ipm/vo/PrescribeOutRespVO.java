package com.rs.module.ihc.controller.admin.ipm.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.module.ihc.entity.ipm.PrescribeOutMedicineDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 所内就医-处方药品出库记录 Response VO")
@Data
public class PrescribeOutRespVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("添加人姓名")
    private String addUserName;
    @ApiModelProperty("添加时间")
    private Date addTime;
    @ApiModelProperty("更新人姓名")
    private String updateUserName;
    @ApiModelProperty("处方id，对应ihc_ipm_prescribe.id")
    private String prescribeId;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("医嘱")
    private String doctorAdvice;
    @ApiModelProperty("处方说明")
    private String prescribeDescribe;
    @ApiModelProperty("所内就医-处方药品出库记录关联药品列表")
    private List<PrescribeOutMedicineRespVO> prescribeOutMedicines;
}
