package com.rs.module.ihc.entity.pm;

import lombok.*;
import java.util.*;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 药房管理-供应商 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_pm_supplier")
@KeySequence("ihc_pm_supplier_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 地址
     */
    private String address;
    /**
     * 邮编
     */
    private String postalCode;
    /**
     * 电话
     */
    private String telephone;
    /**
     * 联系人
     */
    private String contacts;
    /**
     * 开户行
     */
    private String openingBank;
    /**
     * 账号
     */
    private String account;
    /**
     * 税号
     */
    private String dutyParagraph;
    /**
     * 监所id
     */
    private String prisonId;

}
