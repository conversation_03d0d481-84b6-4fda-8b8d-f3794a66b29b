package com.rs.module.ihc.controller.admin.ipm.vo;

import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@ApiModel(description = "管理后台 - 所内就医-现场巡诊计划分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VisitPlanPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("来源（2:手动新增，3:加戴械具）")
    private String source;

    @ApiModelProperty("来源id，存第三方来源的主键")
    private String sourceId;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("在押人员编号")
    private String rybh;

    @ApiModelProperty("在押人员名称")
    private String ryxm;

    @ApiModelProperty("原因")
    private String reason;

    @ApiModelProperty("开始日期")
    private Date[] startDate;

    @ApiModelProperty("结束日期")
    private Date[] endDate;

    @ApiModelProperty("巡诊频率")
    private String frequency;

    @ApiModelProperty("监所id")
    private String prisonId;

    @ApiModelProperty("状态（ 0：停用，1：启用）")
    private String status;

    @ApiModelProperty("最后一次创建巡诊时间")
    private Date[] lastVisitCreateTime;

}
