package com.rs.module.ihc.service.ipm;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.CaseTemplateDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.ipm.CaseTemplateDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 所内就医-病例模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CaseTemplateServiceImpl extends BaseServiceImpl<CaseTemplateDao, CaseTemplateDO> implements CaseTemplateService {

    @Resource
    private CaseTemplateDao caseTemplateDao;

    @Override
    public String createCaseTemplate(CaseTemplateSaveReqVO createReqVO) {
        // 插入
        CaseTemplateDO caseTemplate = BeanUtils.toBean(createReqVO, CaseTemplateDO.class);
        caseTemplateDao.insert(caseTemplate);
        // 返回
        return caseTemplate.getId();
    }

    @Override
    public void updateCaseTemplate(CaseTemplateSaveReqVO updateReqVO) {
        // 校验存在
        validateCaseTemplateExists(updateReqVO.getId());
        // 更新
        CaseTemplateDO updateObj = BeanUtils.toBean(updateReqVO, CaseTemplateDO.class);
        caseTemplateDao.updateById(updateObj);
    }

    @Override
    public void deleteCaseTemplate(String id) {
        // 校验存在
        validateCaseTemplateExists(id);
        // 删除
        caseTemplateDao.deleteById(id);
    }

    private void validateCaseTemplateExists(String id) {
        if (caseTemplateDao.selectById(id) == null) {
            throw new ServerException("所内就医-病例模板数据不存在");
        }
    }

    @Override
    public CaseTemplateDO getCaseTemplate(String id) {
        return caseTemplateDao.selectById(id);
    }

    @Override
    public PageResult<CaseTemplateDO> getCaseTemplatePage(CaseTemplatePageReqVO pageReqVO) {
        return caseTemplateDao.selectPage(pageReqVO);
    }

    @Override
    public List<CaseTemplateDO> getCaseTemplateList(CaseTemplateListReqVO listReqVO) {
        return caseTemplateDao.selectList(listReqVO);
    }


}
