package com.rs.module.ihc.controller.admin.idm.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 传染病管理-定期检查登记列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RegularInspectionListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("传染病登记ID")
    private String infectionId;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("检查日期/分类管控记录日期")
    private Date[] checkDate;

    @ApiModelProperty("cd4t淋巴值")
    private String cd4tlbz;

    @ApiModelProperty("病毒载量值")
    private String viralLoadValue;

    @ApiModelProperty("管理情况/分类管控情况")
    private String managementSituation;

    @ApiModelProperty("管控人")
    private String controlPersonnel;

    @ApiModelProperty("附件地址")
    private String attUrl;

    @ApiModelProperty("登记民警身份证号")
    private String operatePoliceSfzh;

    @ApiModelProperty("登记民警")
    private String operatePolice;

    @ApiModelProperty("登记时间")
    private Date[] operateTime;

}
