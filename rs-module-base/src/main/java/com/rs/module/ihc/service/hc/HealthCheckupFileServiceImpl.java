package com.rs.module.ihc.service.hc;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.ihc.controller.admin.hc.vo.*;
import com.rs.module.ihc.entity.hc.HealthCheckupFileDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.hc.HealthCheckupFileDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 五项体检-附件 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HealthCheckupFileServiceImpl extends BaseServiceImpl<HealthCheckupFileDao, HealthCheckupFileDO> implements HealthCheckupFileService {

    @Resource
    private HealthCheckupFileDao healthCheckupFileDao;

    @Override
    public String createHealthCheckupFile(HealthCheckupFileSaveReqVO createReqVO) {
        // 插入
        HealthCheckupFileDO healthCheckupFile = BeanUtils.toBean(createReqVO, HealthCheckupFileDO.class);
        healthCheckupFileDao.insert(healthCheckupFile);
        // 返回
        return healthCheckupFile.getId();
    }

    @Override
    public void updateHealthCheckupFile(HealthCheckupFileSaveReqVO updateReqVO) {
        // 校验存在
        validateHealthCheckupFileExists(updateReqVO.getId());
        // 更新
        HealthCheckupFileDO updateObj = BeanUtils.toBean(updateReqVO, HealthCheckupFileDO.class);
        healthCheckupFileDao.updateById(updateObj);
    }

    @Override
    public void deleteHealthCheckupFile(String id) {
        // 校验存在
        validateHealthCheckupFileExists(id);
        // 删除
        healthCheckupFileDao.deleteById(id);
    }

    private void validateHealthCheckupFileExists(String id) {
        if (healthCheckupFileDao.selectById(id) == null) {
            throw new ServerException("五项体检-附件数据不存在");
        }
    }

    @Override
    public HealthCheckupFileDO getHealthCheckupFile(String id) {
        return healthCheckupFileDao.selectById(id);
    }

    @Override
    public PageResult<HealthCheckupFileDO> getHealthCheckupFilePage(HealthCheckupFilePageReqVO pageReqVO) {
        return healthCheckupFileDao.selectPage(pageReqVO);
    }

    @Override
    public List<HealthCheckupFileDO> getHealthCheckupFileList(HealthCheckupFileListReqVO listReqVO) {
        return healthCheckupFileDao.selectList(listReqVO);
    }


}
