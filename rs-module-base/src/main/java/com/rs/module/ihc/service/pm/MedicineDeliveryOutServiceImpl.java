package com.rs.module.ihc.service.pm;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.MedicineDeliveryOutDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.pm.MedicineDeliveryOutDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 药房管理-顾送药品出库 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MedicineDeliveryOutServiceImpl extends BaseServiceImpl<MedicineDeliveryOutDao, MedicineDeliveryOutDO> implements MedicineDeliveryOutService {

    @Resource
    private MedicineDeliveryOutDao medicineDeliveryOutDao;

    @Override
    public String createMedicineDeliveryOut(MedicineDeliveryOutSaveReqVO createReqVO) {
        // 插入
        MedicineDeliveryOutDO medicineDeliveryOut = BeanUtils.toBean(createReqVO, MedicineDeliveryOutDO.class);
        medicineDeliveryOutDao.insert(medicineDeliveryOut);
        // 返回
        return medicineDeliveryOut.getId();
    }

    @Override
    public void updateMedicineDeliveryOut(MedicineDeliveryOutSaveReqVO updateReqVO) {
        // 校验存在
        validateMedicineDeliveryOutExists(updateReqVO.getId());
        // 更新
        MedicineDeliveryOutDO updateObj = BeanUtils.toBean(updateReqVO, MedicineDeliveryOutDO.class);
        medicineDeliveryOutDao.updateById(updateObj);
    }

    @Override
    public void deleteMedicineDeliveryOut(String id) {
        // 校验存在
        validateMedicineDeliveryOutExists(id);
        // 删除
        medicineDeliveryOutDao.deleteById(id);
    }

    private void validateMedicineDeliveryOutExists(String id) {
        if (medicineDeliveryOutDao.selectById(id) == null) {
            throw new ServerException("药房管理-顾送药品出库数据不存在");
        }
    }

    @Override
    public MedicineDeliveryOutDO getMedicineDeliveryOut(String id) {
        return medicineDeliveryOutDao.selectById(id);
    }

    @Override
    public PageResult<MedicineDeliveryOutDO> getMedicineDeliveryOutPage(MedicineDeliveryOutPageReqVO pageReqVO) {
        return medicineDeliveryOutDao.selectPage(pageReqVO);
    }

    @Override
    public List<MedicineDeliveryOutDO> getMedicineDeliveryOutList(MedicineDeliveryOutListReqVO listReqVO) {
        return medicineDeliveryOutDao.selectList(listReqVO);
    }


}
