package com.rs.module.ihc.controller.admin.ipm.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * @ClassName PrescribeMedicineOutInfoV2List
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/1 16:18
 * @Version 1.0
 */
@Data
public class PrescribeMedicineOutInfoV2List {
    /**
     * 处方/医嘱id
     */
    @NotNull(message = "处方/医嘱id不能为空")
    @ApiModelProperty(value = "处方/医嘱id", required = true)
    private String prescribeId;

    /**
     * 药品id
     */
    @NotNull(message = "药品id不能为空")
    @ApiModelProperty(value = "药品id", required = true)
    private String medicineId;

    /**
     * 处方药品出库数量 最小单位
     */
    @Positive(message = "药品出库数量不能小于0")
    @NotNull(message = "药品出库数量不能为空")
    @ApiModelProperty(value = "处方药品出库数量 最小单位", required = true)
    private BigDecimal outNum;
}
