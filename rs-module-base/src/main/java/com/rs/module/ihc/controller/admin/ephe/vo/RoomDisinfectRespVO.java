package com.rs.module.ihc.controller.admin.ephe.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "管理后台 - 卫生防疫与健康教育--监室消毒 Response VO")
@Data
public class RoomDisinfectRespVO implements TransPojo {
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("数据来源")
    private String dataSources;
    @ApiModelProperty("监室ID")
    private String roomId;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("消毒方式")
    @Trans(type = TransType.DICTIONARY,key = "ZD_JSXDFS")
    private String disinfectMethod;
    @ApiModelProperty("消毒民警身份证号")
    private String disinfectPoliceSfzh;
    @ApiModelProperty("消毒民警姓名")
    private String disinfectPolice;
    @ApiModelProperty("消毒时间")
    private Date disinfectTime;
}
