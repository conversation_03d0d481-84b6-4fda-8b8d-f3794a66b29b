package com.rs.module.ihc.controller.admin.ipm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 所内就医-病例模板表-字典新增/修改 Request VO")
@Data
public class CaseTemplateDicSaveReqVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("名称")
    private String lable;

    @ApiModelProperty("分类1既往史2个人史3体征4体温5脉搏6呼吸7血压8体重9身高10血氧11血糖")
    private String fl;

    @ApiModelProperty("分组1收缩压2舒张压3婴儿童4青少年5成年人")
    private String fz;

    @ApiModelProperty("排列顺序")
    private String order;

    @ApiModelProperty("分组排列顺序")
    private String fzOrder;

    @ApiModelProperty("大类1主诉2既往史3体格检查")
    private String dl;

}
