package com.rs.module.ihc.dao.ipm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.ipm.CaseTemplateDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 所内就医-病例模板 Dao
*
* <AUTHOR>
*/
@Mapper
public interface CaseTemplateDao extends IBaseDao<CaseTemplateDO> {


    default PageResult<CaseTemplateDO> selectPage(CaseTemplatePageReqVO reqVO) {
        Page<CaseTemplateDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<CaseTemplateDO> wrapper = new LambdaQueryWrapperX<CaseTemplateDO>()
            .likeIfPresent(CaseTemplateDO::getTemplateName, reqVO.getTemplateName())
            .eqIfPresent(CaseTemplateDO::getTemplateType, reqVO.getTemplateType())
            .eqIfPresent(CaseTemplateDO::getMainComplaint, reqVO.getMainComplaint())
            .eqIfPresent(CaseTemplateDO::getMedicalHistory, reqVO.getMedicalHistory())
            .eqIfPresent(CaseTemplateDO::getPhysicalCheck, reqVO.getPhysicalCheck())
            .eqIfPresent(CaseTemplateDO::getAuxiliaryCheck, reqVO.getAuxiliaryCheck())
            .eqIfPresent(CaseTemplateDO::getPrimaryDiagnosis, reqVO.getPrimaryDiagnosis())
            .eqIfPresent(CaseTemplateDO::getSuggestion, reqVO.getSuggestion())
            .eqIfPresent(CaseTemplateDO::getIllnessResume, reqVO.getIllnessResume())
            .eqIfPresent(CaseTemplateDO::getVisitState, reqVO.getVisitState())
            .eqIfPresent(CaseTemplateDO::getVisitConclusion, reqVO.getVisitConclusion())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
            wrapper.orderByDesc(CaseTemplateDO::getAddTime);
        }
        Page<CaseTemplateDO> caseTemplatePage = selectPage(page, wrapper);
        return new PageResult<>(caseTemplatePage.getRecords(), caseTemplatePage.getTotal());
    }
    default List<CaseTemplateDO> selectList(CaseTemplateListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CaseTemplateDO>()
            .likeIfPresent(CaseTemplateDO::getTemplateName, reqVO.getTemplateName())
            .eqIfPresent(CaseTemplateDO::getTemplateType, reqVO.getTemplateType())
            .eqIfPresent(CaseTemplateDO::getMainComplaint, reqVO.getMainComplaint())
            .eqIfPresent(CaseTemplateDO::getMedicalHistory, reqVO.getMedicalHistory())
            .eqIfPresent(CaseTemplateDO::getPhysicalCheck, reqVO.getPhysicalCheck())
            .eqIfPresent(CaseTemplateDO::getAuxiliaryCheck, reqVO.getAuxiliaryCheck())
            .eqIfPresent(CaseTemplateDO::getPrimaryDiagnosis, reqVO.getPrimaryDiagnosis())
            .eqIfPresent(CaseTemplateDO::getSuggestion, reqVO.getSuggestion())
            .eqIfPresent(CaseTemplateDO::getIllnessResume, reqVO.getIllnessResume())
            .eqIfPresent(CaseTemplateDO::getVisitState, reqVO.getVisitState())
            .eqIfPresent(CaseTemplateDO::getVisitConclusion, reqVO.getVisitConclusion())
        .orderByDesc(CaseTemplateDO::getAddTime));    }


    }
