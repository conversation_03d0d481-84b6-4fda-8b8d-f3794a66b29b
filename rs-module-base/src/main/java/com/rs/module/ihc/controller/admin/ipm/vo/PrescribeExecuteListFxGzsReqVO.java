package com.rs.module.ihc.controller.admin.ipm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "管理后台 - 所内就医-医嘱/处方执行记录列表 Request VO")
@Data
public class PrescribeExecuteListFxGzsReqVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("添加人姓名")
    private String addUserName;

    @ApiModelProperty("更新人姓名")
    private String updateUserName;

    @ApiModelProperty("处方id，对应ihc_ipm_prescribe.id")
    private String prescribeId;

    @ApiModelProperty("签名图片地址")
    private String signPicUrl;

    @ApiModelProperty("服药视频url")
    private String doseVideoUrl;

    @ApiModelProperty("签名状态 0=未签名 1=已签名 字典值")
    private String signStatus;

    @ApiModelProperty("签名时间")
    private Date[] signTime;

    @ApiModelProperty("服药状态 0=未服药 1=已服药 2=拒绝服药 字典：dose_status")
    private String doseStatus;

    @ApiModelProperty("服药时间")
    private Date[] doseTime;

    @ApiModelProperty("医嘱")
    private String doctorAdvice;

    @ApiModelProperty("处方说明")
    private String prescribeDescribe;

    @ApiModelProperty("医嘱/处方执行发药来源 1：监室送药  2：临床送药 字典值 internal_medical_prescribe_execute_source")
    private String executeSource;

    @ApiModelProperty("医务民警id")
    private String medicalPoliceSfzh;

    @ApiModelProperty("医务民警名称")
    private String medicalPoliceName;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("监室id")
    private String roomId;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("主管民警id")
    private String managePoliceSfzh;

    @ApiModelProperty("主管民警名称")
    private String managePoliceName;

    @ApiModelProperty("协管民警ids，多个逗号分隔")
    private String assistPoliceSfzhs;

    @ApiModelProperty("协管民警名称，多个逗号分隔")
    private String assistPoliceNames;

}
