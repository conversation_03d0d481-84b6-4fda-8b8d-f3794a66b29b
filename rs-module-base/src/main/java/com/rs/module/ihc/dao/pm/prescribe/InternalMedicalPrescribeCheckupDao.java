package com.rs.module.ihc.dao.pm.prescribe;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.pm.prescribe.InternalMedicalPrescribeCheckupDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.pm.prescribe.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 所内就医-处方-体检登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface InternalMedicalPrescribeCheckupDao extends IBaseDao<InternalMedicalPrescribeCheckupDO> {


    default PageResult<InternalMedicalPrescribeCheckupDO> selectPage(InternalMedicalPrescribeCheckupPageReqVO reqVO) {
        Page<InternalMedicalPrescribeCheckupDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<InternalMedicalPrescribeCheckupDO> wrapper = new LambdaQueryWrapperX<InternalMedicalPrescribeCheckupDO>()
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getPrescribeId, reqVO.getPrescribeId())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getSupervisedUserCode, reqVO.getSupervisedUserCode())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getBloodPressureHeight, reqVO.getBloodPressureHeight())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getBloodPressureLow, reqVO.getBloodPressureLow())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getTemperature, reqVO.getTemperature())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getHeartRate, reqVO.getHeartRate())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getBreatheFrequency, reqVO.getBreatheFrequency())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getPulse, reqVO.getPulse())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getBloodOxygen, reqVO.getBloodOxygen())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getBloodSugar, reqVO.getBloodSugar())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getInsulin, reqVO.getInsulin())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getRemark, reqVO.getRemark())
            .betweenIfPresent(InternalMedicalPrescribeCheckupDO::getInjectionTime, reqVO.getInjectionTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(InternalMedicalPrescribeCheckupDO::getAddTime);
        }
        Page<InternalMedicalPrescribeCheckupDO> internalMedicalPrescribeCheckupPage = selectPage(page, wrapper);
        return new PageResult<>(internalMedicalPrescribeCheckupPage.getRecords(), internalMedicalPrescribeCheckupPage.getTotal());
    }
    default List<InternalMedicalPrescribeCheckupDO> selectList(InternalMedicalPrescribeCheckupListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<InternalMedicalPrescribeCheckupDO>()
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getPrescribeId, reqVO.getPrescribeId())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getSupervisedUserCode, reqVO.getSupervisedUserCode())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getBloodPressureHeight, reqVO.getBloodPressureHeight())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getBloodPressureLow, reqVO.getBloodPressureLow())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getTemperature, reqVO.getTemperature())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getHeartRate, reqVO.getHeartRate())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getBreatheFrequency, reqVO.getBreatheFrequency())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getPulse, reqVO.getPulse())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getBloodOxygen, reqVO.getBloodOxygen())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getBloodSugar, reqVO.getBloodSugar())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getInsulin, reqVO.getInsulin())
            .eqIfPresent(InternalMedicalPrescribeCheckupDO::getRemark, reqVO.getRemark())
            .betweenIfPresent(InternalMedicalPrescribeCheckupDO::getInjectionTime, reqVO.getInjectionTime())
        .orderByDesc(InternalMedicalPrescribeCheckupDO::getAddTime));    }


    }
