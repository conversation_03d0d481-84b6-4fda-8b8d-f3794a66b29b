package com.rs.module.ihc.dao.ipm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.controller.admin.ipm.vo.PrescribeListReqVO;
import com.rs.module.ihc.controller.admin.ipm.vo.PrescribePageReqVO;
import com.rs.module.ihc.controller.admin.ipm.vo.PrescribeRespVO;
import com.rs.module.ihc.entity.ipm.PrescribeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 所内就医-处方 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PrescribeDao extends IBaseDao<PrescribeDO> {


    default PageResult<PrescribeDO> selectPage(PrescribePageReqVO reqVO) {
        Page<PrescribeDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<PrescribeDO> prescribePage = selectPage(page, new LambdaQueryWrapperX<PrescribeDO>()
            .eqIfPresent(PrescribeDO::getPrescribeType, reqVO.getPrescribeType())
            .eqIfPresent(PrescribeDO::getBusinessId, reqVO.getBusinessId())
            .eqIfPresent(PrescribeDO::getDoctorAdviceNum, reqVO.getDoctorAdviceNum())
            .eqIfPresent(PrescribeDO::getDoctorAdviceType, reqVO.getDoctorAdviceType())
            .eqIfPresent(PrescribeDO::getMainComplaint, reqVO.getMainComplaint())
            .eqIfPresent(PrescribeDO::getMedicalHistory, reqVO.getMedicalHistory())
            .eqIfPresent(PrescribeDO::getPhysicalCheck, reqVO.getPhysicalCheck())
            .eqIfPresent(PrescribeDO::getAuxiliaryCheck, reqVO.getAuxiliaryCheck())
            .eqIfPresent(PrescribeDO::getPrimaryDiagnosis, reqVO.getPrimaryDiagnosis())
            .eqIfPresent(PrescribeDO::getSuggestion, reqVO.getSuggestion())
            .eqIfPresent(PrescribeDO::getPrescribeNum, reqVO.getPrescribeNum())
            .eqIfPresent(PrescribeDO::getDoctorAdvice, reqVO.getDoctorAdvice())
            .eqIfPresent(PrescribeDO::getPrescribeDescribe, reqVO.getPrescribeDescribe())
            .betweenIfPresent(PrescribeDO::getPrescribeTime, reqVO.getPrescribeTime())
            .eqIfPresent(PrescribeDO::getPrescribeUserid, reqVO.getPrescribeUserid())
            .likeIfPresent(PrescribeDO::getPrescribeUserName, reqVO.getPrescribeUserName())
            .eqIfPresent(PrescribeDO::getPrescribeStatus, reqVO.getPrescribeStatus())
            .eqIfPresent(PrescribeDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PrescribeDO::getRybh, reqVO.getRybh())
            .eqIfPresent(PrescribeDO::getRyxm, reqVO.getRyxm())
            .eqIfPresent(PrescribeDO::getDispenseNum, reqVO.getDispenseNum())
            .betweenIfPresent(PrescribeDO::getLastDispenseTime, reqVO.getLastDispenseTime())
            .eqIfPresent(PrescribeDO::getDoseNum, reqVO.getDoseNum())
            .eqIfPresent(PrescribeDO::getOutNum, reqVO.getOutNum())
            .betweenIfPresent(PrescribeDO::getLastOutTime, reqVO.getLastOutTime())
            .eqIfPresent(PrescribeDO::getPrisonId, reqVO.getPrisonId())
            .eqIfPresent(PrescribeDO::getPrescribeMedicineType, reqVO.getPrescribeMedicineType())
            .betweenIfPresent(PrescribeDO::getStartPrescribeDate, reqVO.getStartPrescribeDate())
            .betweenIfPresent(PrescribeDO::getEndPrescribeDate, reqVO.getEndPrescribeDate())
            .eqIfPresent(PrescribeDO::getCheckupNum, reqVO.getCheckupNum())
            .orderByDesc(PrescribeDO::getId));
            return new PageResult<>(prescribePage.getRecords(), prescribePage.getTotal());
    }
    default List<PrescribeDO> selectList(PrescribeListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrescribeDO>()
            .eqIfPresent(PrescribeDO::getPrescribeType, reqVO.getPrescribeType())
            .eqIfPresent(PrescribeDO::getBusinessId, reqVO.getBusinessId())
            .eqIfPresent(PrescribeDO::getDoctorAdviceNum, reqVO.getDoctorAdviceNum())
            .eqIfPresent(PrescribeDO::getDoctorAdviceType, reqVO.getDoctorAdviceType())
            .eqIfPresent(PrescribeDO::getMainComplaint, reqVO.getMainComplaint())
            .eqIfPresent(PrescribeDO::getMedicalHistory, reqVO.getMedicalHistory())
            .eqIfPresent(PrescribeDO::getPhysicalCheck, reqVO.getPhysicalCheck())
            .eqIfPresent(PrescribeDO::getAuxiliaryCheck, reqVO.getAuxiliaryCheck())
            .eqIfPresent(PrescribeDO::getPrimaryDiagnosis, reqVO.getPrimaryDiagnosis())
            .eqIfPresent(PrescribeDO::getSuggestion, reqVO.getSuggestion())
            .eqIfPresent(PrescribeDO::getPrescribeNum, reqVO.getPrescribeNum())
            .eqIfPresent(PrescribeDO::getDoctorAdvice, reqVO.getDoctorAdvice())
            .eqIfPresent(PrescribeDO::getPrescribeDescribe, reqVO.getPrescribeDescribe())
            .betweenIfPresent(PrescribeDO::getPrescribeTime, reqVO.getPrescribeTime())
            .eqIfPresent(PrescribeDO::getPrescribeUserid, reqVO.getPrescribeUserid())
            .likeIfPresent(PrescribeDO::getPrescribeUserName, reqVO.getPrescribeUserName())
            .eqIfPresent(PrescribeDO::getPrescribeStatus, reqVO.getPrescribeStatus())
            .eqIfPresent(PrescribeDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(PrescribeDO::getRybh, reqVO.getRybh())
            .eqIfPresent(PrescribeDO::getRyxm, reqVO.getRyxm())
            .eqIfPresent(PrescribeDO::getDispenseNum, reqVO.getDispenseNum())
            .betweenIfPresent(PrescribeDO::getLastDispenseTime, reqVO.getLastDispenseTime())
            .eqIfPresent(PrescribeDO::getDoseNum, reqVO.getDoseNum())
            .eqIfPresent(PrescribeDO::getOutNum, reqVO.getOutNum())
            .betweenIfPresent(PrescribeDO::getLastOutTime, reqVO.getLastOutTime())
            .eqIfPresent(PrescribeDO::getPrisonId, reqVO.getPrisonId())
            .eqIfPresent(PrescribeDO::getPrescribeMedicineType, reqVO.getPrescribeMedicineType())
            .betweenIfPresent(PrescribeDO::getStartPrescribeDate, reqVO.getStartPrescribeDate())
            .betweenIfPresent(PrescribeDO::getEndPrescribeDate, reqVO.getEndPrescribeDate())
            .eqIfPresent(PrescribeDO::getCheckupNum, reqVO.getCheckupNum())
        .orderByDesc(PrescribeDO::getId));    }


    List<String> listPrisonerOutPrescribe(@Param("filterPrescribeStatusList") List<String> filterPrescribeStatusList);

    public List<PrescribeRespVO> getPrescribeList(@Param("roomId") String roomId);

    List<PrescribeRespVO> getPrescribeListByJgrybm(@Param("jgrybm") String jgrybm,@Param("doctorAdviceType") String doctorAdviceType);
}
