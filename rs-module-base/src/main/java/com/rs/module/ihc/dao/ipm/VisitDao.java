package com.rs.module.ihc.dao.ipm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.base.entity.RyReqVO;
import com.rs.module.ihc.controller.admin.ipm.vo.VisitListReqVO;
import com.rs.module.ihc.controller.admin.ipm.vo.VisitPageReqVO;
import com.rs.module.ihc.controller.admin.ipm.vo.VisitRespVO;
import com.rs.module.ihc.controller.admin.ipm.vo.VisitXzjgRespVO;
import com.rs.module.ihc.entity.ipm.VisitDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 所内就医-现场巡检 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface VisitDao extends IBaseDao<VisitDO> {


    default PageResult<VisitDO> selectPage(VisitPageReqVO reqVO) {
        Page<VisitDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<VisitDO> visitPage = selectPage(page, new LambdaQueryWrapperX<VisitDO>()
                .eqIfPresent(VisitDO::getSource, reqVO.getSource())
                .eqIfPresent(VisitDO::getAppointmentId, reqVO.getAppointmentId())
                .eqIfPresent(VisitDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(VisitDO::getRybh, reqVO.getRybh())
                .eqIfPresent(VisitDO::getRyxm, reqVO.getRyxm())
                .eqIfPresent(VisitDO::getSickType, reqVO.getSickType())
                .betweenIfPresent(VisitDO::getDiseaseTime, reqVO.getDiseaseTime())
                .eqIfPresent(VisitDO::getDiseaseReason, reqVO.getDiseaseReason())
                .eqIfPresent(VisitDO::getMainComplaint, reqVO.getMainComplaint())
                .eqIfPresent(VisitDO::getVisitState, reqVO.getVisitState())
                .eqIfPresent(VisitDO::getVisitProcessMethod, reqVO.getVisitProcessMethod())
                .betweenIfPresent(VisitDO::getVisitTime, reqVO.getVisitTime())
                .eqIfPresent(VisitDO::getVisitUserid, reqVO.getVisitUserid())
                .likeIfPresent(VisitDO::getVisitUserName, reqVO.getVisitUserName())
                .eqIfPresent(VisitDO::getVisitConclusion, reqVO.getVisitConclusion())
                .eqIfPresent(VisitDO::getPrisonId, reqVO.getPrisonId())
                .eqIfPresent(VisitDO::getToolUseAdvise, reqVO.getToolUseAdvise())
                .eqIfPresent(VisitDO::getToolUseCancelReason, reqVO.getToolUseCancelReason())
                .eqIfPresent(VisitDO::getExtId, reqVO.getExtId())
                .orderByDesc(VisitDO::getId));
        return new PageResult<>(visitPage.getRecords(), visitPage.getTotal());
    }

    default List<VisitDO> selectList(VisitListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<VisitDO>()
                .eqIfPresent(VisitDO::getSource, reqVO.getSource())
                .eqIfPresent(VisitDO::getAppointmentId, reqVO.getAppointmentId())
                .eqIfPresent(VisitDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(VisitDO::getRybh, reqVO.getRybh())
                .eqIfPresent(VisitDO::getRyxm, reqVO.getRyxm())
                .eqIfPresent(VisitDO::getSickType, reqVO.getSickType())
                .betweenIfPresent(VisitDO::getDiseaseTime, reqVO.getDiseaseTime())
                .eqIfPresent(VisitDO::getDiseaseReason, reqVO.getDiseaseReason())
                .eqIfPresent(VisitDO::getMainComplaint, reqVO.getMainComplaint())
                .eqIfPresent(VisitDO::getVisitState, reqVO.getVisitState())
                .eqIfPresent(VisitDO::getVisitProcessMethod, reqVO.getVisitProcessMethod())
                .betweenIfPresent(VisitDO::getVisitTime, reqVO.getVisitTime())
                .eqIfPresent(VisitDO::getVisitUserid, reqVO.getVisitUserid())
                .likeIfPresent(VisitDO::getVisitUserName, reqVO.getVisitUserName())
                .eqIfPresent(VisitDO::getVisitConclusion, reqVO.getVisitConclusion())
                .eqIfPresent(VisitDO::getPrisonId, reqVO.getPrisonId())
                .eqIfPresent(VisitDO::getToolUseAdvise, reqVO.getToolUseAdvise())
                .eqIfPresent(VisitDO::getToolUseCancelReason, reqVO.getToolUseCancelReason())
                .eqIfPresent(VisitDO::getExtId, reqVO.getExtId())
                .orderByDesc(VisitDO::getId));
    }


    List<VisitRespVO> getVisitTodo(@Param("ry") RyReqVO ry);

    List<VisitXzjgRespVO> listXzjgByRoomId(@Param("type") String type, @Param("roomId") String roomId);

    Integer countEquipmentUseByJgrybm(@Param("jgrybm") String jgrybm);

    List<VisitDO> findDiseaseTimeDescInId(@Param("ids") String[] ids);
}
