package com.rs.module.ihc.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.pm.MedicineCodeDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 药房管理-国家药品编码本位码信息 Dao
*
* <AUTHOR>
*/
@Mapper
public interface MedicineCodeDao extends IBaseDao<MedicineCodeDO> {


    default PageResult<MedicineCodeDO> selectPage(MedicineCodePageReqVO reqVO) {
        Page<MedicineCodeDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<MedicineCodeDO> medicineCodePage = selectPage(page, new LambdaQueryWrapperX<MedicineCodeDO>()
            .eqIfPresent(MedicineCodeDO::getOriginType, reqVO.getOriginType())
            .eqIfPresent(MedicineCodeDO::getApprovalNum, reqVO.getApprovalNum())
            .likeIfPresent(MedicineCodeDO::getMedicineName, reqVO.getMedicineName())
            .eqIfPresent(MedicineCodeDO::getDosageForm, reqVO.getDosageForm())
            .eqIfPresent(MedicineCodeDO::getSpecs, reqVO.getSpecs())
            .eqIfPresent(MedicineCodeDO::getMarketApprovalHolder, reqVO.getMarketApprovalHolder())
            .eqIfPresent(MedicineCodeDO::getMarketApprovalHolderEn, reqVO.getMarketApprovalHolderEn())
            .eqIfPresent(MedicineCodeDO::getProductUnit, reqVO.getProductUnit())
            .eqIfPresent(MedicineCodeDO::getProductUnitEn, reqVO.getProductUnitEn())
            .eqIfPresent(MedicineCodeDO::getMedicineStandardCode, reqVO.getMedicineStandardCode())
            .eqIfPresent(MedicineCodeDO::getMedicineStandardCodeRemark, reqVO.getMedicineStandardCodeRemark())
            .eqIfPresent(MedicineCodeDO::getPrisonId, reqVO.getPrisonId())
            .eqIfPresent(MedicineCodeDO::getZczh, reqVO.getZczh())
            .orderByDesc(MedicineCodeDO::getId));
            return new PageResult<>(medicineCodePage.getRecords(), medicineCodePage.getTotal());
    }
    default List<MedicineCodeDO> selectList(MedicineCodeListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MedicineCodeDO>()
            .eqIfPresent(MedicineCodeDO::getOriginType, reqVO.getOriginType())
            .eqIfPresent(MedicineCodeDO::getApprovalNum, reqVO.getApprovalNum())
            .likeIfPresent(MedicineCodeDO::getMedicineName, reqVO.getMedicineName())
            .eqIfPresent(MedicineCodeDO::getDosageForm, reqVO.getDosageForm())
            .eqIfPresent(MedicineCodeDO::getSpecs, reqVO.getSpecs())
            .eqIfPresent(MedicineCodeDO::getMarketApprovalHolder, reqVO.getMarketApprovalHolder())
            .eqIfPresent(MedicineCodeDO::getMarketApprovalHolderEn, reqVO.getMarketApprovalHolderEn())
            .eqIfPresent(MedicineCodeDO::getProductUnit, reqVO.getProductUnit())
            .eqIfPresent(MedicineCodeDO::getProductUnitEn, reqVO.getProductUnitEn())
            .eqIfPresent(MedicineCodeDO::getMedicineStandardCode, reqVO.getMedicineStandardCode())
            .eqIfPresent(MedicineCodeDO::getMedicineStandardCodeRemark, reqVO.getMedicineStandardCodeRemark())
            .eqIfPresent(MedicineCodeDO::getPrisonId, reqVO.getPrisonId())
            .eqIfPresent(MedicineCodeDO::getZczh, reqVO.getZczh())
        .orderByDesc(MedicineCodeDO::getId));    }


    }
