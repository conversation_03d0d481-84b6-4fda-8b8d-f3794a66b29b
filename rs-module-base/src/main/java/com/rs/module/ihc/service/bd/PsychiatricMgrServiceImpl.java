package com.rs.module.ihc.service.bd;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.bd.vo.PsychiatricMgrSaveReqVO;
import com.rs.module.ihc.dao.bd.PsychiatricMgrDao;
import com.rs.module.ihc.entity.bd.PsychiatricMgrDO;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;


/**
 * 精神病异常管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PsychiatricMgrServiceImpl extends BaseServiceImpl<PsychiatricMgrDao, PsychiatricMgrDO> implements PsychiatricMgrService {

    @Resource
    private PsychiatricMgrDao psychiatricMgrDao;

    @Override
    public String createPsychiatricMgr(PsychiatricMgrSaveReqVO createReqVO) {
        // 插入
        PsychiatricMgrDO psychiatricMgr = BeanUtils.toBean(createReqVO, PsychiatricMgrDO.class);

        SessionUser user = SessionUserUtil.getSessionUser();
        psychiatricMgr.setOperatePoliceSfzh(user.getIdCard());
        psychiatricMgr.setOperatePolice(user.getName());
        psychiatricMgr.setOperateTime(new Date());
        psychiatricMgr.setStatus("1");

        Integer count =  psychiatricMgrDao.selectCount(new LambdaQueryWrapper<PsychiatricMgrDO>()
                .eq(PsychiatricMgrDO::getStatus, "1").eq(PsychiatricMgrDO::getJgrybm, psychiatricMgr.getJgrybm()));
        if(count > 0){
            throw new RuntimeException("该人员已被列管，不可重复登记。");
        }
        psychiatricMgrDao.insert(psychiatricMgr);


        // 返回
        return psychiatricMgr.getId();
    }



    @Override
    public PsychiatricMgrDO getPsychiatricMgr(String id) {
        return psychiatricMgrDao.selectById(id);
    }

    @Override
    public void releasePerson(String id) {
        PsychiatricMgrDO mgrDO = getPsychiatricMgr(id);
        Assert.notNull(mgrDO, "传入ID有误");
        mgrDO.setStatus("0");
        psychiatricMgrDao.updateById(mgrDO);
    }

}
