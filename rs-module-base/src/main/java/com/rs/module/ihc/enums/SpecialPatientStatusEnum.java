package com.rs.module.ihc.enums;


import lombok.Getter;

/**
 * 重特病号状态枚举
 */
@Getter
public enum SpecialPatientStatusEnum {

    /**
     * 已列管
     */
    LISTED("1", "已列管"),

    /**
     * 已解除
     */
    UNLISTED("2", "已解除");

    private final String code;
    private final String description;

    SpecialPatientStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据code获取对应的枚举值
     *
     * @param code 状态码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static SpecialPatientStatusEnum fromCode(String code) {
        for (SpecialPatientStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}

