package com.rs.module.ihc.service.ipm;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.VisitPlanDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.ipm.VisitPlanDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 所内就医-现场巡诊计划 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class VisitPlanServiceImpl extends BaseServiceImpl<VisitPlanDao, VisitPlanDO> implements VisitPlanService {

    @Resource
    private VisitPlanDao visitPlanDao;

    @Override
    public String createVisitPlan(VisitPlanSaveReqVO createReqVO) {
        // 插入
        VisitPlanDO visitPlan = BeanUtils.toBean(createReqVO, VisitPlanDO.class);
        visitPlanDao.insert(visitPlan);
        // 返回
        return visitPlan.getId();
    }

    @Override
    public void updateVisitPlan(VisitPlanSaveReqVO updateReqVO) {
        // 校验存在
        validateVisitPlanExists(updateReqVO.getId());
        // 更新
        VisitPlanDO updateObj = BeanUtils.toBean(updateReqVO, VisitPlanDO.class);
        visitPlanDao.updateById(updateObj);
    }

    @Override
    public void deleteVisitPlan(String id) {
        // 校验存在
        validateVisitPlanExists(id);
        // 删除
        visitPlanDao.deleteById(id);
    }

    private void validateVisitPlanExists(String id) {
        if (visitPlanDao.selectById(id) == null) {
            throw new ServerException("所内就医-现场巡诊计划数据不存在");
        }
    }

    @Override
    public VisitPlanDO getVisitPlan(String id) {
        return visitPlanDao.selectById(id);
    }

    @Override
    public PageResult<VisitPlanDO> getVisitPlanPage(VisitPlanPageReqVO pageReqVO) {
        return visitPlanDao.selectPage(pageReqVO);
    }

    @Override
    public List<VisitPlanDO> getVisitPlanList(VisitPlanListReqVO listReqVO) {
        return visitPlanDao.selectList(listReqVO);
    }


}
