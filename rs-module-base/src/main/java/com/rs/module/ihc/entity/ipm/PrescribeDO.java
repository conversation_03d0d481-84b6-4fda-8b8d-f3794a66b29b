package com.rs.module.ihc.entity.ipm;

import lombok.*;
import java.util.*;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 所内就医-处方 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_ipm_prescribe")
@KeySequence("ihc_ipm_prescribe_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrescribeDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 处方类型（1：所内就医远程问诊处方， 2：所内就医现场巡诊处方）
     */
    private Integer prescribeType;
    /**
     * 业务id
     */
    private String businessId;
    /**
     * 医嘱编号
     */
    private String doctorAdviceNum;
    /**
     * 医嘱类型
     */
    private String doctorAdviceType;
    /**
     * 主诉
     */
    private String mainComplaint;
    /**
     * 病史
     */
    private String medicalHistory;
    /**
     * 体格检查
     */
    private String physicalCheck;
    /**
     * 辅助检查
     */
    private String auxiliaryCheck;
    /**
     * 初步诊断
     */
    private String primaryDiagnosis;
    /**
     * 处理意见
     */
    private String suggestion;
    /**
     * 处方编号
     */
    private String prescribeNum;
    /**
     * 医嘱
     */
    private String doctorAdvice;
    /**
     * 处方说明
     */
    private String prescribeDescribe;
    /**
     * 开处方时间
     */
    private Date prescribeTime;
    /**
     * 开处方人，对应permission_user.userid
     */
    private String prescribeUserid;
    /**
     * 开处方人名称
     */
    private String prescribeUserName;
    /**
     * 处方状态
     */
    private String prescribeStatus;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 在押人员编号
     */
    private String rybh;
    /**
     * 在押人员名称
     */
    private String ryxm;
    /**
     * 发药次数/医嘱执行次数
     */
    private Integer dispenseNum;
    /**
     * 最新发药时间/医嘱执行时间
     */
    private Date lastDispenseTime;
    /**
     * 服药次数
     */
    private Integer doseNum;
    /**
     * 处方药品出库次数
     */
    private Integer outNum;
    /**
     * 处方药品最后一次出库时间
     */
    private Date lastOutTime;
    /**
     * 监所id
     */
    private String prisonId;
    /**
     * 处方药品类型 1：西药 2：中药 。。。字典类型值 internal_prescribe_medicine_type
     */
    private String prescribeMedicineType;
    /**
     * 医嘱/处方 开始日期
     */
    private Date startPrescribeDate;
    /**
     * 医嘱/处方 结束日期
     */
    private Date endPrescribeDate;
    /**
     * 检查次数
     */
    private Integer checkupNum;

}
