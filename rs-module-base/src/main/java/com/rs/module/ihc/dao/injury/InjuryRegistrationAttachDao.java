package com.rs.module.ihc.dao.injury;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.injury.InjuryRegistrationAttachDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.injury.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 医疗子系统-伤亡登记-附件信息 Dao
*
* <AUTHOR>
*/
@Mapper
public interface InjuryRegistrationAttachDao extends IBaseDao<InjuryRegistrationAttachDO> {


    default PageResult<InjuryRegistrationAttachDO> selectPage(InjuryRegistrationAttachPageReqVO reqVO) {
        Page<InjuryRegistrationAttachDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<InjuryRegistrationAttachDO> wrapper = new LambdaQueryWrapperX<InjuryRegistrationAttachDO>()
            .eqIfPresent(InjuryRegistrationAttachDO::getRegistrationId, reqVO.getRegistrationId())
            .eqIfPresent(InjuryRegistrationAttachDO::getAttUrl, reqVO.getAttUrl())
            .eqIfPresent(InjuryRegistrationAttachDO::getRemark, reqVO.getRemark())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(InjuryRegistrationAttachDO::getAddTime);
        }
        Page<InjuryRegistrationAttachDO> injuryRegistrationAttachPage = selectPage(page, wrapper);
        return new PageResult<>(injuryRegistrationAttachPage.getRecords(), injuryRegistrationAttachPage.getTotal());
    }
    default List<InjuryRegistrationAttachDO> selectList(InjuryRegistrationAttachListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<InjuryRegistrationAttachDO>()
            .eqIfPresent(InjuryRegistrationAttachDO::getRegistrationId, reqVO.getRegistrationId())
            .eqIfPresent(InjuryRegistrationAttachDO::getAttUrl, reqVO.getAttUrl())
            .eqIfPresent(InjuryRegistrationAttachDO::getRemark, reqVO.getRemark())
        .orderByDesc(InjuryRegistrationAttachDO::getAddTime));    }


    }
