package com.rs.module.ihc.service.ipm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.DiseasereportDictDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.ipm.DiseasereportDictDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 所内就医-报病字典管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DiseasereportDictServiceImpl extends BaseServiceImpl<DiseasereportDictDao, DiseasereportDictDO> implements DiseasereportDictService {

    @Resource
    private DiseasereportDictDao diseasereportDictDao;

    @Override
    public String createDiseasereportDict(DiseasereportDictSaveReqVO createReqVO) {
        // 插入
        DiseasereportDictDO diseasereportDict = BeanUtils.toBean(createReqVO, DiseasereportDictDO.class);
        diseasereportDictDao.insert(diseasereportDict);
        // 返回

        return diseasereportDict.getId();
    }

    @Override
    public void updateDiseasereportDict(DiseasereportDictSaveReqVO updateReqVO) {
        // 校验存在
        validateDiseasereportDictExists(updateReqVO.getId());
        // 更新
        DiseasereportDictDO updateObj = BeanUtils.toBean(updateReqVO, DiseasereportDictDO.class);
        diseasereportDictDao.updateById(updateObj);
    }

    @Override
    public void deleteDiseasereportDict(String id) {
        // 校验存在
        validateDiseasereportDictExists(id);
        // 删除
        diseasereportDictDao.deleteById(id);
    }

    private void validateDiseasereportDictExists(String id) {
        if (diseasereportDictDao.selectById(id) == null) {
            throw new ServerException("所内就医-报病字典管理数据不存在");
        }
    }

    @Override
    public DiseasereportDictDO getDiseasereportDict(String id) {
        return diseasereportDictDao.selectById(id);
    }

    @Override
    public PageResult<DiseasereportDictDO> getDiseasereportDictPage(DiseasereportDictPageReqVO pageReqVO) {
        return diseasereportDictDao.selectPage(pageReqVO);
    }

    @Override
    public List<DiseasereportDictDO> getDiseasereportDictList(DiseasereportDictListReqVO listReqVO) {
        return diseasereportDictDao.selectList(listReqVO);
    }

    @Override
    public List<DiseasereportDictDO> getDiseaseType() {
        LambdaQueryWrapper<DiseasereportDictDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.groupBy(DiseasereportDictDO::getDiseaseType);
        queryWrapper.select(DiseasereportDictDO::getDiseaseType);
        return diseasereportDictDao.selectList(queryWrapper);
    }


}
