package com.rs.module.ihc.service.injury;

import cn.hutool.core.collection.CollUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.injury.vo.InjuryRegistrationAttachSaveReqVO;
import com.rs.module.ihc.controller.admin.injury.vo.InjuryRegistrationListReqVO;
import com.rs.module.ihc.controller.admin.injury.vo.InjuryRegistrationPageReqVO;
import com.rs.module.ihc.controller.admin.injury.vo.InjuryRegistrationSaveReqVO;
import com.rs.module.ihc.dao.injury.InjuryRegistrationDao;
import com.rs.module.ihc.entity.injury.InjuryRegistrationAttachDO;
import com.rs.module.ihc.entity.injury.InjuryRegistrationDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 医疗子系统-伤亡登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InjuryRegistrationServiceImpl extends BaseServiceImpl<InjuryRegistrationDao, InjuryRegistrationDO> implements InjuryRegistrationService {

    @Resource
    private InjuryRegistrationDao injuryRegistrationDao;

    @Resource
    private InjuryRegistrationAttachService injuryRegistrationAttachService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createInjuryRegistration(InjuryRegistrationSaveReqVO createReqVO) {
        // 插入
        InjuryRegistrationDO injuryRegistration = BeanUtils.toBean(createReqVO, InjuryRegistrationDO.class);

        SessionUser user = SessionUserUtil.getSessionUser();
        injuryRegistration.setOperatePoliceSfzh(user.getIdCard());
        injuryRegistration.setOperatePolice(user.getName());
        injuryRegistration.setOperateTime(new Date());
        injuryRegistrationDao.insert(injuryRegistration);

        if (CollUtil.isNotEmpty(createReqVO.getAttachList())) {
            List<InjuryRegistrationAttachDO> injuryRegistrationAttachList = new ArrayList<>();
            for (InjuryRegistrationAttachSaveReqVO injuryRegistrationAttachSaveReqVO : createReqVO.getAttachList()) {
                InjuryRegistrationAttachDO registrationAttachDO = BeanUtils.toBean(injuryRegistrationAttachSaveReqVO, InjuryRegistrationAttachDO.class);
                registrationAttachDO.setRegistrationId( injuryRegistration.getId());
                injuryRegistrationAttachList.add(registrationAttachDO);
            }
            injuryRegistrationAttachService.saveBatch(injuryRegistrationAttachList);
        }
        return injuryRegistration.getId();
    }

    private void validateInjuryRegistrationExists(String id) {
        if (injuryRegistrationDao.selectById(id) == null) {
            throw new ServerException("医疗子系统-伤亡登记数据不存在");
        }
    }

    @Override
    public InjuryRegistrationDO getInjuryRegistration(String id) {
        return injuryRegistrationDao.selectById(id);
    }

    @Override
    public PageResult<InjuryRegistrationDO> getInjuryRegistrationPage(InjuryRegistrationPageReqVO pageReqVO) {
        return injuryRegistrationDao.selectPage(pageReqVO);
    }

    @Override
    public List<InjuryRegistrationDO> getInjuryRegistrationList(InjuryRegistrationListReqVO listReqVO) {
        return injuryRegistrationDao.selectList(listReqVO);
    }


}
