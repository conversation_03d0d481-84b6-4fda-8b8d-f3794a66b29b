package com.rs.module.ihc.service.hr;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.hr.vo.*;
import com.rs.module.ihc.entity.hr.PrintdocConfigDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 健康档案-打印文书配置 Service 接口
 *
 * <AUTHOR>
 */
public interface PrintdocConfigService extends IBaseService<PrintdocConfigDO>{

    /**
     * 创建健康档案-打印文书配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrintdocConfig(@Valid PrintdocConfigSaveReqVO createReqVO);

    /**
     * 更新健康档案-打印文书配置
     *
     * @param updateReqVO 更新信息
     */
    void updatePrintdocConfig(@Valid PrintdocConfigSaveReqVO updateReqVO);

    /**
     * 删除健康档案-打印文书配置
     *
     * @param id 编号
     */
    void deletePrintdocConfig(String id);

    /**
     * 获得健康档案-打印文书配置
     *
     * @param id 编号
     * @return 健康档案-打印文书配置
     */
    PrintdocConfigDO getPrintdocConfig(String id);

    /**
    * 获得健康档案-打印文书配置分页
    *
    * @param pageReqVO 分页查询
    * @return 健康档案-打印文书配置分页
    */
    PageResult<PrintdocConfigDO> getPrintdocConfigPage(PrintdocConfigPageReqVO pageReqVO);

    /**
    * 获得健康档案-打印文书配置列表
    *
    * @param listReqVO 查询条件
    * @return 健康档案-打印文书配置列表
    */
    List<PrintdocConfigDO> getPrintdocConfigList(PrintdocConfigListReqVO listReqVO);


}
