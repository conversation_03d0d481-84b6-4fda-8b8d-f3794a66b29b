package com.rs.module.ihc.controller.admin.pm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 药房管理-药房信息新增/修改 Request VO")
@Data
public class PharmacySaveReqVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("药房名称")
    @NotEmpty(message = "药房名称不能为空")
    private String pharmacyName;

    @ApiModelProperty("监所id")
    private String prisonId;

}
