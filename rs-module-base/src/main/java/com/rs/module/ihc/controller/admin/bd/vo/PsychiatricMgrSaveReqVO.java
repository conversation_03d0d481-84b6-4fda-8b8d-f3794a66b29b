package com.rs.module.ihc.controller.admin.bd.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "管理后台 - 精神病异常管理新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PsychiatricMgrSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("会诊初步诊断 字典ZD_JSB_HZCBZD")
    @NotEmpty(message = "会诊初步诊断 字典ZD_JSB_HZCBZD不能为空")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JSB_HZCBZD")
    private String cnsltPreDiag;


    @ApiModelProperty("被监管人员编码")
    @NotBlank(message = "监管人员编码，不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotBlank(message = "监管人员姓名，不能为空")
    private String jgryxm;

    @ApiModelProperty("治疗后是否好转 1是0否")
    @NotEmpty(message = "治疗后是否好转 1是0否不能为空")
    private String treatmentOutcome;

    @ApiModelProperty("列管等级  字典：ZD_JSB_LGDJ")
    @NotEmpty(message = "列管等级  字典：ZD_JSB_LGDJ不能为空")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JSB_LGDJ")
    private String managementLevel;

    @ApiModelProperty("列控时间")
    private Date controlTime;

    @ApiModelProperty("列控原因")
    private String controlReason;

    //@ApiModelProperty("登记民警身份证号")
    //@NotEmpty(message = "登记民警身份证号不能为空")
    //private String operatePoliceSfzh;
    //
    //@ApiModelProperty("登记民警")
    //@NotEmpty(message = "登记民警不能为空")
    //private String operatePolice;
    //
    //@ApiModelProperty("登记时间")
    //@NotNull(message = "登记时间不能为空")
    //private Date operateTime;

}
