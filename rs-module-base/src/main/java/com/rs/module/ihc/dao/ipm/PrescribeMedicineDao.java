package com.rs.module.ihc.dao.ipm;

import java.util.*;

import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.ihc.entity.ipm.PrescribeMedicineDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 所内就医-处方-药方信息
 *
 * <AUTHOR>
 */
@Mapper
public interface PrescribeMedicineDao extends IBaseDao<PrescribeMedicineDO> {

    default List<PrescribeMedicineDO> selectListByMlh(String mlh) {
        return selectList(new LambdaQueryWrapperX<PrescribeMedicineDO>().eq(PrescribeMedicineDO::getMlh, mlh));
    }

    default int deleteByMlh(String mlh) {
        return delete(new LambdaQueryWrapperX<PrescribeMedicineDO>().eq(PrescribeMedicineDO::getMlh, mlh));
    }

}
