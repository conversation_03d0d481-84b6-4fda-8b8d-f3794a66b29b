package com.rs.module.ihc.controller.admin.ephe.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 卫生防疫与健康教育-健康教育分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HealthEduPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("宣教课程")
    private String xjkc;

    @ApiModelProperty("健康教育情况")
    private String jkjyqk;

    @ApiModelProperty("宣教范围")
    private String cjfw;

    @ApiModelProperty("宣教时段开始时间")
    private Date xjsdKssj;

    @ApiModelProperty("宣教时段结束时间")
    private Date xjsdJssj;

    @ApiModelProperty("宣教人姓名")
    private String xjrxm;

    @ApiModelProperty("登记民警身份证号")
    private String operatePoliceSfzh;

    @ApiModelProperty("登记民警")
    private String operatePolice;

    @ApiModelProperty("登记时间")
    private Date[] operateTime;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
