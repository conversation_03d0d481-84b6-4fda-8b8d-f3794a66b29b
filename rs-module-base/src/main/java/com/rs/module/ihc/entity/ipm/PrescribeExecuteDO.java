package com.rs.module.ihc.entity.ipm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import java.util.Date;

/**
 * 所内就医-医嘱/处方执行记录 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_ipm_prescribe_execute")
@KeySequence("ihc_ipm_prescribe_execute_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrescribeExecuteDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;


    /**
     * 处方id，对应ihc_ipm_prescribe.id
     */
    private String prescribeId;
    /**
     * 签名图片地址
     */
    private String signPicUrl;
    /**
     * 服药视频url
     */
    private String doseVideoUrl;
    /**
     * 签名状态 0=未签名 1=已签名 字典值
     */
    private String signStatus;
    /**
     * 签名时间
     */
    private Date signTime;
    /**
     * 服药状态 0=未服药 1=已服药 2=拒绝服药 字典：dose_status
     */
    private String doseStatus;
    /**
     * 服药时间
     */
    private Date doseTime;
    /**
     * 医嘱
     */
    private String doctorAdvice;
    /**
     * 处方说明
     */
    private String prescribeDescribe;
    /**
     * 医嘱/处方执行发药来源 1：监室送药  2：临床送药 字典值 internal_medical_prescribe_execute_source
     */
    private String executeSource;
    /**
     * 医务民警id
     */
    private String medicalPoliceSfzh;
    /**
     * 医务民警名称
     */
    private String medicalPoliceName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 主管民警id
     */
    private String managePoliceSfzh;
    /**
     * 主管民警名称
     */
    private String managePoliceName;
    /**
     * 协管民警ids，多个逗号分隔
     */
    private String assistPoliceSfzhs;
    /**
     * 协管民警名称，多个逗号分隔
     */
    private String assistPoliceNames;

    /**
     * 患者意见
     */
    private String hzyj;
    /**
     * 捺印url
     */
    private String nyUrl;
    /**
     * 捺印时间
     */
    private Date nyTime;

    /**
     * 捺印后的告知书url
     */
    private String fxgzsUrl;

}
