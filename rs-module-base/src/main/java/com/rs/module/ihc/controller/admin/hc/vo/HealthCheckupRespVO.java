package com.rs.module.ihc.controller.admin.hc.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 五项体检 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class HealthCheckupRespVO extends BaseVO implements TransPojo{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("被监管人员编号")
    private String jgrybm;

    @ApiModelProperty("体检状态")
    private String checkupStatus;

    @ApiModelProperty("体检时间")
    private Date checkupTime;

    @ApiModelProperty("病史")
    private String medicalHistory;

    @ApiModelProperty("药物过敏")
    private String drugAllergy;

    @ApiModelProperty("家族病史")
    private String familyMedicalHistory;

    @ApiModelProperty("目前身体状况")
    private String healthStatus;

    @ApiModelProperty("血压(高压)")
    private Double bloodPressureHeight;

    @ApiModelProperty("血压(低压)")
    private Double bloodPressureLow;

    @ApiModelProperty("脉搏")
    private Double pulse;

    @ApiModelProperty("常规检查")
    private String routineExamination;

    @ApiModelProperty("血常规")
    private String bloodRoutineExamination;

    @ApiModelProperty("B超")
    private String bUltrasonics;

    @ApiModelProperty("肝、胆、脾、胰")
    private String fiveInternalOrgans;

    @ApiModelProperty("双肾、输尿管、膀胱")
    private String urinarySystem;

    @ApiModelProperty("心电图")
    private String electrocardiogram;

    @ApiModelProperty("常规心电图")
    private String routineElectrocardiogram;

    @ApiModelProperty("X光")
    private String xRay;

    @ApiModelProperty("胸部")
    private String chest;

    @ApiModelProperty("检查结论")
    private String checkConclusion;

    @ApiModelProperty("医生")
    private String checkupDoctor;

    @ApiModelProperty("医生名称")
    private String checkupDoctorName;

    @ApiModelProperty("录入时间")
    private Date enterTime;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty(" 被监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("附件信息")
    private List<HealthCheckupFileRespVO> fileList;
}
