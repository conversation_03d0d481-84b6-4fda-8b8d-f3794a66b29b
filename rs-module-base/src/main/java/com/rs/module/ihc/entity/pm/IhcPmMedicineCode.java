package com.rs.module.ihc.entity.pm;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.module.ihc.entity.base.BaseEntity;
import io.swagger.annotations.ApiModel;

import java.io.Serializable;

@ApiModel
@TableName("ihc_pm_medicine_code")
public class IhcPmMedicineCode extends BaseEntity implements Serializable {

    @TableId("id")
    private String id;

    @TableField("is_del")
    private String isDel;

    /**
     * 药品批准文号
     */
    @TableField("approval_num")
    private String approvalNum;

    /**
     * 产品名称
     */
    @TableField("medicine_name")
    private String medicineName;

    /**
     * 剂型
     */
    @TableField("dosage_form")
    private String dosageForm;

    /**
     * 规格
     */
    @TableField("specs")
    private String specs;

    /**
     * 上市许可持有人
     */
    @TableField("market_approval_holder")
    private String marketApprovalHolder;

    /**
     * 上市许可持有人英文
     */
    @TableField("market_approval_holder_en")
    private String marketApprovalHolderEn;

    /**
     * 生产单位
     */
    @TableField("product_unit")
    private String productUnit;

    /**
     * 生产单位英文
     */
    @TableField("product_unit_en")
    private String productUnitEn;

    /**
     * 药品本位码
     */
    @TableField("medicine_standard_code")
    private String medicineStandardCode;

    /**
     * 药品本位编码备注
     */
    @TableField("medicine_standard_code_remark")
    private String medicineStandardCodeRemark;

    @TableField("zczh")
    private String zczh;

    @TableField("origin_type")
    private String originType;

    @TableField(exist = false)
    private Boolean isUpdate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIsDel() {
        return isDel;
    }

    public void setIsDel(String isDel) {
        this.isDel = isDel;
    }

    public String getApprovalNum() {
        return approvalNum;
    }

    public void setApprovalNum(String approvalNum) {
        this.approvalNum = approvalNum;
    }

    public String getDosageForm() {
        return dosageForm;
    }

    public void setDosageForm(String dosageForm) {
        this.dosageForm = dosageForm;
    }

    public String getSpecs() {
        return specs;
    }

    public void setSpecs(String specs) {
        this.specs = specs;
    }

    public String getMarketApprovalHolder() {
        return marketApprovalHolder;
    }

    public void setMarketApprovalHolder(String marketApprovalHolder) {
        this.marketApprovalHolder = marketApprovalHolder;
    }

    public String getMarketApprovalHolderEn() {
        return marketApprovalHolderEn;
    }

    public void setMarketApprovalHolderEn(String marketApprovalHolderEn) {
        this.marketApprovalHolderEn = marketApprovalHolderEn;
    }

    public String getProductUnit() {
        return productUnit;
    }

    public void setProductUnit(String productUnit) {
        this.productUnit = productUnit;
    }

    public String getProductUnitEn() {
        return productUnitEn;
    }

    public void setProductUnitEn(String productUnitEn) {
        this.productUnitEn = productUnitEn;
    }

    public String getMedicineStandardCode() {
        return medicineStandardCode;
    }

    public void setMedicineStandardCode(String medicineStandardCode) {
        this.medicineStandardCode = medicineStandardCode;
    }

    public String getMedicineStandardCodeRemark() {
        return medicineStandardCodeRemark;
    }

    public void setMedicineStandardCodeRemark(String medicineStandardCodeRemark) {
        this.medicineStandardCodeRemark = medicineStandardCodeRemark;
    }

    public Boolean getUpdate() {
        return isUpdate;
    }

    public void setUpdate(Boolean update) {
        isUpdate = update;
    }

    public String getZczh() {
        return zczh;
    }

    public void setZczh(String zczh) {
        this.zczh = zczh;
    }

    public String getMedicineName() {
        return medicineName;
    }

    public void setMedicineName(String medicineName) {
        this.medicineName = medicineName;
    }

    public String getOriginType() {
        return originType;
    }

    public void setOriginType(String originType) {
        this.originType = originType;
    }
}
