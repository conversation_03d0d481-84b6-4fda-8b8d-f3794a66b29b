package com.rs.module.ihc.controller.admin.ipm.appointment.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class AuditMedicalAppointmentDTO {

    /**
     * 主键id
     */
    @NotNull(message = "预约信息id不能为空")
    @ApiModelProperty(value = "主键id", required = true)
    private String id;

    /**
     * 初步诊断
     */
    @ApiModelProperty("初步诊断")
    private String primaryDiagnosis;

    /**
     * 病情等级 字典值
     */
    @ApiModelProperty(value = "病情等级 字典值", required = true)
    private String diseaseLevel;

    /**
     * 处理方式/预约审核结果 字典值
     */
    @NotBlank(message = "处理方式/预约审核结果不能为空")
    @ApiModelProperty(value = "处理方式/预约审核结果 字典值", required = true)
    private String processMethod;

}
