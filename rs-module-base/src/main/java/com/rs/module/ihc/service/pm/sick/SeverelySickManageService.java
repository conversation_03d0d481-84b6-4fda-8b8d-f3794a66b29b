package com.rs.module.ihc.service.pm.sick;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.ihc.controller.admin.pm.sick.vo.SeverelySickManageListReqVO;
import com.rs.module.ihc.controller.admin.pm.sick.vo.SeverelySickManagePageReqVO;
import com.rs.module.ihc.controller.admin.pm.sick.vo.SeverelySickManageSaveReqVO;
import com.rs.module.ihc.entity.pm.sick.SeverelySickManageDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 重特病号管理 Service 接口
 *
 * <AUTHOR>
 */
public interface SeverelySickManageService extends IBaseService<SeverelySickManageDO>{

    /**
     * 创建重特病号管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createSeverelySickManage(@Valid SeverelySickManageSaveReqVO createReqVO);

    /**
     * 更新重特病号管理
     *
     * @param updateReqVO 更新信息
     */
    void updateSeverelySickManage( SeverelySickManageSaveReqVO updateReqVO);

    /**
     * 删除重特病号管理
     *
     * @param id 编号
     */
    void deleteSeverelySickManage(String id);

    /**
     * 获得重特病号管理
     *
     * @param id 编号
     * @return 重特病号管理
     */
    SeverelySickManageDO getSeverelySickManage(String id);

    /**
    * 获得重特病号管理分页
    *
    * @param pageReqVO 分页查询
    * @return 重特病号管理分页
    */
    PageResult<SeverelySickManageDO> getSeverelySickManagePage(SeverelySickManagePageReqVO pageReqVO);

    /**
    * 获得重特病号管理列表
    *
    * @param listReqVO 查询条件
    * @return 重特病号管理列表
    */
    List<SeverelySickManageDO> getSeverelySickManageList(SeverelySickManageListReqVO listReqVO);


}
