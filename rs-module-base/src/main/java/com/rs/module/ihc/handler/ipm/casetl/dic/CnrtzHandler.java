package com.rs.module.ihc.handler.ipm.casetl.dic;

import com.rs.framework.common.handler.AbstractHandler;
import com.rs.module.ihc.controller.admin.ipm.vo.CaseTemplateDicRespV2VO;
import com.rs.module.ihc.controller.admin.ipm.vo.CaseTemplateDicRespVO;
import org.springframework.stereotype.Component;

/**
 * @ClassName CnrtzHandler
 * @Description 体重-成年人
 * <AUTHOR>
 * @Date 2025/3/21 17:13
 * @Version 1.0
 */
@Component
public class CnrtzHandler extends AbstractHandler<CaseTemplateDicRespV2VO> {
    @Override
    protected CaseTemplateDicRespV2VO doHandle(CaseTemplateDicRespV2VO input) {
        //体重45至100
        for (int i = 45; i <= 100; i++) {
            CaseTemplateDicRespVO caseTemplateDicRespVO = new CaseTemplateDicRespVO();
            caseTemplateDicRespVO.setLable(String.valueOf(i));
            input.getCnrtz().add(caseTemplateDicRespVO);
        }
        return input;
    }
}
