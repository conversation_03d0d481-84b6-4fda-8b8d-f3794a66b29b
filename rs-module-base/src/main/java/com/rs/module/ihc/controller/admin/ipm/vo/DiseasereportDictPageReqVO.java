package com.rs.module.ihc.controller.admin.ipm.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;


@ApiModel(description = "管理后台 - 所内就医-报病字典管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DiseasereportDictPageReqVO extends PageParam {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("疾病类型（字典：报病字典 ZD_BBZD）")
    private String diseaseType;

    @ApiModelProperty("疾病症状")
    private String diseaseSymptom;

    @ApiModelProperty("是否内置")
    private String isBuiltIn;

    @ApiModelProperty("排序属性")
    List<OrderItem> orderFields;
}
