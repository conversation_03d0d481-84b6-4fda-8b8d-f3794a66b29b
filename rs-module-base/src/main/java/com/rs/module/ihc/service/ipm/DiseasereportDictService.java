package com.rs.module.ihc.service.ipm;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.DiseasereportDictDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 所内就医-报病字典管理 Service 接口
 *
 * <AUTHOR>
 */
public interface DiseasereportDictService extends IBaseService<DiseasereportDictDO>{

    /**
     * 创建所内就医-报病字典管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createDiseasereportDict(@Valid DiseasereportDictSaveReqVO createReqVO);

    /**
     * 更新所内就医-报病字典管理
     *
     * @param updateReqVO 更新信息
     */
    void updateDiseasereportDict(@Valid DiseasereportDictSaveReqVO updateReqVO);

    /**
     * 删除所内就医-报病字典管理
     *
     * @param id 编号
     */
    void deleteDiseasereportDict(String id);

    /**
     * 获得所内就医-报病字典管理
     *
     * @param id 编号
     * @return 所内就医-报病字典管理
     */
    DiseasereportDictDO getDiseasereportDict(String id);

    /**
    * 获得所内就医-报病字典管理分页
    *
    * @param pageReqVO 分页查询
    * @return 所内就医-报病字典管理分页
    */
    PageResult<DiseasereportDictDO> getDiseasereportDictPage(DiseasereportDictPageReqVO pageReqVO);

    /**
    * 获得所内就医-报病字典管理列表
    *
    * @param listReqVO 查询条件
    * @return 所内就医-报病字典管理列表
    */
    List<DiseasereportDictDO> getDiseasereportDictList(DiseasereportDictListReqVO listReqVO);


    /**
     * 获取疾病类型
     */
    List<DiseasereportDictDO> getDiseaseType();

}
