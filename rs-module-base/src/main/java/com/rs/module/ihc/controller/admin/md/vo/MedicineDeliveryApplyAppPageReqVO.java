package com.rs.module.ihc.controller.admin.md.vo;

import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@ApiModel(description = "管理后台 - ihc- 药品顾送APP查询")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MedicineDeliveryApplyAppPageReqVO extends PageParam{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("监室id")
    private String roomId;

    @ApiModelProperty(value = "机构代码", hidden = true)
    private String orgCode;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty(value = "申请-开始时间", required = true)
    private Date startTime;

    @ApiModelProperty(value = "申请-结束时间", required = true)
    private Date endTime;

    @ApiModelProperty("类型 type为空查询全部   1=今天  2=昨天  3=近一周")
    private Integer type;

}
