package com.rs.module.ihc.dao.bd;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.bd.PsychiatricMgrDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.bd.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 精神病异常管理 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PsychiatricMgrDao extends IBaseDao<PsychiatricMgrDO> {


    default PageResult<PsychiatricMgrDO> selectPage(PsychiatricMgrPageReqVO reqVO) {
        Page<PsychiatricMgrDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PsychiatricMgrDO> wrapper = new LambdaQueryWrapperX<PsychiatricMgrDO>()
            .eqIfPresent(PsychiatricMgrDO::getCnsltPreDiag, reqVO.getCnsltPreDiag())
            .eqIfPresent(PsychiatricMgrDO::getTreatmentOutcome, reqVO.getTreatmentOutcome())
            .eqIfPresent(PsychiatricMgrDO::getManagementLevel, reqVO.getManagementLevel())
            .betweenIfPresent(PsychiatricMgrDO::getControlTime, reqVO.getControlTime())
            .eqIfPresent(PsychiatricMgrDO::getControlReason, reqVO.getControlReason())
            .eqIfPresent(PsychiatricMgrDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(PsychiatricMgrDO::getOperatePolice, reqVO.getOperatePolice())
            .betweenIfPresent(PsychiatricMgrDO::getOperateTime, reqVO.getOperateTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PsychiatricMgrDO::getAddTime);
        }
        Page<PsychiatricMgrDO> psychiatricMgrPage = selectPage(page, wrapper);
        return new PageResult<>(psychiatricMgrPage.getRecords(), psychiatricMgrPage.getTotal());
    }
    default List<PsychiatricMgrDO> selectList(PsychiatricMgrListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PsychiatricMgrDO>()
            .eqIfPresent(PsychiatricMgrDO::getCnsltPreDiag, reqVO.getCnsltPreDiag())
            .eqIfPresent(PsychiatricMgrDO::getTreatmentOutcome, reqVO.getTreatmentOutcome())
            .eqIfPresent(PsychiatricMgrDO::getManagementLevel, reqVO.getManagementLevel())
            .betweenIfPresent(PsychiatricMgrDO::getControlTime, reqVO.getControlTime())
            .eqIfPresent(PsychiatricMgrDO::getControlReason, reqVO.getControlReason())
            .eqIfPresent(PsychiatricMgrDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(PsychiatricMgrDO::getOperatePolice, reqVO.getOperatePolice())
            .betweenIfPresent(PsychiatricMgrDO::getOperateTime, reqVO.getOperateTime())
        .orderByDesc(PsychiatricMgrDO::getAddTime));    }


    }
