package com.rs.module.ihc.controller.admin.ipm.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel(description = "管理后台 - 所内就医-处方药品出库记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrescribeOutPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("添加人姓名")
    private String addUserName;

    @ApiModelProperty("更新人姓名")
    private String updateUserName;

    @ApiModelProperty("处方id，对应ihc_ipm_prescribe.id")
    private String prescribeId;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("医嘱")
    private String doctorAdvice;

    @ApiModelProperty("处方说明")
    private String prescribeDescribe;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
