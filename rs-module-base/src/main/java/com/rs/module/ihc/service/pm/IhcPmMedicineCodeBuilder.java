package com.rs.module.ihc.service.pm;

import com.rs.module.ihc.controller.admin.pm.vo.ValidationResult;

public abstract class IhcPmMedicineCodeBuilder<T> {
    private IhcPmMedicineCodeBuilder<T> next;

    private void next(IhcPmMedicineCodeBuilder next) {
        this.next = next;
    }

    public static class Builder<T> {

        private IhcPmMedicineCodeBuilder<T> head;
        private IhcPmMedicineCodeBuilder<T> tail;

        public Builder<T> linkWith(IhcPmMedicineCodeBuilder<T> validator) {
            // 第一次设置head和tail，后面head不再改变，只是不断的扩展tail
            if (this.head == null) {
                this.head = this.tail = validator;
                return this;
            }
            // 不断扩展tail
            this.tail.next(validator);
            this.tail = validator;
            return this;
        }

        public IhcPmMedicineCodeBuilder<T> build() {
            return this.head;

        }
    }

    public abstract ValidationResult importExcel(T toValidate);

    protected ValidationResult checkNext(T toValidate) {
        return next == null ? ValidationResult.success() : next.importExcel(toValidate);
    }
}
