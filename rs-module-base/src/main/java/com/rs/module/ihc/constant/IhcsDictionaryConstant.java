package com.rs.module.ihc.constant;

/**
 * 字典常量表
 */
public interface IhcsDictionaryConstant {

    /**
     * 是否类型
     */
    String BOOLEAN_TYPE = "boolean_type";

    /**
     * 用餐时段类型
     */
    String MEAL_PERIOD = "meal_period";

    /**
     * 周/星期
     */
    String DAY_OF_WEEK = "day_of_week";

    /**
     * 同意/不同意
     *
     * @see IsAgreeConstant
     */
    String IS_AGREE = "is_agree";

    // -------------------------------------------------------------------------------------------------------------------------

    /**
     * 字典类型：性别
     */
    String SEX = "sex";

    /**
     * 实战平台 案件类别/涉嫌罪名
     */
    String C_AJLB = "C_AJLB";

    /**
     * 实战平台 户籍
     */
    String C_HJ = "C_HJ";

    /**
     * 实战平台 诉讼阶段/诉讼环节
     */
    String C_KSS_SSJD = "C_KSS_SSJD";

    /**
     * 实战平台 民族
     */
    String C_MZ = "C_MZ";

    /**
     * 实战平台 婚姻状况
     */
    String C_HYZK = "C_HYZK";

    /**
     * 实战平台 证件类型
     */
    String C_ZJLX = "C_ZJLX";

    /**
     * 实战平台 国籍
     */
    String C_GJ = "C_GJ";

    /**
     * 实战平台 文化程度
     */
    String C_WHCD = "C_WHCD";

    /**
     * 实战平台 政治面貌
     */
    String C_ZZMM = "C_ZZMM";

    /**
     * 实战平台 健康状况
     */
    String C_KSS_JKZK = "C_KSS_JKZK";

    /**
     * 实战平台 身份
     */
    String C_SF = "C_SF";

    /**
     * 实战平台 办案单位类型
     */
    String C_KSS_BADWLX = "C_KSS_BADWLX";

    /**
     * 实战平台 看守所风险等级
     */
    String C_KSS_DANGERLEVEL = "C_KSS_DANGERLEVEL";
    // -------------------------------------------------------------------------------------------------------------------------

    /**
     * 字典中的药品类别字典类型名称
     */
    String MEDICINE_CATEGORY_DICT_TYPE = "medicine_category";

    /**
     * 字典中的药品类别 {@link #MEDICINE_CATEGORY_DICT_TYPE} 为卫材的字典初始父值，即该字典值下的所有的子值都是属于卫材
     */
    String SANITARY_CONSUMABLES_ROOT_DICT_VALUE = "3";

    /**
     * 字典中的药品出库原因，医嘱用药字典，对应字典类型 {@code out_storage_reason}
     */
    String PRESCRIBE_USE_MEDICINE = "1";

    /**
     * 字典类型：所内就医预约审核结果
     */
    String INTERNAL_MEDICAL_APPOINTMENT_PROCESS_METHOD_DICT_TYPE = "internal_medical_appointment_process_method";

    /**
     * 字典类型：病情等级
     */
    String INTERNAL_MEDICAL_APPOINTMENT_DISEASE_LEVEL_DICT_TYPE = "internal_medical_appointment_disease_level";
    /**
     * 字典类型：医嘱类型
     */
    String DOCTOR_ADVICE_TYPE_DICT_TYPE = "doctor_advice_type";
    /**
     * 字典类型：处方状态
     */
    String PRESCRIBE_STATUS_DICT_TYPE = "prescribe_status";

    /**
     * 字典类型：医嘱执行来源/送药类型
     */
    String PRESCRIBE_EXECUTE_STATUS_DICT_TYPE = "internal_medical_prescribe_execute_source";

    /**
     * 字典类型：巡诊处理方式
     */
    String VISIT_PROCESS_METHOD_DICT_TYPE = "visit_process_method";
    /**
     * 字典类型：检查结果登记状态
     */
    String OUTPATIENT_CHECKLIST_STATUS_DICT_TYPE = "outpatient_checklist_status";
    /**
     * 字典类型：检查项目类型
     */
    String OUTPATIENT_CHECKLIST_CATEGORY_DICT_TYPE = "outpatient_checklist_category";
    /**
     * 字典类型：剂型
     */
    String DOSAGE_FORM_DICT_TYPE = "dosage_form";
    /**
     * 字典类型：计量单位
     */
    String MEASUREMENT_UNIT_DICT_TYPE = "measurement_unit";
    /**
     * 字典类型：是否毒麻品
     */
    String IS_DRUG_DICT_TYPE = "is_drug";
    /**
     * 字典类型：报损原因
     */
    String LOSS_REASON_DICT_TYPE = "loss_reason";
    /**
     * 字典类型：盘点类型
     */
    String STOCKTAKING_TYPE_DICT_TYPE = "stocktaking_type";
    /**
     * 字典类型：盘点状态
     */
    String STOCKTAKING_STATUS_DICT_TYPE = "stocktaking_status";
    /**
     * 字典类型：五项体检状态
     */
    String HEALTH_CHECKUP_STATUS_DICT_TYPE = "health_checkup_status";

    /**
     * 字典类型：所情事件来源
     */
    String PRISON_EVENT_PLATFORM_SOURCE = "prison_event_platform_source";

    /**
     * 字典类型：领导审批状态
     */
    String PRISON_EVENT_APPROVAL_STATUS = "prison_event_approval_status";

    /**
     * 字典类型：医生处置状态
     */
    String PRISON_EVENT_DOCTOR_HANDLE_STATUS = "prison_event_doctor_handle_status";

    /**
     * 字典类型：所情事件等级
     */
    String PRISON_EVENT_LEVEL = "prison_event_level";

    /**
     * 字典类型：处置方式
     */
    String PRISON_EVENT_PRISONER_HANDLE_METHOD = "prison_event_prisoner_handle_method";

    /**
     * 字典类型：现场处置方式
     */
    String PRISON_EVENT_PRISONER_SITE_HANDLE_METHOD = "prison_event_prisoner_site_handle_method";

    /**
     * 字典类型：巡诊计划来源
     */
    String INTERNAL_MEDICAL_VISIT_PLAN_SOURCE = "internal_medical_visit_plan_source";

    /**
     * 字典类型：巡诊频率
     */
    String INTERNAL_MEDICAL_VISIT_PLAN_FREQUENCY = "internal_medical_visit_plan_frequency";

    /**
     * 字典类型：巡诊计划状态
     */
    String INTERNAL_MEDICAL_VISIT_PLAN_STATUS = "internal_medical_visit_plan_status";

    /**
     * 字典类型：病例模板类型
     */
    String CASE_TEMPLATE_TYPE = "case_template_type";

    /**
     * 字典类型：处方签名状态
     */
    String INTERNAL_MEDICAL_PRESCRIBE_SIGN_STATUS = "internal_medical_prescribe_sign_status";

    /**
     * 字典类型：服药状态
     */
    String DOSE_STATUS = "dose_status";

    /**
     * 顾送药送药原因
     */
    String CARE_OF_MEDICINE_DELIVERY_REASON = "care_of_medicine_delivery_reason";

    /**
     * 顾送药业务状态
     */
    String CARE_OF_MEDICINE_BUSINESS_STATUS = "care_of_medicine_business_status";

    /**
     * 顾送药审批结果
     */
    String CARE_OF_MEDICINE_APPROVAL_RESULT = "care_of_medicine_approval_result";

    /**
     * 特殊餐申请餐类型
     *
     * @see SpecialMealTypeConstant
     */
    String SPECIAL_MEAL_TYPE = "special_meal_type";

    /**
     * 特殊餐申请业务状态
     *
     * @see SpecialMealBusinessStatusConstant
     */
    String SPECIAL_MEAL_APPLY_BUSINESS_STATUS = "special_meal_apply_business_status";
}
