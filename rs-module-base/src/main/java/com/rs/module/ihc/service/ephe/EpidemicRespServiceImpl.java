package com.rs.module.ihc.service.ephe;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.ephe.vo.EpidemicRespSaveReqVO;
import com.rs.module.ihc.dao.ephe.EpidemicRespDao;
import com.rs.module.ihc.entity.ephe.EpidemicRespDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;


/**
 * 卫生防疫与健康教育-疫情处置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EpidemicRespServiceImpl extends BaseServiceImpl<EpidemicRespDao, EpidemicRespDO> implements EpidemicRespService {

    @Resource
    private EpidemicRespDao epidemicRespDao;

    @Override
    public String createEpidemicResp(EpidemicRespSaveReqVO createReqVO) {
        // 插入
        EpidemicRespDO epidemicResp = BeanUtils.toBean(createReqVO, EpidemicRespDO.class);
        SessionUser user = SessionUserUtil.getSessionUser();
        epidemicResp.setOperatePoliceSfzh(user.getIdCard());
        epidemicResp.setOperatePolice(user.getName());
        epidemicResp.setOperateTime(new Date());
        epidemicRespDao.insert(epidemicResp);
        // 返回
        return epidemicResp.getId();
    }

    @Override
    public EpidemicRespDO getEpidemicResp(String id) {
        return epidemicRespDao.selectById(id);
    }



}
