package com.rs.module.ihc.entity.ipm;

import lombok.*;
import java.util.*;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 所内就医-处方-药方信息 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 所内就医-处方-药方信息新增/修改 Request VO")
@TableName("ihc_ipm_prescribe_medicine")
@KeySequence("ihc_ipm_prescribe_medicine_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrescribeMedicineDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;
    /**
     * 处方id，对应ihc_ipm_prescribe.id
     */
    @ApiModelProperty("处方id，对应ihc_ipm_prescribe.id")
    private String mlh;
    /**
     * 药品id，对应ihc_pm_drug.id
     */
    @ApiModelProperty("药品id，对应ihc_pm_drug.id")
    private String medicineId;
    /**
     * 总库存数量
     */
    @ApiModelProperty("总库存数量")
    private BigDecimal totalInventoryNum;
    /**
     * 每次用量（数字）
     */
    @ApiModelProperty("每次用量（数字）")
    private BigDecimal oneDosageNum;
    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String unit;
    /**
     * 使用频率
     */
    @ApiModelProperty("使用频率")
    private String useFrequency;
    /**
     * 用药天数
     */
    @ApiModelProperty("用药天数")
    private Integer useDay;
    /**
     * 总药量
     */
    @ApiModelProperty("总药量")
    private BigDecimal num;
    /**
     * 给药方式
     */
    @ApiModelProperty("给药方式")
    private String useMedicineMethod;
    /**
     * 嘱托
     */
    @ApiModelProperty("嘱托")
    private String entrust;

}
