package com.rs.module.ihc.controller.admin.ipm.vo;

import com.rs.framework.common.annotation.DefaultValueDate;
import com.rs.framework.common.annotation.DefaultValueInteger;
import com.rs.framework.common.annotation.DefaultValueString;
import com.rs.framework.common.annotation.SessionUserIdCard;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 所内就医-处方新增/修改 Request VO")
@Data
public class PrescribeSaveReqVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("处方类型（1：所内就医远程问诊处方， 2：所内就医现场巡诊处方）")
    @DefaultValueInteger(value = 3)
    private Integer prescribeType;

    @ApiModelProperty("业务id")
    @NotNull(message = "业务id不能为空")
    private String businessId;

    @ApiModelProperty("医嘱编号")
    @NotNull(message = "医嘱编号不能为空")
    private String doctorAdviceNum;

    @ApiModelProperty("医嘱类型")
    @NotNull(message = "医嘱类型不能为空")
    private String doctorAdviceType;

    @ApiModelProperty("主诉")
    private String mainComplaint;

    @ApiModelProperty("病史")
    private String medicalHistory;

    @ApiModelProperty("体格检查")
    private String physicalCheck;

    @ApiModelProperty("辅助检查")
    private String auxiliaryCheck;

    @ApiModelProperty("初步诊断")
    @NotNull(message = "初步诊断不能为空")
    private String primaryDiagnosis;

    @ApiModelProperty("处理意见")
    private String suggestion;

    @ApiModelProperty("处方编号")
    @NotNull(message = "处方编号不能为空")
    private String prescribeNum;

    @ApiModelProperty("医嘱")
    private String doctorAdvice;

    @ApiModelProperty("处方说明")
    private String prescribeDescribe;

    @ApiModelProperty("开处方时间")
    @DefaultValueDate()
    private Date prescribeTime;

    @ApiModelProperty("开处方人，对应permission_user.userid")
    @SessionUserIdCard()
    private String prescribeUserid;

    @ApiModelProperty("开处方人名称")
    private String prescribeUserName;

    @ApiModelProperty("处方状态")
    @DefaultValueString("0")
    private String prescribeStatus;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("在押人员编号")
    private String rybh;

    @ApiModelProperty("在押人员名称")
    private String ryxm;

    @ApiModelProperty("发药次数/医嘱执行次数")
    private Integer dispenseNum;

    @ApiModelProperty("最新发药时间/医嘱执行时间")
    private Date lastDispenseTime;

    @ApiModelProperty("服药次数")
    private Integer doseNum;

    @ApiModelProperty("处方药品出库次数")
    private Integer outNum;

    @ApiModelProperty("处方药品最后一次出库时间")
    private Date lastOutTime;

    @ApiModelProperty("监所id")
    private String prisonId;

    @ApiModelProperty("处方药品类型 1：西药 2：中药 。。。字典类型值 internal_prescribe_medicine_type")
    private String prescribeMedicineType;

    @ApiModelProperty("医嘱/处方 开始日期")
    @NotNull(message = "医嘱/处方 开始日期不能为空")
    private Date startPrescribeDate;

    @ApiModelProperty("医嘱/处方 结束日期")
    @NotNull(message = "医嘱/处方 结束日期不能为空")
    private Date endPrescribeDate;

    @ApiModelProperty("检查次数")
    private Integer checkupNum;

    @ApiModelProperty("所内就医-处方-药方信息列表")
    private List<PrescribeMedicineSaveReqVO> medicineList;

}
