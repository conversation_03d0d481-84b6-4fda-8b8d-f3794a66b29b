package com.rs.module.ihc.events.consumer.ipm;

import com.rs.module.ihc.events.event.ipm.PrescribeStateEvent;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * @ClassName PrescribeConsumer
 * <AUTHOR>
 * @Date 2025/3/25 18:56
 * @Version 1.0
 */
@Component
@Log4j2
public class PrescribeConsumer {
    @EventListener
    public void handlePrescribeCreateEvent(PrescribeStateEvent event) {

    }
}
