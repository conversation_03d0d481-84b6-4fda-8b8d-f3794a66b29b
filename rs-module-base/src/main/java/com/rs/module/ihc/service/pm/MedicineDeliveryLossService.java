package com.rs.module.ihc.service.pm;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.MedicineDeliveryLossDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 药房管理-顾送药品报损 Service 接口
 *
 * <AUTHOR>
 */
public interface MedicineDeliveryLossService extends IBaseService<MedicineDeliveryLossDO>{

    /**
     * 创建药房管理-顾送药品报损
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMedicineDeliveryLoss(@Valid MedicineDeliveryLossSaveReqVO createReqVO);

    /**
     * 更新药房管理-顾送药品报损
     *
     * @param updateReqVO 更新信息
     */
    void updateMedicineDeliveryLoss(@Valid MedicineDeliveryLossSaveReqVO updateReqVO);

    /**
     * 删除药房管理-顾送药品报损
     *
     * @param id 编号
     */
    void deleteMedicineDeliveryLoss(String id);

    /**
     * 获得药房管理-顾送药品报损
     *
     * @param id 编号
     * @return 药房管理-顾送药品报损
     */
    MedicineDeliveryLossDO getMedicineDeliveryLoss(String id);

    /**
    * 获得药房管理-顾送药品报损分页
    *
    * @param pageReqVO 分页查询
    * @return 药房管理-顾送药品报损分页
    */
    PageResult<MedicineDeliveryLossDO> getMedicineDeliveryLossPage(MedicineDeliveryLossPageReqVO pageReqVO);

    /**
    * 获得药房管理-顾送药品报损列表
    *
    * @param listReqVO 查询条件
    * @return 药房管理-顾送药品报损列表
    */
    List<MedicineDeliveryLossDO> getMedicineDeliveryLossList(MedicineDeliveryLossListReqVO listReqVO);


}
