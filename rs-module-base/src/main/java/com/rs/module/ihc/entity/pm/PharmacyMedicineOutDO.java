package com.rs.module.ihc.entity.pm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 大药房管理-药品出库 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_pm_pharmacy_medicine_out")
@KeySequence("ihc_pm_pharmacy_medicine_out_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ihc_pm_pharmacy_medicine_out")
public class PharmacyMedicineOutDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 药品id，对应ichs_medicine.id
     */
    private String medicineId;
    /**
     * 入库批次id，对应ichs_medicine_in.id
     */
    private String medicineInId;
    /**
     * 出库原因
     */
    private String outStorageReason;
    /**
     * 出库数量
     */
    private BigDecimal outNum;
    /**
     * 取药单位
     */
    private String dispensaryUnit;
    /**
     * 取药时间
     */
    private Date dispensaryTime;
    /**
     * 取药人，对应permission_user.userid
     */
    private String userid;
    /**
     * 取药人名称
     */
    private String userName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 批次库存数量 最小单位
     */
    private BigDecimal inventoryNum;
    /**
     * 当前药品总库存数量 最小单位
     */
    private BigDecimal totalInventoryNum;
    /**
     * 出库状态
     */
    private String outStatus;
    /**
     * 流程是否结束 0=否 1=是
     */
    private Integer workflowEnd;

}
