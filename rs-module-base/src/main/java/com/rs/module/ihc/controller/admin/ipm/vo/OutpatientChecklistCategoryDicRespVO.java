package com.rs.module.ihc.controller.admin.ipm.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.annotation.Query;
import com.rs.framework.mybatis.config.GlobalConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class OutpatientChecklistCategoryDicRespVO  implements TransPojo {
private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 检查类型 字典值 1=常规检查 2=心电图 3=B超 4=DR
     */
    @ApiModelProperty("检查类型 字典值 1=常规检查 2=心电图 3=B超 4=DR")
    private String checklistCategory;
    /**
     * 检查子类型 字典值 多选 ,分割
     */
    @ApiModelProperty("检查子类型名称")
    private String checklistCategoryName;


    @Query(datasource = GlobalConstant.BSP_DATASOURCE_KEY,sql = "select t.id, t.NAME as checklistCategoryName,t.CODE as checklistCategory from ops_dic_code t where PARENT_ID = '${id}'",beanClass= OutpatientChecklistCategoryDicRespVO.class)
    public List<OutpatientChecklistCategoryDicRespVO> outpatientChecklistCategorys;
}
