package com.rs.module.ihc.controller.admin.pm.sick.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 重特病号解除申请审批 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SeverelySickReliveApproveRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键id")
    private String id;
    @ApiModelProperty("关联重特病号解除申请表id")
    private String reliveApplyId;
    @ApiModelProperty("业务状态 字典值：severely_sick_business_status")
    private String businessStatus;
    @ApiModelProperty("审批结果 字典值：is_agree")
    private String approveResult;
    @ApiModelProperty("审批人id")
    private Long approveUserId;
    @ApiModelProperty("审批人姓名")
    private String approveUserName;
    @ApiModelProperty("审批意见")
    private String approveOpinion;
    @ApiModelProperty("审批时间")
    private Date approveTime;
}
