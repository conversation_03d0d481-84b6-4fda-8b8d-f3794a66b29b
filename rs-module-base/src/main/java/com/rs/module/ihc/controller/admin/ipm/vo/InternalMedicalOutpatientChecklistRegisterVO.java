package com.rs.module.ihc.controller.admin.ipm.vo;

import com.rs.framework.common.annotation.DefaultValueDate;
import com.rs.framework.common.annotation.SessionUserIdCard;
import com.rs.framework.common.annotation.SessionUserName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> LiuWenxing
 * @create 2024/7/31 10:44
 */
@Data
public class InternalMedicalOutpatientChecklistRegisterVO {

    /**
     * 登记人，对应permission_user.userid
     */
    @SessionUserIdCard
    private String registerUserid;
    /**
     * 登记人名称
     */
    @SessionUserName
    private String registerUserName;
    /**
     * 登记时间
     */
    @DefaultValueDate
    private Date registerTime;


    @ApiModelProperty("检查单id")
    private String id;

    @ApiModelProperty(value="检查结论")
    private String checkConclusion;

    @ApiModelProperty("检查结果")
    private List<InternalMedicalOutpatientChecklistCategoryRegisterVO> categoryList;

}
