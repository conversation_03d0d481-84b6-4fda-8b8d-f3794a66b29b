package com.rs.module.ihc.enums;

/**
 * @ClassName AdviceType
 * <AUTHOR>
 * @Date 2025/3/26 11:02
 * @Version 1.0
 */
public enum OutpatientStatus {
    //0未看诊1已看诊
    IHC_OUT_PATIENT_STATUS_0("未看诊", 0),
    IHC_OUT_PATIENT_STATUS_1("已看诊", 1);

    private String name;
    private Integer value;

    OutpatientStatus(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }
}
