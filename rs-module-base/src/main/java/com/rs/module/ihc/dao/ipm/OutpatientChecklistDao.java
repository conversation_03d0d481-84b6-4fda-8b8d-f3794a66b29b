package com.rs.module.ihc.dao.ipm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.ipm.OutpatientChecklistDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 所内就医-所内门诊-检查单 Dao
*
* <AUTHOR>
*/
@Mapper
public interface OutpatientChecklistDao extends IBaseDao<OutpatientChecklistDO> {


    default PageResult<OutpatientChecklistDO> selectPage(OutpatientChecklistPageReqVO reqVO) {
        Page<OutpatientChecklistDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<OutpatientChecklistDO> wrapper = new LambdaQueryWrapperX<OutpatientChecklistDO>()
            .eqIfPresent(OutpatientChecklistDO::getOutpatientId, reqVO.getOutpatientId())
            .eqIfPresent(OutpatientChecklistDO::getMainComplaint, reqVO.getMainComplaint())
            .eqIfPresent(OutpatientChecklistDO::getIllnessResume, reqVO.getIllnessResume())
            .eqIfPresent(OutpatientChecklistDO::getCheckConclusion, reqVO.getCheckConclusion())
            .eqIfPresent(OutpatientChecklistDO::getChecklistNum, reqVO.getChecklistNum())
            .eqIfPresent(OutpatientChecklistDO::getChecklistCategory, reqVO.getChecklistCategory())
            .eqIfPresent(OutpatientChecklistDO::getChecklistStatus, reqVO.getChecklistStatus())
            .eqIfPresent(OutpatientChecklistDO::getRegisterUserid, reqVO.getRegisterUserid())
            .likeIfPresent(OutpatientChecklistDO::getRegisterUserName, reqVO.getRegisterUserName())
            .betweenIfPresent(OutpatientChecklistDO::getRegisterTime, reqVO.getRegisterTime())
            .eqIfPresent(OutpatientChecklistDO::getPrisonId, reqVO.getPrisonId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(OutpatientChecklistDO::getAddTime);
        }
        Page<OutpatientChecklistDO> outpatientChecklistPage = selectPage(page, wrapper);
        return new PageResult<>(outpatientChecklistPage.getRecords(), outpatientChecklistPage.getTotal());
    }
    default List<OutpatientChecklistDO> selectList(OutpatientChecklistListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<OutpatientChecklistDO>()
            .eqIfPresent(OutpatientChecklistDO::getOutpatientId, reqVO.getOutpatientId())
            .eqIfPresent(OutpatientChecklistDO::getMainComplaint, reqVO.getMainComplaint())
            .eqIfPresent(OutpatientChecklistDO::getIllnessResume, reqVO.getIllnessResume())
            .eqIfPresent(OutpatientChecklistDO::getCheckConclusion, reqVO.getCheckConclusion())
            .eqIfPresent(OutpatientChecklistDO::getChecklistNum, reqVO.getChecklistNum())
            .eqIfPresent(OutpatientChecklistDO::getChecklistCategory, reqVO.getChecklistCategory())
            .eqIfPresent(OutpatientChecklistDO::getChecklistStatus, reqVO.getChecklistStatus())
            .eqIfPresent(OutpatientChecklistDO::getRegisterUserid, reqVO.getRegisterUserid())
            .likeIfPresent(OutpatientChecklistDO::getRegisterUserName, reqVO.getRegisterUserName())
            .betweenIfPresent(OutpatientChecklistDO::getRegisterTime, reqVO.getRegisterTime())
            .eqIfPresent(OutpatientChecklistDO::getPrisonId, reqVO.getPrisonId())
        .orderByDesc(OutpatientChecklistDO::getAddTime));    }


    }
