package com.rs.module.ihc.dao.pm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.controller.admin.pm.vo.GetMaxBatchCodeVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineInListReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineInPageReqVO;
import com.rs.module.ihc.entity.pm.MedicineInDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
* 药房管理-药品入库批次 Dao
*
* <AUTHOR>
*/
@Mapper
public interface MedicineInDao extends IBaseDao<MedicineInDO> {


    default PageResult<MedicineInDO> selectPage(MedicineInPageReqVO reqVO) {
        Page<MedicineInDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<MedicineInDO> medicineInPage = selectPage(page, new LambdaQueryWrapperX<MedicineInDO>()
            .eqIfPresent(MedicineInDO::getMedicineId, reqVO.getMedicineId())
            .eqIfPresent(MedicineInDO::getInNum, reqVO.getInNum())
            .eqIfPresent(MedicineInDO::getBatchCode, reqVO.getBatchCode())
            .eqIfPresent(MedicineInDO::getSupplierId, reqVO.getSupplierId())
            .likeIfPresent(MedicineInDO::getSupplierName, reqVO.getSupplierName())
            .eqIfPresent(MedicineInDO::getVoucherCode, reqVO.getVoucherCode())
            .eqIfPresent(MedicineInDO::getPurchasePrice, reqVO.getPurchasePrice())
            .eqIfPresent(MedicineInDO::getWholesalePrice, reqVO.getWholesalePrice())
            .eqIfPresent(MedicineInDO::getTransferPrice, reqVO.getTransferPrice())
            .eqIfPresent(MedicineInDO::getRetailPrice, reqVO.getRetailPrice())
            .betweenIfPresent(MedicineInDO::getInDate, reqVO.getInDate())
            .betweenIfPresent(MedicineInDO::getExpireDate, reqVO.getExpireDate())
            .eqIfPresent(MedicineInDO::getInStorageMethod, reqVO.getInStorageMethod())
            .eqIfPresent(MedicineInDO::getTransferMethod, reqVO.getTransferMethod())
            .eqIfPresent(MedicineInDO::getMedicinePlace, reqVO.getMedicinePlace())
            .eqIfPresent(MedicineInDO::getInventoryNum, reqVO.getInventoryNum())
            .eqIfPresent(MedicineInDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
            .eqIfPresent(MedicineInDO::getInStatus, reqVO.getInStatus())
            .eqIfPresent(MedicineInDO::getWorkflowEnd, reqVO.getWorkflowEnd())
            .orderByDesc(MedicineInDO::getId));
            return new PageResult<>(medicineInPage.getRecords(), medicineInPage.getTotal());
    }
    default List<MedicineInDO> selectList(MedicineInListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MedicineInDO>()
            .eqIfPresent(MedicineInDO::getMedicineId, reqVO.getMedicineId())
            .eqIfPresent(MedicineInDO::getInNum, reqVO.getInNum())
            .eqIfPresent(MedicineInDO::getBatchCode, reqVO.getBatchCode())
            .eqIfPresent(MedicineInDO::getSupplierId, reqVO.getSupplierId())
            .likeIfPresent(MedicineInDO::getSupplierName, reqVO.getSupplierName())
            .eqIfPresent(MedicineInDO::getVoucherCode, reqVO.getVoucherCode())
            .eqIfPresent(MedicineInDO::getPurchasePrice, reqVO.getPurchasePrice())
            .eqIfPresent(MedicineInDO::getWholesalePrice, reqVO.getWholesalePrice())
            .eqIfPresent(MedicineInDO::getTransferPrice, reqVO.getTransferPrice())
            .eqIfPresent(MedicineInDO::getRetailPrice, reqVO.getRetailPrice())
            .betweenIfPresent(MedicineInDO::getInDate, reqVO.getInDate())
            .betweenIfPresent(MedicineInDO::getExpireDate, reqVO.getExpireDate())
            .eqIfPresent(MedicineInDO::getInStorageMethod, reqVO.getInStorageMethod())
            .eqIfPresent(MedicineInDO::getTransferMethod, reqVO.getTransferMethod())
            .eqIfPresent(MedicineInDO::getMedicinePlace, reqVO.getMedicinePlace())
            .eqIfPresent(MedicineInDO::getInventoryNum, reqVO.getInventoryNum())
            .eqIfPresent(MedicineInDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
            .eqIfPresent(MedicineInDO::getInStatus, reqVO.getInStatus())
            .eqIfPresent(MedicineInDO::getWorkflowEnd, reqVO.getWorkflowEnd())
        .orderByDesc(MedicineInDO::getId));    }

    /**
     * 获取存在批次过期的药品id
     * <p>
     * 当过期时间到了并且库存已经为0的批次就不算存在过期药品的批次
     *
     * @param expireDate 过期日期
     * @param medicineIdList 药品id集合 可以为空
     * @return 批次过期的药品id集合
     */
    List<String> getExistExpireBatchMedicineId(@Param("expireDate") Date expireDate,
                                             @Param("medicineIdList") List<String> medicineIdList);

    /**
     * 根据多个药品集合获取每个药品第一个没有过期的药品批次
     *
     * @param medicineIdList 药品id集合
     * @param expireDate 过期日期
     * @return 药品批次
     */
    List<MedicineInDO> selectFirstNoExpireMedicineBatch(@Param("medicineIdList") Collection<String> medicineIdList, @Param("expireDate") Date expireDate);


    /**
     * 获取入库最大批次号
     *
     * @param inDateList 入库日期集合
     */
    List<GetMaxBatchCodeVO> getMaxBatchCode(@Param("inDateList") List<LocalDate> inDateList);



    }
