package com.rs.module.ihc.controller.admin.idm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 传染病管理-传染病管理登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class InfectionSaveReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    //@ApiModelProperty("主键")
    //private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("传染病类型 字典：ZD_CYB_CRBLX")
    @NotEmpty(message = "传染病类型不能为空")
    private String infectiousDiseaseType;

    @ApiModelProperty("艾滋病-初筛时间")
    private Date aidsFirstScreeningTime;

    @ApiModelProperty("艾滋病-初筛操作方式 字典：ZD_CYB_CSCZFS")
    private String aidsFirstScreeningMethod;

    @ApiModelProperty("艾滋病-初筛结果 字典：ZD_CYB_CSJG_YXYX")
    private String aidsFirstScreeningResult;

    @ApiModelProperty("艾滋病-确诊日期")
    private Date aidsDiagnosisDate;

    @ApiModelProperty("艾滋病-实验室反馈时间")
    private Date aidsLabFeedbackTime;

    @ApiModelProperty("艾滋病-实验室反馈结果 字典：ZD_CYB_CSJG_YXYX")
    private String aidsLabFeedbackResult;

    @ApiModelProperty("艾滋病-告知在押人员时间")
    private Date aidsInformInmateTime;

    @ApiModelProperty("艾滋病-是否告知家属")
    private String aidsIsInformFamily;

    @ApiModelProperty("艾滋病-告知家属时间")
    private Date aidsInformFamilyTime;

    @ApiModelProperty("告知疾控机构日期")
    private Date gzjkjgrq;

    @ApiModelProperty("疾控机构名称")
    private String jkjgrqmc;

    @ApiModelProperty("是否接收转入疾控机构名称反馈信息")
    private String sfjszrjkjgfkxx;

    @ApiModelProperty("接收转入疾控机构反馈信息日期")
    private Date jszrjkjgfkxxrq;

    @ApiModelProperty("是否需要再次联系转入疾控机构")
    private String sfxyzclxzrjkjg;

    @ApiModelProperty("再次联系转入疾控机构日期")
    private Date zclxzrjkjgrq;

    @ApiModelProperty("送交人")
    private String submitter;

    @ApiModelProperty("附件地址")
    private String attUrl;

    @ApiModelProperty("健康体检时间")
    private Date checkupTime;

    @ApiModelProperty("健康体检方式：ZD_CYB_JKTJFS")
    private String checkupMethod;

    @ApiModelProperty("健康体检检查方式：ZD_CYB_CSJG_YXYX")
    private String checkupResult;

    @ApiModelProperty("健康体检检查人")
    private String checkupExaminer;


    //@ApiModelProperty("登记民警身份证号")
    //@NotEmpty(message = "登记民警身份证号不能为空")
    //private String operatePoliceSfzh;
    //
    //@ApiModelProperty("登记民警")
    //@NotEmpty(message = "登记民警不能为空")
    //private String operatePolice;
    //
    //@ApiModelProperty("登记时间")
    //@NotNull(message = "登记时间不能为空")
    //private Date operateTime;

}
