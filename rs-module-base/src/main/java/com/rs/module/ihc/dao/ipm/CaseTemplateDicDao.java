package com.rs.module.ihc.dao.ipm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.ipm.CaseTemplateDicDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 所内就医-病例模板表-字典 Dao
*
* <AUTHOR>
*/
@Mapper
public interface CaseTemplateDicDao extends IBaseDao<CaseTemplateDicDO> {


    default PageResult<CaseTemplateDicDO> selectPage(CaseTemplateDicPageReqVO reqVO) {
        Page<CaseTemplateDicDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<CaseTemplateDicDO> caseTemplateDicPage = selectPage(page, new LambdaQueryWrapperX<CaseTemplateDicDO>()
            .eqIfPresent(CaseTemplateDicDO::getLable, reqVO.getLable())
            .eqIfPresent(CaseTemplateDicDO::getFl, reqVO.getFl())
            .eqIfPresent(CaseTemplateDicDO::getFz, reqVO.getFz())
            .eqIfPresent(CaseTemplateDicDO::getSort, reqVO.getSort())
            .eqIfPresent(CaseTemplateDicDO::getFzOrder, reqVO.getFzOrder())
            .eqIfPresent(CaseTemplateDicDO::getDl, reqVO.getDl())
            .orderByDesc(CaseTemplateDicDO::getId));
            return new PageResult<>(caseTemplateDicPage.getRecords(), caseTemplateDicPage.getTotal());
    }
    default List<CaseTemplateDicDO> selectList(CaseTemplateDicListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CaseTemplateDicDO>()
            .eqIfPresent(CaseTemplateDicDO::getLable, reqVO.getLable())
            .eqIfPresent(CaseTemplateDicDO::getFl, reqVO.getFl())
            .eqIfPresent(CaseTemplateDicDO::getFz, reqVO.getFz())
            .eqIfPresent(CaseTemplateDicDO::getSort, reqVO.getSort())
            .eqIfPresent(CaseTemplateDicDO::getFzOrder, reqVO.getFzOrder())
            .eqIfPresent(CaseTemplateDicDO::getDl, reqVO.getDl())
        .orderByDesc(CaseTemplateDicDO::getId));    }


    }
