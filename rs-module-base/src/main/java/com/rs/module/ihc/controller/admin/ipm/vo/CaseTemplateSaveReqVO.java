package com.rs.module.ihc.controller.admin.ipm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel(description = "管理后台 - 所内就医-病例模板新增/修改 Request VO")
@Data
public class CaseTemplateSaveReqVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("病例名称")
    @NotEmpty(message = "病例名称不能为空")
    private String templateName;

    @ApiModelProperty("模板类型（1：医嘱模板， 2：门诊登记模板，3：巡诊登记模板）")
    @NotNull(message = "模板类型（1：医嘱模板， 2：门诊登记模板，3：巡诊登记模板）不能为空")
    private Integer templateType;

    @ApiModelProperty("主诉")
    private String mainComplaint;

    @ApiModelProperty("病史")
    private String medicalHistory;

    @ApiModelProperty("体格检查")
    private String physicalCheck;

    @ApiModelProperty("辅助检查")
    private String auxiliaryCheck;

    @ApiModelProperty("初步诊断")
    private String primaryDiagnosis;

    @ApiModelProperty("处理意见")
    private String suggestion;

    @ApiModelProperty("病情简述")
    private String illnessResume;

    @ApiModelProperty("巡诊情况")
    private String visitState;

    @ApiModelProperty("巡诊结论")
    private String visitConclusion;

}
