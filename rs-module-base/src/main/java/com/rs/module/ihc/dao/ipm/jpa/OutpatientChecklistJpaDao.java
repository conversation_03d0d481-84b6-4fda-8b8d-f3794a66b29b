package com.rs.module.ihc.dao.ipm.jpa;

import com.rs.module.ihc.entity.ipm.OutpatientChecklistDO;
import org.springframework.data.repository.CrudRepository;

/**
 * @ClassName OutpatientChecklistJpaDao
 * @Description jpa测试
 * <AUTHOR>
 * @Date 2025/3/30 11:11
 * @Version 1.0
 */
public interface OutpatientChecklistJpaDao extends CrudRepository<OutpatientChecklistDO, String> {
    OutpatientChecklistDO findByOutpatientId(String outpatientId);
}
