package com.rs.module.ihc.controller.admin.ipm.appointment.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class SaveMedicalAppointmentDTO {

    /**
     * 被监管人员编号
     */
    @NotBlank(message = "被监管人员编号不能为空")
    @ApiModelProperty(value = "被监管人员编号", required = true)
    private String supervisedUserCode;

    /**
     * 报病原因
     */
    @ApiModelProperty("报病原因")
    private String diseaseReason;
}
