package com.rs.module.ihc.controller.admin.pm.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class BatchSaveMedicineLossDTO {

    /**
     * 药品id
     */
    @ApiModelProperty(value = "药品id", required = true)
    @NotNull(message = "药品id不能为空")
    private String id;

    /**
     * 出库批次列表
     */
    @ApiModelProperty(value = "报损批次列表", required = true)
    @NotEmpty(message = "报损批次列表不能为空")
    private List<SaveMedicineLossDTO> medicineLossList;

}
