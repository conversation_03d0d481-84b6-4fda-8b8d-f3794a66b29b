package com.rs.module.ihc.service.ipm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.base.entity.RyReqVO;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.VisitDO;
import com.rs.module.ihc.entity.ipm.VisitMedicineDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 所内就医-现场巡检 Service 接口
 *
 * <AUTHOR>
 */
public interface VisitService extends IBaseService<VisitDO> {

    /**
     * 创建所内就医-现场巡检
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createVisit(@Valid VisitSaveReqVO createReqVO);

    String createVisitCf(@Valid VisitSaveCfReqVO createReqVO);

    String createVisitWxyy(@Valid VisitSaveWxclReqVO createReqVO);

    /**
     * 更新所内就医-现场巡检
     *
     * @param updateReqVO 更新信息
     */
    void updateVisit(@Valid VisitSaveReqVO updateReqVO);

    void updateVisitCf(@Valid VisitSaveCfReqVO updateReqVO);

    /**
     * 删除所内就医-现场巡检
     *
     * @param id 编号
     */
    void deleteVisit(String id);

    /**
     * 获得所内就医-现场巡检
     *
     * @param id 编号
     * @return 所内就医-现场巡检
     */
    VisitDO getVisit(String id);

    /**
     * 获得所内就医-现场巡检分页
     *
     * @param pageReqVO 分页查询
     * @return 所内就医-现场巡检分页
     */
    PageResult<VisitDO> getVisitPage(VisitPageReqVO pageReqVO);

    /**
     * 获得所内就医-现场巡检列表
     *
     * @param listReqVO 查询条件
     * @return 所内就医-现场巡检列表
     */
    List<VisitDO> getVisitList(VisitListReqVO listReqVO);


    // ==================== 子表（所内就医-现场巡诊-现场司药信息） ====================

    /**
     * 获得所内就医-现场巡诊-现场司药信息列表
     *
     * @param visitId 现场巡诊id，对应ihc_ipm_visit.id
     * @return 所内就医-现场巡诊-现场司药信息列表
     */
    List<VisitMedicineDO> getVisitMedicineListByVisitId(String visitId);


    List<VisitRespVO> getVisitTodo(RyReqVO ry);


    List<VisitXzjgRespVO> listXzjgByRoomId(String type,  String roomId);


    Boolean getHaveRestraints(String jgrybm);
}
