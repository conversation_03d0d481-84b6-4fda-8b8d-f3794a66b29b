package com.rs.module.ihc.controller.admin.pm.sick.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 重特病号审批新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SeverelySickApproveSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键id")
    private String id;

    @ApiModelProperty("关联重特病号管理表id")
    @NotEmpty(message = "关联重特病号管理表id不能为空")
    private String manageId;

    @ApiModelProperty("业务状态 字典值：severely_sick_business_status")
    @NotEmpty(message = "业务状态 字典值：severely_sick_business_status不能为空")
    private String businessStatus;

    @ApiModelProperty("审批结果 字典值：is_agree")
    private String approveResult;

    @ApiModelProperty("审批人id")
    @NotNull(message = "审批人id不能为空")
    private Long approveUserId;

    @ApiModelProperty("审批人姓名")
    @NotEmpty(message = "审批人姓名不能为空")
    private String approveUserName;

    @ApiModelProperty("审批意见")
    private String approveOpinion;

    @ApiModelProperty("审批时间")
    @NotNull(message = "审批时间不能为空")
    private Date approveTime;

}
