package com.rs.module.ihc.service.ipm;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.OutpatientChecklistDO;
import com.rs.module.ihc.entity.ipm.OutpatientChecklistCategoryDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 所内就医-所内门诊-检查单 Service 接口
 *
 * <AUTHOR>
 */
public interface OutpatientChecklistService extends IBaseService<OutpatientChecklistDO>{

    /**
     * 创建所内就医-所内门诊-检查单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createOutpatientChecklist(@Valid OutpatientChecklistSaveReqVO createReqVO);

    /**
     * 更新所内就医-所内门诊-检查单
     *
     * @param updateReqVO 更新信息
     */
    void updateOutpatientChecklist(@Valid OutpatientChecklistSaveReqVO updateReqVO);

    /**
     * 删除所内就医-所内门诊-检查单
     *
     * @param id 编号
     */
    void deleteOutpatientChecklist(String id);

    /**
     * 获得所内就医-所内门诊-检查单
     *
     * @param id 编号
     * @return 所内就医-所内门诊-检查单
     */
    OutpatientChecklistDO getOutpatientChecklist(String id);

    /**
    * 获得所内就医-所内门诊-检查单分页
    *
    * @param pageReqVO 分页查询
    * @return 所内就医-所内门诊-检查单分页
    */
    PageResult<OutpatientChecklistDO> getOutpatientChecklistPage(OutpatientChecklistPageReqVO pageReqVO);

    /**
    * 获得所内就医-所内门诊-检查单列表
    *
    * @param listReqVO 查询条件
    * @return 所内就医-所内门诊-检查单列表
    */
    List<OutpatientChecklistDO> getOutpatientChecklistList(OutpatientChecklistListReqVO listReqVO);


    // ==================== 子表（所内就医-所内门诊-检查单-检查种类） ====================

    /**
     * 获得所内就医-所内门诊-检查单-检查种类列表
     *
     * @param checklistId 检查单id，对应ihc_ipm_outpatient_checklist.id
     * @return 所内就医-所内门诊-检查单-检查种类列表
     */
    List<OutpatientChecklistCategoryDO> getOutpatientChecklistCategoryListByChecklistId(String checklistId);


    /**
     * 登记检查单
     * @param vo
     */
    void registerCheckList(InternalMedicalOutpatientChecklistRegisterVO vo);

    /**
     * 作废检查单
     * @param id
     */
    void nullifyCheckListById(String id);

    String getChecklistSubCategoryName(String checklistSubCategory);

}
