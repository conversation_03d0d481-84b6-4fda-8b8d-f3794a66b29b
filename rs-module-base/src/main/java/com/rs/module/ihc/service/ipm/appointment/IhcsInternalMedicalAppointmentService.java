package com.rs.module.ihc.service.ipm.appointment;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.ihc.controller.admin.ipm.appointment.dto.AuditMedicalAppointmentDTO;
import com.rs.module.ihc.controller.admin.ipm.appointment.dto.BatchAuditNoRequireProcessDTO;
import com.rs.module.ihc.controller.admin.ipm.appointment.dto.SaveMedicalAppointmentDTO;
import com.rs.module.ihc.controller.admin.ipm.appointment.vo.GetMedicalAppointmentByIdVO;
import com.rs.module.ihc.controller.admin.ipm.appointment.vo.IhcsInternalMedicalAppointmentListReqVO;
import com.rs.module.ihc.controller.admin.ipm.appointment.vo.IhcsInternalMedicalAppointmentPageReqVO;
import com.rs.module.ihc.controller.admin.ipm.appointment.vo.IhcsInternalMedicalAppointmentSaveReqVO;
import com.rs.module.ihc.entity.ipm.appointment.IhcsInternalMedicalAppointmentDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 所内就医-预约登记 Service 接口
 *
 * <AUTHOR>
 */
public interface IhcsInternalMedicalAppointmentService extends IBaseService<IhcsInternalMedicalAppointmentDO>{

    /**
     * 创建所内就医-预约登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
//    String createsInternalMedicalAppointment(@Valid IhcsInternalMedicalAppointmentSaveReqVO createReqVO);
    String createsInternalMedicalAppointment(@Valid SaveMedicalAppointmentDTO createReqVO);

    /**
     * 更新所内就医-预约登记
     *
     * @param updateReqVO 更新信息
     */
    void updatesInternalMedicalAppointment(@Valid IhcsInternalMedicalAppointmentSaveReqVO updateReqVO);

    /**
     * 删除所内就医-预约登记
     *
     * @param id 编号
     */
    void deletesInternalMedicalAppointment(String id);

    /**
     * 获得所内就医-预约登记
     *
     * @param id 编号
     * @return 所内就医-预约登记
     */
    IhcsInternalMedicalAppointmentDO getsInternalMedicalAppointment(String id);

    /**
    * 获得所内就医-预约登记分页
    *
    * @param pageReqVO 分页查询
    * @return 所内就医-预约登记分页
    */
    PageResult<IhcsInternalMedicalAppointmentDO> getsInternalMedicalAppointmentPage(IhcsInternalMedicalAppointmentPageReqVO pageReqVO);

    /**
    * 获得所内就医-预约登记列表
    *
    * @param listReqVO 查询条件
    * @return 所内就医-预约登记列表
    */
    List<IhcsInternalMedicalAppointmentDO> getsInternalMedicalAppointmentList(IhcsInternalMedicalAppointmentListReqVO listReqVO);


    /**
     * 审核预约信息
     *
     * @param auditMedicalAppointmentDTO 审核预约信息实体
     */
    void auditMedicalAppointment(AuditMedicalAppointmentDTO auditMedicalAppointmentDTO);

    /**
     * 批量审核预约信息无需处理
     *
     * @param batchAuditNoRequireProcessDTO 批量审核预约信息无需处理实体
     */
    void batchAuditNoRequireProcess(BatchAuditNoRequireProcessDTO batchAuditNoRequireProcessDTO);

    /**
     * 根据预约id获取预约信息详情
     *
     * @param id 预约id
     * @return 预约信息详情
     */
    GetMedicalAppointmentByIdVO getMedicalAppointmentById(String id);

}
