package com.rs.module.ihc.service.pm;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.MedicineDeliveryInDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 药房管理-顾送药品入库批次 Service 接口
 *
 * <AUTHOR>
 */
public interface MedicineDeliveryInService extends IBaseService<MedicineDeliveryInDO>{

    /**
     * 创建药房管理-顾送药品入库批次
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMedicineDeliveryIn(@Valid MedicineDeliveryInSaveReqVO createReqVO);

    /**
     * 更新药房管理-顾送药品入库批次
     *
     * @param updateReqVO 更新信息
     */
    void updateMedicineDeliveryIn(@Valid MedicineDeliveryInSaveReqVO updateReqVO);

    /**
     * 删除药房管理-顾送药品入库批次
     *
     * @param id 编号
     */
    void deleteMedicineDeliveryIn(String id);

    /**
     * 获得药房管理-顾送药品入库批次
     *
     * @param id 编号
     * @return 药房管理-顾送药品入库批次
     */
    MedicineDeliveryInDO getMedicineDeliveryIn(String id);

    /**
    * 获得药房管理-顾送药品入库批次分页
    *
    * @param pageReqVO 分页查询
    * @return 药房管理-顾送药品入库批次分页
    */
    PageResult<MedicineDeliveryInDO> getMedicineDeliveryInPage(MedicineDeliveryInPageReqVO pageReqVO);

    /**
    * 获得药房管理-顾送药品入库批次列表
    *
    * @param listReqVO 查询条件
    * @return 药房管理-顾送药品入库批次列表
    */
    List<MedicineDeliveryInDO> getMedicineDeliveryInList(MedicineDeliveryInListReqVO listReqVO);


}
