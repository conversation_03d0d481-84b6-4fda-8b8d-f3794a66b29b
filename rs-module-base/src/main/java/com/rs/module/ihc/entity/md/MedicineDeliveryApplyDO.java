package com.rs.module.ihc.entity.md;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 药品顾送管理-药品顾送申请 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_md_medicine_delivery_apply")
@KeySequence("ihc_md_medicine_delivery_apply_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ihc_md_medicine_delivery_apply")
public class MedicineDeliveryApplyDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 申请-药品名称
     */
    private String applyMedicineName;
    /**
     * 申请-剂型（字典:剂型）
     */
    private String applyDosageForm;
    /**
     * 申请规格
     */
    private String applySpecs;
    /**
     * 申请-生产单位
     */
    private String applyProductUnit;
    /**
     * 申请-批准文号
     */
    private String applyApprovalNum;
    /**
     * 申请-原批准文号
     */
    private String applyOriginalApprovalNum;
    /**
     * 申请-数量
     */
    private BigDecimal applyTotalNum;
    /**
     * 期望送药开始日期
     */
    private Date expectedStartDate;
    /**
     * 期望送药结束日期
     */
    private Date expectedEndDate;
    /**
     * 顾送药来源 字典：ZD_YPGS_GSYLY
     */
    private String drugSource;
    /**
     * 顾送药原因 字典： ZD_YPGS_GSYY
     */
    private String deliveryReason;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否一致
     */
    private Short isConsistent;
    /**
     * 不一致原因
     */
    private String reasonForInconsistency;
    /**
     * 实际送药-药品名称
     */
    private String deliveryMedicineName;
    /**
     * 实际送药--剂型（字典:ZD_DOSAGE_FORM ）
     */
    private String deliveryDosageForm;
    /**
     * 实际送药-规格
     */
    private String deliverySpecs;
    /**
     * 实际送药-生产单位
     */
    private String deliveryProductUnit;
    /**
     * 实际送药-批准文号
     */
    private String deliveryApprovalNum;
    /**
     * 实际送药-原批准文号
     */
    private String deliveryOriginalApprovalNum;
    /**
     * 实际送药-数量
     */
    private BigDecimal deliveryTotalNum;
    /**
     * 有效期开始
     */
    private Date expiryStartDate;
    /**
     * 有效期结束
     */
    private Date expiryEndDate;
    /**
     * 药品照片URL
     */
    private String imgUrl;
    /**
     * 送药日期
     */
    private Date deliveryDate;
    /**
     * 送药备注
     */
    private String deliveryRemark;
    /**
     * 异常原因 ZD_YPGS_YCYY 
     */
    private String exceptionReason;
    /**
     * 状态
     */
    private String status;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;


}
