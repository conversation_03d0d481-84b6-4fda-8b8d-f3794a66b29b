package com.rs.module.ihc.service.ephe;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.ephe.vo.DisinfectSaveReqVO;
import com.rs.module.ihc.dao.ephe.DisinfectDao;
import com.rs.module.ihc.entity.ephe.DisinfectDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 卫生防疫与健康教育-卫生消毒登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DisinfectServiceImpl extends BaseServiceImpl<DisinfectDao, DisinfectDO> implements DisinfectService {

    @Resource
    private DisinfectDao disinfectDao;

    @Override
    public String createDisinfect(DisinfectSaveReqVO createReqVO) {
        // 插入
        DisinfectDO disinfect = BeanUtils.toBean(createReqVO, DisinfectDO.class);
        SessionUser user = SessionUserUtil.getSessionUser();
        disinfect.setOperatePoliceSfzh(user.getIdCard());
        disinfect.setOperatePolice(user.getName());
        disinfect.setOperateTime(new Date());
        disinfectDao.insert(disinfect);
        // 返回
        return disinfect.getId();
    }




    @Override
    public DisinfectDO getDisinfect(String id) {
        return disinfectDao.selectById(id);
    }




}
