package com.rs.module.ihc.service.ipm;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.PrescribeOutDO;
import com.rs.module.ihc.entity.ipm.PrescribeOutMedicineDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 所内就医-处方药品出库记录 Service 接口
 *
 * <AUTHOR>
 */
public interface PrescribeOutService extends IBaseService<PrescribeOutDO>{

    /**
     * 创建所内就医-处方药品出库记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrescribeOut(@Valid PrescribeOutSaveReqVO createReqVO);

    /**
     * 更新所内就医-处方药品出库记录
     *
     * @param updateReqVO 更新信息
     */
    void updatePrescribeOut(@Valid PrescribeOutSaveReqVO updateReqVO);

    /**
     * 删除所内就医-处方药品出库记录
     *
     * @param id 编号
     */
    void deletePrescribeOut(String id);

    /**
     * 获得所内就医-处方药品出库记录
     *
     * @param id 编号
     * @return 所内就医-处方药品出库记录
     */
    PrescribeOutDO getPrescribeOut(String id);

    /**
     * 获得所内就医-处方药品出库记录
     *
     * @param id 编号
     * @return 所内就医-处方药品出库记录
     */
    PrescribeOutRespVO getPrescribeOutById(String id);

    /**
    * 获得所内就医-处方药品出库记录分页
    *
    * @param pageReqVO 分页查询
    * @return 所内就医-处方药品出库记录分页
    */
    PageResult<PrescribeOutDO> getPrescribeOutPage(PrescribeOutPageReqVO pageReqVO);

    /**
    * 获得所内就医-处方药品出库记录列表
    *
    * @param listReqVO 查询条件
    * @return 所内就医-处方药品出库记录列表
    */
    List<PrescribeOutDO> getPrescribeOutList(PrescribeOutListReqVO listReqVO);


    // ==================== 子表（所内就医-处方药品出库记录关联药品） ====================

    /**
     * 获得所内就医-处方药品出库记录关联药品列表
     *
     * @param outId 处方药品出库id，对应iihc_ipm_prescribe_out.id
     * @return 所内就医-处方药品出库记录关联药品列表
     */
    List<PrescribeOutMedicineDO> getPrescribeOutMedicineListByOutId(String outId);

    /**
     * 处方药品出库
     *
     * @param prescribeMedicineOutDTO 数据
     */
    void prescribeMedicineOut(PrescribeMedicineOutReqVO prescribeMedicineOutDTO);

}
