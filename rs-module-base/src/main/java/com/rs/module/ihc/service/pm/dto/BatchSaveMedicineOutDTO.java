package com.rs.module.ihc.service.pm.dto;

import com.rs.module.ihc.controller.admin.ipm.dto.SaveMedicineOutDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class BatchSaveMedicineOutDTO {

    /**
     * 药品id
     */
    @ApiModelProperty(value = "药品id", required = true)
    @NotNull(message = "药品id不能为空")
    private String id;

    /**
     * 出库批次列表
     */
    @ApiModelProperty(value = "出库批次列表", required = true)
    @NotEmpty(message = "出库批次列表不能为空")
    private List<SaveMedicineOutDTO> medicineOutList;

}
