package com.rs.module.ihc.controller.admin.idm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 传染病管理-定期检查登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RegularInspectionSaveReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("传染病登记ID")
    @NotEmpty(message = "传染病登记ID不能为空")
    private String infectionId;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("检查日期/分类管控记录日期")
    @NotNull(message = "检查日期/分类管控记录日期不能为空")
    private Date checkDate;

    @ApiModelProperty("cd4t淋巴值")
    private String cd4tlbz;

    @ApiModelProperty("病毒载量值")
    private String viralLoadValue;

    @ApiModelProperty("管理情况/分类管控情况")
    private String managementSituation;

    @ApiModelProperty("管控人")
    private String controlPersonnel;

    @ApiModelProperty("附件地址")
    private String attUrl;

    //@ApiModelProperty("登记民警身份证号")
    //@NotEmpty(message = "登记民警身份证号不能为空")
    //private String operatePoliceSfzh;
    //
    //@ApiModelProperty("登记民警")
    //@NotEmpty(message = "登记民警不能为空")
    //private String operatePolice;
    //
    //@ApiModelProperty("登记时间")
    //@NotNull(message = "登记时间不能为空")
    //private Date operateTime;

}
