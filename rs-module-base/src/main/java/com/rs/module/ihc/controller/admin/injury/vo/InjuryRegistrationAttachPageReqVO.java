package com.rs.module.ihc.controller.admin.injury.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 医疗子系统-伤亡登记-附件信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InjuryRegistrationAttachPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("关联主表ID")
    private String registrationId;

    @ApiModelProperty("附件URL")
    private String attUrl;

    @ApiModelProperty("描述")
    private String remark;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
