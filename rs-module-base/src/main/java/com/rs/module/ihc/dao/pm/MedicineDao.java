package com.rs.module.ihc.dao.pm;

import cn.hutool.extra.pinyin.PinyinUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.framework.mybatis.util.BizAssert;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineListReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicinePageReqVO;
import com.rs.module.ihc.entity.pm.MedicineDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 药房管理-药品信息 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface MedicineDao extends IBaseDao<MedicineDO> {


    default PageResult<MedicineDO> selectPage(MedicinePageReqVO reqVO) {
        Page<MedicineDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        LambdaQueryWrapperX<MedicineDO> medicineDOLambdaQueryWrapperX = new LambdaQueryWrapperX<MedicineDO>()
                .eqIfPresent(MedicineDO::getIsDel, reqVO.getIsDel())
                .betweenIfPresent(MedicineDO::getAddTime, reqVO.getAddTime())
                .eqIfPresent(MedicineDO::getAddUser, reqVO.getAddUser())
                .eqIfPresent(MedicineDO::getUpdateUser, reqVO.getUpdateUser())
                .eqIfPresent(MedicineDO::getProCode, reqVO.getProCode())
                .likeIfPresent(MedicineDO::getProName, reqVO.getProName())
                .eqIfPresent(MedicineDO::getCityCode, reqVO.getCityCode())
                .likeIfPresent(MedicineDO::getCityName, reqVO.getCityName())
                .eqIfPresent(MedicineDO::getRegCode, reqVO.getRegCode())
                .likeIfPresent(MedicineDO::getRegName, reqVO.getRegName())
                .eqIfPresent(MedicineDO::getOrgCode, reqVO.getOrgCode())
                .likeIfPresent(MedicineDO::getOrgName, reqVO.getOrgName())
                .eqIfPresent(MedicineDO::getPharmacyId, reqVO.getPharmacyId())
                .likeIfPresent(MedicineDO::getMedicineSpellName, reqVO.getMedicineSpellName())
                .eqIfPresent(MedicineDO::getMedicineNameFirstLetter, reqVO.getMedicineNameFirstLetter())
                .eqIfPresent(MedicineDO::getDosageForm, reqVO.getDosageForm())
                .eqIfPresent(MedicineDO::getMeasurementUnit, reqVO.getMeasurementUnit())
                .eqIfPresent(MedicineDO::getSpecs, reqVO.getSpecs())
                .eqIfPresent(MedicineDO::getProductUnit, reqVO.getProductUnit())
                .eqIfPresent(MedicineDO::getApprovalNum, reqVO.getApprovalNum())
                .eqIfPresent(MedicineDO::getOriginalApprovalNum, reqVO.getOriginalApprovalNum())
                .eqIfPresent(MedicineDO::getMedicineStandardCode, reqVO.getMedicineStandardCode())
                .eqIfPresent(MedicineDO::getDrug, reqVO.getDrug())
                .eqIfPresent(MedicineDO::getDayDosage, reqVO.getDayDosage())
                .eqIfPresent(MedicineDO::getOneDosage, reqVO.getOneDosage())
                .eqIfPresent(MedicineDO::getInventoryWarnValue, reqVO.getInventoryWarnValue())
                .eqIfPresent(MedicineDO::getReportableLoss, reqVO.getReportableLoss())
                .likeIfPresent(MedicineDO::getMedicineEnglishName, reqVO.getMedicineEnglishName())
                .likeIfPresent(MedicineDO::getProductName, reqVO.getProductName())
                .eqIfPresent(MedicineDO::getMarketApprovalHolder, reqVO.getMarketApprovalHolder())
                .eqIfPresent(MedicineDO::getMarketApprovalHolderAddress, reqVO.getMarketApprovalHolderAddress())
                .eqIfPresent(MedicineDO::getProductAddress, reqVO.getProductAddress())
                .eqIfPresent(MedicineDO::getMedicineCategory, reqVO.getMedicineCategory())
                .eqIfPresent(MedicineDO::getMedicineStandardCodeRemark, reqVO.getMedicineStandardCodeRemark())
                .eqIfPresent(MedicineDO::getMedicineAlias, reqVO.getMedicineAlias())
                .eqIfPresent(MedicineDO::getPackageUnit, reqVO.getPackageUnit())
                .betweenIfPresent(MedicineDO::getApprovalDate, reqVO.getApprovalDate())
                .eqIfPresent(MedicineDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
                .eqIfPresent(MedicineDO::getHasExpireDateBatch, reqVO.getHasExpireDateBatch())
                .eqIfPresent(MedicineDO::getMinMeasurementUnit, reqVO.getMinMeasurementUnit())
                .eqIfPresent(MedicineDO::getUnitConversionRatio, reqVO.getUnitConversionRatio())
                .eqIfPresent(MedicineDO::getBelongSensitive, reqVO.getBelongSensitive())
                .eqIfPresent(MedicineDO::getPaychoactieDrug, reqVO.getPaychoactieDrug())
                .eqIfPresent(MedicineDO::getPrisonId, reqVO.getPrisonId())
                .orderByDesc(MedicineDO::getPx)
                .orderByDesc(MedicineDO::getUpdateTime);

        medicineDOLambdaQueryWrapperX.eq(MedicineDO::getOrgCode, sessionUser.getOrgCode());
        if (StringUtils.isNotEmpty(reqVO.getMedicineName())) {
            String pinyin = PinyinUtil.getPinyin(reqVO.getMedicineName(), "");
            medicineDOLambdaQueryWrapperX.and(wrapper -> wrapper
                            .like(MedicineDO::getPinyin, pinyin)
                            .or()
                            .like(MedicineDO::getJp, pinyin))
                    .or()
                    .like(MedicineDO::getMedicineName, reqVO.getMedicineName())
            ;
        }

        Page<MedicineDO> medicinePage = selectPage(page, medicineDOLambdaQueryWrapperX);

        return new PageResult<>(medicinePage.getRecords(), medicinePage.getTotal());
    }

    default List<MedicineDO> selectList(MedicineListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MedicineDO>()
                .eqIfPresent(MedicineDO::getIsDel, reqVO.getIsDel())
                .betweenIfPresent(MedicineDO::getAddTime, reqVO.getAddTime())
                .eqIfPresent(MedicineDO::getAddUser, reqVO.getAddUser())
                .eqIfPresent(MedicineDO::getUpdateUser, reqVO.getUpdateUser())
                .eqIfPresent(MedicineDO::getProCode, reqVO.getProCode())
                .likeIfPresent(MedicineDO::getProName, reqVO.getProName())
                .eqIfPresent(MedicineDO::getCityCode, reqVO.getCityCode())
                .likeIfPresent(MedicineDO::getCityName, reqVO.getCityName())
                .eqIfPresent(MedicineDO::getRegCode, reqVO.getRegCode())
                .likeIfPresent(MedicineDO::getRegName, reqVO.getRegName())
                .eqIfPresent(MedicineDO::getOrgCode, reqVO.getOrgCode())
                .likeIfPresent(MedicineDO::getOrgName, reqVO.getOrgName())
                .eqIfPresent(MedicineDO::getPharmacyId, reqVO.getPharmacyId())
                .likeIfPresent(MedicineDO::getMedicineName, reqVO.getMedicineName())
                .likeIfPresent(MedicineDO::getMedicineSpellName, reqVO.getMedicineSpellName())
                .eqIfPresent(MedicineDO::getMedicineNameFirstLetter, reqVO.getMedicineNameFirstLetter())
                .eqIfPresent(MedicineDO::getDosageForm, reqVO.getDosageForm())
                .eqIfPresent(MedicineDO::getMeasurementUnit, reqVO.getMeasurementUnit())
                .eqIfPresent(MedicineDO::getSpecs, reqVO.getSpecs())
                .eqIfPresent(MedicineDO::getProductUnit, reqVO.getProductUnit())
                .eqIfPresent(MedicineDO::getApprovalNum, reqVO.getApprovalNum())
                .eqIfPresent(MedicineDO::getOriginalApprovalNum, reqVO.getOriginalApprovalNum())
                .eqIfPresent(MedicineDO::getMedicineStandardCode, reqVO.getMedicineStandardCode())
                .eqIfPresent(MedicineDO::getDrug, reqVO.getDrug())
                .eqIfPresent(MedicineDO::getDayDosage, reqVO.getDayDosage())
                .eqIfPresent(MedicineDO::getOneDosage, reqVO.getOneDosage())
                .eqIfPresent(MedicineDO::getInventoryWarnValue, reqVO.getInventoryWarnValue())
                .eqIfPresent(MedicineDO::getReportableLoss, reqVO.getReportableLoss())
                .likeIfPresent(MedicineDO::getMedicineEnglishName, reqVO.getMedicineEnglishName())
                .likeIfPresent(MedicineDO::getProductName, reqVO.getProductName())
                .eqIfPresent(MedicineDO::getMarketApprovalHolder, reqVO.getMarketApprovalHolder())
                .eqIfPresent(MedicineDO::getMarketApprovalHolderAddress, reqVO.getMarketApprovalHolderAddress())
                .eqIfPresent(MedicineDO::getProductAddress, reqVO.getProductAddress())
                .eqIfPresent(MedicineDO::getMedicineCategory, reqVO.getMedicineCategory())
                .eqIfPresent(MedicineDO::getMedicineStandardCodeRemark, reqVO.getMedicineStandardCodeRemark())
                .eqIfPresent(MedicineDO::getMedicineAlias, reqVO.getMedicineAlias())
                .eqIfPresent(MedicineDO::getPackageUnit, reqVO.getPackageUnit())
                .betweenIfPresent(MedicineDO::getApprovalDate, reqVO.getApprovalDate())
                .eqIfPresent(MedicineDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
                .eqIfPresent(MedicineDO::getHasExpireDateBatch, reqVO.getHasExpireDateBatch())
                .eqIfPresent(MedicineDO::getMinMeasurementUnit, reqVO.getMinMeasurementUnit())
                .eqIfPresent(MedicineDO::getUnitConversionRatio, reqVO.getUnitConversionRatio())
                .eqIfPresent(MedicineDO::getBelongSensitive, reqVO.getBelongSensitive())
                .eqIfPresent(MedicineDO::getPaychoactieDrug, reqVO.getPaychoactieDrug())
                .eqIfPresent(MedicineDO::getPrisonId, reqVO.getPrisonId())
                .orderByDesc(MedicineDO::getUpdateTime));
    }

    /**
     * 比较替换的方式更新药品库存
     *
     * @param medicineId         药品id
     * @param sourceInventoryNum 原库存
     * @param targetInventoryNum 目标库存
     */
    default void compareAndSetMedicineInventoryNum(String medicineId, BigDecimal sourceInventoryNum, BigDecimal targetInventoryNum) {
        int update = this.update(null, Wrappers.lambdaUpdate(MedicineDO.class)
                .set(MedicineDO::getTotalInventoryNum, targetInventoryNum)
                .set(MedicineDO::getUpdateTime, LocalDateTime.now())
                .eq(MedicineDO::getId, medicineId)
                .eq(MedicineDO::getTotalInventoryNum, sourceInventoryNum));
        BizAssert.isTrue(update == 1, "该药品id[" + medicineId + "]库存数量同时被其他操作修改，操作失败请重试");
    }

}
