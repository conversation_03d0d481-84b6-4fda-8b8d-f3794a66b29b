package com.rs.module.ihc.handler.ipm.casetl.dic;

import com.rs.framework.common.handler.AbstractHandler;
import com.rs.module.ihc.controller.admin.ipm.vo.CaseTemplateDicRespV2VO;
import com.rs.module.ihc.controller.admin.ipm.vo.CaseTemplateDicRespVO;
import org.springframework.stereotype.Component;

/**
 * @ClassName HxHandler
 * @Description 呼吸
 * <AUTHOR>
 * @Date 2025/3/21 17:13
 * @Version 1.0
 */
@Component
public class HxHandler extends AbstractHandler<CaseTemplateDicRespV2VO> {
    @Override
    protected CaseTemplateDicRespV2VO doHandle(CaseTemplateDicRespV2VO input) {
        //呼吸10至40
        for (int i = 10; i <= 40; i++) {
            CaseTemplateDicRespVO caseTemplateDicRespVO = new CaseTemplateDicRespVO();
            caseTemplateDicRespVO.setLable(String.valueOf(i));
            input.getHx().add(caseTemplateDicRespVO);
        }
        return input;
    }
}
