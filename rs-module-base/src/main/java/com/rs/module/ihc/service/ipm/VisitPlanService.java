package com.rs.module.ihc.service.ipm;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.VisitPlanDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 所内就医-现场巡诊计划 Service 接口
 *
 * <AUTHOR>
 */
public interface VisitPlanService extends IBaseService<VisitPlanDO>{

    /**
     * 创建所内就医-现场巡诊计划
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createVisitPlan(@Valid VisitPlanSaveReqVO createReqVO);

    /**
     * 更新所内就医-现场巡诊计划
     *
     * @param updateReqVO 更新信息
     */
    void updateVisitPlan(@Valid VisitPlanSaveReqVO updateReqVO);

    /**
     * 删除所内就医-现场巡诊计划
     *
     * @param id 编号
     */
    void deleteVisitPlan(String id);

    /**
     * 获得所内就医-现场巡诊计划
     *
     * @param id 编号
     * @return 所内就医-现场巡诊计划
     */
    VisitPlanDO getVisitPlan(String id);

    /**
    * 获得所内就医-现场巡诊计划分页
    *
    * @param pageReqVO 分页查询
    * @return 所内就医-现场巡诊计划分页
    */
    PageResult<VisitPlanDO> getVisitPlanPage(VisitPlanPageReqVO pageReqVO);

    /**
    * 获得所内就医-现场巡诊计划列表
    *
    * @param listReqVO 查询条件
    * @return 所内就医-现场巡诊计划列表
    */
    List<VisitPlanDO> getVisitPlanList(VisitPlanListReqVO listReqVO);


}
