package com.rs.module.ihc.controller.admin.ipm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;

@ApiModel(description = "管理后台 - 时间范围 Request VO")
@Data
public class DateRangVO {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("0全部1今天2昨天3近一周")
    private String rangType;


    /**
     * 根据rangType计算日期范围
     * @return Date数组，[开始时间, 结束时间]，如果是全部则返回null
     */
    public Date[] calculateDateRange() {
        LocalDate today = LocalDate.now();
        LocalDateTime startDateTime;
        LocalDateTime endDateTime;
        switch (rangType) {
            case "0": // 全部
                return null;
            case "1": // 今天
                startDateTime = today.atStartOfDay();
                endDateTime = today.atTime(LocalTime.MAX);
                break;
            case "2": // 昨天
                LocalDate yesterday = today.minusDays(1);
                startDateTime = yesterday.atStartOfDay();
                endDateTime = yesterday.atTime(LocalTime.MAX);
                break;
            case "3": // 近一周
                LocalDate weekAgo = today.minusDays(6); // 包含今天共7天
                startDateTime = weekAgo.atStartOfDay();
                endDateTime = today.atTime(LocalTime.MAX);
                break;
            default:
                return null;
        }

        Date startDate = Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant());

        return new Date[]{startDate, endDate};
    }

}
