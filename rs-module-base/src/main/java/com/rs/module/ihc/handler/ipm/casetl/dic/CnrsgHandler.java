package com.rs.module.ihc.handler.ipm.casetl.dic;

import com.rs.framework.common.handler.AbstractHandler;
import com.rs.module.ihc.controller.admin.ipm.vo.CaseTemplateDicRespV2VO;
import com.rs.module.ihc.controller.admin.ipm.vo.CaseTemplateDicRespVO;
import org.springframework.stereotype.Component;

/**
 * @ClassName CnrsgHandler
 * @Description 身高-成年人
 * <AUTHOR>
 * @Date 2025/3/21 17:13
 * @Version 1.0
 */
@Component
public class CnrsgHandler extends AbstractHandler<CaseTemplateDicRespV2VO> {
    @Override
    protected CaseTemplateDicRespV2VO doHandle(CaseTemplateDicRespV2VO input) {
        //150至230
        for (int i = 150; i <= 230; i++) {
            CaseTemplateDicRespVO caseTemplateDicRespVO = new CaseTemplateDicRespVO();
            caseTemplateDicRespVO.setLable(String.valueOf(i));
            input.getCnrsg().add(caseTemplateDicRespVO);
        }
        return input;
    }
}
