package com.rs.module.ihc.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineListReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicinePageReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineRespVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineSaveReqVO;
import com.rs.module.ihc.entity.pm.MedicineDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 药房管理-药品信息 Service 接口
 *
 * <AUTHOR>
 */
public interface MedicineService extends IBaseService<MedicineDO> {

    /**
     * 创建药房管理-药品信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMedicine(@Valid MedicineSaveReqVO createReqVO);

    /**
     * 更新药房管理-药品信息
     *
     * @param updateReqVO 更新信息
     */
    void updateMedicine(@Valid MedicineSaveReqVO updateReqVO);

    /**
     * 删除药房管理-药品信息
     *
     * @param id 编号
     */
    void deleteMedicine(String id);

    /**
     * 获得药房管理-药品信息
     *
     * @param id 编号
     * @return 药房管理-药品信息
     */
    MedicineDO getMedicine(String id);

    /**
     * 获得药房管理-药品信息
     *
     * @param id 编号
     * @return 药房管理-药品信息
     */
    MedicineRespVO getMedicineDetail(String id);

    /**
    * 获得药房管理-药品信息分页
    *
    * @param pageReqVO 分页查询
    * @return 药房管理-药品信息分页
    */
    PageResult<MedicineDO> getMedicinePage(MedicinePageReqVO pageReqVO);

    /**
    * 获得药房管理-药品信息列表
    *
    * @param listReqVO 查询条件
    * @return 药房管理-药品信息列表
    */
    List<MedicineDO> getMedicineList(MedicineListReqVO listReqVO);

    void getMedicineListByPharmacyId(MedicineListReqVO medicineList);

    void init();

    /**
     * 根据药品的id集合校验是否存在不属于传入监管用户的药品
     * 即检查传入的药品id集合中是否有其他人的专用药
     *
     * @param medicineIds        药品id集合
     * @param supervisedUserCode 监管用户id
     */
    void checkMedicineHasNotBelongUser(List<String> medicineIds, String supervisedUserCode);

    /**
     * 获取唯一的药品规格信息
     * <AUTHOR>
     * @date 2025/7/15 17:30
     * @param [medicineName, specs, prisonId]
     * @return com.rs.module.ihc.entity.pm.MedicineDO
     */
    MedicineDO getOnlyMedicine(String medicineName, String specs, String orgCode);
}
