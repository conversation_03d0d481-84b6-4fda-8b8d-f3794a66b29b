package com.rs.module.ihc.controller.admin.pm.vo;

import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel(description = "管理后台 - 药房管理-药房信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PharmacyPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("药房名称")
    private String pharmacyName;

    @ApiModelProperty("监所id")
    private String prisonId;

}
