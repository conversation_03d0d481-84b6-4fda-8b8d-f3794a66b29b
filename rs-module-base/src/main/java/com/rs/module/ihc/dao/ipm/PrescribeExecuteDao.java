package com.rs.module.ihc.dao.ipm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.PrescribeExecuteDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 所内就医-医嘱/处方执行记录 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface PrescribeExecuteDao extends IBaseDao<PrescribeExecuteDO> {


    default PageResult<PrescribeExecuteDO> selectPage(PrescribeExecutePageReqVO reqVO) {
        Page<PrescribeExecuteDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrescribeExecuteDO> wrapper = new LambdaQueryWrapperX<PrescribeExecuteDO>()
                .likeIfPresent(PrescribeExecuteDO::getAddUserName, reqVO.getAddUserName())
                .likeIfPresent(PrescribeExecuteDO::getUpdateUserName, reqVO.getUpdateUserName())
                .eqIfPresent(PrescribeExecuteDO::getPrescribeId, reqVO.getPrescribeId())
                .eqIfPresent(PrescribeExecuteDO::getSignPicUrl, reqVO.getSignPicUrl())
                .eqIfPresent(PrescribeExecuteDO::getDoseVideoUrl, reqVO.getDoseVideoUrl())
                .eqIfPresent(PrescribeExecuteDO::getSignStatus, reqVO.getSignStatus())
                .betweenIfPresent(PrescribeExecuteDO::getSignTime, reqVO.getSignTime())
                .eqIfPresent(PrescribeExecuteDO::getDoseStatus, reqVO.getDoseStatus())
                .betweenIfPresent(PrescribeExecuteDO::getDoseTime, reqVO.getDoseTime())
                .eqIfPresent(PrescribeExecuteDO::getDoctorAdvice, reqVO.getDoctorAdvice())
                .eqIfPresent(PrescribeExecuteDO::getPrescribeDescribe, reqVO.getPrescribeDescribe())
                .eqIfPresent(PrescribeExecuteDO::getExecuteSource, reqVO.getExecuteSource())
                .eqIfPresent(PrescribeExecuteDO::getMedicalPoliceSfzh, reqVO.getMedicalPoliceSfzh())
                .likeIfPresent(PrescribeExecuteDO::getMedicalPoliceName, reqVO.getMedicalPoliceName())
                .eqIfPresent(PrescribeExecuteDO::getRemark, reqVO.getRemark())
                .eqIfPresent(PrescribeExecuteDO::getRoomId, reqVO.getRoomId())
                .likeIfPresent(PrescribeExecuteDO::getRoomName, reqVO.getRoomName())
                .eqIfPresent(PrescribeExecuteDO::getManagePoliceSfzh, reqVO.getManagePoliceSfzh())
                .likeIfPresent(PrescribeExecuteDO::getManagePoliceName, reqVO.getManagePoliceName())
                .eqIfPresent(PrescribeExecuteDO::getAssistPoliceSfzhs, reqVO.getAssistPoliceSfzhs())
                .eqIfPresent(PrescribeExecuteDO::getAssistPoliceNames, reqVO.getAssistPoliceNames());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(PrescribeExecuteDO::getAddTime);
        }
        Page<PrescribeExecuteDO> prescribeExecutePage = selectPage(page, wrapper);
        return new PageResult<>(prescribeExecutePage.getRecords(), prescribeExecutePage.getTotal());
    }

    default List<PrescribeExecuteDO> selectList(PrescribeExecuteListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrescribeExecuteDO>()
                .likeIfPresent(PrescribeExecuteDO::getAddUserName, reqVO.getAddUserName())
                .likeIfPresent(PrescribeExecuteDO::getUpdateUserName, reqVO.getUpdateUserName())
                .eqIfPresent(PrescribeExecuteDO::getPrescribeId, reqVO.getPrescribeId())
                .eqIfPresent(PrescribeExecuteDO::getSignPicUrl, reqVO.getSignPicUrl())
                .eqIfPresent(PrescribeExecuteDO::getDoseVideoUrl, reqVO.getDoseVideoUrl())
                .eqIfPresent(PrescribeExecuteDO::getSignStatus, reqVO.getSignStatus())
                .betweenIfPresent(PrescribeExecuteDO::getSignTime, reqVO.getSignTime())
                .eqIfPresent(PrescribeExecuteDO::getDoseStatus, reqVO.getDoseStatus())
                .betweenIfPresent(PrescribeExecuteDO::getDoseTime, reqVO.getDoseTime())
                .eqIfPresent(PrescribeExecuteDO::getDoctorAdvice, reqVO.getDoctorAdvice())
                .eqIfPresent(PrescribeExecuteDO::getPrescribeDescribe, reqVO.getPrescribeDescribe())
                .eqIfPresent(PrescribeExecuteDO::getExecuteSource, reqVO.getExecuteSource())
                .eqIfPresent(PrescribeExecuteDO::getMedicalPoliceSfzh, reqVO.getMedicalPoliceSfzh())
                .likeIfPresent(PrescribeExecuteDO::getMedicalPoliceName, reqVO.getMedicalPoliceName())
                .eqIfPresent(PrescribeExecuteDO::getRemark, reqVO.getRemark())
                .eqIfPresent(PrescribeExecuteDO::getRoomId, reqVO.getRoomId())
                .likeIfPresent(PrescribeExecuteDO::getRoomName, reqVO.getRoomName())
                .eqIfPresent(PrescribeExecuteDO::getManagePoliceSfzh, reqVO.getManagePoliceSfzh())
                .likeIfPresent(PrescribeExecuteDO::getManagePoliceName, reqVO.getManagePoliceName())
                .eqIfPresent(PrescribeExecuteDO::getAssistPoliceSfzhs, reqVO.getAssistPoliceSfzhs())
                .eqIfPresent(PrescribeExecuteDO::getAssistPoliceNames, reqVO.getAssistPoliceNames())
                .orderByDesc(PrescribeExecuteDO::getAddTime));
    }

    /**
     * 服药台账
     * @param reqVO
     * @return
     */
    List<PrescribeExecuteFytzRespVO> selectFytz(@Param("reqVO") PrescribeExecuteFytzReqVO reqVO);

    List<PrescribeExecuteFytzRespVO> selectFxgzsTodo(@Param("reqVO") PrescribeExecuteFxgzTodoReqVO reqVO);
    List<PrescribeExecuteFytzRespVO> selectFxgzsSuccess(@Param("reqVO") PrescribeExecuteFxgzTodoReqVO reqVO);



}

