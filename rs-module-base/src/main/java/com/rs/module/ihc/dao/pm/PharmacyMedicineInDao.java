package com.rs.module.ihc.dao.pm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineInListReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineInPageReqVO;
import com.rs.module.ihc.entity.pm.PharmacyMedicineInDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
* 大药房管理-药品入库批次 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PharmacyMedicineInDao extends IBaseDao<PharmacyMedicineInDO> {


    default PageResult<PharmacyMedicineInDO> selectPage(PharmacyMedicineInPageReqVO reqVO) {
        Page<PharmacyMedicineInDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PharmacyMedicineInDO> wrapper = new LambdaQueryWrapperX<PharmacyMedicineInDO>()
            .eqIfPresent(PharmacyMedicineInDO::getMedicineId, reqVO.getMedicineId())
            .eqIfPresent(PharmacyMedicineInDO::getInNum, reqVO.getInNum())
            .eqIfPresent(PharmacyMedicineInDO::getBatchCode, reqVO.getBatchCode())
            .eqIfPresent(PharmacyMedicineInDO::getSupplierId, reqVO.getSupplierId())
            .likeIfPresent(PharmacyMedicineInDO::getSupplierName, reqVO.getSupplierName())
            .eqIfPresent(PharmacyMedicineInDO::getVoucherCode, reqVO.getVoucherCode())
            .eqIfPresent(PharmacyMedicineInDO::getPurchasePrice, reqVO.getPurchasePrice())
            .eqIfPresent(PharmacyMedicineInDO::getWholesalePrice, reqVO.getWholesalePrice())
            .eqIfPresent(PharmacyMedicineInDO::getTransferPrice, reqVO.getTransferPrice())
            .eqIfPresent(PharmacyMedicineInDO::getRetailPrice, reqVO.getRetailPrice())
            .betweenIfPresent(PharmacyMedicineInDO::getInDate, reqVO.getInDate())
            .betweenIfPresent(PharmacyMedicineInDO::getExpireDate, reqVO.getExpireDate())
            .eqIfPresent(PharmacyMedicineInDO::getInStorageMethod, reqVO.getInStorageMethod())
            .eqIfPresent(PharmacyMedicineInDO::getTransferMethod, reqVO.getTransferMethod())
            .eqIfPresent(PharmacyMedicineInDO::getMedicinePlace, reqVO.getMedicinePlace())
            .eqIfPresent(PharmacyMedicineInDO::getInventoryNum, reqVO.getInventoryNum())
            .eqIfPresent(PharmacyMedicineInDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
            .eqIfPresent(PharmacyMedicineInDO::getInStatus, reqVO.getInStatus())
            .eqIfPresent(PharmacyMedicineInDO::getWorkflowEnd, reqVO.getWorkflowEnd())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PharmacyMedicineInDO::getAddTime);
        }
        Page<PharmacyMedicineInDO> pharmacyMedicineInPage = selectPage(page, wrapper);
        return new PageResult<>(pharmacyMedicineInPage.getRecords(), pharmacyMedicineInPage.getTotal());
    }
    default List<PharmacyMedicineInDO> selectList(PharmacyMedicineInListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PharmacyMedicineInDO>()
            .eqIfPresent(PharmacyMedicineInDO::getMedicineId, reqVO.getMedicineId())
            .eqIfPresent(PharmacyMedicineInDO::getInNum, reqVO.getInNum())
            .eqIfPresent(PharmacyMedicineInDO::getBatchCode, reqVO.getBatchCode())
            .eqIfPresent(PharmacyMedicineInDO::getSupplierId, reqVO.getSupplierId())
            .likeIfPresent(PharmacyMedicineInDO::getSupplierName, reqVO.getSupplierName())
            .eqIfPresent(PharmacyMedicineInDO::getVoucherCode, reqVO.getVoucherCode())
            .eqIfPresent(PharmacyMedicineInDO::getPurchasePrice, reqVO.getPurchasePrice())
            .eqIfPresent(PharmacyMedicineInDO::getWholesalePrice, reqVO.getWholesalePrice())
            .eqIfPresent(PharmacyMedicineInDO::getTransferPrice, reqVO.getTransferPrice())
            .eqIfPresent(PharmacyMedicineInDO::getRetailPrice, reqVO.getRetailPrice())
            .betweenIfPresent(PharmacyMedicineInDO::getInDate, reqVO.getInDate())
            .betweenIfPresent(PharmacyMedicineInDO::getExpireDate, reqVO.getExpireDate())
            .eqIfPresent(PharmacyMedicineInDO::getInStorageMethod, reqVO.getInStorageMethod())
            .eqIfPresent(PharmacyMedicineInDO::getTransferMethod, reqVO.getTransferMethod())
            .eqIfPresent(PharmacyMedicineInDO::getMedicinePlace, reqVO.getMedicinePlace())
            .eqIfPresent(PharmacyMedicineInDO::getInventoryNum, reqVO.getInventoryNum())
            .eqIfPresent(PharmacyMedicineInDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
            .eqIfPresent(PharmacyMedicineInDO::getInStatus, reqVO.getInStatus())
            .eqIfPresent(PharmacyMedicineInDO::getWorkflowEnd, reqVO.getWorkflowEnd())
        .orderByDesc(PharmacyMedicineInDO::getAddTime));    }



    /**
     * 获取存在批次过期的药品id
     * <p>
     * 当过期时间到了并且库存已经为0的批次就不算存在过期药品的批次
     *
     * @param expireDate 过期日期
     * @param medicineIdList 药品id集合 可以为空
     * @return 批次过期的药品id集合
     */
    List<String> getExistExpireBatchMedicineId(@Param("expireDate") Date expireDate,
                                               @Param("medicineIdList") List<String> medicineIdList);

    /**
     * 根据多个药品集合获取每个药品第一个没有过期的药品批次
     *
     * @param medicineIdList 药品id集合
     * @param expireDate 过期日期
     * @return 药品批次
     */
    List<PharmacyMedicineInDO> selectFirstNoExpireMedicineBatch(@Param("medicineIdList") Collection<String> medicineIdList, @Param("expireDate") Date expireDate);






}
