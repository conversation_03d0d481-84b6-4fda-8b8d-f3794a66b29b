package com.rs.module.ihc.dao.md;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.controller.admin.md.vo.MedicineDeliveryApplyAppPageReqVO;
import com.rs.module.ihc.controller.admin.md.vo.MedicineDeliveryApplyAppRespVO;
import com.rs.module.ihc.controller.admin.md.vo.MedicineDeliveryApplyListReqVO;
import com.rs.module.ihc.controller.admin.md.vo.MedicineDeliveryApplyPageReqVO;
import com.rs.module.ihc.entity.md.MedicineDeliveryApplyDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 药品顾送管理-药品顾送申请 Dao
*
* <AUTHOR>
*/
@Mapper
public interface MedicineDeliveryApplyDao extends IBaseDao<MedicineDeliveryApplyDO> {


    default PageResult<MedicineDeliveryApplyDO> selectPage(MedicineDeliveryApplyPageReqVO reqVO) {
        Page<MedicineDeliveryApplyDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<MedicineDeliveryApplyDO> wrapper = new LambdaQueryWrapperX<MedicineDeliveryApplyDO>()
            .eqIfPresent(MedicineDeliveryApplyDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(MedicineDeliveryApplyDO::getJgryxm, reqVO.getJgryxm())
            .likeIfPresent(MedicineDeliveryApplyDO::getApplyMedicineName, reqVO.getApplyMedicineName())
            .eqIfPresent(MedicineDeliveryApplyDO::getApplyDosageForm, reqVO.getApplyDosageForm())
            .eqIfPresent(MedicineDeliveryApplyDO::getApplySpecs, reqVO.getApplySpecs())
            .eqIfPresent(MedicineDeliveryApplyDO::getApplyProductUnit, reqVO.getApplyProductUnit())
            .eqIfPresent(MedicineDeliveryApplyDO::getApplyApprovalNum, reqVO.getApplyApprovalNum())
            .eqIfPresent(MedicineDeliveryApplyDO::getApplyOriginalApprovalNum, reqVO.getApplyOriginalApprovalNum())
            .eqIfPresent(MedicineDeliveryApplyDO::getApplyTotalNum, reqVO.getApplyTotalNum())
            .betweenIfPresent(MedicineDeliveryApplyDO::getExpectedStartDate, reqVO.getExpectedStartDate())
            .betweenIfPresent(MedicineDeliveryApplyDO::getExpectedEndDate, reqVO.getExpectedEndDate())
            .eqIfPresent(MedicineDeliveryApplyDO::getDrugSource, reqVO.getDrugSource())
            .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryReason, reqVO.getDeliveryReason())
            .eqIfPresent(MedicineDeliveryApplyDO::getRemark, reqVO.getRemark())
            .eqIfPresent(MedicineDeliveryApplyDO::getIsConsistent, reqVO.getIsConsistent())
            .eqIfPresent(MedicineDeliveryApplyDO::getReasonForInconsistency, reqVO.getReasonForInconsistency())
            .likeIfPresent(MedicineDeliveryApplyDO::getDeliveryMedicineName, reqVO.getDeliveryMedicineName())
            .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryDosageForm, reqVO.getDeliveryDosageForm())
            .eqIfPresent(MedicineDeliveryApplyDO::getDeliverySpecs, reqVO.getDeliverySpecs())
            .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryProductUnit, reqVO.getDeliveryProductUnit())
            .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryApprovalNum, reqVO.getDeliveryApprovalNum())
            .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryOriginalApprovalNum, reqVO.getDeliveryOriginalApprovalNum())
            .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryTotalNum, reqVO.getDeliveryTotalNum())
            .betweenIfPresent(MedicineDeliveryApplyDO::getExpiryStartDate, reqVO.getExpiryStartDate())
            .betweenIfPresent(MedicineDeliveryApplyDO::getExpiryEndDate, reqVO.getExpiryEndDate())
            .eqIfPresent(MedicineDeliveryApplyDO::getImgUrl, reqVO.getImgUrl())
            .betweenIfPresent(MedicineDeliveryApplyDO::getDeliveryDate, reqVO.getDeliveryDate())
            .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryRemark, reqVO.getDeliveryRemark())
            .eqIfPresent(MedicineDeliveryApplyDO::getExceptionReason, reqVO.getExceptionReason())
            .eqIfPresent(MedicineDeliveryApplyDO::getStatus, reqVO.getStatus())
            .eqIfPresent(MedicineDeliveryApplyDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(MedicineDeliveryApplyDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(MedicineDeliveryApplyDO::getAddTime);
        }
        Page<MedicineDeliveryApplyDO> medicineDeliveryApplyPage = selectPage(page, wrapper);
        return new PageResult<>(medicineDeliveryApplyPage.getRecords(), medicineDeliveryApplyPage.getTotal());
    }
    default List<MedicineDeliveryApplyDO> selectList(MedicineDeliveryApplyListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MedicineDeliveryApplyDO>()
            .eqIfPresent(MedicineDeliveryApplyDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(MedicineDeliveryApplyDO::getJgryxm, reqVO.getJgryxm())
            .likeIfPresent(MedicineDeliveryApplyDO::getApplyMedicineName, reqVO.getApplyMedicineName())
            .eqIfPresent(MedicineDeliveryApplyDO::getApplyDosageForm, reqVO.getApplyDosageForm())
            .eqIfPresent(MedicineDeliveryApplyDO::getApplySpecs, reqVO.getApplySpecs())
            .eqIfPresent(MedicineDeliveryApplyDO::getApplyProductUnit, reqVO.getApplyProductUnit())
            .eqIfPresent(MedicineDeliveryApplyDO::getApplyApprovalNum, reqVO.getApplyApprovalNum())
            .eqIfPresent(MedicineDeliveryApplyDO::getApplyOriginalApprovalNum, reqVO.getApplyOriginalApprovalNum())
            .eqIfPresent(MedicineDeliveryApplyDO::getApplyTotalNum, reqVO.getApplyTotalNum())
            .betweenIfPresent(MedicineDeliveryApplyDO::getExpectedStartDate, reqVO.getExpectedStartDate())
            .betweenIfPresent(MedicineDeliveryApplyDO::getExpectedEndDate, reqVO.getExpectedEndDate())
            .eqIfPresent(MedicineDeliveryApplyDO::getDrugSource, reqVO.getDrugSource())
            .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryReason, reqVO.getDeliveryReason())
            .eqIfPresent(MedicineDeliveryApplyDO::getRemark, reqVO.getRemark())
            .eqIfPresent(MedicineDeliveryApplyDO::getIsConsistent, reqVO.getIsConsistent())
            .eqIfPresent(MedicineDeliveryApplyDO::getReasonForInconsistency, reqVO.getReasonForInconsistency())
            .likeIfPresent(MedicineDeliveryApplyDO::getDeliveryMedicineName, reqVO.getDeliveryMedicineName())
            .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryDosageForm, reqVO.getDeliveryDosageForm())
            .eqIfPresent(MedicineDeliveryApplyDO::getDeliverySpecs, reqVO.getDeliverySpecs())
            .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryProductUnit, reqVO.getDeliveryProductUnit())
            .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryApprovalNum, reqVO.getDeliveryApprovalNum())
            .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryOriginalApprovalNum, reqVO.getDeliveryOriginalApprovalNum())
            .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryTotalNum, reqVO.getDeliveryTotalNum())
            .betweenIfPresent(MedicineDeliveryApplyDO::getExpiryStartDate, reqVO.getExpiryStartDate())
            .betweenIfPresent(MedicineDeliveryApplyDO::getExpiryEndDate, reqVO.getExpiryEndDate())
            .eqIfPresent(MedicineDeliveryApplyDO::getImgUrl, reqVO.getImgUrl())
            .betweenIfPresent(MedicineDeliveryApplyDO::getDeliveryDate, reqVO.getDeliveryDate())
            .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryRemark, reqVO.getDeliveryRemark())
            .eqIfPresent(MedicineDeliveryApplyDO::getExceptionReason, reqVO.getExceptionReason())
            .eqIfPresent(MedicineDeliveryApplyDO::getStatus, reqVO.getStatus())
            .eqIfPresent(MedicineDeliveryApplyDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(MedicineDeliveryApplyDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(MedicineDeliveryApplyDO::getAddTime));    }


    /**
     * app接口查询获取
     * <AUTHOR>
     * @date 2025/7/7 19:41
     * @param [page, pageReqVO]
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.rs.module.ihc.controller.admin.md.vo.MedicineDeliveryApplyAppRespVO>
     */
    IPage<MedicineDeliveryApplyAppRespVO> getMedicineDeliveryApplyAppRespVO(@Param("page") Page<MedicineDeliveryApplyAppRespVO> page, @Param("pageReqVO") MedicineDeliveryApplyAppPageReqVO pageReqVO);

}
