package com.rs.module.ihc.service.pm;

import cn.hutool.core.date.DateUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.util.BizAssert;
import com.rs.module.ihc.constant.IhcsDictionaryConstant;
import com.rs.module.ihc.controller.admin.ipm.bo.UpdateMedicineInInventoryNumBO;
import com.rs.module.ihc.controller.admin.ipm.dto.SaveMedicineOutDTO;
import com.rs.module.ihc.controller.admin.ipm.vo.PrescribeMedicineOutReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineOutListReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineOutPageReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineOutSaveReqVO;
import com.rs.module.ihc.dao.pm.MedicineDao;
import com.rs.module.ihc.dao.pm.MedicineOutDao;
import com.rs.module.ihc.entity.pm.MedicineDO;
import com.rs.module.ihc.entity.pm.MedicineInDO;
import com.rs.module.ihc.entity.pm.MedicineOutDO;
import lombok.Data;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 药房管理-药品出库 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MedicineOutServiceImpl extends BaseServiceImpl<MedicineOutDao, MedicineOutDO> implements MedicineOutService {

    @Resource
    private MedicineOutDao medicineOutDao;
    @Resource
    private MedicineInService medicineInService;
    @Resource
    private MedicineService medicineService;
    @Resource
    private MedicineDao medicineDao;


    @Override
    public String createMedicineOut(MedicineOutSaveReqVO createReqVO) {
        // 插入
        MedicineOutDO medicineOut = BeanUtils.toBean(createReqVO, MedicineOutDO.class);
        medicineOutDao.insert(medicineOut);
        // 返回
        return medicineOut.getId();
    }

    @Override
    public void updateMedicineOut(MedicineOutSaveReqVO updateReqVO) {
        // 校验存在
        validateMedicineOutExists(updateReqVO.getId());
        // 更新
        MedicineOutDO updateObj = BeanUtils.toBean(updateReqVO, MedicineOutDO.class);
        medicineOutDao.updateById(updateObj);
    }

    @Override
    public void deleteMedicineOut(String id) {
        // 校验存在
        validateMedicineOutExists(id);
        // 删除
        medicineOutDao.deleteById(id);
    }

    private void validateMedicineOutExists(String id) {
        if (medicineOutDao.selectById(id) == null) {
            throw new ServerException("药房管理-药品出库数据不存在");
        }
    }

    @Override
    public MedicineOutDO getMedicineOut(String id) {
        return medicineOutDao.selectById(id);
    }

    @Override
    public PageResult<MedicineOutDO> getMedicineOutPage(MedicineOutPageReqVO pageReqVO) {
        return medicineOutDao.selectPage(pageReqVO);
    }

    @Override
    public List<MedicineOutDO> getMedicineOutList(MedicineOutListReqVO listReqVO) {
        return medicineOutDao.selectList(listReqVO);
    }
    @Override
    public void batchSaveMedicineOutBySourceData(List<SaveMedicineOutDTO> medicineOutList, String medicineId, List<MedicineInDO> dataSourceIhcsMedicineIn) {
        SessionUser user = SessionUserUtil.getSessionUser();
        MedicineDO ihcsMedicine = medicineService.getBaseMapper().selectById(medicineId);
        BizAssert.notNull(ihcsMedicine, String.format("药品不存在，id：%s", medicineId));
        Map<String, MedicineInDO> ihcsMedicineInMap = dataSourceIhcsMedicineIn.stream()
                .collect(Collectors.toMap(MedicineInDO::getId, Function.identity()));
        // 药品库存数量
        final BigDecimal sourceInventoryNum = ihcsMedicine.getTotalInventoryNum();
        BigDecimal targetInventoryNum = sourceInventoryNum;
        // 记录每个批次的扣减记录
        Map<String, UpdateMedicineInInventoryNumBO> batchInventoryNumMap = new LinkedHashMap<>();
        List<MedicineOutDO> ihcsMedicineOutList = new ArrayList<>();
        Date nowDate = new Date();
        for (SaveMedicineOutDTO saveMedicineOutDTO : medicineOutList) {
            String medicineInId = saveMedicineOutDTO.getMedicineInId();
            MedicineInDO ihcsMedicineIn = ihcsMedicineInMap.get(medicineInId);
            BizAssert.notNull(ihcsMedicineIn, "入库批次id[" + medicineInId + "]不存在");
            String batchCode = ihcsMedicineIn.getBatchCode();
            BizAssert.isTrue(Objects.equals(ihcsMedicineIn.getMedicineId(), medicineId),
                    "入库批次[" + batchCode + "]不属于该药品");
            long l = DateUtil.betweenDay(nowDate, ihcsMedicineIn.getExpireDate(), true);
            BizAssert.isTrue(l >= 0, "入库批次[" + batchCode + "]已过期，无法出库");
            // 出库数量
            BigDecimal outNum = saveMedicineOutDTO.getOutNum();
            // 批次库存数量
            BigDecimal batchSourceInventoryNum = ihcsMedicineIn.getInventoryNum();
            // 药品总库存扣减
            targetInventoryNum = targetInventoryNum.subtract(outNum);
            // 这里是由于可能出现批量出库时，一个批次在一次请求中分两个记录来出
            UpdateMedicineInInventoryNumBO updateMedicineInInventoryNumBO =
                    batchInventoryNumMap.computeIfAbsent(medicineInId, key -> UpdateMedicineInInventoryNumBO.builder()
                            .id(medicineInId)
                            .sourceInventoryNum(batchSourceInventoryNum)
                            .targetInventoryNum(batchSourceInventoryNum)
                            .build());
            // 批次库存数量扣减
            BigDecimal batchTargetInventoryNum = updateMedicineInInventoryNumBO.getTargetInventoryNum().subtract(outNum);
            BizAssert.isTrue(batchTargetInventoryNum.signum() >= 0, "批次[" + batchCode + "]出库数量不能大于库存数量");
            updateMedicineInInventoryNumBO.setTargetInventoryNum(batchTargetInventoryNum);
            // 记录存库
            MedicineOutDO ihcsMedicineOut = this.convertToIhcsMedicineOut(saveMedicineOutDTO);
            ihcsMedicineOut.setMedicineId(medicineId);
            ihcsMedicineOut.setInventoryNum(batchTargetInventoryNum);
            ihcsMedicineOut.setTotalInventoryNum(targetInventoryNum);
            ihcsMedicineOut.setUserid(user.getIdCard());
            ihcsMedicineOut.setUserName(user.getName());
            ihcsMedicineOutList.add(ihcsMedicineOut);
        }
        // 批次库存更新
        medicineInService.batchCompareAndSetInventoryNum(batchInventoryNumMap.values());
        // 药品库存更新
        medicineDao.compareAndSetMedicineInventoryNum(medicineId, sourceInventoryNum, targetInventoryNum);

        // 出库记录保存
        this.saveBatch(ihcsMedicineOutList);
    }



    private MedicineOutDO convertToIhcsMedicineOut(SaveMedicineOutDTO saveMedicineOutDTO) {
        return MedicineOutDO.builder()
                .medicineInId(saveMedicineOutDTO.getMedicineInId())
                .outStorageReason(saveMedicineOutDTO.getOutStorageReason())
                .outNum(saveMedicineOutDTO.getOutNum())
                .dispensaryUnit(saveMedicineOutDTO.getDispensaryUnit())
                .dispensaryTime(saveMedicineOutDTO.getDispensaryTime())
                .remark(saveMedicineOutDTO.getRemark())
                .build();
    }

    /**
     * 获取药品的批次自动扣减批次信息
     * 每个药品都有对应的入库批次，按照先进先出的原则排列批次，
     * 扣减的数量可能有一个批次出库就够了，可能需要多个批次出库，这样就需要组装药品出库数量和批次的关系
     *
     * @param prescribeMedicineOutInfoList 药品扣减信息
     * @param remark                       备注信息
     */
    private List<MedicineOutInfo> getBatchAutoMedicineOutInfo(Collection<PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo>
                                                                                              prescribeMedicineOutInfoList, String remark) {
        // 出现同一种药分两个记录出的话需要把出库数量合并比较批次的数量
        Collection<PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo> noDuplicateMedicineOutInfoList =
                prescribeMedicineOutInfoList.stream()
                        .collect(Collectors.toMap(PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo::getMedicineId,
                                Function.identity(), (a, b) -> {
                                    a.setOutNum(a.getOutNum().add(b.getOutNum()));
                                    return a;
                                }, LinkedHashMap::new))
                        .values();
        // 先确保药品的库存足够
        this.checkMedicineInventory(noDuplicateMedicineOutInfoList);
        // 先去查每种药的最先入库的未过期且有库存的批次，一般处方出库的一个批次足以，不够的话每种药再单独考虑
        Map<String, MedicineInDO> medicineInMap = medicineInService
                .selectFirstNoExpireMedicineBatch(prescribeMedicineOutInfoList.stream()
                        .map(PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo::getMedicineId)
                        .collect(Collectors.toList()), new Date())
                .stream()
                .collect(Collectors.toMap(MedicineInDO::getMedicineId, Function.identity()));
        List<MedicineOutInfo> medicineOutInfoList = new ArrayList<>();
        // 取药时间
        Date nowDateTime = new Date();
        for (PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo prescribeMedicineOutInfo : prescribeMedicineOutInfoList) {
            String medicineId = prescribeMedicineOutInfo.getMedicineId();
            MedicineInDO ihcsMedicineIn = medicineInMap.get(medicineId);
            BizAssert.notNull(ihcsMedicineIn, String.format("药品id[%s]对应的出库批次获取为空", medicineId));
            String medicineInId = ihcsMedicineIn.getId();
            // 当前批次数量足够的情况下
            final BigDecimal outNum = prescribeMedicineOutInfo.getOutNum();
            if (ihcsMedicineIn.getInventoryNum().compareTo(outNum) >= 0) {
                MedicineOutInfo medicineOutInfo = new MedicineOutInfo();
                medicineOutInfo.setMedicineId(medicineId);
                medicineOutInfo.setMedicineInList(Collections.singletonList(ihcsMedicineIn));
                SaveMedicineOutDTO saveMedicineOutDTO = this.createSaveMedicineOutDTO(outNum, nowDateTime,
                        medicineInId, remark);
                medicineOutInfo.setMedicineOutList(Collections.singletonList(saveMedicineOutDTO));
                medicineOutInfoList.add(medicineOutInfo);
            } else {
                // 当前批次数量不够的情况下
                List<MedicineInDO> ihcsMedicineIns = medicineInService.lambdaQuery()
                        .eq(MedicineInDO::getMedicineId, medicineId)
                        .ge(MedicineInDO::getExpireDate, LocalDate.now())
                        .gt(MedicineInDO::getInventoryNum, 0)
                        .orderByAsc(MedicineInDO::getId)
                        .list();
                // 批次需要扣减的值
                BigDecimal needOutNum = outNum;
                MedicineOutInfo medicineOutInfo = new MedicineOutInfo();
                medicineOutInfo.setMedicineId(medicineId);
                final List<MedicineInDO> ihcsMedicineInSourceData = new ArrayList<>();
                final List<SaveMedicineOutDTO> saveMedicineOutDTOList = new ArrayList<>();
                medicineOutInfo.setMedicineInList(ihcsMedicineInSourceData);
                medicineOutInfo.setMedicineOutList(saveMedicineOutDTOList);
                for (MedicineInDO medicineIn : ihcsMedicineIns) {
                    if (needOutNum.compareTo(BigDecimal.ZERO) <= 0) {
                        break;
                    }
                    BigDecimal inventoryNum = medicineIn.getInventoryNum();
                    String medicineInId0 = medicineIn.getId();
                    ihcsMedicineInSourceData.add(medicineIn);
                    // 当前批次库存足够
                    if (inventoryNum.compareTo(needOutNum) >= 0) {
                        saveMedicineOutDTOList.add(this.createSaveMedicineOutDTO(needOutNum, nowDateTime,
                                medicineInId0, remark));
                        needOutNum = BigDecimal.ZERO;
                    } else {
                        // 当前批次库存不够
                        saveMedicineOutDTOList.add(this.createSaveMedicineOutDTO(inventoryNum, nowDateTime,
                                medicineInId0, remark));
                        // 下个批次所需要扣减得数量
                        needOutNum = needOutNum.subtract(inventoryNum);
                    }
                }
                BizAssert.isTrue(needOutNum.compareTo(BigDecimal.ZERO) <= 0,
                        String.format("药品id[%s]的出库数量[%s]库存不足", prescribeMedicineOutInfo.getMedicineId(), outNum));
                medicineOutInfoList.add(medicineOutInfo);
            }
        }
        return medicineOutInfoList;
    }

    /**
     * 检测药品库存
     *
     * @param prescribeMedicineOutInfoList 出库药品信息
     */
    private void checkMedicineInventory(Collection<PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo> prescribeMedicineOutInfoList) {
        Map<String, MedicineDO> medicineInventoryMap = this.medicineService.getBaseMapper().selectBatchIds(
                        prescribeMedicineOutInfoList.stream()
                                .map(PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo::getMedicineId)
                                .collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(MedicineDO::getId, Function.identity()));

        for (PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo prescribeMedicineOutInfo : prescribeMedicineOutInfoList) {
            String id = prescribeMedicineOutInfo.getMedicineId();
            MedicineDO ihcsMedicine = medicineInventoryMap.get(id);
            BizAssert.notNull(ihcsMedicine, String.format("药品id[%s]不存在或者已删除", id));
            BigDecimal totalInventoryNum = ihcsMedicine.getTotalInventoryNum();
            BizAssert.isTrue(totalInventoryNum.compareTo(prescribeMedicineOutInfo.getOutNum()) >= 0, String.format("药品[%s]库存不足", ihcsMedicine.getMedicineName()));
        }
    }

    private SaveMedicineOutDTO createSaveMedicineOutDTO(BigDecimal outNum,
                                                        Date nowDateTime,
                                                        String medicineInId,
                                                        String remark) {
        // 取药单位
        String prisonName = SessionUserUtil.getSessionUser().getOrgName();
        return SaveMedicineOutDTO.builder()
                .outNum(outNum)
                .outStorageReason(IhcsDictionaryConstant.PRESCRIBE_USE_MEDICINE)
                .dispensaryTime(nowDateTime)
                .dispensaryUnit(prisonName)
                .medicineInId(medicineInId)
                .remark(remark)
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAutoMedicalOut(Collection<PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo> prescribeMedicineOutInfoList,
                                    String remark) {
        // 判断并且获取药品的出库批次信息
        List<MedicineOutInfo> medicineOutInfoList = this.getBatchAutoMedicineOutInfo(prescribeMedicineOutInfoList, remark);
        // 进行库存扣减
        medicineOutInfoList.forEach(medicineOutInfo ->
                this.batchSaveMedicineOutBySourceData(medicineOutInfo.getMedicineOutList(),
                        medicineOutInfo.getMedicineId(), medicineOutInfo.getMedicineInList()));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveMedicineOut(List<SaveMedicineOutDTO> medicineOutList, String medicineId) {
        List<MedicineInDO> ihcsMedicineIns = medicineInService.lambdaQuery()
                .in(MedicineInDO::getId, medicineOutList.stream()
                        .map(SaveMedicineOutDTO::getMedicineInId)
                        .collect(Collectors.toList()))
                .list();
        this.batchSaveMedicineOutBySourceData(medicineOutList, medicineId, ihcsMedicineIns);
    }


    @Data
    private static class MedicineOutInfo {
        /**
         * 出库批次数据
         */
        private List<SaveMedicineOutDTO> medicineOutList;

        /**
         * 药品id
         */
        private String medicineId;

        /**
         * 药品入库信息
         */
        private List<MedicineInDO> medicineInList;
    }
}
