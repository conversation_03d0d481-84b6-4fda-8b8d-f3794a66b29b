package com.rs.module.ihc.service.ipm;

import javax.validation.*;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.PrescribeMedicineDO;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 所内就医-处方-药方信息 Service 接口
 *
 * <AUTHOR>
 */
public interface PrescribeMedicineService extends IBaseService<PrescribeMedicineDO>{

    /**
     * 创建所内就医-处方-药方信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrescribeMedicine(@Valid PrescribeMedicineSaveReqVO createReqVO);

    /**
     * 更新所内就医-处方-药方信息
     *
     * @param updateReqVO 更新信息
     */
    void updatePrescribeMedicine(@Valid PrescribeMedicineSaveReqVO updateReqVO);

    /**
     * 删除所内就医-处方-药方信息
     *
     * @param id 编号
     */
    void deletePrescribeMedicine(String id);

    /**
     * 获得所内就医-处方-药方信息
     *
     * @param id 编号
     * @return 所内就医-处方-药方信息
     */
    PrescribeMedicineDO getPrescribeMedicine(String id);



}
