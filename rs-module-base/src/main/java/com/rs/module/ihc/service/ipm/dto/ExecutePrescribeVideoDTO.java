package com.rs.module.ihc.service.ipm.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class ExecutePrescribeVideoDTO {
    /**
     * 处方/医嘱执行id
     */
    @NotNull(message = "处方/医嘱执行id不能为空")
    @ApiModelProperty(value = "处方/医嘱执行id", required = true)
    private String id;

    /**
     * 服药视频地址
     */
    @NotNull(message = "服药视频地址不能为空")
    @ApiModelProperty(value = "服药视频地址", required = true)
    private String doseVideoUrl;
}
