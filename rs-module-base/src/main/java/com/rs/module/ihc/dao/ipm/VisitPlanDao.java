package com.rs.module.ihc.dao.ipm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.ipm.VisitPlanDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 所内就医-现场巡诊计划 Dao
*
* <AUTHOR>
*/
@Mapper
public interface VisitPlanDao extends IBaseDao<VisitPlanDO> {


    default PageResult<VisitPlanDO> selectPage(VisitPlanPageReqVO reqVO) {
        Page<VisitPlanDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<VisitPlanDO> visitPlanPage = selectPage(page, new LambdaQueryWrapperX<VisitPlanDO>()
            .eqIfPresent(VisitPlanDO::getSource, reqVO.getSource())
            .eqIfPresent(VisitPlanDO::getSourceId, reqVO.getSourceId())
            .eqIfPresent(VisitPlanDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(VisitPlanDO::getRybh, reqVO.getRybh())
            .eqIfPresent(VisitPlanDO::getRyxm, reqVO.getRyxm())
            .eqIfPresent(VisitPlanDO::getReason, reqVO.getReason())
            .betweenIfPresent(VisitPlanDO::getStartDate, reqVO.getStartDate())
            .betweenIfPresent(VisitPlanDO::getEndDate, reqVO.getEndDate())
            .eqIfPresent(VisitPlanDO::getFrequency, reqVO.getFrequency())
            .eqIfPresent(VisitPlanDO::getPrisonId, reqVO.getPrisonId())
            .eqIfPresent(VisitPlanDO::getStatus, reqVO.getStatus())
            .betweenIfPresent(VisitPlanDO::getLastVisitCreateTime, reqVO.getLastVisitCreateTime())
            .orderByDesc(VisitPlanDO::getId));
            return new PageResult<>(visitPlanPage.getRecords(), visitPlanPage.getTotal());
    }
    default List<VisitPlanDO> selectList(VisitPlanListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<VisitPlanDO>()
            .eqIfPresent(VisitPlanDO::getSource, reqVO.getSource())
            .eqIfPresent(VisitPlanDO::getSourceId, reqVO.getSourceId())
            .eqIfPresent(VisitPlanDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(VisitPlanDO::getRybh, reqVO.getRybh())
            .eqIfPresent(VisitPlanDO::getRyxm, reqVO.getRyxm())
            .eqIfPresent(VisitPlanDO::getReason, reqVO.getReason())
            .betweenIfPresent(VisitPlanDO::getStartDate, reqVO.getStartDate())
            .betweenIfPresent(VisitPlanDO::getEndDate, reqVO.getEndDate())
            .eqIfPresent(VisitPlanDO::getFrequency, reqVO.getFrequency())
            .eqIfPresent(VisitPlanDO::getPrisonId, reqVO.getPrisonId())
            .eqIfPresent(VisitPlanDO::getStatus, reqVO.getStatus())
            .betweenIfPresent(VisitPlanDO::getLastVisitCreateTime, reqVO.getLastVisitCreateTime())
        .orderByDesc(VisitPlanDO::getId));    }


    }
