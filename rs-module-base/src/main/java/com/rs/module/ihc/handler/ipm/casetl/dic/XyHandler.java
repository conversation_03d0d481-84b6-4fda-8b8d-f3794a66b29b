package com.rs.module.ihc.handler.ipm.casetl.dic;

import com.rs.framework.common.handler.AbstractHandler;
import com.rs.module.ihc.controller.admin.ipm.vo.CaseTemplateDicRespV2VO;
import com.rs.module.ihc.controller.admin.ipm.vo.CaseTemplateDicRespVO;
import org.springframework.stereotype.Component;

/**
 * @ClassName XyHandler
 * @Description 血氧
 * <AUTHOR>
 * @Date 2025/3/21 17:13
 * @Version 1.0
 */
@Component
public class XyHandler extends AbstractHandler<CaseTemplateDicRespV2VO> {
    @Override
    protected CaseTemplateDicRespV2VO doHandle(CaseTemplateDicRespV2VO input) {
        //血氧70~99
        for (int i = 70; i <= 99; i++) {
            CaseTemplateDicRespVO caseTemplateDicRespVO = new CaseTemplateDicRespVO();
            caseTemplateDicRespVO.setLable(String.valueOf(i));
            input.getXy().add(caseTemplateDicRespVO);
        }
        return input;
    }
}
