package com.rs.module.ihc.controller.admin.pm.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaveMedicineInReqVO {

    /**
     * 入库数量
     */
    @Positive(message = "入库数量必须大于0")
    @NotNull(message = "入库数量不能为空")
    @ApiModelProperty(value = "入库数量", required = true)
    private BigDecimal inNum;

    /**
     * 供药单位，对应ihcs_supplier.id
     */
    @ApiModelProperty(value = "供药单位id")
    private String supplierId;

    /**
     * 供药单位名称
     */
    @ApiModelProperty(value = "供药单位名称")
    private String supplierName;

    /**
     * 凭证号
     */
    @ApiModelProperty(value = "凭证号")
    private String voucherCode;

    /**
     * 进价
     */
    @PositiveOrZero(message = "进价不能小于0")
    @ApiModelProperty(value = "进价")
    private BigDecimal purchasePrice;

    /**
     * 批发价
     */
    @PositiveOrZero(message = "批发价不能小于0")
    @ApiModelProperty(value = "批发价")
    private BigDecimal wholesalePrice;

    /**
     * 调拨价
     */
    @PositiveOrZero(message = "调拨价不能小于0")
    @ApiModelProperty(value = "调拨价")
    private BigDecimal transferPrice;

    /**
     * 零售价
     */
    @PositiveOrZero(message = "零售价不能小于0")
    @ApiModelProperty(value = "零售价")
    private BigDecimal retailPrice;

    /**
     * 入库日期
     */
    @NotNull(message = "入库日期不能为空")
    @ApiModelProperty(value = "入库日期 yyyy-MM-dd", required = true)
    private Date inDate;

    /**
     * 有效期
     */
    @FutureOrPresent(message = "有效期不能小于当前日期")
    @NotNull(message = "有效期不能为空")
    @ApiModelProperty(value = "有效期 yyyy-MM-dd", required = true)
    private Date expireDate;

    /**
     * 入库方式 字典值
     */
    @NotBlank(message = "入库方式不能为空")
    @ApiModelProperty(value = "入库方式 字典值", required = true)
    private String inStorageMethod;

    /**
     * 调拨方式 字典值
     */
    @ApiModelProperty(value = "调拨方式 字典值")
    private String transferMethod;

    /**
     * 药品货位
     */
    @ApiModelProperty(value = "药品货位")
    private String medicinePlace;

}
