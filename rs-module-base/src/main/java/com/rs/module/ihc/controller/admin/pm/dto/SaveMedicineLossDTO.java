package com.rs.module.ihc.controller.admin.pm.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class SaveMedicineLossDTO {

    /**
     * 入库批次id，对应ichs_medicine_in.id
     */
    @NotNull(message = "入库批次id不能为空")
    @ApiModelProperty(value = "入库批次id", required = true)
    private String medicineInId;

    /**
     * 报损原因 字典值
     */
    @NotBlank(message = "报损原因不能为空")
    @ApiModelProperty(value = "报损原因 字典值", required = true)
    private String lossReason;

    /**
     * 报损数量
     */
    @Positive(message = "报损数量不能小于0")
    @NotNull(message = "报损数量不能为空")
    @ApiModelProperty(value = "报损数量", required = true)
    private BigDecimal lossNum;

    /**
     * 报损时间
     */
    @NotNull(message = "报损时间不能为空")
    @ApiModelProperty(value = "报损时间 yyyy-MM-dd HH:mm:ss", required = true)
    private Date lossTime;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
}
