package com.rs.module.ihc.controller.admin.ephe.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 卫生防疫与健康教育-卫生消毒登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DisinfectSaveReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("夏季每周洗热水澡次数")
    private String xjmzxrszcs;

    @ApiModelProperty("每15天热水澡次数")
    private Integer mswtrszcs;

    @ApiModelProperty("每周晾晒被褥次数")
    private Integer mzlsbrcs;

    @ApiModelProperty("每季度拆洗被褥次数")
    private Integer mjdcxbrcs;

    @ApiModelProperty("每周消毒次数")
    private Integer mzxdcs;

    //@ApiModelProperty("登记民警身份证号")
    //@NotEmpty(message = "登记民警身份证号不能为空")
    //private String operatePoliceSfzh;
    //
    //@ApiModelProperty("登记民警")
    //@NotEmpty(message = "登记民警不能为空")
    //private String operatePolice;
    //
    //@ApiModelProperty("登记时间")
    //@NotNull(message = "登记时间不能为空")
    //private Date operateTime;

}
