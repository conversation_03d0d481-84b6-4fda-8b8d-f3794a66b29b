package com.rs.module.ihc.service.hr;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.ihc.controller.admin.hr.vo.*;
import com.rs.module.ihc.entity.hr.PrintdocConfigDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.hr.PrintdocConfigDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 健康档案-打印文书配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrintdocConfigServiceImpl extends BaseServiceImpl<PrintdocConfigDao, PrintdocConfigDO> implements PrintdocConfigService {

    @Resource
    private PrintdocConfigDao printdocConfigDao;

    @Override
    public String createPrintdocConfig(PrintdocConfigSaveReqVO createReqVO) {
        // 插入
        PrintdocConfigDO printdocConfig = BeanUtils.toBean(createReqVO, PrintdocConfigDO.class);
        printdocConfigDao.insert(printdocConfig);
        // 返回
        return printdocConfig.getId();
    }

    @Override
    public void updatePrintdocConfig(PrintdocConfigSaveReqVO updateReqVO) {
        // 校验存在
        validatePrintdocConfigExists(updateReqVO.getId());
        // 更新
        PrintdocConfigDO updateObj = BeanUtils.toBean(updateReqVO, PrintdocConfigDO.class);
        printdocConfigDao.updateById(updateObj);
    }

    @Override
    public void deletePrintdocConfig(String id) {
        // 校验存在
        validatePrintdocConfigExists(id);
        // 删除
        printdocConfigDao.deleteById(id);
    }

    private void validatePrintdocConfigExists(String id) {
        if (printdocConfigDao.selectById(id) == null) {
            throw new ServerException("健康档案-打印文书配置数据不存在");
        }
    }

    @Override
    public PrintdocConfigDO getPrintdocConfig(String id) {
        return printdocConfigDao.selectById(id);
    }

    @Override
    public PageResult<PrintdocConfigDO> getPrintdocConfigPage(PrintdocConfigPageReqVO pageReqVO) {
        return printdocConfigDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrintdocConfigDO> getPrintdocConfigList(PrintdocConfigListReqVO listReqVO) {
        return printdocConfigDao.selectList(listReqVO);
    }


}
