package com.rs.module.ihc.controller.admin.ipm.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
public class InternalMedicalRemotePrescribeVO {

    /**
     * 远程问诊预约主键id
     */
    @NotNull(message = "远程问诊预约主键id不能为空")
    @ApiModelProperty(value = "远程问诊预约主键id", required = true)
    private String id;

    /**
     * 药方
     * <p>
     * copy 共用现场巡诊的处方类
     */
    @Valid
    @NotNull(message = "处方信息不能为空")
    @ApiModelProperty(value = "处方信息", required = true)
    private PrescribeSaveReqVO prescribe;

}
