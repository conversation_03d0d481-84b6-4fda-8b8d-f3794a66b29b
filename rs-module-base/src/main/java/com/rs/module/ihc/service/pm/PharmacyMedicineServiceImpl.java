package com.rs.module.ihc.service.pm;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineListReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicinePageReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineRespVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineSaveReqVO;
import com.rs.module.ihc.dao.pm.PharmacyMedicineDao;
import com.rs.module.ihc.entity.pm.PharmacyMedicineDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 大药房管理-药品信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PharmacyMedicineServiceImpl extends BaseServiceImpl<PharmacyMedicineDao, PharmacyMedicineDO> implements PharmacyMedicineService {

    @Resource
    private PharmacyMedicineDao pharmacyMedicineDao;

    @Override
    public String createPharmacyMedicine(PharmacyMedicineSaveReqVO createReqVO) {
        // 插入
        PharmacyMedicineDO pharmacyMedicine = BeanUtils.toBean(createReqVO, PharmacyMedicineDO.class);
        pharmacyMedicineDao.insert(pharmacyMedicine);
        // 返回
        return pharmacyMedicine.getId();
    }

    @Override
    public void updatePharmacyMedicine(PharmacyMedicineSaveReqVO updateReqVO) {
        // 校验存在
        validatePharmacyMedicineExists(updateReqVO.getId());
        // 更新
        PharmacyMedicineDO updateObj = BeanUtils.toBean(updateReqVO, PharmacyMedicineDO.class);
        pharmacyMedicineDao.updateById(updateObj);
    }

    @Override
    public void deletePharmacyMedicine(String id) {
        // 校验存在
        validatePharmacyMedicineExists(id);
        // 删除
        pharmacyMedicineDao.deleteById(id);
    }

    private void validatePharmacyMedicineExists(String id) {
        if (pharmacyMedicineDao.selectById(id) == null) {
            throw new ServerException("大药房管理-药品信息数据不存在");
        }
    }

    @Override
    public PharmacyMedicineDO getPharmacyMedicine(String id) {
        return pharmacyMedicineDao.selectById(id);
    }

    @Override
    public PharmacyMedicineRespVO getMedicineDetail(String id) {
        PharmacyMedicineDO pharmacyMedicine = pharmacyMedicineDao.selectById(id);
        return BeanUtils.toBean(pharmacyMedicine, PharmacyMedicineRespVO.class);
    }

    @Override
    public PageResult<PharmacyMedicineDO> getPharmacyMedicinePage(PharmacyMedicinePageReqVO pageReqVO) {
        return pharmacyMedicineDao.selectPage(pageReqVO);
    }

    @Override
    public List<PharmacyMedicineDO> getPharmacyMedicineList(PharmacyMedicineListReqVO listReqVO) {
        return pharmacyMedicineDao.selectList(listReqVO);
    }


}
