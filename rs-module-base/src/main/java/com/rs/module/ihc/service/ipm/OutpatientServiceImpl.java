package com.rs.module.ihc.service.ipm;

import cn.hutool.core.lang.Assert;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.constant.InternalMedicalOutpatientSourceConstant;
import com.rs.module.ihc.constant.InternalMedicalOutpatientStatusConstant;
import com.rs.module.ihc.constant.InternalMedicalPrescribeSourceConstant;
import com.rs.module.ihc.controller.admin.ipm.vo.OutpatientListReqVO;
import com.rs.module.ihc.controller.admin.ipm.vo.OutpatientPageReqVO;
import com.rs.module.ihc.controller.admin.ipm.vo.OutpatientSaveReqVO;
import com.rs.module.ihc.controller.admin.ipm.vo.PrescribeSaveReqVO;
import com.rs.module.ihc.dao.ipm.OutpatientChecklistDao;
import com.rs.module.ihc.dao.ipm.OutpatientDao;
import com.rs.module.ihc.dao.ipm.PrescribeDao;
import com.rs.module.ihc.dao.ipm.PrescribeMedicineDao;
import com.rs.module.ihc.entity.ipm.OutpatientDO;
import com.rs.module.ihc.entity.ipm.PrescribeDO;
import com.rs.module.ihc.entity.ipm.PrescribeMedicineDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


/**
 * 所内就医-所内门诊 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OutpatientServiceImpl extends BaseServiceImpl<OutpatientDao, OutpatientDO> implements OutpatientService {

    @Resource
    private OutpatientDao outpatientDao;
    @Resource
    private OutpatientChecklistDao outpatientChecklistDao;

    @Resource
    private PrescribeMedicineDao prescribeMedicineDao;
    @Resource
    private PrescribeMedicineService prescribeMedicineService;

    @Resource
    private PrescribeDao prescribeDao;
    @Resource
    private PrescribeService prescribeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createOutpatient(OutpatientSaveReqVO createReqVO) {
        // 插入
        createReqVO.setSource(InternalMedicalOutpatientSourceConstant.MANUAL_OPERATION);
        createReqVO.setStatus(InternalMedicalOutpatientStatusConstant.NO_DIAGNOSIS);
        OutpatientDO outpatient = BeanUtils.toBean(createReqVO, OutpatientDO.class);
        outpatientDao.insert(outpatient);// 返回
        return outpatient.getId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOutpatient(OutpatientSaveReqVO updateReqVO) {
        // 校验存在
        validateOutpatientExists(updateReqVO.getId());
        // 更新
        OutpatientDO updateObj = BeanUtils.toBean(updateReqVO, OutpatientDO.class);
        outpatientDao.updateById(updateObj);
    }

    @Override
    public void createOutpatientCf(OutpatientSaveReqVO updateReqVO) {
        OutpatientDO outpatient = outpatientDao.selectById(updateReqVO.getPrescribe().getBusinessId());
        Assert.isTrue(Objects.equals(InternalMedicalOutpatientStatusConstant.NO_DIAGNOSIS, outpatient.getStatus()), "已看诊不能重复开具处方");
        updateReqVO.setId(outpatient.getId());
        updateReqVO.setJgrybm(outpatient.getJgrybm());
        updateOutpatient(updateReqVO);
        updateReqVO.getPrescribe().setPrescribeType(InternalMedicalPrescribeSourceConstant.OUTPATIENT);
        updateReqVO.getPrescribe().setJgrybm(outpatient.getJgrybm());
        prescribeService.createPrescribe(updateReqVO.getPrescribe());
    }

    @Override
    public void updateOutpatientCf(OutpatientSaveReqVO updateReqVO) {
        updateOutpatient(updateReqVO);
        prescribeService.updatePrescribe(updateReqVO.getPrescribe());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOutpatient(String id) {
        // 校验存在
        validateOutpatientExists(id);
        // 删除
        outpatientDao.deleteById(id);

    }

    private void validateOutpatientExists(String id) {
        if (outpatientDao.selectById(id) == null) {
            throw new ServerException("所内就医-所内门诊数据不存在");
        }
    }

    @Override
    public OutpatientDO getOutpatient(String id) {
        return outpatientDao.selectById(id);
    }

    @Override
    public PageResult<OutpatientDO> getOutpatientPage(OutpatientPageReqVO pageReqVO) {
        return outpatientDao.selectPage(pageReqVO);
    }

    @Override
    public List<OutpatientDO> getOutpatientList(OutpatientListReqVO listReqVO) {
        return outpatientDao.selectList(listReqVO);
    }


    private void createOutpatientChecklistList(String outpatientId, PrescribeSaveReqVO prescribeSaveReqVO) {
        PrescribeDO prescribeDO = BeanUtils.toBean(prescribeSaveReqVO, PrescribeDO.class);
        prescribeDO.setBusinessId(outpatientId);
        prescribeDao.insert(prescribeDO);
        prescribeSaveReqVO.getMedicineList().forEach(o -> {
            PrescribeMedicineDO prescribeMedicineDO = BeanUtils.toBean(o, PrescribeMedicineDO.class);
            prescribeMedicineDO.setMlh(prescribeDO.getId());
            prescribeMedicineDao.insert(prescribeMedicineDO);
        });
    }


}
