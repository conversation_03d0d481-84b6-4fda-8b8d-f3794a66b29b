package com.rs.module.ihc.controller.admin.injury.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 医疗子系统-伤情登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class InjuryRegistrationRespVO extends BaseVO implements TransPojo{

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("外伤情况 字典ZD_WSBQK")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WSBQK")
    private String injuryDetails;

    @ApiModelProperty("外伤情况记录方式 字典ZD_WSQKJLFS")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WSQKJLFS")
    private String recordingMethod;

    @ApiModelProperty("致伤日期")
    private Date injuryDate;

    @ApiModelProperty("致伤原因")
    private String injuryCause;

    @ApiModelProperty("登记民警身份证号")
    private String operatePoliceSfzh;

    @ApiModelProperty("登记民警")
    private String operatePolice;

    @ApiModelProperty("登记时间")
    private Date operateTime;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("附件信息")
    private String attUrl;

    @ApiModelProperty("伤病信息-JSON存储")
    private String medicalConditionInformation;

    @ApiModelProperty("附件信息")
    private List<InjuryRegistrationAttachRespVO> attachRespList;

}
