package com.rs.module.ihc.service.ipm.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class SignRefuseExecutePrescribeDTO {

    /**
     * 处方送药记录id
     */
    @NotNull(message = "处方送药记录id不能为空")
    @ApiModelProperty(value = "处方送药记录id", required = true)
    private String id;

    /**
     * 拒绝服药告知书签名地址
     */
    @NotBlank(message = "拒绝服药告知书签名地址不能为空")
    @ApiModelProperty(value = "拒绝服药告知书签名地址", required = true)
    private String refuseDoseDescUrl;
}
