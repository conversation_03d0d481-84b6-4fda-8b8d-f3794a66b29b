package com.rs.module.ihc.service.pm;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.SupplierDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 药房管理-供应商 Service 接口
 *
 * <AUTHOR>
 */
public interface SupplierService extends IBaseService<SupplierDO>{

    /**
     * 创建药房管理-供应商
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createSupplier(@Valid SupplierSaveReqVO createReqVO);

    /**
     * 更新药房管理-供应商
     *
     * @param updateReqVO 更新信息
     */
    void updateSupplier(@Valid SupplierSaveReqVO updateReqVO);

    /**
     * 删除药房管理-供应商
     *
     * @param id 编号
     */
    void deleteSupplier(String id);

    /**
     * 获得药房管理-供应商
     *
     * @param id 编号
     * @return 药房管理-供应商
     */
    SupplierDO getSupplier(String id);

    /**
    * 获得药房管理-供应商分页
    *
    * @param pageReqVO 分页查询
    * @return 药房管理-供应商分页
    */
    PageResult<SupplierDO> getSupplierPage(SupplierPageReqVO pageReqVO);

    /**
    * 获得药房管理-供应商列表
    *
    * @param listReqVO 查询条件
    * @return 药房管理-供应商列表
    */
    List<SupplierDO> getSupplierList(SupplierListReqVO listReqVO);


}
