package com.rs.module.ihc.entity.injury;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 医疗子系统-伤亡登记-附件信息 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_injury_registration_attach")
@KeySequence("ihc_injury_registration_attach_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ihc_injury_registration_attach")
public class InjuryRegistrationAttachDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 关联主表ID
     */
    private String registrationId;
    /**
     * 附件URL
     */
    private String attUrl;
    /**
     * 描述
     */
    private String remark;

    @ApiModelProperty("x轴")
    private String xAxis;

    @ApiModelProperty("y轴")
    private String yAxis;

    @ApiModelProperty("序号")
    private Integer serialNumber;

}
