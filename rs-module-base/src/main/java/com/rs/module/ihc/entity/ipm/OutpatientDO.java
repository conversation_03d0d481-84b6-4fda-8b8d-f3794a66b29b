package com.rs.module.ihc.entity.ipm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Id;
import java.util.Date;

/**
 * 所内就医-所内门诊 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_ipm_outpatient")
@KeySequence("ihc_ipm_outpatient_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutpatientDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @Id
    private String id;
    /**
     * 来源（1:所内就医预约，2:手动新增）
     */
    private String source;
    /**
     * 预约id，对应ihc_ipm_internal_medical_appointment.id
     */
    private String appointmentId;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 在押人员编号
     */
    private String rybh;
    /**
     * 在押人员名称
     */
    private String ryxm;
    /**
     * 是否重病号（ 0：否 ，1：是）
     */
    private Integer sickType;
    /**
     * 报病时间
     */
    private Date diseaseTime;
    /**
     * 报病原因
     */
    private String diseaseReason;
    /**
     * 主诉
     */
    private String mainComplaint;
    /**
     * 情况登记
     */
    private String illnessResume;
    /**
     * 状态（ 0：待看诊，1：已看诊）
     */
    private Integer status;
    /**
     * 监所id
     */
    private String prisonId;

}
