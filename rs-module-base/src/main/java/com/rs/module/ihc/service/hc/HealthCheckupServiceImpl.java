package com.rs.module.ihc.service.hc;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.ihc.constant.IhcsHealthCheckupConstant;
import com.rs.module.ihc.controller.admin.hc.vo.*;
import com.rs.module.ihc.dao.hc.HealthCheckupDao;
import com.rs.module.ihc.entity.hc.HealthCheckupDO;
import com.rs.module.ihc.entity.hc.HealthCheckupFileDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 五项体检 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HealthCheckupServiceImpl extends BaseServiceImpl<HealthCheckupDao, HealthCheckupDO> implements HealthCheckupService {

    @Resource
    private HealthCheckupDao healthCheckupDao;

    @Resource
    private PrisonerService prisonerService;

    @Resource
    private HealthCheckupFileService healthCheckupFileService;

    @Override
    @Transactional( rollbackFor = Exception.class)
    public String createHealthCheckup(HealthCheckupAdditionalRecordVO createReqVO) {
        // 插入
        HealthCheckupDO healthCheckup = BeanUtils.toBean(createReqVO, HealthCheckupDO.class);

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        healthCheckup.setCheckupDoctor(sessionUser.getIdCard());
        healthCheckup.setCheckupDoctorName(sessionUser.getName());
        healthCheckup.setCheckupStatus( IhcsHealthCheckupConstant.CHECKUP_STATUS_DONE);
        healthCheckup.setEnterTime(new Date());
        healthCheckupDao.insert(healthCheckup);

        if (CollectionUtil.isNotEmpty(createReqVO.getFileList())) {
            List<HealthCheckupFileDO> healthCheckupFileDOList = new ArrayList<>();
            for (HealthCheckupFileSaveReqVO file : createReqVO.getFileList()) {
                HealthCheckupFileDO checkupFileDO = BeanUtils.toBean(file, HealthCheckupFileDO.class);
                checkupFileDO.setCheckupId(healthCheckup.getId());
                healthCheckupFileDOList.add(checkupFileDO );
            }
            healthCheckupFileService.saveBatch( healthCheckupFileDOList);
        }
        return healthCheckup.getId();
    }

    @Override
    public void updateHealthCheckup(HealthCheckupSaveReqVO updateReqVO) {
        // 校验存在
        HealthCheckupDO healthCheckupDO = healthCheckupDao.selectById( updateReqVO.getId());
        if(healthCheckupDO == null ){
            throw new RuntimeException("记录不存在！");
        }

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        if(StrUtil.isBlank(healthCheckupDO.getCheckupDoctor())){
            healthCheckupDO.setCheckupDoctor(sessionUser.getIdCard());
            healthCheckupDO.setCheckupDoctorName(sessionUser.getName());
            healthCheckupDO.setCheckupStatus( IhcsHealthCheckupConstant.CHECKUP_STATUS_DONE);
            healthCheckupDO.setEnterTime(new Date());
        }
        BeanUtils.copyProperties(updateReqVO, healthCheckupDO);
        healthCheckupDao.updateById(healthCheckupDO);

        if(CollectionUtil.isNotEmpty(updateReqVO.getFileList())){
            healthCheckupFileService.remove(new LambdaQueryWrapper<HealthCheckupFileDO>().eq(HealthCheckupFileDO::getCheckupId, updateReqVO.getId()));
            List<HealthCheckupFileDO> healthCheckupFileDOList = new ArrayList<>();
            for (HealthCheckupFileSaveReqVO file : updateReqVO.getFileList()) {
                HealthCheckupFileDO checkupFileDO = BeanUtils.toBean(file, HealthCheckupFileDO.class);
                checkupFileDO.setCheckupId(updateReqVO.getId());
                healthCheckupFileDOList.add(checkupFileDO );
            }
            healthCheckupFileService.saveBatch( healthCheckupFileDOList);
        }
    }

    @Override
    public HealthCheckupRespVO getHealthCheckup(String id) {
        HealthCheckupDO healthCheckupDO = healthCheckupDao.selectById(id);
        HealthCheckupRespVO checkupRespVO =  BeanUtils.toBean(healthCheckupDO, HealthCheckupRespVO.class);
        if(checkupRespVO != null){
            HealthCheckupFileListReqVO healthCheckupFileListReqVO = new HealthCheckupFileListReqVO();
            healthCheckupFileListReqVO.setCheckupId(id );
            List<HealthCheckupFileDO>  healthCheckupFileDOList = healthCheckupFileService.getHealthCheckupFileList(healthCheckupFileListReqVO);
            checkupRespVO.setFileList(BeanUtils.toBean(healthCheckupFileDOList, HealthCheckupFileRespVO.class) );
        }
        return checkupRespVO;
    }

    @Override
    @Transactional( rollbackFor = Exception.class)
    public void apply(String jgrybms) {
        if (StrUtil.isNotBlank(jgrybms)) {
            for (String jgrybm : jgrybms.split(",")) {
                HealthCheckupDO checkup = new HealthCheckupDO();
                checkup.setJgrybm(jgrybm);
                PrisonerInDO prisonerInDO = prisonerService.getPrisonerInOne(jgrybm);
                checkup.setJgryxm(prisonerInDO.getXm());
                checkup.setCheckupStatus(IhcsHealthCheckupConstant.CHECKUP_STATUS_DOING);
                healthCheckupDao.insert(checkup);
            }
        }
    }

}
