package com.rs.module.ihc.controller.admin.pm.sick.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 重特病号管理解除 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SeverelySickManageReleaseReqVO extends BaseVO {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键id")
    private String id;
    @ApiModelProperty("解除时间")
    private Date relieveTime;
    @ApiModelProperty("当前是否是重病号 字典值：boolean_type")
    private String businessStatus;

}
