package com.rs.module.ihc.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.fastjson.JSON;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineCodeImportVO;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @ClassName DemoDataListener
 * 
 * <AUTHOR>
 * @Date 2025/3/18 14:19
 * @Version 1.0
 */
@Log4j2
public class MedicineCodeListener implements ReadListener<MedicineCodeImportVO> {

    List<MedicineCodeImportVO> cachedDataList;

    public MedicineCodeListener(List<MedicineCodeImportVO> cachedDataList) {
        this.cachedDataList = cachedDataList;
    }


    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(MedicineCodeImportVO data, AnalysisContext context) {
        log.info("解析到一条数据:{}", JSON.toJSONString(data));
        if (StringUtils.isNotEmpty(data.getZczh())) {
            data.setApprovalNum(data.getZczh());
            data.setOriginType("2");
            data.setMarketApprovalHolder(data.getMarketApprovalHolderZw());
            data.setProductUnit(data.getProductUnitZw());
        }else {
            data.setOriginType("1");
        }
        cachedDataList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

}
