package com.rs.module.ihc.service.ephe;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.ihc.controller.admin.ephe.vo.EpidemicRespSaveReqVO;
import com.rs.module.ihc.entity.ephe.EpidemicRespDO;

import javax.validation.Valid;

/**
 * 卫生防疫与健康教育-疫情处置 Service 接口
 *
 * <AUTHOR>
 */
public interface EpidemicRespService extends IBaseService<EpidemicRespDO>{

    /**
     * 创建卫生防疫与健康教育-疫情处置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createEpidemicResp(@Valid EpidemicRespSaveReqVO createReqVO);

    /**
     * 获得卫生防疫与健康教育-疫情处置
     *
     * @param id 编号
     * @return 卫生防疫与健康教育-疫情处置
     */
    EpidemicRespDO getEpidemicResp(String id);




}
