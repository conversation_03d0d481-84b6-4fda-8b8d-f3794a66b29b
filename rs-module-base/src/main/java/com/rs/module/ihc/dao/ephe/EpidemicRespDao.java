package com.rs.module.ihc.dao.ephe;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.ephe.EpidemicRespDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.ephe.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 卫生防疫与健康教育-疫情处置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface EpidemicRespDao extends IBaseDao<EpidemicRespDO> {


    default PageResult<EpidemicRespDO> selectPage(EpidemicRespPageReqVO reqVO) {
        Page<EpidemicRespDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<EpidemicRespDO> wrapper = new LambdaQueryWrapperX<EpidemicRespDO>()
            .eqIfPresent(EpidemicRespDO::getSfcxzjxyq, reqVO.getSfcxzjxyq())
            .eqIfPresent(EpidemicRespDO::getCxzjxyqrq, reqVO.getCxzjxyqrq())
            .eqIfPresent(EpidemicRespDO::getZjxyqczqk, reqVO.getZjxyqczqk())
            .eqIfPresent(EpidemicRespDO::getBgfs, reqVO.getBgfs())
            .eqIfPresent(EpidemicRespDO::getCrbbgktbrq, reqVO.getCrbbgktbrq())
            .eqIfPresent(EpidemicRespDO::getCrbbgk, reqVO.getCrbbgk())
            .eqIfPresent(EpidemicRespDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(EpidemicRespDO::getOperatePolice, reqVO.getOperatePolice())
            .betweenIfPresent(EpidemicRespDO::getOperateTime, reqVO.getOperateTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(EpidemicRespDO::getAddTime);
        }
        Page<EpidemicRespDO> epidemicRespPage = selectPage(page, wrapper);
        return new PageResult<>(epidemicRespPage.getRecords(), epidemicRespPage.getTotal());
    }
    default List<EpidemicRespDO> selectList(EpidemicRespListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<EpidemicRespDO>()
            .eqIfPresent(EpidemicRespDO::getSfcxzjxyq, reqVO.getSfcxzjxyq())
            .eqIfPresent(EpidemicRespDO::getCxzjxyqrq, reqVO.getCxzjxyqrq())
            .eqIfPresent(EpidemicRespDO::getZjxyqczqk, reqVO.getZjxyqczqk())
            .eqIfPresent(EpidemicRespDO::getBgfs, reqVO.getBgfs())
            .eqIfPresent(EpidemicRespDO::getCrbbgktbrq, reqVO.getCrbbgktbrq())
            .eqIfPresent(EpidemicRespDO::getCrbbgk, reqVO.getCrbbgk())
            .eqIfPresent(EpidemicRespDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(EpidemicRespDO::getOperatePolice, reqVO.getOperatePolice())
            .betweenIfPresent(EpidemicRespDO::getOperateTime, reqVO.getOperateTime())
        .orderByDesc(EpidemicRespDO::getAddTime));    }


    }
