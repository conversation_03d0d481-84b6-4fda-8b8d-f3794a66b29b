package com.rs.module.ihc.dao.pm;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.framework.mybatis.util.BizAssert;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineListReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicinePageReqVO;
import com.rs.module.ihc.entity.pm.PharmacyMedicineDO;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
* 大药房管理-药品信息 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PharmacyMedicineDao extends IBaseDao<PharmacyMedicineDO> {


    default PageResult<PharmacyMedicineDO> selectPage(PharmacyMedicinePageReqVO reqVO) {
        Page<PharmacyMedicineDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PharmacyMedicineDO> wrapper = new LambdaQueryWrapperX<PharmacyMedicineDO>()
            .eqIfPresent(PharmacyMedicineDO::getPharmacyId, reqVO.getPharmacyId())
            .likeIfPresent(PharmacyMedicineDO::getMedicineName, reqVO.getMedicineName())
            .likeIfPresent(PharmacyMedicineDO::getMedicineSpellName, reqVO.getMedicineSpellName())
            .eqIfPresent(PharmacyMedicineDO::getMedicineNameFirstLetter, reqVO.getMedicineNameFirstLetter())
            .eqIfPresent(PharmacyMedicineDO::getDosageForm, reqVO.getDosageForm())
            .eqIfPresent(PharmacyMedicineDO::getMeasurementUnit, reqVO.getMeasurementUnit())
            .eqIfPresent(PharmacyMedicineDO::getSpecs, reqVO.getSpecs())
            .eqIfPresent(PharmacyMedicineDO::getProductUnit, reqVO.getProductUnit())
            .eqIfPresent(PharmacyMedicineDO::getApprovalNum, reqVO.getApprovalNum())
            .eqIfPresent(PharmacyMedicineDO::getOriginalApprovalNum, reqVO.getOriginalApprovalNum())
            .eqIfPresent(PharmacyMedicineDO::getMedicineStandardCode, reqVO.getMedicineStandardCode())
            .eqIfPresent(PharmacyMedicineDO::getDrug, reqVO.getDrug())
            .eqIfPresent(PharmacyMedicineDO::getDayDosage, reqVO.getDayDosage())
            .eqIfPresent(PharmacyMedicineDO::getOneDosage, reqVO.getOneDosage())
            .eqIfPresent(PharmacyMedicineDO::getInventoryWarnValue, reqVO.getInventoryWarnValue())
            .eqIfPresent(PharmacyMedicineDO::getReportableLoss, reqVO.getReportableLoss())
            .likeIfPresent(PharmacyMedicineDO::getMedicineEnglishName, reqVO.getMedicineEnglishName())
            .likeIfPresent(PharmacyMedicineDO::getProductName, reqVO.getProductName())
            .eqIfPresent(PharmacyMedicineDO::getMarketApprovalHolder, reqVO.getMarketApprovalHolder())
            .eqIfPresent(PharmacyMedicineDO::getMarketApprovalHolderAddress, reqVO.getMarketApprovalHolderAddress())
            .eqIfPresent(PharmacyMedicineDO::getProductAddress, reqVO.getProductAddress())
            .eqIfPresent(PharmacyMedicineDO::getMedicineCategory, reqVO.getMedicineCategory())
            .eqIfPresent(PharmacyMedicineDO::getMedicineStandardCodeRemark, reqVO.getMedicineStandardCodeRemark())
            .eqIfPresent(PharmacyMedicineDO::getMedicineAlias, reqVO.getMedicineAlias())
            .eqIfPresent(PharmacyMedicineDO::getPackageUnit, reqVO.getPackageUnit())
            .betweenIfPresent(PharmacyMedicineDO::getApprovalDate, reqVO.getApprovalDate())
            .eqIfPresent(PharmacyMedicineDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
            .eqIfPresent(PharmacyMedicineDO::getHasExpireDateBatch, reqVO.getHasExpireDateBatch())
            .eqIfPresent(PharmacyMedicineDO::getMinMeasurementUnit, reqVO.getMinMeasurementUnit())
            .eqIfPresent(PharmacyMedicineDO::getUnitConversionRatio, reqVO.getUnitConversionRatio())
            .eqIfPresent(PharmacyMedicineDO::getBelongSensitive, reqVO.getBelongSensitive())
            .eqIfPresent(PharmacyMedicineDO::getPaychoactieDrug, reqVO.getPaychoactieDrug())
            .eqIfPresent(PharmacyMedicineDO::getPrisonId, reqVO.getPrisonId())
            .eqIfPresent(PharmacyMedicineDO::getPinyin, reqVO.getPinyin())
            .eqIfPresent(PharmacyMedicineDO::getJp, reqVO.getJp())
            .eqIfPresent(PharmacyMedicineDO::getPx, reqVO.getPx())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PharmacyMedicineDO::getAddTime);
        }
        Page<PharmacyMedicineDO> pharmacyMedicinePage = selectPage(page, wrapper);
        return new PageResult<>(pharmacyMedicinePage.getRecords(), pharmacyMedicinePage.getTotal());
    }
    default List<PharmacyMedicineDO> selectList(PharmacyMedicineListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PharmacyMedicineDO>()
            .eqIfPresent(PharmacyMedicineDO::getPharmacyId, reqVO.getPharmacyId())
            .likeIfPresent(PharmacyMedicineDO::getMedicineName, reqVO.getMedicineName())
            .likeIfPresent(PharmacyMedicineDO::getMedicineSpellName, reqVO.getMedicineSpellName())
            .eqIfPresent(PharmacyMedicineDO::getMedicineNameFirstLetter, reqVO.getMedicineNameFirstLetter())
            .eqIfPresent(PharmacyMedicineDO::getDosageForm, reqVO.getDosageForm())
            .eqIfPresent(PharmacyMedicineDO::getMeasurementUnit, reqVO.getMeasurementUnit())
            .eqIfPresent(PharmacyMedicineDO::getSpecs, reqVO.getSpecs())
            .eqIfPresent(PharmacyMedicineDO::getProductUnit, reqVO.getProductUnit())
            .eqIfPresent(PharmacyMedicineDO::getApprovalNum, reqVO.getApprovalNum())
            .eqIfPresent(PharmacyMedicineDO::getOriginalApprovalNum, reqVO.getOriginalApprovalNum())
            .eqIfPresent(PharmacyMedicineDO::getMedicineStandardCode, reqVO.getMedicineStandardCode())
            .eqIfPresent(PharmacyMedicineDO::getDrug, reqVO.getDrug())
            .eqIfPresent(PharmacyMedicineDO::getDayDosage, reqVO.getDayDosage())
            .eqIfPresent(PharmacyMedicineDO::getOneDosage, reqVO.getOneDosage())
            .eqIfPresent(PharmacyMedicineDO::getInventoryWarnValue, reqVO.getInventoryWarnValue())
            .eqIfPresent(PharmacyMedicineDO::getReportableLoss, reqVO.getReportableLoss())
            .likeIfPresent(PharmacyMedicineDO::getMedicineEnglishName, reqVO.getMedicineEnglishName())
            .likeIfPresent(PharmacyMedicineDO::getProductName, reqVO.getProductName())
            .eqIfPresent(PharmacyMedicineDO::getMarketApprovalHolder, reqVO.getMarketApprovalHolder())
            .eqIfPresent(PharmacyMedicineDO::getMarketApprovalHolderAddress, reqVO.getMarketApprovalHolderAddress())
            .eqIfPresent(PharmacyMedicineDO::getProductAddress, reqVO.getProductAddress())
            .eqIfPresent(PharmacyMedicineDO::getMedicineCategory, reqVO.getMedicineCategory())
            .eqIfPresent(PharmacyMedicineDO::getMedicineStandardCodeRemark, reqVO.getMedicineStandardCodeRemark())
            .eqIfPresent(PharmacyMedicineDO::getMedicineAlias, reqVO.getMedicineAlias())
            .eqIfPresent(PharmacyMedicineDO::getPackageUnit, reqVO.getPackageUnit())
            .betweenIfPresent(PharmacyMedicineDO::getApprovalDate, reqVO.getApprovalDate())
            .eqIfPresent(PharmacyMedicineDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
            .eqIfPresent(PharmacyMedicineDO::getHasExpireDateBatch, reqVO.getHasExpireDateBatch())
            .eqIfPresent(PharmacyMedicineDO::getMinMeasurementUnit, reqVO.getMinMeasurementUnit())
            .eqIfPresent(PharmacyMedicineDO::getUnitConversionRatio, reqVO.getUnitConversionRatio())
            .eqIfPresent(PharmacyMedicineDO::getBelongSensitive, reqVO.getBelongSensitive())
            .eqIfPresent(PharmacyMedicineDO::getPaychoactieDrug, reqVO.getPaychoactieDrug())
            .eqIfPresent(PharmacyMedicineDO::getPrisonId, reqVO.getPrisonId())
            .eqIfPresent(PharmacyMedicineDO::getPinyin, reqVO.getPinyin())
            .eqIfPresent(PharmacyMedicineDO::getJp, reqVO.getJp())
            .eqIfPresent(PharmacyMedicineDO::getPx, reqVO.getPx())
        .orderByDesc(PharmacyMedicineDO::getAddTime));    }


    /**
     * 比较替换的方式更新药品库存
     *
     * @param medicineId         药品id
     * @param sourceInventoryNum 原库存
     * @param targetInventoryNum 目标库存
     */
    default void compareAndSetMedicineInventoryNum(String medicineId, BigDecimal sourceInventoryNum, BigDecimal targetInventoryNum) {
        int update = this.update(null, Wrappers.lambdaUpdate(PharmacyMedicineDO.class)
                .set(PharmacyMedicineDO::getTotalInventoryNum, targetInventoryNum)
                .set(PharmacyMedicineDO::getUpdateTime, LocalDateTime.now())
                .eq(PharmacyMedicineDO::getId, medicineId)
                .eq(PharmacyMedicineDO::getTotalInventoryNum, sourceInventoryNum));
        BizAssert.isTrue(update == 1, "该药品id[" + medicineId + "]库存数量同时被其他操作修改，操作失败请重试");
    }


}
