package com.rs.module.ihc.controller.admin.ipm.vo;

import com.rs.framework.common.annotation.DefaultValueDate;
import com.rs.framework.common.annotation.DefaultValueString;
import com.rs.framework.common.annotation.SessionUserIdCard;
import com.rs.framework.common.annotation.SessionUserName;
import com.rs.module.ihc.entity.ipm.OutpatientChecklistCategoryDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 所内就医-所内门诊-检查单新增/修改 Request VO")
@Data
public class OutpatientChecklistSaveReqVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("所内门诊id，对应ihc_ipm_outpatient.id")
    @NotNull(message = "所内门诊id，对应ihc_ipm_outpatient.id不能为空")
    private String outpatientId;

    @ApiModelProperty("主诉")
    @NotBlank(message = "主诉不能为空")
    private String mainComplaint;

    @ApiModelProperty("病情简述")
    @NotBlank(message = "病情简述不能为空")
    private String illnessResume;

    @ApiModelProperty("检查结论")
    private String checkConclusion;

    @ApiModelProperty("检查单编号")
    @NotBlank(message = "检查单编号不能为空")
    private String checklistNum;

    @ApiModelProperty("检查单种类 多选  ,分割")
    @NotBlank(message = "检查单种类 多选  ,分割不能为空")
    private String checklistCategory;

    @ApiModelProperty("检查单状态")
    @DefaultValueString("0")
    private String checklistStatus;

    @ApiModelProperty("登记人，对应permission_user.userid")
    @SessionUserIdCard
    private String registerUserid;

    @ApiModelProperty("登记人名称")
    @SessionUserName
    private String registerUserName;

    @ApiModelProperty("登记时间")
    @DefaultValueDate
    private Date registerTime;

    @ApiModelProperty("监所id")
    private String prisonId;

    @ApiModelProperty("jgrybm")
    private String jgrybm;


    @ApiModelProperty("所内就医-所内门诊-检查单-检查种类列表")
    private List<OutpatientChecklistCategoryDO> outpatientChecklistCategorys;

}
