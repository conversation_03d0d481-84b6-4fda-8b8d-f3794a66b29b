package com.rs.module.ihc.entity.ipm.appointment;

import lombok.*;
import java.util.*;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 所内就医-预约登记 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_ipm_internal_medical_appointment")
@KeySequence("ihc_ipm_internal_medical_appointment_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IhcsInternalMedicalAppointmentDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 预约编号
     */
    private String appointmentNum;
    /**
     * 被监管人员编号
     */
    private String supervisedUserCode;
    /**
     * 是否重病号 0：否 1：是
     */
    private Integer sickType;
    /**
     * 报病时间
     */
    private Date diseaseTime;
    /**
     * 报病原因
     */
    private String diseaseReason;
    /**
     * 初步诊断
     */
    private String primaryDiagnosis;
    /**
     * 病情等级 字典值
     */
    private String diseaseLevel;
    /**
     * 处理方式/预约审核结果 字典值
     */
    private String processMethod;
    /**
     * 处理人 对应permission_user.userid
     */
    private String processUserid;
    /**
     * 处理人名称
     */
    private String processUserName;
    /**
     * 处理时间
     */
    private Date processTime;
    /**
     * 远程问诊状态 0：待处理 1：已处理
     */
    private Integer remoteDiagnoseStatus;
    /**
     * 监所id
     */
    private String prisonId;
    /**
     * 有无过敏史 字典值：is_have
     */
    private String hasAllergyHistory;

}
