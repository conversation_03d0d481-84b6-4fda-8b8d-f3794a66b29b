package com.rs.module.ihc.service.ephe;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.ephe.vo.*;
import com.rs.module.ihc.entity.ephe.HealthEduDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 卫生防疫与健康教育-健康教育 Service 接口
 *
 * <AUTHOR>
 */
public interface HealthEduService extends IBaseService<HealthEduDO>{

    /**
     * 创建卫生防疫与健康教育-健康教育
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createHealthEdu(@Valid HealthEduSaveReqVO createReqVO);

    /**
     * 获得卫生防疫与健康教育-健康教育
     *
     * @param id 编号
     * @return 卫生防疫与健康教育-健康教育
     */
    HealthEduDO getHealthEdu(String id);

}
