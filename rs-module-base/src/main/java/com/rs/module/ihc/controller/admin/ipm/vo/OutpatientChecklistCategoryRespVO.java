package com.rs.module.ihc.controller.admin.ipm.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.common.annotation.Format;
import com.rs.framework.mybatis.entity.BaseDO;
import com.rs.module.ihc.service.ipm.OutpatientChecklistService;
import com.rs.module.oss.service.FileDetailService;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.dromara.x.file.storage.core.FileInfo;

import java.util.List;

/**
 * 所内就医-所内门诊-检查单-检查种类 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 所内就医-所内门诊-检查单-检查种类新增/修改 Request VO")
@TableName("ihc_ipm_outpatient_checklist_category")
@KeySequence("ihc_ipm_outpatient_checklist_category_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutpatientChecklistCategoryRespVO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;
    /**
     * 检查单id，对应ihc_ipm_outpatient_checklist.id
     */
    @ApiModelProperty("检查单id，对应ihc_ipm_outpatient_checklist.id")
    private String checklistId;
    /**
     * 检查类型 字典值 1=常规检查 2=心电图 3=B超 4=DR
     */
    @ApiModelProperty("检查类型 字典值 1=常规检查 2=心电图 3=B超 4=DR")
    private String checklistCategory;
    /**
     * 检查子类型 字典值 多选 ,分割
     */
    @ApiModelProperty("检查子类型 字典值 多选 ,分割")
    private String checklistSubCategory;
    /**
     * 检查子类型其他
     */
    @ApiModelProperty("检查子类型其他")
    private String checklistSubCategoryOther;
    /**
     * 检查结果
     */
    @ApiModelProperty("检查结果")
    private String checkResult;

    @Format(value = "checklistSubCategory",service= OutpatientChecklistService.class , method = "getChecklistSubCategoryName" )
    private String checklistSubCategoryName;

    @Format(value = "id",service= FileDetailService.class , method = "getFileInfoListByObjectId" )
    @ApiModelProperty("附件")
    private List<FileInfo> fileList;

}
