package com.rs.module.ihc.controller.admin.pm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@ApiModel(description = "管理后台 - 药房管理-药品入库批次列表 Request VO")
@Data
public class MedicineInYpbsReqVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("药品id，对应ihc_ppm_drug.id")
    @NotNull
    private String medicineId;
    @ApiModelProperty("批次编码，编号规则：8位日期+3位序列号，如：20250228001")
    private String batchCode;

}
