package com.rs.module.ihc.controller.admin.pm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 药房管理-顾送药品入库批次新增/修改 Request VO")
@Data
public class MedicineDeliveryInSaveReqVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("药品id，对应ihc_ppm_drug.id")
    @NotEmpty(message = "药品id，对应ihc_ppm_drug.id不能为空")
    private String drugId;

    @ApiModelProperty("入库数量")
    @NotNull(message = "入库数量不能为空")
    private BigDecimal inNum;

    @ApiModelProperty("批次编码，编号规则：8位日期+3位序列号，如：20250228001")
    @NotEmpty(message = "批次编码，编号规则：8位日期+3位序列号，如：20250228001不能为空")
    private String batchCode;

    @ApiModelProperty("供药单位，对应ihc_pm_supplier.id")
    @NotNull(message = "供药单位，对应ihc_pm_supplier.id不能为空")
    private Long supplierId;

    @ApiModelProperty("供药单位名称")
    private String supplierName;

    @ApiModelProperty("凭证号")
    private String voucherCode;

    @ApiModelProperty("进价")
    private BigDecimal purchasePrice;

    @ApiModelProperty("批发价")
    private BigDecimal wholesalePrice;

    @ApiModelProperty("调拨价")
    private BigDecimal transferPrice;

    @ApiModelProperty("零售价")
    private BigDecimal retailPrice;

    @ApiModelProperty("入库日期")
    @NotNull(message = "入库日期不能为空")
    private Date inDate;

    @ApiModelProperty("有效期至")
    private Date expireDate;

    @ApiModelProperty("入库方式")
    private String inStorageMethod;

    @ApiModelProperty("调拨方式")
    private String transferMethod;

    @ApiModelProperty("药品货位")
    private String medicinePlace;

    @ApiModelProperty("批次库存数量 最小单位")
    @NotNull(message = "批次库存数量 最小单位不能为空")
    private BigDecimal inventoryNum;

    @ApiModelProperty("当前药品总库存数量 最小单位")
    @NotNull(message = "当前药品总库存数量 最小单位不能为空")
    private BigDecimal totalInventoryNum;

    @ApiModelProperty("入库状态")
    private String inStatus;

    @ApiModelProperty("流程是否结束 0=否 1=是")
    private Integer workflowEnd;

}
