package com.rs.module.ihc.service.pm;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.SupplierDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.pm.SupplierDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 药房管理-供应商 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SupplierServiceImpl extends BaseServiceImpl<SupplierDao, SupplierDO> implements SupplierService {

    @Resource
    private SupplierDao supplierDao;

    @Override
    public String createSupplier(SupplierSaveReqVO createReqVO) {
        // 插入
        SupplierDO supplier = BeanUtils.toBean(createReqVO, SupplierDO.class);
        supplierDao.insert(supplier);
        // 返回
        return supplier.getId();
    }

    @Override
    public void updateSupplier(SupplierSaveReqVO updateReqVO) {
        // 校验存在
        validateSupplierExists(updateReqVO.getId());
        // 更新
        SupplierDO updateObj = BeanUtils.toBean(updateReqVO, SupplierDO.class);
        supplierDao.updateById(updateObj);
    }

    @Override
    public void deleteSupplier(String id) {
        // 校验存在
        validateSupplierExists(id);
        // 删除
        supplierDao.deleteById(id);
    }

    private void validateSupplierExists(String id) {
        if (supplierDao.selectById(id) == null) {
            throw new ServerException("药房管理-供应商数据不存在");
        }
    }

    @Override
    public SupplierDO getSupplier(String id) {
        return supplierDao.selectById(id);
    }

    @Override
    public PageResult<SupplierDO> getSupplierPage(SupplierPageReqVO pageReqVO) {
        return supplierDao.selectPage(pageReqVO);
    }

    @Override
    public List<SupplierDO> getSupplierList(SupplierListReqVO listReqVO) {
        return supplierDao.selectList(listReqVO);
    }


}
