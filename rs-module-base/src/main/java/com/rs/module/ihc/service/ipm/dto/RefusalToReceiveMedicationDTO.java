package com.rs.module.ihc.service.ipm.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName RefusalToReceiveMedicationDTO
 * @Description 拒绝服药DTO
 * <AUTHOR>
 * @Date 2025/6/3 21:02
 * @Version 1.0
 */
@Data
public class RefusalToReceiveMedicationDTO {
    /**
     * 处方id
     */
    @NotNull(message = "处方id不能为空")
    @ApiModelProperty(value = "处方id", required = true)
    private String prescribeId;
}
