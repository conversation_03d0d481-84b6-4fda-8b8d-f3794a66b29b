package com.rs.module.ihc.controller.admin.pm.sick.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 重特病号解除申请新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SeverelySickReliveApplySaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键id")
    private String id;

    @ApiModelProperty("关联重特病号管理表id")
    @NotEmpty(message = "关联重特病号管理表id不能为空")
    private String manageId;

    @ApiModelProperty("被监管人id")
    @NotEmpty(message = "被监管人id不能为空")
    private String prisonerId;

    @ApiModelProperty("解除申请时间")
    @NotNull(message = "解除申请时间不能为空")
    private Date relieveApplyTime;

    @ApiModelProperty("解除原因")
    private String relieveReason;

    @ApiModelProperty("解除申请人id")
    @NotNull(message = "解除申请人id不能为空")
    private Long relieveUserId;

    @ApiModelProperty("解除申请人姓名")
    @NotEmpty(message = "解除申请人姓名不能为空")
    private String relieveUserName;

    @ApiModelProperty("最终审批结果 字典值：is_agree")
    private String finalApproveResult;

    @ApiModelProperty("解除时间")
    private Date relieveTime;

}
