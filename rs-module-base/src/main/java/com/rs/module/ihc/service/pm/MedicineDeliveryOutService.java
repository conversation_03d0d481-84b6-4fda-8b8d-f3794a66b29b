package com.rs.module.ihc.service.pm;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.MedicineDeliveryOutDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 药房管理-顾送药品出库 Service 接口
 *
 * <AUTHOR>
 */
public interface MedicineDeliveryOutService extends IBaseService<MedicineDeliveryOutDO>{

    /**
     * 创建药房管理-顾送药品出库
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMedicineDeliveryOut(@Valid MedicineDeliveryOutSaveReqVO createReqVO);

    /**
     * 更新药房管理-顾送药品出库
     *
     * @param updateReqVO 更新信息
     */
    void updateMedicineDeliveryOut(@Valid MedicineDeliveryOutSaveReqVO updateReqVO);

    /**
     * 删除药房管理-顾送药品出库
     *
     * @param id 编号
     */
    void deleteMedicineDeliveryOut(String id);

    /**
     * 获得药房管理-顾送药品出库
     *
     * @param id 编号
     * @return 药房管理-顾送药品出库
     */
    MedicineDeliveryOutDO getMedicineDeliveryOut(String id);

    /**
    * 获得药房管理-顾送药品出库分页
    *
    * @param pageReqVO 分页查询
    * @return 药房管理-顾送药品出库分页
    */
    PageResult<MedicineDeliveryOutDO> getMedicineDeliveryOutPage(MedicineDeliveryOutPageReqVO pageReqVO);

    /**
    * 获得药房管理-顾送药品出库列表
    *
    * @param listReqVO 查询条件
    * @return 药房管理-顾送药品出库列表
    */
    List<MedicineDeliveryOutDO> getMedicineDeliveryOutList(MedicineDeliveryOutListReqVO listReqVO);


}
