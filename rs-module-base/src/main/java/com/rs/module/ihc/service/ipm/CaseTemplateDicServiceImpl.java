package com.rs.module.ihc.service.ipm;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.CaseTemplateDicDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.ipm.CaseTemplateDicDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 所内就医-病例模板表-字典 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CaseTemplateDicServiceImpl extends BaseServiceImpl<CaseTemplateDicDao, CaseTemplateDicDO> implements CaseTemplateDicService {

    @Resource
    private CaseTemplateDicDao caseTemplateDicDao;

    @Override
    public String createCaseTemplateDic(CaseTemplateDicSaveReqVO createReqVO) {
        // 插入
        CaseTemplateDicDO caseTemplateDic = BeanUtils.toBean(createReqVO, CaseTemplateDicDO.class);
        caseTemplateDicDao.insert(caseTemplateDic);
        // 返回
        return caseTemplateDic.getId();
    }

    @Override
    public void updateCaseTemplateDic(CaseTemplateDicSaveReqVO updateReqVO) {
        // 校验存在
        validateCaseTemplateDicExists(updateReqVO.getId());
        // 更新
        CaseTemplateDicDO updateObj = BeanUtils.toBean(updateReqVO, CaseTemplateDicDO.class);
        caseTemplateDicDao.updateById(updateObj);
    }

    @Override
    public void deleteCaseTemplateDic(String id) {
        // 校验存在
        validateCaseTemplateDicExists(id);
        // 删除
        caseTemplateDicDao.deleteById(id);
    }

    private void validateCaseTemplateDicExists(String id) {
        if (caseTemplateDicDao.selectById(id) == null) {
            throw new ServerException("所内就医-病例模板表-字典数据不存在");
        }
    }

    @Override
    public CaseTemplateDicDO getCaseTemplateDic(String id) {
        return caseTemplateDicDao.selectById(id);
    }

    @Override
    public PageResult<CaseTemplateDicDO> getCaseTemplateDicPage(CaseTemplateDicPageReqVO pageReqVO) {
        return caseTemplateDicDao.selectPage(pageReqVO);
    }

    @Override
    public List<CaseTemplateDicDO> getCaseTemplateDicList(CaseTemplateDicListReqVO listReqVO) {
        return caseTemplateDicDao.selectList(listReqVO);
    }


}
