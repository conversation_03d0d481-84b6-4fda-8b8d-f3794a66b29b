package com.rs.module.ihc.dao.hc;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.hc.HealthCheckupDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.hc.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 五项体检 Dao
*
* <AUTHOR>
*/
@Mapper
public interface HealthCheckupDao extends IBaseDao<HealthCheckupDO> {


    default PageResult<HealthCheckupDO> selectPage(HealthCheckupPageReqVO reqVO) {
        Page<HealthCheckupDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<HealthCheckupDO> wrapper = new LambdaQueryWrapperX<HealthCheckupDO>()
            .eqIfPresent(HealthCheckupDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(HealthCheckupDO::getCheckupStatus, reqVO.getCheckupStatus())
            .betweenIfPresent(HealthCheckupDO::getCheckupTime, reqVO.getCheckupTime())
            .eqIfPresent(HealthCheckupDO::getMedicalHistory, reqVO.getMedicalHistory())
            .eqIfPresent(HealthCheckupDO::getDrugAllergy, reqVO.getDrugAllergy())
            .eqIfPresent(HealthCheckupDO::getFamilyMedicalHistory, reqVO.getFamilyMedicalHistory())
            .eqIfPresent(HealthCheckupDO::getHealthStatus, reqVO.getHealthStatus())
            .eqIfPresent(HealthCheckupDO::getBloodPressureHeight, reqVO.getBloodPressureHeight())
            .eqIfPresent(HealthCheckupDO::getBloodPressureLow, reqVO.getBloodPressureLow())
            .eqIfPresent(HealthCheckupDO::getPulse, reqVO.getPulse())
            .eqIfPresent(HealthCheckupDO::getRoutineExamination, reqVO.getRoutineExamination())
            .eqIfPresent(HealthCheckupDO::getBloodRoutineExamination, reqVO.getBloodRoutineExamination())
            .eqIfPresent(HealthCheckupDO::getBUltrasonics, reqVO.getBUltrasonics())
            .eqIfPresent(HealthCheckupDO::getFiveInternalOrgans, reqVO.getFiveInternalOrgans())
            .eqIfPresent(HealthCheckupDO::getUrinarySystem, reqVO.getUrinarySystem())
            .eqIfPresent(HealthCheckupDO::getElectrocardiogram, reqVO.getElectrocardiogram())
            .eqIfPresent(HealthCheckupDO::getRoutineElectrocardiogram, reqVO.getRoutineElectrocardiogram())
            .eqIfPresent(HealthCheckupDO::getXRay, reqVO.getXRay())
            .eqIfPresent(HealthCheckupDO::getChest, reqVO.getChest())
            .eqIfPresent(HealthCheckupDO::getCheckConclusion, reqVO.getCheckConclusion())
            .eqIfPresent(HealthCheckupDO::getCheckupDoctor, reqVO.getCheckupDoctor())
            .likeIfPresent(HealthCheckupDO::getCheckupDoctorName, reqVO.getCheckupDoctorName())
            .betweenIfPresent(HealthCheckupDO::getEnterTime, reqVO.getEnterTime())
            .eqIfPresent(HealthCheckupDO::getJgryxm, reqVO.getJgryxm())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(HealthCheckupDO::getAddTime);
        }
        Page<HealthCheckupDO> healthCheckupPage = selectPage(page, wrapper);
        return new PageResult<>(healthCheckupPage.getRecords(), healthCheckupPage.getTotal());
    }
    default List<HealthCheckupDO> selectList(HealthCheckupListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<HealthCheckupDO>()
            .eqIfPresent(HealthCheckupDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(HealthCheckupDO::getCheckupStatus, reqVO.getCheckupStatus())
            .betweenIfPresent(HealthCheckupDO::getCheckupTime, reqVO.getCheckupTime())
            .eqIfPresent(HealthCheckupDO::getMedicalHistory, reqVO.getMedicalHistory())
            .eqIfPresent(HealthCheckupDO::getDrugAllergy, reqVO.getDrugAllergy())
            .eqIfPresent(HealthCheckupDO::getFamilyMedicalHistory, reqVO.getFamilyMedicalHistory())
            .eqIfPresent(HealthCheckupDO::getHealthStatus, reqVO.getHealthStatus())
            .eqIfPresent(HealthCheckupDO::getBloodPressureHeight, reqVO.getBloodPressureHeight())
            .eqIfPresent(HealthCheckupDO::getBloodPressureLow, reqVO.getBloodPressureLow())
            .eqIfPresent(HealthCheckupDO::getPulse, reqVO.getPulse())
            .eqIfPresent(HealthCheckupDO::getRoutineExamination, reqVO.getRoutineExamination())
            .eqIfPresent(HealthCheckupDO::getBloodRoutineExamination, reqVO.getBloodRoutineExamination())
            .eqIfPresent(HealthCheckupDO::getBUltrasonics, reqVO.getBUltrasonics())
            .eqIfPresent(HealthCheckupDO::getFiveInternalOrgans, reqVO.getFiveInternalOrgans())
            .eqIfPresent(HealthCheckupDO::getUrinarySystem, reqVO.getUrinarySystem())
            .eqIfPresent(HealthCheckupDO::getElectrocardiogram, reqVO.getElectrocardiogram())
            .eqIfPresent(HealthCheckupDO::getRoutineElectrocardiogram, reqVO.getRoutineElectrocardiogram())
            .eqIfPresent(HealthCheckupDO::getXRay, reqVO.getXRay())
            .eqIfPresent(HealthCheckupDO::getChest, reqVO.getChest())
            .eqIfPresent(HealthCheckupDO::getCheckConclusion, reqVO.getCheckConclusion())
            .eqIfPresent(HealthCheckupDO::getCheckupDoctor, reqVO.getCheckupDoctor())
            .likeIfPresent(HealthCheckupDO::getCheckupDoctorName, reqVO.getCheckupDoctorName())
            .betweenIfPresent(HealthCheckupDO::getEnterTime, reqVO.getEnterTime())
            .eqIfPresent(HealthCheckupDO::getJgryxm, reqVO.getJgryxm())
        .orderByDesc(HealthCheckupDO::getAddTime));    }


    }
