package com.rs.module.ihc.service.pm.sick;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.pm.sick.vo.*;
import com.rs.module.ihc.entity.pm.sick.SeverelySickReliveApplyDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 重特病号解除申请 Service 接口
 *
 * <AUTHOR>
 */
public interface SeverelySickReliveApplyService extends IBaseService<SeverelySickReliveApplyDO>{

    /**
     * 创建重特病号解除申请
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createSeverelySickReliveApply(@Valid SeverelySickReliveApplySaveReqVO createReqVO);

    /**
     * 更新重特病号解除申请
     *
     * @param updateReqVO 更新信息
     */
    void updateSeverelySickReliveApply(@Valid SeverelySickReliveApplySaveReqVO updateReqVO);

    /**
     * 删除重特病号解除申请
     *
     * @param id 编号
     */
    void deleteSeverelySickReliveApply(String id);

    /**
     * 获得重特病号解除申请
     *
     * @param id 编号
     * @return 重特病号解除申请
     */
    SeverelySickReliveApplyDO getSeverelySickReliveApply(String id);

    /**
    * 获得重特病号解除申请分页
    *
    * @param pageReqVO 分页查询
    * @return 重特病号解除申请分页
    */
    PageResult<SeverelySickReliveApplyDO> getSeverelySickReliveApplyPage(SeverelySickReliveApplyPageReqVO pageReqVO);

    /**
    * 获得重特病号解除申请列表
    *
    * @param listReqVO 查询条件
    * @return 重特病号解除申请列表
    */
    List<SeverelySickReliveApplyDO> getSeverelySickReliveApplyList(SeverelySickReliveApplyListReqVO listReqVO);


}
