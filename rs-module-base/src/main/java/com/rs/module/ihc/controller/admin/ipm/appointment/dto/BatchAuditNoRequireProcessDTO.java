package com.rs.module.ihc.controller.admin.ipm.appointment.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class BatchAuditNoRequireProcessDTO {

    /**
     * 预约信息的ids
     */
    @NotEmpty(message = "预约信息的id集合不能为空")
    @ApiModelProperty(value = "预约信息的id集合", required = true)
    private List<String> ids;
}
