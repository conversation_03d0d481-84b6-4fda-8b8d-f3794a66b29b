package com.rs.module.ihc.controller.admin.pm;

import cn.hutool.core.util.ObjectUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.pm.dto.BatchSaveMedicineLossDTO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineLossListReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineLossPageReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineLossRespVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineLossSaveReqVO;
import com.rs.module.ihc.entity.pm.PharmacyMedicineLossDO;
import com.rs.module.ihc.service.pm.PharmacyMedicineInService;
import com.rs.module.ihc.service.pm.PharmacyMedicineLossService;
import com.rs.module.ihc.service.pm.PharmacyMedicineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "大药房管理-药品报损")
@RestController
@RequestMapping("/ihc/pm/pharmacyMedicineLoss")
@Validated
public class PharmacyMedicineLossController {

    @Resource
    private PharmacyMedicineLossService pharmacyMedicineLossService;

    @Resource
    private PharmacyMedicineInService pharmacyMedicineInService;

    @Resource
    private PharmacyMedicineService pharmacyMedicineService;

    @PostMapping("/create")
    @ApiOperation(value = "创建大药房管理-药品报损")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicineLoss:create", operateType = LogOperateType.CREATE, title = "创建大药房管理-药品报损",
            success = "创建大药房管理-药品报损成功", fail = "创建大药房管理-药品报损失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPharmacyMedicineLoss(@Valid @RequestBody PharmacyMedicineLossSaveReqVO createReqVO) {
        return success(pharmacyMedicineLossService.createPharmacyMedicineLoss(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新大药房管理-药品报损")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicineLoss:update", operateType = LogOperateType.UPDATE, title = "更新大药房管理-药品报损",
            success = "更新大药房管理-药品报损成功", fail = "更新大药房管理-药品报损失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePharmacyMedicineLoss(@Valid @RequestBody PharmacyMedicineLossSaveReqVO updateReqVO) {
        pharmacyMedicineLossService.updatePharmacyMedicineLoss(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除大药房管理-药品报损")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicineLoss:delete", operateType = LogOperateType.DELETE, title = "删除大药房管理-药品报损",
            success = "删除大药房管理-药品报损成功", fail = "删除大药房管理-药品报损失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePharmacyMedicineLoss(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           pharmacyMedicineLossService.deletePharmacyMedicineLoss(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得大药房管理-药品报损")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicineLoss:get", operateType = LogOperateType.QUERY,
            title = "获取药房管理-药品报损", bizNo = "{{#id}}", success = "获取药房管理-药品报损成功", fail = "获取药房管理-药品报损失败", extraInfo = "{{#id}}")
    public CommonResult<PharmacyMedicineLossRespVO> getPharmacyMedicineLoss(@RequestParam("id") String id) {
        PharmacyMedicineLossDO pharmacyMedicineLoss = pharmacyMedicineLossService.getPharmacyMedicineLoss(id);
        return success(BeanUtils.toBean(pharmacyMedicineLoss, PharmacyMedicineLossRespVO.class));
    }


    @GetMapping("/getDetail")
    @ApiOperation(value = "获得大药房管理-药品报损详情")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicineLoss:get", operateType = LogOperateType.QUERY, title = "获得大药房管理-药品报损详情",
            bizNo = "{{#id}}", success = "获得大药房管理-药品报损详情成功", fail = "获得大药房管理-药品报损详情失败", extraInfo = "{{#id}}")
    public CommonResult<PharmacyMedicineLossRespVO> getMedicineLossDetail(@RequestParam("id") String id) {
        PharmacyMedicineLossDO medicineLoss = pharmacyMedicineLossService.getPharmacyMedicineLoss(id);
        if (ObjectUtil.isEmpty(medicineLoss)) {
            return success(null);
        }
        PharmacyMedicineLossRespVO lossRespVO = BeanUtils.toBean(medicineLoss, PharmacyMedicineLossRespVO.class);
        lossRespVO.setMedicineIn(pharmacyMedicineInService.getMedicineInDetail(medicineLoss.getMedicineInId()));
        lossRespVO.setMedicine(pharmacyMedicineService.getMedicineDetail(medicineLoss.getMedicineId()));
        return success(lossRespVO);
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得大药房管理-药品报损分页")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicineLoss:page", operateType = LogOperateType.QUERY, title = "获得大药房管理-药品报损分页",
            success = "获得大药房管理-药品报损分页成功", fail = "获得大药房管理-药品报损分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<PharmacyMedicineLossRespVO>> getPharmacyMedicineLossPage(@Valid @RequestBody PharmacyMedicineLossPageReqVO pageReqVO) {
        PageResult<PharmacyMedicineLossDO> pageResult = pharmacyMedicineLossService.getPharmacyMedicineLossPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PharmacyMedicineLossRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得大药房管理-药品报损列表")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicineLoss:list", operateType = LogOperateType.QUERY, title = "获得大药房管理-药品报损列表",
            success = "获得大药房管理-药品报损列表成功", fail = "获得大药房管理-药品报损列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<PharmacyMedicineLossRespVO>> getPharmacyMedicineLossList(@Valid @RequestBody PharmacyMedicineLossListReqVO listReqVO) {
        List<PharmacyMedicineLossDO> list = pharmacyMedicineLossService.getPharmacyMedicineLossList(listReqVO);
        return success(BeanUtils.toBean(list, PharmacyMedicineLossRespVO.class));
    }

    @PostMapping("/batchSave")
    @ApiOperation("批量新增报损批次")
    public CommonResult<Void> batchSaveMedicineLoss(@RequestBody @Valid BatchSaveMedicineLossDTO batchSaveMedicineLossDTO) {
        pharmacyMedicineLossService.batchSaveMedicineLoss(batchSaveMedicineLossDTO.getMedicineLossList(), batchSaveMedicineLossDTO.getId());
        return CommonResult.success();
    }
}
