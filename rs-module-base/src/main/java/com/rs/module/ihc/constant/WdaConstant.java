package com.rs.module.ihc.constant;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @create 2025/2/11 9:56
 */
public interface WdaConstant {
    /**
     * 更新类型：新增
     */
    String UPDATE_TYPE_INSERT = "insert";
    /**
     * 更新类型：更新
     */
    String UPDATE_TYPE_UPDATE = "update";
    /**
     * 更新类型：删除
     */
    String UPDATE_TYPE_DELETE = "delete";

    /**
     * 本地表名：药品表
     */
    String TABLE_NAME_IHCS_MEDICINE = "ihcs_medicine";

    /**
     * 本地表名：处方表
     */
    String TABLE_NAME_IHCS_INTERNAL_MEDICAL_PRESCRIBE = "TABLE_NAME_IHCS_INTERNAL_MEDICAL_PRESCRIBE";

    /**
     * 本地表名：现场巡诊表
     */
    String TABLE_NAME_IHCS_INTERNAL_MEDICAL_VISIT = "IHCS_INTERNAL_MEDICAL_VISIT";

}
