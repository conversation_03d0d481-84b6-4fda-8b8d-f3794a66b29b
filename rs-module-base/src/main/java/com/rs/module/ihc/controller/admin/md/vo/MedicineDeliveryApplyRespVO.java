package com.rs.module.ihc.controller.admin.md.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.base.vo.ApproveTraceVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 药品顾送管理-药品顾送申请 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MedicineDeliveryApplyRespVO extends BaseVO implements TransPojo{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("申请-药品名称")
    private String applyMedicineName;

    @ApiModelProperty("申请-剂型（字典:剂型  ZD_DOSAGE_FORM）")
    @Trans(type = TransType.DICTIONARY,key = "ZD_DOSAGE_FORM")
    private String applyDosageForm;

    @ApiModelProperty("申请规格")
    private String applySpecs;

    @ApiModelProperty("申请-生产单位")
    private String applyProductUnit;

    @ApiModelProperty("申请-批准文号")
    private String applyApprovalNum;

    @ApiModelProperty("申请-原批准文号")
    private String applyOriginalApprovalNum;

    @ApiModelProperty("申请-数量")
    private BigDecimal applyTotalNum;

    @ApiModelProperty("期望送药开始日期")
    private Date expectedStartDate;

    @ApiModelProperty("期望送药结束日期")
    private Date expectedEndDate;

    @ApiModelProperty("顾送药来源 字典：ZD_YPGS_GSYLY")
    @Trans(type = TransType.DICTIONARY,key = "ZD_YPGS_GSYLY")
    private String drugSource;

    @ApiModelProperty("顾送药原因 字典： ZD_YPGS_GSYY")
    @Trans(type = TransType.DICTIONARY,key = "ZD_YPGS_GSYY")
    private String deliveryReason;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否一致")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SFDM")
    private Short isConsistent;

    @ApiModelProperty("不一致原因")
    private String reasonForInconsistency;

    @ApiModelProperty("实际送药-药品名称")
    private String deliveryMedicineName;

    @ApiModelProperty("实际送药--剂型（字典:ZD_DOSAGE_FORM ）")
    @Trans(type = TransType.DICTIONARY,key = "ZD_DOSAGE_FORM")
    private String deliveryDosageForm;

    @ApiModelProperty("实际送药-规格")
    private String deliverySpecs;

    @ApiModelProperty("实际送药-生产单位")
    private String deliveryProductUnit;

    @ApiModelProperty("实际送药-批准文号")
    private String deliveryApprovalNum;

    @ApiModelProperty("实际送药-原批准文号")
    private String deliveryOriginalApprovalNum;

    @ApiModelProperty("实际送药-数量")
    private BigDecimal deliveryTotalNum;

    @ApiModelProperty("有效期开始")
    private Date expiryStartDate;

    @ApiModelProperty("有效期结束")
    private Date expiryEndDate;

    @ApiModelProperty("药品照片URL")
    private String imgUrl;

    @ApiModelProperty("送药日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryDate;

    @ApiModelProperty("送药备注")
    private String deliveryRemark;

    @ApiModelProperty("异常原因 ZD_YPGS_YCYY ")
    @Trans(type = TransType.DICTIONARY,key = "ZD_YPGS_YCYY")
    private String exceptionReason;

    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY,key = "ZD_YPGS_ZT")
    private String status;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("审批信息节点信息")
    private List<ApproveTraceVO> approveList;

    @ApiModelProperty("登记人")
    private String addUserName;

    @ApiModelProperty("登记时间")
    private Date addTime;

}
