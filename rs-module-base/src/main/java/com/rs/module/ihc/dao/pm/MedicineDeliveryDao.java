package com.rs.module.ihc.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.pm.MedicineDeliveryDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 药房管理-顾送药品信息 Dao
*
* <AUTHOR>
*/
@Mapper
public interface MedicineDeliveryDao extends IBaseDao<MedicineDeliveryDO> {


    default PageResult<MedicineDeliveryDO> selectPage(MedicineDeliveryPageReqVO reqVO) {
        Page<MedicineDeliveryDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<MedicineDeliveryDO> medicineDeliveryPage = selectPage(page, new LambdaQueryWrapperX<MedicineDeliveryDO>()
            .eqIfPresent(MedicineDeliveryDO::getPharmacyId, reqVO.getPharmacyId())
            .likeIfPresent(MedicineDeliveryDO::getMedicineName, reqVO.getMedicineName())
            .likeIfPresent(MedicineDeliveryDO::getMedicineSpellName, reqVO.getMedicineSpellName())
            .eqIfPresent(MedicineDeliveryDO::getMedicineNameFirstLetter, reqVO.getMedicineNameFirstLetter())
            .eqIfPresent(MedicineDeliveryDO::getDosageForm, reqVO.getDosageForm())
            .eqIfPresent(MedicineDeliveryDO::getMeasurementUnit, reqVO.getMeasurementUnit())
            .eqIfPresent(MedicineDeliveryDO::getSpecs, reqVO.getSpecs())
            .eqIfPresent(MedicineDeliveryDO::getProductUnit, reqVO.getProductUnit())
            .eqIfPresent(MedicineDeliveryDO::getApprovalNum, reqVO.getApprovalNum())
            .eqIfPresent(MedicineDeliveryDO::getOriginalApprovalNum, reqVO.getOriginalApprovalNum())
            .eqIfPresent(MedicineDeliveryDO::getMedicineStandardCode, reqVO.getMedicineStandardCode())
            .eqIfPresent(MedicineDeliveryDO::getDrug, reqVO.getDrug())
            .eqIfPresent(MedicineDeliveryDO::getDayDosage, reqVO.getDayDosage())
            .eqIfPresent(MedicineDeliveryDO::getOneDosage, reqVO.getOneDosage())
            .eqIfPresent(MedicineDeliveryDO::getInventoryWarnValue, reqVO.getInventoryWarnValue())
            .eqIfPresent(MedicineDeliveryDO::getReportableLoss, reqVO.getReportableLoss())
            .likeIfPresent(MedicineDeliveryDO::getMedicineEnglishName, reqVO.getMedicineEnglishName())
            .likeIfPresent(MedicineDeliveryDO::getProductName, reqVO.getProductName())
            .eqIfPresent(MedicineDeliveryDO::getMarketApprovalHolder, reqVO.getMarketApprovalHolder())
            .eqIfPresent(MedicineDeliveryDO::getMarketApprovalHolderAddress, reqVO.getMarketApprovalHolderAddress())
            .eqIfPresent(MedicineDeliveryDO::getProductAddress, reqVO.getProductAddress())
            .eqIfPresent(MedicineDeliveryDO::getMedicineCategory, reqVO.getMedicineCategory())
            .eqIfPresent(MedicineDeliveryDO::getMedicineStandardCodeRemark, reqVO.getMedicineStandardCodeRemark())
            .eqIfPresent(MedicineDeliveryDO::getMedicineAlias, reqVO.getMedicineAlias())
            .eqIfPresent(MedicineDeliveryDO::getPackageUnit, reqVO.getPackageUnit())
            .betweenIfPresent(MedicineDeliveryDO::getApprovalDate, reqVO.getApprovalDate())
            .eqIfPresent(MedicineDeliveryDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
            .eqIfPresent(MedicineDeliveryDO::getHasExpireDateBatch, reqVO.getHasExpireDateBatch())
            .eqIfPresent(MedicineDeliveryDO::getMinMeasurementUnit, reqVO.getMinMeasurementUnit())
            .eqIfPresent(MedicineDeliveryDO::getUnitConversionRatio, reqVO.getUnitConversionRatio())
            .eqIfPresent(MedicineDeliveryDO::getRybh, reqVO.getRybh())
            .eqIfPresent(MedicineDeliveryDO::getRyxm, reqVO.getRyxm())
            .eqIfPresent(MedicineDeliveryDO::getBelongSensitive, reqVO.getBelongSensitive())
            .eqIfPresent(MedicineDeliveryDO::getPrisonId, reqVO.getPrisonId())
            .orderByDesc(MedicineDeliveryDO::getId));
            return new PageResult<>(medicineDeliveryPage.getRecords(), medicineDeliveryPage.getTotal());
    }
    default List<MedicineDeliveryDO> selectList(MedicineDeliveryListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MedicineDeliveryDO>()
            .eqIfPresent(MedicineDeliveryDO::getPharmacyId, reqVO.getPharmacyId())
            .likeIfPresent(MedicineDeliveryDO::getMedicineName, reqVO.getMedicineName())
            .likeIfPresent(MedicineDeliveryDO::getMedicineSpellName, reqVO.getMedicineSpellName())
            .eqIfPresent(MedicineDeliveryDO::getMedicineNameFirstLetter, reqVO.getMedicineNameFirstLetter())
            .eqIfPresent(MedicineDeliveryDO::getDosageForm, reqVO.getDosageForm())
            .eqIfPresent(MedicineDeliveryDO::getMeasurementUnit, reqVO.getMeasurementUnit())
            .eqIfPresent(MedicineDeliveryDO::getSpecs, reqVO.getSpecs())
            .eqIfPresent(MedicineDeliveryDO::getProductUnit, reqVO.getProductUnit())
            .eqIfPresent(MedicineDeliveryDO::getApprovalNum, reqVO.getApprovalNum())
            .eqIfPresent(MedicineDeliveryDO::getOriginalApprovalNum, reqVO.getOriginalApprovalNum())
            .eqIfPresent(MedicineDeliveryDO::getMedicineStandardCode, reqVO.getMedicineStandardCode())
            .eqIfPresent(MedicineDeliveryDO::getDrug, reqVO.getDrug())
            .eqIfPresent(MedicineDeliveryDO::getDayDosage, reqVO.getDayDosage())
            .eqIfPresent(MedicineDeliveryDO::getOneDosage, reqVO.getOneDosage())
            .eqIfPresent(MedicineDeliveryDO::getInventoryWarnValue, reqVO.getInventoryWarnValue())
            .eqIfPresent(MedicineDeliveryDO::getReportableLoss, reqVO.getReportableLoss())
            .likeIfPresent(MedicineDeliveryDO::getMedicineEnglishName, reqVO.getMedicineEnglishName())
            .likeIfPresent(MedicineDeliveryDO::getProductName, reqVO.getProductName())
            .eqIfPresent(MedicineDeliveryDO::getMarketApprovalHolder, reqVO.getMarketApprovalHolder())
            .eqIfPresent(MedicineDeliveryDO::getMarketApprovalHolderAddress, reqVO.getMarketApprovalHolderAddress())
            .eqIfPresent(MedicineDeliveryDO::getProductAddress, reqVO.getProductAddress())
            .eqIfPresent(MedicineDeliveryDO::getMedicineCategory, reqVO.getMedicineCategory())
            .eqIfPresent(MedicineDeliveryDO::getMedicineStandardCodeRemark, reqVO.getMedicineStandardCodeRemark())
            .eqIfPresent(MedicineDeliveryDO::getMedicineAlias, reqVO.getMedicineAlias())
            .eqIfPresent(MedicineDeliveryDO::getPackageUnit, reqVO.getPackageUnit())
            .betweenIfPresent(MedicineDeliveryDO::getApprovalDate, reqVO.getApprovalDate())
            .eqIfPresent(MedicineDeliveryDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
            .eqIfPresent(MedicineDeliveryDO::getHasExpireDateBatch, reqVO.getHasExpireDateBatch())
            .eqIfPresent(MedicineDeliveryDO::getMinMeasurementUnit, reqVO.getMinMeasurementUnit())
            .eqIfPresent(MedicineDeliveryDO::getUnitConversionRatio, reqVO.getUnitConversionRatio())
            .eqIfPresent(MedicineDeliveryDO::getRybh, reqVO.getRybh())
            .eqIfPresent(MedicineDeliveryDO::getRyxm, reqVO.getRyxm())
            .eqIfPresent(MedicineDeliveryDO::getBelongSensitive, reqVO.getBelongSensitive())
            .eqIfPresent(MedicineDeliveryDO::getPrisonId, reqVO.getPrisonId())
        .orderByDesc(MedicineDeliveryDO::getId));    }


    }
