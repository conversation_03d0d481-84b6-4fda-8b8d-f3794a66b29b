package com.rs.module.ihc.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.ihc.controller.admin.ipm.bo.UpdateMedicineInInventoryNumBO;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.PharmacyMedicineInDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 大药房管理-药品入库批次 Service 接口
 *
 * <AUTHOR>
 */
public interface PharmacyMedicineInService extends IBaseService<PharmacyMedicineInDO>{

    /**
     * 创建大药房管理-药品入库批次
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPharmacyMedicineIn(@Valid PharmacyMedicineInSaveReqVO medicineInSaveReqVO);

    /**
     * 更新大药房管理-药品入库批次
     *
     * @param updateReqVO 更新信息
     */
    void updatePharmacyMedicineIn(@Valid PharmacyMedicineInSaveReqVO updateReqVO);

    /**
     * 删除大药房管理-药品入库批次
     *
     * @param id 编号
     */
    void deletePharmacyMedicineIn(String id);

    /**
     * 获得大药房管理-药品入库批次
     *
     * @param id 编号
     * @return 大药房管理-药品入库批次
     */
    PharmacyMedicineInDO getPharmacyMedicineIn(String id);

    /**
    * 获得大药房管理-药品入库批次分页
    *
    * @param pageReqVO 分页查询
    * @return 大药房管理-药品入库批次分页
    */
    PageResult<PharmacyMedicineInDO> getPharmacyMedicineInPage(PharmacyMedicineInPageReqVO pageReqVO);

    /**
    * 获得大药房管理-药品入库批次列表
    *
    * @param listReqVO 查询条件
    * @return 大药房管理-药品入库批次列表
    */
    List<PharmacyMedicineInDO> getPharmacyMedicineInList(PharmacyMedicineInListReqVO listReqVO);

    /**
     * /返回详情信息
     * <AUTHOR>
     * @date 2025/7/15 14:25
     * @param [medicineInId]
     * @return com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineInRespVO
     */
    PharmacyMedicineInRespVO getMedicineInDetail(String medicineInId);


    /**
     * 批量比较替换入库批次的库存数量
     * <p>
     * 将批次的源库存更新为目标库存，只有当源库存没有被其他操作修改时才能更新成功，否则更新失败
     *
     * @param updateMedicineInInventoryNumBOList 入库批次库存数量比较替换集合
     */
    void batchCompareAndSetInventoryNum(Collection<UpdateMedicineInInventoryNumBO> updateMedicineInInventoryNumBOList);


    /**
     * 查询过期批次药品并且更新药品的标识
     * <p>
     * 当传入的medicineIdList不为空时，将先去更新药品的是否存在过期批次为否，然后再根据查询重新设置是否存在过期批次
     *
     * @param medicineIdList 药品id集合 指定查询某些药品批次，为空时查询全部的存在过期批次的药品进行标识
     */
    void selectExpireBatchAndUpdateMedicineFlag(List<String> medicineIdList);

    /**
     * 获得药房管理-药品入库批次列表
     * <AUTHOR>
     * @date 2025/7/15 16:21
     * @param [listReqVO]
     * @return java.util.List<com.rs.module.ihc.entity.pm.PharmacyMedicineInDO>
     */
    List<PharmacyMedicineInDO> ypbsList(MedicineInYpbsReqVO listReqVO);
}
