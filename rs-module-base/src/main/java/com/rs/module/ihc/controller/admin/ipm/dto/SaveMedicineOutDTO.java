package com.rs.module.ihc.controller.admin.ipm.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaveMedicineOutDTO {

    /**
     * 入库批次id
     */
    @NotNull(message = "入库批次id不能为空")
    @ApiModelProperty(value = "入库批次id", required = true)
    private String medicineInId;

    /**
     * 出库原因 字典值
     */
    @NotBlank(message = "出库原因不能为空")
    @ApiModelProperty(value = "出库原因 字典值", required = true)
    private String outStorageReason;

    /**
     * 出库数量
     */
    @Positive(message = "出库数量不能小于0")
    @NotNull(message = "出库数量不能为空")
    @ApiModelProperty(value = "出库数量", required = true)
    private BigDecimal outNum;

    /**
     * 取药单位
     */
    @NotBlank(message = "取药单位不能为空")
    @ApiModelProperty(value = "取药单位", required = true)
    private String dispensaryUnit;

    /**
     * 取药时间
     */
    @NotNull(message = "取药时间不能为空")
    @ApiModelProperty(value = "取药时间 yyyy-MM-dd HH:mm:ss", required = true)
    private Date dispensaryTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

}
