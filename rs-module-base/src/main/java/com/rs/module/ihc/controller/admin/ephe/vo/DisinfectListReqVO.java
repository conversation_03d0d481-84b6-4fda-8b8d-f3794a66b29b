package com.rs.module.ihc.controller.admin.ephe.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 卫生防疫与健康教育-卫生消毒登记列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DisinfectListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("夏季每周洗热水澡次数")
    private String xjmzxrszcs;

    @ApiModelProperty("每15天热水澡次数")
    private Integer mswtrszcs;

    @ApiModelProperty("每周晾晒被褥次数	")
    private Integer mzlsbrcs;

    @ApiModelProperty("每季度拆洗被褥次数")
    private Integer mjdcxbrcs;

    @ApiModelProperty("每周消毒次数")
    private Integer mzxdcs;

    @ApiModelProperty("登记民警身份证号")
    private String operatePoliceSfzh;

    @ApiModelProperty("登记民警")
    private String operatePolice;

    @ApiModelProperty("登记时间")
    private Date[] operateTime;

}
