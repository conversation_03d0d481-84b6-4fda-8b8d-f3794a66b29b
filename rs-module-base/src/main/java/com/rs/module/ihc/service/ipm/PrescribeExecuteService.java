package com.rs.module.ihc.service.ipm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.PrescribeExecuteDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 所内就医-医嘱/处方执行记录 Service 接口
 *
 * <AUTHOR>
 */
public interface PrescribeExecuteService extends IBaseService<PrescribeExecuteDO>{

    /**
     * 创建所内就医-医嘱/处方执行记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrescribeExecute(@Valid PrescribeExecuteSaveReqVO createReqVO);

    /**
     * 更新所内就医-医嘱/处方执行记录
     *
     * @param updateReqVO 更新信息
     */
    void updatePrescribeExecute(@Valid PrescribeExecuteSaveReqVO updateReqVO);

    /**
     * 删除所内就医-医嘱/处方执行记录
     *
     * @param id 编号
     */
    void deletePrescribeExecute(String id);

    /**
     * 获得所内就医-医嘱/处方执行记录
     *
     * @param id 编号
     * @return 所内就医-医嘱/处方执行记录
     */
    PrescribeExecuteDO getPrescribeExecute(String id);

    /**
    * 获得所内就医-医嘱/处方执行记录分页
    *
    * @param pageReqVO 分页查询
    * @return 所内就医-医嘱/处方执行记录分页
    */
    PageResult<PrescribeExecuteDO> getPrescribeExecutePage(PrescribeExecutePageReqVO pageReqVO);

    /**
    * 获得所内就医-医嘱/处方执行记录列表
    *
    * @param listReqVO 查询条件
    * @return 所内就医-医嘱/处方执行记录列表
    */
    List<PrescribeExecuteDO> getPrescribeExecuteList(PrescribeExecuteListReqVO listReqVO);


    /**
     * 服药台账
     * @param prescribeExecuteListReqVO
     * @return
     */
    public List<PrescribeExecuteFytzRespVO> fyList(PrescribeExecuteFytzReqVO prescribeExecuteListReqVO);

    /**
     * 获取风险告知书（未签名前）
     *
     * @param executionId
     * @return
     */
    public byte[] getRiskDisclosurePdf(String executionId);

    /**
     * 签署风险告知书
     *
     * @return
     */
    public void signRiskDisclosurePdf(SignRiskDisclosurePdfVO signRiskDisclosurePdfVO);

    public List<PrescribeExecuteFytzRespVO> selectFxgzsTodo(PrescribeExecuteFxgzTodoReqVO prescribeExecuteListReqVO);
    public List<PrescribeExecuteFytzRespVO> selectFxgzsSuccess(PrescribeExecuteFxgzTodoReqVO prescribeExecuteListReqVO);
}
