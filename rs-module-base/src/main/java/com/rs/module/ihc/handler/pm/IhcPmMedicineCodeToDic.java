package com.rs.module.ihc.handler.pm;

import com.rs.module.ihc.controller.admin.pm.vo.MedicineCodeImportVO;
import com.rs.module.ihc.controller.admin.pm.vo.ValidationResult;
import com.rs.module.ihc.service.pm.IhcPmMedicineCodeBuilder;

import java.util.Map;
import java.util.Set;

/**
 * @ClassName IhcPmMedicineCodeValidate
 * @Description 字典翻译
 * <AUTHOR>
 * @Date 2025/3/18 15:30
 * @Version 1.0
 */
public class IhcPmMedicineCodeToDic extends IhcPmMedicineCodeBuilder<MedicineCodeImportVO> {
    Map<String, String> dosageFormDic = null;

    public IhcPmMedicineCodeToDic(Map<String, String> dosageFormDic) {
        this.dosageFormDic = dosageFormDic;
    }

    @Override
    public ValidationResult importExcel(MedicineCodeImportVO toValidate) {
        Set<String> keys = dosageFormDic.keySet();
        for (String key: keys) {
            if (toValidate.getDosageForm().contains(dosageFormDic.get(key))) {
                toValidate.setDosageForm(key);
                break;
            }
        }
        return checkNext(toValidate);
    }
}
