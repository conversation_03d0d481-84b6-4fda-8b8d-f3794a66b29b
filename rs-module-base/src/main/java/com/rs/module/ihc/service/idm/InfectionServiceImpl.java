package com.rs.module.ihc.service.idm;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.idm.vo.InfectionSaveReqVO;
import com.rs.module.ihc.dao.idm.InfectionDao;
import com.rs.module.ihc.entity.idm.InfectionDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;


/**
 * 传染病管理-传染病管理登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InfectionServiceImpl extends BaseServiceImpl<InfectionDao, InfectionDO> implements InfectionService {

    @Resource
    private InfectionDao infectionDao;

    @Override
    public String createInfection(InfectionSaveReqVO createReqVO) {
        // 插入
        InfectionDO infection = BeanUtils.toBean(createReqVO, InfectionDO.class);
        SessionUser user = SessionUserUtil.getSessionUser();
        infection.setOperatePoliceSfzh(user.getIdCard());
        infection.setOperatePolice(user.getName());
        infection.setOperateTime(new Date());
        infectionDao.insert(infection);
        return infection.getId();
    }



    @Override
    public InfectionDO getInfection(String id) {
        return infectionDao.selectById(id);
    }



}
