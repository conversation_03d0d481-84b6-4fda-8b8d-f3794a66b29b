package com.rs.module.ihc.service.pm;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.framework.mybatis.util.BizAssert;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineListReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicinePageReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineRespVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineSaveReqVO;
import com.rs.module.ihc.dao.ipm.VisitMedicineDao;
import com.rs.module.ihc.dao.pm.MedicineDao;
import com.rs.module.ihc.entity.ipm.VisitMedicineDO;
import com.rs.module.ihc.entity.pm.MedicineDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 药房管理-药品信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class MedicineServiceImpl extends BaseServiceImpl<MedicineDao, MedicineDO> implements MedicineService {

    @Resource
    private MedicineDao medicineDao;
    @Resource
    private VisitMedicineDao visitMedicineDao;

    @Override
    public String createMedicine(MedicineSaveReqVO createReqVO) {
        // 插入
        MedicineDO medicine = BeanUtils.toBean(createReqVO, MedicineDO.class);
        medicineDao.insert(medicine);
        // 返回
        return medicine.getId();
    }

    @Override
    public void updateMedicine(MedicineSaveReqVO updateReqVO) {
        // 校验存在
        validateMedicineExists(updateReqVO.getId());
        // 更新
        MedicineDO updateObj = BeanUtils.toBean(updateReqVO, MedicineDO.class);
        medicineDao.updateById(updateObj);
    }

    @Override
    public void deleteMedicine(String id) {
        // 校验存在
        validateMedicineExists(id);
        // 删除
        medicineDao.deleteById(id);
    }

    private void validateMedicineExists(String id) {
        if (medicineDao.selectById(id) == null) {
            throw new ServerException("药房管理-药品信息数据不存在");
        }
    }

    @Override
    public MedicineDO getMedicine(String id) {
        return medicineDao.selectById(id);
    }

    @Override
    public MedicineRespVO getMedicineDetail(String id) {
        MedicineDO medicineDO = medicineDao.selectById(id);
        return BeanUtils.toBean(medicineDO, MedicineRespVO.class);
    }

    @Override
    public PageResult<MedicineDO> getMedicinePage(MedicinePageReqVO pageReqVO) {
        return medicineDao.selectPage(pageReqVO);
    }

    @Override
    public List<MedicineDO> getMedicineList(MedicineListReqVO listReqVO) {
        return medicineDao.selectList(listReqVO);
    }

    @Override
    public void getMedicineListByPharmacyId(MedicineListReqVO medicineList) {
        log.info("medicineList:{}", medicineList);
    }

    @Override
    public void init() {
        List<MedicineDO> list = list();
        list.forEach(medicineDO -> {
            String firstLetter = PinyinUtil.getFirstLetter(medicineDO.getMedicineName(), "");
            String pinyin = PinyinUtil.getPinyin(medicineDO.getMedicineName(), "");
            medicineDO.setPinyin(pinyin);
            medicineDO.setJp(firstLetter);
            LambdaQueryWrapperX<VisitMedicineDO> countWrapperX = new LambdaQueryWrapperX<>();
            countWrapperX.eq(VisitMedicineDO::getMedicineId, medicineDO.getId());
            Integer count = visitMedicineDao.selectCount(countWrapperX);
            medicineDO.setPx(count);
            medicineDao.updateById(medicineDO);
        });

    }
    @Override
    public void checkMedicineHasNotBelongUser(List<String> medicineIds, String supervisedUserCode) {
        if (CollectionUtils.isEmpty(medicineIds)) {
            return;
        }

        //TODO 广州版本的逻辑 顾送药判断

//        Page<IhcsMedicine> ihcsMedicinePage = this.lambdaQuery()
//                .select(IhcsMedicine::getId, IhcsMedicine::getMedicineName, IhcsMedicine::getCareForUserName)
//                .in(IhcsMedicine::getId, medicineIds)
//                .eq(IhcsMedicine::getCareForGive, BooleanIntConstant.TRUE)
//                .ne(IhcsMedicine::getCareForUserCode, supervisedUserCode)
//                .page(PageQueryUtil.createOnePageNoSearchCount());
//        IhcsMedicine ihcsMedicine = PageQueryUtil.getOnePageRecord(ihcsMedicinePage);
//        if (Objects.nonNull(ihcsMedicine)) {
//            throw new BizException(String.format("药品[%s]为专人[%s]顾送药，不可添加",
//                    ihcsMedicine.getMedicineName(), ihcsMedicine.getCareForUserName()));
//        }
    }

    @Override
    public MedicineDO getOnlyMedicine(String medicineName, String specs, String orgCode) {
       List<MedicineDO> medicineList = medicineDao.selectList(new LambdaQueryWrapper<MedicineDO>()
                .eq(MedicineDO::getSpecs, StringUtils.trim(specs))
                .eq(MedicineDO::getOrgCode, orgCode)
                .eq(MedicineDO::getMedicineName, medicineName));

        if (CollectionUtil.isEmpty(medicineList)) {
            return null;
        }
        BizAssert.isTrue(medicineList.size() <= 1, "药房中存在药品名称和规格同样的多种药品");
        return medicineList.get(0);
    }

}
