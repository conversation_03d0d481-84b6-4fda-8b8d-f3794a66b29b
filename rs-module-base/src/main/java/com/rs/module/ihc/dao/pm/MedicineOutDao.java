package com.rs.module.ihc.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.pm.MedicineOutDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 药房管理-药品出库 Dao
*
* <AUTHOR>
*/
@Mapper
public interface MedicineOutDao extends IBaseDao<MedicineOutDO> {


    default PageResult<MedicineOutDO> selectPage(MedicineOutPageReqVO reqVO) {
        Page<MedicineOutDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<MedicineOutDO> wrapper = new LambdaQueryWrapperX<MedicineOutDO>()
            .eqIfPresent(MedicineOutDO::getMedicineId, reqVO.getMedicineId())
            .eqIfPresent(MedicineOutDO::getMedicineInId, reqVO.getMedicineInId())
            .eqIfPresent(MedicineOutDO::getOutStorageReason, reqVO.getOutStorageReason())
            .eqIfPresent(MedicineOutDO::getOutNum, reqVO.getOutNum())
            .eqIfPresent(MedicineOutDO::getDispensaryUnit, reqVO.getDispensaryUnit())
            .betweenIfPresent(MedicineOutDO::getDispensaryTime, reqVO.getDispensaryTime())
            .eqIfPresent(MedicineOutDO::getUserid, reqVO.getUserid())
            .likeIfPresent(MedicineOutDO::getUserName, reqVO.getUserName())
            .eqIfPresent(MedicineOutDO::getRemark, reqVO.getRemark())
            .eqIfPresent(MedicineOutDO::getInventoryNum, reqVO.getInventoryNum())
            .eqIfPresent(MedicineOutDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
            .eqIfPresent(MedicineOutDO::getOutStatus, reqVO.getOutStatus())
            .eqIfPresent(MedicineOutDO::getWorkflowEnd, reqVO.getWorkflowEnd())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(MedicineOutDO::getAddTime);
        }
        Page<MedicineOutDO> medicineOutPage = selectPage(page, wrapper);
        return new PageResult<>(medicineOutPage.getRecords(), medicineOutPage.getTotal());
    }
    default List<MedicineOutDO> selectList(MedicineOutListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MedicineOutDO>()
            .eqIfPresent(MedicineOutDO::getMedicineId, reqVO.getMedicineId())
            .eqIfPresent(MedicineOutDO::getMedicineInId, reqVO.getMedicineInId())
            .eqIfPresent(MedicineOutDO::getOutStorageReason, reqVO.getOutStorageReason())
            .eqIfPresent(MedicineOutDO::getOutNum, reqVO.getOutNum())
            .eqIfPresent(MedicineOutDO::getDispensaryUnit, reqVO.getDispensaryUnit())
            .betweenIfPresent(MedicineOutDO::getDispensaryTime, reqVO.getDispensaryTime())
            .eqIfPresent(MedicineOutDO::getUserid, reqVO.getUserid())
            .likeIfPresent(MedicineOutDO::getUserName, reqVO.getUserName())
            .eqIfPresent(MedicineOutDO::getRemark, reqVO.getRemark())
            .eqIfPresent(MedicineOutDO::getInventoryNum, reqVO.getInventoryNum())
            .eqIfPresent(MedicineOutDO::getTotalInventoryNum, reqVO.getTotalInventoryNum())
            .eqIfPresent(MedicineOutDO::getOutStatus, reqVO.getOutStatus())
            .eqIfPresent(MedicineOutDO::getWorkflowEnd, reqVO.getWorkflowEnd())
        .orderByDesc(MedicineOutDO::getAddTime));    }


    }
