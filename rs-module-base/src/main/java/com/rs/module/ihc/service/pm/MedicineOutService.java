package com.rs.module.ihc.service.pm;

import java.util.*;
import javax.validation.*;

import com.rs.module.ihc.controller.admin.ipm.dto.SaveMedicineOutDTO;
import com.rs.module.ihc.controller.admin.ipm.vo.PrescribeMedicineOutReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.MedicineInDO;
import com.rs.module.ihc.entity.pm.MedicineOutDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 药房管理-药品出库 Service 接口
 *
 * <AUTHOR>
 */
public interface MedicineOutService extends IBaseService<MedicineOutDO>{

    /**
     * 创建药房管理-药品出库
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMedicineOut(@Valid MedicineOutSaveReqVO createReqVO);

    /**
     * 更新药房管理-药品出库
     *
     * @param updateReqVO 更新信息
     */
    void updateMedicineOut(@Valid MedicineOutSaveReqVO updateReqVO);

    /**
     * 删除药房管理-药品出库
     *
     * @param id 编号
     */
    void deleteMedicineOut(String id);

    /**
     * 获得药房管理-药品出库
     *
     * @param id 编号
     * @return 药房管理-药品出库
     */
    MedicineOutDO getMedicineOut(String id);

    /**
    * 获得药房管理-药品出库分页
    *
    * @param pageReqVO 分页查询
    * @return 药房管理-药品出库分页
    */
    PageResult<MedicineOutDO> getMedicineOutPage(MedicineOutPageReqVO pageReqVO);

    /**
    * 获得药房管理-药品出库列表
    *
    * @param listReqVO 查询条件
    * @return 药房管理-药品出库列表
    */
    List<MedicineOutDO> getMedicineOutList(MedicineOutListReqVO listReqVO);

    /**
     * 批量新增出库批次
     *
     * @param medicineOutList          出库批次列表
     * @param medicineId               药品id
     * @param dataSourceIhcsMedicineIn 出库批次对应的数据库源数据
     */
    void batchSaveMedicineOutBySourceData(List<SaveMedicineOutDTO> medicineOutList,
                                          String medicineId,
                                          List<MedicineInDO> dataSourceIhcsMedicineIn);

    /**
     * 针对药品的自动扣减出库
     * 每个药品都有对应的入库批次，按照先进先出的原则排列批次，
     * 扣减的数量可能有一个批次出库就够了，可能需要多个批次出库，这样就需要组装药品出库数量和批次的关系
     * 然后按照 {@link IhcsMedicineOutService#batchSaveMedicineOutBySourceData(List, Long, List)} 的格式组装分批次出库
     *
     * @param prescribeMedicineOutInfoList 药品扣减信息
     * @param remark                       自动扣减出库备注
     */
    void batchAutoMedicalOut(Collection<PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo>
                                     prescribeMedicineOutInfoList, String remark);

    /**
     * 批量新增出库批次
     *
     * @param medicineOutList 出库批次列表
     * @param medicineId      药品id
     */
    void batchSaveMedicineOut(List<SaveMedicineOutDTO> medicineOutList, String medicineId);

}
