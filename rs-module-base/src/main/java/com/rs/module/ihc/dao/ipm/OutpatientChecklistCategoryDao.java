package com.rs.module.ihc.dao.ipm;

import java.util.*;

import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.ihc.entity.ipm.OutpatientChecklistCategoryDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 所内就医-所内门诊-检查单-检查种类
 *
 * <AUTHOR>
 */
@Mapper
public interface OutpatientChecklistCategoryDao extends IBaseDao<OutpatientChecklistCategoryDO> {

    default List<OutpatientChecklistCategoryDO> selectListByChecklistId(String checklistId) {
        return selectList(new LambdaQueryWrapperX<OutpatientChecklistCategoryDO>().eq(OutpatientChecklistCategoryDO::getChecklistId, checklistId));
    }

    default int deleteByChecklistId(String checklistId) {
        return delete(new LambdaQueryWrapperX<OutpatientChecklistCategoryDO>().eq(OutpatientChecklistCategoryDO::getChecklistId, checklistId));
    }

}
