package com.rs.module.acp.service.pm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import com.rs.module.base.vo.RoomVideoVO;
import java.util.*;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.BaseDeviceCameraDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pm.BaseDeviceCameraDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-监管管理-摄像机设备 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BaseDeviceCameraServiceImpl extends BaseServiceImpl<BaseDeviceCameraDao, BaseDeviceCameraDO> implements BaseDeviceCameraService {

    @Resource
    private BaseDeviceCameraDao baseDeviceCameraDao;

    @Override
    public String createBaseDeviceCamera(BaseDeviceCameraSaveReqVO createReqVO) {
        // 插入
        BaseDeviceCameraDO baseDeviceCamera = BeanUtils.toBean(createReqVO, BaseDeviceCameraDO.class);
        baseDeviceCameraDao.insert(baseDeviceCamera);
        // 返回
        return baseDeviceCamera.getId();
    }

    @Override
    public void updateBaseDeviceCamera(BaseDeviceCameraSaveReqVO updateReqVO) {
        // 校验存在
        validateBaseDeviceCameraExists(updateReqVO.getId());
        // 更新
        BaseDeviceCameraDO updateObj = BeanUtils.toBean(updateReqVO, BaseDeviceCameraDO.class);
        baseDeviceCameraDao.updateById(updateObj);
    }

    @Override
    public void deleteBaseDeviceCamera(String id) {
        // 校验存在
        validateBaseDeviceCameraExists(id);
        // 删除
        baseDeviceCameraDao.deleteById(id);
    }

    private void validateBaseDeviceCameraExists(String id) {
        if (baseDeviceCameraDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-摄像机设备数据不存在");
        }
    }

    @Override
    public BaseDeviceCameraDO getBaseDeviceCamera(String id) {
        return baseDeviceCameraDao.selectById(id);
    }

    @Override
    public PageResult<BaseDeviceCameraDO> getBaseDeviceCameraPage(BaseDeviceCameraPageReqVO pageReqVO) {
        return baseDeviceCameraDao.selectPage(pageReqVO);
    }

    @Override
    public List<BaseDeviceCameraDO> getBaseDeviceCameraList(BaseDeviceCameraListReqVO listReqVO) {
        return baseDeviceCameraDao.selectList(listReqVO);
    }

    @Override
    public List<BaseDeviceCameraPageVO> findByPage(Page page, BaseDeviceCameraPageDto pageDto) {
        return baseDeviceCameraDao.findByPage(page,pageDto);
    }

    @Override
    public BaseDeviceCameraPageVO findOneById(String id) {
        return baseDeviceCameraDao.findOneById(id);
    }
    @Override
    public Integer updateChanDeviceStatus(String chanId, Integer type){
        BaseDeviceCameraDO baseDeviceCameraDO = new BaseDeviceCameraDO();
        baseDeviceCameraDO.setType(type);
        baseDeviceCameraDO.setIsVideo(1);
        UpdateWrapper updateWrapper = new UpdateWrapper();
        updateWrapper.eq("channel_id", chanId);
        return baseDeviceCameraDao.update(baseDeviceCameraDO,updateWrapper);
    }
    @Override
    public Integer clearDeviceCameraTable(String channelId, String orgCode){
        LambdaQueryWrapper<BaseDeviceCameraDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.inSql(BaseDeviceCameraDO::getChannelId,
                "SELECT id FROM acp_pm_device a WHERE a.channel_id = '" + channelId + "' AND a.org_code = '" + orgCode + "'");

        return baseDeviceCameraDao.delete(wrapper);
    }

    @Override
    public List<RoomVideoVO> roomVideo(String roomId) {
        return baseDeviceCameraDao.roomVideo(roomId);
    }

}
