package com.rs.module.acp.controller.admin.pm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-监管管理-业务数据纠错申请 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BizDataCorrectionApplyRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("业务类型")
    private String businessType;
    @ApiModelProperty("纠错原因")
    private String correctionReason;
    @ApiModelProperty("operator_time")
    private Date operatorTime;
    @ApiModelProperty("纠错类型 1 纠错，2 作废")
    private String correctionType;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("操作人身份证号")
    private String operatorSfzh;
    @ApiModelProperty("操作人")
    private String operator;
    @ApiModelProperty("纠错状态（01：编辑中、02：审批中、03：审批通过、04：未通过、05：退查、09：作废）")
    private String status;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;
}
