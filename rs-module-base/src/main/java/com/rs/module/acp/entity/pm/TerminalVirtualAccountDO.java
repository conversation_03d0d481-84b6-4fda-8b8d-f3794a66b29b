package com.rs.module.acp.entity.pm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 实战平台-监管管理-虚拟终端账号 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_terminal_virtual_account")
@KeySequence("acp_pm_terminal_virtual_account_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_terminal_virtual_account")
public class TerminalVirtualAccountDO extends BaseDO {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.INPUT)
    private String id;
    /**
     * 虚拟终端名称
     */
    private String name;
    /**
     * 排序字段，从小到大
     */
    private Integer sortOrder;


}
