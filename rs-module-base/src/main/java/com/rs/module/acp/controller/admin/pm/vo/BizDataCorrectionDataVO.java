package com.rs.module.acp.controller.admin.pm.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-监管管理-业务数据纠错 Response VO")
@Data
public class BizDataCorrectionDataVO {
private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("纠错id")
    private String correctionId;

    @ApiModelProperty("操作类型")
    private String operationType;

    @ApiModelProperty("业务类型")
    private String businessType;

    @ApiModelProperty("业务类型名称")
    private String businessTypeName;

    @ApiModelProperty("数据类型")
    private String dataType;

    @ApiModelProperty("数据类型名称")
    private String dateTypeName;

    @ApiModelProperty("数据字段")
    private String field;

    @ApiModelProperty("数据字段名称")
    private String fieldName;

    @ApiModelProperty("修改值")
    private String newValue;

    @ApiModelProperty("原值")
    private String oldValue;

    @ApiModelProperty("修改值翻译")
    private String newValueName;

    @ApiModelProperty("原值翻译")
    private String oldValueName;

    @ApiModelProperty("理由")
    private String reason;

    @ApiModelProperty("备注")
    private String remark;

}
