package com.rs.module.acp.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.pm.BaseDeviceVideoMoveDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-监管管理-视频异动设备信息 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BaseDeviceVideoMoveDao extends IBaseDao<BaseDeviceVideoMoveDO> {


    default PageResult<BaseDeviceVideoMoveDO> selectPage(BaseDeviceVideoMovePageReqVO reqVO) {
        Page<BaseDeviceVideoMoveDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<BaseDeviceVideoMoveDO> wrapper = new LambdaQueryWrapperX<BaseDeviceVideoMoveDO>()
            .eqIfPresent(BaseDeviceVideoMoveDO::getDeviceId, reqVO.getDeviceId())
            .eqIfPresent(BaseDeviceVideoMoveDO::getFactoryDeviceId, reqVO.getFactoryDeviceId())
            .eqIfPresent(BaseDeviceVideoMoveDO::getDeviceIp, reqVO.getDeviceIp())
            .eqIfPresent(BaseDeviceVideoMoveDO::getChannelId, reqVO.getChannelId())
            .likeIfPresent(BaseDeviceVideoMoveDO::getChannelName, reqVO.getChannelName())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(BaseDeviceVideoMoveDO::getAddTime);
        }
        Page<BaseDeviceVideoMoveDO> baseDeviceVideoMovePage = selectPage(page, wrapper);
        return new PageResult<>(baseDeviceVideoMovePage.getRecords(), baseDeviceVideoMovePage.getTotal());
    }
    default List<BaseDeviceVideoMoveDO> selectList(BaseDeviceVideoMoveListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BaseDeviceVideoMoveDO>()
            .eqIfPresent(BaseDeviceVideoMoveDO::getDeviceId, reqVO.getDeviceId())
            .eqIfPresent(BaseDeviceVideoMoveDO::getFactoryDeviceId, reqVO.getFactoryDeviceId())
            .eqIfPresent(BaseDeviceVideoMoveDO::getDeviceIp, reqVO.getDeviceIp())
            .eqIfPresent(BaseDeviceVideoMoveDO::getChannelId, reqVO.getChannelId())
            .likeIfPresent(BaseDeviceVideoMoveDO::getChannelName, reqVO.getChannelName())
        .orderByDesc(BaseDeviceVideoMoveDO::getAddTime));    }


    }
