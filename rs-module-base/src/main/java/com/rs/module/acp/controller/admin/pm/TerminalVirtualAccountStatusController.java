package com.rs.module.acp.controller.admin.pm;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.TerminalVirtualAccountStatusDO;
import com.rs.module.acp.service.pm.TerminalVirtualAccountStatusService;

@Api(tags = "实战平台-监管管理-虚拟终端账号-使用状态")
@RestController
@RequestMapping("/acp/pm/terminalVirtualAccountStatus")
@Validated
public class TerminalVirtualAccountStatusController {

    @Resource
    private TerminalVirtualAccountStatusService terminalVirtualAccountStatusService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-监管管理-虚拟终端账号-使用状态")
    public CommonResult<String> createTerminalVirtualAccountStatus(@Valid @RequestBody TerminalVirtualAccountStatusSaveReqVO createReqVO) {
        return success(terminalVirtualAccountStatusService.createTerminalVirtualAccountStatus(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-监管管理-虚拟终端账号-使用状态")
    public CommonResult<Boolean> updateTerminalVirtualAccountStatus(@Valid @RequestBody TerminalVirtualAccountStatusSaveReqVO updateReqVO) {
        terminalVirtualAccountStatusService.updateTerminalVirtualAccountStatus(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-监管管理-虚拟终端账号-使用状态")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteTerminalVirtualAccountStatus(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           terminalVirtualAccountStatusService.deleteTerminalVirtualAccountStatus(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-监管管理-虚拟终端账号-使用状态")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<TerminalVirtualAccountStatusRespVO> getTerminalVirtualAccountStatus(@RequestParam("id") String id) {
        TerminalVirtualAccountStatusDO terminalVirtualAccountStatus = terminalVirtualAccountStatusService.getTerminalVirtualAccountStatus(id);
        return success(BeanUtils.toBean(terminalVirtualAccountStatus, TerminalVirtualAccountStatusRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-监管管理-虚拟终端账号-使用状态分页")
    public CommonResult<PageResult<TerminalVirtualAccountStatusRespVO>> getTerminalVirtualAccountStatusPage(@Valid @RequestBody TerminalVirtualAccountStatusPageReqVO pageReqVO) {
        PageResult<TerminalVirtualAccountStatusDO> pageResult = terminalVirtualAccountStatusService.getTerminalVirtualAccountStatusPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TerminalVirtualAccountStatusRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-监管管理-虚拟终端账号-使用状态列表")
    public CommonResult<List<TerminalVirtualAccountStatusRespVO>> getTerminalVirtualAccountStatusList(@Valid @RequestBody TerminalVirtualAccountStatusListReqVO listReqVO) {
        List<TerminalVirtualAccountStatusDO> list = terminalVirtualAccountStatusService.getTerminalVirtualAccountStatusList(listReqVO);
        return success(BeanUtils.toBean(list, TerminalVirtualAccountStatusRespVO.class));
    }
}
