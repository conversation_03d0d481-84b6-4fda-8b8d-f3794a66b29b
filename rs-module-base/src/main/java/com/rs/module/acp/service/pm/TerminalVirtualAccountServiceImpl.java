package com.rs.module.acp.service.pm;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.util.BspDbUtil;
import com.rs.module.acp.controller.admin.pm.vo.TerminalVirtualAccountListReqVO;
import com.rs.module.acp.controller.admin.pm.vo.TerminalVirtualAccountPageReqVO;
import com.rs.module.acp.controller.admin.pm.vo.TerminalVirtualAccountRespVO;
import com.rs.module.acp.controller.admin.pm.vo.TerminalVirtualAccountSaveReqVO;
import com.rs.module.acp.dao.pm.TerminalVirtualAccountDao;
import com.rs.module.acp.dao.pm.TerminalVirtualAccountStatusDao;
import com.rs.module.acp.entity.pm.BaseDeviceInscreenDO;
import com.rs.module.acp.entity.pm.TerminalVirtualAccountDO;
import com.rs.module.acp.entity.pm.TerminalVirtualAccountStatusDO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.pm.PrisonerService;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 实战平台-监管管理-虚拟终端账号 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Log4j2
public class TerminalVirtualAccountServiceImpl extends BaseServiceImpl<TerminalVirtualAccountDao, TerminalVirtualAccountDO> implements TerminalVirtualAccountService {

    @Resource
    private TerminalVirtualAccountDao terminalVirtualAccountDao;

    @Resource
    private TerminalVirtualAccountStatusDao terminalVirtualAccountStatusDao;

    @Resource
    private PrisonerService prisonerService;

    @Resource
    private BaseDeviceInscreenService baseDeviceInscreenService;


    @Override
    public String createTerminalVirtualAccount(TerminalVirtualAccountSaveReqVO createReqVO) {
        // 插入
        TerminalVirtualAccountDO terminalVirtualAccount = BeanUtils.toBean(createReqVO, TerminalVirtualAccountDO.class);
        terminalVirtualAccountDao.insert(terminalVirtualAccount);
        // 返回
        return terminalVirtualAccount.getId();
    }

    @Override
    public void updateTerminalVirtualAccount(TerminalVirtualAccountSaveReqVO updateReqVO) {
        // 校验存在
        validateTerminalVirtualAccountExists(updateReqVO.getId());
        // 更新
        TerminalVirtualAccountDO updateObj = BeanUtils.toBean(updateReqVO, TerminalVirtualAccountDO.class);
        terminalVirtualAccountDao.updateById(updateObj);
    }

    @Override
    public void deleteTerminalVirtualAccount(String id) {
        // 校验存在
        validateTerminalVirtualAccountExists(id);
        // 删除
        terminalVirtualAccountDao.deleteById(id);
    }

    private void validateTerminalVirtualAccountExists(String id) {
        if (terminalVirtualAccountDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-虚拟终端账号数据不存在");
        }
    }

    @Override
    public TerminalVirtualAccountDO getTerminalVirtualAccount(String id) {
        return terminalVirtualAccountDao.selectById(id);
    }

    @Override
    public PageResult<TerminalVirtualAccountDO> getTerminalVirtualAccountPage(TerminalVirtualAccountPageReqVO pageReqVO) {
        return terminalVirtualAccountDao.selectPage(pageReqVO);
    }

    @Override
    public List<TerminalVirtualAccountDO> getTerminalVirtualAccountList(TerminalVirtualAccountListReqVO listReqVO) {
        return terminalVirtualAccountDao.selectList(listReqVO);
    }

    @PostConstruct
    @Override
    public void init() {
        new Thread(() -> {
            List<TerminalVirtualAccountDO> terminalVirtualAccountDOList = terminalVirtualAccountDao.selectList();
            if (terminalVirtualAccountDOList.isEmpty()) {
                // 初始化数据
                String kyUrl = BspDbUtil.getParam("KY_URL");
                String kyInitRang = BspDbUtil.getParam("KY_INIT_RANG");
                //300-2000 分割这个取开始和结束值
                String[] kyInitRangArr = kyInitRang.split("-");
                for (int i = Integer.parseInt(kyInitRangArr[0]); i <= Integer.parseInt(kyInitRangArr[1]); i++) {

                    Map<String, Object> map = new HashMap<>();
                    map.put("jsondata[tid]", i);
                    map.put("jsondata[tname]", i);
                    map.put("jsondata[multiparty]", 1);
                    map.put("jsondata[enablevideorecord]", 1);
                    String resp = HttpUtil.post(kyUrl, map, 5000);
                    log.info(resp);
                    JSONObject jsonObject = JSONObject.parseObject(resp);
                    if ("1".equals(jsonObject.getString("res"))) {
                        try {
                            TerminalVirtualAccountDO terminalVirtualAccountDO = new TerminalVirtualAccountDO();
                            terminalVirtualAccountDO.setName(i + "");
                            terminalVirtualAccountDO.setSortOrder(i);
                            terminalVirtualAccountDO.setId(i + "");
                            terminalVirtualAccountDao.insert(terminalVirtualAccountDO);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
            }
        }).start();
    }


    /**
     * 获取空闲虚拟终端账号
     *
     * @param businessId
     * @return
     */
    @Override
    public TerminalVirtualAccountRespVO getAvailableAccount(String businessId, String jgrybm) {
        TerminalVirtualAccountRespVO availableAccount = terminalVirtualAccountDao.getAvailableAccount();
        if (availableAccount == null) {
            throw new ServerException("没有可用的虚拟终端账号");
        }

        //获取在押人员
        PrisonerVwRespVO prisoner = prisonerService.getPrisonerWithMedicalInformationByJgrybm(jgrybm);
        if (prisoner == null) {
            throw new ServerException("没有找到在押人员信息");
        }
        //获取在押人员关联监室设备
        BaseDeviceInscreenDO baseDeviceInscreenDO = baseDeviceInscreenService.getCnpByRoomId(prisoner.getJsh());

        if (baseDeviceInscreenDO == null) {
            throw new ServerException("没有找到在押人员关联监室设备");
        }

        TerminalVirtualAccountStatusDO terminalVirtualAccountStatusDO = new TerminalVirtualAccountStatusDO();
        terminalVirtualAccountStatusDO.setVirtualId(availableAccount.getId());
        terminalVirtualAccountStatusDO.setBusinessId(businessId);
        terminalVirtualAccountStatusDO.setTargetId(baseDeviceInscreenDO.getDeviceNum() + "");
        terminalVirtualAccountStatusDao.insert(terminalVirtualAccountStatusDO);
        availableAccount.setKyIp(BspDbUtil.getParam("KY_IP"));
        availableAccount.setKyPort(BspDbUtil.getParam("KY_PORT"));
        availableAccount.setKyPassword(BspDbUtil.getParam("KY_PWD"));
        availableAccount.setVirtualAccount(availableAccount.getId());
        availableAccount.setTargetAccount(baseDeviceInscreenDO.getDeviceNum()+"");


        return availableAccount;
    }
}
