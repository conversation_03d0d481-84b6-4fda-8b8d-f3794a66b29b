package com.rs.module.acp.controller.admin.pm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

import com.rs.module.acp.entity.pm.BizDataCorrectionDO;

@ApiModel(description = "管理后台 - 实战平台-监管管理-业务数据纠错申请新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BizDataCorrectionApplySaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("业务类型")
    @NotEmpty(message = "业务类型不能为空")
    private String businessType;

    @ApiModelProperty("纠错原因")
    @NotEmpty(message = "纠错原因不能为空")
    private String correctionReason;

    @ApiModelProperty("operator_time")
    private Date operatorTime;

    @ApiModelProperty("纠错类型 1 纠错，2 作废")
    @NotEmpty(message = "纠错类型 1 纠错，2 作废不能为空")
    private String correctionType;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("操作人身份证号")
    private String operatorSfzh;

    @ApiModelProperty("操作人")
    private String operator;

    @ApiModelProperty("纠错状态（01：编辑中、02：审批中、03：审批通过、04：未通过、05：退查、09：作废）")
    @NotEmpty(message = "纠错状态（01：编辑中、02：审批中、03：审批通过、04：未通过、05：退查、09：作废）不能为空")
    private String status;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("实战平台-监管管理-业务数据纠错列表")
    private List<BizDataCorrectionDO> bizDataCorrections;

}
