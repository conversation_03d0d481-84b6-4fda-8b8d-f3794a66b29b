package com.rs.module.acp.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.pm.vo.BizDataCorrectionApplySaveReqVO;
import com.rs.module.acp.entity.pm.BizDataCorrectionApplyDO;
import com.rs.module.acp.entity.pm.BizDataCorrectionDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-监管管理-业务数据纠错 Service 接口
 *
 * <AUTHOR>
 */
public interface BizDataCorrectionService extends IBaseService<BizDataCorrectionDO>{

    /**
     * 创建实战平台-监管管理-业务数据纠错
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createBizDataCorrection(@Valid BizDataCorrectionDO createReqVO);

    /**
     * 根据申请记录id获取变更记录
     * @param applyId
     * @return
     */
    List<BizDataCorrectionDO> getByApplyId(String applyId);


}
