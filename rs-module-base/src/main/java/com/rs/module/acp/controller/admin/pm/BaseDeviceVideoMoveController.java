package com.rs.module.acp.controller.admin.pm;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.BaseDeviceVideoMoveDO;
import com.rs.module.acp.service.pm.BaseDeviceVideoMoveService;

@Api(tags = "实战平台-监管管理-视频异动设备信息")
@RestController
@RequestMapping("/acp/pm/baseDeviceVideoMove")
@Validated
public class BaseDeviceVideoMoveController {

    @Resource
    private BaseDeviceVideoMoveService baseDeviceVideoMoveService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-监管管理-视频异动设备信息")
    public CommonResult<String> createBaseDeviceVideoMove(@Valid @RequestBody BaseDeviceVideoMoveSaveReqVO createReqVO) {
        return success(baseDeviceVideoMoveService.createBaseDeviceVideoMove(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-监管管理-视频异动设备信息")
    public CommonResult<Boolean> updateBaseDeviceVideoMove(@Valid @RequestBody BaseDeviceVideoMoveSaveReqVO updateReqVO) {
        baseDeviceVideoMoveService.updateBaseDeviceVideoMove(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-监管管理-视频异动设备信息")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteBaseDeviceVideoMove(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           baseDeviceVideoMoveService.deleteBaseDeviceVideoMove(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-监管管理-视频异动设备信息")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<BaseDeviceVideoMoveRespVO> getBaseDeviceVideoMove(@RequestParam("id") String id) {
        BaseDeviceVideoMoveDO baseDeviceVideoMove = baseDeviceVideoMoveService.getBaseDeviceVideoMove(id);
        return success(BeanUtils.toBean(baseDeviceVideoMove, BaseDeviceVideoMoveRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-监管管理-视频异动设备信息分页")
    public CommonResult<PageResult<BaseDeviceVideoMoveRespVO>> getBaseDeviceVideoMovePage(@Valid @RequestBody BaseDeviceVideoMovePageReqVO pageReqVO) {
        PageResult<BaseDeviceVideoMoveDO> pageResult = baseDeviceVideoMoveService.getBaseDeviceVideoMovePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BaseDeviceVideoMoveRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-监管管理-视频异动设备信息列表")
    public CommonResult<List<BaseDeviceVideoMoveRespVO>> getBaseDeviceVideoMoveList(@Valid @RequestBody BaseDeviceVideoMoveListReqVO listReqVO) {
        List<BaseDeviceVideoMoveDO> list = baseDeviceVideoMoveService.getBaseDeviceVideoMoveList(listReqVO);
        return success(BeanUtils.toBean(list, BaseDeviceVideoMoveRespVO.class));
    }
}
