package com.rs.module.acp.controller.admin.pm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-监管管理-虚拟终端账号新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TerminalVirtualAccountSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("虚拟终端名称")
    private String name;

    @ApiModelProperty("排序字段，从小到大")
    private Integer sortOrder;

}
