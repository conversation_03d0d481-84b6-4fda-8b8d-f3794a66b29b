package com.rs.module.acp.dao.pm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.pm.vo.TerminalVirtualAccountListReqVO;
import com.rs.module.acp.controller.admin.pm.vo.TerminalVirtualAccountPageReqVO;
import com.rs.module.acp.controller.admin.pm.vo.TerminalVirtualAccountRespVO;
import com.rs.module.acp.entity.pm.TerminalVirtualAccountDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 实战平台-监管管理-虚拟终端账号 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface TerminalVirtualAccountDao extends IBaseDao<TerminalVirtualAccountDO> {


    default PageResult<TerminalVirtualAccountDO> selectPage(TerminalVirtualAccountPageReqVO reqVO) {
        Page<TerminalVirtualAccountDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<TerminalVirtualAccountDO> wrapper = new LambdaQueryWrapperX<TerminalVirtualAccountDO>()
                .likeIfPresent(TerminalVirtualAccountDO::getName, reqVO.getName())
                .eqIfPresent(TerminalVirtualAccountDO::getSortOrder, reqVO.getSortOrder());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(TerminalVirtualAccountDO::getAddTime);
        }
        Page<TerminalVirtualAccountDO> terminalVirtualAccountPage = selectPage(page, wrapper);
        return new PageResult<>(terminalVirtualAccountPage.getRecords(), terminalVirtualAccountPage.getTotal());
    }

    default List<TerminalVirtualAccountDO> selectList(TerminalVirtualAccountListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TerminalVirtualAccountDO>()
                .likeIfPresent(TerminalVirtualAccountDO::getName, reqVO.getName())
                .eqIfPresent(TerminalVirtualAccountDO::getSortOrder, reqVO.getSortOrder())
                .orderByDesc(TerminalVirtualAccountDO::getAddTime));
    }


    /**
     * 获取空闲虚拟终端账号
     */
    public TerminalVirtualAccountRespVO getAvailableAccount();

    public String findJgrybmByTalkTaskId(@Param("talkTaskId") String talkTaskId);

}
