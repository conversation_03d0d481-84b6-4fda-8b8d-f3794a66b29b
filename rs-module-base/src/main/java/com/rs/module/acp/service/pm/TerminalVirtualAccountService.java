package com.rs.module.acp.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.pm.vo.TerminalVirtualAccountListReqVO;
import com.rs.module.acp.controller.admin.pm.vo.TerminalVirtualAccountPageReqVO;
import com.rs.module.acp.controller.admin.pm.vo.TerminalVirtualAccountRespVO;
import com.rs.module.acp.controller.admin.pm.vo.TerminalVirtualAccountSaveReqVO;
import com.rs.module.acp.entity.pm.TerminalVirtualAccountDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-监管管理-虚拟终端账号 Service 接口
 *
 * <AUTHOR>
 */
public interface TerminalVirtualAccountService extends IBaseService<TerminalVirtualAccountDO> {

    /**
     * 创建实战平台-监管管理-虚拟终端账号
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createTerminalVirtualAccount(@Valid TerminalVirtualAccountSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-虚拟终端账号
     *
     * @param updateReqVO 更新信息
     */
    void updateTerminalVirtualAccount(@Valid TerminalVirtualAccountSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-虚拟终端账号
     *
     * @param id 编号
     */
    void deleteTerminalVirtualAccount(String id);

    /**
     * 获得实战平台-监管管理-虚拟终端账号
     *
     * @param id 编号
     * @return 实战平台-监管管理-虚拟终端账号
     */
    TerminalVirtualAccountDO getTerminalVirtualAccount(String id);

    /**
     * 获得实战平台-监管管理-虚拟终端账号分页
     *
     * @param pageReqVO 分页查询
     * @return 实战平台-监管管理-虚拟终端账号分页
     */
    PageResult<TerminalVirtualAccountDO> getTerminalVirtualAccountPage(TerminalVirtualAccountPageReqVO pageReqVO);

    /**
     * 获得实战平台-监管管理-虚拟终端账号列表
     *
     * @param listReqVO 查询条件
     * @return 实战平台-监管管理-虚拟终端账号列表
     */
    List<TerminalVirtualAccountDO> getTerminalVirtualAccountList(TerminalVirtualAccountListReqVO listReqVO);

    /**
     * 初始化快鱼的虚拟账号
     */
    public void init();


    /**
     * 获取空闲虚拟终端账号
     *
     * @param businessId
     * @return
     */
    public TerminalVirtualAccountRespVO getAvailableAccount(String businessId,String jgrybm);
}
