package com.rs.module.acp.controller.admin.pm.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-监管管理-视频异动设备信息列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseDeviceVideoMoveListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("设备编号")
    private String deviceId;

    @ApiModelProperty("厂家设备编号")
    private String factoryDeviceId;

    @ApiModelProperty("设备ip")
    private String deviceIp;

    @ApiModelProperty("通道编号")
    private String channelId;

    @ApiModelProperty("通道名称")
    private String channelName;

}
