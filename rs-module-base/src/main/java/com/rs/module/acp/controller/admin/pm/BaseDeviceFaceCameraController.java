package com.rs.module.acp.controller.admin.pm;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.BaseDeviceFaceCameraDO;
import com.rs.module.acp.service.pm.BaseDeviceFaceCameraService;

@Api(tags = "实战平台-监管管理-人脸摄像头设备")
@RestController
@RequestMapping("/acp/pm/baseDeviceFaceCamera")
@Validated
public class BaseDeviceFaceCameraController {

    @Resource
    private BaseDeviceFaceCameraService baseDeviceFaceCameraService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-监管管理-人脸摄像头设备")
    public CommonResult<String> createBaseDeviceFaceCamera(@Valid @RequestBody BaseDeviceFaceCameraSaveReqVO createReqVO) {
        return success(baseDeviceFaceCameraService.createBaseDeviceFaceCamera(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-监管管理-人脸摄像头设备")
    public CommonResult<Boolean> updateBaseDeviceFaceCamera(@Valid @RequestBody BaseDeviceFaceCameraSaveReqVO updateReqVO) {
        baseDeviceFaceCameraService.updateBaseDeviceFaceCamera(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-监管管理-人脸摄像头设备")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteBaseDeviceFaceCamera(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           baseDeviceFaceCameraService.deleteBaseDeviceFaceCamera(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-监管管理-人脸摄像头设备")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<BaseDeviceFaceCameraRespVO> getBaseDeviceFaceCamera(@RequestParam("id") String id) {
        BaseDeviceFaceCameraDO baseDeviceFaceCamera = baseDeviceFaceCameraService.getBaseDeviceFaceCamera(id);
        return success(BeanUtils.toBean(baseDeviceFaceCamera, BaseDeviceFaceCameraRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-监管管理-人脸摄像头设备分页")
    public CommonResult<PageResult<BaseDeviceFaceCameraRespVO>> getBaseDeviceFaceCameraPage(@Valid @RequestBody BaseDeviceFaceCameraPageReqVO pageReqVO) {
        PageResult<BaseDeviceFaceCameraDO> pageResult = baseDeviceFaceCameraService.getBaseDeviceFaceCameraPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BaseDeviceFaceCameraRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-监管管理-人脸摄像头设备列表")
    public CommonResult<List<BaseDeviceFaceCameraRespVO>> getBaseDeviceFaceCameraList(@Valid @RequestBody BaseDeviceFaceCameraListReqVO listReqVO) {
        List<BaseDeviceFaceCameraDO> list = baseDeviceFaceCameraService.getBaseDeviceFaceCameraList(listReqVO);
        return success(BeanUtils.toBean(list, BaseDeviceFaceCameraRespVO.class));
    }
}
