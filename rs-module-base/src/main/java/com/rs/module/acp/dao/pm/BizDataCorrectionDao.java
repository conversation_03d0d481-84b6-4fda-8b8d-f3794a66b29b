package com.rs.module.acp.dao.pm;

import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.PageParam;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.acp.entity.pm.BizDataCorrectionDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
 * 实战平台-监管管理-业务数据纠错
 *
 * <AUTHOR>
 */
@Mapper
public interface BizDataCorrectionDao extends IBaseDao<BizDataCorrectionDO> {

    default List<BizDataCorrectionDO> selectListByCorrectionApplyId(String correctionApplyId) {
        return selectList(new LambdaQueryWrapperX<BizDataCorrectionDO>().eq(BizDataCorrectionDO::getCorrectionApplyId, correctionApplyId));
    }

    default int deleteByCorrectionApplyId(String correctionApplyId) {
        return delete(new LambdaQueryWrapperX<BizDataCorrectionDO>().eq(BizDataCorrectionDO::getCorrectionApplyId, correctionApplyId));
    }

}
