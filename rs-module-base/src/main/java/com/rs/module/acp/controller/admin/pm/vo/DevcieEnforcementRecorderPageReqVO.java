package com.rs.module.acp.controller.admin.pm.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-监管管理-执法记录仪设备分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DevcieEnforcementRecorderPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("执法记录仪编号")
    private String deviceCode;

    @ApiModelProperty("名字")
    private String deviceName;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
