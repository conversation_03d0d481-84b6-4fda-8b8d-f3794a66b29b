package com.rs.module.acp.controller.admin.pm.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-监管管理-摄像机设备列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseDeviceCameraListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("设备编号")
    private String deviceId;

    @ApiModelProperty("通道编号")
    private String channelId;

    @ApiModelProperty("设备IP")
    private String deviceIp;

    @ApiModelProperty("摄像机类型（0枪机，1球机）")
    private Integer type;

    @ApiModelProperty("通道名称")
    private String channelName;

    @ApiModelProperty("是否视频联网同步过来 1-是")
    private Integer isVideo;

}
