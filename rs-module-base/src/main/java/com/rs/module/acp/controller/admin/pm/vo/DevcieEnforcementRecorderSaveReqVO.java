package com.rs.module.acp.controller.admin.pm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-监管管理-执法记录仪设备新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DevcieEnforcementRecorderSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("执法记录仪编号")
    private String deviceCode;

    @ApiModelProperty("名字")
    private String deviceName;

}
