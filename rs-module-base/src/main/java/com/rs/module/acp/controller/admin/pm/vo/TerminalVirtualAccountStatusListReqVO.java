package com.rs.module.acp.controller.admin.pm.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-监管管理-虚拟终端账号-使用状态列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TerminalVirtualAccountStatusListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("虚拟终端名称")
    private String name;

    @ApiModelProperty("排序字段，从小到大")
    private Integer sortOrder;

}
