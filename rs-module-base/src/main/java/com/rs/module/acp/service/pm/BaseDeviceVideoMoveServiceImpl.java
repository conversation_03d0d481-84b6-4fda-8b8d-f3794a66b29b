package com.rs.module.acp.service.pm;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.BaseDeviceVideoMoveDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pm.BaseDeviceVideoMoveDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-监管管理-视频异动设备信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BaseDeviceVideoMoveServiceImpl extends BaseServiceImpl<BaseDeviceVideoMoveDao, BaseDeviceVideoMoveDO> implements BaseDeviceVideoMoveService {

    @Resource
    private BaseDeviceVideoMoveDao baseDeviceVideoMoveDao;

    @Override
    public String createBaseDeviceVideoMove(BaseDeviceVideoMoveSaveReqVO createReqVO) {
        // 插入
        BaseDeviceVideoMoveDO baseDeviceVideoMove = BeanUtils.toBean(createReqVO, BaseDeviceVideoMoveDO.class);
        baseDeviceVideoMoveDao.insert(baseDeviceVideoMove);
        // 返回
        return baseDeviceVideoMove.getId();
    }

    @Override
    public void updateBaseDeviceVideoMove(BaseDeviceVideoMoveSaveReqVO updateReqVO) {
        // 校验存在
        validateBaseDeviceVideoMoveExists(updateReqVO.getId());
        // 更新
        BaseDeviceVideoMoveDO updateObj = BeanUtils.toBean(updateReqVO, BaseDeviceVideoMoveDO.class);
        baseDeviceVideoMoveDao.updateById(updateObj);
    }

    @Override
    public void deleteBaseDeviceVideoMove(String id) {
        // 校验存在
        validateBaseDeviceVideoMoveExists(id);
        // 删除
        baseDeviceVideoMoveDao.deleteById(id);
    }

    private void validateBaseDeviceVideoMoveExists(String id) {
        if (baseDeviceVideoMoveDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-视频异动设备信息数据不存在");
        }
    }

    @Override
    public BaseDeviceVideoMoveDO getBaseDeviceVideoMove(String id) {
        return baseDeviceVideoMoveDao.selectById(id);
    }

    @Override
    public PageResult<BaseDeviceVideoMoveDO> getBaseDeviceVideoMovePage(BaseDeviceVideoMovePageReqVO pageReqVO) {
        return baseDeviceVideoMoveDao.selectPage(pageReqVO);
    }

    @Override
    public List<BaseDeviceVideoMoveDO> getBaseDeviceVideoMoveList(BaseDeviceVideoMoveListReqVO listReqVO) {
        return baseDeviceVideoMoveDao.selectList(listReqVO);
    }


}
