package com.rs.module.acp.controller.admin.pm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-监管管理-视频异动设备信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseDeviceVideoMoveRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("设备编号")
    private String deviceId;
    @ApiModelProperty("厂家设备编号")
    private String factoryDeviceId;
    @ApiModelProperty("设备ip")
    private String deviceIp;
    @ApiModelProperty("通道编号")
    private String channelId;
    @ApiModelProperty("通道名称")
    private String channelName;
}
