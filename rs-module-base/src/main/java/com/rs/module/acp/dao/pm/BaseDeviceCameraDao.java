package com.rs.module.acp.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.pm.BaseDeviceCameraDO;
import com.rs.module.base.vo.RoomVideoVO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 实战平台-监管管理-摄像机设备 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BaseDeviceCameraDao extends IBaseDao<BaseDeviceCameraDO> {


    default PageResult<BaseDeviceCameraDO> selectPage(BaseDeviceCameraPageReqVO reqVO) {
        Page<BaseDeviceCameraDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<BaseDeviceCameraDO> wrapper = new LambdaQueryWrapperX<BaseDeviceCameraDO>()
            .eqIfPresent(BaseDeviceCameraDO::getDeviceId, reqVO.getDeviceId())
            .eqIfPresent(BaseDeviceCameraDO::getChannelId, reqVO.getChannelId())
            .eqIfPresent(BaseDeviceCameraDO::getDeviceIp, reqVO.getDeviceIp())
            .eqIfPresent(BaseDeviceCameraDO::getType, reqVO.getType())
            .likeIfPresent(BaseDeviceCameraDO::getChannelName, reqVO.getChannelName())
            .eqIfPresent(BaseDeviceCameraDO::getIsVideo, reqVO.getIsVideo())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(BaseDeviceCameraDO::getAddTime);
        }
        Page<BaseDeviceCameraDO> baseDeviceCameraPage = selectPage(page, wrapper);
        return new PageResult<>(baseDeviceCameraPage.getRecords(), baseDeviceCameraPage.getTotal());
    }
    default List<BaseDeviceCameraDO> selectList(BaseDeviceCameraListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BaseDeviceCameraDO>()
            .eqIfPresent(BaseDeviceCameraDO::getDeviceId, reqVO.getDeviceId())
            .eqIfPresent(BaseDeviceCameraDO::getChannelId, reqVO.getChannelId())
            .eqIfPresent(BaseDeviceCameraDO::getDeviceIp, reqVO.getDeviceIp())
            .eqIfPresent(BaseDeviceCameraDO::getType, reqVO.getType())
            .likeIfPresent(BaseDeviceCameraDO::getChannelName, reqVO.getChannelName())
            .eqIfPresent(BaseDeviceCameraDO::getIsVideo, reqVO.getIsVideo())
        .orderByDesc(BaseDeviceCameraDO::getAddTime));}


    List<BaseDeviceCameraPageVO> findByPage(Page page, @Param("form") BaseDeviceCameraPageDto pageDto);

    BaseDeviceCameraPageVO findOneById(String id);

    List<RoomVideoVO> roomVideo(@Param("roomId") String roomId);

}
