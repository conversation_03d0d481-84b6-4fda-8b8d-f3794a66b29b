package com.rs.module.acp.service.pm;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.BaseDeviceFaceCameraDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pm.BaseDeviceFaceCameraDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-监管管理-人脸摄像头设备 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BaseDeviceFaceCameraServiceImpl extends BaseServiceImpl<BaseDeviceFaceCameraDao, BaseDeviceFaceCameraDO> implements BaseDeviceFaceCameraService {

    @Resource
    private BaseDeviceFaceCameraDao baseDeviceFaceCameraDao;

    @Override
    public String createBaseDeviceFaceCamera(BaseDeviceFaceCameraSaveReqVO createReqVO) {
        // 插入
        BaseDeviceFaceCameraDO baseDeviceFaceCamera = BeanUtils.toBean(createReqVO, BaseDeviceFaceCameraDO.class);
        baseDeviceFaceCameraDao.insert(baseDeviceFaceCamera);
        // 返回
        return baseDeviceFaceCamera.getId();
    }

    @Override
    public void updateBaseDeviceFaceCamera(BaseDeviceFaceCameraSaveReqVO updateReqVO) {
        // 校验存在
        validateBaseDeviceFaceCameraExists(updateReqVO.getId());
        // 更新
        BaseDeviceFaceCameraDO updateObj = BeanUtils.toBean(updateReqVO, BaseDeviceFaceCameraDO.class);
        baseDeviceFaceCameraDao.updateById(updateObj);
    }

    @Override
    public void deleteBaseDeviceFaceCamera(String id) {
        // 校验存在
        validateBaseDeviceFaceCameraExists(id);
        // 删除
        baseDeviceFaceCameraDao.deleteById(id);
    }

    private void validateBaseDeviceFaceCameraExists(String id) {
        if (baseDeviceFaceCameraDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-人脸摄像头设备数据不存在");
        }
    }

    @Override
    public BaseDeviceFaceCameraDO getBaseDeviceFaceCamera(String id) {
        return baseDeviceFaceCameraDao.selectById(id);
    }

    @Override
    public PageResult<BaseDeviceFaceCameraDO> getBaseDeviceFaceCameraPage(BaseDeviceFaceCameraPageReqVO pageReqVO) {
        return baseDeviceFaceCameraDao.selectPage(pageReqVO);
    }

    @Override
    public List<BaseDeviceFaceCameraDO> getBaseDeviceFaceCameraList(BaseDeviceFaceCameraListReqVO listReqVO) {
        return baseDeviceFaceCameraDao.selectList(listReqVO);
    }


}
