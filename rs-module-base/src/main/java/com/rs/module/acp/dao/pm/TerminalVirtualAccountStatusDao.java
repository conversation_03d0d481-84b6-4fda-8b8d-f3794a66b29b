package com.rs.module.acp.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.pm.TerminalVirtualAccountStatusDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-监管管理-虚拟终端账号-使用状态 Dao
*
* <AUTHOR>
*/
@Mapper
public interface TerminalVirtualAccountStatusDao extends IBaseDao<TerminalVirtualAccountStatusDO> {


    default PageResult<TerminalVirtualAccountStatusDO> selectPage(TerminalVirtualAccountStatusPageReqVO reqVO) {
        Page<TerminalVirtualAccountStatusDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<TerminalVirtualAccountStatusDO> wrapper = new LambdaQueryWrapperX<TerminalVirtualAccountStatusDO>()
            .likeIfPresent(TerminalVirtualAccountStatusDO::getName, reqVO.getName())
            .eqIfPresent(TerminalVirtualAccountStatusDO::getSortOrder, reqVO.getSortOrder())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(TerminalVirtualAccountStatusDO::getAddTime);
        }
        Page<TerminalVirtualAccountStatusDO> terminalVirtualAccountStatusPage = selectPage(page, wrapper);
        return new PageResult<>(terminalVirtualAccountStatusPage.getRecords(), terminalVirtualAccountStatusPage.getTotal());
    }
    default List<TerminalVirtualAccountStatusDO> selectList(TerminalVirtualAccountStatusListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TerminalVirtualAccountStatusDO>()
            .likeIfPresent(TerminalVirtualAccountStatusDO::getName, reqVO.getName())
            .eqIfPresent(TerminalVirtualAccountStatusDO::getSortOrder, reqVO.getSortOrder())
        .orderByDesc(TerminalVirtualAccountStatusDO::getAddTime));    }


    }
