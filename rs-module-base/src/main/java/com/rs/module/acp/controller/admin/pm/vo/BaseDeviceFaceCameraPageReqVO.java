package com.rs.module.acp.controller.admin.pm.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-监管管理-人脸摄像头设备分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BaseDeviceFaceCameraPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("设备编号")
    private String deviceId;

    @ApiModelProperty("通道编号")
    private String channelId;

    @ApiModelProperty("设备IP")
    private String deviceIp;

    @ApiModelProperty("合法人员类型")
    private String personType;

    @ApiModelProperty("通道名称")
    private String channelName;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
