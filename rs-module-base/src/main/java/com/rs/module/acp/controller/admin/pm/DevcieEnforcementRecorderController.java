package com.rs.module.acp.controller.admin.pm;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.DevcieEnforcementRecorderDO;
import com.rs.module.acp.service.pm.DevcieEnforcementRecorderService;

@Api(tags = "实战平台-监管管理-执法记录仪设备")
@RestController
@RequestMapping("/acp/pm/devcieEnforcementRecorder")
@Validated
public class DevcieEnforcementRecorderController {

    @Resource
    private DevcieEnforcementRecorderService devcieEnforcementRecorderService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-监管管理-执法记录仪设备")
    public CommonResult<String> createDevcieEnforcementRecorder(@Valid @RequestBody DevcieEnforcementRecorderSaveReqVO createReqVO) {
        return success(devcieEnforcementRecorderService.createDevcieEnforcementRecorder(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-监管管理-执法记录仪设备")
    public CommonResult<Boolean> updateDevcieEnforcementRecorder(@Valid @RequestBody DevcieEnforcementRecorderSaveReqVO updateReqVO) {
        devcieEnforcementRecorderService.updateDevcieEnforcementRecorder(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-监管管理-执法记录仪设备")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteDevcieEnforcementRecorder(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           devcieEnforcementRecorderService.deleteDevcieEnforcementRecorder(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-监管管理-执法记录仪设备")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<DevcieEnforcementRecorderRespVO> getDevcieEnforcementRecorder(@RequestParam("id") String id) {
        DevcieEnforcementRecorderDO devcieEnforcementRecorder = devcieEnforcementRecorderService.getDevcieEnforcementRecorder(id);
        return success(BeanUtils.toBean(devcieEnforcementRecorder, DevcieEnforcementRecorderRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-监管管理-执法记录仪设备分页")
    public CommonResult<PageResult<DevcieEnforcementRecorderRespVO>> getDevcieEnforcementRecorderPage(@Valid @RequestBody DevcieEnforcementRecorderPageReqVO pageReqVO) {
        PageResult<DevcieEnforcementRecorderDO> pageResult = devcieEnforcementRecorderService.getDevcieEnforcementRecorderPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DevcieEnforcementRecorderRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-监管管理-执法记录仪设备列表")
    public CommonResult<List<DevcieEnforcementRecorderRespVO>> getDevcieEnforcementRecorderList(@Valid @RequestBody DevcieEnforcementRecorderListReqVO listReqVO) {
        List<DevcieEnforcementRecorderDO> list = devcieEnforcementRecorderService.getDevcieEnforcementRecorderList(listReqVO);
        return success(BeanUtils.toBean(list, DevcieEnforcementRecorderRespVO.class));
    }
}
