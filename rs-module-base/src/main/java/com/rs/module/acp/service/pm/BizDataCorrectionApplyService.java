package com.rs.module.acp.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.pm.vo.BizDataCorrectionApplySaveReqVO;
import com.rs.module.acp.entity.pm.BizDataCorrectionApplyDO;
import com.rs.module.acp.entity.pm.BizDataCorrectionDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-监管管理-业务数据纠错申请 Service 接口
 *
 * <AUTHOR>
 */
public interface BizDataCorrectionApplyService extends IBaseService<BizDataCorrectionApplyDO>{

    /**
     * 创建实战平台-监管管理-业务数据纠错申请
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createBizDataCorrectionApply(@Valid BizDataCorrectionApplySaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-业务数据纠错申请
     *
     * @param updateReqVO 更新信息
     */
    void updateBizDataCorrectionApply(@Valid BizDataCorrectionApplySaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-业务数据纠错申请
     *
     * @param id 编号
     */
    void deleteBizDataCorrectionApply(String id);

    /**
     * 获得实战平台-监管管理-业务数据纠错申请
     *
     * @param id 编号
     * @return 实战平台-监管管理-业务数据纠错申请
     */
    BizDataCorrectionApplyDO getBizDataCorrectionApply(String id);


    // ==================== 子表（实战平台-监管管理-业务数据纠错） ====================

    /**
     * 获得实战平台-监管管理-业务数据纠错列表
     *
     * @param correctionApplyId 业务数据纠错申请ID
     * @return 实战平台-监管管理-业务数据纠错列表
     */
    List<BizDataCorrectionDO> getBizDataCorrectionListByCorrectionApplyId(String correctionApplyId);

}
