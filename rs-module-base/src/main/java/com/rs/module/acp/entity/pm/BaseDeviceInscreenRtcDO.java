package com.rs.module.acp.entity.pm;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-监管管理-GoCloud-RTC平台设备信息 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_device_rtc")
@KeySequence("acp_pm_device_rtc_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_device_rtc")
public class BaseDeviceInscreenRtcDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 1，设备，3用户
     */
    private Short deviceFlag;
    /**
     * device_id
     */
    private String deviceId;
    /**
     * serial_number
     */
    private String serialNumber;
    /**
     * 描述信息-设备名
     */
    private String deviceName;
    /**
     * 会议平台设备设备编号
     */
    private Long rtcDevCode;
    /**
     * 会议平台设备用户名
     */
    private String rtcDevUserName;
    /**
     * 会议平台设备密码
     */
    private String rtcDevPassword;
    /**
     * rtc_dev_id
     */
    private Long rtcDevId;

}
