package com.rs.module.acp.entity.pm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 实战平台-监管管理-业务数据纠错申请 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_biz_data_correction_apply")
@KeySequence("acp_pm_biz_data_correction_apply_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_biz_data_correction_apply")
public class BizDataCorrectionApplyDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 纠错原因
     */
    private String correctionReason;
    /**
     * operator_time
     */
    private Date operatorTime;
    /**
     * 纠错类型 1 纠错，2 作废
     */
    private String correctionType;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 操作人身份证号
     */
    private String operatorSfzh;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 纠错状态（01：编辑中、02：审批中、03：审批通过、04：未通过、05：退查、09：作废）
     */
    private String status;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

}
