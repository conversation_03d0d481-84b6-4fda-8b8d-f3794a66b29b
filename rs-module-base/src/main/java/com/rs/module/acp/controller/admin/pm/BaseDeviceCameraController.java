package com.rs.module.acp.controller.admin.pm;

import com.rs.module.base.vo.RoomVideoVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.BaseDeviceCameraDO;
import com.rs.module.acp.service.pm.BaseDeviceCameraService;

@Api(tags = "实战平台-监管管理-摄像机设备")
@RestController
@RequestMapping("/acp/pm/baseDeviceCamera")
@Validated
public class BaseDeviceCameraController {

    @Resource
    private BaseDeviceCameraService baseDeviceCameraService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-监管管理-摄像机设备")
    public CommonResult<String> createBaseDeviceCamera(@Valid @RequestBody BaseDeviceCameraSaveReqVO createReqVO) {
        return success(baseDeviceCameraService.createBaseDeviceCamera(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-监管管理-摄像机设备")
    public CommonResult<Boolean> updateBaseDeviceCamera(@Valid @RequestBody BaseDeviceCameraSaveReqVO updateReqVO) {
        baseDeviceCameraService.updateBaseDeviceCamera(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-监管管理-摄像机设备")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteBaseDeviceCamera(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           baseDeviceCameraService.deleteBaseDeviceCamera(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-监管管理-摄像机设备")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<BaseDeviceCameraRespVO> getBaseDeviceCamera(@RequestParam("id") String id) {
        BaseDeviceCameraDO baseDeviceCamera = baseDeviceCameraService.getBaseDeviceCamera(id);
        return success(BeanUtils.toBean(baseDeviceCamera, BaseDeviceCameraRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-监管管理-摄像机设备分页")
    public CommonResult<PageResult<BaseDeviceCameraRespVO>> getBaseDeviceCameraPage(@Valid @RequestBody BaseDeviceCameraPageReqVO pageReqVO) {
        PageResult<BaseDeviceCameraDO> pageResult = baseDeviceCameraService.getBaseDeviceCameraPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BaseDeviceCameraRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-监管管理-摄像机设备列表")
    public CommonResult<List<BaseDeviceCameraRespVO>> getBaseDeviceCameraList(@Valid @RequestBody BaseDeviceCameraListReqVO listReqVO) {
        List<BaseDeviceCameraDO> list = baseDeviceCameraService.getBaseDeviceCameraList(listReqVO);
        return success(BeanUtils.toBean(list, BaseDeviceCameraRespVO.class));
    }
    @GetMapping(value = "/roomVideo")
    @ApiOperation(value = "监室关联视频")
    @ApiImplicitParam(paramType = "path", name = "roomId", value = "监室id", required = true)
    public CommonResult<List<RoomVideoVO>> roomVideo(@RequestParam("roomId") String roomId) {
        return success(baseDeviceCameraService.roomVideo(roomId));
    }
}
