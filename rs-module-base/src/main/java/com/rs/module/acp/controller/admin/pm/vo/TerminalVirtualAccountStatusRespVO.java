package com.rs.module.acp.controller.admin.pm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-监管管理-虚拟终端账号-使用状态 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TerminalVirtualAccountStatusRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("虚拟终端名称")
    private String name;
    @ApiModelProperty("排序字段，从小到大")
    private Integer sortOrder;
}
