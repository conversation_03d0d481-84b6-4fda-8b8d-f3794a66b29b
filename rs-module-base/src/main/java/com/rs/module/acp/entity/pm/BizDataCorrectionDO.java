package com.rs.module.acp.entity.pm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 实战平台-监管管理-业务数据纠错 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 实战平台-监管管理-业务数据纠错新增/修改 Request VO")
@TableName("acp_pm_biz_data_correction")
@KeySequence("acp_pm_biz_data_correction_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_biz_data_correction_apply")
public class BizDataCorrectionDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;
    /**
     * 业务数据纠错申请ID
     */
    @ApiModelProperty("业务数据纠错申请ID")
    private String correctionApplyId;
    /**
     * 业务代码
     */
    @ApiModelProperty("业务代码")
    private String businessCode;
    /**
     * 业务名称
     */
    @ApiModelProperty("业务名称")
    private String businessName;
    /**
     * 业务ID
     */
    @ApiModelProperty("业务ID")
    private String businessId;
    /**
     * 数据内容
     */
    @ApiModelProperty("数据内容")
    private String content;
    /**
     * 纠错前的历史数据内容
     */
    @ApiModelProperty("纠错前的历史数据内容")
    private String historyContent;
    /**
     * 变更内容
     */
    @ApiModelProperty("变更内容")
    private String changedContent;

}
