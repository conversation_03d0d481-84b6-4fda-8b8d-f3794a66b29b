package com.rs.module.acp.controller.admin.pm.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-监管管理-虚拟终端账号分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TerminalVirtualAccountPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("虚拟终端名称")
    private String name;

    @ApiModelProperty("排序字段，从小到大")
    private Integer sortOrder;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
