package com.rs.module.acp.controller.admin.pm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-监管管理-人脸摄像头设备 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseDeviceFaceCameraRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("设备编号")
    private String deviceId;
    @ApiModelProperty("通道编号")
    private String channelId;
    @ApiModelProperty("设备IP")
    private String deviceIp;
    @ApiModelProperty("合法人员类型")
    private String personType;
    @ApiModelProperty("通道名称")
    private String channelName;
}
