package com.rs.module.acp.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.pm.DevcieEnforcementRecorderDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-监管管理-执法记录仪设备 Dao
*
* <AUTHOR>
*/
@Mapper
public interface DevcieEnforcementRecorderDao extends IBaseDao<DevcieEnforcementRecorderDO> {


    default PageResult<DevcieEnforcementRecorderDO> selectPage(DevcieEnforcementRecorderPageReqVO reqVO) {
        Page<DevcieEnforcementRecorderDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<DevcieEnforcementRecorderDO> wrapper = new LambdaQueryWrapperX<DevcieEnforcementRecorderDO>()
            .eqIfPresent(DevcieEnforcementRecorderDO::getDeviceCode, reqVO.getDeviceCode())
            .likeIfPresent(DevcieEnforcementRecorderDO::getDeviceName, reqVO.getDeviceName())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(DevcieEnforcementRecorderDO::getAddTime);
        }
        Page<DevcieEnforcementRecorderDO> devcieEnforcementRecorderPage = selectPage(page, wrapper);
        return new PageResult<>(devcieEnforcementRecorderPage.getRecords(), devcieEnforcementRecorderPage.getTotal());
    }
    default List<DevcieEnforcementRecorderDO> selectList(DevcieEnforcementRecorderListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DevcieEnforcementRecorderDO>()
            .eqIfPresent(DevcieEnforcementRecorderDO::getDeviceCode, reqVO.getDeviceCode())
            .likeIfPresent(DevcieEnforcementRecorderDO::getDeviceName, reqVO.getDeviceName())
        .orderByDesc(DevcieEnforcementRecorderDO::getAddTime));    }


    }
