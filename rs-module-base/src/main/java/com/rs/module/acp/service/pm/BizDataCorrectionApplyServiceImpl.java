package com.rs.module.acp.service.pm;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.pm.vo.BizDataCorrectionApplySaveReqVO;
import com.rs.module.acp.dao.pm.BizDataCorrectionApplyDao;
import com.rs.module.acp.dao.pm.BizDataCorrectionDao;
import com.rs.module.acp.entity.pm.BizDataCorrectionApplyDO;
import com.rs.module.acp.entity.pm.BizDataCorrectionDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 实战平台-监管管理-业务数据纠错申请 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BizDataCorrectionApplyServiceImpl extends BaseServiceImpl<BizDataCorrectionApplyDao, BizDataCorrectionApplyDO> implements BizDataCorrectionApplyService {

    @Resource
    private BizDataCorrectionApplyDao bizDataCorrectionApplyDao;
    @Resource
    private BizDataCorrectionDao bizDataCorrectionDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createBizDataCorrectionApply(BizDataCorrectionApplySaveReqVO createReqVO) {
        // 插入
        BizDataCorrectionApplyDO bizDataCorrectionApply = BeanUtils.toBean(createReqVO, BizDataCorrectionApplyDO.class);
        bizDataCorrectionApplyDao.insert(bizDataCorrectionApply);

        // 插入子表
        createBizDataCorrectionList(bizDataCorrectionApply.getId(), createReqVO.getBizDataCorrections());
        // 返回
        return bizDataCorrectionApply.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBizDataCorrectionApply(BizDataCorrectionApplySaveReqVO updateReqVO) {
        // 校验存在
        validateBizDataCorrectionApplyExists(updateReqVO.getId());
        // 更新
        BizDataCorrectionApplyDO updateObj = BeanUtils.toBean(updateReqVO, BizDataCorrectionApplyDO.class);
        bizDataCorrectionApplyDao.updateById(updateObj);

        // 更新子表
        updateBizDataCorrectionList(updateReqVO.getId(), updateReqVO.getBizDataCorrections());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBizDataCorrectionApply(String id) {
        // 校验存在
        validateBizDataCorrectionApplyExists(id);
        // 删除
        bizDataCorrectionApplyDao.deleteById(id);

        // 删除子表
        deleteBizDataCorrectionByCorrectionApplyId(id);
    }

    private void validateBizDataCorrectionApplyExists(String id) {
        if (bizDataCorrectionApplyDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-业务数据纠错申请数据不存在");
        }
    }

    @Override
    public BizDataCorrectionApplyDO getBizDataCorrectionApply(String id) {
        return bizDataCorrectionApplyDao.selectById(id);
    }


    // ==================== 子表（实战平台-监管管理-业务数据纠错） ====================

    @Override
    public List<BizDataCorrectionDO> getBizDataCorrectionListByCorrectionApplyId(String correctionApplyId) {
        return bizDataCorrectionDao.selectListByCorrectionApplyId(correctionApplyId);
    }

    private void createBizDataCorrectionList(String correctionApplyId, List<BizDataCorrectionDO> list) {
        list.forEach(o -> o.setCorrectionApplyId(correctionApplyId));
        list.forEach(o -> bizDataCorrectionDao.insert(o));
    }

    private void updateBizDataCorrectionList(String correctionApplyId, List<BizDataCorrectionDO> list) {
        deleteBizDataCorrectionByCorrectionApplyId(correctionApplyId);
		list.forEach(o -> o.setId(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createBizDataCorrectionList(correctionApplyId, list);
    }

    private void deleteBizDataCorrectionByCorrectionApplyId(String correctionApplyId) {
        bizDataCorrectionDao.deleteByCorrectionApplyId(correctionApplyId);
    }

}
