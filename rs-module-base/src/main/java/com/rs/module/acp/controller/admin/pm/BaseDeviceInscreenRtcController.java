package com.rs.module.acp.controller.admin.pm;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.pm.vo.BaseDeviceInscreenRtcListReqVO;
import com.rs.module.acp.controller.admin.pm.vo.BaseDeviceInscreenRtcPageReqVO;
import com.rs.module.acp.controller.admin.pm.vo.BaseDeviceInscreenRtcRespVO;
import com.rs.module.acp.controller.admin.pm.vo.BaseDeviceInscreenRtcSaveReqVO;
import com.rs.module.acp.entity.pm.BaseDeviceInscreenRtcDO;
import com.rs.module.acp.service.pm.BaseDeviceInscreenRtcService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-监管管理-GoCloud-RTC平台设备信息")
@RestController
@RequestMapping("/acp/pm/baseDeviceInscreenRtc")
@Validated
public class BaseDeviceInscreenRtcController {

    @Resource
    private BaseDeviceInscreenRtcService baseDeviceInscreenRtcService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-监管管理-GoCloud-RTC平台设备信息")
    public CommonResult<String> createBaseDeviceInscreenRtc(@Valid @RequestBody BaseDeviceInscreenRtcSaveReqVO createReqVO) {
        return success(baseDeviceInscreenRtcService.createBaseDeviceInscreenRtc(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-监管管理-GoCloud-RTC平台设备信息")
    public CommonResult<Boolean> updateBaseDeviceInscreenRtc(@Valid @RequestBody BaseDeviceInscreenRtcSaveReqVO updateReqVO) {
        baseDeviceInscreenRtcService.updateBaseDeviceInscreenRtc(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-监管管理-GoCloud-RTC平台设备信息")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteBaseDeviceInscreenRtc(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           baseDeviceInscreenRtcService.deleteBaseDeviceInscreenRtc(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-监管管理-GoCloud-RTC平台设备信息")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<BaseDeviceInscreenRtcRespVO> getBaseDeviceInscreenRtc(@RequestParam("id") String id) {
        BaseDeviceInscreenRtcDO baseDeviceInscreenRtc = baseDeviceInscreenRtcService.getBaseDeviceInscreenRtc(id);
        return success(BeanUtils.toBean(baseDeviceInscreenRtc, BaseDeviceInscreenRtcRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-监管管理-GoCloud-RTC平台设备信息分页")
    public CommonResult<PageResult<BaseDeviceInscreenRtcRespVO>> getBaseDeviceInscreenRtcPage(@Valid @RequestBody BaseDeviceInscreenRtcPageReqVO pageReqVO) {
        PageResult<BaseDeviceInscreenRtcDO> pageResult = baseDeviceInscreenRtcService.getBaseDeviceInscreenRtcPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BaseDeviceInscreenRtcRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-监管管理-GoCloud-RTC平台设备信息列表")
    public CommonResult<List<BaseDeviceInscreenRtcRespVO>> getBaseDeviceInscreenRtcList(@Valid @RequestBody BaseDeviceInscreenRtcListReqVO listReqVO) {
        List<BaseDeviceInscreenRtcDO> list = baseDeviceInscreenRtcService.getBaseDeviceInscreenRtcList(listReqVO);
        return success(BeanUtils.toBean(list, BaseDeviceInscreenRtcRespVO.class));
    }
}
