package com.rs.module.acp.controller.admin.pm.vo;

import com.rs.module.base.controller.admin.pm.vo.AbstractPageQueryForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备-摄像机
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-11-17 10:28:39
 */
@Data
@ApiModel("设备-摄像机分页Dto")
public class BaseDeviceCameraPageDto extends AbstractPageQueryForm {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 设备编号
     */
    @ApiModelProperty(value = "设备编号")
    private String deviceId;
    /**
     * 通道编号
     */
    @ApiModelProperty(value = "通道编号")
    private String channelId;
    /**
     * 设备IP
     */
    @ApiModelProperty(value = "设备IP")
    private String deviceIp;

    /**
     * 设备编码
     */
    @ApiModelProperty(value = "设备编码")
    private String deviceCode;
    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String deviceName;
    /**
     * 设备类型
     */
    @ApiModelProperty(value = "设备类型")
    private String deviceTypeId;
    /**
     * 所属区域
     */
    @ApiModelProperty(value = "所属区域")
    private String areaId;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String status;
    /**
     * 点位名称
     */
    @ApiModelProperty(value = "点位名称")
    private String pointName;
    /**
     * 监室号
     */
    @ApiModelProperty(value = "监室号")
    private String roomId;
    /**
     * 设备状态
     */
    @ApiModelProperty(value = "设备状态")
    private String deviceStatus;
    /**
     * 所属监所
     */
    @ApiModelProperty(value = "所属监所")
    private String prisonId;

}
