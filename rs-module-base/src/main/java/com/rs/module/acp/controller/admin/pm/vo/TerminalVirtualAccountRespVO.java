package com.rs.module.acp.controller.admin.pm.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-监管管理-虚拟终端账号 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TerminalVirtualAccountRespVO extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("虚拟终端名称")
    private String name;
    @ApiModelProperty("排序字段，从小到大")
    private Integer sortOrder;
    @ApiModelProperty("快鱼的ip")
    private String kyIp;
    @ApiModelProperty("快鱼的端口")
    private String kyPort;
    @ApiModelProperty("快鱼的密码")
    private String kyPassword;
    @ApiModelProperty("对方账号")
    private String targetAccount;
    @ApiModelProperty("账号")
    private String virtualAccount;
}
