package com.rs.module.acp.service.pm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.CollectionUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.pm.vo.BizDataCorrectionApplySaveReqVO;
import com.rs.module.acp.dao.pm.BizDataCorrectionApplyDao;
import com.rs.module.acp.dao.pm.BizDataCorrectionDao;
import com.rs.module.acp.entity.pm.BizDataCorrectionApplyDO;
import com.rs.module.acp.entity.pm.BizDataCorrectionDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;


/**
 * 实战平台-监管管理-业务数据纠错申请 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BizDataCorrectionServiceImpl extends BaseServiceImpl<BizDataCorrectionDao, BizDataCorrectionDO> implements BizDataCorrectionService {

    @Resource
    private BizDataCorrectionDao bizDataCorrectionDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createBizDataCorrection(BizDataCorrectionDO createReqVO) {
        // 插入
        bizDataCorrectionDao.insert(createReqVO);
        // 返回
        return createReqVO.getId();
    }

    @Override
    public List<BizDataCorrectionDO> getByApplyId(String applyId) {
        List<BizDataCorrectionDO> list = list(new LambdaQueryWrapper<BizDataCorrectionDO>()
                .eq(BizDataCorrectionDO::getCorrectionApplyId, applyId));
        if (CollectionUtil.isNull(list)) {
            return Collections.emptyList();
        }
        return list;
    }


}
