package com.rs.module.acp.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.pm.BaseDeviceInscreenRtcDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-监管管理-GoCloud-RTC平台设备信息 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BaseDeviceInscreenRtcDao extends IBaseDao<BaseDeviceInscreenRtcDO> {


    default PageResult<BaseDeviceInscreenRtcDO> selectPage(BaseDeviceInscreenRtcPageReqVO reqVO) {
        Page<BaseDeviceInscreenRtcDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<BaseDeviceInscreenRtcDO> wrapper = new LambdaQueryWrapperX<BaseDeviceInscreenRtcDO>()
            .eqIfPresent(BaseDeviceInscreenRtcDO::getDeviceFlag, reqVO.getDeviceFlag())
            .eqIfPresent(BaseDeviceInscreenRtcDO::getDeviceId, reqVO.getDeviceId())
            .eqIfPresent(BaseDeviceInscreenRtcDO::getSerialNumber, reqVO.getSerialNumber())
            .likeIfPresent(BaseDeviceInscreenRtcDO::getDeviceName, reqVO.getDeviceName())
            .eqIfPresent(BaseDeviceInscreenRtcDO::getRtcDevCode, reqVO.getRtcDevCode())
            .likeIfPresent(BaseDeviceInscreenRtcDO::getRtcDevUserName, reqVO.getRtcDevUserName())
            .eqIfPresent(BaseDeviceInscreenRtcDO::getRtcDevPassword, reqVO.getRtcDevPassword())
            .eqIfPresent(BaseDeviceInscreenRtcDO::getRtcDevId, reqVO.getRtcDevId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(BaseDeviceInscreenRtcDO::getAddTime);
        }
        Page<BaseDeviceInscreenRtcDO> baseDeviceInscreenRtcPage = selectPage(page, wrapper);
        return new PageResult<>(baseDeviceInscreenRtcPage.getRecords(), baseDeviceInscreenRtcPage.getTotal());
    }
    default List<BaseDeviceInscreenRtcDO> selectList(BaseDeviceInscreenRtcListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BaseDeviceInscreenRtcDO>()
            .eqIfPresent(BaseDeviceInscreenRtcDO::getDeviceFlag, reqVO.getDeviceFlag())
            .eqIfPresent(BaseDeviceInscreenRtcDO::getDeviceId, reqVO.getDeviceId())
            .eqIfPresent(BaseDeviceInscreenRtcDO::getSerialNumber, reqVO.getSerialNumber())
            .likeIfPresent(BaseDeviceInscreenRtcDO::getDeviceName, reqVO.getDeviceName())
            .eqIfPresent(BaseDeviceInscreenRtcDO::getRtcDevCode, reqVO.getRtcDevCode())
            .likeIfPresent(BaseDeviceInscreenRtcDO::getRtcDevUserName, reqVO.getRtcDevUserName())
            .eqIfPresent(BaseDeviceInscreenRtcDO::getRtcDevPassword, reqVO.getRtcDevPassword())
            .eqIfPresent(BaseDeviceInscreenRtcDO::getRtcDevId, reqVO.getRtcDevId())
        .orderByDesc(BaseDeviceInscreenRtcDO::getAddTime));    }


    }
