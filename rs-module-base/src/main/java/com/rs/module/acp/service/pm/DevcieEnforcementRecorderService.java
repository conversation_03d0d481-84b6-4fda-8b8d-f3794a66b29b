package com.rs.module.acp.service.pm;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.DevcieEnforcementRecorderDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-监管管理-执法记录仪设备 Service 接口
 *
 * <AUTHOR>
 */
public interface DevcieEnforcementRecorderService extends IBaseService<DevcieEnforcementRecorderDO>{

    /**
     * 创建实战平台-监管管理-执法记录仪设备
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createDevcieEnforcementRecorder(@Valid DevcieEnforcementRecorderSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-执法记录仪设备
     *
     * @param updateReqVO 更新信息
     */
    void updateDevcieEnforcementRecorder(@Valid DevcieEnforcementRecorderSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-执法记录仪设备
     *
     * @param id 编号
     */
    void deleteDevcieEnforcementRecorder(String id);

    /**
     * 获得实战平台-监管管理-执法记录仪设备
     *
     * @param id 编号
     * @return 实战平台-监管管理-执法记录仪设备
     */
    DevcieEnforcementRecorderDO getDevcieEnforcementRecorder(String id);

    /**
    * 获得实战平台-监管管理-执法记录仪设备分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-监管管理-执法记录仪设备分页
    */
    PageResult<DevcieEnforcementRecorderDO> getDevcieEnforcementRecorderPage(DevcieEnforcementRecorderPageReqVO pageReqVO);

    /**
    * 获得实战平台-监管管理-执法记录仪设备列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-监管管理-执法记录仪设备列表
    */
    List<DevcieEnforcementRecorderDO> getDevcieEnforcementRecorderList(DevcieEnforcementRecorderListReqVO listReqVO);


}
