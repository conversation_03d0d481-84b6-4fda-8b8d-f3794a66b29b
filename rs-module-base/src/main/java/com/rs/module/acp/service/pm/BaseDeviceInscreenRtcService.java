package com.rs.module.acp.service.pm;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.BaseDeviceInscreenRtcDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-监管管理-GoCloud-RTC平台设备信息 Service 接口
 *
 * <AUTHOR>
 */
public interface BaseDeviceInscreenRtcService extends IBaseService<BaseDeviceInscreenRtcDO>{

    /**
     * 创建实战平台-监管管理-GoCloud-RTC平台设备信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createBaseDeviceInscreenRtc(@Valid BaseDeviceInscreenRtcSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-GoCloud-RTC平台设备信息
     *
     * @param updateReqVO 更新信息
     */
    void updateBaseDeviceInscreenRtc(@Valid BaseDeviceInscreenRtcSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-GoCloud-RTC平台设备信息
     *
     * @param id 编号
     */
    void deleteBaseDeviceInscreenRtc(String id);

    /**
     * 获得实战平台-监管管理-GoCloud-RTC平台设备信息
     *
     * @param id 编号
     * @return 实战平台-监管管理-GoCloud-RTC平台设备信息
     */
    BaseDeviceInscreenRtcDO getBaseDeviceInscreenRtc(String id);

    /**
    * 获得实战平台-监管管理-GoCloud-RTC平台设备信息分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-监管管理-GoCloud-RTC平台设备信息分页
    */
    PageResult<BaseDeviceInscreenRtcDO> getBaseDeviceInscreenRtcPage(BaseDeviceInscreenRtcPageReqVO pageReqVO);

    /**
    * 获得实战平台-监管管理-GoCloud-RTC平台设备信息列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-监管管理-GoCloud-RTC平台设备信息列表
    */
    List<BaseDeviceInscreenRtcDO> getBaseDeviceInscreenRtcList(BaseDeviceInscreenRtcListReqVO listReqVO);


}
