package com.rs.module.acp.controller.admin.pm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-监管管理-GoCloud-RTC平台设备信息新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseDeviceInscreenRtcSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("1，设备，3用户")
    @NotNull(message = "1，设备，3用户不能为空")
    private Short deviceFlag;

    @ApiModelProperty("device_id")
    private String deviceId;

    @ApiModelProperty("serial_number")
    private String serialNumber;

    @ApiModelProperty("描述信息-设备名")
    private String deviceName;

    @ApiModelProperty("会议平台设备设备编号")
    private Long rtcDevCode;

    @ApiModelProperty("会议平台设备用户名")
    private String rtcDevUserName;

    @ApiModelProperty("会议平台设备密码")
    private String rtcDevPassword;

    @ApiModelProperty("rtc_dev_id")
    private Long rtcDevId;

}
