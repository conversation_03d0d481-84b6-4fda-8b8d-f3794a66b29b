package com.rs.module.acp.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.pm.BaseDeviceFaceCameraDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-监管管理-人脸摄像头设备 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BaseDeviceFaceCameraDao extends IBaseDao<BaseDeviceFaceCameraDO> {


    default PageResult<BaseDeviceFaceCameraDO> selectPage(BaseDeviceFaceCameraPageReqVO reqVO) {
        Page<BaseDeviceFaceCameraDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<BaseDeviceFaceCameraDO> wrapper = new LambdaQueryWrapperX<BaseDeviceFaceCameraDO>()
            .eqIfPresent(BaseDeviceFaceCameraDO::getDeviceId, reqVO.getDeviceId())
            .eqIfPresent(BaseDeviceFaceCameraDO::getChannelId, reqVO.getChannelId())
            .eqIfPresent(BaseDeviceFaceCameraDO::getDeviceIp, reqVO.getDeviceIp())
            .eqIfPresent(BaseDeviceFaceCameraDO::getPersonType, reqVO.getPersonType())
            .likeIfPresent(BaseDeviceFaceCameraDO::getChannelName, reqVO.getChannelName())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(BaseDeviceFaceCameraDO::getAddTime);
        }
        Page<BaseDeviceFaceCameraDO> baseDeviceFaceCameraPage = selectPage(page, wrapper);
        return new PageResult<>(baseDeviceFaceCameraPage.getRecords(), baseDeviceFaceCameraPage.getTotal());
    }
    default List<BaseDeviceFaceCameraDO> selectList(BaseDeviceFaceCameraListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BaseDeviceFaceCameraDO>()
            .eqIfPresent(BaseDeviceFaceCameraDO::getDeviceId, reqVO.getDeviceId())
            .eqIfPresent(BaseDeviceFaceCameraDO::getChannelId, reqVO.getChannelId())
            .eqIfPresent(BaseDeviceFaceCameraDO::getDeviceIp, reqVO.getDeviceIp())
            .eqIfPresent(BaseDeviceFaceCameraDO::getPersonType, reqVO.getPersonType())
            .likeIfPresent(BaseDeviceFaceCameraDO::getChannelName, reqVO.getChannelName())
        .orderByDesc(BaseDeviceFaceCameraDO::getAddTime));    }


    }
