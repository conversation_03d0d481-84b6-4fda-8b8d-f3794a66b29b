package com.rs.module.acp.service.pm;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.TerminalVirtualAccountStatusDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-监管管理-虚拟终端账号-使用状态 Service 接口
 *
 * <AUTHOR>
 */
public interface TerminalVirtualAccountStatusService extends IBaseService<TerminalVirtualAccountStatusDO>{

    /**
     * 创建实战平台-监管管理-虚拟终端账号-使用状态
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createTerminalVirtualAccountStatus(@Valid TerminalVirtualAccountStatusSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-虚拟终端账号-使用状态
     *
     * @param updateReqVO 更新信息
     */
    void updateTerminalVirtualAccountStatus(@Valid TerminalVirtualAccountStatusSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-虚拟终端账号-使用状态
     *
     * @param id 编号
     */
    void deleteTerminalVirtualAccountStatus(String id);

    /**
     * 获得实战平台-监管管理-虚拟终端账号-使用状态
     *
     * @param id 编号
     * @return 实战平台-监管管理-虚拟终端账号-使用状态
     */
    TerminalVirtualAccountStatusDO getTerminalVirtualAccountStatus(String id);

    /**
    * 获得实战平台-监管管理-虚拟终端账号-使用状态分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-监管管理-虚拟终端账号-使用状态分页
    */
    PageResult<TerminalVirtualAccountStatusDO> getTerminalVirtualAccountStatusPage(TerminalVirtualAccountStatusPageReqVO pageReqVO);

    /**
    * 获得实战平台-监管管理-虚拟终端账号-使用状态列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-监管管理-虚拟终端账号-使用状态列表
    */
    List<TerminalVirtualAccountStatusDO> getTerminalVirtualAccountStatusList(TerminalVirtualAccountStatusListReqVO listReqVO);


}
