package com.rs.module.acp.entity.pm;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-监管管理-视频异动设备信息 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_device_video_move")
@KeySequence("acp_pm_device_video_move_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_device_video_move")
public class BaseDeviceVideoMoveDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 设备编号
     */
    private String deviceId;
    /**
     * 厂家设备编号
     */
    private String factoryDeviceId;
    /**
     * 设备ip
     */
    private String deviceIp;
    /**
     * 通道编号
     */
    private String channelId;
    /**
     * 通道名称
     */
    private String channelName;

}
