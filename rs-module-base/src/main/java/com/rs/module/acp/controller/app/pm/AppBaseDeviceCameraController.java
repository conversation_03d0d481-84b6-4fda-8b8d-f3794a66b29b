package com.rs.module.acp.controller.app.pm;

import com.alibaba.fastjson.JSON;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.pm.vo.BaseDeviceCameraListReqVO;
import com.rs.module.acp.controller.admin.pm.vo.BaseDeviceCameraPageReqVO;
import com.rs.module.acp.controller.admin.pm.vo.BaseDeviceCameraRespVO;
import com.rs.module.acp.controller.admin.pm.vo.BaseDeviceCameraSaveReqVO;
import com.rs.module.acp.entity.pm.BaseDeviceCameraDO;
import com.rs.module.acp.service.pm.BaseDeviceCameraService;
import com.rs.module.base.controller.admin.video.vo.OtherRealTimeStreamAppDto;
import com.rs.module.base.service.video.VideoRouteService;
import com.rs.module.base.vo.RoomVideoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "仓内外屏-监管管理-视频播放")
@RestController
@RequestMapping("/app/acp/pm/baseDeviceCamera")
@Validated
public class AppBaseDeviceCameraController {

    @Resource
    private BaseDeviceCameraService baseDeviceCameraService;
    @Autowired
    private VideoRouteService videoRouteService;
    @GetMapping(value = "/roomVideo")
    @ApiOperation(value = "监室关联视频")
    @ApiImplicitParam(paramType = "path", name = "roomId", value = "监室id", required = true)
    public CommonResult<List<RoomVideoVO>> roomVideo(@RequestParam("roomId") String roomId) {
        return success(baseDeviceCameraService.roomVideo(roomId));
    }
    @PostMapping(value = "/getRealTimeStreamForApp")
    @ApiOperation(value = "app点流-对接视频联网新版本V1.0")
    public CommonResult<String> getRealTimeStreamForApp(@RequestBody OtherRealTimeStreamAppDto dto) {
        try {
            if(dto.getStreamAgentType()==null){
                dto.setStreamAgentType(5);
            }
            String realTimeStreamForApp = videoRouteService.getRealTimeStreamForApp(dto);
            CommonResult CommonResult = JSON.parseObject(realTimeStreamForApp, CommonResult.class);
            return CommonResult;
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(e.getMessage());
        }
    }
}
