package com.rs.module.acp.service.pm;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.BaseDeviceInscreenRtcDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pm.BaseDeviceInscreenRtcDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-监管管理-GoCloud-RTC平台设备信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BaseDeviceInscreenRtcServiceImpl extends BaseServiceImpl<BaseDeviceInscreenRtcDao, BaseDeviceInscreenRtcDO> implements BaseDeviceInscreenRtcService {

    @Resource
    private BaseDeviceInscreenRtcDao baseDeviceInscreenRtcDao;

    @Override
    public String createBaseDeviceInscreenRtc(BaseDeviceInscreenRtcSaveReqVO createReqVO) {
        // 插入
        BaseDeviceInscreenRtcDO baseDeviceInscreenRtc = BeanUtils.toBean(createReqVO, BaseDeviceInscreenRtcDO.class);
        baseDeviceInscreenRtcDao.insert(baseDeviceInscreenRtc);
        // 返回
        return baseDeviceInscreenRtc.getId();
    }

    @Override
    public void updateBaseDeviceInscreenRtc(BaseDeviceInscreenRtcSaveReqVO updateReqVO) {
        // 校验存在
        validateBaseDeviceInscreenRtcExists(updateReqVO.getId());
        // 更新
        BaseDeviceInscreenRtcDO updateObj = BeanUtils.toBean(updateReqVO, BaseDeviceInscreenRtcDO.class);
        baseDeviceInscreenRtcDao.updateById(updateObj);
    }

    @Override
    public void deleteBaseDeviceInscreenRtc(String id) {
        // 校验存在
        validateBaseDeviceInscreenRtcExists(id);
        // 删除
        baseDeviceInscreenRtcDao.deleteById(id);
    }

    private void validateBaseDeviceInscreenRtcExists(String id) {
        if (baseDeviceInscreenRtcDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-GoCloud-RTC平台设备信息数据不存在");
        }
    }

    @Override
    public BaseDeviceInscreenRtcDO getBaseDeviceInscreenRtc(String id) {
        return baseDeviceInscreenRtcDao.selectById(id);
    }

    @Override
    public PageResult<BaseDeviceInscreenRtcDO> getBaseDeviceInscreenRtcPage(BaseDeviceInscreenRtcPageReqVO pageReqVO) {
        return baseDeviceInscreenRtcDao.selectPage(pageReqVO);
    }

    @Override
    public List<BaseDeviceInscreenRtcDO> getBaseDeviceInscreenRtcList(BaseDeviceInscreenRtcListReqVO listReqVO) {
        return baseDeviceInscreenRtcDao.selectList(listReqVO);
    }


}
