package com.rs.module.acp.service.pm;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.TerminalVirtualAccountStatusDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pm.TerminalVirtualAccountStatusDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-监管管理-虚拟终端账号-使用状态 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TerminalVirtualAccountStatusServiceImpl extends BaseServiceImpl<TerminalVirtualAccountStatusDao, TerminalVirtualAccountStatusDO> implements TerminalVirtualAccountStatusService {

    @Resource
    private TerminalVirtualAccountStatusDao terminalVirtualAccountStatusDao;

    @Override
    public String createTerminalVirtualAccountStatus(TerminalVirtualAccountStatusSaveReqVO createReqVO) {
        // 插入
        TerminalVirtualAccountStatusDO terminalVirtualAccountStatus = BeanUtils.toBean(createReqVO, TerminalVirtualAccountStatusDO.class);
        terminalVirtualAccountStatusDao.insert(terminalVirtualAccountStatus);
        // 返回
        return terminalVirtualAccountStatus.getId();
    }

    @Override
    public void updateTerminalVirtualAccountStatus(TerminalVirtualAccountStatusSaveReqVO updateReqVO) {
        // 校验存在
        validateTerminalVirtualAccountStatusExists(updateReqVO.getId());
        // 更新
        TerminalVirtualAccountStatusDO updateObj = BeanUtils.toBean(updateReqVO, TerminalVirtualAccountStatusDO.class);
        terminalVirtualAccountStatusDao.updateById(updateObj);
    }

    @Override
    public void deleteTerminalVirtualAccountStatus(String id) {
        // 校验存在
        validateTerminalVirtualAccountStatusExists(id);
        // 删除
        terminalVirtualAccountStatusDao.deleteById(id);
    }

    private void validateTerminalVirtualAccountStatusExists(String id) {
        if (terminalVirtualAccountStatusDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-虚拟终端账号-使用状态数据不存在");
        }
    }

    @Override
    public TerminalVirtualAccountStatusDO getTerminalVirtualAccountStatus(String id) {
        return terminalVirtualAccountStatusDao.selectById(id);
    }

    @Override
    public PageResult<TerminalVirtualAccountStatusDO> getTerminalVirtualAccountStatusPage(TerminalVirtualAccountStatusPageReqVO pageReqVO) {
        return terminalVirtualAccountStatusDao.selectPage(pageReqVO);
    }

    @Override
    public List<TerminalVirtualAccountStatusDO> getTerminalVirtualAccountStatusList(TerminalVirtualAccountStatusListReqVO listReqVO) {
        return terminalVirtualAccountStatusDao.selectList(listReqVO);
    }


}
