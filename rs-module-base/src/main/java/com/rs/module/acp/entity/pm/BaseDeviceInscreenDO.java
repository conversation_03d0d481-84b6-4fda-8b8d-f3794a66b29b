package com.rs.module.acp.entity.pm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

/**
 * 实战平台-监管管理-仓内屏设备 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_device_inscreen")
@KeySequence("acp_pm_device_inscreen_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BaseDeviceInscreenDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 序列号
     */
    private String serialNumber;
    /**
     * 设备编号
     */
    private String deviceId;
    /**
     * 设备ip
     */
    private String deviceIp;
    /**
     * 监室号
     */
    private String roomId;
    /**
     * 设备编号（本机编号）
     */
    private Integer deviceNum;
    /**
     * 主机编号
     */
    private Integer hostNum;
    /**
     * 地址盒IP
     */
    private String addressIp;
    /**
     * 设备类型 1仓内屏，2仓外屏，3对讲分机，4对讲主机，5揭西大屏联动主机
     */
    private Integer deviceType;
    /**
     * 描述信息
     */
    private String remark;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 主机设备id
     */
    private String hostDeviceId;
    /**
     * 设备用户名，base_device_rtc表迁移过来
     */
    private String devUserName;
    /**
     * 设备密码
     */
    private String devPassword;

}
