package com.rs.module.acp.service.pm;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.BaseDeviceVideoMoveDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-监管管理-视频异动设备信息 Service 接口
 *
 * <AUTHOR>
 */
public interface BaseDeviceVideoMoveService extends IBaseService<BaseDeviceVideoMoveDO>{

    /**
     * 创建实战平台-监管管理-视频异动设备信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createBaseDeviceVideoMove(@Valid BaseDeviceVideoMoveSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-视频异动设备信息
     *
     * @param updateReqVO 更新信息
     */
    void updateBaseDeviceVideoMove(@Valid BaseDeviceVideoMoveSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-视频异动设备信息
     *
     * @param id 编号
     */
    void deleteBaseDeviceVideoMove(String id);

    /**
     * 获得实战平台-监管管理-视频异动设备信息
     *
     * @param id 编号
     * @return 实战平台-监管管理-视频异动设备信息
     */
    BaseDeviceVideoMoveDO getBaseDeviceVideoMove(String id);

    /**
    * 获得实战平台-监管管理-视频异动设备信息分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-监管管理-视频异动设备信息分页
    */
    PageResult<BaseDeviceVideoMoveDO> getBaseDeviceVideoMovePage(BaseDeviceVideoMovePageReqVO pageReqVO);

    /**
    * 获得实战平台-监管管理-视频异动设备信息列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-监管管理-视频异动设备信息列表
    */
    List<BaseDeviceVideoMoveDO> getBaseDeviceVideoMoveList(BaseDeviceVideoMoveListReqVO listReqVO);


}
