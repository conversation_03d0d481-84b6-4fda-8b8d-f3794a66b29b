package com.rs.module.acp.service.pm;

import java.util.*;
import javax.validation.*;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.BaseDeviceCameraDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.base.vo.RoomVideoVO;

/**
 * 实战平台-监管管理-摄像机设备 Service 接口
 *
 * <AUTHOR>
 */
public interface BaseDeviceCameraService extends IBaseService<BaseDeviceCameraDO>{

    /**
     * 创建实战平台-监管管理-摄像机设备
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createBaseDeviceCamera(@Valid BaseDeviceCameraSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-摄像机设备
     *
     * @param updateReqVO 更新信息
     */
    void updateBaseDeviceCamera(@Valid BaseDeviceCameraSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-摄像机设备
     *
     * @param id 编号
     */
    void deleteBaseDeviceCamera(String id);

    /**
     * 获得实战平台-监管管理-摄像机设备
     *
     * @param id 编号
     * @return 实战平台-监管管理-摄像机设备
     */
    BaseDeviceCameraDO getBaseDeviceCamera(String id);

    /**
    * 获得实战平台-监管管理-摄像机设备分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-监管管理-摄像机设备分页
    */
    PageResult<BaseDeviceCameraDO> getBaseDeviceCameraPage(BaseDeviceCameraPageReqVO pageReqVO);

    /**
    * 获得实战平台-监管管理-摄像机设备列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-监管管理-摄像机设备列表
    */
    List<BaseDeviceCameraDO> getBaseDeviceCameraList(BaseDeviceCameraListReqVO listReqVO);


    List<BaseDeviceCameraPageVO> findByPage(Page page, BaseDeviceCameraPageDto pageDto);

    BaseDeviceCameraPageVO findOneById(String id);

    Integer updateChanDeviceStatus(String chanId, Integer type);

    Integer clearDeviceCameraTable(String channelId, String orgCode);

    List<RoomVideoVO> roomVideo(String roomId);
}
