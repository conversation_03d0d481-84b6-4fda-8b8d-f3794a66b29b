package com.rs.module.acp.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.entity.pm.BaseDeviceInscreenDO;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDeviceSaveReqVO;
import com.rs.module.base.entity.pm.device.BaseDeviceDO;

import java.util.List;

/**
 * 实战平台-监管管理-仓内屏设备 Service 接口
 *
 * <AUTHOR>
 */
public interface BaseDeviceInscreenService extends IBaseService<BaseDeviceInscreenDO>{


    /**
     * 获得仓内屏设备
     *
     * @param id 编号
     * @return 仓内屏设备
     */
    BaseDeviceInscreenDO getBaseDeviceInscreen(String id);

    /**
     * 获得仓内屏设备
     *
     * @param roomId 监室编号
     * @return 仓内屏设备
     */
    List<BaseDeviceInscreenDO> getByRoomId(String roomId);

    /**
     * 获得仓内屏设备
     *
     * @param orgCode 监所编号
     * @return 仓内屏设备
     */
    List<BaseDeviceInscreenDO> getByOrgCode(String orgCode);

    BaseDeviceInscreenDO getCnpByRoomId(String roomId);

    /**
     * 保存或者更新设备信息
     * @param createReqVO
     * @param baseDevice
     */
    void saveOrUpdateDeviceInfo(BaseDeviceSaveReqVO createReqVO, BaseDeviceDO baseDevice);
    
    /**
     * 根据条件获取仓内屏设备
     * @param condition String 查询条件(二维数组)
     * @param deviceType Integer 设备类型
     * @return List<BaseDeviceInscreenDO>
     */
    public List<BaseDeviceInscreenDO> getInscreenByCondition(String condition, Integer deviceType);
    
    /**
     * 根据设备序列号获取仓内屏设备
     * @param serialNumber String 设备序列号
     * @return BaseDeviceInscreenDO
     */
    public BaseDeviceInscreenDO getInscreenBySeiralNumber(String serialNumber);
}
