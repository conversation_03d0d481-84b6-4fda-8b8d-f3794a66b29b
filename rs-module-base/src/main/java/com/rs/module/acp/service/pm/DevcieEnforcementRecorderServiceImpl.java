package com.rs.module.acp.service.pm;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.DevcieEnforcementRecorderDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pm.DevcieEnforcementRecorderDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-监管管理-执法记录仪设备 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DevcieEnforcementRecorderServiceImpl extends BaseServiceImpl<DevcieEnforcementRecorderDao, DevcieEnforcementRecorderDO> implements DevcieEnforcementRecorderService {

    @Resource
    private DevcieEnforcementRecorderDao devcieEnforcementRecorderDao;

    @Override
    public String createDevcieEnforcementRecorder(DevcieEnforcementRecorderSaveReqVO createReqVO) {
        // 插入
        DevcieEnforcementRecorderDO devcieEnforcementRecorder = BeanUtils.toBean(createReqVO, DevcieEnforcementRecorderDO.class);
        devcieEnforcementRecorderDao.insert(devcieEnforcementRecorder);
        // 返回
        return devcieEnforcementRecorder.getId();
    }

    @Override
    public void updateDevcieEnforcementRecorder(DevcieEnforcementRecorderSaveReqVO updateReqVO) {
        // 校验存在
        validateDevcieEnforcementRecorderExists(updateReqVO.getId());
        // 更新
        DevcieEnforcementRecorderDO updateObj = BeanUtils.toBean(updateReqVO, DevcieEnforcementRecorderDO.class);
        devcieEnforcementRecorderDao.updateById(updateObj);
    }

    @Override
    public void deleteDevcieEnforcementRecorder(String id) {
        // 校验存在
        validateDevcieEnforcementRecorderExists(id);
        // 删除
        devcieEnforcementRecorderDao.deleteById(id);
    }

    private void validateDevcieEnforcementRecorderExists(String id) {
        if (devcieEnforcementRecorderDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-执法记录仪设备数据不存在");
        }
    }

    @Override
    public DevcieEnforcementRecorderDO getDevcieEnforcementRecorder(String id) {
        return devcieEnforcementRecorderDao.selectById(id);
    }

    @Override
    public PageResult<DevcieEnforcementRecorderDO> getDevcieEnforcementRecorderPage(DevcieEnforcementRecorderPageReqVO pageReqVO) {
        return devcieEnforcementRecorderDao.selectPage(pageReqVO);
    }

    @Override
    public List<DevcieEnforcementRecorderDO> getDevcieEnforcementRecorderList(DevcieEnforcementRecorderListReqVO listReqVO) {
        return devcieEnforcementRecorderDao.selectList(listReqVO);
    }


}
