package com.rs.module.acp.service.pm;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.BaseDeviceFaceCameraDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-监管管理-人脸摄像头设备 Service 接口
 *
 * <AUTHOR>
 */
public interface BaseDeviceFaceCameraService extends IBaseService<BaseDeviceFaceCameraDO>{

    /**
     * 创建实战平台-监管管理-人脸摄像头设备
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createBaseDeviceFaceCamera(@Valid BaseDeviceFaceCameraSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-人脸摄像头设备
     *
     * @param updateReqVO 更新信息
     */
    void updateBaseDeviceFaceCamera(@Valid BaseDeviceFaceCameraSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-人脸摄像头设备
     *
     * @param id 编号
     */
    void deleteBaseDeviceFaceCamera(String id);

    /**
     * 获得实战平台-监管管理-人脸摄像头设备
     *
     * @param id 编号
     * @return 实战平台-监管管理-人脸摄像头设备
     */
    BaseDeviceFaceCameraDO getBaseDeviceFaceCamera(String id);

    /**
    * 获得实战平台-监管管理-人脸摄像头设备分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-监管管理-人脸摄像头设备分页
    */
    PageResult<BaseDeviceFaceCameraDO> getBaseDeviceFaceCameraPage(BaseDeviceFaceCameraPageReqVO pageReqVO);

    /**
    * 获得实战平台-监管管理-人脸摄像头设备列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-监管管理-人脸摄像头设备列表
    */
    List<BaseDeviceFaceCameraDO> getBaseDeviceFaceCameraList(BaseDeviceFaceCameraListReqVO listReqVO);


}
