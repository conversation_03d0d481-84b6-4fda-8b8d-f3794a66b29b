package com.rs.module.acp.dao.pm;

import cn.hutool.core.collection.CollectionUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.acp.entity.pm.BaseDeviceInscreenDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;

/**
* 实战平台-监管管理-仓内屏设备 Dao
*
* <AUTHOR>
*/
@Mapper
public interface RsBaseDeviceInscreenDao extends IBaseDao<BaseDeviceInscreenDO> {

    default List<BaseDeviceInscreenDO> getListByRoomIds(List<String> roomIdList) {
        if (CollectionUtil.isEmpty(roomIdList)) {
            new ArrayList<>();
        }
        return selectList(new LambdaQueryWrapper<BaseDeviceInscreenDO>()
                .in(BaseDeviceInscreenDO::getRoomId, roomIdList));
    }

    /**
     * 获取仓内屏设备
     * @param areaIds List<String> 区域Id集合
     * @param roomId String 监室Pd
     * @param deviceType Integer 设备类型
     * @return List<BaseDeviceInscreenDO>
     */
    public List<BaseDeviceInscreenDO> getInscreenByCondition(@Param("areaIds") List<String> areaIds,
    		@Param("roomId") String roomId, @Param("deviceType") Integer deviceType);
}
