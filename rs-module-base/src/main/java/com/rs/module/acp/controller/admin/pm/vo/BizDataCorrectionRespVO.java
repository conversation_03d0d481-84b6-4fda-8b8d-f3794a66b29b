package com.rs.module.acp.controller.admin.pm.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-监管管理-业务数据纠错 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BizDataCorrectionRespVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("业务数据纠错申请ID")
    private String correctionApplyId;
    @ApiModelProperty("业务代码")
    private String businessCode;
    @ApiModelProperty("业务名称")
    private String businessName;
    @ApiModelProperty("业务ID")
    private String businessId;
    @ApiModelProperty("变更内容")
    private List<BizDataCorrectionDataVO> changedContent;

}
