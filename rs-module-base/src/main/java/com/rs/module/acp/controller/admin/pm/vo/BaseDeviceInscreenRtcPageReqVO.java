package com.rs.module.acp.controller.admin.pm.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-监管管理-GoCloud-RTC平台设备信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BaseDeviceInscreenRtcPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("1，设备，3用户")
    private Short deviceFlag;

    @ApiModelProperty("device_id")
    private String deviceId;

    @ApiModelProperty("serial_number")
    private String serialNumber;

    @ApiModelProperty("描述信息-设备名")
    private String deviceName;

    @ApiModelProperty("会议平台设备设备编号")
    private Long rtcDevCode;

    @ApiModelProperty("会议平台设备用户名")
    private String rtcDevUserName;

    @ApiModelProperty("会议平台设备密码")
    private String rtcDevPassword;

    @ApiModelProperty("rtc_dev_id")
    private Long rtcDevId;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
