package com.rs.module.acp.controller.admin.pm.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-监管管理-视频异动设备信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BaseDeviceVideoMovePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("设备编号")
    private String deviceId;

    @ApiModelProperty("厂家设备编号")
    private String factoryDeviceId;

    @ApiModelProperty("设备ip")
    private String deviceIp;

    @ApiModelProperty("通道编号")
    private String channelId;

    @ApiModelProperty("通道名称")
    private String channelName;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
