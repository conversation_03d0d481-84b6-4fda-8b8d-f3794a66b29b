package com.rs.module.base.controller.admin.zh.vo.staffduty;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
@ApiModel(description="")
public class StaffDutyPostRespVO extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "模板表id")
    private String tempId;

    @ApiModelProperty(value = "值班岗位")
    private String postName;

    @ApiModelProperty(value = "是否有值班岗位 0-无 1-有")
    private Integer hasSubPost;

    @ApiModelProperty(value = "值班角色列表")
    private List<StaffDutyRoleRespVO> dutyRoleDTOS;

}
