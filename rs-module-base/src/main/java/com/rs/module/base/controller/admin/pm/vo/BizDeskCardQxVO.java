package com.rs.module.base.controller.admin.pm.vo;

import com.alibaba.fastjson.JSONArray;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "BizDeskCardQxVO对象", description = "总台配置选项卡权限VO实体")
@Data
public class BizDeskCardQxVO implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 6602418704346180746L;


	@ApiModelProperty(value = "总台id")
	private String bizDeskId;

	@ApiModelProperty(value = "选项卡id")
	private String cardId;

	@ApiModelProperty(value = "选项卡名称")
	private String cardName;

	@ApiModelProperty(value = "选项卡url")
	private String cardUrl;

	@ApiModelProperty(value = "排序")
	private Integer orderId;

	@ApiModelProperty(value = "是否权限控制")
	private Short isEnableBrowsingPermission;

	@ApiModelProperty(value = "地址类型(1:外部0:内部)")
	private String urlType;

	@ApiModelProperty(value = "加载类型(01:微前端)")
	private String loaderType;

	@ApiModelProperty(value = "容器标识（微前端模式使用）")
	private String containerMark;

	@ApiModelProperty(value = "微前端地址")
	private String microWebUrl;

	@ApiModelProperty(value = "角色数组")
	private JSONArray roleArr;

	@ApiModelProperty(value = "区域数组")
	private JSONArray regArr;

	@ApiModelProperty(value = "机构数组")
	private JSONArray orgArr;

	@ApiModelProperty(value = "路径激活规则")
	private String activeRule;

}
