package com.rs.module.base.controller.admin.pm.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@ApiModel(description = "管理后台 - 外来人员 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PoreginPersonnelPageReqVO extends PageParam {

    @ApiModelProperty("add_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[] addTime;

    @ApiModelProperty("姓名")
    private String xm;

    @ApiModelProperty("监所代码")
    private String orgCode;

    @ApiModelProperty("外来人员类型（01：办案人员，02：律师，03：家属，04：领事会见, 05：其他访客）")
    private String ryType;



}
