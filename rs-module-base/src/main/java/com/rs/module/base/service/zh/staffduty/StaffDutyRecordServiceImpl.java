package com.rs.module.base.service.zh.staffduty;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.UserApi;
import com.rs.adapter.bsp.api.dto.UserRespDTO;
import com.rs.module.base.controller.admin.zh.vo.staffduty.*;
import com.rs.module.base.dao.zh.StaffDutyRecordDao;
import com.rs.module.base.dao.zh.StaffDutyRecordPersonDao;
import com.rs.module.base.entity.zh.StaffDutyRecordDO;
import com.rs.module.base.entity.zh.StaffDutyRecordPersonDO;
import com.rs.module.base.util.StaffDutyDateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.util.*;

/**
 *
 */
@Slf4j
@Service
public class StaffDutyRecordServiceImpl extends BaseServiceImpl<StaffDutyRecordDao, StaffDutyRecordDO> implements StaffDutyRecordService {

    @Resource
    private StaffDutyRecordDao staffDutyRecordDao;

    @Resource
    private StaffDutyRecordPersonDao recordPersonDao;

    @Resource
    private StaffDutyRecordPersonDao personDao;
    @Autowired
    private UserApi userApi;
    private static Map<String, Object> posList = new HashMap<>();


    @Override
    public DutyManageVO listByDutyDate(Date startTime, Date endTime,Integer templateType) {
        String tempId = staffDutyRecordDao.getTempId(SessionUserUtil.getSessionUser().getOrgCode(), templateType);
        List<DutyManageHeaderVO> headerList = staffDutyRecordDao.getHeaderList(tempId);
        JSONArray personList = new JSONArray();

        try {
            int day = StaffDutyDateUtils.daysBetween(startTime, endTime);
            for (int i = 0; i <= day; i++) {
                Date date = StaffDutyDateUtils.addDateDays(startTime, i);
                String format = StaffDutyDateUtils.format(date, StaffDutyDateUtils.DATE_PATTERN);
                personList.add(buildDutyDayData(tempId, format));
            }
        } catch (ParseException e) {
            log.error("查询值班安排出错: {}", e.getMessage());
            throw new RuntimeException(e);
        }

        return buildDutyManageVO(tempId, headerList, personList);
    }
    @Override
    public DutyManageVO listBySingleDutyDate(String dutyDate,Integer templateType) {
        String tempId = staffDutyRecordDao.getTempId(SessionUserUtil.getSessionUser().getOrgCode(), templateType);
        List<DutyManageHeaderVO> headerList = staffDutyRecordDao.getHeaderList(tempId);
        JSONArray personList = new JSONArray();

        try {
            personList.add(buildDutyDayData(tempId, dutyDate));
        } catch (Exception e) {
            log.error("查询单天值班安排出错: {}", e.getMessage());
            throw new RuntimeException(e);
        }

        return buildDutyManageVO(tempId, headerList, personList);
    }
    @Override
    public List<JSONObject> listByDutySingleDateIndex(String dutyDate, String orgCode,Integer templateType){
        List<JSONObject> list = new ArrayList<>();
        String tempId = staffDutyRecordDao.getTempId(orgCode, templateType);
        List<DutyManageHeaderVO> headerList = staffDutyRecordDao.getHeaderList(tempId);
        //List<String> postKeyByTempId = staffDutyRecordDao.getPostKeyByTempId(tempId);

        //按照值班岗位分组 合并当天值班人员
        for (DutyManageHeaderVO headerVO : headerList) {
            JSONObject result = new JSONObject();
            JSONArray personList = new JSONArray();
            Map<String, StaffDutyRecordPersoRespVO> personVOMap = new HashMap<>();
            for (DutyManageSubPostVO subPostVO : headerVO.getSubPostList()){
                for (DutyManageSubPostTimeVO timeVO:subPostVO.getSubPostTimeVOS()) {
                    List<StaffDutyRecordPersoRespVO> personVOS = staffDutyRecordDao.getPersonList(timeVO.getPostKey(), dutyDate);
                    if (personVOS != null) {
                        for (StaffDutyRecordPersoRespVO personVO : personVOS) {
                            personVOMap.put(personVO.getPoliceId(), personVO);
                        }
                    }
                }

            }
            // 获取用户照片信息
            List<UserRespDTO> userRespDTOList = userApi.getUserPhotoByIdCard(personVOMap.keySet());
            personVOMap.forEach((key, personVO) -> {
                UserRespDTO userRespDTO = userRespDTOList.stream().filter(user -> user.getIdCard().equals(key)).findFirst().orElse(null);
                if (userRespDTO != null) {
                    personVO.setPhoto(userRespDTO.getPhoto() == null ? "" : userRespDTO.getPhoto());
                }
                personList.add(buildPersonData(personVO));
            });
            result.put(headerVO.getPost(), personList);
            list.add( result);
        }
        return list;
    }
    private JSONObject buildDutyDayData(String tempId, String date) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("date", date);
        List<String> postKeyByTempId = staffDutyRecordDao.getPostKeyByTempId(tempId);

        for (String postKeyId : postKeyByTempId) {
            List<StaffDutyRecordPersoRespVO> personVOS = staffDutyRecordDao.getPersonList(postKeyId, date);
            JSONArray personArray = new JSONArray();

            for (StaffDutyRecordPersoRespVO vo : personVOS) {
                personArray.add(buildPersonData(vo));
            }

            jsonObject.put(postKeyId, personArray);
        }

        return jsonObject;
    }

    private JSONObject buildPersonData(StaffDutyRecordPersoRespVO vo) {
        JSONObject personObj = new JSONObject();
        personObj.put("policeType", vo.getPoliceType());
        personObj.put("policeId", vo.getPoliceId());
        personObj.put("policeName", vo.getPoliceName());
        personObj.put("postKey", vo.getPostKey());
        personObj.put("photo",vo.getPhoto());
        return personObj;
    }

    private DutyManageVO buildDutyManageVO(String tempId, List<DutyManageHeaderVO> headerList, JSONArray personList) {
        DutyManageVO dutyManageVO = new DutyManageVO();
        dutyManageVO.setTempId(tempId);
        dutyManageVO.setHeaderList(headerList);
        dutyManageVO.setPersonList(personList);
        return dutyManageVO;
    }
    @Override
    public DutyManageVO defaultHeader(Integer templateType) {
        String tempId = staffDutyRecordDao.getTempId(SessionUserUtil.getSessionUser().getOrgCode(), templateType);
        List<DutyManageHeaderVO> headerList = staffDutyRecordDao.getHeaderList(tempId);
        DutyManageVO dutyManageVO = new DutyManageVO();
        dutyManageVO.setTempId(tempId);
        dutyManageVO.setHeaderList(headerList);
        return dutyManageVO;
    }
    @Override
    public List<DutyManageHeaderVO> getHeaderList(String tempId){
        return staffDutyRecordDao.getHeaderList(tempId);
    }
    @Override
    public String getTempId(String orgCode,Integer templateType){
        if (StringUtils.isEmpty(orgCode)) orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        return staffDutyRecordDao.getTempId(SessionUserUtil.getSessionUser().getOrgCode(), templateType);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer add(DutyManageSaveDTO dto) {
        JSONArray personList = dto.getPersonList();
        if (!personList.isEmpty()) {
            for (Object obj : personList){
                Map<String, Object> jsonObject = (Map<String, Object>) obj;
                String date = jsonObject.get("date").toString();
                StaffDutyRecordSaveReqVO recordDTO = new StaffDutyRecordSaveReqVO();
                //recordDTO.setDelFlag(0);
                recordDTO.setTempId(dto.getTempId());
                recordDTO.setDutyDate(StaffDutyDateUtils.stringToDate(date, StaffDutyDateUtils.DATE_PATTERN));
                //先删除之前数据再保存
                staffDutyRecordDao.delRecordByDate(dto.getTempId(), recordDTO.getDutyDate());
                //删除民警信息
                staffDutyRecordDao.delPersonByByRecordId(dto.getTempId(), recordDTO.getDutyDate());

                StaffDutyRecordDO recordEntity = new StaffDutyRecordDO();
                BeanUtils.copyProperties(recordDTO, recordEntity);
                recordEntity.setIsDel(false);
                this.staffDutyRecordDao.insert(recordEntity);
                List<String> postKeyByTempId = staffDutyRecordDao.getPostKeyByTempId(dto.getTempId());
                for (String postKeyId : postKeyByTempId) {
                    List<Map<String, Object>> postKeyList = (List<Map<String, Object>>) jsonObject.get(postKeyId);
                    if (!CollectionUtils.isEmpty(postKeyList)) {
                        for (Map<String, Object> postKey : postKeyList) {
                            if (postKey != null) {
                                StaffDutyRecordPersoSaveReqVO personDTO = new StaffDutyRecordPersoSaveReqVO();
                                Integer policeType = Integer.valueOf(postKey.get("policeType").toString());
                                //if (policeType == 1) {
                                String policeId = postKey.get("policeId") != null ? postKey.get("policeId").toString():"";
                                personDTO.setPoliceId(policeId);
                                //}
                                String policeName = postKey.get("policeName").toString();
                                personDTO.setPoliceName(policeName);
                                personDTO.setPostKey(postKeyId);
                                personDTO.setRecordId(recordEntity.getId());
                                personDTO.setPoliceType(policeType);
                                StaffDutyRecordPersonDO personEntity = new StaffDutyRecordPersonDO();
                                BeanUtils.copyProperties(personDTO, personEntity);
                                this.recordPersonDao.insert(personEntity);
                            }
                        }
                    }

                }
            }
        }
        return 1;
    }

    @Override
    public Integer exportConfigByDutyDate(Date startTime, Date endTime,Integer templateType, HttpServletResponse response) throws IOException {
        //找到排版模板导出
        ClassPathResource classPathResource = new ClassPathResource("xlsx/PB.xlsx");
        InputStream inputStream = classPathResource.getInputStream();
        XSSFWorkbook sheets = new XSSFWorkbook(inputStream);
        XSSFCellStyle cellStyle = sheets.createCellStyle();
        //设置表头和单元格样式
        XSSFFont font = sheets.createFont();
        font.setFontName("宋体");//字体风格
        font.setFontHeightInPoints((short) 16);//字体大小
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//水平居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);//垂直居中
        cellStyle.setWrapText(true);

        //设置日期和正文字体单元格样式
        XSSFCellStyle cellStyle2 = sheets.createCellStyle();
        XSSFFont font2 = sheets.createFont();
        font2.setFontName("宋体");
        font2.setFontHeightInPoints((short) 9);
        cellStyle2.setFont(font2);
        cellStyle2.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle2.setAlignment(HorizontalAlignment.CENTER);

        //设置提醒注意事项样式
        XSSFCellStyle cellStyle3 = sheets.createCellStyle();
        XSSFFont font3 = sheets.createFont();
        font3.setFontName("宋体");
        font3.setBold(true);
        font3.setFontHeightInPoints((short) 12);
        font3.setColor(new XSSFColor(new java.awt.Color(213, 25, 41)));
        cellStyle3.setFont(font3);
        cellStyle3.setFillPattern(FillPatternType.SOLID_FOREGROUND);    //设置填充方案
        cellStyle3.setFillForegroundColor(new XSSFColor(new java.awt.Color(203, 173, 40)));  //设置填充颜色
        cellStyle3.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle3.setAlignment(HorizontalAlignment.CENTER);
        cellStyle3.setWrapText(true);


        //根据获取导出的列表
        DutyManageVO dutyManageVO = defaultHeader(templateType);
        XSSFRow dateRows = null;
        if (dutyManageVO != null) {
            sheets.removeSheetAt(0);//移除默认的sheet1
        }
        int i = 1; //初始表头时间列的位置
        int k = 0; //初始表头值班角色列的位置
        int g = 0; //初始表头值班岗位列的位置

        //导出模板的导入注意事项行
        int tipsRowStart = 4;
        int tipsRowEnd = 10;

        int dateRow = 11;//初始日期内容的列的位置
        List<Integer> indexList = new ArrayList<>(); //角色定位合并集合
        List<Integer> headerIndexList = new ArrayList<>();//岗位定位合并集合
        indexList.add(1); //角色定位初始化
        headerIndexList.add(1);//岗位定位初始化
        XSSFSheet sheet = sheets.createSheet(staffDutyRecordDao.getTempNameById(dutyManageVO.getTempId(), templateType));//创建sheet名字
        sheet.autoSizeColumn(0, true); //设置换行
        XSSFRow rows = sheet.createRow(3); //始表头时间行的位置
        XSSFRow rows1 = sheet.createRow(2);//初始表头值班角色行的位置
        XSSFRow rows2 = sheet.createRow(0);//初始表头值班岗位行的位置
        XSSFRow rowsTips = sheet.createRow(tipsRowStart);//初始表头值班岗位行的位置

        List<DutyManageHeaderVO> headerList = dutyManageVO.getHeaderList();
        for (DutyManageHeaderVO headerVO : headerList) {
            List<DutyManageSubPostVO> subPostList = headerVO.getSubPostList();
            for (DutyManageSubPostVO postVO : subPostList) {
                List<DutyManageSubPostTimeVO> subPostTimeVOS = postVO.getSubPostTimeVOS();
                for (DutyManageSubPostTimeVO timeVO : subPostTimeVOS) {
                    try {
                        //获取排版时间并合并
                        rows.createCell(i).setCellValue(StringUtils.isNotBlank(timeVO.getDutyShift()) ? timeVO.getDutyShift() + "\r\n" + timeVO.getTime() : "" + timeVO.getTime());
                        rows.setHeight((short) 648);
                        XSSFCell cell = rows.getCell(i);
                        cell.setCellStyle(cellStyle);
                        CellRangeAddress cellRangeAddress = new CellRangeAddress(3, 3, i, i + 2);//合并单元格
                        sheet.addMergedRegion(cellRangeAddress);
                        int day = StaffDutyDateUtils.daysBetween(startTime, endTime);
                        for (int m = 0; m <= day; m++) {
                            Date date = StaffDutyDateUtils.addDateDays(startTime, m);//循环拿到间隔的日期
                            String dateStr = StaffDutyDateUtils.format(date, StaffDutyDateUtils.DATE_PATTERN);
                            //插入日期数据
                            if (sheet.getRow(dateRow) == null) {
                                dateRows = sheet.createRow(dateRow);
                            } else {
                                dateRows = sheet.getRow(dateRow);
                            }
                            dateRows.createCell(0).setCellValue(dateStr);
                            sheet.getRow(dateRow).getCell(0).setCellStyle(cellStyle2);
                            CellRangeAddress cellRangeAddress2 = new CellRangeAddress(dateRow, dateRow, i, i + 2);
                            sheet.addMergedRegion(cellRangeAddress2);
                            dateRow += 1;
                        }
                    } catch (ParseException e) {
                        log.info(e.getMessage());
                        throw new RuntimeException(e);
                    }
                    dateRow = 11;//重置日期行  防止数据重复
                    i += 3;//每三列合并
                }
                //角色单元格合并
                indexList.add(i);
                rows1.createCell(indexList.get(k)).setCellValue(postVO.getSubPost());
                XSSFCell cell = rows.getCell(indexList.get(k));
                CellRangeAddress cellRangeAddress = new CellRangeAddress(2, 2, indexList.get(k), i - 1);
                sheet.addMergedRegion(cellRangeAddress);
                cell.setCellStyle(cellStyle);
                sheet.getRow(2).getCell(indexList.get(k)).setCellStyle(cellStyle);
                k += 1;
            }
            //岗位单元格合并
            headerIndexList.add(i);
            rows2.createCell(headerIndexList.get(g)).setCellValue(headerVO.getPost());
            XSSFCell cell = rows2.getCell(headerIndexList.get(g));
            //记录位置 导入校验用
            posList.put(cell.getRowIndex() + cell.getColumnIndex() + "", cell.getStringCellValue());
            CellRangeAddress cellRangeAddress = new CellRangeAddress(0, 1, headerIndexList.get(g), i - 1);
            sheet.addMergedRegion(cellRangeAddress);
            cell.setCellStyle(cellStyle);
            sheet.getRow(0).getCell(headerIndexList.get(g)).setCellStyle(cellStyle);
            g += 1;
        }
        //表头日期合并
        rows2.createCell(0).setCellValue("日期");
        XSSFCell cell = rows2.getCell(0);
        CellRangeAddress cellRangeAddress = new CellRangeAddress(0, 3, 0, 0);
        sheet.addMergedRegion(cellRangeAddress);
        cell.setCellStyle(cellStyle);
        sheet.getRow(0).getCell(0).setCellStyle(cellStyle);

        //插入注意事项
        rowsTips.createCell(0).setCellValue("注意事项： " + "\r\n" +
                "1.使用此模板导入数据必须确保当前使用的模板和导入一致 请勿随便改动模板格式 " + "\r\n" +
                "2.值班姓名用英文逗号隔开填写 如：张三,李四 " + "\r\n" +
                "3.姓名如果填写错误则按照非民警数据导入 " + "\r\n" +
                "4.导入值班人员数据如果存在岗位相同并且名字相同，这些相同的数据都会按照非民警数据导入");

        XSSFCell cellTips = rowsTips.getCell(0);
        CellRangeAddress cellRangeAddressTip = new CellRangeAddress(tipsRowStart, tipsRowEnd, 0, i - 1);
        sheet.addMergedRegion(cellRangeAddressTip);
        cellTips.setCellStyle(cellStyle3);
        sheet.getRow(tipsRowStart).getCell(0).setCellStyle(cellStyle3);

        //执行导出
        //ExportExcelUtil.setExportHTTPResponse(sheets, "PBGL", response);
        sheets.close();
        return 1;
    }

    public Integer initImportConfigMap(Integer templateType) {
        //出初始化排版模板导入的模板位置
        posList.clear();
        DutyManageVO dutyManageVO = defaultHeader(templateType);
        int i = 1; //初始表头时间列的位置
        int k = 0; //初始表头值班角色列的位置
        int g = 0; //初始表头值班岗位列的位置


        List<Integer> indexList = new ArrayList<>(); //角色定位合并集合
        List<Integer> headerIndexList = new ArrayList<>();//岗位定位合并集合
        indexList.add(1); //角色定位初始化
        headerIndexList.add(1);//岗位定位初始化

        int rows = 2; //始表头时间行的位置
        int rows1 = 1;//初始表头值班角色行的位置
        int rows2 = 0;//初始表头值班岗位行的位置

        posList.put("tempId", dutyManageVO.getTempId());
        List<DutyManageHeaderVO> headerList = dutyManageVO.getHeaderList();
        for (DutyManageHeaderVO headerVO : headerList) {
            List<DutyManageSubPostVO> subPostList = headerVO.getSubPostList();
            for (DutyManageSubPostVO postVO : subPostList) {
                List<DutyManageSubPostTimeVO> subPostTimeVOS = postVO.getSubPostTimeVOS();
                for (DutyManageSubPostTimeVO timeVO : subPostTimeVOS) {
                    try {
                        posList.put(rows + "-" + i + "", StringUtils.isNotBlank(timeVO.getDutyShift()) ? timeVO.getDutyShift() + "\r\n" + timeVO.getTime() : "" + timeVO.getTime());
                        posList.put(rows + "-" + i + "key", timeVO.getPostKey());
                    } catch (Exception e) {
                        log.info(e.getMessage());
                        throw new RuntimeException(e);
                    }
                    i += 3;//
                }
                //角色单元格合并
                indexList.add(i);
                posList.put(rows1 + "-" + indexList.get(k) + "", postVO.getSubPost());
                posList.put(rows1 + "-" + indexList.get(k) + "post_id", postVO.getDutyPostId());
                k += 1;
            }
            //岗位单元格合并
            headerIndexList.add(i);
            //记录位置 导入校验用
            posList.put(rows2 + "-" + headerIndexList.get(g) + "", headerVO.getPost());
            g += 1;
        }
        //表头日期合并
        posList.put(rows2 + "-" + "0", "日期");
        return 1;
    }

    @Override
    public DutyManageVO importConfigByDutyDate(MultipartFile multipartFile,Integer templateType) throws IOException {
        DutyManageVO vo = new DutyManageVO();
        JSONArray personList = new JSONArray();
        String tempId = staffDutyRecordDao.getTempId(SessionUserUtil.getSessionUser().getOrgCode(), templateType);
        Integer hasRoleByTempId = staffDutyRecordDao.getHasRoleByTempId(tempId);
        Integer headSizeMax = 4;
        Integer num = 2;
        if(hasRoleByTempId==0){
           headSizeMax=3;
           num = 1;
        }
        vo.setTempId(tempId);
        //获取表头数据进行校验
        initImportConfigMap(templateType);
        System.out.println(posList);
        int headSize = 0;
        int countNum = 0;
        //待处理
        List<Object> objects = null;//ExcelUtil.readMoreThan1000Row(multipartFile.getInputStream());
        List<Map<String, Object>> maps = listTransToMap(objects);

        Date dateCheck = null;
        for (Map<String, Object> map : maps) {
            //校验表头部份
            Date date = null;
            Object elementData = map.get("elementData");
            JSONArray jsonArray = (JSONArray) JSON.toJSON(elementData);
            Iterator<Object> iterator = jsonArray.stream().iterator();
            if (headSize <= 2) {
                try {
                    int colNum = 0;
                    while (iterator.hasNext()) {
                        String key = headSize + "-" + colNum;
                        Object next = iterator.next();
                        Object o = posList.get(key);
                        if (o != null && next!=null && StringUtils.isNotBlank(o.toString())) {
                            String str = next.toString().replace("_x000D_", "\r");
                            if (o.toString().equals(str)) {
                                log.info("成功");
                            } else {
                                vo.setCode(-1);
                                return vo;
                            }
                        }
                        colNum += 1;
                    }
                } catch (Exception e) {
                    log.info(e.getMessage());
                    throw new RuntimeException(e);
                }
            } else if (headSize >= headSizeMax) { //校验日期和数据部分
                int colNum = 0;
                JSONObject jsonObject = new JSONObject();
                List<String> postKeyByTempId = staffDutyRecordDao.getPostKeyByTempId(tempId);
                ArrayList<String> strings = new ArrayList<>();
                while (iterator.hasNext()) {
                    Object next = iterator.next();
                    if (colNum == 0) {
                        if (next != null && StringUtils.isNotBlank(next.toString())) {
                            String dateStr = next.toString();
                            if (dateStr.contains(":")) {
                                date = StaffDutyDateUtils.stringToDate(dateStr, StaffDutyDateUtils.DATE_TIME_PATTERN);
                                date = StaffDutyDateUtils.stringToDate(StaffDutyDateUtils.format(date, StaffDutyDateUtils.DATE_PATTERN), StaffDutyDateUtils.DATE_PATTERN);
                            } else {
                                date = StaffDutyDateUtils.stringToDate(dateStr, StaffDutyDateUtils.DATE_PATTERN);
                            }
                            List<Integer> checkRecord = staffDutyRecordDao.checkRecordByDate(date, tempId);
                            if (!checkRecord.isEmpty()) {
                                vo.setIsCover(true);
                            }
                            if (dateCheck != null) {//日期连续性校验
                                try {
                                    int i = StaffDutyDateUtils.daysBetween(dateCheck, date);
                                    if (i > 1) {
                                        //日期小于当前时间
                                        vo.setCode(-4);
                                        return vo;
                                    }
                                } catch (ParseException e) {
                                    log.info(e.getMessage());
                                    throw new RuntimeException(e);
                                }
                            }
                            jsonObject.put("date", dateStr);
                            for (String postKeyId : postKeyByTempId) {
                                JSONArray personArray = new JSONArray();
//                                JSONObject personObj = new JSONObject();
//                                personArray.add(personObj);
                                jsonObject.put(postKeyId, personArray);
                            }
                            dateCheck = date;
                            //清除数据
//                            staffDutyRecordDao.delRecordByDate(tempId, date);
//                            staffDutyRecordDao.delPersonByByRecordId(tempId, date);
                            if (date.before(new Date()) && !StaffDutyDateUtils.format(date, StaffDutyDateUtils.DATE_PATTERN).equals(StaffDutyDateUtils.format(new Date(), StaffDutyDateUtils.DATE_PATTERN))) {
                                //日期小于当前时间
                                vo.setCode(-2);
                                return vo;
                            }
                        } else {//日期不能为空
                            vo.setCode(-3);
                            return vo;
                        }
                    } else {
                        if (next != null && StringUtils.isNotBlank(next.toString())) {
                            String policeName = next.toString();
                            String postKey = (headSize - countNum) - num + "-" + colNum;//获取岗位key的key
                            Object o2 = posList.get(postKey + "key");
                            if (o2 != null && date != null) {
                                //根据角色唯一key拿到岗位编号
                                String postId = staffDutyRecordDao.getPostIdByPostKey(o2.toString());
                                String[] split = policeName.split(",");
                                //插入民警数据
                                for (String postKeyId : postKeyByTempId) {
                                    if (!strings.contains(postKeyId)) {
                                        List<String> policeIdByName = new ArrayList<>();
                                        JSONArray personArray = new JSONArray();
                                        for (String name : split) {
                                            if (o2.toString().equals(postKeyId)) {
                                                if (StringUtils.isNotBlank(postId)) {//存在岗位关联
                                                    //policeIdByName = staffDutyRecordDao.getPoliceIdByNameAndPostId(name, postId, SessionUserUtil.getSessionUser().getOrgCode());
                                                } else {//不存在岗位关联
                                                    //policeIdByName = staffDutyRecordDao.getPoliceIdByName(name, SessionUserUtil.getSessionUser().getOrgCode());
                                                }
                                                JSONObject personObj = new JSONObject();
                                                personObj.put("policeType", policeIdByName.size() > 1 || policeIdByName.size() == 0 ? 2 : 1);
                                                personObj.put("policeId", policeIdByName.size() > 1 || policeIdByName.size() == 0 ? null : policeIdByName.get(0));
                                                personObj.put("policeName", name);
                                                personObj.put("postKey", o2.toString());
                                                personArray.add(personObj);
                                                strings.add(o2.toString());
                                            }

                                        }
                                        jsonObject.put(postKeyId, personArray);
                                    }
                                }
                            }
                        }

                    }
                    colNum += 1;
                }
                personList.add(jsonObject);
                countNum += 1;
            }
            headSize += 1;
        }
        vo.setPersonList(personList);
        vo.setCode(1);
        vo.setHeaderList(defaultHeader(templateType).getHeaderList());
        return vo;
    }

    @Override
    public List<StaffDutyRecordPersoRespVO> getPersonList(String postKeyId, String tempDate) {
        return staffDutyRecordDao.getPersonList(postKeyId, tempDate);
    }

    @Override
    public List<String> getPostKeyByTempId(String tempId) {
        return staffDutyRecordDao.getPostKeyByTempId(tempId);
    }

    @Override
    public Map<String, Object> getTemIdAndDateByDate(String format, String orgCode, Integer code) {
        return staffDutyRecordDao.getTemIdAndDateByDate(format, orgCode, code);
    }


    /**
     * 将List<Object>转换为List<Map<String,Object>>
     *
     * @param list
     * @return
     */
    private List<Map<String, Object>> listTransToMap(List<Object> list) {
        List<Map<String, Object>> maps = new ArrayList<Map<String, Object>>();
        for (Object obj : list) {
            Class c = obj.getClass();
            Field[] f = c.getDeclaredFields();
            Map<String, Object> map = new HashMap<String, Object>();
            for (Field fie : f) {
                try {
                    //反射知识
                    fie.setAccessible(true);//取消语言访问检查
                    map.put(fie.getName(), fie.get(obj));//获取私有变量值
                } catch (IllegalArgumentException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
            //获取父类的私有属性
            for (Field fie : c.getSuperclass().getDeclaredFields()) {
                try {
                    fie.setAccessible(true);//取消语言访问检查
                    map.put(fie.getName(), fie.get(obj));//获取私有变量值
                } catch (IllegalArgumentException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
            maps.add(map);
        }
        return maps;
    }


}
