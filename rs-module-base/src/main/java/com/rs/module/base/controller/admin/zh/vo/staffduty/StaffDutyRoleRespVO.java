package com.rs.module.base.controller.admin.zh.vo.staffduty;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

@Data
@ApiModel(description = "值班模板关联角色表")
public class StaffDutyRoleRespVO extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "duty_post关联表的主键",hidden = true)
    private String postId;

    @ApiModelProperty(value = "值班角色")
    private String dutyRoleName;

    @ApiModelProperty(value = "岗位名称")
    private String postName;

    @ApiModelProperty(value = "岗位编号")
    private String dutyPostId;

    @ApiModelProperty(value = "缺勤通知人sfzh")
    private String absentNotifierSfzh;
    @ApiModelProperty(value = "缺勤通知人")
    private String absentNotifierName;
    @ApiModelProperty(value = "模板时间列表")
    private List<StaffDutyRoleTimeRespVO> roleTimeDTOS;

}
