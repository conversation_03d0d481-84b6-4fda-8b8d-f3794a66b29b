package com.rs.module.base.controller.admin.pm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-监管管理-区域新增/修改 Request VO")
@Data
public class AreaSaveReqVO {

    @ApiModelProperty("主键")
    private String id;

//    @ApiModelProperty("是否删除(0:否,1:是)")
//    private Integer isDel;
//
//    @ApiModelProperty("添加时间")
//    private Date addTime;
//
//    @ApiModelProperty("添加人")
//    private String addUser;
//
//    @ApiModelProperty("更新人")
//    private String updateUser;
//
//    @ApiModelProperty("所属省级代码")
//    private String proCode;
//
//    @ApiModelProperty("所属省级名称")
//    private String proName;
//
//    @ApiModelProperty("所属市级代码")
//    private String cityCode;
//
//    @ApiModelProperty("所属市级名称")
//    private String cityName;
//
//    @ApiModelProperty("区域代码")
//    private String regCode;
//
//    @ApiModelProperty("区域名称")
//    private String regName;
//
//    @ApiModelProperty("机构编号")
//    private String orgCode;
//
//    @ApiModelProperty("机构名称")
//    private String orgName;

    @ApiModelProperty("监所区域名称")
    private String areaName;

    @ApiModelProperty("父节点ID")
    private String parentId;

//    @ApiModelProperty("父区域的路径信息,#分隔")
//    private String allParentId;

    @ApiModelProperty("排序")
    private Integer orderId;

//    @ApiModelProperty("区域类型")
//    private String areaType;

//    @ApiModelProperty("区域编码")
//    private String areaCode;

    @ApiModelProperty("所在层级")
    private Integer level;

    @ApiModelProperty("监室信息，区域类型为监室时传")
    private AreaPrisonRoomSaveReqDTO areaPrisonRoomSaveDto;

    @ApiModelProperty("主协管信息")
    private List<AreaRelatedWarderReqVO> areaRelatedWarderReqVOList;

}
