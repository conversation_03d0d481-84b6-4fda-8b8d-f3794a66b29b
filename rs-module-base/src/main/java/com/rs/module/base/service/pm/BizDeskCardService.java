package com.rs.module.base.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.base.controller.admin.pm.vo.BizDeskCardSaveReqVO;
import com.rs.module.base.entity.pm.BizDeskCardDO;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 实战平台-监管管理-业务总台卡片配置 Service 接口
 *
 * <AUTHOR>
 */
public interface BizDeskCardService extends IBaseService<BizDeskCardDO>{

    /**
     * 创建实战平台-监管管理-业务总台卡片配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    BizDeskCardDO createBizDeskCard(@Valid BizDeskCardSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-业务总台卡片配置
     *
     * @param updateReqVO 更新信息
     */
    BizDeskCardDO updateBizDeskCard(@Valid BizDeskCardSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-业务总台卡片配置
     *
     * @param id 编号
     */
    void deleteBizDeskCard(String id);

    /**
     * 获得实战平台-监管管理-业务总台卡片配置
     *
     * @param id 编号
     * @return 实战平台-监管管理-业务总台卡片配置
     */
    BizDeskCardDO getBizDeskCard(String id);

}
