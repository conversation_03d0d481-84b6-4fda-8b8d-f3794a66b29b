package com.rs.module.base.entity.pm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 实战平台-监管管理-业务快捷访问入口类型配置 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_biz_quick_access_type")
@KeySequence("acp_pm_biz_quick_access_type_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_biz_quick_access_type")
public class BizQuickAccessTypeDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 业务快捷访问入口类型名称
     */
    private String name;
    /**
     * 业务快捷访问入口类型标识
     */
    private String mark;
    /**
     * 备注
     */
    private String remark;
    /**
     * 排序Id
     */
    private Integer orderId;

}
