package com.rs.module.base.controller.admin.pm.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-监管管理-监管人员标签分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrisonerTagPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("标签id")
    private String tagCode;

    @ApiModelProperty("标签名称")
    private String tagName;

    @ApiModelProperty("人员标签类型（字典：ZD_JGRYBQLX）")
    private String tagType;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
