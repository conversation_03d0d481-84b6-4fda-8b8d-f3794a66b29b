package com.rs.module.base.tag.annotation;

import com.rs.module.base.tag.enums.ConditionCombineType;
import com.rs.module.base.tag.enums.TagConditionType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标签条件注解
 * 用于配置标签规则的条件
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface TagCondition {
    /**
     * 条件类型
     */
    TagConditionType type();

    /**
     * 比较值
     * 如果为空，则使用TagEnum中的compareValue
     */
    String compareValue() default "";

    /**
     * 是否反转条件
     */
    boolean reverse() default false;

    /**
     * SpEL表达式
     * 用于复杂条件判断
     */
    String spelExpression() default "";

    /**
     * 自定义服务方法
     * 用于调用外部服务进行条件判断
     */
    String serviceMethod() default "";

    /**
     * 自定义服务类
     * 用于指定serviceMethod所在的类
     */
    Class<?> serviceClass() default void.class;

    /**
     * 条件组合类型
     * 用于指定多个条件的组合方式
     */
    ConditionCombineType combineType() default ConditionCombineType.AND;

    /**
     * 值列表
     * 用于IN/NOT_IN类型的条件
     */
    String[] values() default {};

    /**
     * 条件组
     * 用于配置多个条件的组合
     */
    TagConditionGroup[] groups() default {};
}
