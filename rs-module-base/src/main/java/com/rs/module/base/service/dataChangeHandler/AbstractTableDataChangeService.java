package com.rs.module.base.service.dataChangeHandler;

import com.rs.module.base.dto.pm.TableDataChangeDTO;
import org.springframework.context.ApplicationListener;

import java.util.List;

public abstract class AbstractTableDataChangeService implements ApplicationListener<TableDataChangeEvent> {

    @Override
    public void onApplicationEvent(TableDataChangeEvent event) {
        Object source = event.getSource();
        if (!(source instanceof TableDataChangeDTO)) {
            return;
        }
        TableDataChangeDTO data = (TableDataChangeDTO) source;
        if (data != null && this.listenTableList().contains(data.getTableName())) {
            this.handler(data);
        }
    }

    /**
     * 需要监听的表名集合
     *
     * @return
     */
    public abstract List<String> listenTableList();

    /**
     * 数据处理
     *
     * @param data
     */
    public abstract void handler(TableDataChangeDTO data);
}
