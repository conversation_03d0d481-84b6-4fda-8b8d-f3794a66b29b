package com.rs.module.base.dao.pm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.base.entity.pm.BizBrowsingPermissionDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 实战平台-监管管理-业务浏览权限配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BizBrowsingPermissionDao extends IBaseDao<BizBrowsingPermissionDO> {

    default List<BizBrowsingPermissionDO> getByBizIds(List<String> bizIds) {
        return this.selectList(new LambdaQueryWrapper<BizBrowsingPermissionDO>()
                .in(BizBrowsingPermissionDO::getBizId, bizIds));
    }

    default List<BizBrowsingPermissionDO> getByMarks(List<String> marks) {
        return this.selectList(new LambdaQueryWrapper<BizBrowsingPermissionDO>()
                .in(BizBrowsingPermissionDO::getMark, marks));
    }

}
