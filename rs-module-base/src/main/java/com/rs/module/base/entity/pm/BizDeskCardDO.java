package com.rs.module.base.entity.pm;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-监管管理-业务总台卡片配置 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_biz_desk_card")
@KeySequence("acp_pm_biz_desk_card_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_biz_desk_card")
public class BizDeskCardDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 业务总台ID
     */
    private String bizDeskId;
    /**
     * 总台标识
     */
    private String bizDeskMark;
    /**
     * 卡片名称
     */
    private String name;
    /**
     * 卡片地址
     */
    private String cardUrl;
    /**
     * 地址类型(1:外部0:内部)
     */
    private String urlType;
    /**
     * 加载类型
     */
    private String loaderType;
    /**
     * 容器标识（微前端模式使用）
     */
    private String containerMark;
    /**
     * 微前端地址
     */
    private String microWebUrl;
    /**
     * 路径激活规则
     */
    private String activeRule;
    /**
     * 是否启动浏览权限配置
     */
    private Short isEnableBrowsingPermission;
    /**
     * 是否启用
     */
    private Short isEnable;
    /**
     * 排序Id
     */
    private Integer orderId;
    /**
     * 备注
     */
    private String remark;

}
