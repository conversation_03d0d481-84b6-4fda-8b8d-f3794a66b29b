package com.rs.module.base.service.ry.fill.handle;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.ry.fill.handle.base.RyxxFillHandler;
import com.rs.module.ihc.dao.ipm.appointment.IhcsInternalMedicalAppointmentDao;
import com.rs.module.ihc.entity.ipm.appointment.IhcsInternalMedicalAppointmentDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @ClassName YyshHandle
 * @Description 预约审核
 * <AUTHOR>
 * @Date 2025/6/9 23:13
 * @Version 1.0
 */
@Component
public class YyshHandle extends RyxxFillHandler {
    @Autowired
    public IhcsInternalMedicalAppointmentDao ihcsInternalMedicalAppointmentDao;

    @Override
    public void handle(PrisonerVwRespVO respVO) {
        /**
         * 查询过敏史、当日报病次数
         */
        int medicalCount = ihcsInternalMedicalAppointmentDao.selectCount(new LambdaQueryWrapper<IhcsInternalMedicalAppointmentDO>()
                .eq(IhcsInternalMedicalAppointmentDO::getSupervisedUserCode, respVO.getJgrybm())
                .ge(IhcsInternalMedicalAppointmentDO::getAddTime, DateUtil.beginOfDay(new Date()))
                .le(IhcsInternalMedicalAppointmentDO::getAddTime, DateUtil.endOfDay(new Date())));
        respVO.setNumberOfReportedIllnesses(medicalCount);

        respVO.setHasAllergyHistory(false);
    }

}
