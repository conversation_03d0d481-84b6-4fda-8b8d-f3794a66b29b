package com.rs.module.base.controller.admin.pm.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-监管管理-风险评估列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RiskAssmtListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监室号")
    private String roomId;

    @ApiModelProperty("评估类型")
    private String riskType;

    @ApiModelProperty("评估风险等级")
    private String riskLevel;

    @ApiModelProperty("规范指标")
    private String standardQuota;

    @ApiModelProperty("调整理由")
    private String adjustReason;

    @ApiModelProperty("列控建议")
    private String controlSuggest;

    @ApiModelProperty("评估人")
    private String assmtUserId;

    @ApiModelProperty("评估人姓名")
    private String assmtUserName;

    @ApiModelProperty("评估呈批日期")
    private Date[] assmtTime;

    @ApiModelProperty("领导意见")
    private String leaderOpinion;

    @ApiModelProperty("领导意见内容")
    private String leaderOpinionContent;

    @ApiModelProperty("领导签名")
    private String leaderAutograph;

    @ApiModelProperty("领导签名时间")
    private Date[] leaderAutographTime;

    @ApiModelProperty("思想情绪")
    private String emotion;

    @ApiModelProperty("心里情况")
    private String psychology;

    @ApiModelProperty("经办人")
    private String operateUserId;

    @ApiModelProperty("operate_user_name")
    private String operateUserName;

    @ApiModelProperty("经办时间")
    private Date[] operateTime;

    @ApiModelProperty("原风险等级")
    private String oldRiskLevel;

    @ApiModelProperty("评估状态：0:待评估 1:待审批 2:审批完成")
    private Integer status;

    @ApiModelProperty("评估原因。字典：RISK_ASSESSMENT_CAUSE")
    private String assmtCause;

    @ApiModelProperty("具体评估理由")
    private String specificAdjustReason;

}
