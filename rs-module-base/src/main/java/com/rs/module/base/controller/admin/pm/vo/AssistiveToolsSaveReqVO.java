package com.rs.module.base.controller.admin.pm.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel(description = "管理后台 - 实战平台-监管管理-辅助工具新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AssistiveToolsSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("文件名称")
    @NotEmpty(message = "文件名称不能为空")
    private String name;

    @ApiModelProperty("文件存储路径")
    @NotEmpty(message = "文件存储路径不能为空")
    private String filePath;

    @ApiModelProperty("文件大小")
    @NotNull(message = "文件大小不能为空")
    private Integer fileSize;

    @ApiModelProperty("文件类型")
    private String fileType;

    @ApiModelProperty("是否启用")
    @NotNull(message = "是否启用不能为空")
    private Short isEnable;

    @ApiModelProperty("排序Id")
    @NotNull(message = "排序Id不能为空")
    private Integer orderId;

    @ApiModelProperty("备注")
    private String remark;

}
