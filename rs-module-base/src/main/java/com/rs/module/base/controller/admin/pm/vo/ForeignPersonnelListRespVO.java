package com.rs.module.base.controller.admin.pm.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 外来人员列表返回VO
 * <AUTHOR>
 * @Date 2025/7/18 20:39
 */
@ApiModel(value = "外来人员列表返回VO")
@Data
public class ForeignPersonnelListRespVO implements TransPojo {

    @ApiModelProperty(value = "主键ID")
    private String id;
    @ApiModelProperty(value = "添加时间")
    private Date addTime;
    @ApiModelProperty(value = "机构编号")
    private String orgCode;
    @ApiModelProperty(value = "警号")
    private String jh;
    @ApiModelProperty(value = "姓名")
    private String xm;
    @ApiModelProperty(value = "证件类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_ZJLX")
    private String zjlx;
    @ApiModelProperty(value = "证件号码")
    private String zjhm;
    @ApiModelProperty(value = "性别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XB")
    private String xb;
    @ApiModelProperty(value = "照片路径")
    private String zpUrl;
    @ApiModelProperty(value = "外来人员类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WLRYLX")
    private String ryType;

}
