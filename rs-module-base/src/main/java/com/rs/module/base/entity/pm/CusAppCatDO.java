package com.rs.module.base.entity.pm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.Data;

/**
 * 自定义应用分类 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_cus_app_cat")
@Data
public class CusAppCatDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 系统ID
     */
    private String systemId;
    /**
     * 分类名称
     */
    private String flmc;
    /**
     * 排序
     */
    private Integer orderId;
    /**
     * 分类标识
     */
    private String mark;
    /**
     * 前缀地址
     */
    private String preUrl;
}
