package com.rs.module.base.tag.annotation;

import com.rs.module.base.tag.enums.TagEnum;
import com.rs.module.base.tag.enums.TagScenarioType;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Target({ ElementType.FIELD })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TagRule {
    /**
     * 标签枚举
     */
    TagEnum tag();

    /**
     * 标签场景类型
     * 指定标签规则的操作场景（添加、删除、更新等）
     */
    TagScenarioType scenario() default TagScenarioType.ADD;

    /**
     * 标签条件
     */
    TagCondition[] conditions();

    /**
     * 规则优先级，数字越小优先级越高
     */
    int priority() default 0;

    /**
     * 是否互斥，互斥的规则只会生效一个
     */
    boolean exclusive() default false;

    /**
     * 标签有效期（天），0表示永久有效
     */
    int validDays() default 0;

    /**
     * 标签自动失效时间
     */
    String expireTime() default "";

    /**
     * 依赖的标签编码
     */
    String[] dependsOn() default {};

    /**
     * 是否强制依赖
     */
    boolean forceDependency() default false;

    /**
     * 删除场景下的目标标签
     * 当scenario为REMOVE或REPLACE时，指定要删除的标签
     * 如果为空，则删除tag()指定的标签
     */
    TagEnum[] removeTargets() default {};

    /**
     * 删除场景的描述信息
     */
    String removeDescription() default "";
}
