package com.rs.module.base.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.rs.module.base.enums.BusTypeEnum;

/**
 * 业务轨迹注解类
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface BusTrace {

	//监管人员编码
	String jgrybm() default "";
	
	//监所代码
	String orgCode() default "";
	
	//操作用户
	String operateUser() default "";
	
	//业务类型
	BusTypeEnum busType();
	
	//轨迹内容
	String content();
	
	//记录条件
	String condition() default "true";
	
	//业务主键
	String businessId() default "";
	
	//监室Id
	String roomId() default "";
}
