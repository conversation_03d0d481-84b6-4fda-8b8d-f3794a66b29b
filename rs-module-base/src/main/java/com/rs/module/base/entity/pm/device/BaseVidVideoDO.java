package com.rs.module.base.entity.pm.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * base_vid_video 实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date: 2022-05-10
 */
@Data
@TableName("acp_pm_vid_video")
public class BaseVidVideoDO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @TableId(type = IdType.UUID)
    private String id;
    /**
     * 设备编码
     */
    private String deviceCode;
    /**
     * 视频联网节点编码
     */
    private String treeCode;

    /***
     * 监所编号
     */
    private String prisonId;
}
