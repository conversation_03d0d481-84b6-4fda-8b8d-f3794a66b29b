package com.rs.module.base.service.pm;

import java.util.List;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.base.controller.admin.pm.vo.AreaInfoRespVO;
import com.rs.module.base.controller.admin.pm.vo.AreaListReqVO;
import com.rs.module.base.controller.admin.pm.vo.AreaListRespVO;
import com.rs.module.base.controller.admin.pm.vo.AreaPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.AreaSaveReqDTO;
import com.rs.module.base.controller.admin.pm.vo.AreaSaveReqVO;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.base.entity.pm.AreaData;
import com.rs.module.base.enums.AreaTypeEnum;

import cn.hutool.core.lang.tree.Tree;

/**
 * 实战平台-监管管理-区域 Service 接口
 *
 * <AUTHOR>
 */
public interface AreaService extends IBaseService<AreaDO> {


    List<AreaListRespVO> getAreaListByOrgCode(String orgCode, String areaType);


    List<Tree<String>> getAreaListByOrgCodeAndAreaType(String orgCode, AreaTypeEnum areaType);

    /**
     * 创建实战平台-监管管理-区域
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createArea(@Valid AreaSaveReqDTO createReqVO);

    /**
     * 获取最大编码
     *
     * @param str
     * @return
     */
    String getMaxIdByAreaType(String str);

    /**
     * 更新实战平台-监管管理-区域
     *
     * @param updateReqVO 更新信息
     */
    void updateArea(@Valid AreaSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-区域
     *
     * @param id 编号
     */
    void deleteArea(String id);

    /**
     * 获得实战平台-监管管理-区域
     *
     * @param id 编号
     * @return 实战平台-监管管理-区域
     */
    AreaDO getArea(String id);

    /**
     * 获得实战平台-监管管理-区域列表
     *
     * @param orgCode  监所编号
     * @param areaType 区域类型
     * @return
     */
    List<AreaInfoRespVO> getAreaByOrgCode(String orgCode, String areaType);
    AreaDO getPrisonByOrgCode(String orgCode);

    /**
     * 获得实战平台-监管管理-区域分页
     *
     * @param pageReqVO 分页查询
     * @return 实战平台-监管管理-区域分页
     */
    PageResult<AreaDO> getAreaPage(AreaPageReqVO pageReqVO);

    /**
     * 获得实战平台-监管管理-区域列表
     *
     * @param listReqVO 查询条件
     * @return 实战平台-监管管理-区域列表
     */
    List<AreaDO> getAreaList(AreaListReqVO listReqVO);

    List<Tree<String>> getAreaTree(AreaListReqVO listReqVO);

    List<AreaInfoRespVO> getChildrenAreas(String orgCode, String parentId, String areaName, String areaCode, String areaType);

    PageResult<AreaDO> getChildrenAreaPage(String orgCode, String id, String areaName, String areaCode, String areaType, Integer pageNo, Integer pageSize);

    AreaInfoRespVO getAreaDetail(String id);

    void dealAreaData(List<AreaData> areaDataList);

    AreaDO getPrisonByRoomId(String roomId);

    /**
     * 监所机构树
     * <AUTHOR>
     * @date 2025/6/13 9:54
     * @param [listReqVO]
     * @return java.util.List<cn.hutool.core.lang.tree.Tree<java.lang.String>>
     */
    List<Tree<String>> getAreaCameraTree(AreaListReqVO listReqVO);
    
    /**
     * 获取子区域Id
     * @param areaId String 区域Id
     * @return List<String>
     */
    public List<String> getChildAreaId(String areaId);
    
    /**
     * 获取所有子区域Id
     * @param areaId String 区域Id
     * @return List<String>
     */
    public List<String> getAllChildAreaIds(String areaId);
}
