package com.rs.module.base.controller.admin.zh.vo.staffduty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@ApiModel("排版管理-岗位部分")
@Data
public class DutyManageSubPostDTO {
    @ApiModelProperty(value = "值班角色")
    private String subPost;

    @ApiModelProperty(value = "值班时间列表")
    @NotEmpty(message = "值班时间列表 不能为空")
    List<DutyManageSubPostTimeDTO> subPostTimeDTOS;

}
