package com.rs.module.base.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.base.controller.admin.pm.vo.PrisonerTagListReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerTagPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerTagSaveReqVO;
import com.rs.module.base.entity.pm.PrisonerTagDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-监管管理-监管人员标签 Service 接口
 *
 * <AUTHOR>
 */
public interface PrisonerTagService extends IBaseService<PrisonerTagDO>{

    /**
     * 创建实战平台-监管管理-监管人员标签
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrisonerTag(@Valid PrisonerTagSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-监管人员标签
     *
     * @param updateReqVO 更新信息
     */
    void updatePrisonerTag(@Valid PrisonerTagSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-监管人员标签
     *
     * @param id 编号
     */
    void deletePrisonerTag(String id);

    /**
     * 获得实战平台-监管管理-监管人员标签
     *
     * @param id 编号
     * @return 实战平台-监管管理-监管人员标签
     */
    PrisonerTagDO getPrisonerTag(String id);

    /**
    * 获得实战平台-监管管理-监管人员标签分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-监管管理-监管人员标签分页
    */
    PageResult<PrisonerTagDO> getPrisonerTagPage(PrisonerTagPageReqVO pageReqVO);

    /**
    * 获得实战平台-监管管理-监管人员标签列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-监管管理-监管人员标签列表
    */
    List<PrisonerTagDO> getPrisonerTagList(PrisonerTagListReqVO listReqVO);


}
