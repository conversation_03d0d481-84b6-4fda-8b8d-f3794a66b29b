package com.rs.module.base.service.ry.fill.handle;

import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.ry.fill.handle.base.RyxxFillHandler;
import com.rs.module.ihc.service.ipm.VisitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName RestraintsHandle
 * @Description 戒具
 * <AUTHOR>
 * @Date 2025/6/9 23:13
 * @Version 1.0
 */
@Component
public class RestraintsHandle extends RyxxFillHandler {
    @Autowired
    public VisitService visitService;


    @Override
    public void handle(PrisonerVwRespVO respVO) {
        respVO.setHaveRestraints(visitService.getHaveRestraints(respVO.getJgrybm()));
    }

}
