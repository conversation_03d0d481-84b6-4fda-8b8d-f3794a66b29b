package com.rs.module.base.entity.zh;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@TableName("acp_zh_staff_duty_record_person")
@KeySequence("acp_zh_staff_duty_record_person_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_staff_duty_record_person")
public class StaffDutyRecordPersonDO extends BaseDO {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * acp_zh_staff_duty_record关联的id
     */
    private String recordId;
    /**
     * 民警编号
     */
    private String policeId;
    /**
     * 民警姓名
     */
    private String policeName;
    /**
     * 民警类型 1-民警  2-非民警
     */
    private Integer policeType;
    /**
     * 岗位唯一关联key
     */
    private String postKey;

    /**
     * 是否缺勤 0 否,1 是
     */
    private String isAbsent;

    /**
     * 缺勤原因
     */
    private String absentReason;
}
