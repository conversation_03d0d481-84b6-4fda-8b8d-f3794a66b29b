package com.rs.module.base.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.base.controller.admin.pm.vo.AssistiveToolsSaveReqVO;
import com.rs.module.base.entity.pm.AssistiveToolsDO;

import javax.validation.Valid;

/**
 * 实战平台-监管管理-辅助工具 Service 接口
 *
 * <AUTHOR>
 */
public interface AssistiveToolsService extends IBaseService<AssistiveToolsDO>{

    /**
     * 创建实战平台-监管管理-辅助工具
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createAssistiveTools(@Valid AssistiveToolsSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-辅助工具
     *
     * @param updateReqVO 更新信息
     */
    void updateAssistiveTools(@Valid AssistiveToolsSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-辅助工具
     *
     * @param id 编号
     */
    void deleteAssistiveTools(String id);

    /**
     * 获得实战平台-监管管理-辅助工具
     *
     * @param id 编号
     * @return 实战平台-监管管理-辅助工具
     */
    AssistiveToolsDO getAssistiveTools(String id);


}
