package com.rs.module.base.service.pm;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.AssistiveToolsSaveReqVO;
import com.rs.module.base.dao.pm.AssistiveToolsDao;
import com.rs.module.base.entity.pm.AssistiveToolsDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


/**
 * 实战平台-监管管理-辅助工具 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AssistiveToolsServiceImpl extends BaseServiceImpl<AssistiveToolsDao, AssistiveToolsDO> implements AssistiveToolsService {

    @Resource
    private AssistiveToolsDao assistiveToolsDao;

    @Override
    public String createAssistiveTools(AssistiveToolsSaveReqVO createReqVO) {
        // 插入
        AssistiveToolsDO assistiveTools = BeanUtils.toBean(createReqVO, AssistiveToolsDO.class);
        assistiveToolsDao.insert(assistiveTools);
        // 返回
        return assistiveTools.getId();
    }

    @Override
    public void updateAssistiveTools(AssistiveToolsSaveReqVO updateReqVO) {
        // 校验存在
        validateAssistiveToolsExists(updateReqVO.getId());
        // 更新
        AssistiveToolsDO updateObj = BeanUtils.toBean(updateReqVO, AssistiveToolsDO.class);
        assistiveToolsDao.updateById(updateObj);
    }

    @Override
    public void deleteAssistiveTools(String id) {
        // 删除
        assistiveToolsDao.deleteById(id);
    }

    private void validateAssistiveToolsExists(String id) {
        if (assistiveToolsDao.selectById(id) == null) {
            throw new ServerException("辅助工具数据不存在");
        }
    }

    @Override
    public AssistiveToolsDO getAssistiveTools(String id) {
        return assistiveToolsDao.selectById(id);
    }

}
