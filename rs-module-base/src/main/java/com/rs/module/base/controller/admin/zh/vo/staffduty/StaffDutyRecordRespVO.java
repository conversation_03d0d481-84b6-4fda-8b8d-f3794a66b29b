package com.rs.module.base.controller.admin.zh.vo.staffduty;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

@Data
@ApiModel(description="")
public class StaffDutyRecordRespVO extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "模板id")
    private String tempId;

    @ApiModelProperty(value = "值班日期")
    private Date dutyDate;

}
