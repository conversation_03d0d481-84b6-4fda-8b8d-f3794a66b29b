package com.rs.module.base.controller.admin.pm.vo;

import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-监管管理-区域监室 Response VO")
@Data
public class RoomPoliceRespVO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主管民警")
    private List<PrisonRoomWarderRespVO> sponsorList;
    @ApiModelProperty("协管民警")
    private List<PrisonRoomWarderRespVO> assistList;
    @ApiModelProperty("主管民警id")
    private String sponsorIds;
    @ApiModelProperty("主管民警身份证号")
    private String sponsorSfzhs;
    @ApiModelProperty("主管民警名称")
    private String sponsorNames;
    @ApiModelProperty("协管民警id")
    private String assistIds;
    @ApiModelProperty("协管民警身份证号")
    private String assistSfzhs;
    @ApiModelProperty("协管民警名称")
    private String assistNames;
}
