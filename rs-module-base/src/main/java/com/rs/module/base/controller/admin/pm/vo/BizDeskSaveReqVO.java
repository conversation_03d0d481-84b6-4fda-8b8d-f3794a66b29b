package com.rs.module.base.controller.admin.pm.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel(description = "管理后台 - 实战平台-监管管理-业务总台配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BizDeskSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("总台名称")
    @NotEmpty(message = "总台名称不能为空")
    private String name;

    @ApiModelProperty("总台标识")
    @NotEmpty(message = "总台标识不能为空")
    private String mark;

    @ApiModelProperty("是否启动浏览权限配置")
    @NotNull(message = "是否启动浏览权限配置不能为空")
    private Short isEnableBrowsingPermission;

    @ApiModelProperty("备注")
    private String remark;

}
