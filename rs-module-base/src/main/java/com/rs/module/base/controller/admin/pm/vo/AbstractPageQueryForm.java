package com.rs.module.base.controller.admin.pm.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.ArrayUtils;

/**
 * <AUTHOR>
 * @date 2019/1/9
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class AbstractPageQueryForm<T> {

	/**
     * 监室编号
     */
    @ApiModelProperty("监所编号")
    private String prisonId;

    /***
     * id
      * @return
     */
    @ApiModelProperty("主键")
   private String id;
    public Integer getCurrent() {
//        return current==null||current==0?1:current;
    	return this.current != null ? this.current : this.getCurPage();
    }

    public void setCurrent(Integer current) {
        this.current = current;
    }

    public Integer getSize() {
//        return size==null||size==0?10:size;
    	return this.size != null ? this.size : this.getPageSize();
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    /**
     * 当前页
     */
    @ApiModelProperty("当前页")
    private Integer current;

    /**
     * 单页记录数
     */
    @ApiModelProperty("单页记录数")
    private Integer size;

    /**
     * 排序列明
     */
    private String sortColumn;

    /**
     * 注意这个优先级会更高，在拦截器内处理使用。会覆盖掉sortColumn。为兼容之前的排序
     */
    @ApiModelProperty("排序列")
    private String orderBy;

    /**
     * 排序：
     * 1 - > id 倒序
     * 0 - > id 正序
     */
    @ApiModelProperty("排序：1 - > id 倒序;0 - > id 正序")
    private Integer sort;

	@ApiModelProperty("SQL 排序 ASC")
	private String ascs;

	public String[] getAscArray() {
		String[] arr = org.springframework.util.StringUtils.delimitedListToStringArray(ascs, ",");
		return arr.length == 0 ? null : arr;
	}

	@ApiModelProperty("SQL 排序 DESC")
	private String descs;

	public String[] getDescArray() {
		String[] arr = org.springframework.util.StringUtils.delimitedListToStringArray(descs, ",");
		return arr.length == 0 ? null : arr;
	}

	public String[] getOrderByArray() {
		String[] arr = org.springframework.util.StringUtils.delimitedListToStringArray(orderBy, ",");
		return arr.length == 0 ? null : arr;
	}

	public Page<T> trainToPage() {
		String[] aa = this.getAscArray();
		String[] da = this.getDescArray();
		if (this.getOrderByArray() != null) {
			// xml一般默认倒序，这里也同样
			if (sort == null || sort == 1) {
				da = ArrayUtils.addAll(da, this.getOrderByArray());
			} else {
				aa = ArrayUtils.addAll(aa, this.getOrderByArray());
			}
		}
		return new Page<T>(this.getCurrent(), this.getSize()).setAsc(aa).setDesc(da);
	}

	@ApiModelProperty(value = "当前页 等于current 《JAVA后台框架结构规范V1.0》新增", required = false)
	private Integer curPage;

	@ApiModelProperty(value = "分页大小 等于size 《JAVA后台框架结构规范V1.0》新增", required = false)
	private Integer pageSize;

	public Integer getCurPage() {
		return curPage == null || curPage == 0 ? 1 : curPage;
	}

	public Integer getPageSize() {
		return pageSize == null || pageSize == 0 ? 10 : pageSize;
	}
}
