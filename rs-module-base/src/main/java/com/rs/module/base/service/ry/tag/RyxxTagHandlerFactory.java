package com.rs.module.base.service.ry.tag;

import com.rs.module.base.service.ry.tag.handle.base.RyxxTagHandler;
import com.rs.module.base.service.ry.tag.handle.enums.RyxxTagBusinessType;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class RyxxTagHandlerFactory {

    private final Map<RyxxTagBusinessType, RyxxTagHandler> handlerMap = new HashMap<>();

    public RyxxTagHandlerFactory(List<RyxxTagHandler> handlers) {
        // 初始化时将所有处理器注册到工厂中
        handlers.forEach(handler -> handlerMap.put(handler.getSupportedBusinessType(), handler));
    }

    public RyxxTagHandler getHandler(RyxxTagBusinessType businessType) {
        return handlerMap.get(businessType);
    }

    public List<RyxxTagHandler> getAllHandlers() {
        return handlerMap.values().stream().collect(Collectors.toList());
    }
}
