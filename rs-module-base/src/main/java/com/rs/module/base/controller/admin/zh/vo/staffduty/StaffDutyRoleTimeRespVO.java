package com.rs.module.base.controller.admin.zh.vo.staffduty;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.base.entity.zh.StaffDutyRoleTimeDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

@Data
@ApiModel( description = "")
public class StaffDutyRoleTimeRespVO extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "关联值班角色表id")
    private String dutyRoleId;

    @ApiModelProperty(value = "值班班次")
    private String dutyShift;

    @ApiModelProperty(value = "值班时间类型-开始 1-当日 2-次日")
    private Integer dutyTimeTypeBegin;

    @ApiModelProperty(value = "值班时间类型-结束  1-当日 2-次日")
    private Integer dutyTimeTypeEnd;

    @ApiModelProperty(value = "值班时间开始")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm", timezone="GMT+8")
    private String dutyTimeBegin;

    @ApiModelProperty(value = "值班时间结束")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm", timezone="GMT+8")
    private String dutyTimeEnd;

    @ApiModelProperty(value = "岗位key")
    private String postKey;
}
