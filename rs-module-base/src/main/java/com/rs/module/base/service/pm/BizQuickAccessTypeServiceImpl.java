package com.rs.module.base.service.pm;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.BizQuickAccessTypeSaveReqVO;
import com.rs.module.base.dao.pm.BizQuickAccessTypeDao;
import com.rs.module.base.entity.pm.BizQuickAccessTypeDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


/**
 * 实战平台-监管管理-业务快捷访问入口类型配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BizQuickAccessTypeServiceImpl extends BaseServiceImpl<BizQuickAccessTypeDao, BizQuickAccessTypeDO> implements BizQuickAccessTypeService {

    @Resource
    private BizQuickAccessTypeDao bizQuickAccessTypeDao;

    @Override
    public String createBizQuickAccessType(BizQuickAccessTypeSaveReqVO createReqVO) {
        // 插入
        BizQuickAccessTypeDO bizQuickAccessType = BeanUtils.toBean(createReqVO, BizQuickAccessTypeDO.class);
        bizQuickAccessTypeDao.insert(bizQuickAccessType);
        // 返回
        return bizQuickAccessType.getId();
    }

    @Override
    public void updateBizQuickAccessType(BizQuickAccessTypeSaveReqVO updateReqVO) {
        // 校验存在
        validateBizQuickAccessTypeExists(updateReqVO.getId());
        // 更新
        BizQuickAccessTypeDO updateObj = BeanUtils.toBean(updateReqVO, BizQuickAccessTypeDO.class);
        bizQuickAccessTypeDao.updateById(updateObj);
    }

    @Override
    @Transactional
    public void deleteBizQuickAccessType(String id) {
        // 删除
        bizQuickAccessTypeDao.deleteById(id);
    }

    private void validateBizQuickAccessTypeExists(String id) {
        if (bizQuickAccessTypeDao.selectById(id) == null) {
            throw new ServerException("业务分类不存在");
        }
    }

    @Override
    public BizQuickAccessTypeDO getBizQuickAccessType(String id) {
        return bizQuickAccessTypeDao.selectById(id);
    }


}
