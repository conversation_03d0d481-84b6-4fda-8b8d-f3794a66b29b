package com.rs.module.base.enums;

/**
 * 被监管人员类型
 *
 * <AUTHOR>
 * @Date 2025/4/21 10:23
 */
public enum BjgrylxEnum {

    ZYRY("01", "在押人员"),
    ZJRY("02", "被拘人员"),
    JDRY("03", "戒毒人员"),
    QTRY("99", "其他");

    BjgrylxEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
