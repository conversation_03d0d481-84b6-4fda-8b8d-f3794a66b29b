package com.rs.module.base.controller.admin.pm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-监管管理-设备授权新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DeviceAuthSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("设备id")
    @NotEmpty(message = "设备id不能为空")
    private String deviceId;

    @ApiModelProperty("授权给ip")
    private String authIp;

    @ApiModelProperty("授权给用户")
    private String authUser;

    @ApiModelProperty("授权的给设备")
    private String authDeviceId;

    @ApiModelProperty("授权给角色")
    private String authRole;

}
