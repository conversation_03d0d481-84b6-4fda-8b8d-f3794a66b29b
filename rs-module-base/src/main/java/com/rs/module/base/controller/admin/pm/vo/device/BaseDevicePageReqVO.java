package com.rs.module.base.controller.admin.pm.vo.device;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-监管管理-设备信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BaseDevicePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("设备编码")
    private String deviceCode;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("设备类型（字典：ZD_SBLXDM）")
    private String deviceTypeId;

    @ApiModelProperty("厂家")
    private String factory;

    @ApiModelProperty("型号")
    private String model;

    @ApiModelProperty("协议")
    private String protocol;

    @ApiModelProperty("ip地址")
    private String ipAddress;

    @ApiModelProperty("端口")
    private Integer port;

    @ApiModelProperty("设备用户名")
    private String devUserName;

    @ApiModelProperty("设备密码")
    private String devPassword;

    @ApiModelProperty("点位名称")
    private String pointName;

    @ApiModelProperty("通道编号")
    private String channelId;

    @ApiModelProperty("通道名称")
    private String channelName;

    @ApiModelProperty("设备国标编号")
    private String gbCode;

    @ApiModelProperty("mac地址")
    private String macAddress;

    @ApiModelProperty("在线时间")
    private Date[] onlineTime;

    @ApiModelProperty("所属区域")
    private String areaId;

    @ApiModelProperty("监室号")
    private String roomId;

    @ApiModelProperty("是否启用")
    private Integer isEnabled;

    @ApiModelProperty("设备状态（字典：ZD_SBZTDM）")
    private String deviceStatus;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;

    @ApiModelProperty(value = "搜索类型(1:只查看会见室、审讯室设备,2,当前区域下所有节点的设备)")
    private Integer searchType;

    private String orgCode;
}
