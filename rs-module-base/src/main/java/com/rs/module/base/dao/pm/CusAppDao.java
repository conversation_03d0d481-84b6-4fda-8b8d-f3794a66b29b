package com.rs.module.base.dao.pm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.base.controller.admin.pm.vo.CusAppPageReqVO;
import com.rs.module.base.entity.pm.CusAppDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 自定义应用管理 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface CusAppDao extends IBaseDao<CusAppDO> {


    default PageResult<CusAppDO> selectPage(CusAppPageReqVO reqVO) {
        Page<CusAppDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<CusAppDO> wrapper = new LambdaQueryWrapperX<CusAppDO>()
                .eqIfPresent(CusAppDO::getSystemId, reqVO.getSystemId())
                .eqIfPresent(CusAppDO::getYymc, reqVO.getYymc())
                .eqIfPresent(CusAppDO::getLjdz, reqVO.getLjdz())
                .eqIfPresent(CusAppDO::getFlId, reqVO.getFlId())
                .eqIfPresent(CusAppDO::getSfnb, reqVO.getSfnb())
                .eqIfPresent(CusAppDO::getSfgg, reqVO.getSfgg())
                .eqIfPresent(CusAppDO::getYylx, reqVO.getYylx())
                .eqIfPresent(CusAppDO::getSfjy, reqVO.getSfjy())
                .eqIfPresent(CusAppDO::getOrderId, reqVO.getOrderId())
                .eqIfPresent(CusAppDO::getYyjs, reqVO.getYyjs())
                .eqIfPresent(CusAppDO::getYytb, reqVO.getYytb());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(CusAppDO::getAddTime);
        }
        Page<CusAppDO> cusAppPage = selectPage(page, wrapper);
        return new PageResult<>(cusAppPage.getRecords(), cusAppPage.getTotal());
    }

    List<Map<String, Object>> getAllApply(@Param("roleIds") List<String> roleIds, @Param("orgCode") String orgCode,
                                          @Param("regCode") String regCode, @Param("yylx")String yylx,
                                          @Param("systemId") String systemId, @Param("mark") String mark);

    List<Map<String, Object>> getWdyyList(@Param("yyidList") List<String> yyidList,@Param("yylx") String yylx);

    void deleteCusAppUserByUserIdCard(@Param("idCard") String idCard);
}

