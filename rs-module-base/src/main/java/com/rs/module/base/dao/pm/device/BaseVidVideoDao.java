package com.rs.module.base.dao.pm.device;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDeviceListReqVO;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDevicePageReqVO;
import com.rs.module.base.entity.pm.device.BaseDeviceDO;
import com.rs.module.base.entity.pm.device.BaseVidVideoDO;
import com.rs.module.base.vo.TreeNode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
* 实战平台-监管管理-设备信息 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BaseVidVideoDao extends IBaseDao<BaseVidVideoDO> {
    String getTreeCodeByDeviceCode(@Param(value = "deviceCode") String deviceCode);

    void clearTable(@Param("orgCode") String orgCode);

    List<String> getAllDevice();

    Integer updateDeviceStatus(@Param("id") String id,@Param("status") String status);
}
