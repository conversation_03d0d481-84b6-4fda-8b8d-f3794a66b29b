package com.rs.module.base.entity.pm;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class AreaData {

    /**
     * 监所区域名称
     */
    @ExcelProperty("区域名称*")
    private String areaName;
    /**
     * 父节点ID
     */
    @ExcelProperty("父节点ID*")
    private String parentId;
    /**
     * 区域类型
     */
    @ExcelProperty("区域类型ID*")
    private String areaType;
    /**
     * 区域编码
     */
    @ExcelProperty("区域编码*")
    private String areaCode;
    /**
     * 是否启用:1启用,0停用
     */
    @ExcelProperty("状态*")
    private String status;
    /**
     * 关押量
     */
    @ExcelProperty("关押量")
    private Integer imprisonmentAmount;
    /**
     * 监室类型
     */
    @ExcelProperty("监室类型")
    private String roomType;
    /**
     * 人员性别
     */
    @ExcelProperty("性别类型")
    private String roomSex;
    /**
     * 设计关押量
     */
    @ExcelProperty("设计关押量")
    private Integer planImprisonmentAmount;
    /**
     * 监室面积
     */
    @ExcelProperty("监室面积*")
    private BigDecimal roomArea;
    /**
     * 人均铺位面积
     */
    @ExcelProperty("人均铺位面积")
    private BigDecimal avgBedsArea;
    /**
     * 上游区域ID
     */
    @ExcelProperty("上游区域ID*")
    private String selfAreaId;

}

