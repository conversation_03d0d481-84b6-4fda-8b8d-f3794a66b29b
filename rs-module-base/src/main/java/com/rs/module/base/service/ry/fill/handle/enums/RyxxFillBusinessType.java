package com.rs.module.base.service.ry.fill.handle.enums;

import com.rs.module.base.service.ry.fill.handle.RestraintsHandle;
import com.rs.module.base.service.ry.fill.handle.YyshHandle;
import com.rs.module.base.service.ry.fill.handle.YyztHandle;

import java.util.Arrays;
import java.util.List;

/**
 * 人员信息补充业务类型枚举
 */

public enum RyxxFillBusinessType {
    getPrisonerList("/app/ihc/getPrisonerList", "根据监室号查询人员信息",
            RestraintsHandle.class,
            YyztHandle.class),
    internalMedicalAppointmentGetById("/ihc/internalMedicalAppointment/getById", "根据id获取预约信息详情",
            YyshHandle.class);

    private List<Class> handlers;
    private String desc;
    private final String code;

    RyxxFillBusinessType(String code, String desc, Class... ryxxHandlers) {
        this.handlers = Arrays.asList(ryxxHandlers);
        this.code = code;
        this.desc = desc;
    }

    public List<Class> getDescription() {
        return handlers;
    }

    public String getCode() {
        return code;
    }

    public List<Class> getHandlers() {
        return handlers;
    }

    public String getDesc() {
        return desc;
    }

    @Override
    public String toString() {
        return code;
    }
}
