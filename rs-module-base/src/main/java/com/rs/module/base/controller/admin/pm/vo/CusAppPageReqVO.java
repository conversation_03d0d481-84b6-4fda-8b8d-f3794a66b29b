package com.rs.module.base.controller.admin.pm.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(description = "管理后台 - 自定义应用管理分页 Request VO")
@Data
public class CusAppPageReqVO extends PageParam {

    @ApiModelProperty("系统ID")
    private String systemId;

    @ApiModelProperty("应用名称")
    private String yymc;

    @ApiModelProperty("链接地址")
    private String ljdz;

    @ApiModelProperty("应用分类ID")
    private String flId;

    @ApiModelProperty("是否内部应用(0:否1:是)")
    private String sfnb;

    @ApiModelProperty("是否公共应用(0:否1:是)")
    private String sfgg;

    @ApiModelProperty("应用类型(0:pc,1:移动端)")
    private String yylx;

    @ApiModelProperty("是否禁用(0:否1:是)")
    private String sfjy;

    @ApiModelProperty("排序")
    private Integer orderId;

    @ApiModelProperty("应用介绍")
    private String yyjs;

    @ApiModelProperty("应用图标")
    private String yytb;

    @ApiModelProperty("排序属性")
    private List<OrderItem> orderFields;
}
