package com.rs.module.base.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.base.controller.admin.pm.vo.BizQuickAccessTypeSaveReqVO;
import com.rs.module.base.entity.pm.BizQuickAccessTypeDO;

import javax.validation.Valid;

/**
 * 实战平台-监管管理-业务快捷访问入口类型配置 Service 接口
 *
 * <AUTHOR>
 */
public interface BizQuickAccessTypeService extends IBaseService<BizQuickAccessTypeDO>{

    /**
     * 创建实战平台-监管管理-业务快捷访问入口类型配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createBizQuickAccessType(@Valid BizQuickAccessTypeSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-业务快捷访问入口类型配置
     *
     * @param updateReqVO 更新信息
     */
    void updateBizQuickAccessType(@Valid BizQuickAccessTypeSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-业务快捷访问入口类型配置
     *
     * @param id 编号
     */
    void deleteBizQuickAccessType(String id);

    /**
     * 获得实战平台-监管管理-业务快捷访问入口类型配置
     *
     * @param id 编号
     * @return 实战平台-监管管理-业务快捷访问入口类型配置
     */
    BizQuickAccessTypeDO getBizQuickAccessType(String id);


}
