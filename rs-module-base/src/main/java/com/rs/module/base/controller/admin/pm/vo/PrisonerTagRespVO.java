package com.rs.module.base.controller.admin.pm.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-监管管理-监管人员标签 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonerTagRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("标签id")
    private String tagCode;
    @ApiModelProperty("标签名称")
    private String tagName;
    @ApiModelProperty("人员标签类型（字典：ZD_JGRYBQLX）")
    private String tagType;

}
