package com.rs.module.base.entity.pm;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-监管管理-业务浏览权限配置 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_biz_browsing_permission")
@KeySequence("acp_pm_biz_browsing_permission_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_biz_browsing_permission")
public class BizBrowsingPermissionDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 业务ID
     */
    private String bizId;
    /**
     * 业务标识
     */
    private String mark;
    /**
     * 权限类型（01：角色，02：机构，03：用户）
     */
    private String permissionType;
    /**
     * 权限ID,根据权限类型存储对应ID
     */
    private String permissionId;

}
