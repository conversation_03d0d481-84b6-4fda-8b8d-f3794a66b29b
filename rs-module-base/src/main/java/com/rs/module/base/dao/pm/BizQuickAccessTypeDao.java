package com.rs.module.base.dao.pm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.base.entity.pm.BizQuickAccessTypeDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 实战平台-监管管理-业务快捷访问入口类型配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BizQuickAccessTypeDao extends IBaseDao<BizQuickAccessTypeDO> {

    default List<BizQuickAccessTypeDO> getAll() {
        return selectList();
    }

    default BizQuickAccessTypeDO getByMark(String mark) {
        return selectOne(new LambdaQueryWrapper<BizQuickAccessTypeDO>()
                .eq(BizQuickAccessTypeDO::getMark, mark));
    }

    default List<BizQuickAccessTypeDO> getByMarks(List<String> marks) {
        return selectList(new LambdaQueryWrapper<BizQuickAccessTypeDO>()
                .in(BizQuickAccessTypeDO::getMark, marks)
                .orderByAsc(BizQuickAccessTypeDO::getOrderId));
    }

}
