package com.rs.module.base.entity.pm;

import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 实战平台-监管管理-区域监室 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_area_prison_room")
@KeySequence("acp_pm_area_prison_room_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AreaPrisonRoomDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private String id;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 是否启用:1启用,0停用
     */
    private String status;
    /**
     * 关押量
     */
    private Integer imprisonmentAmount;
    /**
     * 所属中队
     */
    private String squadronId;
    /**
     * 排序
     */
    private Integer orderId;
    /**
     * 监室编号
     */
    private String roomCode;
    /**
     * 监室类型
     */
    private String roomType;
    /**
     * 人员性别
     */
    private String roomSex;
    /**
     * 设计关押量
     */
    private Integer planImprisonmentAmount;
    /**
     * 监区id
     */
    private String areaId;
    /**
     * 监区名称
     */
    private String areaName;
    /**
     * 监室面积
     */
    private BigDecimal roomArea;
    /**
     * 人均铺位面积
     */
    private BigDecimal avgBedsArea;
    /**
     * 是否一级风险
     */
    private String isLevelRisk;
    /**
     * 上游区域ID
     */
    private String selfAreaId;
    /**
     * 风险等级
     */
    private String fxdj;
    /**
     * 预警时间
     */
    private Date yjsj;
    /**
     * 自动循环床位开关 1-打开
     */
    private Integer autoFlag;
    /**
     * 监室等级 字典：ROOM_LEVEL
     */
    private String roomLevel;

    @TableField(exist = false)
    private Integer violationNum;
    /**
     * 卫生间改造
     */
    private String toiletRenovation;
    /**
     * 取暖方式
     */
    private String heatingMethods;
    /**
     * 防暑降温
     */
    private String fsjw;
    /**
     * 直饮水改造
     */
    private String zysgz;
    /**
     * 床位制
     */
    private String cwz;

}
