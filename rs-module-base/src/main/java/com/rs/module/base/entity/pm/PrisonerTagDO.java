package com.rs.module.base.entity.pm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

/**
 * 实战平台-监管管理-监管人员标签 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_prisoner_tag")
@KeySequence("acp_pm_prisoner_tag_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrisonerTagDO extends BaseDO {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 标签id
     */
    private String tagCode;
    /**
     * 标签名称
     */
    private String tagName;
    /**
     * 人员标签类型（字典：ZD_JGRYBQLX）
     */
    private String tagType;
    /**
     * 业务类型一级类型
     */
    private String businessType;
    /**
     * 业务id
     */
    private String businessId;


}
