package com.rs.module.base.entity;

import com.rs.framework.common.annotation.Format;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.pm.PrisonerService;
import lombok.Data;

/**
 * @ClassName RyVO
 * <AUTHOR>
 * @Date 2025/4/8 11:29
 * @Version 1.0
 */
@Data
public class RyVO extends BaseVO {
    @Format(service = PrisonerService.class, method = "getPrisonerByJgrybm", value = "jgrybm", toBean = PrisonerVwRespVO.class)
    public PrisonerVwRespVO jgryxx;

}
