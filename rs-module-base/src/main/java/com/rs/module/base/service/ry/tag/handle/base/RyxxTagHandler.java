package com.rs.module.base.service.ry.tag.handle.base;

import com.rs.module.base.controller.admin.pm.vo.PrisonerTagRespVO;
import com.rs.module.base.service.ry.tag.handle.enums.RyxxTagBusinessType;

import java.util.List;

public abstract class RyxxTagHandler {
    // 判断是否可以处理当前上下文
    public  boolean canHandle(RyxxTagBusinessType ryxxBusinessType){
        return false;
    }

    // 处理逻辑，返回泛型类型 T
    public abstract void handle(String  jgrybm, List<PrisonerTagRespVO> tags);

    // 获取处理器支持的业务类型
    public abstract RyxxTagBusinessType getSupportedBusinessType();

}
