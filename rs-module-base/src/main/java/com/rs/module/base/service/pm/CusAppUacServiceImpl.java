package com.rs.module.base.service.pm;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.module.base.dao.pm.CusAppUacDao;
import com.rs.module.base.entity.pm.CusAppUacDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


/**
 * 自定义应用权限设置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CusAppUacServiceImpl extends BaseServiceImpl<CusAppUacDao, CusAppUacDO> implements CusAppUacService {

    @Resource
    private CusAppUacDao cusAppUacDao;



}
