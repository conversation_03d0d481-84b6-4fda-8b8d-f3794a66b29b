package com.rs.module.base.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实战平台-系统-消息模板 DO
 *
 * <AUTHOR>
 */
@TableName("acp_sys_msg_template")
@KeySequence("acp_sys_msg_template_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MsgTemplateDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 消息标题
     */
    private String msgTitle;
    /**
     * 消息模版
     */
    private String msgContent;
    /**
     * 消息类型（1：通知消息，2：待办消息）
     */
    private String msgType;
    /**
     * 是否主协管民警
     */
    private Integer isPolice;
    /**
     * 岗位id，多个用,分割
     */
    private String postId;
    /**
     * 模板状态1已发布0未发布
     */
    private String templateStatus;
    /**
     * 模块编码
     */
    private String moduleCode;
    /**
     * 监所ID（消息模版对应的单位）
     */
    private String orgCode;

}
