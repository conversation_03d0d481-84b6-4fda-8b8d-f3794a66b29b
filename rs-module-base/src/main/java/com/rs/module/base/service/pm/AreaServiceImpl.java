package com.rs.module.base.service.pm;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.framework.mybatis.util.UacUserUtil;
import com.rs.module.base.controller.admin.pm.vo.*;
import com.rs.module.base.dao.pm.AreaDao;
import com.rs.module.base.dao.pm.AreaPrisonRoomDao;
import com.rs.module.base.dao.pm.device.BaseDeviceDao;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.base.entity.pm.AreaData;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.entity.pm.device.BaseDeviceDO;
import com.rs.module.base.enums.AreaTypeEnum;
import com.rs.module.base.enums.DeviceTypeEnum;
import com.rs.module.base.util.TimeBasedCodeGenerator;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;


/**
 * 实战平台-监管管理-区域 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AreaServiceImpl extends BaseServiceImpl<AreaDao, AreaDO> implements AreaService {

    @Resource
    private AreaDao areaDao;

    @Resource
    private AreaPrisonRoomDao areaPrisonRoomDao;

    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;

    @Resource
    private BaseDeviceDao baseDeviceDao;

    @Resource
    private PrisonRoomWarderService prisonRoomWarderService;

    private final String BASESTARTNUM = "0001";


    @Override
    public List<AreaListRespVO> getAreaListByOrgCode(String orgCode, String areaType) {
        List<AreaListRespVO> resultList = new ArrayList<>();
        List<AreaListRespVO> areaListRespVOList = areaDao.getAreaListByOrgCode(orgCode, areaType);
        if (CollUtil.isEmpty(areaListRespVOList)) {
            return new ArrayList<>();
        }
        Map<String, List<AreaListRespVO>> listMap = areaListRespVOList.stream()
                .collect(Collectors.groupingBy(AreaListRespVO::getAreaCode));
        listMap.forEach((k, v) -> {
            AreaListRespVO areaListRespVO = v.get(0);
            AreaListRespVO quyu = new AreaListRespVO();
            quyu.setAreaCode(areaListRespVO.getAreaCode());
            quyu.setAreaName(areaListRespVO.getAreaName());
            List<AreaListRespVO> children = new ArrayList<>();
            for (AreaListRespVO area : v) {
                AreaListRespVO child = new AreaListRespVO();
                child.setRoomName(area.getRoomName());
                child.setRoomCode(area.getRoomCode());
                children.add(child);
            }
            quyu.setChildren(children);
            resultList.add(quyu);
        });

        resultList.sort(Comparator.comparing(AreaListRespVO::getAreaCode)
                .thenComparing(AreaListRespVO::getRoomCode));
        return resultList;
    }

    @Override
    public List<Tree<String>> getAreaListByOrgCodeAndAreaType(String orgCode, AreaTypeEnum areaType) {
        List<TreeNode<String>> nodeList = CollUtil.newArrayList();
        List<AreaInfoRespVO> list = areaDao.getAreaListByOrgCodeAndAreaType(orgCode, areaType.getCode());
        for (AreaInfoRespVO areaInfo : list) {
            TreeNode<String> node = new TreeNode<String>(areaInfo.getId(), areaInfo.getParentId(), null, 0);
            node.setExtra(BeanUtil.beanToMap(areaInfo, "orgCode", "orgName", "areaName", "areaCode", "areaType", "level"));
            nodeList.add(node);
        }
        List<Tree<String>> treeList = TreeUtil.build(nodeList, null);
        return treeList;
    }

    /**
     * 创建区域信息。
     * <p>
     * 1. 获取当前登录用户信息（SessionUser），包括用户名、用户ID等。
     * 2. 将请求对象（AreaSaveReqDTO）转换为实体对象（AreaDO）。
     * 3. 设置区域的通用属性，例如：是否删除（isDel）、创建时间（addTime）、创建人（addUser）、更新人（updateUser）等。
     * 4. 如果存在父节点ID，则查询父节点信息，并构造完整的父节点路径（allParentId）。
     * 5. 调用 areaDao.insert 方法将区域信息插入数据库。
     * 6. 如果区域类型为“监室”（DETENTION_ROOM），则查询相关区域信息并调用 createAreaPrisonRoom 方法处理监室信息。
     * 7. 返回新创建区域的主键ID（id）。
     *
     * @param createReqVO 区域创建请求对象
     * @return 返回新创建区域的主键ID
     */
    @Override
    public String createArea(AreaSaveReqDTO createReqVO) {
        String orgCode = createReqVO.getOrgCode();
        // 插入
        AreaDO area = BeanUtils.toBean(createReqVO, AreaDO.class);
        if (StringUtil.isEmpty(createReqVO.getOrgCode())) {
            SessionUser sessionUser = SessionUserUtil.getSessionUser();
            String userName = sessionUser.getName();
            String userId = sessionUser.getId();
            area.setAddUser(userName);
            area.setUpdateUser(userId);
            area.setUpdateUserName(userName);
            area.setProCode(sessionUser.getProCode());
            area.setProName(sessionUser.getProName());
            area.setCityCode(sessionUser.getCityCode());
            area.setCityName(sessionUser.getCityName());
            area.setRegCode(sessionUser.getRegCode());
            area.setRegName(sessionUser.getRegName());
            area.setOrgCode(sessionUser.getOrgCode());
            area.setOrgName(sessionUser.getOrgName());
            orgCode = sessionUser.getOrgCode();
        } else {
            AreaDO jsAreaDO = lambdaQuery()
                    .eq(AreaDO::getOrgCode, createReqVO.getOrgCode())
                    .eq(AreaDO::getAreaType, AreaTypeEnum.DETENTION_FACILITY.getCode())
                    .one();
            area.setAddUser("系统同步");
            area.setUpdateUser("系统同步");
            area.setUpdateUserName("系统同步");
            area.setProCode(jsAreaDO.getProCode());
            area.setProName(jsAreaDO.getProName());
            area.setCityCode(jsAreaDO.getCityCode());
            area.setCityName(jsAreaDO.getCityName());
            area.setRegCode(jsAreaDO.getRegCode());
            area.setRegName(jsAreaDO.getRegName());
            area.setOrgCode(jsAreaDO.getOrgCode());
            area.setOrgName(jsAreaDO.getOrgName());
        }

        area.setIsDel(false);
        area.setAddTime(new Date());


        //监所
        // 编码+01+类型编码+四位递增
        String maxAreaId = "";
        if (AreaTypeEnum.DETENTION_FACILITY.getCode().equals(createReqVO.getAreaType())) {
            //maxAreaId = area.setAreaCode(createReqVO.getAreaCode() + "01" + area.getAreaType() + TimeBasedCodeGenerator.generateCode());
            //area.setAreaCode(createReqVO.getAreaCode() + "01" + area.getAreaType() + TimeBasedCodeGenerator.generateCode());

            maxAreaId = getMaxIdByAreaType(createReqVO.getAreaCode() + "01" + area.getAreaType());
            //判断上机是否是同级，那就直接清空
            if (StrUtil.isNotBlank(area.getParentId())) {
                AreaDO areaTemp = areaDao.selectById(area.getParentId());
                if (areaTemp != null && AreaTypeEnum.DETENTION_FACILITY.getCode().equals(areaTemp.getAreaType())) {
                    createReqVO.setParentId(null);
                    area.setParentId(null);
                }
            }

        } else {
            area.setAreaCode(orgCode + "01" + area.getAreaType() + TimeBasedCodeGenerator.generateCode());
            maxAreaId = getMaxIdByAreaType(orgCode + "01" + area.getAreaType());
        }

        if (StrUtil.isBlank(area.getParentId()) && AreaTypeEnum.DETENTION_FACILITY.getCode().equals(area.getAreaType())) {
            area.setAllParentId("默认根节点");
        }

        area.setId(maxAreaId);
        area.setAreaCode(maxAreaId);
        //根据父节点查询该父节点对应的ID数据，并构造父节点数据
        if (createReqVO.getParentId() != null) {
            AreaDO areaDO = areaDao.selectById(createReqVO.getParentId());
            Assert.notNull(areaDO, StrUtil.format("父节点ID:{}不存在", createReqVO.getParentId()));
            area.setAllParentId((StrUtil.isBlank(areaDO.getAllParentId()) ? StrUtil.EMPTY : areaDO.getAllParentId()) + "#" + createReqVO.getParentId());
        }
        areaDao.insert(area);

        //如果区域类型为监室
        if (area.getAreaType().equals(AreaTypeEnum.DETENTION_ROOM.getCode())) {
            List<String> allParentIdList = Arrays.asList(area.getAllParentId().split("#"));
            List<AreaDO> list = areaDao.queryArea(allParentIdList, area.getOrgCode());
            //处理保存监室的信息
            areaPrisonRoomService.createAreaPrisonRoom(area.getId(), createReqVO, list);
            //保存主协管信息
            List<AreaRelatedWarderReqVO> warderReqVOList = createReqVO.getAreaRelatedWarderReqVOList();
            if (CollUtil.isNotEmpty(warderReqVOList)) {
                for (AreaRelatedWarderReqVO warderReqVO : warderReqVOList) {
                    PrisonRoomWarderSaveReqVO prisonRoomWarderSaveReqVO = BeanUtils.toBean(warderReqVO, PrisonRoomWarderSaveReqVO.class);
                    prisonRoomWarderSaveReqVO.setRoomId(area.getId());
                    prisonRoomWarderSaveReqVO.setPoliceSfzh("");
                    prisonRoomWarderService.createPrisonRoomWarder(prisonRoomWarderSaveReqVO);
                }
            }
        }
        // 返回
        return area.getId();
    }

    /**
     * 获取最大类型的区域编码
     *
     * @param str
     * @return
     */
    @Override
    public String getMaxIdByAreaType(String str) {
        String maxId = areaDao.getMaxIdByAreaType(str);
        if (StrUtil.isBlank(maxId)) {
            //区域生成规则：监所编码+01+类型编码+四位递增
            return str + BASESTARTNUM;
        }
        return increment(maxId);
    }

    public static String increment(String input) {
        // 转换为 BigInteger 并加 1
        BigInteger number = new BigInteger(input);
        BigInteger incremented = number.add(BigInteger.ONE);
        // 格式化为原长度的字符串（保持前导零）
        return String.format("%0" + input.length() + "d", incremented);
    }


    @Override
    public void updateArea(AreaSaveReqVO updateReqVO) {
        updateAreaPrisonRoom(updateReqVO);
        updatePrisonRoomWarder(updateReqVO);
        // 校验存在
        validateAreaExists(updateReqVO.getId());
        // 更新
        AreaDO updateObj = BeanUtils.toBean(updateReqVO, AreaDO.class);
        //父节点ID更新
        if (updateReqVO.getParentId() != null) {
            AreaDO areaDO = areaDao.selectById(updateReqVO.getParentId());
            if (areaDO != null) {
                updateObj.setAllParentId(areaDO.getAllParentId() + "#" + updateReqVO.getParentId());
            }
        }
        areaDao.updateById(updateObj);
    }

    private void updatePrisonRoomWarder(AreaSaveReqVO updateReqVO) {
        prisonRoomWarderService.deletePrisonRoomWarderByRoomId(updateReqVO.getId());

        List<AreaRelatedWarderReqVO> warderReqVOList = updateReqVO.getAreaRelatedWarderReqVOList();
        for (AreaRelatedWarderReqVO warderReqVO : warderReqVOList) {
            PrisonRoomWarderSaveReqVO prisonRoomWarderSaveReqVO = BeanUtils.toBean(warderReqVO, PrisonRoomWarderSaveReqVO.class);

            prisonRoomWarderSaveReqVO.setRoomId(updateReqVO.getId());
            if (StrUtil.isBlank(prisonRoomWarderSaveReqVO.getPoliceSfzh()) && StrUtil.isNotBlank(warderReqVO.getPoliceId())) {
                prisonRoomWarderSaveReqVO.setPoliceSfzh(UacUserUtil.getUserById(warderReqVO.getPoliceId()).getIdCard());
            }

            prisonRoomWarderService.createPrisonRoomWarder(prisonRoomWarderSaveReqVO);
        }
    }

    private void updateAreaPrisonRoom(AreaSaveReqVO updateReqVO) {
        AreaPrisonRoomSaveReqDTO areaPrisonRoomSaveReqDTO = updateReqVO.getAreaPrisonRoomSaveDto();
        if (areaPrisonRoomSaveReqDTO != null) {
            AreaPrisonRoomListReqVO listReqVO = new AreaPrisonRoomListReqVO();
            listReqVO.setRoomCode(updateReqVO.getId());
            List<AreaPrisonRoomDO> areaPrisonRoomDOList = areaPrisonRoomService.getAreaPrisonRoomListByRoomCode(listReqVO);
            if (areaPrisonRoomDOList != null && areaPrisonRoomDOList.size() > 1) {
                throw new ServerException("实战平台-监管管理-区域数据关联的监室数据不唯一，数据可能存在问题，请核查！");
            } else if (areaPrisonRoomDOList != null) {
                String areaPrisonRoomId = areaPrisonRoomDOList.get(0).getId();
                AreaPrisonRoomSaveReqVO areaPrisonRoomSaveReqVO = BeanUtils.toBean(areaPrisonRoomSaveReqDTO, AreaPrisonRoomSaveReqVO.class);
                areaPrisonRoomSaveReqVO.setId(areaPrisonRoomId);
                areaPrisonRoomSaveReqVO.setAreaRelatedWarderReqVOList(updateReqVO.getAreaRelatedWarderReqVOList());
                areaPrisonRoomService.updateAreaPrisonRoom(areaPrisonRoomSaveReqVO);
            } else {
                throw new ServerException("实战平台-监管管理-区域监室数据不存在");
            }

        }

    }

    @Override
    public void deleteArea(String id) {
        // 校验存在
        validateAreaExists(id);
        AreaListReqVO reqVO = new AreaListReqVO();
        reqVO.setParentId(id);
        List<AreaDO> list = areaDao.selectList(reqVO);
        if (list != null && list.size() > 0) {
            throw new ServerException("该区域节点下有子节点，不允许删除，若要删除，请先删除子节点");
        }
        // 删除
        areaDao.deleteById(id);

        areaPrisonRoomService.deleteAreaPrisonRoomByRoomCode(id);

        prisonRoomWarderService.deletePrisonRoomWarderByRoomId(id);
    }

    private void validateAreaExists(String id) {
        if (areaDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-区域数据不存在");
        }
    }

    @Override
    public AreaDO getArea(String id) {
        return areaDao.selectById(id);
    }

    @Override
    public List<AreaInfoRespVO> getAreaByOrgCode(String orgCode, String areaType) {
        List<AreaDO> areaList = areaDao.selectList(orgCode, areaType);
        if (CollUtil.isEmpty(areaList)) {
            return Collections.emptyList();
        }
        return BeanUtils.toBean(areaList, AreaInfoRespVO.class);
    }

    @Override
    public AreaDO getPrisonByOrgCode(String orgCode) {
        return areaDao.selectOne(new LambdaQueryWrapper<AreaDO>()
                .eq(AreaDO::getOrgCode, orgCode)
                .eq(AreaDO::getAreaType, AreaTypeEnum.DETENTION_FACILITY.getCode()));
    }

    @Override
    public PageResult<AreaDO> getAreaPage(AreaPageReqVO pageReqVO) {
        return areaDao.selectPage(pageReqVO);
    }

    @Override
    public List<AreaDO> getAreaList(AreaListReqVO listReqVO) {
        return areaDao.selectList(listReqVO);
    }

    /**
     * 获取区域树结构
     * <p>
     * 根据传入的参数，构建并返回区域树结构如果未指定具体区域名称，则返回完整的区域树；
     * 否则，返回与指定区域名称匹配的区域及其所有祖先节点构成的树结构
     *
     * @param listReqVO 包含查询参数的请求对象
     * @return 区域树列表
     */
    @Override
    public List<Tree<String>> getAreaTree(AreaListReqVO listReqVO) {
        // 查询匹配条件的区域列表
        List<AreaDO> areaList = areaDao.selectList(listReqVO);

        // 如果 areaName 没传，则返回完整树
        if (StrUtil.isBlank(listReqVO.getAreaName())) {
            return buildTree(areaList);
        }

        // 否则：需要向上回溯祖先节点，构造完整路径
        // 1. 查询 org_code 下的所有节点
        listReqVO.setAreaName(null);
        List<AreaDO> allList = areaDao.selectList(listReqVO);

        // 2. 组合匹配节点和所有祖先节点
        Set<String> requiredIds = new HashSet<>();
        for (AreaDO matched : areaList) {
            if (StrUtil.isNotBlank(matched.getAllParentId())) {
                requiredIds.addAll(Arrays.asList(matched.getAllParentId().split("#")));
            }
            requiredIds.add(matched.getId());
        }

        // 3. 过滤出所需的所有节点
        List<AreaDO> filtered = allList.stream()
                .filter(area -> requiredIds.contains(area.getId()))
                .collect(Collectors.toList());

        // 构建并返回过滤后的区域树
        return buildTree(filtered);
    }


    /**
     * 构建地区信息树
     * 此方法将一组地区数据对象转换为树结构，以便于层次显示
     * 树的构建基于每个地区的ID和其父ID，根节点的父ID应为"0"
     *
     * @param areaList 包含地区信息的数据对象列表
     * @return 返回一个由地区名称构成的树结构列表
     */
    private List<Tree<String>> buildTree(List<AreaDO> areaList) {
        // 使用TreeUtil工具类构建树结构，根节点的父ID为"0"
        return TreeUtil.build(areaList, "0", (area, treeNode) -> {
            // 将地区数据对象的属性值赋给树节点
            treeNode.setId(area.getId());
            treeNode.setParentId(StrUtil.blankToDefault(area.getParentId(), "0"));
            treeNode.setName(area.getAreaName());
            treeNode.setWeight(area.getOrderId() != null ? area.getOrderId() : 0);
            // 将额外的地区信息作为节点的扩展属性
            treeNode.putExtra("orgCode", area.getOrgCode());
            treeNode.putExtra("areaName", area.getAreaName());
            treeNode.putExtra("areaType", area.getAreaType());
        });
    }


    /**
     * 获取指定条件下的子区域列表
     *
     * @param orgCode  组织代码，用于区分不同的组织
     * @param parentId 父区域ID，用于筛选子区域
     * @param areaName 区域名称，用于模糊匹配区域
     * @param areaCode 区域代码，用于精确匹配区域
     * @param areaType 区域类型，用于筛选特定类型的区域
     * @return 符合条件的子区域列表
     */
    @Override
    public List<AreaInfoRespVO> getChildrenAreas(String orgCode, String parentId, String areaName, String areaCode, String areaType) {
        // 从数据库查询符合条件的区域列表（包括父子）
        List<AreaInfoRespVO> allAreaList = areaDao.selectByOrgCodeAndCondition(orgCode, areaName, areaCode, areaType);

        // 如果查询结果为空，直接返回空列表
        if (CollUtil.isEmpty(allAreaList)) {
            return Collections.emptyList();
        }

        // 构建Map<id, area> 便于递归时查找
        //Map<String, AreaInfoRespVO> areaMap = allAreaList.stream()
        //        .collect(Collectors.toMap(AreaInfoRespVO::getId, Function.identity(), (a, b) -> a));

        // 只保留以parentId为起点的子树
        List<AreaInfoRespVO> resultList = new ArrayList<>();

        // 先筛选根节点下的直接/间接匹配项
        for (AreaInfoRespVO area : allAreaList) {
            // 如果当前区域是父区域或者其所有父区域中包含指定的父区域ID，则加入结果列表
            if (area.getId().equals(parentId) || (area.getParentId() != null && area.getAllParentId().contains(parentId))) {
                resultList.add(area);
            }
        }

        // 返回结果
        return resultList;
    }

    @Override
    public PageResult<AreaDO> getChildrenAreaPage(String orgCode, String id, String areaName, String areaCode, String areaType, Integer pageNo, Integer pageSize) {

        return areaDao.selecChildrentPage(orgCode, id, areaName, areaCode, areaType, pageNo, pageSize);
    }

    @Override
    public AreaInfoRespVO getAreaDetail(String id) {
        AreaDO areaDO = areaDao.selectById(id);
        if (areaDO != null) {
            AreaInfoRespVO areaInfoRespVO = areaDO.toBean(AreaInfoRespVO.class);
            if (StrUtil.isNotBlank(areaDO.getParentId())) {
                AreaDO areaParent = areaDao.selectById(areaDO.getParentId());
                if (areaParent != null) {
                    areaInfoRespVO.setParentName(areaParent.getAreaName());
                }
            }

            AreaPrisonRoomListReqVO listReqVO = new AreaPrisonRoomListReqVO();
            listReqVO.setRoomCode(id);
            List<AreaPrisonRoomDO> areaPrisonRoomDOList = areaPrisonRoomService.getAreaPrisonRoomListByRoomCode(listReqVO);
            if (areaPrisonRoomDOList != null && areaPrisonRoomDOList.size() > 0) {
                AreaPrisonRoomDO areaPrisonRoomDO = areaPrisonRoomDOList.get(0);
                areaInfoRespVO.setAreaPrisonRoomRespVO(areaPrisonRoomDO.toBean(AreaPrisonRoomRespVO.class));
            }

            Map<String, List<PrisonRoomWarderRespVO>> map = prisonRoomWarderService.getPrisonRoomWarderListByRoomId(id);
            if (map != null && map.size() > 0) {
                for (Map.Entry<String, List<PrisonRoomWarderRespVO>> entry : map.entrySet()) {
                    if (entry.getValue() != null && entry.getValue().size() > 0) {
                        areaInfoRespVO.setAreaRelatedWarderReqVOList(entry.getValue());
                    }
                }
            }
            return areaInfoRespVO;
        }
        return new AreaInfoRespVO();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealAreaData(List<AreaData> areaDataList) {
        //将areaDataList每个对象里的areaType、status转换成枚举类型
        areaDataList.forEach(areaData -> {
            areaData.setAreaType(AreaTypeEnum.getCodeByName(areaData.getAreaType()));
            if (areaData.getAreaCode() != null && areaData.getAreaCode().equalsIgnoreCase(AreaTypeEnum.DETENTION_ROOM.getName())) {
                areaData.setStatus(areaData.getStatus().equalsIgnoreCase("启用") ? "1" : "0");
            }
        });
        List<AreaSaveReqDTO> areaSaveReqDTOList = new ArrayList<>();
        //循环每个AreaData，设置属性到AreaSaveReqDTO
        areaDataList.forEach(areaData -> {
            AreaSaveReqDTO areaSaveReqDTO = BeanUtils.toBean(areaData, AreaSaveReqDTO.class);
            AreaPrisonRoomSaveReqDTO areaPrisonRoomSaveReqDTO = new AreaPrisonRoomSaveReqDTO();
            areaPrisonRoomSaveReqDTO.setRoomName(areaData.getAreaName());
            areaPrisonRoomSaveReqDTO.setStatus(areaData.getStatus());
            areaPrisonRoomSaveReqDTO.setImprisonmentAmount(areaData.getImprisonmentAmount());
            areaPrisonRoomSaveReqDTO.setPlanImprisonmentAmount(areaData.getPlanImprisonmentAmount());
            areaPrisonRoomSaveReqDTO.setRoomArea(areaData.getRoomArea());
            areaPrisonRoomSaveReqDTO.setAvgBedsArea(areaData.getAvgBedsArea());
            areaPrisonRoomSaveReqDTO.setSelfAreaId(areaData.getSelfAreaId());
            areaPrisonRoomSaveReqDTO.setAreaId(areaData.getParentId());
            areaPrisonRoomSaveReqDTO.setAreaName(areaData.getAreaName());
            areaPrisonRoomSaveReqDTO.setRoomType(areaData.getRoomType());
            areaPrisonRoomSaveReqDTO.setRoomSex(areaData.getRoomSex());
            areaSaveReqDTO.setAreaPrisonRoomSaveDto(areaPrisonRoomSaveReqDTO);
            areaSaveReqDTO.setAreaRelatedWarderReqVOList(new ArrayList<>());
            areaSaveReqDTOList.add(areaSaveReqDTO);

        });
        //循环调用createArea存储数据
        areaSaveReqDTOList.forEach(areaSaveReqDTO -> {
            createArea(areaSaveReqDTO);
        });


    }

    @Override
    public AreaDO getPrisonByRoomId(String roomId) {
        AreaDO areaDO = areaDao.selectById(roomId);
        //获取监所
        while (areaDO != null) {
            if ("0001".equals(areaDO.getAreaType())) {
                return areaDO;
            }
            areaDO = areaDao.selectById(areaDO.getParentId());
        }

        return null;
    }

    @Override
    public List<Tree<String>> getAreaCameraTree(AreaListReqVO listReqVO) {
        Map<String, List<BaseDeviceDO>> cameraListMap = null;
        List<BaseDeviceDO> baseDeviceDOList = baseDeviceDao.selectList(new LambdaQueryWrapper<BaseDeviceDO>().eq(BaseDeviceDO::getDeviceTypeId, DeviceTypeEnum.CAMERA.getCode()));
        if (CollUtil.isNotEmpty(baseDeviceDOList)) {
            cameraListMap = baseDeviceDOList.stream().filter(e -> StrUtil.isNotBlank(e.getAreaId())).collect(Collectors.groupingBy(BaseDeviceDO::getAreaId));
        }

        // 查询匹配条件的区域列表
        List<AreaDO> areaList = areaDao.selectList(listReqVO);
        List<AreaDO> areaDOListNew = new ArrayList<>();
        for (AreaDO areaDO : areaList) {
            if (CollUtil.isNotEmpty(cameraListMap)) {
                List<BaseDeviceDO> baseDeviceDOListTemp = cameraListMap.get(areaDO.getId());
                if (CollUtil.isNotEmpty(baseDeviceDOListTemp)) {
                    baseDeviceDOListTemp.forEach(e -> {
                        AreaDO areaTemp = new AreaDO();
                        areaTemp.setId(e.getId());
                        areaTemp.setAreaCode(e.getId());
                        areaTemp.setParentId(e.getAreaId());
                        areaTemp.setAreaType(areaDO.getAreaType() + "_" + e.getDeviceTypeId());
                        areaTemp.setAreaName(e.getDeviceName());
                        areaTemp.setOrgCode(e.getOrgCode());
                        // 将额外的地区信息作为节点的扩展属性
                        areaDOListNew.add(areaTemp);
                    });
                }
            }
        }

        if (CollUtil.isNotEmpty(areaDOListNew)) {
            areaList.addAll(areaDOListNew);
        }
        List<Tree<String>> treeList = buildTree(areaList);
        if (CollUtil.isNotEmpty(treeList)) {
            List<Tree<String>> newTree = new ArrayList<>();
            //移除第一层
            for (Tree<String> tree : treeList) {
                AtomicBoolean atomicBoolean = new AtomicBoolean(false);
                setCameraInfo(tree, atomicBoolean);
                if (atomicBoolean.get()) {
                    newTree.add(tree);
                }
            }

            filterCameraNodes(newTree);
            return newTree;
        }
        return treeList;
    }


    /**
     * 过滤没有摄像头的节点
     *
     * @param [nodes]
     * @return void
     * <AUTHOR>
     * @date 2025/6/13 17:38
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    private void filterCameraNodes(List<Tree<String>> nodes) {
        if (nodes == null) {
            return;
        }
        // 从后向前遍历，避免ConcurrentModificationException
        for (int i = nodes.size() - 1; i >= 0; i--) {
            Tree node = nodes.get(i);
            //String areaType = (String) node.get("areaType");
            AtomicBoolean atomicBoolean = new AtomicBoolean(false);
            setCameraInfo(node, atomicBoolean);
            if (!atomicBoolean.get()) {
                nodes.remove(i);
                continue;
            }
            // 递归处理子节点
            filterCameraNodes(node.getChildren());
        }
    }


    /**
     * 设置是否有摄像头
     *
     * @param tree
     * @param flag
     */
    private void setCameraInfo(Tree<String> tree, AtomicBoolean flag) {
        String areaType = (String) tree.get("areaType");
        if (areaType != null && areaType.lastIndexOf("_0001") != -1) {
            flag.set(true);
            return;
        }
        List<Tree<String>> treeList = tree.getChildren();
        if (CollUtil.isNotEmpty(treeList)) {
            for (Tree<String> stringTree : treeList) {
                setCameraInfo(stringTree, flag);
            }
        }

    }

    /**
     * 获取子区域Id
     *
     * @param areaId String 区域Id
     * @return List<String>
     */
    public List<String> getChildAreaId(String areaId) {
        return list(new LambdaQueryWrapperX<AreaDO>()
                .eqIfPresent(AreaDO::getParentId, areaId)
                .select(AreaDO::getId))
                .stream()
                .map(s -> s.getId())
                .collect(Collectors.toList());
    }

    /**
     * 获取所有子区域Id
     *
     * @param areaId String 区域Id
     * @return List<String>
     */
    public List<String> getAllChildAreaIds(String areaId) {
        List<String> areaIdList = getChildAreaId(areaId);
        getChildAreaIdCur(areaId, areaIdList);
        return areaIdList;
    }

    /**
     * 递归获取子区域Id
     *
     * @param areaId     String 区域Id
     * @param areaIdList List<String> 已存在区域Id集合
     */
    private void getChildAreaIdCur(String areaId, List<String> areaIdList) {
        List<String> childAreaIdList = getChildAreaId(areaId);
        if (CollectionUtil.isNotNull(childAreaIdList)) {
            areaIdList.addAll(childAreaIdList);
            for (String id : areaIdList) {
                getChildAreaIdCur(id, areaIdList);
            }
        }
    }
}
