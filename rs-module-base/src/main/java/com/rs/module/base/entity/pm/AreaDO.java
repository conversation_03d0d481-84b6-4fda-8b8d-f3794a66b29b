package com.rs.module.base.entity.pm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 实战平台-监管管理-区域 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_area")
@KeySequence("acp_pm_area_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_area")
public class AreaDO extends BaseDO {

    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.INPUT)
    private String id;
    /**
     * 所属省级代码
     */
    private String proCode;
    /**
     * 所属省级名称
     */
    private String proName;
    /**
     * 监所区域名称
     */
    private String areaName;
    /**
     * 父节点ID
     */
    private String parentId;
    /**
     * 父区域的路径信息,#分隔
     */
    private String allParentId;
    /**
     * 排序
     */
    private Integer orderId;
    /**
     * 区域类型
     */
    private String areaType;
    /**
     * 区域编码
     */
    private String areaCode;
    /**
     * 所在层级
     */
    private Integer level;
    /**
     * 上游id
     */
    private String selfAreaId;

}
