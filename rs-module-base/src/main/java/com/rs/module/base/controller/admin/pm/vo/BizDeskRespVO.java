package com.rs.module.base.controller.admin.pm.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-监管管理-业务总台配置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BizDeskRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("总台名称")
    private String name;
    @ApiModelProperty("总台标识")
    private String mark;
    @ApiModelProperty("是否启动浏览权限配置")
    private Short isEnableBrowsingPermission;
    @ApiModelProperty("备注")
    private String remark;
}
