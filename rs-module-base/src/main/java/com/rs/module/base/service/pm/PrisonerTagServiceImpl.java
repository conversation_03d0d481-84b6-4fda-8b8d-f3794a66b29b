package com.rs.module.base.service.pm;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.PrisonerTagListReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerTagPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerTagSaveReqVO;
import com.rs.module.base.dao.pm.PrisonerTagDao;
import com.rs.module.base.entity.pm.PrisonerTagDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 实战平台-监管管理-监管人员标签 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrisonerTagServiceImpl extends BaseServiceImpl<PrisonerTagDao, PrisonerTagDO> implements PrisonerTagService {

    @Resource
    private PrisonerTagDao prisonerTagDao;

    @Override
    public String createPrisonerTag(PrisonerTagSaveReqVO createReqVO) {
        // 插入
        PrisonerTagDO prisonerTag = BeanUtils.toBean(createReqVO, PrisonerTagDO.class);
        prisonerTagDao.insert(prisonerTag);
        // 返回
        return prisonerTag.getId();
    }

    @Override
    public void updatePrisonerTag(PrisonerTagSaveReqVO updateReqVO) {
        // 校验存在
        validatePrisonerTagExists(updateReqVO.getId());
        // 更新
        PrisonerTagDO updateObj = BeanUtils.toBean(updateReqVO, PrisonerTagDO.class);
        prisonerTagDao.updateById(updateObj);
    }

    @Override
    public void deletePrisonerTag(String id) {
        // 校验存在
        validatePrisonerTagExists(id);
        // 删除
        prisonerTagDao.deleteById(id);
    }

    private void validatePrisonerTagExists(String id) {
        if (prisonerTagDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-监管人员标签数据不存在");
        }
    }

    @Override
    public PrisonerTagDO getPrisonerTag(String id) {
        return prisonerTagDao.selectById(id);
    }

    @Override
    public PageResult<PrisonerTagDO> getPrisonerTagPage(PrisonerTagPageReqVO pageReqVO) {
        return prisonerTagDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrisonerTagDO> getPrisonerTagList(PrisonerTagListReqVO listReqVO) {
        return prisonerTagDao.selectList(listReqVO);
    }


}
