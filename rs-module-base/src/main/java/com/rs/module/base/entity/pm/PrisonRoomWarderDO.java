package com.rs.module.base.entity.pm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 实战平台-监管管理-监室主协管人员 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_prison_room_warder")
@KeySequence("acp_pm_prison_room_warder_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrisonRoomWarderDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 民警id
     */
    private String policeId;
    /**
     * 民警名字
     */
    private String policeName;
    /**
     * 民警身份证号
     */
    private String policeSfzh;
    /**
     * 用户类型 主 w,协 a,机动 m
     */
    private String userType;
    /**
     * 监室ID
     */
    private String roomId;
    /**
     * 状态
     */
    private String status;
    /**
     * 旧民警id
     */
    private String historyPoliceId;
    /**
     * 旧民警名称
     */
    private String historyPoliceName;
    /**
     * 旧民警身份证
     */
    private String historyPoliceSfzh;

}
