package com.rs.module.base.controller.admin.pm.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-监管管理-业务快捷访问入口类型配置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BizQuickAccessTypeRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("业务快捷访问入口类型名称")
    private String name;
    @ApiModelProperty("业务快捷访问入口类型标识")
    private String mark;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("排序Id")
    private Integer orderId;
}
