package com.rs.module.base.service.ry.tag.handle;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.PrisonerTagRespVO;
import com.rs.module.base.entity.pm.PrisonerTagDO;
import com.rs.module.base.service.pm.PrisonerTagService;
import com.rs.module.base.service.ry.tag.handle.enums.RyxxTagBusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CommonTagHandler {

    @Autowired
    PrisonerTagService prisonerTagService;

    // 判断是否可以处理当前上下文
    public boolean canHandle(RyxxTagBusinessType ryxxTagBusinessType) {
        return true;
    }

    public void handle(String jgrybm, List<PrisonerTagRespVO> tags) {
        LambdaQueryWrapper<PrisonerTagDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PrisonerTagDO::getJgrybm, jgrybm);
        tags.addAll(BeanUtils.toBean(prisonerTagService.list(queryWrapper), PrisonerTagRespVO.class));
    }

    // 获取处理器支持的业务类型
    public RyxxTagBusinessType getSupportedBusinessType() {
        return RyxxTagBusinessType.SystemTag;
    }

}
