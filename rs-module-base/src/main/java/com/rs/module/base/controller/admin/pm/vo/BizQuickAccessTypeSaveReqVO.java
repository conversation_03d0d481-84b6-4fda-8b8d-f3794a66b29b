package com.rs.module.base.controller.admin.pm.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 实战平台-监管管理-业务快捷访问入口类型配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BizQuickAccessTypeSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("业务分类名称")
    @NotEmpty(message = "业务分类名称不能为空")
    private String name;

    @ApiModelProperty("业务分类标识")
    @NotEmpty(message = "业务分类标识不能为空")
    private String mark;

    @ApiModelProperty("排序Id")
    private Integer orderId;

    @ApiModelProperty("备注")
    private String remark;

}
