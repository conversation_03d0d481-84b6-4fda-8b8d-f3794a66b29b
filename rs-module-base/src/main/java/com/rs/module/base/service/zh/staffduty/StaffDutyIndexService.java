package com.rs.module.base.service.zh.staffduty;
import com.rs.module.base.controller.admin.zh.vo.staffduty.*;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;



public interface StaffDutyIndexService {

    /***
     * 值班管理首页列表
     * @param startTime
     * @param endTime
     * @return
     */
    List<DutyManageVO> indexListByDutyDate(Date startTime, Date endTime,Integer templateType);

    List<DutyManageHeaderVO> getIndexHeaderListByNowDay(Integer queryType,Integer templateType);

    void exportByDutyDate(Date startTime, Date endTime,Integer type, HttpServletResponse response) throws IOException;

    /***
     * 复制排版-日期过滤
     * @param startTime
     * @param endTime
     * @return
     */
    List<String> checkCopyDate(Date startTime, Date endTime,Integer templateType);

    /***
     * 复制排版-下一步校验
     * @param startTime
     * @param endTime
     * @return
     */
    Integer checkCopyDateNext(Date startTime, Date endTime,Integer templateType);

    /***
     * 复制排版-提交是否存在覆盖校验
     * @param startTime
     * @param endTime
     * @return
     */
    Boolean checkHasData(Date startTime, Date endTime,Integer templateType);

    /***
     * 复制排版-确认提交排版
     * @param dutyManageCopyDTO
     * @return
     */
    Integer copyData(DutyManageCopyDTO dutyManageCopyDTO, Integer templateType);
}
