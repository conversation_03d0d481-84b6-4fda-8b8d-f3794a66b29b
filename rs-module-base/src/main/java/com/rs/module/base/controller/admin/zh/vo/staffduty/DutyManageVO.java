package com.rs.module.base.controller.admin.zh.vo.staffduty;

import com.alibaba.fastjson.JSONArray;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "值班管理首页")
public class DutyManageVO {
  @ApiModelProperty(value = "值班表头列表")
  private List<DutyManageHeaderVO> headerList;

  @ApiModelProperty(value = "值班人员列表")
  private JSONArray personList;

  @ApiModelProperty(value = "模板编号")
  private String tempId;

  private Integer code;

  @ApiModelProperty("true=存在覆盖")
  private Boolean isCover;
}
