package com.rs.module.base.controller.admin.zh.vo.staffduty;

import com.alibaba.fastjson.JSONArray;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;


@ApiModel("排版管理-新增排版")
@Data
public class DutyManageSaveDTO {
    @ApiModelProperty(value = "模板编号")
    @NotBlank(message = "模板编号 不能为空")
    private String tempId;


    @ApiModelProperty(value = "值班人员列表")
    @NotEmpty(message = "值班人员列表 不能为空")
    private JSONArray personList;
}
