package com.rs.module.base.controller.admin.pm.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/4/28 13:55
 */
@ApiModel(description = "管理后台 - 所有在所人员列表 Response VO")
@Data
public class PrisonerListVwRespVO implements TransPojo {

    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("机构编码")
    private String orgCode;
    @ApiModelProperty("机构编号")
    private String orgName;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("人员编号")
    private String rybh;
    @ApiModelProperty("姓名")
    private String xm;
    @ApiModelProperty("证件类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_ZJLX")
    private String zjlx;
    @ApiModelProperty("证件号码")
    private String zjhm;
    @ApiModelProperty("性别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XB")
    private String xb;
    @ApiModelProperty("民族")
    @Trans(type = TransType.DICTIONARY, key = "ZD_MZ")
    private String mz;
    @ApiModelProperty("出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date csrq;
    @ApiModelProperty("国籍")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GJ")
    private String gj;
    @Trans(type = TransType.DICTIONARY, key = "ZD_RYZT")
    private String ryzt;
    @ApiModelProperty("档案编号")
    private String dabh;
    @ApiModelProperty("入所时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date rssj;

    @ApiModelProperty("入所天数")
    private Long rsts;

    @ApiModelProperty("是否病号")
    private Boolean sfbh;

    @ApiModelProperty("案件编号")
    private String ajbh;
    @ApiModelProperty("案件类别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_AJLB")
    private String ajlb;
    @ApiModelProperty("最终处置结果")
    private String zzczjg;
    @ApiModelProperty("监室号")
    private String jsh;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("监区id")
    private String areaId;
    @ApiModelProperty("床位号")
    private String cwh;
    @ApiModelProperty("正面照片")
    private String frontPhoto;
    @ApiModelProperty("被监管人员类型（01：在押人员，02：被拘人员，03：戒毒人员，99：其他）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_BJGRYLX")
    private String bjgrylx;
    @ApiModelProperty("年龄")
    private Integer age;
    @ApiModelProperty("关押期限")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gyqx;
    @ApiModelProperty("籍贯")
    @Trans(type = TransType.DICTIONARY, key = "ZD_HJ")
    private String jg;
    @ApiModelProperty("涉嫌罪名")
    @Trans(type = TransType.DICTIONARY, key = "ZD_SXZM")
    private String sxzm;
    @Trans(type = TransType.DICTIONARY, key = "ZD_SSJD")
    @ApiModelProperty("诉讼环节")
    private String sshj;

    @ApiModelProperty("本月奖励数")
    private Integer byjls;
    @ApiModelProperty("本月违规数")
    private Integer bywgs;
    @ApiModelProperty("本月惩罚数")
    private Integer bycfs;

}
