package com.rs.module.base.service.pm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.cache.RedisClient;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.BizDeskCardSaveReqVO;
import com.rs.module.base.dao.pm.BizDeskCardDao;
import com.rs.module.base.entity.pm.BizBrowsingPermissionDO;
import com.rs.module.base.entity.pm.BizDeskCardDO;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;


/**
 * 实战平台-监管管理-业务总台卡片配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BizDeskCardServiceImpl extends BaseServiceImpl<BizDeskCardDao, BizDeskCardDO> implements BizDeskCardService {

    @Resource
    private BizDeskCardDao bizDeskCardDao;

    @Override
    public BizDeskCardDO createBizDeskCard(BizDeskCardSaveReqVO createReqVO) {
        // 插入
        BizDeskCardDO bizDeskCard = BeanUtils.toBean(createReqVO, BizDeskCardDO.class);
        bizDeskCardDao.insert(bizDeskCard);
        // 返回
        return bizDeskCard;
    }

    @Override
    public BizDeskCardDO updateBizDeskCard(BizDeskCardSaveReqVO updateReqVO) {
        // 校验存在
        validateBizDeskCardExists(updateReqVO.getId());
        // 更新
        BizDeskCardDO updateObj = BeanUtils.toBean(updateReqVO, BizDeskCardDO.class);
        bizDeskCardDao.updateById(updateObj);
        return updateObj;
    }

    @Override
    public void deleteBizDeskCard(String id) {
        // 校验存在
        validateBizDeskCardExists(id);
        // 删除
        bizDeskCardDao.deleteById(id);
    }

    private void validateBizDeskCardExists(String id) {
        if (bizDeskCardDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-业务总台卡片配置数据不存在");
        }
    }

    @Override
    public BizDeskCardDO getBizDeskCard(String id) {
        return bizDeskCardDao.selectById(id);
    }

}
