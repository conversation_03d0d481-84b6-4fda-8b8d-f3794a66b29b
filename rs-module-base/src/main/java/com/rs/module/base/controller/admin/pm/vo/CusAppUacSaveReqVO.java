package com.rs.module.base.controller.admin.pm.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 自定义应用权限设置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = false)
public class CusAppUacSaveReqVO extends BaseVO{

    @ApiModelProperty(value = "自定义应用ID", required = true)
    @NotEmpty(message = "自定义应用ID不能为空")
    private String yyid;

    @ApiModelProperty(value = "权限类型: 01 role   02 area   03 org")
    @NotBlank(message = "权限类型不能为空")
    private String qxlx;

    @ApiModelProperty("权限ID(01时存角色ID，02时存区域ID，03时存机构ID),多个权限ID用逗号分隔")
    private String qxids;

}
