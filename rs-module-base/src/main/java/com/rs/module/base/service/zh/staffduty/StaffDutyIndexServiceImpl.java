package com.rs.module.base.service.zh.staffduty;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.base.controller.admin.zh.vo.staffduty.*;
import com.rs.module.base.dao.zh.StaffDutyRecordDao;
import com.rs.module.base.dao.zh.StaffDutyRecordPersonDao;
import com.rs.module.base.entity.zh.StaffDutyRecordDO;
import com.rs.module.base.entity.zh.StaffDutyRecordPersonDO;
import com.rs.module.base.util.StaffDutyDateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.*;

/**
 *
 */
@Slf4j
@Service
public class StaffDutyIndexServiceImpl implements StaffDutyIndexService {
    @Resource
    private StaffDutyRecordDao staffDutyRecordDao;

    @Resource
    private StaffDutyRecordPersonDao personDao;

    //@Value("${file-url-prefix:}")
    private String pathPrefix;


    @Override
    public List<DutyManageVO> indexListByDutyDate(Date startTime, Date endTime,Integer templateType) {
        List<DutyManageVO> dutyManageVOS = new ArrayList<>();
        List<Map<String, Object>> list = new ArrayList<>();
        int day = 0;
        try {
            day = StaffDutyDateUtils.daysBetween(startTime, endTime);//获取开始时间和结束时间间隔的日期天数
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        for (int i = 0; i <= day; i++) {
            Map<String, Object> tempMap = new LinkedHashMap<>();
            Date date = StaffDutyDateUtils.addDateDays(startTime, i);//循环拿到间隔的日期
            //根据日期到同步每日模板的使用表拿到当天的使用的模板id和日期
            String format = StaffDutyDateUtils.format(date, StaffDutyDateUtils.DATE_PATTERN);
            Map<String, Object> temIdAndDateByDate = staffDutyRecordDao.getTemIdAndDateByDate(format, SessionUserUtil.getSessionUser().getOrgCode(), templateType);
            if (temIdAndDateByDate != null) {
                String tempId = temIdAndDateByDate.get("temp_id").toString();
                String tempDate = temIdAndDateByDate.get("date").toString();
                ArrayList<String> tempDateList = new ArrayList<>();
                tempDateList.add(tempDate);
                //添加模板id和日期到map里面
                tempMap.put("id", tempId);
                tempMap.put("date", tempDateList);
            }
            if (date.after(new Date())) {//如果日期大于当前时间 则使用当前最新模板到这些日期里面
                StaffDutyTemplateRespVO nowTempId = staffDutyRecordDao.getNowTempId(SessionUserUtil.getSessionUser().getOrgCode(), templateType);
                ArrayList<String> tempDateList = new ArrayList<>();
                tempDateList.add(StaffDutyDateUtils.format(date, StaffDutyDateUtils.DATE_PATTERN));
                //添加模板id和日期到map里面
                if(nowTempId != null ){
                    tempMap.put("id", nowTempId.getId());
                }
                tempMap.put("date", tempDateList);
            }
            list.add(tempMap);
        }
        //合并相同的模板编号的key，并把日期进行相加 组装新的集合
        List<Map<String, Object>> maps = combineMap("id", list);
        for (Map<String, Object> map : maps) {
            List<String> date = (List<String>) map.get("date");
            Collections.sort(date);//对每个模板的日期进行正序排序
            String tempId = map.get("id").toString();
            DutyManageVO dutyManageVO = new DutyManageVO();
            dutyManageVO.setTempId(tempId);
            //生成表头部分
            List<DutyManageHeaderVO> headerList = staffDutyRecordDao.getHeaderList(tempId);
            JSONArray personList = new JSONArray();
            if (map != null) {
                for (String tempDate : date) {
                    JSONObject jsonObject = new JSONObject();
                    tempDate = tempDate.substring(0, 10);
                    jsonObject.put("date", tempDate);
                    //找到该模板的所有角色绑定的唯一key
                    List<String> postKeyByTempId = staffDutyRecordDao.getPostKeyByTempId(tempId);
                    for (String postKeyId : postKeyByTempId) {
                        //查找绑定的唯一key对应的值班人员进行匹配
                        List<StaffDutyRecordPersoRespVO> personVOS = staffDutyRecordDao.getPersonList(postKeyId, tempDate);
                        JSONArray personArray = new JSONArray();
                        for (StaffDutyRecordPersoRespVO vo : personVOS) {
                            JSONObject personObj = new JSONObject();
                            personObj.put("policeType", vo.getPoliceType());
                            personObj.put("policeId", vo.getPoliceId());
                            personObj.put("policeName", vo.getPoliceName());
                            personObj.put("postKey", vo.getPostKey());
                            personArray.add(personObj);
                        }
                        jsonObject.put(postKeyId, personArray);
                    }
                    //添加值班人员到集合里面
                    personList.add(jsonObject);
                }
            }
            dutyManageVO.setHeaderList(headerList);
            dutyManageVO.setPersonList(personList);
            dutyManageVOS.add(dutyManageVO);
        }
        return dutyManageVOS;
    }

    @Override
    public List<DutyManageHeaderVO> getIndexHeaderListByNowDay(Integer queryType,Integer templateType) {
        List<DutyManageHeaderVO> indexHeaderListByNowDay = new ArrayList<>();
        String postId = SessionUserUtil.getSessionUser().getPost();//岗位编号
        Integer type = 0;
        //需要判定是否是所领导
        /*if((StringUtils.isNoneEmpty(postId) && postId.equals(RoleConstants.rolepost_sld)) || Objects.equals(1,queryType)){
            type = 1;
        }*/
        StaffDutyTemplateRespVO nowTempId = staffDutyRecordDao.getNowTempId(SessionUserUtil.getSessionUser().getOrgCode(), templateType);
        if(null == nowTempId || StringUtils.isEmpty(nowTempId.getId())) return indexHeaderListByNowDay;
        indexHeaderListByNowDay = staffDutyRecordDao.getIndexHeaderListByNowDay(nowTempId.getId(),type,postId);
        for(DutyManageHeaderVO vo:indexHeaderListByNowDay){
            List<DutyManageSubPostVO> subPostList = vo.getSubPostList();
            for(DutyManageSubPostVO subPostVO:subPostList){
                List<DutyManageSubPostTimeVO> subPostTimeVOS = subPostVO.getSubPostTimeVOS();
                for(DutyManageSubPostTimeVO timeVO:subPostTimeVOS){
                    if(StringUtils.isNotBlank(timeVO.getPhoto())){
                        timeVO.setPhoto(pathPrefix+timeVO.getPhoto());
                    }
                }
            }
        }
        return indexHeaderListByNowDay;
    }


    /**
     * @param combineField 根据该字段进行合并
     * @param dataList     原始数据
     * @return
     */
    public static List<Map<String, Object>> combineMap(String combineField, List<Map<String, Object>> dataList) {
        //用于存放最后的结果
        List<Map<String, Object>> countList = new ArrayList<Map<String, Object>>();
        for (int i = 0; i < dataList.size(); i++) {
            Object o = dataList.get(i).get(combineField);
            if (o != null) {
                String oldCombineField = String.valueOf(o);
                int flag = 0;//0为新增数据，1为增加count
                for (int j = 0; j < countList.size(); j++) {
                    String newCombineField = String.valueOf(countList.get(j).get(combineField));
                    if (oldCombineField.equals(newCombineField)) {
                        //做累加的操作
                        List<String> date = (List<String>) dataList.get(i).get("date");
                        List<String> date2 = (List<String>) countList.get(j).get("date");
                        date.addAll(date2);
                        countList.get(j).put("date", date);
                        flag = 1;
                        break;
                    }
                }
                if (flag == 0) {
                    countList.add(dataList.get(i));
                }
            }

        }
        return countList;
    }


    @Override
    public void exportByDutyDate(Date startTime, Date endTime,Integer templateType, HttpServletResponse response) throws IOException {
        //设置表头和单元格样式
        //List<DutyManageVO> dutyManageVOS =  getDutyManageTest();
        List<DutyManageVO> dutyManageVOS = indexListByDutyDate(startTime, endTime, templateType);
        // 设置响应头
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里设置文件名，建议使用 URLEncoder 处理中文
            String fileName = StrUtil.format("值班安排表（{}-{}）.xlsx", DateUtil.formatDate(startTime), DateUtil.formatDate(endTime));
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName);
            // 创建测试数据
            int GLOBAL_COLUMN_WIDTH = 25;
            // 使用 try-with-resources 自动关闭 writer 和输出流
            try (BigExcelWriter writer = ExcelUtil.getBigWriter(); ServletOutputStream out = response.getOutputStream()) {

                // 获取工作簿和样式创建工厂
                Workbook workbook = writer.getWorkbook();
                CreationHelper createHelper = workbook.getCreationHelper();
                // 定义日期格式
                CellStyle dateCellStyle = workbook.createCellStyle();
                dateCellStyle.setDataFormat(createHelper.createDataFormat().getFormat("yyyy-MM-dd"));
                // 获取当前工作表
                int sheetIndex = 0;
                for (DutyManageVO dutyManageVO : dutyManageVOS) {
                    List<List<String>> rows = new ArrayList<>();
                    String sheetName = staffDutyRecordDao.getTempNameById(dutyManageVO.getTempId(), templateType);
                    // 如果不是第一个Sheet，则创建新Sheet
                    if (sheetIndex > 0) {
                        writer.setSheet(writer.getWorkbook().createSheet(sheetName));
                    } else {
                        // 第一个Sheet使用默认创建的，只需重命名
                        writer.renameSheet(sheetName);
                    }
                    Sheet sheet = writer.getSheet();
                    List<DutyManageHeaderVO> headerList = dutyManageVO.getHeaderList();
                    //表头
                    List<String> headerListObject = new ArrayList<>();
                    headerListObject.add("值班岗位");
                    headerListObject.add("值班角色");
                    headerListObject.add("班次名称");
                    headerListObject.add("班次时间");
                    headerListObject.addAll(com.rs.util.DateUtil.getDatesBetween(startTime,  endTime));
                    rows.add(headerListObject);
                    //表头信息
                    List<String> headerListWeekdaysObject = new ArrayList<>();
                    headerListWeekdaysObject.add("值班岗位");
                    headerListWeekdaysObject.add("值班角色");
                    headerListWeekdaysObject.add("班次名称");
                    headerListWeekdaysObject.add("班次时间");
                    headerListWeekdaysObject.addAll(com.rs.util.DateUtil.getWeekdaysBetween(startTime,  endTime));
                    rows.add(headerListWeekdaysObject);

                    for (DutyManageHeaderVO dutyManageHeaderVO : headerList) {
                        if(CollUtil.isNotEmpty(dutyManageHeaderVO.getSubPostList())){
                            for (DutyManageSubPostVO dutyManageSubPostVO : dutyManageHeaderVO.getSubPostList()) {
                                String subpost = StrUtil.isNotBlank( dutyManageSubPostVO.getSubPost()) ?  dutyManageSubPostVO.getSubPost() : StrUtil.EMPTY;
                                List<String> tempArray = new ArrayList<>();
                                for (DutyManageSubPostTimeVO subPostTimeVO : dutyManageSubPostVO.getSubPostTimeVOS()) {
                                    //值班人员信息
                                    JSONArray array = dutyManageVO.getPersonList();
                                    tempArray.add(dutyManageHeaderVO.getPost());
                                    //值班角色
                                    tempArray.add(subpost);
                                    //值班班次
                                    tempArray.add(subPostTimeVO.getDutyShift());
                                    tempArray.add(subPostTimeVO.getTime());
                                    if(CollUtil.isNotEmpty(array)){
                                        for (Object o : array) {
                                            //key是日期  Object
                                            JSONObject obj = (JSONObject) o;
                                            JSONArray jsonArray = obj.getJSONArray(subPostTimeVO.getPostKey());
                                            StringBuffer policeNameStr = new StringBuffer();
                                            for (Object oduty : jsonArray) {
                                                JSONObject jsonObject = (JSONObject) oduty;
                                                if(policeNameStr.length() > 0) {
                                                    policeNameStr.append("、");
                                                }
                                                policeNameStr.append(jsonObject.get("policeName"));
                                            }
                                            //值班人员姓名
                                            tempArray.add(policeNameStr.toString());
                                        }
                                    }
                                    rows.add(tempArray);
                                    tempArray = new ArrayList<>();
                                }
                            }
                        }


                    }

                    // 获取最大列数（基于第一行数据）
                    int maxColumnIndex = rows.isEmpty() ? 0 : rows.get(0).size();

                    // 全局统一设置列宽
                    for (int i = 0; i < maxColumnIndex; i++) {
                        sheet.setColumnWidth(i, GLOBAL_COLUMN_WIDTH * 256);
                    }
                    // 前四列，第一行和第二行合并
                    sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));
                    sheet.addMergedRegion(new CellRangeAddress(0, 1, 1, 1));
                    sheet.addMergedRegion(new CellRangeAddress(0, 1, 2, 2));
                    sheet.addMergedRegion(new CellRangeAddress(0, 1, 3, 3));
                    sheetIndex++;

                    for (List<String> row : rows) {
                        while (row.size() != maxColumnIndex && row.size() < maxColumnIndex){
                            row.add(StrUtil.EMPTY);
                        }
                    }
                    mergeCellsInColumn(sheet, rows, 0);
                    mergeCellsInColumn(sheet, rows, 1);

                    // 写入数据
                    writer.write(rows, false);
                }
                // 将 Excel 写入响应输出流
                writer.flush(out, true);

            }


        } catch (Exception e) {
          log.error("导出Excel异常", e);
          throw new RuntimeException("导出Excel数据异常！");
            // 这里可以添加错误处理逻辑，例如返回错误响应
        }

    }



    @Override
    public List<String> checkCopyDate(Date startTime, Date endTime,Integer templateType) {
        String tempId = staffDutyRecordDao.getTempId(SessionUserUtil.getSessionUser().getOrgCode(), templateType);
        List<String> dateList = new ArrayList<>();
        int day = 0;
        //遍历日期集合
        try {
            day = StaffDutyDateUtils.daysBetween(startTime, endTime);
            for (int i = 0; i <= day; i++) {
                Date date = StaffDutyDateUtils.addDateDays(startTime, i);//循环拿到间隔的日期
                //List<Integer> checkCopyDate = staffDutyRecordDao.checkCopyDate(date, tempId);
                if (date.before(new Date()) && !StaffDutyDateUtils.format(date, StaffDutyDateUtils.DATE_PATTERN).equals(StaffDutyDateUtils.format(new Date(), StaffDutyDateUtils.DATE_PATTERN))) {
                    dateList.add(StaffDutyDateUtils.format(date, StaffDutyDateUtils.DATE_PATTERN));
                    /*if (checkCopyDate.isEmpty()) {
                        dateList.add(StaffDutyDateUtils.format(date, StaffDutyDateUtils.DATE_PATTERN));
                    } else {
                        List<Integer> checkCopyPerson = staffDutyRecordDao.checkRecordByDate(date, tempId);
                        if (checkCopyPerson.isEmpty()) {
                            dateList.add(StaffDutyDateUtils.format(date, StaffDutyDateUtils.DATE_PATTERN));
                        }
                    }*/
                } else {
                    List<Integer> checkCopyPerson = staffDutyRecordDao.checkRecordByDate(date, tempId);
                    if (checkCopyPerson.isEmpty()) {
                        dateList.add(StaffDutyDateUtils.format(date, StaffDutyDateUtils.DATE_PATTERN));
                    }
                }

            }
        } catch (ParseException e) {
            log.info(e.getMessage());
            throw new RuntimeException(e);
        }
        return dateList;
    }

    @Override
    public Integer checkCopyDateNext(Date startTime, Date endTime,Integer templateType) {
        String tempId = staffDutyRecordDao.getTempId(SessionUserUtil.getSessionUser().getOrgCode(), templateType);
        int day = 0;
        //遍历日期集合
        try {
            day = StaffDutyDateUtils.daysBetween(startTime, endTime);
            for (int i = 0; i <= day; i++) {
                Date date = StaffDutyDateUtils.addDateDays(startTime, i);//循环拿到间隔的日期
                //String tempHisId = staffDutyRecordDao.checkCopyDateNext(date, SessionUserUtil.getSessionUser().getOrgCode());
                List<Integer> result = staffDutyRecordDao.checkRecordByDate(date, tempId);
                if (result.isEmpty()) {
                    return -2;
                }
               /* if (date.before(new Date()) && !StaffDutyDateUtils.format(date, StaffDutyDateUtils.DATE_PATTERN).equals(StaffDutyDateUtils.format(new Date(), StaffDutyDateUtils.DATE_PATTERN))) {
                    if (!tempId.equals(tempHisId)) {
                        return -1;
                    }
                }*/
            }
        } catch (ParseException e) {
            log.info(e.getMessage());
            throw new RuntimeException(e);
        }
        return 1;
    }

    @Override
    public Boolean checkHasData(Date startTime, Date endTime, Integer templateType) {
        String tempId = staffDutyRecordDao.getTempId(SessionUserUtil.getSessionUser().getOrgCode(), templateType);
        int day = 0;
        //遍历日期集合
        try {
            day = StaffDutyDateUtils.daysBetween(startTime, endTime);
            for (int i = 0; i <= day; i++) {
                Date date = StaffDutyDateUtils.addDateDays(startTime, i);//循环拿到间隔的日期
                List<Integer> result = staffDutyRecordDao.checkRecordByDate(date, tempId);
                if (!result.isEmpty()) {
                    return true;
                }
            }
        } catch (ParseException e) {
            log.info(e.getMessage());
            throw new RuntimeException(e);
        }
        return false;
    }

    @Override
    public Integer copyData(DutyManageCopyDTO dutyManageCopyDTO,Integer templateType) {
        List<String> recordIdList = new ArrayList<>();
        //校验数据
        if (dutyManageCopyDTO.getTargetStartTime().before(new Date()) && !StaffDutyDateUtils.format(dutyManageCopyDTO.getTargetStartTime(), StaffDutyDateUtils.DATE_PATTERN).equals(StaffDutyDateUtils.format(new Date(), StaffDutyDateUtils.DATE_PATTERN))) {
            return -2;
        }
        String tempId = staffDutyRecordDao.getTempId(dutyManageCopyDTO.getOrgCode(), templateType);
        //清除之前排版数据
        int day = 0;
        int day2 = 0;
        //遍历日期集合
        try {
            day = StaffDutyDateUtils.daysBetween(dutyManageCopyDTO.getTargetStartTime(), dutyManageCopyDTO.getTargetEndTime());
            day2 = StaffDutyDateUtils.daysBetween(dutyManageCopyDTO.getSourceStartTime(), dutyManageCopyDTO.getSourceEndTime());
            if (day != day2) {//如果源排版和目标排班数据长度不一致提醒
                return -1;
            }
            for (int i = 0; i <= day; i++) {
                Date date = StaffDutyDateUtils.addDateDays(dutyManageCopyDTO.getTargetStartTime(), i);//循环拿到间隔的日期 源排班开始日期
                Date date2 = StaffDutyDateUtils.addDateDays(dutyManageCopyDTO.getSourceStartTime(), i);//循环拿到间隔的日期 目标排班开始日期
                //插入源排班对应的排班数据

                StaffDutyRecordRespVO recordByDate = staffDutyRecordDao.getRecordByDate(date2, tempId);
                if (recordByDate != null) {
                    //插入值班日期数据
                    StaffDutyRecordDO recordEntity = new StaffDutyRecordDO();
                    recordEntity.setDutyDate(date);
                    //recordEntity.setDelFlag(1);
                    recordEntity.setTempId(tempId);
                    this.staffDutyRecordDao.insert(recordEntity);
                    recordIdList.add(recordEntity.getId());
                    //插入民警数据
                    List<StaffDutyRecordPersoRespVO> personByRecordId = staffDutyRecordDao.getPersonByRecordId(recordByDate.getId());
                    for (StaffDutyRecordPersoRespVO personVO : personByRecordId) {
                        StaffDutyRecordPersonDO personEntity = new StaffDutyRecordPersonDO();
                        personEntity.setPoliceId(personVO.getPoliceId());
                        personEntity.setRecordId(recordEntity.getId());
                        personEntity.setPoliceType(personVO.getPoliceType());
                        personEntity.setPoliceName(personVO.getPoliceName());
                        personEntity.setPostKey(personVO.getPostKey());
                        this.personDao.insert(personEntity);
                    }

                }

            }
            //清除值班日期表数据
            for (String id : recordIdList) {
                StaffDutyRecordRespVO delRecordById = staffDutyRecordDao.getDelRecordById(id);
                staffDutyRecordDao.delRecordByDate(delRecordById.getTempId(), delRecordById.getDutyDate());
                staffDutyRecordDao.updateRecordById(id);
                staffDutyRecordDao.delPersonByByRecordId(delRecordById.getTempId(), delRecordById.getDutyDate());
            }

        } catch (ParseException e) {
            log.info(e.getMessage());
            throw new RuntimeException(e);
        }
        return 1;
    }



    /**
     * 合并指定列中值相同的连续单元格，避免重复合并
     * @param sheet 工作表
     * @param data 数据列表
     * @param columnIndex 列索引
     */
    private static void mergeCellsInColumn(Sheet sheet, List<List<String>> data, int columnIndex) {
        if (data.isEmpty() || columnIndex < 0) return;

        int startRow = 0; // 从第1行开始（索引0）
        int endRow = 0;

        // 遍历数据，查找需要合并的单元格
        for (int i = 1; i < data.size(); i++) {
            List<?> currentRow = data.get(i);
            List<?> previousRow = data.get(i - 1);

            // 检查当前行和上一行的指定列是否相同
            boolean isSame = isCellValueEqual(currentRow, previousRow, columnIndex);

            if (isSame) {
                // 如果相同，则扩大合并范围
                endRow = i;
            } else {
                // 如果不同，则执行合并并重置范围
                if (endRow > startRow) {
                    // 检查是否存在重叠的合并区域
                    if (!hasOverlappingMergedRegion(sheet, startRow, endRow, columnIndex)) {
                        // 合并单元格
                        sheet.addMergedRegion(new CellRangeAddress(startRow, endRow, columnIndex, columnIndex));
                    }
                }
                // 重置起始行
                startRow = i;
                endRow = i;
            }
        }

        // 处理最后一组可能的合并
        if (endRow > startRow) {
            if (!hasOverlappingMergedRegion(sheet, startRow, endRow, columnIndex)) {
                sheet.addMergedRegion(new CellRangeAddress(startRow, endRow, columnIndex, columnIndex));
            }
        }
    }

    /**
     * 检查是否存在与指定区域重叠的合并单元格
     */
    private static boolean hasOverlappingMergedRegion(Sheet sheet, int startRow, int endRow, int column) {
        int numMergedRegions = sheet.getNumMergedRegions();
        for (int i = 0; i < numMergedRegions; i++) {
            CellRangeAddress mergedRegion = sheet.getMergedRegion(i);
            // 检查是否有重叠
            if (mergedRegion.getFirstColumn() <= column && mergedRegion.getLastColumn() >= column) {
                if (mergedRegion.getFirstRow() <= endRow && mergedRegion.getLastRow() >= startRow) {
                    return true; // 存在重叠
                }
            }
        }
        return false; // 不存在重叠
    }

    /**
     * 检查两行指定列的值是否相等
     */
    private static boolean isCellValueEqual(List<?> row1, List<?> row2, int column) {
        if (row1 == null || row2 == null || column >= row1.size() || column >= row2.size()) {
            return false;
        }
        Object value1 = row1.get(column);
        Object value2 = row2.get(column);
        return (value1 == null && value2 == null) || (value1 != null && value1.equals(value2));
    }



    /**
     * 模拟数据测试
     * <AUTHOR>
     * @date 2025/7/4 10:05
     * @param []
     * @return java.util.List<com.rs.module.base.controller.admin.zh.vo.staffduty.DutyManageVO>
     */
    private List<DutyManageVO> getDutyManageTest(){
        //String duty = "[{\"headerList\":[{\"post\":\"巡控\",\"hasSubPost\":true,\"subPostList\":[{\"subPost\":\"巡控\",\"dutyPostId\":\"9\",\"subPostTimeVOS\":[{\"time\":\"00:00-23:59\",\"dutyShift\":\"全天\",\"postKey\":\"DUTY202507022102055898\",\"policeName\":null,\"userId\":null,\"policeType\":null,\"photo\":null}]}]}],\"personList\":[{\"date\":\"2025-07-04\",\"DUTY202507022102055898\":[{\"policeId\":\"5df79dcee95947fe8e0490c60f977d0a\",\"postKey\":\"DUTY202507022102055898\",\"policeName\":\"阮铁东\",\"policeType\":1}]},{\"date\":\"2025-07-05\",\"DUTY202507022102055898\":[{\"policeId\":\"2ffdf1d32485496cafd0efd2296d09e3\",\"postKey\":\"DUTY202507022102055898\",\"policeName\":\"孙喜强\",\"policeType\":1}]},{\"date\":\"2025-07-06\",\"DUTY202507022102055898\":[]},{\"date\":\"2025-07-07\",\"DUTY202507022102055898\":[{\"policeId\":\"83ea136621ae4281940ca4f7cd39036e\",\"postKey\":\"DUTY202507022102055898\",\"policeName\":\"陈波\",\"policeType\":1}]},{\"date\":\"2025-07-08\",\"DUTY202507022102055898\":[{\"policeId\":\"5df79dcee95947fe8e0490c60f977d0a\",\"postKey\":\"DUTY202507022102055898\",\"policeName\":\"阮铁东\",\"policeType\":1}]},{\"date\":\"2025-07-09\",\"DUTY202507022102055898\":[]},{\"date\":\"2025-07-10\",\"DUTY202507022102055898\":[]}],\"tempId\":\"1940938021398646784\",\"code\":null,\"isCover\":null}]";
        String duty = "[{\"headerList\":[{\"post\":\"巡控\",\"hasSubPost\":true,\"subPostList\":[{\"subPost\":\"巡控1\",\"dutyPostId\":\"9\",\"subPostTimeVOS\":[{\"time\":\"00:00-23:59\",\"dutyShift\":\"全天\",\"postKey\":\"DUTY202507041509127249\",\"policeName\":null,\"userId\":null,\"policeType\":null,\"photo\":null}]},{\"subPost\":\"巡控2\",\"dutyPostId\":\"10\",\"subPostTimeVOS\":[{\"time\":\"00:00-06:00\",\"dutyShift\":\"夜班\",\"postKey\":\"DUTY202507041509127128\",\"policeName\":null,\"userId\":null,\"policeType\":null,\"photo\":null},{\"time\":\"07:00-18:00\",\"dutyShift\":\"早班\",\"postKey\":\"DUTY202507041509128697\",\"policeName\":null,\"userId\":null,\"policeType\":null,\"photo\":null}]}]},{\"post\":\"医务\",\"hasSubPost\":true,\"subPostList\":[{\"subPost\":\"医务角色1\",\"dutyPostId\":\"10\",\"subPostTimeVOS\":[{\"time\":\"01:02-23:03\",\"dutyShift\":\"早班\",\"postKey\":\"DUTY202507041509123561\",\"policeName\":null,\"userId\":null,\"policeType\":null,\"photo\":null}]}]}],\"personList\":[{\"date\":\"2025-07-04\",\"DUTY202507041509127249\":[],\"DUTY202507041509127128\":[{\"policeId\":\"83ea136621ae4281940ca4f7cd39036e\",\"postKey\":\"DUTY202507041509127128\",\"policeName\":\"陈波\",\"policeType\":1}],\"DUTY202507041509128697\":[],\"DUTY202507041509123561\":[]},{\"date\":\"2025-07-05\",\"DUTY202507041509127249\":[],\"DUTY202507041509127128\":[],\"DUTY202507041509128697\":[{\"policeId\":\"733440346cb5409ebb1ba300aafbab37\",\"postKey\":\"DUTY202507041509128697\",\"policeName\":\"张东旭\",\"policeType\":1}],\"DUTY202507041509123561\":[]},{\"date\":\"2025-07-06\",\"DUTY202507041509127249\":[],\"DUTY202507041509127128\":[],\"DUTY202507041509128697\":[],\"DUTY202507041509123561\":[]},{\"date\":\"2025-07-07\",\"DUTY202507041509127249\":[],\"DUTY202507041509127128\":[{\"policeId\":\"5df79dcee95947fe8e0490c60f977d0a\",\"postKey\":\"DUTY202507041509127128\",\"policeName\":\"阮铁东\",\"policeType\":1},{\"policeId\":\"83ea136621ae4281940ca4f7cd39036e\",\"postKey\":\"DUTY202507041509127128\",\"policeName\":\"陈波\",\"policeType\":1},{\"policeId\":\"733440346cb5409ebb1ba300aafbab37\",\"postKey\":\"DUTY202507041509127128\",\"policeName\":\"张东旭\",\"policeType\":1},{\"policeId\":\"2ffdf1d32485496cafd0efd2296d09e3\",\"postKey\":\"DUTY202507041509127128\",\"policeName\":\"孙喜强\",\"policeType\":1}],\"DUTY202507041509128697\":[],\"DUTY202507041509123561\":[]},{\"date\":\"2025-07-08\",\"DUTY202507041509127249\":[],\"DUTY202507041509127128\":[],\"DUTY202507041509128697\":[],\"DUTY202507041509123561\":[]},{\"date\":\"2025-07-09\",\"DUTY202507041509127249\":[],\"DUTY202507041509127128\":[],\"DUTY202507041509128697\":[{\"policeId\":\"733440346cb5409ebb1ba300aafbab37\",\"postKey\":\"DUTY202507041509128697\",\"policeName\":\"张东旭\",\"policeType\":1}],\"DUTY202507041509123561\":[{\"policeId\":\"af680e841e5648ada7746bf1034084a3\",\"postKey\":\"DUTY202507041509123561\",\"policeName\":\"样板间\",\"policeType\":1}]},{\"date\":\"2025-07-10\",\"DUTY202507041509127249\":[{\"policeId\":\"733440346cb5409ebb1ba300aafbab37\",\"postKey\":\"DUTY202507041509127249\",\"policeName\":\"张东旭\",\"policeType\":1}],\"DUTY202507041509127128\":[],\"DUTY202507041509128697\":[],\"DUTY202507041509123561\":[]}],\"tempId\":\"1940938021398646784\",\"code\":null,\"isCover\":null}]";
        return JSONArray.parseArray(duty,DutyManageVO.class );
    }

}
