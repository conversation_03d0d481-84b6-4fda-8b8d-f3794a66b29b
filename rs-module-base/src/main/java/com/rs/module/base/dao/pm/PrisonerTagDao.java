package com.rs.module.base.dao.pm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.base.controller.admin.pm.vo.PrisonerTagListReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerTagPageReqVO;
import com.rs.module.base.entity.pm.PrisonerTagDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 实战平台-监管管理-监管人员标签 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface PrisonerTagDao extends IBaseDao<PrisonerTagDO> {


    default PageResult<PrisonerTagDO> selectPage(PrisonerTagPageReqVO reqVO) {
        Page<PrisonerTagDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrisonerTagDO> wrapper = new LambdaQueryWrapperX<PrisonerTagDO>()
                .eqIfPresent(PrisonerTagDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(PrisonerTagDO::getTagCode, reqVO.getTagCode())
                .likeIfPresent(PrisonerTagDO::getTagName, reqVO.getTagName())
                .eqIfPresent(PrisonerTagDO::getTagType, reqVO.getTagType());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(PrisonerTagDO::getAddTime);
        }
        Page<PrisonerTagDO> prisonerTagPage = selectPage(page, wrapper);
        return new PageResult<>(prisonerTagPage.getRecords(), prisonerTagPage.getTotal());
    }

    default List<PrisonerTagDO> selectList(PrisonerTagListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrisonerTagDO>()
                .eqIfPresent(PrisonerTagDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(PrisonerTagDO::getTagCode, reqVO.getTagCode())
                .likeIfPresent(PrisonerTagDO::getTagName, reqVO.getTagName())
                .eqIfPresent(PrisonerTagDO::getTagType, reqVO.getTagType())
                .orderByDesc(PrisonerTagDO::getAddTime));
    }


}
