package com.rs.module.base.controller.admin.pm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-监管管理-业务快捷访问入口配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BizQuickAccessSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("业务入口名称")
    @NotEmpty(message = "业务入口名称不能为空")
    private String name;

    @ApiModelProperty("业务入口标识")
    @NotEmpty(message = "业务入口标识不能为空")
    private String mark;

    @ApiModelProperty("业务分类id")
    @NotEmpty(message = "业务分类ID不能为空")
    private String typeId;

    @ApiModelProperty("业务分类标识")
    @NotEmpty(message = "业务分类标识不能为空")
    private String typeMark;

    @ApiModelProperty("访问地址")
    private String accessUrl;

    @ApiModelProperty("触发方式")
    @NotEmpty(message = "触发方式不能为空")
    private String triggerMethod;

    @ApiModelProperty("图标地址")
    private String iconUrl;

    @ApiModelProperty("弹窗名称")
    private String popName;

    @ApiModelProperty("是否启动浏览权限配置")
    @NotNull(message = "是否启动浏览权限配置不能为空")
    private Short isEnableBrowsingPermission;

    @ApiModelProperty("是否启用")
    @NotNull(message = "是否启用不能为空")
    private Short isEnable;

    @ApiModelProperty("排序Id")
    @NotNull(message = "排序Id不能为空")
    private Integer orderId;

    @ApiModelProperty("备注")
    private String remark;

}
