package com.rs.module.base.service.ry.fill;

import cn.hutool.extra.spring.SpringUtil;
import com.rs.module.base.service.ry.fill.handle.base.RyxxFillHandler;
import com.rs.module.base.service.ry.fill.handle.enums.RyxxFillBusinessType;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class RyxxHandlerFactory {

    public List<RyxxFillHandler> getHandler(RyxxFillBusinessType businessType) {
        List<RyxxFillHandler> list = new ArrayList<>();
        for (Class handler : businessType.getHandlers()) {
            list.add((RyxxFillHandler) SpringUtil.getBean(handler));
        }
        return list;
    }

}
