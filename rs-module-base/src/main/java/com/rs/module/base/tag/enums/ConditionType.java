package com.rs.module.base.tag.enums;

import lombok.Getter;

/**
 * 条件类型枚举
 * <AUTHOR>
 */
@Getter
public enum ConditionType {
    /**
     * 等于
     */
    EQUALS("等于"),

    /**
     * 不等于
     */
    NOT_EQUALS("不等于"),

    /**
     * 大于
     */
    GREATER_THAN("大于"),

    /**
     * 小于
     */
    LESS_THAN("小于"),

    /**
     * 大于等于
     */
    GREATER_EQUALS("大于等于"),

    /**
     * 小于等于
     */
    LESS_EQUALS("小于等于"),

    /**
     * 包含
     */
    CONTAINS("包含"),

    /**
     * 不包含
     */
    NOT_CONTAINS("不包含"),

    /**
     * SpEL表达式
     */
    SPEL("SpEL表达式"),

    /**
     * 自定义方法
     */
    CUSTOM("自定义方法"),

    /**
     * AND组合
     */
    AND("AND组合"),

    /**
     * OR组合
     */
    OR("OR组合"),

    /**
     * NOT组合
     */
    NOT("NOT组合");

    /**
     * 描述
     */
    private final String description;

    ConditionType(String description) {
        this.description = description;
    }
}
