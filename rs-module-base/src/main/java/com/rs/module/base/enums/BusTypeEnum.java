package com.rs.module.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务类型枚举
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Getter
@AllArgsConstructor
public enum BusTypeEnum {

	//业务
	YEWU_RSJL("0101", "入所记录"),
	YEWU_SLGL("0102", "死亡管理"),
	YEWU_XKDJ("0103", "巡视登记"),
	YEWU_FXPG("0104", "风险评估"),
	YEWU_CSJL("0105", "出所记录"),
	YEWU_THJY("0106", "谈话教育"),
	YEWU_TIXUN("0107", "提讯"),
	YEWU_TIJIE("0108", "提解"),
	YEWU_LSHJ("0109", "律师会见"),
	YEWU_JSHJ("0110", "家属会见"),
	YEWU_XJSY("0111", "戒具使用"),
	YEWU_JSTZ("0112", "监室调整"),
	YEWU_MDMGL("0113", "面对面管理"),
	YEWU_AQJC("0114", "安全检查"),
	YEWU_JIASHI("0115", "假释"),
	YEWU_JIANXING("0116", "减刑"),
	YEWU_JSZB("0117", "监室值班"),
	YEWU_CWGL("0118", "床位管理"),
	YEWU_TSCSQ("0119", "特殊餐申请"),
	YEWU_XXYGL("0120", "信息员布建"),
	YEWU_WXGD("0121", "维修管理"),
	YEWU_ZDRYGZ("0122", "重点人员关注"),
	YEWU_ZYJWZX("0123", "暂予监外执行"),
	YEWU_CF("0124", "惩罚"),
	YEWU_DDGY("0125", "单独关押"),
	YEWU_JB("0126", "禁闭"),
	YEWU_YG("0127", "严管"),
	YEWU_JSDM("0128", "监室点名"),

	YEWU_TRJS("0128", "调入监室"),
	YEWU_TCJS("0129", "调出监室"),

	YEWU_XXYCX("0130", "信息员撤销"),
	YEWU_JJJC("0131", "戒具解除"),

	YEWU_LINGSHI("0132","使馆/领事会见"),

	//YEWU_DANXIANGSHIPIN("0133","家属单向视频会见"),

	YEWU_JULIUSUOTIXUN("0134","提询"),
	YEWU_JIANGLIGUANLI("0135","奖励管理"),
	
	//购物采买
	YEWU_GWCM("0136", "购物采买"),

	//医疗
	YILIAO_RSJC("0201", "入所检查"),
	YILIAO_SNJY("0202", "所内就医"),
	YILIAO_SWJY("0203", "所外就医"),
	YILIAO_YPRK("0204", "药品入库"),
	YILIAO_YPCK("0205", "药品出库"),
	YILIAO_LSYZ("0206", "临时医嘱"),
	YILIAO_CQYZ("0207", "长期医嘱"),
	;

	//业务类型
	private String busType;

	//业务名称
	private String busName;
}
