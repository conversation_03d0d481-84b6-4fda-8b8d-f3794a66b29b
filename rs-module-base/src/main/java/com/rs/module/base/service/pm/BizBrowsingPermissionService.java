package com.rs.module.base.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.bsp.security.model.SessionUser;
import com.rs.module.base.entity.pm.BizBrowsingPermissionDO;

import java.util.List;
import java.util.Map;

/**
 * 实战平台-监管管理-业务浏览权限配置 Service 接口
 *
 * <AUTHOR>
 */
public interface BizBrowsingPermissionService extends IBaseService<BizBrowsingPermissionDO>{


    /**
     * 保存总台配置选项卡权限分配设置
     *
     * @param id    业务id
     * @param type     权限类型
     * @param idList 权限id列表
     */
    void saveCardPermissionData(String id, String mark, String type, List<String> idList);

    /**
     * 根据业务编号和类型获取权限信息
     * @param id    业务id
     * @param typeList
     * @return
     */
    Map<String, List<String>> findPermissionData(String id, List<String> typeList);

    /**
     * 获取业务id全部到权限
     * @param id    业务id
     * @return
     */
    Map<String, List<String>> findPermissionDataById(String id);


}
