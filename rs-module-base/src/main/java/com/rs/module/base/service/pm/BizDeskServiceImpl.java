package com.rs.module.base.service.pm;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.*;
import com.rs.module.base.dao.pm.BizBrowsingPermissionDao;
import com.rs.module.base.dao.pm.BizDeskCardDao;
import com.rs.module.base.dao.pm.BizDeskDao;
import com.rs.module.base.entity.pm.BizBrowsingPermissionDO;
import com.rs.module.base.entity.pm.BizDeskCardDO;
import com.rs.module.base.entity.pm.BizDeskDO;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;


/**
 * 实战平台-监管管理-业务总台配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BizDeskServiceImpl extends BaseServiceImpl<BizDeskDao, BizDeskDO> implements BizDeskService {

    @Resource
    private BizDeskDao bizDeskDao;
    @Resource
    private BizDeskCardDao bizDeskCardDao;
    @Resource
    private BizBrowsingPermissionDao bizBrowsingPermissionDao;

    @Override
    public BizDeskDO createBizDesk(BizDeskSaveReqVO createReqVO) {
        // 插入
        BizDeskDO bizDesk = BeanUtils.toBean(createReqVO, BizDeskDO.class);
        bizDeskDao.insert(bizDesk);
        // 返回
        return bizDesk;
    }

    @Override
    public BizDeskDO updateBizDesk(BizDeskSaveReqVO updateReqVO) {
        // 校验存在
        validateBizDeskExists(updateReqVO.getId());
        // 更新
        BizDeskDO updateObj = BeanUtils.toBean(updateReqVO, BizDeskDO.class);
        bizDeskDao.updateById(updateObj);
        return updateObj;
    }

    @Override
    public BizDeskDO getByMark(String mark) {
        return bizDeskDao.getByMark(mark);
    }

    @Override
    public void deleteBizDesk(String id) {
        // 校验存在
        validateBizDeskExists(id);
        // 删除
        bizDeskDao.deleteById(id);
    }

    private void validateBizDeskExists(String id) {
        if (bizDeskDao.selectById(id) == null) {
            throw new ServerException("总台配置不存在");
        }
    }

    @Override
    public BizDeskDO getBizDesk(String id) {
        return bizDeskDao.selectById(id);
    }

    @Override
    public List<BizDeskTreeNodeVO> findDeskTreeData() {
        // 查询总台配置
        List<BizDeskDO> deskList = list(new LambdaQueryWrapper<BizDeskDO>()
                .orderByAsc(BizDeskDO::getAddTime));
        // 查询总台选项卡配置
        List<BizDeskCardDO> deskCardList = bizDeskCardDao.selectList(
                new LambdaQueryWrapper<BizDeskCardDO>()
                        .orderByAsc(BizDeskCardDO::getBizDeskId, BizDeskCardDO::getOrderId));
        // 转化成树节点VO列表
        List<BizDeskTreeNodeVO> deskTreeNodeVOList = new ArrayList<>();
        List<String> parentIdList = new ArrayList<>();
        deskList.stream().forEach(desk -> {
            BizDeskRespVO deskRespVO = BeanUtil.copyProperties(desk, BizDeskRespVO.class);
            BizDeskTreeNodeVO deskTreeNodeVO = new BizDeskTreeNodeVO();
            deskTreeNodeVO.setId(deskRespVO.getId());
            deskTreeNodeVO.setName(deskRespVO.getName());
            deskTreeNodeVO.setBindData(deskRespVO);
            deskTreeNodeVOList.add(deskTreeNodeVO);
            parentIdList.add(deskRespVO.getId());
        });
        deskCardList.stream()
                .filter(deskCard -> parentIdList.contains(deskCard.getBizDeskId()))
                .forEach(deskCard -> {
                    BizDeskCardRespVO deskCardRespVO = BeanUtil.copyProperties(deskCard, BizDeskCardRespVO.class);
                    BizDeskTreeNodeVO deskTreeNodeVO = new BizDeskTreeNodeVO();
                    deskTreeNodeVO.setId(deskCardRespVO.getId());
                    deskTreeNodeVO.setName(deskCardRespVO.getName());
                    deskTreeNodeVO.setParentId(deskCardRespVO.getBizDeskId());
                    deskTreeNodeVO.setBindData(deskCardRespVO);
                    deskTreeNodeVOList.add(deskTreeNodeVO);
                });
        return deskTreeNodeVOList;
    }

    @Override
    public CommonResult buildDeskComponent(String mark) {
        BizDeskRespVO deskRespVO = getDeskByMark(mark);
        if(null == deskRespVO) {
            return error("未查询到总台信息");
        }
        List<BizDeskCardQxVO> cardList = getCardByDeskId(deskRespVO.getId());
        if(CollectionUtils.isEmpty(cardList)) {
            return error("");
        }
        JSONObject result = new JSONObject();
        List<BizDeskCardQxVO> powerCardList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(cardList)) {
            SessionUser sessionUser =  SessionUserUtil.getSessionUser();
            if(sessionUser.getIsAdmin()) { // 管理员，返回全部
                result.put("desk", deskRespVO);
                result.put("cardList", cardList);
                return success(result);
            }
            for(BizDeskCardQxVO item : cardList) {
                if(StringUtil.getBoolean(item.getIsEnableBrowsingPermission()+"")) {
                    JSONArray regArr = item.getRegArr();
                    JSONArray orgArr = item.getOrgArr();
                    JSONArray roleArr = item.getRoleArr();
                    if(regArr == null && orgArr == null && roleArr == null) {
                        continue;
                    }
                    String regCode = sessionUser.getRegCode();
                    String orgCode = sessionUser.getOrgCode();
                    String roleIds = sessionUser.getRoleIds();
                    boolean isRegPower = true;
                    boolean isOrgPower = true;
                    boolean isRolePower = true;
                    if(null != regArr) {
                        isRegPower = regArr.contains(regCode);
                    }
                    if(null != orgArr) {
                        isOrgPower = orgArr.contains(orgCode);
                    }
                    if(null !=  roleArr && StringUtil.isNotEmpty(roleIds)) {
                        isRolePower = false;
                        List<String> roleList = StrUtil.splitTrim(roleIds, ",");
                        for(String roleId : roleList) {
                            if(roleArr.contains(roleId)) {
                                isRolePower = true;
                                break;
                            }
                        }
                    }
                    if(isRegPower && isOrgPower && isRolePower) {
                        powerCardList.add(item);
                    }
                } else {
                    powerCardList.add(item);
                }
            }
        }
        result.put("desk", deskRespVO);
        result.put("cardList", powerCardList);
        return success(result);
    }


    /**
     * 根据总台标识查询总台配置
     * @param mark 总台标识
     * @return
     */
    private BizDeskRespVO getDeskByMark(String mark) {
        BizDeskDO bizDeskDO = bizDeskDao.getByMark(mark);
        if(null == bizDeskDO) {
            return null;
        }
        BizDeskRespVO deskRespVO = BeanUtil.toBean(bizDeskDO, BizDeskRespVO.class);
        return deskRespVO;
    }

    /**
     * 根据总台ID获取总台选项卡信息
     * @param bizDeskId 总台id
     * @return
     */
    private List<BizDeskCardQxVO> getCardByDeskId(String bizDeskId) {
        List<BizDeskCardQxVO> cardQxList = new ArrayList<>();
        List<BizDeskCardDO> cardList = bizDeskCardDao.getByBizId(bizDeskId);
        if (!CollectionUtils.isEmpty(cardList)) {
            List<String> cardIdList = cardList.stream().map(card -> card.getId()).collect(Collectors.toList());
            List<BizBrowsingPermissionDO> permissionList = bizBrowsingPermissionDao.getByBizIds(cardIdList);
            if (!CollectionUtils.isEmpty(permissionList)) {
                JSONObject json = new JSONObject();
                for (BizBrowsingPermissionDO permission : permissionList) {
                    String permissionType = permission.getPermissionType();
                    String permissionId = permission.getPermissionId();
                    String bizId = permission.getBizId();

                    JSONObject bizObj = json.getJSONObject(bizId);
                    if (null == bizObj) {
                        JSONObject permissionObj = new JSONObject();
                        JSONArray arr = new JSONArray();
                        arr.add(permissionId);
                        permissionObj.put(permissionType, arr);
                        json.put(bizId, permissionObj);
                    } else {
                        JSONArray arr = bizObj.getJSONArray(permissionType);
                        if (null == arr) {
                            arr = new JSONArray();
                            bizObj.put(permissionType, arr);
                        }
                        arr.add(permissionId);
                    }

                }
                for (BizDeskCardDO card : cardList) {
                    BizDeskCardQxVO cardVo = new BizDeskCardQxVO();
                    cardVo.setCardId(card.getId());
                    cardVo.setBizDeskId(bizDeskId);
                    cardVo.setCardName(card.getName());
                    cardVo.setCardUrl(card.getCardUrl());
                    cardVo.setOrderId(card.getOrderId());
                    cardVo.setIsEnableBrowsingPermission(card.getIsEnableBrowsingPermission());
                    cardVo.setUrlType(card.getUrlType());
                    if(StringUtil.getBoolean(card.getIsEnableBrowsingPermission()+"")) {
                        JSONObject cardObj = json.getJSONObject(card.getId());
                        if (null != cardObj) {
                            cardVo.setRegArr(cardObj.getJSONArray("02"));
                            cardVo.setRoleArr(cardObj.getJSONArray("01"));
                        }
                    }
                    cardQxList.add(cardVo);
                }
            } else {
                for (BizDeskCardDO card : cardList) {
                    BizDeskCardQxVO cardVo = new BizDeskCardQxVO();
                    cardVo.setCardId(card.getId());
                    cardVo.setBizDeskId(bizDeskId);
                    cardVo.setCardName(card.getName());
                    cardVo.setCardUrl(card.getCardUrl());
                    cardVo.setOrderId(card.getOrderId());
                    cardVo.setIsEnableBrowsingPermission(card.getIsEnableBrowsingPermission());
                    cardVo.setUrlType(card.getUrlType());
                    cardQxList.add(cardVo);
                }
            }
        }
        return cardQxList;
    }

}
