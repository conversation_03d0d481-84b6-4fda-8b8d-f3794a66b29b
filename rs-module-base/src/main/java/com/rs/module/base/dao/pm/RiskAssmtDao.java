package com.rs.module.base.dao.pm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.base.controller.admin.pm.vo.RiskAssmtListReqVO;
import com.rs.module.base.controller.admin.pm.vo.RiskAssmtPageReqVO;
import com.rs.module.base.entity.pm.RiskAssmtDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 实战平台-监管管理-风险评估 Dao
*
* <AUTHOR>
*/
@Mapper
public interface RiskAssmtDao extends IBaseDao<RiskAssmtDO> {


    default PageResult<RiskAssmtDO> selectPage(RiskAssmtPageReqVO reqVO) {
        Page<RiskAssmtDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<RiskAssmtDO> wrapper = new LambdaQueryWrapperX<RiskAssmtDO>()
            .eqIfPresent(RiskAssmtDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(RiskAssmtDO::getRoomId, reqVO.getRoomId())
            .eqIfPresent(RiskAssmtDO::getRiskType, reqVO.getRiskType())
            .eqIfPresent(RiskAssmtDO::getRiskLevel, reqVO.getRiskLevel())
            .eqIfPresent(RiskAssmtDO::getStandardQuota, reqVO.getStandardQuota())
            .eqIfPresent(RiskAssmtDO::getAdjustReason, reqVO.getAdjustReason())
            .eqIfPresent(RiskAssmtDO::getControlSuggest, reqVO.getControlSuggest())
            .eqIfPresent(RiskAssmtDO::getAssmtUserId, reqVO.getAssmtUserId())
            .likeIfPresent(RiskAssmtDO::getAssmtUserName, reqVO.getAssmtUserName())
            .betweenIfPresent(RiskAssmtDO::getAssmtTime, reqVO.getAssmtTime())
            .eqIfPresent(RiskAssmtDO::getLeaderOpinion, reqVO.getLeaderOpinion())
            .eqIfPresent(RiskAssmtDO::getLeaderOpinionContent, reqVO.getLeaderOpinionContent())
            .eqIfPresent(RiskAssmtDO::getLeaderAutograph, reqVO.getLeaderAutograph())
            .betweenIfPresent(RiskAssmtDO::getLeaderAutographTime, reqVO.getLeaderAutographTime())
            .eqIfPresent(RiskAssmtDO::getEmotion, reqVO.getEmotion())
            .eqIfPresent(RiskAssmtDO::getPsychology, reqVO.getPsychology())
            .eqIfPresent(RiskAssmtDO::getOperateUserId, reqVO.getOperateUserId())
            .likeIfPresent(RiskAssmtDO::getOperateUserName, reqVO.getOperateUserName())
            .betweenIfPresent(RiskAssmtDO::getOperateTime, reqVO.getOperateTime())
            .eqIfPresent(RiskAssmtDO::getOldRiskLevel, reqVO.getOldRiskLevel())
            .eqIfPresent(RiskAssmtDO::getStatus, reqVO.getStatus())
            .eqIfPresent(RiskAssmtDO::getAssmtCause, reqVO.getAssmtCause())
            .eqIfPresent(RiskAssmtDO::getSpecificAdjustReason, reqVO.getSpecificAdjustReason())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(RiskAssmtDO::getAddTime);
        }
        Page<RiskAssmtDO> riskAssmtPage = selectPage(page, wrapper);
        return new PageResult<>(riskAssmtPage.getRecords(), riskAssmtPage.getTotal());
    }
    default List<RiskAssmtDO> selectList(RiskAssmtListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<RiskAssmtDO>()
            .eqIfPresent(RiskAssmtDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(RiskAssmtDO::getRoomId, reqVO.getRoomId())
            .eqIfPresent(RiskAssmtDO::getRiskType, reqVO.getRiskType())
            .eqIfPresent(RiskAssmtDO::getRiskLevel, reqVO.getRiskLevel())
            .eqIfPresent(RiskAssmtDO::getStandardQuota, reqVO.getStandardQuota())
            .eqIfPresent(RiskAssmtDO::getAdjustReason, reqVO.getAdjustReason())
            .eqIfPresent(RiskAssmtDO::getControlSuggest, reqVO.getControlSuggest())
            .eqIfPresent(RiskAssmtDO::getAssmtUserId, reqVO.getAssmtUserId())
            .likeIfPresent(RiskAssmtDO::getAssmtUserName, reqVO.getAssmtUserName())
            .betweenIfPresent(RiskAssmtDO::getAssmtTime, reqVO.getAssmtTime())
            .eqIfPresent(RiskAssmtDO::getLeaderOpinion, reqVO.getLeaderOpinion())
            .eqIfPresent(RiskAssmtDO::getLeaderOpinionContent, reqVO.getLeaderOpinionContent())
            .eqIfPresent(RiskAssmtDO::getLeaderAutograph, reqVO.getLeaderAutograph())
            .betweenIfPresent(RiskAssmtDO::getLeaderAutographTime, reqVO.getLeaderAutographTime())
            .eqIfPresent(RiskAssmtDO::getEmotion, reqVO.getEmotion())
            .eqIfPresent(RiskAssmtDO::getPsychology, reqVO.getPsychology())
            .eqIfPresent(RiskAssmtDO::getOperateUserId, reqVO.getOperateUserId())
            .likeIfPresent(RiskAssmtDO::getOperateUserName, reqVO.getOperateUserName())
            .betweenIfPresent(RiskAssmtDO::getOperateTime, reqVO.getOperateTime())
            .eqIfPresent(RiskAssmtDO::getOldRiskLevel, reqVO.getOldRiskLevel())
            .eqIfPresent(RiskAssmtDO::getStatus, reqVO.getStatus())
            .eqIfPresent(RiskAssmtDO::getAssmtCause, reqVO.getAssmtCause())
            .eqIfPresent(RiskAssmtDO::getSpecificAdjustReason, reqVO.getSpecificAdjustReason())
        .orderByDesc(RiskAssmtDO::getAddTime));    }


    }
