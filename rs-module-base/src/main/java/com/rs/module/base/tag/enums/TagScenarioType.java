package com.rs.module.base.tag.enums;

import lombok.Getter;

/**
 * 标签场景类型枚举
 * 用于定义标签规则的操作场景
 * 
 * <AUTHOR>
 */
@Getter
public enum TagScenarioType {
    /**
     * 添加标签场景
     * 当条件满足时，为实体添加指定标签
     */
    ADD("ADD", "添加标签"),

    /**
     * 删除标签场景
     * 当条件满足时，从实体中删除指定标签
     */
    REMOVE("REMOVE", "删除标签"),

    /**
     * 更新标签场景
     * 当条件满足时，更新实体的指定标签
     */
    UPDATE("UPDATE", "更新标签"),

    /**
     * 替换标签场景
     * 当条件满足时，用新标签替换实体的旧标签
     */
    REPLACE("REPLACE", "替换标签"),

    /**
     * 条件检查场景
     * 仅检查条件是否满足，不执行标签操作
     */
    CHECK("CHECK", "条件检查");

    /**
     * 场景编码
     */
    private final String code;

    /**
     * 场景描述
     */
    private final String description;

    TagScenarioType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据编码获取枚举
     * 
     * @param code 场景编码
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static TagScenarioType getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (TagScenarioType scenario : values()) {
            if (scenario.getCode().equals(code)) {
                return scenario;
            }
        }
        return null;
    }

    /**
     * 判断是否为删除场景
     * 
     * @return 如果是删除场景返回true，否则返回false
     */
    public boolean isRemoveScenario() {
        return this == REMOVE;
    }

    /**
     * 判断是否为添加场景
     * 
     * @return 如果是添加场景返回true，否则返回false
     */
    public boolean isAddScenario() {
        return this == ADD;
    }

    /**
     * 判断是否为更新场景
     * 
     * @return 如果是更新场景返回true，否则返回false
     */
    public boolean isUpdateScenario() {
        return this == UPDATE;
    }

    /**
     * 判断是否为替换场景
     * 
     * @return 如果是替换场景返回true，否则返回false
     */
    public boolean isReplaceScenario() {
        return this == REPLACE;
    }

    /**
     * 判断是否为检查场景
     * 
     * @return 如果是检查场景返回true，否则返回false
     */
    public boolean isCheckScenario() {
        return this == CHECK;
    }
}
