package com.rs.module.base.tag.parser;

import cn.hutool.extra.spring.SpringUtil;
import com.rs.module.base.tag.annotation.TagCondition;
import com.rs.module.base.tag.annotation.TagConditionGroup;
import com.rs.module.base.tag.enums.ConditionCombineType;
import com.rs.module.base.tag.enums.TagConditionType;
import com.rs.module.base.tag.enums.TagEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.regex.Pattern;

@Slf4j
@Component
public class TagConditionParser {

    private final ExpressionParser expressionParser = new SpelExpressionParser();

    public boolean evaluate(Object value, TagCondition condition, TagEnum tagEnum) {
        if (value == null) {
            return condition.type() == TagConditionType.IS_NULL;
        }

        // 处理条件组
        if (condition.groups().length > 0) {
            return evaluateGroups(value, condition.groups(), condition.combineType(), tagEnum);
        }

        boolean result = false;
        switch (condition.type()) {
            case EQUALS:
                result = equals(value, condition.compareValue(), tagEnum);
                break;
            case NOT_EQUALS:
                result = !equals(value, condition.compareValue(), tagEnum);
                break;
            case CONTAINS:
                result = contains(value, condition.compareValue(), tagEnum);
                break;
            case NOT_CONTAINS:
                result = !contains(value, condition.compareValue(), tagEnum);
                break;
            case GREATER_THAN:
                result = greaterThan(value, condition.compareValue(), tagEnum);
                break;
            case LESS_THAN:
                result = lessThan(value, condition.compareValue(), tagEnum);
                break;
            case GREATER_EQUALS:
                result = greaterEquals(value, condition.compareValue(), tagEnum);
                break;
            case LESS_EQUALS:
                result = lessEquals(value, condition.compareValue(), tagEnum);
                break;
            case IN:
                result = in(value, condition.values());
                break;
            case NOT_IN:
                result = !in(value, condition.values());
                break;
            case IS_NULL:
                result = value == null;
                break;
            case NOT_NULL:
                result = value != null;
                break;
            case REGEX:
                result = regex(value, condition.compareValue(), tagEnum);
                break;
            case SPEL:
                result = evaluateSpel(value, condition.spelExpression());
                break;
            case CUSTOM:
                result = evaluateCustomService(value, condition);
                break;
            default:
                result = false;
        }

        return condition.reverse() ? !result : result;
    }

    private boolean evaluateGroups(Object value, TagConditionGroup[] groups, ConditionCombineType combineType,
            TagEnum tagEnum) {
        if (groups == null || groups.length == 0) {
            return true;
        }

        boolean result = combineType == ConditionCombineType.AND;
        for (TagConditionGroup group : groups) {
            boolean groupResult = evaluateGroup(value, group, tagEnum);
            if (combineType == ConditionCombineType.AND) {
                result = result && groupResult;
                if (!result)
                    break; // 短路优化
            } else {
                result = result || groupResult;
                if (result)
                    break; // 短路优化
            }
        }
        return result;
    }

    private boolean evaluateGroup(Object value, TagConditionGroup group, TagEnum tagEnum) {
        if (value == null) {
            return group.type() == TagConditionType.IS_NULL;
        }

        // 将 TagConditionGroup 转换为 TagCondition
        TagCondition condition = new TagCondition() {
            @Override
            public TagConditionType type() {
                return group.type();
            }

            @Override
            public String compareValue() {
                return group.compareValue();
            }

            @Override
            public boolean reverse() {
                return group.reverse();
            }

            @Override
            public String spelExpression() {
                return group.spelExpression();
            }

            @Override
            public String serviceMethod() {
                return group.serviceMethod();
            }

            @Override
            public Class<?> serviceClass() {
                return group.serviceClass();
            }

            @Override
            public ConditionCombineType combineType() {
                return group.combineType();
            }

            @Override
            public String[] values() {
                return group.values();
            }

            @Override
            public TagConditionGroup[] groups() {
                return new TagConditionGroup[0];
            }

            @Override
            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                return TagCondition.class;
            }
        };

        boolean result = false;
        switch (group.type()) {
            case EQUALS:
                result = equals(value, group.compareValue(), tagEnum);
                break;
            case NOT_EQUALS:
                result = !equals(value, group.compareValue(), tagEnum);
                break;
            case CONTAINS:
                result = contains(value, group.compareValue(), tagEnum);
                break;
            case NOT_CONTAINS:
                result = !contains(value, group.compareValue(), tagEnum);
                break;
            case GREATER_THAN:
                result = greaterThan(value, group.compareValue(), tagEnum);
                break;
            case LESS_THAN:
                result = lessThan(value, group.compareValue(), tagEnum);
                break;
            case GREATER_EQUALS:
                result = greaterEquals(value, group.compareValue(), tagEnum);
                break;
            case LESS_EQUALS:
                result = lessEquals(value, group.compareValue(), tagEnum);
                break;
            case IN:
                result = in(value, group.values());
                break;
            case NOT_IN:
                result = !in(value, group.values());
                break;
            case IS_NULL:
                result = value == null;
                break;
            case NOT_NULL:
                result = value != null;
                break;
            case REGEX:
                result = regex(value, group.compareValue(), tagEnum);
                break;
            case SPEL:
                result = evaluateSpel(value, group.spelExpression());
                break;
            case CUSTOM:
                result = evaluateCustomService(value, condition);
                break;
            default:
                result = false;
        }

        return group.reverse() ? !result : result;
    }

    private boolean evaluateSpel(Object value, String expression) {
        try {
            Expression exp = expressionParser.parseExpression(expression);
            EvaluationContext context = new StandardEvaluationContext();
            context.setVariable("value", value);
            return exp.getValue(context, Boolean.class);
        } catch (Exception e) {
            log.error("SpEL表达式解析失败", e);
            return false;
        }
    }

    private boolean evaluateCustomService(Object value, TagCondition condition) {
        try {
            // 获取服务实例
            Object service = SpringUtil.getBean(condition.serviceClass());

            // 获取方法
            Method method = condition.serviceClass().getMethod(condition.serviceMethod(), Object.class);

            // 调用方法
            Object result = method.invoke(service, value);

            return result instanceof Boolean ? (Boolean) result : false;
        } catch (Exception e) {
            log.error("自定义服务方法调用失败", e);
            return false;
        }
    }

    private boolean equals(Object value, String compareValue, TagEnum tagEnum) {
        if (value == null) {
            return false;
        }
        // 如果compareValue为空，则使用TagEnum中的compareValue
        if (compareValue == null || compareValue.isEmpty()) {
            compareValue = tagEnum != null ? tagEnum.getCompareValue() : null;
        }
        if (compareValue == null) {
            return false;
        }
        return value.toString().equals(compareValue);
    }

    private boolean contains(Object value, String compareValue, TagEnum tagEnum) {
        if (value == null) {
            return false;
        }
        // 如果compareValue为空，则使用TagEnum中的compareValue
        if (compareValue == null || compareValue.isEmpty()) {
            compareValue = tagEnum != null ? tagEnum.getCompareValue() : null;
        }
        if (compareValue == null) {
            return false;
        }
        return value.toString().contains(compareValue);
    }

    private boolean greaterThan(Object value, String compareValue, TagEnum tagEnum) {
        if (value == null) {
            return false;
        }
        // 如果compareValue为空，则使用TagEnum中的compareValue
        if (compareValue == null || compareValue.isEmpty()) {
            compareValue = tagEnum != null ? tagEnum.getCompareValue() : null;
        }
        if (compareValue == null) {
            return false;
        }
        try {
            double v1 = Double.parseDouble(value.toString());
            double v2 = Double.parseDouble(compareValue);
            return v1 > v2;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    private boolean lessThan(Object value, String compareValue, TagEnum tagEnum) {
        if (value == null) {
            return false;
        }
        // 如果compareValue为空，则使用TagEnum中的compareValue
        if (compareValue == null || compareValue.isEmpty()) {
            compareValue = tagEnum != null ? tagEnum.getCompareValue() : null;
        }
        if (compareValue == null) {
            return false;
        }
        try {
            double v1 = Double.parseDouble(value.toString());
            double v2 = Double.parseDouble(compareValue);
            return v1 < v2;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    private boolean greaterEquals(Object value, String compareValue, TagEnum tagEnum) {
        if (value == null) {
            return false;
        }
        // 如果compareValue为空，则使用TagEnum中的compareValue
        if (compareValue == null || compareValue.isEmpty()) {
            compareValue = tagEnum != null ? tagEnum.getCompareValue() : null;
        }
        if (compareValue == null) {
            return false;
        }
        try {
            double v1 = Double.parseDouble(value.toString());
            double v2 = Double.parseDouble(compareValue);
            return v1 >= v2;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    private boolean lessEquals(Object value, String compareValue, TagEnum tagEnum) {
        if (value == null) {
            return false;
        }
        // 如果compareValue为空，则使用TagEnum中的compareValue
        if (compareValue == null || compareValue.isEmpty()) {
            compareValue = tagEnum != null ? tagEnum.getCompareValue() : null;
        }
        if (compareValue == null) {
            return false;
        }
        try {
            double v1 = Double.parseDouble(value.toString());
            double v2 = Double.parseDouble(compareValue);
            return v1 <= v2;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    private boolean in(Object value, String[] values) {
        if (value == null || values == null || values.length == 0) {
            return false;
        }
        return Arrays.asList(values).contains(value.toString());
    }

    private boolean regex(Object value, String pattern, TagEnum tagEnum) {
        if (value == null) {
            return false;
        }
        // 如果pattern为空，则使用TagEnum中的compareValue
        if (pattern == null || pattern.isEmpty()) {
            pattern = tagEnum != null ? tagEnum.getCompareValue() : null;
        }
        if (pattern == null) {
            return false;
        }
        try {
            return Pattern.matches(pattern, value.toString());
        } catch (Exception e) {
            return false;
        }
    }
}
