package com.rs.module.base.service.sys;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.IpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.controller.admin.sys.vo.BusTraceListReqVO;
import com.rs.module.base.controller.admin.sys.vo.BusTracePageReqVO;
import com.rs.module.base.controller.admin.sys.vo.BusTraceSaveReqVO;
import com.rs.module.base.dao.sys.BusTraceDao;
import com.rs.module.base.entity.sys.BusTraceDO;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.base.service.pm.PrisonerService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


/**
 * 实战平台-业务轨迹 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BusTraceServiceImpl extends BaseServiceImpl<BusTraceDao, BusTraceDO> implements BusTraceService {

    @Resource
    private BusTraceDao busTraceDao;
    
    @Resource
	private PrisonerService prisonerService;

    @Override
    public String createBusTrace(BusTraceSaveReqVO createReqVO) {
        // 插入
        BusTraceDO busTrace = BeanUtils.toBean(createReqVO, BusTraceDO.class);
        busTraceDao.insert(busTrace);
        // 返回
        return busTrace.getId();
    }

    @Override
    public void updateBusTrace(BusTraceSaveReqVO updateReqVO) {
        // 校验存在
        validateBusTraceExists(updateReqVO.getId());
        // 更新
        BusTraceDO updateObj = BeanUtils.toBean(updateReqVO, BusTraceDO.class);
        busTraceDao.updateById(updateObj);
    }
    
    /**
     * 保存实战平台-业务轨迹
     * @param busType BusTypeEnum 业务类型枚举
     * @param content String 轨迹内容
     * @param jgrybm String 监管人员编码
     * @param orgCode String 机构代码
     * @param businessId String 业务主键
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBusTrace(BusTypeEnum busType, String content, String jgrybm, String orgCode, String businessId) {
    	
    	//构建轨迹实体
    	BusTraceDO trace = new BusTraceDO();
		trace.setBusName(busType.getBusName());
		trace.setBusType(busType.getBusType());		
		trace.setContent(content);
		trace.setJgrybm(jgrybm);
		trace.setOrgCode(orgCode);
		trace.setBusinessId(businessId);
		
		//会话用户
		SessionUser user = SessionUserUtil.getSessionUser();
		
		//设置监所信息
		if(StringUtil.isEmpty(orgCode) && user != null) {
			trace.setOrgCode(user.getOrgCode());
		}
		
		//设置操作用户信息
		if(user != null) {
			trace.setOperateUser(user.getIdCard());
		}
		
		//设置证件号码
		if(StringUtil.isNotEmpty(jgrybm)) {
			PrisonerVwRespVO prisoner = prisonerService.getPrisonerByJgrybm(jgrybm);
			if(prisoner != null) {
				trace.setZjhm(prisoner.getZjhm());
			}
		}
		
		//设置请求ip地址
		trace.setIp(IpUtils.getClientIp());
		
		save(trace);
    }

    @Override
    public void deleteBusTrace(String id) {
        // 校验存在
        validateBusTraceExists(id);
        // 删除
        busTraceDao.deleteById(id);
    }

    private void validateBusTraceExists(String id) {
        if (busTraceDao.selectById(id) == null) {
            throw new ServerException("实战平台-业务轨迹数据不存在");
        }
    }

    @Override
    public BusTraceDO getBusTrace(String id) {
        return busTraceDao.selectById(id);
    }

    @Override
    public PageResult<BusTraceDO> getBusTracePage(BusTracePageReqVO pageReqVO) {
        return busTraceDao.selectPage(pageReqVO);
    }

    @Override
    public List<BusTraceDO> getBusTraceList(BusTraceListReqVO listReqVO) {
        return busTraceDao.selectList(listReqVO);
    }

    /**
     * 获取监管人员轨迹相关的场所
     * @param jgrybm String 监管人员编码
     * @param busTypes List<String> 轨迹业务类型
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getJgryTraceOrgList(String jgrybm, List<String> busTypes){
    	QueryWrapper<BusTraceDO> wrapper = new QueryWrapper<>();
    	wrapper.eq("jgrybm", jgrybm);
    	wrapper.isNotNull("zjhm");
    	BusTraceDO busTrace = getOne(wrapper);
    	
    	//通过证件号码获取场所
    	if(busTrace != null) {
    		return busTraceDao.getJgryTraceOrgList(busTrace.getZjhm(), null, busTypes);
    	}
    	
    	//通过监管人员编码获取场所
    	else {
    		return busTraceDao.getJgryTraceOrgList(null, jgrybm, busTypes);
    	}    	
    }
    @Override
    public void updateBusTraceByBusinessId(String businessId, String jgrybm, BusTypeEnum busType, String content) {
        BusTraceDO entity = new BusTraceDO();
        entity.setContent(content);
        busTraceDao.update(entity, new LambdaUpdateWrapper<BusTraceDO>().eq(BusTraceDO::getBusinessId, businessId).eq(BusTraceDO::getJgrybm, jgrybm).eq(BusTraceDO::getBusType, busType.getBusType()));
    }
}
