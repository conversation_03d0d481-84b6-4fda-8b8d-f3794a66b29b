package com.rs.module.base.service.video;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.entity.pm.BaseDeviceInscreenDO;
import com.rs.module.acp.service.pm.BaseDeviceCameraService;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDeviceListReqVO;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDevicePageReqVO;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDeviceRespVO;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDeviceSaveReqVO;
import com.rs.module.base.controller.admin.video.vo.AddTreeNodeRequestVO;
import com.rs.module.base.dao.pm.device.BaseDeviceDao;
import com.rs.module.base.dao.pm.device.BaseVidVideoDao;
import com.rs.module.base.dao.pm.device.SplwTreeDao;
import com.rs.module.base.entity.pm.DeviceData;
import com.rs.module.base.entity.pm.device.BaseDeviceDO;
import com.rs.module.base.entity.pm.device.BaseVidVideoDO;
import com.rs.module.base.entity.pm.device.SplwTreeDO;
import com.rs.module.base.enums.DeviceStatusEnum;
import com.rs.module.base.enums.DeviceTypeEnum;
import com.rs.module.base.util.TreeUtil;
import com.rs.module.base.vo.TreeNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


/**
 * 实战平台-监管管理-设备信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class BaseVidVideoServiceImpl extends BaseServiceImpl<BaseVidVideoDao, BaseVidVideoDO> implements BaseVidVideoService {

    @Resource
    private BaseVidVideoDao baseVidVideoDao;

}
