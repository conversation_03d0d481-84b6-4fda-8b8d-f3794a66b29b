package com.rs.module.base.service.pm;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.base.controller.admin.pm.vo.*;
import com.rs.module.base.controller.admin.pm.vo.terminal.AreaPrisonRoomExportReqVO;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.entity.pm.PrisonRoomWarderDO;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import static org.springframework.transaction.annotation.Propagation.REQUIRED;

/**
 * 实战平台-监管管理-区域监室 Service 接口
 *
 * <AUTHOR>
 */
public interface AreaPrisonRoomService extends IBaseService<AreaPrisonRoomDO>{

    /**
     * 创建实战平台-监管管理-区域监室
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createAreaPrisonRoom(@Valid AreaPrisonRoomSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-区域监室
     *
     * @param updateReqVO 更新信息
     */
    void updateAreaPrisonRoom(@Valid AreaPrisonRoomSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-区域监室
     *
     * @param id 编号
     */
    void deleteAreaPrisonRoom(String id);

    /**
     * 获得实战平台-监管管理-区域监室
     *
     * @param orgCode 编号
     * @return 实战平台-监管管理-区域监室
     */
    List<AreaPrisonRoomDO> getRoomByOrgCode(String orgCode);

    /**
     * 获得实战平台-监管管理-区域监室
     *
     * @param roomCode 编号
     * @return 实战平台-监管管理-区域监室
     */
    AreaPrisonRoomDO getAreaPrisonRoom(String roomCode);

    /**
     * 获得实战平台-监管管理-区域监室
     *
     * @param roomCode 编号
     * @return 实战平台-监管管理-区域监室
     */
    AreaPrisonRoomDO getAreaPrisonRoom(String orgCode, String roomCode);


    String getAreaPrisonRoomName(String id);

    /**
    * 获得实战平台-监管管理-区域监室分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-监管管理-区域监室分页
    */
    PageResult<AreaPrisonRoomDO> getAreaPrisonRoomPage(AreaPrisonRoomPageReqVO pageReqVO);

    /**
    * 获得实战平台-监管管理-区域监室列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-监管管理-区域监室列表
    */
    List<AreaPrisonRoomDO> getAreaPrisonRoomList(AreaPrisonRoomListReqVO listReqVO);

    /**
     * 获得实战平台-监管管理-区域监室列表
     *
     * @param roomIds 监室编号列表
     * @return 实战平台-监管管理-区域监室列表
     */
    List<AreaPrisonRoomDO> getAreaPrisonRoomList(List<String> roomIds);


    String createAreaPrisonRoom(String id,AreaSaveReqDTO createReqVO, List<AreaDO> list);

    void deleteAreaPrisonRoomByRoomCode(String id);

    List<AreaPrisonRoomDO> getAreaPrisonRoomListByRoomCode(AreaPrisonRoomListReqVO listReqVO);

    /**
     * 校验导入的监室数据完整性
     *
     * @param roomMapList
     * @return
     */
    JSONObject validateData(List<Map<String, Object>> roomMapList, String orgCode);

    /**
     * 批量添加, 遇到相同数据则替换
     *
     * @param roomList
     */
    @Transactional(rollbackFor = Exception.class, propagation = REQUIRED)
    void replaceBatchRoom(List<AreaPrisonRoomDO> roomList);

    /**
     * 批量添加, 遇到相同数据则替换
     *
     * @param roomList
     */
    @Transactional(rollbackFor = Exception.class, propagation = REQUIRED)
    void replaceBatchWarder(List<PrisonRoomWarderDO> roomList);

    /**
     * 获取导出数据
     *
     * @param exportReqVO
     * @return
     */
    List<JSONObject> findExportListBy(AreaPrisonRoomExportReqVO exportReqVO);

    /**
     * 查询监室人员数量
     * @param orgCode
     * @param roomId
     * @return
     */
    JSONObject getRoomPrisonerInfo(String orgCode, String roomId);

    PageResult<AreaPrisonRoomDO> getRoomWithViolationPage(AreaPrisonRoomPageWithViolationReqVO pageReqVO);
}
