package com.rs.module.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 仓内屏设备类型
 * <AUTHOR>
 * @date 2025年7月2日
 */
@Getter
@AllArgsConstructor
public enum InscreenDeviceTypeEnum {

    INSCREEN(1, "仓内屏"),
    OUTSCREEN(2, "仓外屏"),
    DJFJ(3,	"对讲分机"),
    DJZJ(4,	"对讲主机"),
    DPLDZJ(5, "大屏联动主机");

    private final int code;
    private final String name;

    public static InscreenDeviceTypeEnum getByCode(int code) {
        for (InscreenDeviceTypeEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据名称获取code
     * @param name 设备类型名称
     * @return 对应的code，如果找不到则返回null
     */
    public static int getCodeByName(String name) {
        for (InscreenDeviceTypeEnum type : values()) {
            if (type.name.equals(name)) {
                return type.code;
            }
        }
        return 0;
    }
} 