package com.rs.module.base.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务轨迹组枚举类
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Getter
@AllArgsConstructor
public enum BusTraceGroupEnum {

	YEWU("01", "业务轨迹", Arrays.asList(BusTypeEnum.YEWU_RSJL, BusTypeEnum.YEWU_SLGL, BusTypeEnum.YEWU_XKDJ,
			BusTypeEnum.YEWU_FXPG, BusTypeEnum.YEWU_CSJL, BusTypeEnum.YEWU_THJY, BusTypeEnum.YEWU_TIXUN,
			BusTypeEnum.YEWU_TIJIE, BusTypeEnum.YEWU_LSHJ, BusTypeEnum.YEWU_JSHJ, BusTypeEnum.YEWU_XJSY,
			BusTypeEnum.YEWU_JSTZ, BusTypeEnum.YEWU_GWCM)),
	
	YIL<PERSON><PERSON>("02", "医疗轨迹", Arrays.asList(BusTypeEnum.YILIAO_RSJC, BusTypeEnum.YILIAO_SNJY, BusTypeEnum.YILIAO_SWJY)),
	
	POLICE("03", "警员轨迹", Arrays.asList(BusTypeEnum.YEWU_THJY, BusTypeEnum.YEWU_MDMGL, BusTypeEnum.YEWU_XJSY,
			BusTypeEnum.YEWU_TIJIE, BusTypeEnum.YEWU_LSHJ, BusTypeEnum.YEWU_JSHJ, BusTypeEnum.YEWU_AQJC,
			BusTypeEnum.YEWU_FXPG, BusTypeEnum.YEWU_JIASHI, BusTypeEnum.YEWU_JIANXING, BusTypeEnum.YEWU_JSZB,
			BusTypeEnum.YILIAO_YPRK, BusTypeEnum.YILIAO_YPCK, BusTypeEnum.YEWU_CWGL, BusTypeEnum.YEWU_TSCSQ,
			BusTypeEnum.YEWU_XXYGL, BusTypeEnum.YEWU_JSTZ, BusTypeEnum.YEWU_WXGD, BusTypeEnum.YEWU_ZDRYGZ,
			BusTypeEnum.YILIAO_LSYZ, BusTypeEnum.YILIAO_CQYZ, BusTypeEnum.YEWU_ZYJWZX));
	
	//代码
	private String code;
	
	//名称
	private String name;
	
	//业务类型枚举数组
	private List<BusTypeEnum> busTypes;
	
    /**
     * 根据代码获取分组
     * @param code String 组代码
     * @return BusTraceGroupEnum
     */
    public static BusTraceGroupEnum getGroupByCode(String code) {
        for (BusTraceGroupEnum group : values()) {
            if (group.code.equals(code)) {
                return group;
            }
        }
        return null;
    }
    
    /**
     * 获取分组包含的所有业务类型
     * @param code String 分组代码
     * @return List<String>
     */
    public static List<String> getGroupBusTypes(String code){
    	List<String> busTypes = new ArrayList<>();
    	BusTraceGroupEnum group = getGroupByCode(code);
    	if(group != null) {
    		List<BusTypeEnum> busTypeEnums = group.getBusTypes();
    		for(BusTypeEnum busTypeEnum : busTypeEnums) {
    			busTypes.add(busTypeEnum.getBusType());
    		}
    	}    	
    	return busTypes;
    }
}
