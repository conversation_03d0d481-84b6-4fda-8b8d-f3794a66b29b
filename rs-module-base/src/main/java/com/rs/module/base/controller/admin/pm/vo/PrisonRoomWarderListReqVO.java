package com.rs.module.base.controller.admin.pm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-监管管理-监室主协管人员列表 Request VO")
@Data
public class PrisonRoomWarderListReqVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("更新时间")
    private Date updateTime2;

    @ApiModelProperty("民警id")
    private String policeId;

    @ApiModelProperty("民警身份证号")
    private String policeSfzh;

    @ApiModelProperty("民警名字")
    private String policeName;

    @ApiModelProperty("用户类型 主 w,协 a,机动 m")
    private String userType;

    @ApiModelProperty("监室ID")
    private String roomId;

}
