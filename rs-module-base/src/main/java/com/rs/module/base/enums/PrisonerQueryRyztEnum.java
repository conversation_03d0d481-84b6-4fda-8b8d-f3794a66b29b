package com.rs.module.base.enums;

/**
 * 人员查询代码枚举
 *
 * <AUTHOR>
 * @Date 2025/3/17 16:37
 */
public enum PrisonerQueryRyztEnum {

    ZS("10", "查询在所人员"),
    ALL("11", "查询全部人员");

    PrisonerQueryRyztEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }
    public String getName() {
        return name;
    }

}
