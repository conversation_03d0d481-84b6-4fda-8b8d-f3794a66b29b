package com.rs.module.base.tag.enums;

import lombok.Getter;

/**
 * 标签枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum TagEnum {
    // 风险等级标签
    RISK_LEVEL_HIGH("RISK_LEVEL_HIGH", "风险等级", "高风险", "80"),
    RISK_LEVEL_MEDIUM("RISK_LEVEL_MEDIUM", "风险等级", "中风险", "50"),
    RISK_LEVEL_LOW("RISK_LEVEL_LOW", "风险等级", "低风险", "20"),

    // 教育程度标签
    EDUCATION_DOCTOR("EDUCATION_DOCTOR", "教育程度", "博士", "DOCTOR"),
    EDUCATION_MASTER("EDUCATION_MASTER", "教育程度", "硕士", "MASTER"),
    EDUCATION_BACHELOR("EDUCATION_BACHELOR", "教育程度", "本科", "BACHELOR"),
    EDUCATION_COLLEGE("EDUCATION_COLLEGE", "教育程度", "大专", "COLLEGE"),
    EDUCATION_HIGH("EDUCATION_HIGH", "教育程度", "高中", "HIGH"),
    EDUCATION_MIDDLE("EDUCATION_MIDDLE", "教育程度", "初中", "MIDDLE"),
    EDUCATION_PRIMARY("EDUCATION_PRIMARY", "教育程度", "小学", "PRIMARY"),
    EDUCATION_ILLITERATE("EDUCATION_ILLITERATE", "教育程度", "文盲", "ILLITERATE"),

    // 特殊关注标签
    SPECIAL_CARE("SPECIAL_CARE", "特殊关注", "需要特殊关注", null),
    SENSITIVE_PRISONER("SENSITIVE_PRISONER", "特殊关注", "敏感人员", null),

    // 健康状况标签
    HEALTH_GOOD("HEALTH_GOOD", "健康状况", "健康", "GOOD"),
    HEALTH_NORMAL("HEALTH_NORMAL", "健康状况", "一般", "NORMAL"),
    HEALTH_POOR("HEALTH_POOR", "健康状况", "较差", "POOR"),
    HEALTH_CHRONIC("HEALTH_CHRONIC", "健康状况", "慢性病", "CHRONIC"),
    HEALTH_MENTAL("HEALTH_MENTAL", "健康状况", "精神疾病", "MENTAL"),
    HEALTH_DISABILITY("HEALTH_DISABILITY", "健康状况", "残疾", "DISABILITY"),
    HEALTH_INFECTIOUS("HEALTH_INFECTIOUS", "健康状况", "传染病", "INFECTIOUS"),

    // 改造表现标签
    PERFORMANCE_EXCELLENT("PERFORMANCE_EXCELLENT", "改造表现", "优秀", "EXCELLENT"),
    PERFORMANCE_GOOD("PERFORMANCE_GOOD", "改造表现", "良好", "GOOD"),
    PERFORMANCE_NORMAL("PERFORMANCE_NORMAL", "改造表现", "一般", "NORMAL"),
    PERFORMANCE_POOR("PERFORMANCE_POOR", "改造表现", "较差", "POOR"),
    PERFORMANCE_VIOLATION("PERFORMANCE_VIOLATION", "改造表现", "违规", "VIOLATION"),
    PERFORMANCE_REWARD("PERFORMANCE_REWARD", "改造表现", "奖励", "REWARD"),
    PERFORMANCE_PUNISHMENT("PERFORMANCE_PUNISHMENT", "改造表现", "处罚", "PUNISHMENT"),

    // 性别标签
    MALE("MALE", "性别", "男性", "1"),
    FEMALE("FEMALE", "性别", "女性", "2"),

    // 风险标签
    HIGH_RISK("HIGH_RISK", "风险等级", "高风险", "80"),
    MEDIUM_RISK("MEDIUM_RISK", "风险等级", "中等风险", "50"),
    LOW_RISK("LOW_RISK", "风险等级", "低风险", "20"),
    TEMPORARY_RISK("TEMPORARY_RISK", "风险等级", "临时风险", "70"),
    HIGH_RISK_DETAIL("HIGH_RISK_DETAIL", "风险等级", "高风险详情", "90"),

    // 行为标签
    VIOLENT("VIOLENT", "行为特征", "暴力倾向", "暴力"),
    NEED_ATTENTION("NEED_ATTENTION", "行为特征", "需要关注", null),
    BEHAVIOR_AGGRESSIVE("BEHAVIOR_AGGRESSIVE", "行为特征", "攻击性", "AGGRESSIVE"),
    BEHAVIOR_DEPRESSED("BEHAVIOR_DEPRESSED", "行为特征", "抑郁倾向", "DEPRESSED"),
    BEHAVIOR_ANXIOUS("BEHAVIOR_ANXIOUS", "行为特征", "焦虑倾向", "ANXIOUS"),
    BEHAVIOR_SELF_HARM("BEHAVIOR_SELF_HARM", "行为特征", "自伤倾向", "SELF_HARM"),
    BEHAVIOR_ESCAPE("BEHAVIOR_ESCAPE", "行为特征", "脱逃倾向", "ESCAPE"),

    // 特殊时期标签
    SPECIAL_PERIOD("SPECIAL_PERIOD", "特殊时期", "特殊时期", "特殊"),
    PERIOD_HOLIDAY("PERIOD_HOLIDAY", "特殊时期", "节假日", "HOLIDAY"),
    PERIOD_FESTIVAL("PERIOD_FESTIVAL", "特殊时期", "重大节日", "FESTIVAL"),
    PERIOD_WEATHER("PERIOD_WEATHER", "特殊时期", "恶劣天气", "WEATHER"),

    // 姓名标签
    SPECIAL_NAME("SPECIAL_NAME", "姓名特征", "特殊姓名", null),

    // 特殊照顾标签
    SPECIAL_CARE_ZD("SPECIAL_CARE_ZD", "特殊照顾", "特殊照顾", "ZD_JGRYBQLX"),
    // 正常标签
    NORMAL("NORMAL", "正常", "正常", "ZD_JGRYBQLX"),
    // 敏感人员标签
    SENSITIVE("SENSITIVE", "敏感人员", "敏感人员", "ZD_JGRYBQLX"),
    // 重要人员标签
    VIP("VIP", "重要人员", "重要人员", "ZD_JGRYBQLX"),

    // 刑期标签
    SENTENCE_LIFE("SENTENCE_LIFE", "刑期", "无期徒刑", "LIFE"),
    SENTENCE_DEATH("SENTENCE_DEATH", "刑期", "死刑", "DEATH"),
    SENTENCE_LONG("SENTENCE_LONG", "刑期", "长期", "LONG"),
    SENTENCE_MEDIUM("SENTENCE_MEDIUM", "刑期", "中期", "MEDIUM"),
    SENTENCE_SHORT("SENTENCE_SHORT", "刑期", "短期", "SHORT"),

    // 犯罪类型标签
    CRIME_VIOLENT("CRIME_VIOLENT", "犯罪类型", "暴力犯罪", "VIOLENT"),
    CRIME_ECONOMIC("CRIME_ECONOMIC", "犯罪类型", "经济犯罪", "ECONOMIC"),
    CRIME_DRUG("CRIME_DRUG", "犯罪类型", "毒品犯罪", "DRUG"),
    CRIME_SEXUAL("CRIME_SEXUAL", "犯罪类型", "性犯罪", "SEXUAL"),
    CRIME_TRAFFIC("CRIME_TRAFFIC", "犯罪类型", "交通肇事", "TRAFFIC"),
    CRIME_CORRUPTION("CRIME_CORRUPTION", "犯罪类型", "职务犯罪", "CORRUPTION"),

    // 改造项目标签
    REFORM_EDUCATION("REFORM_EDUCATION", "改造项目", "文化教育", "EDUCATION"),
    REFORM_VOCATIONAL("REFORM_VOCATIONAL", "改造项目", "职业技能", "VOCATIONAL"),
    REFORM_PSYCHOLOGICAL("REFORM_PSYCHOLOGICAL", "改造项目", "心理矫治", "PSYCHOLOGICAL"),
    REFORM_LABOR("REFORM_LABOR", "改造项目", "劳动改造", "LABOR"),
    REFORM_LEGAL("REFORM_LEGAL", "改造项目", "法制教育", "LEGAL"),

    // 会见管理标签
    VISIT_FREQUENT("VISIT_FREQUENT", "会见管理", "频繁会见", "FREQUENT"),
    VISIT_RESTRICTED("VISIT_RESTRICTED", "会见管理", "限制会见", "RESTRICTED"),
    VISIT_SPECIAL("VISIT_SPECIAL", "会见管理", "特殊会见", "SPECIAL"),

    // 通信管理标签
    COMMUNICATION_MONITORED("COMMUNICATION_MONITORED", "通信管理", "重点监控", "MONITORED"),
    COMMUNICATION_RESTRICTED("COMMUNICATION_RESTRICTED", "通信管理", "限制通信", "RESTRICTED"),
    COMMUNICATION_NORMAL("COMMUNICATION_NORMAL", "通信管理", "正常通信", "NORMAL"),

    // 奖惩管理标签
    REWARD_MERIT("REWARD_MERIT", "奖惩管理", "立功", "MERIT"),
    REWARD_GOOD("REWARD_GOOD", "奖惩管理", "表扬", "GOOD"),
    PUNISHMENT_WARNING("PUNISHMENT_WARNING", "奖惩管理", "警告", "WARNING"),
    PUNISHMENT_DETENTION("PUNISHMENT_DETENTION", "奖惩管理", "禁闭", "DETENTION"),

    // 医疗管理标签
    MEDICAL_REGULAR("MEDICAL_REGULAR", "医疗管理", "定期检查", "REGULAR"),
    MEDICAL_SPECIAL("MEDICAL_SPECIAL", "医疗管理", "特殊治疗", "SPECIAL"),
    MEDICAL_EMERGENCY("MEDICAL_EMERGENCY", "医疗管理", "紧急救治", "EMERGENCY"),

    // 劳动管理标签
    LABOR_SKILLED("LABOR_SKILLED", "劳动管理", "技术工种", "SKILLED"),
    LABOR_GENERAL("LABOR_GENERAL", "劳动管理", "普通工种", "GENERAL"),
    LABOR_RESTRICTED("LABOR_RESTRICTED", "劳动管理", "限制劳动", "RESTRICTED"),

    // 安全风险评估标签
    SAFETY_HIGH("SAFETY_HIGH", "安全风险", "高风险", "HIGH"),
    SAFETY_MEDIUM("SAFETY_MEDIUM", "安全风险", "中风险", "MEDIUM"),
    SAFETY_LOW("SAFETY_LOW", "安全风险", "低风险", "LOW"),
    SAFETY_SPECIAL("SAFETY_SPECIAL", "安全风险", "特殊风险", "SPECIAL");

    /**
     * 标签编码
     */
    private final String code;

    /**
     * 标签类型
     */
    private final String type;

    /**
     * 标签名称
     */
    private final String name;

    /**
     * 比较值
     */
    private final String compareValue;

    TagEnum(String code, String type, String name, String compareValue) {
        this.code = code;
        this.type = type;
        this.name = name;
        this.compareValue = compareValue;
    }

    /**
     * 根据编码获取枚举
     */
    public static TagEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (TagEnum tagEnum : values()) {
            if (tagEnum.getCode().equals(code)) {
                return tagEnum;
            }
        }
        return null;
    }

    /**
     * 根据类型获取枚举列表
     */
    public static TagEnum[] getByType(String type) {
        if (type == null) {
            return new TagEnum[0];
        }
        return java.util.Arrays.stream(values())
                .filter(tagEnum -> tagEnum.getType().equals(type))
                .toArray(TagEnum[]::new);
    }

    /**
     * 获取标签编码
     */
    public String getTagCode() {
        return this.code;
    }

    /**
     * 获取标签名称
     */
    public String getTagName() {
        return this.name;
    }

    /**
     * 获取标签类型
     */
    public String getTagType() {
        return this.type;
    }
}
