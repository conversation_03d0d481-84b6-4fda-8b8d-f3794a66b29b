package com.rs.module.base.controller.admin.pm.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-监管管理-监室主协管人员 Response VO")
@Data
public class PrisonRoomWarderRespVO implements TransPojo {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @ApiModelProperty("身份证号")
    private String policeSfzh;
    @ApiModelProperty("民警id")
    private String policeId;
    @ApiModelProperty("民警名字")
    private String policeName;
    @ApiModelProperty("用户类型 主 w,协 a,机动 m")
    private String userType;
    @ApiModelProperty("性别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_SEX")
    private String sex;
    @ApiModelProperty("监室ID")
    private String roomId;
    @ApiModelProperty("登记状态")
    @NotEmpty(message = "登记状态不能为空")
    private String regStatus;

}
