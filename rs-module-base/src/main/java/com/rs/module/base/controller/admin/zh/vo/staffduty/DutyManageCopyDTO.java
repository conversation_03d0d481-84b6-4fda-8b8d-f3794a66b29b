package com.rs.module.base.controller.admin.zh.vo.staffduty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@Data
public class DutyManageCopyDTO {
    @ApiModelProperty(value = "源排班日期开始",required = true)
    @NotEmpty(message = "源排班日期开始 不能为空")
    private Date sourceStartTime;
    
    @ApiModelProperty(value = "源排班日期结束",required = true)
    @NotEmpty(message = "源排班日期结束 不能为空")
    private Date sourceEndTime;
    
    @ApiModelProperty(value = "目标排班日期开始",required = true)
    @NotEmpty(message = "目标排班日期开始 不能为空")
    private Date targetStartTime;

    @ApiModelProperty(value = "目标排班日期结束",required = true)
    @NotEmpty(message = "目标排班日期结束 不能为空")
    private Date targetEndTime;

    @ApiModelProperty(value = "监所编号",hidden = true)
    @NotEmpty(message = "监所编号 不能为空")
    private String orgCode;
}
