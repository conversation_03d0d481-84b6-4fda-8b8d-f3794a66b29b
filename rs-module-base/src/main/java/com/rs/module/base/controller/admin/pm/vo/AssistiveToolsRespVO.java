package com.rs.module.base.controller.admin.pm.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-监管管理-辅助工具 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AssistiveToolsRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("文件名称")
    private String name;
    @ApiModelProperty("文件存储路径")
    private String filePath;
    @ApiModelProperty("文件大小")
    private Integer fileSize;
    @ApiModelProperty("文件类型")
    private String fileType;
    @ApiModelProperty("是否启用")
    private Short isEnable;
    @ApiModelProperty("排序Id")
    private Integer orderId;
    @ApiModelProperty("备注")
    private String remark;
}
