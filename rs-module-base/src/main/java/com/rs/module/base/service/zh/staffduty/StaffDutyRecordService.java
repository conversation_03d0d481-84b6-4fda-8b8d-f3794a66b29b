package com.rs.module.base.service.zh.staffduty;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.base.controller.admin.zh.vo.staffduty.*;
import com.rs.module.base.entity.zh.StaffDutyRecordDO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 值班安排服务
 */
public interface StaffDutyRecordService extends IBaseService<StaffDutyRecordDO> {


 /***
  * 值班安排首页列表
  * @param startTime
  * @param endTime
  * @return
  */
 DutyManageVO listByDutyDate(Date startTime,Date endTime,Integer templateType);

 DutyManageVO listBySingleDutyDate(String dutyDate,Integer templateType);

 List<JSONObject> listByDutySingleDateIndex(String dutyDate, String orgCode,Integer templateType);

 /***
  * 值班安排首页默认启用状态表头
  * @return
  */
 DutyManageVO defaultHeader(Integer templateType);

    List<DutyManageHeaderVO> getHeaderList(String tempId);

 String getTempId(String orgCode, Integer templateType);

 /***
 * 新增
 * @param dto
 * @return
 */
 Integer add(DutyManageSaveDTO dto);

 /***
  * 导出配置
  * @param startTime
  * @param endTime
  * @param response
  * @return
  * @throws IOException
  */
 Integer exportConfigByDutyDate(Date startTime, Date endTime,Integer templateType, HttpServletResponse response) throws IOException;


 /***
  * 导入配置
  * @param multipartFile
  * @return
  * @throws IOException
  */
 DutyManageVO importConfigByDutyDate(MultipartFile multipartFile, Integer templateType) throws IOException;


    List<StaffDutyRecordPersoRespVO> getPersonList(String postKeyId, String tempDate);

 List<String> getPostKeyByTempId(String tempId);

 Map<String, Object> getTemIdAndDateByDate(String format, String orgCode, Integer code);
}
