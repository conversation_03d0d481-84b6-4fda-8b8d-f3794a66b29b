package com.rs.module.base.service.pm;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.BizQuickAccessRespVO;
import com.rs.module.base.controller.admin.pm.vo.BizQuickAccessSaveReqVO;
import com.rs.module.base.dao.pm.BizBrowsingPermissionDao;
import com.rs.module.base.dao.pm.BizQuickAccessDao;
import com.rs.module.base.dao.pm.BizQuickAccessTypeDao;
import com.rs.module.base.entity.pm.BizBrowsingPermissionDO;
import com.rs.module.base.entity.pm.BizQuickAccessDO;
import com.rs.module.base.entity.pm.BizQuickAccessTypeDO;
import com.rs.module.base.util.PermissionUtil;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.rs.framework.common.pojo.CommonResult.success;


/**
 * 实战平台-监管管理-业务快捷访问入口配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BizQuickAccessServiceImpl extends BaseServiceImpl<BizQuickAccessDao, BizQuickAccessDO> implements BizQuickAccessService {

    @Resource
    private BizQuickAccessTypeDao bizQuickAccessTypeDao;
    @Resource
    private BizQuickAccessDao bizQuickAccessDao;
    @Resource
    private BizBrowsingPermissionDao bizBrowsingPermissionDao;

    @Override
    public String createBizQuickAccess(BizQuickAccessSaveReqVO createReqVO) {
        // 插入
        BizQuickAccessDO bizQuickAccess = BeanUtils.toBean(createReqVO, BizQuickAccessDO.class);
        bizQuickAccessDao.insert(bizQuickAccess);
        // 返回
        return bizQuickAccess.getId();
    }

    @Override
    public void updateBizQuickAccess(BizQuickAccessSaveReqVO updateReqVO) {
        // 校验存在
        validateBizQuickAccessExists(updateReqVO.getId());
        // 更新
        BizQuickAccessDO updateObj = BeanUtils.toBean(updateReqVO, BizQuickAccessDO.class);
        bizQuickAccessDao.updateById(updateObj);
    }

    @Override
    public void deleteBizQuickAccess(String id) {
        // 删除
        bizQuickAccessDao.deleteById(id);
    }

    private void validateBizQuickAccessExists(String id) {
        if (bizQuickAccessDao.selectById(id) == null) {
            throw new ServerException("业务入口不存在");
        }
    }

    @Override
    public BizQuickAccessDO getBizQuickAccess(String id) {
        return bizQuickAccessDao.selectById(id);
    }

    @Override
    public CommonResult buildAccessComponent(List<String> markList) {
        List<JSONObject> result = new ArrayList<>();
        List<BizQuickAccessTypeDO> typeList = bizQuickAccessTypeDao.getByMarks(markList);
        List<BizQuickAccessDO> accessList = bizQuickAccessDao.getByTypeMarks(markList);
        Map<String, List<BizQuickAccessDO>> accessMap = accessList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(
                        BizQuickAccessDO::getTypeMark,
                        Collectors.toList()
                ));

        SessionUser sessionUser =  SessionUserUtil.getSessionUser();
        if(sessionUser.getIsAdmin()) { // 管理员，返回全部
            for(BizQuickAccessTypeDO type : typeList) {
                JSONObject resultMap = new JSONObject();
                resultMap.put("title", type.getName());
                resultMap.put("children", accessMap.get(type.getMark()));
                result.add(resultMap);
            }
            return success(result);
        }

        List<String> typeIdList = typeList.stream().map(type -> type.getId()).collect(Collectors.toList());
        List<BizBrowsingPermissionDO> typeAllPermissionS = bizBrowsingPermissionDao.getByBizIds(typeIdList);
        Map<String, List<BizBrowsingPermissionDO>> typePermissionMap = typeAllPermissionS.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(
                        BizBrowsingPermissionDO::getBizId,
                        Collectors.toList()
                ));
        for(BizQuickAccessTypeDO type : typeList) {
            // 判断业务分类的权限
            List<BizBrowsingPermissionDO> typePermissionList = typePermissionMap.get(type.getId());
            if (!PermissionUtil.judgePermission(typePermissionList)) {
                continue;
            }
            List<BizQuickAccessDO> children = new ArrayList<>();
            List<BizQuickAccessDO> bizQuickAccessDOS = accessMap.get(type.getMark());

            List<String> accessIdList = bizQuickAccessDOS.stream().map(accessDO -> accessDO.getId()).collect(Collectors.toList());
            List<BizBrowsingPermissionDO> accessAllPermission = bizBrowsingPermissionDao.getByBizIds(accessIdList);
            Map<String, List<BizBrowsingPermissionDO>> accessPermissionMap = accessAllPermission.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(
                            BizBrowsingPermissionDO::getBizId,
                            Collectors.toList()
                    ));
            // 判断分类下的业务入口的权限
            for (BizQuickAccessDO accessDO : bizQuickAccessDOS) {
                List<BizBrowsingPermissionDO> accessPermissionList = accessPermissionMap.get(accessDO.getId());
                if (PermissionUtil.judgePermission(accessPermissionList)) {
                    children.add(accessDO);
                }
            }
            JSONObject resultMap = new JSONObject();
            resultMap.put("title", type.getName());
            resultMap.put("children", BeanUtils.toBean(children, BizQuickAccessRespVO.class));
            result.add(resultMap);
        }
        return success(result);
    }

    @Override
    public CommonResult buildAllAccessComponent() {
        List<BizQuickAccessTypeDO> typeList = bizQuickAccessTypeDao.getAll();
        List<String> markList = typeList.stream().map(type -> type.getMark()).collect(Collectors.toList());
        return buildAccessComponent(markList);
    }

}
