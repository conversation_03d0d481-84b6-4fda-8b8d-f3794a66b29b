package com.rs.module.base.entity;

import com.rs.framework.mybatis.annotation.Query;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName RyVO
 * <AUTHOR>
 * @Date 2025/4/8 11:29
 * @Version 1.0
 */
@Data
public class RoomVO extends BaseVO {
    @ApiModelProperty("监室名称")
    @Query(sql = "select * from acp_pm_area_prison_room where id = '${roomId}'")
    public String roomName;
}
