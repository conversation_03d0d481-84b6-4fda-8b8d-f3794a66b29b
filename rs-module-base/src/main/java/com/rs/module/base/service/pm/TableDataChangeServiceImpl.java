package com.rs.module.base.service.pm;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.module.base.dao.pm.TableDataChangeDao;
import com.rs.module.base.dto.pm.TableDataChangeDTO;
import com.rs.module.base.entity.pm.TableDataChangeDO;
import com.rs.module.base.service.dataChangeHandler.TableDataChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据变动调度服务
 *
 * <AUTHOR>
 * @date 2023/7/24 9:37
 */
@Slf4j
@Service
public class TableDataChangeServiceImpl implements TableDataChangeService {

    /**
     * 每次数据库查询出10条变动进行下发
     */
    private static final int DEFAULT_LIMIT = 10;

    @Autowired
    private TableDataChangeDao tableDataChangeDao;

    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public void handler(String tableName) {
        while (true) {
            List<TableDataChangeDO> entities = this.queryDateChange(DEFAULT_LIMIT, tableName);
            for (TableDataChangeDO entity : entities) {
                try {
                    this.notice(entity);
                } catch (Exception ex) {
                    log.error("", ex);
                }
            }
            List<Long> idList = entities.stream().map(TableDataChangeDO::getId).collect(Collectors.toList());
            this.deleteByIdList(idList);
            if (entities.size() < DEFAULT_LIMIT) {
                break;
            }
        }
    }

    private void notice(TableDataChangeDO entity) {
        TableDataChangeDTO dto = new TableDataChangeDTO();
        dto.setTableName(entity.getTableName());
        dto.setUpdateType(entity.getUpdateType());
        dto.setPkId(entity.getPkId());
        dto.setOldJsonDataMap(JSON.parseObject(entity.getOldJsonData(), HashMap.class));
        dto.setCreateTime(entity.getAddTime());
        String createTimeStr = entity.getAddTime() == null ? null : new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(entity.getAddTime());
        String logMsg = String.format("ct=%s %s %s %s", createTimeStr, entity.getTableName(), entity.getUpdateType(), entity.getPkId());
        log.debug(logMsg);
        // 事件驱动
        applicationContext.publishEvent(new TableDataChangeEvent(dto));
    }

    /**
     * 查询出指定条变动数据
     *
     * @param limit
     * @param tableName
     * @return
     */
    private List<TableDataChangeDO> queryDateChange(int limit, String tableName) {
        Date createTimeGreatThan = new Date();
        createTimeGreatThan = new Date(createTimeGreatThan.getYear(), createTimeGreatThan.getMonth() - 1, createTimeGreatThan.getDate());
        List<TableDataChangeDO> tableDataChangeDO = tableDataChangeDao.queryDateChange(tableName, createTimeGreatThan, limit);
        return tableDataChangeDO;
    }

    /**
     * 根据id标记删除
     *
     * @param idList
     */
    private void deleteByIdList(List<Long> idList) {
        if (idList == null || idList.isEmpty()) {
            return;
        }
        TableDataChangeDO entity = new TableDataChangeDO();
        entity.setIsDel(1);
        tableDataChangeDao.update(entity, new LambdaQueryWrapper<TableDataChangeDO>().in(TableDataChangeDO::getId, idList));
    }

}
