package com.rs.module.base.dao.pm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.base.entity.pm.BizDeskDO;
import org.apache.ibatis.annotations.Mapper;

/**
* 实战平台-监管管理-业务总台配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BizDeskDao extends IBaseDao<BizDeskDO> {

    default BizDeskDO getByMark(String mark) {
        return this.selectOne(new LambdaQueryWrapper<BizDeskDO>().eq(BizDeskDO::getMark, mark));
    }

}
