package com.rs.module.base.enums;

/**
 * 值班类型
 * <AUTHOR>
 * @date 2025/7/3 20:26
 */
public enum StaffDutyTypeEnum {

    DOCTOR(1, "医务人员值班"),
    ON_DUTY_AT_INSTITUTE(2, "所内值班");

    StaffDutyTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private Integer code;

    private String name;

    public Integer getCode() {
        return code;
    }
    public String getName() {
        return name;
    }

}
