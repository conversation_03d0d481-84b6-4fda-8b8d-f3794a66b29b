package com.rs.module.base.enums;

/**
 * 区域类型
 */
public enum AreaTypeEnum {
    OTHER("0000", "其他"),
    DETENTION_FACILITY("0001", "监所"),
    DETENTION_AREA("0002", "监区"),
    DETENTION_ROOM("0003", "监室"),
    STAIRWAY("0004", "楼道"),
    CORRIDOR("0005", "走廊"),
    GUARD_ROOM("0006", "门卫室"),
    INTERROGATION_ROOM("0007", "审讯室"),
    ADMISSION_HALL("0008", "收押大厅"),
    OFFICE("0009", "办公室"),
    MONITORING_CENTER("0010", "监控中心"),
    COMPUTER_ROOM("0011", "机房"),
    INFIRMARY("0012", "医务室"),
    ELECTRONIC_EDUCATION_ROOM("0013", "电化教育室"),
    POLICE_EQUIPMENT_ROOM("0014", "警用装备室"),
    FAMILY_VISITING_ROOM("0015", "家属会见室"),
    LAWYER_VISITING_ROOM("0016", "律师会见室"),
    PERIMETER("0017", "周界"),
    SENTRY_POST("0018", "岗哨"),
    MULTIFUNCTION_HALL("0019", "多功能厅"),
    CONFERENCE_ROOM("0020", "会议室"),
    TRAINING_FIELD("0021", "训练场"),
    WORKSHOP("0022", "工场"),
    BOILER_ROOM("0023", "开水房"),
    PSYCHOLOGICAL_COUNSELING_ROOM("0024", "心理咨询室"),
    SOCIAL_WORKER_OFFICE("0025", "社工办公室"),
    KITCHEN("0026", "厨房"),
    CONFLICT_RESOLUTION_ROOM("0027", "矛盾化解室"),
    WAREHOUSE("0028", "仓库"),
    DRESSING_ROOM("0029", "更衣室"),
    AB_GATE("0030", "AB门"),
    INQUIRY_ROOM("0031", "询问室"),
    TALKING_ROOM("0032", "谈话室"),
    DUTY_ROOM("0033", "值班室"),
    MEDICAL_EXAMINATION_CENTER("0034", "体检中心"),
    RECEPTION_HALL("0035", "接待大厅"),
    LAWN("0036", "草坪"),
    FLOOR("0037", "楼层"),
    SUB_CONTROL_ROOM("0038", "分控室");

    private final String code;
    private final String name;

    AreaTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static AreaTypeEnum getByCode(String code) {
        for (AreaTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据名称获取code
     * @param name 区域类型名称
     * @return 对应的code，如果找不到则返回null
     */
    public static String getCodeByName(String name) {
        for (AreaTypeEnum type : values()) {
            if (type.name.equals(name)) {
                return type.code;
            }
        }
        return null;
    }

} 