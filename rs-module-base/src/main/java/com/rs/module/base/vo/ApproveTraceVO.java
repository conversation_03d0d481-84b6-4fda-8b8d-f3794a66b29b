package com.rs.module.base.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "审批 意见返回的VO")
@Data
public class ApproveTraceVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("节点名称")
    private String nodeName;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date approverTime;

    @ApiModelProperty("审批结果 1 同意")
    private String approvalResult;

    @ApiModelProperty("审批结果-中文")
    private String approvalResultName;

    @ApiModelProperty("审核意见")
    private String approvalComments;

}
