package com.rs.module.base.controller.admin.pm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-监管管理-设备授权 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DeviceAuthRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("设备id")
    private String deviceId;
    @ApiModelProperty("授权给ip")
    private String authIp;
    @ApiModelProperty("授权给用户")
    private String authUser;
    @ApiModelProperty("授权的给设备")
    private String authDeviceId;
    @ApiModelProperty("授权给角色")
    private String authRole;
}
