package com.rs.module.base.tag.service;


import com.rs.module.base.entity.pm.PrisonerTagDO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TagService {
    // 添加标签
    void addTag(String jgrybm, String tagCode,String tagType,String tagName,String businessType,String businessId);

    // 移除标签
    void removeTag(String jgrybm, String tagCode,String tagType);

    // 移除所有标签
    void removeAllTags(String businessId, String businessType);

    // 获取实体的所有标签
    List<PrisonerTagDO> getEntityTags(String jgrybm);

}
