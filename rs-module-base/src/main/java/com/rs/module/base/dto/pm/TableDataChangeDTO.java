package com.rs.module.base.dto.pm;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.Map;

/**
 * 数据变动dto
 *
 * <AUTHOR>
 * @date 2023/7/24 9:44
 */
@Data
public class TableDataChangeDTO {

    /**
     * 数据变动的表名
     */
    private String tableName;
    /**
     * INSERT、DELETE、UPDATE
     * 使用枚举: TableDataChangeUpdateTypeEnum
     */
    private String updateType;
    /**
     * 表主键
     */
    private String pkId;
    /**
     * 变动前数据备份。key为表列明
     */
    private Map<String, Object> oldJsonDataMap;
    /**
     * 数据变动检测时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
