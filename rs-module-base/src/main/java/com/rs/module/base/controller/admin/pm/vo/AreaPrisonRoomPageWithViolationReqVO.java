package com.rs.module.base.controller.admin.pm.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-监管管理-区域监室分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AreaPrisonRoomPageWithViolationReqVO extends PageParam{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("区域代码")
    private String regCode;

    @ApiModelProperty("机构编号")
    private String orgCode;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("监区id")
    private String areaId;

    @ApiModelProperty("监区名称")
    private String areaName;

    @ApiModelProperty("是否主协管监室")
    private Boolean mainAssistantManager;

    @ApiModelProperty("监室号ID数组")
    private List<String> roomCodes;

}
