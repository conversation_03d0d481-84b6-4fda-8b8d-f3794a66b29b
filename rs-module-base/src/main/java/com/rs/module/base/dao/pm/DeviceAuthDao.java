package com.rs.module.base.dao.pm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.base.controller.admin.pm.vo.DeviceAuthListReqVO;
import com.rs.module.base.controller.admin.pm.vo.DeviceAuthPageReqVO;
import com.rs.module.base.entity.pm.DeviceAuthDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 实战平台-监管管理-设备授权 Dao
*
* <AUTHOR>
*/
@Mapper
public interface DeviceAuthDao extends IBaseDao<DeviceAuthDO> {


    default PageResult<DeviceAuthDO> selectPage(DeviceAuthPageReqVO reqVO) {
        Page<DeviceAuthDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<DeviceAuthDO> wrapper = new LambdaQueryWrapperX<DeviceAuthDO>()
            .eqIfPresent(DeviceAuthDO::getDeviceId, reqVO.getDeviceId())
            .eqIfPresent(DeviceAuthDO::getAuthIp, reqVO.getAuthIp())
            .eqIfPresent(DeviceAuthDO::getAuthUser, reqVO.getAuthUser())
            .eqIfPresent(DeviceAuthDO::getAuthDeviceId, reqVO.getAuthDeviceId())
            .eqIfPresent(DeviceAuthDO::getAuthRole, reqVO.getAuthRole())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(DeviceAuthDO::getAddTime);
        }
        Page<DeviceAuthDO> deviceAuthPage = selectPage(page, wrapper);
        return new PageResult<>(deviceAuthPage.getRecords(), deviceAuthPage.getTotal());
    }
    default List<DeviceAuthDO> selectList(DeviceAuthListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DeviceAuthDO>()
            .eqIfPresent(DeviceAuthDO::getDeviceId, reqVO.getDeviceId())
            .eqIfPresent(DeviceAuthDO::getAuthIp, reqVO.getAuthIp())
            .eqIfPresent(DeviceAuthDO::getAuthUser, reqVO.getAuthUser())
            .eqIfPresent(DeviceAuthDO::getAuthDeviceId, reqVO.getAuthDeviceId())
            .eqIfPresent(DeviceAuthDO::getAuthRole, reqVO.getAuthRole())
        .orderByDesc(DeviceAuthDO::getAddTime));    }


    }
