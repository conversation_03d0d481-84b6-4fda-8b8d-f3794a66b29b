package com.rs.module.base.service.sys;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.module.base.dao.sys.MsgTemplateDao;
import com.rs.module.base.entity.sys.MsgTemplateDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


/**
 * 实战平台-系统-消息模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MsgTemplateServiceImpl extends BaseServiceImpl<MsgTemplateDao, MsgTemplateDO> implements MsgTemplateService {

    @Resource
    private MsgTemplateDao msgTemplateDao;



}
