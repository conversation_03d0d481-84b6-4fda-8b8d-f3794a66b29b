package com.rs.module.base.controller.admin.pm.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;

@ApiModel(description = "管理后台 - 实战平台-监管管理-设备授权分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DeviceAuthPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("授权给ip")
    private String authIp;

    @ApiModelProperty("授权给用户")
    private String authUser;

    @ApiModelProperty("授权的给设备")
    private String authDeviceId;

    @ApiModelProperty("授权给角色")
    private String authRole;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
