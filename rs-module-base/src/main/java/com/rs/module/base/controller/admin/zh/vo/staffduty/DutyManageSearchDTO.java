package com.rs.module.base.controller.admin.zh.vo.staffduty;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class DutyManageSearchDTO extends BaseVO implements Serializable {
    private static final long serialVersionUID = 1L;
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @ApiModelProperty("开始时间")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @ApiModelProperty("结束时间")
    private Date endTime;
}
