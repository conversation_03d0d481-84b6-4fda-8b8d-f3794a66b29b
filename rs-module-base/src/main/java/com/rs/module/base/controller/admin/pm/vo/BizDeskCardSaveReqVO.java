package com.rs.module.base.controller.admin.pm.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel(description = "管理后台 - 实战平台-监管管理-业务总台卡片配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BizDeskCardSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("业务总台ID")
    @NotEmpty(message = "业务总台ID不能为空")
    private String bizDeskId;

    @ApiModelProperty("总台标识")
    @NotEmpty(message = "总台标识不能为空")
    private String bizDeskMark;

    @ApiModelProperty("卡片名称")
    @NotEmpty(message = "卡片名称不能为空")
    private String name;

    @ApiModelProperty("卡片地址")
    private String cardUrl;

    @ApiModelProperty("地址类型(1:外部0:内部)")
    @NotEmpty(message = "地址类型(1:外部0:内部)不能为空")
    private String urlType;

    @ApiModelProperty("加载类型")
    private String loaderType;

    @ApiModelProperty("容器标识（微前端模式使用）")
    private String containerMark;

    @ApiModelProperty("微前端地址")
    private String microWebUrl;

    @ApiModelProperty("路径激活规则")
    private String activeRule;

    @ApiModelProperty("是否启动浏览权限配置")
    @NotNull(message = "是否启动浏览权限配置不能为空")
    private Short isEnableBrowsingPermission;

    @ApiModelProperty("是否启用")
    @NotNull(message = "是否启用不能为空")
    private Short isEnable;

    @ApiModelProperty("排序Id")
    @NotNull(message = "排序Id不能为空")
    private Integer orderId;

    @ApiModelProperty("备注")
    private String remark;

}
