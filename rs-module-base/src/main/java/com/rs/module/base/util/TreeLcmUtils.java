package com.rs.module.base.util;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 树工具类
 *
 * <AUTHOR>
 */
public class TreeLcmUtils {

    /**
     * 实现集成此类
     *
     * @param <T>
     * <AUTHOR>
     */
    public static abstract class TreeNode<T> {

        protected abstract String getId();

        protected abstract String getParentId();

        protected abstract void setChild(List<T> child);

        /**
         * 需要迭代子元素时需要进行重写
         *
         * @return
         */
        protected List<T> getChild() {
            return null;
        }
    }

    /**
     * 不允许对此类中在添加属性。如果一个可放置mark中。多个可继承此类。不允许修改此类
     *
     * <AUTHOR>
     * @date 2022/8/29 12:26
     */
    @Data
    @Accessors(chain = true)
    public static class TreeNodeVO<T> extends TreeNode<T> {

        private String id;

        private String nodeName;

        private String parentId;
        /**
         * 扩展属性 可用于控制是否拥有此节点，编码等。 如果有一个拓展属性可放到此
         */
        private Object mark;

        @Accessors(chain = false)
        private List<T> child = Collections.emptyList();

        public TreeNodeVO() {

        }

        public TreeNodeVO(String id, String nodeName) {
            this.id = id;
            this.nodeName = nodeName;
        }
    }

    /**
     * 数遍历回调
     *
     * @param <T>
     * <AUTHOR>
     */
    public interface TreeBuildCallback<T> {
        void callback(T node);
    }

    /**
     * @param <T>
     * @param parentId 父节点ID
     * @param allList  数所有节点列表
     * @return
     */
    public static <T extends TreeNode<T>> List<T> build(String parentId, List<T> allList) {
        return build(parentId, allList, null);
    }

    /**
     * @param <T>
     * @param parentId 父节点ID
     * @param allList  数所有节点列表
     * @param callback 遍历每个节点回调
     * @return
     */
    public static <T extends TreeNode<T>> List<T> build(String parentId, List<T> allList, TreeBuildCallback<T> callback) {
        if (allList == null) {
            return Lists.newArrayList();
        }
        List<T> list = allList.stream().filter(f -> parentId.equals(f.getParentId())).collect(Collectors.toList());
        for (T node : list) {
            List<T> child = build(node.getId(), allList, callback);
            node.setChild(child);
            if (callback != null) {
                callback.callback(node);
            }
        }
        return list;
    }

    /**
     * 迭代树中每个元素
     *
     * @param treeList
     * @param callback
     * @param <T>
     */
    public static <T extends TreeNode<T>> void forEach(List<T> treeList, TreeBuildCallback<T> callback) {
        if (treeList == null || treeList.isEmpty()) {
            return;
        }
        for (T node : treeList) {
            callback.callback(node);
            forEach(node.getChild(), callback);
        }
    }

}
