package com.rs.module.base.entity.pm.repository;

import com.rs.module.base.entity.pm.AreaDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * @ClassName AreaRepository
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/19 17:28
 * @Version 1.0
 */
@Repository
public interface AreaRepository extends JpaRepository<AreaDO, String> {
    AreaDO findByAreaCode(String areaCode);
}
