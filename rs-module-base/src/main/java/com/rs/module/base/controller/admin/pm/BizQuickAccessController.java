package com.rs.module.base.controller.admin.pm;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.BizDeskCardQxVO;
import com.rs.module.base.controller.admin.pm.vo.BizQuickAccessRespVO;
import com.rs.module.base.controller.admin.pm.vo.BizQuickAccessSaveReqVO;
import com.rs.module.base.entity.pm.BizQuickAccessDO;
import com.rs.module.base.service.pm.BizQuickAccessService;
import com.rs.module.base.service.pm.BizQuickAccessTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.Arrays;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;


@Api(tags = "业务入口管理")
@RestController
@RequestMapping("/acp/pm/bizQuickAccess")
@Validated
public class BizQuickAccessController {

    @Resource
    private BizQuickAccessService bizQuickAccessService;
    @Resource
    private BizQuickAccessTypeService bizQuickAccessTypeService;

    @PostMapping("/create")
    @ApiOperation(value = "创建业务入口")
    public CommonResult<String> createBizQuickAccess(@Valid @RequestBody BizQuickAccessSaveReqVO createReqVO) {
        Assert.notNull(createReqVO.getMark(), "业务标识不能为空");
        if (StringUtil.isEmpty(createReqVO.getId())) {
            BizQuickAccessDO one = bizQuickAccessService.getOne(new LambdaQueryWrapper<BizQuickAccessDO>()
                    .eq(BizQuickAccessDO::getMark, createReqVO.getMark()));
            if (null != one) {
                return error("该业务入口标识已存在");
            }
            bizQuickAccessService.createBizQuickAccess(createReqVO);
        } else {
            bizQuickAccessService.updateBizQuickAccess(createReqVO);
        }
        return success("", "保存业务入口成功");
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除业务入口")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteBizQuickAccess(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           bizQuickAccessService.deleteBizQuickAccess(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得业务入口")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<BizQuickAccessRespVO> getBizQuickAccess(@RequestParam("id") String id) {
        BizQuickAccessDO bizQuickAccess = bizQuickAccessService.getBizQuickAccess(id);
        return success(BeanUtils.toBean(bizQuickAccess, BizQuickAccessRespVO.class));
    }

    /**
     * 通过标识查询业务分类信息
     * @param marks 业务分类标识
     * @return
     */
    @GetMapping("/{marks}")
    @ApiOperation(value = "通过标识查询业务入口信息", response = BizDeskCardQxVO.class)
    public CommonResult getByMark(@PathVariable("marks") String marks) {
        List<String> markList = Arrays.asList(marks.split(","));
        return bizQuickAccessService.buildAccessComponent(markList);
    }

    /**
     * 查询所有业务分类信息
     * @return
     */
    @GetMapping("/getAll")
    @ApiOperation(value = "查询所有业务入口信息", response = BizDeskCardQxVO.class)
    public CommonResult getAll() {
        return bizQuickAccessService.buildAllAccessComponent();
    }

}
