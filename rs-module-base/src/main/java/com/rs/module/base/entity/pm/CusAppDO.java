package com.rs.module.base.entity.pm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 自定义应用管理 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_cus_app")
@Data
@EqualsAndHashCode(callSuper = true)
public class CusAppDO extends BaseDO {
	private static final long serialVersionUID = 5858833509270167341L;
	
	/**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 系统ID
     */
    private String systemId;
    /**
     * 应用名称
     */
    private String yymc;
    /**
     * 链接地址
     */
    private String ljdz;
    /**
     * 应用分类ID
     */
    private String flId;
    /**
     * 是否内部应用(0:否1:是)
     */
    private String sfnb;
    /**
     * 是否公共应用(0:否1:是)
     */
    private String sfgg;
    /**
     * 应用类型(0:pc,1:移动端)
     */
    private String yylx;
    /**
     * 是否禁用(0:否1:是)
     */
    private String sfjy;
    /**
     * 排序
     */
    private Integer orderId;
    /**
     * 应用介绍
     */
    private String yyjs;
    /**
     * 应用图标
     */
    private String yytb;
    /**
     * 模型标识
     */
    private String modelMark;
}
