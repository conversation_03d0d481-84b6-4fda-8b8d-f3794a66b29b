package com.rs.module.base.dao.pm;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.base.entity.pm.TableDataChangeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/24 11:05
 */
@Mapper
public interface TableDataChangeDao extends IBaseDao<TableDataChangeDO> {

    /**
     * 查询指定条数
     *
     * @param limit
     * @param tableName
     * @param addTimeGreatThan
     * @return
     */
    List<TableDataChangeDO> queryDateChange(@Param("tableName") String tableName,
                                            @Param("addTimeGreatThan") Date addTimeGreatThan,
                                            @Param("limit") int limit);


}
