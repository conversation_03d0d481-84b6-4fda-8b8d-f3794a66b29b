package com.rs.module.base.controller.admin.pm.vo.device;

import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.acp.entity.pm.BaseDeviceInscreenDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-监管管理-设备信息新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseDeviceSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

//    @ApiModelProperty("设备编码")
//    @NotEmpty(message = "设备编码不能为空")
//    private String deviceCode;

    @ApiModelProperty("设备名称")
    @NotEmpty(message = "设备名称不能为空")
    private String deviceName;

    @ApiModelProperty("设备类型（字典：ZD_SBLXDM）")
    @NotEmpty(message = "设备类型（字典：ZD_SBLXDM）不能为空")
    private String deviceTypeId;

    @ApiModelProperty("厂家")
    private String factory;

    @ApiModelProperty("型号")
    private String model;

    @ApiModelProperty("协议")
    private String protocol;

    @ApiModelProperty("ip地址")
    private String ipAddress;

    @ApiModelProperty("端口")
    private Integer port;

    @ApiModelProperty("设备用户名")
    private String devUserName;

    @ApiModelProperty("设备密码")
    private String devPassword;

    @ApiModelProperty("点位名称")
    private String pointName;

    @ApiModelProperty("通道编号")
    private String channelId;

    @ApiModelProperty("通道名称")
    private String channelName;

    @ApiModelProperty("设备国标编号")
    private String gbCode;

    @ApiModelProperty("mac地址")
    private String macAddress;

    @ApiModelProperty("在线时间")
    private Date onlineTime;

    @ApiModelProperty("所属区域")
//    @NotEmpty(message = "所属区域不能为空")
    private String areaId;

    @ApiModelProperty("监室号")
    private String roomId;

//    @ApiModelProperty("是否启用")
//    @NotNull(message = "是否启用不能为空")
//    private Integer isEnabled;

    @ApiModelProperty("设备状态（字典：ZD_SBZTDM）")
    @NotEmpty(message = "设备状态（字典：ZD_SBZTDM）不能为空")
    private String deviceStatus;

    @ApiModelProperty("摄像头关联id，多个逗号分割")
    private String refDeviceId;

    @ApiModelProperty("")
    private String orgCode;
    @ApiModelProperty("监室终端（仓内外屏）信息")
    private BaseDeviceInscreenDO deviceInscreen;

    @ApiModelProperty(value = "主机编号")
    private Integer hostNum;

    @ApiModelProperty(value = "设备编号")
    private Integer deviceNum;

    @ApiModelProperty("序列号")
    private String serialNumber;

}
