package com.rs.module.base.service.ry.tag.handle.enums;

public enum RyxxTagBusinessType {
    SystemTag("SystemTag", "系统通用tag"),
    RestraintsTag("RestraintsTag", "是否上戒具");
    private final String description;
    private final String code;

    RyxxTagBusinessType(String code, String description) {
        this.description = description;
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public String getCode() {
        return code;
    }

    @Override
    public String toString() {
        return code;
    }
}
