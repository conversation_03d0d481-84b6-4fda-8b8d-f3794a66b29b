package com.rs.module.base.controller.admin.pm.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 自定义应用分类新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CusAppCatSaveReqVO extends BaseVO{

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("系统ID")
    @NotEmpty(message = "系统ID不能为空")
    private String systemId;

    @ApiModelProperty("分类名称")
    private String flmc;

    @ApiModelProperty("排序")
    private Integer orderId;

    @ApiModelProperty("分类标识")
    private String mark;

    @ApiModelProperty("前缀地址")
    private String preUrl;
}
