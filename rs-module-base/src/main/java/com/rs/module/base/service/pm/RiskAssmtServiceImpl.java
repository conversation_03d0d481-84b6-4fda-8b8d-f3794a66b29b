package com.rs.module.base.service.pm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.RiskAssmtListReqVO;
import com.rs.module.base.controller.admin.pm.vo.RiskAssmtPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.RiskAssmtSaveReqVO;
import com.rs.module.base.dao.pm.RiskAssmtDao;
import com.rs.module.base.entity.pm.RiskAssmtDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 实战平台-监管管理-风险评估 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RiskAssmtServiceImpl extends BaseServiceImpl<RiskAssmtDao, RiskAssmtDO> implements RiskAssmtService {

    @Resource
    private RiskAssmtDao riskAssmtDao;

    @Override
    public String createRiskAssmt(RiskAssmtSaveReqVO createReqVO) {
        // 插入
        RiskAssmtDO riskAssmt = BeanUtils.toBean(createReqVO, RiskAssmtDO.class);
        riskAssmtDao.insert(riskAssmt);
        // 返回
        return riskAssmt.getId();
    }

    @Override
    public void updateRiskAssmt(RiskAssmtSaveReqVO updateReqVO) {
        // 校验存在
        validateRiskAssmtExists(updateReqVO.getId());
        // 更新
        RiskAssmtDO updateObj = BeanUtils.toBean(updateReqVO, RiskAssmtDO.class);
        riskAssmtDao.updateById(updateObj);
    }

    @Override
    public void deleteRiskAssmt(String id) {
        // 校验存在
        validateRiskAssmtExists(id);
        // 删除
        riskAssmtDao.deleteById(id);
    }

    private void validateRiskAssmtExists(String id) {
        if (riskAssmtDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-风险评估数据不存在");
        }
    }

    @Override
    public RiskAssmtDO getRiskAssmt(String id) {
        return riskAssmtDao.selectById(id);
    }

    @Override
    public PageResult<RiskAssmtDO> getRiskAssmtPage(RiskAssmtPageReqVO pageReqVO) {
        return riskAssmtDao.selectPage(pageReqVO);
    }

    @Override
    public List<RiskAssmtDO> getRiskAssmtList(RiskAssmtListReqVO listReqVO) {
        return riskAssmtDao.selectList(listReqVO);
    }

    @Override
    public RiskAssmtDO getOneRiskAssmtByRybm(String rybm) {
        List<RiskAssmtDO> riskAssmtDOList = riskAssmtDao.selectList(new LambdaQueryWrapper<RiskAssmtDO>()
                .eq(RiskAssmtDO::getJgrybm, rybm)
                .orderByDesc(RiskAssmtDO::getUpdateTime));
        if (!riskAssmtDOList.isEmpty()) {
            return riskAssmtDOList.get(0);
        }
        return null;
    }


}
