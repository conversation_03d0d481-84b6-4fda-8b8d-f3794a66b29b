package com.rs.module.base.dao.zh;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.base.controller.admin.zh.vo.staffduty.*;
import com.rs.module.base.entity.zh.StaffDutyTemplateDO;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

@Mapper
public interface StaffDutyTemplateDao extends IBaseDao<StaffDutyTemplateDO> {

  @Select("select 1 from acp_zh_staff_duty_template where name=#{name} and  is_del = 0 and type = #{type} ")
  List<Integer> checkName(@Param(value = "name") String name, @Param(value = "type") Integer type);

  /**
   * 编辑的时候判断是否重复
   */
  Integer checkNameById(@Param(value = "name") String name, @Param(value = "id") String id,  @Param(value = "type") Integer type);



  StaffDutyTemplateRespVO info(@Param(value = "id") String id);

  @Select("SELECT\n" +
          "\t1 \n" +
          "FROM\n" +
          "\tacp_zh_staff_duty_record A \n" +
          "WHERE\n" +
          "\tA.is_del = '0' \n" +
          "\tAND A.duty_date >= CURRENT_DATE ")
  List<Integer> tempOpenCheck();

  @Update("update acp_zh_staff_duty_template set status = '0' where status = '1' and type = #{type}")
  void initStatus(@Param("type") Integer type);

  @Update("update acp_zh_staff_duty_record set is_del = 1 where duty_date >= CURRENT_DATE")
  void delRecord();

  @Delete("delete from acp_zh_staff_duty_record_person " +
          "where record_id in" +
          "(select id from acp_zh_staff_duty_record where duty_date >= CURRENT_DATE)")
  void delPersonByByRecordId();

  @Select("select id,org_code from acp_zh_staff_duty_template where status = '1' and is_del='0' and type = #{type}")
  List<Map<String,Object>> getUseTempId(@Param("type") Integer type);

}
