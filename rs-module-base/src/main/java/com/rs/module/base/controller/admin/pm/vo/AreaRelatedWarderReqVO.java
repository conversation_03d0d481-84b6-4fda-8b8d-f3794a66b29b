package com.rs.module.base.controller.admin.pm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 实战平台-监管管理-监室主协管人员新增/修改 Request VO")
@Data
public class AreaRelatedWarderReqVO {
private static final long serialVersionUID = 1L;
//    @ApiModelProperty("主键")
//    private String id;
//
//    @ApiModelProperty("更新时间")
//    private Date updateTime2;

    @ApiModelProperty("民警id")
    private String policeId;

    @ApiModelProperty("民警名字")
    private String policeName;

    @ApiModelProperty("民警身份证号")
    private String policeSfzh;

    @ApiModelProperty("用户类型 主 w,协 a,机动 m")
    private String userType;

    @ApiModelProperty("监室ID")
    private String roomId;

}
