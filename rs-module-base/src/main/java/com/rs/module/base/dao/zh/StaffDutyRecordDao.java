package com.rs.module.base.dao.zh;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.base.controller.admin.zh.vo.staffduty.*;
import com.rs.module.base.entity.zh.StaffDutyRecordDO;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface StaffDutyRecordDao extends IBaseDao<StaffDutyRecordDO> {
    List<DutyManageHeaderVO> getHeaderList(@Param("id") String id);

    /***
     * 管教岗-首页-值班
     * @param id
     * @return
     */
    List<DutyManageHeaderVO> getIndexHeaderListByNowDay(@Param("id") String id,@Param("type") Integer type,@Param("postId") String postId);

    @Select("select id from acp_zh_staff_duty_template where status = '1' and is_del='0' and type = #{type} and org_code = #{orgCode} LIMIT 1")
    String getTempId(@Param("orgCode") String orgCode,@Param("type") Integer type);

    @Select("select count(1) from acp_zh_staff_duty_post where temp_id =#{tempId} and has_sub_post = '1'")
    Integer getHasRoleByTempId(@Param("tempId") String tempId);


    Map<String, Object> getTemIdAndDateByDate(@Param("beginDate") String beginDate, @Param("orgCode") String orgCode,@Param("type") Integer type);


    @Select("SELECT * from acp_zh_staff_duty_template WHERE is_del = '0' and status = '1' and  org_code =#{orgCode} and type = #{templateType} LIMIT 1")
    StaffDutyTemplateRespVO getNowTempId(@Param("orgCode") String orgCode, @Param("templateType") Integer templateType);

    @Select("SELECT\n" +
            "\t* \n" +
            "FROM\n" +
            "\tacp_zh_staff_duty_record_person\n" +
            "\tA LEFT JOIN acp_zh_staff_duty_record b ON A.record_id = b.ID \n" +
            "WHERE\n" +
            "\tA.post_key = #{postKey} \n" +
            "\tAND b.is_del = '0' \n" +
            "\tAND to_char( b.duty_date, 'yyyy-mm-dd' ) = #{dutyDate}")
    List<StaffDutyRecordPersoRespVO> getPersonList(@Param("postKey") String postKey, @Param("dutyDate") String dutyDate);

    @Select("\tSELECT\n" +
            "\ta.post_key\n" +
            "FROM\n" +
            "\tacp_zh_staff_duty_role_time a\n" +
            "\tLEFT JOIN acp_zh_staff_duty_role b ON A.duty_role_id = b.id\n" +
            "\tINNER JOIN acp_zh_staff_duty_post c on b.post_id = c.id\n" +
            "\tWHERE c.temp_id = #{tempId}")
    List<String> getPostKeyByTempId(@Param("tempId") String tempId);

    @Update("update acp_zh_staff_duty_record set is_del = '1' where temp_id = #{tempId} and duty_date = #{date}")
    void delRecordByDate(@Param("tempId") String tempId, @Param("date") Date date);

    @Update("update acp_zh_staff_duty_record set is_del = '0' where id = #{id}")
    void updateRecordById(@Param("id") String id);

    @Delete("delete from acp_zh_staff_duty_record_person " +
            "where record_id in" +
            "(select id from acp_zh_staff_duty_record where temp_id = #{tempId} and duty_date = #{date} and is_del = '1')")
    void delPersonByByRecordId(@Param("tempId") String tempId, @Param("date") Date date);

    @Delete("delete from acp_zh_staff_duty_record_person " +
            "where record_id = #{id}")
    void delPersonByById(@Param("id") String id);


    @Select("select name from acp_zh_staff_duty_template where id = #{tempId} and type = #{type}")
    String getTempNameById(@Param("tempId") String tempId,@Param("type") Integer type);

    /*@Select("SELECT\n" +
            "\t1 \n" +
            "FROM\n" +
            "\tacp_zh_staff_duty_template_task A \n" +
            "WHERE\n" +
            "\tA.\"date\" = #{date} \n" +
            "\tAND temp_id = #{tempId}")
    List<Integer> checkCopyDate(@Param("date") Date date, @Param("tempId") String tempId);

    @Select("SELECT A\n" +
            "\t.temp_id \n" +
            "FROM\n" +
            "\tacp_zh_staff_duty_template_task A \n" +
            "WHERE\n" +
            "\tA.\"date\" = #{date} \n" +
            "\tAND A.org_code = #{orgCode} \n" +
            "GROUP BY\n" +
            "\tA.temp_id")
    String checkCopyDateNext(@Param("date") Date date, @Param("orgCode") String orgCode);*/

    @Select("SELECT * from acp_zh_staff_duty_record WHERE duty_date =#{date} and temp_id = #{tempId} and is_del = '0' LIMIT 1")
    StaffDutyRecordRespVO getRecordByDate(@Param("date") Date date, @Param("tempId") String tempId);

    @Select("SELECT A\n" +
            "\t.duty_date,\n" +
            "\tA.temp_id \n" +
            "FROM\n" +
            "\tacp_zh_staff_duty_record A \n" +
            "WHERE\n" +
            "\tA.ID = #{id} LIMIT 1")
    StaffDutyRecordRespVO getDelRecordById(@Param("id") String id);

    @Select("SELECT\n" +
            "\t1 \n" +
            "FROM\n" +
            "\tacp_zh_staff_duty_record_person A \n" +
            "WHERE\n" +
            "\tA.record_id IN ( SELECT ID FROM acp_zh_staff_duty_record b WHERE b.duty_date = #{date} AND b.is_del = '0' AND b.temp_id = #{tempId} )")
    List<Integer> checkRecordByDate(@Param("date") Date date, @Param("tempId") String tempId);

    @Select("SELECT * from acp_zh_staff_duty_record_person WHERE record_id = #{id}")
    List<StaffDutyRecordPersoRespVO> getPersonByRecordId(@Param("id") String id);

    //List<String> getPoliceIdByNameAndPostId(@Param("name") String name, @Param("postId") String postId, @Param("orgCode") String orgCode);

    @Select("SELECT\n" +
            "\tb.duty_post_id \n" +
            "FROM\n" +
            "\tacp_zh_staff_duty_role_time\n" +
            "\tA LEFT JOIN acp_zh_staff_duty_role b ON A.duty_role_id = b.ID \n" +
            "WHERE\n" +
            "\tA.post_key = #{postKey} ")
    String getPostIdByPostKey(@Param("postKey") String postKey);


    //List<String> getPoliceIdByName(@Param("name") String name, @Param("orgCode") String orgCode);

}
