package com.rs.module.base.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.base.controller.admin.pm.vo.RiskAssmtListReqVO;
import com.rs.module.base.controller.admin.pm.vo.RiskAssmtPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.RiskAssmtSaveReqVO;
import com.rs.module.base.entity.pm.RiskAssmtDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-监管管理-风险评估 Service 接口
 *
 * <AUTHOR>
 */
public interface RiskAssmtService extends IBaseService<RiskAssmtDO>{

    /**
     * 创建实战平台-监管管理-风险评估
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createRiskAssmt(@Valid RiskAssmtSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-风险评估
     *
     * @param updateReqVO 更新信息
     */
    void updateRiskAssmt(@Valid RiskAssmtSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-风险评估
     *
     * @param id 编号
     */
    void deleteRiskAssmt(String id);

    /**
     * 获得实战平台-监管管理-风险评估
     *
     * @param id 编号
     * @return 实战平台-监管管理-风险评估
     */
    RiskAssmtDO getRiskAssmt(String id);

    /**
    * 获得实战平台-监管管理-风险评估分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-监管管理-风险评估分页
    */
    PageResult<RiskAssmtDO> getRiskAssmtPage(RiskAssmtPageReqVO pageReqVO);

    /**
    * 获得实战平台-监管管理-风险评估列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-监管管理-风险评估列表
    */
    List<RiskAssmtDO> getRiskAssmtList(RiskAssmtListReqVO listReqVO);


    /**
     *
     * @param rybm
     * @return
     */
    RiskAssmtDO getOneRiskAssmtByRybm(String rybm);


}
