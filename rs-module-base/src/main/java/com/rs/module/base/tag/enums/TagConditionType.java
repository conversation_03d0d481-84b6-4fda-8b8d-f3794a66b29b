package com.rs.module.base.tag.enums;

import lombok.Getter;

/**
 * 标签条件类型
 */
@Getter
public enum TagConditionType {
    /**
     * 等于
     */
    EQUALS("等于"),

    /**
     * 不等于
     */
    NOT_EQUALS("不等于"),

    /**
     * 包含
     */
    CONTAINS("包含"),

    /**
     * 不包含
     */
    NOT_CONTAINS("不包含"),

    /**
     * 在列表中
     */
    IN("在列表中"),

    /**
     * 不在列表中
     */
    NOT_IN("不在列表中"),

    /**
     * 大于
     */
    GREATER_THAN("大于"),

    /**
     * 小于
     */
    LESS_THAN("小于"),

    /**
     * 大于等于
     */
    GREATER_EQUALS("大于等于"),

    /**
     * 小于等于
     */
    LESS_EQUALS("小于等于"),

    /**
     * SpEL表达式
     */
    REGEX("正则匹配"),

    /**
     * SpEL表达式
     */
    SPEL("SpEL表达式"),

    /**
     * 自定义服务方法
     */
    CUSTOM("自定义服务方法"),
    IS_NULL("为空"),
    NOT_NULL("不为空");



    private final String description;

    TagConditionType(String description) {
        this.description = description;
    }
}
