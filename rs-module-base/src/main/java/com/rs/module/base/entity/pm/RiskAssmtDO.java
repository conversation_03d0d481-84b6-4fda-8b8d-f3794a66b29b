package com.rs.module.base.entity.pm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import java.util.Date;

/**
 * 实战平台-监管管理-风险评估 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_risk_assmt")
@KeySequence("acp_pm_risk_assmt_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskAssmtDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监室号
     */
    private String roomId;
    /**
     * 评估类型
     */
    private String riskType;
    /**
     * 评估风险等级
     */
    private String riskLevel;
    /**
     * 规范指标
     */
    private String standardQuota;
    /**
     * 调整理由
     */
    private String adjustReason;
    /**
     * 列控建议
     */
    private String controlSuggest;
    /**
     * 评估人
     */
    private String assmtUserId;
    /**
     * 评估人姓名
     */
    private String assmtUserName;
    /**
     * 评估呈批日期
     */
    private Date assmtTime;
    /**
     * 领导意见
     */
    private String leaderOpinion;
    /**
     * 领导意见内容
     */
    private String leaderOpinionContent;
    /**
     * 领导签名
     */
    private String leaderAutograph;
    /**
     * 领导签名时间
     */
    private Date leaderAutographTime;
    /**
     * 思想情绪
     */
    private String emotion;
    /**
     * 心里情况
     */
    private String psychology;
    /**
     * 经办人
     */
    private String operateUserId;
    /**
     * operate_user_name
     */
    private String operateUserName;
    /**
     * 经办时间
     */
    private Date operateTime;
    /**
     * 原风险等级
     */
    private String oldRiskLevel;
    /**
     * 评估状态：0:待评估 1:待审批 2:审批完成
     */
    private Integer status;
    /**
     * 评估原因。字典：RISK_ASSESSMENT_CAUSE
     */
    private String assmtCause;
    /**
     * 具体评估理由
     */
    private String specificAdjustReason;

}
