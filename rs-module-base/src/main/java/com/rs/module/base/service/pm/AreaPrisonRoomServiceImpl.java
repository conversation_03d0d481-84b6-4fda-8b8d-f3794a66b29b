package com.rs.module.base.service.pm;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.cache.RedisClient;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.dto.OrgRespDTO;
import com.rs.adapter.bsp.api.dto.OrgUserRespDTO;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.util.UacUserUtil;
import com.rs.module.base.constant.CommonConstants;
import com.rs.module.base.constant.PoliceWarderUserTypeConstant;
import com.rs.module.base.controller.admin.pm.vo.*;
import com.rs.module.base.controller.admin.pm.vo.terminal.AreaPrisonRoomExportReqVO;
import com.rs.module.base.dao.pm.AreaDao;
import com.rs.module.base.dao.pm.AreaPrisonRoomDao;
import com.rs.module.base.dao.pm.PrisonerInDao;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.entity.pm.PrisonRoomWarderDO;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.enums.AreaTypeEnum;
import com.rs.module.base.enums.RoomStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.springframework.transaction.annotation.Propagation.REQUIRED;


/**
 * 实战平台-监管管理-区域监室 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AreaPrisonRoomServiceImpl extends BaseServiceImpl<AreaPrisonRoomDao, AreaPrisonRoomDO> implements AreaPrisonRoomService {

    @Resource
    private AreaPrisonRoomDao areaPrisonRoomDao;

    @Resource
    private PrisonRoomWarderService roomWarderService;

    //@Resource
    //private  PrisonRoomChangeService prisonRoomChangeService;

    @Resource
    private PrisonerService prisonerService;


    @Resource
    private AreaDao areaDao;
    @Resource
    private BspApi bspApi;
    @Resource
    private PrisonerInDao prisonerInDao;
    private final String BASESTARTNUM = "0001";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createAreaPrisonRoom(AreaPrisonRoomSaveReqVO createReqVO) {
        // 插入
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        AreaPrisonRoomDO room = BeanUtils.toBean(createReqVO, AreaPrisonRoomDO.class);
        String maxRoomId = getMaxIdByRoomType(sessionUser.getOrgCode() + "01" + room.getRoomType()) ;
        room.setId(maxRoomId);
        room.setRoomCode(maxRoomId);
        areaPrisonRoomDao.insert(room);

        AreaDO area = new AreaDO();
        area.setId(room.getId());
        area.setAreaCode(maxRoomId);
        area.setAreaName(room.getRoomName());
        area.setAreaType(AreaTypeEnum.DETENTION_ROOM.getCode());
        area.setParentId(room.getAreaId());
        //根据父节点查询该父节点对应的ID数据，并构造父节点数据
        if (area.getParentId() != null) {
            AreaDO areaDO = areaDao.selectById(area.getParentId());
            Assert.notNull(areaDO, StrUtil.format("父节点ID:{}不存在", area.getParentId()));
            area.setAllParentId((StrUtil.isBlank(areaDO.getAllParentId()) ? StrUtil.EMPTY : areaDO.getAllParentId()) + "#" + area.getParentId());
        }
        areaDao.insert(area);

        // 插入主协管人员
        saveWarder(room.getOrgCode(), room.getRoomCode(), createReqVO.getSponsorList(), createReqVO.getAssistList());
        // 返回
        return room.getId();
    }

    public String getMaxIdByRoomType(String str){
        String maxId = areaPrisonRoomDao.getMaxIdByRoomType(str);
        if (StrUtil.isBlank(maxId)) {
            //区域生成规则：监所编码+01+类型编码+四位递增
            return  str + BASESTARTNUM;
        }
        return increment(maxId);
    }

    public static String increment(String input) {
        // 转换为 BigInteger 并加 1
        BigInteger number = new BigInteger(input);
        BigInteger incremented = number.add(BigInteger.ONE);
        // 格式化为原长度的字符串（保持前导零）
        return String.format("%0" + input.length() + "d", incremented);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAreaPrisonRoom(AreaPrisonRoomSaveReqVO updateReqVO) {
        // 校验存在
        validateAreaPrisonRoomExists(updateReqVO.getId());
        // 更新
        AreaPrisonRoomDO updateObj = BeanUtils.toBean(updateReqVO, AreaPrisonRoomDO.class);
        areaPrisonRoomDao.updateById(updateObj);
        if(StrUtil.isBlank(updateReqVO.getRoomCode() )){
            updateReqVO.setRoomCode(updateReqVO.getId());
        }

        // 删除旧主管人员，插入新主协管人员
//        roomWarderService.deletePrisonRoomWarderByRoomId(updateReqVO.getRoomCode());
//        List<AreaRelatedWarderReqVO>  areaRelatedWarderReqVOList =  updateReqVO.getAreaRelatedWarderReqVOList();
//        List<PrisonRoomWarderDO> sponsorList = new ArrayList<>();
//        List<PrisonRoomWarderDO> assistList = new ArrayList<>();
//        if(CollectionUtil.isNotEmpty(areaRelatedWarderReqVOList)){
//            areaRelatedWarderReqVOList.forEach( e-> {
//                if(PoliceWarderUserTypeConstant.MANAGER.equals(e.getUserType())){
//                    sponsorList.add(BeanUtils.toBean(e, PrisonRoomWarderDO.class));
//                }
//                if(PoliceWarderUserTypeConstant.ASSIST.equals(e.getUserType())){
//                    assistList.add(BeanUtils.toBean(e, PrisonRoomWarderDO.class));
//                }
//            });
//        }
//        updateReqVO.setSponsorList(sponsorList);
//        updateReqVO.setAssistList(assistList);
        //主管
        if(updateReqVO.getSponsorList() == null && updateReqVO.getAssistList() == null   && CollUtil.isNotEmpty(updateReqVO.getAreaRelatedWarderReqVOList()) ){
            List<AreaRelatedWarderReqVO>  areaRelatedWarderReqVOS = updateReqVO.getAreaRelatedWarderReqVOList();
            List<PrisonRoomWarderDO> sponsorList = new ArrayList<>();
            List<PrisonRoomWarderDO> assistList = new ArrayList<>();
            for (AreaRelatedWarderReqVO e : areaRelatedWarderReqVOS) {
                if(PoliceWarderUserTypeConstant.MANAGER.equals(e.getUserType()) && StrUtil.isNotBlank( e.getPoliceId())){
                    PrisonRoomWarderDO warderDO = BeanUtils.toBean(e, PrisonRoomWarderDO.class);
                    warderDO.setPoliceSfzh(UacUserUtil.getUserById(warderDO.getPoliceId()).getIdCard());
                    sponsorList.add(warderDO);
                }
                if(PoliceWarderUserTypeConstant.ASSIST.equals(e.getUserType()) && StrUtil.isNotBlank( e.getPoliceId())){
                    PrisonRoomWarderDO warderDO = BeanUtils.toBean(e, PrisonRoomWarderDO.class);
                    warderDO.setPoliceSfzh(UacUserUtil.getUserById(warderDO.getPoliceId()).getIdCard());
                    assistList.add(warderDO);
                }
            }
            updateReqVO.setSponsorList(sponsorList);
            updateReqVO.setAssistList(assistList);
        }
        saveWarder(updateReqVO.getOrgCode(), updateReqVO.getRoomCode(), updateReqVO.getSponsorList(), updateReqVO.getAssistList());
    }

    private void saveWarder(String orgCode, String roomCode, List<PrisonRoomWarderDO> sponsorList, List<PrisonRoomWarderDO> assistList) {
        if (CollectionUtil.isEmpty(sponsorList) || CollectionUtil.isEmpty(assistList)) {
            return;
        }
        // 处理主管民警数据
        List<PrisonRoomWarderDO> oldManagerList = roomWarderService.getByRoomId(orgCode, roomCode, PoliceWarderUserTypeConstant.MANAGER);
        List<PrisonRoomWarderDO> oldAssistList = roomWarderService.getByRoomId(orgCode, roomCode, PoliceWarderUserTypeConstant.ASSIST);
        List<String> newIds = assistList.stream().map(warder -> warder.getPoliceId()).collect(Collectors.toList());
        List<String> oldIds = oldAssistList.stream().map(warder -> warder.getPoliceId()).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(oldManagerList) &&
                oldManagerList.get(0).getPoliceId().equals(sponsorList.get(0).getPoliceId()) &&
                CollectionUtil.isEqualList(newIds, oldIds)) {
            return;
        }
        if(CollectionUtil.isNotEmpty(sponsorList)){
            // 组装新民警数据
            PrisonRoomWarderDO warderDO = new PrisonRoomWarderDO();
            warderDO.setUserType(PoliceWarderUserTypeConstant.MANAGER);
            warderDO.setStatus("1");
            warderDO.setOrgCode(orgCode);
            warderDO.setRoomId(roomCode);
            warderDO.setPoliceId(sponsorList.get(0).getPoliceId());
            warderDO.setPoliceName(sponsorList.get(0).getPoliceName());
            warderDO.setPoliceSfzh(sponsorList.get(0).getPoliceSfzh());
            // 组装历史民警数据
            if(CollectionUtil.isNotEmpty(oldManagerList)) {
                PrisonRoomWarderDO oldWarder = oldManagerList.get(0);
                warderDO.setHistoryPoliceId(oldWarder.getPoliceId());
                warderDO.setHistoryPoliceName(oldWarder.getPoliceName());
                warderDO.setHistoryPoliceSfzh(oldWarder.getPoliceSfzh());
                // 将旧数据修改为失效状态
                oldWarder.setStatus("0");
                roomWarderService.updateById(oldWarder);
            }
            // 保存新数据
            roomWarderService.save(warderDO);
        }

        // 处理协管民警数据
        if(CollectionUtil.isNotEmpty(assistList)){
            String oldPoliceId = "";
            String oldPoliceName = "";
            String oldPoliceSfzh = "";
            // 组装历史民警数据
            if(CollectionUtil.isNotEmpty(oldAssistList)) {
                for (PrisonRoomWarderDO oldWarder : oldAssistList) {
                    oldPoliceId = oldPoliceId.equals("") ? oldWarder.getPoliceId() : (oldPoliceId + "," + oldWarder.getPoliceId());
                    oldPoliceName = oldPoliceName.equals("") ? oldWarder.getPoliceName() : (oldPoliceName + "," + oldWarder.getPoliceName());
                    oldPoliceSfzh = oldPoliceSfzh.equals("") ? oldWarder.getPoliceSfzh() : (oldPoliceSfzh + "," + oldWarder.getPoliceSfzh());

                    // 将旧数据修改为失效状态
                    oldWarder.setStatus("0");
                    roomWarderService.updateById(oldWarder);
                }
            }

            // 处理新民警数据
            for (PrisonRoomWarderDO assistPolice : assistList) {
                PrisonRoomWarderDO warderDO = new PrisonRoomWarderDO();
                warderDO.setUserType(PoliceWarderUserTypeConstant.ASSIST);
                warderDO.setStatus("1");
                warderDO.setOrgCode(orgCode);
                warderDO.setRoomId(roomCode);
                warderDO.setPoliceId(assistPolice.getPoliceId());
                warderDO.setPoliceSfzh(assistPolice.getPoliceSfzh());
                warderDO.setPoliceName(assistPolice.getPoliceName());
                warderDO.setHistoryPoliceId(oldPoliceId);
                warderDO.setHistoryPoliceName(oldPoliceName);
                warderDO.setHistoryPoliceSfzh(oldPoliceSfzh);
                roomWarderService.save(warderDO);
            }
        }

    }

    @Override
    public void deleteAreaPrisonRoom(String id) {
        // 校验存在
        AreaPrisonRoomDO prisonRoomDO = areaPrisonRoomDao.selectById(id);
        if (prisonRoomDO == null) {
            throw new ServerException("实战平台-监管管理-区域监室数据不存在");
        }
        // 删除
        areaPrisonRoomDao.deleteById(id);
        // 删除主协管人员
        roomWarderService.deletePrisonRoomWarderByRoomId(prisonRoomDO.getRoomCode());
    }

    @Override
    public List<AreaPrisonRoomDO> getRoomByOrgCode(String orgCode) {
        return areaPrisonRoomDao.selectByOrgCode(orgCode);
    }

    private void validateAreaPrisonRoomExists(String id) {
        if (areaPrisonRoomDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-区域监室数据不存在");
        }
    }

    @Override
    public AreaPrisonRoomDO getAreaPrisonRoom(String roomCode) {
        return areaPrisonRoomDao.selectByRoomCode(roomCode);
    }

    @Override
    public AreaPrisonRoomDO getAreaPrisonRoom(String orgCode, String roomCode) {
        return areaPrisonRoomDao.selectByRoomCode(orgCode, roomCode);
    }

    @Override
    public String getAreaPrisonRoomName(String id) {
        if (StringUtils.isEmpty(id)) {
            return null;
        }
        String[] split = id.split(",");
        List<String> roomName = new ArrayList<>();
        for (String s : split) {
            AreaPrisonRoomDO areaPrisonRoom = areaPrisonRoomDao.selectById(s);
            if (areaPrisonRoom != null) {
                roomName.add(areaPrisonRoom.getRoomName());
            }
        }
        return String.join(",", roomName);
    }

    @Override
    public PageResult<AreaPrisonRoomDO> getAreaPrisonRoomPage(AreaPrisonRoomPageReqVO pageReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        // 无需根据超级管理员判断，后面由上角添加监所切换功能
        pageReqVO.setOrgCode(sessionUser.getOrgCode());

        List<PrisonerInDO> prisonerInList = null;
        if(CollectionUtil.isNotEmpty(pageReqVO.getJgrybmList())){
            prisonerInList = prisonerService.getPrisonerInOneList(pageReqVO.getJgrybmList());
        }

        //传参 （用于排除-是否同户籍、同居住地、历史同监室、同案件类型）
        //同户籍
        List<String> hjdList = null;
        if(CollUtil.isNotEmpty(prisonerInList) && pageReqVO.getExcludeHouseholdRegistration() != null && pageReqVO.getExcludeHouseholdRegistration() ){
            hjdList = prisonerInList.stream().map(PrisonerInDO::getHjd).filter(StrUtil::isNotBlank).collect(Collectors.toList());
        }

        //同居住地
        List<String> xzzList = null;
        if(CollUtil.isNotEmpty(prisonerInList) && pageReqVO.getExcludeHomePlace() != null && pageReqVO.getExcludeHomePlace() ){
            xzzList = prisonerInList.stream().map(PrisonerInDO::getXzz).filter(StrUtil::isNotBlank).collect(Collectors.toList());
        }

        //同案编码  同案监室
        List<String> tabmList = null;
        if(CollUtil.isNotEmpty(prisonerInList) && pageReqVO.getSameCase() != null && pageReqVO.getSameCase() ){
            tabmList = prisonerInList.stream().map(PrisonerInDO::getTabh).filter(StrUtil::isNotBlank).collect(Collectors.toList());
        }

        //同案件类型
        List<String> caseList = null;
        if(CollUtil.isNotEmpty(prisonerInList) && pageReqVO.getExcludeCaseType() != null && pageReqVO.getExcludeCaseType() ){
            caseList = prisonerInList.stream().map(PrisonerInDO::getAjlb).filter(StrUtil::isNotBlank).collect(Collectors.toList());
            //案件类型，多个逗号分割过
            List<String> caseListNew = new ArrayList<>();
            if(CollUtil.isNotEmpty(caseList)){
                caseList.forEach( e->{
                    String [] array = e.split(",");
                    caseListNew.addAll(Arrays.asList(array));
                });
                caseList = caseListNew;
            }
        }

        //拼接关联SQL
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT DISTINCT ry.jsh from vw_acp_pm_prisoner_in ry ");
        sql.append(" where ry.is_del = 0 ");

        boolean isSql = false;
        //户籍地
        if(CollUtil.isNotEmpty(hjdList)){
            sql.append(" and ry.xzz not in ( " );
            sql.append(hjdList.stream().map( e-> "'"  + e + "'").collect(Collectors.joining(",")));
            sql.append(")");
            isSql = true;
        }
        //居住地
        if(CollUtil.isNotEmpty(xzzList)){
            sql.append(" and ry.xzz not in ( " );
            sql.append(xzzList.stream().map( e-> "'"  + e + "'").collect(Collectors.joining(",")));
            sql.append(")");
            isSql = true;
        }
        //同案监室
        if(CollUtil.isNotEmpty(tabmList)){
            sql.append(" and ry.tabh not in ( " );
            sql.append(tabmList.stream().map( e-> "'"  + e + "'").collect(Collectors.joining(",")));
            sql.append(")");
            isSql = true;
        }
        //同案件类型
        if(CollUtil.isNotEmpty(caseList)){
            sql.append(" and ry.ajlb not in ( " );
            sql.append(caseList.stream().map( e-> "'"  + e + "'").collect(Collectors.joining(",")));
            sql.append(")");
            isSql = true;
        }
        //排除关押人户籍在指定地区的监室
        if(StringUtil.isNotEmpty(pageReqVO.getExcludeAreaRoom())){
            List<String> areaList = Arrays.asList(pageReqVO.getExcludeAreaRoom().split(","));
            sql.append(" and (" );
            sql.append(areaList.stream().map( e-> "ry.hjd like '"  + e + "%'").collect(Collectors.joining(" or ")));
            sql.append(")");
            isSql = true;
        }

        pageReqVO.setJshSql(sql.toString());


        //主协管
        if(pageReqVO.getMainAssistantManager() != null && pageReqVO.getMainAssistantManager()){
            PrisonRoomWarderListReqVO prisonRoomWarderListReqVO = new PrisonRoomWarderListReqVO();
            prisonRoomWarderListReqVO.setPoliceSfzh(sessionUser.getIdCard());
            List<PrisonRoomWarderDO>  prisonRoomWarderDOList = roomWarderService.getPrisonRoomWarderList(prisonRoomWarderListReqVO);
            pageReqVO.setRoomCodes(prisonRoomWarderDOList.stream().map(PrisonRoomWarderDO::getRoomId).collect(Collectors.toList()));
        }

        pageReqVO.setIsSql(isSql);

        //排除历史同监室的   getHistoryRoomList
        if(CollUtil.isNotEmpty(pageReqVO.getJgrybmList()) && pageReqVO.getExcludeHistoryRoom() != null && pageReqVO.getExcludeHistoryRoom()){
           List<String> historyRoomList = areaPrisonRoomDao.getHistoryRoomList(pageReqVO.getJgrybmList());
            pageReqVO.setNotRoomCodes(historyRoomList);
        }

        //判断性别
        if(CollUtil.isNotEmpty(prisonerInList)){
            List<String> sexList = prisonerInList.stream().map(PrisonerInDO::getXb).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
            pageReqVO.setExcludeSexList(sexList);

        }

        return areaPrisonRoomDao.selectPage(pageReqVO);
    }

    @Override
    public List<AreaPrisonRoomDO> getAreaPrisonRoomList(AreaPrisonRoomListReqVO listReqVO) {
        return areaPrisonRoomDao.selectList(listReqVO);
    }

    @Override
    public List<AreaPrisonRoomDO> getAreaPrisonRoomList(List<String> roomIds) {
        return areaPrisonRoomDao.selectListByRoomIds(roomIds);
    }

    /**
     * 创建监室信息。
     *
     * 1. 从请求对象中获取监室保存请求数据（AreaPrisonRoomSaveReqDTO）。
     * 2. 将监室保存请求数据转换为监室实体对象（AreaPrisonRoomDO）。
     * 3. 获取当前登录用户信息（SessionUser），包括用户名、用户ID等。
     * 4. 设置监室的通用属性，例如：是否删除（isDel）、创建时间（addTime）、创建人（addUser）、更新人（updateUser）等。
     * 5. 如果传入的区域列表（list）不为空，则设置监室所属的区域ID和区域名称。
     * 6. 返回新创建监室的主键ID（id）。
     *
     * @param id          区域ID
     * @param createReqVO 区域创建请求对象
     * @param list        区域列表，用于获取监室所属的区域信息
     * @return 返回新创建监室的主键ID
     */
    public String createAreaPrisonRoom(String id,AreaSaveReqDTO createReqVO, List<AreaDO> list) {
        AreaPrisonRoomSaveReqDTO areaPrisonRoomSaveDto = createReqVO.getAreaPrisonRoomSaveDto();
        AreaPrisonRoomDO areaPrisonRoom = BeanUtils.toBean(areaPrisonRoomSaveDto, AreaPrisonRoomDO.class);
        AreaDO jqArea = areaDao.selectById(id);
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String userName = sessionUser.getName();
        String userId = sessionUser.getId();

        areaPrisonRoom.setIsDel(false);
        areaPrisonRoom.setAddTime(new Date());
        areaPrisonRoom.setAddUser(userName);
        areaPrisonRoom.setUpdateUser(userId);
        areaPrisonRoom.setUpdateUserName(userName);
        areaPrisonRoom.setUpdateTime(new Date());
        areaPrisonRoom.setCityCode(jqArea.getCityCode());
        areaPrisonRoom.setCityName(jqArea.getCityName());
        areaPrisonRoom.setRegCode(jqArea.getRegCode());
        areaPrisonRoom.setRegName(jqArea.getRegName());
        areaPrisonRoom.setOrgCode(jqArea.getOrgCode());
        areaPrisonRoom.setOrgName(jqArea.getOrgName());
        areaPrisonRoom.setRoomCode(id);
        areaPrisonRoom.setRoomName(createReqVO.getAreaName());
        areaPrisonRoom.setId(id);
        //list是否为空
        if(list!=null && list.size()>0){
            AreaDO areaDO = list.get(0);
            areaPrisonRoom.setAreaId(areaDO.getId());
            areaPrisonRoom.setAreaName(areaDO.getAreaName());
        }
        areaPrisonRoomDao.insert(areaPrisonRoom);

        return areaPrisonRoom.getId();
    }

    @Override
    public void deleteAreaPrisonRoomByRoomCode(String id) {
        areaPrisonRoomDao.deleteAreaPrisonRoomByRoomCode(id);
    }


    @Override
    public List<AreaPrisonRoomDO> getAreaPrisonRoomListByRoomCode(AreaPrisonRoomListReqVO listReqVO) {
        if (StringUtils.isNotBlank(listReqVO.getRoomCode())) {
            return areaPrisonRoomDao.selectListByRoomCode(listReqVO.getRoomCode());
        }
        return Collections.emptyList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONObject validateData(List<Map<String, Object>> roomMapList, String orgCode) {
        OrgRespDTO org = bspApi.getOrgByCode(orgCode);
        // 添加行号
        for (int i = 0; i < roomMapList.size(); i++) {
            Map<String, Object> tempMap = roomMapList.get(i);
            for (Map.Entry<String, Object> entry : tempMap.entrySet()) {
                entry.setValue(entry.getValue() == null ? null : String.valueOf(entry.getValue()).trim().replaceAll(" ", ""));
            }
            tempMap.put("rowNo", i + 2);
        }

        // 校验是否删除说明行
        checkHasDelDesRow(roomMapList, 2);
        // 校验必填项规则
        checkLostKeyField(roomMapList);
        // 必填校验后，删除唯一列【监室名称+所属监区编码】重复的行
        checkUniqueFieldDuplicateRow(roomMapList);
        // 校验启用状态字段是否合规
        checkStatusIsLegal(roomMapList);
        // 校验监室类型字段是否合规
        checkRoomTypeIsLegal(roomMapList);
        // 校验性别类型字段是否合规
        checkRoomSexIsLegal(roomMapList);
        // 校验监区信息是否存在
        checkAreaExist(roomMapList, orgCode);
        // 待更新而非新增列表
        List<AreaPrisonRoomDO> roomList = new ArrayList<>();
        List<PrisonRoomWarderDO> warderList = new ArrayList<>();
        for (Map<String, Object> map : roomMapList) {
            // 获取监室信息
            AreaPrisonRoomDO roomDO = BeanUtil.mapToBean(map, AreaPrisonRoomDO.class, true);
            AreaPrisonRoomDO query = getOne(new LambdaQueryWrapper<AreaPrisonRoomDO>()
                    .eq(AreaPrisonRoomDO::getRoomName, roomDO.getRoomName())
                    .eq(AreaPrisonRoomDO::getAreaId, roomDO.getAreaId()));
            if(query != null) {
                roomDO.setId(query.getId());
            } else {
                roomDO.setId(StringUtil.getGuid());
            }
            roomDO.setOrgCode(orgCode);
            roomDO.setOrgName(org.getName());
            roomDO.setCityCode(org.getCityId());
            roomDO.setCityName(org.getCityName());
            roomDO.setRegCode(org.getRegionId());
            roomDO.setRegName(org.getRegionName());
            roomList.add(roomDO);

            // 获取监室主协管人员信息
            String sponsorIdCards = String.valueOf(map.get("sponsorIdCards"));
            String assistIdCards = String.valueOf(map.get("assistIdCards"));
            List<String> sponsorIdCardList = new ArrayList<>();
            List<String> assistIdCardList = new ArrayList<>();
            if(StringUtils.isNotBlank(sponsorIdCards)) {
                sponsorIdCardList = Arrays.asList(sponsorIdCards.split(","));
            }
            if(StringUtils.isNotBlank(assistIdCards)) {
                assistIdCardList.addAll(Arrays.asList(assistIdCards.split(",")));
            }
            String idCards = Stream.concat(sponsorIdCardList.stream(), assistIdCardList.stream())
                    .collect(Collectors.joining(","));
            // 获取bsp用户信息
            List<OrgUserRespDTO> userList = bspApi.getUserByIdCards(idCards);
            Map<String, OrgUserRespDTO> userMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(userList)) {
                userMap = userList.stream()
                        .collect(Collectors.toMap(
                                OrgUserRespDTO::getIdCard,
                                obj -> obj,
                                (existing, replacement) -> replacement));
            }

            for (String idCard : sponsorIdCardList) {
                PrisonRoomWarderDO prisonRoomWarderDO = new PrisonRoomWarderDO();
                prisonRoomWarderDO.setPoliceId(userMap.get(idCard).getId());
                prisonRoomWarderDO.setUserType("w");
                prisonRoomWarderDO.setRoomId(roomDO.getId());
                if (userMap.containsKey(idCard)) {
                    prisonRoomWarderDO.setPoliceName(userMap.get(idCard).getName());
                }
                warderList.add(prisonRoomWarderDO);
            }

            for (String idCard : assistIdCardList) {
                PrisonRoomWarderDO prisonRoomWarderDO = new PrisonRoomWarderDO();
                prisonRoomWarderDO.setPoliceId(userMap.get(idCard).getId());
                prisonRoomWarderDO.setUserType("a");
                prisonRoomWarderDO.setRoomId(roomDO.getId());
                if (userMap.containsKey(idCard)) {
                    prisonRoomWarderDO.setPoliceName(userMap.get(idCard).getName());
                }
                warderList.add(prisonRoomWarderDO);
            }
        }

        JSONObject result = new JSONObject();
        result.put("roomList", JSONObject.toJSONString(roomList));
        result.put("warderList", JSONObject.toJSONString(warderList));
        return result;
    }

    private void checkHasDelDesRow(List<Map<String, Object>> list, int delSize) {
        list.forEach(x -> {
            Collection<Object> values = x.values();
            if (new HashSet<>(values).size() <= delSize) {
                throw new RuntimeException("第" + x.get("rowNo") + "行数据有误,请检查");
            }
        });
    }

    private void checkLostKeyField(List<Map<String, Object>> list) {
        list.forEach(x -> {
            if(StringUtil.isEmpty((String)x.get("roomName"))) {
                throw new RuntimeException("第" + x.get("rowNo") + "行【监室名称】不能为空");
            }else if(String.valueOf(x.get("roomName")).length() > 50) {
                throw new RuntimeException("第" + x.get("rowNo") + "行【监室名称】长度超出限制");
            }
            if(StringUtil.isEmpty((String)x.get("areaId"))) {
                throw new RuntimeException("第" + x.get("rowNo") + "行【所属监区编码】不能为空");
            }else if(String.valueOf(x.get("areaId")).length() > 20) {
                throw new RuntimeException("第" + x.get("rowNo") + "行【所属监区编码】长度超出限制");
            }

            if(!StringUtil.isEmpty((String)x.get("imprisonmentAmount")) &&
                    String.valueOf(x.get("imprisonmentAmount")).length() > 3) {
                throw new RuntimeException("第" + x.get("rowNo") + "行【关押量】长度超出限制");
            }

            if(StringUtil.isEmpty((String)x.get("statusName"))) {
                throw new RuntimeException("第" + x.get("rowNo") + "行【启用状态】不能为空");
            }else if(String.valueOf(x.get("statusName")).length() > 2) {
                throw new RuntimeException("第" + x.get("rowNo") + "行【启用状态】长度超出限制");
            }

            if(StringUtil.isEmpty((String)x.get("roomTypeName"))) {
                throw new RuntimeException("第" + x.get("rowNo") + "行【监室类型】不能为空");
            }else if(String.valueOf(x.get("roomTypeName")).length() > 2) {
                throw new RuntimeException("第" + x.get("rowNo") + "行【监室类型】长度超出限制");
            }

            if(StringUtil.isEmpty((String)x.get("roomSexName"))) {
                throw new RuntimeException("第" + x.get("rowNo") + "行【性别类型】不能为空");
            }else if(String.valueOf(x.get("roomSexName")).length() > 1) {
                throw new RuntimeException("第" + x.get("rowNo") + "行【性别类型】长度超出限制");
            }

            if(!StringUtil.isEmpty((String)x.get("roomArea")) &&
                    String.valueOf(x.get("avgBedsArea")).length() > 10) {
                throw new RuntimeException("第" + x.get("rowNo") + "行【监室面积】长度超出限制");
            }
            if(!StringUtil.isEmpty((String)x.get("avgBedsArea")) &&
                    String.valueOf(x.get("avgBedsArea")).length() > 10) {
                throw new RuntimeException("第" + x.get("rowNo") + "行【人均铺位面积】长度超出限制");
            }

        });
    }

    private void checkUniqueFieldDuplicateRow(List<Map<String, Object>> list) {
        Set<String> set = new HashSet<>(list.size());
        for (Iterator<Map<String, Object>> iter = list.iterator(); iter.hasNext(); ) {
            Map<String, Object> tempMap = iter.next();
            String loginId = (String) tempMap.get("roomName");
            String areaId = (String) tempMap.get("areaId");
            if (!set.contains(loginId+areaId)) {
                set.add(loginId+areaId);
            } else {
                logger.warn("第" + tempMap.get("rowNo") + "行唯一键【监室名称+所属监区编码】存在重复行，已过滤");
                iter.remove();
            }
        }
    }

    private void checkStatusIsLegal(List<Map<String, Object>> list) {
        list.forEach(x -> {
            RoomStatusEnum statusName = RoomStatusEnum.getByName(String.valueOf(x.get("statusName")));
            if (statusName == null) {
                throw new RuntimeException("第" + x.get("rowNo") + "行启用状态不正确");
            }
            x.put("status", statusName.getCode());
        });
    }

    private void checkRoomTypeIsLegal(List<Map<String, Object>> list) {
        List<String> typeCodes = new ArrayList<>();
        List<String> typeNames = new ArrayList<>();
        Map<String, String> typeMap = RedisClient.hgetAll(CommonConstants.REDIS_DIC_KEY_PAM_ZXGJSLX);
        Set<String> keys = typeMap.keySet();
        for (String key : keys) {
            typeCodes.add(key);
            typeNames.add(typeMap.get(key));
        }
        list.forEach(x -> {
            String roomType = String.valueOf(x.get("roomTypeName"));
            if (!typeNames.contains(roomType)) {
                throw new RuntimeException("第" + x.get("rowNo") + "行监室类型不正确");
            }
            x.put("roomType", typeCodes.get(typeNames.indexOf(roomType)));
        });
    }

    private void checkRoomSexIsLegal(List<Map<String, Object>> list) {
        List<String> sexCodes = new ArrayList<>();
        List<String> sexNames = new ArrayList<>();
        Map<String, String> sexMap = RedisClient.hgetAll(CommonConstants.REDIS_DIC_KEY_PAM_XB);
        Set<String> keys = sexMap.keySet();
        for (String key : keys) {
            sexCodes.add(key);
            sexNames.add(sexMap.get(key));
        }
        list.forEach(x -> {
            String roomSex = String.valueOf(x.get("roomSexName"));
            if (!sexNames.contains(roomSex)) {
                throw new RuntimeException("第" + x.get("rowNo") + "行性别类型不正确");
            }
            x.put("roomSex", sexCodes.get(sexNames.indexOf(roomSex)));
        });
    }

    private void checkAreaExist(List<Map<String, Object>> list, String orgCode) {
        List<String> areaIds = list.stream().map(item -> String.valueOf(item.get("areaId"))).collect(Collectors.toList());
        List<AreaDO> areaList = areaDao.queryArea(areaIds, orgCode);
        Map<String, AreaDO> areaMap = areaList.stream()
                .collect(Collectors.toMap(
                        AreaDO::getId,
                        obj -> obj,
                        (existing, replacement) -> replacement));
        list.forEach(x -> {
            String areaId = String.valueOf(x.get("areaId"));
            AreaDO area = areaMap.get(areaId);
            if (area == null) {
                throw new RuntimeException("第" + x.get("rowNo") + "行监区信息不存在");
            } else {
                x.put("areaName", area.getAreaName());
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = REQUIRED)
    public void replaceBatchRoom(List<AreaPrisonRoomDO> roomList) {
        // 区分出哪些是插入，哪些是更新
        List<String> allIdList = roomList.stream().map(room -> room.getId()).collect(Collectors.toList());
        List<AreaPrisonRoomDO> updateList = this.baseMapper.selectBatchIds(allIdList);

        List<String> updateIdList = updateList.stream().map(org -> org.getId()).collect(Collectors.toList());
        List<AreaPrisonRoomDO> updateRoomList = roomList.stream().filter(
                room -> updateIdList.contains(room.getId())).collect(Collectors.toList());
        List<AreaPrisonRoomDO> addRoomList = roomList.stream().filter(
                room -> !updateIdList.contains(room.getId())).collect(Collectors.toList());
        // replace业务数据
//        baseMapper.deleteBatchByUnique(roomList);
//        baseMapper.insertBatch(roomList);
        // 查询数据变更后的数据集以记录数据变更轨迹
        if (!CollectionUtils.isEmpty(updateRoomList)) {
            this.baseMapper.updateBatch(updateRoomList);
        }
        if (!CollectionUtils.isEmpty(addRoomList)) {
            this.baseMapper.insertBatch(addRoomList);
        }
        roomWarderService.remove(new LambdaQueryWrapper<PrisonRoomWarderDO>()
                .in(PrisonRoomWarderDO::getRoomId, allIdList));
    }

    @Override
    public void replaceBatchWarder(List<PrisonRoomWarderDO> roomList) {
        roomWarderService.saveBatch(roomList);
    }

    @Override
    public List<JSONObject> findExportListBy(AreaPrisonRoomExportReqVO exportReqVO) {
        List<AreaPrisonRoomDO> roomDOList = baseMapper.findExportListBy(exportReqVO);
        List<JSONObject> list = JSONObject.parseArray(JSONObject.toJSONString(roomDOList), JSONObject.class);
        // 获取各监室的主协管人员
        String roomIds = roomDOList.stream().map(roomDO -> roomDO.getId()).collect(Collectors.joining(","));
        Map<String, List<PrisonRoomWarderRespVO>> warderMap = roomWarderService.getPrisonRoomWarderListByRoomId(roomIds);
        for (JSONObject room : list) {
            // 字段翻译
            Map<String, String> typeMap = RedisClient.hgetAll(CommonConstants.REDIS_DIC_KEY_PAM_ZXGJSLX);
            Map<String, String> sexMap = RedisClient.hgetAll(CommonConstants.REDIS_DIC_KEY_PAM_XB);
            RoomStatusEnum status = RoomStatusEnum.getByCode(room.getString("status"));
            room.put("statusName", status == null ? "" : status.getName());
            room.put("roomTypeName", typeMap.get(room.getString("roomType")));
            room.put("roomSexName", sexMap.get(room.getString("roomSex")));
            // 组装主协管信息
            List<PrisonRoomWarderRespVO> warderList = warderMap.get(room.getString("id"));
            if (CollectionUtil.isNotEmpty(warderList)) {
                String sponsorNames = warderList.stream().filter(warder -> "w".equals(warder.getUserType()))
                        .map(PrisonRoomWarderRespVO::getPoliceName).collect(Collectors.joining(","));
                String assistNames = warderList.stream().filter(warder -> "a".equals(warder.getUserType()))
                        .map(PrisonRoomWarderRespVO::getPoliceName).collect(Collectors.joining(","));
                room.put("sponsorNames", sponsorNames);
                room.put("assistNames", assistNames);
            }
        }
        return list;
    }

    @Override
    public JSONObject getRoomPrisonerInfo(String orgCode, String roomId) {
        List<PrisonerInDO> prisonerList = prisonerInDao.getByJsh(orgCode, roomId);
        JSONObject result = new JSONObject();
        result.put("count", prisonerList.size());
        result.put("riskNum1", prisonerList.stream().filter(prisoner -> "1".equals(prisoner.getFxdj())).count());
        result.put("riskNum2", prisonerList.stream().filter(prisoner -> "2".equals(prisoner.getFxdj())).count());
        result.put("riskNum3", prisonerList.stream().filter(prisoner -> "3".equals(prisoner.getFxdj())).count());
        result.put("sickCount", 0);
        return result;
    }

    @Override
    public PageResult<AreaPrisonRoomDO> getRoomWithViolationPage(AreaPrisonRoomPageWithViolationReqVO pageReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        // 无需根据超级管理员判断，后面由上角添加监所切换功能
        if(StringUtils.isEmpty(pageReqVO.getOrgCode())){
            pageReqVO.setOrgCode(sessionUser.getOrgCode());
        }

        //主协管
        if(pageReqVO.getMainAssistantManager() != null && pageReqVO.getMainAssistantManager()){
            PrisonRoomWarderListReqVO prisonRoomWarderListReqVO = new PrisonRoomWarderListReqVO();
            prisonRoomWarderListReqVO.setPoliceSfzh(sessionUser.getIdCard());
            List<PrisonRoomWarderDO>  prisonRoomWarderDOList = roomWarderService.getPrisonRoomWarderList(prisonRoomWarderListReqVO);
            pageReqVO.setRoomCodes(prisonRoomWarderDOList.stream().map(PrisonRoomWarderDO::getRoomId).collect(Collectors.toList()));
        }
        Page<AreaPrisonRoomDO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Page<AreaPrisonRoomDO> result = areaPrisonRoomDao.getRoomWithViolationPage(page, pageReqVO);
        return new PageResult<>(result.getRecords(), result.getTotal());
    }

    private Map<String, String> getRiskLevel() {

        return null;
    }

}
