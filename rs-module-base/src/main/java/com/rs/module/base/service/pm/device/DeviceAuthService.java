package com.rs.module.base.service.pm.device;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.base.controller.admin.pm.vo.DeviceAuthListReqVO;
import com.rs.module.base.controller.admin.pm.vo.DeviceAuthPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.DeviceAuthSaveReqVO;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.base.entity.pm.DeviceAuthDO;
import com.rs.module.base.entity.pm.device.BaseDeviceDO;


import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-监管管理-设备授权 Service 接口
 *
 * <AUTHOR>
 */
public interface DeviceAuthService extends IBaseService<DeviceAuthDO>{

    /**
     * 创建实战平台-监管管理-设备授权
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createDeviceAuth(@Valid DeviceAuthSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-设备授权
     *
     * @param updateReqVO 更新信息
     */
    void updateDeviceAuth(@Valid DeviceAuthSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-设备授权
     *
     * @param id 编号
     */
    void deleteDeviceAuth(String id);

    /**
     * 获得实战平台-监管管理-设备授权
     *
     * @param id 编号
     * @return 实战平台-监管管理-设备授权
     */
    DeviceAuthDO getDeviceAuth(String id);

    /**
     * 根据IP查询设备信息
     * @param ip
     * @param deviceType
     * @return
     */
    List<BaseDeviceDO> getDeviceAuthByIP(String ip, String deviceType);

    /**
     * 根据IP查询设备绑定的房间信息
     * @param ip
     * @param deviceType
     * @return
     */
    AreaDO getOneAreaByIP(String ip, String deviceType);

    /**
    * 获得实战平台-监管管理-设备授权分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-监管管理-设备授权分页
    */
    PageResult<DeviceAuthDO> getDeviceAuthPage(DeviceAuthPageReqVO pageReqVO);

    /**
    * 获得实战平台-监管管理-设备授权列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-监管管理-设备授权列表
    */
    List<DeviceAuthDO> getDeviceAuthList(DeviceAuthListReqVO listReqVO);


}
