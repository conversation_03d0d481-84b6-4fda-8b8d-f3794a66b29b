package com.rs.module.base.entity.pm;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.type.BooleanToIntegerTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 人员列表 DO
 *
 * <AUTHOR>
 */
@TableName("vw_acp_pm_prisoner_List")
@Data
public class PrisonerListDO  {

    /**
     * id
     */
    private String id;

    @TableField(value = "IS_DEL", typeHandler = BooleanToIntegerTypeHandler.class)
    @TableLogic
    private Boolean isDel;
    /**
     * 添加时间
     */
    private Date addTime;
    /**
     * 机构编码
     */
    private String orgCode;
    /**
     * 机构名称
     */
    private String orgName;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 人员编号
     */
    private String rybh;
    /**
     * 姓名
     */
    private String xm;
    /**
     * 姓名拼音
     */
    private String xmpy;
    /**
     * 别名
     */
    private String bm;
    /**
     * 证件类型
     */
    private String zjlx;
    /**
     * 证件号码
     */
    private String zjhm;
    /**
     * 性别
     */
    private String xb;
    /**
     * 民族
     */
    private String mz;
    /**
     * 出生日期
     */
    private Date csrq;
    /**
     * 国籍
     */
    private String gj;
    /**
     * 人员状态
     */
    private String ryzt;
    /**
     * 档案编号
     */
    private String dabh;
    /**
     * 入所时间
     */
    private Date rssj;
    /**
     * 案件编号
     */
    private String ajbh;
    /**
     * 案件类别
     */
    private String ajlb;
    /**
     * 最终处置结果
     */
    private String zzczjg;
    /**
     * 监室号
     */
    private String jsh;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 监区id
     */
    private String areaId;
    /**
     * 床位号
     */
    private String cwh;
    /**
     * 正面照片
     */
    private String frontPhoto;
    /**
     * 被监管人员类型（01：在押人员，02：被拘人员，03：戒毒人员，99：其他）
     */
    private String bjgrylx;
    /**
     * 籍贯
     */
    private String jg;
    /**
     * 关押期限
     */
    private Date gyqx;
    /**
     * 涉嫌罪名
     */
    private String sxzm;
    /**
     * 诉讼环节
     */
    private String sshj;

    //本月奖励数
    @TableField(exist = false)
    private Integer byjls;

    //本月违规数
    @TableField(exist = false)
    private Integer bywgs;

    //本月惩罚数
    @TableField(exist = false)
    private Integer bycfs;


}
