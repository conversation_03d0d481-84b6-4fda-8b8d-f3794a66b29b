package com.rs.module.base.controller.admin.zh.vo.staffduty;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
@Data
@ApiModel(description = "排班模板")
public class StaffDutyTemplateRespVO extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "模板名称")
    private String name;

    @ApiModelProperty(value = " 0-停用 1-启用")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SFQY")
    private String status;

    @ApiModelProperty(value = "值班岗位详情")
    private List<StaffDutyPostRespVO> dutyPostDTOS;

}
