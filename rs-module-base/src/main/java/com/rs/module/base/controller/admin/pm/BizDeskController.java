package com.rs.module.base.controller.admin.pm;

import cn.hutool.core.util.ObjectUtil;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.*;
import com.rs.module.base.entity.pm.BizDeskCardDO;
import com.rs.module.base.entity.pm.BizDeskDO;
import com.rs.module.base.service.pm.BizDeskCardService;
import com.rs.module.base.service.pm.BizDeskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "总台配置")
@RestController
@RequestMapping("/acp/pm/bizDesk")
@Validated
public class BizDeskController {

    @Resource
    private BizDeskService bizDeskService;
    @Resource
    private BizDeskCardService bizDeskCardService;

    @PostMapping("/save")
    @ApiOperation(value = "创建总台")
    public CommonResult createBizDesk(@Valid @RequestBody BizDeskSaveReqVO createReqVO) {
        BizDeskDO bizDesk = null;
        if (StringUtil.isNullBlank(createReqVO.getId())) {
            BizDeskDO dbDesk = bizDeskService.getByMark(createReqVO.getMark());
            if (ObjectUtil.isNotEmpty(dbDesk)) {
                return error("该总台标识已存在");
            }
            bizDesk = bizDeskService.createBizDesk(createReqVO);
        } else {
            bizDesk = bizDeskService.updateBizDesk(createReqVO);
        }
        return success(bizDesk);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除总台")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult deleteBizDesk(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           bizDeskService.deleteBizDesk(id);
        }
        return success("", "删除成功");
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得总台")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<BizDeskRespVO> getBizDesk(@RequestParam("id") String id) {
        BizDeskDO bizDesk = bizDeskService.getBizDesk(id);
        return success(BeanUtils.toBean(bizDesk, BizDeskRespVO.class));
    }



    @PostMapping("/card/save")
    @ApiOperation(value = "创建选项卡配置")
    public CommonResult createBizDeskCard(@Valid @RequestBody BizDeskCardSaveReqVO createReqVO) {
        BizDeskCardDO bizDeskCard = null;
        if (StringUtil.isNullBlank(createReqVO.getId())) {
            bizDeskCard = bizDeskCardService.createBizDeskCard(createReqVO);
        } else {
            bizDeskCard = bizDeskCardService.updateBizDeskCard(createReqVO);
        }
        return success(bizDeskCard);
    }

    @GetMapping("/card/delete")
    @ApiOperation(value = "删除选项卡配置")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult deleteBizDeskCard(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            bizDeskCardService.deleteBizDeskCard(id);
        }
        return success("", "删除成功");
    }

    @GetMapping("/card/get")
    @ApiOperation(value = "获得选项卡配置")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<BizDeskCardRespVO> getBizDeskCard(@RequestParam("id") String id) {
        BizDeskCardDO bizDeskCard = bizDeskCardService.getBizDeskCard(id);
        return success(BeanUtils.toBean(bizDeskCard, BizDeskCardRespVO.class));
    }



    @ApiOperation(value = "查询总台配置树数据", response = BizDeskTreeNodeVO.class, responseContainer = "List")
    @RequestMapping(value = "/treedata", method = RequestMethod.GET)
    public CommonResult<List<BizDeskTreeNodeVO>> findDeskTreeData() {
        return success(bizDeskService.findDeskTreeData());
    }

    /**
     * 通过标识查询总台配置信息
     * @param mark 总台标识
     * @return
     */
    @RequestMapping(value = "/{mark}", method = {RequestMethod.GET, RequestMethod.POST})
    @ApiOperation(value = "通过标识查询总台配置信息", response = BizDeskCardQxVO.class)
    public CommonResult getByMark(@PathVariable("mark") String mark) {
        return bizDeskService.buildDeskComponent(mark);
    }

}
