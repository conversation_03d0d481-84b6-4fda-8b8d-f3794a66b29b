package com.rs.module.base.dao.pm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.base.entity.pm.BizQuickAccessDO;
import com.rs.module.base.entity.pm.BizQuickAccessTypeDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 实战平台-监管管理-业务快捷访问入口配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BizQuickAccessDao extends IBaseDao<BizQuickAccessDO> {

    default BizQuickAccessDO getByMark(String mark) {
        return selectOne(new LambdaQueryWrapper<BizQuickAccessDO>()
                .eq(BizQuickAccessDO::getMark, mark));
    }

    default List<BizQuickAccessDO> getByMarks(List<String> marks) {
        return selectList(new LambdaQueryWrapper<BizQuickAccessDO>()
                .in(BizQuickAccessDO::getMark, marks));
    }

    default List<BizQuickAccessDO> getByTypeMark(String typeMark) {
        return selectList(new LambdaQueryWrapper<BizQuickAccessDO>()
                .eq(BizQuickAccessDO::getTypeMark, typeMark));
    }

    default List<BizQuickAccessDO> getByTypeMarks(List<String> typeMarks) {
        return selectList(new LambdaQueryWrapper<BizQuickAccessDO>()
                .in(BizQuickAccessDO::getTypeMark, typeMarks));
    }

}
