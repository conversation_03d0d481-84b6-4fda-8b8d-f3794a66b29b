package com.rs.module.base.service.pm;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.cons.CommonConstants;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.CusAppSaveReqVO;
import com.rs.module.base.controller.admin.pm.vo.CusAppUacRespVO;
import com.rs.module.base.controller.admin.pm.vo.CusAppUacSaveReqVO;
import com.rs.module.base.dao.pm.CusAppDao;
import com.rs.module.base.entity.pm.CusAppCatDO;
import com.rs.module.base.entity.pm.CusAppDO;
import com.rs.module.base.entity.pm.CusAppUacDO;
import com.rs.module.base.enums.PermissionEnum;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 自定义应用管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CusAppServiceImpl extends BaseServiceImpl<CusAppDao, CusAppDO> implements CusAppService {

    @Resource
    private CusAppDao cusAppDao;
    @Resource
    private CusAppUacService cusAppUacService;
    @Resource
    @Lazy
    private CusAppCatService cusAppCatService;

    @Override
    public String createOrUpdateCusApp(CusAppSaveReqVO createReqVO) {
        String flId = createReqVO.getFlId();
        CusAppCatDO fl = cusAppCatService.getById(flId);
        if (fl == null) {
            throw new ServerException("分类不存在");
        }
        CusAppDO cusApp = BeanUtils.toBean(createReqVO, CusAppDO.class);
        cusApp.setSystemId(fl.getSystemId());
        CusAppDO appDO = getBySystemIdAndYymcAndFlId(fl.getSystemId(), cusApp.getYymc(), flId);
        if (appDO != null && !appDO.getId().equals(createReqVO.getId())) {
            throw new ServerException("应用名称已存在");
        }
        if (StrUtil.isBlank(cusApp.getId())) {
            cusAppDao.insert(cusApp);
        } else {
            cusAppDao.updateById(cusApp);
        }
        return cusApp.getId();
    }

    private CusAppDO getBySystemIdAndYymcAndFlId(String systemId, String yymc, String flId) {
        LambdaQueryWrapper<CusAppDO> queryWrapper = Wrappers.lambdaQuery(CusAppDO.class);
        queryWrapper.eq(CusAppDO::getSystemId, systemId).eq(CusAppDO::getYymc, yymc).eq(CusAppDO::getFlId, flId);
        return cusAppDao.selectOne(queryWrapper);
    }

    @Override
    public void deleteCusApp(String id) {
        // 校验存在
        validateCusAppExists(id);
        // 删除
        cusAppDao.deleteById(id);
    }

    private void validateCusAppExists(String id) {
        if (cusAppDao.selectById(id) == null) {
            throw new ServerException("自定义应用管理数据不存在");
        }
    }

    @Override
    public CusAppDO getCusApp(String id) {
        return cusAppDao.selectById(id);
    }

    @Override
    public boolean checkCusApp(String flId) {
        LambdaQueryWrapper<CusAppDO> queryWrapper = Wrappers.lambdaQuery(CusAppDO.class);
        Integer integer = cusAppDao.selectCount(queryWrapper.eq(CusAppDO::getFlId, flId));
        return integer > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByIds(List<String> ids) {
        cusAppDao.deleteBatchIds(ids);
        // 级联删除权限数据
        LambdaQueryWrapper<CusAppUacDO> wrapper = Wrappers.lambdaQuery(CusAppUacDO.class);
        wrapper.in(CusAppUacDO::getYyid, ids);
        cusAppUacService.remove(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePermissions(CusAppUacSaveReqVO saveReqVO) {
        String type = saveReqVO.getQxlx();
        // 先删除旧的数据
        String yyid = saveReqVO.getYyid();
        LambdaQueryWrapper<CusAppUacDO> deleteWrapper = Wrappers.lambdaQuery();
        deleteWrapper.eq(CusAppUacDO::getYyid, yyid).eq(CusAppUacDO::getQxlx, type);
        cusAppUacService.remove(deleteWrapper);
        // 插入新数据
        String ids = saveReqVO.getQxids();
        if (StringUtil.isNotEmpty(ids)) {
            String[] split = ids.split(",");
            List<CusAppUacDO> idList = Arrays.stream(split).filter(areaId -> StrUtil.isNotBlank(areaId)).map(areaId -> {
                CusAppUacDO uac = new CusAppUacDO();
                uac.setYyid(yyid);
                uac.setQxlx(type);
                uac.setQxid(areaId);
                return uac;
            }).collect(Collectors.toList());
            cusAppUacService.saveBatch(idList);
        }
    }

    @Override
    public CusAppUacRespVO getPermissions(String yyid) {
        CusAppUacRespVO result = new CusAppUacRespVO();
        result.setYyid(yyid);
        List<CusAppUacDO> list = cusAppUacService.list(Wrappers.lambdaQuery(CusAppUacDO.class).eq(CusAppUacDO::getYyid, yyid));
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        list.forEach(x -> {
            if (x.getQxlx().equals(PermissionEnum.ROLE.getValue())) {
                result.getRoleIdList().add(x.getQxid());
            } else if (x.getQxlx().equals(PermissionEnum.AREA.getValue())) {
                result.getAreaIdList().add(x.getQxid());
            } else {
                result.getOrgIdList().add(x.getQxid());
            }
        });
        return result;
    }

    @Override
    public List<Map<String, Object>> getAllApply(String roleIds, String orgCode, String regCode, String yylx,
    		String systemId, String mark) {
        String[] roleIdArray = StringUtil.splitString(roleIds, CommonConstants.DEFAULT_SPLIT_STR);
        List<String> roleList = Arrays.asList(roleIdArray);
        List<Map<String, Object>> allApply = cusAppDao.getAllApply(roleList, orgCode, regCode, yylx, systemId, mark);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(allApply)) {
            return Collections.emptyList();
        } else {
            return allApply;
        }
    }
}
