package com.rs.module.base.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.base.controller.admin.pm.vo.BizDeskSaveReqVO;
import com.rs.module.base.controller.admin.pm.vo.BizDeskTreeNodeVO;
import com.rs.module.base.entity.pm.BizDeskCardDO;
import com.rs.module.base.entity.pm.BizDeskDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-监管管理-业务总台配置 Service 接口
 *
 * <AUTHOR>
 */
public interface BizDeskService extends IBaseService<BizDeskDO>{

    /**
     * 创建实战平台-监管管理-业务总台配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    BizDeskDO createBizDesk(@Valid BizDeskSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-业务总台配置
     *
     * @param updateReqVO 更新信息
     */
    BizDeskDO updateBizDesk(@Valid BizDeskSaveReqVO updateReqVO);

    /**
     * 根据标识获取总台配置
     * @param mark
     * @return
     */
    BizDeskDO getByMark(String mark);

    /**
     * 删除实战平台-监管管理-业务总台配置
     *
     * @param id 编号
     */
    void deleteBizDesk(String id);

    /**
     * 获得实战平台-监管管理-业务总台配置
     *
     * @param id 编号
     * @return 实战平台-监管管理-业务总台配置
     */
    BizDeskDO getBizDesk(String id);

    /**
     * 查询配置树数据
     *
     * @return 总台配置树节点列表
     */
    List<BizDeskTreeNodeVO> findDeskTreeData();

    /**
     * 根据总台标识查询总台配置信息
     * @param mark
     * @return
     */
    CommonResult buildDeskComponent(String mark);

}
