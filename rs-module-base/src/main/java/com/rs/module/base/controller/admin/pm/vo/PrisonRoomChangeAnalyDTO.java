package com.rs.module.base.controller.admin.pm.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Create by JieXing on ${Date} 20:38
 * <AUTHOR>
 */

@Data
public class PrisonRoomChangeAnalyDTO implements Serializable {
    @ApiModelProperty(value = "原监室id", required = false)
    private String roomId;
    @ApiModelProperty(value = "在押人员id", required = false)
    private String prisonerId;
    @ApiModelProperty(value = "单位代码", required = false)
    private String prisonId;

    ////排除监室 0-未选 1-已选

    @ApiModelProperty(value = "同案监室", required = false)
    private String sameCase;
    @ApiModelProperty(value = "不同性别监室", required = false)
    private String roomSex;
    @ApiModelProperty(value = "过渡监室", required = false)
    private String transition;
    @ApiModelProperty(value = "含同警情事件的监室", required = false)
    private String sameEvent;
    @ApiModelProperty(value = "空监室", required = false)
    private String noPrisonerRoom;
    @ApiModelProperty(value = "单人监室", required = false)
    private String singleRoom;
    
    //优先监室 0-未选  1-已选

    @ApiModelProperty(value = "押量较少", required = false)
    private String prisonNum;
    @ApiModelProperty(value = "同户籍较少", required = false)
    private String samePlace;
    @ApiModelProperty(value = "重大风险人数较少", required = false)
    private String highRick;
    @ApiModelProperty(value = "重病号较少", required = false)
    private String highSick;
    @ApiModelProperty(value = "紧急风险人员较少", required = false)
    private String urgent;
    @ApiModelProperty(value = "死刑犯较少", required = false)
    private String deadPerson;

    @ApiModelProperty("监区id，多个用逗号隔开")
    private String areaIds;

}
