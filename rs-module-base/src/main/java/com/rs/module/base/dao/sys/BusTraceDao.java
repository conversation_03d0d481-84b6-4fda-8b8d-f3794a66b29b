package com.rs.module.base.dao.sys;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.base.controller.admin.sys.vo.BusTraceListReqVO;
import com.rs.module.base.controller.admin.sys.vo.BusTracePageReqVO;
import com.rs.module.base.entity.sys.BusTraceDO;
import com.rs.module.base.enums.BusTraceGroupEnum;

/**
* 实战平台-业务轨迹 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BusTraceDao extends IBaseDao<BusTraceDO> {


    default PageResult<BusTraceDO> selectPage(BusTracePageReqVO reqVO) {
        Page<BusTraceDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<BusTraceDO> wrapper = new LambdaQueryWrapperX<BusTraceDO>()
            .eqIfPresent(BusTraceDO::getIp, reqVO.getIp())
            .eqIfPresent(BusTraceDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(BusTraceDO::getOperateUser, reqVO.getOperateUser())
            .eqIfPresent(BusTraceDO::getBusType, reqVO.getBusType())
            .likeIfPresent(BusTraceDO::getBusName, reqVO.getBusName())
            .eqIfPresent(BusTraceDO::getContent, reqVO.getContent());
        
        //分组处理
        if(StringUtil.isNotEmpty(reqVO.getGroup())) {
        	List<String> busTypes = BusTraceGroupEnum.getGroupBusTypes(reqVO.getGroup());
        	if(busTypes != null) {
        		wrapper.in(BusTraceDO::getBusType, busTypes);
        	}
        }
        
        //时间处理
        if(reqVO.getStartTime() != null) {
        	wrapper.ge(BusTraceDO::getAddTime, reqVO.getStartTime());
        }
        if(reqVO.getEndTime() != null) {
        	wrapper.lt(BusTraceDO::getAddTime, reqVO.getEndTime());
        }
        
        //排序处理
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }
        else {
           wrapper.orderByDesc(BusTraceDO::getAddTime);
        }
        
        Page<BusTraceDO> busTracePage = selectPage(page, wrapper);
        return new PageResult<>(busTracePage.getRecords(), busTracePage.getTotal());
    }
    
    default List<BusTraceDO> selectList(BusTraceListReqVO reqVO) {
    	LambdaQueryWrapperX<BusTraceDO> wrapper = new LambdaQueryWrapperX<BusTraceDO>()
                .eqIfPresent(BusTraceDO::getIp, reqVO.getIp())
                .eqIfPresent(BusTraceDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(BusTraceDO::getOperateUser, reqVO.getOperateUser())
                .eqIfPresent(BusTraceDO::getBusType, reqVO.getBusType())
                .likeIfPresent(BusTraceDO::getBusName, reqVO.getBusName())
                .eqIfPresent(BusTraceDO::getContent, reqVO.getContent())
            .orderByDesc(BusTraceDO::getAddTime);
    	
    	//分组处理
        if(StringUtil.isNotEmpty(reqVO.getGroup())) {
        	List<String> busTypes = BusTraceGroupEnum.getGroupBusTypes(reqVO.getGroup());
        	if(busTypes != null) {
        		wrapper.in(BusTraceDO::getBusType, busTypes);
        	}
        }
        
        //时间处理
        if(reqVO.getStartTime() != null) {
        	wrapper.ge(BusTraceDO::getAddTime, reqVO.getStartTime());
        }
        if(reqVO.getEndTime() != null) {
        	wrapper.lt(BusTraceDO::getAddTime, reqVO.getEndTime());
        }
        
        return selectList(wrapper);
    }
    
    /**
     * 获取监管人员轨迹相关的场所
     * @param zjhm String 证件号码
     * @param jgrybm String 监管人员编码
     * @param busTypes List<String> 轨迹业务类型
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getJgryTraceOrgList(@Param("zjhm") String zjhm,
    		@Param("jgrybm") String jgrybm,	@Param("busTypes") List<String> busTypes);
}
