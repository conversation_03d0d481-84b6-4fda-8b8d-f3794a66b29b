package com.rs.module.base.entity.pm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

/**
 * 实战平台-监管管理-设备授权 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_device_auth")
@KeySequence("acp_pm_device_auth_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceAuthDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 授权给ip
     */
    private String authIp;
    /**
     * 授权给用户
     */
    private String authUser;
    /**
     * 授权的给设备
     */
    private String authDeviceId;
    /**
     * 授权给角色
     */
    private String authRole;

}
