package com.rs.module.base.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "简化版 审批 Request VO")
@Data
public class SimpleApproveReqVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;


    @ApiModelProperty("审批结果 0 不同意,1 同意")
    private String approvalResult;

    @ApiModelProperty("审核意见")
    private String approvalComments;


}
