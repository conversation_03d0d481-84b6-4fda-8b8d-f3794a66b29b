package com.rs.module.base.util;

import java.util.Calendar;
import java.util.Date;

/**
 * 计算年龄的工具类
 *
 * <AUTHOR>
 * @Date 2025/5/9 18:47
 */
public class AgeCalculatorUtil {

    public static Integer calculateAge(Date birthDate) {
        if (birthDate == null) {
            return null;
        }
        Calendar birth = Calendar.getInstance();
        birth.setTime(birthDate);
        Calendar today = Calendar.getInstance();
        Integer age = today.get(Calendar.YEAR) - birth.get(Calendar.YEAR);
        if (today.get(Calendar.MONTH) < birth.get(Calendar.MONTH) ||
                (today.get(Calendar.MONTH) == birth.get(Calendar.MONTH) &&
                        today.get(Calendar.DAY_OF_MONTH) < birth.get(Calendar.DAY_OF_MONTH))) {
            age--;
        }
        return age;
    }
}
