package com.rs.module.base.controller.admin.pm;

import com.bsp.common.util.StringUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.AssistiveToolsRespVO;
import com.rs.module.base.controller.admin.pm.vo.AssistiveToolsSaveReqVO;
import com.rs.module.base.entity.pm.AssistiveToolsDO;
import com.rs.module.base.service.pm.AssistiveToolsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "辅助工具管理")
@RestController
@RequestMapping("/acp/pm/tools")
@Validated
public class AssistiveToolsController {

    @Resource
    private AssistiveToolsService assistiveToolsService;

    @PostMapping("/create")
    @ApiOperation(value = "创建辅助工具")
    public CommonResult<String> createAssistiveTools(@Valid @RequestBody AssistiveToolsSaveReqVO createReqVO) {
        if (StringUtil.isNullBlank(createReqVO.getFileType())) {
            String filePath = createReqVO.getFilePath();
            createReqVO.setFileType(filePath.substring(filePath.lastIndexOf(".")+1));
        }
        if (StringUtil.isNullBlank(createReqVO.getId())) {
            assistiveToolsService.createAssistiveTools(createReqVO);
        } else {
            assistiveToolsService.updateAssistiveTools(createReqVO);
        }
        return success("", "保存成功");
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除辅助工具")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteAssistiveTools(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           assistiveToolsService.deleteAssistiveTools(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得辅助工具")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<AssistiveToolsRespVO> getAssistiveTools(@RequestParam("id") String id) {
        AssistiveToolsDO assistiveTools = assistiveToolsService.getAssistiveTools(id);
        return success(BeanUtils.toBean(assistiveTools, AssistiveToolsRespVO.class));
    }

    @ApiOperation(value = "获取所有工具")
    @RequestMapping(value = "/findListAll",method = {RequestMethod.POST})
    public CommonResult findListAll(){
        List<AssistiveToolsDO> list = assistiveToolsService.list();
        return success(BeanUtils.toBean(list, AssistiveToolsRespVO.class));
    }

}
