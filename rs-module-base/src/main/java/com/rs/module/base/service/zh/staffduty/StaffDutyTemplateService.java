package com.rs.module.base.service.zh.staffduty;


import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.base.controller.admin.zh.vo.staffduty.*;
import com.rs.module.base.entity.zh.StaffDutyTemplateDO;


/**
 *
 */
public interface StaffDutyTemplateService extends IBaseService<StaffDutyTemplateDO> {

 /***
  * 模板详情
  * @param id
  * @return
  */
 StaffDutyTemplateRespVO info(String id);

 /***
 * 新增模板
 * @param dto
 * @return
 */
 Integer add(StaffDutyTemplateSaveReqVO dto,Integer templateType);

 /***
  * 模板启用/停用
  * @param id
  * @param status
  * @return
  */
 Integer tempOpen(String id,Integer status,Integer templateType);

 /***
  * 模板启用校验
  * @param id
  * @return
  */
 Boolean tempOpenCheck(String id);



 /***
 * 删除
 * @param ids
 * @return
 */
 Integer delete(String[] ids);

 /**
  * 更新
  * @param staffDutyTemplateSaveReqVO
  * @return
  */
 Integer updateInfo(StaffDutyTemplateSaveReqVO staffDutyTemplateSaveReqVO,Integer templateType);
}
