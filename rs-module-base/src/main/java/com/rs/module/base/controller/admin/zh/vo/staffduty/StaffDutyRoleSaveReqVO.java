package com.rs.module.base.controller.admin.zh.vo.staffduty;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel(description = "值班模板关联角色表")
public class StaffDutyRoleSaveReqVO extends BaseVO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模板duty_post id",hidden = true)
    @NotEmpty(message = "模板编号不能为空")
    private String postId;
    
    @ApiModelProperty(value = "值班角色")
    private String dutyRoleName;

    @ApiModelProperty(value = "岗位名称")
    private String postName;

    @ApiModelProperty(value = "岗位编号")
    private String dutyPostId;
    @ApiModelProperty(value = "缺勤通知人sfzh")
    private String absentNotifierSfzh;
    @ApiModelProperty(value = "缺勤通知人")
    private String absentNotifierName;
    @ApiModelProperty(value = "值班时间模板列表",required = true)
    @NotEmpty(message = "值班时间模板列表 不能为空")
    private List<StaffDutyRoleTimeSaveReqVO> roleTimeDTOS;



}

