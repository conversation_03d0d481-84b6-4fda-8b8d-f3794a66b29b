package com.rs.module.base.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.base.controller.admin.pm.vo.CusAppSaveReqVO;
import com.rs.module.base.controller.admin.pm.vo.CusAppUacRespVO;
import com.rs.module.base.controller.admin.pm.vo.CusAppUacSaveReqVO;
import com.rs.module.base.entity.pm.CusAppDO;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 自定义应用管理 Service 接口
 *
 * <AUTHOR>
 */
public interface CusAppService extends IBaseService<CusAppDO>{

    /**
     * 创建自定义应用管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createOrUpdateCusApp(CusAppSaveReqVO createReqVO);
    /**
     * 删除自定义应用管理
     *
     * @param id 编号
     */
    void deleteCusApp(String id);
    /**
     * 获得自定义应用管理
     *
     * @param id 编号
     * @return 自定义应用管理
     */
    CusAppDO getCusApp(String id);
    /**
     * 根据分类ID查询自定义应用管理
     * @param flId
     * @return
     */
    boolean checkCusApp(String flId);
    /**
     * 删除多个自定义应用管理
     * @param ids
     */
    void delByIds(List<String> ids);
    /**
     * 保存自定义应用管理的权限
     * @param saveReqVO
     */
    void savePermissions(@Valid CusAppUacSaveReqVO saveReqVO);

    /**
     * 获得自定义应用管理的权限
     * @param yyid
     * @return
     */
    CusAppUacRespVO getPermissions(String yyid);

    /**
     * 获取全部应用
     * @param roleIds String 用户角色
     * @param orgCode String 机构代码
     * @param regCode String 区域代码
     * @param yylx String 应用类型
     * @param systemId String 系统Id
     * @param mark String 分类标识
     * @return List<Map<String, Object>>
     */
    List<Map<String, Object>> getAllApply(String roleIds, String orgCode, String regCode,
    		String yylx, String systemId, String mark);
}
