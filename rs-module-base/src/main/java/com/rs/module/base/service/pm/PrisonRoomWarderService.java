package com.rs.module.base.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderListReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderRespVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderSaveReqVO;
import com.rs.module.base.entity.pm.PrisonRoomWarderDO;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 实战平台-监管管理-监室主协管人员 Service 接口
 *
 * <AUTHOR>
 */
public interface PrisonRoomWarderService extends IBaseService<PrisonRoomWarderDO> {

    /**
     * 创建实战平台-监管管理-监室主协管人员
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPrisonRoomWarder(@Valid PrisonRoomWarderSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-监室主协管人员
     *
     * @param updateReqVO 更新信息
     */
    void updatePrisonRoomWarder(@Valid PrisonRoomWarderSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-监室主协管人员
     *
     * @param id 编号
     */
    void deletePrisonRoomWarder(String id);

    /**
     * 获得实战平台-监管管理-监室主协管人员
     *
     * @param id 编号
     * @return 实战平台-监管管理-监室主协管人员
     */
    PrisonRoomWarderDO getPrisonRoomWarder(String id);

    /**
     * 获得实战平台-监管管理-监室主协管人员分页
     *
     * @param pageReqVO 分页查询
     * @return 实战平台-监管管理-监室主协管人员分页
     */
    PageResult<PrisonRoomWarderDO> getPrisonRoomWarderPage(PrisonRoomWarderPageReqVO pageReqVO);

    /**
     * 获得实战平台-监管管理-监室主协管人员列表
     *
     * @param listReqVO 查询条件
     * @return 实战平台-监管管理-监室主协管人员列表
     */
    List<PrisonRoomWarderDO> getPrisonRoomWarderList(PrisonRoomWarderListReqVO listReqVO);

    /**
     * 获得实战平台-监管管理-监室主协管人员列表
     *
     * @param roomId 监室ID,多个用逗号分隔
     */
    Map<String,List<PrisonRoomWarderRespVO>> getPrisonRoomWarderListByRoomId(String roomId);

    /**
     * 获得实战平台-监管管理-监室主协管人员列表
     * @param roomId 监室ID
     * @return
     */
    List<PrisonRoomWarderRespVO> getListByRoomId(String orgCode, String roomId);

    /**
     * 获得实战平台-监管管理-监室主协管人员列表
     * @param roomId 监室ID
     * @return
     */
    List<PrisonRoomWarderDO> getByRoomId(String orgCode, String roomId, String type);

    void deletePrisonRoomWarderByRoomId(String roomId);
}
