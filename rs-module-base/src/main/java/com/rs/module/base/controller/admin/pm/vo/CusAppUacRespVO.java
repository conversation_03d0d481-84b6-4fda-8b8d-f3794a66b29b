package com.rs.module.base.controller.admin.pm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/9 16:35
 */
@ApiModel(description = "管理后台 - 自定义应用权限 Response VO")
@Data
public class CusAppUacRespVO implements Serializable {

    @ApiModelProperty(value = "应用ID")
    private String yyid;

    @ApiModelProperty(value = "角色ID")
    private List<String> roleIdList = new ArrayList<>();

    @ApiModelProperty(value = "区域ID")
    private List<String> areaIdList = new ArrayList<>();

    @ApiModelProperty(value = "机构ID")
    private List<String> orgIdList = new ArrayList<>();


}
