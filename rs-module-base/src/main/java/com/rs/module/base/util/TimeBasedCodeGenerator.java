package com.rs.module.base.util;

import java.time.Instant;
import java.util.concurrent.atomic.AtomicLong;

public class TimeBasedCodeGenerator {

    // 当前的秒时间戳（单位秒）
    private static volatile long lastSecond = 0;

    // 每秒内自增序列（最大到9999）
    private static final AtomicLong counter = new AtomicLong(0);

    /**
     * 生成不重复的4位数字字符串（每秒最多支持10000个唯一值）
     */
    public static synchronized String generateCode() {
        long currentSecond = Instant.now().getEpochSecond();

        // 如果进入了新的1秒，重置计数器
        if (currentSecond != lastSecond) {
            lastSecond = currentSecond;
            counter.set(0);
        }

        long value = counter.getAndIncrement();

        if (value >= 10000) {
            // 超出最大支持并发，抛异常或等待
            throw new RuntimeException("Exceeded max generation per second (10000)");
        }

        // 格式化为四位，不足补0
        return String.format("%04d", value);
    }

    public static void main(String[] args) {
        // 测试高并发输出
        for (int i = 0; i < 20; i++) {
            System.out.println(generateCode());
        }
    }
}

