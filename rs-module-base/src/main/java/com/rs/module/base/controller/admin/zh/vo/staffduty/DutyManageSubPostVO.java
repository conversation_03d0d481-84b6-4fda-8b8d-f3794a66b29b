package com.rs.module.base.controller.admin.zh.vo.staffduty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "值班岗位明细")
public class DutyManageSubPostVO {
    @ApiModelProperty(value = "post_role.id")
    private String id;
    @ApiModelProperty(value = "值班角色")
    private String subPost;

    @ApiModelProperty("所属岗位编码")
    private String dutyPostId;
    @ApiModelProperty("是否展示")
    private boolean isShowbc;
    @ApiModelProperty(value = "值班时间列表")
    List<DutyManageSubPostTimeVO> subPostTimeVOS;
}
