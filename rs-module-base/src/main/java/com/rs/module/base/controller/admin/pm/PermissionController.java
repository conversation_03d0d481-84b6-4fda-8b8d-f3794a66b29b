package com.rs.module.base.controller.admin.pm;

import com.bsp.common.util.RequestUtil;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.*;
import com.rs.module.base.entity.pm.BizDeskCardDO;
import com.rs.module.base.entity.pm.BizDeskDO;
import com.rs.module.base.service.pm.BizBrowsingPermissionService;
import com.rs.module.base.service.pm.BizDeskCardService;
import com.rs.module.base.service.pm.BizDeskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "权限配置")
@RestController
@RequestMapping("/acp/pm/permission")
@Validated
public class PermissionController {

    @Resource
    private BizBrowsingPermissionService bizBrowsingPermissionService;


    @ApiOperation("保存权限分配设置")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务id", required = true),
            @ApiImplicitParam(name = "type", value = "权限类型(01：角色，02：机构)", required = true),
            @ApiImplicitParam(name = "permissionIds", value = "权限ids")
    })
    public CommonResult saveCardPermissionData(@RequestParam("id") @Valid @NotBlank(message = "业务id不能为空") String id,
                                               @RequestParam("mark") @Valid @NotBlank(message = "业务mark不能为空") String mark,
                                               @RequestParam("type") @Valid @NotBlank(message = "权限类型不能为空") String type,
                                               @RequestParam("permissionIds") String permissionIds) {
        List<String> idList = Collections.emptyList();
        if (StringUtils.isNotBlank(permissionIds)) {
            idList = Arrays.asList(permissionIds.split(","));
        }
        bizBrowsingPermissionService.saveCardPermissionData(id, mark, type, idList);
        return success("", "保存成功");
    }

    @ApiOperation("根据权限类型获取权限分配设置")
    @RequestMapping(value = "/findByType", method = RequestMethod.GET)
    public CommonResult findPermissionData(@RequestParam("id") @Valid @NotBlank(message = "业务id不能为空") String id,
                                               @RequestParam("types") @Valid @NotBlank(message = "权限类型不能为空") String types) {
        List<String> typeList = Arrays.asList(types.split(","));
        return success(bizBrowsingPermissionService.findPermissionData(id, typeList));
    }

    @ApiOperation("根据业务id获取权限分配设置")
    @RequestMapping(value = "/findById", method = RequestMethod.GET)
    public CommonResult findPermissionDataById(@RequestParam("id") @Valid @NotBlank(message = "业务id不能为空") String id) {
        return success(bizBrowsingPermissionService.findPermissionDataById(id));
    }

}
