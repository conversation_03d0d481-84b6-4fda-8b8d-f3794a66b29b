package com.rs.module.base.entity.pm.device;

import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.Data;

import java.io.Serializable;

/**
 * base_splw_tree 实体
 */
@Data
@TableName("acp_pm_splw_tree")
public class SplwTreeDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;
    private String name;
    private Integer type;
    private String treeCode;
    private Integer treeLevel;
    private Integer allNum;
    private Integer isLocked;
    private Integer onlineNum;
    private Integer totalNum;
    private Long chanId;
    private Integer isFocus;
    private Integer status;
    private Integer chanTypeId;
    private Integer treeId;
    private String chnAbility;
    private Integer controlType;
    private Integer platformId;
    private Long devId;
    private Integer storageType;
    private String gb28181Code;
    private Integer devLockStatus;
    private Integer ptzLockStatus;
    private Integer usageType;
    private Integer defaultStreamType;
    private Integer facade;
    private String pathName;
    private Integer pid;
    private String parentPathId;
    private Integer childNumber;
    private Integer orderNo;
    private String prisonId;
}
