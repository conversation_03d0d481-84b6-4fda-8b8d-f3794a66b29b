package com.rs.module.base.controller.admin.pm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "BizDeskTreeNodeVO对象", description = "总台配置树节点VO实体")
@Data
public class BizDeskTreeNodeVO<T> implements Serializable {

    private static final long serialVersionUID = -3388728388316427386L;

    /**
     * 当前节点id
     */
    @ApiModelProperty(value = "当前节点id")
    private String id;

    /**
     * 父节点id
     */
    @ApiModelProperty(value = "父节点id")
    private String parentId;

    /**
     * 当前节点名称
     */
    @ApiModelProperty(value = "当前节点名称")
    private String name;

    /**
     * 当前节点绑定的数据
     */
    @ApiModelProperty(value = "当前节点绑定的数据")
    private T bindData;

}
