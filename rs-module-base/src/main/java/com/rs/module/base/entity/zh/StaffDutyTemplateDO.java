package com.rs.module.base.entity.zh;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@TableName("acp_zh_staff_duty_template")
@KeySequence("acp_zh_staff_duty_template_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_staff_duty_template")
public class StaffDutyTemplateDO extends BaseDO {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 模板名称
     */
    private String name;

    /**
     * 0-停用 1-启用
     */
    private String status;

    /**
     * 类型 1所内值班 医生值班2
     */
    private Integer type;

}
