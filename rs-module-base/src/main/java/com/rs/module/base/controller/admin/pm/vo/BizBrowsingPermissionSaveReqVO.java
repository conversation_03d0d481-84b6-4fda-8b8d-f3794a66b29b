package com.rs.module.base.controller.admin.pm.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 实战平台-监管管理-业务浏览权限配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BizBrowsingPermissionSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("业务ID")
    @NotEmpty(message = "业务ID不能为空")
    private String bizId;

    @ApiModelProperty("业务标识")
    @NotEmpty(message = "业务标识不能为空")
    private String mark;

    @ApiModelProperty("权限类型（01：角色，02：机构，03：用户）")
    @NotEmpty(message = "权限类型（01：角色，02：机构，03：用户）不能为空")
    private String permissionType;

    @ApiModelProperty("权限ID,根据权限类型存储对应ID")
    @NotEmpty(message = "权限ID,根据权限类型存储对应ID不能为空")
    private String permissionId;

}
