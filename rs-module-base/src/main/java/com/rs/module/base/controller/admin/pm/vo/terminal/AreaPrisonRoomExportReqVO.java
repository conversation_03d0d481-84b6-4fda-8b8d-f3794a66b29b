package com.rs.module.base.controller.admin.pm.vo.terminal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-监管管理-区域监室导出 Request VO")
@Data
public class AreaPrisonRoomExportReqVO {
private static final long serialVersionUID = 1L;

    @ApiModelProperty("监室编号")
    private List<String> ids;

    @ApiModelProperty("添加时间")
    private Date[] addTime;

    @ApiModelProperty("机构编号")
    private String orgCode;

    @ApiModelProperty("监室编号")
    private String roomCode;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("是否启用:1启用,0停用")
    private String status;

    @ApiModelProperty("监室类型")
    private String roomType;

    @ApiModelProperty("监区id")
    private String areaId;

}
