package com.rs.module.base.enums;

/**
 * 区域类型
 */
public enum RoomStatusEnum {
    ENABLE("1", "启用"),
    DISABLE("0", "停用");

    private final String code;
    private final String name;

    RoomStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static RoomStatusEnum getByCode(String code) {
        for (RoomStatusEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static RoomStatusEnum getByName(String name) {
        for (RoomStatusEnum type : values()) {
            if (type.name.equals(name)) {
                return type;
            }
        }
        return null;
    }
} 