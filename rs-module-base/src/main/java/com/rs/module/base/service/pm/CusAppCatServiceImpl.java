package com.rs.module.base.service.pm;

import cn.hutool.core.util.StrUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.CusAppCatSaveReqVO;
import com.rs.module.base.dao.pm.CusAppCatDao;
import com.rs.module.base.entity.pm.CusAppCatDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


/**
 * 自定义应用分类 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CusAppCatServiceImpl extends BaseServiceImpl<CusAppCatDao, CusAppCatDO> implements CusAppCatService {

    @Resource
    private CusAppCatDao cusAppCatDao;
    @Resource
    private CusAppService cusAppService;

    @Override
    public String createOrUpdateCusAppCat(CusAppCatSaveReqVO createReqVO) {
        // 插入
        CusAppCatDO cusAppCat = BeanUtils.toBean(createReqVO, CusAppCatDO.class);
        if (StrUtil.isBlank(cusAppCat.getId())) {
            cusAppCatDao.insert(cusAppCat);
        } else {
            cusAppCatDao.updateById(cusAppCat);
        }
        return cusAppCat.getId();
    }

    @Override
    public void deleteCusAppCat(String id) {
        // 校验存在
        validateCusAppCatExists(id);
        boolean exsist = cusAppService.checkCusApp(id);
        if (exsist) {
            throw new ServerException("该分类下存在自定义应用，不能删除");
        }
        // 删除
        cusAppCatDao.deleteById(id);
    }

    private void validateCusAppCatExists(String id) {
        if (cusAppCatDao.selectById(id) == null) {
            throw new ServerException("自定义应用分类数据不存在");
        }
    }

    @Override
    public CusAppCatDO getCusAppCat(String id) {
        return cusAppCatDao.selectById(id);
    }


}
