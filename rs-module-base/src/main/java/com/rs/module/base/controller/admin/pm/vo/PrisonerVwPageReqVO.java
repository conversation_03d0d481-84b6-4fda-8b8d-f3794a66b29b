package com.rs.module.base.controller.admin.pm.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rs.framework.common.pojo.PageParam;
import com.rs.module.base.enums.PrisonerQueryRyztEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 人员组件 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrisonerVwPageReqVO extends PageParam {

    @ApiModelProperty("add_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[] addTime;

    @ApiModelProperty("入所时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[] rssj;

    @ApiModelProperty("姓名")
    private String xm;

    @ApiModelProperty("监区id")
    private String areaId;

    @ApiModelProperty("监所代码")
    private String orgCode;

    @ApiModelProperty("监室号")
    private String jsh;

    @ApiModelProperty("被监管人员类型（01：在押人员，02：被拘人员，03：戒毒人员，99：其他）")
    private String bjgrylx;

    @ApiModelProperty(value = "查询人员类型(ZS:查询在所人员，ALL:查询全部人员)")
    @NotNull(message = "查询人员类型不能为空")
    private PrisonerQueryRyztEnum ryzt;

    @ApiModelProperty("是否查询全部人员")
    private boolean isAll;



}
