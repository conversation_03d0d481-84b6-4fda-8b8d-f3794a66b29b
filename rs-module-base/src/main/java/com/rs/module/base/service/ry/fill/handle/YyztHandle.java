package com.rs.module.base.service.ry.fill.handle;

import cn.hutool.core.collection.CollUtil;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.RyReqVO;
import com.rs.module.base.service.ry.fill.handle.base.RyxxFillHandler;
import com.rs.module.ihc.controller.admin.ipm.vo.VisitRespVO;
import com.rs.module.ihc.dao.ipm.VisitDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName YyztHandle
 * @Description 预约状态
 * <AUTHOR>
 * @Date 2025/6/9 23:13
 * @Version 1.0
 */
@Component
public class YyztHandle extends RyxxFillHandler {
    @Autowired
    public VisitDao visitDao;


    @Override
    public void handle(PrisonerVwRespVO respVO) {
        //查询预约状态
        RyReqVO ryReqVO = new RyReqVO();
        ryReqVO.setJgrybm(respVO.getJgrybm());
        List<VisitRespVO> visitTodo = visitDao.getVisitTodo(ryReqVO);
        if (CollUtil.isNotEmpty(visitTodo)) {
            respVO.setHaveMadeAnAppointment(true);
            respVO.setAppointmentDate(visitTodo.get(0).getDiseaseTime());
        } else {
            respVO.setHaveMadeAnAppointment(false);
        }
    }
}
