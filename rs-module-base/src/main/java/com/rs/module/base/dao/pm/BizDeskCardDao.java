package com.rs.module.base.dao.pm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.cons.CommonConstants;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.base.entity.pm.BizDeskCardDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 实战平台-监管管理-业务总台卡片配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BizDeskCardDao extends IBaseDao<BizDeskCardDO> {


    default List<BizDeskCardDO> getByBizId(String bizId) {
        return this.selectList(new LambdaQueryWrapper<BizDeskCardDO>()
                .eq(BizDeskCardDO::getIsEnable, (short)1)
                .eq(BizDeskCardDO::getBizDeskId, bizId)
                .orderByAsc(BizDeskCardDO::getOrderId));
    }

}
