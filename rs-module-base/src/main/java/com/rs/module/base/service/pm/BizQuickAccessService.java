package com.rs.module.base.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.base.controller.admin.pm.vo.BizQuickAccessSaveReqVO;
import com.rs.module.base.entity.pm.BizQuickAccessDO;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 实战平台-监管管理-业务快捷访问入口配置 Service 接口
 *
 * <AUTHOR>
 */
public interface BizQuickAccessService extends IBaseService<BizQuickAccessDO>{

    /**
     * 创建实战平台-监管管理-业务快捷访问入口配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createBizQuickAccess(@Valid BizQuickAccessSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-业务快捷访问入口配置
     *
     * @param updateReqVO 更新信息
     */
    void updateBizQuickAccess(@Valid BizQuickAccessSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-业务快捷访问入口配置
     *
     * @param id 编号
     */
    void deleteBizQuickAccess(String id);

    /**
     * 获得实战平台-监管管理-业务快捷访问入口配置
     *
     * @param id 编号
     * @return 实战平台-监管管理-业务快捷访问入口配置
     */
    BizQuickAccessDO getBizQuickAccess(String id);

    /**
     *
     * @param markList
     * @return
     */
    CommonResult buildAccessComponent(List<String> markList);

    /**
     *
     * @return
     */
    CommonResult buildAllAccessComponent();


}
