package com.rs.module.base.entity.pm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 自定义应用权限设置 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_cus_app_uac")
@Data
public class CusAppUacDO {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 自定义应用ID
     */
    private String yyid;
    /**
     * 权限类型(01:角色ID 02:区域ID 03:机构ID)
     */
    private String qxlx;
    /**
     * 权限ID(01时存角色ID，02时存区域ID，03时存机构ID)
     */
    private String qxid;

}
