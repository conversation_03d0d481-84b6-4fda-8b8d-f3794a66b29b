package com.rs.module.base.controller.admin.pm.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 在所人员分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrisonerInPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("is_del")
    private Integer isDel;

    @ApiModelProperty("add_time")
    private Date[] addTime;

    @ApiModelProperty("add_user")
    private String addUser;

    @ApiModelProperty("update_user")
    private String updateUser;

    @ApiModelProperty("pro_code")
    private String proCode;

    @ApiModelProperty("pro_name")
    private String proName;

    @ApiModelProperty("city_code")
    private String cityCode;

    @ApiModelProperty("city_name")
    private String cityName;

    @ApiModelProperty("reg_code")
    private String regCode;

    @ApiModelProperty("reg_name")
    private String regName;

    @ApiModelProperty("org_code")
    private String orgCode;

    @ApiModelProperty("org_name")
    private String orgName;

    @ApiModelProperty("jgrybm")
    private String jgrybm;

    @ApiModelProperty("rybh")
    private String rybh;

    @ApiModelProperty("bm")
    private String bm;

    @ApiModelProperty("bz")
    private String bz;

    @ApiModelProperty("csqx")
    private String csqx;

    @ApiModelProperty("csrq")
    private Date csrq;

    @ApiModelProperty("cssj")
    private Date cssj;

    @ApiModelProperty("csyy")
    private String csyy;

    @ApiModelProperty("cwh")
    private String cwh;

    @ApiModelProperty("dwdm")
    private String dwdm;

    @ApiModelProperty("fxdj")
    private String fxdj;

    @ApiModelProperty("gj")
    private String gj;

    @ApiModelProperty("gyqx")
    private Date gyqx;

    @ApiModelProperty("gzdw")
    private String gzdw;

    @ApiModelProperty("hjd")
    private String hjd;

    @ApiModelProperty("hjdxz")
    private String hjdxz;

    @ApiModelProperty("hyzk")
    private String hyzk;

    @ApiModelProperty("jg")
    private String jg;

    @ApiModelProperty("jbr")
    private String jbr;

    @ApiModelProperty("jbsj")
    private Date jbsj;

    @ApiModelProperty("jyaq")
    private String jyaq;

    @ApiModelProperty("jkzk")
    private String jkzk;

    @ApiModelProperty("jsh")
    private String jsh;

    @ApiModelProperty("jlrq")
    private Date jlrq;

    @ApiModelProperty("mz")
    private String mz;

    @ApiModelProperty("rssj")
    private Date rssj;

    @ApiModelProperty("rsyy")
    private String rsyy;

    @ApiModelProperty("ryzt")
    private String ryzt;

    @ApiModelProperty("sbfh")
    private String sbfh;

    @ApiModelProperty("sf")
    private String sf;

    @ApiModelProperty("sfhs")
    private String sfhs;

    @ApiModelProperty("sg")
    private String sg;

    @ApiModelProperty("sypz")
    private String sypz;

    @ApiModelProperty("sypzwsh")
    private String sypzwsh;

    @ApiModelProperty("tabh")
    private String tabh;

    @ApiModelProperty("tssf")
    private String tssf;

    @ApiModelProperty("tz")
    private String tz;

    @ApiModelProperty("tc")
    private String tc;

    @ApiModelProperty("whcd")
    private String whcd;

    @ApiModelProperty("xm")
    private String xm;

    @ApiModelProperty("xmpy")
    private String xmpy;

    @ApiModelProperty("xzz")
    private String xzz;

    @ApiModelProperty("xzzxz")
    private String xzzxz;

    @ApiModelProperty("zc")
    private String zc;

    @ApiModelProperty("zjhm")
    private String zjhm;

    @ApiModelProperty("zjlx")
    private String zjlx;

    @ApiModelProperty("zw")
    private String zw;

    @ApiModelProperty("zwjb")
    private String zwjb;

    @ApiModelProperty("zy")
    private String zy;

    @ApiModelProperty("zzmm")
    private String zzmm;

    @ApiModelProperty("zzczrq")
    private Date zzczrq;

    @ApiModelProperty("front_photo")
    private String frontPhoto;

    @ApiModelProperty("left_photo")
    private String leftPhoto;

    @ApiModelProperty("right_photo")
    private String rightPhoto;

    @ApiModelProperty("sxzm")
    private String sxzm;

    @ApiModelProperty("dabh")
    private String dabh;

    @ApiModelProperty("badw")
    private String badw;

    @ApiModelProperty("badwlx")
    private String badwlx;

    @ApiModelProperty("bar")
    private String bar;

    @ApiModelProperty("barlxff")
    private String barlxff;

    @ApiModelProperty("room_name")
    private String roomName;

    @ApiModelProperty("xb")
    private String xb;

    @ApiModelProperty("sshj")
    private String sshj;

    @ApiModelProperty("gllb")
    private String gllb;

    @ApiModelProperty("zzczjg")
    private String zzczjg;

    @ApiModelProperty("xqqsrq")
    private Date xqqsrq;

    @ApiModelProperty("xqjzrq")
    private Date xqjzrq;

    @ApiModelProperty("ajbh")
    private String ajbh;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
