package com.rs.module.base.controller.admin.pm.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 自定义应用分类 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CusAppCatRespVO extends BaseVO implements TransPojo{
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("系统ID")
    private String systemId;
    @ApiModelProperty("分类名称")
    private String flmc;
    @ApiModelProperty("排序")
    private Integer orderId;
    @ApiModelProperty("分类标识")
    private String mark;
    @ApiModelProperty("前缀地址")
    private String preUrl;
}
