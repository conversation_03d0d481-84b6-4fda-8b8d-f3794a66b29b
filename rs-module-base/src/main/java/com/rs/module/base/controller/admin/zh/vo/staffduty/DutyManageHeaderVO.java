package com.rs.module.base.controller.admin.zh.vo.staffduty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("排版管理-表头")
public class DutyManageHeaderVO {
    @ApiModelProperty(value = "")
    private String id;
    @ApiModelProperty(value = "值班岗位")
    private String post;

    @ApiModelProperty("是否下面有角色")
    private boolean hasSubPost;

    @ApiModelProperty("值班角色明显列表")
    private List<DutyManageSubPostVO> subPostList;

}
