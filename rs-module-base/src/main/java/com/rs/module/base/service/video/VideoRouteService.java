package com.rs.module.base.service.video;

import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.base.controller.admin.video.vo.*;
import org.springframework.scheduling.annotation.Async;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2020/11/12 15:03
 */

public interface VideoRouteService {

    /**
     * 获取指定后缀格式的实时视频流
     * @param dto
     * @return
     */
    String getOtherRealTimeStream(OtherRealTimeStreamDto dto) throws Exception;

    /***
     * 获取实时视频流地址(web端)
     * @param dto
     * @return
     */
    String getRealTimeStreamForWeb(OtherRealTimeStreamDto dto);

    String getRealTimeStreamForApp(OtherRealTimeStreamAppDto dto) throws Exception;
    
    String getSearchRecordRequest(Map<String, Object> dto) throws Exception;

     String getUapToken();

     JSONObject getUapTokenFe();

    /***
     * 适配V1.4版本
     * @return
     */
    String getUapTokenFeNew();


    /**
     * 获取实时视频流地址（flv格式）
     * @param dto
     * @return
     */
    String getFlvRealTimeStream(FlvRealTimeStreamDto dto) throws Exception;

    /**
     * 录像文件操作请求
     * @param dto
     * @return
     */
    String recordFileOperRequestTransfer(FileOperRequestTransferDto dto) throws Exception;

    /**
     * 视频流地址
     * @param request
     * @return
     */
    String recordFileUrl(HttpServletRequest request);

    /**
     * 获取实时视频流地址（flv格式）
     * @param dto
     * @return
     */
    String getGoVideoOrGoVlinkRecordRequest(GoVideoOrGoVlinkRecordRequestDto dto) throws Exception;

    /**
     * 云台控制
     * @param dto
     * @return
     */
    String ptzCtrl(PtzCtrlDto dto) throws Exception;

    /**
     * 云台控制-视频联网
     * @param dto
     * @return
     */
    String ptzCtrlRequest(PtzCtrlNewDto dto) throws Exception;

    /**
     * 云台控制-视频联网
     * @param dto
     * @return
     */
    Object recordMonthRetrievalRequest(RecordMonthRetrievalDTO dto) throws Exception;

    /**
     * 云台控制(网关开放接口)
     * @param dto
     * @return
     */
    String ptzCtrl2(PtzCtrlDto dto) throws Exception;

    /**
     * 恢复到默认预置位
     * @param dto
     * @return
     */
    String turnToDefaultPreset(TurnToDefaultPresetDto dto)throws Exception;

    /**
     * 删除预置位
     * @param vo
     * @return
     */
    String deletePreset(DeletePresetVO vo)throws Exception;

    /**
     * 恢复到默认预置位
     * @param chanId
     * @return
     */
    String turnToDefaultPreset2(String chanId)throws Exception;



    /**
     * 设置云台预置点
     * @param dto
     * @return
     */
    String presetCtrl(PresetCtrlDto dto)throws Exception;

    /***
     * 获取通道所有预置点信息
     * @param chanId
     * @return
     */
    List<PreseDTO> getPresetByChannelId(String chanId);

    /**
     * 保存锁定信息
     * @param dto
     * @return
     */
    String savePtzLock(SavePtzLockDto dto)throws Exception;

    /***
     * 获取设备树
     * @param dto
     * @return
     */
    List<VideoTreeVO> getVideoTree(VideoTreeDto dto) throws Exception;

    /***
     * 搜索设备树
     * @param dto
     * @return
     */
    List<VideoTreeVO> getVideoByPathId(VideoPageDto dto) throws Exception;

    /***
     * 搜索设备树
     * @param dto
     * @return
     */
    PageResult getVideoByPage(VideoPageDto dto) throws Exception;

    /***
     * 视频联网-根据目录id查询下面指定摄像头
     * @param id
     * @param num
     * @return
     */
    List<VideoTreeVO> getVideoListById(String id,Integer num,String prisonId);


    /***
     *录像文件操作请求
     * @param transferDTO
     * @return
     */
    JSONObject recordFileOperRequestTransfer(RecordFileOperRequestTransferDTO transferDTO);

    /**
     * 获取实时视频流地址（flv格式）
     * @param dto
     * @return
     */
    List<QueryRecordRequestVO> queryRecordRequest(QueryRecordRequestDTO dto) throws Exception;

    /***
     * 获取通道音视频信息
     * @param chanId
     * @return
     */
    String govDevChnnInfoAv(String chanId);

    /***
     * 获取通道详细信息
     * @param chanId
     * @return
     */
    String govDevChnnInfo(String chanId);

    /***
     * 获取通道详情
     * @param chanId
     * @return
     */
    DevChnnInfoVO govChanInfo(String chanId);

    /***
     * 根据chanId获取监室信息
     * @param chanId
     * @return
     */
    //AreaPrisonRoomPageResult getRoomInfoByChanId(String chanId);

    void init() throws Exception;

    void initNew(String prisonId);

    /***
     * 视频联网添加区域
     * @return
     */
    String addTreeNodeRequest(AddTreeNodeRequestVO addTreeNodeRequestVO);

    /***
     * 删除区域树
     * @param addTreeNodeRequestVO
     * @return
     */
    String clearTreeNodeRequest(AddTreeNodeRequestVO addTreeNodeRequestVO);

    /***
     * 复制设备
     * @param addTreeNodeRequestVO
     * @return
     */
    String moveTreeNodeLeafRequest(AddTreeNodeRequestVO addTreeNodeRequestVO);


    String getParentTreeCode();
    /***
     * 删除区域树
     * @param treeCode
     * @return
     */
    String deleteTreeNodeRequest(String treeCode);


    /***
     * 编辑通道信息
     * @param chanId
     * @return
     */
    void setDevChnnRequest(String chanId,String name,String treeCode);

    /****
     * 获取treeCode
     * @param url
     * @param map
     * @return
     */
    String getTreeCode(String url,String chanId, Map map);

    /****
     * 获取treeCode
     * @param
     * @return
     */
    String getChannelIds(String chanId);

    Integer getChannelStatus(String chanId);


    /***
     *
     * @param devId
     * @return
     */
    String getAllDeviceRequest(String devId);

    /***
     * 获取所有视频联网同步过来的所有设备
     * @return
     */

    /***
     * 同步视频联网的设备状态
     */
    void sysDeviceStatus();


    /***
     * 根据treeCode获取联网平台目录
     * @param treeCode
     * @return
     */
    JSONObject getSplwAreaByTreeCode(String treeCode) throws Exception;


    String getRootCode();

    Integer getTreeLevel(String treeCode, Integer type);

    Map<String, String> getTreePathIds(String treeCode, Integer treeLevel);

    Map<String, String> getTreePathIds2(String treeCode, Integer treeLevel);

    Map<String, String> getTreePathIds3(String treeCode, Integer treeLevel);

    @Async
    void sysDeviceTree(String prisonId);
}
