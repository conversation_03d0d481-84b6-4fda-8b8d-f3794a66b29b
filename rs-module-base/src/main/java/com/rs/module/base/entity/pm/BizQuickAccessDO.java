package com.rs.module.base.entity.pm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 实战平台-监管管理-业务快捷访问入口配置 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_biz_quick_access")
@KeySequence("acp_pm_biz_quick_access_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_biz_quick_access")
public class BizQuickAccessDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 业务入口名称
     */
    private String name;
    /**
     * 业务入口标识
     */
    private String mark;
    /**
     * 业务分类id
     */
    private String typeId;
    /**
     * 业务分类标识
     */
    private String typeMark;
    /**
     * 访问地址
     */
    private String accessUrl;
    /**
     * 触发方式
     */
    private String triggerMethod;
    /**
     * 图标地址
     */
    private String iconUrl;
    /**
     * 弹窗名称
     */
    private String popName;
    /**
     * 是否启动浏览权限配置
     */
    private Short isEnableBrowsingPermission;
    /**
     * 是否启用
     */
    private Short isEnable;
    /**
     * 排序Id
     */
    private Integer orderId;
    /**
     * 备注
     */
    private String remark;

}
