package com.rs.module.base.entity.zh;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@TableName("acp_zh_staff_duty_role")
@KeySequence("acp_zh_staff_duty_role_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_staff_duty_role")

public class StaffDutyRoleDO extends BaseDO {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 模板id-acp_zh_staff_duty_template
     */
    private String postId;
    /**
     * 值班角色
     */
    private String dutyRoleName;
    /**
     * 岗位名称
     */
    private String postName;

    /***
     * 岗位编号
     */
    private String dutyPostId;

    /**
     * 缺勤通知人sfzh
     */
    private String absentNotifierSfzh;
    /**
     * 缺勤通知人
     */
    private String absentNotifierName;
}
