package com.rs.module.base.dao.pm;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.base.controller.admin.pm.vo.ForeignPersonnelListRespVO;
import com.rs.module.base.controller.admin.pm.vo.PoreginPersonnelPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwPageReqVO;
import com.rs.module.base.entity.pm.PrisonerListDO;
import com.rs.module.base.enums.PrisonerQueryRyztEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 在所人员ListDao
 *
 * <AUTHOR>
 */
@Mapper
public interface PrisonerListDao extends IBaseDao<PrisonerListDO> {


    default PageResult<PrisonerListDO> selectPage(PrisonerVwPageReqVO reqVO) {
        LambdaQueryWrapperX<PrisonerListDO> queryWrapper = new LambdaQueryWrapperX<PrisonerListDO>()
                .betweenIfPresent(PrisonerListDO::getAddTime, reqVO.getAddTime())
                .betweenIfPresent(PrisonerListDO::getRssj, reqVO.getRssj())
                .eqIfPresent(PrisonerListDO::getAreaId, reqVO.getAreaId())
                .eqIfPresent(PrisonerListDO::getOrgCode, reqVO.getOrgCode())
                .eqIfPresent(PrisonerListDO::getJsh, reqVO.getJsh())
                .eqIfPresent(PrisonerListDO::getBjgrylx, reqVO.getBjgrylx())
                .eq(reqVO.getRyzt() != null && reqVO.getRyzt().getCode().equals(PrisonerQueryRyztEnum.ZS.getCode()),
                        PrisonerListDO::getRyzt, reqVO.getRyzt().getCode());
        if (StrUtil.isNotBlank(reqVO.getXm())) {
            queryWrapper.and(like -> {
                like.like(PrisonerListDO::getXm, reqVO.getXm()).or()
                        .like(PrisonerListDO::getXmpy, reqVO.getXm().toUpperCase()).or()
                        .like(PrisonerListDO::getBm, reqVO.getXm());
            });
        }
        queryWrapper.orderByDesc(PrisonerListDO::getRssj);
        // 特殊：不分页，直接查询全部
        if (reqVO.isAll()) {
            List<PrisonerListDO> list = selectList(queryWrapper);
            return new PageResult<>(list, (long) list.size());
        }
        Page<PrisonerListDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<PrisonerListDO> prisonerInVwPage = selectPage(page, queryWrapper);
        return new PageResult<>(prisonerInVwPage.getRecords(), prisonerInVwPage.getTotal());
    }


    // 暂时未进行机构筛选
    Page<PrisonerListDO> getCivilizedPersonalPrisonerSelectCompomentList(Page<PrisonerListDO> page,
                                                                         @Param("req") PrisonerVwPageReqVO req,
                                                                         @Param("startAddTime") Date startAddTime,
                                                                         @Param("endAddTime") Date endAddTime,
                                                                         @Param("startRssj") Date startRssj,
                                                                         @Param("endRssj") Date endRssj,
                                                                         @Param("ryzt") String ryzt,
                                                                         @Param("xmPy") String xmPy);

    /**
     * 外来人员
     * @param pageReqVO
     * @return
     */
    IPage<ForeignPersonnelListRespVO> getForeinPersionnelSelectCompoment(Page page,
                                                                         @Param("pageReqVO") PoreginPersonnelPageReqVO pageReqVO);
}

