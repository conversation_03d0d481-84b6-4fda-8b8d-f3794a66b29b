package com.rs.module.base.service.sys;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.base.controller.admin.sys.vo.BusTraceListReqVO;
import com.rs.module.base.controller.admin.sys.vo.BusTracePageReqVO;
import com.rs.module.base.controller.admin.sys.vo.BusTraceSaveReqVO;
import com.rs.module.base.entity.sys.BusTraceDO;
import com.rs.module.base.enums.BusTypeEnum;

/**
 * 实战平台-业务轨迹 Service 接口
 *
 * <AUTHOR>
 */
public interface BusTraceService extends IBaseService<BusTraceDO>{

    /**
     * 创建实战平台-业务轨迹
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createBusTrace(@Valid BusTraceSaveReqVO createReqVO);

    /**
     * 更新实战平台-业务轨迹
     *
     * @param updateReqVO 更新信息
     */
    void updateBusTrace(@Valid BusTraceSaveReqVO updateReqVO);
    
    /**
     * 保存实战平台-业务轨迹
     * @param busType BusTypeEnum 业务类型枚举
     * @param content String 轨迹内容
     * @param jgrybm String 监管人员编码
     * @param orgCode String 机构代码
     * @param businessId String 业务主键
     */
    void saveBusTrace(BusTypeEnum busType, String content, String jgrybm, String orgCode, String businessId);

    /**
     * 删除实战平台-业务轨迹
     *
     * @param id 编号
     */
    void deleteBusTrace(String id);

    /**
     * 获得实战平台-业务轨迹
     *
     * @param id 编号
     * @return 实战平台-业务轨迹
     */
    BusTraceDO getBusTrace(String id);

    /**
    * 获得实战平台-业务轨迹分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-业务轨迹分页
    */
    PageResult<BusTraceDO> getBusTracePage(BusTracePageReqVO pageReqVO);

    /**
    * 获得实战平台-业务轨迹列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-业务轨迹列表
    */
    List<BusTraceDO> getBusTraceList(BusTraceListReqVO listReqVO);


    /**
     * 获取监管人员轨迹相关的场所
     * @param jgrybm String 监管人员编码
     * @param busTypes List<String> 轨迹业务类型
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getJgryTraceOrgList(String jgrybm, List<String> busTypes);

    void updateBusTraceByBusinessId(String businessId, String jgrybm, BusTypeEnum busType, String content);
}
