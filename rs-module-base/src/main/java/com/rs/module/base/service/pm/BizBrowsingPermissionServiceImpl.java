package com.rs.module.base.service.pm;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.cache.RedisClient;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.base.constant.CommonConstants;
import com.rs.module.base.dao.pm.BizBrowsingPermissionDao;
import com.rs.module.base.entity.pm.BizBrowsingPermissionDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 实战平台-监管管理-业务浏览权限配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BizBrowsingPermissionServiceImpl extends BaseServiceImpl<BizBrowsingPermissionDao, BizBrowsingPermissionDO> implements BizBrowsingPermissionService {

    @Resource
    private BizBrowsingPermissionDao bizBrowsingPermissionDao;

    @Override
    public void saveCardPermissionData(String id, String mark, String type, List<String> idList) {
        List<BizBrowsingPermissionDO> privileges = new ArrayList<>();
        privileges.addAll(getPrivilegeEntities(id, mark, type, idList));
        // 删除旧的权限分配数据
        Map<String, Object> rmMapParam = new HashMap<>();
        this.remove(new LambdaQueryWrapper<BizBrowsingPermissionDO>()
                .eq(BizBrowsingPermissionDO::getBizId, id)
                .eq(BizBrowsingPermissionDO::getPermissionType, type));
        // 批量插入新的权限分配数据
        if (!CollectionUtils.isEmpty(privileges)) {
            this.saveBatch(privileges);
        }
    }

    private List<BizBrowsingPermissionDO> getPrivilegeEntities(String bizId, String mark, String type, List<String> idList) {
        if (!CollectionUtils.isEmpty(idList)) {
            List<BizBrowsingPermissionDO> privilegeEntities = idList.stream()
                    .filter(id -> StringUtils.isNotEmpty(id))
                    .map(id -> {
                        BizBrowsingPermissionDO permissionDO = new BizBrowsingPermissionDO();
                        permissionDO.setId(StringUtil.getGuid());
                        permissionDO.setBizId(bizId);
                        permissionDO.setMark(mark);
                        permissionDO.setPermissionType(type);
                        permissionDO.setPermissionId(id);
                        return permissionDO;
                    }).collect(Collectors.toList());
            return privilegeEntities;
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public Map<String, List<String>> findPermissionData(String id, List<String> typeList) {
        List<BizBrowsingPermissionDO> permissionList = list(new LambdaQueryWrapper<BizBrowsingPermissionDO>()
                .eq(BizBrowsingPermissionDO::getBizId, id)
                .in(BizBrowsingPermissionDO::getPermissionType, typeList));

        Map<String, List<String>> permissionMap = new HashMap<>();
        permissionList.forEach(permission -> {
            String type = permission.getPermissionType();
            String permissionId = permission.getPermissionId();
            List<String> idListTemp;
            if (permissionMap.containsKey(type)) {
                idListTemp = permissionMap.get(type);
            } else {
                idListTemp = new ArrayList<>();
                permissionMap.put(type, idListTemp);
            }
            if (!idListTemp.contains(permissionId)) {
                idListTemp.add(permissionId);
            }
        });
        return permissionMap;
    }

    @Override
    public Map<String, List<String>> findPermissionDataById(String id) {
        List<BizBrowsingPermissionDO> permissionList = list(new LambdaQueryWrapper<BizBrowsingPermissionDO>()
                .eq(BizBrowsingPermissionDO::getBizId, id));

        Map<String, List<String>> permissionMap = new HashMap<>();
        List<String> roleIdList = permissionList.stream().filter(permission -> "01".equals(permission.getPermissionType()))
                .map(permission -> permission.getPermissionId()).collect(Collectors.toList());
        List<String> areaIdList = permissionList.stream().filter(permission -> "02".equals(permission.getPermissionType()))
                .map(permission -> permission.getPermissionId()).collect(Collectors.toList());
        List<String> orgIdList = permissionList.stream().filter(permission -> "03".equals(permission.getPermissionType()))
                .map(permission -> permission.getPermissionId()).collect(Collectors.toList());
        permissionMap.put("roleIdList", roleIdList);
        permissionMap.put("areaIdList", areaIdList);
        permissionMap.put("orgIdList", orgIdList);
        return permissionMap;
    }

}
