package com.rs.module.base.controller.admin.pm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 实战平台-综合管理-基础信息-起居信息新增/修改 Request VO")
@Data
public class AreaPrisonRoomQjxxSaveReqVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("更新人")
    private String updateUser;
    @ApiModelProperty("卫生间改造")
    private String toiletRenovation;
    @ApiModelProperty("取暖方式")
    private String heatingMethods;
    @ApiModelProperty("防暑降温")
    private String fsjw;
    @ApiModelProperty("直饮水改造")
    private String zysgz;
    @ApiModelProperty("床位制")
    private String cwz;

}
