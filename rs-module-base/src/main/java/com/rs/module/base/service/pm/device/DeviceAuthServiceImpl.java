package com.rs.module.base.service.pm.device;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.DeviceAuthListReqVO;
import com.rs.module.base.controller.admin.pm.vo.DeviceAuthPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.DeviceAuthSaveReqVO;
import com.rs.module.base.dao.pm.AreaDao;
import com.rs.module.base.dao.pm.DeviceAuthDao;

import com.rs.module.base.dao.pm.device.BaseDeviceDao;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.base.entity.pm.DeviceAuthDO;

import com.rs.module.base.entity.pm.device.BaseDeviceDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * 实战平台-监管管理-设备授权 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DeviceAuthServiceImpl extends BaseServiceImpl<DeviceAuthDao, DeviceAuthDO> implements DeviceAuthService {

    @Resource
    private DeviceAuthDao deviceAuthDao;

    @Resource
    private BaseDeviceDao baseDeviceDao;

    @Resource
    private AreaDao areaDao;

    @Override
    public String createDeviceAuth(DeviceAuthSaveReqVO createReqVO) {
        // 插入
        DeviceAuthDO deviceAuth = BeanUtils.toBean(createReqVO, DeviceAuthDO.class);
        deviceAuthDao.insert(deviceAuth);
        // 返回
        return deviceAuth.getId();
    }

    @Override
    public void updateDeviceAuth(DeviceAuthSaveReqVO updateReqVO) {
        // 校验存在
        validateDeviceAuthExists(updateReqVO.getId());
        // 更新
        DeviceAuthDO updateObj = BeanUtils.toBean(updateReqVO, DeviceAuthDO.class);
        deviceAuthDao.updateById(updateObj);
    }

    @Override
    public void deleteDeviceAuth(String id) {
        // 校验存在
        validateDeviceAuthExists(id);
        // 删除
        deviceAuthDao.deleteById(id);
    }

    private void validateDeviceAuthExists(String id) {
        if (deviceAuthDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-设备授权数据不存在");
        }
    }

    @Override
    public DeviceAuthDO getDeviceAuth(String id) {
        return deviceAuthDao.selectById(id);
    }

    @Override
    public List<BaseDeviceDO> getDeviceAuthByIP(String ip, String deviceType) {
        List<BaseDeviceDO> deviceDOList = new ArrayList<>();
        List<DeviceAuthDO> deviceAuthDOList = deviceAuthDao.selectList(new LambdaQueryWrapper<DeviceAuthDO>().eq(DeviceAuthDO::getAuthIp, ip));
        for (DeviceAuthDO deviceAuthDO : deviceAuthDOList) {
            deviceDOList.addAll(baseDeviceDao.selectList(new LambdaQueryWrapper<BaseDeviceDO>().eq(BaseDeviceDO::getId, deviceAuthDO.getDeviceId()).eq(BaseDeviceDO::getDeviceTypeId, deviceType).eq(BaseDeviceDO::getIsEnabled, 1)));
        }
        return deviceDOList;
    }

    @Override
    public AreaDO getOneAreaByIP(String ip, String deviceType) {
        List<BaseDeviceDO> deviceDOList = getDeviceAuthByIP(ip, deviceType);
        if (deviceDOList.size() > 0) {
            BaseDeviceDO deviceDO = deviceDOList.get(0);
            return areaDao.selectById(deviceDO.getAreaId());
        }
        return null;
    }

    @Override
    public PageResult<DeviceAuthDO> getDeviceAuthPage(DeviceAuthPageReqVO pageReqVO) {
        return deviceAuthDao.selectPage(pageReqVO);
    }

    @Override
    public List<DeviceAuthDO> getDeviceAuthList(DeviceAuthListReqVO listReqVO) {
        return deviceAuthDao.selectList(listReqVO);
    }


}
