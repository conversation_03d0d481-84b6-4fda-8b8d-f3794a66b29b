package com.rs.module.base.tag.interceptor;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rs.framework.common.util.spring.SpringUtils;
import com.rs.framework.mybatis.entity.BaseDO;
import com.rs.module.base.entity.pm.PrisonerTagDO;
import com.rs.module.base.tag.annotation.*;
import com.rs.module.base.tag.enums.RuleCombineType;
import com.rs.module.base.tag.enums.TagEnum;
import com.rs.module.base.tag.enums.TagScenarioType;
import com.rs.module.base.tag.parser.TagConditionParser;
import com.rs.module.base.tag.service.TagService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Intercepts({ @Signature(type = Executor.class, method = "update", args = { MappedStatement.class, Object.class }) })
public class TagInterceptor implements Interceptor {

    @Autowired
    @Lazy
    private TagService tagService;
    @Autowired
    private TagConditionParser conditionParser;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 1. 获取执行参数
        Object[] args = invocation.getArgs();
        if (args == null || args.length < 2) {
            return invocation.proceed();
        }

        MappedStatement ms = (MappedStatement) args[0];
        Object parameterObject = args[1];

        // 2. 获取SQL类型
        if (ms == null) {
            return invocation.proceed();
        }
        SqlCommandType sqlCommandType = ms.getSqlCommandType();

        // 3. 根据SQL类型处理标签
        if (parameterObject != null) {
            // 获取实际的实体对象
            Object entity = getEntityFromParameter(parameterObject);
            if (entity != null) {
                // 检查是否是逻辑删除
                boolean isLogicDelete = false;
                if (sqlCommandType == SqlCommandType.UPDATE) {
                    try {
                        Field deleteField = getFieldFromClassHierarchy(entity.getClass(), "isDel");
                        if (deleteField != null) {
                            deleteField.setAccessible(true);

                            Object deleteValue = deleteField.get(entity);
                            if (deleteValue instanceof Boolean && (Boolean) deleteValue) {
                                isLogicDelete = true;
                            }
                        }
                    } catch (Exception e) {
                        log.warn("检查逻辑删除状态失败", e);
                    }
                }

                switch (sqlCommandType) {
                    case INSERT:
                        handleInsert(entity);
                        break;
                    case UPDATE:
                        if (isLogicDelete) {
                            handleDelete(entity);
                        } else {
                            handleUpdate(entity);
                        }
                        break;
                    case DELETE:
                        handleDelete(entity);
                        break;
                }
            }
        }
        // 4. 执行原始方法
        return invocation.proceed();
    }

    private void handleInsert(Object entity) {
        if (entity == null) {
            log.warn("Entity is null");
            return;
        }

        Class<?> entityClass = entity.getClass();
        if (entityClass == null) {
            log.error("Entity class is null");
            return;
        }

        // 1. 检查实体是否需要自动打标签
        AutoTag autoTag = null;
        try {
            autoTag = entityClass.getAnnotation(AutoTag.class);
            if (autoTag == null) {
                return;
            }
        } catch (Exception e) {
            log.error("获取AutoTag注解失败", e);
            return;
        }

        // 2. 获取实体ID
        String businessId = getBusinessId(entity);
        if (businessId == null) {
            log.warn("获取businessId失败");
            return;
        }

        String jgrybm = getJgrybm(entity);
        if (jgrybm == null) {
            log.warn("获取jgrybm失败");
            return;
        }

        // 3. 处理所有字段的标签规则
        processEntityTags(entity, businessId, jgrybm, autoTag.businessType());
    }

    private void handleUpdate(Object entity) {
        // 1. 检查实体是否需要自动打标签
        AutoTag autoTag = null;
        try {
            autoTag = entity.getClass().getAnnotation(AutoTag.class);
            if (autoTag == null) {
                return;
            }
        } catch (Exception e) {
            return;
        }

        // 2. 获取实体ID
        String businessId = getBusinessId(entity);
        if (businessId == null) {
            return;
        }

        String jgrybm = getJgrybm(entity);
        if (jgrybm == null) {
            return;
        }

        // 3. 获取更新前的实体数据
        Object oldEntity = getOldEntity(entity);
        if (oldEntity == null) {
            // 如果获取不到旧数据，则按新增处理
            handleInsert(entity);
            return;
        }

        // 4. 处理所有字段的标签规则
        processEntityTagsWithOldValue(entity, oldEntity, businessId, jgrybm, autoTag.businessType());
    }

    private void handleDelete(Object entity) {
        // 1. 检查实体是否需要自动打标签
        AutoTag autoTag = null;
        try {
            autoTag = entity.getClass().getAnnotation(AutoTag.class);
            if (autoTag == null) {
                return;
            }
        } catch (Exception e) {
            return;
        }

        // 2. 获取实体ID
        String businessId = getBusinessId(entity);
        if (businessId == null) {
            return;
        }

        // 3. 删除所有相关标签
        tagService.removeAllTags(businessId, autoTag.businessType());
    }

    private void processEntityTags(Object entity, String businessId, String jgrybm, String businessType) {
        if (entity == null || businessId == null || jgrybm == null || businessType == null) {
            log.warn("Required parameters are null: entity={}, businessId={}, jgrybm={}, businessType={}",
                    entity, businessId, jgrybm, businessType);
            return;
        }

        Class<?> entityClass = entity.getClass();
        if (entityClass == null) {
            log.error("Entity class is null");
            return;
        }

        // 收集所有规则及其匹配结果
        List<TagRuleMatch> ruleMatches = new ArrayList<>();

        for (Field field : entityClass.getDeclaredFields()) {
            if (field == null) {
                log.warn("Field is null");
                continue;
            }

            // 处理单个规则
            TagRule tagRule = field.getAnnotation(TagRule.class);
            if (tagRule != null) {
                boolean matched = processTagRule(entity, field, tagRule, businessId, jgrybm, businessType);
                ruleMatches.add(new TagRuleMatch(tagRule, matched));
            }

            // 处理规则组
            TagRuleGroup ruleGroup = field.getAnnotation(TagRuleGroup.class);
            if (ruleGroup != null) {
                processTagRuleGroup(entity, field, ruleGroup, businessId, jgrybm, businessType);
            }
        }

        // 处理互斥规则
        processExclusiveRules(ruleMatches, jgrybm, businessType, businessId);
    }

    private void processTagRuleGroup(Object entity, Field field, TagRuleGroup ruleGroup, String businessId,
            String jgrybm, String businessType) {
        try {
            // 获取字段值
            field.setAccessible(true);
            Object value = field.get(entity);
            // 处理规则组中的所有规则
            boolean matched = false;
            for (TagRule rule : ruleGroup.rules()) {
                boolean ruleMatched = processTagRule(entity, field, rule, businessId, jgrybm, businessType);

                if (ruleGroup.combineType() == RuleCombineType.OR) {
                    matched = matched || ruleMatched;
                } else {
                    matched = matched && ruleMatched;
                }
            }
        } catch (Exception e) {
            log.error("处理标签规则组失败", e);
        }
    }

    private boolean processTagRule(Object entity, Field field, TagRule rule, String businessId, String jgrybm,
            String businessType) {
        try {
            // 1. 检查依赖关系
            if (!checkDependencies(rule, businessId)) {
                return false;
            }

            // 2. 检查有效期
            if (!checkValidity(rule)) {
                return false;
            }

            // 3. 检查统计规则
            // if (!checkStatRules(rule, businessId)) {
            // return false;
            // }

            // 4. 处理标签条件
            boolean matched = processConditions(entity, field, rule);

            // 5. 根据场景类型处理标签
            processTagByScenario(rule, matched, jgrybm, businessType, businessId);

            return matched;
        } catch (Exception e) {
            log.error("处理标签规则失败", e);
            return false;
        }
    }

    private boolean checkDependencies(TagRule rule, String businessId) {
        if (rule.dependsOn().length == 0) {
            return true;
        }

        List<String> existingTags = tagService.getEntityTags(businessId).stream().map(PrisonerTagDO::getTagCode)
                .collect(Collectors.toList());

        boolean hasDependency = Arrays.stream(rule.dependsOn()).anyMatch(existingTags::contains);

        return !rule.forceDependency() || hasDependency;
    }

    private boolean checkValidity(TagRule rule) {
        if (rule.validDays() == 0 && rule.expireTime().isEmpty()) {
            return true;
        }

        if (!rule.expireTime().isEmpty()) {
            return LocalDateTime.now().isBefore(LocalDateTime.parse(rule.expireTime()));
        }

        return true;
    }

    // private boolean checkStatRules(TagRule rule, String businessId) {
    // if (rule.statRules().length == 0) {
    // return true;
    // }
    // return true;
    // }

    private boolean processConditions(Object entity, Field field, TagRule rule) {
        try {
            // 获取字段值
            field.setAccessible(true);
            Object value = field.get(entity);

            // 处理所有条件
            boolean matched = true;
            for (TagCondition condition : rule.conditions()) {
                // 从rule中获取TagEnum
                TagEnum tagEnum = rule.tag();
                boolean conditionMatched = conditionParser.evaluate(value, condition, tagEnum);
                if (condition.reverse()) {
                    conditionMatched = !conditionMatched;
                }
                matched = matched && conditionMatched;
            }

            return matched;
        } catch (Exception e) {
            log.error("处理标签条件失败", e);
            return false;
        }
    }

    /**
     * 根据场景类型处理标签
     *
     * @param rule 标签规则
     * @param matched 条件是否匹配
     * @param jgrybm 监管人员编码
     * @param businessType 业务类型
     * @param businessId 业务ID
     */
    private void processTagByScenario(TagRule rule, boolean matched, String jgrybm, String businessType, String businessId) {
        try {
            TagScenarioType scenario = rule.scenario();

            switch (scenario) {
                case ADD:
                    // 添加标签场景：条件匹配时添加标签，不匹配时移除标签
                    if (matched) {
                        tagService.addTag(jgrybm, rule.tag().getTagCode(), rule.tag().getTagType(),
                                rule.tag().getTagName(), businessType, businessId);
                    } else {
                        tagService.removeTag(jgrybm, rule.tag().getTagCode(), rule.tag().getTagType());
                    }
                    break;

                case REMOVE:
                    // 删除标签场景：条件匹配时删除标签
                    if (matched) {
                        // 如果指定了删除目标，删除指定的标签
                        if (rule.removeTargets().length > 0) {
                            for (TagEnum removeTarget : rule.removeTargets()) {
                                tagService.removeTag(jgrybm, removeTarget.getTagCode(), removeTarget.getTagType());
                                log.info("删除标签场景：删除目标标签 {} for jgrybm: {}, 原因: {}",
                                        removeTarget.getTagName(), jgrybm, rule.removeDescription());
                            }
                        } else {
                            // 否则删除规则中指定的标签
                            tagService.removeTag(jgrybm, rule.tag().getTagCode(), rule.tag().getTagType());
                            log.info("删除标签场景：删除标签 {} for jgrybm: {}, 原因: {}",
                                    rule.tag().getTagName(), jgrybm, rule.removeDescription());
                        }
                    }
                    break;

                case UPDATE:
                    // 更新标签场景：条件匹配时更新标签
                    if (matched) {
                        // 先删除旧标签，再添加新标签
                        tagService.removeTag(jgrybm, rule.tag().getTagCode(), rule.tag().getTagType());
                        tagService.addTag(jgrybm, rule.tag().getTagCode(), rule.tag().getTagType(),
                                rule.tag().getTagName(), businessType, businessId);
                        log.info("更新标签场景：更新标签 {} for jgrybm: {}", rule.tag().getTagName(), jgrybm);
                    }
                    break;

                case REPLACE:
                    // 替换标签场景：条件匹配时用新标签替换旧标签
                    if (matched) {
                        // 删除指定的旧标签
                        if (rule.removeTargets().length > 0) {
                            for (TagEnum removeTarget : rule.removeTargets()) {
                                tagService.removeTag(jgrybm, removeTarget.getTagCode(), removeTarget.getTagType());
                            }
                        }
                        // 添加新标签
                        tagService.addTag(jgrybm, rule.tag().getTagCode(), rule.tag().getTagType(),
                                rule.tag().getTagName(), businessType, businessId);
                        log.info("替换标签场景：替换标签 {} for jgrybm: {}", rule.tag().getTagName(), jgrybm);
                    }
                    break;

                case CHECK:
                    // 检查场景：仅记录条件匹配结果，不执行标签操作
                    log.info("检查标签场景：标签 {} 条件匹配结果: {} for jgrybm: {}",
                            rule.tag().getTagName(), matched, jgrybm);
                    break;

                default:
                    log.warn("未知的标签场景类型: {}", scenario);
                    break;
            }
        } catch (Exception e) {
            log.error("处理标签场景失败", e);
        }
    }

    private void processEntityTagsWithOldValue(Object newEntity, Object oldEntity, String businessId, String jgrybm,
            String businessType) {
        for (Field field : newEntity.getClass().getDeclaredFields()) {
            // 获取新旧值
            Object newValue = getFieldValue(newEntity, field);
            Object oldValue = getFieldValue(oldEntity, field);

            // 如果值发生变化，才处理标签
            if (!Objects.equals(newValue, oldValue)) {
                // 处理单个规则
                TagRule tagRule = field.getAnnotation(TagRule.class);
                if (tagRule != null) {
                    processTagRule(newEntity, field, tagRule, businessId, jgrybm, businessType);
                }

                // 处理规则组
                TagRuleGroup ruleGroup = field.getAnnotation(TagRuleGroup.class);
                if (ruleGroup != null) {
                    processTagRuleGroup(newEntity, field, ruleGroup, businessId, jgrybm, businessType);
                }
            }
        }
    }

    private Object getFieldValue(Object entity, Field field) {
        try {
            field.setAccessible(true);
            return field.get(entity);
        } catch (Exception e) {
            return null;
        }
    }

    private String getBusinessId(Object entity) {
        try {
            Field idField = getFieldFromClassHierarchy(entity.getClass(), "id");
            if (idField != null) {
                idField.setAccessible(true);
                return (String) idField.get(entity);
            }
        } catch (Exception e) {
            log.warn("获取businessId失败", e);
        }
        return null;
    }

    private String getJgrybm(Object entity) {
        try {
            Field jgrybmField = getFieldFromClassHierarchy(entity.getClass(), "jgrybm");
            if (jgrybmField != null) {
                jgrybmField.setAccessible(true);
                return (String) jgrybmField.get(entity);
            }
        } catch (Exception e) {
            log.warn("获取jgrybm失败", e);
        }
        return null;
    }

    /**
     * 获取更新前的实体数据
     */
    private Object getOldEntity(Object entity) {
        if (entity == null) {
            return null;
        }

        Class<?> entityClass = entity.getClass();

        // 1. 获取实体对应的Mapper
        BaseMapper<?> mapper = getMapper(entityClass);
        if (mapper == null) {
            return null;
        }

        // 2. 获取实体ID
        Object id = null;
        try {
            Field idField = getFieldFromClassHierarchy(entityClass, "id");
            if (idField != null) {
                idField.setAccessible(true);
                id = idField.get(entity);
            }
        } catch (Exception e) {
            log.warn("Failed to get entity id", e);
            return null;
        }

        if (id == null) {
            return null;
        }

        // 3. 查询原实体
        try {
            return mapper.selectById(id.toString());
        } catch (Exception e) {
            log.warn("Failed to query old entity", e);
            return null;
        }
    }

    /**
     * 获取实体对应的Mapper
     */
    private BaseMapper<?> getMapper(Class<?> entityClass) {
        // 1. 获取MapperClass注解
        MapperClass mapperClass = entityClass.getAnnotation(MapperClass.class);
        if (mapperClass == null) {
            log.warn("Entity {} has no @MapperClass annotation", entityClass.getName());
            return null;
        }

        // 2. 从Spring容器中获取Mapper实例
        try {
            return (BaseMapper<?>) SpringUtils.getBean(mapperClass.value());
        } catch (Exception e) {
            log.warn("Failed to get mapper for entity {}", entityClass.getName(), e);
            return null;
        }
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // 可以在这里设置一些属性
    }

    /**
     * 从参数对象中获取实体对象
     */
    private Object getEntityFromParameter(Object parameterObject) {
        if (parameterObject == null) {
            log.warn("parameterObject is null");
            return null;
        }

        // 如果是实体对象，直接返回
        if (parameterObject instanceof BaseDO) {
            return parameterObject;
        }

        // 如果是Map类型，尝试获取实体对象
        if (parameterObject instanceof Map) {
            Map<String, Object> paramMap = (Map<String, Object>) parameterObject;
            // 尝试获取实体对象
            for (Object value : paramMap.values()) {
                if (value instanceof BaseDO) {
                    return value;
                }
            }
            log.warn("No BaseDO found in parameter map");
            return null;
        }

        // 如果是Wrapper类型，尝试获取实体对象
        if (parameterObject instanceof Wrapper) {
            Wrapper<?> wrapper = (Wrapper<?>) parameterObject;
            try {
                // 获取Wrapper中的实体对象
                Field entityField = wrapper.getClass().getDeclaredField("entity");
                entityField.setAccessible(true);
                Object entity = entityField.get(wrapper);
                if (entity instanceof BaseDO) {
                    return entity;
                }
                log.warn("Entity in wrapper is not BaseDO type");
            } catch (Exception e) {
                log.warn("Failed to get entity from wrapper", e);
            }
        }

        log.warn("Unsupported parameter type: {}", parameterObject.getClass().getName());
        return null;
    }

    private void processExclusiveRules(List<TagRuleMatch> ruleMatches, String jgrybm, String businessType,
            String businessId) {
        if (ruleMatches == null || jgrybm == null || businessType == null || businessId == null) {
            return;
        }

        // 按优先级排序（数字越大优先级越高）
        ruleMatches.sort(Comparator.comparingInt(match -> -match.getRule().priority()));

        // 处理互斥规则
        Set<String> exclusiveGroups = new HashSet<>();
        for (TagRuleMatch match : ruleMatches) {
            if (match != null && match.isMatched() && match.getRule().exclusive()) {
                String exclusiveGroup = match.getRule().tag().getTagType();
                if (exclusiveGroup != null && !exclusiveGroups.contains(exclusiveGroup)) {
                    exclusiveGroups.add(exclusiveGroup);
                    // 添加标签
                    tagService.addTag(jgrybm, match.getRule().tag().getTagCode(),
                            match.getRule().tag().getTagType(), match.getRule().tag().getTagName(), businessType,
                            businessId);
                }
            }
        }
    }

    /**
     * 从类层次结构中获取指定名称的字段（包括父类）
     *
     * @param clazz 要搜索的类
     * @param fieldName 字段名称
     * @return 找到的字段，如果未找到则返回null
     */
    private Field getFieldFromClassHierarchy(Class<?> clazz, String fieldName) {
        Class<?> currentClass = clazz;
        while (currentClass != null) {
            try {
                return currentClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                // 在当前类中未找到，继续搜索父类
                currentClass = currentClass.getSuperclass();
            }
        }
        return null;
    }

    // 用于存储规则匹配结果的内部类
    private static class TagRuleMatch {
        private final TagRule rule;
        private final boolean matched;

        public TagRuleMatch(TagRule rule, boolean matched) {
            this.rule = rule;
            this.matched = matched;
        }

        public TagRule getRule() {
            return rule;
        }

        public boolean isMatched() {
            return matched;
        }
    }
}
