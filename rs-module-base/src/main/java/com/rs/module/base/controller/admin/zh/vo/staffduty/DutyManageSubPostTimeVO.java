package com.rs.module.base.controller.admin.zh.vo.staffduty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel(value = "值班时间列表")
public class DutyManageSubPostTimeVO {
    @ApiModelProperty(value = "值班时间")
    private String time;

    @ApiModelProperty(value = "值班班次")
    private String dutyShift;

    @ApiModelProperty(value = "值班角色key 不能为空")
    private String postKey;

    private String policeName;

    @ApiModelProperty(value = "用户id")
    private Integer userId;

    private String policeType;

    private String photo;
}
