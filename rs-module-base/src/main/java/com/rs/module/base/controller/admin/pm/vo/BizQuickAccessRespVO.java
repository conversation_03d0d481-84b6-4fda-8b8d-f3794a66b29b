package com.rs.module.base.controller.admin.pm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 实战平台-监管管理-业务快捷访问入口配置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BizQuickAccessRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("业务入口名称")
    private String name;
    @ApiModelProperty("业务入口标识")
    private String mark;
    @ApiModelProperty("业务分类id")
    private String typeId;
    @ApiModelProperty("业务分类标识")
    private String typeMark;
    @ApiModelProperty("访问地址")
    private String accessUrl;
    @ApiModelProperty("触发方式")
    private String triggerMethod;
    @ApiModelProperty("图标地址")
    private String iconUrl;
    @ApiModelProperty("弹窗名称")
    private String popName;
    @ApiModelProperty("是否启动浏览权限配置")
    private Short isEnableBrowsingPermission;
    @ApiModelProperty("是否启用")
    private Short isEnable;
    @ApiModelProperty("排序Id")
    private Integer orderId;
    @ApiModelProperty("备注")
    private String remark;
}
