package com.rs.module.base.controller.admin.pm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.BizQuickAccessTypeRespVO;
import com.rs.module.base.controller.admin.pm.vo.BizQuickAccessTypeSaveReqVO;
import com.rs.module.base.entity.pm.BizQuickAccessDO;
import com.rs.module.base.entity.pm.BizQuickAccessTypeDO;
import com.rs.module.base.service.pm.BizQuickAccessService;
import com.rs.module.base.service.pm.BizQuickAccessTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

import static com.rs.framework.common.pojo.CommonResult.success;


@Api(tags = "业务分类管理")
@RestController
@RequestMapping("/acp/pm/bizQuickAccessType")
@Validated
public class BizQuickAccessTypeController {

    @Resource
    private BizQuickAccessTypeService bizQuickAccessTypeService;
    @Resource
    private BizQuickAccessService bizQuickAccessService;

    @PostMapping("/save")
    @ApiOperation(value = "创建业务分类")
    public CommonResult<String> createBizQuickAccessType(@Valid @RequestBody BizQuickAccessTypeSaveReqVO createReqVO) {
        if (StringUtil.isNullBlank(createReqVO.getId())) {
            bizQuickAccessTypeService.createBizQuickAccessType(createReqVO);
        } else {
            bizQuickAccessTypeService.updateBizQuickAccessType(createReqVO);
        }
        return success("", "保存成功");
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除业务分类")
    @ApiImplicitParam(name = "ids", value = "编号")
    @Transactional
    public CommonResult deleteBizQuickAccessType(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            BizQuickAccessDO bizQuickAccessDO = bizQuickAccessService.getOne(new LambdaQueryWrapper<BizQuickAccessDO>()
                    .eq(BizQuickAccessDO::getTypeId, id), false);
            if(Objects.nonNull(bizQuickAccessDO)){
                throw new ServerException("业务分类被引用不能删除");
            }
            bizQuickAccessTypeService.deleteBizQuickAccessType(id);
        }
        return success("", "删除成功");
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得业务分类")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<BizQuickAccessTypeRespVO> getBizQuickAccessType(@RequestParam("id") String id) {
        BizQuickAccessTypeDO bizQuickAccessType = bizQuickAccessTypeService.getBizQuickAccessType(id);
        return success(BeanUtils.toBean(bizQuickAccessType, BizQuickAccessTypeRespVO.class));
    }

    @ApiOperation(value = "获取所有业务分类")
    @PostMapping(value = "/getAll")
    public CommonResult getAll() {
        List<BizQuickAccessTypeDO> list = bizQuickAccessTypeService.list(new LambdaQueryWrapper<BizQuickAccessTypeDO>()
                .select(BizQuickAccessTypeDO::getId, BizQuickAccessTypeDO::getName, BizQuickAccessTypeDO::getMark)
                .orderByDesc(BizQuickAccessTypeDO::getOrderId));
        return success(list);
    }

}
