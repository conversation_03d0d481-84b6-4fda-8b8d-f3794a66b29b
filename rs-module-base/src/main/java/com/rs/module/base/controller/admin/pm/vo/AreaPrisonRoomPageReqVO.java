package com.rs.module.base.controller.admin.pm.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-监管管理-区域监室分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AreaPrisonRoomPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("是否删除(0:否,1:是)")
    private Integer isDel;

    @ApiModelProperty("添加时间")
    private Date[] addTime;

    @ApiModelProperty("添加人")
    private String addUser;

    @ApiModelProperty("更新人")
    private String updateUser;

    @ApiModelProperty("所属省级代码")
    private String proCode;

    @ApiModelProperty("所属省级名称")
    private String proName;

    @ApiModelProperty("所属市级代码")
    private String cityCode;

    @ApiModelProperty("所属市级名称")
    private String cityName;

    @ApiModelProperty("区域代码")
    private String regCode;

    @ApiModelProperty("区域名称")
    private String regName;

    @ApiModelProperty("机构编号")
    private String orgCode;

    @ApiModelProperty("机构名称")
    private String orgName;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("是否启用:1启用,0停用")
    private String status;

    @ApiModelProperty("关押量")
    private Integer imprisonmentAmount;

    @ApiModelProperty("所属中队")
    private String squadronId;

    @ApiModelProperty("排序")
    private Integer orderId;

    @ApiModelProperty("监室编号")
    private String roomCode;

    @ApiModelProperty("监室类型")
    private String roomType;

    @ApiModelProperty("人员性别")
    private String roomSex;

    @ApiModelProperty("设计关押量")
    private Integer planImprisonmentAmount;

    @ApiModelProperty("监区id")
    private String areaId;

    @ApiModelProperty("监区名称")
    private String areaName;

    @ApiModelProperty("监室面积")
    private BigDecimal roomArea;

    @ApiModelProperty("人均铺位面积")
    private BigDecimal avgBedsArea;

    @ApiModelProperty("是否一级风险")
    private String isLevelRisk;

    @ApiModelProperty("上游区域ID")
    private String selfAreaId;

    @ApiModelProperty("风险等级")
    private String fxdj;

    @ApiModelProperty("预警时间")
    private Date yjsj;

    @ApiModelProperty("自动循环床位开关 1-打开")
    private Integer autoFlag;

    @ApiModelProperty("监室等级 字典：ROOM_LEVEL")
    private String roomLevel;

    @ApiModelProperty("排序属性")
    List<OrderItem> orderFields;

    @ApiModelProperty("是否主协管监室")
    private Boolean mainAssistantManager;

    @ApiModelProperty("排除空监室")
    private Boolean airMonitoringRoom;

    @ApiModelProperty("排除个人监室")
    private Boolean singleCellRoom;

    @ApiModelProperty("排除过渡监室")
    private Boolean transitionRoom;

    @ApiModelProperty("排除同案监室")
    private Boolean sameCase;

    @ApiModelProperty("排除同户籍")
    private Boolean excludeHouseholdRegistration;

    @ApiModelProperty("排除同居住地的")
    private Boolean excludeHomePlace;

    @ApiModelProperty("排除历史同监室的")
    private Boolean excludeHistoryRoom;

    @ApiModelProperty("排除同案件类型")
    private Boolean excludeCaseType;

    @ApiModelProperty("排除不同性别科室")
    private Boolean excludeSex;

    @ApiModelProperty("排除关押人户籍在指定地区的监室-多个地区用逗号隔开")
    private String excludeAreaRoom;

    @ApiModelProperty("监室号ID数组")
    private List<String> roomCodes;

    @ApiModelProperty("监室号ID数组-查询列表排除该数组列表")
    private List<String> notRoomCodes;

    @ApiModelProperty("监区id数组")
    private List<String> areaIds;

    @ApiModelProperty("被监管人员编码 数组传参 （用于排除-是否同户籍、同居住地、历史同监室、同案件类型、以及不同性别）")
    private List<String> jgrybmList;

    @ApiModelProperty(value = "关联监室号SQL",hidden = true)
    private String jshSql;

    @ApiModelProperty(value = "sql查询判断",hidden = true)
    private Boolean isSql;

    @ApiModelProperty(value = "App调用",hidden = true)
    private Boolean reqApp;

    @ApiModelProperty(value = "排除不同性别监室",hidden = true)
    private List<String> excludeSexList;

}
