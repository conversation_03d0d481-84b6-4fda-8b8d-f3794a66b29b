package com.rs.module.base.controller.admin.pm.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 自定义应用管理新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CusAppSaveReqVO extends BaseVO{

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("应用名称")
    private String yymc;

    @ApiModelProperty("链接地址")
    private String ljdz;

    @ApiModelProperty("应用分类ID")
    private String flId;

    @ApiModelProperty("是否内部应用(0:否1:是)")
    private String sfnb;

    @ApiModelProperty("是否公共应用(0:否1:是)")
    private String sfgg;

    @ApiModelProperty("应用类型(0:pc,1:移动端)")
    private String yylx;

    @ApiModelProperty("是否禁用(0:否1:是)")
    private String sfjy;

    @ApiModelProperty("排序")
    private Integer orderId;

    @ApiModelProperty("应用介绍")
    private String yyjs;

    @ApiModelProperty("应用图标")
    private String yytb;

    @ApiModelProperty("模型标识")
    private String modelMark;
}
