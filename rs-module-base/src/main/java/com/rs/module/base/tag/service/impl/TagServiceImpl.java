package com.rs.module.base.tag.service.impl;

import com.rs.module.base.entity.pm.PrisonerTagDO;
import com.rs.module.base.service.pm.PrisonerTagService;
import com.rs.module.base.tag.service.TagService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class TagServiceImpl implements TagService {


    @Autowired
    private PrisonerTagService prisonerTagService;


    @Override
    public void addTag(String jgrybm, String tagCode, String tagType,String tagName, String businessType,String businessId) {
        // 1. 检查标签是否已存在
        PrisonerTagDO tagDO = prisonerTagService.lambdaQuery().eq(PrisonerTagDO::getJgrybm, jgrybm)
                .eq(PrisonerTagDO::getTagCode, tagCode)
                .eq(PrisonerTagDO::getTagType, tagType)
                .eq(PrisonerTagDO::getBusinessType, businessType)
                .one();
        if (tagDO != null) {
            return;
        }

        // 2. 获取标签信息
        PrisonerTagDO prisonerTagDO = new PrisonerTagDO();
        prisonerTagDO.setJgrybm(jgrybm);
        prisonerTagDO.setTagCode(tagCode);
        prisonerTagDO.setTagType(tagType);
        prisonerTagDO.setTagName(tagName);
        prisonerTagDO.setBusinessType(businessType);
        prisonerTagDO.setBusinessId(businessId);
        prisonerTagService.save(prisonerTagDO);

    }

    @Override
    public void removeTag(String jgrybm, String tagCode, String tagType) {
        // 1. 删除标签关联
        prisonerTagService.lambdaUpdate().set(PrisonerTagDO::getIsDel, 1)
                .eq(PrisonerTagDO::getJgrybm, jgrybm)
                .eq(PrisonerTagDO::getTagCode, tagCode)
                .eq(PrisonerTagDO::getTagType, tagType)
                .update();

    }

    @Override
    public void removeAllTags(String businessId, String businessType) {
        // 1. 删除所有标签关联
        prisonerTagService.lambdaUpdate().set(PrisonerTagDO::getIsDel, 1)
                .eq(PrisonerTagDO::getBusinessId, businessId)
                .eq(PrisonerTagDO::getTagType, businessType)
                .update();
    }

    @Override
    public List<PrisonerTagDO> getEntityTags(String jgrybm) {

        return prisonerTagService.lambdaQuery().eq(PrisonerTagDO::getJgrybm, jgrybm)
                .list();
    }

}
