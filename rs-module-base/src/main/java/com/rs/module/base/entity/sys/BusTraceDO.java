package com.rs.module.base.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO_;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 实战平台-业务轨迹 DO
 *
 * <AUTHOR>
 */
@TableName("acp_sys_bus_trace")
@KeySequence("acp_sys_bus_trace_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusTraceDO extends BaseDO_ {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * ip地址
     */
    private String ip;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 证件号码
     */
    private String zjhm;
    /**
     * 操作用户
     */
    private String operateUser;
    /**
     * 业务类型
     */
    private String busType;
    /**
     * 业务名称
     */
    private String busName;
    /**
     * 业务内容
     */
    private String content;
    /**
     * 业务主键
     */
    private String businessId;
    /**
     * 监室Id
     */
    private String roomId;
}
