package com.rs.module.base.service.ry.fill;

import com.rs.module.base.controller.admin.pm.vo.PrisonerTagRespVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.ry.fill.handle.base.RyxxFillHandler;
import com.rs.module.base.service.ry.fill.handle.enums.RyxxFillBusinessType;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Log4j2
@Service
public class RxxxFillService {

    @Autowired
    private RyxxHandlerFactory ryxxHandlerFactory;

    public void fill(PrisonerVwRespVO prisonerVwRespVO) {
        List<PrisonerTagRespVO> tags = prisonerVwRespVO.getTags();
        if (tags == null) {
            prisonerVwRespVO.setTags(new ArrayList<>());
        }
        for (RyxxFillBusinessType value : RyxxFillBusinessType.values()) {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String url = request.getRequestURI().toString();
                // 使用 url 进行后续处理
                if (url.equals(value.getCode())) {
                    // 获取处理器并处理
                    List<RyxxFillHandler> handlerList = ryxxHandlerFactory.getHandler(value);
                    for (RyxxFillHandler ryxxFillHandler : handlerList) {
                        ryxxFillHandler.handle(prisonerVwRespVO);
                    }
                }
            }
        }

    }


}
