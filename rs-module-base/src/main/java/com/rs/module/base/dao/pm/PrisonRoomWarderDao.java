package com.rs.module.base.dao.pm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderListReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderPageReqVO;
import com.rs.module.base.entity.pm.PrisonRoomWarderDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 实战平台-监管管理-监室主协管人员 Dao
*
* <AUTHOR>
*/
@Mapper
public interface PrisonRoomWarderDao extends IBaseDao<PrisonRoomWarderDO> {


    default PageResult<PrisonRoomWarderDO> selectPage(PrisonRoomWarderPageReqVO reqVO) {
        Page<PrisonRoomWarderDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<PrisonRoomWarderDO> wrapper = new LambdaQueryWrapperX<PrisonRoomWarderDO>()
            .eqIfPresent(PrisonRoomWarderDO::getPoliceId, reqVO.getPoliceId())
            .likeIfPresent(PrisonRoomWarderDO::getPoliceName, reqVO.getPoliceName())
            .eqIfPresent(PrisonRoomWarderDO::getUserType, reqVO.getUserType())
            .eqIfPresent(PrisonRoomWarderDO::getRoomId, reqVO.getRoomId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(PrisonRoomWarderDO::getAddTime);
        }
        Page<PrisonRoomWarderDO> prisonRoomWarderPage = selectPage(page, wrapper);
        return new PageResult<>(prisonRoomWarderPage.getRecords(), prisonRoomWarderPage.getTotal());
    }
    default List<PrisonRoomWarderDO> selectList(PrisonRoomWarderListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrisonRoomWarderDO>()
            .eqIfPresent(PrisonRoomWarderDO::getPoliceId, reqVO.getPoliceId())
            .eqIfPresent(PrisonRoomWarderDO::getPoliceSfzh, reqVO.getPoliceSfzh())
            .likeIfPresent(PrisonRoomWarderDO::getPoliceName, reqVO.getPoliceName())
            .eqIfPresent(PrisonRoomWarderDO::getUserType, reqVO.getUserType())
            .eqIfPresent(PrisonRoomWarderDO::getRoomId, reqVO.getRoomId())
        .orderByDesc(PrisonRoomWarderDO::getAddTime));
    }


    void deletePrisonRoomWarderByRoomId(String roomId);
}
