package com.rs.module.base.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "审批 Request VO")
@Data
public class ApproveReqVO {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date approverTime;

    @ApiModelProperty("审批结果 0 不同意,1 同意")
    private String approvalResult;

    @ApiModelProperty("领导签名")
    private String approvalAutograph;

    @ApiModelProperty("领导签名日期")
    private Date approvalAutographTime;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;
    @ApiModelProperty(name="流程定义key", required = false,hidden = true)
    private String defKey;
}
