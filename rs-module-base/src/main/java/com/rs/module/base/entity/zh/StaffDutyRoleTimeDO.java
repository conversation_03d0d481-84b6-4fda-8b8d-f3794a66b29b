package com.rs.module.base.entity.zh;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalTime;

@TableName("acp_zh_staff_duty_role_time")
@KeySequence("acp_zh_staff_duty_role_time_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_staff_duty_role_time")
public class StaffDutyRoleTimeDO extends BaseDO {
    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 关联值班角色表id
     */
    private String dutyRoleId;
    /**
     * 值班班次
     */
    private String dutyShift;
    /**
     * 值班时间类型-开始 1-当日 2-次日
     */
    private Integer dutyTimeTypeBegin;
    /**
     * 值班时间类型-结束  1-当日 2-次日
     */
    private Integer dutyTimeTypeEnd;
    /**
     * 值班时间开始
     */
    private String dutyTimeBegin;
    /**
     * 值班时间结束
     */
    private String dutyTimeEnd;
    /**
     * 岗位key
     */
    private String postKey;
}
