package com.rs.module.base.dao.pm;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.base.controller.admin.pm.vo.GetPoliceWarderInfoVO;
import com.rs.module.base.controller.admin.pm.vo.GetRoomPoliceWarderInfoVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwPageReqVO;
import com.rs.module.base.entity.pm.PrisonerInDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 在所人员 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface PrisonerInDao extends IBaseDao<PrisonerInDO> {


    default PageResult<PrisonerInDO> selectPage(PrisonerVwPageReqVO reqVO) {
        LambdaQueryWrapperX<PrisonerInDO> queryWrapper = new LambdaQueryWrapperX<PrisonerInDO>()
                .betweenIfPresent(PrisonerInDO::getAddTime, reqVO.getAddTime())
                .betweenIfPresent(PrisonerInDO::getRssj, reqVO.getRssj())
                .likeIfPresent(PrisonerInDO::getXm, reqVO.getXm())
                .eqIfPresent(PrisonerInDO::getAreaId, reqVO.getAreaId())
                .inIfPresent(PrisonerInDO::getOrgCode, reqVO.getOrgCode() == null ? null : reqVO.getOrgCode().split(","))
                .eqIfPresent(PrisonerInDO::getJsh, reqVO.getJsh())
                .eqIfPresent(PrisonerInDO::getBjgrylx, reqVO.getBjgrylx())
                .orderByDesc(PrisonerInDO::getRssj);

        // 特殊：不分页，直接查询全部
        if (reqVO.isAll()) {
            List<PrisonerInDO> list = selectList(queryWrapper);
            return new PageResult<>(list, (long) list.size());
        }
        Page<PrisonerInDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<PrisonerInDO> prisonerInVwPage = selectPage(page, queryWrapper);
        return new PageResult<>(prisonerInVwPage.getRecords(), prisonerInVwPage.getTotal());
    }

    default List<PrisonerInDO> getByJsh(String orgCode, String jsh) {
        return selectList(new LambdaQueryWrapperX<PrisonerInDO>()
                .eq(PrisonerInDO::getOrgCode, orgCode)
                .eq(PrisonerInDO::getJsh, jsh));
    }


    /**
     * 根据监管人员编号集合查询管教人员信息
     *
     * @param supervisedUserCodeList 监管人员编号集合
     * @return 管教人员信息
     */
    List<GetPoliceWarderInfoVO> getPoliceWarderInfo(@Param("supervisedUserCodeList") List<String> supervisedUserCodeList);


    /**
     * 根据监管人员编号集合查询有械具的人员
     *
     * @param supervisedUserCodeList 监管人员编号集合
     */
    Set<String> getHavePunishmentToolUse(@Param("supervisedUserCodeList") Collection<String> supervisedUserCodeList);

    /**
     * 根据监室号集合查询管教人员信息
     *
     * @param roomIds 监室号集合
     * @return 管教人员信息
     */
    List<GetRoomPoliceWarderInfoVO> getRoomPoliceWarderInfo(@Param("roomIds") List<String> roomIds);

    /**
     * 修改看守所人员监室号
     * @param orgCode
     * @param roomId
     * @param jgrybm
     * @param cwh
     * @return
     */
    @Select("UPDATE acp_pm_prisoner_kss_in SET cwh = #{cwh} WHERE org_code = #{orgCode} AND jsh = #{roomId} AND jgrybm = #{jgrybm}")
    void updateKssCwh(@Param("orgCode") String orgCode, @Param("roomId") String roomId, @Param("jgrybm") String jgrybm, @Param("cwh") String cwh);

    /**
     * 修改拘留所人员监室号
     * @param orgCode
     * @param roomId
     * @param jgrybm
     * @param cwh
     * @return
     */
    @Select("UPDATE acp_pm_prisoner_jls_in SET cwh = #{cwh} WHERE org_code = #{orgCode} AND jsh = #{roomId} AND jgrybm = #{jgrybm}")
    void updateJlsCwh(@Param("orgCode") String orgCode, @Param("roomId") String roomId, @Param("jgrybm") String jgrybm, @Param("cwh") String cwh);

    /**
     * 修改戒毒所人员监室号
     * @param orgCode
     * @param roomId
     * @param jgrybm
     * @param cwh
     * @return
     */
    @Select("UPDATE acp_pm_prisoner_jds_in SET cwh = #{cwh} WHERE org_code = #{orgCode} AND jsh = #{roomId} AND jgrybm = #{jgrybm}")
    void updateJdsCwh(@Param("orgCode") String orgCode, @Param("roomId") String roomId, @Param("jgrybm") String jgrybm, @Param("cwh") String cwh);

    /**
     * 修改戒毒所人员监室号
     * @param orgCode
     * @param roomId
     * @return
     */
    List<PrisonerInDO> getNotPlanCwh(@Param("orgCode") String orgCode, @Param("roomId") String roomId);

    /**
     * 获取当前监室中的重病号人员
     * @param orgCode
     * @param roomId
     * @return
     */
    List<PrisonerInDO> getSickByRoom(@Param("orgCode") String orgCode, @Param("roomId") String roomId);

    /**
     * TODO
     * 获取当前监室中的单独关押人员-未完成-待业务完善
     * @param orgCode
     * @param roomId
     * @return
     */
    List<PrisonerInDO> getAloneByRoom(@Param("orgCode") String orgCode, @Param("roomId") String roomId);

    /**
     * TODO
     * 获取当前监室中的禁闭人员-未完成-待业务完善
     * @param orgCode
     * @param roomId
     * @return
     */
    List<PrisonerInDO> getConfinementByRoom(@Param("orgCode") String orgCode, @Param("roomId") String roomId);

    /**
     * 获取当前监室中的戴戒具人员
     * @param orgCode
     * @param roomId
     * @return
     */
    List<PrisonerInDO> getEquipmentByRoom(@Param("orgCode") String orgCode, @Param("roomId") String roomId);

    /**
     * 获取监室中的外出类型的人员数量
     * @param orgCode
     * @param roomId
     * @return
     */
    List<Map<String, Object>> getOutPrisonerByRoom(@Param("orgCode") String orgCode, @Param("roomId") String roomId);

    /**
     * 获取监室中的所外就医的人员数量
     * @param orgCode
     * @return
     */
    Integer getOutTreatmentByOrg(@Param("orgCode") String orgCode, @Param("roomId") String roomId);

    /**
     * 判断人员是否为重病号
     * @param jgrybm
     * @return
     */
    Boolean isSickByJgrybm(@Param("jgrybm") String jgrybm);


}

