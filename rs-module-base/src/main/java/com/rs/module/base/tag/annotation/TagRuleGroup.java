package com.rs.module.base.tag.annotation;

import com.rs.module.base.tag.enums.RuleCombineType;

import java.lang.annotation.*;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TagRuleGroup {
    // 规则组名称
    String name() default "";

    // 规则组描述
    String description() default "";

    // 规则组中的规则
    TagRule[] rules();

    // 规则组合方式：AND/OR
    RuleCombineType combineType() default RuleCombineType.AND;
}
