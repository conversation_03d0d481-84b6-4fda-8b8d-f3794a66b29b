package com.rs.module.base.entity.sys;

import com.bsp.sdk.msg.model.ReceiveUser;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MsgAddVO {

    @ApiModelProperty("消息跳转链接")
    private String url;

    @ApiModelProperty("批次id手动生成-后面根据此Id来，修改待办状态")
    private String pcid;

    @ApiModelProperty("消息标题，可不传，不传就默认使用模版配置的标题")
    private String title;

    @ApiModelProperty("消息类型代码 字典编码对应：ZD_MSG_BUSTYPE")
    private String msgType;

    @ApiModelProperty("模块编码 必传")
    private String moduleCode;

    @ApiModelProperty("监所id(模板所属监所id)")
    private String orgCode;

    @ApiModelProperty("监所名称")
    private String orgName;

    @ApiModelProperty("业务主键id")
    private String businessId;

    @ApiModelProperty("按被监管人员的主协管民警进行过滤。默认自动填充 roomName和prisonerName字段")
    private String jgrybm;

    @ApiModelProperty("发送给具体监所的id，不传递以模板的监所id为准，多个用,分割")
    private String toOrgCode;

    @ApiModelProperty("来源编码 可不传 默认pc")
    private String source;

    @ApiModelProperty("消息内容数据")
    private Map<String, Object> contentData;

    @ApiModelProperty("系统标识 可不传 默认为 acp ")
    private String systemMark;

    @ApiModelProperty("系统标识名称 可不传 默认为 实战平台 ")
    private String systemMarkName;

    @ApiModelProperty("扩展参数")
    private String extendData;

    @ApiModelProperty("是否指定")
    private boolean isSpecify = false;

    @ApiModelProperty("指定接收用户")
    private List<ReceiveUser> specifyReceiveUserList;
}
