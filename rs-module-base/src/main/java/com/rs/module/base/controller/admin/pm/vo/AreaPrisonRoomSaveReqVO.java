package com.rs.module.base.controller.admin.pm.vo;

import com.rs.module.base.entity.pm.PrisonRoomWarderDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-监管管理-区域监室新增/修改 Request VO")
@Data
public class AreaPrisonRoomSaveReqVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("是否删除(0:否,1:是)")
    private Integer isDel;

    @ApiModelProperty("添加时间")
    private Date addTime;

    @ApiModelProperty("添加人")
    private String addUser;

    @ApiModelProperty("更新人")
    private String updateUser;

    @ApiModelProperty("所属省级代码")
    private String proCode;

    @ApiModelProperty("所属省级名称")
    private String proName;

    @ApiModelProperty("所属市级代码")
    private String cityCode;

    @ApiModelProperty("所属市级名称")
    private String cityName;

    @ApiModelProperty("区域代码")
    private String regCode;

    @ApiModelProperty("区域名称")
    private String regName;

    @ApiModelProperty("机构编号")
    private String orgCode;

    @ApiModelProperty("机构名称")
    private String orgName;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("是否启用:1启用,0停用")
    private String status;

    @ApiModelProperty("关押量")
    private Integer imprisonmentAmount;

    @ApiModelProperty("所属中队")
    private String squadronId;

    @ApiModelProperty("排序")
    private Integer orderId;

    @ApiModelProperty("监室编号")
    private String roomCode;

    @ApiModelProperty("监室类型")
    private String roomType;

    @ApiModelProperty("人员性别")
    private String roomSex;

    @ApiModelProperty("设计关押量")
    private Integer planImprisonmentAmount;

    @ApiModelProperty("监区id")
    private String areaId;

    @ApiModelProperty("监区名称")
    private String areaName;

    @ApiModelProperty("监室面积")
    private BigDecimal roomArea;

    @ApiModelProperty("人均铺位面积")
    private BigDecimal avgBedsArea;

    @ApiModelProperty("是否一级风险")
    private String isLevelRisk;

    @ApiModelProperty("上游区域ID")
    private String selfAreaId;

    @ApiModelProperty("风险等级")
    private String fxdj;

    @ApiModelProperty("预警时间")
    private Date yjsj;

    @ApiModelProperty("自动循环床位开关 1-打开")
    private Integer autoFlag;

    @ApiModelProperty("监室等级 字典：ROOM_LEVEL")
    private String roomLevel;

    @ApiModelProperty("主管民警")
    private List<PrisonRoomWarderDO> sponsorList;

    @ApiModelProperty("协管民警")
    private List<PrisonRoomWarderDO> assistList;

    @ApiModelProperty("主协管信息")
    private List<AreaRelatedWarderReqVO> areaRelatedWarderReqVOList;

}
