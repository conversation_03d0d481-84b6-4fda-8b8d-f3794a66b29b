package com.rs.module.base.enums;

/**
 * 设备状态
 */
public enum DeviceStatusEnum {

    ONLINE("001",	"在线"),
    OFFLINE("002",	"离线"),
    MALFUNCTION("003",	"故障");
    private final String code;
    private final String name;

    DeviceStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static DeviceStatusEnum getByCode(String code) {
        for (DeviceStatusEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据名称获取code
     * @param name 区域类型名称
     * @return 对应的code，如果找不到则返回null
     */
    public static String getCodeByName(String name) {
        for (DeviceStatusEnum type : values()) {
            if (type.name.equals(name)) {
                return type.code;
            }
        }
        return null;
    }

} 