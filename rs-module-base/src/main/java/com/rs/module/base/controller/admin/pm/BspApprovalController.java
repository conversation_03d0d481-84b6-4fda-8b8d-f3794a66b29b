package com.rs.module.base.controller.admin.pm;

import com.alibaba.fastjson.JSONObject;
import com.rs.adapter.bsp.api.BpmApi;
import com.rs.framework.common.pojo.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "bsp - 流程审批")
@RestController
@RequestMapping("/base/bsp/approve")
@Validated
public class BspApprovalController {

    @Resource
    private BpmApi bpmApi;

    @GetMapping("/approveTrack")
    @ApiOperation(value = "获取审批轨迹")
    @ApiImplicitParam(name = "actInstId", value = "ACT流程实例Id")
    public CommonResult<List<JSONObject>> approveTrack(@RequestParam("actInstId") String actInstId) {
        JSONObject result = bpmApi.approveTrack(actInstId);
        
        // 获取审批轨迹
        List<JSONObject> approveTrack = JSONObject.parseArray(JSONObject.toJSONString(result.get("data")), JSONObject.class);
        return success(approveTrack);
    }

    @GetMapping("/getApproveUser")
    @ApiOperation(value = "获取下一级审批用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "actInstId", value = "ACT流程实例Id"),
            @ApiImplicitParam(name = "userId", value = "执行用户Id")
    })
    public CommonResult<List<JSONObject>> getApproveUser(@RequestParam("actInstId") String actInstId,
                                                         @RequestParam("userId") String userId,
                                                         @RequestParam(value = "formProcessVar", required = false) String formProcessVar
    ) {
        JSONObject result = bpmApi.getApproveUser(actInstId, userId, formProcessVar);
        // 获取审批用户
        List<JSONObject> approveUser = JSONObject.parseArray(JSONObject.toJSONString(result.get("data")), JSONObject.class);
        return success(approveUser);
    }

    @GetMapping("/isFinishProcinst")
    @ApiOperation(value = "判断流程是否结束")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "actInstId", value = "ACT流程实例Id"),
    })
    public CommonResult<Boolean> isFinishProcinst(@RequestParam("actInstId") String actInstId) {
        Boolean finishProcinst = bpmApi.isFinishProcinst(actInstId);
        return success(finishProcinst);
    }

    @GetMapping("/checkIsApproveAuthority")
    @ApiOperation(value = "判断用户是否具有权限审批")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "taskId", value = "流程任务ID"),
            @ApiImplicitParam(name = "userId", value = "执行用户身份证号")
    })
    public CommonResult<Boolean> checkIsApproveAuthority(@RequestParam("taskId") String taskId, @RequestParam("userId") String userId) {
        Boolean isApproveAuthority = bpmApi.checkIsApproveAuthority(taskId, userId);
        return success(isApproveAuthority);
    }

}
