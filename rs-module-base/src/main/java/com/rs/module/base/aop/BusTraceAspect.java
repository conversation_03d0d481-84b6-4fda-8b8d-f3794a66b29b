package com.rs.module.base.aop;

import java.lang.reflect.Method;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import com.bsp.common.util.R;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.google.common.base.Strings;
import com.mzt.bsp.logapi.service.IFunctionService;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.http.IpUtils;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.sys.BusTraceDO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.service.sys.BusTraceService;

import cn.hutool.core.date.DateUtil;
import lombok.RequiredArgsConstructor;

/**
 * 业务轨迹切面类
 * <AUTHOR>
 * @date 2025年5月26日
 */
@RequiredArgsConstructor
@Aspect
@Component
public class BusTraceAspect {
	
	@Resource
    private BusTraceService busTraceService;
	
	@Resource
	private IFunctionService functionService;
	
	@Resource
	private PrisonerService prisonerService;
	
	private final ExpressionParser parser = new SpelExpressionParser();
	private final String spElFlag = "#";
	private static Pattern pattern = Pattern.compile("\\{\\s*(\\w*)\\s*\\{(.*?)}}");

	@Pointcut("@annotation(com.rs.module.base.annotation.BusTrace)")
	public void busTracePointcut() {}
	
	/**
	 * 环绕拦截
	 * @param joinPoint ProceedingJoinPoint 环绕通知
	 * @param request HttpServletRequest http请求
	 * @return Object
	 * @throws Throwable
	 */
	@Around("busTracePointcut()")
	public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
		Object proceed = joinPoint.proceed();
		if(proceed instanceof CommonResult) {
			if(((CommonResult<?>)proceed).isSuccess()) {
				recordTrace(joinPoint, proceed);
			}
		}
		else if(proceed instanceof R) {
			if(((R)proceed).ckSuccess()) {
				recordTrace(joinPoint, proceed);
			}
		}
		return proceed;
	}
	
	/**
	 * 记录轨迹
	 * @param joinPoint ProceedingJoinPoint 环绕通知
	 * @param request HttpServletRequest http请求
	 * @param result Object 响应结果
	 */
	private void recordTrace(ProceedingJoinPoint joinPoint, Object result) {
		MethodSignature signature = (MethodSignature)joinPoint.getSignature();
		Method method = signature.getMethod();
		
		//业务轨迹注解
		BusTrace busTrace = method.getAnnotation(BusTrace.class);
		
		//获取方法入参
		LinkedHashMap<String, Object> params = resolveParams(joinPoint);
		
		//变量上下文
		StandardEvaluationContext context = new StandardEvaluationContext();
		
		//当参数只有一个时，设置根对象，可使用#root.id 来获取对象的id
		if(params.size() == 1) {
			params.forEach((k, v) -> context.setRootObject(v));
		}
		
		//上下文中绑定变量
		int i = 0;
		for(Map.Entry<String, Object> entry : params.entrySet()) {
			context.setVariable(entry.getKey(), entry.getValue());
			
			//设置参数别名，可使用#a0或#p0来获取第一个入参
			context.setVariable("a" + i, entry.getValue());
			context.setVariable("p" + i, entry.getValue());
			i ++;
		}
		
		//执行结果变量
		context.setVariable("result", result);
		
		//不满足记录条件直接返回
		String matchCondition = resolveValue(busTrace.condition(), context);
		if(!StringUtil.getBoolean(matchCondition)) {
			return;
		}
		
		try {
			//注册日期转换函数
			Method formatDateTimeMethod = DateUtil.class.getDeclaredMethod("formatDateTime", Date.class);
			Method formatDateMethod = DateUtil.class.getDeclaredMethod("formatDate", Date.class);
			context.registerFunction("fDateTime", formatDateTimeMethod);
			context.registerFunction("fDate", formatDateMethod);
		}
		catch(Exception e) {}		
		
		//记录业务轨迹
		BusTraceDO trace = new BusTraceDO();
		trace.setBusName(busTrace.busType().getBusName());
		trace.setBusType(busTrace.busType().getBusType());		
		trace.setContent(resolveValueByTemplate(busTrace.content(), context));		//busTrace.content()
		trace.setJgrybm(resolveValueByTemplate(busTrace.jgrybm(), context));
		trace.setOrgCode(busTrace.orgCode());
		
		//业务主键
		trace.setBusinessId(resolveValue(busTrace.businessId(), context));
		
		//监室Id
		trace.setRoomId(resolveValue(busTrace.roomId(), context));
		
		//会话用户
		SessionUser user = SessionUserUtil.getSessionUser();
		
		//设置监所信息
		if(StringUtil.isEmpty(busTrace.orgCode()) && user != null) {
			trace.setOrgCode(user.getOrgCode());
		}
		
		//设置用户信息
		if(StringUtil.isEmpty(busTrace.operateUser()) && user != null) {
			trace.setOperateUser(user.getIdCard());
		}
		
		//设置证件号码
		if(StringUtil.isNotEmpty(busTrace.jgrybm())) {
			PrisonerVwRespVO prisoner = prisonerService.getPrisonerByJgrybm(trace.getJgrybm());
			if(prisoner != null) {
				trace.setZjhm(prisoner.getZjhm());
			}
		}
		
		//设置请求ip地址
		trace.setIp(IpUtils.getClientIp());
		
		busTraceService.save(trace);
	}
	
	/**
	 * 绑定请求参数到Map中
	 * @param joinPoint ProceedingJoinPoint 环绕通知
	 * @return LinkedHashMap<String, Object>
	 */
	private LinkedHashMap<String, Object> resolveParams(ProceedingJoinPoint joinPoint){
		MethodSignature signature = (MethodSignature)joinPoint.getSignature();
		Method method = signature.getMethod();
		Object[] arguments = joinPoint.getArgs();
		String[] paramNames = getParameterNames(method);
		
		LinkedHashMap<String, Object> params = new LinkedHashMap<>();
		for(int i = 0; i < arguments.length; i ++) {
			params.put(paramNames[i], arguments[i]);
		}
		
		return params;
	}
	
	/**
	 * 获取方法参数名称
	 * @param method Method 方法
	 * @return String[]
	 */
	private String[] getParameterNames(Method method) {
		ParameterNameDiscoverer pnd = new DefaultParameterNameDiscoverer();
		return pnd.getParameterNames(method);
	}
	
	/**
	 * 从表达式中解析字符串值
	 * @param exp String 表达式
	 * @param context EvaluationContext 上下文
	 * @return String
	 */
	private String resolveValue(String exp, EvaluationContext context) {
		String value;
		
		if(exp.contains(spElFlag)) {
			value = resolveValueByExpression(exp, context);
		}
		else {
			value = exp;
		}
		
		return value;
	}
	
	/**
	 * 从EL表达式中解析字符串值
	 * @param spElString String el表达式
	 * @param context EvaluationContext 上下文
	 * @return String
	 */
	private String resolveValueByExpression(String spElString, EvaluationContext context) {
		Expression expression = parser.parseExpression(spElString);
		return expression.getValue(context, String.class);
	}
	
	/**
	 * 从表达式中解析对象值
	 * @param exp String 表达式
	 * @param context EvaluationContext 上下文
	 * @return Object
	 */
	private Object resolveObjectValue(String exp, EvaluationContext context) {
		Object value;
		
		if(exp.contains(spElFlag)) {
			value = resolveObjectValueByExpression(exp, context);
		}
		else {
			value = exp;
		}
		
		return value;
	}
	
	/**
	 * 从EL表达式中解析对象值
	 * @param spElString String el表达式
	 * @param context EvaluationContext 上下文
	 * @return Object
	 */
	private Object resolveObjectValueByExpression(String spElString, EvaluationContext context) {
		Expression expression = parser.parseExpression(spElString);
		return expression.getValue(context, Object.class);
	}
	
	/**
	 * 从模板中解析值
	 * @param template String 模板
	 * @param context EvaluationContext 上下文
	 * @return String
	 */
	private String resolveValueByTemplate(String template, EvaluationContext context) {
		if(StringUtil.isNotEmpty(template)) {
			if (template.contains("{{") || template.contains("{")) {
				Matcher matcher = pattern.matcher(template);
				StringBuffer sb = new StringBuffer();
				while(matcher.find()) {
					String functionName = matcher.group(1);
					String expression = matcher.group(2);
					
					Object objectValue;
					
					//使用函数
					if(StringUtil.isNotEmpty(functionName)) {
						objectValue = resolveObjectValue(expression, context);
					}
					
					//使用变量
					else {
						objectValue = resolveValue(expression, context);
					}
					
					String replacement = Strings.nullToEmpty(functionService.apply(functionName, objectValue));
					replacement = Matcher.quoteReplacement(replacement);
					matcher.appendReplacement(sb, replacement);
				}
				
				matcher.appendTail(sb);
				return sb.toString();
			}
		}
		
		return template;
	}
}
