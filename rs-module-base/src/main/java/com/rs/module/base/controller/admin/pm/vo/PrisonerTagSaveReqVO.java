package com.rs.module.base.controller.admin.pm.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 实战平台-监管管理-监管人员标签新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonerTagSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("标签id")
    @NotEmpty(message = "标签id不能为空")
    private String tagCode;

    @ApiModelProperty("标签名称")
    @NotEmpty(message = "标签名称不能为空")
    private String tagName;

    @ApiModelProperty("人员标签类型（字典：ZD_JGRYBQLX）")
    @NotEmpty(message = "人员标签类型（字典：ZD_JGRYBQLX）不能为空")
    private String tagType;

}
