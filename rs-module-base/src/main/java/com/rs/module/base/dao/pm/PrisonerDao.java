package com.rs.module.base.dao.pm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.entity.pm.PrisonerVwDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 所有人员视图
 *
 * <AUTHOR>
 */
@Mapper
public interface PrisonerDao extends IBaseDao<PrisonerVwDO> {

    default PageResult<PrisonerVwDO> selectPage(PrisonerVwPageReqVO reqVO) {
        LambdaQueryWrapperX<PrisonerVwDO> wrapperX = new LambdaQueryWrapperX<PrisonerVwDO>()
                .betweenIfPresent(PrisonerVwDO::getAddTime, reqVO.getAddTime())
                .betweenIfPresent(PrisonerVwDO::getRssj, reqVO.getRssj())
                .likeIfPresent(PrisonerVwDO::getXm, reqVO.getXm())
                .eqIfPresent(PrisonerVwDO::getAreaId, reqVO.getAreaId())
                .eqIfPresent(PrisonerVwDO::getOrgCode, reqVO.getOrgCode())
                .eqIfPresent(PrisonerVwDO::getJsh, reqVO.getJsh())
                .eqIfPresent(PrisonerInDO::getBjgrylx, reqVO.getBjgrylx())
                .orderByDesc(PrisonerVwDO::getId);

        // 特殊：不分页，直接查询全部
        if (reqVO.isAll()) {
            List<PrisonerVwDO> list = selectList(wrapperX);
            return new PageResult<>(list, (long) list.size());
        }
        Page<PrisonerVwDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<PrisonerVwDO> prisonerVwPage = selectPage(page, wrapperX);
        return new PageResult<>(prisonerVwPage.getRecords(), prisonerVwPage.getTotal());
    }

    /**
     * 根据监管人员编号查询监管人员的最新医疗信息
     * @param jgrybm
     * @return
     */
    @Select("SELECT visit_time AS zjjzsj,visit_conclusion AS zjzdbq FROM ihc_ipm_visit WHERE jgrybm = #{jgrybm} ORDER BY visit_time DESC LIMIT 1")
    PrisonerVwRespVO selectPrisonerMedInfoByJgrybm(@Param("jgrybm") String jgrybm);


}
