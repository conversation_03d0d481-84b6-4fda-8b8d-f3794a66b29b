package com.rs.module.base.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;

@Data
public class DeleteRequestBody {

	@NotNull
	private String[] ids;

	@SuppressWarnings("unchecked")
	@JsonIgnore
	public List<String> getIdList() {
		return ids == null ? Collections.EMPTY_LIST : Lists.newArrayList(ids);
	}
}
