package com.rs.module.base.controller.admin.pm.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/15 19:21
 */
@ApiModel(description = "管理后台 - 实战平台-监管管理-区域信息 Request VO")
@Data
public class AreaInfoRespVO extends BaseVO implements TransPojo {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("区域ID")
    private String id;
    @ApiModelProperty("父节点ID")
    private String parentId;
    @ApiModelProperty("机构编号")
    private String orgCode;
    @ApiModelProperty("机构名称")
    private String orgName;
    @ApiModelProperty("监所区域名称")
    private String areaName;
    @ApiModelProperty("监所区域编码")
    private String areaCode;
    @ApiModelProperty("区域类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_QYLXDM")
    private String areaType;
    @ApiModelProperty("所在层级")
    private Integer level;

    @ApiModelProperty("父区域的路径信息,#分隔")
    private String allParentId;

    @ApiModelProperty("父节点名称")
    private String parentName;

    @ApiModelProperty("创建时间")
    private Date addTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("区域监室信息")
    private AreaPrisonRoomRespVO areaPrisonRoomRespVO;

    @ApiModelProperty("监室信息，区域类型为监室时传")
    private List<PrisonRoomWarderRespVO> areaRelatedWarderReqVOList;
}
