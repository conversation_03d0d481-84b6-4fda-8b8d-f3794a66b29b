package com.rs.module.base.entity.pm;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-监管管理-业务总台配置 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_biz_desk")
@KeySequence("acp_pm_biz_desk_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_biz_desk")
public class BizDeskDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 总台名称
     */
    private String name;
    /**
     * 总台标识
     */
    private String mark;
    /**
     * 是否启动浏览权限配置
     */
    private Short isEnableBrowsingPermission;
    /**
     * 备注
     */
    private String remark;

}
