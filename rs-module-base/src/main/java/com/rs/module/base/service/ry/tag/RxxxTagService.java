package com.rs.module.base.service.ry.tag;

import com.rs.module.base.controller.admin.pm.vo.PrisonerTagRespVO;
import com.rs.module.base.service.ry.tag.handle.base.RyxxTagHandler;
import com.rs.module.base.service.ry.tag.handle.enums.RyxxTagBusinessType;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Log4j2
@Service
public class RxxxTagService {

    @Autowired
    private RyxxTagHandlerFactory ryxxTagHandlerFactory;

    public List<PrisonerTagRespVO> getTag(String jgrybm) {
        List<PrisonerTagRespVO> tags = new ArrayList<>();
        for (RyxxTagBusinessType value : RyxxTagBusinessType.values()) {
            // 获取处理器并处理
            RyxxTagHandler handler = ryxxTagHandlerFactory.getHandler(value);
            if (handler != null && handler.canHandle(value)) {
                handler.handle(jgrybm, tags);
            }
        }
        return tags;
    }


}
