package com.rs.module.base.controller.admin.pm.vo.device;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-监管管理-设备信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseDeviceRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("设备编码")
    private String deviceCode;
    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("设备类型（字典：ZD_SBLXDM）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_SBLXDM")
    private String deviceTypeId;

    @ApiModelProperty("厂家")
    private String factory;
    @ApiModelProperty("型号")
    private String model;
    @ApiModelProperty("协议")
    private String protocol;
    @ApiModelProperty("ip地址")
    private String ipAddress;
    @ApiModelProperty("端口")
    private Integer port;
    @ApiModelProperty("设备用户名")
    private String devUserName;
    @ApiModelProperty("设备密码")
    private String devPassword;
    @ApiModelProperty("点位名称")
    private String pointName;
    @ApiModelProperty("通道编号")
    private String channelId;
    @ApiModelProperty("通道名称")
    private String channelName;
    @ApiModelProperty("设备国标编号")
    private String gbCode;
    @ApiModelProperty("mac地址")
    private String macAddress;
    @ApiModelProperty("在线时间")
    private Date onlineTime;
    @ApiModelProperty("所属区域")
    private String areaId;
    @ApiModelProperty("监室号")
    private String roomId;


    @ApiModelProperty("是否启用")
    private Integer isEnabled;
    @ApiModelProperty("设备状态（字典：ZD_SBZTDM）")

    @Trans(type = TransType.DICTIONARY, key = "ZD_SBZTDM")
    private String deviceStatus;

    private String allParentId;

    @ApiModelProperty("关联设备id，多个逗号分割")
    private String refDeviceId;


    @ApiModelProperty("监室号名称")
    private String roomName;

    @ApiModelProperty("所属区域")
    private String areaName;

    @ApiModelProperty("关联设备名称")
    private String refDeviceName;

    @ApiModelProperty(value = "设备编号")
    private Integer deviceNum;

    @ApiModelProperty("序列号")
    private String serialNumber;

    @ApiModelProperty("监室摄像头信息")
    private List<BaseDeviceRespVO> cameraList;
}
