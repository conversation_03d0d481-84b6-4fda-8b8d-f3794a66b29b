package com.rs.module.base.controller.admin.pm.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel(description = "仓内外屏，在所人员列表-简化版")
@Data
public class PrisonerAppRespVO implements TransPojo {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监室号id")
    private String jsh;

    @ApiModelProperty("民族")
    @Trans(type = TransType.DICTIONARY, key = "ZD_MZ")
    private String mz;

    @ApiModelProperty("入所时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date rssj;

    @ApiModelProperty("姓名")
    private String xm;

    @ApiModelProperty("性别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XB", ref = "xbName")
    private String xb;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date csrq;

    @ApiModelProperty("性别名称")
    private String xbName;

    @ApiModelProperty("入所天数")
    private Long rsts;

    @ApiModelProperty("年龄")
    private Integer age;

    @ApiModelProperty("照片URL，http开头绝对路径")
    private String frontPhoto;

    @ApiModelProperty("标签")
    private List<String> tags;

    @ApiModelProperty("涉嫌罪名")
    @Trans(type = TransType.DICTIONARY, key = "ZD_SXZM")
    private String sxzm;

    @ApiModelProperty("诉讼环节")
    @Trans(type = TransType.DICTIONARY, key = "ZD_SSJD")
    private String sshj;

    @ApiModelProperty("关押期限")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gyqx;

}
