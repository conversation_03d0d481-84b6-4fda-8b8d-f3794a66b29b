package com.rs.module.base.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 平台、终端类型枚举
 *
 * <AUTHOR>
 */

public enum PlatformEnum {
    CNP("CNP", "仓内屏"),
    CWP("CWP", "仓外屏"),
    PLATFORM("PLATFORM", "实战平台"),
//	IHCS("IHCS", "医疗系统"),
    ;

    @Getter
    private final String value;
    private final String desc;

    PlatformEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    @JsonValue
    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value) {
        for (PlatformEnum e : PlatformEnum.values()) {
            if (e.value.equals(value)) {
                return e.desc;
            }
        }
        return null;
    }
}
