package com.rs.module.base.controller.admin.pm.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-监管管理-业务浏览权限配置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BizBrowsingPermissionRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("业务ID")
    private String bizId;
    @ApiModelProperty("业务标识")
    private String mark;
    @ApiModelProperty("权限类型（01：角色，02：机构，03：用户）")
    private String permissionType;
    @ApiModelProperty("权限ID,根据权限类型存储对应ID")
    private String permissionId;
}
