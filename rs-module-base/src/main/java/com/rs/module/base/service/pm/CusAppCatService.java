package com.rs.module.base.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.base.controller.admin.pm.vo.CusAppCatSaveReqVO;
import com.rs.module.base.entity.pm.CusAppCatDO;

import javax.validation.Valid;

/**
 * 自定义应用分类 Service 接口
 *
 * <AUTHOR>
 */
public interface CusAppCatService extends IBaseService<CusAppCatDO>{

    /**
     * 创建自定义应用分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createOrUpdateCusAppCat(@Valid CusAppCatSaveReqVO createReqVO);

    /**
     * 删除自定义应用分类
     *
     * @param id 编号
     */
    void deleteCusAppCat(String id);

    /**
     * 获得自定义应用分类
     *
     * @param id 编号
     * @return 自定义应用分类
     */
    CusAppCatDO getCusAppCat(String id);

}
