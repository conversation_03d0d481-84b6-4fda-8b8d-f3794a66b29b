package com.rs.module.base.util;

import cn.hutool.core.util.StrUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.base.entity.pm.BizBrowsingPermissionDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PermissionUtil {

    /**
     * 判断当前用户是否有业务权限
     * @param permissionDOS
     * @return
     */
    public static Boolean judgePermission(List<BizBrowsingPermissionDO> permissionDOS) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        List<String> roleArr = permissionDOS.stream().filter(permission -> "01".equals(permission.getPermissionType()))
                .map(permission -> permission.getPermissionId()).collect(Collectors.toList());
        List<String> regArr = permissionDOS.stream().filter(permission -> "02".equals(permission.getPermissionType()))
                .map(permission -> permission.getPermissionId()).collect(Collectors.toList());
        List<String> orgArr = permissionDOS.stream().filter(permission -> "03".equals(permission.getPermissionType()))
                .map(permission -> permission.getPermissionId()).collect(Collectors.toList());

        String regCode = sessionUser.getRegCode();
        String orgCode = sessionUser.getOrgCode();
        String roleIds = sessionUser.getRoleIds();
        boolean isRegPower = true;
        boolean isOrgPower = true;
        boolean isRolePower = true;
        // 是否授权给用户所在的区域
        if(null != regArr) {
            isRegPower = regArr.contains(regCode);
        }
        // 是否授权给用户所在的机构
        if(null != orgArr) {
            isRegPower = orgArr.contains(orgCode);
        }
        // 当前用户是否存在授权角色
        if(null !=  roleArr && StringUtil.isNotEmpty(roleIds)) {
            isRolePower = false;
            List<String> roleList = StrUtil.splitTrim(roleIds, ",");
            for(String roleId : roleList) {
                if(roleArr.contains(roleId)) {
                    isRolePower = true;
                    break;
                }
            }
        }
        if(isRegPower && isOrgPower && isRolePower) {
            return true;
        }
        return false;
    }

}
